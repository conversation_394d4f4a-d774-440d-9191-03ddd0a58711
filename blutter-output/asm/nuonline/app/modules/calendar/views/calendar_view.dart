// lib: , url: package:nuonline/app/modules/calendar/views/calendar_view.dart

// class id: 1050158, size: 0x8
class :: {
}

// class id: 5305, size: 0x1c, field offset: 0x14
//   const constructor, 
class CalendarView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad5400, size: 0x55c
    // 0xad5400: EnterFrame
    //     0xad5400: stp             fp, lr, [SP, #-0x10]!
    //     0xad5404: mov             fp, SP
    // 0xad5408: AllocStack(0x60)
    //     0xad5408: sub             SP, SP, #0x60
    // 0xad540c: SetupParameters(CalendarView this /* r1 => r1, fp-0x8 */)
    //     0xad540c: stur            x1, [fp, #-8]
    // 0xad5410: CheckStackOverflow
    //     0xad5410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5414: cmp             SP, x16
    //     0xad5418: b.ls            #0xad593c
    // 0xad541c: r1 = 1
    //     0xad541c: movz            x1, #0x1
    // 0xad5420: r0 = AllocateContext()
    //     0xad5420: bl              #0xec126c  ; AllocateContextStub
    // 0xad5424: mov             x1, x0
    // 0xad5428: ldur            x0, [fp, #-8]
    // 0xad542c: stur            x1, [fp, #-0x10]
    // 0xad5430: StoreField: r1->field_f = r0
    //     0xad5430: stur            w0, [x1, #0xf]
    // 0xad5434: LoadField: d0 = r0->field_13
    //     0xad5434: ldur            d0, [x0, #0x13]
    // 0xad5438: stur            d0, [fp, #-0x48]
    // 0xad543c: r0 = Obx()
    //     0xad543c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad5440: ldur            x2, [fp, #-0x10]
    // 0xad5444: r1 = Function '<anonymous closure>':.
    //     0xad5444: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa18] AnonymousClosure: (0xad6b1c), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5448: ldr             x1, [x1, #0xa18]
    // 0xad544c: stur            x0, [fp, #-8]
    // 0xad5450: r0 = AllocateClosure()
    //     0xad5450: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5454: mov             x1, x0
    // 0xad5458: ldur            x0, [fp, #-8]
    // 0xad545c: StoreField: r0->field_b = r1
    //     0xad545c: stur            w1, [x0, #0xb]
    // 0xad5460: r1 = Function '<anonymous closure>':.
    //     0xad5460: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa20] AnonymousClosure: (0xad69e8), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5464: ldr             x1, [x1, #0xa20]
    // 0xad5468: r2 = Null
    //     0xad5468: mov             x2, NULL
    // 0xad546c: r0 = AllocateClosure()
    //     0xad546c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5470: stur            x0, [fp, #-0x18]
    // 0xad5474: r0 = IconButton()
    //     0xad5474: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad5478: mov             x3, x0
    // 0xad547c: ldur            x0, [fp, #-0x18]
    // 0xad5480: stur            x3, [fp, #-0x20]
    // 0xad5484: StoreField: r3->field_3b = r0
    //     0xad5484: stur            w0, [x3, #0x3b]
    // 0xad5488: r0 = false
    //     0xad5488: add             x0, NULL, #0x30  ; false
    // 0xad548c: StoreField: r3->field_47 = r0
    //     0xad548c: stur            w0, [x3, #0x47]
    // 0xad5490: r1 = Instance_Icon
    //     0xad5490: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f70] Obj!Icon@e24431
    //     0xad5494: ldr             x1, [x1, #0xf70]
    // 0xad5498: StoreField: r3->field_1f = r1
    //     0xad5498: stur            w1, [x3, #0x1f]
    // 0xad549c: r4 = Instance__IconButtonVariant
    //     0xad549c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad54a0: ldr             x4, [x4, #0xf78]
    // 0xad54a4: StoreField: r3->field_63 = r4
    //     0xad54a4: stur            w4, [x3, #0x63]
    // 0xad54a8: ldur            x2, [fp, #-0x10]
    // 0xad54ac: r1 = Function '<anonymous closure>':.
    //     0xad54ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa28] AnonymousClosure: (0xad693c), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad54b0: ldr             x1, [x1, #0xa28]
    // 0xad54b4: r0 = AllocateClosure()
    //     0xad54b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad54b8: stur            x0, [fp, #-0x18]
    // 0xad54bc: r0 = IconButton()
    //     0xad54bc: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad54c0: mov             x3, x0
    // 0xad54c4: ldur            x0, [fp, #-0x18]
    // 0xad54c8: stur            x3, [fp, #-0x28]
    // 0xad54cc: StoreField: r3->field_3b = r0
    //     0xad54cc: stur            w0, [x3, #0x3b]
    // 0xad54d0: r0 = false
    //     0xad54d0: add             x0, NULL, #0x30  ; false
    // 0xad54d4: StoreField: r3->field_47 = r0
    //     0xad54d4: stur            w0, [x3, #0x47]
    // 0xad54d8: r1 = Instance_Icon
    //     0xad54d8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be78] Obj!Icon@e243f1
    //     0xad54dc: ldr             x1, [x1, #0xe78]
    // 0xad54e0: StoreField: r3->field_1f = r1
    //     0xad54e0: stur            w1, [x3, #0x1f]
    // 0xad54e4: r1 = Instance__IconButtonVariant
    //     0xad54e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad54e8: ldr             x1, [x1, #0xf78]
    // 0xad54ec: StoreField: r3->field_63 = r1
    //     0xad54ec: stur            w1, [x3, #0x63]
    // 0xad54f0: r1 = Null
    //     0xad54f0: mov             x1, NULL
    // 0xad54f4: r2 = 4
    //     0xad54f4: movz            x2, #0x4
    // 0xad54f8: r0 = AllocateArray()
    //     0xad54f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad54fc: mov             x2, x0
    // 0xad5500: ldur            x0, [fp, #-0x20]
    // 0xad5504: stur            x2, [fp, #-0x18]
    // 0xad5508: StoreField: r2->field_f = r0
    //     0xad5508: stur            w0, [x2, #0xf]
    // 0xad550c: ldur            x0, [fp, #-0x28]
    // 0xad5510: StoreField: r2->field_13 = r0
    //     0xad5510: stur            w0, [x2, #0x13]
    // 0xad5514: r1 = <Widget>
    //     0xad5514: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad5518: r0 = AllocateGrowableArray()
    //     0xad5518: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad551c: mov             x1, x0
    // 0xad5520: ldur            x0, [fp, #-0x18]
    // 0xad5524: stur            x1, [fp, #-0x20]
    // 0xad5528: StoreField: r1->field_f = r0
    //     0xad5528: stur            w0, [x1, #0xf]
    // 0xad552c: r0 = 4
    //     0xad552c: movz            x0, #0x4
    // 0xad5530: StoreField: r1->field_b = r0
    //     0xad5530: stur            w0, [x1, #0xb]
    // 0xad5534: ldur            d0, [fp, #-0x48]
    // 0xad5538: r0 = inline_Allocate_Double()
    //     0xad5538: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xad553c: add             x0, x0, #0x10
    //     0xad5540: cmp             x2, x0
    //     0xad5544: b.ls            #0xad5944
    //     0xad5548: str             x0, [THR, #0x50]  ; THR::top
    //     0xad554c: sub             x0, x0, #0xf
    //     0xad5550: movz            x2, #0xe15c
    //     0xad5554: movk            x2, #0x3, lsl #16
    //     0xad5558: stur            x2, [x0, #-1]
    // 0xad555c: StoreField: r0->field_7 = d0
    //     0xad555c: stur            d0, [x0, #7]
    // 0xad5560: stur            x0, [fp, #-0x18]
    // 0xad5564: r0 = AppBar()
    //     0xad5564: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad5568: stur            x0, [fp, #-0x28]
    // 0xad556c: ldur            x16, [fp, #-0x18]
    // 0xad5570: ldur            lr, [fp, #-8]
    // 0xad5574: stp             lr, x16, [SP, #8]
    // 0xad5578: ldur            x16, [fp, #-0x20]
    // 0xad557c: str             x16, [SP]
    // 0xad5580: mov             x1, x0
    // 0xad5584: r4 = const [0, 0x4, 0x3, 0x1, actions, 0x3, title, 0x2, titleSpacing, 0x1, null]
    //     0xad5584: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa30] List(11) [0, 0x4, 0x3, 0x1, "actions", 0x3, "title", 0x2, "titleSpacing", 0x1, Null]
    //     0xad5588: ldr             x4, [x4, #0xa30]
    // 0xad558c: r0 = AppBar()
    //     0xad558c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad5590: r0 = Obx()
    //     0xad5590: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad5594: ldur            x2, [fp, #-0x10]
    // 0xad5598: r1 = Function '<anonymous closure>':.
    //     0xad5598: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa38] AnonymousClosure: (0xad6514), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad559c: ldr             x1, [x1, #0xa38]
    // 0xad55a0: stur            x0, [fp, #-8]
    // 0xad55a4: r0 = AllocateClosure()
    //     0xad55a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad55a8: mov             x1, x0
    // 0xad55ac: ldur            x0, [fp, #-8]
    // 0xad55b0: StoreField: r0->field_b = r1
    //     0xad55b0: stur            w1, [x0, #0xb]
    // 0xad55b4: r0 = Obx()
    //     0xad55b4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad55b8: ldur            x2, [fp, #-0x10]
    // 0xad55bc: r1 = Function '<anonymous closure>':.
    //     0xad55bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa40] AnonymousClosure: (0xad61a0), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad55c0: ldr             x1, [x1, #0xa40]
    // 0xad55c4: stur            x0, [fp, #-0x18]
    // 0xad55c8: r0 = AllocateClosure()
    //     0xad55c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad55cc: mov             x1, x0
    // 0xad55d0: ldur            x0, [fp, #-0x18]
    // 0xad55d4: StoreField: r0->field_b = r1
    //     0xad55d4: stur            w1, [x0, #0xb]
    // 0xad55d8: r1 = _ConstMap len:3
    //     0xad55d8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xad55dc: ldr             x1, [x1, #0xbe8]
    // 0xad55e0: r2 = 6
    //     0xad55e0: movz            x2, #0x6
    // 0xad55e4: r0 = []()
    //     0xad55e4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xad55e8: r16 = <Color?>
    //     0xad55e8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xad55ec: ldr             x16, [x16, #0x98]
    // 0xad55f0: stp             x0, x16, [SP, #8]
    // 0xad55f4: r16 = Instance_MaterialColor
    //     0xad55f4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xad55f8: ldr             x16, [x16, #0xe38]
    // 0xad55fc: str             x16, [SP]
    // 0xad5600: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad5600: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad5604: r0 = mode()
    //     0xad5604: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xad5608: stur            x0, [fp, #-0x20]
    // 0xad560c: r0 = Divider()
    //     0xad560c: bl              #0xad5980  ; AllocateDividerStub -> Divider (size=0x20)
    // 0xad5610: mov             x1, x0
    // 0xad5614: r0 = 12.000000
    //     0xad5614: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xad5618: ldr             x0, [x0, #0xc60]
    // 0xad561c: stur            x1, [fp, #-0x30]
    // 0xad5620: StoreField: r1->field_b = r0
    //     0xad5620: stur            w0, [x1, #0xb]
    // 0xad5624: StoreField: r1->field_f = r0
    //     0xad5624: stur            w0, [x1, #0xf]
    // 0xad5628: ldur            x0, [fp, #-0x20]
    // 0xad562c: StoreField: r1->field_1b = r0
    //     0xad562c: stur            w0, [x1, #0x1b]
    // 0xad5630: r0 = NArticleHeader()
    //     0xad5630: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xad5634: mov             x1, x0
    // 0xad5638: r0 = "Hari Besar & Libur Nasional"
    //     0xad5638: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa48] "Hari Besar & Libur Nasional"
    //     0xad563c: ldr             x0, [x0, #0xa48]
    // 0xad5640: stur            x1, [fp, #-0x20]
    // 0xad5644: StoreField: r1->field_b = r0
    //     0xad5644: stur            w0, [x1, #0xb]
    // 0xad5648: r0 = false
    //     0xad5648: add             x0, NULL, #0x30  ; false
    // 0xad564c: StoreField: r1->field_13 = r0
    //     0xad564c: stur            w0, [x1, #0x13]
    // 0xad5650: r0 = Obx()
    //     0xad5650: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad5654: ldur            x2, [fp, #-0x10]
    // 0xad5658: r1 = Function '<anonymous closure>':.
    //     0xad5658: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa50] AnonymousClosure: (0xad5e70), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad565c: ldr             x1, [x1, #0xa50]
    // 0xad5660: stur            x0, [fp, #-0x38]
    // 0xad5664: r0 = AllocateClosure()
    //     0xad5664: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5668: mov             x1, x0
    // 0xad566c: ldur            x0, [fp, #-0x38]
    // 0xad5670: StoreField: r0->field_b = r1
    //     0xad5670: stur            w1, [x0, #0xb]
    // 0xad5674: r1 = Null
    //     0xad5674: mov             x1, NULL
    // 0xad5678: r2 = 2
    //     0xad5678: movz            x2, #0x2
    // 0xad567c: r0 = AllocateArray()
    //     0xad567c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad5680: mov             x2, x0
    // 0xad5684: ldur            x0, [fp, #-0x38]
    // 0xad5688: stur            x2, [fp, #-0x40]
    // 0xad568c: StoreField: r2->field_f = r0
    //     0xad568c: stur            w0, [x2, #0xf]
    // 0xad5690: r1 = <Widget>
    //     0xad5690: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad5694: r0 = AllocateGrowableArray()
    //     0xad5694: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad5698: mov             x1, x0
    // 0xad569c: ldur            x0, [fp, #-0x40]
    // 0xad56a0: stur            x1, [fp, #-0x38]
    // 0xad56a4: StoreField: r1->field_f = r0
    //     0xad56a4: stur            w0, [x1, #0xf]
    // 0xad56a8: r2 = 2
    //     0xad56a8: movz            x2, #0x2
    // 0xad56ac: StoreField: r1->field_b = r2
    //     0xad56ac: stur            w2, [x1, #0xb]
    // 0xad56b0: r0 = NExpansionTile()
    //     0xad56b0: bl              #0xad5968  ; AllocateNExpansionTileStub -> NExpansionTile (size=0x58)
    // 0xad56b4: mov             x1, x0
    // 0xad56b8: ldur            x0, [fp, #-0x20]
    // 0xad56bc: stur            x1, [fp, #-0x40]
    // 0xad56c0: StoreField: r1->field_f = r0
    //     0xad56c0: stur            w0, [x1, #0xf]
    // 0xad56c4: ldur            x0, [fp, #-0x38]
    // 0xad56c8: StoreField: r1->field_1b = r0
    //     0xad56c8: stur            w0, [x1, #0x1b]
    // 0xad56cc: r0 = true
    //     0xad56cc: add             x0, NULL, #0x20  ; true
    // 0xad56d0: StoreField: r1->field_2b = r0
    //     0xad56d0: stur            w0, [x1, #0x2b]
    // 0xad56d4: r2 = false
    //     0xad56d4: add             x2, NULL, #0x30  ; false
    // 0xad56d8: StoreField: r1->field_2f = r2
    //     0xad56d8: stur            w2, [x1, #0x2f]
    // 0xad56dc: r3 = Instance_EdgeInsets
    //     0xad56dc: add             x3, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xad56e0: ldr             x3, [x3, #0x360]
    // 0xad56e4: StoreField: r1->field_33 = r3
    //     0xad56e4: stur            w3, [x1, #0x33]
    // 0xad56e8: r0 = NArticleHeader()
    //     0xad56e8: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xad56ec: mov             x1, x0
    // 0xad56f0: r0 = "Puasa"
    //     0xad56f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Puasa"
    //     0xad56f4: ldr             x0, [x0, #0xa58]
    // 0xad56f8: stur            x1, [fp, #-0x20]
    // 0xad56fc: StoreField: r1->field_b = r0
    //     0xad56fc: stur            w0, [x1, #0xb]
    // 0xad5700: r0 = false
    //     0xad5700: add             x0, NULL, #0x30  ; false
    // 0xad5704: StoreField: r1->field_13 = r0
    //     0xad5704: stur            w0, [x1, #0x13]
    // 0xad5708: r0 = Obx()
    //     0xad5708: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad570c: ldur            x2, [fp, #-0x10]
    // 0xad5710: r1 = Function '<anonymous closure>':.
    //     0xad5710: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa60] AnonymousClosure: (0xad598c), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5714: ldr             x1, [x1, #0xa60]
    // 0xad5718: stur            x0, [fp, #-0x10]
    // 0xad571c: r0 = AllocateClosure()
    //     0xad571c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5720: mov             x1, x0
    // 0xad5724: ldur            x0, [fp, #-0x10]
    // 0xad5728: StoreField: r0->field_b = r1
    //     0xad5728: stur            w1, [x0, #0xb]
    // 0xad572c: r1 = Null
    //     0xad572c: mov             x1, NULL
    // 0xad5730: r2 = 2
    //     0xad5730: movz            x2, #0x2
    // 0xad5734: r0 = AllocateArray()
    //     0xad5734: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad5738: mov             x2, x0
    // 0xad573c: ldur            x0, [fp, #-0x10]
    // 0xad5740: stur            x2, [fp, #-0x38]
    // 0xad5744: StoreField: r2->field_f = r0
    //     0xad5744: stur            w0, [x2, #0xf]
    // 0xad5748: r1 = <Widget>
    //     0xad5748: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad574c: r0 = AllocateGrowableArray()
    //     0xad574c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad5750: mov             x1, x0
    // 0xad5754: ldur            x0, [fp, #-0x38]
    // 0xad5758: stur            x1, [fp, #-0x10]
    // 0xad575c: StoreField: r1->field_f = r0
    //     0xad575c: stur            w0, [x1, #0xf]
    // 0xad5760: r0 = 2
    //     0xad5760: movz            x0, #0x2
    // 0xad5764: StoreField: r1->field_b = r0
    //     0xad5764: stur            w0, [x1, #0xb]
    // 0xad5768: r0 = NExpansionTile()
    //     0xad5768: bl              #0xad5968  ; AllocateNExpansionTileStub -> NExpansionTile (size=0x58)
    // 0xad576c: mov             x3, x0
    // 0xad5770: ldur            x0, [fp, #-0x20]
    // 0xad5774: stur            x3, [fp, #-0x38]
    // 0xad5778: StoreField: r3->field_f = r0
    //     0xad5778: stur            w0, [x3, #0xf]
    // 0xad577c: ldur            x0, [fp, #-0x10]
    // 0xad5780: StoreField: r3->field_1b = r0
    //     0xad5780: stur            w0, [x3, #0x1b]
    // 0xad5784: r0 = true
    //     0xad5784: add             x0, NULL, #0x20  ; true
    // 0xad5788: StoreField: r3->field_2b = r0
    //     0xad5788: stur            w0, [x3, #0x2b]
    // 0xad578c: r4 = false
    //     0xad578c: add             x4, NULL, #0x30  ; false
    // 0xad5790: StoreField: r3->field_2f = r4
    //     0xad5790: stur            w4, [x3, #0x2f]
    // 0xad5794: r1 = Instance_EdgeInsets
    //     0xad5794: add             x1, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xad5798: ldr             x1, [x1, #0x360]
    // 0xad579c: StoreField: r3->field_33 = r1
    //     0xad579c: stur            w1, [x3, #0x33]
    // 0xad57a0: r1 = Null
    //     0xad57a0: mov             x1, NULL
    // 0xad57a4: r2 = 8
    //     0xad57a4: movz            x2, #0x8
    // 0xad57a8: r0 = AllocateArray()
    //     0xad57a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad57ac: mov             x2, x0
    // 0xad57b0: ldur            x0, [fp, #-0x18]
    // 0xad57b4: stur            x2, [fp, #-0x10]
    // 0xad57b8: StoreField: r2->field_f = r0
    //     0xad57b8: stur            w0, [x2, #0xf]
    // 0xad57bc: ldur            x0, [fp, #-0x30]
    // 0xad57c0: StoreField: r2->field_13 = r0
    //     0xad57c0: stur            w0, [x2, #0x13]
    // 0xad57c4: ldur            x0, [fp, #-0x40]
    // 0xad57c8: ArrayStore: r2[0] = r0  ; List_4
    //     0xad57c8: stur            w0, [x2, #0x17]
    // 0xad57cc: ldur            x0, [fp, #-0x38]
    // 0xad57d0: StoreField: r2->field_1b = r0
    //     0xad57d0: stur            w0, [x2, #0x1b]
    // 0xad57d4: r1 = <Widget>
    //     0xad57d4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad57d8: r0 = AllocateGrowableArray()
    //     0xad57d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad57dc: mov             x1, x0
    // 0xad57e0: ldur            x0, [fp, #-0x10]
    // 0xad57e4: stur            x1, [fp, #-0x18]
    // 0xad57e8: StoreField: r1->field_f = r0
    //     0xad57e8: stur            w0, [x1, #0xf]
    // 0xad57ec: r0 = 8
    //     0xad57ec: movz            x0, #0x8
    // 0xad57f0: StoreField: r1->field_b = r0
    //     0xad57f0: stur            w0, [x1, #0xb]
    // 0xad57f4: r0 = ListView()
    //     0xad57f4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad57f8: mov             x1, x0
    // 0xad57fc: ldur            x2, [fp, #-0x18]
    // 0xad5800: stur            x0, [fp, #-0x10]
    // 0xad5804: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xad5804: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xad5808: r0 = ListView()
    //     0xad5808: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xad580c: r1 = <FlexParentData>
    //     0xad580c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad5810: ldr             x1, [x1, #0x720]
    // 0xad5814: r0 = Expanded()
    //     0xad5814: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad5818: mov             x3, x0
    // 0xad581c: r0 = 1
    //     0xad581c: movz            x0, #0x1
    // 0xad5820: stur            x3, [fp, #-0x18]
    // 0xad5824: StoreField: r3->field_13 = r0
    //     0xad5824: stur            x0, [x3, #0x13]
    // 0xad5828: r0 = Instance_FlexFit
    //     0xad5828: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad582c: ldr             x0, [x0, #0x728]
    // 0xad5830: StoreField: r3->field_1b = r0
    //     0xad5830: stur            w0, [x3, #0x1b]
    // 0xad5834: ldur            x0, [fp, #-0x10]
    // 0xad5838: StoreField: r3->field_b = r0
    //     0xad5838: stur            w0, [x3, #0xb]
    // 0xad583c: r1 = Null
    //     0xad583c: mov             x1, NULL
    // 0xad5840: r2 = 6
    //     0xad5840: movz            x2, #0x6
    // 0xad5844: r0 = AllocateArray()
    //     0xad5844: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad5848: mov             x1, x0
    // 0xad584c: ldur            x0, [fp, #-8]
    // 0xad5850: stur            x1, [fp, #-0x10]
    // 0xad5854: StoreField: r1->field_f = r0
    //     0xad5854: stur            w0, [x1, #0xf]
    // 0xad5858: r0 = NCalendarDaysHeader()
    //     0xad5858: bl              #0xad595c  ; AllocateNCalendarDaysHeaderStub -> NCalendarDaysHeader (size=0xc)
    // 0xad585c: mov             x1, x0
    // 0xad5860: ldur            x0, [fp, #-0x10]
    // 0xad5864: StoreField: r0->field_13 = r1
    //     0xad5864: stur            w1, [x0, #0x13]
    // 0xad5868: ldur            x1, [fp, #-0x18]
    // 0xad586c: ArrayStore: r0[0] = r1  ; List_4
    //     0xad586c: stur            w1, [x0, #0x17]
    // 0xad5870: r1 = <Widget>
    //     0xad5870: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad5874: r0 = AllocateGrowableArray()
    //     0xad5874: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad5878: mov             x1, x0
    // 0xad587c: ldur            x0, [fp, #-0x10]
    // 0xad5880: stur            x1, [fp, #-8]
    // 0xad5884: StoreField: r1->field_f = r0
    //     0xad5884: stur            w0, [x1, #0xf]
    // 0xad5888: r0 = 6
    //     0xad5888: movz            x0, #0x6
    // 0xad588c: StoreField: r1->field_b = r0
    //     0xad588c: stur            w0, [x1, #0xb]
    // 0xad5890: r0 = Column()
    //     0xad5890: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad5894: mov             x1, x0
    // 0xad5898: r0 = Instance_Axis
    //     0xad5898: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad589c: stur            x1, [fp, #-0x10]
    // 0xad58a0: StoreField: r1->field_f = r0
    //     0xad58a0: stur            w0, [x1, #0xf]
    // 0xad58a4: r0 = Instance_MainAxisAlignment
    //     0xad58a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad58a8: ldr             x0, [x0, #0x730]
    // 0xad58ac: StoreField: r1->field_13 = r0
    //     0xad58ac: stur            w0, [x1, #0x13]
    // 0xad58b0: r0 = Instance_MainAxisSize
    //     0xad58b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad58b4: ldr             x0, [x0, #0x738]
    // 0xad58b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xad58b8: stur            w0, [x1, #0x17]
    // 0xad58bc: r0 = Instance_CrossAxisAlignment
    //     0xad58bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad58c0: ldr             x0, [x0, #0x740]
    // 0xad58c4: StoreField: r1->field_1b = r0
    //     0xad58c4: stur            w0, [x1, #0x1b]
    // 0xad58c8: r0 = Instance_VerticalDirection
    //     0xad58c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad58cc: ldr             x0, [x0, #0x748]
    // 0xad58d0: StoreField: r1->field_23 = r0
    //     0xad58d0: stur            w0, [x1, #0x23]
    // 0xad58d4: r0 = Instance_Clip
    //     0xad58d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad58d8: ldr             x0, [x0, #0x750]
    // 0xad58dc: StoreField: r1->field_2b = r0
    //     0xad58dc: stur            w0, [x1, #0x2b]
    // 0xad58e0: StoreField: r1->field_2f = rZR
    //     0xad58e0: stur            xzr, [x1, #0x2f]
    // 0xad58e4: ldur            x0, [fp, #-8]
    // 0xad58e8: StoreField: r1->field_b = r0
    //     0xad58e8: stur            w0, [x1, #0xb]
    // 0xad58ec: r0 = Scaffold()
    //     0xad58ec: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad58f0: ldur            x1, [fp, #-0x28]
    // 0xad58f4: StoreField: r0->field_13 = r1
    //     0xad58f4: stur            w1, [x0, #0x13]
    // 0xad58f8: ldur            x1, [fp, #-0x10]
    // 0xad58fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xad58fc: stur            w1, [x0, #0x17]
    // 0xad5900: r1 = Instance_AlignmentDirectional
    //     0xad5900: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad5904: ldr             x1, [x1, #0x758]
    // 0xad5908: StoreField: r0->field_2b = r1
    //     0xad5908: stur            w1, [x0, #0x2b]
    // 0xad590c: r1 = true
    //     0xad590c: add             x1, NULL, #0x20  ; true
    // 0xad5910: StoreField: r0->field_53 = r1
    //     0xad5910: stur            w1, [x0, #0x53]
    // 0xad5914: r2 = Instance_DragStartBehavior
    //     0xad5914: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad5918: StoreField: r0->field_57 = r2
    //     0xad5918: stur            w2, [x0, #0x57]
    // 0xad591c: r2 = false
    //     0xad591c: add             x2, NULL, #0x30  ; false
    // 0xad5920: StoreField: r0->field_b = r2
    //     0xad5920: stur            w2, [x0, #0xb]
    // 0xad5924: StoreField: r0->field_f = r2
    //     0xad5924: stur            w2, [x0, #0xf]
    // 0xad5928: StoreField: r0->field_5f = r1
    //     0xad5928: stur            w1, [x0, #0x5f]
    // 0xad592c: StoreField: r0->field_63 = r1
    //     0xad592c: stur            w1, [x0, #0x63]
    // 0xad5930: LeaveFrame
    //     0xad5930: mov             SP, fp
    //     0xad5934: ldp             fp, lr, [SP], #0x10
    // 0xad5938: ret
    //     0xad5938: ret             
    // 0xad593c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad593c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5940: b               #0xad541c
    // 0xad5944: SaveReg d0
    //     0xad5944: str             q0, [SP, #-0x10]!
    // 0xad5948: SaveReg r1
    //     0xad5948: str             x1, [SP, #-8]!
    // 0xad594c: r0 = AllocateDouble()
    //     0xad594c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xad5950: RestoreReg r1
    //     0xad5950: ldr             x1, [SP], #8
    // 0xad5954: RestoreReg d0
    //     0xad5954: ldr             q0, [SP], #0x10
    // 0xad5958: b               #0xad555c
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xad598c, size: 0x144
    // 0xad598c: EnterFrame
    //     0xad598c: stp             fp, lr, [SP, #-0x10]!
    //     0xad5990: mov             fp, SP
    // 0xad5994: AllocStack(0x40)
    //     0xad5994: sub             SP, SP, #0x40
    // 0xad5998: SetupParameters()
    //     0xad5998: ldr             x0, [fp, #0x10]
    //     0xad599c: ldur            w2, [x0, #0x17]
    //     0xad59a0: add             x2, x2, HEAP, lsl #32
    //     0xad59a4: stur            x2, [fp, #-8]
    // 0xad59a8: CheckStackOverflow
    //     0xad59a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad59ac: cmp             SP, x16
    //     0xad59b0: b.ls            #0xad5ac8
    // 0xad59b4: LoadField: r1 = r2->field_f
    //     0xad59b4: ldur            w1, [x2, #0xf]
    // 0xad59b8: DecompressPointer r1
    //     0xad59b8: add             x1, x1, HEAP, lsl #32
    // 0xad59bc: r0 = controller()
    //     0xad59bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad59c0: LoadField: r1 = r0->field_37
    //     0xad59c0: ldur            w1, [x0, #0x37]
    // 0xad59c4: DecompressPointer r1
    //     0xad59c4: add             x1, x1, HEAP, lsl #32
    // 0xad59c8: r0 = value()
    //     0xad59c8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad59cc: r1 = LoadClassIdInstr(r0)
    //     0xad59cc: ldur            x1, [x0, #-1]
    //     0xad59d0: ubfx            x1, x1, #0xc, #0x14
    // 0xad59d4: str             x0, [SP]
    // 0xad59d8: mov             x0, x1
    // 0xad59dc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad59dc: movz            x17, #0xc834
    //     0xad59e0: add             lr, x0, x17
    //     0xad59e4: ldr             lr, [x21, lr, lsl #3]
    //     0xad59e8: blr             lr
    // 0xad59ec: ldur            x2, [fp, #-8]
    // 0xad59f0: stur            x0, [fp, #-0x10]
    // 0xad59f4: LoadField: r1 = r2->field_f
    //     0xad59f4: ldur            w1, [x2, #0xf]
    // 0xad59f8: DecompressPointer r1
    //     0xad59f8: add             x1, x1, HEAP, lsl #32
    // 0xad59fc: r0 = controller()
    //     0xad59fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5a00: LoadField: r1 = r0->field_37
    //     0xad5a00: ldur            w1, [x0, #0x37]
    // 0xad5a04: DecompressPointer r1
    //     0xad5a04: add             x1, x1, HEAP, lsl #32
    // 0xad5a08: r0 = value()
    //     0xad5a08: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad5a0c: r1 = LoadClassIdInstr(r0)
    //     0xad5a0c: ldur            x1, [x0, #-1]
    //     0xad5a10: ubfx            x1, x1, #0xc, #0x14
    // 0xad5a14: str             x0, [SP]
    // 0xad5a18: mov             x0, x1
    // 0xad5a1c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad5a1c: movz            x17, #0xc834
    //     0xad5a20: add             lr, x0, x17
    //     0xad5a24: ldr             lr, [x21, lr, lsl #3]
    //     0xad5a28: blr             lr
    // 0xad5a2c: r3 = LoadInt32Instr(r0)
    //     0xad5a2c: sbfx            x3, x0, #1, #0x1f
    //     0xad5a30: tbz             w0, #0, #0xad5a38
    //     0xad5a34: ldur            x3, [x0, #7]
    // 0xad5a38: ldur            x2, [fp, #-8]
    // 0xad5a3c: stur            x3, [fp, #-0x18]
    // 0xad5a40: r1 = Function '<anonymous closure>':.
    //     0xad5a40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa68] AnonymousClosure: (0xad5adc), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5a44: ldr             x1, [x1, #0xa68]
    // 0xad5a48: r0 = AllocateClosure()
    //     0xad5a48: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5a4c: r1 = Function '<anonymous closure>':.
    //     0xad5a4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] AnonymousClosure: (0xad5ad0), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5a50: ldr             x1, [x1, #0xa70]
    // 0xad5a54: r2 = Null
    //     0xad5a54: mov             x2, NULL
    // 0xad5a58: stur            x0, [fp, #-8]
    // 0xad5a5c: r0 = AllocateClosure()
    //     0xad5a5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5a60: stur            x0, [fp, #-0x20]
    // 0xad5a64: r0 = ListView()
    //     0xad5a64: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad5a68: stur            x0, [fp, #-0x28]
    // 0xad5a6c: r16 = Instance_EdgeInsets
    //     0xad5a6c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xad5a70: ldr             x16, [x16, #0x360]
    // 0xad5a74: r30 = true
    //     0xad5a74: add             lr, NULL, #0x20  ; true
    // 0xad5a78: stp             lr, x16, [SP, #8]
    // 0xad5a7c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xad5a7c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xad5a80: ldr             x16, [x16, #0x290]
    // 0xad5a84: str             x16, [SP]
    // 0xad5a88: mov             x1, x0
    // 0xad5a8c: ldur            x2, [fp, #-8]
    // 0xad5a90: ldur            x3, [fp, #-0x18]
    // 0xad5a94: ldur            x5, [fp, #-0x20]
    // 0xad5a98: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x4, physics, 0x6, shrinkWrap, 0x5, null]
    //     0xad5a98: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x4, "physics", 0x6, "shrinkWrap", 0x5, Null]
    //     0xad5a9c: ldr             x4, [x4, #0xa78]
    // 0xad5aa0: r0 = ListView.separated()
    //     0xad5aa0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad5aa4: ldur            x1, [fp, #-0x10]
    // 0xad5aa8: cbz             w1, #0xad5ab4
    // 0xad5aac: ldur            x0, [fp, #-0x28]
    // 0xad5ab0: b               #0xad5abc
    // 0xad5ab4: r0 = Instance_SizedBox
    //     0xad5ab4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad5ab8: ldr             x0, [x0, #0xc40]
    // 0xad5abc: LeaveFrame
    //     0xad5abc: mov             SP, fp
    //     0xad5ac0: ldp             fp, lr, [SP], #0x10
    // 0xad5ac4: ret
    //     0xad5ac4: ret             
    // 0xad5ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5ac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5acc: b               #0xad59b4
  }
  [closure] Divider <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad5ad0, size: 0xc
    // 0xad5ad0: r0 = Instance_Divider
    //     0xad5ad0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa80] Obj!Divider@e25741
    //     0xad5ad4: ldr             x0, [x0, #0xa80]
    // 0xad5ad8: ret
    //     0xad5ad8: ret             
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad5adc, size: 0xd8
    // 0xad5adc: EnterFrame
    //     0xad5adc: stp             fp, lr, [SP, #-0x10]!
    //     0xad5ae0: mov             fp, SP
    // 0xad5ae4: AllocStack(0x20)
    //     0xad5ae4: sub             SP, SP, #0x20
    // 0xad5ae8: SetupParameters()
    //     0xad5ae8: ldr             x0, [fp, #0x20]
    //     0xad5aec: ldur            w1, [x0, #0x17]
    //     0xad5af0: add             x1, x1, HEAP, lsl #32
    //     0xad5af4: stur            x1, [fp, #-8]
    // 0xad5af8: CheckStackOverflow
    //     0xad5af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5afc: cmp             SP, x16
    //     0xad5b00: b.ls            #0xad5bac
    // 0xad5b04: r1 = 1
    //     0xad5b04: movz            x1, #0x1
    // 0xad5b08: r0 = AllocateContext()
    //     0xad5b08: bl              #0xec126c  ; AllocateContextStub
    // 0xad5b0c: mov             x2, x0
    // 0xad5b10: ldur            x0, [fp, #-8]
    // 0xad5b14: stur            x2, [fp, #-0x10]
    // 0xad5b18: StoreField: r2->field_b = r0
    //     0xad5b18: stur            w0, [x2, #0xb]
    // 0xad5b1c: LoadField: r1 = r0->field_f
    //     0xad5b1c: ldur            w1, [x0, #0xf]
    // 0xad5b20: DecompressPointer r1
    //     0xad5b20: add             x1, x1, HEAP, lsl #32
    // 0xad5b24: r0 = controller()
    //     0xad5b24: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5b28: LoadField: r1 = r0->field_37
    //     0xad5b28: ldur            w1, [x0, #0x37]
    // 0xad5b2c: DecompressPointer r1
    //     0xad5b2c: add             x1, x1, HEAP, lsl #32
    // 0xad5b30: r0 = value()
    //     0xad5b30: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad5b34: r1 = LoadClassIdInstr(r0)
    //     0xad5b34: ldur            x1, [x0, #-1]
    //     0xad5b38: ubfx            x1, x1, #0xc, #0x14
    // 0xad5b3c: ldr             x16, [fp, #0x10]
    // 0xad5b40: stp             x16, x0, [SP]
    // 0xad5b44: mov             x0, x1
    // 0xad5b48: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad5b48: movz            x17, #0x3037
    //     0xad5b4c: movk            x17, #0x1, lsl #16
    //     0xad5b50: add             lr, x0, x17
    //     0xad5b54: ldr             lr, [x21, lr, lsl #3]
    //     0xad5b58: blr             lr
    // 0xad5b5c: ldur            x2, [fp, #-0x10]
    // 0xad5b60: StoreField: r2->field_f = r0
    //     0xad5b60: stur            w0, [x2, #0xf]
    //     0xad5b64: ldurb           w16, [x2, #-1]
    //     0xad5b68: ldurb           w17, [x0, #-1]
    //     0xad5b6c: and             x16, x17, x16, lsr #2
    //     0xad5b70: tst             x16, HEAP, lsr #32
    //     0xad5b74: b.eq            #0xad5b7c
    //     0xad5b78: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xad5b7c: r0 = Obx()
    //     0xad5b7c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad5b80: ldur            x2, [fp, #-0x10]
    // 0xad5b84: r1 = Function '<anonymous closure>':.
    //     0xad5b84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa88] AnonymousClosure: (0xad5bb4), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5b88: ldr             x1, [x1, #0xa88]
    // 0xad5b8c: stur            x0, [fp, #-8]
    // 0xad5b90: r0 = AllocateClosure()
    //     0xad5b90: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5b94: mov             x1, x0
    // 0xad5b98: ldur            x0, [fp, #-8]
    // 0xad5b9c: StoreField: r0->field_b = r1
    //     0xad5b9c: stur            w1, [x0, #0xb]
    // 0xad5ba0: LeaveFrame
    //     0xad5ba0: mov             SP, fp
    //     0xad5ba4: ldp             fp, lr, [SP], #0x10
    // 0xad5ba8: ret
    //     0xad5ba8: ret             
    // 0xad5bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5bac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5bb0: b               #0xad5b04
  }
  [closure] NCalendarEventListTile <anonymous closure>(dynamic) {
    // ** addr: 0xad5bb4, size: 0x114
    // 0xad5bb4: EnterFrame
    //     0xad5bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xad5bb8: mov             fp, SP
    // 0xad5bbc: AllocStack(0x38)
    //     0xad5bbc: sub             SP, SP, #0x38
    // 0xad5bc0: SetupParameters()
    //     0xad5bc0: ldr             x0, [fp, #0x10]
    //     0xad5bc4: ldur            w2, [x0, #0x17]
    //     0xad5bc8: add             x2, x2, HEAP, lsl #32
    //     0xad5bcc: stur            x2, [fp, #-8]
    // 0xad5bd0: CheckStackOverflow
    //     0xad5bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5bd4: cmp             SP, x16
    //     0xad5bd8: b.ls            #0xad5cc0
    // 0xad5bdc: LoadField: r0 = r2->field_b
    //     0xad5bdc: ldur            w0, [x2, #0xb]
    // 0xad5be0: DecompressPointer r0
    //     0xad5be0: add             x0, x0, HEAP, lsl #32
    // 0xad5be4: LoadField: r1 = r0->field_f
    //     0xad5be4: ldur            w1, [x0, #0xf]
    // 0xad5be8: DecompressPointer r1
    //     0xad5be8: add             x1, x1, HEAP, lsl #32
    // 0xad5bec: r0 = controller()
    //     0xad5bec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5bf0: mov             x1, x0
    // 0xad5bf4: ldur            x0, [fp, #-8]
    // 0xad5bf8: LoadField: r3 = r0->field_f
    //     0xad5bf8: ldur            w3, [x0, #0xf]
    // 0xad5bfc: DecompressPointer r3
    //     0xad5bfc: add             x3, x3, HEAP, lsl #32
    // 0xad5c00: stur            x3, [fp, #-0x18]
    // 0xad5c04: LoadField: r4 = r3->field_7
    //     0xad5c04: ldur            w4, [x3, #7]
    // 0xad5c08: DecompressPointer r4
    //     0xad5c08: add             x4, x4, HEAP, lsl #32
    // 0xad5c0c: mov             x2, x4
    // 0xad5c10: stur            x4, [fp, #-0x10]
    // 0xad5c14: r0 = isSelected()
    //     0xad5c14: bl              #0xad5cd4  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::isSelected
    // 0xad5c18: mov             x3, x0
    // 0xad5c1c: ldur            x0, [fp, #-0x18]
    // 0xad5c20: stur            x3, [fp, #-0x38]
    // 0xad5c24: LoadField: r4 = r0->field_b
    //     0xad5c24: ldur            w4, [x0, #0xb]
    // 0xad5c28: DecompressPointer r4
    //     0xad5c28: add             x4, x4, HEAP, lsl #32
    // 0xad5c2c: stur            x4, [fp, #-0x30]
    // 0xad5c30: LoadField: r5 = r0->field_f
    //     0xad5c30: ldur            w5, [x0, #0xf]
    // 0xad5c34: DecompressPointer r5
    //     0xad5c34: add             x5, x5, HEAP, lsl #32
    // 0xad5c38: stur            x5, [fp, #-0x28]
    // 0xad5c3c: LoadField: r6 = r0->field_13
    //     0xad5c3c: ldur            w6, [x0, #0x13]
    // 0xad5c40: DecompressPointer r6
    //     0xad5c40: add             x6, x6, HEAP, lsl #32
    // 0xad5c44: ldur            x2, [fp, #-8]
    // 0xad5c48: stur            x6, [fp, #-0x20]
    // 0xad5c4c: r1 = Function '<anonymous closure>':.
    //     0xad5c4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa90] AnonymousClosure: (0xad5d28), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5c50: ldr             x1, [x1, #0xa90]
    // 0xad5c54: r0 = AllocateClosure()
    //     0xad5c54: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5c58: mov             x1, x0
    // 0xad5c5c: ldur            x0, [fp, #-0x20]
    // 0xad5c60: cmp             w0, NULL
    // 0xad5c64: b.eq            #0xad5c70
    // 0xad5c68: mov             x4, x1
    // 0xad5c6c: b               #0xad5c74
    // 0xad5c70: r4 = Null
    //     0xad5c70: mov             x4, NULL
    // 0xad5c74: ldur            x3, [fp, #-0x10]
    // 0xad5c78: ldur            x0, [fp, #-0x38]
    // 0xad5c7c: ldur            x1, [fp, #-0x30]
    // 0xad5c80: ldur            x2, [fp, #-0x28]
    // 0xad5c84: stur            x4, [fp, #-8]
    // 0xad5c88: r0 = NCalendarEventListTile()
    //     0xad5c88: bl              #0xad5cc8  ; AllocateNCalendarEventListTileStub -> NCalendarEventListTile (size=0x20)
    // 0xad5c8c: ldur            x1, [fp, #-0x10]
    // 0xad5c90: StoreField: r0->field_b = r1
    //     0xad5c90: stur            w1, [x0, #0xb]
    // 0xad5c94: ldur            x1, [fp, #-0x30]
    // 0xad5c98: StoreField: r0->field_f = r1
    //     0xad5c98: stur            w1, [x0, #0xf]
    // 0xad5c9c: ldur            x1, [fp, #-0x28]
    // 0xad5ca0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad5ca0: stur            w1, [x0, #0x17]
    // 0xad5ca4: ldur            x1, [fp, #-0x38]
    // 0xad5ca8: StoreField: r0->field_13 = r1
    //     0xad5ca8: stur            w1, [x0, #0x13]
    // 0xad5cac: ldur            x1, [fp, #-8]
    // 0xad5cb0: StoreField: r0->field_1b = r1
    //     0xad5cb0: stur            w1, [x0, #0x1b]
    // 0xad5cb4: LeaveFrame
    //     0xad5cb4: mov             SP, fp
    //     0xad5cb8: ldp             fp, lr, [SP], #0x10
    // 0xad5cbc: ret
    //     0xad5cbc: ret             
    // 0xad5cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5cc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5cc4: b               #0xad5bdc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad5d28, size: 0x64
    // 0xad5d28: EnterFrame
    //     0xad5d28: stp             fp, lr, [SP, #-0x10]!
    //     0xad5d2c: mov             fp, SP
    // 0xad5d30: AllocStack(0x8)
    //     0xad5d30: sub             SP, SP, #8
    // 0xad5d34: SetupParameters()
    //     0xad5d34: ldr             x0, [fp, #0x10]
    //     0xad5d38: ldur            w2, [x0, #0x17]
    //     0xad5d3c: add             x2, x2, HEAP, lsl #32
    //     0xad5d40: stur            x2, [fp, #-8]
    // 0xad5d44: CheckStackOverflow
    //     0xad5d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5d48: cmp             SP, x16
    //     0xad5d4c: b.ls            #0xad5d84
    // 0xad5d50: LoadField: r0 = r2->field_b
    //     0xad5d50: ldur            w0, [x2, #0xb]
    // 0xad5d54: DecompressPointer r0
    //     0xad5d54: add             x0, x0, HEAP, lsl #32
    // 0xad5d58: LoadField: r1 = r0->field_f
    //     0xad5d58: ldur            w1, [x0, #0xf]
    // 0xad5d5c: DecompressPointer r1
    //     0xad5d5c: add             x1, x1, HEAP, lsl #32
    // 0xad5d60: r0 = controller()
    //     0xad5d60: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5d64: mov             x1, x0
    // 0xad5d68: ldur            x0, [fp, #-8]
    // 0xad5d6c: LoadField: r2 = r0->field_f
    //     0xad5d6c: ldur            w2, [x0, #0xf]
    // 0xad5d70: DecompressPointer r2
    //     0xad5d70: add             x2, x2, HEAP, lsl #32
    // 0xad5d74: r0 = onEventActionPressed()
    //     0xad5d74: bl              #0xad5d8c  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onEventActionPressed
    // 0xad5d78: LeaveFrame
    //     0xad5d78: mov             SP, fp
    //     0xad5d7c: ldp             fp, lr, [SP], #0x10
    // 0xad5d80: ret
    //     0xad5d80: ret             
    // 0xad5d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5d88: b               #0xad5d50
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xad5e70, size: 0x144
    // 0xad5e70: EnterFrame
    //     0xad5e70: stp             fp, lr, [SP, #-0x10]!
    //     0xad5e74: mov             fp, SP
    // 0xad5e78: AllocStack(0x40)
    //     0xad5e78: sub             SP, SP, #0x40
    // 0xad5e7c: SetupParameters()
    //     0xad5e7c: ldr             x0, [fp, #0x10]
    //     0xad5e80: ldur            w2, [x0, #0x17]
    //     0xad5e84: add             x2, x2, HEAP, lsl #32
    //     0xad5e88: stur            x2, [fp, #-8]
    // 0xad5e8c: CheckStackOverflow
    //     0xad5e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5e90: cmp             SP, x16
    //     0xad5e94: b.ls            #0xad5fac
    // 0xad5e98: LoadField: r1 = r2->field_f
    //     0xad5e98: ldur            w1, [x2, #0xf]
    // 0xad5e9c: DecompressPointer r1
    //     0xad5e9c: add             x1, x1, HEAP, lsl #32
    // 0xad5ea0: r0 = controller()
    //     0xad5ea0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5ea4: LoadField: r1 = r0->field_3f
    //     0xad5ea4: ldur            w1, [x0, #0x3f]
    // 0xad5ea8: DecompressPointer r1
    //     0xad5ea8: add             x1, x1, HEAP, lsl #32
    // 0xad5eac: r0 = value()
    //     0xad5eac: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad5eb0: r1 = LoadClassIdInstr(r0)
    //     0xad5eb0: ldur            x1, [x0, #-1]
    //     0xad5eb4: ubfx            x1, x1, #0xc, #0x14
    // 0xad5eb8: str             x0, [SP]
    // 0xad5ebc: mov             x0, x1
    // 0xad5ec0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad5ec0: movz            x17, #0xc834
    //     0xad5ec4: add             lr, x0, x17
    //     0xad5ec8: ldr             lr, [x21, lr, lsl #3]
    //     0xad5ecc: blr             lr
    // 0xad5ed0: ldur            x2, [fp, #-8]
    // 0xad5ed4: stur            x0, [fp, #-0x10]
    // 0xad5ed8: LoadField: r1 = r2->field_f
    //     0xad5ed8: ldur            w1, [x2, #0xf]
    // 0xad5edc: DecompressPointer r1
    //     0xad5edc: add             x1, x1, HEAP, lsl #32
    // 0xad5ee0: r0 = controller()
    //     0xad5ee0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5ee4: LoadField: r1 = r0->field_3f
    //     0xad5ee4: ldur            w1, [x0, #0x3f]
    // 0xad5ee8: DecompressPointer r1
    //     0xad5ee8: add             x1, x1, HEAP, lsl #32
    // 0xad5eec: r0 = value()
    //     0xad5eec: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad5ef0: r1 = LoadClassIdInstr(r0)
    //     0xad5ef0: ldur            x1, [x0, #-1]
    //     0xad5ef4: ubfx            x1, x1, #0xc, #0x14
    // 0xad5ef8: str             x0, [SP]
    // 0xad5efc: mov             x0, x1
    // 0xad5f00: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad5f00: movz            x17, #0xc834
    //     0xad5f04: add             lr, x0, x17
    //     0xad5f08: ldr             lr, [x21, lr, lsl #3]
    //     0xad5f0c: blr             lr
    // 0xad5f10: r3 = LoadInt32Instr(r0)
    //     0xad5f10: sbfx            x3, x0, #1, #0x1f
    //     0xad5f14: tbz             w0, #0, #0xad5f1c
    //     0xad5f18: ldur            x3, [x0, #7]
    // 0xad5f1c: ldur            x2, [fp, #-8]
    // 0xad5f20: stur            x3, [fp, #-0x18]
    // 0xad5f24: r1 = Function '<anonymous closure>':.
    //     0xad5f24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa98] AnonymousClosure: (0xad5fb4), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5f28: ldr             x1, [x1, #0xa98]
    // 0xad5f2c: r0 = AllocateClosure()
    //     0xad5f2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5f30: r1 = Function '<anonymous closure>':.
    //     0xad5f30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2faa0] AnonymousClosure: (0xad5ad0), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad5f34: ldr             x1, [x1, #0xaa0]
    // 0xad5f38: r2 = Null
    //     0xad5f38: mov             x2, NULL
    // 0xad5f3c: stur            x0, [fp, #-8]
    // 0xad5f40: r0 = AllocateClosure()
    //     0xad5f40: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5f44: stur            x0, [fp, #-0x20]
    // 0xad5f48: r0 = ListView()
    //     0xad5f48: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad5f4c: stur            x0, [fp, #-0x28]
    // 0xad5f50: r16 = Instance_EdgeInsets
    //     0xad5f50: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xad5f54: ldr             x16, [x16, #0x360]
    // 0xad5f58: r30 = true
    //     0xad5f58: add             lr, NULL, #0x20  ; true
    // 0xad5f5c: stp             lr, x16, [SP, #8]
    // 0xad5f60: r16 = Instance_NeverScrollableScrollPhysics
    //     0xad5f60: add             x16, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xad5f64: ldr             x16, [x16, #0x290]
    // 0xad5f68: str             x16, [SP]
    // 0xad5f6c: mov             x1, x0
    // 0xad5f70: ldur            x2, [fp, #-8]
    // 0xad5f74: ldur            x3, [fp, #-0x18]
    // 0xad5f78: ldur            x5, [fp, #-0x20]
    // 0xad5f7c: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x4, physics, 0x6, shrinkWrap, 0x5, null]
    //     0xad5f7c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x4, "physics", 0x6, "shrinkWrap", 0x5, Null]
    //     0xad5f80: ldr             x4, [x4, #0xa78]
    // 0xad5f84: r0 = ListView.separated()
    //     0xad5f84: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad5f88: ldur            x1, [fp, #-0x10]
    // 0xad5f8c: cbz             w1, #0xad5f98
    // 0xad5f90: ldur            x0, [fp, #-0x28]
    // 0xad5f94: b               #0xad5fa0
    // 0xad5f98: r0 = Instance_SizedBox
    //     0xad5f98: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad5f9c: ldr             x0, [x0, #0xc40]
    // 0xad5fa0: LeaveFrame
    //     0xad5fa0: mov             SP, fp
    //     0xad5fa4: ldp             fp, lr, [SP], #0x10
    // 0xad5fa8: ret
    //     0xad5fa8: ret             
    // 0xad5fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5fac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5fb0: b               #0xad5e98
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad5fb4, size: 0xd8
    // 0xad5fb4: EnterFrame
    //     0xad5fb4: stp             fp, lr, [SP, #-0x10]!
    //     0xad5fb8: mov             fp, SP
    // 0xad5fbc: AllocStack(0x20)
    //     0xad5fbc: sub             SP, SP, #0x20
    // 0xad5fc0: SetupParameters()
    //     0xad5fc0: ldr             x0, [fp, #0x20]
    //     0xad5fc4: ldur            w1, [x0, #0x17]
    //     0xad5fc8: add             x1, x1, HEAP, lsl #32
    //     0xad5fcc: stur            x1, [fp, #-8]
    // 0xad5fd0: CheckStackOverflow
    //     0xad5fd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5fd4: cmp             SP, x16
    //     0xad5fd8: b.ls            #0xad6084
    // 0xad5fdc: r1 = 1
    //     0xad5fdc: movz            x1, #0x1
    // 0xad5fe0: r0 = AllocateContext()
    //     0xad5fe0: bl              #0xec126c  ; AllocateContextStub
    // 0xad5fe4: mov             x2, x0
    // 0xad5fe8: ldur            x0, [fp, #-8]
    // 0xad5fec: stur            x2, [fp, #-0x10]
    // 0xad5ff0: StoreField: r2->field_b = r0
    //     0xad5ff0: stur            w0, [x2, #0xb]
    // 0xad5ff4: LoadField: r1 = r0->field_f
    //     0xad5ff4: ldur            w1, [x0, #0xf]
    // 0xad5ff8: DecompressPointer r1
    //     0xad5ff8: add             x1, x1, HEAP, lsl #32
    // 0xad5ffc: r0 = controller()
    //     0xad5ffc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6000: LoadField: r1 = r0->field_3f
    //     0xad6000: ldur            w1, [x0, #0x3f]
    // 0xad6004: DecompressPointer r1
    //     0xad6004: add             x1, x1, HEAP, lsl #32
    // 0xad6008: r0 = value()
    //     0xad6008: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad600c: r1 = LoadClassIdInstr(r0)
    //     0xad600c: ldur            x1, [x0, #-1]
    //     0xad6010: ubfx            x1, x1, #0xc, #0x14
    // 0xad6014: ldr             x16, [fp, #0x10]
    // 0xad6018: stp             x16, x0, [SP]
    // 0xad601c: mov             x0, x1
    // 0xad6020: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad6020: movz            x17, #0x3037
    //     0xad6024: movk            x17, #0x1, lsl #16
    //     0xad6028: add             lr, x0, x17
    //     0xad602c: ldr             lr, [x21, lr, lsl #3]
    //     0xad6030: blr             lr
    // 0xad6034: ldur            x2, [fp, #-0x10]
    // 0xad6038: StoreField: r2->field_f = r0
    //     0xad6038: stur            w0, [x2, #0xf]
    //     0xad603c: ldurb           w16, [x2, #-1]
    //     0xad6040: ldurb           w17, [x0, #-1]
    //     0xad6044: and             x16, x17, x16, lsr #2
    //     0xad6048: tst             x16, HEAP, lsr #32
    //     0xad604c: b.eq            #0xad6054
    //     0xad6050: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xad6054: r0 = Obx()
    //     0xad6054: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad6058: ldur            x2, [fp, #-0x10]
    // 0xad605c: r1 = Function '<anonymous closure>':.
    //     0xad605c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2faa8] AnonymousClosure: (0xad608c), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad6060: ldr             x1, [x1, #0xaa8]
    // 0xad6064: stur            x0, [fp, #-8]
    // 0xad6068: r0 = AllocateClosure()
    //     0xad6068: bl              #0xec1630  ; AllocateClosureStub
    // 0xad606c: mov             x1, x0
    // 0xad6070: ldur            x0, [fp, #-8]
    // 0xad6074: StoreField: r0->field_b = r1
    //     0xad6074: stur            w1, [x0, #0xb]
    // 0xad6078: LeaveFrame
    //     0xad6078: mov             SP, fp
    //     0xad607c: ldp             fp, lr, [SP], #0x10
    // 0xad6080: ret
    //     0xad6080: ret             
    // 0xad6084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6088: b               #0xad5fdc
  }
  [closure] NCalendarEventListTile <anonymous closure>(dynamic) {
    // ** addr: 0xad608c, size: 0x114
    // 0xad608c: EnterFrame
    //     0xad608c: stp             fp, lr, [SP, #-0x10]!
    //     0xad6090: mov             fp, SP
    // 0xad6094: AllocStack(0x38)
    //     0xad6094: sub             SP, SP, #0x38
    // 0xad6098: SetupParameters()
    //     0xad6098: ldr             x0, [fp, #0x10]
    //     0xad609c: ldur            w2, [x0, #0x17]
    //     0xad60a0: add             x2, x2, HEAP, lsl #32
    //     0xad60a4: stur            x2, [fp, #-8]
    // 0xad60a8: CheckStackOverflow
    //     0xad60a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad60ac: cmp             SP, x16
    //     0xad60b0: b.ls            #0xad6198
    // 0xad60b4: LoadField: r0 = r2->field_b
    //     0xad60b4: ldur            w0, [x2, #0xb]
    // 0xad60b8: DecompressPointer r0
    //     0xad60b8: add             x0, x0, HEAP, lsl #32
    // 0xad60bc: LoadField: r1 = r0->field_f
    //     0xad60bc: ldur            w1, [x0, #0xf]
    // 0xad60c0: DecompressPointer r1
    //     0xad60c0: add             x1, x1, HEAP, lsl #32
    // 0xad60c4: r0 = controller()
    //     0xad60c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad60c8: mov             x1, x0
    // 0xad60cc: ldur            x0, [fp, #-8]
    // 0xad60d0: LoadField: r3 = r0->field_f
    //     0xad60d0: ldur            w3, [x0, #0xf]
    // 0xad60d4: DecompressPointer r3
    //     0xad60d4: add             x3, x3, HEAP, lsl #32
    // 0xad60d8: stur            x3, [fp, #-0x18]
    // 0xad60dc: LoadField: r4 = r3->field_7
    //     0xad60dc: ldur            w4, [x3, #7]
    // 0xad60e0: DecompressPointer r4
    //     0xad60e0: add             x4, x4, HEAP, lsl #32
    // 0xad60e4: mov             x2, x4
    // 0xad60e8: stur            x4, [fp, #-0x10]
    // 0xad60ec: r0 = isSelected()
    //     0xad60ec: bl              #0xad5cd4  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::isSelected
    // 0xad60f0: mov             x3, x0
    // 0xad60f4: ldur            x0, [fp, #-0x18]
    // 0xad60f8: stur            x3, [fp, #-0x38]
    // 0xad60fc: LoadField: r4 = r0->field_b
    //     0xad60fc: ldur            w4, [x0, #0xb]
    // 0xad6100: DecompressPointer r4
    //     0xad6100: add             x4, x4, HEAP, lsl #32
    // 0xad6104: stur            x4, [fp, #-0x30]
    // 0xad6108: LoadField: r5 = r0->field_f
    //     0xad6108: ldur            w5, [x0, #0xf]
    // 0xad610c: DecompressPointer r5
    //     0xad610c: add             x5, x5, HEAP, lsl #32
    // 0xad6110: stur            x5, [fp, #-0x28]
    // 0xad6114: LoadField: r6 = r0->field_13
    //     0xad6114: ldur            w6, [x0, #0x13]
    // 0xad6118: DecompressPointer r6
    //     0xad6118: add             x6, x6, HEAP, lsl #32
    // 0xad611c: ldur            x2, [fp, #-8]
    // 0xad6120: stur            x6, [fp, #-0x20]
    // 0xad6124: r1 = Function '<anonymous closure>':.
    //     0xad6124: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fab0] AnonymousClosure: (0xad5d28), in [package:nuonline/app/modules/calendar/views/calendar_view.dart] CalendarView::build (0xad5400)
    //     0xad6128: ldr             x1, [x1, #0xab0]
    // 0xad612c: r0 = AllocateClosure()
    //     0xad612c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad6130: mov             x1, x0
    // 0xad6134: ldur            x0, [fp, #-0x20]
    // 0xad6138: cmp             w0, NULL
    // 0xad613c: b.eq            #0xad6148
    // 0xad6140: mov             x4, x1
    // 0xad6144: b               #0xad614c
    // 0xad6148: r4 = Null
    //     0xad6148: mov             x4, NULL
    // 0xad614c: ldur            x3, [fp, #-0x10]
    // 0xad6150: ldur            x0, [fp, #-0x38]
    // 0xad6154: ldur            x1, [fp, #-0x30]
    // 0xad6158: ldur            x2, [fp, #-0x28]
    // 0xad615c: stur            x4, [fp, #-8]
    // 0xad6160: r0 = NCalendarEventListTile()
    //     0xad6160: bl              #0xad5cc8  ; AllocateNCalendarEventListTileStub -> NCalendarEventListTile (size=0x20)
    // 0xad6164: ldur            x1, [fp, #-0x10]
    // 0xad6168: StoreField: r0->field_b = r1
    //     0xad6168: stur            w1, [x0, #0xb]
    // 0xad616c: ldur            x1, [fp, #-0x30]
    // 0xad6170: StoreField: r0->field_f = r1
    //     0xad6170: stur            w1, [x0, #0xf]
    // 0xad6174: ldur            x1, [fp, #-0x28]
    // 0xad6178: ArrayStore: r0[0] = r1  ; List_4
    //     0xad6178: stur            w1, [x0, #0x17]
    // 0xad617c: ldur            x1, [fp, #-0x38]
    // 0xad6180: StoreField: r0->field_13 = r1
    //     0xad6180: stur            w1, [x0, #0x13]
    // 0xad6184: ldur            x1, [fp, #-8]
    // 0xad6188: StoreField: r0->field_1b = r1
    //     0xad6188: stur            w1, [x0, #0x1b]
    // 0xad618c: LeaveFrame
    //     0xad618c: mov             SP, fp
    //     0xad6190: ldp             fp, lr, [SP], #0x10
    // 0xad6194: ret
    //     0xad6194: ret             
    // 0xad6198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad619c: b               #0xad60b4
  }
  [closure] NCalendar <anonymous closure>(dynamic) {
    // ** addr: 0xad61a0, size: 0x27c
    // 0xad61a0: EnterFrame
    //     0xad61a0: stp             fp, lr, [SP, #-0x10]!
    //     0xad61a4: mov             fp, SP
    // 0xad61a8: AllocStack(0x60)
    //     0xad61a8: sub             SP, SP, #0x60
    // 0xad61ac: SetupParameters()
    //     0xad61ac: ldr             x0, [fp, #0x10]
    //     0xad61b0: ldur            w2, [x0, #0x17]
    //     0xad61b4: add             x2, x2, HEAP, lsl #32
    //     0xad61b8: stur            x2, [fp, #-8]
    // 0xad61bc: CheckStackOverflow
    //     0xad61bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad61c0: cmp             SP, x16
    //     0xad61c4: b.ls            #0xad6414
    // 0xad61c8: LoadField: r1 = r2->field_f
    //     0xad61c8: ldur            w1, [x2, #0xf]
    // 0xad61cc: DecompressPointer r1
    //     0xad61cc: add             x1, x1, HEAP, lsl #32
    // 0xad61d0: r0 = controller()
    //     0xad61d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad61d4: LoadField: r1 = r0->field_27
    //     0xad61d4: ldur            w1, [x0, #0x27]
    // 0xad61d8: DecompressPointer r1
    //     0xad61d8: add             x1, x1, HEAP, lsl #32
    // 0xad61dc: LoadField: r0 = r1->field_23
    //     0xad61dc: ldur            w0, [x1, #0x23]
    // 0xad61e0: DecompressPointer r0
    //     0xad61e0: add             x0, x0, HEAP, lsl #32
    // 0xad61e4: mov             x1, x0
    // 0xad61e8: r0 = value()
    //     0xad61e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad61ec: mov             x2, x0
    // 0xad61f0: ldur            x0, [fp, #-8]
    // 0xad61f4: stur            x2, [fp, #-0x10]
    // 0xad61f8: LoadField: r1 = r0->field_f
    //     0xad61f8: ldur            w1, [x0, #0xf]
    // 0xad61fc: DecompressPointer r1
    //     0xad61fc: add             x1, x1, HEAP, lsl #32
    // 0xad6200: r0 = controller()
    //     0xad6200: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6204: LoadField: r1 = r0->field_27
    //     0xad6204: ldur            w1, [x0, #0x27]
    // 0xad6208: DecompressPointer r1
    //     0xad6208: add             x1, x1, HEAP, lsl #32
    // 0xad620c: r0 = hijriMethod()
    //     0xad620c: bl              #0x817818  ; [package:nuonline/services/hijri_service.dart] HijriService::hijriMethod
    // 0xad6210: mov             x2, x0
    // 0xad6214: ldur            x0, [fp, #-8]
    // 0xad6218: stur            x2, [fp, #-0x18]
    // 0xad621c: LoadField: r1 = r0->field_f
    //     0xad621c: ldur            w1, [x0, #0xf]
    // 0xad6220: DecompressPointer r1
    //     0xad6220: add             x1, x1, HEAP, lsl #32
    // 0xad6224: r0 = controller()
    //     0xad6224: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6228: mov             x1, x0
    // 0xad622c: LoadField: r0 = r1->field_2b
    //     0xad622c: ldur            w0, [x1, #0x2b]
    // 0xad6230: DecompressPointer r0
    //     0xad6230: add             x0, x0, HEAP, lsl #32
    // 0xad6234: r16 = Sentinel
    //     0xad6234: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad6238: cmp             w0, w16
    // 0xad623c: b.ne            #0xad624c
    // 0xad6240: r2 = firstDay
    //     0xad6240: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fab8] Field <CalendarController.firstDay>: late final (offset: 0x2c)
    //     0xad6244: ldr             x2, [x2, #0xab8]
    // 0xad6248: r0 = InitLateFinalInstanceField()
    //     0xad6248: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xad624c: mov             x2, x0
    // 0xad6250: ldur            x0, [fp, #-8]
    // 0xad6254: stur            x2, [fp, #-0x20]
    // 0xad6258: LoadField: r1 = r0->field_f
    //     0xad6258: ldur            w1, [x0, #0xf]
    // 0xad625c: DecompressPointer r1
    //     0xad625c: add             x1, x1, HEAP, lsl #32
    // 0xad6260: r0 = controller()
    //     0xad6260: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6264: mov             x1, x0
    // 0xad6268: LoadField: r0 = r1->field_2f
    //     0xad6268: ldur            w0, [x1, #0x2f]
    // 0xad626c: DecompressPointer r0
    //     0xad626c: add             x0, x0, HEAP, lsl #32
    // 0xad6270: r16 = Sentinel
    //     0xad6270: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad6274: cmp             w0, w16
    // 0xad6278: b.ne            #0xad6288
    // 0xad627c: r2 = lastDay
    //     0xad627c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fac0] Field <CalendarController.lastDay>: late final (offset: 0x30)
    //     0xad6280: ldr             x2, [x2, #0xac0]
    // 0xad6284: r0 = InitLateFinalInstanceField()
    //     0xad6284: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xad6288: mov             x2, x0
    // 0xad628c: ldur            x0, [fp, #-8]
    // 0xad6290: stur            x2, [fp, #-0x28]
    // 0xad6294: LoadField: r1 = r0->field_f
    //     0xad6294: ldur            w1, [x0, #0xf]
    // 0xad6298: DecompressPointer r1
    //     0xad6298: add             x1, x1, HEAP, lsl #32
    // 0xad629c: r0 = controller()
    //     0xad629c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad62a0: LoadField: r1 = r0->field_47
    //     0xad62a0: ldur            w1, [x0, #0x47]
    // 0xad62a4: DecompressPointer r1
    //     0xad62a4: add             x1, x1, HEAP, lsl #32
    // 0xad62a8: r0 = value()
    //     0xad62a8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad62ac: mov             x2, x0
    // 0xad62b0: ldur            x0, [fp, #-8]
    // 0xad62b4: stur            x2, [fp, #-0x30]
    // 0xad62b8: LoadField: r1 = r0->field_f
    //     0xad62b8: ldur            w1, [x0, #0xf]
    // 0xad62bc: DecompressPointer r1
    //     0xad62bc: add             x1, x1, HEAP, lsl #32
    // 0xad62c0: r0 = controller()
    //     0xad62c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad62c4: LoadField: r1 = r0->field_4b
    //     0xad62c4: ldur            w1, [x0, #0x4b]
    // 0xad62c8: DecompressPointer r1
    //     0xad62c8: add             x1, x1, HEAP, lsl #32
    // 0xad62cc: r0 = value()
    //     0xad62cc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad62d0: mov             x2, x0
    // 0xad62d4: ldur            x0, [fp, #-8]
    // 0xad62d8: stur            x2, [fp, #-0x38]
    // 0xad62dc: LoadField: r1 = r0->field_f
    //     0xad62dc: ldur            w1, [x0, #0xf]
    // 0xad62e0: DecompressPointer r1
    //     0xad62e0: add             x1, x1, HEAP, lsl #32
    // 0xad62e4: r0 = controller()
    //     0xad62e4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad62e8: LoadField: r2 = r0->field_3b
    //     0xad62e8: ldur            w2, [x0, #0x3b]
    // 0xad62ec: DecompressPointer r2
    //     0xad62ec: add             x2, x2, HEAP, lsl #32
    // 0xad62f0: ldur            x0, [fp, #-8]
    // 0xad62f4: stur            x2, [fp, #-0x40]
    // 0xad62f8: LoadField: r1 = r0->field_f
    //     0xad62f8: ldur            w1, [x0, #0xf]
    // 0xad62fc: DecompressPointer r1
    //     0xad62fc: add             x1, x1, HEAP, lsl #32
    // 0xad6300: r0 = controller()
    //     0xad6300: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6304: mov             x2, x0
    // 0xad6308: ldur            x0, [fp, #-8]
    // 0xad630c: stur            x2, [fp, #-0x48]
    // 0xad6310: LoadField: r1 = r0->field_f
    //     0xad6310: ldur            w1, [x0, #0xf]
    // 0xad6314: DecompressPointer r1
    //     0xad6314: add             x1, x1, HEAP, lsl #32
    // 0xad6318: r0 = controller()
    //     0xad6318: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad631c: mov             x2, x0
    // 0xad6320: ldur            x0, [fp, #-8]
    // 0xad6324: stur            x2, [fp, #-0x50]
    // 0xad6328: LoadField: r1 = r0->field_f
    //     0xad6328: ldur            w1, [x0, #0xf]
    // 0xad632c: DecompressPointer r1
    //     0xad632c: add             x1, x1, HEAP, lsl #32
    // 0xad6330: r0 = controller()
    //     0xad6330: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6334: mov             x2, x0
    // 0xad6338: ldur            x0, [fp, #-8]
    // 0xad633c: stur            x2, [fp, #-0x58]
    // 0xad6340: LoadField: r1 = r0->field_f
    //     0xad6340: ldur            w1, [x0, #0xf]
    // 0xad6344: DecompressPointer r1
    //     0xad6344: add             x1, x1, HEAP, lsl #32
    // 0xad6348: r0 = controller()
    //     0xad6348: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad634c: LoadField: r1 = r0->field_43
    //     0xad634c: ldur            w1, [x0, #0x43]
    // 0xad6350: DecompressPointer r1
    //     0xad6350: add             x1, x1, HEAP, lsl #32
    // 0xad6354: r0 = value()
    //     0xad6354: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6358: stur            x0, [fp, #-8]
    // 0xad635c: r0 = NCalendar()
    //     0xad635c: bl              #0xad641c  ; AllocateNCalendarStub -> NCalendar (size=0x3c)
    // 0xad6360: mov             x3, x0
    // 0xad6364: ldur            x0, [fp, #-0x20]
    // 0xad6368: stur            x3, [fp, #-0x60]
    // 0xad636c: StoreField: r3->field_b = r0
    //     0xad636c: stur            w0, [x3, #0xb]
    // 0xad6370: ldur            x0, [fp, #-0x28]
    // 0xad6374: StoreField: r3->field_f = r0
    //     0xad6374: stur            w0, [x3, #0xf]
    // 0xad6378: ldur            x0, [fp, #-0x30]
    // 0xad637c: StoreField: r3->field_13 = r0
    //     0xad637c: stur            w0, [x3, #0x13]
    // 0xad6380: ldur            x0, [fp, #-0x10]
    // 0xad6384: r1 = LoadInt32Instr(r0)
    //     0xad6384: sbfx            x1, x0, #1, #0x1f
    //     0xad6388: tbz             w0, #0, #0xad6390
    //     0xad638c: ldur            x1, [x0, #7]
    // 0xad6390: StoreField: r3->field_33 = r1
    //     0xad6390: stur            x1, [x3, #0x33]
    // 0xad6394: ldur            x0, [fp, #-0x18]
    // 0xad6398: StoreField: r3->field_2f = r0
    //     0xad6398: stur            w0, [x3, #0x2f]
    // 0xad639c: ldur            x0, [fp, #-8]
    // 0xad63a0: StoreField: r3->field_2b = r0
    //     0xad63a0: stur            w0, [x3, #0x2b]
    // 0xad63a4: ldur            x0, [fp, #-0x38]
    // 0xad63a8: ArrayStore: r3[0] = r0  ; List_4
    //     0xad63a8: stur            w0, [x3, #0x17]
    // 0xad63ac: ldur            x2, [fp, #-0x50]
    // 0xad63b0: r1 = Function 'onDaySelected':.
    //     0xad63b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fac8] AnonymousClosure: (0xa50060), in [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onDaySelected (0xa500a0)
    //     0xad63b4: ldr             x1, [x1, #0xac8]
    // 0xad63b8: r0 = AllocateClosure()
    //     0xad63b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad63bc: mov             x1, x0
    // 0xad63c0: ldur            x0, [fp, #-0x60]
    // 0xad63c4: StoreField: r0->field_1b = r1
    //     0xad63c4: stur            w1, [x0, #0x1b]
    // 0xad63c8: ldur            x2, [fp, #-0x58]
    // 0xad63cc: r1 = Function 'onPageChanged':.
    //     0xad63cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fad0] AnonymousClosure: (0xa50bc0), in [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onPageChanged (0xa50bfc)
    //     0xad63d0: ldr             x1, [x1, #0xad0]
    // 0xad63d4: r0 = AllocateClosure()
    //     0xad63d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad63d8: mov             x1, x0
    // 0xad63dc: ldur            x0, [fp, #-0x60]
    // 0xad63e0: StoreField: r0->field_1f = r1
    //     0xad63e0: stur            w1, [x0, #0x1f]
    // 0xad63e4: ldur            x2, [fp, #-0x48]
    // 0xad63e8: r1 = Function 'onCalendarCreated':.
    //     0xad63e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fad8] AnonymousClosure: (0xad6428), of [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController
    //     0xad63ec: ldr             x1, [x1, #0xad8]
    // 0xad63f0: r0 = AllocateClosure()
    //     0xad63f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad63f4: mov             x1, x0
    // 0xad63f8: ldur            x0, [fp, #-0x60]
    // 0xad63fc: StoreField: r0->field_23 = r1
    //     0xad63fc: stur            w1, [x0, #0x23]
    // 0xad6400: ldur            x1, [fp, #-0x40]
    // 0xad6404: StoreField: r0->field_27 = r1
    //     0xad6404: stur            w1, [x0, #0x27]
    // 0xad6408: LeaveFrame
    //     0xad6408: mov             SP, fp
    //     0xad640c: ldp             fp, lr, [SP], #0x10
    // 0xad6410: ret
    //     0xad6410: ret             
    // 0xad6414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6418: b               #0xad61c8
  }
  [closure] NCalendarHeader <anonymous closure>(dynamic) {
    // ** addr: 0xad6514, size: 0xfc
    // 0xad6514: EnterFrame
    //     0xad6514: stp             fp, lr, [SP, #-0x10]!
    //     0xad6518: mov             fp, SP
    // 0xad651c: AllocStack(0x28)
    //     0xad651c: sub             SP, SP, #0x28
    // 0xad6520: SetupParameters()
    //     0xad6520: ldr             x0, [fp, #0x10]
    //     0xad6524: ldur            w2, [x0, #0x17]
    //     0xad6528: add             x2, x2, HEAP, lsl #32
    //     0xad652c: stur            x2, [fp, #-8]
    // 0xad6530: CheckStackOverflow
    //     0xad6530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6534: cmp             SP, x16
    //     0xad6538: b.ls            #0xad6608
    // 0xad653c: LoadField: r1 = r2->field_f
    //     0xad653c: ldur            w1, [x2, #0xf]
    // 0xad6540: DecompressPointer r1
    //     0xad6540: add             x1, x1, HEAP, lsl #32
    // 0xad6544: r0 = controller()
    //     0xad6544: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6548: LoadField: r1 = r0->field_4f
    //     0xad6548: ldur            w1, [x0, #0x4f]
    // 0xad654c: DecompressPointer r1
    //     0xad654c: add             x1, x1, HEAP, lsl #32
    // 0xad6550: r0 = value()
    //     0xad6550: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6554: mov             x2, x0
    // 0xad6558: ldur            x0, [fp, #-8]
    // 0xad655c: stur            x2, [fp, #-0x10]
    // 0xad6560: LoadField: r1 = r0->field_f
    //     0xad6560: ldur            w1, [x0, #0xf]
    // 0xad6564: DecompressPointer r1
    //     0xad6564: add             x1, x1, HEAP, lsl #32
    // 0xad6568: r0 = controller()
    //     0xad6568: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad656c: LoadField: r1 = r0->field_53
    //     0xad656c: ldur            w1, [x0, #0x53]
    // 0xad6570: DecompressPointer r1
    //     0xad6570: add             x1, x1, HEAP, lsl #32
    // 0xad6574: r0 = value()
    //     0xad6574: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6578: mov             x2, x0
    // 0xad657c: ldur            x0, [fp, #-8]
    // 0xad6580: stur            x2, [fp, #-0x18]
    // 0xad6584: LoadField: r1 = r0->field_f
    //     0xad6584: ldur            w1, [x0, #0xf]
    // 0xad6588: DecompressPointer r1
    //     0xad6588: add             x1, x1, HEAP, lsl #32
    // 0xad658c: r0 = controller()
    //     0xad658c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6590: mov             x2, x0
    // 0xad6594: ldur            x0, [fp, #-8]
    // 0xad6598: stur            x2, [fp, #-0x20]
    // 0xad659c: LoadField: r1 = r0->field_f
    //     0xad659c: ldur            w1, [x0, #0xf]
    // 0xad65a0: DecompressPointer r1
    //     0xad65a0: add             x1, x1, HEAP, lsl #32
    // 0xad65a4: r0 = controller()
    //     0xad65a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad65a8: mov             x2, x0
    // 0xad65ac: r1 = Function 'prevCalendarPage':.
    //     0xad65ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb10] AnonymousClosure: (0xad6788), in [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::prevCalendarPage (0xad67c0)
    //     0xad65b0: ldr             x1, [x1, #0xb10]
    // 0xad65b4: r0 = AllocateClosure()
    //     0xad65b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad65b8: stur            x0, [fp, #-8]
    // 0xad65bc: r0 = NCalendarHeader()
    //     0xad65bc: bl              #0xad6610  ; AllocateNCalendarHeaderStub -> NCalendarHeader (size=0x1c)
    // 0xad65c0: mov             x3, x0
    // 0xad65c4: ldur            x0, [fp, #-8]
    // 0xad65c8: stur            x3, [fp, #-0x28]
    // 0xad65cc: StoreField: r3->field_13 = r0
    //     0xad65cc: stur            w0, [x3, #0x13]
    // 0xad65d0: ldur            x2, [fp, #-0x20]
    // 0xad65d4: r1 = Function 'nextCalendarPage':.
    //     0xad65d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] AnonymousClosure: (0xad661c), in [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::nextCalendarPage (0xad6654)
    //     0xad65d8: ldr             x1, [x1, #0xb18]
    // 0xad65dc: r0 = AllocateClosure()
    //     0xad65dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad65e0: mov             x1, x0
    // 0xad65e4: ldur            x0, [fp, #-0x28]
    // 0xad65e8: ArrayStore: r0[0] = r1  ; List_4
    //     0xad65e8: stur            w1, [x0, #0x17]
    // 0xad65ec: ldur            x1, [fp, #-0x10]
    // 0xad65f0: StoreField: r0->field_b = r1
    //     0xad65f0: stur            w1, [x0, #0xb]
    // 0xad65f4: ldur            x1, [fp, #-0x18]
    // 0xad65f8: StoreField: r0->field_f = r1
    //     0xad65f8: stur            w1, [x0, #0xf]
    // 0xad65fc: LeaveFrame
    //     0xad65fc: mov             SP, fp
    //     0xad6600: ldp             fp, lr, [SP], #0x10
    // 0xad6604: ret
    //     0xad6604: ret             
    // 0xad6608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad660c: b               #0xad653c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xad693c, size: 0xac
    // 0xad693c: EnterFrame
    //     0xad693c: stp             fp, lr, [SP, #-0x10]!
    //     0xad6940: mov             fp, SP
    // 0xad6944: AllocStack(0x28)
    //     0xad6944: sub             SP, SP, #0x28
    // 0xad6948: SetupParameters(CalendarView this /* r1 */)
    //     0xad6948: stur            NULL, [fp, #-8]
    //     0xad694c: movz            x0, #0
    //     0xad6950: add             x1, fp, w0, sxtw #2
    //     0xad6954: ldr             x1, [x1, #0x10]
    //     0xad6958: ldur            w2, [x1, #0x17]
    //     0xad695c: add             x2, x2, HEAP, lsl #32
    //     0xad6960: stur            x2, [fp, #-0x10]
    // 0xad6964: CheckStackOverflow
    //     0xad6964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6968: cmp             SP, x16
    //     0xad696c: b.ls            #0xad69e0
    // 0xad6970: InitAsync() -> Future<void?>
    //     0xad6970: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad6974: bl              #0x661298  ; InitAsyncStub
    // 0xad6978: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad6978: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad697c: ldr             x0, [x0, #0x2670]
    //     0xad6980: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad6984: cmp             w0, w16
    //     0xad6988: b.ne            #0xad6994
    //     0xad698c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad6990: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad6994: r16 = "/setting/calendar"
    //     0xad6994: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cd08] "/setting/calendar"
    //     0xad6998: ldr             x16, [x16, #0xd08]
    // 0xad699c: stp             x16, NULL, [SP]
    // 0xad69a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad69a0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad69a4: r0 = GetNavigation.toNamed()
    //     0xad69a4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad69a8: mov             x1, x0
    // 0xad69ac: stur            x1, [fp, #-0x18]
    // 0xad69b0: r0 = Await()
    //     0xad69b0: bl              #0x661044  ; AwaitStub
    // 0xad69b4: ldur            x0, [fp, #-0x10]
    // 0xad69b8: LoadField: r1 = r0->field_f
    //     0xad69b8: ldur            w1, [x0, #0xf]
    // 0xad69bc: DecompressPointer r1
    //     0xad69bc: add             x1, x1, HEAP, lsl #32
    // 0xad69c0: r0 = controller()
    //     0xad69c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad69c4: mov             x1, x0
    // 0xad69c8: r0 = loadEvent()
    //     0xad69c8: bl              #0x8ed330  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::loadEvent
    // 0xad69cc: mov             x1, x0
    // 0xad69d0: stur            x1, [fp, #-0x18]
    // 0xad69d4: r0 = Await()
    //     0xad69d4: bl              #0x661044  ; AwaitStub
    // 0xad69d8: r0 = Null
    //     0xad69d8: mov             x0, NULL
    // 0xad69dc: r0 = ReturnAsyncNotFuture()
    //     0xad69dc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad69e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad69e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad69e4: b               #0xad6970
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad69e8, size: 0x134
    // 0xad69e8: EnterFrame
    //     0xad69e8: stp             fp, lr, [SP, #-0x10]!
    //     0xad69ec: mov             fp, SP
    // 0xad69f0: AllocStack(0x20)
    //     0xad69f0: sub             SP, SP, #0x20
    // 0xad69f4: CheckStackOverflow
    //     0xad69f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad69f8: cmp             SP, x16
    //     0xad69fc: b.ls            #0xad6b14
    // 0xad6a00: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad6a00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad6a04: ldr             x0, [x0, #0x2670]
    //     0xad6a08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad6a0c: cmp             w0, w16
    //     0xad6a10: b.ne            #0xad6a1c
    //     0xad6a14: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad6a18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad6a1c: r0 = GetNavigation.textTheme()
    //     0xad6a1c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xad6a20: LoadField: r1 = r0->field_27
    //     0xad6a20: ldur            w1, [x0, #0x27]
    // 0xad6a24: DecompressPointer r1
    //     0xad6a24: add             x1, x1, HEAP, lsl #32
    // 0xad6a28: cmp             w1, NULL
    // 0xad6a2c: b.ne            #0xad6a38
    // 0xad6a30: r0 = Null
    //     0xad6a30: mov             x0, NULL
    // 0xad6a34: b               #0xad6a50
    // 0xad6a38: r16 = 14.000000
    //     0xad6a38: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xad6a3c: ldr             x16, [x16, #0x9a0]
    // 0xad6a40: str             x16, [SP]
    // 0xad6a44: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xad6a44: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xad6a48: ldr             x4, [x4, #0x88]
    // 0xad6a4c: r0 = copyWith()
    //     0xad6a4c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad6a50: stur            x0, [fp, #-8]
    // 0xad6a54: r0 = Text()
    //     0xad6a54: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad6a58: mov             x3, x0
    // 0xad6a5c: r0 = "Kalender Hijriah ini hanya membantu. Penentuan awal bulan Hijriah menunggu hasil rukyat hilal."
    //     0xad6a5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb50] "Kalender Hijriah ini hanya membantu. Penentuan awal bulan Hijriah menunggu hasil rukyat hilal."
    //     0xad6a60: ldr             x0, [x0, #0xb50]
    // 0xad6a64: stur            x3, [fp, #-0x10]
    // 0xad6a68: StoreField: r3->field_b = r0
    //     0xad6a68: stur            w0, [x3, #0xb]
    // 0xad6a6c: ldur            x0, [fp, #-8]
    // 0xad6a70: StoreField: r3->field_13 = r0
    //     0xad6a70: stur            w0, [x3, #0x13]
    // 0xad6a74: r1 = Null
    //     0xad6a74: mov             x1, NULL
    // 0xad6a78: r2 = 2
    //     0xad6a78: movz            x2, #0x2
    // 0xad6a7c: r0 = AllocateArray()
    //     0xad6a7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad6a80: mov             x2, x0
    // 0xad6a84: ldur            x0, [fp, #-0x10]
    // 0xad6a88: stur            x2, [fp, #-8]
    // 0xad6a8c: StoreField: r2->field_f = r0
    //     0xad6a8c: stur            w0, [x2, #0xf]
    // 0xad6a90: r1 = <Widget>
    //     0xad6a90: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad6a94: r0 = AllocateGrowableArray()
    //     0xad6a94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad6a98: mov             x1, x0
    // 0xad6a9c: ldur            x0, [fp, #-8]
    // 0xad6aa0: stur            x1, [fp, #-0x10]
    // 0xad6aa4: StoreField: r1->field_f = r0
    //     0xad6aa4: stur            w0, [x1, #0xf]
    // 0xad6aa8: r0 = 2
    //     0xad6aa8: movz            x0, #0x2
    // 0xad6aac: StoreField: r1->field_b = r0
    //     0xad6aac: stur            w0, [x1, #0xb]
    // 0xad6ab0: r0 = NDialog()
    //     0xad6ab0: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xad6ab4: mov             x3, x0
    // 0xad6ab8: r0 = "Pengingat"
    //     0xad6ab8: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2a480] "Pengingat"
    //     0xad6abc: ldr             x0, [x0, #0x480]
    // 0xad6ac0: stur            x3, [fp, #-8]
    // 0xad6ac4: StoreField: r3->field_b = r0
    //     0xad6ac4: stur            w0, [x3, #0xb]
    // 0xad6ac8: r1 = Function '<anonymous closure>':.
    //     0xad6ac8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb58] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xad6acc: ldr             x1, [x1, #0xb58]
    // 0xad6ad0: r2 = Null
    //     0xad6ad0: mov             x2, NULL
    // 0xad6ad4: r0 = AllocateClosure()
    //     0xad6ad4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad6ad8: mov             x1, x0
    // 0xad6adc: ldur            x0, [fp, #-8]
    // 0xad6ae0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad6ae0: stur            w1, [x0, #0x17]
    // 0xad6ae4: ldur            x1, [fp, #-0x10]
    // 0xad6ae8: StoreField: r0->field_f = r1
    //     0xad6ae8: stur            w1, [x0, #0xf]
    // 0xad6aec: r1 = "Mengerti"
    //     0xad6aec: add             x1, PP, #0x28, lsl #12  ; [pp+0x280e0] "Mengerti"
    //     0xad6af0: ldr             x1, [x1, #0xe0]
    // 0xad6af4: StoreField: r0->field_13 = r1
    //     0xad6af4: stur            w1, [x0, #0x13]
    // 0xad6af8: stp             x0, NULL, [SP]
    // 0xad6afc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad6afc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad6b00: r0 = ExtensionDialog.dialog()
    //     0xad6b00: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xad6b04: r0 = Null
    //     0xad6b04: mov             x0, NULL
    // 0xad6b08: LeaveFrame
    //     0xad6b08: mov             SP, fp
    //     0xad6b0c: ldp             fp, lr, [SP], #0x10
    // 0xad6b10: ret
    //     0xad6b10: ret             
    // 0xad6b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6b18: b               #0xad6a00
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xad6b1c, size: 0x1fc
    // 0xad6b1c: EnterFrame
    //     0xad6b1c: stp             fp, lr, [SP, #-0x10]!
    //     0xad6b20: mov             fp, SP
    // 0xad6b24: AllocStack(0x28)
    //     0xad6b24: sub             SP, SP, #0x28
    // 0xad6b28: SetupParameters()
    //     0xad6b28: ldr             x0, [fp, #0x10]
    //     0xad6b2c: ldur            w2, [x0, #0x17]
    //     0xad6b30: add             x2, x2, HEAP, lsl #32
    //     0xad6b34: stur            x2, [fp, #-8]
    // 0xad6b38: CheckStackOverflow
    //     0xad6b38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6b3c: cmp             SP, x16
    //     0xad6b40: b.ls            #0xad6d10
    // 0xad6b44: LoadField: r1 = r2->field_f
    //     0xad6b44: ldur            w1, [x2, #0xf]
    // 0xad6b48: DecompressPointer r1
    //     0xad6b48: add             x1, x1, HEAP, lsl #32
    // 0xad6b4c: r0 = controller()
    //     0xad6b4c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6b50: mov             x1, x0
    // 0xad6b54: r0 = appBarTitle()
    //     0xad6b54: bl              #0xad6d74  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::appBarTitle
    // 0xad6b58: stur            x0, [fp, #-0x10]
    // 0xad6b5c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad6b5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad6b60: ldr             x0, [x0, #0x2670]
    //     0xad6b64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad6b68: cmp             w0, w16
    //     0xad6b6c: b.ne            #0xad6b78
    //     0xad6b70: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad6b74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad6b78: r0 = GetNavigation.theme()
    //     0xad6b78: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xad6b7c: LoadField: r1 = r0->field_9b
    //     0xad6b7c: ldur            w1, [x0, #0x9b]
    // 0xad6b80: DecompressPointer r1
    //     0xad6b80: add             x1, x1, HEAP, lsl #32
    // 0xad6b84: LoadField: r0 = r1->field_3b
    //     0xad6b84: ldur            w0, [x1, #0x3b]
    // 0xad6b88: DecompressPointer r0
    //     0xad6b88: add             x0, x0, HEAP, lsl #32
    // 0xad6b8c: cmp             w0, NULL
    // 0xad6b90: b.ne            #0xad6b9c
    // 0xad6b94: r2 = Null
    //     0xad6b94: mov             x2, NULL
    // 0xad6b98: b               #0xad6bbc
    // 0xad6b9c: r16 = 12.000000
    //     0xad6b9c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xad6ba0: ldr             x16, [x16, #0xc60]
    // 0xad6ba4: str             x16, [SP]
    // 0xad6ba8: mov             x1, x0
    // 0xad6bac: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xad6bac: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xad6bb0: ldr             x4, [x4, #0x88]
    // 0xad6bb4: r0 = copyWith()
    //     0xad6bb4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad6bb8: mov             x2, x0
    // 0xad6bbc: ldur            x1, [fp, #-8]
    // 0xad6bc0: ldur            x0, [fp, #-0x10]
    // 0xad6bc4: stur            x2, [fp, #-0x18]
    // 0xad6bc8: r0 = Text()
    //     0xad6bc8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad6bcc: mov             x2, x0
    // 0xad6bd0: ldur            x0, [fp, #-0x10]
    // 0xad6bd4: stur            x2, [fp, #-0x20]
    // 0xad6bd8: StoreField: r2->field_b = r0
    //     0xad6bd8: stur            w0, [x2, #0xb]
    // 0xad6bdc: ldur            x0, [fp, #-0x18]
    // 0xad6be0: StoreField: r2->field_13 = r0
    //     0xad6be0: stur            w0, [x2, #0x13]
    // 0xad6be4: ldur            x0, [fp, #-8]
    // 0xad6be8: LoadField: r1 = r0->field_f
    //     0xad6be8: ldur            w1, [x0, #0xf]
    // 0xad6bec: DecompressPointer r1
    //     0xad6bec: add             x1, x1, HEAP, lsl #32
    // 0xad6bf0: r0 = controller()
    //     0xad6bf0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad6bf4: mov             x1, x0
    // 0xad6bf8: r0 = appBarSubtitle()
    //     0xad6bf8: bl              #0xad6d18  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::appBarSubtitle
    // 0xad6bfc: stur            x0, [fp, #-8]
    // 0xad6c00: r0 = GetNavigation.theme()
    //     0xad6c00: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xad6c04: LoadField: r1 = r0->field_9b
    //     0xad6c04: ldur            w1, [x0, #0x9b]
    // 0xad6c08: DecompressPointer r1
    //     0xad6c08: add             x1, x1, HEAP, lsl #32
    // 0xad6c0c: LoadField: r0 = r1->field_3b
    //     0xad6c0c: ldur            w0, [x1, #0x3b]
    // 0xad6c10: DecompressPointer r0
    //     0xad6c10: add             x0, x0, HEAP, lsl #32
    // 0xad6c14: cmp             w0, NULL
    // 0xad6c18: b.ne            #0xad6c24
    // 0xad6c1c: r2 = Null
    //     0xad6c1c: mov             x2, NULL
    // 0xad6c20: b               #0xad6c44
    // 0xad6c24: r16 = 12.000000
    //     0xad6c24: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xad6c28: ldr             x16, [x16, #0xc60]
    // 0xad6c2c: str             x16, [SP]
    // 0xad6c30: mov             x1, x0
    // 0xad6c34: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xad6c34: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xad6c38: ldr             x4, [x4, #0x88]
    // 0xad6c3c: r0 = copyWith()
    //     0xad6c3c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad6c40: mov             x2, x0
    // 0xad6c44: ldur            x1, [fp, #-0x20]
    // 0xad6c48: ldur            x0, [fp, #-8]
    // 0xad6c4c: stur            x2, [fp, #-0x10]
    // 0xad6c50: r0 = Text()
    //     0xad6c50: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad6c54: mov             x3, x0
    // 0xad6c58: ldur            x0, [fp, #-8]
    // 0xad6c5c: stur            x3, [fp, #-0x18]
    // 0xad6c60: StoreField: r3->field_b = r0
    //     0xad6c60: stur            w0, [x3, #0xb]
    // 0xad6c64: ldur            x0, [fp, #-0x10]
    // 0xad6c68: StoreField: r3->field_13 = r0
    //     0xad6c68: stur            w0, [x3, #0x13]
    // 0xad6c6c: r1 = Null
    //     0xad6c6c: mov             x1, NULL
    // 0xad6c70: r2 = 4
    //     0xad6c70: movz            x2, #0x4
    // 0xad6c74: r0 = AllocateArray()
    //     0xad6c74: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad6c78: mov             x2, x0
    // 0xad6c7c: ldur            x0, [fp, #-0x20]
    // 0xad6c80: stur            x2, [fp, #-8]
    // 0xad6c84: StoreField: r2->field_f = r0
    //     0xad6c84: stur            w0, [x2, #0xf]
    // 0xad6c88: ldur            x0, [fp, #-0x18]
    // 0xad6c8c: StoreField: r2->field_13 = r0
    //     0xad6c8c: stur            w0, [x2, #0x13]
    // 0xad6c90: r1 = <Widget>
    //     0xad6c90: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad6c94: r0 = AllocateGrowableArray()
    //     0xad6c94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad6c98: mov             x1, x0
    // 0xad6c9c: ldur            x0, [fp, #-8]
    // 0xad6ca0: stur            x1, [fp, #-0x10]
    // 0xad6ca4: StoreField: r1->field_f = r0
    //     0xad6ca4: stur            w0, [x1, #0xf]
    // 0xad6ca8: r0 = 4
    //     0xad6ca8: movz            x0, #0x4
    // 0xad6cac: StoreField: r1->field_b = r0
    //     0xad6cac: stur            w0, [x1, #0xb]
    // 0xad6cb0: r0 = Column()
    //     0xad6cb0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad6cb4: r1 = Instance_Axis
    //     0xad6cb4: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad6cb8: StoreField: r0->field_f = r1
    //     0xad6cb8: stur            w1, [x0, #0xf]
    // 0xad6cbc: r1 = Instance_MainAxisAlignment
    //     0xad6cbc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad6cc0: ldr             x1, [x1, #0x730]
    // 0xad6cc4: StoreField: r0->field_13 = r1
    //     0xad6cc4: stur            w1, [x0, #0x13]
    // 0xad6cc8: r1 = Instance_MainAxisSize
    //     0xad6cc8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xad6ccc: ldr             x1, [x1, #0xe88]
    // 0xad6cd0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad6cd0: stur            w1, [x0, #0x17]
    // 0xad6cd4: r1 = Instance_CrossAxisAlignment
    //     0xad6cd4: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xad6cd8: ldr             x1, [x1, #0x68]
    // 0xad6cdc: StoreField: r0->field_1b = r1
    //     0xad6cdc: stur            w1, [x0, #0x1b]
    // 0xad6ce0: r1 = Instance_VerticalDirection
    //     0xad6ce0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad6ce4: ldr             x1, [x1, #0x748]
    // 0xad6ce8: StoreField: r0->field_23 = r1
    //     0xad6ce8: stur            w1, [x0, #0x23]
    // 0xad6cec: r1 = Instance_Clip
    //     0xad6cec: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad6cf0: ldr             x1, [x1, #0x750]
    // 0xad6cf4: StoreField: r0->field_2b = r1
    //     0xad6cf4: stur            w1, [x0, #0x2b]
    // 0xad6cf8: StoreField: r0->field_2f = rZR
    //     0xad6cf8: stur            xzr, [x0, #0x2f]
    // 0xad6cfc: ldur            x1, [fp, #-0x10]
    // 0xad6d00: StoreField: r0->field_b = r1
    //     0xad6d00: stur            w1, [x0, #0xb]
    // 0xad6d04: LeaveFrame
    //     0xad6d04: mov             SP, fp
    //     0xad6d08: ldp             fp, lr, [SP], #0x10
    // 0xad6d0c: ret
    //     0xad6d0c: ret             
    // 0xad6d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6d10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6d14: b               #0xad6b44
  }
}
