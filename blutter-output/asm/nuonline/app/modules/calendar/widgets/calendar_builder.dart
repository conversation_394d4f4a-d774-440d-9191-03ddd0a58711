// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_builder.dart

// class id: 1050159, size: 0x8
class :: {
}

// class id: 5304, size: 0x1c, field offset: 0x14
//   const constructor, 
class CalendarBuilder extends GetView<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0xa8aa60, size: 0x8
    // 0xa8aa60: r0 = "hijri_service"
    //     0xa8aa60: ldr             x0, [PP, #0x118]  ; [pp+0x118] "hijri_service"
    // 0xa8aa64: ret
    //     0xa8aa64: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xad71f4, size: 0x58
    // 0xad71f4: EnterFrame
    //     0xad71f4: stp             fp, lr, [SP, #-0x10]!
    //     0xad71f8: mov             fp, SP
    // 0xad71fc: AllocStack(0x10)
    //     0xad71fc: sub             SP, SP, #0x10
    // 0xad7200: SetupParameters(CalendarBuilder this /* r1 => r1, fp-0x8 */)
    //     0xad7200: stur            x1, [fp, #-8]
    // 0xad7204: r1 = 1
    //     0xad7204: movz            x1, #0x1
    // 0xad7208: r0 = AllocateContext()
    //     0xad7208: bl              #0xec126c  ; AllocateContextStub
    // 0xad720c: mov             x1, x0
    // 0xad7210: ldur            x0, [fp, #-8]
    // 0xad7214: stur            x1, [fp, #-0x10]
    // 0xad7218: StoreField: r1->field_f = r0
    //     0xad7218: stur            w0, [x1, #0xf]
    // 0xad721c: r0 = Obx()
    //     0xad721c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad7220: ldur            x2, [fp, #-0x10]
    // 0xad7224: r1 = Function '<anonymous closure>':.
    //     0xad7224: add             x1, PP, #0x40, lsl #12  ; [pp+0x406e8] AnonymousClosure: (0xad724c), in [package:nuonline/app/modules/calendar/widgets/calendar_builder.dart] CalendarBuilder::build (0xad71f4)
    //     0xad7228: ldr             x1, [x1, #0x6e8]
    // 0xad722c: stur            x0, [fp, #-8]
    // 0xad7230: r0 = AllocateClosure()
    //     0xad7230: bl              #0xec1630  ; AllocateClosureStub
    // 0xad7234: mov             x1, x0
    // 0xad7238: ldur            x0, [fp, #-8]
    // 0xad723c: StoreField: r0->field_b = r1
    //     0xad723c: stur            w1, [x0, #0xb]
    // 0xad7240: LeaveFrame
    //     0xad7240: mov             SP, fp
    //     0xad7244: ldp             fp, lr, [SP], #0x10
    // 0xad7248: ret
    //     0xad7248: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xad724c, size: 0x108
    // 0xad724c: EnterFrame
    //     0xad724c: stp             fp, lr, [SP, #-0x10]!
    //     0xad7250: mov             fp, SP
    // 0xad7254: AllocStack(0x38)
    //     0xad7254: sub             SP, SP, #0x38
    // 0xad7258: SetupParameters()
    //     0xad7258: ldr             x0, [fp, #0x10]
    //     0xad725c: ldur            w2, [x0, #0x17]
    //     0xad7260: add             x2, x2, HEAP, lsl #32
    //     0xad7264: stur            x2, [fp, #-8]
    // 0xad7268: CheckStackOverflow
    //     0xad7268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad726c: cmp             SP, x16
    //     0xad7270: b.ls            #0xad734c
    // 0xad7274: LoadField: r1 = r2->field_f
    //     0xad7274: ldur            w1, [x2, #0xf]
    // 0xad7278: DecompressPointer r1
    //     0xad7278: add             x1, x1, HEAP, lsl #32
    // 0xad727c: r0 = _date()
    //     0xad727c: bl              #0xad7354  ; [package:nuonline/app/modules/calendar/widgets/calendar_builder.dart] CalendarBuilder::_date
    // 0xad7280: mov             x1, x0
    // 0xad7284: r0 = DateTimeExtensions.gregorian()
    //     0xad7284: bl              #0x81f220  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.gregorian
    // 0xad7288: mov             x2, x0
    // 0xad728c: ldur            x0, [fp, #-8]
    // 0xad7290: stur            x2, [fp, #-0x10]
    // 0xad7294: LoadField: r1 = r0->field_f
    //     0xad7294: ldur            w1, [x0, #0xf]
    // 0xad7298: DecompressPointer r1
    //     0xad7298: add             x1, x1, HEAP, lsl #32
    // 0xad729c: r0 = controller()
    //     0xad729c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad72a0: mov             x1, x0
    // 0xad72a4: ldur            x0, [fp, #-8]
    // 0xad72a8: stur            x1, [fp, #-0x18]
    // 0xad72ac: LoadField: r2 = r0->field_f
    //     0xad72ac: ldur            w2, [x0, #0xf]
    // 0xad72b0: DecompressPointer r2
    //     0xad72b0: add             x2, x2, HEAP, lsl #32
    // 0xad72b4: LoadField: r3 = r2->field_13
    //     0xad72b4: ldur            w3, [x2, #0x13]
    // 0xad72b8: DecompressPointer r3
    //     0xad72b8: add             x3, x3, HEAP, lsl #32
    // 0xad72bc: cmp             w3, NULL
    // 0xad72c0: b.ne            #0xad72f8
    // 0xad72c4: r0 = DateTime()
    //     0xad72c4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xad72c8: mov             x1, x0
    // 0xad72cc: r0 = false
    //     0xad72cc: add             x0, NULL, #0x30  ; false
    // 0xad72d0: stur            x1, [fp, #-0x20]
    // 0xad72d4: StoreField: r1->field_13 = r0
    //     0xad72d4: stur            w0, [x1, #0x13]
    // 0xad72d8: r0 = _getCurrentMicros()
    //     0xad72d8: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xad72dc: r1 = LoadInt32Instr(r0)
    //     0xad72dc: sbfx            x1, x0, #1, #0x1f
    //     0xad72e0: tbz             w0, #0, #0xad72e8
    //     0xad72e4: ldur            x1, [x0, #7]
    // 0xad72e8: ldur            x0, [fp, #-0x20]
    // 0xad72ec: StoreField: r0->field_7 = r1
    //     0xad72ec: stur            x1, [x0, #7]
    // 0xad72f0: mov             x2, x0
    // 0xad72f4: b               #0xad72fc
    // 0xad72f8: mov             x2, x3
    // 0xad72fc: ldur            x0, [fp, #-8]
    // 0xad7300: ldur            x1, [fp, #-0x18]
    // 0xad7304: r0 = from()
    //     0xad7304: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0xad7308: mov             x1, x0
    // 0xad730c: r0 = dMY()
    //     0xad730c: bl              #0x81f29c  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::dMY
    // 0xad7310: mov             x1, x0
    // 0xad7314: ldur            x0, [fp, #-8]
    // 0xad7318: LoadField: r2 = r0->field_f
    //     0xad7318: ldur            w2, [x0, #0xf]
    // 0xad731c: DecompressPointer r2
    //     0xad731c: add             x2, x2, HEAP, lsl #32
    // 0xad7320: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xad7320: ldur            w0, [x2, #0x17]
    // 0xad7324: DecompressPointer r0
    //     0xad7324: add             x0, x0, HEAP, lsl #32
    // 0xad7328: ldur            x16, [fp, #-0x10]
    // 0xad732c: stp             x16, x0, [SP, #8]
    // 0xad7330: str             x1, [SP]
    // 0xad7334: ClosureCall
    //     0xad7334: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xad7338: ldur            x2, [x0, #0x1f]
    //     0xad733c: blr             x2
    // 0xad7340: LeaveFrame
    //     0xad7340: mov             SP, fp
    //     0xad7344: ldp             fp, lr, [SP], #0x10
    // 0xad7348: ret
    //     0xad7348: ret             
    // 0xad734c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad734c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7350: b               #0xad7274
  }
  get _ _date(/* No info */) {
    // ** addr: 0xad7354, size: 0x6c
    // 0xad7354: EnterFrame
    //     0xad7354: stp             fp, lr, [SP, #-0x10]!
    //     0xad7358: mov             fp, SP
    // 0xad735c: AllocStack(0x8)
    //     0xad735c: sub             SP, SP, #8
    // 0xad7360: CheckStackOverflow
    //     0xad7360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad7364: cmp             SP, x16
    //     0xad7368: b.ls            #0xad73b8
    // 0xad736c: LoadField: r0 = r1->field_13
    //     0xad736c: ldur            w0, [x1, #0x13]
    // 0xad7370: DecompressPointer r0
    //     0xad7370: add             x0, x0, HEAP, lsl #32
    // 0xad7374: cmp             w0, NULL
    // 0xad7378: b.ne            #0xad73ac
    // 0xad737c: r0 = DateTime()
    //     0xad737c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xad7380: mov             x1, x0
    // 0xad7384: r0 = false
    //     0xad7384: add             x0, NULL, #0x30  ; false
    // 0xad7388: stur            x1, [fp, #-8]
    // 0xad738c: StoreField: r1->field_13 = r0
    //     0xad738c: stur            w0, [x1, #0x13]
    // 0xad7390: r0 = _getCurrentMicros()
    //     0xad7390: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xad7394: r1 = LoadInt32Instr(r0)
    //     0xad7394: sbfx            x1, x0, #1, #0x1f
    //     0xad7398: tbz             w0, #0, #0xad73a0
    //     0xad739c: ldur            x1, [x0, #7]
    // 0xad73a0: ldur            x2, [fp, #-8]
    // 0xad73a4: StoreField: r2->field_7 = r1
    //     0xad73a4: stur            x1, [x2, #7]
    // 0xad73a8: mov             x0, x2
    // 0xad73ac: LeaveFrame
    //     0xad73ac: mov             SP, fp
    //     0xad73b0: ldp             fp, lr, [SP], #0x10
    // 0xad73b4: ret
    //     0xad73b4: ret             
    // 0xad73b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad73b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad73bc: b               #0xad736c
  }
}
