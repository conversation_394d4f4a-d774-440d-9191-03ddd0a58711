// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_cell.dart

// class id: 1050160, size: 0x8
class :: {
}

// class id: 5060, size: 0x18, field offset: 0xc
//   const constructor, 
class NCalendarCell extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8646c, size: 0x250
    // 0xb8646c: EnterFrame
    //     0xb8646c: stp             fp, lr, [SP, #-0x10]!
    //     0xb86470: mov             fp, SP
    // 0xb86474: AllocStack(0x38)
    //     0xb86474: sub             SP, SP, #0x38
    // 0xb86478: SetupParameters(NCalendarCell this /* r1 => r0, fp-0x8 */)
    //     0xb86478: mov             x0, x1
    //     0xb8647c: stur            x1, [fp, #-8]
    // 0xb86480: CheckStackOverflow
    //     0xb86480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86484: cmp             SP, x16
    //     0xb86488: b.ls            #0xb86680
    // 0xb8648c: r1 = _ConstMap len:6
    //     0xb8648c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb86490: ldr             x1, [x1, #0xc20]
    // 0xb86494: r2 = 8
    //     0xb86494: movz            x2, #0x8
    // 0xb86498: r0 = []()
    //     0xb86498: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8649c: cmp             w0, NULL
    // 0xb864a0: b.eq            #0xb86688
    // 0xb864a4: r16 = <Color>
    //     0xb864a4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb864a8: ldr             x16, [x16, #0x158]
    // 0xb864ac: stp             x0, x16, [SP, #8]
    // 0xb864b0: r16 = Instance_MaterialColor
    //     0xb864b0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb864b4: ldr             x16, [x16, #0xe38]
    // 0xb864b8: str             x16, [SP]
    // 0xb864bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb864bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb864c0: r0 = mode()
    //     0xb864c0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb864c4: r1 = _ConstMap len:10
    //     0xb864c4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb864c8: ldr             x1, [x1, #0xc08]
    // 0xb864cc: r2 = 600
    //     0xb864cc: movz            x2, #0x258
    // 0xb864d0: stur            x0, [fp, #-0x10]
    // 0xb864d4: r0 = []()
    //     0xb864d4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb864d8: cmp             w0, NULL
    // 0xb864dc: b.eq            #0xb8668c
    // 0xb864e0: r16 = <Color>
    //     0xb864e0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb864e4: ldr             x16, [x16, #0x158]
    // 0xb864e8: stp             x0, x16, [SP, #8]
    // 0xb864ec: r16 = Instance_MaterialColor
    //     0xb864ec: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb864f0: ldr             x16, [x16, #0xcf0]
    // 0xb864f4: str             x16, [SP]
    // 0xb864f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb864f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb864fc: r0 = mode()
    //     0xb864fc: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb86500: r1 = _ConstMap len:10
    //     0xb86500: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb86504: ldr             x1, [x1, #0xc08]
    // 0xb86508: r2 = 100
    //     0xb86508: movz            x2, #0x64
    // 0xb8650c: stur            x0, [fp, #-0x18]
    // 0xb86510: r0 = []()
    //     0xb86510: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb86514: stur            x0, [fp, #-0x20]
    // 0xb86518: cmp             w0, NULL
    // 0xb8651c: b.eq            #0xb86690
    // 0xb86520: r1 = _ConstMap len:10
    //     0xb86520: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb86524: ldr             x1, [x1, #0xc08]
    // 0xb86528: r2 = 1800
    //     0xb86528: movz            x2, #0x708
    // 0xb8652c: r0 = []()
    //     0xb8652c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb86530: cmp             w0, NULL
    // 0xb86534: b.eq            #0xb86694
    // 0xb86538: r16 = <Color>
    //     0xb86538: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8653c: ldr             x16, [x16, #0x158]
    // 0xb86540: stp             x0, x16, [SP, #8]
    // 0xb86544: ldur            x16, [fp, #-0x20]
    // 0xb86548: str             x16, [SP]
    // 0xb8654c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8654c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb86550: r0 = mode()
    //     0xb86550: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb86554: mov             x1, x0
    // 0xb86558: ldur            x0, [fp, #-8]
    // 0xb8655c: LoadField: r2 = r0->field_b
    //     0xb8655c: ldur            w2, [x0, #0xb]
    // 0xb86560: DecompressPointer r2
    //     0xb86560: add             x2, x2, HEAP, lsl #32
    // 0xb86564: tbnz            w2, #4, #0xb86570
    // 0xb86568: mov             x2, x1
    // 0xb8656c: b               #0xb86574
    // 0xb86570: r2 = Null
    //     0xb86570: mov             x2, NULL
    // 0xb86574: stur            x2, [fp, #-0x20]
    // 0xb86578: LoadField: r1 = r0->field_f
    //     0xb86578: ldur            w1, [x0, #0xf]
    // 0xb8657c: DecompressPointer r1
    //     0xb8657c: add             x1, x1, HEAP, lsl #32
    // 0xb86580: tbnz            w1, #4, #0xb8658c
    // 0xb86584: ldur            x3, [fp, #-0x18]
    // 0xb86588: b               #0xb86590
    // 0xb8658c: ldur            x3, [fp, #-0x10]
    // 0xb86590: tbnz            w1, #4, #0xb8659c
    // 0xb86594: d0 = 2.000000
    //     0xb86594: fmov            d0, #2.00000000
    // 0xb86598: b               #0xb865a0
    // 0xb8659c: d0 = 1.000000
    //     0xb8659c: fmov            d0, #1.00000000
    // 0xb865a0: r1 = inline_Allocate_Double()
    //     0xb865a0: ldp             x1, x4, [THR, #0x50]  ; THR::top
    //     0xb865a4: add             x1, x1, #0x10
    //     0xb865a8: cmp             x4, x1
    //     0xb865ac: b.ls            #0xb86698
    //     0xb865b0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb865b4: sub             x1, x1, #0xf
    //     0xb865b8: movz            x4, #0xe15c
    //     0xb865bc: movk            x4, #0x3, lsl #16
    //     0xb865c0: stur            x4, [x1, #-1]
    // 0xb865c4: StoreField: r1->field_7 = d0
    //     0xb865c4: stur            d0, [x1, #7]
    // 0xb865c8: stp             x1, x3, [SP]
    // 0xb865cc: r1 = Null
    //     0xb865cc: mov             x1, NULL
    // 0xb865d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, width, 0x2, null]
    //     0xb865d0: add             x4, PP, #0x33, lsl #12  ; [pp+0x333a8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "width", 0x2, Null]
    //     0xb865d4: ldr             x4, [x4, #0x3a8]
    // 0xb865d8: r0 = Border.all()
    //     0xb865d8: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb865dc: stur            x0, [fp, #-0x10]
    // 0xb865e0: r0 = BoxDecoration()
    //     0xb865e0: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb865e4: mov             x1, x0
    // 0xb865e8: ldur            x0, [fp, #-0x20]
    // 0xb865ec: stur            x1, [fp, #-0x18]
    // 0xb865f0: StoreField: r1->field_7 = r0
    //     0xb865f0: stur            w0, [x1, #7]
    // 0xb865f4: ldur            x0, [fp, #-0x10]
    // 0xb865f8: StoreField: r1->field_f = r0
    //     0xb865f8: stur            w0, [x1, #0xf]
    // 0xb865fc: r0 = Instance_BoxShape
    //     0xb865fc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb86600: ldr             x0, [x0, #0xca8]
    // 0xb86604: StoreField: r1->field_23 = r0
    //     0xb86604: stur            w0, [x1, #0x23]
    // 0xb86608: ldur            x2, [fp, #-8]
    // 0xb8660c: LoadField: r3 = r2->field_13
    //     0xb8660c: ldur            w3, [x2, #0x13]
    // 0xb86610: DecompressPointer r3
    //     0xb86610: add             x3, x3, HEAP, lsl #32
    // 0xb86614: stur            x3, [fp, #-0x10]
    // 0xb86618: r0 = DecoratedBox()
    //     0xb86618: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb8661c: mov             x1, x0
    // 0xb86620: ldur            x0, [fp, #-0x18]
    // 0xb86624: stur            x1, [fp, #-8]
    // 0xb86628: StoreField: r1->field_f = r0
    //     0xb86628: stur            w0, [x1, #0xf]
    // 0xb8662c: r0 = Instance_DecorationPosition
    //     0xb8662c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb86630: ldr             x0, [x0, #0xb28]
    // 0xb86634: StoreField: r1->field_13 = r0
    //     0xb86634: stur            w0, [x1, #0x13]
    // 0xb86638: ldur            x0, [fp, #-0x10]
    // 0xb8663c: StoreField: r1->field_b = r0
    //     0xb8663c: stur            w0, [x1, #0xb]
    // 0xb86640: r0 = InkWell()
    //     0xb86640: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb86644: ldur            x1, [fp, #-8]
    // 0xb86648: StoreField: r0->field_b = r1
    //     0xb86648: stur            w1, [x0, #0xb]
    // 0xb8664c: r1 = true
    //     0xb8664c: add             x1, NULL, #0x20  ; true
    // 0xb86650: StoreField: r0->field_43 = r1
    //     0xb86650: stur            w1, [x0, #0x43]
    // 0xb86654: r2 = Instance_BoxShape
    //     0xb86654: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb86658: ldr             x2, [x2, #0xca8]
    // 0xb8665c: StoreField: r0->field_47 = r2
    //     0xb8665c: stur            w2, [x0, #0x47]
    // 0xb86660: StoreField: r0->field_6f = r1
    //     0xb86660: stur            w1, [x0, #0x6f]
    // 0xb86664: r2 = false
    //     0xb86664: add             x2, NULL, #0x30  ; false
    // 0xb86668: StoreField: r0->field_73 = r2
    //     0xb86668: stur            w2, [x0, #0x73]
    // 0xb8666c: StoreField: r0->field_83 = r1
    //     0xb8666c: stur            w1, [x0, #0x83]
    // 0xb86670: StoreField: r0->field_7b = r2
    //     0xb86670: stur            w2, [x0, #0x7b]
    // 0xb86674: LeaveFrame
    //     0xb86674: mov             SP, fp
    //     0xb86678: ldp             fp, lr, [SP], #0x10
    // 0xb8667c: ret
    //     0xb8667c: ret             
    // 0xb86680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86684: b               #0xb8648c
    // 0xb86688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86688: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8668c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8668c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86690: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86694: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86694: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86698: SaveReg d0
    //     0xb86698: str             q0, [SP, #-0x10]!
    // 0xb8669c: stp             x2, x3, [SP, #-0x10]!
    // 0xb866a0: SaveReg r0
    //     0xb866a0: str             x0, [SP, #-8]!
    // 0xb866a4: r0 = AllocateDouble()
    //     0xb866a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb866a8: mov             x1, x0
    // 0xb866ac: RestoreReg r0
    //     0xb866ac: ldr             x0, [SP], #8
    // 0xb866b0: ldp             x2, x3, [SP], #0x10
    // 0xb866b4: RestoreReg d0
    //     0xb866b4: ldr             q0, [SP], #0x10
    // 0xb866b8: b               #0xb865c4
  }
}
