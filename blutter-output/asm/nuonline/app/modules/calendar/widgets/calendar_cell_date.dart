// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_cell_date.dart

// class id: 1050161, size: 0x8
class :: {

  static late Map<EventCategory, Color> eventCategoryMarkColor; // offset: 0x15c8

  static Map<EventCategory, Color> eventCategoryMarkColor() {
    // ** addr: 0xb8700c, size: 0x238
    // 0xb8700c: EnterFrame
    //     0xb8700c: stp             fp, lr, [SP, #-0x10]!
    //     0xb87010: mov             fp, SP
    // 0xb87014: AllocStack(0x28)
    //     0xb87014: sub             SP, SP, #0x28
    // 0xb87018: CheckStackOverflow
    //     0xb87018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8701c: cmp             SP, x16
    //     0xb87020: b.ls            #0xb87228
    // 0xb87024: r1 = Null
    //     0xb87024: mov             x1, NULL
    // 0xb87028: r2 = 16
    //     0xb87028: movz            x2, #0x10
    // 0xb8702c: r0 = AllocateArray()
    //     0xb8702c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb87030: stur            x0, [fp, #-8]
    // 0xb87034: r16 = Instance_EventCategory
    //     0xb87034: add             x16, PP, #9, lsl #12  ; [pp+0x92d0] Obj!EventCategory@e310a1
    //     0xb87038: ldr             x16, [x16, #0x2d0]
    // 0xb8703c: StoreField: r0->field_f = r16
    //     0xb8703c: stur            w16, [x0, #0xf]
    // 0xb87040: r1 = _ConstMap len:3
    //     0xb87040: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ee0] Map<int, Color>(3)
    //     0xb87044: ldr             x1, [x1, #0xee0]
    // 0xb87048: r2 = 4
    //     0xb87048: movz            x2, #0x4
    // 0xb8704c: r0 = []()
    //     0xb8704c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb87050: cmp             w0, NULL
    // 0xb87054: b.eq            #0xb87230
    // 0xb87058: r16 = <Color>
    //     0xb87058: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8705c: ldr             x16, [x16, #0x158]
    // 0xb87060: stp             x0, x16, [SP, #8]
    // 0xb87064: r16 = Instance_MaterialColor
    //     0xb87064: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ee8] Obj!MaterialColor@e2bc71
    //     0xb87068: ldr             x16, [x16, #0xee8]
    // 0xb8706c: str             x16, [SP]
    // 0xb87070: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87070: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87074: r0 = mode()
    //     0xb87074: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb87078: ldur            x1, [fp, #-8]
    // 0xb8707c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb8707c: add             x25, x1, #0x13
    //     0xb87080: str             w0, [x25]
    //     0xb87084: tbz             w0, #0, #0xb870a0
    //     0xb87088: ldurb           w16, [x1, #-1]
    //     0xb8708c: ldurb           w17, [x0, #-1]
    //     0xb87090: and             x16, x17, x16, lsr #2
    //     0xb87094: tst             x16, HEAP, lsr #32
    //     0xb87098: b.eq            #0xb870a0
    //     0xb8709c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb870a0: ldur            x0, [fp, #-8]
    // 0xb870a4: r16 = Instance_EventCategory
    //     0xb870a4: add             x16, PP, #9, lsl #12  ; [pp+0x9180] Obj!EventCategory@e31101
    //     0xb870a8: ldr             x16, [x16, #0x180]
    // 0xb870ac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb870ac: stur            w16, [x0, #0x17]
    // 0xb870b0: r1 = _ConstMap len:3
    //     0xb870b0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb870b4: ldr             x1, [x1, #0xcd0]
    // 0xb870b8: r2 = 4
    //     0xb870b8: movz            x2, #0x4
    // 0xb870bc: r0 = []()
    //     0xb870bc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb870c0: cmp             w0, NULL
    // 0xb870c4: b.eq            #0xb87234
    // 0xb870c8: r16 = <Color>
    //     0xb870c8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb870cc: ldr             x16, [x16, #0x158]
    // 0xb870d0: stp             x0, x16, [SP, #8]
    // 0xb870d4: r16 = Instance_MaterialColor
    //     0xb870d4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e40] Obj!MaterialColor@e2bbf1
    //     0xb870d8: ldr             x16, [x16, #0xe40]
    // 0xb870dc: str             x16, [SP]
    // 0xb870e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb870e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb870e4: r0 = mode()
    //     0xb870e4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb870e8: ldur            x1, [fp, #-8]
    // 0xb870ec: ArrayStore: r1[3] = r0  ; List_4
    //     0xb870ec: add             x25, x1, #0x1b
    //     0xb870f0: str             w0, [x25]
    //     0xb870f4: tbz             w0, #0, #0xb87110
    //     0xb870f8: ldurb           w16, [x1, #-1]
    //     0xb870fc: ldurb           w17, [x0, #-1]
    //     0xb87100: and             x16, x17, x16, lsr #2
    //     0xb87104: tst             x16, HEAP, lsr #32
    //     0xb87108: b.eq            #0xb87110
    //     0xb8710c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87110: ldur            x0, [fp, #-8]
    // 0xb87114: r16 = Instance_EventCategory
    //     0xb87114: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f50] Obj!EventCategory@e310c1
    //     0xb87118: ldr             x16, [x16, #0xf50]
    // 0xb8711c: StoreField: r0->field_1f = r16
    //     0xb8711c: stur            w16, [x0, #0x1f]
    // 0xb87120: r1 = _ConstMap len:3
    //     0xb87120: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f68] Map<int, Color>(3)
    //     0xb87124: ldr             x1, [x1, #0xf68]
    // 0xb87128: r2 = 6
    //     0xb87128: movz            x2, #0x6
    // 0xb8712c: r0 = []()
    //     0xb8712c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb87130: stur            x0, [fp, #-0x10]
    // 0xb87134: cmp             w0, NULL
    // 0xb87138: b.eq            #0xb87238
    // 0xb8713c: r1 = _ConstMap len:3
    //     0xb8713c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f68] Map<int, Color>(3)
    //     0xb87140: ldr             x1, [x1, #0xf68]
    // 0xb87144: r2 = 4
    //     0xb87144: movz            x2, #0x4
    // 0xb87148: r0 = []()
    //     0xb87148: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8714c: cmp             w0, NULL
    // 0xb87150: b.eq            #0xb8723c
    // 0xb87154: r16 = <Color>
    //     0xb87154: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb87158: ldr             x16, [x16, #0x158]
    // 0xb8715c: stp             x0, x16, [SP, #8]
    // 0xb87160: ldur            x16, [fp, #-0x10]
    // 0xb87164: str             x16, [SP]
    // 0xb87168: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87168: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8716c: r0 = mode()
    //     0xb8716c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb87170: ldur            x1, [fp, #-8]
    // 0xb87174: ArrayStore: r1[5] = r0  ; List_4
    //     0xb87174: add             x25, x1, #0x23
    //     0xb87178: str             w0, [x25]
    //     0xb8717c: tbz             w0, #0, #0xb87198
    //     0xb87180: ldurb           w16, [x1, #-1]
    //     0xb87184: ldurb           w17, [x0, #-1]
    //     0xb87188: and             x16, x17, x16, lsr #2
    //     0xb8718c: tst             x16, HEAP, lsr #32
    //     0xb87190: b.eq            #0xb87198
    //     0xb87194: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87198: ldur            x0, [fp, #-8]
    // 0xb8719c: r16 = Instance_EventCategory
    //     0xb8719c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EventCategory@e310e1
    //     0xb871a0: ldr             x16, [x16, #0xf70]
    // 0xb871a4: StoreField: r0->field_27 = r16
    //     0xb871a4: stur            w16, [x0, #0x27]
    // 0xb871a8: r1 = _ConstMap len:10
    //     0xb871a8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb871ac: ldr             x1, [x1, #0xc08]
    // 0xb871b0: r2 = 600
    //     0xb871b0: movz            x2, #0x258
    // 0xb871b4: r0 = []()
    //     0xb871b4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb871b8: cmp             w0, NULL
    // 0xb871bc: b.eq            #0xb87240
    // 0xb871c0: r16 = <Color>
    //     0xb871c0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb871c4: ldr             x16, [x16, #0x158]
    // 0xb871c8: stp             x0, x16, [SP, #8]
    // 0xb871cc: r16 = Instance_MaterialColor
    //     0xb871cc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb871d0: ldr             x16, [x16, #0xcf0]
    // 0xb871d4: str             x16, [SP]
    // 0xb871d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb871d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb871dc: r0 = mode()
    //     0xb871dc: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb871e0: ldur            x1, [fp, #-8]
    // 0xb871e4: ArrayStore: r1[7] = r0  ; List_4
    //     0xb871e4: add             x25, x1, #0x2b
    //     0xb871e8: str             w0, [x25]
    //     0xb871ec: tbz             w0, #0, #0xb87208
    //     0xb871f0: ldurb           w16, [x1, #-1]
    //     0xb871f4: ldurb           w17, [x0, #-1]
    //     0xb871f8: and             x16, x17, x16, lsr #2
    //     0xb871fc: tst             x16, HEAP, lsr #32
    //     0xb87200: b.eq            #0xb87208
    //     0xb87204: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87208: r16 = <EventCategory, Color>
    //     0xb87208: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ef0] TypeArguments: <EventCategory, Color>
    //     0xb8720c: ldr             x16, [x16, #0xef0]
    // 0xb87210: ldur            lr, [fp, #-8]
    // 0xb87214: stp             lr, x16, [SP]
    // 0xb87218: r0 = Map._fromLiteral()
    //     0xb87218: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8721c: LeaveFrame
    //     0xb8721c: mov             SP, fp
    //     0xb87220: ldp             fp, lr, [SP], #0x10
    // 0xb87224: ret
    //     0xb87224: ret             
    // 0xb87228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb87228: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8722c: b               #0xb87024
    // 0xb87230: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87230: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87234: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87234: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87238: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87238: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8723c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8723c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87240: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5058, size: 0x10, field offset: 0xc
//   const constructor, 
class NCalendarEventMark extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb86f20, size: 0xec
    // 0xb86f20: EnterFrame
    //     0xb86f20: stp             fp, lr, [SP, #-0x10]!
    //     0xb86f24: mov             fp, SP
    // 0xb86f28: AllocStack(0x28)
    //     0xb86f28: sub             SP, SP, #0x28
    // 0xb86f2c: SetupParameters(NCalendarEventMark this /* r1 => r1, fp-0x8 */)
    //     0xb86f2c: stur            x1, [fp, #-8]
    // 0xb86f30: CheckStackOverflow
    //     0xb86f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86f34: cmp             SP, x16
    //     0xb86f38: b.ls            #0xb87004
    // 0xb86f3c: r0 = InitLateStaticField(0x15c8) // [package:nuonline/app/modules/calendar/widgets/calendar_cell_date.dart] ::eventCategoryMarkColor
    //     0xb86f3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb86f40: ldr             x0, [x0, #0x2b90]
    //     0xb86f44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb86f48: cmp             w0, w16
    //     0xb86f4c: b.ne            #0xb86f5c
    //     0xb86f50: add             x2, PP, #0x47, lsl #12  ; [pp+0x47ed8] Field <::.eventCategoryMarkColor>: static late (offset: 0x15c8)
    //     0xb86f54: ldr             x2, [x2, #0xed8]
    //     0xb86f58: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xb86f5c: mov             x3, x0
    // 0xb86f60: ldur            x0, [fp, #-8]
    // 0xb86f64: stur            x3, [fp, #-0x10]
    // 0xb86f68: LoadField: r2 = r0->field_b
    //     0xb86f68: ldur            w2, [x0, #0xb]
    // 0xb86f6c: DecompressPointer r2
    //     0xb86f6c: add             x2, x2, HEAP, lsl #32
    // 0xb86f70: mov             x1, x3
    // 0xb86f74: r0 = _getValueOrData()
    //     0xb86f74: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb86f78: mov             x1, x0
    // 0xb86f7c: ldur            x0, [fp, #-0x10]
    // 0xb86f80: LoadField: r2 = r0->field_f
    //     0xb86f80: ldur            w2, [x0, #0xf]
    // 0xb86f84: DecompressPointer r2
    //     0xb86f84: add             x2, x2, HEAP, lsl #32
    // 0xb86f88: cmp             w2, w1
    // 0xb86f8c: b.ne            #0xb86f98
    // 0xb86f90: r0 = Null
    //     0xb86f90: mov             x0, NULL
    // 0xb86f94: b               #0xb86f9c
    // 0xb86f98: mov             x0, x1
    // 0xb86f9c: stur            x0, [fp, #-8]
    // 0xb86fa0: r0 = BoxDecoration()
    //     0xb86fa0: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb86fa4: mov             x1, x0
    // 0xb86fa8: ldur            x0, [fp, #-8]
    // 0xb86fac: stur            x1, [fp, #-0x10]
    // 0xb86fb0: StoreField: r1->field_7 = r0
    //     0xb86fb0: stur            w0, [x1, #7]
    // 0xb86fb4: r0 = Instance_BoxShape
    //     0xb86fb4: add             x0, PP, #0x34, lsl #12  ; [pp+0x349f0] Obj!BoxShape@e35e21
    //     0xb86fb8: ldr             x0, [x0, #0x9f0]
    // 0xb86fbc: StoreField: r1->field_23 = r0
    //     0xb86fbc: stur            w0, [x1, #0x23]
    // 0xb86fc0: r0 = Container()
    //     0xb86fc0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb86fc4: stur            x0, [fp, #-8]
    // 0xb86fc8: r16 = 6.000000
    //     0xb86fc8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xb86fcc: ldr             x16, [x16, #0xd70]
    // 0xb86fd0: r30 = 6.000000
    //     0xb86fd0: add             lr, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xb86fd4: ldr             lr, [lr, #0xd70]
    // 0xb86fd8: stp             lr, x16, [SP, #8]
    // 0xb86fdc: ldur            x16, [fp, #-0x10]
    // 0xb86fe0: str             x16, [SP]
    // 0xb86fe4: mov             x1, x0
    // 0xb86fe8: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb86fe8: add             x4, PP, #0x37, lsl #12  ; [pp+0x37fb8] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb86fec: ldr             x4, [x4, #0xfb8]
    // 0xb86ff0: r0 = Container()
    //     0xb86ff0: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb86ff4: ldur            x0, [fp, #-8]
    // 0xb86ff8: LeaveFrame
    //     0xb86ff8: mov             SP, fp
    //     0xb86ffc: ldp             fp, lr, [SP], #0x10
    // 0xb87000: ret
    //     0xb87000: ret             
    // 0xb87004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb87004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb87008: b               #0xb86f3c
  }
}

// class id: 5059, size: 0x20, field offset: 0xc
//   const constructor, 
class NCalendarCellDate extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb866bc, size: 0x764
    // 0xb866bc: EnterFrame
    //     0xb866bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb866c0: mov             fp, SP
    // 0xb866c4: AllocStack(0x68)
    //     0xb866c4: sub             SP, SP, #0x68
    // 0xb866c8: SetupParameters(NCalendarCellDate this /* r1 => r1, fp-0x8 */)
    //     0xb866c8: stur            x1, [fp, #-8]
    // 0xb866cc: CheckStackOverflow
    //     0xb866cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb866d0: cmp             SP, x16
    //     0xb866d4: b.ls            #0xb86e0c
    // 0xb866d8: r16 = <Color>
    //     0xb866d8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb866dc: ldr             x16, [x16, #0x158]
    // 0xb866e0: r30 = Instance_Color
    //     0xb866e0: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb866e4: stp             lr, x16, [SP, #8]
    // 0xb866e8: r16 = Instance_Color
    //     0xb866e8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fc8] Obj!Color@e2b0e1
    //     0xb866ec: ldr             x16, [x16, #0xfc8]
    // 0xb866f0: str             x16, [SP]
    // 0xb866f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb866f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb866f8: r0 = mode()
    //     0xb866f8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb866fc: r1 = _ConstMap len:10
    //     0xb866fc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb86700: ldr             x1, [x1, #0xc08]
    // 0xb86704: r2 = 600
    //     0xb86704: movz            x2, #0x258
    // 0xb86708: stur            x0, [fp, #-0x10]
    // 0xb8670c: r0 = []()
    //     0xb8670c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb86710: cmp             w0, NULL
    // 0xb86714: b.eq            #0xb86e14
    // 0xb86718: r16 = <Color>
    //     0xb86718: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8671c: ldr             x16, [x16, #0x158]
    // 0xb86720: stp             x0, x16, [SP, #8]
    // 0xb86724: r16 = Instance_MaterialColor
    //     0xb86724: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb86728: ldr             x16, [x16, #0xcf0]
    // 0xb8672c: str             x16, [SP]
    // 0xb86730: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb86730: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb86734: r0 = mode()
    //     0xb86734: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb86738: r1 = _ConstMap len:3
    //     0xb86738: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb8673c: ldr             x1, [x1, #0xcd0]
    // 0xb86740: r2 = 4
    //     0xb86740: movz            x2, #0x4
    // 0xb86744: stur            x0, [fp, #-0x18]
    // 0xb86748: r0 = []()
    //     0xb86748: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8674c: cmp             w0, NULL
    // 0xb86750: b.eq            #0xb86e18
    // 0xb86754: r16 = <Color>
    //     0xb86754: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb86758: ldr             x16, [x16, #0x158]
    // 0xb8675c: stp             x0, x16, [SP, #8]
    // 0xb86760: r16 = Instance_MaterialColor
    //     0xb86760: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e40] Obj!MaterialColor@e2bbf1
    //     0xb86764: ldr             x16, [x16, #0xe40]
    // 0xb86768: str             x16, [SP]
    // 0xb8676c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8676c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb86770: r0 = mode()
    //     0xb86770: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb86774: mov             x1, x0
    // 0xb86778: ldur            x0, [fp, #-8]
    // 0xb8677c: LoadField: r2 = r0->field_13
    //     0xb8677c: ldur            w2, [x0, #0x13]
    // 0xb86780: DecompressPointer r2
    //     0xb86780: add             x2, x2, HEAP, lsl #32
    // 0xb86784: LoadField: r3 = r0->field_f
    //     0xb86784: ldur            w3, [x0, #0xf]
    // 0xb86788: DecompressPointer r3
    //     0xb86788: add             x3, x3, HEAP, lsl #32
    // 0xb8678c: tbnz            w3, #4, #0xb86798
    // 0xb86790: ldur            x3, [fp, #-0x18]
    // 0xb86794: b               #0xb8679c
    // 0xb86798: ldur            x3, [fp, #-0x10]
    // 0xb8679c: tbnz            w2, #4, #0xb867a8
    // 0xb867a0: mov             x2, x1
    // 0xb867a4: b               #0xb867ac
    // 0xb867a8: mov             x2, x3
    // 0xb867ac: stur            x2, [fp, #-0x18]
    // 0xb867b0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb867b0: ldur            w3, [x0, #0x17]
    // 0xb867b4: DecompressPointer r3
    //     0xb867b4: add             x3, x3, HEAP, lsl #32
    // 0xb867b8: stur            x3, [fp, #-0x10]
    // 0xb867bc: tbz             w3, #4, #0xb867d4
    // 0xb867c0: LoadField: r1 = r0->field_b
    //     0xb867c0: ldur            w1, [x0, #0xb]
    // 0xb867c4: DecompressPointer r1
    //     0xb867c4: add             x1, x1, HEAP, lsl #32
    // 0xb867c8: r0 = toHijri()
    //     0xb867c8: bl              #0x815550  ; [package:nuonline/services/hijri_service.dart] HijriService::toHijri
    // 0xb867cc: LoadField: r1 = r0->field_7
    //     0xb867cc: ldur            x1, [x0, #7]
    // 0xb867d0: b               #0xb867d8
    // 0xb867d4: r1 = 0
    //     0xb867d4: movz            x1, #0
    // 0xb867d8: ldur            x0, [fp, #-8]
    // 0xb867dc: stur            x1, [fp, #-0x28]
    // 0xb867e0: LoadField: r2 = r0->field_b
    //     0xb867e0: ldur            w2, [x0, #0xb]
    // 0xb867e4: DecompressPointer r2
    //     0xb867e4: add             x2, x2, HEAP, lsl #32
    // 0xb867e8: stur            x2, [fp, #-0x20]
    // 0xb867ec: r0 = JavaneseCalendar()
    //     0xb867ec: bl              #0x821fe0  ; AllocateJavaneseCalendarStub -> JavaneseCalendar (size=0x14)
    // 0xb867f0: mov             x1, x0
    // 0xb867f4: ldur            x2, [fp, #-0x20]
    // 0xb867f8: stur            x0, [fp, #-0x30]
    // 0xb867fc: r0 = JavaneseCalendar.fromDate()
    //     0xb867fc: bl              #0x821c10  ; [package:nuonline/app/modules/calendar/widgets/javanese_calendar.dart] JavaneseCalendar::JavaneseCalendar.fromDate
    // 0xb86800: ldur            x0, [fp, #-0x30]
    // 0xb86804: LoadField: r2 = r0->field_f
    //     0xb86804: ldur            w2, [x0, #0xf]
    // 0xb86808: DecompressPointer r2
    //     0xb86808: add             x2, x2, HEAP, lsl #32
    // 0xb8680c: LoadField: r1 = r0->field_7
    //     0xb8680c: ldur            x1, [x0, #7]
    // 0xb86810: sub             x3, x1, #1
    // 0xb86814: LoadField: r0 = r2->field_b
    //     0xb86814: ldur            w0, [x2, #0xb]
    // 0xb86818: r1 = LoadInt32Instr(r0)
    //     0xb86818: sbfx            x1, x0, #1, #0x1f
    // 0xb8681c: mov             x0, x1
    // 0xb86820: mov             x1, x3
    // 0xb86824: cmp             x1, x0
    // 0xb86828: b.hs            #0xb86e1c
    // 0xb8682c: LoadField: r0 = r2->field_f
    //     0xb8682c: ldur            w0, [x2, #0xf]
    // 0xb86830: DecompressPointer r0
    //     0xb86830: add             x0, x0, HEAP, lsl #32
    // 0xb86834: ArrayLoad: r2 = r0[r3]  ; Unknown_4
    //     0xb86834: add             x16, x0, x3, lsl #2
    //     0xb86838: ldur            w2, [x16, #0xf]
    // 0xb8683c: DecompressPointer r2
    //     0xb8683c: add             x2, x2, HEAP, lsl #32
    // 0xb86840: ldur            x3, [fp, #-0x28]
    // 0xb86844: stur            x2, [fp, #-0x30]
    // 0xb86848: r0 = BoxInt64Instr(r3)
    //     0xb86848: sbfiz           x0, x3, #1, #0x1f
    //     0xb8684c: cmp             x3, x0, asr #1
    //     0xb86850: b.eq            #0xb8685c
    //     0xb86854: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb86858: stur            x3, [x0, #7]
    // 0xb8685c: r1 = 60
    //     0xb8685c: movz            x1, #0x3c
    // 0xb86860: branchIfSmi(r0, 0xb8686c)
    //     0xb86860: tbz             w0, #0, #0xb8686c
    // 0xb86864: r1 = LoadClassIdInstr(r0)
    //     0xb86864: ldur            x1, [x0, #-1]
    //     0xb86868: ubfx            x1, x1, #0xc, #0x14
    // 0xb8686c: str             x0, [SP]
    // 0xb86870: mov             x0, x1
    // 0xb86874: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb86874: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb86878: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb86878: movz            x17, #0x2b03
    //     0xb8687c: add             lr, x0, x17
    //     0xb86880: ldr             lr, [x21, lr, lsl #3]
    //     0xb86884: blr             lr
    // 0xb86888: mov             x1, x0
    // 0xb8688c: r0 = arabicNumber()
    //     0xb8688c: bl              #0xb86e20  ; [package:nuikit/src/utils/utils.dart] ::arabicNumber
    // 0xb86890: stur            x0, [fp, #-0x38]
    // 0xb86894: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb86894: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb86898: ldr             x0, [x0, #0x2670]
    //     0xb8689c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb868a0: cmp             w0, w16
    //     0xb868a4: b.ne            #0xb868b0
    //     0xb868a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb868ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb868b0: r0 = GetNavigation.textTheme()
    //     0xb868b0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb868b4: LoadField: r1 = r0->field_23
    //     0xb868b4: ldur            w1, [x0, #0x23]
    // 0xb868b8: DecompressPointer r1
    //     0xb868b8: add             x1, x1, HEAP, lsl #32
    // 0xb868bc: cmp             w1, NULL
    // 0xb868c0: b.ne            #0xb868cc
    // 0xb868c4: r2 = Null
    //     0xb868c4: mov             x2, NULL
    // 0xb868c8: b               #0xb868ec
    // 0xb868cc: r16 = "OmarNaskh"
    //     0xb868cc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xb868d0: ldr             x16, [x16, #0xbc8]
    // 0xb868d4: ldur            lr, [fp, #-0x18]
    // 0xb868d8: stp             lr, x16, [SP]
    // 0xb868dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb868dc: add             x4, PP, #0x40, lsl #12  ; [pp+0x406b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb868e0: ldr             x4, [x4, #0x6b8]
    // 0xb868e4: r0 = copyWith()
    //     0xb868e4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb868e8: mov             x2, x0
    // 0xb868ec: ldur            x1, [fp, #-0x20]
    // 0xb868f0: ldur            x0, [fp, #-0x38]
    // 0xb868f4: stur            x2, [fp, #-0x40]
    // 0xb868f8: r0 = Text()
    //     0xb868f8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb868fc: mov             x2, x0
    // 0xb86900: ldur            x0, [fp, #-0x38]
    // 0xb86904: stur            x2, [fp, #-0x48]
    // 0xb86908: StoreField: r2->field_b = r0
    //     0xb86908: stur            w0, [x2, #0xb]
    // 0xb8690c: ldur            x0, [fp, #-0x40]
    // 0xb86910: StoreField: r2->field_13 = r0
    //     0xb86910: stur            w0, [x2, #0x13]
    // 0xb86914: r0 = Instance_TextAlign
    //     0xb86914: ldr             x0, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0xb86918: StoreField: r2->field_1b = r0
    //     0xb86918: stur            w0, [x2, #0x1b]
    // 0xb8691c: r3 = Instance__LinearTextScaler
    //     0xb8691c: ldr             x3, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb86920: StoreField: r2->field_33 = r3
    //     0xb86920: stur            w3, [x2, #0x33]
    // 0xb86924: ldur            x4, [fp, #-0x20]
    // 0xb86928: r0 = LoadClassIdInstr(r4)
    //     0xb86928: ldur            x0, [x4, #-1]
    //     0xb8692c: ubfx            x0, x0, #0xc, #0x14
    // 0xb86930: mov             x1, x4
    // 0xb86934: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xb86934: sub             lr, x0, #0xfdf
    //     0xb86938: ldr             lr, [x21, lr, lsl #3]
    //     0xb8693c: blr             lr
    // 0xb86940: mov             x2, x0
    // 0xb86944: r0 = BoxInt64Instr(r2)
    //     0xb86944: sbfiz           x0, x2, #1, #0x1f
    //     0xb86948: cmp             x2, x0, asr #1
    //     0xb8694c: b.eq            #0xb86958
    //     0xb86950: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb86954: stur            x2, [x0, #7]
    // 0xb86958: r1 = 60
    //     0xb86958: movz            x1, #0x3c
    // 0xb8695c: branchIfSmi(r0, 0xb86968)
    //     0xb8695c: tbz             w0, #0, #0xb86968
    // 0xb86960: r1 = LoadClassIdInstr(r0)
    //     0xb86960: ldur            x1, [x0, #-1]
    //     0xb86964: ubfx            x1, x1, #0xc, #0x14
    // 0xb86968: str             x0, [SP]
    // 0xb8696c: mov             x0, x1
    // 0xb86970: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb86970: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb86974: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb86974: movz            x17, #0x2b03
    //     0xb86978: add             lr, x0, x17
    //     0xb8697c: ldr             lr, [x21, lr, lsl #3]
    //     0xb86980: blr             lr
    // 0xb86984: stur            x0, [fp, #-0x38]
    // 0xb86988: r0 = GetNavigation.textTheme()
    //     0xb86988: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8698c: LoadField: r1 = r0->field_f
    //     0xb8698c: ldur            w1, [x0, #0xf]
    // 0xb86990: DecompressPointer r1
    //     0xb86990: add             x1, x1, HEAP, lsl #32
    // 0xb86994: cmp             w1, NULL
    // 0xb86998: b.ne            #0xb869a4
    // 0xb8699c: r2 = Null
    //     0xb8699c: mov             x2, NULL
    // 0xb869a0: b               #0xb869c4
    // 0xb869a4: r16 = 18.000000
    //     0xb869a4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb869a8: ldr             x16, [x16, #0x958]
    // 0xb869ac: ldur            lr, [fp, #-0x18]
    // 0xb869b0: stp             lr, x16, [SP]
    // 0xb869b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb869b4: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb869b8: ldr             x4, [x4, #0xaa0]
    // 0xb869bc: r0 = copyWith()
    //     0xb869bc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb869c0: mov             x2, x0
    // 0xb869c4: ldur            x1, [fp, #-0x48]
    // 0xb869c8: ldur            x0, [fp, #-0x38]
    // 0xb869cc: stur            x2, [fp, #-0x40]
    // 0xb869d0: r0 = Text()
    //     0xb869d0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb869d4: mov             x3, x0
    // 0xb869d8: ldur            x0, [fp, #-0x38]
    // 0xb869dc: stur            x3, [fp, #-0x50]
    // 0xb869e0: StoreField: r3->field_b = r0
    //     0xb869e0: stur            w0, [x3, #0xb]
    // 0xb869e4: ldur            x0, [fp, #-0x40]
    // 0xb869e8: StoreField: r3->field_13 = r0
    //     0xb869e8: stur            w0, [x3, #0x13]
    // 0xb869ec: r0 = Instance__LinearTextScaler
    //     0xb869ec: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb869f0: StoreField: r3->field_33 = r0
    //     0xb869f0: stur            w0, [x3, #0x33]
    // 0xb869f4: r1 = Null
    //     0xb869f4: mov             x1, NULL
    // 0xb869f8: r2 = 6
    //     0xb869f8: movz            x2, #0x6
    // 0xb869fc: r0 = AllocateArray()
    //     0xb869fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb86a00: mov             x2, x0
    // 0xb86a04: ldur            x0, [fp, #-0x48]
    // 0xb86a08: stur            x2, [fp, #-0x38]
    // 0xb86a0c: StoreField: r2->field_f = r0
    //     0xb86a0c: stur            w0, [x2, #0xf]
    // 0xb86a10: r16 = Instance_SizedBox
    //     0xb86a10: add             x16, PP, #0x40, lsl #12  ; [pp+0x406c0] Obj!SizedBox@e1e561
    //     0xb86a14: ldr             x16, [x16, #0x6c0]
    // 0xb86a18: StoreField: r2->field_13 = r16
    //     0xb86a18: stur            w16, [x2, #0x13]
    // 0xb86a1c: ldur            x0, [fp, #-0x50]
    // 0xb86a20: ArrayStore: r2[0] = r0  ; List_4
    //     0xb86a20: stur            w0, [x2, #0x17]
    // 0xb86a24: r1 = <Widget>
    //     0xb86a24: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb86a28: r0 = AllocateGrowableArray()
    //     0xb86a28: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb86a2c: mov             x1, x0
    // 0xb86a30: ldur            x0, [fp, #-0x38]
    // 0xb86a34: stur            x1, [fp, #-0x40]
    // 0xb86a38: StoreField: r1->field_f = r0
    //     0xb86a38: stur            w0, [x1, #0xf]
    // 0xb86a3c: r0 = 6
    //     0xb86a3c: movz            x0, #0x6
    // 0xb86a40: StoreField: r1->field_b = r0
    //     0xb86a40: stur            w0, [x1, #0xb]
    // 0xb86a44: r0 = Row()
    //     0xb86a44: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb86a48: mov             x1, x0
    // 0xb86a4c: r0 = Instance_Axis
    //     0xb86a4c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb86a50: stur            x1, [fp, #-0x38]
    // 0xb86a54: StoreField: r1->field_f = r0
    //     0xb86a54: stur            w0, [x1, #0xf]
    // 0xb86a58: r2 = Instance_MainAxisAlignment
    //     0xb86a58: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb86a5c: ldr             x2, [x2, #0x290]
    // 0xb86a60: StoreField: r1->field_13 = r2
    //     0xb86a60: stur            w2, [x1, #0x13]
    // 0xb86a64: r2 = Instance_MainAxisSize
    //     0xb86a64: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb86a68: ldr             x2, [x2, #0x738]
    // 0xb86a6c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb86a6c: stur            w2, [x1, #0x17]
    // 0xb86a70: r2 = Instance_CrossAxisAlignment
    //     0xb86a70: add             x2, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0xb86a74: ldr             x2, [x2, #0xfa8]
    // 0xb86a78: StoreField: r1->field_1b = r2
    //     0xb86a78: stur            w2, [x1, #0x1b]
    // 0xb86a7c: r2 = Instance_VerticalDirection
    //     0xb86a7c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb86a80: ldr             x2, [x2, #0x748]
    // 0xb86a84: StoreField: r1->field_23 = r2
    //     0xb86a84: stur            w2, [x1, #0x23]
    // 0xb86a88: r3 = Instance_Clip
    //     0xb86a88: add             x3, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb86a8c: ldr             x3, [x3, #0x750]
    // 0xb86a90: StoreField: r1->field_2b = r3
    //     0xb86a90: stur            w3, [x1, #0x2b]
    // 0xb86a94: StoreField: r1->field_2f = rZR
    //     0xb86a94: stur            xzr, [x1, #0x2f]
    // 0xb86a98: ldur            x4, [fp, #-0x40]
    // 0xb86a9c: StoreField: r1->field_b = r4
    //     0xb86a9c: stur            w4, [x1, #0xb]
    // 0xb86aa0: r0 = GetNavigation.textTheme()
    //     0xb86aa0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb86aa4: LoadField: r1 = r0->field_27
    //     0xb86aa4: ldur            w1, [x0, #0x27]
    // 0xb86aa8: DecompressPointer r1
    //     0xb86aa8: add             x1, x1, HEAP, lsl #32
    // 0xb86aac: cmp             w1, NULL
    // 0xb86ab0: b.ne            #0xb86abc
    // 0xb86ab4: r4 = Null
    //     0xb86ab4: mov             x4, NULL
    // 0xb86ab8: b               #0xb86ad4
    // 0xb86abc: r16 = 10.000000
    //     0xb86abc: ldr             x16, [PP, #0x6a00]  ; [pp+0x6a00] 10
    // 0xb86ac0: str             x16, [SP]
    // 0xb86ac4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb86ac4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb86ac8: ldr             x4, [x4, #0x88]
    // 0xb86acc: r0 = copyWith()
    //     0xb86acc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb86ad0: mov             x4, x0
    // 0xb86ad4: ldur            x3, [fp, #-8]
    // 0xb86ad8: ldur            x1, [fp, #-0x20]
    // 0xb86adc: ldur            x0, [fp, #-0x38]
    // 0xb86ae0: ldur            x2, [fp, #-0x30]
    // 0xb86ae4: stur            x4, [fp, #-0x40]
    // 0xb86ae8: r0 = Text()
    //     0xb86ae8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb86aec: mov             x3, x0
    // 0xb86af0: ldur            x0, [fp, #-0x30]
    // 0xb86af4: stur            x3, [fp, #-0x48]
    // 0xb86af8: StoreField: r3->field_b = r0
    //     0xb86af8: stur            w0, [x3, #0xb]
    // 0xb86afc: ldur            x0, [fp, #-0x40]
    // 0xb86b00: StoreField: r3->field_13 = r0
    //     0xb86b00: stur            w0, [x3, #0x13]
    // 0xb86b04: r0 = Instance__LinearTextScaler
    //     0xb86b04: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb86b08: StoreField: r3->field_33 = r0
    //     0xb86b08: stur            w0, [x3, #0x33]
    // 0xb86b0c: ldur            x1, [fp, #-8]
    // 0xb86b10: LoadField: r4 = r1->field_1b
    //     0xb86b10: ldur            w4, [x1, #0x1b]
    // 0xb86b14: DecompressPointer r4
    //     0xb86b14: add             x4, x4, HEAP, lsl #32
    // 0xb86b18: stur            x4, [fp, #-0x30]
    // 0xb86b1c: r1 = Function '<anonymous closure>':.
    //     0xb86b1c: add             x1, PP, #0x40, lsl #12  ; [pp+0x406c8] AnonymousClosure: (0xb86ef4), in [package:nuonline/app/modules/calendar/widgets/calendar_cell_date.dart] NCalendarCellDate::build (0xb866bc)
    //     0xb86b20: ldr             x1, [x1, #0x6c8]
    // 0xb86b24: r2 = Null
    //     0xb86b24: mov             x2, NULL
    // 0xb86b28: r0 = AllocateClosure()
    //     0xb86b28: bl              #0xec1630  ; AllocateClosureStub
    // 0xb86b2c: mov             x1, x0
    // 0xb86b30: ldur            x0, [fp, #-0x30]
    // 0xb86b34: r2 = LoadClassIdInstr(r0)
    //     0xb86b34: ldur            x2, [x0, #-1]
    //     0xb86b38: ubfx            x2, x2, #0xc, #0x14
    // 0xb86b3c: r16 = <NCalendarEventMark>
    //     0xb86b3c: add             x16, PP, #0x40, lsl #12  ; [pp+0x406d0] TypeArguments: <NCalendarEventMark>
    //     0xb86b40: ldr             x16, [x16, #0x6d0]
    // 0xb86b44: stp             x0, x16, [SP, #8]
    // 0xb86b48: str             x1, [SP]
    // 0xb86b4c: mov             x0, x2
    // 0xb86b50: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb86b50: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb86b54: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xb86b54: movz            x17, #0xf28c
    //     0xb86b58: add             lr, x0, x17
    //     0xb86b5c: ldr             lr, [x21, lr, lsl #3]
    //     0xb86b60: blr             lr
    // 0xb86b64: r1 = LoadClassIdInstr(r0)
    //     0xb86b64: ldur            x1, [x0, #-1]
    //     0xb86b68: ubfx            x1, x1, #0xc, #0x14
    // 0xb86b6c: mov             x16, x0
    // 0xb86b70: mov             x0, x1
    // 0xb86b74: mov             x1, x16
    // 0xb86b78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb86b78: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb86b7c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb86b7c: movz            x17, #0xd889
    //     0xb86b80: add             lr, x0, x17
    //     0xb86b84: ldr             lr, [x21, lr, lsl #3]
    //     0xb86b88: blr             lr
    // 0xb86b8c: stur            x0, [fp, #-8]
    // 0xb86b90: r0 = Wrap()
    //     0xb86b90: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb86b94: mov             x1, x0
    // 0xb86b98: r0 = Instance_Axis
    //     0xb86b98: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb86b9c: stur            x1, [fp, #-0x30]
    // 0xb86ba0: StoreField: r1->field_f = r0
    //     0xb86ba0: stur            w0, [x1, #0xf]
    // 0xb86ba4: r0 = Instance_WrapAlignment
    //     0xb86ba4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xb86ba8: ldr             x0, [x0, #0x610]
    // 0xb86bac: StoreField: r1->field_13 = r0
    //     0xb86bac: stur            w0, [x1, #0x13]
    // 0xb86bb0: d0 = 4.000000
    //     0xb86bb0: fmov            d0, #4.00000000
    // 0xb86bb4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb86bb4: stur            d0, [x1, #0x17]
    // 0xb86bb8: StoreField: r1->field_1f = r0
    //     0xb86bb8: stur            w0, [x1, #0x1f]
    // 0xb86bbc: StoreField: r1->field_23 = rZR
    //     0xb86bbc: stur            xzr, [x1, #0x23]
    // 0xb86bc0: r0 = Instance_WrapCrossAlignment
    //     0xb86bc0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xb86bc4: ldr             x0, [x0, #0x618]
    // 0xb86bc8: StoreField: r1->field_2b = r0
    //     0xb86bc8: stur            w0, [x1, #0x2b]
    // 0xb86bcc: r0 = Instance_VerticalDirection
    //     0xb86bcc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb86bd0: ldr             x0, [x0, #0x748]
    // 0xb86bd4: StoreField: r1->field_33 = r0
    //     0xb86bd4: stur            w0, [x1, #0x33]
    // 0xb86bd8: r2 = Instance_Clip
    //     0xb86bd8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb86bdc: ldr             x2, [x2, #0x750]
    // 0xb86be0: StoreField: r1->field_37 = r2
    //     0xb86be0: stur            w2, [x1, #0x37]
    // 0xb86be4: ldur            x3, [fp, #-8]
    // 0xb86be8: StoreField: r1->field_b = r3
    //     0xb86be8: stur            w3, [x1, #0xb]
    // 0xb86bec: r0 = SizedBox()
    //     0xb86bec: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb86bf0: mov             x3, x0
    // 0xb86bf4: r0 = 6.000000
    //     0xb86bf4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xb86bf8: ldr             x0, [x0, #0xd70]
    // 0xb86bfc: stur            x3, [fp, #-8]
    // 0xb86c00: StoreField: r3->field_13 = r0
    //     0xb86c00: stur            w0, [x3, #0x13]
    // 0xb86c04: ldur            x0, [fp, #-0x30]
    // 0xb86c08: StoreField: r3->field_b = r0
    //     0xb86c08: stur            w0, [x3, #0xb]
    // 0xb86c0c: r1 = Null
    //     0xb86c0c: mov             x1, NULL
    // 0xb86c10: r2 = 10
    //     0xb86c10: movz            x2, #0xa
    // 0xb86c14: r0 = AllocateArray()
    //     0xb86c14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb86c18: mov             x2, x0
    // 0xb86c1c: ldur            x0, [fp, #-0x38]
    // 0xb86c20: stur            x2, [fp, #-0x30]
    // 0xb86c24: StoreField: r2->field_f = r0
    //     0xb86c24: stur            w0, [x2, #0xf]
    // 0xb86c28: r16 = Instance_SizedBox
    //     0xb86c28: add             x16, PP, #0x40, lsl #12  ; [pp+0x406d8] Obj!SizedBox@e1e541
    //     0xb86c2c: ldr             x16, [x16, #0x6d8]
    // 0xb86c30: StoreField: r2->field_13 = r16
    //     0xb86c30: stur            w16, [x2, #0x13]
    // 0xb86c34: ldur            x0, [fp, #-0x48]
    // 0xb86c38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb86c38: stur            w0, [x2, #0x17]
    // 0xb86c3c: r16 = Instance_SizedBox
    //     0xb86c3c: add             x16, PP, #0x40, lsl #12  ; [pp+0x406e0] Obj!SizedBox@e1e321
    //     0xb86c40: ldr             x16, [x16, #0x6e0]
    // 0xb86c44: StoreField: r2->field_1b = r16
    //     0xb86c44: stur            w16, [x2, #0x1b]
    // 0xb86c48: ldur            x0, [fp, #-8]
    // 0xb86c4c: StoreField: r2->field_1f = r0
    //     0xb86c4c: stur            w0, [x2, #0x1f]
    // 0xb86c50: r1 = <Widget>
    //     0xb86c50: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb86c54: r0 = AllocateGrowableArray()
    //     0xb86c54: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb86c58: mov             x1, x0
    // 0xb86c5c: ldur            x0, [fp, #-0x30]
    // 0xb86c60: stur            x1, [fp, #-8]
    // 0xb86c64: StoreField: r1->field_f = r0
    //     0xb86c64: stur            w0, [x1, #0xf]
    // 0xb86c68: r0 = 10
    //     0xb86c68: movz            x0, #0xa
    // 0xb86c6c: StoreField: r1->field_b = r0
    //     0xb86c6c: stur            w0, [x1, #0xb]
    // 0xb86c70: r0 = Column()
    //     0xb86c70: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb86c74: mov             x1, x0
    // 0xb86c78: r0 = Instance_Axis
    //     0xb86c78: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb86c7c: stur            x1, [fp, #-0x30]
    // 0xb86c80: StoreField: r1->field_f = r0
    //     0xb86c80: stur            w0, [x1, #0xf]
    // 0xb86c84: r0 = Instance_MainAxisAlignment
    //     0xb86c84: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb86c88: ldr             x0, [x0, #0x730]
    // 0xb86c8c: StoreField: r1->field_13 = r0
    //     0xb86c8c: stur            w0, [x1, #0x13]
    // 0xb86c90: r0 = Instance_MainAxisSize
    //     0xb86c90: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb86c94: ldr             x0, [x0, #0xe88]
    // 0xb86c98: ArrayStore: r1[0] = r0  ; List_4
    //     0xb86c98: stur            w0, [x1, #0x17]
    // 0xb86c9c: r0 = Instance_CrossAxisAlignment
    //     0xb86c9c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb86ca0: ldr             x0, [x0, #0x740]
    // 0xb86ca4: StoreField: r1->field_1b = r0
    //     0xb86ca4: stur            w0, [x1, #0x1b]
    // 0xb86ca8: r0 = Instance_VerticalDirection
    //     0xb86ca8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb86cac: ldr             x0, [x0, #0x748]
    // 0xb86cb0: StoreField: r1->field_23 = r0
    //     0xb86cb0: stur            w0, [x1, #0x23]
    // 0xb86cb4: r0 = Instance_Clip
    //     0xb86cb4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb86cb8: ldr             x0, [x0, #0x750]
    // 0xb86cbc: StoreField: r1->field_2b = r0
    //     0xb86cbc: stur            w0, [x1, #0x2b]
    // 0xb86cc0: StoreField: r1->field_2f = rZR
    //     0xb86cc0: stur            xzr, [x1, #0x2f]
    // 0xb86cc4: ldur            x0, [fp, #-8]
    // 0xb86cc8: StoreField: r1->field_b = r0
    //     0xb86cc8: stur            w0, [x1, #0xb]
    // 0xb86ccc: r0 = Center()
    //     0xb86ccc: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb86cd0: mov             x3, x0
    // 0xb86cd4: r2 = Instance_Alignment
    //     0xb86cd4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb86cd8: ldr             x2, [x2, #0x898]
    // 0xb86cdc: stur            x3, [fp, #-8]
    // 0xb86ce0: StoreField: r3->field_f = r2
    //     0xb86ce0: stur            w2, [x3, #0xf]
    // 0xb86ce4: ldur            x0, [fp, #-0x30]
    // 0xb86ce8: StoreField: r3->field_b = r0
    //     0xb86ce8: stur            w0, [x3, #0xb]
    // 0xb86cec: ldur            x1, [fp, #-0x20]
    // 0xb86cf0: r0 = LoadClassIdInstr(r1)
    //     0xb86cf0: ldur            x0, [x1, #-1]
    //     0xb86cf4: ubfx            x0, x0, #0xc, #0x14
    // 0xb86cf8: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xb86cf8: sub             lr, x0, #0xfdf
    //     0xb86cfc: ldr             lr, [x21, lr, lsl #3]
    //     0xb86d00: blr             lr
    // 0xb86d04: mov             x2, x0
    // 0xb86d08: r0 = BoxInt64Instr(r2)
    //     0xb86d08: sbfiz           x0, x2, #1, #0x1f
    //     0xb86d0c: cmp             x2, x0, asr #1
    //     0xb86d10: b.eq            #0xb86d1c
    //     0xb86d14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb86d18: stur            x2, [x0, #7]
    // 0xb86d1c: r1 = 60
    //     0xb86d1c: movz            x1, #0x3c
    // 0xb86d20: branchIfSmi(r0, 0xb86d2c)
    //     0xb86d20: tbz             w0, #0, #0xb86d2c
    // 0xb86d24: r1 = LoadClassIdInstr(r0)
    //     0xb86d24: ldur            x1, [x0, #-1]
    //     0xb86d28: ubfx            x1, x1, #0xc, #0x14
    // 0xb86d2c: str             x0, [SP]
    // 0xb86d30: mov             x0, x1
    // 0xb86d34: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb86d34: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb86d38: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb86d38: movz            x17, #0x2b03
    //     0xb86d3c: add             lr, x0, x17
    //     0xb86d40: ldr             lr, [x21, lr, lsl #3]
    //     0xb86d44: blr             lr
    // 0xb86d48: stur            x0, [fp, #-0x20]
    // 0xb86d4c: r0 = GetNavigation.textTheme()
    //     0xb86d4c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb86d50: LoadField: r1 = r0->field_f
    //     0xb86d50: ldur            w1, [x0, #0xf]
    // 0xb86d54: DecompressPointer r1
    //     0xb86d54: add             x1, x1, HEAP, lsl #32
    // 0xb86d58: cmp             w1, NULL
    // 0xb86d5c: b.ne            #0xb86d68
    // 0xb86d60: r2 = Null
    //     0xb86d60: mov             x2, NULL
    // 0xb86d64: b               #0xb86d88
    // 0xb86d68: r16 = 18.000000
    //     0xb86d68: add             x16, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb86d6c: ldr             x16, [x16, #0x958]
    // 0xb86d70: ldur            lr, [fp, #-0x18]
    // 0xb86d74: stp             lr, x16, [SP]
    // 0xb86d78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb86d78: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb86d7c: ldr             x4, [x4, #0xaa0]
    // 0xb86d80: r0 = copyWith()
    //     0xb86d80: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb86d84: mov             x2, x0
    // 0xb86d88: ldur            x1, [fp, #-0x10]
    // 0xb86d8c: ldur            x0, [fp, #-0x20]
    // 0xb86d90: stur            x2, [fp, #-0x18]
    // 0xb86d94: r0 = Text()
    //     0xb86d94: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb86d98: mov             x1, x0
    // 0xb86d9c: ldur            x0, [fp, #-0x20]
    // 0xb86da0: stur            x1, [fp, #-0x30]
    // 0xb86da4: StoreField: r1->field_b = r0
    //     0xb86da4: stur            w0, [x1, #0xb]
    // 0xb86da8: ldur            x0, [fp, #-0x18]
    // 0xb86dac: StoreField: r1->field_13 = r0
    //     0xb86dac: stur            w0, [x1, #0x13]
    // 0xb86db0: r0 = Instance__LinearTextScaler
    //     0xb86db0: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb86db4: StoreField: r1->field_33 = r0
    //     0xb86db4: stur            w0, [x1, #0x33]
    // 0xb86db8: r0 = Opacity()
    //     0xb86db8: bl              #0x9e12d8  ; AllocateOpacityStub -> Opacity (size=0x1c)
    // 0xb86dbc: d0 = 0.300000
    //     0xb86dbc: add             x17, PP, #0x29, lsl #12  ; [pp+0x29068] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb86dc0: ldr             d0, [x17, #0x68]
    // 0xb86dc4: stur            x0, [fp, #-0x18]
    // 0xb86dc8: StoreField: r0->field_f = d0
    //     0xb86dc8: stur            d0, [x0, #0xf]
    // 0xb86dcc: r1 = false
    //     0xb86dcc: add             x1, NULL, #0x30  ; false
    // 0xb86dd0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb86dd0: stur            w1, [x0, #0x17]
    // 0xb86dd4: ldur            x1, [fp, #-0x30]
    // 0xb86dd8: StoreField: r0->field_b = r1
    //     0xb86dd8: stur            w1, [x0, #0xb]
    // 0xb86ddc: r0 = Center()
    //     0xb86ddc: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb86de0: r1 = Instance_Alignment
    //     0xb86de0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb86de4: ldr             x1, [x1, #0x898]
    // 0xb86de8: StoreField: r0->field_f = r1
    //     0xb86de8: stur            w1, [x0, #0xf]
    // 0xb86dec: ldur            x1, [fp, #-0x18]
    // 0xb86df0: StoreField: r0->field_b = r1
    //     0xb86df0: stur            w1, [x0, #0xb]
    // 0xb86df4: ldur            x1, [fp, #-0x10]
    // 0xb86df8: tbz             w1, #4, #0xb86e00
    // 0xb86dfc: ldur            x0, [fp, #-8]
    // 0xb86e00: LeaveFrame
    //     0xb86e00: mov             SP, fp
    //     0xb86e04: ldp             fp, lr, [SP], #0x10
    // 0xb86e08: ret
    //     0xb86e08: ret             
    // 0xb86e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86e0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86e10: b               #0xb866d8
    // 0xb86e14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86e14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86e18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86e18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86e1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb86e1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] NCalendarEventMark <anonymous closure>(dynamic, EventCategory) {
    // ** addr: 0xb86ef4, size: 0x20
    // 0xb86ef4: EnterFrame
    //     0xb86ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xb86ef8: mov             fp, SP
    // 0xb86efc: r0 = NCalendarEventMark()
    //     0xb86efc: bl              #0xb86f14  ; AllocateNCalendarEventMarkStub -> NCalendarEventMark (size=0x10)
    // 0xb86f00: ldr             x1, [fp, #0x10]
    // 0xb86f04: StoreField: r0->field_b = r1
    //     0xb86f04: stur            w1, [x0, #0xb]
    // 0xb86f08: LeaveFrame
    //     0xb86f08: mov             SP, fp
    //     0xb86f0c: ldp             fp, lr, [SP], #0x10
    // 0xb86f10: ret
    //     0xb86f10: ret             
  }
}
