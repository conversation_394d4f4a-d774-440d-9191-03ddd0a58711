// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_days_header.dart

// class id: 1050162, size: 0x8
class :: {
}

// class id: 5057, size: 0xc, field offset: 0xc
//   const constructor, 
class NCalendarDaysHeader extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb87244, size: 0x13c
    // 0xb87244: EnterFrame
    //     0xb87244: stp             fp, lr, [SP, #-0x10]!
    //     0xb87248: mov             fp, SP
    // 0xb8724c: AllocStack(0x38)
    //     0xb8724c: sub             SP, SP, #0x38
    // 0xb87250: CheckStackOverflow
    //     0xb87250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb87254: cmp             SP, x16
    //     0xb87258: b.ls            #0xb87378
    // 0xb8725c: r1 = _ConstMap len:3
    //     0xb8725c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb87260: ldr             x1, [x1, #0xbe8]
    // 0xb87264: r2 = 2
    //     0xb87264: movz            x2, #0x2
    // 0xb87268: r0 = []()
    //     0xb87268: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8726c: r16 = <Color?>
    //     0xb8726c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb87270: ldr             x16, [x16, #0x98]
    // 0xb87274: stp             x0, x16, [SP, #8]
    // 0xb87278: r16 = Instance_MaterialColor
    //     0xb87278: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb8727c: ldr             x16, [x16, #0xe38]
    // 0xb87280: str             x16, [SP]
    // 0xb87284: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87284: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87288: r0 = mode()
    //     0xb87288: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8728c: r1 = Function '<anonymous closure>':.
    //     0xb8728c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35fa8] AnonymousClosure: (0xb87380), in [package:nuonline/app/modules/calendar/widgets/calendar_days_header.dart] NCalendarDaysHeader::build (0xb87244)
    //     0xb87290: ldr             x1, [x1, #0xfa8]
    // 0xb87294: r2 = Null
    //     0xb87294: mov             x2, NULL
    // 0xb87298: stur            x0, [fp, #-8]
    // 0xb8729c: r0 = AllocateClosure()
    //     0xb8729c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb872a0: r16 = <Expanded>
    //     0xb872a0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fb0] TypeArguments: <Expanded>
    //     0xb872a4: ldr             x16, [x16, #0xfb0]
    // 0xb872a8: r30 = const [Ahad, Senin, Selasa, Rabu, Kamis, Jumat, Sabtu]
    //     0xb872a8: add             lr, PP, #0x35, lsl #12  ; [pp+0x35fb8] List<String>(7)
    //     0xb872ac: ldr             lr, [lr, #0xfb8]
    // 0xb872b0: stp             lr, x16, [SP, #8]
    // 0xb872b4: str             x0, [SP]
    // 0xb872b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb872b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb872bc: r0 = map()
    //     0xb872bc: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb872c0: LoadField: r1 = r0->field_7
    //     0xb872c0: ldur            w1, [x0, #7]
    // 0xb872c4: DecompressPointer r1
    //     0xb872c4: add             x1, x1, HEAP, lsl #32
    // 0xb872c8: mov             x2, x0
    // 0xb872cc: r0 = _GrowableList.of()
    //     0xb872cc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb872d0: stur            x0, [fp, #-0x10]
    // 0xb872d4: r0 = Row()
    //     0xb872d4: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb872d8: mov             x1, x0
    // 0xb872dc: r0 = Instance_Axis
    //     0xb872dc: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb872e0: stur            x1, [fp, #-0x18]
    // 0xb872e4: StoreField: r1->field_f = r0
    //     0xb872e4: stur            w0, [x1, #0xf]
    // 0xb872e8: r0 = Instance_MainAxisAlignment
    //     0xb872e8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb872ec: ldr             x0, [x0, #0x290]
    // 0xb872f0: StoreField: r1->field_13 = r0
    //     0xb872f0: stur            w0, [x1, #0x13]
    // 0xb872f4: r0 = Instance_MainAxisSize
    //     0xb872f4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb872f8: ldr             x0, [x0, #0x738]
    // 0xb872fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb872fc: stur            w0, [x1, #0x17]
    // 0xb87300: r0 = Instance_CrossAxisAlignment
    //     0xb87300: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb87304: ldr             x0, [x0, #0x740]
    // 0xb87308: StoreField: r1->field_1b = r0
    //     0xb87308: stur            w0, [x1, #0x1b]
    // 0xb8730c: r0 = Instance_VerticalDirection
    //     0xb8730c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb87310: ldr             x0, [x0, #0x748]
    // 0xb87314: StoreField: r1->field_23 = r0
    //     0xb87314: stur            w0, [x1, #0x23]
    // 0xb87318: r0 = Instance_Clip
    //     0xb87318: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8731c: ldr             x0, [x0, #0x750]
    // 0xb87320: StoreField: r1->field_2b = r0
    //     0xb87320: stur            w0, [x1, #0x2b]
    // 0xb87324: StoreField: r1->field_2f = rZR
    //     0xb87324: stur            xzr, [x1, #0x2f]
    // 0xb87328: ldur            x0, [fp, #-0x10]
    // 0xb8732c: StoreField: r1->field_b = r0
    //     0xb8732c: stur            w0, [x1, #0xb]
    // 0xb87330: r0 = Container()
    //     0xb87330: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb87334: stur            x0, [fp, #-0x10]
    // 0xb87338: r16 = 32.000000
    //     0xb87338: add             x16, PP, #0x25, lsl #12  ; [pp+0x25d88] 32
    //     0xb8733c: ldr             x16, [x16, #0xd88]
    // 0xb87340: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb87340: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb87344: ldr             lr, [lr, #0xc58]
    // 0xb87348: stp             lr, x16, [SP, #0x10]
    // 0xb8734c: ldur            x16, [fp, #-8]
    // 0xb87350: ldur            lr, [fp, #-0x18]
    // 0xb87354: stp             lr, x16, [SP]
    // 0xb87358: mov             x1, x0
    // 0xb8735c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xb8735c: add             x4, PP, #0x35, lsl #12  ; [pp+0x35fc0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb87360: ldr             x4, [x4, #0xfc0]
    // 0xb87364: r0 = Container()
    //     0xb87364: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb87368: ldur            x0, [fp, #-0x10]
    // 0xb8736c: LeaveFrame
    //     0xb8736c: mov             SP, fp
    //     0xb87370: ldp             fp, lr, [SP], #0x10
    // 0xb87374: ret
    //     0xb87374: ret             
    // 0xb87378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb87378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8737c: b               #0xb8725c
  }
  [closure] Expanded <anonymous closure>(dynamic, String) {
    // ** addr: 0xb87380, size: 0x110
    // 0xb87380: EnterFrame
    //     0xb87380: stp             fp, lr, [SP, #-0x10]!
    //     0xb87384: mov             fp, SP
    // 0xb87388: AllocStack(0x28)
    //     0xb87388: sub             SP, SP, #0x28
    // 0xb8738c: CheckStackOverflow
    //     0xb8738c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb87390: cmp             SP, x16
    //     0xb87394: b.ls            #0xb87488
    // 0xb87398: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb87398: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8739c: ldr             x0, [x0, #0x2670]
    //     0xb873a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb873a4: cmp             w0, w16
    //     0xb873a8: b.ne            #0xb873b4
    //     0xb873ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb873b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb873b4: r0 = GetNavigation.textTheme()
    //     0xb873b4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb873b8: LoadField: r1 = r0->field_27
    //     0xb873b8: ldur            w1, [x0, #0x27]
    // 0xb873bc: DecompressPointer r1
    //     0xb873bc: add             x1, x1, HEAP, lsl #32
    // 0xb873c0: stur            x1, [fp, #-8]
    // 0xb873c4: cmp             w1, NULL
    // 0xb873c8: b.ne            #0xb873d4
    // 0xb873cc: r1 = Null
    //     0xb873cc: mov             x1, NULL
    // 0xb873d0: b               #0xb87414
    // 0xb873d4: r16 = <Color?>
    //     0xb873d4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb873d8: ldr             x16, [x16, #0x98]
    // 0xb873dc: r30 = Instance_MaterialColor
    //     0xb873dc: add             lr, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb873e0: ldr             lr, [lr, #0xe38]
    // 0xb873e4: stp             lr, x16, [SP, #8]
    // 0xb873e8: r16 = Instance_Color
    //     0xb873e8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fc8] Obj!Color@e2b0e1
    //     0xb873ec: ldr             x16, [x16, #0xfc8]
    // 0xb873f0: str             x16, [SP]
    // 0xb873f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb873f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb873f8: r0 = mode()
    //     0xb873f8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb873fc: str             x0, [SP]
    // 0xb87400: ldur            x1, [fp, #-8]
    // 0xb87404: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb87404: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb87408: ldr             x4, [x4, #0x228]
    // 0xb8740c: r0 = copyWith()
    //     0xb8740c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb87410: mov             x1, x0
    // 0xb87414: ldr             x0, [fp, #0x10]
    // 0xb87418: stur            x1, [fp, #-8]
    // 0xb8741c: r0 = Text()
    //     0xb8741c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb87420: mov             x2, x0
    // 0xb87424: ldr             x0, [fp, #0x10]
    // 0xb87428: stur            x2, [fp, #-0x10]
    // 0xb8742c: StoreField: r2->field_b = r0
    //     0xb8742c: stur            w0, [x2, #0xb]
    // 0xb87430: ldur            x0, [fp, #-8]
    // 0xb87434: StoreField: r2->field_13 = r0
    //     0xb87434: stur            w0, [x2, #0x13]
    // 0xb87438: r0 = Instance_TextAlign
    //     0xb87438: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xb8743c: StoreField: r2->field_1b = r0
    //     0xb8743c: stur            w0, [x2, #0x1b]
    // 0xb87440: r0 = Instance_TextOverflow
    //     0xb87440: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f08] Obj!TextOverflow@e35ce1
    //     0xb87444: ldr             x0, [x0, #0xf08]
    // 0xb87448: StoreField: r2->field_2b = r0
    //     0xb87448: stur            w0, [x2, #0x2b]
    // 0xb8744c: r0 = Instance__LinearTextScaler
    //     0xb8744c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb87450: StoreField: r2->field_33 = r0
    //     0xb87450: stur            w0, [x2, #0x33]
    // 0xb87454: r1 = <FlexParentData>
    //     0xb87454: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb87458: ldr             x1, [x1, #0x720]
    // 0xb8745c: r0 = Expanded()
    //     0xb8745c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb87460: r1 = 1
    //     0xb87460: movz            x1, #0x1
    // 0xb87464: StoreField: r0->field_13 = r1
    //     0xb87464: stur            x1, [x0, #0x13]
    // 0xb87468: r1 = Instance_FlexFit
    //     0xb87468: add             x1, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb8746c: ldr             x1, [x1, #0x728]
    // 0xb87470: StoreField: r0->field_1b = r1
    //     0xb87470: stur            w1, [x0, #0x1b]
    // 0xb87474: ldur            x1, [fp, #-0x10]
    // 0xb87478: StoreField: r0->field_b = r1
    //     0xb87478: stur            w1, [x0, #0xb]
    // 0xb8747c: LeaveFrame
    //     0xb8747c: mov             SP, fp
    //     0xb87480: ldp             fp, lr, [SP], #0x10
    // 0xb87484: ret
    //     0xb87484: ret             
    // 0xb87488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb87488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8748c: b               #0xb87398
  }
}
