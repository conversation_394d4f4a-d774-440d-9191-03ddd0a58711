// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_event_list_tile.dart

// class id: 1050163, size: 0x8
class :: {
}

// class id: 5056, size: 0x20, field offset: 0xc
//   const constructor, 
class NCalendarEventListTile extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb87490, size: 0x14d8
    // 0xb87490: EnterFrame
    //     0xb87490: stp             fp, lr, [SP, #-0x10]!
    //     0xb87494: mov             fp, SP
    // 0xb87498: AllocStack(0x80)
    //     0xb87498: sub             SP, SP, #0x80
    // 0xb8749c: SetupParameters(NCalendarEventListTile this /* r1 => r0, fp-0x8 */)
    //     0xb8749c: mov             x0, x1
    //     0xb874a0: stur            x1, [fp, #-8]
    // 0xb874a4: CheckStackOverflow
    //     0xb874a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb874a8: cmp             SP, x16
    //     0xb874ac: b.ls            #0xb88904
    // 0xb874b0: r1 = Null
    //     0xb874b0: mov             x1, NULL
    // 0xb874b4: r2 = 16
    //     0xb874b4: movz            x2, #0x10
    // 0xb874b8: r0 = AllocateArray()
    //     0xb874b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb874bc: stur            x0, [fp, #-0x10]
    // 0xb874c0: r16 = Instance_EventCategory
    //     0xb874c0: add             x16, PP, #9, lsl #12  ; [pp+0x92d0] Obj!EventCategory@e310a1
    //     0xb874c4: ldr             x16, [x16, #0x2d0]
    // 0xb874c8: StoreField: r0->field_f = r16
    //     0xb874c8: stur            w16, [x0, #0xf]
    // 0xb874cc: r1 = Null
    //     0xb874cc: mov             x1, NULL
    // 0xb874d0: r2 = 8
    //     0xb874d0: movz            x2, #0x8
    // 0xb874d4: r0 = AllocateArray()
    //     0xb874d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb874d8: stur            x0, [fp, #-0x18]
    // 0xb874dc: r16 = "color"
    //     0xb874dc: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0xb874e0: StoreField: r0->field_f = r16
    //     0xb874e0: stur            w16, [x0, #0xf]
    // 0xb874e4: r16 = <Color>
    //     0xb874e4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb874e8: ldr             x16, [x16, #0x158]
    // 0xb874ec: r30 = Instance_Color
    //     0xb874ec: add             lr, PP, #0x35, lsl #12  ; [pp+0x35f18] Obj!Color@e2b381
    //     0xb874f0: ldr             lr, [lr, #0xf18]
    // 0xb874f4: stp             lr, x16, [SP, #8]
    // 0xb874f8: r16 = Instance_Color
    //     0xb874f8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f20] Obj!Color@e2b351
    //     0xb874fc: ldr             x16, [x16, #0xf20]
    // 0xb87500: str             x16, [SP]
    // 0xb87504: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87504: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87508: r0 = mode()
    //     0xb87508: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8750c: ldur            x1, [fp, #-0x18]
    // 0xb87510: ArrayStore: r1[1] = r0  ; List_4
    //     0xb87510: add             x25, x1, #0x13
    //     0xb87514: str             w0, [x25]
    //     0xb87518: tbz             w0, #0, #0xb87534
    //     0xb8751c: ldurb           w16, [x1, #-1]
    //     0xb87520: ldurb           w17, [x0, #-1]
    //     0xb87524: and             x16, x17, x16, lsr #2
    //     0xb87528: tst             x16, HEAP, lsr #32
    //     0xb8752c: b.eq            #0xb87534
    //     0xb87530: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87534: ldur            x1, [fp, #-0x18]
    // 0xb87538: r16 = "borderColor"
    //     0xb87538: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb8753c: ldr             x16, [x16, #0xf28]
    // 0xb87540: ArrayStore: r1[0] = r16  ; List_4
    //     0xb87540: stur            w16, [x1, #0x17]
    // 0xb87544: r16 = <Color>
    //     0xb87544: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb87548: ldr             x16, [x16, #0x158]
    // 0xb8754c: r30 = Instance_Color
    //     0xb8754c: add             lr, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!Color@e2b321
    //     0xb87550: ldr             lr, [lr, #0xf30]
    // 0xb87554: stp             lr, x16, [SP, #8]
    // 0xb87558: r16 = Instance_Color
    //     0xb87558: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!Color@e2b321
    //     0xb8755c: ldr             x16, [x16, #0xf30]
    // 0xb87560: str             x16, [SP]
    // 0xb87564: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87564: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87568: r0 = mode()
    //     0xb87568: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8756c: ldur            x1, [fp, #-0x18]
    // 0xb87570: ArrayStore: r1[3] = r0  ; List_4
    //     0xb87570: add             x25, x1, #0x1b
    //     0xb87574: str             w0, [x25]
    //     0xb87578: tbz             w0, #0, #0xb87594
    //     0xb8757c: ldurb           w16, [x1, #-1]
    //     0xb87580: ldurb           w17, [x0, #-1]
    //     0xb87584: and             x16, x17, x16, lsr #2
    //     0xb87588: tst             x16, HEAP, lsr #32
    //     0xb8758c: b.eq            #0xb87594
    //     0xb87590: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87594: r16 = <String, Color>
    //     0xb87594: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f38] TypeArguments: <String, Color>
    //     0xb87598: ldr             x16, [x16, #0xf38]
    // 0xb8759c: ldur            lr, [fp, #-0x18]
    // 0xb875a0: stp             lr, x16, [SP]
    // 0xb875a4: r0 = Map._fromLiteral()
    //     0xb875a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb875a8: ldur            x1, [fp, #-0x10]
    // 0xb875ac: ArrayStore: r1[1] = r0  ; List_4
    //     0xb875ac: add             x25, x1, #0x13
    //     0xb875b0: str             w0, [x25]
    //     0xb875b4: tbz             w0, #0, #0xb875d0
    //     0xb875b8: ldurb           w16, [x1, #-1]
    //     0xb875bc: ldurb           w17, [x0, #-1]
    //     0xb875c0: and             x16, x17, x16, lsr #2
    //     0xb875c4: tst             x16, HEAP, lsr #32
    //     0xb875c8: b.eq            #0xb875d0
    //     0xb875cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb875d0: ldur            x0, [fp, #-0x10]
    // 0xb875d4: r16 = Instance_EventCategory
    //     0xb875d4: add             x16, PP, #9, lsl #12  ; [pp+0x9180] Obj!EventCategory@e31101
    //     0xb875d8: ldr             x16, [x16, #0x180]
    // 0xb875dc: ArrayStore: r0[0] = r16  ; List_4
    //     0xb875dc: stur            w16, [x0, #0x17]
    // 0xb875e0: r1 = Null
    //     0xb875e0: mov             x1, NULL
    // 0xb875e4: r2 = 8
    //     0xb875e4: movz            x2, #0x8
    // 0xb875e8: r0 = AllocateArray()
    //     0xb875e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb875ec: stur            x0, [fp, #-0x18]
    // 0xb875f0: r16 = "color"
    //     0xb875f0: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0xb875f4: StoreField: r0->field_f = r16
    //     0xb875f4: stur            w16, [x0, #0xf]
    // 0xb875f8: r16 = <Color>
    //     0xb875f8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb875fc: ldr             x16, [x16, #0x158]
    // 0xb87600: r30 = Instance_Color
    //     0xb87600: add             lr, PP, #0x35, lsl #12  ; [pp+0x35f40] Obj!Color@e2b2f1
    //     0xb87604: ldr             lr, [lr, #0xf40]
    // 0xb87608: stp             lr, x16, [SP, #8]
    // 0xb8760c: r16 = Instance_Color
    //     0xb8760c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!Color@e2b2c1
    //     0xb87610: ldr             x16, [x16, #0xf48]
    // 0xb87614: str             x16, [SP]
    // 0xb87618: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87618: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8761c: r0 = mode()
    //     0xb8761c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb87620: ldur            x1, [fp, #-0x18]
    // 0xb87624: ArrayStore: r1[1] = r0  ; List_4
    //     0xb87624: add             x25, x1, #0x13
    //     0xb87628: str             w0, [x25]
    //     0xb8762c: tbz             w0, #0, #0xb87648
    //     0xb87630: ldurb           w16, [x1, #-1]
    //     0xb87634: ldurb           w17, [x0, #-1]
    //     0xb87638: and             x16, x17, x16, lsr #2
    //     0xb8763c: tst             x16, HEAP, lsr #32
    //     0xb87640: b.eq            #0xb87648
    //     0xb87644: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87648: ldur            x0, [fp, #-0x18]
    // 0xb8764c: r16 = "borderColor"
    //     0xb8764c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb87650: ldr             x16, [x16, #0xf28]
    // 0xb87654: ArrayStore: r0[0] = r16  ; List_4
    //     0xb87654: stur            w16, [x0, #0x17]
    // 0xb87658: r1 = _ConstMap len:3
    //     0xb87658: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb8765c: ldr             x1, [x1, #0xcd0]
    // 0xb87660: r2 = 4
    //     0xb87660: movz            x2, #0x4
    // 0xb87664: r0 = []()
    //     0xb87664: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb87668: stur            x0, [fp, #-0x20]
    // 0xb8766c: cmp             w0, NULL
    // 0xb87670: b.eq            #0xb8890c
    // 0xb87674: r1 = _ConstMap len:3
    //     0xb87674: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb87678: ldr             x1, [x1, #0xcd0]
    // 0xb8767c: r2 = 4
    //     0xb8767c: movz            x2, #0x4
    // 0xb87680: r0 = []()
    //     0xb87680: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb87684: cmp             w0, NULL
    // 0xb87688: b.eq            #0xb88910
    // 0xb8768c: r16 = <Color>
    //     0xb8768c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb87690: ldr             x16, [x16, #0x158]
    // 0xb87694: stp             x0, x16, [SP, #8]
    // 0xb87698: ldur            x16, [fp, #-0x20]
    // 0xb8769c: str             x16, [SP]
    // 0xb876a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb876a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb876a4: r0 = mode()
    //     0xb876a4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb876a8: ldur            x1, [fp, #-0x18]
    // 0xb876ac: ArrayStore: r1[3] = r0  ; List_4
    //     0xb876ac: add             x25, x1, #0x1b
    //     0xb876b0: str             w0, [x25]
    //     0xb876b4: tbz             w0, #0, #0xb876d0
    //     0xb876b8: ldurb           w16, [x1, #-1]
    //     0xb876bc: ldurb           w17, [x0, #-1]
    //     0xb876c0: and             x16, x17, x16, lsr #2
    //     0xb876c4: tst             x16, HEAP, lsr #32
    //     0xb876c8: b.eq            #0xb876d0
    //     0xb876cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb876d0: r16 = <String, Color>
    //     0xb876d0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f38] TypeArguments: <String, Color>
    //     0xb876d4: ldr             x16, [x16, #0xf38]
    // 0xb876d8: ldur            lr, [fp, #-0x18]
    // 0xb876dc: stp             lr, x16, [SP]
    // 0xb876e0: r0 = Map._fromLiteral()
    //     0xb876e0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb876e4: ldur            x1, [fp, #-0x10]
    // 0xb876e8: ArrayStore: r1[3] = r0  ; List_4
    //     0xb876e8: add             x25, x1, #0x1b
    //     0xb876ec: str             w0, [x25]
    //     0xb876f0: tbz             w0, #0, #0xb8770c
    //     0xb876f4: ldurb           w16, [x1, #-1]
    //     0xb876f8: ldurb           w17, [x0, #-1]
    //     0xb876fc: and             x16, x17, x16, lsr #2
    //     0xb87700: tst             x16, HEAP, lsr #32
    //     0xb87704: b.eq            #0xb8770c
    //     0xb87708: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8770c: ldur            x0, [fp, #-0x10]
    // 0xb87710: r16 = Instance_EventCategory
    //     0xb87710: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f50] Obj!EventCategory@e310c1
    //     0xb87714: ldr             x16, [x16, #0xf50]
    // 0xb87718: StoreField: r0->field_1f = r16
    //     0xb87718: stur            w16, [x0, #0x1f]
    // 0xb8771c: r1 = Null
    //     0xb8771c: mov             x1, NULL
    // 0xb87720: r2 = 8
    //     0xb87720: movz            x2, #0x8
    // 0xb87724: r0 = AllocateArray()
    //     0xb87724: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb87728: stur            x0, [fp, #-0x18]
    // 0xb8772c: r16 = "color"
    //     0xb8772c: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0xb87730: StoreField: r0->field_f = r16
    //     0xb87730: stur            w16, [x0, #0xf]
    // 0xb87734: r16 = <Color>
    //     0xb87734: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb87738: ldr             x16, [x16, #0x158]
    // 0xb8773c: r30 = Instance_Color
    //     0xb8773c: add             lr, PP, #0x35, lsl #12  ; [pp+0x35f58] Obj!Color@e2b291
    //     0xb87740: ldr             lr, [lr, #0xf58]
    // 0xb87744: stp             lr, x16, [SP, #8]
    // 0xb87748: r16 = Instance_Color
    //     0xb87748: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f60] Obj!Color@e2b261
    //     0xb8774c: ldr             x16, [x16, #0xf60]
    // 0xb87750: str             x16, [SP]
    // 0xb87754: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87754: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87758: r0 = mode()
    //     0xb87758: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8775c: ldur            x1, [fp, #-0x18]
    // 0xb87760: ArrayStore: r1[1] = r0  ; List_4
    //     0xb87760: add             x25, x1, #0x13
    //     0xb87764: str             w0, [x25]
    //     0xb87768: tbz             w0, #0, #0xb87784
    //     0xb8776c: ldurb           w16, [x1, #-1]
    //     0xb87770: ldurb           w17, [x0, #-1]
    //     0xb87774: and             x16, x17, x16, lsr #2
    //     0xb87778: tst             x16, HEAP, lsr #32
    //     0xb8777c: b.eq            #0xb87784
    //     0xb87780: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87784: ldur            x0, [fp, #-0x18]
    // 0xb87788: r16 = "borderColor"
    //     0xb87788: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb8778c: ldr             x16, [x16, #0xf28]
    // 0xb87790: ArrayStore: r0[0] = r16  ; List_4
    //     0xb87790: stur            w16, [x0, #0x17]
    // 0xb87794: r1 = _ConstMap len:3
    //     0xb87794: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f68] Map<int, Color>(3)
    //     0xb87798: ldr             x1, [x1, #0xf68]
    // 0xb8779c: r2 = 4
    //     0xb8779c: movz            x2, #0x4
    // 0xb877a0: r0 = []()
    //     0xb877a0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb877a4: stur            x0, [fp, #-0x20]
    // 0xb877a8: cmp             w0, NULL
    // 0xb877ac: b.eq            #0xb88914
    // 0xb877b0: r1 = _ConstMap len:3
    //     0xb877b0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f68] Map<int, Color>(3)
    //     0xb877b4: ldr             x1, [x1, #0xf68]
    // 0xb877b8: r2 = 4
    //     0xb877b8: movz            x2, #0x4
    // 0xb877bc: r0 = []()
    //     0xb877bc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb877c0: cmp             w0, NULL
    // 0xb877c4: b.eq            #0xb88918
    // 0xb877c8: r16 = <Color>
    //     0xb877c8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb877cc: ldr             x16, [x16, #0x158]
    // 0xb877d0: stp             x0, x16, [SP, #8]
    // 0xb877d4: ldur            x16, [fp, #-0x20]
    // 0xb877d8: str             x16, [SP]
    // 0xb877dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb877dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb877e0: r0 = mode()
    //     0xb877e0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb877e4: ldur            x1, [fp, #-0x18]
    // 0xb877e8: ArrayStore: r1[3] = r0  ; List_4
    //     0xb877e8: add             x25, x1, #0x1b
    //     0xb877ec: str             w0, [x25]
    //     0xb877f0: tbz             w0, #0, #0xb8780c
    //     0xb877f4: ldurb           w16, [x1, #-1]
    //     0xb877f8: ldurb           w17, [x0, #-1]
    //     0xb877fc: and             x16, x17, x16, lsr #2
    //     0xb87800: tst             x16, HEAP, lsr #32
    //     0xb87804: b.eq            #0xb8780c
    //     0xb87808: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8780c: r16 = <String, Color>
    //     0xb8780c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f38] TypeArguments: <String, Color>
    //     0xb87810: ldr             x16, [x16, #0xf38]
    // 0xb87814: ldur            lr, [fp, #-0x18]
    // 0xb87818: stp             lr, x16, [SP]
    // 0xb8781c: r0 = Map._fromLiteral()
    //     0xb8781c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb87820: ldur            x1, [fp, #-0x10]
    // 0xb87824: ArrayStore: r1[5] = r0  ; List_4
    //     0xb87824: add             x25, x1, #0x23
    //     0xb87828: str             w0, [x25]
    //     0xb8782c: tbz             w0, #0, #0xb87848
    //     0xb87830: ldurb           w16, [x1, #-1]
    //     0xb87834: ldurb           w17, [x0, #-1]
    //     0xb87838: and             x16, x17, x16, lsr #2
    //     0xb8783c: tst             x16, HEAP, lsr #32
    //     0xb87840: b.eq            #0xb87848
    //     0xb87844: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87848: ldur            x0, [fp, #-0x10]
    // 0xb8784c: r16 = Instance_EventCategory
    //     0xb8784c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EventCategory@e310e1
    //     0xb87850: ldr             x16, [x16, #0xf70]
    // 0xb87854: StoreField: r0->field_27 = r16
    //     0xb87854: stur            w16, [x0, #0x27]
    // 0xb87858: r1 = Null
    //     0xb87858: mov             x1, NULL
    // 0xb8785c: r2 = 8
    //     0xb8785c: movz            x2, #0x8
    // 0xb87860: r0 = AllocateArray()
    //     0xb87860: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb87864: stur            x0, [fp, #-0x18]
    // 0xb87868: r16 = "color"
    //     0xb87868: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0xb8786c: StoreField: r0->field_f = r16
    //     0xb8786c: stur            w16, [x0, #0xf]
    // 0xb87870: r1 = _ConstMap len:10
    //     0xb87870: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb87874: ldr             x1, [x1, #0xc08]
    // 0xb87878: r2 = 1800
    //     0xb87878: movz            x2, #0x708
    // 0xb8787c: r0 = []()
    //     0xb8787c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb87880: cmp             w0, NULL
    // 0xb87884: b.eq            #0xb8891c
    // 0xb87888: r16 = <Color>
    //     0xb87888: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8788c: ldr             x16, [x16, #0x158]
    // 0xb87890: stp             x0, x16, [SP, #8]
    // 0xb87894: r16 = Instance_Color
    //     0xb87894: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f78] Obj!Color@e2b231
    //     0xb87898: ldr             x16, [x16, #0xf78]
    // 0xb8789c: str             x16, [SP]
    // 0xb878a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb878a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb878a4: r0 = mode()
    //     0xb878a4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb878a8: ldur            x1, [fp, #-0x18]
    // 0xb878ac: ArrayStore: r1[1] = r0  ; List_4
    //     0xb878ac: add             x25, x1, #0x13
    //     0xb878b0: str             w0, [x25]
    //     0xb878b4: tbz             w0, #0, #0xb878d0
    //     0xb878b8: ldurb           w16, [x1, #-1]
    //     0xb878bc: ldurb           w17, [x0, #-1]
    //     0xb878c0: and             x16, x17, x16, lsr #2
    //     0xb878c4: tst             x16, HEAP, lsr #32
    //     0xb878c8: b.eq            #0xb878d0
    //     0xb878cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb878d0: ldur            x0, [fp, #-0x18]
    // 0xb878d4: r16 = "borderColor"
    //     0xb878d4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb878d8: ldr             x16, [x16, #0xf28]
    // 0xb878dc: ArrayStore: r0[0] = r16  ; List_4
    //     0xb878dc: stur            w16, [x0, #0x17]
    // 0xb878e0: r1 = _ConstMap len:10
    //     0xb878e0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb878e4: ldr             x1, [x1, #0xc08]
    // 0xb878e8: r2 = 600
    //     0xb878e8: movz            x2, #0x258
    // 0xb878ec: r0 = []()
    //     0xb878ec: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb878f0: cmp             w0, NULL
    // 0xb878f4: b.eq            #0xb88920
    // 0xb878f8: r16 = <Color>
    //     0xb878f8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb878fc: ldr             x16, [x16, #0x158]
    // 0xb87900: stp             x0, x16, [SP, #8]
    // 0xb87904: r16 = Instance_MaterialColor
    //     0xb87904: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb87908: ldr             x16, [x16, #0xcf0]
    // 0xb8790c: str             x16, [SP]
    // 0xb87910: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb87910: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb87914: r0 = mode()
    //     0xb87914: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb87918: ldur            x1, [fp, #-0x18]
    // 0xb8791c: ArrayStore: r1[3] = r0  ; List_4
    //     0xb8791c: add             x25, x1, #0x1b
    //     0xb87920: str             w0, [x25]
    //     0xb87924: tbz             w0, #0, #0xb87940
    //     0xb87928: ldurb           w16, [x1, #-1]
    //     0xb8792c: ldurb           w17, [x0, #-1]
    //     0xb87930: and             x16, x17, x16, lsr #2
    //     0xb87934: tst             x16, HEAP, lsr #32
    //     0xb87938: b.eq            #0xb87940
    //     0xb8793c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87940: r16 = <String, Color>
    //     0xb87940: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f38] TypeArguments: <String, Color>
    //     0xb87944: ldr             x16, [x16, #0xf38]
    // 0xb87948: ldur            lr, [fp, #-0x18]
    // 0xb8794c: stp             lr, x16, [SP]
    // 0xb87950: r0 = Map._fromLiteral()
    //     0xb87950: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb87954: ldur            x1, [fp, #-0x10]
    // 0xb87958: ArrayStore: r1[7] = r0  ; List_4
    //     0xb87958: add             x25, x1, #0x2b
    //     0xb8795c: str             w0, [x25]
    //     0xb87960: tbz             w0, #0, #0xb8797c
    //     0xb87964: ldurb           w16, [x1, #-1]
    //     0xb87968: ldurb           w17, [x0, #-1]
    //     0xb8796c: and             x16, x17, x16, lsr #2
    //     0xb87970: tst             x16, HEAP, lsr #32
    //     0xb87974: b.eq            #0xb8797c
    //     0xb87978: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8797c: r16 = <EventCategory, Map<String, Color>>
    //     0xb8797c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f80] TypeArguments: <EventCategory, Map<String, Color>>
    //     0xb87980: ldr             x16, [x16, #0xf80]
    // 0xb87984: ldur            lr, [fp, #-0x10]
    // 0xb87988: stp             lr, x16, [SP]
    // 0xb8798c: r0 = Map._fromLiteral()
    //     0xb8798c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb87990: mov             x2, x0
    // 0xb87994: ldur            x0, [fp, #-8]
    // 0xb87998: stur            x2, [fp, #-0x18]
    // 0xb8799c: LoadField: r3 = r0->field_b
    //     0xb8799c: ldur            w3, [x0, #0xb]
    // 0xb879a0: DecompressPointer r3
    //     0xb879a0: add             x3, x3, HEAP, lsl #32
    // 0xb879a4: mov             x1, x3
    // 0xb879a8: stur            x3, [fp, #-0x10]
    // 0xb879ac: r0 = toHijri()
    //     0xb879ac: bl              #0x815550  ; [package:nuonline/services/hijri_service.dart] HijriService::toHijri
    // 0xb879b0: ldur            x1, [fp, #-0x10]
    // 0xb879b4: stur            x0, [fp, #-0x20]
    // 0xb879b8: r0 = _parts()
    //     0xb879b8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb879bc: mov             x2, x0
    // 0xb879c0: LoadField: r0 = r2->field_b
    //     0xb879c0: ldur            w0, [x2, #0xb]
    // 0xb879c4: r1 = LoadInt32Instr(r0)
    //     0xb879c4: sbfx            x1, x0, #1, #0x1f
    // 0xb879c8: mov             x0, x1
    // 0xb879cc: r1 = 6
    //     0xb879cc: movz            x1, #0x6
    // 0xb879d0: cmp             x1, x0
    // 0xb879d4: b.hs            #0xb88924
    // 0xb879d8: LoadField: r0 = r2->field_27
    //     0xb879d8: ldur            w0, [x2, #0x27]
    // 0xb879dc: DecompressPointer r0
    //     0xb879dc: add             x0, x0, HEAP, lsl #32
    // 0xb879e0: r1 = LoadInt32Instr(r0)
    //     0xb879e0: sbfx            x1, x0, #1, #0x1f
    //     0xb879e4: tbz             w0, #0, #0xb879ec
    //     0xb879e8: ldur            x1, [x0, #7]
    // 0xb879ec: cmp             x1, #4
    // 0xb879f0: b.gt            #0xb87a44
    // 0xb879f4: cmp             x1, #2
    // 0xb879f8: b.gt            #0xb87a24
    // 0xb879fc: cmp             x1, #1
    // 0xb87a00: b.gt            #0xb87a18
    // 0xb87a04: cmp             w0, #2
    // 0xb87a08: b.ne            #0xb87a80
    // 0xb87a0c: r0 = "Senin"
    //     0xb87a0c: add             x0, PP, #9, lsl #12  ; [pp+0x90f8] "Senin"
    //     0xb87a10: ldr             x0, [x0, #0xf8]
    // 0xb87a14: b               #0xb87a88
    // 0xb87a18: r0 = "Selasa"
    //     0xb87a18: add             x0, PP, #9, lsl #12  ; [pp+0x9100] "Selasa"
    //     0xb87a1c: ldr             x0, [x0, #0x100]
    // 0xb87a20: b               #0xb87a88
    // 0xb87a24: cmp             x1, #3
    // 0xb87a28: b.gt            #0xb87a38
    // 0xb87a2c: r0 = "Rabu"
    //     0xb87a2c: add             x0, PP, #9, lsl #12  ; [pp+0x9108] "Rabu"
    //     0xb87a30: ldr             x0, [x0, #0x108]
    // 0xb87a34: b               #0xb87a88
    // 0xb87a38: r0 = "Kamis"
    //     0xb87a38: add             x0, PP, #9, lsl #12  ; [pp+0x9110] "Kamis"
    //     0xb87a3c: ldr             x0, [x0, #0x110]
    // 0xb87a40: b               #0xb87a88
    // 0xb87a44: cmp             x1, #6
    // 0xb87a48: b.gt            #0xb87a6c
    // 0xb87a4c: cmp             x1, #5
    // 0xb87a50: b.gt            #0xb87a60
    // 0xb87a54: r0 = "Jumat"
    //     0xb87a54: add             x0, PP, #9, lsl #12  ; [pp+0x9118] "Jumat"
    //     0xb87a58: ldr             x0, [x0, #0x118]
    // 0xb87a5c: b               #0xb87a88
    // 0xb87a60: r0 = "Sabtu"
    //     0xb87a60: add             x0, PP, #9, lsl #12  ; [pp+0x9120] "Sabtu"
    //     0xb87a64: ldr             x0, [x0, #0x120]
    // 0xb87a68: b               #0xb87a88
    // 0xb87a6c: cmp             w0, #0xe
    // 0xb87a70: b.ne            #0xb87a80
    // 0xb87a74: r0 = "Ahad"
    //     0xb87a74: add             x0, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xb87a78: ldr             x0, [x0, #0x128]
    // 0xb87a7c: b               #0xb87a88
    // 0xb87a80: r0 = "Ahad"
    //     0xb87a80: add             x0, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xb87a84: ldr             x0, [x0, #0x128]
    // 0xb87a88: stur            x0, [fp, #-0x28]
    // 0xb87a8c: r1 = Null
    //     0xb87a8c: mov             x1, NULL
    // 0xb87a90: r2 = 26
    //     0xb87a90: movz            x2, #0x1a
    // 0xb87a94: r0 = AllocateArray()
    //     0xb87a94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb87a98: mov             x2, x0
    // 0xb87a9c: ldur            x0, [fp, #-0x28]
    // 0xb87aa0: stur            x2, [fp, #-0x30]
    // 0xb87aa4: StoreField: r2->field_f = r0
    //     0xb87aa4: stur            w0, [x2, #0xf]
    // 0xb87aa8: r16 = ", "
    //     0xb87aa8: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb87aac: StoreField: r2->field_13 = r16
    //     0xb87aac: stur            w16, [x2, #0x13]
    // 0xb87ab0: ldur            x1, [fp, #-0x10]
    // 0xb87ab4: r0 = _parts()
    //     0xb87ab4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb87ab8: mov             x2, x0
    // 0xb87abc: LoadField: r0 = r2->field_b
    //     0xb87abc: ldur            w0, [x2, #0xb]
    // 0xb87ac0: r1 = LoadInt32Instr(r0)
    //     0xb87ac0: sbfx            x1, x0, #1, #0x1f
    // 0xb87ac4: mov             x0, x1
    // 0xb87ac8: r1 = 5
    //     0xb87ac8: movz            x1, #0x5
    // 0xb87acc: cmp             x1, x0
    // 0xb87ad0: b.hs            #0xb88928
    // 0xb87ad4: LoadField: r0 = r2->field_23
    //     0xb87ad4: ldur            w0, [x2, #0x23]
    // 0xb87ad8: DecompressPointer r0
    //     0xb87ad8: add             x0, x0, HEAP, lsl #32
    // 0xb87adc: ldur            x1, [fp, #-0x30]
    // 0xb87ae0: ArrayStore: r1[2] = r0  ; List_4
    //     0xb87ae0: add             x25, x1, #0x17
    //     0xb87ae4: str             w0, [x25]
    //     0xb87ae8: tbz             w0, #0, #0xb87b04
    //     0xb87aec: ldurb           w16, [x1, #-1]
    //     0xb87af0: ldurb           w17, [x0, #-1]
    //     0xb87af4: and             x16, x17, x16, lsr #2
    //     0xb87af8: tst             x16, HEAP, lsr #32
    //     0xb87afc: b.eq            #0xb87b04
    //     0xb87b00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87b04: ldur            x0, [fp, #-0x30]
    // 0xb87b08: r16 = " "
    //     0xb87b08: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb87b0c: StoreField: r0->field_1b = r16
    //     0xb87b0c: stur            w16, [x0, #0x1b]
    // 0xb87b10: ldur            x1, [fp, #-0x10]
    // 0xb87b14: r0 = _parts()
    //     0xb87b14: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb87b18: mov             x2, x0
    // 0xb87b1c: LoadField: r0 = r2->field_b
    //     0xb87b1c: ldur            w0, [x2, #0xb]
    // 0xb87b20: r1 = LoadInt32Instr(r0)
    //     0xb87b20: sbfx            x1, x0, #1, #0x1f
    // 0xb87b24: mov             x0, x1
    // 0xb87b28: r1 = 7
    //     0xb87b28: movz            x1, #0x7
    // 0xb87b2c: cmp             x1, x0
    // 0xb87b30: b.hs            #0xb8892c
    // 0xb87b34: LoadField: r0 = r2->field_2b
    //     0xb87b34: ldur            w0, [x2, #0x2b]
    // 0xb87b38: DecompressPointer r0
    //     0xb87b38: add             x0, x0, HEAP, lsl #32
    // 0xb87b3c: r1 = LoadInt32Instr(r0)
    //     0xb87b3c: sbfx            x1, x0, #1, #0x1f
    //     0xb87b40: tbz             w0, #0, #0xb87b48
    //     0xb87b44: ldur            x1, [x0, #7]
    // 0xb87b48: cmp             x1, #6
    // 0xb87b4c: b.gt            #0xb87bc8
    // 0xb87b50: cmp             x1, #3
    // 0xb87b54: b.gt            #0xb87b94
    // 0xb87b58: cmp             x1, #2
    // 0xb87b5c: b.gt            #0xb87b88
    // 0xb87b60: cmp             x1, #1
    // 0xb87b64: b.gt            #0xb87b7c
    // 0xb87b68: cmp             w0, #2
    // 0xb87b6c: b.ne            #0xb87c40
    // 0xb87b70: r0 = "Januari"
    //     0xb87b70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xb87b74: ldr             x0, [x0, #0xb60]
    // 0xb87b78: b               #0xb87c48
    // 0xb87b7c: r0 = "Februari"
    //     0xb87b7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb68] "Februari"
    //     0xb87b80: ldr             x0, [x0, #0xb68]
    // 0xb87b84: b               #0xb87c48
    // 0xb87b88: r0 = "Maret"
    //     0xb87b88: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb70] "Maret"
    //     0xb87b8c: ldr             x0, [x0, #0xb70]
    // 0xb87b90: b               #0xb87c48
    // 0xb87b94: cmp             x1, #5
    // 0xb87b98: b.gt            #0xb87bbc
    // 0xb87b9c: cmp             x1, #4
    // 0xb87ba0: b.gt            #0xb87bb0
    // 0xb87ba4: r0 = "April"
    //     0xb87ba4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb78] "April"
    //     0xb87ba8: ldr             x0, [x0, #0xb78]
    // 0xb87bac: b               #0xb87c48
    // 0xb87bb0: r0 = "Mei"
    //     0xb87bb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb80] "Mei"
    //     0xb87bb4: ldr             x0, [x0, #0xb80]
    // 0xb87bb8: b               #0xb87c48
    // 0xb87bbc: r0 = "Juni"
    //     0xb87bbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb88] "Juni"
    //     0xb87bc0: ldr             x0, [x0, #0xb88]
    // 0xb87bc4: b               #0xb87c48
    // 0xb87bc8: cmp             x1, #9
    // 0xb87bcc: b.gt            #0xb87c04
    // 0xb87bd0: cmp             x1, #8
    // 0xb87bd4: b.gt            #0xb87bf8
    // 0xb87bd8: cmp             x1, #7
    // 0xb87bdc: b.gt            #0xb87bec
    // 0xb87be0: r0 = "Juli"
    //     0xb87be0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb90] "Juli"
    //     0xb87be4: ldr             x0, [x0, #0xb90]
    // 0xb87be8: b               #0xb87c48
    // 0xb87bec: r0 = "Agustus"
    //     0xb87bec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb98] "Agustus"
    //     0xb87bf0: ldr             x0, [x0, #0xb98]
    // 0xb87bf4: b               #0xb87c48
    // 0xb87bf8: r0 = "September"
    //     0xb87bf8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fba0] "September"
    //     0xb87bfc: ldr             x0, [x0, #0xba0]
    // 0xb87c00: b               #0xb87c48
    // 0xb87c04: cmp             x1, #0xb
    // 0xb87c08: b.gt            #0xb87c2c
    // 0xb87c0c: cmp             x1, #0xa
    // 0xb87c10: b.gt            #0xb87c20
    // 0xb87c14: r0 = "Oktober"
    //     0xb87c14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fba8] "Oktober"
    //     0xb87c18: ldr             x0, [x0, #0xba8]
    // 0xb87c1c: b               #0xb87c48
    // 0xb87c20: r0 = "November"
    //     0xb87c20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbb0] "November"
    //     0xb87c24: ldr             x0, [x0, #0xbb0]
    // 0xb87c28: b               #0xb87c48
    // 0xb87c2c: cmp             w0, #0x18
    // 0xb87c30: b.ne            #0xb87c40
    // 0xb87c34: r0 = "Desember"
    //     0xb87c34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbb8] "Desember"
    //     0xb87c38: ldr             x0, [x0, #0xbb8]
    // 0xb87c3c: b               #0xb87c48
    // 0xb87c40: r0 = "Januari"
    //     0xb87c40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xb87c44: ldr             x0, [x0, #0xb60]
    // 0xb87c48: ldur            x3, [fp, #-0x20]
    // 0xb87c4c: ldur            x2, [fp, #-0x30]
    // 0xb87c50: mov             x1, x2
    // 0xb87c54: ArrayStore: r1[4] = r0  ; List_4
    //     0xb87c54: add             x25, x1, #0x1f
    //     0xb87c58: str             w0, [x25]
    //     0xb87c5c: tbz             w0, #0, #0xb87c78
    //     0xb87c60: ldurb           w16, [x1, #-1]
    //     0xb87c64: ldurb           w17, [x0, #-1]
    //     0xb87c68: and             x16, x17, x16, lsr #2
    //     0xb87c6c: tst             x16, HEAP, lsr #32
    //     0xb87c70: b.eq            #0xb87c78
    //     0xb87c74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87c78: r16 = " "
    //     0xb87c78: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb87c7c: StoreField: r2->field_23 = r16
    //     0xb87c7c: stur            w16, [x2, #0x23]
    // 0xb87c80: ldur            x1, [fp, #-0x10]
    // 0xb87c84: r0 = _parts()
    //     0xb87c84: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb87c88: mov             x2, x0
    // 0xb87c8c: LoadField: r0 = r2->field_b
    //     0xb87c8c: ldur            w0, [x2, #0xb]
    // 0xb87c90: r1 = LoadInt32Instr(r0)
    //     0xb87c90: sbfx            x1, x0, #1, #0x1f
    // 0xb87c94: mov             x0, x1
    // 0xb87c98: r1 = 8
    //     0xb87c98: movz            x1, #0x8
    // 0xb87c9c: cmp             x1, x0
    // 0xb87ca0: b.hs            #0xb88930
    // 0xb87ca4: LoadField: r0 = r2->field_2f
    //     0xb87ca4: ldur            w0, [x2, #0x2f]
    // 0xb87ca8: DecompressPointer r0
    //     0xb87ca8: add             x0, x0, HEAP, lsl #32
    // 0xb87cac: ldur            x1, [fp, #-0x30]
    // 0xb87cb0: ArrayStore: r1[6] = r0  ; List_4
    //     0xb87cb0: add             x25, x1, #0x27
    //     0xb87cb4: str             w0, [x25]
    //     0xb87cb8: tbz             w0, #0, #0xb87cd4
    //     0xb87cbc: ldurb           w16, [x1, #-1]
    //     0xb87cc0: ldurb           w17, [x0, #-1]
    //     0xb87cc4: and             x16, x17, x16, lsr #2
    //     0xb87cc8: tst             x16, HEAP, lsr #32
    //     0xb87ccc: b.eq            #0xb87cd4
    //     0xb87cd0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87cd4: ldur            x2, [fp, #-0x30]
    // 0xb87cd8: r16 = " / "
    //     0xb87cd8: add             x16, PP, #9, lsl #12  ; [pp+0x9130] " / "
    //     0xb87cdc: ldr             x16, [x16, #0x130]
    // 0xb87ce0: StoreField: r2->field_2b = r16
    //     0xb87ce0: stur            w16, [x2, #0x2b]
    // 0xb87ce4: ldur            x3, [fp, #-0x20]
    // 0xb87ce8: LoadField: r4 = r3->field_7
    //     0xb87ce8: ldur            x4, [x3, #7]
    // 0xb87cec: r0 = BoxInt64Instr(r4)
    //     0xb87cec: sbfiz           x0, x4, #1, #0x1f
    //     0xb87cf0: cmp             x4, x0, asr #1
    //     0xb87cf4: b.eq            #0xb87d00
    //     0xb87cf8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb87cfc: stur            x4, [x0, #7]
    // 0xb87d00: mov             x1, x2
    // 0xb87d04: ArrayStore: r1[8] = r0  ; List_4
    //     0xb87d04: add             x25, x1, #0x2f
    //     0xb87d08: str             w0, [x25]
    //     0xb87d0c: tbz             w0, #0, #0xb87d28
    //     0xb87d10: ldurb           w16, [x1, #-1]
    //     0xb87d14: ldurb           w17, [x0, #-1]
    //     0xb87d18: and             x16, x17, x16, lsr #2
    //     0xb87d1c: tst             x16, HEAP, lsr #32
    //     0xb87d20: b.eq            #0xb87d28
    //     0xb87d24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87d28: r16 = " "
    //     0xb87d28: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb87d2c: StoreField: r2->field_33 = r16
    //     0xb87d2c: stur            w16, [x2, #0x33]
    // 0xb87d30: LoadField: r0 = r3->field_f
    //     0xb87d30: ldur            w0, [x3, #0xf]
    // 0xb87d34: DecompressPointer r0
    //     0xb87d34: add             x0, x0, HEAP, lsl #32
    // 0xb87d38: r16 = Sentinel
    //     0xb87d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb87d3c: cmp             w0, w16
    // 0xb87d40: b.eq            #0xb88934
    // 0xb87d44: r1 = LoadInt32Instr(r0)
    //     0xb87d44: sbfx            x1, x0, #1, #0x1f
    //     0xb87d48: tbz             w0, #0, #0xb87d50
    //     0xb87d4c: ldur            x1, [x0, #7]
    // 0xb87d50: cmp             x1, #6
    // 0xb87d54: b.gt            #0xb87dd0
    // 0xb87d58: cmp             x1, #3
    // 0xb87d5c: b.gt            #0xb87d9c
    // 0xb87d60: cmp             x1, #2
    // 0xb87d64: b.gt            #0xb87d90
    // 0xb87d68: cmp             x1, #1
    // 0xb87d6c: b.gt            #0xb87d84
    // 0xb87d70: cmp             w0, #2
    // 0xb87d74: b.ne            #0xb87e48
    // 0xb87d78: r0 = "Muharam"
    //     0xb87d78: add             x0, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0xb87d7c: ldr             x0, [x0, #0x210]
    // 0xb87d80: b               #0xb87e50
    // 0xb87d84: r0 = "Safar"
    //     0xb87d84: add             x0, PP, #9, lsl #12  ; [pp+0x9218] "Safar"
    //     0xb87d88: ldr             x0, [x0, #0x218]
    // 0xb87d8c: b               #0xb87e50
    // 0xb87d90: r0 = "Rabiul Awal"
    //     0xb87d90: add             x0, PP, #9, lsl #12  ; [pp+0x9220] "Rabiul Awal"
    //     0xb87d94: ldr             x0, [x0, #0x220]
    // 0xb87d98: b               #0xb87e50
    // 0xb87d9c: cmp             x1, #5
    // 0xb87da0: b.gt            #0xb87dc4
    // 0xb87da4: cmp             x1, #4
    // 0xb87da8: b.gt            #0xb87db8
    // 0xb87dac: r0 = "Rabiul Akhir"
    //     0xb87dac: add             x0, PP, #9, lsl #12  ; [pp+0x9228] "Rabiul Akhir"
    //     0xb87db0: ldr             x0, [x0, #0x228]
    // 0xb87db4: b               #0xb87e50
    // 0xb87db8: r0 = "Jumadal Ula"
    //     0xb87db8: add             x0, PP, #9, lsl #12  ; [pp+0x9230] "Jumadal Ula"
    //     0xb87dbc: ldr             x0, [x0, #0x230]
    // 0xb87dc0: b               #0xb87e50
    // 0xb87dc4: r0 = "Jumadal Akhirah"
    //     0xb87dc4: add             x0, PP, #9, lsl #12  ; [pp+0x9238] "Jumadal Akhirah"
    //     0xb87dc8: ldr             x0, [x0, #0x238]
    // 0xb87dcc: b               #0xb87e50
    // 0xb87dd0: cmp             x1, #9
    // 0xb87dd4: b.gt            #0xb87e0c
    // 0xb87dd8: cmp             x1, #8
    // 0xb87ddc: b.gt            #0xb87e00
    // 0xb87de0: cmp             x1, #7
    // 0xb87de4: b.gt            #0xb87df4
    // 0xb87de8: r0 = "Rajab"
    //     0xb87de8: add             x0, PP, #9, lsl #12  ; [pp+0x9240] "Rajab"
    //     0xb87dec: ldr             x0, [x0, #0x240]
    // 0xb87df0: b               #0xb87e50
    // 0xb87df4: r0 = "Sya\'ban"
    //     0xb87df4: add             x0, PP, #9, lsl #12  ; [pp+0x9248] "Sya\'ban"
    //     0xb87df8: ldr             x0, [x0, #0x248]
    // 0xb87dfc: b               #0xb87e50
    // 0xb87e00: r0 = "Ramadhan"
    //     0xb87e00: add             x0, PP, #9, lsl #12  ; [pp+0x9250] "Ramadhan"
    //     0xb87e04: ldr             x0, [x0, #0x250]
    // 0xb87e08: b               #0xb87e50
    // 0xb87e0c: cmp             x1, #0xb
    // 0xb87e10: b.gt            #0xb87e34
    // 0xb87e14: cmp             x1, #0xa
    // 0xb87e18: b.gt            #0xb87e28
    // 0xb87e1c: r0 = "Syawal"
    //     0xb87e1c: add             x0, PP, #9, lsl #12  ; [pp+0x9258] "Syawal"
    //     0xb87e20: ldr             x0, [x0, #0x258]
    // 0xb87e24: b               #0xb87e50
    // 0xb87e28: r0 = "Dzulqa\'dah"
    //     0xb87e28: add             x0, PP, #9, lsl #12  ; [pp+0x9260] "Dzulqa\'dah"
    //     0xb87e2c: ldr             x0, [x0, #0x260]
    // 0xb87e30: b               #0xb87e50
    // 0xb87e34: cmp             w0, #0x18
    // 0xb87e38: b.ne            #0xb87e48
    // 0xb87e3c: r0 = "Dzulhijjah"
    //     0xb87e3c: add             x0, PP, #9, lsl #12  ; [pp+0x9268] "Dzulhijjah"
    //     0xb87e40: ldr             x0, [x0, #0x268]
    // 0xb87e44: b               #0xb87e50
    // 0xb87e48: r0 = "Muharam"
    //     0xb87e48: add             x0, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0xb87e4c: ldr             x0, [x0, #0x210]
    // 0xb87e50: ldur            x4, [fp, #-8]
    // 0xb87e54: ldur            x5, [fp, #-0x18]
    // 0xb87e58: mov             x1, x2
    // 0xb87e5c: ArrayStore: r1[10] = r0  ; List_4
    //     0xb87e5c: add             x25, x1, #0x37
    //     0xb87e60: str             w0, [x25]
    //     0xb87e64: tbz             w0, #0, #0xb87e80
    //     0xb87e68: ldurb           w16, [x1, #-1]
    //     0xb87e6c: ldurb           w17, [x0, #-1]
    //     0xb87e70: and             x16, x17, x16, lsr #2
    //     0xb87e74: tst             x16, HEAP, lsr #32
    //     0xb87e78: b.eq            #0xb87e80
    //     0xb87e7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87e80: r16 = " "
    //     0xb87e80: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb87e84: StoreField: r2->field_3b = r16
    //     0xb87e84: stur            w16, [x2, #0x3b]
    // 0xb87e88: LoadField: r0 = r3->field_13
    //     0xb87e88: ldur            w0, [x3, #0x13]
    // 0xb87e8c: DecompressPointer r0
    //     0xb87e8c: add             x0, x0, HEAP, lsl #32
    // 0xb87e90: r16 = Sentinel
    //     0xb87e90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb87e94: cmp             w0, w16
    // 0xb87e98: b.eq            #0xb88940
    // 0xb87e9c: mov             x1, x2
    // 0xb87ea0: ArrayStore: r1[12] = r0  ; List_4
    //     0xb87ea0: add             x25, x1, #0x3f
    //     0xb87ea4: str             w0, [x25]
    //     0xb87ea8: tbz             w0, #0, #0xb87ec4
    //     0xb87eac: ldurb           w16, [x1, #-1]
    //     0xb87eb0: ldurb           w17, [x0, #-1]
    //     0xb87eb4: and             x16, x17, x16, lsr #2
    //     0xb87eb8: tst             x16, HEAP, lsr #32
    //     0xb87ebc: b.eq            #0xb87ec4
    //     0xb87ec0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb87ec4: str             x2, [SP]
    // 0xb87ec8: r0 = _interpolate()
    //     0xb87ec8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb87ecc: mov             x4, x0
    // 0xb87ed0: ldur            x3, [fp, #-8]
    // 0xb87ed4: stur            x4, [fp, #-0x38]
    // 0xb87ed8: LoadField: r5 = r3->field_1b
    //     0xb87ed8: ldur            w5, [x3, #0x1b]
    // 0xb87edc: DecompressPointer r5
    //     0xb87edc: add             x5, x5, HEAP, lsl #32
    // 0xb87ee0: stur            x5, [fp, #-0x30]
    // 0xb87ee4: LoadField: r6 = r3->field_13
    //     0xb87ee4: ldur            w6, [x3, #0x13]
    // 0xb87ee8: DecompressPointer r6
    //     0xb87ee8: add             x6, x6, HEAP, lsl #32
    // 0xb87eec: stur            x6, [fp, #-0x28]
    // 0xb87ef0: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xb87ef0: ldur            w7, [x3, #0x17]
    // 0xb87ef4: DecompressPointer r7
    //     0xb87ef4: add             x7, x7, HEAP, lsl #32
    // 0xb87ef8: ldur            x8, [fp, #-0x18]
    // 0xb87efc: stur            x7, [fp, #-0x20]
    // 0xb87f00: r0 = LoadClassIdInstr(r8)
    //     0xb87f00: ldur            x0, [x8, #-1]
    //     0xb87f04: ubfx            x0, x0, #0xc, #0x14
    // 0xb87f08: mov             x1, x8
    // 0xb87f0c: mov             x2, x7
    // 0xb87f10: r0 = GDT[cid_x0 + -0x114]()
    //     0xb87f10: sub             lr, x0, #0x114
    //     0xb87f14: ldr             lr, [x21, lr, lsl #3]
    //     0xb87f18: blr             lr
    // 0xb87f1c: cmp             w0, NULL
    // 0xb87f20: b.eq            #0xb8894c
    // 0xb87f24: r1 = LoadClassIdInstr(r0)
    //     0xb87f24: ldur            x1, [x0, #-1]
    //     0xb87f28: ubfx            x1, x1, #0xc, #0x14
    // 0xb87f2c: mov             x16, x0
    // 0xb87f30: mov             x0, x1
    // 0xb87f34: mov             x1, x16
    // 0xb87f38: r2 = "color"
    //     0xb87f38: ldr             x2, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0xb87f3c: r0 = GDT[cid_x0 + -0x114]()
    //     0xb87f3c: sub             lr, x0, #0x114
    //     0xb87f40: ldr             lr, [x21, lr, lsl #3]
    //     0xb87f44: blr             lr
    // 0xb87f48: mov             x1, x0
    // 0xb87f4c: ldur            x0, [fp, #-0x28]
    // 0xb87f50: tbnz            w0, #4, #0xb87f5c
    // 0xb87f54: mov             x2, x1
    // 0xb87f58: b               #0xb87f60
    // 0xb87f5c: r2 = Null
    //     0xb87f5c: mov             x2, NULL
    // 0xb87f60: ldur            x1, [fp, #-0x18]
    // 0xb87f64: stur            x2, [fp, #-0x40]
    // 0xb87f68: r0 = Radius()
    //     0xb87f68: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb87f6c: d0 = 8.000000
    //     0xb87f6c: fmov            d0, #8.00000000
    // 0xb87f70: stur            x0, [fp, #-0x48]
    // 0xb87f74: StoreField: r0->field_7 = d0
    //     0xb87f74: stur            d0, [x0, #7]
    // 0xb87f78: StoreField: r0->field_f = d0
    //     0xb87f78: stur            d0, [x0, #0xf]
    // 0xb87f7c: r0 = BorderRadius()
    //     0xb87f7c: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb87f80: mov             x3, x0
    // 0xb87f84: ldur            x0, [fp, #-0x48]
    // 0xb87f88: stur            x3, [fp, #-0x50]
    // 0xb87f8c: StoreField: r3->field_7 = r0
    //     0xb87f8c: stur            w0, [x3, #7]
    // 0xb87f90: StoreField: r3->field_b = r0
    //     0xb87f90: stur            w0, [x3, #0xb]
    // 0xb87f94: StoreField: r3->field_f = r0
    //     0xb87f94: stur            w0, [x3, #0xf]
    // 0xb87f98: StoreField: r3->field_13 = r0
    //     0xb87f98: stur            w0, [x3, #0x13]
    // 0xb87f9c: ldur            x4, [fp, #-0x18]
    // 0xb87fa0: r0 = LoadClassIdInstr(r4)
    //     0xb87fa0: ldur            x0, [x4, #-1]
    //     0xb87fa4: ubfx            x0, x0, #0xc, #0x14
    // 0xb87fa8: mov             x1, x4
    // 0xb87fac: ldur            x2, [fp, #-0x20]
    // 0xb87fb0: r0 = GDT[cid_x0 + -0x114]()
    //     0xb87fb0: sub             lr, x0, #0x114
    //     0xb87fb4: ldr             lr, [x21, lr, lsl #3]
    //     0xb87fb8: blr             lr
    // 0xb87fbc: cmp             w0, NULL
    // 0xb87fc0: b.eq            #0xb88950
    // 0xb87fc4: r1 = LoadClassIdInstr(r0)
    //     0xb87fc4: ldur            x1, [x0, #-1]
    //     0xb87fc8: ubfx            x1, x1, #0xc, #0x14
    // 0xb87fcc: mov             x16, x0
    // 0xb87fd0: mov             x0, x1
    // 0xb87fd4: mov             x1, x16
    // 0xb87fd8: r2 = "borderColor"
    //     0xb87fd8: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb87fdc: ldr             x2, [x2, #0xf28]
    // 0xb87fe0: r0 = GDT[cid_x0 + -0x114]()
    //     0xb87fe0: sub             lr, x0, #0x114
    //     0xb87fe4: ldr             lr, [x21, lr, lsl #3]
    //     0xb87fe8: blr             lr
    // 0xb87fec: stur            x0, [fp, #-0x48]
    // 0xb87ff0: cmp             w0, NULL
    // 0xb87ff4: b.eq            #0xb88954
    // 0xb87ff8: r1 = _ConstMap len:6
    //     0xb87ff8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb87ffc: ldr             x1, [x1, #0xc20]
    // 0xb88000: r2 = 2
    //     0xb88000: movz            x2, #0x2
    // 0xb88004: r0 = []()
    //     0xb88004: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb88008: stur            x0, [fp, #-0x58]
    // 0xb8800c: cmp             w0, NULL
    // 0xb88010: b.eq            #0xb88958
    // 0xb88014: r1 = _ConstMap len:6
    //     0xb88014: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb88018: ldr             x1, [x1, #0xc20]
    // 0xb8801c: r2 = 8
    //     0xb8801c: movz            x2, #0x8
    // 0xb88020: r0 = []()
    //     0xb88020: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb88024: cmp             w0, NULL
    // 0xb88028: b.eq            #0xb8895c
    // 0xb8802c: r16 = <Color>
    //     0xb8802c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb88030: ldr             x16, [x16, #0x158]
    // 0xb88034: stp             x0, x16, [SP, #8]
    // 0xb88038: ldur            x16, [fp, #-0x58]
    // 0xb8803c: str             x16, [SP]
    // 0xb88040: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb88040: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb88044: r0 = mode()
    //     0xb88044: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb88048: mov             x1, x0
    // 0xb8804c: ldur            x0, [fp, #-0x28]
    // 0xb88050: tbnz            w0, #4, #0xb88058
    // 0xb88054: ldur            x1, [fp, #-0x48]
    // 0xb88058: ldur            x0, [fp, #-0x50]
    // 0xb8805c: ldur            x2, [fp, #-0x40]
    // 0xb88060: str             x1, [SP]
    // 0xb88064: r1 = Null
    //     0xb88064: mov             x1, NULL
    // 0xb88068: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb88068: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb8806c: ldr             x4, [x4, #0x228]
    // 0xb88070: r0 = Border.all()
    //     0xb88070: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb88074: stur            x0, [fp, #-0x28]
    // 0xb88078: r0 = BoxDecoration()
    //     0xb88078: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8807c: mov             x2, x0
    // 0xb88080: ldur            x0, [fp, #-0x40]
    // 0xb88084: stur            x2, [fp, #-0x48]
    // 0xb88088: StoreField: r2->field_7 = r0
    //     0xb88088: stur            w0, [x2, #7]
    // 0xb8808c: ldur            x0, [fp, #-0x28]
    // 0xb88090: StoreField: r2->field_f = r0
    //     0xb88090: stur            w0, [x2, #0xf]
    // 0xb88094: ldur            x0, [fp, #-0x50]
    // 0xb88098: StoreField: r2->field_13 = r0
    //     0xb88098: stur            w0, [x2, #0x13]
    // 0xb8809c: r0 = Instance_BoxShape
    //     0xb8809c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb880a0: ldr             x0, [x0, #0xca8]
    // 0xb880a4: StoreField: r2->field_23 = r0
    //     0xb880a4: stur            w0, [x2, #0x23]
    // 0xb880a8: ldur            x1, [fp, #-0x10]
    // 0xb880ac: r0 = _parts()
    //     0xb880ac: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb880b0: mov             x2, x0
    // 0xb880b4: LoadField: r0 = r2->field_b
    //     0xb880b4: ldur            w0, [x2, #0xb]
    // 0xb880b8: r1 = LoadInt32Instr(r0)
    //     0xb880b8: sbfx            x1, x0, #1, #0x1f
    // 0xb880bc: mov             x0, x1
    // 0xb880c0: r1 = 7
    //     0xb880c0: movz            x1, #0x7
    // 0xb880c4: cmp             x1, x0
    // 0xb880c8: b.hs            #0xb88960
    // 0xb880cc: LoadField: r0 = r2->field_2b
    //     0xb880cc: ldur            w0, [x2, #0x2b]
    // 0xb880d0: DecompressPointer r0
    //     0xb880d0: add             x0, x0, HEAP, lsl #32
    // 0xb880d4: r1 = LoadInt32Instr(r0)
    //     0xb880d4: sbfx            x1, x0, #1, #0x1f
    //     0xb880d8: tbz             w0, #0, #0xb880e0
    //     0xb880dc: ldur            x1, [x0, #7]
    // 0xb880e0: cmp             x1, #6
    // 0xb880e4: b.gt            #0xb88160
    // 0xb880e8: cmp             x1, #3
    // 0xb880ec: b.gt            #0xb8812c
    // 0xb880f0: cmp             x1, #2
    // 0xb880f4: b.gt            #0xb88120
    // 0xb880f8: cmp             x1, #1
    // 0xb880fc: b.gt            #0xb88114
    // 0xb88100: cmp             w0, #2
    // 0xb88104: b.ne            #0xb881d8
    // 0xb88108: r1 = "Januari"
    //     0xb88108: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xb8810c: ldr             x1, [x1, #0xb60]
    // 0xb88110: b               #0xb881e0
    // 0xb88114: r1 = "Februari"
    //     0xb88114: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb68] "Februari"
    //     0xb88118: ldr             x1, [x1, #0xb68]
    // 0xb8811c: b               #0xb881e0
    // 0xb88120: r1 = "Maret"
    //     0xb88120: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] "Maret"
    //     0xb88124: ldr             x1, [x1, #0xb70]
    // 0xb88128: b               #0xb881e0
    // 0xb8812c: cmp             x1, #5
    // 0xb88130: b.gt            #0xb88154
    // 0xb88134: cmp             x1, #4
    // 0xb88138: b.gt            #0xb88148
    // 0xb8813c: r1 = "April"
    //     0xb8813c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb78] "April"
    //     0xb88140: ldr             x1, [x1, #0xb78]
    // 0xb88144: b               #0xb881e0
    // 0xb88148: r1 = "Mei"
    //     0xb88148: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb80] "Mei"
    //     0xb8814c: ldr             x1, [x1, #0xb80]
    // 0xb88150: b               #0xb881e0
    // 0xb88154: r1 = "Juni"
    //     0xb88154: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb88] "Juni"
    //     0xb88158: ldr             x1, [x1, #0xb88]
    // 0xb8815c: b               #0xb881e0
    // 0xb88160: cmp             x1, #9
    // 0xb88164: b.gt            #0xb8819c
    // 0xb88168: cmp             x1, #8
    // 0xb8816c: b.gt            #0xb88190
    // 0xb88170: cmp             x1, #7
    // 0xb88174: b.gt            #0xb88184
    // 0xb88178: r1 = "Juli"
    //     0xb88178: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb90] "Juli"
    //     0xb8817c: ldr             x1, [x1, #0xb90]
    // 0xb88180: b               #0xb881e0
    // 0xb88184: r1 = "Agustus"
    //     0xb88184: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb98] "Agustus"
    //     0xb88188: ldr             x1, [x1, #0xb98]
    // 0xb8818c: b               #0xb881e0
    // 0xb88190: r1 = "September"
    //     0xb88190: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fba0] "September"
    //     0xb88194: ldr             x1, [x1, #0xba0]
    // 0xb88198: b               #0xb881e0
    // 0xb8819c: cmp             x1, #0xb
    // 0xb881a0: b.gt            #0xb881c4
    // 0xb881a4: cmp             x1, #0xa
    // 0xb881a8: b.gt            #0xb881b8
    // 0xb881ac: r1 = "Oktober"
    //     0xb881ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fba8] "Oktober"
    //     0xb881b0: ldr             x1, [x1, #0xba8]
    // 0xb881b4: b               #0xb881e0
    // 0xb881b8: r1 = "November"
    //     0xb881b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbb0] "November"
    //     0xb881bc: ldr             x1, [x1, #0xbb0]
    // 0xb881c0: b               #0xb881e0
    // 0xb881c4: cmp             w0, #0x18
    // 0xb881c8: b.ne            #0xb881d8
    // 0xb881cc: r1 = "Desember"
    //     0xb881cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbb8] "Desember"
    //     0xb881d0: ldr             x1, [x1, #0xbb8]
    // 0xb881d4: b               #0xb881e0
    // 0xb881d8: r1 = "Januari"
    //     0xb881d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xb881dc: ldr             x1, [x1, #0xb60]
    // 0xb881e0: r16 = 6
    //     0xb881e0: movz            x16, #0x6
    // 0xb881e4: str             x16, [SP]
    // 0xb881e8: r2 = 0
    //     0xb881e8: movz            x2, #0
    // 0xb881ec: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb881ec: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb881f0: r0 = substring()
    //     0xb881f0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xb881f4: stur            x0, [fp, #-0x28]
    // 0xb881f8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb881f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb881fc: ldr             x0, [x0, #0x2670]
    //     0xb88200: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb88204: cmp             w0, w16
    //     0xb88208: b.ne            #0xb88214
    //     0xb8820c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb88210: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb88214: r0 = GetNavigation.textTheme()
    //     0xb88214: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb88218: LoadField: r1 = r0->field_27
    //     0xb88218: ldur            w1, [x0, #0x27]
    // 0xb8821c: DecompressPointer r1
    //     0xb8821c: add             x1, x1, HEAP, lsl #32
    // 0xb88220: stur            x1, [fp, #-0x40]
    // 0xb88224: r0 = Text()
    //     0xb88224: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb88228: mov             x2, x0
    // 0xb8822c: ldur            x0, [fp, #-0x28]
    // 0xb88230: stur            x2, [fp, #-0x50]
    // 0xb88234: StoreField: r2->field_b = r0
    //     0xb88234: stur            w0, [x2, #0xb]
    // 0xb88238: ldur            x0, [fp, #-0x40]
    // 0xb8823c: StoreField: r2->field_13 = r0
    //     0xb8823c: stur            w0, [x2, #0x13]
    // 0xb88240: r0 = Instance__LinearTextScaler
    //     0xb88240: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb88244: StoreField: r2->field_33 = r0
    //     0xb88244: stur            w0, [x2, #0x33]
    // 0xb88248: ldur            x1, [fp, #-0x10]
    // 0xb8824c: r0 = _parts()
    //     0xb8824c: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb88250: mov             x2, x0
    // 0xb88254: LoadField: r0 = r2->field_b
    //     0xb88254: ldur            w0, [x2, #0xb]
    // 0xb88258: r1 = LoadInt32Instr(r0)
    //     0xb88258: sbfx            x1, x0, #1, #0x1f
    // 0xb8825c: mov             x0, x1
    // 0xb88260: r1 = 5
    //     0xb88260: movz            x1, #0x5
    // 0xb88264: cmp             x1, x0
    // 0xb88268: b.hs            #0xb88964
    // 0xb8826c: LoadField: r0 = r2->field_23
    //     0xb8826c: ldur            w0, [x2, #0x23]
    // 0xb88270: DecompressPointer r0
    //     0xb88270: add             x0, x0, HEAP, lsl #32
    // 0xb88274: r1 = 60
    //     0xb88274: movz            x1, #0x3c
    // 0xb88278: branchIfSmi(r0, 0xb88284)
    //     0xb88278: tbz             w0, #0, #0xb88284
    // 0xb8827c: r1 = LoadClassIdInstr(r0)
    //     0xb8827c: ldur            x1, [x0, #-1]
    //     0xb88280: ubfx            x1, x1, #0xc, #0x14
    // 0xb88284: str             x0, [SP]
    // 0xb88288: mov             x0, x1
    // 0xb8828c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb8828c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb88290: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb88290: movz            x17, #0x2b03
    //     0xb88294: add             lr, x0, x17
    //     0xb88298: ldr             lr, [x21, lr, lsl #3]
    //     0xb8829c: blr             lr
    // 0xb882a0: stur            x0, [fp, #-0x10]
    // 0xb882a4: r0 = GetNavigation.textTheme()
    //     0xb882a4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb882a8: LoadField: r3 = r0->field_f
    //     0xb882a8: ldur            w3, [x0, #0xf]
    // 0xb882ac: DecompressPointer r3
    //     0xb882ac: add             x3, x3, HEAP, lsl #32
    // 0xb882b0: stur            x3, [fp, #-0x28]
    // 0xb882b4: cmp             w3, NULL
    // 0xb882b8: b.ne            #0xb882c4
    // 0xb882bc: r3 = Null
    //     0xb882bc: mov             x3, NULL
    // 0xb882c0: b               #0xb88338
    // 0xb882c4: ldur            x1, [fp, #-0x18]
    // 0xb882c8: r0 = LoadClassIdInstr(r1)
    //     0xb882c8: ldur            x0, [x1, #-1]
    //     0xb882cc: ubfx            x0, x0, #0xc, #0x14
    // 0xb882d0: ldur            x2, [fp, #-0x20]
    // 0xb882d4: r0 = GDT[cid_x0 + -0x114]()
    //     0xb882d4: sub             lr, x0, #0x114
    //     0xb882d8: ldr             lr, [x21, lr, lsl #3]
    //     0xb882dc: blr             lr
    // 0xb882e0: cmp             w0, NULL
    // 0xb882e4: b.ne            #0xb882f0
    // 0xb882e8: r0 = Null
    //     0xb882e8: mov             x0, NULL
    // 0xb882ec: b               #0xb88318
    // 0xb882f0: r1 = LoadClassIdInstr(r0)
    //     0xb882f0: ldur            x1, [x0, #-1]
    //     0xb882f4: ubfx            x1, x1, #0xc, #0x14
    // 0xb882f8: mov             x16, x0
    // 0xb882fc: mov             x0, x1
    // 0xb88300: mov             x1, x16
    // 0xb88304: r2 = "borderColor"
    //     0xb88304: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "borderColor"
    //     0xb88308: ldr             x2, [x2, #0xf28]
    // 0xb8830c: r0 = GDT[cid_x0 + -0x114]()
    //     0xb8830c: sub             lr, x0, #0x114
    //     0xb88310: ldr             lr, [x21, lr, lsl #3]
    //     0xb88314: blr             lr
    // 0xb88318: r16 = 16.000000
    //     0xb88318: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb8831c: ldr             x16, [x16, #0x80]
    // 0xb88320: stp             x0, x16, [SP]
    // 0xb88324: ldur            x1, [fp, #-0x28]
    // 0xb88328: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb88328: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8832c: ldr             x4, [x4, #0xaa0]
    // 0xb88330: r0 = copyWith()
    //     0xb88330: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb88334: mov             x3, x0
    // 0xb88338: ldur            x2, [fp, #-8]
    // 0xb8833c: ldur            x1, [fp, #-0x50]
    // 0xb88340: ldur            x0, [fp, #-0x10]
    // 0xb88344: stur            x3, [fp, #-0x18]
    // 0xb88348: r0 = Text()
    //     0xb88348: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8834c: mov             x3, x0
    // 0xb88350: ldur            x0, [fp, #-0x10]
    // 0xb88354: stur            x3, [fp, #-0x20]
    // 0xb88358: StoreField: r3->field_b = r0
    //     0xb88358: stur            w0, [x3, #0xb]
    // 0xb8835c: ldur            x0, [fp, #-0x18]
    // 0xb88360: StoreField: r3->field_13 = r0
    //     0xb88360: stur            w0, [x3, #0x13]
    // 0xb88364: r0 = Instance_TextAlign
    //     0xb88364: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xb88368: StoreField: r3->field_1b = r0
    //     0xb88368: stur            w0, [x3, #0x1b]
    // 0xb8836c: r0 = Instance__LinearTextScaler
    //     0xb8836c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb88370: StoreField: r3->field_33 = r0
    //     0xb88370: stur            w0, [x3, #0x33]
    // 0xb88374: r1 = Null
    //     0xb88374: mov             x1, NULL
    // 0xb88378: r2 = 4
    //     0xb88378: movz            x2, #0x4
    // 0xb8837c: r0 = AllocateArray()
    //     0xb8837c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb88380: mov             x2, x0
    // 0xb88384: ldur            x0, [fp, #-0x50]
    // 0xb88388: stur            x2, [fp, #-0x10]
    // 0xb8838c: StoreField: r2->field_f = r0
    //     0xb8838c: stur            w0, [x2, #0xf]
    // 0xb88390: ldur            x0, [fp, #-0x20]
    // 0xb88394: StoreField: r2->field_13 = r0
    //     0xb88394: stur            w0, [x2, #0x13]
    // 0xb88398: r1 = <Widget>
    //     0xb88398: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8839c: r0 = AllocateGrowableArray()
    //     0xb8839c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb883a0: mov             x1, x0
    // 0xb883a4: ldur            x0, [fp, #-0x10]
    // 0xb883a8: stur            x1, [fp, #-0x18]
    // 0xb883ac: StoreField: r1->field_f = r0
    //     0xb883ac: stur            w0, [x1, #0xf]
    // 0xb883b0: r2 = 4
    //     0xb883b0: movz            x2, #0x4
    // 0xb883b4: StoreField: r1->field_b = r2
    //     0xb883b4: stur            w2, [x1, #0xb]
    // 0xb883b8: r0 = Column()
    //     0xb883b8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb883bc: mov             x1, x0
    // 0xb883c0: r0 = Instance_Axis
    //     0xb883c0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb883c4: stur            x1, [fp, #-0x10]
    // 0xb883c8: StoreField: r1->field_f = r0
    //     0xb883c8: stur            w0, [x1, #0xf]
    // 0xb883cc: r2 = Instance_MainAxisAlignment
    //     0xb883cc: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb883d0: ldr             x2, [x2, #0x290]
    // 0xb883d4: StoreField: r1->field_13 = r2
    //     0xb883d4: stur            w2, [x1, #0x13]
    // 0xb883d8: r3 = Instance_MainAxisSize
    //     0xb883d8: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb883dc: ldr             x3, [x3, #0x738]
    // 0xb883e0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb883e0: stur            w3, [x1, #0x17]
    // 0xb883e4: r4 = Instance_CrossAxisAlignment
    //     0xb883e4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb883e8: ldr             x4, [x4, #0x740]
    // 0xb883ec: StoreField: r1->field_1b = r4
    //     0xb883ec: stur            w4, [x1, #0x1b]
    // 0xb883f0: r5 = Instance_VerticalDirection
    //     0xb883f0: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb883f4: ldr             x5, [x5, #0x748]
    // 0xb883f8: StoreField: r1->field_23 = r5
    //     0xb883f8: stur            w5, [x1, #0x23]
    // 0xb883fc: r6 = Instance_Clip
    //     0xb883fc: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb88400: ldr             x6, [x6, #0x750]
    // 0xb88404: StoreField: r1->field_2b = r6
    //     0xb88404: stur            w6, [x1, #0x2b]
    // 0xb88408: StoreField: r1->field_2f = rZR
    //     0xb88408: stur            xzr, [x1, #0x2f]
    // 0xb8840c: ldur            x7, [fp, #-0x18]
    // 0xb88410: StoreField: r1->field_b = r7
    //     0xb88410: stur            w7, [x1, #0xb]
    // 0xb88414: r0 = Container()
    //     0xb88414: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb88418: stur            x0, [fp, #-0x18]
    // 0xb8841c: r16 = 40.000000
    //     0xb8841c: add             x16, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xb88420: ldr             x16, [x16, #0x1b8]
    // 0xb88424: r30 = 46.000000
    //     0xb88424: add             lr, PP, #0x35, lsl #12  ; [pp+0x35f88] 46
    //     0xb88428: ldr             lr, [lr, #0xf88]
    // 0xb8842c: stp             lr, x16, [SP, #0x10]
    // 0xb88430: ldur            x16, [fp, #-0x48]
    // 0xb88434: ldur            lr, [fp, #-0x10]
    // 0xb88438: stp             lr, x16, [SP]
    // 0xb8843c: mov             x1, x0
    // 0xb88440: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb88440: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f90] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb88444: ldr             x4, [x4, #0xf90]
    // 0xb88448: r0 = Container()
    //     0xb88448: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8844c: r0 = Padding()
    //     0xb8844c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb88450: mov             x1, x0
    // 0xb88454: r0 = Instance_EdgeInsets
    //     0xb88454: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!EdgeInsets@e13421
    //     0xb88458: ldr             x0, [x0, #0xf98]
    // 0xb8845c: stur            x1, [fp, #-0x20]
    // 0xb88460: StoreField: r1->field_f = r0
    //     0xb88460: stur            w0, [x1, #0xf]
    // 0xb88464: ldur            x0, [fp, #-0x18]
    // 0xb88468: StoreField: r1->field_b = r0
    //     0xb88468: stur            w0, [x1, #0xb]
    // 0xb8846c: ldur            x0, [fp, #-8]
    // 0xb88470: LoadField: r2 = r0->field_f
    //     0xb88470: ldur            w2, [x0, #0xf]
    // 0xb88474: DecompressPointer r2
    //     0xb88474: add             x2, x2, HEAP, lsl #32
    // 0xb88478: stur            x2, [fp, #-0x10]
    // 0xb8847c: r0 = GetNavigation.textTheme()
    //     0xb8847c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb88480: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb88480: ldur            w3, [x0, #0x17]
    // 0xb88484: DecompressPointer r3
    //     0xb88484: add             x3, x3, HEAP, lsl #32
    // 0xb88488: stur            x3, [fp, #-8]
    // 0xb8848c: cmp             w3, NULL
    // 0xb88490: b.ne            #0xb8849c
    // 0xb88494: r3 = Null
    //     0xb88494: mov             x3, NULL
    // 0xb88498: b               #0xb884fc
    // 0xb8849c: r1 = _ConstMap len:3
    //     0xb8849c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb884a0: ldr             x1, [x1, #0xbe8]
    // 0xb884a4: r2 = 2
    //     0xb884a4: movz            x2, #0x2
    // 0xb884a8: r0 = []()
    //     0xb884a8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb884ac: r1 = _ConstMap len:6
    //     0xb884ac: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb884b0: ldr             x1, [x1, #0xc20]
    // 0xb884b4: r2 = 2
    //     0xb884b4: movz            x2, #0x2
    // 0xb884b8: stur            x0, [fp, #-0x18]
    // 0xb884bc: r0 = []()
    //     0xb884bc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb884c0: r16 = <Color?>
    //     0xb884c0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb884c4: ldr             x16, [x16, #0x98]
    // 0xb884c8: stp             x0, x16, [SP, #8]
    // 0xb884cc: ldur            x16, [fp, #-0x18]
    // 0xb884d0: str             x16, [SP]
    // 0xb884d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb884d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb884d8: r0 = mode()
    //     0xb884d8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb884dc: r16 = 15.000000
    //     0xb884dc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb988] 15
    //     0xb884e0: ldr             x16, [x16, #0x988]
    // 0xb884e4: stp             x0, x16, [SP]
    // 0xb884e8: ldur            x1, [fp, #-8]
    // 0xb884ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb884ec: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb884f0: ldr             x4, [x4, #0xaa0]
    // 0xb884f4: r0 = copyWith()
    //     0xb884f4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb884f8: mov             x3, x0
    // 0xb884fc: ldur            x1, [fp, #-0x38]
    // 0xb88500: ldur            x2, [fp, #-0x30]
    // 0xb88504: ldur            x0, [fp, #-0x10]
    // 0xb88508: stur            x3, [fp, #-8]
    // 0xb8850c: r0 = Text()
    //     0xb8850c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb88510: mov             x1, x0
    // 0xb88514: ldur            x0, [fp, #-0x10]
    // 0xb88518: stur            x1, [fp, #-0x18]
    // 0xb8851c: StoreField: r1->field_b = r0
    //     0xb8851c: stur            w0, [x1, #0xb]
    // 0xb88520: ldur            x0, [fp, #-8]
    // 0xb88524: StoreField: r1->field_13 = r0
    //     0xb88524: stur            w0, [x1, #0x13]
    // 0xb88528: r0 = Instance__LinearTextScaler
    //     0xb88528: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb8852c: StoreField: r1->field_33 = r0
    //     0xb8852c: stur            w0, [x1, #0x33]
    // 0xb88530: r0 = GetNavigation.textTheme()
    //     0xb88530: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb88534: LoadField: r1 = r0->field_27
    //     0xb88534: ldur            w1, [x0, #0x27]
    // 0xb88538: DecompressPointer r1
    //     0xb88538: add             x1, x1, HEAP, lsl #32
    // 0xb8853c: stur            x1, [fp, #-8]
    // 0xb88540: r0 = Text()
    //     0xb88540: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb88544: mov             x3, x0
    // 0xb88548: ldur            x0, [fp, #-0x38]
    // 0xb8854c: stur            x3, [fp, #-0x10]
    // 0xb88550: StoreField: r3->field_b = r0
    //     0xb88550: stur            w0, [x3, #0xb]
    // 0xb88554: ldur            x0, [fp, #-8]
    // 0xb88558: StoreField: r3->field_13 = r0
    //     0xb88558: stur            w0, [x3, #0x13]
    // 0xb8855c: r0 = Instance__LinearTextScaler
    //     0xb8855c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb88560: StoreField: r3->field_33 = r0
    //     0xb88560: stur            w0, [x3, #0x33]
    // 0xb88564: r1 = Null
    //     0xb88564: mov             x1, NULL
    // 0xb88568: r2 = 6
    //     0xb88568: movz            x2, #0x6
    // 0xb8856c: r0 = AllocateArray()
    //     0xb8856c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb88570: mov             x2, x0
    // 0xb88574: ldur            x0, [fp, #-0x18]
    // 0xb88578: stur            x2, [fp, #-8]
    // 0xb8857c: StoreField: r2->field_f = r0
    //     0xb8857c: stur            w0, [x2, #0xf]
    // 0xb88580: r16 = Instance_SizedBox
    //     0xb88580: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb88584: ldr             x16, [x16, #0x4a0]
    // 0xb88588: StoreField: r2->field_13 = r16
    //     0xb88588: stur            w16, [x2, #0x13]
    // 0xb8858c: ldur            x0, [fp, #-0x10]
    // 0xb88590: ArrayStore: r2[0] = r0  ; List_4
    //     0xb88590: stur            w0, [x2, #0x17]
    // 0xb88594: r1 = <Widget>
    //     0xb88594: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb88598: r0 = AllocateGrowableArray()
    //     0xb88598: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8859c: mov             x1, x0
    // 0xb885a0: ldur            x0, [fp, #-8]
    // 0xb885a4: stur            x1, [fp, #-0x10]
    // 0xb885a8: StoreField: r1->field_f = r0
    //     0xb885a8: stur            w0, [x1, #0xf]
    // 0xb885ac: r2 = 6
    //     0xb885ac: movz            x2, #0x6
    // 0xb885b0: StoreField: r1->field_b = r2
    //     0xb885b0: stur            w2, [x1, #0xb]
    // 0xb885b4: r0 = Column()
    //     0xb885b4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb885b8: mov             x2, x0
    // 0xb885bc: r0 = Instance_Axis
    //     0xb885bc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb885c0: stur            x2, [fp, #-8]
    // 0xb885c4: StoreField: r2->field_f = r0
    //     0xb885c4: stur            w0, [x2, #0xf]
    // 0xb885c8: r0 = Instance_MainAxisAlignment
    //     0xb885c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb885cc: ldr             x0, [x0, #0x290]
    // 0xb885d0: StoreField: r2->field_13 = r0
    //     0xb885d0: stur            w0, [x2, #0x13]
    // 0xb885d4: r0 = Instance_MainAxisSize
    //     0xb885d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb885d8: ldr             x0, [x0, #0x738]
    // 0xb885dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb885dc: stur            w0, [x2, #0x17]
    // 0xb885e0: r3 = Instance_CrossAxisAlignment
    //     0xb885e0: add             x3, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb885e4: ldr             x3, [x3, #0x68]
    // 0xb885e8: StoreField: r2->field_1b = r3
    //     0xb885e8: stur            w3, [x2, #0x1b]
    // 0xb885ec: r4 = Instance_VerticalDirection
    //     0xb885ec: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb885f0: ldr             x4, [x4, #0x748]
    // 0xb885f4: StoreField: r2->field_23 = r4
    //     0xb885f4: stur            w4, [x2, #0x23]
    // 0xb885f8: r5 = Instance_Clip
    //     0xb885f8: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb885fc: ldr             x5, [x5, #0x750]
    // 0xb88600: StoreField: r2->field_2b = r5
    //     0xb88600: stur            w5, [x2, #0x2b]
    // 0xb88604: StoreField: r2->field_2f = rZR
    //     0xb88604: stur            xzr, [x2, #0x2f]
    // 0xb88608: ldur            x1, [fp, #-0x10]
    // 0xb8860c: StoreField: r2->field_b = r1
    //     0xb8860c: stur            w1, [x2, #0xb]
    // 0xb88610: r1 = <FlexParentData>
    //     0xb88610: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb88614: ldr             x1, [x1, #0x720]
    // 0xb88618: r0 = Expanded()
    //     0xb88618: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb8861c: mov             x3, x0
    // 0xb88620: r0 = 1
    //     0xb88620: movz            x0, #0x1
    // 0xb88624: stur            x3, [fp, #-0x10]
    // 0xb88628: StoreField: r3->field_13 = r0
    //     0xb88628: stur            x0, [x3, #0x13]
    // 0xb8862c: r4 = Instance_FlexFit
    //     0xb8862c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb88630: ldr             x4, [x4, #0x728]
    // 0xb88634: StoreField: r3->field_1b = r4
    //     0xb88634: stur            w4, [x3, #0x1b]
    // 0xb88638: ldur            x1, [fp, #-8]
    // 0xb8863c: StoreField: r3->field_b = r1
    //     0xb8863c: stur            w1, [x3, #0xb]
    // 0xb88640: r1 = Null
    //     0xb88640: mov             x1, NULL
    // 0xb88644: r2 = 2
    //     0xb88644: movz            x2, #0x2
    // 0xb88648: r0 = AllocateArray()
    //     0xb88648: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8864c: mov             x2, x0
    // 0xb88650: ldur            x0, [fp, #-0x10]
    // 0xb88654: stur            x2, [fp, #-8]
    // 0xb88658: StoreField: r2->field_f = r0
    //     0xb88658: stur            w0, [x2, #0xf]
    // 0xb8865c: r1 = <Widget>
    //     0xb8865c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb88660: r0 = AllocateGrowableArray()
    //     0xb88660: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb88664: mov             x3, x0
    // 0xb88668: ldur            x0, [fp, #-8]
    // 0xb8866c: stur            x3, [fp, #-0x10]
    // 0xb88670: StoreField: r3->field_f = r0
    //     0xb88670: stur            w0, [x3, #0xf]
    // 0xb88674: r0 = 2
    //     0xb88674: movz            x0, #0x2
    // 0xb88678: StoreField: r3->field_b = r0
    //     0xb88678: stur            w0, [x3, #0xb]
    // 0xb8867c: ldur            x0, [fp, #-0x30]
    // 0xb88680: cmp             w0, NULL
    // 0xb88684: b.eq            #0xb88744
    // 0xb88688: r1 = _ConstMap len:6
    //     0xb88688: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb8868c: ldr             x1, [x1, #0xc20]
    // 0xb88690: r2 = 6
    //     0xb88690: movz            x2, #0x6
    // 0xb88694: r0 = []()
    //     0xb88694: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb88698: stur            x0, [fp, #-8]
    // 0xb8869c: r0 = Icon()
    //     0xb8869c: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb886a0: mov             x2, x0
    // 0xb886a4: r0 = Instance_IconData
    //     0xb886a4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xb886a8: ldr             x0, [x0, #0x590]
    // 0xb886ac: stur            x2, [fp, #-0x18]
    // 0xb886b0: StoreField: r2->field_b = r0
    //     0xb886b0: stur            w0, [x2, #0xb]
    // 0xb886b4: r0 = 16.000000
    //     0xb886b4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb886b8: ldr             x0, [x0, #0x80]
    // 0xb886bc: StoreField: r2->field_f = r0
    //     0xb886bc: stur            w0, [x2, #0xf]
    // 0xb886c0: ldur            x0, [fp, #-8]
    // 0xb886c4: StoreField: r2->field_23 = r0
    //     0xb886c4: stur            w0, [x2, #0x23]
    // 0xb886c8: ldur            x0, [fp, #-0x10]
    // 0xb886cc: LoadField: r1 = r0->field_b
    //     0xb886cc: ldur            w1, [x0, #0xb]
    // 0xb886d0: LoadField: r3 = r0->field_f
    //     0xb886d0: ldur            w3, [x0, #0xf]
    // 0xb886d4: DecompressPointer r3
    //     0xb886d4: add             x3, x3, HEAP, lsl #32
    // 0xb886d8: LoadField: r4 = r3->field_b
    //     0xb886d8: ldur            w4, [x3, #0xb]
    // 0xb886dc: r3 = LoadInt32Instr(r1)
    //     0xb886dc: sbfx            x3, x1, #1, #0x1f
    // 0xb886e0: stur            x3, [fp, #-0x60]
    // 0xb886e4: r1 = LoadInt32Instr(r4)
    //     0xb886e4: sbfx            x1, x4, #1, #0x1f
    // 0xb886e8: cmp             x3, x1
    // 0xb886ec: b.ne            #0xb886f8
    // 0xb886f0: mov             x1, x0
    // 0xb886f4: r0 = _growToNextCapacity()
    //     0xb886f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb886f8: ldur            x2, [fp, #-0x10]
    // 0xb886fc: ldur            x3, [fp, #-0x60]
    // 0xb88700: add             x0, x3, #1
    // 0xb88704: lsl             x1, x0, #1
    // 0xb88708: StoreField: r2->field_b = r1
    //     0xb88708: stur            w1, [x2, #0xb]
    // 0xb8870c: LoadField: r1 = r2->field_f
    //     0xb8870c: ldur            w1, [x2, #0xf]
    // 0xb88710: DecompressPointer r1
    //     0xb88710: add             x1, x1, HEAP, lsl #32
    // 0xb88714: ldur            x0, [fp, #-0x18]
    // 0xb88718: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb88718: add             x25, x1, x3, lsl #2
    //     0xb8871c: add             x25, x25, #0xf
    //     0xb88720: str             w0, [x25]
    //     0xb88724: tbz             w0, #0, #0xb88740
    //     0xb88728: ldurb           w16, [x1, #-1]
    //     0xb8872c: ldurb           w17, [x0, #-1]
    //     0xb88730: and             x16, x17, x16, lsr #2
    //     0xb88734: tst             x16, HEAP, lsr #32
    //     0xb88738: b.eq            #0xb88740
    //     0xb8873c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb88740: b               #0xb88748
    // 0xb88744: mov             x2, x3
    // 0xb88748: ldur            x0, [fp, #-0x30]
    // 0xb8874c: ldur            x1, [fp, #-0x20]
    // 0xb88750: r0 = Row()
    //     0xb88750: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb88754: mov             x1, x0
    // 0xb88758: r0 = Instance_Axis
    //     0xb88758: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8875c: stur            x1, [fp, #-8]
    // 0xb88760: StoreField: r1->field_f = r0
    //     0xb88760: stur            w0, [x1, #0xf]
    // 0xb88764: r2 = Instance_MainAxisAlignment
    //     0xb88764: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb88768: ldr             x2, [x2, #0x730]
    // 0xb8876c: StoreField: r1->field_13 = r2
    //     0xb8876c: stur            w2, [x1, #0x13]
    // 0xb88770: r3 = Instance_MainAxisSize
    //     0xb88770: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb88774: ldr             x3, [x3, #0x738]
    // 0xb88778: ArrayStore: r1[0] = r3  ; List_4
    //     0xb88778: stur            w3, [x1, #0x17]
    // 0xb8877c: r4 = Instance_CrossAxisAlignment
    //     0xb8877c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb88780: ldr             x4, [x4, #0x740]
    // 0xb88784: StoreField: r1->field_1b = r4
    //     0xb88784: stur            w4, [x1, #0x1b]
    // 0xb88788: r4 = Instance_VerticalDirection
    //     0xb88788: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8878c: ldr             x4, [x4, #0x748]
    // 0xb88790: StoreField: r1->field_23 = r4
    //     0xb88790: stur            w4, [x1, #0x23]
    // 0xb88794: r5 = Instance_Clip
    //     0xb88794: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb88798: ldr             x5, [x5, #0x750]
    // 0xb8879c: StoreField: r1->field_2b = r5
    //     0xb8879c: stur            w5, [x1, #0x2b]
    // 0xb887a0: StoreField: r1->field_2f = rZR
    //     0xb887a0: stur            xzr, [x1, #0x2f]
    // 0xb887a4: ldur            x6, [fp, #-0x10]
    // 0xb887a8: StoreField: r1->field_b = r6
    //     0xb887a8: stur            w6, [x1, #0xb]
    // 0xb887ac: r0 = Padding()
    //     0xb887ac: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb887b0: mov             x2, x0
    // 0xb887b4: r0 = Instance_EdgeInsets
    //     0xb887b4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35fa0] Obj!EdgeInsets@e133f1
    //     0xb887b8: ldr             x0, [x0, #0xfa0]
    // 0xb887bc: stur            x2, [fp, #-0x10]
    // 0xb887c0: StoreField: r2->field_f = r0
    //     0xb887c0: stur            w0, [x2, #0xf]
    // 0xb887c4: ldur            x0, [fp, #-8]
    // 0xb887c8: StoreField: r2->field_b = r0
    //     0xb887c8: stur            w0, [x2, #0xb]
    // 0xb887cc: r1 = <FlexParentData>
    //     0xb887cc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb887d0: ldr             x1, [x1, #0x720]
    // 0xb887d4: r0 = Expanded()
    //     0xb887d4: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb887d8: mov             x3, x0
    // 0xb887dc: r0 = 1
    //     0xb887dc: movz            x0, #0x1
    // 0xb887e0: stur            x3, [fp, #-8]
    // 0xb887e4: StoreField: r3->field_13 = r0
    //     0xb887e4: stur            x0, [x3, #0x13]
    // 0xb887e8: r0 = Instance_FlexFit
    //     0xb887e8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb887ec: ldr             x0, [x0, #0x728]
    // 0xb887f0: StoreField: r3->field_1b = r0
    //     0xb887f0: stur            w0, [x3, #0x1b]
    // 0xb887f4: ldur            x0, [fp, #-0x10]
    // 0xb887f8: StoreField: r3->field_b = r0
    //     0xb887f8: stur            w0, [x3, #0xb]
    // 0xb887fc: r1 = Null
    //     0xb887fc: mov             x1, NULL
    // 0xb88800: r2 = 4
    //     0xb88800: movz            x2, #0x4
    // 0xb88804: r0 = AllocateArray()
    //     0xb88804: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb88808: mov             x2, x0
    // 0xb8880c: ldur            x0, [fp, #-0x20]
    // 0xb88810: stur            x2, [fp, #-0x10]
    // 0xb88814: StoreField: r2->field_f = r0
    //     0xb88814: stur            w0, [x2, #0xf]
    // 0xb88818: ldur            x0, [fp, #-8]
    // 0xb8881c: StoreField: r2->field_13 = r0
    //     0xb8881c: stur            w0, [x2, #0x13]
    // 0xb88820: r1 = <Widget>
    //     0xb88820: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb88824: r0 = AllocateGrowableArray()
    //     0xb88824: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb88828: mov             x1, x0
    // 0xb8882c: ldur            x0, [fp, #-0x10]
    // 0xb88830: stur            x1, [fp, #-8]
    // 0xb88834: StoreField: r1->field_f = r0
    //     0xb88834: stur            w0, [x1, #0xf]
    // 0xb88838: r0 = 4
    //     0xb88838: movz            x0, #0x4
    // 0xb8883c: StoreField: r1->field_b = r0
    //     0xb8883c: stur            w0, [x1, #0xb]
    // 0xb88840: r0 = Row()
    //     0xb88840: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb88844: mov             x1, x0
    // 0xb88848: r0 = Instance_Axis
    //     0xb88848: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8884c: stur            x1, [fp, #-0x10]
    // 0xb88850: StoreField: r1->field_f = r0
    //     0xb88850: stur            w0, [x1, #0xf]
    // 0xb88854: r0 = Instance_MainAxisAlignment
    //     0xb88854: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb88858: ldr             x0, [x0, #0x730]
    // 0xb8885c: StoreField: r1->field_13 = r0
    //     0xb8885c: stur            w0, [x1, #0x13]
    // 0xb88860: r0 = Instance_MainAxisSize
    //     0xb88860: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb88864: ldr             x0, [x0, #0x738]
    // 0xb88868: ArrayStore: r1[0] = r0  ; List_4
    //     0xb88868: stur            w0, [x1, #0x17]
    // 0xb8886c: r0 = Instance_CrossAxisAlignment
    //     0xb8886c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb88870: ldr             x0, [x0, #0x68]
    // 0xb88874: StoreField: r1->field_1b = r0
    //     0xb88874: stur            w0, [x1, #0x1b]
    // 0xb88878: r0 = Instance_VerticalDirection
    //     0xb88878: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8887c: ldr             x0, [x0, #0x748]
    // 0xb88880: StoreField: r1->field_23 = r0
    //     0xb88880: stur            w0, [x1, #0x23]
    // 0xb88884: r0 = Instance_Clip
    //     0xb88884: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb88888: ldr             x0, [x0, #0x750]
    // 0xb8888c: StoreField: r1->field_2b = r0
    //     0xb8888c: stur            w0, [x1, #0x2b]
    // 0xb88890: StoreField: r1->field_2f = rZR
    //     0xb88890: stur            xzr, [x1, #0x2f]
    // 0xb88894: ldur            x0, [fp, #-8]
    // 0xb88898: StoreField: r1->field_b = r0
    //     0xb88898: stur            w0, [x1, #0xb]
    // 0xb8889c: r0 = SizedBox()
    //     0xb8889c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb888a0: mov             x1, x0
    // 0xb888a4: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb888a4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb888a8: ldr             x0, [x0, #0xc58]
    // 0xb888ac: stur            x1, [fp, #-8]
    // 0xb888b0: StoreField: r1->field_f = r0
    //     0xb888b0: stur            w0, [x1, #0xf]
    // 0xb888b4: ldur            x0, [fp, #-0x10]
    // 0xb888b8: StoreField: r1->field_b = r0
    //     0xb888b8: stur            w0, [x1, #0xb]
    // 0xb888bc: r0 = InkWell()
    //     0xb888bc: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb888c0: ldur            x1, [fp, #-8]
    // 0xb888c4: StoreField: r0->field_b = r1
    //     0xb888c4: stur            w1, [x0, #0xb]
    // 0xb888c8: ldur            x1, [fp, #-0x30]
    // 0xb888cc: StoreField: r0->field_f = r1
    //     0xb888cc: stur            w1, [x0, #0xf]
    // 0xb888d0: r1 = true
    //     0xb888d0: add             x1, NULL, #0x20  ; true
    // 0xb888d4: StoreField: r0->field_43 = r1
    //     0xb888d4: stur            w1, [x0, #0x43]
    // 0xb888d8: r2 = Instance_BoxShape
    //     0xb888d8: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb888dc: ldr             x2, [x2, #0xca8]
    // 0xb888e0: StoreField: r0->field_47 = r2
    //     0xb888e0: stur            w2, [x0, #0x47]
    // 0xb888e4: StoreField: r0->field_6f = r1
    //     0xb888e4: stur            w1, [x0, #0x6f]
    // 0xb888e8: r2 = false
    //     0xb888e8: add             x2, NULL, #0x30  ; false
    // 0xb888ec: StoreField: r0->field_73 = r2
    //     0xb888ec: stur            w2, [x0, #0x73]
    // 0xb888f0: StoreField: r0->field_83 = r1
    //     0xb888f0: stur            w1, [x0, #0x83]
    // 0xb888f4: StoreField: r0->field_7b = r2
    //     0xb888f4: stur            w2, [x0, #0x7b]
    // 0xb888f8: LeaveFrame
    //     0xb888f8: mov             SP, fp
    //     0xb888fc: ldp             fp, lr, [SP], #0x10
    // 0xb88900: ret
    //     0xb88900: ret             
    // 0xb88904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb88904: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88908: b               #0xb874b0
    // 0xb8890c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8890c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88910: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88914: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88914: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88918: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8891c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8891c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88920: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88920: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88924: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88924: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88928: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88928: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8892c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8892c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88930: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88930: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88934: r9 = hMonth
    //     0xb88934: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0xb88938: ldr             x9, [x9, #0x278]
    // 0xb8893c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb8893c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb88940: r9 = hYear
    //     0xb88940: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0xb88944: ldr             x9, [x9, #0x270]
    // 0xb88948: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb88948: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb8894c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8894c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88950: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88950: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88954: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88954: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88958: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8895c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8895c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88960: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88960: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88964: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88964: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
