// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_header.dart

// class id: 1050164, size: 0x8
class :: {
}

// class id: 5055, size: 0x1c, field offset: 0xc
//   const constructor, 
class NCalendarHeader extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb88a88, size: 0x3cc
    // 0xb88a88: EnterFrame
    //     0xb88a88: stp             fp, lr, [SP, #-0x10]!
    //     0xb88a8c: mov             fp, SP
    // 0xb88a90: AllocStack(0x38)
    //     0xb88a90: sub             SP, SP, #0x38
    // 0xb88a94: SetupParameters(NCalendarHeader this /* r1 => r1, fp-0x10 */)
    //     0xb88a94: stur            x1, [fp, #-0x10]
    // 0xb88a98: CheckStackOverflow
    //     0xb88a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88a9c: cmp             SP, x16
    //     0xb88aa0: b.ls            #0xb88e48
    // 0xb88aa4: LoadField: r0 = r1->field_13
    //     0xb88aa4: ldur            w0, [x1, #0x13]
    // 0xb88aa8: DecompressPointer r0
    //     0xb88aa8: add             x0, x0, HEAP, lsl #32
    // 0xb88aac: stur            x0, [fp, #-8]
    // 0xb88ab0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb88ab0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb88ab4: ldr             x0, [x0, #0x2670]
    //     0xb88ab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb88abc: cmp             w0, w16
    //     0xb88ac0: b.ne            #0xb88acc
    //     0xb88ac4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb88ac8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb88acc: r0 = GetNavigation.theme()
    //     0xb88acc: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb88ad0: LoadField: r1 = r0->field_3f
    //     0xb88ad0: ldur            w1, [x0, #0x3f]
    // 0xb88ad4: DecompressPointer r1
    //     0xb88ad4: add             x1, x1, HEAP, lsl #32
    // 0xb88ad8: LoadField: r0 = r1->field_2b
    //     0xb88ad8: ldur            w0, [x1, #0x2b]
    // 0xb88adc: DecompressPointer r0
    //     0xb88adc: add             x0, x0, HEAP, lsl #32
    // 0xb88ae0: stur            x0, [fp, #-0x18]
    // 0xb88ae4: r0 = Icon()
    //     0xb88ae4: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb88ae8: mov             x1, x0
    // 0xb88aec: r0 = Instance_IconData
    //     0xb88aec: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f00] Obj!IconData@e10871
    //     0xb88af0: ldr             x0, [x0, #0xf00]
    // 0xb88af4: stur            x1, [fp, #-0x20]
    // 0xb88af8: StoreField: r1->field_b = r0
    //     0xb88af8: stur            w0, [x1, #0xb]
    // 0xb88afc: r0 = 20.000000
    //     0xb88afc: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xb88b00: ldr             x0, [x0, #0x430]
    // 0xb88b04: StoreField: r1->field_f = r0
    //     0xb88b04: stur            w0, [x1, #0xf]
    // 0xb88b08: ldur            x2, [fp, #-0x18]
    // 0xb88b0c: StoreField: r1->field_23 = r2
    //     0xb88b0c: stur            w2, [x1, #0x23]
    // 0xb88b10: r0 = IconButton()
    //     0xb88b10: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb88b14: mov             x1, x0
    // 0xb88b18: r0 = Instance_EdgeInsets
    //     0xb88b18: add             x0, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb88b1c: ldr             x0, [x0, #0x360]
    // 0xb88b20: stur            x1, [fp, #-0x18]
    // 0xb88b24: StoreField: r1->field_13 = r0
    //     0xb88b24: stur            w0, [x1, #0x13]
    // 0xb88b28: ldur            x2, [fp, #-8]
    // 0xb88b2c: StoreField: r1->field_3b = r2
    //     0xb88b2c: stur            w2, [x1, #0x3b]
    // 0xb88b30: r2 = false
    //     0xb88b30: add             x2, NULL, #0x30  ; false
    // 0xb88b34: StoreField: r1->field_47 = r2
    //     0xb88b34: stur            w2, [x1, #0x47]
    // 0xb88b38: ldur            x3, [fp, #-0x20]
    // 0xb88b3c: StoreField: r1->field_1f = r3
    //     0xb88b3c: stur            w3, [x1, #0x1f]
    // 0xb88b40: r3 = Instance__IconButtonVariant
    //     0xb88b40: add             x3, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb88b44: ldr             x3, [x3, #0xf78]
    // 0xb88b48: StoreField: r1->field_63 = r3
    //     0xb88b48: stur            w3, [x1, #0x63]
    // 0xb88b4c: ldur            x4, [fp, #-0x10]
    // 0xb88b50: LoadField: r5 = r4->field_b
    //     0xb88b50: ldur            w5, [x4, #0xb]
    // 0xb88b54: DecompressPointer r5
    //     0xb88b54: add             x5, x5, HEAP, lsl #32
    // 0xb88b58: stur            x5, [fp, #-8]
    // 0xb88b5c: r0 = GetNavigation.textTheme()
    //     0xb88b5c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb88b60: LoadField: r1 = r0->field_f
    //     0xb88b60: ldur            w1, [x0, #0xf]
    // 0xb88b64: DecompressPointer r1
    //     0xb88b64: add             x1, x1, HEAP, lsl #32
    // 0xb88b68: cmp             w1, NULL
    // 0xb88b6c: b.ne            #0xb88b78
    // 0xb88b70: r3 = Null
    //     0xb88b70: mov             x3, NULL
    // 0xb88b74: b               #0xb88b94
    // 0xb88b78: r16 = 15.000000
    //     0xb88b78: add             x16, PP, #0xb, lsl #12  ; [pp+0xb988] 15
    //     0xb88b7c: ldr             x16, [x16, #0x988]
    // 0xb88b80: str             x16, [SP]
    // 0xb88b84: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb88b84: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb88b88: ldr             x4, [x4, #0x88]
    // 0xb88b8c: r0 = copyWith()
    //     0xb88b8c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb88b90: mov             x3, x0
    // 0xb88b94: ldur            x1, [fp, #-0x10]
    // 0xb88b98: ldur            x0, [fp, #-0x18]
    // 0xb88b9c: ldur            x2, [fp, #-8]
    // 0xb88ba0: stur            x3, [fp, #-0x20]
    // 0xb88ba4: r0 = Text()
    //     0xb88ba4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb88ba8: mov             x1, x0
    // 0xb88bac: ldur            x0, [fp, #-8]
    // 0xb88bb0: stur            x1, [fp, #-0x28]
    // 0xb88bb4: StoreField: r1->field_b = r0
    //     0xb88bb4: stur            w0, [x1, #0xb]
    // 0xb88bb8: ldur            x0, [fp, #-0x20]
    // 0xb88bbc: StoreField: r1->field_13 = r0
    //     0xb88bbc: stur            w0, [x1, #0x13]
    // 0xb88bc0: r0 = Instance__LinearTextScaler
    //     0xb88bc0: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb88bc4: StoreField: r1->field_33 = r0
    //     0xb88bc4: stur            w0, [x1, #0x33]
    // 0xb88bc8: ldur            x2, [fp, #-0x10]
    // 0xb88bcc: LoadField: r3 = r2->field_f
    //     0xb88bcc: ldur            w3, [x2, #0xf]
    // 0xb88bd0: DecompressPointer r3
    //     0xb88bd0: add             x3, x3, HEAP, lsl #32
    // 0xb88bd4: stur            x3, [fp, #-8]
    // 0xb88bd8: r0 = GetNavigation.textTheme()
    //     0xb88bd8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb88bdc: LoadField: r1 = r0->field_33
    //     0xb88bdc: ldur            w1, [x0, #0x33]
    // 0xb88be0: DecompressPointer r1
    //     0xb88be0: add             x1, x1, HEAP, lsl #32
    // 0xb88be4: cmp             w1, NULL
    // 0xb88be8: b.eq            #0xb88e50
    // 0xb88bec: r16 = 0.000000
    //     0xb88bec: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb88bf0: str             x16, [SP]
    // 0xb88bf4: r4 = const [0, 0x2, 0x1, 0x1, letterSpacing, 0x1, null]
    //     0xb88bf4: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d380] List(7) [0, 0x2, 0x1, 0x1, "letterSpacing", 0x1, Null]
    //     0xb88bf8: ldr             x4, [x4, #0x380]
    // 0xb88bfc: r0 = copyWith()
    //     0xb88bfc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb88c00: stur            x0, [fp, #-0x20]
    // 0xb88c04: r0 = Text()
    //     0xb88c04: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb88c08: mov             x3, x0
    // 0xb88c0c: ldur            x0, [fp, #-8]
    // 0xb88c10: stur            x3, [fp, #-0x30]
    // 0xb88c14: StoreField: r3->field_b = r0
    //     0xb88c14: stur            w0, [x3, #0xb]
    // 0xb88c18: ldur            x0, [fp, #-0x20]
    // 0xb88c1c: StoreField: r3->field_13 = r0
    //     0xb88c1c: stur            w0, [x3, #0x13]
    // 0xb88c20: r0 = Instance_TextOverflow
    //     0xb88c20: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f08] Obj!TextOverflow@e35ce1
    //     0xb88c24: ldr             x0, [x0, #0xf08]
    // 0xb88c28: StoreField: r3->field_2b = r0
    //     0xb88c28: stur            w0, [x3, #0x2b]
    // 0xb88c2c: r0 = Instance__LinearTextScaler
    //     0xb88c2c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb88c30: StoreField: r3->field_33 = r0
    //     0xb88c30: stur            w0, [x3, #0x33]
    // 0xb88c34: r1 = Null
    //     0xb88c34: mov             x1, NULL
    // 0xb88c38: r2 = 6
    //     0xb88c38: movz            x2, #0x6
    // 0xb88c3c: r0 = AllocateArray()
    //     0xb88c3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb88c40: mov             x2, x0
    // 0xb88c44: ldur            x0, [fp, #-0x28]
    // 0xb88c48: stur            x2, [fp, #-8]
    // 0xb88c4c: StoreField: r2->field_f = r0
    //     0xb88c4c: stur            w0, [x2, #0xf]
    // 0xb88c50: r16 = Instance_SizedBox
    //     0xb88c50: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb88c54: ldr             x16, [x16, #0x7a8]
    // 0xb88c58: StoreField: r2->field_13 = r16
    //     0xb88c58: stur            w16, [x2, #0x13]
    // 0xb88c5c: ldur            x0, [fp, #-0x30]
    // 0xb88c60: ArrayStore: r2[0] = r0  ; List_4
    //     0xb88c60: stur            w0, [x2, #0x17]
    // 0xb88c64: r1 = <Widget>
    //     0xb88c64: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb88c68: r0 = AllocateGrowableArray()
    //     0xb88c68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb88c6c: mov             x1, x0
    // 0xb88c70: ldur            x0, [fp, #-8]
    // 0xb88c74: stur            x1, [fp, #-0x20]
    // 0xb88c78: StoreField: r1->field_f = r0
    //     0xb88c78: stur            w0, [x1, #0xf]
    // 0xb88c7c: r2 = 6
    //     0xb88c7c: movz            x2, #0x6
    // 0xb88c80: StoreField: r1->field_b = r2
    //     0xb88c80: stur            w2, [x1, #0xb]
    // 0xb88c84: r0 = Column()
    //     0xb88c84: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb88c88: mov             x1, x0
    // 0xb88c8c: r0 = Instance_Axis
    //     0xb88c8c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb88c90: stur            x1, [fp, #-0x28]
    // 0xb88c94: StoreField: r1->field_f = r0
    //     0xb88c94: stur            w0, [x1, #0xf]
    // 0xb88c98: r0 = Instance_MainAxisAlignment
    //     0xb88c98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb88c9c: ldr             x0, [x0, #0x730]
    // 0xb88ca0: StoreField: r1->field_13 = r0
    //     0xb88ca0: stur            w0, [x1, #0x13]
    // 0xb88ca4: r0 = Instance_MainAxisSize
    //     0xb88ca4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb88ca8: ldr             x0, [x0, #0xe88]
    // 0xb88cac: ArrayStore: r1[0] = r0  ; List_4
    //     0xb88cac: stur            w0, [x1, #0x17]
    // 0xb88cb0: r0 = Instance_CrossAxisAlignment
    //     0xb88cb0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb88cb4: ldr             x0, [x0, #0x740]
    // 0xb88cb8: StoreField: r1->field_1b = r0
    //     0xb88cb8: stur            w0, [x1, #0x1b]
    // 0xb88cbc: r2 = Instance_VerticalDirection
    //     0xb88cbc: add             x2, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb88cc0: ldr             x2, [x2, #0x748]
    // 0xb88cc4: StoreField: r1->field_23 = r2
    //     0xb88cc4: stur            w2, [x1, #0x23]
    // 0xb88cc8: r3 = Instance_Clip
    //     0xb88cc8: add             x3, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb88ccc: ldr             x3, [x3, #0x750]
    // 0xb88cd0: StoreField: r1->field_2b = r3
    //     0xb88cd0: stur            w3, [x1, #0x2b]
    // 0xb88cd4: StoreField: r1->field_2f = rZR
    //     0xb88cd4: stur            xzr, [x1, #0x2f]
    // 0xb88cd8: ldur            x4, [fp, #-0x20]
    // 0xb88cdc: StoreField: r1->field_b = r4
    //     0xb88cdc: stur            w4, [x1, #0xb]
    // 0xb88ce0: ldur            x4, [fp, #-0x10]
    // 0xb88ce4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xb88ce4: ldur            w5, [x4, #0x17]
    // 0xb88ce8: DecompressPointer r5
    //     0xb88ce8: add             x5, x5, HEAP, lsl #32
    // 0xb88cec: stur            x5, [fp, #-8]
    // 0xb88cf0: r0 = GetNavigation.theme()
    //     0xb88cf0: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb88cf4: LoadField: r1 = r0->field_3f
    //     0xb88cf4: ldur            w1, [x0, #0x3f]
    // 0xb88cf8: DecompressPointer r1
    //     0xb88cf8: add             x1, x1, HEAP, lsl #32
    // 0xb88cfc: LoadField: r0 = r1->field_2b
    //     0xb88cfc: ldur            w0, [x1, #0x2b]
    // 0xb88d00: DecompressPointer r0
    //     0xb88d00: add             x0, x0, HEAP, lsl #32
    // 0xb88d04: stur            x0, [fp, #-0x10]
    // 0xb88d08: r0 = Icon()
    //     0xb88d08: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb88d0c: mov             x1, x0
    // 0xb88d10: r0 = Instance_IconData
    //     0xb88d10: add             x0, PP, #0x29, lsl #12  ; [pp+0x29fa8] Obj!IconData@e0fe71
    //     0xb88d14: ldr             x0, [x0, #0xfa8]
    // 0xb88d18: stur            x1, [fp, #-0x20]
    // 0xb88d1c: StoreField: r1->field_b = r0
    //     0xb88d1c: stur            w0, [x1, #0xb]
    // 0xb88d20: r0 = 20.000000
    //     0xb88d20: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xb88d24: ldr             x0, [x0, #0x430]
    // 0xb88d28: StoreField: r1->field_f = r0
    //     0xb88d28: stur            w0, [x1, #0xf]
    // 0xb88d2c: ldur            x0, [fp, #-0x10]
    // 0xb88d30: StoreField: r1->field_23 = r0
    //     0xb88d30: stur            w0, [x1, #0x23]
    // 0xb88d34: r0 = IconButton()
    //     0xb88d34: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb88d38: mov             x3, x0
    // 0xb88d3c: r0 = Instance_EdgeInsets
    //     0xb88d3c: add             x0, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb88d40: ldr             x0, [x0, #0x360]
    // 0xb88d44: stur            x3, [fp, #-0x10]
    // 0xb88d48: StoreField: r3->field_13 = r0
    //     0xb88d48: stur            w0, [x3, #0x13]
    // 0xb88d4c: ldur            x0, [fp, #-8]
    // 0xb88d50: StoreField: r3->field_3b = r0
    //     0xb88d50: stur            w0, [x3, #0x3b]
    // 0xb88d54: r0 = false
    //     0xb88d54: add             x0, NULL, #0x30  ; false
    // 0xb88d58: StoreField: r3->field_47 = r0
    //     0xb88d58: stur            w0, [x3, #0x47]
    // 0xb88d5c: ldur            x0, [fp, #-0x20]
    // 0xb88d60: StoreField: r3->field_1f = r0
    //     0xb88d60: stur            w0, [x3, #0x1f]
    // 0xb88d64: r0 = Instance__IconButtonVariant
    //     0xb88d64: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb88d68: ldr             x0, [x0, #0xf78]
    // 0xb88d6c: StoreField: r3->field_63 = r0
    //     0xb88d6c: stur            w0, [x3, #0x63]
    // 0xb88d70: r1 = Null
    //     0xb88d70: mov             x1, NULL
    // 0xb88d74: r2 = 6
    //     0xb88d74: movz            x2, #0x6
    // 0xb88d78: r0 = AllocateArray()
    //     0xb88d78: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb88d7c: mov             x2, x0
    // 0xb88d80: ldur            x0, [fp, #-0x18]
    // 0xb88d84: stur            x2, [fp, #-8]
    // 0xb88d88: StoreField: r2->field_f = r0
    //     0xb88d88: stur            w0, [x2, #0xf]
    // 0xb88d8c: ldur            x0, [fp, #-0x28]
    // 0xb88d90: StoreField: r2->field_13 = r0
    //     0xb88d90: stur            w0, [x2, #0x13]
    // 0xb88d94: ldur            x0, [fp, #-0x10]
    // 0xb88d98: ArrayStore: r2[0] = r0  ; List_4
    //     0xb88d98: stur            w0, [x2, #0x17]
    // 0xb88d9c: r1 = <Widget>
    //     0xb88d9c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb88da0: r0 = AllocateGrowableArray()
    //     0xb88da0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb88da4: mov             x1, x0
    // 0xb88da8: ldur            x0, [fp, #-8]
    // 0xb88dac: stur            x1, [fp, #-0x10]
    // 0xb88db0: StoreField: r1->field_f = r0
    //     0xb88db0: stur            w0, [x1, #0xf]
    // 0xb88db4: r0 = 6
    //     0xb88db4: movz            x0, #0x6
    // 0xb88db8: StoreField: r1->field_b = r0
    //     0xb88db8: stur            w0, [x1, #0xb]
    // 0xb88dbc: r0 = Row()
    //     0xb88dbc: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb88dc0: mov             x1, x0
    // 0xb88dc4: r0 = Instance_Axis
    //     0xb88dc4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb88dc8: stur            x1, [fp, #-8]
    // 0xb88dcc: StoreField: r1->field_f = r0
    //     0xb88dcc: stur            w0, [x1, #0xf]
    // 0xb88dd0: r0 = Instance_MainAxisAlignment
    //     0xb88dd0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb88dd4: ldr             x0, [x0, #0xae8]
    // 0xb88dd8: StoreField: r1->field_13 = r0
    //     0xb88dd8: stur            w0, [x1, #0x13]
    // 0xb88ddc: r0 = Instance_MainAxisSize
    //     0xb88ddc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb88de0: ldr             x0, [x0, #0x738]
    // 0xb88de4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb88de4: stur            w0, [x1, #0x17]
    // 0xb88de8: r0 = Instance_CrossAxisAlignment
    //     0xb88de8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb88dec: ldr             x0, [x0, #0x740]
    // 0xb88df0: StoreField: r1->field_1b = r0
    //     0xb88df0: stur            w0, [x1, #0x1b]
    // 0xb88df4: r0 = Instance_VerticalDirection
    //     0xb88df4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb88df8: ldr             x0, [x0, #0x748]
    // 0xb88dfc: StoreField: r1->field_23 = r0
    //     0xb88dfc: stur            w0, [x1, #0x23]
    // 0xb88e00: r0 = Instance_Clip
    //     0xb88e00: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb88e04: ldr             x0, [x0, #0x750]
    // 0xb88e08: StoreField: r1->field_2b = r0
    //     0xb88e08: stur            w0, [x1, #0x2b]
    // 0xb88e0c: StoreField: r1->field_2f = rZR
    //     0xb88e0c: stur            xzr, [x1, #0x2f]
    // 0xb88e10: ldur            x0, [fp, #-0x10]
    // 0xb88e14: StoreField: r1->field_b = r0
    //     0xb88e14: stur            w0, [x1, #0xb]
    // 0xb88e18: r0 = SizedBox()
    //     0xb88e18: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb88e1c: r1 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb88e1c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb88e20: ldr             x1, [x1, #0xc58]
    // 0xb88e24: StoreField: r0->field_f = r1
    //     0xb88e24: stur            w1, [x0, #0xf]
    // 0xb88e28: r1 = 66.000000
    //     0xb88e28: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f10] 66
    //     0xb88e2c: ldr             x1, [x1, #0xf10]
    // 0xb88e30: StoreField: r0->field_13 = r1
    //     0xb88e30: stur            w1, [x0, #0x13]
    // 0xb88e34: ldur            x1, [fp, #-8]
    // 0xb88e38: StoreField: r0->field_b = r1
    //     0xb88e38: stur            w1, [x0, #0xb]
    // 0xb88e3c: LeaveFrame
    //     0xb88e3c: mov             SP, fp
    //     0xb88e40: ldp             fp, lr, [SP], #0x10
    // 0xb88e44: ret
    //     0xb88e44: ret             
    // 0xb88e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb88e48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88e4c: b               #0xb88aa4
    // 0xb88e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88e50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
