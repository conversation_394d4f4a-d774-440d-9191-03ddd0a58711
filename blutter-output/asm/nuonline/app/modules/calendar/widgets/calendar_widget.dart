// lib: , url: package:nuonline/app/modules/calendar/widgets/calendar_widget.dart

// class id: 1050165, size: 0x8
class :: {
}

// class id: 5054, size: 0x3c, field offset: 0xc
//   const constructor, 
class NCalendar extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb88e54, size: 0x1b4
    // 0xb88e54: EnterFrame
    //     0xb88e54: stp             fp, lr, [SP, #-0x10]!
    //     0xb88e58: mov             fp, SP
    // 0xb88e5c: AllocStack(0x70)
    //     0xb88e5c: sub             SP, SP, #0x70
    // 0xb88e60: SetupParameters(NCalendar this /* r1 => r1, fp-0x8 */)
    //     0xb88e60: stur            x1, [fp, #-8]
    // 0xb88e64: CheckStackOverflow
    //     0xb88e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88e68: cmp             SP, x16
    //     0xb88e6c: b.ls            #0xb89000
    // 0xb88e70: r1 = 1
    //     0xb88e70: movz            x1, #0x1
    // 0xb88e74: r0 = AllocateContext()
    //     0xb88e74: bl              #0xec126c  ; AllocateContextStub
    // 0xb88e78: mov             x3, x0
    // 0xb88e7c: ldur            x0, [fp, #-8]
    // 0xb88e80: stur            x3, [fp, #-0x40]
    // 0xb88e84: StoreField: r3->field_f = r0
    //     0xb88e84: stur            w0, [x3, #0xf]
    // 0xb88e88: LoadField: r4 = r0->field_b
    //     0xb88e88: ldur            w4, [x0, #0xb]
    // 0xb88e8c: DecompressPointer r4
    //     0xb88e8c: add             x4, x4, HEAP, lsl #32
    // 0xb88e90: stur            x4, [fp, #-0x38]
    // 0xb88e94: LoadField: r7 = r0->field_f
    //     0xb88e94: ldur            w7, [x0, #0xf]
    // 0xb88e98: DecompressPointer r7
    //     0xb88e98: add             x7, x7, HEAP, lsl #32
    // 0xb88e9c: stur            x7, [fp, #-0x30]
    // 0xb88ea0: LoadField: r5 = r0->field_13
    //     0xb88ea0: ldur            w5, [x0, #0x13]
    // 0xb88ea4: DecompressPointer r5
    //     0xb88ea4: add             x5, x5, HEAP, lsl #32
    // 0xb88ea8: stur            x5, [fp, #-0x28]
    // 0xb88eac: LoadField: r6 = r0->field_1b
    //     0xb88eac: ldur            w6, [x0, #0x1b]
    // 0xb88eb0: DecompressPointer r6
    //     0xb88eb0: add             x6, x6, HEAP, lsl #32
    // 0xb88eb4: stur            x6, [fp, #-0x20]
    // 0xb88eb8: LoadField: r8 = r0->field_1f
    //     0xb88eb8: ldur            w8, [x0, #0x1f]
    // 0xb88ebc: DecompressPointer r8
    //     0xb88ebc: add             x8, x8, HEAP, lsl #32
    // 0xb88ec0: stur            x8, [fp, #-0x18]
    // 0xb88ec4: LoadField: r9 = r0->field_23
    //     0xb88ec4: ldur            w9, [x0, #0x23]
    // 0xb88ec8: DecompressPointer r9
    //     0xb88ec8: add             x9, x9, HEAP, lsl #32
    // 0xb88ecc: mov             x2, x3
    // 0xb88ed0: stur            x9, [fp, #-0x10]
    // 0xb88ed4: r1 = Function '<anonymous closure>':.
    //     0xb88ed4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e60] AnonymousClosure: (0xb899cc), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88ed8: ldr             x1, [x1, #0xe60]
    // 0xb88edc: r0 = AllocateClosure()
    //     0xb88edc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88ee0: r1 = Null
    //     0xb88ee0: mov             x1, NULL
    // 0xb88ee4: stur            x0, [fp, #-8]
    // 0xb88ee8: r0 = CalendarBuilders()
    //     0xb88ee8: bl              #0xb892f0  ; AllocateCalendarBuildersStub -> CalendarBuilders<X0> (size=0x4c)
    // 0xb88eec: mov             x3, x0
    // 0xb88ef0: ldur            x0, [fp, #-8]
    // 0xb88ef4: stur            x3, [fp, #-0x48]
    // 0xb88ef8: StoreField: r3->field_f = r0
    //     0xb88ef8: stur            w0, [x3, #0xf]
    // 0xb88efc: ldur            x2, [fp, #-0x40]
    // 0xb88f00: r1 = Function '<anonymous closure>':.
    //     0xb88f00: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e68] AnonymousClosure: (0xb897e0), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f04: ldr             x1, [x1, #0xe68]
    // 0xb88f08: r0 = AllocateClosure()
    //     0xb88f08: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f0c: mov             x1, x0
    // 0xb88f10: ldur            x0, [fp, #-0x48]
    // 0xb88f14: StoreField: r0->field_13 = r1
    //     0xb88f14: stur            w1, [x0, #0x13]
    // 0xb88f18: ldur            x2, [fp, #-0x40]
    // 0xb88f1c: r1 = Function '<anonymous closure>':.
    //     0xb88f1c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e70] AnonymousClosure: (0xb8970c), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f20: ldr             x1, [x1, #0xe70]
    // 0xb88f24: r0 = AllocateClosure()
    //     0xb88f24: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f28: mov             x1, x0
    // 0xb88f2c: ldur            x0, [fp, #-0x48]
    // 0xb88f30: StoreField: r0->field_23 = r1
    //     0xb88f30: stur            w1, [x0, #0x23]
    // 0xb88f34: ldur            x2, [fp, #-0x40]
    // 0xb88f38: r1 = Function '<anonymous closure>':.
    //     0xb88f38: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e78] AnonymousClosure: (0xb8970c), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f3c: ldr             x1, [x1, #0xe78]
    // 0xb88f40: r0 = AllocateClosure()
    //     0xb88f40: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f44: mov             x1, x0
    // 0xb88f48: ldur            x0, [fp, #-0x48]
    // 0xb88f4c: StoreField: r0->field_27 = r1
    //     0xb88f4c: stur            w1, [x0, #0x27]
    // 0xb88f50: ldur            x2, [fp, #-0x40]
    // 0xb88f54: r1 = Function '<anonymous closure>':.
    //     0xb88f54: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e80] AnonymousClosure: (0xb895b8), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f58: ldr             x1, [x1, #0xe80]
    // 0xb88f5c: r0 = AllocateClosure()
    //     0xb88f5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f60: mov             x1, x0
    // 0xb88f64: ldur            x0, [fp, #-0x48]
    // 0xb88f68: StoreField: r0->field_2b = r1
    //     0xb88f68: stur            w1, [x0, #0x2b]
    // 0xb88f6c: ldur            x2, [fp, #-0x40]
    // 0xb88f70: r1 = Function '<anonymous closure>':.
    //     0xb88f70: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e88] AnonymousClosure: (0xb89478), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f74: ldr             x1, [x1, #0xe88]
    // 0xb88f78: r0 = AllocateClosure()
    //     0xb88f78: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f7c: mov             x1, x0
    // 0xb88f80: ldur            x0, [fp, #-0x48]
    // 0xb88f84: StoreField: r0->field_2f = r1
    //     0xb88f84: stur            w1, [x0, #0x2f]
    // 0xb88f88: ldur            x2, [fp, #-0x40]
    // 0xb88f8c: r1 = Function '<anonymous closure>':.
    //     0xb88f8c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e90] AnonymousClosure: (0xb8942c), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88f90: ldr             x1, [x1, #0xe90]
    // 0xb88f94: r0 = AllocateClosure()
    //     0xb88f94: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88f98: ldur            x2, [fp, #-0x40]
    // 0xb88f9c: r1 = Function '<anonymous closure>':.
    //     0xb88f9c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e98] AnonymousClosure: (0xb892fc), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::build (0xb88e54)
    //     0xb88fa0: ldr             x1, [x1, #0xe98]
    // 0xb88fa4: stur            x0, [fp, #-8]
    // 0xb88fa8: r0 = AllocateClosure()
    //     0xb88fa8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb88fac: r1 = Null
    //     0xb88fac: mov             x1, NULL
    // 0xb88fb0: stur            x0, [fp, #-0x40]
    // 0xb88fb4: r0 = TableCalendar()
    //     0xb88fb4: bl              #0xb892e4  ; AllocateTableCalendarStub -> TableCalendar<X0> (size=0xd4)
    // 0xb88fb8: stur            x0, [fp, #-0x50]
    // 0xb88fbc: ldur            x16, [fp, #-0x10]
    // 0xb88fc0: ldur            lr, [fp, #-0x20]
    // 0xb88fc4: stp             lr, x16, [SP, #0x10]
    // 0xb88fc8: ldur            x16, [fp, #-0x18]
    // 0xb88fcc: ldur            lr, [fp, #-8]
    // 0xb88fd0: stp             lr, x16, [SP]
    // 0xb88fd4: mov             x1, x0
    // 0xb88fd8: ldur            x2, [fp, #-0x48]
    // 0xb88fdc: ldur            x3, [fp, #-0x38]
    // 0xb88fe0: ldur            x5, [fp, #-0x28]
    // 0xb88fe4: ldur            x6, [fp, #-0x40]
    // 0xb88fe8: ldur            x7, [fp, #-0x30]
    // 0xb88fec: r0 = TableCalendar()
    //     0xb88fec: bl              #0xb89008  ; [package:table_calendar/src/table_calendar.dart] TableCalendar::TableCalendar
    // 0xb88ff0: ldur            x0, [fp, #-0x50]
    // 0xb88ff4: LeaveFrame
    //     0xb88ff4: mov             SP, fp
    //     0xb88ff8: ldp             fp, lr, [SP], #0x10
    // 0xb88ffc: ret
    //     0xb88ffc: ret             
    // 0xb89000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89004: b               #0xb88e70
  }
  [closure] bool <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb892fc, size: 0x48
    // 0xb892fc: EnterFrame
    //     0xb892fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb89300: mov             fp, SP
    // 0xb89304: ldr             x0, [fp, #0x18]
    // 0xb89308: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb89308: ldur            w1, [x0, #0x17]
    // 0xb8930c: DecompressPointer r1
    //     0xb8930c: add             x1, x1, HEAP, lsl #32
    // 0xb89310: CheckStackOverflow
    //     0xb89310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89314: cmp             SP, x16
    //     0xb89318: b.ls            #0xb8933c
    // 0xb8931c: LoadField: r0 = r1->field_f
    //     0xb8931c: ldur            w0, [x1, #0xf]
    // 0xb89320: DecompressPointer r0
    //     0xb89320: add             x0, x0, HEAP, lsl #32
    // 0xb89324: mov             x1, x0
    // 0xb89328: ldr             x2, [fp, #0x10]
    // 0xb8932c: r0 = isHoliday()
    //     0xb8932c: bl              #0xb89344  ; [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::isHoliday
    // 0xb89330: LeaveFrame
    //     0xb89330: mov             SP, fp
    //     0xb89334: ldp             fp, lr, [SP], #0x10
    // 0xb89338: ret
    //     0xb89338: ret             
    // 0xb8933c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8933c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89340: b               #0xb8931c
  }
  _ isHoliday(/* No info */) {
    // ** addr: 0xb89344, size: 0xa4
    // 0xb89344: EnterFrame
    //     0xb89344: stp             fp, lr, [SP, #-0x10]!
    //     0xb89348: mov             fp, SP
    // 0xb8934c: AllocStack(0x18)
    //     0xb8934c: sub             SP, SP, #0x18
    // 0xb89350: SetupParameters(NCalendar this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb89350: mov             x0, x1
    //     0xb89354: stur            x1, [fp, #-8]
    //     0xb89358: mov             x1, x2
    //     0xb8935c: stur            x2, [fp, #-0x10]
    // 0xb89360: CheckStackOverflow
    //     0xb89360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89364: cmp             SP, x16
    //     0xb89368: b.ls            #0xb893e0
    // 0xb8936c: r1 = 1
    //     0xb8936c: movz            x1, #0x1
    // 0xb89370: r0 = AllocateContext()
    //     0xb89370: bl              #0xec126c  ; AllocateContextStub
    // 0xb89374: mov             x2, x0
    // 0xb89378: ldur            x1, [fp, #-0x10]
    // 0xb8937c: stur            x2, [fp, #-0x18]
    // 0xb89380: StoreField: r2->field_f = r1
    //     0xb89380: stur            w1, [x2, #0xf]
    // 0xb89384: r0 = LoadClassIdInstr(r1)
    //     0xb89384: ldur            x0, [x1, #-1]
    //     0xb89388: ubfx            x0, x0, #0xc, #0x14
    // 0xb8938c: r0 = GDT[cid_x0 + -0xfad]()
    //     0xb8938c: sub             lr, x0, #0xfad
    //     0xb89390: ldr             lr, [x21, lr, lsl #3]
    //     0xb89394: blr             lr
    // 0xb89398: cmp             x0, #7
    // 0xb8939c: b.ne            #0xb893a8
    // 0xb893a0: r0 = true
    //     0xb893a0: add             x0, NULL, #0x20  ; true
    // 0xb893a4: b               #0xb893d4
    // 0xb893a8: ldur            x0, [fp, #-8]
    // 0xb893ac: LoadField: r3 = r0->field_27
    //     0xb893ac: ldur            w3, [x0, #0x27]
    // 0xb893b0: DecompressPointer r3
    //     0xb893b0: add             x3, x3, HEAP, lsl #32
    // 0xb893b4: ldur            x2, [fp, #-0x18]
    // 0xb893b8: stur            x3, [fp, #-0x10]
    // 0xb893bc: r1 = Function '<anonymous closure>':.
    //     0xb893bc: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ea0] AnonymousClosure: (0xb893e8), in [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::isHoliday (0xb89344)
    //     0xb893c0: ldr             x1, [x1, #0xea0]
    // 0xb893c4: r0 = AllocateClosure()
    //     0xb893c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb893c8: ldur            x1, [fp, #-0x10]
    // 0xb893cc: mov             x2, x0
    // 0xb893d0: r0 = any()
    //     0xb893d0: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0xb893d4: LeaveFrame
    //     0xb893d4: mov             SP, fp
    //     0xb893d8: ldp             fp, lr, [SP], #0x10
    // 0xb893dc: ret
    //     0xb893dc: ret             
    // 0xb893e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb893e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb893e4: b               #0xb8936c
  }
  [closure] bool <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb893e8, size: 0x44
    // 0xb893e8: EnterFrame
    //     0xb893e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb893ec: mov             fp, SP
    // 0xb893f0: ldr             x0, [fp, #0x18]
    // 0xb893f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb893f4: ldur            w1, [x0, #0x17]
    // 0xb893f8: DecompressPointer r1
    //     0xb893f8: add             x1, x1, HEAP, lsl #32
    // 0xb893fc: CheckStackOverflow
    //     0xb893fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89400: cmp             SP, x16
    //     0xb89404: b.ls            #0xb89424
    // 0xb89408: LoadField: r2 = r1->field_f
    //     0xb89408: ldur            w2, [x1, #0xf]
    // 0xb8940c: DecompressPointer r2
    //     0xb8940c: add             x2, x2, HEAP, lsl #32
    // 0xb89410: ldr             x1, [fp, #0x10]
    // 0xb89414: r0 = isSameDay()
    //     0xb89414: bl              #0xa4fa74  ; [package:table_calendar/src/shared/utils.dart] ::isSameDay
    // 0xb89418: LeaveFrame
    //     0xb89418: mov             SP, fp
    //     0xb8941c: ldp             fp, lr, [SP], #0x10
    // 0xb89420: ret
    //     0xb89420: ret             
    // 0xb89424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89428: b               #0xb89408
  }
  [closure] bool <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb8942c, size: 0x4c
    // 0xb8942c: EnterFrame
    //     0xb8942c: stp             fp, lr, [SP, #-0x10]!
    //     0xb89430: mov             fp, SP
    // 0xb89434: ldr             x0, [fp, #0x18]
    // 0xb89438: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb89438: ldur            w1, [x0, #0x17]
    // 0xb8943c: DecompressPointer r1
    //     0xb8943c: add             x1, x1, HEAP, lsl #32
    // 0xb89440: CheckStackOverflow
    //     0xb89440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89444: cmp             SP, x16
    //     0xb89448: b.ls            #0xb89470
    // 0xb8944c: LoadField: r0 = r1->field_f
    //     0xb8944c: ldur            w0, [x1, #0xf]
    // 0xb89450: DecompressPointer r0
    //     0xb89450: add             x0, x0, HEAP, lsl #32
    // 0xb89454: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb89454: ldur            w1, [x0, #0x17]
    // 0xb89458: DecompressPointer r1
    //     0xb89458: add             x1, x1, HEAP, lsl #32
    // 0xb8945c: ldr             x2, [fp, #0x10]
    // 0xb89460: r0 = isSameDay()
    //     0xb89460: bl              #0xa4fa74  ; [package:table_calendar/src/shared/utils.dart] ::isSameDay
    // 0xb89464: LeaveFrame
    //     0xb89464: mov             SP, fp
    //     0xb89468: ldp             fp, lr, [SP], #0x10
    // 0xb8946c: ret
    //     0xb8946c: ret             
    // 0xb89470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89470: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89474: b               #0xb8944c
  }
  [closure] NCalendarCell <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xb89478, size: 0x128
    // 0xb89478: EnterFrame
    //     0xb89478: stp             fp, lr, [SP, #-0x10]!
    //     0xb8947c: mov             fp, SP
    // 0xb89480: AllocStack(0x18)
    //     0xb89480: sub             SP, SP, #0x18
    // 0xb89484: SetupParameters()
    //     0xb89484: ldr             x0, [fp, #0x28]
    //     0xb89488: ldur            w2, [x0, #0x17]
    //     0xb8948c: add             x2, x2, HEAP, lsl #32
    //     0xb89490: stur            x2, [fp, #-8]
    // 0xb89494: CheckStackOverflow
    //     0xb89494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89498: cmp             SP, x16
    //     0xb8949c: b.ls            #0xb89598
    // 0xb894a0: ldr             x3, [fp, #0x18]
    // 0xb894a4: r0 = LoadClassIdInstr(r3)
    //     0xb894a4: ldur            x0, [x3, #-1]
    //     0xb894a8: ubfx            x0, x0, #0xc, #0x14
    // 0xb894ac: mov             x1, x3
    // 0xb894b0: r0 = GDT[cid_x0 + -0xfad]()
    //     0xb894b0: sub             lr, x0, #0xfad
    //     0xb894b4: ldr             lr, [x21, lr, lsl #3]
    //     0xb894b8: blr             lr
    // 0xb894bc: cmp             x0, #5
    // 0xb894c0: r16 = true
    //     0xb894c0: add             x16, NULL, #0x20  ; true
    // 0xb894c4: r17 = false
    //     0xb894c4: add             x17, NULL, #0x30  ; false
    // 0xb894c8: csel            x2, x16, x17, eq
    // 0xb894cc: ldur            x0, [fp, #-8]
    // 0xb894d0: stur            x2, [fp, #-0x10]
    // 0xb894d4: LoadField: r1 = r0->field_f
    //     0xb894d4: ldur            w1, [x0, #0xf]
    // 0xb894d8: DecompressPointer r1
    //     0xb894d8: add             x1, x1, HEAP, lsl #32
    // 0xb894dc: LoadField: r0 = r1->field_2b
    //     0xb894dc: ldur            w0, [x1, #0x2b]
    // 0xb894e0: DecompressPointer r0
    //     0xb894e0: add             x0, x0, HEAP, lsl #32
    // 0xb894e4: ldr             x1, [fp, #0x18]
    // 0xb894e8: stur            x0, [fp, #-8]
    // 0xb894ec: r0 = DateTimeExtensions.toUniqueKey()
    //     0xb894ec: bl              #0x8ed9bc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueKey
    // 0xb894f0: ldur            x1, [fp, #-8]
    // 0xb894f4: r2 = LoadClassIdInstr(r1)
    //     0xb894f4: ldur            x2, [x1, #-1]
    //     0xb894f8: ubfx            x2, x2, #0xc, #0x14
    // 0xb894fc: mov             x16, x0
    // 0xb89500: mov             x0, x2
    // 0xb89504: mov             x2, x16
    // 0xb89508: r0 = GDT[cid_x0 + -0x114]()
    //     0xb89508: sub             lr, x0, #0x114
    //     0xb8950c: ldr             lr, [x21, lr, lsl #3]
    //     0xb89510: blr             lr
    // 0xb89514: cmp             w0, NULL
    // 0xb89518: b.ne            #0xb89534
    // 0xb8951c: r1 = <EventCategory>
    //     0xb8951c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] TypeArguments: <EventCategory>
    //     0xb89520: ldr             x1, [x1, #0xb08]
    // 0xb89524: r2 = 0
    //     0xb89524: movz            x2, #0
    // 0xb89528: r0 = _GrowableList()
    //     0xb89528: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8952c: mov             x2, x0
    // 0xb89530: b               #0xb89538
    // 0xb89534: mov             x2, x0
    // 0xb89538: ldr             x1, [fp, #0x18]
    // 0xb8953c: ldur            x0, [fp, #-0x10]
    // 0xb89540: stur            x2, [fp, #-8]
    // 0xb89544: r0 = NCalendarCellDate()
    //     0xb89544: bl              #0xb895ac  ; AllocateNCalendarCellDateStub -> NCalendarCellDate (size=0x20)
    // 0xb89548: mov             x1, x0
    // 0xb8954c: ldr             x0, [fp, #0x18]
    // 0xb89550: stur            x1, [fp, #-0x18]
    // 0xb89554: StoreField: r1->field_b = r0
    //     0xb89554: stur            w0, [x1, #0xb]
    // 0xb89558: ldur            x0, [fp, #-0x10]
    // 0xb8955c: StoreField: r1->field_f = r0
    //     0xb8955c: stur            w0, [x1, #0xf]
    // 0xb89560: r0 = false
    //     0xb89560: add             x0, NULL, #0x30  ; false
    // 0xb89564: StoreField: r1->field_13 = r0
    //     0xb89564: stur            w0, [x1, #0x13]
    // 0xb89568: ArrayStore: r1[0] = r0  ; List_4
    //     0xb89568: stur            w0, [x1, #0x17]
    // 0xb8956c: ldur            x2, [fp, #-8]
    // 0xb89570: StoreField: r1->field_1b = r2
    //     0xb89570: stur            w2, [x1, #0x1b]
    // 0xb89574: r0 = NCalendarCell()
    //     0xb89574: bl              #0xb895a0  ; AllocateNCalendarCellStub -> NCalendarCell (size=0x18)
    // 0xb89578: r1 = false
    //     0xb89578: add             x1, NULL, #0x30  ; false
    // 0xb8957c: StoreField: r0->field_b = r1
    //     0xb8957c: stur            w1, [x0, #0xb]
    // 0xb89580: StoreField: r0->field_f = r1
    //     0xb89580: stur            w1, [x0, #0xf]
    // 0xb89584: ldur            x1, [fp, #-0x18]
    // 0xb89588: StoreField: r0->field_13 = r1
    //     0xb89588: stur            w1, [x0, #0x13]
    // 0xb8958c: LeaveFrame
    //     0xb8958c: mov             SP, fp
    //     0xb89590: ldp             fp, lr, [SP], #0x10
    // 0xb89594: ret
    //     0xb89594: ret             
    // 0xb89598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8959c: b               #0xb894a0
  }
  [closure] NCalendarCell <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xb895b8, size: 0x154
    // 0xb895b8: EnterFrame
    //     0xb895b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb895bc: mov             fp, SP
    // 0xb895c0: AllocStack(0x20)
    //     0xb895c0: sub             SP, SP, #0x20
    // 0xb895c4: SetupParameters()
    //     0xb895c4: ldr             x0, [fp, #0x28]
    //     0xb895c8: ldur            w2, [x0, #0x17]
    //     0xb895cc: add             x2, x2, HEAP, lsl #32
    //     0xb895d0: stur            x2, [fp, #-8]
    // 0xb895d4: CheckStackOverflow
    //     0xb895d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb895d8: cmp             SP, x16
    //     0xb895dc: b.ls            #0xb89704
    // 0xb895e0: ldr             x3, [fp, #0x18]
    // 0xb895e4: r0 = LoadClassIdInstr(r3)
    //     0xb895e4: ldur            x0, [x3, #-1]
    //     0xb895e8: ubfx            x0, x0, #0xc, #0x14
    // 0xb895ec: mov             x1, x3
    // 0xb895f0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb895f0: sub             lr, x0, #0xfff
    //     0xb895f4: ldr             lr, [x21, lr, lsl #3]
    //     0xb895f8: blr             lr
    // 0xb895fc: mov             x2, x0
    // 0xb89600: ldr             x1, [fp, #0x10]
    // 0xb89604: stur            x2, [fp, #-0x10]
    // 0xb89608: r0 = LoadClassIdInstr(r1)
    //     0xb89608: ldur            x0, [x1, #-1]
    //     0xb8960c: ubfx            x0, x0, #0xc, #0x14
    // 0xb89610: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb89610: sub             lr, x0, #0xfff
    //     0xb89614: ldr             lr, [x21, lr, lsl #3]
    //     0xb89618: blr             lr
    // 0xb8961c: mov             x1, x0
    // 0xb89620: ldur            x0, [fp, #-0x10]
    // 0xb89624: cmp             x0, x1
    // 0xb89628: r16 = true
    //     0xb89628: add             x16, NULL, #0x20  ; true
    // 0xb8962c: r17 = false
    //     0xb8962c: add             x17, NULL, #0x30  ; false
    // 0xb89630: csel            x2, x16, x17, ne
    // 0xb89634: ldur            x0, [fp, #-8]
    // 0xb89638: stur            x2, [fp, #-0x18]
    // 0xb8963c: LoadField: r1 = r0->field_f
    //     0xb8963c: ldur            w1, [x0, #0xf]
    // 0xb89640: DecompressPointer r1
    //     0xb89640: add             x1, x1, HEAP, lsl #32
    // 0xb89644: LoadField: r0 = r1->field_2b
    //     0xb89644: ldur            w0, [x1, #0x2b]
    // 0xb89648: DecompressPointer r0
    //     0xb89648: add             x0, x0, HEAP, lsl #32
    // 0xb8964c: ldr             x1, [fp, #0x18]
    // 0xb89650: stur            x0, [fp, #-8]
    // 0xb89654: r0 = DateTimeExtensions.toUniqueKey()
    //     0xb89654: bl              #0x8ed9bc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueKey
    // 0xb89658: ldur            x1, [fp, #-8]
    // 0xb8965c: r2 = LoadClassIdInstr(r1)
    //     0xb8965c: ldur            x2, [x1, #-1]
    //     0xb89660: ubfx            x2, x2, #0xc, #0x14
    // 0xb89664: mov             x16, x0
    // 0xb89668: mov             x0, x2
    // 0xb8966c: mov             x2, x16
    // 0xb89670: r0 = GDT[cid_x0 + -0x114]()
    //     0xb89670: sub             lr, x0, #0x114
    //     0xb89674: ldr             lr, [x21, lr, lsl #3]
    //     0xb89678: blr             lr
    // 0xb8967c: cmp             w0, NULL
    // 0xb89680: b.ne            #0xb8969c
    // 0xb89684: r1 = <EventCategory>
    //     0xb89684: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] TypeArguments: <EventCategory>
    //     0xb89688: ldr             x1, [x1, #0xb08]
    // 0xb8968c: r2 = 0
    //     0xb8968c: movz            x2, #0
    // 0xb89690: r0 = _GrowableList()
    //     0xb89690: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb89694: mov             x2, x0
    // 0xb89698: b               #0xb896a0
    // 0xb8969c: mov             x2, x0
    // 0xb896a0: ldr             x1, [fp, #0x18]
    // 0xb896a4: ldur            x0, [fp, #-0x18]
    // 0xb896a8: stur            x2, [fp, #-8]
    // 0xb896ac: r0 = NCalendarCellDate()
    //     0xb896ac: bl              #0xb895ac  ; AllocateNCalendarCellDateStub -> NCalendarCellDate (size=0x20)
    // 0xb896b0: mov             x1, x0
    // 0xb896b4: ldr             x0, [fp, #0x18]
    // 0xb896b8: stur            x1, [fp, #-0x20]
    // 0xb896bc: StoreField: r1->field_b = r0
    //     0xb896bc: stur            w0, [x1, #0xb]
    // 0xb896c0: r0 = false
    //     0xb896c0: add             x0, NULL, #0x30  ; false
    // 0xb896c4: StoreField: r1->field_f = r0
    //     0xb896c4: stur            w0, [x1, #0xf]
    // 0xb896c8: r2 = true
    //     0xb896c8: add             x2, NULL, #0x20  ; true
    // 0xb896cc: StoreField: r1->field_13 = r2
    //     0xb896cc: stur            w2, [x1, #0x13]
    // 0xb896d0: ldur            x2, [fp, #-0x18]
    // 0xb896d4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb896d4: stur            w2, [x1, #0x17]
    // 0xb896d8: ldur            x2, [fp, #-8]
    // 0xb896dc: StoreField: r1->field_1b = r2
    //     0xb896dc: stur            w2, [x1, #0x1b]
    // 0xb896e0: r0 = NCalendarCell()
    //     0xb896e0: bl              #0xb895a0  ; AllocateNCalendarCellStub -> NCalendarCell (size=0x18)
    // 0xb896e4: r1 = false
    //     0xb896e4: add             x1, NULL, #0x30  ; false
    // 0xb896e8: StoreField: r0->field_b = r1
    //     0xb896e8: stur            w1, [x0, #0xb]
    // 0xb896ec: StoreField: r0->field_f = r1
    //     0xb896ec: stur            w1, [x0, #0xf]
    // 0xb896f0: ldur            x1, [fp, #-0x20]
    // 0xb896f4: StoreField: r0->field_13 = r1
    //     0xb896f4: stur            w1, [x0, #0x13]
    // 0xb896f8: LeaveFrame
    //     0xb896f8: mov             SP, fp
    //     0xb896fc: ldp             fp, lr, [SP], #0x10
    // 0xb89700: ret
    //     0xb89700: ret             
    // 0xb89704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89704: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89708: b               #0xb895e0
  }
  [closure] NCalendarCell <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xb8970c, size: 0xd4
    // 0xb8970c: EnterFrame
    //     0xb8970c: stp             fp, lr, [SP, #-0x10]!
    //     0xb89710: mov             fp, SP
    // 0xb89714: AllocStack(0x18)
    //     0xb89714: sub             SP, SP, #0x18
    // 0xb89718: SetupParameters()
    //     0xb89718: ldr             x0, [fp, #0x28]
    //     0xb8971c: ldur            w2, [x0, #0x17]
    //     0xb89720: add             x2, x2, HEAP, lsl #32
    //     0xb89724: stur            x2, [fp, #-8]
    // 0xb89728: CheckStackOverflow
    //     0xb89728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8972c: cmp             SP, x16
    //     0xb89730: b.ls            #0xb897d8
    // 0xb89734: ldr             x3, [fp, #0x18]
    // 0xb89738: r0 = LoadClassIdInstr(r3)
    //     0xb89738: ldur            x0, [x3, #-1]
    //     0xb8973c: ubfx            x0, x0, #0xc, #0x14
    // 0xb89740: mov             x1, x3
    // 0xb89744: r0 = GDT[cid_x0 + -0xfad]()
    //     0xb89744: sub             lr, x0, #0xfad
    //     0xb89748: ldr             lr, [x21, lr, lsl #3]
    //     0xb8974c: blr             lr
    // 0xb89750: cmp             x0, #5
    // 0xb89754: r16 = true
    //     0xb89754: add             x16, NULL, #0x20  ; true
    // 0xb89758: r17 = false
    //     0xb89758: add             x17, NULL, #0x30  ; false
    // 0xb8975c: csel            x3, x16, x17, eq
    // 0xb89760: ldur            x0, [fp, #-8]
    // 0xb89764: stur            x3, [fp, #-0x10]
    // 0xb89768: LoadField: r1 = r0->field_f
    //     0xb89768: ldur            w1, [x0, #0xf]
    // 0xb8976c: DecompressPointer r1
    //     0xb8976c: add             x1, x1, HEAP, lsl #32
    // 0xb89770: ldr             x2, [fp, #0x18]
    // 0xb89774: r0 = isHoliday()
    //     0xb89774: bl              #0xb89344  ; [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::isHoliday
    // 0xb89778: stur            x0, [fp, #-8]
    // 0xb8977c: r0 = NCalendarCellDate()
    //     0xb8977c: bl              #0xb895ac  ; AllocateNCalendarCellDateStub -> NCalendarCellDate (size=0x20)
    // 0xb89780: mov             x1, x0
    // 0xb89784: ldr             x0, [fp, #0x18]
    // 0xb89788: stur            x1, [fp, #-0x18]
    // 0xb8978c: StoreField: r1->field_b = r0
    //     0xb8978c: stur            w0, [x1, #0xb]
    // 0xb89790: ldur            x0, [fp, #-0x10]
    // 0xb89794: StoreField: r1->field_f = r0
    //     0xb89794: stur            w0, [x1, #0xf]
    // 0xb89798: ldur            x0, [fp, #-8]
    // 0xb8979c: StoreField: r1->field_13 = r0
    //     0xb8979c: stur            w0, [x1, #0x13]
    // 0xb897a0: r0 = true
    //     0xb897a0: add             x0, NULL, #0x20  ; true
    // 0xb897a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb897a4: stur            w0, [x1, #0x17]
    // 0xb897a8: r0 = const []
    //     0xb897a8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ea8] List<EventCategory>(0)
    //     0xb897ac: ldr             x0, [x0, #0xea8]
    // 0xb897b0: StoreField: r1->field_1b = r0
    //     0xb897b0: stur            w0, [x1, #0x1b]
    // 0xb897b4: r0 = NCalendarCell()
    //     0xb897b4: bl              #0xb895a0  ; AllocateNCalendarCellStub -> NCalendarCell (size=0x18)
    // 0xb897b8: r1 = false
    //     0xb897b8: add             x1, NULL, #0x30  ; false
    // 0xb897bc: StoreField: r0->field_b = r1
    //     0xb897bc: stur            w1, [x0, #0xb]
    // 0xb897c0: StoreField: r0->field_f = r1
    //     0xb897c0: stur            w1, [x0, #0xf]
    // 0xb897c4: ldur            x1, [fp, #-0x18]
    // 0xb897c8: StoreField: r0->field_13 = r1
    //     0xb897c8: stur            w1, [x0, #0x13]
    // 0xb897cc: LeaveFrame
    //     0xb897cc: mov             SP, fp
    //     0xb897d0: ldp             fp, lr, [SP], #0x10
    // 0xb897d4: ret
    //     0xb897d4: ret             
    // 0xb897d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb897d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb897dc: b               #0xb89734
  }
  [closure] NCalendarCell <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xb897e0, size: 0x1ec
    // 0xb897e0: EnterFrame
    //     0xb897e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb897e4: mov             fp, SP
    // 0xb897e8: AllocStack(0x38)
    //     0xb897e8: sub             SP, SP, #0x38
    // 0xb897ec: SetupParameters()
    //     0xb897ec: ldr             x0, [fp, #0x28]
    //     0xb897f0: ldur            w1, [x0, #0x17]
    //     0xb897f4: add             x1, x1, HEAP, lsl #32
    //     0xb897f8: stur            x1, [fp, #-8]
    // 0xb897fc: CheckStackOverflow
    //     0xb897fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89800: cmp             SP, x16
    //     0xb89804: b.ls            #0xb899c4
    // 0xb89808: r0 = DateTime()
    //     0xb89808: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb8980c: mov             x1, x0
    // 0xb89810: r0 = false
    //     0xb89810: add             x0, NULL, #0x30  ; false
    // 0xb89814: stur            x1, [fp, #-0x10]
    // 0xb89818: StoreField: r1->field_13 = r0
    //     0xb89818: stur            w0, [x1, #0x13]
    // 0xb8981c: r0 = _getCurrentMicros()
    //     0xb8981c: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb89820: r1 = LoadInt32Instr(r0)
    //     0xb89820: sbfx            x1, x0, #1, #0x1f
    //     0xb89824: tbz             w0, #0, #0xb8982c
    //     0xb89828: ldur            x1, [x0, #7]
    // 0xb8982c: ldur            x2, [fp, #-0x10]
    // 0xb89830: StoreField: r2->field_7 = r1
    //     0xb89830: stur            x1, [x2, #7]
    // 0xb89834: ldr             x1, [fp, #0x18]
    // 0xb89838: r0 = isSameDay()
    //     0xb89838: bl              #0xa4fa74  ; [package:table_calendar/src/shared/utils.dart] ::isSameDay
    // 0xb8983c: mov             x3, x0
    // 0xb89840: ldr             x2, [fp, #0x18]
    // 0xb89844: stur            x3, [fp, #-0x10]
    // 0xb89848: r0 = LoadClassIdInstr(r2)
    //     0xb89848: ldur            x0, [x2, #-1]
    //     0xb8984c: ubfx            x0, x0, #0xc, #0x14
    // 0xb89850: mov             x1, x2
    // 0xb89854: r0 = GDT[cid_x0 + -0xfad]()
    //     0xb89854: sub             lr, x0, #0xfad
    //     0xb89858: ldr             lr, [x21, lr, lsl #3]
    //     0xb8985c: blr             lr
    // 0xb89860: cmp             x0, #5
    // 0xb89864: r16 = true
    //     0xb89864: add             x16, NULL, #0x20  ; true
    // 0xb89868: r17 = false
    //     0xb89868: add             x17, NULL, #0x30  ; false
    // 0xb8986c: csel            x3, x16, x17, eq
    // 0xb89870: ldur            x0, [fp, #-8]
    // 0xb89874: stur            x3, [fp, #-0x18]
    // 0xb89878: LoadField: r1 = r0->field_f
    //     0xb89878: ldur            w1, [x0, #0xf]
    // 0xb8987c: DecompressPointer r1
    //     0xb8987c: add             x1, x1, HEAP, lsl #32
    // 0xb89880: ldr             x2, [fp, #0x18]
    // 0xb89884: r0 = isHoliday()
    //     0xb89884: bl              #0xb89344  ; [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::isHoliday
    // 0xb89888: mov             x3, x0
    // 0xb8988c: ldr             x2, [fp, #0x18]
    // 0xb89890: stur            x3, [fp, #-0x20]
    // 0xb89894: r0 = LoadClassIdInstr(r2)
    //     0xb89894: ldur            x0, [x2, #-1]
    //     0xb89898: ubfx            x0, x0, #0xc, #0x14
    // 0xb8989c: mov             x1, x2
    // 0xb898a0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb898a0: sub             lr, x0, #0xfff
    //     0xb898a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb898a8: blr             lr
    // 0xb898ac: mov             x2, x0
    // 0xb898b0: ldr             x1, [fp, #0x10]
    // 0xb898b4: stur            x2, [fp, #-0x28]
    // 0xb898b8: r0 = LoadClassIdInstr(r1)
    //     0xb898b8: ldur            x0, [x1, #-1]
    //     0xb898bc: ubfx            x0, x0, #0xc, #0x14
    // 0xb898c0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb898c0: sub             lr, x0, #0xfff
    //     0xb898c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb898c8: blr             lr
    // 0xb898cc: mov             x1, x0
    // 0xb898d0: ldur            x0, [fp, #-0x28]
    // 0xb898d4: cmp             x0, x1
    // 0xb898d8: r16 = true
    //     0xb898d8: add             x16, NULL, #0x20  ; true
    // 0xb898dc: r17 = false
    //     0xb898dc: add             x17, NULL, #0x30  ; false
    // 0xb898e0: csel            x2, x16, x17, ne
    // 0xb898e4: ldur            x0, [fp, #-8]
    // 0xb898e8: stur            x2, [fp, #-0x30]
    // 0xb898ec: LoadField: r1 = r0->field_f
    //     0xb898ec: ldur            w1, [x0, #0xf]
    // 0xb898f0: DecompressPointer r1
    //     0xb898f0: add             x1, x1, HEAP, lsl #32
    // 0xb898f4: LoadField: r0 = r1->field_2b
    //     0xb898f4: ldur            w0, [x1, #0x2b]
    // 0xb898f8: DecompressPointer r0
    //     0xb898f8: add             x0, x0, HEAP, lsl #32
    // 0xb898fc: ldr             x1, [fp, #0x18]
    // 0xb89900: stur            x0, [fp, #-8]
    // 0xb89904: r0 = DateTimeExtensions.toUniqueKey()
    //     0xb89904: bl              #0x8ed9bc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueKey
    // 0xb89908: ldur            x1, [fp, #-8]
    // 0xb8990c: r2 = LoadClassIdInstr(r1)
    //     0xb8990c: ldur            x2, [x1, #-1]
    //     0xb89910: ubfx            x2, x2, #0xc, #0x14
    // 0xb89914: mov             x16, x0
    // 0xb89918: mov             x0, x2
    // 0xb8991c: mov             x2, x16
    // 0xb89920: r0 = GDT[cid_x0 + -0x114]()
    //     0xb89920: sub             lr, x0, #0x114
    //     0xb89924: ldr             lr, [x21, lr, lsl #3]
    //     0xb89928: blr             lr
    // 0xb8992c: cmp             w0, NULL
    // 0xb89930: b.ne            #0xb8994c
    // 0xb89934: r1 = <EventCategory>
    //     0xb89934: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] TypeArguments: <EventCategory>
    //     0xb89938: ldr             x1, [x1, #0xb08]
    // 0xb8993c: r2 = 0
    //     0xb8993c: movz            x2, #0
    // 0xb89940: r0 = _GrowableList()
    //     0xb89940: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb89944: mov             x5, x0
    // 0xb89948: b               #0xb89950
    // 0xb8994c: mov             x5, x0
    // 0xb89950: ldr             x1, [fp, #0x18]
    // 0xb89954: ldur            x4, [fp, #-0x10]
    // 0xb89958: ldur            x3, [fp, #-0x18]
    // 0xb8995c: ldur            x2, [fp, #-0x20]
    // 0xb89960: ldur            x0, [fp, #-0x30]
    // 0xb89964: stur            x5, [fp, #-8]
    // 0xb89968: r0 = NCalendarCellDate()
    //     0xb89968: bl              #0xb895ac  ; AllocateNCalendarCellDateStub -> NCalendarCellDate (size=0x20)
    // 0xb8996c: mov             x1, x0
    // 0xb89970: ldr             x0, [fp, #0x18]
    // 0xb89974: stur            x1, [fp, #-0x38]
    // 0xb89978: StoreField: r1->field_b = r0
    //     0xb89978: stur            w0, [x1, #0xb]
    // 0xb8997c: ldur            x0, [fp, #-0x18]
    // 0xb89980: StoreField: r1->field_f = r0
    //     0xb89980: stur            w0, [x1, #0xf]
    // 0xb89984: ldur            x0, [fp, #-0x20]
    // 0xb89988: StoreField: r1->field_13 = r0
    //     0xb89988: stur            w0, [x1, #0x13]
    // 0xb8998c: ldur            x0, [fp, #-0x30]
    // 0xb89990: ArrayStore: r1[0] = r0  ; List_4
    //     0xb89990: stur            w0, [x1, #0x17]
    // 0xb89994: ldur            x0, [fp, #-8]
    // 0xb89998: StoreField: r1->field_1b = r0
    //     0xb89998: stur            w0, [x1, #0x1b]
    // 0xb8999c: r0 = NCalendarCell()
    //     0xb8999c: bl              #0xb895a0  ; AllocateNCalendarCellStub -> NCalendarCell (size=0x18)
    // 0xb899a0: ldur            x1, [fp, #-0x10]
    // 0xb899a4: StoreField: r0->field_b = r1
    //     0xb899a4: stur            w1, [x0, #0xb]
    // 0xb899a8: r1 = true
    //     0xb899a8: add             x1, NULL, #0x20  ; true
    // 0xb899ac: StoreField: r0->field_f = r1
    //     0xb899ac: stur            w1, [x0, #0xf]
    // 0xb899b0: ldur            x1, [fp, #-0x38]
    // 0xb899b4: StoreField: r0->field_13 = r1
    //     0xb899b4: stur            w1, [x0, #0x13]
    // 0xb899b8: LeaveFrame
    //     0xb899b8: mov             SP, fp
    //     0xb899bc: ldp             fp, lr, [SP], #0x10
    // 0xb899c0: ret
    //     0xb899c0: ret             
    // 0xb899c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb899c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb899c8: b               #0xb89808
  }
  [closure] NCalendarCell <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xb899cc, size: 0x1ac
    // 0xb899cc: EnterFrame
    //     0xb899cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb899d0: mov             fp, SP
    // 0xb899d4: AllocStack(0x30)
    //     0xb899d4: sub             SP, SP, #0x30
    // 0xb899d8: SetupParameters()
    //     0xb899d8: ldr             x0, [fp, #0x28]
    //     0xb899dc: ldur            w2, [x0, #0x17]
    //     0xb899e0: add             x2, x2, HEAP, lsl #32
    //     0xb899e4: stur            x2, [fp, #-8]
    // 0xb899e8: CheckStackOverflow
    //     0xb899e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb899ec: cmp             SP, x16
    //     0xb899f0: b.ls            #0xb89b70
    // 0xb899f4: ldr             x3, [fp, #0x18]
    // 0xb899f8: r0 = LoadClassIdInstr(r3)
    //     0xb899f8: ldur            x0, [x3, #-1]
    //     0xb899fc: ubfx            x0, x0, #0xc, #0x14
    // 0xb89a00: mov             x1, x3
    // 0xb89a04: r0 = GDT[cid_x0 + -0xfad]()
    //     0xb89a04: sub             lr, x0, #0xfad
    //     0xb89a08: ldr             lr, [x21, lr, lsl #3]
    //     0xb89a0c: blr             lr
    // 0xb89a10: cmp             x0, #5
    // 0xb89a14: r16 = true
    //     0xb89a14: add             x16, NULL, #0x20  ; true
    // 0xb89a18: r17 = false
    //     0xb89a18: add             x17, NULL, #0x30  ; false
    // 0xb89a1c: csel            x3, x16, x17, eq
    // 0xb89a20: ldur            x0, [fp, #-8]
    // 0xb89a24: stur            x3, [fp, #-0x10]
    // 0xb89a28: LoadField: r1 = r0->field_f
    //     0xb89a28: ldur            w1, [x0, #0xf]
    // 0xb89a2c: DecompressPointer r1
    //     0xb89a2c: add             x1, x1, HEAP, lsl #32
    // 0xb89a30: ldr             x2, [fp, #0x18]
    // 0xb89a34: r0 = isHoliday()
    //     0xb89a34: bl              #0xb89344  ; [package:nuonline/app/modules/calendar/widgets/calendar_widget.dart] NCalendar::isHoliday
    // 0xb89a38: mov             x3, x0
    // 0xb89a3c: ldr             x2, [fp, #0x18]
    // 0xb89a40: stur            x3, [fp, #-0x18]
    // 0xb89a44: r0 = LoadClassIdInstr(r2)
    //     0xb89a44: ldur            x0, [x2, #-1]
    //     0xb89a48: ubfx            x0, x0, #0xc, #0x14
    // 0xb89a4c: mov             x1, x2
    // 0xb89a50: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb89a50: sub             lr, x0, #0xfff
    //     0xb89a54: ldr             lr, [x21, lr, lsl #3]
    //     0xb89a58: blr             lr
    // 0xb89a5c: mov             x2, x0
    // 0xb89a60: ldr             x1, [fp, #0x10]
    // 0xb89a64: stur            x2, [fp, #-0x20]
    // 0xb89a68: r0 = LoadClassIdInstr(r1)
    //     0xb89a68: ldur            x0, [x1, #-1]
    //     0xb89a6c: ubfx            x0, x0, #0xc, #0x14
    // 0xb89a70: r0 = GDT[cid_x0 + -0xfff]()
    //     0xb89a70: sub             lr, x0, #0xfff
    //     0xb89a74: ldr             lr, [x21, lr, lsl #3]
    //     0xb89a78: blr             lr
    // 0xb89a7c: mov             x1, x0
    // 0xb89a80: ldur            x0, [fp, #-0x20]
    // 0xb89a84: cmp             x0, x1
    // 0xb89a88: r16 = true
    //     0xb89a88: add             x16, NULL, #0x20  ; true
    // 0xb89a8c: r17 = false
    //     0xb89a8c: add             x17, NULL, #0x30  ; false
    // 0xb89a90: csel            x2, x16, x17, ne
    // 0xb89a94: ldur            x0, [fp, #-8]
    // 0xb89a98: stur            x2, [fp, #-0x28]
    // 0xb89a9c: LoadField: r1 = r0->field_f
    //     0xb89a9c: ldur            w1, [x0, #0xf]
    // 0xb89aa0: DecompressPointer r1
    //     0xb89aa0: add             x1, x1, HEAP, lsl #32
    // 0xb89aa4: LoadField: r0 = r1->field_2b
    //     0xb89aa4: ldur            w0, [x1, #0x2b]
    // 0xb89aa8: DecompressPointer r0
    //     0xb89aa8: add             x0, x0, HEAP, lsl #32
    // 0xb89aac: ldr             x1, [fp, #0x18]
    // 0xb89ab0: stur            x0, [fp, #-8]
    // 0xb89ab4: r0 = DateTimeExtensions.toUniqueKey()
    //     0xb89ab4: bl              #0x8ed9bc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueKey
    // 0xb89ab8: ldur            x1, [fp, #-8]
    // 0xb89abc: r2 = LoadClassIdInstr(r1)
    //     0xb89abc: ldur            x2, [x1, #-1]
    //     0xb89ac0: ubfx            x2, x2, #0xc, #0x14
    // 0xb89ac4: mov             x16, x0
    // 0xb89ac8: mov             x0, x2
    // 0xb89acc: mov             x2, x16
    // 0xb89ad0: r0 = GDT[cid_x0 + -0x114]()
    //     0xb89ad0: sub             lr, x0, #0x114
    //     0xb89ad4: ldr             lr, [x21, lr, lsl #3]
    //     0xb89ad8: blr             lr
    // 0xb89adc: cmp             w0, NULL
    // 0xb89ae0: b.ne            #0xb89afc
    // 0xb89ae4: r1 = <EventCategory>
    //     0xb89ae4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] TypeArguments: <EventCategory>
    //     0xb89ae8: ldr             x1, [x1, #0xb08]
    // 0xb89aec: r2 = 0
    //     0xb89aec: movz            x2, #0
    // 0xb89af0: r0 = _GrowableList()
    //     0xb89af0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb89af4: mov             x4, x0
    // 0xb89af8: b               #0xb89b00
    // 0xb89afc: mov             x4, x0
    // 0xb89b00: ldr             x1, [fp, #0x18]
    // 0xb89b04: ldur            x3, [fp, #-0x10]
    // 0xb89b08: ldur            x2, [fp, #-0x18]
    // 0xb89b0c: ldur            x0, [fp, #-0x28]
    // 0xb89b10: stur            x4, [fp, #-8]
    // 0xb89b14: r0 = NCalendarCellDate()
    //     0xb89b14: bl              #0xb895ac  ; AllocateNCalendarCellDateStub -> NCalendarCellDate (size=0x20)
    // 0xb89b18: mov             x1, x0
    // 0xb89b1c: ldr             x0, [fp, #0x18]
    // 0xb89b20: stur            x1, [fp, #-0x30]
    // 0xb89b24: StoreField: r1->field_b = r0
    //     0xb89b24: stur            w0, [x1, #0xb]
    // 0xb89b28: ldur            x0, [fp, #-0x10]
    // 0xb89b2c: StoreField: r1->field_f = r0
    //     0xb89b2c: stur            w0, [x1, #0xf]
    // 0xb89b30: ldur            x0, [fp, #-0x18]
    // 0xb89b34: StoreField: r1->field_13 = r0
    //     0xb89b34: stur            w0, [x1, #0x13]
    // 0xb89b38: ldur            x0, [fp, #-0x28]
    // 0xb89b3c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb89b3c: stur            w0, [x1, #0x17]
    // 0xb89b40: ldur            x0, [fp, #-8]
    // 0xb89b44: StoreField: r1->field_1b = r0
    //     0xb89b44: stur            w0, [x1, #0x1b]
    // 0xb89b48: r0 = NCalendarCell()
    //     0xb89b48: bl              #0xb895a0  ; AllocateNCalendarCellStub -> NCalendarCell (size=0x18)
    // 0xb89b4c: r1 = true
    //     0xb89b4c: add             x1, NULL, #0x20  ; true
    // 0xb89b50: StoreField: r0->field_b = r1
    //     0xb89b50: stur            w1, [x0, #0xb]
    // 0xb89b54: r1 = false
    //     0xb89b54: add             x1, NULL, #0x30  ; false
    // 0xb89b58: StoreField: r0->field_f = r1
    //     0xb89b58: stur            w1, [x0, #0xf]
    // 0xb89b5c: ldur            x1, [fp, #-0x30]
    // 0xb89b60: StoreField: r0->field_13 = r1
    //     0xb89b60: stur            w1, [x0, #0x13]
    // 0xb89b64: LeaveFrame
    //     0xb89b64: mov             SP, fp
    //     0xb89b68: ldp             fp, lr, [SP], #0x10
    // 0xb89b6c: ret
    //     0xb89b6c: ret             
    // 0xb89b70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89b70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89b74: b               #0xb899f4
  }
}
