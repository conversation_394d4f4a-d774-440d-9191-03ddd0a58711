// lib: , url: package:nuonline/app/modules/calendar/widgets/javanese_calendar.dart

// class id: 1050166, size: 0x8
class :: {
}

// class id: 1065, size: 0x14, field offset: 0x8
class JavaneseCalendar extends Object {

  _ JavaneseCalendar.fromDate(/* No info */) {
    // ** addr: 0x821c10, size: 0x14c
    // 0x821c10: EnterFrame
    //     0x821c10: stp             fp, lr, [SP, #-0x10]!
    //     0x821c14: mov             fp, SP
    // 0x821c18: AllocStack(0x28)
    //     0x821c18: sub             SP, SP, #0x28
    // 0x821c1c: r3 = 10
    //     0x821c1c: movz            x3, #0xa
    // 0x821c20: r0 = 1
    //     0x821c20: movz            x0, #0x1
    // 0x821c24: mov             x5, x1
    // 0x821c28: mov             x4, x2
    // 0x821c2c: stur            x1, [fp, #-8]
    // 0x821c30: stur            x2, [fp, #-0x10]
    // 0x821c34: CheckStackOverflow
    //     0x821c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x821c38: cmp             SP, x16
    //     0x821c3c: b.ls            #0x821d54
    // 0x821c40: StoreField: r5->field_7 = r0
    //     0x821c40: stur            x0, [x5, #7]
    // 0x821c44: mov             x2, x3
    // 0x821c48: r1 = Null
    //     0x821c48: mov             x1, NULL
    // 0x821c4c: r0 = AllocateArray()
    //     0x821c4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x821c50: stur            x0, [fp, #-0x18]
    // 0x821c54: r16 = "Legi"
    //     0x821c54: add             x16, PP, #9, lsl #12  ; [pp+0x9448] "Legi"
    //     0x821c58: ldr             x16, [x16, #0x448]
    // 0x821c5c: StoreField: r0->field_f = r16
    //     0x821c5c: stur            w16, [x0, #0xf]
    // 0x821c60: r16 = "Pahing"
    //     0x821c60: add             x16, PP, #9, lsl #12  ; [pp+0x9450] "Pahing"
    //     0x821c64: ldr             x16, [x16, #0x450]
    // 0x821c68: StoreField: r0->field_13 = r16
    //     0x821c68: stur            w16, [x0, #0x13]
    // 0x821c6c: r16 = "Pon"
    //     0x821c6c: add             x16, PP, #9, lsl #12  ; [pp+0x9458] "Pon"
    //     0x821c70: ldr             x16, [x16, #0x458]
    // 0x821c74: ArrayStore: r0[0] = r16  ; List_4
    //     0x821c74: stur            w16, [x0, #0x17]
    // 0x821c78: r16 = "Wage"
    //     0x821c78: add             x16, PP, #9, lsl #12  ; [pp+0x9460] "Wage"
    //     0x821c7c: ldr             x16, [x16, #0x460]
    // 0x821c80: StoreField: r0->field_1b = r16
    //     0x821c80: stur            w16, [x0, #0x1b]
    // 0x821c84: r16 = "Kliwon"
    //     0x821c84: add             x16, PP, #9, lsl #12  ; [pp+0x9468] "Kliwon"
    //     0x821c88: ldr             x16, [x16, #0x468]
    // 0x821c8c: StoreField: r0->field_1f = r16
    //     0x821c8c: stur            w16, [x0, #0x1f]
    // 0x821c90: r1 = <String>
    //     0x821c90: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x821c94: r0 = AllocateGrowableArray()
    //     0x821c94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x821c98: mov             x1, x0
    // 0x821c9c: ldur            x0, [fp, #-0x18]
    // 0x821ca0: StoreField: r1->field_f = r0
    //     0x821ca0: stur            w0, [x1, #0xf]
    // 0x821ca4: r0 = 10
    //     0x821ca4: movz            x0, #0xa
    // 0x821ca8: StoreField: r1->field_b = r0
    //     0x821ca8: stur            w0, [x1, #0xb]
    // 0x821cac: mov             x0, x1
    // 0x821cb0: ldur            x2, [fp, #-8]
    // 0x821cb4: StoreField: r2->field_f = r0
    //     0x821cb4: stur            w0, [x2, #0xf]
    //     0x821cb8: ldurb           w16, [x2, #-1]
    //     0x821cbc: ldurb           w17, [x0, #-1]
    //     0x821cc0: and             x16, x17, x16, lsr #2
    //     0x821cc4: tst             x16, HEAP, lsr #32
    //     0x821cc8: b.eq            #0x821cd0
    //     0x821ccc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x821cd0: ldur            x3, [fp, #-0x10]
    // 0x821cd4: r0 = LoadClassIdInstr(r3)
    //     0x821cd4: ldur            x0, [x3, #-1]
    //     0x821cd8: ubfx            x0, x0, #0xc, #0x14
    // 0x821cdc: mov             x1, x3
    // 0x821ce0: r0 = GDT[cid_x0 + -0xff6]()
    //     0x821ce0: sub             lr, x0, #0xff6
    //     0x821ce4: ldr             lr, [x21, lr, lsl #3]
    //     0x821ce8: blr             lr
    // 0x821cec: mov             x3, x0
    // 0x821cf0: ldur            x2, [fp, #-0x10]
    // 0x821cf4: stur            x3, [fp, #-0x20]
    // 0x821cf8: r0 = LoadClassIdInstr(r2)
    //     0x821cf8: ldur            x0, [x2, #-1]
    //     0x821cfc: ubfx            x0, x0, #0xc, #0x14
    // 0x821d00: mov             x1, x2
    // 0x821d04: r0 = GDT[cid_x0 + -0xfff]()
    //     0x821d04: sub             lr, x0, #0xfff
    //     0x821d08: ldr             lr, [x21, lr, lsl #3]
    //     0x821d0c: blr             lr
    // 0x821d10: mov             x2, x0
    // 0x821d14: ldur            x1, [fp, #-0x10]
    // 0x821d18: stur            x2, [fp, #-0x28]
    // 0x821d1c: r0 = LoadClassIdInstr(r1)
    //     0x821d1c: ldur            x0, [x1, #-1]
    //     0x821d20: ubfx            x0, x0, #0xc, #0x14
    // 0x821d24: r0 = GDT[cid_x0 + -0xfdf]()
    //     0x821d24: sub             lr, x0, #0xfdf
    //     0x821d28: ldr             lr, [x21, lr, lsl #3]
    //     0x821d2c: blr             lr
    // 0x821d30: ldur            x1, [fp, #-8]
    // 0x821d34: ldur            x2, [fp, #-0x20]
    // 0x821d38: ldur            x3, [fp, #-0x28]
    // 0x821d3c: mov             x5, x0
    // 0x821d40: r0 = gregorianToHijri()
    //     0x821d40: bl              #0x821d5c  ; [package:nuonline/app/modules/calendar/widgets/javanese_calendar.dart] JavaneseCalendar::gregorianToHijri
    // 0x821d44: r0 = Null
    //     0x821d44: mov             x0, NULL
    // 0x821d48: LeaveFrame
    //     0x821d48: mov             SP, fp
    //     0x821d4c: ldp             fp, lr, [SP], #0x10
    // 0x821d50: ret
    //     0x821d50: ret             
    // 0x821d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x821d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x821d58: b               #0x821c40
  }
  _ gregorianToHijri(/* No info */) {
    // ** addr: 0x821d5c, size: 0x284
    // 0x821d5c: EnterFrame
    //     0x821d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x821d60: mov             fp, SP
    // 0x821d64: cmp             x3, #3
    // 0x821d68: b.ge            #0x821d80
    // 0x821d6c: sub             x4, x2, #1
    // 0x821d70: add             x6, x3, #0xc
    // 0x821d74: mov             x3, x4
    // 0x821d78: mov             x4, x6
    // 0x821d7c: b               #0x821d88
    // 0x821d80: mov             x4, x3
    // 0x821d84: mov             x3, x2
    // 0x821d88: d3 = 100.000000
    //     0x821d88: ldr             d3, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0x821d8c: d2 = 4.000000
    //     0x821d8c: fmov            d2, #4.00000000
    // 0x821d90: d1 = 365.250000
    //     0x821d90: add             x17, PP, #8, lsl #12  ; [pp+0x8e20] IMM: double(365.25) from 0x4076d40000000000
    //     0x821d94: ldr             d1, [x17, #0xe20]
    // 0x821d98: d0 = 30.600100
    //     0x821d98: add             x17, PP, #8, lsl #12  ; [pp+0x8e28] IMM: double(30.6001) from 0x403e99a027525461
    //     0x821d9c: ldr             d0, [x17, #0xe28]
    // 0x821da0: r2 = 5
    //     0x821da0: movz            x2, #0x5
    // 0x821da4: scvtf           d4, x3
    // 0x821da8: fdiv            d5, d4, d3
    // 0x821dac: fcmp            d5, d5
    // 0x821db0: b.vs            #0x821ed0
    // 0x821db4: fcvtms          x6, d5
    // 0x821db8: asr             x16, x6, #0x1e
    // 0x821dbc: cmp             x16, x6, asr #63
    // 0x821dc0: b.ne            #0x821ed0
    // 0x821dc4: lsl             x6, x6, #1
    // 0x821dc8: r7 = LoadInt32Instr(r6)
    //     0x821dc8: sbfx            x7, x6, #1, #0x1f
    //     0x821dcc: tbz             w6, #0, #0x821dd4
    //     0x821dd0: ldur            x7, [x6, #7]
    // 0x821dd4: scvtf           d3, x7
    // 0x821dd8: fdiv            d4, d3, d2
    // 0x821ddc: fcmp            d4, d4
    // 0x821de0: b.vs            #0x821f14
    // 0x821de4: fcvtms          x6, d4
    // 0x821de8: asr             x16, x6, #0x1e
    // 0x821dec: cmp             x16, x6, asr #63
    // 0x821df0: b.ne            #0x821f14
    // 0x821df4: lsl             x6, x6, #1
    // 0x821df8: r8 = LoadInt32Instr(r6)
    //     0x821df8: sbfx            x8, x6, #1, #0x1f
    //     0x821dfc: tbz             w6, #0, #0x821e04
    //     0x821e00: ldur            x8, [x6, #7]
    // 0x821e04: sub             x6, x7, x8
    // 0x821e08: sub             x7, x6, #2
    // 0x821e0c: r17 = 4716
    //     0x821e0c: movz            x17, #0x126c
    // 0x821e10: add             x6, x3, x17
    // 0x821e14: scvtf           d2, x6
    // 0x821e18: fmul            d3, d2, d1
    // 0x821e1c: fcmp            d3, d3
    // 0x821e20: b.vs            #0x821f58
    // 0x821e24: fcvtms          x3, d3
    // 0x821e28: asr             x16, x3, #0x1e
    // 0x821e2c: cmp             x16, x3, asr #63
    // 0x821e30: b.ne            #0x821f58
    // 0x821e34: lsl             x3, x3, #1
    // 0x821e38: add             x6, x4, #1
    // 0x821e3c: scvtf           d1, x6
    // 0x821e40: fmul            d2, d1, d0
    // 0x821e44: fcmp            d2, d2
    // 0x821e48: b.vs            #0x821f94
    // 0x821e4c: fcvtms          x4, d2
    // 0x821e50: asr             x16, x4, #0x1e
    // 0x821e54: cmp             x16, x4, asr #63
    // 0x821e58: b.ne            #0x821f94
    // 0x821e5c: lsl             x4, x4, #1
    // 0x821e60: r6 = LoadInt32Instr(r3)
    //     0x821e60: sbfx            x6, x3, #1, #0x1f
    //     0x821e64: tbz             w3, #0, #0x821e6c
    //     0x821e68: ldur            x6, [x3, #7]
    // 0x821e6c: r3 = LoadInt32Instr(r4)
    //     0x821e6c: sbfx            x3, x4, #1, #0x1f
    //     0x821e70: tbz             w4, #0, #0x821e78
    //     0x821e74: ldur            x3, [x4, #7]
    // 0x821e78: add             x4, x6, x3
    // 0x821e7c: add             x3, x4, x5
    // 0x821e80: sub             x4, x3, x7
    // 0x821e84: sub             x3, x4, #0x5f4
    // 0x821e88: add             x4, x3, #1
    // 0x821e8c: sdiv            x5, x4, x2
    // 0x821e90: msub            x3, x5, x2, x4
    // 0x821e94: cmp             x3, xzr
    // 0x821e98: b.lt            #0x821fd0
    // 0x821e9c: add             x4, x3, #5
    // 0x821ea0: sdiv            x5, x4, x2
    // 0x821ea4: msub            x3, x5, x2, x4
    // 0x821ea8: cmp             x3, xzr
    // 0x821eac: b.lt            #0x821fd8
    // 0x821eb0: cbnz            x3, #0x821ebc
    // 0x821eb4: r0 = 5
    //     0x821eb4: movz            x0, #0x5
    // 0x821eb8: b               #0x821ec0
    // 0x821ebc: mov             x0, x3
    // 0x821ec0: StoreField: r1->field_7 = r0
    //     0x821ec0: stur            x0, [x1, #7]
    // 0x821ec4: LeaveFrame
    //     0x821ec4: mov             SP, fp
    //     0x821ec8: ldp             fp, lr, [SP], #0x10
    // 0x821ecc: ret
    //     0x821ecc: ret             
    // 0x821ed0: stp             q2, q5, [SP, #-0x20]!
    // 0x821ed4: stp             q0, q1, [SP, #-0x20]!
    // 0x821ed8: stp             x4, x5, [SP, #-0x10]!
    // 0x821edc: stp             x2, x3, [SP, #-0x10]!
    // 0x821ee0: SaveReg r1
    //     0x821ee0: str             x1, [SP, #-8]!
    // 0x821ee4: d0 = 0.000000
    //     0x821ee4: fmov            d0, d5
    // 0x821ee8: r0 = 68
    //     0x821ee8: movz            x0, #0x44
    // 0x821eec: r30 = DoubleToIntegerStub
    //     0x821eec: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x821ef0: LoadField: r30 = r30->field_7
    //     0x821ef0: ldur            lr, [lr, #7]
    // 0x821ef4: blr             lr
    // 0x821ef8: mov             x6, x0
    // 0x821efc: RestoreReg r1
    //     0x821efc: ldr             x1, [SP], #8
    // 0x821f00: ldp             x2, x3, [SP], #0x10
    // 0x821f04: ldp             x4, x5, [SP], #0x10
    // 0x821f08: ldp             q0, q1, [SP], #0x20
    // 0x821f0c: ldp             q2, q5, [SP], #0x20
    // 0x821f10: b               #0x821dc8
    // 0x821f14: stp             q1, q4, [SP, #-0x20]!
    // 0x821f18: SaveReg d0
    //     0x821f18: str             q0, [SP, #-0x10]!
    // 0x821f1c: stp             x5, x7, [SP, #-0x10]!
    // 0x821f20: stp             x3, x4, [SP, #-0x10]!
    // 0x821f24: stp             x1, x2, [SP, #-0x10]!
    // 0x821f28: d0 = 0.000000
    //     0x821f28: fmov            d0, d4
    // 0x821f2c: r0 = 68
    //     0x821f2c: movz            x0, #0x44
    // 0x821f30: r30 = DoubleToIntegerStub
    //     0x821f30: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x821f34: LoadField: r30 = r30->field_7
    //     0x821f34: ldur            lr, [lr, #7]
    // 0x821f38: blr             lr
    // 0x821f3c: mov             x6, x0
    // 0x821f40: ldp             x1, x2, [SP], #0x10
    // 0x821f44: ldp             x3, x4, [SP], #0x10
    // 0x821f48: ldp             x5, x7, [SP], #0x10
    // 0x821f4c: RestoreReg d0
    //     0x821f4c: ldr             q0, [SP], #0x10
    // 0x821f50: ldp             q1, q4, [SP], #0x20
    // 0x821f54: b               #0x821df8
    // 0x821f58: stp             q0, q3, [SP, #-0x20]!
    // 0x821f5c: stp             x5, x7, [SP, #-0x10]!
    // 0x821f60: stp             x2, x4, [SP, #-0x10]!
    // 0x821f64: SaveReg r1
    //     0x821f64: str             x1, [SP, #-8]!
    // 0x821f68: d0 = 0.000000
    //     0x821f68: fmov            d0, d3
    // 0x821f6c: r0 = 68
    //     0x821f6c: movz            x0, #0x44
    // 0x821f70: r30 = DoubleToIntegerStub
    //     0x821f70: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x821f74: LoadField: r30 = r30->field_7
    //     0x821f74: ldur            lr, [lr, #7]
    // 0x821f78: blr             lr
    // 0x821f7c: mov             x3, x0
    // 0x821f80: RestoreReg r1
    //     0x821f80: ldr             x1, [SP], #8
    // 0x821f84: ldp             x2, x4, [SP], #0x10
    // 0x821f88: ldp             x5, x7, [SP], #0x10
    // 0x821f8c: ldp             q0, q3, [SP], #0x20
    // 0x821f90: b               #0x821e38
    // 0x821f94: SaveReg d2
    //     0x821f94: str             q2, [SP, #-0x10]!
    // 0x821f98: stp             x5, x7, [SP, #-0x10]!
    // 0x821f9c: stp             x2, x3, [SP, #-0x10]!
    // 0x821fa0: SaveReg r1
    //     0x821fa0: str             x1, [SP, #-8]!
    // 0x821fa4: d0 = 0.000000
    //     0x821fa4: fmov            d0, d2
    // 0x821fa8: r0 = 68
    //     0x821fa8: movz            x0, #0x44
    // 0x821fac: r30 = DoubleToIntegerStub
    //     0x821fac: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x821fb0: LoadField: r30 = r30->field_7
    //     0x821fb0: ldur            lr, [lr, #7]
    // 0x821fb4: blr             lr
    // 0x821fb8: mov             x4, x0
    // 0x821fbc: RestoreReg r1
    //     0x821fbc: ldr             x1, [SP], #8
    // 0x821fc0: ldp             x2, x3, [SP], #0x10
    // 0x821fc4: ldp             x5, x7, [SP], #0x10
    // 0x821fc8: RestoreReg d2
    //     0x821fc8: ldr             q2, [SP], #0x10
    // 0x821fcc: b               #0x821e60
    // 0x821fd0: add             x3, x3, x2
    // 0x821fd4: b               #0x821e9c
    // 0x821fd8: add             x3, x3, x2
    // 0x821fdc: b               #0x821eb0
  }
}
