// lib: , url: package:nuonline/app/modules/doa/bindings/doa_binding.dart

// class id: 1050167, size: 0x8
class :: {
}

// class id: 2187, size: 0x8, field offset: 0x8
class DoaBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80f0e8, size: 0x70
    // 0x80f0e8: EnterFrame
    //     0x80f0e8: stp             fp, lr, [SP, #-0x10]!
    //     0x80f0ec: mov             fp, SP
    // 0x80f0f0: AllocStack(0x10)
    //     0x80f0f0: sub             SP, SP, #0x10
    // 0x80f0f4: CheckStackOverflow
    //     0x80f0f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f0f8: cmp             SP, x16
    //     0x80f0fc: b.ls            #0x80f150
    // 0x80f100: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f104: ldr             x0, [x0, #0x2670]
    //     0x80f108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f10c: cmp             w0, w16
    //     0x80f110: b.ne            #0x80f11c
    //     0x80f114: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f118: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f11c: r1 = Function '<anonymous closure>':.
    //     0x80f11c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e50] AnonymousClosure: (0x80f158), in [package:nuonline/app/modules/doa/bindings/doa_binding.dart] DoaBinding::dependencies (0x80f0e8)
    //     0x80f120: ldr             x1, [x1, #0xe50]
    // 0x80f124: r2 = Null
    //     0x80f124: mov             x2, NULL
    // 0x80f128: r0 = AllocateClosure()
    //     0x80f128: bl              #0xec1630  ; AllocateClosureStub
    // 0x80f12c: r16 = <DoaController>
    //     0x80f12c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bf8] TypeArguments: <DoaController>
    //     0x80f130: ldr             x16, [x16, #0xbf8]
    // 0x80f134: stp             x0, x16, [SP]
    // 0x80f138: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80f138: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80f13c: r0 = Inst.lazyPut()
    //     0x80f13c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80f140: r0 = Null
    //     0x80f140: mov             x0, NULL
    // 0x80f144: LeaveFrame
    //     0x80f144: mov             SP, fp
    //     0x80f148: ldp             fp, lr, [SP], #0x10
    // 0x80f14c: ret
    //     0x80f14c: ret             
    // 0x80f150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f154: b               #0x80f100
  }
  [closure] DoaController <anonymous closure>(dynamic) {
    // ** addr: 0x80f158, size: 0x40
    // 0x80f158: EnterFrame
    //     0x80f158: stp             fp, lr, [SP, #-0x10]!
    //     0x80f15c: mov             fp, SP
    // 0x80f160: AllocStack(0x8)
    //     0x80f160: sub             SP, SP, #8
    // 0x80f164: CheckStackOverflow
    //     0x80f164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f168: cmp             SP, x16
    //     0x80f16c: b.ls            #0x80f190
    // 0x80f170: r0 = DoaController()
    //     0x80f170: bl              #0x80f284  ; AllocateDoaControllerStub -> DoaController (size=0x30)
    // 0x80f174: mov             x1, x0
    // 0x80f178: stur            x0, [fp, #-8]
    // 0x80f17c: r0 = DoaController()
    //     0x80f17c: bl              #0x80f198  ; [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::DoaController
    // 0x80f180: ldur            x0, [fp, #-8]
    // 0x80f184: LeaveFrame
    //     0x80f184: mov             SP, fp
    //     0x80f188: ldp             fp, lr, [SP], #0x10
    // 0x80f18c: ret
    //     0x80f18c: ret             
    // 0x80f190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f194: b               #0x80f170
  }
}
