// lib: , url: package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart

// class id: 1050168, size: 0x8
class :: {
}

// class id: 1994, size: 0x2c, field offset: 0x24
//   transformed mixin,
abstract class _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin extends OfflineFirstNoSyncController<dynamic>
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0xb537b8, size: 0xbc
    // 0xb537b8: EnterFrame
    //     0xb537b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb537bc: mov             fp, SP
    // 0xb537c0: AllocStack(0x20)
    //     0xb537c0: sub             SP, SP, #0x20
    // 0xb537c4: SetupParameters(_DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0xb537c4: stur            x1, [fp, #-8]
    //     0xb537c8: mov             x16, x2
    //     0xb537cc: mov             x2, x1
    //     0xb537d0: mov             x1, x16
    //     0xb537d4: mov             x0, x3
    //     0xb537d8: stur            x1, [fp, #-0x10]
    // 0xb537dc: CheckStackOverflow
    //     0xb537dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb537e0: cmp             SP, x16
    //     0xb537e4: b.ls            #0xb5386c
    // 0xb537e8: StoreField: r2->field_27 = r0
    //     0xb537e8: stur            w0, [x2, #0x27]
    //     0xb537ec: ldurb           w16, [x2, #-1]
    //     0xb537f0: ldurb           w17, [x0, #-1]
    //     0xb537f4: and             x16, x17, x16, lsr #2
    //     0xb537f8: tst             x16, HEAP, lsr #32
    //     0xb537fc: b.eq            #0xb53804
    //     0xb53800: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb53804: LoadField: r0 = r2->field_23
    //     0xb53804: ldur            w0, [x2, #0x23]
    // 0xb53808: DecompressPointer r0
    //     0xb53808: add             x0, x0, HEAP, lsl #32
    // 0xb5380c: r3 = LoadClassIdInstr(r1)
    //     0xb5380c: ldur            x3, [x1, #-1]
    //     0xb53810: ubfx            x3, x3, #0xc, #0x14
    // 0xb53814: stp             x0, x1, [SP]
    // 0xb53818: mov             x0, x3
    // 0xb5381c: mov             lr, x0
    // 0xb53820: ldr             lr, [x21, lr, lsl #3]
    // 0xb53824: blr             lr
    // 0xb53828: tbz             w0, #4, #0xb53854
    // 0xb5382c: ldur            x1, [fp, #-8]
    // 0xb53830: ldur            x0, [fp, #-0x10]
    // 0xb53834: StoreField: r1->field_23 = r0
    //     0xb53834: stur            w0, [x1, #0x23]
    //     0xb53838: ldurb           w16, [x1, #-1]
    //     0xb5383c: ldurb           w17, [x0, #-1]
    //     0xb53840: and             x16, x17, x16, lsr #2
    //     0xb53844: tst             x16, HEAP, lsr #32
    //     0xb53848: b.eq            #0xb53850
    //     0xb5384c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb53850: b               #0xb53858
    // 0xb53854: ldur            x1, [fp, #-8]
    // 0xb53858: r0 = _notifyUpdate()
    //     0xb53858: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0xb5385c: r0 = Null
    //     0xb5385c: mov             x0, NULL
    // 0xb53860: LeaveFrame
    //     0xb53860: mov             SP, fp
    //     0xb53864: ldp             fp, lr, [SP], #0x10
    // 0xb53868: ret
    //     0xb53868: ret             
    // 0xb5386c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5386c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb53870: b               #0xb537e8
  }
}

// class id: 1995, size: 0x3c, field offset: 0x2c
class DoaCategoryBuilderController extends _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin {

  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0x6fb34c, size: 0x60
    // 0x6fb34c: EnterFrame
    //     0x6fb34c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb350: mov             fp, SP
    // 0x6fb354: AllocStack(0x10)
    //     0x6fb354: sub             SP, SP, #0x10
    // 0x6fb358: SetupParameters(DoaCategoryBuilderController this /* r1 => r1, fp-0x10 */)
    //     0x6fb358: stur            NULL, [fp, #-8]
    //     0x6fb35c: stur            x1, [fp, #-0x10]
    // 0x6fb360: CheckStackOverflow
    //     0x6fb360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fb364: cmp             SP, x16
    //     0x6fb368: b.ls            #0x6fb3a4
    // 0x6fb36c: InitAsync() -> Future<ApiResult<List<DoaCategory>>>
    //     0x6fb36c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40680] TypeArguments: <ApiResult<List<DoaCategory>>>
    //     0x6fb370: ldr             x0, [x0, #0x680]
    //     0x6fb374: bl              #0x661298  ; InitAsyncStub
    // 0x6fb378: ldur            x0, [fp, #-0x10]
    // 0x6fb37c: LoadField: r1 = r0->field_33
    //     0x6fb37c: ldur            w1, [x0, #0x33]
    // 0x6fb380: DecompressPointer r1
    //     0x6fb380: add             x1, x1, HEAP, lsl #32
    // 0x6fb384: LoadField: r2 = r0->field_2b
    //     0x6fb384: ldur            w2, [x0, #0x2b]
    // 0x6fb388: DecompressPointer r2
    //     0x6fb388: add             x2, x2, HEAP, lsl #32
    // 0x6fb38c: r0 = LoadClassIdInstr(r1)
    //     0x6fb38c: ldur            x0, [x1, #-1]
    //     0x6fb390: ubfx            x0, x0, #0xc, #0x14
    // 0x6fb394: r0 = GDT[cid_x0 + -0xffb]()
    //     0x6fb394: sub             lr, x0, #0xffb
    //     0x6fb398: ldr             lr, [x21, lr, lsl #3]
    //     0x6fb39c: blr             lr
    // 0x6fb3a0: r0 = ReturnAsync()
    //     0x6fb3a0: b               #0x6576a4  ; ReturnAsyncStub
    // 0x6fb3a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fb3a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fb3a8: b               #0x6fb36c
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0x7f5dd4, size: 0x60
    // 0x7f5dd4: EnterFrame
    //     0x7f5dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x7f5dd8: mov             fp, SP
    // 0x7f5ddc: AllocStack(0x20)
    //     0x7f5ddc: sub             SP, SP, #0x20
    // 0x7f5de0: SetupParameters(DoaCategoryBuilderController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7f5de0: stur            NULL, [fp, #-8]
    //     0x7f5de4: stur            x1, [fp, #-0x10]
    //     0x7f5de8: stur            x2, [fp, #-0x18]
    //     0x7f5dec: stur            x3, [fp, #-0x20]
    // 0x7f5df0: CheckStackOverflow
    //     0x7f5df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f5df4: cmp             SP, x16
    //     0x7f5df8: b.ls            #0x7f5e2c
    // 0x7f5dfc: InitAsync() -> Future<void?>
    //     0x7f5dfc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7f5e00: bl              #0x661298  ; InitAsyncStub
    // 0x7f5e04: ldur            x0, [fp, #-0x10]
    // 0x7f5e08: LoadField: r1 = r0->field_2f
    //     0x7f5e08: ldur            w1, [x0, #0x2f]
    // 0x7f5e0c: DecompressPointer r1
    //     0x7f5e0c: add             x1, x1, HEAP, lsl #32
    // 0x7f5e10: r0 = LoadClassIdInstr(r1)
    //     0x7f5e10: ldur            x0, [x1, #-1]
    //     0x7f5e14: ubfx            x0, x0, #0xc, #0x14
    // 0x7f5e18: ldur            x2, [fp, #-0x18]
    // 0x7f5e1c: r0 = GDT[cid_x0 + -0xfdb]()
    //     0x7f5e1c: sub             lr, x0, #0xfdb
    //     0x7f5e20: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5e24: blr             lr
    // 0x7f5e28: r0 = ReturnAsync()
    //     0x7f5e28: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7f5e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f5e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5e30: b               #0x7f5dfc
  }
  _ onOfflineModeRequested(/* No info */) {
    // ** addr: 0xbef238, size: 0x84
    // 0xbef238: EnterFrame
    //     0xbef238: stp             fp, lr, [SP, #-0x10]!
    //     0xbef23c: mov             fp, SP
    // 0xbef240: AllocStack(0x8)
    //     0xbef240: sub             SP, SP, #8
    // 0xbef244: SetupParameters(DoaCategoryBuilderController this /* r1 => r1, fp-0x8 */)
    //     0xbef244: stur            x1, [fp, #-8]
    // 0xbef248: CheckStackOverflow
    //     0xbef248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef24c: cmp             SP, x16
    //     0xbef250: b.ls            #0xbef2b4
    // 0xbef254: r0 = RxStatus()
    //     0xbef254: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbef258: mov             x1, x0
    // 0xbef25c: r0 = false
    //     0xbef25c: add             x0, NULL, #0x30  ; false
    // 0xbef260: StoreField: r1->field_f = r0
    //     0xbef260: stur            w0, [x1, #0xf]
    // 0xbef264: r2 = true
    //     0xbef264: add             x2, NULL, #0x20  ; true
    // 0xbef268: StoreField: r1->field_7 = r2
    //     0xbef268: stur            w2, [x1, #7]
    // 0xbef26c: StoreField: r1->field_b = r0
    //     0xbef26c: stur            w0, [x1, #0xb]
    // 0xbef270: mov             x3, x1
    // 0xbef274: ldur            x1, [fp, #-8]
    // 0xbef278: r2 = Null
    //     0xbef278: mov             x2, NULL
    // 0xbef27c: r0 = change()
    //     0xbef27c: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbef280: ldur            x0, [fp, #-8]
    // 0xbef284: LoadField: r1 = r0->field_2f
    //     0xbef284: ldur            w1, [x0, #0x2f]
    // 0xbef288: DecompressPointer r1
    //     0xbef288: add             x1, x1, HEAP, lsl #32
    // 0xbef28c: LoadField: r2 = r0->field_2b
    //     0xbef28c: ldur            w2, [x0, #0x2b]
    // 0xbef290: DecompressPointer r2
    //     0xbef290: add             x2, x2, HEAP, lsl #32
    // 0xbef294: r0 = LoadClassIdInstr(r1)
    //     0xbef294: ldur            x0, [x1, #-1]
    //     0xbef298: ubfx            x0, x0, #0xc, #0x14
    // 0xbef29c: r0 = GDT[cid_x0 + -0xffb]()
    //     0xbef29c: sub             lr, x0, #0xffb
    //     0xbef2a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbef2a4: blr             lr
    // 0xbef2a8: LeaveFrame
    //     0xbef2a8: mov             SP, fp
    //     0xbef2ac: ldp             fp, lr, [SP], #0x10
    // 0xbef2b0: ret
    //     0xbef2b0: ret             
    // 0xbef2b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef2b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef2b8: b               #0xbef254
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf5978, size: 0xec
    // 0xbf5978: EnterFrame
    //     0xbf5978: stp             fp, lr, [SP, #-0x10]!
    //     0xbf597c: mov             fp, SP
    // 0xbf5980: AllocStack(0x38)
    //     0xbf5980: sub             SP, SP, #0x38
    // 0xbf5984: SetupParameters(DoaCategoryBuilderController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbf5984: stur            NULL, [fp, #-8]
    //     0xbf5988: stur            x1, [fp, #-0x10]
    //     0xbf598c: stur            x2, [fp, #-0x18]
    // 0xbf5990: CheckStackOverflow
    //     0xbf5990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5994: cmp             SP, x16
    //     0xbf5998: b.ls            #0xbf5a5c
    // 0xbf599c: r1 = 1
    //     0xbf599c: movz            x1, #0x1
    // 0xbf59a0: r0 = AllocateContext()
    //     0xbf59a0: bl              #0xec126c  ; AllocateContextStub
    // 0xbf59a4: mov             x2, x0
    // 0xbf59a8: ldur            x1, [fp, #-0x10]
    // 0xbf59ac: stur            x2, [fp, #-0x20]
    // 0xbf59b0: StoreField: r2->field_f = r1
    //     0xbf59b0: stur            w1, [x2, #0xf]
    // 0xbf59b4: InitAsync() -> Future<void?>
    //     0xbf59b4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf59b8: bl              #0x661298  ; InitAsyncStub
    // 0xbf59bc: ldur            x2, [fp, #-0x10]
    // 0xbf59c0: LoadField: r1 = r2->field_37
    //     0xbf59c0: ldur            w1, [x2, #0x37]
    // 0xbf59c4: DecompressPointer r1
    //     0xbf59c4: add             x1, x1, HEAP, lsl #32
    // 0xbf59c8: r0 = LoadClassIdInstr(r1)
    //     0xbf59c8: ldur            x0, [x1, #-1]
    //     0xbf59cc: ubfx            x0, x0, #0xc, #0x14
    // 0xbf59d0: r0 = GDT[cid_x0 + 0xd488]()
    //     0xbf59d0: movz            x17, #0xd488
    //     0xbf59d4: add             lr, x0, x17
    //     0xbf59d8: ldr             lr, [x21, lr, lsl #3]
    //     0xbf59dc: blr             lr
    // 0xbf59e0: tbnz            w0, #4, #0xbf5a24
    // 0xbf59e4: ldur            x2, [fp, #-0x20]
    // 0xbf59e8: r1 = Function '<anonymous closure>':.
    //     0xbf59e8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40688] AnonymousClosure: (0xbf5bf8), in [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] DoaCategoryBuilderController::onOfflineModeLoaded (0xbf5978)
    //     0xbf59ec: ldr             x1, [x1, #0x688]
    // 0xbf59f0: r0 = AllocateClosure()
    //     0xbf59f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf59f4: r16 = <DoaCategory>
    //     0xbf59f4: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xbf59f8: ldur            lr, [fp, #-0x18]
    // 0xbf59fc: stp             lr, x16, [SP, #8]
    // 0xbf5a00: str             x0, [SP]
    // 0xbf5a04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf5a04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf5a08: r0 = ListExtensions.whereIndexed()
    //     0xbf5a08: bl              #0xbf5a64  ; [package:collection/src/list_extensions.dart] ::ListExtensions.whereIndexed
    // 0xbf5a0c: LoadField: r1 = r0->field_7
    //     0xbf5a0c: ldur            w1, [x0, #7]
    // 0xbf5a10: DecompressPointer r1
    //     0xbf5a10: add             x1, x1, HEAP, lsl #32
    // 0xbf5a14: mov             x2, x0
    // 0xbf5a18: r0 = _GrowableList.of()
    //     0xbf5a18: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbf5a1c: mov             x2, x0
    // 0xbf5a20: b               #0xbf5a28
    // 0xbf5a24: ldur            x2, [fp, #-0x18]
    // 0xbf5a28: stur            x2, [fp, #-0x18]
    // 0xbf5a2c: r0 = RxStatus()
    //     0xbf5a2c: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5a30: mov             x1, x0
    // 0xbf5a34: r0 = false
    //     0xbf5a34: add             x0, NULL, #0x30  ; false
    // 0xbf5a38: StoreField: r1->field_f = r0
    //     0xbf5a38: stur            w0, [x1, #0xf]
    // 0xbf5a3c: StoreField: r1->field_7 = r0
    //     0xbf5a3c: stur            w0, [x1, #7]
    // 0xbf5a40: StoreField: r1->field_b = r0
    //     0xbf5a40: stur            w0, [x1, #0xb]
    // 0xbf5a44: mov             x3, x1
    // 0xbf5a48: ldur            x1, [fp, #-0x10]
    // 0xbf5a4c: ldur            x2, [fp, #-0x18]
    // 0xbf5a50: r0 = change()
    //     0xbf5a50: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbf5a54: r0 = Null
    //     0xbf5a54: mov             x0, NULL
    // 0xbf5a58: r0 = ReturnAsyncNotFuture()
    //     0xbf5a58: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf5a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5a60: b               #0xbf599c
  }
  [closure] bool <anonymous closure>(dynamic, int, DoaCategory) {
    // ** addr: 0xbf5bf8, size: 0x54
    // 0xbf5bf8: EnterFrame
    //     0xbf5bf8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5bfc: mov             fp, SP
    // 0xbf5c00: ldr             x0, [fp, #0x20]
    // 0xbf5c04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf5c04: ldur            w1, [x0, #0x17]
    // 0xbf5c08: DecompressPointer r1
    //     0xbf5c08: add             x1, x1, HEAP, lsl #32
    // 0xbf5c0c: CheckStackOverflow
    //     0xbf5c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5c10: cmp             SP, x16
    //     0xbf5c14: b.ls            #0xbf5c44
    // 0xbf5c18: LoadField: r0 = r1->field_f
    //     0xbf5c18: ldur            w0, [x1, #0xf]
    // 0xbf5c1c: DecompressPointer r0
    //     0xbf5c1c: add             x0, x0, HEAP, lsl #32
    // 0xbf5c20: LoadField: r1 = r0->field_37
    //     0xbf5c20: ldur            w1, [x0, #0x37]
    // 0xbf5c24: DecompressPointer r1
    //     0xbf5c24: add             x1, x1, HEAP, lsl #32
    // 0xbf5c28: ldr             x2, [fp, #0x18]
    // 0xbf5c2c: r0 = contains()
    //     0xbf5c2c: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xbf5c30: eor             x1, x0, #0x10
    // 0xbf5c34: mov             x0, x1
    // 0xbf5c38: LeaveFrame
    //     0xbf5c38: mov             SP, fp
    //     0xbf5c3c: ldp             fp, lr, [SP], #0x10
    // 0xbf5c40: ret
    //     0xbf5c40: ret             
    // 0xbf5c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5c48: b               #0xbf5c18
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xdc7120, size: 0x3c
    // 0xdc7120: EnterFrame
    //     0xdc7120: stp             fp, lr, [SP, #-0x10]!
    //     0xdc7124: mov             fp, SP
    // 0xdc7128: AllocStack(0x18)
    //     0xdc7128: sub             SP, SP, #0x18
    // 0xdc712c: SetupParameters(DoaCategoryBuilderController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xdc712c: stur            NULL, [fp, #-8]
    //     0xdc7130: stur            x1, [fp, #-0x10]
    //     0xdc7134: stur            x2, [fp, #-0x18]
    // 0xdc7138: CheckStackOverflow
    //     0xdc7138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc713c: cmp             SP, x16
    //     0xdc7140: b.ls            #0xdc7154
    // 0xdc7144: InitAsync() -> Future<void?>
    //     0xdc7144: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xdc7148: bl              #0x661298  ; InitAsyncStub
    // 0xdc714c: r0 = Null
    //     0xdc714c: mov             x0, NULL
    // 0xdc7150: r0 = ReturnAsyncNotFuture()
    //     0xdc7150: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xdc7154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc7154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc7158: b               #0xdc7144
  }
}
