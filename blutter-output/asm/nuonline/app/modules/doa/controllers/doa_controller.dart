// lib: , url: package:nuonline/app/modules/doa/controllers/doa_controller.dart

// class id: 1050169, size: 0x8
class :: {
}

// class id: 2030, size: 0x24, field offset: 0x24
//   transformed mixin,
abstract class _DoaController&GetxController&GetSingleTickerProviderStateMixin&AnalyticMixin extends _ArticleChannelSettingController&GetxController&GetSingleTickerProviderStateMixin
     with AnalyticMixin {
}

// class id: 2035, size: 0x30, field offset: 0x24
class DoaController extends _DoaController&GetxController&GetSingleTickerProviderStateMixin&AnalyticMixin {

  late TabController tabController; // offset: 0x24

  _ DoaController(/* No info */) {
    // ** addr: 0x80f198, size: 0xcc
    // 0x80f198: EnterFrame
    //     0x80f198: stp             fp, lr, [SP, #-0x10]!
    //     0x80f19c: mov             fp, SP
    // 0x80f1a0: AllocStack(0x18)
    //     0x80f1a0: sub             SP, SP, #0x18
    // 0x80f1a4: r0 = Sentinel
    //     0x80f1a4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80f1a8: mov             x2, x1
    // 0x80f1ac: stur            x1, [fp, #-8]
    // 0x80f1b0: CheckStackOverflow
    //     0x80f1b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f1b4: cmp             SP, x16
    //     0x80f1b8: b.ls            #0x80f25c
    // 0x80f1bc: StoreField: r2->field_23 = r0
    //     0x80f1bc: stur            w0, [x2, #0x23]
    // 0x80f1c0: r1 = 0
    //     0x80f1c0: movz            x1, #0
    // 0x80f1c4: r0 = IntExtension.obs()
    //     0x80f1c4: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80f1c8: ldur            x3, [fp, #-8]
    // 0x80f1cc: StoreField: r3->field_27 = r0
    //     0x80f1cc: stur            w0, [x3, #0x27]
    //     0x80f1d0: ldurb           w16, [x3, #-1]
    //     0x80f1d4: ldurb           w17, [x0, #-1]
    //     0x80f1d8: and             x16, x17, x16, lsr #2
    //     0x80f1dc: tst             x16, HEAP, lsr #32
    //     0x80f1e0: b.eq            #0x80f1e8
    //     0x80f1e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80f1e8: r1 = Null
    //     0x80f1e8: mov             x1, NULL
    // 0x80f1ec: r2 = 8
    //     0x80f1ec: movz            x2, #0x8
    // 0x80f1f0: r0 = AllocateArray()
    //     0x80f1f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x80f1f4: StoreField: r0->field_f = rZR
    //     0x80f1f4: stur            wzr, [x0, #0xf]
    // 0x80f1f8: r16 = "wirid"
    //     0x80f1f8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e58] "wirid"
    //     0x80f1fc: ldr             x16, [x16, #0xe58]
    // 0x80f200: StoreField: r0->field_13 = r16
    //     0x80f200: stur            w16, [x0, #0x13]
    // 0x80f204: r16 = 2
    //     0x80f204: movz            x16, #0x2
    // 0x80f208: ArrayStore: r0[0] = r16  ; List_4
    //     0x80f208: stur            w16, [x0, #0x17]
    // 0x80f20c: r16 = "doa"
    //     0x80f20c: add             x16, PP, #0xf, lsl #12  ; [pp+0xfb60] "doa"
    //     0x80f210: ldr             x16, [x16, #0xb60]
    // 0x80f214: StoreField: r0->field_1b = r16
    //     0x80f214: stur            w16, [x0, #0x1b]
    // 0x80f218: r16 = <int, String>
    //     0x80f218: add             x16, PP, #0x31, lsl #12  ; [pp+0x316d8] TypeArguments: <int, String>
    //     0x80f21c: ldr             x16, [x16, #0x6d8]
    // 0x80f220: stp             x0, x16, [SP]
    // 0x80f224: r0 = Map._fromLiteral()
    //     0x80f224: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x80f228: ldur            x1, [fp, #-8]
    // 0x80f22c: StoreField: r1->field_2b = r0
    //     0x80f22c: stur            w0, [x1, #0x2b]
    //     0x80f230: ldurb           w16, [x1, #-1]
    //     0x80f234: ldurb           w17, [x0, #-1]
    //     0x80f238: and             x16, x17, x16, lsr #2
    //     0x80f23c: tst             x16, HEAP, lsr #32
    //     0x80f240: b.eq            #0x80f248
    //     0x80f244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f248: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80f248: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80f24c: r0 = Null
    //     0x80f24c: mov             x0, NULL
    // 0x80f250: LeaveFrame
    //     0x80f250: mov             SP, fp
    //     0x80f254: ldp             fp, lr, [SP], #0x10
    // 0x80f258: ret
    //     0x80f258: ret             
    // 0x80f25c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f25c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f260: b               #0x80f1bc
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8ac3b0, size: 0xc4
    // 0x8ac3b0: EnterFrame
    //     0x8ac3b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ac3b4: mov             fp, SP
    // 0x8ac3b8: AllocStack(0x18)
    //     0x8ac3b8: sub             SP, SP, #0x18
    // 0x8ac3bc: SetupParameters(DoaController this /* r1 => r1, fp-0x8 */)
    //     0x8ac3bc: stur            x1, [fp, #-8]
    // 0x8ac3c0: CheckStackOverflow
    //     0x8ac3c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ac3c4: cmp             SP, x16
    //     0x8ac3c8: b.ls            #0x8ac46c
    // 0x8ac3cc: r1 = 1
    //     0x8ac3cc: movz            x1, #0x1
    // 0x8ac3d0: r0 = AllocateContext()
    //     0x8ac3d0: bl              #0xec126c  ; AllocateContextStub
    // 0x8ac3d4: mov             x2, x0
    // 0x8ac3d8: ldur            x0, [fp, #-8]
    // 0x8ac3dc: stur            x2, [fp, #-0x10]
    // 0x8ac3e0: StoreField: r2->field_f = r0
    //     0x8ac3e0: stur            w0, [x2, #0xf]
    // 0x8ac3e4: mov             x1, x0
    // 0x8ac3e8: r0 = onInit()
    //     0x8ac3e8: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8ac3ec: r0 = TabController()
    //     0x8ac3ec: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8ac3f0: mov             x1, x0
    // 0x8ac3f4: ldur            x3, [fp, #-8]
    // 0x8ac3f8: r2 = 2
    //     0x8ac3f8: movz            x2, #0x2
    // 0x8ac3fc: stur            x0, [fp, #-0x18]
    // 0x8ac400: r0 = TabController()
    //     0x8ac400: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8ac404: ldur            x0, [fp, #-0x18]
    // 0x8ac408: ldur            x2, [fp, #-8]
    // 0x8ac40c: StoreField: r2->field_23 = r0
    //     0x8ac40c: stur            w0, [x2, #0x23]
    //     0x8ac410: ldurb           w16, [x2, #-1]
    //     0x8ac414: ldurb           w17, [x0, #-1]
    //     0x8ac418: and             x16, x17, x16, lsr #2
    //     0x8ac41c: tst             x16, HEAP, lsr #32
    //     0x8ac420: b.eq            #0x8ac428
    //     0x8ac424: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8ac428: mov             x1, x2
    // 0x8ac42c: r0 = sendAnalytic()
    //     0x8ac42c: bl              #0x8ac474  ; [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::sendAnalytic
    // 0x8ac430: ldur            x0, [fp, #-8]
    // 0x8ac434: LoadField: r3 = r0->field_23
    //     0x8ac434: ldur            w3, [x0, #0x23]
    // 0x8ac438: DecompressPointer r3
    //     0x8ac438: add             x3, x3, HEAP, lsl #32
    // 0x8ac43c: ldur            x2, [fp, #-0x10]
    // 0x8ac440: stur            x3, [fp, #-0x18]
    // 0x8ac444: r1 = Function '<anonymous closure>':.
    //     0x8ac444: add             x1, PP, #0x40, lsl #12  ; [pp+0x40888] AnonymousClosure: (0x8ac5f8), in [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::onInit (0x8ac3b0)
    //     0x8ac448: ldr             x1, [x1, #0x888]
    // 0x8ac44c: r0 = AllocateClosure()
    //     0x8ac44c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ac450: ldur            x1, [fp, #-0x18]
    // 0x8ac454: mov             x2, x0
    // 0x8ac458: r0 = addListener()
    //     0x8ac458: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x8ac45c: r0 = Null
    //     0x8ac45c: mov             x0, NULL
    // 0x8ac460: LeaveFrame
    //     0x8ac460: mov             SP, fp
    //     0x8ac464: ldp             fp, lr, [SP], #0x10
    // 0x8ac468: ret
    //     0x8ac468: ret             
    // 0x8ac46c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ac46c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ac470: b               #0x8ac3cc
  }
  _ sendAnalytic(/* No info */) {
    // ** addr: 0x8ac474, size: 0x64
    // 0x8ac474: EnterFrame
    //     0x8ac474: stp             fp, lr, [SP, #-0x10]!
    //     0x8ac478: mov             fp, SP
    // 0x8ac47c: AllocStack(0x8)
    //     0x8ac47c: sub             SP, SP, #8
    // 0x8ac480: SetupParameters(DoaController this /* r1 => r1, fp-0x8 */)
    //     0x8ac480: stur            x1, [fp, #-8]
    // 0x8ac484: CheckStackOverflow
    //     0x8ac484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ac488: cmp             SP, x16
    //     0x8ac48c: b.ls            #0x8ac4d0
    // 0x8ac490: r1 = 1
    //     0x8ac490: movz            x1, #0x1
    // 0x8ac494: r0 = AllocateContext()
    //     0x8ac494: bl              #0xec126c  ; AllocateContextStub
    // 0x8ac498: mov             x1, x0
    // 0x8ac49c: ldur            x0, [fp, #-8]
    // 0x8ac4a0: StoreField: r1->field_f = r0
    //     0x8ac4a0: stur            w0, [x1, #0xf]
    // 0x8ac4a4: mov             x2, x1
    // 0x8ac4a8: r1 = Function '<anonymous closure>':.
    //     0x8ac4a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40890] AnonymousClosure: (0x8ac4d8), in [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::sendAnalytic (0x8ac474)
    //     0x8ac4ac: ldr             x1, [x1, #0x890]
    // 0x8ac4b0: r0 = AllocateClosure()
    //     0x8ac4b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ac4b4: ldur            x1, [fp, #-8]
    // 0x8ac4b8: mov             x2, x0
    // 0x8ac4bc: r0 = withAnalytic()
    //     0x8ac4bc: bl              #0x8abac8  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin::withAnalytic
    // 0x8ac4c0: r0 = Null
    //     0x8ac4c0: mov             x0, NULL
    // 0x8ac4c4: LeaveFrame
    //     0x8ac4c4: mov             SP, fp
    //     0x8ac4c8: ldp             fp, lr, [SP], #0x10
    // 0x8ac4cc: ret
    //     0x8ac4cc: ret             
    // 0x8ac4d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ac4d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ac4d4: b               #0x8ac490
  }
  [closure] void <anonymous closure>(dynamic, AnalyticService) {
    // ** addr: 0x8ac4d8, size: 0x120
    // 0x8ac4d8: EnterFrame
    //     0x8ac4d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ac4dc: mov             fp, SP
    // 0x8ac4e0: AllocStack(0x18)
    //     0x8ac4e0: sub             SP, SP, #0x18
    // 0x8ac4e4: SetupParameters()
    //     0x8ac4e4: ldr             x0, [fp, #0x18]
    //     0x8ac4e8: ldur            w3, [x0, #0x17]
    //     0x8ac4ec: add             x3, x3, HEAP, lsl #32
    //     0x8ac4f0: stur            x3, [fp, #-8]
    // 0x8ac4f4: CheckStackOverflow
    //     0x8ac4f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ac4f8: cmp             SP, x16
    //     0x8ac4fc: b.ls            #0x8ac5e4
    // 0x8ac500: r1 = Null
    //     0x8ac500: mov             x1, NULL
    // 0x8ac504: r2 = 4
    //     0x8ac504: movz            x2, #0x4
    // 0x8ac508: r0 = AllocateArray()
    //     0x8ac508: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ac50c: mov             x3, x0
    // 0x8ac510: stur            x3, [fp, #-0x10]
    // 0x8ac514: r16 = "doa/"
    //     0x8ac514: add             x16, PP, #0x40, lsl #12  ; [pp+0x40898] "doa/"
    //     0x8ac518: ldr             x16, [x16, #0x898]
    // 0x8ac51c: StoreField: r3->field_f = r16
    //     0x8ac51c: stur            w16, [x3, #0xf]
    // 0x8ac520: ldur            x0, [fp, #-8]
    // 0x8ac524: LoadField: r1 = r0->field_f
    //     0x8ac524: ldur            w1, [x0, #0xf]
    // 0x8ac528: DecompressPointer r1
    //     0x8ac528: add             x1, x1, HEAP, lsl #32
    // 0x8ac52c: LoadField: r4 = r1->field_2b
    //     0x8ac52c: ldur            w4, [x1, #0x2b]
    // 0x8ac530: DecompressPointer r4
    //     0x8ac530: add             x4, x4, HEAP, lsl #32
    // 0x8ac534: stur            x4, [fp, #-8]
    // 0x8ac538: LoadField: r0 = r1->field_23
    //     0x8ac538: ldur            w0, [x1, #0x23]
    // 0x8ac53c: DecompressPointer r0
    //     0x8ac53c: add             x0, x0, HEAP, lsl #32
    // 0x8ac540: r16 = Sentinel
    //     0x8ac540: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ac544: cmp             w0, w16
    // 0x8ac548: b.eq            #0x8ac5ec
    // 0x8ac54c: LoadField: r2 = r0->field_33
    //     0x8ac54c: ldur            x2, [x0, #0x33]
    // 0x8ac550: r0 = BoxInt64Instr(r2)
    //     0x8ac550: sbfiz           x0, x2, #1, #0x1f
    //     0x8ac554: cmp             x2, x0, asr #1
    //     0x8ac558: b.eq            #0x8ac564
    //     0x8ac55c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ac560: stur            x2, [x0, #7]
    // 0x8ac564: mov             x1, x4
    // 0x8ac568: mov             x2, x0
    // 0x8ac56c: r0 = _getValueOrData()
    //     0x8ac56c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8ac570: mov             x1, x0
    // 0x8ac574: ldur            x0, [fp, #-8]
    // 0x8ac578: LoadField: r2 = r0->field_f
    //     0x8ac578: ldur            w2, [x0, #0xf]
    // 0x8ac57c: DecompressPointer r2
    //     0x8ac57c: add             x2, x2, HEAP, lsl #32
    // 0x8ac580: cmp             w2, w1
    // 0x8ac584: b.ne            #0x8ac590
    // 0x8ac588: r0 = Null
    //     0x8ac588: mov             x0, NULL
    // 0x8ac58c: b               #0x8ac594
    // 0x8ac590: mov             x0, x1
    // 0x8ac594: ldur            x1, [fp, #-0x10]
    // 0x8ac598: ArrayStore: r1[1] = r0  ; List_4
    //     0x8ac598: add             x25, x1, #0x13
    //     0x8ac59c: str             w0, [x25]
    //     0x8ac5a0: tbz             w0, #0, #0x8ac5bc
    //     0x8ac5a4: ldurb           w16, [x1, #-1]
    //     0x8ac5a8: ldurb           w17, [x0, #-1]
    //     0x8ac5ac: and             x16, x17, x16, lsr #2
    //     0x8ac5b0: tst             x16, HEAP, lsr #32
    //     0x8ac5b4: b.eq            #0x8ac5bc
    //     0x8ac5b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ac5bc: ldur            x16, [fp, #-0x10]
    // 0x8ac5c0: str             x16, [SP]
    // 0x8ac5c4: r0 = _interpolate()
    //     0x8ac5c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ac5c8: ldr             x1, [fp, #0x10]
    // 0x8ac5cc: mov             x2, x0
    // 0x8ac5d0: r0 = sendCurrentScreen()
    //     0x8ac5d0: bl              #0x8abc70  ; [package:nuonline/services/analytic_service.dart] AnalyticService::sendCurrentScreen
    // 0x8ac5d4: r0 = Null
    //     0x8ac5d4: mov             x0, NULL
    // 0x8ac5d8: LeaveFrame
    //     0x8ac5d8: mov             SP, fp
    //     0x8ac5dc: ldp             fp, lr, [SP], #0x10
    // 0x8ac5e0: ret
    //     0x8ac5e0: ret             
    // 0x8ac5e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ac5e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ac5e8: b               #0x8ac500
    // 0x8ac5ec: r9 = tabController
    //     0x8ac5ec: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f7a8] Field <DoaController.tabController>: late (offset: 0x24)
    //     0x8ac5f0: ldr             x9, [x9, #0x7a8]
    // 0x8ac5f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ac5f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8ac5f8, size: 0xa4
    // 0x8ac5f8: EnterFrame
    //     0x8ac5f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ac5fc: mov             fp, SP
    // 0x8ac600: AllocStack(0x8)
    //     0x8ac600: sub             SP, SP, #8
    // 0x8ac604: SetupParameters()
    //     0x8ac604: ldr             x0, [fp, #0x10]
    //     0x8ac608: ldur            w3, [x0, #0x17]
    //     0x8ac60c: add             x3, x3, HEAP, lsl #32
    //     0x8ac610: stur            x3, [fp, #-8]
    // 0x8ac614: CheckStackOverflow
    //     0x8ac614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ac618: cmp             SP, x16
    //     0x8ac61c: b.ls            #0x8ac688
    // 0x8ac620: LoadField: r0 = r3->field_f
    //     0x8ac620: ldur            w0, [x3, #0xf]
    // 0x8ac624: DecompressPointer r0
    //     0x8ac624: add             x0, x0, HEAP, lsl #32
    // 0x8ac628: LoadField: r2 = r0->field_27
    //     0x8ac628: ldur            w2, [x0, #0x27]
    // 0x8ac62c: DecompressPointer r2
    //     0x8ac62c: add             x2, x2, HEAP, lsl #32
    // 0x8ac630: LoadField: r1 = r0->field_23
    //     0x8ac630: ldur            w1, [x0, #0x23]
    // 0x8ac634: DecompressPointer r1
    //     0x8ac634: add             x1, x1, HEAP, lsl #32
    // 0x8ac638: r16 = Sentinel
    //     0x8ac638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ac63c: cmp             w1, w16
    // 0x8ac640: b.eq            #0x8ac690
    // 0x8ac644: LoadField: r4 = r1->field_33
    //     0x8ac644: ldur            x4, [x1, #0x33]
    // 0x8ac648: r0 = BoxInt64Instr(r4)
    //     0x8ac648: sbfiz           x0, x4, #1, #0x1f
    //     0x8ac64c: cmp             x4, x0, asr #1
    //     0x8ac650: b.eq            #0x8ac65c
    //     0x8ac654: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ac658: stur            x4, [x0, #7]
    // 0x8ac65c: mov             x1, x2
    // 0x8ac660: mov             x2, x0
    // 0x8ac664: r0 = value=()
    //     0x8ac664: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ac668: ldur            x0, [fp, #-8]
    // 0x8ac66c: LoadField: r1 = r0->field_f
    //     0x8ac66c: ldur            w1, [x0, #0xf]
    // 0x8ac670: DecompressPointer r1
    //     0x8ac670: add             x1, x1, HEAP, lsl #32
    // 0x8ac674: r0 = sendAnalytic()
    //     0x8ac674: bl              #0x8ac474  ; [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::sendAnalytic
    // 0x8ac678: r0 = Null
    //     0x8ac678: mov             x0, NULL
    // 0x8ac67c: LeaveFrame
    //     0x8ac67c: mov             SP, fp
    //     0x8ac680: ldp             fp, lr, [SP], #0x10
    // 0x8ac684: ret
    //     0x8ac684: ret             
    // 0x8ac688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ac688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ac68c: b               #0x8ac620
    // 0x8ac690: r9 = tabController
    //     0x8ac690: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f7a8] Field <DoaController.tabController>: late (offset: 0x24)
    //     0x8ac694: ldr             x9, [x9, #0x7a8]
    // 0x8ac698: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ac698: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ onClose(/* No info */) {
    // ** addr: 0x92694c, size: 0x54
    // 0x92694c: EnterFrame
    //     0x92694c: stp             fp, lr, [SP, #-0x10]!
    //     0x926950: mov             fp, SP
    // 0x926954: CheckStackOverflow
    //     0x926954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926958: cmp             SP, x16
    //     0x92695c: b.ls            #0x92698c
    // 0x926960: LoadField: r0 = r1->field_23
    //     0x926960: ldur            w0, [x1, #0x23]
    // 0x926964: DecompressPointer r0
    //     0x926964: add             x0, x0, HEAP, lsl #32
    // 0x926968: r16 = Sentinel
    //     0x926968: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92696c: cmp             w0, w16
    // 0x926970: b.eq            #0x926994
    // 0x926974: mov             x1, x0
    // 0x926978: r0 = dispose()
    //     0x926978: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x92697c: r0 = Null
    //     0x92697c: mov             x0, NULL
    // 0x926980: LeaveFrame
    //     0x926980: mov             SP, fp
    //     0x926984: ldp             fp, lr, [SP], #0x10
    // 0x926988: ret
    //     0x926988: ret             
    // 0x92698c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92698c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926990: b               #0x926960
    // 0x926994: r9 = tabController
    //     0x926994: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f7a8] Field <DoaController.tabController>: late (offset: 0x24)
    //     0x926998: ldr             x9, [x9, #0x7a8]
    // 0x92699c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x92699c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void onValueChanged(dynamic, int?) {
    // ** addr: 0xadd21c, size: 0x3c
    // 0xadd21c: EnterFrame
    //     0xadd21c: stp             fp, lr, [SP, #-0x10]!
    //     0xadd220: mov             fp, SP
    // 0xadd224: ldr             x0, [fp, #0x18]
    // 0xadd228: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadd228: ldur            w1, [x0, #0x17]
    // 0xadd22c: DecompressPointer r1
    //     0xadd22c: add             x1, x1, HEAP, lsl #32
    // 0xadd230: CheckStackOverflow
    //     0xadd230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd234: cmp             SP, x16
    //     0xadd238: b.ls            #0xadd250
    // 0xadd23c: ldr             x2, [fp, #0x10]
    // 0xadd240: r0 = onValueChanged()
    //     0xadd240: bl              #0xadd258  ; [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::onValueChanged
    // 0xadd244: LeaveFrame
    //     0xadd244: mov             SP, fp
    //     0xadd248: ldp             fp, lr, [SP], #0x10
    // 0xadd24c: ret
    //     0xadd24c: ret             
    // 0xadd250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd254: b               #0xadd23c
  }
  _ onValueChanged(/* No info */) {
    // ** addr: 0xadd258, size: 0x90
    // 0xadd258: EnterFrame
    //     0xadd258: stp             fp, lr, [SP, #-0x10]!
    //     0xadd25c: mov             fp, SP
    // 0xadd260: AllocStack(0x10)
    //     0xadd260: sub             SP, SP, #0x10
    // 0xadd264: SetupParameters(DoaController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xadd264: mov             x3, x1
    //     0xadd268: mov             x0, x2
    //     0xadd26c: stur            x1, [fp, #-8]
    //     0xadd270: stur            x2, [fp, #-0x10]
    // 0xadd274: CheckStackOverflow
    //     0xadd274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd278: cmp             SP, x16
    //     0xadd27c: b.ls            #0xadd2d4
    // 0xadd280: cmp             w0, NULL
    // 0xadd284: b.eq            #0xadd2c4
    // 0xadd288: LoadField: r1 = r3->field_27
    //     0xadd288: ldur            w1, [x3, #0x27]
    // 0xadd28c: DecompressPointer r1
    //     0xadd28c: add             x1, x1, HEAP, lsl #32
    // 0xadd290: mov             x2, x0
    // 0xadd294: r0 = value=()
    //     0xadd294: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xadd298: ldur            x0, [fp, #-8]
    // 0xadd29c: LoadField: r1 = r0->field_23
    //     0xadd29c: ldur            w1, [x0, #0x23]
    // 0xadd2a0: DecompressPointer r1
    //     0xadd2a0: add             x1, x1, HEAP, lsl #32
    // 0xadd2a4: r16 = Sentinel
    //     0xadd2a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadd2a8: cmp             w1, w16
    // 0xadd2ac: b.eq            #0xadd2dc
    // 0xadd2b0: ldur            x0, [fp, #-0x10]
    // 0xadd2b4: r2 = LoadInt32Instr(r0)
    //     0xadd2b4: sbfx            x2, x0, #1, #0x1f
    //     0xadd2b8: tbz             w0, #0, #0xadd2c0
    //     0xadd2bc: ldur            x2, [x0, #7]
    // 0xadd2c0: r0 = animateTo()
    //     0xadd2c0: bl              #0xa06e80  ; [package:flutter/src/material/tab_controller.dart] TabController::animateTo
    // 0xadd2c4: r0 = Null
    //     0xadd2c4: mov             x0, NULL
    // 0xadd2c8: LeaveFrame
    //     0xadd2c8: mov             SP, fp
    //     0xadd2cc: ldp             fp, lr, [SP], #0x10
    // 0xadd2d0: ret
    //     0xadd2d0: ret             
    // 0xadd2d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd2d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd2d8: b               #0xadd280
    // 0xadd2dc: r9 = tabController
    //     0xadd2dc: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f7a8] Field <DoaController.tabController>: late (offset: 0x24)
    //     0xadd2e0: ldr             x9, [x9, #0x7a8]
    // 0xadd2e4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xadd2e4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
