// lib: , url: package:nuonline/app/modules/doa/doa_bookmark/bindings/doa_bookmark_binding.dart

// class id: 1050170, size: 0x8
class :: {
}

// class id: 2186, size: 0x8, field offset: 0x8
class DoaBookmarkBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80f290, size: 0x70
    // 0x80f290: EnterFrame
    //     0x80f290: stp             fp, lr, [SP, #-0x10]!
    //     0x80f294: mov             fp, SP
    // 0x80f298: AllocStack(0x10)
    //     0x80f298: sub             SP, SP, #0x10
    // 0x80f29c: CheckStackOverflow
    //     0x80f29c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f2a0: cmp             SP, x16
    //     0x80f2a4: b.ls            #0x80f2f8
    // 0x80f2a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f2a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f2ac: ldr             x0, [x0, #0x2670]
    //     0x80f2b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f2b4: cmp             w0, w16
    //     0x80f2b8: b.ne            #0x80f2c4
    //     0x80f2bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f2c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f2c4: r1 = Function '<anonymous closure>':.
    //     0x80f2c4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e48] AnonymousClosure: (0x80f300), in [package:nuonline/app/modules/doa/doa_bookmark/bindings/doa_bookmark_binding.dart] DoaBookmarkBinding::dependencies (0x80f290)
    //     0x80f2c8: ldr             x1, [x1, #0xe48]
    // 0x80f2cc: r2 = Null
    //     0x80f2cc: mov             x2, NULL
    // 0x80f2d0: r0 = AllocateClosure()
    //     0x80f2d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x80f2d4: r16 = <DoaBookmarkController>
    //     0x80f2d4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24c08] TypeArguments: <DoaBookmarkController>
    //     0x80f2d8: ldr             x16, [x16, #0xc08]
    // 0x80f2dc: stp             x0, x16, [SP]
    // 0x80f2e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80f2e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80f2e4: r0 = Inst.lazyPut()
    //     0x80f2e4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80f2e8: r0 = Null
    //     0x80f2e8: mov             x0, NULL
    // 0x80f2ec: LeaveFrame
    //     0x80f2ec: mov             SP, fp
    //     0x80f2f0: ldp             fp, lr, [SP], #0x10
    // 0x80f2f4: ret
    //     0x80f2f4: ret             
    // 0x80f2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f2f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f2fc: b               #0x80f2a8
  }
  [closure] DoaBookmarkController <anonymous closure>(dynamic) {
    // ** addr: 0x80f300, size: 0x54
    // 0x80f300: EnterFrame
    //     0x80f300: stp             fp, lr, [SP, #-0x10]!
    //     0x80f304: mov             fp, SP
    // 0x80f308: AllocStack(0x10)
    //     0x80f308: sub             SP, SP, #0x10
    // 0x80f30c: CheckStackOverflow
    //     0x80f30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f310: cmp             SP, x16
    //     0x80f314: b.ls            #0x80f34c
    // 0x80f318: r0 = find()
    //     0x80f318: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0x80f31c: stur            x0, [fp, #-8]
    // 0x80f320: r0 = DoaBookmarkController()
    //     0x80f320: bl              #0x80f354  ; AllocateDoaBookmarkControllerStub -> DoaBookmarkController (size=0x2c)
    // 0x80f324: mov             x2, x0
    // 0x80f328: ldur            x0, [fp, #-8]
    // 0x80f32c: stur            x2, [fp, #-0x10]
    // 0x80f330: StoreField: r2->field_27 = r0
    //     0x80f330: stur            w0, [x2, #0x27]
    // 0x80f334: mov             x1, x2
    // 0x80f338: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80f338: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80f33c: ldur            x0, [fp, #-0x10]
    // 0x80f340: LeaveFrame
    //     0x80f340: mov             SP, fp
    //     0x80f344: ldp             fp, lr, [SP], #0x10
    // 0x80f348: ret
    //     0x80f348: ret             
    // 0x80f34c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f34c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f350: b               #0x80f318
  }
}
