// lib: , url: package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart

// class id: 1050171, size: 0x8
class :: {
}

// class id: 1970, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _DoaBookmarkController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x72a6e0, size: 0xbc
    // 0x72a6e0: EnterFrame
    //     0x72a6e0: stp             fp, lr, [SP, #-0x10]!
    //     0x72a6e4: mov             fp, SP
    // 0x72a6e8: AllocStack(0x20)
    //     0x72a6e8: sub             SP, SP, #0x20
    // 0x72a6ec: SetupParameters(_DoaBookmarkController&GetxController&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x72a6ec: stur            x1, [fp, #-8]
    //     0x72a6f0: mov             x16, x2
    //     0x72a6f4: mov             x2, x1
    //     0x72a6f8: mov             x1, x16
    //     0x72a6fc: mov             x0, x3
    //     0x72a700: stur            x1, [fp, #-0x10]
    // 0x72a704: CheckStackOverflow
    //     0x72a704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72a708: cmp             SP, x16
    //     0x72a70c: b.ls            #0x72a794
    // 0x72a710: StoreField: r2->field_23 = r0
    //     0x72a710: stur            w0, [x2, #0x23]
    //     0x72a714: ldurb           w16, [x2, #-1]
    //     0x72a718: ldurb           w17, [x0, #-1]
    //     0x72a71c: and             x16, x17, x16, lsr #2
    //     0x72a720: tst             x16, HEAP, lsr #32
    //     0x72a724: b.eq            #0x72a72c
    //     0x72a728: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x72a72c: LoadField: r0 = r2->field_1f
    //     0x72a72c: ldur            w0, [x2, #0x1f]
    // 0x72a730: DecompressPointer r0
    //     0x72a730: add             x0, x0, HEAP, lsl #32
    // 0x72a734: r3 = LoadClassIdInstr(r1)
    //     0x72a734: ldur            x3, [x1, #-1]
    //     0x72a738: ubfx            x3, x3, #0xc, #0x14
    // 0x72a73c: stp             x0, x1, [SP]
    // 0x72a740: mov             x0, x3
    // 0x72a744: mov             lr, x0
    // 0x72a748: ldr             lr, [x21, lr, lsl #3]
    // 0x72a74c: blr             lr
    // 0x72a750: tbz             w0, #4, #0x72a77c
    // 0x72a754: ldur            x1, [fp, #-8]
    // 0x72a758: ldur            x0, [fp, #-0x10]
    // 0x72a75c: StoreField: r1->field_1f = r0
    //     0x72a75c: stur            w0, [x1, #0x1f]
    //     0x72a760: ldurb           w16, [x1, #-1]
    //     0x72a764: ldurb           w17, [x0, #-1]
    //     0x72a768: and             x16, x17, x16, lsr #2
    //     0x72a76c: tst             x16, HEAP, lsr #32
    //     0x72a770: b.eq            #0x72a778
    //     0x72a774: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72a778: b               #0x72a780
    // 0x72a77c: ldur            x1, [fp, #-8]
    // 0x72a780: r0 = _notifyUpdate()
    //     0x72a780: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x72a784: r0 = Null
    //     0x72a784: mov             x0, NULL
    // 0x72a788: LeaveFrame
    //     0x72a788: mov             SP, fp
    //     0x72a78c: ldp             fp, lr, [SP], #0x10
    // 0x72a790: ret
    //     0x72a790: ret             
    // 0x72a794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72a794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72a798: b               #0x72a710
  }
}

// class id: 1971, size: 0x28, field offset: 0x28
//   transformed mixin,
abstract class _DoaBookmarkController&GetxController&StateMixin&OfflineMixin extends _DoaBookmarkController&GetxController&StateMixin
     with OfflineMixin<X0> {

  _ executeOfflineMode(/* No info */) async {
    // ** addr: 0x8f06fc, size: 0x80
    // 0x8f06fc: EnterFrame
    //     0x8f06fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0700: mov             fp, SP
    // 0x8f0704: AllocStack(0x30)
    //     0x8f0704: sub             SP, SP, #0x30
    // 0x8f0708: SetupParameters(_DoaBookmarkController&GetxController&StateMixin&OfflineMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f0708: stur            NULL, [fp, #-8]
    //     0x8f070c: stur            x1, [fp, #-0x10]
    // 0x8f0710: CheckStackOverflow
    //     0x8f0710: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0714: cmp             SP, x16
    //     0x8f0718: b.ls            #0x8f0774
    // 0x8f071c: r1 = 1
    //     0x8f071c: movz            x1, #0x1
    // 0x8f0720: r0 = AllocateContext()
    //     0x8f0720: bl              #0xec126c  ; AllocateContextStub
    // 0x8f0724: mov             x2, x0
    // 0x8f0728: ldur            x1, [fp, #-0x10]
    // 0x8f072c: stur            x2, [fp, #-0x18]
    // 0x8f0730: StoreField: r2->field_f = r1
    //     0x8f0730: stur            w1, [x2, #0xf]
    // 0x8f0734: InitAsync() -> Future<void?>
    //     0x8f0734: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f0738: bl              #0x661298  ; InitAsyncStub
    // 0x8f073c: ldur            x1, [fp, #-0x10]
    // 0x8f0740: r0 = onOfflineModeRequested()
    //     0x8f0740: bl              #0xbef800  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] DoaBookmarkController::onOfflineModeRequested
    // 0x8f0744: ldur            x2, [fp, #-0x18]
    // 0x8f0748: r1 = Function '<anonymous closure>':.
    //     0x8f0748: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] AnonymousClosure: (0x8f077c), in [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f06fc)
    //     0x8f074c: ldr             x1, [x1, #0xa00]
    // 0x8f0750: stur            x0, [fp, #-0x10]
    // 0x8f0754: r0 = AllocateClosure()
    //     0x8f0754: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0758: r16 = <void?>
    //     0x8f0758: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f075c: ldur            lr, [fp, #-0x10]
    // 0x8f0760: stp             lr, x16, [SP, #8]
    // 0x8f0764: str             x0, [SP]
    // 0x8f0768: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f0768: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f076c: r0 = then()
    //     0x8f076c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f0770: r0 = ReturnAsync()
    //     0x8f0770: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f0774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0778: b               #0x8f071c
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<DoaSubCategory>>) {
    // ** addr: 0x8f077c, size: 0x94
    // 0x8f077c: EnterFrame
    //     0x8f077c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0780: mov             fp, SP
    // 0x8f0784: AllocStack(0x28)
    //     0x8f0784: sub             SP, SP, #0x28
    // 0x8f0788: SetupParameters()
    //     0x8f0788: ldr             x0, [fp, #0x18]
    //     0x8f078c: ldur            w3, [x0, #0x17]
    //     0x8f0790: add             x3, x3, HEAP, lsl #32
    //     0x8f0794: stur            x3, [fp, #-8]
    // 0x8f0798: CheckStackOverflow
    //     0x8f0798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f079c: cmp             SP, x16
    //     0x8f07a0: b.ls            #0x8f0808
    // 0x8f07a4: mov             x2, x3
    // 0x8f07a8: r1 = Function '<anonymous closure>':.
    //     0x8f07a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa08] AnonymousClosure: (0x8f0868), in [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f06fc)
    //     0x8f07ac: ldr             x1, [x1, #0xa08]
    // 0x8f07b0: r0 = AllocateClosure()
    //     0x8f07b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f07b4: ldur            x2, [fp, #-8]
    // 0x8f07b8: r1 = Function '<anonymous closure>':.
    //     0x8f07b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa10] AnonymousClosure: (0x8f0810), in [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f06fc)
    //     0x8f07bc: ldr             x1, [x1, #0xa10]
    // 0x8f07c0: stur            x0, [fp, #-8]
    // 0x8f07c4: r0 = AllocateClosure()
    //     0x8f07c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f07c8: mov             x1, x0
    // 0x8f07cc: ldr             x0, [fp, #0x10]
    // 0x8f07d0: r2 = LoadClassIdInstr(r0)
    //     0x8f07d0: ldur            x2, [x0, #-1]
    //     0x8f07d4: ubfx            x2, x2, #0xc, #0x14
    // 0x8f07d8: r16 = <Object?>
    //     0x8f07d8: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f07dc: stp             x0, x16, [SP, #0x10]
    // 0x8f07e0: ldur            x16, [fp, #-8]
    // 0x8f07e4: stp             x16, x1, [SP]
    // 0x8f07e8: mov             x0, x2
    // 0x8f07ec: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f07ec: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f07f0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f07f0: sub             lr, x0, #1, lsl #12
    //     0x8f07f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8f07f8: blr             lr
    // 0x8f07fc: LeaveFrame
    //     0x8f07fc: mov             SP, fp
    //     0x8f0800: ldp             fp, lr, [SP], #0x10
    // 0x8f0804: ret
    //     0x8f0804: ret             
    // 0x8f0808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f080c: b               #0x8f07a4
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f0810, size: 0x58
    // 0x8f0810: EnterFrame
    //     0x8f0810: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0814: mov             fp, SP
    // 0x8f0818: AllocStack(0x8)
    //     0x8f0818: sub             SP, SP, #8
    // 0x8f081c: SetupParameters()
    //     0x8f081c: ldr             x0, [fp, #0x18]
    //     0x8f0820: ldur            w1, [x0, #0x17]
    //     0x8f0824: add             x1, x1, HEAP, lsl #32
    // 0x8f0828: CheckStackOverflow
    //     0x8f0828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f082c: cmp             SP, x16
    //     0x8f0830: b.ls            #0x8f0860
    // 0x8f0834: LoadField: r0 = r1->field_f
    //     0x8f0834: ldur            w0, [x1, #0xf]
    // 0x8f0838: DecompressPointer r0
    //     0x8f0838: add             x0, x0, HEAP, lsl #32
    // 0x8f083c: ldr             x1, [fp, #0x10]
    // 0x8f0840: stur            x0, [fp, #-8]
    // 0x8f0844: r0 = getErrorMessage()
    //     0x8f0844: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f0848: ldur            x1, [fp, #-8]
    // 0x8f084c: mov             x2, x0
    // 0x8f0850: r0 = onOfflineModeFailure()
    //     0x8f0850: bl              #0xc2a418  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] DoaBookmarkController::onOfflineModeFailure
    // 0x8f0854: LeaveFrame
    //     0x8f0854: mov             SP, fp
    //     0x8f0858: ldp             fp, lr, [SP], #0x10
    // 0x8f085c: ret
    //     0x8f085c: ret             
    // 0x8f0860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0864: b               #0x8f0834
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<DoaSubCategory>, Pagination?) {
    // ** addr: 0x8f0868, size: 0x48
    // 0x8f0868: EnterFrame
    //     0x8f0868: stp             fp, lr, [SP, #-0x10]!
    //     0x8f086c: mov             fp, SP
    // 0x8f0870: ldr             x0, [fp, #0x20]
    // 0x8f0874: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0874: ldur            w1, [x0, #0x17]
    // 0x8f0878: DecompressPointer r1
    //     0x8f0878: add             x1, x1, HEAP, lsl #32
    // 0x8f087c: CheckStackOverflow
    //     0x8f087c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0880: cmp             SP, x16
    //     0x8f0884: b.ls            #0x8f08a8
    // 0x8f0888: LoadField: r0 = r1->field_f
    //     0x8f0888: ldur            w0, [x1, #0xf]
    // 0x8f088c: DecompressPointer r0
    //     0x8f088c: add             x0, x0, HEAP, lsl #32
    // 0x8f0890: mov             x1, x0
    // 0x8f0894: ldr             x2, [fp, #0x18]
    // 0x8f0898: r0 = onOfflineModeLoaded()
    //     0x8f0898: bl              #0xbf6250  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] DoaBookmarkController::onOfflineModeLoaded
    // 0x8f089c: LeaveFrame
    //     0x8f089c: mov             SP, fp
    //     0x8f08a0: ldp             fp, lr, [SP], #0x10
    // 0x8f08a4: ret
    //     0x8f08a4: ret             
    // 0x8f08a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f08a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f08ac: b               #0x8f0888
  }
}

// class id: 1972, size: 0x2c, field offset: 0x28
class DoaBookmarkController extends _DoaBookmarkController&GetxController&StateMixin&OfflineMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8f06b4, size: 0x48
    // 0x8f06b4: EnterFrame
    //     0x8f06b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f06b8: mov             fp, SP
    // 0x8f06bc: AllocStack(0x8)
    //     0x8f06bc: sub             SP, SP, #8
    // 0x8f06c0: SetupParameters(DoaBookmarkController this /* r1 => r0, fp-0x8 */)
    //     0x8f06c0: mov             x0, x1
    //     0x8f06c4: stur            x1, [fp, #-8]
    // 0x8f06c8: CheckStackOverflow
    //     0x8f06c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f06cc: cmp             SP, x16
    //     0x8f06d0: b.ls            #0x8f06f4
    // 0x8f06d4: mov             x1, x0
    // 0x8f06d8: r0 = onInit()
    //     0x8f06d8: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f06dc: ldur            x1, [fp, #-8]
    // 0x8f06e0: r0 = executeOfflineMode()
    //     0x8f06e0: bl              #0x8f06fc  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin&OfflineMixin::executeOfflineMode
    // 0x8f06e4: r0 = Null
    //     0x8f06e4: mov             x0, NULL
    // 0x8f06e8: LeaveFrame
    //     0x8f06e8: mov             SP, fp
    //     0x8f06ec: ldp             fp, lr, [SP], #0x10
    // 0x8f06f0: ret
    //     0x8f06f0: ret             
    // 0x8f06f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f06f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f06f8: b               #0x8f06d4
  }
  _ onPressed(/* No info */) async {
    // ** addr: 0xad781c, size: 0x110
    // 0xad781c: EnterFrame
    //     0xad781c: stp             fp, lr, [SP, #-0x10]!
    //     0xad7820: mov             fp, SP
    // 0xad7824: AllocStack(0x38)
    //     0xad7824: sub             SP, SP, #0x38
    // 0xad7828: SetupParameters(DoaBookmarkController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xad7828: stur            NULL, [fp, #-8]
    //     0xad782c: stur            x1, [fp, #-0x10]
    //     0xad7830: stur            x2, [fp, #-0x18]
    // 0xad7834: CheckStackOverflow
    //     0xad7834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad7838: cmp             SP, x16
    //     0xad783c: b.ls            #0xad7920
    // 0xad7840: InitAsync() -> Future<void?>
    //     0xad7840: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad7844: bl              #0x661298  ; InitAsyncStub
    // 0xad7848: ldur            x1, [fp, #-0x10]
    // 0xad784c: r0 = value()
    //     0xad784c: bl              #0x72cf78  ; [package:nuonline/app/modules/zakat/controllers/zakat_controller.dart] _ZakatController&GetxController&StateMixin::value
    // 0xad7850: cmp             w0, NULL
    // 0xad7854: b.eq            #0xad7928
    // 0xad7858: r1 = LoadClassIdInstr(r0)
    //     0xad7858: ldur            x1, [x0, #-1]
    //     0xad785c: ubfx            x1, x1, #0xc, #0x14
    // 0xad7860: mov             x16, x0
    // 0xad7864: mov             x0, x1
    // 0xad7868: mov             x1, x16
    // 0xad786c: ldur            x2, [fp, #-0x18]
    // 0xad7870: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xad7870: movz            x17, #0xd28f
    //     0xad7874: add             lr, x0, x17
    //     0xad7878: ldr             lr, [x21, lr, lsl #3]
    //     0xad787c: blr             lr
    // 0xad7880: stur            x0, [fp, #-0x20]
    // 0xad7884: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad7884: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad7888: ldr             x0, [x0, #0x2670]
    //     0xad788c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad7890: cmp             w0, w16
    //     0xad7894: b.ne            #0xad78a0
    //     0xad7898: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad789c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad78a0: r1 = Null
    //     0xad78a0: mov             x1, NULL
    // 0xad78a4: r2 = 4
    //     0xad78a4: movz            x2, #0x4
    // 0xad78a8: r0 = AllocateArray()
    //     0xad78a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad78ac: mov             x2, x0
    // 0xad78b0: r16 = "id"
    //     0xad78b0: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xad78b4: ldr             x16, [x16, #0x740]
    // 0xad78b8: StoreField: r2->field_f = r16
    //     0xad78b8: stur            w16, [x2, #0xf]
    // 0xad78bc: ldur            x0, [fp, #-0x20]
    // 0xad78c0: LoadField: r3 = r0->field_7
    //     0xad78c0: ldur            x3, [x0, #7]
    // 0xad78c4: r0 = BoxInt64Instr(r3)
    //     0xad78c4: sbfiz           x0, x3, #1, #0x1f
    //     0xad78c8: cmp             x3, x0, asr #1
    //     0xad78cc: b.eq            #0xad78d8
    //     0xad78d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad78d4: stur            x3, [x0, #7]
    // 0xad78d8: StoreField: r2->field_13 = r0
    //     0xad78d8: stur            w0, [x2, #0x13]
    // 0xad78dc: r16 = <String, int>
    //     0xad78dc: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xad78e0: stp             x2, x16, [SP]
    // 0xad78e4: r0 = Map._fromLiteral()
    //     0xad78e4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xad78e8: r16 = "/doa/doa-detail"
    //     0xad78e8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c220] "/doa/doa-detail"
    //     0xad78ec: ldr             x16, [x16, #0x220]
    // 0xad78f0: stp             x16, NULL, [SP, #8]
    // 0xad78f4: str             x0, [SP]
    // 0xad78f8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad78f8: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad78fc: ldr             x4, [x4, #0x478]
    // 0xad7900: r0 = GetNavigation.toNamed()
    //     0xad7900: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad7904: mov             x1, x0
    // 0xad7908: stur            x1, [fp, #-0x20]
    // 0xad790c: r0 = Await()
    //     0xad790c: bl              #0x661044  ; AwaitStub
    // 0xad7910: ldur            x1, [fp, #-0x10]
    // 0xad7914: r0 = executeOfflineMode()
    //     0xad7914: bl              #0x8f06fc  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin&OfflineMixin::executeOfflineMode
    // 0xad7918: r0 = Null
    //     0xad7918: mov             x0, NULL
    // 0xad791c: r0 = ReturnAsyncNotFuture()
    //     0xad791c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad7920: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad7920: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7924: b               #0xad7840
    // 0xad7928: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad7928: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef800, size: 0x58
    // 0xbef800: EnterFrame
    //     0xbef800: stp             fp, lr, [SP, #-0x10]!
    //     0xbef804: mov             fp, SP
    // 0xbef808: AllocStack(0x10)
    //     0xbef808: sub             SP, SP, #0x10
    // 0xbef80c: SetupParameters(DoaBookmarkController this /* r1 => r1, fp-0x10 */)
    //     0xbef80c: stur            NULL, [fp, #-8]
    //     0xbef810: stur            x1, [fp, #-0x10]
    // 0xbef814: CheckStackOverflow
    //     0xbef814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef818: cmp             SP, x16
    //     0xbef81c: b.ls            #0xbef850
    // 0xbef820: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xbef820: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xbef824: ldr             x0, [x0, #0xd38]
    //     0xbef828: bl              #0x661298  ; InitAsyncStub
    // 0xbef82c: ldur            x0, [fp, #-0x10]
    // 0xbef830: LoadField: r1 = r0->field_27
    //     0xbef830: ldur            w1, [x0, #0x27]
    // 0xbef834: DecompressPointer r1
    //     0xbef834: add             x1, x1, HEAP, lsl #32
    // 0xbef838: r0 = LoadClassIdInstr(r1)
    //     0xbef838: ldur            x0, [x1, #-1]
    //     0xbef83c: ubfx            x0, x0, #0xc, #0x14
    // 0xbef840: r0 = GDT[cid_x0 + -0xfca]()
    //     0xbef840: sub             lr, x0, #0xfca
    //     0xbef844: ldr             lr, [x21, lr, lsl #3]
    //     0xbef848: blr             lr
    // 0xbef84c: r0 = ReturnAsync()
    //     0xbef84c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef854: b               #0xbef820
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf6250, size: 0xbc
    // 0xbf6250: EnterFrame
    //     0xbf6250: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6254: mov             fp, SP
    // 0xbf6258: AllocStack(0x18)
    //     0xbf6258: sub             SP, SP, #0x18
    // 0xbf625c: SetupParameters(DoaBookmarkController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xbf625c: stur            NULL, [fp, #-8]
    //     0xbf6260: stur            x1, [fp, #-0x10]
    //     0xbf6264: mov             x16, x2
    //     0xbf6268: mov             x2, x1
    //     0xbf626c: mov             x1, x16
    //     0xbf6270: stur            x1, [fp, #-0x18]
    // 0xbf6274: CheckStackOverflow
    //     0xbf6274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6278: cmp             SP, x16
    //     0xbf627c: b.ls            #0xbf6304
    // 0xbf6280: InitAsync() -> Future<void?>
    //     0xbf6280: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf6284: bl              #0x661298  ; InitAsyncStub
    // 0xbf6288: ldur            x2, [fp, #-0x18]
    // 0xbf628c: r0 = LoadClassIdInstr(r2)
    //     0xbf628c: ldur            x0, [x2, #-1]
    //     0xbf6290: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6294: mov             x1, x2
    // 0xbf6298: r0 = GDT[cid_x0 + 0xe879]()
    //     0xbf6298: movz            x17, #0xe879
    //     0xbf629c: add             lr, x0, x17
    //     0xbf62a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf62a4: blr             lr
    // 0xbf62a8: tbnz            w0, #4, #0xbf62d0
    // 0xbf62ac: r0 = RxStatus()
    //     0xbf62ac: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf62b0: mov             x1, x0
    // 0xbf62b4: r0 = true
    //     0xbf62b4: add             x0, NULL, #0x20  ; true
    // 0xbf62b8: StoreField: r1->field_f = r0
    //     0xbf62b8: stur            w0, [x1, #0xf]
    // 0xbf62bc: r0 = false
    //     0xbf62bc: add             x0, NULL, #0x30  ; false
    // 0xbf62c0: StoreField: r1->field_7 = r0
    //     0xbf62c0: stur            w0, [x1, #7]
    // 0xbf62c4: StoreField: r1->field_b = r0
    //     0xbf62c4: stur            w0, [x1, #0xb]
    // 0xbf62c8: mov             x3, x1
    // 0xbf62cc: b               #0xbf62f0
    // 0xbf62d0: r0 = false
    //     0xbf62d0: add             x0, NULL, #0x30  ; false
    // 0xbf62d4: r0 = RxStatus()
    //     0xbf62d4: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf62d8: mov             x1, x0
    // 0xbf62dc: r0 = false
    //     0xbf62dc: add             x0, NULL, #0x30  ; false
    // 0xbf62e0: StoreField: r1->field_f = r0
    //     0xbf62e0: stur            w0, [x1, #0xf]
    // 0xbf62e4: StoreField: r1->field_7 = r0
    //     0xbf62e4: stur            w0, [x1, #7]
    // 0xbf62e8: StoreField: r1->field_b = r0
    //     0xbf62e8: stur            w0, [x1, #0xb]
    // 0xbf62ec: mov             x3, x1
    // 0xbf62f0: ldur            x1, [fp, #-0x10]
    // 0xbf62f4: ldur            x2, [fp, #-0x18]
    // 0xbf62f8: r0 = change()
    //     0xbf62f8: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0xbf62fc: r0 = Null
    //     0xbf62fc: mov             x0, NULL
    // 0xbf6300: r0 = ReturnAsyncNotFuture()
    //     0xbf6300: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf6304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf6304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6308: b               #0xbf6280
  }
  _ onOfflineModeFailure(/* No info */) async {
    // ** addr: 0xc2a418, size: 0x68
    // 0xc2a418: EnterFrame
    //     0xc2a418: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a41c: mov             fp, SP
    // 0xc2a420: AllocStack(0x18)
    //     0xc2a420: sub             SP, SP, #0x18
    // 0xc2a424: SetupParameters(DoaBookmarkController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc2a424: stur            NULL, [fp, #-8]
    //     0xc2a428: stur            x1, [fp, #-0x10]
    //     0xc2a42c: stur            x2, [fp, #-0x18]
    // 0xc2a430: CheckStackOverflow
    //     0xc2a430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a434: cmp             SP, x16
    //     0xc2a438: b.ls            #0xc2a478
    // 0xc2a43c: InitAsync() -> Future<void?>
    //     0xc2a43c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc2a440: bl              #0x661298  ; InitAsyncStub
    // 0xc2a444: r0 = RxStatus()
    //     0xc2a444: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xc2a448: mov             x1, x0
    // 0xc2a44c: r0 = true
    //     0xc2a44c: add             x0, NULL, #0x20  ; true
    // 0xc2a450: StoreField: r1->field_f = r0
    //     0xc2a450: stur            w0, [x1, #0xf]
    // 0xc2a454: r0 = false
    //     0xc2a454: add             x0, NULL, #0x30  ; false
    // 0xc2a458: StoreField: r1->field_7 = r0
    //     0xc2a458: stur            w0, [x1, #7]
    // 0xc2a45c: StoreField: r1->field_b = r0
    //     0xc2a45c: stur            w0, [x1, #0xb]
    // 0xc2a460: mov             x3, x1
    // 0xc2a464: ldur            x1, [fp, #-0x10]
    // 0xc2a468: r2 = Null
    //     0xc2a468: mov             x2, NULL
    // 0xc2a46c: r0 = change()
    //     0xc2a46c: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0xc2a470: r0 = Null
    //     0xc2a470: mov             x0, NULL
    // 0xc2a474: r0 = ReturnAsyncNotFuture()
    //     0xc2a474: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc2a478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a47c: b               #0xc2a43c
  }
}
