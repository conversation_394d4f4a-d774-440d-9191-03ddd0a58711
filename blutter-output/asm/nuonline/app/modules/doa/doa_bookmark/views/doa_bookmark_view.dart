// lib: , url: package:nuonline/app/modules/doa/doa_bookmark/views/doa_bookmark_view.dart

// class id: 1050172, size: 0x8
class :: {
}

// class id: 5303, size: 0x14, field offset: 0x14
class DoaBookmarkView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad73c0, size: 0x178
    // 0xad73c0: EnterFrame
    //     0xad73c0: stp             fp, lr, [SP, #-0x10]!
    //     0xad73c4: mov             fp, SP
    // 0xad73c8: AllocStack(0x58)
    //     0xad73c8: sub             SP, SP, #0x58
    // 0xad73cc: SetupParameters(DoaBookmarkView this /* r1 => r1, fp-0x8 */)
    //     0xad73cc: stur            x1, [fp, #-8]
    // 0xad73d0: CheckStackOverflow
    //     0xad73d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad73d4: cmp             SP, x16
    //     0xad73d8: b.ls            #0xad7530
    // 0xad73dc: r1 = 1
    //     0xad73dc: movz            x1, #0x1
    // 0xad73e0: r0 = AllocateContext()
    //     0xad73e0: bl              #0xec126c  ; AllocateContextStub
    // 0xad73e4: ldur            x1, [fp, #-8]
    // 0xad73e8: stur            x0, [fp, #-0x10]
    // 0xad73ec: StoreField: r0->field_f = r1
    //     0xad73ec: stur            w1, [x0, #0xf]
    // 0xad73f0: r0 = AppBar()
    //     0xad73f0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad73f4: stur            x0, [fp, #-0x18]
    // 0xad73f8: r16 = Instance_Text
    //     0xad73f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] Obj!Text@e21961
    //     0xad73fc: ldr             x16, [x16, #0x9b8]
    // 0xad7400: str             x16, [SP]
    // 0xad7404: mov             x1, x0
    // 0xad7408: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xad7408: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xad740c: ldr             x4, [x4, #0x6e8]
    // 0xad7410: r0 = AppBar()
    //     0xad7410: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad7414: ldur            x1, [fp, #-8]
    // 0xad7418: r0 = controller()
    //     0xad7418: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad741c: r1 = Function '<anonymous closure>':.
    //     0xad741c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] AnonymousClosure: (0xb98358), in [package:nuonline/app/modules/tutorial/tutorial_bookmark/views/tutorial_bookmark_view.dart] TutorialBookmarkView::build (0xb593b4)
    //     0xad7420: ldr             x1, [x1, #0x9c0]
    // 0xad7424: r2 = Null
    //     0xad7424: mov             x2, NULL
    // 0xad7428: stur            x0, [fp, #-8]
    // 0xad742c: r0 = AllocateClosure()
    //     0xad742c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad7430: r1 = Function '<anonymous closure>':.
    //     0xad7430: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xad7434: ldr             x1, [x1, #0x9c8]
    // 0xad7438: r2 = Null
    //     0xad7438: mov             x2, NULL
    // 0xad743c: stur            x0, [fp, #-0x20]
    // 0xad7440: r0 = AllocateClosure()
    //     0xad7440: bl              #0xec1630  ; AllocateClosureStub
    // 0xad7444: stur            x0, [fp, #-0x28]
    // 0xad7448: r0 = ListView()
    //     0xad7448: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad744c: stur            x0, [fp, #-0x30]
    // 0xad7450: r16 = Instance_EdgeInsets
    //     0xad7450: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad7454: str             x16, [SP]
    // 0xad7458: mov             x1, x0
    // 0xad745c: ldur            x2, [fp, #-0x20]
    // 0xad7460: ldur            x5, [fp, #-0x28]
    // 0xad7464: r3 = 8
    //     0xad7464: movz            x3, #0x8
    // 0xad7468: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xad7468: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xad746c: ldr             x4, [x4, #0x700]
    // 0xad7470: r0 = ListView.separated()
    //     0xad7470: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad7474: r0 = NEmptyState()
    //     0xad7474: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xad7478: mov             x1, x0
    // 0xad747c: r2 = "Simpan bacaan doa atau wirid ke bookmark agar kamu mudah membacanya kembali."
    //     0xad747c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] "Simpan bacaan doa atau wirid ke bookmark agar kamu mudah membacanya kembali."
    //     0xad7480: ldr             x2, [x2, #0x9d0]
    // 0xad7484: r3 = "assets/images/illustration/no_bookmark.svg"
    //     0xad7484: add             x3, PP, #0x29, lsl #12  ; [pp+0x29670] "assets/images/illustration/no_bookmark.svg"
    //     0xad7488: ldr             x3, [x3, #0x670]
    // 0xad748c: r5 = "Belum Ada Wirid & Doa yang Kamu Simpan"
    //     0xad748c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "Belum Ada Wirid & Doa yang Kamu Simpan"
    //     0xad7490: ldr             x5, [x5, #0x9d8]
    // 0xad7494: stur            x0, [fp, #-0x20]
    // 0xad7498: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xad7498: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xad749c: r0 = NEmptyState.svg()
    //     0xad749c: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xad74a0: ldur            x2, [fp, #-0x10]
    // 0xad74a4: r1 = Function '<anonymous closure>':.
    //     0xad74a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] AnonymousClosure: (0xad7538), in [package:nuonline/app/modules/doa/doa_bookmark/views/doa_bookmark_view.dart] DoaBookmarkView::build (0xad73c0)
    //     0xad74a8: ldr             x1, [x1, #0x9e0]
    // 0xad74ac: r0 = AllocateClosure()
    //     0xad74ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xad74b0: r16 = <List<DoaSubCategory>>
    //     0xad74b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xad74b4: ldr             x16, [x16, #0x7d8]
    // 0xad74b8: ldur            lr, [fp, #-8]
    // 0xad74bc: stp             lr, x16, [SP, #0x18]
    // 0xad74c0: ldur            x16, [fp, #-0x30]
    // 0xad74c4: stp             x16, x0, [SP, #8]
    // 0xad74c8: ldur            x16, [fp, #-0x20]
    // 0xad74cc: str             x16, [SP]
    // 0xad74d0: r4 = const [0x1, 0x4, 0x4, 0x2, onEmpty, 0x3, onLoading, 0x2, null]
    //     0xad74d0: add             x4, PP, #0x29, lsl #12  ; [pp+0x29688] List(9) [0x1, 0x4, 0x4, 0x2, "onEmpty", 0x3, "onLoading", 0x2, Null]
    //     0xad74d4: ldr             x4, [x4, #0x688]
    // 0xad74d8: r0 = StateExt.obx()
    //     0xad74d8: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xad74dc: stur            x0, [fp, #-8]
    // 0xad74e0: r0 = Scaffold()
    //     0xad74e0: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad74e4: ldur            x1, [fp, #-0x18]
    // 0xad74e8: StoreField: r0->field_13 = r1
    //     0xad74e8: stur            w1, [x0, #0x13]
    // 0xad74ec: ldur            x1, [fp, #-8]
    // 0xad74f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad74f0: stur            w1, [x0, #0x17]
    // 0xad74f4: r1 = Instance_AlignmentDirectional
    //     0xad74f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad74f8: ldr             x1, [x1, #0x758]
    // 0xad74fc: StoreField: r0->field_2b = r1
    //     0xad74fc: stur            w1, [x0, #0x2b]
    // 0xad7500: r1 = true
    //     0xad7500: add             x1, NULL, #0x20  ; true
    // 0xad7504: StoreField: r0->field_53 = r1
    //     0xad7504: stur            w1, [x0, #0x53]
    // 0xad7508: r2 = Instance_DragStartBehavior
    //     0xad7508: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad750c: StoreField: r0->field_57 = r2
    //     0xad750c: stur            w2, [x0, #0x57]
    // 0xad7510: r2 = false
    //     0xad7510: add             x2, NULL, #0x30  ; false
    // 0xad7514: StoreField: r0->field_b = r2
    //     0xad7514: stur            w2, [x0, #0xb]
    // 0xad7518: StoreField: r0->field_f = r2
    //     0xad7518: stur            w2, [x0, #0xf]
    // 0xad751c: StoreField: r0->field_5f = r1
    //     0xad751c: stur            w1, [x0, #0x5f]
    // 0xad7520: StoreField: r0->field_63 = r1
    //     0xad7520: stur            w1, [x0, #0x63]
    // 0xad7524: LeaveFrame
    //     0xad7524: mov             SP, fp
    //     0xad7528: ldp             fp, lr, [SP], #0x10
    // 0xad752c: ret
    //     0xad752c: ret             
    // 0xad7530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad7530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7534: b               #0xad73dc
  }
  [closure] ListTileTheme <anonymous closure>(dynamic, List<DoaSubCategory>?) {
    // ** addr: 0xad7538, size: 0x104
    // 0xad7538: EnterFrame
    //     0xad7538: stp             fp, lr, [SP, #-0x10]!
    //     0xad753c: mov             fp, SP
    // 0xad7540: AllocStack(0x28)
    //     0xad7540: sub             SP, SP, #0x28
    // 0xad7544: SetupParameters()
    //     0xad7544: ldr             x0, [fp, #0x18]
    //     0xad7548: ldur            w1, [x0, #0x17]
    //     0xad754c: add             x1, x1, HEAP, lsl #32
    //     0xad7550: stur            x1, [fp, #-8]
    // 0xad7554: CheckStackOverflow
    //     0xad7554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad7558: cmp             SP, x16
    //     0xad755c: b.ls            #0xad7630
    // 0xad7560: r1 = 1
    //     0xad7560: movz            x1, #0x1
    // 0xad7564: r0 = AllocateContext()
    //     0xad7564: bl              #0xec126c  ; AllocateContextStub
    // 0xad7568: mov             x1, x0
    // 0xad756c: ldur            x0, [fp, #-8]
    // 0xad7570: stur            x1, [fp, #-0x10]
    // 0xad7574: StoreField: r1->field_b = r0
    //     0xad7574: stur            w0, [x1, #0xb]
    // 0xad7578: ldr             x0, [fp, #0x10]
    // 0xad757c: StoreField: r1->field_f = r0
    //     0xad757c: stur            w0, [x1, #0xf]
    // 0xad7580: cmp             w0, NULL
    // 0xad7584: b.eq            #0xad7638
    // 0xad7588: r2 = LoadClassIdInstr(r0)
    //     0xad7588: ldur            x2, [x0, #-1]
    //     0xad758c: ubfx            x2, x2, #0xc, #0x14
    // 0xad7590: str             x0, [SP]
    // 0xad7594: mov             x0, x2
    // 0xad7598: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad7598: movz            x17, #0xc834
    //     0xad759c: add             lr, x0, x17
    //     0xad75a0: ldr             lr, [x21, lr, lsl #3]
    //     0xad75a4: blr             lr
    // 0xad75a8: r3 = LoadInt32Instr(r0)
    //     0xad75a8: sbfx            x3, x0, #1, #0x1f
    //     0xad75ac: tbz             w0, #0, #0xad75b4
    //     0xad75b0: ldur            x3, [x0, #7]
    // 0xad75b4: stur            x3, [fp, #-0x18]
    // 0xad75b8: r1 = Function '<anonymous closure>':.
    //     0xad75b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9e8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad75bc: ldr             x1, [x1, #0x9e8]
    // 0xad75c0: r2 = Null
    //     0xad75c0: mov             x2, NULL
    // 0xad75c4: r0 = AllocateClosure()
    //     0xad75c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad75c8: ldur            x2, [fp, #-0x10]
    // 0xad75cc: r1 = Function '<anonymous closure>':.
    //     0xad75cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9f0] AnonymousClosure: (0xad763c), in [package:nuonline/app/modules/doa/doa_bookmark/views/doa_bookmark_view.dart] DoaBookmarkView::build (0xad73c0)
    //     0xad75d0: ldr             x1, [x1, #0x9f0]
    // 0xad75d4: stur            x0, [fp, #-8]
    // 0xad75d8: r0 = AllocateClosure()
    //     0xad75d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad75dc: stur            x0, [fp, #-0x10]
    // 0xad75e0: r0 = ListView()
    //     0xad75e0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad75e4: stur            x0, [fp, #-0x20]
    // 0xad75e8: r16 = Instance_EdgeInsets
    //     0xad75e8: add             x16, PP, #0x29, lsl #12  ; [pp+0x29900] Obj!EdgeInsets@e12641
    //     0xad75ec: ldr             x16, [x16, #0x900]
    // 0xad75f0: str             x16, [SP]
    // 0xad75f4: mov             x1, x0
    // 0xad75f8: ldur            x2, [fp, #-0x10]
    // 0xad75fc: ldur            x3, [fp, #-0x18]
    // 0xad7600: ldur            x5, [fp, #-8]
    // 0xad7604: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xad7604: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xad7608: ldr             x4, [x4, #0x700]
    // 0xad760c: r0 = ListView.separated()
    //     0xad760c: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad7610: r0 = ListTileTheme()
    //     0xad7610: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xad7614: r1 = Instance_EdgeInsets
    //     0xad7614: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad7618: StoreField: r0->field_2b = r1
    //     0xad7618: stur            w1, [x0, #0x2b]
    // 0xad761c: ldur            x1, [fp, #-0x20]
    // 0xad7620: StoreField: r0->field_b = r1
    //     0xad7620: stur            w1, [x0, #0xb]
    // 0xad7624: LeaveFrame
    //     0xad7624: mov             SP, fp
    //     0xad7628: ldp             fp, lr, [SP], #0x10
    // 0xad762c: ret
    //     0xad762c: ret             
    // 0xad7630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad7630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7634: b               #0xad7560
    // 0xad7638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad7638: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad763c, size: 0x160
    // 0xad763c: EnterFrame
    //     0xad763c: stp             fp, lr, [SP, #-0x10]!
    //     0xad7640: mov             fp, SP
    // 0xad7644: AllocStack(0x30)
    //     0xad7644: sub             SP, SP, #0x30
    // 0xad7648: SetupParameters()
    //     0xad7648: ldr             x0, [fp, #0x20]
    //     0xad764c: ldur            w1, [x0, #0x17]
    //     0xad7650: add             x1, x1, HEAP, lsl #32
    //     0xad7654: stur            x1, [fp, #-8]
    // 0xad7658: CheckStackOverflow
    //     0xad7658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad765c: cmp             SP, x16
    //     0xad7660: b.ls            #0xad7794
    // 0xad7664: r1 = 1
    //     0xad7664: movz            x1, #0x1
    // 0xad7668: r0 = AllocateContext()
    //     0xad7668: bl              #0xec126c  ; AllocateContextStub
    // 0xad766c: mov             x2, x0
    // 0xad7670: ldur            x1, [fp, #-8]
    // 0xad7674: stur            x2, [fp, #-0x10]
    // 0xad7678: StoreField: r2->field_b = r1
    //     0xad7678: stur            w1, [x2, #0xb]
    // 0xad767c: ldr             x3, [fp, #0x10]
    // 0xad7680: StoreField: r2->field_f = r3
    //     0xad7680: stur            w3, [x2, #0xf]
    // 0xad7684: LoadField: r0 = r1->field_f
    //     0xad7684: ldur            w0, [x1, #0xf]
    // 0xad7688: DecompressPointer r0
    //     0xad7688: add             x0, x0, HEAP, lsl #32
    // 0xad768c: r4 = LoadClassIdInstr(r0)
    //     0xad768c: ldur            x4, [x0, #-1]
    //     0xad7690: ubfx            x4, x4, #0xc, #0x14
    // 0xad7694: stp             x3, x0, [SP]
    // 0xad7698: mov             x0, x4
    // 0xad769c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad769c: movz            x17, #0x3037
    //     0xad76a0: movk            x17, #0x1, lsl #16
    //     0xad76a4: add             lr, x0, x17
    //     0xad76a8: ldr             lr, [x21, lr, lsl #3]
    //     0xad76ac: blr             lr
    // 0xad76b0: LoadField: r1 = r0->field_f
    //     0xad76b0: ldur            w1, [x0, #0xf]
    // 0xad76b4: DecompressPointer r1
    //     0xad76b4: add             x1, x1, HEAP, lsl #32
    // 0xad76b8: stur            x1, [fp, #-0x18]
    // 0xad76bc: r0 = Text()
    //     0xad76bc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad76c0: mov             x1, x0
    // 0xad76c4: ldur            x0, [fp, #-0x18]
    // 0xad76c8: stur            x1, [fp, #-0x20]
    // 0xad76cc: StoreField: r1->field_b = r0
    //     0xad76cc: stur            w0, [x1, #0xb]
    // 0xad76d0: ldur            x0, [fp, #-8]
    // 0xad76d4: LoadField: r2 = r0->field_f
    //     0xad76d4: ldur            w2, [x0, #0xf]
    // 0xad76d8: DecompressPointer r2
    //     0xad76d8: add             x2, x2, HEAP, lsl #32
    // 0xad76dc: r0 = LoadClassIdInstr(r2)
    //     0xad76dc: ldur            x0, [x2, #-1]
    //     0xad76e0: ubfx            x0, x0, #0xc, #0x14
    // 0xad76e4: ldr             x16, [fp, #0x10]
    // 0xad76e8: stp             x16, x2, [SP]
    // 0xad76ec: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad76ec: movz            x17, #0x3037
    //     0xad76f0: movk            x17, #0x1, lsl #16
    //     0xad76f4: add             lr, x0, x17
    //     0xad76f8: ldr             lr, [x21, lr, lsl #3]
    //     0xad76fc: blr             lr
    // 0xad7700: LoadField: r1 = r0->field_23
    //     0xad7700: ldur            w1, [x0, #0x23]
    // 0xad7704: DecompressPointer r1
    //     0xad7704: add             x1, x1, HEAP, lsl #32
    // 0xad7708: stur            x1, [fp, #-8]
    // 0xad770c: r0 = Text()
    //     0xad770c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad7710: mov             x1, x0
    // 0xad7714: ldur            x0, [fp, #-8]
    // 0xad7718: stur            x1, [fp, #-0x18]
    // 0xad771c: StoreField: r1->field_b = r0
    //     0xad771c: stur            w0, [x1, #0xb]
    // 0xad7720: r0 = ListTile()
    //     0xad7720: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xad7724: mov             x3, x0
    // 0xad7728: ldur            x0, [fp, #-0x20]
    // 0xad772c: stur            x3, [fp, #-8]
    // 0xad7730: StoreField: r3->field_f = r0
    //     0xad7730: stur            w0, [x3, #0xf]
    // 0xad7734: ldur            x0, [fp, #-0x18]
    // 0xad7738: StoreField: r3->field_13 = r0
    //     0xad7738: stur            w0, [x3, #0x13]
    // 0xad773c: r0 = Instance_Icon
    //     0xad773c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cfd8] Obj!Icon@e23fb1
    //     0xad7740: ldr             x0, [x0, #0xfd8]
    // 0xad7744: ArrayStore: r3[0] = r0  ; List_4
    //     0xad7744: stur            w0, [x3, #0x17]
    // 0xad7748: r0 = false
    //     0xad7748: add             x0, NULL, #0x30  ; false
    // 0xad774c: StoreField: r3->field_1b = r0
    //     0xad774c: stur            w0, [x3, #0x1b]
    // 0xad7750: r4 = true
    //     0xad7750: add             x4, NULL, #0x20  ; true
    // 0xad7754: StoreField: r3->field_4b = r4
    //     0xad7754: stur            w4, [x3, #0x4b]
    // 0xad7758: ldur            x2, [fp, #-0x10]
    // 0xad775c: r1 = Function '<anonymous closure>':.
    //     0xad775c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] AnonymousClosure: (0xad779c), in [package:nuonline/app/modules/doa/doa_bookmark/views/doa_bookmark_view.dart] DoaBookmarkView::build (0xad73c0)
    //     0xad7760: ldr             x1, [x1, #0x9f8]
    // 0xad7764: r0 = AllocateClosure()
    //     0xad7764: bl              #0xec1630  ; AllocateClosureStub
    // 0xad7768: mov             x1, x0
    // 0xad776c: ldur            x0, [fp, #-8]
    // 0xad7770: StoreField: r0->field_4f = r1
    //     0xad7770: stur            w1, [x0, #0x4f]
    // 0xad7774: r1 = false
    //     0xad7774: add             x1, NULL, #0x30  ; false
    // 0xad7778: StoreField: r0->field_5f = r1
    //     0xad7778: stur            w1, [x0, #0x5f]
    // 0xad777c: StoreField: r0->field_73 = r1
    //     0xad777c: stur            w1, [x0, #0x73]
    // 0xad7780: r1 = true
    //     0xad7780: add             x1, NULL, #0x20  ; true
    // 0xad7784: StoreField: r0->field_97 = r1
    //     0xad7784: stur            w1, [x0, #0x97]
    // 0xad7788: LeaveFrame
    //     0xad7788: mov             SP, fp
    //     0xad778c: ldp             fp, lr, [SP], #0x10
    // 0xad7790: ret
    //     0xad7790: ret             
    // 0xad7794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad7794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7798: b               #0xad7664
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad779c, size: 0x80
    // 0xad779c: EnterFrame
    //     0xad779c: stp             fp, lr, [SP, #-0x10]!
    //     0xad77a0: mov             fp, SP
    // 0xad77a4: AllocStack(0x8)
    //     0xad77a4: sub             SP, SP, #8
    // 0xad77a8: SetupParameters()
    //     0xad77a8: ldr             x0, [fp, #0x10]
    //     0xad77ac: ldur            w2, [x0, #0x17]
    //     0xad77b0: add             x2, x2, HEAP, lsl #32
    //     0xad77b4: stur            x2, [fp, #-8]
    // 0xad77b8: CheckStackOverflow
    //     0xad77b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad77bc: cmp             SP, x16
    //     0xad77c0: b.ls            #0xad7814
    // 0xad77c4: LoadField: r0 = r2->field_b
    //     0xad77c4: ldur            w0, [x2, #0xb]
    // 0xad77c8: DecompressPointer r0
    //     0xad77c8: add             x0, x0, HEAP, lsl #32
    // 0xad77cc: LoadField: r1 = r0->field_b
    //     0xad77cc: ldur            w1, [x0, #0xb]
    // 0xad77d0: DecompressPointer r1
    //     0xad77d0: add             x1, x1, HEAP, lsl #32
    // 0xad77d4: LoadField: r0 = r1->field_f
    //     0xad77d4: ldur            w0, [x1, #0xf]
    // 0xad77d8: DecompressPointer r0
    //     0xad77d8: add             x0, x0, HEAP, lsl #32
    // 0xad77dc: mov             x1, x0
    // 0xad77e0: r0 = controller()
    //     0xad77e0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad77e4: mov             x1, x0
    // 0xad77e8: ldur            x0, [fp, #-8]
    // 0xad77ec: LoadField: r2 = r0->field_f
    //     0xad77ec: ldur            w2, [x0, #0xf]
    // 0xad77f0: DecompressPointer r2
    //     0xad77f0: add             x2, x2, HEAP, lsl #32
    // 0xad77f4: r0 = LoadInt32Instr(r2)
    //     0xad77f4: sbfx            x0, x2, #1, #0x1f
    //     0xad77f8: tbz             w2, #0, #0xad7800
    //     0xad77fc: ldur            x0, [x2, #7]
    // 0xad7800: mov             x2, x0
    // 0xad7804: r0 = onPressed()
    //     0xad7804: bl              #0xad781c  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] DoaBookmarkController::onPressed
    // 0xad7808: LeaveFrame
    //     0xad7808: mov             SP, fp
    //     0xad780c: ldp             fp, lr, [SP], #0x10
    // 0xad7810: ret
    //     0xad7810: ret             
    // 0xad7814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad7814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad7818: b               #0xad77c4
  }
}
