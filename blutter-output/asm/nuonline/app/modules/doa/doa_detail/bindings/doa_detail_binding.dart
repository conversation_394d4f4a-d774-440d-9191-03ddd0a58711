// lib: , url: package:nuonline/app/modules/doa/doa_detail/bindings/doa_detail_binding.dart

// class id: 1050173, size: 0x8
class :: {
}

// class id: 2185, size: 0x8, field offset: 0x8
class DoaDetailBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80f3e4, size: 0x7c
    // 0x80f3e4: EnterFrame
    //     0x80f3e4: stp             fp, lr, [SP, #-0x10]!
    //     0x80f3e8: mov             fp, SP
    // 0x80f3ec: AllocStack(0x10)
    //     0x80f3ec: sub             SP, SP, #0x10
    // 0x80f3f0: CheckStackOverflow
    //     0x80f3f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f3f4: cmp             SP, x16
    //     0x80f3f8: b.ls            #0x80f458
    // 0x80f3fc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f3fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f400: ldr             x0, [x0, #0x2670]
    //     0x80f404: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f408: cmp             w0, w16
    //     0x80f40c: b.ne            #0x80f418
    //     0x80f410: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f414: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f418: r1 = Function '<anonymous closure>':.
    //     0x80f418: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e00] AnonymousClosure: (0x80f460), in [package:nuonline/app/modules/doa/doa_detail/bindings/doa_detail_binding.dart] DoaDetailBinding::dependencies (0x80f3e4)
    //     0x80f41c: ldr             x1, [x1, #0xe00]
    // 0x80f420: r2 = Null
    //     0x80f420: mov             x2, NULL
    // 0x80f424: r0 = AllocateClosure()
    //     0x80f424: bl              #0xec1630  ; AllocateClosureStub
    // 0x80f428: r16 = <DoaDetailController>
    //     0x80f428: add             x16, PP, #0x24, lsl #12  ; [pp+0x24c10] TypeArguments: <DoaDetailController>
    //     0x80f42c: ldr             x16, [x16, #0xc10]
    // 0x80f430: stp             x0, x16, [SP]
    // 0x80f434: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80f434: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80f438: r0 = Inst.lazyPut()
    //     0x80f438: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80f43c: r0 = ReadingPreferenceBinding()
    //     0x80f43c: bl              #0x80da0c  ; AllocateReadingPreferenceBindingStub -> ReadingPreferenceBinding (size=0x8)
    // 0x80f440: mov             x1, x0
    // 0x80f444: r0 = dependencies()
    //     0x80f444: bl              #0x8423bc  ; [package:nuonline/app/modules/setting/reading_preference/bindings/reading_preference_binding.dart] ReadingPreferenceBinding::dependencies
    // 0x80f448: r0 = Null
    //     0x80f448: mov             x0, NULL
    // 0x80f44c: LeaveFrame
    //     0x80f44c: mov             SP, fp
    //     0x80f450: ldp             fp, lr, [SP], #0x10
    // 0x80f454: ret
    //     0x80f454: ret             
    // 0x80f458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f45c: b               #0x80f3fc
  }
  [closure] DoaDetailController <anonymous closure>(dynamic) {
    // ** addr: 0x80f460, size: 0x188
    // 0x80f460: EnterFrame
    //     0x80f460: stp             fp, lr, [SP, #-0x10]!
    //     0x80f464: mov             fp, SP
    // 0x80f468: AllocStack(0x40)
    //     0x80f468: sub             SP, SP, #0x40
    // 0x80f46c: CheckStackOverflow
    //     0x80f46c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f470: cmp             SP, x16
    //     0x80f474: b.ls            #0x80f5e0
    // 0x80f478: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f478: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f47c: ldr             x0, [x0, #0x2670]
    //     0x80f480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f484: cmp             w0, w16
    //     0x80f488: b.ne            #0x80f494
    //     0x80f48c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f490: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f494: r0 = GetNavigation.arguments()
    //     0x80f494: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80f498: r16 = "id"
    //     0x80f498: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80f49c: ldr             x16, [x16, #0x740]
    // 0x80f4a0: stp             x16, x0, [SP]
    // 0x80f4a4: r4 = 0
    //     0x80f4a4: movz            x4, #0
    // 0x80f4a8: ldr             x0, [SP, #8]
    // 0x80f4ac: r16 = UnlinkedCall_0x5f3c08
    //     0x80f4ac: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e08] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80f4b0: add             x16, x16, #0xe08
    // 0x80f4b4: ldp             x5, lr, [x16]
    // 0x80f4b8: blr             lr
    // 0x80f4bc: mov             x3, x0
    // 0x80f4c0: r2 = Null
    //     0x80f4c0: mov             x2, NULL
    // 0x80f4c4: r1 = Null
    //     0x80f4c4: mov             x1, NULL
    // 0x80f4c8: stur            x3, [fp, #-8]
    // 0x80f4cc: branchIfSmi(r0, 0x80f4f4)
    //     0x80f4cc: tbz             w0, #0, #0x80f4f4
    // 0x80f4d0: r4 = LoadClassIdInstr(r0)
    //     0x80f4d0: ldur            x4, [x0, #-1]
    //     0x80f4d4: ubfx            x4, x4, #0xc, #0x14
    // 0x80f4d8: sub             x4, x4, #0x3c
    // 0x80f4dc: cmp             x4, #1
    // 0x80f4e0: b.ls            #0x80f4f4
    // 0x80f4e4: r8 = int
    //     0x80f4e4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80f4e8: r3 = Null
    //     0x80f4e8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35e18] Null
    //     0x80f4ec: ldr             x3, [x3, #0xe18]
    // 0x80f4f0: r0 = int()
    //     0x80f4f0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80f4f4: r0 = find()
    //     0x80f4f4: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0x80f4f8: stur            x0, [fp, #-0x10]
    // 0x80f4fc: r0 = find()
    //     0x80f4fc: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0x80f500: stur            x0, [fp, #-0x18]
    // 0x80f504: r16 = <AppStorage>
    //     0x80f504: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x80f508: r30 = "app_storage"
    //     0x80f508: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x80f50c: stp             lr, x16, [SP]
    // 0x80f510: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80f510: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80f514: r0 = Inst.find()
    //     0x80f514: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80f518: stur            x0, [fp, #-0x20]
    // 0x80f51c: r0 = GetNavigation.arguments()
    //     0x80f51c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80f520: r16 = "showBookmark"
    //     0x80f520: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f800] "showBookmark"
    //     0x80f524: ldr             x16, [x16, #0x800]
    // 0x80f528: stp             x16, x0, [SP]
    // 0x80f52c: r4 = 0
    //     0x80f52c: movz            x4, #0
    // 0x80f530: ldr             x0, [SP, #8]
    // 0x80f534: r16 = UnlinkedCall_0x5f3c08
    //     0x80f534: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e28] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80f538: add             x16, x16, #0xe28
    // 0x80f53c: ldp             x5, lr, [x16]
    // 0x80f540: blr             lr
    // 0x80f544: mov             x3, x0
    // 0x80f548: r2 = Null
    //     0x80f548: mov             x2, NULL
    // 0x80f54c: r1 = Null
    //     0x80f54c: mov             x1, NULL
    // 0x80f550: stur            x3, [fp, #-0x28]
    // 0x80f554: r4 = 60
    //     0x80f554: movz            x4, #0x3c
    // 0x80f558: branchIfSmi(r0, 0x80f564)
    //     0x80f558: tbz             w0, #0, #0x80f564
    // 0x80f55c: r4 = LoadClassIdInstr(r0)
    //     0x80f55c: ldur            x4, [x0, #-1]
    //     0x80f560: ubfx            x4, x4, #0xc, #0x14
    // 0x80f564: cmp             x4, #0x3f
    // 0x80f568: b.eq            #0x80f57c
    // 0x80f56c: r8 = bool?
    //     0x80f56c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x80f570: r3 = Null
    //     0x80f570: add             x3, PP, #0x35, lsl #12  ; [pp+0x35e38] Null
    //     0x80f574: ldr             x3, [x3, #0xe38]
    // 0x80f578: r0 = bool?()
    //     0x80f578: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x80f57c: ldur            x0, [fp, #-0x28]
    // 0x80f580: cmp             w0, NULL
    // 0x80f584: b.ne            #0x80f590
    // 0x80f588: r6 = true
    //     0x80f588: add             x6, NULL, #0x20  ; true
    // 0x80f58c: b               #0x80f594
    // 0x80f590: mov             x6, x0
    // 0x80f594: ldur            x0, [fp, #-8]
    // 0x80f598: stur            x6, [fp, #-0x28]
    // 0x80f59c: r2 = LoadInt32Instr(r0)
    //     0x80f59c: sbfx            x2, x0, #1, #0x1f
    //     0x80f5a0: tbz             w0, #0, #0x80f5a8
    //     0x80f5a4: ldur            x2, [x0, #7]
    // 0x80f5a8: stur            x2, [fp, #-0x30]
    // 0x80f5ac: r0 = DoaDetailController()
    //     0x80f5ac: bl              #0x80f970  ; AllocateDoaDetailControllerStub -> DoaDetailController (size=0x5c)
    // 0x80f5b0: mov             x1, x0
    // 0x80f5b4: ldur            x2, [fp, #-0x30]
    // 0x80f5b8: ldur            x3, [fp, #-0x10]
    // 0x80f5bc: ldur            x5, [fp, #-0x18]
    // 0x80f5c0: ldur            x6, [fp, #-0x28]
    // 0x80f5c4: ldur            x7, [fp, #-0x20]
    // 0x80f5c8: stur            x0, [fp, #-8]
    // 0x80f5cc: r0 = DoaDetailController()
    //     0x80f5cc: bl              #0x80f5e8  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::DoaDetailController
    // 0x80f5d0: ldur            x0, [fp, #-8]
    // 0x80f5d4: LeaveFrame
    //     0x80f5d4: mov             SP, fp
    //     0x80f5d8: ldp             fp, lr, [SP], #0x10
    // 0x80f5dc: ret
    //     0x80f5dc: ret             
    // 0x80f5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f5e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f5e4: b               #0x80f478
  }
}
