// lib: , url: package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart

// class id: 1050174, size: 0x8
class :: {
}

// class id: 1967, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _DoaDetailBuilderController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {
}

// class id: 1968, size: 0x28, field offset: 0x28
//   transformed mixin,
abstract class _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin extends _DoaDetailBuilderController&GetxController&StateMixin
     with OfflineMixin<X0> {

  _ executeOfflineMode(/* No info */) async {
    // ** addr: 0x8f0914, size: 0x80
    // 0x8f0914: EnterFrame
    //     0x8f0914: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0918: mov             fp, SP
    // 0x8f091c: AllocStack(0x30)
    //     0x8f091c: sub             SP, SP, #0x30
    // 0x8f0920: SetupParameters(_DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f0920: stur            NULL, [fp, #-8]
    //     0x8f0924: stur            x1, [fp, #-0x10]
    // 0x8f0928: CheckStackOverflow
    //     0x8f0928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f092c: cmp             SP, x16
    //     0x8f0930: b.ls            #0x8f098c
    // 0x8f0934: r1 = 1
    //     0x8f0934: movz            x1, #0x1
    // 0x8f0938: r0 = AllocateContext()
    //     0x8f0938: bl              #0xec126c  ; AllocateContextStub
    // 0x8f093c: mov             x2, x0
    // 0x8f0940: ldur            x1, [fp, #-0x10]
    // 0x8f0944: stur            x2, [fp, #-0x18]
    // 0x8f0948: StoreField: r2->field_f = r1
    //     0x8f0948: stur            w1, [x2, #0xf]
    // 0x8f094c: InitAsync() -> Future<void?>
    //     0x8f094c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f0950: bl              #0x661298  ; InitAsyncStub
    // 0x8f0954: ldur            x1, [fp, #-0x10]
    // 0x8f0958: r0 = onOfflineModeRequested()
    //     0x8f0958: bl              #0xbef858  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onOfflineModeRequested
    // 0x8f095c: ldur            x2, [fp, #-0x18]
    // 0x8f0960: r1 = Function '<anonymous closure>':.
    //     0x8f0960: add             x1, PP, #0x35, lsl #12  ; [pp+0x35db0] AnonymousClosure: (0x8f0994), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f0914)
    //     0x8f0964: ldr             x1, [x1, #0xdb0]
    // 0x8f0968: stur            x0, [fp, #-0x10]
    // 0x8f096c: r0 = AllocateClosure()
    //     0x8f096c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0970: r16 = <void?>
    //     0x8f0970: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f0974: ldur            lr, [fp, #-0x10]
    // 0x8f0978: stp             lr, x16, [SP, #8]
    // 0x8f097c: str             x0, [SP]
    // 0x8f0980: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f0980: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f0984: r0 = then()
    //     0x8f0984: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f0988: r0 = ReturnAsync()
    //     0x8f0988: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f098c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f098c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0990: b               #0x8f0934
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<Doa>>) {
    // ** addr: 0x8f0994, size: 0x94
    // 0x8f0994: EnterFrame
    //     0x8f0994: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0998: mov             fp, SP
    // 0x8f099c: AllocStack(0x28)
    //     0x8f099c: sub             SP, SP, #0x28
    // 0x8f09a0: SetupParameters()
    //     0x8f09a0: ldr             x0, [fp, #0x18]
    //     0x8f09a4: ldur            w3, [x0, #0x17]
    //     0x8f09a8: add             x3, x3, HEAP, lsl #32
    //     0x8f09ac: stur            x3, [fp, #-8]
    // 0x8f09b0: CheckStackOverflow
    //     0x8f09b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f09b4: cmp             SP, x16
    //     0x8f09b8: b.ls            #0x8f0a20
    // 0x8f09bc: mov             x2, x3
    // 0x8f09c0: r1 = Function '<anonymous closure>':.
    //     0x8f09c0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35db8] AnonymousClosure: (0x8f0a80), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f0914)
    //     0x8f09c4: ldr             x1, [x1, #0xdb8]
    // 0x8f09c8: r0 = AllocateClosure()
    //     0x8f09c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f09cc: ldur            x2, [fp, #-8]
    // 0x8f09d0: r1 = Function '<anonymous closure>':.
    //     0x8f09d0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35dc0] AnonymousClosure: (0x8f0a28), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f0914)
    //     0x8f09d4: ldr             x1, [x1, #0xdc0]
    // 0x8f09d8: stur            x0, [fp, #-8]
    // 0x8f09dc: r0 = AllocateClosure()
    //     0x8f09dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f09e0: mov             x1, x0
    // 0x8f09e4: ldr             x0, [fp, #0x10]
    // 0x8f09e8: r2 = LoadClassIdInstr(r0)
    //     0x8f09e8: ldur            x2, [x0, #-1]
    //     0x8f09ec: ubfx            x2, x2, #0xc, #0x14
    // 0x8f09f0: r16 = <Object?>
    //     0x8f09f0: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f09f4: stp             x0, x16, [SP, #0x10]
    // 0x8f09f8: ldur            x16, [fp, #-8]
    // 0x8f09fc: stp             x16, x1, [SP]
    // 0x8f0a00: mov             x0, x2
    // 0x8f0a04: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f0a04: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f0a08: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f0a08: sub             lr, x0, #1, lsl #12
    //     0x8f0a0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f0a10: blr             lr
    // 0x8f0a14: LeaveFrame
    //     0x8f0a14: mov             SP, fp
    //     0x8f0a18: ldp             fp, lr, [SP], #0x10
    // 0x8f0a1c: ret
    //     0x8f0a1c: ret             
    // 0x8f0a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0a24: b               #0x8f09bc
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f0a28, size: 0x58
    // 0x8f0a28: EnterFrame
    //     0x8f0a28: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0a2c: mov             fp, SP
    // 0x8f0a30: AllocStack(0x8)
    //     0x8f0a30: sub             SP, SP, #8
    // 0x8f0a34: SetupParameters()
    //     0x8f0a34: ldr             x0, [fp, #0x18]
    //     0x8f0a38: ldur            w1, [x0, #0x17]
    //     0x8f0a3c: add             x1, x1, HEAP, lsl #32
    // 0x8f0a40: CheckStackOverflow
    //     0x8f0a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0a44: cmp             SP, x16
    //     0x8f0a48: b.ls            #0x8f0a78
    // 0x8f0a4c: LoadField: r0 = r1->field_f
    //     0x8f0a4c: ldur            w0, [x1, #0xf]
    // 0x8f0a50: DecompressPointer r0
    //     0x8f0a50: add             x0, x0, HEAP, lsl #32
    // 0x8f0a54: ldr             x1, [fp, #0x10]
    // 0x8f0a58: stur            x0, [fp, #-8]
    // 0x8f0a5c: r0 = getErrorMessage()
    //     0x8f0a5c: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f0a60: ldur            x1, [fp, #-8]
    // 0x8f0a64: mov             x2, x0
    // 0x8f0a68: r0 = onOnlineModeFailure()
    //     0x8f0a68: bl              #0xe34c68  ; [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] HajiHikmahController::onOnlineModeFailure
    // 0x8f0a6c: LeaveFrame
    //     0x8f0a6c: mov             SP, fp
    //     0x8f0a70: ldp             fp, lr, [SP], #0x10
    // 0x8f0a74: ret
    //     0x8f0a74: ret             
    // 0x8f0a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0a7c: b               #0x8f0a4c
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<Doa>, Pagination?) {
    // ** addr: 0x8f0a80, size: 0x48
    // 0x8f0a80: EnterFrame
    //     0x8f0a80: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0a84: mov             fp, SP
    // 0x8f0a88: ldr             x0, [fp, #0x20]
    // 0x8f0a8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0a8c: ldur            w1, [x0, #0x17]
    // 0x8f0a90: DecompressPointer r1
    //     0x8f0a90: add             x1, x1, HEAP, lsl #32
    // 0x8f0a94: CheckStackOverflow
    //     0x8f0a94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0a98: cmp             SP, x16
    //     0x8f0a9c: b.ls            #0x8f0ac0
    // 0x8f0aa0: LoadField: r0 = r1->field_f
    //     0x8f0aa0: ldur            w0, [x1, #0xf]
    // 0x8f0aa4: DecompressPointer r0
    //     0x8f0aa4: add             x0, x0, HEAP, lsl #32
    // 0x8f0aa8: mov             x1, x0
    // 0x8f0aac: ldr             x2, [fp, #0x18]
    // 0x8f0ab0: r0 = onOfflineModeLoaded()
    //     0x8f0ab0: bl              #0xbf630c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onOfflineModeLoaded
    // 0x8f0ab4: LeaveFrame
    //     0x8f0ab4: mov             SP, fp
    //     0x8f0ab8: ldp             fp, lr, [SP], #0x10
    // 0x8f0abc: ret
    //     0x8f0abc: ret             
    // 0x8f0ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0ac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0ac4: b               #0x8f0aa0
  }
}

// class id: 1969, size: 0x34, field offset: 0x28
class DoaDetailBuilderController extends _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8f08b0, size: 0x64
    // 0x8f08b0: EnterFrame
    //     0x8f08b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f08b4: mov             fp, SP
    // 0x8f08b8: AllocStack(0x8)
    //     0x8f08b8: sub             SP, SP, #8
    // 0x8f08bc: SetupParameters(DoaDetailBuilderController this /* r1 => r0, fp-0x8 */)
    //     0x8f08bc: mov             x0, x1
    //     0x8f08c0: stur            x1, [fp, #-8]
    // 0x8f08c4: CheckStackOverflow
    //     0x8f08c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f08c8: cmp             SP, x16
    //     0x8f08cc: b.ls            #0x8f090c
    // 0x8f08d0: mov             x1, x0
    // 0x8f08d4: r0 = onInit()
    //     0x8f08d4: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f08d8: ldur            x1, [fp, #-8]
    // 0x8f08dc: r0 = executeOfflineMode()
    //     0x8f08dc: bl              #0x8f0914  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin::executeOfflineMode
    // 0x8f08e0: r0 = find()
    //     0x8f08e0: bl              #0x8efcf4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::find
    // 0x8f08e4: mov             x1, x0
    // 0x8f08e8: ldur            x0, [fp, #-8]
    // 0x8f08ec: LoadField: r2 = r0->field_27
    //     0x8f08ec: ldur            x2, [x0, #0x27]
    // 0x8f08f0: r3 = Instance_CounterType
    //     0x8f08f0: add             x3, PP, #0x40, lsl #12  ; [pp+0x405d0] Obj!CounterType@e31041
    //     0x8f08f4: ldr             x3, [x3, #0x5d0]
    // 0x8f08f8: r0 = record()
    //     0x8f08f8: bl              #0x8efad4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::record
    // 0x8f08fc: r0 = Null
    //     0x8f08fc: mov             x0, NULL
    // 0x8f0900: LeaveFrame
    //     0x8f0900: mov             SP, fp
    //     0x8f0904: ldp             fp, lr, [SP], #0x10
    // 0x8f0908: ret
    //     0x8f0908: ret             
    // 0x8f090c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f090c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0910: b               #0x8f08d0
  }
  [closure] Future<void> onRefresh(dynamic) {
    // ** addr: 0xb89db8, size: 0x38
    // 0xb89db8: EnterFrame
    //     0xb89db8: stp             fp, lr, [SP, #-0x10]!
    //     0xb89dbc: mov             fp, SP
    // 0xb89dc0: ldr             x0, [fp, #0x10]
    // 0xb89dc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb89dc4: ldur            w1, [x0, #0x17]
    // 0xb89dc8: DecompressPointer r1
    //     0xb89dc8: add             x1, x1, HEAP, lsl #32
    // 0xb89dcc: CheckStackOverflow
    //     0xb89dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89dd0: cmp             SP, x16
    //     0xb89dd4: b.ls            #0xb89de8
    // 0xb89dd8: r0 = onRefresh()
    //     0xb89dd8: bl              #0xb89df0  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onRefresh
    // 0xb89ddc: LeaveFrame
    //     0xb89ddc: mov             SP, fp
    //     0xb89de0: ldp             fp, lr, [SP], #0x10
    // 0xb89de4: ret
    //     0xb89de4: ret             
    // 0xb89de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89dec: b               #0xb89dd8
  }
  _ onRefresh(/* No info */) async {
    // ** addr: 0xb89df0, size: 0x40
    // 0xb89df0: EnterFrame
    //     0xb89df0: stp             fp, lr, [SP, #-0x10]!
    //     0xb89df4: mov             fp, SP
    // 0xb89df8: AllocStack(0x10)
    //     0xb89df8: sub             SP, SP, #0x10
    // 0xb89dfc: SetupParameters(DoaDetailBuilderController this /* r1 => r1, fp-0x10 */)
    //     0xb89dfc: stur            NULL, [fp, #-8]
    //     0xb89e00: stur            x1, [fp, #-0x10]
    // 0xb89e04: CheckStackOverflow
    //     0xb89e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89e08: cmp             SP, x16
    //     0xb89e0c: b.ls            #0xb89e28
    // 0xb89e10: InitAsync() -> Future<void?>
    //     0xb89e10: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb89e14: bl              #0x661298  ; InitAsyncStub
    // 0xb89e18: ldur            x1, [fp, #-0x10]
    // 0xb89e1c: r0 = executeOfflineMode()
    //     0xb89e1c: bl              #0x8f0914  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] _DoaDetailBuilderController&GetxController&StateMixin&OfflineMixin::executeOfflineMode
    // 0xb89e20: r0 = Null
    //     0xb89e20: mov             x0, NULL
    // 0xb89e24: r0 = ReturnAsyncNotFuture()
    //     0xb89e24: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb89e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89e2c: b               #0xb89e10
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef858, size: 0x5c
    // 0xbef858: EnterFrame
    //     0xbef858: stp             fp, lr, [SP, #-0x10]!
    //     0xbef85c: mov             fp, SP
    // 0xbef860: AllocStack(0x10)
    //     0xbef860: sub             SP, SP, #0x10
    // 0xbef864: SetupParameters(DoaDetailBuilderController this /* r1 => r1, fp-0x10 */)
    //     0xbef864: stur            NULL, [fp, #-8]
    //     0xbef868: stur            x1, [fp, #-0x10]
    // 0xbef86c: CheckStackOverflow
    //     0xbef86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef870: cmp             SP, x16
    //     0xbef874: b.ls            #0xbef8ac
    // 0xbef878: InitAsync() -> Future<ApiResult<List<Doa>>>
    //     0xbef878: add             x0, PP, #0x35, lsl #12  ; [pp+0x35dd0] TypeArguments: <ApiResult<List<Doa>>>
    //     0xbef87c: ldr             x0, [x0, #0xdd0]
    //     0xbef880: bl              #0x661298  ; InitAsyncStub
    // 0xbef884: ldur            x0, [fp, #-0x10]
    // 0xbef888: LoadField: r1 = r0->field_2f
    //     0xbef888: ldur            w1, [x0, #0x2f]
    // 0xbef88c: DecompressPointer r1
    //     0xbef88c: add             x1, x1, HEAP, lsl #32
    // 0xbef890: LoadField: r2 = r0->field_27
    //     0xbef890: ldur            x2, [x0, #0x27]
    // 0xbef894: r0 = LoadClassIdInstr(r1)
    //     0xbef894: ldur            x0, [x1, #-1]
    //     0xbef898: ubfx            x0, x0, #0xc, #0x14
    // 0xbef89c: r0 = GDT[cid_x0 + -0xfc7]()
    //     0xbef89c: sub             lr, x0, #0xfc7
    //     0xbef8a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbef8a4: blr             lr
    // 0xbef8a8: r0 = ReturnAsync()
    //     0xbef8a8: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef8ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef8ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef8b0: b               #0xbef878
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf630c, size: 0xb0
    // 0xbf630c: EnterFrame
    //     0xbf630c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6310: mov             fp, SP
    // 0xbf6314: AllocStack(0x20)
    //     0xbf6314: sub             SP, SP, #0x20
    // 0xbf6318: SetupParameters(DoaDetailBuilderController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xbf6318: stur            NULL, [fp, #-8]
    //     0xbf631c: stur            x1, [fp, #-0x10]
    //     0xbf6320: mov             x16, x2
    //     0xbf6324: mov             x2, x1
    //     0xbf6328: mov             x1, x16
    //     0xbf632c: stur            x1, [fp, #-0x18]
    // 0xbf6330: CheckStackOverflow
    //     0xbf6330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6334: cmp             SP, x16
    //     0xbf6338: b.ls            #0xbf63b4
    // 0xbf633c: InitAsync() -> Future<void?>
    //     0xbf633c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf6340: bl              #0x661298  ; InitAsyncStub
    // 0xbf6344: r1 = Function '<anonymous closure>':.
    //     0xbf6344: add             x1, PP, #0x35, lsl #12  ; [pp+0x35dc8] AnonymousClosure: (0xbf63bc), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onOfflineModeLoaded (0xbf630c)
    //     0xbf6348: ldr             x1, [x1, #0xdc8]
    // 0xbf634c: r2 = Null
    //     0xbf634c: mov             x2, NULL
    // 0xbf6350: r0 = AllocateClosure()
    //     0xbf6350: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf6354: ldur            x2, [fp, #-0x18]
    // 0xbf6358: r1 = LoadClassIdInstr(r2)
    //     0xbf6358: ldur            x1, [x2, #-1]
    //     0xbf635c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf6360: str             x0, [SP]
    // 0xbf6364: mov             x0, x1
    // 0xbf6368: mov             x1, x2
    // 0xbf636c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbf636c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbf6370: r0 = GDT[cid_x0 + 0x133e4]()
    //     0xbf6370: movz            x17, #0x33e4
    //     0xbf6374: movk            x17, #0x1, lsl #16
    //     0xbf6378: add             lr, x0, x17
    //     0xbf637c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6380: blr             lr
    // 0xbf6384: r0 = RxStatus()
    //     0xbf6384: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf6388: mov             x1, x0
    // 0xbf638c: r0 = false
    //     0xbf638c: add             x0, NULL, #0x30  ; false
    // 0xbf6390: StoreField: r1->field_f = r0
    //     0xbf6390: stur            w0, [x1, #0xf]
    // 0xbf6394: StoreField: r1->field_7 = r0
    //     0xbf6394: stur            w0, [x1, #7]
    // 0xbf6398: StoreField: r1->field_b = r0
    //     0xbf6398: stur            w0, [x1, #0xb]
    // 0xbf639c: mov             x3, x1
    // 0xbf63a0: ldur            x1, [fp, #-0x10]
    // 0xbf63a4: ldur            x2, [fp, #-0x18]
    // 0xbf63a8: r0 = change()
    //     0xbf63a8: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0xbf63ac: r0 = Null
    //     0xbf63ac: mov             x0, NULL
    // 0xbf63b0: r0 = ReturnAsyncNotFuture()
    //     0xbf63b0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf63b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf63b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf63b8: b               #0xbf633c
  }
  [closure] int <anonymous closure>(dynamic, Doa, Doa) {
    // ** addr: 0xbf63bc, size: 0x78
    // 0xbf63bc: EnterFrame
    //     0xbf63bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf63c0: mov             fp, SP
    // 0xbf63c4: CheckStackOverflow
    //     0xbf63c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf63c8: cmp             SP, x16
    //     0xbf63cc: b.ls            #0xbf642c
    // 0xbf63d0: ldr             x0, [fp, #0x18]
    // 0xbf63d4: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xbf63d4: ldur            x2, [x0, #0x17]
    // 0xbf63d8: ldr             x0, [fp, #0x10]
    // 0xbf63dc: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xbf63dc: ldur            x3, [x0, #0x17]
    // 0xbf63e0: r0 = BoxInt64Instr(r2)
    //     0xbf63e0: sbfiz           x0, x2, #1, #0x1f
    //     0xbf63e4: cmp             x2, x0, asr #1
    //     0xbf63e8: b.eq            #0xbf63f4
    //     0xbf63ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf63f0: stur            x2, [x0, #7]
    // 0xbf63f4: mov             x2, x0
    // 0xbf63f8: r0 = BoxInt64Instr(r3)
    //     0xbf63f8: sbfiz           x0, x3, #1, #0x1f
    //     0xbf63fc: cmp             x3, x0, asr #1
    //     0xbf6400: b.eq            #0xbf640c
    //     0xbf6404: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf6408: stur            x3, [x0, #7]
    // 0xbf640c: mov             x1, x2
    // 0xbf6410: mov             x2, x0
    // 0xbf6414: r0 = compareTo()
    //     0xbf6414: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0xbf6418: lsl             x1, x0, #1
    // 0xbf641c: mov             x0, x1
    // 0xbf6420: LeaveFrame
    //     0xbf6420: mov             SP, fp
    //     0xbf6424: ldp             fp, lr, [SP], #0x10
    // 0xbf6428: ret
    //     0xbf6428: ret             
    // 0xbf642c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf642c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6430: b               #0xbf63d0
  }
}
