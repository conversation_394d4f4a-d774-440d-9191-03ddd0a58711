// lib: , url: package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart

// class id: 1050175, size: 0x8
class :: {
}

// class id: 1958, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin extends GetxController
     with ShowCaseMixin {

  late BuildContext showCaseContext; // offset: 0x24

  _ onInit(/* No info */) {
    // ** addr: 0x8f0ed0, size: 0xc0
    // 0x8f0ed0: EnterFrame
    //     0x8f0ed0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0ed4: mov             fp, SP
    // 0x8f0ed8: AllocStack(0x20)
    //     0x8f0ed8: sub             SP, SP, #0x20
    // 0x8f0edc: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin this /* r1 => r0, fp-0x20 */)
    //     0x8f0edc: mov             x0, x1
    //     0x8f0ee0: stur            x1, [fp, #-0x20]
    // 0x8f0ee4: CheckStackOverflow
    //     0x8f0ee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0ee8: cmp             SP, x16
    //     0x8f0eec: b.ls            #0x8f0f80
    // 0x8f0ef0: r2 = LoadClassIdInstr(r0)
    //     0x8f0ef0: ldur            x2, [x0, #-1]
    //     0x8f0ef4: ubfx            x2, x2, #0xc, #0x14
    // 0x8f0ef8: stur            x2, [fp, #-0x18]
    // 0x8f0efc: LoadField: r3 = r0->field_1f
    //     0x8f0efc: ldur            w3, [x0, #0x1f]
    // 0x8f0f00: DecompressPointer r3
    //     0x8f0f00: add             x3, x3, HEAP, lsl #32
    // 0x8f0f04: stur            x3, [fp, #-0x10]
    // 0x8f0f08: r4 = 0
    //     0x8f0f08: movz            x4, #0
    // 0x8f0f0c: stur            x4, [fp, #-8]
    // 0x8f0f10: CheckStackOverflow
    //     0x8f0f10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0f14: cmp             SP, x16
    //     0x8f0f18: b.ls            #0x8f0f88
    // 0x8f0f1c: cmp             x2, #0x7a7
    // 0x8f0f20: b.ne            #0x8f0f2c
    // 0x8f0f24: r1 = 2
    //     0x8f0f24: movz            x1, #0x2
    // 0x8f0f28: b               #0x8f0f30
    // 0x8f0f2c: r1 = 3
    //     0x8f0f2c: movz            x1, #0x3
    // 0x8f0f30: cmp             x4, x1
    // 0x8f0f34: b.ge            #0x8f0f68
    // 0x8f0f38: r1 = <State<StatefulWidget>>
    //     0x8f0f38: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0x8f0f3c: r0 = LabeledGlobalKey()
    //     0x8f0f3c: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x8f0f40: ldur            x1, [fp, #-0x10]
    // 0x8f0f44: mov             x3, x0
    // 0x8f0f48: r2 = 0
    //     0x8f0f48: movz            x2, #0
    // 0x8f0f4c: r0 = insert()
    //     0x8f0f4c: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0x8f0f50: ldur            x0, [fp, #-8]
    // 0x8f0f54: add             x4, x0, #1
    // 0x8f0f58: ldur            x0, [fp, #-0x20]
    // 0x8f0f5c: ldur            x3, [fp, #-0x10]
    // 0x8f0f60: ldur            x2, [fp, #-0x18]
    // 0x8f0f64: b               #0x8f0f0c
    // 0x8f0f68: ldur            x1, [fp, #-0x20]
    // 0x8f0f6c: r0 = onInit()
    //     0x8f0f6c: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f0f70: r0 = Null
    //     0x8f0f70: mov             x0, NULL
    // 0x8f0f74: LeaveFrame
    //     0x8f0f74: mov             SP, fp
    //     0x8f0f78: ldp             fp, lr, [SP], #0x10
    // 0x8f0f7c: ret
    //     0x8f0f7c: ret             
    // 0x8f0f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0f80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0f84: b               #0x8f0ef0
    // 0x8f0f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0f8c: b               #0x8f0f1c
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91e090, size: 0x130
    // 0x91e090: EnterFrame
    //     0x91e090: stp             fp, lr, [SP, #-0x10]!
    //     0x91e094: mov             fp, SP
    // 0x91e098: AllocStack(0x18)
    //     0x91e098: sub             SP, SP, #0x18
    // 0x91e09c: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin this /* r1 => r1, fp-0x8 */)
    //     0x91e09c: stur            x1, [fp, #-8]
    // 0x91e0a0: CheckStackOverflow
    //     0x91e0a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e0a4: cmp             SP, x16
    //     0x91e0a8: b.ls            #0x91e1b4
    // 0x91e0ac: r1 = 1
    //     0x91e0ac: movz            x1, #0x1
    // 0x91e0b0: r0 = AllocateContext()
    //     0x91e0b0: bl              #0xec126c  ; AllocateContextStub
    // 0x91e0b4: mov             x1, x0
    // 0x91e0b8: ldur            x0, [fp, #-8]
    // 0x91e0bc: StoreField: r1->field_f = r0
    //     0x91e0bc: stur            w0, [x1, #0xf]
    // 0x91e0c0: r0 = LoadStaticField(0x7d4)
    //     0x91e0c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91e0c4: ldr             x0, [x0, #0xfa8]
    // 0x91e0c8: cmp             w0, NULL
    // 0x91e0cc: b.eq            #0x91e1bc
    // 0x91e0d0: LoadField: r3 = r0->field_53
    //     0x91e0d0: ldur            w3, [x0, #0x53]
    // 0x91e0d4: DecompressPointer r3
    //     0x91e0d4: add             x3, x3, HEAP, lsl #32
    // 0x91e0d8: stur            x3, [fp, #-0x10]
    // 0x91e0dc: LoadField: r0 = r3->field_7
    //     0x91e0dc: ldur            w0, [x3, #7]
    // 0x91e0e0: DecompressPointer r0
    //     0x91e0e0: add             x0, x0, HEAP, lsl #32
    // 0x91e0e4: mov             x2, x1
    // 0x91e0e8: stur            x0, [fp, #-8]
    // 0x91e0ec: r1 = Function '<anonymous closure>':.
    //     0x91e0ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40648] AnonymousClosure: (0x91e1c0), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin::onReady (0x91e090)
    //     0x91e0f0: ldr             x1, [x1, #0x648]
    // 0x91e0f4: r0 = AllocateClosure()
    //     0x91e0f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x91e0f8: ldur            x2, [fp, #-8]
    // 0x91e0fc: mov             x3, x0
    // 0x91e100: r1 = Null
    //     0x91e100: mov             x1, NULL
    // 0x91e104: stur            x3, [fp, #-8]
    // 0x91e108: cmp             w2, NULL
    // 0x91e10c: b.eq            #0x91e12c
    // 0x91e110: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x91e110: ldur            w4, [x2, #0x17]
    // 0x91e114: DecompressPointer r4
    //     0x91e114: add             x4, x4, HEAP, lsl #32
    // 0x91e118: r8 = X0
    //     0x91e118: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x91e11c: LoadField: r9 = r4->field_7
    //     0x91e11c: ldur            x9, [x4, #7]
    // 0x91e120: r3 = Null
    //     0x91e120: add             x3, PP, #0x40, lsl #12  ; [pp+0x40650] Null
    //     0x91e124: ldr             x3, [x3, #0x650]
    // 0x91e128: blr             x9
    // 0x91e12c: ldur            x0, [fp, #-0x10]
    // 0x91e130: LoadField: r1 = r0->field_b
    //     0x91e130: ldur            w1, [x0, #0xb]
    // 0x91e134: LoadField: r2 = r0->field_f
    //     0x91e134: ldur            w2, [x0, #0xf]
    // 0x91e138: DecompressPointer r2
    //     0x91e138: add             x2, x2, HEAP, lsl #32
    // 0x91e13c: LoadField: r3 = r2->field_b
    //     0x91e13c: ldur            w3, [x2, #0xb]
    // 0x91e140: r2 = LoadInt32Instr(r1)
    //     0x91e140: sbfx            x2, x1, #1, #0x1f
    // 0x91e144: stur            x2, [fp, #-0x18]
    // 0x91e148: r1 = LoadInt32Instr(r3)
    //     0x91e148: sbfx            x1, x3, #1, #0x1f
    // 0x91e14c: cmp             x2, x1
    // 0x91e150: b.ne            #0x91e15c
    // 0x91e154: mov             x1, x0
    // 0x91e158: r0 = _growToNextCapacity()
    //     0x91e158: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x91e15c: ldur            x2, [fp, #-0x10]
    // 0x91e160: ldur            x3, [fp, #-0x18]
    // 0x91e164: add             x4, x3, #1
    // 0x91e168: lsl             x5, x4, #1
    // 0x91e16c: StoreField: r2->field_b = r5
    //     0x91e16c: stur            w5, [x2, #0xb]
    // 0x91e170: LoadField: r1 = r2->field_f
    //     0x91e170: ldur            w1, [x2, #0xf]
    // 0x91e174: DecompressPointer r1
    //     0x91e174: add             x1, x1, HEAP, lsl #32
    // 0x91e178: ldur            x0, [fp, #-8]
    // 0x91e17c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x91e17c: add             x25, x1, x3, lsl #2
    //     0x91e180: add             x25, x25, #0xf
    //     0x91e184: str             w0, [x25]
    //     0x91e188: tbz             w0, #0, #0x91e1a4
    //     0x91e18c: ldurb           w16, [x1, #-1]
    //     0x91e190: ldurb           w17, [x0, #-1]
    //     0x91e194: and             x16, x17, x16, lsr #2
    //     0x91e198: tst             x16, HEAP, lsr #32
    //     0x91e19c: b.eq            #0x91e1a4
    //     0x91e1a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x91e1a4: r0 = Null
    //     0x91e1a4: mov             x0, NULL
    // 0x91e1a8: LeaveFrame
    //     0x91e1a8: mov             SP, fp
    //     0x91e1ac: ldp             fp, lr, [SP], #0x10
    // 0x91e1b0: ret
    //     0x91e1b0: ret             
    // 0x91e1b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e1b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e1b8: b               #0x91e0ac
    // 0x91e1bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91e1bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x91e1c0, size: 0xa4
    // 0x91e1c0: EnterFrame
    //     0x91e1c0: stp             fp, lr, [SP, #-0x10]!
    //     0x91e1c4: mov             fp, SP
    // 0x91e1c8: AllocStack(0x18)
    //     0x91e1c8: sub             SP, SP, #0x18
    // 0x91e1cc: SetupParameters()
    //     0x91e1cc: ldr             x0, [fp, #0x18]
    //     0x91e1d0: ldur            w1, [x0, #0x17]
    //     0x91e1d4: add             x1, x1, HEAP, lsl #32
    //     0x91e1d8: stur            x1, [fp, #-8]
    // 0x91e1dc: CheckStackOverflow
    //     0x91e1dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e1e0: cmp             SP, x16
    //     0x91e1e4: b.ls            #0x91e25c
    // 0x91e1e8: r0 = Duration()
    //     0x91e1e8: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x91e1ec: mov             x3, x0
    // 0x91e1f0: r0 = 200000
    //     0x91e1f0: movz            x0, #0xd40
    //     0x91e1f4: movk            x0, #0x3, lsl #16
    // 0x91e1f8: stur            x3, [fp, #-0x10]
    // 0x91e1fc: StoreField: r3->field_7 = r0
    //     0x91e1fc: stur            x0, [x3, #7]
    // 0x91e200: ldur            x0, [fp, #-8]
    // 0x91e204: LoadField: r2 = r0->field_f
    //     0x91e204: ldur            w2, [x0, #0xf]
    // 0x91e208: DecompressPointer r2
    //     0x91e208: add             x2, x2, HEAP, lsl #32
    // 0x91e20c: r0 = LoadClassIdInstr(r2)
    //     0x91e20c: ldur            x0, [x2, #-1]
    //     0x91e210: ubfx            x0, x0, #0xc, #0x14
    // 0x91e214: cmp             x0, #0x7a7
    // 0x91e218: b.ne            #0x91e22c
    // 0x91e21c: r1 = Function 'startShowCase':.
    //     0x91e21c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40660] AnonymousClosure: (0x91e62c), in [package:nuonline/app/modules/tasbih/controllers/tasbih_controller.dart] TasbihController::startShowCase (0x91e664)
    //     0x91e220: ldr             x1, [x1, #0x660]
    // 0x91e224: r0 = AllocateClosure()
    //     0x91e224: bl              #0xec1630  ; AllocateClosureStub
    // 0x91e228: b               #0x91e238
    // 0x91e22c: r1 = Function 'startShowCase':.
    //     0x91e22c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40668] AnonymousClosure: (0x91e264), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::startShowCase (0x91e29c)
    //     0x91e230: ldr             x1, [x1, #0x668]
    // 0x91e234: r0 = AllocateClosure()
    //     0x91e234: bl              #0xec1630  ; AllocateClosureStub
    // 0x91e238: str             x0, [SP]
    // 0x91e23c: ldur            x2, [fp, #-0x10]
    // 0x91e240: r1 = <void?>
    //     0x91e240: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x91e244: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x91e244: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x91e248: r0 = Future.delayed()
    //     0x91e248: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0x91e24c: r0 = Null
    //     0x91e24c: mov             x0, NULL
    // 0x91e250: LeaveFrame
    //     0x91e250: mov             SP, fp
    //     0x91e254: ldp             fp, lr, [SP], #0x10
    // 0x91e258: ret
    //     0x91e258: ret             
    // 0x91e25c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e25c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e260: b               #0x91e1e8
  }
  _ startShowCase(/* No info */) {
    // ** addr: 0x91e2ec, size: 0x70
    // 0x91e2ec: EnterFrame
    //     0x91e2ec: stp             fp, lr, [SP, #-0x10]!
    //     0x91e2f0: mov             fp, SP
    // 0x91e2f4: AllocStack(0x8)
    //     0x91e2f4: sub             SP, SP, #8
    // 0x91e2f8: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin this /* r1 => r0, fp-0x8 */)
    //     0x91e2f8: mov             x0, x1
    //     0x91e2fc: stur            x1, [fp, #-8]
    // 0x91e300: CheckStackOverflow
    //     0x91e300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e304: cmp             SP, x16
    //     0x91e308: b.ls            #0x91e348
    // 0x91e30c: LoadField: r1 = r0->field_23
    //     0x91e30c: ldur            w1, [x0, #0x23]
    // 0x91e310: DecompressPointer r1
    //     0x91e310: add             x1, x1, HEAP, lsl #32
    // 0x91e314: r16 = Sentinel
    //     0x91e314: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91e318: cmp             w1, w16
    // 0x91e31c: b.eq            #0x91e350
    // 0x91e320: r0 = of()
    //     0x91e320: bl              #0x91e504  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::of
    // 0x91e324: mov             x1, x0
    // 0x91e328: ldur            x0, [fp, #-8]
    // 0x91e32c: LoadField: r2 = r0->field_1f
    //     0x91e32c: ldur            w2, [x0, #0x1f]
    // 0x91e330: DecompressPointer r2
    //     0x91e330: add             x2, x2, HEAP, lsl #32
    // 0x91e334: r0 = startShowCase()
    //     0x91e334: bl              #0x91e35c  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::startShowCase
    // 0x91e338: r0 = Null
    //     0x91e338: mov             x0, NULL
    // 0x91e33c: LeaveFrame
    //     0x91e33c: mov             SP, fp
    //     0x91e340: ldp             fp, lr, [SP], #0x10
    // 0x91e344: ret
    //     0x91e344: ret             
    // 0x91e348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e34c: b               #0x91e30c
    // 0x91e350: r9 = showCaseContext
    //     0x91e350: add             x9, PP, #0x40, lsl #12  ; [pp+0x40670] Field <_DoaDetailController&GetxController&<EMAIL>>: late (offset: 0x24)
    //     0x91e354: ldr             x9, [x9, #0x670]
    // 0x91e358: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91e358: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 1960, size: 0x30, field offset: 0x28
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin&StateMixin extends _DoaDetailController&GetxController&ShowCaseMixin
     with StateMixin<X0> {

  get _ value(/* No info */) {
    // ** addr: 0x6fb858, size: 0x48
    // 0x6fb858: EnterFrame
    //     0x6fb858: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb85c: mov             fp, SP
    // 0x6fb860: AllocStack(0x8)
    //     0x6fb860: sub             SP, SP, #8
    // 0x6fb864: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin this /* r1 => r0, fp-0x8 */)
    //     0x6fb864: mov             x0, x1
    //     0x6fb868: stur            x1, [fp, #-8]
    // 0x6fb86c: CheckStackOverflow
    //     0x6fb86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fb870: cmp             SP, x16
    //     0x6fb874: b.ls            #0x6fb898
    // 0x6fb878: mov             x1, x0
    // 0x6fb87c: r0 = notifyChildrens()
    //     0x6fb87c: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x6fb880: ldur            x1, [fp, #-8]
    // 0x6fb884: LoadField: r0 = r1->field_27
    //     0x6fb884: ldur            w0, [x1, #0x27]
    // 0x6fb888: DecompressPointer r0
    //     0x6fb888: add             x0, x0, HEAP, lsl #32
    // 0x6fb88c: LeaveFrame
    //     0x6fb88c: mov             SP, fp
    //     0x6fb890: ldp             fp, lr, [SP], #0x10
    // 0x6fb894: ret
    //     0x6fb894: ret             
    // 0x6fb898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fb898: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fb89c: b               #0x6fb878
  }
  get _ status(/* No info */) {
    // ** addr: 0x7f82fc, size: 0x9c
    // 0x7f82fc: EnterFrame
    //     0x7f82fc: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8300: mov             fp, SP
    // 0x7f8304: AllocStack(0x8)
    //     0x7f8304: sub             SP, SP, #8
    // 0x7f8308: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin this /* r1 => r0, fp-0x8 */)
    //     0x7f8308: mov             x0, x1
    //     0x7f830c: stur            x1, [fp, #-8]
    // 0x7f8310: CheckStackOverflow
    //     0x7f8310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8314: cmp             SP, x16
    //     0x7f8318: b.ls            #0x7f8390
    // 0x7f831c: mov             x1, x0
    // 0x7f8320: r0 = notifyChildrens()
    //     0x7f8320: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x7f8324: ldur            x0, [fp, #-8]
    // 0x7f8328: LoadField: r1 = r0->field_2b
    //     0x7f8328: ldur            w1, [x0, #0x2b]
    // 0x7f832c: DecompressPointer r1
    //     0x7f832c: add             x1, x1, HEAP, lsl #32
    // 0x7f8330: cmp             w1, NULL
    // 0x7f8334: b.ne            #0x7f8380
    // 0x7f8338: r0 = RxStatus()
    //     0x7f8338: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7f833c: mov             x1, x0
    // 0x7f8340: r2 = false
    //     0x7f8340: add             x2, NULL, #0x30  ; false
    // 0x7f8344: StoreField: r1->field_f = r2
    //     0x7f8344: stur            w2, [x1, #0xf]
    // 0x7f8348: r3 = true
    //     0x7f8348: add             x3, NULL, #0x20  ; true
    // 0x7f834c: StoreField: r1->field_7 = r3
    //     0x7f834c: stur            w3, [x1, #7]
    // 0x7f8350: StoreField: r1->field_b = r2
    //     0x7f8350: stur            w2, [x1, #0xb]
    // 0x7f8354: mov             x0, x1
    // 0x7f8358: ldur            x2, [fp, #-8]
    // 0x7f835c: StoreField: r2->field_2b = r0
    //     0x7f835c: stur            w0, [x2, #0x2b]
    //     0x7f8360: ldurb           w16, [x2, #-1]
    //     0x7f8364: ldurb           w17, [x0, #-1]
    //     0x7f8368: and             x16, x17, x16, lsr #2
    //     0x7f836c: tst             x16, HEAP, lsr #32
    //     0x7f8370: b.eq            #0x7f8378
    //     0x7f8374: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7f8378: mov             x0, x1
    // 0x7f837c: b               #0x7f8384
    // 0x7f8380: mov             x0, x1
    // 0x7f8384: LeaveFrame
    //     0x7f8384: mov             SP, fp
    //     0x7f8388: ldp             fp, lr, [SP], #0x10
    // 0x7f838c: ret
    //     0x7f838c: ret             
    // 0x7f8390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8394: b               #0x7f831c
  }
  _ change(/* No info */) {
    // ** addr: 0xbf65bc, size: 0xbc
    // 0xbf65bc: EnterFrame
    //     0xbf65bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf65c0: mov             fp, SP
    // 0xbf65c4: AllocStack(0x20)
    //     0xbf65c4: sub             SP, SP, #0x20
    // 0xbf65c8: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0xbf65c8: stur            x1, [fp, #-8]
    //     0xbf65cc: mov             x16, x2
    //     0xbf65d0: mov             x2, x1
    //     0xbf65d4: mov             x1, x16
    //     0xbf65d8: mov             x0, x3
    //     0xbf65dc: stur            x1, [fp, #-0x10]
    // 0xbf65e0: CheckStackOverflow
    //     0xbf65e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf65e4: cmp             SP, x16
    //     0xbf65e8: b.ls            #0xbf6670
    // 0xbf65ec: StoreField: r2->field_2b = r0
    //     0xbf65ec: stur            w0, [x2, #0x2b]
    //     0xbf65f0: ldurb           w16, [x2, #-1]
    //     0xbf65f4: ldurb           w17, [x0, #-1]
    //     0xbf65f8: and             x16, x17, x16, lsr #2
    //     0xbf65fc: tst             x16, HEAP, lsr #32
    //     0xbf6600: b.eq            #0xbf6608
    //     0xbf6604: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbf6608: LoadField: r0 = r2->field_27
    //     0xbf6608: ldur            w0, [x2, #0x27]
    // 0xbf660c: DecompressPointer r0
    //     0xbf660c: add             x0, x0, HEAP, lsl #32
    // 0xbf6610: r3 = LoadClassIdInstr(r1)
    //     0xbf6610: ldur            x3, [x1, #-1]
    //     0xbf6614: ubfx            x3, x3, #0xc, #0x14
    // 0xbf6618: stp             x0, x1, [SP]
    // 0xbf661c: mov             x0, x3
    // 0xbf6620: mov             lr, x0
    // 0xbf6624: ldr             lr, [x21, lr, lsl #3]
    // 0xbf6628: blr             lr
    // 0xbf662c: tbz             w0, #4, #0xbf6658
    // 0xbf6630: ldur            x1, [fp, #-8]
    // 0xbf6634: ldur            x0, [fp, #-0x10]
    // 0xbf6638: StoreField: r1->field_27 = r0
    //     0xbf6638: stur            w0, [x1, #0x27]
    //     0xbf663c: ldurb           w16, [x1, #-1]
    //     0xbf6640: ldurb           w17, [x0, #-1]
    //     0xbf6644: and             x16, x17, x16, lsr #2
    //     0xbf6648: tst             x16, HEAP, lsr #32
    //     0xbf664c: b.eq            #0xbf6654
    //     0xbf6650: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbf6654: b               #0xbf665c
    // 0xbf6658: ldur            x1, [fp, #-8]
    // 0xbf665c: r0 = _notifyUpdate()
    //     0xbf665c: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0xbf6660: r0 = Null
    //     0xbf6660: mov             x0, NULL
    // 0xbf6664: LeaveFrame
    //     0xbf6664: mov             SP, fp
    //     0xbf6668: ldp             fp, lr, [SP], #0x10
    // 0xbf666c: ret
    //     0xbf666c: ret             
    // 0xbf6670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf6670: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6674: b               #0xbf65ec
  }
}

// class id: 1961, size: 0x30, field offset: 0x30
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin extends _DoaDetailController&GetxController&ShowCaseMixin&StateMixin
     with OfflineMixin<X0> {

  _ executeOfflineMode(/* No info */) async {
    // ** addr: 0x8f0bfc, size: 0x80
    // 0x8f0bfc: EnterFrame
    //     0x8f0bfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0c00: mov             fp, SP
    // 0x8f0c04: AllocStack(0x30)
    //     0x8f0c04: sub             SP, SP, #0x30
    // 0x8f0c08: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f0c08: stur            NULL, [fp, #-8]
    //     0x8f0c0c: stur            x1, [fp, #-0x10]
    // 0x8f0c10: CheckStackOverflow
    //     0x8f0c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0c14: cmp             SP, x16
    //     0x8f0c18: b.ls            #0x8f0c74
    // 0x8f0c1c: r1 = 1
    //     0x8f0c1c: movz            x1, #0x1
    // 0x8f0c20: r0 = AllocateContext()
    //     0x8f0c20: bl              #0xec126c  ; AllocateContextStub
    // 0x8f0c24: mov             x2, x0
    // 0x8f0c28: ldur            x1, [fp, #-0x10]
    // 0x8f0c2c: stur            x2, [fp, #-0x18]
    // 0x8f0c30: StoreField: r2->field_f = r1
    //     0x8f0c30: stur            w1, [x2, #0xf]
    // 0x8f0c34: InitAsync() -> Future<void?>
    //     0x8f0c34: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f0c38: bl              #0x661298  ; InitAsyncStub
    // 0x8f0c3c: ldur            x1, [fp, #-0x10]
    // 0x8f0c40: r0 = onOfflineModeRequested()
    //     0x8f0c40: bl              #0xbef8b4  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOfflineModeRequested
    // 0x8f0c44: ldur            x2, [fp, #-0x18]
    // 0x8f0c48: r1 = Function '<anonymous closure>':.
    //     0x8f0c48: add             x1, PP, #0x40, lsl #12  ; [pp+0x40620] AnonymousClosure: (0x8f0c7c), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin::executeOfflineMode (0x8f0bfc)
    //     0x8f0c4c: ldr             x1, [x1, #0x620]
    // 0x8f0c50: stur            x0, [fp, #-0x10]
    // 0x8f0c54: r0 = AllocateClosure()
    //     0x8f0c54: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0c58: r16 = <void?>
    //     0x8f0c58: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f0c5c: ldur            lr, [fp, #-0x10]
    // 0x8f0c60: stp             lr, x16, [SP, #8]
    // 0x8f0c64: str             x0, [SP]
    // 0x8f0c68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f0c68: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f0c6c: r0 = then()
    //     0x8f0c6c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f0c70: r0 = ReturnAsync()
    //     0x8f0c70: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f0c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0c74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0c78: b               #0x8f0c1c
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<DoaSubCategory>>) {
    // ** addr: 0x8f0c7c, size: 0x94
    // 0x8f0c7c: EnterFrame
    //     0x8f0c7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0c80: mov             fp, SP
    // 0x8f0c84: AllocStack(0x28)
    //     0x8f0c84: sub             SP, SP, #0x28
    // 0x8f0c88: SetupParameters()
    //     0x8f0c88: ldr             x0, [fp, #0x18]
    //     0x8f0c8c: ldur            w3, [x0, #0x17]
    //     0x8f0c90: add             x3, x3, HEAP, lsl #32
    //     0x8f0c94: stur            x3, [fp, #-8]
    // 0x8f0c98: CheckStackOverflow
    //     0x8f0c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0c9c: cmp             SP, x16
    //     0x8f0ca0: b.ls            #0x8f0d08
    // 0x8f0ca4: mov             x2, x3
    // 0x8f0ca8: r1 = Function '<anonymous closure>':.
    //     0x8f0ca8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40628] AnonymousClosure: (0x8f0d68), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin::executeOfflineMode (0x8f0bfc)
    //     0x8f0cac: ldr             x1, [x1, #0x628]
    // 0x8f0cb0: r0 = AllocateClosure()
    //     0x8f0cb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0cb4: ldur            x2, [fp, #-8]
    // 0x8f0cb8: r1 = Function '<anonymous closure>':.
    //     0x8f0cb8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40630] AnonymousClosure: (0x8f0d10), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin::executeOfflineMode (0x8f0bfc)
    //     0x8f0cbc: ldr             x1, [x1, #0x630]
    // 0x8f0cc0: stur            x0, [fp, #-8]
    // 0x8f0cc4: r0 = AllocateClosure()
    //     0x8f0cc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0cc8: mov             x1, x0
    // 0x8f0ccc: ldr             x0, [fp, #0x10]
    // 0x8f0cd0: r2 = LoadClassIdInstr(r0)
    //     0x8f0cd0: ldur            x2, [x0, #-1]
    //     0x8f0cd4: ubfx            x2, x2, #0xc, #0x14
    // 0x8f0cd8: r16 = <Object?>
    //     0x8f0cd8: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f0cdc: stp             x0, x16, [SP, #0x10]
    // 0x8f0ce0: ldur            x16, [fp, #-8]
    // 0x8f0ce4: stp             x16, x1, [SP]
    // 0x8f0ce8: mov             x0, x2
    // 0x8f0cec: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f0cec: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f0cf0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f0cf0: sub             lr, x0, #1, lsl #12
    //     0x8f0cf4: ldr             lr, [x21, lr, lsl #3]
    //     0x8f0cf8: blr             lr
    // 0x8f0cfc: LeaveFrame
    //     0x8f0cfc: mov             SP, fp
    //     0x8f0d00: ldp             fp, lr, [SP], #0x10
    // 0x8f0d04: ret
    //     0x8f0d04: ret             
    // 0x8f0d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0d0c: b               #0x8f0ca4
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f0d10, size: 0x58
    // 0x8f0d10: EnterFrame
    //     0x8f0d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0d14: mov             fp, SP
    // 0x8f0d18: AllocStack(0x8)
    //     0x8f0d18: sub             SP, SP, #8
    // 0x8f0d1c: SetupParameters()
    //     0x8f0d1c: ldr             x0, [fp, #0x18]
    //     0x8f0d20: ldur            w1, [x0, #0x17]
    //     0x8f0d24: add             x1, x1, HEAP, lsl #32
    // 0x8f0d28: CheckStackOverflow
    //     0x8f0d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0d2c: cmp             SP, x16
    //     0x8f0d30: b.ls            #0x8f0d60
    // 0x8f0d34: LoadField: r0 = r1->field_f
    //     0x8f0d34: ldur            w0, [x1, #0xf]
    // 0x8f0d38: DecompressPointer r0
    //     0x8f0d38: add             x0, x0, HEAP, lsl #32
    // 0x8f0d3c: ldr             x1, [fp, #0x10]
    // 0x8f0d40: stur            x0, [fp, #-8]
    // 0x8f0d44: r0 = getErrorMessage()
    //     0x8f0d44: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f0d48: ldur            x1, [fp, #-8]
    // 0x8f0d4c: mov             x2, x0
    // 0x8f0d50: r0 = onOnlineModeFailure()
    //     0x8f0d50: bl              #0xe34c68  ; [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] HajiHikmahController::onOnlineModeFailure
    // 0x8f0d54: LeaveFrame
    //     0x8f0d54: mov             SP, fp
    //     0x8f0d58: ldp             fp, lr, [SP], #0x10
    // 0x8f0d5c: ret
    //     0x8f0d5c: ret             
    // 0x8f0d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0d64: b               #0x8f0d34
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<DoaSubCategory>, Pagination?) {
    // ** addr: 0x8f0d68, size: 0x48
    // 0x8f0d68: EnterFrame
    //     0x8f0d68: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0d6c: mov             fp, SP
    // 0x8f0d70: ldr             x0, [fp, #0x20]
    // 0x8f0d74: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0d74: ldur            w1, [x0, #0x17]
    // 0x8f0d78: DecompressPointer r1
    //     0x8f0d78: add             x1, x1, HEAP, lsl #32
    // 0x8f0d7c: CheckStackOverflow
    //     0x8f0d7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0d80: cmp             SP, x16
    //     0x8f0d84: b.ls            #0x8f0da8
    // 0x8f0d88: LoadField: r0 = r1->field_f
    //     0x8f0d88: ldur            w0, [x1, #0xf]
    // 0x8f0d8c: DecompressPointer r0
    //     0x8f0d8c: add             x0, x0, HEAP, lsl #32
    // 0x8f0d90: mov             x1, x0
    // 0x8f0d94: ldr             x2, [fp, #0x18]
    // 0x8f0d98: r0 = onOfflineModeLoaded()
    //     0x8f0d98: bl              #0xbf6434  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOfflineModeLoaded
    // 0x8f0d9c: LeaveFrame
    //     0x8f0d9c: mov             SP, fp
    //     0x8f0da0: ldp             fp, lr, [SP], #0x10
    // 0x8f0da4: ret
    //     0x8f0da4: ret             
    // 0x8f0da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0dac: b               #0x8f0d88
  }
}

// class id: 1962, size: 0x30, field offset: 0x30
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin extends _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin
     with OnlineSyncMixin<X0> {

  _ executeOnlineMode(/* No info */) async {
    // ** addr: 0x8f157c, size: 0x88
    // 0x8f157c: EnterFrame
    //     0x8f157c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1580: mov             fp, SP
    // 0x8f1584: AllocStack(0x38)
    //     0x8f1584: sub             SP, SP, #0x38
    // 0x8f1588: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8f1588: stur            NULL, [fp, #-8]
    //     0x8f158c: stur            x1, [fp, #-0x10]
    //     0x8f1590: stur            x2, [fp, #-0x18]
    // 0x8f1594: CheckStackOverflow
    //     0x8f1594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1598: cmp             SP, x16
    //     0x8f159c: b.ls            #0x8f15fc
    // 0x8f15a0: r1 = 1
    //     0x8f15a0: movz            x1, #0x1
    // 0x8f15a4: r0 = AllocateContext()
    //     0x8f15a4: bl              #0xec126c  ; AllocateContextStub
    // 0x8f15a8: mov             x2, x0
    // 0x8f15ac: ldur            x1, [fp, #-0x10]
    // 0x8f15b0: stur            x2, [fp, #-0x20]
    // 0x8f15b4: StoreField: r2->field_f = r1
    //     0x8f15b4: stur            w1, [x2, #0xf]
    // 0x8f15b8: InitAsync() -> Future<void?>
    //     0x8f15b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f15bc: bl              #0x661298  ; InitAsyncStub
    // 0x8f15c0: ldur            x1, [fp, #-0x10]
    // 0x8f15c4: ldur            x2, [fp, #-0x18]
    // 0x8f15c8: r0 = onOnlineModeRequested()
    //     0x8f15c8: bl              #0xe35ccc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeRequested
    // 0x8f15cc: ldur            x2, [fp, #-0x20]
    // 0x8f15d0: r1 = Function '<anonymous closure>':.
    //     0x8f15d0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40600] AnonymousClosure: (0x8f1604), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin::executeOnlineMode (0x8f157c)
    //     0x8f15d4: ldr             x1, [x1, #0x600]
    // 0x8f15d8: stur            x0, [fp, #-0x10]
    // 0x8f15dc: r0 = AllocateClosure()
    //     0x8f15dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f15e0: r16 = <void?>
    //     0x8f15e0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f15e4: ldur            lr, [fp, #-0x10]
    // 0x8f15e8: stp             lr, x16, [SP, #8]
    // 0x8f15ec: str             x0, [SP]
    // 0x8f15f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f15f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f15f4: r0 = then()
    //     0x8f15f4: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f15f8: r0 = ReturnAsync()
    //     0x8f15f8: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f15fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f15fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1600: b               #0x8f15a0
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<Doa>>) {
    // ** addr: 0x8f1604, size: 0x94
    // 0x8f1604: EnterFrame
    //     0x8f1604: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1608: mov             fp, SP
    // 0x8f160c: AllocStack(0x28)
    //     0x8f160c: sub             SP, SP, #0x28
    // 0x8f1610: SetupParameters()
    //     0x8f1610: ldr             x0, [fp, #0x18]
    //     0x8f1614: ldur            w3, [x0, #0x17]
    //     0x8f1618: add             x3, x3, HEAP, lsl #32
    //     0x8f161c: stur            x3, [fp, #-8]
    // 0x8f1620: CheckStackOverflow
    //     0x8f1620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1624: cmp             SP, x16
    //     0x8f1628: b.ls            #0x8f1690
    // 0x8f162c: mov             x2, x3
    // 0x8f1630: r1 = Function '<anonymous closure>':.
    //     0x8f1630: add             x1, PP, #0x40, lsl #12  ; [pp+0x40608] AnonymousClosure: (0x8f16ec), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin::executeOnlineMode (0x8f157c)
    //     0x8f1634: ldr             x1, [x1, #0x608]
    // 0x8f1638: r0 = AllocateClosure()
    //     0x8f1638: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f163c: ldur            x2, [fp, #-8]
    // 0x8f1640: r1 = Function '<anonymous closure>':.
    //     0x8f1640: add             x1, PP, #0x40, lsl #12  ; [pp+0x40610] AnonymousClosure: (0x8f1698), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin::executeOnlineMode (0x8f157c)
    //     0x8f1644: ldr             x1, [x1, #0x610]
    // 0x8f1648: stur            x0, [fp, #-8]
    // 0x8f164c: r0 = AllocateClosure()
    //     0x8f164c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f1650: mov             x1, x0
    // 0x8f1654: ldr             x0, [fp, #0x10]
    // 0x8f1658: r2 = LoadClassIdInstr(r0)
    //     0x8f1658: ldur            x2, [x0, #-1]
    //     0x8f165c: ubfx            x2, x2, #0xc, #0x14
    // 0x8f1660: r16 = <Object?>
    //     0x8f1660: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f1664: stp             x0, x16, [SP, #0x10]
    // 0x8f1668: ldur            x16, [fp, #-8]
    // 0x8f166c: stp             x16, x1, [SP]
    // 0x8f1670: mov             x0, x2
    // 0x8f1674: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f1674: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f1678: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f1678: sub             lr, x0, #1, lsl #12
    //     0x8f167c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f1680: blr             lr
    // 0x8f1684: LeaveFrame
    //     0x8f1684: mov             SP, fp
    //     0x8f1688: ldp             fp, lr, [SP], #0x10
    // 0x8f168c: ret
    //     0x8f168c: ret             
    // 0x8f1690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1694: b               #0x8f162c
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f1698, size: 0x54
    // 0x8f1698: EnterFrame
    //     0x8f1698: stp             fp, lr, [SP, #-0x10]!
    //     0x8f169c: mov             fp, SP
    // 0x8f16a0: AllocStack(0x8)
    //     0x8f16a0: sub             SP, SP, #8
    // 0x8f16a4: SetupParameters()
    //     0x8f16a4: ldr             x0, [fp, #0x18]
    //     0x8f16a8: ldur            w2, [x0, #0x17]
    //     0x8f16ac: add             x2, x2, HEAP, lsl #32
    //     0x8f16b0: stur            x2, [fp, #-8]
    // 0x8f16b4: CheckStackOverflow
    //     0x8f16b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f16b8: cmp             SP, x16
    //     0x8f16bc: b.ls            #0x8f16e4
    // 0x8f16c0: ldr             x1, [fp, #0x10]
    // 0x8f16c4: r0 = getErrorMessage()
    //     0x8f16c4: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f16c8: ldur            x0, [fp, #-8]
    // 0x8f16cc: LoadField: r1 = r0->field_f
    //     0x8f16cc: ldur            w1, [x0, #0xf]
    // 0x8f16d0: DecompressPointer r1
    //     0x8f16d0: add             x1, x1, HEAP, lsl #32
    // 0x8f16d4: r0 = onOnlineModeFailure()
    //     0x8f16d4: bl              #0xe35dcc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeFailure
    // 0x8f16d8: LeaveFrame
    //     0x8f16d8: mov             SP, fp
    //     0x8f16dc: ldp             fp, lr, [SP], #0x10
    // 0x8f16e0: ret
    //     0x8f16e0: ret             
    // 0x8f16e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f16e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f16e8: b               #0x8f16c0
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<Doa>, Pagination?) {
    // ** addr: 0x8f16ec, size: 0x48
    // 0x8f16ec: EnterFrame
    //     0x8f16ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8f16f0: mov             fp, SP
    // 0x8f16f4: ldr             x0, [fp, #0x20]
    // 0x8f16f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f16f8: ldur            w1, [x0, #0x17]
    // 0x8f16fc: DecompressPointer r1
    //     0x8f16fc: add             x1, x1, HEAP, lsl #32
    // 0x8f1700: CheckStackOverflow
    //     0x8f1700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1704: cmp             SP, x16
    //     0x8f1708: b.ls            #0x8f172c
    // 0x8f170c: LoadField: r0 = r1->field_f
    //     0x8f170c: ldur            w0, [x1, #0xf]
    // 0x8f1710: DecompressPointer r0
    //     0x8f1710: add             x0, x0, HEAP, lsl #32
    // 0x8f1714: mov             x1, x0
    // 0x8f1718: ldr             x2, [fp, #0x18]
    // 0x8f171c: r0 = onOnlineModeLoaded()
    //     0x8f171c: bl              #0xe35d2c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeLoaded
    // 0x8f1720: LeaveFrame
    //     0x8f1720: mov             SP, fp
    //     0x8f1724: ldp             fp, lr, [SP], #0x10
    // 0x8f1728: ret
    //     0x8f1728: ret             
    // 0x8f172c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f172c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1730: b               #0x8f170c
  }
}

// class id: 1963, size: 0x38, field offset: 0x30
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin extends _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin
     with RtlPageViewMixin {

  _ _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin(/* No info */) {
    // ** addr: 0x80f880, size: 0xe4
    // 0x80f880: EnterFrame
    //     0x80f880: stp             fp, lr, [SP, #-0x10]!
    //     0x80f884: mov             fp, SP
    // 0x80f888: AllocStack(0x10)
    //     0x80f888: sub             SP, SP, #0x10
    // 0x80f88c: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin this /* r1 => r1, fp-0x8 */)
    //     0x80f88c: stur            x1, [fp, #-8]
    // 0x80f890: CheckStackOverflow
    //     0x80f890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f894: cmp             SP, x16
    //     0x80f898: b.ls            #0x80f95c
    // 0x80f89c: r0 = PageController()
    //     0x80f89c: bl              #0x80f964  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x80f8a0: stur            x0, [fp, #-0x10]
    // 0x80f8a4: StoreField: r0->field_3f = rZR
    //     0x80f8a4: stur            xzr, [x0, #0x3f]
    // 0x80f8a8: r1 = true
    //     0x80f8a8: add             x1, NULL, #0x20  ; true
    // 0x80f8ac: StoreField: r0->field_47 = r1
    //     0x80f8ac: stur            w1, [x0, #0x47]
    // 0x80f8b0: d0 = 1.000000
    //     0x80f8b0: fmov            d0, #1.00000000
    // 0x80f8b4: StoreField: r0->field_4b = d0
    //     0x80f8b4: stur            d0, [x0, #0x4b]
    // 0x80f8b8: mov             x1, x0
    // 0x80f8bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80f8bc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80f8c0: r0 = ScrollController()
    //     0x80f8c0: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x80f8c4: ldur            x0, [fp, #-0x10]
    // 0x80f8c8: ldur            x2, [fp, #-8]
    // 0x80f8cc: StoreField: r2->field_2f = r0
    //     0x80f8cc: stur            w0, [x2, #0x2f]
    //     0x80f8d0: ldurb           w16, [x2, #-1]
    //     0x80f8d4: ldurb           w17, [x0, #-1]
    //     0x80f8d8: and             x16, x17, x16, lsr #2
    //     0x80f8dc: tst             x16, HEAP, lsr #32
    //     0x80f8e0: b.eq            #0x80f8e8
    //     0x80f8e4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80f8e8: r1 = 1
    //     0x80f8e8: movz            x1, #0x1
    // 0x80f8ec: r0 = IntExtension.obs()
    //     0x80f8ec: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80f8f0: ldur            x3, [fp, #-8]
    // 0x80f8f4: StoreField: r3->field_33 = r0
    //     0x80f8f4: stur            w0, [x3, #0x33]
    //     0x80f8f8: ldurb           w16, [x3, #-1]
    //     0x80f8fc: ldurb           w17, [x0, #-1]
    //     0x80f900: and             x16, x17, x16, lsr #2
    //     0x80f904: tst             x16, HEAP, lsr #32
    //     0x80f908: b.eq            #0x80f910
    //     0x80f90c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80f910: r0 = Sentinel
    //     0x80f910: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80f914: StoreField: r3->field_23 = r0
    //     0x80f914: stur            w0, [x3, #0x23]
    // 0x80f918: r1 = <GlobalKey<State<StatefulWidget>>>
    //     0x80f918: add             x1, PP, #0x32, lsl #12  ; [pp+0x323b0] TypeArguments: <GlobalKey<State<StatefulWidget>>>
    //     0x80f91c: ldr             x1, [x1, #0x3b0]
    // 0x80f920: r2 = 0
    //     0x80f920: movz            x2, #0
    // 0x80f924: r0 = _GrowableList()
    //     0x80f924: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80f928: ldur            x1, [fp, #-8]
    // 0x80f92c: StoreField: r1->field_1f = r0
    //     0x80f92c: stur            w0, [x1, #0x1f]
    //     0x80f930: ldurb           w16, [x1, #-1]
    //     0x80f934: ldurb           w17, [x0, #-1]
    //     0x80f938: and             x16, x17, x16, lsr #2
    //     0x80f93c: tst             x16, HEAP, lsr #32
    //     0x80f940: b.eq            #0x80f948
    //     0x80f944: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f948: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80f948: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80f94c: r0 = Null
    //     0x80f94c: mov             x0, NULL
    // 0x80f950: LeaveFrame
    //     0x80f950: mov             SP, fp
    //     0x80f954: ldp             fp, lr, [SP], #0x10
    // 0x80f958: ret
    //     0x80f958: ret             
    // 0x80f95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f960: b               #0x80f89c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f0e64, size: 0x6c
    // 0x8f0e64: EnterFrame
    //     0x8f0e64: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0e68: mov             fp, SP
    // 0x8f0e6c: AllocStack(0x10)
    //     0x8f0e6c: sub             SP, SP, #0x10
    // 0x8f0e70: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin this /* r1 => r0, fp-0x8 */)
    //     0x8f0e70: mov             x0, x1
    //     0x8f0e74: stur            x1, [fp, #-8]
    // 0x8f0e78: CheckStackOverflow
    //     0x8f0e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0e7c: cmp             SP, x16
    //     0x8f0e80: b.ls            #0x8f0ec8
    // 0x8f0e84: mov             x1, x0
    // 0x8f0e88: r0 = onInit()
    //     0x8f0e88: bl              #0x8f0ed0  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin::onInit
    // 0x8f0e8c: ldur            x2, [fp, #-8]
    // 0x8f0e90: LoadField: r0 = r2->field_33
    //     0x8f0e90: ldur            w0, [x2, #0x33]
    // 0x8f0e94: DecompressPointer r0
    //     0x8f0e94: add             x0, x0, HEAP, lsl #32
    // 0x8f0e98: stur            x0, [fp, #-0x10]
    // 0x8f0e9c: r1 = Function 'onPageViewChanged':.
    //     0x8f0e9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40638] AnonymousClosure: (0x8f0f90), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onPageViewChanged (0x8f0fcc)
    //     0x8f0ea0: ldr             x1, [x1, #0x638]
    // 0x8f0ea4: r0 = AllocateClosure()
    //     0x8f0ea4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0ea8: ldur            x1, [fp, #-0x10]
    // 0x8f0eac: mov             x2, x0
    // 0x8f0eb0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f0eb0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f0eb4: r0 = listen()
    //     0x8f0eb4: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8f0eb8: r0 = Null
    //     0x8f0eb8: mov             x0, NULL
    // 0x8f0ebc: LeaveFrame
    //     0x8f0ebc: mov             SP, fp
    //     0x8f0ec0: ldp             fp, lr, [SP], #0x10
    // 0x8f0ec4: ret
    //     0x8f0ec4: ret             
    // 0x8f0ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0ec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0ecc: b               #0x8f0e84
  }
  get _ currentPageView(/* No info */) {
    // ** addr: 0x8f10fc, size: 0x48
    // 0x8f10fc: EnterFrame
    //     0x8f10fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1100: mov             fp, SP
    // 0x8f1104: CheckStackOverflow
    //     0x8f1104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1108: cmp             SP, x16
    //     0x8f110c: b.ls            #0x8f113c
    // 0x8f1110: LoadField: r0 = r1->field_33
    //     0x8f1110: ldur            w0, [x1, #0x33]
    // 0x8f1114: DecompressPointer r0
    //     0x8f1114: add             x0, x0, HEAP, lsl #32
    // 0x8f1118: mov             x1, x0
    // 0x8f111c: r0 = value()
    //     0x8f111c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f1120: r1 = LoadInt32Instr(r0)
    //     0x8f1120: sbfx            x1, x0, #1, #0x1f
    //     0x8f1124: tbz             w0, #0, #0x8f112c
    //     0x8f1128: ldur            x1, [x0, #7]
    // 0x8f112c: mov             x0, x1
    // 0x8f1130: LeaveFrame
    //     0x8f1130: mov             SP, fp
    //     0x8f1134: ldp             fp, lr, [SP], #0x10
    // 0x8f1138: ret
    //     0x8f1138: ret             
    // 0x8f113c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f113c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1140: b               #0x8f1110
  }
  _ onPageViewChanged(/* No info */) {
    // ** addr: 0x8f1144, size: 0x78
    // 0x8f1144: EnterFrame
    //     0x8f1144: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1148: mov             fp, SP
    // 0x8f114c: AllocStack(0x10)
    //     0x8f114c: sub             SP, SP, #0x10
    // 0x8f1150: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8f1150: stur            x2, [fp, #-0x10]
    // 0x8f1154: CheckStackOverflow
    //     0x8f1154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1158: cmp             SP, x16
    //     0x8f115c: b.ls            #0x8f11b4
    // 0x8f1160: LoadField: r0 = r1->field_2f
    //     0x8f1160: ldur            w0, [x1, #0x2f]
    // 0x8f1164: DecompressPointer r0
    //     0x8f1164: add             x0, x0, HEAP, lsl #32
    // 0x8f1168: stur            x0, [fp, #-8]
    // 0x8f116c: r0 = Duration()
    //     0x8f116c: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x8f1170: mov             x1, x0
    // 0x8f1174: r0 = 400000
    //     0x8f1174: movz            x0, #0x1a80
    //     0x8f1178: movk            x0, #0x6, lsl #16
    // 0x8f117c: StoreField: r1->field_7 = r0
    //     0x8f117c: stur            x0, [x1, #7]
    // 0x8f1180: ldur            x0, [fp, #-0x10]
    // 0x8f1184: r2 = LoadInt32Instr(r0)
    //     0x8f1184: sbfx            x2, x0, #1, #0x1f
    //     0x8f1188: tbz             w0, #0, #0x8f1190
    //     0x8f118c: ldur            x2, [x0, #7]
    // 0x8f1190: mov             x5, x1
    // 0x8f1194: ldur            x1, [fp, #-8]
    // 0x8f1198: r3 = Instance_Cubic
    //     0x8f1198: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb00] Obj!Cubic@e14e01
    //     0x8f119c: ldr             x3, [x3, #0xb00]
    // 0x8f11a0: r0 = animateToPage()
    //     0x8f11a0: bl              #0x8f11bc  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0x8f11a4: r0 = Null
    //     0x8f11a4: mov             x0, NULL
    // 0x8f11a8: LeaveFrame
    //     0x8f11a8: mov             SP, fp
    //     0x8f11ac: ldp             fp, lr, [SP], #0x10
    // 0x8f11b0: ret
    //     0x8f11b0: ret             
    // 0x8f11b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f11b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f11b8: b               #0x8f1160
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9272b0, size: 0x3c
    // 0x9272b0: EnterFrame
    //     0x9272b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9272b4: mov             fp, SP
    // 0x9272b8: CheckStackOverflow
    //     0x9272b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9272bc: cmp             SP, x16
    //     0x9272c0: b.ls            #0x9272e4
    // 0x9272c4: LoadField: r0 = r1->field_2f
    //     0x9272c4: ldur            w0, [x1, #0x2f]
    // 0x9272c8: DecompressPointer r0
    //     0x9272c8: add             x0, x0, HEAP, lsl #32
    // 0x9272cc: mov             x1, x0
    // 0x9272d0: r0 = dispose()
    //     0x9272d0: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0x9272d4: r0 = Null
    //     0x9272d4: mov             x0, NULL
    // 0x9272d8: LeaveFrame
    //     0x9272d8: mov             SP, fp
    //     0x9272dc: ldp             fp, lr, [SP], #0x10
    // 0x9272e0: ret
    //     0x9272e0: ret             
    // 0x9272e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9272e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9272e8: b               #0x9272c4
  }
  set _ currentPageView=(/* No info */) {
    // ** addr: 0xad8ccc, size: 0x60
    // 0xad8ccc: EnterFrame
    //     0xad8ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xad8cd0: mov             fp, SP
    // 0xad8cd4: AllocStack(0x8)
    //     0xad8cd4: sub             SP, SP, #8
    // 0xad8cd8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xad8cd8: mov             x3, x2
    //     0xad8cdc: stur            x2, [fp, #-8]
    // 0xad8ce0: CheckStackOverflow
    //     0xad8ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8ce4: cmp             SP, x16
    //     0xad8ce8: b.ls            #0xad8d24
    // 0xad8cec: LoadField: r2 = r1->field_33
    //     0xad8cec: ldur            w2, [x1, #0x33]
    // 0xad8cf0: DecompressPointer r2
    //     0xad8cf0: add             x2, x2, HEAP, lsl #32
    // 0xad8cf4: r0 = BoxInt64Instr(r3)
    //     0xad8cf4: sbfiz           x0, x3, #1, #0x1f
    //     0xad8cf8: cmp             x3, x0, asr #1
    //     0xad8cfc: b.eq            #0xad8d08
    //     0xad8d00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad8d04: stur            x3, [x0, #7]
    // 0xad8d08: mov             x1, x2
    // 0xad8d0c: mov             x2, x0
    // 0xad8d10: r0 = value=()
    //     0xad8d10: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad8d14: ldur            x0, [fp, #-8]
    // 0xad8d18: LeaveFrame
    //     0xad8d18: mov             SP, fp
    //     0xad8d1c: ldp             fp, lr, [SP], #0x10
    // 0xad8d20: ret
    //     0xad8d20: ret             
    // 0xad8d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8d28: b               #0xad8cec
  }
  [closure] void prevPageView(dynamic) {
    // ** addr: 0xad8e88, size: 0x38
    // 0xad8e88: EnterFrame
    //     0xad8e88: stp             fp, lr, [SP, #-0x10]!
    //     0xad8e8c: mov             fp, SP
    // 0xad8e90: ldr             x0, [fp, #0x10]
    // 0xad8e94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad8e94: ldur            w1, [x0, #0x17]
    // 0xad8e98: DecompressPointer r1
    //     0xad8e98: add             x1, x1, HEAP, lsl #32
    // 0xad8e9c: CheckStackOverflow
    //     0xad8e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8ea0: cmp             SP, x16
    //     0xad8ea4: b.ls            #0xad8eb8
    // 0xad8ea8: r0 = prevPageView()
    //     0xad8ea8: bl              #0xad8ec0  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::prevPageView
    // 0xad8eac: LeaveFrame
    //     0xad8eac: mov             SP, fp
    //     0xad8eb0: ldp             fp, lr, [SP], #0x10
    // 0xad8eb4: ret
    //     0xad8eb4: ret             
    // 0xad8eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8ebc: b               #0xad8ea8
  }
  _ prevPageView(/* No info */) {
    // ** addr: 0xad8ec0, size: 0x90
    // 0xad8ec0: EnterFrame
    //     0xad8ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xad8ec4: mov             fp, SP
    // 0xad8ec8: AllocStack(0x8)
    //     0xad8ec8: sub             SP, SP, #8
    // 0xad8ecc: CheckStackOverflow
    //     0xad8ecc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8ed0: cmp             SP, x16
    //     0xad8ed4: b.ls            #0xad8f48
    // 0xad8ed8: LoadField: r0 = r1->field_33
    //     0xad8ed8: ldur            w0, [x1, #0x33]
    // 0xad8edc: DecompressPointer r0
    //     0xad8edc: add             x0, x0, HEAP, lsl #32
    // 0xad8ee0: mov             x1, x0
    // 0xad8ee4: stur            x0, [fp, #-8]
    // 0xad8ee8: r0 = value()
    //     0xad8ee8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad8eec: r1 = LoadInt32Instr(r0)
    //     0xad8eec: sbfx            x1, x0, #1, #0x1f
    //     0xad8ef0: tbz             w0, #0, #0xad8ef8
    //     0xad8ef4: ldur            x1, [x0, #7]
    // 0xad8ef8: cmp             x1, #1
    // 0xad8efc: b.le            #0xad8f38
    // 0xad8f00: ldur            x1, [fp, #-8]
    // 0xad8f04: r0 = value()
    //     0xad8f04: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad8f08: r1 = LoadInt32Instr(r0)
    //     0xad8f08: sbfx            x1, x0, #1, #0x1f
    //     0xad8f0c: tbz             w0, #0, #0xad8f14
    //     0xad8f10: ldur            x1, [x0, #7]
    // 0xad8f14: sub             x2, x1, #1
    // 0xad8f18: r0 = BoxInt64Instr(r2)
    //     0xad8f18: sbfiz           x0, x2, #1, #0x1f
    //     0xad8f1c: cmp             x2, x0, asr #1
    //     0xad8f20: b.eq            #0xad8f2c
    //     0xad8f24: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad8f28: stur            x2, [x0, #7]
    // 0xad8f2c: ldur            x1, [fp, #-8]
    // 0xad8f30: mov             x2, x0
    // 0xad8f34: r0 = value=()
    //     0xad8f34: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad8f38: r0 = Null
    //     0xad8f38: mov             x0, NULL
    // 0xad8f3c: LeaveFrame
    //     0xad8f3c: mov             SP, fp
    //     0xad8f40: ldp             fp, lr, [SP], #0x10
    // 0xad8f44: ret
    //     0xad8f44: ret             
    // 0xad8f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8f4c: b               #0xad8ed8
  }
  [closure] void nextPageView(dynamic) {
    // ** addr: 0xad8f50, size: 0x38
    // 0xad8f50: EnterFrame
    //     0xad8f50: stp             fp, lr, [SP, #-0x10]!
    //     0xad8f54: mov             fp, SP
    // 0xad8f58: ldr             x0, [fp, #0x10]
    // 0xad8f5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad8f5c: ldur            w1, [x0, #0x17]
    // 0xad8f60: DecompressPointer r1
    //     0xad8f60: add             x1, x1, HEAP, lsl #32
    // 0xad8f64: CheckStackOverflow
    //     0xad8f64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8f68: cmp             SP, x16
    //     0xad8f6c: b.ls            #0xad8f80
    // 0xad8f70: r0 = nextPageView()
    //     0xad8f70: bl              #0xad8f88  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::nextPageView
    // 0xad8f74: LeaveFrame
    //     0xad8f74: mov             SP, fp
    //     0xad8f78: ldp             fp, lr, [SP], #0x10
    // 0xad8f7c: ret
    //     0xad8f7c: ret             
    // 0xad8f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8f80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8f84: b               #0xad8f70
  }
  _ nextPageView(/* No info */) {
    // ** addr: 0xad8f88, size: 0xac
    // 0xad8f88: EnterFrame
    //     0xad8f88: stp             fp, lr, [SP, #-0x10]!
    //     0xad8f8c: mov             fp, SP
    // 0xad8f90: AllocStack(0x10)
    //     0xad8f90: sub             SP, SP, #0x10
    // 0xad8f94: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin this /* r1 => r0, fp-0x10 */)
    //     0xad8f94: mov             x0, x1
    //     0xad8f98: stur            x1, [fp, #-0x10]
    // 0xad8f9c: CheckStackOverflow
    //     0xad8f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8fa0: cmp             SP, x16
    //     0xad8fa4: b.ls            #0xad902c
    // 0xad8fa8: LoadField: r2 = r0->field_33
    //     0xad8fa8: ldur            w2, [x0, #0x33]
    // 0xad8fac: DecompressPointer r2
    //     0xad8fac: add             x2, x2, HEAP, lsl #32
    // 0xad8fb0: mov             x1, x2
    // 0xad8fb4: stur            x2, [fp, #-8]
    // 0xad8fb8: r0 = value()
    //     0xad8fb8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad8fbc: ldur            x1, [fp, #-0x10]
    // 0xad8fc0: stur            x0, [fp, #-0x10]
    // 0xad8fc4: r0 = maxPageView()
    //     0xad8fc4: bl              #0xad8b3c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::maxPageView
    // 0xad8fc8: mov             x1, x0
    // 0xad8fcc: ldur            x0, [fp, #-0x10]
    // 0xad8fd0: r2 = LoadInt32Instr(r0)
    //     0xad8fd0: sbfx            x2, x0, #1, #0x1f
    //     0xad8fd4: tbz             w0, #0, #0xad8fdc
    //     0xad8fd8: ldur            x2, [x0, #7]
    // 0xad8fdc: cmp             x2, x1
    // 0xad8fe0: b.ge            #0xad901c
    // 0xad8fe4: ldur            x1, [fp, #-8]
    // 0xad8fe8: r0 = value()
    //     0xad8fe8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad8fec: r1 = LoadInt32Instr(r0)
    //     0xad8fec: sbfx            x1, x0, #1, #0x1f
    //     0xad8ff0: tbz             w0, #0, #0xad8ff8
    //     0xad8ff4: ldur            x1, [x0, #7]
    // 0xad8ff8: add             x2, x1, #1
    // 0xad8ffc: r0 = BoxInt64Instr(r2)
    //     0xad8ffc: sbfiz           x0, x2, #1, #0x1f
    //     0xad9000: cmp             x2, x0, asr #1
    //     0xad9004: b.eq            #0xad9010
    //     0xad9008: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad900c: stur            x2, [x0, #7]
    // 0xad9010: ldur            x1, [fp, #-8]
    // 0xad9014: mov             x2, x0
    // 0xad9018: r0 = value=()
    //     0xad9018: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad901c: r0 = Null
    //     0xad901c: mov             x0, NULL
    // 0xad9020: LeaveFrame
    //     0xad9020: mov             SP, fp
    //     0xad9024: ldp             fp, lr, [SP], #0x10
    // 0xad9028: ret
    //     0xad9028: ret             
    // 0xad902c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad902c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9030: b               #0xad8fa8
  }
}

// class id: 1964, size: 0x38, field offset: 0x38
//   transformed mixin,
abstract class _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin extends _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin
     with WakelockMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8f0db0, size: 0xb4
    // 0x8f0db0: EnterFrame
    //     0x8f0db0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0db4: mov             fp, SP
    // 0x8f0db8: AllocStack(0x10)
    //     0x8f0db8: sub             SP, SP, #0x10
    // 0x8f0dbc: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin this /* r1 => r1, fp-0x8 */)
    //     0x8f0dbc: stur            x1, [fp, #-8]
    // 0x8f0dc0: CheckStackOverflow
    //     0x8f0dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0dc4: cmp             SP, x16
    //     0x8f0dc8: b.ls            #0x8f0e5c
    // 0x8f0dcc: r1 = 1
    //     0x8f0dcc: movz            x1, #0x1
    // 0x8f0dd0: r0 = AllocateContext()
    //     0x8f0dd0: bl              #0xec126c  ; AllocateContextStub
    // 0x8f0dd4: mov             x2, x0
    // 0x8f0dd8: ldur            x0, [fp, #-8]
    // 0x8f0ddc: stur            x2, [fp, #-0x10]
    // 0x8f0de0: StoreField: r2->field_f = r0
    //     0x8f0de0: stur            w0, [x2, #0xf]
    // 0x8f0de4: mov             x1, x0
    // 0x8f0de8: r0 = onInit()
    //     0x8f0de8: bl              #0x8f0e64  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::onInit
    // 0x8f0dec: ldur            x0, [fp, #-8]
    // 0x8f0df0: LoadField: r1 = r0->field_4b
    //     0x8f0df0: ldur            w1, [x0, #0x4b]
    // 0x8f0df4: DecompressPointer r1
    //     0x8f0df4: add             x1, x1, HEAP, lsl #32
    // 0x8f0df8: r0 = _readingPref()
    //     0x8f0df8: bl              #0x8add5c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_readingPref
    // 0x8f0dfc: mov             x1, x0
    // 0x8f0e00: r0 = val()
    //     0x8f0e00: bl              #0x7ec97c  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val
    // 0x8f0e04: mov             x2, x0
    // 0x8f0e08: r1 = Null
    //     0x8f0e08: mov             x1, NULL
    // 0x8f0e0c: r0 = ReadingPref.fromMap()
    //     0x8f0e0c: bl              #0x8adb1c  ; [package:nuonline/app/data/models/reading_pref.dart] ReadingPref::ReadingPref.fromMap
    // 0x8f0e10: LoadField: r1 = r0->field_1f
    //     0x8f0e10: ldur            w1, [x0, #0x1f]
    // 0x8f0e14: DecompressPointer r1
    //     0x8f0e14: add             x1, x1, HEAP, lsl #32
    // 0x8f0e18: r0 = toggle()
    //     0x8f0e18: bl              #0x8ad644  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::toggle
    // 0x8f0e1c: r1 = Null
    //     0x8f0e1c: mov             x1, NULL
    // 0x8f0e20: r0 = GetStorage()
    //     0x8f0e20: bl              #0x7ecca0  ; [package:get_storage/src/storage_impl.dart] GetStorage::GetStorage
    // 0x8f0e24: ldur            x2, [fp, #-0x10]
    // 0x8f0e28: r1 = Function '<anonymous closure>':.
    //     0x8f0e28: add             x1, PP, #0x40, lsl #12  ; [pp+0x405d8] AnonymousClosure: (0x8f14bc), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin::onInit (0x8f0db0)
    //     0x8f0e2c: ldr             x1, [x1, #0x5d8]
    // 0x8f0e30: stur            x0, [fp, #-8]
    // 0x8f0e34: r0 = AllocateClosure()
    //     0x8f0e34: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0e38: ldur            x1, [fp, #-8]
    // 0x8f0e3c: mov             x3, x0
    // 0x8f0e40: r2 = "readingPref"
    //     0x8f0e40: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c400] "readingPref"
    //     0x8f0e44: ldr             x2, [x2, #0x400]
    // 0x8f0e48: r0 = listenKey()
    //     0x8f0e48: bl              #0x8ad200  ; [package:get_storage/src/storage_impl.dart] GetStorage::listenKey
    // 0x8f0e4c: r0 = Null
    //     0x8f0e4c: mov             x0, NULL
    // 0x8f0e50: LeaveFrame
    //     0x8f0e50: mov             SP, fp
    //     0x8f0e54: ldp             fp, lr, [SP], #0x10
    // 0x8f0e58: ret
    //     0x8f0e58: ret             
    // 0x8f0e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0e60: b               #0x8f0dcc
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8f14bc, size: 0x6c
    // 0x8f14bc: EnterFrame
    //     0x8f14bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f14c0: mov             fp, SP
    // 0x8f14c4: ldr             x0, [fp, #0x18]
    // 0x8f14c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f14c8: ldur            w1, [x0, #0x17]
    // 0x8f14cc: DecompressPointer r1
    //     0x8f14cc: add             x1, x1, HEAP, lsl #32
    // 0x8f14d0: CheckStackOverflow
    //     0x8f14d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f14d4: cmp             SP, x16
    //     0x8f14d8: b.ls            #0x8f1520
    // 0x8f14dc: LoadField: r0 = r1->field_f
    //     0x8f14dc: ldur            w0, [x1, #0xf]
    // 0x8f14e0: DecompressPointer r0
    //     0x8f14e0: add             x0, x0, HEAP, lsl #32
    // 0x8f14e4: LoadField: r1 = r0->field_4b
    //     0x8f14e4: ldur            w1, [x0, #0x4b]
    // 0x8f14e8: DecompressPointer r1
    //     0x8f14e8: add             x1, x1, HEAP, lsl #32
    // 0x8f14ec: r0 = _readingPref()
    //     0x8f14ec: bl              #0x8add5c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_readingPref
    // 0x8f14f0: mov             x1, x0
    // 0x8f14f4: r0 = val()
    //     0x8f14f4: bl              #0x7ec97c  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val
    // 0x8f14f8: mov             x2, x0
    // 0x8f14fc: r1 = Null
    //     0x8f14fc: mov             x1, NULL
    // 0x8f1500: r0 = ReadingPref.fromMap()
    //     0x8f1500: bl              #0x8adb1c  ; [package:nuonline/app/data/models/reading_pref.dart] ReadingPref::ReadingPref.fromMap
    // 0x8f1504: LoadField: r1 = r0->field_1f
    //     0x8f1504: ldur            w1, [x0, #0x1f]
    // 0x8f1508: DecompressPointer r1
    //     0x8f1508: add             x1, x1, HEAP, lsl #32
    // 0x8f150c: r0 = toggle()
    //     0x8f150c: bl              #0x8ad644  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::toggle
    // 0x8f1510: r0 = Null
    //     0x8f1510: mov             x0, NULL
    // 0x8f1514: LeaveFrame
    //     0x8f1514: mov             SP, fp
    //     0x8f1518: ldp             fp, lr, [SP], #0x10
    // 0x8f151c: ret
    //     0x8f151c: ret             
    // 0x8f1520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1524: b               #0x8f14dc
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927270, size: 0x40
    // 0x927270: EnterFrame
    //     0x927270: stp             fp, lr, [SP, #-0x10]!
    //     0x927274: mov             fp, SP
    // 0x927278: AllocStack(0x8)
    //     0x927278: sub             SP, SP, #8
    // 0x92727c: SetupParameters(_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin this /* r1 => r1, fp-0x8 */)
    //     0x92727c: stur            x1, [fp, #-8]
    // 0x927280: CheckStackOverflow
    //     0x927280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927284: cmp             SP, x16
    //     0x927288: b.ls            #0x9272a8
    // 0x92728c: r0 = disable()
    //     0x92728c: bl              #0x9270f0  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::disable
    // 0x927290: ldur            x1, [fp, #-8]
    // 0x927294: r0 = onClose()
    //     0x927294: bl              #0x9272b0  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::onClose
    // 0x927298: r0 = Null
    //     0x927298: mov             x0, NULL
    // 0x92729c: LeaveFrame
    //     0x92729c: mov             SP, fp
    //     0x9272a0: ldp             fp, lr, [SP], #0x10
    // 0x9272a4: ret
    //     0x9272a4: ret             
    // 0x9272a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9272a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9272ac: b               #0x92728c
  }
}

// class id: 1965, size: 0x5c, field offset: 0x38
class DoaDetailController extends _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin {

  _ DoaDetailController(/* No info */) {
    // ** addr: 0x80f5e8, size: 0x144
    // 0x80f5e8: EnterFrame
    //     0x80f5e8: stp             fp, lr, [SP, #-0x10]!
    //     0x80f5ec: mov             fp, SP
    // 0x80f5f0: AllocStack(0x30)
    //     0x80f5f0: sub             SP, SP, #0x30
    // 0x80f5f4: SetupParameters(DoaDetailController this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r0, fp-0x30 */)
    //     0x80f5f4: mov             x4, x2
    //     0x80f5f8: stur            x2, [fp, #-0x10]
    //     0x80f5fc: mov             x2, x5
    //     0x80f600: stur            x5, [fp, #-0x20]
    //     0x80f604: mov             x5, x1
    //     0x80f608: mov             x0, x7
    //     0x80f60c: stur            x1, [fp, #-8]
    //     0x80f610: stur            x3, [fp, #-0x18]
    //     0x80f614: stur            x6, [fp, #-0x28]
    //     0x80f618: stur            x7, [fp, #-0x30]
    // 0x80f61c: CheckStackOverflow
    //     0x80f61c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f620: cmp             SP, x16
    //     0x80f624: b.ls            #0x80f724
    // 0x80f628: r1 = ""
    //     0x80f628: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x80f62c: r0 = StringExtension.obs()
    //     0x80f62c: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x80f630: ldur            x2, [fp, #-8]
    // 0x80f634: StoreField: r2->field_4f = r0
    //     0x80f634: stur            w0, [x2, #0x4f]
    //     0x80f638: ldurb           w16, [x2, #-1]
    //     0x80f63c: ldurb           w17, [x0, #-1]
    //     0x80f640: and             x16, x17, x16, lsr #2
    //     0x80f644: tst             x16, HEAP, lsr #32
    //     0x80f648: b.eq            #0x80f650
    //     0x80f64c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80f650: r1 = false
    //     0x80f650: add             x1, NULL, #0x30  ; false
    // 0x80f654: r0 = BoolExtension.obs()
    //     0x80f654: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80f658: ldur            x2, [fp, #-8]
    // 0x80f65c: StoreField: r2->field_53 = r0
    //     0x80f65c: stur            w0, [x2, #0x53]
    //     0x80f660: ldurb           w16, [x2, #-1]
    //     0x80f664: ldurb           w17, [x0, #-1]
    //     0x80f668: and             x16, x17, x16, lsr #2
    //     0x80f66c: tst             x16, HEAP, lsr #32
    //     0x80f670: b.eq            #0x80f678
    //     0x80f674: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80f678: r1 = true
    //     0x80f678: add             x1, NULL, #0x20  ; true
    // 0x80f67c: r0 = BoolExtension.obs()
    //     0x80f67c: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80f680: ldur            x1, [fp, #-8]
    // 0x80f684: StoreField: r1->field_57 = r0
    //     0x80f684: stur            w0, [x1, #0x57]
    //     0x80f688: ldurb           w16, [x1, #-1]
    //     0x80f68c: ldurb           w17, [x0, #-1]
    //     0x80f690: and             x16, x17, x16, lsr #2
    //     0x80f694: tst             x16, HEAP, lsr #32
    //     0x80f698: b.eq            #0x80f6a0
    //     0x80f69c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f6a0: ldur            x0, [fp, #-0x10]
    // 0x80f6a4: StoreField: r1->field_37 = r0
    //     0x80f6a4: stur            x0, [x1, #0x37]
    // 0x80f6a8: ldur            x0, [fp, #-0x18]
    // 0x80f6ac: StoreField: r1->field_43 = r0
    //     0x80f6ac: stur            w0, [x1, #0x43]
    //     0x80f6b0: ldurb           w16, [x1, #-1]
    //     0x80f6b4: ldurb           w17, [x0, #-1]
    //     0x80f6b8: and             x16, x17, x16, lsr #2
    //     0x80f6bc: tst             x16, HEAP, lsr #32
    //     0x80f6c0: b.eq            #0x80f6c8
    //     0x80f6c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f6c8: ldur            x0, [fp, #-0x20]
    // 0x80f6cc: StoreField: r1->field_47 = r0
    //     0x80f6cc: stur            w0, [x1, #0x47]
    //     0x80f6d0: ldurb           w16, [x1, #-1]
    //     0x80f6d4: ldurb           w17, [x0, #-1]
    //     0x80f6d8: and             x16, x17, x16, lsr #2
    //     0x80f6dc: tst             x16, HEAP, lsr #32
    //     0x80f6e0: b.eq            #0x80f6e8
    //     0x80f6e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f6e8: ldur            x0, [fp, #-0x30]
    // 0x80f6ec: StoreField: r1->field_4b = r0
    //     0x80f6ec: stur            w0, [x1, #0x4b]
    //     0x80f6f0: ldurb           w16, [x1, #-1]
    //     0x80f6f4: ldurb           w17, [x0, #-1]
    //     0x80f6f8: and             x16, x17, x16, lsr #2
    //     0x80f6fc: tst             x16, HEAP, lsr #32
    //     0x80f700: b.eq            #0x80f708
    //     0x80f704: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80f708: ldur            x0, [fp, #-0x28]
    // 0x80f70c: StoreField: r1->field_3f = r0
    //     0x80f70c: stur            w0, [x1, #0x3f]
    // 0x80f710: r0 = _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin()
    //     0x80f710: bl              #0x80f880  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::_DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin
    // 0x80f714: r0 = Null
    //     0x80f714: mov             x0, NULL
    // 0x80f718: LeaveFrame
    //     0x80f718: mov             SP, fp
    //     0x80f71c: ldp             fp, lr, [SP], #0x10
    // 0x80f720: ret
    //     0x80f720: ret             
    // 0x80f724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f724: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f728: b               #0x80f628
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f0ac8, size: 0x90
    // 0x8f0ac8: EnterFrame
    //     0x8f0ac8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0acc: mov             fp, SP
    // 0x8f0ad0: AllocStack(0x28)
    //     0x8f0ad0: sub             SP, SP, #0x28
    // 0x8f0ad4: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x8 */)
    //     0x8f0ad4: stur            x1, [fp, #-8]
    // 0x8f0ad8: CheckStackOverflow
    //     0x8f0ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0adc: cmp             SP, x16
    //     0x8f0ae0: b.ls            #0x8f0b50
    // 0x8f0ae4: r1 = 1
    //     0x8f0ae4: movz            x1, #0x1
    // 0x8f0ae8: r0 = AllocateContext()
    //     0x8f0ae8: bl              #0xec126c  ; AllocateContextStub
    // 0x8f0aec: mov             x2, x0
    // 0x8f0af0: ldur            x0, [fp, #-8]
    // 0x8f0af4: stur            x2, [fp, #-0x10]
    // 0x8f0af8: StoreField: r2->field_f = r0
    //     0x8f0af8: stur            w0, [x2, #0xf]
    // 0x8f0afc: mov             x1, x0
    // 0x8f0b00: r0 = onInit()
    //     0x8f0b00: bl              #0x8f0db0  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin&WakelockMixin::onInit
    // 0x8f0b04: ldur            x1, [fp, #-8]
    // 0x8f0b08: r0 = executeOfflineMode()
    //     0x8f0b08: bl              #0x8f0bfc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin::executeOfflineMode
    // 0x8f0b0c: ldur            x1, [fp, #-8]
    // 0x8f0b10: r0 = lastUpdatedAt()
    //     0x8f0b10: bl              #0x8f0b58  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::lastUpdatedAt
    // 0x8f0b14: ldur            x2, [fp, #-0x10]
    // 0x8f0b18: r1 = Function '<anonymous closure>':.
    //     0x8f0b18: add             x1, PP, #0x40, lsl #12  ; [pp+0x405f8] AnonymousClosure: (0x8f1528), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onInit (0x8f0ac8)
    //     0x8f0b1c: ldr             x1, [x1, #0x5f8]
    // 0x8f0b20: stur            x0, [fp, #-8]
    // 0x8f0b24: r0 = AllocateClosure()
    //     0x8f0b24: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0b28: r16 = <Null?>
    //     0x8f0b28: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x8f0b2c: ldur            lr, [fp, #-8]
    // 0x8f0b30: stp             lr, x16, [SP, #8]
    // 0x8f0b34: str             x0, [SP]
    // 0x8f0b38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f0b38: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f0b3c: r0 = then()
    //     0x8f0b3c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f0b40: r0 = Null
    //     0x8f0b40: mov             x0, NULL
    // 0x8f0b44: LeaveFrame
    //     0x8f0b44: mov             SP, fp
    //     0x8f0b48: ldp             fp, lr, [SP], #0x10
    // 0x8f0b4c: ret
    //     0x8f0b4c: ret             
    // 0x8f0b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0b54: b               #0x8f0ae4
  }
  get _ lastUpdatedAt(/* No info */) async {
    // ** addr: 0x8f0b58, size: 0x80
    // 0x8f0b58: EnterFrame
    //     0x8f0b58: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0b5c: mov             fp, SP
    // 0x8f0b60: AllocStack(0x28)
    //     0x8f0b60: sub             SP, SP, #0x28
    // 0x8f0b64: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */)
    //     0x8f0b64: stur            NULL, [fp, #-8]
    //     0x8f0b68: stur            x1, [fp, #-0x10]
    // 0x8f0b6c: CheckStackOverflow
    //     0x8f0b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0b70: cmp             SP, x16
    //     0x8f0b74: b.ls            #0x8f0bd0
    // 0x8f0b78: InitAsync() -> Future<String?>
    //     0x8f0b78: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0x8f0b7c: bl              #0x661298  ; InitAsyncStub
    // 0x8f0b80: ldur            x0, [fp, #-0x10]
    // 0x8f0b84: LoadField: r1 = r0->field_43
    //     0x8f0b84: ldur            w1, [x0, #0x43]
    // 0x8f0b88: DecompressPointer r1
    //     0x8f0b88: add             x1, x1, HEAP, lsl #32
    // 0x8f0b8c: r0 = LoadClassIdInstr(r1)
    //     0x8f0b8c: ldur            x0, [x1, #-1]
    //     0x8f0b90: ubfx            x0, x0, #0xc, #0x14
    // 0x8f0b94: r0 = GDT[cid_x0 + -0xfd7]()
    //     0x8f0b94: sub             lr, x0, #0xfd7
    //     0x8f0b98: ldr             lr, [x21, lr, lsl #3]
    //     0x8f0b9c: blr             lr
    // 0x8f0ba0: r1 = Function '<anonymous closure>':.
    //     0x8f0ba0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40618] AnonymousClosure: (0x8f0bd8), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::lastUpdatedAt (0x8f0b58)
    //     0x8f0ba4: ldr             x1, [x1, #0x618]
    // 0x8f0ba8: r2 = Null
    //     0x8f0ba8: mov             x2, NULL
    // 0x8f0bac: stur            x0, [fp, #-0x10]
    // 0x8f0bb0: r0 = AllocateClosure()
    //     0x8f0bb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f0bb4: r16 = <String?>
    //     0x8f0bb4: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x8f0bb8: ldur            lr, [fp, #-0x10]
    // 0x8f0bbc: stp             lr, x16, [SP, #8]
    // 0x8f0bc0: str             x0, [SP]
    // 0x8f0bc4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f0bc4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f0bc8: r0 = then()
    //     0x8f0bc8: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f0bcc: r0 = ReturnAsync()
    //     0x8f0bcc: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f0bd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0bd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0bd4: b               #0x8f0b78
  }
  [closure] String? <anonymous closure>(dynamic, Doa?) {
    // ** addr: 0x8f0bd8, size: 0x24
    // 0x8f0bd8: ldr             x1, [SP]
    // 0x8f0bdc: cmp             w1, NULL
    // 0x8f0be0: b.ne            #0x8f0bec
    // 0x8f0be4: r0 = Null
    //     0x8f0be4: mov             x0, NULL
    // 0x8f0be8: b               #0x8f0bf8
    // 0x8f0bec: LoadField: r2 = r1->field_33
    //     0x8f0bec: ldur            w2, [x1, #0x33]
    // 0x8f0bf0: DecompressPointer r2
    //     0x8f0bf0: add             x2, x2, HEAP, lsl #32
    // 0x8f0bf4: mov             x0, x2
    // 0x8f0bf8: ret
    //     0x8f0bf8: ret             
  }
  [closure] void onPageViewChanged(dynamic, int) {
    // ** addr: 0x8f0f90, size: 0x3c
    // 0x8f0f90: EnterFrame
    //     0x8f0f90: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0f94: mov             fp, SP
    // 0x8f0f98: ldr             x0, [fp, #0x18]
    // 0x8f0f9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0f9c: ldur            w1, [x0, #0x17]
    // 0x8f0fa0: DecompressPointer r1
    //     0x8f0fa0: add             x1, x1, HEAP, lsl #32
    // 0x8f0fa4: CheckStackOverflow
    //     0x8f0fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0fa8: cmp             SP, x16
    //     0x8f0fac: b.ls            #0x8f0fc4
    // 0x8f0fb0: ldr             x2, [fp, #0x10]
    // 0x8f0fb4: r0 = onPageViewChanged()
    //     0x8f0fb4: bl              #0x8f0fcc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onPageViewChanged
    // 0x8f0fb8: LeaveFrame
    //     0x8f0fb8: mov             SP, fp
    //     0x8f0fbc: ldp             fp, lr, [SP], #0x10
    // 0x8f0fc0: ret
    //     0x8f0fc0: ret             
    // 0x8f0fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0fc8: b               #0x8f0fb0
  }
  _ onPageViewChanged(/* No info */) {
    // ** addr: 0x8f0fcc, size: 0x130
    // 0x8f0fcc: EnterFrame
    //     0x8f0fcc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0fd0: mov             fp, SP
    // 0x8f0fd4: AllocStack(0x38)
    //     0x8f0fd4: sub             SP, SP, #0x38
    // 0x8f0fd8: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x8 */)
    //     0x8f0fd8: stur            x1, [fp, #-8]
    // 0x8f0fdc: CheckStackOverflow
    //     0x8f0fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0fe0: cmp             SP, x16
    //     0x8f0fe4: b.ls            #0x8f10f0
    // 0x8f0fe8: r1 = 1
    //     0x8f0fe8: movz            x1, #0x1
    // 0x8f0fec: r0 = AllocateContext()
    //     0x8f0fec: bl              #0xec126c  ; AllocateContextStub
    // 0x8f0ff0: mov             x3, x0
    // 0x8f0ff4: ldur            x0, [fp, #-8]
    // 0x8f0ff8: stur            x3, [fp, #-0x18]
    // 0x8f0ffc: StoreField: r3->field_f = r0
    //     0x8f0ffc: stur            w0, [x3, #0xf]
    // 0x8f1000: LoadField: r4 = r0->field_57
    //     0x8f1000: ldur            w4, [x0, #0x57]
    // 0x8f1004: DecompressPointer r4
    //     0x8f1004: add             x4, x4, HEAP, lsl #32
    // 0x8f1008: mov             x1, x4
    // 0x8f100c: stur            x4, [fp, #-0x10]
    // 0x8f1010: r2 = true
    //     0x8f1010: add             x2, NULL, #0x20  ; true
    // 0x8f1014: r0 = value=()
    //     0x8f1014: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f1018: ldur            x1, [fp, #-8]
    // 0x8f101c: r0 = currentItem()
    //     0x8f101c: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0x8f1020: cmp             w0, NULL
    // 0x8f1024: b.eq            #0x8f1090
    // 0x8f1028: ldur            x0, [fp, #-8]
    // 0x8f102c: LoadField: r2 = r0->field_43
    //     0x8f102c: ldur            w2, [x0, #0x43]
    // 0x8f1030: DecompressPointer r2
    //     0x8f1030: add             x2, x2, HEAP, lsl #32
    // 0x8f1034: mov             x1, x0
    // 0x8f1038: stur            x2, [fp, #-0x20]
    // 0x8f103c: r0 = currentItem()
    //     0x8f103c: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0x8f1040: cmp             w0, NULL
    // 0x8f1044: b.eq            #0x8f10f8
    // 0x8f1048: LoadField: r2 = r0->field_7
    //     0x8f1048: ldur            x2, [x0, #7]
    // 0x8f104c: ldur            x1, [fp, #-0x20]
    // 0x8f1050: r0 = LoadClassIdInstr(r1)
    //     0x8f1050: ldur            x0, [x1, #-1]
    //     0x8f1054: ubfx            x0, x0, #0xc, #0x14
    // 0x8f1058: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8f1058: sub             lr, x0, #0xfff
    //     0x8f105c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f1060: blr             lr
    // 0x8f1064: ldur            x2, [fp, #-0x18]
    // 0x8f1068: r1 = Function '<anonymous closure>':.
    //     0x8f1068: add             x1, PP, #0x40, lsl #12  ; [pp+0x40640] AnonymousClosure: (0x8f1450), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOfflineModeLoaded (0xbf6434)
    //     0x8f106c: ldr             x1, [x1, #0x640]
    // 0x8f1070: stur            x0, [fp, #-0x18]
    // 0x8f1074: r0 = AllocateClosure()
    //     0x8f1074: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f1078: r16 = <bool>
    //     0x8f1078: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x8f107c: ldur            lr, [fp, #-0x18]
    // 0x8f1080: stp             lr, x16, [SP, #8]
    // 0x8f1084: str             x0, [SP]
    // 0x8f1088: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f1088: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f108c: r0 = then()
    //     0x8f108c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f1090: ldur            x0, [fp, #-8]
    // 0x8f1094: ldur            x1, [fp, #-0x10]
    // 0x8f1098: r2 = false
    //     0x8f1098: add             x2, NULL, #0x30  ; false
    // 0x8f109c: r0 = value=()
    //     0x8f109c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f10a0: ldur            x0, [fp, #-8]
    // 0x8f10a4: LoadField: r1 = r0->field_33
    //     0x8f10a4: ldur            w1, [x0, #0x33]
    // 0x8f10a8: DecompressPointer r1
    //     0x8f10a8: add             x1, x1, HEAP, lsl #32
    // 0x8f10ac: r0 = value()
    //     0x8f10ac: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f10b0: r1 = LoadInt32Instr(r0)
    //     0x8f10b0: sbfx            x1, x0, #1, #0x1f
    //     0x8f10b4: tbz             w0, #0, #0x8f10bc
    //     0x8f10b8: ldur            x1, [x0, #7]
    // 0x8f10bc: sub             x2, x1, #1
    // 0x8f10c0: r0 = BoxInt64Instr(r2)
    //     0x8f10c0: sbfiz           x0, x2, #1, #0x1f
    //     0x8f10c4: cmp             x2, x0, asr #1
    //     0x8f10c8: b.eq            #0x8f10d4
    //     0x8f10cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f10d0: stur            x2, [x0, #7]
    // 0x8f10d4: ldur            x1, [fp, #-8]
    // 0x8f10d8: mov             x2, x0
    // 0x8f10dc: r0 = onPageViewChanged()
    //     0x8f10dc: bl              #0x8f1144  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::onPageViewChanged
    // 0x8f10e0: r0 = Null
    //     0x8f10e0: mov             x0, NULL
    // 0x8f10e4: LeaveFrame
    //     0x8f10e4: mov             SP, fp
    //     0x8f10e8: ldp             fp, lr, [SP], #0x10
    // 0x8f10ec: ret
    //     0x8f10ec: ret             
    // 0x8f10f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f10f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f10f4: b               #0x8f0fe8
    // 0x8f10f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8f10f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ currentItem(/* No info */) {
    // ** addr: 0x8f13bc, size: 0x94
    // 0x8f13bc: EnterFrame
    //     0x8f13bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f13c0: mov             fp, SP
    // 0x8f13c4: AllocStack(0x10)
    //     0x8f13c4: sub             SP, SP, #0x10
    // 0x8f13c8: SetupParameters(DoaDetailController this /* r1 => r0, fp-0x8 */)
    //     0x8f13c8: mov             x0, x1
    //     0x8f13cc: stur            x1, [fp, #-8]
    // 0x8f13d0: CheckStackOverflow
    //     0x8f13d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f13d4: cmp             SP, x16
    //     0x8f13d8: b.ls            #0x8f1448
    // 0x8f13dc: mov             x1, x0
    // 0x8f13e0: r0 = notifyChildrens()
    //     0x8f13e0: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x8f13e4: ldur            x0, [fp, #-8]
    // 0x8f13e8: LoadField: r2 = r0->field_27
    //     0x8f13e8: ldur            w2, [x0, #0x27]
    // 0x8f13ec: DecompressPointer r2
    //     0x8f13ec: add             x2, x2, HEAP, lsl #32
    // 0x8f13f0: stur            x2, [fp, #-0x10]
    // 0x8f13f4: cmp             w2, NULL
    // 0x8f13f8: b.ne            #0x8f1404
    // 0x8f13fc: r0 = Null
    //     0x8f13fc: mov             x0, NULL
    // 0x8f1400: b               #0x8f143c
    // 0x8f1404: LoadField: r1 = r0->field_33
    //     0x8f1404: ldur            w1, [x0, #0x33]
    // 0x8f1408: DecompressPointer r1
    //     0x8f1408: add             x1, x1, HEAP, lsl #32
    // 0x8f140c: r0 = value()
    //     0x8f140c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f1410: r1 = LoadInt32Instr(r0)
    //     0x8f1410: sbfx            x1, x0, #1, #0x1f
    //     0x8f1414: tbz             w0, #0, #0x8f141c
    //     0x8f1418: ldur            x1, [x0, #7]
    // 0x8f141c: sub             x2, x1, #1
    // 0x8f1420: ldur            x1, [fp, #-0x10]
    // 0x8f1424: r0 = LoadClassIdInstr(r1)
    //     0x8f1424: ldur            x0, [x1, #-1]
    //     0x8f1428: ubfx            x0, x0, #0xc, #0x14
    // 0x8f142c: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x8f142c: movz            x17, #0xd28f
    //     0x8f1430: add             lr, x0, x17
    //     0x8f1434: ldr             lr, [x21, lr, lsl #3]
    //     0x8f1438: blr             lr
    // 0x8f143c: LeaveFrame
    //     0x8f143c: mov             SP, fp
    //     0x8f1440: ldp             fp, lr, [SP], #0x10
    // 0x8f1444: ret
    //     0x8f1444: ret             
    // 0x8f1448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f144c: b               #0x8f13dc
  }
  [closure] bool <anonymous closure>(dynamic, DoaSubCategory?) {
    // ** addr: 0x8f1450, size: 0x6c
    // 0x8f1450: EnterFrame
    //     0x8f1450: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1454: mov             fp, SP
    // 0x8f1458: AllocStack(0x8)
    //     0x8f1458: sub             SP, SP, #8
    // 0x8f145c: SetupParameters()
    //     0x8f145c: ldr             x0, [fp, #0x18]
    //     0x8f1460: ldur            w1, [x0, #0x17]
    //     0x8f1464: add             x1, x1, HEAP, lsl #32
    // 0x8f1468: CheckStackOverflow
    //     0x8f1468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f146c: cmp             SP, x16
    //     0x8f1470: b.ls            #0x8f14b4
    // 0x8f1474: LoadField: r0 = r1->field_f
    //     0x8f1474: ldur            w0, [x1, #0xf]
    // 0x8f1478: DecompressPointer r0
    //     0x8f1478: add             x0, x0, HEAP, lsl #32
    // 0x8f147c: LoadField: r1 = r0->field_53
    //     0x8f147c: ldur            w1, [x0, #0x53]
    // 0x8f1480: DecompressPointer r1
    //     0x8f1480: add             x1, x1, HEAP, lsl #32
    // 0x8f1484: ldr             x0, [fp, #0x10]
    // 0x8f1488: cmp             w0, NULL
    // 0x8f148c: r16 = true
    //     0x8f148c: add             x16, NULL, #0x20  ; true
    // 0x8f1490: r17 = false
    //     0x8f1490: add             x17, NULL, #0x30  ; false
    // 0x8f1494: csel            x3, x16, x17, ne
    // 0x8f1498: mov             x2, x3
    // 0x8f149c: stur            x3, [fp, #-8]
    // 0x8f14a0: r0 = value=()
    //     0x8f14a0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f14a4: ldur            x0, [fp, #-8]
    // 0x8f14a8: LeaveFrame
    //     0x8f14a8: mov             SP, fp
    //     0x8f14ac: ldp             fp, lr, [SP], #0x10
    // 0x8f14b0: ret
    //     0x8f14b0: ret             
    // 0x8f14b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f14b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f14b8: b               #0x8f1474
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0x8f1528, size: 0x54
    // 0x8f1528: EnterFrame
    //     0x8f1528: stp             fp, lr, [SP, #-0x10]!
    //     0x8f152c: mov             fp, SP
    // 0x8f1530: ldr             x0, [fp, #0x18]
    // 0x8f1534: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f1534: ldur            w1, [x0, #0x17]
    // 0x8f1538: DecompressPointer r1
    //     0x8f1538: add             x1, x1, HEAP, lsl #32
    // 0x8f153c: CheckStackOverflow
    //     0x8f153c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1540: cmp             SP, x16
    //     0x8f1544: b.ls            #0x8f1574
    // 0x8f1548: ldr             x2, [fp, #0x10]
    // 0x8f154c: cmp             w2, NULL
    // 0x8f1550: b.eq            #0x8f1564
    // 0x8f1554: LoadField: r0 = r1->field_f
    //     0x8f1554: ldur            w0, [x1, #0xf]
    // 0x8f1558: DecompressPointer r0
    //     0x8f1558: add             x0, x0, HEAP, lsl #32
    // 0x8f155c: mov             x1, x0
    // 0x8f1560: r0 = executeOnlineMode()
    //     0x8f1560: bl              #0x8f157c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin::executeOnlineMode
    // 0x8f1564: r0 = Null
    //     0x8f1564: mov             x0, NULL
    // 0x8f1568: LeaveFrame
    //     0x8f1568: mov             SP, fp
    //     0x8f156c: ldp             fp, lr, [SP], #0x10
    // 0x8f1570: ret
    //     0x8f1570: ret             
    // 0x8f1574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1578: b               #0x8f1548
  }
  [closure] void startShowCase(dynamic) {
    // ** addr: 0x91e264, size: 0x38
    // 0x91e264: EnterFrame
    //     0x91e264: stp             fp, lr, [SP, #-0x10]!
    //     0x91e268: mov             fp, SP
    // 0x91e26c: ldr             x0, [fp, #0x10]
    // 0x91e270: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91e270: ldur            w1, [x0, #0x17]
    // 0x91e274: DecompressPointer r1
    //     0x91e274: add             x1, x1, HEAP, lsl #32
    // 0x91e278: CheckStackOverflow
    //     0x91e278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e27c: cmp             SP, x16
    //     0x91e280: b.ls            #0x91e294
    // 0x91e284: r0 = startShowCase()
    //     0x91e284: bl              #0x91e29c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::startShowCase
    // 0x91e288: LeaveFrame
    //     0x91e288: mov             SP, fp
    //     0x91e28c: ldp             fp, lr, [SP], #0x10
    // 0x91e290: ret
    //     0x91e290: ret             
    // 0x91e294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e298: b               #0x91e284
  }
  _ startShowCase(/* No info */) {
    // ** addr: 0x91e29c, size: 0x50
    // 0x91e29c: EnterFrame
    //     0x91e29c: stp             fp, lr, [SP, #-0x10]!
    //     0x91e2a0: mov             fp, SP
    // 0x91e2a4: AllocStack(0x8)
    //     0x91e2a4: sub             SP, SP, #8
    // 0x91e2a8: SetupParameters(DoaDetailController this /* r1 => r0, fp-0x8 */)
    //     0x91e2a8: mov             x0, x1
    //     0x91e2ac: stur            x1, [fp, #-8]
    // 0x91e2b0: CheckStackOverflow
    //     0x91e2b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e2b4: cmp             SP, x16
    //     0x91e2b8: b.ls            #0x91e2e4
    // 0x91e2bc: LoadField: r1 = r0->field_4b
    //     0x91e2bc: ldur            w1, [x0, #0x4b]
    // 0x91e2c0: DecompressPointer r1
    //     0x91e2c0: add             x1, x1, HEAP, lsl #32
    // 0x91e2c4: r0 = isFirstOpenDoa()
    //     0x91e2c4: bl              #0x91e56c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isFirstOpenDoa
    // 0x91e2c8: tbnz            w0, #4, #0x91e2d4
    // 0x91e2cc: ldur            x1, [fp, #-8]
    // 0x91e2d0: r0 = startShowCase()
    //     0x91e2d0: bl              #0x91e2ec  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin::startShowCase
    // 0x91e2d4: r0 = Null
    //     0x91e2d4: mov             x0, NULL
    // 0x91e2d8: LeaveFrame
    //     0x91e2d8: mov             SP, fp
    //     0x91e2dc: ldp             fp, lr, [SP], #0x10
    // 0x91e2e0: ret
    //     0x91e2e0: ret             
    // 0x91e2e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e2e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e2e8: b               #0x91e2bc
  }
  [closure] void finishShowCase(dynamic) {
    // ** addr: 0xad828c, size: 0x38
    // 0xad828c: EnterFrame
    //     0xad828c: stp             fp, lr, [SP, #-0x10]!
    //     0xad8290: mov             fp, SP
    // 0xad8294: ldr             x0, [fp, #0x10]
    // 0xad8298: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad8298: ldur            w1, [x0, #0x17]
    // 0xad829c: DecompressPointer r1
    //     0xad829c: add             x1, x1, HEAP, lsl #32
    // 0xad82a0: CheckStackOverflow
    //     0xad82a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad82a4: cmp             SP, x16
    //     0xad82a8: b.ls            #0xad82bc
    // 0xad82ac: r0 = finishShowCase()
    //     0xad82ac: bl              #0xad82c4  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::finishShowCase
    // 0xad82b0: LeaveFrame
    //     0xad82b0: mov             SP, fp
    //     0xad82b4: ldp             fp, lr, [SP], #0x10
    // 0xad82b8: ret
    //     0xad82b8: ret             
    // 0xad82bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad82bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad82c0: b               #0xad82ac
  }
  _ finishShowCase(/* No info */) {
    // ** addr: 0xad82c4, size: 0x40
    // 0xad82c4: EnterFrame
    //     0xad82c4: stp             fp, lr, [SP, #-0x10]!
    //     0xad82c8: mov             fp, SP
    // 0xad82cc: CheckStackOverflow
    //     0xad82cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad82d0: cmp             SP, x16
    //     0xad82d4: b.ls            #0xad82fc
    // 0xad82d8: LoadField: r0 = r1->field_4b
    //     0xad82d8: ldur            w0, [x1, #0x4b]
    // 0xad82dc: DecompressPointer r0
    //     0xad82dc: add             x0, x0, HEAP, lsl #32
    // 0xad82e0: mov             x1, x0
    // 0xad82e4: r2 = false
    //     0xad82e4: add             x2, NULL, #0x30  ; false
    // 0xad82e8: r0 = isFirstOpenDoa=()
    //     0xad82e8: bl              #0xad8304  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isFirstOpenDoa=
    // 0xad82ec: r0 = Null
    //     0xad82ec: mov             x0, NULL
    // 0xad82f0: LeaveFrame
    //     0xad82f0: mov             SP, fp
    //     0xad82f4: ldp             fp, lr, [SP], #0x10
    // 0xad82f8: ret
    //     0xad82f8: ret             
    // 0xad82fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad82fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8300: b               #0xad82d8
  }
  get _ showPageViewNavigation(/* No info */) {
    // ** addr: 0xad8afc, size: 0x40
    // 0xad8afc: EnterFrame
    //     0xad8afc: stp             fp, lr, [SP, #-0x10]!
    //     0xad8b00: mov             fp, SP
    // 0xad8b04: CheckStackOverflow
    //     0xad8b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8b08: cmp             SP, x16
    //     0xad8b0c: b.ls            #0xad8b34
    // 0xad8b10: r0 = maxPageView()
    //     0xad8b10: bl              #0xad8b3c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::maxPageView
    // 0xad8b14: cmp             x0, #0
    // 0xad8b18: r16 = true
    //     0xad8b18: add             x16, NULL, #0x20  ; true
    // 0xad8b1c: r17 = false
    //     0xad8b1c: add             x17, NULL, #0x30  ; false
    // 0xad8b20: csel            x1, x16, x17, gt
    // 0xad8b24: mov             x0, x1
    // 0xad8b28: LeaveFrame
    //     0xad8b28: mov             SP, fp
    //     0xad8b2c: ldp             fp, lr, [SP], #0x10
    // 0xad8b30: ret
    //     0xad8b30: ret             
    // 0xad8b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8b38: b               #0xad8b10
  }
  int maxPageView(DoaDetailController) {
    // ** addr: 0xad8b3c, size: 0x84
    // 0xad8b3c: EnterFrame
    //     0xad8b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xad8b40: mov             fp, SP
    // 0xad8b44: AllocStack(0x8)
    //     0xad8b44: sub             SP, SP, #8
    // 0xad8b48: CheckStackOverflow
    //     0xad8b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8b4c: cmp             SP, x16
    //     0xad8b50: b.ls            #0xad8bb8
    // 0xad8b54: r0 = value()
    //     0xad8b54: bl              #0x6fb858  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin::value
    // 0xad8b58: cmp             w0, NULL
    // 0xad8b5c: b.ne            #0xad8b68
    // 0xad8b60: r1 = Null
    //     0xad8b60: mov             x1, NULL
    // 0xad8b64: b               #0xad8b8c
    // 0xad8b68: r1 = LoadClassIdInstr(r0)
    //     0xad8b68: ldur            x1, [x0, #-1]
    //     0xad8b6c: ubfx            x1, x1, #0xc, #0x14
    // 0xad8b70: str             x0, [SP]
    // 0xad8b74: mov             x0, x1
    // 0xad8b78: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad8b78: movz            x17, #0xc834
    //     0xad8b7c: add             lr, x0, x17
    //     0xad8b80: ldr             lr, [x21, lr, lsl #3]
    //     0xad8b84: blr             lr
    // 0xad8b88: mov             x1, x0
    // 0xad8b8c: cmp             w1, NULL
    // 0xad8b90: b.ne            #0xad8b9c
    // 0xad8b94: r0 = 0
    //     0xad8b94: movz            x0, #0
    // 0xad8b98: b               #0xad8bac
    // 0xad8b9c: r2 = LoadInt32Instr(r1)
    //     0xad8b9c: sbfx            x2, x1, #1, #0x1f
    //     0xad8ba0: tbz             w1, #0, #0xad8ba8
    //     0xad8ba4: ldur            x2, [x1, #7]
    // 0xad8ba8: mov             x0, x2
    // 0xad8bac: LeaveFrame
    //     0xad8bac: mov             SP, fp
    //     0xad8bb0: ldp             fp, lr, [SP], #0x10
    // 0xad8bb4: ret
    //     0xad8bb4: ret             
    // 0xad8bb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8bb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8bbc: b               #0xad8b54
  }
  get _ bookmark(/* No info */) {
    // ** addr: 0xad9264, size: 0x38
    // 0xad9264: EnterFrame
    //     0xad9264: stp             fp, lr, [SP, #-0x10]!
    //     0xad9268: mov             fp, SP
    // 0xad926c: CheckStackOverflow
    //     0xad926c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9270: cmp             SP, x16
    //     0xad9274: b.ls            #0xad9294
    // 0xad9278: LoadField: r0 = r1->field_53
    //     0xad9278: ldur            w0, [x1, #0x53]
    // 0xad927c: DecompressPointer r0
    //     0xad927c: add             x0, x0, HEAP, lsl #32
    // 0xad9280: mov             x1, x0
    // 0xad9284: r0 = value()
    //     0xad9284: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad9288: LeaveFrame
    //     0xad9288: mov             SP, fp
    //     0xad928c: ldp             fp, lr, [SP], #0x10
    // 0xad9290: ret
    //     0xad9290: ret             
    // 0xad9294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9298: b               #0xad9278
  }
  get _ loadingBookmark(/* No info */) {
    // ** addr: 0xad929c, size: 0x38
    // 0xad929c: EnterFrame
    //     0xad929c: stp             fp, lr, [SP, #-0x10]!
    //     0xad92a0: mov             fp, SP
    // 0xad92a4: CheckStackOverflow
    //     0xad92a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad92a8: cmp             SP, x16
    //     0xad92ac: b.ls            #0xad92cc
    // 0xad92b0: LoadField: r0 = r1->field_57
    //     0xad92b0: ldur            w0, [x1, #0x57]
    // 0xad92b4: DecompressPointer r0
    //     0xad92b4: add             x0, x0, HEAP, lsl #32
    // 0xad92b8: mov             x1, x0
    // 0xad92bc: r0 = value()
    //     0xad92bc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad92c0: LeaveFrame
    //     0xad92c0: mov             SP, fp
    //     0xad92c4: ldp             fp, lr, [SP], #0x10
    // 0xad92c8: ret
    //     0xad92c8: ret             
    // 0xad92cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad92cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad92d0: b               #0xad92b0
  }
  [closure] Future<void> toggleBookmark(dynamic) {
    // ** addr: 0xad92d4, size: 0x38
    // 0xad92d4: EnterFrame
    //     0xad92d4: stp             fp, lr, [SP, #-0x10]!
    //     0xad92d8: mov             fp, SP
    // 0xad92dc: ldr             x0, [fp, #0x10]
    // 0xad92e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad92e0: ldur            w1, [x0, #0x17]
    // 0xad92e4: DecompressPointer r1
    //     0xad92e4: add             x1, x1, HEAP, lsl #32
    // 0xad92e8: CheckStackOverflow
    //     0xad92e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad92ec: cmp             SP, x16
    //     0xad92f0: b.ls            #0xad9304
    // 0xad92f4: r0 = toggleBookmark()
    //     0xad92f4: bl              #0xad930c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::toggleBookmark
    // 0xad92f8: LeaveFrame
    //     0xad92f8: mov             SP, fp
    //     0xad92fc: ldp             fp, lr, [SP], #0x10
    // 0xad9300: ret
    //     0xad9300: ret             
    // 0xad9304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9308: b               #0xad92f4
  }
  _ toggleBookmark(/* No info */) async {
    // ** addr: 0xad930c, size: 0x110
    // 0xad930c: EnterFrame
    //     0xad930c: stp             fp, lr, [SP, #-0x10]!
    //     0xad9310: mov             fp, SP
    // 0xad9314: AllocStack(0x40)
    //     0xad9314: sub             SP, SP, #0x40
    // 0xad9318: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */)
    //     0xad9318: stur            NULL, [fp, #-8]
    //     0xad931c: stur            x1, [fp, #-0x10]
    // 0xad9320: CheckStackOverflow
    //     0xad9320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9324: cmp             SP, x16
    //     0xad9328: b.ls            #0xad9410
    // 0xad932c: r1 = 1
    //     0xad932c: movz            x1, #0x1
    // 0xad9330: r0 = AllocateContext()
    //     0xad9330: bl              #0xec126c  ; AllocateContextStub
    // 0xad9334: mov             x2, x0
    // 0xad9338: ldur            x1, [fp, #-0x10]
    // 0xad933c: stur            x2, [fp, #-0x18]
    // 0xad9340: StoreField: r2->field_f = r1
    //     0xad9340: stur            w1, [x2, #0xf]
    // 0xad9344: InitAsync() -> Future<void?>
    //     0xad9344: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad9348: bl              #0x661298  ; InitAsyncStub
    // 0xad934c: ldur            x1, [fp, #-0x10]
    // 0xad9350: r0 = currentItem()
    //     0xad9350: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0xad9354: cmp             w0, NULL
    // 0xad9358: b.ne            #0xad9364
    // 0xad935c: r0 = Null
    //     0xad935c: mov             x0, NULL
    // 0xad9360: r0 = ReturnAsyncNotFuture()
    //     0xad9360: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad9364: ldur            x0, [fp, #-0x10]
    // 0xad9368: LoadField: r1 = r0->field_57
    //     0xad9368: ldur            w1, [x0, #0x57]
    // 0xad936c: DecompressPointer r1
    //     0xad936c: add             x1, x1, HEAP, lsl #32
    // 0xad9370: r2 = true
    //     0xad9370: add             x2, NULL, #0x20  ; true
    // 0xad9374: r0 = value=()
    //     0xad9374: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad9378: ldur            x0, [fp, #-0x10]
    // 0xad937c: LoadField: r2 = r0->field_43
    //     0xad937c: ldur            w2, [x0, #0x43]
    // 0xad9380: DecompressPointer r2
    //     0xad9380: add             x2, x2, HEAP, lsl #32
    // 0xad9384: mov             x1, x0
    // 0xad9388: stur            x2, [fp, #-0x20]
    // 0xad938c: r0 = currentItem()
    //     0xad938c: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0xad9390: stur            x0, [fp, #-0x28]
    // 0xad9394: cmp             w0, NULL
    // 0xad9398: b.eq            #0xad9418
    // 0xad939c: ldur            x1, [fp, #-0x10]
    // 0xad93a0: LoadField: r2 = r1->field_53
    //     0xad93a0: ldur            w2, [x1, #0x53]
    // 0xad93a4: DecompressPointer r2
    //     0xad93a4: add             x2, x2, HEAP, lsl #32
    // 0xad93a8: mov             x1, x2
    // 0xad93ac: r0 = value()
    //     0xad93ac: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad93b0: eor             x3, x0, #0x10
    // 0xad93b4: ldur            x1, [fp, #-0x20]
    // 0xad93b8: r0 = LoadClassIdInstr(r1)
    //     0xad93b8: ldur            x0, [x1, #-1]
    //     0xad93bc: ubfx            x0, x0, #0xc, #0x14
    // 0xad93c0: ldur            x2, [fp, #-0x28]
    // 0xad93c4: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xad93c4: sub             lr, x0, #0xfdf
    //     0xad93c8: ldr             lr, [x21, lr, lsl #3]
    //     0xad93cc: blr             lr
    // 0xad93d0: ldur            x2, [fp, #-0x18]
    // 0xad93d4: r1 = Function '<anonymous closure>':.
    //     0xad93d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] AnonymousClosure: (0xad941c), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::toggleBookmark (0xad930c)
    //     0xad93d8: ldr             x1, [x1, #0x9a8]
    // 0xad93dc: stur            x0, [fp, #-0x10]
    // 0xad93e0: r0 = AllocateClosure()
    //     0xad93e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad93e4: r16 = <Null?>
    //     0xad93e4: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xad93e8: ldur            lr, [fp, #-0x10]
    // 0xad93ec: stp             lr, x16, [SP, #8]
    // 0xad93f0: str             x0, [SP]
    // 0xad93f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad93f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad93f8: r0 = then()
    //     0xad93f8: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xad93fc: mov             x1, x0
    // 0xad9400: stur            x1, [fp, #-0x10]
    // 0xad9404: r0 = Await()
    //     0xad9404: bl              #0x661044  ; AwaitStub
    // 0xad9408: r0 = Null
    //     0xad9408: mov             x0, NULL
    // 0xad940c: r0 = ReturnAsyncNotFuture()
    //     0xad940c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad9410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9410: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9414: b               #0xad932c
    // 0xad9418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9418: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0xad941c, size: 0xb0
    // 0xad941c: EnterFrame
    //     0xad941c: stp             fp, lr, [SP, #-0x10]!
    //     0xad9420: mov             fp, SP
    // 0xad9424: AllocStack(0x8)
    //     0xad9424: sub             SP, SP, #8
    // 0xad9428: SetupParameters()
    //     0xad9428: ldr             x0, [fp, #0x18]
    //     0xad942c: ldur            w2, [x0, #0x17]
    //     0xad9430: add             x2, x2, HEAP, lsl #32
    //     0xad9434: stur            x2, [fp, #-8]
    // 0xad9438: CheckStackOverflow
    //     0xad9438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad943c: cmp             SP, x16
    //     0xad9440: b.ls            #0xad94c4
    // 0xad9444: LoadField: r0 = r2->field_f
    //     0xad9444: ldur            w0, [x2, #0xf]
    // 0xad9448: DecompressPointer r0
    //     0xad9448: add             x0, x0, HEAP, lsl #32
    // 0xad944c: LoadField: r1 = r0->field_53
    //     0xad944c: ldur            w1, [x0, #0x53]
    // 0xad9450: DecompressPointer r1
    //     0xad9450: add             x1, x1, HEAP, lsl #32
    // 0xad9454: r0 = value()
    //     0xad9454: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad9458: tbnz            w0, #4, #0xad9474
    // 0xad945c: r1 = "Doa dihapus dari bookmark!"
    //     0xad945c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] "Doa dihapus dari bookmark!"
    //     0xad9460: ldr             x1, [x1, #0x9b0]
    // 0xad9464: r2 = Instance_IconData
    //     0xad9464: add             x2, PP, #0x29, lsl #12  ; [pp+0x29890] Obj!IconData@e100b1
    //     0xad9468: ldr             x2, [x2, #0x890]
    // 0xad946c: r0 = show()
    //     0xad946c: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xad9470: b               #0xad9478
    // 0xad9474: r0 = bookmark()
    //     0xad9474: bl              #0xa427d4  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::bookmark
    // 0xad9478: ldur            x0, [fp, #-8]
    // 0xad947c: LoadField: r1 = r0->field_f
    //     0xad947c: ldur            w1, [x0, #0xf]
    // 0xad9480: DecompressPointer r1
    //     0xad9480: add             x1, x1, HEAP, lsl #32
    // 0xad9484: LoadField: r2 = r1->field_53
    //     0xad9484: ldur            w2, [x1, #0x53]
    // 0xad9488: DecompressPointer r2
    //     0xad9488: add             x2, x2, HEAP, lsl #32
    // 0xad948c: mov             x1, x2
    // 0xad9490: r0 = RxBoolExt.toggle()
    //     0xad9490: bl              #0xa428f0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.toggle
    // 0xad9494: ldur            x0, [fp, #-8]
    // 0xad9498: LoadField: r1 = r0->field_f
    //     0xad9498: ldur            w1, [x0, #0xf]
    // 0xad949c: DecompressPointer r1
    //     0xad949c: add             x1, x1, HEAP, lsl #32
    // 0xad94a0: LoadField: r0 = r1->field_57
    //     0xad94a0: ldur            w0, [x1, #0x57]
    // 0xad94a4: DecompressPointer r0
    //     0xad94a4: add             x0, x0, HEAP, lsl #32
    // 0xad94a8: mov             x1, x0
    // 0xad94ac: r2 = false
    //     0xad94ac: add             x2, NULL, #0x30  ; false
    // 0xad94b0: r0 = value=()
    //     0xad94b0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad94b4: r0 = Null
    //     0xad94b4: mov             x0, NULL
    // 0xad94b8: LeaveFrame
    //     0xad94b8: mov             SP, fp
    //     0xad94bc: ldp             fp, lr, [SP], #0x10
    // 0xad94c0: ret
    //     0xad94c0: ret             
    // 0xad94c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad94c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad94c8: b               #0xad9444
  }
  get _ title(/* No info */) {
    // ** addr: 0xad967c, size: 0x38
    // 0xad967c: EnterFrame
    //     0xad967c: stp             fp, lr, [SP, #-0x10]!
    //     0xad9680: mov             fp, SP
    // 0xad9684: CheckStackOverflow
    //     0xad9684: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9688: cmp             SP, x16
    //     0xad968c: b.ls            #0xad96ac
    // 0xad9690: LoadField: r0 = r1->field_4f
    //     0xad9690: ldur            w0, [x1, #0x4f]
    // 0xad9694: DecompressPointer r0
    //     0xad9694: add             x0, x0, HEAP, lsl #32
    // 0xad9698: mov             x1, x0
    // 0xad969c: r0 = value()
    //     0xad969c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad96a0: LeaveFrame
    //     0xad96a0: mov             SP, fp
    //     0xad96a4: ldp             fp, lr, [SP], #0x10
    // 0xad96a8: ret
    //     0xad96a8: ret             
    // 0xad96ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad96ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad96b0: b               #0xad9690
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef8b4, size: 0x5c
    // 0xbef8b4: EnterFrame
    //     0xbef8b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbef8b8: mov             fp, SP
    // 0xbef8bc: AllocStack(0x10)
    //     0xbef8bc: sub             SP, SP, #0x10
    // 0xbef8c0: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbef8c0: stur            NULL, [fp, #-8]
    //     0xbef8c4: stur            x1, [fp, #-0x10]
    // 0xbef8c8: CheckStackOverflow
    //     0xbef8c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef8cc: cmp             SP, x16
    //     0xbef8d0: b.ls            #0xbef908
    // 0xbef8d4: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xbef8d4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xbef8d8: ldr             x0, [x0, #0xd38]
    //     0xbef8dc: bl              #0x661298  ; InitAsyncStub
    // 0xbef8e0: ldur            x0, [fp, #-0x10]
    // 0xbef8e4: LoadField: r1 = r0->field_43
    //     0xbef8e4: ldur            w1, [x0, #0x43]
    // 0xbef8e8: DecompressPointer r1
    //     0xbef8e8: add             x1, x1, HEAP, lsl #32
    // 0xbef8ec: LoadField: r2 = r0->field_37
    //     0xbef8ec: ldur            x2, [x0, #0x37]
    // 0xbef8f0: r0 = LoadClassIdInstr(r1)
    //     0xbef8f0: ldur            x0, [x1, #-1]
    //     0xbef8f4: ubfx            x0, x0, #0xc, #0x14
    // 0xbef8f8: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xbef8f8: sub             lr, x0, #0xfe3
    //     0xbef8fc: ldr             lr, [x21, lr, lsl #3]
    //     0xbef900: blr             lr
    // 0xbef904: r0 = ReturnAsync()
    //     0xbef904: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef90c: b               #0xbef8d4
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf6434, size: 0x188
    // 0xbf6434: EnterFrame
    //     0xbf6434: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6438: mov             fp, SP
    // 0xbf643c: AllocStack(0x40)
    //     0xbf643c: sub             SP, SP, #0x40
    // 0xbf6440: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbf6440: stur            NULL, [fp, #-8]
    //     0xbf6444: stur            x1, [fp, #-0x10]
    //     0xbf6448: stur            x2, [fp, #-0x18]
    // 0xbf644c: CheckStackOverflow
    //     0xbf644c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6450: cmp             SP, x16
    //     0xbf6454: b.ls            #0xbf65b4
    // 0xbf6458: r1 = 1
    //     0xbf6458: movz            x1, #0x1
    // 0xbf645c: r0 = AllocateContext()
    //     0xbf645c: bl              #0xec126c  ; AllocateContextStub
    // 0xbf6460: mov             x2, x0
    // 0xbf6464: ldur            x1, [fp, #-0x10]
    // 0xbf6468: stur            x2, [fp, #-0x20]
    // 0xbf646c: StoreField: r2->field_f = r1
    //     0xbf646c: stur            w1, [x2, #0xf]
    // 0xbf6470: InitAsync() -> Future<void?>
    //     0xbf6470: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf6474: bl              #0x661298  ; InitAsyncStub
    // 0xbf6478: r0 = RxStatus()
    //     0xbf6478: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf647c: mov             x1, x0
    // 0xbf6480: r0 = false
    //     0xbf6480: add             x0, NULL, #0x30  ; false
    // 0xbf6484: StoreField: r1->field_f = r0
    //     0xbf6484: stur            w0, [x1, #0xf]
    // 0xbf6488: StoreField: r1->field_7 = r0
    //     0xbf6488: stur            w0, [x1, #7]
    // 0xbf648c: StoreField: r1->field_b = r0
    //     0xbf648c: stur            w0, [x1, #0xb]
    // 0xbf6490: mov             x3, x1
    // 0xbf6494: ldur            x1, [fp, #-0x10]
    // 0xbf6498: ldur            x2, [fp, #-0x18]
    // 0xbf649c: r0 = change()
    //     0xbf649c: bl              #0xbf65bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin::change
    // 0xbf64a0: r1 = Null
    //     0xbf64a0: mov             x1, NULL
    // 0xbf64a4: r2 = Instance_Duration
    //     0xbf64a4: add             x2, PP, #0x40, lsl #12  ; [pp+0x405e0] Obj!Duration@e3a1d1
    //     0xbf64a8: ldr             x2, [x2, #0x5e0]
    // 0xbf64ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf64ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf64b0: r0 = Future.delayed()
    //     0xbf64b0: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0xbf64b4: mov             x1, x0
    // 0xbf64b8: stur            x1, [fp, #-0x28]
    // 0xbf64bc: r0 = Await()
    //     0xbf64bc: bl              #0x661044  ; AwaitStub
    // 0xbf64c0: ldur            x2, [fp, #-0x20]
    // 0xbf64c4: r1 = Function '<anonymous closure>':.
    //     0xbf64c4: add             x1, PP, #0x40, lsl #12  ; [pp+0x405e8] AnonymousClosure: (0xbf6678), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOfflineModeLoaded (0xbf6434)
    //     0xbf64c8: ldr             x1, [x1, #0x5e8]
    // 0xbf64cc: r0 = AllocateClosure()
    //     0xbf64cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf64d0: r16 = <DoaSubCategory>
    //     0xbf64d0: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xbf64d4: ldur            lr, [fp, #-0x18]
    // 0xbf64d8: stp             lr, x16, [SP, #8]
    // 0xbf64dc: str             x0, [SP]
    // 0xbf64e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf64e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf64e4: r0 = IterableExtension.firstWhereOrNull()
    //     0xbf64e4: bl              #0x7379ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0xbf64e8: mov             x3, x0
    // 0xbf64ec: stur            x3, [fp, #-0x28]
    // 0xbf64f0: cmp             w3, NULL
    // 0xbf64f4: b.eq            #0xbf65ac
    // 0xbf64f8: ldur            x4, [fp, #-0x10]
    // 0xbf64fc: ldur            x5, [fp, #-0x18]
    // 0xbf6500: LoadField: r1 = r4->field_43
    //     0xbf6500: ldur            w1, [x4, #0x43]
    // 0xbf6504: DecompressPointer r1
    //     0xbf6504: add             x1, x1, HEAP, lsl #32
    // 0xbf6508: LoadField: r2 = r3->field_7
    //     0xbf6508: ldur            x2, [x3, #7]
    // 0xbf650c: r0 = LoadClassIdInstr(r1)
    //     0xbf650c: ldur            x0, [x1, #-1]
    //     0xbf6510: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6514: r0 = GDT[cid_x0 + -0xfff]()
    //     0xbf6514: sub             lr, x0, #0xfff
    //     0xbf6518: ldr             lr, [x21, lr, lsl #3]
    //     0xbf651c: blr             lr
    // 0xbf6520: ldur            x2, [fp, #-0x20]
    // 0xbf6524: r1 = Function '<anonymous closure>':.
    //     0xbf6524: add             x1, PP, #0x40, lsl #12  ; [pp+0x405f0] AnonymousClosure: (0x8f1450), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOfflineModeLoaded (0xbf6434)
    //     0xbf6528: ldr             x1, [x1, #0x5f0]
    // 0xbf652c: stur            x0, [fp, #-0x20]
    // 0xbf6530: r0 = AllocateClosure()
    //     0xbf6530: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf6534: r16 = <bool>
    //     0xbf6534: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbf6538: ldur            lr, [fp, #-0x20]
    // 0xbf653c: stp             lr, x16, [SP, #8]
    // 0xbf6540: str             x0, [SP]
    // 0xbf6544: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf6544: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf6548: r0 = then()
    //     0xbf6548: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xbf654c: ldur            x0, [fp, #-0x10]
    // 0xbf6550: LoadField: r1 = r0->field_57
    //     0xbf6550: ldur            w1, [x0, #0x57]
    // 0xbf6554: DecompressPointer r1
    //     0xbf6554: add             x1, x1, HEAP, lsl #32
    // 0xbf6558: r2 = false
    //     0xbf6558: add             x2, NULL, #0x30  ; false
    // 0xbf655c: r0 = value=()
    //     0xbf655c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf6560: ldur            x0, [fp, #-0x10]
    // 0xbf6564: LoadField: r3 = r0->field_2f
    //     0xbf6564: ldur            w3, [x0, #0x2f]
    // 0xbf6568: DecompressPointer r3
    //     0xbf6568: add             x3, x3, HEAP, lsl #32
    // 0xbf656c: ldur            x1, [fp, #-0x18]
    // 0xbf6570: stur            x3, [fp, #-0x20]
    // 0xbf6574: r0 = LoadClassIdInstr(r1)
    //     0xbf6574: ldur            x0, [x1, #-1]
    //     0xbf6578: ubfx            x0, x0, #0xc, #0x14
    // 0xbf657c: ldur            x2, [fp, #-0x28]
    // 0xbf6580: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf6580: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf6584: r0 = GDT[cid_x0 + 0x131ff]()
    //     0xbf6584: movz            x17, #0x31ff
    //     0xbf6588: movk            x17, #0x1, lsl #16
    //     0xbf658c: add             lr, x0, x17
    //     0xbf6590: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6594: blr             lr
    // 0xbf6598: r2 = LoadInt32Instr(r0)
    //     0xbf6598: sbfx            x2, x0, #1, #0x1f
    //     0xbf659c: tbz             w0, #0, #0xbf65a4
    //     0xbf65a0: ldur            x2, [x0, #7]
    // 0xbf65a4: ldur            x1, [fp, #-0x20]
    // 0xbf65a8: r0 = jumpToPage()
    //     0xbf65a8: bl              #0x8e9ab4  ; [package:flutter/src/widgets/page_view.dart] PageController::jumpToPage
    // 0xbf65ac: r0 = Null
    //     0xbf65ac: mov             x0, NULL
    // 0xbf65b0: r0 = ReturnAsyncNotFuture()
    //     0xbf65b0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf65b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf65b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf65b8: b               #0xbf6458
  }
  [closure] bool <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xbf6678, size: 0x34
    // 0xbf6678: ldr             x1, [SP, #8]
    // 0xbf667c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbf667c: ldur            w2, [x1, #0x17]
    // 0xbf6680: DecompressPointer r2
    //     0xbf6680: add             x2, x2, HEAP, lsl #32
    // 0xbf6684: ldr             x1, [SP]
    // 0xbf6688: LoadField: r3 = r1->field_7
    //     0xbf6688: ldur            x3, [x1, #7]
    // 0xbf668c: LoadField: r1 = r2->field_f
    //     0xbf668c: ldur            w1, [x2, #0xf]
    // 0xbf6690: DecompressPointer r1
    //     0xbf6690: add             x1, x1, HEAP, lsl #32
    // 0xbf6694: LoadField: r2 = r1->field_37
    //     0xbf6694: ldur            x2, [x1, #0x37]
    // 0xbf6698: cmp             x3, x2
    // 0xbf669c: r16 = true
    //     0xbf669c: add             x16, NULL, #0x20  ; true
    // 0xbf66a0: r17 = false
    //     0xbf66a0: add             x17, NULL, #0x30  ; false
    // 0xbf66a4: csel            x0, x16, x17, eq
    // 0xbf66a8: ret
    //     0xbf66a8: ret             
  }
  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0xe35ccc, size: 0x60
    // 0xe35ccc: EnterFrame
    //     0xe35ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xe35cd0: mov             fp, SP
    // 0xe35cd4: AllocStack(0x18)
    //     0xe35cd4: sub             SP, SP, #0x18
    // 0xe35cd8: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe35cd8: stur            NULL, [fp, #-8]
    //     0xe35cdc: stur            x1, [fp, #-0x10]
    //     0xe35ce0: stur            x2, [fp, #-0x18]
    // 0xe35ce4: CheckStackOverflow
    //     0xe35ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35ce8: cmp             SP, x16
    //     0xe35cec: b.ls            #0xe35d24
    // 0xe35cf0: InitAsync() -> Future<ApiResult<List<Doa>>>
    //     0xe35cf0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35dd0] TypeArguments: <ApiResult<List<Doa>>>
    //     0xe35cf4: ldr             x0, [x0, #0xdd0]
    //     0xe35cf8: bl              #0x661298  ; InitAsyncStub
    // 0xe35cfc: ldur            x0, [fp, #-0x10]
    // 0xe35d00: LoadField: r1 = r0->field_47
    //     0xe35d00: ldur            w1, [x0, #0x47]
    // 0xe35d04: DecompressPointer r1
    //     0xe35d04: add             x1, x1, HEAP, lsl #32
    // 0xe35d08: r0 = LoadClassIdInstr(r1)
    //     0xe35d08: ldur            x0, [x1, #-1]
    //     0xe35d0c: ubfx            x0, x0, #0xc, #0x14
    // 0xe35d10: ldur            x2, [fp, #-0x18]
    // 0xe35d14: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xe35d14: sub             lr, x0, #0xfcb
    //     0xe35d18: ldr             lr, [x21, lr, lsl #3]
    //     0xe35d1c: blr             lr
    // 0xe35d20: r0 = ReturnAsync()
    //     0xe35d20: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe35d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe35d28: b               #0xe35cf0
  }
  _ onOnlineModeLoaded(/* No info */) {
    // ** addr: 0xe35d2c, size: 0x50
    // 0xe35d2c: EnterFrame
    //     0xe35d2c: stp             fp, lr, [SP, #-0x10]!
    //     0xe35d30: mov             fp, SP
    // 0xe35d34: CheckStackOverflow
    //     0xe35d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35d38: cmp             SP, x16
    //     0xe35d3c: b.ls            #0xe35d74
    // 0xe35d40: LoadField: r0 = r1->field_43
    //     0xe35d40: ldur            w0, [x1, #0x43]
    // 0xe35d44: DecompressPointer r0
    //     0xe35d44: add             x0, x0, HEAP, lsl #32
    // 0xe35d48: r1 = LoadClassIdInstr(r0)
    //     0xe35d48: ldur            x1, [x0, #-1]
    //     0xe35d4c: ubfx            x1, x1, #0xc, #0x14
    // 0xe35d50: mov             x16, x0
    // 0xe35d54: mov             x0, x1
    // 0xe35d58: mov             x1, x16
    // 0xe35d5c: r0 = GDT[cid_x0 + -0xfd8]()
    //     0xe35d5c: sub             lr, x0, #0xfd8
    //     0xe35d60: ldr             lr, [x21, lr, lsl #3]
    //     0xe35d64: blr             lr
    // 0xe35d68: LeaveFrame
    //     0xe35d68: mov             SP, fp
    //     0xe35d6c: ldp             fp, lr, [SP], #0x10
    // 0xe35d70: ret
    //     0xe35d70: ret             
    // 0xe35d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe35d78: b               #0xe35d40
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xe35dcc, size: 0x38
    // 0xe35dcc: EnterFrame
    //     0xe35dcc: stp             fp, lr, [SP, #-0x10]!
    //     0xe35dd0: mov             fp, SP
    // 0xe35dd4: AllocStack(0x10)
    //     0xe35dd4: sub             SP, SP, #0x10
    // 0xe35dd8: SetupParameters(DoaDetailController this /* r1 => r1, fp-0x10 */)
    //     0xe35dd8: stur            NULL, [fp, #-8]
    //     0xe35ddc: stur            x1, [fp, #-0x10]
    // 0xe35de0: CheckStackOverflow
    //     0xe35de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35de4: cmp             SP, x16
    //     0xe35de8: b.ls            #0xe35dfc
    // 0xe35dec: InitAsync() -> Future<void?>
    //     0xe35dec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe35df0: bl              #0x661298  ; InitAsyncStub
    // 0xe35df4: r0 = Null
    //     0xe35df4: mov             x0, NULL
    // 0xe35df8: r0 = ReturnAsyncNotFuture()
    //     0xe35df8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe35dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe35e00: b               #0xe35dec
  }
}
