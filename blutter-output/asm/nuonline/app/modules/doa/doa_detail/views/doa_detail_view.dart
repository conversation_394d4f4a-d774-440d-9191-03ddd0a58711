// lib: , url: package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart

// class id: 1050176, size: 0x8
class :: {
}

// class id: 5302, size: 0x14, field offset: 0x14
//   const constructor, 
class DoaDetailView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad81cc, size: 0xb4
    // 0xad81cc: EnterFrame
    //     0xad81cc: stp             fp, lr, [SP, #-0x10]!
    //     0xad81d0: mov             fp, SP
    // 0xad81d4: AllocStack(0x18)
    //     0xad81d4: sub             SP, SP, #0x18
    // 0xad81d8: SetupParameters(DoaDetailView this /* r1 => r1, fp-0x8 */)
    //     0xad81d8: stur            x1, [fp, #-8]
    // 0xad81dc: CheckStackOverflow
    //     0xad81dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad81e0: cmp             SP, x16
    //     0xad81e4: b.ls            #0xad8278
    // 0xad81e8: r1 = 1
    //     0xad81e8: movz            x1, #0x1
    // 0xad81ec: r0 = AllocateContext()
    //     0xad81ec: bl              #0xec126c  ; AllocateContextStub
    // 0xad81f0: ldur            x1, [fp, #-8]
    // 0xad81f4: stur            x0, [fp, #-0x10]
    // 0xad81f8: StoreField: r0->field_f = r1
    //     0xad81f8: stur            w1, [x0, #0xf]
    // 0xad81fc: r0 = controller()
    //     0xad81fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8200: ldur            x2, [fp, #-0x10]
    // 0xad8204: r1 = Function '<anonymous closure>':.
    //     0xad8204: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] AnonymousClosure: (0xad8340), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad8208: ldr             x1, [x1, #0x8f0]
    // 0xad820c: stur            x0, [fp, #-8]
    // 0xad8210: r0 = AllocateClosure()
    //     0xad8210: bl              #0xec1630  ; AllocateClosureStub
    // 0xad8214: stur            x0, [fp, #-0x10]
    // 0xad8218: r0 = ShowCaseWidget()
    //     0xad8218: bl              #0xad8280  ; AllocateShowCaseWidgetStub -> ShowCaseWidget (size=0x3c)
    // 0xad821c: mov             x3, x0
    // 0xad8220: ldur            x0, [fp, #-0x10]
    // 0xad8224: stur            x3, [fp, #-0x18]
    // 0xad8228: StoreField: r3->field_b = r0
    //     0xad8228: stur            w0, [x3, #0xb]
    // 0xad822c: ldur            x2, [fp, #-8]
    // 0xad8230: r1 = Function 'finishShowCase':.
    //     0xad8230: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8f8] AnonymousClosure: (0xad828c), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::finishShowCase (0xad82c4)
    //     0xad8234: ldr             x1, [x1, #0x8f8]
    // 0xad8238: r0 = AllocateClosure()
    //     0xad8238: bl              #0xec1630  ; AllocateClosureStub
    // 0xad823c: mov             x1, x0
    // 0xad8240: ldur            x0, [fp, #-0x18]
    // 0xad8244: StoreField: r0->field_f = r1
    //     0xad8244: stur            w1, [x0, #0xf]
    // 0xad8248: r1 = false
    //     0xad8248: add             x1, NULL, #0x30  ; false
    // 0xad824c: StoreField: r0->field_1b = r1
    //     0xad824c: stur            w1, [x0, #0x1b]
    // 0xad8250: StoreField: r0->field_2b = rZR
    //     0xad8250: stur            xzr, [x0, #0x2b]
    // 0xad8254: StoreField: r0->field_1f = r1
    //     0xad8254: stur            w1, [x0, #0x1f]
    // 0xad8258: StoreField: r0->field_23 = r1
    //     0xad8258: stur            w1, [x0, #0x23]
    // 0xad825c: StoreField: r0->field_33 = r1
    //     0xad825c: stur            w1, [x0, #0x33]
    // 0xad8260: StoreField: r0->field_27 = r1
    //     0xad8260: stur            w1, [x0, #0x27]
    // 0xad8264: r1 = true
    //     0xad8264: add             x1, NULL, #0x20  ; true
    // 0xad8268: StoreField: r0->field_37 = r1
    //     0xad8268: stur            w1, [x0, #0x37]
    // 0xad826c: LeaveFrame
    //     0xad826c: mov             SP, fp
    //     0xad8270: ldp             fp, lr, [SP], #0x10
    // 0xad8274: ret
    //     0xad8274: ret             
    // 0xad8278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad827c: b               #0xad81e8
  }
  [closure] Scaffold <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xad8340, size: 0x4ec
    // 0xad8340: EnterFrame
    //     0xad8340: stp             fp, lr, [SP, #-0x10]!
    //     0xad8344: mov             fp, SP
    // 0xad8348: AllocStack(0x58)
    //     0xad8348: sub             SP, SP, #0x58
    // 0xad834c: SetupParameters()
    //     0xad834c: ldr             x0, [fp, #0x18]
    //     0xad8350: ldur            w1, [x0, #0x17]
    //     0xad8354: add             x1, x1, HEAP, lsl #32
    //     0xad8358: stur            x1, [fp, #-8]
    // 0xad835c: CheckStackOverflow
    //     0xad835c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8360: cmp             SP, x16
    //     0xad8364: b.ls            #0xad881c
    // 0xad8368: r1 = 1
    //     0xad8368: movz            x1, #0x1
    // 0xad836c: r0 = AllocateContext()
    //     0xad836c: bl              #0xec126c  ; AllocateContextStub
    // 0xad8370: mov             x2, x0
    // 0xad8374: ldur            x0, [fp, #-8]
    // 0xad8378: stur            x2, [fp, #-0x10]
    // 0xad837c: StoreField: r2->field_b = r0
    //     0xad837c: stur            w0, [x2, #0xb]
    // 0xad8380: ldr             x1, [fp, #0x10]
    // 0xad8384: StoreField: r2->field_f = r1
    //     0xad8384: stur            w1, [x2, #0xf]
    // 0xad8388: LoadField: r1 = r0->field_f
    //     0xad8388: ldur            w1, [x0, #0xf]
    // 0xad838c: DecompressPointer r1
    //     0xad838c: add             x1, x1, HEAP, lsl #32
    // 0xad8390: r0 = controller()
    //     0xad8390: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8394: mov             x1, x0
    // 0xad8398: ldur            x2, [fp, #-0x10]
    // 0xad839c: LoadField: r0 = r2->field_f
    //     0xad839c: ldur            w0, [x2, #0xf]
    // 0xad83a0: DecompressPointer r0
    //     0xad83a0: add             x0, x0, HEAP, lsl #32
    // 0xad83a4: StoreField: r1->field_23 = r0
    //     0xad83a4: stur            w0, [x1, #0x23]
    //     0xad83a8: ldurb           w16, [x1, #-1]
    //     0xad83ac: ldurb           w17, [x0, #-1]
    //     0xad83b0: and             x16, x17, x16, lsr #2
    //     0xad83b4: tst             x16, HEAP, lsr #32
    //     0xad83b8: b.eq            #0xad83c0
    //     0xad83bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xad83c0: r0 = Obx()
    //     0xad83c0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad83c4: ldur            x2, [fp, #-0x10]
    // 0xad83c8: r1 = Function '<anonymous closure>':.
    //     0xad83c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f908] AnonymousClosure: (0xad95e0), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad83cc: ldr             x1, [x1, #0x908]
    // 0xad83d0: stur            x0, [fp, #-0x18]
    // 0xad83d4: r0 = AllocateClosure()
    //     0xad83d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad83d8: mov             x1, x0
    // 0xad83dc: ldur            x0, [fp, #-0x18]
    // 0xad83e0: StoreField: r0->field_b = r1
    //     0xad83e0: stur            w1, [x0, #0xb]
    // 0xad83e4: ldur            x2, [fp, #-8]
    // 0xad83e8: LoadField: r1 = r2->field_f
    //     0xad83e8: ldur            w1, [x2, #0xf]
    // 0xad83ec: DecompressPointer r1
    //     0xad83ec: add             x1, x1, HEAP, lsl #32
    // 0xad83f0: r0 = controller()
    //     0xad83f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad83f4: LoadField: r2 = r0->field_1f
    //     0xad83f4: ldur            w2, [x0, #0x1f]
    // 0xad83f8: DecompressPointer r2
    //     0xad83f8: add             x2, x2, HEAP, lsl #32
    // 0xad83fc: LoadField: r0 = r2->field_b
    //     0xad83fc: ldur            w0, [x2, #0xb]
    // 0xad8400: r1 = LoadInt32Instr(r0)
    //     0xad8400: sbfx            x1, x0, #1, #0x1f
    // 0xad8404: mov             x0, x1
    // 0xad8408: r1 = 2
    //     0xad8408: movz            x1, #0x2
    // 0xad840c: cmp             x1, x0
    // 0xad8410: b.hs            #0xad8824
    // 0xad8414: LoadField: r0 = r2->field_f
    //     0xad8414: ldur            w0, [x2, #0xf]
    // 0xad8418: DecompressPointer r0
    //     0xad8418: add             x0, x0, HEAP, lsl #32
    // 0xad841c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xad841c: ldur            w3, [x0, #0x17]
    // 0xad8420: DecompressPointer r3
    //     0xad8420: add             x3, x3, HEAP, lsl #32
    // 0xad8424: ldur            x2, [fp, #-0x10]
    // 0xad8428: stur            x3, [fp, #-0x20]
    // 0xad842c: r1 = Function '<anonymous closure>':.
    //     0xad842c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f910] AnonymousClosure: (0xad94cc), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad8430: ldr             x1, [x1, #0x910]
    // 0xad8434: r0 = AllocateClosure()
    //     0xad8434: bl              #0xec1630  ; AllocateClosureStub
    // 0xad8438: stur            x0, [fp, #-0x28]
    // 0xad843c: r0 = IconButton()
    //     0xad843c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad8440: mov             x1, x0
    // 0xad8444: ldur            x0, [fp, #-0x28]
    // 0xad8448: stur            x1, [fp, #-0x30]
    // 0xad844c: StoreField: r1->field_3b = r0
    //     0xad844c: stur            w0, [x1, #0x3b]
    // 0xad8450: r0 = false
    //     0xad8450: add             x0, NULL, #0x30  ; false
    // 0xad8454: StoreField: r1->field_47 = r0
    //     0xad8454: stur            w0, [x1, #0x47]
    // 0xad8458: r2 = Instance_Icon
    //     0xad8458: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f70] Obj!Icon@e24431
    //     0xad845c: ldr             x2, [x2, #0xf70]
    // 0xad8460: StoreField: r1->field_1f = r2
    //     0xad8460: stur            w2, [x1, #0x1f]
    // 0xad8464: r2 = Instance__IconButtonVariant
    //     0xad8464: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad8468: ldr             x2, [x2, #0xf78]
    // 0xad846c: StoreField: r1->field_63 = r2
    //     0xad846c: stur            w2, [x1, #0x63]
    // 0xad8470: r0 = SizedBox()
    //     0xad8470: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad8474: mov             x1, x0
    // 0xad8478: r0 = 42.000000
    //     0xad8478: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2be80] 42
    //     0xad847c: ldr             x0, [x0, #0xe80]
    // 0xad8480: stur            x1, [fp, #-0x28]
    // 0xad8484: StoreField: r1->field_f = r0
    //     0xad8484: stur            w0, [x1, #0xf]
    // 0xad8488: ldur            x2, [fp, #-0x30]
    // 0xad848c: StoreField: r1->field_b = r2
    //     0xad848c: stur            w2, [x1, #0xb]
    // 0xad8490: r0 = NShowcase()
    //     0xad8490: bl              #0xad882c  ; AllocateNShowcaseStub -> NShowcase (size=0x28)
    // 0xad8494: mov             x1, x0
    // 0xad8498: ldur            x0, [fp, #-0x28]
    // 0xad849c: stur            x1, [fp, #-0x30]
    // 0xad84a0: StoreField: r1->field_f = r0
    //     0xad84a0: stur            w0, [x1, #0xf]
    // 0xad84a4: ldur            x0, [fp, #-0x20]
    // 0xad84a8: StoreField: r1->field_b = r0
    //     0xad84a8: stur            w0, [x1, #0xb]
    // 0xad84ac: r0 = "Lihat informasi terkait doa yang sedang Kamu baca"
    //     0xad84ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f918] "Lihat informasi terkait doa yang sedang Kamu baca"
    //     0xad84b0: ldr             x0, [x0, #0x918]
    // 0xad84b4: StoreField: r1->field_13 = r0
    //     0xad84b4: stur            w0, [x1, #0x13]
    // 0xad84b8: r0 = 3
    //     0xad84b8: movz            x0, #0x3
    // 0xad84bc: ArrayStore: r1[0] = r0  ; List_8
    //     0xad84bc: stur            x0, [x1, #0x17]
    // 0xad84c0: r2 = 2
    //     0xad84c0: movz            x2, #0x2
    // 0xad84c4: StoreField: r1->field_1f = r2
    //     0xad84c4: stur            x2, [x1, #0x1f]
    // 0xad84c8: r0 = Padding()
    //     0xad84c8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad84cc: mov             x3, x0
    // 0xad84d0: r0 = Instance_EdgeInsets
    //     0xad84d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f920] Obj!EdgeInsets@e12671
    //     0xad84d4: ldr             x0, [x0, #0x920]
    // 0xad84d8: stur            x3, [fp, #-0x20]
    // 0xad84dc: StoreField: r3->field_f = r0
    //     0xad84dc: stur            w0, [x3, #0xf]
    // 0xad84e0: ldur            x1, [fp, #-0x30]
    // 0xad84e4: StoreField: r3->field_b = r1
    //     0xad84e4: stur            w1, [x3, #0xb]
    // 0xad84e8: r1 = Null
    //     0xad84e8: mov             x1, NULL
    // 0xad84ec: r2 = 2
    //     0xad84ec: movz            x2, #0x2
    // 0xad84f0: r0 = AllocateArray()
    //     0xad84f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad84f4: mov             x2, x0
    // 0xad84f8: ldur            x0, [fp, #-0x20]
    // 0xad84fc: stur            x2, [fp, #-0x28]
    // 0xad8500: StoreField: r2->field_f = r0
    //     0xad8500: stur            w0, [x2, #0xf]
    // 0xad8504: r1 = <Widget>
    //     0xad8504: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad8508: r0 = AllocateGrowableArray()
    //     0xad8508: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad850c: mov             x2, x0
    // 0xad8510: ldur            x0, [fp, #-0x28]
    // 0xad8514: stur            x2, [fp, #-0x20]
    // 0xad8518: StoreField: r2->field_f = r0
    //     0xad8518: stur            w0, [x2, #0xf]
    // 0xad851c: r0 = 2
    //     0xad851c: movz            x0, #0x2
    // 0xad8520: StoreField: r2->field_b = r0
    //     0xad8520: stur            w0, [x2, #0xb]
    // 0xad8524: ldur            x0, [fp, #-8]
    // 0xad8528: LoadField: r1 = r0->field_f
    //     0xad8528: ldur            w1, [x0, #0xf]
    // 0xad852c: DecompressPointer r1
    //     0xad852c: add             x1, x1, HEAP, lsl #32
    // 0xad8530: r0 = controller()
    //     0xad8530: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8534: LoadField: r1 = r0->field_3f
    //     0xad8534: ldur            w1, [x0, #0x3f]
    // 0xad8538: DecompressPointer r1
    //     0xad8538: add             x1, x1, HEAP, lsl #32
    // 0xad853c: tbnz            w1, #4, #0xad85e4
    // 0xad8540: ldur            x1, [fp, #-0x20]
    // 0xad8544: r0 = Obx()
    //     0xad8544: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad8548: ldur            x2, [fp, #-0x10]
    // 0xad854c: r1 = Function '<anonymous closure>':.
    //     0xad854c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f928] AnonymousClosure: (0xad90b4), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad8550: ldr             x1, [x1, #0x928]
    // 0xad8554: stur            x0, [fp, #-0x28]
    // 0xad8558: r0 = AllocateClosure()
    //     0xad8558: bl              #0xec1630  ; AllocateClosureStub
    // 0xad855c: mov             x1, x0
    // 0xad8560: ldur            x0, [fp, #-0x28]
    // 0xad8564: StoreField: r0->field_b = r1
    //     0xad8564: stur            w1, [x0, #0xb]
    // 0xad8568: ldur            x2, [fp, #-0x20]
    // 0xad856c: LoadField: r1 = r2->field_b
    //     0xad856c: ldur            w1, [x2, #0xb]
    // 0xad8570: LoadField: r3 = r2->field_f
    //     0xad8570: ldur            w3, [x2, #0xf]
    // 0xad8574: DecompressPointer r3
    //     0xad8574: add             x3, x3, HEAP, lsl #32
    // 0xad8578: LoadField: r4 = r3->field_b
    //     0xad8578: ldur            w4, [x3, #0xb]
    // 0xad857c: r3 = LoadInt32Instr(r1)
    //     0xad857c: sbfx            x3, x1, #1, #0x1f
    // 0xad8580: stur            x3, [fp, #-0x38]
    // 0xad8584: r1 = LoadInt32Instr(r4)
    //     0xad8584: sbfx            x1, x4, #1, #0x1f
    // 0xad8588: cmp             x3, x1
    // 0xad858c: b.ne            #0xad8598
    // 0xad8590: mov             x1, x2
    // 0xad8594: r0 = _growToNextCapacity()
    //     0xad8594: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8598: ldur            x2, [fp, #-0x20]
    // 0xad859c: ldur            x3, [fp, #-0x38]
    // 0xad85a0: add             x0, x3, #1
    // 0xad85a4: lsl             x1, x0, #1
    // 0xad85a8: StoreField: r2->field_b = r1
    //     0xad85a8: stur            w1, [x2, #0xb]
    // 0xad85ac: LoadField: r1 = r2->field_f
    //     0xad85ac: ldur            w1, [x2, #0xf]
    // 0xad85b0: DecompressPointer r1
    //     0xad85b0: add             x1, x1, HEAP, lsl #32
    // 0xad85b4: ldur            x0, [fp, #-0x28]
    // 0xad85b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad85b8: add             x25, x1, x3, lsl #2
    //     0xad85bc: add             x25, x25, #0xf
    //     0xad85c0: str             w0, [x25]
    //     0xad85c4: tbz             w0, #0, #0xad85e0
    //     0xad85c8: ldurb           w16, [x1, #-1]
    //     0xad85cc: ldurb           w17, [x0, #-1]
    //     0xad85d0: and             x16, x17, x16, lsr #2
    //     0xad85d4: tst             x16, HEAP, lsr #32
    //     0xad85d8: b.eq            #0xad85e0
    //     0xad85dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad85e0: b               #0xad85e8
    // 0xad85e4: ldur            x2, [fp, #-0x20]
    // 0xad85e8: ldur            x0, [fp, #-8]
    // 0xad85ec: LoadField: r1 = r0->field_f
    //     0xad85ec: ldur            w1, [x0, #0xf]
    // 0xad85f0: DecompressPointer r1
    //     0xad85f0: add             x1, x1, HEAP, lsl #32
    // 0xad85f4: r0 = controller()
    //     0xad85f4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad85f8: LoadField: r2 = r0->field_1f
    //     0xad85f8: ldur            w2, [x0, #0x1f]
    // 0xad85fc: DecompressPointer r2
    //     0xad85fc: add             x2, x2, HEAP, lsl #32
    // 0xad8600: LoadField: r0 = r2->field_b
    //     0xad8600: ldur            w0, [x2, #0xb]
    // 0xad8604: r1 = LoadInt32Instr(r0)
    //     0xad8604: sbfx            x1, x0, #1, #0x1f
    // 0xad8608: mov             x0, x1
    // 0xad860c: r1 = 0
    //     0xad860c: movz            x1, #0
    // 0xad8610: cmp             x1, x0
    // 0xad8614: b.hs            #0xad8828
    // 0xad8618: LoadField: r0 = r2->field_f
    //     0xad8618: ldur            w0, [x2, #0xf]
    // 0xad861c: DecompressPointer r0
    //     0xad861c: add             x0, x0, HEAP, lsl #32
    // 0xad8620: LoadField: r3 = r0->field_f
    //     0xad8620: ldur            w3, [x0, #0xf]
    // 0xad8624: DecompressPointer r3
    //     0xad8624: add             x3, x3, HEAP, lsl #32
    // 0xad8628: stur            x3, [fp, #-0x28]
    // 0xad862c: r1 = Function '<anonymous closure>':.
    //     0xad862c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f930] AnonymousClosure: (0xad9034), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad8630: ldr             x1, [x1, #0x930]
    // 0xad8634: r2 = Null
    //     0xad8634: mov             x2, NULL
    // 0xad8638: r0 = AllocateClosure()
    //     0xad8638: bl              #0xec1630  ; AllocateClosureStub
    // 0xad863c: stur            x0, [fp, #-0x30]
    // 0xad8640: r0 = IconButton()
    //     0xad8640: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad8644: mov             x1, x0
    // 0xad8648: ldur            x0, [fp, #-0x30]
    // 0xad864c: stur            x1, [fp, #-0x40]
    // 0xad8650: StoreField: r1->field_3b = r0
    //     0xad8650: stur            w0, [x1, #0x3b]
    // 0xad8654: r0 = false
    //     0xad8654: add             x0, NULL, #0x30  ; false
    // 0xad8658: StoreField: r1->field_47 = r0
    //     0xad8658: stur            w0, [x1, #0x47]
    // 0xad865c: r2 = Instance_Icon
    //     0xad865c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f938] Obj!Icon@e24471
    //     0xad8660: ldr             x2, [x2, #0x938]
    // 0xad8664: StoreField: r1->field_1f = r2
    //     0xad8664: stur            w2, [x1, #0x1f]
    // 0xad8668: r2 = Instance__IconButtonVariant
    //     0xad8668: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad866c: ldr             x2, [x2, #0xf78]
    // 0xad8670: StoreField: r1->field_63 = r2
    //     0xad8670: stur            w2, [x1, #0x63]
    // 0xad8674: r0 = SizedBox()
    //     0xad8674: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad8678: mov             x1, x0
    // 0xad867c: r0 = 42.000000
    //     0xad867c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2be80] 42
    //     0xad8680: ldr             x0, [x0, #0xe80]
    // 0xad8684: stur            x1, [fp, #-0x30]
    // 0xad8688: StoreField: r1->field_f = r0
    //     0xad8688: stur            w0, [x1, #0xf]
    // 0xad868c: ldur            x0, [fp, #-0x40]
    // 0xad8690: StoreField: r1->field_b = r0
    //     0xad8690: stur            w0, [x1, #0xb]
    // 0xad8694: r0 = NShowcase()
    //     0xad8694: bl              #0xad882c  ; AllocateNShowcaseStub -> NShowcase (size=0x28)
    // 0xad8698: mov             x1, x0
    // 0xad869c: ldur            x0, [fp, #-0x30]
    // 0xad86a0: stur            x1, [fp, #-0x40]
    // 0xad86a4: StoreField: r1->field_f = r0
    //     0xad86a4: stur            w0, [x1, #0xf]
    // 0xad86a8: ldur            x0, [fp, #-0x28]
    // 0xad86ac: StoreField: r1->field_b = r0
    //     0xad86ac: stur            w0, [x1, #0xb]
    // 0xad86b0: r0 = "Atur ukuran teks serta opsi untuk menampilkan terjemah & teks latin"
    //     0xad86b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f940] "Atur ukuran teks serta opsi untuk menampilkan terjemah & teks latin"
    //     0xad86b4: ldr             x0, [x0, #0x940]
    // 0xad86b8: StoreField: r1->field_13 = r0
    //     0xad86b8: stur            w0, [x1, #0x13]
    // 0xad86bc: r0 = 3
    //     0xad86bc: movz            x0, #0x3
    // 0xad86c0: ArrayStore: r1[0] = r0  ; List_8
    //     0xad86c0: stur            x0, [x1, #0x17]
    // 0xad86c4: StoreField: r1->field_1f = rZR
    //     0xad86c4: stur            xzr, [x1, #0x1f]
    // 0xad86c8: r0 = Padding()
    //     0xad86c8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad86cc: mov             x2, x0
    // 0xad86d0: r0 = Instance_EdgeInsets
    //     0xad86d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f920] Obj!EdgeInsets@e12671
    //     0xad86d4: ldr             x0, [x0, #0x920]
    // 0xad86d8: stur            x2, [fp, #-0x28]
    // 0xad86dc: StoreField: r2->field_f = r0
    //     0xad86dc: stur            w0, [x2, #0xf]
    // 0xad86e0: ldur            x0, [fp, #-0x40]
    // 0xad86e4: StoreField: r2->field_b = r0
    //     0xad86e4: stur            w0, [x2, #0xb]
    // 0xad86e8: ldur            x0, [fp, #-0x20]
    // 0xad86ec: LoadField: r1 = r0->field_b
    //     0xad86ec: ldur            w1, [x0, #0xb]
    // 0xad86f0: LoadField: r3 = r0->field_f
    //     0xad86f0: ldur            w3, [x0, #0xf]
    // 0xad86f4: DecompressPointer r3
    //     0xad86f4: add             x3, x3, HEAP, lsl #32
    // 0xad86f8: LoadField: r4 = r3->field_b
    //     0xad86f8: ldur            w4, [x3, #0xb]
    // 0xad86fc: r3 = LoadInt32Instr(r1)
    //     0xad86fc: sbfx            x3, x1, #1, #0x1f
    // 0xad8700: stur            x3, [fp, #-0x38]
    // 0xad8704: r1 = LoadInt32Instr(r4)
    //     0xad8704: sbfx            x1, x4, #1, #0x1f
    // 0xad8708: cmp             x3, x1
    // 0xad870c: b.ne            #0xad8718
    // 0xad8710: mov             x1, x0
    // 0xad8714: r0 = _growToNextCapacity()
    //     0xad8714: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8718: ldur            x4, [fp, #-8]
    // 0xad871c: ldur            x2, [fp, #-0x20]
    // 0xad8720: ldur            x3, [fp, #-0x38]
    // 0xad8724: add             x0, x3, #1
    // 0xad8728: lsl             x1, x0, #1
    // 0xad872c: StoreField: r2->field_b = r1
    //     0xad872c: stur            w1, [x2, #0xb]
    // 0xad8730: LoadField: r1 = r2->field_f
    //     0xad8730: ldur            w1, [x2, #0xf]
    // 0xad8734: DecompressPointer r1
    //     0xad8734: add             x1, x1, HEAP, lsl #32
    // 0xad8738: ldur            x0, [fp, #-0x28]
    // 0xad873c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad873c: add             x25, x1, x3, lsl #2
    //     0xad8740: add             x25, x25, #0xf
    //     0xad8744: str             w0, [x25]
    //     0xad8748: tbz             w0, #0, #0xad8764
    //     0xad874c: ldurb           w16, [x1, #-1]
    //     0xad8750: ldurb           w17, [x0, #-1]
    //     0xad8754: and             x16, x17, x16, lsr #2
    //     0xad8758: tst             x16, HEAP, lsr #32
    //     0xad875c: b.eq            #0xad8764
    //     0xad8760: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad8764: r0 = AppBar()
    //     0xad8764: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad8768: stur            x0, [fp, #-0x28]
    // 0xad876c: ldur            x16, [fp, #-0x18]
    // 0xad8770: ldur            lr, [fp, #-0x20]
    // 0xad8774: stp             lr, x16, [SP]
    // 0xad8778: mov             x1, x0
    // 0xad877c: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xad877c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xad8780: ldr             x4, [x4, #0xf88]
    // 0xad8784: r0 = AppBar()
    //     0xad8784: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad8788: ldur            x0, [fp, #-8]
    // 0xad878c: LoadField: r1 = r0->field_f
    //     0xad878c: ldur            w1, [x0, #0xf]
    // 0xad8790: DecompressPointer r1
    //     0xad8790: add             x1, x1, HEAP, lsl #32
    // 0xad8794: r0 = controller()
    //     0xad8794: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8798: ldur            x2, [fp, #-0x10]
    // 0xad879c: r1 = Function '<anonymous closure>':.
    //     0xad879c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f948] AnonymousClosure: (0xad8838), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad87a0: ldr             x1, [x1, #0x948]
    // 0xad87a4: stur            x0, [fp, #-8]
    // 0xad87a8: r0 = AllocateClosure()
    //     0xad87a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad87ac: r16 = <List<DoaSubCategory>>
    //     0xad87ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xad87b0: ldr             x16, [x16, #0x7d8]
    // 0xad87b4: ldur            lr, [fp, #-8]
    // 0xad87b8: stp             lr, x16, [SP, #8]
    // 0xad87bc: str             x0, [SP]
    // 0xad87c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad87c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad87c4: r0 = StateExt.obx()
    //     0xad87c4: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xad87c8: stur            x0, [fp, #-8]
    // 0xad87cc: r0 = Scaffold()
    //     0xad87cc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad87d0: ldur            x1, [fp, #-0x28]
    // 0xad87d4: StoreField: r0->field_13 = r1
    //     0xad87d4: stur            w1, [x0, #0x13]
    // 0xad87d8: ldur            x1, [fp, #-8]
    // 0xad87dc: ArrayStore: r0[0] = r1  ; List_4
    //     0xad87dc: stur            w1, [x0, #0x17]
    // 0xad87e0: r1 = Instance_AlignmentDirectional
    //     0xad87e0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad87e4: ldr             x1, [x1, #0x758]
    // 0xad87e8: StoreField: r0->field_2b = r1
    //     0xad87e8: stur            w1, [x0, #0x2b]
    // 0xad87ec: r1 = true
    //     0xad87ec: add             x1, NULL, #0x20  ; true
    // 0xad87f0: StoreField: r0->field_53 = r1
    //     0xad87f0: stur            w1, [x0, #0x53]
    // 0xad87f4: r2 = Instance_DragStartBehavior
    //     0xad87f4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad87f8: StoreField: r0->field_57 = r2
    //     0xad87f8: stur            w2, [x0, #0x57]
    // 0xad87fc: r2 = false
    //     0xad87fc: add             x2, NULL, #0x30  ; false
    // 0xad8800: StoreField: r0->field_b = r2
    //     0xad8800: stur            w2, [x0, #0xb]
    // 0xad8804: StoreField: r0->field_f = r2
    //     0xad8804: stur            w2, [x0, #0xf]
    // 0xad8808: StoreField: r0->field_5f = r1
    //     0xad8808: stur            w1, [x0, #0x5f]
    // 0xad880c: StoreField: r0->field_63 = r1
    //     0xad880c: stur            w1, [x0, #0x63]
    // 0xad8810: LeaveFrame
    //     0xad8810: mov             SP, fp
    //     0xad8814: ldp             fp, lr, [SP], #0x10
    // 0xad8818: ret
    //     0xad8818: ret             
    // 0xad881c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad881c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8820: b               #0xad8368
    // 0xad8824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad8824: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad8828: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad8828: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, List<DoaSubCategory>?) {
    // ** addr: 0xad8838, size: 0x2c4
    // 0xad8838: EnterFrame
    //     0xad8838: stp             fp, lr, [SP, #-0x10]!
    //     0xad883c: mov             fp, SP
    // 0xad8840: AllocStack(0x48)
    //     0xad8840: sub             SP, SP, #0x48
    // 0xad8844: SetupParameters()
    //     0xad8844: ldr             x0, [fp, #0x18]
    //     0xad8848: ldur            w1, [x0, #0x17]
    //     0xad884c: add             x1, x1, HEAP, lsl #32
    //     0xad8850: stur            x1, [fp, #-8]
    // 0xad8854: CheckStackOverflow
    //     0xad8854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8858: cmp             SP, x16
    //     0xad885c: b.ls            #0xad8af0
    // 0xad8860: r1 = 1
    //     0xad8860: movz            x1, #0x1
    // 0xad8864: r0 = AllocateContext()
    //     0xad8864: bl              #0xec126c  ; AllocateContextStub
    // 0xad8868: mov             x3, x0
    // 0xad886c: ldur            x0, [fp, #-8]
    // 0xad8870: stur            x3, [fp, #-0x10]
    // 0xad8874: StoreField: r3->field_b = r0
    //     0xad8874: stur            w0, [x3, #0xb]
    // 0xad8878: ldr             x1, [fp, #0x10]
    // 0xad887c: StoreField: r3->field_f = r1
    //     0xad887c: stur            w1, [x3, #0xf]
    // 0xad8880: r1 = <Widget>
    //     0xad8880: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad8884: r2 = 0
    //     0xad8884: movz            x2, #0
    // 0xad8888: r0 = _GrowableList()
    //     0xad8888: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xad888c: mov             x2, x0
    // 0xad8890: ldur            x0, [fp, #-8]
    // 0xad8894: stur            x2, [fp, #-0x20]
    // 0xad8898: LoadField: r3 = r0->field_b
    //     0xad8898: ldur            w3, [x0, #0xb]
    // 0xad889c: DecompressPointer r3
    //     0xad889c: add             x3, x3, HEAP, lsl #32
    // 0xad88a0: stur            x3, [fp, #-0x18]
    // 0xad88a4: LoadField: r1 = r3->field_f
    //     0xad88a4: ldur            w1, [x3, #0xf]
    // 0xad88a8: DecompressPointer r1
    //     0xad88a8: add             x1, x1, HEAP, lsl #32
    // 0xad88ac: r0 = controller()
    //     0xad88ac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad88b0: mov             x1, x0
    // 0xad88b4: r0 = showPageViewNavigation()
    //     0xad88b4: bl              #0xad8afc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::showPageViewNavigation
    // 0xad88b8: tbnz            w0, #4, #0xad8930
    // 0xad88bc: r0 = Obx()
    //     0xad88bc: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad88c0: ldur            x2, [fp, #-0x10]
    // 0xad88c4: r1 = Function '<anonymous closure>':.
    //     0xad88c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] AnonymousClosure: (0xad8d2c), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad88c8: ldr             x1, [x1, #0x950]
    // 0xad88cc: stur            x0, [fp, #-8]
    // 0xad88d0: r0 = AllocateClosure()
    //     0xad88d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad88d4: mov             x1, x0
    // 0xad88d8: ldur            x0, [fp, #-8]
    // 0xad88dc: StoreField: r0->field_b = r1
    //     0xad88dc: stur            w1, [x0, #0xb]
    // 0xad88e0: r1 = Null
    //     0xad88e0: mov             x1, NULL
    // 0xad88e4: r2 = 4
    //     0xad88e4: movz            x2, #0x4
    // 0xad88e8: r0 = AllocateArray()
    //     0xad88e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad88ec: mov             x2, x0
    // 0xad88f0: ldur            x0, [fp, #-8]
    // 0xad88f4: stur            x2, [fp, #-0x28]
    // 0xad88f8: StoreField: r2->field_f = r0
    //     0xad88f8: stur            w0, [x2, #0xf]
    // 0xad88fc: r16 = Instance_Divider
    //     0xad88fc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xad8900: ldr             x16, [x16, #0xc28]
    // 0xad8904: StoreField: r2->field_13 = r16
    //     0xad8904: stur            w16, [x2, #0x13]
    // 0xad8908: r1 = <Widget>
    //     0xad8908: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad890c: r0 = AllocateGrowableArray()
    //     0xad890c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad8910: mov             x1, x0
    // 0xad8914: ldur            x0, [fp, #-0x28]
    // 0xad8918: StoreField: r1->field_f = r0
    //     0xad8918: stur            w0, [x1, #0xf]
    // 0xad891c: r0 = 4
    //     0xad891c: movz            x0, #0x4
    // 0xad8920: StoreField: r1->field_b = r0
    //     0xad8920: stur            w0, [x1, #0xb]
    // 0xad8924: mov             x2, x1
    // 0xad8928: ldur            x1, [fp, #-0x20]
    // 0xad892c: r0 = addAll()
    //     0xad892c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xad8930: ldur            x2, [fp, #-0x10]
    // 0xad8934: ldur            x0, [fp, #-0x20]
    // 0xad8938: ldur            x1, [fp, #-0x18]
    // 0xad893c: LoadField: r3 = r1->field_f
    //     0xad893c: ldur            w3, [x1, #0xf]
    // 0xad8940: DecompressPointer r3
    //     0xad8940: add             x3, x3, HEAP, lsl #32
    // 0xad8944: mov             x1, x3
    // 0xad8948: r0 = controller()
    //     0xad8948: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad894c: LoadField: r2 = r0->field_2f
    //     0xad894c: ldur            w2, [x0, #0x2f]
    // 0xad8950: DecompressPointer r2
    //     0xad8950: add             x2, x2, HEAP, lsl #32
    // 0xad8954: ldur            x1, [fp, #-0x10]
    // 0xad8958: stur            x2, [fp, #-8]
    // 0xad895c: LoadField: r0 = r1->field_f
    //     0xad895c: ldur            w0, [x1, #0xf]
    // 0xad8960: DecompressPointer r0
    //     0xad8960: add             x0, x0, HEAP, lsl #32
    // 0xad8964: cmp             w0, NULL
    // 0xad8968: b.eq            #0xad8af8
    // 0xad896c: r3 = LoadClassIdInstr(r0)
    //     0xad896c: ldur            x3, [x0, #-1]
    //     0xad8970: ubfx            x3, x3, #0xc, #0x14
    // 0xad8974: str             x0, [SP]
    // 0xad8978: mov             x0, x3
    // 0xad897c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad897c: movz            x17, #0xc834
    //     0xad8980: add             lr, x0, x17
    //     0xad8984: ldr             lr, [x21, lr, lsl #3]
    //     0xad8988: blr             lr
    // 0xad898c: ldur            x2, [fp, #-0x10]
    // 0xad8990: r1 = Function '<anonymous closure>':.
    //     0xad8990: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f958] AnonymousClosure: (0xad8c54), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad8994: ldr             x1, [x1, #0x958]
    // 0xad8998: stur            x0, [fp, #-0x18]
    // 0xad899c: r0 = AllocateClosure()
    //     0xad899c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad89a0: ldur            x2, [fp, #-0x10]
    // 0xad89a4: r1 = Function '<anonymous closure>':.
    //     0xad89a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f960] AnonymousClosure: (0xad8bc0), in [package:nuonline/app/modules/doa/doa_detail/views/doa_detail_view.dart] DoaDetailView::build (0xad81cc)
    //     0xad89a8: ldr             x1, [x1, #0x960]
    // 0xad89ac: stur            x0, [fp, #-0x10]
    // 0xad89b0: r0 = AllocateClosure()
    //     0xad89b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad89b4: stur            x0, [fp, #-0x28]
    // 0xad89b8: r0 = PageView()
    //     0xad89b8: bl              #0x9d332c  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xad89bc: stur            x0, [fp, #-0x30]
    // 0xad89c0: r16 = true
    //     0xad89c0: add             x16, NULL, #0x20  ; true
    // 0xad89c4: ldur            lr, [fp, #-0x18]
    // 0xad89c8: stp             lr, x16, [SP]
    // 0xad89cc: mov             x1, x0
    // 0xad89d0: ldur            x2, [fp, #-8]
    // 0xad89d4: ldur            x3, [fp, #-0x28]
    // 0xad89d8: ldur            x5, [fp, #-0x10]
    // 0xad89dc: r4 = const [0, 0x6, 0x2, 0x4, itemCount, 0x5, reverse, 0x4, null]
    //     0xad89dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "itemCount", 0x5, "reverse", 0x4, Null]
    //     0xad89e0: ldr             x4, [x4, #0x968]
    // 0xad89e4: r0 = PageView.builder()
    //     0xad89e4: bl              #0x9d3014  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xad89e8: r1 = <FlexParentData>
    //     0xad89e8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad89ec: ldr             x1, [x1, #0x720]
    // 0xad89f0: r0 = Expanded()
    //     0xad89f0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad89f4: mov             x2, x0
    // 0xad89f8: r0 = 1
    //     0xad89f8: movz            x0, #0x1
    // 0xad89fc: stur            x2, [fp, #-8]
    // 0xad8a00: StoreField: r2->field_13 = r0
    //     0xad8a00: stur            x0, [x2, #0x13]
    // 0xad8a04: r0 = Instance_FlexFit
    //     0xad8a04: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad8a08: ldr             x0, [x0, #0x728]
    // 0xad8a0c: StoreField: r2->field_1b = r0
    //     0xad8a0c: stur            w0, [x2, #0x1b]
    // 0xad8a10: ldur            x0, [fp, #-0x30]
    // 0xad8a14: StoreField: r2->field_b = r0
    //     0xad8a14: stur            w0, [x2, #0xb]
    // 0xad8a18: ldur            x0, [fp, #-0x20]
    // 0xad8a1c: LoadField: r1 = r0->field_b
    //     0xad8a1c: ldur            w1, [x0, #0xb]
    // 0xad8a20: LoadField: r3 = r0->field_f
    //     0xad8a20: ldur            w3, [x0, #0xf]
    // 0xad8a24: DecompressPointer r3
    //     0xad8a24: add             x3, x3, HEAP, lsl #32
    // 0xad8a28: LoadField: r4 = r3->field_b
    //     0xad8a28: ldur            w4, [x3, #0xb]
    // 0xad8a2c: r3 = LoadInt32Instr(r1)
    //     0xad8a2c: sbfx            x3, x1, #1, #0x1f
    // 0xad8a30: stur            x3, [fp, #-0x38]
    // 0xad8a34: r1 = LoadInt32Instr(r4)
    //     0xad8a34: sbfx            x1, x4, #1, #0x1f
    // 0xad8a38: cmp             x3, x1
    // 0xad8a3c: b.ne            #0xad8a48
    // 0xad8a40: mov             x1, x0
    // 0xad8a44: r0 = _growToNextCapacity()
    //     0xad8a44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8a48: ldur            x2, [fp, #-0x20]
    // 0xad8a4c: ldur            x3, [fp, #-0x38]
    // 0xad8a50: add             x0, x3, #1
    // 0xad8a54: lsl             x1, x0, #1
    // 0xad8a58: StoreField: r2->field_b = r1
    //     0xad8a58: stur            w1, [x2, #0xb]
    // 0xad8a5c: LoadField: r1 = r2->field_f
    //     0xad8a5c: ldur            w1, [x2, #0xf]
    // 0xad8a60: DecompressPointer r1
    //     0xad8a60: add             x1, x1, HEAP, lsl #32
    // 0xad8a64: ldur            x0, [fp, #-8]
    // 0xad8a68: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad8a68: add             x25, x1, x3, lsl #2
    //     0xad8a6c: add             x25, x25, #0xf
    //     0xad8a70: str             w0, [x25]
    //     0xad8a74: tbz             w0, #0, #0xad8a90
    //     0xad8a78: ldurb           w16, [x1, #-1]
    //     0xad8a7c: ldurb           w17, [x0, #-1]
    //     0xad8a80: and             x16, x17, x16, lsr #2
    //     0xad8a84: tst             x16, HEAP, lsr #32
    //     0xad8a88: b.eq            #0xad8a90
    //     0xad8a8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad8a90: r0 = Column()
    //     0xad8a90: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad8a94: r1 = Instance_Axis
    //     0xad8a94: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad8a98: StoreField: r0->field_f = r1
    //     0xad8a98: stur            w1, [x0, #0xf]
    // 0xad8a9c: r1 = Instance_MainAxisAlignment
    //     0xad8a9c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad8aa0: ldr             x1, [x1, #0x730]
    // 0xad8aa4: StoreField: r0->field_13 = r1
    //     0xad8aa4: stur            w1, [x0, #0x13]
    // 0xad8aa8: r1 = Instance_MainAxisSize
    //     0xad8aa8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad8aac: ldr             x1, [x1, #0x738]
    // 0xad8ab0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad8ab0: stur            w1, [x0, #0x17]
    // 0xad8ab4: r1 = Instance_CrossAxisAlignment
    //     0xad8ab4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad8ab8: ldr             x1, [x1, #0x740]
    // 0xad8abc: StoreField: r0->field_1b = r1
    //     0xad8abc: stur            w1, [x0, #0x1b]
    // 0xad8ac0: r1 = Instance_VerticalDirection
    //     0xad8ac0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad8ac4: ldr             x1, [x1, #0x748]
    // 0xad8ac8: StoreField: r0->field_23 = r1
    //     0xad8ac8: stur            w1, [x0, #0x23]
    // 0xad8acc: r1 = Instance_Clip
    //     0xad8acc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad8ad0: ldr             x1, [x1, #0x750]
    // 0xad8ad4: StoreField: r0->field_2b = r1
    //     0xad8ad4: stur            w1, [x0, #0x2b]
    // 0xad8ad8: StoreField: r0->field_2f = rZR
    //     0xad8ad8: stur            xzr, [x0, #0x2f]
    // 0xad8adc: ldur            x1, [fp, #-0x20]
    // 0xad8ae0: StoreField: r0->field_b = r1
    //     0xad8ae0: stur            w1, [x0, #0xb]
    // 0xad8ae4: LeaveFrame
    //     0xad8ae4: mov             SP, fp
    //     0xad8ae8: ldp             fp, lr, [SP], #0x10
    // 0xad8aec: ret
    //     0xad8aec: ret             
    // 0xad8af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8af4: b               #0xad8860
    // 0xad8af8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad8af8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] DoaDetailBuilder <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad8bc0, size: 0x88
    // 0xad8bc0: EnterFrame
    //     0xad8bc0: stp             fp, lr, [SP, #-0x10]!
    //     0xad8bc4: mov             fp, SP
    // 0xad8bc8: AllocStack(0x8)
    //     0xad8bc8: sub             SP, SP, #8
    // 0xad8bcc: SetupParameters()
    //     0xad8bcc: ldr             x0, [fp, #0x20]
    //     0xad8bd0: ldur            w1, [x0, #0x17]
    //     0xad8bd4: add             x1, x1, HEAP, lsl #32
    // 0xad8bd8: CheckStackOverflow
    //     0xad8bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8bdc: cmp             SP, x16
    //     0xad8be0: b.ls            #0xad8c40
    // 0xad8be4: LoadField: r0 = r1->field_f
    //     0xad8be4: ldur            w0, [x1, #0xf]
    // 0xad8be8: DecompressPointer r0
    //     0xad8be8: add             x0, x0, HEAP, lsl #32
    // 0xad8bec: ldr             x1, [fp, #0x10]
    // 0xad8bf0: r2 = LoadInt32Instr(r1)
    //     0xad8bf0: sbfx            x2, x1, #1, #0x1f
    //     0xad8bf4: tbz             w1, #0, #0xad8bfc
    //     0xad8bf8: ldur            x2, [x1, #7]
    // 0xad8bfc: r1 = LoadClassIdInstr(r0)
    //     0xad8bfc: ldur            x1, [x0, #-1]
    //     0xad8c00: ubfx            x1, x1, #0xc, #0x14
    // 0xad8c04: mov             x16, x0
    // 0xad8c08: mov             x0, x1
    // 0xad8c0c: mov             x1, x16
    // 0xad8c10: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xad8c10: movz            x17, #0xd28f
    //     0xad8c14: add             lr, x0, x17
    //     0xad8c18: ldr             lr, [x21, lr, lsl #3]
    //     0xad8c1c: blr             lr
    // 0xad8c20: LoadField: r1 = r0->field_7
    //     0xad8c20: ldur            x1, [x0, #7]
    // 0xad8c24: stur            x1, [fp, #-8]
    // 0xad8c28: r0 = DoaDetailBuilder()
    //     0xad8c28: bl              #0xad8c48  ; AllocateDoaDetailBuilderStub -> DoaDetailBuilder (size=0x14)
    // 0xad8c2c: ldur            x1, [fp, #-8]
    // 0xad8c30: StoreField: r0->field_b = r1
    //     0xad8c30: stur            x1, [x0, #0xb]
    // 0xad8c34: LeaveFrame
    //     0xad8c34: mov             SP, fp
    //     0xad8c38: ldp             fp, lr, [SP], #0x10
    // 0xad8c3c: ret
    //     0xad8c3c: ret             
    // 0xad8c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8c40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8c44: b               #0xad8be4
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xad8c54, size: 0x78
    // 0xad8c54: EnterFrame
    //     0xad8c54: stp             fp, lr, [SP, #-0x10]!
    //     0xad8c58: mov             fp, SP
    // 0xad8c5c: ldr             x0, [fp, #0x18]
    // 0xad8c60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad8c60: ldur            w1, [x0, #0x17]
    // 0xad8c64: DecompressPointer r1
    //     0xad8c64: add             x1, x1, HEAP, lsl #32
    // 0xad8c68: CheckStackOverflow
    //     0xad8c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8c6c: cmp             SP, x16
    //     0xad8c70: b.ls            #0xad8cc4
    // 0xad8c74: LoadField: r0 = r1->field_b
    //     0xad8c74: ldur            w0, [x1, #0xb]
    // 0xad8c78: DecompressPointer r0
    //     0xad8c78: add             x0, x0, HEAP, lsl #32
    // 0xad8c7c: LoadField: r1 = r0->field_b
    //     0xad8c7c: ldur            w1, [x0, #0xb]
    // 0xad8c80: DecompressPointer r1
    //     0xad8c80: add             x1, x1, HEAP, lsl #32
    // 0xad8c84: LoadField: r0 = r1->field_f
    //     0xad8c84: ldur            w0, [x1, #0xf]
    // 0xad8c88: DecompressPointer r0
    //     0xad8c88: add             x0, x0, HEAP, lsl #32
    // 0xad8c8c: mov             x1, x0
    // 0xad8c90: r0 = controller()
    //     0xad8c90: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8c94: mov             x1, x0
    // 0xad8c98: ldr             x0, [fp, #0x10]
    // 0xad8c9c: r2 = LoadInt32Instr(r0)
    //     0xad8c9c: sbfx            x2, x0, #1, #0x1f
    //     0xad8ca0: tbz             w0, #0, #0xad8ca8
    //     0xad8ca4: ldur            x2, [x0, #7]
    // 0xad8ca8: add             x0, x2, #1
    // 0xad8cac: mov             x2, x0
    // 0xad8cb0: r0 = currentPageView=()
    //     0xad8cb0: bl              #0xad8ccc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::currentPageView=
    // 0xad8cb4: r0 = Null
    //     0xad8cb4: mov             x0, NULL
    // 0xad8cb8: LeaveFrame
    //     0xad8cb8: mov             SP, fp
    //     0xad8cbc: ldp             fp, lr, [SP], #0x10
    // 0xad8cc0: ret
    //     0xad8cc0: ret             
    // 0xad8cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8cc8: b               #0xad8c74
  }
  [closure] NSwipeNavigation <anonymous closure>(dynamic) {
    // ** addr: 0xad8d2c, size: 0x150
    // 0xad8d2c: EnterFrame
    //     0xad8d2c: stp             fp, lr, [SP, #-0x10]!
    //     0xad8d30: mov             fp, SP
    // 0xad8d34: AllocStack(0x30)
    //     0xad8d34: sub             SP, SP, #0x30
    // 0xad8d38: SetupParameters()
    //     0xad8d38: ldr             x0, [fp, #0x10]
    //     0xad8d3c: ldur            w1, [x0, #0x17]
    //     0xad8d40: add             x1, x1, HEAP, lsl #32
    // 0xad8d44: CheckStackOverflow
    //     0xad8d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8d48: cmp             SP, x16
    //     0xad8d4c: b.ls            #0xad8e70
    // 0xad8d50: LoadField: r0 = r1->field_b
    //     0xad8d50: ldur            w0, [x1, #0xb]
    // 0xad8d54: DecompressPointer r0
    //     0xad8d54: add             x0, x0, HEAP, lsl #32
    // 0xad8d58: LoadField: r2 = r0->field_b
    //     0xad8d58: ldur            w2, [x0, #0xb]
    // 0xad8d5c: DecompressPointer r2
    //     0xad8d5c: add             x2, x2, HEAP, lsl #32
    // 0xad8d60: stur            x2, [fp, #-8]
    // 0xad8d64: LoadField: r1 = r2->field_f
    //     0xad8d64: ldur            w1, [x2, #0xf]
    // 0xad8d68: DecompressPointer r1
    //     0xad8d68: add             x1, x1, HEAP, lsl #32
    // 0xad8d6c: r0 = controller()
    //     0xad8d6c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8d70: mov             x1, x0
    // 0xad8d74: r0 = currentItem()
    //     0xad8d74: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0xad8d78: cmp             w0, NULL
    // 0xad8d7c: b.eq            #0xad8e78
    // 0xad8d80: LoadField: r2 = r0->field_f
    //     0xad8d80: ldur            w2, [x0, #0xf]
    // 0xad8d84: DecompressPointer r2
    //     0xad8d84: add             x2, x2, HEAP, lsl #32
    // 0xad8d88: ldur            x0, [fp, #-8]
    // 0xad8d8c: stur            x2, [fp, #-0x10]
    // 0xad8d90: LoadField: r1 = r0->field_f
    //     0xad8d90: ldur            w1, [x0, #0xf]
    // 0xad8d94: DecompressPointer r1
    //     0xad8d94: add             x1, x1, HEAP, lsl #32
    // 0xad8d98: r0 = controller()
    //     0xad8d98: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8d9c: mov             x1, x0
    // 0xad8da0: r0 = currentPageView()
    //     0xad8da0: bl              #0x8f10fc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::currentPageView
    // 0xad8da4: mov             x2, x0
    // 0xad8da8: ldur            x0, [fp, #-8]
    // 0xad8dac: stur            x2, [fp, #-0x18]
    // 0xad8db0: LoadField: r1 = r0->field_f
    //     0xad8db0: ldur            w1, [x0, #0xf]
    // 0xad8db4: DecompressPointer r1
    //     0xad8db4: add             x1, x1, HEAP, lsl #32
    // 0xad8db8: r0 = controller()
    //     0xad8db8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8dbc: mov             x1, x0
    // 0xad8dc0: r0 = maxPageView()
    //     0xad8dc0: bl              #0xad8b3c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::maxPageView
    // 0xad8dc4: mov             x2, x0
    // 0xad8dc8: ldur            x0, [fp, #-8]
    // 0xad8dcc: stur            x2, [fp, #-0x20]
    // 0xad8dd0: LoadField: r1 = r0->field_f
    //     0xad8dd0: ldur            w1, [x0, #0xf]
    // 0xad8dd4: DecompressPointer r1
    //     0xad8dd4: add             x1, x1, HEAP, lsl #32
    // 0xad8dd8: r0 = controller()
    //     0xad8dd8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8ddc: mov             x2, x0
    // 0xad8de0: ldur            x0, [fp, #-8]
    // 0xad8de4: stur            x2, [fp, #-0x28]
    // 0xad8de8: LoadField: r1 = r0->field_f
    //     0xad8de8: ldur            w1, [x0, #0xf]
    // 0xad8dec: DecompressPointer r1
    //     0xad8dec: add             x1, x1, HEAP, lsl #32
    // 0xad8df0: r0 = controller()
    //     0xad8df0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad8df4: stur            x0, [fp, #-8]
    // 0xad8df8: r0 = NSwipeNavigation()
    //     0xad8df8: bl              #0xad8e7c  ; AllocateNSwipeNavigationStub -> NSwipeNavigation (size=0x34)
    // 0xad8dfc: mov             x3, x0
    // 0xad8e00: ldur            x0, [fp, #-0x18]
    // 0xad8e04: stur            x3, [fp, #-0x30]
    // 0xad8e08: StoreField: r3->field_13 = r0
    //     0xad8e08: stur            x0, [x3, #0x13]
    // 0xad8e0c: ldur            x0, [fp, #-0x20]
    // 0xad8e10: StoreField: r3->field_1b = r0
    //     0xad8e10: stur            x0, [x3, #0x1b]
    // 0xad8e14: ldur            x2, [fp, #-0x28]
    // 0xad8e18: r1 = Function 'nextPageView':.
    //     0xad8e18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f978] AnonymousClosure: (0xad8f50), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::nextPageView (0xad8f88)
    //     0xad8e1c: ldr             x1, [x1, #0x978]
    // 0xad8e20: r0 = AllocateClosure()
    //     0xad8e20: bl              #0xec1630  ; AllocateClosureStub
    // 0xad8e24: mov             x1, x0
    // 0xad8e28: ldur            x0, [fp, #-0x30]
    // 0xad8e2c: StoreField: r0->field_b = r1
    //     0xad8e2c: stur            w1, [x0, #0xb]
    // 0xad8e30: ldur            x2, [fp, #-8]
    // 0xad8e34: r1 = Function 'prevPageView':.
    //     0xad8e34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] AnonymousClosure: (0xad8e88), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] _DoaDetailController&GetxController&ShowCaseMixin&StateMixin&OfflineMixin&OnlineSyncMixin&RtlPageViewMixin::prevPageView (0xad8ec0)
    //     0xad8e38: ldr             x1, [x1, #0x980]
    // 0xad8e3c: r0 = AllocateClosure()
    //     0xad8e3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad8e40: mov             x1, x0
    // 0xad8e44: ldur            x0, [fp, #-0x30]
    // 0xad8e48: StoreField: r0->field_f = r1
    //     0xad8e48: stur            w1, [x0, #0xf]
    // 0xad8e4c: r1 = ""
    //     0xad8e4c: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xad8e50: StoreField: r0->field_23 = r1
    //     0xad8e50: stur            w1, [x0, #0x23]
    // 0xad8e54: ldur            x1, [fp, #-0x10]
    // 0xad8e58: StoreField: r0->field_27 = r1
    //     0xad8e58: stur            w1, [x0, #0x27]
    // 0xad8e5c: r1 = false
    //     0xad8e5c: add             x1, NULL, #0x30  ; false
    // 0xad8e60: StoreField: r0->field_2b = r1
    //     0xad8e60: stur            w1, [x0, #0x2b]
    // 0xad8e64: LeaveFrame
    //     0xad8e64: mov             SP, fp
    //     0xad8e68: ldp             fp, lr, [SP], #0x10
    // 0xad8e6c: ret
    //     0xad8e6c: ret             
    // 0xad8e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8e74: b               #0xad8d50
    // 0xad8e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad8e78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad9034, size: 0x80
    // 0xad9034: EnterFrame
    //     0xad9034: stp             fp, lr, [SP, #-0x10]!
    //     0xad9038: mov             fp, SP
    // 0xad903c: AllocStack(0x18)
    //     0xad903c: sub             SP, SP, #0x18
    // 0xad9040: CheckStackOverflow
    //     0xad9040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9044: cmp             SP, x16
    //     0xad9048: b.ls            #0xad90ac
    // 0xad904c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad904c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad9050: ldr             x0, [x0, #0x2670]
    //     0xad9054: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad9058: cmp             w0, w16
    //     0xad905c: b.ne            #0xad9068
    //     0xad9060: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad9064: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad9068: r0 = ReadingPreferenceArgs()
    //     0xad9068: bl              #0x8425e8  ; AllocateReadingPreferenceArgsStub -> ReadingPreferenceArgs (size=0x14)
    // 0xad906c: mov             x1, x0
    // 0xad9070: r0 = false
    //     0xad9070: add             x0, NULL, #0x30  ; false
    // 0xad9074: StoreField: r1->field_7 = r0
    //     0xad9074: stur            w0, [x1, #7]
    // 0xad9078: StoreField: r1->field_b = r0
    //     0xad9078: stur            w0, [x1, #0xb]
    // 0xad907c: r0 = true
    //     0xad907c: add             x0, NULL, #0x20  ; true
    // 0xad9080: StoreField: r1->field_f = r0
    //     0xad9080: stur            w0, [x1, #0xf]
    // 0xad9084: r16 = "/setting/reading-preference"
    //     0xad9084: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ca0] "/setting/reading-preference"
    //     0xad9088: ldr             x16, [x16, #0xca0]
    // 0xad908c: stp             x16, NULL, [SP, #8]
    // 0xad9090: str             x1, [SP]
    // 0xad9094: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad9094: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad9098: ldr             x4, [x4, #0x478]
    // 0xad909c: r0 = GetNavigation.toNamed()
    //     0xad909c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad90a0: LeaveFrame
    //     0xad90a0: mov             SP, fp
    //     0xad90a4: ldp             fp, lr, [SP], #0x10
    // 0xad90a8: ret
    //     0xad90a8: ret             
    // 0xad90ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad90ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad90b0: b               #0xad904c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xad90b4, size: 0x1b0
    // 0xad90b4: EnterFrame
    //     0xad90b4: stp             fp, lr, [SP, #-0x10]!
    //     0xad90b8: mov             fp, SP
    // 0xad90bc: AllocStack(0x20)
    //     0xad90bc: sub             SP, SP, #0x20
    // 0xad90c0: SetupParameters()
    //     0xad90c0: ldr             x0, [fp, #0x10]
    //     0xad90c4: ldur            w1, [x0, #0x17]
    //     0xad90c8: add             x1, x1, HEAP, lsl #32
    // 0xad90cc: CheckStackOverflow
    //     0xad90cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad90d0: cmp             SP, x16
    //     0xad90d4: b.ls            #0xad9258
    // 0xad90d8: LoadField: r0 = r1->field_b
    //     0xad90d8: ldur            w0, [x1, #0xb]
    // 0xad90dc: DecompressPointer r0
    //     0xad90dc: add             x0, x0, HEAP, lsl #32
    // 0xad90e0: stur            x0, [fp, #-8]
    // 0xad90e4: LoadField: r1 = r0->field_f
    //     0xad90e4: ldur            w1, [x0, #0xf]
    // 0xad90e8: DecompressPointer r1
    //     0xad90e8: add             x1, x1, HEAP, lsl #32
    // 0xad90ec: r0 = controller()
    //     0xad90ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad90f0: LoadField: r2 = r0->field_1f
    //     0xad90f0: ldur            w2, [x0, #0x1f]
    // 0xad90f4: DecompressPointer r2
    //     0xad90f4: add             x2, x2, HEAP, lsl #32
    // 0xad90f8: LoadField: r0 = r2->field_b
    //     0xad90f8: ldur            w0, [x2, #0xb]
    // 0xad90fc: r1 = LoadInt32Instr(r0)
    //     0xad90fc: sbfx            x1, x0, #1, #0x1f
    // 0xad9100: mov             x0, x1
    // 0xad9104: r1 = 1
    //     0xad9104: movz            x1, #0x1
    // 0xad9108: cmp             x1, x0
    // 0xad910c: b.hs            #0xad9260
    // 0xad9110: LoadField: r0 = r2->field_f
    //     0xad9110: ldur            w0, [x2, #0xf]
    // 0xad9114: DecompressPointer r0
    //     0xad9114: add             x0, x0, HEAP, lsl #32
    // 0xad9118: LoadField: r2 = r0->field_13
    //     0xad9118: ldur            w2, [x0, #0x13]
    // 0xad911c: DecompressPointer r2
    //     0xad911c: add             x2, x2, HEAP, lsl #32
    // 0xad9120: ldur            x0, [fp, #-8]
    // 0xad9124: stur            x2, [fp, #-0x10]
    // 0xad9128: LoadField: r1 = r0->field_f
    //     0xad9128: ldur            w1, [x0, #0xf]
    // 0xad912c: DecompressPointer r1
    //     0xad912c: add             x1, x1, HEAP, lsl #32
    // 0xad9130: r0 = controller()
    //     0xad9130: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9134: mov             x1, x0
    // 0xad9138: r0 = loadingBookmark()
    //     0xad9138: bl              #0xad929c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::loadingBookmark
    // 0xad913c: tbnz            w0, #4, #0xad9148
    // 0xad9140: r2 = Null
    //     0xad9140: mov             x2, NULL
    // 0xad9144: b               #0xad916c
    // 0xad9148: ldur            x0, [fp, #-8]
    // 0xad914c: LoadField: r1 = r0->field_f
    //     0xad914c: ldur            w1, [x0, #0xf]
    // 0xad9150: DecompressPointer r1
    //     0xad9150: add             x1, x1, HEAP, lsl #32
    // 0xad9154: r0 = controller()
    //     0xad9154: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9158: mov             x2, x0
    // 0xad915c: r1 = Function 'toggleBookmark':.
    //     0xad915c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f988] AnonymousClosure: (0xad92d4), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::toggleBookmark (0xad930c)
    //     0xad9160: ldr             x1, [x1, #0x988]
    // 0xad9164: r0 = AllocateClosure()
    //     0xad9164: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9168: mov             x2, x0
    // 0xad916c: ldur            x0, [fp, #-8]
    // 0xad9170: stur            x2, [fp, #-0x18]
    // 0xad9174: LoadField: r1 = r0->field_f
    //     0xad9174: ldur            w1, [x0, #0xf]
    // 0xad9178: DecompressPointer r1
    //     0xad9178: add             x1, x1, HEAP, lsl #32
    // 0xad917c: r0 = controller()
    //     0xad917c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9180: mov             x1, x0
    // 0xad9184: r0 = bookmark()
    //     0xad9184: bl              #0xad9264  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::bookmark
    // 0xad9188: tbnz            w0, #4, #0xad9198
    // 0xad918c: r2 = Instance_Icon
    //     0xad918c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!Icon@e241f1
    //     0xad9190: ldr             x2, [x2, #0x990]
    // 0xad9194: b               #0xad91a0
    // 0xad9198: r2 = Instance_Icon
    //     0xad9198: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!Icon@e241b1
    //     0xad919c: ldr             x2, [x2, #0x998]
    // 0xad91a0: ldur            x0, [fp, #-0x18]
    // 0xad91a4: ldur            x1, [fp, #-0x10]
    // 0xad91a8: stur            x2, [fp, #-8]
    // 0xad91ac: r0 = IconButton()
    //     0xad91ac: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad91b0: mov             x1, x0
    // 0xad91b4: ldur            x0, [fp, #-0x18]
    // 0xad91b8: stur            x1, [fp, #-0x20]
    // 0xad91bc: StoreField: r1->field_3b = r0
    //     0xad91bc: stur            w0, [x1, #0x3b]
    // 0xad91c0: r0 = false
    //     0xad91c0: add             x0, NULL, #0x30  ; false
    // 0xad91c4: StoreField: r1->field_47 = r0
    //     0xad91c4: stur            w0, [x1, #0x47]
    // 0xad91c8: ldur            x0, [fp, #-8]
    // 0xad91cc: StoreField: r1->field_1f = r0
    //     0xad91cc: stur            w0, [x1, #0x1f]
    // 0xad91d0: r0 = Instance__IconButtonVariant
    //     0xad91d0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad91d4: ldr             x0, [x0, #0xf78]
    // 0xad91d8: StoreField: r1->field_63 = r0
    //     0xad91d8: stur            w0, [x1, #0x63]
    // 0xad91dc: r0 = SizedBox()
    //     0xad91dc: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad91e0: mov             x1, x0
    // 0xad91e4: r0 = 42.000000
    //     0xad91e4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2be80] 42
    //     0xad91e8: ldr             x0, [x0, #0xe80]
    // 0xad91ec: stur            x1, [fp, #-8]
    // 0xad91f0: StoreField: r1->field_f = r0
    //     0xad91f0: stur            w0, [x1, #0xf]
    // 0xad91f4: ldur            x0, [fp, #-0x20]
    // 0xad91f8: StoreField: r1->field_b = r0
    //     0xad91f8: stur            w0, [x1, #0xb]
    // 0xad91fc: r0 = NShowcase()
    //     0xad91fc: bl              #0xad882c  ; AllocateNShowcaseStub -> NShowcase (size=0x28)
    // 0xad9200: mov             x1, x0
    // 0xad9204: ldur            x0, [fp, #-8]
    // 0xad9208: stur            x1, [fp, #-0x18]
    // 0xad920c: StoreField: r1->field_f = r0
    //     0xad920c: stur            w0, [x1, #0xf]
    // 0xad9210: ldur            x0, [fp, #-0x10]
    // 0xad9214: StoreField: r1->field_b = r0
    //     0xad9214: stur            w0, [x1, #0xb]
    // 0xad9218: r0 = "Simpan doa ke bookmark supaya Kamu lebih mudah mencari jika ingin membacanya kembali"
    //     0xad9218: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "Simpan doa ke bookmark supaya Kamu lebih mudah mencari jika ingin membacanya kembali"
    //     0xad921c: ldr             x0, [x0, #0x9a0]
    // 0xad9220: StoreField: r1->field_13 = r0
    //     0xad9220: stur            w0, [x1, #0x13]
    // 0xad9224: r0 = 3
    //     0xad9224: movz            x0, #0x3
    // 0xad9228: ArrayStore: r1[0] = r0  ; List_8
    //     0xad9228: stur            x0, [x1, #0x17]
    // 0xad922c: r0 = 1
    //     0xad922c: movz            x0, #0x1
    // 0xad9230: StoreField: r1->field_1f = r0
    //     0xad9230: stur            x0, [x1, #0x1f]
    // 0xad9234: r0 = Padding()
    //     0xad9234: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad9238: r1 = Instance_EdgeInsets
    //     0xad9238: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f920] Obj!EdgeInsets@e12671
    //     0xad923c: ldr             x1, [x1, #0x920]
    // 0xad9240: StoreField: r0->field_f = r1
    //     0xad9240: stur            w1, [x0, #0xf]
    // 0xad9244: ldur            x1, [fp, #-0x18]
    // 0xad9248: StoreField: r0->field_b = r1
    //     0xad9248: stur            w1, [x0, #0xb]
    // 0xad924c: LeaveFrame
    //     0xad924c: mov             SP, fp
    //     0xad9250: ldp             fp, lr, [SP], #0x10
    // 0xad9254: ret
    //     0xad9254: ret             
    // 0xad9258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad925c: b               #0xad90d8
    // 0xad9260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9260: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad94cc, size: 0x114
    // 0xad94cc: EnterFrame
    //     0xad94cc: stp             fp, lr, [SP, #-0x10]!
    //     0xad94d0: mov             fp, SP
    // 0xad94d4: AllocStack(0x28)
    //     0xad94d4: sub             SP, SP, #0x28
    // 0xad94d8: SetupParameters()
    //     0xad94d8: ldr             x0, [fp, #0x10]
    //     0xad94dc: ldur            w1, [x0, #0x17]
    //     0xad94e0: add             x1, x1, HEAP, lsl #32
    //     0xad94e4: stur            x1, [fp, #-8]
    // 0xad94e8: CheckStackOverflow
    //     0xad94e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad94ec: cmp             SP, x16
    //     0xad94f0: b.ls            #0xad95d8
    // 0xad94f4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad94f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad94f8: ldr             x0, [x0, #0x2670]
    //     0xad94fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad9500: cmp             w0, w16
    //     0xad9504: b.ne            #0xad9510
    //     0xad9508: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad950c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad9510: r1 = Null
    //     0xad9510: mov             x1, NULL
    // 0xad9514: r2 = 4
    //     0xad9514: movz            x2, #0x4
    // 0xad9518: r0 = AllocateArray()
    //     0xad9518: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad951c: stur            x0, [fp, #-0x10]
    // 0xad9520: r16 = "id"
    //     0xad9520: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xad9524: ldr             x16, [x16, #0x740]
    // 0xad9528: StoreField: r0->field_f = r16
    //     0xad9528: stur            w16, [x0, #0xf]
    // 0xad952c: ldur            x1, [fp, #-8]
    // 0xad9530: LoadField: r2 = r1->field_b
    //     0xad9530: ldur            w2, [x1, #0xb]
    // 0xad9534: DecompressPointer r2
    //     0xad9534: add             x2, x2, HEAP, lsl #32
    // 0xad9538: LoadField: r1 = r2->field_f
    //     0xad9538: ldur            w1, [x2, #0xf]
    // 0xad953c: DecompressPointer r1
    //     0xad953c: add             x1, x1, HEAP, lsl #32
    // 0xad9540: r0 = controller()
    //     0xad9540: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9544: mov             x1, x0
    // 0xad9548: r0 = currentItem()
    //     0xad9548: bl              #0x8f13bc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::currentItem
    // 0xad954c: cmp             w0, NULL
    // 0xad9550: b.ne            #0xad955c
    // 0xad9554: r0 = Null
    //     0xad9554: mov             x0, NULL
    // 0xad9558: b               #0xad9574
    // 0xad955c: LoadField: r2 = r0->field_7
    //     0xad955c: ldur            x2, [x0, #7]
    // 0xad9560: r0 = BoxInt64Instr(r2)
    //     0xad9560: sbfiz           x0, x2, #1, #0x1f
    //     0xad9564: cmp             x2, x0, asr #1
    //     0xad9568: b.eq            #0xad9574
    //     0xad956c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad9570: stur            x2, [x0, #7]
    // 0xad9574: ldur            x1, [fp, #-0x10]
    // 0xad9578: ArrayStore: r1[1] = r0  ; List_4
    //     0xad9578: add             x25, x1, #0x13
    //     0xad957c: str             w0, [x25]
    //     0xad9580: tbz             w0, #0, #0xad959c
    //     0xad9584: ldurb           w16, [x1, #-1]
    //     0xad9588: ldurb           w17, [x0, #-1]
    //     0xad958c: and             x16, x17, x16, lsr #2
    //     0xad9590: tst             x16, HEAP, lsr #32
    //     0xad9594: b.eq            #0xad959c
    //     0xad9598: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad959c: r16 = <String, int?>
    //     0xad959c: add             x16, PP, #0x12, lsl #12  ; [pp+0x124a0] TypeArguments: <String, int?>
    //     0xad95a0: ldr             x16, [x16, #0x4a0]
    // 0xad95a4: ldur            lr, [fp, #-0x10]
    // 0xad95a8: stp             lr, x16, [SP]
    // 0xad95ac: r0 = Map._fromLiteral()
    //     0xad95ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xad95b0: r16 = "/doa/doa-info"
    //     0xad95b0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "/doa/doa-info"
    //     0xad95b4: ldr             x16, [x16, #0xb20]
    // 0xad95b8: stp             x16, NULL, [SP, #8]
    // 0xad95bc: str             x0, [SP]
    // 0xad95c0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad95c0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad95c4: ldr             x4, [x4, #0x478]
    // 0xad95c8: r0 = GetNavigation.toNamed()
    //     0xad95c8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad95cc: LeaveFrame
    //     0xad95cc: mov             SP, fp
    //     0xad95d0: ldp             fp, lr, [SP], #0x10
    // 0xad95d4: ret
    //     0xad95d4: ret             
    // 0xad95d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad95d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad95dc: b               #0xad94f4
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xad95e0, size: 0x9c
    // 0xad95e0: EnterFrame
    //     0xad95e0: stp             fp, lr, [SP, #-0x10]!
    //     0xad95e4: mov             fp, SP
    // 0xad95e8: AllocStack(0x10)
    //     0xad95e8: sub             SP, SP, #0x10
    // 0xad95ec: SetupParameters()
    //     0xad95ec: ldr             x0, [fp, #0x10]
    //     0xad95f0: ldur            w1, [x0, #0x17]
    //     0xad95f4: add             x1, x1, HEAP, lsl #32
    // 0xad95f8: CheckStackOverflow
    //     0xad95f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad95fc: cmp             SP, x16
    //     0xad9600: b.ls            #0xad9674
    // 0xad9604: LoadField: r0 = r1->field_b
    //     0xad9604: ldur            w0, [x1, #0xb]
    // 0xad9608: DecompressPointer r0
    //     0xad9608: add             x0, x0, HEAP, lsl #32
    // 0xad960c: stur            x0, [fp, #-8]
    // 0xad9610: LoadField: r1 = r0->field_f
    //     0xad9610: ldur            w1, [x0, #0xf]
    // 0xad9614: DecompressPointer r1
    //     0xad9614: add             x1, x1, HEAP, lsl #32
    // 0xad9618: r0 = controller()
    //     0xad9618: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad961c: mov             x1, x0
    // 0xad9620: r0 = title()
    //     0xad9620: bl              #0xad967c  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::title
    // 0xad9624: LoadField: r2 = r0->field_7
    //     0xad9624: ldur            w2, [x0, #7]
    // 0xad9628: ldur            x0, [fp, #-8]
    // 0xad962c: stur            x2, [fp, #-0x10]
    // 0xad9630: LoadField: r1 = r0->field_f
    //     0xad9630: ldur            w1, [x0, #0xf]
    // 0xad9634: DecompressPointer r1
    //     0xad9634: add             x1, x1, HEAP, lsl #32
    // 0xad9638: r0 = controller()
    //     0xad9638: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad963c: LoadField: r1 = r0->field_4f
    //     0xad963c: ldur            w1, [x0, #0x4f]
    // 0xad9640: DecompressPointer r1
    //     0xad9640: add             x1, x1, HEAP, lsl #32
    // 0xad9644: r0 = value()
    //     0xad9644: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad9648: stur            x0, [fp, #-8]
    // 0xad964c: r0 = Text()
    //     0xad964c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad9650: ldur            x1, [fp, #-8]
    // 0xad9654: StoreField: r0->field_b = r1
    //     0xad9654: stur            w1, [x0, #0xb]
    // 0xad9658: ldur            x1, [fp, #-0x10]
    // 0xad965c: cbnz            w1, #0xad9668
    // 0xad9660: r0 = Instance_SizedBox
    //     0xad9660: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad9664: ldr             x0, [x0, #0xc40]
    // 0xad9668: LeaveFrame
    //     0xad9668: mov             SP, fp
    //     0xad966c: ldp             fp, lr, [SP], #0x10
    // 0xad9670: ret
    //     0xad9670: ret             
    // 0xad9674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9674: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9678: b               #0xad9604
  }
}
