// lib: , url: package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart

// class id: 1050177, size: 0x8
class :: {
}

// class id: 5053, size: 0x14, field offset: 0xc
//   const constructor, 
class DoaDetailBuilder extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb89b78, size: 0xfc
    // 0xb89b78: EnterFrame
    //     0xb89b78: stp             fp, lr, [SP, #-0x10]!
    //     0xb89b7c: mov             fp, SP
    // 0xb89b80: AllocStack(0x28)
    //     0xb89b80: sub             SP, SP, #0x28
    // 0xb89b84: CheckStackOverflow
    //     0xb89b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89b88: cmp             SP, x16
    //     0xb89b8c: b.ls            #0xb89c6c
    // 0xb89b90: LoadField: r2 = r1->field_b
    //     0xb89b90: ldur            x2, [x1, #0xb]
    // 0xb89b94: stur            x2, [fp, #-8]
    // 0xb89b98: r0 = BoxInt64Instr(r2)
    //     0xb89b98: sbfiz           x0, x2, #1, #0x1f
    //     0xb89b9c: cmp             x2, x0, asr #1
    //     0xb89ba0: b.eq            #0xb89bac
    //     0xb89ba4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb89ba8: stur            x2, [x0, #7]
    // 0xb89bac: r1 = 60
    //     0xb89bac: movz            x1, #0x3c
    // 0xb89bb0: branchIfSmi(r0, 0xb89bbc)
    //     0xb89bb0: tbz             w0, #0, #0xb89bbc
    // 0xb89bb4: r1 = LoadClassIdInstr(r0)
    //     0xb89bb4: ldur            x1, [x0, #-1]
    //     0xb89bb8: ubfx            x1, x1, #0xc, #0x14
    // 0xb89bbc: str             x0, [SP]
    // 0xb89bc0: mov             x0, x1
    // 0xb89bc4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb89bc4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb89bc8: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb89bc8: movz            x17, #0x2b03
    //     0xb89bcc: add             lr, x0, x17
    //     0xb89bd0: ldr             lr, [x21, lr, lsl #3]
    //     0xb89bd4: blr             lr
    // 0xb89bd8: stur            x0, [fp, #-0x10]
    // 0xb89bdc: r0 = find()
    //     0xb89bdc: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0xb89be0: stur            x0, [fp, #-0x18]
    // 0xb89be4: r0 = find()
    //     0xb89be4: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0xb89be8: r0 = DoaDetailBuilderController()
    //     0xb89be8: bl              #0xb89c74  ; AllocateDoaDetailBuilderControllerStub -> DoaDetailBuilderController (size=0x34)
    // 0xb89bec: mov             x2, x0
    // 0xb89bf0: ldur            x0, [fp, #-8]
    // 0xb89bf4: stur            x2, [fp, #-0x20]
    // 0xb89bf8: StoreField: r2->field_27 = r0
    //     0xb89bf8: stur            x0, [x2, #0x27]
    // 0xb89bfc: ldur            x0, [fp, #-0x18]
    // 0xb89c00: StoreField: r2->field_2f = r0
    //     0xb89c00: stur            w0, [x2, #0x2f]
    // 0xb89c04: mov             x1, x2
    // 0xb89c08: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb89c08: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb89c0c: r1 = <DoaDetailBuilderController>
    //     0xb89c0c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d80] TypeArguments: <DoaDetailBuilderController>
    //     0xb89c10: ldr             x1, [x1, #0xd80]
    // 0xb89c14: r0 = GetBuilder()
    //     0xb89c14: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb89c18: mov             x3, x0
    // 0xb89c1c: ldur            x0, [fp, #-0x20]
    // 0xb89c20: stur            x3, [fp, #-0x18]
    // 0xb89c24: StoreField: r3->field_3b = r0
    //     0xb89c24: stur            w0, [x3, #0x3b]
    // 0xb89c28: r0 = true
    //     0xb89c28: add             x0, NULL, #0x20  ; true
    // 0xb89c2c: StoreField: r3->field_13 = r0
    //     0xb89c2c: stur            w0, [x3, #0x13]
    // 0xb89c30: r1 = Function '<anonymous closure>':.
    //     0xb89c30: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d88] AnonymousClosure: (0xb89c80), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb89c34: ldr             x1, [x1, #0xd88]
    // 0xb89c38: r2 = Null
    //     0xb89c38: mov             x2, NULL
    // 0xb89c3c: r0 = AllocateClosure()
    //     0xb89c3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89c40: mov             x1, x0
    // 0xb89c44: ldur            x0, [fp, #-0x18]
    // 0xb89c48: StoreField: r0->field_f = r1
    //     0xb89c48: stur            w1, [x0, #0xf]
    // 0xb89c4c: r1 = false
    //     0xb89c4c: add             x1, NULL, #0x30  ; false
    // 0xb89c50: StoreField: r0->field_1f = r1
    //     0xb89c50: stur            w1, [x0, #0x1f]
    // 0xb89c54: StoreField: r0->field_23 = r1
    //     0xb89c54: stur            w1, [x0, #0x23]
    // 0xb89c58: ldur            x1, [fp, #-0x10]
    // 0xb89c5c: StoreField: r0->field_1b = r1
    //     0xb89c5c: stur            w1, [x0, #0x1b]
    // 0xb89c60: LeaveFrame
    //     0xb89c60: mov             SP, fp
    //     0xb89c64: ldp             fp, lr, [SP], #0x10
    // 0xb89c68: ret
    //     0xb89c68: ret             
    // 0xb89c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89c6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89c70: b               #0xb89b90
  }
  [closure] RefreshIndicator <anonymous closure>(dynamic, DoaDetailBuilderController) {
    // ** addr: 0xb89c80, size: 0x138
    // 0xb89c80: EnterFrame
    //     0xb89c80: stp             fp, lr, [SP, #-0x10]!
    //     0xb89c84: mov             fp, SP
    // 0xb89c88: AllocStack(0x38)
    //     0xb89c88: sub             SP, SP, #0x38
    // 0xb89c8c: SetupParameters()
    //     0xb89c8c: ldr             x0, [fp, #0x18]
    //     0xb89c90: ldur            w1, [x0, #0x17]
    //     0xb89c94: add             x1, x1, HEAP, lsl #32
    //     0xb89c98: stur            x1, [fp, #-8]
    // 0xb89c9c: CheckStackOverflow
    //     0xb89c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89ca0: cmp             SP, x16
    //     0xb89ca4: b.ls            #0xb89db0
    // 0xb89ca8: r1 = 1
    //     0xb89ca8: movz            x1, #0x1
    // 0xb89cac: r0 = AllocateContext()
    //     0xb89cac: bl              #0xec126c  ; AllocateContextStub
    // 0xb89cb0: mov             x3, x0
    // 0xb89cb4: ldur            x0, [fp, #-8]
    // 0xb89cb8: stur            x3, [fp, #-0x10]
    // 0xb89cbc: StoreField: r3->field_b = r0
    //     0xb89cbc: stur            w0, [x3, #0xb]
    // 0xb89cc0: ldr             x0, [fp, #0x10]
    // 0xb89cc4: StoreField: r3->field_f = r0
    //     0xb89cc4: stur            w0, [x3, #0xf]
    // 0xb89cc8: r1 = Function '<anonymous closure>':.
    //     0xb89cc8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d90] AnonymousClosure: (0xb89efc), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb89ccc: ldr             x1, [x1, #0xd90]
    // 0xb89cd0: r2 = Null
    //     0xb89cd0: mov             x2, NULL
    // 0xb89cd4: r0 = AllocateClosure()
    //     0xb89cd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89cd8: ldur            x2, [fp, #-0x10]
    // 0xb89cdc: r1 = Function '<anonymous closure>':.
    //     0xb89cdc: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d98] AnonymousClosure: (0xb89e30), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb89ce0: ldr             x1, [x1, #0xd98]
    // 0xb89ce4: stur            x0, [fp, #-8]
    // 0xb89ce8: r0 = AllocateClosure()
    //     0xb89ce8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89cec: r16 = <List<Doa>>
    //     0xb89cec: add             x16, PP, #0x35, lsl #12  ; [pp+0x35da0] TypeArguments: <List<Doa>>
    //     0xb89cf0: ldr             x16, [x16, #0xda0]
    // 0xb89cf4: ldr             lr, [fp, #0x10]
    // 0xb89cf8: stp             lr, x16, [SP, #0x18]
    // 0xb89cfc: ldur            x16, [fp, #-8]
    // 0xb89d00: r30 = Instance_NSkeleton
    //     0xb89d00: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2ca90] Obj!NSkeleton@e20971
    //     0xb89d04: ldr             lr, [lr, #0xa90]
    // 0xb89d08: stp             lr, x16, [SP, #8]
    // 0xb89d0c: str             x0, [SP]
    // 0xb89d10: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xb89d10: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xb89d14: ldr             x4, [x4, #0x728]
    // 0xb89d18: r0 = StateExt.obx()
    //     0xb89d18: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xb89d1c: stur            x0, [fp, #-8]
    // 0xb89d20: r0 = Scrollbar()
    //     0xb89d20: bl              #0xaf9208  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0xb89d24: mov             x1, x0
    // 0xb89d28: ldur            x0, [fp, #-8]
    // 0xb89d2c: stur            x1, [fp, #-0x10]
    // 0xb89d30: StoreField: r1->field_b = r0
    //     0xb89d30: stur            w0, [x1, #0xb]
    // 0xb89d34: r0 = RefreshIndicator()
    //     0xb89d34: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xb89d38: mov             x3, x0
    // 0xb89d3c: ldur            x0, [fp, #-0x10]
    // 0xb89d40: stur            x3, [fp, #-8]
    // 0xb89d44: StoreField: r3->field_b = r0
    //     0xb89d44: stur            w0, [x3, #0xb]
    // 0xb89d48: d0 = 40.000000
    //     0xb89d48: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xb89d4c: StoreField: r3->field_f = d0
    //     0xb89d4c: stur            d0, [x3, #0xf]
    // 0xb89d50: ArrayStore: r3[0] = rZR  ; List_8
    //     0xb89d50: stur            xzr, [x3, #0x17]
    // 0xb89d54: ldr             x2, [fp, #0x10]
    // 0xb89d58: r1 = Function 'onRefresh':.
    //     0xb89d58: add             x1, PP, #0x35, lsl #12  ; [pp+0x35da8] AnonymousClosure: (0xb89db8), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onRefresh (0xb89df0)
    //     0xb89d5c: ldr             x1, [x1, #0xda8]
    // 0xb89d60: r0 = AllocateClosure()
    //     0xb89d60: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89d64: mov             x1, x0
    // 0xb89d68: ldur            x0, [fp, #-8]
    // 0xb89d6c: StoreField: r0->field_1f = r1
    //     0xb89d6c: stur            w1, [x0, #0x1f]
    // 0xb89d70: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xb89d70: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xb89d74: ldr             x1, [x1, #0xf58]
    // 0xb89d78: StoreField: r0->field_2f = r1
    //     0xb89d78: stur            w1, [x0, #0x2f]
    // 0xb89d7c: d0 = 2.500000
    //     0xb89d7c: fmov            d0, #2.50000000
    // 0xb89d80: StoreField: r0->field_3b = d0
    //     0xb89d80: stur            d0, [x0, #0x3b]
    // 0xb89d84: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xb89d84: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xb89d88: ldr             x1, [x1, #0xa68]
    // 0xb89d8c: StoreField: r0->field_47 = r1
    //     0xb89d8c: stur            w1, [x0, #0x47]
    // 0xb89d90: d0 = 2.000000
    //     0xb89d90: fmov            d0, #2.00000000
    // 0xb89d94: StoreField: r0->field_4b = d0
    //     0xb89d94: stur            d0, [x0, #0x4b]
    // 0xb89d98: r1 = Instance__IndicatorType
    //     0xb89d98: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xb89d9c: ldr             x1, [x1, #0xa70]
    // 0xb89da0: StoreField: r0->field_43 = r1
    //     0xb89da0: stur            w1, [x0, #0x43]
    // 0xb89da4: LeaveFrame
    //     0xb89da4: mov             SP, fp
    //     0xb89da8: ldp             fp, lr, [SP], #0x10
    // 0xb89dac: ret
    //     0xb89dac: ret             
    // 0xb89db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89db0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89db4: b               #0xb89ca8
  }
  [closure] NEmptyState <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb89e30, size: 0xcc
    // 0xb89e30: EnterFrame
    //     0xb89e30: stp             fp, lr, [SP, #-0x10]!
    //     0xb89e34: mov             fp, SP
    // 0xb89e38: AllocStack(0x18)
    //     0xb89e38: sub             SP, SP, #0x18
    // 0xb89e3c: SetupParameters()
    //     0xb89e3c: ldr             x0, [fp, #0x18]
    //     0xb89e40: ldur            w1, [x0, #0x17]
    //     0xb89e44: add             x1, x1, HEAP, lsl #32
    //     0xb89e48: stur            x1, [fp, #-8]
    // 0xb89e4c: CheckStackOverflow
    //     0xb89e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89e50: cmp             SP, x16
    //     0xb89e54: b.ls            #0xb89ef4
    // 0xb89e58: r16 = "ApiError.notFound"
    //     0xb89e58: add             x16, PP, #0x29, lsl #12  ; [pp+0x29730] "ApiError.notFound"
    //     0xb89e5c: ldr             x16, [x16, #0x730]
    // 0xb89e60: ldr             lr, [fp, #0x10]
    // 0xb89e64: stp             lr, x16, [SP]
    // 0xb89e68: r0 = ==()
    //     0xb89e68: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb89e6c: tbz             w0, #4, #0xb89eb8
    // 0xb89e70: r16 = "ApiError.noConnection"
    //     0xb89e70: add             x16, PP, #0x29, lsl #12  ; [pp+0x29738] "ApiError.noConnection"
    //     0xb89e74: ldr             x16, [x16, #0x738]
    // 0xb89e78: ldr             lr, [fp, #0x10]
    // 0xb89e7c: stp             lr, x16, [SP]
    // 0xb89e80: r0 = ==()
    //     0xb89e80: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb89e84: tbnz            w0, #4, #0xb89eb8
    // 0xb89e88: ldur            x0, [fp, #-8]
    // 0xb89e8c: LoadField: r2 = r0->field_f
    //     0xb89e8c: ldur            w2, [x0, #0xf]
    // 0xb89e90: DecompressPointer r2
    //     0xb89e90: add             x2, x2, HEAP, lsl #32
    // 0xb89e94: r1 = Function 'onRefresh':.
    //     0xb89e94: add             x1, PP, #0x35, lsl #12  ; [pp+0x35da8] AnonymousClosure: (0xb89db8), in [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_builder_controller.dart] DoaDetailBuilderController::onRefresh (0xb89df0)
    //     0xb89e98: ldr             x1, [x1, #0xda8]
    // 0xb89e9c: r0 = AllocateClosure()
    //     0xb89e9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89ea0: mov             x2, x0
    // 0xb89ea4: r1 = Null
    //     0xb89ea4: mov             x1, NULL
    // 0xb89ea8: r0 = NEmptyState.notConnection()
    //     0xb89ea8: bl              #0xad9f3c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.notConnection
    // 0xb89eac: LeaveFrame
    //     0xb89eac: mov             SP, fp
    //     0xb89eb0: ldp             fp, lr, [SP], #0x10
    // 0xb89eb4: ret
    //     0xb89eb4: ret             
    // 0xb89eb8: r0 = NEmptyState()
    //     0xb89eb8: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xb89ebc: mov             x1, x0
    // 0xb89ec0: r2 = "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xb89ec0: add             x2, PP, #0x29, lsl #12  ; [pp+0x297e8] "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xb89ec4: ldr             x2, [x2, #0x7e8]
    // 0xb89ec8: r3 = "assets/images/illustration/no_search.svg"
    //     0xb89ec8: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xb89ecc: ldr             x3, [x3, #0x138]
    // 0xb89ed0: r5 = "Halaman Tidak Ditemukan"
    //     0xb89ed0: add             x5, PP, #0x29, lsl #12  ; [pp+0x292c8] "Halaman Tidak Ditemukan"
    //     0xb89ed4: ldr             x5, [x5, #0x2c8]
    // 0xb89ed8: stur            x0, [fp, #-8]
    // 0xb89edc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb89edc: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb89ee0: r0 = NEmptyState.svg()
    //     0xb89ee0: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xb89ee4: ldur            x0, [fp, #-8]
    // 0xb89ee8: LeaveFrame
    //     0xb89ee8: mov             SP, fp
    //     0xb89eec: ldp             fp, lr, [SP], #0x10
    // 0xb89ef0: ret
    //     0xb89ef0: ret             
    // 0xb89ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89ef8: b               #0xb89e58
  }
  [closure] ListView <anonymous closure>(dynamic, List<Doa>?) {
    // ** addr: 0xb89efc, size: 0xe8
    // 0xb89efc: EnterFrame
    //     0xb89efc: stp             fp, lr, [SP, #-0x10]!
    //     0xb89f00: mov             fp, SP
    // 0xb89f04: AllocStack(0x20)
    //     0xb89f04: sub             SP, SP, #0x20
    // 0xb89f08: SetupParameters()
    //     0xb89f08: ldr             x0, [fp, #0x18]
    //     0xb89f0c: ldur            w1, [x0, #0x17]
    //     0xb89f10: add             x1, x1, HEAP, lsl #32
    //     0xb89f14: stur            x1, [fp, #-8]
    // 0xb89f18: CheckStackOverflow
    //     0xb89f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89f1c: cmp             SP, x16
    //     0xb89f20: b.ls            #0xb89fd8
    // 0xb89f24: r1 = 1
    //     0xb89f24: movz            x1, #0x1
    // 0xb89f28: r0 = AllocateContext()
    //     0xb89f28: bl              #0xec126c  ; AllocateContextStub
    // 0xb89f2c: mov             x1, x0
    // 0xb89f30: ldur            x0, [fp, #-8]
    // 0xb89f34: stur            x1, [fp, #-0x10]
    // 0xb89f38: StoreField: r1->field_b = r0
    //     0xb89f38: stur            w0, [x1, #0xb]
    // 0xb89f3c: ldr             x0, [fp, #0x10]
    // 0xb89f40: StoreField: r1->field_f = r0
    //     0xb89f40: stur            w0, [x1, #0xf]
    // 0xb89f44: cmp             w0, NULL
    // 0xb89f48: b.eq            #0xb89fe0
    // 0xb89f4c: r2 = LoadClassIdInstr(r0)
    //     0xb89f4c: ldur            x2, [x0, #-1]
    //     0xb89f50: ubfx            x2, x2, #0xc, #0x14
    // 0xb89f54: str             x0, [SP]
    // 0xb89f58: mov             x0, x2
    // 0xb89f5c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb89f5c: movz            x17, #0xc834
    //     0xb89f60: add             lr, x0, x17
    //     0xb89f64: ldr             lr, [x21, lr, lsl #3]
    //     0xb89f68: blr             lr
    // 0xb89f6c: r1 = LoadInt32Instr(r0)
    //     0xb89f6c: sbfx            x1, x0, #1, #0x1f
    //     0xb89f70: tbz             w0, #0, #0xb89f78
    //     0xb89f74: ldur            x1, [x0, #7]
    // 0xb89f78: add             x3, x1, #1
    // 0xb89f7c: stur            x3, [fp, #-0x18]
    // 0xb89f80: r1 = Function '<anonymous closure>':.
    //     0xb89f80: add             x1, PP, #0x35, lsl #12  ; [pp+0x35dd8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb89f84: ldr             x1, [x1, #0xdd8]
    // 0xb89f88: r2 = Null
    //     0xb89f88: mov             x2, NULL
    // 0xb89f8c: r0 = AllocateClosure()
    //     0xb89f8c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89f90: ldur            x2, [fp, #-0x10]
    // 0xb89f94: r1 = Function '<anonymous closure>':.
    //     0xb89f94: add             x1, PP, #0x35, lsl #12  ; [pp+0x35de0] AnonymousClosure: (0xb89fe4), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb89f98: ldr             x1, [x1, #0xde0]
    // 0xb89f9c: stur            x0, [fp, #-8]
    // 0xb89fa0: r0 = AllocateClosure()
    //     0xb89fa0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb89fa4: stur            x0, [fp, #-0x10]
    // 0xb89fa8: r0 = ListView()
    //     0xb89fa8: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb89fac: mov             x1, x0
    // 0xb89fb0: ldur            x2, [fp, #-0x10]
    // 0xb89fb4: ldur            x3, [fp, #-0x18]
    // 0xb89fb8: ldur            x5, [fp, #-8]
    // 0xb89fbc: stur            x0, [fp, #-8]
    // 0xb89fc0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb89fc0: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb89fc4: r0 = ListView.separated()
    //     0xb89fc4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb89fc8: ldur            x0, [fp, #-8]
    // 0xb89fcc: LeaveFrame
    //     0xb89fcc: mov             SP, fp
    //     0xb89fd0: ldp             fp, lr, [SP], #0x10
    // 0xb89fd4: ret
    //     0xb89fd4: ret             
    // 0xb89fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89fd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89fdc: b               #0xb89f24
    // 0xb89fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89fe0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb89fe4, size: 0x1a0
    // 0xb89fe4: EnterFrame
    //     0xb89fe4: stp             fp, lr, [SP, #-0x10]!
    //     0xb89fe8: mov             fp, SP
    // 0xb89fec: AllocStack(0x28)
    //     0xb89fec: sub             SP, SP, #0x28
    // 0xb89ff0: SetupParameters()
    //     0xb89ff0: ldr             x0, [fp, #0x20]
    //     0xb89ff4: ldur            w1, [x0, #0x17]
    //     0xb89ff8: add             x1, x1, HEAP, lsl #32
    //     0xb89ffc: stur            x1, [fp, #-8]
    // 0xb8a000: CheckStackOverflow
    //     0xb8a000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a004: cmp             SP, x16
    //     0xb8a008: b.ls            #0xb8a17c
    // 0xb8a00c: r1 = 1
    //     0xb8a00c: movz            x1, #0x1
    // 0xb8a010: r0 = AllocateContext()
    //     0xb8a010: bl              #0xec126c  ; AllocateContextStub
    // 0xb8a014: mov             x2, x0
    // 0xb8a018: ldur            x1, [fp, #-8]
    // 0xb8a01c: stur            x2, [fp, #-0x10]
    // 0xb8a020: StoreField: r2->field_b = r1
    //     0xb8a020: stur            w1, [x2, #0xb]
    // 0xb8a024: LoadField: r0 = r1->field_f
    //     0xb8a024: ldur            w0, [x1, #0xf]
    // 0xb8a028: DecompressPointer r0
    //     0xb8a028: add             x0, x0, HEAP, lsl #32
    // 0xb8a02c: r3 = LoadClassIdInstr(r0)
    //     0xb8a02c: ldur            x3, [x0, #-1]
    //     0xb8a030: ubfx            x3, x3, #0xc, #0x14
    // 0xb8a034: str             x0, [SP]
    // 0xb8a038: mov             x0, x3
    // 0xb8a03c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb8a03c: movz            x17, #0xc834
    //     0xb8a040: add             lr, x0, x17
    //     0xb8a044: ldr             lr, [x21, lr, lsl #3]
    //     0xb8a048: blr             lr
    // 0xb8a04c: mov             x1, x0
    // 0xb8a050: ldr             x0, [fp, #0x10]
    // 0xb8a054: r2 = LoadInt32Instr(r0)
    //     0xb8a054: sbfx            x2, x0, #1, #0x1f
    //     0xb8a058: tbz             w0, #0, #0xb8a060
    //     0xb8a05c: ldur            x2, [x0, #7]
    // 0xb8a060: r3 = LoadInt32Instr(r1)
    //     0xb8a060: sbfx            x3, x1, #1, #0x1f
    //     0xb8a064: tbz             w1, #0, #0xb8a06c
    //     0xb8a068: ldur            x3, [x1, #7]
    // 0xb8a06c: cmp             x2, x3
    // 0xb8a070: b.ne            #0xb8a088
    // 0xb8a074: r0 = Instance_Padding
    //     0xb8a074: add             x0, PP, #0x35, lsl #12  ; [pp+0x35de8] Obj!Padding@e1e941
    //     0xb8a078: ldr             x0, [x0, #0xde8]
    // 0xb8a07c: LeaveFrame
    //     0xb8a07c: mov             SP, fp
    //     0xb8a080: ldp             fp, lr, [SP], #0x10
    // 0xb8a084: ret
    //     0xb8a084: ret             
    // 0xb8a088: ldur            x1, [fp, #-8]
    // 0xb8a08c: ldur            x2, [fp, #-0x10]
    // 0xb8a090: LoadField: r3 = r1->field_f
    //     0xb8a090: ldur            w3, [x1, #0xf]
    // 0xb8a094: DecompressPointer r3
    //     0xb8a094: add             x3, x3, HEAP, lsl #32
    // 0xb8a098: r1 = LoadClassIdInstr(r3)
    //     0xb8a098: ldur            x1, [x3, #-1]
    //     0xb8a09c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8a0a0: stp             x0, x3, [SP]
    // 0xb8a0a4: mov             x0, x1
    // 0xb8a0a8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8a0a8: movz            x17, #0x3037
    //     0xb8a0ac: movk            x17, #0x1, lsl #16
    //     0xb8a0b0: add             lr, x0, x17
    //     0xb8a0b4: ldr             lr, [x21, lr, lsl #3]
    //     0xb8a0b8: blr             lr
    // 0xb8a0bc: ldur            x2, [fp, #-0x10]
    // 0xb8a0c0: StoreField: r2->field_f = r0
    //     0xb8a0c0: stur            w0, [x2, #0xf]
    //     0xb8a0c4: ldurb           w16, [x2, #-1]
    //     0xb8a0c8: ldurb           w17, [x0, #-1]
    //     0xb8a0cc: and             x16, x17, x16, lsr #2
    //     0xb8a0d0: tst             x16, HEAP, lsr #32
    //     0xb8a0d4: b.eq            #0xb8a0dc
    //     0xb8a0d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb8a0dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8a0dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8a0e0: ldr             x0, [x0, #0x2670]
    //     0xb8a0e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8a0e8: cmp             w0, w16
    //     0xb8a0ec: b.ne            #0xb8a0f8
    //     0xb8a0f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8a0f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8a0f8: r16 = <AppStorage>
    //     0xb8a0f8: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0xb8a0fc: r30 = "app_storage"
    //     0xb8a0fc: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0xb8a100: stp             lr, x16, [SP]
    // 0xb8a104: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8a104: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8a108: r0 = Inst.find()
    //     0xb8a108: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8a10c: stur            x0, [fp, #-8]
    // 0xb8a110: r0 = ReadingPreferenceController()
    //     0xb8a110: bl              #0x8425f4  ; AllocateReadingPreferenceControllerStub -> ReadingPreferenceController (size=0x30)
    // 0xb8a114: mov             x1, x0
    // 0xb8a118: ldur            x2, [fp, #-8]
    // 0xb8a11c: stur            x0, [fp, #-8]
    // 0xb8a120: r0 = ReadingPreferenceController()
    //     0xb8a120: bl              #0x8424a4  ; [package:nuonline/app/modules/setting/reading_preference/controllers/reading_preference_controller.dart] ReadingPreferenceController::ReadingPreferenceController
    // 0xb8a124: r1 = <ReadingPreferenceController>
    //     0xb8a124: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb8a128: ldr             x1, [x1, #0xe10]
    // 0xb8a12c: r0 = GetBuilder()
    //     0xb8a12c: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb8a130: mov             x3, x0
    // 0xb8a134: ldur            x0, [fp, #-8]
    // 0xb8a138: stur            x3, [fp, #-0x18]
    // 0xb8a13c: StoreField: r3->field_3b = r0
    //     0xb8a13c: stur            w0, [x3, #0x3b]
    // 0xb8a140: r0 = true
    //     0xb8a140: add             x0, NULL, #0x20  ; true
    // 0xb8a144: StoreField: r3->field_13 = r0
    //     0xb8a144: stur            w0, [x3, #0x13]
    // 0xb8a148: ldur            x2, [fp, #-0x10]
    // 0xb8a14c: r1 = Function '<anonymous closure>':.
    //     0xb8a14c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35df0] AnonymousClosure: (0xb8a184), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb8a150: ldr             x1, [x1, #0xdf0]
    // 0xb8a154: r0 = AllocateClosure()
    //     0xb8a154: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8a158: mov             x1, x0
    // 0xb8a15c: ldur            x0, [fp, #-0x18]
    // 0xb8a160: StoreField: r0->field_f = r1
    //     0xb8a160: stur            w1, [x0, #0xf]
    // 0xb8a164: r1 = false
    //     0xb8a164: add             x1, NULL, #0x30  ; false
    // 0xb8a168: StoreField: r0->field_1f = r1
    //     0xb8a168: stur            w1, [x0, #0x1f]
    // 0xb8a16c: StoreField: r0->field_23 = r1
    //     0xb8a16c: stur            w1, [x0, #0x23]
    // 0xb8a170: LeaveFrame
    //     0xb8a170: mov             SP, fp
    //     0xb8a174: ldp             fp, lr, [SP], #0x10
    // 0xb8a178: ret
    //     0xb8a178: ret             
    // 0xb8a17c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a17c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a180: b               #0xb8a00c
  }
  [closure] ReadingPreferenceWidget <anonymous closure>(dynamic, ReadingPreferenceController) {
    // ** addr: 0xb8a184, size: 0x48
    // 0xb8a184: EnterFrame
    //     0xb8a184: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a188: mov             fp, SP
    // 0xb8a18c: AllocStack(0x8)
    //     0xb8a18c: sub             SP, SP, #8
    // 0xb8a190: SetupParameters()
    //     0xb8a190: ldr             x0, [fp, #0x18]
    //     0xb8a194: ldur            w2, [x0, #0x17]
    //     0xb8a198: add             x2, x2, HEAP, lsl #32
    // 0xb8a19c: r1 = Function '<anonymous closure>':.
    //     0xb8a19c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35df8] AnonymousClosure: (0xb8a1cc), in [package:nuonline/app/modules/doa/doa_detail/widgets/doa_detail_builder.dart] DoaDetailBuilder::build (0xb89b78)
    //     0xb8a1a0: ldr             x1, [x1, #0xdf8]
    // 0xb8a1a4: r0 = AllocateClosure()
    //     0xb8a1a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8a1a8: r1 = <ReadingPreferenceController>
    //     0xb8a1a8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb8a1ac: ldr             x1, [x1, #0xe10]
    // 0xb8a1b0: stur            x0, [fp, #-8]
    // 0xb8a1b4: r0 = ReadingPreferenceWidget()
    //     0xb8a1b4: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb8a1b8: ldur            x1, [fp, #-8]
    // 0xb8a1bc: StoreField: r0->field_13 = r1
    //     0xb8a1bc: stur            w1, [x0, #0x13]
    // 0xb8a1c0: LeaveFrame
    //     0xb8a1c0: mov             SP, fp
    //     0xb8a1c4: ldp             fp, lr, [SP], #0x10
    // 0xb8a1c8: ret
    //     0xb8a1c8: ret             
  }
  [closure] NDoaListTile <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb8a1cc, size: 0xb8
    // 0xb8a1cc: EnterFrame
    //     0xb8a1cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a1d0: mov             fp, SP
    // 0xb8a1d4: AllocStack(0x28)
    //     0xb8a1d4: sub             SP, SP, #0x28
    // 0xb8a1d8: SetupParameters()
    //     0xb8a1d8: ldr             x0, [fp, #0x20]
    //     0xb8a1dc: ldur            w1, [x0, #0x17]
    //     0xb8a1e0: add             x1, x1, HEAP, lsl #32
    // 0xb8a1e4: LoadField: r0 = r1->field_f
    //     0xb8a1e4: ldur            w0, [x1, #0xf]
    // 0xb8a1e8: DecompressPointer r0
    //     0xb8a1e8: add             x0, x0, HEAP, lsl #32
    // 0xb8a1ec: LoadField: r1 = r0->field_1f
    //     0xb8a1ec: ldur            w1, [x0, #0x1f]
    // 0xb8a1f0: DecompressPointer r1
    //     0xb8a1f0: add             x1, x1, HEAP, lsl #32
    // 0xb8a1f4: stur            x1, [fp, #-0x28]
    // 0xb8a1f8: LoadField: r2 = r0->field_27
    //     0xb8a1f8: ldur            w2, [x0, #0x27]
    // 0xb8a1fc: DecompressPointer r2
    //     0xb8a1fc: add             x2, x2, HEAP, lsl #32
    // 0xb8a200: stur            x2, [fp, #-0x20]
    // 0xb8a204: LoadField: r3 = r0->field_23
    //     0xb8a204: ldur            w3, [x0, #0x23]
    // 0xb8a208: DecompressPointer r3
    //     0xb8a208: add             x3, x3, HEAP, lsl #32
    // 0xb8a20c: stur            x3, [fp, #-0x18]
    // 0xb8a210: LoadField: r4 = r0->field_f
    //     0xb8a210: ldur            x4, [x0, #0xf]
    // 0xb8a214: cmp             x4, #1
    // 0xb8a218: r16 = true
    //     0xb8a218: add             x16, NULL, #0x20  ; true
    // 0xb8a21c: r17 = false
    //     0xb8a21c: add             x17, NULL, #0x30  ; false
    // 0xb8a220: csel            x0, x16, x17, eq
    // 0xb8a224: stur            x0, [fp, #-0x10]
    // 0xb8a228: cmp             x4, #2
    // 0xb8a22c: r16 = true
    //     0xb8a22c: add             x16, NULL, #0x20  ; true
    // 0xb8a230: r17 = false
    //     0xb8a230: add             x17, NULL, #0x30  ; false
    // 0xb8a234: csel            x5, x16, x17, eq
    // 0xb8a238: stur            x5, [fp, #-8]
    // 0xb8a23c: r0 = NDoaListTile()
    //     0xb8a23c: bl              #0xb8a284  ; AllocateNDoaListTileStub -> NDoaListTile (size=0x28)
    // 0xb8a240: ldur            x1, [fp, #-0x28]
    // 0xb8a244: StoreField: r0->field_b = r1
    //     0xb8a244: stur            w1, [x0, #0xb]
    // 0xb8a248: ldur            x1, [fp, #-0x20]
    // 0xb8a24c: StoreField: r0->field_f = r1
    //     0xb8a24c: stur            w1, [x0, #0xf]
    // 0xb8a250: ldur            x1, [fp, #-0x18]
    // 0xb8a254: StoreField: r0->field_13 = r1
    //     0xb8a254: stur            w1, [x0, #0x13]
    // 0xb8a258: ldur            x1, [fp, #-0x10]
    // 0xb8a25c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8a25c: stur            w1, [x0, #0x17]
    // 0xb8a260: ldur            x1, [fp, #-8]
    // 0xb8a264: StoreField: r0->field_1b = r1
    //     0xb8a264: stur            w1, [x0, #0x1b]
    // 0xb8a268: ldr             x1, [fp, #0x18]
    // 0xb8a26c: StoreField: r0->field_1f = r1
    //     0xb8a26c: stur            w1, [x0, #0x1f]
    // 0xb8a270: r1 = Instance_EdgeInsets
    //     0xb8a270: ldr             x1, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb8a274: StoreField: r0->field_23 = r1
    //     0xb8a274: stur            w1, [x0, #0x23]
    // 0xb8a278: LeaveFrame
    //     0xb8a278: mov             SP, fp
    //     0xb8a27c: ldp             fp, lr, [SP], #0x10
    // 0xb8a280: ret
    //     0xb8a280: ret             
  }
}
