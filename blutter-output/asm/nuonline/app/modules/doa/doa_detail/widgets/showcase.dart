// lib: , url: package:nuonline/app/modules/doa/doa_detail/widgets/showcase.dart

// class id: 1050178, size: 0x8
class :: {
}

// class id: 5052, size: 0x28, field offset: 0xc
//   const constructor, 
class NShowcase extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8a290, size: 0x6bc
    // 0xb8a290: EnterFrame
    //     0xb8a290: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a294: mov             fp, SP
    // 0xb8a298: AllocStack(0x80)
    //     0xb8a298: sub             SP, SP, #0x80
    // 0xb8a29c: SetupParameters(NShowcase this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb8a29c: stur            x1, [fp, #-8]
    //     0xb8a2a0: stur            x2, [fp, #-0x10]
    // 0xb8a2a4: CheckStackOverflow
    //     0xb8a2a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a2a8: cmp             SP, x16
    //     0xb8a2ac: b.ls            #0xb8a924
    // 0xb8a2b0: r1 = 2
    //     0xb8a2b0: movz            x1, #0x2
    // 0xb8a2b4: r0 = AllocateContext()
    //     0xb8a2b4: bl              #0xec126c  ; AllocateContextStub
    // 0xb8a2b8: mov             x1, x0
    // 0xb8a2bc: ldur            x0, [fp, #-8]
    // 0xb8a2c0: stur            x1, [fp, #-0x18]
    // 0xb8a2c4: StoreField: r1->field_f = r0
    //     0xb8a2c4: stur            w0, [x1, #0xf]
    // 0xb8a2c8: ldur            x2, [fp, #-0x10]
    // 0xb8a2cc: StoreField: r1->field_13 = r2
    //     0xb8a2cc: stur            w2, [x1, #0x13]
    // 0xb8a2d0: LoadField: r2 = r0->field_b
    //     0xb8a2d0: ldur            w2, [x0, #0xb]
    // 0xb8a2d4: DecompressPointer r2
    //     0xb8a2d4: add             x2, x2, HEAP, lsl #32
    // 0xb8a2d8: stur            x2, [fp, #-0x10]
    // 0xb8a2dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8a2dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8a2e0: ldr             x0, [x0, #0x2670]
    //     0xb8a2e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8a2e8: cmp             w0, w16
    //     0xb8a2ec: b.ne            #0xb8a2f8
    //     0xb8a2f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8a2f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8a2f8: r0 = GetNavigation.size()
    //     0xb8a2f8: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb8a2fc: LoadField: d0 = r0->field_7
    //     0xb8a2fc: ldur            d0, [x0, #7]
    // 0xb8a300: d1 = 1.500000
    //     0xb8a300: fmov            d1, #1.50000000
    // 0xb8a304: fdiv            d2, d0, d1
    // 0xb8a308: stur            d2, [fp, #-0x60]
    // 0xb8a30c: r0 = Radius()
    //     0xb8a30c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8a310: d0 = 8.000000
    //     0xb8a310: fmov            d0, #8.00000000
    // 0xb8a314: stur            x0, [fp, #-0x20]
    // 0xb8a318: StoreField: r0->field_7 = d0
    //     0xb8a318: stur            d0, [x0, #7]
    // 0xb8a31c: StoreField: r0->field_f = d0
    //     0xb8a31c: stur            d0, [x0, #0xf]
    // 0xb8a320: r0 = BorderRadius()
    //     0xb8a320: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8a324: mov             x1, x0
    // 0xb8a328: ldur            x0, [fp, #-0x20]
    // 0xb8a32c: stur            x1, [fp, #-0x28]
    // 0xb8a330: StoreField: r1->field_7 = r0
    //     0xb8a330: stur            w0, [x1, #7]
    // 0xb8a334: StoreField: r1->field_b = r0
    //     0xb8a334: stur            w0, [x1, #0xb]
    // 0xb8a338: StoreField: r1->field_f = r0
    //     0xb8a338: stur            w0, [x1, #0xf]
    // 0xb8a33c: StoreField: r1->field_13 = r0
    //     0xb8a33c: stur            w0, [x1, #0x13]
    // 0xb8a340: r0 = BoxDecoration()
    //     0xb8a340: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8a344: mov             x1, x0
    // 0xb8a348: r0 = Instance_Color
    //     0xb8a348: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb8a34c: stur            x1, [fp, #-0x30]
    // 0xb8a350: StoreField: r1->field_7 = r0
    //     0xb8a350: stur            w0, [x1, #7]
    // 0xb8a354: ldur            x2, [fp, #-0x28]
    // 0xb8a358: StoreField: r1->field_13 = r2
    //     0xb8a358: stur            w2, [x1, #0x13]
    // 0xb8a35c: r2 = Instance_BoxShape
    //     0xb8a35c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8a360: ldr             x2, [x2, #0xca8]
    // 0xb8a364: StoreField: r1->field_23 = r2
    //     0xb8a364: stur            w2, [x1, #0x23]
    // 0xb8a368: ldur            x3, [fp, #-8]
    // 0xb8a36c: LoadField: r4 = r3->field_13
    //     0xb8a36c: ldur            w4, [x3, #0x13]
    // 0xb8a370: DecompressPointer r4
    //     0xb8a370: add             x4, x4, HEAP, lsl #32
    // 0xb8a374: stur            x4, [fp, #-0x20]
    // 0xb8a378: r0 = GetNavigation.textTheme()
    //     0xb8a378: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8a37c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb8a37c: ldur            w3, [x0, #0x17]
    // 0xb8a380: DecompressPointer r3
    //     0xb8a380: add             x3, x3, HEAP, lsl #32
    // 0xb8a384: stur            x3, [fp, #-0x28]
    // 0xb8a388: cmp             w3, NULL
    // 0xb8a38c: b.ne            #0xb8a398
    // 0xb8a390: r2 = Null
    //     0xb8a390: mov             x2, NULL
    // 0xb8a394: b               #0xb8a3c0
    // 0xb8a398: r1 = _ConstMap len:3
    //     0xb8a398: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb8a39c: ldr             x1, [x1, #0xbe8]
    // 0xb8a3a0: r2 = 6
    //     0xb8a3a0: movz            x2, #0x6
    // 0xb8a3a4: r0 = []()
    //     0xb8a3a4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8a3a8: str             x0, [SP]
    // 0xb8a3ac: ldur            x1, [fp, #-0x28]
    // 0xb8a3b0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb8a3b0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb8a3b4: ldr             x4, [x4, #0x228]
    // 0xb8a3b8: r0 = copyWith()
    //     0xb8a3b8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8a3bc: mov             x2, x0
    // 0xb8a3c0: ldur            x0, [fp, #-8]
    // 0xb8a3c4: ldur            x1, [fp, #-0x20]
    // 0xb8a3c8: stur            x2, [fp, #-0x28]
    // 0xb8a3cc: r0 = Text()
    //     0xb8a3cc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8a3d0: mov             x3, x0
    // 0xb8a3d4: ldur            x0, [fp, #-0x20]
    // 0xb8a3d8: stur            x3, [fp, #-0x40]
    // 0xb8a3dc: StoreField: r3->field_b = r0
    //     0xb8a3dc: stur            w0, [x3, #0xb]
    // 0xb8a3e0: ldur            x0, [fp, #-0x28]
    // 0xb8a3e4: StoreField: r3->field_13 = r0
    //     0xb8a3e4: stur            w0, [x3, #0x13]
    // 0xb8a3e8: ldur            x0, [fp, #-8]
    // 0xb8a3ec: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xb8a3ec: ldur            x4, [x0, #0x17]
    // 0xb8a3f0: mov             x2, x4
    // 0xb8a3f4: stur            x4, [fp, #-0x38]
    // 0xb8a3f8: r1 = <int>
    //     0xb8a3f8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb8a3fc: r0 = _GrowableList()
    //     0xb8a3fc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8a400: stur            x0, [fp, #-0x20]
    // 0xb8a404: LoadField: r1 = r0->field_b
    //     0xb8a404: ldur            w1, [x0, #0xb]
    // 0xb8a408: r2 = LoadInt32Instr(r1)
    //     0xb8a408: sbfx            x2, x1, #1, #0x1f
    // 0xb8a40c: LoadField: r1 = r0->field_f
    //     0xb8a40c: ldur            w1, [x0, #0xf]
    // 0xb8a410: DecompressPointer r1
    //     0xb8a410: add             x1, x1, HEAP, lsl #32
    // 0xb8a414: r3 = 0
    //     0xb8a414: movz            x3, #0
    // 0xb8a418: CheckStackOverflow
    //     0xb8a418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a41c: cmp             SP, x16
    //     0xb8a420: b.ls            #0xb8a92c
    // 0xb8a424: cmp             x3, x2
    // 0xb8a428: b.ge            #0xb8a444
    // 0xb8a42c: lsl             x4, x3, #1
    // 0xb8a430: ArrayStore: r1[r3] = r4  ; Unknown_4
    //     0xb8a430: add             x5, x1, x3, lsl #2
    //     0xb8a434: stur            w4, [x5, #0xf]
    // 0xb8a438: add             x4, x3, #1
    // 0xb8a43c: mov             x3, x4
    // 0xb8a440: b               #0xb8a418
    // 0xb8a444: ldur            x3, [fp, #-8]
    // 0xb8a448: ldur            x4, [fp, #-0x38]
    // 0xb8a44c: ldur            x2, [fp, #-0x18]
    // 0xb8a450: r1 = Function '<anonymous closure>':.
    //     0xb8a450: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d28] AnonymousClosure: (0xb8aa1c), in [package:nuonline/app/modules/doa/doa_detail/widgets/showcase.dart] NShowcase::build (0xb8a290)
    //     0xb8a454: ldr             x1, [x1, #0xd28]
    // 0xb8a458: r0 = AllocateClosure()
    //     0xb8a458: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8a45c: r16 = <Container>
    //     0xb8a45c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35d30] TypeArguments: <Container>
    //     0xb8a460: ldr             x16, [x16, #0xd30]
    // 0xb8a464: ldur            lr, [fp, #-0x20]
    // 0xb8a468: stp             lr, x16, [SP, #8]
    // 0xb8a46c: str             x0, [SP]
    // 0xb8a470: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8a470: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8a474: r0 = map()
    //     0xb8a474: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb8a478: LoadField: r1 = r0->field_7
    //     0xb8a478: ldur            w1, [x0, #7]
    // 0xb8a47c: DecompressPointer r1
    //     0xb8a47c: add             x1, x1, HEAP, lsl #32
    // 0xb8a480: mov             x2, x0
    // 0xb8a484: r0 = _GrowableList.of()
    //     0xb8a484: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb8a488: stur            x0, [fp, #-0x20]
    // 0xb8a48c: r0 = Wrap()
    //     0xb8a48c: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb8a490: mov             x1, x0
    // 0xb8a494: r0 = Instance_Axis
    //     0xb8a494: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8a498: stur            x1, [fp, #-0x28]
    // 0xb8a49c: StoreField: r1->field_f = r0
    //     0xb8a49c: stur            w0, [x1, #0xf]
    // 0xb8a4a0: r2 = Instance_WrapAlignment
    //     0xb8a4a0: add             x2, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xb8a4a4: ldr             x2, [x2, #0x610]
    // 0xb8a4a8: StoreField: r1->field_13 = r2
    //     0xb8a4a8: stur            w2, [x1, #0x13]
    // 0xb8a4ac: d0 = 6.000000
    //     0xb8a4ac: fmov            d0, #6.00000000
    // 0xb8a4b0: ArrayStore: r1[0] = d0  ; List_8
    //     0xb8a4b0: stur            d0, [x1, #0x17]
    // 0xb8a4b4: StoreField: r1->field_1f = r2
    //     0xb8a4b4: stur            w2, [x1, #0x1f]
    // 0xb8a4b8: StoreField: r1->field_23 = rZR
    //     0xb8a4b8: stur            xzr, [x1, #0x23]
    // 0xb8a4bc: r2 = Instance_WrapCrossAlignment
    //     0xb8a4bc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xb8a4c0: ldr             x2, [x2, #0x618]
    // 0xb8a4c4: StoreField: r1->field_2b = r2
    //     0xb8a4c4: stur            w2, [x1, #0x2b]
    // 0xb8a4c8: r2 = Instance_VerticalDirection
    //     0xb8a4c8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8a4cc: ldr             x2, [x2, #0x748]
    // 0xb8a4d0: StoreField: r1->field_33 = r2
    //     0xb8a4d0: stur            w2, [x1, #0x33]
    // 0xb8a4d4: r3 = Instance_Clip
    //     0xb8a4d4: add             x3, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8a4d8: ldr             x3, [x3, #0x750]
    // 0xb8a4dc: StoreField: r1->field_37 = r3
    //     0xb8a4dc: stur            w3, [x1, #0x37]
    // 0xb8a4e0: ldur            x4, [fp, #-0x20]
    // 0xb8a4e4: StoreField: r1->field_b = r4
    //     0xb8a4e4: stur            w4, [x1, #0xb]
    // 0xb8a4e8: r0 = Radius()
    //     0xb8a4e8: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8a4ec: d0 = 12.000000
    //     0xb8a4ec: fmov            d0, #12.00000000
    // 0xb8a4f0: stur            x0, [fp, #-0x20]
    // 0xb8a4f4: StoreField: r0->field_7 = d0
    //     0xb8a4f4: stur            d0, [x0, #7]
    // 0xb8a4f8: StoreField: r0->field_f = d0
    //     0xb8a4f8: stur            d0, [x0, #0xf]
    // 0xb8a4fc: r0 = BorderRadius()
    //     0xb8a4fc: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8a500: mov             x3, x0
    // 0xb8a504: ldur            x0, [fp, #-0x20]
    // 0xb8a508: stur            x3, [fp, #-0x48]
    // 0xb8a50c: StoreField: r3->field_7 = r0
    //     0xb8a50c: stur            w0, [x3, #7]
    // 0xb8a510: StoreField: r3->field_b = r0
    //     0xb8a510: stur            w0, [x3, #0xb]
    // 0xb8a514: StoreField: r3->field_f = r0
    //     0xb8a514: stur            w0, [x3, #0xf]
    // 0xb8a518: StoreField: r3->field_13 = r0
    //     0xb8a518: stur            w0, [x3, #0x13]
    // 0xb8a51c: r1 = _ConstMap len:10
    //     0xb8a51c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb8a520: ldr             x1, [x1, #0xc08]
    // 0xb8a524: r2 = 100
    //     0xb8a524: movz            x2, #0x64
    // 0xb8a528: r0 = []()
    //     0xb8a528: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8a52c: stur            x0, [fp, #-0x20]
    // 0xb8a530: r0 = BoxDecoration()
    //     0xb8a530: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8a534: mov             x1, x0
    // 0xb8a538: ldur            x0, [fp, #-0x20]
    // 0xb8a53c: stur            x1, [fp, #-0x50]
    // 0xb8a540: StoreField: r1->field_7 = r0
    //     0xb8a540: stur            w0, [x1, #7]
    // 0xb8a544: ldur            x0, [fp, #-0x48]
    // 0xb8a548: StoreField: r1->field_13 = r0
    //     0xb8a548: stur            w0, [x1, #0x13]
    // 0xb8a54c: r0 = Instance_BoxShape
    //     0xb8a54c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8a550: ldr             x0, [x0, #0xca8]
    // 0xb8a554: StoreField: r1->field_23 = r0
    //     0xb8a554: stur            w0, [x1, #0x23]
    // 0xb8a558: ldur            x0, [fp, #-0x38]
    // 0xb8a55c: sub             x2, x0, #1
    // 0xb8a560: ldur            x0, [fp, #-8]
    // 0xb8a564: LoadField: r3 = r0->field_1f
    //     0xb8a564: ldur            x3, [x0, #0x1f]
    // 0xb8a568: cmp             x2, x3
    // 0xb8a56c: b.ne            #0xb8a57c
    // 0xb8a570: r2 = "Selesai"
    //     0xb8a570: add             x2, PP, #0x35, lsl #12  ; [pp+0x35d38] "Selesai"
    //     0xb8a574: ldr             x2, [x2, #0xd38]
    // 0xb8a578: b               #0xb8a584
    // 0xb8a57c: r2 = "Lanjut"
    //     0xb8a57c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c8a8] "Lanjut"
    //     0xb8a580: ldr             x2, [x2, #0x8a8]
    // 0xb8a584: stur            x2, [fp, #-0x20]
    // 0xb8a588: r0 = GetNavigation.textTheme()
    //     0xb8a588: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8a58c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8a58c: ldur            w1, [x0, #0x17]
    // 0xb8a590: DecompressPointer r1
    //     0xb8a590: add             x1, x1, HEAP, lsl #32
    // 0xb8a594: cmp             w1, NULL
    // 0xb8a598: b.ne            #0xb8a5a4
    // 0xb8a59c: r6 = Null
    //     0xb8a59c: mov             x6, NULL
    // 0xb8a5a0: b               #0xb8a5c0
    // 0xb8a5a4: r16 = Instance_MaterialColor
    //     0xb8a5a4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb8a5a8: ldr             x16, [x16, #0xcf0]
    // 0xb8a5ac: str             x16, [SP]
    // 0xb8a5b0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb8a5b0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb8a5b4: ldr             x4, [x4, #0x228]
    // 0xb8a5b8: r0 = copyWith()
    //     0xb8a5b8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8a5bc: mov             x6, x0
    // 0xb8a5c0: ldur            x1, [fp, #-8]
    // 0xb8a5c4: ldur            x5, [fp, #-0x10]
    // 0xb8a5c8: ldur            d0, [fp, #-0x60]
    // 0xb8a5cc: ldur            x4, [fp, #-0x40]
    // 0xb8a5d0: ldur            x3, [fp, #-0x28]
    // 0xb8a5d4: ldur            x0, [fp, #-0x50]
    // 0xb8a5d8: ldur            x2, [fp, #-0x20]
    // 0xb8a5dc: stur            x6, [fp, #-0x48]
    // 0xb8a5e0: r0 = Text()
    //     0xb8a5e0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8a5e4: mov             x1, x0
    // 0xb8a5e8: ldur            x0, [fp, #-0x20]
    // 0xb8a5ec: stur            x1, [fp, #-0x58]
    // 0xb8a5f0: StoreField: r1->field_b = r0
    //     0xb8a5f0: stur            w0, [x1, #0xb]
    // 0xb8a5f4: ldur            x0, [fp, #-0x48]
    // 0xb8a5f8: StoreField: r1->field_13 = r0
    //     0xb8a5f8: stur            w0, [x1, #0x13]
    // 0xb8a5fc: r0 = Padding()
    //     0xb8a5fc: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a600: mov             x1, x0
    // 0xb8a604: r0 = Instance_EdgeInsets
    //     0xb8a604: add             x0, PP, #0x31, lsl #12  ; [pp+0x31530] Obj!EdgeInsets@e12251
    //     0xb8a608: ldr             x0, [x0, #0x530]
    // 0xb8a60c: stur            x1, [fp, #-0x20]
    // 0xb8a610: StoreField: r1->field_f = r0
    //     0xb8a610: stur            w0, [x1, #0xf]
    // 0xb8a614: ldur            x0, [fp, #-0x58]
    // 0xb8a618: StoreField: r1->field_b = r0
    //     0xb8a618: stur            w0, [x1, #0xb]
    // 0xb8a61c: r0 = DecoratedBox()
    //     0xb8a61c: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb8a620: mov             x1, x0
    // 0xb8a624: ldur            x0, [fp, #-0x50]
    // 0xb8a628: stur            x1, [fp, #-0x48]
    // 0xb8a62c: StoreField: r1->field_f = r0
    //     0xb8a62c: stur            w0, [x1, #0xf]
    // 0xb8a630: r0 = Instance_DecorationPosition
    //     0xb8a630: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb8a634: ldr             x0, [x0, #0xb28]
    // 0xb8a638: StoreField: r1->field_13 = r0
    //     0xb8a638: stur            w0, [x1, #0x13]
    // 0xb8a63c: ldur            x0, [fp, #-0x20]
    // 0xb8a640: StoreField: r1->field_b = r0
    //     0xb8a640: stur            w0, [x1, #0xb]
    // 0xb8a644: r0 = GestureDetector()
    //     0xb8a644: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb8a648: ldur            x2, [fp, #-0x18]
    // 0xb8a64c: r1 = Function '<anonymous closure>':.
    //     0xb8a64c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d40] AnonymousClosure: (0xb8a958), in [package:nuonline/app/modules/doa/doa_detail/widgets/showcase.dart] NShowcase::build (0xb8a290)
    //     0xb8a650: ldr             x1, [x1, #0xd40]
    // 0xb8a654: stur            x0, [fp, #-0x18]
    // 0xb8a658: r0 = AllocateClosure()
    //     0xb8a658: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8a65c: ldur            x16, [fp, #-0x48]
    // 0xb8a660: stp             x16, x0, [SP]
    // 0xb8a664: ldur            x1, [fp, #-0x18]
    // 0xb8a668: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb8a668: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb8a66c: ldr             x4, [x4, #0x7d0]
    // 0xb8a670: r0 = GestureDetector()
    //     0xb8a670: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb8a674: r1 = Null
    //     0xb8a674: mov             x1, NULL
    // 0xb8a678: r2 = 4
    //     0xb8a678: movz            x2, #0x4
    // 0xb8a67c: r0 = AllocateArray()
    //     0xb8a67c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8a680: mov             x2, x0
    // 0xb8a684: ldur            x0, [fp, #-0x28]
    // 0xb8a688: stur            x2, [fp, #-0x20]
    // 0xb8a68c: StoreField: r2->field_f = r0
    //     0xb8a68c: stur            w0, [x2, #0xf]
    // 0xb8a690: ldur            x0, [fp, #-0x18]
    // 0xb8a694: StoreField: r2->field_13 = r0
    //     0xb8a694: stur            w0, [x2, #0x13]
    // 0xb8a698: r1 = <Widget>
    //     0xb8a698: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8a69c: r0 = AllocateGrowableArray()
    //     0xb8a69c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8a6a0: mov             x1, x0
    // 0xb8a6a4: ldur            x0, [fp, #-0x20]
    // 0xb8a6a8: stur            x1, [fp, #-0x18]
    // 0xb8a6ac: StoreField: r1->field_f = r0
    //     0xb8a6ac: stur            w0, [x1, #0xf]
    // 0xb8a6b0: r0 = 4
    //     0xb8a6b0: movz            x0, #0x4
    // 0xb8a6b4: StoreField: r1->field_b = r0
    //     0xb8a6b4: stur            w0, [x1, #0xb]
    // 0xb8a6b8: r0 = Row()
    //     0xb8a6b8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8a6bc: mov             x3, x0
    // 0xb8a6c0: r0 = Instance_Axis
    //     0xb8a6c0: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8a6c4: stur            x3, [fp, #-0x20]
    // 0xb8a6c8: StoreField: r3->field_f = r0
    //     0xb8a6c8: stur            w0, [x3, #0xf]
    // 0xb8a6cc: r0 = Instance_MainAxisAlignment
    //     0xb8a6cc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb8a6d0: ldr             x0, [x0, #0xae8]
    // 0xb8a6d4: StoreField: r3->field_13 = r0
    //     0xb8a6d4: stur            w0, [x3, #0x13]
    // 0xb8a6d8: r0 = Instance_MainAxisSize
    //     0xb8a6d8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8a6dc: ldr             x0, [x0, #0x738]
    // 0xb8a6e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb8a6e0: stur            w0, [x3, #0x17]
    // 0xb8a6e4: r1 = Instance_CrossAxisAlignment
    //     0xb8a6e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8a6e8: ldr             x1, [x1, #0x740]
    // 0xb8a6ec: StoreField: r3->field_1b = r1
    //     0xb8a6ec: stur            w1, [x3, #0x1b]
    // 0xb8a6f0: r4 = Instance_VerticalDirection
    //     0xb8a6f0: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8a6f4: ldr             x4, [x4, #0x748]
    // 0xb8a6f8: StoreField: r3->field_23 = r4
    //     0xb8a6f8: stur            w4, [x3, #0x23]
    // 0xb8a6fc: r5 = Instance_Clip
    //     0xb8a6fc: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8a700: ldr             x5, [x5, #0x750]
    // 0xb8a704: StoreField: r3->field_2b = r5
    //     0xb8a704: stur            w5, [x3, #0x2b]
    // 0xb8a708: StoreField: r3->field_2f = rZR
    //     0xb8a708: stur            xzr, [x3, #0x2f]
    // 0xb8a70c: ldur            x1, [fp, #-0x18]
    // 0xb8a710: StoreField: r3->field_b = r1
    //     0xb8a710: stur            w1, [x3, #0xb]
    // 0xb8a714: r1 = Null
    //     0xb8a714: mov             x1, NULL
    // 0xb8a718: r2 = 6
    //     0xb8a718: movz            x2, #0x6
    // 0xb8a71c: r0 = AllocateArray()
    //     0xb8a71c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8a720: mov             x2, x0
    // 0xb8a724: ldur            x0, [fp, #-0x40]
    // 0xb8a728: stur            x2, [fp, #-0x18]
    // 0xb8a72c: StoreField: r2->field_f = r0
    //     0xb8a72c: stur            w0, [x2, #0xf]
    // 0xb8a730: r16 = Instance_SizedBox
    //     0xb8a730: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8a734: ldr             x16, [x16, #0xfe8]
    // 0xb8a738: StoreField: r2->field_13 = r16
    //     0xb8a738: stur            w16, [x2, #0x13]
    // 0xb8a73c: ldur            x0, [fp, #-0x20]
    // 0xb8a740: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8a740: stur            w0, [x2, #0x17]
    // 0xb8a744: r1 = <Widget>
    //     0xb8a744: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8a748: r0 = AllocateGrowableArray()
    //     0xb8a748: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8a74c: mov             x1, x0
    // 0xb8a750: ldur            x0, [fp, #-0x18]
    // 0xb8a754: stur            x1, [fp, #-0x20]
    // 0xb8a758: StoreField: r1->field_f = r0
    //     0xb8a758: stur            w0, [x1, #0xf]
    // 0xb8a75c: r0 = 6
    //     0xb8a75c: movz            x0, #0x6
    // 0xb8a760: StoreField: r1->field_b = r0
    //     0xb8a760: stur            w0, [x1, #0xb]
    // 0xb8a764: r0 = Column()
    //     0xb8a764: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8a768: mov             x1, x0
    // 0xb8a76c: r0 = Instance_Axis
    //     0xb8a76c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8a770: stur            x1, [fp, #-0x28]
    // 0xb8a774: StoreField: r1->field_f = r0
    //     0xb8a774: stur            w0, [x1, #0xf]
    // 0xb8a778: r0 = Instance_MainAxisAlignment
    //     0xb8a778: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8a77c: ldr             x0, [x0, #0x730]
    // 0xb8a780: StoreField: r1->field_13 = r0
    //     0xb8a780: stur            w0, [x1, #0x13]
    // 0xb8a784: r0 = Instance_MainAxisSize
    //     0xb8a784: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8a788: ldr             x0, [x0, #0x738]
    // 0xb8a78c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8a78c: stur            w0, [x1, #0x17]
    // 0xb8a790: r0 = Instance_CrossAxisAlignment
    //     0xb8a790: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb8a794: ldr             x0, [x0, #0x68]
    // 0xb8a798: StoreField: r1->field_1b = r0
    //     0xb8a798: stur            w0, [x1, #0x1b]
    // 0xb8a79c: r0 = Instance_VerticalDirection
    //     0xb8a79c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8a7a0: ldr             x0, [x0, #0x748]
    // 0xb8a7a4: StoreField: r1->field_23 = r0
    //     0xb8a7a4: stur            w0, [x1, #0x23]
    // 0xb8a7a8: r0 = Instance_Clip
    //     0xb8a7a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8a7ac: ldr             x0, [x0, #0x750]
    // 0xb8a7b0: StoreField: r1->field_2b = r0
    //     0xb8a7b0: stur            w0, [x1, #0x2b]
    // 0xb8a7b4: StoreField: r1->field_2f = rZR
    //     0xb8a7b4: stur            xzr, [x1, #0x2f]
    // 0xb8a7b8: ldur            x0, [fp, #-0x20]
    // 0xb8a7bc: StoreField: r1->field_b = r0
    //     0xb8a7bc: stur            w0, [x1, #0xb]
    // 0xb8a7c0: ldur            d0, [fp, #-0x60]
    // 0xb8a7c4: r0 = inline_Allocate_Double()
    //     0xb8a7c4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb8a7c8: add             x0, x0, #0x10
    //     0xb8a7cc: cmp             x2, x0
    //     0xb8a7d0: b.ls            #0xb8a934
    //     0xb8a7d4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb8a7d8: sub             x0, x0, #0xf
    //     0xb8a7dc: movz            x2, #0xe15c
    //     0xb8a7e0: movk            x2, #0x3, lsl #16
    //     0xb8a7e4: stur            x2, [x0, #-1]
    // 0xb8a7e8: StoreField: r0->field_7 = d0
    //     0xb8a7e8: stur            d0, [x0, #7]
    // 0xb8a7ec: stur            x0, [fp, #-0x18]
    // 0xb8a7f0: r0 = Container()
    //     0xb8a7f0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8a7f4: stur            x0, [fp, #-0x20]
    // 0xb8a7f8: ldur            x16, [fp, #-0x18]
    // 0xb8a7fc: r30 = Instance_EdgeInsets
    //     0xb8a7fc: add             lr, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb8a800: ldr             lr, [lr, #0x768]
    // 0xb8a804: stp             lr, x16, [SP, #0x10]
    // 0xb8a808: ldur            x16, [fp, #-0x30]
    // 0xb8a80c: ldur            lr, [fp, #-0x28]
    // 0xb8a810: stp             lr, x16, [SP]
    // 0xb8a814: mov             x1, x0
    // 0xb8a818: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb8a818: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f650] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb8a81c: ldr             x4, [x4, #0x650]
    // 0xb8a820: r0 = Container()
    //     0xb8a820: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8a824: r0 = Padding()
    //     0xb8a824: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a828: mov             x1, x0
    // 0xb8a82c: r0 = Instance_EdgeInsets
    //     0xb8a82c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35d48] Obj!EdgeInsets@e12281
    //     0xb8a830: ldr             x0, [x0, #0xd48]
    // 0xb8a834: stur            x1, [fp, #-0x18]
    // 0xb8a838: StoreField: r1->field_f = r0
    //     0xb8a838: stur            w0, [x1, #0xf]
    // 0xb8a83c: ldur            x0, [fp, #-0x20]
    // 0xb8a840: StoreField: r1->field_b = r0
    //     0xb8a840: stur            w0, [x1, #0xb]
    // 0xb8a844: r0 = GetNavigation.size()
    //     0xb8a844: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb8a848: LoadField: d0 = r0->field_7
    //     0xb8a848: ldur            d0, [x0, #7]
    // 0xb8a84c: d1 = 1.500000
    //     0xb8a84c: fmov            d1, #1.50000000
    // 0xb8a850: fdiv            d2, d0, d1
    // 0xb8a854: ldur            x0, [fp, #-8]
    // 0xb8a858: stur            d2, [fp, #-0x60]
    // 0xb8a85c: LoadField: r1 = r0->field_f
    //     0xb8a85c: ldur            w1, [x0, #0xf]
    // 0xb8a860: DecompressPointer r1
    //     0xb8a860: add             x1, x1, HEAP, lsl #32
    // 0xb8a864: stur            x1, [fp, #-0x20]
    // 0xb8a868: r0 = Showcase()
    //     0xb8a868: bl              #0xb8a94c  ; AllocateShowcaseStub -> Showcase (size=0xc4)
    // 0xb8a86c: ldur            x1, [fp, #-0x10]
    // 0xb8a870: StoreField: r0->field_b = r1
    //     0xb8a870: stur            w1, [x0, #0xb]
    // 0xb8a874: d0 = 50.000000
    //     0xb8a874: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xb8a878: StoreField: r0->field_4f = d0
    //     0xb8a878: stur            d0, [x0, #0x4f]
    // 0xb8a87c: ldur            d0, [fp, #-0x60]
    // 0xb8a880: StoreField: r0->field_57 = d0
    //     0xb8a880: stur            d0, [x0, #0x57]
    // 0xb8a884: ldur            x1, [fp, #-0x18]
    // 0xb8a888: StoreField: r0->field_3f = r1
    //     0xb8a888: stur            w1, [x0, #0x3f]
    // 0xb8a88c: ldur            x1, [fp, #-0x20]
    // 0xb8a890: StoreField: r0->field_f = r1
    //     0xb8a890: stur            w1, [x0, #0xf]
    // 0xb8a894: r1 = Instance_RoundedRectangleBorder
    //     0xb8a894: ldr             x1, [PP, #0x5700]  ; [pp+0x5700] Obj!RoundedRectangleBorder@e14681
    // 0xb8a898: StoreField: r0->field_1f = r1
    //     0xb8a898: stur            w1, [x0, #0x1f]
    // 0xb8a89c: r1 = Instance_Color
    //     0xb8a89c: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d218] Obj!Color@e28bc1
    //     0xb8a8a0: ldr             x1, [x1, #0x218]
    // 0xb8a8a4: StoreField: r0->field_33 = r1
    //     0xb8a8a4: stur            w1, [x0, #0x33]
    // 0xb8a8a8: d0 = 0.750000
    //     0xb8a8a8: fmov            d0, #0.75000000
    // 0xb8a8ac: StoreField: r0->field_37 = d0
    //     0xb8a8ac: stur            d0, [x0, #0x37]
    // 0xb8a8b0: r1 = Instance_Duration
    //     0xb8a8b0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28130] Obj!Duration@e3a131
    //     0xb8a8b4: ldr             x1, [x1, #0x130]
    // 0xb8a8b8: StoreField: r0->field_5f = r1
    //     0xb8a8b8: stur            w1, [x0, #0x5f]
    // 0xb8a8bc: r1 = Instance_EdgeInsets
    //     0xb8a8bc: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb8a8c0: StoreField: r0->field_77 = r1
    //     0xb8a8c0: stur            w1, [x0, #0x77]
    // 0xb8a8c4: r1 = false
    //     0xb8a8c4: add             x1, NULL, #0x30  ; false
    // 0xb8a8c8: StoreField: r0->field_8b = r1
    //     0xb8a8c8: stur            w1, [x0, #0x8b]
    // 0xb8a8cc: StoreField: r0->field_b7 = r1
    //     0xb8a8cc: stur            w1, [x0, #0xb7]
    // 0xb8a8d0: d0 = 7.000000
    //     0xb8a8d0: fmov            d0, #7.00000000
    // 0xb8a8d4: StoreField: r0->field_bb = d0
    //     0xb8a8d4: stur            d0, [x0, #0xbb]
    // 0xb8a8d8: StoreField: r0->field_4b = r1
    //     0xb8a8d8: stur            w1, [x0, #0x4b]
    // 0xb8a8dc: r1 = Instance_Duration
    //     0xb8a8dc: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xb8a8e0: ldr             x1, [x1, #0x9c0]
    // 0xb8a8e4: StoreField: r0->field_93 = r1
    //     0xb8a8e4: stur            w1, [x0, #0x93]
    // 0xb8a8e8: r1 = Instance__DecelerateCurve
    //     0xb8a8e8: ldr             x1, [PP, #0x4e48]  ; [pp+0x4e48] Obj!_DecelerateCurve@e14cc1
    // 0xb8a8ec: StoreField: r0->field_97 = r1
    //     0xb8a8ec: stur            w1, [x0, #0x97]
    // 0xb8a8f0: r1 = Instance_TextAlign
    //     0xb8a8f0: ldr             x1, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb8a8f4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8a8f4: stur            w1, [x0, #0x17]
    // 0xb8a8f8: StoreField: r0->field_87 = r1
    //     0xb8a8f8: stur            w1, [x0, #0x87]
    // 0xb8a8fc: r1 = Instance_Color
    //     0xb8a8fc: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb8a900: StoreField: r0->field_43 = r1
    //     0xb8a900: stur            w1, [x0, #0x43]
    // 0xb8a904: r1 = Instance_Color
    //     0xb8a904: ldr             x1, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0xb8a908: StoreField: r0->field_47 = r1
    //     0xb8a908: stur            w1, [x0, #0x47]
    // 0xb8a90c: r1 = Instance_EdgeInsets
    //     0xb8a90c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f648] Obj!EdgeInsets@e12761
    //     0xb8a910: ldr             x1, [x1, #0x648]
    // 0xb8a914: StoreField: r0->field_2f = r1
    //     0xb8a914: stur            w1, [x0, #0x2f]
    // 0xb8a918: LeaveFrame
    //     0xb8a918: mov             SP, fp
    //     0xb8a91c: ldp             fp, lr, [SP], #0x10
    // 0xb8a920: ret
    //     0xb8a920: ret             
    // 0xb8a924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a928: b               #0xb8a2b0
    // 0xb8a92c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8a92c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8a930: b               #0xb8a424
    // 0xb8a934: SaveReg d0
    //     0xb8a934: str             q0, [SP, #-0x10]!
    // 0xb8a938: SaveReg r1
    //     0xb8a938: str             x1, [SP, #-8]!
    // 0xb8a93c: r0 = AllocateDouble()
    //     0xb8a93c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb8a940: RestoreReg r1
    //     0xb8a940: ldr             x1, [SP], #8
    // 0xb8a944: RestoreReg d0
    //     0xb8a944: ldr             q0, [SP], #0x10
    // 0xb8a948: b               #0xb8a7e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8a958, size: 0xc4
    // 0xb8a958: EnterFrame
    //     0xb8a958: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a95c: mov             fp, SP
    // 0xb8a960: AllocStack(0x10)
    //     0xb8a960: sub             SP, SP, #0x10
    // 0xb8a964: SetupParameters()
    //     0xb8a964: ldr             x0, [fp, #0x10]
    //     0xb8a968: ldur            w2, [x0, #0x17]
    //     0xb8a96c: add             x2, x2, HEAP, lsl #32
    //     0xb8a970: stur            x2, [fp, #-8]
    // 0xb8a974: CheckStackOverflow
    //     0xb8a974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a978: cmp             SP, x16
    //     0xb8a97c: b.ls            #0xb8aa0c
    // 0xb8a980: LoadField: r1 = r2->field_13
    //     0xb8a980: ldur            w1, [x2, #0x13]
    // 0xb8a984: DecompressPointer r1
    //     0xb8a984: add             x1, x1, HEAP, lsl #32
    // 0xb8a988: r0 = of()
    //     0xb8a988: bl              #0x91e504  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::of
    // 0xb8a98c: LoadField: r2 = r0->field_13
    //     0xb8a98c: ldur            w2, [x0, #0x13]
    // 0xb8a990: DecompressPointer r2
    //     0xb8a990: add             x2, x2, HEAP, lsl #32
    // 0xb8a994: ldur            x0, [fp, #-8]
    // 0xb8a998: stur            x2, [fp, #-0x10]
    // 0xb8a99c: LoadField: r1 = r0->field_13
    //     0xb8a99c: ldur            w1, [x0, #0x13]
    // 0xb8a9a0: DecompressPointer r1
    //     0xb8a9a0: add             x1, x1, HEAP, lsl #32
    // 0xb8a9a4: r0 = of()
    //     0xb8a9a4: bl              #0x91e504  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::of
    // 0xb8a9a8: mov             x3, x0
    // 0xb8a9ac: ldur            x2, [fp, #-0x10]
    // 0xb8a9b0: cmp             w2, NULL
    // 0xb8a9b4: b.eq            #0xb8aa14
    // 0xb8a9b8: ldur            x0, [fp, #-8]
    // 0xb8a9bc: LoadField: r1 = r0->field_f
    //     0xb8a9bc: ldur            w1, [x0, #0xf]
    // 0xb8a9c0: DecompressPointer r1
    //     0xb8a9c0: add             x1, x1, HEAP, lsl #32
    // 0xb8a9c4: LoadField: r4 = r1->field_1f
    //     0xb8a9c4: ldur            x4, [x1, #0x1f]
    // 0xb8a9c8: LoadField: r0 = r2->field_b
    //     0xb8a9c8: ldur            w0, [x2, #0xb]
    // 0xb8a9cc: r1 = LoadInt32Instr(r0)
    //     0xb8a9cc: sbfx            x1, x0, #1, #0x1f
    // 0xb8a9d0: mov             x0, x1
    // 0xb8a9d4: mov             x1, x4
    // 0xb8a9d8: cmp             x1, x0
    // 0xb8a9dc: b.hs            #0xb8aa18
    // 0xb8a9e0: LoadField: r0 = r2->field_f
    //     0xb8a9e0: ldur            w0, [x2, #0xf]
    // 0xb8a9e4: DecompressPointer r0
    //     0xb8a9e4: add             x0, x0, HEAP, lsl #32
    // 0xb8a9e8: ArrayLoad: r2 = r0[r4]  ; Unknown_4
    //     0xb8a9e8: add             x16, x0, x4, lsl #2
    //     0xb8a9ec: ldur            w2, [x16, #0xf]
    // 0xb8a9f0: DecompressPointer r2
    //     0xb8a9f0: add             x2, x2, HEAP, lsl #32
    // 0xb8a9f4: mov             x1, x3
    // 0xb8a9f8: r0 = completed()
    //     0xb8a9f8: bl              #0xa4d3cc  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::completed
    // 0xb8a9fc: r0 = Null
    //     0xb8a9fc: mov             x0, NULL
    // 0xb8aa00: LeaveFrame
    //     0xb8aa00: mov             SP, fp
    //     0xb8aa04: ldp             fp, lr, [SP], #0x10
    // 0xb8aa08: ret
    //     0xb8aa08: ret             
    // 0xb8aa0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8aa0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8aa10: b               #0xb8a980
    // 0xb8aa14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8aa14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8aa18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8aa18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, int) {
    // ** addr: 0xb8aa1c, size: 0xcc
    // 0xb8aa1c: EnterFrame
    //     0xb8aa1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8aa20: mov             fp, SP
    // 0xb8aa24: AllocStack(0x28)
    //     0xb8aa24: sub             SP, SP, #0x28
    // 0xb8aa28: SetupParameters()
    //     0xb8aa28: ldr             x0, [fp, #0x18]
    //     0xb8aa2c: ldur            w1, [x0, #0x17]
    //     0xb8aa30: add             x1, x1, HEAP, lsl #32
    // 0xb8aa34: CheckStackOverflow
    //     0xb8aa34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8aa38: cmp             SP, x16
    //     0xb8aa3c: b.ls            #0xb8aae0
    // 0xb8aa40: LoadField: r0 = r1->field_f
    //     0xb8aa40: ldur            w0, [x1, #0xf]
    // 0xb8aa44: DecompressPointer r0
    //     0xb8aa44: add             x0, x0, HEAP, lsl #32
    // 0xb8aa48: LoadField: r1 = r0->field_1f
    //     0xb8aa48: ldur            x1, [x0, #0x1f]
    // 0xb8aa4c: ldr             x0, [fp, #0x10]
    // 0xb8aa50: r2 = LoadInt32Instr(r0)
    //     0xb8aa50: sbfx            x2, x0, #1, #0x1f
    //     0xb8aa54: tbz             w0, #0, #0xb8aa5c
    //     0xb8aa58: ldur            x2, [x0, #7]
    // 0xb8aa5c: cmp             x2, x1
    // 0xb8aa60: b.ne            #0xb8aa70
    // 0xb8aa64: r0 = Instance_MaterialColor
    //     0xb8aa64: add             x0, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb8aa68: ldr             x0, [x0, #0xcf0]
    // 0xb8aa6c: b               #0xb8aa78
    // 0xb8aa70: r0 = Instance_Color
    //     0xb8aa70: add             x0, PP, #0x35, lsl #12  ; [pp+0x35d68] Obj!Color@e2b3b1
    //     0xb8aa74: ldr             x0, [x0, #0xd68]
    // 0xb8aa78: stur            x0, [fp, #-8]
    // 0xb8aa7c: r0 = BoxDecoration()
    //     0xb8aa7c: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8aa80: mov             x1, x0
    // 0xb8aa84: ldur            x0, [fp, #-8]
    // 0xb8aa88: stur            x1, [fp, #-0x10]
    // 0xb8aa8c: StoreField: r1->field_7 = r0
    //     0xb8aa8c: stur            w0, [x1, #7]
    // 0xb8aa90: r0 = Instance_BoxShape
    //     0xb8aa90: add             x0, PP, #0x34, lsl #12  ; [pp+0x349f0] Obj!BoxShape@e35e21
    //     0xb8aa94: ldr             x0, [x0, #0x9f0]
    // 0xb8aa98: StoreField: r1->field_23 = r0
    //     0xb8aa98: stur            w0, [x1, #0x23]
    // 0xb8aa9c: r0 = Container()
    //     0xb8aa9c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8aaa0: stur            x0, [fp, #-8]
    // 0xb8aaa4: r16 = 6.000000
    //     0xb8aaa4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xb8aaa8: ldr             x16, [x16, #0xd70]
    // 0xb8aaac: r30 = 6.000000
    //     0xb8aaac: add             lr, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xb8aab0: ldr             lr, [lr, #0xd70]
    // 0xb8aab4: stp             lr, x16, [SP, #8]
    // 0xb8aab8: ldur            x16, [fp, #-0x10]
    // 0xb8aabc: str             x16, [SP]
    // 0xb8aac0: mov             x1, x0
    // 0xb8aac4: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb8aac4: add             x4, PP, #0x35, lsl #12  ; [pp+0x35d78] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb8aac8: ldr             x4, [x4, #0xd78]
    // 0xb8aacc: r0 = Container()
    //     0xb8aacc: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8aad0: ldur            x0, [fp, #-8]
    // 0xb8aad4: LeaveFrame
    //     0xb8aad4: mov             SP, fp
    //     0xb8aad8: ldp             fp, lr, [SP], #0x10
    // 0xb8aadc: ret
    //     0xb8aadc: ret             
    // 0xb8aae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8aae0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8aae4: b               #0xb8aa40
  }
}
