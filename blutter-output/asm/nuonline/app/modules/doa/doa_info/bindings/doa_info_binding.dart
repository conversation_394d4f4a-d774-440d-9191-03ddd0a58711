// lib: , url: package:nuonline/app/modules/doa/doa_info/bindings/doa_info_binding.dart

// class id: 1050179, size: 0x8
class :: {
}

// class id: 2184, size: 0x8, field offset: 0x8
class DoaInfoBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80f9e0, size: 0x70
    // 0x80f9e0: EnterFrame
    //     0x80f9e0: stp             fp, lr, [SP, #-0x10]!
    //     0x80f9e4: mov             fp, SP
    // 0x80f9e8: AllocStack(0x10)
    //     0x80f9e8: sub             SP, SP, #0x10
    // 0x80f9ec: CheckStackOverflow
    //     0x80f9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f9f0: cmp             SP, x16
    //     0x80f9f4: b.ls            #0x80fa48
    // 0x80f9f8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f9f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f9fc: ldr             x0, [x0, #0x2670]
    //     0x80fa00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80fa04: cmp             w0, w16
    //     0x80fa08: b.ne            #0x80fa14
    //     0x80fa0c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80fa10: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80fa14: r1 = Function '<anonymous closure>':.
    //     0x80fa14: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cf0] AnonymousClosure: (0x80fa50), in [package:nuonline/app/modules/doa/doa_info/bindings/doa_info_binding.dart] DoaInfoBinding::dependencies (0x80f9e0)
    //     0x80fa18: ldr             x1, [x1, #0xcf0]
    // 0x80fa1c: r2 = Null
    //     0x80fa1c: mov             x2, NULL
    // 0x80fa20: r0 = AllocateClosure()
    //     0x80fa20: bl              #0xec1630  ; AllocateClosureStub
    // 0x80fa24: r16 = <DoaInfoController>
    //     0x80fa24: add             x16, PP, #0x24, lsl #12  ; [pp+0x24c18] TypeArguments: <DoaInfoController>
    //     0x80fa28: ldr             x16, [x16, #0xc18]
    // 0x80fa2c: stp             x0, x16, [SP]
    // 0x80fa30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80fa30: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80fa34: r0 = Inst.lazyPut()
    //     0x80fa34: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80fa38: r0 = Null
    //     0x80fa38: mov             x0, NULL
    // 0x80fa3c: LeaveFrame
    //     0x80fa3c: mov             SP, fp
    //     0x80fa40: ldp             fp, lr, [SP], #0x10
    // 0x80fa44: ret
    //     0x80fa44: ret             
    // 0x80fa48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80fa48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80fa4c: b               #0x80f9f8
  }
  [closure] DoaInfoController <anonymous closure>(dynamic) {
    // ** addr: 0x80fa50, size: 0xf0
    // 0x80fa50: EnterFrame
    //     0x80fa50: stp             fp, lr, [SP, #-0x10]!
    //     0x80fa54: mov             fp, SP
    // 0x80fa58: AllocStack(0x30)
    //     0x80fa58: sub             SP, SP, #0x30
    // 0x80fa5c: CheckStackOverflow
    //     0x80fa5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80fa60: cmp             SP, x16
    //     0x80fa64: b.ls            #0x80fb38
    // 0x80fa68: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80fa68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80fa6c: ldr             x0, [x0, #0x2670]
    //     0x80fa70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80fa74: cmp             w0, w16
    //     0x80fa78: b.ne            #0x80fa84
    //     0x80fa7c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80fa80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80fa84: r0 = GetNavigation.arguments()
    //     0x80fa84: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80fa88: r16 = "id"
    //     0x80fa88: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80fa8c: ldr             x16, [x16, #0x740]
    // 0x80fa90: stp             x16, x0, [SP]
    // 0x80fa94: r4 = 0
    //     0x80fa94: movz            x4, #0
    // 0x80fa98: ldr             x0, [SP, #8]
    // 0x80fa9c: r16 = UnlinkedCall_0x5f3c08
    //     0x80fa9c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35cf8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80faa0: add             x16, x16, #0xcf8
    // 0x80faa4: ldp             x5, lr, [x16]
    // 0x80faa8: blr             lr
    // 0x80faac: mov             x3, x0
    // 0x80fab0: r2 = Null
    //     0x80fab0: mov             x2, NULL
    // 0x80fab4: r1 = Null
    //     0x80fab4: mov             x1, NULL
    // 0x80fab8: stur            x3, [fp, #-8]
    // 0x80fabc: branchIfSmi(r0, 0x80fae4)
    //     0x80fabc: tbz             w0, #0, #0x80fae4
    // 0x80fac0: r4 = LoadClassIdInstr(r0)
    //     0x80fac0: ldur            x4, [x0, #-1]
    //     0x80fac4: ubfx            x4, x4, #0xc, #0x14
    // 0x80fac8: sub             x4, x4, #0x3c
    // 0x80facc: cmp             x4, #1
    // 0x80fad0: b.ls            #0x80fae4
    // 0x80fad4: r8 = int
    //     0x80fad4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80fad8: r3 = Null
    //     0x80fad8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35d08] Null
    //     0x80fadc: ldr             x3, [x3, #0xd08]
    // 0x80fae0: r0 = int()
    //     0x80fae0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80fae4: r0 = find()
    //     0x80fae4: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0x80fae8: stur            x0, [fp, #-0x10]
    // 0x80faec: r0 = find()
    //     0x80faec: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0x80faf0: mov             x1, x0
    // 0x80faf4: ldur            x0, [fp, #-8]
    // 0x80faf8: stur            x1, [fp, #-0x20]
    // 0x80fafc: r2 = LoadInt32Instr(r0)
    //     0x80fafc: sbfx            x2, x0, #1, #0x1f
    //     0x80fb00: tbz             w0, #0, #0x80fb08
    //     0x80fb04: ldur            x2, [x0, #7]
    // 0x80fb08: stur            x2, [fp, #-0x18]
    // 0x80fb0c: r0 = DoaInfoController()
    //     0x80fb0c: bl              #0x80fcf8  ; AllocateDoaInfoControllerStub -> DoaInfoController (size=0x3c)
    // 0x80fb10: mov             x1, x0
    // 0x80fb14: ldur            x2, [fp, #-0x18]
    // 0x80fb18: ldur            x3, [fp, #-0x10]
    // 0x80fb1c: ldur            x5, [fp, #-0x20]
    // 0x80fb20: stur            x0, [fp, #-8]
    // 0x80fb24: r0 = DoaInfoController()
    //     0x80fb24: bl              #0x80fb40  ; [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] DoaInfoController::DoaInfoController
    // 0x80fb28: ldur            x0, [fp, #-8]
    // 0x80fb2c: LeaveFrame
    //     0x80fb2c: mov             SP, fp
    //     0x80fb30: ldp             fp, lr, [SP], #0x10
    // 0x80fb34: ret
    //     0x80fb34: ret             
    // 0x80fb38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80fb38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80fb3c: b               #0x80fa68
  }
}
