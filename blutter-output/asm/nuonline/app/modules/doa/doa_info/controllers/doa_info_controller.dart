// lib: , url: package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart

// class id: 1050180, size: 0x8
class :: {
}

// class id: 1954, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _DoaInfoController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {
}

// class id: 1955, size: 0x28, field offset: 0x28
//   transformed mixin,
abstract class _DoaInfoController&GetxController&StateMixin&OfflineMixin extends _DoaInfoController&GetxController&StateMixin
     with OfflineMixin<X0> {

  _ executeOfflineMode(/* No info */) async {
    // ** addr: 0x8f34e8, size: 0x80
    // 0x8f34e8: EnterFrame
    //     0x8f34e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f34ec: mov             fp, SP
    // 0x8f34f0: AllocStack(0x30)
    //     0x8f34f0: sub             SP, SP, #0x30
    // 0x8f34f4: SetupParameters(_DoaInfoController&GetxController&StateMixin&OfflineMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f34f4: stur            NULL, [fp, #-8]
    //     0x8f34f8: stur            x1, [fp, #-0x10]
    // 0x8f34fc: CheckStackOverflow
    //     0x8f34fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f3500: cmp             SP, x16
    //     0x8f3504: b.ls            #0x8f3560
    // 0x8f3508: r1 = 1
    //     0x8f3508: movz            x1, #0x1
    // 0x8f350c: r0 = AllocateContext()
    //     0x8f350c: bl              #0xec126c  ; AllocateContextStub
    // 0x8f3510: mov             x2, x0
    // 0x8f3514: ldur            x1, [fp, #-0x10]
    // 0x8f3518: stur            x2, [fp, #-0x18]
    // 0x8f351c: StoreField: r2->field_f = r1
    //     0x8f351c: stur            w1, [x2, #0xf]
    // 0x8f3520: InitAsync() -> Future<void?>
    //     0x8f3520: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f3524: bl              #0x661298  ; InitAsyncStub
    // 0x8f3528: ldur            x1, [fp, #-0x10]
    // 0x8f352c: r0 = onOfflineModeRequested()
    //     0x8f352c: bl              #0xbef910  ; [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] DoaInfoController::onOfflineModeRequested
    // 0x8f3530: ldur            x2, [fp, #-0x18]
    // 0x8f3534: r1 = Function '<anonymous closure>':.
    //     0x8f3534: add             x1, PP, #0x40, lsl #12  ; [pp+0x40580] AnonymousClosure: (0x8f3568), in [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] _DoaInfoController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f34e8)
    //     0x8f3538: ldr             x1, [x1, #0x580]
    // 0x8f353c: stur            x0, [fp, #-0x10]
    // 0x8f3540: r0 = AllocateClosure()
    //     0x8f3540: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f3544: r16 = <void?>
    //     0x8f3544: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f3548: ldur            lr, [fp, #-0x10]
    // 0x8f354c: stp             lr, x16, [SP, #8]
    // 0x8f3550: str             x0, [SP]
    // 0x8f3554: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f3554: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f3558: r0 = then()
    //     0x8f3558: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f355c: r0 = ReturnAsync()
    //     0x8f355c: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f3560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3564: b               #0x8f3508
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<DoaSubCategory>) {
    // ** addr: 0x8f3568, size: 0x94
    // 0x8f3568: EnterFrame
    //     0x8f3568: stp             fp, lr, [SP, #-0x10]!
    //     0x8f356c: mov             fp, SP
    // 0x8f3570: AllocStack(0x28)
    //     0x8f3570: sub             SP, SP, #0x28
    // 0x8f3574: SetupParameters()
    //     0x8f3574: ldr             x0, [fp, #0x18]
    //     0x8f3578: ldur            w3, [x0, #0x17]
    //     0x8f357c: add             x3, x3, HEAP, lsl #32
    //     0x8f3580: stur            x3, [fp, #-8]
    // 0x8f3584: CheckStackOverflow
    //     0x8f3584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f3588: cmp             SP, x16
    //     0x8f358c: b.ls            #0x8f35f4
    // 0x8f3590: mov             x2, x3
    // 0x8f3594: r1 = Function '<anonymous closure>':.
    //     0x8f3594: add             x1, PP, #0x40, lsl #12  ; [pp+0x40588] AnonymousClosure: (0x8f3654), in [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] _DoaInfoController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f34e8)
    //     0x8f3598: ldr             x1, [x1, #0x588]
    // 0x8f359c: r0 = AllocateClosure()
    //     0x8f359c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f35a0: ldur            x2, [fp, #-8]
    // 0x8f35a4: r1 = Function '<anonymous closure>':.
    //     0x8f35a4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40590] AnonymousClosure: (0x8f35fc), in [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] _DoaInfoController&GetxController&StateMixin&OfflineMixin::executeOfflineMode (0x8f34e8)
    //     0x8f35a8: ldr             x1, [x1, #0x590]
    // 0x8f35ac: stur            x0, [fp, #-8]
    // 0x8f35b0: r0 = AllocateClosure()
    //     0x8f35b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f35b4: mov             x1, x0
    // 0x8f35b8: ldr             x0, [fp, #0x10]
    // 0x8f35bc: r2 = LoadClassIdInstr(r0)
    //     0x8f35bc: ldur            x2, [x0, #-1]
    //     0x8f35c0: ubfx            x2, x2, #0xc, #0x14
    // 0x8f35c4: r16 = <Object?>
    //     0x8f35c4: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f35c8: stp             x0, x16, [SP, #0x10]
    // 0x8f35cc: ldur            x16, [fp, #-8]
    // 0x8f35d0: stp             x16, x1, [SP]
    // 0x8f35d4: mov             x0, x2
    // 0x8f35d8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f35d8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f35dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f35dc: sub             lr, x0, #1, lsl #12
    //     0x8f35e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8f35e4: blr             lr
    // 0x8f35e8: LeaveFrame
    //     0x8f35e8: mov             SP, fp
    //     0x8f35ec: ldp             fp, lr, [SP], #0x10
    // 0x8f35f0: ret
    //     0x8f35f0: ret             
    // 0x8f35f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f35f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f35f8: b               #0x8f3590
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f35fc, size: 0x58
    // 0x8f35fc: EnterFrame
    //     0x8f35fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f3600: mov             fp, SP
    // 0x8f3604: AllocStack(0x8)
    //     0x8f3604: sub             SP, SP, #8
    // 0x8f3608: SetupParameters()
    //     0x8f3608: ldr             x0, [fp, #0x18]
    //     0x8f360c: ldur            w1, [x0, #0x17]
    //     0x8f3610: add             x1, x1, HEAP, lsl #32
    // 0x8f3614: CheckStackOverflow
    //     0x8f3614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f3618: cmp             SP, x16
    //     0x8f361c: b.ls            #0x8f364c
    // 0x8f3620: LoadField: r0 = r1->field_f
    //     0x8f3620: ldur            w0, [x1, #0xf]
    // 0x8f3624: DecompressPointer r0
    //     0x8f3624: add             x0, x0, HEAP, lsl #32
    // 0x8f3628: ldr             x1, [fp, #0x10]
    // 0x8f362c: stur            x0, [fp, #-8]
    // 0x8f3630: r0 = getErrorMessage()
    //     0x8f3630: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f3634: ldur            x1, [fp, #-8]
    // 0x8f3638: mov             x2, x0
    // 0x8f363c: r0 = onOnlineModeFailure()
    //     0x8f363c: bl              #0xe34c68  ; [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] HajiHikmahController::onOnlineModeFailure
    // 0x8f3640: LeaveFrame
    //     0x8f3640: mov             SP, fp
    //     0x8f3644: ldp             fp, lr, [SP], #0x10
    // 0x8f3648: ret
    //     0x8f3648: ret             
    // 0x8f364c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f364c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3650: b               #0x8f3620
  }
  [closure] Future<void> <anonymous closure>(dynamic, DoaSubCategory, Pagination?) {
    // ** addr: 0x8f3654, size: 0x48
    // 0x8f3654: EnterFrame
    //     0x8f3654: stp             fp, lr, [SP, #-0x10]!
    //     0x8f3658: mov             fp, SP
    // 0x8f365c: ldr             x0, [fp, #0x20]
    // 0x8f3660: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f3660: ldur            w1, [x0, #0x17]
    // 0x8f3664: DecompressPointer r1
    //     0x8f3664: add             x1, x1, HEAP, lsl #32
    // 0x8f3668: CheckStackOverflow
    //     0x8f3668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f366c: cmp             SP, x16
    //     0x8f3670: b.ls            #0x8f3694
    // 0x8f3674: LoadField: r0 = r1->field_f
    //     0x8f3674: ldur            w0, [x1, #0xf]
    // 0x8f3678: DecompressPointer r0
    //     0x8f3678: add             x0, x0, HEAP, lsl #32
    // 0x8f367c: mov             x1, x0
    // 0x8f3680: ldr             x2, [fp, #0x18]
    // 0x8f3684: r0 = onOfflineModeLoaded()
    //     0x8f3684: bl              #0xbf66ac  ; [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] DoaInfoController::onOfflineModeLoaded
    // 0x8f3688: LeaveFrame
    //     0x8f3688: mov             SP, fp
    //     0x8f368c: ldp             fp, lr, [SP], #0x10
    // 0x8f3690: ret
    //     0x8f3690: ret             
    // 0x8f3694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3698: b               #0x8f3674
  }
}

// class id: 1956, size: 0x3c, field offset: 0x28
class DoaInfoController extends _DoaInfoController&GetxController&StateMixin&OfflineMixin {

  const int dyn:get:id(DoaInfoController) {
    // ** addr: 0x7d8aa4, size: 0x48
    // 0x7d8aa4: ldr             x2, [SP]
    // 0x7d8aa8: LoadField: r3 = r2->field_2f
    //     0x7d8aa8: ldur            x3, [x2, #0x2f]
    // 0x7d8aac: r0 = BoxInt64Instr(r3)
    //     0x7d8aac: sbfiz           x0, x3, #1, #0x1f
    //     0x7d8ab0: cmp             x3, x0, asr #1
    //     0x7d8ab4: b.eq            #0x7d8ad0
    //     0x7d8ab8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d8abc: mov             fp, SP
    //     0x7d8ac0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d8ac4: mov             SP, fp
    //     0x7d8ac8: ldp             fp, lr, [SP], #0x10
    //     0x7d8acc: stur            x3, [x0, #7]
    // 0x7d8ad0: ret
    //     0x7d8ad0: ret             
  }
  _ DoaInfoController(/* No info */) {
    // ** addr: 0x80fb40, size: 0xe0
    // 0x80fb40: EnterFrame
    //     0x80fb40: stp             fp, lr, [SP, #-0x10]!
    //     0x80fb44: mov             fp, SP
    // 0x80fb48: AllocStack(0x28)
    //     0x80fb48: sub             SP, SP, #0x28
    // 0x80fb4c: SetupParameters(DoaInfoController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x80fb4c: mov             x4, x1
    //     0x80fb50: stur            x2, [fp, #-0x10]
    //     0x80fb54: mov             x16, x3
    //     0x80fb58: mov             x3, x2
    //     0x80fb5c: mov             x2, x16
    //     0x80fb60: mov             x0, x5
    //     0x80fb64: stur            x1, [fp, #-8]
    //     0x80fb68: stur            x2, [fp, #-0x18]
    //     0x80fb6c: stur            x5, [fp, #-0x20]
    // 0x80fb70: CheckStackOverflow
    //     0x80fb70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80fb74: cmp             SP, x16
    //     0x80fb78: b.ls            #0x80fc18
    // 0x80fb7c: r1 = <List<DoaRelated>?>
    //     0x80fb7c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d18] TypeArguments: <List<DoaRelated>?>
    //     0x80fb80: ldr             x1, [x1, #0xd18]
    // 0x80fb84: r0 = Rx()
    //     0x80fb84: bl              #0x80ef18  ; AllocateRxStub -> Rx<X0> (size=0x20)
    // 0x80fb88: mov             x1, x0
    // 0x80fb8c: r2 = Null
    //     0x80fb8c: mov             x2, NULL
    // 0x80fb90: stur            x0, [fp, #-0x28]
    // 0x80fb94: r0 = _RxImpl()
    //     0x80fb94: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x80fb98: ldur            x0, [fp, #-0x28]
    // 0x80fb9c: ldur            x1, [fp, #-8]
    // 0x80fba0: StoreField: r1->field_37 = r0
    //     0x80fba0: stur            w0, [x1, #0x37]
    //     0x80fba4: ldurb           w16, [x1, #-1]
    //     0x80fba8: ldurb           w17, [x0, #-1]
    //     0x80fbac: and             x16, x17, x16, lsr #2
    //     0x80fbb0: tst             x16, HEAP, lsr #32
    //     0x80fbb4: b.eq            #0x80fbbc
    //     0x80fbb8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80fbbc: ldur            x0, [fp, #-0x18]
    // 0x80fbc0: StoreField: r1->field_27 = r0
    //     0x80fbc0: stur            w0, [x1, #0x27]
    //     0x80fbc4: ldurb           w16, [x1, #-1]
    //     0x80fbc8: ldurb           w17, [x0, #-1]
    //     0x80fbcc: and             x16, x17, x16, lsr #2
    //     0x80fbd0: tst             x16, HEAP, lsr #32
    //     0x80fbd4: b.eq            #0x80fbdc
    //     0x80fbd8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80fbdc: ldur            x0, [fp, #-0x20]
    // 0x80fbe0: StoreField: r1->field_2b = r0
    //     0x80fbe0: stur            w0, [x1, #0x2b]
    //     0x80fbe4: ldurb           w16, [x1, #-1]
    //     0x80fbe8: ldurb           w17, [x0, #-1]
    //     0x80fbec: and             x16, x17, x16, lsr #2
    //     0x80fbf0: tst             x16, HEAP, lsr #32
    //     0x80fbf4: b.eq            #0x80fbfc
    //     0x80fbf8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80fbfc: ldur            x0, [fp, #-0x10]
    // 0x80fc00: StoreField: r1->field_2f = r0
    //     0x80fc00: stur            x0, [x1, #0x2f]
    // 0x80fc04: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80fc04: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80fc08: r0 = Null
    //     0x80fc08: mov             x0, NULL
    // 0x80fc0c: LeaveFrame
    //     0x80fc0c: mov             SP, fp
    //     0x80fc10: ldp             fp, lr, [SP], #0x10
    // 0x80fc14: ret
    //     0x80fc14: ret             
    // 0x80fc18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80fc18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80fc1c: b               #0x80fb7c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f34a0, size: 0x48
    // 0x8f34a0: EnterFrame
    //     0x8f34a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f34a4: mov             fp, SP
    // 0x8f34a8: AllocStack(0x8)
    //     0x8f34a8: sub             SP, SP, #8
    // 0x8f34ac: SetupParameters(DoaInfoController this /* r1 => r0, fp-0x8 */)
    //     0x8f34ac: mov             x0, x1
    //     0x8f34b0: stur            x1, [fp, #-8]
    // 0x8f34b4: CheckStackOverflow
    //     0x8f34b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f34b8: cmp             SP, x16
    //     0x8f34bc: b.ls            #0x8f34e0
    // 0x8f34c0: mov             x1, x0
    // 0x8f34c4: r0 = onInit()
    //     0x8f34c4: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f34c8: ldur            x1, [fp, #-8]
    // 0x8f34cc: r0 = executeOfflineMode()
    //     0x8f34cc: bl              #0x8f34e8  ; [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] _DoaInfoController&GetxController&StateMixin&OfflineMixin::executeOfflineMode
    // 0x8f34d0: r0 = Null
    //     0x8f34d0: mov             x0, NULL
    // 0x8f34d4: LeaveFrame
    //     0x8f34d4: mov             SP, fp
    //     0x8f34d8: ldp             fp, lr, [SP], #0x10
    // 0x8f34dc: ret
    //     0x8f34dc: ret             
    // 0x8f34e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f34e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f34e4: b               #0x8f34c0
  }
  get _ related(/* No info */) {
    // ** addr: 0xad99cc, size: 0x50
    // 0xad99cc: EnterFrame
    //     0xad99cc: stp             fp, lr, [SP, #-0x10]!
    //     0xad99d0: mov             fp, SP
    // 0xad99d4: CheckStackOverflow
    //     0xad99d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad99d8: cmp             SP, x16
    //     0xad99dc: b.ls            #0xad9a14
    // 0xad99e0: LoadField: r0 = r1->field_37
    //     0xad99e0: ldur            w0, [x1, #0x37]
    // 0xad99e4: DecompressPointer r0
    //     0xad99e4: add             x0, x0, HEAP, lsl #32
    // 0xad99e8: mov             x1, x0
    // 0xad99ec: r0 = value()
    //     0xad99ec: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad99f0: cmp             w0, NULL
    // 0xad99f4: b.ne            #0xad9a08
    // 0xad99f8: r1 = <DoaRelated>
    //     0xad99f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] TypeArguments: <DoaRelated>
    //     0xad99fc: ldr             x1, [x1, #0x8e8]
    // 0xad9a00: r2 = 0
    //     0xad9a00: movz            x2, #0
    // 0xad9a04: r0 = _GrowableList()
    //     0xad9a04: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xad9a08: LeaveFrame
    //     0xad9a08: mov             SP, fp
    //     0xad9a0c: ldp             fp, lr, [SP], #0x10
    // 0xad9a10: ret
    //     0xad9a10: ret             
    // 0xad9a14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9a14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9a18: b               #0xad99e0
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef910, size: 0x5c
    // 0xbef910: EnterFrame
    //     0xbef910: stp             fp, lr, [SP, #-0x10]!
    //     0xbef914: mov             fp, SP
    // 0xbef918: AllocStack(0x10)
    //     0xbef918: sub             SP, SP, #0x10
    // 0xbef91c: SetupParameters(DoaInfoController this /* r1 => r1, fp-0x10 */)
    //     0xbef91c: stur            NULL, [fp, #-8]
    //     0xbef920: stur            x1, [fp, #-0x10]
    // 0xbef924: CheckStackOverflow
    //     0xbef924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef928: cmp             SP, x16
    //     0xbef92c: b.ls            #0xbef964
    // 0xbef930: InitAsync() -> Future<ApiResult<DoaSubCategory>>
    //     0xbef930: add             x0, PP, #0x40, lsl #12  ; [pp+0x40568] TypeArguments: <ApiResult<DoaSubCategory>>
    //     0xbef934: ldr             x0, [x0, #0x568]
    //     0xbef938: bl              #0x661298  ; InitAsyncStub
    // 0xbef93c: ldur            x0, [fp, #-0x10]
    // 0xbef940: LoadField: r1 = r0->field_27
    //     0xbef940: ldur            w1, [x0, #0x27]
    // 0xbef944: DecompressPointer r1
    //     0xbef944: add             x1, x1, HEAP, lsl #32
    // 0xbef948: LoadField: r2 = r0->field_2f
    //     0xbef948: ldur            x2, [x0, #0x2f]
    // 0xbef94c: r0 = LoadClassIdInstr(r1)
    //     0xbef94c: ldur            x0, [x1, #-1]
    //     0xbef950: ubfx            x0, x0, #0xc, #0x14
    // 0xbef954: r0 = GDT[cid_x0 + -0xfe0]()
    //     0xbef954: sub             lr, x0, #0xfe0
    //     0xbef958: ldr             lr, [x21, lr, lsl #3]
    //     0xbef95c: blr             lr
    // 0xbef960: r0 = ReturnAsync()
    //     0xbef960: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef968: b               #0xbef930
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf66ac, size: 0x14c
    // 0xbf66ac: EnterFrame
    //     0xbf66ac: stp             fp, lr, [SP, #-0x10]!
    //     0xbf66b0: mov             fp, SP
    // 0xbf66b4: AllocStack(0x38)
    //     0xbf66b4: sub             SP, SP, #0x38
    // 0xbf66b8: SetupParameters(DoaInfoController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbf66b8: stur            NULL, [fp, #-8]
    //     0xbf66bc: stur            x1, [fp, #-0x10]
    //     0xbf66c0: stur            x2, [fp, #-0x18]
    // 0xbf66c4: CheckStackOverflow
    //     0xbf66c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf66c8: cmp             SP, x16
    //     0xbf66cc: b.ls            #0xbf67f0
    // 0xbf66d0: r1 = 1
    //     0xbf66d0: movz            x1, #0x1
    // 0xbf66d4: r0 = AllocateContext()
    //     0xbf66d4: bl              #0xec126c  ; AllocateContextStub
    // 0xbf66d8: mov             x2, x0
    // 0xbf66dc: ldur            x1, [fp, #-0x10]
    // 0xbf66e0: stur            x2, [fp, #-0x20]
    // 0xbf66e4: StoreField: r2->field_f = r1
    //     0xbf66e4: stur            w1, [x2, #0xf]
    // 0xbf66e8: InitAsync() -> Future<void?>
    //     0xbf66e8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf66ec: bl              #0x661298  ; InitAsyncStub
    // 0xbf66f0: ldur            x2, [fp, #-0x18]
    // 0xbf66f4: LoadField: r0 = r2->field_1b
    //     0xbf66f4: ldur            w0, [x2, #0x1b]
    // 0xbf66f8: DecompressPointer r0
    //     0xbf66f8: add             x0, x0, HEAP, lsl #32
    // 0xbf66fc: cmp             w0, NULL
    // 0xbf6700: b.ne            #0xbf670c
    // 0xbf6704: r0 = false
    //     0xbf6704: add             x0, NULL, #0x30  ; false
    // 0xbf6708: b               #0xbf6738
    // 0xbf670c: LoadField: r1 = r0->field_7
    //     0xbf670c: ldur            w1, [x0, #7]
    // 0xbf6710: cbz             w1, #0xbf6734
    // 0xbf6714: r0 = RxStatus()
    //     0xbf6714: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf6718: mov             x1, x0
    // 0xbf671c: r0 = false
    //     0xbf671c: add             x0, NULL, #0x30  ; false
    // 0xbf6720: StoreField: r1->field_f = r0
    //     0xbf6720: stur            w0, [x1, #0xf]
    // 0xbf6724: StoreField: r1->field_7 = r0
    //     0xbf6724: stur            w0, [x1, #7]
    // 0xbf6728: StoreField: r1->field_b = r0
    //     0xbf6728: stur            w0, [x1, #0xb]
    // 0xbf672c: mov             x3, x1
    // 0xbf6730: b               #0xbf6758
    // 0xbf6734: r0 = false
    //     0xbf6734: add             x0, NULL, #0x30  ; false
    // 0xbf6738: r0 = RxStatus()
    //     0xbf6738: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf673c: mov             x1, x0
    // 0xbf6740: r0 = true
    //     0xbf6740: add             x0, NULL, #0x20  ; true
    // 0xbf6744: StoreField: r1->field_f = r0
    //     0xbf6744: stur            w0, [x1, #0xf]
    // 0xbf6748: r0 = false
    //     0xbf6748: add             x0, NULL, #0x30  ; false
    // 0xbf674c: StoreField: r1->field_7 = r0
    //     0xbf674c: stur            w0, [x1, #7]
    // 0xbf6750: StoreField: r1->field_b = r0
    //     0xbf6750: stur            w0, [x1, #0xb]
    // 0xbf6754: mov             x3, x1
    // 0xbf6758: ldur            x1, [fp, #-0x10]
    // 0xbf675c: ldur            x2, [fp, #-0x18]
    // 0xbf6760: r0 = change()
    //     0xbf6760: bl              #0x8ece58  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin::change
    // 0xbf6764: ldur            x1, [fp, #-0x18]
    // 0xbf6768: r0 = hasInfo()
    //     0xbf6768: bl              #0xbf67f8  ; [package:nuonline/app/data/models/doa.dart] DoaSubCategory::hasInfo
    // 0xbf676c: tbnz            w0, #4, #0xbf67e8
    // 0xbf6770: ldur            x0, [fp, #-0x10]
    // 0xbf6774: LoadField: r1 = r0->field_2b
    //     0xbf6774: ldur            w1, [x0, #0x2b]
    // 0xbf6778: DecompressPointer r1
    //     0xbf6778: add             x1, x1, HEAP, lsl #32
    // 0xbf677c: LoadField: r2 = r0->field_2f
    //     0xbf677c: ldur            x2, [x0, #0x2f]
    // 0xbf6780: r0 = LoadClassIdInstr(r1)
    //     0xbf6780: ldur            x0, [x1, #-1]
    //     0xbf6784: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6788: r0 = GDT[cid_x0 + -0xfd4]()
    //     0xbf6788: sub             lr, x0, #0xfd4
    //     0xbf678c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6790: blr             lr
    // 0xbf6794: mov             x1, x0
    // 0xbf6798: stur            x1, [fp, #-0x10]
    // 0xbf679c: r0 = Await()
    //     0xbf679c: bl              #0x661044  ; AwaitStub
    // 0xbf67a0: ldur            x2, [fp, #-0x20]
    // 0xbf67a4: r1 = Function '<anonymous closure>':.
    //     0xbf67a4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40570] AnonymousClosure: (0xbf68d0), in [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] DoaInfoController::onOfflineModeLoaded (0xbf66ac)
    //     0xbf67a8: ldr             x1, [x1, #0x570]
    // 0xbf67ac: stur            x0, [fp, #-0x10]
    // 0xbf67b0: r0 = AllocateClosure()
    //     0xbf67b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf67b4: mov             x1, x0
    // 0xbf67b8: ldur            x0, [fp, #-0x10]
    // 0xbf67bc: r2 = LoadClassIdInstr(r0)
    //     0xbf67bc: ldur            x2, [x0, #-1]
    //     0xbf67c0: ubfx            x2, x2, #0xc, #0x14
    // 0xbf67c4: r16 = <List<DoaRelated>>
    //     0xbf67c4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40578] TypeArguments: <List<DoaRelated>>
    //     0xbf67c8: ldr             x16, [x16, #0x578]
    // 0xbf67cc: stp             x0, x16, [SP, #8]
    // 0xbf67d0: str             x1, [SP]
    // 0xbf67d4: mov             x0, x2
    // 0xbf67d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf67d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf67dc: r0 = GDT[cid_x0 + -0xfff]()
    //     0xbf67dc: sub             lr, x0, #0xfff
    //     0xbf67e0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf67e4: blr             lr
    // 0xbf67e8: r0 = Null
    //     0xbf67e8: mov             x0, NULL
    // 0xbf67ec: r0 = ReturnAsyncNotFuture()
    //     0xbf67ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf67f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf67f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf67f4: b               #0xbf66d0
  }
  [closure] List<DoaRelated> <anonymous closure>(dynamic, Success<List<DoaRelated>>) {
    // ** addr: 0xbf68d0, size: 0x64
    // 0xbf68d0: EnterFrame
    //     0xbf68d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf68d4: mov             fp, SP
    // 0xbf68d8: AllocStack(0x8)
    //     0xbf68d8: sub             SP, SP, #8
    // 0xbf68dc: SetupParameters()
    //     0xbf68dc: ldr             x0, [fp, #0x18]
    //     0xbf68e0: ldur            w1, [x0, #0x17]
    //     0xbf68e4: add             x1, x1, HEAP, lsl #32
    // 0xbf68e8: CheckStackOverflow
    //     0xbf68e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf68ec: cmp             SP, x16
    //     0xbf68f0: b.ls            #0xbf692c
    // 0xbf68f4: LoadField: r0 = r1->field_f
    //     0xbf68f4: ldur            w0, [x1, #0xf]
    // 0xbf68f8: DecompressPointer r0
    //     0xbf68f8: add             x0, x0, HEAP, lsl #32
    // 0xbf68fc: LoadField: r1 = r0->field_37
    //     0xbf68fc: ldur            w1, [x0, #0x37]
    // 0xbf6900: DecompressPointer r1
    //     0xbf6900: add             x1, x1, HEAP, lsl #32
    // 0xbf6904: ldr             x0, [fp, #0x10]
    // 0xbf6908: LoadField: r3 = r0->field_b
    //     0xbf6908: ldur            w3, [x0, #0xb]
    // 0xbf690c: DecompressPointer r3
    //     0xbf690c: add             x3, x3, HEAP, lsl #32
    // 0xbf6910: mov             x2, x3
    // 0xbf6914: stur            x3, [fp, #-8]
    // 0xbf6918: r0 = value=()
    //     0xbf6918: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf691c: ldur            x0, [fp, #-8]
    // 0xbf6920: LeaveFrame
    //     0xbf6920: mov             SP, fp
    //     0xbf6924: ldp             fp, lr, [SP], #0x10
    // 0xbf6928: ret
    //     0xbf6928: ret             
    // 0xbf692c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf692c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6930: b               #0xbf68f4
  }
}
