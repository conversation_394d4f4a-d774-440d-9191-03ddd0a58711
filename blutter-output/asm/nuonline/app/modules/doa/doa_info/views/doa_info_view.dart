// lib: , url: package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart

// class id: 1050181, size: 0x8
class :: {
}

// class id: 5051, size: 0x10, field offset: 0xc
//   const constructor, 
class _DoaInfoRelatedView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8aae8, size: 0x48c
    // 0xb8aae8: EnterFrame
    //     0xb8aae8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8aaec: mov             fp, SP
    // 0xb8aaf0: AllocStack(0x58)
    //     0xb8aaf0: sub             SP, SP, #0x58
    // 0xb8aaf4: SetupParameters(_DoaInfoRelatedView this /* r1 => r1, fp-0x8 */)
    //     0xb8aaf4: stur            x1, [fp, #-8]
    // 0xb8aaf8: CheckStackOverflow
    //     0xb8aaf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8aafc: cmp             SP, x16
    //     0xb8ab00: b.ls            #0xb8af6c
    // 0xb8ab04: r1 = 1
    //     0xb8ab04: movz            x1, #0x1
    // 0xb8ab08: r0 = AllocateContext()
    //     0xb8ab08: bl              #0xec126c  ; AllocateContextStub
    // 0xb8ab0c: mov             x3, x0
    // 0xb8ab10: ldur            x0, [fp, #-8]
    // 0xb8ab14: stur            x3, [fp, #-0x10]
    // 0xb8ab18: StoreField: r3->field_f = r0
    //     0xb8ab18: stur            w0, [x3, #0xf]
    // 0xb8ab1c: r1 = <Widget>
    //     0xb8ab1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8ab20: r2 = 0
    //     0xb8ab20: movz            x2, #0
    // 0xb8ab24: r0 = _GrowableList()
    //     0xb8ab24: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8ab28: mov             x3, x0
    // 0xb8ab2c: ldur            x0, [fp, #-8]
    // 0xb8ab30: stur            x3, [fp, #-0x20]
    // 0xb8ab34: LoadField: r4 = r0->field_b
    //     0xb8ab34: ldur            w4, [x0, #0xb]
    // 0xb8ab38: DecompressPointer r4
    //     0xb8ab38: add             x4, x4, HEAP, lsl #32
    // 0xb8ab3c: stur            x4, [fp, #-0x18]
    // 0xb8ab40: r1 = Function '<anonymous closure>':.
    //     0xb8ab40: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ca8] AnonymousClosure: (0xb8b594), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::articles (0xb8b00c)
    //     0xb8ab44: ldr             x1, [x1, #0xca8]
    // 0xb8ab48: r2 = Null
    //     0xb8ab48: mov             x2, NULL
    // 0xb8ab4c: r0 = AllocateClosure()
    //     0xb8ab4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ab50: ldur            x3, [fp, #-0x18]
    // 0xb8ab54: r1 = LoadClassIdInstr(r3)
    //     0xb8ab54: ldur            x1, [x3, #-1]
    //     0xb8ab58: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ab5c: mov             x2, x0
    // 0xb8ab60: mov             x0, x1
    // 0xb8ab64: mov             x1, x3
    // 0xb8ab68: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8ab68: movz            x17, #0xea28
    //     0xb8ab6c: add             lr, x0, x17
    //     0xb8ab70: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ab74: blr             lr
    // 0xb8ab78: r1 = LoadClassIdInstr(r0)
    //     0xb8ab78: ldur            x1, [x0, #-1]
    //     0xb8ab7c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ab80: mov             x16, x0
    // 0xb8ab84: mov             x0, x1
    // 0xb8ab88: mov             x1, x16
    // 0xb8ab8c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8ab8c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8ab90: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8ab90: movz            x17, #0xd889
    //     0xb8ab94: add             lr, x0, x17
    //     0xb8ab98: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ab9c: blr             lr
    // 0xb8aba0: LoadField: r1 = r0->field_b
    //     0xb8aba0: ldur            w1, [x0, #0xb]
    // 0xb8aba4: cbz             w1, #0xb8ace0
    // 0xb8aba8: ldur            x0, [fp, #-0x18]
    // 0xb8abac: r1 = Function '<anonymous closure>':.
    //     0xb8abac: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ca8] AnonymousClosure: (0xb8b594), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::articles (0xb8b00c)
    //     0xb8abb0: ldr             x1, [x1, #0xca8]
    // 0xb8abb4: r2 = Null
    //     0xb8abb4: mov             x2, NULL
    // 0xb8abb8: r0 = AllocateClosure()
    //     0xb8abb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8abbc: ldur            x3, [fp, #-0x18]
    // 0xb8abc0: r1 = LoadClassIdInstr(r3)
    //     0xb8abc0: ldur            x1, [x3, #-1]
    //     0xb8abc4: ubfx            x1, x1, #0xc, #0x14
    // 0xb8abc8: mov             x2, x0
    // 0xb8abcc: mov             x0, x1
    // 0xb8abd0: mov             x1, x3
    // 0xb8abd4: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8abd4: movz            x17, #0xea28
    //     0xb8abd8: add             lr, x0, x17
    //     0xb8abdc: ldr             lr, [x21, lr, lsl #3]
    //     0xb8abe0: blr             lr
    // 0xb8abe4: r1 = LoadClassIdInstr(r0)
    //     0xb8abe4: ldur            x1, [x0, #-1]
    //     0xb8abe8: ubfx            x1, x1, #0xc, #0x14
    // 0xb8abec: mov             x16, x0
    // 0xb8abf0: mov             x0, x1
    // 0xb8abf4: mov             x1, x16
    // 0xb8abf8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8abf8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8abfc: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8abfc: movz            x17, #0xd889
    //     0xb8ac00: add             lr, x0, x17
    //     0xb8ac04: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ac08: blr             lr
    // 0xb8ac0c: LoadField: r1 = r0->field_b
    //     0xb8ac0c: ldur            w1, [x0, #0xb]
    // 0xb8ac10: r3 = LoadInt32Instr(r1)
    //     0xb8ac10: sbfx            x3, x1, #1, #0x1f
    // 0xb8ac14: stur            x3, [fp, #-0x28]
    // 0xb8ac18: r1 = Function '<anonymous closure>':.
    //     0xb8ac18: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cb0] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb8ac1c: ldr             x1, [x1, #0xcb0]
    // 0xb8ac20: r2 = Null
    //     0xb8ac20: mov             x2, NULL
    // 0xb8ac24: r0 = AllocateClosure()
    //     0xb8ac24: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ac28: ldur            x2, [fp, #-0x10]
    // 0xb8ac2c: r1 = Function '<anonymous closure>':.
    //     0xb8ac2c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cb8] AnonymousClosure: (0xb8b2fc), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::build (0xb8aae8)
    //     0xb8ac30: ldr             x1, [x1, #0xcb8]
    // 0xb8ac34: stur            x0, [fp, #-0x30]
    // 0xb8ac38: r0 = AllocateClosure()
    //     0xb8ac38: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ac3c: stur            x0, [fp, #-0x38]
    // 0xb8ac40: r0 = ListView()
    //     0xb8ac40: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8ac44: stur            x0, [fp, #-0x40]
    // 0xb8ac48: r16 = true
    //     0xb8ac48: add             x16, NULL, #0x20  ; true
    // 0xb8ac4c: r30 = Instance_EdgeInsets
    //     0xb8ac4c: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb8ac50: stp             lr, x16, [SP, #8]
    // 0xb8ac54: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb8ac54: add             x16, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xb8ac58: ldr             x16, [x16, #0x290]
    // 0xb8ac5c: str             x16, [SP]
    // 0xb8ac60: mov             x1, x0
    // 0xb8ac64: ldur            x2, [fp, #-0x38]
    // 0xb8ac68: ldur            x3, [fp, #-0x28]
    // 0xb8ac6c: ldur            x5, [fp, #-0x30]
    // 0xb8ac70: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x5, physics, 0x6, shrinkWrap, 0x4, null]
    //     0xb8ac70: add             x4, PP, #0x34, lsl #12  ; [pp+0x34070] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x5, "physics", 0x6, "shrinkWrap", 0x4, Null]
    //     0xb8ac74: ldr             x4, [x4, #0x70]
    // 0xb8ac78: r0 = ListView.separated()
    //     0xb8ac78: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8ac7c: r1 = Null
    //     0xb8ac7c: mov             x1, NULL
    // 0xb8ac80: r2 = 8
    //     0xb8ac80: movz            x2, #0x8
    // 0xb8ac84: r0 = AllocateArray()
    //     0xb8ac84: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8ac88: stur            x0, [fp, #-0x30]
    // 0xb8ac8c: r16 = Instance_NArticleHeader
    //     0xb8ac8c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2ca38] Obj!NArticleHeader@e20e91
    //     0xb8ac90: ldr             x16, [x16, #0xa38]
    // 0xb8ac94: StoreField: r0->field_f = r16
    //     0xb8ac94: stur            w16, [x0, #0xf]
    // 0xb8ac98: r16 = Instance_SizedBox
    //     0xb8ac98: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8ac9c: ldr             x16, [x16, #0xfe8]
    // 0xb8aca0: StoreField: r0->field_13 = r16
    //     0xb8aca0: stur            w16, [x0, #0x13]
    // 0xb8aca4: r16 = Instance_Divider
    //     0xb8aca4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb8aca8: ldr             x16, [x16, #0xc28]
    // 0xb8acac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8acac: stur            w16, [x0, #0x17]
    // 0xb8acb0: ldur            x1, [fp, #-0x40]
    // 0xb8acb4: StoreField: r0->field_1b = r1
    //     0xb8acb4: stur            w1, [x0, #0x1b]
    // 0xb8acb8: r1 = <Widget>
    //     0xb8acb8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8acbc: r0 = AllocateGrowableArray()
    //     0xb8acbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8acc0: mov             x1, x0
    // 0xb8acc4: ldur            x0, [fp, #-0x30]
    // 0xb8acc8: StoreField: r1->field_f = r0
    //     0xb8acc8: stur            w0, [x1, #0xf]
    // 0xb8accc: r0 = 8
    //     0xb8accc: movz            x0, #0x8
    // 0xb8acd0: StoreField: r1->field_b = r0
    //     0xb8acd0: stur            w0, [x1, #0xb]
    // 0xb8acd4: mov             x2, x1
    // 0xb8acd8: ldur            x1, [fp, #-0x20]
    // 0xb8acdc: r0 = addAll()
    //     0xb8acdc: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb8ace0: ldur            x1, [fp, #-8]
    // 0xb8ace4: r0 = articles()
    //     0xb8ace4: bl              #0xb8b00c  ; [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::articles
    // 0xb8ace8: LoadField: r1 = r0->field_b
    //     0xb8ace8: ldur            w1, [x0, #0xb]
    // 0xb8acec: cbz             w1, #0xb8adc4
    // 0xb8acf0: ldur            x0, [fp, #-0x18]
    // 0xb8acf4: r1 = Function '<anonymous closure>':.
    //     0xb8acf4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cc0] AnonymousClosure: (0xb8b2e0), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::videos (0xb8af74)
    //     0xb8acf8: ldr             x1, [x1, #0xcc0]
    // 0xb8acfc: r2 = Null
    //     0xb8acfc: mov             x2, NULL
    // 0xb8ad00: r0 = AllocateClosure()
    //     0xb8ad00: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ad04: ldur            x3, [fp, #-0x18]
    // 0xb8ad08: r1 = LoadClassIdInstr(r3)
    //     0xb8ad08: ldur            x1, [x3, #-1]
    //     0xb8ad0c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ad10: mov             x2, x0
    // 0xb8ad14: mov             x0, x1
    // 0xb8ad18: mov             x1, x3
    // 0xb8ad1c: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8ad1c: movz            x17, #0xea28
    //     0xb8ad20: add             lr, x0, x17
    //     0xb8ad24: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ad28: blr             lr
    // 0xb8ad2c: r1 = LoadClassIdInstr(r0)
    //     0xb8ad2c: ldur            x1, [x0, #-1]
    //     0xb8ad30: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ad34: mov             x16, x0
    // 0xb8ad38: mov             x0, x1
    // 0xb8ad3c: mov             x1, x16
    // 0xb8ad40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8ad40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8ad44: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8ad44: movz            x17, #0xd889
    //     0xb8ad48: add             lr, x0, x17
    //     0xb8ad4c: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ad50: blr             lr
    // 0xb8ad54: LoadField: r1 = r0->field_b
    //     0xb8ad54: ldur            w1, [x0, #0xb]
    // 0xb8ad58: cbz             w1, #0xb8adbc
    // 0xb8ad5c: ldur            x0, [fp, #-0x20]
    // 0xb8ad60: LoadField: r1 = r0->field_b
    //     0xb8ad60: ldur            w1, [x0, #0xb]
    // 0xb8ad64: LoadField: r2 = r0->field_f
    //     0xb8ad64: ldur            w2, [x0, #0xf]
    // 0xb8ad68: DecompressPointer r2
    //     0xb8ad68: add             x2, x2, HEAP, lsl #32
    // 0xb8ad6c: LoadField: r3 = r2->field_b
    //     0xb8ad6c: ldur            w3, [x2, #0xb]
    // 0xb8ad70: r2 = LoadInt32Instr(r1)
    //     0xb8ad70: sbfx            x2, x1, #1, #0x1f
    // 0xb8ad74: stur            x2, [fp, #-0x28]
    // 0xb8ad78: r1 = LoadInt32Instr(r3)
    //     0xb8ad78: sbfx            x1, x3, #1, #0x1f
    // 0xb8ad7c: cmp             x2, x1
    // 0xb8ad80: b.ne            #0xb8ad8c
    // 0xb8ad84: mov             x1, x0
    // 0xb8ad88: r0 = _growToNextCapacity()
    //     0xb8ad88: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8ad8c: ldur            x0, [fp, #-0x20]
    // 0xb8ad90: ldur            x1, [fp, #-0x28]
    // 0xb8ad94: add             x2, x1, #1
    // 0xb8ad98: lsl             x3, x2, #1
    // 0xb8ad9c: StoreField: r0->field_b = r3
    //     0xb8ad9c: stur            w3, [x0, #0xb]
    // 0xb8ada0: LoadField: r2 = r0->field_f
    //     0xb8ada0: ldur            w2, [x0, #0xf]
    // 0xb8ada4: DecompressPointer r2
    //     0xb8ada4: add             x2, x2, HEAP, lsl #32
    // 0xb8ada8: add             x3, x2, x1, lsl #2
    // 0xb8adac: r16 = Instance_SizedBox
    //     0xb8adac: add             x16, PP, #0x29, lsl #12  ; [pp+0x297f8] Obj!SizedBox@e1e1e1
    //     0xb8adb0: ldr             x16, [x16, #0x7f8]
    // 0xb8adb4: StoreField: r3->field_f = r16
    //     0xb8adb4: stur            w16, [x3, #0xf]
    // 0xb8adb8: b               #0xb8adc8
    // 0xb8adbc: ldur            x0, [fp, #-0x20]
    // 0xb8adc0: b               #0xb8adc8
    // 0xb8adc4: ldur            x0, [fp, #-0x20]
    // 0xb8adc8: ldur            x3, [fp, #-0x18]
    // 0xb8adcc: r1 = Function '<anonymous closure>':.
    //     0xb8adcc: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cc0] AnonymousClosure: (0xb8b2e0), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::videos (0xb8af74)
    //     0xb8add0: ldr             x1, [x1, #0xcc0]
    // 0xb8add4: r2 = Null
    //     0xb8add4: mov             x2, NULL
    // 0xb8add8: r0 = AllocateClosure()
    //     0xb8add8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8addc: ldur            x1, [fp, #-0x18]
    // 0xb8ade0: r2 = LoadClassIdInstr(r1)
    //     0xb8ade0: ldur            x2, [x1, #-1]
    //     0xb8ade4: ubfx            x2, x2, #0xc, #0x14
    // 0xb8ade8: mov             x16, x0
    // 0xb8adec: mov             x0, x2
    // 0xb8adf0: mov             x2, x16
    // 0xb8adf4: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8adf4: movz            x17, #0xea28
    //     0xb8adf8: add             lr, x0, x17
    //     0xb8adfc: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ae00: blr             lr
    // 0xb8ae04: r1 = LoadClassIdInstr(r0)
    //     0xb8ae04: ldur            x1, [x0, #-1]
    //     0xb8ae08: ubfx            x1, x1, #0xc, #0x14
    // 0xb8ae0c: mov             x16, x0
    // 0xb8ae10: mov             x0, x1
    // 0xb8ae14: mov             x1, x16
    // 0xb8ae18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8ae18: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8ae1c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8ae1c: movz            x17, #0xd889
    //     0xb8ae20: add             lr, x0, x17
    //     0xb8ae24: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ae28: blr             lr
    // 0xb8ae2c: LoadField: r1 = r0->field_b
    //     0xb8ae2c: ldur            w1, [x0, #0xb]
    // 0xb8ae30: cbz             w1, #0xb8af08
    // 0xb8ae34: ldur            x1, [fp, #-8]
    // 0xb8ae38: r0 = videos()
    //     0xb8ae38: bl              #0xb8af74  ; [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::videos
    // 0xb8ae3c: LoadField: r1 = r0->field_b
    //     0xb8ae3c: ldur            w1, [x0, #0xb]
    // 0xb8ae40: r3 = LoadInt32Instr(r1)
    //     0xb8ae40: sbfx            x3, x1, #1, #0x1f
    // 0xb8ae44: stur            x3, [fp, #-0x28]
    // 0xb8ae48: r1 = Function '<anonymous closure>':.
    //     0xb8ae48: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cc8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb8ae4c: ldr             x1, [x1, #0xcc8]
    // 0xb8ae50: r2 = Null
    //     0xb8ae50: mov             x2, NULL
    // 0xb8ae54: r0 = AllocateClosure()
    //     0xb8ae54: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ae58: ldur            x2, [fp, #-0x10]
    // 0xb8ae5c: r1 = Function '<anonymous closure>':.
    //     0xb8ae5c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cd0] AnonymousClosure: (0xb8b0a4), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::build (0xb8aae8)
    //     0xb8ae60: ldr             x1, [x1, #0xcd0]
    // 0xb8ae64: stur            x0, [fp, #-8]
    // 0xb8ae68: r0 = AllocateClosure()
    //     0xb8ae68: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ae6c: stur            x0, [fp, #-0x10]
    // 0xb8ae70: r0 = ListView()
    //     0xb8ae70: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8ae74: stur            x0, [fp, #-0x18]
    // 0xb8ae78: r16 = true
    //     0xb8ae78: add             x16, NULL, #0x20  ; true
    // 0xb8ae7c: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb8ae7c: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xb8ae80: ldr             lr, [lr, #0x290]
    // 0xb8ae84: stp             lr, x16, [SP]
    // 0xb8ae88: mov             x1, x0
    // 0xb8ae8c: ldur            x2, [fp, #-0x10]
    // 0xb8ae90: ldur            x3, [fp, #-0x28]
    // 0xb8ae94: ldur            x5, [fp, #-8]
    // 0xb8ae98: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xb8ae98: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb8ae9c: ldr             x4, [x4, #0x298]
    // 0xb8aea0: r0 = ListView.separated()
    //     0xb8aea0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8aea4: r1 = Null
    //     0xb8aea4: mov             x1, NULL
    // 0xb8aea8: r2 = 8
    //     0xb8aea8: movz            x2, #0x8
    // 0xb8aeac: r0 = AllocateArray()
    //     0xb8aeac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8aeb0: stur            x0, [fp, #-8]
    // 0xb8aeb4: r16 = Instance_NArticleHeader
    //     0xb8aeb4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35cd8] Obj!NArticleHeader@e20f51
    //     0xb8aeb8: ldr             x16, [x16, #0xcd8]
    // 0xb8aebc: StoreField: r0->field_f = r16
    //     0xb8aebc: stur            w16, [x0, #0xf]
    // 0xb8aec0: r16 = Instance_SizedBox
    //     0xb8aec0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8aec4: ldr             x16, [x16, #0xfe8]
    // 0xb8aec8: StoreField: r0->field_13 = r16
    //     0xb8aec8: stur            w16, [x0, #0x13]
    // 0xb8aecc: r16 = Instance_Divider
    //     0xb8aecc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb8aed0: ldr             x16, [x16, #0xc28]
    // 0xb8aed4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8aed4: stur            w16, [x0, #0x17]
    // 0xb8aed8: ldur            x1, [fp, #-0x18]
    // 0xb8aedc: StoreField: r0->field_1b = r1
    //     0xb8aedc: stur            w1, [x0, #0x1b]
    // 0xb8aee0: r1 = <Widget>
    //     0xb8aee0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8aee4: r0 = AllocateGrowableArray()
    //     0xb8aee4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8aee8: mov             x1, x0
    // 0xb8aeec: ldur            x0, [fp, #-8]
    // 0xb8aef0: StoreField: r1->field_f = r0
    //     0xb8aef0: stur            w0, [x1, #0xf]
    // 0xb8aef4: r0 = 8
    //     0xb8aef4: movz            x0, #0x8
    // 0xb8aef8: StoreField: r1->field_b = r0
    //     0xb8aef8: stur            w0, [x1, #0xb]
    // 0xb8aefc: mov             x2, x1
    // 0xb8af00: ldur            x1, [fp, #-0x20]
    // 0xb8af04: r0 = addAll()
    //     0xb8af04: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb8af08: ldur            x0, [fp, #-0x20]
    // 0xb8af0c: r0 = Column()
    //     0xb8af0c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8af10: r1 = Instance_Axis
    //     0xb8af10: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8af14: StoreField: r0->field_f = r1
    //     0xb8af14: stur            w1, [x0, #0xf]
    // 0xb8af18: r1 = Instance_MainAxisAlignment
    //     0xb8af18: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8af1c: ldr             x1, [x1, #0x730]
    // 0xb8af20: StoreField: r0->field_13 = r1
    //     0xb8af20: stur            w1, [x0, #0x13]
    // 0xb8af24: r1 = Instance_MainAxisSize
    //     0xb8af24: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8af28: ldr             x1, [x1, #0x738]
    // 0xb8af2c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8af2c: stur            w1, [x0, #0x17]
    // 0xb8af30: r1 = Instance_CrossAxisAlignment
    //     0xb8af30: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8af34: ldr             x1, [x1, #0x740]
    // 0xb8af38: StoreField: r0->field_1b = r1
    //     0xb8af38: stur            w1, [x0, #0x1b]
    // 0xb8af3c: r1 = Instance_VerticalDirection
    //     0xb8af3c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8af40: ldr             x1, [x1, #0x748]
    // 0xb8af44: StoreField: r0->field_23 = r1
    //     0xb8af44: stur            w1, [x0, #0x23]
    // 0xb8af48: r1 = Instance_Clip
    //     0xb8af48: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8af4c: ldr             x1, [x1, #0x750]
    // 0xb8af50: StoreField: r0->field_2b = r1
    //     0xb8af50: stur            w1, [x0, #0x2b]
    // 0xb8af54: StoreField: r0->field_2f = rZR
    //     0xb8af54: stur            xzr, [x0, #0x2f]
    // 0xb8af58: ldur            x1, [fp, #-0x20]
    // 0xb8af5c: StoreField: r0->field_b = r1
    //     0xb8af5c: stur            w1, [x0, #0xb]
    // 0xb8af60: LeaveFrame
    //     0xb8af60: mov             SP, fp
    //     0xb8af64: ldp             fp, lr, [SP], #0x10
    // 0xb8af68: ret
    //     0xb8af68: ret             
    // 0xb8af6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8af6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8af70: b               #0xb8ab04
  }
  get _ videos(/* No info */) {
    // ** addr: 0xb8af74, size: 0x98
    // 0xb8af74: EnterFrame
    //     0xb8af74: stp             fp, lr, [SP, #-0x10]!
    //     0xb8af78: mov             fp, SP
    // 0xb8af7c: AllocStack(0x8)
    //     0xb8af7c: sub             SP, SP, #8
    // 0xb8af80: CheckStackOverflow
    //     0xb8af80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8af84: cmp             SP, x16
    //     0xb8af88: b.ls            #0xb8b004
    // 0xb8af8c: LoadField: r0 = r1->field_b
    //     0xb8af8c: ldur            w0, [x1, #0xb]
    // 0xb8af90: DecompressPointer r0
    //     0xb8af90: add             x0, x0, HEAP, lsl #32
    // 0xb8af94: stur            x0, [fp, #-8]
    // 0xb8af98: r1 = Function '<anonymous closure>':.
    //     0xb8af98: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cc0] AnonymousClosure: (0xb8b2e0), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::videos (0xb8af74)
    //     0xb8af9c: ldr             x1, [x1, #0xcc0]
    // 0xb8afa0: r2 = Null
    //     0xb8afa0: mov             x2, NULL
    // 0xb8afa4: r0 = AllocateClosure()
    //     0xb8afa4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8afa8: ldur            x1, [fp, #-8]
    // 0xb8afac: r2 = LoadClassIdInstr(r1)
    //     0xb8afac: ldur            x2, [x1, #-1]
    //     0xb8afb0: ubfx            x2, x2, #0xc, #0x14
    // 0xb8afb4: mov             x16, x0
    // 0xb8afb8: mov             x0, x2
    // 0xb8afbc: mov             x2, x16
    // 0xb8afc0: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8afc0: movz            x17, #0xea28
    //     0xb8afc4: add             lr, x0, x17
    //     0xb8afc8: ldr             lr, [x21, lr, lsl #3]
    //     0xb8afcc: blr             lr
    // 0xb8afd0: r1 = LoadClassIdInstr(r0)
    //     0xb8afd0: ldur            x1, [x0, #-1]
    //     0xb8afd4: ubfx            x1, x1, #0xc, #0x14
    // 0xb8afd8: mov             x16, x0
    // 0xb8afdc: mov             x0, x1
    // 0xb8afe0: mov             x1, x16
    // 0xb8afe4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8afe4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8afe8: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8afe8: movz            x17, #0xd889
    //     0xb8afec: add             lr, x0, x17
    //     0xb8aff0: ldr             lr, [x21, lr, lsl #3]
    //     0xb8aff4: blr             lr
    // 0xb8aff8: LeaveFrame
    //     0xb8aff8: mov             SP, fp
    //     0xb8affc: ldp             fp, lr, [SP], #0x10
    // 0xb8b000: ret
    //     0xb8b000: ret             
    // 0xb8b004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b008: b               #0xb8af8c
  }
  get _ articles(/* No info */) {
    // ** addr: 0xb8b00c, size: 0x98
    // 0xb8b00c: EnterFrame
    //     0xb8b00c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b010: mov             fp, SP
    // 0xb8b014: AllocStack(0x8)
    //     0xb8b014: sub             SP, SP, #8
    // 0xb8b018: CheckStackOverflow
    //     0xb8b018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b01c: cmp             SP, x16
    //     0xb8b020: b.ls            #0xb8b09c
    // 0xb8b024: LoadField: r0 = r1->field_b
    //     0xb8b024: ldur            w0, [x1, #0xb]
    // 0xb8b028: DecompressPointer r0
    //     0xb8b028: add             x0, x0, HEAP, lsl #32
    // 0xb8b02c: stur            x0, [fp, #-8]
    // 0xb8b030: r1 = Function '<anonymous closure>':.
    //     0xb8b030: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ca8] AnonymousClosure: (0xb8b594), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::articles (0xb8b00c)
    //     0xb8b034: ldr             x1, [x1, #0xca8]
    // 0xb8b038: r2 = Null
    //     0xb8b038: mov             x2, NULL
    // 0xb8b03c: r0 = AllocateClosure()
    //     0xb8b03c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b040: ldur            x1, [fp, #-8]
    // 0xb8b044: r2 = LoadClassIdInstr(r1)
    //     0xb8b044: ldur            x2, [x1, #-1]
    //     0xb8b048: ubfx            x2, x2, #0xc, #0x14
    // 0xb8b04c: mov             x16, x0
    // 0xb8b050: mov             x0, x2
    // 0xb8b054: mov             x2, x16
    // 0xb8b058: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8b058: movz            x17, #0xea28
    //     0xb8b05c: add             lr, x0, x17
    //     0xb8b060: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b064: blr             lr
    // 0xb8b068: r1 = LoadClassIdInstr(r0)
    //     0xb8b068: ldur            x1, [x0, #-1]
    //     0xb8b06c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8b070: mov             x16, x0
    // 0xb8b074: mov             x0, x1
    // 0xb8b078: mov             x1, x16
    // 0xb8b07c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8b07c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8b080: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8b080: movz            x17, #0xd889
    //     0xb8b084: add             lr, x0, x17
    //     0xb8b088: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b08c: blr             lr
    // 0xb8b090: LeaveFrame
    //     0xb8b090: mov             SP, fp
    //     0xb8b094: ldp             fp, lr, [SP], #0x10
    // 0xb8b098: ret
    //     0xb8b098: ret             
    // 0xb8b09c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b09c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b0a0: b               #0xb8b024
  }
  [closure] NVideoListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb8b0a4, size: 0x188
    // 0xb8b0a4: EnterFrame
    //     0xb8b0a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b0a8: mov             fp, SP
    // 0xb8b0ac: AllocStack(0x20)
    //     0xb8b0ac: sub             SP, SP, #0x20
    // 0xb8b0b0: SetupParameters()
    //     0xb8b0b0: ldr             x0, [fp, #0x20]
    //     0xb8b0b4: ldur            w1, [x0, #0x17]
    //     0xb8b0b8: add             x1, x1, HEAP, lsl #32
    //     0xb8b0bc: stur            x1, [fp, #-8]
    // 0xb8b0c0: CheckStackOverflow
    //     0xb8b0c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b0c4: cmp             SP, x16
    //     0xb8b0c8: b.ls            #0xb8b220
    // 0xb8b0cc: r1 = 1
    //     0xb8b0cc: movz            x1, #0x1
    // 0xb8b0d0: r0 = AllocateContext()
    //     0xb8b0d0: bl              #0xec126c  ; AllocateContextStub
    // 0xb8b0d4: mov             x3, x0
    // 0xb8b0d8: ldur            x0, [fp, #-8]
    // 0xb8b0dc: stur            x3, [fp, #-0x10]
    // 0xb8b0e0: StoreField: r3->field_b = r0
    //     0xb8b0e0: stur            w0, [x3, #0xb]
    // 0xb8b0e4: LoadField: r1 = r0->field_f
    //     0xb8b0e4: ldur            w1, [x0, #0xf]
    // 0xb8b0e8: DecompressPointer r1
    //     0xb8b0e8: add             x1, x1, HEAP, lsl #32
    // 0xb8b0ec: LoadField: r0 = r1->field_b
    //     0xb8b0ec: ldur            w0, [x1, #0xb]
    // 0xb8b0f0: DecompressPointer r0
    //     0xb8b0f0: add             x0, x0, HEAP, lsl #32
    // 0xb8b0f4: stur            x0, [fp, #-8]
    // 0xb8b0f8: r1 = Function '<anonymous closure>':.
    //     0xb8b0f8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35cc0] AnonymousClosure: (0xb8b2e0), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::videos (0xb8af74)
    //     0xb8b0fc: ldr             x1, [x1, #0xcc0]
    // 0xb8b100: r2 = Null
    //     0xb8b100: mov             x2, NULL
    // 0xb8b104: r0 = AllocateClosure()
    //     0xb8b104: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b108: ldur            x1, [fp, #-8]
    // 0xb8b10c: r2 = LoadClassIdInstr(r1)
    //     0xb8b10c: ldur            x2, [x1, #-1]
    //     0xb8b110: ubfx            x2, x2, #0xc, #0x14
    // 0xb8b114: mov             x16, x0
    // 0xb8b118: mov             x0, x2
    // 0xb8b11c: mov             x2, x16
    // 0xb8b120: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8b120: movz            x17, #0xea28
    //     0xb8b124: add             lr, x0, x17
    //     0xb8b128: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b12c: blr             lr
    // 0xb8b130: r1 = LoadClassIdInstr(r0)
    //     0xb8b130: ldur            x1, [x0, #-1]
    //     0xb8b134: ubfx            x1, x1, #0xc, #0x14
    // 0xb8b138: mov             x16, x0
    // 0xb8b13c: mov             x0, x1
    // 0xb8b140: mov             x1, x16
    // 0xb8b144: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8b144: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8b148: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8b148: movz            x17, #0xd889
    //     0xb8b14c: add             lr, x0, x17
    //     0xb8b150: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b154: blr             lr
    // 0xb8b158: mov             x2, x0
    // 0xb8b15c: LoadField: r0 = r2->field_b
    //     0xb8b15c: ldur            w0, [x2, #0xb]
    // 0xb8b160: ldr             x1, [fp, #0x10]
    // 0xb8b164: r3 = LoadInt32Instr(r1)
    //     0xb8b164: sbfx            x3, x1, #1, #0x1f
    //     0xb8b168: tbz             w1, #0, #0xb8b170
    //     0xb8b16c: ldur            x3, [x1, #7]
    // 0xb8b170: r1 = LoadInt32Instr(r0)
    //     0xb8b170: sbfx            x1, x0, #1, #0x1f
    // 0xb8b174: mov             x0, x1
    // 0xb8b178: mov             x1, x3
    // 0xb8b17c: cmp             x1, x0
    // 0xb8b180: b.hs            #0xb8b228
    // 0xb8b184: LoadField: r0 = r2->field_f
    //     0xb8b184: ldur            w0, [x2, #0xf]
    // 0xb8b188: DecompressPointer r0
    //     0xb8b188: add             x0, x0, HEAP, lsl #32
    // 0xb8b18c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb8b18c: add             x16, x0, x3, lsl #2
    //     0xb8b190: ldur            w1, [x16, #0xf]
    // 0xb8b194: DecompressPointer r1
    //     0xb8b194: add             x1, x1, HEAP, lsl #32
    // 0xb8b198: mov             x0, x1
    // 0xb8b19c: ldur            x2, [fp, #-0x10]
    // 0xb8b1a0: StoreField: r2->field_f = r0
    //     0xb8b1a0: stur            w0, [x2, #0xf]
    //     0xb8b1a4: ldurb           w16, [x2, #-1]
    //     0xb8b1a8: ldurb           w17, [x0, #-1]
    //     0xb8b1ac: and             x16, x17, x16, lsr #2
    //     0xb8b1b0: tst             x16, HEAP, lsr #32
    //     0xb8b1b4: b.eq            #0xb8b1bc
    //     0xb8b1b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb8b1bc: LoadField: r0 = r1->field_13
    //     0xb8b1bc: ldur            w0, [x1, #0x13]
    // 0xb8b1c0: DecompressPointer r0
    //     0xb8b1c0: add             x0, x0, HEAP, lsl #32
    // 0xb8b1c4: stur            x0, [fp, #-0x18]
    // 0xb8b1c8: LoadField: r3 = r1->field_1b
    //     0xb8b1c8: ldur            w3, [x1, #0x1b]
    // 0xb8b1cc: DecompressPointer r3
    //     0xb8b1cc: add             x3, x3, HEAP, lsl #32
    // 0xb8b1d0: stur            x3, [fp, #-8]
    // 0xb8b1d4: r0 = NVideoListTile()
    //     0xb8b1d4: bl              #0xb5a5c4  ; AllocateNVideoListTileStub -> NVideoListTile (size=0x20)
    // 0xb8b1d8: mov             x3, x0
    // 0xb8b1dc: ldur            x0, [fp, #-8]
    // 0xb8b1e0: stur            x3, [fp, #-0x20]
    // 0xb8b1e4: StoreField: r3->field_b = r0
    //     0xb8b1e4: stur            w0, [x3, #0xb]
    // 0xb8b1e8: ldur            x0, [fp, #-0x18]
    // 0xb8b1ec: StoreField: r3->field_f = r0
    //     0xb8b1ec: stur            w0, [x3, #0xf]
    // 0xb8b1f0: r0 = false
    //     0xb8b1f0: add             x0, NULL, #0x30  ; false
    // 0xb8b1f4: StoreField: r3->field_13 = r0
    //     0xb8b1f4: stur            w0, [x3, #0x13]
    // 0xb8b1f8: ldur            x2, [fp, #-0x10]
    // 0xb8b1fc: r1 = Function '<anonymous closure>':.
    //     0xb8b1fc: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ce0] AnonymousClosure: (0xb8b22c), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::build (0xb8aae8)
    //     0xb8b200: ldr             x1, [x1, #0xce0]
    // 0xb8b204: r0 = AllocateClosure()
    //     0xb8b204: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b208: mov             x1, x0
    // 0xb8b20c: ldur            x0, [fp, #-0x20]
    // 0xb8b210: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8b210: stur            w1, [x0, #0x17]
    // 0xb8b214: LeaveFrame
    //     0xb8b214: mov             SP, fp
    //     0xb8b218: ldp             fp, lr, [SP], #0x10
    // 0xb8b21c: ret
    //     0xb8b21c: ret             
    // 0xb8b220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b224: b               #0xb8b0cc
    // 0xb8b228: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8b228: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Future<dynamic>? <anonymous closure>(dynamic) {
    // ** addr: 0xb8b22c, size: 0xb4
    // 0xb8b22c: EnterFrame
    //     0xb8b22c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b230: mov             fp, SP
    // 0xb8b234: AllocStack(0x20)
    //     0xb8b234: sub             SP, SP, #0x20
    // 0xb8b238: SetupParameters()
    //     0xb8b238: ldr             x0, [fp, #0x10]
    //     0xb8b23c: ldur            w1, [x0, #0x17]
    //     0xb8b240: add             x1, x1, HEAP, lsl #32
    //     0xb8b244: stur            x1, [fp, #-8]
    // 0xb8b248: CheckStackOverflow
    //     0xb8b248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b24c: cmp             SP, x16
    //     0xb8b250: b.ls            #0xb8b2d8
    // 0xb8b254: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8b254: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8b258: ldr             x0, [x0, #0x2670]
    //     0xb8b25c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8b260: cmp             w0, w16
    //     0xb8b264: b.ne            #0xb8b270
    //     0xb8b268: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8b26c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8b270: r1 = Null
    //     0xb8b270: mov             x1, NULL
    // 0xb8b274: r2 = 4
    //     0xb8b274: movz            x2, #0x4
    // 0xb8b278: r0 = AllocateArray()
    //     0xb8b278: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8b27c: r16 = "youtube_id"
    //     0xb8b27c: add             x16, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0xb8b280: ldr             x16, [x16, #0x3f0]
    // 0xb8b284: StoreField: r0->field_f = r16
    //     0xb8b284: stur            w16, [x0, #0xf]
    // 0xb8b288: ldur            x1, [fp, #-8]
    // 0xb8b28c: LoadField: r2 = r1->field_f
    //     0xb8b28c: ldur            w2, [x1, #0xf]
    // 0xb8b290: DecompressPointer r2
    //     0xb8b290: add             x2, x2, HEAP, lsl #32
    // 0xb8b294: LoadField: r1 = r2->field_f
    //     0xb8b294: ldur            w1, [x2, #0xf]
    // 0xb8b298: DecompressPointer r1
    //     0xb8b298: add             x1, x1, HEAP, lsl #32
    // 0xb8b29c: StoreField: r0->field_13 = r1
    //     0xb8b29c: stur            w1, [x0, #0x13]
    // 0xb8b2a0: r16 = <String, String>
    //     0xb8b2a0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb8b2a4: ldr             x16, [x16, #0x668]
    // 0xb8b2a8: stp             x0, x16, [SP]
    // 0xb8b2ac: r0 = Map._fromLiteral()
    //     0xb8b2ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8b2b0: r16 = "/video/video-detail"
    //     0xb8b2b0: add             x16, PP, #0x29, lsl #12  ; [pp+0x29090] "/video/video-detail"
    //     0xb8b2b4: ldr             x16, [x16, #0x90]
    // 0xb8b2b8: stp             x16, NULL, [SP, #8]
    // 0xb8b2bc: str             x0, [SP]
    // 0xb8b2c0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8b2c0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8b2c4: ldr             x4, [x4, #0x478]
    // 0xb8b2c8: r0 = GetNavigation.toNamed()
    //     0xb8b2c8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8b2cc: LeaveFrame
    //     0xb8b2cc: mov             SP, fp
    //     0xb8b2d0: ldp             fp, lr, [SP], #0x10
    // 0xb8b2d4: ret
    //     0xb8b2d4: ret             
    // 0xb8b2d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b2d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b2dc: b               #0xb8b254
  }
  [closure] bool <anonymous closure>(dynamic, DoaRelated) {
    // ** addr: 0xb8b2e0, size: 0x1c
    // 0xb8b2e0: ldr             x1, [SP]
    // 0xb8b2e4: LoadField: r2 = r1->field_1f
    //     0xb8b2e4: ldur            x2, [x1, #0x1f]
    // 0xb8b2e8: cmp             x2, #2
    // 0xb8b2ec: r16 = true
    //     0xb8b2ec: add             x16, NULL, #0x20  ; true
    // 0xb8b2f0: r17 = false
    //     0xb8b2f0: add             x17, NULL, #0x30  ; false
    // 0xb8b2f4: csel            x0, x16, x17, eq
    // 0xb8b2f8: ret
    //     0xb8b2f8: ret             
  }
  [closure] NArticleListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb8b2fc, size: 0x19c
    // 0xb8b2fc: EnterFrame
    //     0xb8b2fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b300: mov             fp, SP
    // 0xb8b304: AllocStack(0x28)
    //     0xb8b304: sub             SP, SP, #0x28
    // 0xb8b308: SetupParameters()
    //     0xb8b308: ldr             x0, [fp, #0x20]
    //     0xb8b30c: ldur            w1, [x0, #0x17]
    //     0xb8b310: add             x1, x1, HEAP, lsl #32
    //     0xb8b314: stur            x1, [fp, #-8]
    // 0xb8b318: CheckStackOverflow
    //     0xb8b318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b31c: cmp             SP, x16
    //     0xb8b320: b.ls            #0xb8b48c
    // 0xb8b324: r1 = 1
    //     0xb8b324: movz            x1, #0x1
    // 0xb8b328: r0 = AllocateContext()
    //     0xb8b328: bl              #0xec126c  ; AllocateContextStub
    // 0xb8b32c: mov             x3, x0
    // 0xb8b330: ldur            x0, [fp, #-8]
    // 0xb8b334: stur            x3, [fp, #-0x10]
    // 0xb8b338: StoreField: r3->field_b = r0
    //     0xb8b338: stur            w0, [x3, #0xb]
    // 0xb8b33c: LoadField: r1 = r0->field_f
    //     0xb8b33c: ldur            w1, [x0, #0xf]
    // 0xb8b340: DecompressPointer r1
    //     0xb8b340: add             x1, x1, HEAP, lsl #32
    // 0xb8b344: LoadField: r0 = r1->field_b
    //     0xb8b344: ldur            w0, [x1, #0xb]
    // 0xb8b348: DecompressPointer r0
    //     0xb8b348: add             x0, x0, HEAP, lsl #32
    // 0xb8b34c: stur            x0, [fp, #-8]
    // 0xb8b350: r1 = Function '<anonymous closure>':.
    //     0xb8b350: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ca8] AnonymousClosure: (0xb8b594), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::articles (0xb8b00c)
    //     0xb8b354: ldr             x1, [x1, #0xca8]
    // 0xb8b358: r2 = Null
    //     0xb8b358: mov             x2, NULL
    // 0xb8b35c: r0 = AllocateClosure()
    //     0xb8b35c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b360: ldur            x1, [fp, #-8]
    // 0xb8b364: r2 = LoadClassIdInstr(r1)
    //     0xb8b364: ldur            x2, [x1, #-1]
    //     0xb8b368: ubfx            x2, x2, #0xc, #0x14
    // 0xb8b36c: mov             x16, x0
    // 0xb8b370: mov             x0, x2
    // 0xb8b374: mov             x2, x16
    // 0xb8b378: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb8b378: movz            x17, #0xea28
    //     0xb8b37c: add             lr, x0, x17
    //     0xb8b380: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b384: blr             lr
    // 0xb8b388: r1 = LoadClassIdInstr(r0)
    //     0xb8b388: ldur            x1, [x0, #-1]
    //     0xb8b38c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8b390: mov             x16, x0
    // 0xb8b394: mov             x0, x1
    // 0xb8b398: mov             x1, x16
    // 0xb8b39c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8b39c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8b3a0: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb8b3a0: movz            x17, #0xd889
    //     0xb8b3a4: add             lr, x0, x17
    //     0xb8b3a8: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b3ac: blr             lr
    // 0xb8b3b0: mov             x2, x0
    // 0xb8b3b4: LoadField: r0 = r2->field_b
    //     0xb8b3b4: ldur            w0, [x2, #0xb]
    // 0xb8b3b8: ldr             x1, [fp, #0x10]
    // 0xb8b3bc: r3 = LoadInt32Instr(r1)
    //     0xb8b3bc: sbfx            x3, x1, #1, #0x1f
    //     0xb8b3c0: tbz             w1, #0, #0xb8b3c8
    //     0xb8b3c4: ldur            x3, [x1, #7]
    // 0xb8b3c8: r1 = LoadInt32Instr(r0)
    //     0xb8b3c8: sbfx            x1, x0, #1, #0x1f
    // 0xb8b3cc: mov             x0, x1
    // 0xb8b3d0: mov             x1, x3
    // 0xb8b3d4: cmp             x1, x0
    // 0xb8b3d8: b.hs            #0xb8b494
    // 0xb8b3dc: LoadField: r0 = r2->field_f
    //     0xb8b3dc: ldur            w0, [x2, #0xf]
    // 0xb8b3e0: DecompressPointer r0
    //     0xb8b3e0: add             x0, x0, HEAP, lsl #32
    // 0xb8b3e4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb8b3e4: add             x16, x0, x3, lsl #2
    //     0xb8b3e8: ldur            w1, [x16, #0xf]
    // 0xb8b3ec: DecompressPointer r1
    //     0xb8b3ec: add             x1, x1, HEAP, lsl #32
    // 0xb8b3f0: mov             x0, x1
    // 0xb8b3f4: ldur            x2, [fp, #-0x10]
    // 0xb8b3f8: StoreField: r2->field_f = r0
    //     0xb8b3f8: stur            w0, [x2, #0xf]
    //     0xb8b3fc: ldurb           w16, [x2, #-1]
    //     0xb8b400: ldurb           w17, [x0, #-1]
    //     0xb8b404: and             x16, x17, x16, lsr #2
    //     0xb8b408: tst             x16, HEAP, lsr #32
    //     0xb8b40c: b.eq            #0xb8b414
    //     0xb8b410: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb8b414: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8b414: ldur            w0, [x1, #0x17]
    // 0xb8b418: DecompressPointer r0
    //     0xb8b418: add             x0, x0, HEAP, lsl #32
    // 0xb8b41c: stur            x0, [fp, #-0x20]
    // 0xb8b420: LoadField: r3 = r1->field_13
    //     0xb8b420: ldur            w3, [x1, #0x13]
    // 0xb8b424: DecompressPointer r3
    //     0xb8b424: add             x3, x3, HEAP, lsl #32
    // 0xb8b428: stur            x3, [fp, #-0x18]
    // 0xb8b42c: LoadField: r4 = r1->field_1b
    //     0xb8b42c: ldur            w4, [x1, #0x1b]
    // 0xb8b430: DecompressPointer r4
    //     0xb8b430: add             x4, x4, HEAP, lsl #32
    // 0xb8b434: stur            x4, [fp, #-8]
    // 0xb8b438: r0 = NArticleListTile()
    //     0xb8b438: bl              #0xb07fe4  ; AllocateNArticleListTileStub -> NArticleListTile (size=0x28)
    // 0xb8b43c: mov             x3, x0
    // 0xb8b440: ldur            x0, [fp, #-0x18]
    // 0xb8b444: stur            x3, [fp, #-0x28]
    // 0xb8b448: StoreField: r3->field_b = r0
    //     0xb8b448: stur            w0, [x3, #0xb]
    // 0xb8b44c: ldur            x0, [fp, #-0x20]
    // 0xb8b450: StoreField: r3->field_f = r0
    //     0xb8b450: stur            w0, [x3, #0xf]
    // 0xb8b454: ldur            x0, [fp, #-8]
    // 0xb8b458: StoreField: r3->field_13 = r0
    //     0xb8b458: stur            w0, [x3, #0x13]
    // 0xb8b45c: ldur            x2, [fp, #-0x10]
    // 0xb8b460: r1 = Function '<anonymous closure>':.
    //     0xb8b460: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ce8] AnonymousClosure: (0xb8b498), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] _DoaInfoRelatedView::build (0xb8aae8)
    //     0xb8b464: ldr             x1, [x1, #0xce8]
    // 0xb8b468: r0 = AllocateClosure()
    //     0xb8b468: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b46c: mov             x1, x0
    // 0xb8b470: ldur            x0, [fp, #-0x28]
    // 0xb8b474: StoreField: r0->field_1f = r1
    //     0xb8b474: stur            w1, [x0, #0x1f]
    // 0xb8b478: r1 = false
    //     0xb8b478: add             x1, NULL, #0x30  ; false
    // 0xb8b47c: StoreField: r0->field_23 = r1
    //     0xb8b47c: stur            w1, [x0, #0x23]
    // 0xb8b480: LeaveFrame
    //     0xb8b480: mov             SP, fp
    //     0xb8b484: ldp             fp, lr, [SP], #0x10
    // 0xb8b488: ret
    //     0xb8b488: ret             
    // 0xb8b48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b48c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b490: b               #0xb8b324
    // 0xb8b494: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8b494: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8b498, size: 0xfc
    // 0xb8b498: EnterFrame
    //     0xb8b498: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b49c: mov             fp, SP
    // 0xb8b4a0: AllocStack(0x28)
    //     0xb8b4a0: sub             SP, SP, #0x28
    // 0xb8b4a4: SetupParameters()
    //     0xb8b4a4: ldr             x0, [fp, #0x10]
    //     0xb8b4a8: ldur            w1, [x0, #0x17]
    //     0xb8b4ac: add             x1, x1, HEAP, lsl #32
    //     0xb8b4b0: stur            x1, [fp, #-8]
    // 0xb8b4b4: CheckStackOverflow
    //     0xb8b4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b4b8: cmp             SP, x16
    //     0xb8b4bc: b.ls            #0xb8b58c
    // 0xb8b4c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8b4c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8b4c4: ldr             x0, [x0, #0x2670]
    //     0xb8b4c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8b4cc: cmp             w0, w16
    //     0xb8b4d0: b.ne            #0xb8b4dc
    //     0xb8b4d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8b4d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8b4dc: r1 = Null
    //     0xb8b4dc: mov             x1, NULL
    // 0xb8b4e0: r2 = 4
    //     0xb8b4e0: movz            x2, #0x4
    // 0xb8b4e4: r0 = AllocateArray()
    //     0xb8b4e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8b4e8: stur            x0, [fp, #-0x10]
    // 0xb8b4ec: r16 = "id"
    //     0xb8b4ec: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8b4f0: ldr             x16, [x16, #0x740]
    // 0xb8b4f4: StoreField: r0->field_f = r16
    //     0xb8b4f4: stur            w16, [x0, #0xf]
    // 0xb8b4f8: ldur            x1, [fp, #-8]
    // 0xb8b4fc: LoadField: r2 = r1->field_f
    //     0xb8b4fc: ldur            w2, [x1, #0xf]
    // 0xb8b500: DecompressPointer r2
    //     0xb8b500: add             x2, x2, HEAP, lsl #32
    // 0xb8b504: LoadField: r1 = r2->field_f
    //     0xb8b504: ldur            w1, [x2, #0xf]
    // 0xb8b508: DecompressPointer r1
    //     0xb8b508: add             x1, x1, HEAP, lsl #32
    // 0xb8b50c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8b50c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8b510: r0 = parse()
    //     0xb8b510: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xb8b514: mov             x2, x0
    // 0xb8b518: r0 = BoxInt64Instr(r2)
    //     0xb8b518: sbfiz           x0, x2, #1, #0x1f
    //     0xb8b51c: cmp             x2, x0, asr #1
    //     0xb8b520: b.eq            #0xb8b52c
    //     0xb8b524: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8b528: stur            x2, [x0, #7]
    // 0xb8b52c: ldur            x1, [fp, #-0x10]
    // 0xb8b530: ArrayStore: r1[1] = r0  ; List_4
    //     0xb8b530: add             x25, x1, #0x13
    //     0xb8b534: str             w0, [x25]
    //     0xb8b538: tbz             w0, #0, #0xb8b554
    //     0xb8b53c: ldurb           w16, [x1, #-1]
    //     0xb8b540: ldurb           w17, [x0, #-1]
    //     0xb8b544: and             x16, x17, x16, lsr #2
    //     0xb8b548: tst             x16, HEAP, lsr #32
    //     0xb8b54c: b.eq            #0xb8b554
    //     0xb8b550: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8b554: r16 = <String, int>
    //     0xb8b554: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb8b558: ldur            lr, [fp, #-0x10]
    // 0xb8b55c: stp             lr, x16, [SP]
    // 0xb8b560: r0 = Map._fromLiteral()
    //     0xb8b560: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8b564: r16 = "/article/article-detail"
    //     0xb8b564: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xb8b568: ldr             x16, [x16, #0x3d0]
    // 0xb8b56c: stp             x16, NULL, [SP, #8]
    // 0xb8b570: str             x0, [SP]
    // 0xb8b574: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8b574: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8b578: ldr             x4, [x4, #0x478]
    // 0xb8b57c: r0 = GetNavigation.toNamed()
    //     0xb8b57c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8b580: LeaveFrame
    //     0xb8b580: mov             SP, fp
    //     0xb8b584: ldp             fp, lr, [SP], #0x10
    // 0xb8b588: ret
    //     0xb8b588: ret             
    // 0xb8b58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b58c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b590: b               #0xb8b4c0
  }
  [closure] bool <anonymous closure>(dynamic, DoaRelated) {
    // ** addr: 0xb8b594, size: 0x1c
    // 0xb8b594: ldr             x1, [SP]
    // 0xb8b598: LoadField: r2 = r1->field_1f
    //     0xb8b598: ldur            x2, [x1, #0x1f]
    // 0xb8b59c: cmp             x2, #1
    // 0xb8b5a0: r16 = true
    //     0xb8b5a0: add             x16, NULL, #0x20  ; true
    // 0xb8b5a4: r17 = false
    //     0xb8b5a4: add             x17, NULL, #0x30  ; false
    // 0xb8b5a8: csel            x0, x16, x17, eq
    // 0xb8b5ac: ret
    //     0xb8b5ac: ret             
  }
}

// class id: 5301, size: 0x14, field offset: 0x14
class DoaInfoView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad96b4, size: 0x124
    // 0xad96b4: EnterFrame
    //     0xad96b4: stp             fp, lr, [SP, #-0x10]!
    //     0xad96b8: mov             fp, SP
    // 0xad96bc: AllocStack(0x48)
    //     0xad96bc: sub             SP, SP, #0x48
    // 0xad96c0: SetupParameters(DoaInfoView this /* r1 => r1, fp-0x8 */)
    //     0xad96c0: stur            x1, [fp, #-8]
    // 0xad96c4: CheckStackOverflow
    //     0xad96c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad96c8: cmp             SP, x16
    //     0xad96cc: b.ls            #0xad97d0
    // 0xad96d0: r1 = 1
    //     0xad96d0: movz            x1, #0x1
    // 0xad96d4: r0 = AllocateContext()
    //     0xad96d4: bl              #0xec126c  ; AllocateContextStub
    // 0xad96d8: ldur            x1, [fp, #-8]
    // 0xad96dc: stur            x0, [fp, #-0x10]
    // 0xad96e0: StoreField: r0->field_f = r1
    //     0xad96e0: stur            w1, [x0, #0xf]
    // 0xad96e4: r0 = AppBar()
    //     0xad96e4: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad96e8: stur            x0, [fp, #-0x18]
    // 0xad96ec: r16 = Instance_Text
    //     0xad96ec: add             x16, PP, #0x28, lsl #12  ; [pp+0x28358] Obj!Text@e219b1
    //     0xad96f0: ldr             x16, [x16, #0x358]
    // 0xad96f4: str             x16, [SP]
    // 0xad96f8: mov             x1, x0
    // 0xad96fc: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xad96fc: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xad9700: ldr             x4, [x4, #0x6e8]
    // 0xad9704: r0 = AppBar()
    //     0xad9704: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad9708: ldur            x1, [fp, #-8]
    // 0xad970c: r0 = controller()
    //     0xad970c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9710: stur            x0, [fp, #-8]
    // 0xad9714: r0 = NEmptyState()
    //     0xad9714: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xad9718: mov             x1, x0
    // 0xad971c: r2 = "Saat tersedia, informasi serta artikel terkait bacaan akan ditampilkan pada halaman ini."
    //     0xad971c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c9f0] "Saat tersedia, informasi serta artikel terkait bacaan akan ditampilkan pada halaman ini."
    //     0xad9720: ldr             x2, [x2, #0x9f0]
    // 0xad9724: r3 = "assets/images/illustration/no_search.svg"
    //     0xad9724: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xad9728: ldr             x3, [x3, #0x138]
    // 0xad972c: r5 = "Informasi Terkait Belum Tersedia"
    //     0xad972c: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2c9f8] "Informasi Terkait Belum Tersedia"
    //     0xad9730: ldr             x5, [x5, #0x9f8]
    // 0xad9734: stur            x0, [fp, #-0x20]
    // 0xad9738: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xad9738: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xad973c: r0 = NEmptyState.svg()
    //     0xad973c: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xad9740: ldur            x2, [fp, #-0x10]
    // 0xad9744: r1 = Function '<anonymous closure>':.
    //     0xad9744: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8d8] AnonymousClosure: (0xad97d8), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] DoaInfoView::build (0xad96b4)
    //     0xad9748: ldr             x1, [x1, #0x8d8]
    // 0xad974c: r0 = AllocateClosure()
    //     0xad974c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9750: r16 = <DoaSubCategory>
    //     0xad9750: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xad9754: ldur            lr, [fp, #-8]
    // 0xad9758: stp             lr, x16, [SP, #0x18]
    // 0xad975c: r16 = Instance_Padding
    //     0xad975c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2ca08] Obj!Padding@e1e7e1
    //     0xad9760: ldr             x16, [x16, #0xa08]
    // 0xad9764: stp             x16, x0, [SP, #8]
    // 0xad9768: ldur            x16, [fp, #-0x20]
    // 0xad976c: str             x16, [SP]
    // 0xad9770: r4 = const [0x1, 0x4, 0x4, 0x2, onEmpty, 0x3, onLoading, 0x2, null]
    //     0xad9770: add             x4, PP, #0x29, lsl #12  ; [pp+0x29688] List(9) [0x1, 0x4, 0x4, 0x2, "onEmpty", 0x3, "onLoading", 0x2, Null]
    //     0xad9774: ldr             x4, [x4, #0x688]
    // 0xad9778: r0 = StateExt.obx()
    //     0xad9778: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xad977c: stur            x0, [fp, #-8]
    // 0xad9780: r0 = Scaffold()
    //     0xad9780: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad9784: ldur            x1, [fp, #-0x18]
    // 0xad9788: StoreField: r0->field_13 = r1
    //     0xad9788: stur            w1, [x0, #0x13]
    // 0xad978c: ldur            x1, [fp, #-8]
    // 0xad9790: ArrayStore: r0[0] = r1  ; List_4
    //     0xad9790: stur            w1, [x0, #0x17]
    // 0xad9794: r1 = Instance_AlignmentDirectional
    //     0xad9794: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad9798: ldr             x1, [x1, #0x758]
    // 0xad979c: StoreField: r0->field_2b = r1
    //     0xad979c: stur            w1, [x0, #0x2b]
    // 0xad97a0: r1 = true
    //     0xad97a0: add             x1, NULL, #0x20  ; true
    // 0xad97a4: StoreField: r0->field_53 = r1
    //     0xad97a4: stur            w1, [x0, #0x53]
    // 0xad97a8: r2 = Instance_DragStartBehavior
    //     0xad97a8: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad97ac: StoreField: r0->field_57 = r2
    //     0xad97ac: stur            w2, [x0, #0x57]
    // 0xad97b0: r2 = false
    //     0xad97b0: add             x2, NULL, #0x30  ; false
    // 0xad97b4: StoreField: r0->field_b = r2
    //     0xad97b4: stur            w2, [x0, #0xb]
    // 0xad97b8: StoreField: r0->field_f = r2
    //     0xad97b8: stur            w2, [x0, #0xf]
    // 0xad97bc: StoreField: r0->field_5f = r1
    //     0xad97bc: stur            w1, [x0, #0x5f]
    // 0xad97c0: StoreField: r0->field_63 = r1
    //     0xad97c0: stur            w1, [x0, #0x63]
    // 0xad97c4: LeaveFrame
    //     0xad97c4: mov             SP, fp
    //     0xad97c8: ldp             fp, lr, [SP], #0x10
    // 0xad97cc: ret
    //     0xad97cc: ret             
    // 0xad97d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad97d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad97d4: b               #0xad96d0
  }
  [closure] ListView <anonymous closure>(dynamic, DoaSubCategory?) {
    // ** addr: 0xad97d8, size: 0x188
    // 0xad97d8: EnterFrame
    //     0xad97d8: stp             fp, lr, [SP, #-0x10]!
    //     0xad97dc: mov             fp, SP
    // 0xad97e0: AllocStack(0x30)
    //     0xad97e0: sub             SP, SP, #0x30
    // 0xad97e4: SetupParameters()
    //     0xad97e4: ldr             x0, [fp, #0x18]
    //     0xad97e8: ldur            w2, [x0, #0x17]
    //     0xad97ec: add             x2, x2, HEAP, lsl #32
    //     0xad97f0: stur            x2, [fp, #-0x10]
    // 0xad97f4: CheckStackOverflow
    //     0xad97f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad97f8: cmp             SP, x16
    //     0xad97fc: b.ls            #0xad9950
    // 0xad9800: ldr             x0, [fp, #0x10]
    // 0xad9804: cmp             w0, NULL
    // 0xad9808: b.eq            #0xad9958
    // 0xad980c: LoadField: r1 = r0->field_f
    //     0xad980c: ldur            w1, [x0, #0xf]
    // 0xad9810: DecompressPointer r1
    //     0xad9810: add             x1, x1, HEAP, lsl #32
    // 0xad9814: stur            x1, [fp, #-8]
    // 0xad9818: r0 = NArticleHeader()
    //     0xad9818: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xad981c: mov             x1, x0
    // 0xad9820: ldur            x0, [fp, #-8]
    // 0xad9824: stur            x1, [fp, #-0x18]
    // 0xad9828: StoreField: r1->field_b = r0
    //     0xad9828: stur            w0, [x1, #0xb]
    // 0xad982c: r0 = false
    //     0xad982c: add             x0, NULL, #0x30  ; false
    // 0xad9830: StoreField: r1->field_13 = r0
    //     0xad9830: stur            w0, [x1, #0x13]
    // 0xad9834: ldr             x0, [fp, #0x10]
    // 0xad9838: LoadField: r2 = r0->field_1b
    //     0xad9838: ldur            w2, [x0, #0x1b]
    // 0xad983c: DecompressPointer r2
    //     0xad983c: add             x2, x2, HEAP, lsl #32
    // 0xad9840: stur            x2, [fp, #-8]
    // 0xad9844: cmp             w2, NULL
    // 0xad9848: b.eq            #0xad995c
    // 0xad984c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad984c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad9850: ldr             x0, [x0, #0x2670]
    //     0xad9854: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad9858: cmp             w0, w16
    //     0xad985c: b.ne            #0xad9868
    //     0xad9860: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad9864: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad9868: r0 = GetNavigation.textTheme()
    //     0xad9868: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xad986c: LoadField: r1 = r0->field_23
    //     0xad986c: ldur            w1, [x0, #0x23]
    // 0xad9870: DecompressPointer r1
    //     0xad9870: add             x1, x1, HEAP, lsl #32
    // 0xad9874: stur            x1, [fp, #-0x20]
    // 0xad9878: r0 = Text()
    //     0xad9878: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad987c: mov             x1, x0
    // 0xad9880: ldur            x0, [fp, #-8]
    // 0xad9884: stur            x1, [fp, #-0x28]
    // 0xad9888: StoreField: r1->field_b = r0
    //     0xad9888: stur            w0, [x1, #0xb]
    // 0xad988c: ldur            x0, [fp, #-0x20]
    // 0xad9890: StoreField: r1->field_13 = r0
    //     0xad9890: stur            w0, [x1, #0x13]
    // 0xad9894: r0 = Obx()
    //     0xad9894: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad9898: ldur            x2, [fp, #-0x10]
    // 0xad989c: r1 = Function '<anonymous closure>':.
    //     0xad989c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8e0] AnonymousClosure: (0xad9960), in [package:nuonline/app/modules/doa/doa_info/views/doa_info_view.dart] DoaInfoView::build (0xad96b4)
    //     0xad98a0: ldr             x1, [x1, #0x8e0]
    // 0xad98a4: stur            x0, [fp, #-8]
    // 0xad98a8: r0 = AllocateClosure()
    //     0xad98a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad98ac: mov             x1, x0
    // 0xad98b0: ldur            x0, [fp, #-8]
    // 0xad98b4: StoreField: r0->field_b = r1
    //     0xad98b4: stur            w1, [x0, #0xb]
    // 0xad98b8: r1 = Null
    //     0xad98b8: mov             x1, NULL
    // 0xad98bc: r2 = 10
    //     0xad98bc: movz            x2, #0xa
    // 0xad98c0: r0 = AllocateArray()
    //     0xad98c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad98c4: mov             x2, x0
    // 0xad98c8: ldur            x0, [fp, #-0x18]
    // 0xad98cc: stur            x2, [fp, #-0x10]
    // 0xad98d0: StoreField: r2->field_f = r0
    //     0xad98d0: stur            w0, [x2, #0xf]
    // 0xad98d4: r16 = Instance_Divider
    //     0xad98d4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2ca30] Obj!Divider@e25761
    //     0xad98d8: ldr             x16, [x16, #0xa30]
    // 0xad98dc: StoreField: r2->field_13 = r16
    //     0xad98dc: stur            w16, [x2, #0x13]
    // 0xad98e0: ldur            x0, [fp, #-0x28]
    // 0xad98e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xad98e4: stur            w0, [x2, #0x17]
    // 0xad98e8: r16 = Instance_SizedBox
    //     0xad98e8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c920] Obj!SizedBox@e1e061
    //     0xad98ec: ldr             x16, [x16, #0x920]
    // 0xad98f0: StoreField: r2->field_1b = r16
    //     0xad98f0: stur            w16, [x2, #0x1b]
    // 0xad98f4: ldur            x0, [fp, #-8]
    // 0xad98f8: StoreField: r2->field_1f = r0
    //     0xad98f8: stur            w0, [x2, #0x1f]
    // 0xad98fc: r1 = <Widget>
    //     0xad98fc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad9900: r0 = AllocateGrowableArray()
    //     0xad9900: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad9904: mov             x1, x0
    // 0xad9908: ldur            x0, [fp, #-0x10]
    // 0xad990c: stur            x1, [fp, #-8]
    // 0xad9910: StoreField: r1->field_f = r0
    //     0xad9910: stur            w0, [x1, #0xf]
    // 0xad9914: r0 = 10
    //     0xad9914: movz            x0, #0xa
    // 0xad9918: StoreField: r1->field_b = r0
    //     0xad9918: stur            w0, [x1, #0xb]
    // 0xad991c: r0 = ListView()
    //     0xad991c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad9920: stur            x0, [fp, #-0x10]
    // 0xad9924: r16 = Instance_EdgeInsets
    //     0xad9924: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad9928: str             x16, [SP]
    // 0xad992c: mov             x1, x0
    // 0xad9930: ldur            x2, [fp, #-8]
    // 0xad9934: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xad9934: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xad9938: ldr             x4, [x4, #0xa0]
    // 0xad993c: r0 = ListView()
    //     0xad993c: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xad9940: ldur            x0, [fp, #-0x10]
    // 0xad9944: LeaveFrame
    //     0xad9944: mov             SP, fp
    //     0xad9948: ldp             fp, lr, [SP], #0x10
    // 0xad994c: ret
    //     0xad994c: ret             
    // 0xad9950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9954: b               #0xad9800
    // 0xad9958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9958: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad995c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad995c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] _DoaInfoRelatedView <anonymous closure>(dynamic) {
    // ** addr: 0xad9960, size: 0x60
    // 0xad9960: EnterFrame
    //     0xad9960: stp             fp, lr, [SP, #-0x10]!
    //     0xad9964: mov             fp, SP
    // 0xad9968: AllocStack(0x8)
    //     0xad9968: sub             SP, SP, #8
    // 0xad996c: SetupParameters()
    //     0xad996c: ldr             x0, [fp, #0x10]
    //     0xad9970: ldur            w1, [x0, #0x17]
    //     0xad9974: add             x1, x1, HEAP, lsl #32
    // 0xad9978: CheckStackOverflow
    //     0xad9978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad997c: cmp             SP, x16
    //     0xad9980: b.ls            #0xad99b8
    // 0xad9984: LoadField: r0 = r1->field_f
    //     0xad9984: ldur            w0, [x1, #0xf]
    // 0xad9988: DecompressPointer r0
    //     0xad9988: add             x0, x0, HEAP, lsl #32
    // 0xad998c: mov             x1, x0
    // 0xad9990: r0 = controller()
    //     0xad9990: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9994: mov             x1, x0
    // 0xad9998: r0 = related()
    //     0xad9998: bl              #0xad99cc  ; [package:nuonline/app/modules/doa/doa_info/controllers/doa_info_controller.dart] DoaInfoController::related
    // 0xad999c: stur            x0, [fp, #-8]
    // 0xad99a0: r0 = _DoaInfoRelatedView()
    //     0xad99a0: bl              #0xad99c0  ; Allocate_DoaInfoRelatedViewStub -> _DoaInfoRelatedView (size=0x10)
    // 0xad99a4: ldur            x1, [fp, #-8]
    // 0xad99a8: StoreField: r0->field_b = r1
    //     0xad99a8: stur            w1, [x0, #0xb]
    // 0xad99ac: LeaveFrame
    //     0xad99ac: mov             SP, fp
    //     0xad99b0: ldp             fp, lr, [SP], #0x10
    // 0xad99b4: ret
    //     0xad99b4: ret             
    // 0xad99b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad99b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad99bc: b               #0xad9984
  }
}
