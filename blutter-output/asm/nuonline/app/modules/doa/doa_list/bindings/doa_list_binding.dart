// lib: , url: package:nuonline/app/modules/doa/doa_list/bindings/doa_list_binding.dart

// class id: 1050182, size: 0x8
class :: {
}

// class id: 2183, size: 0x8, field offset: 0x8
class DoaListBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80fd04, size: 0x70
    // 0x80fd04: EnterFrame
    //     0x80fd04: stp             fp, lr, [SP, #-0x10]!
    //     0x80fd08: mov             fp, SP
    // 0x80fd0c: AllocStack(0x10)
    //     0x80fd0c: sub             SP, SP, #0x10
    // 0x80fd10: CheckStackOverflow
    //     0x80fd10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80fd14: cmp             SP, x16
    //     0x80fd18: b.ls            #0x80fd6c
    // 0x80fd1c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80fd1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80fd20: ldr             x0, [x0, #0x2670]
    //     0x80fd24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80fd28: cmp             w0, w16
    //     0x80fd2c: b.ne            #0x80fd38
    //     0x80fd30: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80fd34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80fd38: r1 = Function '<anonymous closure>':.
    //     0x80fd38: add             x1, PP, #0x35, lsl #12  ; [pp+0x35c18] AnonymousClosure: (0x80fd74), in [package:nuonline/app/modules/doa/doa_list/bindings/doa_list_binding.dart] DoaListBinding::dependencies (0x80fd04)
    //     0x80fd3c: ldr             x1, [x1, #0xc18]
    // 0x80fd40: r2 = Null
    //     0x80fd40: mov             x2, NULL
    // 0x80fd44: r0 = AllocateClosure()
    //     0x80fd44: bl              #0xec1630  ; AllocateClosureStub
    // 0x80fd48: r16 = <DoaListController>
    //     0x80fd48: add             x16, PP, #0x24, lsl #12  ; [pp+0x24c20] TypeArguments: <DoaListController>
    //     0x80fd4c: ldr             x16, [x16, #0xc20]
    // 0x80fd50: stp             x0, x16, [SP]
    // 0x80fd54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80fd54: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80fd58: r0 = Inst.lazyPut()
    //     0x80fd58: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80fd5c: r0 = Null
    //     0x80fd5c: mov             x0, NULL
    // 0x80fd60: LeaveFrame
    //     0x80fd60: mov             SP, fp
    //     0x80fd64: ldp             fp, lr, [SP], #0x10
    // 0x80fd68: ret
    //     0x80fd68: ret             
    // 0x80fd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80fd6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80fd70: b               #0x80fd1c
  }
  [closure] DoaListController <anonymous closure>(dynamic) {
    // ** addr: 0x80fd74, size: 0x268
    // 0x80fd74: EnterFrame
    //     0x80fd74: stp             fp, lr, [SP, #-0x10]!
    //     0x80fd78: mov             fp, SP
    // 0x80fd7c: AllocStack(0x48)
    //     0x80fd7c: sub             SP, SP, #0x48
    // 0x80fd80: CheckStackOverflow
    //     0x80fd80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80fd84: cmp             SP, x16
    //     0x80fd88: b.ls            #0x80ffd4
    // 0x80fd8c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80fd8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80fd90: ldr             x0, [x0, #0x2670]
    //     0x80fd94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80fd98: cmp             w0, w16
    //     0x80fd9c: b.ne            #0x80fda8
    //     0x80fda0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80fda4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80fda8: r0 = GetNavigation.arguments()
    //     0x80fda8: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80fdac: r16 = "id"
    //     0x80fdac: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80fdb0: ldr             x16, [x16, #0x740]
    // 0x80fdb4: stp             x16, x0, [SP]
    // 0x80fdb8: r4 = 0
    //     0x80fdb8: movz            x4, #0
    // 0x80fdbc: ldr             x0, [SP, #8]
    // 0x80fdc0: r16 = UnlinkedCall_0x5f3c08
    //     0x80fdc0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35c20] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80fdc4: add             x16, x16, #0xc20
    // 0x80fdc8: ldp             x5, lr, [x16]
    // 0x80fdcc: blr             lr
    // 0x80fdd0: mov             x3, x0
    // 0x80fdd4: r2 = Null
    //     0x80fdd4: mov             x2, NULL
    // 0x80fdd8: r1 = Null
    //     0x80fdd8: mov             x1, NULL
    // 0x80fddc: stur            x3, [fp, #-8]
    // 0x80fde0: branchIfSmi(r0, 0x80fe08)
    //     0x80fde0: tbz             w0, #0, #0x80fe08
    // 0x80fde4: r4 = LoadClassIdInstr(r0)
    //     0x80fde4: ldur            x4, [x0, #-1]
    //     0x80fde8: ubfx            x4, x4, #0xc, #0x14
    // 0x80fdec: sub             x4, x4, #0x3c
    // 0x80fdf0: cmp             x4, #1
    // 0x80fdf4: b.ls            #0x80fe08
    // 0x80fdf8: r8 = int
    //     0x80fdf8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80fdfc: r3 = Null
    //     0x80fdfc: add             x3, PP, #0x35, lsl #12  ; [pp+0x35c30] Null
    //     0x80fe00: ldr             x3, [x3, #0xc30]
    // 0x80fe04: r0 = int()
    //     0x80fe04: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80fe08: r0 = GetNavigation.arguments()
    //     0x80fe08: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80fe0c: r16 = "title"
    //     0x80fe0c: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x80fe10: ldr             x16, [x16, #0x748]
    // 0x80fe14: stp             x16, x0, [SP]
    // 0x80fe18: r4 = 0
    //     0x80fe18: movz            x4, #0
    // 0x80fe1c: ldr             x0, [SP, #8]
    // 0x80fe20: r16 = UnlinkedCall_0x5f3c08
    //     0x80fe20: add             x16, PP, #0x35, lsl #12  ; [pp+0x35c40] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80fe24: add             x16, x16, #0xc40
    // 0x80fe28: ldp             x5, lr, [x16]
    // 0x80fe2c: blr             lr
    // 0x80fe30: mov             x3, x0
    // 0x80fe34: r2 = Null
    //     0x80fe34: mov             x2, NULL
    // 0x80fe38: r1 = Null
    //     0x80fe38: mov             x1, NULL
    // 0x80fe3c: stur            x3, [fp, #-0x10]
    // 0x80fe40: r4 = 60
    //     0x80fe40: movz            x4, #0x3c
    // 0x80fe44: branchIfSmi(r0, 0x80fe50)
    //     0x80fe44: tbz             w0, #0, #0x80fe50
    // 0x80fe48: r4 = LoadClassIdInstr(r0)
    //     0x80fe48: ldur            x4, [x0, #-1]
    //     0x80fe4c: ubfx            x4, x4, #0xc, #0x14
    // 0x80fe50: sub             x4, x4, #0x5e
    // 0x80fe54: cmp             x4, #1
    // 0x80fe58: b.ls            #0x80fe6c
    // 0x80fe5c: r8 = String
    //     0x80fe5c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80fe60: r3 = Null
    //     0x80fe60: add             x3, PP, #0x35, lsl #12  ; [pp+0x35c50] Null
    //     0x80fe64: ldr             x3, [x3, #0xc50]
    // 0x80fe68: r0 = String()
    //     0x80fe68: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80fe6c: r0 = GetNavigation.arguments()
    //     0x80fe6c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80fe70: r16 = "showBookmarkOnDetail"
    //     0x80fe70: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bc0] "showBookmarkOnDetail"
    //     0x80fe74: ldr             x16, [x16, #0xbc0]
    // 0x80fe78: stp             x16, x0, [SP]
    // 0x80fe7c: r4 = 0
    //     0x80fe7c: movz            x4, #0
    // 0x80fe80: ldr             x0, [SP, #8]
    // 0x80fe84: r16 = UnlinkedCall_0x5f3c08
    //     0x80fe84: add             x16, PP, #0x35, lsl #12  ; [pp+0x35c60] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80fe88: add             x16, x16, #0xc60
    // 0x80fe8c: ldp             x5, lr, [x16]
    // 0x80fe90: blr             lr
    // 0x80fe94: mov             x3, x0
    // 0x80fe98: r2 = Null
    //     0x80fe98: mov             x2, NULL
    // 0x80fe9c: r1 = Null
    //     0x80fe9c: mov             x1, NULL
    // 0x80fea0: stur            x3, [fp, #-0x18]
    // 0x80fea4: r4 = 60
    //     0x80fea4: movz            x4, #0x3c
    // 0x80fea8: branchIfSmi(r0, 0x80feb4)
    //     0x80fea8: tbz             w0, #0, #0x80feb4
    // 0x80feac: r4 = LoadClassIdInstr(r0)
    //     0x80feac: ldur            x4, [x0, #-1]
    //     0x80feb0: ubfx            x4, x4, #0xc, #0x14
    // 0x80feb4: cmp             x4, #0x3f
    // 0x80feb8: b.eq            #0x80fecc
    // 0x80febc: r8 = bool?
    //     0x80febc: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x80fec0: r3 = Null
    //     0x80fec0: add             x3, PP, #0x35, lsl #12  ; [pp+0x35c70] Null
    //     0x80fec4: ldr             x3, [x3, #0xc70]
    // 0x80fec8: r0 = bool?()
    //     0x80fec8: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x80fecc: ldur            x0, [fp, #-0x18]
    // 0x80fed0: cmp             w0, NULL
    // 0x80fed4: b.ne            #0x80fedc
    // 0x80fed8: r0 = false
    //     0x80fed8: add             x0, NULL, #0x30  ; false
    // 0x80fedc: stur            x0, [fp, #-0x18]
    // 0x80fee0: r0 = GetNavigation.arguments()
    //     0x80fee0: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80fee4: r16 = "hideIndexes"
    //     0x80fee4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35c80] "hideIndexes"
    //     0x80fee8: ldr             x16, [x16, #0xc80]
    // 0x80feec: stp             x16, x0, [SP]
    // 0x80fef0: r4 = 0
    //     0x80fef0: movz            x4, #0
    // 0x80fef4: ldr             x0, [SP, #8]
    // 0x80fef8: r16 = UnlinkedCall_0x5f3c08
    //     0x80fef8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35c88] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80fefc: add             x16, x16, #0xc88
    // 0x80ff00: ldp             x5, lr, [x16]
    // 0x80ff04: blr             lr
    // 0x80ff08: mov             x3, x0
    // 0x80ff0c: r2 = Null
    //     0x80ff0c: mov             x2, NULL
    // 0x80ff10: r1 = Null
    //     0x80ff10: mov             x1, NULL
    // 0x80ff14: stur            x3, [fp, #-0x20]
    // 0x80ff18: r8 = List<int>?
    //     0x80ff18: ldr             x8, [PP, #0x5d0]  ; [pp+0x5d0] Type: List<int>?
    // 0x80ff1c: r3 = Null
    //     0x80ff1c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35c98] Null
    //     0x80ff20: ldr             x3, [x3, #0xc98]
    // 0x80ff24: r0 = List<int>?()
    //     0x80ff24: bl              #0x614dbc  ; IsType_List<int>?_Stub
    // 0x80ff28: ldur            x0, [fp, #-0x20]
    // 0x80ff2c: cmp             w0, NULL
    // 0x80ff30: b.ne            #0x80ff48
    // 0x80ff34: r1 = <int>
    //     0x80ff34: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x80ff38: r2 = 0
    //     0x80ff38: movz            x2, #0
    // 0x80ff3c: r0 = _GrowableList()
    //     0x80ff3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80ff40: mov             x3, x0
    // 0x80ff44: b               #0x80ff4c
    // 0x80ff48: mov             x3, x0
    // 0x80ff4c: ldur            x2, [fp, #-8]
    // 0x80ff50: ldur            x1, [fp, #-0x10]
    // 0x80ff54: ldur            x0, [fp, #-0x18]
    // 0x80ff58: stur            x3, [fp, #-0x20]
    // 0x80ff5c: r0 = find()
    //     0x80ff5c: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0x80ff60: stur            x0, [fp, #-0x28]
    // 0x80ff64: r0 = find()
    //     0x80ff64: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0x80ff68: r1 = <List<DoaSubCategory>>
    //     0x80ff68: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0x80ff6c: ldr             x1, [x1, #0x7d8]
    // 0x80ff70: stur            x0, [fp, #-0x30]
    // 0x80ff74: r0 = DoaListController()
    //     0x80ff74: bl              #0x81008c  ; AllocateDoaListControllerStub -> DoaListController (size=0x54)
    // 0x80ff78: mov             x2, x0
    // 0x80ff7c: ldur            x0, [fp, #-0x28]
    // 0x80ff80: stur            x2, [fp, #-0x38]
    // 0x80ff84: StoreField: r2->field_37 = r0
    //     0x80ff84: stur            w0, [x2, #0x37]
    // 0x80ff88: ldur            x0, [fp, #-0x30]
    // 0x80ff8c: StoreField: r2->field_3b = r0
    //     0x80ff8c: stur            w0, [x2, #0x3b]
    // 0x80ff90: ldur            x0, [fp, #-8]
    // 0x80ff94: r1 = LoadInt32Instr(r0)
    //     0x80ff94: sbfx            x1, x0, #1, #0x1f
    //     0x80ff98: tbz             w0, #0, #0x80ffa0
    //     0x80ff9c: ldur            x1, [x0, #7]
    // 0x80ffa0: StoreField: r2->field_3f = r1
    //     0x80ffa0: stur            x1, [x2, #0x3f]
    // 0x80ffa4: ldur            x0, [fp, #-0x10]
    // 0x80ffa8: StoreField: r2->field_47 = r0
    //     0x80ffa8: stur            w0, [x2, #0x47]
    // 0x80ffac: ldur            x0, [fp, #-0x18]
    // 0x80ffb0: StoreField: r2->field_4b = r0
    //     0x80ffb0: stur            w0, [x2, #0x4b]
    // 0x80ffb4: ldur            x0, [fp, #-0x20]
    // 0x80ffb8: StoreField: r2->field_4f = r0
    //     0x80ffb8: stur            w0, [x2, #0x4f]
    // 0x80ffbc: mov             x1, x2
    // 0x80ffc0: r0 = _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin()
    //     0x80ffc0: bl              #0x80ffdc  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::_DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin
    // 0x80ffc4: ldur            x0, [fp, #-0x38]
    // 0x80ffc8: LeaveFrame
    //     0x80ffc8: mov             SP, fp
    //     0x80ffcc: ldp             fp, lr, [SP], #0x10
    // 0x80ffd0: ret
    //     0x80ffd0: ret             
    // 0x80ffd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80ffd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80ffd8: b               #0x80fd8c
  }
}
