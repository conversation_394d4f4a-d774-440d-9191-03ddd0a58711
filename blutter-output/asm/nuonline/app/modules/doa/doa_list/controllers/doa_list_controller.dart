// lib: , url: package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart

// class id: 1050183, size: 0x8
class :: {
}

// class id: 1986, size: 0x2c, field offset: 0x24
//   transformed mixin,
abstract class _DoaListController&OfflineFirstController&StateMixin extends OfflineFirstController<dynamic>
     with StateMixin<X0> {
}

// class id: 1987, size: 0x2c, field offset: 0x2c
//   transformed mixin,
abstract class _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin extends _DoaListController&OfflineFirstController&StateMixin
     with AnalyticMixin {
}

// class id: 1988, size: 0x38, field offset: 0x2c
//   transformed mixin,
abstract class _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin extends _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin
     with SearchMixin {

  late TextEditingController searchController; // offset: 0x2c

  _ _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin(/* No info */) {
    // ** addr: 0x80ffdc, size: 0xb0
    // 0x80ffdc: EnterFrame
    //     0x80ffdc: stp             fp, lr, [SP, #-0x10]!
    //     0x80ffe0: mov             fp, SP
    // 0x80ffe4: AllocStack(0x18)
    //     0x80ffe4: sub             SP, SP, #0x18
    // 0x80ffe8: r0 = Sentinel
    //     0x80ffe8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80ffec: mov             x2, x1
    // 0x80fff0: stur            x1, [fp, #-8]
    // 0x80fff4: CheckStackOverflow
    //     0x80fff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80fff8: cmp             SP, x16
    //     0x80fffc: b.ls            #0x810084
    // 0x810000: StoreField: r2->field_2b = r0
    //     0x810000: stur            w0, [x2, #0x2b]
    // 0x810004: r1 = ""
    //     0x810004: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x810008: r0 = StringExtension.obs()
    //     0x810008: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x81000c: ldur            x3, [fp, #-8]
    // 0x810010: StoreField: r3->field_2f = r0
    //     0x810010: stur            w0, [x3, #0x2f]
    //     0x810014: ldurb           w16, [x3, #-1]
    //     0x810018: ldurb           w17, [x0, #-1]
    //     0x81001c: and             x16, x17, x16, lsr #2
    //     0x810020: tst             x16, HEAP, lsr #32
    //     0x810024: b.eq            #0x81002c
    //     0x810028: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x81002c: r1 = <String>
    //     0x81002c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x810030: r2 = 0
    //     0x810030: movz            x2, #0
    // 0x810034: r0 = _GrowableList()
    //     0x810034: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x810038: r16 = <String>
    //     0x810038: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x81003c: stp             x0, x16, [SP]
    // 0x810040: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x810040: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x810044: r0 = ListExtension.obs()
    //     0x810044: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x810048: r1 = true
    //     0x810048: add             x1, NULL, #0x20  ; true
    // 0x81004c: r0 = BoolExtension.obs()
    //     0x81004c: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x810050: ldur            x1, [fp, #-8]
    // 0x810054: StoreField: r1->field_33 = r0
    //     0x810054: stur            w0, [x1, #0x33]
    //     0x810058: ldurb           w16, [x1, #-1]
    //     0x81005c: ldurb           w17, [x0, #-1]
    //     0x810060: and             x16, x17, x16, lsr #2
    //     0x810064: tst             x16, HEAP, lsr #32
    //     0x810068: b.eq            #0x810070
    //     0x81006c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x810070: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x810070: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x810074: r0 = Null
    //     0x810074: mov             x0, NULL
    // 0x810078: LeaveFrame
    //     0x810078: mov             SP, fp
    //     0x81007c: ldp             fp, lr, [SP], #0x10
    // 0x810080: ret
    //     0x810080: ret             
    // 0x810084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810088: b               #0x810000
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8efdb4, size: 0x7c
    // 0x8efdb4: EnterFrame
    //     0x8efdb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8efdb8: mov             fp, SP
    // 0x8efdbc: AllocStack(0x10)
    //     0x8efdbc: sub             SP, SP, #0x10
    // 0x8efdc0: SetupParameters(_DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin this /* r1 => r0, fp-0x8 */)
    //     0x8efdc0: mov             x0, x1
    //     0x8efdc4: stur            x1, [fp, #-8]
    // 0x8efdc8: CheckStackOverflow
    //     0x8efdc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8efdcc: cmp             SP, x16
    //     0x8efdd0: b.ls            #0x8efe28
    // 0x8efdd4: mov             x1, x0
    // 0x8efdd8: r0 = onInit()
    //     0x8efdd8: bl              #0x8efe30  ; [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstController::onInit
    // 0x8efddc: r1 = <TextEditingValue>
    //     0x8efddc: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8efde0: r0 = TextEditingController()
    //     0x8efde0: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8efde4: mov             x1, x0
    // 0x8efde8: stur            x0, [fp, #-0x10]
    // 0x8efdec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8efdec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8efdf0: r0 = TextEditingController()
    //     0x8efdf0: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8efdf4: ldur            x0, [fp, #-0x10]
    // 0x8efdf8: ldur            x1, [fp, #-8]
    // 0x8efdfc: StoreField: r1->field_2b = r0
    //     0x8efdfc: stur            w0, [x1, #0x2b]
    //     0x8efe00: ldurb           w16, [x1, #-1]
    //     0x8efe04: ldurb           w17, [x0, #-1]
    //     0x8efe08: and             x16, x17, x16, lsr #2
    //     0x8efe0c: tst             x16, HEAP, lsr #32
    //     0x8efe10: b.eq            #0x8efe18
    //     0x8efe14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8efe18: r0 = Null
    //     0x8efe18: mov             x0, NULL
    // 0x8efe1c: LeaveFrame
    //     0x8efe1c: mov             SP, fp
    //     0x8efe20: ldp             fp, lr, [SP], #0x10
    // 0x8efe24: ret
    //     0x8efe24: ret             
    // 0x8efe28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8efe28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8efe2c: b               #0x8efdd4
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927174, size: 0x54
    // 0x927174: EnterFrame
    //     0x927174: stp             fp, lr, [SP, #-0x10]!
    //     0x927178: mov             fp, SP
    // 0x92717c: CheckStackOverflow
    //     0x92717c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927180: cmp             SP, x16
    //     0x927184: b.ls            #0x9271b4
    // 0x927188: LoadField: r0 = r1->field_2b
    //     0x927188: ldur            w0, [x1, #0x2b]
    // 0x92718c: DecompressPointer r0
    //     0x92718c: add             x0, x0, HEAP, lsl #32
    // 0x927190: r16 = Sentinel
    //     0x927190: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x927194: cmp             w0, w16
    // 0x927198: b.eq            #0x9271bc
    // 0x92719c: mov             x1, x0
    // 0x9271a0: r0 = dispose()
    //     0x9271a0: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9271a4: r0 = Null
    //     0x9271a4: mov             x0, NULL
    // 0x9271a8: LeaveFrame
    //     0x9271a8: mov             SP, fp
    //     0x9271ac: ldp             fp, lr, [SP], #0x10
    // 0x9271b0: ret
    //     0x9271b0: ret             
    // 0x9271b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9271b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9271b8: b               #0x927188
    // 0x9271bc: r9 = searchController
    //     0x9271bc: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f8d0] Field <_DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&<EMAIL>>: late (offset: 0x2c)
    //     0x9271c0: ldr             x9, [x9, #0x8d0]
    // 0x9271c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9271c4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ query(/* No info */) {
    // ** addr: 0xada394, size: 0x38
    // 0xada394: EnterFrame
    //     0xada394: stp             fp, lr, [SP, #-0x10]!
    //     0xada398: mov             fp, SP
    // 0xada39c: CheckStackOverflow
    //     0xada39c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada3a0: cmp             SP, x16
    //     0xada3a4: b.ls            #0xada3c4
    // 0xada3a8: LoadField: r0 = r1->field_2f
    //     0xada3a8: ldur            w0, [x1, #0x2f]
    // 0xada3ac: DecompressPointer r0
    //     0xada3ac: add             x0, x0, HEAP, lsl #32
    // 0xada3b0: mov             x1, x0
    // 0xada3b4: r0 = value()
    //     0xada3b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xada3b8: LeaveFrame
    //     0xada3b8: mov             SP, fp
    //     0xada3bc: ldp             fp, lr, [SP], #0x10
    // 0xada3c0: ret
    //     0xada3c0: ret             
    // 0xada3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada3c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada3c8: b               #0xada3a8
  }
  _ onSearchCanceled(/* No info */) {
    // ** addr: 0xada794, size: 0x40
    // 0xada794: EnterFrame
    //     0xada794: stp             fp, lr, [SP, #-0x10]!
    //     0xada798: mov             fp, SP
    // 0xada79c: CheckStackOverflow
    //     0xada79c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada7a0: cmp             SP, x16
    //     0xada7a4: b.ls            #0xada7cc
    // 0xada7a8: LoadField: r0 = r1->field_2f
    //     0xada7a8: ldur            w0, [x1, #0x2f]
    // 0xada7ac: DecompressPointer r0
    //     0xada7ac: add             x0, x0, HEAP, lsl #32
    // 0xada7b0: mov             x1, x0
    // 0xada7b4: r2 = ""
    //     0xada7b4: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xada7b8: r0 = value=()
    //     0xada7b8: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xada7bc: r0 = ""
    //     0xada7bc: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xada7c0: LeaveFrame
    //     0xada7c0: mov             SP, fp
    //     0xada7c4: ldp             fp, lr, [SP], #0x10
    // 0xada7c8: ret
    //     0xada7c8: ret             
    // 0xada7cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada7cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada7d0: b               #0xada7a8
  }
  [closure] void onQueryChanged(dynamic, String) {
    // ** addr: 0xada7d4, size: 0x3c
    // 0xada7d4: EnterFrame
    //     0xada7d4: stp             fp, lr, [SP, #-0x10]!
    //     0xada7d8: mov             fp, SP
    // 0xada7dc: ldr             x0, [fp, #0x18]
    // 0xada7e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada7e0: ldur            w1, [x0, #0x17]
    // 0xada7e4: DecompressPointer r1
    //     0xada7e4: add             x1, x1, HEAP, lsl #32
    // 0xada7e8: CheckStackOverflow
    //     0xada7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada7ec: cmp             SP, x16
    //     0xada7f0: b.ls            #0xada808
    // 0xada7f4: ldr             x2, [fp, #0x10]
    // 0xada7f8: r0 = onQueryChanged()
    //     0xada7f8: bl              #0xada810  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::onQueryChanged
    // 0xada7fc: LeaveFrame
    //     0xada7fc: mov             SP, fp
    //     0xada800: ldp             fp, lr, [SP], #0x10
    // 0xada804: ret
    //     0xada804: ret             
    // 0xada808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada80c: b               #0xada7f4
  }
  _ onQueryChanged(/* No info */) {
    // ** addr: 0xada810, size: 0x6c
    // 0xada810: EnterFrame
    //     0xada810: stp             fp, lr, [SP, #-0x10]!
    //     0xada814: mov             fp, SP
    // 0xada818: AllocStack(0x18)
    //     0xada818: sub             SP, SP, #0x18
    // 0xada81c: SetupParameters(_DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xada81c: mov             x3, x1
    //     0xada820: mov             x0, x2
    //     0xada824: stur            x1, [fp, #-8]
    //     0xada828: stur            x2, [fp, #-0x10]
    // 0xada82c: CheckStackOverflow
    //     0xada82c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada830: cmp             SP, x16
    //     0xada834: b.ls            #0xada874
    // 0xada838: LoadField: r1 = r3->field_2f
    //     0xada838: ldur            w1, [x3, #0x2f]
    // 0xada83c: DecompressPointer r1
    //     0xada83c: add             x1, x1, HEAP, lsl #32
    // 0xada840: mov             x2, x0
    // 0xada844: r0 = value=()
    //     0xada844: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xada848: r16 = false
    //     0xada848: add             x16, NULL, #0x30  ; false
    // 0xada84c: str             x16, [SP]
    // 0xada850: ldur            x1, [fp, #-8]
    // 0xada854: ldur            x2, [fp, #-0x10]
    // 0xada858: r4 = const [0, 0x3, 0x1, 0x2, saveHistory, 0x2, null]
    //     0xada858: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bd10] List(7) [0, 0x3, 0x1, 0x2, "saveHistory", 0x2, Null]
    //     0xada85c: ldr             x4, [x4, #0xd10]
    // 0xada860: r0 = onSearchSubmitted()
    //     0xada860: bl              #0xe3dd48  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onSearchSubmitted
    // 0xada864: r0 = Null
    //     0xada864: mov             x0, NULL
    // 0xada868: LeaveFrame
    //     0xada868: mov             SP, fp
    //     0xada86c: ldp             fp, lr, [SP], #0x10
    // 0xada870: ret
    //     0xada870: ret             
    // 0xada874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada878: b               #0xada838
  }
  _ onSearchSubmitted(/* No info */) {
    // ** addr: 0xe3dda8, size: 0x50
    // 0xe3dda8: EnterFrame
    //     0xe3dda8: stp             fp, lr, [SP, #-0x10]!
    //     0xe3ddac: mov             fp, SP
    // 0xe3ddb0: AllocStack(0x8)
    //     0xe3ddb0: sub             SP, SP, #8
    // 0xe3ddb4: CheckStackOverflow
    //     0xe3ddb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3ddb8: cmp             SP, x16
    //     0xe3ddbc: b.ls            #0xe3ddf0
    // 0xe3ddc0: LoadField: r0 = r1->field_33
    //     0xe3ddc0: ldur            w0, [x1, #0x33]
    // 0xe3ddc4: DecompressPointer r0
    //     0xe3ddc4: add             x0, x0, HEAP, lsl #32
    // 0xe3ddc8: mov             x1, x0
    // 0xe3ddcc: stur            x0, [fp, #-8]
    // 0xe3ddd0: r0 = value()
    //     0xe3ddd0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3ddd4: tbnz            w0, #4, #0xe3dde0
    // 0xe3ddd8: ldur            x1, [fp, #-8]
    // 0xe3dddc: r0 = RxBoolExt.toggle()
    //     0xe3dddc: bl              #0xa428f0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.toggle
    // 0xe3dde0: r0 = Null
    //     0xe3dde0: mov             x0, NULL
    // 0xe3dde4: LeaveFrame
    //     0xe3dde4: mov             SP, fp
    //     0xe3dde8: ldp             fp, lr, [SP], #0x10
    // 0xe3ddec: ret
    //     0xe3ddec: ret             
    // 0xe3ddf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3ddf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3ddf4: b               #0xe3ddc0
  }
}

// class id: 1989, size: 0x54, field offset: 0x38
class DoaListController extends _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin {

  const int dyn:get:id(DoaListController) {
    // ** addr: 0x8100b0, size: 0x48
    // 0x8100b0: ldr             x2, [SP]
    // 0x8100b4: LoadField: r3 = r2->field_3f
    //     0x8100b4: ldur            x3, [x2, #0x3f]
    // 0x8100b8: r0 = BoxInt64Instr(r3)
    //     0x8100b8: sbfiz           x0, x3, #1, #0x1f
    //     0x8100bc: cmp             x3, x0, asr #1
    //     0x8100c0: b.eq            #0x8100dc
    //     0x8100c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8100c8: mov             fp, SP
    //     0x8100cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8100d0: mov             SP, fp
    //     0x8100d4: ldp             fp, lr, [SP], #0x10
    //     0x8100d8: stur            x3, [x0, #7]
    // 0x8100dc: ret
    //     0x8100dc: ret             
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8efd58, size: 0x5c
    // 0x8efd58: EnterFrame
    //     0x8efd58: stp             fp, lr, [SP, #-0x10]!
    //     0x8efd5c: mov             fp, SP
    // 0x8efd60: AllocStack(0x8)
    //     0x8efd60: sub             SP, SP, #8
    // 0x8efd64: SetupParameters(DoaListController this /* r1 => r0, fp-0x8 */)
    //     0x8efd64: mov             x0, x1
    //     0x8efd68: stur            x1, [fp, #-8]
    // 0x8efd6c: CheckStackOverflow
    //     0x8efd6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8efd70: cmp             SP, x16
    //     0x8efd74: b.ls            #0x8efdac
    // 0x8efd78: mov             x1, x0
    // 0x8efd7c: r0 = onInit()
    //     0x8efd7c: bl              #0x8efdb4  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::onInit
    // 0x8efd80: r0 = find()
    //     0x8efd80: bl              #0x8efcf4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::find
    // 0x8efd84: mov             x1, x0
    // 0x8efd88: ldur            x0, [fp, #-8]
    // 0x8efd8c: LoadField: r2 = r0->field_3f
    //     0x8efd8c: ldur            x2, [x0, #0x3f]
    // 0x8efd90: r3 = Instance_CounterType
    //     0x8efd90: add             x3, PP, #0x40, lsl #12  ; [pp+0x40560] Obj!CounterType@e31021
    //     0x8efd94: ldr             x3, [x3, #0x560]
    // 0x8efd98: r0 = record()
    //     0x8efd98: bl              #0x8efad4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::record
    // 0x8efd9c: r0 = Null
    //     0x8efd9c: mov             x0, NULL
    // 0x8efda0: LeaveFrame
    //     0x8efda0: mov             SP, fp
    //     0x8efda4: ldp             fp, lr, [SP], #0x10
    // 0x8efda8: ret
    //     0x8efda8: ret             
    // 0x8efdac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8efdac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8efdb0: b               #0x8efd78
  }
  [closure] void onSearchSubmitted(dynamic, String, {bool saveHistory}) {
    // ** addr: 0xada680, size: 0x94
    // 0xada680: EnterFrame
    //     0xada680: stp             fp, lr, [SP, #-0x10]!
    //     0xada684: mov             fp, SP
    // 0xada688: AllocStack(0x8)
    //     0xada688: sub             SP, SP, #8
    // 0xada68c: SetupParameters(DoaListController this /* r2 */, dynamic _ /* r3 */, {dynamic saveHistory = true /* r0 */})
    //     0xada68c: ldur            w0, [x4, #0x13]
    //     0xada690: sub             x1, x0, #4
    //     0xada694: add             x2, fp, w1, sxtw #2
    //     0xada698: ldr             x2, [x2, #0x18]
    //     0xada69c: add             x3, fp, w1, sxtw #2
    //     0xada6a0: ldr             x3, [x3, #0x10]
    //     0xada6a4: ldur            w1, [x4, #0x1f]
    //     0xada6a8: add             x1, x1, HEAP, lsl #32
    //     0xada6ac: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bde8] "saveHistory"
    //     0xada6b0: ldr             x16, [x16, #0xde8]
    //     0xada6b4: cmp             w1, w16
    //     0xada6b8: b.ne            #0xada6d4
    //     0xada6bc: ldur            w1, [x4, #0x23]
    //     0xada6c0: add             x1, x1, HEAP, lsl #32
    //     0xada6c4: sub             w4, w0, w1
    //     0xada6c8: add             x0, fp, w4, sxtw #2
    //     0xada6cc: ldr             x0, [x0, #8]
    //     0xada6d0: b               #0xada6d8
    //     0xada6d4: add             x0, NULL, #0x20  ; true
    //     0xada6d8: ldur            w1, [x2, #0x17]
    //     0xada6dc: add             x1, x1, HEAP, lsl #32
    // 0xada6e0: CheckStackOverflow
    //     0xada6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada6e4: cmp             SP, x16
    //     0xada6e8: b.ls            #0xada70c
    // 0xada6ec: str             x0, [SP]
    // 0xada6f0: mov             x2, x3
    // 0xada6f4: r4 = const [0, 0x3, 0x1, 0x2, saveHistory, 0x2, null]
    //     0xada6f4: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bd10] List(7) [0, 0x3, 0x1, 0x2, "saveHistory", 0x2, Null]
    //     0xada6f8: ldr             x4, [x4, #0xd10]
    // 0xada6fc: r0 = onSearchSubmitted()
    //     0xada6fc: bl              #0xe3dd48  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onSearchSubmitted
    // 0xada700: LeaveFrame
    //     0xada700: mov             SP, fp
    //     0xada704: ldp             fp, lr, [SP], #0x10
    // 0xada708: ret
    //     0xada708: ret             
    // 0xada70c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada70c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada710: b               #0xada6ec
  }
  [closure] void onSearchCanceled(dynamic) {
    // ** addr: 0xada714, size: 0x38
    // 0xada714: EnterFrame
    //     0xada714: stp             fp, lr, [SP, #-0x10]!
    //     0xada718: mov             fp, SP
    // 0xada71c: ldr             x0, [fp, #0x10]
    // 0xada720: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada720: ldur            w1, [x0, #0x17]
    // 0xada724: DecompressPointer r1
    //     0xada724: add             x1, x1, HEAP, lsl #32
    // 0xada728: CheckStackOverflow
    //     0xada728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada72c: cmp             SP, x16
    //     0xada730: b.ls            #0xada744
    // 0xada734: r0 = onSearchCanceled()
    //     0xada734: bl              #0xada74c  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onSearchCanceled
    // 0xada738: LeaveFrame
    //     0xada738: mov             SP, fp
    //     0xada73c: ldp             fp, lr, [SP], #0x10
    // 0xada740: ret
    //     0xada740: ret             
    // 0xada744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada748: b               #0xada734
  }
  _ onSearchCanceled(/* No info */) {
    // ** addr: 0xada74c, size: 0x48
    // 0xada74c: EnterFrame
    //     0xada74c: stp             fp, lr, [SP, #-0x10]!
    //     0xada750: mov             fp, SP
    // 0xada754: AllocStack(0x8)
    //     0xada754: sub             SP, SP, #8
    // 0xada758: SetupParameters(DoaListController this /* r1 => r0, fp-0x8 */)
    //     0xada758: mov             x0, x1
    //     0xada75c: stur            x1, [fp, #-8]
    // 0xada760: CheckStackOverflow
    //     0xada760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada764: cmp             SP, x16
    //     0xada768: b.ls            #0xada78c
    // 0xada76c: mov             x1, x0
    // 0xada770: r0 = onSearchCanceled()
    //     0xada770: bl              #0xada794  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::onSearchCanceled
    // 0xada774: ldur            x1, [fp, #-8]
    // 0xada778: r0 = executeOfflineMode()
    //     0xada778: bl              #0x8ef81c  ; [package:nuonline/common/mixins/offline_first_mixin.dart] _OfflineFirstController&GetxController&OfflineMixin::executeOfflineMode
    // 0xada77c: r0 = Null
    //     0xada77c: mov             x0, NULL
    // 0xada780: LeaveFrame
    //     0xada780: mov             SP, fp
    //     0xada784: ldp             fp, lr, [SP], #0x10
    // 0xada788: ret
    //     0xada788: ret             
    // 0xada78c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada78c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada790: b               #0xada76c
  }
  _ onOfflineModeRequested(/* No info */) {
    // ** addr: 0xbef318, size: 0xc4
    // 0xbef318: EnterFrame
    //     0xbef318: stp             fp, lr, [SP, #-0x10]!
    //     0xbef31c: mov             fp, SP
    // 0xbef320: AllocStack(0x28)
    //     0xbef320: sub             SP, SP, #0x28
    // 0xbef324: SetupParameters(DoaListController this /* r1 => r1, fp-0x8 */)
    //     0xbef324: stur            x1, [fp, #-8]
    // 0xbef328: CheckStackOverflow
    //     0xbef328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef32c: cmp             SP, x16
    //     0xbef330: b.ls            #0xbef3d4
    // 0xbef334: r0 = RxStatus()
    //     0xbef334: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbef338: mov             x1, x0
    // 0xbef33c: r0 = false
    //     0xbef33c: add             x0, NULL, #0x30  ; false
    // 0xbef340: StoreField: r1->field_f = r0
    //     0xbef340: stur            w0, [x1, #0xf]
    // 0xbef344: r2 = true
    //     0xbef344: add             x2, NULL, #0x20  ; true
    // 0xbef348: StoreField: r1->field_7 = r2
    //     0xbef348: stur            w2, [x1, #7]
    // 0xbef34c: StoreField: r1->field_b = r0
    //     0xbef34c: stur            w0, [x1, #0xb]
    // 0xbef350: mov             x3, x1
    // 0xbef354: ldur            x1, [fp, #-8]
    // 0xbef358: r2 = Null
    //     0xbef358: mov             x2, NULL
    // 0xbef35c: r0 = change()
    //     0xbef35c: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbef360: ldur            x0, [fp, #-8]
    // 0xbef364: LoadField: r2 = r0->field_37
    //     0xbef364: ldur            w2, [x0, #0x37]
    // 0xbef368: DecompressPointer r2
    //     0xbef368: add             x2, x2, HEAP, lsl #32
    // 0xbef36c: stur            x2, [fp, #-0x18]
    // 0xbef370: LoadField: r3 = r0->field_3f
    //     0xbef370: ldur            x3, [x0, #0x3f]
    // 0xbef374: stur            x3, [fp, #-0x10]
    // 0xbef378: LoadField: r1 = r0->field_2f
    //     0xbef378: ldur            w1, [x0, #0x2f]
    // 0xbef37c: DecompressPointer r1
    //     0xbef37c: add             x1, x1, HEAP, lsl #32
    // 0xbef380: r0 = value()
    //     0xbef380: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbef384: mov             x3, x0
    // 0xbef388: ldur            x2, [fp, #-0x10]
    // 0xbef38c: r0 = BoxInt64Instr(r2)
    //     0xbef38c: sbfiz           x0, x2, #1, #0x1f
    //     0xbef390: cmp             x2, x0, asr #1
    //     0xbef394: b.eq            #0xbef3a0
    //     0xbef398: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef39c: stur            x2, [x0, #7]
    // 0xbef3a0: ldur            x1, [fp, #-0x18]
    // 0xbef3a4: r2 = LoadClassIdInstr(r1)
    //     0xbef3a4: ldur            x2, [x1, #-1]
    //     0xbef3a8: ubfx            x2, x2, #0xc, #0x14
    // 0xbef3ac: stp             x3, x0, [SP]
    // 0xbef3b0: mov             x0, x2
    // 0xbef3b4: r4 = const [0, 0x3, 0x2, 0x1, categoryId, 0x1, query, 0x2, null]
    //     0xbef3b4: add             x4, PP, #0x40, lsl #12  ; [pp+0x40550] List(9) [0, 0x3, 0x2, 0x1, "categoryId", 0x1, "query", 0x2, Null]
    //     0xbef3b8: ldr             x4, [x4, #0x550]
    // 0xbef3bc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbef3bc: sub             lr, x0, #1, lsl #12
    //     0xbef3c0: ldr             lr, [x21, lr, lsl #3]
    //     0xbef3c4: blr             lr
    // 0xbef3c8: LeaveFrame
    //     0xbef3c8: mov             SP, fp
    //     0xbef3cc: ldp             fp, lr, [SP], #0x10
    // 0xbef3d0: ret
    //     0xbef3d0: ret             
    // 0xbef3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef3d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef3d8: b               #0xbef334
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf5cb0, size: 0x13c
    // 0xbf5cb0: EnterFrame
    //     0xbf5cb0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5cb4: mov             fp, SP
    // 0xbf5cb8: AllocStack(0x40)
    //     0xbf5cb8: sub             SP, SP, #0x40
    // 0xbf5cbc: SetupParameters(DoaListController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbf5cbc: stur            NULL, [fp, #-8]
    //     0xbf5cc0: stur            x1, [fp, #-0x10]
    //     0xbf5cc4: stur            x2, [fp, #-0x18]
    // 0xbf5cc8: CheckStackOverflow
    //     0xbf5cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5ccc: cmp             SP, x16
    //     0xbf5cd0: b.ls            #0xbf5de4
    // 0xbf5cd4: r1 = 1
    //     0xbf5cd4: movz            x1, #0x1
    // 0xbf5cd8: r0 = AllocateContext()
    //     0xbf5cd8: bl              #0xec126c  ; AllocateContextStub
    // 0xbf5cdc: mov             x2, x0
    // 0xbf5ce0: ldur            x1, [fp, #-0x10]
    // 0xbf5ce4: stur            x2, [fp, #-0x20]
    // 0xbf5ce8: StoreField: r2->field_f = r1
    //     0xbf5ce8: stur            w1, [x2, #0xf]
    // 0xbf5cec: InitAsync() -> Future<void?>
    //     0xbf5cec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf5cf0: bl              #0x661298  ; InitAsyncStub
    // 0xbf5cf4: ldur            x2, [fp, #-0x10]
    // 0xbf5cf8: LoadField: r1 = r2->field_4f
    //     0xbf5cf8: ldur            w1, [x2, #0x4f]
    // 0xbf5cfc: DecompressPointer r1
    //     0xbf5cfc: add             x1, x1, HEAP, lsl #32
    // 0xbf5d00: r0 = LoadClassIdInstr(r1)
    //     0xbf5d00: ldur            x0, [x1, #-1]
    //     0xbf5d04: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5d08: r0 = GDT[cid_x0 + 0xd488]()
    //     0xbf5d08: movz            x17, #0xd488
    //     0xbf5d0c: add             lr, x0, x17
    //     0xbf5d10: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5d14: blr             lr
    // 0xbf5d18: tbnz            w0, #4, #0xbf5d5c
    // 0xbf5d1c: ldur            x2, [fp, #-0x20]
    // 0xbf5d20: r1 = Function '<anonymous closure>':.
    //     0xbf5d20: add             x1, PP, #0x40, lsl #12  ; [pp+0x40558] AnonymousClosure: (0xbf5dec), in [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onOfflineModeLoaded (0xbf5cb0)
    //     0xbf5d24: ldr             x1, [x1, #0x558]
    // 0xbf5d28: r0 = AllocateClosure()
    //     0xbf5d28: bl              #0xec1630  ; AllocateClosureStub
    // 0xbf5d2c: r16 = <DoaSubCategory>
    //     0xbf5d2c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xbf5d30: ldur            lr, [fp, #-0x18]
    // 0xbf5d34: stp             lr, x16, [SP, #8]
    // 0xbf5d38: str             x0, [SP]
    // 0xbf5d3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf5d3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf5d40: r0 = ListExtensions.whereIndexed()
    //     0xbf5d40: bl              #0xbf5a64  ; [package:collection/src/list_extensions.dart] ::ListExtensions.whereIndexed
    // 0xbf5d44: LoadField: r1 = r0->field_7
    //     0xbf5d44: ldur            w1, [x0, #7]
    // 0xbf5d48: DecompressPointer r1
    //     0xbf5d48: add             x1, x1, HEAP, lsl #32
    // 0xbf5d4c: mov             x2, x0
    // 0xbf5d50: r0 = _GrowableList.of()
    //     0xbf5d50: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbf5d54: mov             x2, x0
    // 0xbf5d58: b               #0xbf5d60
    // 0xbf5d5c: ldur            x2, [fp, #-0x18]
    // 0xbf5d60: ldur            x1, [fp, #-0x18]
    // 0xbf5d64: stur            x2, [fp, #-0x20]
    // 0xbf5d68: r0 = LoadClassIdInstr(r1)
    //     0xbf5d68: ldur            x0, [x1, #-1]
    //     0xbf5d6c: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5d70: r0 = GDT[cid_x0 + 0xe879]()
    //     0xbf5d70: movz            x17, #0xe879
    //     0xbf5d74: add             lr, x0, x17
    //     0xbf5d78: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5d7c: blr             lr
    // 0xbf5d80: stur            x0, [fp, #-0x18]
    // 0xbf5d84: r0 = RxStatus()
    //     0xbf5d84: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5d88: mov             x1, x0
    // 0xbf5d8c: r0 = true
    //     0xbf5d8c: add             x0, NULL, #0x20  ; true
    // 0xbf5d90: stur            x1, [fp, #-0x28]
    // 0xbf5d94: StoreField: r1->field_f = r0
    //     0xbf5d94: stur            w0, [x1, #0xf]
    // 0xbf5d98: r0 = false
    //     0xbf5d98: add             x0, NULL, #0x30  ; false
    // 0xbf5d9c: StoreField: r1->field_7 = r0
    //     0xbf5d9c: stur            w0, [x1, #7]
    // 0xbf5da0: StoreField: r1->field_b = r0
    //     0xbf5da0: stur            w0, [x1, #0xb]
    // 0xbf5da4: r0 = RxStatus()
    //     0xbf5da4: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5da8: mov             x1, x0
    // 0xbf5dac: r0 = false
    //     0xbf5dac: add             x0, NULL, #0x30  ; false
    // 0xbf5db0: StoreField: r1->field_f = r0
    //     0xbf5db0: stur            w0, [x1, #0xf]
    // 0xbf5db4: StoreField: r1->field_7 = r0
    //     0xbf5db4: stur            w0, [x1, #7]
    // 0xbf5db8: StoreField: r1->field_b = r0
    //     0xbf5db8: stur            w0, [x1, #0xb]
    // 0xbf5dbc: ldur            x0, [fp, #-0x18]
    // 0xbf5dc0: tbnz            w0, #4, #0xbf5dcc
    // 0xbf5dc4: ldur            x3, [fp, #-0x28]
    // 0xbf5dc8: b               #0xbf5dd0
    // 0xbf5dcc: mov             x3, x1
    // 0xbf5dd0: ldur            x1, [fp, #-0x10]
    // 0xbf5dd4: ldur            x2, [fp, #-0x20]
    // 0xbf5dd8: r0 = change()
    //     0xbf5dd8: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbf5ddc: r0 = Null
    //     0xbf5ddc: mov             x0, NULL
    // 0xbf5de0: r0 = ReturnAsyncNotFuture()
    //     0xbf5de0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf5de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5de4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5de8: b               #0xbf5cd4
  }
  [closure] bool <anonymous closure>(dynamic, int, DoaSubCategory) {
    // ** addr: 0xbf5dec, size: 0x68
    // 0xbf5dec: EnterFrame
    //     0xbf5dec: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5df0: mov             fp, SP
    // 0xbf5df4: ldr             x0, [fp, #0x20]
    // 0xbf5df8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf5df8: ldur            w1, [x0, #0x17]
    // 0xbf5dfc: DecompressPointer r1
    //     0xbf5dfc: add             x1, x1, HEAP, lsl #32
    // 0xbf5e00: CheckStackOverflow
    //     0xbf5e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5e04: cmp             SP, x16
    //     0xbf5e08: b.ls            #0xbf5e4c
    // 0xbf5e0c: LoadField: r0 = r1->field_f
    //     0xbf5e0c: ldur            w0, [x1, #0xf]
    // 0xbf5e10: DecompressPointer r0
    //     0xbf5e10: add             x0, x0, HEAP, lsl #32
    // 0xbf5e14: LoadField: r1 = r0->field_4f
    //     0xbf5e14: ldur            w1, [x0, #0x4f]
    // 0xbf5e18: DecompressPointer r1
    //     0xbf5e18: add             x1, x1, HEAP, lsl #32
    // 0xbf5e1c: r0 = LoadClassIdInstr(r1)
    //     0xbf5e1c: ldur            x0, [x1, #-1]
    //     0xbf5e20: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5e24: ldr             x2, [fp, #0x18]
    // 0xbf5e28: r0 = GDT[cid_x0 + 0xf20c]()
    //     0xbf5e28: movz            x17, #0xf20c
    //     0xbf5e2c: add             lr, x0, x17
    //     0xbf5e30: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5e34: blr             lr
    // 0xbf5e38: eor             x1, x0, #0x10
    // 0xbf5e3c: mov             x0, x1
    // 0xbf5e40: LeaveFrame
    //     0xbf5e40: mov             SP, fp
    //     0xbf5e44: ldp             fp, lr, [SP], #0x10
    // 0xbf5e48: ret
    //     0xbf5e48: ret             
    // 0xbf5e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5e4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5e50: b               #0xbf5e0c
  }
  _ onOfflineModeFailure(/* No info */) async {
    // ** addr: 0xc2a0bc, size: 0x88
    // 0xc2a0bc: EnterFrame
    //     0xc2a0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a0c0: mov             fp, SP
    // 0xc2a0c4: AllocStack(0x20)
    //     0xc2a0c4: sub             SP, SP, #0x20
    // 0xc2a0c8: SetupParameters(DoaListController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc2a0c8: stur            NULL, [fp, #-8]
    //     0xc2a0cc: stur            x1, [fp, #-0x10]
    //     0xc2a0d0: stur            x2, [fp, #-0x18]
    // 0xc2a0d4: CheckStackOverflow
    //     0xc2a0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a0d8: cmp             SP, x16
    //     0xc2a0dc: b.ls            #0xc2a13c
    // 0xc2a0e0: InitAsync() -> Future<void?>
    //     0xc2a0e0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc2a0e4: bl              #0x661298  ; InitAsyncStub
    // 0xc2a0e8: ldur            x1, [fp, #-0x10]
    // 0xc2a0ec: r0 = notifyChildrens()
    //     0xc2a0ec: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xc2a0f0: ldur            x1, [fp, #-0x10]
    // 0xc2a0f4: LoadField: r2 = r1->field_23
    //     0xc2a0f4: ldur            w2, [x1, #0x23]
    // 0xc2a0f8: DecompressPointer r2
    //     0xc2a0f8: add             x2, x2, HEAP, lsl #32
    // 0xc2a0fc: stur            x2, [fp, #-0x20]
    // 0xc2a100: r0 = RxStatus()
    //     0xc2a100: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xc2a104: mov             x1, x0
    // 0xc2a108: r0 = false
    //     0xc2a108: add             x0, NULL, #0x30  ; false
    // 0xc2a10c: StoreField: r1->field_f = r0
    //     0xc2a10c: stur            w0, [x1, #0xf]
    // 0xc2a110: StoreField: r1->field_7 = r0
    //     0xc2a110: stur            w0, [x1, #7]
    // 0xc2a114: r0 = true
    //     0xc2a114: add             x0, NULL, #0x20  ; true
    // 0xc2a118: StoreField: r1->field_b = r0
    //     0xc2a118: stur            w0, [x1, #0xb]
    // 0xc2a11c: ldur            x0, [fp, #-0x18]
    // 0xc2a120: StoreField: r1->field_13 = r0
    //     0xc2a120: stur            w0, [x1, #0x13]
    // 0xc2a124: mov             x3, x1
    // 0xc2a128: ldur            x1, [fp, #-0x10]
    // 0xc2a12c: ldur            x2, [fp, #-0x20]
    // 0xc2a130: r0 = change()
    //     0xc2a130: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xc2a134: r0 = Null
    //     0xc2a134: mov             x0, NULL
    // 0xc2a138: r0 = ReturnAsyncNotFuture()
    //     0xc2a138: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc2a13c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a13c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a140: b               #0xc2a0e0
  }
  _ onOnlineModeRequested(/* No info */) {
    // ** addr: 0xe34d88, size: 0x50
    // 0xe34d88: EnterFrame
    //     0xe34d88: stp             fp, lr, [SP, #-0x10]!
    //     0xe34d8c: mov             fp, SP
    // 0xe34d90: CheckStackOverflow
    //     0xe34d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34d94: cmp             SP, x16
    //     0xe34d98: b.ls            #0xe34dd0
    // 0xe34d9c: LoadField: r0 = r1->field_3b
    //     0xe34d9c: ldur            w0, [x1, #0x3b]
    // 0xe34da0: DecompressPointer r0
    //     0xe34da0: add             x0, x0, HEAP, lsl #32
    // 0xe34da4: r1 = LoadClassIdInstr(r0)
    //     0xe34da4: ldur            x1, [x0, #-1]
    //     0xe34da8: ubfx            x1, x1, #0xc, #0x14
    // 0xe34dac: mov             x16, x0
    // 0xe34db0: mov             x0, x1
    // 0xe34db4: mov             x1, x16
    // 0xe34db8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xe34db8: sub             lr, x0, #0xffc
    //     0xe34dbc: ldr             lr, [x21, lr, lsl #3]
    //     0xe34dc0: blr             lr
    // 0xe34dc4: LeaveFrame
    //     0xe34dc4: mov             SP, fp
    //     0xe34dc8: ldp             fp, lr, [SP], #0x10
    // 0xe34dcc: ret
    //     0xe34dcc: ret             
    // 0xe34dd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34dd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34dd4: b               #0xe34d9c
  }
  _ onOnlineModeLoaded(/* No info */) {
    // ** addr: 0xe34dd8, size: 0x50
    // 0xe34dd8: EnterFrame
    //     0xe34dd8: stp             fp, lr, [SP, #-0x10]!
    //     0xe34ddc: mov             fp, SP
    // 0xe34de0: CheckStackOverflow
    //     0xe34de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34de4: cmp             SP, x16
    //     0xe34de8: b.ls            #0xe34e20
    // 0xe34dec: LoadField: r0 = r1->field_37
    //     0xe34dec: ldur            w0, [x1, #0x37]
    // 0xe34df0: DecompressPointer r0
    //     0xe34df0: add             x0, x0, HEAP, lsl #32
    // 0xe34df4: r1 = LoadClassIdInstr(r0)
    //     0xe34df4: ldur            x1, [x0, #-1]
    //     0xe34df8: ubfx            x1, x1, #0xc, #0x14
    // 0xe34dfc: mov             x16, x0
    // 0xe34e00: mov             x0, x1
    // 0xe34e04: mov             x1, x16
    // 0xe34e08: r0 = GDT[cid_x0 + -0xff8]()
    //     0xe34e08: sub             lr, x0, #0xff8
    //     0xe34e0c: ldr             lr, [x21, lr, lsl #3]
    //     0xe34e10: blr             lr
    // 0xe34e14: LeaveFrame
    //     0xe34e14: mov             SP, fp
    //     0xe34e18: ldp             fp, lr, [SP], #0x10
    // 0xe34e1c: ret
    //     0xe34e1c: ret             
    // 0xe34e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34e24: b               #0xe34dec
  }
  get _ lastUpdatedAt(/* No info */) async {
    // ** addr: 0xe37c30, size: 0x80
    // 0xe37c30: EnterFrame
    //     0xe37c30: stp             fp, lr, [SP, #-0x10]!
    //     0xe37c34: mov             fp, SP
    // 0xe37c38: AllocStack(0x28)
    //     0xe37c38: sub             SP, SP, #0x28
    // 0xe37c3c: SetupParameters(DoaListController this /* r1 => r1, fp-0x10 */)
    //     0xe37c3c: stur            NULL, [fp, #-8]
    //     0xe37c40: stur            x1, [fp, #-0x10]
    // 0xe37c44: CheckStackOverflow
    //     0xe37c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37c48: cmp             SP, x16
    //     0xe37c4c: b.ls            #0xe37ca8
    // 0xe37c50: InitAsync() -> Future<String?>
    //     0xe37c50: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0xe37c54: bl              #0x661298  ; InitAsyncStub
    // 0xe37c58: ldur            x0, [fp, #-0x10]
    // 0xe37c5c: LoadField: r1 = r0->field_37
    //     0xe37c5c: ldur            w1, [x0, #0x37]
    // 0xe37c60: DecompressPointer r1
    //     0xe37c60: add             x1, x1, HEAP, lsl #32
    // 0xe37c64: r0 = LoadClassIdInstr(r1)
    //     0xe37c64: ldur            x0, [x1, #-1]
    //     0xe37c68: ubfx            x0, x0, #0xc, #0x14
    // 0xe37c6c: r0 = GDT[cid_x0 + -0xff7]()
    //     0xe37c6c: sub             lr, x0, #0xff7
    //     0xe37c70: ldr             lr, [x21, lr, lsl #3]
    //     0xe37c74: blr             lr
    // 0xe37c78: r1 = Function '<anonymous closure>':.
    //     0xe37c78: add             x1, PP, #0x47, lsl #12  ; [pp+0x47cf0] AnonymousClosure: (0xe37cb0), in [package:nuonline/app/modules/ramadhan/controllers/ramadhan_controller.dart] RamadhanController::lastUpdatedAt (0xe37e30)
    //     0xe37c7c: ldr             x1, [x1, #0xcf0]
    // 0xe37c80: r2 = Null
    //     0xe37c80: mov             x2, NULL
    // 0xe37c84: stur            x0, [fp, #-0x10]
    // 0xe37c88: r0 = AllocateClosure()
    //     0xe37c88: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37c8c: r16 = <String?>
    //     0xe37c8c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xe37c90: ldur            lr, [fp, #-0x10]
    // 0xe37c94: stp             lr, x16, [SP, #8]
    // 0xe37c98: str             x0, [SP]
    // 0xe37c9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe37c9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe37ca0: r0 = then()
    //     0xe37ca0: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe37ca4: r0 = ReturnAsync()
    //     0xe37ca4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe37ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37cac: b               #0xe37c50
  }
  _ onSearchSubmitted(/* No info */) {
    // ** addr: 0xe3dd48, size: 0x60
    // 0xe3dd48: EnterFrame
    //     0xe3dd48: stp             fp, lr, [SP, #-0x10]!
    //     0xe3dd4c: mov             fp, SP
    // 0xe3dd50: AllocStack(0x18)
    //     0xe3dd50: sub             SP, SP, #0x18
    // 0xe3dd54: SetupParameters(DoaListController this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe3dd54: mov             x0, x1
    //     0xe3dd58: stur            x1, [fp, #-8]
    //     0xe3dd5c: stur            x2, [fp, #-0x10]
    // 0xe3dd60: CheckStackOverflow
    //     0xe3dd60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3dd64: cmp             SP, x16
    //     0xe3dd68: b.ls            #0xe3dda0
    // 0xe3dd6c: mov             x1, x0
    // 0xe3dd70: r0 = executeOfflineMode()
    //     0xe3dd70: bl              #0x8ef81c  ; [package:nuonline/common/mixins/offline_first_mixin.dart] _OfflineFirstController&GetxController&OfflineMixin::executeOfflineMode
    // 0xe3dd74: r16 = false
    //     0xe3dd74: add             x16, NULL, #0x30  ; false
    // 0xe3dd78: str             x16, [SP]
    // 0xe3dd7c: ldur            x1, [fp, #-8]
    // 0xe3dd80: ldur            x2, [fp, #-0x10]
    // 0xe3dd84: r4 = const [0, 0x3, 0x1, 0x2, saveHistory, 0x2, null]
    //     0xe3dd84: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bd10] List(7) [0, 0x3, 0x1, 0x2, "saveHistory", 0x2, Null]
    //     0xe3dd88: ldr             x4, [x4, #0xd10]
    // 0xe3dd8c: r0 = onSearchSubmitted()
    //     0xe3dd8c: bl              #0xe3dda8  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::onSearchSubmitted
    // 0xe3dd90: r0 = Null
    //     0xe3dd90: mov             x0, NULL
    // 0xe3dd94: LeaveFrame
    //     0xe3dd94: mov             SP, fp
    //     0xe3dd98: ldp             fp, lr, [SP], #0x10
    // 0xe3dd9c: ret
    //     0xe3dd9c: ret             
    // 0xe3dda0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3dda0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3dda4: b               #0xe3dd6c
  }
}
