// lib: , url: package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart

// class id: 1050184, size: 0x8
class :: {
}

// class id: 5300, size: 0x20, field offset: 0x14
//   const constructor, 
class DoaListView extends GetView<dynamic> {

  bool field_14;
  _OneByteString field_1c;

  _ build(/* No info */) {
    // ** addr: 0xad9a1c, size: 0x354
    // 0xad9a1c: EnterFrame
    //     0xad9a1c: stp             fp, lr, [SP, #-0x10]!
    //     0xad9a20: mov             fp, SP
    // 0xad9a24: AllocStack(0x68)
    //     0xad9a24: sub             SP, SP, #0x68
    // 0xad9a28: SetupParameters(DoaListView this /* r1 => r1, fp-0x8 */)
    //     0xad9a28: stur            x1, [fp, #-8]
    // 0xad9a2c: CheckStackOverflow
    //     0xad9a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9a30: cmp             SP, x16
    //     0xad9a34: b.ls            #0xad9d68
    // 0xad9a38: r1 = 1
    //     0xad9a38: movz            x1, #0x1
    // 0xad9a3c: r0 = AllocateContext()
    //     0xad9a3c: bl              #0xec126c  ; AllocateContextStub
    // 0xad9a40: mov             x2, x0
    // 0xad9a44: ldur            x0, [fp, #-8]
    // 0xad9a48: stur            x2, [fp, #-0x18]
    // 0xad9a4c: StoreField: r2->field_f = r0
    //     0xad9a4c: stur            w0, [x2, #0xf]
    // 0xad9a50: LoadField: r3 = r0->field_13
    //     0xad9a50: ldur            w3, [x0, #0x13]
    // 0xad9a54: DecompressPointer r3
    //     0xad9a54: add             x3, x3, HEAP, lsl #32
    // 0xad9a58: mov             x1, x0
    // 0xad9a5c: stur            x3, [fp, #-0x10]
    // 0xad9a60: r0 = controller()
    //     0xad9a60: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9a64: LoadField: r1 = r0->field_47
    //     0xad9a64: ldur            w1, [x0, #0x47]
    // 0xad9a68: DecompressPointer r1
    //     0xad9a68: add             x1, x1, HEAP, lsl #32
    // 0xad9a6c: stur            x1, [fp, #-0x20]
    // 0xad9a70: r0 = Text()
    //     0xad9a70: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad9a74: mov             x1, x0
    // 0xad9a78: ldur            x0, [fp, #-0x20]
    // 0xad9a7c: stur            x1, [fp, #-0x28]
    // 0xad9a80: StoreField: r1->field_b = r0
    //     0xad9a80: stur            w0, [x1, #0xb]
    // 0xad9a84: r0 = AppBar()
    //     0xad9a84: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad9a88: stur            x0, [fp, #-0x20]
    // 0xad9a8c: ldur            x16, [fp, #-0x28]
    // 0xad9a90: str             x16, [SP]
    // 0xad9a94: mov             x1, x0
    // 0xad9a98: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xad9a98: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xad9a9c: ldr             x4, [x4, #0x6e8]
    // 0xad9aa0: r0 = AppBar()
    //     0xad9aa0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad9aa4: ldur            x0, [fp, #-0x10]
    // 0xad9aa8: tbnz            w0, #4, #0xad9ab4
    // 0xad9aac: r0 = Null
    //     0xad9aac: mov             x0, NULL
    // 0xad9ab0: b               #0xad9ab8
    // 0xad9ab4: ldur            x0, [fp, #-0x20]
    // 0xad9ab8: ldur            x1, [fp, #-8]
    // 0xad9abc: stur            x0, [fp, #-0x10]
    // 0xad9ac0: r0 = controller()
    //     0xad9ac0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9ac4: stur            x0, [fp, #-0x20]
    // 0xad9ac8: r0 = Obx()
    //     0xad9ac8: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad9acc: ldur            x2, [fp, #-0x18]
    // 0xad9ad0: r1 = Function '<anonymous closure>':.
    //     0xad9ad0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f870] AnonymousClosure: (0xada528), in [package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart] DoaListView::build (0xad9a1c)
    //     0xad9ad4: ldr             x1, [x1, #0x870]
    // 0xad9ad8: stur            x0, [fp, #-0x28]
    // 0xad9adc: r0 = AllocateClosure()
    //     0xad9adc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9ae0: mov             x1, x0
    // 0xad9ae4: ldur            x0, [fp, #-0x28]
    // 0xad9ae8: StoreField: r0->field_b = r1
    //     0xad9ae8: stur            w1, [x0, #0xb]
    // 0xad9aec: ldur            x1, [fp, #-8]
    // 0xad9af0: r0 = controller()
    //     0xad9af0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9af4: r1 = Function '<anonymous closure>':.
    //     0xad9af4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f878] AnonymousClosure: (0xb98358), in [package:nuonline/app/modules/tutorial/tutorial_bookmark/views/tutorial_bookmark_view.dart] TutorialBookmarkView::build (0xb593b4)
    //     0xad9af8: ldr             x1, [x1, #0x878]
    // 0xad9afc: r2 = Null
    //     0xad9afc: mov             x2, NULL
    // 0xad9b00: stur            x0, [fp, #-8]
    // 0xad9b04: r0 = AllocateClosure()
    //     0xad9b04: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9b08: r1 = Function '<anonymous closure>':.
    //     0xad9b08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f880] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xad9b0c: ldr             x1, [x1, #0x880]
    // 0xad9b10: r2 = Null
    //     0xad9b10: mov             x2, NULL
    // 0xad9b14: stur            x0, [fp, #-0x30]
    // 0xad9b18: r0 = AllocateClosure()
    //     0xad9b18: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9b1c: stur            x0, [fp, #-0x38]
    // 0xad9b20: r0 = ListView()
    //     0xad9b20: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad9b24: stur            x0, [fp, #-0x40]
    // 0xad9b28: r16 = Instance_EdgeInsets
    //     0xad9b28: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad9b2c: str             x16, [SP]
    // 0xad9b30: mov             x1, x0
    // 0xad9b34: ldur            x2, [fp, #-0x30]
    // 0xad9b38: ldur            x5, [fp, #-0x38]
    // 0xad9b3c: r3 = 8
    //     0xad9b3c: movz            x3, #0x8
    // 0xad9b40: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xad9b40: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xad9b44: ldr             x4, [x4, #0x700]
    // 0xad9b48: r0 = ListView.separated()
    //     0xad9b48: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad9b4c: ldur            x2, [fp, #-0x18]
    // 0xad9b50: r1 = Function '<anonymous closure>':.
    //     0xad9b50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f888] AnonymousClosure: (0xada01c), in [package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart] DoaListView::build (0xad9a1c)
    //     0xad9b54: ldr             x1, [x1, #0x888]
    // 0xad9b58: r0 = AllocateClosure()
    //     0xad9b58: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9b5c: ldur            x2, [fp, #-0x18]
    // 0xad9b60: r1 = Function '<anonymous closure>':.
    //     0xad9b60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] AnonymousClosure: (0xad9d70), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xad9b64: ldr             x1, [x1, #0x890]
    // 0xad9b68: stur            x0, [fp, #-0x18]
    // 0xad9b6c: r0 = AllocateClosure()
    //     0xad9b6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9b70: r16 = <List<DoaSubCategory>>
    //     0xad9b70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xad9b74: ldr             x16, [x16, #0x7d8]
    // 0xad9b78: ldur            lr, [fp, #-8]
    // 0xad9b7c: stp             lr, x16, [SP, #0x18]
    // 0xad9b80: ldur            x16, [fp, #-0x18]
    // 0xad9b84: ldur            lr, [fp, #-0x40]
    // 0xad9b88: stp             lr, x16, [SP, #8]
    // 0xad9b8c: str             x0, [SP]
    // 0xad9b90: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xad9b90: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xad9b94: ldr             x4, [x4, #0x728]
    // 0xad9b98: r0 = StateExt.obx()
    //     0xad9b98: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xad9b9c: stur            x0, [fp, #-8]
    // 0xad9ba0: r0 = ListTileTheme()
    //     0xad9ba0: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xad9ba4: mov             x2, x0
    // 0xad9ba8: r0 = Instance_EdgeInsets
    //     0xad9ba8: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2abd8] Obj!EdgeInsets@e126d1
    //     0xad9bac: ldr             x0, [x0, #0xbd8]
    // 0xad9bb0: stur            x2, [fp, #-0x18]
    // 0xad9bb4: StoreField: r2->field_2b = r0
    //     0xad9bb4: stur            w0, [x2, #0x2b]
    // 0xad9bb8: r0 = 16.000000
    //     0xad9bb8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xad9bbc: ldr             x0, [x0, #0x80]
    // 0xad9bc0: StoreField: r2->field_37 = r0
    //     0xad9bc0: stur            w0, [x2, #0x37]
    // 0xad9bc4: r0 = 24.000000
    //     0xad9bc4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xad9bc8: ldr             x0, [x0, #0x368]
    // 0xad9bcc: StoreField: r2->field_3f = r0
    //     0xad9bcc: stur            w0, [x2, #0x3f]
    // 0xad9bd0: ldur            x0, [fp, #-8]
    // 0xad9bd4: StoreField: r2->field_b = r0
    //     0xad9bd4: stur            w0, [x2, #0xb]
    // 0xad9bd8: r1 = <FlexParentData>
    //     0xad9bd8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad9bdc: ldr             x1, [x1, #0x720]
    // 0xad9be0: r0 = Expanded()
    //     0xad9be0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad9be4: mov             x3, x0
    // 0xad9be8: r0 = 1
    //     0xad9be8: movz            x0, #0x1
    // 0xad9bec: stur            x3, [fp, #-8]
    // 0xad9bf0: StoreField: r3->field_13 = r0
    //     0xad9bf0: stur            x0, [x3, #0x13]
    // 0xad9bf4: r0 = Instance_FlexFit
    //     0xad9bf4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad9bf8: ldr             x0, [x0, #0x728]
    // 0xad9bfc: StoreField: r3->field_1b = r0
    //     0xad9bfc: stur            w0, [x3, #0x1b]
    // 0xad9c00: ldur            x0, [fp, #-0x18]
    // 0xad9c04: StoreField: r3->field_b = r0
    //     0xad9c04: stur            w0, [x3, #0xb]
    // 0xad9c08: r1 = Null
    //     0xad9c08: mov             x1, NULL
    // 0xad9c0c: r2 = 4
    //     0xad9c0c: movz            x2, #0x4
    // 0xad9c10: r0 = AllocateArray()
    //     0xad9c10: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad9c14: mov             x2, x0
    // 0xad9c18: ldur            x0, [fp, #-0x28]
    // 0xad9c1c: stur            x2, [fp, #-0x18]
    // 0xad9c20: StoreField: r2->field_f = r0
    //     0xad9c20: stur            w0, [x2, #0xf]
    // 0xad9c24: ldur            x0, [fp, #-8]
    // 0xad9c28: StoreField: r2->field_13 = r0
    //     0xad9c28: stur            w0, [x2, #0x13]
    // 0xad9c2c: r1 = <Widget>
    //     0xad9c2c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad9c30: r0 = AllocateGrowableArray()
    //     0xad9c30: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad9c34: mov             x1, x0
    // 0xad9c38: ldur            x0, [fp, #-0x18]
    // 0xad9c3c: stur            x1, [fp, #-8]
    // 0xad9c40: StoreField: r1->field_f = r0
    //     0xad9c40: stur            w0, [x1, #0xf]
    // 0xad9c44: r0 = 4
    //     0xad9c44: movz            x0, #0x4
    // 0xad9c48: StoreField: r1->field_b = r0
    //     0xad9c48: stur            w0, [x1, #0xb]
    // 0xad9c4c: r0 = Column()
    //     0xad9c4c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad9c50: mov             x1, x0
    // 0xad9c54: r0 = Instance_Axis
    //     0xad9c54: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad9c58: stur            x1, [fp, #-0x18]
    // 0xad9c5c: StoreField: r1->field_f = r0
    //     0xad9c5c: stur            w0, [x1, #0xf]
    // 0xad9c60: r0 = Instance_MainAxisAlignment
    //     0xad9c60: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad9c64: ldr             x0, [x0, #0x730]
    // 0xad9c68: StoreField: r1->field_13 = r0
    //     0xad9c68: stur            w0, [x1, #0x13]
    // 0xad9c6c: r0 = Instance_MainAxisSize
    //     0xad9c6c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad9c70: ldr             x0, [x0, #0x738]
    // 0xad9c74: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9c74: stur            w0, [x1, #0x17]
    // 0xad9c78: r0 = Instance_CrossAxisAlignment
    //     0xad9c78: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad9c7c: ldr             x0, [x0, #0x740]
    // 0xad9c80: StoreField: r1->field_1b = r0
    //     0xad9c80: stur            w0, [x1, #0x1b]
    // 0xad9c84: r0 = Instance_VerticalDirection
    //     0xad9c84: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad9c88: ldr             x0, [x0, #0x748]
    // 0xad9c8c: StoreField: r1->field_23 = r0
    //     0xad9c8c: stur            w0, [x1, #0x23]
    // 0xad9c90: r0 = Instance_Clip
    //     0xad9c90: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad9c94: ldr             x0, [x0, #0x750]
    // 0xad9c98: StoreField: r1->field_2b = r0
    //     0xad9c98: stur            w0, [x1, #0x2b]
    // 0xad9c9c: StoreField: r1->field_2f = rZR
    //     0xad9c9c: stur            xzr, [x1, #0x2f]
    // 0xad9ca0: ldur            x0, [fp, #-8]
    // 0xad9ca4: StoreField: r1->field_b = r0
    //     0xad9ca4: stur            w0, [x1, #0xb]
    // 0xad9ca8: r0 = RefreshIndicator()
    //     0xad9ca8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xad9cac: mov             x3, x0
    // 0xad9cb0: ldur            x0, [fp, #-0x18]
    // 0xad9cb4: stur            x3, [fp, #-8]
    // 0xad9cb8: StoreField: r3->field_b = r0
    //     0xad9cb8: stur            w0, [x3, #0xb]
    // 0xad9cbc: d0 = 40.000000
    //     0xad9cbc: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xad9cc0: StoreField: r3->field_f = d0
    //     0xad9cc0: stur            d0, [x3, #0xf]
    // 0xad9cc4: ArrayStore: r3[0] = rZR  ; List_8
    //     0xad9cc4: stur            xzr, [x3, #0x17]
    // 0xad9cc8: ldur            x2, [fp, #-0x20]
    // 0xad9ccc: r1 = Function 'onRefresh':.
    //     0xad9ccc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f130] AnonymousClosure: (0xada87c), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xad9cd0: ldr             x1, [x1, #0x130]
    // 0xad9cd4: r0 = AllocateClosure()
    //     0xad9cd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9cd8: mov             x1, x0
    // 0xad9cdc: ldur            x0, [fp, #-8]
    // 0xad9ce0: StoreField: r0->field_1f = r1
    //     0xad9ce0: stur            w1, [x0, #0x1f]
    // 0xad9ce4: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xad9ce4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xad9ce8: ldr             x1, [x1, #0xf58]
    // 0xad9cec: StoreField: r0->field_2f = r1
    //     0xad9cec: stur            w1, [x0, #0x2f]
    // 0xad9cf0: d0 = 2.500000
    //     0xad9cf0: fmov            d0, #2.50000000
    // 0xad9cf4: StoreField: r0->field_3b = d0
    //     0xad9cf4: stur            d0, [x0, #0x3b]
    // 0xad9cf8: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xad9cf8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xad9cfc: ldr             x1, [x1, #0xa68]
    // 0xad9d00: StoreField: r0->field_47 = r1
    //     0xad9d00: stur            w1, [x0, #0x47]
    // 0xad9d04: d0 = 2.000000
    //     0xad9d04: fmov            d0, #2.00000000
    // 0xad9d08: StoreField: r0->field_4b = d0
    //     0xad9d08: stur            d0, [x0, #0x4b]
    // 0xad9d0c: r1 = Instance__IndicatorType
    //     0xad9d0c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xad9d10: ldr             x1, [x1, #0xa70]
    // 0xad9d14: StoreField: r0->field_43 = r1
    //     0xad9d14: stur            w1, [x0, #0x43]
    // 0xad9d18: r0 = Scaffold()
    //     0xad9d18: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad9d1c: ldur            x1, [fp, #-0x10]
    // 0xad9d20: StoreField: r0->field_13 = r1
    //     0xad9d20: stur            w1, [x0, #0x13]
    // 0xad9d24: ldur            x1, [fp, #-8]
    // 0xad9d28: ArrayStore: r0[0] = r1  ; List_4
    //     0xad9d28: stur            w1, [x0, #0x17]
    // 0xad9d2c: r1 = Instance_AlignmentDirectional
    //     0xad9d2c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad9d30: ldr             x1, [x1, #0x758]
    // 0xad9d34: StoreField: r0->field_2b = r1
    //     0xad9d34: stur            w1, [x0, #0x2b]
    // 0xad9d38: r1 = true
    //     0xad9d38: add             x1, NULL, #0x20  ; true
    // 0xad9d3c: StoreField: r0->field_53 = r1
    //     0xad9d3c: stur            w1, [x0, #0x53]
    // 0xad9d40: r2 = Instance_DragStartBehavior
    //     0xad9d40: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad9d44: StoreField: r0->field_57 = r2
    //     0xad9d44: stur            w2, [x0, #0x57]
    // 0xad9d48: r2 = false
    //     0xad9d48: add             x2, NULL, #0x30  ; false
    // 0xad9d4c: StoreField: r0->field_b = r2
    //     0xad9d4c: stur            w2, [x0, #0xb]
    // 0xad9d50: StoreField: r0->field_f = r2
    //     0xad9d50: stur            w2, [x0, #0xf]
    // 0xad9d54: StoreField: r0->field_5f = r1
    //     0xad9d54: stur            w1, [x0, #0x5f]
    // 0xad9d58: StoreField: r0->field_63 = r1
    //     0xad9d58: stur            w1, [x0, #0x63]
    // 0xad9d5c: LeaveFrame
    //     0xad9d5c: mov             SP, fp
    //     0xad9d60: ldp             fp, lr, [SP], #0x10
    // 0xad9d64: ret
    //     0xad9d64: ret             
    // 0xad9d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9d68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9d6c: b               #0xad9a38
  }
  [closure] ListView <anonymous closure>(dynamic, List<DoaSubCategory>?) {
    // ** addr: 0xada01c, size: 0xf4
    // 0xada01c: EnterFrame
    //     0xada01c: stp             fp, lr, [SP, #-0x10]!
    //     0xada020: mov             fp, SP
    // 0xada024: AllocStack(0x28)
    //     0xada024: sub             SP, SP, #0x28
    // 0xada028: SetupParameters()
    //     0xada028: ldr             x0, [fp, #0x18]
    //     0xada02c: ldur            w1, [x0, #0x17]
    //     0xada030: add             x1, x1, HEAP, lsl #32
    //     0xada034: stur            x1, [fp, #-8]
    // 0xada038: CheckStackOverflow
    //     0xada038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada03c: cmp             SP, x16
    //     0xada040: b.ls            #0xada104
    // 0xada044: r1 = 1
    //     0xada044: movz            x1, #0x1
    // 0xada048: r0 = AllocateContext()
    //     0xada048: bl              #0xec126c  ; AllocateContextStub
    // 0xada04c: mov             x1, x0
    // 0xada050: ldur            x0, [fp, #-8]
    // 0xada054: stur            x1, [fp, #-0x10]
    // 0xada058: StoreField: r1->field_b = r0
    //     0xada058: stur            w0, [x1, #0xb]
    // 0xada05c: ldr             x0, [fp, #0x10]
    // 0xada060: StoreField: r1->field_f = r0
    //     0xada060: stur            w0, [x1, #0xf]
    // 0xada064: cmp             w0, NULL
    // 0xada068: b.eq            #0xada10c
    // 0xada06c: r2 = LoadClassIdInstr(r0)
    //     0xada06c: ldur            x2, [x0, #-1]
    //     0xada070: ubfx            x2, x2, #0xc, #0x14
    // 0xada074: str             x0, [SP]
    // 0xada078: mov             x0, x2
    // 0xada07c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xada07c: movz            x17, #0xc834
    //     0xada080: add             lr, x0, x17
    //     0xada084: ldr             lr, [x21, lr, lsl #3]
    //     0xada088: blr             lr
    // 0xada08c: r3 = LoadInt32Instr(r0)
    //     0xada08c: sbfx            x3, x0, #1, #0x1f
    //     0xada090: tbz             w0, #0, #0xada098
    //     0xada094: ldur            x3, [x0, #7]
    // 0xada098: stur            x3, [fp, #-0x18]
    // 0xada09c: r1 = Function '<anonymous closure>':.
    //     0xada09c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f898] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xada0a0: ldr             x1, [x1, #0x898]
    // 0xada0a4: r2 = Null
    //     0xada0a4: mov             x2, NULL
    // 0xada0a8: r0 = AllocateClosure()
    //     0xada0a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xada0ac: ldur            x2, [fp, #-0x10]
    // 0xada0b0: r1 = Function '<anonymous closure>':.
    //     0xada0b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] AnonymousClosure: (0xada110), in [package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart] DoaListView::build (0xad9a1c)
    //     0xada0b4: ldr             x1, [x1, #0x8a0]
    // 0xada0b8: stur            x0, [fp, #-8]
    // 0xada0bc: r0 = AllocateClosure()
    //     0xada0bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xada0c0: stur            x0, [fp, #-0x10]
    // 0xada0c4: r0 = ListView()
    //     0xada0c4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xada0c8: stur            x0, [fp, #-0x20]
    // 0xada0cc: r16 = Instance_EdgeInsets
    //     0xada0cc: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ac48] Obj!EdgeInsets@e126a1
    //     0xada0d0: ldr             x16, [x16, #0xc48]
    // 0xada0d4: str             x16, [SP]
    // 0xada0d8: mov             x1, x0
    // 0xada0dc: ldur            x2, [fp, #-0x10]
    // 0xada0e0: ldur            x3, [fp, #-0x18]
    // 0xada0e4: ldur            x5, [fp, #-8]
    // 0xada0e8: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xada0e8: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xada0ec: ldr             x4, [x4, #0x700]
    // 0xada0f0: r0 = ListView.separated()
    //     0xada0f0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xada0f4: ldur            x0, [fp, #-0x20]
    // 0xada0f8: LeaveFrame
    //     0xada0f8: mov             SP, fp
    //     0xada0fc: ldp             fp, lr, [SP], #0x10
    // 0xada100: ret
    //     0xada100: ret             
    // 0xada104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada108: b               #0xada044
    // 0xada10c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xada10c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xada110, size: 0x104
    // 0xada110: EnterFrame
    //     0xada110: stp             fp, lr, [SP, #-0x10]!
    //     0xada114: mov             fp, SP
    // 0xada118: AllocStack(0x30)
    //     0xada118: sub             SP, SP, #0x30
    // 0xada11c: SetupParameters()
    //     0xada11c: ldr             x0, [fp, #0x20]
    //     0xada120: ldur            w1, [x0, #0x17]
    //     0xada124: add             x1, x1, HEAP, lsl #32
    //     0xada128: stur            x1, [fp, #-8]
    // 0xada12c: CheckStackOverflow
    //     0xada12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada130: cmp             SP, x16
    //     0xada134: b.ls            #0xada20c
    // 0xada138: r1 = 1
    //     0xada138: movz            x1, #0x1
    // 0xada13c: r0 = AllocateContext()
    //     0xada13c: bl              #0xec126c  ; AllocateContextStub
    // 0xada140: mov             x1, x0
    // 0xada144: ldur            x0, [fp, #-8]
    // 0xada148: stur            x1, [fp, #-0x18]
    // 0xada14c: StoreField: r1->field_b = r0
    //     0xada14c: stur            w0, [x1, #0xb]
    // 0xada150: ldr             x2, [fp, #0x10]
    // 0xada154: StoreField: r1->field_f = r2
    //     0xada154: stur            w2, [x1, #0xf]
    // 0xada158: r3 = LoadInt32Instr(r2)
    //     0xada158: sbfx            x3, x2, #1, #0x1f
    //     0xada15c: tbz             w2, #0, #0xada164
    //     0xada160: ldur            x3, [x2, #7]
    // 0xada164: add             x4, x3, #1
    // 0xada168: stur            x4, [fp, #-0x10]
    // 0xada16c: LoadField: r3 = r0->field_f
    //     0xada16c: ldur            w3, [x0, #0xf]
    // 0xada170: DecompressPointer r3
    //     0xada170: add             x3, x3, HEAP, lsl #32
    // 0xada174: r0 = LoadClassIdInstr(r3)
    //     0xada174: ldur            x0, [x3, #-1]
    //     0xada178: ubfx            x0, x0, #0xc, #0x14
    // 0xada17c: stp             x2, x3, [SP]
    // 0xada180: r0 = GDT[cid_x0 + 0x13037]()
    //     0xada180: movz            x17, #0x3037
    //     0xada184: movk            x17, #0x1, lsl #16
    //     0xada188: add             lr, x0, x17
    //     0xada18c: ldr             lr, [x21, lr, lsl #3]
    //     0xada190: blr             lr
    // 0xada194: LoadField: r1 = r0->field_f
    //     0xada194: ldur            w1, [x0, #0xf]
    // 0xada198: DecompressPointer r1
    //     0xada198: add             x1, x1, HEAP, lsl #32
    // 0xada19c: stur            x1, [fp, #-8]
    // 0xada1a0: r0 = NNumberListTile()
    //     0xada1a0: bl              #0xada214  ; AllocateNNumberListTileStub -> NNumberListTile (size=0x2c)
    // 0xada1a4: mov             x3, x0
    // 0xada1a8: ldur            x0, [fp, #-0x10]
    // 0xada1ac: stur            x3, [fp, #-0x20]
    // 0xada1b0: StoreField: r3->field_b = r0
    //     0xada1b0: stur            x0, [x3, #0xb]
    // 0xada1b4: ldur            x0, [fp, #-8]
    // 0xada1b8: StoreField: r3->field_13 = r0
    //     0xada1b8: stur            w0, [x3, #0x13]
    // 0xada1bc: ldur            x2, [fp, #-0x18]
    // 0xada1c0: r1 = Function '<anonymous closure>':.
    //     0xada1c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] AnonymousClosure: (0xada3cc), in [package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart] DoaListView::build (0xad9a1c)
    //     0xada1c4: ldr             x1, [x1, #0x8a8]
    // 0xada1c8: r0 = AllocateClosure()
    //     0xada1c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xada1cc: mov             x1, x0
    // 0xada1d0: ldur            x0, [fp, #-0x20]
    // 0xada1d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xada1d4: stur            w1, [x0, #0x17]
    // 0xada1d8: r1 = false
    //     0xada1d8: add             x1, NULL, #0x30  ; false
    // 0xada1dc: StoreField: r0->field_1b = r1
    //     0xada1dc: stur            w1, [x0, #0x1b]
    // 0xada1e0: StoreField: r0->field_1f = r1
    //     0xada1e0: stur            w1, [x0, #0x1f]
    // 0xada1e4: ldur            x2, [fp, #-0x18]
    // 0xada1e8: r1 = Function '<anonymous closure>':.
    //     0xada1e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] AnonymousClosure: (0xada220), in [package:nuonline/app/modules/doa/doa_list/views/doa_list_view.dart] DoaListView::build (0xad9a1c)
    //     0xada1ec: ldr             x1, [x1, #0x8b0]
    // 0xada1f0: r0 = AllocateClosure()
    //     0xada1f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xada1f4: mov             x1, x0
    // 0xada1f8: ldur            x0, [fp, #-0x20]
    // 0xada1fc: StoreField: r0->field_27 = r1
    //     0xada1fc: stur            w1, [x0, #0x27]
    // 0xada200: LeaveFrame
    //     0xada200: mov             SP, fp
    //     0xada204: ldp             fp, lr, [SP], #0x10
    // 0xada208: ret
    //     0xada208: ret             
    // 0xada20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada20c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada210: b               #0xada138
  }
  [closure] SubstringHighlight <anonymous closure>(dynamic, String) {
    // ** addr: 0xada220, size: 0x174
    // 0xada220: EnterFrame
    //     0xada220: stp             fp, lr, [SP, #-0x10]!
    //     0xada224: mov             fp, SP
    // 0xada228: AllocStack(0x38)
    //     0xada228: sub             SP, SP, #0x38
    // 0xada22c: SetupParameters()
    //     0xada22c: ldr             x0, [fp, #0x18]
    //     0xada230: ldur            w1, [x0, #0x17]
    //     0xada234: add             x1, x1, HEAP, lsl #32
    //     0xada238: stur            x1, [fp, #-8]
    // 0xada23c: CheckStackOverflow
    //     0xada23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada240: cmp             SP, x16
    //     0xada244: b.ls            #0xada388
    // 0xada248: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xada248: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xada24c: ldr             x0, [x0, #0x2670]
    //     0xada250: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xada254: cmp             w0, w16
    //     0xada258: b.ne            #0xada264
    //     0xada25c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xada260: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xada264: r0 = GetNavigation.textTheme()
    //     0xada264: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xada268: LoadField: r1 = r0->field_23
    //     0xada268: ldur            w1, [x0, #0x23]
    // 0xada26c: DecompressPointer r1
    //     0xada26c: add             x1, x1, HEAP, lsl #32
    // 0xada270: cmp             w1, NULL
    // 0xada274: b.eq            #0xada390
    // 0xada278: r16 = Instance_FontWeight
    //     0xada278: add             x16, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xada27c: ldr             x16, [x16, #0xcc0]
    // 0xada280: str             x16, [SP]
    // 0xada284: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xada284: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xada288: ldr             x4, [x4, #0xfe0]
    // 0xada28c: r0 = copyWith()
    //     0xada28c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada290: mov             x2, x0
    // 0xada294: ldur            x0, [fp, #-8]
    // 0xada298: stur            x2, [fp, #-0x10]
    // 0xada29c: LoadField: r1 = r0->field_b
    //     0xada29c: ldur            w1, [x0, #0xb]
    // 0xada2a0: DecompressPointer r1
    //     0xada2a0: add             x1, x1, HEAP, lsl #32
    // 0xada2a4: LoadField: r0 = r1->field_b
    //     0xada2a4: ldur            w0, [x1, #0xb]
    // 0xada2a8: DecompressPointer r0
    //     0xada2a8: add             x0, x0, HEAP, lsl #32
    // 0xada2ac: LoadField: r1 = r0->field_f
    //     0xada2ac: ldur            w1, [x0, #0xf]
    // 0xada2b0: DecompressPointer r1
    //     0xada2b0: add             x1, x1, HEAP, lsl #32
    // 0xada2b4: r0 = controller()
    //     0xada2b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada2b8: mov             x1, x0
    // 0xada2bc: r0 = query()
    //     0xada2bc: bl              #0xada394  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::query
    // 0xada2c0: r1 = LoadClassIdInstr(r0)
    //     0xada2c0: ldur            x1, [x0, #-1]
    //     0xada2c4: ubfx            x1, x1, #0xc, #0x14
    // 0xada2c8: mov             x16, x0
    // 0xada2cc: mov             x0, x1
    // 0xada2d0: mov             x1, x16
    // 0xada2d4: r2 = " "
    //     0xada2d4: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xada2d8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xada2d8: sub             lr, x0, #1, lsl #12
    //     0xada2dc: ldr             lr, [x21, lr, lsl #3]
    //     0xada2e0: blr             lr
    // 0xada2e4: r1 = _ConstMap len:10
    //     0xada2e4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xada2e8: ldr             x1, [x1, #0xc08]
    // 0xada2ec: r2 = 600
    //     0xada2ec: movz            x2, #0x258
    // 0xada2f0: stur            x0, [fp, #-8]
    // 0xada2f4: r0 = []()
    //     0xada2f4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xada2f8: r16 = <Color?>
    //     0xada2f8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xada2fc: ldr             x16, [x16, #0x98]
    // 0xada300: stp             x0, x16, [SP, #8]
    // 0xada304: r16 = Instance_MaterialColor
    //     0xada304: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xada308: ldr             x16, [x16, #0xcf0]
    // 0xada30c: str             x16, [SP]
    // 0xada310: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xada310: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xada314: r0 = mode()
    //     0xada314: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xada318: stur            x0, [fp, #-0x18]
    // 0xada31c: r0 = TextStyle()
    //     0xada31c: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xada320: mov             x1, x0
    // 0xada324: r0 = true
    //     0xada324: add             x0, NULL, #0x20  ; true
    // 0xada328: stur            x1, [fp, #-0x20]
    // 0xada32c: StoreField: r1->field_7 = r0
    //     0xada32c: stur            w0, [x1, #7]
    // 0xada330: ldur            x0, [fp, #-0x18]
    // 0xada334: StoreField: r1->field_b = r0
    //     0xada334: stur            w0, [x1, #0xb]
    // 0xada338: r0 = SubstringHighlight()
    //     0xada338: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xada33c: r1 = false
    //     0xada33c: add             x1, NULL, #0x30  ; false
    // 0xada340: StoreField: r0->field_b = r1
    //     0xada340: stur            w1, [x0, #0xb]
    // 0xada344: r2 = Instance_TextOverflow
    //     0xada344: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xada348: ldr             x2, [x2, #0xc60]
    // 0xada34c: StoreField: r0->field_f = r2
    //     0xada34c: stur            w2, [x0, #0xf]
    // 0xada350: ldur            x2, [fp, #-8]
    // 0xada354: StoreField: r0->field_1b = r2
    //     0xada354: stur            w2, [x0, #0x1b]
    // 0xada358: ldr             x2, [fp, #0x10]
    // 0xada35c: StoreField: r0->field_1f = r2
    //     0xada35c: stur            w2, [x0, #0x1f]
    // 0xada360: r2 = Instance_TextAlign
    //     0xada360: ldr             x2, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xada364: StoreField: r0->field_23 = r2
    //     0xada364: stur            w2, [x0, #0x23]
    // 0xada368: ldur            x2, [fp, #-0x10]
    // 0xada36c: StoreField: r0->field_27 = r2
    //     0xada36c: stur            w2, [x0, #0x27]
    // 0xada370: ldur            x2, [fp, #-0x20]
    // 0xada374: StoreField: r0->field_2b = r2
    //     0xada374: stur            w2, [x0, #0x2b]
    // 0xada378: StoreField: r0->field_2f = r1
    //     0xada378: stur            w1, [x0, #0x2f]
    // 0xada37c: LeaveFrame
    //     0xada37c: mov             SP, fp
    //     0xada380: ldp             fp, lr, [SP], #0x10
    // 0xada384: ret
    //     0xada384: ret             
    // 0xada388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada38c: b               #0xada248
    // 0xada390: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xada390: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xada3cc, size: 0x15c
    // 0xada3cc: EnterFrame
    //     0xada3cc: stp             fp, lr, [SP, #-0x10]!
    //     0xada3d0: mov             fp, SP
    // 0xada3d4: AllocStack(0x30)
    //     0xada3d4: sub             SP, SP, #0x30
    // 0xada3d8: SetupParameters()
    //     0xada3d8: ldr             x0, [fp, #0x10]
    //     0xada3dc: ldur            w1, [x0, #0x17]
    //     0xada3e0: add             x1, x1, HEAP, lsl #32
    //     0xada3e4: stur            x1, [fp, #-8]
    // 0xada3e8: CheckStackOverflow
    //     0xada3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada3ec: cmp             SP, x16
    //     0xada3f0: b.ls            #0xada520
    // 0xada3f4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xada3f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xada3f8: ldr             x0, [x0, #0x2670]
    //     0xada3fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xada400: cmp             w0, w16
    //     0xada404: b.ne            #0xada410
    //     0xada408: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xada40c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xada410: r1 = Null
    //     0xada410: mov             x1, NULL
    // 0xada414: r2 = 8
    //     0xada414: movz            x2, #0x8
    // 0xada418: r0 = AllocateArray()
    //     0xada418: bl              #0xec22fc  ; AllocateArrayStub
    // 0xada41c: mov             x1, x0
    // 0xada420: stur            x1, [fp, #-0x18]
    // 0xada424: r16 = "id"
    //     0xada424: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xada428: ldr             x16, [x16, #0x740]
    // 0xada42c: StoreField: r1->field_f = r16
    //     0xada42c: stur            w16, [x1, #0xf]
    // 0xada430: ldur            x0, [fp, #-8]
    // 0xada434: LoadField: r2 = r0->field_b
    //     0xada434: ldur            w2, [x0, #0xb]
    // 0xada438: DecompressPointer r2
    //     0xada438: add             x2, x2, HEAP, lsl #32
    // 0xada43c: stur            x2, [fp, #-0x10]
    // 0xada440: LoadField: r3 = r2->field_f
    //     0xada440: ldur            w3, [x2, #0xf]
    // 0xada444: DecompressPointer r3
    //     0xada444: add             x3, x3, HEAP, lsl #32
    // 0xada448: LoadField: r4 = r0->field_f
    //     0xada448: ldur            w4, [x0, #0xf]
    // 0xada44c: DecompressPointer r4
    //     0xada44c: add             x4, x4, HEAP, lsl #32
    // 0xada450: r0 = LoadClassIdInstr(r3)
    //     0xada450: ldur            x0, [x3, #-1]
    //     0xada454: ubfx            x0, x0, #0xc, #0x14
    // 0xada458: stp             x4, x3, [SP]
    // 0xada45c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xada45c: movz            x17, #0x3037
    //     0xada460: movk            x17, #0x1, lsl #16
    //     0xada464: add             lr, x0, x17
    //     0xada468: ldr             lr, [x21, lr, lsl #3]
    //     0xada46c: blr             lr
    // 0xada470: LoadField: r2 = r0->field_7
    //     0xada470: ldur            x2, [x0, #7]
    // 0xada474: r0 = BoxInt64Instr(r2)
    //     0xada474: sbfiz           x0, x2, #1, #0x1f
    //     0xada478: cmp             x2, x0, asr #1
    //     0xada47c: b.eq            #0xada488
    //     0xada480: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xada484: stur            x2, [x0, #7]
    // 0xada488: ldur            x1, [fp, #-0x18]
    // 0xada48c: ArrayStore: r1[1] = r0  ; List_4
    //     0xada48c: add             x25, x1, #0x13
    //     0xada490: str             w0, [x25]
    //     0xada494: tbz             w0, #0, #0xada4b0
    //     0xada498: ldurb           w16, [x1, #-1]
    //     0xada49c: ldurb           w17, [x0, #-1]
    //     0xada4a0: and             x16, x17, x16, lsr #2
    //     0xada4a4: tst             x16, HEAP, lsr #32
    //     0xada4a8: b.eq            #0xada4b0
    //     0xada4ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xada4b0: ldur            x0, [fp, #-0x18]
    // 0xada4b4: r16 = "showBookmark"
    //     0xada4b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f800] "showBookmark"
    //     0xada4b8: ldr             x16, [x16, #0x800]
    // 0xada4bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xada4bc: stur            w16, [x0, #0x17]
    // 0xada4c0: ldur            x1, [fp, #-0x10]
    // 0xada4c4: LoadField: r2 = r1->field_b
    //     0xada4c4: ldur            w2, [x1, #0xb]
    // 0xada4c8: DecompressPointer r2
    //     0xada4c8: add             x2, x2, HEAP, lsl #32
    // 0xada4cc: LoadField: r1 = r2->field_f
    //     0xada4cc: ldur            w1, [x2, #0xf]
    // 0xada4d0: DecompressPointer r1
    //     0xada4d0: add             x1, x1, HEAP, lsl #32
    // 0xada4d4: r0 = controller()
    //     0xada4d4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada4d8: LoadField: r1 = r0->field_4b
    //     0xada4d8: ldur            w1, [x0, #0x4b]
    // 0xada4dc: DecompressPointer r1
    //     0xada4dc: add             x1, x1, HEAP, lsl #32
    // 0xada4e0: ldur            x0, [fp, #-0x18]
    // 0xada4e4: StoreField: r0->field_1b = r1
    //     0xada4e4: stur            w1, [x0, #0x1b]
    // 0xada4e8: r16 = <String, Object>
    //     0xada4e8: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xada4ec: ldr             x16, [x16, #0x790]
    // 0xada4f0: stp             x0, x16, [SP]
    // 0xada4f4: r0 = Map._fromLiteral()
    //     0xada4f4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xada4f8: r16 = "/doa/doa-detail"
    //     0xada4f8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c220] "/doa/doa-detail"
    //     0xada4fc: ldr             x16, [x16, #0x220]
    // 0xada500: stp             x16, NULL, [SP, #8]
    // 0xada504: str             x0, [SP]
    // 0xada508: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xada508: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xada50c: ldr             x4, [x4, #0x478]
    // 0xada510: r0 = GetNavigation.toNamed()
    //     0xada510: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xada514: LeaveFrame
    //     0xada514: mov             SP, fp
    //     0xada518: ldp             fp, lr, [SP], #0x10
    // 0xada51c: ret
    //     0xada51c: ret             
    // 0xada520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada524: b               #0xada3f4
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xada528, size: 0x158
    // 0xada528: EnterFrame
    //     0xada528: stp             fp, lr, [SP, #-0x10]!
    //     0xada52c: mov             fp, SP
    // 0xada530: AllocStack(0x30)
    //     0xada530: sub             SP, SP, #0x30
    // 0xada534: SetupParameters()
    //     0xada534: ldr             x0, [fp, #0x10]
    //     0xada538: ldur            w2, [x0, #0x17]
    //     0xada53c: add             x2, x2, HEAP, lsl #32
    //     0xada540: stur            x2, [fp, #-8]
    // 0xada544: CheckStackOverflow
    //     0xada544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada548: cmp             SP, x16
    //     0xada54c: b.ls            #0xada66c
    // 0xada550: LoadField: r1 = r2->field_f
    //     0xada550: ldur            w1, [x2, #0xf]
    // 0xada554: DecompressPointer r1
    //     0xada554: add             x1, x1, HEAP, lsl #32
    // 0xada558: r0 = controller()
    //     0xada558: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada55c: LoadField: r1 = r0->field_2f
    //     0xada55c: ldur            w1, [x0, #0x2f]
    // 0xada560: DecompressPointer r1
    //     0xada560: add             x1, x1, HEAP, lsl #32
    // 0xada564: r0 = value()
    //     0xada564: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xada568: mov             x2, x0
    // 0xada56c: ldur            x0, [fp, #-8]
    // 0xada570: stur            x2, [fp, #-0x10]
    // 0xada574: LoadField: r1 = r0->field_f
    //     0xada574: ldur            w1, [x0, #0xf]
    // 0xada578: DecompressPointer r1
    //     0xada578: add             x1, x1, HEAP, lsl #32
    // 0xada57c: r0 = controller()
    //     0xada57c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada580: LoadField: r2 = r0->field_2b
    //     0xada580: ldur            w2, [x0, #0x2b]
    // 0xada584: DecompressPointer r2
    //     0xada584: add             x2, x2, HEAP, lsl #32
    // 0xada588: r16 = Sentinel
    //     0xada588: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xada58c: cmp             w2, w16
    // 0xada590: b.eq            #0xada674
    // 0xada594: ldur            x0, [fp, #-8]
    // 0xada598: stur            x2, [fp, #-0x18]
    // 0xada59c: LoadField: r1 = r0->field_f
    //     0xada59c: ldur            w1, [x0, #0xf]
    // 0xada5a0: DecompressPointer r1
    //     0xada5a0: add             x1, x1, HEAP, lsl #32
    // 0xada5a4: r0 = controller()
    //     0xada5a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada5a8: mov             x2, x0
    // 0xada5ac: ldur            x0, [fp, #-8]
    // 0xada5b0: stur            x2, [fp, #-0x20]
    // 0xada5b4: LoadField: r1 = r0->field_f
    //     0xada5b4: ldur            w1, [x0, #0xf]
    // 0xada5b8: DecompressPointer r1
    //     0xada5b8: add             x1, x1, HEAP, lsl #32
    // 0xada5bc: r0 = controller()
    //     0xada5bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada5c0: mov             x2, x0
    // 0xada5c4: ldur            x0, [fp, #-8]
    // 0xada5c8: stur            x2, [fp, #-0x28]
    // 0xada5cc: LoadField: r1 = r0->field_f
    //     0xada5cc: ldur            w1, [x0, #0xf]
    // 0xada5d0: DecompressPointer r1
    //     0xada5d0: add             x1, x1, HEAP, lsl #32
    // 0xada5d4: r0 = controller()
    //     0xada5d4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada5d8: stur            x0, [fp, #-8]
    // 0xada5dc: r0 = NSearchTextField()
    //     0xada5dc: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xada5e0: mov             x3, x0
    // 0xada5e4: ldur            x0, [fp, #-0x18]
    // 0xada5e8: stur            x3, [fp, #-0x30]
    // 0xada5ec: StoreField: r3->field_f = r0
    //     0xada5ec: stur            w0, [x3, #0xf]
    // 0xada5f0: ldur            x0, [fp, #-0x10]
    // 0xada5f4: StoreField: r3->field_b = r0
    //     0xada5f4: stur            w0, [x3, #0xb]
    // 0xada5f8: ldur            x2, [fp, #-0x20]
    // 0xada5fc: r1 = Function 'onQueryChanged':.
    //     0xada5fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] AnonymousClosure: (0xada7d4), in [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::onQueryChanged (0xada810)
    //     0xada600: ldr             x1, [x1, #0x8b8]
    // 0xada604: r0 = AllocateClosure()
    //     0xada604: bl              #0xec1630  ; AllocateClosureStub
    // 0xada608: mov             x1, x0
    // 0xada60c: ldur            x0, [fp, #-0x30]
    // 0xada610: StoreField: r0->field_13 = r1
    //     0xada610: stur            w1, [x0, #0x13]
    // 0xada614: ldur            x2, [fp, #-0x28]
    // 0xada618: r1 = Function 'onSearchCanceled':.
    //     0xada618: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8c0] AnonymousClosure: (0xada714), in [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onSearchCanceled (0xada74c)
    //     0xada61c: ldr             x1, [x1, #0x8c0]
    // 0xada620: r0 = AllocateClosure()
    //     0xada620: bl              #0xec1630  ; AllocateClosureStub
    // 0xada624: mov             x1, x0
    // 0xada628: ldur            x0, [fp, #-0x30]
    // 0xada62c: ArrayStore: r0[0] = r1  ; List_4
    //     0xada62c: stur            w1, [x0, #0x17]
    // 0xada630: r1 = "Cari"
    //     0xada630: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac20] "Cari"
    //     0xada634: ldr             x1, [x1, #0xc20]
    // 0xada638: StoreField: r0->field_23 = r1
    //     0xada638: stur            w1, [x0, #0x23]
    // 0xada63c: ldur            x2, [fp, #-8]
    // 0xada640: r1 = Function 'onSearchSubmitted':.
    //     0xada640: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8c8] AnonymousClosure: (0xada680), in [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] DoaListController::onSearchSubmitted (0xe3dd48)
    //     0xada644: ldr             x1, [x1, #0x8c8]
    // 0xada648: r0 = AllocateClosure()
    //     0xada648: bl              #0xec1630  ; AllocateClosureStub
    // 0xada64c: mov             x1, x0
    // 0xada650: ldur            x0, [fp, #-0x30]
    // 0xada654: StoreField: r0->field_1b = r1
    //     0xada654: stur            w1, [x0, #0x1b]
    // 0xada658: r1 = false
    //     0xada658: add             x1, NULL, #0x30  ; false
    // 0xada65c: StoreField: r0->field_27 = r1
    //     0xada65c: stur            w1, [x0, #0x27]
    // 0xada660: LeaveFrame
    //     0xada660: mov             SP, fp
    //     0xada664: ldp             fp, lr, [SP], #0x10
    // 0xada668: ret
    //     0xada668: ret             
    // 0xada66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada66c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada670: b               #0xada550
    // 0xada674: r9 = searchController
    //     0xada674: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f8d0] Field <_DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&<EMAIL>>: late (offset: 0x2c)
    //     0xada678: ldr             x9, [x9, #0x8d0]
    // 0xada67c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xada67c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
