// lib: , url: package:nuonline/app/modules/doa/doa_search/bindings/doa_search_binding.dart

// class id: 1050185, size: 0x8
class :: {
}

// class id: 2182, size: 0x8, field offset: 0x8
class DoaSearchBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x810100, size: 0x70
    // 0x810100: EnterFrame
    //     0x810100: stp             fp, lr, [SP, #-0x10]!
    //     0x810104: mov             fp, SP
    // 0x810108: AllocStack(0x10)
    //     0x810108: sub             SP, SP, #0x10
    // 0x81010c: CheckStackOverflow
    //     0x81010c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810110: cmp             SP, x16
    //     0x810114: b.ls            #0x810168
    // 0x810118: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x810118: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81011c: ldr             x0, [x0, #0x2670]
    //     0x810120: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x810124: cmp             w0, w16
    //     0x810128: b.ne            #0x810134
    //     0x81012c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x810130: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x810134: r1 = Function '<anonymous closure>':.
    //     0x810134: add             x1, PP, #0x35, lsl #12  ; [pp+0x35bf0] AnonymousClosure: (0x810170), in [package:nuonline/app/modules/doa/doa_search/bindings/doa_search_binding.dart] DoaSearchBinding::dependencies (0x810100)
    //     0x810138: ldr             x1, [x1, #0xbf0]
    // 0x81013c: r2 = Null
    //     0x81013c: mov             x2, NULL
    // 0x810140: r0 = AllocateClosure()
    //     0x810140: bl              #0xec1630  ; AllocateClosureStub
    // 0x810144: r16 = <DoaSearchController>
    //     0x810144: add             x16, PP, #0x24, lsl #12  ; [pp+0x24c00] TypeArguments: <DoaSearchController>
    //     0x810148: ldr             x16, [x16, #0xc00]
    // 0x81014c: stp             x0, x16, [SP]
    // 0x810150: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x810150: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x810154: r0 = Inst.lazyPut()
    //     0x810154: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x810158: r0 = Null
    //     0x810158: mov             x0, NULL
    // 0x81015c: LeaveFrame
    //     0x81015c: mov             SP, fp
    //     0x810160: ldp             fp, lr, [SP], #0x10
    // 0x810164: ret
    //     0x810164: ret             
    // 0x810168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81016c: b               #0x810118
  }
  [closure] DoaSearchController <anonymous closure>(dynamic) {
    // ** addr: 0x810170, size: 0x58
    // 0x810170: EnterFrame
    //     0x810170: stp             fp, lr, [SP, #-0x10]!
    //     0x810174: mov             fp, SP
    // 0x810178: AllocStack(0x10)
    //     0x810178: sub             SP, SP, #0x10
    // 0x81017c: CheckStackOverflow
    //     0x81017c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810180: cmp             SP, x16
    //     0x810184: b.ls            #0x8101c0
    // 0x810188: r0 = find()
    //     0x810188: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0x81018c: stur            x0, [fp, #-8]
    // 0x810190: r0 = find()
    //     0x810190: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0x810194: r0 = DoaSearchController()
    //     0x810194: bl              #0x810298  ; AllocateDoaSearchControllerStub -> DoaSearchController (size=0x3c)
    // 0x810198: mov             x2, x0
    // 0x81019c: ldur            x0, [fp, #-8]
    // 0x8101a0: stur            x2, [fp, #-0x10]
    // 0x8101a4: StoreField: r2->field_37 = r0
    //     0x8101a4: stur            w0, [x2, #0x37]
    // 0x8101a8: mov             x1, x2
    // 0x8101ac: r0 = _QuranSearchController&GetxController&SearchMixin()
    //     0x8101ac: bl              #0x8101c8  ; [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::_QuranSearchController&GetxController&SearchMixin
    // 0x8101b0: ldur            x0, [fp, #-0x10]
    // 0x8101b4: LeaveFrame
    //     0x8101b4: mov             SP, fp
    //     0x8101b8: ldp             fp, lr, [SP], #0x10
    // 0x8101bc: ret
    //     0x8101bc: ret             
    // 0x8101c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8101c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8101c4: b               #0x810188
  }
}
