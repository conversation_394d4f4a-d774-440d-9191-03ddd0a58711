// lib: , url: package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart

// class id: 1050186, size: 0x8
class :: {
}

// class id: 1949, size: 0x38, field offset: 0x30
//   transformed mixin,
abstract class _DoaSearchController&GetxController&SearchMixin&StateMixin extends _QuranSearchController&GetxController&SearchMixin
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x8f3790, size: 0xbc
    // 0x8f3790: EnterFrame
    //     0x8f3790: stp             fp, lr, [SP, #-0x10]!
    //     0x8f3794: mov             fp, SP
    // 0x8f3798: AllocStack(0x20)
    //     0x8f3798: sub             SP, SP, #0x20
    // 0x8f379c: SetupParameters(_DoaSearchController&GetxController&SearchMixin&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x8f379c: stur            x1, [fp, #-8]
    //     0x8f37a0: mov             x16, x2
    //     0x8f37a4: mov             x2, x1
    //     0x8f37a8: mov             x1, x16
    //     0x8f37ac: mov             x0, x3
    //     0x8f37b0: stur            x1, [fp, #-0x10]
    // 0x8f37b4: CheckStackOverflow
    //     0x8f37b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f37b8: cmp             SP, x16
    //     0x8f37bc: b.ls            #0x8f3844
    // 0x8f37c0: StoreField: r2->field_33 = r0
    //     0x8f37c0: stur            w0, [x2, #0x33]
    //     0x8f37c4: ldurb           w16, [x2, #-1]
    //     0x8f37c8: ldurb           w17, [x0, #-1]
    //     0x8f37cc: and             x16, x17, x16, lsr #2
    //     0x8f37d0: tst             x16, HEAP, lsr #32
    //     0x8f37d4: b.eq            #0x8f37dc
    //     0x8f37d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f37dc: LoadField: r0 = r2->field_2f
    //     0x8f37dc: ldur            w0, [x2, #0x2f]
    // 0x8f37e0: DecompressPointer r0
    //     0x8f37e0: add             x0, x0, HEAP, lsl #32
    // 0x8f37e4: r3 = LoadClassIdInstr(r1)
    //     0x8f37e4: ldur            x3, [x1, #-1]
    //     0x8f37e8: ubfx            x3, x3, #0xc, #0x14
    // 0x8f37ec: stp             x0, x1, [SP]
    // 0x8f37f0: mov             x0, x3
    // 0x8f37f4: mov             lr, x0
    // 0x8f37f8: ldr             lr, [x21, lr, lsl #3]
    // 0x8f37fc: blr             lr
    // 0x8f3800: tbz             w0, #4, #0x8f382c
    // 0x8f3804: ldur            x1, [fp, #-8]
    // 0x8f3808: ldur            x0, [fp, #-0x10]
    // 0x8f380c: StoreField: r1->field_2f = r0
    //     0x8f380c: stur            w0, [x1, #0x2f]
    //     0x8f3810: ldurb           w16, [x1, #-1]
    //     0x8f3814: ldurb           w17, [x0, #-1]
    //     0x8f3818: and             x16, x17, x16, lsr #2
    //     0x8f381c: tst             x16, HEAP, lsr #32
    //     0x8f3820: b.eq            #0x8f3828
    //     0x8f3824: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f3828: b               #0x8f3830
    // 0x8f382c: ldur            x1, [fp, #-8]
    // 0x8f3830: r0 = _notifyUpdate()
    //     0x8f3830: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x8f3834: r0 = Null
    //     0x8f3834: mov             x0, NULL
    // 0x8f3838: LeaveFrame
    //     0x8f3838: mov             SP, fp
    //     0x8f383c: ldp             fp, lr, [SP], #0x10
    // 0x8f3840: ret
    //     0x8f3840: ret             
    // 0x8f3844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3848: b               #0x8f37c0
  }
}

// class id: 1950, size: 0x38, field offset: 0x38
//   transformed mixin,
abstract class _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin extends _DoaSearchController&GetxController&SearchMixin&StateMixin
     with AnalyticMixin {
}

// class id: 1951, size: 0x38, field offset: 0x38
//   transformed mixin,
abstract class _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin extends _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin
     with OfflineMixin<X0> {

  _ executeOfflineMode(/* No info */) async {
    // ** addr: 0xadc408, size: 0x80
    // 0xadc408: EnterFrame
    //     0xadc408: stp             fp, lr, [SP, #-0x10]!
    //     0xadc40c: mov             fp, SP
    // 0xadc410: AllocStack(0x30)
    //     0xadc410: sub             SP, SP, #0x30
    // 0xadc414: SetupParameters(_DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin this /* r1 => r1, fp-0x10 */)
    //     0xadc414: stur            NULL, [fp, #-8]
    //     0xadc418: stur            x1, [fp, #-0x10]
    // 0xadc41c: CheckStackOverflow
    //     0xadc41c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc420: cmp             SP, x16
    //     0xadc424: b.ls            #0xadc480
    // 0xadc428: r1 = 1
    //     0xadc428: movz            x1, #0x1
    // 0xadc42c: r0 = AllocateContext()
    //     0xadc42c: bl              #0xec126c  ; AllocateContextStub
    // 0xadc430: mov             x2, x0
    // 0xadc434: ldur            x1, [fp, #-0x10]
    // 0xadc438: stur            x2, [fp, #-0x18]
    // 0xadc43c: StoreField: r2->field_f = r1
    //     0xadc43c: stur            w1, [x2, #0xf]
    // 0xadc440: InitAsync() -> Future<void?>
    //     0xadc440: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xadc444: bl              #0x661298  ; InitAsyncStub
    // 0xadc448: ldur            x1, [fp, #-0x10]
    // 0xadc44c: r0 = onOfflineModeRequested()
    //     0xadc44c: bl              #0xbef96c  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onOfflineModeRequested
    // 0xadc450: ldur            x2, [fp, #-0x18]
    // 0xadc454: r1 = Function '<anonymous closure>':.
    //     0xadc454: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bd20] AnonymousClosure: (0xadc488), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin::executeOfflineMode (0xadc408)
    //     0xadc458: ldr             x1, [x1, #0xd20]
    // 0xadc45c: stur            x0, [fp, #-0x10]
    // 0xadc460: r0 = AllocateClosure()
    //     0xadc460: bl              #0xec1630  ; AllocateClosureStub
    // 0xadc464: r16 = <void?>
    //     0xadc464: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xadc468: ldur            lr, [fp, #-0x10]
    // 0xadc46c: stp             lr, x16, [SP, #8]
    // 0xadc470: str             x0, [SP]
    // 0xadc474: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadc474: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadc478: r0 = then()
    //     0xadc478: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xadc47c: r0 = ReturnAsync()
    //     0xadc47c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xadc480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc484: b               #0xadc428
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<DoaSubCategory>>) {
    // ** addr: 0xadc488, size: 0x94
    // 0xadc488: EnterFrame
    //     0xadc488: stp             fp, lr, [SP, #-0x10]!
    //     0xadc48c: mov             fp, SP
    // 0xadc490: AllocStack(0x28)
    //     0xadc490: sub             SP, SP, #0x28
    // 0xadc494: SetupParameters()
    //     0xadc494: ldr             x0, [fp, #0x18]
    //     0xadc498: ldur            w3, [x0, #0x17]
    //     0xadc49c: add             x3, x3, HEAP, lsl #32
    //     0xadc4a0: stur            x3, [fp, #-8]
    // 0xadc4a4: CheckStackOverflow
    //     0xadc4a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc4a8: cmp             SP, x16
    //     0xadc4ac: b.ls            #0xadc514
    // 0xadc4b0: mov             x2, x3
    // 0xadc4b4: r1 = Function '<anonymous closure>':.
    //     0xadc4b4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bd28] AnonymousClosure: (0xadc574), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin::executeOfflineMode (0xadc408)
    //     0xadc4b8: ldr             x1, [x1, #0xd28]
    // 0xadc4bc: r0 = AllocateClosure()
    //     0xadc4bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xadc4c0: ldur            x2, [fp, #-8]
    // 0xadc4c4: r1 = Function '<anonymous closure>':.
    //     0xadc4c4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bd30] AnonymousClosure: (0xadc51c), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin::executeOfflineMode (0xadc408)
    //     0xadc4c8: ldr             x1, [x1, #0xd30]
    // 0xadc4cc: stur            x0, [fp, #-8]
    // 0xadc4d0: r0 = AllocateClosure()
    //     0xadc4d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xadc4d4: mov             x1, x0
    // 0xadc4d8: ldr             x0, [fp, #0x10]
    // 0xadc4dc: r2 = LoadClassIdInstr(r0)
    //     0xadc4dc: ldur            x2, [x0, #-1]
    //     0xadc4e0: ubfx            x2, x2, #0xc, #0x14
    // 0xadc4e4: r16 = <Object?>
    //     0xadc4e4: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xadc4e8: stp             x0, x16, [SP, #0x10]
    // 0xadc4ec: ldur            x16, [fp, #-8]
    // 0xadc4f0: stp             x16, x1, [SP]
    // 0xadc4f4: mov             x0, x2
    // 0xadc4f8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xadc4f8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xadc4fc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xadc4fc: sub             lr, x0, #1, lsl #12
    //     0xadc500: ldr             lr, [x21, lr, lsl #3]
    //     0xadc504: blr             lr
    // 0xadc508: LeaveFrame
    //     0xadc508: mov             SP, fp
    //     0xadc50c: ldp             fp, lr, [SP], #0x10
    // 0xadc510: ret
    //     0xadc510: ret             
    // 0xadc514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc518: b               #0xadc4b0
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0xadc51c, size: 0x58
    // 0xadc51c: EnterFrame
    //     0xadc51c: stp             fp, lr, [SP, #-0x10]!
    //     0xadc520: mov             fp, SP
    // 0xadc524: AllocStack(0x8)
    //     0xadc524: sub             SP, SP, #8
    // 0xadc528: SetupParameters()
    //     0xadc528: ldr             x0, [fp, #0x18]
    //     0xadc52c: ldur            w1, [x0, #0x17]
    //     0xadc530: add             x1, x1, HEAP, lsl #32
    // 0xadc534: CheckStackOverflow
    //     0xadc534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc538: cmp             SP, x16
    //     0xadc53c: b.ls            #0xadc56c
    // 0xadc540: LoadField: r0 = r1->field_f
    //     0xadc540: ldur            w0, [x1, #0xf]
    // 0xadc544: DecompressPointer r0
    //     0xadc544: add             x0, x0, HEAP, lsl #32
    // 0xadc548: ldr             x1, [fp, #0x10]
    // 0xadc54c: stur            x0, [fp, #-8]
    // 0xadc550: r0 = getErrorMessage()
    //     0xadc550: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0xadc554: ldur            x1, [fp, #-8]
    // 0xadc558: mov             x2, x0
    // 0xadc55c: r0 = onOfflineModeFailure()
    //     0xadc55c: bl              #0xc2a480  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onOfflineModeFailure
    // 0xadc560: LeaveFrame
    //     0xadc560: mov             SP, fp
    //     0xadc564: ldp             fp, lr, [SP], #0x10
    // 0xadc568: ret
    //     0xadc568: ret             
    // 0xadc56c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc56c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc570: b               #0xadc540
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<DoaSubCategory>, Pagination?) {
    // ** addr: 0xadc574, size: 0x48
    // 0xadc574: EnterFrame
    //     0xadc574: stp             fp, lr, [SP, #-0x10]!
    //     0xadc578: mov             fp, SP
    // 0xadc57c: ldr             x0, [fp, #0x20]
    // 0xadc580: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadc580: ldur            w1, [x0, #0x17]
    // 0xadc584: DecompressPointer r1
    //     0xadc584: add             x1, x1, HEAP, lsl #32
    // 0xadc588: CheckStackOverflow
    //     0xadc588: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc58c: cmp             SP, x16
    //     0xadc590: b.ls            #0xadc5b4
    // 0xadc594: LoadField: r0 = r1->field_f
    //     0xadc594: ldur            w0, [x1, #0xf]
    // 0xadc598: DecompressPointer r0
    //     0xadc598: add             x0, x0, HEAP, lsl #32
    // 0xadc59c: mov             x1, x0
    // 0xadc5a0: ldr             x2, [fp, #0x18]
    // 0xadc5a4: r0 = onOfflineModeLoaded()
    //     0xadc5a4: bl              #0xbf6934  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onOfflineModeLoaded
    // 0xadc5a8: LeaveFrame
    //     0xadc5a8: mov             SP, fp
    //     0xadc5ac: ldp             fp, lr, [SP], #0x10
    // 0xadc5b0: ret
    //     0xadc5b0: ret             
    // 0xadc5b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc5b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc5b8: b               #0xadc594
  }
}

// class id: 1952, size: 0x3c, field offset: 0x38
class DoaSearchController extends _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8f369c, size: 0xf4
    // 0x8f369c: EnterFrame
    //     0x8f369c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f36a0: mov             fp, SP
    // 0x8f36a4: AllocStack(0x30)
    //     0x8f36a4: sub             SP, SP, #0x30
    // 0x8f36a8: SetupParameters(DoaSearchController this /* r1 => r0, fp-0x8 */)
    //     0x8f36a8: mov             x0, x1
    //     0x8f36ac: stur            x1, [fp, #-8]
    // 0x8f36b0: CheckStackOverflow
    //     0x8f36b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f36b4: cmp             SP, x16
    //     0x8f36b8: b.ls            #0x8f3788
    // 0x8f36bc: mov             x1, x0
    // 0x8f36c0: r0 = onInit()
    //     0x8f36c0: bl              #0x8f384c  ; [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::onInit
    // 0x8f36c4: ldur            x2, [fp, #-8]
    // 0x8f36c8: LoadField: r1 = r2->field_37
    //     0x8f36c8: ldur            w1, [x2, #0x37]
    // 0x8f36cc: DecompressPointer r1
    //     0x8f36cc: add             x1, x1, HEAP, lsl #32
    // 0x8f36d0: r0 = LoadClassIdInstr(r1)
    //     0x8f36d0: ldur            x0, [x1, #-1]
    //     0x8f36d4: ubfx            x0, x0, #0xc, #0x14
    // 0x8f36d8: r0 = GDT[cid_x0 + -0xfd3]()
    //     0x8f36d8: sub             lr, x0, #0xfd3
    //     0x8f36dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8f36e0: blr             lr
    // 0x8f36e4: mov             x4, x0
    // 0x8f36e8: ldur            x0, [fp, #-8]
    // 0x8f36ec: stur            x4, [fp, #-0x10]
    // 0x8f36f0: LoadField: r2 = r0->field_27
    //     0x8f36f0: ldur            w2, [x0, #0x27]
    // 0x8f36f4: DecompressPointer r2
    //     0x8f36f4: add             x2, x2, HEAP, lsl #32
    // 0x8f36f8: LoadField: r3 = r2->field_7
    //     0x8f36f8: ldur            w3, [x2, #7]
    // 0x8f36fc: DecompressPointer r3
    //     0x8f36fc: add             x3, x3, HEAP, lsl #32
    // 0x8f3700: r1 = Function 'call':.
    //     0x8f3700: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fcd8] AnonymousClosure: (0x8f38c8), in [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::call (0x8f3938)
    //     0x8f3704: ldr             x1, [x1, #0xcd8]
    // 0x8f3708: r0 = AllocateClosureTA()
    //     0x8f3708: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x8f370c: mov             x3, x0
    // 0x8f3710: r2 = Null
    //     0x8f3710: mov             x2, NULL
    // 0x8f3714: r1 = Null
    //     0x8f3714: mov             x1, NULL
    // 0x8f3718: stur            x3, [fp, #-0x18]
    // 0x8f371c: r8 = (dynamic this, List<String>?) => List<String>
    //     0x8f371c: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3fce0] FunctionType: (dynamic this, List<String>?) => List<String>
    //     0x8f3720: ldr             x8, [x8, #0xce0]
    // 0x8f3724: r3 = Null
    //     0x8f3724: add             x3, PP, #0x40, lsl #12  ; [pp+0x40540] Null
    //     0x8f3728: ldr             x3, [x3, #0x540]
    // 0x8f372c: r0 = DefaultTypeTest()
    //     0x8f372c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8f3730: r16 = <List<String>>
    //     0x8f3730: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x8f3734: ldr             x16, [x16, #0xb40]
    // 0x8f3738: ldur            lr, [fp, #-0x10]
    // 0x8f373c: stp             lr, x16, [SP, #8]
    // 0x8f3740: ldur            x16, [fp, #-0x18]
    // 0x8f3744: str             x16, [SP]
    // 0x8f3748: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f3748: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f374c: r0 = then()
    //     0x8f374c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f3750: r0 = RxStatus()
    //     0x8f3750: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8f3754: mov             x1, x0
    // 0x8f3758: r0 = false
    //     0x8f3758: add             x0, NULL, #0x30  ; false
    // 0x8f375c: StoreField: r1->field_f = r0
    //     0x8f375c: stur            w0, [x1, #0xf]
    // 0x8f3760: StoreField: r1->field_7 = r0
    //     0x8f3760: stur            w0, [x1, #7]
    // 0x8f3764: StoreField: r1->field_b = r0
    //     0x8f3764: stur            w0, [x1, #0xb]
    // 0x8f3768: mov             x3, x1
    // 0x8f376c: ldur            x1, [fp, #-8]
    // 0x8f3770: r2 = Null
    //     0x8f3770: mov             x2, NULL
    // 0x8f3774: r0 = change()
    //     0x8f3774: bl              #0x8f3790  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin::change
    // 0x8f3778: r0 = Null
    //     0x8f3778: mov             x0, NULL
    // 0x8f377c: LeaveFrame
    //     0x8f377c: mov             SP, fp
    //     0x8f3780: ldp             fp, lr, [SP], #0x10
    // 0x8f3784: ret
    //     0x8f3784: ret             
    // 0x8f3788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f378c: b               #0x8f36bc
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9275c0, size: 0x64
    // 0x9275c0: EnterFrame
    //     0x9275c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9275c4: mov             fp, SP
    // 0x9275c8: AllocStack(0x8)
    //     0x9275c8: sub             SP, SP, #8
    // 0x9275cc: SetupParameters(DoaSearchController this /* r1 => r3, fp-0x8 */)
    //     0x9275cc: mov             x3, x1
    //     0x9275d0: stur            x1, [fp, #-8]
    // 0x9275d4: CheckStackOverflow
    //     0x9275d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9275d8: cmp             SP, x16
    //     0x9275dc: b.ls            #0x92761c
    // 0x9275e0: LoadField: r1 = r3->field_37
    //     0x9275e0: ldur            w1, [x3, #0x37]
    // 0x9275e4: DecompressPointer r1
    //     0x9275e4: add             x1, x1, HEAP, lsl #32
    // 0x9275e8: LoadField: r2 = r3->field_27
    //     0x9275e8: ldur            w2, [x3, #0x27]
    // 0x9275ec: DecompressPointer r2
    //     0x9275ec: add             x2, x2, HEAP, lsl #32
    // 0x9275f0: r0 = LoadClassIdInstr(r1)
    //     0x9275f0: ldur            x0, [x1, #-1]
    //     0x9275f4: ubfx            x0, x0, #0xc, #0x14
    // 0x9275f8: r0 = GDT[cid_x0 + -0xfdc]()
    //     0x9275f8: sub             lr, x0, #0xfdc
    //     0x9275fc: ldr             lr, [x21, lr, lsl #3]
    //     0x927600: blr             lr
    // 0x927604: ldur            x1, [fp, #-8]
    // 0x927608: r0 = onClose()
    //     0x927608: bl              #0x927624  ; [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::onClose
    // 0x92760c: r0 = Null
    //     0x92760c: mov             x0, NULL
    // 0x927610: LeaveFrame
    //     0x927610: mov             SP, fp
    //     0x927614: ldp             fp, lr, [SP], #0x10
    // 0x927618: ret
    //     0x927618: ret             
    // 0x92761c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92761c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927620: b               #0x9275e0
  }
  [closure] void onSearchSubmitted(dynamic, String, {bool saveHistory}) {
    // ** addr: 0xadc210, size: 0x94
    // 0xadc210: EnterFrame
    //     0xadc210: stp             fp, lr, [SP, #-0x10]!
    //     0xadc214: mov             fp, SP
    // 0xadc218: AllocStack(0x8)
    //     0xadc218: sub             SP, SP, #8
    // 0xadc21c: SetupParameters(DoaSearchController this /* r2 */, dynamic _ /* r3 */, {dynamic saveHistory = true /* r0 */})
    //     0xadc21c: ldur            w0, [x4, #0x13]
    //     0xadc220: sub             x1, x0, #4
    //     0xadc224: add             x2, fp, w1, sxtw #2
    //     0xadc228: ldr             x2, [x2, #0x18]
    //     0xadc22c: add             x3, fp, w1, sxtw #2
    //     0xadc230: ldr             x3, [x3, #0x10]
    //     0xadc234: ldur            w1, [x4, #0x1f]
    //     0xadc238: add             x1, x1, HEAP, lsl #32
    //     0xadc23c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bde8] "saveHistory"
    //     0xadc240: ldr             x16, [x16, #0xde8]
    //     0xadc244: cmp             w1, w16
    //     0xadc248: b.ne            #0xadc264
    //     0xadc24c: ldur            w1, [x4, #0x23]
    //     0xadc250: add             x1, x1, HEAP, lsl #32
    //     0xadc254: sub             w4, w0, w1
    //     0xadc258: add             x0, fp, w4, sxtw #2
    //     0xadc25c: ldr             x0, [x0, #8]
    //     0xadc260: b               #0xadc268
    //     0xadc264: add             x0, NULL, #0x20  ; true
    //     0xadc268: ldur            w1, [x2, #0x17]
    //     0xadc26c: add             x1, x1, HEAP, lsl #32
    // 0xadc270: CheckStackOverflow
    //     0xadc270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc274: cmp             SP, x16
    //     0xadc278: b.ls            #0xadc29c
    // 0xadc27c: str             x0, [SP]
    // 0xadc280: mov             x2, x3
    // 0xadc284: r4 = const [0, 0x3, 0x1, 0x2, saveHistory, 0x2, null]
    //     0xadc284: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bd10] List(7) [0, 0x3, 0x1, 0x2, "saveHistory", 0x2, Null]
    //     0xadc288: ldr             x4, [x4, #0xd10]
    // 0xadc28c: r0 = onSearchSubmitted()
    //     0xadc28c: bl              #0xe3f170  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onSearchSubmitted
    // 0xadc290: LeaveFrame
    //     0xadc290: mov             SP, fp
    //     0xadc294: ldp             fp, lr, [SP], #0x10
    // 0xadc298: ret
    //     0xadc298: ret             
    // 0xadc29c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc29c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc2a0: b               #0xadc27c
  }
  [closure] void onSearchCanceled(dynamic) {
    // ** addr: 0xadcbf8, size: 0x38
    // 0xadcbf8: EnterFrame
    //     0xadcbf8: stp             fp, lr, [SP, #-0x10]!
    //     0xadcbfc: mov             fp, SP
    // 0xadcc00: ldr             x0, [fp, #0x10]
    // 0xadcc04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadcc04: ldur            w1, [x0, #0x17]
    // 0xadcc08: DecompressPointer r1
    //     0xadcc08: add             x1, x1, HEAP, lsl #32
    // 0xadcc0c: CheckStackOverflow
    //     0xadcc0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadcc10: cmp             SP, x16
    //     0xadcc14: b.ls            #0xadcc28
    // 0xadcc18: r0 = onSearchCanceled()
    //     0xadcc18: bl              #0xadcc30  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onSearchCanceled
    // 0xadcc1c: LeaveFrame
    //     0xadcc1c: mov             SP, fp
    //     0xadcc20: ldp             fp, lr, [SP], #0x10
    // 0xadcc24: ret
    //     0xadcc24: ret             
    // 0xadcc28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadcc28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadcc2c: b               #0xadcc18
  }
  _ onSearchCanceled(/* No info */) {
    // ** addr: 0xadcc30, size: 0x68
    // 0xadcc30: EnterFrame
    //     0xadcc30: stp             fp, lr, [SP, #-0x10]!
    //     0xadcc34: mov             fp, SP
    // 0xadcc38: AllocStack(0x8)
    //     0xadcc38: sub             SP, SP, #8
    // 0xadcc3c: SetupParameters(DoaSearchController this /* r1 => r0, fp-0x8 */)
    //     0xadcc3c: mov             x0, x1
    //     0xadcc40: stur            x1, [fp, #-8]
    // 0xadcc44: CheckStackOverflow
    //     0xadcc44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadcc48: cmp             SP, x16
    //     0xadcc4c: b.ls            #0xadcc90
    // 0xadcc50: mov             x1, x0
    // 0xadcc54: r0 = onSearchCanceled()
    //     0xadcc54: bl              #0xadcc98  ; [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::onSearchCanceled
    // 0xadcc58: r0 = RxStatus()
    //     0xadcc58: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xadcc5c: mov             x1, x0
    // 0xadcc60: r0 = false
    //     0xadcc60: add             x0, NULL, #0x30  ; false
    // 0xadcc64: StoreField: r1->field_f = r0
    //     0xadcc64: stur            w0, [x1, #0xf]
    // 0xadcc68: StoreField: r1->field_7 = r0
    //     0xadcc68: stur            w0, [x1, #7]
    // 0xadcc6c: StoreField: r1->field_b = r0
    //     0xadcc6c: stur            w0, [x1, #0xb]
    // 0xadcc70: mov             x3, x1
    // 0xadcc74: ldur            x1, [fp, #-8]
    // 0xadcc78: r2 = Null
    //     0xadcc78: mov             x2, NULL
    // 0xadcc7c: r0 = change()
    //     0xadcc7c: bl              #0x8f3790  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin::change
    // 0xadcc80: r0 = Null
    //     0xadcc80: mov             x0, NULL
    // 0xadcc84: LeaveFrame
    //     0xadcc84: mov             SP, fp
    //     0xadcc88: ldp             fp, lr, [SP], #0x10
    // 0xadcc8c: ret
    //     0xadcc8c: ret             
    // 0xadcc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadcc90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadcc94: b               #0xadcc50
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef96c, size: 0xb0
    // 0xbef96c: EnterFrame
    //     0xbef96c: stp             fp, lr, [SP, #-0x10]!
    //     0xbef970: mov             fp, SP
    // 0xbef974: AllocStack(0x20)
    //     0xbef974: sub             SP, SP, #0x20
    // 0xbef978: SetupParameters(DoaSearchController this /* r1 => r1, fp-0x10 */)
    //     0xbef978: stur            NULL, [fp, #-8]
    //     0xbef97c: stur            x1, [fp, #-0x10]
    // 0xbef980: CheckStackOverflow
    //     0xbef980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef984: cmp             SP, x16
    //     0xbef988: b.ls            #0xbefa14
    // 0xbef98c: r1 = 1
    //     0xbef98c: movz            x1, #0x1
    // 0xbef990: r0 = AllocateContext()
    //     0xbef990: bl              #0xec126c  ; AllocateContextStub
    // 0xbef994: mov             x2, x0
    // 0xbef998: ldur            x1, [fp, #-0x10]
    // 0xbef99c: stur            x2, [fp, #-0x18]
    // 0xbef9a0: StoreField: r2->field_f = r1
    //     0xbef9a0: stur            w1, [x2, #0xf]
    // 0xbef9a4: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xbef9a4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xbef9a8: ldr             x0, [x0, #0xd38]
    //     0xbef9ac: bl              #0x661298  ; InitAsyncStub
    // 0xbef9b0: ldur            x2, [fp, #-0x18]
    // 0xbef9b4: r1 = Function '<anonymous closure>':.
    //     0xbef9b4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bd40] AnonymousClosure: (0xbefa1c), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onOfflineModeRequested (0xbef96c)
    //     0xbef9b8: ldr             x1, [x1, #0xd40]
    // 0xbef9bc: r0 = AllocateClosure()
    //     0xbef9bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbef9c0: ldur            x1, [fp, #-0x10]
    // 0xbef9c4: mov             x2, x0
    // 0xbef9c8: r0 = withAnalytic()
    //     0xbef9c8: bl              #0x8abac8  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin::withAnalytic
    // 0xbef9cc: ldur            x0, [fp, #-0x10]
    // 0xbef9d0: LoadField: r2 = r0->field_37
    //     0xbef9d0: ldur            w2, [x0, #0x37]
    // 0xbef9d4: DecompressPointer r2
    //     0xbef9d4: add             x2, x2, HEAP, lsl #32
    // 0xbef9d8: stur            x2, [fp, #-0x18]
    // 0xbef9dc: LoadField: r1 = r0->field_23
    //     0xbef9dc: ldur            w1, [x0, #0x23]
    // 0xbef9e0: DecompressPointer r1
    //     0xbef9e0: add             x1, x1, HEAP, lsl #32
    // 0xbef9e4: r0 = value()
    //     0xbef9e4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbef9e8: ldur            x1, [fp, #-0x18]
    // 0xbef9ec: r2 = LoadClassIdInstr(r1)
    //     0xbef9ec: ldur            x2, [x1, #-1]
    //     0xbef9f0: ubfx            x2, x2, #0xc, #0x14
    // 0xbef9f4: str             x0, [SP]
    // 0xbef9f8: mov             x0, x2
    // 0xbef9fc: r4 = const [0, 0x2, 0x1, 0x1, query, 0x1, null]
    //     0xbef9fc: add             x4, PP, #0x29, lsl #12  ; [pp+0x291d8] List(7) [0, 0x2, 0x1, 0x1, "query", 0x1, Null]
    //     0xbefa00: ldr             x4, [x4, #0x1d8]
    // 0xbefa04: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbefa04: sub             lr, x0, #1, lsl #12
    //     0xbefa08: ldr             lr, [x21, lr, lsl #3]
    //     0xbefa0c: blr             lr
    // 0xbefa10: r0 = ReturnAsync()
    //     0xbefa10: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbefa14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefa14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefa18: b               #0xbef98c
  }
  [closure] void <anonymous closure>(dynamic, AnalyticService) {
    // ** addr: 0xbefa1c, size: 0x58
    // 0xbefa1c: EnterFrame
    //     0xbefa1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbefa20: mov             fp, SP
    // 0xbefa24: ldr             x0, [fp, #0x18]
    // 0xbefa28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbefa28: ldur            w1, [x0, #0x17]
    // 0xbefa2c: DecompressPointer r1
    //     0xbefa2c: add             x1, x1, HEAP, lsl #32
    // 0xbefa30: CheckStackOverflow
    //     0xbefa30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbefa34: cmp             SP, x16
    //     0xbefa38: b.ls            #0xbefa6c
    // 0xbefa3c: LoadField: r0 = r1->field_f
    //     0xbefa3c: ldur            w0, [x1, #0xf]
    // 0xbefa40: DecompressPointer r0
    //     0xbefa40: add             x0, x0, HEAP, lsl #32
    // 0xbefa44: LoadField: r1 = r0->field_23
    //     0xbefa44: ldur            w1, [x0, #0x23]
    // 0xbefa48: DecompressPointer r1
    //     0xbefa48: add             x1, x1, HEAP, lsl #32
    // 0xbefa4c: r0 = value()
    //     0xbefa4c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbefa50: ldr             x1, [fp, #0x10]
    // 0xbefa54: mov             x2, x0
    // 0xbefa58: r0 = sendDoaSearchLog()
    //     0xbefa58: bl              #0xbef184  ; [package:nuonline/services/analytic_service.dart] AnalyticService::sendDoaSearchLog
    // 0xbefa5c: r0 = Null
    //     0xbefa5c: mov             x0, NULL
    // 0xbefa60: LeaveFrame
    //     0xbefa60: mov             SP, fp
    //     0xbefa64: ldp             fp, lr, [SP], #0x10
    // 0xbefa68: ret
    //     0xbefa68: ret             
    // 0xbefa6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefa6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefa70: b               #0xbefa3c
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf6934, size: 0xc4
    // 0xbf6934: EnterFrame
    //     0xbf6934: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6938: mov             fp, SP
    // 0xbf693c: AllocStack(0x28)
    //     0xbf693c: sub             SP, SP, #0x28
    // 0xbf6940: SetupParameters(DoaSearchController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xbf6940: stur            NULL, [fp, #-8]
    //     0xbf6944: stur            x1, [fp, #-0x10]
    //     0xbf6948: mov             x16, x2
    //     0xbf694c: mov             x2, x1
    //     0xbf6950: mov             x1, x16
    //     0xbf6954: stur            x1, [fp, #-0x18]
    // 0xbf6958: CheckStackOverflow
    //     0xbf6958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf695c: cmp             SP, x16
    //     0xbf6960: b.ls            #0xbf69f0
    // 0xbf6964: InitAsync() -> Future<void?>
    //     0xbf6964: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf6968: bl              #0x661298  ; InitAsyncStub
    // 0xbf696c: ldur            x2, [fp, #-0x18]
    // 0xbf6970: r0 = LoadClassIdInstr(r2)
    //     0xbf6970: ldur            x0, [x2, #-1]
    //     0xbf6974: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6978: mov             x1, x2
    // 0xbf697c: r0 = GDT[cid_x0 + 0xe879]()
    //     0xbf697c: movz            x17, #0xe879
    //     0xbf6980: add             lr, x0, x17
    //     0xbf6984: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6988: blr             lr
    // 0xbf698c: stur            x0, [fp, #-0x20]
    // 0xbf6990: r0 = RxStatus()
    //     0xbf6990: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf6994: mov             x1, x0
    // 0xbf6998: r0 = true
    //     0xbf6998: add             x0, NULL, #0x20  ; true
    // 0xbf699c: stur            x1, [fp, #-0x28]
    // 0xbf69a0: StoreField: r1->field_f = r0
    //     0xbf69a0: stur            w0, [x1, #0xf]
    // 0xbf69a4: r0 = false
    //     0xbf69a4: add             x0, NULL, #0x30  ; false
    // 0xbf69a8: StoreField: r1->field_7 = r0
    //     0xbf69a8: stur            w0, [x1, #7]
    // 0xbf69ac: StoreField: r1->field_b = r0
    //     0xbf69ac: stur            w0, [x1, #0xb]
    // 0xbf69b0: r0 = RxStatus()
    //     0xbf69b0: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf69b4: mov             x1, x0
    // 0xbf69b8: r0 = false
    //     0xbf69b8: add             x0, NULL, #0x30  ; false
    // 0xbf69bc: StoreField: r1->field_f = r0
    //     0xbf69bc: stur            w0, [x1, #0xf]
    // 0xbf69c0: StoreField: r1->field_7 = r0
    //     0xbf69c0: stur            w0, [x1, #7]
    // 0xbf69c4: StoreField: r1->field_b = r0
    //     0xbf69c4: stur            w0, [x1, #0xb]
    // 0xbf69c8: ldur            x0, [fp, #-0x20]
    // 0xbf69cc: tbnz            w0, #4, #0xbf69d8
    // 0xbf69d0: ldur            x3, [fp, #-0x28]
    // 0xbf69d4: b               #0xbf69dc
    // 0xbf69d8: mov             x3, x1
    // 0xbf69dc: ldur            x1, [fp, #-0x10]
    // 0xbf69e0: ldur            x2, [fp, #-0x18]
    // 0xbf69e4: r0 = change()
    //     0xbf69e4: bl              #0x8f3790  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin::change
    // 0xbf69e8: r0 = Null
    //     0xbf69e8: mov             x0, NULL
    // 0xbf69ec: r0 = ReturnAsyncNotFuture()
    //     0xbf69ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf69f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf69f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf69f4: b               #0xbf6964
  }
  _ onOfflineModeFailure(/* No info */) async {
    // ** addr: 0xc2a480, size: 0x58
    // 0xc2a480: EnterFrame
    //     0xc2a480: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a484: mov             fp, SP
    // 0xc2a488: AllocStack(0x18)
    //     0xc2a488: sub             SP, SP, #0x18
    // 0xc2a48c: SetupParameters(DoaSearchController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc2a48c: stur            NULL, [fp, #-8]
    //     0xc2a490: stur            x1, [fp, #-0x10]
    //     0xc2a494: stur            x2, [fp, #-0x18]
    // 0xc2a498: CheckStackOverflow
    //     0xc2a498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a49c: cmp             SP, x16
    //     0xc2a4a0: b.ls            #0xc2a4d0
    // 0xc2a4a4: InitAsync() -> Future<void?>
    //     0xc2a4a4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc2a4a8: bl              #0x661298  ; InitAsyncStub
    // 0xc2a4ac: r1 = Null
    //     0xc2a4ac: mov             x1, NULL
    // 0xc2a4b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc2a4b0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc2a4b4: r0 = RxStatus.error()
    //     0xc2a4b4: bl              #0x7e24f0  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] RxStatus::RxStatus.error
    // 0xc2a4b8: ldur            x1, [fp, #-0x10]
    // 0xc2a4bc: mov             x3, x0
    // 0xc2a4c0: r2 = Null
    //     0xc2a4c0: mov             x2, NULL
    // 0xc2a4c4: r0 = change()
    //     0xc2a4c4: bl              #0x8f3790  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin::change
    // 0xc2a4c8: r0 = Null
    //     0xc2a4c8: mov             x0, NULL
    // 0xc2a4cc: r0 = ReturnAsyncNotFuture()
    //     0xc2a4cc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc2a4d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a4d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a4d4: b               #0xc2a4a4
  }
  _ onSearchSubmitted(/* No info */) {
    // ** addr: 0xe3f170, size: 0xe0
    // 0xe3f170: EnterFrame
    //     0xe3f170: stp             fp, lr, [SP, #-0x10]!
    //     0xe3f174: mov             fp, SP
    // 0xe3f178: AllocStack(0x20)
    //     0xe3f178: sub             SP, SP, #0x20
    // 0xe3f17c: SetupParameters(DoaSearchController this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, {dynamic saveHistory = true /* r3, fp-0x8 */})
    //     0xe3f17c: mov             x0, x1
    //     0xe3f180: stur            x1, [fp, #-0x10]
    //     0xe3f184: stur            x2, [fp, #-0x18]
    //     0xe3f188: ldur            w1, [x4, #0x13]
    //     0xe3f18c: ldur            w3, [x4, #0x1f]
    //     0xe3f190: add             x3, x3, HEAP, lsl #32
    //     0xe3f194: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bde8] "saveHistory"
    //     0xe3f198: ldr             x16, [x16, #0xde8]
    //     0xe3f19c: cmp             w3, w16
    //     0xe3f1a0: b.ne            #0xe3f1c0
    //     0xe3f1a4: ldur            w3, [x4, #0x23]
    //     0xe3f1a8: add             x3, x3, HEAP, lsl #32
    //     0xe3f1ac: sub             w4, w1, w3
    //     0xe3f1b0: add             x1, fp, w4, sxtw #2
    //     0xe3f1b4: ldr             x1, [x1, #8]
    //     0xe3f1b8: mov             x3, x1
    //     0xe3f1bc: b               #0xe3f1c4
    //     0xe3f1c0: add             x3, NULL, #0x20  ; true
    //     0xe3f1c4: stur            x3, [fp, #-8]
    // 0xe3f1c8: CheckStackOverflow
    //     0xe3f1c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3f1cc: cmp             SP, x16
    //     0xe3f1d0: b.ls            #0xe3f248
    // 0xe3f1d4: LoadField: r1 = r0->field_23
    //     0xe3f1d4: ldur            w1, [x0, #0x23]
    // 0xe3f1d8: DecompressPointer r1
    //     0xe3f1d8: add             x1, x1, HEAP, lsl #32
    // 0xe3f1dc: r0 = value()
    //     0xe3f1dc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3f1e0: LoadField: r1 = r0->field_7
    //     0xe3f1e0: ldur            w1, [x0, #7]
    // 0xe3f1e4: cbz             w1, #0xe3f210
    // 0xe3f1e8: ldur            x1, [fp, #-0x10]
    // 0xe3f1ec: r0 = executeOfflineMode()
    //     0xe3f1ec: bl              #0xadc408  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin&AnalyticMixin&OfflineMixin::executeOfflineMode
    // 0xe3f1f0: ldur            x16, [fp, #-8]
    // 0xe3f1f4: str             x16, [SP]
    // 0xe3f1f8: ldur            x1, [fp, #-0x10]
    // 0xe3f1fc: ldur            x2, [fp, #-0x18]
    // 0xe3f200: r4 = const [0, 0x3, 0x1, 0x2, saveHistory, 0x2, null]
    //     0xe3f200: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bd10] List(7) [0, 0x3, 0x1, 0x2, "saveHistory", 0x2, Null]
    //     0xe3f204: ldr             x4, [x4, #0xd10]
    // 0xe3f208: r0 = onSearchSubmitted()
    //     0xe3f208: bl              #0xadc824  ; [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::onSearchSubmitted
    // 0xe3f20c: b               #0xe3f238
    // 0xe3f210: r0 = RxStatus()
    //     0xe3f210: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xe3f214: mov             x1, x0
    // 0xe3f218: r0 = false
    //     0xe3f218: add             x0, NULL, #0x30  ; false
    // 0xe3f21c: StoreField: r1->field_f = r0
    //     0xe3f21c: stur            w0, [x1, #0xf]
    // 0xe3f220: StoreField: r1->field_7 = r0
    //     0xe3f220: stur            w0, [x1, #7]
    // 0xe3f224: StoreField: r1->field_b = r0
    //     0xe3f224: stur            w0, [x1, #0xb]
    // 0xe3f228: mov             x3, x1
    // 0xe3f22c: ldur            x1, [fp, #-0x10]
    // 0xe3f230: r2 = Null
    //     0xe3f230: mov             x2, NULL
    // 0xe3f234: r0 = change()
    //     0xe3f234: bl              #0x8f3790  ; [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] _DoaSearchController&GetxController&SearchMixin&StateMixin::change
    // 0xe3f238: r0 = Null
    //     0xe3f238: mov             x0, NULL
    // 0xe3f23c: LeaveFrame
    //     0xe3f23c: mov             SP, fp
    //     0xe3f240: ldp             fp, lr, [SP], #0x10
    // 0xe3f244: ret
    //     0xe3f244: ret             
    // 0xe3f248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3f248: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3f24c: b               #0xe3f1d4
  }
}
