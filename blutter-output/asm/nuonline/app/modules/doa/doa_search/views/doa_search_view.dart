// lib: , url: package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart

// class id: 1050187, size: 0x8
class :: {
}

// class id: 5299, size: 0x14, field offset: 0x14
//   const constructor, 
class DoaSearchView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xada928, size: 0x168
    // 0xada928: EnterFrame
    //     0xada928: stp             fp, lr, [SP, #-0x10]!
    //     0xada92c: mov             fp, SP
    // 0xada930: AllocStack(0x58)
    //     0xada930: sub             SP, SP, #0x58
    // 0xada934: SetupParameters(DoaSearchView this /* r1 => r1, fp-0x8 */)
    //     0xada934: stur            x1, [fp, #-8]
    // 0xada938: CheckStackOverflow
    //     0xada938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada93c: cmp             SP, x16
    //     0xada940: b.ls            #0xadaa88
    // 0xada944: r1 = 1
    //     0xada944: movz            x1, #0x1
    // 0xada948: r0 = AllocateContext()
    //     0xada948: bl              #0xec126c  ; AllocateContextStub
    // 0xada94c: ldur            x1, [fp, #-8]
    // 0xada950: stur            x0, [fp, #-0x10]
    // 0xada954: StoreField: r0->field_f = r1
    //     0xada954: stur            w1, [x0, #0xf]
    // 0xada958: r0 = Obx()
    //     0xada958: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xada95c: ldur            x2, [fp, #-0x10]
    // 0xada960: r1 = Function '<anonymous closure>':.
    //     0xada960: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7c0] AnonymousClosure: (0xadcaa0), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xada964: ldr             x1, [x1, #0x7c0]
    // 0xada968: stur            x0, [fp, #-0x18]
    // 0xada96c: r0 = AllocateClosure()
    //     0xada96c: bl              #0xec1630  ; AllocateClosureStub
    // 0xada970: mov             x1, x0
    // 0xada974: ldur            x0, [fp, #-0x18]
    // 0xada978: StoreField: r0->field_b = r1
    //     0xada978: stur            w1, [x0, #0xb]
    // 0xada97c: r0 = AppBar()
    //     0xada97c: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xada980: stur            x0, [fp, #-0x20]
    // 0xada984: ldur            x16, [fp, #-0x18]
    // 0xada988: str             x16, [SP]
    // 0xada98c: mov             x1, x0
    // 0xada990: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xada990: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xada994: ldr             x4, [x4, #0x6e8]
    // 0xada998: r0 = AppBar()
    //     0xada998: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xada99c: r0 = PreferredSize()
    //     0xada99c: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xada9a0: mov             x2, x0
    // 0xada9a4: r0 = Instance_Size
    //     0xada9a4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xada9a8: ldr             x0, [x0, #0xc88]
    // 0xada9ac: stur            x2, [fp, #-0x18]
    // 0xada9b0: StoreField: r2->field_f = r0
    //     0xada9b0: stur            w0, [x2, #0xf]
    // 0xada9b4: ldur            x0, [fp, #-0x20]
    // 0xada9b8: StoreField: r2->field_b = r0
    //     0xada9b8: stur            w0, [x2, #0xb]
    // 0xada9bc: ldur            x1, [fp, #-8]
    // 0xada9c0: r0 = controller()
    //     0xada9c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xada9c4: ldur            x1, [fp, #-8]
    // 0xada9c8: stur            x0, [fp, #-0x20]
    // 0xada9cc: r0 = buildLoading()
    //     0xada9cc: bl              #0xadaaec  ; [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildLoading
    // 0xada9d0: ldur            x1, [fp, #-8]
    // 0xada9d4: stur            x0, [fp, #-8]
    // 0xada9d8: r0 = buildEmptyResult()
    //     0xada9d8: bl              #0xadaa90  ; [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildEmptyResult
    // 0xada9dc: ldur            x2, [fp, #-0x10]
    // 0xada9e0: r1 = Function '<anonymous closure>':.
    //     0xada9e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7c8] AnonymousClosure: (0xadabb4), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xada9e4: ldr             x1, [x1, #0x7c8]
    // 0xada9e8: stur            x0, [fp, #-0x28]
    // 0xada9ec: r0 = AllocateClosure()
    //     0xada9ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xada9f0: ldur            x2, [fp, #-0x10]
    // 0xada9f4: r1 = Function '<anonymous closure>':.
    //     0xada9f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7d0] AnonymousClosure: (0xadab70), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xada9f8: ldr             x1, [x1, #0x7d0]
    // 0xada9fc: stur            x0, [fp, #-0x10]
    // 0xadaa00: r0 = AllocateClosure()
    //     0xadaa00: bl              #0xec1630  ; AllocateClosureStub
    // 0xadaa04: r16 = <List<DoaSubCategory>>
    //     0xadaa04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xadaa08: ldr             x16, [x16, #0x7d8]
    // 0xadaa0c: ldur            lr, [fp, #-0x20]
    // 0xadaa10: stp             lr, x16, [SP, #0x20]
    // 0xadaa14: ldur            x16, [fp, #-0x10]
    // 0xadaa18: ldur            lr, [fp, #-8]
    // 0xadaa1c: stp             lr, x16, [SP, #0x10]
    // 0xadaa20: ldur            x16, [fp, #-0x28]
    // 0xadaa24: stp             x0, x16, [SP]
    // 0xadaa28: r4 = const [0x1, 0x5, 0x5, 0x2, onEmpty, 0x3, onError, 0x4, onLoading, 0x2, null]
    //     0xadaa28: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b3b0] List(11) [0x1, 0x5, 0x5, 0x2, "onEmpty", 0x3, "onError", 0x4, "onLoading", 0x2, Null]
    //     0xadaa2c: ldr             x4, [x4, #0x3b0]
    // 0xadaa30: r0 = StateExt.obx()
    //     0xadaa30: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xadaa34: stur            x0, [fp, #-8]
    // 0xadaa38: r0 = Scaffold()
    //     0xadaa38: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xadaa3c: ldur            x1, [fp, #-0x18]
    // 0xadaa40: StoreField: r0->field_13 = r1
    //     0xadaa40: stur            w1, [x0, #0x13]
    // 0xadaa44: ldur            x1, [fp, #-8]
    // 0xadaa48: ArrayStore: r0[0] = r1  ; List_4
    //     0xadaa48: stur            w1, [x0, #0x17]
    // 0xadaa4c: r1 = Instance_AlignmentDirectional
    //     0xadaa4c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xadaa50: ldr             x1, [x1, #0x758]
    // 0xadaa54: StoreField: r0->field_2b = r1
    //     0xadaa54: stur            w1, [x0, #0x2b]
    // 0xadaa58: r1 = true
    //     0xadaa58: add             x1, NULL, #0x20  ; true
    // 0xadaa5c: StoreField: r0->field_53 = r1
    //     0xadaa5c: stur            w1, [x0, #0x53]
    // 0xadaa60: r2 = Instance_DragStartBehavior
    //     0xadaa60: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadaa64: StoreField: r0->field_57 = r2
    //     0xadaa64: stur            w2, [x0, #0x57]
    // 0xadaa68: r2 = false
    //     0xadaa68: add             x2, NULL, #0x30  ; false
    // 0xadaa6c: StoreField: r0->field_b = r2
    //     0xadaa6c: stur            w2, [x0, #0xb]
    // 0xadaa70: StoreField: r0->field_f = r2
    //     0xadaa70: stur            w2, [x0, #0xf]
    // 0xadaa74: StoreField: r0->field_5f = r1
    //     0xadaa74: stur            w1, [x0, #0x5f]
    // 0xadaa78: StoreField: r0->field_63 = r1
    //     0xadaa78: stur            w1, [x0, #0x63]
    // 0xadaa7c: LeaveFrame
    //     0xadaa7c: mov             SP, fp
    //     0xadaa80: ldp             fp, lr, [SP], #0x10
    // 0xadaa84: ret
    //     0xadaa84: ret             
    // 0xadaa88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadaa88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadaa8c: b               #0xada944
  }
  _ buildEmptyResult(/* No info */) {
    // ** addr: 0xadaa90, size: 0x5c
    // 0xadaa90: EnterFrame
    //     0xadaa90: stp             fp, lr, [SP, #-0x10]!
    //     0xadaa94: mov             fp, SP
    // 0xadaa98: AllocStack(0x8)
    //     0xadaa98: sub             SP, SP, #8
    // 0xadaa9c: CheckStackOverflow
    //     0xadaa9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadaaa0: cmp             SP, x16
    //     0xadaaa4: b.ls            #0xadaae4
    // 0xadaaa8: r0 = NEmptyState()
    //     0xadaaa8: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xadaaac: mov             x1, x0
    // 0xadaab0: r2 = "Mohon periksa kembali kata yang anda cari"
    //     0xadaab0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be20] "Mohon periksa kembali kata yang anda cari"
    //     0xadaab4: ldr             x2, [x2, #0xe20]
    // 0xadaab8: r3 = "assets/images/illustration/no_search.svg"
    //     0xadaab8: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xadaabc: ldr             x3, [x3, #0x138]
    // 0xadaac0: r5 = "Wirid/Doa yang Dicari Tidak Ditemukan"
    //     0xadaac0: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f858] "Wirid/Doa yang Dicari Tidak Ditemukan"
    //     0xadaac4: ldr             x5, [x5, #0x858]
    // 0xadaac8: stur            x0, [fp, #-8]
    // 0xadaacc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xadaacc: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xadaad0: r0 = NEmptyState.svg()
    //     0xadaad0: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xadaad4: ldur            x0, [fp, #-8]
    // 0xadaad8: LeaveFrame
    //     0xadaad8: mov             SP, fp
    //     0xadaadc: ldp             fp, lr, [SP], #0x10
    // 0xadaae0: ret
    //     0xadaae0: ret             
    // 0xadaae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadaae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadaae8: b               #0xadaaa8
  }
  _ buildLoading(/* No info */) {
    // ** addr: 0xadaaec, size: 0x84
    // 0xadaaec: EnterFrame
    //     0xadaaec: stp             fp, lr, [SP, #-0x10]!
    //     0xadaaf0: mov             fp, SP
    // 0xadaaf4: AllocStack(0x20)
    //     0xadaaf4: sub             SP, SP, #0x20
    // 0xadaaf8: CheckStackOverflow
    //     0xadaaf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadaafc: cmp             SP, x16
    //     0xadab00: b.ls            #0xadab68
    // 0xadab04: r1 = Function '<anonymous closure>':.
    //     0xadab04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] AnonymousClosure: (0xb98358), in [package:nuonline/app/modules/tutorial/tutorial_bookmark/views/tutorial_bookmark_view.dart] TutorialBookmarkView::build (0xb593b4)
    //     0xadab08: ldr             x1, [x1, #0x860]
    // 0xadab0c: r2 = Null
    //     0xadab0c: mov             x2, NULL
    // 0xadab10: r0 = AllocateClosure()
    //     0xadab10: bl              #0xec1630  ; AllocateClosureStub
    // 0xadab14: r1 = Function '<anonymous closure>':.
    //     0xadab14: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f868] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xadab18: ldr             x1, [x1, #0x868]
    // 0xadab1c: r2 = Null
    //     0xadab1c: mov             x2, NULL
    // 0xadab20: stur            x0, [fp, #-8]
    // 0xadab24: r0 = AllocateClosure()
    //     0xadab24: bl              #0xec1630  ; AllocateClosureStub
    // 0xadab28: stur            x0, [fp, #-0x10]
    // 0xadab2c: r0 = ListView()
    //     0xadab2c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xadab30: stur            x0, [fp, #-0x18]
    // 0xadab34: r16 = Instance_EdgeInsets
    //     0xadab34: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xadab38: str             x16, [SP]
    // 0xadab3c: mov             x1, x0
    // 0xadab40: ldur            x2, [fp, #-8]
    // 0xadab44: ldur            x5, [fp, #-0x10]
    // 0xadab48: r3 = 8
    //     0xadab48: movz            x3, #0x8
    // 0xadab4c: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xadab4c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xadab50: ldr             x4, [x4, #0x700]
    // 0xadab54: r0 = ListView.separated()
    //     0xadab54: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xadab58: ldur            x0, [fp, #-0x18]
    // 0xadab5c: LeaveFrame
    //     0xadab5c: mov             SP, fp
    //     0xadab60: ldp             fp, lr, [SP], #0x10
    // 0xadab64: ret
    //     0xadab64: ret             
    // 0xadab68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadab68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadab6c: b               #0xadab04
  }
  [closure] Widget <anonymous closure>(dynamic, String?) {
    // ** addr: 0xadab70, size: 0x44
    // 0xadab70: EnterFrame
    //     0xadab70: stp             fp, lr, [SP, #-0x10]!
    //     0xadab74: mov             fp, SP
    // 0xadab78: ldr             x0, [fp, #0x18]
    // 0xadab7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadab7c: ldur            w1, [x0, #0x17]
    // 0xadab80: DecompressPointer r1
    //     0xadab80: add             x1, x1, HEAP, lsl #32
    // 0xadab84: CheckStackOverflow
    //     0xadab84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadab88: cmp             SP, x16
    //     0xadab8c: b.ls            #0xadabac
    // 0xadab90: LoadField: r0 = r1->field_f
    //     0xadab90: ldur            w0, [x1, #0xf]
    // 0xadab94: DecompressPointer r0
    //     0xadab94: add             x0, x0, HEAP, lsl #32
    // 0xadab98: mov             x1, x0
    // 0xadab9c: r0 = buildEmptyResult()
    //     0xadab9c: bl              #0xadaa90  ; [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildEmptyResult
    // 0xadaba0: LeaveFrame
    //     0xadaba0: mov             SP, fp
    //     0xadaba4: ldp             fp, lr, [SP], #0x10
    // 0xadaba8: ret
    //     0xadaba8: ret             
    // 0xadabac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadabac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadabb0: b               #0xadab90
  }
  [closure] Widget <anonymous closure>(dynamic, List<DoaSubCategory>?) {
    // ** addr: 0xadabb4, size: 0x14c
    // 0xadabb4: EnterFrame
    //     0xadabb4: stp             fp, lr, [SP, #-0x10]!
    //     0xadabb8: mov             fp, SP
    // 0xadabbc: AllocStack(0x30)
    //     0xadabbc: sub             SP, SP, #0x30
    // 0xadabc0: SetupParameters()
    //     0xadabc0: ldr             x0, [fp, #0x18]
    //     0xadabc4: ldur            w1, [x0, #0x17]
    //     0xadabc8: add             x1, x1, HEAP, lsl #32
    //     0xadabcc: stur            x1, [fp, #-8]
    // 0xadabd0: CheckStackOverflow
    //     0xadabd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadabd4: cmp             SP, x16
    //     0xadabd8: b.ls            #0xadacf8
    // 0xadabdc: r1 = 1
    //     0xadabdc: movz            x1, #0x1
    // 0xadabe0: r0 = AllocateContext()
    //     0xadabe0: bl              #0xec126c  ; AllocateContextStub
    // 0xadabe4: mov             x1, x0
    // 0xadabe8: ldur            x0, [fp, #-8]
    // 0xadabec: stur            x1, [fp, #-0x10]
    // 0xadabf0: StoreField: r1->field_b = r0
    //     0xadabf0: stur            w0, [x1, #0xb]
    // 0xadabf4: ldr             x0, [fp, #0x10]
    // 0xadabf8: StoreField: r1->field_f = r0
    //     0xadabf8: stur            w0, [x1, #0xf]
    // 0xadabfc: cmp             w0, NULL
    // 0xadac00: b.ne            #0xadac34
    // 0xadac04: r0 = Obx()
    //     0xadac04: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xadac08: ldur            x2, [fp, #-0x10]
    // 0xadac0c: r1 = Function '<anonymous closure>':.
    //     0xadac0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7e0] AnonymousClosure: (0xadb1e0), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xadac10: ldr             x1, [x1, #0x7e0]
    // 0xadac14: stur            x0, [fp, #-8]
    // 0xadac18: r0 = AllocateClosure()
    //     0xadac18: bl              #0xec1630  ; AllocateClosureStub
    // 0xadac1c: mov             x1, x0
    // 0xadac20: ldur            x0, [fp, #-8]
    // 0xadac24: StoreField: r0->field_b = r1
    //     0xadac24: stur            w1, [x0, #0xb]
    // 0xadac28: LeaveFrame
    //     0xadac28: mov             SP, fp
    //     0xadac2c: ldp             fp, lr, [SP], #0x10
    // 0xadac30: ret
    //     0xadac30: ret             
    // 0xadac34: r1 = LoadClassIdInstr(r0)
    //     0xadac34: ldur            x1, [x0, #-1]
    //     0xadac38: ubfx            x1, x1, #0xc, #0x14
    // 0xadac3c: str             x0, [SP]
    // 0xadac40: mov             x0, x1
    // 0xadac44: r0 = GDT[cid_x0 + 0xc834]()
    //     0xadac44: movz            x17, #0xc834
    //     0xadac48: add             lr, x0, x17
    //     0xadac4c: ldr             lr, [x21, lr, lsl #3]
    //     0xadac50: blr             lr
    // 0xadac54: r3 = LoadInt32Instr(r0)
    //     0xadac54: sbfx            x3, x0, #1, #0x1f
    //     0xadac58: tbz             w0, #0, #0xadac60
    //     0xadac5c: ldur            x3, [x0, #7]
    // 0xadac60: stur            x3, [fp, #-0x18]
    // 0xadac64: r1 = Function '<anonymous closure>':.
    //     0xadac64: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7e8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xadac68: ldr             x1, [x1, #0x7e8]
    // 0xadac6c: r2 = Null
    //     0xadac6c: mov             x2, NULL
    // 0xadac70: r0 = AllocateClosure()
    //     0xadac70: bl              #0xec1630  ; AllocateClosureStub
    // 0xadac74: ldur            x2, [fp, #-0x10]
    // 0xadac78: r1 = Function '<anonymous closure>':.
    //     0xadac78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7f0] AnonymousClosure: (0xadad00), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xadac7c: ldr             x1, [x1, #0x7f0]
    // 0xadac80: stur            x0, [fp, #-8]
    // 0xadac84: r0 = AllocateClosure()
    //     0xadac84: bl              #0xec1630  ; AllocateClosureStub
    // 0xadac88: stur            x0, [fp, #-0x10]
    // 0xadac8c: r0 = ListView()
    //     0xadac8c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xadac90: stur            x0, [fp, #-0x20]
    // 0xadac94: r16 = true
    //     0xadac94: add             x16, NULL, #0x20  ; true
    // 0xadac98: r30 = Instance_EdgeInsets
    //     0xadac98: add             lr, PP, #0x29, lsl #12  ; [pp+0x290f8] Obj!EdgeInsets@e125b1
    //     0xadac9c: ldr             lr, [lr, #0xf8]
    // 0xadaca0: stp             lr, x16, [SP]
    // 0xadaca4: mov             x1, x0
    // 0xadaca8: ldur            x2, [fp, #-0x10]
    // 0xadacac: ldur            x3, [fp, #-0x18]
    // 0xadacb0: ldur            x5, [fp, #-8]
    // 0xadacb4: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xadacb4: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xadacb8: ldr             x4, [x4, #0x100]
    // 0xadacbc: r0 = ListView.separated()
    //     0xadacbc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xadacc0: r0 = ListTileTheme()
    //     0xadacc0: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xadacc4: r1 = Instance_EdgeInsets
    //     0xadacc4: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xadacc8: StoreField: r0->field_2b = r1
    //     0xadacc8: stur            w1, [x0, #0x2b]
    // 0xadaccc: r1 = 16.000000
    //     0xadaccc: add             x1, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xadacd0: ldr             x1, [x1, #0x80]
    // 0xadacd4: StoreField: r0->field_37 = r1
    //     0xadacd4: stur            w1, [x0, #0x37]
    // 0xadacd8: r1 = 24.000000
    //     0xadacd8: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xadacdc: ldr             x1, [x1, #0x368]
    // 0xadace0: StoreField: r0->field_3f = r1
    //     0xadace0: stur            w1, [x0, #0x3f]
    // 0xadace4: ldur            x1, [fp, #-0x20]
    // 0xadace8: StoreField: r0->field_b = r1
    //     0xadace8: stur            w1, [x0, #0xb]
    // 0xadacec: LeaveFrame
    //     0xadacec: mov             SP, fp
    //     0xadacf0: ldp             fp, lr, [SP], #0x10
    // 0xadacf4: ret
    //     0xadacf4: ret             
    // 0xadacf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadacf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadacfc: b               #0xadabdc
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadad00, size: 0x3a8
    // 0xadad00: EnterFrame
    //     0xadad00: stp             fp, lr, [SP, #-0x10]!
    //     0xadad04: mov             fp, SP
    // 0xadad08: AllocStack(0x58)
    //     0xadad08: sub             SP, SP, #0x58
    // 0xadad0c: SetupParameters()
    //     0xadad0c: ldr             x0, [fp, #0x20]
    //     0xadad10: ldur            w1, [x0, #0x17]
    //     0xadad14: add             x1, x1, HEAP, lsl #32
    //     0xadad18: stur            x1, [fp, #-8]
    // 0xadad1c: CheckStackOverflow
    //     0xadad1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadad20: cmp             SP, x16
    //     0xadad24: b.ls            #0xadb098
    // 0xadad28: r1 = 1
    //     0xadad28: movz            x1, #0x1
    // 0xadad2c: r0 = AllocateContext()
    //     0xadad2c: bl              #0xec126c  ; AllocateContextStub
    // 0xadad30: mov             x2, x0
    // 0xadad34: ldur            x1, [fp, #-8]
    // 0xadad38: stur            x2, [fp, #-0x10]
    // 0xadad3c: StoreField: r2->field_b = r1
    //     0xadad3c: stur            w1, [x2, #0xb]
    // 0xadad40: ldr             x3, [fp, #0x10]
    // 0xadad44: StoreField: r2->field_f = r3
    //     0xadad44: stur            w3, [x2, #0xf]
    // 0xadad48: LoadField: r0 = r1->field_f
    //     0xadad48: ldur            w0, [x1, #0xf]
    // 0xadad4c: DecompressPointer r0
    //     0xadad4c: add             x0, x0, HEAP, lsl #32
    // 0xadad50: r4 = LoadClassIdInstr(r0)
    //     0xadad50: ldur            x4, [x0, #-1]
    //     0xadad54: ubfx            x4, x4, #0xc, #0x14
    // 0xadad58: stp             x3, x0, [SP]
    // 0xadad5c: mov             x0, x4
    // 0xadad60: r0 = GDT[cid_x0 + 0x13037]()
    //     0xadad60: movz            x17, #0x3037
    //     0xadad64: movk            x17, #0x1, lsl #16
    //     0xadad68: add             lr, x0, x17
    //     0xadad6c: ldr             lr, [x21, lr, lsl #3]
    //     0xadad70: blr             lr
    // 0xadad74: LoadField: r1 = r0->field_f
    //     0xadad74: ldur            w1, [x0, #0xf]
    // 0xadad78: DecompressPointer r1
    //     0xadad78: add             x1, x1, HEAP, lsl #32
    // 0xadad7c: stur            x1, [fp, #-0x18]
    // 0xadad80: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadad80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadad84: ldr             x0, [x0, #0x2670]
    //     0xadad88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadad8c: cmp             w0, w16
    //     0xadad90: b.ne            #0xadad9c
    //     0xadad94: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadad98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadad9c: r0 = GetNavigation.textTheme()
    //     0xadad9c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadada0: LoadField: r2 = r0->field_23
    //     0xadada0: ldur            w2, [x0, #0x23]
    // 0xadada4: DecompressPointer r2
    //     0xadada4: add             x2, x2, HEAP, lsl #32
    // 0xadada8: stur            x2, [fp, #-0x28]
    // 0xadadac: cmp             w2, NULL
    // 0xadadb0: b.eq            #0xadb0a0
    // 0xadadb4: ldur            x0, [fp, #-8]
    // 0xadadb8: LoadField: r3 = r0->field_b
    //     0xadadb8: ldur            w3, [x0, #0xb]
    // 0xadadbc: DecompressPointer r3
    //     0xadadbc: add             x3, x3, HEAP, lsl #32
    // 0xadadc0: stur            x3, [fp, #-0x20]
    // 0xadadc4: LoadField: r1 = r3->field_f
    //     0xadadc4: ldur            w1, [x3, #0xf]
    // 0xadadc8: DecompressPointer r1
    //     0xadadc8: add             x1, x1, HEAP, lsl #32
    // 0xadadcc: r0 = controller()
    //     0xadadcc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadadd0: LoadField: r1 = r0->field_23
    //     0xadadd0: ldur            w1, [x0, #0x23]
    // 0xadadd4: DecompressPointer r1
    //     0xadadd4: add             x1, x1, HEAP, lsl #32
    // 0xadadd8: r0 = value()
    //     0xadadd8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xadaddc: r1 = LoadClassIdInstr(r0)
    //     0xadaddc: ldur            x1, [x0, #-1]
    //     0xadade0: ubfx            x1, x1, #0xc, #0x14
    // 0xadade4: mov             x16, x0
    // 0xadade8: mov             x0, x1
    // 0xadadec: mov             x1, x16
    // 0xadadf0: r2 = " "
    //     0xadadf0: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xadadf4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xadadf4: sub             lr, x0, #1, lsl #12
    //     0xadadf8: ldr             lr, [x21, lr, lsl #3]
    //     0xadadfc: blr             lr
    // 0xadae00: r1 = _ConstMap len:10
    //     0xadae00: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xadae04: ldr             x1, [x1, #0xc08]
    // 0xadae08: r2 = 600
    //     0xadae08: movz            x2, #0x258
    // 0xadae0c: stur            x0, [fp, #-0x30]
    // 0xadae10: r0 = []()
    //     0xadae10: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xadae14: r16 = <Color?>
    //     0xadae14: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xadae18: ldr             x16, [x16, #0x98]
    // 0xadae1c: stp             x0, x16, [SP, #8]
    // 0xadae20: r16 = Instance_MaterialColor
    //     0xadae20: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xadae24: ldr             x16, [x16, #0xcf0]
    // 0xadae28: str             x16, [SP]
    // 0xadae2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadae2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadae30: r0 = mode()
    //     0xadae30: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xadae34: stur            x0, [fp, #-0x38]
    // 0xadae38: r0 = TextStyle()
    //     0xadae38: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xadae3c: mov             x1, x0
    // 0xadae40: r0 = true
    //     0xadae40: add             x0, NULL, #0x20  ; true
    // 0xadae44: stur            x1, [fp, #-0x40]
    // 0xadae48: StoreField: r1->field_7 = r0
    //     0xadae48: stur            w0, [x1, #7]
    // 0xadae4c: ldur            x2, [fp, #-0x38]
    // 0xadae50: StoreField: r1->field_b = r2
    //     0xadae50: stur            w2, [x1, #0xb]
    // 0xadae54: r0 = SubstringHighlight()
    //     0xadae54: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xadae58: mov             x2, x0
    // 0xadae5c: r1 = false
    //     0xadae5c: add             x1, NULL, #0x30  ; false
    // 0xadae60: stur            x2, [fp, #-0x38]
    // 0xadae64: StoreField: r2->field_b = r1
    //     0xadae64: stur            w1, [x2, #0xb]
    // 0xadae68: r3 = Instance_TextOverflow
    //     0xadae68: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xadae6c: ldr             x3, [x3, #0xc60]
    // 0xadae70: StoreField: r2->field_f = r3
    //     0xadae70: stur            w3, [x2, #0xf]
    // 0xadae74: ldur            x0, [fp, #-0x30]
    // 0xadae78: StoreField: r2->field_1b = r0
    //     0xadae78: stur            w0, [x2, #0x1b]
    // 0xadae7c: ldur            x0, [fp, #-0x18]
    // 0xadae80: StoreField: r2->field_1f = r0
    //     0xadae80: stur            w0, [x2, #0x1f]
    // 0xadae84: r4 = Instance_TextAlign
    //     0xadae84: ldr             x4, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xadae88: StoreField: r2->field_23 = r4
    //     0xadae88: stur            w4, [x2, #0x23]
    // 0xadae8c: ldur            x0, [fp, #-0x28]
    // 0xadae90: StoreField: r2->field_27 = r0
    //     0xadae90: stur            w0, [x2, #0x27]
    // 0xadae94: ldur            x0, [fp, #-0x40]
    // 0xadae98: StoreField: r2->field_2b = r0
    //     0xadae98: stur            w0, [x2, #0x2b]
    // 0xadae9c: StoreField: r2->field_2f = r1
    //     0xadae9c: stur            w1, [x2, #0x2f]
    // 0xadaea0: ldur            x0, [fp, #-8]
    // 0xadaea4: LoadField: r5 = r0->field_f
    //     0xadaea4: ldur            w5, [x0, #0xf]
    // 0xadaea8: DecompressPointer r5
    //     0xadaea8: add             x5, x5, HEAP, lsl #32
    // 0xadaeac: r0 = LoadClassIdInstr(r5)
    //     0xadaeac: ldur            x0, [x5, #-1]
    //     0xadaeb0: ubfx            x0, x0, #0xc, #0x14
    // 0xadaeb4: ldr             x16, [fp, #0x10]
    // 0xadaeb8: stp             x16, x5, [SP]
    // 0xadaebc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xadaebc: movz            x17, #0x3037
    //     0xadaec0: movk            x17, #0x1, lsl #16
    //     0xadaec4: add             lr, x0, x17
    //     0xadaec8: ldr             lr, [x21, lr, lsl #3]
    //     0xadaecc: blr             lr
    // 0xadaed0: LoadField: r1 = r0->field_23
    //     0xadaed0: ldur            w1, [x0, #0x23]
    // 0xadaed4: DecompressPointer r1
    //     0xadaed4: add             x1, x1, HEAP, lsl #32
    // 0xadaed8: stur            x1, [fp, #-8]
    // 0xadaedc: r0 = GetNavigation.textTheme()
    //     0xadaedc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadaee0: LoadField: r1 = r0->field_2f
    //     0xadaee0: ldur            w1, [x0, #0x2f]
    // 0xadaee4: DecompressPointer r1
    //     0xadaee4: add             x1, x1, HEAP, lsl #32
    // 0xadaee8: stur            x1, [fp, #-0x18]
    // 0xadaeec: cmp             w1, NULL
    // 0xadaef0: b.eq            #0xadb0a4
    // 0xadaef4: r0 = GetNavigation.textTheme()
    //     0xadaef4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadaef8: LoadField: r1 = r0->field_33
    //     0xadaef8: ldur            w1, [x0, #0x33]
    // 0xadaefc: DecompressPointer r1
    //     0xadaefc: add             x1, x1, HEAP, lsl #32
    // 0xadaf00: cmp             w1, NULL
    // 0xadaf04: b.ne            #0xadaf10
    // 0xadaf08: r1 = Null
    //     0xadaf08: mov             x1, NULL
    // 0xadaf0c: b               #0xadaf1c
    // 0xadaf10: LoadField: r0 = r1->field_b
    //     0xadaf10: ldur            w0, [x1, #0xb]
    // 0xadaf14: DecompressPointer r0
    //     0xadaf14: add             x0, x0, HEAP, lsl #32
    // 0xadaf18: mov             x1, x0
    // 0xadaf1c: ldur            x3, [fp, #-0x20]
    // 0xadaf20: ldur            x2, [fp, #-0x38]
    // 0xadaf24: ldur            x0, [fp, #-8]
    // 0xadaf28: str             x1, [SP]
    // 0xadaf2c: ldur            x1, [fp, #-0x18]
    // 0xadaf30: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xadaf30: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xadaf34: ldr             x4, [x4, #0x228]
    // 0xadaf38: r0 = copyWith()
    //     0xadaf38: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadaf3c: mov             x2, x0
    // 0xadaf40: ldur            x0, [fp, #-0x20]
    // 0xadaf44: stur            x2, [fp, #-0x18]
    // 0xadaf48: LoadField: r1 = r0->field_f
    //     0xadaf48: ldur            w1, [x0, #0xf]
    // 0xadaf4c: DecompressPointer r1
    //     0xadaf4c: add             x1, x1, HEAP, lsl #32
    // 0xadaf50: r0 = controller()
    //     0xadaf50: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadaf54: LoadField: r1 = r0->field_23
    //     0xadaf54: ldur            w1, [x0, #0x23]
    // 0xadaf58: DecompressPointer r1
    //     0xadaf58: add             x1, x1, HEAP, lsl #32
    // 0xadaf5c: r0 = value()
    //     0xadaf5c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xadaf60: r1 = LoadClassIdInstr(r0)
    //     0xadaf60: ldur            x1, [x0, #-1]
    //     0xadaf64: ubfx            x1, x1, #0xc, #0x14
    // 0xadaf68: mov             x16, x0
    // 0xadaf6c: mov             x0, x1
    // 0xadaf70: mov             x1, x16
    // 0xadaf74: r2 = " "
    //     0xadaf74: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xadaf78: r0 = GDT[cid_x0 + -0x1000]()
    //     0xadaf78: sub             lr, x0, #1, lsl #12
    //     0xadaf7c: ldr             lr, [x21, lr, lsl #3]
    //     0xadaf80: blr             lr
    // 0xadaf84: r1 = _ConstMap len:10
    //     0xadaf84: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xadaf88: ldr             x1, [x1, #0xc08]
    // 0xadaf8c: r2 = 600
    //     0xadaf8c: movz            x2, #0x258
    // 0xadaf90: stur            x0, [fp, #-0x20]
    // 0xadaf94: r0 = []()
    //     0xadaf94: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xadaf98: r16 = <Color?>
    //     0xadaf98: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xadaf9c: ldr             x16, [x16, #0x98]
    // 0xadafa0: stp             x0, x16, [SP, #8]
    // 0xadafa4: r16 = Instance_MaterialColor
    //     0xadafa4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xadafa8: ldr             x16, [x16, #0xcf0]
    // 0xadafac: str             x16, [SP]
    // 0xadafb0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadafb0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadafb4: r0 = mode()
    //     0xadafb4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xadafb8: stur            x0, [fp, #-0x28]
    // 0xadafbc: r0 = TextStyle()
    //     0xadafbc: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xadafc0: mov             x1, x0
    // 0xadafc4: r0 = true
    //     0xadafc4: add             x0, NULL, #0x20  ; true
    // 0xadafc8: stur            x1, [fp, #-0x30]
    // 0xadafcc: StoreField: r1->field_7 = r0
    //     0xadafcc: stur            w0, [x1, #7]
    // 0xadafd0: ldur            x2, [fp, #-0x28]
    // 0xadafd4: StoreField: r1->field_b = r2
    //     0xadafd4: stur            w2, [x1, #0xb]
    // 0xadafd8: r0 = SubstringHighlight()
    //     0xadafd8: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xadafdc: mov             x1, x0
    // 0xadafe0: r0 = false
    //     0xadafe0: add             x0, NULL, #0x30  ; false
    // 0xadafe4: stur            x1, [fp, #-0x28]
    // 0xadafe8: StoreField: r1->field_b = r0
    //     0xadafe8: stur            w0, [x1, #0xb]
    // 0xadafec: r2 = Instance_TextOverflow
    //     0xadafec: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xadaff0: ldr             x2, [x2, #0xc60]
    // 0xadaff4: StoreField: r1->field_f = r2
    //     0xadaff4: stur            w2, [x1, #0xf]
    // 0xadaff8: ldur            x2, [fp, #-0x20]
    // 0xadaffc: StoreField: r1->field_1b = r2
    //     0xadaffc: stur            w2, [x1, #0x1b]
    // 0xadb000: ldur            x2, [fp, #-8]
    // 0xadb004: StoreField: r1->field_1f = r2
    //     0xadb004: stur            w2, [x1, #0x1f]
    // 0xadb008: r2 = Instance_TextAlign
    //     0xadb008: ldr             x2, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xadb00c: StoreField: r1->field_23 = r2
    //     0xadb00c: stur            w2, [x1, #0x23]
    // 0xadb010: ldur            x2, [fp, #-0x18]
    // 0xadb014: StoreField: r1->field_27 = r2
    //     0xadb014: stur            w2, [x1, #0x27]
    // 0xadb018: ldur            x2, [fp, #-0x30]
    // 0xadb01c: StoreField: r1->field_2b = r2
    //     0xadb01c: stur            w2, [x1, #0x2b]
    // 0xadb020: StoreField: r1->field_2f = r0
    //     0xadb020: stur            w0, [x1, #0x2f]
    // 0xadb024: r0 = ListTile()
    //     0xadb024: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xadb028: mov             x3, x0
    // 0xadb02c: ldur            x0, [fp, #-0x38]
    // 0xadb030: stur            x3, [fp, #-8]
    // 0xadb034: StoreField: r3->field_f = r0
    //     0xadb034: stur            w0, [x3, #0xf]
    // 0xadb038: ldur            x0, [fp, #-0x28]
    // 0xadb03c: StoreField: r3->field_13 = r0
    //     0xadb03c: stur            w0, [x3, #0x13]
    // 0xadb040: r0 = Instance_Icon
    //     0xadb040: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cfd8] Obj!Icon@e23fb1
    //     0xadb044: ldr             x0, [x0, #0xfd8]
    // 0xadb048: ArrayStore: r3[0] = r0  ; List_4
    //     0xadb048: stur            w0, [x3, #0x17]
    // 0xadb04c: r0 = false
    //     0xadb04c: add             x0, NULL, #0x30  ; false
    // 0xadb050: StoreField: r3->field_1b = r0
    //     0xadb050: stur            w0, [x3, #0x1b]
    // 0xadb054: r4 = true
    //     0xadb054: add             x4, NULL, #0x20  ; true
    // 0xadb058: StoreField: r3->field_4b = r4
    //     0xadb058: stur            w4, [x3, #0x4b]
    // 0xadb05c: ldur            x2, [fp, #-0x10]
    // 0xadb060: r1 = Function '<anonymous closure>':.
    //     0xadb060: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7f8] AnonymousClosure: (0xadb0a8), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::build (0xada928)
    //     0xadb064: ldr             x1, [x1, #0x7f8]
    // 0xadb068: r0 = AllocateClosure()
    //     0xadb068: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb06c: mov             x1, x0
    // 0xadb070: ldur            x0, [fp, #-8]
    // 0xadb074: StoreField: r0->field_4f = r1
    //     0xadb074: stur            w1, [x0, #0x4f]
    // 0xadb078: r1 = false
    //     0xadb078: add             x1, NULL, #0x30  ; false
    // 0xadb07c: StoreField: r0->field_5f = r1
    //     0xadb07c: stur            w1, [x0, #0x5f]
    // 0xadb080: StoreField: r0->field_73 = r1
    //     0xadb080: stur            w1, [x0, #0x73]
    // 0xadb084: r1 = true
    //     0xadb084: add             x1, NULL, #0x20  ; true
    // 0xadb088: StoreField: r0->field_97 = r1
    //     0xadb088: stur            w1, [x0, #0x97]
    // 0xadb08c: LeaveFrame
    //     0xadb08c: mov             SP, fp
    //     0xadb090: ldp             fp, lr, [SP], #0x10
    // 0xadb094: ret
    //     0xadb094: ret             
    // 0xadb098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb098: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb09c: b               #0xadad28
    // 0xadb0a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb0a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb0a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb0a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadb0a8, size: 0x138
    // 0xadb0a8: EnterFrame
    //     0xadb0a8: stp             fp, lr, [SP, #-0x10]!
    //     0xadb0ac: mov             fp, SP
    // 0xadb0b0: AllocStack(0x28)
    //     0xadb0b0: sub             SP, SP, #0x28
    // 0xadb0b4: SetupParameters()
    //     0xadb0b4: ldr             x0, [fp, #0x10]
    //     0xadb0b8: ldur            w1, [x0, #0x17]
    //     0xadb0bc: add             x1, x1, HEAP, lsl #32
    //     0xadb0c0: stur            x1, [fp, #-8]
    // 0xadb0c4: CheckStackOverflow
    //     0xadb0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb0c8: cmp             SP, x16
    //     0xadb0cc: b.ls            #0xadb1d8
    // 0xadb0d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadb0d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadb0d4: ldr             x0, [x0, #0x2670]
    //     0xadb0d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadb0dc: cmp             w0, w16
    //     0xadb0e0: b.ne            #0xadb0ec
    //     0xadb0e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadb0e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadb0ec: r1 = Null
    //     0xadb0ec: mov             x1, NULL
    // 0xadb0f0: r2 = 8
    //     0xadb0f0: movz            x2, #0x8
    // 0xadb0f4: r0 = AllocateArray()
    //     0xadb0f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadb0f8: mov             x1, x0
    // 0xadb0fc: stur            x1, [fp, #-0x10]
    // 0xadb100: r16 = "id"
    //     0xadb100: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xadb104: ldr             x16, [x16, #0x740]
    // 0xadb108: StoreField: r1->field_f = r16
    //     0xadb108: stur            w16, [x1, #0xf]
    // 0xadb10c: ldur            x0, [fp, #-8]
    // 0xadb110: LoadField: r2 = r0->field_b
    //     0xadb110: ldur            w2, [x0, #0xb]
    // 0xadb114: DecompressPointer r2
    //     0xadb114: add             x2, x2, HEAP, lsl #32
    // 0xadb118: LoadField: r3 = r2->field_f
    //     0xadb118: ldur            w3, [x2, #0xf]
    // 0xadb11c: DecompressPointer r3
    //     0xadb11c: add             x3, x3, HEAP, lsl #32
    // 0xadb120: LoadField: r2 = r0->field_f
    //     0xadb120: ldur            w2, [x0, #0xf]
    // 0xadb124: DecompressPointer r2
    //     0xadb124: add             x2, x2, HEAP, lsl #32
    // 0xadb128: r0 = LoadClassIdInstr(r3)
    //     0xadb128: ldur            x0, [x3, #-1]
    //     0xadb12c: ubfx            x0, x0, #0xc, #0x14
    // 0xadb130: stp             x2, x3, [SP]
    // 0xadb134: r0 = GDT[cid_x0 + 0x13037]()
    //     0xadb134: movz            x17, #0x3037
    //     0xadb138: movk            x17, #0x1, lsl #16
    //     0xadb13c: add             lr, x0, x17
    //     0xadb140: ldr             lr, [x21, lr, lsl #3]
    //     0xadb144: blr             lr
    // 0xadb148: LoadField: r2 = r0->field_7
    //     0xadb148: ldur            x2, [x0, #7]
    // 0xadb14c: r0 = BoxInt64Instr(r2)
    //     0xadb14c: sbfiz           x0, x2, #1, #0x1f
    //     0xadb150: cmp             x2, x0, asr #1
    //     0xadb154: b.eq            #0xadb160
    //     0xadb158: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadb15c: stur            x2, [x0, #7]
    // 0xadb160: ldur            x1, [fp, #-0x10]
    // 0xadb164: ArrayStore: r1[1] = r0  ; List_4
    //     0xadb164: add             x25, x1, #0x13
    //     0xadb168: str             w0, [x25]
    //     0xadb16c: tbz             w0, #0, #0xadb188
    //     0xadb170: ldurb           w16, [x1, #-1]
    //     0xadb174: ldurb           w17, [x0, #-1]
    //     0xadb178: and             x16, x17, x16, lsr #2
    //     0xadb17c: tst             x16, HEAP, lsr #32
    //     0xadb180: b.eq            #0xadb188
    //     0xadb184: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadb188: ldur            x0, [fp, #-0x10]
    // 0xadb18c: r16 = "showBookmark"
    //     0xadb18c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f800] "showBookmark"
    //     0xadb190: ldr             x16, [x16, #0x800]
    // 0xadb194: ArrayStore: r0[0] = r16  ; List_4
    //     0xadb194: stur            w16, [x0, #0x17]
    // 0xadb198: r16 = true
    //     0xadb198: add             x16, NULL, #0x20  ; true
    // 0xadb19c: StoreField: r0->field_1b = r16
    //     0xadb19c: stur            w16, [x0, #0x1b]
    // 0xadb1a0: r16 = <String, Object>
    //     0xadb1a0: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xadb1a4: ldr             x16, [x16, #0x790]
    // 0xadb1a8: stp             x0, x16, [SP]
    // 0xadb1ac: r0 = Map._fromLiteral()
    //     0xadb1ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xadb1b0: r16 = "/doa/doa-detail"
    //     0xadb1b0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c220] "/doa/doa-detail"
    //     0xadb1b4: ldr             x16, [x16, #0x220]
    // 0xadb1b8: stp             x16, NULL, [SP, #8]
    // 0xadb1bc: str             x0, [SP]
    // 0xadb1c0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xadb1c0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xadb1c4: ldr             x4, [x4, #0x478]
    // 0xadb1c8: r0 = GetNavigation.toNamed()
    //     0xadb1c8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xadb1cc: LeaveFrame
    //     0xadb1cc: mov             SP, fp
    //     0xadb1d0: ldp             fp, lr, [SP], #0x10
    // 0xadb1d4: ret
    //     0xadb1d4: ret             
    // 0xadb1d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb1d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb1dc: b               #0xadb0d0
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xadb1e0, size: 0x48
    // 0xadb1e0: EnterFrame
    //     0xadb1e0: stp             fp, lr, [SP, #-0x10]!
    //     0xadb1e4: mov             fp, SP
    // 0xadb1e8: ldr             x0, [fp, #0x10]
    // 0xadb1ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb1ec: ldur            w1, [x0, #0x17]
    // 0xadb1f0: DecompressPointer r1
    //     0xadb1f0: add             x1, x1, HEAP, lsl #32
    // 0xadb1f4: CheckStackOverflow
    //     0xadb1f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb1f8: cmp             SP, x16
    //     0xadb1fc: b.ls            #0xadb220
    // 0xadb200: LoadField: r0 = r1->field_b
    //     0xadb200: ldur            w0, [x1, #0xb]
    // 0xadb204: DecompressPointer r0
    //     0xadb204: add             x0, x0, HEAP, lsl #32
    // 0xadb208: LoadField: r1 = r0->field_f
    //     0xadb208: ldur            w1, [x0, #0xf]
    // 0xadb20c: DecompressPointer r1
    //     0xadb20c: add             x1, x1, HEAP, lsl #32
    // 0xadb210: r0 = buildHistories()
    //     0xadb210: bl              #0xadb228  ; [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildHistories
    // 0xadb214: LeaveFrame
    //     0xadb214: mov             SP, fp
    //     0xadb218: ldp             fp, lr, [SP], #0x10
    // 0xadb21c: ret
    //     0xadb21c: ret             
    // 0xadb220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb224: b               #0xadb200
  }
  _ buildHistories(/* No info */) {
    // ** addr: 0xadb228, size: 0x3cc
    // 0xadb228: EnterFrame
    //     0xadb228: stp             fp, lr, [SP, #-0x10]!
    //     0xadb22c: mov             fp, SP
    // 0xadb230: AllocStack(0x40)
    //     0xadb230: sub             SP, SP, #0x40
    // 0xadb234: SetupParameters(DoaSearchView this /* r1 => r1, fp-0x8 */)
    //     0xadb234: stur            x1, [fp, #-8]
    // 0xadb238: CheckStackOverflow
    //     0xadb238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb23c: cmp             SP, x16
    //     0xadb240: b.ls            #0xadb5ec
    // 0xadb244: r1 = 1
    //     0xadb244: movz            x1, #0x1
    // 0xadb248: r0 = AllocateContext()
    //     0xadb248: bl              #0xec126c  ; AllocateContextStub
    // 0xadb24c: mov             x2, x0
    // 0xadb250: ldur            x0, [fp, #-8]
    // 0xadb254: stur            x2, [fp, #-0x10]
    // 0xadb258: StoreField: r2->field_f = r0
    //     0xadb258: stur            w0, [x2, #0xf]
    // 0xadb25c: mov             x1, x0
    // 0xadb260: r0 = controller()
    //     0xadb260: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadb264: LoadField: r1 = r0->field_27
    //     0xadb264: ldur            w1, [x0, #0x27]
    // 0xadb268: DecompressPointer r1
    //     0xadb268: add             x1, x1, HEAP, lsl #32
    // 0xadb26c: r0 = value()
    //     0xadb26c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xadb270: r1 = LoadClassIdInstr(r0)
    //     0xadb270: ldur            x1, [x0, #-1]
    //     0xadb274: ubfx            x1, x1, #0xc, #0x14
    // 0xadb278: str             x0, [SP]
    // 0xadb27c: mov             x0, x1
    // 0xadb280: r0 = GDT[cid_x0 + 0xc834]()
    //     0xadb280: movz            x17, #0xc834
    //     0xadb284: add             lr, x0, x17
    //     0xadb288: ldr             lr, [x21, lr, lsl #3]
    //     0xadb28c: blr             lr
    // 0xadb290: cbnz            w0, #0xadb2d0
    // 0xadb294: r0 = NEmptyState()
    //     0xadb294: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xadb298: mov             x1, x0
    // 0xadb29c: r2 = "Tulis kata kunci pada kolom di atas untuk mencari doa atau wirid."
    //     0xadb29c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f808] "Tulis kata kunci pada kolom di atas untuk mencari doa atau wirid."
    //     0xadb2a0: ldr             x2, [x2, #0x808]
    // 0xadb2a4: r3 = "assets/images/illustration/no_search_history.svg"
    //     0xadb2a4: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bcb8] "assets/images/illustration/no_search_history.svg"
    //     0xadb2a8: ldr             x3, [x3, #0xcb8]
    // 0xadb2ac: r5 = "Belum Ada Riwayat Pencarian"
    //     0xadb2ac: add             x5, PP, #0x29, lsl #12  ; [pp+0x294f0] "Belum Ada Riwayat Pencarian"
    //     0xadb2b0: ldr             x5, [x5, #0x4f0]
    // 0xadb2b4: stur            x0, [fp, #-0x18]
    // 0xadb2b8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xadb2b8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xadb2bc: r0 = NEmptyState.svg()
    //     0xadb2bc: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xadb2c0: ldur            x0, [fp, #-0x18]
    // 0xadb2c4: LeaveFrame
    //     0xadb2c4: mov             SP, fp
    //     0xadb2c8: ldp             fp, lr, [SP], #0x10
    // 0xadb2cc: ret
    //     0xadb2cc: ret             
    // 0xadb2d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadb2d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadb2d4: ldr             x0, [x0, #0x2670]
    //     0xadb2d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadb2dc: cmp             w0, w16
    //     0xadb2e0: b.ne            #0xadb2ec
    //     0xadb2e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadb2e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadb2ec: r0 = GetNavigation.textTheme()
    //     0xadb2ec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadb2f0: LoadField: r3 = r0->field_2f
    //     0xadb2f0: ldur            w3, [x0, #0x2f]
    // 0xadb2f4: DecompressPointer r3
    //     0xadb2f4: add             x3, x3, HEAP, lsl #32
    // 0xadb2f8: stur            x3, [fp, #-0x18]
    // 0xadb2fc: cmp             w3, NULL
    // 0xadb300: b.ne            #0xadb30c
    // 0xadb304: r0 = Null
    //     0xadb304: mov             x0, NULL
    // 0xadb308: b               #0xadb350
    // 0xadb30c: r1 = _ConstMap len:3
    //     0xadb30c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xadb310: ldr             x1, [x1, #0xcd0]
    // 0xadb314: r2 = 4
    //     0xadb314: movz            x2, #0x4
    // 0xadb318: r0 = []()
    //     0xadb318: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xadb31c: r16 = <Color?>
    //     0xadb31c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xadb320: ldr             x16, [x16, #0x98]
    // 0xadb324: stp             x0, x16, [SP, #8]
    // 0xadb328: r16 = Instance_MaterialColor
    //     0xadb328: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e40] Obj!MaterialColor@e2bbf1
    //     0xadb32c: ldr             x16, [x16, #0xe40]
    // 0xadb330: str             x16, [SP]
    // 0xadb334: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadb334: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadb338: r0 = mode()
    //     0xadb338: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xadb33c: str             x0, [SP]
    // 0xadb340: ldur            x1, [fp, #-0x18]
    // 0xadb344: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xadb344: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xadb348: ldr             x4, [x4, #0x228]
    // 0xadb34c: r0 = copyWith()
    //     0xadb34c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadb350: stur            x0, [fp, #-0x18]
    // 0xadb354: r0 = Text()
    //     0xadb354: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadb358: mov             x1, x0
    // 0xadb35c: r0 = "Hapus Semua"
    //     0xadb35c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29158] "Hapus Semua"
    //     0xadb360: ldr             x0, [x0, #0x158]
    // 0xadb364: stur            x1, [fp, #-0x20]
    // 0xadb368: StoreField: r1->field_b = r0
    //     0xadb368: stur            w0, [x1, #0xb]
    // 0xadb36c: ldur            x0, [fp, #-0x18]
    // 0xadb370: StoreField: r1->field_13 = r0
    //     0xadb370: stur            w0, [x1, #0x13]
    // 0xadb374: r0 = GestureDetector()
    //     0xadb374: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadb378: ldur            x2, [fp, #-0x10]
    // 0xadb37c: r1 = Function '<anonymous closure>':.
    //     0xadb37c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f810] AnonymousClosure: (0xadc9c8), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildHistories (0xadb228)
    //     0xadb380: ldr             x1, [x1, #0x810]
    // 0xadb384: stur            x0, [fp, #-0x18]
    // 0xadb388: r0 = AllocateClosure()
    //     0xadb388: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb38c: ldur            x16, [fp, #-0x20]
    // 0xadb390: stp             x16, x0, [SP]
    // 0xadb394: ldur            x1, [fp, #-0x18]
    // 0xadb398: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadb398: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadb39c: ldr             x4, [x4, #0x7d0]
    // 0xadb3a0: r0 = GestureDetector()
    //     0xadb3a0: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadb3a4: r1 = Null
    //     0xadb3a4: mov             x1, NULL
    // 0xadb3a8: r2 = 4
    //     0xadb3a8: movz            x2, #0x4
    // 0xadb3ac: r0 = AllocateArray()
    //     0xadb3ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadb3b0: stur            x0, [fp, #-0x20]
    // 0xadb3b4: r16 = Instance_Expanded
    //     0xadb3b4: add             x16, PP, #0x29, lsl #12  ; [pp+0x29168] Obj!Expanded@e1f401
    //     0xadb3b8: ldr             x16, [x16, #0x168]
    // 0xadb3bc: StoreField: r0->field_f = r16
    //     0xadb3bc: stur            w16, [x0, #0xf]
    // 0xadb3c0: ldur            x1, [fp, #-0x18]
    // 0xadb3c4: StoreField: r0->field_13 = r1
    //     0xadb3c4: stur            w1, [x0, #0x13]
    // 0xadb3c8: r1 = <Widget>
    //     0xadb3c8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadb3cc: r0 = AllocateGrowableArray()
    //     0xadb3cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadb3d0: mov             x1, x0
    // 0xadb3d4: ldur            x0, [fp, #-0x20]
    // 0xadb3d8: stur            x1, [fp, #-0x18]
    // 0xadb3dc: StoreField: r1->field_f = r0
    //     0xadb3dc: stur            w0, [x1, #0xf]
    // 0xadb3e0: r0 = 4
    //     0xadb3e0: movz            x0, #0x4
    // 0xadb3e4: StoreField: r1->field_b = r0
    //     0xadb3e4: stur            w0, [x1, #0xb]
    // 0xadb3e8: r0 = Row()
    //     0xadb3e8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xadb3ec: mov             x2, x0
    // 0xadb3f0: r0 = Instance_Axis
    //     0xadb3f0: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xadb3f4: stur            x2, [fp, #-0x20]
    // 0xadb3f8: StoreField: r2->field_f = r0
    //     0xadb3f8: stur            w0, [x2, #0xf]
    // 0xadb3fc: r0 = Instance_MainAxisAlignment
    //     0xadb3fc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xadb400: ldr             x0, [x0, #0x730]
    // 0xadb404: StoreField: r2->field_13 = r0
    //     0xadb404: stur            w0, [x2, #0x13]
    // 0xadb408: r3 = Instance_MainAxisSize
    //     0xadb408: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xadb40c: ldr             x3, [x3, #0x738]
    // 0xadb410: ArrayStore: r2[0] = r3  ; List_4
    //     0xadb410: stur            w3, [x2, #0x17]
    // 0xadb414: r4 = Instance_CrossAxisAlignment
    //     0xadb414: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xadb418: ldr             x4, [x4, #0x740]
    // 0xadb41c: StoreField: r2->field_1b = r4
    //     0xadb41c: stur            w4, [x2, #0x1b]
    // 0xadb420: r5 = Instance_VerticalDirection
    //     0xadb420: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xadb424: ldr             x5, [x5, #0x748]
    // 0xadb428: StoreField: r2->field_23 = r5
    //     0xadb428: stur            w5, [x2, #0x23]
    // 0xadb42c: r6 = Instance_Clip
    //     0xadb42c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xadb430: ldr             x6, [x6, #0x750]
    // 0xadb434: StoreField: r2->field_2b = r6
    //     0xadb434: stur            w6, [x2, #0x2b]
    // 0xadb438: StoreField: r2->field_2f = rZR
    //     0xadb438: stur            xzr, [x2, #0x2f]
    // 0xadb43c: ldur            x1, [fp, #-0x18]
    // 0xadb440: StoreField: r2->field_b = r1
    //     0xadb440: stur            w1, [x2, #0xb]
    // 0xadb444: ldur            x1, [fp, #-8]
    // 0xadb448: r0 = controller()
    //     0xadb448: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadb44c: LoadField: r1 = r0->field_27
    //     0xadb44c: ldur            w1, [x0, #0x27]
    // 0xadb450: DecompressPointer r1
    //     0xadb450: add             x1, x1, HEAP, lsl #32
    // 0xadb454: r0 = value()
    //     0xadb454: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xadb458: r1 = LoadClassIdInstr(r0)
    //     0xadb458: ldur            x1, [x0, #-1]
    //     0xadb45c: ubfx            x1, x1, #0xc, #0x14
    // 0xadb460: str             x0, [SP]
    // 0xadb464: mov             x0, x1
    // 0xadb468: r0 = GDT[cid_x0 + 0xc834]()
    //     0xadb468: movz            x17, #0xc834
    //     0xadb46c: add             lr, x0, x17
    //     0xadb470: ldr             lr, [x21, lr, lsl #3]
    //     0xadb474: blr             lr
    // 0xadb478: r3 = LoadInt32Instr(r0)
    //     0xadb478: sbfx            x3, x0, #1, #0x1f
    //     0xadb47c: tbz             w0, #0, #0xadb484
    //     0xadb480: ldur            x3, [x0, #7]
    // 0xadb484: ldur            x2, [fp, #-0x10]
    // 0xadb488: stur            x3, [fp, #-0x28]
    // 0xadb48c: r1 = Function '<anonymous closure>':.
    //     0xadb48c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f818] AnonymousClosure: (0xadb5f4), in [package:nuonline/app/modules/doa/doa_search/views/doa_search_view.dart] DoaSearchView::buildHistories (0xadb228)
    //     0xadb490: ldr             x1, [x1, #0x818]
    // 0xadb494: r0 = AllocateClosure()
    //     0xadb494: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb498: r1 = Function '<anonymous closure>':.
    //     0xadb498: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f820] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xadb49c: ldr             x1, [x1, #0x820]
    // 0xadb4a0: r2 = Null
    //     0xadb4a0: mov             x2, NULL
    // 0xadb4a4: stur            x0, [fp, #-8]
    // 0xadb4a8: r0 = AllocateClosure()
    //     0xadb4a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb4ac: stur            x0, [fp, #-0x10]
    // 0xadb4b0: r0 = ListView()
    //     0xadb4b0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xadb4b4: stur            x0, [fp, #-0x18]
    // 0xadb4b8: r16 = true
    //     0xadb4b8: add             x16, NULL, #0x20  ; true
    // 0xadb4bc: r30 = Instance_NeverScrollableScrollPhysics
    //     0xadb4bc: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xadb4c0: ldr             lr, [lr, #0x290]
    // 0xadb4c4: stp             lr, x16, [SP]
    // 0xadb4c8: mov             x1, x0
    // 0xadb4cc: ldur            x2, [fp, #-8]
    // 0xadb4d0: ldur            x3, [fp, #-0x28]
    // 0xadb4d4: ldur            x5, [fp, #-0x10]
    // 0xadb4d8: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xadb4d8: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xadb4dc: ldr             x4, [x4, #0x298]
    // 0xadb4e0: r0 = ListView.separated()
    //     0xadb4e0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xadb4e4: r0 = ListTileTheme()
    //     0xadb4e4: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xadb4e8: mov             x3, x0
    // 0xadb4ec: r0 = Instance_EdgeInsets
    //     0xadb4ec: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xadb4f0: stur            x3, [fp, #-8]
    // 0xadb4f4: StoreField: r3->field_2b = r0
    //     0xadb4f4: stur            w0, [x3, #0x2b]
    // 0xadb4f8: r0 = 0.000000
    //     0xadb4f8: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xadb4fc: StoreField: r3->field_37 = r0
    //     0xadb4fc: stur            w0, [x3, #0x37]
    // 0xadb500: ldur            x0, [fp, #-0x18]
    // 0xadb504: StoreField: r3->field_b = r0
    //     0xadb504: stur            w0, [x3, #0xb]
    // 0xadb508: r1 = Null
    //     0xadb508: mov             x1, NULL
    // 0xadb50c: r2 = 10
    //     0xadb50c: movz            x2, #0xa
    // 0xadb510: r0 = AllocateArray()
    //     0xadb510: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadb514: stur            x0, [fp, #-0x10]
    // 0xadb518: r16 = Instance_SizedBox
    //     0xadb518: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bcd8] Obj!SizedBox@e1e1c1
    //     0xadb51c: ldr             x16, [x16, #0xcd8]
    // 0xadb520: StoreField: r0->field_f = r16
    //     0xadb520: stur            w16, [x0, #0xf]
    // 0xadb524: ldur            x1, [fp, #-0x20]
    // 0xadb528: StoreField: r0->field_13 = r1
    //     0xadb528: stur            w1, [x0, #0x13]
    // 0xadb52c: r16 = Instance_SizedBox
    //     0xadb52c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bce0] Obj!SizedBox@e1e1a1
    //     0xadb530: ldr             x16, [x16, #0xce0]
    // 0xadb534: ArrayStore: r0[0] = r16  ; List_4
    //     0xadb534: stur            w16, [x0, #0x17]
    // 0xadb538: r16 = Instance_Divider
    //     0xadb538: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xadb53c: ldr             x16, [x16, #0xc28]
    // 0xadb540: StoreField: r0->field_1b = r16
    //     0xadb540: stur            w16, [x0, #0x1b]
    // 0xadb544: ldur            x1, [fp, #-8]
    // 0xadb548: StoreField: r0->field_1f = r1
    //     0xadb548: stur            w1, [x0, #0x1f]
    // 0xadb54c: r1 = <Widget>
    //     0xadb54c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadb550: r0 = AllocateGrowableArray()
    //     0xadb550: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadb554: mov             x1, x0
    // 0xadb558: ldur            x0, [fp, #-0x10]
    // 0xadb55c: stur            x1, [fp, #-8]
    // 0xadb560: StoreField: r1->field_f = r0
    //     0xadb560: stur            w0, [x1, #0xf]
    // 0xadb564: r0 = 10
    //     0xadb564: movz            x0, #0xa
    // 0xadb568: StoreField: r1->field_b = r0
    //     0xadb568: stur            w0, [x1, #0xb]
    // 0xadb56c: r0 = Column()
    //     0xadb56c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xadb570: mov             x1, x0
    // 0xadb574: r0 = Instance_Axis
    //     0xadb574: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xadb578: stur            x1, [fp, #-0x10]
    // 0xadb57c: StoreField: r1->field_f = r0
    //     0xadb57c: stur            w0, [x1, #0xf]
    // 0xadb580: r0 = Instance_MainAxisAlignment
    //     0xadb580: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xadb584: ldr             x0, [x0, #0x730]
    // 0xadb588: StoreField: r1->field_13 = r0
    //     0xadb588: stur            w0, [x1, #0x13]
    // 0xadb58c: r0 = Instance_MainAxisSize
    //     0xadb58c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xadb590: ldr             x0, [x0, #0x738]
    // 0xadb594: ArrayStore: r1[0] = r0  ; List_4
    //     0xadb594: stur            w0, [x1, #0x17]
    // 0xadb598: r0 = Instance_CrossAxisAlignment
    //     0xadb598: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xadb59c: ldr             x0, [x0, #0x740]
    // 0xadb5a0: StoreField: r1->field_1b = r0
    //     0xadb5a0: stur            w0, [x1, #0x1b]
    // 0xadb5a4: r0 = Instance_VerticalDirection
    //     0xadb5a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xadb5a8: ldr             x0, [x0, #0x748]
    // 0xadb5ac: StoreField: r1->field_23 = r0
    //     0xadb5ac: stur            w0, [x1, #0x23]
    // 0xadb5b0: r0 = Instance_Clip
    //     0xadb5b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xadb5b4: ldr             x0, [x0, #0x750]
    // 0xadb5b8: StoreField: r1->field_2b = r0
    //     0xadb5b8: stur            w0, [x1, #0x2b]
    // 0xadb5bc: StoreField: r1->field_2f = rZR
    //     0xadb5bc: stur            xzr, [x1, #0x2f]
    // 0xadb5c0: ldur            x0, [fp, #-8]
    // 0xadb5c4: StoreField: r1->field_b = r0
    //     0xadb5c4: stur            w0, [x1, #0xb]
    // 0xadb5c8: r0 = Padding()
    //     0xadb5c8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadb5cc: r1 = Instance_EdgeInsets
    //     0xadb5cc: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bce8] Obj!EdgeInsets@e123d1
    //     0xadb5d0: ldr             x1, [x1, #0xce8]
    // 0xadb5d4: StoreField: r0->field_f = r1
    //     0xadb5d4: stur            w1, [x0, #0xf]
    // 0xadb5d8: ldur            x1, [fp, #-0x10]
    // 0xadb5dc: StoreField: r0->field_b = r1
    //     0xadb5dc: stur            w1, [x0, #0xb]
    // 0xadb5e0: LeaveFrame
    //     0xadb5e0: mov             SP, fp
    //     0xadb5e4: ldp             fp, lr, [SP], #0x10
    // 0xadb5e8: ret
    //     0xadb5e8: ret             
    // 0xadb5ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb5ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb5f0: b               #0xadb244
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadb5f4, size: 0x1cc
    // 0xadb5f4: EnterFrame
    //     0xadb5f4: stp             fp, lr, [SP, #-0x10]!
    //     0xadb5f8: mov             fp, SP
    // 0xadb5fc: AllocStack(0x30)
    //     0xadb5fc: sub             SP, SP, #0x30
    // 0xadb600: SetupParameters()
    //     0xadb600: ldr             x0, [fp, #0x20]
    //     0xadb604: ldur            w1, [x0, #0x17]
    //     0xadb608: add             x1, x1, HEAP, lsl #32
    //     0xadb60c: stur            x1, [fp, #-8]
    // 0xadb610: CheckStackOverflow
    //     0xadb610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb614: cmp             SP, x16
    //     0xadb618: b.ls            #0xadb7b8
    // 0xadb61c: r1 = 2
    //     0xadb61c: movz            x1, #0x2
    // 0xadb620: r0 = AllocateContext()
    //     0xadb620: bl              #0xec126c  ; AllocateContextStub
    // 0xadb624: mov             x2, x0
    // 0xadb628: ldur            x0, [fp, #-8]
    // 0xadb62c: stur            x2, [fp, #-0x10]
    // 0xadb630: StoreField: r2->field_b = r0
    //     0xadb630: stur            w0, [x2, #0xb]
    // 0xadb634: ldr             x3, [fp, #0x10]
    // 0xadb638: StoreField: r2->field_f = r3
    //     0xadb638: stur            w3, [x2, #0xf]
    // 0xadb63c: LoadField: r1 = r0->field_f
    //     0xadb63c: ldur            w1, [x0, #0xf]
    // 0xadb640: DecompressPointer r1
    //     0xadb640: add             x1, x1, HEAP, lsl #32
    // 0xadb644: r0 = controller()
    //     0xadb644: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadb648: LoadField: r1 = r0->field_27
    //     0xadb648: ldur            w1, [x0, #0x27]
    // 0xadb64c: DecompressPointer r1
    //     0xadb64c: add             x1, x1, HEAP, lsl #32
    // 0xadb650: r0 = value()
    //     0xadb650: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xadb654: r1 = LoadClassIdInstr(r0)
    //     0xadb654: ldur            x1, [x0, #-1]
    //     0xadb658: ubfx            x1, x1, #0xc, #0x14
    // 0xadb65c: ldr             x16, [fp, #0x10]
    // 0xadb660: stp             x16, x0, [SP]
    // 0xadb664: mov             x0, x1
    // 0xadb668: r0 = GDT[cid_x0 + 0x13037]()
    //     0xadb668: movz            x17, #0x3037
    //     0xadb66c: movk            x17, #0x1, lsl #16
    //     0xadb670: add             lr, x0, x17
    //     0xadb674: ldr             lr, [x21, lr, lsl #3]
    //     0xadb678: blr             lr
    // 0xadb67c: mov             x1, x0
    // 0xadb680: ldur            x2, [fp, #-0x10]
    // 0xadb684: stur            x1, [fp, #-8]
    // 0xadb688: StoreField: r2->field_13 = r0
    //     0xadb688: stur            w0, [x2, #0x13]
    //     0xadb68c: tbz             w0, #0, #0xadb6a8
    //     0xadb690: ldurb           w16, [x2, #-1]
    //     0xadb694: ldurb           w17, [x0, #-1]
    //     0xadb698: and             x16, x17, x16, lsr #2
    //     0xadb69c: tst             x16, HEAP, lsr #32
    //     0xadb6a0: b.eq            #0xadb6a8
    //     0xadb6a4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xadb6a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadb6a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadb6ac: ldr             x0, [x0, #0x2670]
    //     0xadb6b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadb6b4: cmp             w0, w16
    //     0xadb6b8: b.ne            #0xadb6c4
    //     0xadb6bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadb6c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadb6c4: r0 = GetNavigation.textTheme()
    //     0xadb6c4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadb6c8: LoadField: r1 = r0->field_23
    //     0xadb6c8: ldur            w1, [x0, #0x23]
    // 0xadb6cc: DecompressPointer r1
    //     0xadb6cc: add             x1, x1, HEAP, lsl #32
    // 0xadb6d0: stur            x1, [fp, #-0x18]
    // 0xadb6d4: r0 = Text()
    //     0xadb6d4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadb6d8: mov             x3, x0
    // 0xadb6dc: ldur            x0, [fp, #-8]
    // 0xadb6e0: stur            x3, [fp, #-0x20]
    // 0xadb6e4: StoreField: r3->field_b = r0
    //     0xadb6e4: stur            w0, [x3, #0xb]
    // 0xadb6e8: ldur            x0, [fp, #-0x18]
    // 0xadb6ec: StoreField: r3->field_13 = r0
    //     0xadb6ec: stur            w0, [x3, #0x13]
    // 0xadb6f0: ldur            x2, [fp, #-0x10]
    // 0xadb6f4: r1 = Function '<anonymous closure>':.
    //     0xadb6f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f828] AnonymousClosure: (0xadba40), in [package:nuonline/app/modules/quran/quran_search/views/quran_search_view.dart] QuranSearchView::buildHistories (0xadbb30)
    //     0xadb6f8: ldr             x1, [x1, #0x828]
    // 0xadb6fc: r0 = AllocateClosure()
    //     0xadb6fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb700: stur            x0, [fp, #-8]
    // 0xadb704: r0 = IconButton()
    //     0xadb704: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xadb708: mov             x1, x0
    // 0xadb70c: ldur            x0, [fp, #-8]
    // 0xadb710: stur            x1, [fp, #-0x18]
    // 0xadb714: StoreField: r1->field_3b = r0
    //     0xadb714: stur            w0, [x1, #0x3b]
    // 0xadb718: r0 = false
    //     0xadb718: add             x0, NULL, #0x30  ; false
    // 0xadb71c: StoreField: r1->field_47 = r0
    //     0xadb71c: stur            w0, [x1, #0x47]
    // 0xadb720: r2 = Instance_Icon
    //     0xadb720: add             x2, PP, #0x29, lsl #12  ; [pp+0x291a0] Obj!Icon@e24331
    //     0xadb724: ldr             x2, [x2, #0x1a0]
    // 0xadb728: StoreField: r1->field_1f = r2
    //     0xadb728: stur            w2, [x1, #0x1f]
    // 0xadb72c: r2 = Instance__IconButtonVariant
    //     0xadb72c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xadb730: ldr             x2, [x2, #0xf78]
    // 0xadb734: StoreField: r1->field_63 = r2
    //     0xadb734: stur            w2, [x1, #0x63]
    // 0xadb738: r0 = ListTile()
    //     0xadb738: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xadb73c: mov             x3, x0
    // 0xadb740: r0 = Instance_Icon
    //     0xadb740: add             x0, PP, #0x29, lsl #12  ; [pp+0x291a8] Obj!Icon@e242f1
    //     0xadb744: ldr             x0, [x0, #0x1a8]
    // 0xadb748: stur            x3, [fp, #-8]
    // 0xadb74c: StoreField: r3->field_b = r0
    //     0xadb74c: stur            w0, [x3, #0xb]
    // 0xadb750: ldur            x0, [fp, #-0x20]
    // 0xadb754: StoreField: r3->field_f = r0
    //     0xadb754: stur            w0, [x3, #0xf]
    // 0xadb758: ldur            x0, [fp, #-0x18]
    // 0xadb75c: ArrayStore: r3[0] = r0  ; List_4
    //     0xadb75c: stur            w0, [x3, #0x17]
    // 0xadb760: r0 = false
    //     0xadb760: add             x0, NULL, #0x30  ; false
    // 0xadb764: StoreField: r3->field_1b = r0
    //     0xadb764: stur            w0, [x3, #0x1b]
    // 0xadb768: r4 = true
    //     0xadb768: add             x4, NULL, #0x20  ; true
    // 0xadb76c: StoreField: r3->field_4b = r4
    //     0xadb76c: stur            w4, [x3, #0x4b]
    // 0xadb770: ldur            x2, [fp, #-0x10]
    // 0xadb774: r1 = Function '<anonymous closure>':.
    //     0xadb774: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f830] AnonymousClosure: (0xadb7c0), in [package:nuonline/app/modules/quran/quran_search/views/quran_search_view.dart] QuranSearchView::buildHistories (0xadbb30)
    //     0xadb778: ldr             x1, [x1, #0x830]
    // 0xadb77c: r0 = AllocateClosure()
    //     0xadb77c: bl              #0xec1630  ; AllocateClosureStub
    // 0xadb780: mov             x1, x0
    // 0xadb784: ldur            x0, [fp, #-8]
    // 0xadb788: StoreField: r0->field_4f = r1
    //     0xadb788: stur            w1, [x0, #0x4f]
    // 0xadb78c: r1 = false
    //     0xadb78c: add             x1, NULL, #0x30  ; false
    // 0xadb790: StoreField: r0->field_5f = r1
    //     0xadb790: stur            w1, [x0, #0x5f]
    // 0xadb794: StoreField: r0->field_73 = r1
    //     0xadb794: stur            w1, [x0, #0x73]
    // 0xadb798: r1 = 40.000000
    //     0xadb798: add             x1, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xadb79c: ldr             x1, [x1, #0x1b8]
    // 0xadb7a0: StoreField: r0->field_8b = r1
    //     0xadb7a0: stur            w1, [x0, #0x8b]
    // 0xadb7a4: r1 = true
    //     0xadb7a4: add             x1, NULL, #0x20  ; true
    // 0xadb7a8: StoreField: r0->field_97 = r1
    //     0xadb7a8: stur            w1, [x0, #0x97]
    // 0xadb7ac: LeaveFrame
    //     0xadb7ac: mov             SP, fp
    //     0xadb7b0: ldp             fp, lr, [SP], #0x10
    // 0xadb7b4: ret
    //     0xadb7b4: ret             
    // 0xadb7b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb7b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb7bc: b               #0xadb61c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadc9c8, size: 0xd8
    // 0xadc9c8: EnterFrame
    //     0xadc9c8: stp             fp, lr, [SP, #-0x10]!
    //     0xadc9cc: mov             fp, SP
    // 0xadc9d0: AllocStack(0x20)
    //     0xadc9d0: sub             SP, SP, #0x20
    // 0xadc9d4: SetupParameters()
    //     0xadc9d4: ldr             x0, [fp, #0x10]
    //     0xadc9d8: ldur            w1, [x0, #0x17]
    //     0xadc9dc: add             x1, x1, HEAP, lsl #32
    //     0xadc9e0: stur            x1, [fp, #-8]
    // 0xadc9e4: CheckStackOverflow
    //     0xadc9e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc9e8: cmp             SP, x16
    //     0xadc9ec: b.ls            #0xadca98
    // 0xadc9f0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadc9f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadc9f4: ldr             x0, [x0, #0x2670]
    //     0xadc9f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadc9fc: cmp             w0, w16
    //     0xadca00: b.ne            #0xadca0c
    //     0xadca04: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadca08: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadca0c: ldur            x0, [fp, #-8]
    // 0xadca10: LoadField: r1 = r0->field_f
    //     0xadca10: ldur            w1, [x0, #0xf]
    // 0xadca14: DecompressPointer r1
    //     0xadca14: add             x1, x1, HEAP, lsl #32
    // 0xadca18: r0 = controller()
    //     0xadca18: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadca1c: stur            x0, [fp, #-8]
    // 0xadca20: r0 = NDialog()
    //     0xadca20: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xadca24: mov             x3, x0
    // 0xadca28: r0 = "Hapus semua riwayat pencarian\?"
    //     0xadca28: add             x0, PP, #0x29, lsl #12  ; [pp+0x294c0] "Hapus semua riwayat pencarian\?"
    //     0xadca2c: ldr             x0, [x0, #0x4c0]
    // 0xadca30: stur            x3, [fp, #-0x10]
    // 0xadca34: StoreField: r3->field_b = r0
    //     0xadca34: stur            w0, [x3, #0xb]
    // 0xadca38: ldur            x2, [fp, #-8]
    // 0xadca3c: r1 = Function 'removeAllHistory':.
    //     0xadca3c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bdf8] AnonymousClosure: (0xadbfd4), in [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::removeAllHistory (0xadc00c)
    //     0xadca40: ldr             x1, [x1, #0xdf8]
    // 0xadca44: r0 = AllocateClosure()
    //     0xadca44: bl              #0xec1630  ; AllocateClosureStub
    // 0xadca48: mov             x1, x0
    // 0xadca4c: ldur            x0, [fp, #-0x10]
    // 0xadca50: ArrayStore: r0[0] = r1  ; List_4
    //     0xadca50: stur            w1, [x0, #0x17]
    // 0xadca54: r1 = Function '<anonymous closure>':.
    //     0xadca54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f838] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xadca58: ldr             x1, [x1, #0x838]
    // 0xadca5c: r2 = Null
    //     0xadca5c: mov             x2, NULL
    // 0xadca60: r0 = AllocateClosure()
    //     0xadca60: bl              #0xec1630  ; AllocateClosureStub
    // 0xadca64: mov             x1, x0
    // 0xadca68: ldur            x0, [fp, #-0x10]
    // 0xadca6c: StoreField: r0->field_1b = r1
    //     0xadca6c: stur            w1, [x0, #0x1b]
    // 0xadca70: r1 = "Ya, Hapus"
    //     0xadca70: add             x1, PP, #0x29, lsl #12  ; [pp+0x294d8] "Ya, Hapus"
    //     0xadca74: ldr             x1, [x1, #0x4d8]
    // 0xadca78: StoreField: r0->field_13 = r1
    //     0xadca78: stur            w1, [x0, #0x13]
    // 0xadca7c: stp             x0, NULL, [SP]
    // 0xadca80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadca80: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadca84: r0 = ExtensionDialog.dialog()
    //     0xadca84: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xadca88: r0 = Null
    //     0xadca88: mov             x0, NULL
    // 0xadca8c: LeaveFrame
    //     0xadca8c: mov             SP, fp
    //     0xadca90: ldp             fp, lr, [SP], #0x10
    // 0xadca94: ret
    //     0xadca94: ret             
    // 0xadca98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadca98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadca9c: b               #0xadc9f0
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xadcaa0, size: 0x158
    // 0xadcaa0: EnterFrame
    //     0xadcaa0: stp             fp, lr, [SP, #-0x10]!
    //     0xadcaa4: mov             fp, SP
    // 0xadcaa8: AllocStack(0x30)
    //     0xadcaa8: sub             SP, SP, #0x30
    // 0xadcaac: SetupParameters()
    //     0xadcaac: ldr             x0, [fp, #0x10]
    //     0xadcab0: ldur            w2, [x0, #0x17]
    //     0xadcab4: add             x2, x2, HEAP, lsl #32
    //     0xadcab8: stur            x2, [fp, #-8]
    // 0xadcabc: CheckStackOverflow
    //     0xadcabc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadcac0: cmp             SP, x16
    //     0xadcac4: b.ls            #0xadcbe4
    // 0xadcac8: LoadField: r1 = r2->field_f
    //     0xadcac8: ldur            w1, [x2, #0xf]
    // 0xadcacc: DecompressPointer r1
    //     0xadcacc: add             x1, x1, HEAP, lsl #32
    // 0xadcad0: r0 = controller()
    //     0xadcad0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadcad4: LoadField: r2 = r0->field_1f
    //     0xadcad4: ldur            w2, [x0, #0x1f]
    // 0xadcad8: DecompressPointer r2
    //     0xadcad8: add             x2, x2, HEAP, lsl #32
    // 0xadcadc: r16 = Sentinel
    //     0xadcadc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadcae0: cmp             w2, w16
    // 0xadcae4: b.eq            #0xadcbec
    // 0xadcae8: ldur            x0, [fp, #-8]
    // 0xadcaec: stur            x2, [fp, #-0x10]
    // 0xadcaf0: LoadField: r1 = r0->field_f
    //     0xadcaf0: ldur            w1, [x0, #0xf]
    // 0xadcaf4: DecompressPointer r1
    //     0xadcaf4: add             x1, x1, HEAP, lsl #32
    // 0xadcaf8: r0 = controller()
    //     0xadcaf8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadcafc: mov             x2, x0
    // 0xadcb00: ldur            x0, [fp, #-8]
    // 0xadcb04: stur            x2, [fp, #-0x18]
    // 0xadcb08: LoadField: r1 = r0->field_f
    //     0xadcb08: ldur            w1, [x0, #0xf]
    // 0xadcb0c: DecompressPointer r1
    //     0xadcb0c: add             x1, x1, HEAP, lsl #32
    // 0xadcb10: r0 = controller()
    //     0xadcb10: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadcb14: LoadField: r1 = r0->field_23
    //     0xadcb14: ldur            w1, [x0, #0x23]
    // 0xadcb18: DecompressPointer r1
    //     0xadcb18: add             x1, x1, HEAP, lsl #32
    // 0xadcb1c: r0 = value()
    //     0xadcb1c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xadcb20: mov             x2, x0
    // 0xadcb24: ldur            x0, [fp, #-8]
    // 0xadcb28: stur            x2, [fp, #-0x20]
    // 0xadcb2c: LoadField: r1 = r0->field_f
    //     0xadcb2c: ldur            w1, [x0, #0xf]
    // 0xadcb30: DecompressPointer r1
    //     0xadcb30: add             x1, x1, HEAP, lsl #32
    // 0xadcb34: r0 = controller()
    //     0xadcb34: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadcb38: mov             x2, x0
    // 0xadcb3c: ldur            x0, [fp, #-8]
    // 0xadcb40: stur            x2, [fp, #-0x28]
    // 0xadcb44: LoadField: r1 = r0->field_f
    //     0xadcb44: ldur            w1, [x0, #0xf]
    // 0xadcb48: DecompressPointer r1
    //     0xadcb48: add             x1, x1, HEAP, lsl #32
    // 0xadcb4c: r0 = controller()
    //     0xadcb4c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadcb50: stur            x0, [fp, #-8]
    // 0xadcb54: r0 = NSearchTextField()
    //     0xadcb54: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xadcb58: mov             x3, x0
    // 0xadcb5c: ldur            x0, [fp, #-0x10]
    // 0xadcb60: stur            x3, [fp, #-0x30]
    // 0xadcb64: StoreField: r3->field_f = r0
    //     0xadcb64: stur            w0, [x3, #0xf]
    // 0xadcb68: ldur            x0, [fp, #-0x20]
    // 0xadcb6c: StoreField: r3->field_b = r0
    //     0xadcb6c: stur            w0, [x3, #0xb]
    // 0xadcb70: ldur            x2, [fp, #-0x18]
    // 0xadcb74: r1 = Function 'onQueryChanged':.
    //     0xadcb74: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be08] AnonymousClosure: (0xadcd10), in [package:nuonline/app/modules/quran/quran_search/controllers/quran_search_controller.dart] _QuranSearchController&GetxController&SearchMixin::onQueryChanged (0xadcd4c)
    //     0xadcb78: ldr             x1, [x1, #0xe08]
    // 0xadcb7c: r0 = AllocateClosure()
    //     0xadcb7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xadcb80: mov             x1, x0
    // 0xadcb84: ldur            x0, [fp, #-0x30]
    // 0xadcb88: StoreField: r0->field_13 = r1
    //     0xadcb88: stur            w1, [x0, #0x13]
    // 0xadcb8c: ldur            x2, [fp, #-8]
    // 0xadcb90: r1 = Function 'onSearchCanceled':.
    //     0xadcb90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f840] AnonymousClosure: (0xadcbf8), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onSearchCanceled (0xadcc30)
    //     0xadcb94: ldr             x1, [x1, #0x840]
    // 0xadcb98: r0 = AllocateClosure()
    //     0xadcb98: bl              #0xec1630  ; AllocateClosureStub
    // 0xadcb9c: mov             x1, x0
    // 0xadcba0: ldur            x0, [fp, #-0x30]
    // 0xadcba4: ArrayStore: r0[0] = r1  ; List_4
    //     0xadcba4: stur            w1, [x0, #0x17]
    // 0xadcba8: r1 = "Cari Wirid/Doa"
    //     0xadcba8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f848] "Cari Wirid/Doa"
    //     0xadcbac: ldr             x1, [x1, #0x848]
    // 0xadcbb0: StoreField: r0->field_23 = r1
    //     0xadcbb0: stur            w1, [x0, #0x23]
    // 0xadcbb4: ldur            x2, [fp, #-0x28]
    // 0xadcbb8: r1 = Function 'onSearchSubmitted':.
    //     0xadcbb8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f850] AnonymousClosure: (0xadc210), in [package:nuonline/app/modules/doa/doa_search/controllers/doa_search_controller.dart] DoaSearchController::onSearchSubmitted (0xe3f170)
    //     0xadcbbc: ldr             x1, [x1, #0x850]
    // 0xadcbc0: r0 = AllocateClosure()
    //     0xadcbc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xadcbc4: mov             x1, x0
    // 0xadcbc8: ldur            x0, [fp, #-0x30]
    // 0xadcbcc: StoreField: r0->field_1b = r1
    //     0xadcbcc: stur            w1, [x0, #0x1b]
    // 0xadcbd0: r1 = true
    //     0xadcbd0: add             x1, NULL, #0x20  ; true
    // 0xadcbd4: StoreField: r0->field_27 = r1
    //     0xadcbd4: stur            w1, [x0, #0x27]
    // 0xadcbd8: LeaveFrame
    //     0xadcbd8: mov             SP, fp
    //     0xadcbdc: ldp             fp, lr, [SP], #0x10
    // 0xadcbe0: ret
    //     0xadcbe0: ret             
    // 0xadcbe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadcbe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadcbe8: b               #0xadcac8
    // 0xadcbec: r9 = searchController
    //     0xadcbec: add             x9, PP, #0x2b, lsl #12  ; [pp+0x2bd18] Field <_QuranSearchController&GetxController&<EMAIL>>: late (offset: 0x20)
    //     0xadcbf0: ldr             x9, [x9, #0xd18]
    // 0xadcbf4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xadcbf4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
