// lib: , url: package:nuonline/app/modules/doa/views/doa_view.dart

// class id: 1050188, size: 0x8
class :: {
}

// class id: 5298, size: 0x14, field offset: 0x14
class DoaView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xadcec4, size: 0x244
    // 0xadcec4: EnterFrame
    //     0xadcec4: stp             fp, lr, [SP, #-0x10]!
    //     0xadcec8: mov             fp, SP
    // 0xadcecc: AllocStack(0x38)
    //     0xadcecc: sub             SP, SP, #0x38
    // 0xadced0: SetupParameters(DoaView this /* r1 => r1, fp-0x8 */)
    //     0xadced0: stur            x1, [fp, #-8]
    // 0xadced4: CheckStackOverflow
    //     0xadced4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadced8: cmp             SP, x16
    //     0xadcedc: b.ls            #0xadd0f4
    // 0xadcee0: r1 = 1
    //     0xadcee0: movz            x1, #0x1
    // 0xadcee4: r0 = AllocateContext()
    //     0xadcee4: bl              #0xec126c  ; AllocateContextStub
    // 0xadcee8: ldur            x1, [fp, #-8]
    // 0xadceec: stur            x0, [fp, #-0x10]
    // 0xadcef0: StoreField: r0->field_f = r1
    //     0xadcef0: stur            w1, [x0, #0xf]
    // 0xadcef4: r0 = Obx()
    //     0xadcef4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xadcef8: ldur            x2, [fp, #-0x10]
    // 0xadcefc: r1 = Function '<anonymous closure>':.
    //     0xadcefc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f790] AnonymousClosure: (0xadd164), in [package:nuonline/app/modules/doa/views/doa_view.dart] DoaView::build (0xadcec4)
    //     0xadcf00: ldr             x1, [x1, #0x790]
    // 0xadcf04: stur            x0, [fp, #-0x10]
    // 0xadcf08: r0 = AllocateClosure()
    //     0xadcf08: bl              #0xec1630  ; AllocateClosureStub
    // 0xadcf0c: mov             x1, x0
    // 0xadcf10: ldur            x0, [fp, #-0x10]
    // 0xadcf14: StoreField: r0->field_b = r1
    //     0xadcf14: stur            w1, [x0, #0xb]
    // 0xadcf18: r0 = SizedBox()
    //     0xadcf18: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadcf1c: mov             x1, x0
    // 0xadcf20: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xadcf20: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xadcf24: ldr             x0, [x0, #0xc58]
    // 0xadcf28: stur            x1, [fp, #-0x18]
    // 0xadcf2c: StoreField: r1->field_f = r0
    //     0xadcf2c: stur            w0, [x1, #0xf]
    // 0xadcf30: ldur            x0, [fp, #-0x10]
    // 0xadcf34: StoreField: r1->field_b = r0
    //     0xadcf34: stur            w0, [x1, #0xb]
    // 0xadcf38: r0 = Padding()
    //     0xadcf38: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadcf3c: mov             x1, x0
    // 0xadcf40: r0 = Instance_EdgeInsets
    //     0xadcf40: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c60] Obj!EdgeInsets@e12611
    //     0xadcf44: ldr             x0, [x0, #0xc60]
    // 0xadcf48: stur            x1, [fp, #-0x10]
    // 0xadcf4c: StoreField: r1->field_f = r0
    //     0xadcf4c: stur            w0, [x1, #0xf]
    // 0xadcf50: ldur            x0, [fp, #-0x18]
    // 0xadcf54: StoreField: r1->field_b = r0
    //     0xadcf54: stur            w0, [x1, #0xb]
    // 0xadcf58: r0 = SizedBox()
    //     0xadcf58: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadcf5c: mov             x3, x0
    // 0xadcf60: r0 = 56.000000
    //     0xadcf60: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c68] 56
    //     0xadcf64: ldr             x0, [x0, #0xc68]
    // 0xadcf68: stur            x3, [fp, #-0x18]
    // 0xadcf6c: StoreField: r3->field_13 = r0
    //     0xadcf6c: stur            w0, [x3, #0x13]
    // 0xadcf70: ldur            x0, [fp, #-0x10]
    // 0xadcf74: StoreField: r3->field_b = r0
    //     0xadcf74: stur            w0, [x3, #0xb]
    // 0xadcf78: r1 = Function '<anonymous closure>':.
    //     0xadcf78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f798] AnonymousClosure: (0xadd108), in [package:nuonline/app/modules/doa/views/doa_view.dart] DoaView::build (0xadcec4)
    //     0xadcf7c: ldr             x1, [x1, #0x798]
    // 0xadcf80: r2 = Null
    //     0xadcf80: mov             x2, NULL
    // 0xadcf84: r0 = AllocateClosure()
    //     0xadcf84: bl              #0xec1630  ; AllocateClosureStub
    // 0xadcf88: stur            x0, [fp, #-0x10]
    // 0xadcf8c: r0 = IconButton()
    //     0xadcf8c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xadcf90: mov             x3, x0
    // 0xadcf94: ldur            x0, [fp, #-0x10]
    // 0xadcf98: stur            x3, [fp, #-0x20]
    // 0xadcf9c: StoreField: r3->field_3b = r0
    //     0xadcf9c: stur            w0, [x3, #0x3b]
    // 0xadcfa0: r0 = false
    //     0xadcfa0: add             x0, NULL, #0x30  ; false
    // 0xadcfa4: StoreField: r3->field_47 = r0
    //     0xadcfa4: stur            w0, [x3, #0x47]
    // 0xadcfa8: r1 = Instance_Icon
    //     0xadcfa8: add             x1, PP, #0x29, lsl #12  ; [pp+0x296b8] Obj!Icon@e24371
    //     0xadcfac: ldr             x1, [x1, #0x6b8]
    // 0xadcfb0: StoreField: r3->field_1f = r1
    //     0xadcfb0: stur            w1, [x3, #0x1f]
    // 0xadcfb4: r1 = Instance__IconButtonVariant
    //     0xadcfb4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xadcfb8: ldr             x1, [x1, #0xf78]
    // 0xadcfbc: StoreField: r3->field_63 = r1
    //     0xadcfbc: stur            w1, [x3, #0x63]
    // 0xadcfc0: r1 = Null
    //     0xadcfc0: mov             x1, NULL
    // 0xadcfc4: r2 = 2
    //     0xadcfc4: movz            x2, #0x2
    // 0xadcfc8: r0 = AllocateArray()
    //     0xadcfc8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadcfcc: mov             x2, x0
    // 0xadcfd0: ldur            x0, [fp, #-0x20]
    // 0xadcfd4: stur            x2, [fp, #-0x10]
    // 0xadcfd8: StoreField: r2->field_f = r0
    //     0xadcfd8: stur            w0, [x2, #0xf]
    // 0xadcfdc: r1 = <Widget>
    //     0xadcfdc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadcfe0: r0 = AllocateGrowableArray()
    //     0xadcfe0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadcfe4: mov             x1, x0
    // 0xadcfe8: ldur            x0, [fp, #-0x10]
    // 0xadcfec: stur            x1, [fp, #-0x20]
    // 0xadcff0: StoreField: r1->field_f = r0
    //     0xadcff0: stur            w0, [x1, #0xf]
    // 0xadcff4: r0 = 2
    //     0xadcff4: movz            x0, #0x2
    // 0xadcff8: StoreField: r1->field_b = r0
    //     0xadcff8: stur            w0, [x1, #0xb]
    // 0xadcffc: r0 = AppBar()
    //     0xadcffc: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xadd000: stur            x0, [fp, #-0x10]
    // 0xadd004: r16 = true
    //     0xadd004: add             x16, NULL, #0x20  ; true
    // 0xadd008: ldur            lr, [fp, #-0x18]
    // 0xadd00c: stp             lr, x16, [SP, #8]
    // 0xadd010: ldur            x16, [fp, #-0x20]
    // 0xadd014: str             x16, [SP]
    // 0xadd018: mov             x1, x0
    // 0xadd01c: r4 = const [0, 0x4, 0x3, 0x1, actions, 0x3, centerTitle, 0x1, title, 0x2, null]
    //     0xadd01c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27c80] List(11) [0, 0x4, 0x3, 0x1, "actions", 0x3, "centerTitle", 0x1, "title", 0x2, Null]
    //     0xadd020: ldr             x4, [x4, #0xc80]
    // 0xadd024: r0 = AppBar()
    //     0xadd024: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xadd028: r0 = PreferredSize()
    //     0xadd028: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xadd02c: mov             x2, x0
    // 0xadd030: r0 = Instance_Size
    //     0xadd030: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xadd034: ldr             x0, [x0, #0xc88]
    // 0xadd038: stur            x2, [fp, #-0x18]
    // 0xadd03c: StoreField: r2->field_f = r0
    //     0xadd03c: stur            w0, [x2, #0xf]
    // 0xadd040: ldur            x0, [fp, #-0x10]
    // 0xadd044: StoreField: r2->field_b = r0
    //     0xadd044: stur            w0, [x2, #0xb]
    // 0xadd048: ldur            x1, [fp, #-8]
    // 0xadd04c: r0 = controller()
    //     0xadd04c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd050: LoadField: r1 = r0->field_23
    //     0xadd050: ldur            w1, [x0, #0x23]
    // 0xadd054: DecompressPointer r1
    //     0xadd054: add             x1, x1, HEAP, lsl #32
    // 0xadd058: r16 = Sentinel
    //     0xadd058: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadd05c: cmp             w1, w16
    // 0xadd060: b.eq            #0xadd0fc
    // 0xadd064: stur            x1, [fp, #-8]
    // 0xadd068: r0 = TabBarView()
    //     0xadd068: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xadd06c: mov             x1, x0
    // 0xadd070: r0 = const [Instance of 'DoaSearchWrapper', Instance of 'DoaSearchWrapper']
    //     0xadd070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f7a0] List<Widget>(2)
    //     0xadd074: ldr             x0, [x0, #0x7a0]
    // 0xadd078: stur            x1, [fp, #-0x10]
    // 0xadd07c: StoreField: r1->field_f = r0
    //     0xadd07c: stur            w0, [x1, #0xf]
    // 0xadd080: ldur            x0, [fp, #-8]
    // 0xadd084: StoreField: r1->field_b = r0
    //     0xadd084: stur            w0, [x1, #0xb]
    // 0xadd088: r0 = Instance_DragStartBehavior
    //     0xadd088: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadd08c: ArrayStore: r1[0] = r0  ; List_4
    //     0xadd08c: stur            w0, [x1, #0x17]
    // 0xadd090: d0 = 1.000000
    //     0xadd090: fmov            d0, #1.00000000
    // 0xadd094: StoreField: r1->field_1b = d0
    //     0xadd094: stur            d0, [x1, #0x1b]
    // 0xadd098: r2 = Instance_Clip
    //     0xadd098: add             x2, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xadd09c: ldr             x2, [x2, #0x7c0]
    // 0xadd0a0: StoreField: r1->field_23 = r2
    //     0xadd0a0: stur            w2, [x1, #0x23]
    // 0xadd0a4: r0 = Scaffold()
    //     0xadd0a4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xadd0a8: ldur            x1, [fp, #-0x18]
    // 0xadd0ac: StoreField: r0->field_13 = r1
    //     0xadd0ac: stur            w1, [x0, #0x13]
    // 0xadd0b0: ldur            x1, [fp, #-0x10]
    // 0xadd0b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xadd0b4: stur            w1, [x0, #0x17]
    // 0xadd0b8: r1 = Instance_AlignmentDirectional
    //     0xadd0b8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xadd0bc: ldr             x1, [x1, #0x758]
    // 0xadd0c0: StoreField: r0->field_2b = r1
    //     0xadd0c0: stur            w1, [x0, #0x2b]
    // 0xadd0c4: r1 = true
    //     0xadd0c4: add             x1, NULL, #0x20  ; true
    // 0xadd0c8: StoreField: r0->field_53 = r1
    //     0xadd0c8: stur            w1, [x0, #0x53]
    // 0xadd0cc: r2 = Instance_DragStartBehavior
    //     0xadd0cc: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadd0d0: StoreField: r0->field_57 = r2
    //     0xadd0d0: stur            w2, [x0, #0x57]
    // 0xadd0d4: r2 = false
    //     0xadd0d4: add             x2, NULL, #0x30  ; false
    // 0xadd0d8: StoreField: r0->field_b = r2
    //     0xadd0d8: stur            w2, [x0, #0xb]
    // 0xadd0dc: StoreField: r0->field_f = r2
    //     0xadd0dc: stur            w2, [x0, #0xf]
    // 0xadd0e0: StoreField: r0->field_5f = r1
    //     0xadd0e0: stur            w1, [x0, #0x5f]
    // 0xadd0e4: StoreField: r0->field_63 = r1
    //     0xadd0e4: stur            w1, [x0, #0x63]
    // 0xadd0e8: LeaveFrame
    //     0xadd0e8: mov             SP, fp
    //     0xadd0ec: ldp             fp, lr, [SP], #0x10
    // 0xadd0f0: ret
    //     0xadd0f0: ret             
    // 0xadd0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd0f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd0f8: b               #0xadcee0
    // 0xadd0fc: r9 = tabController
    //     0xadd0fc: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f7a8] Field <DoaController.tabController>: late (offset: 0x24)
    //     0xadd100: ldr             x9, [x9, #0x7a8]
    // 0xadd104: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xadd104: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadd108, size: 0x5c
    // 0xadd108: EnterFrame
    //     0xadd108: stp             fp, lr, [SP, #-0x10]!
    //     0xadd10c: mov             fp, SP
    // 0xadd110: AllocStack(0x10)
    //     0xadd110: sub             SP, SP, #0x10
    // 0xadd114: CheckStackOverflow
    //     0xadd114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd118: cmp             SP, x16
    //     0xadd11c: b.ls            #0xadd15c
    // 0xadd120: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadd120: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadd124: ldr             x0, [x0, #0x2670]
    //     0xadd128: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadd12c: cmp             w0, w16
    //     0xadd130: b.ne            #0xadd13c
    //     0xadd134: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadd138: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadd13c: r16 = "/doa/doa-bookmark"
    //     0xadd13c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24e60] "/doa/doa-bookmark"
    //     0xadd140: ldr             x16, [x16, #0xe60]
    // 0xadd144: stp             x16, NULL, [SP]
    // 0xadd148: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadd148: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadd14c: r0 = GetNavigation.toNamed()
    //     0xadd14c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xadd150: LeaveFrame
    //     0xadd150: mov             SP, fp
    //     0xadd154: ldp             fp, lr, [SP], #0x10
    // 0xadd158: ret
    //     0xadd158: ret             
    // 0xadd15c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd15c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd160: b               #0xadd120
  }
  [closure] NSegmentControl <anonymous closure>(dynamic) {
    // ** addr: 0xadd164, size: 0xb8
    // 0xadd164: EnterFrame
    //     0xadd164: stp             fp, lr, [SP, #-0x10]!
    //     0xadd168: mov             fp, SP
    // 0xadd16c: AllocStack(0x18)
    //     0xadd16c: sub             SP, SP, #0x18
    // 0xadd170: SetupParameters()
    //     0xadd170: ldr             x0, [fp, #0x10]
    //     0xadd174: ldur            w2, [x0, #0x17]
    //     0xadd178: add             x2, x2, HEAP, lsl #32
    //     0xadd17c: stur            x2, [fp, #-8]
    // 0xadd180: CheckStackOverflow
    //     0xadd180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd184: cmp             SP, x16
    //     0xadd188: b.ls            #0xadd214
    // 0xadd18c: LoadField: r1 = r2->field_f
    //     0xadd18c: ldur            w1, [x2, #0xf]
    // 0xadd190: DecompressPointer r1
    //     0xadd190: add             x1, x1, HEAP, lsl #32
    // 0xadd194: r0 = controller()
    //     0xadd194: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd198: LoadField: r1 = r0->field_27
    //     0xadd198: ldur            w1, [x0, #0x27]
    // 0xadd19c: DecompressPointer r1
    //     0xadd19c: add             x1, x1, HEAP, lsl #32
    // 0xadd1a0: r0 = value()
    //     0xadd1a0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xadd1a4: mov             x2, x0
    // 0xadd1a8: ldur            x0, [fp, #-8]
    // 0xadd1ac: stur            x2, [fp, #-0x10]
    // 0xadd1b0: LoadField: r1 = r0->field_f
    //     0xadd1b0: ldur            w1, [x0, #0xf]
    // 0xadd1b4: DecompressPointer r1
    //     0xadd1b4: add             x1, x1, HEAP, lsl #32
    // 0xadd1b8: r0 = controller()
    //     0xadd1b8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd1bc: stur            x0, [fp, #-8]
    // 0xadd1c0: r0 = NSegmentControl()
    //     0xadd1c0: bl              #0xad52cc  ; AllocateNSegmentControlStub -> NSegmentControl (size=0x1c)
    // 0xadd1c4: mov             x3, x0
    // 0xadd1c8: r0 = const [Wirid, Doa]
    //     0xadd1c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f7b0] List<String>(2)
    //     0xadd1cc: ldr             x0, [x0, #0x7b0]
    // 0xadd1d0: stur            x3, [fp, #-0x18]
    // 0xadd1d4: StoreField: r3->field_b = r0
    //     0xadd1d4: stur            w0, [x3, #0xb]
    // 0xadd1d8: ldur            x2, [fp, #-8]
    // 0xadd1dc: r1 = Function 'onValueChanged':.
    //     0xadd1dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7b8] AnonymousClosure: (0xadd21c), in [package:nuonline/app/modules/doa/controllers/doa_controller.dart] DoaController::onValueChanged (0xadd258)
    //     0xadd1e0: ldr             x1, [x1, #0x7b8]
    // 0xadd1e4: r0 = AllocateClosure()
    //     0xadd1e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd1e8: mov             x1, x0
    // 0xadd1ec: ldur            x0, [fp, #-0x18]
    // 0xadd1f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xadd1f0: stur            w1, [x0, #0x17]
    // 0xadd1f4: ldur            x1, [fp, #-0x10]
    // 0xadd1f8: r2 = LoadInt32Instr(r1)
    //     0xadd1f8: sbfx            x2, x1, #1, #0x1f
    //     0xadd1fc: tbz             w1, #0, #0xadd204
    //     0xadd200: ldur            x2, [x1, #7]
    // 0xadd204: StoreField: r0->field_f = r2
    //     0xadd204: stur            x2, [x0, #0xf]
    // 0xadd208: LeaveFrame
    //     0xadd208: mov             SP, fp
    //     0xadd20c: ldp             fp, lr, [SP], #0x10
    // 0xadd210: ret
    //     0xadd210: ret             
    // 0xadd214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd218: b               #0xadd18c
  }
}
