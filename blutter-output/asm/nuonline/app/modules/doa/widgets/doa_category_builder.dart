// lib: , url: package:nuonline/app/modules/doa/widgets/doa_category_builder.dart

// class id: 1050189, size: 0x8
class :: {
}

// class id: 5050, size: 0x24, field offset: 0xc
//   const constructor, 
class DoaCategoryBuilder extends StatelessWidget {

  _OneByteString field_c;
  bool field_10;
  bool field_14;
  EdgeInsets field_18;
  _ImmutableList<int> field_1c;

  _ build(/* No info */) {
    // ** addr: 0xb8b5b0, size: 0x108
    // 0xb8b5b0: EnterFrame
    //     0xb8b5b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b5b4: mov             fp, SP
    // 0xb8b5b8: AllocStack(0x30)
    //     0xb8b5b8: sub             SP, SP, #0x30
    // 0xb8b5bc: SetupParameters(DoaCategoryBuilder this /* r1 => r1, fp-0x8 */)
    //     0xb8b5bc: stur            x1, [fp, #-8]
    // 0xb8b5c0: CheckStackOverflow
    //     0xb8b5c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b5c4: cmp             SP, x16
    //     0xb8b5c8: b.ls            #0xb8b6b0
    // 0xb8b5cc: r1 = 1
    //     0xb8b5cc: movz            x1, #0x1
    // 0xb8b5d0: r0 = AllocateContext()
    //     0xb8b5d0: bl              #0xec126c  ; AllocateContextStub
    // 0xb8b5d4: mov             x1, x0
    // 0xb8b5d8: ldur            x0, [fp, #-8]
    // 0xb8b5dc: stur            x1, [fp, #-0x18]
    // 0xb8b5e0: StoreField: r1->field_f = r0
    //     0xb8b5e0: stur            w0, [x1, #0xf]
    // 0xb8b5e4: LoadField: r2 = r0->field_b
    //     0xb8b5e4: ldur            w2, [x0, #0xb]
    // 0xb8b5e8: DecompressPointer r2
    //     0xb8b5e8: add             x2, x2, HEAP, lsl #32
    // 0xb8b5ec: stur            x2, [fp, #-0x10]
    // 0xb8b5f0: r0 = find()
    //     0xb8b5f0: bl              #0x80f380  ; [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::find
    // 0xb8b5f4: stur            x0, [fp, #-0x20]
    // 0xb8b5f8: r0 = find()
    //     0xb8b5f8: bl              #0x80f97c  ; [package:nuonline/app/data/repositories/doa/doa_remote_repository.dart] DoaRemoteRepository::find
    // 0xb8b5fc: mov             x2, x0
    // 0xb8b600: ldur            x0, [fp, #-8]
    // 0xb8b604: stur            x2, [fp, #-0x30]
    // 0xb8b608: LoadField: r3 = r0->field_1b
    //     0xb8b608: ldur            w3, [x0, #0x1b]
    // 0xb8b60c: DecompressPointer r3
    //     0xb8b60c: add             x3, x3, HEAP, lsl #32
    // 0xb8b610: stur            x3, [fp, #-0x28]
    // 0xb8b614: r1 = <List<DoaCategory>>
    //     0xb8b614: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b48] TypeArguments: <List<DoaCategory>>
    //     0xb8b618: ldr             x1, [x1, #0xb48]
    // 0xb8b61c: r0 = DoaCategoryBuilderController()
    //     0xb8b61c: bl              #0xb8b6b8  ; AllocateDoaCategoryBuilderControllerStub -> DoaCategoryBuilderController (size=0x3c)
    // 0xb8b620: mov             x2, x0
    // 0xb8b624: ldur            x0, [fp, #-0x10]
    // 0xb8b628: stur            x2, [fp, #-8]
    // 0xb8b62c: StoreField: r2->field_2b = r0
    //     0xb8b62c: stur            w0, [x2, #0x2b]
    // 0xb8b630: ldur            x1, [fp, #-0x20]
    // 0xb8b634: StoreField: r2->field_2f = r1
    //     0xb8b634: stur            w1, [x2, #0x2f]
    // 0xb8b638: ldur            x1, [fp, #-0x30]
    // 0xb8b63c: StoreField: r2->field_33 = r1
    //     0xb8b63c: stur            w1, [x2, #0x33]
    // 0xb8b640: ldur            x1, [fp, #-0x28]
    // 0xb8b644: StoreField: r2->field_37 = r1
    //     0xb8b644: stur            w1, [x2, #0x37]
    // 0xb8b648: mov             x1, x2
    // 0xb8b64c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb8b64c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb8b650: r1 = <DoaCategoryBuilderController>
    //     0xb8b650: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b50] TypeArguments: <DoaCategoryBuilderController>
    //     0xb8b654: ldr             x1, [x1, #0xb50]
    // 0xb8b658: r0 = GetBuilder()
    //     0xb8b658: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb8b65c: mov             x3, x0
    // 0xb8b660: ldur            x0, [fp, #-8]
    // 0xb8b664: stur            x3, [fp, #-0x20]
    // 0xb8b668: StoreField: r3->field_3b = r0
    //     0xb8b668: stur            w0, [x3, #0x3b]
    // 0xb8b66c: r0 = true
    //     0xb8b66c: add             x0, NULL, #0x20  ; true
    // 0xb8b670: StoreField: r3->field_13 = r0
    //     0xb8b670: stur            w0, [x3, #0x13]
    // 0xb8b674: ldur            x2, [fp, #-0x18]
    // 0xb8b678: r1 = Function '<anonymous closure>':.
    //     0xb8b678: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b58] AnonymousClosure: (0xb8b6c4), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8b67c: ldr             x1, [x1, #0xb58]
    // 0xb8b680: r0 = AllocateClosure()
    //     0xb8b680: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b684: mov             x1, x0
    // 0xb8b688: ldur            x0, [fp, #-0x20]
    // 0xb8b68c: StoreField: r0->field_f = r1
    //     0xb8b68c: stur            w1, [x0, #0xf]
    // 0xb8b690: r1 = false
    //     0xb8b690: add             x1, NULL, #0x30  ; false
    // 0xb8b694: StoreField: r0->field_1f = r1
    //     0xb8b694: stur            w1, [x0, #0x1f]
    // 0xb8b698: StoreField: r0->field_23 = r1
    //     0xb8b698: stur            w1, [x0, #0x23]
    // 0xb8b69c: ldur            x1, [fp, #-0x10]
    // 0xb8b6a0: StoreField: r0->field_1b = r1
    //     0xb8b6a0: stur            w1, [x0, #0x1b]
    // 0xb8b6a4: LeaveFrame
    //     0xb8b6a4: mov             SP, fp
    //     0xb8b6a8: ldp             fp, lr, [SP], #0x10
    // 0xb8b6ac: ret
    //     0xb8b6ac: ret             
    // 0xb8b6b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b6b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b6b4: b               #0xb8b5cc
  }
  [closure] RefreshIndicator <anonymous closure>(dynamic, DoaCategoryBuilderController) {
    // ** addr: 0xb8b6c4, size: 0x178
    // 0xb8b6c4: EnterFrame
    //     0xb8b6c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b6c8: mov             fp, SP
    // 0xb8b6cc: AllocStack(0x48)
    //     0xb8b6cc: sub             SP, SP, #0x48
    // 0xb8b6d0: SetupParameters()
    //     0xb8b6d0: ldr             x0, [fp, #0x18]
    //     0xb8b6d4: ldur            w1, [x0, #0x17]
    //     0xb8b6d8: add             x1, x1, HEAP, lsl #32
    //     0xb8b6dc: stur            x1, [fp, #-8]
    // 0xb8b6e0: CheckStackOverflow
    //     0xb8b6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b6e4: cmp             SP, x16
    //     0xb8b6e8: b.ls            #0xb8b834
    // 0xb8b6ec: r1 = 1
    //     0xb8b6ec: movz            x1, #0x1
    // 0xb8b6f0: r0 = AllocateContext()
    //     0xb8b6f0: bl              #0xec126c  ; AllocateContextStub
    // 0xb8b6f4: mov             x3, x0
    // 0xb8b6f8: ldur            x0, [fp, #-8]
    // 0xb8b6fc: stur            x3, [fp, #-0x10]
    // 0xb8b700: StoreField: r3->field_b = r0
    //     0xb8b700: stur            w0, [x3, #0xb]
    // 0xb8b704: ldr             x0, [fp, #0x10]
    // 0xb8b708: StoreField: r3->field_f = r0
    //     0xb8b708: stur            w0, [x3, #0xf]
    // 0xb8b70c: r1 = Function '<anonymous closure>':.
    //     0xb8b70c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b60] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xb8b710: ldr             x1, [x1, #0xb60]
    // 0xb8b714: r2 = Null
    //     0xb8b714: mov             x2, NULL
    // 0xb8b718: r0 = AllocateClosure()
    //     0xb8b718: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b71c: r1 = Function '<anonymous closure>':.
    //     0xb8b71c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b68] AnonymousClosure: (0xaf40dc), in [package:nuonline/app/modules/tutorial/views/tutorial_view.dart] TutorialView::build (0xb59c7c)
    //     0xb8b720: ldr             x1, [x1, #0xb68]
    // 0xb8b724: r2 = Null
    //     0xb8b724: mov             x2, NULL
    // 0xb8b728: stur            x0, [fp, #-8]
    // 0xb8b72c: r0 = AllocateClosure()
    //     0xb8b72c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b730: stur            x0, [fp, #-0x18]
    // 0xb8b734: r0 = ListView()
    //     0xb8b734: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8b738: stur            x0, [fp, #-0x20]
    // 0xb8b73c: r16 = Instance_EdgeInsets
    //     0xb8b73c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29708] Obj!EdgeInsets@e128b1
    //     0xb8b740: ldr             x16, [x16, #0x708]
    // 0xb8b744: str             x16, [SP]
    // 0xb8b748: mov             x1, x0
    // 0xb8b74c: ldur            x2, [fp, #-0x18]
    // 0xb8b750: ldur            x5, [fp, #-8]
    // 0xb8b754: r3 = 5
    //     0xb8b754: movz            x3, #0x5
    // 0xb8b758: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xb8b758: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xb8b75c: ldr             x4, [x4, #0x700]
    // 0xb8b760: r0 = ListView.separated()
    //     0xb8b760: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8b764: ldur            x2, [fp, #-0x10]
    // 0xb8b768: r1 = Function '<anonymous closure>':.
    //     0xb8b768: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b70] AnonymousClosure: (0xb8b908), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8b76c: ldr             x1, [x1, #0xb70]
    // 0xb8b770: r0 = AllocateClosure()
    //     0xb8b770: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b774: ldur            x2, [fp, #-0x10]
    // 0xb8b778: r1 = Function '<anonymous closure>':.
    //     0xb8b778: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b78] AnonymousClosure: (0xb8b83c), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8b77c: ldr             x1, [x1, #0xb78]
    // 0xb8b780: stur            x0, [fp, #-8]
    // 0xb8b784: r0 = AllocateClosure()
    //     0xb8b784: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b788: r16 = <List<DoaCategory>>
    //     0xb8b788: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b48] TypeArguments: <List<DoaCategory>>
    //     0xb8b78c: ldr             x16, [x16, #0xb48]
    // 0xb8b790: ldr             lr, [fp, #0x10]
    // 0xb8b794: stp             lr, x16, [SP, #0x18]
    // 0xb8b798: ldur            x16, [fp, #-8]
    // 0xb8b79c: ldur            lr, [fp, #-0x20]
    // 0xb8b7a0: stp             lr, x16, [SP, #8]
    // 0xb8b7a4: str             x0, [SP]
    // 0xb8b7a8: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xb8b7a8: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xb8b7ac: ldr             x4, [x4, #0x728]
    // 0xb8b7b0: r0 = StateExt.obx()
    //     0xb8b7b0: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xb8b7b4: stur            x0, [fp, #-8]
    // 0xb8b7b8: r0 = RefreshIndicator()
    //     0xb8b7b8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xb8b7bc: mov             x3, x0
    // 0xb8b7c0: ldur            x0, [fp, #-8]
    // 0xb8b7c4: stur            x3, [fp, #-0x10]
    // 0xb8b7c8: StoreField: r3->field_b = r0
    //     0xb8b7c8: stur            w0, [x3, #0xb]
    // 0xb8b7cc: d0 = 40.000000
    //     0xb8b7cc: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xb8b7d0: StoreField: r3->field_f = d0
    //     0xb8b7d0: stur            d0, [x3, #0xf]
    // 0xb8b7d4: ArrayStore: r3[0] = rZR  ; List_8
    //     0xb8b7d4: stur            xzr, [x3, #0x17]
    // 0xb8b7d8: ldr             x2, [fp, #0x10]
    // 0xb8b7dc: r1 = Function 'onRefresh':.
    //     0xb8b7dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f258] AnonymousClosure: (0xada8f0), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xb8b7e0: ldr             x1, [x1, #0x258]
    // 0xb8b7e4: r0 = AllocateClosure()
    //     0xb8b7e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b7e8: mov             x1, x0
    // 0xb8b7ec: ldur            x0, [fp, #-0x10]
    // 0xb8b7f0: StoreField: r0->field_1f = r1
    //     0xb8b7f0: stur            w1, [x0, #0x1f]
    // 0xb8b7f4: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xb8b7f4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xb8b7f8: ldr             x1, [x1, #0xf58]
    // 0xb8b7fc: StoreField: r0->field_2f = r1
    //     0xb8b7fc: stur            w1, [x0, #0x2f]
    // 0xb8b800: d0 = 2.500000
    //     0xb8b800: fmov            d0, #2.50000000
    // 0xb8b804: StoreField: r0->field_3b = d0
    //     0xb8b804: stur            d0, [x0, #0x3b]
    // 0xb8b808: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xb8b808: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xb8b80c: ldr             x1, [x1, #0xa68]
    // 0xb8b810: StoreField: r0->field_47 = r1
    //     0xb8b810: stur            w1, [x0, #0x47]
    // 0xb8b814: d0 = 2.000000
    //     0xb8b814: fmov            d0, #2.00000000
    // 0xb8b818: StoreField: r0->field_4b = d0
    //     0xb8b818: stur            d0, [x0, #0x4b]
    // 0xb8b81c: r1 = Instance__IndicatorType
    //     0xb8b81c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xb8b820: ldr             x1, [x1, #0xa70]
    // 0xb8b824: StoreField: r0->field_43 = r1
    //     0xb8b824: stur            w1, [x0, #0x43]
    // 0xb8b828: LeaveFrame
    //     0xb8b828: mov             SP, fp
    //     0xb8b82c: ldp             fp, lr, [SP], #0x10
    // 0xb8b830: ret
    //     0xb8b830: ret             
    // 0xb8b834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b834: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b838: b               #0xb8b6ec
  }
  [closure] NEmptyState <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb8b83c, size: 0xcc
    // 0xb8b83c: EnterFrame
    //     0xb8b83c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b840: mov             fp, SP
    // 0xb8b844: AllocStack(0x18)
    //     0xb8b844: sub             SP, SP, #0x18
    // 0xb8b848: SetupParameters()
    //     0xb8b848: ldr             x0, [fp, #0x18]
    //     0xb8b84c: ldur            w1, [x0, #0x17]
    //     0xb8b850: add             x1, x1, HEAP, lsl #32
    //     0xb8b854: stur            x1, [fp, #-8]
    // 0xb8b858: CheckStackOverflow
    //     0xb8b858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b85c: cmp             SP, x16
    //     0xb8b860: b.ls            #0xb8b900
    // 0xb8b864: r16 = "ApiError.notFound"
    //     0xb8b864: add             x16, PP, #0x29, lsl #12  ; [pp+0x29730] "ApiError.notFound"
    //     0xb8b868: ldr             x16, [x16, #0x730]
    // 0xb8b86c: ldr             lr, [fp, #0x10]
    // 0xb8b870: stp             lr, x16, [SP]
    // 0xb8b874: r0 = ==()
    //     0xb8b874: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8b878: tbz             w0, #4, #0xb8b8c4
    // 0xb8b87c: r16 = "ApiError.noConnection"
    //     0xb8b87c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29738] "ApiError.noConnection"
    //     0xb8b880: ldr             x16, [x16, #0x738]
    // 0xb8b884: ldr             lr, [fp, #0x10]
    // 0xb8b888: stp             lr, x16, [SP]
    // 0xb8b88c: r0 = ==()
    //     0xb8b88c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8b890: tbnz            w0, #4, #0xb8b8c4
    // 0xb8b894: ldur            x0, [fp, #-8]
    // 0xb8b898: LoadField: r2 = r0->field_f
    //     0xb8b898: ldur            w2, [x0, #0xf]
    // 0xb8b89c: DecompressPointer r2
    //     0xb8b89c: add             x2, x2, HEAP, lsl #32
    // 0xb8b8a0: r1 = Function 'onRefresh':.
    //     0xb8b8a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f258] AnonymousClosure: (0xada8f0), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xb8b8a4: ldr             x1, [x1, #0x258]
    // 0xb8b8a8: r0 = AllocateClosure()
    //     0xb8b8a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b8ac: mov             x2, x0
    // 0xb8b8b0: r1 = Null
    //     0xb8b8b0: mov             x1, NULL
    // 0xb8b8b4: r0 = NEmptyState.notConnection()
    //     0xb8b8b4: bl              #0xad9f3c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.notConnection
    // 0xb8b8b8: LeaveFrame
    //     0xb8b8b8: mov             SP, fp
    //     0xb8b8bc: ldp             fp, lr, [SP], #0x10
    // 0xb8b8c0: ret
    //     0xb8b8c0: ret             
    // 0xb8b8c4: r0 = NEmptyState()
    //     0xb8b8c4: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xb8b8c8: mov             x1, x0
    // 0xb8b8cc: r2 = "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xb8b8cc: add             x2, PP, #0x29, lsl #12  ; [pp+0x297e8] "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xb8b8d0: ldr             x2, [x2, #0x7e8]
    // 0xb8b8d4: r3 = "assets/images/illustration/no_search.svg"
    //     0xb8b8d4: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xb8b8d8: ldr             x3, [x3, #0x138]
    // 0xb8b8dc: r5 = "Halaman Tidak Ditemukan"
    //     0xb8b8dc: add             x5, PP, #0x29, lsl #12  ; [pp+0x292c8] "Halaman Tidak Ditemukan"
    //     0xb8b8e0: ldr             x5, [x5, #0x2c8]
    // 0xb8b8e4: stur            x0, [fp, #-8]
    // 0xb8b8e8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb8b8e8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb8b8ec: r0 = NEmptyState.svg()
    //     0xb8b8ec: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xb8b8f0: ldur            x0, [fp, #-8]
    // 0xb8b8f4: LeaveFrame
    //     0xb8b8f4: mov             SP, fp
    //     0xb8b8f8: ldp             fp, lr, [SP], #0x10
    // 0xb8b8fc: ret
    //     0xb8b8fc: ret             
    // 0xb8b900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8b900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8b904: b               #0xb8b864
  }
  [closure] ListView <anonymous closure>(dynamic, List<DoaCategory>?) {
    // ** addr: 0xb8b908, size: 0x114
    // 0xb8b908: EnterFrame
    //     0xb8b908: stp             fp, lr, [SP, #-0x10]!
    //     0xb8b90c: mov             fp, SP
    // 0xb8b910: AllocStack(0x30)
    //     0xb8b910: sub             SP, SP, #0x30
    // 0xb8b914: SetupParameters()
    //     0xb8b914: ldr             x0, [fp, #0x18]
    //     0xb8b918: ldur            w1, [x0, #0x17]
    //     0xb8b91c: add             x1, x1, HEAP, lsl #32
    //     0xb8b920: stur            x1, [fp, #-8]
    // 0xb8b924: CheckStackOverflow
    //     0xb8b924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8b928: cmp             SP, x16
    //     0xb8b92c: b.ls            #0xb8ba10
    // 0xb8b930: r1 = 1
    //     0xb8b930: movz            x1, #0x1
    // 0xb8b934: r0 = AllocateContext()
    //     0xb8b934: bl              #0xec126c  ; AllocateContextStub
    // 0xb8b938: mov             x2, x0
    // 0xb8b93c: ldur            x1, [fp, #-8]
    // 0xb8b940: stur            x2, [fp, #-0x10]
    // 0xb8b944: StoreField: r2->field_b = r1
    //     0xb8b944: stur            w1, [x2, #0xb]
    // 0xb8b948: ldr             x0, [fp, #0x10]
    // 0xb8b94c: StoreField: r2->field_f = r0
    //     0xb8b94c: stur            w0, [x2, #0xf]
    // 0xb8b950: cmp             w0, NULL
    // 0xb8b954: b.eq            #0xb8ba18
    // 0xb8b958: r3 = LoadClassIdInstr(r0)
    //     0xb8b958: ldur            x3, [x0, #-1]
    //     0xb8b95c: ubfx            x3, x3, #0xc, #0x14
    // 0xb8b960: str             x0, [SP]
    // 0xb8b964: mov             x0, x3
    // 0xb8b968: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb8b968: movz            x17, #0xc834
    //     0xb8b96c: add             lr, x0, x17
    //     0xb8b970: ldr             lr, [x21, lr, lsl #3]
    //     0xb8b974: blr             lr
    // 0xb8b978: r1 = LoadInt32Instr(r0)
    //     0xb8b978: sbfx            x1, x0, #1, #0x1f
    //     0xb8b97c: tbz             w0, #0, #0xb8b984
    //     0xb8b980: ldur            x1, [x0, #7]
    // 0xb8b984: add             x3, x1, #2
    // 0xb8b988: ldur            x0, [fp, #-8]
    // 0xb8b98c: stur            x3, [fp, #-0x18]
    // 0xb8b990: LoadField: r1 = r0->field_b
    //     0xb8b990: ldur            w1, [x0, #0xb]
    // 0xb8b994: DecompressPointer r1
    //     0xb8b994: add             x1, x1, HEAP, lsl #32
    // 0xb8b998: LoadField: r0 = r1->field_f
    //     0xb8b998: ldur            w0, [x1, #0xf]
    // 0xb8b99c: DecompressPointer r0
    //     0xb8b99c: add             x0, x0, HEAP, lsl #32
    // 0xb8b9a0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb8b9a0: ldur            w4, [x0, #0x17]
    // 0xb8b9a4: DecompressPointer r4
    //     0xb8b9a4: add             x4, x4, HEAP, lsl #32
    // 0xb8b9a8: stur            x4, [fp, #-8]
    // 0xb8b9ac: r1 = Function '<anonymous closure>':.
    //     0xb8b9ac: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b80] AnonymousClosure: (0xaf3ca0), in [package:nuonline/app/modules/tutorial/views/tutorial_view.dart] TutorialView::build (0xb59c7c)
    //     0xb8b9b0: ldr             x1, [x1, #0xb80]
    // 0xb8b9b4: r2 = Null
    //     0xb8b9b4: mov             x2, NULL
    // 0xb8b9b8: r0 = AllocateClosure()
    //     0xb8b9b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b9bc: ldur            x2, [fp, #-0x10]
    // 0xb8b9c0: r1 = Function '<anonymous closure>':.
    //     0xb8b9c0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b88] AnonymousClosure: (0xb8ba1c), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8b9c4: ldr             x1, [x1, #0xb88]
    // 0xb8b9c8: stur            x0, [fp, #-0x10]
    // 0xb8b9cc: r0 = AllocateClosure()
    //     0xb8b9cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8b9d0: stur            x0, [fp, #-0x20]
    // 0xb8b9d4: r0 = ListView()
    //     0xb8b9d4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8b9d8: stur            x0, [fp, #-0x28]
    // 0xb8b9dc: ldur            x16, [fp, #-8]
    // 0xb8b9e0: str             x16, [SP]
    // 0xb8b9e4: mov             x1, x0
    // 0xb8b9e8: ldur            x2, [fp, #-0x20]
    // 0xb8b9ec: ldur            x3, [fp, #-0x18]
    // 0xb8b9f0: ldur            x5, [fp, #-0x10]
    // 0xb8b9f4: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xb8b9f4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xb8b9f8: ldr             x4, [x4, #0x700]
    // 0xb8b9fc: r0 = ListView.separated()
    //     0xb8b9fc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8ba00: ldur            x0, [fp, #-0x28]
    // 0xb8ba04: LeaveFrame
    //     0xb8ba04: mov             SP, fp
    //     0xb8ba08: ldp             fp, lr, [SP], #0x10
    // 0xb8ba0c: ret
    //     0xb8ba0c: ret             
    // 0xb8ba10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ba10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8ba14: b               #0xb8b930
    // 0xb8ba18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8ba18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb8ba1c, size: 0x358
    // 0xb8ba1c: EnterFrame
    //     0xb8ba1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8ba20: mov             fp, SP
    // 0xb8ba24: AllocStack(0x48)
    //     0xb8ba24: sub             SP, SP, #0x48
    // 0xb8ba28: SetupParameters()
    //     0xb8ba28: ldr             x0, [fp, #0x20]
    //     0xb8ba2c: ldur            w1, [x0, #0x17]
    //     0xb8ba30: add             x1, x1, HEAP, lsl #32
    //     0xb8ba34: stur            x1, [fp, #-8]
    // 0xb8ba38: CheckStackOverflow
    //     0xb8ba38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ba3c: cmp             SP, x16
    //     0xb8ba40: b.ls            #0xb8bd6c
    // 0xb8ba44: r1 = 1
    //     0xb8ba44: movz            x1, #0x1
    // 0xb8ba48: r0 = AllocateContext()
    //     0xb8ba48: bl              #0xec126c  ; AllocateContextStub
    // 0xb8ba4c: mov             x2, x0
    // 0xb8ba50: ldur            x1, [fp, #-8]
    // 0xb8ba54: stur            x2, [fp, #-0x10]
    // 0xb8ba58: StoreField: r2->field_b = r1
    //     0xb8ba58: stur            w1, [x2, #0xb]
    // 0xb8ba5c: ldr             x3, [fp, #0x10]
    // 0xb8ba60: StoreField: r2->field_f = r3
    //     0xb8ba60: stur            w3, [x2, #0xf]
    // 0xb8ba64: LoadField: r0 = r1->field_f
    //     0xb8ba64: ldur            w0, [x1, #0xf]
    // 0xb8ba68: DecompressPointer r0
    //     0xb8ba68: add             x0, x0, HEAP, lsl #32
    // 0xb8ba6c: r4 = LoadClassIdInstr(r0)
    //     0xb8ba6c: ldur            x4, [x0, #-1]
    //     0xb8ba70: ubfx            x4, x4, #0xc, #0x14
    // 0xb8ba74: str             x0, [SP]
    // 0xb8ba78: mov             x0, x4
    // 0xb8ba7c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb8ba7c: movz            x17, #0xc834
    //     0xb8ba80: add             lr, x0, x17
    //     0xb8ba84: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ba88: blr             lr
    // 0xb8ba8c: ldr             x1, [fp, #0x10]
    // 0xb8ba90: r2 = LoadInt32Instr(r1)
    //     0xb8ba90: sbfx            x2, x1, #1, #0x1f
    //     0xb8ba94: tbz             w1, #0, #0xb8ba9c
    //     0xb8ba98: ldur            x2, [x1, #7]
    // 0xb8ba9c: stur            x2, [fp, #-0x28]
    // 0xb8baa0: r3 = LoadInt32Instr(r0)
    //     0xb8baa0: sbfx            x3, x0, #1, #0x1f
    //     0xb8baa4: tbz             w0, #0, #0xb8baac
    //     0xb8baa8: ldur            x3, [x0, #7]
    // 0xb8baac: cmp             x2, x3
    // 0xb8bab0: b.ne            #0xb8bbb0
    // 0xb8bab4: ldur            x3, [fp, #-8]
    // 0xb8bab8: LoadField: r0 = r3->field_b
    //     0xb8bab8: ldur            w0, [x3, #0xb]
    // 0xb8babc: DecompressPointer r0
    //     0xb8babc: add             x0, x0, HEAP, lsl #32
    // 0xb8bac0: LoadField: r1 = r0->field_b
    //     0xb8bac0: ldur            w1, [x0, #0xb]
    // 0xb8bac4: DecompressPointer r1
    //     0xb8bac4: add             x1, x1, HEAP, lsl #32
    // 0xb8bac8: LoadField: r0 = r1->field_f
    //     0xb8bac8: ldur            w0, [x1, #0xf]
    // 0xb8bacc: DecompressPointer r0
    //     0xb8bacc: add             x0, x0, HEAP, lsl #32
    // 0xb8bad0: stur            x0, [fp, #-0x18]
    // 0xb8bad4: LoadField: r1 = r0->field_f
    //     0xb8bad4: ldur            w1, [x0, #0xf]
    // 0xb8bad8: DecompressPointer r1
    //     0xb8bad8: add             x1, x1, HEAP, lsl #32
    // 0xb8badc: tbz             w1, #4, #0xb8baf4
    // 0xb8bae0: r0 = Instance_SizedBox
    //     0xb8bae0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb8bae4: ldr             x0, [x0, #0xc40]
    // 0xb8bae8: LeaveFrame
    //     0xb8bae8: mov             SP, fp
    //     0xb8baec: ldp             fp, lr, [SP], #0x10
    // 0xb8baf0: ret
    //     0xb8baf0: ret             
    // 0xb8baf4: r1 = Null
    //     0xb8baf4: mov             x1, NULL
    // 0xb8baf8: r2 = 6
    //     0xb8baf8: movz            x2, #0x6
    // 0xb8bafc: r0 = AllocateArray()
    //     0xb8bafc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8bb00: stur            x0, [fp, #-0x20]
    // 0xb8bb04: r16 = "Artikel "
    //     0xb8bb04: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xb8bb08: ldr             x16, [x16, #0xb90]
    // 0xb8bb0c: StoreField: r0->field_f = r16
    //     0xb8bb0c: stur            w16, [x0, #0xf]
    // 0xb8bb10: ldur            x1, [fp, #-0x18]
    // 0xb8bb14: LoadField: r2 = r1->field_b
    //     0xb8bb14: ldur            w2, [x1, #0xb]
    // 0xb8bb18: DecompressPointer r2
    //     0xb8bb18: add             x2, x2, HEAP, lsl #32
    // 0xb8bb1c: mov             x1, x2
    // 0xb8bb20: r0 = capitalize()
    //     0xb8bb20: bl              #0xb6c9f0  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::capitalize
    // 0xb8bb24: ldur            x1, [fp, #-0x20]
    // 0xb8bb28: ArrayStore: r1[1] = r0  ; List_4
    //     0xb8bb28: add             x25, x1, #0x13
    //     0xb8bb2c: str             w0, [x25]
    //     0xb8bb30: tbz             w0, #0, #0xb8bb4c
    //     0xb8bb34: ldurb           w16, [x1, #-1]
    //     0xb8bb38: ldurb           w17, [x0, #-1]
    //     0xb8bb3c: and             x16, x17, x16, lsr #2
    //     0xb8bb40: tst             x16, HEAP, lsr #32
    //     0xb8bb44: b.eq            #0xb8bb4c
    //     0xb8bb48: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8bb4c: ldur            x0, [fp, #-0x20]
    // 0xb8bb50: r16 = " Lainnya"
    //     0xb8bb50: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b98] " Lainnya"
    //     0xb8bb54: ldr             x16, [x16, #0xb98]
    // 0xb8bb58: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8bb58: stur            w16, [x0, #0x17]
    // 0xb8bb5c: str             x0, [SP]
    // 0xb8bb60: r0 = _interpolate()
    //     0xb8bb60: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb8bb64: stur            x0, [fp, #-0x18]
    // 0xb8bb68: r0 = NCardListHeader()
    //     0xb8bb68: bl              #0xadee68  ; AllocateNCardListHeaderStub -> NCardListHeader (size=0x2c)
    // 0xb8bb6c: mov             x3, x0
    // 0xb8bb70: ldur            x0, [fp, #-0x18]
    // 0xb8bb74: stur            x3, [fp, #-0x20]
    // 0xb8bb78: StoreField: r3->field_f = r0
    //     0xb8bb78: stur            w0, [x3, #0xf]
    // 0xb8bb7c: ldur            x2, [fp, #-0x10]
    // 0xb8bb80: r1 = Function '<anonymous closure>':.
    //     0xb8bb80: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ba0] AnonymousClosure: (0xb8bf78), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8bb84: ldr             x1, [x1, #0xba0]
    // 0xb8bb88: r0 = AllocateClosure()
    //     0xb8bb88: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8bb8c: mov             x1, x0
    // 0xb8bb90: ldur            x0, [fp, #-0x20]
    // 0xb8bb94: StoreField: r0->field_1b = r1
    //     0xb8bb94: stur            w1, [x0, #0x1b]
    // 0xb8bb98: r4 = false
    //     0xb8bb98: add             x4, NULL, #0x30  ; false
    // 0xb8bb9c: StoreField: r0->field_1f = r4
    //     0xb8bb9c: stur            w4, [x0, #0x1f]
    // 0xb8bba0: StoreField: r0->field_23 = r4
    //     0xb8bba0: stur            w4, [x0, #0x23]
    // 0xb8bba4: LeaveFrame
    //     0xb8bba4: mov             SP, fp
    //     0xb8bba8: ldp             fp, lr, [SP], #0x10
    // 0xb8bbac: ret
    //     0xb8bbac: ret             
    // 0xb8bbb0: ldur            x3, [fp, #-8]
    // 0xb8bbb4: r4 = false
    //     0xb8bbb4: add             x4, NULL, #0x30  ; false
    // 0xb8bbb8: LoadField: r0 = r3->field_f
    //     0xb8bbb8: ldur            w0, [x3, #0xf]
    // 0xb8bbbc: DecompressPointer r0
    //     0xb8bbbc: add             x0, x0, HEAP, lsl #32
    // 0xb8bbc0: r5 = LoadClassIdInstr(r0)
    //     0xb8bbc0: ldur            x5, [x0, #-1]
    //     0xb8bbc4: ubfx            x5, x5, #0xc, #0x14
    // 0xb8bbc8: str             x0, [SP]
    // 0xb8bbcc: mov             x0, x5
    // 0xb8bbd0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb8bbd0: movz            x17, #0xc834
    //     0xb8bbd4: add             lr, x0, x17
    //     0xb8bbd8: ldr             lr, [x21, lr, lsl #3]
    //     0xb8bbdc: blr             lr
    // 0xb8bbe0: r1 = LoadInt32Instr(r0)
    //     0xb8bbe0: sbfx            x1, x0, #1, #0x1f
    //     0xb8bbe4: tbz             w0, #0, #0xb8bbec
    //     0xb8bbe8: ldur            x1, [x0, #7]
    // 0xb8bbec: add             x0, x1, #1
    // 0xb8bbf0: ldur            x1, [fp, #-0x28]
    // 0xb8bbf4: cmp             x1, x0
    // 0xb8bbf8: b.ne            #0xb8bc10
    // 0xb8bbfc: r0 = Instance_DonationSupportButton
    //     0xb8bbfc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ba8] Obj!DonationSupportButton@e1fa21
    //     0xb8bc00: ldr             x0, [x0, #0xba8]
    // 0xb8bc04: LeaveFrame
    //     0xb8bc04: mov             SP, fp
    //     0xb8bc08: ldp             fp, lr, [SP], #0x10
    // 0xb8bc0c: ret
    //     0xb8bc0c: ret             
    // 0xb8bc10: ldur            x2, [fp, #-8]
    // 0xb8bc14: add             x3, x1, #1
    // 0xb8bc18: stur            x3, [fp, #-0x30]
    // 0xb8bc1c: LoadField: r0 = r2->field_f
    //     0xb8bc1c: ldur            w0, [x2, #0xf]
    // 0xb8bc20: DecompressPointer r0
    //     0xb8bc20: add             x0, x0, HEAP, lsl #32
    // 0xb8bc24: r1 = LoadClassIdInstr(r0)
    //     0xb8bc24: ldur            x1, [x0, #-1]
    //     0xb8bc28: ubfx            x1, x1, #0xc, #0x14
    // 0xb8bc2c: ldr             x16, [fp, #0x10]
    // 0xb8bc30: stp             x16, x0, [SP]
    // 0xb8bc34: mov             x0, x1
    // 0xb8bc38: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8bc38: movz            x17, #0x3037
    //     0xb8bc3c: movk            x17, #0x1, lsl #16
    //     0xb8bc40: add             lr, x0, x17
    //     0xb8bc44: ldr             lr, [x21, lr, lsl #3]
    //     0xb8bc48: blr             lr
    // 0xb8bc4c: LoadField: r1 = r0->field_f
    //     0xb8bc4c: ldur            w1, [x0, #0xf]
    // 0xb8bc50: DecompressPointer r1
    //     0xb8bc50: add             x1, x1, HEAP, lsl #32
    // 0xb8bc54: ldur            x0, [fp, #-8]
    // 0xb8bc58: stur            x1, [fp, #-0x18]
    // 0xb8bc5c: LoadField: r2 = r0->field_f
    //     0xb8bc5c: ldur            w2, [x0, #0xf]
    // 0xb8bc60: DecompressPointer r2
    //     0xb8bc60: add             x2, x2, HEAP, lsl #32
    // 0xb8bc64: r0 = LoadClassIdInstr(r2)
    //     0xb8bc64: ldur            x0, [x2, #-1]
    //     0xb8bc68: ubfx            x0, x0, #0xc, #0x14
    // 0xb8bc6c: ldr             x16, [fp, #0x10]
    // 0xb8bc70: stp             x16, x2, [SP]
    // 0xb8bc74: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8bc74: movz            x17, #0x3037
    //     0xb8bc78: movk            x17, #0x1, lsl #16
    //     0xb8bc7c: add             lr, x0, x17
    //     0xb8bc80: ldr             lr, [x21, lr, lsl #3]
    //     0xb8bc84: blr             lr
    // 0xb8bc88: LoadField: r2 = r0->field_13
    //     0xb8bc88: ldur            x2, [x0, #0x13]
    // 0xb8bc8c: r0 = BoxInt64Instr(r2)
    //     0xb8bc8c: sbfiz           x0, x2, #1, #0x1f
    //     0xb8bc90: cmp             x2, x0, asr #1
    //     0xb8bc94: b.eq            #0xb8bca0
    //     0xb8bc98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8bc9c: stur            x2, [x0, #7]
    // 0xb8bca0: r1 = Null
    //     0xb8bca0: mov             x1, NULL
    // 0xb8bca4: r2 = 4
    //     0xb8bca4: movz            x2, #0x4
    // 0xb8bca8: stur            x0, [fp, #-8]
    // 0xb8bcac: r0 = AllocateArray()
    //     0xb8bcac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8bcb0: mov             x1, x0
    // 0xb8bcb4: ldur            x0, [fp, #-8]
    // 0xb8bcb8: StoreField: r1->field_f = r0
    //     0xb8bcb8: stur            w0, [x1, #0xf]
    // 0xb8bcbc: r16 = " Bacaan"
    //     0xb8bcbc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bb0] " Bacaan"
    //     0xb8bcc0: ldr             x16, [x16, #0xbb0]
    // 0xb8bcc4: StoreField: r1->field_13 = r16
    //     0xb8bcc4: stur            w16, [x1, #0x13]
    // 0xb8bcc8: str             x1, [SP]
    // 0xb8bccc: r0 = _interpolate()
    //     0xb8bccc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb8bcd0: mov             x3, x0
    // 0xb8bcd4: ldur            x2, [fp, #-0x30]
    // 0xb8bcd8: stur            x3, [fp, #-0x20]
    // 0xb8bcdc: r0 = BoxInt64Instr(r2)
    //     0xb8bcdc: sbfiz           x0, x2, #1, #0x1f
    //     0xb8bce0: cmp             x2, x0, asr #1
    //     0xb8bce4: b.eq            #0xb8bcf0
    //     0xb8bce8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8bcec: stur            x2, [x0, #7]
    // 0xb8bcf0: stur            x0, [fp, #-8]
    // 0xb8bcf4: r0 = NCardListNumber()
    //     0xb8bcf4: bl              #0xaf3b14  ; AllocateNCardListNumberStub -> NCardListNumber (size=0x30)
    // 0xb8bcf8: mov             x3, x0
    // 0xb8bcfc: ldur            x0, [fp, #-8]
    // 0xb8bd00: stur            x3, [fp, #-0x38]
    // 0xb8bd04: StoreField: r3->field_b = r0
    //     0xb8bd04: stur            w0, [x3, #0xb]
    // 0xb8bd08: ldur            x0, [fp, #-0x18]
    // 0xb8bd0c: StoreField: r3->field_f = r0
    //     0xb8bd0c: stur            w0, [x3, #0xf]
    // 0xb8bd10: ldur            x0, [fp, #-0x20]
    // 0xb8bd14: StoreField: r3->field_13 = r0
    //     0xb8bd14: stur            w0, [x3, #0x13]
    // 0xb8bd18: r0 = false
    //     0xb8bd18: add             x0, NULL, #0x30  ; false
    // 0xb8bd1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb8bd1c: stur            w0, [x3, #0x17]
    // 0xb8bd20: ldur            x2, [fp, #-0x10]
    // 0xb8bd24: r1 = Function '<anonymous closure>':.
    //     0xb8bd24: add             x1, PP, #0x35, lsl #12  ; [pp+0x35bb8] AnonymousClosure: (0xb8bd74), in [package:nuonline/app/modules/doa/widgets/doa_category_builder.dart] DoaCategoryBuilder::build (0xb8b5b0)
    //     0xb8bd28: ldr             x1, [x1, #0xbb8]
    // 0xb8bd2c: r0 = AllocateClosure()
    //     0xb8bd2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8bd30: mov             x1, x0
    // 0xb8bd34: ldur            x0, [fp, #-0x38]
    // 0xb8bd38: StoreField: r0->field_1b = r1
    //     0xb8bd38: stur            w1, [x0, #0x1b]
    // 0xb8bd3c: r1 = false
    //     0xb8bd3c: add             x1, NULL, #0x30  ; false
    // 0xb8bd40: StoreField: r0->field_1f = r1
    //     0xb8bd40: stur            w1, [x0, #0x1f]
    // 0xb8bd44: r0 = ListTileTheme()
    //     0xb8bd44: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xb8bd48: r1 = Instance_EdgeInsets
    //     0xb8bd48: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb8bd4c: StoreField: r0->field_2b = r1
    //     0xb8bd4c: stur            w1, [x0, #0x2b]
    // 0xb8bd50: r1 = 0.000000
    //     0xb8bd50: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb8bd54: StoreField: r0->field_3b = r1
    //     0xb8bd54: stur            w1, [x0, #0x3b]
    // 0xb8bd58: ldur            x1, [fp, #-0x38]
    // 0xb8bd5c: StoreField: r0->field_b = r1
    //     0xb8bd5c: stur            w1, [x0, #0xb]
    // 0xb8bd60: LeaveFrame
    //     0xb8bd60: mov             SP, fp
    //     0xb8bd64: ldp             fp, lr, [SP], #0x10
    // 0xb8bd68: ret
    //     0xb8bd68: ret             
    // 0xb8bd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8bd6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8bd70: b               #0xb8ba44
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8bd74, size: 0x204
    // 0xb8bd74: EnterFrame
    //     0xb8bd74: stp             fp, lr, [SP, #-0x10]!
    //     0xb8bd78: mov             fp, SP
    // 0xb8bd7c: AllocStack(0x38)
    //     0xb8bd7c: sub             SP, SP, #0x38
    // 0xb8bd80: SetupParameters()
    //     0xb8bd80: ldr             x0, [fp, #0x10]
    //     0xb8bd84: ldur            w1, [x0, #0x17]
    //     0xb8bd88: add             x1, x1, HEAP, lsl #32
    //     0xb8bd8c: stur            x1, [fp, #-8]
    // 0xb8bd90: CheckStackOverflow
    //     0xb8bd90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8bd94: cmp             SP, x16
    //     0xb8bd98: b.ls            #0xb8bf70
    // 0xb8bd9c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8bd9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8bda0: ldr             x0, [x0, #0x2670]
    //     0xb8bda4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8bda8: cmp             w0, w16
    //     0xb8bdac: b.ne            #0xb8bdb8
    //     0xb8bdb0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8bdb4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8bdb8: r1 = Null
    //     0xb8bdb8: mov             x1, NULL
    // 0xb8bdbc: r2 = 12
    //     0xb8bdbc: movz            x2, #0xc
    // 0xb8bdc0: r0 = AllocateArray()
    //     0xb8bdc0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8bdc4: mov             x1, x0
    // 0xb8bdc8: stur            x1, [fp, #-0x18]
    // 0xb8bdcc: r16 = "id"
    //     0xb8bdcc: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8bdd0: ldr             x16, [x16, #0x740]
    // 0xb8bdd4: StoreField: r1->field_f = r16
    //     0xb8bdd4: stur            w16, [x1, #0xf]
    // 0xb8bdd8: ldur            x2, [fp, #-8]
    // 0xb8bddc: LoadField: r3 = r2->field_b
    //     0xb8bddc: ldur            w3, [x2, #0xb]
    // 0xb8bde0: DecompressPointer r3
    //     0xb8bde0: add             x3, x3, HEAP, lsl #32
    // 0xb8bde4: stur            x3, [fp, #-0x10]
    // 0xb8bde8: LoadField: r0 = r3->field_f
    //     0xb8bde8: ldur            w0, [x3, #0xf]
    // 0xb8bdec: DecompressPointer r0
    //     0xb8bdec: add             x0, x0, HEAP, lsl #32
    // 0xb8bdf0: LoadField: r4 = r2->field_f
    //     0xb8bdf0: ldur            w4, [x2, #0xf]
    // 0xb8bdf4: DecompressPointer r4
    //     0xb8bdf4: add             x4, x4, HEAP, lsl #32
    // 0xb8bdf8: r5 = LoadClassIdInstr(r0)
    //     0xb8bdf8: ldur            x5, [x0, #-1]
    //     0xb8bdfc: ubfx            x5, x5, #0xc, #0x14
    // 0xb8be00: stp             x4, x0, [SP]
    // 0xb8be04: mov             x0, x5
    // 0xb8be08: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8be08: movz            x17, #0x3037
    //     0xb8be0c: movk            x17, #0x1, lsl #16
    //     0xb8be10: add             lr, x0, x17
    //     0xb8be14: ldr             lr, [x21, lr, lsl #3]
    //     0xb8be18: blr             lr
    // 0xb8be1c: LoadField: r2 = r0->field_7
    //     0xb8be1c: ldur            x2, [x0, #7]
    // 0xb8be20: r0 = BoxInt64Instr(r2)
    //     0xb8be20: sbfiz           x0, x2, #1, #0x1f
    //     0xb8be24: cmp             x2, x0, asr #1
    //     0xb8be28: b.eq            #0xb8be34
    //     0xb8be2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8be30: stur            x2, [x0, #7]
    // 0xb8be34: ldur            x1, [fp, #-0x18]
    // 0xb8be38: ArrayStore: r1[1] = r0  ; List_4
    //     0xb8be38: add             x25, x1, #0x13
    //     0xb8be3c: str             w0, [x25]
    //     0xb8be40: tbz             w0, #0, #0xb8be5c
    //     0xb8be44: ldurb           w16, [x1, #-1]
    //     0xb8be48: ldurb           w17, [x0, #-1]
    //     0xb8be4c: and             x16, x17, x16, lsr #2
    //     0xb8be50: tst             x16, HEAP, lsr #32
    //     0xb8be54: b.eq            #0xb8be5c
    //     0xb8be58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8be5c: ldur            x1, [fp, #-0x18]
    // 0xb8be60: r16 = "title"
    //     0xb8be60: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xb8be64: ldr             x16, [x16, #0x748]
    // 0xb8be68: ArrayStore: r1[0] = r16  ; List_4
    //     0xb8be68: stur            w16, [x1, #0x17]
    // 0xb8be6c: ldur            x0, [fp, #-0x10]
    // 0xb8be70: LoadField: r2 = r0->field_b
    //     0xb8be70: ldur            w2, [x0, #0xb]
    // 0xb8be74: DecompressPointer r2
    //     0xb8be74: add             x2, x2, HEAP, lsl #32
    // 0xb8be78: LoadField: r3 = r2->field_b
    //     0xb8be78: ldur            w3, [x2, #0xb]
    // 0xb8be7c: DecompressPointer r3
    //     0xb8be7c: add             x3, x3, HEAP, lsl #32
    // 0xb8be80: stur            x3, [fp, #-0x20]
    // 0xb8be84: LoadField: r2 = r3->field_f
    //     0xb8be84: ldur            w2, [x3, #0xf]
    // 0xb8be88: DecompressPointer r2
    //     0xb8be88: add             x2, x2, HEAP, lsl #32
    // 0xb8be8c: LoadField: r4 = r2->field_1f
    //     0xb8be8c: ldur            w4, [x2, #0x1f]
    // 0xb8be90: DecompressPointer r4
    //     0xb8be90: add             x4, x4, HEAP, lsl #32
    // 0xb8be94: cmp             w4, NULL
    // 0xb8be98: b.ne            #0xb8bee4
    // 0xb8be9c: ldur            x2, [fp, #-8]
    // 0xb8bea0: LoadField: r4 = r0->field_f
    //     0xb8bea0: ldur            w4, [x0, #0xf]
    // 0xb8bea4: DecompressPointer r4
    //     0xb8bea4: add             x4, x4, HEAP, lsl #32
    // 0xb8bea8: LoadField: r0 = r2->field_f
    //     0xb8bea8: ldur            w0, [x2, #0xf]
    // 0xb8beac: DecompressPointer r0
    //     0xb8beac: add             x0, x0, HEAP, lsl #32
    // 0xb8beb0: r2 = LoadClassIdInstr(r4)
    //     0xb8beb0: ldur            x2, [x4, #-1]
    //     0xb8beb4: ubfx            x2, x2, #0xc, #0x14
    // 0xb8beb8: stp             x0, x4, [SP]
    // 0xb8bebc: mov             x0, x2
    // 0xb8bec0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8bec0: movz            x17, #0x3037
    //     0xb8bec4: movk            x17, #0x1, lsl #16
    //     0xb8bec8: add             lr, x0, x17
    //     0xb8becc: ldr             lr, [x21, lr, lsl #3]
    //     0xb8bed0: blr             lr
    // 0xb8bed4: LoadField: r1 = r0->field_f
    //     0xb8bed4: ldur            w1, [x0, #0xf]
    // 0xb8bed8: DecompressPointer r1
    //     0xb8bed8: add             x1, x1, HEAP, lsl #32
    // 0xb8bedc: mov             x0, x1
    // 0xb8bee0: b               #0xb8bee8
    // 0xb8bee4: mov             x0, x4
    // 0xb8bee8: ldur            x2, [fp, #-0x18]
    // 0xb8beec: ldur            x3, [fp, #-0x20]
    // 0xb8bef0: mov             x1, x2
    // 0xb8bef4: ArrayStore: r1[3] = r0  ; List_4
    //     0xb8bef4: add             x25, x1, #0x1b
    //     0xb8bef8: str             w0, [x25]
    //     0xb8befc: tbz             w0, #0, #0xb8bf18
    //     0xb8bf00: ldurb           w16, [x1, #-1]
    //     0xb8bf04: ldurb           w17, [x0, #-1]
    //     0xb8bf08: and             x16, x17, x16, lsr #2
    //     0xb8bf0c: tst             x16, HEAP, lsr #32
    //     0xb8bf10: b.eq            #0xb8bf18
    //     0xb8bf14: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8bf18: r16 = "showBookmarkOnDetail"
    //     0xb8bf18: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bc0] "showBookmarkOnDetail"
    //     0xb8bf1c: ldr             x16, [x16, #0xbc0]
    // 0xb8bf20: StoreField: r2->field_1f = r16
    //     0xb8bf20: stur            w16, [x2, #0x1f]
    // 0xb8bf24: LoadField: r0 = r3->field_f
    //     0xb8bf24: ldur            w0, [x3, #0xf]
    // 0xb8bf28: DecompressPointer r0
    //     0xb8bf28: add             x0, x0, HEAP, lsl #32
    // 0xb8bf2c: LoadField: r1 = r0->field_13
    //     0xb8bf2c: ldur            w1, [x0, #0x13]
    // 0xb8bf30: DecompressPointer r1
    //     0xb8bf30: add             x1, x1, HEAP, lsl #32
    // 0xb8bf34: StoreField: r2->field_23 = r1
    //     0xb8bf34: stur            w1, [x2, #0x23]
    // 0xb8bf38: r16 = <String, Object>
    //     0xb8bf38: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xb8bf3c: ldr             x16, [x16, #0x790]
    // 0xb8bf40: stp             x2, x16, [SP]
    // 0xb8bf44: r0 = Map._fromLiteral()
    //     0xb8bf44: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8bf48: r16 = "/doa/doa-list"
    //     0xb8bf48: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bc8] "/doa/doa-list"
    //     0xb8bf4c: ldr             x16, [x16, #0xbc8]
    // 0xb8bf50: stp             x16, NULL, [SP, #8]
    // 0xb8bf54: str             x0, [SP]
    // 0xb8bf58: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8bf58: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8bf5c: ldr             x4, [x4, #0x478]
    // 0xb8bf60: r0 = GetNavigation.toNamed()
    //     0xb8bf60: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8bf64: LeaveFrame
    //     0xb8bf64: mov             SP, fp
    //     0xb8bf68: ldp             fp, lr, [SP], #0x10
    // 0xb8bf6c: ret
    //     0xb8bf6c: ret             
    // 0xb8bf70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8bf70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8bf74: b               #0xb8bd9c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8bf78, size: 0x178
    // 0xb8bf78: EnterFrame
    //     0xb8bf78: stp             fp, lr, [SP, #-0x10]!
    //     0xb8bf7c: mov             fp, SP
    // 0xb8bf80: AllocStack(0x20)
    //     0xb8bf80: sub             SP, SP, #0x20
    // 0xb8bf84: SetupParameters()
    //     0xb8bf84: ldr             x0, [fp, #0x10]
    //     0xb8bf88: ldur            w1, [x0, #0x17]
    //     0xb8bf8c: add             x1, x1, HEAP, lsl #32
    //     0xb8bf90: stur            x1, [fp, #-8]
    // 0xb8bf94: CheckStackOverflow
    //     0xb8bf94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8bf98: cmp             SP, x16
    //     0xb8bf9c: b.ls            #0xb8c0e8
    // 0xb8bfa0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8bfa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8bfa4: ldr             x0, [x0, #0x2670]
    //     0xb8bfa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8bfac: cmp             w0, w16
    //     0xb8bfb0: b.ne            #0xb8bfbc
    //     0xb8bfb4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8bfb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8bfbc: ldur            x0, [fp, #-8]
    // 0xb8bfc0: LoadField: r1 = r0->field_b
    //     0xb8bfc0: ldur            w1, [x0, #0xb]
    // 0xb8bfc4: DecompressPointer r1
    //     0xb8bfc4: add             x1, x1, HEAP, lsl #32
    // 0xb8bfc8: LoadField: r0 = r1->field_b
    //     0xb8bfc8: ldur            w0, [x1, #0xb]
    // 0xb8bfcc: DecompressPointer r0
    //     0xb8bfcc: add             x0, x0, HEAP, lsl #32
    // 0xb8bfd0: LoadField: r1 = r0->field_b
    //     0xb8bfd0: ldur            w1, [x0, #0xb]
    // 0xb8bfd4: DecompressPointer r1
    //     0xb8bfd4: add             x1, x1, HEAP, lsl #32
    // 0xb8bfd8: LoadField: r0 = r1->field_f
    //     0xb8bfd8: ldur            w0, [x1, #0xf]
    // 0xb8bfdc: DecompressPointer r0
    //     0xb8bfdc: add             x0, x0, HEAP, lsl #32
    // 0xb8bfe0: LoadField: r1 = r0->field_b
    //     0xb8bfe0: ldur            w1, [x0, #0xb]
    // 0xb8bfe4: DecompressPointer r1
    //     0xb8bfe4: add             x1, x1, HEAP, lsl #32
    // 0xb8bfe8: r16 = "doa"
    //     0xb8bfe8: add             x16, PP, #0xf, lsl #12  ; [pp+0xfb60] "doa"
    //     0xb8bfec: ldr             x16, [x16, #0xb60]
    // 0xb8bff0: stp             x16, x1, [SP]
    // 0xb8bff4: r0 = ==()
    //     0xb8bff4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8bff8: tbnz            w0, #4, #0xb8c060
    // 0xb8bffc: r1 = Null
    //     0xb8bffc: mov             x1, NULL
    // 0xb8c000: r2 = 12
    //     0xb8c000: movz            x2, #0xc
    // 0xb8c004: r0 = AllocateArray()
    //     0xb8c004: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8c008: r16 = "id"
    //     0xb8c008: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8c00c: ldr             x16, [x16, #0x740]
    // 0xb8c010: StoreField: r0->field_f = r16
    //     0xb8c010: stur            w16, [x0, #0xf]
    // 0xb8c014: r16 = 144
    //     0xb8c014: movz            x16, #0x90
    // 0xb8c018: StoreField: r0->field_13 = r16
    //     0xb8c018: stur            w16, [x0, #0x13]
    // 0xb8c01c: r16 = "title"
    //     0xb8c01c: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xb8c020: ldr             x16, [x16, #0x748]
    // 0xb8c024: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8c024: stur            w16, [x0, #0x17]
    // 0xb8c028: r16 = "Kumpulan Doa Mustajab"
    //     0xb8c028: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bd0] "Kumpulan Doa Mustajab"
    //     0xb8c02c: ldr             x16, [x16, #0xbd0]
    // 0xb8c030: StoreField: r0->field_1b = r16
    //     0xb8c030: stur            w16, [x0, #0x1b]
    // 0xb8c034: r16 = "slug"
    //     0xb8c034: add             x16, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xb8c038: ldr             x16, [x16, #0x9a8]
    // 0xb8c03c: StoreField: r0->field_1f = r16
    //     0xb8c03c: stur            w16, [x0, #0x1f]
    // 0xb8c040: r16 = "kumpulan-doa-mustajab"
    //     0xb8c040: add             x16, PP, #0x35, lsl #12  ; [pp+0x35bd8] "kumpulan-doa-mustajab"
    //     0xb8c044: ldr             x16, [x16, #0xbd8]
    // 0xb8c048: StoreField: r0->field_23 = r16
    //     0xb8c048: stur            w16, [x0, #0x23]
    // 0xb8c04c: r16 = <String, Object>
    //     0xb8c04c: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xb8c050: ldr             x16, [x16, #0x790]
    // 0xb8c054: stp             x0, x16, [SP]
    // 0xb8c058: r0 = Map._fromLiteral()
    //     0xb8c058: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8c05c: b               #0xb8c0c0
    // 0xb8c060: r1 = Null
    //     0xb8c060: mov             x1, NULL
    // 0xb8c064: r2 = 12
    //     0xb8c064: movz            x2, #0xc
    // 0xb8c068: r0 = AllocateArray()
    //     0xb8c068: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8c06c: r16 = "id"
    //     0xb8c06c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8c070: ldr             x16, [x16, #0x740]
    // 0xb8c074: StoreField: r0->field_f = r16
    //     0xb8c074: stur            w16, [x0, #0xf]
    // 0xb8c078: r16 = 150
    //     0xb8c078: movz            x16, #0x96
    // 0xb8c07c: StoreField: r0->field_13 = r16
    //     0xb8c07c: stur            w16, [x0, #0x13]
    // 0xb8c080: r16 = "title"
    //     0xb8c080: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xb8c084: ldr             x16, [x16, #0x748]
    // 0xb8c088: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8c088: stur            w16, [x0, #0x17]
    // 0xb8c08c: r16 = "Wirid dan Shalawat: Bacaan, Sejarah, dan Keutamaannya"
    //     0xb8c08c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35be0] "Wirid dan Shalawat: Bacaan, Sejarah, dan Keutamaannya"
    //     0xb8c090: ldr             x16, [x16, #0xbe0]
    // 0xb8c094: StoreField: r0->field_1b = r16
    //     0xb8c094: stur            w16, [x0, #0x1b]
    // 0xb8c098: r16 = "slug"
    //     0xb8c098: add             x16, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xb8c09c: ldr             x16, [x16, #0x9a8]
    // 0xb8c0a0: StoreField: r0->field_1f = r16
    //     0xb8c0a0: stur            w16, [x0, #0x1f]
    // 0xb8c0a4: r16 = "wirid-dan-shalawat-bacaan-sejarah-dan-keutamaannya"
    //     0xb8c0a4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35be8] "wirid-dan-shalawat-bacaan-sejarah-dan-keutamaannya"
    //     0xb8c0a8: ldr             x16, [x16, #0xbe8]
    // 0xb8c0ac: StoreField: r0->field_23 = r16
    //     0xb8c0ac: stur            w16, [x0, #0x23]
    // 0xb8c0b0: r16 = <String, Object>
    //     0xb8c0b0: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xb8c0b4: ldr             x16, [x16, #0x790]
    // 0xb8c0b8: stp             x0, x16, [SP]
    // 0xb8c0bc: r0 = Map._fromLiteral()
    //     0xb8c0bc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8c0c0: r16 = "/topic/topic-detail"
    //     0xb8c0c0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24998] "/topic/topic-detail"
    //     0xb8c0c4: ldr             x16, [x16, #0x998]
    // 0xb8c0c8: stp             x16, NULL, [SP, #8]
    // 0xb8c0cc: str             x0, [SP]
    // 0xb8c0d0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8c0d0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8c0d4: ldr             x4, [x4, #0x478]
    // 0xb8c0d8: r0 = GetNavigation.toNamed()
    //     0xb8c0d8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8c0dc: LeaveFrame
    //     0xb8c0dc: mov             SP, fp
    //     0xb8c0e0: ldp             fp, lr, [SP], #0x10
    // 0xb8c0e4: ret
    //     0xb8c0e4: ret             
    // 0xb8c0e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8c0e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8c0ec: b               #0xb8bfa0
  }
}
