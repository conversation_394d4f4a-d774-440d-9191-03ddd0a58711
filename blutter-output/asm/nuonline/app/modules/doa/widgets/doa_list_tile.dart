// lib: , url: package:nuonline/app/modules/doa/widgets/doa_list_tile.dart

// class id: 1050190, size: 0x8
class :: {
}

// class id: 5049, size: 0x28, field offset: 0xc
//   const constructor, 
class NDoaListTile extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8c0f0, size: 0x61c
    // 0xb8c0f0: EnterFrame
    //     0xb8c0f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8c0f4: mov             fp, SP
    // 0xb8c0f8: AllocStack(0x78)
    //     0xb8c0f8: sub             SP, SP, #0x78
    // 0xb8c0fc: SetupParameters(NDoaListTile this /* r1 => r0, fp-0x30 */)
    //     0xb8c0fc: mov             x0, x1
    //     0xb8c100: stur            x1, [fp, #-0x30]
    // 0xb8c104: CheckStackOverflow
    //     0xb8c104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8c108: cmp             SP, x16
    //     0xb8c10c: b.ls            #0xb8c6c4
    // 0xb8c110: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb8c110: ldur            w3, [x0, #0x17]
    // 0xb8c114: DecompressPointer r3
    //     0xb8c114: add             x3, x3, HEAP, lsl #32
    // 0xb8c118: stur            x3, [fp, #-0x28]
    // 0xb8c11c: tbz             w3, #4, #0xb8c12c
    // 0xb8c120: LoadField: r1 = r0->field_1b
    //     0xb8c120: ldur            w1, [x0, #0x1b]
    // 0xb8c124: DecompressPointer r1
    //     0xb8c124: add             x1, x1, HEAP, lsl #32
    // 0xb8c128: tbnz            w1, #4, #0xb8c134
    // 0xb8c12c: r4 = Instance_TextAlign
    //     0xb8c12c: ldr             x4, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xb8c130: b               #0xb8c138
    // 0xb8c134: r4 = Null
    //     0xb8c134: mov             x4, NULL
    // 0xb8c138: stur            x4, [fp, #-0x20]
    // 0xb8c13c: tbnz            w3, #4, #0xb8c14c
    // 0xb8c140: r5 = "ArefRuqaa"
    //     0xb8c140: add             x5, PP, #0x36, lsl #12  ; [pp+0x36f48] "ArefRuqaa"
    //     0xb8c144: ldr             x5, [x5, #0xf48]
    // 0xb8c148: b               #0xb8c154
    // 0xb8c14c: r5 = "OmarNaskh"
    //     0xb8c14c: add             x5, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xb8c150: ldr             x5, [x5, #0xbc8]
    // 0xb8c154: stur            x5, [fp, #-0x18]
    // 0xb8c158: LoadField: r1 = r0->field_1b
    //     0xb8c158: ldur            w1, [x0, #0x1b]
    // 0xb8c15c: DecompressPointer r1
    //     0xb8c15c: add             x1, x1, HEAP, lsl #32
    // 0xb8c160: tbnz            w1, #4, #0xb8c170
    // 0xb8c164: r6 = Instance_FontWeight
    //     0xb8c164: add             x6, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb8c168: ldr             x6, [x6, #0xe20]
    // 0xb8c16c: b               #0xb8c178
    // 0xb8c170: r6 = Instance_FontWeight
    //     0xb8c170: add             x6, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xb8c174: ldr             x6, [x6, #0xc50]
    // 0xb8c178: stur            x6, [fp, #-0x10]
    // 0xb8c17c: tbnz            w3, #4, #0xb8c18c
    // 0xb8c180: r7 = Instance_FontWeight
    //     0xb8c180: add             x7, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb8c184: ldr             x7, [x7, #0xe20]
    // 0xb8c188: b               #0xb8c194
    // 0xb8c18c: r7 = Instance_FontWeight
    //     0xb8c18c: add             x7, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xb8c190: ldr             x7, [x7, #0xc50]
    // 0xb8c194: stur            x7, [fp, #-8]
    // 0xb8c198: tbnz            w1, #4, #0xb8c1dc
    // 0xb8c19c: r1 = _ConstMap len:10
    //     0xb8c19c: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xb8c1a0: ldr             x1, [x1, #0x6c8]
    // 0xb8c1a4: r2 = 600
    //     0xb8c1a4: movz            x2, #0x258
    // 0xb8c1a8: r0 = []()
    //     0xb8c1a8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8c1ac: cmp             w0, NULL
    // 0xb8c1b0: b.eq            #0xb8c6cc
    // 0xb8c1b4: r16 = <Color>
    //     0xb8c1b4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8c1b8: ldr             x16, [x16, #0x158]
    // 0xb8c1bc: stp             x0, x16, [SP, #8]
    // 0xb8c1c0: r16 = Instance_MaterialColor
    //     0xb8c1c0: add             x16, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xb8c1c4: ldr             x16, [x16, #0x6d0]
    // 0xb8c1c8: str             x16, [SP]
    // 0xb8c1cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8c1cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8c1d0: r0 = mode()
    //     0xb8c1d0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8c1d4: mov             x6, x0
    // 0xb8c1d8: b               #0xb8c204
    // 0xb8c1dc: r16 = <Color>
    //     0xb8c1dc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8c1e0: ldr             x16, [x16, #0x158]
    // 0xb8c1e4: r30 = Instance_Color
    //     0xb8c1e4: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb8c1e8: stp             lr, x16, [SP, #8]
    // 0xb8c1ec: r16 = Instance_MaterialColor
    //     0xb8c1ec: add             x16, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xb8c1f0: ldr             x16, [x16, #0xbf0]
    // 0xb8c1f4: str             x16, [SP]
    // 0xb8c1f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8c1f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8c1fc: r0 = mode()
    //     0xb8c1fc: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8c200: mov             x6, x0
    // 0xb8c204: ldur            x0, [fp, #-0x30]
    // 0xb8c208: ldur            x1, [fp, #-0x28]
    // 0xb8c20c: ldur            x2, [fp, #-0x20]
    // 0xb8c210: ldur            x3, [fp, #-0x18]
    // 0xb8c214: ldur            x4, [fp, #-0x10]
    // 0xb8c218: ldur            x5, [fp, #-8]
    // 0xb8c21c: stur            x6, [fp, #-0x40]
    // 0xb8c220: LoadField: r7 = r0->field_1f
    //     0xb8c220: ldur            w7, [x0, #0x1f]
    // 0xb8c224: DecompressPointer r7
    //     0xb8c224: add             x7, x7, HEAP, lsl #32
    // 0xb8c228: stur            x7, [fp, #-0x38]
    // 0xb8c22c: LoadField: d0 = r7->field_7
    //     0xb8c22c: ldur            d0, [x7, #7]
    // 0xb8c230: tst             x1, #0x10
    // 0xb8c234: cset            x8, ne
    // 0xb8c238: sub             x8, x8, #1
    // 0xb8c23c: r16 = 20
    //     0xb8c23c: movz            x16, #0x14
    // 0xb8c240: and             x8, x8, x16
    // 0xb8c244: r16 = LoadInt32Instr(r8)
    //     0xb8c244: sbfx            x16, x8, #1, #0x1f
    // 0xb8c248: scvtf           d1, w16
    // 0xb8c24c: fadd            d2, d0, d1
    // 0xb8c250: stur            d2, [fp, #-0x60]
    // 0xb8c254: r0 = TextStyle()
    //     0xb8c254: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb8c258: mov             x3, x0
    // 0xb8c25c: r0 = true
    //     0xb8c25c: add             x0, NULL, #0x20  ; true
    // 0xb8c260: stur            x3, [fp, #-0x28]
    // 0xb8c264: StoreField: r3->field_7 = r0
    //     0xb8c264: stur            w0, [x3, #7]
    // 0xb8c268: ldur            x1, [fp, #-0x40]
    // 0xb8c26c: StoreField: r3->field_b = r1
    //     0xb8c26c: stur            w1, [x3, #0xb]
    // 0xb8c270: ldur            d0, [fp, #-0x60]
    // 0xb8c274: r1 = inline_Allocate_Double()
    //     0xb8c274: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb8c278: add             x1, x1, #0x10
    //     0xb8c27c: cmp             x2, x1
    //     0xb8c280: b.ls            #0xb8c6d0
    //     0xb8c284: str             x1, [THR, #0x50]  ; THR::top
    //     0xb8c288: sub             x1, x1, #0xf
    //     0xb8c28c: movz            x2, #0xe15c
    //     0xb8c290: movk            x2, #0x3, lsl #16
    //     0xb8c294: stur            x2, [x1, #-1]
    // 0xb8c298: StoreField: r1->field_7 = d0
    //     0xb8c298: stur            d0, [x1, #7]
    // 0xb8c29c: StoreField: r3->field_1f = r1
    //     0xb8c29c: stur            w1, [x3, #0x1f]
    // 0xb8c2a0: ldur            x1, [fp, #-0x10]
    // 0xb8c2a4: StoreField: r3->field_23 = r1
    //     0xb8c2a4: stur            w1, [x3, #0x23]
    // 0xb8c2a8: ldur            x1, [fp, #-0x18]
    // 0xb8c2ac: StoreField: r3->field_13 = r1
    //     0xb8c2ac: stur            w1, [x3, #0x13]
    // 0xb8c2b0: ldur            x4, [fp, #-0x38]
    // 0xb8c2b4: LoadField: d0 = r4->field_f
    //     0xb8c2b4: ldur            d0, [x4, #0xf]
    // 0xb8c2b8: stur            d0, [fp, #-0x60]
    // 0xb8c2bc: r1 = _ConstMap len:10
    //     0xb8c2bc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb8c2c0: ldr             x1, [x1, #0xc08]
    // 0xb8c2c4: r2 = 600
    //     0xb8c2c4: movz            x2, #0x258
    // 0xb8c2c8: r0 = []()
    //     0xb8c2c8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8c2cc: r16 = <Color?>
    //     0xb8c2cc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8c2d0: ldr             x16, [x16, #0x98]
    // 0xb8c2d4: stp             x0, x16, [SP, #8]
    // 0xb8c2d8: r16 = Instance_Color
    //     0xb8c2d8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bd8] Obj!Color@e2ae41
    //     0xb8c2dc: ldr             x16, [x16, #0xbd8]
    // 0xb8c2e0: str             x16, [SP]
    // 0xb8c2e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8c2e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8c2e8: r0 = mode()
    //     0xb8c2e8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8c2ec: stur            x0, [fp, #-0x10]
    // 0xb8c2f0: r0 = TextStyle()
    //     0xb8c2f0: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb8c2f4: mov             x3, x0
    // 0xb8c2f8: r0 = true
    //     0xb8c2f8: add             x0, NULL, #0x20  ; true
    // 0xb8c2fc: stur            x3, [fp, #-0x18]
    // 0xb8c300: StoreField: r3->field_7 = r0
    //     0xb8c300: stur            w0, [x3, #7]
    // 0xb8c304: ldur            x1, [fp, #-0x10]
    // 0xb8c308: StoreField: r3->field_b = r1
    //     0xb8c308: stur            w1, [x3, #0xb]
    // 0xb8c30c: ldur            d0, [fp, #-0x60]
    // 0xb8c310: r4 = inline_Allocate_Double()
    //     0xb8c310: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xb8c314: add             x4, x4, #0x10
    //     0xb8c318: cmp             x1, x4
    //     0xb8c31c: b.ls            #0xb8c6ec
    //     0xb8c320: str             x4, [THR, #0x50]  ; THR::top
    //     0xb8c324: sub             x4, x4, #0xf
    //     0xb8c328: movz            x1, #0xe15c
    //     0xb8c32c: movk            x1, #0x3, lsl #16
    //     0xb8c330: stur            x1, [x4, #-1]
    // 0xb8c334: StoreField: r4->field_7 = d0
    //     0xb8c334: stur            d0, [x4, #7]
    // 0xb8c338: stur            x4, [fp, #-0x10]
    // 0xb8c33c: StoreField: r3->field_1f = r4
    //     0xb8c33c: stur            w4, [x3, #0x1f]
    // 0xb8c340: ldur            x5, [fp, #-8]
    // 0xb8c344: StoreField: r3->field_23 = r5
    //     0xb8c344: stur            w5, [x3, #0x23]
    // 0xb8c348: r6 = 1.250000
    //     0xb8c348: add             x6, PP, #0x27, lsl #12  ; [pp+0x274a8] 1.25
    //     0xb8c34c: ldr             x6, [x6, #0x4a8]
    // 0xb8c350: StoreField: r3->field_37 = r6
    //     0xb8c350: stur            w6, [x3, #0x37]
    // 0xb8c354: r7 = "Inter"
    //     0xb8c354: add             x7, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb8c358: ldr             x7, [x7, #0xcb0]
    // 0xb8c35c: StoreField: r3->field_13 = r7
    //     0xb8c35c: stur            w7, [x3, #0x13]
    // 0xb8c360: r1 = _ConstMap len:6
    //     0xb8c360: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb8c364: ldr             x1, [x1, #0xc20]
    // 0xb8c368: r2 = 8
    //     0xb8c368: movz            x2, #0x8
    // 0xb8c36c: r0 = []()
    //     0xb8c36c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8c370: r1 = _ConstMap len:6
    //     0xb8c370: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb8c374: ldr             x1, [x1, #0xc20]
    // 0xb8c378: r2 = 4
    //     0xb8c378: movz            x2, #0x4
    // 0xb8c37c: stur            x0, [fp, #-0x40]
    // 0xb8c380: r0 = []()
    //     0xb8c380: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8c384: r16 = <Color?>
    //     0xb8c384: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8c388: ldr             x16, [x16, #0x98]
    // 0xb8c38c: stp             x0, x16, [SP, #8]
    // 0xb8c390: ldur            x16, [fp, #-0x40]
    // 0xb8c394: str             x16, [SP]
    // 0xb8c398: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8c398: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8c39c: r0 = mode()
    //     0xb8c39c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8c3a0: stur            x0, [fp, #-0x40]
    // 0xb8c3a4: r0 = TextStyle()
    //     0xb8c3a4: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb8c3a8: mov             x1, x0
    // 0xb8c3ac: r0 = true
    //     0xb8c3ac: add             x0, NULL, #0x20  ; true
    // 0xb8c3b0: stur            x1, [fp, #-0x48]
    // 0xb8c3b4: StoreField: r1->field_7 = r0
    //     0xb8c3b4: stur            w0, [x1, #7]
    // 0xb8c3b8: ldur            x0, [fp, #-0x40]
    // 0xb8c3bc: StoreField: r1->field_b = r0
    //     0xb8c3bc: stur            w0, [x1, #0xb]
    // 0xb8c3c0: ldur            x0, [fp, #-0x10]
    // 0xb8c3c4: StoreField: r1->field_1f = r0
    //     0xb8c3c4: stur            w0, [x1, #0x1f]
    // 0xb8c3c8: ldur            x0, [fp, #-8]
    // 0xb8c3cc: StoreField: r1->field_23 = r0
    //     0xb8c3cc: stur            w0, [x1, #0x23]
    // 0xb8c3d0: r0 = 1.250000
    //     0xb8c3d0: add             x0, PP, #0x27, lsl #12  ; [pp+0x274a8] 1.25
    //     0xb8c3d4: ldr             x0, [x0, #0x4a8]
    // 0xb8c3d8: StoreField: r1->field_37 = r0
    //     0xb8c3d8: stur            w0, [x1, #0x37]
    // 0xb8c3dc: r0 = "Inter"
    //     0xb8c3dc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb8c3e0: ldr             x0, [x0, #0xcb0]
    // 0xb8c3e4: StoreField: r1->field_13 = r0
    //     0xb8c3e4: stur            w0, [x1, #0x13]
    // 0xb8c3e8: ldur            x0, [fp, #-0x30]
    // 0xb8c3ec: LoadField: r2 = r0->field_23
    //     0xb8c3ec: ldur            w2, [x0, #0x23]
    // 0xb8c3f0: DecompressPointer r2
    //     0xb8c3f0: add             x2, x2, HEAP, lsl #32
    // 0xb8c3f4: stur            x2, [fp, #-0x10]
    // 0xb8c3f8: LoadField: r3 = r0->field_b
    //     0xb8c3f8: ldur            w3, [x0, #0xb]
    // 0xb8c3fc: DecompressPointer r3
    //     0xb8c3fc: add             x3, x3, HEAP, lsl #32
    // 0xb8c400: stur            x3, [fp, #-8]
    // 0xb8c404: r0 = Text()
    //     0xb8c404: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8c408: mov             x3, x0
    // 0xb8c40c: ldur            x0, [fp, #-8]
    // 0xb8c410: stur            x3, [fp, #-0x40]
    // 0xb8c414: StoreField: r3->field_b = r0
    //     0xb8c414: stur            w0, [x3, #0xb]
    // 0xb8c418: ldur            x0, [fp, #-0x28]
    // 0xb8c41c: StoreField: r3->field_13 = r0
    //     0xb8c41c: stur            w0, [x3, #0x13]
    // 0xb8c420: ldur            x0, [fp, #-0x20]
    // 0xb8c424: StoreField: r3->field_1b = r0
    //     0xb8c424: stur            w0, [x3, #0x1b]
    // 0xb8c428: r1 = Instance_TextDirection
    //     0xb8c428: ldr             x1, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xb8c42c: StoreField: r3->field_1f = r1
    //     0xb8c42c: stur            w1, [x3, #0x1f]
    // 0xb8c430: r1 = Null
    //     0xb8c430: mov             x1, NULL
    // 0xb8c434: r2 = 2
    //     0xb8c434: movz            x2, #0x2
    // 0xb8c438: r0 = AllocateArray()
    //     0xb8c438: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8c43c: mov             x2, x0
    // 0xb8c440: ldur            x0, [fp, #-0x40]
    // 0xb8c444: stur            x2, [fp, #-8]
    // 0xb8c448: StoreField: r2->field_f = r0
    //     0xb8c448: stur            w0, [x2, #0xf]
    // 0xb8c44c: r1 = <Widget>
    //     0xb8c44c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8c450: r0 = AllocateGrowableArray()
    //     0xb8c450: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8c454: mov             x1, x0
    // 0xb8c458: ldur            x0, [fp, #-8]
    // 0xb8c45c: stur            x1, [fp, #-0x40]
    // 0xb8c460: StoreField: r1->field_f = r0
    //     0xb8c460: stur            w0, [x1, #0xf]
    // 0xb8c464: r2 = 2
    //     0xb8c464: movz            x2, #0x2
    // 0xb8c468: StoreField: r1->field_b = r2
    //     0xb8c468: stur            w2, [x1, #0xb]
    // 0xb8c46c: ldur            x2, [fp, #-0x38]
    // 0xb8c470: LoadField: r3 = r2->field_1b
    //     0xb8c470: ldur            w3, [x2, #0x1b]
    // 0xb8c474: DecompressPointer r3
    //     0xb8c474: add             x3, x3, HEAP, lsl #32
    // 0xb8c478: tbnz            w3, #4, #0xb8c538
    // 0xb8c47c: ldur            x3, [fp, #-0x30]
    // 0xb8c480: LoadField: r4 = r3->field_f
    //     0xb8c480: ldur            w4, [x3, #0xf]
    // 0xb8c484: DecompressPointer r4
    //     0xb8c484: add             x4, x4, HEAP, lsl #32
    // 0xb8c488: stur            x4, [fp, #-0x28]
    // 0xb8c48c: cmp             w4, NULL
    // 0xb8c490: b.eq            #0xb8c530
    // 0xb8c494: ldur            x0, [fp, #-0x20]
    // 0xb8c498: ldur            x5, [fp, #-0x18]
    // 0xb8c49c: r0 = Text()
    //     0xb8c49c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8c4a0: mov             x1, x0
    // 0xb8c4a4: ldur            x0, [fp, #-0x28]
    // 0xb8c4a8: stur            x1, [fp, #-0x50]
    // 0xb8c4ac: StoreField: r1->field_b = r0
    //     0xb8c4ac: stur            w0, [x1, #0xb]
    // 0xb8c4b0: ldur            x0, [fp, #-0x18]
    // 0xb8c4b4: StoreField: r1->field_13 = r0
    //     0xb8c4b4: stur            w0, [x1, #0x13]
    // 0xb8c4b8: ldur            x0, [fp, #-0x20]
    // 0xb8c4bc: StoreField: r1->field_1b = r0
    //     0xb8c4bc: stur            w0, [x1, #0x1b]
    // 0xb8c4c0: r0 = Padding()
    //     0xb8c4c0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8c4c4: mov             x2, x0
    // 0xb8c4c8: r0 = Instance_EdgeInsets
    //     0xb8c4c8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3ccd8] Obj!EdgeInsets@e13451
    //     0xb8c4cc: ldr             x0, [x0, #0xcd8]
    // 0xb8c4d0: stur            x2, [fp, #-0x18]
    // 0xb8c4d4: StoreField: r2->field_f = r0
    //     0xb8c4d4: stur            w0, [x2, #0xf]
    // 0xb8c4d8: ldur            x0, [fp, #-0x50]
    // 0xb8c4dc: StoreField: r2->field_b = r0
    //     0xb8c4dc: stur            w0, [x2, #0xb]
    // 0xb8c4e0: ldur            x1, [fp, #-0x40]
    // 0xb8c4e4: r0 = _growToNextCapacity()
    //     0xb8c4e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8c4e8: ldur            x2, [fp, #-0x40]
    // 0xb8c4ec: r0 = 4
    //     0xb8c4ec: movz            x0, #0x4
    // 0xb8c4f0: StoreField: r2->field_b = r0
    //     0xb8c4f0: stur            w0, [x2, #0xb]
    // 0xb8c4f4: LoadField: r3 = r2->field_f
    //     0xb8c4f4: ldur            w3, [x2, #0xf]
    // 0xb8c4f8: DecompressPointer r3
    //     0xb8c4f8: add             x3, x3, HEAP, lsl #32
    // 0xb8c4fc: mov             x1, x3
    // 0xb8c500: ldur            x0, [fp, #-0x18]
    // 0xb8c504: ArrayStore: r1[1] = r0  ; List_4
    //     0xb8c504: add             x25, x1, #0x13
    //     0xb8c508: str             w0, [x25]
    //     0xb8c50c: tbz             w0, #0, #0xb8c528
    //     0xb8c510: ldurb           w16, [x1, #-1]
    //     0xb8c514: ldurb           w17, [x0, #-1]
    //     0xb8c518: and             x16, x17, x16, lsr #2
    //     0xb8c51c: tst             x16, HEAP, lsr #32
    //     0xb8c520: b.eq            #0xb8c528
    //     0xb8c524: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8c528: r1 = 2
    //     0xb8c528: movz            x1, #0x2
    // 0xb8c52c: b               #0xb8c544
    // 0xb8c530: mov             x2, x1
    // 0xb8c534: b               #0xb8c53c
    // 0xb8c538: mov             x2, x1
    // 0xb8c53c: mov             x3, x0
    // 0xb8c540: r1 = 1
    //     0xb8c540: movz            x1, #0x1
    // 0xb8c544: ldur            x0, [fp, #-0x38]
    // 0xb8c548: stur            x3, [fp, #-0x18]
    // 0xb8c54c: stur            x1, [fp, #-0x58]
    // 0xb8c550: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb8c550: ldur            w4, [x0, #0x17]
    // 0xb8c554: DecompressPointer r4
    //     0xb8c554: add             x4, x4, HEAP, lsl #32
    // 0xb8c558: tbnz            w4, #4, #0xb8c640
    // 0xb8c55c: ldur            x0, [fp, #-0x30]
    // 0xb8c560: LoadField: r4 = r0->field_13
    //     0xb8c560: ldur            w4, [x0, #0x13]
    // 0xb8c564: DecompressPointer r4
    //     0xb8c564: add             x4, x4, HEAP, lsl #32
    // 0xb8c568: stur            x4, [fp, #-8]
    // 0xb8c56c: cmp             w4, NULL
    // 0xb8c570: b.eq            #0xb8c638
    // 0xb8c574: ldur            x0, [fp, #-0x20]
    // 0xb8c578: ldur            x5, [fp, #-0x48]
    // 0xb8c57c: r0 = Text()
    //     0xb8c57c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8c580: mov             x1, x0
    // 0xb8c584: ldur            x0, [fp, #-8]
    // 0xb8c588: stur            x1, [fp, #-0x28]
    // 0xb8c58c: StoreField: r1->field_b = r0
    //     0xb8c58c: stur            w0, [x1, #0xb]
    // 0xb8c590: ldur            x0, [fp, #-0x48]
    // 0xb8c594: StoreField: r1->field_13 = r0
    //     0xb8c594: stur            w0, [x1, #0x13]
    // 0xb8c598: ldur            x0, [fp, #-0x20]
    // 0xb8c59c: StoreField: r1->field_1b = r0
    //     0xb8c59c: stur            w0, [x1, #0x1b]
    // 0xb8c5a0: r0 = Padding()
    //     0xb8c5a0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8c5a4: mov             x2, x0
    // 0xb8c5a8: r0 = Instance_EdgeInsets
    //     0xb8c5a8: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb8c5ac: ldr             x0, [x0, #0x7b0]
    // 0xb8c5b0: stur            x2, [fp, #-8]
    // 0xb8c5b4: StoreField: r2->field_f = r0
    //     0xb8c5b4: stur            w0, [x2, #0xf]
    // 0xb8c5b8: ldur            x0, [fp, #-0x28]
    // 0xb8c5bc: StoreField: r2->field_b = r0
    //     0xb8c5bc: stur            w0, [x2, #0xb]
    // 0xb8c5c0: ldur            x0, [fp, #-0x18]
    // 0xb8c5c4: LoadField: r1 = r0->field_b
    //     0xb8c5c4: ldur            w1, [x0, #0xb]
    // 0xb8c5c8: r0 = LoadInt32Instr(r1)
    //     0xb8c5c8: sbfx            x0, x1, #1, #0x1f
    // 0xb8c5cc: ldur            x3, [fp, #-0x58]
    // 0xb8c5d0: cmp             x3, x0
    // 0xb8c5d4: b.ne            #0xb8c5e0
    // 0xb8c5d8: ldur            x1, [fp, #-0x40]
    // 0xb8c5dc: r0 = _growToNextCapacity()
    //     0xb8c5dc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8c5e0: ldur            x3, [fp, #-0x40]
    // 0xb8c5e4: ldur            x2, [fp, #-0x58]
    // 0xb8c5e8: add             x0, x2, #1
    // 0xb8c5ec: lsl             x1, x0, #1
    // 0xb8c5f0: StoreField: r3->field_b = r1
    //     0xb8c5f0: stur            w1, [x3, #0xb]
    // 0xb8c5f4: mov             x1, x2
    // 0xb8c5f8: cmp             x1, x0
    // 0xb8c5fc: b.hs            #0xb8c708
    // 0xb8c600: LoadField: r1 = r3->field_f
    //     0xb8c600: ldur            w1, [x3, #0xf]
    // 0xb8c604: DecompressPointer r1
    //     0xb8c604: add             x1, x1, HEAP, lsl #32
    // 0xb8c608: ldur            x0, [fp, #-8]
    // 0xb8c60c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8c60c: add             x25, x1, x2, lsl #2
    //     0xb8c610: add             x25, x25, #0xf
    //     0xb8c614: str             w0, [x25]
    //     0xb8c618: tbz             w0, #0, #0xb8c634
    //     0xb8c61c: ldurb           w16, [x1, #-1]
    //     0xb8c620: ldurb           w17, [x0, #-1]
    //     0xb8c624: and             x16, x17, x16, lsr #2
    //     0xb8c628: tst             x16, HEAP, lsr #32
    //     0xb8c62c: b.eq            #0xb8c634
    //     0xb8c630: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8c634: b               #0xb8c644
    // 0xb8c638: mov             x3, x2
    // 0xb8c63c: b               #0xb8c644
    // 0xb8c640: mov             x3, x2
    // 0xb8c644: ldur            x0, [fp, #-0x10]
    // 0xb8c648: r0 = Column()
    //     0xb8c648: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8c64c: mov             x1, x0
    // 0xb8c650: r0 = Instance_Axis
    //     0xb8c650: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8c654: stur            x1, [fp, #-8]
    // 0xb8c658: StoreField: r1->field_f = r0
    //     0xb8c658: stur            w0, [x1, #0xf]
    // 0xb8c65c: r0 = Instance_MainAxisAlignment
    //     0xb8c65c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8c660: ldr             x0, [x0, #0x730]
    // 0xb8c664: StoreField: r1->field_13 = r0
    //     0xb8c664: stur            w0, [x1, #0x13]
    // 0xb8c668: r0 = Instance_MainAxisSize
    //     0xb8c668: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8c66c: ldr             x0, [x0, #0x738]
    // 0xb8c670: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8c670: stur            w0, [x1, #0x17]
    // 0xb8c674: r0 = Instance_CrossAxisAlignment
    //     0xb8c674: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xb8c678: ldr             x0, [x0, #0xf50]
    // 0xb8c67c: StoreField: r1->field_1b = r0
    //     0xb8c67c: stur            w0, [x1, #0x1b]
    // 0xb8c680: r0 = Instance_VerticalDirection
    //     0xb8c680: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8c684: ldr             x0, [x0, #0x748]
    // 0xb8c688: StoreField: r1->field_23 = r0
    //     0xb8c688: stur            w0, [x1, #0x23]
    // 0xb8c68c: r0 = Instance_Clip
    //     0xb8c68c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8c690: ldr             x0, [x0, #0x750]
    // 0xb8c694: StoreField: r1->field_2b = r0
    //     0xb8c694: stur            w0, [x1, #0x2b]
    // 0xb8c698: StoreField: r1->field_2f = rZR
    //     0xb8c698: stur            xzr, [x1, #0x2f]
    // 0xb8c69c: ldur            x0, [fp, #-0x40]
    // 0xb8c6a0: StoreField: r1->field_b = r0
    //     0xb8c6a0: stur            w0, [x1, #0xb]
    // 0xb8c6a4: r0 = Padding()
    //     0xb8c6a4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8c6a8: ldur            x1, [fp, #-0x10]
    // 0xb8c6ac: StoreField: r0->field_f = r1
    //     0xb8c6ac: stur            w1, [x0, #0xf]
    // 0xb8c6b0: ldur            x1, [fp, #-8]
    // 0xb8c6b4: StoreField: r0->field_b = r1
    //     0xb8c6b4: stur            w1, [x0, #0xb]
    // 0xb8c6b8: LeaveFrame
    //     0xb8c6b8: mov             SP, fp
    //     0xb8c6bc: ldp             fp, lr, [SP], #0x10
    // 0xb8c6c0: ret
    //     0xb8c6c0: ret             
    // 0xb8c6c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8c6c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8c6c8: b               #0xb8c110
    // 0xb8c6cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8c6cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8c6d0: SaveReg d0
    //     0xb8c6d0: str             q0, [SP, #-0x10]!
    // 0xb8c6d4: stp             x0, x3, [SP, #-0x10]!
    // 0xb8c6d8: r0 = AllocateDouble()
    //     0xb8c6d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb8c6dc: mov             x1, x0
    // 0xb8c6e0: ldp             x0, x3, [SP], #0x10
    // 0xb8c6e4: RestoreReg d0
    //     0xb8c6e4: ldr             q0, [SP], #0x10
    // 0xb8c6e8: b               #0xb8c298
    // 0xb8c6ec: SaveReg d0
    //     0xb8c6ec: str             q0, [SP, #-0x10]!
    // 0xb8c6f0: stp             x0, x3, [SP, #-0x10]!
    // 0xb8c6f4: r0 = AllocateDouble()
    //     0xb8c6f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb8c6f8: mov             x4, x0
    // 0xb8c6fc: ldp             x0, x3, [SP], #0x10
    // 0xb8c700: RestoreReg d0
    //     0xb8c700: ldr             q0, [SP], #0x10
    // 0xb8c704: b               #0xb8c334
    // 0xb8c708: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8c708: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
