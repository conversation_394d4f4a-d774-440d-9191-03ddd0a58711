// lib: , url: package:nuonline/app/modules/doa/widgets/doa_search_wrapper.dart

// class id: 1050191, size: 0x8
class :: {
}

// class id: 5048, size: 0x14, field offset: 0xc
//   const constructor, 
class DoaSearchWrapper extends StatelessWidget {

  DoaCategoryBuilder field_c;
  _OneByteString field_10;

  _ build(/* No info */) {
    // ** addr: 0xb8c70c, size: 0x150
    // 0xb8c70c: EnterFrame
    //     0xb8c70c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8c710: mov             fp, SP
    // 0xb8c714: AllocStack(0x20)
    //     0xb8c714: sub             SP, SP, #0x20
    // 0xb8c718: SetupParameters(DoaSearchWrapper this /* r1 => r1, fp-0x10 */)
    //     0xb8c718: stur            x1, [fp, #-0x10]
    // 0xb8c71c: LoadField: r0 = r1->field_f
    //     0xb8c71c: ldur            w0, [x1, #0xf]
    // 0xb8c720: DecompressPointer r0
    //     0xb8c720: add             x0, x0, HEAP, lsl #32
    // 0xb8c724: stur            x0, [fp, #-8]
    // 0xb8c728: r0 = NSearchTextFieldDisabled()
    //     0xb8c728: bl              #0xaf5574  ; AllocateNSearchTextFieldDisabledStub -> NSearchTextFieldDisabled (size=0x14)
    // 0xb8c72c: mov             x3, x0
    // 0xb8c730: ldur            x0, [fp, #-8]
    // 0xb8c734: stur            x3, [fp, #-0x18]
    // 0xb8c738: StoreField: r3->field_f = r0
    //     0xb8c738: stur            w0, [x3, #0xf]
    // 0xb8c73c: r1 = Function '<anonymous closure>':.
    //     0xb8c73c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b38] AnonymousClosure: (0xb8c85c), in [package:nuonline/app/modules/doa/widgets/doa_search_wrapper.dart] DoaSearchWrapper::build (0xb8c70c)
    //     0xb8c740: ldr             x1, [x1, #0xb38]
    // 0xb8c744: r2 = Null
    //     0xb8c744: mov             x2, NULL
    // 0xb8c748: r0 = AllocateClosure()
    //     0xb8c748: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8c74c: mov             x1, x0
    // 0xb8c750: ldur            x0, [fp, #-0x18]
    // 0xb8c754: StoreField: r0->field_b = r1
    //     0xb8c754: stur            w1, [x0, #0xb]
    // 0xb8c758: r0 = Padding()
    //     0xb8c758: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8c75c: mov             x2, x0
    // 0xb8c760: r0 = Instance_EdgeInsets
    //     0xb8c760: add             x0, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xb8c764: ldr             x0, [x0, #0xde8]
    // 0xb8c768: stur            x2, [fp, #-0x20]
    // 0xb8c76c: StoreField: r2->field_f = r0
    //     0xb8c76c: stur            w0, [x2, #0xf]
    // 0xb8c770: ldur            x0, [fp, #-0x18]
    // 0xb8c774: StoreField: r2->field_b = r0
    //     0xb8c774: stur            w0, [x2, #0xb]
    // 0xb8c778: ldur            x0, [fp, #-0x10]
    // 0xb8c77c: LoadField: r3 = r0->field_b
    //     0xb8c77c: ldur            w3, [x0, #0xb]
    // 0xb8c780: DecompressPointer r3
    //     0xb8c780: add             x3, x3, HEAP, lsl #32
    // 0xb8c784: stur            x3, [fp, #-8]
    // 0xb8c788: r1 = <FlexParentData>
    //     0xb8c788: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb8c78c: ldr             x1, [x1, #0x720]
    // 0xb8c790: r0 = Expanded()
    //     0xb8c790: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb8c794: mov             x3, x0
    // 0xb8c798: r0 = 1
    //     0xb8c798: movz            x0, #0x1
    // 0xb8c79c: stur            x3, [fp, #-0x10]
    // 0xb8c7a0: StoreField: r3->field_13 = r0
    //     0xb8c7a0: stur            x0, [x3, #0x13]
    // 0xb8c7a4: r0 = Instance_FlexFit
    //     0xb8c7a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb8c7a8: ldr             x0, [x0, #0x728]
    // 0xb8c7ac: StoreField: r3->field_1b = r0
    //     0xb8c7ac: stur            w0, [x3, #0x1b]
    // 0xb8c7b0: ldur            x0, [fp, #-8]
    // 0xb8c7b4: StoreField: r3->field_b = r0
    //     0xb8c7b4: stur            w0, [x3, #0xb]
    // 0xb8c7b8: r1 = Null
    //     0xb8c7b8: mov             x1, NULL
    // 0xb8c7bc: r2 = 4
    //     0xb8c7bc: movz            x2, #0x4
    // 0xb8c7c0: r0 = AllocateArray()
    //     0xb8c7c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8c7c4: mov             x2, x0
    // 0xb8c7c8: ldur            x0, [fp, #-0x20]
    // 0xb8c7cc: stur            x2, [fp, #-8]
    // 0xb8c7d0: StoreField: r2->field_f = r0
    //     0xb8c7d0: stur            w0, [x2, #0xf]
    // 0xb8c7d4: ldur            x0, [fp, #-0x10]
    // 0xb8c7d8: StoreField: r2->field_13 = r0
    //     0xb8c7d8: stur            w0, [x2, #0x13]
    // 0xb8c7dc: r1 = <Widget>
    //     0xb8c7dc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8c7e0: r0 = AllocateGrowableArray()
    //     0xb8c7e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8c7e4: mov             x1, x0
    // 0xb8c7e8: ldur            x0, [fp, #-8]
    // 0xb8c7ec: stur            x1, [fp, #-0x10]
    // 0xb8c7f0: StoreField: r1->field_f = r0
    //     0xb8c7f0: stur            w0, [x1, #0xf]
    // 0xb8c7f4: r0 = 4
    //     0xb8c7f4: movz            x0, #0x4
    // 0xb8c7f8: StoreField: r1->field_b = r0
    //     0xb8c7f8: stur            w0, [x1, #0xb]
    // 0xb8c7fc: r0 = Column()
    //     0xb8c7fc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8c800: r1 = Instance_Axis
    //     0xb8c800: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8c804: StoreField: r0->field_f = r1
    //     0xb8c804: stur            w1, [x0, #0xf]
    // 0xb8c808: r1 = Instance_MainAxisAlignment
    //     0xb8c808: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8c80c: ldr             x1, [x1, #0x730]
    // 0xb8c810: StoreField: r0->field_13 = r1
    //     0xb8c810: stur            w1, [x0, #0x13]
    // 0xb8c814: r1 = Instance_MainAxisSize
    //     0xb8c814: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8c818: ldr             x1, [x1, #0x738]
    // 0xb8c81c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8c81c: stur            w1, [x0, #0x17]
    // 0xb8c820: r1 = Instance_CrossAxisAlignment
    //     0xb8c820: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8c824: ldr             x1, [x1, #0x740]
    // 0xb8c828: StoreField: r0->field_1b = r1
    //     0xb8c828: stur            w1, [x0, #0x1b]
    // 0xb8c82c: r1 = Instance_VerticalDirection
    //     0xb8c82c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8c830: ldr             x1, [x1, #0x748]
    // 0xb8c834: StoreField: r0->field_23 = r1
    //     0xb8c834: stur            w1, [x0, #0x23]
    // 0xb8c838: r1 = Instance_Clip
    //     0xb8c838: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8c83c: ldr             x1, [x1, #0x750]
    // 0xb8c840: StoreField: r0->field_2b = r1
    //     0xb8c840: stur            w1, [x0, #0x2b]
    // 0xb8c844: StoreField: r0->field_2f = rZR
    //     0xb8c844: stur            xzr, [x0, #0x2f]
    // 0xb8c848: ldur            x1, [fp, #-0x10]
    // 0xb8c84c: StoreField: r0->field_b = r1
    //     0xb8c84c: stur            w1, [x0, #0xb]
    // 0xb8c850: LeaveFrame
    //     0xb8c850: mov             SP, fp
    //     0xb8c854: ldp             fp, lr, [SP], #0x10
    // 0xb8c858: ret
    //     0xb8c858: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8c85c, size: 0x5c
    // 0xb8c85c: EnterFrame
    //     0xb8c85c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8c860: mov             fp, SP
    // 0xb8c864: AllocStack(0x10)
    //     0xb8c864: sub             SP, SP, #0x10
    // 0xb8c868: CheckStackOverflow
    //     0xb8c868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8c86c: cmp             SP, x16
    //     0xb8c870: b.ls            #0xb8c8b0
    // 0xb8c874: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8c874: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8c878: ldr             x0, [x0, #0x2670]
    //     0xb8c87c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8c880: cmp             w0, w16
    //     0xb8c884: b.ne            #0xb8c890
    //     0xb8c888: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8c88c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8c890: r16 = "/doa/doa-search"
    //     0xb8c890: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b40] "/doa/doa-search"
    //     0xb8c894: ldr             x16, [x16, #0xb40]
    // 0xb8c898: stp             x16, NULL, [SP]
    // 0xb8c89c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb8c89c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8c8a0: r0 = GetNavigation.toNamed()
    //     0xb8c8a0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8c8a4: LeaveFrame
    //     0xb8c8a4: mov             SP, fp
    //     0xb8c8a8: ldp             fp, lr, [SP], #0x10
    // 0xb8c8ac: ret
    //     0xb8c8ac: ret             
    // 0xb8c8b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8c8b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8c8b4: b               #0xb8c874
  }
}
