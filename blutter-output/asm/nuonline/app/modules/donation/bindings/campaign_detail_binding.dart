// lib: , url: package:nuonline/app/modules/donation/bindings/campaign_detail_binding.dart

// class id: 1050192, size: 0x8
class :: {
}

// class id: 2181, size: 0x8, field offset: 0x8
class CampaignDetailBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x8102c4, size: 0x70
    // 0x8102c4: EnterFrame
    //     0x8102c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8102c8: mov             fp, SP
    // 0x8102cc: AllocStack(0x10)
    //     0x8102cc: sub             SP, SP, #0x10
    // 0x8102d0: CheckStackOverflow
    //     0x8102d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8102d4: cmp             SP, x16
    //     0x8102d8: b.ls            #0x81032c
    // 0x8102dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8102dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8102e0: ldr             x0, [x0, #0x2670]
    //     0x8102e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8102e8: cmp             w0, w16
    //     0x8102ec: b.ne            #0x8102f8
    //     0x8102f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8102f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8102f8: r1 = Function '<anonymous closure>':.
    //     0x8102f8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ae8] AnonymousClosure: (0x810334), in [package:nuonline/app/modules/donation/bindings/campaign_detail_binding.dart] CampaignDetailBinding::dependencies (0x8102c4)
    //     0x8102fc: ldr             x1, [x1, #0xae8]
    // 0x810300: r2 = Null
    //     0x810300: mov             x2, NULL
    // 0x810304: r0 = AllocateClosure()
    //     0x810304: bl              #0xec1630  ; AllocateClosureStub
    // 0x810308: r16 = <CampaignDetailController>
    //     0x810308: add             x16, PP, #0x24, lsl #12  ; [pp+0x248e0] TypeArguments: <CampaignDetailController>
    //     0x81030c: ldr             x16, [x16, #0x8e0]
    // 0x810310: stp             x0, x16, [SP]
    // 0x810314: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x810314: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x810318: r0 = Inst.lazyPut()
    //     0x810318: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x81031c: r0 = Null
    //     0x81031c: mov             x0, NULL
    // 0x810320: LeaveFrame
    //     0x810320: mov             SP, fp
    //     0x810324: ldp             fp, lr, [SP], #0x10
    // 0x810328: ret
    //     0x810328: ret             
    // 0x81032c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81032c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810330: b               #0x8102dc
  }
  [closure] CampaignDetailController <anonymous closure>(dynamic) {
    // ** addr: 0x810334, size: 0x19c
    // 0x810334: EnterFrame
    //     0x810334: stp             fp, lr, [SP, #-0x10]!
    //     0x810338: mov             fp, SP
    // 0x81033c: AllocStack(0x28)
    //     0x81033c: sub             SP, SP, #0x28
    // 0x810340: CheckStackOverflow
    //     0x810340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810344: cmp             SP, x16
    //     0x810348: b.ls            #0x8104c8
    // 0x81034c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x81034c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x810350: ldr             x0, [x0, #0x2670]
    //     0x810354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x810358: cmp             w0, w16
    //     0x81035c: b.ne            #0x810368
    //     0x810360: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x810364: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x810368: r0 = GetNavigation.arguments()
    //     0x810368: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x81036c: r16 = "id"
    //     0x81036c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x810370: ldr             x16, [x16, #0x740]
    // 0x810374: stp             x16, x0, [SP]
    // 0x810378: r4 = 0
    //     0x810378: movz            x4, #0
    // 0x81037c: ldr             x0, [SP, #8]
    // 0x810380: r16 = UnlinkedCall_0x5f3c08
    //     0x810380: add             x16, PP, #0x35, lsl #12  ; [pp+0x35af0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x810384: add             x16, x16, #0xaf0
    // 0x810388: ldp             x5, lr, [x16]
    // 0x81038c: blr             lr
    // 0x810390: mov             x3, x0
    // 0x810394: r2 = Null
    //     0x810394: mov             x2, NULL
    // 0x810398: r1 = Null
    //     0x810398: mov             x1, NULL
    // 0x81039c: stur            x3, [fp, #-8]
    // 0x8103a0: branchIfSmi(r0, 0x8103c8)
    //     0x8103a0: tbz             w0, #0, #0x8103c8
    // 0x8103a4: r4 = LoadClassIdInstr(r0)
    //     0x8103a4: ldur            x4, [x0, #-1]
    //     0x8103a8: ubfx            x4, x4, #0xc, #0x14
    // 0x8103ac: sub             x4, x4, #0x3c
    // 0x8103b0: cmp             x4, #1
    // 0x8103b4: b.ls            #0x8103c8
    // 0x8103b8: r8 = int?
    //     0x8103b8: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x8103bc: r3 = Null
    //     0x8103bc: add             x3, PP, #0x35, lsl #12  ; [pp+0x35b00] Null
    //     0x8103c0: ldr             x3, [x3, #0xb00]
    // 0x8103c4: r0 = int?()
    //     0x8103c4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x8103c8: ldur            x0, [fp, #-8]
    // 0x8103cc: cmp             w0, NULL
    // 0x8103d0: b.ne            #0x8103dc
    // 0x8103d4: r2 = 0
    //     0x8103d4: movz            x2, #0
    // 0x8103d8: b               #0x8103ec
    // 0x8103dc: r1 = LoadInt32Instr(r0)
    //     0x8103dc: sbfx            x1, x0, #1, #0x1f
    //     0x8103e0: tbz             w0, #0, #0x8103e8
    //     0x8103e4: ldur            x1, [x0, #7]
    // 0x8103e8: mov             x2, x1
    // 0x8103ec: stur            x2, [fp, #-0x10]
    // 0x8103f0: r16 = <DonationRepository>
    //     0x8103f0: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x8103f4: ldr             x16, [x16, #0xb0]
    // 0x8103f8: r30 = "donation_repo"
    //     0x8103f8: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x8103fc: ldr             lr, [lr, #0xb8]
    // 0x810400: stp             lr, x16, [SP]
    // 0x810404: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x810404: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x810408: r0 = Inst.find()
    //     0x810408: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x81040c: stur            x0, [fp, #-8]
    // 0x810410: r0 = GetNavigation.arguments()
    //     0x810410: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x810414: r16 = "type"
    //     0x810414: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x810418: stp             x16, x0, [SP]
    // 0x81041c: r4 = 0
    //     0x81041c: movz            x4, #0
    // 0x810420: ldr             x0, [SP, #8]
    // 0x810424: r16 = UnlinkedCall_0x5f3c08
    //     0x810424: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b10] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x810428: add             x16, x16, #0xb10
    // 0x81042c: ldp             x5, lr, [x16]
    // 0x810430: blr             lr
    // 0x810434: mov             x3, x0
    // 0x810438: r2 = Null
    //     0x810438: mov             x2, NULL
    // 0x81043c: r1 = Null
    //     0x81043c: mov             x1, NULL
    // 0x810440: stur            x3, [fp, #-0x18]
    // 0x810444: r4 = 60
    //     0x810444: movz            x4, #0x3c
    // 0x810448: branchIfSmi(r0, 0x810454)
    //     0x810448: tbz             w0, #0, #0x810454
    // 0x81044c: r4 = LoadClassIdInstr(r0)
    //     0x81044c: ldur            x4, [x0, #-1]
    //     0x810450: ubfx            x4, x4, #0xc, #0x14
    // 0x810454: r17 = 6845
    //     0x810454: movz            x17, #0x1abd
    // 0x810458: cmp             x4, x17
    // 0x81045c: b.eq            #0x810474
    // 0x810460: r8 = PaymentType?
    //     0x810460: add             x8, PP, #0x35, lsl #12  ; [pp+0x35b20] Type: PaymentType?
    //     0x810464: ldr             x8, [x8, #0xb20]
    // 0x810468: r3 = Null
    //     0x810468: add             x3, PP, #0x35, lsl #12  ; [pp+0x35b28] Null
    //     0x81046c: ldr             x3, [x3, #0xb28]
    // 0x810470: r0 = DefaultNullableTypeTest()
    //     0x810470: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x810474: ldur            x0, [fp, #-0x18]
    // 0x810478: cmp             w0, NULL
    // 0x81047c: b.ne            #0x81048c
    // 0x810480: r5 = Instance_PaymentType
    //     0x810480: add             x5, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0x810484: ldr             x5, [x5, #0x5d8]
    // 0x810488: b               #0x810490
    // 0x81048c: mov             x5, x0
    // 0x810490: stur            x5, [fp, #-0x18]
    // 0x810494: r1 = <Campaign>
    //     0x810494: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0x810498: ldr             x1, [x1, #0x598]
    // 0x81049c: r0 = CampaignDetailController()
    //     0x81049c: bl              #0x810600  ; AllocateCampaignDetailControllerStub -> CampaignDetailController (size=0x50)
    // 0x8104a0: mov             x1, x0
    // 0x8104a4: ldur            x2, [fp, #-0x10]
    // 0x8104a8: ldur            x3, [fp, #-8]
    // 0x8104ac: ldur            x5, [fp, #-0x18]
    // 0x8104b0: stur            x0, [fp, #-8]
    // 0x8104b4: r0 = CampaignDetailController()
    //     0x8104b4: bl              #0x8104d0  ; [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::CampaignDetailController
    // 0x8104b8: ldur            x0, [fp, #-8]
    // 0x8104bc: LeaveFrame
    //     0x8104bc: mov             SP, fp
    //     0x8104c0: ldp             fp, lr, [SP], #0x10
    // 0x8104c4: ret
    //     0x8104c4: ret             
    // 0x8104c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8104c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8104cc: b               #0x81034c
  }
}
