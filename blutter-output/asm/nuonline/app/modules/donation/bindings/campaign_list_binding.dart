// lib: , url: package:nuonline/app/modules/donation/bindings/campaign_list_binding.dart

// class id: 1050193, size: 0x8
class :: {
}

// class id: 2180, size: 0xc, field offset: 0x8
class CampaignListBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x81060c, size: 0x8c
    // 0x81060c: EnterFrame
    //     0x81060c: stp             fp, lr, [SP, #-0x10]!
    //     0x810610: mov             fp, SP
    // 0x810614: AllocStack(0x20)
    //     0x810614: sub             SP, SP, #0x20
    // 0x810618: SetupParameters(CampaignListBinding this /* r1 => r1, fp-0x8 */)
    //     0x810618: stur            x1, [fp, #-8]
    // 0x81061c: CheckStackOverflow
    //     0x81061c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810620: cmp             SP, x16
    //     0x810624: b.ls            #0x810690
    // 0x810628: r1 = 1
    //     0x810628: movz            x1, #0x1
    // 0x81062c: r0 = AllocateContext()
    //     0x81062c: bl              #0xec126c  ; AllocateContextStub
    // 0x810630: mov             x1, x0
    // 0x810634: ldur            x0, [fp, #-8]
    // 0x810638: stur            x1, [fp, #-0x10]
    // 0x81063c: StoreField: r1->field_f = r0
    //     0x81063c: stur            w0, [x1, #0xf]
    // 0x810640: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x810640: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x810644: ldr             x0, [x0, #0x2670]
    //     0x810648: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81064c: cmp             w0, w16
    //     0x810650: b.ne            #0x81065c
    //     0x810654: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x810658: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81065c: ldur            x2, [fp, #-0x10]
    // 0x810660: r1 = Function '<anonymous closure>':.
    //     0x810660: add             x1, PP, #0x36, lsl #12  ; [pp+0x36280] AnonymousClosure: (0x810698), in [package:nuonline/app/modules/donation/bindings/campaign_list_binding.dart] CampaignListBinding::dependencies (0x81060c)
    //     0x810664: ldr             x1, [x1, #0x280]
    // 0x810668: r0 = AllocateClosure()
    //     0x810668: bl              #0xec1630  ; AllocateClosureStub
    // 0x81066c: r16 = <CampaignListController>
    //     0x81066c: add             x16, PP, #0x24, lsl #12  ; [pp+0x248e8] TypeArguments: <CampaignListController>
    //     0x810670: ldr             x16, [x16, #0x8e8]
    // 0x810674: stp             x0, x16, [SP]
    // 0x810678: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x810678: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x81067c: r0 = Inst.lazyPut()
    //     0x81067c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x810680: r0 = Null
    //     0x810680: mov             x0, NULL
    // 0x810684: LeaveFrame
    //     0x810684: mov             SP, fp
    //     0x810688: ldp             fp, lr, [SP], #0x10
    // 0x81068c: ret
    //     0x81068c: ret             
    // 0x810690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810694: b               #0x810628
  }
  [closure] CampaignListController <anonymous closure>(dynamic) {
    // ** addr: 0x810698, size: 0xb8
    // 0x810698: EnterFrame
    //     0x810698: stp             fp, lr, [SP, #-0x10]!
    //     0x81069c: mov             fp, SP
    // 0x8106a0: AllocStack(0x28)
    //     0x8106a0: sub             SP, SP, #0x28
    // 0x8106a4: SetupParameters()
    //     0x8106a4: ldr             x0, [fp, #0x10]
    //     0x8106a8: ldur            w1, [x0, #0x17]
    //     0x8106ac: add             x1, x1, HEAP, lsl #32
    // 0x8106b0: CheckStackOverflow
    //     0x8106b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8106b4: cmp             SP, x16
    //     0x8106b8: b.ls            #0x810748
    // 0x8106bc: LoadField: r0 = r1->field_f
    //     0x8106bc: ldur            w0, [x1, #0xf]
    // 0x8106c0: DecompressPointer r0
    //     0x8106c0: add             x0, x0, HEAP, lsl #32
    // 0x8106c4: LoadField: r1 = r0->field_7
    //     0x8106c4: ldur            w1, [x0, #7]
    // 0x8106c8: DecompressPointer r1
    //     0x8106c8: add             x1, x1, HEAP, lsl #32
    // 0x8106cc: stur            x1, [fp, #-8]
    // 0x8106d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8106d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8106d4: ldr             x0, [x0, #0x2670]
    //     0x8106d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8106dc: cmp             w0, w16
    //     0x8106e0: b.ne            #0x8106ec
    //     0x8106e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8106e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8106ec: r16 = <DonationRepository>
    //     0x8106ec: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x8106f0: ldr             x16, [x16, #0xb0]
    // 0x8106f4: r30 = "donation_repo"
    //     0x8106f4: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x8106f8: ldr             lr, [lr, #0xb8]
    // 0x8106fc: stp             lr, x16, [SP]
    // 0x810700: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x810700: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x810704: r0 = Inst.find()
    //     0x810704: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x810708: r1 = <List<Campaign>, Campaign>
    //     0x810708: add             x1, PP, #0x30, lsl #12  ; [pp+0x303c0] TypeArguments: <List<Campaign>, Campaign>
    //     0x81070c: ldr             x1, [x1, #0x3c0]
    // 0x810710: stur            x0, [fp, #-0x10]
    // 0x810714: r0 = CampaignListController()
    //     0x810714: bl              #0x81089c  ; AllocateCampaignListControllerStub -> CampaignListController (size=0x40)
    // 0x810718: stur            x0, [fp, #-0x18]
    // 0x81071c: ldur            x16, [fp, #-8]
    // 0x810720: str             x16, [SP]
    // 0x810724: mov             x1, x0
    // 0x810728: ldur            x2, [fp, #-0x10]
    // 0x81072c: r4 = const [0, 0x3, 0x1, 0x2, type, 0x2, null]
    //     0x81072c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36288] List(7) [0, 0x3, 0x1, 0x2, "type", 0x2, Null]
    //     0x810730: ldr             x4, [x4, #0x288]
    // 0x810734: r0 = CampaignListController()
    //     0x810734: bl              #0x810750  ; [package:nuonline/app/modules/donation/controllers/campaign_list_controller.dart] CampaignListController::CampaignListController
    // 0x810738: ldur            x0, [fp, #-0x18]
    // 0x81073c: LeaveFrame
    //     0x81073c: mov             SP, fp
    //     0x810740: ldp             fp, lr, [SP], #0x10
    // 0x810744: ret
    //     0x810744: ret             
    // 0x810748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81074c: b               #0x8106bc
  }
}
