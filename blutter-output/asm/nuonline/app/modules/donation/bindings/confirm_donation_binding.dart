// lib: , url: package:nuonline/app/modules/donation/bindings/confirm_donation_binding.dart

// class id: 1050194, size: 0x8
class :: {
}

// class id: 2179, size: 0x8, field offset: 0x8
class ConfirmDonationBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x8108a8, size: 0x70
    // 0x8108a8: EnterFrame
    //     0x8108a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8108ac: mov             fp, SP
    // 0x8108b0: AllocStack(0x10)
    //     0x8108b0: sub             SP, SP, #0x10
    // 0x8108b4: CheckStackOverflow
    //     0x8108b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8108b8: cmp             SP, x16
    //     0x8108bc: b.ls            #0x810910
    // 0x8108c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8108c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8108c4: ldr             x0, [x0, #0x2670]
    //     0x8108c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8108cc: cmp             w0, w16
    //     0x8108d0: b.ne            #0x8108dc
    //     0x8108d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8108d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8108dc: r1 = Function '<anonymous closure>':.
    //     0x8108dc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36178] AnonymousClosure: (0x810918), in [package:nuonline/app/modules/donation/bindings/confirm_donation_binding.dart] ConfirmDonationBinding::dependencies (0x8108a8)
    //     0x8108e0: ldr             x1, [x1, #0x178]
    // 0x8108e4: r2 = Null
    //     0x8108e4: mov             x2, NULL
    // 0x8108e8: r0 = AllocateClosure()
    //     0x8108e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8108ec: r16 = <ConfirmDonationController>
    //     0x8108ec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24900] TypeArguments: <ConfirmDonationController>
    //     0x8108f0: ldr             x16, [x16, #0x900]
    // 0x8108f4: stp             x0, x16, [SP]
    // 0x8108f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8108f8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8108fc: r0 = Inst.lazyPut()
    //     0x8108fc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x810900: r0 = Null
    //     0x810900: mov             x0, NULL
    // 0x810904: LeaveFrame
    //     0x810904: mov             SP, fp
    //     0x810908: ldp             fp, lr, [SP], #0x10
    // 0x81090c: ret
    //     0x81090c: ret             
    // 0x810910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810914: b               #0x8108c0
  }
  [closure] ConfirmDonationController <anonymous closure>(dynamic) {
    // ** addr: 0x810918, size: 0xc8
    // 0x810918: EnterFrame
    //     0x810918: stp             fp, lr, [SP, #-0x10]!
    //     0x81091c: mov             fp, SP
    // 0x810920: AllocStack(0x28)
    //     0x810920: sub             SP, SP, #0x28
    // 0x810924: CheckStackOverflow
    //     0x810924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810928: cmp             SP, x16
    //     0x81092c: b.ls            #0x8109d8
    // 0x810930: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x810930: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x810934: ldr             x0, [x0, #0x2670]
    //     0x810938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81093c: cmp             w0, w16
    //     0x810940: b.ne            #0x81094c
    //     0x810944: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x810948: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81094c: r0 = GetNavigation.arguments()
    //     0x81094c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x810950: mov             x3, x0
    // 0x810954: r2 = Null
    //     0x810954: mov             x2, NULL
    // 0x810958: r1 = Null
    //     0x810958: mov             x1, NULL
    // 0x81095c: stur            x3, [fp, #-8]
    // 0x810960: r8 = Map<String, dynamic>
    //     0x810960: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x810964: r3 = Null
    //     0x810964: add             x3, PP, #0x36, lsl #12  ; [pp+0x36180] Null
    //     0x810968: ldr             x3, [x3, #0x180]
    // 0x81096c: r0 = Map<String, dynamic>()
    //     0x81096c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x810970: ldur            x2, [fp, #-8]
    // 0x810974: r1 = <ZakatTypes>
    //     0x810974: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0x810978: ldr             x1, [x1, #0x68]
    // 0x81097c: r0 = TransactionRequest.fromMap()
    //     0x81097c: bl              #0x8109ec  ; [package:nuonline/app/data/models/transaction.dart] TransactionRequest::TransactionRequest.fromMap
    // 0x810980: stur            x0, [fp, #-8]
    // 0x810984: r16 = <DonationRepository>
    //     0x810984: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x810988: ldr             x16, [x16, #0xb0]
    // 0x81098c: r30 = "donation_repo"
    //     0x81098c: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x810990: ldr             lr, [lr, #0xb8]
    // 0x810994: stp             lr, x16, [SP]
    // 0x810998: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x810998: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x81099c: r0 = Inst.find()
    //     0x81099c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8109a0: stur            x0, [fp, #-0x10]
    // 0x8109a4: r0 = ConfirmDonationController()
    //     0x8109a4: bl              #0x8109e0  ; AllocateConfirmDonationControllerStub -> ConfirmDonationController (size=0x30)
    // 0x8109a8: mov             x2, x0
    // 0x8109ac: ldur            x0, [fp, #-8]
    // 0x8109b0: stur            x2, [fp, #-0x18]
    // 0x8109b4: StoreField: r2->field_2b = r0
    //     0x8109b4: stur            w0, [x2, #0x2b]
    // 0x8109b8: ldur            x0, [fp, #-0x10]
    // 0x8109bc: StoreField: r2->field_27 = r0
    //     0x8109bc: stur            w0, [x2, #0x27]
    // 0x8109c0: mov             x1, x2
    // 0x8109c4: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8109c4: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8109c8: ldur            x0, [fp, #-0x18]
    // 0x8109cc: LeaveFrame
    //     0x8109cc: mov             SP, fp
    //     0x8109d0: ldp             fp, lr, [SP], #0x10
    // 0x8109d4: ret
    //     0x8109d4: ret             
    // 0x8109d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8109d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8109dc: b               #0x810930
  }
}
