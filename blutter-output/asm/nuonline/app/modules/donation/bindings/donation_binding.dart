// lib: , url: package:nuonline/app/modules/donation/bindings/donation_binding.dart

// class id: 1050195, size: 0x8
class :: {
}

// class id: 2178, size: 0x8, field offset: 0x8
class DonationBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x810ff0, size: 0x7c
    // 0x810ff0: EnterFrame
    //     0x810ff0: stp             fp, lr, [SP, #-0x10]!
    //     0x810ff4: mov             fp, SP
    // 0x810ff8: AllocStack(0x10)
    //     0x810ff8: sub             SP, SP, #0x10
    // 0x810ffc: CheckStackOverflow
    //     0x810ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811000: cmp             SP, x16
    //     0x811004: b.ls            #0x811064
    // 0x811008: r0 = HistoryTabBinding()
    //     0x811008: bl              #0x81106c  ; AllocateHistoryTabBindingStub -> HistoryTabBinding (size=0x8)
    // 0x81100c: mov             x1, x0
    // 0x811010: r0 = dependencies()
    //     0x811010: bl              #0x8112ec  ; [package:nuonline/app/modules/donation/bindings/history_tab_binding.dart] HistoryTabBinding::dependencies
    // 0x811014: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811014: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811018: ldr             x0, [x0, #0x2670]
    //     0x81101c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811020: cmp             w0, w16
    //     0x811024: b.ne            #0x811030
    //     0x811028: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x81102c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811030: r1 = Function '<anonymous closure>':.
    //     0x811030: add             x1, PP, #0x36, lsl #12  ; [pp+0x36170] AnonymousClosure: (0x811078), in [package:nuonline/app/modules/donation/bindings/donation_binding.dart] DonationBinding::dependencies (0x810ff0)
    //     0x811034: ldr             x1, [x1, #0x170]
    // 0x811038: r2 = Null
    //     0x811038: mov             x2, NULL
    // 0x81103c: r0 = AllocateClosure()
    //     0x81103c: bl              #0xec1630  ; AllocateClosureStub
    // 0x811040: r16 = <DonationController>
    //     0x811040: add             x16, PP, #0x24, lsl #12  ; [pp+0x248d8] TypeArguments: <DonationController>
    //     0x811044: ldr             x16, [x16, #0x8d8]
    // 0x811048: stp             x0, x16, [SP]
    // 0x81104c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x81104c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811050: r0 = Inst.lazyPut()
    //     0x811050: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811054: r0 = Null
    //     0x811054: mov             x0, NULL
    // 0x811058: LeaveFrame
    //     0x811058: mov             SP, fp
    //     0x81105c: ldp             fp, lr, [SP], #0x10
    // 0x811060: ret
    //     0x811060: ret             
    // 0x811064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811064: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811068: b               #0x811008
  }
  [closure] DonationController <anonymous closure>(dynamic) {
    // ** addr: 0x811078, size: 0x90
    // 0x811078: EnterFrame
    //     0x811078: stp             fp, lr, [SP, #-0x10]!
    //     0x81107c: mov             fp, SP
    // 0x811080: AllocStack(0x20)
    //     0x811080: sub             SP, SP, #0x20
    // 0x811084: CheckStackOverflow
    //     0x811084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811088: cmp             SP, x16
    //     0x81108c: b.ls            #0x811100
    // 0x811090: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811090: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811094: ldr             x0, [x0, #0x2670]
    //     0x811098: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81109c: cmp             w0, w16
    //     0x8110a0: b.ne            #0x8110ac
    //     0x8110a4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8110a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8110ac: r16 = <DonationRepository>
    //     0x8110ac: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x8110b0: ldr             x16, [x16, #0xb0]
    // 0x8110b4: r30 = "donation_repo"
    //     0x8110b4: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x8110b8: ldr             lr, [lr, #0xb8]
    // 0x8110bc: stp             lr, x16, [SP]
    // 0x8110c0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8110c0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8110c4: r0 = Inst.find()
    //     0x8110c4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8110c8: r1 = <List<Transaction>>
    //     0x8110c8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b090] TypeArguments: <List<Transaction>>
    //     0x8110cc: ldr             x1, [x1, #0x90]
    // 0x8110d0: stur            x0, [fp, #-8]
    // 0x8110d4: r0 = DonationController()
    //     0x8110d4: bl              #0x811108  ; AllocateDonationControllerStub -> DonationController (size=0x30)
    // 0x8110d8: mov             x2, x0
    // 0x8110dc: ldur            x0, [fp, #-8]
    // 0x8110e0: stur            x2, [fp, #-0x10]
    // 0x8110e4: StoreField: r2->field_2b = r0
    //     0x8110e4: stur            w0, [x2, #0x2b]
    // 0x8110e8: mov             x1, x2
    // 0x8110ec: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8110ec: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8110f0: ldur            x0, [fp, #-0x10]
    // 0x8110f4: LeaveFrame
    //     0x8110f4: mov             SP, fp
    //     0x8110f8: ldp             fp, lr, [SP], #0x10
    // 0x8110fc: ret
    //     0x8110fc: ret             
    // 0x811100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811104: b               #0x811090
  }
}
