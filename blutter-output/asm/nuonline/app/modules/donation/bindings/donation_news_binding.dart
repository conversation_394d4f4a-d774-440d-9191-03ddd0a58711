// lib: , url: package:nuonline/app/modules/donation/bindings/donation_news_binding.dart

// class id: 1050196, size: 0x8
class :: {
}

// class id: 2177, size: 0x10, field offset: 0x8
class DonationNewsBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x811114, size: 0x8c
    // 0x811114: EnterFrame
    //     0x811114: stp             fp, lr, [SP, #-0x10]!
    //     0x811118: mov             fp, SP
    // 0x81111c: AllocStack(0x20)
    //     0x81111c: sub             SP, SP, #0x20
    // 0x811120: SetupParameters(DonationNewsBinding this /* r1 => r1, fp-0x8 */)
    //     0x811120: stur            x1, [fp, #-8]
    // 0x811124: CheckStackOverflow
    //     0x811124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811128: cmp             SP, x16
    //     0x81112c: b.ls            #0x811198
    // 0x811130: r1 = 1
    //     0x811130: movz            x1, #0x1
    // 0x811134: r0 = AllocateContext()
    //     0x811134: bl              #0xec126c  ; AllocateContextStub
    // 0x811138: mov             x1, x0
    // 0x81113c: ldur            x0, [fp, #-8]
    // 0x811140: stur            x1, [fp, #-0x10]
    // 0x811144: StoreField: r1->field_f = r0
    //     0x811144: stur            w0, [x1, #0xf]
    // 0x811148: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811148: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81114c: ldr             x0, [x0, #0x2670]
    //     0x811150: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811154: cmp             w0, w16
    //     0x811158: b.ne            #0x811164
    //     0x81115c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811160: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811164: ldur            x2, [fp, #-0x10]
    // 0x811168: r1 = Function '<anonymous closure>':.
    //     0x811168: add             x1, PP, #0x36, lsl #12  ; [pp+0x36148] AnonymousClosure: (0x8111a0), in [package:nuonline/app/modules/donation/bindings/donation_news_binding.dart] DonationNewsBinding::dependencies (0x811114)
    //     0x81116c: ldr             x1, [x1, #0x148]
    // 0x811170: r0 = AllocateClosure()
    //     0x811170: bl              #0xec1630  ; AllocateClosureStub
    // 0x811174: r16 = <DonationNewsController>
    //     0x811174: add             x16, PP, #0x36, lsl #12  ; [pp+0x36150] TypeArguments: <DonationNewsController>
    //     0x811178: ldr             x16, [x16, #0x150]
    // 0x81117c: stp             x0, x16, [SP]
    // 0x811180: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811180: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811184: r0 = Inst.lazyPut()
    //     0x811184: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811188: r0 = Null
    //     0x811188: mov             x0, NULL
    // 0x81118c: LeaveFrame
    //     0x81118c: mov             SP, fp
    //     0x811190: ldp             fp, lr, [SP], #0x10
    // 0x811194: ret
    //     0x811194: ret             
    // 0x811198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81119c: b               #0x811130
  }
  [closure] DonationNewsController <anonymous closure>(dynamic) {
    // ** addr: 0x8111a0, size: 0x140
    // 0x8111a0: EnterFrame
    //     0x8111a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8111a4: mov             fp, SP
    // 0x8111a8: AllocStack(0x28)
    //     0x8111a8: sub             SP, SP, #0x28
    // 0x8111ac: SetupParameters()
    //     0x8111ac: ldr             x0, [fp, #0x10]
    //     0x8111b0: ldur            w1, [x0, #0x17]
    //     0x8111b4: add             x1, x1, HEAP, lsl #32
    //     0x8111b8: stur            x1, [fp, #-8]
    // 0x8111bc: CheckStackOverflow
    //     0x8111bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8111c0: cmp             SP, x16
    //     0x8111c4: b.ls            #0x8112d8
    // 0x8111c8: LoadField: r0 = r1->field_f
    //     0x8111c8: ldur            w0, [x1, #0xf]
    // 0x8111cc: DecompressPointer r0
    //     0x8111cc: add             x0, x0, HEAP, lsl #32
    // 0x8111d0: LoadField: r2 = r0->field_7
    //     0x8111d0: ldur            w2, [x0, #7]
    // 0x8111d4: DecompressPointer r2
    //     0x8111d4: add             x2, x2, HEAP, lsl #32
    // 0x8111d8: cmp             w2, NULL
    // 0x8111dc: b.ne            #0x811244
    // 0x8111e0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8111e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8111e4: ldr             x0, [x0, #0x2670]
    //     0x8111e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8111ec: cmp             w0, w16
    //     0x8111f0: b.ne            #0x8111fc
    //     0x8111f4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8111f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8111fc: r0 = GetNavigation.arguments()
    //     0x8111fc: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x811200: mov             x3, x0
    // 0x811204: r2 = Null
    //     0x811204: mov             x2, NULL
    // 0x811208: r1 = Null
    //     0x811208: mov             x1, NULL
    // 0x81120c: stur            x3, [fp, #-0x10]
    // 0x811210: r4 = 60
    //     0x811210: movz            x4, #0x3c
    // 0x811214: branchIfSmi(r0, 0x811220)
    //     0x811214: tbz             w0, #0, #0x811220
    // 0x811218: r4 = LoadClassIdInstr(r0)
    //     0x811218: ldur            x4, [x0, #-1]
    //     0x81121c: ubfx            x4, x4, #0xc, #0x14
    // 0x811220: cmp             x4, #0x48d
    // 0x811224: b.eq            #0x81123c
    // 0x811228: r8 = Merchant
    //     0x811228: add             x8, PP, #0x36, lsl #12  ; [pp+0x36158] Type: Merchant
    //     0x81122c: ldr             x8, [x8, #0x158]
    // 0x811230: r3 = Null
    //     0x811230: add             x3, PP, #0x36, lsl #12  ; [pp+0x36160] Null
    //     0x811234: ldr             x3, [x3, #0x160]
    // 0x811238: r0 = DefaultTypeTest()
    //     0x811238: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x81123c: ldur            x1, [fp, #-0x10]
    // 0x811240: b               #0x811248
    // 0x811244: mov             x1, x2
    // 0x811248: ldur            x0, [fp, #-8]
    // 0x81124c: stur            x1, [fp, #-0x10]
    // 0x811250: LoadField: r2 = r0->field_f
    //     0x811250: ldur            w2, [x0, #0xf]
    // 0x811254: DecompressPointer r2
    //     0x811254: add             x2, x2, HEAP, lsl #32
    // 0x811258: LoadField: r0 = r2->field_b
    //     0x811258: ldur            w0, [x2, #0xb]
    // 0x81125c: DecompressPointer r0
    //     0x81125c: add             x0, x0, HEAP, lsl #32
    // 0x811260: stur            x0, [fp, #-8]
    // 0x811264: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811264: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811268: ldr             x0, [x0, #0x2670]
    //     0x81126c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811270: cmp             w0, w16
    //     0x811274: b.ne            #0x811280
    //     0x811278: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x81127c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811280: r16 = <DonationRepository>
    //     0x811280: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x811284: ldr             x16, [x16, #0xb0]
    // 0x811288: r30 = "donation_repo"
    //     0x811288: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x81128c: ldr             lr, [lr, #0xb8]
    // 0x811290: stp             lr, x16, [SP]
    // 0x811294: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x811294: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x811298: r0 = Inst.find()
    //     0x811298: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x81129c: r0 = DonationNewsController()
    //     0x81129c: bl              #0x8112e0  ; AllocateDonationNewsControllerStub -> DonationNewsController (size=0x30)
    // 0x8112a0: mov             x2, x0
    // 0x8112a4: r0 = Sentinel
    //     0x8112a4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8112a8: stur            x2, [fp, #-0x18]
    // 0x8112ac: StoreField: r2->field_2b = r0
    //     0x8112ac: stur            w0, [x2, #0x2b]
    // 0x8112b0: ldur            x0, [fp, #-0x10]
    // 0x8112b4: StoreField: r2->field_23 = r0
    //     0x8112b4: stur            w0, [x2, #0x23]
    // 0x8112b8: ldur            x0, [fp, #-8]
    // 0x8112bc: StoreField: r2->field_27 = r0
    //     0x8112bc: stur            w0, [x2, #0x27]
    // 0x8112c0: mov             x1, x2
    // 0x8112c4: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8112c4: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8112c8: ldur            x0, [fp, #-0x18]
    // 0x8112cc: LeaveFrame
    //     0x8112cc: mov             SP, fp
    //     0x8112d0: ldp             fp, lr, [SP], #0x10
    // 0x8112d4: ret
    //     0x8112d4: ret             
    // 0x8112d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8112d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8112dc: b               #0x8111c8
  }
}
