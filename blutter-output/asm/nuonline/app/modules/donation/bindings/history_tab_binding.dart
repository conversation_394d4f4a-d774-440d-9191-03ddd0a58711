// lib: , url: package:nuonline/app/modules/donation/bindings/history_tab_binding.dart

// class id: 1050197, size: 0x8
class :: {
}

// class id: 2176, size: 0x8, field offset: 0x8
class HistoryTabBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x8112ec, size: 0x70
    // 0x8112ec: EnterFrame
    //     0x8112ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8112f0: mov             fp, SP
    // 0x8112f4: AllocStack(0x10)
    //     0x8112f4: sub             SP, SP, #0x10
    // 0x8112f8: CheckStackOverflow
    //     0x8112f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8112fc: cmp             SP, x16
    //     0x811300: b.ls            #0x811354
    // 0x811304: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811304: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811308: ldr             x0, [x0, #0x2670]
    //     0x81130c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811310: cmp             w0, w16
    //     0x811314: b.ne            #0x811320
    //     0x811318: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x81131c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811320: r1 = Function '<anonymous closure>':.
    //     0x811320: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ae0] AnonymousClosure: (0x81135c), in [package:nuonline/app/modules/donation/bindings/history_tab_binding.dart] HistoryTabBinding::dependencies (0x8112ec)
    //     0x811324: ldr             x1, [x1, #0xae0]
    // 0x811328: r2 = Null
    //     0x811328: mov             x2, NULL
    // 0x81132c: r0 = AllocateClosure()
    //     0x81132c: bl              #0xec1630  ; AllocateClosureStub
    // 0x811330: r16 = <HistoryTabController>
    //     0x811330: add             x16, PP, #0x24, lsl #12  ; [pp+0x24910] TypeArguments: <HistoryTabController>
    //     0x811334: ldr             x16, [x16, #0x910]
    // 0x811338: stp             x0, x16, [SP]
    // 0x81133c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x81133c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811340: r0 = Inst.lazyPut()
    //     0x811340: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811344: r0 = Null
    //     0x811344: mov             x0, NULL
    // 0x811348: LeaveFrame
    //     0x811348: mov             SP, fp
    //     0x81134c: ldp             fp, lr, [SP], #0x10
    // 0x811350: ret
    //     0x811350: ret             
    // 0x811354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811358: b               #0x811304
  }
  [closure] HistoryTabController <anonymous closure>(dynamic) {
    // ** addr: 0x81135c, size: 0xd0
    // 0x81135c: EnterFrame
    //     0x81135c: stp             fp, lr, [SP, #-0x10]!
    //     0x811360: mov             fp, SP
    // 0x811364: AllocStack(0x20)
    //     0x811364: sub             SP, SP, #0x20
    // 0x811368: CheckStackOverflow
    //     0x811368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81136c: cmp             SP, x16
    //     0x811370: b.ls            #0x811424
    // 0x811374: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811374: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811378: ldr             x0, [x0, #0x2670]
    //     0x81137c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811380: cmp             w0, w16
    //     0x811384: b.ne            #0x811390
    //     0x811388: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x81138c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811390: r16 = <DonationRepository>
    //     0x811390: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x811394: ldr             x16, [x16, #0xb0]
    // 0x811398: r30 = "donation_repo"
    //     0x811398: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x81139c: ldr             lr, [lr, #0xb8]
    // 0x8113a0: stp             lr, x16, [SP]
    // 0x8113a4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8113a4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8113a8: r0 = Inst.find()
    //     0x8113a8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8113ac: stur            x0, [fp, #-8]
    // 0x8113b0: r0 = HistoryTabController()
    //     0x8113b0: bl              #0x81144c  ; AllocateHistoryTabControllerStub -> HistoryTabController (size=0x30)
    // 0x8113b4: mov             x2, x0
    // 0x8113b8: r0 = Sentinel
    //     0x8113b8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8113bc: stur            x2, [fp, #-0x10]
    // 0x8113c0: StoreField: r2->field_27 = r0
    //     0x8113c0: stur            w0, [x2, #0x27]
    // 0x8113c4: r1 = 0
    //     0x8113c4: movz            x1, #0
    // 0x8113c8: r0 = IntExtension.obs()
    //     0x8113c8: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x8113cc: ldur            x2, [fp, #-0x10]
    // 0x8113d0: StoreField: r2->field_2b = r0
    //     0x8113d0: stur            w0, [x2, #0x2b]
    //     0x8113d4: ldurb           w16, [x2, #-1]
    //     0x8113d8: ldurb           w17, [x0, #-1]
    //     0x8113dc: and             x16, x17, x16, lsr #2
    //     0x8113e0: tst             x16, HEAP, lsr #32
    //     0x8113e4: b.eq            #0x8113ec
    //     0x8113e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8113ec: ldur            x0, [fp, #-8]
    // 0x8113f0: StoreField: r2->field_23 = r0
    //     0x8113f0: stur            w0, [x2, #0x23]
    //     0x8113f4: ldurb           w16, [x2, #-1]
    //     0x8113f8: ldurb           w17, [x0, #-1]
    //     0x8113fc: and             x16, x17, x16, lsr #2
    //     0x811400: tst             x16, HEAP, lsr #32
    //     0x811404: b.eq            #0x81140c
    //     0x811408: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81140c: mov             x1, x2
    // 0x811410: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x811410: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x811414: ldur            x0, [fp, #-0x10]
    // 0x811418: LeaveFrame
    //     0x811418: mov             SP, fp
    //     0x81141c: ldp             fp, lr, [SP], #0x10
    // 0x811420: ret
    //     0x811420: ret             
    // 0x811424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811428: b               #0x811374
  }
}
