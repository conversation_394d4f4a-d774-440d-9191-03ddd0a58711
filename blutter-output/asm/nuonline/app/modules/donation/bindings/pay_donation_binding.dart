// lib: , url: package:nuonline/app/modules/donation/bindings/pay_donation_binding.dart

// class id: 1050198, size: 0x8
class :: {
}

// class id: 2175, size: 0x10, field offset: 0x8
class PayDonationBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x811458, size: 0x8c
    // 0x811458: EnterFrame
    //     0x811458: stp             fp, lr, [SP, #-0x10]!
    //     0x81145c: mov             fp, SP
    // 0x811460: AllocStack(0x20)
    //     0x811460: sub             SP, SP, #0x20
    // 0x811464: SetupParameters(PayDonationBinding this /* r1 => r1, fp-0x8 */)
    //     0x811464: stur            x1, [fp, #-8]
    // 0x811468: CheckStackOverflow
    //     0x811468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81146c: cmp             SP, x16
    //     0x811470: b.ls            #0x8114dc
    // 0x811474: r1 = 1
    //     0x811474: movz            x1, #0x1
    // 0x811478: r0 = AllocateContext()
    //     0x811478: bl              #0xec126c  ; AllocateContextStub
    // 0x81147c: mov             x1, x0
    // 0x811480: ldur            x0, [fp, #-8]
    // 0x811484: stur            x1, [fp, #-0x10]
    // 0x811488: StoreField: r1->field_f = r0
    //     0x811488: stur            w0, [x1, #0xf]
    // 0x81148c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x81148c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811490: ldr             x0, [x0, #0x2670]
    //     0x811494: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811498: cmp             w0, w16
    //     0x81149c: b.ne            #0x8114a8
    //     0x8114a0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8114a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8114a8: ldur            x2, [fp, #-0x10]
    // 0x8114ac: r1 = Function '<anonymous closure>':.
    //     0x8114ac: add             x1, PP, #0x36, lsl #12  ; [pp+0x36100] AnonymousClosure: (0x8114e4), in [package:nuonline/app/modules/donation/bindings/pay_donation_binding.dart] PayDonationBinding::dependencies (0x811458)
    //     0x8114b0: ldr             x1, [x1, #0x100]
    // 0x8114b4: r0 = AllocateClosure()
    //     0x8114b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8114b8: r16 = <PayDonationController>
    //     0x8114b8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24908] TypeArguments: <PayDonationController>
    //     0x8114bc: ldr             x16, [x16, #0x908]
    // 0x8114c0: stp             x0, x16, [SP]
    // 0x8114c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8114c4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8114c8: r0 = Inst.lazyPut()
    //     0x8114c8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8114cc: r0 = Null
    //     0x8114cc: mov             x0, NULL
    // 0x8114d0: LeaveFrame
    //     0x8114d0: mov             SP, fp
    //     0x8114d4: ldp             fp, lr, [SP], #0x10
    // 0x8114d8: ret
    //     0x8114d8: ret             
    // 0x8114dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8114dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8114e0: b               #0x811474
  }
  [closure] PayDonationController <anonymous closure>(dynamic) {
    // ** addr: 0x8114e4, size: 0x1dc
    // 0x8114e4: EnterFrame
    //     0x8114e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8114e8: mov             fp, SP
    // 0x8114ec: AllocStack(0x38)
    //     0x8114ec: sub             SP, SP, #0x38
    // 0x8114f0: SetupParameters()
    //     0x8114f0: ldr             x0, [fp, #0x10]
    //     0x8114f4: ldur            w1, [x0, #0x17]
    //     0x8114f8: add             x1, x1, HEAP, lsl #32
    // 0x8114fc: CheckStackOverflow
    //     0x8114fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811500: cmp             SP, x16
    //     0x811504: b.ls            #0x8116b8
    // 0x811508: LoadField: r0 = r1->field_f
    //     0x811508: ldur            w0, [x1, #0xf]
    // 0x81150c: DecompressPointer r0
    //     0x81150c: add             x0, x0, HEAP, lsl #32
    // 0x811510: LoadField: r6 = r0->field_7
    //     0x811510: ldur            w6, [x0, #7]
    // 0x811514: DecompressPointer r6
    //     0x811514: add             x6, x6, HEAP, lsl #32
    // 0x811518: stur            x6, [fp, #-0x10]
    // 0x81151c: LoadField: r7 = r0->field_b
    //     0x81151c: ldur            w7, [x0, #0xb]
    // 0x811520: DecompressPointer r7
    //     0x811520: add             x7, x7, HEAP, lsl #32
    // 0x811524: stur            x7, [fp, #-8]
    // 0x811528: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811528: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81152c: ldr             x0, [x0, #0x2670]
    //     0x811530: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811534: cmp             w0, w16
    //     0x811538: b.ne            #0x811544
    //     0x81153c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811540: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811544: r0 = GetNavigation.arguments()
    //     0x811544: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x811548: r2 = Null
    //     0x811548: mov             x2, NULL
    // 0x81154c: r1 = Null
    //     0x81154c: mov             x1, NULL
    // 0x811550: cmp             w0, NULL
    // 0x811554: b.eq            #0x8115ec
    // 0x811558: branchIfSmi(r0, 0x8115ec)
    //     0x811558: tbz             w0, #0, #0x8115ec
    // 0x81155c: r3 = LoadClassIdInstr(r0)
    //     0x81155c: ldur            x3, [x0, #-1]
    //     0x811560: ubfx            x3, x3, #0xc, #0x14
    // 0x811564: r17 = 6717
    //     0x811564: movz            x17, #0x1a3d
    // 0x811568: cmp             x3, x17
    // 0x81156c: b.eq            #0x8115f4
    // 0x811570: r4 = LoadClassIdInstr(r0)
    //     0x811570: ldur            x4, [x0, #-1]
    //     0x811574: ubfx            x4, x4, #0xc, #0x14
    // 0x811578: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x81157c: ldr             x3, [x3, #0x18]
    // 0x811580: ldr             x3, [x3, x4, lsl #3]
    // 0x811584: LoadField: r3 = r3->field_2b
    //     0x811584: ldur            w3, [x3, #0x2b]
    // 0x811588: DecompressPointer r3
    //     0x811588: add             x3, x3, HEAP, lsl #32
    // 0x81158c: cmp             w3, NULL
    // 0x811590: b.eq            #0x8115ec
    // 0x811594: LoadField: r3 = r3->field_f
    //     0x811594: ldur            w3, [x3, #0xf]
    // 0x811598: lsr             x3, x3, #3
    // 0x81159c: r17 = 6717
    //     0x81159c: movz            x17, #0x1a3d
    // 0x8115a0: cmp             x3, x17
    // 0x8115a4: b.eq            #0x8115f4
    // 0x8115a8: r3 = SubtypeTestCache
    //     0x8115a8: add             x3, PP, #0x36, lsl #12  ; [pp+0x36108] SubtypeTestCache
    //     0x8115ac: ldr             x3, [x3, #0x108]
    // 0x8115b0: r30 = Subtype1TestCacheStub
    //     0x8115b0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8115b4: LoadField: r30 = r30->field_7
    //     0x8115b4: ldur            lr, [lr, #7]
    // 0x8115b8: blr             lr
    // 0x8115bc: cmp             w7, NULL
    // 0x8115c0: b.eq            #0x8115cc
    // 0x8115c4: tbnz            w7, #4, #0x8115ec
    // 0x8115c8: b               #0x8115f4
    // 0x8115cc: r8 = Map
    //     0x8115cc: add             x8, PP, #0x36, lsl #12  ; [pp+0x36110] Type: Map
    //     0x8115d0: ldr             x8, [x8, #0x110]
    // 0x8115d4: r3 = SubtypeTestCache
    //     0x8115d4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36118] SubtypeTestCache
    //     0x8115d8: ldr             x3, [x3, #0x118]
    // 0x8115dc: r30 = InstanceOfStub
    //     0x8115dc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8115e0: LoadField: r30 = r30->field_7
    //     0x8115e0: ldur            lr, [lr, #7]
    // 0x8115e4: blr             lr
    // 0x8115e8: b               #0x8115f8
    // 0x8115ec: r0 = false
    //     0x8115ec: add             x0, NULL, #0x30  ; false
    // 0x8115f0: b               #0x8115f8
    // 0x8115f4: r0 = true
    //     0x8115f4: add             x0, NULL, #0x20  ; true
    // 0x8115f8: tbnz            w0, #4, #0x811604
    // 0x8115fc: r2 = Null
    //     0x8115fc: mov             x2, NULL
    // 0x811600: b               #0x811648
    // 0x811604: r0 = GetNavigation.arguments()
    //     0x811604: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x811608: mov             x3, x0
    // 0x81160c: r2 = Null
    //     0x81160c: mov             x2, NULL
    // 0x811610: r1 = Null
    //     0x811610: mov             x1, NULL
    // 0x811614: stur            x3, [fp, #-0x18]
    // 0x811618: r4 = 60
    //     0x811618: movz            x4, #0x3c
    // 0x81161c: branchIfSmi(r0, 0x811628)
    //     0x81161c: tbz             w0, #0, #0x811628
    // 0x811620: r4 = LoadClassIdInstr(r0)
    //     0x811620: ldur            x4, [x0, #-1]
    //     0x811624: ubfx            x4, x4, #0xc, #0x14
    // 0x811628: cmp             x4, #0x486
    // 0x81162c: b.eq            #0x811644
    // 0x811630: r8 = Campaign?
    //     0x811630: add             x8, PP, #0x36, lsl #12  ; [pp+0x36120] Type: Campaign?
    //     0x811634: ldr             x8, [x8, #0x120]
    // 0x811638: r3 = Null
    //     0x811638: add             x3, PP, #0x36, lsl #12  ; [pp+0x36128] Null
    //     0x81163c: ldr             x3, [x3, #0x128]
    // 0x811640: r0 = DefaultNullableTypeTest()
    //     0x811640: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x811644: ldur            x2, [fp, #-0x18]
    // 0x811648: stur            x2, [fp, #-0x18]
    // 0x81164c: r16 = <DonorRepository>
    //     0x81164c: add             x16, PP, #0xf, lsl #12  ; [pp+0xffd8] TypeArguments: <DonorRepository>
    //     0x811650: ldr             x16, [x16, #0xfd8]
    // 0x811654: r30 = "donor_repo"
    //     0x811654: add             lr, PP, #0xf, lsl #12  ; [pp+0xffe0] "donor_repo"
    //     0x811658: ldr             lr, [lr, #0xfe0]
    // 0x81165c: stp             lr, x16, [SP]
    // 0x811660: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x811660: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x811664: r0 = Inst.find()
    //     0x811664: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x811668: stur            x0, [fp, #-0x20]
    // 0x81166c: r16 = <AppStorage>
    //     0x81166c: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x811670: r30 = "app_storage"
    //     0x811670: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x811674: stp             lr, x16, [SP]
    // 0x811678: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x811678: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x81167c: r0 = Inst.find()
    //     0x81167c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x811680: stur            x0, [fp, #-0x28]
    // 0x811684: r0 = PayDonationController()
    //     0x811684: bl              #0x811934  ; AllocatePayDonationControllerStub -> PayDonationController (size=0x50)
    // 0x811688: mov             x1, x0
    // 0x81168c: ldur            x2, [fp, #-0x18]
    // 0x811690: ldur            x3, [fp, #-0x20]
    // 0x811694: ldur            x5, [fp, #-0x28]
    // 0x811698: ldur            x6, [fp, #-0x10]
    // 0x81169c: ldur            x7, [fp, #-8]
    // 0x8116a0: stur            x0, [fp, #-8]
    // 0x8116a4: r0 = PayDonationController()
    //     0x8116a4: bl              #0x8116c0  ; [package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart] PayDonationController::PayDonationController
    // 0x8116a8: ldur            x0, [fp, #-8]
    // 0x8116ac: LeaveFrame
    //     0x8116ac: mov             SP, fp
    //     0x8116b0: ldp             fp, lr, [SP], #0x10
    // 0x8116b4: ret
    //     0x8116b4: ret             
    // 0x8116b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8116b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8116bc: b               #0x811508
  }
}
