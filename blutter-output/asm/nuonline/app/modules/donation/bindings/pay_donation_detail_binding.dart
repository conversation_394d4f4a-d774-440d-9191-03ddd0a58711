// lib: , url: package:nuonline/app/modules/donation/bindings/pay_donation_detail_binding.dart

// class id: 1050199, size: 0x8
class :: {
}

// class id: 2174, size: 0x8, field offset: 0x8
class PayDonationDetailBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x811940, size: 0x70
    // 0x811940: EnterFrame
    //     0x811940: stp             fp, lr, [SP, #-0x10]!
    //     0x811944: mov             fp, SP
    // 0x811948: AllocStack(0x10)
    //     0x811948: sub             SP, SP, #0x10
    // 0x81194c: CheckStackOverflow
    //     0x81194c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811950: cmp             SP, x16
    //     0x811954: b.ls            #0x8119a8
    // 0x811958: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811958: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81195c: ldr             x0, [x0, #0x2670]
    //     0x811960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811964: cmp             w0, w16
    //     0x811968: b.ne            #0x811974
    //     0x81196c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811970: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811974: r1 = Function '<anonymous closure>':.
    //     0x811974: add             x1, PP, #0x36, lsl #12  ; [pp+0x360d8] AnonymousClosure: (0x8119b0), in [package:nuonline/app/modules/donation/bindings/pay_donation_detail_binding.dart] PayDonationDetailBinding::dependencies (0x811940)
    //     0x811978: ldr             x1, [x1, #0xd8]
    // 0x81197c: r2 = Null
    //     0x81197c: mov             x2, NULL
    // 0x811980: r0 = AllocateClosure()
    //     0x811980: bl              #0xec1630  ; AllocateClosureStub
    // 0x811984: r16 = <PayDonationDetailController>
    //     0x811984: add             x16, PP, #0x24, lsl #12  ; [pp+0x248f8] TypeArguments: <PayDonationDetailController>
    //     0x811988: ldr             x16, [x16, #0x8f8]
    // 0x81198c: stp             x0, x16, [SP]
    // 0x811990: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811990: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811994: r0 = Inst.lazyPut()
    //     0x811994: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811998: r0 = Null
    //     0x811998: mov             x0, NULL
    // 0x81199c: LeaveFrame
    //     0x81199c: mov             SP, fp
    //     0x8119a0: ldp             fp, lr, [SP], #0x10
    // 0x8119a4: ret
    //     0x8119a4: ret             
    // 0x8119a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8119a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8119ac: b               #0x811958
  }
  [closure] PayDonationDetailController <anonymous closure>(dynamic) {
    // ** addr: 0x8119b0, size: 0x104
    // 0x8119b0: EnterFrame
    //     0x8119b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8119b4: mov             fp, SP
    // 0x8119b8: AllocStack(0x28)
    //     0x8119b8: sub             SP, SP, #0x28
    // 0x8119bc: CheckStackOverflow
    //     0x8119bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8119c0: cmp             SP, x16
    //     0x8119c4: b.ls            #0x811aac
    // 0x8119c8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8119c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8119cc: ldr             x0, [x0, #0x2670]
    //     0x8119d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8119d4: cmp             w0, w16
    //     0x8119d8: b.ne            #0x8119e4
    //     0x8119dc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8119e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8119e4: r0 = GetNavigation.arguments()
    //     0x8119e4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8119e8: r16 = "id"
    //     0x8119e8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8119ec: ldr             x16, [x16, #0x740]
    // 0x8119f0: stp             x16, x0, [SP]
    // 0x8119f4: r4 = 0
    //     0x8119f4: movz            x4, #0
    // 0x8119f8: ldr             x0, [SP, #8]
    // 0x8119fc: r5 = UnlinkedCall_0x5f3c08
    //     0x8119fc: add             x16, PP, #0x36, lsl #12  ; [pp+0x360e0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x811a00: ldp             x5, lr, [x16, #0xe0]
    // 0x811a04: blr             lr
    // 0x811a08: mov             x3, x0
    // 0x811a0c: r2 = Null
    //     0x811a0c: mov             x2, NULL
    // 0x811a10: r1 = Null
    //     0x811a10: mov             x1, NULL
    // 0x811a14: stur            x3, [fp, #-8]
    // 0x811a18: branchIfSmi(r0, 0x811a40)
    //     0x811a18: tbz             w0, #0, #0x811a40
    // 0x811a1c: r4 = LoadClassIdInstr(r0)
    //     0x811a1c: ldur            x4, [x0, #-1]
    //     0x811a20: ubfx            x4, x4, #0xc, #0x14
    // 0x811a24: sub             x4, x4, #0x3c
    // 0x811a28: cmp             x4, #1
    // 0x811a2c: b.ls            #0x811a40
    // 0x811a30: r8 = int
    //     0x811a30: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x811a34: r3 = Null
    //     0x811a34: add             x3, PP, #0x36, lsl #12  ; [pp+0x360f0] Null
    //     0x811a38: ldr             x3, [x3, #0xf0]
    // 0x811a3c: r0 = int()
    //     0x811a3c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x811a40: r16 = <DonationRepository>
    //     0x811a40: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x811a44: ldr             x16, [x16, #0xb0]
    // 0x811a48: r30 = "donation_repo"
    //     0x811a48: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x811a4c: ldr             lr, [lr, #0xb8]
    // 0x811a50: stp             lr, x16, [SP]
    // 0x811a54: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x811a54: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x811a58: r0 = Inst.find()
    //     0x811a58: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x811a5c: mov             x1, x0
    // 0x811a60: ldur            x0, [fp, #-8]
    // 0x811a64: stur            x1, [fp, #-0x18]
    // 0x811a68: r2 = LoadInt32Instr(r0)
    //     0x811a68: sbfx            x2, x0, #1, #0x1f
    //     0x811a6c: tbz             w0, #0, #0x811a74
    //     0x811a70: ldur            x2, [x0, #7]
    // 0x811a74: stur            x2, [fp, #-0x10]
    // 0x811a78: r0 = PayDonationDetailController()
    //     0x811a78: bl              #0x811ab4  ; AllocatePayDonationDetailControllerStub -> PayDonationDetailController (size=0x34)
    // 0x811a7c: mov             x2, x0
    // 0x811a80: ldur            x0, [fp, #-0x10]
    // 0x811a84: stur            x2, [fp, #-8]
    // 0x811a88: StoreField: r2->field_27 = r0
    //     0x811a88: stur            x0, [x2, #0x27]
    // 0x811a8c: ldur            x0, [fp, #-0x18]
    // 0x811a90: StoreField: r2->field_2f = r0
    //     0x811a90: stur            w0, [x2, #0x2f]
    // 0x811a94: mov             x1, x2
    // 0x811a98: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x811a98: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x811a9c: ldur            x0, [fp, #-8]
    // 0x811aa0: LeaveFrame
    //     0x811aa0: mov             SP, fp
    //     0x811aa4: ldp             fp, lr, [SP], #0x10
    // 0x811aa8: ret
    //     0x811aa8: ret             
    // 0x811aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811ab0: b               #0x8119c8
  }
}
