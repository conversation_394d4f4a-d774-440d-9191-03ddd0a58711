// lib: , url: package:nuonline/app/modules/donation/bindings/transaction_history_binding.dart

// class id: 1050200, size: 0x8
class :: {
}

// class id: 2173, size: 0xc, field offset: 0x8
class TransactionHistoryBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x811ac0, size: 0x8c
    // 0x811ac0: EnterFrame
    //     0x811ac0: stp             fp, lr, [SP, #-0x10]!
    //     0x811ac4: mov             fp, SP
    // 0x811ac8: AllocStack(0x20)
    //     0x811ac8: sub             SP, SP, #0x20
    // 0x811acc: SetupParameters(TransactionHistoryBinding this /* r1 => r1, fp-0x8 */)
    //     0x811acc: stur            x1, [fp, #-8]
    // 0x811ad0: CheckStackOverflow
    //     0x811ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811ad4: cmp             SP, x16
    //     0x811ad8: b.ls            #0x811b44
    // 0x811adc: r1 = 1
    //     0x811adc: movz            x1, #0x1
    // 0x811ae0: r0 = AllocateContext()
    //     0x811ae0: bl              #0xec126c  ; AllocateContextStub
    // 0x811ae4: mov             x1, x0
    // 0x811ae8: ldur            x0, [fp, #-8]
    // 0x811aec: stur            x1, [fp, #-0x10]
    // 0x811af0: StoreField: r1->field_f = r0
    //     0x811af0: stur            w0, [x1, #0xf]
    // 0x811af4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811af4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811af8: ldr             x0, [x0, #0x2670]
    //     0x811afc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811b00: cmp             w0, w16
    //     0x811b04: b.ne            #0x811b10
    //     0x811b08: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811b0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811b10: ldur            x2, [fp, #-0x10]
    // 0x811b14: r1 = Function '<anonymous closure>':.
    //     0x811b14: add             x1, PP, #0x36, lsl #12  ; [pp+0x360b8] AnonymousClosure: (0x811b4c), in [package:nuonline/app/modules/donation/bindings/transaction_history_binding.dart] TransactionHistoryBinding::dependencies (0x811ac0)
    //     0x811b18: ldr             x1, [x1, #0xb8]
    // 0x811b1c: r0 = AllocateClosure()
    //     0x811b1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x811b20: r16 = <TransactionHistoryController>
    //     0x811b20: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af08] TypeArguments: <TransactionHistoryController>
    //     0x811b24: ldr             x16, [x16, #0xf08]
    // 0x811b28: stp             x0, x16, [SP]
    // 0x811b2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811b2c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811b30: r0 = Inst.lazyPut()
    //     0x811b30: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811b34: r0 = Null
    //     0x811b34: mov             x0, NULL
    // 0x811b38: LeaveFrame
    //     0x811b38: mov             SP, fp
    //     0x811b3c: ldp             fp, lr, [SP], #0x10
    // 0x811b40: ret
    //     0x811b40: ret             
    // 0x811b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811b44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811b48: b               #0x811adc
  }
  [closure] TransactionHistoryController <anonymous closure>(dynamic) {
    // ** addr: 0x811b4c, size: 0xf0
    // 0x811b4c: EnterFrame
    //     0x811b4c: stp             fp, lr, [SP, #-0x10]!
    //     0x811b50: mov             fp, SP
    // 0x811b54: AllocStack(0x30)
    //     0x811b54: sub             SP, SP, #0x30
    // 0x811b58: SetupParameters()
    //     0x811b58: ldr             x0, [fp, #0x10]
    //     0x811b5c: ldur            w1, [x0, #0x17]
    //     0x811b60: add             x1, x1, HEAP, lsl #32
    // 0x811b64: CheckStackOverflow
    //     0x811b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811b68: cmp             SP, x16
    //     0x811b6c: b.ls            #0x811c34
    // 0x811b70: LoadField: r0 = r1->field_f
    //     0x811b70: ldur            w0, [x1, #0xf]
    // 0x811b74: DecompressPointer r0
    //     0x811b74: add             x0, x0, HEAP, lsl #32
    // 0x811b78: LoadField: r3 = r0->field_7
    //     0x811b78: ldur            w3, [x0, #7]
    // 0x811b7c: DecompressPointer r3
    //     0x811b7c: add             x3, x3, HEAP, lsl #32
    // 0x811b80: stur            x3, [fp, #-8]
    // 0x811b84: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811b84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811b88: ldr             x0, [x0, #0x2670]
    //     0x811b8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811b90: cmp             w0, w16
    //     0x811b94: b.ne            #0x811ba0
    //     0x811b98: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811b9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811ba0: r0 = GetNavigation.arguments()
    //     0x811ba0: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x811ba4: mov             x3, x0
    // 0x811ba8: r2 = Null
    //     0x811ba8: mov             x2, NULL
    // 0x811bac: r1 = Null
    //     0x811bac: mov             x1, NULL
    // 0x811bb0: stur            x3, [fp, #-0x10]
    // 0x811bb4: branchIfSmi(r0, 0x811bdc)
    //     0x811bb4: tbz             w0, #0, #0x811bdc
    // 0x811bb8: r4 = LoadClassIdInstr(r0)
    //     0x811bb8: ldur            x4, [x0, #-1]
    //     0x811bbc: ubfx            x4, x4, #0xc, #0x14
    // 0x811bc0: sub             x4, x4, #0x3c
    // 0x811bc4: cmp             x4, #1
    // 0x811bc8: b.ls            #0x811bdc
    // 0x811bcc: r8 = int?
    //     0x811bcc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x811bd0: r3 = Null
    //     0x811bd0: add             x3, PP, #0x36, lsl #12  ; [pp+0x360c0] Null
    //     0x811bd4: ldr             x3, [x3, #0xc0]
    // 0x811bd8: r0 = int?()
    //     0x811bd8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x811bdc: r16 = <DonationRepository>
    //     0x811bdc: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x811be0: ldr             x16, [x16, #0xb0]
    // 0x811be4: r30 = "donation_repo"
    //     0x811be4: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x811be8: ldr             lr, [lr, #0xb8]
    // 0x811bec: stp             lr, x16, [SP]
    // 0x811bf0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x811bf0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x811bf4: r0 = Inst.find()
    //     0x811bf4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x811bf8: stur            x0, [fp, #-0x18]
    // 0x811bfc: r0 = TransactionHistoryController()
    //     0x811bfc: bl              #0x811f60  ; AllocateTransactionHistoryControllerStub -> TransactionHistoryController (size=0x4c)
    // 0x811c00: stur            x0, [fp, #-0x20]
    // 0x811c04: ldur            x16, [fp, #-0x10]
    // 0x811c08: str             x16, [SP]
    // 0x811c0c: mov             x1, x0
    // 0x811c10: ldur            x2, [fp, #-0x18]
    // 0x811c14: ldur            x3, [fp, #-8]
    // 0x811c18: r4 = const [0, 0x4, 0x1, 0x3, donationId, 0x3, null]
    //     0x811c18: add             x4, PP, #0x36, lsl #12  ; [pp+0x360d0] List(7) [0, 0x4, 0x1, 0x3, "donationId", 0x3, Null]
    //     0x811c1c: ldr             x4, [x4, #0xd0]
    // 0x811c20: r0 = TransactionHistoryController()
    //     0x811c20: bl              #0x811c3c  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::TransactionHistoryController
    // 0x811c24: ldur            x0, [fp, #-0x20]
    // 0x811c28: LeaveFrame
    //     0x811c28: mov             SP, fp
    //     0x811c2c: ldp             fp, lr, [SP], #0x10
    // 0x811c30: ret
    //     0x811c30: ret             
    // 0x811c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811c34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811c38: b               #0x811b70
  }
}
