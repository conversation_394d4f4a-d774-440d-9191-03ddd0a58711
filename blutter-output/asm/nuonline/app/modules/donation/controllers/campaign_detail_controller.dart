// lib: , url: package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart

// class id: 1050201, size: 0x8
class :: {
}

// class id: 1939, size: 0x2c, field offset: 0x24
//   transformed mixin,
abstract class _CampaignDetailController&FetchController&StateMixin extends FetchController<dynamic>
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x7d8b34, size: 0x90
    // 0x7d8b34: EnterFrame
    //     0x7d8b34: stp             fp, lr, [SP, #-0x10]!
    //     0x7d8b38: mov             fp, SP
    // 0x7d8b3c: mov             x16, x2
    // 0x7d8b40: mov             x2, x1
    // 0x7d8b44: mov             x1, x16
    // 0x7d8b48: mov             x0, x3
    // 0x7d8b4c: CheckStackOverflow
    //     0x7d8b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d8b50: cmp             SP, x16
    //     0x7d8b54: b.ls            #0x7d8bbc
    // 0x7d8b58: StoreField: r2->field_27 = r0
    //     0x7d8b58: stur            w0, [x2, #0x27]
    //     0x7d8b5c: ldurb           w16, [x2, #-1]
    //     0x7d8b60: ldurb           w17, [x0, #-1]
    //     0x7d8b64: and             x16, x17, x16, lsr #2
    //     0x7d8b68: tst             x16, HEAP, lsr #32
    //     0x7d8b6c: b.eq            #0x7d8b74
    //     0x7d8b70: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7d8b74: LoadField: r0 = r2->field_23
    //     0x7d8b74: ldur            w0, [x2, #0x23]
    // 0x7d8b78: DecompressPointer r0
    //     0x7d8b78: add             x0, x0, HEAP, lsl #32
    // 0x7d8b7c: cmp             w1, w0
    // 0x7d8b80: b.eq            #0x7d8ba4
    // 0x7d8b84: mov             x0, x1
    // 0x7d8b88: StoreField: r2->field_23 = r0
    //     0x7d8b88: stur            w0, [x2, #0x23]
    //     0x7d8b8c: ldurb           w16, [x2, #-1]
    //     0x7d8b90: ldurb           w17, [x0, #-1]
    //     0x7d8b94: and             x16, x17, x16, lsr #2
    //     0x7d8b98: tst             x16, HEAP, lsr #32
    //     0x7d8b9c: b.eq            #0x7d8ba4
    //     0x7d8ba0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7d8ba4: mov             x1, x2
    // 0x7d8ba8: r0 = _notifyUpdate()
    //     0x7d8ba8: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x7d8bac: r0 = Null
    //     0x7d8bac: mov             x0, NULL
    // 0x7d8bb0: LeaveFrame
    //     0x7d8bb0: mov             SP, fp
    //     0x7d8bb4: ldp             fp, lr, [SP], #0x10
    // 0x7d8bb8: ret
    //     0x7d8bb8: ret             
    // 0x7d8bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d8bbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d8bc0: b               #0x7d8b58
  }
}

// class id: 1940, size: 0x30, field offset: 0x2c
//   transformed mixin,
abstract class _CampaignDetailController&FetchController&StateMixin&GetSingleTickerProviderStateMixin extends _CampaignDetailController&FetchController&StateMixin
     with GetSingleTickerProviderStateMixin {
}

// class id: 1941, size: 0x50, field offset: 0x30
class CampaignDetailController extends _CampaignDetailController&FetchController&StateMixin&GetSingleTickerProviderStateMixin {

  late final AnimationController animationController; // offset: 0x44

  _ onFetchLoaded(/* No info */) async {
    // ** addr: 0x7d89fc, size: 0x68
    // 0x7d89fc: EnterFrame
    //     0x7d89fc: stp             fp, lr, [SP, #-0x10]!
    //     0x7d8a00: mov             fp, SP
    // 0x7d8a04: AllocStack(0x20)
    //     0x7d8a04: sub             SP, SP, #0x20
    // 0x7d8a08: SetupParameters(CampaignDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7d8a08: stur            NULL, [fp, #-8]
    //     0x7d8a0c: stur            x1, [fp, #-0x10]
    //     0x7d8a10: stur            x2, [fp, #-0x18]
    //     0x7d8a14: stur            x3, [fp, #-0x20]
    // 0x7d8a18: CheckStackOverflow
    //     0x7d8a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d8a1c: cmp             SP, x16
    //     0x7d8a20: b.ls            #0x7d8a5c
    // 0x7d8a24: InitAsync() -> Future<void?>
    //     0x7d8a24: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7d8a28: bl              #0x661298  ; InitAsyncStub
    // 0x7d8a2c: r0 = RxStatus()
    //     0x7d8a2c: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7d8a30: mov             x1, x0
    // 0x7d8a34: r0 = false
    //     0x7d8a34: add             x0, NULL, #0x30  ; false
    // 0x7d8a38: StoreField: r1->field_f = r0
    //     0x7d8a38: stur            w0, [x1, #0xf]
    // 0x7d8a3c: StoreField: r1->field_7 = r0
    //     0x7d8a3c: stur            w0, [x1, #7]
    // 0x7d8a40: StoreField: r1->field_b = r0
    //     0x7d8a40: stur            w0, [x1, #0xb]
    // 0x7d8a44: mov             x3, x1
    // 0x7d8a48: ldur            x1, [fp, #-0x10]
    // 0x7d8a4c: ldur            x2, [fp, #-0x18]
    // 0x7d8a50: r0 = change()
    //     0x7d8a50: bl              #0x7d8b34  ; [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] _CampaignDetailController&FetchController&StateMixin::change
    // 0x7d8a54: r0 = Null
    //     0x7d8a54: mov             x0, NULL
    // 0x7d8a58: r0 = ReturnAsyncNotFuture()
    //     0x7d8a58: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7d8a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d8a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d8a60: b               #0x7d8a24
  }
  _ onFetchFailure(/* No info */) async {
    // ** addr: 0x7e2464, size: 0x8c
    // 0x7e2464: EnterFrame
    //     0x7e2464: stp             fp, lr, [SP, #-0x10]!
    //     0x7e2468: mov             fp, SP
    // 0x7e246c: AllocStack(0x20)
    //     0x7e246c: sub             SP, SP, #0x20
    // 0x7e2470: SetupParameters(CampaignDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7e2470: stur            NULL, [fp, #-8]
    //     0x7e2474: stur            x1, [fp, #-0x10]
    //     0x7e2478: stur            x2, [fp, #-0x18]
    //     0x7e247c: stur            x3, [fp, #-0x20]
    // 0x7e2480: CheckStackOverflow
    //     0x7e2480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e2484: cmp             SP, x16
    //     0x7e2488: b.ls            #0x7e24e8
    // 0x7e248c: InitAsync() -> Future<void?>
    //     0x7e248c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7e2490: bl              #0x661298  ; InitAsyncStub
    // 0x7e2494: ldur            x1, [fp, #-0x10]
    // 0x7e2498: r0 = notifyChildrens()
    //     0x7e2498: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x7e249c: ldur            x1, [fp, #-0x10]
    // 0x7e24a0: LoadField: r2 = r1->field_23
    //     0x7e24a0: ldur            w2, [x1, #0x23]
    // 0x7e24a4: DecompressPointer r2
    //     0x7e24a4: add             x2, x2, HEAP, lsl #32
    // 0x7e24a8: stur            x2, [fp, #-0x18]
    // 0x7e24ac: r0 = RxStatus()
    //     0x7e24ac: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7e24b0: mov             x1, x0
    // 0x7e24b4: r0 = false
    //     0x7e24b4: add             x0, NULL, #0x30  ; false
    // 0x7e24b8: StoreField: r1->field_f = r0
    //     0x7e24b8: stur            w0, [x1, #0xf]
    // 0x7e24bc: StoreField: r1->field_7 = r0
    //     0x7e24bc: stur            w0, [x1, #7]
    // 0x7e24c0: r0 = true
    //     0x7e24c0: add             x0, NULL, #0x20  ; true
    // 0x7e24c4: StoreField: r1->field_b = r0
    //     0x7e24c4: stur            w0, [x1, #0xb]
    // 0x7e24c8: ldur            x0, [fp, #-0x20]
    // 0x7e24cc: StoreField: r1->field_13 = r0
    //     0x7e24cc: stur            w0, [x1, #0x13]
    // 0x7e24d0: mov             x3, x1
    // 0x7e24d4: ldur            x1, [fp, #-0x10]
    // 0x7e24d8: ldur            x2, [fp, #-0x18]
    // 0x7e24dc: r0 = change()
    //     0x7e24dc: bl              #0x7d8b34  ; [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] _CampaignDetailController&FetchController&StateMixin::change
    // 0x7e24e0: r0 = Null
    //     0x7e24e0: mov             x0, NULL
    // 0x7e24e4: r0 = ReturnAsyncNotFuture()
    //     0x7e24e4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e24e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e24e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e24ec: b               #0x7e248c
  }
  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e6460, size: 0x3c
    // 0x7e6460: EnterFrame
    //     0x7e6460: stp             fp, lr, [SP, #-0x10]!
    //     0x7e6464: mov             fp, SP
    // 0x7e6468: CheckStackOverflow
    //     0x7e6468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e646c: cmp             SP, x16
    //     0x7e6470: b.ls            #0x7e6494
    // 0x7e6474: LoadField: r0 = r1->field_3b
    //     0x7e6474: ldur            w0, [x1, #0x3b]
    // 0x7e6478: DecompressPointer r0
    //     0x7e6478: add             x0, x0, HEAP, lsl #32
    // 0x7e647c: LoadField: r2 = r1->field_2f
    //     0x7e647c: ldur            x2, [x1, #0x2f]
    // 0x7e6480: mov             x1, x0
    // 0x7e6484: r0 = findCampaignById()
    //     0x7e6484: bl              #0x7e649c  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findCampaignById
    // 0x7e6488: LeaveFrame
    //     0x7e6488: mov             SP, fp
    //     0x7e648c: ldp             fp, lr, [SP], #0x10
    // 0x7e6490: ret
    //     0x7e6490: ret             
    // 0x7e6494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e6494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e6498: b               #0x7e6474
  }
  _ CampaignDetailController(/* No info */) {
    // ** addr: 0x8104d0, size: 0x130
    // 0x8104d0: EnterFrame
    //     0x8104d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8104d4: mov             fp, SP
    // 0x8104d8: AllocStack(0x28)
    //     0x8104d8: sub             SP, SP, #0x28
    // 0x8104dc: r0 = Sentinel
    //     0x8104dc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8104e0: mov             x4, x1
    // 0x8104e4: stur            x2, [fp, #-0x10]
    // 0x8104e8: mov             x16, x3
    // 0x8104ec: mov             x3, x2
    // 0x8104f0: mov             x2, x16
    // 0x8104f4: stur            x1, [fp, #-8]
    // 0x8104f8: mov             x1, x5
    // 0x8104fc: stur            x2, [fp, #-0x18]
    // 0x810500: stur            x5, [fp, #-0x20]
    // 0x810504: CheckStackOverflow
    //     0x810504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810508: cmp             SP, x16
    //     0x81050c: b.ls            #0x8105f8
    // 0x810510: StoreField: r4->field_43 = r0
    //     0x810510: stur            w0, [x4, #0x43]
    // 0x810514: r0 = ScrollController()
    //     0x810514: bl              #0x6852cc  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0x810518: mov             x1, x0
    // 0x81051c: stur            x0, [fp, #-0x28]
    // 0x810520: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x810520: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x810524: r0 = ScrollController()
    //     0x810524: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x810528: ldur            x0, [fp, #-0x28]
    // 0x81052c: ldur            x2, [fp, #-8]
    // 0x810530: StoreField: r2->field_3f = r0
    //     0x810530: stur            w0, [x2, #0x3f]
    //     0x810534: ldurb           w16, [x2, #-1]
    //     0x810538: ldurb           w17, [x0, #-1]
    //     0x81053c: and             x16, x17, x16, lsr #2
    //     0x810540: tst             x16, HEAP, lsr #32
    //     0x810544: b.eq            #0x81054c
    //     0x810548: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81054c: r1 = false
    //     0x81054c: add             x1, NULL, #0x30  ; false
    // 0x810550: r0 = BoolExtension.obs()
    //     0x810550: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x810554: ldur            x2, [fp, #-8]
    // 0x810558: StoreField: r2->field_47 = r0
    //     0x810558: stur            w0, [x2, #0x47]
    //     0x81055c: ldurb           w16, [x2, #-1]
    //     0x810560: ldurb           w17, [x0, #-1]
    //     0x810564: and             x16, x17, x16, lsr #2
    //     0x810568: tst             x16, HEAP, lsr #32
    //     0x81056c: b.eq            #0x810574
    //     0x810570: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x810574: r1 = false
    //     0x810574: add             x1, NULL, #0x30  ; false
    // 0x810578: r0 = BoolExtension.obs()
    //     0x810578: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x81057c: ldur            x1, [fp, #-8]
    // 0x810580: StoreField: r1->field_4b = r0
    //     0x810580: stur            w0, [x1, #0x4b]
    //     0x810584: ldurb           w16, [x1, #-1]
    //     0x810588: ldurb           w17, [x0, #-1]
    //     0x81058c: and             x16, x17, x16, lsr #2
    //     0x810590: tst             x16, HEAP, lsr #32
    //     0x810594: b.eq            #0x81059c
    //     0x810598: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x81059c: ldur            x0, [fp, #-0x10]
    // 0x8105a0: StoreField: r1->field_2f = r0
    //     0x8105a0: stur            x0, [x1, #0x2f]
    // 0x8105a4: ldur            x0, [fp, #-0x18]
    // 0x8105a8: StoreField: r1->field_3b = r0
    //     0x8105a8: stur            w0, [x1, #0x3b]
    //     0x8105ac: ldurb           w16, [x1, #-1]
    //     0x8105b0: ldurb           w17, [x0, #-1]
    //     0x8105b4: and             x16, x17, x16, lsr #2
    //     0x8105b8: tst             x16, HEAP, lsr #32
    //     0x8105bc: b.eq            #0x8105c4
    //     0x8105c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8105c4: ldur            x0, [fp, #-0x20]
    // 0x8105c8: StoreField: r1->field_37 = r0
    //     0x8105c8: stur            w0, [x1, #0x37]
    //     0x8105cc: ldurb           w16, [x1, #-1]
    //     0x8105d0: ldurb           w17, [x0, #-1]
    //     0x8105d4: and             x16, x17, x16, lsr #2
    //     0x8105d8: tst             x16, HEAP, lsr #32
    //     0x8105dc: b.eq            #0x8105e4
    //     0x8105e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8105e4: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8105e4: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8105e8: r0 = Null
    //     0x8105e8: mov             x0, NULL
    // 0x8105ec: LeaveFrame
    //     0x8105ec: mov             SP, fp
    //     0x8105f0: ldp             fp, lr, [SP], #0x10
    // 0x8105f4: ret
    //     0x8105f4: ret             
    // 0x8105f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8105f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8105fc: b               #0x810510
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91e7cc, size: 0x70
    // 0x91e7cc: EnterFrame
    //     0x91e7cc: stp             fp, lr, [SP, #-0x10]!
    //     0x91e7d0: mov             fp, SP
    // 0x91e7d4: AllocStack(0x10)
    //     0x91e7d4: sub             SP, SP, #0x10
    // 0x91e7d8: SetupParameters(CampaignDetailController this /* r1 => r1, fp-0x8 */)
    //     0x91e7d8: stur            x1, [fp, #-8]
    // 0x91e7dc: CheckStackOverflow
    //     0x91e7dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e7e0: cmp             SP, x16
    //     0x91e7e4: b.ls            #0x91e834
    // 0x91e7e8: r1 = 1
    //     0x91e7e8: movz            x1, #0x1
    // 0x91e7ec: r0 = AllocateContext()
    //     0x91e7ec: bl              #0xec126c  ; AllocateContextStub
    // 0x91e7f0: mov             x1, x0
    // 0x91e7f4: ldur            x0, [fp, #-8]
    // 0x91e7f8: StoreField: r1->field_f = r0
    //     0x91e7f8: stur            w0, [x1, #0xf]
    // 0x91e7fc: LoadField: r3 = r0->field_3f
    //     0x91e7fc: ldur            w3, [x0, #0x3f]
    // 0x91e800: DecompressPointer r3
    //     0x91e800: add             x3, x3, HEAP, lsl #32
    // 0x91e804: mov             x2, x1
    // 0x91e808: stur            x3, [fp, #-0x10]
    // 0x91e80c: r1 = Function '<anonymous closure>':.
    //     0x91e80c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40520] AnonymousClosure: (0x91e83c), in [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::onReady (0x91e7cc)
    //     0x91e810: ldr             x1, [x1, #0x520]
    // 0x91e814: r0 = AllocateClosure()
    //     0x91e814: bl              #0xec1630  ; AllocateClosureStub
    // 0x91e818: ldur            x1, [fp, #-0x10]
    // 0x91e81c: mov             x2, x0
    // 0x91e820: r0 = addListener()
    //     0x91e820: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x91e824: r0 = Null
    //     0x91e824: mov             x0, NULL
    // 0x91e828: LeaveFrame
    //     0x91e828: mov             SP, fp
    //     0x91e82c: ldp             fp, lr, [SP], #0x10
    // 0x91e830: ret
    //     0x91e830: ret             
    // 0x91e834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e834: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e838: b               #0x91e7e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x91e83c, size: 0x2e0
    // 0x91e83c: EnterFrame
    //     0x91e83c: stp             fp, lr, [SP, #-0x10]!
    //     0x91e840: mov             fp, SP
    // 0x91e844: AllocStack(0x28)
    //     0x91e844: sub             SP, SP, #0x28
    // 0x91e848: SetupParameters()
    //     0x91e848: ldr             x0, [fp, #0x10]
    //     0x91e84c: ldur            w2, [x0, #0x17]
    //     0x91e850: add             x2, x2, HEAP, lsl #32
    //     0x91e854: stur            x2, [fp, #-0x10]
    // 0x91e858: CheckStackOverflow
    //     0x91e858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e85c: cmp             SP, x16
    //     0x91e860: b.ls            #0x91eb08
    // 0x91e864: LoadField: r0 = r2->field_f
    //     0x91e864: ldur            w0, [x2, #0xf]
    // 0x91e868: DecompressPointer r0
    //     0x91e868: add             x0, x0, HEAP, lsl #32
    // 0x91e86c: mov             x1, x0
    // 0x91e870: stur            x0, [fp, #-8]
    // 0x91e874: r0 = notifyChildrens()
    //     0x91e874: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x91e878: ldur            x0, [fp, #-8]
    // 0x91e87c: LoadField: r1 = r0->field_23
    //     0x91e87c: ldur            w1, [x0, #0x23]
    // 0x91e880: DecompressPointer r1
    //     0x91e880: add             x1, x1, HEAP, lsl #32
    // 0x91e884: cmp             w1, NULL
    // 0x91e888: b.eq            #0x91eaf8
    // 0x91e88c: ldur            x2, [fp, #-0x10]
    // 0x91e890: LoadField: r0 = r2->field_f
    //     0x91e890: ldur            w0, [x2, #0xf]
    // 0x91e894: DecompressPointer r0
    //     0x91e894: add             x0, x0, HEAP, lsl #32
    // 0x91e898: mov             x1, x0
    // 0x91e89c: stur            x0, [fp, #-8]
    // 0x91e8a0: r0 = notifyChildrens()
    //     0x91e8a0: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x91e8a4: ldur            x0, [fp, #-8]
    // 0x91e8a8: LoadField: r1 = r0->field_23
    //     0x91e8a8: ldur            w1, [x0, #0x23]
    // 0x91e8ac: DecompressPointer r1
    //     0x91e8ac: add             x1, x1, HEAP, lsl #32
    // 0x91e8b0: cmp             w1, NULL
    // 0x91e8b4: b.eq            #0x91eb10
    // 0x91e8b8: r0 = isEnded()
    //     0x91e8b8: bl              #0x91eb50  ; [package:nuonline/app/data/models/campaign.dart] Campaign::isEnded
    // 0x91e8bc: tbnz            w0, #4, #0x91e8f4
    // 0x91e8c0: ldur            x2, [fp, #-0x10]
    // 0x91e8c4: LoadField: r0 = r2->field_f
    //     0x91e8c4: ldur            w0, [x2, #0xf]
    // 0x91e8c8: DecompressPointer r0
    //     0x91e8c8: add             x0, x0, HEAP, lsl #32
    // 0x91e8cc: LoadField: r1 = r0->field_37
    //     0x91e8cc: ldur            w1, [x0, #0x37]
    // 0x91e8d0: DecompressPointer r1
    //     0x91e8d0: add             x1, x1, HEAP, lsl #32
    // 0x91e8d4: r16 = Instance_PaymentType
    //     0x91e8d4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0x91e8d8: ldr             x16, [x16, #0x608]
    // 0x91e8dc: cmp             w1, w16
    // 0x91e8e0: b.eq            #0x91e8f8
    // 0x91e8e4: r0 = Null
    //     0x91e8e4: mov             x0, NULL
    // 0x91e8e8: LeaveFrame
    //     0x91e8e8: mov             SP, fp
    //     0x91e8ec: ldp             fp, lr, [SP], #0x10
    // 0x91e8f0: ret
    //     0x91e8f0: ret             
    // 0x91e8f4: ldur            x2, [fp, #-0x10]
    // 0x91e8f8: LoadField: r0 = r2->field_f
    //     0x91e8f8: ldur            w0, [x2, #0xf]
    // 0x91e8fc: DecompressPointer r0
    //     0x91e8fc: add             x0, x0, HEAP, lsl #32
    // 0x91e900: LoadField: r1 = r0->field_3f
    //     0x91e900: ldur            w1, [x0, #0x3f]
    // 0x91e904: DecompressPointer r1
    //     0x91e904: add             x1, x1, HEAP, lsl #32
    // 0x91e908: LoadField: r0 = r1->field_3b
    //     0x91e908: ldur            w0, [x1, #0x3b]
    // 0x91e90c: DecompressPointer r0
    //     0x91e90c: add             x0, x0, HEAP, lsl #32
    // 0x91e910: mov             x1, x0
    // 0x91e914: r0 = single()
    //     0x91e914: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x91e918: LoadField: r1 = r0->field_3f
    //     0x91e918: ldur            w1, [x0, #0x3f]
    // 0x91e91c: DecompressPointer r1
    //     0x91e91c: add             x1, x1, HEAP, lsl #32
    // 0x91e920: cmp             w1, NULL
    // 0x91e924: b.eq            #0x91eb14
    // 0x91e928: LoadField: d0 = r1->field_7
    //     0x91e928: ldur            d0, [x1, #7]
    // 0x91e92c: d1 = 56.000000
    //     0x91e92c: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x91e930: ldr             d1, [x17, #0xf60]
    // 0x91e934: fcmp            d0, d1
    // 0x91e938: b.le            #0x91eab8
    // 0x91e93c: ldur            x2, [fp, #-0x10]
    // 0x91e940: LoadField: r0 = r2->field_f
    //     0x91e940: ldur            w0, [x2, #0xf]
    // 0x91e944: DecompressPointer r0
    //     0x91e944: add             x0, x0, HEAP, lsl #32
    // 0x91e948: LoadField: r1 = r0->field_47
    //     0x91e948: ldur            w1, [x0, #0x47]
    // 0x91e94c: DecompressPointer r1
    //     0x91e94c: add             x1, x1, HEAP, lsl #32
    // 0x91e950: r0 = RxBoolExt.isFalse()
    //     0x91e950: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0x91e954: tbnz            w0, #4, #0x91e978
    // 0x91e958: ldur            x0, [fp, #-0x10]
    // 0x91e95c: LoadField: r1 = r0->field_f
    //     0x91e95c: ldur            w1, [x0, #0xf]
    // 0x91e960: DecompressPointer r1
    //     0x91e960: add             x1, x1, HEAP, lsl #32
    // 0x91e964: LoadField: r2 = r1->field_47
    //     0x91e964: ldur            w2, [x1, #0x47]
    // 0x91e968: DecompressPointer r2
    //     0x91e968: add             x2, x2, HEAP, lsl #32
    // 0x91e96c: mov             x1, x2
    // 0x91e970: r2 = true
    //     0x91e970: add             x2, NULL, #0x20  ; true
    // 0x91e974: r0 = value=()
    //     0x91e974: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x91e978: ldur            x2, [fp, #-0x10]
    // 0x91e97c: LoadField: r0 = r2->field_f
    //     0x91e97c: ldur            w0, [x2, #0xf]
    // 0x91e980: DecompressPointer r0
    //     0x91e980: add             x0, x0, HEAP, lsl #32
    // 0x91e984: LoadField: r1 = r0->field_3f
    //     0x91e984: ldur            w1, [x0, #0x3f]
    // 0x91e988: DecompressPointer r1
    //     0x91e988: add             x1, x1, HEAP, lsl #32
    // 0x91e98c: LoadField: r0 = r1->field_3b
    //     0x91e98c: ldur            w0, [x1, #0x3b]
    // 0x91e990: DecompressPointer r0
    //     0x91e990: add             x0, x0, HEAP, lsl #32
    // 0x91e994: mov             x1, x0
    // 0x91e998: r0 = single()
    //     0x91e998: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x91e99c: LoadField: r1 = r0->field_3f
    //     0x91e99c: ldur            w1, [x0, #0x3f]
    // 0x91e9a0: DecompressPointer r1
    //     0x91e9a0: add             x1, x1, HEAP, lsl #32
    // 0x91e9a4: cmp             w1, NULL
    // 0x91e9a8: b.eq            #0x91eb18
    // 0x91e9ac: LoadField: d0 = r1->field_7
    //     0x91e9ac: ldur            d0, [x1, #7]
    // 0x91e9b0: d1 = 400.000000
    //     0x91e9b0: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(400) from 0x4079000000000000
    // 0x91e9b4: fcmp            d0, d1
    // 0x91e9b8: b.lt            #0x91ea34
    // 0x91e9bc: ldur            x2, [fp, #-0x10]
    // 0x91e9c0: LoadField: r0 = r2->field_f
    //     0x91e9c0: ldur            w0, [x2, #0xf]
    // 0x91e9c4: DecompressPointer r0
    //     0x91e9c4: add             x0, x0, HEAP, lsl #32
    // 0x91e9c8: LoadField: r1 = r0->field_4b
    //     0x91e9c8: ldur            w1, [x0, #0x4b]
    // 0x91e9cc: DecompressPointer r1
    //     0x91e9cc: add             x1, x1, HEAP, lsl #32
    // 0x91e9d0: r0 = RxBoolExt.isFalse()
    //     0x91e9d0: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0x91e9d4: tbnz            w0, #4, #0x91eaf8
    // 0x91e9d8: ldur            x0, [fp, #-0x10]
    // 0x91e9dc: LoadField: r1 = r0->field_f
    //     0x91e9dc: ldur            w1, [x0, #0xf]
    // 0x91e9e0: DecompressPointer r1
    //     0x91e9e0: add             x1, x1, HEAP, lsl #32
    // 0x91e9e4: LoadField: r2 = r1->field_4b
    //     0x91e9e4: ldur            w2, [x1, #0x4b]
    // 0x91e9e8: DecompressPointer r2
    //     0x91e9e8: add             x2, x2, HEAP, lsl #32
    // 0x91e9ec: mov             x1, x2
    // 0x91e9f0: r2 = true
    //     0x91e9f0: add             x2, NULL, #0x20  ; true
    // 0x91e9f4: r0 = value=()
    //     0x91e9f4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x91e9f8: ldur            x2, [fp, #-0x10]
    // 0x91e9fc: LoadField: r1 = r2->field_f
    //     0x91e9fc: ldur            w1, [x2, #0xf]
    // 0x91ea00: DecompressPointer r1
    //     0x91ea00: add             x1, x1, HEAP, lsl #32
    // 0x91ea04: LoadField: r0 = r1->field_43
    //     0x91ea04: ldur            w0, [x1, #0x43]
    // 0x91ea08: DecompressPointer r0
    //     0x91ea08: add             x0, x0, HEAP, lsl #32
    // 0x91ea0c: r16 = Sentinel
    //     0x91ea0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91ea10: cmp             w0, w16
    // 0x91ea14: b.ne            #0x91ea24
    // 0x91ea18: r2 = animationController
    //     0x91ea18: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f5b8] Field <CampaignDetailController.animationController>: late final (offset: 0x44)
    //     0x91ea1c: ldr             x2, [x2, #0x5b8]
    // 0x91ea20: r0 = InitLateFinalInstanceField()
    //     0x91ea20: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x91ea24: mov             x1, x0
    // 0x91ea28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91ea28: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91ea2c: r0 = forward()
    //     0x91ea2c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x91ea30: b               #0x91eaf8
    // 0x91ea34: ldur            x2, [fp, #-0x10]
    // 0x91ea38: LoadField: r0 = r2->field_f
    //     0x91ea38: ldur            w0, [x2, #0xf]
    // 0x91ea3c: DecompressPointer r0
    //     0x91ea3c: add             x0, x0, HEAP, lsl #32
    // 0x91ea40: LoadField: r1 = r0->field_4b
    //     0x91ea40: ldur            w1, [x0, #0x4b]
    // 0x91ea44: DecompressPointer r1
    //     0x91ea44: add             x1, x1, HEAP, lsl #32
    // 0x91ea48: r0 = value()
    //     0x91ea48: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x91ea4c: tbnz            w0, #4, #0x91eaf8
    // 0x91ea50: ldur            x2, [fp, #-0x10]
    // 0x91ea54: LoadField: r1 = r2->field_f
    //     0x91ea54: ldur            w1, [x2, #0xf]
    // 0x91ea58: DecompressPointer r1
    //     0x91ea58: add             x1, x1, HEAP, lsl #32
    // 0x91ea5c: LoadField: r0 = r1->field_43
    //     0x91ea5c: ldur            w0, [x1, #0x43]
    // 0x91ea60: DecompressPointer r0
    //     0x91ea60: add             x0, x0, HEAP, lsl #32
    // 0x91ea64: r16 = Sentinel
    //     0x91ea64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91ea68: cmp             w0, w16
    // 0x91ea6c: b.ne            #0x91ea7c
    // 0x91ea70: r2 = animationController
    //     0x91ea70: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f5b8] Field <CampaignDetailController.animationController>: late final (offset: 0x44)
    //     0x91ea74: ldr             x2, [x2, #0x5b8]
    // 0x91ea78: r0 = InitLateFinalInstanceField()
    //     0x91ea78: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x91ea7c: mov             x1, x0
    // 0x91ea80: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91ea80: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91ea84: r0 = reverse()
    //     0x91ea84: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x91ea88: ldur            x2, [fp, #-0x10]
    // 0x91ea8c: r1 = Function '<anonymous closure>':.
    //     0x91ea8c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40528] AnonymousClosure: (0x91ebb8), in [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::onReady (0x91e7cc)
    //     0x91ea90: ldr             x1, [x1, #0x528]
    // 0x91ea94: stur            x0, [fp, #-8]
    // 0x91ea98: r0 = AllocateClosure()
    //     0x91ea98: bl              #0xec1630  ; AllocateClosureStub
    // 0x91ea9c: r16 = <Null?>
    //     0x91ea9c: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x91eaa0: ldur            lr, [fp, #-8]
    // 0x91eaa4: stp             lr, x16, [SP, #8]
    // 0x91eaa8: str             x0, [SP]
    // 0x91eaac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91eaac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91eab0: r0 = then()
    //     0x91eab0: bl              #0xdb1ab8  ; [package:flutter/src/scheduler/ticker.dart] TickerFuture::then
    // 0x91eab4: b               #0x91eaf8
    // 0x91eab8: ldur            x0, [fp, #-0x10]
    // 0x91eabc: LoadField: r1 = r0->field_f
    //     0x91eabc: ldur            w1, [x0, #0xf]
    // 0x91eac0: DecompressPointer r1
    //     0x91eac0: add             x1, x1, HEAP, lsl #32
    // 0x91eac4: LoadField: r2 = r1->field_47
    //     0x91eac4: ldur            w2, [x1, #0x47]
    // 0x91eac8: DecompressPointer r2
    //     0x91eac8: add             x2, x2, HEAP, lsl #32
    // 0x91eacc: mov             x1, x2
    // 0x91ead0: r0 = value()
    //     0x91ead0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x91ead4: tbnz            w0, #4, #0x91eaf8
    // 0x91ead8: ldur            x0, [fp, #-0x10]
    // 0x91eadc: LoadField: r1 = r0->field_f
    //     0x91eadc: ldur            w1, [x0, #0xf]
    // 0x91eae0: DecompressPointer r1
    //     0x91eae0: add             x1, x1, HEAP, lsl #32
    // 0x91eae4: LoadField: r0 = r1->field_47
    //     0x91eae4: ldur            w0, [x1, #0x47]
    // 0x91eae8: DecompressPointer r0
    //     0x91eae8: add             x0, x0, HEAP, lsl #32
    // 0x91eaec: mov             x1, x0
    // 0x91eaf0: r2 = false
    //     0x91eaf0: add             x2, NULL, #0x30  ; false
    // 0x91eaf4: r0 = value=()
    //     0x91eaf4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x91eaf8: r0 = Null
    //     0x91eaf8: mov             x0, NULL
    // 0x91eafc: LeaveFrame
    //     0x91eafc: mov             SP, fp
    //     0x91eb00: ldp             fp, lr, [SP], #0x10
    // 0x91eb04: ret
    //     0x91eb04: ret             
    // 0x91eb08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91eb08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91eb0c: b               #0x91e864
    // 0x91eb10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91eb10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x91eb14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91eb14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x91eb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91eb18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x91ebb8, size: 0x50
    // 0x91ebb8: EnterFrame
    //     0x91ebb8: stp             fp, lr, [SP, #-0x10]!
    //     0x91ebbc: mov             fp, SP
    // 0x91ebc0: ldr             x0, [fp, #0x18]
    // 0x91ebc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91ebc4: ldur            w1, [x0, #0x17]
    // 0x91ebc8: DecompressPointer r1
    //     0x91ebc8: add             x1, x1, HEAP, lsl #32
    // 0x91ebcc: CheckStackOverflow
    //     0x91ebcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ebd0: cmp             SP, x16
    //     0x91ebd4: b.ls            #0x91ec00
    // 0x91ebd8: LoadField: r0 = r1->field_f
    //     0x91ebd8: ldur            w0, [x1, #0xf]
    // 0x91ebdc: DecompressPointer r0
    //     0x91ebdc: add             x0, x0, HEAP, lsl #32
    // 0x91ebe0: LoadField: r1 = r0->field_4b
    //     0x91ebe0: ldur            w1, [x0, #0x4b]
    // 0x91ebe4: DecompressPointer r1
    //     0x91ebe4: add             x1, x1, HEAP, lsl #32
    // 0x91ebe8: r2 = false
    //     0x91ebe8: add             x2, NULL, #0x30  ; false
    // 0x91ebec: r0 = value=()
    //     0x91ebec: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x91ebf0: r0 = Null
    //     0x91ebf0: mov             x0, NULL
    // 0x91ebf4: LeaveFrame
    //     0x91ebf4: mov             SP, fp
    //     0x91ebf8: ldp             fp, lr, [SP], #0x10
    // 0x91ebfc: ret
    //     0x91ebfc: ret             
    // 0x91ec00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ec00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ec04: b               #0x91ebd8
  }
  AnimationController animationController(CampaignDetailController) {
    // ** addr: 0x91ec08, size: 0x5c
    // 0x91ec08: EnterFrame
    //     0x91ec08: stp             fp, lr, [SP, #-0x10]!
    //     0x91ec0c: mov             fp, SP
    // 0x91ec10: AllocStack(0x10)
    //     0x91ec10: sub             SP, SP, #0x10
    // 0x91ec14: CheckStackOverflow
    //     0x91ec14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ec18: cmp             SP, x16
    //     0x91ec1c: b.ls            #0x91ec5c
    // 0x91ec20: r1 = <double>
    //     0x91ec20: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x91ec24: r0 = AnimationController()
    //     0x91ec24: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x91ec28: stur            x0, [fp, #-8]
    // 0x91ec2c: r16 = Instance_Duration
    //     0x91ec2c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Obj!Duration@e3a191
    //     0x91ec30: ldr             x16, [x16, #0xb08]
    // 0x91ec34: str             x16, [SP]
    // 0x91ec38: mov             x1, x0
    // 0x91ec3c: ldr             x2, [fp, #0x10]
    // 0x91ec40: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x91ec40: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x91ec44: ldr             x4, [x4, #0x408]
    // 0x91ec48: r0 = AnimationController()
    //     0x91ec48: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x91ec4c: ldur            x0, [fp, #-8]
    // 0x91ec50: LeaveFrame
    //     0x91ec50: mov             SP, fp
    //     0x91ec54: ldp             fp, lr, [SP], #0x10
    // 0x91ec58: ret
    //     0x91ec58: ret             
    // 0x91ec5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ec5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ec60: b               #0x91ec20
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9277e0, size: 0x70
    // 0x9277e0: EnterFrame
    //     0x9277e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9277e4: mov             fp, SP
    // 0x9277e8: AllocStack(0x8)
    //     0x9277e8: sub             SP, SP, #8
    // 0x9277ec: SetupParameters(CampaignDetailController this /* r1 => r0, fp-0x8 */)
    //     0x9277ec: mov             x0, x1
    //     0x9277f0: stur            x1, [fp, #-8]
    // 0x9277f4: CheckStackOverflow
    //     0x9277f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9277f8: cmp             SP, x16
    //     0x9277fc: b.ls            #0x927848
    // 0x927800: LoadField: r1 = r0->field_3f
    //     0x927800: ldur            w1, [x0, #0x3f]
    // 0x927804: DecompressPointer r1
    //     0x927804: add             x1, x1, HEAP, lsl #32
    // 0x927808: r0 = dispose()
    //     0x927808: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0x92780c: ldur            x1, [fp, #-8]
    // 0x927810: LoadField: r0 = r1->field_43
    //     0x927810: ldur            w0, [x1, #0x43]
    // 0x927814: DecompressPointer r0
    //     0x927814: add             x0, x0, HEAP, lsl #32
    // 0x927818: r16 = Sentinel
    //     0x927818: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92781c: cmp             w0, w16
    // 0x927820: b.ne            #0x927830
    // 0x927824: r2 = animationController
    //     0x927824: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f5b8] Field <CampaignDetailController.animationController>: late final (offset: 0x44)
    //     0x927828: ldr             x2, [x2, #0x5b8]
    // 0x92782c: r0 = InitLateFinalInstanceField()
    //     0x92782c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x927830: mov             x1, x0
    // 0x927834: r0 = dispose()
    //     0x927834: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x927838: r0 = Null
    //     0x927838: mov             x0, NULL
    // 0x92783c: LeaveFrame
    //     0x92783c: mov             SP, fp
    //     0x927840: ldp             fp, lr, [SP], #0x10
    // 0x927844: ret
    //     0x927844: ret             
    // 0x927848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92784c: b               #0x927800
  }
  [closure] void pay(dynamic) {
    // ** addr: 0xadd70c, size: 0x38
    // 0xadd70c: EnterFrame
    //     0xadd70c: stp             fp, lr, [SP, #-0x10]!
    //     0xadd710: mov             fp, SP
    // 0xadd714: ldr             x0, [fp, #0x10]
    // 0xadd718: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadd718: ldur            w1, [x0, #0x17]
    // 0xadd71c: DecompressPointer r1
    //     0xadd71c: add             x1, x1, HEAP, lsl #32
    // 0xadd720: CheckStackOverflow
    //     0xadd720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd724: cmp             SP, x16
    //     0xadd728: b.ls            #0xadd73c
    // 0xadd72c: r0 = pay()
    //     0xadd72c: bl              #0xadd744  ; [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::pay
    // 0xadd730: LeaveFrame
    //     0xadd730: mov             SP, fp
    //     0xadd734: ldp             fp, lr, [SP], #0x10
    // 0xadd738: ret
    //     0xadd738: ret             
    // 0xadd73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd740: b               #0xadd72c
  }
  _ pay(/* No info */) {
    // ** addr: 0xadd744, size: 0xbc
    // 0xadd744: EnterFrame
    //     0xadd744: stp             fp, lr, [SP, #-0x10]!
    //     0xadd748: mov             fp, SP
    // 0xadd74c: AllocStack(0x28)
    //     0xadd74c: sub             SP, SP, #0x28
    // 0xadd750: SetupParameters(CampaignDetailController this /* r1 => r1, fp-0x8 */)
    //     0xadd750: stur            x1, [fp, #-8]
    // 0xadd754: CheckStackOverflow
    //     0xadd754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd758: cmp             SP, x16
    //     0xadd75c: b.ls            #0xadd7f8
    // 0xadd760: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadd760: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadd764: ldr             x0, [x0, #0x2670]
    //     0xadd768: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadd76c: cmp             w0, w16
    //     0xadd770: b.ne            #0xadd77c
    //     0xadd774: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadd778: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadd77c: ldur            x1, [fp, #-8]
    // 0xadd780: LoadField: r0 = r1->field_37
    //     0xadd780: ldur            w0, [x1, #0x37]
    // 0xadd784: DecompressPointer r0
    //     0xadd784: add             x0, x0, HEAP, lsl #32
    // 0xadd788: r16 = Instance_PaymentType
    //     0xadd788: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xadd78c: ldr             x16, [x16, #0x5f0]
    // 0xadd790: cmp             w0, w16
    // 0xadd794: b.ne            #0xadd7a4
    // 0xadd798: r0 = "/donation/pay-qurban"
    //     0xadd798: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5d0] "/donation/pay-qurban"
    //     0xadd79c: ldr             x0, [x0, #0x5d0]
    // 0xadd7a0: b               #0xadd7c8
    // 0xadd7a4: r16 = Instance_PaymentType
    //     0xadd7a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xadd7a8: ldr             x16, [x16, #0x608]
    // 0xadd7ac: cmp             w0, w16
    // 0xadd7b0: b.ne            #0xadd7c0
    // 0xadd7b4: r0 = "/donation/pay-koin-nu"
    //     0xadd7b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5d8] "/donation/pay-koin-nu"
    //     0xadd7b8: ldr             x0, [x0, #0x5d8]
    // 0xadd7bc: b               #0xadd7c8
    // 0xadd7c0: r0 = "/donation/pay-campaign"
    //     0xadd7c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5e0] "/donation/pay-campaign"
    //     0xadd7c4: ldr             x0, [x0, #0x5e0]
    // 0xadd7c8: stur            x0, [fp, #-0x10]
    // 0xadd7cc: r0 = value()
    //     0xadd7cc: bl              #0x6fbc78  ; [package:nuonline/common/mixins/fetch_mixin.dart] _SimpleFetchController&FetchController&StateMixin::value
    // 0xadd7d0: ldur            x16, [fp, #-0x10]
    // 0xadd7d4: stp             x16, NULL, [SP, #8]
    // 0xadd7d8: str             x0, [SP]
    // 0xadd7dc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xadd7dc: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xadd7e0: ldr             x4, [x4, #0x478]
    // 0xadd7e4: r0 = GetNavigation.toNamed()
    //     0xadd7e4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xadd7e8: r0 = Null
    //     0xadd7e8: mov             x0, NULL
    // 0xadd7ec: LeaveFrame
    //     0xadd7ec: mov             SP, fp
    //     0xadd7f0: ldp             fp, lr, [SP], #0x10
    // 0xadd7f4: ret
    //     0xadd7f4: ret             
    // 0xadd7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd7f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd7fc: b               #0xadd760
  }
}
