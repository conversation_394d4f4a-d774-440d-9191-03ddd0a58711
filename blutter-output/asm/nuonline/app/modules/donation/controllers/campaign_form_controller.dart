// lib: , url: package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart

// class id: 1050202, size: 0x8
class :: {
}

// class id: 1914, size: 0x64, field offset: 0x40
class CampaignFormController extends DonationFormController {

  _ CampaignFormController(/* No info */) {
    // ** addr: 0x8f8970, size: 0x260
    // 0x8f8970: EnterFrame
    //     0x8f8970: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8974: mov             fp, SP
    // 0x8f8978: AllocStack(0x20)
    //     0x8f8978: sub             SP, SP, #0x20
    // 0x8f897c: SetupParameters(CampaignFormController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x8f897c: mov             x0, x2
    //     0x8f8980: stur            x2, [fp, #-0x10]
    //     0x8f8984: mov             x2, x3
    //     0x8f8988: stur            x3, [fp, #-0x18]
    //     0x8f898c: mov             x3, x1
    //     0x8f8990: stur            x1, [fp, #-8]
    // 0x8f8994: CheckStackOverflow
    //     0x8f8994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8998: cmp             SP, x16
    //     0x8f899c: b.ls            #0x8f8bc8
    // 0x8f89a0: r1 = <Province?, Province>
    //     0x8f89a0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30010] TypeArguments: <Province?, Province>
    //     0x8f89a4: ldr             x1, [x1, #0x10]
    // 0x8f89a8: r0 = Rxn()
    //     0x8f89a8: bl              #0x83ec38  ; AllocateRxnStub -> Rxn<C1X0> (size=0x20)
    // 0x8f89ac: mov             x1, x0
    // 0x8f89b0: r2 = Null
    //     0x8f89b0: mov             x2, NULL
    // 0x8f89b4: stur            x0, [fp, #-0x20]
    // 0x8f89b8: r0 = _RxImpl()
    //     0x8f89b8: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x8f89bc: ldur            x0, [fp, #-0x20]
    // 0x8f89c0: ldur            x2, [fp, #-8]
    // 0x8f89c4: StoreField: r2->field_43 = r0
    //     0x8f89c4: stur            w0, [x2, #0x43]
    //     0x8f89c8: ldurb           w16, [x2, #-1]
    //     0x8f89cc: ldurb           w17, [x0, #-1]
    //     0x8f89d0: and             x16, x17, x16, lsr #2
    //     0x8f89d4: tst             x16, HEAP, lsr #32
    //     0x8f89d8: b.eq            #0x8f89e0
    //     0x8f89dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f89e0: r1 = <SimpleRegency?, SimpleRegency>
    //     0x8f89e0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30018] TypeArguments: <SimpleRegency?, SimpleRegency>
    //     0x8f89e4: ldr             x1, [x1, #0x18]
    // 0x8f89e8: r0 = Rxn()
    //     0x8f89e8: bl              #0x83ec38  ; AllocateRxnStub -> Rxn<C1X0> (size=0x20)
    // 0x8f89ec: mov             x1, x0
    // 0x8f89f0: r2 = Null
    //     0x8f89f0: mov             x2, NULL
    // 0x8f89f4: stur            x0, [fp, #-0x20]
    // 0x8f89f8: r0 = _RxImpl()
    //     0x8f89f8: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x8f89fc: ldur            x0, [fp, #-0x20]
    // 0x8f8a00: ldur            x2, [fp, #-8]
    // 0x8f8a04: StoreField: r2->field_47 = r0
    //     0x8f8a04: stur            w0, [x2, #0x47]
    //     0x8f8a08: ldurb           w16, [x2, #-1]
    //     0x8f8a0c: ldurb           w17, [x0, #-1]
    //     0x8f8a10: and             x16, x17, x16, lsr #2
    //     0x8f8a14: tst             x16, HEAP, lsr #32
    //     0x8f8a18: b.eq            #0x8f8a20
    //     0x8f8a1c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8a20: r1 = <District?, District>
    //     0x8f8a20: add             x1, PP, #0x30, lsl #12  ; [pp+0x30020] TypeArguments: <District?, District>
    //     0x8f8a24: ldr             x1, [x1, #0x20]
    // 0x8f8a28: r0 = Rxn()
    //     0x8f8a28: bl              #0x83ec38  ; AllocateRxnStub -> Rxn<C1X0> (size=0x20)
    // 0x8f8a2c: mov             x1, x0
    // 0x8f8a30: r2 = Null
    //     0x8f8a30: mov             x2, NULL
    // 0x8f8a34: stur            x0, [fp, #-0x20]
    // 0x8f8a38: r0 = _RxImpl()
    //     0x8f8a38: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x8f8a3c: ldur            x0, [fp, #-0x20]
    // 0x8f8a40: ldur            x2, [fp, #-8]
    // 0x8f8a44: StoreField: r2->field_4b = r0
    //     0x8f8a44: stur            w0, [x2, #0x4b]
    //     0x8f8a48: ldurb           w16, [x2, #-1]
    //     0x8f8a4c: ldurb           w17, [x0, #-1]
    //     0x8f8a50: and             x16, x17, x16, lsr #2
    //     0x8f8a54: tst             x16, HEAP, lsr #32
    //     0x8f8a58: b.eq            #0x8f8a60
    //     0x8f8a5c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8a60: r1 = <Locality?, Locality>
    //     0x8f8a60: add             x1, PP, #0x30, lsl #12  ; [pp+0x30028] TypeArguments: <Locality?, Locality>
    //     0x8f8a64: ldr             x1, [x1, #0x28]
    // 0x8f8a68: r0 = Rxn()
    //     0x8f8a68: bl              #0x83ec38  ; AllocateRxnStub -> Rxn<C1X0> (size=0x20)
    // 0x8f8a6c: mov             x1, x0
    // 0x8f8a70: r2 = Null
    //     0x8f8a70: mov             x2, NULL
    // 0x8f8a74: stur            x0, [fp, #-0x20]
    // 0x8f8a78: r0 = _RxImpl()
    //     0x8f8a78: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x8f8a7c: ldur            x0, [fp, #-0x20]
    // 0x8f8a80: ldur            x2, [fp, #-8]
    // 0x8f8a84: StoreField: r2->field_4f = r0
    //     0x8f8a84: stur            w0, [x2, #0x4f]
    //     0x8f8a88: ldurb           w16, [x2, #-1]
    //     0x8f8a8c: ldurb           w17, [x0, #-1]
    //     0x8f8a90: and             x16, x17, x16, lsr #2
    //     0x8f8a94: tst             x16, HEAP, lsr #32
    //     0x8f8a98: b.eq            #0x8f8aa0
    //     0x8f8a9c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8aa0: r1 = <TextEditingValue>
    //     0x8f8aa0: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f8aa4: r0 = TextEditingController()
    //     0x8f8aa4: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f8aa8: mov             x1, x0
    // 0x8f8aac: stur            x0, [fp, #-0x20]
    // 0x8f8ab0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f8ab0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f8ab4: r0 = TextEditingController()
    //     0x8f8ab4: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f8ab8: ldur            x0, [fp, #-0x20]
    // 0x8f8abc: ldur            x2, [fp, #-8]
    // 0x8f8ac0: StoreField: r2->field_53 = r0
    //     0x8f8ac0: stur            w0, [x2, #0x53]
    //     0x8f8ac4: ldurb           w16, [x2, #-1]
    //     0x8f8ac8: ldurb           w17, [x0, #-1]
    //     0x8f8acc: and             x16, x17, x16, lsr #2
    //     0x8f8ad0: tst             x16, HEAP, lsr #32
    //     0x8f8ad4: b.eq            #0x8f8adc
    //     0x8f8ad8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8adc: r1 = <TextEditingValue>
    //     0x8f8adc: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f8ae0: r0 = TextEditingController()
    //     0x8f8ae0: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f8ae4: mov             x1, x0
    // 0x8f8ae8: stur            x0, [fp, #-0x20]
    // 0x8f8aec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f8aec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f8af0: r0 = TextEditingController()
    //     0x8f8af0: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f8af4: ldur            x0, [fp, #-0x20]
    // 0x8f8af8: ldur            x2, [fp, #-8]
    // 0x8f8afc: StoreField: r2->field_57 = r0
    //     0x8f8afc: stur            w0, [x2, #0x57]
    //     0x8f8b00: ldurb           w16, [x2, #-1]
    //     0x8f8b04: ldurb           w17, [x0, #-1]
    //     0x8f8b08: and             x16, x17, x16, lsr #2
    //     0x8f8b0c: tst             x16, HEAP, lsr #32
    //     0x8f8b10: b.eq            #0x8f8b18
    //     0x8f8b14: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8b18: r1 = <TextEditingValue>
    //     0x8f8b18: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f8b1c: r0 = TextEditingController()
    //     0x8f8b1c: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f8b20: mov             x1, x0
    // 0x8f8b24: stur            x0, [fp, #-0x20]
    // 0x8f8b28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f8b28: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f8b2c: r0 = TextEditingController()
    //     0x8f8b2c: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f8b30: ldur            x0, [fp, #-0x20]
    // 0x8f8b34: ldur            x2, [fp, #-8]
    // 0x8f8b38: StoreField: r2->field_5b = r0
    //     0x8f8b38: stur            w0, [x2, #0x5b]
    //     0x8f8b3c: ldurb           w16, [x2, #-1]
    //     0x8f8b40: ldurb           w17, [x0, #-1]
    //     0x8f8b44: and             x16, x17, x16, lsr #2
    //     0x8f8b48: tst             x16, HEAP, lsr #32
    //     0x8f8b4c: b.eq            #0x8f8b54
    //     0x8f8b50: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8b54: r1 = <TextEditingValue>
    //     0x8f8b54: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f8b58: r0 = TextEditingController()
    //     0x8f8b58: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f8b5c: mov             x1, x0
    // 0x8f8b60: stur            x0, [fp, #-0x20]
    // 0x8f8b64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f8b64: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f8b68: r0 = TextEditingController()
    //     0x8f8b68: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f8b6c: ldur            x0, [fp, #-0x20]
    // 0x8f8b70: ldur            x1, [fp, #-8]
    // 0x8f8b74: StoreField: r1->field_5f = r0
    //     0x8f8b74: stur            w0, [x1, #0x5f]
    //     0x8f8b78: ldurb           w16, [x1, #-1]
    //     0x8f8b7c: ldurb           w17, [x0, #-1]
    //     0x8f8b80: and             x16, x17, x16, lsr #2
    //     0x8f8b84: tst             x16, HEAP, lsr #32
    //     0x8f8b88: b.eq            #0x8f8b90
    //     0x8f8b8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8b90: ldur            x0, [fp, #-0x10]
    // 0x8f8b94: StoreField: r1->field_3f = r0
    //     0x8f8b94: stur            w0, [x1, #0x3f]
    //     0x8f8b98: ldurb           w16, [x1, #-1]
    //     0x8f8b9c: ldurb           w17, [x0, #-1]
    //     0x8f8ba0: and             x16, x17, x16, lsr #2
    //     0x8f8ba4: tst             x16, HEAP, lsr #32
    //     0x8f8ba8: b.eq            #0x8f8bb0
    //     0x8f8bac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8bb0: ldur            x2, [fp, #-0x18]
    // 0x8f8bb4: r0 = DonationFormController()
    //     0x8f8bb4: bl              #0x8f8f2c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::DonationFormController
    // 0x8f8bb8: r0 = Null
    //     0x8f8bb8: mov             x0, NULL
    // 0x8f8bbc: LeaveFrame
    //     0x8f8bbc: mov             SP, fp
    //     0x8f8bc0: ldp             fp, lr, [SP], #0x10
    // 0x8f8bc4: ret
    //     0x8f8bc4: ret             
    // 0x8f8bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8bc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8bcc: b               #0x8f89a0
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927990, size: 0x74
    // 0x927990: EnterFrame
    //     0x927990: stp             fp, lr, [SP, #-0x10]!
    //     0x927994: mov             fp, SP
    // 0x927998: AllocStack(0x8)
    //     0x927998: sub             SP, SP, #8
    // 0x92799c: SetupParameters(CampaignFormController this /* r1 => r0, fp-0x8 */)
    //     0x92799c: mov             x0, x1
    //     0x9279a0: stur            x1, [fp, #-8]
    // 0x9279a4: CheckStackOverflow
    //     0x9279a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9279a8: cmp             SP, x16
    //     0x9279ac: b.ls            #0x9279fc
    // 0x9279b0: LoadField: r1 = r0->field_53
    //     0x9279b0: ldur            w1, [x0, #0x53]
    // 0x9279b4: DecompressPointer r1
    //     0x9279b4: add             x1, x1, HEAP, lsl #32
    // 0x9279b8: r0 = dispose()
    //     0x9279b8: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9279bc: ldur            x0, [fp, #-8]
    // 0x9279c0: LoadField: r1 = r0->field_57
    //     0x9279c0: ldur            w1, [x0, #0x57]
    // 0x9279c4: DecompressPointer r1
    //     0x9279c4: add             x1, x1, HEAP, lsl #32
    // 0x9279c8: r0 = dispose()
    //     0x9279c8: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9279cc: ldur            x0, [fp, #-8]
    // 0x9279d0: LoadField: r1 = r0->field_5b
    //     0x9279d0: ldur            w1, [x0, #0x5b]
    // 0x9279d4: DecompressPointer r1
    //     0x9279d4: add             x1, x1, HEAP, lsl #32
    // 0x9279d8: r0 = dispose()
    //     0x9279d8: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9279dc: ldur            x0, [fp, #-8]
    // 0x9279e0: LoadField: r1 = r0->field_5f
    //     0x9279e0: ldur            w1, [x0, #0x5f]
    // 0x9279e4: DecompressPointer r1
    //     0x9279e4: add             x1, x1, HEAP, lsl #32
    // 0x9279e8: r0 = dispose()
    //     0x9279e8: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9279ec: r0 = Null
    //     0x9279ec: mov             x0, NULL
    // 0x9279f0: LeaveFrame
    //     0x9279f0: mov             SP, fp
    //     0x9279f4: ldp             fp, lr, [SP], #0x10
    // 0x9279f8: ret
    //     0x9279f8: ret             
    // 0x9279fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9279fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927a00: b               #0x9279b0
  }
  get _ isKoinNu(/* No info */) {
    // ** addr: 0xae7610, size: 0x4c
    // 0xae7610: LoadField: r2 = r1->field_3f
    //     0xae7610: ldur            w2, [x1, #0x3f]
    // 0xae7614: DecompressPointer r2
    //     0xae7614: add             x2, x2, HEAP, lsl #32
    // 0xae7618: cmp             w2, NULL
    // 0xae761c: b.ne            #0xae7628
    // 0xae7620: r1 = Null
    //     0xae7620: mov             x1, NULL
    // 0xae7624: b               #0xae7644
    // 0xae7628: LoadField: r1 = r2->field_33
    //     0xae7628: ldur            w1, [x2, #0x33]
    // 0xae762c: DecompressPointer r1
    //     0xae762c: add             x1, x1, HEAP, lsl #32
    // 0xae7630: LoadField: r2 = r1->field_7
    //     0xae7630: ldur            x2, [x1, #7]
    // 0xae7634: cmp             x2, #6
    // 0xae7638: r16 = true
    //     0xae7638: add             x16, NULL, #0x20  ; true
    // 0xae763c: r17 = false
    //     0xae763c: add             x17, NULL, #0x30  ; false
    // 0xae7640: csel            x1, x16, x17, eq
    // 0xae7644: cmp             w1, NULL
    // 0xae7648: b.ne            #0xae7654
    // 0xae764c: r0 = false
    //     0xae764c: add             x0, NULL, #0x30  ; false
    // 0xae7650: b               #0xae7658
    // 0xae7654: mov             x0, x1
    // 0xae7658: ret
    //     0xae7658: ret             
  }
  get _ title(/* No info */) {
    // ** addr: 0xae78d0, size: 0x88
    // 0xae78d0: EnterFrame
    //     0xae78d0: stp             fp, lr, [SP, #-0x10]!
    //     0xae78d4: mov             fp, SP
    // 0xae78d8: AllocStack(0x8)
    //     0xae78d8: sub             SP, SP, #8
    // 0xae78dc: SetupParameters(CampaignFormController this /* r1 => r0, fp-0x8 */)
    //     0xae78dc: mov             x0, x1
    //     0xae78e0: stur            x1, [fp, #-8]
    // 0xae78e4: CheckStackOverflow
    //     0xae78e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae78e8: cmp             SP, x16
    //     0xae78ec: b.ls            #0xae7950
    // 0xae78f0: mov             x1, x0
    // 0xae78f4: r0 = isKoinNu()
    //     0xae78f4: bl              #0xae7610  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::isKoinNu
    // 0xae78f8: tbnz            w0, #4, #0xae7940
    // 0xae78fc: ldur            x1, [fp, #-8]
    // 0xae7900: LoadField: r2 = r1->field_3f
    //     0xae7900: ldur            w2, [x1, #0x3f]
    // 0xae7904: DecompressPointer r2
    //     0xae7904: add             x2, x2, HEAP, lsl #32
    // 0xae7908: cmp             w2, NULL
    // 0xae790c: b.ne            #0xae7918
    // 0xae7910: r1 = Null
    //     0xae7910: mov             x1, NULL
    // 0xae7914: b               #0xae7920
    // 0xae7918: LoadField: r1 = r2->field_f
    //     0xae7918: ldur            w1, [x2, #0xf]
    // 0xae791c: DecompressPointer r1
    //     0xae791c: add             x1, x1, HEAP, lsl #32
    // 0xae7920: cmp             w1, NULL
    // 0xae7924: b.ne            #0xae7930
    // 0xae7928: r0 = ""
    //     0xae7928: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae792c: b               #0xae7934
    // 0xae7930: mov             x0, x1
    // 0xae7934: LeaveFrame
    //     0xae7934: mov             SP, fp
    //     0xae7938: ldp             fp, lr, [SP], #0x10
    // 0xae793c: ret
    //     0xae793c: ret             
    // 0xae7940: r0 = ""
    //     0xae7940: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae7944: LeaveFrame
    //     0xae7944: mov             SP, fp
    //     0xae7948: ldp             fp, lr, [SP], #0x10
    // 0xae794c: ret
    //     0xae794c: ret             
    // 0xae7950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7954: b               #0xae78f0
  }
  [closure] Future<void> selectLocality(dynamic) {
    // ** addr: 0xae8b74, size: 0x38
    // 0xae8b74: EnterFrame
    //     0xae8b74: stp             fp, lr, [SP, #-0x10]!
    //     0xae8b78: mov             fp, SP
    // 0xae8b7c: ldr             x0, [fp, #0x10]
    // 0xae8b80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae8b80: ldur            w1, [x0, #0x17]
    // 0xae8b84: DecompressPointer r1
    //     0xae8b84: add             x1, x1, HEAP, lsl #32
    // 0xae8b88: CheckStackOverflow
    //     0xae8b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8b8c: cmp             SP, x16
    //     0xae8b90: b.ls            #0xae8ba4
    // 0xae8b94: r0 = selectLocality()
    //     0xae8b94: bl              #0xae8bac  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectLocality
    // 0xae8b98: LeaveFrame
    //     0xae8b98: mov             SP, fp
    //     0xae8b9c: ldp             fp, lr, [SP], #0x10
    // 0xae8ba0: ret
    //     0xae8ba0: ret             
    // 0xae8ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8ba8: b               #0xae8b94
  }
  _ selectLocality(/* No info */) async {
    // ** addr: 0xae8bac, size: 0x168
    // 0xae8bac: EnterFrame
    //     0xae8bac: stp             fp, lr, [SP, #-0x10]!
    //     0xae8bb0: mov             fp, SP
    // 0xae8bb4: AllocStack(0x30)
    //     0xae8bb4: sub             SP, SP, #0x30
    // 0xae8bb8: SetupParameters(CampaignFormController this /* r1 => r1, fp-0x10 */)
    //     0xae8bb8: stur            NULL, [fp, #-8]
    //     0xae8bbc: stur            x1, [fp, #-0x10]
    // 0xae8bc0: CheckStackOverflow
    //     0xae8bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8bc4: cmp             SP, x16
    //     0xae8bc8: b.ls            #0xae8d0c
    // 0xae8bcc: InitAsync() -> Future<void?>
    //     0xae8bcc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae8bd0: bl              #0x661298  ; InitAsyncStub
    // 0xae8bd4: ldur            x0, [fp, #-0x10]
    // 0xae8bd8: LoadField: r1 = r0->field_4b
    //     0xae8bd8: ldur            w1, [x0, #0x4b]
    // 0xae8bdc: DecompressPointer r1
    //     0xae8bdc: add             x1, x1, HEAP, lsl #32
    // 0xae8be0: r0 = value()
    //     0xae8be0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae8be4: cmp             w0, NULL
    // 0xae8be8: b.ne            #0xae8bf4
    // 0xae8bec: r0 = Null
    //     0xae8bec: mov             x0, NULL
    // 0xae8bf0: b               #0xae8c0c
    // 0xae8bf4: LoadField: r2 = r0->field_7
    //     0xae8bf4: ldur            x2, [x0, #7]
    // 0xae8bf8: r0 = BoxInt64Instr(r2)
    //     0xae8bf8: sbfiz           x0, x2, #1, #0x1f
    //     0xae8bfc: cmp             x2, x0, asr #1
    //     0xae8c00: b.eq            #0xae8c0c
    //     0xae8c04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8c08: stur            x2, [x0, #7]
    // 0xae8c0c: cmp             w0, NULL
    // 0xae8c10: b.ne            #0xae8c44
    // 0xae8c14: ldur            x1, [fp, #-0x10]
    // 0xae8c18: LoadField: r0 = r1->field_3f
    //     0xae8c18: ldur            w0, [x1, #0x3f]
    // 0xae8c1c: DecompressPointer r0
    //     0xae8c1c: add             x0, x0, HEAP, lsl #32
    // 0xae8c20: cmp             w0, NULL
    // 0xae8c24: b.ne            #0xae8c30
    // 0xae8c28: r0 = Null
    //     0xae8c28: mov             x0, NULL
    // 0xae8c2c: b               #0xae8c48
    // 0xae8c30: LoadField: r2 = r0->field_3b
    //     0xae8c30: ldur            w2, [x0, #0x3b]
    // 0xae8c34: DecompressPointer r2
    //     0xae8c34: add             x2, x2, HEAP, lsl #32
    // 0xae8c38: LoadField: r0 = r2->field_23
    //     0xae8c38: ldur            w0, [x2, #0x23]
    // 0xae8c3c: DecompressPointer r0
    //     0xae8c3c: add             x0, x0, HEAP, lsl #32
    // 0xae8c40: b               #0xae8c48
    // 0xae8c44: ldur            x1, [fp, #-0x10]
    // 0xae8c48: stur            x0, [fp, #-0x18]
    // 0xae8c4c: r1 = 1
    //     0xae8c4c: movz            x1, #0x1
    // 0xae8c50: r0 = AllocateContext()
    //     0xae8c50: bl              #0xec126c  ; AllocateContextStub
    // 0xae8c54: mov             x1, x0
    // 0xae8c58: ldur            x0, [fp, #-0x18]
    // 0xae8c5c: stur            x1, [fp, #-0x20]
    // 0xae8c60: StoreField: r1->field_f = r0
    //     0xae8c60: stur            w0, [x1, #0xf]
    // 0xae8c64: cmp             w0, NULL
    // 0xae8c68: b.ne            #0xae8c74
    // 0xae8c6c: r0 = Null
    //     0xae8c6c: mov             x0, NULL
    // 0xae8c70: r0 = ReturnAsyncNotFuture()
    //     0xae8c70: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae8c74: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8c74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8c78: ldr             x0, [x0, #0x2670]
    //     0xae8c7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae8c80: cmp             w0, w16
    //     0xae8c84: b.ne            #0xae8c90
    //     0xae8c88: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae8c8c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae8c90: ldur            x2, [fp, #-0x20]
    // 0xae8c94: r1 = Function '<anonymous closure>':.
    //     0xae8c94: add             x1, PP, #0x40, lsl #12  ; [pp+0x40048] AnonymousClosure: (0xae8d14), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectLocality (0xae8bac)
    //     0xae8c98: ldr             x1, [x1, #0x48]
    // 0xae8c9c: r0 = AllocateClosure()
    //     0xae8c9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8ca0: r16 = <Locality>
    //     0xae8ca0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cf98] TypeArguments: <Locality>
    //     0xae8ca4: ldr             x16, [x16, #0xf98]
    // 0xae8ca8: stp             x0, x16, [SP]
    // 0xae8cac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae8cac: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae8cb0: r0 = GetNavigation.to()
    //     0xae8cb0: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xae8cb4: mov             x2, x0
    // 0xae8cb8: r1 = <Locality?>
    //     0xae8cb8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33aa8] TypeArguments: <Locality?>
    //     0xae8cbc: ldr             x1, [x1, #0xaa8]
    // 0xae8cc0: stur            x2, [fp, #-0x18]
    // 0xae8cc4: r0 = AwaitWithTypeCheck()
    //     0xae8cc4: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xae8cc8: stur            x0, [fp, #-0x18]
    // 0xae8ccc: cmp             w0, NULL
    // 0xae8cd0: b.eq            #0xae8d04
    // 0xae8cd4: ldur            x3, [fp, #-0x10]
    // 0xae8cd8: LoadField: r1 = r3->field_4f
    //     0xae8cd8: ldur            w1, [x3, #0x4f]
    // 0xae8cdc: DecompressPointer r1
    //     0xae8cdc: add             x1, x1, HEAP, lsl #32
    // 0xae8ce0: mov             x2, x0
    // 0xae8ce4: r0 = value=()
    //     0xae8ce4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae8ce8: ldur            x0, [fp, #-0x10]
    // 0xae8cec: LoadField: r1 = r0->field_5f
    //     0xae8cec: ldur            w1, [x0, #0x5f]
    // 0xae8cf0: DecompressPointer r1
    //     0xae8cf0: add             x1, x1, HEAP, lsl #32
    // 0xae8cf4: ldur            x0, [fp, #-0x18]
    // 0xae8cf8: LoadField: r2 = r0->field_f
    //     0xae8cf8: ldur            w2, [x0, #0xf]
    // 0xae8cfc: DecompressPointer r2
    //     0xae8cfc: add             x2, x2, HEAP, lsl #32
    // 0xae8d00: r0 = text=()
    //     0xae8d00: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae8d04: r0 = Null
    //     0xae8d04: mov             x0, NULL
    // 0xae8d08: r0 = ReturnAsyncNotFuture()
    //     0xae8d08: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae8d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8d0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8d10: b               #0xae8bcc
  }
  [closure] SelectLocalityWidget <anonymous closure>(dynamic) {
    // ** addr: 0xae8d14, size: 0x48
    // 0xae8d14: EnterFrame
    //     0xae8d14: stp             fp, lr, [SP, #-0x10]!
    //     0xae8d18: mov             fp, SP
    // 0xae8d1c: AllocStack(0x8)
    //     0xae8d1c: sub             SP, SP, #8
    // 0xae8d20: SetupParameters()
    //     0xae8d20: ldr             x0, [fp, #0x10]
    //     0xae8d24: ldur            w1, [x0, #0x17]
    //     0xae8d28: add             x1, x1, HEAP, lsl #32
    // 0xae8d2c: LoadField: r0 = r1->field_f
    //     0xae8d2c: ldur            w0, [x1, #0xf]
    // 0xae8d30: DecompressPointer r0
    //     0xae8d30: add             x0, x0, HEAP, lsl #32
    // 0xae8d34: r1 = LoadInt32Instr(r0)
    //     0xae8d34: sbfx            x1, x0, #1, #0x1f
    //     0xae8d38: tbz             w0, #0, #0xae8d40
    //     0xae8d3c: ldur            x1, [x0, #7]
    // 0xae8d40: stur            x1, [fp, #-8]
    // 0xae8d44: r0 = SelectLocalityWidget()
    //     0xae8d44: bl              #0xae8d5c  ; AllocateSelectLocalityWidgetStub -> SelectLocalityWidget (size=0x14)
    // 0xae8d48: ldur            x1, [fp, #-8]
    // 0xae8d4c: StoreField: r0->field_b = r1
    //     0xae8d4c: stur            x1, [x0, #0xb]
    // 0xae8d50: LeaveFrame
    //     0xae8d50: mov             SP, fp
    //     0xae8d54: ldp             fp, lr, [SP], #0x10
    // 0xae8d58: ret
    //     0xae8d58: ret             
  }
  [closure] Future<void> selectDistrict(dynamic) {
    // ** addr: 0xae8f3c, size: 0x38
    // 0xae8f3c: EnterFrame
    //     0xae8f3c: stp             fp, lr, [SP, #-0x10]!
    //     0xae8f40: mov             fp, SP
    // 0xae8f44: ldr             x0, [fp, #0x10]
    // 0xae8f48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae8f48: ldur            w1, [x0, #0x17]
    // 0xae8f4c: DecompressPointer r1
    //     0xae8f4c: add             x1, x1, HEAP, lsl #32
    // 0xae8f50: CheckStackOverflow
    //     0xae8f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8f54: cmp             SP, x16
    //     0xae8f58: b.ls            #0xae8f6c
    // 0xae8f5c: r0 = selectDistrict()
    //     0xae8f5c: bl              #0xae8f74  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectDistrict
    // 0xae8f60: LeaveFrame
    //     0xae8f60: mov             SP, fp
    //     0xae8f64: ldp             fp, lr, [SP], #0x10
    // 0xae8f68: ret
    //     0xae8f68: ret             
    // 0xae8f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8f70: b               #0xae8f5c
  }
  _ selectDistrict(/* No info */) async {
    // ** addr: 0xae8f74, size: 0x194
    // 0xae8f74: EnterFrame
    //     0xae8f74: stp             fp, lr, [SP, #-0x10]!
    //     0xae8f78: mov             fp, SP
    // 0xae8f7c: AllocStack(0x30)
    //     0xae8f7c: sub             SP, SP, #0x30
    // 0xae8f80: SetupParameters(CampaignFormController this /* r1 => r1, fp-0x10 */)
    //     0xae8f80: stur            NULL, [fp, #-8]
    //     0xae8f84: stur            x1, [fp, #-0x10]
    // 0xae8f88: CheckStackOverflow
    //     0xae8f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8f8c: cmp             SP, x16
    //     0xae8f90: b.ls            #0xae9100
    // 0xae8f94: InitAsync() -> Future<void?>
    //     0xae8f94: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae8f98: bl              #0x661298  ; InitAsyncStub
    // 0xae8f9c: ldur            x0, [fp, #-0x10]
    // 0xae8fa0: LoadField: r1 = r0->field_47
    //     0xae8fa0: ldur            w1, [x0, #0x47]
    // 0xae8fa4: DecompressPointer r1
    //     0xae8fa4: add             x1, x1, HEAP, lsl #32
    // 0xae8fa8: r0 = value()
    //     0xae8fa8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae8fac: cmp             w0, NULL
    // 0xae8fb0: b.ne            #0xae8fbc
    // 0xae8fb4: r0 = Null
    //     0xae8fb4: mov             x0, NULL
    // 0xae8fb8: b               #0xae8fd4
    // 0xae8fbc: LoadField: r2 = r0->field_7
    //     0xae8fbc: ldur            x2, [x0, #7]
    // 0xae8fc0: r0 = BoxInt64Instr(r2)
    //     0xae8fc0: sbfiz           x0, x2, #1, #0x1f
    //     0xae8fc4: cmp             x2, x0, asr #1
    //     0xae8fc8: b.eq            #0xae8fd4
    //     0xae8fcc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8fd0: stur            x2, [x0, #7]
    // 0xae8fd4: cmp             w0, NULL
    // 0xae8fd8: b.ne            #0xae900c
    // 0xae8fdc: ldur            x1, [fp, #-0x10]
    // 0xae8fe0: LoadField: r0 = r1->field_3f
    //     0xae8fe0: ldur            w0, [x1, #0x3f]
    // 0xae8fe4: DecompressPointer r0
    //     0xae8fe4: add             x0, x0, HEAP, lsl #32
    // 0xae8fe8: cmp             w0, NULL
    // 0xae8fec: b.ne            #0xae8ff8
    // 0xae8ff0: r0 = Null
    //     0xae8ff0: mov             x0, NULL
    // 0xae8ff4: b               #0xae9010
    // 0xae8ff8: LoadField: r2 = r0->field_3b
    //     0xae8ff8: ldur            w2, [x0, #0x3b]
    // 0xae8ffc: DecompressPointer r2
    //     0xae8ffc: add             x2, x2, HEAP, lsl #32
    // 0xae9000: LoadField: r0 = r2->field_1f
    //     0xae9000: ldur            w0, [x2, #0x1f]
    // 0xae9004: DecompressPointer r0
    //     0xae9004: add             x0, x0, HEAP, lsl #32
    // 0xae9008: b               #0xae9010
    // 0xae900c: ldur            x1, [fp, #-0x10]
    // 0xae9010: stur            x0, [fp, #-0x18]
    // 0xae9014: r1 = 1
    //     0xae9014: movz            x1, #0x1
    // 0xae9018: r0 = AllocateContext()
    //     0xae9018: bl              #0xec126c  ; AllocateContextStub
    // 0xae901c: mov             x1, x0
    // 0xae9020: ldur            x0, [fp, #-0x18]
    // 0xae9024: stur            x1, [fp, #-0x20]
    // 0xae9028: StoreField: r1->field_f = r0
    //     0xae9028: stur            w0, [x1, #0xf]
    // 0xae902c: cmp             w0, NULL
    // 0xae9030: b.ne            #0xae903c
    // 0xae9034: r0 = Null
    //     0xae9034: mov             x0, NULL
    // 0xae9038: r0 = ReturnAsyncNotFuture()
    //     0xae9038: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae903c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae903c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae9040: ldr             x0, [x0, #0x2670]
    //     0xae9044: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae9048: cmp             w0, w16
    //     0xae904c: b.ne            #0xae9058
    //     0xae9050: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae9054: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae9058: ldur            x2, [fp, #-0x20]
    // 0xae905c: r1 = Function '<anonymous closure>':.
    //     0xae905c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40060] AnonymousClosure: (0xae9108), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectDistrict (0xae8f74)
    //     0xae9060: ldr             x1, [x1, #0x60]
    // 0xae9064: r0 = AllocateClosure()
    //     0xae9064: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9068: r16 = <District>
    //     0xae9068: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ad8] TypeArguments: <District>
    //     0xae906c: ldr             x16, [x16, #0xad8]
    // 0xae9070: stp             x0, x16, [SP]
    // 0xae9074: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae9074: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae9078: r0 = GetNavigation.to()
    //     0xae9078: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xae907c: mov             x2, x0
    // 0xae9080: r1 = <District?>
    //     0xae9080: add             x1, PP, #0x40, lsl #12  ; [pp+0x40068] TypeArguments: <District?>
    //     0xae9084: ldr             x1, [x1, #0x68]
    // 0xae9088: stur            x2, [fp, #-0x18]
    // 0xae908c: r0 = AwaitWithTypeCheck()
    //     0xae908c: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xae9090: stur            x0, [fp, #-0x18]
    // 0xae9094: cmp             w0, NULL
    // 0xae9098: b.eq            #0xae90f8
    // 0xae909c: ldur            x3, [fp, #-0x10]
    // 0xae90a0: LoadField: r1 = r3->field_4b
    //     0xae90a0: ldur            w1, [x3, #0x4b]
    // 0xae90a4: DecompressPointer r1
    //     0xae90a4: add             x1, x1, HEAP, lsl #32
    // 0xae90a8: mov             x2, x0
    // 0xae90ac: r0 = value=()
    //     0xae90ac: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae90b0: ldur            x0, [fp, #-0x10]
    // 0xae90b4: LoadField: r1 = r0->field_5b
    //     0xae90b4: ldur            w1, [x0, #0x5b]
    // 0xae90b8: DecompressPointer r1
    //     0xae90b8: add             x1, x1, HEAP, lsl #32
    // 0xae90bc: ldur            x2, [fp, #-0x18]
    // 0xae90c0: LoadField: r3 = r2->field_f
    //     0xae90c0: ldur            w3, [x2, #0xf]
    // 0xae90c4: DecompressPointer r3
    //     0xae90c4: add             x3, x3, HEAP, lsl #32
    // 0xae90c8: mov             x2, x3
    // 0xae90cc: r0 = text=()
    //     0xae90cc: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae90d0: ldur            x0, [fp, #-0x10]
    // 0xae90d4: LoadField: r1 = r0->field_4f
    //     0xae90d4: ldur            w1, [x0, #0x4f]
    // 0xae90d8: DecompressPointer r1
    //     0xae90d8: add             x1, x1, HEAP, lsl #32
    // 0xae90dc: r2 = Null
    //     0xae90dc: mov             x2, NULL
    // 0xae90e0: r0 = value=()
    //     0xae90e0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae90e4: ldur            x0, [fp, #-0x10]
    // 0xae90e8: LoadField: r1 = r0->field_5f
    //     0xae90e8: ldur            w1, [x0, #0x5f]
    // 0xae90ec: DecompressPointer r1
    //     0xae90ec: add             x1, x1, HEAP, lsl #32
    // 0xae90f0: r2 = ""
    //     0xae90f0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae90f4: r0 = text=()
    //     0xae90f4: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae90f8: r0 = Null
    //     0xae90f8: mov             x0, NULL
    // 0xae90fc: r0 = ReturnAsyncNotFuture()
    //     0xae90fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae9100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9104: b               #0xae8f94
  }
  [closure] SelectDistrictWidget <anonymous closure>(dynamic) {
    // ** addr: 0xae9108, size: 0x48
    // 0xae9108: EnterFrame
    //     0xae9108: stp             fp, lr, [SP, #-0x10]!
    //     0xae910c: mov             fp, SP
    // 0xae9110: AllocStack(0x8)
    //     0xae9110: sub             SP, SP, #8
    // 0xae9114: SetupParameters()
    //     0xae9114: ldr             x0, [fp, #0x10]
    //     0xae9118: ldur            w1, [x0, #0x17]
    //     0xae911c: add             x1, x1, HEAP, lsl #32
    // 0xae9120: LoadField: r0 = r1->field_f
    //     0xae9120: ldur            w0, [x1, #0xf]
    // 0xae9124: DecompressPointer r0
    //     0xae9124: add             x0, x0, HEAP, lsl #32
    // 0xae9128: r1 = LoadInt32Instr(r0)
    //     0xae9128: sbfx            x1, x0, #1, #0x1f
    //     0xae912c: tbz             w0, #0, #0xae9134
    //     0xae9130: ldur            x1, [x0, #7]
    // 0xae9134: stur            x1, [fp, #-8]
    // 0xae9138: r0 = SelectDistrictWidget()
    //     0xae9138: bl              #0xae9150  ; AllocateSelectDistrictWidgetStub -> SelectDistrictWidget (size=0x14)
    // 0xae913c: ldur            x1, [fp, #-8]
    // 0xae9140: StoreField: r0->field_b = r1
    //     0xae9140: stur            x1, [x0, #0xb]
    // 0xae9144: LeaveFrame
    //     0xae9144: mov             SP, fp
    //     0xae9148: ldp             fp, lr, [SP], #0x10
    // 0xae914c: ret
    //     0xae914c: ret             
  }
  [closure] Future<void> selectRegency(dynamic) {
    // ** addr: 0xae9330, size: 0x38
    // 0xae9330: EnterFrame
    //     0xae9330: stp             fp, lr, [SP, #-0x10]!
    //     0xae9334: mov             fp, SP
    // 0xae9338: ldr             x0, [fp, #0x10]
    // 0xae933c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae933c: ldur            w1, [x0, #0x17]
    // 0xae9340: DecompressPointer r1
    //     0xae9340: add             x1, x1, HEAP, lsl #32
    // 0xae9344: CheckStackOverflow
    //     0xae9344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9348: cmp             SP, x16
    //     0xae934c: b.ls            #0xae9360
    // 0xae9350: r0 = selectRegency()
    //     0xae9350: bl              #0xae9368  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectRegency
    // 0xae9354: LeaveFrame
    //     0xae9354: mov             SP, fp
    //     0xae9358: ldp             fp, lr, [SP], #0x10
    // 0xae935c: ret
    //     0xae935c: ret             
    // 0xae9360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9364: b               #0xae9350
  }
  _ selectRegency(/* No info */) async {
    // ** addr: 0xae9368, size: 0x1bc
    // 0xae9368: EnterFrame
    //     0xae9368: stp             fp, lr, [SP, #-0x10]!
    //     0xae936c: mov             fp, SP
    // 0xae9370: AllocStack(0x30)
    //     0xae9370: sub             SP, SP, #0x30
    // 0xae9374: SetupParameters(CampaignFormController this /* r1 => r1, fp-0x10 */)
    //     0xae9374: stur            NULL, [fp, #-8]
    //     0xae9378: stur            x1, [fp, #-0x10]
    // 0xae937c: CheckStackOverflow
    //     0xae937c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9380: cmp             SP, x16
    //     0xae9384: b.ls            #0xae951c
    // 0xae9388: InitAsync() -> Future<void?>
    //     0xae9388: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae938c: bl              #0x661298  ; InitAsyncStub
    // 0xae9390: ldur            x0, [fp, #-0x10]
    // 0xae9394: LoadField: r1 = r0->field_43
    //     0xae9394: ldur            w1, [x0, #0x43]
    // 0xae9398: DecompressPointer r1
    //     0xae9398: add             x1, x1, HEAP, lsl #32
    // 0xae939c: r0 = value()
    //     0xae939c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae93a0: cmp             w0, NULL
    // 0xae93a4: b.ne            #0xae93b0
    // 0xae93a8: r0 = Null
    //     0xae93a8: mov             x0, NULL
    // 0xae93ac: b               #0xae93c8
    // 0xae93b0: LoadField: r2 = r0->field_13
    //     0xae93b0: ldur            x2, [x0, #0x13]
    // 0xae93b4: r0 = BoxInt64Instr(r2)
    //     0xae93b4: sbfiz           x0, x2, #1, #0x1f
    //     0xae93b8: cmp             x2, x0, asr #1
    //     0xae93bc: b.eq            #0xae93c8
    //     0xae93c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae93c4: stur            x2, [x0, #7]
    // 0xae93c8: cmp             w0, NULL
    // 0xae93cc: b.ne            #0xae9400
    // 0xae93d0: ldur            x1, [fp, #-0x10]
    // 0xae93d4: LoadField: r0 = r1->field_3f
    //     0xae93d4: ldur            w0, [x1, #0x3f]
    // 0xae93d8: DecompressPointer r0
    //     0xae93d8: add             x0, x0, HEAP, lsl #32
    // 0xae93dc: cmp             w0, NULL
    // 0xae93e0: b.ne            #0xae93ec
    // 0xae93e4: r0 = Null
    //     0xae93e4: mov             x0, NULL
    // 0xae93e8: b               #0xae9404
    // 0xae93ec: LoadField: r2 = r0->field_3b
    //     0xae93ec: ldur            w2, [x0, #0x3b]
    // 0xae93f0: DecompressPointer r2
    //     0xae93f0: add             x2, x2, HEAP, lsl #32
    // 0xae93f4: LoadField: r0 = r2->field_1b
    //     0xae93f4: ldur            w0, [x2, #0x1b]
    // 0xae93f8: DecompressPointer r0
    //     0xae93f8: add             x0, x0, HEAP, lsl #32
    // 0xae93fc: b               #0xae9404
    // 0xae9400: ldur            x1, [fp, #-0x10]
    // 0xae9404: stur            x0, [fp, #-0x18]
    // 0xae9408: r1 = 1
    //     0xae9408: movz            x1, #0x1
    // 0xae940c: r0 = AllocateContext()
    //     0xae940c: bl              #0xec126c  ; AllocateContextStub
    // 0xae9410: mov             x1, x0
    // 0xae9414: ldur            x0, [fp, #-0x18]
    // 0xae9418: stur            x1, [fp, #-0x20]
    // 0xae941c: StoreField: r1->field_f = r0
    //     0xae941c: stur            w0, [x1, #0xf]
    // 0xae9420: cmp             w0, NULL
    // 0xae9424: b.ne            #0xae9430
    // 0xae9428: r0 = Null
    //     0xae9428: mov             x0, NULL
    // 0xae942c: r0 = ReturnAsyncNotFuture()
    //     0xae942c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae9430: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae9430: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae9434: ldr             x0, [x0, #0x2670]
    //     0xae9438: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae943c: cmp             w0, w16
    //     0xae9440: b.ne            #0xae944c
    //     0xae9444: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae9448: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae944c: ldur            x2, [fp, #-0x20]
    // 0xae9450: r1 = Function '<anonymous closure>':.
    //     0xae9450: add             x1, PP, #0x40, lsl #12  ; [pp+0x40080] AnonymousClosure: (0xae9524), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectRegency (0xae9368)
    //     0xae9454: ldr             x1, [x1, #0x80]
    // 0xae9458: r0 = AllocateClosure()
    //     0xae9458: bl              #0xec1630  ; AllocateClosureStub
    // 0xae945c: r16 = <SimpleRegency>
    //     0xae945c: add             x16, PP, #0x35, lsl #12  ; [pp+0x359e8] TypeArguments: <SimpleRegency>
    //     0xae9460: ldr             x16, [x16, #0x9e8]
    // 0xae9464: stp             x0, x16, [SP]
    // 0xae9468: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae9468: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae946c: r0 = GetNavigation.to()
    //     0xae946c: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xae9470: mov             x2, x0
    // 0xae9474: r1 = <SimpleRegency?>
    //     0xae9474: add             x1, PP, #0x40, lsl #12  ; [pp+0x40088] TypeArguments: <SimpleRegency?>
    //     0xae9478: ldr             x1, [x1, #0x88]
    // 0xae947c: stur            x2, [fp, #-0x18]
    // 0xae9480: r0 = AwaitWithTypeCheck()
    //     0xae9480: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xae9484: stur            x0, [fp, #-0x18]
    // 0xae9488: cmp             w0, NULL
    // 0xae948c: b.eq            #0xae9514
    // 0xae9490: ldur            x3, [fp, #-0x10]
    // 0xae9494: LoadField: r1 = r3->field_47
    //     0xae9494: ldur            w1, [x3, #0x47]
    // 0xae9498: DecompressPointer r1
    //     0xae9498: add             x1, x1, HEAP, lsl #32
    // 0xae949c: mov             x2, x0
    // 0xae94a0: r0 = value=()
    //     0xae94a0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae94a4: ldur            x0, [fp, #-0x10]
    // 0xae94a8: LoadField: r1 = r0->field_57
    //     0xae94a8: ldur            w1, [x0, #0x57]
    // 0xae94ac: DecompressPointer r1
    //     0xae94ac: add             x1, x1, HEAP, lsl #32
    // 0xae94b0: ldur            x2, [fp, #-0x18]
    // 0xae94b4: LoadField: r3 = r2->field_f
    //     0xae94b4: ldur            w3, [x2, #0xf]
    // 0xae94b8: DecompressPointer r3
    //     0xae94b8: add             x3, x3, HEAP, lsl #32
    // 0xae94bc: mov             x2, x3
    // 0xae94c0: r0 = text=()
    //     0xae94c0: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae94c4: ldur            x0, [fp, #-0x10]
    // 0xae94c8: LoadField: r1 = r0->field_4b
    //     0xae94c8: ldur            w1, [x0, #0x4b]
    // 0xae94cc: DecompressPointer r1
    //     0xae94cc: add             x1, x1, HEAP, lsl #32
    // 0xae94d0: r2 = Null
    //     0xae94d0: mov             x2, NULL
    // 0xae94d4: r0 = value=()
    //     0xae94d4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae94d8: ldur            x0, [fp, #-0x10]
    // 0xae94dc: LoadField: r1 = r0->field_5b
    //     0xae94dc: ldur            w1, [x0, #0x5b]
    // 0xae94e0: DecompressPointer r1
    //     0xae94e0: add             x1, x1, HEAP, lsl #32
    // 0xae94e4: r2 = ""
    //     0xae94e4: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae94e8: r0 = text=()
    //     0xae94e8: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae94ec: ldur            x0, [fp, #-0x10]
    // 0xae94f0: LoadField: r1 = r0->field_4f
    //     0xae94f0: ldur            w1, [x0, #0x4f]
    // 0xae94f4: DecompressPointer r1
    //     0xae94f4: add             x1, x1, HEAP, lsl #32
    // 0xae94f8: r2 = Null
    //     0xae94f8: mov             x2, NULL
    // 0xae94fc: r0 = value=()
    //     0xae94fc: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae9500: ldur            x0, [fp, #-0x10]
    // 0xae9504: LoadField: r1 = r0->field_5f
    //     0xae9504: ldur            w1, [x0, #0x5f]
    // 0xae9508: DecompressPointer r1
    //     0xae9508: add             x1, x1, HEAP, lsl #32
    // 0xae950c: r2 = ""
    //     0xae950c: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae9510: r0 = text=()
    //     0xae9510: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae9514: r0 = Null
    //     0xae9514: mov             x0, NULL
    // 0xae9518: r0 = ReturnAsyncNotFuture()
    //     0xae9518: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae951c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae951c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9520: b               #0xae9388
  }
  [closure] SelectRegencyWidget <anonymous closure>(dynamic) {
    // ** addr: 0xae9524, size: 0x48
    // 0xae9524: EnterFrame
    //     0xae9524: stp             fp, lr, [SP, #-0x10]!
    //     0xae9528: mov             fp, SP
    // 0xae952c: AllocStack(0x8)
    //     0xae952c: sub             SP, SP, #8
    // 0xae9530: SetupParameters()
    //     0xae9530: ldr             x0, [fp, #0x10]
    //     0xae9534: ldur            w1, [x0, #0x17]
    //     0xae9538: add             x1, x1, HEAP, lsl #32
    // 0xae953c: LoadField: r0 = r1->field_f
    //     0xae953c: ldur            w0, [x1, #0xf]
    // 0xae9540: DecompressPointer r0
    //     0xae9540: add             x0, x0, HEAP, lsl #32
    // 0xae9544: r1 = LoadInt32Instr(r0)
    //     0xae9544: sbfx            x1, x0, #1, #0x1f
    //     0xae9548: tbz             w0, #0, #0xae9550
    //     0xae954c: ldur            x1, [x0, #7]
    // 0xae9550: stur            x1, [fp, #-8]
    // 0xae9554: r0 = SelectRegencyWidget()
    //     0xae9554: bl              #0xae956c  ; AllocateSelectRegencyWidgetStub -> SelectRegencyWidget (size=0x14)
    // 0xae9558: ldur            x1, [fp, #-8]
    // 0xae955c: StoreField: r0->field_b = r1
    //     0xae955c: stur            x1, [x0, #0xb]
    // 0xae9560: LeaveFrame
    //     0xae9560: mov             SP, fp
    //     0xae9564: ldp             fp, lr, [SP], #0x10
    // 0xae9568: ret
    //     0xae9568: ret             
  }
  [closure] Future<void> selectProvince(dynamic) {
    // ** addr: 0xae974c, size: 0x38
    // 0xae974c: EnterFrame
    //     0xae974c: stp             fp, lr, [SP, #-0x10]!
    //     0xae9750: mov             fp, SP
    // 0xae9754: ldr             x0, [fp, #0x10]
    // 0xae9758: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae9758: ldur            w1, [x0, #0x17]
    // 0xae975c: DecompressPointer r1
    //     0xae975c: add             x1, x1, HEAP, lsl #32
    // 0xae9760: CheckStackOverflow
    //     0xae9760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9764: cmp             SP, x16
    //     0xae9768: b.ls            #0xae977c
    // 0xae976c: r0 = selectProvince()
    //     0xae976c: bl              #0xae9784  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectProvince
    // 0xae9770: LeaveFrame
    //     0xae9770: mov             SP, fp
    //     0xae9774: ldp             fp, lr, [SP], #0x10
    // 0xae9778: ret
    //     0xae9778: ret             
    // 0xae977c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae977c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9780: b               #0xae976c
  }
  _ selectProvince(/* No info */) async {
    // ** addr: 0xae9784, size: 0x144
    // 0xae9784: EnterFrame
    //     0xae9784: stp             fp, lr, [SP, #-0x10]!
    //     0xae9788: mov             fp, SP
    // 0xae978c: AllocStack(0x28)
    //     0xae978c: sub             SP, SP, #0x28
    // 0xae9790: SetupParameters(CampaignFormController this /* r1 => r1, fp-0x10 */)
    //     0xae9790: stur            NULL, [fp, #-8]
    //     0xae9794: stur            x1, [fp, #-0x10]
    // 0xae9798: CheckStackOverflow
    //     0xae9798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae979c: cmp             SP, x16
    //     0xae97a0: b.ls            #0xae98c0
    // 0xae97a4: InitAsync() -> Future<void?>
    //     0xae97a4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae97a8: bl              #0x661298  ; InitAsyncStub
    // 0xae97ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae97ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae97b0: ldr             x0, [x0, #0x2670]
    //     0xae97b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae97b8: cmp             w0, w16
    //     0xae97bc: b.ne            #0xae97c8
    //     0xae97c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae97c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae97c8: r1 = Function '<anonymous closure>':.
    //     0xae97c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x400a0] AnonymousClosure: (0xae98c8), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectProvince (0xae9784)
    //     0xae97cc: ldr             x1, [x1, #0xa0]
    // 0xae97d0: r2 = Null
    //     0xae97d0: mov             x2, NULL
    // 0xae97d4: r0 = AllocateClosure()
    //     0xae97d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae97d8: r16 = <Province>
    //     0xae97d8: add             x16, PP, #8, lsl #12  ; [pp+0x80e8] TypeArguments: <Province>
    //     0xae97dc: ldr             x16, [x16, #0xe8]
    // 0xae97e0: stp             x0, x16, [SP]
    // 0xae97e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae97e4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae97e8: r0 = GetNavigation.to()
    //     0xae97e8: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xae97ec: mov             x2, x0
    // 0xae97f0: r1 = <Province?>
    //     0xae97f0: add             x1, PP, #0x40, lsl #12  ; [pp+0x400a8] TypeArguments: <Province?>
    //     0xae97f4: ldr             x1, [x1, #0xa8]
    // 0xae97f8: stur            x2, [fp, #-0x18]
    // 0xae97fc: r0 = AwaitWithTypeCheck()
    //     0xae97fc: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xae9800: stur            x0, [fp, #-0x18]
    // 0xae9804: cmp             w0, NULL
    // 0xae9808: b.eq            #0xae98b8
    // 0xae980c: ldur            x3, [fp, #-0x10]
    // 0xae9810: LoadField: r1 = r3->field_43
    //     0xae9810: ldur            w1, [x3, #0x43]
    // 0xae9814: DecompressPointer r1
    //     0xae9814: add             x1, x1, HEAP, lsl #32
    // 0xae9818: mov             x2, x0
    // 0xae981c: r0 = value=()
    //     0xae981c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae9820: ldur            x0, [fp, #-0x10]
    // 0xae9824: LoadField: r1 = r0->field_53
    //     0xae9824: ldur            w1, [x0, #0x53]
    // 0xae9828: DecompressPointer r1
    //     0xae9828: add             x1, x1, HEAP, lsl #32
    // 0xae982c: ldur            x2, [fp, #-0x18]
    // 0xae9830: LoadField: r3 = r2->field_1b
    //     0xae9830: ldur            w3, [x2, #0x1b]
    // 0xae9834: DecompressPointer r3
    //     0xae9834: add             x3, x3, HEAP, lsl #32
    // 0xae9838: mov             x2, x3
    // 0xae983c: r0 = text=()
    //     0xae983c: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae9840: ldur            x0, [fp, #-0x10]
    // 0xae9844: LoadField: r1 = r0->field_47
    //     0xae9844: ldur            w1, [x0, #0x47]
    // 0xae9848: DecompressPointer r1
    //     0xae9848: add             x1, x1, HEAP, lsl #32
    // 0xae984c: r2 = Null
    //     0xae984c: mov             x2, NULL
    // 0xae9850: r0 = value=()
    //     0xae9850: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae9854: ldur            x0, [fp, #-0x10]
    // 0xae9858: LoadField: r1 = r0->field_57
    //     0xae9858: ldur            w1, [x0, #0x57]
    // 0xae985c: DecompressPointer r1
    //     0xae985c: add             x1, x1, HEAP, lsl #32
    // 0xae9860: r2 = ""
    //     0xae9860: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae9864: r0 = text=()
    //     0xae9864: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae9868: ldur            x0, [fp, #-0x10]
    // 0xae986c: LoadField: r1 = r0->field_4b
    //     0xae986c: ldur            w1, [x0, #0x4b]
    // 0xae9870: DecompressPointer r1
    //     0xae9870: add             x1, x1, HEAP, lsl #32
    // 0xae9874: r2 = Null
    //     0xae9874: mov             x2, NULL
    // 0xae9878: r0 = value=()
    //     0xae9878: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae987c: ldur            x0, [fp, #-0x10]
    // 0xae9880: LoadField: r1 = r0->field_5b
    //     0xae9880: ldur            w1, [x0, #0x5b]
    // 0xae9884: DecompressPointer r1
    //     0xae9884: add             x1, x1, HEAP, lsl #32
    // 0xae9888: r2 = ""
    //     0xae9888: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae988c: r0 = text=()
    //     0xae988c: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae9890: ldur            x0, [fp, #-0x10]
    // 0xae9894: LoadField: r1 = r0->field_4f
    //     0xae9894: ldur            w1, [x0, #0x4f]
    // 0xae9898: DecompressPointer r1
    //     0xae9898: add             x1, x1, HEAP, lsl #32
    // 0xae989c: r2 = Null
    //     0xae989c: mov             x2, NULL
    // 0xae98a0: r0 = value=()
    //     0xae98a0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae98a4: ldur            x0, [fp, #-0x10]
    // 0xae98a8: LoadField: r1 = r0->field_5f
    //     0xae98a8: ldur            w1, [x0, #0x5f]
    // 0xae98ac: DecompressPointer r1
    //     0xae98ac: add             x1, x1, HEAP, lsl #32
    // 0xae98b0: r2 = ""
    //     0xae98b0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae98b4: r0 = text=()
    //     0xae98b4: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae98b8: r0 = Null
    //     0xae98b8: mov             x0, NULL
    // 0xae98bc: r0 = ReturnAsyncNotFuture()
    //     0xae98bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae98c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae98c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae98c4: b               #0xae97a4
  }
  [closure] SelectProvinceWidget <anonymous closure>(dynamic) {
    // ** addr: 0xae98c8, size: 0xc
    // 0xae98c8: r0 = Instance_SelectProvinceWidget
    //     0xae98c8: add             x0, PP, #0x40, lsl #12  ; [pp+0x400b0] Obj!SelectProvinceWidget@e1f971
    //     0xae98cc: ldr             x0, [x0, #0xb0]
    // 0xae98d0: ret
    //     0xae98d0: ret             
  }
  get _ trx(/* No info */) {
    // ** addr: 0xe350b0, size: 0x1a0
    // 0xe350b0: EnterFrame
    //     0xe350b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe350b4: mov             fp, SP
    // 0xe350b8: AllocStack(0x68)
    //     0xe350b8: sub             SP, SP, #0x68
    // 0xe350bc: SetupParameters(CampaignFormController this /* r1 => r0, fp-0x8 */)
    //     0xe350bc: mov             x0, x1
    //     0xe350c0: stur            x1, [fp, #-8]
    // 0xe350c4: CheckStackOverflow
    //     0xe350c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe350c8: cmp             SP, x16
    //     0xe350cc: b.ls            #0xe35248
    // 0xe350d0: mov             x1, x0
    // 0xe350d4: r0 = trx()
    //     0xe350d4: bl              #0xe35bf0  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::trx
    // 0xe350d8: mov             x2, x0
    // 0xe350dc: ldur            x0, [fp, #-8]
    // 0xe350e0: stur            x2, [fp, #-0x20]
    // 0xe350e4: LoadField: r3 = r0->field_3f
    //     0xe350e4: ldur            w3, [x0, #0x3f]
    // 0xe350e8: DecompressPointer r3
    //     0xe350e8: add             x3, x3, HEAP, lsl #32
    // 0xe350ec: stur            x3, [fp, #-0x18]
    // 0xe350f0: cmp             w3, NULL
    // 0xe350f4: b.ne            #0xe35100
    // 0xe350f8: r4 = Null
    //     0xe350f8: mov             x4, NULL
    // 0xe350fc: b               #0xe3510c
    // 0xe35100: LoadField: r1 = r3->field_f
    //     0xe35100: ldur            w1, [x3, #0xf]
    // 0xe35104: DecompressPointer r1
    //     0xe35104: add             x1, x1, HEAP, lsl #32
    // 0xe35108: mov             x4, x1
    // 0xe3510c: stur            x4, [fp, #-0x10]
    // 0xe35110: LoadField: r1 = r0->field_43
    //     0xe35110: ldur            w1, [x0, #0x43]
    // 0xe35114: DecompressPointer r1
    //     0xe35114: add             x1, x1, HEAP, lsl #32
    // 0xe35118: r0 = value()
    //     0xe35118: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3511c: cmp             w0, NULL
    // 0xe35120: b.ne            #0xe3512c
    // 0xe35124: r2 = Null
    //     0xe35124: mov             x2, NULL
    // 0xe35128: b               #0xe35148
    // 0xe3512c: LoadField: r2 = r0->field_13
    //     0xe3512c: ldur            x2, [x0, #0x13]
    // 0xe35130: r0 = BoxInt64Instr(r2)
    //     0xe35130: sbfiz           x0, x2, #1, #0x1f
    //     0xe35134: cmp             x2, x0, asr #1
    //     0xe35138: b.eq            #0xe35144
    //     0xe3513c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe35140: stur            x2, [x0, #7]
    // 0xe35144: mov             x2, x0
    // 0xe35148: ldur            x0, [fp, #-8]
    // 0xe3514c: stur            x2, [fp, #-0x28]
    // 0xe35150: LoadField: r1 = r0->field_47
    //     0xe35150: ldur            w1, [x0, #0x47]
    // 0xe35154: DecompressPointer r1
    //     0xe35154: add             x1, x1, HEAP, lsl #32
    // 0xe35158: r0 = value()
    //     0xe35158: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3515c: cmp             w0, NULL
    // 0xe35160: b.ne            #0xe3516c
    // 0xe35164: r2 = Null
    //     0xe35164: mov             x2, NULL
    // 0xe35168: b               #0xe35188
    // 0xe3516c: LoadField: r2 = r0->field_7
    //     0xe3516c: ldur            x2, [x0, #7]
    // 0xe35170: r0 = BoxInt64Instr(r2)
    //     0xe35170: sbfiz           x0, x2, #1, #0x1f
    //     0xe35174: cmp             x2, x0, asr #1
    //     0xe35178: b.eq            #0xe35184
    //     0xe3517c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe35180: stur            x2, [x0, #7]
    // 0xe35184: mov             x2, x0
    // 0xe35188: ldur            x0, [fp, #-8]
    // 0xe3518c: stur            x2, [fp, #-0x30]
    // 0xe35190: LoadField: r1 = r0->field_4b
    //     0xe35190: ldur            w1, [x0, #0x4b]
    // 0xe35194: DecompressPointer r1
    //     0xe35194: add             x1, x1, HEAP, lsl #32
    // 0xe35198: r0 = value()
    //     0xe35198: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3519c: cmp             w0, NULL
    // 0xe351a0: b.ne            #0xe351ac
    // 0xe351a4: r2 = Null
    //     0xe351a4: mov             x2, NULL
    // 0xe351a8: b               #0xe351c8
    // 0xe351ac: LoadField: r2 = r0->field_7
    //     0xe351ac: ldur            x2, [x0, #7]
    // 0xe351b0: r0 = BoxInt64Instr(r2)
    //     0xe351b0: sbfiz           x0, x2, #1, #0x1f
    //     0xe351b4: cmp             x2, x0, asr #1
    //     0xe351b8: b.eq            #0xe351c4
    //     0xe351bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe351c0: stur            x2, [x0, #7]
    // 0xe351c4: mov             x2, x0
    // 0xe351c8: ldur            x0, [fp, #-8]
    // 0xe351cc: stur            x2, [fp, #-0x38]
    // 0xe351d0: LoadField: r1 = r0->field_4f
    //     0xe351d0: ldur            w1, [x0, #0x4f]
    // 0xe351d4: DecompressPointer r1
    //     0xe351d4: add             x1, x1, HEAP, lsl #32
    // 0xe351d8: r0 = value()
    //     0xe351d8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe351dc: cmp             w0, NULL
    // 0xe351e0: b.ne            #0xe351ec
    // 0xe351e4: r0 = Null
    //     0xe351e4: mov             x0, NULL
    // 0xe351e8: b               #0xe35204
    // 0xe351ec: LoadField: r2 = r0->field_7
    //     0xe351ec: ldur            x2, [x0, #7]
    // 0xe351f0: r0 = BoxInt64Instr(r2)
    //     0xe351f0: sbfiz           x0, x2, #1, #0x1f
    //     0xe351f4: cmp             x2, x0, asr #1
    //     0xe351f8: b.eq            #0xe35204
    //     0xe351fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe35200: stur            x2, [x0, #7]
    // 0xe35204: ldur            x16, [fp, #-0x10]
    // 0xe35208: ldur            lr, [fp, #-0x18]
    // 0xe3520c: stp             lr, x16, [SP, #0x20]
    // 0xe35210: ldur            x16, [fp, #-0x28]
    // 0xe35214: ldur            lr, [fp, #-0x30]
    // 0xe35218: stp             lr, x16, [SP, #0x10]
    // 0xe3521c: ldur            x16, [fp, #-0x38]
    // 0xe35220: stp             x0, x16, [SP]
    // 0xe35224: ldur            x1, [fp, #-0x20]
    // 0xe35228: r2 = Instance_PaymentType
    //     0xe35228: add             x2, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0xe3522c: ldr             x2, [x2, #0x5d8]
    // 0xe35230: r4 = const [0, 0x8, 0x6, 0x2, campaign, 0x3, description, 0x2, districtId, 0x6, localityId, 0x7, provinceId, 0x4, regencyId, 0x5, null]
    //     0xe35230: add             x4, PP, #0x35, lsl #12  ; [pp+0x359f8] List(17) [0, 0x8, 0x6, 0x2, "campaign", 0x3, "description", 0x2, "districtId", 0x6, "localityId", 0x7, "provinceId", 0x4, "regencyId", 0x5, Null]
    //     0xe35234: ldr             x4, [x4, #0x9f8]
    // 0xe35238: r0 = copyWith()
    //     0xe35238: bl              #0xae5680  ; [package:nuonline/app/data/models/transaction.dart] TransactionRequest::copyWith
    // 0xe3523c: LeaveFrame
    //     0xe3523c: mov             SP, fp
    //     0xe35240: ldp             fp, lr, [SP], #0x10
    // 0xe35244: ret
    //     0xe35244: ret             
    // 0xe35248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35248: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3524c: b               #0xe350d0
  }
}
