// lib: , url: package:nuonline/app/modules/donation/controllers/campaign_list_controller.dart

// class id: 1050203, size: 0x8
class :: {
}

// class id: 1938, size: 0x40, field offset: 0x34
class CampaignListController extends PaginatedFetchController<dynamic> {

  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e7ca4, size: 0x48
    // 0x7e7ca4: EnterFrame
    //     0x7e7ca4: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7ca8: mov             fp, SP
    // 0x7e7cac: CheckStackOverflow
    //     0x7e7cac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7cb0: cmp             SP, x16
    //     0x7e7cb4: b.ls            #0x7e7ce4
    // 0x7e7cb8: LoadField: r0 = r1->field_37
    //     0x7e7cb8: ldur            w0, [x1, #0x37]
    // 0x7e7cbc: DecompressPointer r0
    //     0x7e7cbc: add             x0, x0, HEAP, lsl #32
    // 0x7e7cc0: LoadField: r2 = r1->field_33
    //     0x7e7cc0: ldur            w2, [x1, #0x33]
    // 0x7e7cc4: DecompressPointer r2
    //     0x7e7cc4: add             x2, x2, HEAP, lsl #32
    // 0x7e7cc8: LoadField: r1 = r2->field_1f
    //     0x7e7cc8: ldur            x1, [x2, #0x1f]
    // 0x7e7ccc: mov             x2, x1
    // 0x7e7cd0: mov             x1, x0
    // 0x7e7cd4: r0 = findAllCampaign()
    //     0x7e7cd4: bl              #0x7e7d0c  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAllCampaign
    // 0x7e7cd8: LeaveFrame
    //     0x7e7cd8: mov             SP, fp
    //     0x7e7cdc: ldp             fp, lr, [SP], #0x10
    // 0x7e7ce0: ret
    //     0x7e7ce0: ret             
    // 0x7e7ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7ce8: b               #0x7e7cb8
  }
  _ CampaignListController(/* No info */) {
    // ** addr: 0x810750, size: 0x14c
    // 0x810750: EnterFrame
    //     0x810750: stp             fp, lr, [SP, #-0x10]!
    //     0x810754: mov             fp, SP
    // 0x810758: AllocStack(0x8)
    //     0x810758: sub             SP, SP, #8
    // 0x81075c: SetupParameters(CampaignListController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */, {dynamic isSummary = false /* r5 */, dynamic type = Instance_PaymentType /* r0 */})
    //     0x81075c: stur            x1, [fp, #-8]
    //     0x810760: mov             x16, x2
    //     0x810764: mov             x2, x1
    //     0x810768: mov             x1, x16
    //     0x81076c: ldur            w0, [x4, #0x13]
    //     0x810770: ldur            w3, [x4, #0x1f]
    //     0x810774: add             x3, x3, HEAP, lsl #32
    //     0x810778: add             x16, PP, #0x30, lsl #12  ; [pp+0x30458] "isSummary"
    //     0x81077c: ldr             x16, [x16, #0x458]
    //     0x810780: cmp             w3, w16
    //     0x810784: b.ne            #0x8107a8
    //     0x810788: ldur            w3, [x4, #0x23]
    //     0x81078c: add             x3, x3, HEAP, lsl #32
    //     0x810790: sub             w5, w0, w3
    //     0x810794: add             x3, fp, w5, sxtw #2
    //     0x810798: ldr             x3, [x3, #8]
    //     0x81079c: mov             x5, x3
    //     0x8107a0: movz            x3, #0x1
    //     0x8107a4: b               #0x8107b0
    //     0x8107a8: add             x5, NULL, #0x30  ; false
    //     0x8107ac: movz            x3, #0
    //     0x8107b0: lsl             x6, x3, #1
    //     0x8107b4: lsl             w3, w6, #1
    //     0x8107b8: add             w6, w3, #8
    //     0x8107bc: add             x16, x4, w6, sxtw #1
    //     0x8107c0: ldur            w7, [x16, #0xf]
    //     0x8107c4: add             x7, x7, HEAP, lsl #32
    //     0x8107c8: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    //     0x8107cc: cmp             w7, w16
    //     0x8107d0: b.ne            #0x8107f4
    //     0x8107d4: add             w6, w3, #0xa
    //     0x8107d8: add             x16, x4, w6, sxtw #1
    //     0x8107dc: ldur            w3, [x16, #0xf]
    //     0x8107e0: add             x3, x3, HEAP, lsl #32
    //     0x8107e4: sub             w4, w0, w3
    //     0x8107e8: add             x0, fp, w4, sxtw #2
    //     0x8107ec: ldr             x0, [x0, #8]
    //     0x8107f0: b               #0x8107fc
    //     0x8107f4: add             x0, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0x8107f8: ldr             x0, [x0, #0x5d8]
    //     0x8107fc: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x810800: movz            x3, #0x1
    // 0x8107fc: r4 = Sentinel
    // 0x810800: r3 = 1
    // 0x810804: CheckStackOverflow
    //     0x810804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810808: cmp             SP, x16
    //     0x81080c: b.ls            #0x810894
    // 0x810810: StoreField: r2->field_33 = r0
    //     0x810810: stur            w0, [x2, #0x33]
    //     0x810814: ldurb           w16, [x2, #-1]
    //     0x810818: ldurb           w17, [x0, #-1]
    //     0x81081c: and             x16, x17, x16, lsr #2
    //     0x810820: tst             x16, HEAP, lsr #32
    //     0x810824: b.eq            #0x81082c
    //     0x810828: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81082c: mov             x0, x1
    // 0x810830: StoreField: r2->field_37 = r0
    //     0x810830: stur            w0, [x2, #0x37]
    //     0x810834: ldurb           w16, [x2, #-1]
    //     0x810838: ldurb           w17, [x0, #-1]
    //     0x81083c: and             x16, x17, x16, lsr #2
    //     0x810840: tst             x16, HEAP, lsr #32
    //     0x810844: b.eq            #0x81084c
    //     0x810848: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81084c: StoreField: r2->field_3b = r5
    //     0x81084c: stur            w5, [x2, #0x3b]
    // 0x810850: StoreField: r2->field_23 = r3
    //     0x810850: stur            x3, [x2, #0x23]
    // 0x810854: StoreField: r2->field_2b = r4
    //     0x810854: stur            w4, [x2, #0x2b]
    // 0x810858: r1 = ""
    //     0x810858: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x81085c: r0 = StringExtension.obs()
    //     0x81085c: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x810860: ldur            x1, [fp, #-8]
    // 0x810864: StoreField: r1->field_2f = r0
    //     0x810864: stur            w0, [x1, #0x2f]
    //     0x810868: ldurb           w16, [x1, #-1]
    //     0x81086c: ldurb           w17, [x0, #-1]
    //     0x810870: and             x16, x17, x16, lsr #2
    //     0x810874: tst             x16, HEAP, lsr #32
    //     0x810878: b.eq            #0x810880
    //     0x81087c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x810880: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x810880: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x810884: r0 = Null
    //     0x810884: mov             x0, NULL
    // 0x810888: LeaveFrame
    //     0x810888: mov             SP, fp
    //     0x81088c: ldp             fp, lr, [SP], #0x10
    // 0x810890: ret
    //     0x810890: ret             
    // 0x810894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810894: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810898: b               #0x810810
  }
}
