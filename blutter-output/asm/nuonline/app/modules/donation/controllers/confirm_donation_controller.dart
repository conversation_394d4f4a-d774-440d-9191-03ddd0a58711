// lib: , url: package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart

// class id: 1050204, size: 0x8
class :: {
}

// class id: 1903, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _ConfirmDonationController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {
}

// class id: 1904, size: 0x28, field offset: 0x28
//   transformed mixin,
abstract class _ConfirmDonationController&GetxController&StateMixin&OnlineMixin extends _ConfirmDonationController&GetxController&StateMixin
     with OnlineMixin<X0> {

  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8ef618, size: 0x70
    // 0x8ef618: EnterFrame
    //     0x8ef618: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef61c: mov             fp, SP
    // 0x8ef620: AllocStack(0x8)
    //     0x8ef620: sub             SP, SP, #8
    // 0x8ef624: SetupParameters()
    //     0x8ef624: ldr             x0, [fp, #0x18]
    //     0x8ef628: ldur            w1, [x0, #0x17]
    //     0x8ef62c: add             x1, x1, HEAP, lsl #32
    // 0x8ef630: CheckStackOverflow
    //     0x8ef630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ef634: cmp             SP, x16
    //     0x8ef638: b.ls            #0x8ef680
    // 0x8ef63c: LoadField: r0 = r1->field_f
    //     0x8ef63c: ldur            w0, [x1, #0xf]
    // 0x8ef640: DecompressPointer r0
    //     0x8ef640: add             x0, x0, HEAP, lsl #32
    // 0x8ef644: ldr             x1, [fp, #0x10]
    // 0x8ef648: stur            x0, [fp, #-8]
    // 0x8ef64c: r0 = getErrorMessage()
    //     0x8ef64c: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8ef650: ldur            x1, [fp, #-8]
    // 0x8ef654: r2 = LoadClassIdInstr(r1)
    //     0x8ef654: ldur            x2, [x1, #-1]
    //     0x8ef658: ubfx            x2, x2, #0xc, #0x14
    // 0x8ef65c: mov             x16, x0
    // 0x8ef660: mov             x0, x2
    // 0x8ef664: mov             x2, x16
    // 0x8ef668: r0 = GDT[cid_x0 + -0xe19]()
    //     0x8ef668: sub             lr, x0, #0xe19
    //     0x8ef66c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ef670: blr             lr
    // 0x8ef674: LeaveFrame
    //     0x8ef674: mov             SP, fp
    //     0x8ef678: ldp             fp, lr, [SP], #0x10
    // 0x8ef67c: ret
    //     0x8ef67c: ret             
    // 0x8ef680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef684: b               #0x8ef63c
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<Transaction>) {
    // ** addr: 0x8ef688, size: 0x94
    // 0x8ef688: EnterFrame
    //     0x8ef688: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef68c: mov             fp, SP
    // 0x8ef690: AllocStack(0x28)
    //     0x8ef690: sub             SP, SP, #0x28
    // 0x8ef694: SetupParameters()
    //     0x8ef694: ldr             x0, [fp, #0x18]
    //     0x8ef698: ldur            w3, [x0, #0x17]
    //     0x8ef69c: add             x3, x3, HEAP, lsl #32
    //     0x8ef6a0: stur            x3, [fp, #-8]
    // 0x8ef6a4: CheckStackOverflow
    //     0x8ef6a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ef6a8: cmp             SP, x16
    //     0x8ef6ac: b.ls            #0x8ef714
    // 0x8ef6b0: mov             x2, x3
    // 0x8ef6b4: r1 = Function '<anonymous closure>':.
    //     0x8ef6b4: add             x1, PP, #0x30, lsl #12  ; [pp+0x302b0] AnonymousClosure: (0x8ef71c), in [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode (0x8ef784)
    //     0x8ef6b8: ldr             x1, [x1, #0x2b0]
    // 0x8ef6bc: r0 = AllocateClosure()
    //     0x8ef6bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ef6c0: ldur            x2, [fp, #-8]
    // 0x8ef6c4: r1 = Function '<anonymous closure>':.
    //     0x8ef6c4: add             x1, PP, #0x30, lsl #12  ; [pp+0x302b8] AnonymousClosure: (0x8ef618), in [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode (0x8ef784)
    //     0x8ef6c8: ldr             x1, [x1, #0x2b8]
    // 0x8ef6cc: stur            x0, [fp, #-8]
    // 0x8ef6d0: r0 = AllocateClosure()
    //     0x8ef6d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ef6d4: mov             x1, x0
    // 0x8ef6d8: ldr             x0, [fp, #0x10]
    // 0x8ef6dc: r2 = LoadClassIdInstr(r0)
    //     0x8ef6dc: ldur            x2, [x0, #-1]
    //     0x8ef6e0: ubfx            x2, x2, #0xc, #0x14
    // 0x8ef6e4: r16 = <Object?>
    //     0x8ef6e4: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8ef6e8: stp             x0, x16, [SP, #0x10]
    // 0x8ef6ec: ldur            x16, [fp, #-8]
    // 0x8ef6f0: stp             x16, x1, [SP]
    // 0x8ef6f4: mov             x0, x2
    // 0x8ef6f8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8ef6f8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8ef6fc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8ef6fc: sub             lr, x0, #1, lsl #12
    //     0x8ef700: ldr             lr, [x21, lr, lsl #3]
    //     0x8ef704: blr             lr
    // 0x8ef708: LeaveFrame
    //     0x8ef708: mov             SP, fp
    //     0x8ef70c: ldp             fp, lr, [SP], #0x10
    // 0x8ef710: ret
    //     0x8ef710: ret             
    // 0x8ef714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef718: b               #0x8ef6b0
  }
  [closure] Future<void> <anonymous closure>(dynamic, Transaction, Pagination?) {
    // ** addr: 0x8ef71c, size: 0x68
    // 0x8ef71c: EnterFrame
    //     0x8ef71c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef720: mov             fp, SP
    // 0x8ef724: ldr             x0, [fp, #0x20]
    // 0x8ef728: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ef728: ldur            w1, [x0, #0x17]
    // 0x8ef72c: DecompressPointer r1
    //     0x8ef72c: add             x1, x1, HEAP, lsl #32
    // 0x8ef730: CheckStackOverflow
    //     0x8ef730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ef734: cmp             SP, x16
    //     0x8ef738: b.ls            #0x8ef77c
    // 0x8ef73c: LoadField: r0 = r1->field_f
    //     0x8ef73c: ldur            w0, [x1, #0xf]
    // 0x8ef740: DecompressPointer r0
    //     0x8ef740: add             x0, x0, HEAP, lsl #32
    // 0x8ef744: r1 = LoadClassIdInstr(r0)
    //     0x8ef744: ldur            x1, [x0, #-1]
    //     0x8ef748: ubfx            x1, x1, #0xc, #0x14
    // 0x8ef74c: mov             x16, x0
    // 0x8ef750: mov             x0, x1
    // 0x8ef754: mov             x1, x16
    // 0x8ef758: ldr             x2, [fp, #0x18]
    // 0x8ef75c: ldr             x3, [fp, #0x10]
    // 0x8ef760: r0 = GDT[cid_x0 + 0xfc16]()
    //     0x8ef760: movz            x17, #0xfc16
    //     0x8ef764: add             lr, x0, x17
    //     0x8ef768: ldr             lr, [x21, lr, lsl #3]
    //     0x8ef76c: blr             lr
    // 0x8ef770: LeaveFrame
    //     0x8ef770: mov             SP, fp
    //     0x8ef774: ldp             fp, lr, [SP], #0x10
    // 0x8ef778: ret
    //     0x8ef778: ret             
    // 0x8ef77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef77c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef780: b               #0x8ef73c
  }
  _ executeOnlineMode(/* No info */) async {
    // ** addr: 0x8ef784, size: 0x98
    // 0x8ef784: EnterFrame
    //     0x8ef784: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef788: mov             fp, SP
    // 0x8ef78c: AllocStack(0x30)
    //     0x8ef78c: sub             SP, SP, #0x30
    // 0x8ef790: SetupParameters(_ConfirmDonationController&GetxController&StateMixin&OnlineMixin this /* r1 => r1, fp-0x10 */)
    //     0x8ef790: stur            NULL, [fp, #-8]
    //     0x8ef794: stur            x1, [fp, #-0x10]
    // 0x8ef798: CheckStackOverflow
    //     0x8ef798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ef79c: cmp             SP, x16
    //     0x8ef7a0: b.ls            #0x8ef814
    // 0x8ef7a4: r1 = 1
    //     0x8ef7a4: movz            x1, #0x1
    // 0x8ef7a8: r0 = AllocateContext()
    //     0x8ef7a8: bl              #0xec126c  ; AllocateContextStub
    // 0x8ef7ac: mov             x2, x0
    // 0x8ef7b0: ldur            x1, [fp, #-0x10]
    // 0x8ef7b4: stur            x2, [fp, #-0x18]
    // 0x8ef7b8: StoreField: r2->field_f = r1
    //     0x8ef7b8: stur            w1, [x2, #0xf]
    // 0x8ef7bc: InitAsync() -> Future<void?>
    //     0x8ef7bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ef7c0: bl              #0x661298  ; InitAsyncStub
    // 0x8ef7c4: ldur            x1, [fp, #-0x10]
    // 0x8ef7c8: r0 = LoadClassIdInstr(r1)
    //     0x8ef7c8: ldur            x0, [x1, #-1]
    //     0x8ef7cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8ef7d0: r0 = GDT[cid_x0 + 0x1277b]()
    //     0x8ef7d0: movz            x17, #0x277b
    //     0x8ef7d4: movk            x17, #0x1, lsl #16
    //     0x8ef7d8: add             lr, x0, x17
    //     0x8ef7dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8ef7e0: blr             lr
    // 0x8ef7e4: ldur            x2, [fp, #-0x18]
    // 0x8ef7e8: r1 = Function '<anonymous closure>':.
    //     0x8ef7e8: add             x1, PP, #0x30, lsl #12  ; [pp+0x302a8] AnonymousClosure: (0x8ef688), in [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode (0x8ef784)
    //     0x8ef7ec: ldr             x1, [x1, #0x2a8]
    // 0x8ef7f0: stur            x0, [fp, #-0x10]
    // 0x8ef7f4: r0 = AllocateClosure()
    //     0x8ef7f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ef7f8: r16 = <void?>
    //     0x8ef7f8: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8ef7fc: ldur            lr, [fp, #-0x10]
    // 0x8ef800: stp             lr, x16, [SP, #8]
    // 0x8ef804: str             x0, [SP]
    // 0x8ef808: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ef808: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ef80c: r0 = then()
    //     0x8ef80c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8ef810: r0 = ReturnAsync()
    //     0x8ef810: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8ef814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef818: b               #0x8ef7a4
  }
}

// class id: 1906, size: 0x30, field offset: 0x28
class ConfirmDonationController extends _ConfirmDonationController&GetxController&StateMixin&OnlineMixin {

  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0x6fbcc0, size: 0x7c
    // 0x6fbcc0: EnterFrame
    //     0x6fbcc0: stp             fp, lr, [SP, #-0x10]!
    //     0x6fbcc4: mov             fp, SP
    // 0x6fbcc8: AllocStack(0x10)
    //     0x6fbcc8: sub             SP, SP, #0x10
    // 0x6fbccc: SetupParameters(ConfirmDonationController this /* r1 => r1, fp-0x10 */)
    //     0x6fbccc: stur            NULL, [fp, #-8]
    //     0x6fbcd0: stur            x1, [fp, #-0x10]
    // 0x6fbcd4: CheckStackOverflow
    //     0x6fbcd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fbcd8: cmp             SP, x16
    //     0x6fbcdc: b.ls            #0x6fbd34
    // 0x6fbce0: InitAsync() -> Future<ApiResult<Transaction>>
    //     0x6fbce0: add             x0, PP, #0x40, lsl #12  ; [pp+0x404a0] TypeArguments: <ApiResult<Transaction>>
    //     0x6fbce4: ldr             x0, [x0, #0x4a0]
    //     0x6fbce8: bl              #0x661298  ; InitAsyncStub
    // 0x6fbcec: r0 = RxStatus()
    //     0x6fbcec: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x6fbcf0: mov             x1, x0
    // 0x6fbcf4: r0 = false
    //     0x6fbcf4: add             x0, NULL, #0x30  ; false
    // 0x6fbcf8: StoreField: r1->field_f = r0
    //     0x6fbcf8: stur            w0, [x1, #0xf]
    // 0x6fbcfc: r2 = true
    //     0x6fbcfc: add             x2, NULL, #0x20  ; true
    // 0x6fbd00: StoreField: r1->field_7 = r2
    //     0x6fbd00: stur            w2, [x1, #7]
    // 0x6fbd04: StoreField: r1->field_b = r0
    //     0x6fbd04: stur            w0, [x1, #0xb]
    // 0x6fbd08: mov             x3, x1
    // 0x6fbd0c: ldur            x1, [fp, #-0x10]
    // 0x6fbd10: r2 = Null
    //     0x6fbd10: mov             x2, NULL
    // 0x6fbd14: r0 = change()
    //     0x6fbd14: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0x6fbd18: ldur            x0, [fp, #-0x10]
    // 0x6fbd1c: LoadField: r1 = r0->field_27
    //     0x6fbd1c: ldur            w1, [x0, #0x27]
    // 0x6fbd20: DecompressPointer r1
    //     0x6fbd20: add             x1, x1, HEAP, lsl #32
    // 0x6fbd24: LoadField: r2 = r0->field_2b
    //     0x6fbd24: ldur            w2, [x0, #0x2b]
    // 0x6fbd28: DecompressPointer r2
    //     0x6fbd28: add             x2, x2, HEAP, lsl #32
    // 0x6fbd2c: r0 = addTransaction()
    //     0x6fbd2c: bl              #0x6fbdac  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::addTransaction
    // 0x6fbd30: r0 = ReturnAsync()
    //     0x6fbd30: b               #0x6576a4  ; ReturnAsyncStub
    // 0x6fbd34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fbd34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fbd38: b               #0x6fbce0
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0x7f8434, size: 0x100
    // 0x7f8434: EnterFrame
    //     0x7f8434: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8438: mov             fp, SP
    // 0x7f843c: AllocStack(0x38)
    //     0x7f843c: sub             SP, SP, #0x38
    // 0x7f8440: SetupParameters(ConfirmDonationController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7f8440: stur            NULL, [fp, #-8]
    //     0x7f8444: stur            x1, [fp, #-0x10]
    //     0x7f8448: stur            x2, [fp, #-0x18]
    //     0x7f844c: stur            x3, [fp, #-0x20]
    // 0x7f8450: CheckStackOverflow
    //     0x7f8450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8454: cmp             SP, x16
    //     0x7f8458: b.ls            #0x7f852c
    // 0x7f845c: InitAsync() -> Future<void?>
    //     0x7f845c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7f8460: bl              #0x661298  ; InitAsyncStub
    // 0x7f8464: r0 = RxStatus()
    //     0x7f8464: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7f8468: mov             x1, x0
    // 0x7f846c: r0 = false
    //     0x7f846c: add             x0, NULL, #0x30  ; false
    // 0x7f8470: StoreField: r1->field_f = r0
    //     0x7f8470: stur            w0, [x1, #0xf]
    // 0x7f8474: StoreField: r1->field_7 = r0
    //     0x7f8474: stur            w0, [x1, #7]
    // 0x7f8478: StoreField: r1->field_b = r0
    //     0x7f8478: stur            w0, [x1, #0xb]
    // 0x7f847c: mov             x3, x1
    // 0x7f8480: ldur            x1, [fp, #-0x10]
    // 0x7f8484: ldur            x2, [fp, #-0x18]
    // 0x7f8488: r0 = change()
    //     0x7f8488: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0x7f848c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x7f848c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7f8490: ldr             x0, [x0, #0x2670]
    //     0x7f8494: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7f8498: cmp             w0, w16
    //     0x7f849c: b.ne            #0x7f84a8
    //     0x7f84a0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x7f84a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7f84a8: r1 = Null
    //     0x7f84a8: mov             x1, NULL
    // 0x7f84ac: r2 = 4
    //     0x7f84ac: movz            x2, #0x4
    // 0x7f84b0: r0 = AllocateArray()
    //     0x7f84b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7f84b4: mov             x2, x0
    // 0x7f84b8: r16 = "id"
    //     0x7f84b8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7f84bc: ldr             x16, [x16, #0x740]
    // 0x7f84c0: StoreField: r2->field_f = r16
    //     0x7f84c0: stur            w16, [x2, #0xf]
    // 0x7f84c4: ldur            x0, [fp, #-0x18]
    // 0x7f84c8: LoadField: r3 = r0->field_7
    //     0x7f84c8: ldur            x3, [x0, #7]
    // 0x7f84cc: r0 = BoxInt64Instr(r3)
    //     0x7f84cc: sbfiz           x0, x3, #1, #0x1f
    //     0x7f84d0: cmp             x3, x0, asr #1
    //     0x7f84d4: b.eq            #0x7f84e0
    //     0x7f84d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7f84dc: stur            x3, [x0, #7]
    // 0x7f84e0: StoreField: r2->field_13 = r0
    //     0x7f84e0: stur            w0, [x2, #0x13]
    // 0x7f84e4: r16 = <String, int>
    //     0x7f84e4: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0x7f84e8: stp             x2, x16, [SP]
    // 0x7f84ec: r0 = Map._fromLiteral()
    //     0x7f84ec: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7f84f0: r1 = Function '<anonymous closure>':.
    //     0x7f84f0: add             x1, PP, #0x40, lsl #12  ; [pp+0x404f0] AnonymousClosure: (0x7f88e0), in [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] ConfirmDonationController::onOnlineModeLoaded (0x7f8434)
    //     0x7f84f4: ldr             x1, [x1, #0x4f0]
    // 0x7f84f8: r2 = Null
    //     0x7f84f8: mov             x2, NULL
    // 0x7f84fc: stur            x0, [fp, #-0x10]
    // 0x7f8500: r0 = AllocateClosure()
    //     0x7f8500: bl              #0xec1630  ; AllocateClosureStub
    // 0x7f8504: stp             x0, NULL, [SP, #8]
    // 0x7f8508: ldur            x16, [fp, #-0x10]
    // 0x7f850c: str             x16, [SP]
    // 0x7f8510: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7f8510: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7f8514: r0 = GetNavigation.offNamedUntil()
    //     0x7f8514: bl              #0x7f8534  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offNamedUntil
    // 0x7f8518: mov             x1, x0
    // 0x7f851c: stur            x1, [fp, #-0x10]
    // 0x7f8520: r0 = Await()
    //     0x7f8520: bl              #0x661044  ; AwaitStub
    // 0x7f8524: r0 = Null
    //     0x7f8524: mov             x0, NULL
    // 0x7f8528: r0 = ReturnAsyncNotFuture()
    //     0x7f8528: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7f852c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f852c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8530: b               #0x7f845c
  }
  [closure] bool <anonymous closure>(dynamic, Route<dynamic>) {
    // ** addr: 0x7f88e0, size: 0xb0
    // 0x7f88e0: EnterFrame
    //     0x7f88e0: stp             fp, lr, [SP, #-0x10]!
    //     0x7f88e4: mov             fp, SP
    // 0x7f88e8: AllocStack(0x8)
    //     0x7f88e8: sub             SP, SP, #8
    // 0x7f88ec: SetupParameters()
    //     0x7f88ec: movz            x0, #0x4
    // 0x7f88ec: r0 = 4
    // 0x7f88f0: CheckStackOverflow
    //     0x7f88f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f88f4: cmp             SP, x16
    //     0x7f88f8: b.ls            #0x7f8988
    // 0x7f88fc: mov             x2, x0
    // 0x7f8900: r1 = Null
    //     0x7f8900: mov             x1, NULL
    // 0x7f8904: r0 = AllocateArray()
    //     0x7f8904: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7f8908: stur            x0, [fp, #-8]
    // 0x7f890c: r16 = "/donation"
    //     0x7f890c: add             x16, PP, #0xf, lsl #12  ; [pp+0xfc30] "/donation"
    //     0x7f8910: ldr             x16, [x16, #0xc30]
    // 0x7f8914: StoreField: r0->field_f = r16
    //     0x7f8914: stur            w16, [x0, #0xf]
    // 0x7f8918: r16 = "/qurban"
    //     0x7f8918: add             x16, PP, #0x24, lsl #12  ; [pp+0x24820] "/qurban"
    //     0x7f891c: ldr             x16, [x16, #0x820]
    // 0x7f8920: StoreField: r0->field_13 = r16
    //     0x7f8920: stur            w16, [x0, #0x13]
    // 0x7f8924: r1 = <String>
    //     0x7f8924: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7f8928: r0 = AllocateGrowableArray()
    //     0x7f8928: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7f892c: mov             x1, x0
    // 0x7f8930: ldur            x0, [fp, #-8]
    // 0x7f8934: StoreField: r1->field_f = r0
    //     0x7f8934: stur            w0, [x1, #0xf]
    // 0x7f8938: r0 = 4
    //     0x7f8938: movz            x0, #0x4
    // 0x7f893c: StoreField: r1->field_b = r0
    //     0x7f893c: stur            w0, [x1, #0xb]
    // 0x7f8940: ldr             x0, [fp, #0x10]
    // 0x7f8944: LoadField: r2 = r0->field_13
    //     0x7f8944: ldur            w2, [x0, #0x13]
    // 0x7f8948: DecompressPointer r2
    //     0x7f8948: add             x2, x2, HEAP, lsl #32
    // 0x7f894c: r0 = LoadClassIdInstr(r2)
    //     0x7f894c: ldur            x0, [x2, #-1]
    //     0x7f8950: ubfx            x0, x0, #0xc, #0x14
    // 0x7f8954: cmp             x0, #0xa51
    // 0x7f8958: b.ne            #0x7f896c
    // 0x7f895c: LoadField: r0 = r2->field_7
    //     0x7f895c: ldur            w0, [x2, #7]
    // 0x7f8960: DecompressPointer r0
    //     0x7f8960: add             x0, x0, HEAP, lsl #32
    // 0x7f8964: mov             x2, x0
    // 0x7f8968: b               #0x7f8978
    // 0x7f896c: LoadField: r0 = r2->field_6b
    //     0x7f896c: ldur            w0, [x2, #0x6b]
    // 0x7f8970: DecompressPointer r0
    //     0x7f8970: add             x0, x0, HEAP, lsl #32
    // 0x7f8974: mov             x2, x0
    // 0x7f8978: r0 = contains()
    //     0x7f8978: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x7f897c: LeaveFrame
    //     0x7f897c: mov             SP, fp
    //     0x7f8980: ldp             fp, lr, [SP], #0x10
    // 0x7f8984: ret
    //     0x7f8984: ret             
    // 0x7f8988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f898c: b               #0x7f88fc
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f8180, size: 0x68
    // 0x8f8180: EnterFrame
    //     0x8f8180: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8184: mov             fp, SP
    // 0x8f8188: AllocStack(0x8)
    //     0x8f8188: sub             SP, SP, #8
    // 0x8f818c: SetupParameters(ConfirmDonationController this /* r1 => r1, fp-0x8 */)
    //     0x8f818c: stur            x1, [fp, #-8]
    // 0x8f8190: CheckStackOverflow
    //     0x8f8190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8194: cmp             SP, x16
    //     0x8f8198: b.ls            #0x8f81e0
    // 0x8f819c: r0 = RxStatus()
    //     0x8f819c: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8f81a0: mov             x1, x0
    // 0x8f81a4: r0 = true
    //     0x8f81a4: add             x0, NULL, #0x20  ; true
    // 0x8f81a8: StoreField: r1->field_f = r0
    //     0x8f81a8: stur            w0, [x1, #0xf]
    // 0x8f81ac: r0 = false
    //     0x8f81ac: add             x0, NULL, #0x30  ; false
    // 0x8f81b0: StoreField: r1->field_7 = r0
    //     0x8f81b0: stur            w0, [x1, #7]
    // 0x8f81b4: StoreField: r1->field_b = r0
    //     0x8f81b4: stur            w0, [x1, #0xb]
    // 0x8f81b8: mov             x3, x1
    // 0x8f81bc: ldur            x1, [fp, #-8]
    // 0x8f81c0: r2 = Null
    //     0x8f81c0: mov             x2, NULL
    // 0x8f81c4: r0 = change()
    //     0x8f81c4: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0x8f81c8: ldur            x1, [fp, #-8]
    // 0x8f81cc: r0 = onInit()
    //     0x8f81cc: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f81d0: r0 = Null
    //     0x8f81d0: mov             x0, NULL
    // 0x8f81d4: LeaveFrame
    //     0x8f81d4: mov             SP, fp
    //     0x8f81d8: ldp             fp, lr, [SP], #0x10
    // 0x8f81dc: ret
    //     0x8f81dc: ret             
    // 0x8f81e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f81e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f81e4: b               #0x8f819c
  }
  get _ merchantTitle(/* No info */) {
    // ** addr: 0xae195c, size: 0x124
    // 0xae195c: EnterFrame
    //     0xae195c: stp             fp, lr, [SP, #-0x10]!
    //     0xae1960: mov             fp, SP
    // 0xae1964: AllocStack(0x10)
    //     0xae1964: sub             SP, SP, #0x10
    // 0xae1968: CheckStackOverflow
    //     0xae1968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae196c: cmp             SP, x16
    //     0xae1970: b.ls            #0xae1a78
    // 0xae1974: LoadField: r0 = r1->field_2b
    //     0xae1974: ldur            w0, [x1, #0x2b]
    // 0xae1978: DecompressPointer r0
    //     0xae1978: add             x0, x0, HEAP, lsl #32
    // 0xae197c: LoadField: r1 = r0->field_f
    //     0xae197c: ldur            w1, [x0, #0xf]
    // 0xae1980: DecompressPointer r1
    //     0xae1980: add             x1, x1, HEAP, lsl #32
    // 0xae1984: LoadField: r0 = r1->field_7
    //     0xae1984: ldur            x0, [x1, #7]
    // 0xae1988: cmp             x0, #2
    // 0xae198c: b.gt            #0xae1a3c
    // 0xae1990: cmp             x0, #1
    // 0xae1994: b.gt            #0xae1a30
    // 0xae1998: cmp             x0, #0
    // 0xae199c: b.gt            #0xae19e8
    // 0xae19a0: LoadField: r0 = r1->field_13
    //     0xae19a0: ldur            w0, [x1, #0x13]
    // 0xae19a4: DecompressPointer r0
    //     0xae19a4: add             x0, x0, HEAP, lsl #32
    // 0xae19a8: stur            x0, [fp, #-8]
    // 0xae19ac: r1 = Null
    //     0xae19ac: mov             x1, NULL
    // 0xae19b0: r2 = 6
    //     0xae19b0: movz            x2, #0x6
    // 0xae19b4: r0 = AllocateArray()
    //     0xae19b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae19b8: mov             x1, x0
    // 0xae19bc: ldur            x0, [fp, #-8]
    // 0xae19c0: StoreField: r1->field_f = r0
    //     0xae19c0: stur            w0, [x1, #0xf]
    // 0xae19c4: r16 = " ke "
    //     0xae19c4: add             x16, PP, #0x30, lsl #12  ; [pp+0x308a0] " ke "
    //     0xae19c8: ldr             x16, [x16, #0x8a0]
    // 0xae19cc: StoreField: r1->field_13 = r16
    //     0xae19cc: stur            w16, [x1, #0x13]
    // 0xae19d0: r16 = "PBNU"
    //     0xae19d0: add             x16, PP, #0x30, lsl #12  ; [pp+0x308a8] "PBNU"
    //     0xae19d4: ldr             x16, [x16, #0x8a8]
    // 0xae19d8: ArrayStore: r1[0] = r16  ; List_4
    //     0xae19d8: stur            w16, [x1, #0x17]
    // 0xae19dc: str             x1, [SP]
    // 0xae19e0: r0 = _interpolate()
    //     0xae19e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae19e4: b               #0xae1a6c
    // 0xae19e8: LoadField: r0 = r1->field_13
    //     0xae19e8: ldur            w0, [x1, #0x13]
    // 0xae19ec: DecompressPointer r0
    //     0xae19ec: add             x0, x0, HEAP, lsl #32
    // 0xae19f0: stur            x0, [fp, #-8]
    // 0xae19f4: r1 = Null
    //     0xae19f4: mov             x1, NULL
    // 0xae19f8: r2 = 6
    //     0xae19f8: movz            x2, #0x6
    // 0xae19fc: r0 = AllocateArray()
    //     0xae19fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1a00: mov             x1, x0
    // 0xae1a04: ldur            x0, [fp, #-8]
    // 0xae1a08: StoreField: r1->field_f = r0
    //     0xae1a08: stur            w0, [x1, #0xf]
    // 0xae1a0c: r16 = " ke "
    //     0xae1a0c: add             x16, PP, #0x30, lsl #12  ; [pp+0x308a0] " ke "
    //     0xae1a10: ldr             x16, [x16, #0x8a0]
    // 0xae1a14: StoreField: r1->field_13 = r16
    //     0xae1a14: stur            w16, [x1, #0x13]
    // 0xae1a18: r16 = "NU Care-LAZISNU"
    //     0xae1a18: add             x16, PP, #0x30, lsl #12  ; [pp+0x308b0] "NU Care-LAZISNU"
    //     0xae1a1c: ldr             x16, [x16, #0x8b0]
    // 0xae1a20: ArrayStore: r1[0] = r16  ; List_4
    //     0xae1a20: stur            w16, [x1, #0x17]
    // 0xae1a24: str             x1, [SP]
    // 0xae1a28: r0 = _interpolate()
    //     0xae1a28: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae1a2c: b               #0xae1a6c
    // 0xae1a30: r0 = "Galang Dana"
    //     0xae1a30: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xae1a34: ldr             x0, [x0, #0x348]
    // 0xae1a38: b               #0xae1a6c
    // 0xae1a3c: cmp             x0, #4
    // 0xae1a40: b.gt            #0xae1a64
    // 0xae1a44: cmp             x0, #3
    // 0xae1a48: b.gt            #0xae1a58
    // 0xae1a4c: r0 = "Qurban"
    //     0xae1a4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5e8] "Qurban"
    //     0xae1a50: ldr             x0, [x0, #0x5e8]
    // 0xae1a54: b               #0xae1a6c
    // 0xae1a58: r0 = "Koin NU"
    //     0xae1a58: add             x0, PP, #0x30, lsl #12  ; [pp+0x30330] "Koin NU"
    //     0xae1a5c: ldr             x0, [x0, #0x330]
    // 0xae1a60: b               #0xae1a6c
    // 0xae1a64: r0 = "Fidyah"
    //     0xae1a64: add             x0, PP, #0x30, lsl #12  ; [pp+0x308b8] "Fidyah"
    //     0xae1a68: ldr             x0, [x0, #0x8b8]
    // 0xae1a6c: LeaveFrame
    //     0xae1a6c: mov             SP, fp
    //     0xae1a70: ldp             fp, lr, [SP], #0x10
    // 0xae1a74: ret
    //     0xae1a74: ret             
    // 0xae1a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1a7c: b               #0xae1974
  }
  get _ title(/* No info */) {
    // ** addr: 0xae1a80, size: 0x74
    // 0xae1a80: EnterFrame
    //     0xae1a80: stp             fp, lr, [SP, #-0x10]!
    //     0xae1a84: mov             fp, SP
    // 0xae1a88: AllocStack(0x10)
    //     0xae1a88: sub             SP, SP, #0x10
    // 0xae1a8c: SetupParameters(ConfirmDonationController this /* r1 => r0, fp-0x8 */)
    //     0xae1a8c: mov             x0, x1
    //     0xae1a90: stur            x1, [fp, #-8]
    // 0xae1a94: CheckStackOverflow
    //     0xae1a94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1a98: cmp             SP, x16
    //     0xae1a9c: b.ls            #0xae1aec
    // 0xae1aa0: r1 = Null
    //     0xae1aa0: mov             x1, NULL
    // 0xae1aa4: r2 = 4
    //     0xae1aa4: movz            x2, #0x4
    // 0xae1aa8: r0 = AllocateArray()
    //     0xae1aa8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1aac: r16 = "Konfirmasi Bayar "
    //     0xae1aac: add             x16, PP, #0x30, lsl #12  ; [pp+0x308c0] "Konfirmasi Bayar "
    //     0xae1ab0: ldr             x16, [x16, #0x8c0]
    // 0xae1ab4: StoreField: r0->field_f = r16
    //     0xae1ab4: stur            w16, [x0, #0xf]
    // 0xae1ab8: ldur            x1, [fp, #-8]
    // 0xae1abc: LoadField: r2 = r1->field_2b
    //     0xae1abc: ldur            w2, [x1, #0x2b]
    // 0xae1ac0: DecompressPointer r2
    //     0xae1ac0: add             x2, x2, HEAP, lsl #32
    // 0xae1ac4: LoadField: r1 = r2->field_f
    //     0xae1ac4: ldur            w1, [x2, #0xf]
    // 0xae1ac8: DecompressPointer r1
    //     0xae1ac8: add             x1, x1, HEAP, lsl #32
    // 0xae1acc: LoadField: r2 = r1->field_13
    //     0xae1acc: ldur            w2, [x1, #0x13]
    // 0xae1ad0: DecompressPointer r2
    //     0xae1ad0: add             x2, x2, HEAP, lsl #32
    // 0xae1ad4: StoreField: r0->field_13 = r2
    //     0xae1ad4: stur            w2, [x0, #0x13]
    // 0xae1ad8: str             x0, [SP]
    // 0xae1adc: r0 = _interpolate()
    //     0xae1adc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae1ae0: LeaveFrame
    //     0xae1ae0: mov             SP, fp
    //     0xae1ae4: ldp             fp, lr, [SP], #0x10
    // 0xae1ae8: ret
    //     0xae1ae8: ret             
    // 0xae1aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1af0: b               #0xae1aa0
  }
  [closure] void pay(dynamic) {
    // ** addr: 0xae1af4, size: 0x38
    // 0xae1af4: EnterFrame
    //     0xae1af4: stp             fp, lr, [SP, #-0x10]!
    //     0xae1af8: mov             fp, SP
    // 0xae1afc: ldr             x0, [fp, #0x10]
    // 0xae1b00: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae1b00: ldur            w1, [x0, #0x17]
    // 0xae1b04: DecompressPointer r1
    //     0xae1b04: add             x1, x1, HEAP, lsl #32
    // 0xae1b08: CheckStackOverflow
    //     0xae1b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1b0c: cmp             SP, x16
    //     0xae1b10: b.ls            #0xae1b24
    // 0xae1b14: r0 = pay()
    //     0xae1b14: bl              #0xae1b2c  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] ConfirmDonationController::pay
    // 0xae1b18: LeaveFrame
    //     0xae1b18: mov             SP, fp
    //     0xae1b1c: ldp             fp, lr, [SP], #0x10
    // 0xae1b20: ret
    //     0xae1b20: ret             
    // 0xae1b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1b24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1b28: b               #0xae1b14
  }
  _ pay(/* No info */) {
    // ** addr: 0xae1b2c, size: 0x54
    // 0xae1b2c: EnterFrame
    //     0xae1b2c: stp             fp, lr, [SP, #-0x10]!
    //     0xae1b30: mov             fp, SP
    // 0xae1b34: AllocStack(0x8)
    //     0xae1b34: sub             SP, SP, #8
    // 0xae1b38: SetupParameters(ConfirmDonationController this /* r1 => r0, fp-0x8 */)
    //     0xae1b38: mov             x0, x1
    //     0xae1b3c: stur            x1, [fp, #-8]
    // 0xae1b40: CheckStackOverflow
    //     0xae1b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1b44: cmp             SP, x16
    //     0xae1b48: b.ls            #0xae1b78
    // 0xae1b4c: mov             x1, x0
    // 0xae1b50: r0 = status()
    //     0xae1b50: bl              #0x7f9290  ; [package:nuonline/app/modules/zakat/controllers/zakat_controller.dart] _ZakatController&GetxController&StateMixin::status
    // 0xae1b54: LoadField: r1 = r0->field_f
    //     0xae1b54: ldur            w1, [x0, #0xf]
    // 0xae1b58: DecompressPointer r1
    //     0xae1b58: add             x1, x1, HEAP, lsl #32
    // 0xae1b5c: tbnz            w1, #4, #0xae1b68
    // 0xae1b60: ldur            x1, [fp, #-8]
    // 0xae1b64: r0 = executeOnlineMode()
    //     0xae1b64: bl              #0x8ef784  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode
    // 0xae1b68: r0 = Null
    //     0xae1b68: mov             x0, NULL
    // 0xae1b6c: LeaveFrame
    //     0xae1b6c: mov             SP, fp
    //     0xae1b70: ldp             fp, lr, [SP], #0x10
    // 0xae1b74: ret
    //     0xae1b74: ret             
    // 0xae1b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1b7c: b               #0xae1b4c
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xe34878, size: 0x90
    // 0xe34878: EnterFrame
    //     0xe34878: stp             fp, lr, [SP, #-0x10]!
    //     0xe3487c: mov             fp, SP
    // 0xe34880: AllocStack(0x18)
    //     0xe34880: sub             SP, SP, #0x18
    // 0xe34884: SetupParameters(ConfirmDonationController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xe34884: stur            NULL, [fp, #-8]
    //     0xe34888: stur            x1, [fp, #-0x10]
    //     0xe3488c: mov             x16, x2
    //     0xe34890: mov             x2, x1
    //     0xe34894: mov             x1, x16
    //     0xe34898: stur            x1, [fp, #-0x18]
    // 0xe3489c: CheckStackOverflow
    //     0xe3489c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe348a0: cmp             SP, x16
    //     0xe348a4: b.ls            #0xe34900
    // 0xe348a8: InitAsync() -> Future<void?>
    //     0xe348a8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe348ac: bl              #0x661298  ; InitAsyncStub
    // 0xe348b0: ldur            x1, [fp, #-0x18]
    // 0xe348b4: r2 = Instance_IconData
    //     0xe348b4: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xe348b8: ldr             x2, [x2, #0x590]
    // 0xe348bc: r0 = show()
    //     0xe348bc: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xe348c0: r0 = RxStatus()
    //     0xe348c0: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xe348c4: mov             x1, x0
    // 0xe348c8: r0 = true
    //     0xe348c8: add             x0, NULL, #0x20  ; true
    // 0xe348cc: StoreField: r1->field_f = r0
    //     0xe348cc: stur            w0, [x1, #0xf]
    // 0xe348d0: r0 = false
    //     0xe348d0: add             x0, NULL, #0x30  ; false
    // 0xe348d4: StoreField: r1->field_7 = r0
    //     0xe348d4: stur            w0, [x1, #7]
    // 0xe348d8: StoreField: r1->field_b = r0
    //     0xe348d8: stur            w0, [x1, #0xb]
    // 0xe348dc: mov             x3, x1
    // 0xe348e0: ldur            x1, [fp, #-0x10]
    // 0xe348e4: r2 = Null
    //     0xe348e4: mov             x2, NULL
    // 0xe348e8: r0 = change()
    //     0xe348e8: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0xe348ec: ldur            x1, [fp, #-0x10]
    // 0xe348f0: ldur            x2, [fp, #-0x18]
    // 0xe348f4: r0 = GetDynamicUtils.printError()
    //     0xe348f4: bl              #0x9189ec  ; [package:get/get_utils/src/extensions/dynamic_extensions.dart] ::GetDynamicUtils.printError
    // 0xe348f8: r0 = Null
    //     0xe348f8: mov             x0, NULL
    // 0xe348fc: r0 = ReturnAsyncNotFuture()
    //     0xe348fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe34900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34904: b               #0xe348a8
  }
}
