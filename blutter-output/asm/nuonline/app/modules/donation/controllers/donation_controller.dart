// lib: , url: package:nuonline/app/modules/donation/controllers/donation_controller.dart

// class id: 1050205, size: 0x8
class :: {
}

// class id: 1933, size: 0x30, field offset: 0x2c
class DonationController extends SimpleFetchController<dynamic> {

  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e8e64, size: 0xb8
    // 0x7e8e64: EnterFrame
    //     0x7e8e64: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8e68: mov             fp, SP
    // 0x7e8e6c: AllocStack(0x18)
    //     0x7e8e6c: sub             SP, SP, #0x18
    // 0x7e8e70: CheckStackOverflow
    //     0x7e8e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8e74: cmp             SP, x16
    //     0x7e8e78: b.ls            #0x7e8f14
    // 0x7e8e7c: LoadField: r0 = r1->field_2b
    //     0x7e8e7c: ldur            w0, [x1, #0x2b]
    // 0x7e8e80: DecompressPointer r0
    //     0x7e8e80: add             x0, x0, HEAP, lsl #32
    // 0x7e8e84: stur            x0, [fp, #-8]
    // 0x7e8e88: r1 = Null
    //     0x7e8e88: mov             x1, NULL
    // 0x7e8e8c: r2 = 12
    //     0x7e8e8c: movz            x2, #0xc
    // 0x7e8e90: r0 = AllocateArray()
    //     0x7e8e90: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e8e94: r16 = "page"
    //     0x7e8e94: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7e8e98: ldr             x16, [x16, #0x300]
    // 0x7e8e9c: StoreField: r0->field_f = r16
    //     0x7e8e9c: stur            w16, [x0, #0xf]
    // 0x7e8ea0: r16 = "1"
    //     0x7e8ea0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x7e8ea4: ldr             x16, [x16, #0x718]
    // 0x7e8ea8: StoreField: r0->field_13 = r16
    //     0x7e8ea8: stur            w16, [x0, #0x13]
    // 0x7e8eac: r16 = "limit"
    //     0x7e8eac: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0x7e8eb0: ldr             x16, [x16, #0x50]
    // 0x7e8eb4: ArrayStore: r0[0] = r16  ; List_4
    //     0x7e8eb4: stur            w16, [x0, #0x17]
    // 0x7e8eb8: r16 = "20"
    //     0x7e8eb8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34ff0] "20"
    //     0x7e8ebc: ldr             x16, [x16, #0xff0]
    // 0x7e8ec0: StoreField: r0->field_1b = r16
    //     0x7e8ec0: stur            w16, [x0, #0x1b]
    // 0x7e8ec4: r16 = "status"
    //     0x7e8ec4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b130] "status"
    //     0x7e8ec8: ldr             x16, [x16, #0x130]
    // 0x7e8ecc: StoreField: r0->field_1f = r16
    //     0x7e8ecc: stur            w16, [x0, #0x1f]
    // 0x7e8ed0: r16 = "PENDING"
    //     0x7e8ed0: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0x7e8ed4: ldr             x16, [x16, #0x2c8]
    // 0x7e8ed8: StoreField: r0->field_23 = r16
    //     0x7e8ed8: stur            w16, [x0, #0x23]
    // 0x7e8edc: r16 = <String, String?>
    //     0x7e8edc: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x7e8ee0: ldr             x16, [x16, #0x198]
    // 0x7e8ee4: stp             x0, x16, [SP]
    // 0x7e8ee8: r0 = Map._fromLiteral()
    //     0x7e8ee8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e8eec: r16 = true
    //     0x7e8eec: add             x16, NULL, #0x20  ; true
    // 0x7e8ef0: str             x16, [SP]
    // 0x7e8ef4: ldur            x1, [fp, #-8]
    // 0x7e8ef8: mov             x2, x0
    // 0x7e8efc: r4 = const [0, 0x3, 0x1, 0x2, self, 0x2, null]
    //     0x7e8efc: add             x4, PP, #0x34, lsl #12  ; [pp+0x34ff8] List(7) [0, 0x3, 0x1, 0x2, "self", 0x2, Null]
    //     0x7e8f00: ldr             x4, [x4, #0xff8]
    // 0x7e8f04: r0 = findAll()
    //     0x7e8f04: bl              #0x72b550  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAll
    // 0x7e8f08: LeaveFrame
    //     0x7e8f08: mov             SP, fp
    //     0x7e8f0c: ldp             fp, lr, [SP], #0x10
    // 0x7e8f10: ret
    //     0x7e8f10: ret             
    // 0x7e8f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8f18: b               #0x7e8e7c
  }
}
