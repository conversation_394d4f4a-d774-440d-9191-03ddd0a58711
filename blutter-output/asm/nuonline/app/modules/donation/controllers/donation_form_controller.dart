// lib: , url: package:nuonline/app/modules/donation/controllers/donation_form_controller.dart

// class id: 1050206, size: 0x8
class :: {
}

// class id: 1907, size: 0x40, field offset: 0x20
class DonationFormController extends GetxController {

  late final bool showDonationNuonlineOnly; // offset: 0x3c

  _ onInit(/* No info */) {
    // ** addr: 0x8f7e70, size: 0x98
    // 0x8f7e70: EnterFrame
    //     0x8f7e70: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7e74: mov             fp, SP
    // 0x8f7e78: AllocStack(0x8)
    //     0x8f7e78: sub             SP, SP, #8
    // 0x8f7e7c: SetupParameters(DonationFormController this /* r1 => r0, fp-0x8 */)
    //     0x8f7e7c: mov             x0, x1
    //     0x8f7e80: stur            x1, [fp, #-8]
    // 0x8f7e84: CheckStackOverflow
    //     0x8f7e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7e88: cmp             SP, x16
    //     0x8f7e8c: b.ls            #0x8f7ef4
    // 0x8f7e90: mov             x1, x0
    // 0x8f7e94: r0 = onInit()
    //     0x8f7e94: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f7e98: ldur            x0, [fp, #-8]
    // 0x8f7e9c: LoadField: r1 = r0->field_1f
    //     0x8f7e9c: ldur            w1, [x0, #0x1f]
    // 0x8f7ea0: DecompressPointer r1
    //     0x8f7ea0: add             x1, x1, HEAP, lsl #32
    // 0x8f7ea4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8f7ea4: ldur            w2, [x1, #0x17]
    // 0x8f7ea8: DecompressPointer r2
    //     0x8f7ea8: add             x2, x2, HEAP, lsl #32
    // 0x8f7eac: r16 = Sentinel
    //     0x8f7eac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f7eb0: cmp             w2, w16
    // 0x8f7eb4: b.eq            #0x8f7efc
    // 0x8f7eb8: mov             x1, x2
    // 0x8f7ebc: r2 = "show_donation_nuonline"
    //     0x8f7ebc: add             x2, PP, #0xf, lsl #12  ; [pp+0xf810] "show_donation_nuonline"
    //     0x8f7ec0: ldr             x2, [x2, #0x810]
    // 0x8f7ec4: r0 = getBool()
    //     0x8f7ec4: bl              #0x8f7f98  ; [package:firebase_remote_config/firebase_remote_config.dart] FirebaseRemoteConfig::getBool
    // 0x8f7ec8: tbz             w0, #4, #0x8f7ee4
    // 0x8f7ecc: ldur            x0, [fp, #-8]
    // 0x8f7ed0: LoadField: r1 = r0->field_2f
    //     0x8f7ed0: ldur            w1, [x0, #0x2f]
    // 0x8f7ed4: DecompressPointer r1
    //     0x8f7ed4: add             x1, x1, HEAP, lsl #32
    // 0x8f7ed8: r2 = Instance_ZakatTypes
    //     0x8f7ed8: add             x2, PP, #0x30, lsl #12  ; [pp+0x30528] Obj!ZakatTypes@e307a1
    //     0x8f7edc: ldr             x2, [x2, #0x528]
    // 0x8f7ee0: r0 = value=()
    //     0x8f7ee0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f7ee4: r0 = Null
    //     0x8f7ee4: mov             x0, NULL
    // 0x8f7ee8: LeaveFrame
    //     0x8f7ee8: mov             SP, fp
    //     0x8f7eec: ldp             fp, lr, [SP], #0x10
    // 0x8f7ef0: ret
    //     0x8f7ef0: ret             
    // 0x8f7ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7ef8: b               #0x8f7e90
    // 0x8f7efc: r9 = remoteConfig
    //     0x8f7efc: add             x9, PP, #0xf, lsl #12  ; [pp+0xf860] Field <RemoteConfigService.remoteConfig>: late final (offset: 0x18)
    //     0x8f7f00: ldr             x9, [x9, #0x860]
    // 0x8f7f04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f7f04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  bool showDonationNuonline(DonationFormController) {
    // ** addr: 0x8f7f60, size: 0x38
    // 0x8f7f60: EnterFrame
    //     0x8f7f60: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7f64: mov             fp, SP
    // 0x8f7f68: CheckStackOverflow
    //     0x8f7f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7f6c: cmp             SP, x16
    //     0x8f7f70: b.ls            #0x8f7f90
    // 0x8f7f74: LoadField: r0 = r1->field_1f
    //     0x8f7f74: ldur            w0, [x1, #0x1f]
    // 0x8f7f78: DecompressPointer r0
    //     0x8f7f78: add             x0, x0, HEAP, lsl #32
    // 0x8f7f7c: mov             x1, x0
    // 0x8f7f80: r0 = showDonationNuonline()
    //     0x8f7f80: bl              #0x8f7f08  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::showDonationNuonline
    // 0x8f7f84: LeaveFrame
    //     0x8f7f84: mov             SP, fp
    //     0x8f7f88: ldp             fp, lr, [SP], #0x10
    // 0x8f7f8c: ret
    //     0x8f7f8c: ret             
    // 0x8f7f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7f94: b               #0x8f7f74
  }
  _ DonationFormController(/* No info */) {
    // ** addr: 0x8f8f2c, size: 0x164
    // 0x8f8f2c: EnterFrame
    //     0x8f8f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8f30: mov             fp, SP
    // 0x8f8f34: AllocStack(0x28)
    //     0x8f8f34: sub             SP, SP, #0x28
    // 0x8f8f38: r3 = const [0x2710, 0x61a8, 0x88b8, 0xc350, 0x11170, 0x186a0]
    //     0x8f8f38: add             x3, PP, #0x30, lsl #12  ; [pp+0x30060] List<int>(6)
    //     0x8f8f3c: ldr             x3, [x3, #0x60]
    // 0x8f8f40: r0 = Sentinel
    //     0x8f8f40: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f8f44: mov             x4, x1
    // 0x8f8f48: stur            x1, [fp, #-8]
    // 0x8f8f4c: stur            x2, [fp, #-0x10]
    // 0x8f8f50: CheckStackOverflow
    //     0x8f8f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8f54: cmp             SP, x16
    //     0x8f8f58: b.ls            #0x8f9088
    // 0x8f8f5c: StoreField: r4->field_37 = r3
    //     0x8f8f5c: stur            w3, [x4, #0x37]
    // 0x8f8f60: StoreField: r4->field_3b = r0
    //     0x8f8f60: stur            w0, [x4, #0x3b]
    // 0x8f8f64: r1 = 0
    //     0x8f8f64: movz            x1, #0
    // 0x8f8f68: r0 = IntExtension.obs()
    //     0x8f8f68: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x8f8f6c: ldur            x2, [fp, #-8]
    // 0x8f8f70: StoreField: r2->field_23 = r0
    //     0x8f8f70: stur            w0, [x2, #0x23]
    //     0x8f8f74: ldurb           w16, [x2, #-1]
    //     0x8f8f78: ldurb           w17, [x0, #-1]
    //     0x8f8f7c: and             x16, x17, x16, lsr #2
    //     0x8f8f80: tst             x16, HEAP, lsr #32
    //     0x8f8f84: b.eq            #0x8f8f8c
    //     0x8f8f88: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8f8c: r1 = ""
    //     0x8f8f8c: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f8f90: r0 = StringExtension.obs()
    //     0x8f8f90: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x8f8f94: ldur            x2, [fp, #-8]
    // 0x8f8f98: StoreField: r2->field_27 = r0
    //     0x8f8f98: stur            w0, [x2, #0x27]
    //     0x8f8f9c: ldurb           w16, [x2, #-1]
    //     0x8f8fa0: ldurb           w17, [x0, #-1]
    //     0x8f8fa4: and             x16, x17, x16, lsr #2
    //     0x8f8fa8: tst             x16, HEAP, lsr #32
    //     0x8f8fac: b.eq            #0x8f8fb4
    //     0x8f8fb0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8fb4: r1 = false
    //     0x8f8fb4: add             x1, NULL, #0x30  ; false
    // 0x8f8fb8: r0 = BoolExtension.obs()
    //     0x8f8fb8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x8f8fbc: ldur            x1, [fp, #-8]
    // 0x8f8fc0: StoreField: r1->field_2b = r0
    //     0x8f8fc0: stur            w0, [x1, #0x2b]
    //     0x8f8fc4: ldurb           w16, [x1, #-1]
    //     0x8f8fc8: ldurb           w17, [x0, #-1]
    //     0x8f8fcc: and             x16, x17, x16, lsr #2
    //     0x8f8fd0: tst             x16, HEAP, lsr #32
    //     0x8f8fd4: b.eq            #0x8f8fdc
    //     0x8f8fd8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8fdc: r16 = <ZakatTypes>
    //     0x8f8fdc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0x8f8fe0: ldr             x16, [x16, #0x68]
    // 0x8f8fe4: r30 = Instance_ZakatTypes
    //     0x8f8fe4: add             lr, PP, #0x30, lsl #12  ; [pp+0x30070] Obj!ZakatTypes@e307c1
    //     0x8f8fe8: ldr             lr, [lr, #0x70]
    // 0x8f8fec: stp             lr, x16, [SP]
    // 0x8f8ff0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f8ff0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f8ff4: r0 = RxT.obs()
    //     0x8f8ff4: bl              #0x80eeb8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x8f8ff8: ldur            x2, [fp, #-8]
    // 0x8f8ffc: StoreField: r2->field_2f = r0
    //     0x8f8ffc: stur            w0, [x2, #0x2f]
    //     0x8f9000: ldurb           w16, [x2, #-1]
    //     0x8f9004: ldurb           w17, [x0, #-1]
    //     0x8f9008: and             x16, x17, x16, lsr #2
    //     0x8f900c: tst             x16, HEAP, lsr #32
    //     0x8f9010: b.eq            #0x8f9018
    //     0x8f9014: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f9018: r1 = <TextEditingValue>
    //     0x8f9018: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f901c: r0 = TextEditingController()
    //     0x8f901c: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f9020: mov             x1, x0
    // 0x8f9024: stur            x0, [fp, #-0x18]
    // 0x8f9028: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f9028: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f902c: r0 = TextEditingController()
    //     0x8f902c: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f9030: ldur            x0, [fp, #-0x18]
    // 0x8f9034: ldur            x1, [fp, #-8]
    // 0x8f9038: StoreField: r1->field_33 = r0
    //     0x8f9038: stur            w0, [x1, #0x33]
    //     0x8f903c: ldurb           w16, [x1, #-1]
    //     0x8f9040: ldurb           w17, [x0, #-1]
    //     0x8f9044: and             x16, x17, x16, lsr #2
    //     0x8f9048: tst             x16, HEAP, lsr #32
    //     0x8f904c: b.eq            #0x8f9054
    //     0x8f9050: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f9054: ldur            x0, [fp, #-0x10]
    // 0x8f9058: StoreField: r1->field_1f = r0
    //     0x8f9058: stur            w0, [x1, #0x1f]
    //     0x8f905c: ldurb           w16, [x1, #-1]
    //     0x8f9060: ldurb           w17, [x0, #-1]
    //     0x8f9064: and             x16, x17, x16, lsr #2
    //     0x8f9068: tst             x16, HEAP, lsr #32
    //     0x8f906c: b.eq            #0x8f9074
    //     0x8f9070: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f9074: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8f9074: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8f9078: r0 = Null
    //     0x8f9078: mov             x0, NULL
    // 0x8f907c: LeaveFrame
    //     0x8f907c: mov             SP, fp
    //     0x8f9080: ldp             fp, lr, [SP], #0x10
    // 0x8f9084: ret
    //     0x8f9084: ret             
    // 0x8f9088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f9088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f908c: b               #0x8f8f5c
  }
  [closure] void onSuggestionChanged(dynamic, int) {
    // ** addr: 0xae81e0, size: 0x3c
    // 0xae81e0: EnterFrame
    //     0xae81e0: stp             fp, lr, [SP, #-0x10]!
    //     0xae81e4: mov             fp, SP
    // 0xae81e8: ldr             x0, [fp, #0x18]
    // 0xae81ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae81ec: ldur            w1, [x0, #0x17]
    // 0xae81f0: DecompressPointer r1
    //     0xae81f0: add             x1, x1, HEAP, lsl #32
    // 0xae81f4: CheckStackOverflow
    //     0xae81f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae81f8: cmp             SP, x16
    //     0xae81fc: b.ls            #0xae8214
    // 0xae8200: ldr             x2, [fp, #0x10]
    // 0xae8204: r0 = onSuggestionChanged()
    //     0xae8204: bl              #0xae821c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onSuggestionChanged
    // 0xae8208: LeaveFrame
    //     0xae8208: mov             SP, fp
    //     0xae820c: ldp             fp, lr, [SP], #0x10
    // 0xae8210: ret
    //     0xae8210: ret             
    // 0xae8214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8218: b               #0xae8200
  }
  _ onSuggestionChanged(/* No info */) {
    // ** addr: 0xae821c, size: 0x74
    // 0xae821c: EnterFrame
    //     0xae821c: stp             fp, lr, [SP, #-0x10]!
    //     0xae8220: mov             fp, SP
    // 0xae8224: AllocStack(0x18)
    //     0xae8224: sub             SP, SP, #0x18
    // 0xae8228: SetupParameters(DonationFormController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xae8228: mov             x3, x1
    //     0xae822c: mov             x0, x2
    //     0xae8230: stur            x1, [fp, #-8]
    //     0xae8234: stur            x2, [fp, #-0x10]
    // 0xae8238: CheckStackOverflow
    //     0xae8238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae823c: cmp             SP, x16
    //     0xae8240: b.ls            #0xae8288
    // 0xae8244: LoadField: r1 = r3->field_23
    //     0xae8244: ldur            w1, [x3, #0x23]
    // 0xae8248: DecompressPointer r1
    //     0xae8248: add             x1, x1, HEAP, lsl #32
    // 0xae824c: mov             x2, x0
    // 0xae8250: r0 = value=()
    //     0xae8250: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae8254: ldur            x0, [fp, #-8]
    // 0xae8258: LoadField: r2 = r0->field_33
    //     0xae8258: ldur            w2, [x0, #0x33]
    // 0xae825c: DecompressPointer r2
    //     0xae825c: add             x2, x2, HEAP, lsl #32
    // 0xae8260: ldur            x1, [fp, #-0x10]
    // 0xae8264: stur            x2, [fp, #-0x18]
    // 0xae8268: r0 = IntExtension.currency()
    //     0xae8268: bl              #0xae80dc  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.currency
    // 0xae826c: ldur            x1, [fp, #-0x18]
    // 0xae8270: mov             x2, x0
    // 0xae8274: r0 = text=()
    //     0xae8274: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xae8278: r0 = Null
    //     0xae8278: mov             x0, NULL
    // 0xae827c: LeaveFrame
    //     0xae827c: mov             SP, fp
    //     0xae8280: ldp             fp, lr, [SP], #0x10
    // 0xae8284: ret
    //     0xae8284: ret             
    // 0xae8288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae828c: b               #0xae8244
  }
  bool showDonationNuonlineOnly(DonationFormController) {
    // ** addr: 0xaea9f8, size: 0xa8
    // 0xaea9f8: EnterFrame
    //     0xaea9f8: stp             fp, lr, [SP, #-0x10]!
    //     0xaea9fc: mov             fp, SP
    // 0xaeaa00: AllocStack(0x18)
    //     0xaeaa00: sub             SP, SP, #0x18
    // 0xaeaa04: CheckStackOverflow
    //     0xaeaa04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeaa08: cmp             SP, x16
    //     0xaeaa0c: b.ls            #0xaeaa98
    // 0xaeaa10: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaeaa10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaeaa14: ldr             x0, [x0, #0x2670]
    //     0xaeaa18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaeaa1c: cmp             w0, w16
    //     0xaeaa20: b.ne            #0xaeaa2c
    //     0xaeaa24: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaeaa28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaeaa2c: r0 = GetNavigation.parameters()
    //     0xaeaa2c: bl              #0x65ae4c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.parameters
    // 0xaeaa30: mov             x1, x0
    // 0xaeaa34: r2 = "target"
    //     0xaeaa34: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9a8] "target"
    //     0xaeaa38: ldr             x2, [x2, #0x9a8]
    // 0xaeaa3c: stur            x0, [fp, #-8]
    // 0xaeaa40: r0 = _getValueOrData()
    //     0xaeaa40: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaeaa44: mov             x1, x0
    // 0xaeaa48: ldur            x0, [fp, #-8]
    // 0xaeaa4c: LoadField: r2 = r0->field_f
    //     0xaeaa4c: ldur            w2, [x0, #0xf]
    // 0xaeaa50: DecompressPointer r2
    //     0xaeaa50: add             x2, x2, HEAP, lsl #32
    // 0xaeaa54: cmp             w2, w1
    // 0xaeaa58: b.ne            #0xaeaa64
    // 0xaeaa5c: r0 = Null
    //     0xaeaa5c: mov             x0, NULL
    // 0xaeaa60: b               #0xaeaa68
    // 0xaeaa64: mov             x0, x1
    // 0xaeaa68: r1 = LoadClassIdInstr(r0)
    //     0xaeaa68: ldur            x1, [x0, #-1]
    //     0xaeaa6c: ubfx            x1, x1, #0xc, #0x14
    // 0xaeaa70: r16 = "contribution"
    //     0xaeaa70: add             x16, PP, #0x35, lsl #12  ; [pp+0x35608] "contribution"
    //     0xaeaa74: ldr             x16, [x16, #0x608]
    // 0xaeaa78: stp             x16, x0, [SP]
    // 0xaeaa7c: mov             x0, x1
    // 0xaeaa80: mov             lr, x0
    // 0xaeaa84: ldr             lr, [x21, lr, lsl #3]
    // 0xaeaa88: blr             lr
    // 0xaeaa8c: LeaveFrame
    //     0xaeaa8c: mov             SP, fp
    //     0xaeaa90: ldp             fp, lr, [SP], #0x10
    // 0xaeaa94: ret
    //     0xaeaa94: ret             
    // 0xaeaa98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeaa98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeaa9c: b               #0xaeaa10
  }
  get _ trx(/* No info */) {
    // ** addr: 0xe35bf0, size: 0xdc
    // 0xe35bf0: EnterFrame
    //     0xe35bf0: stp             fp, lr, [SP, #-0x10]!
    //     0xe35bf4: mov             fp, SP
    // 0xe35bf8: AllocStack(0x20)
    //     0xe35bf8: sub             SP, SP, #0x20
    // 0xe35bfc: SetupParameters(DonationFormController this /* r1 => r0, fp-0x8 */)
    //     0xe35bfc: mov             x0, x1
    //     0xe35c00: stur            x1, [fp, #-8]
    // 0xe35c04: CheckStackOverflow
    //     0xe35c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35c08: cmp             SP, x16
    //     0xe35c0c: b.ls            #0xe35cc4
    // 0xe35c10: LoadField: r1 = r0->field_2f
    //     0xe35c10: ldur            w1, [x0, #0x2f]
    // 0xe35c14: DecompressPointer r1
    //     0xe35c14: add             x1, x1, HEAP, lsl #32
    // 0xe35c18: r0 = value()
    //     0xe35c18: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35c1c: mov             x2, x0
    // 0xe35c20: ldur            x0, [fp, #-8]
    // 0xe35c24: stur            x2, [fp, #-0x10]
    // 0xe35c28: LoadField: r1 = r0->field_27
    //     0xe35c28: ldur            w1, [x0, #0x27]
    // 0xe35c2c: DecompressPointer r1
    //     0xe35c2c: add             x1, x1, HEAP, lsl #32
    // 0xe35c30: r0 = value()
    //     0xe35c30: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35c34: mov             x2, x0
    // 0xe35c38: ldur            x0, [fp, #-8]
    // 0xe35c3c: stur            x2, [fp, #-0x18]
    // 0xe35c40: LoadField: r1 = r0->field_2b
    //     0xe35c40: ldur            w1, [x0, #0x2b]
    // 0xe35c44: DecompressPointer r1
    //     0xe35c44: add             x1, x1, HEAP, lsl #32
    // 0xe35c48: r0 = value()
    //     0xe35c48: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35c4c: mov             x2, x0
    // 0xe35c50: ldur            x0, [fp, #-8]
    // 0xe35c54: stur            x2, [fp, #-0x20]
    // 0xe35c58: LoadField: r1 = r0->field_23
    //     0xe35c58: ldur            w1, [x0, #0x23]
    // 0xe35c5c: DecompressPointer r1
    //     0xe35c5c: add             x1, x1, HEAP, lsl #32
    // 0xe35c60: r0 = value()
    //     0xe35c60: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35c64: r1 = <ZakatTypes>
    //     0xe35c64: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xe35c68: ldr             x1, [x1, #0x68]
    // 0xe35c6c: stur            x0, [fp, #-8]
    // 0xe35c70: r0 = TransactionRequest()
    //     0xe35c70: bl              #0x810fc0  ; AllocateTransactionRequestStub -> TransactionRequest<X0> (size=0x4c)
    // 0xe35c74: r1 = Instance_PaymentMethod
    //     0xe35c74: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xe35c78: ldr             x1, [x1, #0x220]
    // 0xe35c7c: StoreField: r0->field_b = r1
    //     0xe35c7c: stur            w1, [x0, #0xb]
    // 0xe35c80: r1 = Instance_PaymentType
    //     0xe35c80: add             x1, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xe35c84: ldr             x1, [x1, #0x5a8]
    // 0xe35c88: StoreField: r0->field_f = r1
    //     0xe35c88: stur            w1, [x0, #0xf]
    // 0xe35c8c: ldur            x1, [fp, #-0x10]
    // 0xe35c90: StoreField: r0->field_13 = r1
    //     0xe35c90: stur            w1, [x0, #0x13]
    // 0xe35c94: ldur            x1, [fp, #-8]
    // 0xe35c98: ArrayStore: r0[0] = r1  ; List_4
    //     0xe35c98: stur            w1, [x0, #0x17]
    // 0xe35c9c: ldur            x1, [fp, #-0x18]
    // 0xe35ca0: StoreField: r0->field_1b = r1
    //     0xe35ca0: stur            w1, [x0, #0x1b]
    // 0xe35ca4: r1 = "Infak & Sedekah"
    //     0xe35ca4: add             x1, PP, #0x30, lsl #12  ; [pp+0x302d8] "Infak & Sedekah"
    //     0xe35ca8: ldr             x1, [x1, #0x2d8]
    // 0xe35cac: StoreField: r0->field_2f = r1
    //     0xe35cac: stur            w1, [x0, #0x2f]
    // 0xe35cb0: ldur            x1, [fp, #-0x20]
    // 0xe35cb4: StoreField: r0->field_33 = r1
    //     0xe35cb4: stur            w1, [x0, #0x33]
    // 0xe35cb8: LeaveFrame
    //     0xe35cb8: mov             SP, fp
    //     0xe35cbc: ldp             fp, lr, [SP], #0x10
    // 0xe35cc0: ret
    //     0xe35cc0: ret             
    // 0xe35cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe35cc8: b               #0xe35c10
  }
  dynamic onSuggestionChanged(dynamic) {
    // ** addr: 0xe3f688, size: 0x24
    // 0xe3f688: EnterFrame
    //     0xe3f688: stp             fp, lr, [SP, #-0x10]!
    //     0xe3f68c: mov             fp, SP
    // 0xe3f690: ldr             x2, [fp, #0x10]
    // 0xe3f694: r1 = Function 'onSuggestionChanged':.
    //     0xe3f694: add             x1, PP, #0x40, lsl #12  ; [pp+0x40498] AnonymousClosure: (0xae81e0), in [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onSuggestionChanged (0xae821c)
    //     0xe3f698: ldr             x1, [x1, #0x498]
    // 0xe3f69c: r0 = AllocateClosure()
    //     0xe3f69c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3f6a0: LeaveFrame
    //     0xe3f6a0: mov             SP, fp
    //     0xe3f6a4: ldp             fp, lr, [SP], #0x10
    // 0xe3f6a8: ret
    //     0xe3f6a8: ret             
  }
}
