// lib: , url: package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart

// class id: 1050207, size: 0x8
class :: {
}

// class id: 1920, size: 0x24, field offset: 0x24
//   transformed mixin,
abstract class _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin extends FetchController<dynamic>
     with GetSingleTickerProviderStateMixin {
}

// class id: 1921, size: 0x3c, field offset: 0x24
//   transformed mixin,
abstract class _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin extends _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin
     with PagingMixin<X0> {

  get _ page(/* No info */) {
    // ** addr: 0x7da3b8, size: 0x48
    // 0x7da3b8: EnterFrame
    //     0x7da3b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7da3bc: mov             fp, SP
    // 0x7da3c0: CheckStackOverflow
    //     0x7da3c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da3c4: cmp             SP, x16
    //     0x7da3c8: b.ls            #0x7da3f8
    // 0x7da3cc: LoadField: r0 = r1->field_23
    //     0x7da3cc: ldur            w0, [x1, #0x23]
    // 0x7da3d0: DecompressPointer r0
    //     0x7da3d0: add             x0, x0, HEAP, lsl #32
    // 0x7da3d4: mov             x1, x0
    // 0x7da3d8: r0 = value()
    //     0x7da3d8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7da3dc: r1 = LoadInt32Instr(r0)
    //     0x7da3dc: sbfx            x1, x0, #1, #0x1f
    //     0x7da3e0: tbz             w0, #0, #0x7da3e8
    //     0x7da3e4: ldur            x1, [x0, #7]
    // 0x7da3e8: mov             x0, x1
    // 0x7da3ec: LeaveFrame
    //     0x7da3ec: mov             SP, fp
    //     0x7da3f0: ldp             fp, lr, [SP], #0x10
    // 0x7da3f4: ret
    //     0x7da3f4: ret             
    // 0x7da3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da3f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da3fc: b               #0x7da3cc
  }
  _ appendToPage(/* No info */) {
    // ** addr: 0x7da4cc, size: 0x130
    // 0x7da4cc: EnterFrame
    //     0x7da4cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7da4d0: mov             fp, SP
    // 0x7da4d4: AllocStack(0x20)
    //     0x7da4d4: sub             SP, SP, #0x20
    // 0x7da4d8: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7da4d8: mov             x4, x1
    //     0x7da4dc: mov             x0, x2
    //     0x7da4e0: stur            x1, [fp, #-8]
    //     0x7da4e4: stur            x2, [fp, #-0x10]
    //     0x7da4e8: stur            x3, [fp, #-0x18]
    // 0x7da4ec: CheckStackOverflow
    //     0x7da4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da4f0: cmp             SP, x16
    //     0x7da4f4: b.ls            #0x7da5f4
    // 0x7da4f8: LoadField: r1 = r4->field_2b
    //     0x7da4f8: ldur            w1, [x4, #0x2b]
    // 0x7da4fc: DecompressPointer r1
    //     0x7da4fc: add             x1, x1, HEAP, lsl #32
    // 0x7da500: r2 = false
    //     0x7da500: add             x2, NULL, #0x30  ; false
    // 0x7da504: r0 = value=()
    //     0x7da504: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7da508: ldur            x0, [fp, #-8]
    // 0x7da50c: LoadField: r1 = r0->field_27
    //     0x7da50c: ldur            w1, [x0, #0x27]
    // 0x7da510: DecompressPointer r1
    //     0x7da510: add             x1, x1, HEAP, lsl #32
    // 0x7da514: ldur            x3, [fp, #-0x18]
    // 0x7da518: cmp             w3, NULL
    // 0x7da51c: b.ne            #0x7da528
    // 0x7da520: r2 = false
    //     0x7da520: add             x2, NULL, #0x30  ; false
    // 0x7da524: b               #0x7da52c
    // 0x7da528: mov             x2, x3
    // 0x7da52c: r0 = value=()
    //     0x7da52c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7da530: ldur            x0, [fp, #-8]
    // 0x7da534: LoadField: r2 = r0->field_23
    //     0x7da534: ldur            w2, [x0, #0x23]
    // 0x7da538: DecompressPointer r2
    //     0x7da538: add             x2, x2, HEAP, lsl #32
    // 0x7da53c: mov             x1, x2
    // 0x7da540: stur            x2, [fp, #-0x20]
    // 0x7da544: r0 = value()
    //     0x7da544: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7da548: cmp             w0, #2
    // 0x7da54c: b.ne            #0x7da588
    // 0x7da550: ldur            x2, [fp, #-0x10]
    // 0x7da554: r0 = LoadClassIdInstr(r2)
    //     0x7da554: ldur            x0, [x2, #-1]
    //     0x7da558: ubfx            x0, x0, #0xc, #0x14
    // 0x7da55c: mov             x1, x2
    // 0x7da560: r0 = GDT[cid_x0 + 0xe879]()
    //     0x7da560: movz            x17, #0xe879
    //     0x7da564: add             lr, x0, x17
    //     0x7da568: ldr             lr, [x21, lr, lsl #3]
    //     0x7da56c: blr             lr
    // 0x7da570: tbnz            w0, #4, #0x7da588
    // 0x7da574: ldur            x0, [fp, #-8]
    // 0x7da578: LoadField: r1 = r0->field_37
    //     0x7da578: ldur            w1, [x0, #0x37]
    // 0x7da57c: DecompressPointer r1
    //     0x7da57c: add             x1, x1, HEAP, lsl #32
    // 0x7da580: r2 = true
    //     0x7da580: add             x2, NULL, #0x20  ; true
    // 0x7da584: r0 = value=()
    //     0x7da584: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7da588: ldur            x0, [fp, #-0x18]
    // 0x7da58c: cmp             w0, NULL
    // 0x7da590: b.eq            #0x7da5d0
    // 0x7da594: tbnz            w0, #4, #0x7da5d0
    // 0x7da598: ldur            x1, [fp, #-0x20]
    // 0x7da59c: r0 = value()
    //     0x7da59c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7da5a0: r1 = LoadInt32Instr(r0)
    //     0x7da5a0: sbfx            x1, x0, #1, #0x1f
    //     0x7da5a4: tbz             w0, #0, #0x7da5ac
    //     0x7da5a8: ldur            x1, [x0, #7]
    // 0x7da5ac: add             x2, x1, #1
    // 0x7da5b0: r0 = BoxInt64Instr(r2)
    //     0x7da5b0: sbfiz           x0, x2, #1, #0x1f
    //     0x7da5b4: cmp             x2, x0, asr #1
    //     0x7da5b8: b.eq            #0x7da5c4
    //     0x7da5bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7da5c0: stur            x2, [x0, #7]
    // 0x7da5c4: ldur            x1, [fp, #-0x20]
    // 0x7da5c8: mov             x2, x0
    // 0x7da5cc: r0 = value=()
    //     0x7da5cc: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7da5d0: ldur            x0, [fp, #-8]
    // 0x7da5d4: LoadField: r1 = r0->field_33
    //     0x7da5d4: ldur            w1, [x0, #0x33]
    // 0x7da5d8: DecompressPointer r1
    //     0x7da5d8: add             x1, x1, HEAP, lsl #32
    // 0x7da5dc: ldur            x2, [fp, #-0x10]
    // 0x7da5e0: r0 = addAll()
    //     0x7da5e0: bl              #0x667efc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::addAll
    // 0x7da5e4: r0 = Null
    //     0x7da5e4: mov             x0, NULL
    // 0x7da5e8: LeaveFrame
    //     0x7da5e8: mov             SP, fp
    //     0x7da5ec: ldp             fp, lr, [SP], #0x10
    // 0x7da5f0: ret
    //     0x7da5f0: ret             
    // 0x7da5f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da5f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da5f8: b               #0x7da4f8
  }
  set _ hasError=(/* No info */) {
    // ** addr: 0x7da75c, size: 0x40
    // 0x7da75c: EnterFrame
    //     0x7da75c: stp             fp, lr, [SP, #-0x10]!
    //     0x7da760: mov             fp, SP
    // 0x7da764: CheckStackOverflow
    //     0x7da764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da768: cmp             SP, x16
    //     0x7da76c: b.ls            #0x7da794
    // 0x7da770: LoadField: r0 = r1->field_2f
    //     0x7da770: ldur            w0, [x1, #0x2f]
    // 0x7da774: DecompressPointer r0
    //     0x7da774: add             x0, x0, HEAP, lsl #32
    // 0x7da778: mov             x1, x0
    // 0x7da77c: r2 = true
    //     0x7da77c: add             x2, NULL, #0x20  ; true
    // 0x7da780: r0 = value=()
    //     0x7da780: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7da784: r0 = true
    //     0x7da784: add             x0, NULL, #0x20  ; true
    // 0x7da788: LeaveFrame
    //     0x7da788: mov             SP, fp
    //     0x7da78c: ldp             fp, lr, [SP], #0x10
    // 0x7da790: ret
    //     0x7da790: ret             
    // 0x7da794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da798: b               #0x7da770
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91ec64, size: 0x58
    // 0x91ec64: EnterFrame
    //     0x91ec64: stp             fp, lr, [SP, #-0x10]!
    //     0x91ec68: mov             fp, SP
    // 0x91ec6c: AllocStack(0x8)
    //     0x91ec6c: sub             SP, SP, #8
    // 0x91ec70: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x91ec70: mov             x0, x1
    //     0x91ec74: stur            x1, [fp, #-8]
    // 0x91ec78: CheckStackOverflow
    //     0x91ec78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ec7c: cmp             SP, x16
    //     0x91ec80: b.ls            #0x91ecb4
    // 0x91ec84: LoadField: r1 = r0->field_23
    //     0x91ec84: ldur            w1, [x0, #0x23]
    // 0x91ec88: DecompressPointer r1
    //     0x91ec88: add             x1, x1, HEAP, lsl #32
    // 0x91ec8c: r0 = value()
    //     0x91ec8c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x91ec90: r2 = LoadInt32Instr(r0)
    //     0x91ec90: sbfx            x2, x0, #1, #0x1f
    //     0x91ec94: tbz             w0, #0, #0x91ec9c
    //     0x91ec98: ldur            x2, [x0, #7]
    // 0x91ec9c: ldur            x1, [fp, #-8]
    // 0x91eca0: r0 = onPageRequest()
    //     0x91eca0: bl              #0xe35e04  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] DonationNewsListBuilderController::onPageRequest
    // 0x91eca4: r0 = Null
    //     0x91eca4: mov             x0, NULL
    // 0x91eca8: LeaveFrame
    //     0x91eca8: mov             SP, fp
    //     0x91ecac: ldp             fp, lr, [SP], #0x10
    // 0x91ecb0: ret
    //     0x91ecb0: ret             
    // 0x91ecb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ecb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ecb8: b               #0x91ec84
  }
  _ _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin(/* No info */) {
    // ** addr: 0xae1f4c, size: 0x148
    // 0xae1f4c: EnterFrame
    //     0xae1f4c: stp             fp, lr, [SP, #-0x10]!
    //     0xae1f50: mov             fp, SP
    // 0xae1f54: AllocStack(0x18)
    //     0xae1f54: sub             SP, SP, #0x18
    // 0xae1f58: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0xae1f58: mov             x0, x1
    //     0xae1f5c: stur            x1, [fp, #-8]
    // 0xae1f60: CheckStackOverflow
    //     0xae1f60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1f64: cmp             SP, x16
    //     0xae1f68: b.ls            #0xae208c
    // 0xae1f6c: r1 = 1
    //     0xae1f6c: movz            x1, #0x1
    // 0xae1f70: r0 = IntExtension.obs()
    //     0xae1f70: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0xae1f74: ldur            x2, [fp, #-8]
    // 0xae1f78: StoreField: r2->field_23 = r0
    //     0xae1f78: stur            w0, [x2, #0x23]
    //     0xae1f7c: ldurb           w16, [x2, #-1]
    //     0xae1f80: ldurb           w17, [x0, #-1]
    //     0xae1f84: and             x16, x17, x16, lsr #2
    //     0xae1f88: tst             x16, HEAP, lsr #32
    //     0xae1f8c: b.eq            #0xae1f94
    //     0xae1f90: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xae1f94: r1 = true
    //     0xae1f94: add             x1, NULL, #0x20  ; true
    // 0xae1f98: r0 = BoolExtension.obs()
    //     0xae1f98: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0xae1f9c: ldur            x2, [fp, #-8]
    // 0xae1fa0: StoreField: r2->field_27 = r0
    //     0xae1fa0: stur            w0, [x2, #0x27]
    //     0xae1fa4: ldurb           w16, [x2, #-1]
    //     0xae1fa8: ldurb           w17, [x0, #-1]
    //     0xae1fac: and             x16, x17, x16, lsr #2
    //     0xae1fb0: tst             x16, HEAP, lsr #32
    //     0xae1fb4: b.eq            #0xae1fbc
    //     0xae1fb8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xae1fbc: r1 = false
    //     0xae1fbc: add             x1, NULL, #0x30  ; false
    // 0xae1fc0: r0 = BoolExtension.obs()
    //     0xae1fc0: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0xae1fc4: ldur            x2, [fp, #-8]
    // 0xae1fc8: StoreField: r2->field_2b = r0
    //     0xae1fc8: stur            w0, [x2, #0x2b]
    //     0xae1fcc: ldurb           w16, [x2, #-1]
    //     0xae1fd0: ldurb           w17, [x0, #-1]
    //     0xae1fd4: and             x16, x17, x16, lsr #2
    //     0xae1fd8: tst             x16, HEAP, lsr #32
    //     0xae1fdc: b.eq            #0xae1fe4
    //     0xae1fe0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xae1fe4: r1 = false
    //     0xae1fe4: add             x1, NULL, #0x30  ; false
    // 0xae1fe8: r0 = BoolExtension.obs()
    //     0xae1fe8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0xae1fec: ldur            x3, [fp, #-8]
    // 0xae1ff0: StoreField: r3->field_2f = r0
    //     0xae1ff0: stur            w0, [x3, #0x2f]
    //     0xae1ff4: ldurb           w16, [x3, #-1]
    //     0xae1ff8: ldurb           w17, [x0, #-1]
    //     0xae1ffc: and             x16, x17, x16, lsr #2
    //     0xae2000: tst             x16, HEAP, lsr #32
    //     0xae2004: b.eq            #0xae200c
    //     0xae2008: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xae200c: r1 = <DonationNews>
    //     0xae200c: add             x1, PP, #0x30, lsl #12  ; [pp+0x304f8] TypeArguments: <DonationNews>
    //     0xae2010: ldr             x1, [x1, #0x4f8]
    // 0xae2014: r2 = 0
    //     0xae2014: movz            x2, #0
    // 0xae2018: r0 = _GrowableList()
    //     0xae2018: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xae201c: r16 = <DonationNews>
    //     0xae201c: add             x16, PP, #0x30, lsl #12  ; [pp+0x304f8] TypeArguments: <DonationNews>
    //     0xae2020: ldr             x16, [x16, #0x4f8]
    // 0xae2024: stp             x0, x16, [SP]
    // 0xae2028: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae2028: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae202c: r0 = ListExtension.obs()
    //     0xae202c: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0xae2030: ldur            x2, [fp, #-8]
    // 0xae2034: StoreField: r2->field_33 = r0
    //     0xae2034: stur            w0, [x2, #0x33]
    //     0xae2038: ldurb           w16, [x2, #-1]
    //     0xae203c: ldurb           w17, [x0, #-1]
    //     0xae2040: and             x16, x17, x16, lsr #2
    //     0xae2044: tst             x16, HEAP, lsr #32
    //     0xae2048: b.eq            #0xae2050
    //     0xae204c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xae2050: r1 = false
    //     0xae2050: add             x1, NULL, #0x30  ; false
    // 0xae2054: r0 = BoolExtension.obs()
    //     0xae2054: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0xae2058: ldur            x1, [fp, #-8]
    // 0xae205c: StoreField: r1->field_37 = r0
    //     0xae205c: stur            w0, [x1, #0x37]
    //     0xae2060: ldurb           w16, [x1, #-1]
    //     0xae2064: ldurb           w17, [x0, #-1]
    //     0xae2068: and             x16, x17, x16, lsr #2
    //     0xae206c: tst             x16, HEAP, lsr #32
    //     0xae2070: b.eq            #0xae2078
    //     0xae2074: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xae2078: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xae2078: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xae207c: r0 = Null
    //     0xae207c: mov             x0, NULL
    // 0xae2080: LeaveFrame
    //     0xae2080: mov             SP, fp
    //     0xae2084: ldp             fp, lr, [SP], #0x10
    // 0xae2088: ret
    //     0xae2088: ret             
    // 0xae208c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae208c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2090: b               #0xae1f6c
  }
  [closure] Future<void> onPageRefresh(dynamic) {
    // ** addr: 0xaeabcc, size: 0x38
    // 0xaeabcc: EnterFrame
    //     0xaeabcc: stp             fp, lr, [SP, #-0x10]!
    //     0xaeabd0: mov             fp, SP
    // 0xaeabd4: ldr             x0, [fp, #0x10]
    // 0xaeabd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaeabd8: ldur            w1, [x0, #0x17]
    // 0xaeabdc: DecompressPointer r1
    //     0xaeabdc: add             x1, x1, HEAP, lsl #32
    // 0xaeabe0: CheckStackOverflow
    //     0xaeabe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeabe4: cmp             SP, x16
    //     0xaeabe8: b.ls            #0xaeabfc
    // 0xaeabec: r0 = onPageRefresh()
    //     0xaeabec: bl              #0xaeac04  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageRefresh
    // 0xaeabf0: LeaveFrame
    //     0xaeabf0: mov             SP, fp
    //     0xaeabf4: ldp             fp, lr, [SP], #0x10
    // 0xaeabf8: ret
    //     0xaeabf8: ret             
    // 0xaeabfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeabfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeac00: b               #0xaeabec
  }
  _ onPageRefresh(/* No info */) async {
    // ** addr: 0xaeac04, size: 0xb4
    // 0xaeac04: EnterFrame
    //     0xaeac04: stp             fp, lr, [SP, #-0x10]!
    //     0xaeac08: mov             fp, SP
    // 0xaeac0c: AllocStack(0x18)
    //     0xaeac0c: sub             SP, SP, #0x18
    // 0xaeac10: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r1, fp-0x10 */)
    //     0xaeac10: stur            NULL, [fp, #-8]
    //     0xaeac14: stur            x1, [fp, #-0x10]
    // 0xaeac18: CheckStackOverflow
    //     0xaeac18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeac1c: cmp             SP, x16
    //     0xaeac20: b.ls            #0xaeacb0
    // 0xaeac24: InitAsync() -> Future<void?>
    //     0xaeac24: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xaeac28: bl              #0x661298  ; InitAsyncStub
    // 0xaeac2c: ldur            x0, [fp, #-0x10]
    // 0xaeac30: LoadField: r3 = r0->field_33
    //     0xaeac30: ldur            w3, [x0, #0x33]
    // 0xaeac34: DecompressPointer r3
    //     0xaeac34: add             x3, x3, HEAP, lsl #32
    // 0xaeac38: stur            x3, [fp, #-0x18]
    // 0xaeac3c: r1 = <DonationNews>
    //     0xaeac3c: add             x1, PP, #0x30, lsl #12  ; [pp+0x304f8] TypeArguments: <DonationNews>
    //     0xaeac40: ldr             x1, [x1, #0x4f8]
    // 0xaeac44: r2 = 0
    //     0xaeac44: movz            x2, #0
    // 0xaeac48: r0 = _GrowableList()
    //     0xaeac48: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaeac4c: ldur            x1, [fp, #-0x18]
    // 0xaeac50: mov             x2, x0
    // 0xaeac54: r0 = value=()
    //     0xaeac54: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0xaeac58: ldur            x0, [fp, #-0x10]
    // 0xaeac5c: LoadField: r1 = r0->field_23
    //     0xaeac5c: ldur            w1, [x0, #0x23]
    // 0xaeac60: DecompressPointer r1
    //     0xaeac60: add             x1, x1, HEAP, lsl #32
    // 0xaeac64: r2 = 2
    //     0xaeac64: movz            x2, #0x2
    // 0xaeac68: r0 = value=()
    //     0xaeac68: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaeac6c: ldur            x0, [fp, #-0x10]
    // 0xaeac70: LoadField: r1 = r0->field_27
    //     0xaeac70: ldur            w1, [x0, #0x27]
    // 0xaeac74: DecompressPointer r1
    //     0xaeac74: add             x1, x1, HEAP, lsl #32
    // 0xaeac78: r2 = false
    //     0xaeac78: add             x2, NULL, #0x30  ; false
    // 0xaeac7c: r0 = value=()
    //     0xaeac7c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaeac80: ldur            x0, [fp, #-0x10]
    // 0xaeac84: LoadField: r1 = r0->field_2b
    //     0xaeac84: ldur            w1, [x0, #0x2b]
    // 0xaeac88: DecompressPointer r1
    //     0xaeac88: add             x1, x1, HEAP, lsl #32
    // 0xaeac8c: r2 = false
    //     0xaeac8c: add             x2, NULL, #0x30  ; false
    // 0xaeac90: r0 = value=()
    //     0xaeac90: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaeac94: ldur            x1, [fp, #-0x10]
    // 0xaeac98: r0 = page()
    //     0xaeac98: bl              #0x7da3b8  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::page
    // 0xaeac9c: ldur            x1, [fp, #-0x10]
    // 0xaeaca0: mov             x2, x0
    // 0xaeaca4: r0 = onPageRequest()
    //     0xaeaca4: bl              #0xe35e04  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] DonationNewsListBuilderController::onPageRequest
    // 0xaeaca8: r0 = Null
    //     0xaeaca8: mov             x0, NULL
    // 0xaeacac: r0 = ReturnAsyncNotFuture()
    //     0xaeacac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xaeacb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeacb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeacb4: b               #0xaeac24
  }
  [closure] bool onPageScrolled(dynamic, ScrollNotification) {
    // ** addr: 0xaeacb8, size: 0x3c
    // 0xaeacb8: EnterFrame
    //     0xaeacb8: stp             fp, lr, [SP, #-0x10]!
    //     0xaeacbc: mov             fp, SP
    // 0xaeacc0: ldr             x0, [fp, #0x18]
    // 0xaeacc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaeacc4: ldur            w1, [x0, #0x17]
    // 0xaeacc8: DecompressPointer r1
    //     0xaeacc8: add             x1, x1, HEAP, lsl #32
    // 0xaeaccc: CheckStackOverflow
    //     0xaeaccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeacd0: cmp             SP, x16
    //     0xaeacd4: b.ls            #0xaeacec
    // 0xaeacd8: ldr             x2, [fp, #0x10]
    // 0xaeacdc: r0 = onPageScrolled()
    //     0xaeacdc: bl              #0xaeacf4  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageScrolled
    // 0xaeace0: LeaveFrame
    //     0xaeace0: mov             SP, fp
    //     0xaeace4: ldp             fp, lr, [SP], #0x10
    // 0xaeace8: ret
    //     0xaeace8: ret             
    // 0xaeacec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeacec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeacf0: b               #0xaeacd8
  }
  _ onPageScrolled(/* No info */) {
    // ** addr: 0xaeacf4, size: 0xcc
    // 0xaeacf4: EnterFrame
    //     0xaeacf4: stp             fp, lr, [SP, #-0x10]!
    //     0xaeacf8: mov             fp, SP
    // 0xaeacfc: AllocStack(0x10)
    //     0xaeacfc: sub             SP, SP, #0x10
    // 0xaead00: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaead00: mov             x0, x1
    //     0xaead04: stur            x1, [fp, #-8]
    //     0xaead08: stur            x2, [fp, #-0x10]
    // 0xaead0c: CheckStackOverflow
    //     0xaead0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaead10: cmp             SP, x16
    //     0xaead14: b.ls            #0xaeadb0
    // 0xaead18: mov             x1, x0
    // 0xaead1c: r0 = isFetching()
    //     0xaead1c: bl              #0xad3170  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::isFetching
    // 0xaead20: tbz             w0, #4, #0xaead34
    // 0xaead24: r0 = true
    //     0xaead24: add             x0, NULL, #0x20  ; true
    // 0xaead28: LeaveFrame
    //     0xaead28: mov             SP, fp
    //     0xaead2c: ldp             fp, lr, [SP], #0x10
    // 0xaead30: ret
    //     0xaead30: ret             
    // 0xaead34: ldur            x0, [fp, #-0x10]
    // 0xaead38: LoadField: r1 = r0->field_f
    //     0xaead38: ldur            w1, [x0, #0xf]
    // 0xaead3c: DecompressPointer r1
    //     0xaead3c: add             x1, x1, HEAP, lsl #32
    // 0xaead40: LoadField: r0 = r1->field_f
    //     0xaead40: ldur            w0, [x1, #0xf]
    // 0xaead44: DecompressPointer r0
    //     0xaead44: add             x0, x0, HEAP, lsl #32
    // 0xaead48: cmp             w0, NULL
    // 0xaead4c: b.eq            #0xaeadb8
    // 0xaead50: LoadField: r2 = r1->field_b
    //     0xaead50: ldur            w2, [x1, #0xb]
    // 0xaead54: DecompressPointer r2
    //     0xaead54: add             x2, x2, HEAP, lsl #32
    // 0xaead58: cmp             w2, NULL
    // 0xaead5c: b.eq            #0xaeadbc
    // 0xaead60: LoadField: d0 = r0->field_7
    //     0xaead60: ldur            d0, [x0, #7]
    // 0xaead64: LoadField: d1 = r2->field_7
    //     0xaead64: ldur            d1, [x2, #7]
    // 0xaead68: fcmp            d0, d1
    // 0xaead6c: b.ne            #0xaeada0
    // 0xaead70: ldur            x1, [fp, #-8]
    // 0xaead74: r0 = hasError()
    //     0xaead74: bl              #0xad1aa0  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError
    // 0xaead78: tbz             w0, #4, #0xaeada0
    // 0xaead7c: ldur            x0, [fp, #-8]
    // 0xaead80: LoadField: r1 = r0->field_23
    //     0xaead80: ldur            w1, [x0, #0x23]
    // 0xaead84: DecompressPointer r1
    //     0xaead84: add             x1, x1, HEAP, lsl #32
    // 0xaead88: r0 = value()
    //     0xaead88: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaead8c: r2 = LoadInt32Instr(r0)
    //     0xaead8c: sbfx            x2, x0, #1, #0x1f
    //     0xaead90: tbz             w0, #0, #0xaead98
    //     0xaead94: ldur            x2, [x0, #7]
    // 0xaead98: ldur            x1, [fp, #-8]
    // 0xaead9c: r0 = onPageRequest()
    //     0xaead9c: bl              #0xe35e04  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] DonationNewsListBuilderController::onPageRequest
    // 0xaeada0: r0 = false
    //     0xaeada0: add             x0, NULL, #0x30  ; false
    // 0xaeada4: LeaveFrame
    //     0xaeada4: mov             SP, fp
    //     0xaeada8: ldp             fp, lr, [SP], #0x10
    // 0xaeadac: ret
    //     0xaeadac: ret             
    // 0xaeadb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeadb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeadb4: b               #0xaead18
    // 0xaeadb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaeadb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaeadbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaeadbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ itemsCount(/* No info */) {
    // ** addr: 0xaeaec0, size: 0xe4
    // 0xaeaec0: EnterFrame
    //     0xaeaec0: stp             fp, lr, [SP, #-0x10]!
    //     0xaeaec4: mov             fp, SP
    // 0xaeaec8: AllocStack(0x18)
    //     0xaeaec8: sub             SP, SP, #0x18
    // 0xaeaecc: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r0, fp-0x10 */)
    //     0xaeaecc: mov             x0, x1
    //     0xaeaed0: stur            x1, [fp, #-0x10]
    // 0xaeaed4: CheckStackOverflow
    //     0xaeaed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeaed8: cmp             SP, x16
    //     0xaeaedc: b.ls            #0xaeaf9c
    // 0xaeaee0: LoadField: r2 = r0->field_33
    //     0xaeaee0: ldur            w2, [x0, #0x33]
    // 0xaeaee4: DecompressPointer r2
    //     0xaeaee4: add             x2, x2, HEAP, lsl #32
    // 0xaeaee8: mov             x1, x2
    // 0xaeaeec: stur            x2, [fp, #-8]
    // 0xaeaef0: r0 = value()
    //     0xaeaef0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeaef4: r1 = LoadClassIdInstr(r0)
    //     0xaeaef4: ldur            x1, [x0, #-1]
    //     0xaeaef8: ubfx            x1, x1, #0xc, #0x14
    // 0xaeaefc: str             x0, [SP]
    // 0xaeaf00: mov             x0, x1
    // 0xaeaf04: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeaf04: movz            x17, #0xc834
    //     0xaeaf08: add             lr, x0, x17
    //     0xaeaf0c: ldr             lr, [x21, lr, lsl #3]
    //     0xaeaf10: blr             lr
    // 0xaeaf14: cbnz            w0, #0xaeaf20
    // 0xaeaf18: r0 = 8
    //     0xaeaf18: movz            x0, #0x8
    // 0xaeaf1c: b               #0xaeaf90
    // 0xaeaf20: ldur            x0, [fp, #-0x10]
    // 0xaeaf24: ldur            x1, [fp, #-8]
    // 0xaeaf28: r0 = value()
    //     0xaeaf28: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeaf2c: r1 = LoadClassIdInstr(r0)
    //     0xaeaf2c: ldur            x1, [x0, #-1]
    //     0xaeaf30: ubfx            x1, x1, #0xc, #0x14
    // 0xaeaf34: str             x0, [SP]
    // 0xaeaf38: mov             x0, x1
    // 0xaeaf3c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeaf3c: movz            x17, #0xc834
    //     0xaeaf40: add             lr, x0, x17
    //     0xaeaf44: ldr             lr, [x21, lr, lsl #3]
    //     0xaeaf48: blr             lr
    // 0xaeaf4c: mov             x2, x0
    // 0xaeaf50: ldur            x0, [fp, #-0x10]
    // 0xaeaf54: stur            x2, [fp, #-8]
    // 0xaeaf58: LoadField: r1 = r0->field_27
    //     0xaeaf58: ldur            w1, [x0, #0x27]
    // 0xaeaf5c: DecompressPointer r1
    //     0xaeaf5c: add             x1, x1, HEAP, lsl #32
    // 0xaeaf60: r0 = value()
    //     0xaeaf60: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaeaf64: tst             x0, #0x10
    // 0xaeaf68: cset            x1, ne
    // 0xaeaf6c: sub             x1, x1, #1
    // 0xaeaf70: and             x1, x1, #6
    // 0xaeaf74: ldur            x2, [fp, #-8]
    // 0xaeaf78: r3 = LoadInt32Instr(r2)
    //     0xaeaf78: sbfx            x3, x2, #1, #0x1f
    //     0xaeaf7c: tbz             w2, #0, #0xaeaf84
    //     0xaeaf80: ldur            x3, [x2, #7]
    // 0xaeaf84: r2 = LoadInt32Instr(r1)
    //     0xaeaf84: sbfx            x2, x1, #1, #0x1f
    // 0xaeaf88: add             x1, x3, x2
    // 0xaeaf8c: mov             x0, x1
    // 0xaeaf90: LeaveFrame
    //     0xaeaf90: mov             SP, fp
    //     0xaeaf94: ldp             fp, lr, [SP], #0x10
    // 0xaeaf98: ret
    //     0xaeaf98: ret             
    // 0xaeaf9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeaf9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeafa0: b               #0xaeaee0
  }
  _ find(/* No info */) {
    // ** addr: 0xaeb068, size: 0x120
    // 0xaeb068: EnterFrame
    //     0xaeb068: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb06c: mov             fp, SP
    // 0xaeb070: AllocStack(0x68)
    //     0xaeb070: sub             SP, SP, #0x68
    // 0xaeb074: SetupParameters(dynamic _ /* r2 => r2, fp-0x58 */)
    //     0xaeb074: stur            x2, [fp, #-0x58]
    // 0xaeb078: CheckStackOverflow
    //     0xaeb078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb07c: cmp             SP, x16
    //     0xaeb080: b.ls            #0xaeb180
    // 0xaeb084: LoadField: r0 = r1->field_33
    //     0xaeb084: ldur            w0, [x1, #0x33]
    // 0xaeb088: DecompressPointer r0
    //     0xaeb088: add             x0, x0, HEAP, lsl #32
    // 0xaeb08c: mov             x1, x0
    // 0xaeb090: stur            x0, [fp, #-0x50]
    // 0xaeb094: r0 = value()
    //     0xaeb094: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeb098: r1 = LoadClassIdInstr(r0)
    //     0xaeb098: ldur            x1, [x0, #-1]
    //     0xaeb09c: ubfx            x1, x1, #0xc, #0x14
    // 0xaeb0a0: str             x0, [SP]
    // 0xaeb0a4: mov             x0, x1
    // 0xaeb0a8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeb0a8: movz            x17, #0xc834
    //     0xaeb0ac: add             lr, x0, x17
    //     0xaeb0b0: ldr             lr, [x21, lr, lsl #3]
    //     0xaeb0b4: blr             lr
    // 0xaeb0b8: cbz             w0, #0xaeb15c
    // 0xaeb0bc: ldur            x0, [fp, #-0x58]
    // 0xaeb0c0: ldur            x1, [fp, #-0x50]
    // 0xaeb0c4: r0 = value()
    //     0xaeb0c4: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeb0c8: r1 = LoadClassIdInstr(r0)
    //     0xaeb0c8: ldur            x1, [x0, #-1]
    //     0xaeb0cc: ubfx            x1, x1, #0xc, #0x14
    // 0xaeb0d0: str             x0, [SP]
    // 0xaeb0d4: mov             x0, x1
    // 0xaeb0d8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeb0d8: movz            x17, #0xc834
    //     0xaeb0dc: add             lr, x0, x17
    //     0xaeb0e0: ldr             lr, [x21, lr, lsl #3]
    //     0xaeb0e4: blr             lr
    // 0xaeb0e8: r1 = LoadInt32Instr(r0)
    //     0xaeb0e8: sbfx            x1, x0, #1, #0x1f
    //     0xaeb0ec: tbz             w0, #0, #0xaeb0f4
    //     0xaeb0f0: ldur            x1, [x0, #7]
    // 0xaeb0f4: ldur            x0, [fp, #-0x58]
    // 0xaeb0f8: cmp             x1, x0
    // 0xaeb0fc: b.le            #0xaeb14c
    // 0xaeb100: ldur            x1, [fp, #-0x50]
    // 0xaeb104: r0 = value()
    //     0xaeb104: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeb108: mov             x3, x0
    // 0xaeb10c: ldur            x2, [fp, #-0x58]
    // 0xaeb110: r0 = BoxInt64Instr(r2)
    //     0xaeb110: sbfiz           x0, x2, #1, #0x1f
    //     0xaeb114: cmp             x2, x0, asr #1
    //     0xaeb118: b.eq            #0xaeb124
    //     0xaeb11c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaeb120: stur            x2, [x0, #7]
    // 0xaeb124: r1 = LoadClassIdInstr(r3)
    //     0xaeb124: ldur            x1, [x3, #-1]
    //     0xaeb128: ubfx            x1, x1, #0xc, #0x14
    // 0xaeb12c: stp             x0, x3, [SP]
    // 0xaeb130: mov             x0, x1
    // 0xaeb134: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaeb134: movz            x17, #0x3037
    //     0xaeb138: movk            x17, #0x1, lsl #16
    //     0xaeb13c: add             lr, x0, x17
    //     0xaeb140: ldr             lr, [x21, lr, lsl #3]
    //     0xaeb144: blr             lr
    // 0xaeb148: b               #0xaeb150
    // 0xaeb14c: r0 = Null
    //     0xaeb14c: mov             x0, NULL
    // 0xaeb150: LeaveFrame
    //     0xaeb150: mov             SP, fp
    //     0xaeb154: ldp             fp, lr, [SP], #0x10
    // 0xaeb158: ret
    //     0xaeb158: ret             
    // 0xaeb15c: r0 = Null
    //     0xaeb15c: mov             x0, NULL
    // 0xaeb160: LeaveFrame
    //     0xaeb160: mov             SP, fp
    //     0xaeb164: ldp             fp, lr, [SP], #0x10
    // 0xaeb168: ret
    //     0xaeb168: ret             
    // 0xaeb16c: sub             SP, fp, #0x68
    // 0xaeb170: r0 = Null
    //     0xaeb170: mov             x0, NULL
    // 0xaeb174: LeaveFrame
    //     0xaeb174: mov             SP, fp
    //     0xaeb178: ldp             fp, lr, [SP], #0x10
    // 0xaeb17c: ret
    //     0xaeb17c: ret             
    // 0xaeb180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb180: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb184: b               #0xaeb084
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe346cc, size: 0x5c
    // 0xe346cc: EnterFrame
    //     0xe346cc: stp             fp, lr, [SP, #-0x10]!
    //     0xe346d0: mov             fp, SP
    // 0xe346d4: AllocStack(0x8)
    //     0xe346d4: sub             SP, SP, #8
    // 0xe346d8: SetupParameters(_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0xe346d8: mov             x0, x1
    //     0xe346dc: stur            x1, [fp, #-8]
    // 0xe346e0: CheckStackOverflow
    //     0xe346e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe346e4: cmp             SP, x16
    //     0xe346e8: b.ls            #0xe34720
    // 0xe346ec: LoadField: r1 = r0->field_2b
    //     0xe346ec: ldur            w1, [x0, #0x2b]
    // 0xe346f0: DecompressPointer r1
    //     0xe346f0: add             x1, x1, HEAP, lsl #32
    // 0xe346f4: r2 = true
    //     0xe346f4: add             x2, NULL, #0x20  ; true
    // 0xe346f8: r0 = value=()
    //     0xe346f8: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe346fc: ldur            x0, [fp, #-8]
    // 0xe34700: LoadField: r1 = r0->field_2f
    //     0xe34700: ldur            w1, [x0, #0x2f]
    // 0xe34704: DecompressPointer r1
    //     0xe34704: add             x1, x1, HEAP, lsl #32
    // 0xe34708: r2 = false
    //     0xe34708: add             x2, NULL, #0x30  ; false
    // 0xe3470c: r0 = value=()
    //     0xe3470c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe34710: r0 = Null
    //     0xe34710: mov             x0, NULL
    // 0xe34714: LeaveFrame
    //     0xe34714: mov             SP, fp
    //     0xe34718: ldp             fp, lr, [SP], #0x10
    // 0xe3471c: ret
    //     0xe3471c: ret             
    // 0xe34720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34724: b               #0xe346ec
  }
}

// class id: 1922, size: 0x4c, field offset: 0x3c
class DonationNewsListBuilderController extends _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin {

  _ onFetchLoaded(/* No info */) async {
    // ** addr: 0x7da2e4, size: 0xd4
    // 0x7da2e4: EnterFrame
    //     0x7da2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7da2e8: mov             fp, SP
    // 0x7da2ec: AllocStack(0x20)
    //     0x7da2ec: sub             SP, SP, #0x20
    // 0x7da2f0: SetupParameters(DonationNewsListBuilderController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7da2f0: stur            NULL, [fp, #-8]
    //     0x7da2f4: stur            x1, [fp, #-0x10]
    //     0x7da2f8: mov             x16, x2
    //     0x7da2fc: mov             x2, x1
    //     0x7da300: mov             x1, x16
    //     0x7da304: stur            x1, [fp, #-0x18]
    //     0x7da308: stur            x3, [fp, #-0x20]
    // 0x7da30c: CheckStackOverflow
    //     0x7da30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da310: cmp             SP, x16
    //     0x7da314: b.ls            #0x7da3b0
    // 0x7da318: InitAsync() -> Future<void?>
    //     0x7da318: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7da31c: bl              #0x661298  ; InitAsyncStub
    // 0x7da320: ldur            x0, [fp, #-0x10]
    // 0x7da324: LoadField: r1 = r0->field_23
    //     0x7da324: ldur            w1, [x0, #0x23]
    // 0x7da328: DecompressPointer r1
    //     0x7da328: add             x1, x1, HEAP, lsl #32
    // 0x7da32c: r0 = value()
    //     0x7da32c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7da330: cmp             w0, #2
    // 0x7da334: b.ne            #0x7da36c
    // 0x7da338: ldur            x2, [fp, #-0x18]
    // 0x7da33c: r0 = LoadClassIdInstr(r2)
    //     0x7da33c: ldur            x0, [x2, #-1]
    //     0x7da340: ubfx            x0, x0, #0xc, #0x14
    // 0x7da344: mov             x1, x2
    // 0x7da348: r0 = GDT[cid_x0 + 0xe879]()
    //     0x7da348: movz            x17, #0xe879
    //     0x7da34c: add             lr, x0, x17
    //     0x7da350: ldr             lr, [x21, lr, lsl #3]
    //     0x7da354: blr             lr
    // 0x7da358: tbnz            w0, #4, #0x7da36c
    // 0x7da35c: ldur            x1, [fp, #-0x10]
    // 0x7da360: r2 = true
    //     0x7da360: add             x2, NULL, #0x20  ; true
    // 0x7da364: r0 = hasError=()
    //     0x7da364: bl              #0x7da75c  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::hasError=
    // 0x7da368: b               #0x7da3a8
    // 0x7da36c: ldur            x0, [fp, #-0x20]
    // 0x7da370: cmp             w0, NULL
    // 0x7da374: b.ne            #0x7da380
    // 0x7da378: r3 = Null
    //     0x7da378: mov             x3, NULL
    // 0x7da37c: b               #0x7da39c
    // 0x7da380: LoadField: r1 = r0->field_7
    //     0x7da380: ldur            x1, [x0, #7]
    // 0x7da384: LoadField: r2 = r0->field_f
    //     0x7da384: ldur            x2, [x0, #0xf]
    // 0x7da388: cmp             x1, x2
    // 0x7da38c: r16 = true
    //     0x7da38c: add             x16, NULL, #0x20  ; true
    // 0x7da390: r17 = false
    //     0x7da390: add             x17, NULL, #0x30  ; false
    // 0x7da394: csel            x0, x16, x17, lt
    // 0x7da398: mov             x3, x0
    // 0x7da39c: ldur            x1, [fp, #-0x10]
    // 0x7da3a0: ldur            x2, [fp, #-0x18]
    // 0x7da3a4: r0 = appendToPage()
    //     0x7da3a4: bl              #0x7da4cc  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::appendToPage
    // 0x7da3a8: r0 = Null
    //     0x7da3a8: mov             x0, NULL
    // 0x7da3ac: r0 = ReturnAsyncNotFuture()
    //     0x7da3ac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7da3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da3b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da3b4: b               #0x7da318
  }
  _ onFetchFailure(/* No info */) async {
    // ** addr: 0x7e27b8, size: 0x5c
    // 0x7e27b8: EnterFrame
    //     0x7e27b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7e27bc: mov             fp, SP
    // 0x7e27c0: AllocStack(0x20)
    //     0x7e27c0: sub             SP, SP, #0x20
    // 0x7e27c4: SetupParameters(DonationNewsListBuilderController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x7e27c4: stur            NULL, [fp, #-8]
    //     0x7e27c8: stur            x1, [fp, #-0x10]
    //     0x7e27cc: mov             x16, x3
    //     0x7e27d0: mov             x3, x1
    //     0x7e27d4: mov             x1, x16
    //     0x7e27d8: stur            x2, [fp, #-0x18]
    //     0x7e27dc: stur            x1, [fp, #-0x20]
    // 0x7e27e0: CheckStackOverflow
    //     0x7e27e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e27e4: cmp             SP, x16
    //     0x7e27e8: b.ls            #0x7e280c
    // 0x7e27ec: InitAsync() -> Future<void?>
    //     0x7e27ec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7e27f0: bl              #0x661298  ; InitAsyncStub
    // 0x7e27f4: ldur            x1, [fp, #-0x20]
    // 0x7e27f8: r2 = Instance_IconData
    //     0x7e27f8: add             x2, PP, #0x28, lsl #12  ; [pp+0x28080] Obj!IconData@e0fe91
    //     0x7e27fc: ldr             x2, [x2, #0x80]
    // 0x7e2800: r0 = show()
    //     0x7e2800: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0x7e2804: r0 = Null
    //     0x7e2804: mov             x0, NULL
    // 0x7e2808: r0 = ReturnAsyncNotFuture()
    //     0x7e2808: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e280c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e280c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e2810: b               #0x7e27ec
  }
  _ onFetchRequested(/* No info */) async {
    // ** addr: 0x7eb770, size: 0x88
    // 0x7eb770: EnterFrame
    //     0x7eb770: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb774: mov             fp, SP
    // 0x7eb778: AllocStack(0x18)
    //     0x7eb778: sub             SP, SP, #0x18
    // 0x7eb77c: SetupParameters(DonationNewsListBuilderController this /* r1 => r1, fp-0x10 */)
    //     0x7eb77c: stur            NULL, [fp, #-8]
    //     0x7eb780: stur            x1, [fp, #-0x10]
    // 0x7eb784: CheckStackOverflow
    //     0x7eb784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb788: cmp             SP, x16
    //     0x7eb78c: b.ls            #0x7eb7f0
    // 0x7eb790: InitAsync() -> Future<ApiResult<List<DonationNews>>>
    //     0x7eb790: add             x0, PP, #0x35, lsl #12  ; [pp+0x35880] TypeArguments: <ApiResult<List<DonationNews>>>
    //     0x7eb794: ldr             x0, [x0, #0x880]
    //     0x7eb798: bl              #0x661298  ; InitAsyncStub
    // 0x7eb79c: ldur            x0, [fp, #-0x10]
    // 0x7eb7a0: LoadField: r2 = r0->field_47
    //     0x7eb7a0: ldur            w2, [x0, #0x47]
    // 0x7eb7a4: DecompressPointer r2
    //     0x7eb7a4: add             x2, x2, HEAP, lsl #32
    // 0x7eb7a8: stur            x2, [fp, #-0x18]
    // 0x7eb7ac: LoadField: r1 = r0->field_23
    //     0x7eb7ac: ldur            w1, [x0, #0x23]
    // 0x7eb7b0: DecompressPointer r1
    //     0x7eb7b0: add             x1, x1, HEAP, lsl #32
    // 0x7eb7b4: r0 = value()
    //     0x7eb7b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7eb7b8: mov             x1, x0
    // 0x7eb7bc: ldur            x0, [fp, #-0x10]
    // 0x7eb7c0: LoadField: r6 = r0->field_3f
    //     0x7eb7c0: ldur            w6, [x0, #0x3f]
    // 0x7eb7c4: DecompressPointer r6
    //     0x7eb7c4: add             x6, x6, HEAP, lsl #32
    // 0x7eb7c8: LoadField: r3 = r0->field_3b
    //     0x7eb7c8: ldur            w3, [x0, #0x3b]
    // 0x7eb7cc: DecompressPointer r3
    //     0x7eb7cc: add             x3, x3, HEAP, lsl #32
    // 0x7eb7d0: LoadField: r2 = r0->field_43
    //     0x7eb7d0: ldur            w2, [x0, #0x43]
    // 0x7eb7d4: DecompressPointer r2
    //     0x7eb7d4: add             x2, x2, HEAP, lsl #32
    // 0x7eb7d8: r5 = LoadInt32Instr(r1)
    //     0x7eb7d8: sbfx            x5, x1, #1, #0x1f
    //     0x7eb7dc: tbz             w1, #0, #0x7eb7e4
    //     0x7eb7e0: ldur            x5, [x1, #7]
    // 0x7eb7e4: ldur            x1, [fp, #-0x18]
    // 0x7eb7e8: r0 = findAllNews()
    //     0x7eb7e8: bl              #0x7eb7f8  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAllNews
    // 0x7eb7ec: r0 = ReturnAsync()
    //     0x7eb7ec: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7eb7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb7f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb7f4: b               #0x7eb790
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe35e04, size: 0x48
    // 0xe35e04: EnterFrame
    //     0xe35e04: stp             fp, lr, [SP, #-0x10]!
    //     0xe35e08: mov             fp, SP
    // 0xe35e0c: AllocStack(0x8)
    //     0xe35e0c: sub             SP, SP, #8
    // 0xe35e10: SetupParameters(DonationNewsListBuilderController this /* r1 => r0, fp-0x8 */)
    //     0xe35e10: mov             x0, x1
    //     0xe35e14: stur            x1, [fp, #-8]
    // 0xe35e18: CheckStackOverflow
    //     0xe35e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35e1c: cmp             SP, x16
    //     0xe35e20: b.ls            #0xe35e44
    // 0xe35e24: mov             x1, x0
    // 0xe35e28: r0 = onPageRequest()
    //     0xe35e28: bl              #0xe346cc  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageRequest
    // 0xe35e2c: ldur            x1, [fp, #-8]
    // 0xe35e30: r0 = fetch()
    //     0xe35e30: bl              #0x8f43f8  ; [package:nuonline/common/mixins/fetch_mixin.dart] _FetchController&GetxController&FetchMixin::fetch
    // 0xe35e34: r0 = Null
    //     0xe35e34: mov             x0, NULL
    // 0xe35e38: LeaveFrame
    //     0xe35e38: mov             SP, fp
    //     0xe35e3c: ldp             fp, lr, [SP], #0x10
    // 0xe35e40: ret
    //     0xe35e40: ret             
    // 0xe35e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe35e48: b               #0xe35e24
  }
}
