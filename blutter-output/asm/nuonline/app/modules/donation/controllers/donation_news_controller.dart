// lib: , url: package:nuonline/app/modules/donation/controllers/donation_news_controller.dart

// class id: 1050208, size: 0x8
class :: {
}

// class id: 2029, size: 0x30, field offset: 0x24
class DonationNewsController extends _ArticleChannelSettingController&GetxController&GetSingleTickerProviderStateMixin {

  late TabController tabController; // offset: 0x2c

  _ onInit(/* No info */) {
    // ** addr: 0x8bfaa0, size: 0x7c
    // 0x8bfaa0: EnterFrame
    //     0x8bfaa0: stp             fp, lr, [SP, #-0x10]!
    //     0x8bfaa4: mov             fp, SP
    // 0x8bfaa8: AllocStack(0x10)
    //     0x8bfaa8: sub             SP, SP, #0x10
    // 0x8bfaac: SetupParameters(DonationNewsController this /* r1 => r0, fp-0x8 */)
    //     0x8bfaac: mov             x0, x1
    //     0x8bfab0: stur            x1, [fp, #-8]
    // 0x8bfab4: CheckStackOverflow
    //     0x8bfab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bfab8: cmp             SP, x16
    //     0x8bfabc: b.ls            #0x8bfb14
    // 0x8bfac0: mov             x1, x0
    // 0x8bfac4: r0 = onInit()
    //     0x8bfac4: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8bfac8: r0 = TabController()
    //     0x8bfac8: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8bfacc: mov             x1, x0
    // 0x8bfad0: ldur            x3, [fp, #-8]
    // 0x8bfad4: r2 = 2
    //     0x8bfad4: movz            x2, #0x2
    // 0x8bfad8: stur            x0, [fp, #-0x10]
    // 0x8bfadc: r0 = TabController()
    //     0x8bfadc: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8bfae0: ldur            x0, [fp, #-0x10]
    // 0x8bfae4: ldur            x1, [fp, #-8]
    // 0x8bfae8: StoreField: r1->field_2b = r0
    //     0x8bfae8: stur            w0, [x1, #0x2b]
    //     0x8bfaec: ldurb           w16, [x1, #-1]
    //     0x8bfaf0: ldurb           w17, [x0, #-1]
    //     0x8bfaf4: and             x16, x17, x16, lsr #2
    //     0x8bfaf8: tst             x16, HEAP, lsr #32
    //     0x8bfafc: b.eq            #0x8bfb04
    //     0x8bfb00: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8bfb04: r0 = Null
    //     0x8bfb04: mov             x0, NULL
    // 0x8bfb08: LeaveFrame
    //     0x8bfb08: mov             SP, fp
    //     0x8bfb0c: ldp             fp, lr, [SP], #0x10
    // 0x8bfb10: ret
    //     0x8bfb10: ret             
    // 0x8bfb14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bfb14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bfb18: b               #0x8bfac0
  }
  _ onClose(/* No info */) {
    // ** addr: 0x926b0c, size: 0x54
    // 0x926b0c: EnterFrame
    //     0x926b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x926b10: mov             fp, SP
    // 0x926b14: CheckStackOverflow
    //     0x926b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926b18: cmp             SP, x16
    //     0x926b1c: b.ls            #0x926b4c
    // 0x926b20: LoadField: r0 = r1->field_2b
    //     0x926b20: ldur            w0, [x1, #0x2b]
    // 0x926b24: DecompressPointer r0
    //     0x926b24: add             x0, x0, HEAP, lsl #32
    // 0x926b28: r16 = Sentinel
    //     0x926b28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x926b2c: cmp             w0, w16
    // 0x926b30: b.eq            #0x926b54
    // 0x926b34: mov             x1, x0
    // 0x926b38: r0 = dispose()
    //     0x926b38: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x926b3c: r0 = Null
    //     0x926b3c: mov             x0, NULL
    // 0x926b40: LeaveFrame
    //     0x926b40: mov             SP, fp
    //     0x926b44: ldp             fp, lr, [SP], #0x10
    // 0x926b48: ret
    //     0x926b48: ret             
    // 0x926b4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926b4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926b50: b               #0x926b20
    // 0x926b54: r9 = tabController
    //     0x926b54: add             x9, PP, #0x30, lsl #12  ; [pp+0x304d0] Field <DonationNewsController.tabController>: late (offset: 0x2c)
    //     0x926b58: ldr             x9, [x9, #0x4d0]
    // 0x926b5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x926b5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
