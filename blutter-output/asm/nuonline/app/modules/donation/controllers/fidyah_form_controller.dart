// lib: , url: package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart

// class id: 1050210, size: 0x8
class :: {
}

// class id: 1912, size: 0x48, field offset: 0x40
//   transformed mixin,
abstract class _FidyahFormController&DonationFormController&StateMixin extends DonationFormController
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x8f48ac, size: 0xd4
    // 0x8f48ac: EnterFrame
    //     0x8f48ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8f48b0: mov             fp, SP
    // 0x8f48b4: mov             x0, x3
    // 0x8f48b8: mov             x3, x1
    // 0x8f48bc: CheckStackOverflow
    //     0x8f48bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f48c0: cmp             SP, x16
    //     0x8f48c4: b.ls            #0x8f4978
    // 0x8f48c8: StoreField: r3->field_43 = r0
    //     0x8f48c8: stur            w0, [x3, #0x43]
    //     0x8f48cc: ldurb           w16, [x3, #-1]
    //     0x8f48d0: ldurb           w17, [x0, #-1]
    //     0x8f48d4: and             x16, x17, x16, lsr #2
    //     0x8f48d8: tst             x16, HEAP, lsr #32
    //     0x8f48dc: b.eq            #0x8f48e4
    //     0x8f48e0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f48e4: LoadField: r4 = r3->field_3f
    //     0x8f48e4: ldur            w4, [x3, #0x3f]
    // 0x8f48e8: DecompressPointer r4
    //     0x8f48e8: add             x4, x4, HEAP, lsl #32
    // 0x8f48ec: r0 = BoxInt64Instr(r2)
    //     0x8f48ec: sbfiz           x0, x2, #1, #0x1f
    //     0x8f48f0: cmp             x2, x0, asr #1
    //     0x8f48f4: b.eq            #0x8f4900
    //     0x8f48f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f48fc: stur            x2, [x0, #7]
    // 0x8f4900: cmp             w0, w4
    // 0x8f4904: b.eq            #0x8f4960
    // 0x8f4908: and             w16, w0, w4
    // 0x8f490c: branchIfSmi(r16, 0x8f4940)
    //     0x8f490c: tbz             w16, #0, #0x8f4940
    // 0x8f4910: r16 = LoadClassIdInstr(r0)
    //     0x8f4910: ldur            x16, [x0, #-1]
    //     0x8f4914: ubfx            x16, x16, #0xc, #0x14
    // 0x8f4918: cmp             x16, #0x3d
    // 0x8f491c: b.ne            #0x8f4940
    // 0x8f4920: r16 = LoadClassIdInstr(r4)
    //     0x8f4920: ldur            x16, [x4, #-1]
    //     0x8f4924: ubfx            x16, x16, #0xc, #0x14
    // 0x8f4928: cmp             x16, #0x3d
    // 0x8f492c: b.ne            #0x8f4940
    // 0x8f4930: LoadField: r16 = r0->field_7
    //     0x8f4930: ldur            x16, [x0, #7]
    // 0x8f4934: LoadField: r17 = r4->field_7
    //     0x8f4934: ldur            x17, [x4, #7]
    // 0x8f4938: cmp             x16, x17
    // 0x8f493c: b.eq            #0x8f4960
    // 0x8f4940: StoreField: r3->field_3f = r0
    //     0x8f4940: stur            w0, [x3, #0x3f]
    //     0x8f4944: tbz             w0, #0, #0x8f4960
    //     0x8f4948: ldurb           w16, [x3, #-1]
    //     0x8f494c: ldurb           w17, [x0, #-1]
    //     0x8f4950: and             x16, x17, x16, lsr #2
    //     0x8f4954: tst             x16, HEAP, lsr #32
    //     0x8f4958: b.eq            #0x8f4960
    //     0x8f495c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f4960: mov             x1, x3
    // 0x8f4964: r0 = _notifyUpdate()
    //     0x8f4964: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x8f4968: r0 = Null
    //     0x8f4968: mov             x0, NULL
    // 0x8f496c: LeaveFrame
    //     0x8f496c: mov             SP, fp
    //     0x8f4970: ldp             fp, lr, [SP], #0x10
    // 0x8f4974: ret
    //     0x8f4974: ret             
    // 0x8f4978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f497c: b               #0x8f48c8
  }
}

// class id: 1913, size: 0x50, field offset: 0x48
class FidyahFormController extends _FidyahFormController&DonationFormController&StateMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8f46ec, size: 0xb4
    // 0x8f46ec: EnterFrame
    //     0x8f46ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8f46f0: mov             fp, SP
    // 0x8f46f4: AllocStack(0x18)
    //     0x8f46f4: sub             SP, SP, #0x18
    // 0x8f46f8: SetupParameters(FidyahFormController this /* r1 => r1, fp-0x8 */)
    //     0x8f46f8: stur            x1, [fp, #-8]
    // 0x8f46fc: CheckStackOverflow
    //     0x8f46fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4700: cmp             SP, x16
    //     0x8f4704: b.ls            #0x8f4798
    // 0x8f4708: r1 = 1
    //     0x8f4708: movz            x1, #0x1
    // 0x8f470c: r0 = AllocateContext()
    //     0x8f470c: bl              #0xec126c  ; AllocateContextStub
    // 0x8f4710: ldur            x1, [fp, #-8]
    // 0x8f4714: stur            x0, [fp, #-0x10]
    // 0x8f4718: StoreField: r0->field_f = r1
    //     0x8f4718: stur            w1, [x0, #0xf]
    // 0x8f471c: r0 = onInit()
    //     0x8f471c: bl              #0x8f7e70  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onInit
    // 0x8f4720: ldur            x2, [fp, #-0x10]
    // 0x8f4724: r1 = Function '<anonymous closure>':.
    //     0x8f4724: add             x1, PP, #0x35, lsl #12  ; [pp+0x35828] AnonymousClosure: (0x8f4a00), in [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::onInit (0x8f46ec)
    //     0x8f4728: ldr             x1, [x1, #0x828]
    // 0x8f472c: r0 = AllocateClosure()
    //     0x8f472c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4730: r1 = <ZakatSetting>
    //     0x8f4730: add             x1, PP, #0x34, lsl #12  ; [pp+0x347a0] TypeArguments: <ZakatSetting>
    //     0x8f4734: ldr             x1, [x1, #0x7a0]
    // 0x8f4738: stur            x0, [fp, #-8]
    // 0x8f473c: r0 = FetchWrapper()
    //     0x8f473c: bl              #0x8c01bc  ; AllocateFetchWrapperStub -> FetchWrapper<X0> (size=0x18)
    // 0x8f4740: mov             x3, x0
    // 0x8f4744: ldur            x0, [fp, #-8]
    // 0x8f4748: stur            x3, [fp, #-0x18]
    // 0x8f474c: StoreField: r3->field_b = r0
    //     0x8f474c: stur            w0, [x3, #0xb]
    // 0x8f4750: ldur            x2, [fp, #-0x10]
    // 0x8f4754: r1 = Function '<anonymous closure>':.
    //     0x8f4754: add             x1, PP, #0x35, lsl #12  ; [pp+0x35830] AnonymousClosure: (0x8f4980), in [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::onInit (0x8f46ec)
    //     0x8f4758: ldr             x1, [x1, #0x830]
    // 0x8f475c: r0 = AllocateClosure()
    //     0x8f475c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4760: mov             x1, x0
    // 0x8f4764: ldur            x0, [fp, #-0x18]
    // 0x8f4768: StoreField: r0->field_f = r1
    //     0x8f4768: stur            w1, [x0, #0xf]
    // 0x8f476c: ldur            x2, [fp, #-0x10]
    // 0x8f4770: r1 = Function '<anonymous closure>':.
    //     0x8f4770: add             x1, PP, #0x35, lsl #12  ; [pp+0x35838] AnonymousClosure: (0x8f47a0), in [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::onInit (0x8f46ec)
    //     0x8f4774: ldr             x1, [x1, #0x838]
    // 0x8f4778: r0 = AllocateClosure()
    //     0x8f4778: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f477c: ldur            x1, [fp, #-0x18]
    // 0x8f4780: StoreField: r1->field_13 = r0
    //     0x8f4780: stur            w0, [x1, #0x13]
    // 0x8f4784: r0 = fetch()
    //     0x8f4784: bl              #0x8bfc28  ; [package:nuonline/common/mixins/fetch_mixin.dart] _FetchWrapper&Object&FetchMixin::fetch
    // 0x8f4788: r0 = Null
    //     0x8f4788: mov             x0, NULL
    // 0x8f478c: LeaveFrame
    //     0x8f478c: mov             SP, fp
    //     0x8f4790: ldp             fp, lr, [SP], #0x10
    // 0x8f4794: ret
    //     0x8f4794: ret             
    // 0x8f4798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f479c: b               #0x8f4708
  }
  [closure] void <anonymous closure>(dynamic, NetworkExceptions, String) {
    // ** addr: 0x8f47a0, size: 0x10c
    // 0x8f47a0: EnterFrame
    //     0x8f47a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f47a4: mov             fp, SP
    // 0x8f47a8: AllocStack(0x38)
    //     0x8f47a8: sub             SP, SP, #0x38
    // 0x8f47ac: SetupParameters()
    //     0x8f47ac: ldr             x0, [fp, #0x20]
    //     0x8f47b0: ldur            w1, [x0, #0x17]
    //     0x8f47b4: add             x1, x1, HEAP, lsl #32
    //     0x8f47b8: stur            x1, [fp, #-8]
    // 0x8f47bc: CheckStackOverflow
    //     0x8f47bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f47c0: cmp             SP, x16
    //     0x8f47c4: b.ls            #0x8f48a4
    // 0x8f47c8: r1 = 1
    //     0x8f47c8: movz            x1, #0x1
    // 0x8f47cc: r0 = AllocateContext()
    //     0x8f47cc: bl              #0xec126c  ; AllocateContextStub
    // 0x8f47d0: mov             x1, x0
    // 0x8f47d4: ldur            x0, [fp, #-8]
    // 0x8f47d8: stur            x1, [fp, #-0x10]
    // 0x8f47dc: StoreField: r1->field_b = r0
    //     0x8f47dc: stur            w0, [x1, #0xb]
    // 0x8f47e0: ldr             x2, [fp, #0x10]
    // 0x8f47e4: StoreField: r1->field_f = r2
    //     0x8f47e4: stur            w2, [x1, #0xf]
    // 0x8f47e8: r0 = RxStatus()
    //     0x8f47e8: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8f47ec: mov             x1, x0
    // 0x8f47f0: r0 = false
    //     0x8f47f0: add             x0, NULL, #0x30  ; false
    // 0x8f47f4: StoreField: r1->field_f = r0
    //     0x8f47f4: stur            w0, [x1, #0xf]
    // 0x8f47f8: StoreField: r1->field_7 = r0
    //     0x8f47f8: stur            w0, [x1, #7]
    // 0x8f47fc: StoreField: r1->field_b = r0
    //     0x8f47fc: stur            w0, [x1, #0xb]
    // 0x8f4800: ldur            x0, [fp, #-8]
    // 0x8f4804: LoadField: r2 = r0->field_f
    //     0x8f4804: ldur            w2, [x0, #0xf]
    // 0x8f4808: DecompressPointer r2
    //     0x8f4808: add             x2, x2, HEAP, lsl #32
    // 0x8f480c: mov             x3, x1
    // 0x8f4810: mov             x1, x2
    // 0x8f4814: r2 = 0
    //     0x8f4814: movz            x2, #0
    // 0x8f4818: r0 = change()
    //     0x8f4818: bl              #0x8f48ac  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] _FidyahFormController&DonationFormController&StateMixin::change
    // 0x8f481c: r1 = Function '<anonymous closure>':.
    //     0x8f481c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35840] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8f4820: ldr             x1, [x1, #0x840]
    // 0x8f4824: r2 = Null
    //     0x8f4824: mov             x2, NULL
    // 0x8f4828: r0 = AllocateClosure()
    //     0x8f4828: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f482c: r1 = Function '<anonymous closure>':.
    //     0x8f482c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35848] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8f4830: ldr             x1, [x1, #0x848]
    // 0x8f4834: r2 = Null
    //     0x8f4834: mov             x2, NULL
    // 0x8f4838: stur            x0, [fp, #-8]
    // 0x8f483c: r0 = AllocateClosure()
    //     0x8f483c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4840: ldur            x2, [fp, #-0x10]
    // 0x8f4844: r1 = Function '<anonymous closure>':.
    //     0x8f4844: add             x1, PP, #0x35, lsl #12  ; [pp+0x35850] AnonymousClosure: (0x7e6108), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onFetchFailure (0x7e5ff8)
    //     0x8f4848: ldr             x1, [x1, #0x850]
    // 0x8f484c: stur            x0, [fp, #-0x10]
    // 0x8f4850: r0 = AllocateClosure()
    //     0x8f4850: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4854: mov             x1, x0
    // 0x8f4858: ldr             x0, [fp, #0x18]
    // 0x8f485c: r2 = LoadClassIdInstr(r0)
    //     0x8f485c: ldur            x2, [x0, #-1]
    //     0x8f4860: ubfx            x2, x2, #0xc, #0x14
    // 0x8f4864: r16 = <Null?>
    //     0x8f4864: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x8f4868: stp             x0, x16, [SP, #0x18]
    // 0x8f486c: ldur            x16, [fp, #-0x10]
    // 0x8f4870: stp             x16, x1, [SP, #8]
    // 0x8f4874: ldur            x16, [fp, #-8]
    // 0x8f4878: str             x16, [SP]
    // 0x8f487c: mov             x0, x2
    // 0x8f4880: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x8f4880: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x8f4884: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x8f4884: movz            x17, #0x47d8
    //     0x8f4888: add             lr, x0, x17
    //     0x8f488c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4890: blr             lr
    // 0x8f4894: r0 = Null
    //     0x8f4894: mov             x0, NULL
    // 0x8f4898: LeaveFrame
    //     0x8f4898: mov             SP, fp
    //     0x8f489c: ldp             fp, lr, [SP], #0x10
    // 0x8f48a0: ret
    //     0x8f48a0: ret             
    // 0x8f48a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f48a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f48a8: b               #0x8f47c8
  }
  [closure] void <anonymous closure>(dynamic, ZakatSetting, Pagination?) {
    // ** addr: 0x8f4980, size: 0x80
    // 0x8f4980: EnterFrame
    //     0x8f4980: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4984: mov             fp, SP
    // 0x8f4988: AllocStack(0x10)
    //     0x8f4988: sub             SP, SP, #0x10
    // 0x8f498c: SetupParameters()
    //     0x8f498c: ldr             x0, [fp, #0x20]
    //     0x8f4990: ldur            w1, [x0, #0x17]
    //     0x8f4994: add             x1, x1, HEAP, lsl #32
    //     0x8f4998: stur            x1, [fp, #-0x10]
    // 0x8f499c: CheckStackOverflow
    //     0x8f499c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f49a0: cmp             SP, x16
    //     0x8f49a4: b.ls            #0x8f49f8
    // 0x8f49a8: ldr             x0, [fp, #0x18]
    // 0x8f49ac: LoadField: r2 = r0->field_37
    //     0x8f49ac: ldur            x2, [x0, #0x37]
    // 0x8f49b0: stur            x2, [fp, #-8]
    // 0x8f49b4: r0 = RxStatus()
    //     0x8f49b4: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8f49b8: mov             x1, x0
    // 0x8f49bc: r0 = false
    //     0x8f49bc: add             x0, NULL, #0x30  ; false
    // 0x8f49c0: StoreField: r1->field_f = r0
    //     0x8f49c0: stur            w0, [x1, #0xf]
    // 0x8f49c4: StoreField: r1->field_7 = r0
    //     0x8f49c4: stur            w0, [x1, #7]
    // 0x8f49c8: StoreField: r1->field_b = r0
    //     0x8f49c8: stur            w0, [x1, #0xb]
    // 0x8f49cc: ldur            x0, [fp, #-0x10]
    // 0x8f49d0: LoadField: r2 = r0->field_f
    //     0x8f49d0: ldur            w2, [x0, #0xf]
    // 0x8f49d4: DecompressPointer r2
    //     0x8f49d4: add             x2, x2, HEAP, lsl #32
    // 0x8f49d8: mov             x3, x1
    // 0x8f49dc: mov             x1, x2
    // 0x8f49e0: ldur            x2, [fp, #-8]
    // 0x8f49e4: r0 = change()
    //     0x8f49e4: bl              #0x8f48ac  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] _FidyahFormController&DonationFormController&StateMixin::change
    // 0x8f49e8: r0 = Null
    //     0x8f49e8: mov             x0, NULL
    // 0x8f49ec: LeaveFrame
    //     0x8f49ec: mov             SP, fp
    //     0x8f49f0: ldp             fp, lr, [SP], #0x10
    // 0x8f49f4: ret
    //     0x8f49f4: ret             
    // 0x8f49f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f49f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f49fc: b               #0x8f49a8
  }
  [closure] Future<ApiResult<ZakatSetting>> <anonymous closure>(dynamic) {
    // ** addr: 0x8f4a00, size: 0x48
    // 0x8f4a00: EnterFrame
    //     0x8f4a00: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4a04: mov             fp, SP
    // 0x8f4a08: ldr             x0, [fp, #0x10]
    // 0x8f4a0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f4a0c: ldur            w1, [x0, #0x17]
    // 0x8f4a10: DecompressPointer r1
    //     0x8f4a10: add             x1, x1, HEAP, lsl #32
    // 0x8f4a14: CheckStackOverflow
    //     0x8f4a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4a18: cmp             SP, x16
    //     0x8f4a1c: b.ls            #0x8f4a40
    // 0x8f4a20: LoadField: r0 = r1->field_f
    //     0x8f4a20: ldur            w0, [x1, #0xf]
    // 0x8f4a24: DecompressPointer r0
    //     0x8f4a24: add             x0, x0, HEAP, lsl #32
    // 0x8f4a28: LoadField: r1 = r0->field_47
    //     0x8f4a28: ldur            w1, [x0, #0x47]
    // 0x8f4a2c: DecompressPointer r1
    //     0x8f4a2c: add             x1, x1, HEAP, lsl #32
    // 0x8f4a30: r0 = findSetting()
    //     0x8f4a30: bl              #0x7e9244  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::findSetting
    // 0x8f4a34: LeaveFrame
    //     0x8f4a34: mov             SP, fp
    //     0x8f4a38: ldp             fp, lr, [SP], #0x10
    // 0x8f4a3c: ret
    //     0x8f4a3c: ret             
    // 0x8f4a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4a40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4a44: b               #0x8f4a20
  }
  _ FidyahFormController(/* No info */) {
    // ** addr: 0x8f8c60, size: 0xe0
    // 0x8f8c60: EnterFrame
    //     0x8f8c60: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8c64: mov             fp, SP
    // 0x8f8c68: AllocStack(0x30)
    //     0x8f8c68: sub             SP, SP, #0x30
    // 0x8f8c6c: r0 = 2
    //     0x8f8c6c: movz            x0, #0x2
    // 0x8f8c70: mov             x5, x1
    // 0x8f8c74: mov             x4, x2
    // 0x8f8c78: stur            x1, [fp, #-8]
    // 0x8f8c7c: stur            x2, [fp, #-0x10]
    // 0x8f8c80: stur            x3, [fp, #-0x18]
    // 0x8f8c84: CheckStackOverflow
    //     0x8f8c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8c88: cmp             SP, x16
    //     0x8f8c8c: b.ls            #0x8f8d38
    // 0x8f8c90: mov             x2, x0
    // 0x8f8c94: r1 = Null
    //     0x8f8c94: mov             x1, NULL
    // 0x8f8c98: r0 = AllocateArray()
    //     0x8f8c98: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8f8c9c: stur            x0, [fp, #-0x20]
    // 0x8f8ca0: r16 = Instance_FidyahItem
    //     0x8f8ca0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30030] Obj!FidyahItem@e259e1
    //     0x8f8ca4: ldr             x16, [x16, #0x30]
    // 0x8f8ca8: StoreField: r0->field_f = r16
    //     0x8f8ca8: stur            w16, [x0, #0xf]
    // 0x8f8cac: r1 = <FidyahItem>
    //     0x8f8cac: add             x1, PP, #0x30, lsl #12  ; [pp+0x30038] TypeArguments: <FidyahItem>
    //     0x8f8cb0: ldr             x1, [x1, #0x38]
    // 0x8f8cb4: r0 = AllocateGrowableArray()
    //     0x8f8cb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8f8cb8: mov             x1, x0
    // 0x8f8cbc: ldur            x0, [fp, #-0x20]
    // 0x8f8cc0: StoreField: r1->field_f = r0
    //     0x8f8cc0: stur            w0, [x1, #0xf]
    // 0x8f8cc4: r0 = 2
    //     0x8f8cc4: movz            x0, #0x2
    // 0x8f8cc8: StoreField: r1->field_b = r0
    //     0x8f8cc8: stur            w0, [x1, #0xb]
    // 0x8f8ccc: r16 = <FidyahItem>
    //     0x8f8ccc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] TypeArguments: <FidyahItem>
    //     0x8f8cd0: ldr             x16, [x16, #0x38]
    // 0x8f8cd4: stp             x1, x16, [SP]
    // 0x8f8cd8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f8cd8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f8cdc: r0 = ListExtension.obs()
    //     0x8f8cdc: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x8f8ce0: ldur            x1, [fp, #-8]
    // 0x8f8ce4: StoreField: r1->field_4b = r0
    //     0x8f8ce4: stur            w0, [x1, #0x4b]
    //     0x8f8ce8: ldurb           w16, [x1, #-1]
    //     0x8f8cec: ldurb           w17, [x0, #-1]
    //     0x8f8cf0: and             x16, x17, x16, lsr #2
    //     0x8f8cf4: tst             x16, HEAP, lsr #32
    //     0x8f8cf8: b.eq            #0x8f8d00
    //     0x8f8cfc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8d00: ldur            x0, [fp, #-0x18]
    // 0x8f8d04: StoreField: r1->field_47 = r0
    //     0x8f8d04: stur            w0, [x1, #0x47]
    //     0x8f8d08: ldurb           w16, [x1, #-1]
    //     0x8f8d0c: ldurb           w17, [x0, #-1]
    //     0x8f8d10: and             x16, x17, x16, lsr #2
    //     0x8f8d14: tst             x16, HEAP, lsr #32
    //     0x8f8d18: b.eq            #0x8f8d20
    //     0x8f8d1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8d20: ldur            x2, [fp, #-0x10]
    // 0x8f8d24: r0 = DonationFormController()
    //     0x8f8d24: bl              #0x8f8f2c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::DonationFormController
    // 0x8f8d28: r0 = Null
    //     0x8f8d28: mov             x0, NULL
    // 0x8f8d2c: LeaveFrame
    //     0x8f8d2c: mov             SP, fp
    //     0x8f8d30: ldp             fp, lr, [SP], #0x10
    // 0x8f8d34: ret
    //     0x8f8d34: ret             
    // 0x8f8d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8d3c: b               #0x8f8c90
  }
  [closure] void addItem(dynamic) {
    // ** addr: 0xaeb8c8, size: 0x38
    // 0xaeb8c8: EnterFrame
    //     0xaeb8c8: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb8cc: mov             fp, SP
    // 0xaeb8d0: ldr             x0, [fp, #0x10]
    // 0xaeb8d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaeb8d4: ldur            w1, [x0, #0x17]
    // 0xaeb8d8: DecompressPointer r1
    //     0xaeb8d8: add             x1, x1, HEAP, lsl #32
    // 0xaeb8dc: CheckStackOverflow
    //     0xaeb8dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb8e0: cmp             SP, x16
    //     0xaeb8e4: b.ls            #0xaeb8f8
    // 0xaeb8e8: r0 = addItem()
    //     0xaeb8e8: bl              #0xaeb900  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::addItem
    // 0xaeb8ec: LeaveFrame
    //     0xaeb8ec: mov             SP, fp
    //     0xaeb8f0: ldp             fp, lr, [SP], #0x10
    // 0xaeb8f4: ret
    //     0xaeb8f4: ret             
    // 0xaeb8f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb8f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb8fc: b               #0xaeb8e8
  }
  _ addItem(/* No info */) {
    // ** addr: 0xaeb900, size: 0x48
    // 0xaeb900: EnterFrame
    //     0xaeb900: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb904: mov             fp, SP
    // 0xaeb908: AllocStack(0x10)
    //     0xaeb908: sub             SP, SP, #0x10
    // 0xaeb90c: CheckStackOverflow
    //     0xaeb90c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb910: cmp             SP, x16
    //     0xaeb914: b.ls            #0xaeb940
    // 0xaeb918: LoadField: r0 = r1->field_4b
    //     0xaeb918: ldur            w0, [x1, #0x4b]
    // 0xaeb91c: DecompressPointer r0
    //     0xaeb91c: add             x0, x0, HEAP, lsl #32
    // 0xaeb920: r16 = Instance_FidyahItem
    //     0xaeb920: add             x16, PP, #0x30, lsl #12  ; [pp+0x30030] Obj!FidyahItem@e259e1
    //     0xaeb924: ldr             x16, [x16, #0x30]
    // 0xaeb928: stp             x16, x0, [SP]
    // 0xaeb92c: r0 = add()
    //     0xaeb92c: bl              #0x66b5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0xaeb930: r0 = Null
    //     0xaeb930: mov             x0, NULL
    // 0xaeb934: LeaveFrame
    //     0xaeb934: mov             SP, fp
    //     0xaeb938: ldp             fp, lr, [SP], #0x10
    // 0xaeb93c: ret
    //     0xaeb93c: ret             
    // 0xaeb940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb944: b               #0xaeb918
  }
  get _ canDeleteItem(/* No info */) {
    // ** addr: 0xaebc14, size: 0x78
    // 0xaebc14: EnterFrame
    //     0xaebc14: stp             fp, lr, [SP, #-0x10]!
    //     0xaebc18: mov             fp, SP
    // 0xaebc1c: AllocStack(0x8)
    //     0xaebc1c: sub             SP, SP, #8
    // 0xaebc20: CheckStackOverflow
    //     0xaebc20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebc24: cmp             SP, x16
    //     0xaebc28: b.ls            #0xaebc84
    // 0xaebc2c: LoadField: r0 = r1->field_4b
    //     0xaebc2c: ldur            w0, [x1, #0x4b]
    // 0xaebc30: DecompressPointer r0
    //     0xaebc30: add             x0, x0, HEAP, lsl #32
    // 0xaebc34: mov             x1, x0
    // 0xaebc38: r0 = value()
    //     0xaebc38: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaebc3c: r1 = LoadClassIdInstr(r0)
    //     0xaebc3c: ldur            x1, [x0, #-1]
    //     0xaebc40: ubfx            x1, x1, #0xc, #0x14
    // 0xaebc44: str             x0, [SP]
    // 0xaebc48: mov             x0, x1
    // 0xaebc4c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaebc4c: movz            x17, #0xc834
    //     0xaebc50: add             lr, x0, x17
    //     0xaebc54: ldr             lr, [x21, lr, lsl #3]
    //     0xaebc58: blr             lr
    // 0xaebc5c: r1 = LoadInt32Instr(r0)
    //     0xaebc5c: sbfx            x1, x0, #1, #0x1f
    //     0xaebc60: tbz             w0, #0, #0xaebc68
    //     0xaebc64: ldur            x1, [x0, #7]
    // 0xaebc68: cmp             x1, #1
    // 0xaebc6c: r16 = true
    //     0xaebc6c: add             x16, NULL, #0x20  ; true
    // 0xaebc70: r17 = false
    //     0xaebc70: add             x17, NULL, #0x30  ; false
    // 0xaebc74: csel            x0, x16, x17, gt
    // 0xaebc78: LeaveFrame
    //     0xaebc78: mov             SP, fp
    //     0xaebc7c: ldp             fp, lr, [SP], #0x10
    // 0xaebc80: ret
    //     0xaebc80: ret             
    // 0xaebc84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebc84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebc88: b               #0xaebc2c
  }
  _ changeItem(/* No info */) {
    // ** addr: 0xaebd40, size: 0x64
    // 0xaebd40: EnterFrame
    //     0xaebd40: stp             fp, lr, [SP, #-0x10]!
    //     0xaebd44: mov             fp, SP
    // 0xaebd48: AllocStack(0x20)
    //     0xaebd48: sub             SP, SP, #0x20
    // 0xaebd4c: CheckStackOverflow
    //     0xaebd4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebd50: cmp             SP, x16
    //     0xaebd54: b.ls            #0xaebd9c
    // 0xaebd58: LoadField: r4 = r1->field_4b
    //     0xaebd58: ldur            w4, [x1, #0x4b]
    // 0xaebd5c: DecompressPointer r4
    //     0xaebd5c: add             x4, x4, HEAP, lsl #32
    // 0xaebd60: stur            x4, [fp, #-8]
    // 0xaebd64: r0 = BoxInt64Instr(r2)
    //     0xaebd64: sbfiz           x0, x2, #1, #0x1f
    //     0xaebd68: cmp             x2, x0, asr #1
    //     0xaebd6c: b.eq            #0xaebd78
    //     0xaebd70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaebd74: stur            x2, [x0, #7]
    // 0xaebd78: stp             x0, x4, [SP, #8]
    // 0xaebd7c: str             x3, [SP]
    // 0xaebd80: r0 = []=()
    //     0xaebd80: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xaebd84: ldur            x1, [fp, #-8]
    // 0xaebd88: r0 = refresh()
    //     0xaebd88: bl              #0x6680f4  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::refresh
    // 0xaebd8c: r0 = Null
    //     0xaebd8c: mov             x0, NULL
    // 0xaebd90: LeaveFrame
    //     0xaebd90: mov             SP, fp
    //     0xaebd94: ldp             fp, lr, [SP], #0x10
    // 0xaebd98: ret
    //     0xaebd98: ret             
    // 0xaebd9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebd9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebda0: b               #0xaebd58
  }
  get _ totalAmount(/* No info */) {
    // ** addr: 0xaec858, size: 0x70
    // 0xaec858: EnterFrame
    //     0xaec858: stp             fp, lr, [SP, #-0x10]!
    //     0xaec85c: mov             fp, SP
    // 0xaec860: AllocStack(0x10)
    //     0xaec860: sub             SP, SP, #0x10
    // 0xaec864: SetupParameters(FidyahFormController this /* r1 => r0, fp-0x8 */)
    //     0xaec864: mov             x0, x1
    //     0xaec868: stur            x1, [fp, #-8]
    // 0xaec86c: CheckStackOverflow
    //     0xaec86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec870: cmp             SP, x16
    //     0xaec874: b.ls            #0xaec8c0
    // 0xaec878: mov             x1, x0
    // 0xaec87c: r0 = totalDay()
    //     0xaec87c: bl              #0xaec8c8  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::totalDay
    // 0xaec880: ldur            x1, [fp, #-8]
    // 0xaec884: stur            x0, [fp, #-0x10]
    // 0xaec888: r0 = value()
    //     0xaec888: bl              #0x72ba14  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin&StateMixin::value
    // 0xaec88c: cmp             w0, NULL
    // 0xaec890: b.ne            #0xaec89c
    // 0xaec894: r2 = 0
    //     0xaec894: movz            x2, #0
    // 0xaec898: b               #0xaec8ac
    // 0xaec89c: r1 = LoadInt32Instr(r0)
    //     0xaec89c: sbfx            x1, x0, #1, #0x1f
    //     0xaec8a0: tbz             w0, #0, #0xaec8a8
    //     0xaec8a4: ldur            x1, [x0, #7]
    // 0xaec8a8: mov             x2, x1
    // 0xaec8ac: ldur            x1, [fp, #-0x10]
    // 0xaec8b0: mul             x0, x1, x2
    // 0xaec8b4: LeaveFrame
    //     0xaec8b4: mov             SP, fp
    //     0xaec8b8: ldp             fp, lr, [SP], #0x10
    // 0xaec8bc: ret
    //     0xaec8bc: ret             
    // 0xaec8c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec8c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec8c4: b               #0xaec878
  }
  get _ totalDay(/* No info */) {
    // ** addr: 0xaec8c8, size: 0x64
    // 0xaec8c8: EnterFrame
    //     0xaec8c8: stp             fp, lr, [SP, #-0x10]!
    //     0xaec8cc: mov             fp, SP
    // 0xaec8d0: AllocStack(0x20)
    //     0xaec8d0: sub             SP, SP, #0x20
    // 0xaec8d4: CheckStackOverflow
    //     0xaec8d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec8d8: cmp             SP, x16
    //     0xaec8dc: b.ls            #0xaec924
    // 0xaec8e0: LoadField: r0 = r1->field_4b
    //     0xaec8e0: ldur            w0, [x1, #0x4b]
    // 0xaec8e4: DecompressPointer r0
    //     0xaec8e4: add             x0, x0, HEAP, lsl #32
    // 0xaec8e8: stur            x0, [fp, #-8]
    // 0xaec8ec: r1 = Function '<anonymous closure>':.
    //     0xaec8ec: add             x1, PP, #0x34, lsl #12  ; [pp+0x34cb0] Function: [dart:io] _ExternalBuffer::start (0xbf35d0)
    //     0xaec8f0: ldr             x1, [x1, #0xcb0]
    // 0xaec8f4: r2 = Null
    //     0xaec8f4: mov             x2, NULL
    // 0xaec8f8: r0 = AllocateClosure()
    //     0xaec8f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec8fc: r16 = <FidyahItem>
    //     0xaec8fc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] TypeArguments: <FidyahItem>
    //     0xaec900: ldr             x16, [x16, #0x38]
    // 0xaec904: ldur            lr, [fp, #-8]
    // 0xaec908: stp             lr, x16, [SP, #8]
    // 0xaec90c: str             x0, [SP]
    // 0xaec910: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaec910: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaec914: r0 = IterableSC.sumBy()
    //     0xaec914: bl              #0xaec92c  ; [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumBy
    // 0xaec918: LeaveFrame
    //     0xaec918: mov             SP, fp
    //     0xaec91c: ldp             fp, lr, [SP], #0x10
    // 0xaec920: ret
    //     0xaec920: ret             
    // 0xaec924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec928: b               #0xaec8e0
  }
  get _ trx(/* No info */) {
    // ** addr: 0xe35250, size: 0x230
    // 0xe35250: EnterFrame
    //     0xe35250: stp             fp, lr, [SP, #-0x10]!
    //     0xe35254: mov             fp, SP
    // 0xe35258: AllocStack(0x58)
    //     0xe35258: sub             SP, SP, #0x58
    // 0xe3525c: SetupParameters(FidyahFormController this /* r1 => r0, fp-0x10 */)
    //     0xe3525c: mov             x0, x1
    //     0xe35260: stur            x1, [fp, #-0x10]
    // 0xe35264: CheckStackOverflow
    //     0xe35264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35268: cmp             SP, x16
    //     0xe3526c: b.ls            #0xe35478
    // 0xe35270: LoadField: r3 = r0->field_4b
    //     0xe35270: ldur            w3, [x0, #0x4b]
    // 0xe35274: DecompressPointer r3
    //     0xe35274: add             x3, x3, HEAP, lsl #32
    // 0xe35278: stur            x3, [fp, #-8]
    // 0xe3527c: r1 = Function '<anonymous closure>':.
    //     0xe3527c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35858] AnonymousClosure: (0xadf834), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xe35280: ldr             x1, [x1, #0x858]
    // 0xe35284: r2 = Null
    //     0xe35284: mov             x2, NULL
    // 0xe35288: r0 = AllocateClosure()
    //     0xe35288: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3528c: r16 = <String>
    //     0xe3528c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe35290: ldur            lr, [fp, #-8]
    // 0xe35294: stp             lr, x16, [SP, #8]
    // 0xe35298: str             x0, [SP]
    // 0xe3529c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe3529c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe352a0: r0 = map()
    //     0xe352a0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xe352a4: r16 = ", "
    //     0xe352a4: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xe352a8: str             x16, [SP]
    // 0xe352ac: mov             x1, x0
    // 0xe352b0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe352b0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe352b4: r0 = join()
    //     0xe352b4: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xe352b8: r1 = Function '<anonymous closure>':.
    //     0xe352b8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35860] AnonymousClosure: (0xadf834), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xe352bc: ldr             x1, [x1, #0x860]
    // 0xe352c0: r2 = Null
    //     0xe352c0: mov             x2, NULL
    // 0xe352c4: stur            x0, [fp, #-0x18]
    // 0xe352c8: r0 = AllocateClosure()
    //     0xe352c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe352cc: r16 = <String>
    //     0xe352cc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe352d0: ldur            lr, [fp, #-8]
    // 0xe352d4: stp             lr, x16, [SP, #8]
    // 0xe352d8: str             x0, [SP]
    // 0xe352dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe352dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe352e0: r0 = map()
    //     0xe352e0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xe352e4: LoadField: r1 = r0->field_7
    //     0xe352e4: ldur            w1, [x0, #7]
    // 0xe352e8: DecompressPointer r1
    //     0xe352e8: add             x1, x1, HEAP, lsl #32
    // 0xe352ec: mov             x2, x0
    // 0xe352f0: r0 = _GrowableList.of()
    //     0xe352f0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe352f4: r1 = Function '<anonymous closure>':.
    //     0xe352f4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35868] Function: [dart:io] _ExternalBuffer::start (0xbf35d0)
    //     0xe352f8: ldr             x1, [x1, #0x868]
    // 0xe352fc: r2 = Null
    //     0xe352fc: mov             x2, NULL
    // 0xe35300: stur            x0, [fp, #-0x20]
    // 0xe35304: r0 = AllocateClosure()
    //     0xe35304: bl              #0xec1630  ; AllocateClosureStub
    // 0xe35308: r16 = <int>
    //     0xe35308: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe3530c: ldur            lr, [fp, #-8]
    // 0xe35310: stp             lr, x16, [SP, #8]
    // 0xe35314: str             x0, [SP]
    // 0xe35318: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe35318: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe3531c: r0 = map()
    //     0xe3531c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xe35320: LoadField: r1 = r0->field_7
    //     0xe35320: ldur            w1, [x0, #7]
    // 0xe35324: DecompressPointer r1
    //     0xe35324: add             x1, x1, HEAP, lsl #32
    // 0xe35328: mov             x2, x0
    // 0xe3532c: r0 = _GrowableList.of()
    //     0xe3532c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe35330: mov             x2, x0
    // 0xe35334: ldur            x0, [fp, #-0x10]
    // 0xe35338: stur            x2, [fp, #-0x28]
    // 0xe3533c: LoadField: r1 = r0->field_2b
    //     0xe3533c: ldur            w1, [x0, #0x2b]
    // 0xe35340: DecompressPointer r1
    //     0xe35340: add             x1, x1, HEAP, lsl #32
    // 0xe35344: r0 = value()
    //     0xe35344: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35348: r1 = Null
    //     0xe35348: mov             x1, NULL
    // 0xe3534c: r2 = 6
    //     0xe3534c: movz            x2, #0x6
    // 0xe35350: stur            x0, [fp, #-0x30]
    // 0xe35354: r0 = AllocateArray()
    //     0xe35354: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe35358: stur            x0, [fp, #-0x38]
    // 0xe3535c: r16 = "Fidyah "
    //     0xe3535c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35870] "Fidyah "
    //     0xe35360: ldr             x16, [x16, #0x870]
    // 0xe35364: StoreField: r0->field_f = r16
    //     0xe35364: stur            w16, [x0, #0xf]
    // 0xe35368: r1 = Function '<anonymous closure>':.
    //     0xe35368: add             x1, PP, #0x34, lsl #12  ; [pp+0x34cb0] Function: [dart:io] _ExternalBuffer::start (0xbf35d0)
    //     0xe3536c: ldr             x1, [x1, #0xcb0]
    // 0xe35370: r2 = Null
    //     0xe35370: mov             x2, NULL
    // 0xe35374: r0 = AllocateClosure()
    //     0xe35374: bl              #0xec1630  ; AllocateClosureStub
    // 0xe35378: r16 = <FidyahItem>
    //     0xe35378: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] TypeArguments: <FidyahItem>
    //     0xe3537c: ldr             x16, [x16, #0x38]
    // 0xe35380: ldur            lr, [fp, #-8]
    // 0xe35384: stp             lr, x16, [SP, #8]
    // 0xe35388: str             x0, [SP]
    // 0xe3538c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe3538c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe35390: r0 = IterableSC.sumBy()
    //     0xe35390: bl              #0xaec92c  ; [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumBy
    // 0xe35394: mov             x2, x0
    // 0xe35398: r0 = BoxInt64Instr(r2)
    //     0xe35398: sbfiz           x0, x2, #1, #0x1f
    //     0xe3539c: cmp             x2, x0, asr #1
    //     0xe353a0: b.eq            #0xe353ac
    //     0xe353a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe353a8: stur            x2, [x0, #7]
    // 0xe353ac: ldur            x1, [fp, #-0x38]
    // 0xe353b0: ArrayStore: r1[1] = r0  ; List_4
    //     0xe353b0: add             x25, x1, #0x13
    //     0xe353b4: str             w0, [x25]
    //     0xe353b8: tbz             w0, #0, #0xe353d4
    //     0xe353bc: ldurb           w16, [x1, #-1]
    //     0xe353c0: ldurb           w17, [x0, #-1]
    //     0xe353c4: and             x16, x17, x16, lsr #2
    //     0xe353c8: tst             x16, HEAP, lsr #32
    //     0xe353cc: b.eq            #0xe353d4
    //     0xe353d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe353d4: ldur            x0, [fp, #-0x38]
    // 0xe353d8: r16 = " Hari"
    //     0xe353d8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35878] " Hari"
    //     0xe353dc: ldr             x16, [x16, #0x878]
    // 0xe353e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xe353e0: stur            w16, [x0, #0x17]
    // 0xe353e4: str             x0, [SP]
    // 0xe353e8: r0 = _interpolate()
    //     0xe353e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe353ec: ldur            x1, [fp, #-0x10]
    // 0xe353f0: stur            x0, [fp, #-8]
    // 0xe353f4: r0 = totalAmount()
    //     0xe353f4: bl              #0xaec858  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::totalAmount
    // 0xe353f8: r1 = <ZakatTypes>
    //     0xe353f8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xe353fc: ldr             x1, [x1, #0x68]
    // 0xe35400: stur            x0, [fp, #-0x40]
    // 0xe35404: r0 = TransactionRequest()
    //     0xe35404: bl              #0x810fc0  ; AllocateTransactionRequestStub -> TransactionRequest<X0> (size=0x4c)
    // 0xe35408: mov             x3, x0
    // 0xe3540c: r2 = Instance_PaymentMethod
    //     0xe3540c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xe35410: ldr             x2, [x2, #0x220]
    // 0xe35414: StoreField: r3->field_b = r2
    //     0xe35414: stur            w2, [x3, #0xb]
    // 0xe35418: r2 = Instance_PaymentType
    //     0xe35418: add             x2, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xe3541c: ldr             x2, [x2, #0x638]
    // 0xe35420: StoreField: r3->field_f = r2
    //     0xe35420: stur            w2, [x3, #0xf]
    // 0xe35424: ldur            x2, [fp, #-0x40]
    // 0xe35428: r0 = BoxInt64Instr(r2)
    //     0xe35428: sbfiz           x0, x2, #1, #0x1f
    //     0xe3542c: cmp             x2, x0, asr #1
    //     0xe35430: b.eq            #0xe3543c
    //     0xe35434: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe35438: stur            x2, [x0, #7]
    // 0xe3543c: ArrayStore: r3[0] = r0  ; List_4
    //     0xe3543c: stur            w0, [x3, #0x17]
    // 0xe35440: ldur            x1, [fp, #-0x18]
    // 0xe35444: StoreField: r3->field_1b = r1
    //     0xe35444: stur            w1, [x3, #0x1b]
    // 0xe35448: ldur            x1, [fp, #-0x20]
    // 0xe3544c: StoreField: r3->field_1f = r1
    //     0xe3544c: stur            w1, [x3, #0x1f]
    // 0xe35450: ldur            x1, [fp, #-0x28]
    // 0xe35454: StoreField: r3->field_23 = r1
    //     0xe35454: stur            w1, [x3, #0x23]
    // 0xe35458: ldur            x1, [fp, #-8]
    // 0xe3545c: StoreField: r3->field_2f = r1
    //     0xe3545c: stur            w1, [x3, #0x2f]
    // 0xe35460: ldur            x1, [fp, #-0x30]
    // 0xe35464: StoreField: r3->field_33 = r1
    //     0xe35464: stur            w1, [x3, #0x33]
    // 0xe35468: mov             x0, x3
    // 0xe3546c: LeaveFrame
    //     0xe3546c: mov             SP, fp
    //     0xe35470: ldp             fp, lr, [SP], #0x10
    // 0xe35474: ret
    //     0xe35474: ret             
    // 0xe35478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3547c: b               #0xe35270
  }
}

// class id: 5561, size: 0x14, field offset: 0x8
//   const constructor, 
class FidyahItem extends Equatable {

  _Mint field_c;

  _ copyWith(/* No info */) {
    // ** addr: 0xaebda4, size: 0xf0
    // 0xaebda4: EnterFrame
    //     0xaebda4: stp             fp, lr, [SP, #-0x10]!
    //     0xaebda8: mov             fp, SP
    // 0xaebdac: AllocStack(0x10)
    //     0xaebdac: sub             SP, SP, #0x10
    // 0xaebdb0: SetupParameters({dynamic day = Null /* r3 */, dynamic name = Null /* r0 */})
    //     0xaebdb0: ldur            w0, [x4, #0x13]
    //     0xaebdb4: ldur            w2, [x4, #0x1f]
    //     0xaebdb8: add             x2, x2, HEAP, lsl #32
    //     0xaebdbc: add             x16, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0xaebdc0: ldr             x16, [x16, #0x340]
    //     0xaebdc4: cmp             w2, w16
    //     0xaebdc8: b.ne            #0xaebdec
    //     0xaebdcc: ldur            w2, [x4, #0x23]
    //     0xaebdd0: add             x2, x2, HEAP, lsl #32
    //     0xaebdd4: sub             w3, w0, w2
    //     0xaebdd8: add             x2, fp, w3, sxtw #2
    //     0xaebddc: ldr             x2, [x2, #8]
    //     0xaebde0: mov             x3, x2
    //     0xaebde4: movz            x2, #0x1
    //     0xaebde8: b               #0xaebdf4
    //     0xaebdec: mov             x3, NULL
    //     0xaebdf0: movz            x2, #0
    //     0xaebdf4: lsl             x5, x2, #1
    //     0xaebdf8: lsl             w2, w5, #1
    //     0xaebdfc: add             w5, w2, #8
    //     0xaebe00: add             x16, x4, w5, sxtw #1
    //     0xaebe04: ldur            w6, [x16, #0xf]
    //     0xaebe08: add             x6, x6, HEAP, lsl #32
    //     0xaebe0c: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    //     0xaebe10: cmp             w6, w16
    //     0xaebe14: b.ne            #0xaebe38
    //     0xaebe18: add             w5, w2, #0xa
    //     0xaebe1c: add             x16, x4, w5, sxtw #1
    //     0xaebe20: ldur            w2, [x16, #0xf]
    //     0xaebe24: add             x2, x2, HEAP, lsl #32
    //     0xaebe28: sub             w4, w0, w2
    //     0xaebe2c: add             x0, fp, w4, sxtw #2
    //     0xaebe30: ldr             x0, [x0, #8]
    //     0xaebe34: b               #0xaebe3c
    //     0xaebe38: mov             x0, NULL
    // 0xaebe3c: cmp             w0, NULL
    // 0xaebe40: b.ne            #0xaebe4c
    // 0xaebe44: LoadField: r0 = r1->field_7
    //     0xaebe44: ldur            w0, [x1, #7]
    // 0xaebe48: DecompressPointer r0
    //     0xaebe48: add             x0, x0, HEAP, lsl #32
    // 0xaebe4c: stur            x0, [fp, #-0x10]
    // 0xaebe50: cmp             w3, NULL
    // 0xaebe54: b.ne            #0xaebe64
    // 0xaebe58: LoadField: r2 = r1->field_b
    //     0xaebe58: ldur            x2, [x1, #0xb]
    // 0xaebe5c: mov             x1, x2
    // 0xaebe60: b               #0xaebe70
    // 0xaebe64: r1 = LoadInt32Instr(r3)
    //     0xaebe64: sbfx            x1, x3, #1, #0x1f
    //     0xaebe68: tbz             w3, #0, #0xaebe70
    //     0xaebe6c: ldur            x1, [x3, #7]
    // 0xaebe70: stur            x1, [fp, #-8]
    // 0xaebe74: r0 = FidyahItem()
    //     0xaebe74: bl              #0xaebe94  ; AllocateFidyahItemStub -> FidyahItem (size=0x14)
    // 0xaebe78: ldur            x1, [fp, #-0x10]
    // 0xaebe7c: StoreField: r0->field_7 = r1
    //     0xaebe7c: stur            w1, [x0, #7]
    // 0xaebe80: ldur            x1, [fp, #-8]
    // 0xaebe84: StoreField: r0->field_b = r1
    //     0xaebe84: stur            x1, [x0, #0xb]
    // 0xaebe88: LeaveFrame
    //     0xaebe88: mov             SP, fp
    //     0xaebe8c: ldp             fp, lr, [SP], #0x10
    // 0xaebe90: ret
    //     0xaebe90: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc3e8, size: 0x80
    // 0xbdc3e8: EnterFrame
    //     0xbdc3e8: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc3ec: mov             fp, SP
    // 0xbdc3f0: AllocStack(0x18)
    //     0xbdc3f0: sub             SP, SP, #0x18
    // 0xbdc3f4: r0 = 4
    //     0xbdc3f4: movz            x0, #0x4
    // 0xbdc3f8: LoadField: r3 = r1->field_7
    //     0xbdc3f8: ldur            w3, [x1, #7]
    // 0xbdc3fc: DecompressPointer r3
    //     0xbdc3fc: add             x3, x3, HEAP, lsl #32
    // 0xbdc400: stur            x3, [fp, #-0x10]
    // 0xbdc404: LoadField: r4 = r1->field_b
    //     0xbdc404: ldur            x4, [x1, #0xb]
    // 0xbdc408: mov             x2, x0
    // 0xbdc40c: stur            x4, [fp, #-8]
    // 0xbdc410: r1 = Null
    //     0xbdc410: mov             x1, NULL
    // 0xbdc414: r0 = AllocateArray()
    //     0xbdc414: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc418: mov             x2, x0
    // 0xbdc41c: ldur            x0, [fp, #-0x10]
    // 0xbdc420: stur            x2, [fp, #-0x18]
    // 0xbdc424: StoreField: r2->field_f = r0
    //     0xbdc424: stur            w0, [x2, #0xf]
    // 0xbdc428: ldur            x3, [fp, #-8]
    // 0xbdc42c: r0 = BoxInt64Instr(r3)
    //     0xbdc42c: sbfiz           x0, x3, #1, #0x1f
    //     0xbdc430: cmp             x3, x0, asr #1
    //     0xbdc434: b.eq            #0xbdc440
    //     0xbdc438: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc43c: stur            x3, [x0, #7]
    // 0xbdc440: StoreField: r2->field_13 = r0
    //     0xbdc440: stur            w0, [x2, #0x13]
    // 0xbdc444: r1 = <Object?>
    //     0xbdc444: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc448: r0 = AllocateGrowableArray()
    //     0xbdc448: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc44c: ldur            x1, [fp, #-0x18]
    // 0xbdc450: StoreField: r0->field_f = r1
    //     0xbdc450: stur            w1, [x0, #0xf]
    // 0xbdc454: r1 = 4
    //     0xbdc454: movz            x1, #0x4
    // 0xbdc458: StoreField: r0->field_b = r1
    //     0xbdc458: stur            w1, [x0, #0xb]
    // 0xbdc45c: LeaveFrame
    //     0xbdc45c: mov             SP, fp
    //     0xbdc460: ldp             fp, lr, [SP], #0x10
    // 0xbdc464: ret
    //     0xbdc464: ret             
  }
}
