// lib: , url: package:nuonline/app/modules/donation/controllers/history_tab_controller.dart

// class id: 1050211, size: 0x8
class :: {
}

// class id: 1937, size: 0x40, field offset: 0x34
class HistoryController extends PaginatedFetchController<dynamic> {

  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e80b0, size: 0x1e8
    // 0x7e80b0: EnterFrame
    //     0x7e80b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7e80b4: mov             fp, SP
    // 0x7e80b8: AllocStack(0x40)
    //     0x7e80b8: sub             SP, SP, #0x40
    // 0x7e80bc: SetupParameters(HistoryController this /* r1 => r0, fp-0x10 */)
    //     0x7e80bc: mov             x0, x1
    //     0x7e80c0: stur            x1, [fp, #-0x10]
    // 0x7e80c4: CheckStackOverflow
    //     0x7e80c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e80c8: cmp             SP, x16
    //     0x7e80cc: b.ls            #0x7e8290
    // 0x7e80d0: LoadField: r3 = r0->field_3b
    //     0x7e80d0: ldur            w3, [x0, #0x3b]
    // 0x7e80d4: DecompressPointer r3
    //     0x7e80d4: add             x3, x3, HEAP, lsl #32
    // 0x7e80d8: stur            x3, [fp, #-8]
    // 0x7e80dc: r1 = Null
    //     0x7e80dc: mov             x1, NULL
    // 0x7e80e0: r2 = 12
    //     0x7e80e0: movz            x2, #0xc
    // 0x7e80e4: r0 = AllocateArray()
    //     0x7e80e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e80e8: mov             x2, x0
    // 0x7e80ec: stur            x2, [fp, #-0x18]
    // 0x7e80f0: r16 = "page"
    //     0x7e80f0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7e80f4: ldr             x16, [x16, #0x300]
    // 0x7e80f8: StoreField: r2->field_f = r16
    //     0x7e80f8: stur            w16, [x2, #0xf]
    // 0x7e80fc: ldur            x3, [fp, #-0x10]
    // 0x7e8100: LoadField: r4 = r3->field_23
    //     0x7e8100: ldur            x4, [x3, #0x23]
    // 0x7e8104: r0 = BoxInt64Instr(r4)
    //     0x7e8104: sbfiz           x0, x4, #1, #0x1f
    //     0x7e8108: cmp             x4, x0, asr #1
    //     0x7e810c: b.eq            #0x7e8118
    //     0x7e8110: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7e8114: stur            x4, [x0, #7]
    // 0x7e8118: r1 = 60
    //     0x7e8118: movz            x1, #0x3c
    // 0x7e811c: branchIfSmi(r0, 0x7e8128)
    //     0x7e811c: tbz             w0, #0, #0x7e8128
    // 0x7e8120: r1 = LoadClassIdInstr(r0)
    //     0x7e8120: ldur            x1, [x0, #-1]
    //     0x7e8124: ubfx            x1, x1, #0xc, #0x14
    // 0x7e8128: str             x0, [SP]
    // 0x7e812c: mov             x0, x1
    // 0x7e8130: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x7e8130: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x7e8134: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x7e8134: movz            x17, #0x2b03
    //     0x7e8138: add             lr, x0, x17
    //     0x7e813c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8140: blr             lr
    // 0x7e8144: ldur            x1, [fp, #-0x18]
    // 0x7e8148: ArrayStore: r1[1] = r0  ; List_4
    //     0x7e8148: add             x25, x1, #0x13
    //     0x7e814c: str             w0, [x25]
    //     0x7e8150: tbz             w0, #0, #0x7e816c
    //     0x7e8154: ldurb           w16, [x1, #-1]
    //     0x7e8158: ldurb           w17, [x0, #-1]
    //     0x7e815c: and             x16, x17, x16, lsr #2
    //     0x7e8160: tst             x16, HEAP, lsr #32
    //     0x7e8164: b.eq            #0x7e816c
    //     0x7e8168: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7e816c: ldur            x1, [fp, #-0x18]
    // 0x7e8170: r16 = "limit"
    //     0x7e8170: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0x7e8174: ldr             x16, [x16, #0x50]
    // 0x7e8178: ArrayStore: r1[0] = r16  ; List_4
    //     0x7e8178: stur            w16, [x1, #0x17]
    // 0x7e817c: r16 = 20
    //     0x7e817c: movz            x16, #0x14
    // 0x7e8180: str             x16, [SP]
    // 0x7e8184: r0 = toString()
    //     0x7e8184: bl              #0xc460ec  ; [dart:core] _Smi::toString
    // 0x7e8188: ldur            x1, [fp, #-0x18]
    // 0x7e818c: ArrayStore: r1[3] = r0  ; List_4
    //     0x7e818c: add             x25, x1, #0x1b
    //     0x7e8190: str             w0, [x25]
    //     0x7e8194: tbz             w0, #0, #0x7e81b0
    //     0x7e8198: ldurb           w16, [x1, #-1]
    //     0x7e819c: ldurb           w17, [x0, #-1]
    //     0x7e81a0: and             x16, x17, x16, lsr #2
    //     0x7e81a4: tst             x16, HEAP, lsr #32
    //     0x7e81a8: b.eq            #0x7e81b0
    //     0x7e81ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7e81b0: ldur            x2, [fp, #-0x18]
    // 0x7e81b4: r16 = "status"
    //     0x7e81b4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b130] "status"
    //     0x7e81b8: ldr             x16, [x16, #0x130]
    // 0x7e81bc: StoreField: r2->field_1f = r16
    //     0x7e81bc: stur            w16, [x2, #0x1f]
    // 0x7e81c0: ldur            x3, [fp, #-0x10]
    // 0x7e81c4: LoadField: r0 = r3->field_33
    //     0x7e81c4: ldur            w0, [x3, #0x33]
    // 0x7e81c8: DecompressPointer r0
    //     0x7e81c8: add             x0, x0, HEAP, lsl #32
    // 0x7e81cc: cmp             w0, NULL
    // 0x7e81d0: b.ne            #0x7e81dc
    // 0x7e81d4: r0 = Null
    //     0x7e81d4: mov             x0, NULL
    // 0x7e81d8: b               #0x7e81e8
    // 0x7e81dc: LoadField: r1 = r0->field_f
    //     0x7e81dc: ldur            w1, [x0, #0xf]
    // 0x7e81e0: DecompressPointer r1
    //     0x7e81e0: add             x1, x1, HEAP, lsl #32
    // 0x7e81e4: mov             x0, x1
    // 0x7e81e8: mov             x1, x2
    // 0x7e81ec: ArrayStore: r1[5] = r0  ; List_4
    //     0x7e81ec: add             x25, x1, #0x23
    //     0x7e81f0: str             w0, [x25]
    //     0x7e81f4: tbz             w0, #0, #0x7e8210
    //     0x7e81f8: ldurb           w16, [x1, #-1]
    //     0x7e81fc: ldurb           w17, [x0, #-1]
    //     0x7e8200: and             x16, x17, x16, lsr #2
    //     0x7e8204: tst             x16, HEAP, lsr #32
    //     0x7e8208: b.eq            #0x7e8210
    //     0x7e820c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7e8210: r16 = <String, String?>
    //     0x7e8210: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x7e8214: ldr             x16, [x16, #0x198]
    // 0x7e8218: stp             x2, x16, [SP]
    // 0x7e821c: r0 = Map._fromLiteral()
    //     0x7e821c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e8220: mov             x1, x0
    // 0x7e8224: ldur            x0, [fp, #-0x10]
    // 0x7e8228: stur            x1, [fp, #-0x18]
    // 0x7e822c: LoadField: r2 = r0->field_37
    //     0x7e822c: ldur            w2, [x0, #0x37]
    // 0x7e8230: DecompressPointer r2
    //     0x7e8230: add             x2, x2, HEAP, lsl #32
    // 0x7e8234: tbnz            w2, #4, #0x7e8240
    // 0x7e8238: r0 = "true"
    //     0x7e8238: ldr             x0, [PP, #0x13a0]  ; [pp+0x13a0] "true"
    // 0x7e823c: b               #0x7e8244
    // 0x7e8240: r0 = "false"
    //     0x7e8240: ldr             x0, [PP, #0x13a8]  ; [pp+0x13a8] "false"
    // 0x7e8244: r16 = <String, String?>
    //     0x7e8244: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x7e8248: ldr             x16, [x16, #0x198]
    // 0x7e824c: stp             x1, x16, [SP, #0x18]
    // 0x7e8250: r16 = "is_qurban"
    //     0x7e8250: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b070] "is_qurban"
    //     0x7e8254: ldr             x16, [x16, #0x70]
    // 0x7e8258: stp             x16, x2, [SP, #8]
    // 0x7e825c: str             x0, [SP]
    // 0x7e8260: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x7e8260: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x7e8264: r0 = MapExtension.addIf()
    //     0x7e8264: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0x7e8268: r16 = true
    //     0x7e8268: add             x16, NULL, #0x20  ; true
    // 0x7e826c: str             x16, [SP]
    // 0x7e8270: ldur            x1, [fp, #-8]
    // 0x7e8274: ldur            x2, [fp, #-0x18]
    // 0x7e8278: r4 = const [0, 0x3, 0x1, 0x2, self, 0x2, null]
    //     0x7e8278: add             x4, PP, #0x34, lsl #12  ; [pp+0x34ff8] List(7) [0, 0x3, 0x1, 0x2, "self", 0x2, Null]
    //     0x7e827c: ldr             x4, [x4, #0xff8]
    // 0x7e8280: r0 = findAll()
    //     0x7e8280: bl              #0x72b550  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAll
    // 0x7e8284: LeaveFrame
    //     0x7e8284: mov             SP, fp
    //     0x7e8288: ldp             fp, lr, [SP], #0x10
    // 0x7e828c: ret
    //     0x7e828c: ret             
    // 0x7e8290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8294: b               #0x7e80d0
  }
  _ HistoryController(/* No info */) {
    // ** addr: 0xb3e438, size: 0x14c
    // 0xb3e438: EnterFrame
    //     0xb3e438: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e43c: mov             fp, SP
    // 0xb3e440: AllocStack(0x8)
    //     0xb3e440: sub             SP, SP, #8
    // 0xb3e444: SetupParameters(HistoryController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */, {dynamic isQurban = false /* r5 */, dynamic status = Null /* r0 */})
    //     0xb3e444: stur            x1, [fp, #-8]
    //     0xb3e448: mov             x16, x2
    //     0xb3e44c: mov             x2, x1
    //     0xb3e450: mov             x1, x16
    //     0xb3e454: ldur            w0, [x4, #0x13]
    //     0xb3e458: ldur            w3, [x4, #0x1f]
    //     0xb3e45c: add             x3, x3, HEAP, lsl #32
    //     0xb3e460: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b2e0] "isQurban"
    //     0xb3e464: ldr             x16, [x16, #0x2e0]
    //     0xb3e468: cmp             w3, w16
    //     0xb3e46c: b.ne            #0xb3e490
    //     0xb3e470: ldur            w3, [x4, #0x23]
    //     0xb3e474: add             x3, x3, HEAP, lsl #32
    //     0xb3e478: sub             w5, w0, w3
    //     0xb3e47c: add             x3, fp, w5, sxtw #2
    //     0xb3e480: ldr             x3, [x3, #8]
    //     0xb3e484: mov             x5, x3
    //     0xb3e488: movz            x3, #0x1
    //     0xb3e48c: b               #0xb3e498
    //     0xb3e490: add             x5, NULL, #0x30  ; false
    //     0xb3e494: movz            x3, #0
    //     0xb3e498: lsl             x6, x3, #1
    //     0xb3e49c: lsl             w3, w6, #1
    //     0xb3e4a0: add             w6, w3, #8
    //     0xb3e4a4: add             x16, x4, w6, sxtw #1
    //     0xb3e4a8: ldur            w7, [x16, #0xf]
    //     0xb3e4ac: add             x7, x7, HEAP, lsl #32
    //     0xb3e4b0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b130] "status"
    //     0xb3e4b4: ldr             x16, [x16, #0x130]
    //     0xb3e4b8: cmp             w7, w16
    //     0xb3e4bc: b.ne            #0xb3e4e0
    //     0xb3e4c0: add             w6, w3, #0xa
    //     0xb3e4c4: add             x16, x4, w6, sxtw #1
    //     0xb3e4c8: ldur            w3, [x16, #0xf]
    //     0xb3e4cc: add             x3, x3, HEAP, lsl #32
    //     0xb3e4d0: sub             w4, w0, w3
    //     0xb3e4d4: add             x0, fp, w4, sxtw #2
    //     0xb3e4d8: ldr             x0, [x0, #8]
    //     0xb3e4dc: b               #0xb3e4e4
    //     0xb3e4e0: mov             x0, NULL
    //     0xb3e4e4: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3e4e8: movz            x3, #0x1
    // 0xb3e4e4: r4 = Sentinel
    // 0xb3e4e8: r3 = 1
    // 0xb3e4ec: CheckStackOverflow
    //     0xb3e4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e4f0: cmp             SP, x16
    //     0xb3e4f4: b.ls            #0xb3e57c
    // 0xb3e4f8: StoreField: r2->field_33 = r0
    //     0xb3e4f8: stur            w0, [x2, #0x33]
    //     0xb3e4fc: ldurb           w16, [x2, #-1]
    //     0xb3e500: ldurb           w17, [x0, #-1]
    //     0xb3e504: and             x16, x17, x16, lsr #2
    //     0xb3e508: tst             x16, HEAP, lsr #32
    //     0xb3e50c: b.eq            #0xb3e514
    //     0xb3e510: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb3e514: StoreField: r2->field_37 = r5
    //     0xb3e514: stur            w5, [x2, #0x37]
    // 0xb3e518: mov             x0, x1
    // 0xb3e51c: StoreField: r2->field_3b = r0
    //     0xb3e51c: stur            w0, [x2, #0x3b]
    //     0xb3e520: ldurb           w16, [x2, #-1]
    //     0xb3e524: ldurb           w17, [x0, #-1]
    //     0xb3e528: and             x16, x17, x16, lsr #2
    //     0xb3e52c: tst             x16, HEAP, lsr #32
    //     0xb3e530: b.eq            #0xb3e538
    //     0xb3e534: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb3e538: StoreField: r2->field_23 = r3
    //     0xb3e538: stur            x3, [x2, #0x23]
    // 0xb3e53c: StoreField: r2->field_2b = r4
    //     0xb3e53c: stur            w4, [x2, #0x2b]
    // 0xb3e540: r1 = ""
    //     0xb3e540: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb3e544: r0 = StringExtension.obs()
    //     0xb3e544: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0xb3e548: ldur            x1, [fp, #-8]
    // 0xb3e54c: StoreField: r1->field_2f = r0
    //     0xb3e54c: stur            w0, [x1, #0x2f]
    //     0xb3e550: ldurb           w16, [x1, #-1]
    //     0xb3e554: ldurb           w17, [x0, #-1]
    //     0xb3e558: and             x16, x17, x16, lsr #2
    //     0xb3e55c: tst             x16, HEAP, lsr #32
    //     0xb3e560: b.eq            #0xb3e568
    //     0xb3e564: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb3e568: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb3e568: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb3e56c: r0 = Null
    //     0xb3e56c: mov             x0, NULL
    // 0xb3e570: LeaveFrame
    //     0xb3e570: mov             SP, fp
    //     0xb3e574: ldp             fp, lr, [SP], #0x10
    // 0xb3e578: ret
    //     0xb3e578: ret             
    // 0xb3e57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e57c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e580: b               #0xb3e4f8
  }
}

// class id: 2028, size: 0x30, field offset: 0x24
class HistoryTabController extends _ArticleChannelSettingController&GetxController&GetSingleTickerProviderStateMixin {

  late final TabController tabController; // offset: 0x28

  _ onInit(/* No info */) {
    // ** addr: 0x8bfb1c, size: 0x48
    // 0x8bfb1c: EnterFrame
    //     0x8bfb1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8bfb20: mov             fp, SP
    // 0x8bfb24: AllocStack(0x8)
    //     0x8bfb24: sub             SP, SP, #8
    // 0x8bfb28: SetupParameters(HistoryTabController this /* r1 => r0, fp-0x8 */)
    //     0x8bfb28: mov             x0, x1
    //     0x8bfb2c: stur            x1, [fp, #-8]
    // 0x8bfb30: CheckStackOverflow
    //     0x8bfb30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bfb34: cmp             SP, x16
    //     0x8bfb38: b.ls            #0x8bfb5c
    // 0x8bfb3c: mov             x1, x0
    // 0x8bfb40: r0 = onInit()
    //     0x8bfb40: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8bfb44: ldur            x1, [fp, #-8]
    // 0x8bfb48: r0 = fetchPendingCount()
    //     0x8bfb48: bl              #0x8bfb64  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::fetchPendingCount
    // 0x8bfb4c: r0 = Null
    //     0x8bfb4c: mov             x0, NULL
    // 0x8bfb50: LeaveFrame
    //     0x8bfb50: mov             SP, fp
    //     0x8bfb54: ldp             fp, lr, [SP], #0x10
    // 0x8bfb58: ret
    //     0x8bfb58: ret             
    // 0x8bfb5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bfb5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bfb60: b               #0x8bfb3c
  }
  _ fetchPendingCount(/* No info */) async {
    // ** addr: 0x8bfb64, size: 0xc4
    // 0x8bfb64: EnterFrame
    //     0x8bfb64: stp             fp, lr, [SP, #-0x10]!
    //     0x8bfb68: mov             fp, SP
    // 0x8bfb6c: AllocStack(0x20)
    //     0x8bfb6c: sub             SP, SP, #0x20
    // 0x8bfb70: SetupParameters(HistoryTabController this /* r1 => r1, fp-0x10 */)
    //     0x8bfb70: stur            NULL, [fp, #-8]
    //     0x8bfb74: stur            x1, [fp, #-0x10]
    // 0x8bfb78: CheckStackOverflow
    //     0x8bfb78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bfb7c: cmp             SP, x16
    //     0x8bfb80: b.ls            #0x8bfc20
    // 0x8bfb84: r1 = 1
    //     0x8bfb84: movz            x1, #0x1
    // 0x8bfb88: r0 = AllocateContext()
    //     0x8bfb88: bl              #0xec126c  ; AllocateContextStub
    // 0x8bfb8c: mov             x1, x0
    // 0x8bfb90: ldur            x0, [fp, #-0x10]
    // 0x8bfb94: stur            x1, [fp, #-0x18]
    // 0x8bfb98: StoreField: r1->field_f = r0
    //     0x8bfb98: stur            w0, [x1, #0xf]
    // 0x8bfb9c: InitAsync() -> Future<void?>
    //     0x8bfb9c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8bfba0: bl              #0x661298  ; InitAsyncStub
    // 0x8bfba4: ldur            x2, [fp, #-0x18]
    // 0x8bfba8: r1 = Function '<anonymous closure>':.
    //     0x8bfba8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34fd8] AnonymousClosure: (0x8c0248), in [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::fetchPendingCount (0x8bfb64)
    //     0x8bfbac: ldr             x1, [x1, #0xfd8]
    // 0x8bfbb0: r0 = AllocateClosure()
    //     0x8bfbb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bfbb4: r1 = <List<Transaction>>
    //     0x8bfbb4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b090] TypeArguments: <List<Transaction>>
    //     0x8bfbb8: ldr             x1, [x1, #0x90]
    // 0x8bfbbc: stur            x0, [fp, #-0x10]
    // 0x8bfbc0: r0 = FetchWrapper()
    //     0x8bfbc0: bl              #0x8c01bc  ; AllocateFetchWrapperStub -> FetchWrapper<X0> (size=0x18)
    // 0x8bfbc4: mov             x3, x0
    // 0x8bfbc8: ldur            x0, [fp, #-0x10]
    // 0x8bfbcc: stur            x3, [fp, #-0x20]
    // 0x8bfbd0: StoreField: r3->field_b = r0
    //     0x8bfbd0: stur            w0, [x3, #0xb]
    // 0x8bfbd4: ldur            x2, [fp, #-0x18]
    // 0x8bfbd8: r1 = Function '<anonymous closure>':.
    //     0x8bfbd8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34fe0] AnonymousClosure: (0x8c01c8), in [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::fetchPendingCount (0x8bfb64)
    //     0x8bfbdc: ldr             x1, [x1, #0xfe0]
    // 0x8bfbe0: r0 = AllocateClosure()
    //     0x8bfbe0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bfbe4: mov             x1, x0
    // 0x8bfbe8: ldur            x0, [fp, #-0x20]
    // 0x8bfbec: StoreField: r0->field_f = r1
    //     0x8bfbec: stur            w1, [x0, #0xf]
    // 0x8bfbf0: r1 = Function '<anonymous closure>':.
    //     0x8bfbf0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34fe8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8bfbf4: ldr             x1, [x1, #0xfe8]
    // 0x8bfbf8: r2 = Null
    //     0x8bfbf8: mov             x2, NULL
    // 0x8bfbfc: r0 = AllocateClosure()
    //     0x8bfbfc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bfc00: ldur            x1, [fp, #-0x20]
    // 0x8bfc04: StoreField: r1->field_13 = r0
    //     0x8bfc04: stur            w0, [x1, #0x13]
    // 0x8bfc08: r0 = fetch()
    //     0x8bfc08: bl              #0x8bfc28  ; [package:nuonline/common/mixins/fetch_mixin.dart] _FetchWrapper&Object&FetchMixin::fetch
    // 0x8bfc0c: mov             x1, x0
    // 0x8bfc10: stur            x1, [fp, #-0x10]
    // 0x8bfc14: r0 = Await()
    //     0x8bfc14: bl              #0x661044  ; AwaitStub
    // 0x8bfc18: r0 = Null
    //     0x8bfc18: mov             x0, NULL
    // 0x8bfc1c: r0 = ReturnAsyncNotFuture()
    //     0x8bfc1c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8bfc20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bfc20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bfc24: b               #0x8bfb84
  }
  [closure] void <anonymous closure>(dynamic, List<Transaction>, Pagination?) {
    // ** addr: 0x8c01c8, size: 0x80
    // 0x8c01c8: EnterFrame
    //     0x8c01c8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c01cc: mov             fp, SP
    // 0x8c01d0: AllocStack(0x10)
    //     0x8c01d0: sub             SP, SP, #0x10
    // 0x8c01d4: SetupParameters()
    //     0x8c01d4: ldr             x0, [fp, #0x20]
    //     0x8c01d8: ldur            w1, [x0, #0x17]
    //     0x8c01dc: add             x1, x1, HEAP, lsl #32
    // 0x8c01e0: CheckStackOverflow
    //     0x8c01e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c01e4: cmp             SP, x16
    //     0x8c01e8: b.ls            #0x8c0240
    // 0x8c01ec: LoadField: r0 = r1->field_f
    //     0x8c01ec: ldur            w0, [x1, #0xf]
    // 0x8c01f0: DecompressPointer r0
    //     0x8c01f0: add             x0, x0, HEAP, lsl #32
    // 0x8c01f4: LoadField: r1 = r0->field_2b
    //     0x8c01f4: ldur            w1, [x0, #0x2b]
    // 0x8c01f8: DecompressPointer r1
    //     0x8c01f8: add             x1, x1, HEAP, lsl #32
    // 0x8c01fc: ldr             x0, [fp, #0x18]
    // 0x8c0200: stur            x1, [fp, #-8]
    // 0x8c0204: r2 = LoadClassIdInstr(r0)
    //     0x8c0204: ldur            x2, [x0, #-1]
    //     0x8c0208: ubfx            x2, x2, #0xc, #0x14
    // 0x8c020c: str             x0, [SP]
    // 0x8c0210: mov             x0, x2
    // 0x8c0214: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8c0214: movz            x17, #0xc834
    //     0x8c0218: add             lr, x0, x17
    //     0x8c021c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c0220: blr             lr
    // 0x8c0224: ldur            x1, [fp, #-8]
    // 0x8c0228: mov             x2, x0
    // 0x8c022c: r0 = value=()
    //     0x8c022c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8c0230: r0 = Null
    //     0x8c0230: mov             x0, NULL
    // 0x8c0234: LeaveFrame
    //     0x8c0234: mov             SP, fp
    //     0x8c0238: ldp             fp, lr, [SP], #0x10
    // 0x8c023c: ret
    //     0x8c023c: ret             
    // 0x8c0240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0244: b               #0x8c01ec
  }
  [closure] Future<ApiResult<List<Transaction>>> <anonymous closure>(dynamic) {
    // ** addr: 0x8c0248, size: 0xcc
    // 0x8c0248: EnterFrame
    //     0x8c0248: stp             fp, lr, [SP, #-0x10]!
    //     0x8c024c: mov             fp, SP
    // 0x8c0250: AllocStack(0x18)
    //     0x8c0250: sub             SP, SP, #0x18
    // 0x8c0254: SetupParameters()
    //     0x8c0254: ldr             x0, [fp, #0x10]
    //     0x8c0258: ldur            w1, [x0, #0x17]
    //     0x8c025c: add             x1, x1, HEAP, lsl #32
    // 0x8c0260: CheckStackOverflow
    //     0x8c0260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0264: cmp             SP, x16
    //     0x8c0268: b.ls            #0x8c030c
    // 0x8c026c: LoadField: r0 = r1->field_f
    //     0x8c026c: ldur            w0, [x1, #0xf]
    // 0x8c0270: DecompressPointer r0
    //     0x8c0270: add             x0, x0, HEAP, lsl #32
    // 0x8c0274: LoadField: r3 = r0->field_23
    //     0x8c0274: ldur            w3, [x0, #0x23]
    // 0x8c0278: DecompressPointer r3
    //     0x8c0278: add             x3, x3, HEAP, lsl #32
    // 0x8c027c: stur            x3, [fp, #-8]
    // 0x8c0280: r1 = Null
    //     0x8c0280: mov             x1, NULL
    // 0x8c0284: r2 = 12
    //     0x8c0284: movz            x2, #0xc
    // 0x8c0288: r0 = AllocateArray()
    //     0x8c0288: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c028c: r16 = "page"
    //     0x8c028c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x8c0290: ldr             x16, [x16, #0x300]
    // 0x8c0294: StoreField: r0->field_f = r16
    //     0x8c0294: stur            w16, [x0, #0xf]
    // 0x8c0298: r16 = "1"
    //     0x8c0298: add             x16, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8c029c: ldr             x16, [x16, #0x718]
    // 0x8c02a0: StoreField: r0->field_13 = r16
    //     0x8c02a0: stur            w16, [x0, #0x13]
    // 0x8c02a4: r16 = "limit"
    //     0x8c02a4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0x8c02a8: ldr             x16, [x16, #0x50]
    // 0x8c02ac: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c02ac: stur            w16, [x0, #0x17]
    // 0x8c02b0: r16 = "20"
    //     0x8c02b0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34ff0] "20"
    //     0x8c02b4: ldr             x16, [x16, #0xff0]
    // 0x8c02b8: StoreField: r0->field_1b = r16
    //     0x8c02b8: stur            w16, [x0, #0x1b]
    // 0x8c02bc: r16 = "status"
    //     0x8c02bc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b130] "status"
    //     0x8c02c0: ldr             x16, [x16, #0x130]
    // 0x8c02c4: StoreField: r0->field_1f = r16
    //     0x8c02c4: stur            w16, [x0, #0x1f]
    // 0x8c02c8: r16 = "PENDING"
    //     0x8c02c8: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0x8c02cc: ldr             x16, [x16, #0x2c8]
    // 0x8c02d0: StoreField: r0->field_23 = r16
    //     0x8c02d0: stur            w16, [x0, #0x23]
    // 0x8c02d4: r16 = <String, String?>
    //     0x8c02d4: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x8c02d8: ldr             x16, [x16, #0x198]
    // 0x8c02dc: stp             x0, x16, [SP]
    // 0x8c02e0: r0 = Map._fromLiteral()
    //     0x8c02e0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8c02e4: r16 = true
    //     0x8c02e4: add             x16, NULL, #0x20  ; true
    // 0x8c02e8: str             x16, [SP]
    // 0x8c02ec: ldur            x1, [fp, #-8]
    // 0x8c02f0: mov             x2, x0
    // 0x8c02f4: r4 = const [0, 0x3, 0x1, 0x2, self, 0x2, null]
    //     0x8c02f4: add             x4, PP, #0x34, lsl #12  ; [pp+0x34ff8] List(7) [0, 0x3, 0x1, 0x2, "self", 0x2, Null]
    //     0x8c02f8: ldr             x4, [x4, #0xff8]
    // 0x8c02fc: r0 = findAll()
    //     0x8c02fc: bl              #0x72b550  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAll
    // 0x8c0300: LeaveFrame
    //     0x8c0300: mov             SP, fp
    //     0x8c0304: ldp             fp, lr, [SP], #0x10
    // 0x8c0308: ret
    //     0x8c0308: ret             
    // 0x8c030c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c030c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0310: b               #0x8c026c
  }
  TabController tabController(HistoryTabController) {
    // ** addr: 0x8eae4c, size: 0x48
    // 0x8eae4c: EnterFrame
    //     0x8eae4c: stp             fp, lr, [SP, #-0x10]!
    //     0x8eae50: mov             fp, SP
    // 0x8eae54: AllocStack(0x8)
    //     0x8eae54: sub             SP, SP, #8
    // 0x8eae58: CheckStackOverflow
    //     0x8eae58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eae5c: cmp             SP, x16
    //     0x8eae60: b.ls            #0x8eae8c
    // 0x8eae64: r0 = TabController()
    //     0x8eae64: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8eae68: mov             x1, x0
    // 0x8eae6c: ldr             x3, [fp, #0x10]
    // 0x8eae70: r2 = 3
    //     0x8eae70: movz            x2, #0x3
    // 0x8eae74: stur            x0, [fp, #-8]
    // 0x8eae78: r0 = TabController()
    //     0x8eae78: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8eae7c: ldur            x0, [fp, #-8]
    // 0x8eae80: LeaveFrame
    //     0x8eae80: mov             SP, fp
    //     0x8eae84: ldp             fp, lr, [SP], #0x10
    // 0x8eae88: ret
    //     0x8eae88: ret             
    // 0x8eae8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eae8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eae90: b               #0x8eae64
  }
  _ onClose(/* No info */) {
    // ** addr: 0x926b60, size: 0x54
    // 0x926b60: EnterFrame
    //     0x926b60: stp             fp, lr, [SP, #-0x10]!
    //     0x926b64: mov             fp, SP
    // 0x926b68: CheckStackOverflow
    //     0x926b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926b6c: cmp             SP, x16
    //     0x926b70: b.ls            #0x926bac
    // 0x926b74: LoadField: r0 = r1->field_27
    //     0x926b74: ldur            w0, [x1, #0x27]
    // 0x926b78: DecompressPointer r0
    //     0x926b78: add             x0, x0, HEAP, lsl #32
    // 0x926b7c: r16 = Sentinel
    //     0x926b7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x926b80: cmp             w0, w16
    // 0x926b84: b.ne            #0x926b94
    // 0x926b88: r2 = tabController
    //     0x926b88: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f510] Field <HistoryTabController.tabController>: late final (offset: 0x28)
    //     0x926b8c: ldr             x2, [x2, #0x510]
    // 0x926b90: r0 = InitLateFinalInstanceField()
    //     0x926b90: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x926b94: mov             x1, x0
    // 0x926b98: r0 = dispose()
    //     0x926b98: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x926b9c: r0 = Null
    //     0x926b9c: mov             x0, NULL
    // 0x926ba0: LeaveFrame
    //     0x926ba0: mov             SP, fp
    //     0x926ba4: ldp             fp, lr, [SP], #0x10
    // 0x926ba8: ret
    //     0x926ba8: ret             
    // 0x926bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926bac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926bb0: b               #0x926b74
  }
  static _ find(/* No info */) {
    // ** addr: 0xb8ed34, size: 0x5c
    // 0xb8ed34: EnterFrame
    //     0xb8ed34: stp             fp, lr, [SP, #-0x10]!
    //     0xb8ed38: mov             fp, SP
    // 0xb8ed3c: AllocStack(0x8)
    //     0xb8ed3c: sub             SP, SP, #8
    // 0xb8ed40: CheckStackOverflow
    //     0xb8ed40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ed44: cmp             SP, x16
    //     0xb8ed48: b.ls            #0xb8ed88
    // 0xb8ed4c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8ed4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8ed50: ldr             x0, [x0, #0x2670]
    //     0xb8ed54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8ed58: cmp             w0, w16
    //     0xb8ed5c: b.ne            #0xb8ed68
    //     0xb8ed60: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8ed64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8ed68: r16 = <HistoryTabController>
    //     0xb8ed68: add             x16, PP, #0x24, lsl #12  ; [pp+0x24910] TypeArguments: <HistoryTabController>
    //     0xb8ed6c: ldr             x16, [x16, #0x910]
    // 0xb8ed70: str             x16, [SP]
    // 0xb8ed74: r4 = const [0x1, 0, 0, 0, null]
    //     0xb8ed74: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xb8ed78: r0 = Inst.find()
    //     0xb8ed78: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8ed7c: LeaveFrame
    //     0xb8ed7c: mov             SP, fp
    //     0xb8ed80: ldp             fp, lr, [SP], #0x10
    // 0xb8ed84: ret
    //     0xb8ed84: ret             
    // 0xb8ed88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ed88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8ed8c: b               #0xb8ed4c
  }
  [closure] Future<void> onPageRefresh(dynamic) {
    // ** addr: 0xb8ed90, size: 0x38
    // 0xb8ed90: EnterFrame
    //     0xb8ed90: stp             fp, lr, [SP, #-0x10]!
    //     0xb8ed94: mov             fp, SP
    // 0xb8ed98: ldr             x0, [fp, #0x10]
    // 0xb8ed9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8ed9c: ldur            w1, [x0, #0x17]
    // 0xb8eda0: DecompressPointer r1
    //     0xb8eda0: add             x1, x1, HEAP, lsl #32
    // 0xb8eda4: CheckStackOverflow
    //     0xb8eda4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8eda8: cmp             SP, x16
    //     0xb8edac: b.ls            #0xb8edc0
    // 0xb8edb0: r0 = onPageRefresh()
    //     0xb8edb0: bl              #0xb8edc8  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::onPageRefresh
    // 0xb8edb4: LeaveFrame
    //     0xb8edb4: mov             SP, fp
    //     0xb8edb8: ldp             fp, lr, [SP], #0x10
    // 0xb8edbc: ret
    //     0xb8edbc: ret             
    // 0xb8edc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8edc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8edc4: b               #0xb8edb0
  }
  _ onPageRefresh(/* No info */) async {
    // ** addr: 0xb8edc8, size: 0x158
    // 0xb8edc8: EnterFrame
    //     0xb8edc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8edcc: mov             fp, SP
    // 0xb8edd0: AllocStack(0x20)
    //     0xb8edd0: sub             SP, SP, #0x20
    // 0xb8edd4: SetupParameters(HistoryTabController this /* r1 => r1, fp-0x10 */)
    //     0xb8edd4: stur            NULL, [fp, #-8]
    //     0xb8edd8: stur            x1, [fp, #-0x10]
    // 0xb8eddc: CheckStackOverflow
    //     0xb8eddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ede0: cmp             SP, x16
    //     0xb8ede4: b.ls            #0xb8ef18
    // 0xb8ede8: InitAsync() -> Future<void?>
    //     0xb8ede8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb8edec: bl              #0x661298  ; InitAsyncStub
    // 0xb8edf0: ldur            x1, [fp, #-0x10]
    // 0xb8edf4: r0 = fetchPendingCount()
    //     0xb8edf4: bl              #0x8bfb64  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::fetchPendingCount
    // 0xb8edf8: mov             x1, x0
    // 0xb8edfc: stur            x1, [fp, #-0x10]
    // 0xb8ee00: r0 = Await()
    //     0xb8ee00: bl              #0x661044  ; AwaitStub
    // 0xb8ee04: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8ee04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8ee08: ldr             x0, [x0, #0x2670]
    //     0xb8ee0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8ee10: cmp             w0, w16
    //     0xb8ee14: b.ne            #0xb8ee20
    //     0xb8ee18: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8ee1c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8ee20: r16 = <HistoryController>
    //     0xb8ee20: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8ee24: ldr             x16, [x16, #0xf28]
    // 0xb8ee28: r30 = "SUCCESS"
    //     0xb8ee28: add             lr, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8ee2c: ldr             lr, [lr, #0x2c0]
    // 0xb8ee30: stp             lr, x16, [SP]
    // 0xb8ee34: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8ee34: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8ee38: r0 = Inst.isRegistered()
    //     0xb8ee38: bl              #0x831a04  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.isRegistered
    // 0xb8ee3c: tbnz            w0, #4, #0xb8ee70
    // 0xb8ee40: r16 = <HistoryController>
    //     0xb8ee40: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8ee44: ldr             x16, [x16, #0xf28]
    // 0xb8ee48: r30 = "SUCCESS"
    //     0xb8ee48: add             lr, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8ee4c: ldr             lr, [lr, #0x2c0]
    // 0xb8ee50: stp             lr, x16, [SP]
    // 0xb8ee54: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8ee54: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8ee58: r0 = Inst.find()
    //     0xb8ee58: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8ee5c: mov             x1, x0
    // 0xb8ee60: r0 = onRefresh()
    //     0xb8ee60: bl              #0xadfeac  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onRefresh
    // 0xb8ee64: mov             x1, x0
    // 0xb8ee68: stur            x1, [fp, #-0x10]
    // 0xb8ee6c: r0 = Await()
    //     0xb8ee6c: bl              #0x661044  ; AwaitStub
    // 0xb8ee70: r16 = <HistoryController>
    //     0xb8ee70: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8ee74: ldr             x16, [x16, #0xf28]
    // 0xb8ee78: r30 = "PENDING"
    //     0xb8ee78: add             lr, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8ee7c: ldr             lr, [lr, #0x2c8]
    // 0xb8ee80: stp             lr, x16, [SP]
    // 0xb8ee84: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8ee84: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8ee88: r0 = Inst.isRegistered()
    //     0xb8ee88: bl              #0x831a04  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.isRegistered
    // 0xb8ee8c: tbnz            w0, #4, #0xb8eec0
    // 0xb8ee90: r16 = <HistoryController>
    //     0xb8ee90: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8ee94: ldr             x16, [x16, #0xf28]
    // 0xb8ee98: r30 = "PENDING"
    //     0xb8ee98: add             lr, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8ee9c: ldr             lr, [lr, #0x2c8]
    // 0xb8eea0: stp             lr, x16, [SP]
    // 0xb8eea4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8eea4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8eea8: r0 = Inst.find()
    //     0xb8eea8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8eeac: mov             x1, x0
    // 0xb8eeb0: r0 = onRefresh()
    //     0xb8eeb0: bl              #0xadfeac  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onRefresh
    // 0xb8eeb4: mov             x1, x0
    // 0xb8eeb8: stur            x1, [fp, #-0x10]
    // 0xb8eebc: r0 = Await()
    //     0xb8eebc: bl              #0x661044  ; AwaitStub
    // 0xb8eec0: r16 = <HistoryController>
    //     0xb8eec0: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8eec4: ldr             x16, [x16, #0xf28]
    // 0xb8eec8: r30 = "EXPIRED"
    //     0xb8eec8: add             lr, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8eecc: ldr             lr, [lr, #0xf88]
    // 0xb8eed0: stp             lr, x16, [SP]
    // 0xb8eed4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8eed4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8eed8: r0 = Inst.isRegistered()
    //     0xb8eed8: bl              #0x831a04  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.isRegistered
    // 0xb8eedc: tbnz            w0, #4, #0xb8ef10
    // 0xb8eee0: r16 = <HistoryController>
    //     0xb8eee0: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8eee4: ldr             x16, [x16, #0xf28]
    // 0xb8eee8: r30 = "EXPIRED"
    //     0xb8eee8: add             lr, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8eeec: ldr             lr, [lr, #0xf88]
    // 0xb8eef0: stp             lr, x16, [SP]
    // 0xb8eef4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8eef4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8eef8: r0 = Inst.find()
    //     0xb8eef8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8eefc: mov             x1, x0
    // 0xb8ef00: r0 = onRefresh()
    //     0xb8ef00: bl              #0xadfeac  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onRefresh
    // 0xb8ef04: mov             x1, x0
    // 0xb8ef08: stur            x1, [fp, #-0x10]
    // 0xb8ef0c: r0 = Await()
    //     0xb8ef0c: bl              #0x661044  ; AwaitStub
    // 0xb8ef10: r0 = Null
    //     0xb8ef10: mov             x0, NULL
    // 0xb8ef14: r0 = ReturnAsyncNotFuture()
    //     0xb8ef14: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb8ef18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ef18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8ef1c: b               #0xb8ede8
  }
}
