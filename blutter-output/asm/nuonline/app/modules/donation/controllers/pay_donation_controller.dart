// lib: , url: package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart

// class id: 1050212, size: 0x8
class :: {
}

// class id: 1901, size: 0x50, field offset: 0x20
class PayDonationController extends GetxController {

  late final DonationFormController form; // offset: 0x40

  _ PayDonationController(/* No info */) {
    // ** addr: 0x8116c0, size: 0x224
    // 0x8116c0: EnterFrame
    //     0x8116c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8116c4: mov             fp, SP
    // 0x8116c8: AllocStack(0x48)
    //     0x8116c8: sub             SP, SP, #0x48
    // 0x8116cc: r0 = Sentinel
    //     0x8116cc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8116d0: mov             x4, x3
    // 0x8116d4: stur            x3, [fp, #-0x18]
    // 0x8116d8: mov             x3, x5
    // 0x8116dc: stur            x5, [fp, #-0x20]
    // 0x8116e0: mov             x5, x2
    // 0x8116e4: stur            x2, [fp, #-0x10]
    // 0x8116e8: mov             x2, x6
    // 0x8116ec: stur            x6, [fp, #-0x28]
    // 0x8116f0: mov             x6, x1
    // 0x8116f4: stur            x1, [fp, #-8]
    // 0x8116f8: mov             x1, x7
    // 0x8116fc: stur            x7, [fp, #-0x30]
    // 0x811700: CheckStackOverflow
    //     0x811700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811704: cmp             SP, x16
    //     0x811708: b.ls            #0x8118dc
    // 0x81170c: StoreField: r6->field_3f = r0
    //     0x81170c: stur            w0, [x6, #0x3f]
    // 0x811710: r16 = <PaymentMethod>
    //     0x811710: add             x16, PP, #0x36, lsl #12  ; [pp+0x36138] TypeArguments: <PaymentMethod>
    //     0x811714: ldr             x16, [x16, #0x138]
    // 0x811718: r30 = Instance_PaymentMethod
    //     0x811718: add             lr, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0x81171c: ldr             lr, [lr, #0x220]
    // 0x811720: stp             lr, x16, [SP]
    // 0x811724: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811724: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811728: r0 = RxT.obs()
    //     0x811728: bl              #0x80eeb8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x81172c: ldur            x2, [fp, #-8]
    // 0x811730: StoreField: r2->field_33 = r0
    //     0x811730: stur            w0, [x2, #0x33]
    //     0x811734: ldurb           w16, [x2, #-1]
    //     0x811738: ldurb           w17, [x0, #-1]
    //     0x81173c: and             x16, x17, x16, lsr #2
    //     0x811740: tst             x16, HEAP, lsr #32
    //     0x811744: b.eq            #0x81174c
    //     0x811748: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81174c: r1 = <FormState>
    //     0x81174c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36140] TypeArguments: <FormState>
    //     0x811750: ldr             x1, [x1, #0x140]
    // 0x811754: r0 = LabeledGlobalKey()
    //     0x811754: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x811758: ldur            x2, [fp, #-8]
    // 0x81175c: StoreField: r2->field_37 = r0
    //     0x81175c: stur            w0, [x2, #0x37]
    //     0x811760: ldurb           w16, [x2, #-1]
    //     0x811764: ldurb           w17, [x0, #-1]
    //     0x811768: and             x16, x17, x16, lsr #2
    //     0x81176c: tst             x16, HEAP, lsr #32
    //     0x811770: b.eq            #0x811778
    //     0x811774: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x811778: r1 = false
    //     0x811778: add             x1, NULL, #0x30  ; false
    // 0x81177c: r0 = BoolExtension.obs()
    //     0x81177c: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x811780: ldur            x2, [fp, #-8]
    // 0x811784: StoreField: r2->field_3b = r0
    //     0x811784: stur            w0, [x2, #0x3b]
    //     0x811788: ldurb           w16, [x2, #-1]
    //     0x81178c: ldurb           w17, [x0, #-1]
    //     0x811790: and             x16, x17, x16, lsr #2
    //     0x811794: tst             x16, HEAP, lsr #32
    //     0x811798: b.eq            #0x8117a0
    //     0x81179c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8117a0: r1 = ""
    //     0x8117a0: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8117a4: r0 = StringExtension.obs()
    //     0x8117a4: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x8117a8: ldur            x2, [fp, #-8]
    // 0x8117ac: StoreField: r2->field_43 = r0
    //     0x8117ac: stur            w0, [x2, #0x43]
    //     0x8117b0: ldurb           w16, [x2, #-1]
    //     0x8117b4: ldurb           w17, [x0, #-1]
    //     0x8117b8: and             x16, x17, x16, lsr #2
    //     0x8117bc: tst             x16, HEAP, lsr #32
    //     0x8117c0: b.eq            #0x8117c8
    //     0x8117c4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8117c8: r1 = ""
    //     0x8117c8: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8117cc: r0 = StringExtension.obs()
    //     0x8117cc: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x8117d0: ldur            x1, [fp, #-8]
    // 0x8117d4: StoreField: r1->field_47 = r0
    //     0x8117d4: stur            w0, [x1, #0x47]
    //     0x8117d8: ldurb           w16, [x1, #-1]
    //     0x8117dc: ldurb           w17, [x0, #-1]
    //     0x8117e0: and             x16, x17, x16, lsr #2
    //     0x8117e4: tst             x16, HEAP, lsr #32
    //     0x8117e8: b.eq            #0x8117f0
    //     0x8117ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8117f0: r0 = FocusNode()
    //     0x8117f0: bl              #0x811904  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0x8117f4: mov             x1, x0
    // 0x8117f8: stur            x0, [fp, #-0x38]
    // 0x8117fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8117fc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x811800: r0 = FocusNode()
    //     0x811800: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0x811804: ldur            x0, [fp, #-0x38]
    // 0x811808: ldur            x1, [fp, #-8]
    // 0x81180c: StoreField: r1->field_4b = r0
    //     0x81180c: stur            w0, [x1, #0x4b]
    //     0x811810: ldurb           w16, [x1, #-1]
    //     0x811814: ldurb           w17, [x0, #-1]
    //     0x811818: and             x16, x17, x16, lsr #2
    //     0x81181c: tst             x16, HEAP, lsr #32
    //     0x811820: b.eq            #0x811828
    //     0x811824: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811828: ldur            x0, [fp, #-0x28]
    // 0x81182c: StoreField: r1->field_1f = r0
    //     0x81182c: stur            w0, [x1, #0x1f]
    //     0x811830: ldurb           w16, [x1, #-1]
    //     0x811834: ldurb           w17, [x0, #-1]
    //     0x811838: and             x16, x17, x16, lsr #2
    //     0x81183c: tst             x16, HEAP, lsr #32
    //     0x811840: b.eq            #0x811848
    //     0x811844: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811848: ldur            x0, [fp, #-0x30]
    // 0x81184c: StoreField: r1->field_23 = r0
    //     0x81184c: stur            w0, [x1, #0x23]
    //     0x811850: ldurb           w16, [x1, #-1]
    //     0x811854: ldurb           w17, [x0, #-1]
    //     0x811858: and             x16, x17, x16, lsr #2
    //     0x81185c: tst             x16, HEAP, lsr #32
    //     0x811860: b.eq            #0x811868
    //     0x811864: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811868: ldur            x0, [fp, #-0x10]
    // 0x81186c: StoreField: r1->field_27 = r0
    //     0x81186c: stur            w0, [x1, #0x27]
    //     0x811870: ldurb           w16, [x1, #-1]
    //     0x811874: ldurb           w17, [x0, #-1]
    //     0x811878: and             x16, x17, x16, lsr #2
    //     0x81187c: tst             x16, HEAP, lsr #32
    //     0x811880: b.eq            #0x811888
    //     0x811884: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811888: ldur            x0, [fp, #-0x18]
    // 0x81188c: StoreField: r1->field_2b = r0
    //     0x81188c: stur            w0, [x1, #0x2b]
    //     0x811890: ldurb           w16, [x1, #-1]
    //     0x811894: ldurb           w17, [x0, #-1]
    //     0x811898: and             x16, x17, x16, lsr #2
    //     0x81189c: tst             x16, HEAP, lsr #32
    //     0x8118a0: b.eq            #0x8118a8
    //     0x8118a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8118a8: ldur            x0, [fp, #-0x20]
    // 0x8118ac: StoreField: r1->field_2f = r0
    //     0x8118ac: stur            w0, [x1, #0x2f]
    //     0x8118b0: ldurb           w16, [x1, #-1]
    //     0x8118b4: ldurb           w17, [x0, #-1]
    //     0x8118b8: and             x16, x17, x16, lsr #2
    //     0x8118bc: tst             x16, HEAP, lsr #32
    //     0x8118c0: b.eq            #0x8118c8
    //     0x8118c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8118c8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8118c8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8118cc: r0 = Null
    //     0x8118cc: mov             x0, NULL
    // 0x8118d0: LeaveFrame
    //     0x8118d0: mov             SP, fp
    //     0x8118d4: ldp             fp, lr, [SP], #0x10
    // 0x8118d8: ret
    //     0x8118d8: ret             
    // 0x8118dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8118dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8118e0: b               #0x81170c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f8230, size: 0x160
    // 0x8f8230: EnterFrame
    //     0x8f8230: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8234: mov             fp, SP
    // 0x8f8238: AllocStack(0x18)
    //     0x8f8238: sub             SP, SP, #0x18
    // 0x8f823c: SetupParameters(PayDonationController this /* r1 => r1, fp-0x8 */)
    //     0x8f823c: stur            x1, [fp, #-8]
    // 0x8f8240: CheckStackOverflow
    //     0x8f8240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8244: cmp             SP, x16
    //     0x8f8248: b.ls            #0x8f8388
    // 0x8f824c: r1 = 1
    //     0x8f824c: movz            x1, #0x1
    // 0x8f8250: r0 = AllocateContext()
    //     0x8f8250: bl              #0xec126c  ; AllocateContextStub
    // 0x8f8254: mov             x2, x0
    // 0x8f8258: ldur            x0, [fp, #-8]
    // 0x8f825c: stur            x2, [fp, #-0x10]
    // 0x8f8260: StoreField: r2->field_f = r0
    //     0x8f8260: stur            w0, [x2, #0xf]
    // 0x8f8264: mov             x1, x0
    // 0x8f8268: r0 = onInit()
    //     0x8f8268: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f826c: ldur            x0, [fp, #-8]
    // 0x8f8270: LoadField: r1 = r0->field_2b
    //     0x8f8270: ldur            w1, [x0, #0x2b]
    // 0x8f8274: DecompressPointer r1
    //     0x8f8274: add             x1, x1, HEAP, lsl #32
    // 0x8f8278: LoadField: r2 = r0->field_1f
    //     0x8f8278: ldur            w2, [x0, #0x1f]
    // 0x8f827c: DecompressPointer r2
    //     0x8f827c: add             x2, x2, HEAP, lsl #32
    // 0x8f8280: r0 = getDonor()
    //     0x8f8280: bl              #0x8f8390  ; [package:nuonline/app/data/repositories/donor_repository.dart] DonorRepository::getDonor
    // 0x8f8284: stur            x0, [fp, #-0x18]
    // 0x8f8288: cmp             w0, NULL
    // 0x8f828c: b.eq            #0x8f8328
    // 0x8f8290: ldur            x2, [fp, #-8]
    // 0x8f8294: mov             x1, x2
    // 0x8f8298: LoadField: r0 = r1->field_3f
    //     0x8f8298: ldur            w0, [x1, #0x3f]
    // 0x8f829c: DecompressPointer r0
    //     0x8f829c: add             x0, x0, HEAP, lsl #32
    // 0x8f82a0: r16 = Sentinel
    //     0x8f82a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f82a4: cmp             w0, w16
    // 0x8f82a8: b.ne            #0x8f82b8
    // 0x8f82ac: r2 = form
    //     0x8f82ac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0x8f82b0: ldr             x2, [x2, #0xc60]
    // 0x8f82b4: r0 = InitLateFinalInstanceField()
    //     0x8f82b4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x8f82b8: LoadField: r1 = r0->field_27
    //     0x8f82b8: ldur            w1, [x0, #0x27]
    // 0x8f82bc: DecompressPointer r1
    //     0x8f82bc: add             x1, x1, HEAP, lsl #32
    // 0x8f82c0: ldur            x0, [fp, #-0x18]
    // 0x8f82c4: LoadField: r2 = r0->field_7
    //     0x8f82c4: ldur            w2, [x0, #7]
    // 0x8f82c8: DecompressPointer r2
    //     0x8f82c8: add             x2, x2, HEAP, lsl #32
    // 0x8f82cc: r0 = value=()
    //     0x8f82cc: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f82d0: ldur            x0, [fp, #-8]
    // 0x8f82d4: LoadField: r1 = r0->field_43
    //     0x8f82d4: ldur            w1, [x0, #0x43]
    // 0x8f82d8: DecompressPointer r1
    //     0x8f82d8: add             x1, x1, HEAP, lsl #32
    // 0x8f82dc: ldur            x3, [fp, #-0x18]
    // 0x8f82e0: LoadField: r2 = r3->field_b
    //     0x8f82e0: ldur            w2, [x3, #0xb]
    // 0x8f82e4: DecompressPointer r2
    //     0x8f82e4: add             x2, x2, HEAP, lsl #32
    // 0x8f82e8: cmp             w2, NULL
    // 0x8f82ec: b.ne            #0x8f82f4
    // 0x8f82f0: r2 = ""
    //     0x8f82f0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f82f4: r0 = value=()
    //     0x8f82f4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f82f8: ldur            x0, [fp, #-8]
    // 0x8f82fc: LoadField: r1 = r0->field_47
    //     0x8f82fc: ldur            w1, [x0, #0x47]
    // 0x8f8300: DecompressPointer r1
    //     0x8f8300: add             x1, x1, HEAP, lsl #32
    // 0x8f8304: ldur            x2, [fp, #-0x18]
    // 0x8f8308: LoadField: r3 = r2->field_f
    //     0x8f8308: ldur            w3, [x2, #0xf]
    // 0x8f830c: DecompressPointer r3
    //     0x8f830c: add             x3, x3, HEAP, lsl #32
    // 0x8f8310: cmp             w3, NULL
    // 0x8f8314: b.ne            #0x8f8320
    // 0x8f8318: r2 = ""
    //     0x8f8318: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f831c: b               #0x8f8324
    // 0x8f8320: mov             x2, x3
    // 0x8f8324: r0 = value=()
    //     0x8f8324: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f8328: ldur            x1, [fp, #-8]
    // 0x8f832c: LoadField: r0 = r1->field_3f
    //     0x8f832c: ldur            w0, [x1, #0x3f]
    // 0x8f8330: DecompressPointer r0
    //     0x8f8330: add             x0, x0, HEAP, lsl #32
    // 0x8f8334: r16 = Sentinel
    //     0x8f8334: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f8338: cmp             w0, w16
    // 0x8f833c: b.ne            #0x8f834c
    // 0x8f8340: r2 = form
    //     0x8f8340: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0x8f8344: ldr             x2, [x2, #0xc60]
    // 0x8f8348: r0 = InitLateFinalInstanceField()
    //     0x8f8348: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x8f834c: LoadField: r3 = r0->field_23
    //     0x8f834c: ldur            w3, [x0, #0x23]
    // 0x8f8350: DecompressPointer r3
    //     0x8f8350: add             x3, x3, HEAP, lsl #32
    // 0x8f8354: ldur            x2, [fp, #-0x10]
    // 0x8f8358: stur            x3, [fp, #-8]
    // 0x8f835c: r1 = Function '<anonymous closure>':.
    //     0x8f835c: add             x1, PP, #0x40, lsl #12  ; [pp+0x404b8] AnonymousClosure: (0x8f84d0), in [package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart] PayDonationController::onInit (0x8f8230)
    //     0x8f8360: ldr             x1, [x1, #0x4b8]
    // 0x8f8364: r0 = AllocateClosure()
    //     0x8f8364: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f8368: ldur            x1, [fp, #-8]
    // 0x8f836c: mov             x2, x0
    // 0x8f8370: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f8370: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f8374: r0 = listen()
    //     0x8f8374: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8f8378: r0 = Null
    //     0x8f8378: mov             x0, NULL
    // 0x8f837c: LeaveFrame
    //     0x8f837c: mov             SP, fp
    //     0x8f8380: ldp             fp, lr, [SP], #0x10
    // 0x8f8384: ret
    //     0x8f8384: ret             
    // 0x8f8388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f838c: b               #0x8f824c
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x8f84d0, size: 0xec
    // 0x8f84d0: EnterFrame
    //     0x8f84d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f84d4: mov             fp, SP
    // 0x8f84d8: AllocStack(0x8)
    //     0x8f84d8: sub             SP, SP, #8
    // 0x8f84dc: SetupParameters()
    //     0x8f84dc: ldr             x0, [fp, #0x18]
    //     0x8f84e0: ldur            w3, [x0, #0x17]
    //     0x8f84e4: add             x3, x3, HEAP, lsl #32
    //     0x8f84e8: stur            x3, [fp, #-8]
    // 0x8f84ec: CheckStackOverflow
    //     0x8f84ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f84f0: cmp             SP, x16
    //     0x8f84f4: b.ls            #0x8f85b4
    // 0x8f84f8: ldr             x0, [fp, #0x10]
    // 0x8f84fc: r1 = LoadInt32Instr(r0)
    //     0x8f84fc: sbfx            x1, x0, #1, #0x1f
    //     0x8f8500: tbz             w0, #0, #0x8f8508
    //     0x8f8504: ldur            x1, [x0, #7]
    // 0x8f8508: r17 = 10000000
    //     0x8f8508: movz            x17, #0x9680
    //     0x8f850c: movk            x17, #0x98, lsl #16
    // 0x8f8510: cmp             x1, x17
    // 0x8f8514: b.le            #0x8f8584
    // 0x8f8518: LoadField: r0 = r3->field_f
    //     0x8f8518: ldur            w0, [x3, #0xf]
    // 0x8f851c: DecompressPointer r0
    //     0x8f851c: add             x0, x0, HEAP, lsl #32
    // 0x8f8520: LoadField: r1 = r0->field_3b
    //     0x8f8520: ldur            w1, [x0, #0x3b]
    // 0x8f8524: DecompressPointer r1
    //     0x8f8524: add             x1, x1, HEAP, lsl #32
    // 0x8f8528: r2 = true
    //     0x8f8528: add             x2, NULL, #0x20  ; true
    // 0x8f852c: r0 = value=()
    //     0x8f852c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f8530: ldur            x0, [fp, #-8]
    // 0x8f8534: LoadField: r1 = r0->field_f
    //     0x8f8534: ldur            w1, [x0, #0xf]
    // 0x8f8538: DecompressPointer r1
    //     0x8f8538: add             x1, x1, HEAP, lsl #32
    // 0x8f853c: LoadField: r2 = r1->field_33
    //     0x8f853c: ldur            w2, [x1, #0x33]
    // 0x8f8540: DecompressPointer r2
    //     0x8f8540: add             x2, x2, HEAP, lsl #32
    // 0x8f8544: mov             x1, x2
    // 0x8f8548: r0 = value()
    //     0x8f8548: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f854c: r16 = Instance_PaymentMethod
    //     0x8f854c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0x8f8550: ldr             x16, [x16, #0x220]
    // 0x8f8554: cmp             w0, w16
    // 0x8f8558: b.ne            #0x8f85a4
    // 0x8f855c: ldur            x0, [fp, #-8]
    // 0x8f8560: LoadField: r1 = r0->field_f
    //     0x8f8560: ldur            w1, [x0, #0xf]
    // 0x8f8564: DecompressPointer r1
    //     0x8f8564: add             x1, x1, HEAP, lsl #32
    // 0x8f8568: LoadField: r0 = r1->field_33
    //     0x8f8568: ldur            w0, [x1, #0x33]
    // 0x8f856c: DecompressPointer r0
    //     0x8f856c: add             x0, x0, HEAP, lsl #32
    // 0x8f8570: mov             x1, x0
    // 0x8f8574: r2 = Instance_PaymentMethod
    //     0x8f8574: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b230] Obj!PaymentMethod@e30dc1
    //     0x8f8578: ldr             x2, [x2, #0x230]
    // 0x8f857c: r0 = value=()
    //     0x8f857c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f8580: b               #0x8f85a4
    // 0x8f8584: mov             x0, x3
    // 0x8f8588: LoadField: r1 = r0->field_f
    //     0x8f8588: ldur            w1, [x0, #0xf]
    // 0x8f858c: DecompressPointer r1
    //     0x8f858c: add             x1, x1, HEAP, lsl #32
    // 0x8f8590: LoadField: r0 = r1->field_3b
    //     0x8f8590: ldur            w0, [x1, #0x3b]
    // 0x8f8594: DecompressPointer r0
    //     0x8f8594: add             x0, x0, HEAP, lsl #32
    // 0x8f8598: mov             x1, x0
    // 0x8f859c: r2 = false
    //     0x8f859c: add             x2, NULL, #0x30  ; false
    // 0x8f85a0: r0 = value=()
    //     0x8f85a0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f85a4: r0 = Null
    //     0x8f85a4: mov             x0, NULL
    // 0x8f85a8: LeaveFrame
    //     0x8f85a8: mov             SP, fp
    //     0x8f85ac: ldp             fp, lr, [SP], #0x10
    // 0x8f85b0: ret
    //     0x8f85b0: ret             
    // 0x8f85b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f85b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f85b8: b               #0x8f84f8
  }
  DonationFormController form(PayDonationController) {
    // ** addr: 0x8f85bc, size: 0x2ac
    // 0x8f85bc: EnterFrame
    //     0x8f85bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f85c0: mov             fp, SP
    // 0x8f85c4: AllocStack(0x30)
    //     0x8f85c4: sub             SP, SP, #0x30
    // 0x8f85c8: CheckStackOverflow
    //     0x8f85c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f85cc: cmp             SP, x16
    //     0x8f85d0: b.ls            #0x8f8860
    // 0x8f85d4: ldr             x0, [fp, #0x10]
    // 0x8f85d8: LoadField: r1 = r0->field_1f
    //     0x8f85d8: ldur            w1, [x0, #0x1f]
    // 0x8f85dc: DecompressPointer r1
    //     0x8f85dc: add             x1, x1, HEAP, lsl #32
    // 0x8f85e0: r16 = Instance_PaymentType
    //     0x8f85e0: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0x8f85e4: ldr             x16, [x16, #0x5a8]
    // 0x8f85e8: cmp             w1, w16
    // 0x8f85ec: b.ne            #0x8f8648
    // 0x8f85f0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f85f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f85f4: ldr             x0, [x0, #0x2670]
    //     0x8f85f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f85fc: cmp             w0, w16
    //     0x8f8600: b.ne            #0x8f860c
    //     0x8f8604: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f8608: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f860c: r16 = <RemoteConfigService>
    //     0x8f860c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8f8610: ldr             x16, [x16, #0xae8]
    // 0x8f8614: r30 = "remote_config_service"
    //     0x8f8614: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8f8618: ldr             lr, [lr, #0xaf0]
    // 0x8f861c: stp             lr, x16, [SP]
    // 0x8f8620: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8f8620: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8f8624: r0 = Inst.find()
    //     0x8f8624: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8f8628: stur            x0, [fp, #-8]
    // 0x8f862c: r0 = DonationFormController()
    //     0x8f862c: bl              #0x8f9090  ; AllocateDonationFormControllerStub -> DonationFormController (size=0x40)
    // 0x8f8630: mov             x1, x0
    // 0x8f8634: ldur            x2, [fp, #-8]
    // 0x8f8638: stur            x0, [fp, #-8]
    // 0x8f863c: r0 = DonationFormController()
    //     0x8f863c: bl              #0x8f8f2c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::DonationFormController
    // 0x8f8640: ldur            x0, [fp, #-8]
    // 0x8f8644: b               #0x8f8854
    // 0x8f8648: r16 = Instance_PaymentType
    //     0x8f8648: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0x8f864c: ldr             x16, [x16, #0x5c0]
    // 0x8f8650: cmp             w1, w16
    // 0x8f8654: b.ne            #0x8f86e4
    // 0x8f8658: LoadField: r2 = r0->field_37
    //     0x8f8658: ldur            w2, [x0, #0x37]
    // 0x8f865c: DecompressPointer r2
    //     0x8f865c: add             x2, x2, HEAP, lsl #32
    // 0x8f8660: stur            x2, [fp, #-8]
    // 0x8f8664: r0 = find()
    //     0x8f8664: bl              #0x813400  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::find
    // 0x8f8668: stur            x0, [fp, #-0x10]
    // 0x8f866c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f866c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f8670: ldr             x0, [x0, #0x2670]
    //     0x8f8674: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f8678: cmp             w0, w16
    //     0x8f867c: b.ne            #0x8f8688
    //     0x8f8680: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f8684: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f8688: r16 = <RemoteConfigService>
    //     0x8f8688: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8f868c: ldr             x16, [x16, #0xae8]
    // 0x8f8690: r30 = "remote_config_service"
    //     0x8f8690: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8f8694: ldr             lr, [lr, #0xaf0]
    // 0x8f8698: stp             lr, x16, [SP]
    // 0x8f869c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8f869c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8f86a0: r0 = Inst.find()
    //     0x8f86a0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8f86a4: mov             x1, x0
    // 0x8f86a8: ldr             x0, [fp, #0x10]
    // 0x8f86ac: stur            x1, [fp, #-0x20]
    // 0x8f86b0: LoadField: r6 = r0->field_23
    //     0x8f86b0: ldur            w6, [x0, #0x23]
    // 0x8f86b4: DecompressPointer r6
    //     0x8f86b4: add             x6, x6, HEAP, lsl #32
    // 0x8f86b8: stur            x6, [fp, #-0x18]
    // 0x8f86bc: r0 = ZakatFormController()
    //     0x8f86bc: bl              #0x8f8f20  ; AllocateZakatFormControllerStub -> ZakatFormController (size=0x64)
    // 0x8f86c0: mov             x1, x0
    // 0x8f86c4: ldur            x2, [fp, #-8]
    // 0x8f86c8: ldur            x3, [fp, #-0x20]
    // 0x8f86cc: ldur            x5, [fp, #-0x10]
    // 0x8f86d0: ldur            x6, [fp, #-0x18]
    // 0x8f86d4: stur            x0, [fp, #-8]
    // 0x8f86d8: r0 = ZakatFormController()
    //     0x8f86d8: bl              #0x8f8d70  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::ZakatFormController
    // 0x8f86dc: ldur            x0, [fp, #-8]
    // 0x8f86e0: b               #0x8f8854
    // 0x8f86e4: r16 = Instance_PaymentType
    //     0x8f86e4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0x8f86e8: ldr             x16, [x16, #0x638]
    // 0x8f86ec: cmp             w1, w16
    // 0x8f86f0: b.ne            #0x8f8758
    // 0x8f86f4: r0 = find()
    //     0x8f86f4: bl              #0x813400  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::find
    // 0x8f86f8: stur            x0, [fp, #-8]
    // 0x8f86fc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f86fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f8700: ldr             x0, [x0, #0x2670]
    //     0x8f8704: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f8708: cmp             w0, w16
    //     0x8f870c: b.ne            #0x8f8718
    //     0x8f8710: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f8714: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f8718: r16 = <RemoteConfigService>
    //     0x8f8718: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8f871c: ldr             x16, [x16, #0xae8]
    // 0x8f8720: r30 = "remote_config_service"
    //     0x8f8720: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8f8724: ldr             lr, [lr, #0xaf0]
    // 0x8f8728: stp             lr, x16, [SP]
    // 0x8f872c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8f872c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8f8730: r0 = Inst.find()
    //     0x8f8730: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8f8734: stur            x0, [fp, #-0x10]
    // 0x8f8738: r0 = FidyahFormController()
    //     0x8f8738: bl              #0x8f8d64  ; AllocateFidyahFormControllerStub -> FidyahFormController (size=0x50)
    // 0x8f873c: mov             x1, x0
    // 0x8f8740: ldur            x2, [fp, #-0x10]
    // 0x8f8744: ldur            x3, [fp, #-8]
    // 0x8f8748: stur            x0, [fp, #-8]
    // 0x8f874c: r0 = FidyahFormController()
    //     0x8f874c: bl              #0x8f8c60  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::FidyahFormController
    // 0x8f8750: ldur            x0, [fp, #-8]
    // 0x8f8754: b               #0x8f8854
    // 0x8f8758: r16 = Instance_PaymentType
    //     0x8f8758: add             x16, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0x8f875c: ldr             x16, [x16, #0x5d8]
    // 0x8f8760: cmp             w1, w16
    // 0x8f8764: b.eq            #0x8f8778
    // 0x8f8768: r16 = Instance_PaymentType
    //     0x8f8768: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0x8f876c: ldr             x16, [x16, #0x608]
    // 0x8f8770: cmp             w1, w16
    // 0x8f8774: b.ne            #0x8f87e8
    // 0x8f8778: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f8778: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f877c: ldr             x0, [x0, #0x2670]
    //     0x8f8780: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f8784: cmp             w0, w16
    //     0x8f8788: b.ne            #0x8f8794
    //     0x8f878c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f8790: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f8794: r16 = <RemoteConfigService>
    //     0x8f8794: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8f8798: ldr             x16, [x16, #0xae8]
    // 0x8f879c: r30 = "remote_config_service"
    //     0x8f879c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8f87a0: ldr             lr, [lr, #0xaf0]
    // 0x8f87a4: stp             lr, x16, [SP]
    // 0x8f87a8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8f87a8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8f87ac: r0 = Inst.find()
    //     0x8f87ac: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8f87b0: mov             x1, x0
    // 0x8f87b4: ldr             x0, [fp, #0x10]
    // 0x8f87b8: stur            x1, [fp, #-0x10]
    // 0x8f87bc: LoadField: r2 = r0->field_27
    //     0x8f87bc: ldur            w2, [x0, #0x27]
    // 0x8f87c0: DecompressPointer r2
    //     0x8f87c0: add             x2, x2, HEAP, lsl #32
    // 0x8f87c4: stur            x2, [fp, #-8]
    // 0x8f87c8: r0 = CampaignFormController()
    //     0x8f87c8: bl              #0x8f8c54  ; AllocateCampaignFormControllerStub -> CampaignFormController (size=0x64)
    // 0x8f87cc: mov             x1, x0
    // 0x8f87d0: ldur            x2, [fp, #-8]
    // 0x8f87d4: ldur            x3, [fp, #-0x10]
    // 0x8f87d8: stur            x0, [fp, #-8]
    // 0x8f87dc: r0 = CampaignFormController()
    //     0x8f87dc: bl              #0x8f8970  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::CampaignFormController
    // 0x8f87e0: ldur            x0, [fp, #-8]
    // 0x8f87e4: b               #0x8f8854
    // 0x8f87e8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f87e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f87ec: ldr             x0, [x0, #0x2670]
    //     0x8f87f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f87f4: cmp             w0, w16
    //     0x8f87f8: b.ne            #0x8f8804
    //     0x8f87fc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f8800: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f8804: r16 = <RemoteConfigService>
    //     0x8f8804: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8f8808: ldr             x16, [x16, #0xae8]
    // 0x8f880c: r30 = "remote_config_service"
    //     0x8f880c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8f8810: ldr             lr, [lr, #0xaf0]
    // 0x8f8814: stp             lr, x16, [SP]
    // 0x8f8818: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8f8818: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8f881c: r0 = Inst.find()
    //     0x8f881c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8f8820: mov             x1, x0
    // 0x8f8824: ldr             x0, [fp, #0x10]
    // 0x8f8828: stur            x1, [fp, #-0x10]
    // 0x8f882c: LoadField: r2 = r0->field_27
    //     0x8f882c: ldur            w2, [x0, #0x27]
    // 0x8f8830: DecompressPointer r2
    //     0x8f8830: add             x2, x2, HEAP, lsl #32
    // 0x8f8834: stur            x2, [fp, #-8]
    // 0x8f8838: r0 = QurbanFormController()
    //     0x8f8838: bl              #0x8f8964  ; AllocateQurbanFormControllerStub -> QurbanFormController (size=0x50)
    // 0x8f883c: mov             x1, x0
    // 0x8f8840: ldur            x2, [fp, #-8]
    // 0x8f8844: ldur            x3, [fp, #-0x10]
    // 0x8f8848: stur            x0, [fp, #-8]
    // 0x8f884c: r0 = QurbanFormController()
    //     0x8f884c: bl              #0x8f8868  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::QurbanFormController
    // 0x8f8850: ldur            x0, [fp, #-8]
    // 0x8f8854: LeaveFrame
    //     0x8f8854: mov             SP, fp
    //     0x8f8858: ldp             fp, lr, [SP], #0x10
    // 0x8f885c: ret
    //     0x8f885c: ret             
    // 0x8f8860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8864: b               #0x8f85d4
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927a04, size: 0x3c
    // 0x927a04: EnterFrame
    //     0x927a04: stp             fp, lr, [SP, #-0x10]!
    //     0x927a08: mov             fp, SP
    // 0x927a0c: CheckStackOverflow
    //     0x927a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927a10: cmp             SP, x16
    //     0x927a14: b.ls            #0x927a38
    // 0x927a18: LoadField: r0 = r1->field_4b
    //     0x927a18: ldur            w0, [x1, #0x4b]
    // 0x927a1c: DecompressPointer r0
    //     0x927a1c: add             x0, x0, HEAP, lsl #32
    // 0x927a20: mov             x1, x0
    // 0x927a24: r0 = dispose()
    //     0x927a24: bl              #0xa8b4f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::dispose
    // 0x927a28: r0 = Null
    //     0x927a28: mov             x0, NULL
    // 0x927a2c: LeaveFrame
    //     0x927a2c: mov             SP, fp
    //     0x927a30: ldp             fp, lr, [SP], #0x10
    // 0x927a34: ret
    //     0x927a34: ret             
    // 0x927a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927a38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927a3c: b               #0x927a18
  }
  get _ title(/* No info */) {
    // ** addr: 0xae5024, size: 0xa4
    // 0xae5024: EnterFrame
    //     0xae5024: stp             fp, lr, [SP, #-0x10]!
    //     0xae5028: mov             fp, SP
    // 0xae502c: AllocStack(0x10)
    //     0xae502c: sub             SP, SP, #0x10
    // 0xae5030: CheckStackOverflow
    //     0xae5030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5034: cmp             SP, x16
    //     0xae5038: b.ls            #0xae50c0
    // 0xae503c: LoadField: r0 = r1->field_1f
    //     0xae503c: ldur            w0, [x1, #0x1f]
    // 0xae5040: DecompressPointer r0
    //     0xae5040: add             x0, x0, HEAP, lsl #32
    // 0xae5044: stur            x0, [fp, #-8]
    // 0xae5048: r16 = Instance_PaymentType
    //     0xae5048: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xae504c: ldr             x16, [x16, #0x5c0]
    // 0xae5050: cmp             w0, w16
    // 0xae5054: b.ne            #0xae5084
    // 0xae5058: LoadField: r2 = r1->field_23
    //     0xae5058: ldur            w2, [x1, #0x23]
    // 0xae505c: DecompressPointer r2
    //     0xae505c: add             x2, x2, HEAP, lsl #32
    // 0xae5060: r16 = Instance_ZakatTypes
    //     0xae5060: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xae5064: ldr             x16, [x16, #0x620]
    // 0xae5068: cmp             w2, w16
    // 0xae506c: b.ne            #0xae5084
    // 0xae5070: r0 = "Bayar Zakat Fitrah"
    //     0xae5070: add             x0, PP, #0x30, lsl #12  ; [pp+0x30278] "Bayar Zakat Fitrah"
    //     0xae5074: ldr             x0, [x0, #0x278]
    // 0xae5078: LeaveFrame
    //     0xae5078: mov             SP, fp
    //     0xae507c: ldp             fp, lr, [SP], #0x10
    // 0xae5080: ret
    //     0xae5080: ret             
    // 0xae5084: r1 = Null
    //     0xae5084: mov             x1, NULL
    // 0xae5088: r2 = 4
    //     0xae5088: movz            x2, #0x4
    // 0xae508c: r0 = AllocateArray()
    //     0xae508c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae5090: r16 = "Bayar "
    //     0xae5090: add             x16, PP, #0x30, lsl #12  ; [pp+0x30280] "Bayar "
    //     0xae5094: ldr             x16, [x16, #0x280]
    // 0xae5098: StoreField: r0->field_f = r16
    //     0xae5098: stur            w16, [x0, #0xf]
    // 0xae509c: ldur            x1, [fp, #-8]
    // 0xae50a0: LoadField: r2 = r1->field_13
    //     0xae50a0: ldur            w2, [x1, #0x13]
    // 0xae50a4: DecompressPointer r2
    //     0xae50a4: add             x2, x2, HEAP, lsl #32
    // 0xae50a8: StoreField: r0->field_13 = r2
    //     0xae50a8: stur            w2, [x0, #0x13]
    // 0xae50ac: str             x0, [SP]
    // 0xae50b0: r0 = _interpolate()
    //     0xae50b0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae50b4: LeaveFrame
    //     0xae50b4: mov             SP, fp
    //     0xae50b8: ldp             fp, lr, [SP], #0x10
    // 0xae50bc: ret
    //     0xae50bc: ret             
    // 0xae50c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae50c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae50c4: b               #0xae503c
  }
  [closure] void next(dynamic) {
    // ** addr: 0xae50c8, size: 0x38
    // 0xae50c8: EnterFrame
    //     0xae50c8: stp             fp, lr, [SP, #-0x10]!
    //     0xae50cc: mov             fp, SP
    // 0xae50d0: ldr             x0, [fp, #0x10]
    // 0xae50d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae50d4: ldur            w1, [x0, #0x17]
    // 0xae50d8: DecompressPointer r1
    //     0xae50d8: add             x1, x1, HEAP, lsl #32
    // 0xae50dc: CheckStackOverflow
    //     0xae50dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae50e0: cmp             SP, x16
    //     0xae50e4: b.ls            #0xae50f8
    // 0xae50e8: r0 = next()
    //     0xae50e8: bl              #0xae5100  ; [package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart] PayDonationController::next
    // 0xae50ec: LeaveFrame
    //     0xae50ec: mov             SP, fp
    //     0xae50f0: ldp             fp, lr, [SP], #0x10
    // 0xae50f4: ret
    //     0xae50f4: ret             
    // 0xae50f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae50f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae50fc: b               #0xae50e8
  }
  _ next(/* No info */) {
    // ** addr: 0xae5100, size: 0x2bc
    // 0xae5100: EnterFrame
    //     0xae5100: stp             fp, lr, [SP, #-0x10]!
    //     0xae5104: mov             fp, SP
    // 0xae5108: AllocStack(0x50)
    //     0xae5108: sub             SP, SP, #0x50
    // 0xae510c: SetupParameters(PayDonationController this /* r1 => r0, fp-0x8 */)
    //     0xae510c: mov             x0, x1
    //     0xae5110: stur            x1, [fp, #-8]
    // 0xae5114: CheckStackOverflow
    //     0xae5114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5118: cmp             SP, x16
    //     0xae511c: b.ls            #0xae53b4
    // 0xae5120: LoadField: r1 = r0->field_37
    //     0xae5120: ldur            w1, [x0, #0x37]
    // 0xae5124: DecompressPointer r1
    //     0xae5124: add             x1, x1, HEAP, lsl #32
    // 0xae5128: r0 = currentState()
    //     0xae5128: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0xae512c: cmp             w0, NULL
    // 0xae5130: b.ne            #0xae513c
    // 0xae5134: r0 = Null
    //     0xae5134: mov             x0, NULL
    // 0xae5138: b               #0xae5144
    // 0xae513c: mov             x1, x0
    // 0xae5140: r0 = validate()
    //     0xae5140: bl              #0xae5c74  ; [package:flutter/src/widgets/form.dart] FormState::validate
    // 0xae5144: cmp             w0, NULL
    // 0xae5148: b.ne            #0xae5154
    // 0xae514c: ldur            x0, [fp, #-8]
    // 0xae5150: b               #0xae52f8
    // 0xae5154: tbnz            w0, #4, #0xae52f4
    // 0xae5158: ldur            x0, [fp, #-8]
    // 0xae515c: mov             x1, x0
    // 0xae5160: LoadField: r0 = r1->field_3f
    //     0xae5160: ldur            w0, [x1, #0x3f]
    // 0xae5164: DecompressPointer r0
    //     0xae5164: add             x0, x0, HEAP, lsl #32
    // 0xae5168: r16 = Sentinel
    //     0xae5168: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae516c: cmp             w0, w16
    // 0xae5170: b.ne            #0xae5180
    // 0xae5174: r2 = form
    //     0xae5174: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae5178: ldr             x2, [x2, #0xc60]
    // 0xae517c: r0 = InitLateFinalInstanceField()
    //     0xae517c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae5180: r1 = LoadClassIdInstr(r0)
    //     0xae5180: ldur            x1, [x0, #-1]
    //     0xae5184: ubfx            x1, x1, #0xc, #0x14
    // 0xae5188: mov             x16, x0
    // 0xae518c: mov             x0, x1
    // 0xae5190: mov             x1, x16
    // 0xae5194: r0 = GDT[cid_x0 + -0xe54]()
    //     0xae5194: sub             lr, x0, #0xe54
    //     0xae5198: ldr             lr, [x21, lr, lsl #3]
    //     0xae519c: blr             lr
    // 0xae51a0: mov             x2, x0
    // 0xae51a4: ldur            x0, [fp, #-8]
    // 0xae51a8: stur            x2, [fp, #-0x10]
    // 0xae51ac: LoadField: r1 = r0->field_33
    //     0xae51ac: ldur            w1, [x0, #0x33]
    // 0xae51b0: DecompressPointer r1
    //     0xae51b0: add             x1, x1, HEAP, lsl #32
    // 0xae51b4: r0 = value()
    //     0xae51b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae51b8: mov             x2, x0
    // 0xae51bc: ldur            x0, [fp, #-8]
    // 0xae51c0: stur            x2, [fp, #-0x20]
    // 0xae51c4: LoadField: r3 = r0->field_43
    //     0xae51c4: ldur            w3, [x0, #0x43]
    // 0xae51c8: DecompressPointer r3
    //     0xae51c8: add             x3, x3, HEAP, lsl #32
    // 0xae51cc: mov             x1, x3
    // 0xae51d0: stur            x3, [fp, #-0x18]
    // 0xae51d4: r0 = value()
    //     0xae51d4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae51d8: mov             x2, x0
    // 0xae51dc: ldur            x0, [fp, #-8]
    // 0xae51e0: stur            x2, [fp, #-0x30]
    // 0xae51e4: LoadField: r3 = r0->field_47
    //     0xae51e4: ldur            w3, [x0, #0x47]
    // 0xae51e8: DecompressPointer r3
    //     0xae51e8: add             x3, x3, HEAP, lsl #32
    // 0xae51ec: mov             x1, x3
    // 0xae51f0: stur            x3, [fp, #-0x28]
    // 0xae51f4: r0 = value()
    //     0xae51f4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae51f8: mov             x1, x0
    // 0xae51fc: ldur            x0, [fp, #-8]
    // 0xae5200: LoadField: r3 = r0->field_1f
    //     0xae5200: ldur            w3, [x0, #0x1f]
    // 0xae5204: DecompressPointer r3
    //     0xae5204: add             x3, x3, HEAP, lsl #32
    // 0xae5208: stur            x3, [fp, #-0x38]
    // 0xae520c: ldur            x16, [fp, #-0x20]
    // 0xae5210: ldur            lr, [fp, #-0x30]
    // 0xae5214: stp             lr, x16, [SP, #8]
    // 0xae5218: str             x1, [SP]
    // 0xae521c: ldur            x1, [fp, #-0x10]
    // 0xae5220: mov             x2, x3
    // 0xae5224: r4 = const [0, 0x5, 0x3, 0x2, email, 0x4, method, 0x2, phone, 0x3, null]
    //     0xae5224: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe30] List(11) [0, 0x5, 0x3, 0x2, "email", 0x4, "method", 0x2, "phone", 0x3, Null]
    //     0xae5228: ldr             x4, [x4, #0xe30]
    // 0xae522c: r0 = copyWith()
    //     0xae522c: bl              #0xae5680  ; [package:nuonline/app/data/models/transaction.dart] TransactionRequest::copyWith
    // 0xae5230: mov             x2, x0
    // 0xae5234: ldur            x0, [fp, #-8]
    // 0xae5238: stur            x2, [fp, #-0x30]
    // 0xae523c: LoadField: r3 = r0->field_2b
    //     0xae523c: ldur            w3, [x0, #0x2b]
    // 0xae5240: DecompressPointer r3
    //     0xae5240: add             x3, x3, HEAP, lsl #32
    // 0xae5244: stur            x3, [fp, #-0x20]
    // 0xae5248: LoadField: r4 = r2->field_1b
    //     0xae5248: ldur            w4, [x2, #0x1b]
    // 0xae524c: DecompressPointer r4
    //     0xae524c: add             x4, x4, HEAP, lsl #32
    // 0xae5250: ldur            x1, [fp, #-0x18]
    // 0xae5254: stur            x4, [fp, #-0x10]
    // 0xae5258: r0 = value()
    //     0xae5258: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae525c: ldur            x1, [fp, #-0x28]
    // 0xae5260: stur            x0, [fp, #-0x18]
    // 0xae5264: r0 = value()
    //     0xae5264: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae5268: stur            x0, [fp, #-0x28]
    // 0xae526c: r0 = Donor()
    //     0xae526c: bl              #0xa619a0  ; AllocateDonorStub -> Donor (size=0x14)
    // 0xae5270: mov             x1, x0
    // 0xae5274: ldur            x0, [fp, #-0x10]
    // 0xae5278: StoreField: r1->field_7 = r0
    //     0xae5278: stur            w0, [x1, #7]
    // 0xae527c: ldur            x0, [fp, #-0x18]
    // 0xae5280: StoreField: r1->field_b = r0
    //     0xae5280: stur            w0, [x1, #0xb]
    // 0xae5284: ldur            x0, [fp, #-0x28]
    // 0xae5288: StoreField: r1->field_f = r0
    //     0xae5288: stur            w0, [x1, #0xf]
    // 0xae528c: mov             x3, x1
    // 0xae5290: ldur            x1, [fp, #-0x20]
    // 0xae5294: ldur            x2, [fp, #-0x38]
    // 0xae5298: r0 = updateDonor()
    //     0xae5298: bl              #0xae560c  ; [package:nuonline/app/data/repositories/donor_repository.dart] DonorRepository::updateDonor
    // 0xae529c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae529c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae52a0: ldr             x0, [x0, #0x2670]
    //     0xae52a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae52a8: cmp             w0, w16
    //     0xae52ac: b.ne            #0xae52b8
    //     0xae52b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae52b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae52b8: ldur            x1, [fp, #-0x30]
    // 0xae52bc: r0 = toMap()
    //     0xae52bc: bl              #0xae5444  ; [package:nuonline/app/data/models/transaction.dart] TransactionRequest::toMap
    // 0xae52c0: r16 = "/donation/confirm-donation"
    //     0xae52c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe38] "/donation/confirm-donation"
    //     0xae52c4: ldr             x16, [x16, #0xe38]
    // 0xae52c8: stp             x16, NULL, [SP, #8]
    // 0xae52cc: str             x0, [SP]
    // 0xae52d0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae52d0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae52d4: ldr             x4, [x4, #0x478]
    // 0xae52d8: r0 = GetNavigation.toNamed()
    //     0xae52d8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae52dc: ldur            x0, [fp, #-8]
    // 0xae52e0: LoadField: r1 = r0->field_2f
    //     0xae52e0: ldur            w1, [x0, #0x2f]
    // 0xae52e4: DecompressPointer r1
    //     0xae52e4: add             x1, x1, HEAP, lsl #32
    // 0xae52e8: r2 = true
    //     0xae52e8: add             x2, NULL, #0x20  ; true
    // 0xae52ec: r0 = isAlreadyDonate=()
    //     0xae52ec: bl              #0x916cb8  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isAlreadyDonate=
    // 0xae52f0: b               #0xae53a4
    // 0xae52f4: ldur            x0, [fp, #-8]
    // 0xae52f8: LoadField: r1 = r0->field_1f
    //     0xae52f8: ldur            w1, [x0, #0x1f]
    // 0xae52fc: DecompressPointer r1
    //     0xae52fc: add             x1, x1, HEAP, lsl #32
    // 0xae5300: r16 = Instance_PaymentType
    //     0xae5300: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xae5304: ldr             x16, [x16, #0x5f0]
    // 0xae5308: cmp             w1, w16
    // 0xae530c: b.ne            #0xae53a4
    // 0xae5310: mov             x1, x0
    // 0xae5314: LoadField: r0 = r1->field_3f
    //     0xae5314: ldur            w0, [x1, #0x3f]
    // 0xae5318: DecompressPointer r0
    //     0xae5318: add             x0, x0, HEAP, lsl #32
    // 0xae531c: r16 = Sentinel
    //     0xae531c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae5320: cmp             w0, w16
    // 0xae5324: b.ne            #0xae5334
    // 0xae5328: r2 = form
    //     0xae5328: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae532c: ldr             x2, [x2, #0xc60]
    // 0xae5330: r0 = InitLateFinalInstanceField()
    //     0xae5330: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae5334: LoadField: r1 = r0->field_27
    //     0xae5334: ldur            w1, [x0, #0x27]
    // 0xae5338: DecompressPointer r1
    //     0xae5338: add             x1, x1, HEAP, lsl #32
    // 0xae533c: r0 = RxStringExt.isNotEmpty()
    //     0xae533c: bl              #0xae5410  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0xae5340: tbnz            w0, #4, #0xae53a4
    // 0xae5344: ldur            x3, [fp, #-8]
    // 0xae5348: LoadField: r4 = r3->field_3f
    //     0xae5348: ldur            w4, [x3, #0x3f]
    // 0xae534c: DecompressPointer r4
    //     0xae534c: add             x4, x4, HEAP, lsl #32
    // 0xae5350: mov             x0, x4
    // 0xae5354: stur            x4, [fp, #-0x10]
    // 0xae5358: r2 = Null
    //     0xae5358: mov             x2, NULL
    // 0xae535c: r1 = Null
    //     0xae535c: mov             x1, NULL
    // 0xae5360: r4 = LoadClassIdInstr(r0)
    //     0xae5360: ldur            x4, [x0, #-1]
    //     0xae5364: ubfx            x4, x4, #0xc, #0x14
    // 0xae5368: cmp             x4, #0x777
    // 0xae536c: b.eq            #0xae5384
    // 0xae5370: r8 = QurbanFormController
    //     0xae5370: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fd30] Type: QurbanFormController
    //     0xae5374: ldr             x8, [x8, #0xd30]
    // 0xae5378: r3 = Null
    //     0xae5378: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe40] Null
    //     0xae537c: ldr             x3, [x3, #0xe40]
    // 0xae5380: r0 = DefaultTypeTest()
    //     0xae5380: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae5384: ldur            x1, [fp, #-0x10]
    // 0xae5388: r0 = isValid()
    //     0xae5388: bl              #0xae53bc  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::isValid
    // 0xae538c: tbnz            w0, #4, #0xae53a4
    // 0xae5390: ldur            x0, [fp, #-8]
    // 0xae5394: LoadField: r1 = r0->field_4b
    //     0xae5394: ldur            w1, [x0, #0x4b]
    // 0xae5398: DecompressPointer r1
    //     0xae5398: add             x1, x1, HEAP, lsl #32
    // 0xae539c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae539c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae53a0: r0 = requestFocus()
    //     0xae53a0: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xae53a4: r0 = Null
    //     0xae53a4: mov             x0, NULL
    // 0xae53a8: LeaveFrame
    //     0xae53a8: mov             SP, fp
    //     0xae53ac: ldp             fp, lr, [SP], #0x10
    // 0xae53b0: ret
    //     0xae53b0: ret             
    // 0xae53b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae53b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae53b8: b               #0xae5120
  }
}
