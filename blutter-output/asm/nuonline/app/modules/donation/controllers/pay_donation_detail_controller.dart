// lib: , url: package:nuonline/app/modules/donation/controllers/pay_donation_detail_controller.dart

// class id: 1050213, size: 0x8
class :: {
}

// class id: 1905, size: 0x34, field offset: 0x28
class PayDonationDetailController extends _ConfirmDonationController&GetxController&StateMixin&OnlineMixin {

  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0x72a8d8, size: 0x78
    // 0x72a8d8: EnterFrame
    //     0x72a8d8: stp             fp, lr, [SP, #-0x10]!
    //     0x72a8dc: mov             fp, SP
    // 0x72a8e0: AllocStack(0x10)
    //     0x72a8e0: sub             SP, SP, #0x10
    // 0x72a8e4: SetupParameters(PayDonationDetailController this /* r1 => r1, fp-0x10 */)
    //     0x72a8e4: stur            NULL, [fp, #-8]
    //     0x72a8e8: stur            x1, [fp, #-0x10]
    // 0x72a8ec: CheckStackOverflow
    //     0x72a8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72a8f0: cmp             SP, x16
    //     0x72a8f4: b.ls            #0x72a948
    // 0x72a8f8: InitAsync() -> Future<ApiResult<Transaction>>
    //     0x72a8f8: add             x0, PP, #0x40, lsl #12  ; [pp+0x404a0] TypeArguments: <ApiResult<Transaction>>
    //     0x72a8fc: ldr             x0, [x0, #0x4a0]
    //     0x72a900: bl              #0x661298  ; InitAsyncStub
    // 0x72a904: r0 = RxStatus()
    //     0x72a904: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x72a908: mov             x1, x0
    // 0x72a90c: r0 = false
    //     0x72a90c: add             x0, NULL, #0x30  ; false
    // 0x72a910: StoreField: r1->field_f = r0
    //     0x72a910: stur            w0, [x1, #0xf]
    // 0x72a914: r2 = true
    //     0x72a914: add             x2, NULL, #0x20  ; true
    // 0x72a918: StoreField: r1->field_7 = r2
    //     0x72a918: stur            w2, [x1, #7]
    // 0x72a91c: StoreField: r1->field_b = r0
    //     0x72a91c: stur            w0, [x1, #0xb]
    // 0x72a920: mov             x3, x1
    // 0x72a924: ldur            x1, [fp, #-0x10]
    // 0x72a928: r2 = Null
    //     0x72a928: mov             x2, NULL
    // 0x72a92c: r0 = change()
    //     0x72a92c: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0x72a930: ldur            x0, [fp, #-0x10]
    // 0x72a934: LoadField: r1 = r0->field_2f
    //     0x72a934: ldur            w1, [x0, #0x2f]
    // 0x72a938: DecompressPointer r1
    //     0x72a938: add             x1, x1, HEAP, lsl #32
    // 0x72a93c: LoadField: r2 = r0->field_27
    //     0x72a93c: ldur            x2, [x0, #0x27]
    // 0x72a940: r0 = findTransactionById()
    //     0x72a940: bl              #0x72a9b8  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findTransactionById
    // 0x72a944: r0 = ReturnAsync()
    //     0x72a944: b               #0x6576a4  ; ReturnAsyncStub
    // 0x72a948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72a948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72a94c: b               #0x72a8f8
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0x7f8990, size: 0x68
    // 0x7f8990: EnterFrame
    //     0x7f8990: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8994: mov             fp, SP
    // 0x7f8998: AllocStack(0x20)
    //     0x7f8998: sub             SP, SP, #0x20
    // 0x7f899c: SetupParameters(PayDonationDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7f899c: stur            NULL, [fp, #-8]
    //     0x7f89a0: stur            x1, [fp, #-0x10]
    //     0x7f89a4: stur            x2, [fp, #-0x18]
    //     0x7f89a8: stur            x3, [fp, #-0x20]
    // 0x7f89ac: CheckStackOverflow
    //     0x7f89ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f89b0: cmp             SP, x16
    //     0x7f89b4: b.ls            #0x7f89f0
    // 0x7f89b8: InitAsync() -> Future<void?>
    //     0x7f89b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7f89bc: bl              #0x661298  ; InitAsyncStub
    // 0x7f89c0: r0 = RxStatus()
    //     0x7f89c0: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7f89c4: mov             x1, x0
    // 0x7f89c8: r0 = false
    //     0x7f89c8: add             x0, NULL, #0x30  ; false
    // 0x7f89cc: StoreField: r1->field_f = r0
    //     0x7f89cc: stur            w0, [x1, #0xf]
    // 0x7f89d0: StoreField: r1->field_7 = r0
    //     0x7f89d0: stur            w0, [x1, #7]
    // 0x7f89d4: StoreField: r1->field_b = r0
    //     0x7f89d4: stur            w0, [x1, #0xb]
    // 0x7f89d8: mov             x3, x1
    // 0x7f89dc: ldur            x1, [fp, #-0x10]
    // 0x7f89e0: ldur            x2, [fp, #-0x18]
    // 0x7f89e4: r0 = change()
    //     0x7f89e4: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0x7f89e8: r0 = Null
    //     0x7f89e8: mov             x0, NULL
    // 0x7f89ec: r0 = ReturnAsyncNotFuture()
    //     0x7f89ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7f89f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f89f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f89f4: b               #0x7f89b8
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f81e8, size: 0x48
    // 0x8f81e8: EnterFrame
    //     0x8f81e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f81ec: mov             fp, SP
    // 0x8f81f0: AllocStack(0x8)
    //     0x8f81f0: sub             SP, SP, #8
    // 0x8f81f4: SetupParameters(PayDonationDetailController this /* r1 => r0, fp-0x8 */)
    //     0x8f81f4: mov             x0, x1
    //     0x8f81f8: stur            x1, [fp, #-8]
    // 0x8f81fc: CheckStackOverflow
    //     0x8f81fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8200: cmp             SP, x16
    //     0x8f8204: b.ls            #0x8f8228
    // 0x8f8208: mov             x1, x0
    // 0x8f820c: r0 = onInit()
    //     0x8f820c: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f8210: ldur            x1, [fp, #-8]
    // 0x8f8214: r0 = executeOnlineMode()
    //     0x8f8214: bl              #0x8ef784  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode
    // 0x8f8218: r0 = Null
    //     0x8f8218: mov             x0, NULL
    // 0x8f821c: LeaveFrame
    //     0x8f821c: mov             SP, fp
    //     0x8f8220: ldp             fp, lr, [SP], #0x10
    // 0x8f8224: ret
    //     0x8f8224: ret             
    // 0x8f8228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8228: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f822c: b               #0x8f8208
  }
  [closure] Future<void> onRefresh(dynamic) {
    // ** addr: 0xae3c20, size: 0x38
    // 0xae3c20: EnterFrame
    //     0xae3c20: stp             fp, lr, [SP, #-0x10]!
    //     0xae3c24: mov             fp, SP
    // 0xae3c28: ldr             x0, [fp, #0x10]
    // 0xae3c2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae3c2c: ldur            w1, [x0, #0x17]
    // 0xae3c30: DecompressPointer r1
    //     0xae3c30: add             x1, x1, HEAP, lsl #32
    // 0xae3c34: CheckStackOverflow
    //     0xae3c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3c38: cmp             SP, x16
    //     0xae3c3c: b.ls            #0xae3c50
    // 0xae3c40: r0 = onRefresh()
    //     0xae3c40: bl              #0xae3c58  ; [package:nuonline/app/modules/donation/controllers/pay_donation_detail_controller.dart] PayDonationDetailController::onRefresh
    // 0xae3c44: LeaveFrame
    //     0xae3c44: mov             SP, fp
    //     0xae3c48: ldp             fp, lr, [SP], #0x10
    // 0xae3c4c: ret
    //     0xae3c4c: ret             
    // 0xae3c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3c54: b               #0xae3c40
  }
  _ onRefresh(/* No info */) async {
    // ** addr: 0xae3c58, size: 0x40
    // 0xae3c58: EnterFrame
    //     0xae3c58: stp             fp, lr, [SP, #-0x10]!
    //     0xae3c5c: mov             fp, SP
    // 0xae3c60: AllocStack(0x10)
    //     0xae3c60: sub             SP, SP, #0x10
    // 0xae3c64: SetupParameters(PayDonationDetailController this /* r1 => r1, fp-0x10 */)
    //     0xae3c64: stur            NULL, [fp, #-8]
    //     0xae3c68: stur            x1, [fp, #-0x10]
    // 0xae3c6c: CheckStackOverflow
    //     0xae3c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3c70: cmp             SP, x16
    //     0xae3c74: b.ls            #0xae3c90
    // 0xae3c78: InitAsync() -> Future<void?>
    //     0xae3c78: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae3c7c: bl              #0x661298  ; InitAsyncStub
    // 0xae3c80: ldur            x1, [fp, #-0x10]
    // 0xae3c84: r0 = executeOnlineMode()
    //     0xae3c84: bl              #0x8ef784  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] _ConfirmDonationController&GetxController&StateMixin&OnlineMixin::executeOnlineMode
    // 0xae3c88: r0 = Null
    //     0xae3c88: mov             x0, NULL
    // 0xae3c8c: r0 = ReturnAsyncNotFuture()
    //     0xae3c8c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae3c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3c94: b               #0xae3c78
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xe34908, size: 0x70
    // 0xe34908: EnterFrame
    //     0xe34908: stp             fp, lr, [SP, #-0x10]!
    //     0xe3490c: mov             fp, SP
    // 0xe34910: AllocStack(0x18)
    //     0xe34910: sub             SP, SP, #0x18
    // 0xe34914: SetupParameters(PayDonationDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe34914: stur            NULL, [fp, #-8]
    //     0xe34918: stur            x1, [fp, #-0x10]
    //     0xe3491c: stur            x2, [fp, #-0x18]
    // 0xe34920: CheckStackOverflow
    //     0xe34920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34924: cmp             SP, x16
    //     0xe34928: b.ls            #0xe34970
    // 0xe3492c: InitAsync() -> Future<void?>
    //     0xe3492c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe34930: bl              #0x661298  ; InitAsyncStub
    // 0xe34934: r0 = RxStatus()
    //     0xe34934: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xe34938: mov             x1, x0
    // 0xe3493c: r0 = false
    //     0xe3493c: add             x0, NULL, #0x30  ; false
    // 0xe34940: StoreField: r1->field_f = r0
    //     0xe34940: stur            w0, [x1, #0xf]
    // 0xe34944: StoreField: r1->field_7 = r0
    //     0xe34944: stur            w0, [x1, #7]
    // 0xe34948: r0 = true
    //     0xe34948: add             x0, NULL, #0x20  ; true
    // 0xe3494c: StoreField: r1->field_b = r0
    //     0xe3494c: stur            w0, [x1, #0xb]
    // 0xe34950: ldur            x0, [fp, #-0x18]
    // 0xe34954: StoreField: r1->field_13 = r0
    //     0xe34954: stur            w0, [x1, #0x13]
    // 0xe34958: mov             x3, x1
    // 0xe3495c: ldur            x1, [fp, #-0x10]
    // 0xe34960: r2 = Null
    //     0xe34960: mov             x2, NULL
    // 0xe34964: r0 = change()
    //     0xe34964: bl              #0x72a6e0  ; [package:nuonline/app/modules/doa/doa_bookmark/controllers/doa_bookmark_controller.dart] _DoaBookmarkController&GetxController&StateMixin::change
    // 0xe34968: r0 = Null
    //     0xe34968: mov             x0, NULL
    // 0xe3496c: r0 = ReturnAsyncNotFuture()
    //     0xe3496c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe34970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34970: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34974: b               #0xe3492c
  }
}
