// lib: , url: package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart

// class id: 1050214, size: 0x8
class :: {
}

// class id: 1911, size: 0x50, field offset: 0x40
class QurbanFormController extends DonationFormController {

  _ onInit(/* No info */) {
    // ** addr: 0x8f4a48, size: 0xc8
    // 0x8f4a48: EnterFrame
    //     0x8f4a48: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4a4c: mov             fp, SP
    // 0x8f4a50: AllocStack(0x18)
    //     0x8f4a50: sub             SP, SP, #0x18
    // 0x8f4a54: SetupParameters(QurbanFormController this /* r1 => r1, fp-0x8 */)
    //     0x8f4a54: stur            x1, [fp, #-8]
    // 0x8f4a58: CheckStackOverflow
    //     0x8f4a58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4a5c: cmp             SP, x16
    //     0x8f4a60: b.ls            #0x8f4b08
    // 0x8f4a64: r1 = 1
    //     0x8f4a64: movz            x1, #0x1
    // 0x8f4a68: r0 = AllocateContext()
    //     0x8f4a68: bl              #0xec126c  ; AllocateContextStub
    // 0x8f4a6c: mov             x2, x0
    // 0x8f4a70: ldur            x0, [fp, #-8]
    // 0x8f4a74: stur            x2, [fp, #-0x10]
    // 0x8f4a78: StoreField: r2->field_f = r0
    //     0x8f4a78: stur            w0, [x2, #0xf]
    // 0x8f4a7c: mov             x1, x0
    // 0x8f4a80: r0 = onInit()
    //     0x8f4a80: bl              #0x8f7e70  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onInit
    // 0x8f4a84: ldur            x0, [fp, #-8]
    // 0x8f4a88: LoadField: r3 = r0->field_4b
    //     0x8f4a88: ldur            w3, [x0, #0x4b]
    // 0x8f4a8c: DecompressPointer r3
    //     0x8f4a8c: add             x3, x3, HEAP, lsl #32
    // 0x8f4a90: ldur            x2, [fp, #-0x10]
    // 0x8f4a94: stur            x3, [fp, #-0x18]
    // 0x8f4a98: r1 = Function '<anonymous closure>':.
    //     0x8f4a98: add             x1, PP, #0x35, lsl #12  ; [pp+0x357f8] AnonymousClosure: (0x8f4dac), in [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onInit (0x8f4a48)
    //     0x8f4a9c: ldr             x1, [x1, #0x7f8]
    // 0x8f4aa0: r0 = AllocateClosure()
    //     0x8f4aa0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4aa4: ldur            x1, [fp, #-0x18]
    // 0x8f4aa8: mov             x2, x0
    // 0x8f4aac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f4aac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f4ab0: r0 = listen()
    //     0x8f4ab0: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8f4ab4: ldur            x0, [fp, #-8]
    // 0x8f4ab8: LoadField: r3 = r0->field_43
    //     0x8f4ab8: ldur            w3, [x0, #0x43]
    // 0x8f4abc: DecompressPointer r3
    //     0x8f4abc: add             x3, x3, HEAP, lsl #32
    // 0x8f4ac0: ldur            x2, [fp, #-0x10]
    // 0x8f4ac4: stur            x3, [fp, #-0x18]
    // 0x8f4ac8: r1 = Function '<anonymous closure>':.
    //     0x8f4ac8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35800] AnonymousClosure: (0x8f4bc8), in [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onInit (0x8f4a48)
    //     0x8f4acc: ldr             x1, [x1, #0x800]
    // 0x8f4ad0: r0 = AllocateClosure()
    //     0x8f4ad0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4ad4: ldur            x1, [fp, #-0x18]
    // 0x8f4ad8: mov             x2, x0
    // 0x8f4adc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f4adc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f4ae0: r0 = listen()
    //     0x8f4ae0: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8f4ae4: ldur            x1, [fp, #-0x18]
    // 0x8f4ae8: r0 = value()
    //     0x8f4ae8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f4aec: ldur            x1, [fp, #-0x18]
    // 0x8f4af0: mov             x2, x0
    // 0x8f4af4: r0 = trigger()
    //     0x8f4af4: bl              #0x8f4b10  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::trigger
    // 0x8f4af8: r0 = Null
    //     0x8f4af8: mov             x0, NULL
    // 0x8f4afc: LeaveFrame
    //     0x8f4afc: mov             SP, fp
    //     0x8f4b00: ldp             fp, lr, [SP], #0x10
    // 0x8f4b04: ret
    //     0x8f4b04: ret             
    // 0x8f4b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4b0c: b               #0x8f4a64
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x8f4bc8, size: 0x88
    // 0x8f4bc8: EnterFrame
    //     0x8f4bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4bcc: mov             fp, SP
    // 0x8f4bd0: AllocStack(0x10)
    //     0x8f4bd0: sub             SP, SP, #0x10
    // 0x8f4bd4: SetupParameters()
    //     0x8f4bd4: ldr             x0, [fp, #0x18]
    //     0x8f4bd8: ldur            w1, [x0, #0x17]
    //     0x8f4bdc: add             x1, x1, HEAP, lsl #32
    // 0x8f4be0: CheckStackOverflow
    //     0x8f4be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4be4: cmp             SP, x16
    //     0x8f4be8: b.ls            #0x8f4c48
    // 0x8f4bec: LoadField: r0 = r1->field_f
    //     0x8f4bec: ldur            w0, [x1, #0xf]
    // 0x8f4bf0: DecompressPointer r0
    //     0x8f4bf0: add             x0, x0, HEAP, lsl #32
    // 0x8f4bf4: LoadField: r2 = r0->field_23
    //     0x8f4bf4: ldur            w2, [x0, #0x23]
    // 0x8f4bf8: DecompressPointer r2
    //     0x8f4bf8: add             x2, x2, HEAP, lsl #32
    // 0x8f4bfc: mov             x1, x0
    // 0x8f4c00: stur            x2, [fp, #-8]
    // 0x8f4c04: r0 = subtotal()
    //     0x8f4c04: bl              #0x8f4c50  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::subtotal
    // 0x8f4c08: r1 = 60
    //     0x8f4c08: movz            x1, #0x3c
    // 0x8f4c0c: branchIfSmi(r0, 0x8f4c18)
    //     0x8f4c0c: tbz             w0, #0, #0x8f4c18
    // 0x8f4c10: r1 = LoadClassIdInstr(r0)
    //     0x8f4c10: ldur            x1, [x0, #-1]
    //     0x8f4c14: ubfx            x1, x1, #0xc, #0x14
    // 0x8f4c18: str             x0, [SP]
    // 0x8f4c1c: mov             x0, x1
    // 0x8f4c20: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f4c20: sub             lr, x0, #1, lsl #12
    //     0x8f4c24: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4c28: blr             lr
    // 0x8f4c2c: ldur            x1, [fp, #-8]
    // 0x8f4c30: mov             x2, x0
    // 0x8f4c34: r0 = value=()
    //     0x8f4c34: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f4c38: r0 = Null
    //     0x8f4c38: mov             x0, NULL
    // 0x8f4c3c: LeaveFrame
    //     0x8f4c3c: mov             SP, fp
    //     0x8f4c40: ldp             fp, lr, [SP], #0x10
    // 0x8f4c44: ret
    //     0x8f4c44: ret             
    // 0x8f4c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4c48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4c4c: b               #0x8f4bec
  }
  get _ subtotal(/* No info */) {
    // ** addr: 0x8f4c50, size: 0xac
    // 0x8f4c50: EnterFrame
    //     0x8f4c50: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4c54: mov             fp, SP
    // 0x8f4c58: AllocStack(0x20)
    //     0x8f4c58: sub             SP, SP, #0x20
    // 0x8f4c5c: SetupParameters(QurbanFormController this /* r1 => r0, fp-0x8 */)
    //     0x8f4c5c: mov             x0, x1
    //     0x8f4c60: stur            x1, [fp, #-8]
    // 0x8f4c64: CheckStackOverflow
    //     0x8f4c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4c68: cmp             SP, x16
    //     0x8f4c6c: b.ls            #0x8f4cf4
    // 0x8f4c70: mov             x1, x0
    // 0x8f4c74: r0 = qurban()
    //     0x8f4c74: bl              #0x8f4cfc  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::qurban
    // 0x8f4c78: cmp             w0, NULL
    // 0x8f4c7c: b.ne            #0x8f4c88
    // 0x8f4c80: r0 = Null
    //     0x8f4c80: mov             x0, NULL
    // 0x8f4c84: b               #0x8f4c94
    // 0x8f4c88: LoadField: r1 = r0->field_b
    //     0x8f4c88: ldur            w1, [x0, #0xb]
    // 0x8f4c8c: DecompressPointer r1
    //     0x8f4c8c: add             x1, x1, HEAP, lsl #32
    // 0x8f4c90: mov             x0, x1
    // 0x8f4c94: cmp             w0, NULL
    // 0x8f4c98: b.ne            #0x8f4ca4
    // 0x8f4c9c: r2 = 0
    //     0x8f4c9c: movz            x2, #0
    // 0x8f4ca0: b               #0x8f4ca8
    // 0x8f4ca4: mov             x2, x0
    // 0x8f4ca8: ldur            x0, [fp, #-8]
    // 0x8f4cac: stur            x2, [fp, #-0x10]
    // 0x8f4cb0: LoadField: r1 = r0->field_43
    //     0x8f4cb0: ldur            w1, [x0, #0x43]
    // 0x8f4cb4: DecompressPointer r1
    //     0x8f4cb4: add             x1, x1, HEAP, lsl #32
    // 0x8f4cb8: r0 = value()
    //     0x8f4cb8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f4cbc: mov             x1, x0
    // 0x8f4cc0: ldur            x0, [fp, #-0x10]
    // 0x8f4cc4: r2 = 60
    //     0x8f4cc4: movz            x2, #0x3c
    // 0x8f4cc8: branchIfSmi(r0, 0x8f4cd4)
    //     0x8f4cc8: tbz             w0, #0, #0x8f4cd4
    // 0x8f4ccc: r2 = LoadClassIdInstr(r0)
    //     0x8f4ccc: ldur            x2, [x0, #-1]
    //     0x8f4cd0: ubfx            x2, x2, #0xc, #0x14
    // 0x8f4cd4: stp             x1, x0, [SP]
    // 0x8f4cd8: mov             x0, x2
    // 0x8f4cdc: r0 = GDT[cid_x0 + -0xffd]()
    //     0x8f4cdc: sub             lr, x0, #0xffd
    //     0x8f4ce0: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4ce4: blr             lr
    // 0x8f4ce8: LeaveFrame
    //     0x8f4ce8: mov             SP, fp
    //     0x8f4cec: ldp             fp, lr, [SP], #0x10
    // 0x8f4cf0: ret
    //     0x8f4cf0: ret             
    // 0x8f4cf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4cf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4cf8: b               #0x8f4c70
  }
  get _ qurban(/* No info */) {
    // ** addr: 0x8f4cfc, size: 0xb0
    // 0x8f4cfc: EnterFrame
    //     0x8f4cfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4d00: mov             fp, SP
    // 0x8f4d04: AllocStack(0x30)
    //     0x8f4d04: sub             SP, SP, #0x30
    // 0x8f4d08: CheckStackOverflow
    //     0x8f4d08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4d0c: cmp             SP, x16
    //     0x8f4d10: b.ls            #0x8f4da4
    // 0x8f4d14: LoadField: r0 = r1->field_3f
    //     0x8f4d14: ldur            w0, [x1, #0x3f]
    // 0x8f4d18: DecompressPointer r0
    //     0x8f4d18: add             x0, x0, HEAP, lsl #32
    // 0x8f4d1c: cmp             w0, NULL
    // 0x8f4d20: b.ne            #0x8f4d2c
    // 0x8f4d24: r0 = Null
    //     0x8f4d24: mov             x0, NULL
    // 0x8f4d28: b               #0x8f4d98
    // 0x8f4d2c: LoadField: r3 = r0->field_3f
    //     0x8f4d2c: ldur            w3, [x0, #0x3f]
    // 0x8f4d30: DecompressPointer r3
    //     0x8f4d30: add             x3, x3, HEAP, lsl #32
    // 0x8f4d34: stur            x3, [fp, #-8]
    // 0x8f4d38: r1 = Function '<anonymous closure>':.
    //     0x8f4d38: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c00] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x8f4d3c: ldr             x1, [x1, #0xc00]
    // 0x8f4d40: r2 = Null
    //     0x8f4d40: mov             x2, NULL
    // 0x8f4d44: r0 = AllocateClosure()
    //     0x8f4d44: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4d48: r1 = Function '<anonymous closure>':.
    //     0x8f4d48: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c08] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8f4d4c: ldr             x1, [x1, #0xc08]
    // 0x8f4d50: r2 = Null
    //     0x8f4d50: mov             x2, NULL
    // 0x8f4d54: stur            x0, [fp, #-0x10]
    // 0x8f4d58: r0 = AllocateClosure()
    //     0x8f4d58: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f4d5c: mov             x1, x0
    // 0x8f4d60: ldur            x0, [fp, #-8]
    // 0x8f4d64: r2 = LoadClassIdInstr(r0)
    //     0x8f4d64: ldur            x2, [x0, #-1]
    //     0x8f4d68: ubfx            x2, x2, #0xc, #0x14
    // 0x8f4d6c: r16 = <CampaignMetadataQurban?>
    //     0x8f4d6c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34c10] TypeArguments: <CampaignMetadataQurban?>
    //     0x8f4d70: ldr             x16, [x16, #0xc10]
    // 0x8f4d74: stp             x0, x16, [SP, #0x10]
    // 0x8f4d78: ldur            x16, [fp, #-0x10]
    // 0x8f4d7c: stp             x1, x16, [SP]
    // 0x8f4d80: mov             x0, x2
    // 0x8f4d84: r4 = const [0x1, 0x3, 0x3, 0x1, qurban, 0x1, unknown, 0x2, null]
    //     0x8f4d84: add             x4, PP, #0x32, lsl #12  ; [pp+0x32928] List(9) [0x1, 0x3, 0x3, 0x1, "qurban", 0x1, "unknown", 0x2, Null]
    //     0x8f4d88: ldr             x4, [x4, #0x928]
    // 0x8f4d8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f4d8c: sub             lr, x0, #1, lsl #12
    //     0x8f4d90: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4d94: blr             lr
    // 0x8f4d98: LeaveFrame
    //     0x8f4d98: mov             SP, fp
    //     0x8f4d9c: ldp             fp, lr, [SP], #0x10
    // 0x8f4da0: ret
    //     0x8f4da0: ret             
    // 0x8f4da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4da4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4da8: b               #0x8f4d14
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x8f4dac, size: 0x26c
    // 0x8f4dac: EnterFrame
    //     0x8f4dac: stp             fp, lr, [SP, #-0x10]!
    //     0x8f4db0: mov             fp, SP
    // 0x8f4db4: AllocStack(0x28)
    //     0x8f4db4: sub             SP, SP, #0x28
    // 0x8f4db8: SetupParameters()
    //     0x8f4db8: ldr             x0, [fp, #0x18]
    //     0x8f4dbc: ldur            w2, [x0, #0x17]
    //     0x8f4dc0: add             x2, x2, HEAP, lsl #32
    //     0x8f4dc4: stur            x2, [fp, #-0x10]
    // 0x8f4dc8: CheckStackOverflow
    //     0x8f4dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4dcc: cmp             SP, x16
    //     0x8f4dd0: b.ls            #0x8f4ff0
    // 0x8f4dd4: ldr             x0, [fp, #0x10]
    // 0x8f4dd8: tbnz            w0, #4, #0x8f4e0c
    // 0x8f4ddc: LoadField: r0 = r2->field_f
    //     0x8f4ddc: ldur            w0, [x2, #0xf]
    // 0x8f4de0: DecompressPointer r0
    //     0x8f4de0: add             x0, x0, HEAP, lsl #32
    // 0x8f4de4: LoadField: r3 = r0->field_47
    //     0x8f4de4: ldur            w3, [x0, #0x47]
    // 0x8f4de8: DecompressPointer r3
    //     0x8f4de8: add             x3, x3, HEAP, lsl #32
    // 0x8f4dec: stur            x3, [fp, #-8]
    // 0x8f4df0: r1 = <String>
    //     0x8f4df0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f4df4: r2 = 0
    //     0x8f4df4: movz            x2, #0
    // 0x8f4df8: r0 = _GrowableList()
    //     0x8f4df8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8f4dfc: ldur            x1, [fp, #-8]
    // 0x8f4e00: mov             x2, x0
    // 0x8f4e04: r0 = value=()
    //     0x8f4e04: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8f4e08: b               #0x8f4fe0
    // 0x8f4e0c: LoadField: r0 = r2->field_f
    //     0x8f4e0c: ldur            w0, [x2, #0xf]
    // 0x8f4e10: DecompressPointer r0
    //     0x8f4e10: add             x0, x0, HEAP, lsl #32
    // 0x8f4e14: LoadField: r1 = r0->field_43
    //     0x8f4e14: ldur            w1, [x0, #0x43]
    // 0x8f4e18: DecompressPointer r1
    //     0x8f4e18: add             x1, x1, HEAP, lsl #32
    // 0x8f4e1c: r0 = value()
    //     0x8f4e1c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f4e20: r1 = LoadInt32Instr(r0)
    //     0x8f4e20: sbfx            x1, x0, #1, #0x1f
    //     0x8f4e24: tbz             w0, #0, #0x8f4e2c
    //     0x8f4e28: ldur            x1, [x0, #7]
    // 0x8f4e2c: cmp             x1, #1
    // 0x8f4e30: b.le            #0x8f4fe0
    // 0x8f4e34: ldur            x0, [fp, #-0x10]
    // 0x8f4e38: LoadField: r1 = r0->field_f
    //     0x8f4e38: ldur            w1, [x0, #0xf]
    // 0x8f4e3c: DecompressPointer r1
    //     0x8f4e3c: add             x1, x1, HEAP, lsl #32
    // 0x8f4e40: LoadField: r2 = r1->field_47
    //     0x8f4e40: ldur            w2, [x1, #0x47]
    // 0x8f4e44: DecompressPointer r2
    //     0x8f4e44: add             x2, x2, HEAP, lsl #32
    // 0x8f4e48: mov             x1, x2
    // 0x8f4e4c: r0 = value()
    //     0x8f4e4c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x8f4e50: r1 = LoadClassIdInstr(r0)
    //     0x8f4e50: ldur            x1, [x0, #-1]
    //     0x8f4e54: ubfx            x1, x1, #0xc, #0x14
    // 0x8f4e58: str             x0, [SP]
    // 0x8f4e5c: mov             x0, x1
    // 0x8f4e60: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8f4e60: movz            x17, #0xc834
    //     0x8f4e64: add             lr, x0, x17
    //     0x8f4e68: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4e6c: blr             lr
    // 0x8f4e70: mov             x2, x0
    // 0x8f4e74: ldur            x0, [fp, #-0x10]
    // 0x8f4e78: stur            x2, [fp, #-8]
    // 0x8f4e7c: LoadField: r1 = r0->field_f
    //     0x8f4e7c: ldur            w1, [x0, #0xf]
    // 0x8f4e80: DecompressPointer r1
    //     0x8f4e80: add             x1, x1, HEAP, lsl #32
    // 0x8f4e84: LoadField: r3 = r1->field_43
    //     0x8f4e84: ldur            w3, [x1, #0x43]
    // 0x8f4e88: DecompressPointer r3
    //     0x8f4e88: add             x3, x3, HEAP, lsl #32
    // 0x8f4e8c: mov             x1, x3
    // 0x8f4e90: r0 = value()
    //     0x8f4e90: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8f4e94: mov             x1, x0
    // 0x8f4e98: ldur            x0, [fp, #-8]
    // 0x8f4e9c: r2 = LoadInt32Instr(r0)
    //     0x8f4e9c: sbfx            x2, x0, #1, #0x1f
    //     0x8f4ea0: tbz             w0, #0, #0x8f4ea8
    //     0x8f4ea4: ldur            x2, [x0, #7]
    // 0x8f4ea8: r0 = LoadInt32Instr(r1)
    //     0x8f4ea8: sbfx            x0, x1, #1, #0x1f
    //     0x8f4eac: tbz             w1, #0, #0x8f4eb4
    //     0x8f4eb0: ldur            x0, [x1, #7]
    // 0x8f4eb4: cmp             x2, x0
    // 0x8f4eb8: b.ge            #0x8f4fe0
    // 0x8f4ebc: r3 = 0
    //     0x8f4ebc: movz            x3, #0
    // 0x8f4ec0: ldur            x0, [fp, #-0x10]
    // 0x8f4ec4: stur            x3, [fp, #-0x18]
    // 0x8f4ec8: CheckStackOverflow
    //     0x8f4ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f4ecc: cmp             SP, x16
    //     0x8f4ed0: b.ls            #0x8f4ff8
    // 0x8f4ed4: LoadField: r1 = r0->field_f
    //     0x8f4ed4: ldur            w1, [x0, #0xf]
    // 0x8f4ed8: DecompressPointer r1
    //     0x8f4ed8: add             x1, x1, HEAP, lsl #32
    // 0x8f4edc: LoadField: r4 = r1->field_43
    //     0x8f4edc: ldur            w4, [x1, #0x43]
    // 0x8f4ee0: DecompressPointer r4
    //     0x8f4ee0: add             x4, x4, HEAP, lsl #32
    // 0x8f4ee4: stur            x4, [fp, #-8]
    // 0x8f4ee8: r1 = LoadStaticField(0x137c)
    //     0x8f4ee8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x8f4eec: ldr             x1, [x1, #0x26f8]
    // 0x8f4ef0: cmp             w1, NULL
    // 0x8f4ef4: b.ne            #0x8f4f00
    // 0x8f4ef8: mov             x0, x4
    // 0x8f4efc: b               #0x8f4f14
    // 0x8f4f00: LoadField: r2 = r4->field_b
    //     0x8f4f00: ldur            w2, [x4, #0xb]
    // 0x8f4f04: DecompressPointer r2
    //     0x8f4f04: add             x2, x2, HEAP, lsl #32
    // 0x8f4f08: r0 = addListener()
    //     0x8f4f08: bl              #0x8f5018  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::addListener
    // 0x8f4f0c: ldur            x3, [fp, #-0x18]
    // 0x8f4f10: ldur            x0, [fp, #-8]
    // 0x8f4f14: LoadField: r1 = r0->field_13
    //     0x8f4f14: ldur            w1, [x0, #0x13]
    // 0x8f4f18: DecompressPointer r1
    //     0x8f4f18: add             x1, x1, HEAP, lsl #32
    // 0x8f4f1c: r16 = Sentinel
    //     0x8f4f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f4f20: cmp             w1, w16
    // 0x8f4f24: b.eq            #0x8f5000
    // 0x8f4f28: r0 = LoadInt32Instr(r1)
    //     0x8f4f28: sbfx            x0, x1, #1, #0x1f
    //     0x8f4f2c: tbz             w1, #0, #0x8f4f34
    //     0x8f4f30: ldur            x0, [x1, #7]
    // 0x8f4f34: sub             x1, x0, #1
    // 0x8f4f38: cmp             x3, x1
    // 0x8f4f3c: b.ge            #0x8f4fe0
    // 0x8f4f40: ldur            x4, [fp, #-0x10]
    // 0x8f4f44: LoadField: r0 = r4->field_f
    //     0x8f4f44: ldur            w0, [x4, #0xf]
    // 0x8f4f48: DecompressPointer r0
    //     0x8f4f48: add             x0, x0, HEAP, lsl #32
    // 0x8f4f4c: LoadField: r5 = r0->field_47
    //     0x8f4f4c: ldur            w5, [x0, #0x47]
    // 0x8f4f50: DecompressPointer r5
    //     0x8f4f50: add             x5, x5, HEAP, lsl #32
    // 0x8f4f54: stur            x5, [fp, #-8]
    // 0x8f4f58: LoadField: r2 = r5->field_7
    //     0x8f4f58: ldur            w2, [x5, #7]
    // 0x8f4f5c: DecompressPointer r2
    //     0x8f4f5c: add             x2, x2, HEAP, lsl #32
    // 0x8f4f60: r0 = ""
    //     0x8f4f60: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f4f64: r1 = Null
    //     0x8f4f64: mov             x1, NULL
    // 0x8f4f68: cmp             w2, NULL
    // 0x8f4f6c: b.eq            #0x8f4f8c
    // 0x8f4f70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8f4f70: ldur            w4, [x2, #0x17]
    // 0x8f4f74: DecompressPointer r4
    //     0x8f4f74: add             x4, x4, HEAP, lsl #32
    // 0x8f4f78: r8 = X0
    //     0x8f4f78: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8f4f7c: LoadField: r9 = r4->field_7
    //     0x8f4f7c: ldur            x9, [x4, #7]
    // 0x8f4f80: r3 = Null
    //     0x8f4f80: add             x3, PP, #0x35, lsl #12  ; [pp+0x35808] Null
    //     0x8f4f84: ldr             x3, [x3, #0x808]
    // 0x8f4f88: blr             x9
    // 0x8f4f8c: ldur            x1, [fp, #-8]
    // 0x8f4f90: LoadField: r0 = r1->field_13
    //     0x8f4f90: ldur            w0, [x1, #0x13]
    // 0x8f4f94: DecompressPointer r0
    //     0x8f4f94: add             x0, x0, HEAP, lsl #32
    // 0x8f4f98: r16 = Sentinel
    //     0x8f4f98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f4f9c: cmp             w0, w16
    // 0x8f4fa0: b.eq            #0x8f500c
    // 0x8f4fa4: r2 = LoadClassIdInstr(r0)
    //     0x8f4fa4: ldur            x2, [x0, #-1]
    //     0x8f4fa8: ubfx            x2, x2, #0xc, #0x14
    // 0x8f4fac: r16 = ""
    //     0x8f4fac: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f4fb0: stp             x16, x0, [SP]
    // 0x8f4fb4: mov             x0, x2
    // 0x8f4fb8: r0 = GDT[cid_x0 + 0x13254]()
    //     0x8f4fb8: movz            x17, #0x3254
    //     0x8f4fbc: movk            x17, #0x1, lsl #16
    //     0x8f4fc0: add             lr, x0, x17
    //     0x8f4fc4: ldr             lr, [x21, lr, lsl #3]
    //     0x8f4fc8: blr             lr
    // 0x8f4fcc: ldur            x1, [fp, #-8]
    // 0x8f4fd0: r0 = refresh()
    //     0x8f4fd0: bl              #0x6680f4  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::refresh
    // 0x8f4fd4: ldur            x1, [fp, #-0x18]
    // 0x8f4fd8: add             x3, x1, #1
    // 0x8f4fdc: b               #0x8f4ec0
    // 0x8f4fe0: r0 = Null
    //     0x8f4fe0: mov             x0, NULL
    // 0x8f4fe4: LeaveFrame
    //     0x8f4fe4: mov             SP, fp
    //     0x8f4fe8: ldp             fp, lr, [SP], #0x10
    // 0x8f4fec: ret
    //     0x8f4fec: ret             
    // 0x8f4ff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4ff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4ff4: b               #0x8f4dd4
    // 0x8f4ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f4ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f4ffc: b               #0x8f4ed4
    // 0x8f5000: r9 = _value
    //     0x8f5000: add             x9, PP, #8, lsl #12  ; [pp+0x8f80] Field <__RxImpl&RxNotifier&RxObjectMixin@1289443565._value@1289443565>: late (offset: 0x14)
    //     0x8f5004: ldr             x9, [x9, #0xf80]
    // 0x8f5008: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f5008: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8f500c: r9 = _value
    //     0x8f500c: add             x9, PP, #0x28, lsl #12  ; [pp+0x282d0] Field <_RxList&ListMixin&NotifyManager&RxObjectMixin@1289443565._value@1289443565>: late (offset: 0x14)
    //     0x8f5010: ldr             x9, [x9, #0x2d0]
    // 0x8f5014: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f5014: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ QurbanFormController(/* No info */) {
    // ** addr: 0x8f8868, size: 0xfc
    // 0x8f8868: EnterFrame
    //     0x8f8868: stp             fp, lr, [SP, #-0x10]!
    //     0x8f886c: mov             fp, SP
    // 0x8f8870: AllocStack(0x28)
    //     0x8f8870: sub             SP, SP, #0x28
    // 0x8f8874: SetupParameters(QurbanFormController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x8f8874: mov             x0, x2
    //     0x8f8878: stur            x2, [fp, #-0x10]
    //     0x8f887c: mov             x2, x3
    //     0x8f8880: stur            x3, [fp, #-0x18]
    //     0x8f8884: mov             x3, x1
    //     0x8f8888: stur            x1, [fp, #-8]
    // 0x8f888c: CheckStackOverflow
    //     0x8f888c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8890: cmp             SP, x16
    //     0x8f8894: b.ls            #0x8f895c
    // 0x8f8898: r1 = 1
    //     0x8f8898: movz            x1, #0x1
    // 0x8f889c: r0 = IntExtension.obs()
    //     0x8f889c: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x8f88a0: ldur            x3, [fp, #-8]
    // 0x8f88a4: StoreField: r3->field_43 = r0
    //     0x8f88a4: stur            w0, [x3, #0x43]
    //     0x8f88a8: ldurb           w16, [x3, #-1]
    //     0x8f88ac: ldurb           w17, [x0, #-1]
    //     0x8f88b0: and             x16, x17, x16, lsr #2
    //     0x8f88b4: tst             x16, HEAP, lsr #32
    //     0x8f88b8: b.eq            #0x8f88c0
    //     0x8f88bc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f88c0: r1 = <String>
    //     0x8f88c0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f88c4: r2 = 0
    //     0x8f88c4: movz            x2, #0
    // 0x8f88c8: r0 = _GrowableList()
    //     0x8f88c8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8f88cc: r16 = <String>
    //     0x8f88cc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f88d0: stp             x0, x16, [SP]
    // 0x8f88d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f88d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f88d8: r0 = ListExtension.obs()
    //     0x8f88d8: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x8f88dc: ldur            x2, [fp, #-8]
    // 0x8f88e0: StoreField: r2->field_47 = r0
    //     0x8f88e0: stur            w0, [x2, #0x47]
    //     0x8f88e4: ldurb           w16, [x2, #-1]
    //     0x8f88e8: ldurb           w17, [x0, #-1]
    //     0x8f88ec: and             x16, x17, x16, lsr #2
    //     0x8f88f0: tst             x16, HEAP, lsr #32
    //     0x8f88f4: b.eq            #0x8f88fc
    //     0x8f88f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f88fc: r1 = false
    //     0x8f88fc: add             x1, NULL, #0x30  ; false
    // 0x8f8900: r0 = BoolExtension.obs()
    //     0x8f8900: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x8f8904: ldur            x1, [fp, #-8]
    // 0x8f8908: StoreField: r1->field_4b = r0
    //     0x8f8908: stur            w0, [x1, #0x4b]
    //     0x8f890c: ldurb           w16, [x1, #-1]
    //     0x8f8910: ldurb           w17, [x0, #-1]
    //     0x8f8914: and             x16, x17, x16, lsr #2
    //     0x8f8918: tst             x16, HEAP, lsr #32
    //     0x8f891c: b.eq            #0x8f8924
    //     0x8f8920: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8924: ldur            x0, [fp, #-0x10]
    // 0x8f8928: StoreField: r1->field_3f = r0
    //     0x8f8928: stur            w0, [x1, #0x3f]
    //     0x8f892c: ldurb           w16, [x1, #-1]
    //     0x8f8930: ldurb           w17, [x0, #-1]
    //     0x8f8934: and             x16, x17, x16, lsr #2
    //     0x8f8938: tst             x16, HEAP, lsr #32
    //     0x8f893c: b.eq            #0x8f8944
    //     0x8f8940: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8944: ldur            x2, [fp, #-0x18]
    // 0x8f8948: r0 = DonationFormController()
    //     0x8f8948: bl              #0x8f8f2c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::DonationFormController
    // 0x8f894c: r0 = Null
    //     0x8f894c: mov             x0, NULL
    // 0x8f8950: LeaveFrame
    //     0x8f8950: mov             SP, fp
    //     0x8f8954: ldp             fp, lr, [SP], #0x10
    // 0x8f8958: ret
    //     0x8f8958: ret             
    // 0x8f895c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f895c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8960: b               #0x8f8898
  }
  get _ isValid(/* No info */) {
    // ** addr: 0xae53bc, size: 0x54
    // 0xae53bc: EnterFrame
    //     0xae53bc: stp             fp, lr, [SP, #-0x10]!
    //     0xae53c0: mov             fp, SP
    // 0xae53c4: AllocStack(0x8)
    //     0xae53c4: sub             SP, SP, #8
    // 0xae53c8: CheckStackOverflow
    //     0xae53c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae53cc: cmp             SP, x16
    //     0xae53d0: b.ls            #0xae5408
    // 0xae53d4: LoadField: r0 = r1->field_47
    //     0xae53d4: ldur            w0, [x1, #0x47]
    // 0xae53d8: DecompressPointer r0
    //     0xae53d8: add             x0, x0, HEAP, lsl #32
    // 0xae53dc: stur            x0, [fp, #-8]
    // 0xae53e0: r1 = Function '<anonymous closure>':.
    //     0xae53e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe50] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    //     0xae53e4: ldr             x1, [x1, #0xe50]
    // 0xae53e8: r2 = Null
    //     0xae53e8: mov             x2, NULL
    // 0xae53ec: r0 = AllocateClosure()
    //     0xae53ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xae53f0: ldur            x1, [fp, #-8]
    // 0xae53f4: mov             x2, x0
    // 0xae53f8: r0 = every()
    //     0xae53f8: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0xae53fc: LeaveFrame
    //     0xae53fc: mov             SP, fp
    //     0xae5400: ldp             fp, lr, [SP], #0x10
    // 0xae5404: ret
    //     0xae5404: ret             
    // 0xae5408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae540c: b               #0xae53d4
  }
  _ removePassenger(/* No info */) {
    // ** addr: 0xaec2dc, size: 0x8c
    // 0xaec2dc: EnterFrame
    //     0xaec2dc: stp             fp, lr, [SP, #-0x10]!
    //     0xaec2e0: mov             fp, SP
    // 0xaec2e4: AllocStack(0x10)
    //     0xaec2e4: sub             SP, SP, #0x10
    // 0xaec2e8: SetupParameters(QurbanFormController this /* r1 => r0, fp-0x8 */)
    //     0xaec2e8: mov             x0, x1
    //     0xaec2ec: stur            x1, [fp, #-8]
    // 0xaec2f0: CheckStackOverflow
    //     0xaec2f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec2f4: cmp             SP, x16
    //     0xaec2f8: b.ls            #0xaec360
    // 0xaec2fc: LoadField: r1 = r0->field_47
    //     0xaec2fc: ldur            w1, [x0, #0x47]
    // 0xaec300: DecompressPointer r1
    //     0xaec300: add             x1, x1, HEAP, lsl #32
    // 0xaec304: r0 = removeAt()
    //     0xaec304: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xaec308: ldur            x0, [fp, #-8]
    // 0xaec30c: LoadField: r2 = r0->field_43
    //     0xaec30c: ldur            w2, [x0, #0x43]
    // 0xaec310: DecompressPointer r2
    //     0xaec310: add             x2, x2, HEAP, lsl #32
    // 0xaec314: mov             x1, x2
    // 0xaec318: stur            x2, [fp, #-0x10]
    // 0xaec31c: r0 = value()
    //     0xaec31c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaec320: r1 = LoadInt32Instr(r0)
    //     0xaec320: sbfx            x1, x0, #1, #0x1f
    //     0xaec324: tbz             w0, #0, #0xaec32c
    //     0xaec328: ldur            x1, [x0, #7]
    // 0xaec32c: sub             x2, x1, #1
    // 0xaec330: r0 = BoxInt64Instr(r2)
    //     0xaec330: sbfiz           x0, x2, #1, #0x1f
    //     0xaec334: cmp             x2, x0, asr #1
    //     0xaec338: b.eq            #0xaec344
    //     0xaec33c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaec340: stur            x2, [x0, #7]
    // 0xaec344: ldur            x1, [fp, #-0x10]
    // 0xaec348: mov             x2, x0
    // 0xaec34c: r0 = value=()
    //     0xaec34c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaec350: r0 = Null
    //     0xaec350: mov             x0, NULL
    // 0xaec354: LeaveFrame
    //     0xaec354: mov             SP, fp
    //     0xaec358: ldp             fp, lr, [SP], #0x10
    // 0xaec35c: ret
    //     0xaec35c: ret             
    // 0xaec360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec364: b               #0xaec2fc
  }
  _ updatePassenger(/* No info */) {
    // ** addr: 0xaec6ac, size: 0x58
    // 0xaec6ac: EnterFrame
    //     0xaec6ac: stp             fp, lr, [SP, #-0x10]!
    //     0xaec6b0: mov             fp, SP
    // 0xaec6b4: AllocStack(0x18)
    //     0xaec6b4: sub             SP, SP, #0x18
    // 0xaec6b8: CheckStackOverflow
    //     0xaec6b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec6bc: cmp             SP, x16
    //     0xaec6c0: b.ls            #0xaec6fc
    // 0xaec6c4: LoadField: r4 = r1->field_47
    //     0xaec6c4: ldur            w4, [x1, #0x47]
    // 0xaec6c8: DecompressPointer r4
    //     0xaec6c8: add             x4, x4, HEAP, lsl #32
    // 0xaec6cc: r0 = BoxInt64Instr(r2)
    //     0xaec6cc: sbfiz           x0, x2, #1, #0x1f
    //     0xaec6d0: cmp             x2, x0, asr #1
    //     0xaec6d4: b.eq            #0xaec6e0
    //     0xaec6d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaec6dc: stur            x2, [x0, #7]
    // 0xaec6e0: stp             x0, x4, [SP, #8]
    // 0xaec6e4: str             x3, [SP]
    // 0xaec6e8: r0 = []=()
    //     0xaec6e8: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xaec6ec: r0 = Null
    //     0xaec6ec: mov             x0, NULL
    // 0xaec6f0: LeaveFrame
    //     0xaec6f0: mov             SP, fp
    //     0xaec6f4: ldp             fp, lr, [SP], #0x10
    // 0xaec6f8: ret
    //     0xaec6f8: ret             
    // 0xaec6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec6fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec700: b               #0xaec6c4
  }
  [closure] void onIncrementPressed(dynamic) {
    // ** addr: 0xaedabc, size: 0x38
    // 0xaedabc: EnterFrame
    //     0xaedabc: stp             fp, lr, [SP, #-0x10]!
    //     0xaedac0: mov             fp, SP
    // 0xaedac4: ldr             x0, [fp, #0x10]
    // 0xaedac8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaedac8: ldur            w1, [x0, #0x17]
    // 0xaedacc: DecompressPointer r1
    //     0xaedacc: add             x1, x1, HEAP, lsl #32
    // 0xaedad0: CheckStackOverflow
    //     0xaedad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedad4: cmp             SP, x16
    //     0xaedad8: b.ls            #0xaedaec
    // 0xaedadc: r0 = onIncrementPressed()
    //     0xaedadc: bl              #0xaedaf4  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onIncrementPressed
    // 0xaedae0: LeaveFrame
    //     0xaedae0: mov             SP, fp
    //     0xaedae4: ldp             fp, lr, [SP], #0x10
    // 0xaedae8: ret
    //     0xaedae8: ret             
    // 0xaedaec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedaec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedaf0: b               #0xaedadc
  }
  _ onIncrementPressed(/* No info */) {
    // ** addr: 0xaedaf4, size: 0xa8
    // 0xaedaf4: EnterFrame
    //     0xaedaf4: stp             fp, lr, [SP, #-0x10]!
    //     0xaedaf8: mov             fp, SP
    // 0xaedafc: AllocStack(0x20)
    //     0xaedafc: sub             SP, SP, #0x20
    // 0xaedb00: SetupParameters(QurbanFormController this /* r1 => r0, fp-0x10 */)
    //     0xaedb00: mov             x0, x1
    //     0xaedb04: stur            x1, [fp, #-0x10]
    // 0xaedb08: CheckStackOverflow
    //     0xaedb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedb0c: cmp             SP, x16
    //     0xaedb10: b.ls            #0xaedb94
    // 0xaedb14: LoadField: r2 = r0->field_43
    //     0xaedb14: ldur            w2, [x0, #0x43]
    // 0xaedb18: DecompressPointer r2
    //     0xaedb18: add             x2, x2, HEAP, lsl #32
    // 0xaedb1c: mov             x1, x2
    // 0xaedb20: stur            x2, [fp, #-8]
    // 0xaedb24: r0 = value()
    //     0xaedb24: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaedb28: r1 = LoadInt32Instr(r0)
    //     0xaedb28: sbfx            x1, x0, #1, #0x1f
    //     0xaedb2c: tbz             w0, #0, #0xaedb34
    //     0xaedb30: ldur            x1, [x0, #7]
    // 0xaedb34: add             x2, x1, #1
    // 0xaedb38: r0 = BoxInt64Instr(r2)
    //     0xaedb38: sbfiz           x0, x2, #1, #0x1f
    //     0xaedb3c: cmp             x2, x0, asr #1
    //     0xaedb40: b.eq            #0xaedb4c
    //     0xaedb44: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaedb48: stur            x2, [x0, #7]
    // 0xaedb4c: ldur            x1, [fp, #-8]
    // 0xaedb50: mov             x2, x0
    // 0xaedb54: r0 = value=()
    //     0xaedb54: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaedb58: ldur            x0, [fp, #-0x10]
    // 0xaedb5c: LoadField: r1 = r0->field_4b
    //     0xaedb5c: ldur            w1, [x0, #0x4b]
    // 0xaedb60: DecompressPointer r1
    //     0xaedb60: add             x1, x1, HEAP, lsl #32
    // 0xaedb64: r0 = RxBoolExt.isFalse()
    //     0xaedb64: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xaedb68: tbnz            w0, #4, #0xaedb84
    // 0xaedb6c: ldur            x0, [fp, #-0x10]
    // 0xaedb70: LoadField: r1 = r0->field_47
    //     0xaedb70: ldur            w1, [x0, #0x47]
    // 0xaedb74: DecompressPointer r1
    //     0xaedb74: add             x1, x1, HEAP, lsl #32
    // 0xaedb78: r16 = ""
    //     0xaedb78: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaedb7c: stp             x16, x1, [SP]
    // 0xaedb80: r0 = add()
    //     0xaedb80: bl              #0x66b5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0xaedb84: r0 = Null
    //     0xaedb84: mov             x0, NULL
    // 0xaedb88: LeaveFrame
    //     0xaedb88: mov             SP, fp
    //     0xaedb8c: ldp             fp, lr, [SP], #0x10
    // 0xaedb90: ret
    //     0xaedb90: ret             
    // 0xaedb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedb94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedb98: b               #0xaedb14
  }
  [closure] void onDecrementPressed(dynamic) {
    // ** addr: 0xaedb9c, size: 0x38
    // 0xaedb9c: EnterFrame
    //     0xaedb9c: stp             fp, lr, [SP, #-0x10]!
    //     0xaedba0: mov             fp, SP
    // 0xaedba4: ldr             x0, [fp, #0x10]
    // 0xaedba8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaedba8: ldur            w1, [x0, #0x17]
    // 0xaedbac: DecompressPointer r1
    //     0xaedbac: add             x1, x1, HEAP, lsl #32
    // 0xaedbb0: CheckStackOverflow
    //     0xaedbb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedbb4: cmp             SP, x16
    //     0xaedbb8: b.ls            #0xaedbcc
    // 0xaedbbc: r0 = onDecrementPressed()
    //     0xaedbbc: bl              #0xaedbd4  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onDecrementPressed
    // 0xaedbc0: LeaveFrame
    //     0xaedbc0: mov             SP, fp
    //     0xaedbc4: ldp             fp, lr, [SP], #0x10
    // 0xaedbc8: ret
    //     0xaedbc8: ret             
    // 0xaedbcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedbcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedbd0: b               #0xaedbbc
  }
  _ onDecrementPressed(/* No info */) {
    // ** addr: 0xaedbd4, size: 0xc4
    // 0xaedbd4: EnterFrame
    //     0xaedbd4: stp             fp, lr, [SP, #-0x10]!
    //     0xaedbd8: mov             fp, SP
    // 0xaedbdc: AllocStack(0x18)
    //     0xaedbdc: sub             SP, SP, #0x18
    // 0xaedbe0: SetupParameters(QurbanFormController this /* r1 => r0, fp-0x10 */)
    //     0xaedbe0: mov             x0, x1
    //     0xaedbe4: stur            x1, [fp, #-0x10]
    // 0xaedbe8: CheckStackOverflow
    //     0xaedbe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedbec: cmp             SP, x16
    //     0xaedbf0: b.ls            #0xaedc90
    // 0xaedbf4: LoadField: r2 = r0->field_43
    //     0xaedbf4: ldur            w2, [x0, #0x43]
    // 0xaedbf8: DecompressPointer r2
    //     0xaedbf8: add             x2, x2, HEAP, lsl #32
    // 0xaedbfc: mov             x1, x2
    // 0xaedc00: stur            x2, [fp, #-8]
    // 0xaedc04: r0 = value()
    //     0xaedc04: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaedc08: r1 = LoadInt32Instr(r0)
    //     0xaedc08: sbfx            x1, x0, #1, #0x1f
    //     0xaedc0c: tbz             w0, #0, #0xaedc14
    //     0xaedc10: ldur            x1, [x0, #7]
    // 0xaedc14: sub             x2, x1, #1
    // 0xaedc18: r0 = BoxInt64Instr(r2)
    //     0xaedc18: sbfiz           x0, x2, #1, #0x1f
    //     0xaedc1c: cmp             x2, x0, asr #1
    //     0xaedc20: b.eq            #0xaedc2c
    //     0xaedc24: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaedc28: stur            x2, [x0, #7]
    // 0xaedc2c: ldur            x1, [fp, #-8]
    // 0xaedc30: mov             x2, x0
    // 0xaedc34: r0 = value=()
    //     0xaedc34: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaedc38: ldur            x0, [fp, #-0x10]
    // 0xaedc3c: LoadField: r2 = r0->field_4b
    //     0xaedc3c: ldur            w2, [x0, #0x4b]
    // 0xaedc40: DecompressPointer r2
    //     0xaedc40: add             x2, x2, HEAP, lsl #32
    // 0xaedc44: mov             x1, x2
    // 0xaedc48: stur            x2, [fp, #-0x18]
    // 0xaedc4c: r0 = RxBoolExt.isFalse()
    //     0xaedc4c: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xaedc50: tbnz            w0, #4, #0xaedc64
    // 0xaedc54: ldur            x0, [fp, #-0x10]
    // 0xaedc58: LoadField: r1 = r0->field_47
    //     0xaedc58: ldur            w1, [x0, #0x47]
    // 0xaedc5c: DecompressPointer r1
    //     0xaedc5c: add             x1, x1, HEAP, lsl #32
    // 0xaedc60: r0 = removeLast()
    //     0xaedc60: bl              #0x66b2f4  ; [dart:collection] ListBase::removeLast
    // 0xaedc64: ldur            x1, [fp, #-8]
    // 0xaedc68: r0 = value()
    //     0xaedc68: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaedc6c: cmp             w0, #2
    // 0xaedc70: b.ne            #0xaedc80
    // 0xaedc74: ldur            x1, [fp, #-0x18]
    // 0xaedc78: r2 = false
    //     0xaedc78: add             x2, NULL, #0x30  ; false
    // 0xaedc7c: r0 = value=()
    //     0xaedc7c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaedc80: r0 = Null
    //     0xaedc80: mov             x0, NULL
    // 0xaedc84: LeaveFrame
    //     0xaedc84: mov             SP, fp
    //     0xaedc88: ldp             fp, lr, [SP], #0x10
    // 0xaedc8c: ret
    //     0xaedc8c: ret             
    // 0xaedc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedc90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedc94: b               #0xaedbf4
  }
  get _ trx(/* No info */) {
    // ** addr: 0xe35480, size: 0x350
    // 0xe35480: EnterFrame
    //     0xe35480: stp             fp, lr, [SP, #-0x10]!
    //     0xe35484: mov             fp, SP
    // 0xe35488: AllocStack(0x68)
    //     0xe35488: sub             SP, SP, #0x68
    // 0xe3548c: SetupParameters(QurbanFormController this /* r1 => r0, fp-0x10 */)
    //     0xe3548c: mov             x0, x1
    //     0xe35490: stur            x1, [fp, #-0x10]
    // 0xe35494: CheckStackOverflow
    //     0xe35494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe35498: cmp             SP, x16
    //     0xe3549c: b.ls            #0xe357c8
    // 0xe354a0: LoadField: r3 = r0->field_3f
    //     0xe354a0: ldur            w3, [x0, #0x3f]
    // 0xe354a4: DecompressPointer r3
    //     0xe354a4: add             x3, x3, HEAP, lsl #32
    // 0xe354a8: stur            x3, [fp, #-8]
    // 0xe354ac: cmp             w3, NULL
    // 0xe354b0: b.ne            #0xe354bc
    // 0xe354b4: r0 = Null
    //     0xe354b4: mov             x0, NULL
    // 0xe354b8: b               #0xe354d4
    // 0xe354bc: LoadField: r1 = r3->field_f
    //     0xe354bc: ldur            w1, [x3, #0xf]
    // 0xe354c0: DecompressPointer r1
    //     0xe354c0: add             x1, x1, HEAP, lsl #32
    // 0xe354c4: r2 = "1/7"
    //     0xe354c4: add             x2, PP, #0x35, lsl #12  ; [pp+0x357d8] "1/7"
    //     0xe354c8: ldr             x2, [x2, #0x7d8]
    // 0xe354cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe354cc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe354d0: r0 = startsWith()
    //     0xe354d0: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe354d4: cmp             w0, NULL
    // 0xe354d8: b.eq            #0xe35550
    // 0xe354dc: tbnz            w0, #4, #0xe35550
    // 0xe354e0: ldur            x0, [fp, #-0x10]
    // 0xe354e4: LoadField: r2 = r0->field_43
    //     0xe354e4: ldur            w2, [x0, #0x43]
    // 0xe354e8: DecompressPointer r2
    //     0xe354e8: add             x2, x2, HEAP, lsl #32
    // 0xe354ec: mov             x1, x2
    // 0xe354f0: stur            x2, [fp, #-0x18]
    // 0xe354f4: r0 = value()
    //     0xe354f4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe354f8: r1 = LoadInt32Instr(r0)
    //     0xe354f8: sbfx            x1, x0, #1, #0x1f
    //     0xe354fc: tbz             w0, #0, #0xe35504
    //     0xe35500: ldur            x1, [x0, #7]
    // 0xe35504: cmp             x1, #1
    // 0xe35508: b.le            #0xe35548
    // 0xe3550c: ldur            x1, [fp, #-0x18]
    // 0xe35510: r0 = value()
    //     0xe35510: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35514: r1 = Null
    //     0xe35514: mov             x1, NULL
    // 0xe35518: r2 = 4
    //     0xe35518: movz            x2, #0x4
    // 0xe3551c: stur            x0, [fp, #-0x18]
    // 0xe35520: r0 = AllocateArray()
    //     0xe35520: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe35524: mov             x1, x0
    // 0xe35528: ldur            x0, [fp, #-0x18]
    // 0xe3552c: StoreField: r1->field_f = r0
    //     0xe3552c: stur            w0, [x1, #0xf]
    // 0xe35530: r16 = " x"
    //     0xe35530: add             x16, PP, #0x35, lsl #12  ; [pp+0x357e0] " x"
    //     0xe35534: ldr             x16, [x16, #0x7e0]
    // 0xe35538: StoreField: r1->field_13 = r16
    //     0xe35538: stur            w16, [x1, #0x13]
    // 0xe3553c: str             x1, [SP]
    // 0xe35540: r0 = _interpolate()
    //     0xe35540: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe35544: b               #0xe35590
    // 0xe35548: r0 = Null
    //     0xe35548: mov             x0, NULL
    // 0xe3554c: b               #0xe35590
    // 0xe35550: ldur            x0, [fp, #-0x10]
    // 0xe35554: LoadField: r1 = r0->field_43
    //     0xe35554: ldur            w1, [x0, #0x43]
    // 0xe35558: DecompressPointer r1
    //     0xe35558: add             x1, x1, HEAP, lsl #32
    // 0xe3555c: r0 = value()
    //     0xe3555c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35560: r1 = Null
    //     0xe35560: mov             x1, NULL
    // 0xe35564: r2 = 4
    //     0xe35564: movz            x2, #0x4
    // 0xe35568: stur            x0, [fp, #-0x18]
    // 0xe3556c: r0 = AllocateArray()
    //     0xe3556c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe35570: mov             x1, x0
    // 0xe35574: ldur            x0, [fp, #-0x18]
    // 0xe35578: StoreField: r1->field_f = r0
    //     0xe35578: stur            w0, [x1, #0xf]
    // 0xe3557c: r16 = " Ekor"
    //     0xe3557c: add             x16, PP, #0x35, lsl #12  ; [pp+0x357e8] " Ekor"
    //     0xe35580: ldr             x16, [x16, #0x7e8]
    // 0xe35584: StoreField: r1->field_13 = r16
    //     0xe35584: stur            w16, [x1, #0x13]
    // 0xe35588: str             x1, [SP]
    // 0xe3558c: r0 = _interpolate()
    //     0xe3558c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe35590: ldur            x1, [fp, #-0x10]
    // 0xe35594: stur            x0, [fp, #-0x18]
    // 0xe35598: r0 = trx()
    //     0xe35598: bl              #0xe35bf0  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::trx
    // 0xe3559c: r1 = <String?>
    //     0xe3559c: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xe355a0: r2 = 0
    //     0xe355a0: movz            x2, #0
    // 0xe355a4: stur            x0, [fp, #-0x20]
    // 0xe355a8: r0 = _GrowableList()
    //     0xe355a8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe355ac: mov             x2, x0
    // 0xe355b0: ldur            x0, [fp, #-0x18]
    // 0xe355b4: stur            x2, [fp, #-0x30]
    // 0xe355b8: cmp             w0, NULL
    // 0xe355bc: b.eq            #0xe35634
    // 0xe355c0: LoadField: r1 = r2->field_b
    //     0xe355c0: ldur            w1, [x2, #0xb]
    // 0xe355c4: LoadField: r3 = r2->field_f
    //     0xe355c4: ldur            w3, [x2, #0xf]
    // 0xe355c8: DecompressPointer r3
    //     0xe355c8: add             x3, x3, HEAP, lsl #32
    // 0xe355cc: LoadField: r4 = r3->field_b
    //     0xe355cc: ldur            w4, [x3, #0xb]
    // 0xe355d0: r3 = LoadInt32Instr(r1)
    //     0xe355d0: sbfx            x3, x1, #1, #0x1f
    // 0xe355d4: stur            x3, [fp, #-0x28]
    // 0xe355d8: r1 = LoadInt32Instr(r4)
    //     0xe355d8: sbfx            x1, x4, #1, #0x1f
    // 0xe355dc: cmp             x3, x1
    // 0xe355e0: b.ne            #0xe355ec
    // 0xe355e4: mov             x1, x2
    // 0xe355e8: r0 = _growToNextCapacity()
    //     0xe355e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe355ec: ldur            x2, [fp, #-0x30]
    // 0xe355f0: ldur            x3, [fp, #-0x28]
    // 0xe355f4: add             x0, x3, #1
    // 0xe355f8: lsl             x1, x0, #1
    // 0xe355fc: StoreField: r2->field_b = r1
    //     0xe355fc: stur            w1, [x2, #0xb]
    // 0xe35600: LoadField: r1 = r2->field_f
    //     0xe35600: ldur            w1, [x2, #0xf]
    // 0xe35604: DecompressPointer r1
    //     0xe35604: add             x1, x1, HEAP, lsl #32
    // 0xe35608: ldur            x0, [fp, #-0x18]
    // 0xe3560c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe3560c: add             x25, x1, x3, lsl #2
    //     0xe35610: add             x25, x25, #0xf
    //     0xe35614: str             w0, [x25]
    //     0xe35618: tbz             w0, #0, #0xe35634
    //     0xe3561c: ldurb           w16, [x1, #-1]
    //     0xe35620: ldurb           w17, [x0, #-1]
    //     0xe35624: and             x16, x17, x16, lsr #2
    //     0xe35628: tst             x16, HEAP, lsr #32
    //     0xe3562c: b.eq            #0xe35634
    //     0xe35630: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe35634: ldur            x0, [fp, #-8]
    // 0xe35638: cmp             w0, NULL
    // 0xe3563c: b.ne            #0xe35648
    // 0xe35640: r3 = Null
    //     0xe35640: mov             x3, NULL
    // 0xe35644: b               #0xe35654
    // 0xe35648: LoadField: r1 = r0->field_f
    //     0xe35648: ldur            w1, [x0, #0xf]
    // 0xe3564c: DecompressPointer r1
    //     0xe3564c: add             x1, x1, HEAP, lsl #32
    // 0xe35650: mov             x3, x1
    // 0xe35654: stur            x3, [fp, #-0x18]
    // 0xe35658: LoadField: r1 = r2->field_b
    //     0xe35658: ldur            w1, [x2, #0xb]
    // 0xe3565c: LoadField: r4 = r2->field_f
    //     0xe3565c: ldur            w4, [x2, #0xf]
    // 0xe35660: DecompressPointer r4
    //     0xe35660: add             x4, x4, HEAP, lsl #32
    // 0xe35664: LoadField: r5 = r4->field_b
    //     0xe35664: ldur            w5, [x4, #0xb]
    // 0xe35668: r4 = LoadInt32Instr(r1)
    //     0xe35668: sbfx            x4, x1, #1, #0x1f
    // 0xe3566c: stur            x4, [fp, #-0x28]
    // 0xe35670: r1 = LoadInt32Instr(r5)
    //     0xe35670: sbfx            x1, x5, #1, #0x1f
    // 0xe35674: cmp             x4, x1
    // 0xe35678: b.ne            #0xe35684
    // 0xe3567c: mov             x1, x2
    // 0xe35680: r0 = _growToNextCapacity()
    //     0xe35680: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe35684: ldur            x4, [fp, #-0x10]
    // 0xe35688: ldur            x2, [fp, #-0x30]
    // 0xe3568c: ldur            x3, [fp, #-0x28]
    // 0xe35690: add             x0, x3, #1
    // 0xe35694: lsl             x1, x0, #1
    // 0xe35698: StoreField: r2->field_b = r1
    //     0xe35698: stur            w1, [x2, #0xb]
    // 0xe3569c: LoadField: r1 = r2->field_f
    //     0xe3569c: ldur            w1, [x2, #0xf]
    // 0xe356a0: DecompressPointer r1
    //     0xe356a0: add             x1, x1, HEAP, lsl #32
    // 0xe356a4: ldur            x0, [fp, #-0x18]
    // 0xe356a8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe356a8: add             x25, x1, x3, lsl #2
    //     0xe356ac: add             x25, x25, #0xf
    //     0xe356b0: str             w0, [x25]
    //     0xe356b4: tbz             w0, #0, #0xe356d0
    //     0xe356b8: ldurb           w16, [x1, #-1]
    //     0xe356bc: ldurb           w17, [x0, #-1]
    //     0xe356c0: and             x16, x17, x16, lsr #2
    //     0xe356c4: tst             x16, HEAP, lsr #32
    //     0xe356c8: b.eq            #0xe356d0
    //     0xe356cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe356d0: r16 = " "
    //     0xe356d0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe356d4: str             x16, [SP]
    // 0xe356d8: mov             x1, x2
    // 0xe356dc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe356dc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe356e0: r0 = join()
    //     0xe356e0: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xe356e4: ldur            x1, [fp, #-0x10]
    // 0xe356e8: stur            x0, [fp, #-0x18]
    // 0xe356ec: r0 = subtotal()
    //     0xe356ec: bl              #0x8f4c50  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::subtotal
    // 0xe356f0: mov             x2, x0
    // 0xe356f4: ldur            x0, [fp, #-0x10]
    // 0xe356f8: stur            x2, [fp, #-0x30]
    // 0xe356fc: LoadField: r1 = r0->field_27
    //     0xe356fc: ldur            w1, [x0, #0x27]
    // 0xe35700: DecompressPointer r1
    //     0xe35700: add             x1, x1, HEAP, lsl #32
    // 0xe35704: r0 = value()
    //     0xe35704: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35708: r1 = Null
    //     0xe35708: mov             x1, NULL
    // 0xe3570c: r2 = 2
    //     0xe3570c: movz            x2, #0x2
    // 0xe35710: stur            x0, [fp, #-0x38]
    // 0xe35714: r0 = AllocateArray()
    //     0xe35714: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe35718: mov             x2, x0
    // 0xe3571c: ldur            x0, [fp, #-0x38]
    // 0xe35720: stur            x2, [fp, #-0x40]
    // 0xe35724: StoreField: r2->field_f = r0
    //     0xe35724: stur            w0, [x2, #0xf]
    // 0xe35728: r1 = <String>
    //     0xe35728: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe3572c: r0 = AllocateGrowableArray()
    //     0xe3572c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe35730: mov             x3, x0
    // 0xe35734: ldur            x0, [fp, #-0x40]
    // 0xe35738: stur            x3, [fp, #-0x38]
    // 0xe3573c: StoreField: r3->field_f = r0
    //     0xe3573c: stur            w0, [x3, #0xf]
    // 0xe35740: r0 = 2
    //     0xe35740: movz            x0, #0x2
    // 0xe35744: StoreField: r3->field_b = r0
    //     0xe35744: stur            w0, [x3, #0xb]
    // 0xe35748: ldur            x0, [fp, #-0x10]
    // 0xe3574c: LoadField: r2 = r0->field_47
    //     0xe3574c: ldur            w2, [x0, #0x47]
    // 0xe35750: DecompressPointer r2
    //     0xe35750: add             x2, x2, HEAP, lsl #32
    // 0xe35754: mov             x1, x3
    // 0xe35758: r0 = addAll()
    //     0xe35758: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xe3575c: r16 = ", "
    //     0xe3575c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xe35760: str             x16, [SP]
    // 0xe35764: ldur            x1, [fp, #-0x38]
    // 0xe35768: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe35768: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe3576c: r0 = join()
    //     0xe3576c: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xe35770: mov             x2, x0
    // 0xe35774: ldur            x0, [fp, #-0x10]
    // 0xe35778: stur            x2, [fp, #-0x38]
    // 0xe3577c: LoadField: r1 = r0->field_2b
    //     0xe3577c: ldur            w1, [x0, #0x2b]
    // 0xe35780: DecompressPointer r1
    //     0xe35780: add             x1, x1, HEAP, lsl #32
    // 0xe35784: r0 = value()
    //     0xe35784: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35788: ldur            x16, [fp, #-0x18]
    // 0xe3578c: ldur            lr, [fp, #-8]
    // 0xe35790: stp             lr, x16, [SP, #0x18]
    // 0xe35794: ldur            x16, [fp, #-0x30]
    // 0xe35798: ldur            lr, [fp, #-0x38]
    // 0xe3579c: stp             lr, x16, [SP, #8]
    // 0xe357a0: str             x0, [SP]
    // 0xe357a4: ldur            x1, [fp, #-0x20]
    // 0xe357a8: r2 = Instance_PaymentType
    //     0xe357a8: add             x2, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xe357ac: ldr             x2, [x2, #0x5f0]
    // 0xe357b0: r4 = const [0, 0x7, 0x5, 0x2, amount, 0x4, campaign, 0x3, description, 0x2, isAnon, 0x6, name, 0x5, null]
    //     0xe357b0: add             x4, PP, #0x35, lsl #12  ; [pp+0x357f0] List(15) [0, 0x7, 0x5, 0x2, "amount", 0x4, "campaign", 0x3, "description", 0x2, "isAnon", 0x6, "name", 0x5, Null]
    //     0xe357b4: ldr             x4, [x4, #0x7f0]
    // 0xe357b8: r0 = copyWith()
    //     0xe357b8: bl              #0xae5680  ; [package:nuonline/app/data/models/transaction.dart] TransactionRequest::copyWith
    // 0xe357bc: LeaveFrame
    //     0xe357bc: mov             SP, fp
    //     0xe357c0: ldp             fp, lr, [SP], #0x10
    // 0xe357c4: ret
    //     0xe357c4: ret             
    // 0xe357c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe357c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe357cc: b               #0xe354a0
  }
}
