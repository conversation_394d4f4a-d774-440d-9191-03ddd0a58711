// lib: , url: package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart

// class id: 1050215, size: 0x8
class :: {
}

// class id: 1898, size: 0x38, field offset: 0x20
//   transformed mixin,
abstract class _TransactionHistoryController&GetxController&PagingMixin extends GetxController
     with PagingMixin<X0> {

  _ _TransactionHistoryController&GetxController&PagingMixin(/* No info */) {
    // ** addr: 0x811dd0, size: 0x148
    // 0x811dd0: EnterFrame
    //     0x811dd0: stp             fp, lr, [SP, #-0x10]!
    //     0x811dd4: mov             fp, SP
    // 0x811dd8: AllocStack(0x18)
    //     0x811dd8: sub             SP, SP, #0x18
    // 0x811ddc: SetupParameters(_TransactionHistoryController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x811ddc: mov             x0, x1
    //     0x811de0: stur            x1, [fp, #-8]
    // 0x811de4: CheckStackOverflow
    //     0x811de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811de8: cmp             SP, x16
    //     0x811dec: b.ls            #0x811f10
    // 0x811df0: r1 = 1
    //     0x811df0: movz            x1, #0x1
    // 0x811df4: r0 = IntExtension.obs()
    //     0x811df4: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x811df8: ldur            x2, [fp, #-8]
    // 0x811dfc: StoreField: r2->field_1f = r0
    //     0x811dfc: stur            w0, [x2, #0x1f]
    //     0x811e00: ldurb           w16, [x2, #-1]
    //     0x811e04: ldurb           w17, [x0, #-1]
    //     0x811e08: and             x16, x17, x16, lsr #2
    //     0x811e0c: tst             x16, HEAP, lsr #32
    //     0x811e10: b.eq            #0x811e18
    //     0x811e14: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x811e18: r1 = true
    //     0x811e18: add             x1, NULL, #0x20  ; true
    // 0x811e1c: r0 = BoolExtension.obs()
    //     0x811e1c: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x811e20: ldur            x2, [fp, #-8]
    // 0x811e24: StoreField: r2->field_23 = r0
    //     0x811e24: stur            w0, [x2, #0x23]
    //     0x811e28: ldurb           w16, [x2, #-1]
    //     0x811e2c: ldurb           w17, [x0, #-1]
    //     0x811e30: and             x16, x17, x16, lsr #2
    //     0x811e34: tst             x16, HEAP, lsr #32
    //     0x811e38: b.eq            #0x811e40
    //     0x811e3c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x811e40: r1 = false
    //     0x811e40: add             x1, NULL, #0x30  ; false
    // 0x811e44: r0 = BoolExtension.obs()
    //     0x811e44: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x811e48: ldur            x2, [fp, #-8]
    // 0x811e4c: StoreField: r2->field_27 = r0
    //     0x811e4c: stur            w0, [x2, #0x27]
    //     0x811e50: ldurb           w16, [x2, #-1]
    //     0x811e54: ldurb           w17, [x0, #-1]
    //     0x811e58: and             x16, x17, x16, lsr #2
    //     0x811e5c: tst             x16, HEAP, lsr #32
    //     0x811e60: b.eq            #0x811e68
    //     0x811e64: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x811e68: r1 = false
    //     0x811e68: add             x1, NULL, #0x30  ; false
    // 0x811e6c: r0 = BoolExtension.obs()
    //     0x811e6c: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x811e70: ldur            x3, [fp, #-8]
    // 0x811e74: StoreField: r3->field_2b = r0
    //     0x811e74: stur            w0, [x3, #0x2b]
    //     0x811e78: ldurb           w16, [x3, #-1]
    //     0x811e7c: ldurb           w17, [x0, #-1]
    //     0x811e80: and             x16, x17, x16, lsr #2
    //     0x811e84: tst             x16, HEAP, lsr #32
    //     0x811e88: b.eq            #0x811e90
    //     0x811e8c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x811e90: r1 = <Transaction>
    //     0x811e90: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x811e94: ldr             x1, [x1, #0x28]
    // 0x811e98: r2 = 0
    //     0x811e98: movz            x2, #0
    // 0x811e9c: r0 = _GrowableList()
    //     0x811e9c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x811ea0: r16 = <Transaction>
    //     0x811ea0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x811ea4: ldr             x16, [x16, #0x28]
    // 0x811ea8: stp             x0, x16, [SP]
    // 0x811eac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811eac: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811eb0: r0 = ListExtension.obs()
    //     0x811eb0: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x811eb4: ldur            x2, [fp, #-8]
    // 0x811eb8: StoreField: r2->field_2f = r0
    //     0x811eb8: stur            w0, [x2, #0x2f]
    //     0x811ebc: ldurb           w16, [x2, #-1]
    //     0x811ec0: ldurb           w17, [x0, #-1]
    //     0x811ec4: and             x16, x17, x16, lsr #2
    //     0x811ec8: tst             x16, HEAP, lsr #32
    //     0x811ecc: b.eq            #0x811ed4
    //     0x811ed0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x811ed4: r1 = false
    //     0x811ed4: add             x1, NULL, #0x30  ; false
    // 0x811ed8: r0 = BoolExtension.obs()
    //     0x811ed8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x811edc: ldur            x1, [fp, #-8]
    // 0x811ee0: StoreField: r1->field_33 = r0
    //     0x811ee0: stur            w0, [x1, #0x33]
    //     0x811ee4: ldurb           w16, [x1, #-1]
    //     0x811ee8: ldurb           w17, [x0, #-1]
    //     0x811eec: and             x16, x17, x16, lsr #2
    //     0x811ef0: tst             x16, HEAP, lsr #32
    //     0x811ef4: b.eq            #0x811efc
    //     0x811ef8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811efc: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x811efc: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x811f00: r0 = Null
    //     0x811f00: mov             x0, NULL
    // 0x811f04: LeaveFrame
    //     0x811f04: mov             SP, fp
    //     0x811f08: ldp             fp, lr, [SP], #0x10
    // 0x811f0c: ret
    //     0x811f0c: ret             
    // 0x811f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811f10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811f14: b               #0x811df0
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91ecbc, size: 0x58
    // 0x91ecbc: EnterFrame
    //     0x91ecbc: stp             fp, lr, [SP, #-0x10]!
    //     0x91ecc0: mov             fp, SP
    // 0x91ecc4: AllocStack(0x8)
    //     0x91ecc4: sub             SP, SP, #8
    // 0x91ecc8: SetupParameters(_TransactionHistoryController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x91ecc8: mov             x0, x1
    //     0x91eccc: stur            x1, [fp, #-8]
    // 0x91ecd0: CheckStackOverflow
    //     0x91ecd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ecd4: cmp             SP, x16
    //     0x91ecd8: b.ls            #0x91ed0c
    // 0x91ecdc: LoadField: r1 = r0->field_1f
    //     0x91ecdc: ldur            w1, [x0, #0x1f]
    // 0x91ece0: DecompressPointer r1
    //     0x91ece0: add             x1, x1, HEAP, lsl #32
    // 0x91ece4: r0 = value()
    //     0x91ece4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x91ece8: r2 = LoadInt32Instr(r0)
    //     0x91ece8: sbfx            x2, x0, #1, #0x1f
    //     0x91ecec: tbz             w0, #0, #0x91ecf4
    //     0x91ecf0: ldur            x2, [x0, #7]
    // 0x91ecf4: ldur            x1, [fp, #-8]
    // 0x91ecf8: r0 = onPageRequest()
    //     0x91ecf8: bl              #0xe37a30  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onPageRequest
    // 0x91ecfc: r0 = Null
    //     0x91ecfc: mov             x0, NULL
    // 0x91ed00: LeaveFrame
    //     0x91ed00: mov             SP, fp
    //     0x91ed04: ldp             fp, lr, [SP], #0x10
    // 0x91ed08: ret
    //     0x91ed08: ret             
    // 0x91ed0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ed0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ed10: b               #0x91ecdc
  }
  _ find(/* No info */) {
    // ** addr: 0xae6864, size: 0x120
    // 0xae6864: EnterFrame
    //     0xae6864: stp             fp, lr, [SP, #-0x10]!
    //     0xae6868: mov             fp, SP
    // 0xae686c: AllocStack(0x68)
    //     0xae686c: sub             SP, SP, #0x68
    // 0xae6870: SetupParameters(dynamic _ /* r2 => r2, fp-0x58 */)
    //     0xae6870: stur            x2, [fp, #-0x58]
    // 0xae6874: CheckStackOverflow
    //     0xae6874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6878: cmp             SP, x16
    //     0xae687c: b.ls            #0xae697c
    // 0xae6880: LoadField: r0 = r1->field_2f
    //     0xae6880: ldur            w0, [x1, #0x2f]
    // 0xae6884: DecompressPointer r0
    //     0xae6884: add             x0, x0, HEAP, lsl #32
    // 0xae6888: mov             x1, x0
    // 0xae688c: stur            x0, [fp, #-0x50]
    // 0xae6890: r0 = value()
    //     0xae6890: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xae6894: r1 = LoadClassIdInstr(r0)
    //     0xae6894: ldur            x1, [x0, #-1]
    //     0xae6898: ubfx            x1, x1, #0xc, #0x14
    // 0xae689c: str             x0, [SP]
    // 0xae68a0: mov             x0, x1
    // 0xae68a4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xae68a4: movz            x17, #0xc834
    //     0xae68a8: add             lr, x0, x17
    //     0xae68ac: ldr             lr, [x21, lr, lsl #3]
    //     0xae68b0: blr             lr
    // 0xae68b4: cbz             w0, #0xae6958
    // 0xae68b8: ldur            x0, [fp, #-0x58]
    // 0xae68bc: ldur            x1, [fp, #-0x50]
    // 0xae68c0: r0 = value()
    //     0xae68c0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xae68c4: r1 = LoadClassIdInstr(r0)
    //     0xae68c4: ldur            x1, [x0, #-1]
    //     0xae68c8: ubfx            x1, x1, #0xc, #0x14
    // 0xae68cc: str             x0, [SP]
    // 0xae68d0: mov             x0, x1
    // 0xae68d4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xae68d4: movz            x17, #0xc834
    //     0xae68d8: add             lr, x0, x17
    //     0xae68dc: ldr             lr, [x21, lr, lsl #3]
    //     0xae68e0: blr             lr
    // 0xae68e4: r1 = LoadInt32Instr(r0)
    //     0xae68e4: sbfx            x1, x0, #1, #0x1f
    //     0xae68e8: tbz             w0, #0, #0xae68f0
    //     0xae68ec: ldur            x1, [x0, #7]
    // 0xae68f0: ldur            x0, [fp, #-0x58]
    // 0xae68f4: cmp             x1, x0
    // 0xae68f8: b.le            #0xae6948
    // 0xae68fc: ldur            x1, [fp, #-0x50]
    // 0xae6900: r0 = value()
    //     0xae6900: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xae6904: mov             x3, x0
    // 0xae6908: ldur            x2, [fp, #-0x58]
    // 0xae690c: r0 = BoxInt64Instr(r2)
    //     0xae690c: sbfiz           x0, x2, #1, #0x1f
    //     0xae6910: cmp             x2, x0, asr #1
    //     0xae6914: b.eq            #0xae6920
    //     0xae6918: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae691c: stur            x2, [x0, #7]
    // 0xae6920: r1 = LoadClassIdInstr(r3)
    //     0xae6920: ldur            x1, [x3, #-1]
    //     0xae6924: ubfx            x1, x1, #0xc, #0x14
    // 0xae6928: stp             x0, x3, [SP]
    // 0xae692c: mov             x0, x1
    // 0xae6930: r0 = GDT[cid_x0 + 0x13037]()
    //     0xae6930: movz            x17, #0x3037
    //     0xae6934: movk            x17, #0x1, lsl #16
    //     0xae6938: add             lr, x0, x17
    //     0xae693c: ldr             lr, [x21, lr, lsl #3]
    //     0xae6940: blr             lr
    // 0xae6944: b               #0xae694c
    // 0xae6948: r0 = Null
    //     0xae6948: mov             x0, NULL
    // 0xae694c: LeaveFrame
    //     0xae694c: mov             SP, fp
    //     0xae6950: ldp             fp, lr, [SP], #0x10
    // 0xae6954: ret
    //     0xae6954: ret             
    // 0xae6958: r0 = Null
    //     0xae6958: mov             x0, NULL
    // 0xae695c: LeaveFrame
    //     0xae695c: mov             SP, fp
    //     0xae6960: ldp             fp, lr, [SP], #0x10
    // 0xae6964: ret
    //     0xae6964: ret             
    // 0xae6968: sub             SP, fp, #0x68
    // 0xae696c: r0 = Null
    //     0xae696c: mov             x0, NULL
    // 0xae6970: LeaveFrame
    //     0xae6970: mov             SP, fp
    //     0xae6974: ldp             fp, lr, [SP], #0x10
    // 0xae6978: ret
    //     0xae6978: ret             
    // 0xae697c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae697c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6980: b               #0xae6880
  }
  [closure] bool onPageScrolled(dynamic, ScrollNotification) {
    // ** addr: 0xae6984, size: 0x3c
    // 0xae6984: EnterFrame
    //     0xae6984: stp             fp, lr, [SP, #-0x10]!
    //     0xae6988: mov             fp, SP
    // 0xae698c: ldr             x0, [fp, #0x18]
    // 0xae6990: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae6990: ldur            w1, [x0, #0x17]
    // 0xae6994: DecompressPointer r1
    //     0xae6994: add             x1, x1, HEAP, lsl #32
    // 0xae6998: CheckStackOverflow
    //     0xae6998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae699c: cmp             SP, x16
    //     0xae69a0: b.ls            #0xae69b8
    // 0xae69a4: ldr             x2, [fp, #0x10]
    // 0xae69a8: r0 = onPageScrolled()
    //     0xae69a8: bl              #0xae69c0  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageScrolled
    // 0xae69ac: LeaveFrame
    //     0xae69ac: mov             SP, fp
    //     0xae69b0: ldp             fp, lr, [SP], #0x10
    // 0xae69b4: ret
    //     0xae69b4: ret             
    // 0xae69b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae69b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae69bc: b               #0xae69a4
  }
  _ onPageScrolled(/* No info */) {
    // ** addr: 0xae69c0, size: 0xcc
    // 0xae69c0: EnterFrame
    //     0xae69c0: stp             fp, lr, [SP, #-0x10]!
    //     0xae69c4: mov             fp, SP
    // 0xae69c8: AllocStack(0x10)
    //     0xae69c8: sub             SP, SP, #0x10
    // 0xae69cc: SetupParameters(_TransactionHistoryController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae69cc: mov             x0, x1
    //     0xae69d0: stur            x1, [fp, #-8]
    //     0xae69d4: stur            x2, [fp, #-0x10]
    // 0xae69d8: CheckStackOverflow
    //     0xae69d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae69dc: cmp             SP, x16
    //     0xae69e0: b.ls            #0xae6a7c
    // 0xae69e4: mov             x1, x0
    // 0xae69e8: r0 = hasNextPage()
    //     0xae69e8: bl              #0x6fb9ec  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasNextPage
    // 0xae69ec: tbz             w0, #4, #0xae6a00
    // 0xae69f0: r0 = true
    //     0xae69f0: add             x0, NULL, #0x20  ; true
    // 0xae69f4: LeaveFrame
    //     0xae69f4: mov             SP, fp
    //     0xae69f8: ldp             fp, lr, [SP], #0x10
    // 0xae69fc: ret
    //     0xae69fc: ret             
    // 0xae6a00: ldur            x0, [fp, #-0x10]
    // 0xae6a04: LoadField: r1 = r0->field_f
    //     0xae6a04: ldur            w1, [x0, #0xf]
    // 0xae6a08: DecompressPointer r1
    //     0xae6a08: add             x1, x1, HEAP, lsl #32
    // 0xae6a0c: LoadField: r0 = r1->field_f
    //     0xae6a0c: ldur            w0, [x1, #0xf]
    // 0xae6a10: DecompressPointer r0
    //     0xae6a10: add             x0, x0, HEAP, lsl #32
    // 0xae6a14: cmp             w0, NULL
    // 0xae6a18: b.eq            #0xae6a84
    // 0xae6a1c: LoadField: r2 = r1->field_b
    //     0xae6a1c: ldur            w2, [x1, #0xb]
    // 0xae6a20: DecompressPointer r2
    //     0xae6a20: add             x2, x2, HEAP, lsl #32
    // 0xae6a24: cmp             w2, NULL
    // 0xae6a28: b.eq            #0xae6a88
    // 0xae6a2c: LoadField: d0 = r0->field_7
    //     0xae6a2c: ldur            d0, [x0, #7]
    // 0xae6a30: LoadField: d1 = r2->field_7
    //     0xae6a30: ldur            d1, [x2, #7]
    // 0xae6a34: fcmp            d0, d1
    // 0xae6a38: b.ne            #0xae6a6c
    // 0xae6a3c: ldur            x1, [fp, #-8]
    // 0xae6a40: r0 = isFetching()
    //     0xae6a40: bl              #0xad3170  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::isFetching
    // 0xae6a44: tbz             w0, #4, #0xae6a6c
    // 0xae6a48: ldur            x0, [fp, #-8]
    // 0xae6a4c: LoadField: r1 = r0->field_1f
    //     0xae6a4c: ldur            w1, [x0, #0x1f]
    // 0xae6a50: DecompressPointer r1
    //     0xae6a50: add             x1, x1, HEAP, lsl #32
    // 0xae6a54: r0 = value()
    //     0xae6a54: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6a58: r2 = LoadInt32Instr(r0)
    //     0xae6a58: sbfx            x2, x0, #1, #0x1f
    //     0xae6a5c: tbz             w0, #0, #0xae6a64
    //     0xae6a60: ldur            x2, [x0, #7]
    // 0xae6a64: ldur            x1, [fp, #-8]
    // 0xae6a68: r0 = onPageRequest()
    //     0xae6a68: bl              #0xe37a30  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onPageRequest
    // 0xae6a6c: r0 = false
    //     0xae6a6c: add             x0, NULL, #0x30  ; false
    // 0xae6a70: LeaveFrame
    //     0xae6a70: mov             SP, fp
    //     0xae6a74: ldp             fp, lr, [SP], #0x10
    // 0xae6a78: ret
    //     0xae6a78: ret             
    // 0xae6a7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6a7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6a80: b               #0xae69e4
    // 0xae6a84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6a84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6a88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ onPageRefresh(/* No info */) async {
    // ** addr: 0xae6ebc, size: 0xb4
    // 0xae6ebc: EnterFrame
    //     0xae6ebc: stp             fp, lr, [SP, #-0x10]!
    //     0xae6ec0: mov             fp, SP
    // 0xae6ec4: AllocStack(0x18)
    //     0xae6ec4: sub             SP, SP, #0x18
    // 0xae6ec8: SetupParameters(_TransactionHistoryController&GetxController&PagingMixin this /* r1 => r1, fp-0x10 */)
    //     0xae6ec8: stur            NULL, [fp, #-8]
    //     0xae6ecc: stur            x1, [fp, #-0x10]
    // 0xae6ed0: CheckStackOverflow
    //     0xae6ed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6ed4: cmp             SP, x16
    //     0xae6ed8: b.ls            #0xae6f68
    // 0xae6edc: InitAsync() -> Future<void?>
    //     0xae6edc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae6ee0: bl              #0x661298  ; InitAsyncStub
    // 0xae6ee4: ldur            x0, [fp, #-0x10]
    // 0xae6ee8: LoadField: r3 = r0->field_2f
    //     0xae6ee8: ldur            w3, [x0, #0x2f]
    // 0xae6eec: DecompressPointer r3
    //     0xae6eec: add             x3, x3, HEAP, lsl #32
    // 0xae6ef0: stur            x3, [fp, #-0x18]
    // 0xae6ef4: r1 = <Transaction>
    //     0xae6ef4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0xae6ef8: ldr             x1, [x1, #0x28]
    // 0xae6efc: r2 = 0
    //     0xae6efc: movz            x2, #0
    // 0xae6f00: r0 = _GrowableList()
    //     0xae6f00: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xae6f04: ldur            x1, [fp, #-0x18]
    // 0xae6f08: mov             x2, x0
    // 0xae6f0c: r0 = value=()
    //     0xae6f0c: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0xae6f10: ldur            x0, [fp, #-0x10]
    // 0xae6f14: LoadField: r1 = r0->field_1f
    //     0xae6f14: ldur            w1, [x0, #0x1f]
    // 0xae6f18: DecompressPointer r1
    //     0xae6f18: add             x1, x1, HEAP, lsl #32
    // 0xae6f1c: r2 = 2
    //     0xae6f1c: movz            x2, #0x2
    // 0xae6f20: r0 = value=()
    //     0xae6f20: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae6f24: ldur            x0, [fp, #-0x10]
    // 0xae6f28: LoadField: r1 = r0->field_23
    //     0xae6f28: ldur            w1, [x0, #0x23]
    // 0xae6f2c: DecompressPointer r1
    //     0xae6f2c: add             x1, x1, HEAP, lsl #32
    // 0xae6f30: r2 = false
    //     0xae6f30: add             x2, NULL, #0x30  ; false
    // 0xae6f34: r0 = value=()
    //     0xae6f34: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae6f38: ldur            x0, [fp, #-0x10]
    // 0xae6f3c: LoadField: r1 = r0->field_27
    //     0xae6f3c: ldur            w1, [x0, #0x27]
    // 0xae6f40: DecompressPointer r1
    //     0xae6f40: add             x1, x1, HEAP, lsl #32
    // 0xae6f44: r2 = false
    //     0xae6f44: add             x2, NULL, #0x30  ; false
    // 0xae6f48: r0 = value=()
    //     0xae6f48: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae6f4c: ldur            x1, [fp, #-0x10]
    // 0xae6f50: r0 = page()
    //     0xae6f50: bl              #0x72b460  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::page
    // 0xae6f54: ldur            x1, [fp, #-0x10]
    // 0xae6f58: mov             x2, x0
    // 0xae6f5c: r0 = onPageRequest()
    //     0xae6f5c: bl              #0xe37a30  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onPageRequest
    // 0xae6f60: r0 = Null
    //     0xae6f60: mov             x0, NULL
    // 0xae6f64: r0 = ReturnAsyncNotFuture()
    //     0xae6f64: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae6f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6f6c: b               #0xae6edc
  }
  [closure] Future<void> onPageRefresh(dynamic) {
    // ** addr: 0xae7024, size: 0x38
    // 0xae7024: EnterFrame
    //     0xae7024: stp             fp, lr, [SP, #-0x10]!
    //     0xae7028: mov             fp, SP
    // 0xae702c: ldr             x0, [fp, #0x10]
    // 0xae7030: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae7030: ldur            w1, [x0, #0x17]
    // 0xae7034: DecompressPointer r1
    //     0xae7034: add             x1, x1, HEAP, lsl #32
    // 0xae7038: CheckStackOverflow
    //     0xae7038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae703c: cmp             SP, x16
    //     0xae7040: b.ls            #0xae7054
    // 0xae7044: r0 = onPageRefresh()
    //     0xae7044: bl              #0xae6ebc  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageRefresh
    // 0xae7048: LeaveFrame
    //     0xae7048: mov             SP, fp
    //     0xae704c: ldp             fp, lr, [SP], #0x10
    // 0xae7050: ret
    //     0xae7050: ret             
    // 0xae7054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7054: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7058: b               #0xae7044
  }
}

// class id: 1899, size: 0x38, field offset: 0x38
//   transformed mixin,
abstract class _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin extends _TransactionHistoryController&GetxController&PagingMixin
     with OnlineMixin<X0> {

  _ executeOnlineMode(/* No info */) async {
    // ** addr: 0xe37a78, size: 0x80
    // 0xe37a78: EnterFrame
    //     0xe37a78: stp             fp, lr, [SP, #-0x10]!
    //     0xe37a7c: mov             fp, SP
    // 0xe37a80: AllocStack(0x30)
    //     0xe37a80: sub             SP, SP, #0x30
    // 0xe37a84: SetupParameters(_TransactionHistoryController&GetxController&PagingMixin&OnlineMixin this /* r1 => r1, fp-0x10 */)
    //     0xe37a84: stur            NULL, [fp, #-8]
    //     0xe37a88: stur            x1, [fp, #-0x10]
    // 0xe37a8c: CheckStackOverflow
    //     0xe37a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37a90: cmp             SP, x16
    //     0xe37a94: b.ls            #0xe37af0
    // 0xe37a98: r1 = 1
    //     0xe37a98: movz            x1, #0x1
    // 0xe37a9c: r0 = AllocateContext()
    //     0xe37a9c: bl              #0xec126c  ; AllocateContextStub
    // 0xe37aa0: mov             x2, x0
    // 0xe37aa4: ldur            x1, [fp, #-0x10]
    // 0xe37aa8: stur            x2, [fp, #-0x18]
    // 0xe37aac: StoreField: r2->field_f = r1
    //     0xe37aac: stur            w1, [x2, #0xf]
    // 0xe37ab0: InitAsync() -> Future<void?>
    //     0xe37ab0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe37ab4: bl              #0x661298  ; InitAsyncStub
    // 0xe37ab8: ldur            x1, [fp, #-0x10]
    // 0xe37abc: r0 = onOnlineModeRequested()
    //     0xe37abc: bl              #0x72b190  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onOnlineModeRequested
    // 0xe37ac0: ldur            x2, [fp, #-0x18]
    // 0xe37ac4: r1 = Function '<anonymous closure>':.
    //     0xe37ac4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b030] AnonymousClosure: (0xe37af8), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin::executeOnlineMode (0xe37a78)
    //     0xe37ac8: ldr             x1, [x1, #0x30]
    // 0xe37acc: stur            x0, [fp, #-0x10]
    // 0xe37ad0: r0 = AllocateClosure()
    //     0xe37ad0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37ad4: r16 = <void?>
    //     0xe37ad4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe37ad8: ldur            lr, [fp, #-0x10]
    // 0xe37adc: stp             lr, x16, [SP, #8]
    // 0xe37ae0: str             x0, [SP]
    // 0xe37ae4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe37ae4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe37ae8: r0 = then()
    //     0xe37ae8: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe37aec: r0 = ReturnAsync()
    //     0xe37aec: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe37af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37af4: b               #0xe37a98
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<List<Transaction>>) {
    // ** addr: 0xe37af8, size: 0x94
    // 0xe37af8: EnterFrame
    //     0xe37af8: stp             fp, lr, [SP, #-0x10]!
    //     0xe37afc: mov             fp, SP
    // 0xe37b00: AllocStack(0x28)
    //     0xe37b00: sub             SP, SP, #0x28
    // 0xe37b04: SetupParameters()
    //     0xe37b04: ldr             x0, [fp, #0x18]
    //     0xe37b08: ldur            w3, [x0, #0x17]
    //     0xe37b0c: add             x3, x3, HEAP, lsl #32
    //     0xe37b10: stur            x3, [fp, #-8]
    // 0xe37b14: CheckStackOverflow
    //     0xe37b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37b18: cmp             SP, x16
    //     0xe37b1c: b.ls            #0xe37b84
    // 0xe37b20: mov             x2, x3
    // 0xe37b24: r1 = Function '<anonymous closure>':.
    //     0xe37b24: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b038] AnonymousClosure: (0xe37be4), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin::executeOnlineMode (0xe37a78)
    //     0xe37b28: ldr             x1, [x1, #0x38]
    // 0xe37b2c: r0 = AllocateClosure()
    //     0xe37b2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37b30: ldur            x2, [fp, #-8]
    // 0xe37b34: r1 = Function '<anonymous closure>':.
    //     0xe37b34: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b040] AnonymousClosure: (0xe37b8c), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin::executeOnlineMode (0xe37a78)
    //     0xe37b38: ldr             x1, [x1, #0x40]
    // 0xe37b3c: stur            x0, [fp, #-8]
    // 0xe37b40: r0 = AllocateClosure()
    //     0xe37b40: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37b44: mov             x1, x0
    // 0xe37b48: ldr             x0, [fp, #0x10]
    // 0xe37b4c: r2 = LoadClassIdInstr(r0)
    //     0xe37b4c: ldur            x2, [x0, #-1]
    //     0xe37b50: ubfx            x2, x2, #0xc, #0x14
    // 0xe37b54: r16 = <Object?>
    //     0xe37b54: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xe37b58: stp             x0, x16, [SP, #0x10]
    // 0xe37b5c: ldur            x16, [fp, #-8]
    // 0xe37b60: stp             x16, x1, [SP]
    // 0xe37b64: mov             x0, x2
    // 0xe37b68: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe37b68: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe37b6c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe37b6c: sub             lr, x0, #1, lsl #12
    //     0xe37b70: ldr             lr, [x21, lr, lsl #3]
    //     0xe37b74: blr             lr
    // 0xe37b78: LeaveFrame
    //     0xe37b78: mov             SP, fp
    //     0xe37b7c: ldp             fp, lr, [SP], #0x10
    // 0xe37b80: ret
    //     0xe37b80: ret             
    // 0xe37b84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37b84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37b88: b               #0xe37b20
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0xe37b8c, size: 0x58
    // 0xe37b8c: EnterFrame
    //     0xe37b8c: stp             fp, lr, [SP, #-0x10]!
    //     0xe37b90: mov             fp, SP
    // 0xe37b94: AllocStack(0x8)
    //     0xe37b94: sub             SP, SP, #8
    // 0xe37b98: SetupParameters()
    //     0xe37b98: ldr             x0, [fp, #0x18]
    //     0xe37b9c: ldur            w1, [x0, #0x17]
    //     0xe37ba0: add             x1, x1, HEAP, lsl #32
    // 0xe37ba4: CheckStackOverflow
    //     0xe37ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37ba8: cmp             SP, x16
    //     0xe37bac: b.ls            #0xe37bdc
    // 0xe37bb0: LoadField: r0 = r1->field_f
    //     0xe37bb0: ldur            w0, [x1, #0xf]
    // 0xe37bb4: DecompressPointer r0
    //     0xe37bb4: add             x0, x0, HEAP, lsl #32
    // 0xe37bb8: ldr             x1, [fp, #0x10]
    // 0xe37bbc: stur            x0, [fp, #-8]
    // 0xe37bc0: r0 = getErrorMessage()
    //     0xe37bc0: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0xe37bc4: ldur            x1, [fp, #-8]
    // 0xe37bc8: mov             x2, x0
    // 0xe37bcc: r0 = onOnlineModeFailure()
    //     0xe37bcc: bl              #0xe34978  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onOnlineModeFailure
    // 0xe37bd0: LeaveFrame
    //     0xe37bd0: mov             SP, fp
    //     0xe37bd4: ldp             fp, lr, [SP], #0x10
    // 0xe37bd8: ret
    //     0xe37bd8: ret             
    // 0xe37bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37be0: b               #0xe37bb0
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<Transaction>, Pagination?) {
    // ** addr: 0xe37be4, size: 0x4c
    // 0xe37be4: EnterFrame
    //     0xe37be4: stp             fp, lr, [SP, #-0x10]!
    //     0xe37be8: mov             fp, SP
    // 0xe37bec: ldr             x0, [fp, #0x20]
    // 0xe37bf0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe37bf0: ldur            w1, [x0, #0x17]
    // 0xe37bf4: DecompressPointer r1
    //     0xe37bf4: add             x1, x1, HEAP, lsl #32
    // 0xe37bf8: CheckStackOverflow
    //     0xe37bf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37bfc: cmp             SP, x16
    //     0xe37c00: b.ls            #0xe37c28
    // 0xe37c04: LoadField: r0 = r1->field_f
    //     0xe37c04: ldur            w0, [x1, #0xf]
    // 0xe37c08: DecompressPointer r0
    //     0xe37c08: add             x0, x0, HEAP, lsl #32
    // 0xe37c0c: mov             x1, x0
    // 0xe37c10: ldr             x2, [fp, #0x18]
    // 0xe37c14: ldr             x3, [fp, #0x10]
    // 0xe37c18: r0 = onOnlineModeLoaded()
    //     0xe37c18: bl              #0x7f89f8  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::onOnlineModeLoaded
    // 0xe37c1c: LeaveFrame
    //     0xe37c1c: mov             SP, fp
    //     0xe37c20: ldp             fp, lr, [SP], #0x10
    // 0xe37c24: ret
    //     0xe37c24: ret             
    // 0xe37c28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37c28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37c2c: b               #0xe37c04
  }
}

// class id: 1900, size: 0x4c, field offset: 0x38
class TransactionHistoryController extends _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin {

  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0x72b190, size: 0x2d0
    // 0x72b190: EnterFrame
    //     0x72b190: stp             fp, lr, [SP, #-0x10]!
    //     0x72b194: mov             fp, SP
    // 0x72b198: AllocStack(0x58)
    //     0x72b198: sub             SP, SP, #0x58
    // 0x72b19c: SetupParameters(TransactionHistoryController this /* r1 => r1, fp-0x10 */)
    //     0x72b19c: stur            NULL, [fp, #-8]
    //     0x72b1a0: stur            x1, [fp, #-0x10]
    // 0x72b1a4: CheckStackOverflow
    //     0x72b1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72b1a8: cmp             SP, x16
    //     0x72b1ac: b.ls            #0x72b458
    // 0x72b1b0: InitAsync() -> Future<ApiResult<List<Transaction>>>
    //     0x72b1b0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b048] TypeArguments: <ApiResult<List<Transaction>>>
    //     0x72b1b4: ldr             x0, [x0, #0x48]
    //     0x72b1b8: bl              #0x661298  ; InitAsyncStub
    // 0x72b1bc: ldur            x0, [fp, #-0x10]
    // 0x72b1c0: LoadField: r3 = r0->field_3f
    //     0x72b1c0: ldur            w3, [x0, #0x3f]
    // 0x72b1c4: DecompressPointer r3
    //     0x72b1c4: add             x3, x3, HEAP, lsl #32
    // 0x72b1c8: stur            x3, [fp, #-0x18]
    // 0x72b1cc: r1 = Null
    //     0x72b1cc: mov             x1, NULL
    // 0x72b1d0: r2 = 20
    //     0x72b1d0: movz            x2, #0x14
    // 0x72b1d4: r0 = AllocateArray()
    //     0x72b1d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72b1d8: stur            x0, [fp, #-0x20]
    // 0x72b1dc: r16 = "page"
    //     0x72b1dc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x72b1e0: ldr             x16, [x16, #0x300]
    // 0x72b1e4: StoreField: r0->field_f = r16
    //     0x72b1e4: stur            w16, [x0, #0xf]
    // 0x72b1e8: ldur            x2, [fp, #-0x10]
    // 0x72b1ec: LoadField: r1 = r2->field_1f
    //     0x72b1ec: ldur            w1, [x2, #0x1f]
    // 0x72b1f0: DecompressPointer r1
    //     0x72b1f0: add             x1, x1, HEAP, lsl #32
    // 0x72b1f4: r0 = value()
    //     0x72b1f4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x72b1f8: str             x0, [SP]
    // 0x72b1fc: r0 = _interpolateSingle()
    //     0x72b1fc: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0x72b200: ldur            x1, [fp, #-0x20]
    // 0x72b204: ArrayStore: r1[1] = r0  ; List_4
    //     0x72b204: add             x25, x1, #0x13
    //     0x72b208: str             w0, [x25]
    //     0x72b20c: tbz             w0, #0, #0x72b228
    //     0x72b210: ldurb           w16, [x1, #-1]
    //     0x72b214: ldurb           w17, [x0, #-1]
    //     0x72b218: and             x16, x17, x16, lsr #2
    //     0x72b21c: tst             x16, HEAP, lsr #32
    //     0x72b220: b.eq            #0x72b228
    //     0x72b224: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x72b228: ldur            x0, [fp, #-0x20]
    // 0x72b22c: r16 = "limit"
    //     0x72b22c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0x72b230: ldr             x16, [x16, #0x50]
    // 0x72b234: ArrayStore: r0[0] = r16  ; List_4
    //     0x72b234: stur            w16, [x0, #0x17]
    // 0x72b238: ldur            x1, [fp, #-0x10]
    // 0x72b23c: r0 = limit()
    //     0x72b23c: bl              #0x72b980  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::limit
    // 0x72b240: mov             x2, x0
    // 0x72b244: r0 = BoxInt64Instr(r2)
    //     0x72b244: sbfiz           x0, x2, #1, #0x1f
    //     0x72b248: cmp             x2, x0, asr #1
    //     0x72b24c: b.eq            #0x72b258
    //     0x72b250: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72b254: stur            x2, [x0, #7]
    // 0x72b258: str             x0, [SP]
    // 0x72b25c: r0 = _interpolateSingle()
    //     0x72b25c: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0x72b260: ldur            x1, [fp, #-0x20]
    // 0x72b264: ArrayStore: r1[3] = r0  ; List_4
    //     0x72b264: add             x25, x1, #0x1b
    //     0x72b268: str             w0, [x25]
    //     0x72b26c: tbz             w0, #0, #0x72b288
    //     0x72b270: ldurb           w16, [x1, #-1]
    //     0x72b274: ldurb           w17, [x0, #-1]
    //     0x72b278: and             x16, x17, x16, lsr #2
    //     0x72b27c: tst             x16, HEAP, lsr #32
    //     0x72b280: b.eq            #0x72b288
    //     0x72b284: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x72b288: ldur            x2, [fp, #-0x20]
    // 0x72b28c: r16 = "category_id"
    //     0x72b28c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0x72b290: ldr             x16, [x16, #0xe70]
    // 0x72b294: StoreField: r2->field_1f = r16
    //     0x72b294: stur            w16, [x2, #0x1f]
    // 0x72b298: ldur            x3, [fp, #-0x10]
    // 0x72b29c: LoadField: r4 = r3->field_37
    //     0x72b29c: ldur            w4, [x3, #0x37]
    // 0x72b2a0: DecompressPointer r4
    //     0x72b2a0: add             x4, x4, HEAP, lsl #32
    // 0x72b2a4: stur            x4, [fp, #-0x28]
    // 0x72b2a8: LoadField: r5 = r4->field_1f
    //     0x72b2a8: ldur            x5, [x4, #0x1f]
    // 0x72b2ac: r0 = BoxInt64Instr(r5)
    //     0x72b2ac: sbfiz           x0, x5, #1, #0x1f
    //     0x72b2b0: cmp             x5, x0, asr #1
    //     0x72b2b4: b.eq            #0x72b2c0
    //     0x72b2b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72b2bc: stur            x5, [x0, #7]
    // 0x72b2c0: str             x0, [SP]
    // 0x72b2c4: r0 = _interpolateSingle()
    //     0x72b2c4: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0x72b2c8: ldur            x1, [fp, #-0x20]
    // 0x72b2cc: ArrayStore: r1[5] = r0  ; List_4
    //     0x72b2cc: add             x25, x1, #0x23
    //     0x72b2d0: str             w0, [x25]
    //     0x72b2d4: tbz             w0, #0, #0x72b2f0
    //     0x72b2d8: ldurb           w16, [x1, #-1]
    //     0x72b2dc: ldurb           w17, [x0, #-1]
    //     0x72b2e0: and             x16, x17, x16, lsr #2
    //     0x72b2e4: tst             x16, HEAP, lsr #32
    //     0x72b2e8: b.eq            #0x72b2f0
    //     0x72b2ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x72b2f0: ldur            x0, [fp, #-0x20]
    // 0x72b2f4: r16 = "sort"
    //     0x72b2f4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b058] "sort"
    //     0x72b2f8: ldr             x16, [x16, #0x58]
    // 0x72b2fc: StoreField: r0->field_27 = r16
    //     0x72b2fc: stur            w16, [x0, #0x27]
    // 0x72b300: ldur            x2, [fp, #-0x10]
    // 0x72b304: LoadField: r3 = r2->field_47
    //     0x72b304: ldur            w3, [x2, #0x47]
    // 0x72b308: DecompressPointer r3
    //     0x72b308: add             x3, x3, HEAP, lsl #32
    // 0x72b30c: mov             x1, x3
    // 0x72b310: stur            x3, [fp, #-0x30]
    // 0x72b314: r0 = value()
    //     0x72b314: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x72b318: ldur            x0, [fp, #-0x20]
    // 0x72b31c: r16 = "DESC"
    //     0x72b31c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b060] "DESC"
    //     0x72b320: ldr             x16, [x16, #0x60]
    // 0x72b324: StoreField: r0->field_2b = r16
    //     0x72b324: stur            w16, [x0, #0x2b]
    // 0x72b328: r16 = "sort_field"
    //     0x72b328: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b068] "sort_field"
    //     0x72b32c: ldr             x16, [x16, #0x68]
    // 0x72b330: StoreField: r0->field_2f = r16
    //     0x72b330: stur            w16, [x0, #0x2f]
    // 0x72b334: ldur            x1, [fp, #-0x30]
    // 0x72b338: r0 = value()
    //     0x72b338: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x72b33c: LoadField: r1 = r0->field_7
    //     0x72b33c: ldur            w1, [x0, #7]
    // 0x72b340: DecompressPointer r1
    //     0x72b340: add             x1, x1, HEAP, lsl #32
    // 0x72b344: r0 = OrderByExtension.id()
    //     0x72b344: bl              #0x72b904  ; [package:nuonline/app/data/enums/sort_ordering_enum.dart] ::OrderByExtension.id
    // 0x72b348: ldur            x1, [fp, #-0x20]
    // 0x72b34c: ArrayStore: r1[9] = r0  ; List_4
    //     0x72b34c: add             x25, x1, #0x33
    //     0x72b350: str             w0, [x25]
    //     0x72b354: tbz             w0, #0, #0x72b370
    //     0x72b358: ldurb           w16, [x1, #-1]
    //     0x72b35c: ldurb           w17, [x0, #-1]
    //     0x72b360: and             x16, x17, x16, lsr #2
    //     0x72b364: tst             x16, HEAP, lsr #32
    //     0x72b368: b.eq            #0x72b370
    //     0x72b36c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x72b370: r16 = <String, String?>
    //     0x72b370: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x72b374: ldr             x16, [x16, #0x198]
    // 0x72b378: ldur            lr, [fp, #-0x20]
    // 0x72b37c: stp             lr, x16, [SP]
    // 0x72b380: r0 = Map._fromLiteral()
    //     0x72b380: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x72b384: mov             x1, x0
    // 0x72b388: ldur            x0, [fp, #-0x28]
    // 0x72b38c: stur            x1, [fp, #-0x20]
    // 0x72b390: r16 = Instance_PaymentType
    //     0x72b390: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0x72b394: ldr             x16, [x16, #0x5f0]
    // 0x72b398: cmp             w0, w16
    // 0x72b39c: r16 = true
    //     0x72b39c: add             x16, NULL, #0x20  ; true
    // 0x72b3a0: r17 = false
    //     0x72b3a0: add             x17, NULL, #0x30  ; false
    // 0x72b3a4: csel            x2, x16, x17, eq
    // 0x72b3a8: r16 = <String, String?>
    //     0x72b3a8: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x72b3ac: ldr             x16, [x16, #0x198]
    // 0x72b3b0: stp             x1, x16, [SP, #0x18]
    // 0x72b3b4: r16 = "is_qurban"
    //     0x72b3b4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b070] "is_qurban"
    //     0x72b3b8: ldr             x16, [x16, #0x70]
    // 0x72b3bc: stp             x16, x2, [SP, #8]
    // 0x72b3c0: r16 = "true"
    //     0x72b3c0: ldr             x16, [PP, #0x13a0]  ; [pp+0x13a0] "true"
    // 0x72b3c4: str             x16, [SP]
    // 0x72b3c8: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x72b3c8: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x72b3cc: r0 = MapExtension.addIf()
    //     0x72b3cc: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0x72b3d0: ldur            x0, [fp, #-0x10]
    // 0x72b3d4: LoadField: r1 = r0->field_3b
    //     0x72b3d4: ldur            w1, [x0, #0x3b]
    // 0x72b3d8: DecompressPointer r1
    //     0x72b3d8: add             x1, x1, HEAP, lsl #32
    // 0x72b3dc: cmp             w1, NULL
    // 0x72b3e0: r16 = true
    //     0x72b3e0: add             x16, NULL, #0x20  ; true
    // 0x72b3e4: r17 = false
    //     0x72b3e4: add             x17, NULL, #0x30  ; false
    // 0x72b3e8: csel            x2, x16, x17, ne
    // 0x72b3ec: stur            x2, [fp, #-0x10]
    // 0x72b3f0: r0 = 60
    //     0x72b3f0: movz            x0, #0x3c
    // 0x72b3f4: branchIfSmi(r1, 0x72b400)
    //     0x72b3f4: tbz             w1, #0, #0x72b400
    // 0x72b3f8: r0 = LoadClassIdInstr(r1)
    //     0x72b3f8: ldur            x0, [x1, #-1]
    //     0x72b3fc: ubfx            x0, x0, #0xc, #0x14
    // 0x72b400: str             x1, [SP]
    // 0x72b404: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x72b404: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x72b408: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x72b408: movz            x17, #0x2b03
    //     0x72b40c: add             lr, x0, x17
    //     0x72b410: ldr             lr, [x21, lr, lsl #3]
    //     0x72b414: blr             lr
    // 0x72b418: r16 = <String, String?>
    //     0x72b418: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x72b41c: ldr             x16, [x16, #0x198]
    // 0x72b420: ldur            lr, [fp, #-0x20]
    // 0x72b424: stp             lr, x16, [SP, #0x18]
    // 0x72b428: ldur            x16, [fp, #-0x10]
    // 0x72b42c: r30 = "donation_id"
    //     0x72b42c: add             lr, PP, #0x2b, lsl #12  ; [pp+0x2b078] "donation_id"
    //     0x72b430: ldr             lr, [lr, #0x78]
    // 0x72b434: stp             lr, x16, [SP, #8]
    // 0x72b438: str             x0, [SP]
    // 0x72b43c: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x72b43c: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x72b440: r0 = MapExtension.addIf()
    //     0x72b440: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0x72b444: ldur            x1, [fp, #-0x18]
    // 0x72b448: ldur            x2, [fp, #-0x20]
    // 0x72b44c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72b44c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72b450: r0 = findAll()
    //     0x72b450: bl              #0x72b550  ; [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAll
    // 0x72b454: r0 = ReturnAsync()
    //     0x72b454: b               #0x6576a4  ; ReturnAsyncStub
    // 0x72b458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b45c: b               #0x72b1b0
  }
  get _ limit(/* No info */) {
    // ** addr: 0x72b980, size: 0x28
    // 0x72b980: LoadField: r2 = r1->field_43
    //     0x72b980: ldur            w2, [x1, #0x43]
    // 0x72b984: DecompressPointer r2
    //     0x72b984: add             x2, x2, HEAP, lsl #32
    // 0x72b988: tst             x2, #0x10
    // 0x72b98c: cset            x1, ne
    // 0x72b990: sub             x1, x1, #1
    // 0x72b994: r16 = 10
    //     0x72b994: movz            x16, #0xa
    // 0x72b998: and             x1, x1, x16
    // 0x72b99c: add             x1, x1, #0xa
    // 0x72b9a0: r0 = LoadInt32Instr(r1)
    //     0x72b9a0: sbfx            x0, x1, #1, #0x1f
    // 0x72b9a4: ret
    //     0x72b9a4: ret             
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0x7f89f8, size: 0x108
    // 0x7f89f8: EnterFrame
    //     0x7f89f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7f89fc: mov             fp, SP
    // 0x7f8a00: AllocStack(0x20)
    //     0x7f8a00: sub             SP, SP, #0x20
    // 0x7f8a04: SetupParameters(TransactionHistoryController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7f8a04: stur            NULL, [fp, #-8]
    //     0x7f8a08: stur            x1, [fp, #-0x10]
    //     0x7f8a0c: mov             x16, x2
    //     0x7f8a10: mov             x2, x1
    //     0x7f8a14: mov             x1, x16
    //     0x7f8a18: stur            x1, [fp, #-0x18]
    //     0x7f8a1c: stur            x3, [fp, #-0x20]
    // 0x7f8a20: CheckStackOverflow
    //     0x7f8a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8a24: cmp             SP, x16
    //     0x7f8a28: b.ls            #0x7f8af8
    // 0x7f8a2c: InitAsync() -> Future<void?>
    //     0x7f8a2c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7f8a30: bl              #0x661298  ; InitAsyncStub
    // 0x7f8a34: ldur            x0, [fp, #-0x10]
    // 0x7f8a38: LoadField: r1 = r0->field_1f
    //     0x7f8a38: ldur            w1, [x0, #0x1f]
    // 0x7f8a3c: DecompressPointer r1
    //     0x7f8a3c: add             x1, x1, HEAP, lsl #32
    // 0x7f8a40: r0 = value()
    //     0x7f8a40: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7f8a44: cmp             w0, #2
    // 0x7f8a48: b.ne            #0x7f8a94
    // 0x7f8a4c: ldur            x2, [fp, #-0x18]
    // 0x7f8a50: r0 = LoadClassIdInstr(r2)
    //     0x7f8a50: ldur            x0, [x2, #-1]
    //     0x7f8a54: ubfx            x0, x0, #0xc, #0x14
    // 0x7f8a58: mov             x1, x2
    // 0x7f8a5c: r0 = GDT[cid_x0 + 0xe879]()
    //     0x7f8a5c: movz            x17, #0xe879
    //     0x7f8a60: add             lr, x0, x17
    //     0x7f8a64: ldr             lr, [x21, lr, lsl #3]
    //     0x7f8a68: blr             lr
    // 0x7f8a6c: tbnz            w0, #4, #0x7f8a8c
    // 0x7f8a70: ldur            x1, [fp, #-0x10]
    // 0x7f8a74: LoadField: r0 = r1->field_2b
    //     0x7f8a74: ldur            w0, [x1, #0x2b]
    // 0x7f8a78: DecompressPointer r0
    //     0x7f8a78: add             x0, x0, HEAP, lsl #32
    // 0x7f8a7c: mov             x1, x0
    // 0x7f8a80: r2 = true
    //     0x7f8a80: add             x2, NULL, #0x20  ; true
    // 0x7f8a84: r0 = value=()
    //     0x7f8a84: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8a88: b               #0x7f8af0
    // 0x7f8a8c: ldur            x1, [fp, #-0x10]
    // 0x7f8a90: b               #0x7f8a98
    // 0x7f8a94: ldur            x1, [fp, #-0x10]
    // 0x7f8a98: LoadField: r0 = r1->field_43
    //     0x7f8a98: ldur            w0, [x1, #0x43]
    // 0x7f8a9c: DecompressPointer r0
    //     0x7f8a9c: add             x0, x0, HEAP, lsl #32
    // 0x7f8aa0: tbnz            w0, #4, #0x7f8ae4
    // 0x7f8aa4: ldur            x0, [fp, #-0x20]
    // 0x7f8aa8: cmp             w0, NULL
    // 0x7f8aac: b.ne            #0x7f8ab8
    // 0x7f8ab0: r0 = Null
    //     0x7f8ab0: mov             x0, NULL
    // 0x7f8ab4: b               #0x7f8ad0
    // 0x7f8ab8: LoadField: r2 = r0->field_7
    //     0x7f8ab8: ldur            x2, [x0, #7]
    // 0x7f8abc: LoadField: r3 = r0->field_f
    //     0x7f8abc: ldur            x3, [x0, #0xf]
    // 0x7f8ac0: cmp             x2, x3
    // 0x7f8ac4: r16 = true
    //     0x7f8ac4: add             x16, NULL, #0x20  ; true
    // 0x7f8ac8: r17 = false
    //     0x7f8ac8: add             x17, NULL, #0x30  ; false
    // 0x7f8acc: csel            x0, x16, x17, lt
    // 0x7f8ad0: cmp             w0, NULL
    // 0x7f8ad4: b.ne            #0x7f8adc
    // 0x7f8ad8: r0 = false
    //     0x7f8ad8: add             x0, NULL, #0x30  ; false
    // 0x7f8adc: mov             x3, x0
    // 0x7f8ae0: b               #0x7f8ae8
    // 0x7f8ae4: r3 = false
    //     0x7f8ae4: add             x3, NULL, #0x30  ; false
    // 0x7f8ae8: ldur            x2, [fp, #-0x18]
    // 0x7f8aec: r0 = appendToPage()
    //     0x7f8aec: bl              #0x7f8b40  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::appendToPage
    // 0x7f8af0: r0 = Null
    //     0x7f8af0: mov             x0, NULL
    // 0x7f8af4: r0 = ReturnAsyncNotFuture()
    //     0x7f8af4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7f8af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8afc: b               #0x7f8a2c
  }
  _ TransactionHistoryController(/* No info */) {
    // ** addr: 0x811c3c, size: 0x194
    // 0x811c3c: EnterFrame
    //     0x811c3c: stp             fp, lr, [SP, #-0x10]!
    //     0x811c40: mov             fp, SP
    // 0x811c44: AllocStack(0x30)
    //     0x811c44: sub             SP, SP, #0x30
    // 0x811c48: SetupParameters(TransactionHistoryController this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x28 */, {dynamic donationId = Null /* r6, fp-0x10 */, dynamic infiniteScroll = true /* r4, fp-0x8 */})
    //     0x811c48: mov             x0, x3
    //     0x811c4c: stur            x3, [fp, #-0x28]
    //     0x811c50: mov             x3, x1
    //     0x811c54: stur            x1, [fp, #-0x18]
    //     0x811c58: stur            x2, [fp, #-0x20]
    //     0x811c5c: ldur            w1, [x4, #0x13]
    //     0x811c60: ldur            w5, [x4, #0x1f]
    //     0x811c64: add             x5, x5, HEAP, lsl #32
    //     0x811c68: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b2e8] "donationId"
    //     0x811c6c: ldr             x16, [x16, #0x2e8]
    //     0x811c70: cmp             w5, w16
    //     0x811c74: b.ne            #0x811c98
    //     0x811c78: ldur            w5, [x4, #0x23]
    //     0x811c7c: add             x5, x5, HEAP, lsl #32
    //     0x811c80: sub             w6, w1, w5
    //     0x811c84: add             x5, fp, w6, sxtw #2
    //     0x811c88: ldr             x5, [x5, #8]
    //     0x811c8c: mov             x6, x5
    //     0x811c90: movz            x5, #0x1
    //     0x811c94: b               #0x811ca0
    //     0x811c98: mov             x6, NULL
    //     0x811c9c: movz            x5, #0
    //     0x811ca0: stur            x6, [fp, #-0x10]
    //     0x811ca4: lsl             x7, x5, #1
    //     0x811ca8: lsl             w5, w7, #1
    //     0x811cac: add             w7, w5, #8
    //     0x811cb0: add             x16, x4, w7, sxtw #1
    //     0x811cb4: ldur            w8, [x16, #0xf]
    //     0x811cb8: add             x8, x8, HEAP, lsl #32
    //     0x811cbc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b2f0] "infiniteScroll"
    //     0x811cc0: ldr             x16, [x16, #0x2f0]
    //     0x811cc4: cmp             w8, w16
    //     0x811cc8: b.ne            #0x811cf0
    //     0x811ccc: add             w7, w5, #0xa
    //     0x811cd0: add             x16, x4, w7, sxtw #1
    //     0x811cd4: ldur            w5, [x16, #0xf]
    //     0x811cd8: add             x5, x5, HEAP, lsl #32
    //     0x811cdc: sub             w4, w1, w5
    //     0x811ce0: add             x1, fp, w4, sxtw #2
    //     0x811ce4: ldr             x1, [x1, #8]
    //     0x811ce8: mov             x4, x1
    //     0x811cec: b               #0x811cf4
    //     0x811cf0: add             x4, NULL, #0x20  ; true
    //     0x811cf4: stur            x4, [fp, #-8]
    // 0x811cf8: CheckStackOverflow
    //     0x811cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811cfc: cmp             SP, x16
    //     0x811d00: b.ls            #0x811dc8
    // 0x811d04: r1 = <SortOrdering>
    //     0x811d04: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b2f8] TypeArguments: <SortOrdering>
    //     0x811d08: ldr             x1, [x1, #0x2f8]
    // 0x811d0c: r0 = Rx()
    //     0x811d0c: bl              #0x80ef18  ; AllocateRxStub -> Rx<X0> (size=0x20)
    // 0x811d10: mov             x1, x0
    // 0x811d14: r2 = Instance_SortOrdering
    //     0x811d14: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b300] Obj!SortOrdering@e25c81
    //     0x811d18: ldr             x2, [x2, #0x300]
    // 0x811d1c: stur            x0, [fp, #-0x30]
    // 0x811d20: r0 = _RxImpl()
    //     0x811d20: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x811d24: ldur            x0, [fp, #-0x30]
    // 0x811d28: ldur            x1, [fp, #-0x18]
    // 0x811d2c: StoreField: r1->field_47 = r0
    //     0x811d2c: stur            w0, [x1, #0x47]
    //     0x811d30: ldurb           w16, [x1, #-1]
    //     0x811d34: ldurb           w17, [x0, #-1]
    //     0x811d38: and             x16, x17, x16, lsr #2
    //     0x811d3c: tst             x16, HEAP, lsr #32
    //     0x811d40: b.eq            #0x811d48
    //     0x811d44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811d48: ldur            x0, [fp, #-0x28]
    // 0x811d4c: StoreField: r1->field_37 = r0
    //     0x811d4c: stur            w0, [x1, #0x37]
    //     0x811d50: ldurb           w16, [x1, #-1]
    //     0x811d54: ldurb           w17, [x0, #-1]
    //     0x811d58: and             x16, x17, x16, lsr #2
    //     0x811d5c: tst             x16, HEAP, lsr #32
    //     0x811d60: b.eq            #0x811d68
    //     0x811d64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811d68: ldur            x0, [fp, #-0x10]
    // 0x811d6c: StoreField: r1->field_3b = r0
    //     0x811d6c: stur            w0, [x1, #0x3b]
    //     0x811d70: tbz             w0, #0, #0x811d8c
    //     0x811d74: ldurb           w16, [x1, #-1]
    //     0x811d78: ldurb           w17, [x0, #-1]
    //     0x811d7c: and             x16, x17, x16, lsr #2
    //     0x811d80: tst             x16, HEAP, lsr #32
    //     0x811d84: b.eq            #0x811d8c
    //     0x811d88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811d8c: ldur            x0, [fp, #-0x20]
    // 0x811d90: StoreField: r1->field_3f = r0
    //     0x811d90: stur            w0, [x1, #0x3f]
    //     0x811d94: ldurb           w16, [x1, #-1]
    //     0x811d98: ldurb           w17, [x0, #-1]
    //     0x811d9c: and             x16, x17, x16, lsr #2
    //     0x811da0: tst             x16, HEAP, lsr #32
    //     0x811da4: b.eq            #0x811dac
    //     0x811da8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x811dac: ldur            x0, [fp, #-8]
    // 0x811db0: StoreField: r1->field_43 = r0
    //     0x811db0: stur            w0, [x1, #0x43]
    // 0x811db4: r0 = _TransactionHistoryController&GetxController&PagingMixin()
    //     0x811db4: bl              #0x811dd0  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::_TransactionHistoryController&GetxController&PagingMixin
    // 0x811db8: r0 = Null
    //     0x811db8: mov             x0, NULL
    // 0x811dbc: LeaveFrame
    //     0x811dbc: mov             SP, fp
    //     0x811dc0: ldp             fp, lr, [SP], #0x10
    // 0x811dc4: ret
    //     0x811dc4: ret             
    // 0x811dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811dc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811dcc: b               #0x811d04
  }
  [closure] Future<void> selectOrderBy(dynamic) {
    // ** addr: 0xae6c34, size: 0x38
    // 0xae6c34: EnterFrame
    //     0xae6c34: stp             fp, lr, [SP, #-0x10]!
    //     0xae6c38: mov             fp, SP
    // 0xae6c3c: ldr             x0, [fp, #0x10]
    // 0xae6c40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae6c40: ldur            w1, [x0, #0x17]
    // 0xae6c44: DecompressPointer r1
    //     0xae6c44: add             x1, x1, HEAP, lsl #32
    // 0xae6c48: CheckStackOverflow
    //     0xae6c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6c4c: cmp             SP, x16
    //     0xae6c50: b.ls            #0xae6c64
    // 0xae6c54: r0 = selectOrderBy()
    //     0xae6c54: bl              #0xae6c6c  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::selectOrderBy
    // 0xae6c58: LeaveFrame
    //     0xae6c58: mov             SP, fp
    //     0xae6c5c: ldp             fp, lr, [SP], #0x10
    // 0xae6c60: ret
    //     0xae6c60: ret             
    // 0xae6c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6c64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6c68: b               #0xae6c54
  }
  _ selectOrderBy(/* No info */) async {
    // ** addr: 0xae6c6c, size: 0x250
    // 0xae6c6c: EnterFrame
    //     0xae6c6c: stp             fp, lr, [SP, #-0x10]!
    //     0xae6c70: mov             fp, SP
    // 0xae6c74: AllocStack(0x40)
    //     0xae6c74: sub             SP, SP, #0x40
    // 0xae6c78: SetupParameters(TransactionHistoryController this /* r1 => r1, fp-0x10 */)
    //     0xae6c78: stur            NULL, [fp, #-8]
    //     0xae6c7c: stur            x1, [fp, #-0x10]
    // 0xae6c80: CheckStackOverflow
    //     0xae6c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6c84: cmp             SP, x16
    //     0xae6c88: b.ls            #0xae6eb4
    // 0xae6c8c: InitAsync() -> Future<void?>
    //     0xae6c8c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae6c90: bl              #0x661298  ; InitAsyncStub
    // 0xae6c94: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae6c94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae6c98: ldr             x0, [x0, #0x2670]
    //     0xae6c9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae6ca0: cmp             w0, w16
    //     0xae6ca4: b.ne            #0xae6cb0
    //     0xae6ca8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae6cac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae6cb0: ldur            x0, [fp, #-0x10]
    // 0xae6cb4: LoadField: r2 = r0->field_47
    //     0xae6cb4: ldur            w2, [x0, #0x47]
    // 0xae6cb8: DecompressPointer r2
    //     0xae6cb8: add             x2, x2, HEAP, lsl #32
    // 0xae6cbc: mov             x1, x2
    // 0xae6cc0: stur            x2, [fp, #-0x18]
    // 0xae6cc4: r0 = value()
    //     0xae6cc4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6cc8: LoadField: r2 = r0->field_7
    //     0xae6cc8: ldur            w2, [x0, #7]
    // 0xae6ccc: DecompressPointer r2
    //     0xae6ccc: add             x2, x2, HEAP, lsl #32
    // 0xae6cd0: stur            x2, [fp, #-0x20]
    // 0xae6cd4: r1 = <OrderBy>
    //     0xae6cd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc08] TypeArguments: <OrderBy>
    //     0xae6cd8: ldr             x1, [x1, #0xc08]
    // 0xae6cdc: r0 = NRadioListTile()
    //     0xae6cdc: bl              #0xae6fb0  ; AllocateNRadioListTileStub -> NRadioListTile<X0> (size=0x2c)
    // 0xae6ce0: mov             x3, x0
    // 0xae6ce4: r0 = "Terbaru"
    //     0xae6ce4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc10] "Terbaru"
    //     0xae6ce8: ldr             x0, [x0, #0xc10]
    // 0xae6cec: stur            x3, [fp, #-0x28]
    // 0xae6cf0: StoreField: r3->field_f = r0
    //     0xae6cf0: stur            w0, [x3, #0xf]
    // 0xae6cf4: r0 = Instance_OrderBy
    //     0xae6cf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc18] Obj!OrderBy@e30d01
    //     0xae6cf8: ldr             x0, [x0, #0xc18]
    // 0xae6cfc: StoreField: r3->field_13 = r0
    //     0xae6cfc: stur            w0, [x3, #0x13]
    // 0xae6d00: ldur            x0, [fp, #-0x20]
    // 0xae6d04: ArrayStore: r3[0] = r0  ; List_4
    //     0xae6d04: stur            w0, [x3, #0x17]
    // 0xae6d08: r1 = Function '<anonymous closure>':.
    //     0xae6d08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc20] AnonymousClosure: (0xae6fbc), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::selectOrderBy (0xae6c6c)
    //     0xae6d0c: ldr             x1, [x1, #0xc20]
    // 0xae6d10: r2 = Null
    //     0xae6d10: mov             x2, NULL
    // 0xae6d14: r0 = AllocateClosure()
    //     0xae6d14: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6d18: mov             x1, x0
    // 0xae6d1c: ldur            x0, [fp, #-0x28]
    // 0xae6d20: StoreField: r0->field_1b = r1
    //     0xae6d20: stur            w1, [x0, #0x1b]
    // 0xae6d24: r2 = true
    //     0xae6d24: add             x2, NULL, #0x20  ; true
    // 0xae6d28: StoreField: r0->field_27 = r2
    //     0xae6d28: stur            w2, [x0, #0x27]
    // 0xae6d2c: ldur            x1, [fp, #-0x18]
    // 0xae6d30: r0 = value()
    //     0xae6d30: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6d34: LoadField: r2 = r0->field_7
    //     0xae6d34: ldur            w2, [x0, #7]
    // 0xae6d38: DecompressPointer r2
    //     0xae6d38: add             x2, x2, HEAP, lsl #32
    // 0xae6d3c: stur            x2, [fp, #-0x20]
    // 0xae6d40: r1 = <OrderBy>
    //     0xae6d40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc08] TypeArguments: <OrderBy>
    //     0xae6d44: ldr             x1, [x1, #0xc08]
    // 0xae6d48: r0 = NRadioListTile()
    //     0xae6d48: bl              #0xae6fb0  ; AllocateNRadioListTileStub -> NRadioListTile<X0> (size=0x2c)
    // 0xae6d4c: mov             x3, x0
    // 0xae6d50: r0 = "Terbanyak"
    //     0xae6d50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc28] "Terbanyak"
    //     0xae6d54: ldr             x0, [x0, #0xc28]
    // 0xae6d58: stur            x3, [fp, #-0x30]
    // 0xae6d5c: StoreField: r3->field_f = r0
    //     0xae6d5c: stur            w0, [x3, #0xf]
    // 0xae6d60: r0 = Instance_OrderBy
    //     0xae6d60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc30] Obj!OrderBy@e30d21
    //     0xae6d64: ldr             x0, [x0, #0xc30]
    // 0xae6d68: StoreField: r3->field_13 = r0
    //     0xae6d68: stur            w0, [x3, #0x13]
    // 0xae6d6c: ldur            x0, [fp, #-0x20]
    // 0xae6d70: ArrayStore: r3[0] = r0  ; List_4
    //     0xae6d70: stur            w0, [x3, #0x17]
    // 0xae6d74: r1 = Function '<anonymous closure>':.
    //     0xae6d74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc38] AnonymousClosure: (0xae6fbc), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::selectOrderBy (0xae6c6c)
    //     0xae6d78: ldr             x1, [x1, #0xc38]
    // 0xae6d7c: r2 = Null
    //     0xae6d7c: mov             x2, NULL
    // 0xae6d80: r0 = AllocateClosure()
    //     0xae6d80: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6d84: mov             x1, x0
    // 0xae6d88: ldur            x0, [fp, #-0x30]
    // 0xae6d8c: StoreField: r0->field_1b = r1
    //     0xae6d8c: stur            w1, [x0, #0x1b]
    // 0xae6d90: r1 = true
    //     0xae6d90: add             x1, NULL, #0x20  ; true
    // 0xae6d94: StoreField: r0->field_27 = r1
    //     0xae6d94: stur            w1, [x0, #0x27]
    // 0xae6d98: r1 = Null
    //     0xae6d98: mov             x1, NULL
    // 0xae6d9c: r2 = 6
    //     0xae6d9c: movz            x2, #0x6
    // 0xae6da0: r0 = AllocateArray()
    //     0xae6da0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae6da4: mov             x2, x0
    // 0xae6da8: ldur            x0, [fp, #-0x28]
    // 0xae6dac: stur            x2, [fp, #-0x20]
    // 0xae6db0: StoreField: r2->field_f = r0
    //     0xae6db0: stur            w0, [x2, #0xf]
    // 0xae6db4: r16 = Instance_Divider
    //     0xae6db4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xae6db8: ldr             x16, [x16, #0xc28]
    // 0xae6dbc: StoreField: r2->field_13 = r16
    //     0xae6dbc: stur            w16, [x2, #0x13]
    // 0xae6dc0: ldur            x0, [fp, #-0x30]
    // 0xae6dc4: ArrayStore: r2[0] = r0  ; List_4
    //     0xae6dc4: stur            w0, [x2, #0x17]
    // 0xae6dc8: r1 = <Widget>
    //     0xae6dc8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae6dcc: r0 = AllocateGrowableArray()
    //     0xae6dcc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae6dd0: mov             x1, x0
    // 0xae6dd4: ldur            x0, [fp, #-0x20]
    // 0xae6dd8: stur            x1, [fp, #-0x28]
    // 0xae6ddc: StoreField: r1->field_f = r0
    //     0xae6ddc: stur            w0, [x1, #0xf]
    // 0xae6de0: r0 = 6
    //     0xae6de0: movz            x0, #0x6
    // 0xae6de4: StoreField: r1->field_b = r0
    //     0xae6de4: stur            w0, [x1, #0xb]
    // 0xae6de8: r0 = NDialog()
    //     0xae6de8: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xae6dec: mov             x3, x0
    // 0xae6df0: r0 = "Urutkan"
    //     0xae6df0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc40] "Urutkan"
    //     0xae6df4: ldr             x0, [x0, #0xc40]
    // 0xae6df8: stur            x3, [fp, #-0x20]
    // 0xae6dfc: StoreField: r3->field_b = r0
    //     0xae6dfc: stur            w0, [x3, #0xb]
    // 0xae6e00: r1 = Function '<anonymous closure>':.
    //     0xae6e00: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc48] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xae6e04: ldr             x1, [x1, #0xc48]
    // 0xae6e08: r2 = Null
    //     0xae6e08: mov             x2, NULL
    // 0xae6e0c: r0 = AllocateClosure()
    //     0xae6e0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6e10: mov             x1, x0
    // 0xae6e14: ldur            x0, [fp, #-0x20]
    // 0xae6e18: ArrayStore: r0[0] = r1  ; List_4
    //     0xae6e18: stur            w1, [x0, #0x17]
    // 0xae6e1c: ldur            x1, [fp, #-0x28]
    // 0xae6e20: StoreField: r0->field_f = r1
    //     0xae6e20: stur            w1, [x0, #0xf]
    // 0xae6e24: r1 = Function '<anonymous closure>':.
    //     0xae6e24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc50] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xae6e28: ldr             x1, [x1, #0xc50]
    // 0xae6e2c: r2 = Null
    //     0xae6e2c: mov             x2, NULL
    // 0xae6e30: r0 = AllocateClosure()
    //     0xae6e30: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6e34: mov             x1, x0
    // 0xae6e38: ldur            x0, [fp, #-0x20]
    // 0xae6e3c: StoreField: r0->field_1b = r1
    //     0xae6e3c: stur            w1, [x0, #0x1b]
    // 0xae6e40: r0 = ListTileTheme()
    //     0xae6e40: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xae6e44: mov             x1, x0
    // 0xae6e48: r0 = Instance_EdgeInsets
    //     0xae6e48: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xae6e4c: StoreField: r1->field_2b = r0
    //     0xae6e4c: stur            w0, [x1, #0x2b]
    // 0xae6e50: ldur            x0, [fp, #-0x20]
    // 0xae6e54: StoreField: r1->field_b = r0
    //     0xae6e54: stur            w0, [x1, #0xb]
    // 0xae6e58: r16 = <OrderBy>
    //     0xae6e58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc08] TypeArguments: <OrderBy>
    //     0xae6e5c: ldr             x16, [x16, #0xc08]
    // 0xae6e60: stp             x1, x16, [SP]
    // 0xae6e64: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae6e64: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae6e68: r0 = ExtensionDialog.dialog()
    //     0xae6e68: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xae6e6c: mov             x1, x0
    // 0xae6e70: stur            x1, [fp, #-0x20]
    // 0xae6e74: r0 = Await()
    //     0xae6e74: bl              #0x661044  ; AwaitStub
    // 0xae6e78: stur            x0, [fp, #-0x20]
    // 0xae6e7c: cmp             w0, NULL
    // 0xae6e80: b.eq            #0xae6eac
    // 0xae6e84: ldur            x1, [fp, #-0x18]
    // 0xae6e88: r0 = value()
    //     0xae6e88: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6e8c: mov             x1, x0
    // 0xae6e90: ldur            x2, [fp, #-0x20]
    // 0xae6e94: r0 = copyWith()
    //     0xae6e94: bl              #0xae6f70  ; [package:nuonline/app/data/enums/sort_ordering_enum.dart] SortOrdering::copyWith
    // 0xae6e98: ldur            x1, [fp, #-0x18]
    // 0xae6e9c: mov             x2, x0
    // 0xae6ea0: r0 = value=()
    //     0xae6ea0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xae6ea4: ldur            x1, [fp, #-0x10]
    // 0xae6ea8: r0 = onPageRefresh()
    //     0xae6ea8: bl              #0xae6ebc  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageRefresh
    // 0xae6eac: r0 = Null
    //     0xae6eac: mov             x0, NULL
    // 0xae6eb0: r0 = ReturnAsyncNotFuture()
    //     0xae6eb0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae6eb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6eb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6eb8: b               #0xae6c8c
  }
  [closure] void <anonymous closure>(dynamic, OrderBy?) {
    // ** addr: 0xae6fbc, size: 0x68
    // 0xae6fbc: EnterFrame
    //     0xae6fbc: stp             fp, lr, [SP, #-0x10]!
    //     0xae6fc0: mov             fp, SP
    // 0xae6fc4: AllocStack(0x10)
    //     0xae6fc4: sub             SP, SP, #0x10
    // 0xae6fc8: CheckStackOverflow
    //     0xae6fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6fcc: cmp             SP, x16
    //     0xae6fd0: b.ls            #0xae701c
    // 0xae6fd4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae6fd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae6fd8: ldr             x0, [x0, #0x2670]
    //     0xae6fdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae6fe0: cmp             w0, w16
    //     0xae6fe4: b.ne            #0xae6ff0
    //     0xae6fe8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae6fec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae6ff0: r16 = <OrderBy>
    //     0xae6ff0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc08] TypeArguments: <OrderBy>
    //     0xae6ff4: ldr             x16, [x16, #0xc08]
    // 0xae6ff8: ldr             lr, [fp, #0x10]
    // 0xae6ffc: stp             lr, x16, [SP]
    // 0xae7000: r4 = const [0x1, 0x1, 0x1, 0, result, 0, null]
    //     0xae7000: add             x4, PP, #0x25, lsl #12  ; [pp+0x257f0] List(7) [0x1, 0x1, 0x1, 0, "result", 0, Null]
    //     0xae7004: ldr             x4, [x4, #0x7f0]
    // 0xae7008: r0 = GetNavigation.back()
    //     0xae7008: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xae700c: r0 = Null
    //     0xae700c: mov             x0, NULL
    // 0xae7010: LeaveFrame
    //     0xae7010: mov             SP, fp
    //     0xae7014: ldp             fp, lr, [SP], #0x10
    // 0xae7018: ret
    //     0xae7018: ret             
    // 0xae701c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae701c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7020: b               #0xae6fd4
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xe34978, size: 0x48
    // 0xe34978: EnterFrame
    //     0xe34978: stp             fp, lr, [SP, #-0x10]!
    //     0xe3497c: mov             fp, SP
    // 0xe34980: AllocStack(0x18)
    //     0xe34980: sub             SP, SP, #0x18
    // 0xe34984: SetupParameters(TransactionHistoryController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe34984: stur            NULL, [fp, #-8]
    //     0xe34988: stur            x1, [fp, #-0x10]
    //     0xe3498c: stur            x2, [fp, #-0x18]
    // 0xe34990: CheckStackOverflow
    //     0xe34990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34994: cmp             SP, x16
    //     0xe34998: b.ls            #0xe349b8
    // 0xe3499c: InitAsync() -> Future<void?>
    //     0xe3499c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe349a0: bl              #0x661298  ; InitAsyncStub
    // 0xe349a4: ldur            x1, [fp, #-0x10]
    // 0xe349a8: r2 = true
    //     0xe349a8: add             x2, NULL, #0x20  ; true
    // 0xe349ac: r0 = hasError=()
    //     0xe349ac: bl              #0x7f8b00  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError=
    // 0xe349b0: r0 = Null
    //     0xe349b0: mov             x0, NULL
    // 0xe349b4: r0 = ReturnAsyncNotFuture()
    //     0xe349b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe349b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe349b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe349bc: b               #0xe3499c
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe37a30, size: 0x48
    // 0xe37a30: EnterFrame
    //     0xe37a30: stp             fp, lr, [SP, #-0x10]!
    //     0xe37a34: mov             fp, SP
    // 0xe37a38: AllocStack(0x8)
    //     0xe37a38: sub             SP, SP, #8
    // 0xe37a3c: SetupParameters(TransactionHistoryController this /* r1 => r0, fp-0x8 */)
    //     0xe37a3c: mov             x0, x1
    //     0xe37a40: stur            x1, [fp, #-8]
    // 0xe37a44: CheckStackOverflow
    //     0xe37a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37a48: cmp             SP, x16
    //     0xe37a4c: b.ls            #0xe37a70
    // 0xe37a50: mov             x1, x0
    // 0xe37a54: r0 = onPageRequest()
    //     0xe37a54: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe37a58: ldur            x1, [fp, #-8]
    // 0xe37a5c: r0 = executeOnlineMode()
    //     0xe37a5c: bl              #0xe37a78  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin&OnlineMixin::executeOnlineMode
    // 0xe37a60: r0 = Null
    //     0xe37a60: mov             x0, NULL
    // 0xe37a64: LeaveFrame
    //     0xe37a64: mov             SP, fp
    //     0xe37a68: ldp             fp, lr, [SP], #0x10
    // 0xe37a6c: ret
    //     0xe37a6c: ret             
    // 0xe37a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37a74: b               #0xe37a50
  }
}
