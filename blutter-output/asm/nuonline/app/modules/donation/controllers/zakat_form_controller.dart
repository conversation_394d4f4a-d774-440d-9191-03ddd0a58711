// lib: , url: package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart

// class id: 1050216, size: 0x8
class :: {
}

// class id: 1908, size: 0x40, field offset: 0x40
//   transformed mixin,
abstract class _ZakatFormController&DonationFormController&FetchMixin extends DonationFormController
     with FetchMixin<X0> {

  _ fetch(/* No info */) async {
    // ** addr: 0x8f53b4, size: 0x80
    // 0x8f53b4: EnterFrame
    //     0x8f53b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f53b8: mov             fp, SP
    // 0x8f53bc: AllocStack(0x30)
    //     0x8f53bc: sub             SP, SP, #0x30
    // 0x8f53c0: SetupParameters(_ZakatFormController&DonationFormController&FetchMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f53c0: stur            NULL, [fp, #-8]
    //     0x8f53c4: stur            x1, [fp, #-0x10]
    // 0x8f53c8: CheckStackOverflow
    //     0x8f53c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f53cc: cmp             SP, x16
    //     0x8f53d0: b.ls            #0x8f542c
    // 0x8f53d4: r1 = 1
    //     0x8f53d4: movz            x1, #0x1
    // 0x8f53d8: r0 = AllocateContext()
    //     0x8f53d8: bl              #0xec126c  ; AllocateContextStub
    // 0x8f53dc: mov             x2, x0
    // 0x8f53e0: ldur            x1, [fp, #-0x10]
    // 0x8f53e4: stur            x2, [fp, #-0x18]
    // 0x8f53e8: StoreField: r2->field_f = r1
    //     0x8f53e8: stur            w1, [x2, #0xf]
    // 0x8f53ec: InitAsync() -> Future<void?>
    //     0x8f53ec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f53f0: bl              #0x661298  ; InitAsyncStub
    // 0x8f53f4: ldur            x1, [fp, #-0x10]
    // 0x8f53f8: r0 = onFetchRequested()
    //     0x8f53f8: bl              #0x7efbfc  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onFetchRequested
    // 0x8f53fc: ldur            x2, [fp, #-0x18]
    // 0x8f5400: r1 = Function '<anonymous closure>':.
    //     0x8f5400: add             x1, PP, #0x35, lsl #12  ; [pp+0x357c0] AnonymousClosure: (0x8f5434), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin::fetch (0x8f53b4)
    //     0x8f5404: ldr             x1, [x1, #0x7c0]
    // 0x8f5408: stur            x0, [fp, #-0x10]
    // 0x8f540c: r0 = AllocateClosure()
    //     0x8f540c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f5410: r16 = <void?>
    //     0x8f5410: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f5414: ldur            lr, [fp, #-0x10]
    // 0x8f5418: stp             lr, x16, [SP, #8]
    // 0x8f541c: str             x0, [SP]
    // 0x8f5420: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f5420: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f5424: r0 = then()
    //     0x8f5424: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f5428: r0 = ReturnAsync()
    //     0x8f5428: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8f542c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f542c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5430: b               #0x8f53d4
  }
  [closure] Object? <anonymous closure>(dynamic, ApiResult<ZakatSetting>) {
    // ** addr: 0x8f5434, size: 0x94
    // 0x8f5434: EnterFrame
    //     0x8f5434: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5438: mov             fp, SP
    // 0x8f543c: AllocStack(0x28)
    //     0x8f543c: sub             SP, SP, #0x28
    // 0x8f5440: SetupParameters()
    //     0x8f5440: ldr             x0, [fp, #0x18]
    //     0x8f5444: ldur            w3, [x0, #0x17]
    //     0x8f5448: add             x3, x3, HEAP, lsl #32
    //     0x8f544c: stur            x3, [fp, #-8]
    // 0x8f5450: CheckStackOverflow
    //     0x8f5450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5454: cmp             SP, x16
    //     0x8f5458: b.ls            #0x8f54c0
    // 0x8f545c: mov             x2, x3
    // 0x8f5460: r1 = Function '<anonymous closure>':.
    //     0x8f5460: add             x1, PP, #0x35, lsl #12  ; [pp+0x357c8] AnonymousClosure: (0x8f5524), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin::fetch (0x8f53b4)
    //     0x8f5464: ldr             x1, [x1, #0x7c8]
    // 0x8f5468: r0 = AllocateClosure()
    //     0x8f5468: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f546c: ldur            x2, [fp, #-8]
    // 0x8f5470: r1 = Function '<anonymous closure>':.
    //     0x8f5470: add             x1, PP, #0x35, lsl #12  ; [pp+0x357d0] AnonymousClosure: (0x8f54c8), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin::fetch (0x8f53b4)
    //     0x8f5474: ldr             x1, [x1, #0x7d0]
    // 0x8f5478: stur            x0, [fp, #-8]
    // 0x8f547c: r0 = AllocateClosure()
    //     0x8f547c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f5480: mov             x1, x0
    // 0x8f5484: ldr             x0, [fp, #0x10]
    // 0x8f5488: r2 = LoadClassIdInstr(r0)
    //     0x8f5488: ldur            x2, [x0, #-1]
    //     0x8f548c: ubfx            x2, x2, #0xc, #0x14
    // 0x8f5490: r16 = <Object?>
    //     0x8f5490: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8f5494: stp             x0, x16, [SP, #0x10]
    // 0x8f5498: ldur            x16, [fp, #-8]
    // 0x8f549c: stp             x16, x1, [SP]
    // 0x8f54a0: mov             x0, x2
    // 0x8f54a4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8f54a4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8f54a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8f54a8: sub             lr, x0, #1, lsl #12
    //     0x8f54ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8f54b0: blr             lr
    // 0x8f54b4: LeaveFrame
    //     0x8f54b4: mov             SP, fp
    //     0x8f54b8: ldp             fp, lr, [SP], #0x10
    // 0x8f54bc: ret
    //     0x8f54bc: ret             
    // 0x8f54c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f54c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f54c4: b               #0x8f545c
  }
  [closure] Future<void> <anonymous closure>(dynamic, NetworkExceptions) {
    // ** addr: 0x8f54c8, size: 0x5c
    // 0x8f54c8: EnterFrame
    //     0x8f54c8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f54cc: mov             fp, SP
    // 0x8f54d0: AllocStack(0x8)
    //     0x8f54d0: sub             SP, SP, #8
    // 0x8f54d4: SetupParameters()
    //     0x8f54d4: ldr             x0, [fp, #0x18]
    //     0x8f54d8: ldur            w1, [x0, #0x17]
    //     0x8f54dc: add             x1, x1, HEAP, lsl #32
    // 0x8f54e0: CheckStackOverflow
    //     0x8f54e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f54e4: cmp             SP, x16
    //     0x8f54e8: b.ls            #0x8f551c
    // 0x8f54ec: LoadField: r0 = r1->field_f
    //     0x8f54ec: ldur            w0, [x1, #0xf]
    // 0x8f54f0: DecompressPointer r0
    //     0x8f54f0: add             x0, x0, HEAP, lsl #32
    // 0x8f54f4: ldr             x1, [fp, #0x10]
    // 0x8f54f8: stur            x0, [fp, #-8]
    // 0x8f54fc: r0 = getErrorMessage()
    //     0x8f54fc: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8f5500: ldur            x1, [fp, #-8]
    // 0x8f5504: ldr             x2, [fp, #0x10]
    // 0x8f5508: mov             x3, x0
    // 0x8f550c: r0 = onFetchFailure()
    //     0x8f550c: bl              #0x7e5ff8  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onFetchFailure
    // 0x8f5510: LeaveFrame
    //     0x8f5510: mov             SP, fp
    //     0x8f5514: ldp             fp, lr, [SP], #0x10
    // 0x8f5518: ret
    //     0x8f5518: ret             
    // 0x8f551c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f551c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5520: b               #0x8f54ec
  }
  [closure] Future<void> <anonymous closure>(dynamic, ZakatSetting, Pagination?) {
    // ** addr: 0x8f5524, size: 0x4c
    // 0x8f5524: EnterFrame
    //     0x8f5524: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5528: mov             fp, SP
    // 0x8f552c: ldr             x0, [fp, #0x20]
    // 0x8f5530: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f5530: ldur            w1, [x0, #0x17]
    // 0x8f5534: DecompressPointer r1
    //     0x8f5534: add             x1, x1, HEAP, lsl #32
    // 0x8f5538: CheckStackOverflow
    //     0x8f5538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f553c: cmp             SP, x16
    //     0x8f5540: b.ls            #0x8f5568
    // 0x8f5544: LoadField: r0 = r1->field_f
    //     0x8f5544: ldur            w0, [x1, #0xf]
    // 0x8f5548: DecompressPointer r0
    //     0x8f5548: add             x0, x0, HEAP, lsl #32
    // 0x8f554c: mov             x1, x0
    // 0x8f5550: ldr             x2, [fp, #0x18]
    // 0x8f5554: ldr             x3, [fp, #0x10]
    // 0x8f5558: r0 = onFetchLoaded()
    //     0x8f5558: bl              #0x7db010  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onFetchLoaded
    // 0x8f555c: LeaveFrame
    //     0x8f555c: mov             SP, fp
    //     0x8f5560: ldp             fp, lr, [SP], #0x10
    // 0x8f5564: ret
    //     0x8f5564: ret             
    // 0x8f5568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f5568: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f556c: b               #0x8f5544
  }
}

// class id: 1909, size: 0x48, field offset: 0x40
//   transformed mixin,
abstract class _ZakatFormController&DonationFormController&FetchMixin&StateMixin extends _ZakatFormController&DonationFormController&FetchMixin
     with StateMixin<X0> {

  get _ value(/* No info */) {
    // ** addr: 0x72ba14, size: 0x48
    // 0x72ba14: EnterFrame
    //     0x72ba14: stp             fp, lr, [SP, #-0x10]!
    //     0x72ba18: mov             fp, SP
    // 0x72ba1c: AllocStack(0x8)
    //     0x72ba1c: sub             SP, SP, #8
    // 0x72ba20: SetupParameters(_ZakatFormController&DonationFormController&FetchMixin&StateMixin this /* r1 => r0, fp-0x8 */)
    //     0x72ba20: mov             x0, x1
    //     0x72ba24: stur            x1, [fp, #-8]
    // 0x72ba28: CheckStackOverflow
    //     0x72ba28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72ba2c: cmp             SP, x16
    //     0x72ba30: b.ls            #0x72ba54
    // 0x72ba34: mov             x1, x0
    // 0x72ba38: r0 = notifyChildrens()
    //     0x72ba38: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x72ba3c: ldur            x1, [fp, #-8]
    // 0x72ba40: LoadField: r0 = r1->field_3f
    //     0x72ba40: ldur            w0, [x1, #0x3f]
    // 0x72ba44: DecompressPointer r0
    //     0x72ba44: add             x0, x0, HEAP, lsl #32
    // 0x72ba48: LeaveFrame
    //     0x72ba48: mov             SP, fp
    //     0x72ba4c: ldp             fp, lr, [SP], #0x10
    // 0x72ba50: ret
    //     0x72ba50: ret             
    // 0x72ba54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72ba54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72ba58: b               #0x72ba34
  }
  _ change(/* No info */) {
    // ** addr: 0x7db30c, size: 0x90
    // 0x7db30c: EnterFrame
    //     0x7db30c: stp             fp, lr, [SP, #-0x10]!
    //     0x7db310: mov             fp, SP
    // 0x7db314: mov             x16, x2
    // 0x7db318: mov             x2, x1
    // 0x7db31c: mov             x1, x16
    // 0x7db320: mov             x0, x3
    // 0x7db324: CheckStackOverflow
    //     0x7db324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7db328: cmp             SP, x16
    //     0x7db32c: b.ls            #0x7db394
    // 0x7db330: StoreField: r2->field_43 = r0
    //     0x7db330: stur            w0, [x2, #0x43]
    //     0x7db334: ldurb           w16, [x2, #-1]
    //     0x7db338: ldurb           w17, [x0, #-1]
    //     0x7db33c: and             x16, x17, x16, lsr #2
    //     0x7db340: tst             x16, HEAP, lsr #32
    //     0x7db344: b.eq            #0x7db34c
    //     0x7db348: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7db34c: LoadField: r0 = r2->field_3f
    //     0x7db34c: ldur            w0, [x2, #0x3f]
    // 0x7db350: DecompressPointer r0
    //     0x7db350: add             x0, x0, HEAP, lsl #32
    // 0x7db354: cmp             w1, w0
    // 0x7db358: b.eq            #0x7db37c
    // 0x7db35c: mov             x0, x1
    // 0x7db360: StoreField: r2->field_3f = r0
    //     0x7db360: stur            w0, [x2, #0x3f]
    //     0x7db364: ldurb           w16, [x2, #-1]
    //     0x7db368: ldurb           w17, [x0, #-1]
    //     0x7db36c: and             x16, x17, x16, lsr #2
    //     0x7db370: tst             x16, HEAP, lsr #32
    //     0x7db374: b.eq            #0x7db37c
    //     0x7db378: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7db37c: mov             x1, x2
    // 0x7db380: r0 = _notifyUpdate()
    //     0x7db380: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x7db384: r0 = Null
    //     0x7db384: mov             x0, NULL
    // 0x7db388: LeaveFrame
    //     0x7db388: mov             SP, fp
    //     0x7db38c: ldp             fp, lr, [SP], #0x10
    // 0x7db390: ret
    //     0x7db390: ret             
    // 0x7db394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7db394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7db398: b               #0x7db330
  }
  get _ status(/* No info */) {
    // ** addr: 0x7f8e0c, size: 0x9c
    // 0x7f8e0c: EnterFrame
    //     0x7f8e0c: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8e10: mov             fp, SP
    // 0x7f8e14: AllocStack(0x8)
    //     0x7f8e14: sub             SP, SP, #8
    // 0x7f8e18: SetupParameters(_ZakatFormController&DonationFormController&FetchMixin&StateMixin this /* r1 => r0, fp-0x8 */)
    //     0x7f8e18: mov             x0, x1
    //     0x7f8e1c: stur            x1, [fp, #-8]
    // 0x7f8e20: CheckStackOverflow
    //     0x7f8e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8e24: cmp             SP, x16
    //     0x7f8e28: b.ls            #0x7f8ea0
    // 0x7f8e2c: mov             x1, x0
    // 0x7f8e30: r0 = notifyChildrens()
    //     0x7f8e30: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x7f8e34: ldur            x0, [fp, #-8]
    // 0x7f8e38: LoadField: r1 = r0->field_43
    //     0x7f8e38: ldur            w1, [x0, #0x43]
    // 0x7f8e3c: DecompressPointer r1
    //     0x7f8e3c: add             x1, x1, HEAP, lsl #32
    // 0x7f8e40: cmp             w1, NULL
    // 0x7f8e44: b.ne            #0x7f8e90
    // 0x7f8e48: r0 = RxStatus()
    //     0x7f8e48: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7f8e4c: mov             x1, x0
    // 0x7f8e50: r2 = false
    //     0x7f8e50: add             x2, NULL, #0x30  ; false
    // 0x7f8e54: StoreField: r1->field_f = r2
    //     0x7f8e54: stur            w2, [x1, #0xf]
    // 0x7f8e58: r3 = true
    //     0x7f8e58: add             x3, NULL, #0x20  ; true
    // 0x7f8e5c: StoreField: r1->field_7 = r3
    //     0x7f8e5c: stur            w3, [x1, #7]
    // 0x7f8e60: StoreField: r1->field_b = r2
    //     0x7f8e60: stur            w2, [x1, #0xb]
    // 0x7f8e64: mov             x0, x1
    // 0x7f8e68: ldur            x2, [fp, #-8]
    // 0x7f8e6c: StoreField: r2->field_43 = r0
    //     0x7f8e6c: stur            w0, [x2, #0x43]
    //     0x7f8e70: ldurb           w16, [x2, #-1]
    //     0x7f8e74: ldurb           w17, [x0, #-1]
    //     0x7f8e78: and             x16, x17, x16, lsr #2
    //     0x7f8e7c: tst             x16, HEAP, lsr #32
    //     0x7f8e80: b.eq            #0x7f8e88
    //     0x7f8e84: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7f8e88: mov             x0, x1
    // 0x7f8e8c: b               #0x7f8e94
    // 0x7f8e90: mov             x0, x1
    // 0x7f8e94: LeaveFrame
    //     0x7f8e94: mov             SP, fp
    //     0x7f8e98: ldp             fp, lr, [SP], #0x10
    // 0x7f8e9c: ret
    //     0x7f8e9c: ret             
    // 0x7f8ea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8ea0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8ea4: b               #0x7f8e2c
  }
}

// class id: 1910, size: 0x64, field offset: 0x48
class ZakatFormController extends _ZakatFormController&DonationFormController&FetchMixin&StateMixin {

  _ onFetchLoaded(/* No info */) async {
    // ** addr: 0x7db010, size: 0x24c
    // 0x7db010: EnterFrame
    //     0x7db010: stp             fp, lr, [SP, #-0x10]!
    //     0x7db014: mov             fp, SP
    // 0x7db018: AllocStack(0x40)
    //     0x7db018: sub             SP, SP, #0x40
    // 0x7db01c: SetupParameters(ZakatFormController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7db01c: stur            NULL, [fp, #-8]
    //     0x7db020: stur            x1, [fp, #-0x10]
    //     0x7db024: stur            x2, [fp, #-0x18]
    //     0x7db028: stur            x3, [fp, #-0x20]
    // 0x7db02c: CheckStackOverflow
    //     0x7db02c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7db030: cmp             SP, x16
    //     0x7db034: b.ls            #0x7db254
    // 0x7db038: InitAsync() -> Future<void?>
    //     0x7db038: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7db03c: bl              #0x661298  ; InitAsyncStub
    // 0x7db040: ldur            x0, [fp, #-0x10]
    // 0x7db044: LoadField: r3 = r0->field_4f
    //     0x7db044: ldur            w3, [x0, #0x4f]
    // 0x7db048: DecompressPointer r3
    //     0x7db048: add             x3, x3, HEAP, lsl #32
    // 0x7db04c: stur            x3, [fp, #-0x20]
    // 0x7db050: r1 = <ZakatQuality>
    //     0x7db050: add             x1, PP, #0x30, lsl #12  ; [pp+0x30040] TypeArguments: <ZakatQuality>
    //     0x7db054: ldr             x1, [x1, #0x40]
    // 0x7db058: r2 = 0
    //     0x7db058: movz            x2, #0
    // 0x7db05c: r0 = _GrowableList()
    //     0x7db05c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7db060: ldur            x2, [fp, #-0x18]
    // 0x7db064: stur            x0, [fp, #-0x38]
    // 0x7db068: LoadField: r1 = r2->field_1b
    //     0x7db068: ldur            w1, [x2, #0x1b]
    // 0x7db06c: DecompressPointer r1
    //     0x7db06c: add             x1, x1, HEAP, lsl #32
    // 0x7db070: stur            x1, [fp, #-0x30]
    // 0x7db074: LoadField: r3 = r2->field_b
    //     0x7db074: ldur            x3, [x2, #0xb]
    // 0x7db078: stur            x3, [fp, #-0x28]
    // 0x7db07c: r0 = ZakatQuality()
    //     0x7db07c: bl              #0x7db3d0  ; AllocateZakatQualityStub -> ZakatQuality (size=0x14)
    // 0x7db080: mov             x2, x0
    // 0x7db084: r0 = "Kualitas Premium"
    //     0x7db084: add             x0, PP, #0x30, lsl #12  ; [pp+0x30058] "Kualitas Premium"
    //     0x7db088: ldr             x0, [x0, #0x58]
    // 0x7db08c: stur            x2, [fp, #-0x40]
    // 0x7db090: StoreField: r2->field_7 = r0
    //     0x7db090: stur            w0, [x2, #7]
    // 0x7db094: ldur            x0, [fp, #-0x28]
    // 0x7db098: StoreField: r2->field_b = r0
    //     0x7db098: stur            x0, [x2, #0xb]
    // 0x7db09c: ldur            x0, [fp, #-0x30]
    // 0x7db0a0: tbnz            w0, #4, #0x7db120
    // 0x7db0a4: ldur            x0, [fp, #-0x38]
    // 0x7db0a8: LoadField: r1 = r0->field_b
    //     0x7db0a8: ldur            w1, [x0, #0xb]
    // 0x7db0ac: LoadField: r3 = r0->field_f
    //     0x7db0ac: ldur            w3, [x0, #0xf]
    // 0x7db0b0: DecompressPointer r3
    //     0x7db0b0: add             x3, x3, HEAP, lsl #32
    // 0x7db0b4: LoadField: r4 = r3->field_b
    //     0x7db0b4: ldur            w4, [x3, #0xb]
    // 0x7db0b8: r3 = LoadInt32Instr(r1)
    //     0x7db0b8: sbfx            x3, x1, #1, #0x1f
    // 0x7db0bc: stur            x3, [fp, #-0x28]
    // 0x7db0c0: r1 = LoadInt32Instr(r4)
    //     0x7db0c0: sbfx            x1, x4, #1, #0x1f
    // 0x7db0c4: cmp             x3, x1
    // 0x7db0c8: b.ne            #0x7db0d4
    // 0x7db0cc: mov             x1, x0
    // 0x7db0d0: r0 = _growToNextCapacity()
    //     0x7db0d0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7db0d4: ldur            x3, [fp, #-0x38]
    // 0x7db0d8: ldur            x2, [fp, #-0x28]
    // 0x7db0dc: add             x0, x2, #1
    // 0x7db0e0: lsl             x1, x0, #1
    // 0x7db0e4: StoreField: r3->field_b = r1
    //     0x7db0e4: stur            w1, [x3, #0xb]
    // 0x7db0e8: LoadField: r1 = r3->field_f
    //     0x7db0e8: ldur            w1, [x3, #0xf]
    // 0x7db0ec: DecompressPointer r1
    //     0x7db0ec: add             x1, x1, HEAP, lsl #32
    // 0x7db0f0: ldur            x0, [fp, #-0x40]
    // 0x7db0f4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7db0f4: add             x25, x1, x2, lsl #2
    //     0x7db0f8: add             x25, x25, #0xf
    //     0x7db0fc: str             w0, [x25]
    //     0x7db100: tbz             w0, #0, #0x7db11c
    //     0x7db104: ldurb           w16, [x1, #-1]
    //     0x7db108: ldurb           w17, [x0, #-1]
    //     0x7db10c: and             x16, x17, x16, lsr #2
    //     0x7db110: tst             x16, HEAP, lsr #32
    //     0x7db114: b.eq            #0x7db11c
    //     0x7db118: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7db11c: b               #0x7db124
    // 0x7db120: ldur            x3, [fp, #-0x38]
    // 0x7db124: ldur            x0, [fp, #-0x18]
    // 0x7db128: LoadField: r4 = r0->field_1f
    //     0x7db128: ldur            w4, [x0, #0x1f]
    // 0x7db12c: DecompressPointer r4
    //     0x7db12c: add             x4, x4, HEAP, lsl #32
    // 0x7db130: stur            x4, [fp, #-0x30]
    // 0x7db134: LoadField: r2 = r0->field_13
    //     0x7db134: ldur            x2, [x0, #0x13]
    // 0x7db138: r1 = Null
    //     0x7db138: mov             x1, NULL
    // 0x7db13c: r0 = ZakatQuality.medium()
    //     0x7db13c: bl              #0x7db39c  ; [package:nuonline/app/data/enums/zakat_enum.dart] ZakatQuality::ZakatQuality.medium
    // 0x7db140: mov             x2, x0
    // 0x7db144: ldur            x0, [fp, #-0x30]
    // 0x7db148: stur            x2, [fp, #-0x40]
    // 0x7db14c: tbnz            w0, #4, #0x7db1cc
    // 0x7db150: ldur            x0, [fp, #-0x38]
    // 0x7db154: LoadField: r1 = r0->field_b
    //     0x7db154: ldur            w1, [x0, #0xb]
    // 0x7db158: LoadField: r3 = r0->field_f
    //     0x7db158: ldur            w3, [x0, #0xf]
    // 0x7db15c: DecompressPointer r3
    //     0x7db15c: add             x3, x3, HEAP, lsl #32
    // 0x7db160: LoadField: r4 = r3->field_b
    //     0x7db160: ldur            w4, [x3, #0xb]
    // 0x7db164: r3 = LoadInt32Instr(r1)
    //     0x7db164: sbfx            x3, x1, #1, #0x1f
    // 0x7db168: stur            x3, [fp, #-0x28]
    // 0x7db16c: r1 = LoadInt32Instr(r4)
    //     0x7db16c: sbfx            x1, x4, #1, #0x1f
    // 0x7db170: cmp             x3, x1
    // 0x7db174: b.ne            #0x7db180
    // 0x7db178: mov             x1, x0
    // 0x7db17c: r0 = _growToNextCapacity()
    //     0x7db17c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7db180: ldur            x2, [fp, #-0x38]
    // 0x7db184: ldur            x3, [fp, #-0x28]
    // 0x7db188: add             x0, x3, #1
    // 0x7db18c: lsl             x1, x0, #1
    // 0x7db190: StoreField: r2->field_b = r1
    //     0x7db190: stur            w1, [x2, #0xb]
    // 0x7db194: LoadField: r1 = r2->field_f
    //     0x7db194: ldur            w1, [x2, #0xf]
    // 0x7db198: DecompressPointer r1
    //     0x7db198: add             x1, x1, HEAP, lsl #32
    // 0x7db19c: ldur            x0, [fp, #-0x40]
    // 0x7db1a0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7db1a0: add             x25, x1, x3, lsl #2
    //     0x7db1a4: add             x25, x25, #0xf
    //     0x7db1a8: str             w0, [x25]
    //     0x7db1ac: tbz             w0, #0, #0x7db1c8
    //     0x7db1b0: ldurb           w16, [x1, #-1]
    //     0x7db1b4: ldurb           w17, [x0, #-1]
    //     0x7db1b8: and             x16, x17, x16, lsr #2
    //     0x7db1bc: tst             x16, HEAP, lsr #32
    //     0x7db1c0: b.eq            #0x7db1c8
    //     0x7db1c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7db1c8: b               #0x7db1d0
    // 0x7db1cc: ldur            x2, [fp, #-0x38]
    // 0x7db1d0: ldur            x0, [fp, #-0x10]
    // 0x7db1d4: ldur            x1, [fp, #-0x20]
    // 0x7db1d8: r0 = value=()
    //     0x7db1d8: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x7db1dc: ldur            x0, [fp, #-0x10]
    // 0x7db1e0: LoadField: r2 = r0->field_53
    //     0x7db1e0: ldur            w2, [x0, #0x53]
    // 0x7db1e4: DecompressPointer r2
    //     0x7db1e4: add             x2, x2, HEAP, lsl #32
    // 0x7db1e8: ldur            x1, [fp, #-0x20]
    // 0x7db1ec: stur            x2, [fp, #-0x30]
    // 0x7db1f0: r0 = value()
    //     0x7db1f0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x7db1f4: r1 = LoadClassIdInstr(r0)
    //     0x7db1f4: ldur            x1, [x0, #-1]
    //     0x7db1f8: ubfx            x1, x1, #0xc, #0x14
    // 0x7db1fc: mov             x16, x0
    // 0x7db200: mov             x0, x1
    // 0x7db204: mov             x1, x16
    // 0x7db208: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x7db208: movz            x17, #0xd20f
    //     0x7db20c: add             lr, x0, x17
    //     0x7db210: ldr             lr, [x21, lr, lsl #3]
    //     0x7db214: blr             lr
    // 0x7db218: ldur            x1, [fp, #-0x30]
    // 0x7db21c: mov             x2, x0
    // 0x7db220: r0 = value=()
    //     0x7db220: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7db224: r0 = RxStatus()
    //     0x7db224: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7db228: mov             x1, x0
    // 0x7db22c: r0 = false
    //     0x7db22c: add             x0, NULL, #0x30  ; false
    // 0x7db230: StoreField: r1->field_f = r0
    //     0x7db230: stur            w0, [x1, #0xf]
    // 0x7db234: StoreField: r1->field_7 = r0
    //     0x7db234: stur            w0, [x1, #7]
    // 0x7db238: StoreField: r1->field_b = r0
    //     0x7db238: stur            w0, [x1, #0xb]
    // 0x7db23c: mov             x3, x1
    // 0x7db240: ldur            x1, [fp, #-0x10]
    // 0x7db244: ldur            x2, [fp, #-0x18]
    // 0x7db248: r0 = change()
    //     0x7db248: bl              #0x7db30c  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin&StateMixin::change
    // 0x7db24c: r0 = Null
    //     0x7db24c: mov             x0, NULL
    // 0x7db250: r0 = ReturnAsyncNotFuture()
    //     0x7db250: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7db254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7db254: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7db258: b               #0x7db038
  }
  _ onFetchFailure(/* No info */) async {
    // ** addr: 0x7e5ff8, size: 0x110
    // 0x7e5ff8: EnterFrame
    //     0x7e5ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x7e5ffc: mov             fp, SP
    // 0x7e6000: AllocStack(0x50)
    //     0x7e6000: sub             SP, SP, #0x50
    // 0x7e6004: SetupParameters(ZakatFormController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7e6004: stur            NULL, [fp, #-8]
    //     0x7e6008: stur            x1, [fp, #-0x10]
    //     0x7e600c: stur            x2, [fp, #-0x18]
    //     0x7e6010: stur            x3, [fp, #-0x20]
    // 0x7e6014: CheckStackOverflow
    //     0x7e6014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e6018: cmp             SP, x16
    //     0x7e601c: b.ls            #0x7e6100
    // 0x7e6020: r1 = 1
    //     0x7e6020: movz            x1, #0x1
    // 0x7e6024: r0 = AllocateContext()
    //     0x7e6024: bl              #0xec126c  ; AllocateContextStub
    // 0x7e6028: mov             x1, x0
    // 0x7e602c: ldur            x0, [fp, #-0x20]
    // 0x7e6030: stur            x1, [fp, #-0x28]
    // 0x7e6034: StoreField: r1->field_f = r0
    //     0x7e6034: stur            w0, [x1, #0xf]
    // 0x7e6038: InitAsync() -> Future<void?>
    //     0x7e6038: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7e603c: bl              #0x661298  ; InitAsyncStub
    // 0x7e6040: ldur            x1, [fp, #-0x10]
    // 0x7e6044: r0 = notifyChildrens()
    //     0x7e6044: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0x7e6048: ldur            x1, [fp, #-0x10]
    // 0x7e604c: LoadField: r2 = r1->field_3f
    //     0x7e604c: ldur            w2, [x1, #0x3f]
    // 0x7e6050: DecompressPointer r2
    //     0x7e6050: add             x2, x2, HEAP, lsl #32
    // 0x7e6054: stur            x2, [fp, #-0x20]
    // 0x7e6058: r0 = RxStatus()
    //     0x7e6058: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x7e605c: mov             x1, x0
    // 0x7e6060: r0 = false
    //     0x7e6060: add             x0, NULL, #0x30  ; false
    // 0x7e6064: StoreField: r1->field_f = r0
    //     0x7e6064: stur            w0, [x1, #0xf]
    // 0x7e6068: StoreField: r1->field_7 = r0
    //     0x7e6068: stur            w0, [x1, #7]
    // 0x7e606c: StoreField: r1->field_b = r0
    //     0x7e606c: stur            w0, [x1, #0xb]
    // 0x7e6070: mov             x3, x1
    // 0x7e6074: ldur            x1, [fp, #-0x10]
    // 0x7e6078: ldur            x2, [fp, #-0x20]
    // 0x7e607c: r0 = change()
    //     0x7e607c: bl              #0x7db30c  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin&StateMixin::change
    // 0x7e6080: r1 = Function '<anonymous closure>':.
    //     0x7e6080: add             x1, PP, #0x35, lsl #12  ; [pp+0x357a8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x7e6084: ldr             x1, [x1, #0x7a8]
    // 0x7e6088: r2 = Null
    //     0x7e6088: mov             x2, NULL
    // 0x7e608c: r0 = AllocateClosure()
    //     0x7e608c: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e6090: r1 = Function '<anonymous closure>':.
    //     0x7e6090: add             x1, PP, #0x35, lsl #12  ; [pp+0x357b0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x7e6094: ldr             x1, [x1, #0x7b0]
    // 0x7e6098: r2 = Null
    //     0x7e6098: mov             x2, NULL
    // 0x7e609c: stur            x0, [fp, #-0x10]
    // 0x7e60a0: r0 = AllocateClosure()
    //     0x7e60a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e60a4: ldur            x2, [fp, #-0x28]
    // 0x7e60a8: r1 = Function '<anonymous closure>':.
    //     0x7e60a8: add             x1, PP, #0x35, lsl #12  ; [pp+0x357b8] AnonymousClosure: (0x7e6108), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onFetchFailure (0x7e5ff8)
    //     0x7e60ac: ldr             x1, [x1, #0x7b8]
    // 0x7e60b0: stur            x0, [fp, #-0x20]
    // 0x7e60b4: r0 = AllocateClosure()
    //     0x7e60b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e60b8: mov             x1, x0
    // 0x7e60bc: ldur            x0, [fp, #-0x18]
    // 0x7e60c0: r2 = LoadClassIdInstr(r0)
    //     0x7e60c0: ldur            x2, [x0, #-1]
    //     0x7e60c4: ubfx            x2, x2, #0xc, #0x14
    // 0x7e60c8: r16 = <Null?>
    //     0x7e60c8: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x7e60cc: stp             x0, x16, [SP, #0x18]
    // 0x7e60d0: ldur            x16, [fp, #-0x20]
    // 0x7e60d4: stp             x16, x1, [SP, #8]
    // 0x7e60d8: ldur            x16, [fp, #-0x10]
    // 0x7e60dc: str             x16, [SP]
    // 0x7e60e0: mov             x0, x2
    // 0x7e60e4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x7e60e4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x7e60e8: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x7e60e8: movz            x17, #0x47d8
    //     0x7e60ec: add             lr, x0, x17
    //     0x7e60f0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e60f4: blr             lr
    // 0x7e60f8: r0 = Null
    //     0x7e60f8: mov             x0, NULL
    // 0x7e60fc: r0 = ReturnAsyncNotFuture()
    //     0x7e60fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e6100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e6100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e6104: b               #0x7e6020
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x7e6108, size: 0x50
    // 0x7e6108: EnterFrame
    //     0x7e6108: stp             fp, lr, [SP, #-0x10]!
    //     0x7e610c: mov             fp, SP
    // 0x7e6110: ldr             x0, [fp, #0x10]
    // 0x7e6114: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7e6114: ldur            w1, [x0, #0x17]
    // 0x7e6118: DecompressPointer r1
    //     0x7e6118: add             x1, x1, HEAP, lsl #32
    // 0x7e611c: CheckStackOverflow
    //     0x7e611c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e6120: cmp             SP, x16
    //     0x7e6124: b.ls            #0x7e6150
    // 0x7e6128: LoadField: r0 = r1->field_f
    //     0x7e6128: ldur            w0, [x1, #0xf]
    // 0x7e612c: DecompressPointer r0
    //     0x7e612c: add             x0, x0, HEAP, lsl #32
    // 0x7e6130: mov             x1, x0
    // 0x7e6134: r2 = Instance_IconData
    //     0x7e6134: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0x7e6138: ldr             x2, [x2, #0x590]
    // 0x7e613c: r0 = show()
    //     0x7e613c: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0x7e6140: r0 = Null
    //     0x7e6140: mov             x0, NULL
    // 0x7e6144: LeaveFrame
    //     0x7e6144: mov             SP, fp
    //     0x7e6148: ldp             fp, lr, [SP], #0x10
    // 0x7e614c: ret
    //     0x7e614c: ret             
    // 0x7e6150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e6150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e6154: b               #0x7e6128
  }
  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7efbfc, size: 0x38
    // 0x7efbfc: EnterFrame
    //     0x7efbfc: stp             fp, lr, [SP, #-0x10]!
    //     0x7efc00: mov             fp, SP
    // 0x7efc04: CheckStackOverflow
    //     0x7efc04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7efc08: cmp             SP, x16
    //     0x7efc0c: b.ls            #0x7efc2c
    // 0x7efc10: LoadField: r0 = r1->field_47
    //     0x7efc10: ldur            w0, [x1, #0x47]
    // 0x7efc14: DecompressPointer r0
    //     0x7efc14: add             x0, x0, HEAP, lsl #32
    // 0x7efc18: mov             x1, x0
    // 0x7efc1c: r0 = findSetting()
    //     0x7efc1c: bl              #0x7e9244  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::findSetting
    // 0x7efc20: LeaveFrame
    //     0x7efc20: mov             SP, fp
    //     0x7efc24: ldp             fp, lr, [SP], #0x10
    // 0x7efc28: ret
    //     0x7efc28: ret             
    // 0x7efc2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7efc2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7efc30: b               #0x7efc10
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f5254, size: 0x160
    // 0x8f5254: EnterFrame
    //     0x8f5254: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5258: mov             fp, SP
    // 0x8f525c: AllocStack(0x28)
    //     0x8f525c: sub             SP, SP, #0x28
    // 0x8f5260: SetupParameters(ZakatFormController this /* r1 => r0, fp-0x8 */)
    //     0x8f5260: mov             x0, x1
    //     0x8f5264: stur            x1, [fp, #-8]
    // 0x8f5268: CheckStackOverflow
    //     0x8f5268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f526c: cmp             SP, x16
    //     0x8f5270: b.ls            #0x8f53ac
    // 0x8f5274: mov             x1, x0
    // 0x8f5278: r0 = onInit()
    //     0x8f5278: bl              #0x8f7e70  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onInit
    // 0x8f527c: ldur            x0, [fp, #-8]
    // 0x8f5280: LoadField: r2 = r0->field_5f
    //     0x8f5280: ldur            w2, [x0, #0x5f]
    // 0x8f5284: DecompressPointer r2
    //     0x8f5284: add             x2, x2, HEAP, lsl #32
    // 0x8f5288: mov             x1, x0
    // 0x8f528c: stur            x2, [fp, #-0x10]
    // 0x8f5290: r0 = preAmount()
    //     0x8f5290: bl              #0x8f7d90  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::preAmount
    // 0x8f5294: mov             x2, x0
    // 0x8f5298: r0 = BoxInt64Instr(r2)
    //     0x8f5298: sbfiz           x0, x2, #1, #0x1f
    //     0x8f529c: cmp             x2, x0, asr #1
    //     0x8f52a0: b.eq            #0x8f52ac
    //     0x8f52a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f52a8: stur            x2, [x0, #7]
    // 0x8f52ac: ldur            x1, [fp, #-0x10]
    // 0x8f52b0: mov             x2, x0
    // 0x8f52b4: r0 = format()
    //     0x8f52b4: bl              #0x8f5664  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x8f52b8: ldur            x1, [fp, #-8]
    // 0x8f52bc: stur            x0, [fp, #-0x20]
    // 0x8f52c0: LoadField: r2 = r1->field_33
    //     0x8f52c0: ldur            w2, [x1, #0x33]
    // 0x8f52c4: DecompressPointer r2
    //     0x8f52c4: add             x2, x2, HEAP, lsl #32
    // 0x8f52c8: stur            x2, [fp, #-0x18]
    // 0x8f52cc: LoadField: r3 = r0->field_7
    //     0x8f52cc: ldur            w3, [x0, #7]
    // 0x8f52d0: stur            x3, [fp, #-0x10]
    // 0x8f52d4: r0 = TextSelection()
    //     0x8f52d4: bl              #0x6865a8  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x8f52d8: mov             x1, x0
    // 0x8f52dc: r0 = Instance_TextAffinity
    //     0x8f52dc: ldr             x0, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0x8f52e0: stur            x1, [fp, #-0x28]
    // 0x8f52e4: StoreField: r1->field_27 = r0
    //     0x8f52e4: stur            w0, [x1, #0x27]
    // 0x8f52e8: ldur            x0, [fp, #-0x10]
    // 0x8f52ec: r2 = LoadInt32Instr(r0)
    //     0x8f52ec: sbfx            x2, x0, #1, #0x1f
    // 0x8f52f0: ArrayStore: r1[0] = r2  ; List_8
    //     0x8f52f0: stur            x2, [x1, #0x17]
    // 0x8f52f4: StoreField: r1->field_1f = r2
    //     0x8f52f4: stur            x2, [x1, #0x1f]
    // 0x8f52f8: r0 = false
    //     0x8f52f8: add             x0, NULL, #0x30  ; false
    // 0x8f52fc: StoreField: r1->field_2b = r0
    //     0x8f52fc: stur            w0, [x1, #0x2b]
    // 0x8f5300: StoreField: r1->field_7 = r2
    //     0x8f5300: stur            x2, [x1, #7]
    // 0x8f5304: StoreField: r1->field_f = r2
    //     0x8f5304: stur            x2, [x1, #0xf]
    // 0x8f5308: r0 = TextEditingValue()
    //     0x8f5308: bl              #0x6aef08  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x8f530c: mov             x1, x0
    // 0x8f5310: ldur            x0, [fp, #-0x20]
    // 0x8f5314: StoreField: r1->field_7 = r0
    //     0x8f5314: stur            w0, [x1, #7]
    // 0x8f5318: ldur            x0, [fp, #-0x28]
    // 0x8f531c: StoreField: r1->field_b = r0
    //     0x8f531c: stur            w0, [x1, #0xb]
    // 0x8f5320: r0 = Instance_TextRange
    //     0x8f5320: ldr             x0, [PP, #0x7268]  ; [pp+0x7268] Obj!TextRange@e26391
    // 0x8f5324: StoreField: r1->field_f = r0
    //     0x8f5324: stur            w0, [x1, #0xf]
    // 0x8f5328: mov             x2, x1
    // 0x8f532c: ldur            x1, [fp, #-0x18]
    // 0x8f5330: r0 = value=()
    //     0x8f5330: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x8f5334: ldur            x0, [fp, #-8]
    // 0x8f5338: LoadField: r2 = r0->field_23
    //     0x8f5338: ldur            w2, [x0, #0x23]
    // 0x8f533c: DecompressPointer r2
    //     0x8f533c: add             x2, x2, HEAP, lsl #32
    // 0x8f5340: mov             x1, x0
    // 0x8f5344: stur            x2, [fp, #-0x10]
    // 0x8f5348: r0 = preAmount()
    //     0x8f5348: bl              #0x8f7d90  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::preAmount
    // 0x8f534c: mov             x2, x0
    // 0x8f5350: r0 = BoxInt64Instr(r2)
    //     0x8f5350: sbfiz           x0, x2, #1, #0x1f
    //     0x8f5354: cmp             x2, x0, asr #1
    //     0x8f5358: b.eq            #0x8f5364
    //     0x8f535c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f5360: stur            x2, [x0, #7]
    // 0x8f5364: ldur            x1, [fp, #-0x10]
    // 0x8f5368: mov             x2, x0
    // 0x8f536c: r0 = value=()
    //     0x8f536c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f5370: ldur            x0, [fp, #-8]
    // 0x8f5374: LoadField: r2 = r0->field_2f
    //     0x8f5374: ldur            w2, [x0, #0x2f]
    // 0x8f5378: DecompressPointer r2
    //     0x8f5378: add             x2, x2, HEAP, lsl #32
    // 0x8f537c: mov             x1, x0
    // 0x8f5380: stur            x2, [fp, #-0x10]
    // 0x8f5384: r0 = preType()
    //     0x8f5384: bl              #0x8f5570  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::preType
    // 0x8f5388: ldur            x1, [fp, #-0x10]
    // 0x8f538c: mov             x2, x0
    // 0x8f5390: r0 = value=()
    //     0x8f5390: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f5394: ldur            x1, [fp, #-8]
    // 0x8f5398: r0 = fetch()
    //     0x8f5398: bl              #0x8f53b4  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] _ZakatFormController&DonationFormController&FetchMixin::fetch
    // 0x8f539c: r0 = Null
    //     0x8f539c: mov             x0, NULL
    // 0x8f53a0: LeaveFrame
    //     0x8f53a0: mov             SP, fp
    //     0x8f53a4: ldp             fp, lr, [SP], #0x10
    // 0x8f53a8: ret
    //     0x8f53a8: ret             
    // 0x8f53ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f53ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f53b0: b               #0x8f5274
  }
  get _ preType(/* No info */) {
    // ** addr: 0x8f5570, size: 0xf4
    // 0x8f5570: EnterFrame
    //     0x8f5570: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5574: mov             fp, SP
    // 0x8f5578: AllocStack(0x18)
    //     0x8f5578: sub             SP, SP, #0x18
    // 0x8f557c: CheckStackOverflow
    //     0x8f557c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5580: cmp             SP, x16
    //     0x8f5584: b.ls            #0x8f565c
    // 0x8f5588: LoadField: r0 = r1->field_5b
    //     0x8f5588: ldur            w0, [x1, #0x5b]
    // 0x8f558c: DecompressPointer r0
    //     0x8f558c: add             x0, x0, HEAP, lsl #32
    // 0x8f5590: cmp             w0, NULL
    // 0x8f5594: b.ne            #0x8f5634
    // 0x8f5598: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f5598: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f559c: ldr             x0, [x0, #0x2670]
    //     0x8f55a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f55a4: cmp             w0, w16
    //     0x8f55a8: b.ne            #0x8f55b4
    //     0x8f55ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f55b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f55b4: r0 = GetNavigation.arguments()
    //     0x8f55b4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8f55b8: cmp             w0, NULL
    // 0x8f55bc: b.ne            #0x8f55c8
    // 0x8f55c0: r3 = Null
    //     0x8f55c0: mov             x3, NULL
    // 0x8f55c4: b               #0x8f55ec
    // 0x8f55c8: r16 = "type"
    //     0x8f55c8: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x8f55cc: stp             x16, x0, [SP]
    // 0x8f55d0: r4 = 0
    //     0x8f55d0: movz            x4, #0
    // 0x8f55d4: ldr             x0, [SP, #8]
    // 0x8f55d8: r16 = UnlinkedCall_0x5f3c08
    //     0x8f55d8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35300] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f55dc: add             x16, x16, #0x300
    // 0x8f55e0: ldp             x5, lr, [x16]
    // 0x8f55e4: blr             lr
    // 0x8f55e8: mov             x3, x0
    // 0x8f55ec: mov             x0, x3
    // 0x8f55f0: stur            x3, [fp, #-8]
    // 0x8f55f4: r2 = Null
    //     0x8f55f4: mov             x2, NULL
    // 0x8f55f8: r1 = Null
    //     0x8f55f8: mov             x1, NULL
    // 0x8f55fc: r4 = 60
    //     0x8f55fc: movz            x4, #0x3c
    // 0x8f5600: branchIfSmi(r0, 0x8f560c)
    //     0x8f5600: tbz             w0, #0, #0x8f560c
    // 0x8f5604: r4 = LoadClassIdInstr(r0)
    //     0x8f5604: ldur            x4, [x0, #-1]
    //     0x8f5608: ubfx            x4, x4, #0xc, #0x14
    // 0x8f560c: r17 = 6839
    //     0x8f560c: movz            x17, #0x1ab7
    // 0x8f5610: cmp             x4, x17
    // 0x8f5614: b.eq            #0x8f562c
    // 0x8f5618: r8 = ZakatTypes?
    //     0x8f5618: add             x8, PP, #0x35, lsl #12  ; [pp+0x35310] Type: ZakatTypes?
    //     0x8f561c: ldr             x8, [x8, #0x310]
    // 0x8f5620: r3 = Null
    //     0x8f5620: add             x3, PP, #0x35, lsl #12  ; [pp+0x35318] Null
    //     0x8f5624: ldr             x3, [x3, #0x318]
    // 0x8f5628: r0 = DefaultNullableTypeTest()
    //     0x8f5628: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x8f562c: ldur            x1, [fp, #-8]
    // 0x8f5630: b               #0x8f5638
    // 0x8f5634: mov             x1, x0
    // 0x8f5638: cmp             w1, NULL
    // 0x8f563c: b.ne            #0x8f564c
    // 0x8f5640: r0 = Instance_ZakatTypes
    //     0x8f5640: add             x0, PP, #0x27, lsl #12  ; [pp+0x27130] Obj!ZakatTypes@e30781
    //     0x8f5644: ldr             x0, [x0, #0x130]
    // 0x8f5648: b               #0x8f5650
    // 0x8f564c: mov             x0, x1
    // 0x8f5650: LeaveFrame
    //     0x8f5650: mov             SP, fp
    //     0x8f5654: ldp             fp, lr, [SP], #0x10
    // 0x8f5658: ret
    //     0x8f5658: ret             
    // 0x8f565c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f565c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5660: b               #0x8f5588
  }
  get _ preAmount(/* No info */) {
    // ** addr: 0x8f7d90, size: 0xe0
    // 0x8f7d90: EnterFrame
    //     0x8f7d90: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7d94: mov             fp, SP
    // 0x8f7d98: AllocStack(0x18)
    //     0x8f7d98: sub             SP, SP, #0x18
    // 0x8f7d9c: CheckStackOverflow
    //     0x8f7d9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7da0: cmp             SP, x16
    //     0x8f7da4: b.ls            #0x8f7e68
    // 0x8f7da8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8f7da8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f7dac: ldr             x0, [x0, #0x2670]
    //     0x8f7db0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f7db4: cmp             w0, w16
    //     0x8f7db8: b.ne            #0x8f7dc4
    //     0x8f7dbc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8f7dc0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f7dc4: r0 = GetNavigation.arguments()
    //     0x8f7dc4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8f7dc8: cmp             w0, NULL
    // 0x8f7dcc: b.ne            #0x8f7dd8
    // 0x8f7dd0: r3 = Null
    //     0x8f7dd0: mov             x3, NULL
    // 0x8f7dd4: b               #0x8f7e00
    // 0x8f7dd8: r16 = "amount"
    //     0x8f7dd8: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x8f7ddc: ldr             x16, [x16, #0xe0]
    // 0x8f7de0: stp             x16, x0, [SP]
    // 0x8f7de4: r4 = 0
    //     0x8f7de4: movz            x4, #0
    // 0x8f7de8: ldr             x0, [SP, #8]
    // 0x8f7dec: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7dec: add             x16, PP, #0x35, lsl #12  ; [pp+0x35218] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7df0: add             x16, x16, #0x218
    // 0x8f7df4: ldp             x5, lr, [x16]
    // 0x8f7df8: blr             lr
    // 0x8f7dfc: mov             x3, x0
    // 0x8f7e00: mov             x0, x3
    // 0x8f7e04: stur            x3, [fp, #-8]
    // 0x8f7e08: r2 = Null
    //     0x8f7e08: mov             x2, NULL
    // 0x8f7e0c: r1 = Null
    //     0x8f7e0c: mov             x1, NULL
    // 0x8f7e10: branchIfSmi(r0, 0x8f7e38)
    //     0x8f7e10: tbz             w0, #0, #0x8f7e38
    // 0x8f7e14: r4 = LoadClassIdInstr(r0)
    //     0x8f7e14: ldur            x4, [x0, #-1]
    //     0x8f7e18: ubfx            x4, x4, #0xc, #0x14
    // 0x8f7e1c: sub             x4, x4, #0x3c
    // 0x8f7e20: cmp             x4, #1
    // 0x8f7e24: b.ls            #0x8f7e38
    // 0x8f7e28: r8 = int?
    //     0x8f7e28: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x8f7e2c: r3 = Null
    //     0x8f7e2c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35228] Null
    //     0x8f7e30: ldr             x3, [x3, #0x228]
    // 0x8f7e34: r0 = int?()
    //     0x8f7e34: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x8f7e38: ldur            x1, [fp, #-8]
    // 0x8f7e3c: cmp             w1, NULL
    // 0x8f7e40: b.ne            #0x8f7e4c
    // 0x8f7e44: r0 = 0
    //     0x8f7e44: movz            x0, #0
    // 0x8f7e48: b               #0x8f7e5c
    // 0x8f7e4c: r2 = LoadInt32Instr(r1)
    //     0x8f7e4c: sbfx            x2, x1, #1, #0x1f
    //     0x8f7e50: tbz             w1, #0, #0x8f7e58
    //     0x8f7e54: ldur            x2, [x1, #7]
    // 0x8f7e58: mov             x0, x2
    // 0x8f7e5c: LeaveFrame
    //     0x8f7e5c: mov             SP, fp
    //     0x8f7e60: ldp             fp, lr, [SP], #0x10
    // 0x8f7e64: ret
    //     0x8f7e64: ret             
    // 0x8f7e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7e68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7e6c: b               #0x8f7da8
  }
  _ ZakatFormController(/* No info */) {
    // ** addr: 0x8f8d70, size: 0x1b0
    // 0x8f8d70: EnterFrame
    //     0x8f8d70: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8d74: mov             fp, SP
    // 0x8f8d78: AllocStack(0x38)
    //     0x8f8d78: sub             SP, SP, #0x38
    // 0x8f8d7c: SetupParameters(ZakatFormController this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x10 */, dynamic _ /* r3 => r4, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */)
    //     0x8f8d7c: mov             x0, x6
    //     0x8f8d80: stur            x6, [fp, #-0x28]
    //     0x8f8d84: mov             x6, x1
    //     0x8f8d88: mov             x4, x3
    //     0x8f8d8c: stur            x3, [fp, #-0x18]
    //     0x8f8d90: mov             x3, x5
    //     0x8f8d94: stur            x5, [fp, #-0x20]
    //     0x8f8d98: mov             x5, x2
    //     0x8f8d9c: stur            x1, [fp, #-8]
    //     0x8f8da0: stur            x2, [fp, #-0x10]
    // 0x8f8da4: CheckStackOverflow
    //     0x8f8da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f8da8: cmp             SP, x16
    //     0x8f8dac: b.ls            #0x8f8f18
    // 0x8f8db0: r1 = <ZakatQuality>
    //     0x8f8db0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30040] TypeArguments: <ZakatQuality>
    //     0x8f8db4: ldr             x1, [x1, #0x40]
    // 0x8f8db8: r2 = 0
    //     0x8f8db8: movz            x2, #0
    // 0x8f8dbc: r0 = _GrowableList()
    //     0x8f8dbc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8f8dc0: r16 = <ZakatQuality>
    //     0x8f8dc0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] TypeArguments: <ZakatQuality>
    //     0x8f8dc4: ldr             x16, [x16, #0x40]
    // 0x8f8dc8: stp             x0, x16, [SP]
    // 0x8f8dcc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f8dcc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f8dd0: r0 = ListExtension.obs()
    //     0x8f8dd0: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x8f8dd4: ldur            x2, [fp, #-8]
    // 0x8f8dd8: StoreField: r2->field_4f = r0
    //     0x8f8dd8: stur            w0, [x2, #0x4f]
    //     0x8f8ddc: ldurb           w16, [x2, #-1]
    //     0x8f8de0: ldurb           w17, [x0, #-1]
    //     0x8f8de4: and             x16, x17, x16, lsr #2
    //     0x8f8de8: tst             x16, HEAP, lsr #32
    //     0x8f8dec: b.eq            #0x8f8df4
    //     0x8f8df0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f8df4: r1 = Null
    //     0x8f8df4: mov             x1, NULL
    // 0x8f8df8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f8df8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f8dfc: r0 = ZakatQuality.premium()
    //     0x8f8dfc: bl              #0x7db25c  ; [package:nuonline/app/data/enums/zakat_enum.dart] ZakatQuality::ZakatQuality.premium
    // 0x8f8e00: r16 = <ZakatQuality>
    //     0x8f8e00: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] TypeArguments: <ZakatQuality>
    //     0x8f8e04: ldr             x16, [x16, #0x40]
    // 0x8f8e08: stp             x0, x16, [SP]
    // 0x8f8e0c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f8e0c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f8e10: r0 = RxT.obs()
    //     0x8f8e10: bl              #0x80eeb8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x8f8e14: ldur            x3, [fp, #-8]
    // 0x8f8e18: StoreField: r3->field_53 = r0
    //     0x8f8e18: stur            w0, [x3, #0x53]
    //     0x8f8e1c: ldurb           w16, [x3, #-1]
    //     0x8f8e20: ldurb           w17, [x0, #-1]
    //     0x8f8e24: and             x16, x17, x16, lsr #2
    //     0x8f8e28: tst             x16, HEAP, lsr #32
    //     0x8f8e2c: b.eq            #0x8f8e34
    //     0x8f8e30: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f8e34: r1 = <String>
    //     0x8f8e34: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f8e38: r2 = 0
    //     0x8f8e38: movz            x2, #0
    // 0x8f8e3c: r0 = _GrowableList()
    //     0x8f8e3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8f8e40: r16 = <String>
    //     0x8f8e40: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f8e44: stp             x0, x16, [SP]
    // 0x8f8e48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8f8e48: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8f8e4c: r0 = ListExtension.obs()
    //     0x8f8e4c: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x8f8e50: ldur            x3, [fp, #-8]
    // 0x8f8e54: StoreField: r3->field_57 = r0
    //     0x8f8e54: stur            w0, [x3, #0x57]
    //     0x8f8e58: ldurb           w16, [x3, #-1]
    //     0x8f8e5c: ldurb           w17, [x0, #-1]
    //     0x8f8e60: and             x16, x17, x16, lsr #2
    //     0x8f8e64: tst             x16, HEAP, lsr #32
    //     0x8f8e68: b.eq            #0x8f8e70
    //     0x8f8e6c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f8e70: r1 = Null
    //     0x8f8e70: mov             x1, NULL
    // 0x8f8e74: r2 = "#,##0"
    //     0x8f8e74: add             x2, PP, #0x30, lsl #12  ; [pp+0x30048] "#,##0"
    //     0x8f8e78: ldr             x2, [x2, #0x48]
    // 0x8f8e7c: r0 = NumberFormat()
    //     0x8f8e7c: bl              #0x84698c  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat
    // 0x8f8e80: ldur            x1, [fp, #-8]
    // 0x8f8e84: StoreField: r1->field_5f = r0
    //     0x8f8e84: stur            w0, [x1, #0x5f]
    //     0x8f8e88: ldurb           w16, [x1, #-1]
    //     0x8f8e8c: ldurb           w17, [x0, #-1]
    //     0x8f8e90: and             x16, x17, x16, lsr #2
    //     0x8f8e94: tst             x16, HEAP, lsr #32
    //     0x8f8e98: b.eq            #0x8f8ea0
    //     0x8f8e9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8ea0: ldur            x0, [fp, #-0x20]
    // 0x8f8ea4: StoreField: r1->field_47 = r0
    //     0x8f8ea4: stur            w0, [x1, #0x47]
    //     0x8f8ea8: ldurb           w16, [x1, #-1]
    //     0x8f8eac: ldurb           w17, [x0, #-1]
    //     0x8f8eb0: and             x16, x17, x16, lsr #2
    //     0x8f8eb4: tst             x16, HEAP, lsr #32
    //     0x8f8eb8: b.eq            #0x8f8ec0
    //     0x8f8ebc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8ec0: ldur            x0, [fp, #-0x10]
    // 0x8f8ec4: StoreField: r1->field_4b = r0
    //     0x8f8ec4: stur            w0, [x1, #0x4b]
    //     0x8f8ec8: ldurb           w16, [x1, #-1]
    //     0x8f8ecc: ldurb           w17, [x0, #-1]
    //     0x8f8ed0: and             x16, x17, x16, lsr #2
    //     0x8f8ed4: tst             x16, HEAP, lsr #32
    //     0x8f8ed8: b.eq            #0x8f8ee0
    //     0x8f8edc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8ee0: ldur            x0, [fp, #-0x28]
    // 0x8f8ee4: StoreField: r1->field_5b = r0
    //     0x8f8ee4: stur            w0, [x1, #0x5b]
    //     0x8f8ee8: ldurb           w16, [x1, #-1]
    //     0x8f8eec: ldurb           w17, [x0, #-1]
    //     0x8f8ef0: and             x16, x17, x16, lsr #2
    //     0x8f8ef4: tst             x16, HEAP, lsr #32
    //     0x8f8ef8: b.eq            #0x8f8f00
    //     0x8f8efc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f8f00: ldur            x2, [fp, #-0x18]
    // 0x8f8f04: r0 = DonationFormController()
    //     0x8f8f04: bl              #0x8f8f2c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::DonationFormController
    // 0x8f8f08: r0 = Null
    //     0x8f8f08: mov             x0, NULL
    // 0x8f8f0c: LeaveFrame
    //     0x8f8f0c: mov             SP, fp
    //     0x8f8f10: ldp             fp, lr, [SP], #0x10
    // 0x8f8f14: ret
    //     0x8f8f14: ret             
    // 0x8f8f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8f1c: b               #0x8f8db0
  }
  get _ subtotal(/* No info */) {
    // ** addr: 0xaef798, size: 0x5c
    // 0xaef798: EnterFrame
    //     0xaef798: stp             fp, lr, [SP, #-0x10]!
    //     0xaef79c: mov             fp, SP
    // 0xaef7a0: AllocStack(0x10)
    //     0xaef7a0: sub             SP, SP, #0x10
    // 0xaef7a4: SetupParameters(ZakatFormController this /* r1 => r0, fp-0x8 */)
    //     0xaef7a4: mov             x0, x1
    //     0xaef7a8: stur            x1, [fp, #-8]
    // 0xaef7ac: CheckStackOverflow
    //     0xaef7ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef7b0: cmp             SP, x16
    //     0xaef7b4: b.ls            #0xaef7ec
    // 0xaef7b8: LoadField: r1 = r0->field_53
    //     0xaef7b8: ldur            w1, [x0, #0x53]
    // 0xaef7bc: DecompressPointer r1
    //     0xaef7bc: add             x1, x1, HEAP, lsl #32
    // 0xaef7c0: r0 = value()
    //     0xaef7c0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaef7c4: LoadField: r2 = r0->field_b
    //     0xaef7c4: ldur            x2, [x0, #0xb]
    // 0xaef7c8: ldur            x1, [fp, #-8]
    // 0xaef7cc: stur            x2, [fp, #-0x10]
    // 0xaef7d0: r0 = seatCount()
    //     0xaef7d0: bl              #0xaef7f4  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::seatCount
    // 0xaef7d4: ldur            x1, [fp, #-0x10]
    // 0xaef7d8: mul             x2, x1, x0
    // 0xaef7dc: mov             x0, x2
    // 0xaef7e0: LeaveFrame
    //     0xaef7e0: mov             SP, fp
    //     0xaef7e4: ldp             fp, lr, [SP], #0x10
    // 0xaef7e8: ret
    //     0xaef7e8: ret             
    // 0xaef7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef7ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef7f0: b               #0xaef7b8
  }
  get _ seatCount(/* No info */) {
    // ** addr: 0xaef7f4, size: 0x6c
    // 0xaef7f4: EnterFrame
    //     0xaef7f4: stp             fp, lr, [SP, #-0x10]!
    //     0xaef7f8: mov             fp, SP
    // 0xaef7fc: AllocStack(0x8)
    //     0xaef7fc: sub             SP, SP, #8
    // 0xaef800: CheckStackOverflow
    //     0xaef800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef804: cmp             SP, x16
    //     0xaef808: b.ls            #0xaef858
    // 0xaef80c: LoadField: r0 = r1->field_57
    //     0xaef80c: ldur            w0, [x1, #0x57]
    // 0xaef810: DecompressPointer r0
    //     0xaef810: add             x0, x0, HEAP, lsl #32
    // 0xaef814: mov             x1, x0
    // 0xaef818: r0 = value()
    //     0xaef818: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaef81c: r1 = LoadClassIdInstr(r0)
    //     0xaef81c: ldur            x1, [x0, #-1]
    //     0xaef820: ubfx            x1, x1, #0xc, #0x14
    // 0xaef824: str             x0, [SP]
    // 0xaef828: mov             x0, x1
    // 0xaef82c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaef82c: movz            x17, #0xc834
    //     0xaef830: add             lr, x0, x17
    //     0xaef834: ldr             lr, [x21, lr, lsl #3]
    //     0xaef838: blr             lr
    // 0xaef83c: r1 = LoadInt32Instr(r0)
    //     0xaef83c: sbfx            x1, x0, #1, #0x1f
    //     0xaef840: tbz             w0, #0, #0xaef848
    //     0xaef844: ldur            x1, [x0, #7]
    // 0xaef848: add             x0, x1, #1
    // 0xaef84c: LeaveFrame
    //     0xaef84c: mov             SP, fp
    //     0xaef850: ldp             fp, lr, [SP], #0x10
    // 0xaef854: ret
    //     0xaef854: ret             
    // 0xaef858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef85c: b               #0xaef80c
  }
  get _ subtotalCalculation(/* No info */) {
    // ** addr: 0xaef860, size: 0xd0
    // 0xaef860: EnterFrame
    //     0xaef860: stp             fp, lr, [SP, #-0x10]!
    //     0xaef864: mov             fp, SP
    // 0xaef868: AllocStack(0x20)
    //     0xaef868: sub             SP, SP, #0x20
    // 0xaef86c: SetupParameters(ZakatFormController this /* r1 => r0, fp-0x8 */)
    //     0xaef86c: mov             x0, x1
    //     0xaef870: stur            x1, [fp, #-8]
    // 0xaef874: CheckStackOverflow
    //     0xaef874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef878: cmp             SP, x16
    //     0xaef87c: b.ls            #0xaef928
    // 0xaef880: LoadField: r1 = r0->field_53
    //     0xaef880: ldur            w1, [x0, #0x53]
    // 0xaef884: DecompressPointer r1
    //     0xaef884: add             x1, x1, HEAP, lsl #32
    // 0xaef888: r0 = value()
    //     0xaef888: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaef88c: LoadField: r1 = r0->field_b
    //     0xaef88c: ldur            x1, [x0, #0xb]
    // 0xaef890: r0 = IntExtension.idr()
    //     0xaef890: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xaef894: ldur            x1, [fp, #-8]
    // 0xaef898: stur            x0, [fp, #-8]
    // 0xaef89c: r0 = seatCount()
    //     0xaef89c: bl              #0xaef7f4  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::seatCount
    // 0xaef8a0: r1 = Null
    //     0xaef8a0: mov             x1, NULL
    // 0xaef8a4: r2 = 8
    //     0xaef8a4: movz            x2, #0x8
    // 0xaef8a8: stur            x0, [fp, #-0x10]
    // 0xaef8ac: r0 = AllocateArray()
    //     0xaef8ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaef8b0: mov             x2, x0
    // 0xaef8b4: ldur            x0, [fp, #-8]
    // 0xaef8b8: stur            x2, [fp, #-0x18]
    // 0xaef8bc: StoreField: r2->field_f = r0
    //     0xaef8bc: stur            w0, [x2, #0xf]
    // 0xaef8c0: r16 = "x"
    //     0xaef8c0: ldr             x16, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xaef8c4: StoreField: r2->field_13 = r16
    //     0xaef8c4: stur            w16, [x2, #0x13]
    // 0xaef8c8: ldur            x3, [fp, #-0x10]
    // 0xaef8cc: r0 = BoxInt64Instr(r3)
    //     0xaef8cc: sbfiz           x0, x3, #1, #0x1f
    //     0xaef8d0: cmp             x3, x0, asr #1
    //     0xaef8d4: b.eq            #0xaef8e0
    //     0xaef8d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaef8dc: stur            x3, [x0, #7]
    // 0xaef8e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xaef8e0: stur            w0, [x2, #0x17]
    // 0xaef8e4: r16 = "= "
    //     0xaef8e4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35198] "= "
    //     0xaef8e8: ldr             x16, [x16, #0x198]
    // 0xaef8ec: StoreField: r2->field_1b = r16
    //     0xaef8ec: stur            w16, [x2, #0x1b]
    // 0xaef8f0: r1 = <Object>
    //     0xaef8f0: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0xaef8f4: r0 = AllocateGrowableArray()
    //     0xaef8f4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaef8f8: mov             x1, x0
    // 0xaef8fc: ldur            x0, [fp, #-0x18]
    // 0xaef900: StoreField: r1->field_f = r0
    //     0xaef900: stur            w0, [x1, #0xf]
    // 0xaef904: r0 = 8
    //     0xaef904: movz            x0, #0x8
    // 0xaef908: StoreField: r1->field_b = r0
    //     0xaef908: stur            w0, [x1, #0xb]
    // 0xaef90c: r16 = " "
    //     0xaef90c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xaef910: str             x16, [SP]
    // 0xaef914: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xaef914: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xaef918: r0 = join()
    //     0xaef918: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xaef91c: LeaveFrame
    //     0xaef91c: mov             SP, fp
    //     0xaef920: ldp             fp, lr, [SP], #0x10
    // 0xaef924: ret
    //     0xaef924: ret             
    // 0xaef928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef928: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef92c: b               #0xaef880
  }
  [closure] void addPassenger(dynamic) {
    // ** addr: 0xaef930, size: 0x38
    // 0xaef930: EnterFrame
    //     0xaef930: stp             fp, lr, [SP, #-0x10]!
    //     0xaef934: mov             fp, SP
    // 0xaef938: ldr             x0, [fp, #0x10]
    // 0xaef93c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaef93c: ldur            w1, [x0, #0x17]
    // 0xaef940: DecompressPointer r1
    //     0xaef940: add             x1, x1, HEAP, lsl #32
    // 0xaef944: CheckStackOverflow
    //     0xaef944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef948: cmp             SP, x16
    //     0xaef94c: b.ls            #0xaef960
    // 0xaef950: r0 = addPassenger()
    //     0xaef950: bl              #0xaef968  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::addPassenger
    // 0xaef954: LeaveFrame
    //     0xaef954: mov             SP, fp
    //     0xaef958: ldp             fp, lr, [SP], #0x10
    // 0xaef95c: ret
    //     0xaef95c: ret             
    // 0xaef960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef964: b               #0xaef950
  }
  _ addPassenger(/* No info */) {
    // ** addr: 0xaef968, size: 0x44
    // 0xaef968: EnterFrame
    //     0xaef968: stp             fp, lr, [SP, #-0x10]!
    //     0xaef96c: mov             fp, SP
    // 0xaef970: AllocStack(0x10)
    //     0xaef970: sub             SP, SP, #0x10
    // 0xaef974: CheckStackOverflow
    //     0xaef974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef978: cmp             SP, x16
    //     0xaef97c: b.ls            #0xaef9a4
    // 0xaef980: LoadField: r0 = r1->field_57
    //     0xaef980: ldur            w0, [x1, #0x57]
    // 0xaef984: DecompressPointer r0
    //     0xaef984: add             x0, x0, HEAP, lsl #32
    // 0xaef988: r16 = ""
    //     0xaef988: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaef98c: stp             x16, x0, [SP]
    // 0xaef990: r0 = add()
    //     0xaef990: bl              #0x66b5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0xaef994: r0 = Null
    //     0xaef994: mov             x0, NULL
    // 0xaef998: LeaveFrame
    //     0xaef998: mov             SP, fp
    //     0xaef99c: ldp             fp, lr, [SP], #0x10
    // 0xaef9a0: ret
    //     0xaef9a0: ret             
    // 0xaef9a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef9a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef9a8: b               #0xaef980
  }
  _ removePassenger(/* No info */) {
    // ** addr: 0xaefe00, size: 0x3c
    // 0xaefe00: EnterFrame
    //     0xaefe00: stp             fp, lr, [SP, #-0x10]!
    //     0xaefe04: mov             fp, SP
    // 0xaefe08: CheckStackOverflow
    //     0xaefe08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefe0c: cmp             SP, x16
    //     0xaefe10: b.ls            #0xaefe34
    // 0xaefe14: LoadField: r0 = r1->field_57
    //     0xaefe14: ldur            w0, [x1, #0x57]
    // 0xaefe18: DecompressPointer r0
    //     0xaefe18: add             x0, x0, HEAP, lsl #32
    // 0xaefe1c: mov             x1, x0
    // 0xaefe20: r0 = removeAt()
    //     0xaefe20: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xaefe24: r0 = Null
    //     0xaefe24: mov             x0, NULL
    // 0xaefe28: LeaveFrame
    //     0xaefe28: mov             SP, fp
    //     0xaefe2c: ldp             fp, lr, [SP], #0x10
    // 0xaefe30: ret
    //     0xaefe30: ret             
    // 0xaefe34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefe34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefe38: b               #0xaefe14
  }
  _ updatePassenger(/* No info */) {
    // ** addr: 0xaf0180, size: 0x58
    // 0xaf0180: EnterFrame
    //     0xaf0180: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0184: mov             fp, SP
    // 0xaf0188: AllocStack(0x18)
    //     0xaf0188: sub             SP, SP, #0x18
    // 0xaf018c: CheckStackOverflow
    //     0xaf018c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0190: cmp             SP, x16
    //     0xaf0194: b.ls            #0xaf01d0
    // 0xaf0198: LoadField: r4 = r1->field_57
    //     0xaf0198: ldur            w4, [x1, #0x57]
    // 0xaf019c: DecompressPointer r4
    //     0xaf019c: add             x4, x4, HEAP, lsl #32
    // 0xaf01a0: r0 = BoxInt64Instr(r2)
    //     0xaf01a0: sbfiz           x0, x2, #1, #0x1f
    //     0xaf01a4: cmp             x2, x0, asr #1
    //     0xaf01a8: b.eq            #0xaf01b4
    //     0xaf01ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf01b0: stur            x2, [x0, #7]
    // 0xaf01b4: stp             x0, x4, [SP, #8]
    // 0xaf01b8: str             x3, [SP]
    // 0xaf01bc: r0 = []=()
    //     0xaf01bc: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xaf01c0: r0 = Null
    //     0xaf01c0: mov             x0, NULL
    // 0xaf01c4: LeaveFrame
    //     0xaf01c4: mov             SP, fp
    //     0xaf01c8: ldp             fp, lr, [SP], #0x10
    // 0xaf01cc: ret
    //     0xaf01cc: ret             
    // 0xaf01d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf01d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf01d4: b               #0xaf0198
  }
  get _ showCalcInfo(/* No info */) {
    // ** addr: 0xaf0918, size: 0x90
    // 0xaf0918: EnterFrame
    //     0xaf0918: stp             fp, lr, [SP, #-0x10]!
    //     0xaf091c: mov             fp, SP
    // 0xaf0920: AllocStack(0x10)
    //     0xaf0920: sub             SP, SP, #0x10
    // 0xaf0924: CheckStackOverflow
    //     0xaf0924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0928: cmp             SP, x16
    //     0xaf092c: b.ls            #0xaf09a0
    // 0xaf0930: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf0930: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf0934: ldr             x0, [x0, #0x2670]
    //     0xaf0938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf093c: cmp             w0, w16
    //     0xaf0940: b.ne            #0xaf094c
    //     0xaf0944: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf0948: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf094c: r0 = GetNavigation.arguments()
    //     0xaf094c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0xaf0950: cmp             w0, NULL
    // 0xaf0954: b.ne            #0xaf0960
    // 0xaf0958: r1 = Null
    //     0xaf0958: mov             x1, NULL
    // 0xaf095c: b               #0xaf0984
    // 0xaf0960: r16 = "type"
    //     0xaf0960: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0xaf0964: stp             x16, x0, [SP]
    // 0xaf0968: r4 = 0
    //     0xaf0968: movz            x4, #0
    // 0xaf096c: ldr             x0, [SP, #8]
    // 0xaf0970: r16 = UnlinkedCall_0x5f3c08
    //     0xaf0970: add             x16, PP, #0x35, lsl #12  ; [pp+0x352a8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xaf0974: add             x16, x16, #0x2a8
    // 0xaf0978: ldp             x5, lr, [x16]
    // 0xaf097c: blr             lr
    // 0xaf0980: mov             x1, x0
    // 0xaf0984: cmp             w1, NULL
    // 0xaf0988: r16 = true
    //     0xaf0988: add             x16, NULL, #0x20  ; true
    // 0xaf098c: r17 = false
    //     0xaf098c: add             x17, NULL, #0x30  ; false
    // 0xaf0990: csel            x0, x16, x17, eq
    // 0xaf0994: LeaveFrame
    //     0xaf0994: mov             SP, fp
    //     0xaf0998: ldp             fp, lr, [SP], #0x10
    // 0xaf099c: ret
    //     0xaf099c: ret             
    // 0xaf09a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf09a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf09a4: b               #0xaf0930
  }
  [closure] Future<void> onCalcPressed(dynamic) {
    // ** addr: 0xaf0a00, size: 0x38
    // 0xaf0a00: EnterFrame
    //     0xaf0a00: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0a04: mov             fp, SP
    // 0xaf0a08: ldr             x0, [fp, #0x10]
    // 0xaf0a0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf0a0c: ldur            w1, [x0, #0x17]
    // 0xaf0a10: DecompressPointer r1
    //     0xaf0a10: add             x1, x1, HEAP, lsl #32
    // 0xaf0a14: CheckStackOverflow
    //     0xaf0a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0a18: cmp             SP, x16
    //     0xaf0a1c: b.ls            #0xaf0a30
    // 0xaf0a20: r0 = onCalcPressed()
    //     0xaf0a20: bl              #0xaf0a38  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onCalcPressed
    // 0xaf0a24: LeaveFrame
    //     0xaf0a24: mov             SP, fp
    //     0xaf0a28: ldp             fp, lr, [SP], #0x10
    // 0xaf0a2c: ret
    //     0xaf0a2c: ret             
    // 0xaf0a30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0a30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0a34: b               #0xaf0a20
  }
  _ onCalcPressed(/* No info */) async {
    // ** addr: 0xaf0a38, size: 0x198
    // 0xaf0a38: EnterFrame
    //     0xaf0a38: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0a3c: mov             fp, SP
    // 0xaf0a40: AllocStack(0x48)
    //     0xaf0a40: sub             SP, SP, #0x48
    // 0xaf0a44: SetupParameters(ZakatFormController this /* r1 => r1, fp-0x10 */)
    //     0xaf0a44: stur            NULL, [fp, #-8]
    //     0xaf0a48: stur            x1, [fp, #-0x10]
    // 0xaf0a4c: CheckStackOverflow
    //     0xaf0a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0a50: cmp             SP, x16
    //     0xaf0a54: b.ls            #0xaf0bc8
    // 0xaf0a58: InitAsync() -> Future<void?>
    //     0xaf0a58: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xaf0a5c: bl              #0x661298  ; InitAsyncStub
    // 0xaf0a60: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf0a60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf0a64: ldr             x0, [x0, #0x2670]
    //     0xaf0a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf0a6c: cmp             w0, w16
    //     0xaf0a70: b.ne            #0xaf0a7c
    //     0xaf0a74: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf0a78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf0a7c: ldur            x0, [fp, #-0x10]
    // 0xaf0a80: LoadField: r1 = r0->field_2f
    //     0xaf0a80: ldur            w1, [x0, #0x2f]
    // 0xaf0a84: DecompressPointer r1
    //     0xaf0a84: add             x1, x1, HEAP, lsl #32
    // 0xaf0a88: r0 = value()
    //     0xaf0a88: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0a8c: mov             x1, x0
    // 0xaf0a90: r0 = ZakatTypesExtension.route()
    //     0xaf0a90: bl              #0xaf0bd0  ; [package:nuonline/app/data/enums/zakat_enum.dart] ::ZakatTypesExtension.route
    // 0xaf0a94: r1 = Null
    //     0xaf0a94: mov             x1, NULL
    // 0xaf0a98: r2 = 4
    //     0xaf0a98: movz            x2, #0x4
    // 0xaf0a9c: stur            x0, [fp, #-0x18]
    // 0xaf0aa0: r0 = AllocateArray()
    //     0xaf0aa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0aa4: r16 = "from"
    //     0xaf0aa4: add             x16, PP, #0xc, lsl #12  ; [pp+0xc3d0] "from"
    //     0xaf0aa8: ldr             x16, [x16, #0x3d0]
    // 0xaf0aac: StoreField: r0->field_f = r16
    //     0xaf0aac: stur            w16, [x0, #0xf]
    // 0xaf0ab0: r16 = "pay-zakat"
    //     0xaf0ab0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27140] "pay-zakat"
    //     0xaf0ab4: ldr             x16, [x16, #0x140]
    // 0xaf0ab8: StoreField: r0->field_13 = r16
    //     0xaf0ab8: stur            w16, [x0, #0x13]
    // 0xaf0abc: r16 = <String, String>
    //     0xaf0abc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xaf0ac0: ldr             x16, [x16, #0x668]
    // 0xaf0ac4: stp             x0, x16, [SP]
    // 0xaf0ac8: r0 = Map._fromLiteral()
    //     0xaf0ac8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf0acc: ldur            x16, [fp, #-0x18]
    // 0xaf0ad0: stp             x16, NULL, [SP, #8]
    // 0xaf0ad4: str             x0, [SP]
    // 0xaf0ad8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xaf0ad8: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xaf0adc: ldr             x4, [x4, #0x478]
    // 0xaf0ae0: r0 = GetNavigation.toNamed()
    //     0xaf0ae0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf0ae4: mov             x1, x0
    // 0xaf0ae8: stur            x1, [fp, #-0x18]
    // 0xaf0aec: r0 = Await()
    //     0xaf0aec: bl              #0x661044  ; AwaitStub
    // 0xaf0af0: cmp             w0, NULL
    // 0xaf0af4: b.eq            #0xaf0bc0
    // 0xaf0af8: ldur            x3, [fp, #-0x10]
    // 0xaf0afc: LoadField: r1 = r3->field_5f
    //     0xaf0afc: ldur            w1, [x3, #0x5f]
    // 0xaf0b00: DecompressPointer r1
    //     0xaf0b00: add             x1, x1, HEAP, lsl #32
    // 0xaf0b04: mov             x2, x0
    // 0xaf0b08: r0 = format()
    //     0xaf0b08: bl              #0x8f5664  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xaf0b0c: ldur            x1, [fp, #-0x10]
    // 0xaf0b10: stur            x0, [fp, #-0x28]
    // 0xaf0b14: LoadField: r2 = r1->field_33
    //     0xaf0b14: ldur            w2, [x1, #0x33]
    // 0xaf0b18: DecompressPointer r2
    //     0xaf0b18: add             x2, x2, HEAP, lsl #32
    // 0xaf0b1c: stur            x2, [fp, #-0x20]
    // 0xaf0b20: LoadField: r3 = r0->field_7
    //     0xaf0b20: ldur            w3, [x0, #7]
    // 0xaf0b24: stur            x3, [fp, #-0x18]
    // 0xaf0b28: r0 = TextSelection()
    //     0xaf0b28: bl              #0x6865a8  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0xaf0b2c: mov             x1, x0
    // 0xaf0b30: r0 = Instance_TextAffinity
    //     0xaf0b30: ldr             x0, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0xaf0b34: stur            x1, [fp, #-0x30]
    // 0xaf0b38: StoreField: r1->field_27 = r0
    //     0xaf0b38: stur            w0, [x1, #0x27]
    // 0xaf0b3c: ldur            x0, [fp, #-0x18]
    // 0xaf0b40: r2 = LoadInt32Instr(r0)
    //     0xaf0b40: sbfx            x2, x0, #1, #0x1f
    // 0xaf0b44: ArrayStore: r1[0] = r2  ; List_8
    //     0xaf0b44: stur            x2, [x1, #0x17]
    // 0xaf0b48: StoreField: r1->field_1f = r2
    //     0xaf0b48: stur            x2, [x1, #0x1f]
    // 0xaf0b4c: r0 = false
    //     0xaf0b4c: add             x0, NULL, #0x30  ; false
    // 0xaf0b50: StoreField: r1->field_2b = r0
    //     0xaf0b50: stur            w0, [x1, #0x2b]
    // 0xaf0b54: StoreField: r1->field_7 = r2
    //     0xaf0b54: stur            x2, [x1, #7]
    // 0xaf0b58: StoreField: r1->field_f = r2
    //     0xaf0b58: stur            x2, [x1, #0xf]
    // 0xaf0b5c: r0 = TextEditingValue()
    //     0xaf0b5c: bl              #0x6aef08  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0xaf0b60: mov             x1, x0
    // 0xaf0b64: ldur            x0, [fp, #-0x28]
    // 0xaf0b68: StoreField: r1->field_7 = r0
    //     0xaf0b68: stur            w0, [x1, #7]
    // 0xaf0b6c: ldur            x0, [fp, #-0x30]
    // 0xaf0b70: StoreField: r1->field_b = r0
    //     0xaf0b70: stur            w0, [x1, #0xb]
    // 0xaf0b74: r0 = Instance_TextRange
    //     0xaf0b74: ldr             x0, [PP, #0x7268]  ; [pp+0x7268] Obj!TextRange@e26391
    // 0xaf0b78: StoreField: r1->field_f = r0
    //     0xaf0b78: stur            w0, [x1, #0xf]
    // 0xaf0b7c: mov             x2, x1
    // 0xaf0b80: ldur            x1, [fp, #-0x20]
    // 0xaf0b84: r0 = value=()
    //     0xaf0b84: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xaf0b88: ldur            x1, [fp, #-0x10]
    // 0xaf0b8c: LoadField: r0 = r1->field_23
    //     0xaf0b8c: ldur            w0, [x1, #0x23]
    // 0xaf0b90: DecompressPointer r0
    //     0xaf0b90: add             x0, x0, HEAP, lsl #32
    // 0xaf0b94: stur            x0, [fp, #-0x18]
    // 0xaf0b98: r0 = preAmount()
    //     0xaf0b98: bl              #0x8f7d90  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::preAmount
    // 0xaf0b9c: mov             x2, x0
    // 0xaf0ba0: r0 = BoxInt64Instr(r2)
    //     0xaf0ba0: sbfiz           x0, x2, #1, #0x1f
    //     0xaf0ba4: cmp             x2, x0, asr #1
    //     0xaf0ba8: b.eq            #0xaf0bb4
    //     0xaf0bac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf0bb0: stur            x2, [x0, #7]
    // 0xaf0bb4: ldur            x1, [fp, #-0x18]
    // 0xaf0bb8: mov             x2, x0
    // 0xaf0bbc: r0 = value=()
    //     0xaf0bbc: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf0bc0: r0 = Null
    //     0xaf0bc0: mov             x0, NULL
    // 0xaf0bc4: r0 = ReturnAsyncNotFuture()
    //     0xaf0bc4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xaf0bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0bc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0bcc: b               #0xaf0a58
  }
  [closure] Future<void> selectType(dynamic) {
    // ** addr: 0xaf134c, size: 0x38
    // 0xaf134c: EnterFrame
    //     0xaf134c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1350: mov             fp, SP
    // 0xaf1354: ldr             x0, [fp, #0x10]
    // 0xaf1358: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf1358: ldur            w1, [x0, #0x17]
    // 0xaf135c: DecompressPointer r1
    //     0xaf135c: add             x1, x1, HEAP, lsl #32
    // 0xaf1360: CheckStackOverflow
    //     0xaf1360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1364: cmp             SP, x16
    //     0xaf1368: b.ls            #0xaf137c
    // 0xaf136c: r0 = selectType()
    //     0xaf136c: bl              #0xaf1384  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::selectType
    // 0xaf1370: LeaveFrame
    //     0xaf1370: mov             SP, fp
    //     0xaf1374: ldp             fp, lr, [SP], #0x10
    // 0xaf1378: ret
    //     0xaf1378: ret             
    // 0xaf137c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf137c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1380: b               #0xaf136c
  }
  _ selectType(/* No info */) async {
    // ** addr: 0xaf1384, size: 0xe0
    // 0xaf1384: EnterFrame
    //     0xaf1384: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1388: mov             fp, SP
    // 0xaf138c: AllocStack(0x30)
    //     0xaf138c: sub             SP, SP, #0x30
    // 0xaf1390: SetupParameters(ZakatFormController this /* r1 => r1, fp-0x10 */)
    //     0xaf1390: stur            NULL, [fp, #-8]
    //     0xaf1394: stur            x1, [fp, #-0x10]
    // 0xaf1398: CheckStackOverflow
    //     0xaf1398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf139c: cmp             SP, x16
    //     0xaf13a0: b.ls            #0xaf145c
    // 0xaf13a4: r1 = 1
    //     0xaf13a4: movz            x1, #0x1
    // 0xaf13a8: r0 = AllocateContext()
    //     0xaf13a8: bl              #0xec126c  ; AllocateContextStub
    // 0xaf13ac: mov             x2, x0
    // 0xaf13b0: ldur            x1, [fp, #-0x10]
    // 0xaf13b4: stur            x2, [fp, #-0x18]
    // 0xaf13b8: StoreField: r2->field_f = r1
    //     0xaf13b8: stur            w1, [x2, #0xf]
    // 0xaf13bc: InitAsync() -> Future<void?>
    //     0xaf13bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xaf13c0: bl              #0x661298  ; InitAsyncStub
    // 0xaf13c4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf13c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf13c8: ldr             x0, [x0, #0x2670]
    //     0xaf13cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf13d0: cmp             w0, w16
    //     0xaf13d4: b.ne            #0xaf13e0
    //     0xaf13d8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf13dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf13e0: ldur            x2, [fp, #-0x18]
    // 0xaf13e4: r1 = Function '<anonymous closure>':.
    //     0xaf13e4: add             x1, PP, #0x35, lsl #12  ; [pp+0x352c8] AnonymousClosure: (0xaf1738), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::selectType (0xaf1384)
    //     0xaf13e8: ldr             x1, [x1, #0x2c8]
    // 0xaf13ec: r0 = AllocateClosure()
    //     0xaf13ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf13f0: r16 = <ZakatTypes>
    //     0xaf13f0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xaf13f4: ldr             x16, [x16, #0x68]
    // 0xaf13f8: stp             x0, x16, [SP]
    // 0xaf13fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf13fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf1400: r0 = GetNavigation.to()
    //     0xaf1400: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xaf1404: mov             x2, x0
    // 0xaf1408: r1 = <ZakatTypes?>
    //     0xaf1408: add             x1, PP, #0x35, lsl #12  ; [pp+0x352d0] TypeArguments: <ZakatTypes?>
    //     0xaf140c: ldr             x1, [x1, #0x2d0]
    // 0xaf1410: stur            x2, [fp, #-0x20]
    // 0xaf1414: r0 = AwaitWithTypeCheck()
    //     0xaf1414: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xaf1418: cmp             w0, NULL
    // 0xaf141c: b.eq            #0xaf1434
    // 0xaf1420: ldur            x3, [fp, #-0x10]
    // 0xaf1424: LoadField: r1 = r3->field_2f
    //     0xaf1424: ldur            w1, [x3, #0x2f]
    // 0xaf1428: DecompressPointer r1
    //     0xaf1428: add             x1, x1, HEAP, lsl #32
    // 0xaf142c: mov             x2, x0
    // 0xaf1430: r0 = value=()
    //     0xaf1430: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf1434: ldur            x0, [fp, #-0x10]
    // 0xaf1438: LoadField: r1 = r0->field_4b
    //     0xaf1438: ldur            w1, [x0, #0x4b]
    // 0xaf143c: DecompressPointer r1
    //     0xaf143c: add             x1, x1, HEAP, lsl #32
    // 0xaf1440: r0 = currentState()
    //     0xaf1440: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0xaf1444: cmp             w0, NULL
    // 0xaf1448: b.eq            #0xaf1454
    // 0xaf144c: mov             x1, x0
    // 0xaf1450: r0 = reset()
    //     0xaf1450: bl              #0xaf1464  ; [package:flutter/src/widgets/form.dart] FormState::reset
    // 0xaf1454: r0 = Null
    //     0xaf1454: mov             x0, NULL
    // 0xaf1458: r0 = ReturnAsyncNotFuture()
    //     0xaf1458: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xaf145c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf145c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1460: b               #0xaf13a4
  }
  [closure] ZakatTypesWidget <anonymous closure>(dynamic) {
    // ** addr: 0xaf1738, size: 0xa4
    // 0xaf1738: EnterFrame
    //     0xaf1738: stp             fp, lr, [SP, #-0x10]!
    //     0xaf173c: mov             fp, SP
    // 0xaf1740: AllocStack(0x10)
    //     0xaf1740: sub             SP, SP, #0x10
    // 0xaf1744: SetupParameters()
    //     0xaf1744: ldr             x0, [fp, #0x10]
    //     0xaf1748: ldur            w2, [x0, #0x17]
    //     0xaf174c: add             x2, x2, HEAP, lsl #32
    //     0xaf1750: stur            x2, [fp, #-8]
    // 0xaf1754: CheckStackOverflow
    //     0xaf1754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1758: cmp             SP, x16
    //     0xaf175c: b.ls            #0xaf17d4
    // 0xaf1760: LoadField: r0 = r2->field_f
    //     0xaf1760: ldur            w0, [x2, #0xf]
    // 0xaf1764: DecompressPointer r0
    //     0xaf1764: add             x0, x0, HEAP, lsl #32
    // 0xaf1768: LoadField: r1 = r0->field_2f
    //     0xaf1768: ldur            w1, [x0, #0x2f]
    // 0xaf176c: DecompressPointer r1
    //     0xaf176c: add             x1, x1, HEAP, lsl #32
    // 0xaf1770: r0 = value()
    //     0xaf1770: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf1774: mov             x2, x0
    // 0xaf1778: ldur            x0, [fp, #-8]
    // 0xaf177c: stur            x2, [fp, #-0x10]
    // 0xaf1780: LoadField: r1 = r0->field_f
    //     0xaf1780: ldur            w1, [x0, #0xf]
    // 0xaf1784: DecompressPointer r1
    //     0xaf1784: add             x1, x1, HEAP, lsl #32
    // 0xaf1788: LoadField: r0 = r1->field_2f
    //     0xaf1788: ldur            w0, [x1, #0x2f]
    // 0xaf178c: DecompressPointer r0
    //     0xaf178c: add             x0, x0, HEAP, lsl #32
    // 0xaf1790: mov             x1, x0
    // 0xaf1794: r0 = value()
    //     0xaf1794: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf1798: r16 = Instance_ZakatTypes
    //     0xaf1798: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xaf179c: ldr             x16, [x16, #0x620]
    // 0xaf17a0: cmp             w0, w16
    // 0xaf17a4: r16 = true
    //     0xaf17a4: add             x16, NULL, #0x20  ; true
    // 0xaf17a8: r17 = false
    //     0xaf17a8: add             x17, NULL, #0x30  ; false
    // 0xaf17ac: csel            x1, x16, x17, eq
    // 0xaf17b0: stur            x1, [fp, #-8]
    // 0xaf17b4: r0 = ZakatTypesWidget()
    //     0xaf17b4: bl              #0xaf17dc  ; AllocateZakatTypesWidgetStub -> ZakatTypesWidget (size=0x14)
    // 0xaf17b8: ldur            x1, [fp, #-0x10]
    // 0xaf17bc: StoreField: r0->field_b = r1
    //     0xaf17bc: stur            w1, [x0, #0xb]
    // 0xaf17c0: ldur            x1, [fp, #-8]
    // 0xaf17c4: StoreField: r0->field_f = r1
    //     0xaf17c4: stur            w1, [x0, #0xf]
    // 0xaf17c8: LeaveFrame
    //     0xaf17c8: mov             SP, fp
    //     0xaf17cc: ldp             fp, lr, [SP], #0x10
    // 0xaf17d0: ret
    //     0xaf17d0: ret             
    // 0xaf17d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf17d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf17d8: b               #0xaf1760
  }
  get _ trx(/* No info */) {
    // ** addr: 0xe357d0, size: 0x1f4
    // 0xe357d0: EnterFrame
    //     0xe357d0: stp             fp, lr, [SP, #-0x10]!
    //     0xe357d4: mov             fp, SP
    // 0xe357d8: AllocStack(0x48)
    //     0xe357d8: sub             SP, SP, #0x48
    // 0xe357dc: SetupParameters(ZakatFormController this /* r1 => r0, fp-0x10 */)
    //     0xe357dc: mov             x0, x1
    //     0xe357e0: stur            x1, [fp, #-0x10]
    // 0xe357e4: CheckStackOverflow
    //     0xe357e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe357e8: cmp             SP, x16
    //     0xe357ec: b.ls            #0xe359bc
    // 0xe357f0: LoadField: r2 = r0->field_2f
    //     0xe357f0: ldur            w2, [x0, #0x2f]
    // 0xe357f4: DecompressPointer r2
    //     0xe357f4: add             x2, x2, HEAP, lsl #32
    // 0xe357f8: mov             x1, x2
    // 0xe357fc: stur            x2, [fp, #-8]
    // 0xe35800: r0 = value()
    //     0xe35800: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35804: mov             x2, x0
    // 0xe35808: ldur            x0, [fp, #-0x10]
    // 0xe3580c: stur            x2, [fp, #-0x18]
    // 0xe35810: LoadField: r1 = r0->field_27
    //     0xe35810: ldur            w1, [x0, #0x27]
    // 0xe35814: DecompressPointer r1
    //     0xe35814: add             x1, x1, HEAP, lsl #32
    // 0xe35818: r0 = value()
    //     0xe35818: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3581c: r1 = Null
    //     0xe3581c: mov             x1, NULL
    // 0xe35820: r2 = 2
    //     0xe35820: movz            x2, #0x2
    // 0xe35824: stur            x0, [fp, #-0x20]
    // 0xe35828: r0 = AllocateArray()
    //     0xe35828: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe3582c: mov             x2, x0
    // 0xe35830: ldur            x0, [fp, #-0x20]
    // 0xe35834: stur            x2, [fp, #-0x28]
    // 0xe35838: StoreField: r2->field_f = r0
    //     0xe35838: stur            w0, [x2, #0xf]
    // 0xe3583c: r1 = <String>
    //     0xe3583c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe35840: r0 = AllocateGrowableArray()
    //     0xe35840: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe35844: mov             x3, x0
    // 0xe35848: ldur            x0, [fp, #-0x28]
    // 0xe3584c: stur            x3, [fp, #-0x20]
    // 0xe35850: StoreField: r3->field_f = r0
    //     0xe35850: stur            w0, [x3, #0xf]
    // 0xe35854: r0 = 2
    //     0xe35854: movz            x0, #0x2
    // 0xe35858: StoreField: r3->field_b = r0
    //     0xe35858: stur            w0, [x3, #0xb]
    // 0xe3585c: ldur            x0, [fp, #-0x10]
    // 0xe35860: LoadField: r2 = r0->field_57
    //     0xe35860: ldur            w2, [x0, #0x57]
    // 0xe35864: DecompressPointer r2
    //     0xe35864: add             x2, x2, HEAP, lsl #32
    // 0xe35868: mov             x1, x3
    // 0xe3586c: r0 = addAll()
    //     0xe3586c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xe35870: r16 = ", "
    //     0xe35870: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xe35874: str             x16, [SP]
    // 0xe35878: ldur            x1, [fp, #-0x20]
    // 0xe3587c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe3587c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe35880: r0 = join()
    //     0xe35880: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xe35884: mov             x2, x0
    // 0xe35888: ldur            x0, [fp, #-0x10]
    // 0xe3588c: stur            x2, [fp, #-0x20]
    // 0xe35890: LoadField: r1 = r0->field_2b
    //     0xe35890: ldur            w1, [x0, #0x2b]
    // 0xe35894: DecompressPointer r1
    //     0xe35894: add             x1, x1, HEAP, lsl #32
    // 0xe35898: r0 = value()
    //     0xe35898: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe3589c: ldur            x1, [fp, #-8]
    // 0xe358a0: stur            x0, [fp, #-0x28]
    // 0xe358a4: r0 = value()
    //     0xe358a4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe358a8: mov             x1, x0
    // 0xe358ac: r0 = ZakatTypesExtension.title()
    //     0xe358ac: bl              #0xaf11b4  ; [package:nuonline/app/data/enums/zakat_enum.dart] ::ZakatTypesExtension.title
    // 0xe358b0: ldur            x1, [fp, #-8]
    // 0xe358b4: stur            x0, [fp, #-8]
    // 0xe358b8: r0 = value()
    //     0xe358b8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe358bc: mov             x2, x0
    // 0xe358c0: ldur            x0, [fp, #-0x10]
    // 0xe358c4: stur            x2, [fp, #-0x30]
    // 0xe358c8: LoadField: r1 = r0->field_53
    //     0xe358c8: ldur            w1, [x0, #0x53]
    // 0xe358cc: DecompressPointer r1
    //     0xe358cc: add             x1, x1, HEAP, lsl #32
    // 0xe358d0: r0 = value()
    //     0xe358d0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe358d4: LoadField: r2 = r0->field_b
    //     0xe358d4: ldur            x2, [x0, #0xb]
    // 0xe358d8: ldur            x1, [fp, #-0x10]
    // 0xe358dc: stur            x2, [fp, #-0x38]
    // 0xe358e0: r0 = seatCount()
    //     0xe358e0: bl              #0xaef7f4  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::seatCount
    // 0xe358e4: mov             x1, x0
    // 0xe358e8: ldur            x0, [fp, #-0x38]
    // 0xe358ec: mul             x2, x0, x1
    // 0xe358f0: ldur            x0, [fp, #-0x10]
    // 0xe358f4: stur            x2, [fp, #-0x40]
    // 0xe358f8: LoadField: r1 = r0->field_23
    //     0xe358f8: ldur            w1, [x0, #0x23]
    // 0xe358fc: DecompressPointer r1
    //     0xe358fc: add             x1, x1, HEAP, lsl #32
    // 0xe35900: r0 = value()
    //     0xe35900: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe35904: mov             x1, x0
    // 0xe35908: ldur            x0, [fp, #-0x30]
    // 0xe3590c: r16 = Instance_ZakatTypes
    //     0xe3590c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xe35910: ldr             x16, [x16, #0x620]
    // 0xe35914: cmp             w0, w16
    // 0xe35918: b.ne            #0xe35924
    // 0xe3591c: ldur            x5, [fp, #-0x40]
    // 0xe35920: b               #0xe35934
    // 0xe35924: r0 = LoadInt32Instr(r1)
    //     0xe35924: sbfx            x0, x1, #1, #0x1f
    //     0xe35928: tbz             w1, #0, #0xe35930
    //     0xe3592c: ldur            x0, [x1, #7]
    // 0xe35930: mov             x5, x0
    // 0xe35934: ldur            x4, [fp, #-0x18]
    // 0xe35938: ldur            x3, [fp, #-0x20]
    // 0xe3593c: ldur            x2, [fp, #-0x28]
    // 0xe35940: ldur            x0, [fp, #-8]
    // 0xe35944: stur            x5, [fp, #-0x38]
    // 0xe35948: r1 = <ZakatTypes>
    //     0xe35948: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xe3594c: ldr             x1, [x1, #0x68]
    // 0xe35950: r0 = TransactionRequest()
    //     0xe35950: bl              #0x810fc0  ; AllocateTransactionRequestStub -> TransactionRequest<X0> (size=0x4c)
    // 0xe35954: mov             x3, x0
    // 0xe35958: r2 = Instance_PaymentMethod
    //     0xe35958: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xe3595c: ldr             x2, [x2, #0x220]
    // 0xe35960: StoreField: r3->field_b = r2
    //     0xe35960: stur            w2, [x3, #0xb]
    // 0xe35964: r2 = Instance_PaymentType
    //     0xe35964: add             x2, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xe35968: ldr             x2, [x2, #0x5c0]
    // 0xe3596c: StoreField: r3->field_f = r2
    //     0xe3596c: stur            w2, [x3, #0xf]
    // 0xe35970: ldur            x2, [fp, #-0x18]
    // 0xe35974: StoreField: r3->field_13 = r2
    //     0xe35974: stur            w2, [x3, #0x13]
    // 0xe35978: ldur            x2, [fp, #-0x38]
    // 0xe3597c: r0 = BoxInt64Instr(r2)
    //     0xe3597c: sbfiz           x0, x2, #1, #0x1f
    //     0xe35980: cmp             x2, x0, asr #1
    //     0xe35984: b.eq            #0xe35990
    //     0xe35988: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe3598c: stur            x2, [x0, #7]
    // 0xe35990: ArrayStore: r3[0] = r0  ; List_4
    //     0xe35990: stur            w0, [x3, #0x17]
    // 0xe35994: ldur            x1, [fp, #-0x20]
    // 0xe35998: StoreField: r3->field_1b = r1
    //     0xe35998: stur            w1, [x3, #0x1b]
    // 0xe3599c: ldur            x1, [fp, #-8]
    // 0xe359a0: StoreField: r3->field_2f = r1
    //     0xe359a0: stur            w1, [x3, #0x2f]
    // 0xe359a4: ldur            x1, [fp, #-0x28]
    // 0xe359a8: StoreField: r3->field_33 = r1
    //     0xe359a8: stur            w1, [x3, #0x33]
    // 0xe359ac: mov             x0, x3
    // 0xe359b0: LeaveFrame
    //     0xe359b0: mov             SP, fp
    //     0xe359b4: ldp             fp, lr, [SP], #0x10
    // 0xe359b8: ret
    //     0xe359b8: ret             
    // 0xe359bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe359bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe359c0: b               #0xe357f0
  }
}
