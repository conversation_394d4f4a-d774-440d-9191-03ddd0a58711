// lib: , url: package:nuonline/app/modules/donation/views/campaign_detail_view.dart

// class id: 1050217, size: 0x8
class :: {
}

// class id: 5047, size: 0xc, field offset: 0xc
//   const constructor, 
class _Skeleton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8c8b8, size: 0xc
    // 0xb8c8b8: r0 = Instance_Scaffold
    //     0xb8c8b8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35638] Obj!Scaffold@e1f5c1
    //     0xb8c8bc: ldr             x0, [x0, #0x638]
    // 0xb8c8c0: ret
    //     0xb8c8c0: ret             
  }
}

// class id: 5297, size: 0x14, field offset: 0x14
//   const constructor, 
class CampaignDetailView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xadd2e8, size: 0x1b8
    // 0xadd2e8: EnterFrame
    //     0xadd2e8: stp             fp, lr, [SP, #-0x10]!
    //     0xadd2ec: mov             fp, SP
    // 0xadd2f0: AllocStack(0x38)
    //     0xadd2f0: sub             SP, SP, #0x38
    // 0xadd2f4: SetupParameters(CampaignDetailView this /* r1 => r1, fp-0x8 */)
    //     0xadd2f4: stur            x1, [fp, #-8]
    // 0xadd2f8: CheckStackOverflow
    //     0xadd2f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd2fc: cmp             SP, x16
    //     0xadd300: b.ls            #0xadd498
    // 0xadd304: r1 = 1
    //     0xadd304: movz            x1, #0x1
    // 0xadd308: r0 = AllocateContext()
    //     0xadd308: bl              #0xec126c  ; AllocateContextStub
    // 0xadd30c: ldur            x1, [fp, #-8]
    // 0xadd310: stur            x0, [fp, #-0x10]
    // 0xadd314: StoreField: r0->field_f = r1
    //     0xadd314: stur            w1, [x0, #0xf]
    // 0xadd318: r0 = controller()
    //     0xadd318: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd31c: ldur            x2, [fp, #-0x10]
    // 0xadd320: r1 = Function '<anonymous closure>':.
    //     0xadd320: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f590] AnonymousClosure: (0xadd800), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadd324: ldr             x1, [x1, #0x590]
    // 0xadd328: stur            x0, [fp, #-8]
    // 0xadd32c: r0 = AllocateClosure()
    //     0xadd32c: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd330: r16 = <Campaign>
    //     0xadd330: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0xadd334: ldr             x16, [x16, #0x598]
    // 0xadd338: ldur            lr, [fp, #-8]
    // 0xadd33c: stp             lr, x16, [SP, #0x10]
    // 0xadd340: r16 = Instance__Skeleton
    //     0xadd340: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f5a0] Obj!_Skeleton@e1fac1
    //     0xadd344: ldr             x16, [x16, #0x5a0]
    // 0xadd348: stp             x16, x0, [SP]
    // 0xadd34c: r4 = const [0x1, 0x3, 0x3, 0x2, onLoading, 0x2, null]
    //     0xadd34c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25718] List(7) [0x1, 0x3, 0x3, 0x2, "onLoading", 0x2, Null]
    //     0xadd350: ldr             x4, [x4, #0x718]
    // 0xadd354: r0 = StateExt.obx()
    //     0xadd354: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xadd358: r1 = <FlexParentData>
    //     0xadd358: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xadd35c: ldr             x1, [x1, #0x720]
    // 0xadd360: stur            x0, [fp, #-8]
    // 0xadd364: r0 = Expanded()
    //     0xadd364: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xadd368: mov             x1, x0
    // 0xadd36c: r0 = 1
    //     0xadd36c: movz            x0, #0x1
    // 0xadd370: stur            x1, [fp, #-0x18]
    // 0xadd374: StoreField: r1->field_13 = r0
    //     0xadd374: stur            x0, [x1, #0x13]
    // 0xadd378: r0 = Instance_FlexFit
    //     0xadd378: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xadd37c: ldr             x0, [x0, #0x728]
    // 0xadd380: StoreField: r1->field_1b = r0
    //     0xadd380: stur            w0, [x1, #0x1b]
    // 0xadd384: ldur            x0, [fp, #-8]
    // 0xadd388: StoreField: r1->field_b = r0
    //     0xadd388: stur            w0, [x1, #0xb]
    // 0xadd38c: r0 = Obx()
    //     0xadd38c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xadd390: ldur            x2, [fp, #-0x10]
    // 0xadd394: r1 = Function '<anonymous closure>':.
    //     0xadd394: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f5a8] AnonymousClosure: (0xadd4a0), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadd398: ldr             x1, [x1, #0x5a8]
    // 0xadd39c: stur            x0, [fp, #-8]
    // 0xadd3a0: r0 = AllocateClosure()
    //     0xadd3a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd3a4: mov             x1, x0
    // 0xadd3a8: ldur            x0, [fp, #-8]
    // 0xadd3ac: StoreField: r0->field_b = r1
    //     0xadd3ac: stur            w1, [x0, #0xb]
    // 0xadd3b0: r1 = Null
    //     0xadd3b0: mov             x1, NULL
    // 0xadd3b4: r2 = 4
    //     0xadd3b4: movz            x2, #0x4
    // 0xadd3b8: r0 = AllocateArray()
    //     0xadd3b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadd3bc: mov             x2, x0
    // 0xadd3c0: ldur            x0, [fp, #-0x18]
    // 0xadd3c4: stur            x2, [fp, #-0x10]
    // 0xadd3c8: StoreField: r2->field_f = r0
    //     0xadd3c8: stur            w0, [x2, #0xf]
    // 0xadd3cc: ldur            x0, [fp, #-8]
    // 0xadd3d0: StoreField: r2->field_13 = r0
    //     0xadd3d0: stur            w0, [x2, #0x13]
    // 0xadd3d4: r1 = <Widget>
    //     0xadd3d4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadd3d8: r0 = AllocateGrowableArray()
    //     0xadd3d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadd3dc: mov             x1, x0
    // 0xadd3e0: ldur            x0, [fp, #-0x10]
    // 0xadd3e4: stur            x1, [fp, #-8]
    // 0xadd3e8: StoreField: r1->field_f = r0
    //     0xadd3e8: stur            w0, [x1, #0xf]
    // 0xadd3ec: r0 = 4
    //     0xadd3ec: movz            x0, #0x4
    // 0xadd3f0: StoreField: r1->field_b = r0
    //     0xadd3f0: stur            w0, [x1, #0xb]
    // 0xadd3f4: r0 = Column()
    //     0xadd3f4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xadd3f8: mov             x1, x0
    // 0xadd3fc: r0 = Instance_Axis
    //     0xadd3fc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xadd400: stur            x1, [fp, #-0x10]
    // 0xadd404: StoreField: r1->field_f = r0
    //     0xadd404: stur            w0, [x1, #0xf]
    // 0xadd408: r0 = Instance_MainAxisAlignment
    //     0xadd408: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xadd40c: ldr             x0, [x0, #0x730]
    // 0xadd410: StoreField: r1->field_13 = r0
    //     0xadd410: stur            w0, [x1, #0x13]
    // 0xadd414: r0 = Instance_MainAxisSize
    //     0xadd414: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xadd418: ldr             x0, [x0, #0x738]
    // 0xadd41c: ArrayStore: r1[0] = r0  ; List_4
    //     0xadd41c: stur            w0, [x1, #0x17]
    // 0xadd420: r0 = Instance_CrossAxisAlignment
    //     0xadd420: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xadd424: ldr             x0, [x0, #0x740]
    // 0xadd428: StoreField: r1->field_1b = r0
    //     0xadd428: stur            w0, [x1, #0x1b]
    // 0xadd42c: r0 = Instance_VerticalDirection
    //     0xadd42c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xadd430: ldr             x0, [x0, #0x748]
    // 0xadd434: StoreField: r1->field_23 = r0
    //     0xadd434: stur            w0, [x1, #0x23]
    // 0xadd438: r0 = Instance_Clip
    //     0xadd438: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xadd43c: ldr             x0, [x0, #0x750]
    // 0xadd440: StoreField: r1->field_2b = r0
    //     0xadd440: stur            w0, [x1, #0x2b]
    // 0xadd444: StoreField: r1->field_2f = rZR
    //     0xadd444: stur            xzr, [x1, #0x2f]
    // 0xadd448: ldur            x0, [fp, #-8]
    // 0xadd44c: StoreField: r1->field_b = r0
    //     0xadd44c: stur            w0, [x1, #0xb]
    // 0xadd450: r0 = Scaffold()
    //     0xadd450: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xadd454: ldur            x1, [fp, #-0x10]
    // 0xadd458: ArrayStore: r0[0] = r1  ; List_4
    //     0xadd458: stur            w1, [x0, #0x17]
    // 0xadd45c: r1 = Instance_AlignmentDirectional
    //     0xadd45c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xadd460: ldr             x1, [x1, #0x758]
    // 0xadd464: StoreField: r0->field_2b = r1
    //     0xadd464: stur            w1, [x0, #0x2b]
    // 0xadd468: r1 = true
    //     0xadd468: add             x1, NULL, #0x20  ; true
    // 0xadd46c: StoreField: r0->field_53 = r1
    //     0xadd46c: stur            w1, [x0, #0x53]
    // 0xadd470: r2 = Instance_DragStartBehavior
    //     0xadd470: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadd474: StoreField: r0->field_57 = r2
    //     0xadd474: stur            w2, [x0, #0x57]
    // 0xadd478: r2 = false
    //     0xadd478: add             x2, NULL, #0x30  ; false
    // 0xadd47c: StoreField: r0->field_b = r2
    //     0xadd47c: stur            w2, [x0, #0xb]
    // 0xadd480: StoreField: r0->field_f = r2
    //     0xadd480: stur            w2, [x0, #0xf]
    // 0xadd484: StoreField: r0->field_5f = r1
    //     0xadd484: stur            w1, [x0, #0x5f]
    // 0xadd488: StoreField: r0->field_63 = r1
    //     0xadd488: stur            w1, [x0, #0x63]
    // 0xadd48c: LeaveFrame
    //     0xadd48c: mov             SP, fp
    //     0xadd490: ldp             fp, lr, [SP], #0x10
    // 0xadd494: ret
    //     0xadd494: ret             
    // 0xadd498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd49c: b               #0xadd304
  }
  [closure] Visibility <anonymous closure>(dynamic) {
    // ** addr: 0xadd4a0, size: 0x208
    // 0xadd4a0: EnterFrame
    //     0xadd4a0: stp             fp, lr, [SP, #-0x10]!
    //     0xadd4a4: mov             fp, SP
    // 0xadd4a8: AllocStack(0x30)
    //     0xadd4a8: sub             SP, SP, #0x30
    // 0xadd4ac: SetupParameters()
    //     0xadd4ac: ldr             x0, [fp, #0x10]
    //     0xadd4b0: ldur            w2, [x0, #0x17]
    //     0xadd4b4: add             x2, x2, HEAP, lsl #32
    //     0xadd4b8: stur            x2, [fp, #-8]
    // 0xadd4bc: CheckStackOverflow
    //     0xadd4bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd4c0: cmp             SP, x16
    //     0xadd4c4: b.ls            #0xadd6a0
    // 0xadd4c8: LoadField: r1 = r2->field_f
    //     0xadd4c8: ldur            w1, [x2, #0xf]
    // 0xadd4cc: DecompressPointer r1
    //     0xadd4cc: add             x1, x1, HEAP, lsl #32
    // 0xadd4d0: r0 = controller()
    //     0xadd4d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd4d4: LoadField: r1 = r0->field_4b
    //     0xadd4d4: ldur            w1, [x0, #0x4b]
    // 0xadd4d8: DecompressPointer r1
    //     0xadd4d8: add             x1, x1, HEAP, lsl #32
    // 0xadd4dc: r0 = value()
    //     0xadd4dc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xadd4e0: r1 = <Offset>
    //     0xadd4e0: add             x1, PP, #0x22, lsl #12  ; [pp+0x22100] TypeArguments: <Offset>
    //     0xadd4e4: ldr             x1, [x1, #0x100]
    // 0xadd4e8: stur            x0, [fp, #-0x10]
    // 0xadd4ec: r0 = Tween()
    //     0xadd4ec: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xadd4f0: mov             x2, x0
    // 0xadd4f4: r0 = Instance_Offset
    //     0xadd4f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5b0] Obj!Offset@e2c3c1
    //     0xadd4f8: ldr             x0, [x0, #0x5b0]
    // 0xadd4fc: stur            x2, [fp, #-0x18]
    // 0xadd500: StoreField: r2->field_b = r0
    //     0xadd500: stur            w0, [x2, #0xb]
    // 0xadd504: r0 = Instance_Offset
    //     0xadd504: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xadd508: StoreField: r2->field_f = r0
    //     0xadd508: stur            w0, [x2, #0xf]
    // 0xadd50c: ldur            x0, [fp, #-8]
    // 0xadd510: LoadField: r1 = r0->field_f
    //     0xadd510: ldur            w1, [x0, #0xf]
    // 0xadd514: DecompressPointer r1
    //     0xadd514: add             x1, x1, HEAP, lsl #32
    // 0xadd518: r0 = controller()
    //     0xadd518: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd51c: mov             x1, x0
    // 0xadd520: LoadField: r0 = r1->field_43
    //     0xadd520: ldur            w0, [x1, #0x43]
    // 0xadd524: DecompressPointer r0
    //     0xadd524: add             x0, x0, HEAP, lsl #32
    // 0xadd528: r16 = Sentinel
    //     0xadd528: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadd52c: cmp             w0, w16
    // 0xadd530: b.ne            #0xadd540
    // 0xadd534: r2 = animationController
    //     0xadd534: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f5b8] Field <CampaignDetailController.animationController>: late final (offset: 0x44)
    //     0xadd538: ldr             x2, [x2, #0x5b8]
    // 0xadd53c: r0 = InitLateFinalInstanceField()
    //     0xadd53c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xadd540: r1 = <double>
    //     0xadd540: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xadd544: stur            x0, [fp, #-0x20]
    // 0xadd548: r0 = CurvedAnimation()
    //     0xadd548: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0xadd54c: mov             x1, x0
    // 0xadd550: ldur            x3, [fp, #-0x20]
    // 0xadd554: r2 = Instance_Cubic
    //     0xadd554: ldr             x2, [PP, #0x6e20]  ; [pp+0x6e20] Obj!Cubic@e14d41
    // 0xadd558: stur            x0, [fp, #-0x20]
    // 0xadd55c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xadd55c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xadd560: r0 = CurvedAnimation()
    //     0xadd560: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0xadd564: ldur            x1, [fp, #-0x18]
    // 0xadd568: ldur            x2, [fp, #-0x20]
    // 0xadd56c: r0 = animate()
    //     0xadd56c: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0xadd570: mov             x2, x0
    // 0xadd574: ldur            x0, [fp, #-8]
    // 0xadd578: stur            x2, [fp, #-0x18]
    // 0xadd57c: LoadField: r1 = r0->field_f
    //     0xadd57c: ldur            w1, [x0, #0xf]
    // 0xadd580: DecompressPointer r1
    //     0xadd580: add             x1, x1, HEAP, lsl #32
    // 0xadd584: r0 = controller()
    //     0xadd584: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd588: mov             x2, x0
    // 0xadd58c: ldur            x0, [fp, #-8]
    // 0xadd590: stur            x2, [fp, #-0x20]
    // 0xadd594: LoadField: r1 = r0->field_f
    //     0xadd594: ldur            w1, [x0, #0xf]
    // 0xadd598: DecompressPointer r1
    //     0xadd598: add             x1, x1, HEAP, lsl #32
    // 0xadd59c: r0 = type()
    //     0xadd59c: bl              #0xadd6b4  ; [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::type
    // 0xadd5a0: r1 = Null
    //     0xadd5a0: mov             x1, NULL
    // 0xadd5a4: r2 = 4
    //     0xadd5a4: movz            x2, #0x4
    // 0xadd5a8: stur            x0, [fp, #-8]
    // 0xadd5ac: r0 = AllocateArray()
    //     0xadd5ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadd5b0: mov             x1, x0
    // 0xadd5b4: ldur            x0, [fp, #-8]
    // 0xadd5b8: StoreField: r1->field_f = r0
    //     0xadd5b8: stur            w0, [x1, #0xf]
    // 0xadd5bc: r16 = " Sekarang"
    //     0xadd5bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f5c0] " Sekarang"
    //     0xadd5c0: ldr             x16, [x16, #0x5c0]
    // 0xadd5c4: StoreField: r1->field_13 = r16
    //     0xadd5c4: stur            w16, [x1, #0x13]
    // 0xadd5c8: str             x1, [SP]
    // 0xadd5cc: r0 = _interpolate()
    //     0xadd5cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xadd5d0: stur            x0, [fp, #-8]
    // 0xadd5d4: r0 = Text()
    //     0xadd5d4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadd5d8: mov             x3, x0
    // 0xadd5dc: ldur            x0, [fp, #-8]
    // 0xadd5e0: stur            x3, [fp, #-0x28]
    // 0xadd5e4: StoreField: r3->field_b = r0
    //     0xadd5e4: stur            w0, [x3, #0xb]
    // 0xadd5e8: ldur            x2, [fp, #-0x20]
    // 0xadd5ec: r1 = Function 'pay':.
    //     0xadd5ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f5c8] AnonymousClosure: (0xadd70c), in [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::pay (0xadd744)
    //     0xadd5f0: ldr             x1, [x1, #0x5c8]
    // 0xadd5f4: r0 = AllocateClosure()
    //     0xadd5f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd5f8: stur            x0, [fp, #-8]
    // 0xadd5fc: r0 = TextButton()
    //     0xadd5fc: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xadd600: mov             x1, x0
    // 0xadd604: ldur            x0, [fp, #-8]
    // 0xadd608: stur            x1, [fp, #-0x20]
    // 0xadd60c: StoreField: r1->field_b = r0
    //     0xadd60c: stur            w0, [x1, #0xb]
    // 0xadd610: r0 = false
    //     0xadd610: add             x0, NULL, #0x30  ; false
    // 0xadd614: StoreField: r1->field_27 = r0
    //     0xadd614: stur            w0, [x1, #0x27]
    // 0xadd618: r2 = true
    //     0xadd618: add             x2, NULL, #0x20  ; true
    // 0xadd61c: StoreField: r1->field_2f = r2
    //     0xadd61c: stur            w2, [x1, #0x2f]
    // 0xadd620: ldur            x3, [fp, #-0x28]
    // 0xadd624: StoreField: r1->field_37 = r3
    //     0xadd624: stur            w3, [x1, #0x37]
    // 0xadd628: r0 = NPersistentFooterButton()
    //     0xadd628: bl              #0xadd6a8  ; AllocateNPersistentFooterButtonStub -> NPersistentFooterButton (size=0x10)
    // 0xadd62c: mov             x1, x0
    // 0xadd630: ldur            x0, [fp, #-0x20]
    // 0xadd634: stur            x1, [fp, #-8]
    // 0xadd638: StoreField: r1->field_b = r0
    //     0xadd638: stur            w0, [x1, #0xb]
    // 0xadd63c: r0 = SlideTransition()
    //     0xadd63c: bl              #0x9da71c  ; AllocateSlideTransitionStub -> SlideTransition (size=0x1c)
    // 0xadd640: mov             x1, x0
    // 0xadd644: r0 = true
    //     0xadd644: add             x0, NULL, #0x20  ; true
    // 0xadd648: stur            x1, [fp, #-0x20]
    // 0xadd64c: StoreField: r1->field_13 = r0
    //     0xadd64c: stur            w0, [x1, #0x13]
    // 0xadd650: ldur            x0, [fp, #-8]
    // 0xadd654: ArrayStore: r1[0] = r0  ; List_4
    //     0xadd654: stur            w0, [x1, #0x17]
    // 0xadd658: ldur            x0, [fp, #-0x18]
    // 0xadd65c: StoreField: r1->field_b = r0
    //     0xadd65c: stur            w0, [x1, #0xb]
    // 0xadd660: r0 = Visibility()
    //     0xadd660: bl              #0xaa1994  ; AllocateVisibilityStub -> Visibility (size=0x2c)
    // 0xadd664: ldur            x1, [fp, #-0x20]
    // 0xadd668: StoreField: r0->field_b = r1
    //     0xadd668: stur            w1, [x0, #0xb]
    // 0xadd66c: r1 = Instance_SizedBox
    //     0xadd66c: ldr             x1, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xadd670: StoreField: r0->field_f = r1
    //     0xadd670: stur            w1, [x0, #0xf]
    // 0xadd674: ldur            x1, [fp, #-0x10]
    // 0xadd678: StoreField: r0->field_13 = r1
    //     0xadd678: stur            w1, [x0, #0x13]
    // 0xadd67c: r1 = false
    //     0xadd67c: add             x1, NULL, #0x30  ; false
    // 0xadd680: ArrayStore: r0[0] = r1  ; List_4
    //     0xadd680: stur            w1, [x0, #0x17]
    // 0xadd684: StoreField: r0->field_1b = r1
    //     0xadd684: stur            w1, [x0, #0x1b]
    // 0xadd688: StoreField: r0->field_1f = r1
    //     0xadd688: stur            w1, [x0, #0x1f]
    // 0xadd68c: StoreField: r0->field_23 = r1
    //     0xadd68c: stur            w1, [x0, #0x23]
    // 0xadd690: StoreField: r0->field_27 = r1
    //     0xadd690: stur            w1, [x0, #0x27]
    // 0xadd694: LeaveFrame
    //     0xadd694: mov             SP, fp
    //     0xadd698: ldp             fp, lr, [SP], #0x10
    // 0xadd69c: ret
    //     0xadd69c: ret             
    // 0xadd6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd6a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd6a4: b               #0xadd4c8
  }
  get _ type(/* No info */) {
    // ** addr: 0xadd6b4, size: 0x58
    // 0xadd6b4: EnterFrame
    //     0xadd6b4: stp             fp, lr, [SP, #-0x10]!
    //     0xadd6b8: mov             fp, SP
    // 0xadd6bc: CheckStackOverflow
    //     0xadd6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd6c0: cmp             SP, x16
    //     0xadd6c4: b.ls            #0xadd704
    // 0xadd6c8: r0 = controller()
    //     0xadd6c8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd6cc: LoadField: r1 = r0->field_37
    //     0xadd6cc: ldur            w1, [x0, #0x37]
    // 0xadd6d0: DecompressPointer r1
    //     0xadd6d0: add             x1, x1, HEAP, lsl #32
    // 0xadd6d4: r16 = Instance_PaymentType
    //     0xadd6d4: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xadd6d8: ldr             x16, [x16, #0x5f0]
    // 0xadd6dc: cmp             w1, w16
    // 0xadd6e0: b.ne            #0xadd6f0
    // 0xadd6e4: r0 = "Qurban"
    //     0xadd6e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5e8] "Qurban"
    //     0xadd6e8: ldr             x0, [x0, #0x5e8]
    // 0xadd6ec: b               #0xadd6f8
    // 0xadd6f0: r0 = "Donasi"
    //     0xadd6f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5f0] "Donasi"
    //     0xadd6f4: ldr             x0, [x0, #0x5f0]
    // 0xadd6f8: LeaveFrame
    //     0xadd6f8: mov             SP, fp
    //     0xadd6fc: ldp             fp, lr, [SP], #0x10
    // 0xadd700: ret
    //     0xadd700: ret             
    // 0xadd704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd704: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd708: b               #0xadd6c8
  }
  [closure] CustomScrollView <anonymous closure>(dynamic, Campaign?) {
    // ** addr: 0xadd800, size: 0x159c
    // 0xadd800: EnterFrame
    //     0xadd800: stp             fp, lr, [SP, #-0x10]!
    //     0xadd804: mov             fp, SP
    // 0xadd808: AllocStack(0x80)
    //     0xadd808: sub             SP, SP, #0x80
    // 0xadd80c: SetupParameters()
    //     0xadd80c: ldr             x0, [fp, #0x18]
    //     0xadd810: ldur            w1, [x0, #0x17]
    //     0xadd814: add             x1, x1, HEAP, lsl #32
    //     0xadd818: stur            x1, [fp, #-8]
    // 0xadd81c: CheckStackOverflow
    //     0xadd81c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd820: cmp             SP, x16
    //     0xadd824: b.ls            #0xaded44
    // 0xadd828: r1 = 1
    //     0xadd828: movz            x1, #0x1
    // 0xadd82c: r0 = AllocateContext()
    //     0xadd82c: bl              #0xec126c  ; AllocateContextStub
    // 0xadd830: mov             x2, x0
    // 0xadd834: ldur            x0, [fp, #-8]
    // 0xadd838: stur            x2, [fp, #-0x10]
    // 0xadd83c: StoreField: r2->field_b = r0
    //     0xadd83c: stur            w0, [x2, #0xb]
    // 0xadd840: ldr             x1, [fp, #0x10]
    // 0xadd844: StoreField: r2->field_f = r1
    //     0xadd844: stur            w1, [x2, #0xf]
    // 0xadd848: LoadField: r1 = r0->field_f
    //     0xadd848: ldur            w1, [x0, #0xf]
    // 0xadd84c: DecompressPointer r1
    //     0xadd84c: add             x1, x1, HEAP, lsl #32
    // 0xadd850: r0 = controller()
    //     0xadd850: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd854: LoadField: r1 = r0->field_3f
    //     0xadd854: ldur            w1, [x0, #0x3f]
    // 0xadd858: DecompressPointer r1
    //     0xadd858: add             x1, x1, HEAP, lsl #32
    // 0xadd85c: stur            x1, [fp, #-0x18]
    // 0xadd860: r0 = Obx()
    //     0xadd860: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xadd864: ldur            x2, [fp, #-0x10]
    // 0xadd868: r1 = Function '<anonymous closure>':.
    //     0xadd868: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f5f8] AnonymousClosure: (0xadf858), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadd86c: ldr             x1, [x1, #0x5f8]
    // 0xadd870: stur            x0, [fp, #-0x20]
    // 0xadd874: r0 = AllocateClosure()
    //     0xadd874: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd878: mov             x1, x0
    // 0xadd87c: ldur            x0, [fp, #-0x20]
    // 0xadd880: StoreField: r0->field_b = r1
    //     0xadd880: stur            w1, [x0, #0xb]
    // 0xadd884: ldur            x2, [fp, #-0x10]
    // 0xadd888: LoadField: r1 = r2->field_f
    //     0xadd888: ldur            w1, [x2, #0xf]
    // 0xadd88c: DecompressPointer r1
    //     0xadd88c: add             x1, x1, HEAP, lsl #32
    // 0xadd890: cmp             w1, NULL
    // 0xadd894: b.eq            #0xaded4c
    // 0xadd898: LoadField: r3 = r1->field_1b
    //     0xadd898: ldur            w3, [x1, #0x1b]
    // 0xadd89c: DecompressPointer r3
    //     0xadd89c: add             x3, x3, HEAP, lsl #32
    // 0xadd8a0: stur            x3, [fp, #-0x28]
    // 0xadd8a4: r0 = NFadeInImageNetwork()
    //     0xadd8a4: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xadd8a8: mov             x1, x0
    // 0xadd8ac: ldur            x0, [fp, #-0x28]
    // 0xadd8b0: stur            x1, [fp, #-0x30]
    // 0xadd8b4: StoreField: r1->field_b = r0
    //     0xadd8b4: stur            w0, [x1, #0xb]
    // 0xadd8b8: r0 = "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xadd8b8: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a18] "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xadd8bc: ldr             x0, [x0, #0xa18]
    // 0xadd8c0: StoreField: r1->field_f = r0
    //     0xadd8c0: stur            w0, [x1, #0xf]
    // 0xadd8c4: r0 = "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xadd8c4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a20] "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xadd8c8: ldr             x0, [x0, #0xa20]
    // 0xadd8cc: StoreField: r1->field_13 = r0
    //     0xadd8cc: stur            w0, [x1, #0x13]
    // 0xadd8d0: r0 = Instance_BoxFit
    //     0xadd8d0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xadd8d4: ldr             x0, [x0, #0xa28]
    // 0xadd8d8: ArrayStore: r1[0] = r0  ; List_4
    //     0xadd8d8: stur            w0, [x1, #0x17]
    // 0xadd8dc: r0 = FlexibleSpaceBar()
    //     0xadd8dc: bl              #0xadf22c  ; AllocateFlexibleSpaceBarStub -> FlexibleSpaceBar (size=0x1c)
    // 0xadd8e0: mov             x3, x0
    // 0xadd8e4: ldur            x0, [fp, #-0x30]
    // 0xadd8e8: stur            x3, [fp, #-0x28]
    // 0xadd8ec: StoreField: r3->field_f = r0
    //     0xadd8ec: stur            w0, [x3, #0xf]
    // 0xadd8f0: r0 = Instance_CollapseMode
    //     0xadd8f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f600] Obj!CollapseMode@e369c1
    //     0xadd8f4: ldr             x0, [x0, #0x600]
    // 0xadd8f8: StoreField: r3->field_13 = r0
    //     0xadd8f8: stur            w0, [x3, #0x13]
    // 0xadd8fc: r0 = const [Instance of 'StretchMode']
    //     0xadd8fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f608] List<StretchMode>(1)
    //     0xadd900: ldr             x0, [x0, #0x608]
    // 0xadd904: ArrayStore: r3[0] = r0  ; List_4
    //     0xadd904: stur            w0, [x3, #0x17]
    // 0xadd908: r1 = <Widget>
    //     0xadd908: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadd90c: r2 = 0
    //     0xadd90c: movz            x2, #0
    // 0xadd910: r0 = _GrowableList()
    //     0xadd910: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xadd914: mov             x2, x0
    // 0xadd918: ldur            x0, [fp, #-8]
    // 0xadd91c: stur            x2, [fp, #-0x30]
    // 0xadd920: LoadField: r1 = r0->field_f
    //     0xadd920: ldur            w1, [x0, #0xf]
    // 0xadd924: DecompressPointer r1
    //     0xadd924: add             x1, x1, HEAP, lsl #32
    // 0xadd928: r0 = controller()
    //     0xadd928: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadd92c: LoadField: r1 = r0->field_37
    //     0xadd92c: ldur            w1, [x0, #0x37]
    // 0xadd930: DecompressPointer r1
    //     0xadd930: add             x1, x1, HEAP, lsl #32
    // 0xadd934: r16 = Instance_PaymentType
    //     0xadd934: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xadd938: ldr             x16, [x16, #0x5f0]
    // 0xadd93c: cmp             w1, w16
    // 0xadd940: b.eq            #0xadda0c
    // 0xadd944: ldur            x0, [fp, #-0x30]
    // 0xadd948: ldur            x2, [fp, #-0x10]
    // 0xadd94c: r1 = Function '<anonymous closure>':.
    //     0xadd94c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f610] AnonymousClosure: (0xadf238), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadd950: ldr             x1, [x1, #0x610]
    // 0xadd954: r0 = AllocateClosure()
    //     0xadd954: bl              #0xec1630  ; AllocateClosureStub
    // 0xadd958: stur            x0, [fp, #-0x38]
    // 0xadd95c: r0 = IconButton()
    //     0xadd95c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xadd960: mov             x2, x0
    // 0xadd964: ldur            x0, [fp, #-0x38]
    // 0xadd968: stur            x2, [fp, #-0x48]
    // 0xadd96c: StoreField: r2->field_3b = r0
    //     0xadd96c: stur            w0, [x2, #0x3b]
    // 0xadd970: r0 = false
    //     0xadd970: add             x0, NULL, #0x30  ; false
    // 0xadd974: StoreField: r2->field_47 = r0
    //     0xadd974: stur            w0, [x2, #0x47]
    // 0xadd978: r1 = Instance_Icon
    //     0xadd978: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aec8] Obj!Icon@e24531
    //     0xadd97c: ldr             x1, [x1, #0xec8]
    // 0xadd980: StoreField: r2->field_1f = r1
    //     0xadd980: stur            w1, [x2, #0x1f]
    // 0xadd984: r1 = Instance__IconButtonVariant
    //     0xadd984: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xadd988: ldr             x1, [x1, #0xf78]
    // 0xadd98c: StoreField: r2->field_63 = r1
    //     0xadd98c: stur            w1, [x2, #0x63]
    // 0xadd990: ldur            x3, [fp, #-0x30]
    // 0xadd994: LoadField: r1 = r3->field_b
    //     0xadd994: ldur            w1, [x3, #0xb]
    // 0xadd998: LoadField: r4 = r3->field_f
    //     0xadd998: ldur            w4, [x3, #0xf]
    // 0xadd99c: DecompressPointer r4
    //     0xadd99c: add             x4, x4, HEAP, lsl #32
    // 0xadd9a0: LoadField: r5 = r4->field_b
    //     0xadd9a0: ldur            w5, [x4, #0xb]
    // 0xadd9a4: r4 = LoadInt32Instr(r1)
    //     0xadd9a4: sbfx            x4, x1, #1, #0x1f
    // 0xadd9a8: stur            x4, [fp, #-0x40]
    // 0xadd9ac: r1 = LoadInt32Instr(r5)
    //     0xadd9ac: sbfx            x1, x5, #1, #0x1f
    // 0xadd9b0: cmp             x4, x1
    // 0xadd9b4: b.ne            #0xadd9c0
    // 0xadd9b8: mov             x1, x3
    // 0xadd9bc: r0 = _growToNextCapacity()
    //     0xadd9bc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd9c0: ldur            x2, [fp, #-0x30]
    // 0xadd9c4: ldur            x3, [fp, #-0x40]
    // 0xadd9c8: add             x0, x3, #1
    // 0xadd9cc: lsl             x1, x0, #1
    // 0xadd9d0: StoreField: r2->field_b = r1
    //     0xadd9d0: stur            w1, [x2, #0xb]
    // 0xadd9d4: LoadField: r1 = r2->field_f
    //     0xadd9d4: ldur            w1, [x2, #0xf]
    // 0xadd9d8: DecompressPointer r1
    //     0xadd9d8: add             x1, x1, HEAP, lsl #32
    // 0xadd9dc: ldur            x0, [fp, #-0x48]
    // 0xadd9e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadd9e0: add             x25, x1, x3, lsl #2
    //     0xadd9e4: add             x25, x25, #0xf
    //     0xadd9e8: str             w0, [x25]
    //     0xadd9ec: tbz             w0, #0, #0xadda08
    //     0xadd9f0: ldurb           w16, [x1, #-1]
    //     0xadd9f4: ldurb           w17, [x0, #-1]
    //     0xadd9f8: and             x16, x17, x16, lsr #2
    //     0xadd9fc: tst             x16, HEAP, lsr #32
    //     0xadda00: b.eq            #0xadda08
    //     0xadda04: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadda08: b               #0xadda10
    // 0xadda0c: ldur            x2, [fp, #-0x30]
    // 0xadda10: ldur            x0, [fp, #-8]
    // 0xadda14: ldur            x3, [fp, #-0x20]
    // 0xadda18: ldur            x1, [fp, #-0x28]
    // 0xadda1c: r0 = SliverAppBar()
    //     0xadda1c: bl              #0xadf220  ; AllocateSliverAppBarStub -> SliverAppBar (size=0xa0)
    // 0xadda20: mov             x3, x0
    // 0xadda24: r0 = true
    //     0xadda24: add             x0, NULL, #0x20  ; true
    // 0xadda28: stur            x3, [fp, #-0x38]
    // 0xadda2c: StoreField: r3->field_f = r0
    //     0xadda2c: stur            w0, [x3, #0xf]
    // 0xadda30: ldur            x1, [fp, #-0x20]
    // 0xadda34: StoreField: r3->field_13 = r1
    //     0xadda34: stur            w1, [x3, #0x13]
    // 0xadda38: ldur            x1, [fp, #-0x30]
    // 0xadda3c: ArrayStore: r3[0] = r1  ; List_4
    //     0xadda3c: stur            w1, [x3, #0x17]
    // 0xadda40: ldur            x1, [fp, #-0x28]
    // 0xadda44: StoreField: r3->field_1b = r1
    //     0xadda44: stur            w1, [x3, #0x1b]
    // 0xadda48: r4 = false
    //     0xadda48: add             x4, NULL, #0x30  ; false
    // 0xadda4c: StoreField: r3->field_33 = r4
    //     0xadda4c: stur            w4, [x3, #0x33]
    // 0xadda50: r1 = Instance_Color
    //     0xadda50: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xadda54: StoreField: r3->field_3b = r1
    //     0xadda54: stur            w1, [x3, #0x3b]
    // 0xadda58: StoreField: r3->field_47 = r0
    //     0xadda58: stur            w0, [x3, #0x47]
    // 0xadda5c: StoreField: r3->field_4f = r4
    //     0xadda5c: stur            w4, [x3, #0x4f]
    // 0xadda60: d0 = 56.000000
    //     0xadda60: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0xadda64: ldr             d0, [x17, #0xf60]
    // 0xadda68: StoreField: r3->field_57 = d0
    //     0xadda68: stur            d0, [x3, #0x57]
    // 0xadda6c: d1 = 180.000000
    //     0xadda6c: ldr             d1, [PP, #0x5a50]  ; [pp+0x5a50] IMM: double(180) from 0x4066800000000000
    // 0xadda70: StoreField: r3->field_5f = d1
    //     0xadda70: stur            d1, [x3, #0x5f]
    // 0xadda74: StoreField: r3->field_67 = r4
    //     0xadda74: stur            w4, [x3, #0x67]
    // 0xadda78: StoreField: r3->field_6b = r0
    //     0xadda78: stur            w0, [x3, #0x6b]
    // 0xadda7c: StoreField: r3->field_73 = r4
    //     0xadda7c: stur            w4, [x3, #0x73]
    // 0xadda80: StoreField: r3->field_77 = r4
    //     0xadda80: stur            w4, [x3, #0x77]
    // 0xadda84: StoreField: r3->field_7b = d0
    //     0xadda84: stur            d0, [x3, #0x7b]
    // 0xadda88: StoreField: r3->field_93 = r4
    //     0xadda88: stur            w4, [x3, #0x93]
    // 0xadda8c: r1 = Instance__SliverAppVariant
    //     0xadda8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f618] Obj!_SliverAppVariant@e36bc1
    //     0xadda90: ldr             x1, [x1, #0x618]
    // 0xadda94: StoreField: r3->field_9b = r1
    //     0xadda94: stur            w1, [x3, #0x9b]
    // 0xadda98: r1 = <Widget>
    //     0xadda98: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadda9c: r2 = 0
    //     0xadda9c: movz            x2, #0
    // 0xaddaa0: r0 = _GrowableList()
    //     0xaddaa0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaddaa4: mov             x2, x0
    // 0xaddaa8: ldur            x0, [fp, #-8]
    // 0xaddaac: stur            x2, [fp, #-0x20]
    // 0xaddab0: LoadField: r1 = r0->field_f
    //     0xaddab0: ldur            w1, [x0, #0xf]
    // 0xaddab4: DecompressPointer r1
    //     0xaddab4: add             x1, x1, HEAP, lsl #32
    // 0xaddab8: r0 = controller()
    //     0xaddab8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaddabc: LoadField: r1 = r0->field_37
    //     0xaddabc: ldur            w1, [x0, #0x37]
    // 0xaddac0: DecompressPointer r1
    //     0xaddac0: add             x1, x1, HEAP, lsl #32
    // 0xaddac4: r16 = Instance_PaymentType
    //     0xaddac4: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xaddac8: ldr             x16, [x16, #0x5f0]
    // 0xaddacc: cmp             w1, w16
    // 0xaddad0: b.eq            #0xaddba4
    // 0xaddad4: ldur            x0, [fp, #-8]
    // 0xaddad8: LoadField: r1 = r0->field_f
    //     0xaddad8: ldur            w1, [x0, #0xf]
    // 0xaddadc: DecompressPointer r1
    //     0xaddadc: add             x1, x1, HEAP, lsl #32
    // 0xaddae0: r0 = controller()
    //     0xaddae0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaddae4: LoadField: r1 = r0->field_37
    //     0xaddae4: ldur            w1, [x0, #0x37]
    // 0xaddae8: DecompressPointer r1
    //     0xaddae8: add             x1, x1, HEAP, lsl #32
    // 0xaddaec: r16 = Instance_PaymentType
    //     0xaddaec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xaddaf0: ldr             x16, [x16, #0x608]
    // 0xaddaf4: cmp             w1, w16
    // 0xaddaf8: b.eq            #0xaddba4
    // 0xaddafc: ldur            x2, [fp, #-0x10]
    // 0xaddb00: LoadField: r0 = r2->field_f
    //     0xaddb00: ldur            w0, [x2, #0xf]
    // 0xaddb04: DecompressPointer r0
    //     0xaddb04: add             x0, x0, HEAP, lsl #32
    // 0xaddb08: cmp             w0, NULL
    // 0xaddb0c: b.eq            #0xaded50
    // 0xaddb10: LoadField: r1 = r0->field_37
    //     0xaddb10: ldur            w1, [x0, #0x37]
    // 0xaddb14: DecompressPointer r1
    //     0xaddb14: add             x1, x1, HEAP, lsl #32
    // 0xaddb18: cmp             w1, NULL
    // 0xaddb1c: b.ne            #0xaddb28
    // 0xaddb20: r0 = Null
    //     0xaddb20: mov             x0, NULL
    // 0xaddb24: b               #0xaddb30
    // 0xaddb28: LoadField: r0 = r1->field_f
    //     0xaddb28: ldur            w0, [x1, #0xf]
    // 0xaddb2c: DecompressPointer r0
    //     0xaddb2c: add             x0, x0, HEAP, lsl #32
    // 0xaddb30: cmp             w0, NULL
    // 0xaddb34: b.ne            #0xaddb3c
    // 0xaddb38: r0 = ""
    //     0xaddb38: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaddb3c: stur            x0, [fp, #-0x28]
    // 0xaddb40: r0 = NLabelButton()
    //     0xaddb40: bl              #0xadf214  ; AllocateNLabelButtonStub -> NLabelButton (size=0x24)
    // 0xaddb44: mov             x3, x0
    // 0xaddb48: ldur            x0, [fp, #-0x28]
    // 0xaddb4c: stur            x3, [fp, #-0x30]
    // 0xaddb50: StoreField: r3->field_b = r0
    //     0xaddb50: stur            w0, [x3, #0xb]
    // 0xaddb54: r1 = Null
    //     0xaddb54: mov             x1, NULL
    // 0xaddb58: r2 = 4
    //     0xaddb58: movz            x2, #0x4
    // 0xaddb5c: r0 = AllocateArray()
    //     0xaddb5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaddb60: mov             x2, x0
    // 0xaddb64: ldur            x0, [fp, #-0x30]
    // 0xaddb68: stur            x2, [fp, #-0x28]
    // 0xaddb6c: StoreField: r2->field_f = r0
    //     0xaddb6c: stur            w0, [x2, #0xf]
    // 0xaddb70: r16 = Instance_SizedBox
    //     0xaddb70: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xaddb74: ldr             x16, [x16, #0xfb0]
    // 0xaddb78: StoreField: r2->field_13 = r16
    //     0xaddb78: stur            w16, [x2, #0x13]
    // 0xaddb7c: r1 = <Widget>
    //     0xaddb7c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaddb80: r0 = AllocateGrowableArray()
    //     0xaddb80: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaddb84: mov             x1, x0
    // 0xaddb88: ldur            x0, [fp, #-0x28]
    // 0xaddb8c: StoreField: r1->field_f = r0
    //     0xaddb8c: stur            w0, [x1, #0xf]
    // 0xaddb90: r0 = 4
    //     0xaddb90: movz            x0, #0x4
    // 0xaddb94: StoreField: r1->field_b = r0
    //     0xaddb94: stur            w0, [x1, #0xb]
    // 0xaddb98: mov             x2, x1
    // 0xaddb9c: ldur            x1, [fp, #-0x20]
    // 0xaddba0: r0 = addAll()
    //     0xaddba0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaddba4: ldur            x0, [fp, #-8]
    // 0xaddba8: LoadField: r1 = r0->field_f
    //     0xaddba8: ldur            w1, [x0, #0xf]
    // 0xaddbac: DecompressPointer r1
    //     0xaddbac: add             x1, x1, HEAP, lsl #32
    // 0xaddbb0: r0 = controller()
    //     0xaddbb0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaddbb4: LoadField: r1 = r0->field_37
    //     0xaddbb4: ldur            w1, [x0, #0x37]
    // 0xaddbb8: DecompressPointer r1
    //     0xaddbb8: add             x1, x1, HEAP, lsl #32
    // 0xaddbbc: r16 = Instance_PaymentType
    //     0xaddbbc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xaddbc0: ldr             x16, [x16, #0x608]
    // 0xaddbc4: cmp             w1, w16
    // 0xaddbc8: b.ne            #0xaddc6c
    // 0xaddbcc: ldur            x2, [fp, #-0x10]
    // 0xaddbd0: LoadField: r0 = r2->field_f
    //     0xaddbd0: ldur            w0, [x2, #0xf]
    // 0xaddbd4: DecompressPointer r0
    //     0xaddbd4: add             x0, x0, HEAP, lsl #32
    // 0xaddbd8: cmp             w0, NULL
    // 0xaddbdc: b.eq            #0xaded54
    // 0xaddbe0: LoadField: r1 = r0->field_3b
    //     0xaddbe0: ldur            w1, [x0, #0x3b]
    // 0xaddbe4: DecompressPointer r1
    //     0xaddbe4: add             x1, x1, HEAP, lsl #32
    // 0xaddbe8: LoadField: r0 = r1->field_13
    //     0xaddbe8: ldur            w0, [x1, #0x13]
    // 0xaddbec: DecompressPointer r0
    //     0xaddbec: add             x0, x0, HEAP, lsl #32
    // 0xaddbf0: stur            x0, [fp, #-0x30]
    // 0xaddbf4: LoadField: r3 = r1->field_f
    //     0xaddbf4: ldur            w3, [x1, #0xf]
    // 0xaddbf8: DecompressPointer r3
    //     0xaddbf8: add             x3, x3, HEAP, lsl #32
    // 0xaddbfc: stur            x3, [fp, #-0x28]
    // 0xaddc00: r0 = NOrganizationListTile()
    //     0xaddc00: bl              #0xadf208  ; AllocateNOrganizationListTileStub -> NOrganizationListTile (size=0x14)
    // 0xaddc04: mov             x3, x0
    // 0xaddc08: ldur            x0, [fp, #-0x30]
    // 0xaddc0c: stur            x3, [fp, #-0x48]
    // 0xaddc10: StoreField: r3->field_b = r0
    //     0xaddc10: stur            w0, [x3, #0xb]
    // 0xaddc14: ldur            x0, [fp, #-0x28]
    // 0xaddc18: StoreField: r3->field_f = r0
    //     0xaddc18: stur            w0, [x3, #0xf]
    // 0xaddc1c: r1 = Null
    //     0xaddc1c: mov             x1, NULL
    // 0xaddc20: r2 = 4
    //     0xaddc20: movz            x2, #0x4
    // 0xaddc24: r0 = AllocateArray()
    //     0xaddc24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaddc28: mov             x2, x0
    // 0xaddc2c: ldur            x0, [fp, #-0x48]
    // 0xaddc30: stur            x2, [fp, #-0x28]
    // 0xaddc34: StoreField: r2->field_f = r0
    //     0xaddc34: stur            w0, [x2, #0xf]
    // 0xaddc38: r16 = Instance_SizedBox
    //     0xaddc38: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaddc3c: ldr             x16, [x16, #0x950]
    // 0xaddc40: StoreField: r2->field_13 = r16
    //     0xaddc40: stur            w16, [x2, #0x13]
    // 0xaddc44: r1 = <Widget>
    //     0xaddc44: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaddc48: r0 = AllocateGrowableArray()
    //     0xaddc48: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaddc4c: mov             x1, x0
    // 0xaddc50: ldur            x0, [fp, #-0x28]
    // 0xaddc54: StoreField: r1->field_f = r0
    //     0xaddc54: stur            w0, [x1, #0xf]
    // 0xaddc58: r0 = 4
    //     0xaddc58: movz            x0, #0x4
    // 0xaddc5c: StoreField: r1->field_b = r0
    //     0xaddc5c: stur            w0, [x1, #0xb]
    // 0xaddc60: mov             x2, x1
    // 0xaddc64: ldur            x1, [fp, #-0x20]
    // 0xaddc68: r0 = addAll()
    //     0xaddc68: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaddc6c: ldur            x2, [fp, #-0x10]
    // 0xaddc70: ldur            x1, [fp, #-0x20]
    // 0xaddc74: LoadField: r0 = r2->field_f
    //     0xaddc74: ldur            w0, [x2, #0xf]
    // 0xaddc78: DecompressPointer r0
    //     0xaddc78: add             x0, x0, HEAP, lsl #32
    // 0xaddc7c: cmp             w0, NULL
    // 0xaddc80: b.eq            #0xaded58
    // 0xaddc84: LoadField: r3 = r0->field_f
    //     0xaddc84: ldur            w3, [x0, #0xf]
    // 0xaddc88: DecompressPointer r3
    //     0xaddc88: add             x3, x3, HEAP, lsl #32
    // 0xaddc8c: stur            x3, [fp, #-0x28]
    // 0xaddc90: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaddc90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaddc94: ldr             x0, [x0, #0x2670]
    //     0xaddc98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaddc9c: cmp             w0, w16
    //     0xaddca0: b.ne            #0xaddcac
    //     0xaddca4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaddca8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaddcac: r0 = GetNavigation.textTheme()
    //     0xaddcac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaddcb0: LoadField: r1 = r0->field_f
    //     0xaddcb0: ldur            w1, [x0, #0xf]
    // 0xaddcb4: DecompressPointer r1
    //     0xaddcb4: add             x1, x1, HEAP, lsl #32
    // 0xaddcb8: stur            x1, [fp, #-0x30]
    // 0xaddcbc: r0 = Text()
    //     0xaddcbc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaddcc0: mov             x2, x0
    // 0xaddcc4: ldur            x0, [fp, #-0x28]
    // 0xaddcc8: stur            x2, [fp, #-0x48]
    // 0xaddccc: StoreField: r2->field_b = r0
    //     0xaddccc: stur            w0, [x2, #0xb]
    // 0xaddcd0: ldur            x0, [fp, #-0x30]
    // 0xaddcd4: StoreField: r2->field_13 = r0
    //     0xaddcd4: stur            w0, [x2, #0x13]
    // 0xaddcd8: ldur            x0, [fp, #-0x20]
    // 0xaddcdc: LoadField: r1 = r0->field_b
    //     0xaddcdc: ldur            w1, [x0, #0xb]
    // 0xaddce0: LoadField: r3 = r0->field_f
    //     0xaddce0: ldur            w3, [x0, #0xf]
    // 0xaddce4: DecompressPointer r3
    //     0xaddce4: add             x3, x3, HEAP, lsl #32
    // 0xaddce8: LoadField: r4 = r3->field_b
    //     0xaddce8: ldur            w4, [x3, #0xb]
    // 0xaddcec: r3 = LoadInt32Instr(r1)
    //     0xaddcec: sbfx            x3, x1, #1, #0x1f
    // 0xaddcf0: stur            x3, [fp, #-0x40]
    // 0xaddcf4: r1 = LoadInt32Instr(r4)
    //     0xaddcf4: sbfx            x1, x4, #1, #0x1f
    // 0xaddcf8: cmp             x3, x1
    // 0xaddcfc: b.ne            #0xaddd08
    // 0xaddd00: mov             x1, x0
    // 0xaddd04: r0 = _growToNextCapacity()
    //     0xaddd04: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaddd08: ldur            x4, [fp, #-8]
    // 0xaddd0c: ldur            x2, [fp, #-0x20]
    // 0xaddd10: ldur            x3, [fp, #-0x40]
    // 0xaddd14: add             x0, x3, #1
    // 0xaddd18: lsl             x1, x0, #1
    // 0xaddd1c: StoreField: r2->field_b = r1
    //     0xaddd1c: stur            w1, [x2, #0xb]
    // 0xaddd20: LoadField: r1 = r2->field_f
    //     0xaddd20: ldur            w1, [x2, #0xf]
    // 0xaddd24: DecompressPointer r1
    //     0xaddd24: add             x1, x1, HEAP, lsl #32
    // 0xaddd28: ldur            x0, [fp, #-0x48]
    // 0xaddd2c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaddd2c: add             x25, x1, x3, lsl #2
    //     0xaddd30: add             x25, x25, #0xf
    //     0xaddd34: str             w0, [x25]
    //     0xaddd38: tbz             w0, #0, #0xaddd54
    //     0xaddd3c: ldurb           w16, [x1, #-1]
    //     0xaddd40: ldurb           w17, [x0, #-1]
    //     0xaddd44: and             x16, x17, x16, lsr #2
    //     0xaddd48: tst             x16, HEAP, lsr #32
    //     0xaddd4c: b.eq            #0xaddd54
    //     0xaddd50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaddd54: LoadField: r1 = r4->field_f
    //     0xaddd54: ldur            w1, [x4, #0xf]
    // 0xaddd58: DecompressPointer r1
    //     0xaddd58: add             x1, x1, HEAP, lsl #32
    // 0xaddd5c: r0 = controller()
    //     0xaddd5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaddd60: LoadField: r1 = r0->field_37
    //     0xaddd60: ldur            w1, [x0, #0x37]
    // 0xaddd64: DecompressPointer r1
    //     0xaddd64: add             x1, x1, HEAP, lsl #32
    // 0xaddd68: r16 = Instance_PaymentType
    //     0xaddd68: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xaddd6c: ldr             x16, [x16, #0x5f0]
    // 0xaddd70: cmp             w1, w16
    // 0xaddd74: b.ne            #0xaddf7c
    // 0xaddd78: ldur            x0, [fp, #-0x10]
    // 0xaddd7c: LoadField: r1 = r0->field_f
    //     0xaddd7c: ldur            w1, [x0, #0xf]
    // 0xaddd80: DecompressPointer r1
    //     0xaddd80: add             x1, x1, HEAP, lsl #32
    // 0xaddd84: cmp             w1, NULL
    // 0xaddd88: b.eq            #0xaded5c
    // 0xaddd8c: LoadField: r3 = r1->field_3f
    //     0xaddd8c: ldur            w3, [x1, #0x3f]
    // 0xaddd90: DecompressPointer r3
    //     0xaddd90: add             x3, x3, HEAP, lsl #32
    // 0xaddd94: stur            x3, [fp, #-0x28]
    // 0xaddd98: r1 = Function '<anonymous closure>':.
    //     0xaddd98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f620] AnonymousClosure: (0xadf834), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xaddd9c: ldr             x1, [x1, #0x620]
    // 0xaddda0: r2 = Null
    //     0xaddda0: mov             x2, NULL
    // 0xaddda4: r0 = AllocateClosure()
    //     0xaddda4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaddda8: r1 = Function '<anonymous closure>':.
    //     0xaddda8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f628] AnonymousClosure: static (0xe62184), in [package:flutter_svg/src/utilities/xml.dart] ::getAttribute (0xac2c14)
    //     0xadddac: ldr             x1, [x1, #0x628]
    // 0xadddb0: r2 = Null
    //     0xadddb0: mov             x2, NULL
    // 0xadddb4: stur            x0, [fp, #-0x30]
    // 0xadddb8: r0 = AllocateClosure()
    //     0xadddb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xadddbc: mov             x1, x0
    // 0xadddc0: ldur            x0, [fp, #-0x28]
    // 0xadddc4: r2 = LoadClassIdInstr(r0)
    //     0xadddc4: ldur            x2, [x0, #-1]
    //     0xadddc8: ubfx            x2, x2, #0xc, #0x14
    // 0xadddcc: r16 = <String>
    //     0xadddcc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xadddd0: stp             x0, x16, [SP, #0x10]
    // 0xadddd4: ldur            x16, [fp, #-0x30]
    // 0xadddd8: stp             x16, x1, [SP]
    // 0xaddddc: mov             x0, x2
    // 0xaddde0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xaddde0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xaddde4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xaddde4: sub             lr, x0, #0xfff
    //     0xaddde8: ldr             lr, [x21, lr, lsl #3]
    //     0xadddec: blr             lr
    // 0xadddf0: stur            x0, [fp, #-0x28]
    // 0xadddf4: r0 = GetNavigation.textTheme()
    //     0xadddf4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xadddf8: LoadField: r1 = r0->field_27
    //     0xadddf8: ldur            w1, [x0, #0x27]
    // 0xadddfc: DecompressPointer r1
    //     0xadddfc: add             x1, x1, HEAP, lsl #32
    // 0xadde00: stur            x1, [fp, #-0x30]
    // 0xadde04: r0 = Text()
    //     0xadde04: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadde08: mov             x3, x0
    // 0xadde0c: ldur            x0, [fp, #-0x28]
    // 0xadde10: stur            x3, [fp, #-0x48]
    // 0xadde14: StoreField: r3->field_b = r0
    //     0xadde14: stur            w0, [x3, #0xb]
    // 0xadde18: ldur            x0, [fp, #-0x30]
    // 0xadde1c: StoreField: r3->field_13 = r0
    //     0xadde1c: stur            w0, [x3, #0x13]
    // 0xadde20: ldur            x0, [fp, #-0x10]
    // 0xadde24: LoadField: r1 = r0->field_f
    //     0xadde24: ldur            w1, [x0, #0xf]
    // 0xadde28: DecompressPointer r1
    //     0xadde28: add             x1, x1, HEAP, lsl #32
    // 0xadde2c: cmp             w1, NULL
    // 0xadde30: b.eq            #0xaded60
    // 0xadde34: LoadField: r4 = r1->field_3f
    //     0xadde34: ldur            w4, [x1, #0x3f]
    // 0xadde38: DecompressPointer r4
    //     0xadde38: add             x4, x4, HEAP, lsl #32
    // 0xadde3c: stur            x4, [fp, #-0x28]
    // 0xadde40: r1 = Function '<anonymous closure>':.
    //     0xadde40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f630] AnonymousClosure: (0xadf790), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadde44: ldr             x1, [x1, #0x630]
    // 0xadde48: r2 = Null
    //     0xadde48: mov             x2, NULL
    // 0xadde4c: r0 = AllocateClosure()
    //     0xadde4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xadde50: r1 = Function '<anonymous closure>':.
    //     0xadde50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f638] AnonymousClosure: static (0xe62184), in [package:flutter_svg/src/utilities/xml.dart] ::getAttribute (0xac2c14)
    //     0xadde54: ldr             x1, [x1, #0x638]
    // 0xadde58: r2 = Null
    //     0xadde58: mov             x2, NULL
    // 0xadde5c: stur            x0, [fp, #-0x30]
    // 0xadde60: r0 = AllocateClosure()
    //     0xadde60: bl              #0xec1630  ; AllocateClosureStub
    // 0xadde64: mov             x1, x0
    // 0xadde68: ldur            x0, [fp, #-0x28]
    // 0xadde6c: r2 = LoadClassIdInstr(r0)
    //     0xadde6c: ldur            x2, [x0, #-1]
    //     0xadde70: ubfx            x2, x2, #0xc, #0x14
    // 0xadde74: r16 = <String>
    //     0xadde74: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xadde78: stp             x0, x16, [SP, #0x10]
    // 0xadde7c: ldur            x16, [fp, #-0x30]
    // 0xadde80: stp             x16, x1, [SP]
    // 0xadde84: mov             x0, x2
    // 0xadde88: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xadde88: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xadde8c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xadde8c: sub             lr, x0, #0xfff
    //     0xadde90: ldr             lr, [x21, lr, lsl #3]
    //     0xadde94: blr             lr
    // 0xadde98: stur            x0, [fp, #-0x28]
    // 0xadde9c: r0 = GetNavigation.textTheme()
    //     0xadde9c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaddea0: LoadField: r1 = r0->field_f
    //     0xaddea0: ldur            w1, [x0, #0xf]
    // 0xaddea4: DecompressPointer r1
    //     0xaddea4: add             x1, x1, HEAP, lsl #32
    // 0xaddea8: stur            x1, [fp, #-0x30]
    // 0xaddeac: cmp             w1, NULL
    // 0xaddeb0: b.ne            #0xaddebc
    // 0xaddeb4: r2 = Null
    //     0xaddeb4: mov             x2, NULL
    // 0xaddeb8: b               #0xaddef0
    // 0xaddebc: r0 = GetNavigation.theme()
    //     0xaddebc: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaddec0: LoadField: r1 = r0->field_3f
    //     0xaddec0: ldur            w1, [x0, #0x3f]
    // 0xaddec4: DecompressPointer r1
    //     0xaddec4: add             x1, x1, HEAP, lsl #32
    // 0xaddec8: LoadField: r0 = r1->field_b
    //     0xaddec8: ldur            w0, [x1, #0xb]
    // 0xaddecc: DecompressPointer r0
    //     0xaddecc: add             x0, x0, HEAP, lsl #32
    // 0xadded0: r16 = 18.000000
    //     0xadded0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xadded4: ldr             x16, [x16, #0x958]
    // 0xadded8: stp             x0, x16, [SP]
    // 0xaddedc: ldur            x1, [fp, #-0x30]
    // 0xaddee0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaddee0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaddee4: ldr             x4, [x4, #0xaa0]
    // 0xaddee8: r0 = copyWith()
    //     0xaddee8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaddeec: mov             x2, x0
    // 0xaddef0: ldur            x1, [fp, #-0x48]
    // 0xaddef4: ldur            x0, [fp, #-0x28]
    // 0xaddef8: stur            x2, [fp, #-0x30]
    // 0xaddefc: r0 = Text()
    //     0xaddefc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaddf00: mov             x3, x0
    // 0xaddf04: ldur            x0, [fp, #-0x28]
    // 0xaddf08: stur            x3, [fp, #-0x50]
    // 0xaddf0c: StoreField: r3->field_b = r0
    //     0xaddf0c: stur            w0, [x3, #0xb]
    // 0xaddf10: ldur            x0, [fp, #-0x30]
    // 0xaddf14: StoreField: r3->field_13 = r0
    //     0xaddf14: stur            w0, [x3, #0x13]
    // 0xaddf18: r1 = Null
    //     0xaddf18: mov             x1, NULL
    // 0xaddf1c: r2 = 8
    //     0xaddf1c: movz            x2, #0x8
    // 0xaddf20: r0 = AllocateArray()
    //     0xaddf20: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaddf24: stur            x0, [fp, #-0x28]
    // 0xaddf28: r16 = Instance_SizedBox
    //     0xaddf28: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xaddf2c: ldr             x16, [x16, #0x4a0]
    // 0xaddf30: StoreField: r0->field_f = r16
    //     0xaddf30: stur            w16, [x0, #0xf]
    // 0xaddf34: ldur            x1, [fp, #-0x48]
    // 0xaddf38: StoreField: r0->field_13 = r1
    //     0xaddf38: stur            w1, [x0, #0x13]
    // 0xaddf3c: r16 = Instance_SizedBox
    //     0xaddf3c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xaddf40: ldr             x16, [x16, #0xfb0]
    // 0xaddf44: ArrayStore: r0[0] = r16  ; List_4
    //     0xaddf44: stur            w16, [x0, #0x17]
    // 0xaddf48: ldur            x1, [fp, #-0x50]
    // 0xaddf4c: StoreField: r0->field_1b = r1
    //     0xaddf4c: stur            w1, [x0, #0x1b]
    // 0xaddf50: r1 = <Widget>
    //     0xaddf50: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaddf54: r0 = AllocateGrowableArray()
    //     0xaddf54: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaddf58: mov             x1, x0
    // 0xaddf5c: ldur            x0, [fp, #-0x28]
    // 0xaddf60: StoreField: r1->field_f = r0
    //     0xaddf60: stur            w0, [x1, #0xf]
    // 0xaddf64: r0 = 8
    //     0xaddf64: movz            x0, #0x8
    // 0xaddf68: StoreField: r1->field_b = r0
    //     0xaddf68: stur            w0, [x1, #0xb]
    // 0xaddf6c: mov             x2, x1
    // 0xaddf70: ldur            x1, [fp, #-0x20]
    // 0xaddf74: r0 = addAll()
    //     0xaddf74: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaddf78: b               #0xade17c
    // 0xaddf7c: ldur            x0, [fp, #-8]
    // 0xaddf80: LoadField: r1 = r0->field_f
    //     0xaddf80: ldur            w1, [x0, #0xf]
    // 0xaddf84: DecompressPointer r1
    //     0xaddf84: add             x1, x1, HEAP, lsl #32
    // 0xaddf88: r0 = controller()
    //     0xaddf88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaddf8c: LoadField: r1 = r0->field_37
    //     0xaddf8c: ldur            w1, [x0, #0x37]
    // 0xaddf90: DecompressPointer r1
    //     0xaddf90: add             x1, x1, HEAP, lsl #32
    // 0xaddf94: r16 = Instance_PaymentType
    //     0xaddf94: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xaddf98: ldr             x16, [x16, #0x608]
    // 0xaddf9c: cmp             w1, w16
    // 0xaddfa0: b.eq            #0xade17c
    // 0xaddfa4: ldur            x0, [fp, #-8]
    // 0xaddfa8: ldur            x2, [fp, #-0x10]
    // 0xaddfac: LoadField: r1 = r2->field_f
    //     0xaddfac: ldur            w1, [x2, #0xf]
    // 0xaddfb0: DecompressPointer r1
    //     0xaddfb0: add             x1, x1, HEAP, lsl #32
    // 0xaddfb4: cmp             w1, NULL
    // 0xaddfb8: b.eq            #0xaded64
    // 0xaddfbc: LoadField: r3 = r1->field_3b
    //     0xaddfbc: ldur            w3, [x1, #0x3b]
    // 0xaddfc0: DecompressPointer r3
    //     0xaddfc0: add             x3, x3, HEAP, lsl #32
    // 0xaddfc4: LoadField: r1 = r3->field_13
    //     0xaddfc4: ldur            w1, [x3, #0x13]
    // 0xaddfc8: DecompressPointer r1
    //     0xaddfc8: add             x1, x1, HEAP, lsl #32
    // 0xaddfcc: stur            x1, [fp, #-0x30]
    // 0xaddfd0: LoadField: r4 = r3->field_f
    //     0xaddfd0: ldur            w4, [x3, #0xf]
    // 0xaddfd4: DecompressPointer r4
    //     0xaddfd4: add             x4, x4, HEAP, lsl #32
    // 0xaddfd8: stur            x4, [fp, #-0x28]
    // 0xaddfdc: r0 = NOrganizationListTile()
    //     0xaddfdc: bl              #0xadf208  ; AllocateNOrganizationListTileStub -> NOrganizationListTile (size=0x14)
    // 0xaddfe0: mov             x3, x0
    // 0xaddfe4: ldur            x0, [fp, #-0x30]
    // 0xaddfe8: stur            x3, [fp, #-0x48]
    // 0xaddfec: StoreField: r3->field_b = r0
    //     0xaddfec: stur            w0, [x3, #0xb]
    // 0xaddff0: ldur            x0, [fp, #-0x28]
    // 0xaddff4: StoreField: r3->field_f = r0
    //     0xaddff4: stur            w0, [x3, #0xf]
    // 0xaddff8: r1 = Null
    //     0xaddff8: mov             x1, NULL
    // 0xaddffc: r2 = 4
    //     0xaddffc: movz            x2, #0x4
    // 0xade000: r0 = AllocateArray()
    //     0xade000: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade004: stur            x0, [fp, #-0x28]
    // 0xade008: r16 = Instance_SizedBox
    //     0xade008: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xade00c: ldr             x16, [x16, #0xfe8]
    // 0xade010: StoreField: r0->field_f = r16
    //     0xade010: stur            w16, [x0, #0xf]
    // 0xade014: ldur            x1, [fp, #-0x48]
    // 0xade018: StoreField: r0->field_13 = r1
    //     0xade018: stur            w1, [x0, #0x13]
    // 0xade01c: r1 = <Widget>
    //     0xade01c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xade020: r0 = AllocateGrowableArray()
    //     0xade020: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xade024: mov             x2, x0
    // 0xade028: ldur            x0, [fp, #-0x28]
    // 0xade02c: stur            x2, [fp, #-0x30]
    // 0xade030: StoreField: r2->field_f = r0
    //     0xade030: stur            w0, [x2, #0xf]
    // 0xade034: r0 = 4
    //     0xade034: movz            x0, #0x4
    // 0xade038: StoreField: r2->field_b = r0
    //     0xade038: stur            w0, [x2, #0xb]
    // 0xade03c: ldur            x3, [fp, #-8]
    // 0xade040: LoadField: r1 = r3->field_f
    //     0xade040: ldur            w1, [x3, #0xf]
    // 0xade044: DecompressPointer r1
    //     0xade044: add             x1, x1, HEAP, lsl #32
    // 0xade048: r0 = controller()
    //     0xade048: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade04c: LoadField: r1 = r0->field_37
    //     0xade04c: ldur            w1, [x0, #0x37]
    // 0xade050: DecompressPointer r1
    //     0xade050: add             x1, x1, HEAP, lsl #32
    // 0xade054: r16 = Instance_PaymentType
    //     0xade054: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xade058: ldr             x16, [x16, #0x608]
    // 0xade05c: cmp             w1, w16
    // 0xade060: b.eq            #0xade170
    // 0xade064: ldur            x2, [fp, #-0x10]
    // 0xade068: LoadField: r1 = r2->field_f
    //     0xade068: ldur            w1, [x2, #0xf]
    // 0xade06c: DecompressPointer r1
    //     0xade06c: add             x1, x1, HEAP, lsl #32
    // 0xade070: cmp             w1, NULL
    // 0xade074: b.eq            #0xaded68
    // 0xade078: r0 = amountRaised()
    //     0xade078: bl              #0xadf16c  ; [package:nuonline/app/data/models/campaign.dart] Campaign::amountRaised
    // 0xade07c: ldur            x2, [fp, #-0x10]
    // 0xade080: stur            x0, [fp, #-0x28]
    // 0xade084: LoadField: r1 = r2->field_f
    //     0xade084: ldur            w1, [x2, #0xf]
    // 0xade088: DecompressPointer r1
    //     0xade088: add             x1, x1, HEAP, lsl #32
    // 0xade08c: cmp             w1, NULL
    // 0xade090: b.eq            #0xaded6c
    // 0xade094: r0 = targetAmount()
    //     0xade094: bl              #0xadefcc  ; [package:nuonline/app/data/models/campaign.dart] Campaign::targetAmount
    // 0xade098: ldur            x2, [fp, #-0x10]
    // 0xade09c: stur            x0, [fp, #-0x48]
    // 0xade0a0: LoadField: r1 = r2->field_f
    //     0xade0a0: ldur            w1, [x2, #0xf]
    // 0xade0a4: DecompressPointer r1
    //     0xade0a4: add             x1, x1, HEAP, lsl #32
    // 0xade0a8: cmp             w1, NULL
    // 0xade0ac: b.eq            #0xaded70
    // 0xade0b0: r0 = remainingDay()
    //     0xade0b0: bl              #0xadeef0  ; [package:nuonline/app/data/models/campaign.dart] Campaign::remainingDay
    // 0xade0b4: ldur            x2, [fp, #-0x10]
    // 0xade0b8: stur            x0, [fp, #-0x50]
    // 0xade0bc: LoadField: r1 = r2->field_f
    //     0xade0bc: ldur            w1, [x2, #0xf]
    // 0xade0c0: DecompressPointer r1
    //     0xade0c0: add             x1, x1, HEAP, lsl #32
    // 0xade0c4: cmp             w1, NULL
    // 0xade0c8: b.eq            #0xaded74
    // 0xade0cc: r0 = progress()
    //     0xade0cc: bl              #0xadee8c  ; [package:nuonline/app/data/models/campaign.dart] Campaign::progress
    // 0xade0d0: ldur            x2, [fp, #-0x10]
    // 0xade0d4: stur            d0, [fp, #-0x60]
    // 0xade0d8: LoadField: r0 = r2->field_f
    //     0xade0d8: ldur            w0, [x2, #0xf]
    // 0xade0dc: DecompressPointer r0
    //     0xade0dc: add             x0, x0, HEAP, lsl #32
    // 0xade0e0: cmp             w0, NULL
    // 0xade0e4: b.eq            #0xaded78
    // 0xade0e8: LoadField: r1 = r0->field_2b
    //     0xade0e8: ldur            x1, [x0, #0x2b]
    // 0xade0ec: stur            x1, [fp, #-0x40]
    // 0xade0f0: r0 = NCampaignProgressDetail()
    //     0xade0f0: bl              #0xadee80  ; AllocateNCampaignProgressDetailStub -> NCampaignProgressDetail (size=0x28)
    // 0xade0f4: mov             x3, x0
    // 0xade0f8: ldur            x0, [fp, #-0x28]
    // 0xade0fc: stur            x3, [fp, #-0x58]
    // 0xade100: StoreField: r3->field_b = r0
    //     0xade100: stur            w0, [x3, #0xb]
    // 0xade104: ldur            x0, [fp, #-0x48]
    // 0xade108: StoreField: r3->field_f = r0
    //     0xade108: stur            w0, [x3, #0xf]
    // 0xade10c: ldur            x0, [fp, #-0x50]
    // 0xade110: StoreField: r3->field_13 = r0
    //     0xade110: stur            w0, [x3, #0x13]
    // 0xade114: ldur            d0, [fp, #-0x60]
    // 0xade118: ArrayStore: r3[0] = d0  ; List_8
    //     0xade118: stur            d0, [x3, #0x17]
    // 0xade11c: ldur            x0, [fp, #-0x40]
    // 0xade120: StoreField: r3->field_1f = r0
    //     0xade120: stur            x0, [x3, #0x1f]
    // 0xade124: r1 = Null
    //     0xade124: mov             x1, NULL
    // 0xade128: r2 = 4
    //     0xade128: movz            x2, #0x4
    // 0xade12c: r0 = AllocateArray()
    //     0xade12c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade130: stur            x0, [fp, #-0x28]
    // 0xade134: r16 = Instance_SizedBox
    //     0xade134: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xade138: ldr             x16, [x16, #0xfe8]
    // 0xade13c: StoreField: r0->field_f = r16
    //     0xade13c: stur            w16, [x0, #0xf]
    // 0xade140: ldur            x1, [fp, #-0x58]
    // 0xade144: StoreField: r0->field_13 = r1
    //     0xade144: stur            w1, [x0, #0x13]
    // 0xade148: r1 = <Widget>
    //     0xade148: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xade14c: r0 = AllocateGrowableArray()
    //     0xade14c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xade150: mov             x1, x0
    // 0xade154: ldur            x0, [fp, #-0x28]
    // 0xade158: StoreField: r1->field_f = r0
    //     0xade158: stur            w0, [x1, #0xf]
    // 0xade15c: r0 = 4
    //     0xade15c: movz            x0, #0x4
    // 0xade160: StoreField: r1->field_b = r0
    //     0xade160: stur            w0, [x1, #0xb]
    // 0xade164: mov             x2, x1
    // 0xade168: ldur            x1, [fp, #-0x30]
    // 0xade16c: r0 = addAll()
    //     0xade16c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xade170: ldur            x1, [fp, #-0x20]
    // 0xade174: ldur            x2, [fp, #-0x30]
    // 0xade178: r0 = addAll()
    //     0xade178: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xade17c: ldur            x0, [fp, #-8]
    // 0xade180: LoadField: r1 = r0->field_f
    //     0xade180: ldur            w1, [x0, #0xf]
    // 0xade184: DecompressPointer r1
    //     0xade184: add             x1, x1, HEAP, lsl #32
    // 0xade188: r0 = controller()
    //     0xade188: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade18c: LoadField: r1 = r0->field_37
    //     0xade18c: ldur            w1, [x0, #0x37]
    // 0xade190: DecompressPointer r1
    //     0xade190: add             x1, x1, HEAP, lsl #32
    // 0xade194: r16 = Instance_PaymentType
    //     0xade194: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xade198: ldr             x16, [x16, #0x608]
    // 0xade19c: cmp             w1, w16
    // 0xade1a0: b.ne            #0xade230
    // 0xade1a4: ldur            x2, [fp, #-0x10]
    // 0xade1a8: LoadField: r0 = r2->field_f
    //     0xade1a8: ldur            w0, [x2, #0xf]
    // 0xade1ac: DecompressPointer r0
    //     0xade1ac: add             x0, x0, HEAP, lsl #32
    // 0xade1b0: cmp             w0, NULL
    // 0xade1b4: b.eq            #0xaded7c
    // 0xade1b8: LoadField: r1 = r0->field_3b
    //     0xade1b8: ldur            w1, [x0, #0x3b]
    // 0xade1bc: DecompressPointer r1
    //     0xade1bc: add             x1, x1, HEAP, lsl #32
    // 0xade1c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xade1c0: ldur            w0, [x1, #0x17]
    // 0xade1c4: DecompressPointer r0
    //     0xade1c4: add             x0, x0, HEAP, lsl #32
    // 0xade1c8: cmp             w0, NULL
    // 0xade1cc: b.ne            #0xade1d4
    // 0xade1d0: r0 = ""
    //     0xade1d0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xade1d4: stur            x0, [fp, #-0x28]
    // 0xade1d8: r0 = Text()
    //     0xade1d8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xade1dc: mov             x3, x0
    // 0xade1e0: ldur            x0, [fp, #-0x28]
    // 0xade1e4: stur            x3, [fp, #-0x30]
    // 0xade1e8: StoreField: r3->field_b = r0
    //     0xade1e8: stur            w0, [x3, #0xb]
    // 0xade1ec: r1 = Null
    //     0xade1ec: mov             x1, NULL
    // 0xade1f0: r2 = 2
    //     0xade1f0: movz            x2, #0x2
    // 0xade1f4: r0 = AllocateArray()
    //     0xade1f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade1f8: mov             x2, x0
    // 0xade1fc: ldur            x0, [fp, #-0x30]
    // 0xade200: stur            x2, [fp, #-0x28]
    // 0xade204: StoreField: r2->field_f = r0
    //     0xade204: stur            w0, [x2, #0xf]
    // 0xade208: r1 = <Widget>
    //     0xade208: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xade20c: r0 = AllocateGrowableArray()
    //     0xade20c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xade210: mov             x1, x0
    // 0xade214: ldur            x0, [fp, #-0x28]
    // 0xade218: StoreField: r1->field_f = r0
    //     0xade218: stur            w0, [x1, #0xf]
    // 0xade21c: r0 = 2
    //     0xade21c: movz            x0, #0x2
    // 0xade220: StoreField: r1->field_b = r0
    //     0xade220: stur            w0, [x1, #0xb]
    // 0xade224: mov             x2, x1
    // 0xade228: ldur            x1, [fp, #-0x20]
    // 0xade22c: r0 = addAll()
    //     0xade22c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xade230: ldur            x0, [fp, #-0x20]
    // 0xade234: LoadField: r1 = r0->field_b
    //     0xade234: ldur            w1, [x0, #0xb]
    // 0xade238: LoadField: r2 = r0->field_f
    //     0xade238: ldur            w2, [x0, #0xf]
    // 0xade23c: DecompressPointer r2
    //     0xade23c: add             x2, x2, HEAP, lsl #32
    // 0xade240: LoadField: r3 = r2->field_b
    //     0xade240: ldur            w3, [x2, #0xb]
    // 0xade244: r2 = LoadInt32Instr(r1)
    //     0xade244: sbfx            x2, x1, #1, #0x1f
    // 0xade248: stur            x2, [fp, #-0x40]
    // 0xade24c: r1 = LoadInt32Instr(r3)
    //     0xade24c: sbfx            x1, x3, #1, #0x1f
    // 0xade250: cmp             x2, x1
    // 0xade254: b.ne            #0xade260
    // 0xade258: mov             x1, x0
    // 0xade25c: r0 = _growToNextCapacity()
    //     0xade25c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xade260: ldur            x2, [fp, #-0x10]
    // 0xade264: ldur            x0, [fp, #-0x20]
    // 0xade268: ldur            x1, [fp, #-0x40]
    // 0xade26c: add             x3, x1, #1
    // 0xade270: lsl             x4, x3, #1
    // 0xade274: StoreField: r0->field_b = r4
    //     0xade274: stur            w4, [x0, #0xb]
    // 0xade278: LoadField: r3 = r0->field_f
    //     0xade278: ldur            w3, [x0, #0xf]
    // 0xade27c: DecompressPointer r3
    //     0xade27c: add             x3, x3, HEAP, lsl #32
    // 0xade280: add             x4, x3, x1, lsl #2
    // 0xade284: r16 = Instance_SizedBox
    //     0xade284: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xade288: ldr             x16, [x16, #0x950]
    // 0xade28c: StoreField: r4->field_f = r16
    //     0xade28c: stur            w16, [x4, #0xf]
    // 0xade290: LoadField: r1 = r2->field_f
    //     0xade290: ldur            w1, [x2, #0xf]
    // 0xade294: DecompressPointer r1
    //     0xade294: add             x1, x1, HEAP, lsl #32
    // 0xade298: cmp             w1, NULL
    // 0xade29c: b.eq            #0xaded80
    // 0xade2a0: r0 = isEnded()
    //     0xade2a0: bl              #0x91eb50  ; [package:nuonline/app/data/models/campaign.dart] Campaign::isEnded
    // 0xade2a4: tbnz            w0, #4, #0xade550
    // 0xade2a8: ldur            x0, [fp, #-8]
    // 0xade2ac: LoadField: r1 = r0->field_f
    //     0xade2ac: ldur            w1, [x0, #0xf]
    // 0xade2b0: DecompressPointer r1
    //     0xade2b0: add             x1, x1, HEAP, lsl #32
    // 0xade2b4: r0 = controller()
    //     0xade2b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade2b8: LoadField: r1 = r0->field_37
    //     0xade2b8: ldur            w1, [x0, #0x37]
    // 0xade2bc: DecompressPointer r1
    //     0xade2bc: add             x1, x1, HEAP, lsl #32
    // 0xade2c0: r16 = Instance_PaymentType
    //     0xade2c0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xade2c4: ldr             x16, [x16, #0x608]
    // 0xade2c8: cmp             w1, w16
    // 0xade2cc: b.eq            #0xade548
    // 0xade2d0: ldur            x0, [fp, #-8]
    // 0xade2d4: r0 = Radius()
    //     0xade2d4: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xade2d8: d0 = 8.000000
    //     0xade2d8: fmov            d0, #8.00000000
    // 0xade2dc: stur            x0, [fp, #-0x28]
    // 0xade2e0: StoreField: r0->field_7 = d0
    //     0xade2e0: stur            d0, [x0, #7]
    // 0xade2e4: StoreField: r0->field_f = d0
    //     0xade2e4: stur            d0, [x0, #0xf]
    // 0xade2e8: r0 = BorderRadius()
    //     0xade2e8: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xade2ec: mov             x3, x0
    // 0xade2f0: ldur            x0, [fp, #-0x28]
    // 0xade2f4: stur            x3, [fp, #-0x30]
    // 0xade2f8: StoreField: r3->field_7 = r0
    //     0xade2f8: stur            w0, [x3, #7]
    // 0xade2fc: StoreField: r3->field_b = r0
    //     0xade2fc: stur            w0, [x3, #0xb]
    // 0xade300: StoreField: r3->field_f = r0
    //     0xade300: stur            w0, [x3, #0xf]
    // 0xade304: StoreField: r3->field_13 = r0
    //     0xade304: stur            w0, [x3, #0x13]
    // 0xade308: r1 = _ConstMap len:10
    //     0xade308: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xade30c: ldr             x1, [x1, #0x6c8]
    // 0xade310: r2 = 100
    //     0xade310: movz            x2, #0x64
    // 0xade314: r0 = []()
    //     0xade314: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xade318: stur            x0, [fp, #-0x28]
    // 0xade31c: cmp             w0, NULL
    // 0xade320: b.eq            #0xaded84
    // 0xade324: r1 = _ConstMap len:10
    //     0xade324: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xade328: ldr             x1, [x1, #0x6c8]
    // 0xade32c: r2 = 1800
    //     0xade32c: movz            x2, #0x708
    // 0xade330: r0 = []()
    //     0xade330: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xade334: cmp             w0, NULL
    // 0xade338: b.eq            #0xaded88
    // 0xade33c: r16 = <Color?>
    //     0xade33c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xade340: ldr             x16, [x16, #0x98]
    // 0xade344: stp             x0, x16, [SP, #8]
    // 0xade348: ldur            x16, [fp, #-0x28]
    // 0xade34c: str             x16, [SP]
    // 0xade350: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xade350: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xade354: r0 = mode()
    //     0xade354: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xade358: stur            x0, [fp, #-0x28]
    // 0xade35c: r0 = BoxDecoration()
    //     0xade35c: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xade360: mov             x2, x0
    // 0xade364: ldur            x0, [fp, #-0x28]
    // 0xade368: stur            x2, [fp, #-0x48]
    // 0xade36c: StoreField: r2->field_7 = r0
    //     0xade36c: stur            w0, [x2, #7]
    // 0xade370: ldur            x0, [fp, #-0x30]
    // 0xade374: StoreField: r2->field_13 = r0
    //     0xade374: stur            w0, [x2, #0x13]
    // 0xade378: r0 = Instance_BoxShape
    //     0xade378: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xade37c: ldr             x0, [x0, #0xca8]
    // 0xade380: StoreField: r2->field_23 = r0
    //     0xade380: stur            w0, [x2, #0x23]
    // 0xade384: ldur            x3, [fp, #-8]
    // 0xade388: LoadField: r1 = r3->field_f
    //     0xade388: ldur            w1, [x3, #0xf]
    // 0xade38c: DecompressPointer r1
    //     0xade38c: add             x1, x1, HEAP, lsl #32
    // 0xade390: r0 = controller()
    //     0xade390: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade394: LoadField: r1 = r0->field_37
    //     0xade394: ldur            w1, [x0, #0x37]
    // 0xade398: DecompressPointer r1
    //     0xade398: add             x1, x1, HEAP, lsl #32
    // 0xade39c: r16 = Instance_PaymentType
    //     0xade39c: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xade3a0: ldr             x16, [x16, #0x5f0]
    // 0xade3a4: cmp             w1, w16
    // 0xade3a8: b.ne            #0xade3b8
    // 0xade3ac: r0 = "Qurban"
    //     0xade3ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5e8] "Qurban"
    //     0xade3b0: ldr             x0, [x0, #0x5e8]
    // 0xade3b4: b               #0xade3c0
    // 0xade3b8: r0 = "Donasi"
    //     0xade3b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5f0] "Donasi"
    //     0xade3bc: ldr             x0, [x0, #0x5f0]
    // 0xade3c0: stur            x0, [fp, #-0x28]
    // 0xade3c4: r1 = Null
    //     0xade3c4: mov             x1, NULL
    // 0xade3c8: r2 = 4
    //     0xade3c8: movz            x2, #0x4
    // 0xade3cc: r0 = AllocateArray()
    //     0xade3cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade3d0: mov             x1, x0
    // 0xade3d4: ldur            x0, [fp, #-0x28]
    // 0xade3d8: StoreField: r1->field_f = r0
    //     0xade3d8: stur            w0, [x1, #0xf]
    // 0xade3dc: r16 = " Berakhir"
    //     0xade3dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f640] " Berakhir"
    //     0xade3e0: ldr             x16, [x16, #0x640]
    // 0xade3e4: StoreField: r1->field_13 = r16
    //     0xade3e4: stur            w16, [x1, #0x13]
    // 0xade3e8: str             x1, [SP]
    // 0xade3ec: r0 = _interpolate()
    //     0xade3ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xade3f0: stur            x0, [fp, #-0x28]
    // 0xade3f4: r0 = GetNavigation.textTheme()
    //     0xade3f4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xade3f8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xade3f8: ldur            w3, [x0, #0x17]
    // 0xade3fc: DecompressPointer r3
    //     0xade3fc: add             x3, x3, HEAP, lsl #32
    // 0xade400: stur            x3, [fp, #-0x30]
    // 0xade404: cmp             w3, NULL
    // 0xade408: b.ne            #0xade414
    // 0xade40c: r2 = Null
    //     0xade40c: mov             x2, NULL
    // 0xade410: b               #0xade464
    // 0xade414: r1 = _ConstMap len:10
    //     0xade414: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xade418: ldr             x1, [x1, #0x6c8]
    // 0xade41c: r2 = 600
    //     0xade41c: movz            x2, #0x258
    // 0xade420: r0 = []()
    //     0xade420: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xade424: cmp             w0, NULL
    // 0xade428: b.eq            #0xaded8c
    // 0xade42c: r16 = <Color?>
    //     0xade42c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xade430: ldr             x16, [x16, #0x98]
    // 0xade434: stp             x0, x16, [SP, #8]
    // 0xade438: r16 = Instance_MaterialColor
    //     0xade438: add             x16, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xade43c: ldr             x16, [x16, #0x6d0]
    // 0xade440: str             x16, [SP]
    // 0xade444: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xade444: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xade448: r0 = mode()
    //     0xade448: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xade44c: str             x0, [SP]
    // 0xade450: ldur            x1, [fp, #-0x30]
    // 0xade454: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xade454: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xade458: ldr             x4, [x4, #0x228]
    // 0xade45c: r0 = copyWith()
    //     0xade45c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xade460: mov             x2, x0
    // 0xade464: ldur            x1, [fp, #-0x20]
    // 0xade468: ldur            x0, [fp, #-0x28]
    // 0xade46c: stur            x2, [fp, #-0x30]
    // 0xade470: r0 = Text()
    //     0xade470: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xade474: mov             x1, x0
    // 0xade478: ldur            x0, [fp, #-0x28]
    // 0xade47c: stur            x1, [fp, #-0x50]
    // 0xade480: StoreField: r1->field_b = r0
    //     0xade480: stur            w0, [x1, #0xb]
    // 0xade484: ldur            x0, [fp, #-0x30]
    // 0xade488: StoreField: r1->field_13 = r0
    //     0xade488: stur            w0, [x1, #0x13]
    // 0xade48c: r0 = Instance_TextAlign
    //     0xade48c: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xade490: StoreField: r1->field_1b = r0
    //     0xade490: stur            w0, [x1, #0x1b]
    // 0xade494: r0 = Container()
    //     0xade494: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xade498: stur            x0, [fp, #-0x28]
    // 0xade49c: r16 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xade49c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xade4a0: ldr             x16, [x16, #0xc58]
    // 0xade4a4: r30 = Instance_EdgeInsets
    //     0xade4a4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f648] Obj!EdgeInsets@e12761
    //     0xade4a8: ldr             lr, [lr, #0x648]
    // 0xade4ac: stp             lr, x16, [SP, #0x10]
    // 0xade4b0: ldur            x16, [fp, #-0x48]
    // 0xade4b4: ldur            lr, [fp, #-0x50]
    // 0xade4b8: stp             lr, x16, [SP]
    // 0xade4bc: mov             x1, x0
    // 0xade4c0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x2, width, 0x1, null]
    //     0xade4c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f650] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xade4c4: ldr             x4, [x4, #0x650]
    // 0xade4c8: r0 = Container()
    //     0xade4c8: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xade4cc: ldur            x0, [fp, #-0x20]
    // 0xade4d0: LoadField: r1 = r0->field_b
    //     0xade4d0: ldur            w1, [x0, #0xb]
    // 0xade4d4: LoadField: r2 = r0->field_f
    //     0xade4d4: ldur            w2, [x0, #0xf]
    // 0xade4d8: DecompressPointer r2
    //     0xade4d8: add             x2, x2, HEAP, lsl #32
    // 0xade4dc: LoadField: r3 = r2->field_b
    //     0xade4dc: ldur            w3, [x2, #0xb]
    // 0xade4e0: r2 = LoadInt32Instr(r1)
    //     0xade4e0: sbfx            x2, x1, #1, #0x1f
    // 0xade4e4: stur            x2, [fp, #-0x40]
    // 0xade4e8: r1 = LoadInt32Instr(r3)
    //     0xade4e8: sbfx            x1, x3, #1, #0x1f
    // 0xade4ec: cmp             x2, x1
    // 0xade4f0: b.ne            #0xade4fc
    // 0xade4f4: mov             x1, x0
    // 0xade4f8: r0 = _growToNextCapacity()
    //     0xade4f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xade4fc: ldur            x2, [fp, #-0x20]
    // 0xade500: ldur            x3, [fp, #-0x40]
    // 0xade504: add             x0, x3, #1
    // 0xade508: lsl             x1, x0, #1
    // 0xade50c: StoreField: r2->field_b = r1
    //     0xade50c: stur            w1, [x2, #0xb]
    // 0xade510: LoadField: r1 = r2->field_f
    //     0xade510: ldur            w1, [x2, #0xf]
    // 0xade514: DecompressPointer r1
    //     0xade514: add             x1, x1, HEAP, lsl #32
    // 0xade518: ldur            x0, [fp, #-0x28]
    // 0xade51c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xade51c: add             x25, x1, x3, lsl #2
    //     0xade520: add             x25, x25, #0xf
    //     0xade524: str             w0, [x25]
    //     0xade528: tbz             w0, #0, #0xade544
    //     0xade52c: ldurb           w16, [x1, #-1]
    //     0xade530: ldurb           w17, [x0, #-1]
    //     0xade534: and             x16, x17, x16, lsr #2
    //     0xade538: tst             x16, HEAP, lsr #32
    //     0xade53c: b.eq            #0xade544
    //     0xade540: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xade544: b               #0xade6cc
    // 0xade548: ldur            x2, [fp, #-0x20]
    // 0xade54c: b               #0xade554
    // 0xade550: ldur            x2, [fp, #-0x20]
    // 0xade554: ldur            x0, [fp, #-8]
    // 0xade558: LoadField: r1 = r0->field_f
    //     0xade558: ldur            w1, [x0, #0xf]
    // 0xade55c: DecompressPointer r1
    //     0xade55c: add             x1, x1, HEAP, lsl #32
    // 0xade560: r0 = controller()
    //     0xade560: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade564: mov             x2, x0
    // 0xade568: ldur            x0, [fp, #-8]
    // 0xade56c: stur            x2, [fp, #-0x28]
    // 0xade570: LoadField: r1 = r0->field_f
    //     0xade570: ldur            w1, [x0, #0xf]
    // 0xade574: DecompressPointer r1
    //     0xade574: add             x1, x1, HEAP, lsl #32
    // 0xade578: r0 = controller()
    //     0xade578: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade57c: LoadField: r1 = r0->field_37
    //     0xade57c: ldur            w1, [x0, #0x37]
    // 0xade580: DecompressPointer r1
    //     0xade580: add             x1, x1, HEAP, lsl #32
    // 0xade584: r16 = Instance_PaymentType
    //     0xade584: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xade588: ldr             x16, [x16, #0x5f0]
    // 0xade58c: cmp             w1, w16
    // 0xade590: b.ne            #0xade5a0
    // 0xade594: r3 = "Qurban"
    //     0xade594: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f5e8] "Qurban"
    //     0xade598: ldr             x3, [x3, #0x5e8]
    // 0xade59c: b               #0xade5a8
    // 0xade5a0: r3 = "Donasi"
    //     0xade5a0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f5f0] "Donasi"
    //     0xade5a4: ldr             x3, [x3, #0x5f0]
    // 0xade5a8: ldur            x0, [fp, #-0x20]
    // 0xade5ac: stur            x3, [fp, #-0x30]
    // 0xade5b0: r1 = Null
    //     0xade5b0: mov             x1, NULL
    // 0xade5b4: r2 = 4
    //     0xade5b4: movz            x2, #0x4
    // 0xade5b8: r0 = AllocateArray()
    //     0xade5b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade5bc: mov             x1, x0
    // 0xade5c0: ldur            x0, [fp, #-0x30]
    // 0xade5c4: StoreField: r1->field_f = r0
    //     0xade5c4: stur            w0, [x1, #0xf]
    // 0xade5c8: r16 = " Sekarang"
    //     0xade5c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f5c0] " Sekarang"
    //     0xade5cc: ldr             x16, [x16, #0x5c0]
    // 0xade5d0: StoreField: r1->field_13 = r16
    //     0xade5d0: stur            w16, [x1, #0x13]
    // 0xade5d4: str             x1, [SP]
    // 0xade5d8: r0 = _interpolate()
    //     0xade5d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xade5dc: stur            x0, [fp, #-0x30]
    // 0xade5e0: r0 = Text()
    //     0xade5e0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xade5e4: mov             x3, x0
    // 0xade5e8: ldur            x0, [fp, #-0x30]
    // 0xade5ec: stur            x3, [fp, #-0x48]
    // 0xade5f0: StoreField: r3->field_b = r0
    //     0xade5f0: stur            w0, [x3, #0xb]
    // 0xade5f4: ldur            x2, [fp, #-0x28]
    // 0xade5f8: r1 = Function 'pay':.
    //     0xade5f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f5c8] AnonymousClosure: (0xadd70c), in [package:nuonline/app/modules/donation/controllers/campaign_detail_controller.dart] CampaignDetailController::pay (0xadd744)
    //     0xade5fc: ldr             x1, [x1, #0x5c8]
    // 0xade600: r0 = AllocateClosure()
    //     0xade600: bl              #0xec1630  ; AllocateClosureStub
    // 0xade604: stur            x0, [fp, #-0x28]
    // 0xade608: r0 = TextButton()
    //     0xade608: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xade60c: mov             x1, x0
    // 0xade610: ldur            x0, [fp, #-0x28]
    // 0xade614: stur            x1, [fp, #-0x30]
    // 0xade618: StoreField: r1->field_b = r0
    //     0xade618: stur            w0, [x1, #0xb]
    // 0xade61c: r0 = false
    //     0xade61c: add             x0, NULL, #0x30  ; false
    // 0xade620: StoreField: r1->field_27 = r0
    //     0xade620: stur            w0, [x1, #0x27]
    // 0xade624: r2 = true
    //     0xade624: add             x2, NULL, #0x20  ; true
    // 0xade628: StoreField: r1->field_2f = r2
    //     0xade628: stur            w2, [x1, #0x2f]
    // 0xade62c: ldur            x3, [fp, #-0x48]
    // 0xade630: StoreField: r1->field_37 = r3
    //     0xade630: stur            w3, [x1, #0x37]
    // 0xade634: r0 = SizedBox()
    //     0xade634: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xade638: mov             x2, x0
    // 0xade63c: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xade63c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xade640: ldr             x0, [x0, #0xc58]
    // 0xade644: stur            x2, [fp, #-0x28]
    // 0xade648: StoreField: r2->field_f = r0
    //     0xade648: stur            w0, [x2, #0xf]
    // 0xade64c: ldur            x0, [fp, #-0x30]
    // 0xade650: StoreField: r2->field_b = r0
    //     0xade650: stur            w0, [x2, #0xb]
    // 0xade654: ldur            x0, [fp, #-0x20]
    // 0xade658: LoadField: r1 = r0->field_b
    //     0xade658: ldur            w1, [x0, #0xb]
    // 0xade65c: LoadField: r3 = r0->field_f
    //     0xade65c: ldur            w3, [x0, #0xf]
    // 0xade660: DecompressPointer r3
    //     0xade660: add             x3, x3, HEAP, lsl #32
    // 0xade664: LoadField: r4 = r3->field_b
    //     0xade664: ldur            w4, [x3, #0xb]
    // 0xade668: r3 = LoadInt32Instr(r1)
    //     0xade668: sbfx            x3, x1, #1, #0x1f
    // 0xade66c: stur            x3, [fp, #-0x40]
    // 0xade670: r1 = LoadInt32Instr(r4)
    //     0xade670: sbfx            x1, x4, #1, #0x1f
    // 0xade674: cmp             x3, x1
    // 0xade678: b.ne            #0xade684
    // 0xade67c: mov             x1, x0
    // 0xade680: r0 = _growToNextCapacity()
    //     0xade680: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xade684: ldur            x2, [fp, #-0x20]
    // 0xade688: ldur            x3, [fp, #-0x40]
    // 0xade68c: add             x0, x3, #1
    // 0xade690: lsl             x1, x0, #1
    // 0xade694: StoreField: r2->field_b = r1
    //     0xade694: stur            w1, [x2, #0xb]
    // 0xade698: LoadField: r1 = r2->field_f
    //     0xade698: ldur            w1, [x2, #0xf]
    // 0xade69c: DecompressPointer r1
    //     0xade69c: add             x1, x1, HEAP, lsl #32
    // 0xade6a0: ldur            x0, [fp, #-0x28]
    // 0xade6a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xade6a4: add             x25, x1, x3, lsl #2
    //     0xade6a8: add             x25, x25, #0xf
    //     0xade6ac: str             w0, [x25]
    //     0xade6b0: tbz             w0, #0, #0xade6cc
    //     0xade6b4: ldurb           w16, [x1, #-1]
    //     0xade6b8: ldurb           w17, [x0, #-1]
    //     0xade6bc: and             x16, x17, x16, lsr #2
    //     0xade6c0: tst             x16, HEAP, lsr #32
    //     0xade6c4: b.eq            #0xade6cc
    //     0xade6c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xade6cc: ldur            x0, [fp, #-8]
    // 0xade6d0: r0 = NSection()
    //     0xade6d0: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xade6d4: mov             x2, x0
    // 0xade6d8: r0 = ""
    //     0xade6d8: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xade6dc: stur            x2, [fp, #-0x28]
    // 0xade6e0: StoreField: r2->field_b = r0
    //     0xade6e0: stur            w0, [x2, #0xb]
    // 0xade6e4: ldur            x0, [fp, #-0x20]
    // 0xade6e8: StoreField: r2->field_f = r0
    //     0xade6e8: stur            w0, [x2, #0xf]
    // 0xade6ec: r0 = false
    //     0xade6ec: add             x0, NULL, #0x30  ; false
    // 0xade6f0: StoreField: r2->field_27 = r0
    //     0xade6f0: stur            w0, [x2, #0x27]
    // 0xade6f4: StoreField: r2->field_2b = r0
    //     0xade6f4: stur            w0, [x2, #0x2b]
    // 0xade6f8: ldur            x3, [fp, #-8]
    // 0xade6fc: LoadField: r1 = r3->field_f
    //     0xade6fc: ldur            w1, [x3, #0xf]
    // 0xade700: DecompressPointer r1
    //     0xade700: add             x1, x1, HEAP, lsl #32
    // 0xade704: r0 = controller()
    //     0xade704: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade708: LoadField: r1 = r0->field_37
    //     0xade708: ldur            w1, [x0, #0x37]
    // 0xade70c: DecompressPointer r1
    //     0xade70c: add             x1, x1, HEAP, lsl #32
    // 0xade710: r16 = Instance_PaymentType
    //     0xade710: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xade714: ldr             x16, [x16, #0x5f0]
    // 0xade718: cmp             w1, w16
    // 0xade71c: b.ne            #0xade72c
    // 0xade720: r2 = "Deskripsi"
    //     0xade720: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f658] "Deskripsi"
    //     0xade724: ldr             x2, [x2, #0x658]
    // 0xade728: b               #0xade734
    // 0xade72c: r2 = "Informasi"
    //     0xade72c: add             x2, PP, #0x28, lsl #12  ; [pp+0x280d0] "Informasi"
    //     0xade730: ldr             x2, [x2, #0xd0]
    // 0xade734: ldur            x0, [fp, #-8]
    // 0xade738: stur            x2, [fp, #-0x20]
    // 0xade73c: LoadField: r1 = r0->field_f
    //     0xade73c: ldur            w1, [x0, #0xf]
    // 0xade740: DecompressPointer r1
    //     0xade740: add             x1, x1, HEAP, lsl #32
    // 0xade744: r0 = controller()
    //     0xade744: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade748: LoadField: r1 = r0->field_37
    //     0xade748: ldur            w1, [x0, #0x37]
    // 0xade74c: DecompressPointer r1
    //     0xade74c: add             x1, x1, HEAP, lsl #32
    // 0xade750: r16 = Instance_PaymentType
    //     0xade750: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xade754: ldr             x16, [x16, #0x5f0]
    // 0xade758: cmp             w1, w16
    // 0xade75c: b.ne            #0xade780
    // 0xade760: ldur            x2, [fp, #-0x10]
    // 0xade764: LoadField: r0 = r2->field_f
    //     0xade764: ldur            w0, [x2, #0xf]
    // 0xade768: DecompressPointer r0
    //     0xade768: add             x0, x0, HEAP, lsl #32
    // 0xade76c: cmp             w0, NULL
    // 0xade770: b.eq            #0xaded90
    // 0xade774: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xade774: ldur            w1, [x0, #0x17]
    // 0xade778: DecompressPointer r1
    //     0xade778: add             x1, x1, HEAP, lsl #32
    // 0xade77c: b               #0xade7b4
    // 0xade780: ldur            x2, [fp, #-0x10]
    // 0xade784: LoadField: r0 = r2->field_f
    //     0xade784: ldur            w0, [x2, #0xf]
    // 0xade788: DecompressPointer r0
    //     0xade788: add             x0, x0, HEAP, lsl #32
    // 0xade78c: cmp             w0, NULL
    // 0xade790: b.eq            #0xaded94
    // 0xade794: LoadField: r1 = r0->field_13
    //     0xade794: ldur            w1, [x0, #0x13]
    // 0xade798: DecompressPointer r1
    //     0xade798: add             x1, x1, HEAP, lsl #32
    // 0xade79c: cmp             w1, NULL
    // 0xade7a0: b.ne            #0xade7ac
    // 0xade7a4: r0 = ""
    //     0xade7a4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xade7a8: b               #0xade7b0
    // 0xade7ac: mov             x0, x1
    // 0xade7b0: mov             x1, x0
    // 0xade7b4: ldur            x0, [fp, #-8]
    // 0xade7b8: stur            x1, [fp, #-0x30]
    // 0xade7bc: r0 = ArticleContentHtml()
    //     0xade7bc: bl              #0xa36cb4  ; AllocateArticleContentHtmlStub -> ArticleContentHtml (size=0x30)
    // 0xade7c0: mov             x3, x0
    // 0xade7c4: ldur            x0, [fp, #-0x30]
    // 0xade7c8: stur            x3, [fp, #-0x48]
    // 0xade7cc: StoreField: r3->field_b = r0
    //     0xade7cc: stur            w0, [x3, #0xb]
    // 0xade7d0: r0 = Instance_ReadingPref
    //     0xade7d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f660] Obj!ReadingPref@e25be1
    //     0xade7d4: ldr             x0, [x0, #0x660]
    // 0xade7d8: StoreField: r3->field_f = r0
    //     0xade7d8: stur            w0, [x3, #0xf]
    // 0xade7dc: r0 = true
    //     0xade7dc: add             x0, NULL, #0x20  ; true
    // 0xade7e0: StoreField: r3->field_13 = r0
    //     0xade7e0: stur            w0, [x3, #0x13]
    // 0xade7e4: r4 = false
    //     0xade7e4: add             x4, NULL, #0x30  ; false
    // 0xade7e8: ArrayStore: r3[0] = r4  ; List_4
    //     0xade7e8: stur            w4, [x3, #0x17]
    // 0xade7ec: r1 = const []
    //     0xade7ec: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d530] List<ArticleInsertion>(0)
    //     0xade7f0: ldr             x1, [x1, #0x530]
    // 0xade7f4: StoreField: r3->field_23 = r1
    //     0xade7f4: stur            w1, [x3, #0x23]
    // 0xade7f8: StoreField: r3->field_27 = r1
    //     0xade7f8: stur            w1, [x3, #0x27]
    // 0xade7fc: r1 = const []
    //     0xade7fc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d538] List<Author>(0)
    //     0xade800: ldr             x1, [x1, #0x538]
    // 0xade804: StoreField: r3->field_1f = r1
    //     0xade804: stur            w1, [x3, #0x1f]
    // 0xade808: StoreField: r3->field_2b = r0
    //     0xade808: stur            w0, [x3, #0x2b]
    // 0xade80c: r1 = Null
    //     0xade80c: mov             x1, NULL
    // 0xade810: r2 = 2
    //     0xade810: movz            x2, #0x2
    // 0xade814: r0 = AllocateArray()
    //     0xade814: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade818: mov             x2, x0
    // 0xade81c: ldur            x0, [fp, #-0x48]
    // 0xade820: stur            x2, [fp, #-0x30]
    // 0xade824: StoreField: r2->field_f = r0
    //     0xade824: stur            w0, [x2, #0xf]
    // 0xade828: r1 = <Widget>
    //     0xade828: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xade82c: r0 = AllocateGrowableArray()
    //     0xade82c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xade830: mov             x2, x0
    // 0xade834: ldur            x0, [fp, #-0x30]
    // 0xade838: stur            x2, [fp, #-0x48]
    // 0xade83c: StoreField: r2->field_f = r0
    //     0xade83c: stur            w0, [x2, #0xf]
    // 0xade840: r0 = 2
    //     0xade840: movz            x0, #0x2
    // 0xade844: StoreField: r2->field_b = r0
    //     0xade844: stur            w0, [x2, #0xb]
    // 0xade848: ldur            x3, [fp, #-8]
    // 0xade84c: LoadField: r1 = r3->field_f
    //     0xade84c: ldur            w1, [x3, #0xf]
    // 0xade850: DecompressPointer r1
    //     0xade850: add             x1, x1, HEAP, lsl #32
    // 0xade854: r0 = controller()
    //     0xade854: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xade858: LoadField: r1 = r0->field_37
    //     0xade858: ldur            w1, [x0, #0x37]
    // 0xade85c: DecompressPointer r1
    //     0xade85c: add             x1, x1, HEAP, lsl #32
    // 0xade860: r16 = Instance_PaymentType
    //     0xade860: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xade864: ldr             x16, [x16, #0x5f0]
    // 0xade868: cmp             w1, w16
    // 0xade86c: b.eq            #0xadea5c
    // 0xade870: r0 = GetNavigation.textTheme()
    //     0xade870: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xade874: LoadField: r1 = r0->field_23
    //     0xade874: ldur            w1, [x0, #0x23]
    // 0xade878: DecompressPointer r1
    //     0xade878: add             x1, x1, HEAP, lsl #32
    // 0xade87c: stur            x1, [fp, #-0x30]
    // 0xade880: cmp             w1, NULL
    // 0xade884: b.ne            #0xade890
    // 0xade888: r0 = Null
    //     0xade888: mov             x0, NULL
    // 0xade88c: b               #0xade8c0
    // 0xade890: r0 = GetNavigation.theme()
    //     0xade890: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xade894: LoadField: r1 = r0->field_3f
    //     0xade894: ldur            w1, [x0, #0x3f]
    // 0xade898: DecompressPointer r1
    //     0xade898: add             x1, x1, HEAP, lsl #32
    // 0xade89c: LoadField: r0 = r1->field_b
    //     0xade89c: ldur            w0, [x1, #0xb]
    // 0xade8a0: DecompressPointer r0
    //     0xade8a0: add             x0, x0, HEAP, lsl #32
    // 0xade8a4: r16 = Instance_FontWeight
    //     0xade8a4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xade8a8: ldr             x16, [x16, #0xe20]
    // 0xade8ac: stp             x16, x0, [SP]
    // 0xade8b0: ldur            x1, [fp, #-0x30]
    // 0xade8b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontWeight, 0x2, null]
    //     0xade8b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f668] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontWeight", 0x2, Null]
    //     0xade8b8: ldr             x4, [x4, #0x668]
    // 0xade8bc: r0 = copyWith()
    //     0xade8bc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xade8c0: ldur            x1, [fp, #-0x48]
    // 0xade8c4: stur            x0, [fp, #-0x30]
    // 0xade8c8: r0 = Text()
    //     0xade8c8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xade8cc: mov             x1, x0
    // 0xade8d0: r0 = "Lihat Selengkapnya"
    //     0xade8d0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c908] "Lihat Selengkapnya"
    //     0xade8d4: ldr             x0, [x0, #0x908]
    // 0xade8d8: stur            x1, [fp, #-0x50]
    // 0xade8dc: StoreField: r1->field_b = r0
    //     0xade8dc: stur            w0, [x1, #0xb]
    // 0xade8e0: ldur            x0, [fp, #-0x30]
    // 0xade8e4: StoreField: r1->field_13 = r0
    //     0xade8e4: stur            w0, [x1, #0x13]
    // 0xade8e8: r0 = Instance_TextAlign
    //     0xade8e8: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xade8ec: StoreField: r1->field_1b = r0
    //     0xade8ec: stur            w0, [x1, #0x1b]
    // 0xade8f0: r0 = InkWell()
    //     0xade8f0: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xade8f4: mov             x3, x0
    // 0xade8f8: ldur            x0, [fp, #-0x50]
    // 0xade8fc: stur            x3, [fp, #-0x30]
    // 0xade900: StoreField: r3->field_b = r0
    //     0xade900: stur            w0, [x3, #0xb]
    // 0xade904: ldur            x2, [fp, #-0x10]
    // 0xade908: r1 = Function '<anonymous closure>':.
    //     0xade908: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f670] AnonymousClosure: (0xadf36c), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xade90c: ldr             x1, [x1, #0x670]
    // 0xade910: r0 = AllocateClosure()
    //     0xade910: bl              #0xec1630  ; AllocateClosureStub
    // 0xade914: mov             x1, x0
    // 0xade918: ldur            x0, [fp, #-0x30]
    // 0xade91c: StoreField: r0->field_f = r1
    //     0xade91c: stur            w1, [x0, #0xf]
    // 0xade920: r3 = true
    //     0xade920: add             x3, NULL, #0x20  ; true
    // 0xade924: StoreField: r0->field_43 = r3
    //     0xade924: stur            w3, [x0, #0x43]
    // 0xade928: r1 = Instance_BoxShape
    //     0xade928: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xade92c: ldr             x1, [x1, #0xca8]
    // 0xade930: StoreField: r0->field_47 = r1
    //     0xade930: stur            w1, [x0, #0x47]
    // 0xade934: StoreField: r0->field_6f = r3
    //     0xade934: stur            w3, [x0, #0x6f]
    // 0xade938: r4 = false
    //     0xade938: add             x4, NULL, #0x30  ; false
    // 0xade93c: StoreField: r0->field_73 = r4
    //     0xade93c: stur            w4, [x0, #0x73]
    // 0xade940: StoreField: r0->field_83 = r3
    //     0xade940: stur            w3, [x0, #0x83]
    // 0xade944: StoreField: r0->field_7b = r4
    //     0xade944: stur            w4, [x0, #0x7b]
    // 0xade948: r1 = Null
    //     0xade948: mov             x1, NULL
    // 0xade94c: r2 = 2
    //     0xade94c: movz            x2, #0x2
    // 0xade950: r0 = AllocateArray()
    //     0xade950: bl              #0xec22fc  ; AllocateArrayStub
    // 0xade954: mov             x2, x0
    // 0xade958: ldur            x0, [fp, #-0x30]
    // 0xade95c: stur            x2, [fp, #-0x50]
    // 0xade960: StoreField: r2->field_f = r0
    //     0xade960: stur            w0, [x2, #0xf]
    // 0xade964: r1 = <Widget>
    //     0xade964: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xade968: r0 = AllocateGrowableArray()
    //     0xade968: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xade96c: mov             x1, x0
    // 0xade970: ldur            x0, [fp, #-0x50]
    // 0xade974: stur            x1, [fp, #-0x30]
    // 0xade978: StoreField: r1->field_f = r0
    //     0xade978: stur            w0, [x1, #0xf]
    // 0xade97c: r0 = 2
    //     0xade97c: movz            x0, #0x2
    // 0xade980: StoreField: r1->field_b = r0
    //     0xade980: stur            w0, [x1, #0xb]
    // 0xade984: r0 = Row()
    //     0xade984: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xade988: mov             x2, x0
    // 0xade98c: r0 = Instance_Axis
    //     0xade98c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xade990: stur            x2, [fp, #-0x50]
    // 0xade994: StoreField: r2->field_f = r0
    //     0xade994: stur            w0, [x2, #0xf]
    // 0xade998: r0 = Instance_MainAxisAlignment
    //     0xade998: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xade99c: ldr             x0, [x0, #0x290]
    // 0xade9a0: StoreField: r2->field_13 = r0
    //     0xade9a0: stur            w0, [x2, #0x13]
    // 0xade9a4: r0 = Instance_MainAxisSize
    //     0xade9a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xade9a8: ldr             x0, [x0, #0x738]
    // 0xade9ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xade9ac: stur            w0, [x2, #0x17]
    // 0xade9b0: r0 = Instance_CrossAxisAlignment
    //     0xade9b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xade9b4: ldr             x0, [x0, #0x740]
    // 0xade9b8: StoreField: r2->field_1b = r0
    //     0xade9b8: stur            w0, [x2, #0x1b]
    // 0xade9bc: r0 = Instance_VerticalDirection
    //     0xade9bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xade9c0: ldr             x0, [x0, #0x748]
    // 0xade9c4: StoreField: r2->field_23 = r0
    //     0xade9c4: stur            w0, [x2, #0x23]
    // 0xade9c8: r0 = Instance_Clip
    //     0xade9c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xade9cc: ldr             x0, [x0, #0x750]
    // 0xade9d0: StoreField: r2->field_2b = r0
    //     0xade9d0: stur            w0, [x2, #0x2b]
    // 0xade9d4: StoreField: r2->field_2f = rZR
    //     0xade9d4: stur            xzr, [x2, #0x2f]
    // 0xade9d8: ldur            x0, [fp, #-0x30]
    // 0xade9dc: StoreField: r2->field_b = r0
    //     0xade9dc: stur            w0, [x2, #0xb]
    // 0xade9e0: ldur            x0, [fp, #-0x48]
    // 0xade9e4: LoadField: r1 = r0->field_b
    //     0xade9e4: ldur            w1, [x0, #0xb]
    // 0xade9e8: LoadField: r3 = r0->field_f
    //     0xade9e8: ldur            w3, [x0, #0xf]
    // 0xade9ec: DecompressPointer r3
    //     0xade9ec: add             x3, x3, HEAP, lsl #32
    // 0xade9f0: LoadField: r4 = r3->field_b
    //     0xade9f0: ldur            w4, [x3, #0xb]
    // 0xade9f4: r3 = LoadInt32Instr(r1)
    //     0xade9f4: sbfx            x3, x1, #1, #0x1f
    // 0xade9f8: stur            x3, [fp, #-0x40]
    // 0xade9fc: r1 = LoadInt32Instr(r4)
    //     0xade9fc: sbfx            x1, x4, #1, #0x1f
    // 0xadea00: cmp             x3, x1
    // 0xadea04: b.ne            #0xadea10
    // 0xadea08: mov             x1, x0
    // 0xadea0c: r0 = _growToNextCapacity()
    //     0xadea0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadea10: ldur            x2, [fp, #-0x48]
    // 0xadea14: ldur            x3, [fp, #-0x40]
    // 0xadea18: add             x0, x3, #1
    // 0xadea1c: lsl             x1, x0, #1
    // 0xadea20: StoreField: r2->field_b = r1
    //     0xadea20: stur            w1, [x2, #0xb]
    // 0xadea24: LoadField: r1 = r2->field_f
    //     0xadea24: ldur            w1, [x2, #0xf]
    // 0xadea28: DecompressPointer r1
    //     0xadea28: add             x1, x1, HEAP, lsl #32
    // 0xadea2c: ldur            x0, [fp, #-0x50]
    // 0xadea30: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadea30: add             x25, x1, x3, lsl #2
    //     0xadea34: add             x25, x25, #0xf
    //     0xadea38: str             w0, [x25]
    //     0xadea3c: tbz             w0, #0, #0xadea58
    //     0xadea40: ldurb           w16, [x1, #-1]
    //     0xadea44: ldurb           w17, [x0, #-1]
    //     0xadea48: and             x16, x17, x16, lsr #2
    //     0xadea4c: tst             x16, HEAP, lsr #32
    //     0xadea50: b.eq            #0xadea58
    //     0xadea54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadea58: b               #0xadea60
    // 0xadea5c: ldur            x2, [fp, #-0x48]
    // 0xadea60: ldur            x0, [fp, #-8]
    // 0xadea64: ldur            x3, [fp, #-0x28]
    // 0xadea68: ldur            x1, [fp, #-0x20]
    // 0xadea6c: r0 = NSection()
    //     0xadea6c: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xadea70: mov             x3, x0
    // 0xadea74: ldur            x0, [fp, #-0x20]
    // 0xadea78: stur            x3, [fp, #-0x30]
    // 0xadea7c: StoreField: r3->field_b = r0
    //     0xadea7c: stur            w0, [x3, #0xb]
    // 0xadea80: ldur            x0, [fp, #-0x48]
    // 0xadea84: StoreField: r3->field_f = r0
    //     0xadea84: stur            w0, [x3, #0xf]
    // 0xadea88: r0 = true
    //     0xadea88: add             x0, NULL, #0x20  ; true
    // 0xadea8c: StoreField: r3->field_27 = r0
    //     0xadea8c: stur            w0, [x3, #0x27]
    // 0xadea90: StoreField: r3->field_2b = r0
    //     0xadea90: stur            w0, [x3, #0x2b]
    // 0xadea94: r1 = Null
    //     0xadea94: mov             x1, NULL
    // 0xadea98: r2 = 6
    //     0xadea98: movz            x2, #0x6
    // 0xadea9c: r0 = AllocateArray()
    //     0xadea9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadeaa0: mov             x2, x0
    // 0xadeaa4: ldur            x0, [fp, #-0x28]
    // 0xadeaa8: stur            x2, [fp, #-0x20]
    // 0xadeaac: StoreField: r2->field_f = r0
    //     0xadeaac: stur            w0, [x2, #0xf]
    // 0xadeab0: r16 = Instance_NSectionDivider
    //     0xadeab0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xadeab4: ldr             x16, [x16, #0x38]
    // 0xadeab8: StoreField: r2->field_13 = r16
    //     0xadeab8: stur            w16, [x2, #0x13]
    // 0xadeabc: ldur            x0, [fp, #-0x30]
    // 0xadeac0: ArrayStore: r2[0] = r0  ; List_4
    //     0xadeac0: stur            w0, [x2, #0x17]
    // 0xadeac4: r1 = <Widget>
    //     0xadeac4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadeac8: r0 = AllocateGrowableArray()
    //     0xadeac8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadeacc: mov             x2, x0
    // 0xadead0: ldur            x0, [fp, #-0x20]
    // 0xadead4: stur            x2, [fp, #-0x28]
    // 0xadead8: StoreField: r2->field_f = r0
    //     0xadead8: stur            w0, [x2, #0xf]
    // 0xadeadc: r0 = 6
    //     0xadeadc: movz            x0, #0x6
    // 0xadeae0: StoreField: r2->field_b = r0
    //     0xadeae0: stur            w0, [x2, #0xb]
    // 0xadeae4: ldur            x0, [fp, #-8]
    // 0xadeae8: LoadField: r1 = r0->field_f
    //     0xadeae8: ldur            w1, [x0, #0xf]
    // 0xadeaec: DecompressPointer r1
    //     0xadeaec: add             x1, x1, HEAP, lsl #32
    // 0xadeaf0: r0 = controller()
    //     0xadeaf0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadeaf4: LoadField: r1 = r0->field_37
    //     0xadeaf4: ldur            w1, [x0, #0x37]
    // 0xadeaf8: DecompressPointer r1
    //     0xadeaf8: add             x1, x1, HEAP, lsl #32
    // 0xadeafc: r16 = Instance_PaymentType
    //     0xadeafc: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xadeb00: ldr             x16, [x16, #0x5f0]
    // 0xadeb04: cmp             w1, w16
    // 0xadeb08: b.eq            #0xadec38
    // 0xadeb0c: ldur            x0, [fp, #-8]
    // 0xadeb10: ldur            x2, [fp, #-0x10]
    // 0xadeb14: LoadField: r1 = r0->field_f
    //     0xadeb14: ldur            w1, [x0, #0xf]
    // 0xadeb18: DecompressPointer r1
    //     0xadeb18: add             x1, x1, HEAP, lsl #32
    // 0xadeb1c: r0 = controller()
    //     0xadeb1c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadeb20: LoadField: r1 = r0->field_37
    //     0xadeb20: ldur            w1, [x0, #0x37]
    // 0xadeb24: DecompressPointer r1
    //     0xadeb24: add             x1, x1, HEAP, lsl #32
    // 0xadeb28: ldur            x2, [fp, #-0x10]
    // 0xadeb2c: stur            x1, [fp, #-8]
    // 0xadeb30: LoadField: r0 = r2->field_f
    //     0xadeb30: ldur            w0, [x2, #0xf]
    // 0xadeb34: DecompressPointer r0
    //     0xadeb34: add             x0, x0, HEAP, lsl #32
    // 0xadeb38: cmp             w0, NULL
    // 0xadeb3c: b.eq            #0xaded98
    // 0xadeb40: LoadField: r3 = r0->field_7
    //     0xadeb40: ldur            x3, [x0, #7]
    // 0xadeb44: stur            x3, [fp, #-0x40]
    // 0xadeb48: r0 = TransactionHistory()
    //     0xadeb48: bl              #0xadee74  ; AllocateTransactionHistoryStub -> TransactionHistory (size=0x14)
    // 0xadeb4c: mov             x2, x0
    // 0xadeb50: ldur            x0, [fp, #-8]
    // 0xadeb54: stur            x2, [fp, #-0x20]
    // 0xadeb58: StoreField: r2->field_b = r0
    //     0xadeb58: stur            w0, [x2, #0xb]
    // 0xadeb5c: ldur            x3, [fp, #-0x40]
    // 0xadeb60: r0 = BoxInt64Instr(r3)
    //     0xadeb60: sbfiz           x0, x3, #1, #0x1f
    //     0xadeb64: cmp             x3, x0, asr #1
    //     0xadeb68: b.eq            #0xadeb74
    //     0xadeb6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadeb70: stur            x3, [x0, #7]
    // 0xadeb74: StoreField: r2->field_f = r0
    //     0xadeb74: stur            w0, [x2, #0xf]
    // 0xadeb78: r0 = NCardListHeader()
    //     0xadeb78: bl              #0xadee68  ; AllocateNCardListHeaderStub -> NCardListHeader (size=0x2c)
    // 0xadeb7c: mov             x3, x0
    // 0xadeb80: r0 = "Lihat Kabar Terbaru"
    //     0xadeb80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f560] "Lihat Kabar Terbaru"
    //     0xadeb84: ldr             x0, [x0, #0x560]
    // 0xadeb88: stur            x3, [fp, #-8]
    // 0xadeb8c: StoreField: r3->field_f = r0
    //     0xadeb8c: stur            w0, [x3, #0xf]
    // 0xadeb90: ldur            x2, [fp, #-0x10]
    // 0xadeb94: r1 = Function '<anonymous closure>':.
    //     0xadeb94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f678] AnonymousClosure: (0xadf238), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadeb98: ldr             x1, [x1, #0x678]
    // 0xadeb9c: r0 = AllocateClosure()
    //     0xadeb9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xadeba0: mov             x1, x0
    // 0xadeba4: ldur            x0, [fp, #-8]
    // 0xadeba8: StoreField: r0->field_1b = r1
    //     0xadeba8: stur            w1, [x0, #0x1b]
    // 0xadebac: r1 = false
    //     0xadebac: add             x1, NULL, #0x30  ; false
    // 0xadebb0: StoreField: r0->field_1f = r1
    //     0xadebb0: stur            w1, [x0, #0x1f]
    // 0xadebb4: StoreField: r0->field_23 = r1
    //     0xadebb4: stur            w1, [x0, #0x23]
    // 0xadebb8: r0 = Padding()
    //     0xadebb8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadebbc: mov             x3, x0
    // 0xadebc0: r0 = Instance_EdgeInsets
    //     0xadebc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f680] Obj!EdgeInsets@e12731
    //     0xadebc4: ldr             x0, [x0, #0x680]
    // 0xadebc8: stur            x3, [fp, #-0x10]
    // 0xadebcc: StoreField: r3->field_f = r0
    //     0xadebcc: stur            w0, [x3, #0xf]
    // 0xadebd0: ldur            x0, [fp, #-8]
    // 0xadebd4: StoreField: r3->field_b = r0
    //     0xadebd4: stur            w0, [x3, #0xb]
    // 0xadebd8: r1 = Null
    //     0xadebd8: mov             x1, NULL
    // 0xadebdc: r2 = 8
    //     0xadebdc: movz            x2, #0x8
    // 0xadebe0: r0 = AllocateArray()
    //     0xadebe0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadebe4: stur            x0, [fp, #-8]
    // 0xadebe8: r16 = Instance_NSectionDivider
    //     0xadebe8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xadebec: ldr             x16, [x16, #0x38]
    // 0xadebf0: StoreField: r0->field_f = r16
    //     0xadebf0: stur            w16, [x0, #0xf]
    // 0xadebf4: ldur            x1, [fp, #-0x20]
    // 0xadebf8: StoreField: r0->field_13 = r1
    //     0xadebf8: stur            w1, [x0, #0x13]
    // 0xadebfc: r16 = Instance_NSectionDivider
    //     0xadebfc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xadec00: ldr             x16, [x16, #0x38]
    // 0xadec04: ArrayStore: r0[0] = r16  ; List_4
    //     0xadec04: stur            w16, [x0, #0x17]
    // 0xadec08: ldur            x1, [fp, #-0x10]
    // 0xadec0c: StoreField: r0->field_1b = r1
    //     0xadec0c: stur            w1, [x0, #0x1b]
    // 0xadec10: r1 = <Widget>
    //     0xadec10: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadec14: r0 = AllocateGrowableArray()
    //     0xadec14: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadec18: mov             x1, x0
    // 0xadec1c: ldur            x0, [fp, #-8]
    // 0xadec20: StoreField: r1->field_f = r0
    //     0xadec20: stur            w0, [x1, #0xf]
    // 0xadec24: r0 = 8
    //     0xadec24: movz            x0, #0x8
    // 0xadec28: StoreField: r1->field_b = r0
    //     0xadec28: stur            w0, [x1, #0xb]
    // 0xadec2c: mov             x2, x1
    // 0xadec30: ldur            x1, [fp, #-0x28]
    // 0xadec34: r0 = addAll()
    //     0xadec34: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xadec38: ldur            x2, [fp, #-0x18]
    // 0xadec3c: ldur            x1, [fp, #-0x38]
    // 0xadec40: ldur            x0, [fp, #-0x28]
    // 0xadec44: r0 = SliverChildListDelegate()
    //     0xadec44: bl              #0xa07e78  ; AllocateSliverChildListDelegateStub -> SliverChildListDelegate (size=0x28)
    // 0xadec48: mov             x1, x0
    // 0xadec4c: ldur            x0, [fp, #-0x28]
    // 0xadec50: stur            x1, [fp, #-8]
    // 0xadec54: StoreField: r1->field_1f = r0
    //     0xadec54: stur            w0, [x1, #0x1f]
    // 0xadec58: r0 = true
    //     0xadec58: add             x0, NULL, #0x20  ; true
    // 0xadec5c: StoreField: r1->field_7 = r0
    //     0xadec5c: stur            w0, [x1, #7]
    // 0xadec60: StoreField: r1->field_b = r0
    //     0xadec60: stur            w0, [x1, #0xb]
    // 0xadec64: StoreField: r1->field_f = r0
    //     0xadec64: stur            w0, [x1, #0xf]
    // 0xadec68: r2 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xadec68: add             x2, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xadec6c: ldr             x2, [x2, #0xef8]
    // 0xadec70: StoreField: r1->field_1b = r2
    //     0xadec70: stur            w2, [x1, #0x1b]
    // 0xadec74: StoreField: r1->field_13 = rZR
    //     0xadec74: stur            xzr, [x1, #0x13]
    // 0xadec78: r0 = SliverList()
    //     0xadec78: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xadec7c: mov             x3, x0
    // 0xadec80: ldur            x0, [fp, #-8]
    // 0xadec84: stur            x3, [fp, #-0x10]
    // 0xadec88: StoreField: r3->field_b = r0
    //     0xadec88: stur            w0, [x3, #0xb]
    // 0xadec8c: r1 = Null
    //     0xadec8c: mov             x1, NULL
    // 0xadec90: r2 = 4
    //     0xadec90: movz            x2, #0x4
    // 0xadec94: r0 = AllocateArray()
    //     0xadec94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadec98: mov             x2, x0
    // 0xadec9c: ldur            x0, [fp, #-0x38]
    // 0xadeca0: stur            x2, [fp, #-8]
    // 0xadeca4: StoreField: r2->field_f = r0
    //     0xadeca4: stur            w0, [x2, #0xf]
    // 0xadeca8: ldur            x0, [fp, #-0x10]
    // 0xadecac: StoreField: r2->field_13 = r0
    //     0xadecac: stur            w0, [x2, #0x13]
    // 0xadecb0: r1 = <Widget>
    //     0xadecb0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xadecb4: r0 = AllocateGrowableArray()
    //     0xadecb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xadecb8: mov             x1, x0
    // 0xadecbc: ldur            x0, [fp, #-8]
    // 0xadecc0: stur            x1, [fp, #-0x10]
    // 0xadecc4: StoreField: r1->field_f = r0
    //     0xadecc4: stur            w0, [x1, #0xf]
    // 0xadecc8: r0 = 4
    //     0xadecc8: movz            x0, #0x4
    // 0xadeccc: StoreField: r1->field_b = r0
    //     0xadeccc: stur            w0, [x1, #0xb]
    // 0xadecd0: r0 = CustomScrollView()
    //     0xadecd0: bl              #0xa0128c  ; AllocateCustomScrollViewStub -> CustomScrollView (size=0x54)
    // 0xadecd4: ldur            x1, [fp, #-0x10]
    // 0xadecd8: StoreField: r0->field_4f = r1
    //     0xadecd8: stur            w1, [x0, #0x4f]
    // 0xadecdc: r1 = Instance_Axis
    //     0xadecdc: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xadece0: StoreField: r0->field_b = r1
    //     0xadece0: stur            w1, [x0, #0xb]
    // 0xadece4: r1 = false
    //     0xadece4: add             x1, NULL, #0x30  ; false
    // 0xadece8: StoreField: r0->field_f = r1
    //     0xadece8: stur            w1, [x0, #0xf]
    // 0xadecec: ldur            x1, [fp, #-0x18]
    // 0xadecf0: StoreField: r0->field_13 = r1
    //     0xadecf0: stur            w1, [x0, #0x13]
    // 0xadecf4: r1 = true
    //     0xadecf4: add             x1, NULL, #0x20  ; true
    // 0xadecf8: StoreField: r0->field_23 = r1
    //     0xadecf8: stur            w1, [x0, #0x23]
    // 0xadecfc: StoreField: r0->field_2b = rZR
    //     0xadecfc: stur            xzr, [x0, #0x2b]
    // 0xaded00: r1 = Instance_DragStartBehavior
    //     0xaded00: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaded04: StoreField: r0->field_3b = r1
    //     0xaded04: stur            w1, [x0, #0x3b]
    // 0xaded08: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xaded08: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xaded0c: ldr             x1, [x1, #0xf00]
    // 0xaded10: StoreField: r0->field_3f = r1
    //     0xaded10: stur            w1, [x0, #0x3f]
    // 0xaded14: r1 = Instance_Clip
    //     0xaded14: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xaded18: ldr             x1, [x1, #0x7c0]
    // 0xaded1c: StoreField: r0->field_47 = r1
    //     0xaded1c: stur            w1, [x0, #0x47]
    // 0xaded20: r1 = Instance_HitTestBehavior
    //     0xaded20: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xaded24: ldr             x1, [x1, #0x1c8]
    // 0xaded28: StoreField: r0->field_4b = r1
    //     0xaded28: stur            w1, [x0, #0x4b]
    // 0xaded2c: r1 = Instance_ClampingScrollPhysics
    //     0xaded2c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xaded30: ldr             x1, [x1, #0x410]
    // 0xaded34: StoreField: r0->field_1b = r1
    //     0xaded34: stur            w1, [x0, #0x1b]
    // 0xaded38: LeaveFrame
    //     0xaded38: mov             SP, fp
    //     0xaded3c: ldp             fp, lr, [SP], #0x10
    // 0xaded40: ret
    //     0xaded40: ret             
    // 0xaded44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaded44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaded48: b               #0xadd828
    // 0xaded4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaded4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaded50: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded50: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded54: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded54: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded58: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded58: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded5c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded5c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded60: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded60: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded64: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded64: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded68: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded68: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded6c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded6c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded70: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded70: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded74: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded74: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded78: r0 = NullErrorSharedWithFPURegs()
    //     0xaded78: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xaded7c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded7c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded80: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded80: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaded84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaded88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaded88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaded8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaded8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaded90: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded90: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded94: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded94: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xaded98: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaded98: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadf238, size: 0x128
    // 0xadf238: EnterFrame
    //     0xadf238: stp             fp, lr, [SP, #-0x10]!
    //     0xadf23c: mov             fp, SP
    // 0xadf240: AllocStack(0x38)
    //     0xadf240: sub             SP, SP, #0x38
    // 0xadf244: SetupParameters()
    //     0xadf244: ldr             x0, [fp, #0x10]
    //     0xadf248: ldur            w1, [x0, #0x17]
    //     0xadf24c: add             x1, x1, HEAP, lsl #32
    //     0xadf250: stur            x1, [fp, #-8]
    // 0xadf254: CheckStackOverflow
    //     0xadf254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf258: cmp             SP, x16
    //     0xadf25c: b.ls            #0xadf354
    // 0xadf260: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadf260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadf264: ldr             x0, [x0, #0x2670]
    //     0xadf268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadf26c: cmp             w0, w16
    //     0xadf270: b.ne            #0xadf27c
    //     0xadf274: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadf278: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadf27c: ldur            x0, [fp, #-8]
    // 0xadf280: LoadField: r1 = r0->field_b
    //     0xadf280: ldur            w1, [x0, #0xb]
    // 0xadf284: DecompressPointer r1
    //     0xadf284: add             x1, x1, HEAP, lsl #32
    // 0xadf288: LoadField: r2 = r1->field_f
    //     0xadf288: ldur            w2, [x1, #0xf]
    // 0xadf28c: DecompressPointer r2
    //     0xadf28c: add             x2, x2, HEAP, lsl #32
    // 0xadf290: mov             x1, x2
    // 0xadf294: r0 = controller()
    //     0xadf294: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadf298: LoadField: r1 = r0->field_37
    //     0xadf298: ldur            w1, [x0, #0x37]
    // 0xadf29c: DecompressPointer r1
    //     0xadf29c: add             x1, x1, HEAP, lsl #32
    // 0xadf2a0: r16 = Instance_PaymentType
    //     0xadf2a0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xadf2a4: ldr             x16, [x16, #0x608]
    // 0xadf2a8: cmp             w1, w16
    // 0xadf2ac: b.ne            #0xadf2bc
    // 0xadf2b0: r2 = "/donation/koin-nu-news"
    //     0xadf2b0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f6e8] "/donation/koin-nu-news"
    //     0xadf2b4: ldr             x2, [x2, #0x6e8]
    // 0xadf2b8: b               #0xadf2c4
    // 0xadf2bc: r2 = "/donation/campaign-news"
    //     0xadf2bc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f6f0] "/donation/campaign-news"
    //     0xadf2c0: ldr             x2, [x2, #0x6f0]
    // 0xadf2c4: ldur            x0, [fp, #-8]
    // 0xadf2c8: stur            x2, [fp, #-0x20]
    // 0xadf2cc: LoadField: r1 = r0->field_f
    //     0xadf2cc: ldur            w1, [x0, #0xf]
    // 0xadf2d0: DecompressPointer r1
    //     0xadf2d0: add             x1, x1, HEAP, lsl #32
    // 0xadf2d4: cmp             w1, NULL
    // 0xadf2d8: b.eq            #0xadf35c
    // 0xadf2dc: LoadField: r3 = r1->field_7
    //     0xadf2dc: ldur            x3, [x1, #7]
    // 0xadf2e0: LoadField: r4 = r1->field_f
    //     0xadf2e0: ldur            w4, [x1, #0xf]
    // 0xadf2e4: DecompressPointer r4
    //     0xadf2e4: add             x4, x4, HEAP, lsl #32
    // 0xadf2e8: stur            x4, [fp, #-0x18]
    // 0xadf2ec: LoadField: r5 = r1->field_1b
    //     0xadf2ec: ldur            w5, [x1, #0x1b]
    // 0xadf2f0: DecompressPointer r5
    //     0xadf2f0: add             x5, x5, HEAP, lsl #32
    // 0xadf2f4: stur            x5, [fp, #-0x10]
    // 0xadf2f8: r0 = BoxInt64Instr(r3)
    //     0xadf2f8: sbfiz           x0, x3, #1, #0x1f
    //     0xadf2fc: cmp             x3, x0, asr #1
    //     0xadf300: b.eq            #0xadf30c
    //     0xadf304: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadf308: stur            x3, [x0, #7]
    // 0xadf30c: stur            x0, [fp, #-8]
    // 0xadf310: r0 = Merchant()
    //     0xadf310: bl              #0xadf360  ; AllocateMerchantStub -> Merchant (size=0x18)
    // 0xadf314: mov             x1, x0
    // 0xadf318: ldur            x0, [fp, #-8]
    // 0xadf31c: StoreField: r1->field_7 = r0
    //     0xadf31c: stur            w0, [x1, #7]
    // 0xadf320: ldur            x0, [fp, #-0x18]
    // 0xadf324: StoreField: r1->field_b = r0
    //     0xadf324: stur            w0, [x1, #0xb]
    // 0xadf328: ldur            x0, [fp, #-0x10]
    // 0xadf32c: StoreField: r1->field_f = r0
    //     0xadf32c: stur            w0, [x1, #0xf]
    // 0xadf330: ldur            x16, [fp, #-0x20]
    // 0xadf334: stp             x16, NULL, [SP, #8]
    // 0xadf338: str             x1, [SP]
    // 0xadf33c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xadf33c: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xadf340: ldr             x4, [x4, #0x478]
    // 0xadf344: r0 = GetNavigation.toNamed()
    //     0xadf344: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xadf348: LeaveFrame
    //     0xadf348: mov             SP, fp
    //     0xadf34c: ldp             fp, lr, [SP], #0x10
    // 0xadf350: ret
    //     0xadf350: ret             
    // 0xadf354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf358: b               #0xadf260
    // 0xadf35c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xadf35c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadf36c, size: 0x74
    // 0xadf36c: EnterFrame
    //     0xadf36c: stp             fp, lr, [SP, #-0x10]!
    //     0xadf370: mov             fp, SP
    // 0xadf374: AllocStack(0x18)
    //     0xadf374: sub             SP, SP, #0x18
    // 0xadf378: SetupParameters()
    //     0xadf378: ldr             x0, [fp, #0x10]
    //     0xadf37c: ldur            w2, [x0, #0x17]
    //     0xadf380: add             x2, x2, HEAP, lsl #32
    //     0xadf384: stur            x2, [fp, #-8]
    // 0xadf388: CheckStackOverflow
    //     0xadf388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf38c: cmp             SP, x16
    //     0xadf390: b.ls            #0xadf3d8
    // 0xadf394: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadf394: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadf398: ldr             x0, [x0, #0x2670]
    //     0xadf39c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadf3a0: cmp             w0, w16
    //     0xadf3a4: b.ne            #0xadf3b0
    //     0xadf3a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadf3ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadf3b0: ldur            x2, [fp, #-8]
    // 0xadf3b4: r1 = Function '<anonymous closure>':.
    //     0xadf3b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f6f8] AnonymousClosure: (0xadf6f4), in [package:nuonline/app/modules/donation/views/campaign_detail_view.dart] CampaignDetailView::build (0xadd2e8)
    //     0xadf3b8: ldr             x1, [x1, #0x6f8]
    // 0xadf3bc: r0 = AllocateClosure()
    //     0xadf3bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xadf3c0: stp             x0, NULL, [SP]
    // 0xadf3c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadf3c4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadf3c8: r0 = GetNavigation.to()
    //     0xadf3c8: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xadf3cc: LeaveFrame
    //     0xadf3cc: mov             SP, fp
    //     0xadf3d0: ldp             fp, lr, [SP], #0x10
    // 0xadf3d4: ret
    //     0xadf3d4: ret             
    // 0xadf3d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf3d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf3dc: b               #0xadf394
  }
  [closure] CampaignInformationView <anonymous closure>(dynamic) {
    // ** addr: 0xadf6f4, size: 0x90
    // 0xadf6f4: EnterFrame
    //     0xadf6f4: stp             fp, lr, [SP, #-0x10]!
    //     0xadf6f8: mov             fp, SP
    // 0xadf6fc: AllocStack(0x10)
    //     0xadf6fc: sub             SP, SP, #0x10
    // 0xadf700: SetupParameters()
    //     0xadf700: ldr             x0, [fp, #0x10]
    //     0xadf704: ldur            w1, [x0, #0x17]
    //     0xadf708: add             x1, x1, HEAP, lsl #32
    // 0xadf70c: CheckStackOverflow
    //     0xadf70c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf710: cmp             SP, x16
    //     0xadf714: b.ls            #0xadf77c
    // 0xadf718: LoadField: r0 = r1->field_f
    //     0xadf718: ldur            w0, [x1, #0xf]
    // 0xadf71c: DecompressPointer r0
    //     0xadf71c: add             x0, x0, HEAP, lsl #32
    // 0xadf720: stur            x0, [fp, #-8]
    // 0xadf724: LoadField: r2 = r1->field_b
    //     0xadf724: ldur            w2, [x1, #0xb]
    // 0xadf728: DecompressPointer r2
    //     0xadf728: add             x2, x2, HEAP, lsl #32
    // 0xadf72c: LoadField: r1 = r2->field_f
    //     0xadf72c: ldur            w1, [x2, #0xf]
    // 0xadf730: DecompressPointer r1
    //     0xadf730: add             x1, x1, HEAP, lsl #32
    // 0xadf734: r0 = controller()
    //     0xadf734: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadf738: LoadField: r1 = r0->field_37
    //     0xadf738: ldur            w1, [x0, #0x37]
    // 0xadf73c: DecompressPointer r1
    //     0xadf73c: add             x1, x1, HEAP, lsl #32
    // 0xadf740: r16 = Instance_PaymentType
    //     0xadf740: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xadf744: ldr             x16, [x16, #0x608]
    // 0xadf748: cmp             w1, w16
    // 0xadf74c: r16 = true
    //     0xadf74c: add             x16, NULL, #0x20  ; true
    // 0xadf750: r17 = false
    //     0xadf750: add             x17, NULL, #0x30  ; false
    // 0xadf754: csel            x0, x16, x17, eq
    // 0xadf758: stur            x0, [fp, #-0x10]
    // 0xadf75c: r0 = CampaignInformationView()
    //     0xadf75c: bl              #0xadf784  ; AllocateCampaignInformationViewStub -> CampaignInformationView (size=0x14)
    // 0xadf760: ldur            x1, [fp, #-8]
    // 0xadf764: StoreField: r0->field_b = r1
    //     0xadf764: stur            w1, [x0, #0xb]
    // 0xadf768: ldur            x1, [fp, #-0x10]
    // 0xadf76c: StoreField: r0->field_f = r1
    //     0xadf76c: stur            w1, [x0, #0xf]
    // 0xadf770: LeaveFrame
    //     0xadf770: mov             SP, fp
    //     0xadf774: ldp             fp, lr, [SP], #0x10
    // 0xadf778: ret
    //     0xadf778: ret             
    // 0xadf77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf77c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf780: b               #0xadf718
  }
  [closure] String <anonymous closure>(dynamic, CampaignMetadataQurban) {
    // ** addr: 0xadf790, size: 0xa4
    // 0xadf790: EnterFrame
    //     0xadf790: stp             fp, lr, [SP, #-0x10]!
    //     0xadf794: mov             fp, SP
    // 0xadf798: AllocStack(0x10)
    //     0xadf798: sub             SP, SP, #0x10
    // 0xadf79c: CheckStackOverflow
    //     0xadf79c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf7a0: cmp             SP, x16
    //     0xadf7a4: b.ls            #0xadf82c
    // 0xadf7a8: r1 = Null
    //     0xadf7a8: mov             x1, NULL
    // 0xadf7ac: r2 = 4
    //     0xadf7ac: movz            x2, #0x4
    // 0xadf7b0: r0 = AllocateArray()
    //     0xadf7b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadf7b4: stur            x0, [fp, #-8]
    // 0xadf7b8: r16 = "Rp"
    //     0xadf7b8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xadf7bc: ldr             x16, [x16, #0xf90]
    // 0xadf7c0: StoreField: r0->field_f = r16
    //     0xadf7c0: stur            w16, [x0, #0xf]
    // 0xadf7c4: ldr             x1, [fp, #0x10]
    // 0xadf7c8: LoadField: r2 = r1->field_b
    //     0xadf7c8: ldur            w2, [x1, #0xb]
    // 0xadf7cc: DecompressPointer r2
    //     0xadf7cc: add             x2, x2, HEAP, lsl #32
    // 0xadf7d0: cmp             w2, NULL
    // 0xadf7d4: b.ne            #0xadf7e0
    // 0xadf7d8: r1 = 0
    //     0xadf7d8: movz            x1, #0
    // 0xadf7dc: b               #0xadf7e4
    // 0xadf7e0: mov             x1, x2
    // 0xadf7e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf7e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf7e8: r0 = NumExtension.idr()
    //     0xadf7e8: bl              #0xadf068  ; [package:nuonline/common/extensions/int_extension.dart] ::NumExtension.idr
    // 0xadf7ec: ldur            x1, [fp, #-8]
    // 0xadf7f0: ArrayStore: r1[1] = r0  ; List_4
    //     0xadf7f0: add             x25, x1, #0x13
    //     0xadf7f4: str             w0, [x25]
    //     0xadf7f8: tbz             w0, #0, #0xadf814
    //     0xadf7fc: ldurb           w16, [x1, #-1]
    //     0xadf800: ldurb           w17, [x0, #-1]
    //     0xadf804: and             x16, x17, x16, lsr #2
    //     0xadf808: tst             x16, HEAP, lsr #32
    //     0xadf80c: b.eq            #0xadf814
    //     0xadf810: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadf814: ldur            x16, [fp, #-8]
    // 0xadf818: str             x16, [SP]
    // 0xadf81c: r0 = _interpolate()
    //     0xadf81c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xadf820: LeaveFrame
    //     0xadf820: mov             SP, fp
    //     0xadf824: ldp             fp, lr, [SP], #0x10
    // 0xadf828: ret
    //     0xadf828: ret             
    // 0xadf82c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf82c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf830: b               #0xadf7a8
  }
  [closure] String <anonymous closure>(dynamic, CampaignMetadataQurban) {
    // ** addr: 0xadf834, size: 0x24
    // 0xadf834: ldr             x1, [SP]
    // 0xadf838: LoadField: r2 = r1->field_7
    //     0xadf838: ldur            w2, [x1, #7]
    // 0xadf83c: DecompressPointer r2
    //     0xadf83c: add             x2, x2, HEAP, lsl #32
    // 0xadf840: cmp             w2, NULL
    // 0xadf844: b.ne            #0xadf850
    // 0xadf848: r0 = ""
    //     0xadf848: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xadf84c: b               #0xadf854
    // 0xadf850: mov             x0, x2
    // 0xadf854: ret
    //     0xadf854: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xadf858, size: 0xa4
    // 0xadf858: EnterFrame
    //     0xadf858: stp             fp, lr, [SP, #-0x10]!
    //     0xadf85c: mov             fp, SP
    // 0xadf860: AllocStack(0x8)
    //     0xadf860: sub             SP, SP, #8
    // 0xadf864: SetupParameters()
    //     0xadf864: ldr             x0, [fp, #0x10]
    //     0xadf868: ldur            w2, [x0, #0x17]
    //     0xadf86c: add             x2, x2, HEAP, lsl #32
    //     0xadf870: stur            x2, [fp, #-8]
    // 0xadf874: CheckStackOverflow
    //     0xadf874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf878: cmp             SP, x16
    //     0xadf87c: b.ls            #0xadf8f0
    // 0xadf880: LoadField: r0 = r2->field_b
    //     0xadf880: ldur            w0, [x2, #0xb]
    // 0xadf884: DecompressPointer r0
    //     0xadf884: add             x0, x0, HEAP, lsl #32
    // 0xadf888: LoadField: r1 = r0->field_f
    //     0xadf888: ldur            w1, [x0, #0xf]
    // 0xadf88c: DecompressPointer r1
    //     0xadf88c: add             x1, x1, HEAP, lsl #32
    // 0xadf890: r0 = controller()
    //     0xadf890: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadf894: LoadField: r1 = r0->field_47
    //     0xadf894: ldur            w1, [x0, #0x47]
    // 0xadf898: DecompressPointer r1
    //     0xadf898: add             x1, x1, HEAP, lsl #32
    // 0xadf89c: r0 = RxBoolExt.isFalse()
    //     0xadf89c: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xadf8a0: tbnz            w0, #4, #0xadf8b8
    // 0xadf8a4: r0 = Instance_SizedBox
    //     0xadf8a4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xadf8a8: ldr             x0, [x0, #0xc40]
    // 0xadf8ac: LeaveFrame
    //     0xadf8ac: mov             SP, fp
    //     0xadf8b0: ldp             fp, lr, [SP], #0x10
    // 0xadf8b4: ret
    //     0xadf8b4: ret             
    // 0xadf8b8: ldur            x0, [fp, #-8]
    // 0xadf8bc: LoadField: r1 = r0->field_f
    //     0xadf8bc: ldur            w1, [x0, #0xf]
    // 0xadf8c0: DecompressPointer r1
    //     0xadf8c0: add             x1, x1, HEAP, lsl #32
    // 0xadf8c4: cmp             w1, NULL
    // 0xadf8c8: b.eq            #0xadf8f8
    // 0xadf8cc: LoadField: r0 = r1->field_f
    //     0xadf8cc: ldur            w0, [x1, #0xf]
    // 0xadf8d0: DecompressPointer r0
    //     0xadf8d0: add             x0, x0, HEAP, lsl #32
    // 0xadf8d4: stur            x0, [fp, #-8]
    // 0xadf8d8: r0 = Text()
    //     0xadf8d8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadf8dc: ldur            x1, [fp, #-8]
    // 0xadf8e0: StoreField: r0->field_b = r1
    //     0xadf8e0: stur            w1, [x0, #0xb]
    // 0xadf8e4: LeaveFrame
    //     0xadf8e4: mov             SP, fp
    //     0xadf8e8: ldp             fp, lr, [SP], #0x10
    // 0xadf8ec: ret
    //     0xadf8ec: ret             
    // 0xadf8f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf8f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf8f4: b               #0xadf880
    // 0xadf8f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf8f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
