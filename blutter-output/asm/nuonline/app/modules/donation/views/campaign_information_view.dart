// lib: , url: package:nuonline/app/modules/donation/views/campaign_information_view.dart

// class id: 1050218, size: 0x8
class :: {
}

// class id: 5046, size: 0x14, field offset: 0xc
//   const constructor, 
class CampaignInformationView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8c8c4, size: 0x898
    // 0xb8c8c4: EnterFrame
    //     0xb8c8c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8c8c8: mov             fp, SP
    // 0xb8c8cc: AllocStack(0x70)
    //     0xb8c8cc: sub             SP, SP, #0x70
    // 0xb8c8d0: SetupParameters(CampaignInformationView this /* r1 => r1, fp-0x8 */)
    //     0xb8c8d0: stur            x1, [fp, #-8]
    // 0xb8c8d4: CheckStackOverflow
    //     0xb8c8d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8c8d8: cmp             SP, x16
    //     0xb8c8dc: b.ls            #0xb8d154
    // 0xb8c8e0: r1 = 1
    //     0xb8c8e0: movz            x1, #0x1
    // 0xb8c8e4: r0 = AllocateContext()
    //     0xb8c8e4: bl              #0xec126c  ; AllocateContextStub
    // 0xb8c8e8: mov             x1, x0
    // 0xb8c8ec: ldur            x0, [fp, #-8]
    // 0xb8c8f0: stur            x1, [fp, #-0x10]
    // 0xb8c8f4: StoreField: r1->field_f = r0
    //     0xb8c8f4: stur            w0, [x1, #0xf]
    // 0xb8c8f8: r0 = AppBar()
    //     0xb8c8f8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb8c8fc: stur            x0, [fp, #-0x18]
    // 0xb8c900: r16 = Instance_Text
    //     0xb8c900: add             x16, PP, #0x28, lsl #12  ; [pp+0x28358] Obj!Text@e219b1
    //     0xb8c904: ldr             x16, [x16, #0x358]
    // 0xb8c908: str             x16, [SP]
    // 0xb8c90c: mov             x1, x0
    // 0xb8c910: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xb8c910: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xb8c914: ldr             x4, [x4, #0x6e8]
    // 0xb8c918: r0 = AppBar()
    //     0xb8c918: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb8c91c: r1 = <Widget>
    //     0xb8c91c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8c920: r2 = 0
    //     0xb8c920: movz            x2, #0
    // 0xb8c924: r0 = _GrowableList()
    //     0xb8c924: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8c928: mov             x1, x0
    // 0xb8c92c: ldur            x0, [fp, #-8]
    // 0xb8c930: stur            x1, [fp, #-0x40]
    // 0xb8c934: LoadField: r2 = r0->field_b
    //     0xb8c934: ldur            w2, [x0, #0xb]
    // 0xb8c938: DecompressPointer r2
    //     0xb8c938: add             x2, x2, HEAP, lsl #32
    // 0xb8c93c: stur            x2, [fp, #-0x38]
    // 0xb8c940: LoadField: r3 = r2->field_33
    //     0xb8c940: ldur            w3, [x2, #0x33]
    // 0xb8c944: DecompressPointer r3
    //     0xb8c944: add             x3, x3, HEAP, lsl #32
    // 0xb8c948: LoadField: r4 = r3->field_7
    //     0xb8c948: ldur            x4, [x3, #7]
    // 0xb8c94c: stur            x4, [fp, #-0x30]
    // 0xb8c950: cmp             x4, #6
    // 0xb8c954: b.ne            #0xb8c9d8
    // 0xb8c958: LoadField: r3 = r2->field_3b
    //     0xb8c958: ldur            w3, [x2, #0x3b]
    // 0xb8c95c: DecompressPointer r3
    //     0xb8c95c: add             x3, x3, HEAP, lsl #32
    // 0xb8c960: LoadField: r5 = r3->field_13
    //     0xb8c960: ldur            w5, [x3, #0x13]
    // 0xb8c964: DecompressPointer r5
    //     0xb8c964: add             x5, x5, HEAP, lsl #32
    // 0xb8c968: stur            x5, [fp, #-0x28]
    // 0xb8c96c: LoadField: r6 = r3->field_f
    //     0xb8c96c: ldur            w6, [x3, #0xf]
    // 0xb8c970: DecompressPointer r6
    //     0xb8c970: add             x6, x6, HEAP, lsl #32
    // 0xb8c974: stur            x6, [fp, #-0x20]
    // 0xb8c978: r0 = NOrganizationListTile()
    //     0xb8c978: bl              #0xadf208  ; AllocateNOrganizationListTileStub -> NOrganizationListTile (size=0x14)
    // 0xb8c97c: mov             x3, x0
    // 0xb8c980: ldur            x0, [fp, #-0x28]
    // 0xb8c984: stur            x3, [fp, #-0x48]
    // 0xb8c988: StoreField: r3->field_b = r0
    //     0xb8c988: stur            w0, [x3, #0xb]
    // 0xb8c98c: ldur            x0, [fp, #-0x20]
    // 0xb8c990: StoreField: r3->field_f = r0
    //     0xb8c990: stur            w0, [x3, #0xf]
    // 0xb8c994: r1 = Null
    //     0xb8c994: mov             x1, NULL
    // 0xb8c998: r2 = 2
    //     0xb8c998: movz            x2, #0x2
    // 0xb8c99c: r0 = AllocateArray()
    //     0xb8c99c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8c9a0: mov             x2, x0
    // 0xb8c9a4: ldur            x0, [fp, #-0x48]
    // 0xb8c9a8: stur            x2, [fp, #-0x20]
    // 0xb8c9ac: StoreField: r2->field_f = r0
    //     0xb8c9ac: stur            w0, [x2, #0xf]
    // 0xb8c9b0: r1 = <Widget>
    //     0xb8c9b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8c9b4: r0 = AllocateGrowableArray()
    //     0xb8c9b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8c9b8: mov             x1, x0
    // 0xb8c9bc: ldur            x0, [fp, #-0x20]
    // 0xb8c9c0: StoreField: r1->field_f = r0
    //     0xb8c9c0: stur            w0, [x1, #0xf]
    // 0xb8c9c4: r0 = 2
    //     0xb8c9c4: movz            x0, #0x2
    // 0xb8c9c8: StoreField: r1->field_b = r0
    //     0xb8c9c8: stur            w0, [x1, #0xb]
    // 0xb8c9cc: mov             x2, x1
    // 0xb8c9d0: ldur            x1, [fp, #-0x40]
    // 0xb8c9d4: r0 = addAll()
    //     0xb8c9d4: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb8c9d8: ldur            x1, [fp, #-0x38]
    // 0xb8c9dc: LoadField: r0 = r1->field_37
    //     0xb8c9dc: ldur            w0, [x1, #0x37]
    // 0xb8c9e0: DecompressPointer r0
    //     0xb8c9e0: add             x0, x0, HEAP, lsl #32
    // 0xb8c9e4: cmp             w0, NULL
    // 0xb8c9e8: b.eq            #0xb8cb24
    // 0xb8c9ec: ldur            x2, [fp, #-0x40]
    // 0xb8c9f0: LoadField: r3 = r0->field_f
    //     0xb8c9f0: ldur            w3, [x0, #0xf]
    // 0xb8c9f4: DecompressPointer r3
    //     0xb8c9f4: add             x3, x3, HEAP, lsl #32
    // 0xb8c9f8: stur            x3, [fp, #-0x20]
    // 0xb8c9fc: r0 = NLabelButton()
    //     0xb8c9fc: bl              #0xadf214  ; AllocateNLabelButtonStub -> NLabelButton (size=0x24)
    // 0xb8ca00: mov             x3, x0
    // 0xb8ca04: ldur            x0, [fp, #-0x20]
    // 0xb8ca08: stur            x3, [fp, #-0x28]
    // 0xb8ca0c: StoreField: r3->field_b = r0
    //     0xb8ca0c: stur            w0, [x3, #0xb]
    // 0xb8ca10: r1 = Null
    //     0xb8ca10: mov             x1, NULL
    // 0xb8ca14: r2 = 2
    //     0xb8ca14: movz            x2, #0x2
    // 0xb8ca18: r0 = AllocateArray()
    //     0xb8ca18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8ca1c: mov             x2, x0
    // 0xb8ca20: ldur            x0, [fp, #-0x28]
    // 0xb8ca24: stur            x2, [fp, #-0x20]
    // 0xb8ca28: StoreField: r2->field_f = r0
    //     0xb8ca28: stur            w0, [x2, #0xf]
    // 0xb8ca2c: r1 = <Widget>
    //     0xb8ca2c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8ca30: r0 = AllocateGrowableArray()
    //     0xb8ca30: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8ca34: mov             x1, x0
    // 0xb8ca38: ldur            x0, [fp, #-0x20]
    // 0xb8ca3c: stur            x1, [fp, #-0x28]
    // 0xb8ca40: StoreField: r1->field_f = r0
    //     0xb8ca40: stur            w0, [x1, #0xf]
    // 0xb8ca44: r2 = 2
    //     0xb8ca44: movz            x2, #0x2
    // 0xb8ca48: StoreField: r1->field_b = r2
    //     0xb8ca48: stur            w2, [x1, #0xb]
    // 0xb8ca4c: r0 = Column()
    //     0xb8ca4c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8ca50: mov             x2, x0
    // 0xb8ca54: r0 = Instance_Axis
    //     0xb8ca54: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8ca58: stur            x2, [fp, #-0x20]
    // 0xb8ca5c: StoreField: r2->field_f = r0
    //     0xb8ca5c: stur            w0, [x2, #0xf]
    // 0xb8ca60: r0 = Instance_MainAxisAlignment
    //     0xb8ca60: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8ca64: ldr             x0, [x0, #0x730]
    // 0xb8ca68: StoreField: r2->field_13 = r0
    //     0xb8ca68: stur            w0, [x2, #0x13]
    // 0xb8ca6c: r0 = Instance_MainAxisSize
    //     0xb8ca6c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8ca70: ldr             x0, [x0, #0x738]
    // 0xb8ca74: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8ca74: stur            w0, [x2, #0x17]
    // 0xb8ca78: r0 = Instance_CrossAxisAlignment
    //     0xb8ca78: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb8ca7c: ldr             x0, [x0, #0x68]
    // 0xb8ca80: StoreField: r2->field_1b = r0
    //     0xb8ca80: stur            w0, [x2, #0x1b]
    // 0xb8ca84: r0 = Instance_VerticalDirection
    //     0xb8ca84: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8ca88: ldr             x0, [x0, #0x748]
    // 0xb8ca8c: StoreField: r2->field_23 = r0
    //     0xb8ca8c: stur            w0, [x2, #0x23]
    // 0xb8ca90: r0 = Instance_Clip
    //     0xb8ca90: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8ca94: ldr             x0, [x0, #0x750]
    // 0xb8ca98: StoreField: r2->field_2b = r0
    //     0xb8ca98: stur            w0, [x2, #0x2b]
    // 0xb8ca9c: StoreField: r2->field_2f = rZR
    //     0xb8ca9c: stur            xzr, [x2, #0x2f]
    // 0xb8caa0: ldur            x0, [fp, #-0x28]
    // 0xb8caa4: StoreField: r2->field_b = r0
    //     0xb8caa4: stur            w0, [x2, #0xb]
    // 0xb8caa8: ldur            x0, [fp, #-0x40]
    // 0xb8caac: LoadField: r1 = r0->field_b
    //     0xb8caac: ldur            w1, [x0, #0xb]
    // 0xb8cab0: LoadField: r3 = r0->field_f
    //     0xb8cab0: ldur            w3, [x0, #0xf]
    // 0xb8cab4: DecompressPointer r3
    //     0xb8cab4: add             x3, x3, HEAP, lsl #32
    // 0xb8cab8: LoadField: r4 = r3->field_b
    //     0xb8cab8: ldur            w4, [x3, #0xb]
    // 0xb8cabc: r3 = LoadInt32Instr(r1)
    //     0xb8cabc: sbfx            x3, x1, #1, #0x1f
    // 0xb8cac0: stur            x3, [fp, #-0x50]
    // 0xb8cac4: r1 = LoadInt32Instr(r4)
    //     0xb8cac4: sbfx            x1, x4, #1, #0x1f
    // 0xb8cac8: cmp             x3, x1
    // 0xb8cacc: b.ne            #0xb8cad8
    // 0xb8cad0: mov             x1, x0
    // 0xb8cad4: r0 = _growToNextCapacity()
    //     0xb8cad4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8cad8: ldur            x2, [fp, #-0x40]
    // 0xb8cadc: ldur            x3, [fp, #-0x50]
    // 0xb8cae0: add             x0, x3, #1
    // 0xb8cae4: lsl             x1, x0, #1
    // 0xb8cae8: StoreField: r2->field_b = r1
    //     0xb8cae8: stur            w1, [x2, #0xb]
    // 0xb8caec: LoadField: r1 = r2->field_f
    //     0xb8caec: ldur            w1, [x2, #0xf]
    // 0xb8caf0: DecompressPointer r1
    //     0xb8caf0: add             x1, x1, HEAP, lsl #32
    // 0xb8caf4: ldur            x0, [fp, #-0x20]
    // 0xb8caf8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8caf8: add             x25, x1, x3, lsl #2
    //     0xb8cafc: add             x25, x25, #0xf
    //     0xb8cb00: str             w0, [x25]
    //     0xb8cb04: tbz             w0, #0, #0xb8cb20
    //     0xb8cb08: ldurb           w16, [x1, #-1]
    //     0xb8cb0c: ldurb           w17, [x0, #-1]
    //     0xb8cb10: and             x16, x17, x16, lsr #2
    //     0xb8cb14: tst             x16, HEAP, lsr #32
    //     0xb8cb18: b.eq            #0xb8cb20
    //     0xb8cb1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8cb20: b               #0xb8cb28
    // 0xb8cb24: ldur            x2, [fp, #-0x40]
    // 0xb8cb28: LoadField: r0 = r2->field_b
    //     0xb8cb28: ldur            w0, [x2, #0xb]
    // 0xb8cb2c: LoadField: r1 = r2->field_f
    //     0xb8cb2c: ldur            w1, [x2, #0xf]
    // 0xb8cb30: DecompressPointer r1
    //     0xb8cb30: add             x1, x1, HEAP, lsl #32
    // 0xb8cb34: LoadField: r3 = r1->field_b
    //     0xb8cb34: ldur            w3, [x1, #0xb]
    // 0xb8cb38: r4 = LoadInt32Instr(r0)
    //     0xb8cb38: sbfx            x4, x0, #1, #0x1f
    // 0xb8cb3c: stur            x4, [fp, #-0x50]
    // 0xb8cb40: r0 = LoadInt32Instr(r3)
    //     0xb8cb40: sbfx            x0, x3, #1, #0x1f
    // 0xb8cb44: cmp             x4, x0
    // 0xb8cb48: b.ne            #0xb8cb54
    // 0xb8cb4c: mov             x1, x2
    // 0xb8cb50: r0 = _growToNextCapacity()
    //     0xb8cb50: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8cb54: ldur            x1, [fp, #-0x40]
    // 0xb8cb58: ldur            x2, [fp, #-0x38]
    // 0xb8cb5c: ldur            x0, [fp, #-0x50]
    // 0xb8cb60: add             x3, x0, #1
    // 0xb8cb64: lsl             x4, x3, #1
    // 0xb8cb68: StoreField: r1->field_b = r4
    //     0xb8cb68: stur            w4, [x1, #0xb]
    // 0xb8cb6c: LoadField: r3 = r1->field_f
    //     0xb8cb6c: ldur            w3, [x1, #0xf]
    // 0xb8cb70: DecompressPointer r3
    //     0xb8cb70: add             x3, x3, HEAP, lsl #32
    // 0xb8cb74: add             x4, x3, x0, lsl #2
    // 0xb8cb78: r16 = Instance_SizedBox
    //     0xb8cb78: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb8cb7c: ldr             x16, [x16, #0xfb0]
    // 0xb8cb80: StoreField: r4->field_f = r16
    //     0xb8cb80: stur            w16, [x4, #0xf]
    // 0xb8cb84: LoadField: r0 = r2->field_f
    //     0xb8cb84: ldur            w0, [x2, #0xf]
    // 0xb8cb88: DecompressPointer r0
    //     0xb8cb88: add             x0, x0, HEAP, lsl #32
    // 0xb8cb8c: stur            x0, [fp, #-0x20]
    // 0xb8cb90: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8cb90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8cb94: ldr             x0, [x0, #0x2670]
    //     0xb8cb98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8cb9c: cmp             w0, w16
    //     0xb8cba0: b.ne            #0xb8cbac
    //     0xb8cba4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8cba8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8cbac: r0 = GetNavigation.textTheme()
    //     0xb8cbac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8cbb0: LoadField: r1 = r0->field_f
    //     0xb8cbb0: ldur            w1, [x0, #0xf]
    // 0xb8cbb4: DecompressPointer r1
    //     0xb8cbb4: add             x1, x1, HEAP, lsl #32
    // 0xb8cbb8: stur            x1, [fp, #-0x28]
    // 0xb8cbbc: r0 = Text()
    //     0xb8cbbc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8cbc0: mov             x2, x0
    // 0xb8cbc4: ldur            x0, [fp, #-0x20]
    // 0xb8cbc8: stur            x2, [fp, #-0x48]
    // 0xb8cbcc: StoreField: r2->field_b = r0
    //     0xb8cbcc: stur            w0, [x2, #0xb]
    // 0xb8cbd0: ldur            x0, [fp, #-0x28]
    // 0xb8cbd4: StoreField: r2->field_13 = r0
    //     0xb8cbd4: stur            w0, [x2, #0x13]
    // 0xb8cbd8: ldur            x0, [fp, #-0x40]
    // 0xb8cbdc: LoadField: r1 = r0->field_b
    //     0xb8cbdc: ldur            w1, [x0, #0xb]
    // 0xb8cbe0: LoadField: r3 = r0->field_f
    //     0xb8cbe0: ldur            w3, [x0, #0xf]
    // 0xb8cbe4: DecompressPointer r3
    //     0xb8cbe4: add             x3, x3, HEAP, lsl #32
    // 0xb8cbe8: LoadField: r4 = r3->field_b
    //     0xb8cbe8: ldur            w4, [x3, #0xb]
    // 0xb8cbec: r3 = LoadInt32Instr(r1)
    //     0xb8cbec: sbfx            x3, x1, #1, #0x1f
    // 0xb8cbf0: stur            x3, [fp, #-0x50]
    // 0xb8cbf4: r1 = LoadInt32Instr(r4)
    //     0xb8cbf4: sbfx            x1, x4, #1, #0x1f
    // 0xb8cbf8: cmp             x3, x1
    // 0xb8cbfc: b.ne            #0xb8cc08
    // 0xb8cc00: mov             x1, x0
    // 0xb8cc04: r0 = _growToNextCapacity()
    //     0xb8cc04: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8cc08: ldur            x2, [fp, #-0x40]
    // 0xb8cc0c: ldur            x4, [fp, #-0x30]
    // 0xb8cc10: ldur            x3, [fp, #-0x50]
    // 0xb8cc14: add             x0, x3, #1
    // 0xb8cc18: lsl             x1, x0, #1
    // 0xb8cc1c: StoreField: r2->field_b = r1
    //     0xb8cc1c: stur            w1, [x2, #0xb]
    // 0xb8cc20: LoadField: r1 = r2->field_f
    //     0xb8cc20: ldur            w1, [x2, #0xf]
    // 0xb8cc24: DecompressPointer r1
    //     0xb8cc24: add             x1, x1, HEAP, lsl #32
    // 0xb8cc28: ldur            x0, [fp, #-0x48]
    // 0xb8cc2c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8cc2c: add             x25, x1, x3, lsl #2
    //     0xb8cc30: add             x25, x25, #0xf
    //     0xb8cc34: str             w0, [x25]
    //     0xb8cc38: tbz             w0, #0, #0xb8cc54
    //     0xb8cc3c: ldurb           w16, [x1, #-1]
    //     0xb8cc40: ldurb           w17, [x0, #-1]
    //     0xb8cc44: and             x16, x17, x16, lsr #2
    //     0xb8cc48: tst             x16, HEAP, lsr #32
    //     0xb8cc4c: b.eq            #0xb8cc54
    //     0xb8cc50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8cc54: cmp             x4, #6
    // 0xb8cc58: b.ne            #0xb8cce0
    // 0xb8cc5c: ldur            x1, [fp, #-0x38]
    // 0xb8cc60: LoadField: r0 = r1->field_3b
    //     0xb8cc60: ldur            w0, [x1, #0x3b]
    // 0xb8cc64: DecompressPointer r0
    //     0xb8cc64: add             x0, x0, HEAP, lsl #32
    // 0xb8cc68: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb8cc68: ldur            w3, [x0, #0x17]
    // 0xb8cc6c: DecompressPointer r3
    //     0xb8cc6c: add             x3, x3, HEAP, lsl #32
    // 0xb8cc70: cmp             w3, NULL
    // 0xb8cc74: b.ne            #0xb8cc80
    // 0xb8cc78: r0 = ""
    //     0xb8cc78: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb8cc7c: b               #0xb8cc84
    // 0xb8cc80: mov             x0, x3
    // 0xb8cc84: stur            x0, [fp, #-0x20]
    // 0xb8cc88: r0 = Text()
    //     0xb8cc88: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8cc8c: mov             x3, x0
    // 0xb8cc90: ldur            x0, [fp, #-0x20]
    // 0xb8cc94: stur            x3, [fp, #-0x28]
    // 0xb8cc98: StoreField: r3->field_b = r0
    //     0xb8cc98: stur            w0, [x3, #0xb]
    // 0xb8cc9c: r1 = Null
    //     0xb8cc9c: mov             x1, NULL
    // 0xb8cca0: r2 = 2
    //     0xb8cca0: movz            x2, #0x2
    // 0xb8cca4: r0 = AllocateArray()
    //     0xb8cca4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8cca8: mov             x2, x0
    // 0xb8ccac: ldur            x0, [fp, #-0x28]
    // 0xb8ccb0: stur            x2, [fp, #-0x20]
    // 0xb8ccb4: StoreField: r2->field_f = r0
    //     0xb8ccb4: stur            w0, [x2, #0xf]
    // 0xb8ccb8: r1 = <Widget>
    //     0xb8ccb8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8ccbc: r0 = AllocateGrowableArray()
    //     0xb8ccbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8ccc0: mov             x1, x0
    // 0xb8ccc4: ldur            x0, [fp, #-0x20]
    // 0xb8ccc8: StoreField: r1->field_f = r0
    //     0xb8ccc8: stur            w0, [x1, #0xf]
    // 0xb8cccc: r0 = 2
    //     0xb8cccc: movz            x0, #0x2
    // 0xb8ccd0: StoreField: r1->field_b = r0
    //     0xb8ccd0: stur            w0, [x1, #0xb]
    // 0xb8ccd4: mov             x2, x1
    // 0xb8ccd8: ldur            x1, [fp, #-0x40]
    // 0xb8ccdc: r0 = addAll()
    //     0xb8ccdc: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb8cce0: ldur            x0, [fp, #-0x40]
    // 0xb8cce4: LoadField: r1 = r0->field_b
    //     0xb8cce4: ldur            w1, [x0, #0xb]
    // 0xb8cce8: LoadField: r2 = r0->field_f
    //     0xb8cce8: ldur            w2, [x0, #0xf]
    // 0xb8ccec: DecompressPointer r2
    //     0xb8ccec: add             x2, x2, HEAP, lsl #32
    // 0xb8ccf0: LoadField: r3 = r2->field_b
    //     0xb8ccf0: ldur            w3, [x2, #0xb]
    // 0xb8ccf4: r2 = LoadInt32Instr(r1)
    //     0xb8ccf4: sbfx            x2, x1, #1, #0x1f
    // 0xb8ccf8: stur            x2, [fp, #-0x30]
    // 0xb8ccfc: r1 = LoadInt32Instr(r3)
    //     0xb8ccfc: sbfx            x1, x3, #1, #0x1f
    // 0xb8cd00: cmp             x2, x1
    // 0xb8cd04: b.ne            #0xb8cd10
    // 0xb8cd08: mov             x1, x0
    // 0xb8cd0c: r0 = _growToNextCapacity()
    //     0xb8cd0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8cd10: ldur            x1, [fp, #-0x40]
    // 0xb8cd14: ldur            x2, [fp, #-0x38]
    // 0xb8cd18: ldur            x0, [fp, #-0x30]
    // 0xb8cd1c: add             x3, x0, #1
    // 0xb8cd20: stur            x3, [fp, #-0x50]
    // 0xb8cd24: lsl             x4, x3, #1
    // 0xb8cd28: StoreField: r1->field_b = r4
    //     0xb8cd28: stur            w4, [x1, #0xb]
    // 0xb8cd2c: LoadField: r4 = r1->field_f
    //     0xb8cd2c: ldur            w4, [x1, #0xf]
    // 0xb8cd30: DecompressPointer r4
    //     0xb8cd30: add             x4, x4, HEAP, lsl #32
    // 0xb8cd34: stur            x4, [fp, #-0x20]
    // 0xb8cd38: add             x5, x4, x0, lsl #2
    // 0xb8cd3c: r16 = Instance_SizedBox
    //     0xb8cd3c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8cd40: ldr             x16, [x16, #0xfe8]
    // 0xb8cd44: StoreField: r5->field_f = r16
    //     0xb8cd44: stur            w16, [x5, #0xf]
    // 0xb8cd48: r0 = Radius()
    //     0xb8cd48: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8cd4c: d0 = 8.000000
    //     0xb8cd4c: fmov            d0, #8.00000000
    // 0xb8cd50: stur            x0, [fp, #-0x28]
    // 0xb8cd54: StoreField: r0->field_7 = d0
    //     0xb8cd54: stur            d0, [x0, #7]
    // 0xb8cd58: StoreField: r0->field_f = d0
    //     0xb8cd58: stur            d0, [x0, #0xf]
    // 0xb8cd5c: r0 = BorderRadius()
    //     0xb8cd5c: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8cd60: mov             x1, x0
    // 0xb8cd64: ldur            x0, [fp, #-0x28]
    // 0xb8cd68: stur            x1, [fp, #-0x48]
    // 0xb8cd6c: StoreField: r1->field_7 = r0
    //     0xb8cd6c: stur            w0, [x1, #7]
    // 0xb8cd70: StoreField: r1->field_b = r0
    //     0xb8cd70: stur            w0, [x1, #0xb]
    // 0xb8cd74: StoreField: r1->field_f = r0
    //     0xb8cd74: stur            w0, [x1, #0xf]
    // 0xb8cd78: StoreField: r1->field_13 = r0
    //     0xb8cd78: stur            w0, [x1, #0x13]
    // 0xb8cd7c: ldur            x0, [fp, #-0x38]
    // 0xb8cd80: LoadField: r2 = r0->field_1b
    //     0xb8cd80: ldur            w2, [x0, #0x1b]
    // 0xb8cd84: DecompressPointer r2
    //     0xb8cd84: add             x2, x2, HEAP, lsl #32
    // 0xb8cd88: stur            x2, [fp, #-0x28]
    // 0xb8cd8c: r0 = NFadeInImageNetwork()
    //     0xb8cd8c: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xb8cd90: mov             x1, x0
    // 0xb8cd94: ldur            x0, [fp, #-0x28]
    // 0xb8cd98: stur            x1, [fp, #-0x58]
    // 0xb8cd9c: StoreField: r1->field_b = r0
    //     0xb8cd9c: stur            w0, [x1, #0xb]
    // 0xb8cda0: r0 = "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xb8cda0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a18] "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xb8cda4: ldr             x0, [x0, #0xa18]
    // 0xb8cda8: StoreField: r1->field_f = r0
    //     0xb8cda8: stur            w0, [x1, #0xf]
    // 0xb8cdac: r0 = "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xb8cdac: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a20] "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xb8cdb0: ldr             x0, [x0, #0xa20]
    // 0xb8cdb4: StoreField: r1->field_13 = r0
    //     0xb8cdb4: stur            w0, [x1, #0x13]
    // 0xb8cdb8: r0 = Instance_BoxFit
    //     0xb8cdb8: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xb8cdbc: ldr             x0, [x0, #0xa28]
    // 0xb8cdc0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8cdc0: stur            w0, [x1, #0x17]
    // 0xb8cdc4: r0 = AspectRatio()
    //     0xb8cdc4: bl              #0x9d2c98  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb8cdc8: d0 = 1.780952
    //     0xb8cdc8: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2ef20] IMM: double(1.7809523809523808) from 0x3ffc7ec7ec7ec7ec
    //     0xb8cdcc: ldr             d0, [x17, #0xf20]
    // 0xb8cdd0: stur            x0, [fp, #-0x28]
    // 0xb8cdd4: StoreField: r0->field_f = d0
    //     0xb8cdd4: stur            d0, [x0, #0xf]
    // 0xb8cdd8: ldur            x1, [fp, #-0x58]
    // 0xb8cddc: StoreField: r0->field_b = r1
    //     0xb8cddc: stur            w1, [x0, #0xb]
    // 0xb8cde0: r0 = ClipRRect()
    //     0xb8cde0: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb8cde4: mov             x2, x0
    // 0xb8cde8: ldur            x0, [fp, #-0x48]
    // 0xb8cdec: stur            x2, [fp, #-0x58]
    // 0xb8cdf0: StoreField: r2->field_f = r0
    //     0xb8cdf0: stur            w0, [x2, #0xf]
    // 0xb8cdf4: r0 = Instance_Clip
    //     0xb8cdf4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xb8cdf8: ldr             x0, [x0, #0x4f8]
    // 0xb8cdfc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8cdfc: stur            w0, [x2, #0x17]
    // 0xb8ce00: ldur            x0, [fp, #-0x28]
    // 0xb8ce04: StoreField: r2->field_b = r0
    //     0xb8ce04: stur            w0, [x2, #0xb]
    // 0xb8ce08: ldur            x0, [fp, #-0x20]
    // 0xb8ce0c: LoadField: r1 = r0->field_b
    //     0xb8ce0c: ldur            w1, [x0, #0xb]
    // 0xb8ce10: r0 = LoadInt32Instr(r1)
    //     0xb8ce10: sbfx            x0, x1, #1, #0x1f
    // 0xb8ce14: ldur            x3, [fp, #-0x50]
    // 0xb8ce18: cmp             x3, x0
    // 0xb8ce1c: b.ne            #0xb8ce28
    // 0xb8ce20: ldur            x1, [fp, #-0x40]
    // 0xb8ce24: r0 = _growToNextCapacity()
    //     0xb8ce24: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8ce28: ldur            x3, [fp, #-0x40]
    // 0xb8ce2c: ldur            x2, [fp, #-0x50]
    // 0xb8ce30: add             x4, x2, #1
    // 0xb8ce34: stur            x4, [fp, #-0x30]
    // 0xb8ce38: lsl             x0, x4, #1
    // 0xb8ce3c: StoreField: r3->field_b = r0
    //     0xb8ce3c: stur            w0, [x3, #0xb]
    // 0xb8ce40: LoadField: r5 = r3->field_f
    //     0xb8ce40: ldur            w5, [x3, #0xf]
    // 0xb8ce44: DecompressPointer r5
    //     0xb8ce44: add             x5, x5, HEAP, lsl #32
    // 0xb8ce48: mov             x1, x5
    // 0xb8ce4c: ldur            x0, [fp, #-0x58]
    // 0xb8ce50: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8ce50: add             x25, x1, x2, lsl #2
    //     0xb8ce54: add             x25, x25, #0xf
    //     0xb8ce58: str             w0, [x25]
    //     0xb8ce5c: tbz             w0, #0, #0xb8ce78
    //     0xb8ce60: ldurb           w16, [x1, #-1]
    //     0xb8ce64: ldurb           w17, [x0, #-1]
    //     0xb8ce68: and             x16, x17, x16, lsr #2
    //     0xb8ce6c: tst             x16, HEAP, lsr #32
    //     0xb8ce70: b.eq            #0xb8ce78
    //     0xb8ce74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8ce78: LoadField: r0 = r5->field_b
    //     0xb8ce78: ldur            w0, [x5, #0xb]
    // 0xb8ce7c: r1 = LoadInt32Instr(r0)
    //     0xb8ce7c: sbfx            x1, x0, #1, #0x1f
    // 0xb8ce80: cmp             x4, x1
    // 0xb8ce84: b.ne            #0xb8ce90
    // 0xb8ce88: mov             x1, x3
    // 0xb8ce8c: r0 = _growToNextCapacity()
    //     0xb8ce8c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8ce90: ldur            x1, [fp, #-0x40]
    // 0xb8ce94: ldur            x2, [fp, #-0x38]
    // 0xb8ce98: ldur            x0, [fp, #-0x30]
    // 0xb8ce9c: add             x3, x0, #1
    // 0xb8cea0: stur            x3, [fp, #-0x50]
    // 0xb8cea4: lsl             x4, x3, #1
    // 0xb8cea8: StoreField: r1->field_b = r4
    //     0xb8cea8: stur            w4, [x1, #0xb]
    // 0xb8ceac: LoadField: r4 = r1->field_f
    //     0xb8ceac: ldur            w4, [x1, #0xf]
    // 0xb8ceb0: DecompressPointer r4
    //     0xb8ceb0: add             x4, x4, HEAP, lsl #32
    // 0xb8ceb4: stur            x4, [fp, #-0x28]
    // 0xb8ceb8: add             x5, x4, x0, lsl #2
    // 0xb8cebc: r16 = Instance_SizedBox
    //     0xb8cebc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8cec0: ldr             x16, [x16, #0xfe8]
    // 0xb8cec4: StoreField: r5->field_f = r16
    //     0xb8cec4: stur            w16, [x5, #0xf]
    // 0xb8cec8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb8cec8: ldur            w0, [x2, #0x17]
    // 0xb8cecc: DecompressPointer r0
    //     0xb8cecc: add             x0, x0, HEAP, lsl #32
    // 0xb8ced0: stur            x0, [fp, #-0x20]
    // 0xb8ced4: r0 = ArticleContentHtml()
    //     0xb8ced4: bl              #0xa36cb4  ; AllocateArticleContentHtmlStub -> ArticleContentHtml (size=0x30)
    // 0xb8ced8: mov             x2, x0
    // 0xb8cedc: ldur            x0, [fp, #-0x20]
    // 0xb8cee0: stur            x2, [fp, #-0x48]
    // 0xb8cee4: StoreField: r2->field_b = r0
    //     0xb8cee4: stur            w0, [x2, #0xb]
    // 0xb8cee8: r0 = Instance_ReadingPref
    //     0xb8cee8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f660] Obj!ReadingPref@e25be1
    //     0xb8ceec: ldr             x0, [x0, #0x660]
    // 0xb8cef0: StoreField: r2->field_f = r0
    //     0xb8cef0: stur            w0, [x2, #0xf]
    // 0xb8cef4: r0 = true
    //     0xb8cef4: add             x0, NULL, #0x20  ; true
    // 0xb8cef8: StoreField: r2->field_13 = r0
    //     0xb8cef8: stur            w0, [x2, #0x13]
    // 0xb8cefc: r3 = false
    //     0xb8cefc: add             x3, NULL, #0x30  ; false
    // 0xb8cf00: ArrayStore: r2[0] = r3  ; List_4
    //     0xb8cf00: stur            w3, [x2, #0x17]
    // 0xb8cf04: r1 = const []
    //     0xb8cf04: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d530] List<ArticleInsertion>(0)
    //     0xb8cf08: ldr             x1, [x1, #0x530]
    // 0xb8cf0c: StoreField: r2->field_23 = r1
    //     0xb8cf0c: stur            w1, [x2, #0x23]
    // 0xb8cf10: StoreField: r2->field_27 = r1
    //     0xb8cf10: stur            w1, [x2, #0x27]
    // 0xb8cf14: r1 = const []
    //     0xb8cf14: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d538] List<Author>(0)
    //     0xb8cf18: ldr             x1, [x1, #0x538]
    // 0xb8cf1c: StoreField: r2->field_1f = r1
    //     0xb8cf1c: stur            w1, [x2, #0x1f]
    // 0xb8cf20: StoreField: r2->field_2b = r0
    //     0xb8cf20: stur            w0, [x2, #0x2b]
    // 0xb8cf24: ldur            x1, [fp, #-0x28]
    // 0xb8cf28: LoadField: r4 = r1->field_b
    //     0xb8cf28: ldur            w4, [x1, #0xb]
    // 0xb8cf2c: r1 = LoadInt32Instr(r4)
    //     0xb8cf2c: sbfx            x1, x4, #1, #0x1f
    // 0xb8cf30: ldur            x4, [fp, #-0x50]
    // 0xb8cf34: cmp             x4, x1
    // 0xb8cf38: b.ne            #0xb8cf44
    // 0xb8cf3c: ldur            x1, [fp, #-0x40]
    // 0xb8cf40: r0 = _growToNextCapacity()
    //     0xb8cf40: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8cf44: ldur            x4, [fp, #-8]
    // 0xb8cf48: ldur            x3, [fp, #-0x40]
    // 0xb8cf4c: ldur            x2, [fp, #-0x50]
    // 0xb8cf50: add             x0, x2, #1
    // 0xb8cf54: lsl             x1, x0, #1
    // 0xb8cf58: StoreField: r3->field_b = r1
    //     0xb8cf58: stur            w1, [x3, #0xb]
    // 0xb8cf5c: LoadField: r1 = r3->field_f
    //     0xb8cf5c: ldur            w1, [x3, #0xf]
    // 0xb8cf60: DecompressPointer r1
    //     0xb8cf60: add             x1, x1, HEAP, lsl #32
    // 0xb8cf64: ldur            x0, [fp, #-0x48]
    // 0xb8cf68: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8cf68: add             x25, x1, x2, lsl #2
    //     0xb8cf6c: add             x25, x25, #0xf
    //     0xb8cf70: str             w0, [x25]
    //     0xb8cf74: tbz             w0, #0, #0xb8cf90
    //     0xb8cf78: ldurb           w16, [x1, #-1]
    //     0xb8cf7c: ldurb           w17, [x0, #-1]
    //     0xb8cf80: and             x16, x17, x16, lsr #2
    //     0xb8cf84: tst             x16, HEAP, lsr #32
    //     0xb8cf88: b.eq            #0xb8cf90
    //     0xb8cf8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8cf90: r0 = ListView()
    //     0xb8cf90: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8cf94: stur            x0, [fp, #-0x20]
    // 0xb8cf98: r16 = Instance_EdgeInsets
    //     0xb8cf98: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb8cf9c: r30 = Instance_ClampingScrollPhysics
    //     0xb8cf9c: add             lr, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xb8cfa0: ldr             lr, [lr, #0x410]
    // 0xb8cfa4: stp             lr, x16, [SP]
    // 0xb8cfa8: mov             x1, x0
    // 0xb8cfac: ldur            x2, [fp, #-0x40]
    // 0xb8cfb0: r4 = const [0, 0x4, 0x2, 0x2, padding, 0x2, physics, 0x3, null]
    //     0xb8cfb0: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2da68] List(9) [0, 0x4, 0x2, 0x2, "padding", 0x2, "physics", 0x3, Null]
    //     0xb8cfb4: ldr             x4, [x4, #0xa68]
    // 0xb8cfb8: r0 = ListView()
    //     0xb8cfb8: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb8cfbc: ldur            x0, [fp, #-8]
    // 0xb8cfc0: LoadField: r1 = r0->field_f
    //     0xb8cfc0: ldur            w1, [x0, #0xf]
    // 0xb8cfc4: DecompressPointer r1
    //     0xb8cfc4: add             x1, x1, HEAP, lsl #32
    // 0xb8cfc8: tbnz            w1, #4, #0xb8d014
    // 0xb8cfcc: ldur            x2, [fp, #-0x10]
    // 0xb8cfd0: r1 = Function '<anonymous closure>':.
    //     0xb8cfd0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35610] AnonymousClosure: (0xb8d284), in [package:nuonline/app/modules/donation/views/campaign_information_view.dart] CampaignInformationView::build (0xb8c8c4)
    //     0xb8cfd4: ldr             x1, [x1, #0x610]
    // 0xb8cfd8: r0 = AllocateClosure()
    //     0xb8cfd8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8cfdc: stur            x0, [fp, #-8]
    // 0xb8cfe0: r0 = TextButton()
    //     0xb8cfe0: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb8cfe4: mov             x1, x0
    // 0xb8cfe8: ldur            x0, [fp, #-8]
    // 0xb8cfec: StoreField: r1->field_b = r0
    //     0xb8cfec: stur            w0, [x1, #0xb]
    // 0xb8cff0: r0 = false
    //     0xb8cff0: add             x0, NULL, #0x30  ; false
    // 0xb8cff4: StoreField: r1->field_27 = r0
    //     0xb8cff4: stur            w0, [x1, #0x27]
    // 0xb8cff8: r2 = true
    //     0xb8cff8: add             x2, NULL, #0x20  ; true
    // 0xb8cffc: StoreField: r1->field_2f = r2
    //     0xb8cffc: stur            w2, [x1, #0x2f]
    // 0xb8d000: r3 = Instance_Text
    //     0xb8d000: add             x3, PP, #0x35, lsl #12  ; [pp+0x35618] Obj!Text@e23a31
    //     0xb8d004: ldr             x3, [x3, #0x618]
    // 0xb8d008: StoreField: r1->field_37 = r3
    //     0xb8d008: stur            w3, [x1, #0x37]
    // 0xb8d00c: mov             x4, x1
    // 0xb8d010: b               #0xb8d080
    // 0xb8d014: r2 = true
    //     0xb8d014: add             x2, NULL, #0x20  ; true
    // 0xb8d018: r0 = false
    //     0xb8d018: add             x0, NULL, #0x30  ; false
    // 0xb8d01c: ldur            x1, [fp, #-0x38]
    // 0xb8d020: r0 = isEnded()
    //     0xb8d020: bl              #0x91eb50  ; [package:nuonline/app/data/models/campaign.dart] Campaign::isEnded
    // 0xb8d024: tbnz            w0, #4, #0xb8d034
    // 0xb8d028: r0 = Instance_Text
    //     0xb8d028: add             x0, PP, #0x35, lsl #12  ; [pp+0x35620] Obj!Text@e239e1
    //     0xb8d02c: ldr             x0, [x0, #0x620]
    // 0xb8d030: b               #0xb8d03c
    // 0xb8d034: r0 = Instance_Text
    //     0xb8d034: add             x0, PP, #0x35, lsl #12  ; [pp+0x35618] Obj!Text@e23a31
    //     0xb8d038: ldr             x0, [x0, #0x618]
    // 0xb8d03c: ldur            x2, [fp, #-0x10]
    // 0xb8d040: stur            x0, [fp, #-8]
    // 0xb8d044: r1 = Function '<anonymous closure>':.
    //     0xb8d044: add             x1, PP, #0x35, lsl #12  ; [pp+0x35628] AnonymousClosure: (0xb8d15c), in [package:nuonline/app/modules/donation/views/campaign_information_view.dart] CampaignInformationView::build (0xb8c8c4)
    //     0xb8d048: ldr             x1, [x1, #0x628]
    // 0xb8d04c: r0 = AllocateClosure()
    //     0xb8d04c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d050: stur            x0, [fp, #-0x10]
    // 0xb8d054: r0 = TextButton()
    //     0xb8d054: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb8d058: mov             x1, x0
    // 0xb8d05c: ldur            x0, [fp, #-0x10]
    // 0xb8d060: StoreField: r1->field_b = r0
    //     0xb8d060: stur            w0, [x1, #0xb]
    // 0xb8d064: r0 = false
    //     0xb8d064: add             x0, NULL, #0x30  ; false
    // 0xb8d068: StoreField: r1->field_27 = r0
    //     0xb8d068: stur            w0, [x1, #0x27]
    // 0xb8d06c: r2 = true
    //     0xb8d06c: add             x2, NULL, #0x20  ; true
    // 0xb8d070: StoreField: r1->field_2f = r2
    //     0xb8d070: stur            w2, [x1, #0x2f]
    // 0xb8d074: ldur            x3, [fp, #-8]
    // 0xb8d078: StoreField: r1->field_37 = r3
    //     0xb8d078: stur            w3, [x1, #0x37]
    // 0xb8d07c: mov             x4, x1
    // 0xb8d080: ldur            x3, [fp, #-0x18]
    // 0xb8d084: ldur            x1, [fp, #-0x20]
    // 0xb8d088: stur            x4, [fp, #-8]
    // 0xb8d08c: r0 = Container()
    //     0xb8d08c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8d090: stur            x0, [fp, #-0x10]
    // 0xb8d094: r16 = Instance_EdgeInsets
    //     0xb8d094: add             x16, PP, #0x35, lsl #12  ; [pp+0x35630] Obj!EdgeInsets@e13481
    //     0xb8d098: ldr             x16, [x16, #0x630]
    // 0xb8d09c: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb8d09c: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb8d0a0: ldr             lr, [lr, #0xc58]
    // 0xb8d0a4: stp             lr, x16, [SP, #8]
    // 0xb8d0a8: ldur            x16, [fp, #-8]
    // 0xb8d0ac: str             x16, [SP]
    // 0xb8d0b0: mov             x1, x0
    // 0xb8d0b4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0xb8d0b4: add             x4, PP, #0x28, lsl #12  ; [pp+0x28058] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xb8d0b8: ldr             x4, [x4, #0x58]
    // 0xb8d0bc: r0 = Container()
    //     0xb8d0bc: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8d0c0: r1 = Null
    //     0xb8d0c0: mov             x1, NULL
    // 0xb8d0c4: r2 = 2
    //     0xb8d0c4: movz            x2, #0x2
    // 0xb8d0c8: r0 = AllocateArray()
    //     0xb8d0c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8d0cc: mov             x2, x0
    // 0xb8d0d0: ldur            x0, [fp, #-0x10]
    // 0xb8d0d4: stur            x2, [fp, #-8]
    // 0xb8d0d8: StoreField: r2->field_f = r0
    //     0xb8d0d8: stur            w0, [x2, #0xf]
    // 0xb8d0dc: r1 = <Widget>
    //     0xb8d0dc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8d0e0: r0 = AllocateGrowableArray()
    //     0xb8d0e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8d0e4: mov             x1, x0
    // 0xb8d0e8: ldur            x0, [fp, #-8]
    // 0xb8d0ec: stur            x1, [fp, #-0x10]
    // 0xb8d0f0: StoreField: r1->field_f = r0
    //     0xb8d0f0: stur            w0, [x1, #0xf]
    // 0xb8d0f4: r0 = 2
    //     0xb8d0f4: movz            x0, #0x2
    // 0xb8d0f8: StoreField: r1->field_b = r0
    //     0xb8d0f8: stur            w0, [x1, #0xb]
    // 0xb8d0fc: r0 = Scaffold()
    //     0xb8d0fc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb8d100: ldur            x1, [fp, #-0x18]
    // 0xb8d104: StoreField: r0->field_13 = r1
    //     0xb8d104: stur            w1, [x0, #0x13]
    // 0xb8d108: ldur            x1, [fp, #-0x20]
    // 0xb8d10c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8d10c: stur            w1, [x0, #0x17]
    // 0xb8d110: ldur            x1, [fp, #-0x10]
    // 0xb8d114: StoreField: r0->field_27 = r1
    //     0xb8d114: stur            w1, [x0, #0x27]
    // 0xb8d118: r1 = Instance_AlignmentDirectional
    //     0xb8d118: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb8d11c: ldr             x1, [x1, #0x758]
    // 0xb8d120: StoreField: r0->field_2b = r1
    //     0xb8d120: stur            w1, [x0, #0x2b]
    // 0xb8d124: r1 = true
    //     0xb8d124: add             x1, NULL, #0x20  ; true
    // 0xb8d128: StoreField: r0->field_53 = r1
    //     0xb8d128: stur            w1, [x0, #0x53]
    // 0xb8d12c: r2 = Instance_DragStartBehavior
    //     0xb8d12c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb8d130: StoreField: r0->field_57 = r2
    //     0xb8d130: stur            w2, [x0, #0x57]
    // 0xb8d134: r2 = false
    //     0xb8d134: add             x2, NULL, #0x30  ; false
    // 0xb8d138: StoreField: r0->field_b = r2
    //     0xb8d138: stur            w2, [x0, #0xb]
    // 0xb8d13c: StoreField: r0->field_f = r2
    //     0xb8d13c: stur            w2, [x0, #0xf]
    // 0xb8d140: StoreField: r0->field_5f = r1
    //     0xb8d140: stur            w1, [x0, #0x5f]
    // 0xb8d144: StoreField: r0->field_63 = r1
    //     0xb8d144: stur            w1, [x0, #0x63]
    // 0xb8d148: LeaveFrame
    //     0xb8d148: mov             SP, fp
    //     0xb8d14c: ldp             fp, lr, [SP], #0x10
    // 0xb8d150: ret
    //     0xb8d150: ret             
    // 0xb8d154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d158: b               #0xb8c8e0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8d15c, size: 0x128
    // 0xb8d15c: EnterFrame
    //     0xb8d15c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d160: mov             fp, SP
    // 0xb8d164: AllocStack(0x30)
    //     0xb8d164: sub             SP, SP, #0x30
    // 0xb8d168: SetupParameters()
    //     0xb8d168: ldr             x0, [fp, #0x10]
    //     0xb8d16c: ldur            w2, [x0, #0x17]
    //     0xb8d170: add             x2, x2, HEAP, lsl #32
    //     0xb8d174: stur            x2, [fp, #-8]
    // 0xb8d178: CheckStackOverflow
    //     0xb8d178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d17c: cmp             SP, x16
    //     0xb8d180: b.ls            #0xb8d27c
    // 0xb8d184: LoadField: r0 = r2->field_f
    //     0xb8d184: ldur            w0, [x2, #0xf]
    // 0xb8d188: DecompressPointer r0
    //     0xb8d188: add             x0, x0, HEAP, lsl #32
    // 0xb8d18c: LoadField: r1 = r0->field_b
    //     0xb8d18c: ldur            w1, [x0, #0xb]
    // 0xb8d190: DecompressPointer r1
    //     0xb8d190: add             x1, x1, HEAP, lsl #32
    // 0xb8d194: r0 = isEnded()
    //     0xb8d194: bl              #0x91eb50  ; [package:nuonline/app/data/models/campaign.dart] Campaign::isEnded
    // 0xb8d198: tbnz            w0, #4, #0xb8d220
    // 0xb8d19c: ldur            x0, [fp, #-8]
    // 0xb8d1a0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d1a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d1a4: ldr             x0, [x0, #0x2670]
    //     0xb8d1a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8d1ac: cmp             w0, w16
    //     0xb8d1b0: b.ne            #0xb8d1bc
    //     0xb8d1b4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8d1b8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8d1bc: ldur            x0, [fp, #-8]
    // 0xb8d1c0: LoadField: r1 = r0->field_f
    //     0xb8d1c0: ldur            w1, [x0, #0xf]
    // 0xb8d1c4: DecompressPointer r1
    //     0xb8d1c4: add             x1, x1, HEAP, lsl #32
    // 0xb8d1c8: LoadField: r0 = r1->field_b
    //     0xb8d1c8: ldur            w0, [x1, #0xb]
    // 0xb8d1cc: DecompressPointer r0
    //     0xb8d1cc: add             x0, x0, HEAP, lsl #32
    // 0xb8d1d0: LoadField: r1 = r0->field_f
    //     0xb8d1d0: ldur            w1, [x0, #0xf]
    // 0xb8d1d4: DecompressPointer r1
    //     0xb8d1d4: add             x1, x1, HEAP, lsl #32
    // 0xb8d1d8: stur            x1, [fp, #-0x18]
    // 0xb8d1dc: LoadField: r2 = r0->field_1b
    //     0xb8d1dc: ldur            w2, [x0, #0x1b]
    // 0xb8d1e0: DecompressPointer r2
    //     0xb8d1e0: add             x2, x2, HEAP, lsl #32
    // 0xb8d1e4: stur            x2, [fp, #-0x10]
    // 0xb8d1e8: r0 = Merchant()
    //     0xb8d1e8: bl              #0xadf360  ; AllocateMerchantStub -> Merchant (size=0x18)
    // 0xb8d1ec: mov             x1, x0
    // 0xb8d1f0: ldur            x0, [fp, #-0x18]
    // 0xb8d1f4: StoreField: r1->field_b = r0
    //     0xb8d1f4: stur            w0, [x1, #0xb]
    // 0xb8d1f8: ldur            x0, [fp, #-0x10]
    // 0xb8d1fc: StoreField: r1->field_f = r0
    //     0xb8d1fc: stur            w0, [x1, #0xf]
    // 0xb8d200: r16 = "/donation/campaign-news"
    //     0xb8d200: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f6f0] "/donation/campaign-news"
    //     0xb8d204: ldr             x16, [x16, #0x6f0]
    // 0xb8d208: stp             x16, NULL, [SP, #8]
    // 0xb8d20c: str             x1, [SP]
    // 0xb8d210: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8d210: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8d214: ldr             x4, [x4, #0x478]
    // 0xb8d218: r0 = GetNavigation.toNamed()
    //     0xb8d218: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8d21c: b               #0xb8d270
    // 0xb8d220: ldur            x0, [fp, #-8]
    // 0xb8d224: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d224: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d228: ldr             x0, [x0, #0x2670]
    //     0xb8d22c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8d230: cmp             w0, w16
    //     0xb8d234: b.ne            #0xb8d240
    //     0xb8d238: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8d23c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8d240: ldur            x0, [fp, #-8]
    // 0xb8d244: LoadField: r1 = r0->field_f
    //     0xb8d244: ldur            w1, [x0, #0xf]
    // 0xb8d248: DecompressPointer r1
    //     0xb8d248: add             x1, x1, HEAP, lsl #32
    // 0xb8d24c: LoadField: r0 = r1->field_b
    //     0xb8d24c: ldur            w0, [x1, #0xb]
    // 0xb8d250: DecompressPointer r0
    //     0xb8d250: add             x0, x0, HEAP, lsl #32
    // 0xb8d254: r16 = "/donation/pay-campaign"
    //     0xb8d254: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f5e0] "/donation/pay-campaign"
    //     0xb8d258: ldr             x16, [x16, #0x5e0]
    // 0xb8d25c: stp             x16, NULL, [SP, #8]
    // 0xb8d260: str             x0, [SP]
    // 0xb8d264: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8d264: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8d268: ldr             x4, [x4, #0x478]
    // 0xb8d26c: r0 = GetNavigation.toNamed()
    //     0xb8d26c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8d270: LeaveFrame
    //     0xb8d270: mov             SP, fp
    //     0xb8d274: ldp             fp, lr, [SP], #0x10
    // 0xb8d278: ret
    //     0xb8d278: ret             
    // 0xb8d27c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d27c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d280: b               #0xb8d184
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8d284, size: 0x88
    // 0xb8d284: EnterFrame
    //     0xb8d284: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d288: mov             fp, SP
    // 0xb8d28c: AllocStack(0x20)
    //     0xb8d28c: sub             SP, SP, #0x20
    // 0xb8d290: SetupParameters()
    //     0xb8d290: ldr             x0, [fp, #0x10]
    //     0xb8d294: ldur            w1, [x0, #0x17]
    //     0xb8d298: add             x1, x1, HEAP, lsl #32
    //     0xb8d29c: stur            x1, [fp, #-8]
    // 0xb8d2a0: CheckStackOverflow
    //     0xb8d2a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d2a4: cmp             SP, x16
    //     0xb8d2a8: b.ls            #0xb8d304
    // 0xb8d2ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d2ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d2b0: ldr             x0, [x0, #0x2670]
    //     0xb8d2b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8d2b8: cmp             w0, w16
    //     0xb8d2bc: b.ne            #0xb8d2c8
    //     0xb8d2c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8d2c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8d2c8: ldur            x0, [fp, #-8]
    // 0xb8d2cc: LoadField: r1 = r0->field_f
    //     0xb8d2cc: ldur            w1, [x0, #0xf]
    // 0xb8d2d0: DecompressPointer r1
    //     0xb8d2d0: add             x1, x1, HEAP, lsl #32
    // 0xb8d2d4: LoadField: r0 = r1->field_b
    //     0xb8d2d4: ldur            w0, [x1, #0xb]
    // 0xb8d2d8: DecompressPointer r0
    //     0xb8d2d8: add             x0, x0, HEAP, lsl #32
    // 0xb8d2dc: r16 = "/donation/pay-campaign"
    //     0xb8d2dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f5e0] "/donation/pay-campaign"
    //     0xb8d2e0: ldr             x16, [x16, #0x5e0]
    // 0xb8d2e4: stp             x16, NULL, [SP, #8]
    // 0xb8d2e8: str             x0, [SP]
    // 0xb8d2ec: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8d2ec: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8d2f0: ldr             x4, [x4, #0x478]
    // 0xb8d2f4: r0 = GetNavigation.toNamed()
    //     0xb8d2f4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8d2f8: LeaveFrame
    //     0xb8d2f8: mov             SP, fp
    //     0xb8d2fc: ldp             fp, lr, [SP], #0x10
    // 0xb8d300: ret
    //     0xb8d300: ret             
    // 0xb8d304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d308: b               #0xb8d2ac
  }
}
