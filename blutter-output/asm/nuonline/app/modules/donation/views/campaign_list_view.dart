// lib: , url: package:nuonline/app/modules/donation/views/campaign_list_view.dart

// class id: 1050219, size: 0x8
class :: {
}

// class id: 5296, size: 0x14, field offset: 0x14
//   const constructor, 
class CampaignListView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xadf8fc, size: 0x174
    // 0xadf8fc: EnterFrame
    //     0xadf8fc: stp             fp, lr, [SP, #-0x10]!
    //     0xadf900: mov             fp, SP
    // 0xadf904: AllocStack(0x40)
    //     0xadf904: sub             SP, SP, #0x40
    // 0xadf908: SetupParameters(CampaignListView this /* r1 => r1, fp-0x8 */)
    //     0xadf908: stur            x1, [fp, #-8]
    // 0xadf90c: CheckStackOverflow
    //     0xadf90c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf910: cmp             SP, x16
    //     0xadf914: b.ls            #0xadfa68
    // 0xadf918: r1 = 1
    //     0xadf918: movz            x1, #0x1
    // 0xadf91c: r0 = AllocateContext()
    //     0xadf91c: bl              #0xec126c  ; AllocateContextStub
    // 0xadf920: mov             x2, x0
    // 0xadf924: ldur            x0, [fp, #-8]
    // 0xadf928: stur            x2, [fp, #-0x10]
    // 0xadf92c: StoreField: r2->field_f = r0
    //     0xadf92c: stur            w0, [x2, #0xf]
    // 0xadf930: mov             x1, x0
    // 0xadf934: r0 = controller()
    //     0xadf934: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadf938: LoadField: r1 = r0->field_33
    //     0xadf938: ldur            w1, [x0, #0x33]
    // 0xadf93c: DecompressPointer r1
    //     0xadf93c: add             x1, x1, HEAP, lsl #32
    // 0xadf940: r16 = Instance_PaymentType
    //     0xadf940: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xadf944: ldr             x16, [x16, #0x608]
    // 0xadf948: cmp             w1, w16
    // 0xadf94c: b.ne            #0xadf95c
    // 0xadf950: r0 = "Koin NU"
    //     0xadf950: add             x0, PP, #0x30, lsl #12  ; [pp+0x30330] "Koin NU"
    //     0xadf954: ldr             x0, [x0, #0x330]
    // 0xadf958: b               #0xadf964
    // 0xadf95c: r0 = "Galang Dana"
    //     0xadf95c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xadf960: ldr             x0, [x0, #0x348]
    // 0xadf964: stur            x0, [fp, #-0x18]
    // 0xadf968: r0 = Text()
    //     0xadf968: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadf96c: mov             x1, x0
    // 0xadf970: ldur            x0, [fp, #-0x18]
    // 0xadf974: stur            x1, [fp, #-0x20]
    // 0xadf978: StoreField: r1->field_b = r0
    //     0xadf978: stur            w0, [x1, #0xb]
    // 0xadf97c: r0 = AppBar()
    //     0xadf97c: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xadf980: stur            x0, [fp, #-0x18]
    // 0xadf984: ldur            x16, [fp, #-0x20]
    // 0xadf988: str             x16, [SP]
    // 0xadf98c: mov             x1, x0
    // 0xadf990: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xadf990: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xadf994: ldr             x4, [x4, #0x6e8]
    // 0xadf998: r0 = AppBar()
    //     0xadf998: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xadf99c: ldur            x1, [fp, #-8]
    // 0xadf9a0: r0 = controller()
    //     0xadf9a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadf9a4: ldur            x2, [fp, #-0x10]
    // 0xadf9a8: r1 = Function '<anonymous closure>':.
    //     0xadf9a8: add             x1, PP, #0x30, lsl #12  ; [pp+0x308c8] AnonymousClosure: (0xae05b4), in [package:nuonline/app/modules/donation/views/campaign_list_view.dart] CampaignListView::build (0xadf8fc)
    //     0xadf9ac: ldr             x1, [x1, #0x8c8]
    // 0xadf9b0: stur            x0, [fp, #-8]
    // 0xadf9b4: r0 = AllocateClosure()
    //     0xadf9b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xadf9b8: r1 = Function '<anonymous closure>':.
    //     0xadf9b8: add             x1, PP, #0x30, lsl #12  ; [pp+0x308d0] AnonymousClosure: (0xae01d4), in [package:nuonline/app/modules/donation/views/campaign_list_view.dart] CampaignListView::build (0xadf8fc)
    //     0xadf9bc: ldr             x1, [x1, #0x8d0]
    // 0xadf9c0: r2 = Null
    //     0xadf9c0: mov             x2, NULL
    // 0xadf9c4: stur            x0, [fp, #-0x20]
    // 0xadf9c8: r0 = AllocateClosure()
    //     0xadf9c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xadf9cc: ldur            x2, [fp, #-0x10]
    // 0xadf9d0: r1 = Function '<anonymous closure>':.
    //     0xadf9d0: add             x1, PP, #0x30, lsl #12  ; [pp+0x308d8] AnonymousClosure: (0xadff28), in [package:nuonline/app/modules/donation/views/campaign_list_view.dart] CampaignListView::build (0xadf8fc)
    //     0xadf9d4: ldr             x1, [x1, #0x8d8]
    // 0xadf9d8: stur            x0, [fp, #-0x10]
    // 0xadf9dc: r0 = AllocateClosure()
    //     0xadf9dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xadf9e0: r16 = true
    //     0xadf9e0: add             x16, NULL, #0x20  ; true
    // 0xadf9e4: r30 = Instance_EdgeInsets
    //     0xadf9e4: add             lr, PP, #0x29, lsl #12  ; [pp+0x296f0] Obj!EdgeInsets@e12791
    //     0xadf9e8: ldr             lr, [lr, #0x6f0]
    // 0xadf9ec: stp             lr, x16, [SP, #0x10]
    // 0xadf9f0: ldur            x16, [fp, #-0x10]
    // 0xadf9f4: stp             x0, x16, [SP]
    // 0xadf9f8: ldur            x1, [fp, #-8]
    // 0xadf9fc: ldur            x2, [fp, #-0x20]
    // 0xadfa00: r3 = Instance_Divider
    //     0xadfa00: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xadfa04: ldr             x3, [x3, #0xc28]
    // 0xadfa08: r4 = const [0, 0x7, 0x4, 0x3, empty, 0x6, firstPageLoading, 0x5, padding, 0x4, shrinkWrap, 0x3, null]
    //     0xadfa08: add             x4, PP, #0x30, lsl #12  ; [pp+0x308e0] List(13) [0, 0x7, 0x4, 0x3, "empty", 0x6, "firstPageLoading", 0x5, "padding", 0x4, "shrinkWrap", 0x3, Null]
    //     0xadfa0c: ldr             x4, [x4, #0x8e0]
    // 0xadfa10: r0 = paginate()
    //     0xadfa10: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xadfa14: stur            x0, [fp, #-8]
    // 0xadfa18: r0 = Scaffold()
    //     0xadfa18: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xadfa1c: ldur            x1, [fp, #-0x18]
    // 0xadfa20: StoreField: r0->field_13 = r1
    //     0xadfa20: stur            w1, [x0, #0x13]
    // 0xadfa24: ldur            x1, [fp, #-8]
    // 0xadfa28: ArrayStore: r0[0] = r1  ; List_4
    //     0xadfa28: stur            w1, [x0, #0x17]
    // 0xadfa2c: r1 = Instance_AlignmentDirectional
    //     0xadfa2c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xadfa30: ldr             x1, [x1, #0x758]
    // 0xadfa34: StoreField: r0->field_2b = r1
    //     0xadfa34: stur            w1, [x0, #0x2b]
    // 0xadfa38: r1 = true
    //     0xadfa38: add             x1, NULL, #0x20  ; true
    // 0xadfa3c: StoreField: r0->field_53 = r1
    //     0xadfa3c: stur            w1, [x0, #0x53]
    // 0xadfa40: r2 = Instance_DragStartBehavior
    //     0xadfa40: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadfa44: StoreField: r0->field_57 = r2
    //     0xadfa44: stur            w2, [x0, #0x57]
    // 0xadfa48: r2 = false
    //     0xadfa48: add             x2, NULL, #0x30  ; false
    // 0xadfa4c: StoreField: r0->field_b = r2
    //     0xadfa4c: stur            w2, [x0, #0xb]
    // 0xadfa50: StoreField: r0->field_f = r2
    //     0xadfa50: stur            w2, [x0, #0xf]
    // 0xadfa54: StoreField: r0->field_5f = r1
    //     0xadfa54: stur            w1, [x0, #0x5f]
    // 0xadfa58: StoreField: r0->field_63 = r1
    //     0xadfa58: stur            w1, [x0, #0x63]
    // 0xadfa5c: LeaveFrame
    //     0xadfa5c: mov             SP, fp
    //     0xadfa60: ldp             fp, lr, [SP], #0x10
    // 0xadfa64: ret
    //     0xadfa64: ret             
    // 0xadfa68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfa68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfa6c: b               #0xadf918
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xadff28, size: 0x2ac
    // 0xadff28: EnterFrame
    //     0xadff28: stp             fp, lr, [SP, #-0x10]!
    //     0xadff2c: mov             fp, SP
    // 0xadff30: AllocStack(0x28)
    //     0xadff30: sub             SP, SP, #0x28
    // 0xadff34: SetupParameters()
    //     0xadff34: ldr             x0, [fp, #0x18]
    //     0xadff38: ldur            w1, [x0, #0x17]
    //     0xadff3c: add             x1, x1, HEAP, lsl #32
    //     0xadff40: stur            x1, [fp, #-8]
    // 0xadff44: CheckStackOverflow
    //     0xadff44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadff48: cmp             SP, x16
    //     0xadff4c: b.ls            #0xae01bc
    // 0xadff50: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadff50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadff54: ldr             x0, [x0, #0x2670]
    //     0xadff58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadff5c: cmp             w0, w16
    //     0xadff60: b.ne            #0xadff6c
    //     0xadff64: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xadff68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xadff6c: r0 = GetNavigation.width()
    //     0xadff6c: bl              #0x7daa60  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xadff70: mov             v1.16b, v0.16b
    // 0xadff74: d0 = 2.000000
    //     0xadff74: fmov            d0, #2.00000000
    // 0xadff78: fdiv            d2, d1, d0
    // 0xadff7c: r0 = inline_Allocate_Double()
    //     0xadff7c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xadff80: add             x0, x0, #0x10
    //     0xadff84: cmp             x1, x0
    //     0xadff88: b.ls            #0xae01c4
    //     0xadff8c: str             x0, [THR, #0x50]  ; THR::top
    //     0xadff90: sub             x0, x0, #0xf
    //     0xadff94: movz            x1, #0xe15c
    //     0xadff98: movk            x1, #0x3, lsl #16
    //     0xadff9c: stur            x1, [x0, #-1]
    // 0xadffa0: StoreField: r0->field_7 = d2
    //     0xadffa0: stur            d2, [x0, #7]
    // 0xadffa4: stur            x0, [fp, #-0x10]
    // 0xadffa8: r0 = SizedBox()
    //     0xadffa8: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadffac: mov             x3, x0
    // 0xadffb0: ldur            x0, [fp, #-0x10]
    // 0xadffb4: stur            x3, [fp, #-0x18]
    // 0xadffb8: StoreField: r3->field_13 = r0
    //     0xadffb8: stur            w0, [x3, #0x13]
    // 0xadffbc: r1 = Null
    //     0xadffbc: mov             x1, NULL
    // 0xadffc0: r2 = 4
    //     0xadffc0: movz            x2, #0x4
    // 0xadffc4: r0 = AllocateArray()
    //     0xadffc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadffc8: stur            x0, [fp, #-0x10]
    // 0xadffcc: r16 = "Belum Ada "
    //     0xadffcc: add             x16, PP, #0x30, lsl #12  ; [pp+0x308e8] "Belum Ada "
    //     0xadffd0: ldr             x16, [x16, #0x8e8]
    // 0xadffd4: StoreField: r0->field_f = r16
    //     0xadffd4: stur            w16, [x0, #0xf]
    // 0xadffd8: ldur            x2, [fp, #-8]
    // 0xadffdc: LoadField: r1 = r2->field_f
    //     0xadffdc: ldur            w1, [x2, #0xf]
    // 0xadffe0: DecompressPointer r1
    //     0xadffe0: add             x1, x1, HEAP, lsl #32
    // 0xadffe4: r0 = controller()
    //     0xadffe4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xadffe8: LoadField: r1 = r0->field_33
    //     0xadffe8: ldur            w1, [x0, #0x33]
    // 0xadffec: DecompressPointer r1
    //     0xadffec: add             x1, x1, HEAP, lsl #32
    // 0xadfff0: r16 = Instance_PaymentType
    //     0xadfff0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xadfff4: ldr             x16, [x16, #0x608]
    // 0xadfff8: cmp             w1, w16
    // 0xadfffc: b.ne            #0xae000c
    // 0xae0000: r0 = "Koin NU"
    //     0xae0000: add             x0, PP, #0x30, lsl #12  ; [pp+0x30330] "Koin NU"
    //     0xae0004: ldr             x0, [x0, #0x330]
    // 0xae0008: b               #0xae0014
    // 0xae000c: r0 = "Galang Dana"
    //     0xae000c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xae0010: ldr             x0, [x0, #0x348]
    // 0xae0014: ldur            x2, [fp, #-8]
    // 0xae0018: ldur            x1, [fp, #-0x10]
    // 0xae001c: ArrayStore: r1[1] = r0  ; List_4
    //     0xae001c: add             x25, x1, #0x13
    //     0xae0020: str             w0, [x25]
    //     0xae0024: tbz             w0, #0, #0xae0040
    //     0xae0028: ldurb           w16, [x1, #-1]
    //     0xae002c: ldurb           w17, [x0, #-1]
    //     0xae0030: and             x16, x17, x16, lsr #2
    //     0xae0034: tst             x16, HEAP, lsr #32
    //     0xae0038: b.eq            #0xae0040
    //     0xae003c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae0040: ldur            x16, [fp, #-0x10]
    // 0xae0044: str             x16, [SP]
    // 0xae0048: r0 = _interpolate()
    //     0xae0048: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae004c: r1 = Null
    //     0xae004c: mov             x1, NULL
    // 0xae0050: r2 = 6
    //     0xae0050: movz            x2, #0x6
    // 0xae0054: stur            x0, [fp, #-0x10]
    // 0xae0058: r0 = AllocateArray()
    //     0xae0058: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae005c: stur            x0, [fp, #-0x20]
    // 0xae0060: r16 = "Jika tersedia, "
    //     0xae0060: add             x16, PP, #0x30, lsl #12  ; [pp+0x308f0] "Jika tersedia, "
    //     0xae0064: ldr             x16, [x16, #0x8f0]
    // 0xae0068: StoreField: r0->field_f = r16
    //     0xae0068: stur            w16, [x0, #0xf]
    // 0xae006c: ldur            x1, [fp, #-8]
    // 0xae0070: LoadField: r2 = r1->field_f
    //     0xae0070: ldur            w2, [x1, #0xf]
    // 0xae0074: DecompressPointer r2
    //     0xae0074: add             x2, x2, HEAP, lsl #32
    // 0xae0078: mov             x1, x2
    // 0xae007c: r0 = controller()
    //     0xae007c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0080: LoadField: r1 = r0->field_33
    //     0xae0080: ldur            w1, [x0, #0x33]
    // 0xae0084: DecompressPointer r1
    //     0xae0084: add             x1, x1, HEAP, lsl #32
    // 0xae0088: r16 = Instance_PaymentType
    //     0xae0088: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae008c: ldr             x16, [x16, #0x608]
    // 0xae0090: cmp             w1, w16
    // 0xae0094: b.ne            #0xae00a4
    // 0xae0098: r0 = "Koin NU"
    //     0xae0098: add             x0, PP, #0x30, lsl #12  ; [pp+0x30330] "Koin NU"
    //     0xae009c: ldr             x0, [x0, #0x330]
    // 0xae00a0: b               #0xae00ac
    // 0xae00a4: r0 = "Galang Dana"
    //     0xae00a4: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xae00a8: ldr             x0, [x0, #0x348]
    // 0xae00ac: ldur            x3, [fp, #-0x18]
    // 0xae00b0: ldur            x2, [fp, #-0x20]
    // 0xae00b4: mov             x1, x2
    // 0xae00b8: ArrayStore: r1[1] = r0  ; List_4
    //     0xae00b8: add             x25, x1, #0x13
    //     0xae00bc: str             w0, [x25]
    //     0xae00c0: tbz             w0, #0, #0xae00dc
    //     0xae00c4: ldurb           w16, [x1, #-1]
    //     0xae00c8: ldurb           w17, [x0, #-1]
    //     0xae00cc: and             x16, x17, x16, lsr #2
    //     0xae00d0: tst             x16, HEAP, lsr #32
    //     0xae00d4: b.eq            #0xae00dc
    //     0xae00d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae00dc: r16 = " akan ditampilkan pada halaman ini."
    //     0xae00dc: add             x16, PP, #0x30, lsl #12  ; [pp+0x308f8] " akan ditampilkan pada halaman ini."
    //     0xae00e0: ldr             x16, [x16, #0x8f8]
    // 0xae00e4: ArrayStore: r2[0] = r16  ; List_4
    //     0xae00e4: stur            w16, [x2, #0x17]
    // 0xae00e8: str             x2, [SP]
    // 0xae00ec: r0 = _interpolate()
    //     0xae00ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae00f0: stur            x0, [fp, #-8]
    // 0xae00f4: r0 = NEmptyState()
    //     0xae00f4: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xae00f8: mov             x1, x0
    // 0xae00fc: ldur            x2, [fp, #-8]
    // 0xae0100: ldur            x5, [fp, #-0x10]
    // 0xae0104: r3 = "assets/images/illustration/no_search.svg"
    //     0xae0104: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xae0108: ldr             x3, [x3, #0x138]
    // 0xae010c: stur            x0, [fp, #-8]
    // 0xae0110: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xae0110: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xae0114: r0 = NEmptyState.svg()
    //     0xae0114: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xae0118: r1 = Null
    //     0xae0118: mov             x1, NULL
    // 0xae011c: r2 = 4
    //     0xae011c: movz            x2, #0x4
    // 0xae0120: r0 = AllocateArray()
    //     0xae0120: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae0124: mov             x2, x0
    // 0xae0128: ldur            x0, [fp, #-0x18]
    // 0xae012c: stur            x2, [fp, #-0x10]
    // 0xae0130: StoreField: r2->field_f = r0
    //     0xae0130: stur            w0, [x2, #0xf]
    // 0xae0134: ldur            x0, [fp, #-8]
    // 0xae0138: StoreField: r2->field_13 = r0
    //     0xae0138: stur            w0, [x2, #0x13]
    // 0xae013c: r1 = <Widget>
    //     0xae013c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae0140: r0 = AllocateGrowableArray()
    //     0xae0140: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae0144: mov             x1, x0
    // 0xae0148: ldur            x0, [fp, #-0x10]
    // 0xae014c: stur            x1, [fp, #-8]
    // 0xae0150: StoreField: r1->field_f = r0
    //     0xae0150: stur            w0, [x1, #0xf]
    // 0xae0154: r0 = 4
    //     0xae0154: movz            x0, #0x4
    // 0xae0158: StoreField: r1->field_b = r0
    //     0xae0158: stur            w0, [x1, #0xb]
    // 0xae015c: r0 = Column()
    //     0xae015c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae0160: r1 = Instance_Axis
    //     0xae0160: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae0164: StoreField: r0->field_f = r1
    //     0xae0164: stur            w1, [x0, #0xf]
    // 0xae0168: r1 = Instance_MainAxisAlignment
    //     0xae0168: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae016c: ldr             x1, [x1, #0x730]
    // 0xae0170: StoreField: r0->field_13 = r1
    //     0xae0170: stur            w1, [x0, #0x13]
    // 0xae0174: r1 = Instance_MainAxisSize
    //     0xae0174: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae0178: ldr             x1, [x1, #0x738]
    // 0xae017c: ArrayStore: r0[0] = r1  ; List_4
    //     0xae017c: stur            w1, [x0, #0x17]
    // 0xae0180: r1 = Instance_CrossAxisAlignment
    //     0xae0180: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae0184: ldr             x1, [x1, #0x740]
    // 0xae0188: StoreField: r0->field_1b = r1
    //     0xae0188: stur            w1, [x0, #0x1b]
    // 0xae018c: r1 = Instance_VerticalDirection
    //     0xae018c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae0190: ldr             x1, [x1, #0x748]
    // 0xae0194: StoreField: r0->field_23 = r1
    //     0xae0194: stur            w1, [x0, #0x23]
    // 0xae0198: r1 = Instance_Clip
    //     0xae0198: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae019c: ldr             x1, [x1, #0x750]
    // 0xae01a0: StoreField: r0->field_2b = r1
    //     0xae01a0: stur            w1, [x0, #0x2b]
    // 0xae01a4: StoreField: r0->field_2f = rZR
    //     0xae01a4: stur            xzr, [x0, #0x2f]
    // 0xae01a8: ldur            x1, [fp, #-8]
    // 0xae01ac: StoreField: r0->field_b = r1
    //     0xae01ac: stur            w1, [x0, #0xb]
    // 0xae01b0: LeaveFrame
    //     0xae01b0: mov             SP, fp
    //     0xae01b4: ldp             fp, lr, [SP], #0x10
    // 0xae01b8: ret
    //     0xae01b8: ret             
    // 0xae01bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae01bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae01c0: b               #0xadff50
    // 0xae01c4: SaveReg d2
    //     0xae01c4: str             q2, [SP, #-0x10]!
    // 0xae01c8: r0 = AllocateDouble()
    //     0xae01c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xae01cc: RestoreReg d2
    //     0xae01cc: ldr             q2, [SP], #0x10
    // 0xae01d0: b               #0xadffa0
  }
  [closure] ListView <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xae01d4, size: 0x8c
    // 0xae01d4: EnterFrame
    //     0xae01d4: stp             fp, lr, [SP, #-0x10]!
    //     0xae01d8: mov             fp, SP
    // 0xae01dc: AllocStack(0x28)
    //     0xae01dc: sub             SP, SP, #0x28
    // 0xae01e0: CheckStackOverflow
    //     0xae01e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae01e4: cmp             SP, x16
    //     0xae01e8: b.ls            #0xae0258
    // 0xae01ec: r1 = Function '<anonymous closure>':.
    //     0xae01ec: add             x1, PP, #0x30, lsl #12  ; [pp+0x30900] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xae01f0: ldr             x1, [x1, #0x900]
    // 0xae01f4: r2 = Null
    //     0xae01f4: mov             x2, NULL
    // 0xae01f8: r0 = AllocateClosure()
    //     0xae01f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae01fc: r1 = Function '<anonymous closure>':.
    //     0xae01fc: add             x1, PP, #0x30, lsl #12  ; [pp+0x30908] AnonymousClosure: (0xae0260), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae0200: ldr             x1, [x1, #0x908]
    // 0xae0204: r2 = Null
    //     0xae0204: mov             x2, NULL
    // 0xae0208: stur            x0, [fp, #-8]
    // 0xae020c: r0 = AllocateClosure()
    //     0xae020c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae0210: stur            x0, [fp, #-0x10]
    // 0xae0214: r0 = ListView()
    //     0xae0214: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae0218: stur            x0, [fp, #-0x18]
    // 0xae021c: r16 = true
    //     0xae021c: add             x16, NULL, #0x20  ; true
    // 0xae0220: r30 = Instance_NeverScrollableScrollPhysics
    //     0xae0220: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xae0224: ldr             lr, [lr, #0x290]
    // 0xae0228: stp             lr, x16, [SP]
    // 0xae022c: mov             x1, x0
    // 0xae0230: ldur            x2, [fp, #-0x10]
    // 0xae0234: ldur            x5, [fp, #-8]
    // 0xae0238: r3 = 5
    //     0xae0238: movz            x3, #0x5
    // 0xae023c: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xae023c: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xae0240: ldr             x4, [x4, #0x298]
    // 0xae0244: r0 = ListView.separated()
    //     0xae0244: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xae0248: ldur            x0, [fp, #-0x18]
    // 0xae024c: LeaveFrame
    //     0xae024c: mov             SP, fp
    //     0xae0250: ldp             fp, lr, [SP], #0x10
    // 0xae0254: ret
    //     0xae0254: ret             
    // 0xae0258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae025c: b               #0xae01ec
  }
  [closure] NCampaignCard <anonymous closure>(dynamic, BuildContext, Campaign, int) {
    // ** addr: 0xae05b4, size: 0x2dc
    // 0xae05b4: EnterFrame
    //     0xae05b4: stp             fp, lr, [SP, #-0x10]!
    //     0xae05b8: mov             fp, SP
    // 0xae05bc: AllocStack(0x70)
    //     0xae05bc: sub             SP, SP, #0x70
    // 0xae05c0: SetupParameters()
    //     0xae05c0: ldr             x0, [fp, #0x28]
    //     0xae05c4: ldur            w1, [x0, #0x17]
    //     0xae05c8: add             x1, x1, HEAP, lsl #32
    //     0xae05cc: stur            x1, [fp, #-8]
    // 0xae05d0: CheckStackOverflow
    //     0xae05d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae05d4: cmp             SP, x16
    //     0xae05d8: b.ls            #0xae0888
    // 0xae05dc: r1 = 1
    //     0xae05dc: movz            x1, #0x1
    // 0xae05e0: r0 = AllocateContext()
    //     0xae05e0: bl              #0xec126c  ; AllocateContextStub
    // 0xae05e4: mov             x2, x0
    // 0xae05e8: ldur            x0, [fp, #-8]
    // 0xae05ec: stur            x2, [fp, #-0x28]
    // 0xae05f0: StoreField: r2->field_b = r0
    //     0xae05f0: stur            w0, [x2, #0xb]
    // 0xae05f4: ldr             x3, [fp, #0x18]
    // 0xae05f8: StoreField: r2->field_f = r3
    //     0xae05f8: stur            w3, [x2, #0xf]
    // 0xae05fc: LoadField: r4 = r3->field_1b
    //     0xae05fc: ldur            w4, [x3, #0x1b]
    // 0xae0600: DecompressPointer r4
    //     0xae0600: add             x4, x4, HEAP, lsl #32
    // 0xae0604: stur            x4, [fp, #-0x20]
    // 0xae0608: LoadField: r1 = r3->field_37
    //     0xae0608: ldur            w1, [x3, #0x37]
    // 0xae060c: DecompressPointer r1
    //     0xae060c: add             x1, x1, HEAP, lsl #32
    // 0xae0610: cmp             w1, NULL
    // 0xae0614: b.ne            #0xae0620
    // 0xae0618: r1 = Null
    //     0xae0618: mov             x1, NULL
    // 0xae061c: b               #0xae062c
    // 0xae0620: LoadField: r5 = r1->field_f
    //     0xae0620: ldur            w5, [x1, #0xf]
    // 0xae0624: DecompressPointer r5
    //     0xae0624: add             x5, x5, HEAP, lsl #32
    // 0xae0628: mov             x1, x5
    // 0xae062c: cmp             w1, NULL
    // 0xae0630: b.ne            #0xae063c
    // 0xae0634: r5 = ""
    //     0xae0634: ldr             x5, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae0638: b               #0xae0640
    // 0xae063c: mov             x5, x1
    // 0xae0640: stur            x5, [fp, #-0x18]
    // 0xae0644: LoadField: r6 = r3->field_f
    //     0xae0644: ldur            w6, [x3, #0xf]
    // 0xae0648: DecompressPointer r6
    //     0xae0648: add             x6, x6, HEAP, lsl #32
    // 0xae064c: mov             x1, x3
    // 0xae0650: stur            x6, [fp, #-0x10]
    // 0xae0654: r0 = amountRaised()
    //     0xae0654: bl              #0xadf16c  ; [package:nuonline/app/data/models/campaign.dart] Campaign::amountRaised
    // 0xae0658: ldr             x1, [fp, #0x18]
    // 0xae065c: stur            x0, [fp, #-0x30]
    // 0xae0660: r0 = remainingDay()
    //     0xae0660: bl              #0xadeef0  ; [package:nuonline/app/data/models/campaign.dart] Campaign::remainingDay
    // 0xae0664: mov             x2, x0
    // 0xae0668: ldr             x1, [fp, #0x18]
    // 0xae066c: stur            x2, [fp, #-0x38]
    // 0xae0670: LoadField: r0 = r1->field_27
    //     0xae0670: ldur            w0, [x1, #0x27]
    // 0xae0674: DecompressPointer r0
    //     0xae0674: add             x0, x0, HEAP, lsl #32
    // 0xae0678: LoadField: r3 = r1->field_23
    //     0xae0678: ldur            w3, [x1, #0x23]
    // 0xae067c: DecompressPointer r3
    //     0xae067c: add             x3, x3, HEAP, lsl #32
    // 0xae0680: r4 = 60
    //     0xae0680: movz            x4, #0x3c
    // 0xae0684: branchIfSmi(r0, 0xae0690)
    //     0xae0684: tbz             w0, #0, #0xae0690
    // 0xae0688: r4 = LoadClassIdInstr(r0)
    //     0xae0688: ldur            x4, [x0, #-1]
    //     0xae068c: ubfx            x4, x4, #0xc, #0x14
    // 0xae0690: stp             x3, x0, [SP]
    // 0xae0694: mov             x0, x4
    // 0xae0698: r0 = GDT[cid_x0 + -0xff7]()
    //     0xae0698: sub             lr, x0, #0xff7
    //     0xae069c: ldr             lr, [x21, lr, lsl #3]
    //     0xae06a0: blr             lr
    // 0xae06a4: mov             x2, x0
    // 0xae06a8: ldur            x0, [fp, #-8]
    // 0xae06ac: stur            x2, [fp, #-0x40]
    // 0xae06b0: LoadField: r1 = r0->field_f
    //     0xae06b0: ldur            w1, [x0, #0xf]
    // 0xae06b4: DecompressPointer r1
    //     0xae06b4: add             x1, x1, HEAP, lsl #32
    // 0xae06b8: r0 = controller()
    //     0xae06b8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae06bc: LoadField: r1 = r0->field_33
    //     0xae06bc: ldur            w1, [x0, #0x33]
    // 0xae06c0: DecompressPointer r1
    //     0xae06c0: add             x1, x1, HEAP, lsl #32
    // 0xae06c4: r16 = Instance_PaymentType
    //     0xae06c4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae06c8: ldr             x16, [x16, #0x608]
    // 0xae06cc: cmp             w1, w16
    // 0xae06d0: r16 = true
    //     0xae06d0: add             x16, NULL, #0x20  ; true
    // 0xae06d4: r17 = false
    //     0xae06d4: add             x17, NULL, #0x30  ; false
    // 0xae06d8: csel            x0, x16, x17, ne
    // 0xae06dc: ldur            x2, [fp, #-8]
    // 0xae06e0: stur            x0, [fp, #-0x48]
    // 0xae06e4: LoadField: r1 = r2->field_f
    //     0xae06e4: ldur            w1, [x2, #0xf]
    // 0xae06e8: DecompressPointer r1
    //     0xae06e8: add             x1, x1, HEAP, lsl #32
    // 0xae06ec: r0 = controller()
    //     0xae06ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae06f0: LoadField: r1 = r0->field_33
    //     0xae06f0: ldur            w1, [x0, #0x33]
    // 0xae06f4: DecompressPointer r1
    //     0xae06f4: add             x1, x1, HEAP, lsl #32
    // 0xae06f8: r16 = Instance_PaymentType
    //     0xae06f8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae06fc: ldr             x16, [x16, #0x608]
    // 0xae0700: cmp             w1, w16
    // 0xae0704: b.eq            #0xae0714
    // 0xae0708: ldr             x0, [fp, #0x18]
    // 0xae070c: r3 = Null
    //     0xae070c: mov             x3, NULL
    // 0xae0710: b               #0xae072c
    // 0xae0714: ldr             x0, [fp, #0x18]
    // 0xae0718: LoadField: r1 = r0->field_3b
    //     0xae0718: ldur            w1, [x0, #0x3b]
    // 0xae071c: DecompressPointer r1
    //     0xae071c: add             x1, x1, HEAP, lsl #32
    // 0xae0720: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae0720: ldur            w2, [x1, #0x17]
    // 0xae0724: DecompressPointer r2
    //     0xae0724: add             x2, x2, HEAP, lsl #32
    // 0xae0728: mov             x3, x2
    // 0xae072c: ldur            x2, [fp, #-8]
    // 0xae0730: stur            x3, [fp, #-0x50]
    // 0xae0734: LoadField: r1 = r2->field_f
    //     0xae0734: ldur            w1, [x2, #0xf]
    // 0xae0738: DecompressPointer r1
    //     0xae0738: add             x1, x1, HEAP, lsl #32
    // 0xae073c: r0 = controller()
    //     0xae073c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0740: LoadField: r1 = r0->field_33
    //     0xae0740: ldur            w1, [x0, #0x33]
    // 0xae0744: DecompressPointer r1
    //     0xae0744: add             x1, x1, HEAP, lsl #32
    // 0xae0748: r16 = Instance_PaymentType
    //     0xae0748: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae074c: ldr             x16, [x16, #0x608]
    // 0xae0750: cmp             w1, w16
    // 0xae0754: r16 = true
    //     0xae0754: add             x16, NULL, #0x20  ; true
    // 0xae0758: r17 = false
    //     0xae0758: add             x17, NULL, #0x30  ; false
    // 0xae075c: csel            x0, x16, x17, ne
    // 0xae0760: ldur            x1, [fp, #-8]
    // 0xae0764: stur            x0, [fp, #-0x58]
    // 0xae0768: LoadField: r2 = r1->field_f
    //     0xae0768: ldur            w2, [x1, #0xf]
    // 0xae076c: DecompressPointer r2
    //     0xae076c: add             x2, x2, HEAP, lsl #32
    // 0xae0770: mov             x1, x2
    // 0xae0774: r0 = controller()
    //     0xae0774: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0778: LoadField: r1 = r0->field_33
    //     0xae0778: ldur            w1, [x0, #0x33]
    // 0xae077c: DecompressPointer r1
    //     0xae077c: add             x1, x1, HEAP, lsl #32
    // 0xae0780: r16 = Instance_PaymentType
    //     0xae0780: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae0784: ldr             x16, [x16, #0x608]
    // 0xae0788: cmp             w1, w16
    // 0xae078c: b.eq            #0xae0798
    // 0xae0790: r9 = Null
    //     0xae0790: mov             x9, NULL
    // 0xae0794: b               #0xae07d8
    // 0xae0798: ldr             x0, [fp, #0x18]
    // 0xae079c: LoadField: r1 = r0->field_3b
    //     0xae079c: ldur            w1, [x0, #0x3b]
    // 0xae07a0: DecompressPointer r1
    //     0xae07a0: add             x1, x1, HEAP, lsl #32
    // 0xae07a4: LoadField: r0 = r1->field_f
    //     0xae07a4: ldur            w0, [x1, #0xf]
    // 0xae07a8: DecompressPointer r0
    //     0xae07a8: add             x0, x0, HEAP, lsl #32
    // 0xae07ac: stur            x0, [fp, #-0x60]
    // 0xae07b0: LoadField: r2 = r1->field_13
    //     0xae07b0: ldur            w2, [x1, #0x13]
    // 0xae07b4: DecompressPointer r2
    //     0xae07b4: add             x2, x2, HEAP, lsl #32
    // 0xae07b8: stur            x2, [fp, #-8]
    // 0xae07bc: r0 = NCampaignCardOrganization()
    //     0xae07bc: bl              #0xae0890  ; AllocateNCampaignCardOrganizationStub -> NCampaignCardOrganization (size=0x10)
    // 0xae07c0: mov             x1, x0
    // 0xae07c4: ldur            x0, [fp, #-0x60]
    // 0xae07c8: StoreField: r1->field_7 = r0
    //     0xae07c8: stur            w0, [x1, #7]
    // 0xae07cc: ldur            x0, [fp, #-8]
    // 0xae07d0: StoreField: r1->field_b = r0
    //     0xae07d0: stur            w0, [x1, #0xb]
    // 0xae07d4: mov             x9, x1
    // 0xae07d8: ldur            x6, [fp, #-0x20]
    // 0xae07dc: ldur            x7, [fp, #-0x18]
    // 0xae07e0: ldur            x8, [fp, #-0x10]
    // 0xae07e4: ldur            x5, [fp, #-0x30]
    // 0xae07e8: ldur            x4, [fp, #-0x38]
    // 0xae07ec: ldur            x1, [fp, #-0x50]
    // 0xae07f0: ldur            x0, [fp, #-0x58]
    // 0xae07f4: ldur            x3, [fp, #-0x40]
    // 0xae07f8: ldur            x2, [fp, #-0x48]
    // 0xae07fc: stur            x9, [fp, #-8]
    // 0xae0800: r0 = NCampaignCard()
    //     0xae0800: bl              #0xae04e4  ; AllocateNCampaignCardStub -> NCampaignCard (size=0x3c)
    // 0xae0804: mov             x3, x0
    // 0xae0808: ldur            x0, [fp, #-0x20]
    // 0xae080c: stur            x3, [fp, #-0x60]
    // 0xae0810: StoreField: r3->field_b = r0
    //     0xae0810: stur            w0, [x3, #0xb]
    // 0xae0814: ldur            x0, [fp, #-0x18]
    // 0xae0818: StoreField: r3->field_f = r0
    //     0xae0818: stur            w0, [x3, #0xf]
    // 0xae081c: ldur            x0, [fp, #-0x10]
    // 0xae0820: StoreField: r3->field_13 = r0
    //     0xae0820: stur            w0, [x3, #0x13]
    // 0xae0824: ldur            x0, [fp, #-0x50]
    // 0xae0828: ArrayStore: r3[0] = r0  ; List_4
    //     0xae0828: stur            w0, [x3, #0x17]
    // 0xae082c: ldur            x0, [fp, #-0x30]
    // 0xae0830: StoreField: r3->field_1b = r0
    //     0xae0830: stur            w0, [x3, #0x1b]
    // 0xae0834: ldur            x0, [fp, #-0x38]
    // 0xae0838: StoreField: r3->field_1f = r0
    //     0xae0838: stur            w0, [x3, #0x1f]
    // 0xae083c: ldur            x0, [fp, #-0x40]
    // 0xae0840: LoadField: d0 = r0->field_7
    //     0xae0840: ldur            d0, [x0, #7]
    // 0xae0844: StoreField: r3->field_23 = d0
    //     0xae0844: stur            d0, [x3, #0x23]
    // 0xae0848: ldur            x2, [fp, #-0x28]
    // 0xae084c: r1 = Function '<anonymous closure>':.
    //     0xae084c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30910] AnonymousClosure: (0xae089c), in [package:nuonline/app/modules/donation/views/campaign_list_view.dart] CampaignListView::build (0xadf8fc)
    //     0xae0850: ldr             x1, [x1, #0x910]
    // 0xae0854: r0 = AllocateClosure()
    //     0xae0854: bl              #0xec1630  ; AllocateClosureStub
    // 0xae0858: mov             x1, x0
    // 0xae085c: ldur            x0, [fp, #-0x60]
    // 0xae0860: StoreField: r0->field_2b = r1
    //     0xae0860: stur            w1, [x0, #0x2b]
    // 0xae0864: ldur            x1, [fp, #-0x48]
    // 0xae0868: StoreField: r0->field_2f = r1
    //     0xae0868: stur            w1, [x0, #0x2f]
    // 0xae086c: ldur            x1, [fp, #-0x58]
    // 0xae0870: StoreField: r0->field_33 = r1
    //     0xae0870: stur            w1, [x0, #0x33]
    // 0xae0874: ldur            x1, [fp, #-8]
    // 0xae0878: StoreField: r0->field_37 = r1
    //     0xae0878: stur            w1, [x0, #0x37]
    // 0xae087c: LeaveFrame
    //     0xae087c: mov             SP, fp
    //     0xae0880: ldp             fp, lr, [SP], #0x10
    // 0xae0884: ret
    //     0xae0884: ret             
    // 0xae0888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae088c: b               #0xae05dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae089c, size: 0x120
    // 0xae089c: EnterFrame
    //     0xae089c: stp             fp, lr, [SP, #-0x10]!
    //     0xae08a0: mov             fp, SP
    // 0xae08a4: AllocStack(0x28)
    //     0xae08a4: sub             SP, SP, #0x28
    // 0xae08a8: SetupParameters()
    //     0xae08a8: ldr             x0, [fp, #0x10]
    //     0xae08ac: ldur            w1, [x0, #0x17]
    //     0xae08b0: add             x1, x1, HEAP, lsl #32
    //     0xae08b4: stur            x1, [fp, #-8]
    // 0xae08b8: CheckStackOverflow
    //     0xae08b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae08bc: cmp             SP, x16
    //     0xae08c0: b.ls            #0xae09b4
    // 0xae08c4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae08c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae08c8: ldr             x0, [x0, #0x2670]
    //     0xae08cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae08d0: cmp             w0, w16
    //     0xae08d4: b.ne            #0xae08e0
    //     0xae08d8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae08dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae08e0: r1 = Null
    //     0xae08e0: mov             x1, NULL
    // 0xae08e4: r2 = 8
    //     0xae08e4: movz            x2, #0x8
    // 0xae08e8: r0 = AllocateArray()
    //     0xae08e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae08ec: mov             x2, x0
    // 0xae08f0: stur            x2, [fp, #-0x10]
    // 0xae08f4: r16 = "id"
    //     0xae08f4: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xae08f8: ldr             x16, [x16, #0x740]
    // 0xae08fc: StoreField: r2->field_f = r16
    //     0xae08fc: stur            w16, [x2, #0xf]
    // 0xae0900: ldur            x3, [fp, #-8]
    // 0xae0904: LoadField: r0 = r3->field_f
    //     0xae0904: ldur            w0, [x3, #0xf]
    // 0xae0908: DecompressPointer r0
    //     0xae0908: add             x0, x0, HEAP, lsl #32
    // 0xae090c: LoadField: r4 = r0->field_7
    //     0xae090c: ldur            x4, [x0, #7]
    // 0xae0910: r0 = BoxInt64Instr(r4)
    //     0xae0910: sbfiz           x0, x4, #1, #0x1f
    //     0xae0914: cmp             x4, x0, asr #1
    //     0xae0918: b.eq            #0xae0924
    //     0xae091c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae0920: stur            x4, [x0, #7]
    // 0xae0924: StoreField: r2->field_13 = r0
    //     0xae0924: stur            w0, [x2, #0x13]
    // 0xae0928: r16 = "type"
    //     0xae0928: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0xae092c: ArrayStore: r2[0] = r16  ; List_4
    //     0xae092c: stur            w16, [x2, #0x17]
    // 0xae0930: LoadField: r0 = r3->field_b
    //     0xae0930: ldur            w0, [x3, #0xb]
    // 0xae0934: DecompressPointer r0
    //     0xae0934: add             x0, x0, HEAP, lsl #32
    // 0xae0938: LoadField: r1 = r0->field_f
    //     0xae0938: ldur            w1, [x0, #0xf]
    // 0xae093c: DecompressPointer r1
    //     0xae093c: add             x1, x1, HEAP, lsl #32
    // 0xae0940: r0 = controller()
    //     0xae0940: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0944: LoadField: r1 = r0->field_33
    //     0xae0944: ldur            w1, [x0, #0x33]
    // 0xae0948: DecompressPointer r1
    //     0xae0948: add             x1, x1, HEAP, lsl #32
    // 0xae094c: mov             x0, x1
    // 0xae0950: ldur            x1, [fp, #-0x10]
    // 0xae0954: ArrayStore: r1[3] = r0  ; List_4
    //     0xae0954: add             x25, x1, #0x1b
    //     0xae0958: str             w0, [x25]
    //     0xae095c: tbz             w0, #0, #0xae0978
    //     0xae0960: ldurb           w16, [x1, #-1]
    //     0xae0964: ldurb           w17, [x0, #-1]
    //     0xae0968: and             x16, x17, x16, lsr #2
    //     0xae096c: tst             x16, HEAP, lsr #32
    //     0xae0970: b.eq            #0xae0978
    //     0xae0974: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae0978: r16 = <String, Object>
    //     0xae0978: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xae097c: ldr             x16, [x16, #0x790]
    // 0xae0980: ldur            lr, [fp, #-0x10]
    // 0xae0984: stp             lr, x16, [SP]
    // 0xae0988: r0 = Map._fromLiteral()
    //     0xae0988: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xae098c: r16 = "/donation/campaign-detail"
    //     0xae098c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30430] "/donation/campaign-detail"
    //     0xae0990: ldr             x16, [x16, #0x430]
    // 0xae0994: stp             x16, NULL, [SP, #8]
    // 0xae0998: str             x0, [SP]
    // 0xae099c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae099c: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae09a0: ldr             x4, [x4, #0x478]
    // 0xae09a4: r0 = GetNavigation.toNamed()
    //     0xae09a4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae09a8: LeaveFrame
    //     0xae09a8: mov             SP, fp
    //     0xae09ac: ldp             fp, lr, [SP], #0x10
    // 0xae09b0: ret
    //     0xae09b0: ret             
    // 0xae09b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae09b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae09b8: b               #0xae08c4
  }
}
