// lib: , url: package:nuonline/app/modules/donation/views/confirm_donation_view.dart

// class id: 1050220, size: 0x8
class :: {
}

// class id: 5295, size: 0x14, field offset: 0x14
//   const constructor, 
class ConfirmDonationView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae09bc, size: 0xde0
    // 0xae09bc: EnterFrame
    //     0xae09bc: stp             fp, lr, [SP, #-0x10]!
    //     0xae09c0: mov             fp, SP
    // 0xae09c4: AllocStack(0x70)
    //     0xae09c4: sub             SP, SP, #0x70
    // 0xae09c8: SetupParameters(ConfirmDonationView this /* r1 => r0, fp-0x8 */)
    //     0xae09c8: mov             x0, x1
    //     0xae09cc: stur            x1, [fp, #-8]
    // 0xae09d0: CheckStackOverflow
    //     0xae09d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae09d4: cmp             SP, x16
    //     0xae09d8: b.ls            #0xae1788
    // 0xae09dc: mov             x1, x0
    // 0xae09e0: r0 = controller()
    //     0xae09e0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae09e4: mov             x1, x0
    // 0xae09e8: r0 = title()
    //     0xae09e8: bl              #0xae1a80  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] ConfirmDonationController::title
    // 0xae09ec: stur            x0, [fp, #-0x10]
    // 0xae09f0: r0 = Text()
    //     0xae09f0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae09f4: mov             x1, x0
    // 0xae09f8: ldur            x0, [fp, #-0x10]
    // 0xae09fc: stur            x1, [fp, #-0x18]
    // 0xae0a00: StoreField: r1->field_b = r0
    //     0xae0a00: stur            w0, [x1, #0xb]
    // 0xae0a04: r0 = AppBar()
    //     0xae0a04: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae0a08: stur            x0, [fp, #-0x10]
    // 0xae0a0c: ldur            x16, [fp, #-0x18]
    // 0xae0a10: str             x16, [SP]
    // 0xae0a14: mov             x1, x0
    // 0xae0a18: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae0a18: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae0a1c: ldr             x4, [x4, #0x6e8]
    // 0xae0a20: r0 = AppBar()
    //     0xae0a20: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae0a24: ldur            x1, [fp, #-8]
    // 0xae0a28: r0 = controller()
    //     0xae0a28: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0a2c: mov             x1, x0
    // 0xae0a30: r0 = merchantTitle()
    //     0xae0a30: bl              #0xae195c  ; [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] ConfirmDonationController::merchantTitle
    // 0xae0a34: stur            x0, [fp, #-0x18]
    // 0xae0a38: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae0a38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae0a3c: ldr             x0, [x0, #0x2670]
    //     0xae0a40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae0a44: cmp             w0, w16
    //     0xae0a48: b.ne            #0xae0a54
    //     0xae0a4c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae0a50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae0a54: r0 = GetNavigation.textTheme()
    //     0xae0a54: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae0a58: LoadField: r1 = r0->field_f
    //     0xae0a58: ldur            w1, [x0, #0xf]
    // 0xae0a5c: DecompressPointer r1
    //     0xae0a5c: add             x1, x1, HEAP, lsl #32
    // 0xae0a60: cmp             w1, NULL
    // 0xae0a64: b.eq            #0xae1790
    // 0xae0a68: r16 = 16.000000
    //     0xae0a68: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xae0a6c: ldr             x16, [x16, #0x80]
    // 0xae0a70: str             x16, [SP]
    // 0xae0a74: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xae0a74: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xae0a78: ldr             x4, [x4, #0x88]
    // 0xae0a7c: r0 = copyWith()
    //     0xae0a7c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae0a80: ldur            x1, [fp, #-8]
    // 0xae0a84: stur            x0, [fp, #-0x20]
    // 0xae0a88: r0 = controller()
    //     0xae0a88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0a8c: ldur            x1, [fp, #-8]
    // 0xae0a90: r0 = controller()
    //     0xae0a90: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0a94: LoadField: r1 = r0->field_2b
    //     0xae0a94: ldur            w1, [x0, #0x2b]
    // 0xae0a98: DecompressPointer r1
    //     0xae0a98: add             x1, x1, HEAP, lsl #32
    // 0xae0a9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xae0a9c: ldur            w0, [x1, #0x17]
    // 0xae0aa0: DecompressPointer r0
    //     0xae0aa0: add             x0, x0, HEAP, lsl #32
    // 0xae0aa4: r1 = 60
    //     0xae0aa4: movz            x1, #0x3c
    // 0xae0aa8: branchIfSmi(r0, 0xae0ab4)
    //     0xae0aa8: tbz             w0, #0, #0xae0ab4
    // 0xae0aac: r1 = LoadClassIdInstr(r0)
    //     0xae0aac: ldur            x1, [x0, #-1]
    //     0xae0ab0: ubfx            x1, x1, #0xc, #0x14
    // 0xae0ab4: str             x0, [SP]
    // 0xae0ab8: mov             x0, x1
    // 0xae0abc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xae0abc: sub             lr, x0, #1, lsl #12
    //     0xae0ac0: ldr             lr, [x21, lr, lsl #3]
    //     0xae0ac4: blr             lr
    // 0xae0ac8: stur            x0, [fp, #-0x28]
    // 0xae0acc: r0 = PaymentInfo()
    //     0xae0acc: bl              #0xae1950  ; AllocatePaymentInfoStub -> PaymentInfo (size=0x18)
    // 0xae0ad0: mov             x2, x0
    // 0xae0ad4: r0 = "Nominal"
    //     0xae0ad4: add             x0, PP, #0x30, lsl #12  ; [pp+0x30500] "Nominal"
    //     0xae0ad8: ldr             x0, [x0, #0x500]
    // 0xae0adc: stur            x2, [fp, #-0x30]
    // 0xae0ae0: StoreField: r2->field_b = r0
    //     0xae0ae0: stur            w0, [x2, #0xb]
    // 0xae0ae4: ldur            x0, [fp, #-0x28]
    // 0xae0ae8: r1 = LoadInt32Instr(r0)
    //     0xae0ae8: sbfx            x1, x0, #1, #0x1f
    //     0xae0aec: tbz             w0, #0, #0xae0af4
    //     0xae0af0: ldur            x1, [x0, #7]
    // 0xae0af4: StoreField: r2->field_f = r1
    //     0xae0af4: stur            x1, [x2, #0xf]
    // 0xae0af8: ldur            x1, [fp, #-8]
    // 0xae0afc: r0 = controller()
    //     0xae0afc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0b00: LoadField: r1 = r0->field_2b
    //     0xae0b00: ldur            w1, [x0, #0x2b]
    // 0xae0b04: DecompressPointer r1
    //     0xae0b04: add             x1, x1, HEAP, lsl #32
    // 0xae0b08: LoadField: r0 = r1->field_1b
    //     0xae0b08: ldur            w0, [x1, #0x1b]
    // 0xae0b0c: DecompressPointer r0
    //     0xae0b0c: add             x0, x0, HEAP, lsl #32
    // 0xae0b10: stur            x0, [fp, #-0x28]
    // 0xae0b14: r1 = Null
    //     0xae0b14: mov             x1, NULL
    // 0xae0b18: r2 = 2
    //     0xae0b18: movz            x2, #0x2
    // 0xae0b1c: r0 = AllocateArray()
    //     0xae0b1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae0b20: mov             x2, x0
    // 0xae0b24: ldur            x0, [fp, #-0x28]
    // 0xae0b28: stur            x2, [fp, #-0x38]
    // 0xae0b2c: StoreField: r2->field_f = r0
    //     0xae0b2c: stur            w0, [x2, #0xf]
    // 0xae0b30: r1 = <String>
    //     0xae0b30: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae0b34: r0 = AllocateGrowableArray()
    //     0xae0b34: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae0b38: mov             x2, x0
    // 0xae0b3c: ldur            x0, [fp, #-0x38]
    // 0xae0b40: stur            x2, [fp, #-0x28]
    // 0xae0b44: StoreField: r2->field_f = r0
    //     0xae0b44: stur            w0, [x2, #0xf]
    // 0xae0b48: r0 = 2
    //     0xae0b48: movz            x0, #0x2
    // 0xae0b4c: StoreField: r2->field_b = r0
    //     0xae0b4c: stur            w0, [x2, #0xb]
    // 0xae0b50: ldur            x1, [fp, #-8]
    // 0xae0b54: r0 = controller()
    //     0xae0b54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0b58: LoadField: r1 = r0->field_2b
    //     0xae0b58: ldur            w1, [x0, #0x2b]
    // 0xae0b5c: DecompressPointer r1
    //     0xae0b5c: add             x1, x1, HEAP, lsl #32
    // 0xae0b60: LoadField: r0 = r1->field_33
    //     0xae0b60: ldur            w0, [x1, #0x33]
    // 0xae0b64: DecompressPointer r0
    //     0xae0b64: add             x0, x0, HEAP, lsl #32
    // 0xae0b68: tbnz            w0, #4, #0xae0bcc
    // 0xae0b6c: ldur            x0, [fp, #-0x28]
    // 0xae0b70: LoadField: r1 = r0->field_b
    //     0xae0b70: ldur            w1, [x0, #0xb]
    // 0xae0b74: LoadField: r2 = r0->field_f
    //     0xae0b74: ldur            w2, [x0, #0xf]
    // 0xae0b78: DecompressPointer r2
    //     0xae0b78: add             x2, x2, HEAP, lsl #32
    // 0xae0b7c: LoadField: r3 = r2->field_b
    //     0xae0b7c: ldur            w3, [x2, #0xb]
    // 0xae0b80: r2 = LoadInt32Instr(r1)
    //     0xae0b80: sbfx            x2, x1, #1, #0x1f
    // 0xae0b84: stur            x2, [fp, #-0x40]
    // 0xae0b88: r1 = LoadInt32Instr(r3)
    //     0xae0b88: sbfx            x1, x3, #1, #0x1f
    // 0xae0b8c: cmp             x2, x1
    // 0xae0b90: b.ne            #0xae0b9c
    // 0xae0b94: mov             x1, x0
    // 0xae0b98: r0 = _growToNextCapacity()
    //     0xae0b98: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0b9c: ldur            x1, [fp, #-0x28]
    // 0xae0ba0: ldur            x0, [fp, #-0x40]
    // 0xae0ba4: add             x2, x0, #1
    // 0xae0ba8: lsl             x3, x2, #1
    // 0xae0bac: StoreField: r1->field_b = r3
    //     0xae0bac: stur            w3, [x1, #0xb]
    // 0xae0bb0: LoadField: r2 = r1->field_f
    //     0xae0bb0: ldur            w2, [x1, #0xf]
    // 0xae0bb4: DecompressPointer r2
    //     0xae0bb4: add             x2, x2, HEAP, lsl #32
    // 0xae0bb8: add             x3, x2, x0, lsl #2
    // 0xae0bbc: r16 = "(Anonim)"
    //     0xae0bbc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30508] "(Anonim)"
    //     0xae0bc0: ldr             x16, [x16, #0x508]
    // 0xae0bc4: StoreField: r3->field_f = r16
    //     0xae0bc4: stur            w16, [x3, #0xf]
    // 0xae0bc8: b               #0xae0bd0
    // 0xae0bcc: ldur            x1, [fp, #-0x28]
    // 0xae0bd0: ldur            x0, [fp, #-0x30]
    // 0xae0bd4: r16 = " "
    //     0xae0bd4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xae0bd8: str             x16, [SP]
    // 0xae0bdc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xae0bdc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xae0be0: r0 = join()
    //     0xae0be0: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xae0be4: stur            x0, [fp, #-0x28]
    // 0xae0be8: r0 = UserInfo()
    //     0xae0be8: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xae0bec: mov             x3, x0
    // 0xae0bf0: r0 = "Nama"
    //     0xae0bf0: add             x0, PP, #0x30, lsl #12  ; [pp+0x30510] "Nama"
    //     0xae0bf4: ldr             x0, [x0, #0x510]
    // 0xae0bf8: stur            x3, [fp, #-0x38]
    // 0xae0bfc: StoreField: r3->field_b = r0
    //     0xae0bfc: stur            w0, [x3, #0xb]
    // 0xae0c00: ldur            x0, [fp, #-0x28]
    // 0xae0c04: StoreField: r3->field_f = r0
    //     0xae0c04: stur            w0, [x3, #0xf]
    // 0xae0c08: r0 = false
    //     0xae0c08: add             x0, NULL, #0x30  ; false
    // 0xae0c0c: ArrayStore: r3[0] = r0  ; List_4
    //     0xae0c0c: stur            w0, [x3, #0x17]
    // 0xae0c10: r1 = Null
    //     0xae0c10: mov             x1, NULL
    // 0xae0c14: r2 = 8
    //     0xae0c14: movz            x2, #0x8
    // 0xae0c18: r0 = AllocateArray()
    //     0xae0c18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae0c1c: stur            x0, [fp, #-0x28]
    // 0xae0c20: r16 = Instance_SizedBox
    //     0xae0c20: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xae0c24: ldr             x16, [x16, #0xfb0]
    // 0xae0c28: StoreField: r0->field_f = r16
    //     0xae0c28: stur            w16, [x0, #0xf]
    // 0xae0c2c: ldur            x1, [fp, #-0x30]
    // 0xae0c30: StoreField: r0->field_13 = r1
    //     0xae0c30: stur            w1, [x0, #0x13]
    // 0xae0c34: r16 = Instance_SizedBox
    //     0xae0c34: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae0c38: ldr             x16, [x16, #0xfe8]
    // 0xae0c3c: ArrayStore: r0[0] = r16  ; List_4
    //     0xae0c3c: stur            w16, [x0, #0x17]
    // 0xae0c40: ldur            x1, [fp, #-0x38]
    // 0xae0c44: StoreField: r0->field_1b = r1
    //     0xae0c44: stur            w1, [x0, #0x1b]
    // 0xae0c48: r1 = <Widget>
    //     0xae0c48: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae0c4c: r0 = AllocateGrowableArray()
    //     0xae0c4c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae0c50: mov             x2, x0
    // 0xae0c54: ldur            x0, [fp, #-0x28]
    // 0xae0c58: stur            x2, [fp, #-0x30]
    // 0xae0c5c: StoreField: r2->field_f = r0
    //     0xae0c5c: stur            w0, [x2, #0xf]
    // 0xae0c60: r0 = 8
    //     0xae0c60: movz            x0, #0x8
    // 0xae0c64: StoreField: r2->field_b = r0
    //     0xae0c64: stur            w0, [x2, #0xb]
    // 0xae0c68: ldur            x1, [fp, #-8]
    // 0xae0c6c: r0 = controller()
    //     0xae0c6c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0c70: LoadField: r1 = r0->field_2b
    //     0xae0c70: ldur            w1, [x0, #0x2b]
    // 0xae0c74: DecompressPointer r1
    //     0xae0c74: add             x1, x1, HEAP, lsl #32
    // 0xae0c78: LoadField: r0 = r1->field_2f
    //     0xae0c78: ldur            w0, [x1, #0x2f]
    // 0xae0c7c: DecompressPointer r0
    //     0xae0c7c: add             x0, x0, HEAP, lsl #32
    // 0xae0c80: cmp             w0, NULL
    // 0xae0c84: b.ne            #0xae0c90
    // 0xae0c88: r0 = Null
    //     0xae0c88: mov             x0, NULL
    // 0xae0c8c: b               #0xae0ca4
    // 0xae0c90: LoadField: r1 = r0->field_7
    //     0xae0c90: ldur            w1, [x0, #7]
    // 0xae0c94: cbnz            w1, #0xae0ca0
    // 0xae0c98: r0 = false
    //     0xae0c98: add             x0, NULL, #0x30  ; false
    // 0xae0c9c: b               #0xae0ca4
    // 0xae0ca0: r0 = true
    //     0xae0ca0: add             x0, NULL, #0x20  ; true
    // 0xae0ca4: cmp             w0, NULL
    // 0xae0ca8: b.ne            #0xae0cb4
    // 0xae0cac: ldur            x2, [fp, #-0x30]
    // 0xae0cb0: b               #0xae0d80
    // 0xae0cb4: tbnz            w0, #4, #0xae0d7c
    // 0xae0cb8: ldur            x0, [fp, #-0x30]
    // 0xae0cbc: ldur            x1, [fp, #-8]
    // 0xae0cc0: r0 = controller()
    //     0xae0cc0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0cc4: LoadField: r1 = r0->field_2b
    //     0xae0cc4: ldur            w1, [x0, #0x2b]
    // 0xae0cc8: DecompressPointer r1
    //     0xae0cc8: add             x1, x1, HEAP, lsl #32
    // 0xae0ccc: LoadField: r0 = r1->field_2f
    //     0xae0ccc: ldur            w0, [x1, #0x2f]
    // 0xae0cd0: DecompressPointer r0
    //     0xae0cd0: add             x0, x0, HEAP, lsl #32
    // 0xae0cd4: stur            x0, [fp, #-0x28]
    // 0xae0cd8: r0 = UserInfo()
    //     0xae0cd8: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xae0cdc: mov             x2, x0
    // 0xae0ce0: r0 = "Deskripsi"
    //     0xae0ce0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f658] "Deskripsi"
    //     0xae0ce4: ldr             x0, [x0, #0x658]
    // 0xae0ce8: stur            x2, [fp, #-0x38]
    // 0xae0cec: StoreField: r2->field_b = r0
    //     0xae0cec: stur            w0, [x2, #0xb]
    // 0xae0cf0: ldur            x0, [fp, #-0x28]
    // 0xae0cf4: StoreField: r2->field_f = r0
    //     0xae0cf4: stur            w0, [x2, #0xf]
    // 0xae0cf8: r0 = false
    //     0xae0cf8: add             x0, NULL, #0x30  ; false
    // 0xae0cfc: ArrayStore: r2[0] = r0  ; List_4
    //     0xae0cfc: stur            w0, [x2, #0x17]
    // 0xae0d00: ldur            x3, [fp, #-0x30]
    // 0xae0d04: LoadField: r1 = r3->field_b
    //     0xae0d04: ldur            w1, [x3, #0xb]
    // 0xae0d08: LoadField: r4 = r3->field_f
    //     0xae0d08: ldur            w4, [x3, #0xf]
    // 0xae0d0c: DecompressPointer r4
    //     0xae0d0c: add             x4, x4, HEAP, lsl #32
    // 0xae0d10: LoadField: r5 = r4->field_b
    //     0xae0d10: ldur            w5, [x4, #0xb]
    // 0xae0d14: r4 = LoadInt32Instr(r1)
    //     0xae0d14: sbfx            x4, x1, #1, #0x1f
    // 0xae0d18: stur            x4, [fp, #-0x40]
    // 0xae0d1c: r1 = LoadInt32Instr(r5)
    //     0xae0d1c: sbfx            x1, x5, #1, #0x1f
    // 0xae0d20: cmp             x4, x1
    // 0xae0d24: b.ne            #0xae0d30
    // 0xae0d28: mov             x1, x3
    // 0xae0d2c: r0 = _growToNextCapacity()
    //     0xae0d2c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0d30: ldur            x2, [fp, #-0x30]
    // 0xae0d34: ldur            x3, [fp, #-0x40]
    // 0xae0d38: add             x0, x3, #1
    // 0xae0d3c: lsl             x1, x0, #1
    // 0xae0d40: StoreField: r2->field_b = r1
    //     0xae0d40: stur            w1, [x2, #0xb]
    // 0xae0d44: LoadField: r1 = r2->field_f
    //     0xae0d44: ldur            w1, [x2, #0xf]
    // 0xae0d48: DecompressPointer r1
    //     0xae0d48: add             x1, x1, HEAP, lsl #32
    // 0xae0d4c: ldur            x0, [fp, #-0x38]
    // 0xae0d50: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0d50: add             x25, x1, x3, lsl #2
    //     0xae0d54: add             x25, x25, #0xf
    //     0xae0d58: str             w0, [x25]
    //     0xae0d5c: tbz             w0, #0, #0xae0d78
    //     0xae0d60: ldurb           w16, [x1, #-1]
    //     0xae0d64: ldurb           w17, [x0, #-1]
    //     0xae0d68: and             x16, x17, x16, lsr #2
    //     0xae0d6c: tst             x16, HEAP, lsr #32
    //     0xae0d70: b.eq            #0xae0d78
    //     0xae0d74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae0d78: b               #0xae0d80
    // 0xae0d7c: ldur            x2, [fp, #-0x30]
    // 0xae0d80: ldur            x1, [fp, #-8]
    // 0xae0d84: r0 = controller()
    //     0xae0d84: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0d88: LoadField: r1 = r0->field_2b
    //     0xae0d88: ldur            w1, [x0, #0x2b]
    // 0xae0d8c: DecompressPointer r1
    //     0xae0d8c: add             x1, x1, HEAP, lsl #32
    // 0xae0d90: LoadField: r0 = r1->field_27
    //     0xae0d90: ldur            w0, [x1, #0x27]
    // 0xae0d94: DecompressPointer r0
    //     0xae0d94: add             x0, x0, HEAP, lsl #32
    // 0xae0d98: cmp             w0, NULL
    // 0xae0d9c: b.ne            #0xae0da8
    // 0xae0da0: ldur            x2, [fp, #-0x30]
    // 0xae0da4: b               #0xae0e78
    // 0xae0da8: LoadField: r1 = r0->field_7
    //     0xae0da8: ldur            w1, [x0, #7]
    // 0xae0dac: cbz             w1, #0xae0e74
    // 0xae0db0: ldur            x0, [fp, #-0x30]
    // 0xae0db4: ldur            x1, [fp, #-8]
    // 0xae0db8: r0 = controller()
    //     0xae0db8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0dbc: LoadField: r1 = r0->field_2b
    //     0xae0dbc: ldur            w1, [x0, #0x2b]
    // 0xae0dc0: DecompressPointer r1
    //     0xae0dc0: add             x1, x1, HEAP, lsl #32
    // 0xae0dc4: LoadField: r0 = r1->field_27
    //     0xae0dc4: ldur            w0, [x1, #0x27]
    // 0xae0dc8: DecompressPointer r0
    //     0xae0dc8: add             x0, x0, HEAP, lsl #32
    // 0xae0dcc: stur            x0, [fp, #-0x28]
    // 0xae0dd0: r0 = UserInfo()
    //     0xae0dd0: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xae0dd4: mov             x2, x0
    // 0xae0dd8: r0 = "Email"
    //     0xae0dd8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30518] "Email"
    //     0xae0ddc: ldr             x0, [x0, #0x518]
    // 0xae0de0: stur            x2, [fp, #-0x38]
    // 0xae0de4: StoreField: r2->field_b = r0
    //     0xae0de4: stur            w0, [x2, #0xb]
    // 0xae0de8: ldur            x0, [fp, #-0x28]
    // 0xae0dec: StoreField: r2->field_f = r0
    //     0xae0dec: stur            w0, [x2, #0xf]
    // 0xae0df0: r0 = false
    //     0xae0df0: add             x0, NULL, #0x30  ; false
    // 0xae0df4: ArrayStore: r2[0] = r0  ; List_4
    //     0xae0df4: stur            w0, [x2, #0x17]
    // 0xae0df8: ldur            x3, [fp, #-0x30]
    // 0xae0dfc: LoadField: r1 = r3->field_b
    //     0xae0dfc: ldur            w1, [x3, #0xb]
    // 0xae0e00: LoadField: r4 = r3->field_f
    //     0xae0e00: ldur            w4, [x3, #0xf]
    // 0xae0e04: DecompressPointer r4
    //     0xae0e04: add             x4, x4, HEAP, lsl #32
    // 0xae0e08: LoadField: r5 = r4->field_b
    //     0xae0e08: ldur            w5, [x4, #0xb]
    // 0xae0e0c: r4 = LoadInt32Instr(r1)
    //     0xae0e0c: sbfx            x4, x1, #1, #0x1f
    // 0xae0e10: stur            x4, [fp, #-0x40]
    // 0xae0e14: r1 = LoadInt32Instr(r5)
    //     0xae0e14: sbfx            x1, x5, #1, #0x1f
    // 0xae0e18: cmp             x4, x1
    // 0xae0e1c: b.ne            #0xae0e28
    // 0xae0e20: mov             x1, x3
    // 0xae0e24: r0 = _growToNextCapacity()
    //     0xae0e24: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0e28: ldur            x2, [fp, #-0x30]
    // 0xae0e2c: ldur            x3, [fp, #-0x40]
    // 0xae0e30: add             x0, x3, #1
    // 0xae0e34: lsl             x1, x0, #1
    // 0xae0e38: StoreField: r2->field_b = r1
    //     0xae0e38: stur            w1, [x2, #0xb]
    // 0xae0e3c: LoadField: r1 = r2->field_f
    //     0xae0e3c: ldur            w1, [x2, #0xf]
    // 0xae0e40: DecompressPointer r1
    //     0xae0e40: add             x1, x1, HEAP, lsl #32
    // 0xae0e44: ldur            x0, [fp, #-0x38]
    // 0xae0e48: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0e48: add             x25, x1, x3, lsl #2
    //     0xae0e4c: add             x25, x25, #0xf
    //     0xae0e50: str             w0, [x25]
    //     0xae0e54: tbz             w0, #0, #0xae0e70
    //     0xae0e58: ldurb           w16, [x1, #-1]
    //     0xae0e5c: ldurb           w17, [x0, #-1]
    //     0xae0e60: and             x16, x17, x16, lsr #2
    //     0xae0e64: tst             x16, HEAP, lsr #32
    //     0xae0e68: b.eq            #0xae0e70
    //     0xae0e6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae0e70: b               #0xae0e78
    // 0xae0e74: ldur            x2, [fp, #-0x30]
    // 0xae0e78: ldur            x1, [fp, #-8]
    // 0xae0e7c: r0 = controller()
    //     0xae0e7c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0e80: LoadField: r1 = r0->field_2b
    //     0xae0e80: ldur            w1, [x0, #0x2b]
    // 0xae0e84: DecompressPointer r1
    //     0xae0e84: add             x1, x1, HEAP, lsl #32
    // 0xae0e88: LoadField: r0 = r1->field_2b
    //     0xae0e88: ldur            w0, [x1, #0x2b]
    // 0xae0e8c: DecompressPointer r0
    //     0xae0e8c: add             x0, x0, HEAP, lsl #32
    // 0xae0e90: cmp             w0, NULL
    // 0xae0e94: b.ne            #0xae0ea0
    // 0xae0e98: ldur            x2, [fp, #-0x30]
    // 0xae0e9c: b               #0xae0f70
    // 0xae0ea0: LoadField: r1 = r0->field_7
    //     0xae0ea0: ldur            w1, [x0, #7]
    // 0xae0ea4: cbz             w1, #0xae0f6c
    // 0xae0ea8: ldur            x0, [fp, #-0x30]
    // 0xae0eac: ldur            x1, [fp, #-8]
    // 0xae0eb0: r0 = controller()
    //     0xae0eb0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0eb4: LoadField: r1 = r0->field_2b
    //     0xae0eb4: ldur            w1, [x0, #0x2b]
    // 0xae0eb8: DecompressPointer r1
    //     0xae0eb8: add             x1, x1, HEAP, lsl #32
    // 0xae0ebc: LoadField: r0 = r1->field_2b
    //     0xae0ebc: ldur            w0, [x1, #0x2b]
    // 0xae0ec0: DecompressPointer r0
    //     0xae0ec0: add             x0, x0, HEAP, lsl #32
    // 0xae0ec4: stur            x0, [fp, #-0x28]
    // 0xae0ec8: r0 = UserInfo()
    //     0xae0ec8: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xae0ecc: mov             x2, x0
    // 0xae0ed0: r0 = "No. HP"
    //     0xae0ed0: add             x0, PP, #0x30, lsl #12  ; [pp+0x30520] "No. HP"
    //     0xae0ed4: ldr             x0, [x0, #0x520]
    // 0xae0ed8: stur            x2, [fp, #-0x38]
    // 0xae0edc: StoreField: r2->field_b = r0
    //     0xae0edc: stur            w0, [x2, #0xb]
    // 0xae0ee0: ldur            x0, [fp, #-0x28]
    // 0xae0ee4: StoreField: r2->field_f = r0
    //     0xae0ee4: stur            w0, [x2, #0xf]
    // 0xae0ee8: r0 = false
    //     0xae0ee8: add             x0, NULL, #0x30  ; false
    // 0xae0eec: ArrayStore: r2[0] = r0  ; List_4
    //     0xae0eec: stur            w0, [x2, #0x17]
    // 0xae0ef0: ldur            x3, [fp, #-0x30]
    // 0xae0ef4: LoadField: r1 = r3->field_b
    //     0xae0ef4: ldur            w1, [x3, #0xb]
    // 0xae0ef8: LoadField: r4 = r3->field_f
    //     0xae0ef8: ldur            w4, [x3, #0xf]
    // 0xae0efc: DecompressPointer r4
    //     0xae0efc: add             x4, x4, HEAP, lsl #32
    // 0xae0f00: LoadField: r5 = r4->field_b
    //     0xae0f00: ldur            w5, [x4, #0xb]
    // 0xae0f04: r4 = LoadInt32Instr(r1)
    //     0xae0f04: sbfx            x4, x1, #1, #0x1f
    // 0xae0f08: stur            x4, [fp, #-0x40]
    // 0xae0f0c: r1 = LoadInt32Instr(r5)
    //     0xae0f0c: sbfx            x1, x5, #1, #0x1f
    // 0xae0f10: cmp             x4, x1
    // 0xae0f14: b.ne            #0xae0f20
    // 0xae0f18: mov             x1, x3
    // 0xae0f1c: r0 = _growToNextCapacity()
    //     0xae0f1c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0f20: ldur            x2, [fp, #-0x30]
    // 0xae0f24: ldur            x3, [fp, #-0x40]
    // 0xae0f28: add             x0, x3, #1
    // 0xae0f2c: lsl             x1, x0, #1
    // 0xae0f30: StoreField: r2->field_b = r1
    //     0xae0f30: stur            w1, [x2, #0xb]
    // 0xae0f34: LoadField: r1 = r2->field_f
    //     0xae0f34: ldur            w1, [x2, #0xf]
    // 0xae0f38: DecompressPointer r1
    //     0xae0f38: add             x1, x1, HEAP, lsl #32
    // 0xae0f3c: ldur            x0, [fp, #-0x38]
    // 0xae0f40: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0f40: add             x25, x1, x3, lsl #2
    //     0xae0f44: add             x25, x25, #0xf
    //     0xae0f48: str             w0, [x25]
    //     0xae0f4c: tbz             w0, #0, #0xae0f68
    //     0xae0f50: ldurb           w16, [x1, #-1]
    //     0xae0f54: ldurb           w17, [x0, #-1]
    //     0xae0f58: and             x16, x17, x16, lsr #2
    //     0xae0f5c: tst             x16, HEAP, lsr #32
    //     0xae0f60: b.eq            #0xae0f68
    //     0xae0f64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae0f68: b               #0xae0f70
    // 0xae0f6c: ldur            x2, [fp, #-0x30]
    // 0xae0f70: ldur            x1, [fp, #-8]
    // 0xae0f74: r0 = controller()
    //     0xae0f74: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae0f78: LoadField: r1 = r0->field_2b
    //     0xae0f78: ldur            w1, [x0, #0x2b]
    // 0xae0f7c: DecompressPointer r1
    //     0xae0f7c: add             x1, x1, HEAP, lsl #32
    // 0xae0f80: LoadField: r0 = r1->field_b
    //     0xae0f80: ldur            w0, [x1, #0xb]
    // 0xae0f84: DecompressPointer r0
    //     0xae0f84: add             x0, x0, HEAP, lsl #32
    // 0xae0f88: LoadField: r1 = r0->field_7
    //     0xae0f88: ldur            x1, [x0, #7]
    // 0xae0f8c: cmp             x1, #1
    // 0xae0f90: b.gt            #0xae0fb4
    // 0xae0f94: cmp             x1, #0
    // 0xae0f98: b.gt            #0xae0fa8
    // 0xae0f9c: r0 = "QRIS"
    //     0xae0f9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xae0fa0: ldr             x0, [x0, #0xf50]
    // 0xae0fa4: b               #0xae0fd0
    // 0xae0fa8: r0 = "BNI Virtual Account"
    //     0xae0fa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xae0fac: ldr             x0, [x0, #0xf58]
    // 0xae0fb0: b               #0xae0fd0
    // 0xae0fb4: cmp             x1, #2
    // 0xae0fb8: b.gt            #0xae0fc8
    // 0xae0fbc: r0 = "BSI Virtual Account"
    //     0xae0fbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xae0fc0: ldr             x0, [x0, #0xf60]
    // 0xae0fc4: b               #0xae0fd0
    // 0xae0fc8: r0 = "BRI Virtual Account"
    //     0xae0fc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xae0fcc: ldr             x0, [x0, #0xf68]
    // 0xae0fd0: ldur            x1, [fp, #-0x30]
    // 0xae0fd4: stur            x0, [fp, #-0x28]
    // 0xae0fd8: r0 = UserInfo()
    //     0xae0fd8: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xae0fdc: mov             x2, x0
    // 0xae0fe0: r0 = "Metode Pembayaran"
    //     0xae0fe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] "Metode Pembayaran"
    //     0xae0fe4: ldr             x0, [x0, #0xe08]
    // 0xae0fe8: stur            x2, [fp, #-0x38]
    // 0xae0fec: StoreField: r2->field_b = r0
    //     0xae0fec: stur            w0, [x2, #0xb]
    // 0xae0ff0: ldur            x0, [fp, #-0x28]
    // 0xae0ff4: StoreField: r2->field_f = r0
    //     0xae0ff4: stur            w0, [x2, #0xf]
    // 0xae0ff8: r0 = false
    //     0xae0ff8: add             x0, NULL, #0x30  ; false
    // 0xae0ffc: ArrayStore: r2[0] = r0  ; List_4
    //     0xae0ffc: stur            w0, [x2, #0x17]
    // 0xae1000: ldur            x3, [fp, #-0x30]
    // 0xae1004: LoadField: r1 = r3->field_b
    //     0xae1004: ldur            w1, [x3, #0xb]
    // 0xae1008: LoadField: r4 = r3->field_f
    //     0xae1008: ldur            w4, [x3, #0xf]
    // 0xae100c: DecompressPointer r4
    //     0xae100c: add             x4, x4, HEAP, lsl #32
    // 0xae1010: LoadField: r5 = r4->field_b
    //     0xae1010: ldur            w5, [x4, #0xb]
    // 0xae1014: r4 = LoadInt32Instr(r1)
    //     0xae1014: sbfx            x4, x1, #1, #0x1f
    // 0xae1018: stur            x4, [fp, #-0x40]
    // 0xae101c: r1 = LoadInt32Instr(r5)
    //     0xae101c: sbfx            x1, x5, #1, #0x1f
    // 0xae1020: cmp             x4, x1
    // 0xae1024: b.ne            #0xae1030
    // 0xae1028: mov             x1, x3
    // 0xae102c: r0 = _growToNextCapacity()
    //     0xae102c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1030: ldur            x5, [fp, #-0x18]
    // 0xae1034: ldur            x4, [fp, #-0x20]
    // 0xae1038: ldur            x2, [fp, #-0x30]
    // 0xae103c: ldur            x3, [fp, #-0x40]
    // 0xae1040: add             x0, x3, #1
    // 0xae1044: lsl             x1, x0, #1
    // 0xae1048: StoreField: r2->field_b = r1
    //     0xae1048: stur            w1, [x2, #0xb]
    // 0xae104c: LoadField: r1 = r2->field_f
    //     0xae104c: ldur            w1, [x2, #0xf]
    // 0xae1050: DecompressPointer r1
    //     0xae1050: add             x1, x1, HEAP, lsl #32
    // 0xae1054: ldur            x0, [fp, #-0x38]
    // 0xae1058: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae1058: add             x25, x1, x3, lsl #2
    //     0xae105c: add             x25, x25, #0xf
    //     0xae1060: str             w0, [x25]
    //     0xae1064: tbz             w0, #0, #0xae1080
    //     0xae1068: ldurb           w16, [x1, #-1]
    //     0xae106c: ldurb           w17, [x0, #-1]
    //     0xae1070: and             x16, x17, x16, lsr #2
    //     0xae1074: tst             x16, HEAP, lsr #32
    //     0xae1078: b.eq            #0xae1080
    //     0xae107c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae1080: r0 = NSection()
    //     0xae1080: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae1084: mov             x3, x0
    // 0xae1088: ldur            x0, [fp, #-0x18]
    // 0xae108c: stur            x3, [fp, #-0x28]
    // 0xae1090: StoreField: r3->field_b = r0
    //     0xae1090: stur            w0, [x3, #0xb]
    // 0xae1094: ldur            x0, [fp, #-0x30]
    // 0xae1098: StoreField: r3->field_f = r0
    //     0xae1098: stur            w0, [x3, #0xf]
    // 0xae109c: ldur            x0, [fp, #-0x20]
    // 0xae10a0: StoreField: r3->field_1f = r0
    //     0xae10a0: stur            w0, [x3, #0x1f]
    // 0xae10a4: r0 = true
    //     0xae10a4: add             x0, NULL, #0x20  ; true
    // 0xae10a8: StoreField: r3->field_27 = r0
    //     0xae10a8: stur            w0, [x3, #0x27]
    // 0xae10ac: r4 = false
    //     0xae10ac: add             x4, NULL, #0x30  ; false
    // 0xae10b0: StoreField: r3->field_2b = r4
    //     0xae10b0: stur            w4, [x3, #0x2b]
    // 0xae10b4: r1 = Null
    //     0xae10b4: mov             x1, NULL
    // 0xae10b8: r2 = 4
    //     0xae10b8: movz            x2, #0x4
    // 0xae10bc: r0 = AllocateArray()
    //     0xae10bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae10c0: mov             x2, x0
    // 0xae10c4: ldur            x0, [fp, #-0x28]
    // 0xae10c8: stur            x2, [fp, #-0x18]
    // 0xae10cc: StoreField: r2->field_f = r0
    //     0xae10cc: stur            w0, [x2, #0xf]
    // 0xae10d0: r16 = Instance_NSectionDivider
    //     0xae10d0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xae10d4: ldr             x16, [x16, #0x38]
    // 0xae10d8: StoreField: r2->field_13 = r16
    //     0xae10d8: stur            w16, [x2, #0x13]
    // 0xae10dc: r1 = <Widget>
    //     0xae10dc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae10e0: r0 = AllocateGrowableArray()
    //     0xae10e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae10e4: mov             x2, x0
    // 0xae10e8: ldur            x0, [fp, #-0x18]
    // 0xae10ec: stur            x2, [fp, #-0x20]
    // 0xae10f0: StoreField: r2->field_f = r0
    //     0xae10f0: stur            w0, [x2, #0xf]
    // 0xae10f4: r0 = 4
    //     0xae10f4: movz            x0, #0x4
    // 0xae10f8: StoreField: r2->field_b = r0
    //     0xae10f8: stur            w0, [x2, #0xb]
    // 0xae10fc: ldur            x1, [fp, #-8]
    // 0xae1100: r0 = controller()
    //     0xae1100: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1104: LoadField: r1 = r0->field_2b
    //     0xae1104: ldur            w1, [x0, #0x2b]
    // 0xae1108: DecompressPointer r1
    //     0xae1108: add             x1, x1, HEAP, lsl #32
    // 0xae110c: LoadField: r0 = r1->field_13
    //     0xae110c: ldur            w0, [x1, #0x13]
    // 0xae1110: DecompressPointer r0
    //     0xae1110: add             x0, x0, HEAP, lsl #32
    // 0xae1114: cmp             w0, NULL
    // 0xae1118: b.eq            #0xae1280
    // 0xae111c: ldur            x1, [fp, #-8]
    // 0xae1120: r0 = controller()
    //     0xae1120: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1124: LoadField: r1 = r0->field_2b
    //     0xae1124: ldur            w1, [x0, #0x2b]
    // 0xae1128: DecompressPointer r1
    //     0xae1128: add             x1, x1, HEAP, lsl #32
    // 0xae112c: LoadField: r0 = r1->field_13
    //     0xae112c: ldur            w0, [x1, #0x13]
    // 0xae1130: DecompressPointer r0
    //     0xae1130: add             x0, x0, HEAP, lsl #32
    // 0xae1134: r16 = Instance_ZakatTypes
    //     0xae1134: add             x16, PP, #0x30, lsl #12  ; [pp+0x30528] Obj!ZakatTypes@e307a1
    //     0xae1138: ldr             x16, [x16, #0x528]
    // 0xae113c: cmp             w0, w16
    // 0xae1140: b.eq            #0xae1278
    // 0xae1144: ldur            x1, [fp, #-8]
    // 0xae1148: r0 = controller()
    //     0xae1148: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae114c: LoadField: r1 = r0->field_2b
    //     0xae114c: ldur            w1, [x0, #0x2b]
    // 0xae1150: DecompressPointer r1
    //     0xae1150: add             x1, x1, HEAP, lsl #32
    // 0xae1154: LoadField: r0 = r1->field_13
    //     0xae1154: ldur            w0, [x1, #0x13]
    // 0xae1158: DecompressPointer r0
    //     0xae1158: add             x0, x0, HEAP, lsl #32
    // 0xae115c: r16 = Instance_ZakatTypes
    //     0xae115c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] Obj!ZakatTypes@e307c1
    //     0xae1160: ldr             x16, [x16, #0x70]
    // 0xae1164: cmp             w0, w16
    // 0xae1168: b.eq            #0xae1270
    // 0xae116c: ldur            x0, [fp, #-0x20]
    // 0xae1170: ldur            x1, [fp, #-8]
    // 0xae1174: r0 = controller()
    //     0xae1174: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1178: LoadField: r1 = r0->field_2b
    //     0xae1178: ldur            w1, [x0, #0x2b]
    // 0xae117c: DecompressPointer r1
    //     0xae117c: add             x1, x1, HEAP, lsl #32
    // 0xae1180: LoadField: r0 = r1->field_13
    //     0xae1180: ldur            w0, [x1, #0x13]
    // 0xae1184: DecompressPointer r0
    //     0xae1184: add             x0, x0, HEAP, lsl #32
    // 0xae1188: stur            x0, [fp, #-0x18]
    // 0xae118c: cmp             w0, NULL
    // 0xae1190: b.eq            #0xae1794
    // 0xae1194: ldur            x1, [fp, #-8]
    // 0xae1198: r0 = controller()
    //     0xae1198: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae119c: LoadField: r1 = r0->field_2b
    //     0xae119c: ldur            w1, [x0, #0x2b]
    // 0xae11a0: DecompressPointer r1
    //     0xae11a0: add             x1, x1, HEAP, lsl #32
    // 0xae11a4: LoadField: r0 = r1->field_1b
    //     0xae11a4: ldur            w0, [x1, #0x1b]
    // 0xae11a8: DecompressPointer r0
    //     0xae11a8: add             x0, x0, HEAP, lsl #32
    // 0xae11ac: r1 = LoadClassIdInstr(r0)
    //     0xae11ac: ldur            x1, [x0, #-1]
    //     0xae11b0: ubfx            x1, x1, #0xc, #0x14
    // 0xae11b4: mov             x16, x0
    // 0xae11b8: mov             x0, x1
    // 0xae11bc: mov             x1, x16
    // 0xae11c0: r2 = ", "
    //     0xae11c0: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xae11c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae11c4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae11c8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xae11c8: sub             lr, x0, #0xffc
    //     0xae11cc: ldr             lr, [x21, lr, lsl #3]
    //     0xae11d0: blr             lr
    // 0xae11d4: stur            x0, [fp, #-0x28]
    // 0xae11d8: r0 = NiatZakat()
    //     0xae11d8: bl              #0xae1938  ; AllocateNiatZakatStub -> NiatZakat (size=0x14)
    // 0xae11dc: mov             x2, x0
    // 0xae11e0: ldur            x0, [fp, #-0x18]
    // 0xae11e4: stur            x2, [fp, #-0x30]
    // 0xae11e8: StoreField: r2->field_b = r0
    //     0xae11e8: stur            w0, [x2, #0xb]
    // 0xae11ec: ldur            x0, [fp, #-0x28]
    // 0xae11f0: StoreField: r2->field_f = r0
    //     0xae11f0: stur            w0, [x2, #0xf]
    // 0xae11f4: ldur            x0, [fp, #-0x20]
    // 0xae11f8: LoadField: r1 = r0->field_b
    //     0xae11f8: ldur            w1, [x0, #0xb]
    // 0xae11fc: LoadField: r3 = r0->field_f
    //     0xae11fc: ldur            w3, [x0, #0xf]
    // 0xae1200: DecompressPointer r3
    //     0xae1200: add             x3, x3, HEAP, lsl #32
    // 0xae1204: LoadField: r4 = r3->field_b
    //     0xae1204: ldur            w4, [x3, #0xb]
    // 0xae1208: r3 = LoadInt32Instr(r1)
    //     0xae1208: sbfx            x3, x1, #1, #0x1f
    // 0xae120c: stur            x3, [fp, #-0x40]
    // 0xae1210: r1 = LoadInt32Instr(r4)
    //     0xae1210: sbfx            x1, x4, #1, #0x1f
    // 0xae1214: cmp             x3, x1
    // 0xae1218: b.ne            #0xae1224
    // 0xae121c: mov             x1, x0
    // 0xae1220: r0 = _growToNextCapacity()
    //     0xae1220: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1224: ldur            x2, [fp, #-0x20]
    // 0xae1228: ldur            x3, [fp, #-0x40]
    // 0xae122c: add             x0, x3, #1
    // 0xae1230: lsl             x1, x0, #1
    // 0xae1234: StoreField: r2->field_b = r1
    //     0xae1234: stur            w1, [x2, #0xb]
    // 0xae1238: LoadField: r1 = r2->field_f
    //     0xae1238: ldur            w1, [x2, #0xf]
    // 0xae123c: DecompressPointer r1
    //     0xae123c: add             x1, x1, HEAP, lsl #32
    // 0xae1240: ldur            x0, [fp, #-0x30]
    // 0xae1244: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae1244: add             x25, x1, x3, lsl #2
    //     0xae1248: add             x25, x25, #0xf
    //     0xae124c: str             w0, [x25]
    //     0xae1250: tbz             w0, #0, #0xae126c
    //     0xae1254: ldurb           w16, [x1, #-1]
    //     0xae1258: ldurb           w17, [x0, #-1]
    //     0xae125c: and             x16, x17, x16, lsr #2
    //     0xae1260: tst             x16, HEAP, lsr #32
    //     0xae1264: b.eq            #0xae126c
    //     0xae1268: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae126c: b               #0xae1284
    // 0xae1270: ldur            x2, [fp, #-0x20]
    // 0xae1274: b               #0xae1284
    // 0xae1278: ldur            x2, [fp, #-0x20]
    // 0xae127c: b               #0xae1284
    // 0xae1280: ldur            x2, [fp, #-0x20]
    // 0xae1284: ldur            x1, [fp, #-8]
    // 0xae1288: r0 = controller()
    //     0xae1288: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae128c: LoadField: r1 = r0->field_2b
    //     0xae128c: ldur            w1, [x0, #0x2b]
    // 0xae1290: DecompressPointer r1
    //     0xae1290: add             x1, x1, HEAP, lsl #32
    // 0xae1294: LoadField: r0 = r1->field_f
    //     0xae1294: ldur            w0, [x1, #0xf]
    // 0xae1298: DecompressPointer r0
    //     0xae1298: add             x0, x0, HEAP, lsl #32
    // 0xae129c: r16 = Instance_PaymentType
    //     0xae129c: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xae12a0: ldr             x16, [x16, #0x5f0]
    // 0xae12a4: cmp             w0, w16
    // 0xae12a8: b.ne            #0xae130c
    // 0xae12ac: ldur            x0, [fp, #-0x20]
    // 0xae12b0: LoadField: r1 = r0->field_b
    //     0xae12b0: ldur            w1, [x0, #0xb]
    // 0xae12b4: LoadField: r2 = r0->field_f
    //     0xae12b4: ldur            w2, [x0, #0xf]
    // 0xae12b8: DecompressPointer r2
    //     0xae12b8: add             x2, x2, HEAP, lsl #32
    // 0xae12bc: LoadField: r3 = r2->field_b
    //     0xae12bc: ldur            w3, [x2, #0xb]
    // 0xae12c0: r2 = LoadInt32Instr(r1)
    //     0xae12c0: sbfx            x2, x1, #1, #0x1f
    // 0xae12c4: stur            x2, [fp, #-0x40]
    // 0xae12c8: r1 = LoadInt32Instr(r3)
    //     0xae12c8: sbfx            x1, x3, #1, #0x1f
    // 0xae12cc: cmp             x2, x1
    // 0xae12d0: b.ne            #0xae12dc
    // 0xae12d4: mov             x1, x0
    // 0xae12d8: r0 = _growToNextCapacity()
    //     0xae12d8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae12dc: ldur            x0, [fp, #-0x20]
    // 0xae12e0: ldur            x1, [fp, #-0x40]
    // 0xae12e4: add             x2, x1, #1
    // 0xae12e8: lsl             x3, x2, #1
    // 0xae12ec: StoreField: r0->field_b = r3
    //     0xae12ec: stur            w3, [x0, #0xb]
    // 0xae12f0: LoadField: r2 = r0->field_f
    //     0xae12f0: ldur            w2, [x0, #0xf]
    // 0xae12f4: DecompressPointer r2
    //     0xae12f4: add             x2, x2, HEAP, lsl #32
    // 0xae12f8: add             x3, x2, x1, lsl #2
    // 0xae12fc: r16 = Instance_NiatQurban
    //     0xae12fc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30530] Obj!NiatQurban@e1f9e1
    //     0xae1300: ldr             x16, [x16, #0x530]
    // 0xae1304: StoreField: r3->field_f = r16
    //     0xae1304: stur            w16, [x3, #0xf]
    // 0xae1308: b               #0xae1310
    // 0xae130c: ldur            x0, [fp, #-0x20]
    // 0xae1310: ldur            x1, [fp, #-8]
    // 0xae1314: r0 = controller()
    //     0xae1314: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1318: LoadField: r1 = r0->field_2b
    //     0xae1318: ldur            w1, [x0, #0x2b]
    // 0xae131c: DecompressPointer r1
    //     0xae131c: add             x1, x1, HEAP, lsl #32
    // 0xae1320: LoadField: r0 = r1->field_f
    //     0xae1320: ldur            w0, [x1, #0xf]
    // 0xae1324: DecompressPointer r0
    //     0xae1324: add             x0, x0, HEAP, lsl #32
    // 0xae1328: r16 = Instance_PaymentType
    //     0xae1328: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xae132c: ldr             x16, [x16, #0x638]
    // 0xae1330: cmp             w0, w16
    // 0xae1334: b.ne            #0xae1398
    // 0xae1338: ldur            x0, [fp, #-0x20]
    // 0xae133c: LoadField: r1 = r0->field_b
    //     0xae133c: ldur            w1, [x0, #0xb]
    // 0xae1340: LoadField: r2 = r0->field_f
    //     0xae1340: ldur            w2, [x0, #0xf]
    // 0xae1344: DecompressPointer r2
    //     0xae1344: add             x2, x2, HEAP, lsl #32
    // 0xae1348: LoadField: r3 = r2->field_b
    //     0xae1348: ldur            w3, [x2, #0xb]
    // 0xae134c: r2 = LoadInt32Instr(r1)
    //     0xae134c: sbfx            x2, x1, #1, #0x1f
    // 0xae1350: stur            x2, [fp, #-0x40]
    // 0xae1354: r1 = LoadInt32Instr(r3)
    //     0xae1354: sbfx            x1, x3, #1, #0x1f
    // 0xae1358: cmp             x2, x1
    // 0xae135c: b.ne            #0xae1368
    // 0xae1360: mov             x1, x0
    // 0xae1364: r0 = _growToNextCapacity()
    //     0xae1364: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1368: ldur            x0, [fp, #-0x20]
    // 0xae136c: ldur            x1, [fp, #-0x40]
    // 0xae1370: add             x2, x1, #1
    // 0xae1374: lsl             x3, x2, #1
    // 0xae1378: StoreField: r0->field_b = r3
    //     0xae1378: stur            w3, [x0, #0xb]
    // 0xae137c: LoadField: r2 = r0->field_f
    //     0xae137c: ldur            w2, [x0, #0xf]
    // 0xae1380: DecompressPointer r2
    //     0xae1380: add             x2, x2, HEAP, lsl #32
    // 0xae1384: add             x3, x2, x1, lsl #2
    // 0xae1388: r16 = Instance_NiatFidyah
    //     0xae1388: add             x16, PP, #0x30, lsl #12  ; [pp+0x30538] Obj!NiatFidyah@e1f9f1
    //     0xae138c: ldr             x16, [x16, #0x538]
    // 0xae1390: StoreField: r3->field_f = r16
    //     0xae1390: stur            w16, [x3, #0xf]
    // 0xae1394: b               #0xae139c
    // 0xae1398: ldur            x0, [fp, #-0x20]
    // 0xae139c: LoadField: r1 = r0->field_b
    //     0xae139c: ldur            w1, [x0, #0xb]
    // 0xae13a0: LoadField: r2 = r0->field_f
    //     0xae13a0: ldur            w2, [x0, #0xf]
    // 0xae13a4: DecompressPointer r2
    //     0xae13a4: add             x2, x2, HEAP, lsl #32
    // 0xae13a8: LoadField: r3 = r2->field_b
    //     0xae13a8: ldur            w3, [x2, #0xb]
    // 0xae13ac: r2 = LoadInt32Instr(r1)
    //     0xae13ac: sbfx            x2, x1, #1, #0x1f
    // 0xae13b0: stur            x2, [fp, #-0x40]
    // 0xae13b4: r1 = LoadInt32Instr(r3)
    //     0xae13b4: sbfx            x1, x3, #1, #0x1f
    // 0xae13b8: cmp             x2, x1
    // 0xae13bc: b.ne            #0xae13c8
    // 0xae13c0: mov             x1, x0
    // 0xae13c4: r0 = _growToNextCapacity()
    //     0xae13c4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae13c8: ldur            x0, [fp, #-0x20]
    // 0xae13cc: ldur            x1, [fp, #-0x40]
    // 0xae13d0: add             x2, x1, #1
    // 0xae13d4: lsl             x3, x2, #1
    // 0xae13d8: StoreField: r0->field_b = r3
    //     0xae13d8: stur            w3, [x0, #0xb]
    // 0xae13dc: LoadField: r2 = r0->field_f
    //     0xae13dc: ldur            w2, [x0, #0xf]
    // 0xae13e0: DecompressPointer r2
    //     0xae13e0: add             x2, x2, HEAP, lsl #32
    // 0xae13e4: add             x3, x2, x1, lsl #2
    // 0xae13e8: r16 = Instance_NSectionDivider
    //     0xae13e8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xae13ec: ldr             x16, [x16, #0x38]
    // 0xae13f0: StoreField: r3->field_f = r16
    //     0xae13f0: stur            w16, [x3, #0xf]
    // 0xae13f4: r1 = Null
    //     0xae13f4: mov             x1, NULL
    // 0xae13f8: r2 = 4
    //     0xae13f8: movz            x2, #0x4
    // 0xae13fc: r0 = AllocateArray()
    //     0xae13fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1400: stur            x0, [fp, #-0x18]
    // 0xae1404: r16 = "Cara Pembayaran dengan "
    //     0xae1404: add             x16, PP, #0x30, lsl #12  ; [pp+0x30540] "Cara Pembayaran dengan "
    //     0xae1408: ldr             x16, [x16, #0x540]
    // 0xae140c: StoreField: r0->field_f = r16
    //     0xae140c: stur            w16, [x0, #0xf]
    // 0xae1410: ldur            x1, [fp, #-8]
    // 0xae1414: r0 = controller()
    //     0xae1414: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1418: LoadField: r1 = r0->field_2b
    //     0xae1418: ldur            w1, [x0, #0x2b]
    // 0xae141c: DecompressPointer r1
    //     0xae141c: add             x1, x1, HEAP, lsl #32
    // 0xae1420: LoadField: r0 = r1->field_b
    //     0xae1420: ldur            w0, [x1, #0xb]
    // 0xae1424: DecompressPointer r0
    //     0xae1424: add             x0, x0, HEAP, lsl #32
    // 0xae1428: LoadField: r1 = r0->field_7
    //     0xae1428: ldur            x1, [x0, #7]
    // 0xae142c: cmp             x1, #1
    // 0xae1430: b.gt            #0xae1454
    // 0xae1434: cmp             x1, #0
    // 0xae1438: b.gt            #0xae1448
    // 0xae143c: r0 = "QRIS"
    //     0xae143c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xae1440: ldr             x0, [x0, #0xf50]
    // 0xae1444: b               #0xae1470
    // 0xae1448: r0 = "BNI Virtual Account"
    //     0xae1448: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xae144c: ldr             x0, [x0, #0xf58]
    // 0xae1450: b               #0xae1470
    // 0xae1454: cmp             x1, #2
    // 0xae1458: b.gt            #0xae1468
    // 0xae145c: r0 = "BSI Virtual Account"
    //     0xae145c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xae1460: ldr             x0, [x0, #0xf60]
    // 0xae1464: b               #0xae1470
    // 0xae1468: r0 = "BRI Virtual Account"
    //     0xae1468: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xae146c: ldr             x0, [x0, #0xf68]
    // 0xae1470: ldur            x2, [fp, #-0x20]
    // 0xae1474: ldur            x1, [fp, #-0x18]
    // 0xae1478: ArrayStore: r1[1] = r0  ; List_4
    //     0xae1478: add             x25, x1, #0x13
    //     0xae147c: str             w0, [x25]
    //     0xae1480: tbz             w0, #0, #0xae149c
    //     0xae1484: ldurb           w16, [x1, #-1]
    //     0xae1488: ldurb           w17, [x0, #-1]
    //     0xae148c: and             x16, x17, x16, lsr #2
    //     0xae1490: tst             x16, HEAP, lsr #32
    //     0xae1494: b.eq            #0xae149c
    //     0xae1498: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae149c: ldur            x16, [fp, #-0x18]
    // 0xae14a0: str             x16, [SP]
    // 0xae14a4: r0 = _interpolate()
    //     0xae14a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae14a8: stur            x0, [fp, #-0x18]
    // 0xae14ac: r0 = GetNavigation.textTheme()
    //     0xae14ac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae14b0: LoadField: r1 = r0->field_f
    //     0xae14b0: ldur            w1, [x0, #0xf]
    // 0xae14b4: DecompressPointer r1
    //     0xae14b4: add             x1, x1, HEAP, lsl #32
    // 0xae14b8: cmp             w1, NULL
    // 0xae14bc: b.eq            #0xae1798
    // 0xae14c0: r16 = 16.000000
    //     0xae14c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xae14c4: ldr             x16, [x16, #0x80]
    // 0xae14c8: str             x16, [SP]
    // 0xae14cc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xae14cc: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xae14d0: ldr             x4, [x4, #0x88]
    // 0xae14d4: r0 = copyWith()
    //     0xae14d4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae14d8: ldur            x1, [fp, #-8]
    // 0xae14dc: stur            x0, [fp, #-0x28]
    // 0xae14e0: r0 = controller()
    //     0xae14e0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae14e4: LoadField: r1 = r0->field_2b
    //     0xae14e4: ldur            w1, [x0, #0x2b]
    // 0xae14e8: DecompressPointer r1
    //     0xae14e8: add             x1, x1, HEAP, lsl #32
    // 0xae14ec: LoadField: r0 = r1->field_b
    //     0xae14ec: ldur            w0, [x1, #0xb]
    // 0xae14f0: DecompressPointer r0
    //     0xae14f0: add             x0, x0, HEAP, lsl #32
    // 0xae14f4: mov             x1, x0
    // 0xae14f8: r0 = PaymentMethodExtension.instructions()
    //     0xae14f8: bl              #0xa38bb4  ; [package:nuonline/app/data/enums/payment_enum.dart] ::PaymentMethodExtension.instructions
    // 0xae14fc: stur            x0, [fp, #-0x30]
    // 0xae1500: r0 = PaymentInstruction()
    //     0xae1500: bl              #0xa38ba8  ; AllocatePaymentInstructionStub -> PaymentInstruction (size=0x10)
    // 0xae1504: mov             x3, x0
    // 0xae1508: ldur            x0, [fp, #-0x30]
    // 0xae150c: stur            x3, [fp, #-0x38]
    // 0xae1510: StoreField: r3->field_b = r0
    //     0xae1510: stur            w0, [x3, #0xb]
    // 0xae1514: r1 = Null
    //     0xae1514: mov             x1, NULL
    // 0xae1518: r2 = 2
    //     0xae1518: movz            x2, #0x2
    // 0xae151c: r0 = AllocateArray()
    //     0xae151c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1520: mov             x2, x0
    // 0xae1524: ldur            x0, [fp, #-0x38]
    // 0xae1528: stur            x2, [fp, #-0x30]
    // 0xae152c: StoreField: r2->field_f = r0
    //     0xae152c: stur            w0, [x2, #0xf]
    // 0xae1530: r1 = <Widget>
    //     0xae1530: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae1534: r0 = AllocateGrowableArray()
    //     0xae1534: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae1538: mov             x1, x0
    // 0xae153c: ldur            x0, [fp, #-0x30]
    // 0xae1540: stur            x1, [fp, #-0x38]
    // 0xae1544: StoreField: r1->field_f = r0
    //     0xae1544: stur            w0, [x1, #0xf]
    // 0xae1548: r2 = 2
    //     0xae1548: movz            x2, #0x2
    // 0xae154c: StoreField: r1->field_b = r2
    //     0xae154c: stur            w2, [x1, #0xb]
    // 0xae1550: r0 = NSection()
    //     0xae1550: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae1554: mov             x2, x0
    // 0xae1558: ldur            x0, [fp, #-0x18]
    // 0xae155c: stur            x2, [fp, #-0x30]
    // 0xae1560: StoreField: r2->field_b = r0
    //     0xae1560: stur            w0, [x2, #0xb]
    // 0xae1564: ldur            x0, [fp, #-0x38]
    // 0xae1568: StoreField: r2->field_f = r0
    //     0xae1568: stur            w0, [x2, #0xf]
    // 0xae156c: ldur            x0, [fp, #-0x28]
    // 0xae1570: StoreField: r2->field_1f = r0
    //     0xae1570: stur            w0, [x2, #0x1f]
    // 0xae1574: r0 = true
    //     0xae1574: add             x0, NULL, #0x20  ; true
    // 0xae1578: StoreField: r2->field_27 = r0
    //     0xae1578: stur            w0, [x2, #0x27]
    // 0xae157c: StoreField: r2->field_2b = r0
    //     0xae157c: stur            w0, [x2, #0x2b]
    // 0xae1580: ldur            x3, [fp, #-0x20]
    // 0xae1584: LoadField: r1 = r3->field_b
    //     0xae1584: ldur            w1, [x3, #0xb]
    // 0xae1588: LoadField: r4 = r3->field_f
    //     0xae1588: ldur            w4, [x3, #0xf]
    // 0xae158c: DecompressPointer r4
    //     0xae158c: add             x4, x4, HEAP, lsl #32
    // 0xae1590: LoadField: r5 = r4->field_b
    //     0xae1590: ldur            w5, [x4, #0xb]
    // 0xae1594: r4 = LoadInt32Instr(r1)
    //     0xae1594: sbfx            x4, x1, #1, #0x1f
    // 0xae1598: stur            x4, [fp, #-0x40]
    // 0xae159c: r1 = LoadInt32Instr(r5)
    //     0xae159c: sbfx            x1, x5, #1, #0x1f
    // 0xae15a0: cmp             x4, x1
    // 0xae15a4: b.ne            #0xae15b0
    // 0xae15a8: mov             x1, x3
    // 0xae15ac: r0 = _growToNextCapacity()
    //     0xae15ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae15b0: ldur            x4, [fp, #-0x10]
    // 0xae15b4: ldur            x2, [fp, #-0x20]
    // 0xae15b8: ldur            x3, [fp, #-0x40]
    // 0xae15bc: add             x0, x3, #1
    // 0xae15c0: lsl             x1, x0, #1
    // 0xae15c4: StoreField: r2->field_b = r1
    //     0xae15c4: stur            w1, [x2, #0xb]
    // 0xae15c8: LoadField: r1 = r2->field_f
    //     0xae15c8: ldur            w1, [x2, #0xf]
    // 0xae15cc: DecompressPointer r1
    //     0xae15cc: add             x1, x1, HEAP, lsl #32
    // 0xae15d0: ldur            x0, [fp, #-0x30]
    // 0xae15d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae15d4: add             x25, x1, x3, lsl #2
    //     0xae15d8: add             x25, x25, #0xf
    //     0xae15dc: str             w0, [x25]
    //     0xae15e0: tbz             w0, #0, #0xae15fc
    //     0xae15e4: ldurb           w16, [x1, #-1]
    //     0xae15e8: ldurb           w17, [x0, #-1]
    //     0xae15ec: and             x16, x17, x16, lsr #2
    //     0xae15f0: tst             x16, HEAP, lsr #32
    //     0xae15f4: b.eq            #0xae15fc
    //     0xae15f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae15fc: r0 = ListView()
    //     0xae15fc: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae1600: mov             x1, x0
    // 0xae1604: ldur            x2, [fp, #-0x20]
    // 0xae1608: stur            x0, [fp, #-0x18]
    // 0xae160c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae160c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae1610: r0 = ListView()
    //     0xae1610: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xae1614: ldur            x1, [fp, #-8]
    // 0xae1618: r0 = controller()
    //     0xae1618: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae161c: r1 = Function '<anonymous closure>':.
    //     0xae161c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30548] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xae1620: ldr             x1, [x1, #0x548]
    // 0xae1624: r2 = Null
    //     0xae1624: mov             x2, NULL
    // 0xae1628: stur            x0, [fp, #-0x20]
    // 0xae162c: r0 = AllocateClosure()
    //     0xae162c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1630: r1 = Function '<anonymous closure>':.
    //     0xae1630: add             x1, PP, #0x30, lsl #12  ; [pp+0x30550] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xae1634: ldr             x1, [x1, #0x550]
    // 0xae1638: r2 = Null
    //     0xae1638: mov             x2, NULL
    // 0xae163c: stur            x0, [fp, #-0x28]
    // 0xae1640: r0 = AllocateClosure()
    //     0xae1640: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1644: r16 = <Transaction>
    //     0xae1644: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0xae1648: ldr             x16, [x16, #0x28]
    // 0xae164c: ldur            lr, [fp, #-0x20]
    // 0xae1650: stp             lr, x16, [SP, #0x20]
    // 0xae1654: ldur            x16, [fp, #-0x28]
    // 0xae1658: r30 = Instance_SizedBox
    //     0xae1658: add             lr, PP, #0x30, lsl #12  ; [pp+0x30558] Obj!SizedBox@e1e221
    //     0xae165c: ldr             lr, [lr, #0x558]
    // 0xae1660: stp             lr, x16, [SP, #0x10]
    // 0xae1664: r16 = Instance_SizedBox
    //     0xae1664: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae1668: ldr             x16, [x16, #0xc40]
    // 0xae166c: stp             x0, x16, [SP]
    // 0xae1670: r4 = const [0x1, 0x5, 0x5, 0x2, onEmpty, 0x3, onError, 0x4, onLoading, 0x2, null]
    //     0xae1670: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b3b0] List(11) [0x1, 0x5, 0x5, 0x2, "onEmpty", 0x3, "onError", 0x4, "onLoading", 0x2, Null]
    //     0xae1674: ldr             x4, [x4, #0x3b0]
    // 0xae1678: r0 = StateExt.obx()
    //     0xae1678: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xae167c: ldur            x1, [fp, #-8]
    // 0xae1680: stur            x0, [fp, #-8]
    // 0xae1684: r0 = controller()
    //     0xae1684: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1688: mov             x2, x0
    // 0xae168c: r1 = Function 'pay':.
    //     0xae168c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30560] AnonymousClosure: (0xae1af4), in [package:nuonline/app/modules/donation/controllers/confirm_donation_controller.dart] ConfirmDonationController::pay (0xae1b2c)
    //     0xae1690: ldr             x1, [x1, #0x560]
    // 0xae1694: r0 = AllocateClosure()
    //     0xae1694: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1698: stur            x0, [fp, #-0x20]
    // 0xae169c: r0 = _TextButtonWithIcon()
    //     0xae169c: bl              #0xae192c  ; Allocate_TextButtonWithIconStub -> _TextButtonWithIcon (size=0x3c)
    // 0xae16a0: mov             x1, x0
    // 0xae16a4: ldur            x2, [fp, #-8]
    // 0xae16a8: ldur            x5, [fp, #-0x20]
    // 0xae16ac: r3 = Instance_Text
    //     0xae16ac: add             x3, PP, #0x30, lsl #12  ; [pp+0x30568] Obj!Text@e21a51
    //     0xae16b0: ldr             x3, [x3, #0x568]
    // 0xae16b4: r6 = Null
    //     0xae16b4: mov             x6, NULL
    // 0xae16b8: stur            x0, [fp, #-8]
    // 0xae16bc: r0 = _TextButtonWithIcon()
    //     0xae16bc: bl              #0xae1838  ; [package:flutter/src/material/text_button.dart] _TextButtonWithIcon::_TextButtonWithIcon
    // 0xae16c0: r0 = Container()
    //     0xae16c0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae16c4: stur            x0, [fp, #-0x20]
    // 0xae16c8: r16 = Instance_EdgeInsets
    //     0xae16c8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28050] Obj!EdgeInsets@e12341
    //     0xae16cc: ldr             x16, [x16, #0x50]
    // 0xae16d0: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xae16d0: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xae16d4: ldr             lr, [lr, #0xc58]
    // 0xae16d8: stp             lr, x16, [SP, #8]
    // 0xae16dc: ldur            x16, [fp, #-8]
    // 0xae16e0: str             x16, [SP]
    // 0xae16e4: mov             x1, x0
    // 0xae16e8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0xae16e8: add             x4, PP, #0x28, lsl #12  ; [pp+0x28058] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xae16ec: ldr             x4, [x4, #0x58]
    // 0xae16f0: r0 = Container()
    //     0xae16f0: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae16f4: r1 = Null
    //     0xae16f4: mov             x1, NULL
    // 0xae16f8: r2 = 2
    //     0xae16f8: movz            x2, #0x2
    // 0xae16fc: r0 = AllocateArray()
    //     0xae16fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1700: mov             x2, x0
    // 0xae1704: ldur            x0, [fp, #-0x20]
    // 0xae1708: stur            x2, [fp, #-8]
    // 0xae170c: StoreField: r2->field_f = r0
    //     0xae170c: stur            w0, [x2, #0xf]
    // 0xae1710: r1 = <Widget>
    //     0xae1710: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae1714: r0 = AllocateGrowableArray()
    //     0xae1714: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae1718: mov             x1, x0
    // 0xae171c: ldur            x0, [fp, #-8]
    // 0xae1720: stur            x1, [fp, #-0x20]
    // 0xae1724: StoreField: r1->field_f = r0
    //     0xae1724: stur            w0, [x1, #0xf]
    // 0xae1728: r0 = 2
    //     0xae1728: movz            x0, #0x2
    // 0xae172c: StoreField: r1->field_b = r0
    //     0xae172c: stur            w0, [x1, #0xb]
    // 0xae1730: r0 = Scaffold()
    //     0xae1730: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae1734: ldur            x1, [fp, #-0x10]
    // 0xae1738: StoreField: r0->field_13 = r1
    //     0xae1738: stur            w1, [x0, #0x13]
    // 0xae173c: ldur            x1, [fp, #-0x18]
    // 0xae1740: ArrayStore: r0[0] = r1  ; List_4
    //     0xae1740: stur            w1, [x0, #0x17]
    // 0xae1744: ldur            x1, [fp, #-0x20]
    // 0xae1748: StoreField: r0->field_27 = r1
    //     0xae1748: stur            w1, [x0, #0x27]
    // 0xae174c: r1 = Instance_AlignmentDirectional
    //     0xae174c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae1750: ldr             x1, [x1, #0x758]
    // 0xae1754: StoreField: r0->field_2b = r1
    //     0xae1754: stur            w1, [x0, #0x2b]
    // 0xae1758: r1 = true
    //     0xae1758: add             x1, NULL, #0x20  ; true
    // 0xae175c: StoreField: r0->field_53 = r1
    //     0xae175c: stur            w1, [x0, #0x53]
    // 0xae1760: r2 = Instance_DragStartBehavior
    //     0xae1760: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae1764: StoreField: r0->field_57 = r2
    //     0xae1764: stur            w2, [x0, #0x57]
    // 0xae1768: r2 = false
    //     0xae1768: add             x2, NULL, #0x30  ; false
    // 0xae176c: StoreField: r0->field_b = r2
    //     0xae176c: stur            w2, [x0, #0xb]
    // 0xae1770: StoreField: r0->field_f = r2
    //     0xae1770: stur            w2, [x0, #0xf]
    // 0xae1774: StoreField: r0->field_5f = r1
    //     0xae1774: stur            w1, [x0, #0x5f]
    // 0xae1778: StoreField: r0->field_63 = r1
    //     0xae1778: stur            w1, [x0, #0x63]
    // 0xae177c: LeaveFrame
    //     0xae177c: mov             SP, fp
    //     0xae1780: ldp             fp, lr, [SP], #0x10
    // 0xae1784: ret
    //     0xae1784: ret             
    // 0xae1788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae178c: b               #0xae09dc
    // 0xae1790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1794: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1798: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
