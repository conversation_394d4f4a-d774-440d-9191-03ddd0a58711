// lib: , url: package:nuonline/app/modules/donation/views/donation_news_view.dart

// class id: 1050221, size: 0x8
class :: {
}

// class id: 5294, size: 0x14, field offset: 0x14
//   const constructor, 
class DonationNewsView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae1b80, size: 0x3c0
    // 0xae1b80: EnterFrame
    //     0xae1b80: stp             fp, lr, [SP, #-0x10]!
    //     0xae1b84: mov             fp, SP
    // 0xae1b88: AllocStack(0x50)
    //     0xae1b88: sub             SP, SP, #0x50
    // 0xae1b8c: SetupParameters(DonationNewsView this /* r1 => r1, fp-0x8 */)
    //     0xae1b8c: stur            x1, [fp, #-8]
    // 0xae1b90: CheckStackOverflow
    //     0xae1b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1b94: cmp             SP, x16
    //     0xae1b98: b.ls            #0xae1f2c
    // 0xae1b9c: r1 = 1
    //     0xae1b9c: movz            x1, #0x1
    // 0xae1ba0: r0 = AllocateContext()
    //     0xae1ba0: bl              #0xec126c  ; AllocateContextStub
    // 0xae1ba4: ldur            x1, [fp, #-8]
    // 0xae1ba8: stur            x0, [fp, #-0x10]
    // 0xae1bac: StoreField: r0->field_f = r1
    //     0xae1bac: stur            w1, [x0, #0xf]
    // 0xae1bb0: r0 = AppBar()
    //     0xae1bb0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae1bb4: stur            x0, [fp, #-0x18]
    // 0xae1bb8: r16 = Instance_Text
    //     0xae1bb8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30470] Obj!Text@e21aa1
    //     0xae1bbc: ldr             x16, [x16, #0x470]
    // 0xae1bc0: str             x16, [SP]
    // 0xae1bc4: mov             x1, x0
    // 0xae1bc8: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae1bc8: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae1bcc: ldr             x4, [x4, #0x6e8]
    // 0xae1bd0: r0 = AppBar()
    //     0xae1bd0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae1bd4: ldur            x1, [fp, #-8]
    // 0xae1bd8: r0 = controller()
    //     0xae1bd8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1bdc: LoadField: r1 = r0->field_2b
    //     0xae1bdc: ldur            w1, [x0, #0x2b]
    // 0xae1be0: DecompressPointer r1
    //     0xae1be0: add             x1, x1, HEAP, lsl #32
    // 0xae1be4: r16 = Sentinel
    //     0xae1be4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae1be8: cmp             w1, w16
    // 0xae1bec: b.eq            #0xae1f34
    // 0xae1bf0: stur            x1, [fp, #-0x20]
    // 0xae1bf4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae1bf4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae1bf8: ldr             x0, [x0, #0x2670]
    //     0xae1bfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae1c00: cmp             w0, w16
    //     0xae1c04: b.ne            #0xae1c10
    //     0xae1c08: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae1c0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae1c10: r16 = <DonationRepository>
    //     0xae1c10: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xae1c14: ldr             x16, [x16, #0xb0]
    // 0xae1c18: r30 = "donation_repo"
    //     0xae1c18: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xae1c1c: ldr             lr, [lr, #0xb8]
    // 0xae1c20: stp             lr, x16, [SP]
    // 0xae1c24: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae1c24: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae1c28: r0 = Inst.find()
    //     0xae1c28: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae1c2c: ldur            x1, [fp, #-8]
    // 0xae1c30: stur            x0, [fp, #-0x28]
    // 0xae1c34: r0 = controller()
    //     0xae1c34: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1c38: LoadField: r2 = r0->field_27
    //     0xae1c38: ldur            w2, [x0, #0x27]
    // 0xae1c3c: DecompressPointer r2
    //     0xae1c3c: add             x2, x2, HEAP, lsl #32
    // 0xae1c40: ldur            x1, [fp, #-8]
    // 0xae1c44: stur            x2, [fp, #-0x30]
    // 0xae1c48: r0 = controller()
    //     0xae1c48: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1c4c: LoadField: r1 = r0->field_23
    //     0xae1c4c: ldur            w1, [x0, #0x23]
    // 0xae1c50: DecompressPointer r1
    //     0xae1c50: add             x1, x1, HEAP, lsl #32
    // 0xae1c54: LoadField: r0 = r1->field_7
    //     0xae1c54: ldur            w0, [x1, #7]
    // 0xae1c58: DecompressPointer r0
    //     0xae1c58: add             x0, x0, HEAP, lsl #32
    // 0xae1c5c: stur            x0, [fp, #-0x38]
    // 0xae1c60: r1 = <List<DonationNews>>
    //     0xae1c60: add             x1, PP, #0x30, lsl #12  ; [pp+0x30478] TypeArguments: <List<DonationNews>>
    //     0xae1c64: ldr             x1, [x1, #0x478]
    // 0xae1c68: r0 = DonationNewsListBuilderController()
    //     0xae1c68: bl              #0xae2094  ; AllocateDonationNewsListBuilderControllerStub -> DonationNewsListBuilderController (size=0x4c)
    // 0xae1c6c: mov             x2, x0
    // 0xae1c70: r0 = "report"
    //     0xae1c70: add             x0, PP, #0x30, lsl #12  ; [pp+0x30480] "report"
    //     0xae1c74: ldr             x0, [x0, #0x480]
    // 0xae1c78: stur            x2, [fp, #-0x40]
    // 0xae1c7c: StoreField: r2->field_3b = r0
    //     0xae1c7c: stur            w0, [x2, #0x3b]
    // 0xae1c80: ldur            x0, [fp, #-0x30]
    // 0xae1c84: StoreField: r2->field_3f = r0
    //     0xae1c84: stur            w0, [x2, #0x3f]
    // 0xae1c88: ldur            x0, [fp, #-0x28]
    // 0xae1c8c: StoreField: r2->field_47 = r0
    //     0xae1c8c: stur            w0, [x2, #0x47]
    // 0xae1c90: ldur            x0, [fp, #-0x38]
    // 0xae1c94: StoreField: r2->field_43 = r0
    //     0xae1c94: stur            w0, [x2, #0x43]
    // 0xae1c98: mov             x1, x2
    // 0xae1c9c: r0 = _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin()
    //     0xae1c9c: bl              #0xae1f4c  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin
    // 0xae1ca0: r1 = <DonationNewsListBuilderController>
    //     0xae1ca0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30488] TypeArguments: <DonationNewsListBuilderController>
    //     0xae1ca4: ldr             x1, [x1, #0x488]
    // 0xae1ca8: r0 = GetBuilder()
    //     0xae1ca8: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae1cac: mov             x3, x0
    // 0xae1cb0: ldur            x0, [fp, #-0x40]
    // 0xae1cb4: stur            x3, [fp, #-0x28]
    // 0xae1cb8: StoreField: r3->field_3b = r0
    //     0xae1cb8: stur            w0, [x3, #0x3b]
    // 0xae1cbc: r0 = true
    //     0xae1cbc: add             x0, NULL, #0x20  ; true
    // 0xae1cc0: StoreField: r3->field_13 = r0
    //     0xae1cc0: stur            w0, [x3, #0x13]
    // 0xae1cc4: r1 = Function '<anonymous closure>':.
    //     0xae1cc4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30490] AnonymousClosure: (0xae2358), in [package:nuonline/app/modules/donation/views/donation_news_view.dart] DonationNewsView::build (0xae1b80)
    //     0xae1cc8: ldr             x1, [x1, #0x490]
    // 0xae1ccc: r2 = Null
    //     0xae1ccc: mov             x2, NULL
    // 0xae1cd0: r0 = AllocateClosure()
    //     0xae1cd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1cd4: mov             x1, x0
    // 0xae1cd8: ldur            x0, [fp, #-0x28]
    // 0xae1cdc: StoreField: r0->field_f = r1
    //     0xae1cdc: stur            w1, [x0, #0xf]
    // 0xae1ce0: r1 = false
    //     0xae1ce0: add             x1, NULL, #0x30  ; false
    // 0xae1ce4: StoreField: r0->field_1f = r1
    //     0xae1ce4: stur            w1, [x0, #0x1f]
    // 0xae1ce8: StoreField: r0->field_23 = r1
    //     0xae1ce8: stur            w1, [x0, #0x23]
    // 0xae1cec: r2 = "donation-report"
    //     0xae1cec: add             x2, PP, #0x30, lsl #12  ; [pp+0x30498] "donation-report"
    //     0xae1cf0: ldr             x2, [x2, #0x498]
    // 0xae1cf4: StoreField: r0->field_1b = r2
    //     0xae1cf4: stur            w2, [x0, #0x1b]
    // 0xae1cf8: r2 = Instance_ValueKey
    //     0xae1cf8: add             x2, PP, #0x30, lsl #12  ; [pp+0x304a0] Obj!ValueKey<String>@e14bc1
    //     0xae1cfc: ldr             x2, [x2, #0x4a0]
    // 0xae1d00: StoreField: r0->field_7 = r2
    //     0xae1d00: stur            w2, [x0, #7]
    // 0xae1d04: r16 = <DonationRepository>
    //     0xae1d04: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xae1d08: ldr             x16, [x16, #0xb0]
    // 0xae1d0c: r30 = "donation_repo"
    //     0xae1d0c: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xae1d10: ldr             lr, [lr, #0xb8]
    // 0xae1d14: stp             lr, x16, [SP]
    // 0xae1d18: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae1d18: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae1d1c: r0 = Inst.find()
    //     0xae1d1c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae1d20: ldur            x1, [fp, #-8]
    // 0xae1d24: stur            x0, [fp, #-0x30]
    // 0xae1d28: r0 = controller()
    //     0xae1d28: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1d2c: LoadField: r2 = r0->field_27
    //     0xae1d2c: ldur            w2, [x0, #0x27]
    // 0xae1d30: DecompressPointer r2
    //     0xae1d30: add             x2, x2, HEAP, lsl #32
    // 0xae1d34: ldur            x1, [fp, #-8]
    // 0xae1d38: stur            x2, [fp, #-0x38]
    // 0xae1d3c: r0 = controller()
    //     0xae1d3c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae1d40: LoadField: r1 = r0->field_23
    //     0xae1d40: ldur            w1, [x0, #0x23]
    // 0xae1d44: DecompressPointer r1
    //     0xae1d44: add             x1, x1, HEAP, lsl #32
    // 0xae1d48: LoadField: r0 = r1->field_7
    //     0xae1d48: ldur            w0, [x1, #7]
    // 0xae1d4c: DecompressPointer r0
    //     0xae1d4c: add             x0, x0, HEAP, lsl #32
    // 0xae1d50: stur            x0, [fp, #-8]
    // 0xae1d54: r1 = <List<DonationNews>>
    //     0xae1d54: add             x1, PP, #0x30, lsl #12  ; [pp+0x30478] TypeArguments: <List<DonationNews>>
    //     0xae1d58: ldr             x1, [x1, #0x478]
    // 0xae1d5c: r0 = DonationNewsListBuilderController()
    //     0xae1d5c: bl              #0xae2094  ; AllocateDonationNewsListBuilderControllerStub -> DonationNewsListBuilderController (size=0x4c)
    // 0xae1d60: mov             x2, x0
    // 0xae1d64: r0 = "general"
    //     0xae1d64: add             x0, PP, #0x30, lsl #12  ; [pp+0x304a8] "general"
    //     0xae1d68: ldr             x0, [x0, #0x4a8]
    // 0xae1d6c: stur            x2, [fp, #-0x40]
    // 0xae1d70: StoreField: r2->field_3b = r0
    //     0xae1d70: stur            w0, [x2, #0x3b]
    // 0xae1d74: ldur            x0, [fp, #-0x38]
    // 0xae1d78: StoreField: r2->field_3f = r0
    //     0xae1d78: stur            w0, [x2, #0x3f]
    // 0xae1d7c: ldur            x0, [fp, #-0x30]
    // 0xae1d80: StoreField: r2->field_47 = r0
    //     0xae1d80: stur            w0, [x2, #0x47]
    // 0xae1d84: ldur            x0, [fp, #-8]
    // 0xae1d88: StoreField: r2->field_43 = r0
    //     0xae1d88: stur            w0, [x2, #0x43]
    // 0xae1d8c: mov             x1, x2
    // 0xae1d90: r0 = _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin()
    //     0xae1d90: bl              #0xae1f4c  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::_DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin
    // 0xae1d94: r1 = <DonationNewsListBuilderController>
    //     0xae1d94: add             x1, PP, #0x30, lsl #12  ; [pp+0x30488] TypeArguments: <DonationNewsListBuilderController>
    //     0xae1d98: ldr             x1, [x1, #0x488]
    // 0xae1d9c: r0 = GetBuilder()
    //     0xae1d9c: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae1da0: mov             x3, x0
    // 0xae1da4: ldur            x0, [fp, #-0x40]
    // 0xae1da8: stur            x3, [fp, #-8]
    // 0xae1dac: StoreField: r3->field_3b = r0
    //     0xae1dac: stur            w0, [x3, #0x3b]
    // 0xae1db0: r0 = true
    //     0xae1db0: add             x0, NULL, #0x20  ; true
    // 0xae1db4: StoreField: r3->field_13 = r0
    //     0xae1db4: stur            w0, [x3, #0x13]
    // 0xae1db8: r1 = Function '<anonymous closure>':.
    //     0xae1db8: add             x1, PP, #0x30, lsl #12  ; [pp+0x304b0] AnonymousClosure: (0xae234c), in [package:nuonline/app/modules/donation/views/donation_news_view.dart] DonationNewsView::build (0xae1b80)
    //     0xae1dbc: ldr             x1, [x1, #0x4b0]
    // 0xae1dc0: r2 = Null
    //     0xae1dc0: mov             x2, NULL
    // 0xae1dc4: r0 = AllocateClosure()
    //     0xae1dc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1dc8: mov             x1, x0
    // 0xae1dcc: ldur            x0, [fp, #-8]
    // 0xae1dd0: StoreField: r0->field_f = r1
    //     0xae1dd0: stur            w1, [x0, #0xf]
    // 0xae1dd4: r3 = false
    //     0xae1dd4: add             x3, NULL, #0x30  ; false
    // 0xae1dd8: StoreField: r0->field_1f = r3
    //     0xae1dd8: stur            w3, [x0, #0x1f]
    // 0xae1ddc: StoreField: r0->field_23 = r3
    //     0xae1ddc: stur            w3, [x0, #0x23]
    // 0xae1de0: r1 = "donation-general"
    //     0xae1de0: add             x1, PP, #0x30, lsl #12  ; [pp+0x304b8] "donation-general"
    //     0xae1de4: ldr             x1, [x1, #0x4b8]
    // 0xae1de8: StoreField: r0->field_1b = r1
    //     0xae1de8: stur            w1, [x0, #0x1b]
    // 0xae1dec: r1 = Instance_ValueKey
    //     0xae1dec: add             x1, PP, #0x30, lsl #12  ; [pp+0x304c0] Obj!ValueKey<String>@e14bb1
    //     0xae1df0: ldr             x1, [x1, #0x4c0]
    // 0xae1df4: StoreField: r0->field_7 = r1
    //     0xae1df4: stur            w1, [x0, #7]
    // 0xae1df8: r1 = Null
    //     0xae1df8: mov             x1, NULL
    // 0xae1dfc: r2 = 4
    //     0xae1dfc: movz            x2, #0x4
    // 0xae1e00: r0 = AllocateArray()
    //     0xae1e00: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae1e04: mov             x2, x0
    // 0xae1e08: ldur            x0, [fp, #-0x28]
    // 0xae1e0c: stur            x2, [fp, #-0x30]
    // 0xae1e10: StoreField: r2->field_f = r0
    //     0xae1e10: stur            w0, [x2, #0xf]
    // 0xae1e14: ldur            x0, [fp, #-8]
    // 0xae1e18: StoreField: r2->field_13 = r0
    //     0xae1e18: stur            w0, [x2, #0x13]
    // 0xae1e1c: r1 = <Widget>
    //     0xae1e1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae1e20: r0 = AllocateGrowableArray()
    //     0xae1e20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae1e24: mov             x1, x0
    // 0xae1e28: ldur            x0, [fp, #-0x30]
    // 0xae1e2c: stur            x1, [fp, #-8]
    // 0xae1e30: StoreField: r1->field_f = r0
    //     0xae1e30: stur            w0, [x1, #0xf]
    // 0xae1e34: r0 = 4
    //     0xae1e34: movz            x0, #0x4
    // 0xae1e38: StoreField: r1->field_b = r0
    //     0xae1e38: stur            w0, [x1, #0xb]
    // 0xae1e3c: r0 = TabBarView()
    //     0xae1e3c: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xae1e40: mov             x1, x0
    // 0xae1e44: ldur            x0, [fp, #-8]
    // 0xae1e48: stur            x1, [fp, #-0x28]
    // 0xae1e4c: StoreField: r1->field_f = r0
    //     0xae1e4c: stur            w0, [x1, #0xf]
    // 0xae1e50: ldur            x0, [fp, #-0x20]
    // 0xae1e54: StoreField: r1->field_b = r0
    //     0xae1e54: stur            w0, [x1, #0xb]
    // 0xae1e58: r0 = Instance_DragStartBehavior
    //     0xae1e58: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae1e5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae1e5c: stur            w0, [x1, #0x17]
    // 0xae1e60: d0 = 1.000000
    //     0xae1e60: fmov            d0, #1.00000000
    // 0xae1e64: StoreField: r1->field_1b = d0
    //     0xae1e64: stur            d0, [x1, #0x1b]
    // 0xae1e68: r2 = Instance_Clip
    //     0xae1e68: add             x2, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xae1e6c: ldr             x2, [x2, #0x7c0]
    // 0xae1e70: StoreField: r1->field_23 = r2
    //     0xae1e70: stur            w2, [x1, #0x23]
    // 0xae1e74: r0 = NestedScrollView()
    //     0xae1e74: bl              #0xae1f40  ; AllocateNestedScrollViewStub -> NestedScrollView (size=0x3c)
    // 0xae1e78: mov             x3, x0
    // 0xae1e7c: r0 = Instance_Axis
    //     0xae1e7c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae1e80: stur            x3, [fp, #-8]
    // 0xae1e84: StoreField: r3->field_f = r0
    //     0xae1e84: stur            w0, [x3, #0xf]
    // 0xae1e88: r0 = false
    //     0xae1e88: add             x0, NULL, #0x30  ; false
    // 0xae1e8c: StoreField: r3->field_13 = r0
    //     0xae1e8c: stur            w0, [x3, #0x13]
    // 0xae1e90: ldur            x2, [fp, #-0x10]
    // 0xae1e94: r1 = Function '<anonymous closure>':.
    //     0xae1e94: add             x1, PP, #0x30, lsl #12  ; [pp+0x304c8] AnonymousClosure: (0xae20a0), in [package:nuonline/app/modules/donation/views/donation_news_view.dart] DonationNewsView::build (0xae1b80)
    //     0xae1e98: ldr             x1, [x1, #0x4c8]
    // 0xae1e9c: r0 = AllocateClosure()
    //     0xae1e9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae1ea0: mov             x1, x0
    // 0xae1ea4: ldur            x0, [fp, #-8]
    // 0xae1ea8: StoreField: r0->field_1b = r1
    //     0xae1ea8: stur            w1, [x0, #0x1b]
    // 0xae1eac: ldur            x1, [fp, #-0x28]
    // 0xae1eb0: StoreField: r0->field_1f = r1
    //     0xae1eb0: stur            w1, [x0, #0x1f]
    // 0xae1eb4: r1 = Instance_DragStartBehavior
    //     0xae1eb4: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae1eb8: StoreField: r0->field_23 = r1
    //     0xae1eb8: stur            w1, [x0, #0x23]
    // 0xae1ebc: r2 = false
    //     0xae1ebc: add             x2, NULL, #0x30  ; false
    // 0xae1ec0: StoreField: r0->field_27 = r2
    //     0xae1ec0: stur            w2, [x0, #0x27]
    // 0xae1ec4: r3 = Instance_Clip
    //     0xae1ec4: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xae1ec8: ldr             x3, [x3, #0x7c0]
    // 0xae1ecc: StoreField: r0->field_2b = r3
    //     0xae1ecc: stur            w3, [x0, #0x2b]
    // 0xae1ed0: r3 = Instance_HitTestBehavior
    //     0xae1ed0: add             x3, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xae1ed4: ldr             x3, [x3, #0x1c8]
    // 0xae1ed8: StoreField: r0->field_2f = r3
    //     0xae1ed8: stur            w3, [x0, #0x2f]
    // 0xae1edc: r0 = Scaffold()
    //     0xae1edc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae1ee0: ldur            x1, [fp, #-0x18]
    // 0xae1ee4: StoreField: r0->field_13 = r1
    //     0xae1ee4: stur            w1, [x0, #0x13]
    // 0xae1ee8: ldur            x1, [fp, #-8]
    // 0xae1eec: ArrayStore: r0[0] = r1  ; List_4
    //     0xae1eec: stur            w1, [x0, #0x17]
    // 0xae1ef0: r1 = Instance_AlignmentDirectional
    //     0xae1ef0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae1ef4: ldr             x1, [x1, #0x758]
    // 0xae1ef8: StoreField: r0->field_2b = r1
    //     0xae1ef8: stur            w1, [x0, #0x2b]
    // 0xae1efc: r1 = true
    //     0xae1efc: add             x1, NULL, #0x20  ; true
    // 0xae1f00: StoreField: r0->field_53 = r1
    //     0xae1f00: stur            w1, [x0, #0x53]
    // 0xae1f04: r2 = Instance_DragStartBehavior
    //     0xae1f04: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae1f08: StoreField: r0->field_57 = r2
    //     0xae1f08: stur            w2, [x0, #0x57]
    // 0xae1f0c: r2 = false
    //     0xae1f0c: add             x2, NULL, #0x30  ; false
    // 0xae1f10: StoreField: r0->field_b = r2
    //     0xae1f10: stur            w2, [x0, #0xb]
    // 0xae1f14: StoreField: r0->field_f = r2
    //     0xae1f14: stur            w2, [x0, #0xf]
    // 0xae1f18: StoreField: r0->field_5f = r1
    //     0xae1f18: stur            w1, [x0, #0x5f]
    // 0xae1f1c: StoreField: r0->field_63 = r1
    //     0xae1f1c: stur            w1, [x0, #0x63]
    // 0xae1f20: LeaveFrame
    //     0xae1f20: mov             SP, fp
    //     0xae1f24: ldp             fp, lr, [SP], #0x10
    // 0xae1f28: ret
    //     0xae1f28: ret             
    // 0xae1f2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1f2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1f30: b               #0xae1b9c
    // 0xae1f34: r9 = tabController
    //     0xae1f34: add             x9, PP, #0x30, lsl #12  ; [pp+0x304d0] Field <DonationNewsController.tabController>: late (offset: 0x2c)
    //     0xae1f38: ldr             x9, [x9, #0x4d0]
    // 0xae1f3c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae1f3c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] List<Widget> <anonymous closure>(dynamic, BuildContext, bool) {
    // ** addr: 0xae20a0, size: 0x288
    // 0xae20a0: EnterFrame
    //     0xae20a0: stp             fp, lr, [SP, #-0x10]!
    //     0xae20a4: mov             fp, SP
    // 0xae20a8: AllocStack(0x28)
    //     0xae20a8: sub             SP, SP, #0x28
    // 0xae20ac: SetupParameters()
    //     0xae20ac: ldr             x0, [fp, #0x20]
    //     0xae20b0: ldur            w2, [x0, #0x17]
    //     0xae20b4: add             x2, x2, HEAP, lsl #32
    //     0xae20b8: stur            x2, [fp, #-8]
    // 0xae20bc: CheckStackOverflow
    //     0xae20bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae20c0: cmp             SP, x16
    //     0xae20c4: b.ls            #0xae2314
    // 0xae20c8: LoadField: r1 = r2->field_f
    //     0xae20c8: ldur            w1, [x2, #0xf]
    // 0xae20cc: DecompressPointer r1
    //     0xae20cc: add             x1, x1, HEAP, lsl #32
    // 0xae20d0: r0 = controller()
    //     0xae20d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae20d4: LoadField: r2 = r0->field_23
    //     0xae20d4: ldur            w2, [x0, #0x23]
    // 0xae20d8: DecompressPointer r2
    //     0xae20d8: add             x2, x2, HEAP, lsl #32
    // 0xae20dc: ldur            x0, [fp, #-8]
    // 0xae20e0: stur            x2, [fp, #-0x10]
    // 0xae20e4: LoadField: r1 = r0->field_f
    //     0xae20e4: ldur            w1, [x0, #0xf]
    // 0xae20e8: DecompressPointer r1
    //     0xae20e8: add             x1, x1, HEAP, lsl #32
    // 0xae20ec: r0 = controller()
    //     0xae20ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae20f0: LoadField: r1 = r0->field_27
    //     0xae20f0: ldur            w1, [x0, #0x27]
    // 0xae20f4: DecompressPointer r1
    //     0xae20f4: add             x1, x1, HEAP, lsl #32
    // 0xae20f8: stur            x1, [fp, #-0x18]
    // 0xae20fc: r0 = MerchantInfo()
    //     0xae20fc: bl              #0xae2340  ; AllocateMerchantInfoStub -> MerchantInfo (size=0x1c)
    // 0xae2100: mov             x1, x0
    // 0xae2104: ldur            x0, [fp, #-0x10]
    // 0xae2108: stur            x1, [fp, #-0x20]
    // 0xae210c: StoreField: r1->field_b = r0
    //     0xae210c: stur            w0, [x1, #0xb]
    // 0xae2110: ldur            x0, [fp, #-0x18]
    // 0xae2114: StoreField: r1->field_f = r0
    //     0xae2114: stur            w0, [x1, #0xf]
    // 0xae2118: r0 = false
    //     0xae2118: add             x0, NULL, #0x30  ; false
    // 0xae211c: StoreField: r1->field_13 = r0
    //     0xae211c: stur            w0, [x1, #0x13]
    // 0xae2120: ArrayStore: r1[0] = r0  ; List_4
    //     0xae2120: stur            w0, [x1, #0x17]
    // 0xae2124: r0 = SliverToBoxAdapter()
    //     0xae2124: bl              #0xae2334  ; AllocateSliverToBoxAdapterStub -> SliverToBoxAdapter (size=0x10)
    // 0xae2128: mov             x1, x0
    // 0xae212c: ldur            x0, [fp, #-0x20]
    // 0xae2130: stur            x1, [fp, #-0x10]
    // 0xae2134: StoreField: r1->field_b = r0
    //     0xae2134: stur            w0, [x1, #0xb]
    // 0xae2138: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae2138: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae213c: ldr             x0, [x0, #0x2670]
    //     0xae2140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae2144: cmp             w0, w16
    //     0xae2148: b.ne            #0xae2154
    //     0xae214c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae2150: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae2154: r0 = GetNavigation.theme()
    //     0xae2154: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xae2158: LoadField: r1 = r0->field_6f
    //     0xae2158: ldur            w1, [x0, #0x6f]
    // 0xae215c: DecompressPointer r1
    //     0xae215c: add             x1, x1, HEAP, lsl #32
    // 0xae2160: stur            x1, [fp, #-0x18]
    // 0xae2164: r0 = GetNavigation.theme()
    //     0xae2164: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xae2168: LoadField: r1 = r0->field_77
    //     0xae2168: ldur            w1, [x0, #0x77]
    // 0xae216c: DecompressPointer r1
    //     0xae216c: add             x1, x1, HEAP, lsl #32
    // 0xae2170: stur            x1, [fp, #-0x20]
    // 0xae2174: r0 = BoxShadow()
    //     0xae2174: bl              #0x794360  ; AllocateBoxShadowStub -> BoxShadow (size=0x24)
    // 0xae2178: stur            x0, [fp, #-0x28]
    // 0xae217c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xae217c: stur            xzr, [x0, #0x17]
    // 0xae2180: r1 = Instance_BlurStyle
    //     0xae2180: add             x1, PP, #0x29, lsl #12  ; [pp+0x29010] Obj!BlurStyle@e39a01
    //     0xae2184: ldr             x1, [x1, #0x10]
    // 0xae2188: StoreField: r0->field_1f = r1
    //     0xae2188: stur            w1, [x0, #0x1f]
    // 0xae218c: ldur            x1, [fp, #-0x20]
    // 0xae2190: StoreField: r0->field_7 = r1
    //     0xae2190: stur            w1, [x0, #7]
    // 0xae2194: r1 = Instance_Offset
    //     0xae2194: add             x1, PP, #0x29, lsl #12  ; [pp+0x29ef0] Obj!Offset@e2c8a1
    //     0xae2198: ldr             x1, [x1, #0xef0]
    // 0xae219c: StoreField: r0->field_b = r1
    //     0xae219c: stur            w1, [x0, #0xb]
    // 0xae21a0: d0 = 8.000000
    //     0xae21a0: fmov            d0, #8.00000000
    // 0xae21a4: StoreField: r0->field_f = d0
    //     0xae21a4: stur            d0, [x0, #0xf]
    // 0xae21a8: r1 = Null
    //     0xae21a8: mov             x1, NULL
    // 0xae21ac: r2 = 2
    //     0xae21ac: movz            x2, #0x2
    // 0xae21b0: r0 = AllocateArray()
    //     0xae21b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae21b4: mov             x2, x0
    // 0xae21b8: ldur            x0, [fp, #-0x28]
    // 0xae21bc: stur            x2, [fp, #-0x20]
    // 0xae21c0: StoreField: r2->field_f = r0
    //     0xae21c0: stur            w0, [x2, #0xf]
    // 0xae21c4: r1 = <BoxShadow>
    //     0xae21c4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29020] TypeArguments: <BoxShadow>
    //     0xae21c8: ldr             x1, [x1, #0x20]
    // 0xae21cc: r0 = AllocateGrowableArray()
    //     0xae21cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae21d0: mov             x1, x0
    // 0xae21d4: ldur            x0, [fp, #-0x20]
    // 0xae21d8: stur            x1, [fp, #-0x28]
    // 0xae21dc: StoreField: r1->field_f = r0
    //     0xae21dc: stur            w0, [x1, #0xf]
    // 0xae21e0: r0 = 2
    //     0xae21e0: movz            x0, #0x2
    // 0xae21e4: StoreField: r1->field_b = r0
    //     0xae21e4: stur            w0, [x1, #0xb]
    // 0xae21e8: r0 = BoxDecoration()
    //     0xae21e8: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae21ec: mov             x2, x0
    // 0xae21f0: ldur            x0, [fp, #-0x18]
    // 0xae21f4: stur            x2, [fp, #-0x20]
    // 0xae21f8: StoreField: r2->field_7 = r0
    //     0xae21f8: stur            w0, [x2, #7]
    // 0xae21fc: ldur            x0, [fp, #-0x28]
    // 0xae2200: ArrayStore: r2[0] = r0  ; List_4
    //     0xae2200: stur            w0, [x2, #0x17]
    // 0xae2204: r0 = Instance_BoxShape
    //     0xae2204: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xae2208: ldr             x0, [x0, #0xca8]
    // 0xae220c: StoreField: r2->field_23 = r0
    //     0xae220c: stur            w0, [x2, #0x23]
    // 0xae2210: ldur            x0, [fp, #-8]
    // 0xae2214: LoadField: r1 = r0->field_f
    //     0xae2214: ldur            w1, [x0, #0xf]
    // 0xae2218: DecompressPointer r1
    //     0xae2218: add             x1, x1, HEAP, lsl #32
    // 0xae221c: r0 = controller()
    //     0xae221c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae2220: LoadField: r1 = r0->field_2b
    //     0xae2220: ldur            w1, [x0, #0x2b]
    // 0xae2224: DecompressPointer r1
    //     0xae2224: add             x1, x1, HEAP, lsl #32
    // 0xae2228: r16 = Sentinel
    //     0xae2228: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae222c: cmp             w1, w16
    // 0xae2230: b.eq            #0xae231c
    // 0xae2234: stur            x1, [fp, #-8]
    // 0xae2238: r0 = TabBar()
    //     0xae2238: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xae223c: mov             x1, x0
    // 0xae2240: r0 = const [Instance of 'Tab', Instance of 'Tab']
    //     0xae2240: add             x0, PP, #0x30, lsl #12  ; [pp+0x304d8] List<Widget>(2)
    //     0xae2244: ldr             x0, [x0, #0x4d8]
    // 0xae2248: stur            x1, [fp, #-0x18]
    // 0xae224c: StoreField: r1->field_b = r0
    //     0xae224c: stur            w0, [x1, #0xb]
    // 0xae2250: ldur            x0, [fp, #-8]
    // 0xae2254: StoreField: r1->field_f = r0
    //     0xae2254: stur            w0, [x1, #0xf]
    // 0xae2258: r0 = false
    //     0xae2258: add             x0, NULL, #0x30  ; false
    // 0xae225c: StoreField: r1->field_13 = r0
    //     0xae225c: stur            w0, [x1, #0x13]
    // 0xae2260: r0 = true
    //     0xae2260: add             x0, NULL, #0x20  ; true
    // 0xae2264: StoreField: r1->field_2f = r0
    //     0xae2264: stur            w0, [x1, #0x2f]
    // 0xae2268: d0 = 2.000000
    //     0xae2268: fmov            d0, #2.00000000
    // 0xae226c: StoreField: r1->field_1f = d0
    //     0xae226c: stur            d0, [x1, #0x1f]
    // 0xae2270: r2 = Instance_EdgeInsets
    //     0xae2270: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xae2274: StoreField: r1->field_27 = r2
    //     0xae2274: stur            w2, [x1, #0x27]
    // 0xae2278: r2 = Instance_DragStartBehavior
    //     0xae2278: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae227c: StoreField: r1->field_57 = r2
    //     0xae227c: stur            w2, [x1, #0x57]
    // 0xae2280: StoreField: r1->field_7f = r0
    //     0xae2280: stur            w0, [x1, #0x7f]
    // 0xae2284: r0 = DecoratedBox()
    //     0xae2284: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xae2288: mov             x1, x0
    // 0xae228c: ldur            x0, [fp, #-0x20]
    // 0xae2290: stur            x1, [fp, #-8]
    // 0xae2294: StoreField: r1->field_f = r0
    //     0xae2294: stur            w0, [x1, #0xf]
    // 0xae2298: r0 = Instance_DecorationPosition
    //     0xae2298: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xae229c: ldr             x0, [x0, #0xb28]
    // 0xae22a0: StoreField: r1->field_13 = r0
    //     0xae22a0: stur            w0, [x1, #0x13]
    // 0xae22a4: ldur            x0, [fp, #-0x18]
    // 0xae22a8: StoreField: r1->field_b = r0
    //     0xae22a8: stur            w0, [x1, #0xb]
    // 0xae22ac: r0 = SliverPinnedHeader()
    //     0xae22ac: bl              #0xae2328  ; AllocateSliverPinnedHeaderStub -> SliverPinnedHeader (size=0x10)
    // 0xae22b0: mov             x3, x0
    // 0xae22b4: ldur            x0, [fp, #-8]
    // 0xae22b8: stur            x3, [fp, #-0x18]
    // 0xae22bc: StoreField: r3->field_b = r0
    //     0xae22bc: stur            w0, [x3, #0xb]
    // 0xae22c0: r1 = Null
    //     0xae22c0: mov             x1, NULL
    // 0xae22c4: r2 = 6
    //     0xae22c4: movz            x2, #0x6
    // 0xae22c8: r0 = AllocateArray()
    //     0xae22c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae22cc: mov             x2, x0
    // 0xae22d0: ldur            x0, [fp, #-0x10]
    // 0xae22d4: stur            x2, [fp, #-8]
    // 0xae22d8: StoreField: r2->field_f = r0
    //     0xae22d8: stur            w0, [x2, #0xf]
    // 0xae22dc: r16 = Instance_SliverToBoxAdapter
    //     0xae22dc: add             x16, PP, #0x30, lsl #12  ; [pp+0x304e0] Obj!SliverToBoxAdapter@e1deb1
    //     0xae22e0: ldr             x16, [x16, #0x4e0]
    // 0xae22e4: StoreField: r2->field_13 = r16
    //     0xae22e4: stur            w16, [x2, #0x13]
    // 0xae22e8: ldur            x0, [fp, #-0x18]
    // 0xae22ec: ArrayStore: r2[0] = r0  ; List_4
    //     0xae22ec: stur            w0, [x2, #0x17]
    // 0xae22f0: r1 = <Widget>
    //     0xae22f0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae22f4: r0 = AllocateGrowableArray()
    //     0xae22f4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae22f8: ldur            x1, [fp, #-8]
    // 0xae22fc: StoreField: r0->field_f = r1
    //     0xae22fc: stur            w1, [x0, #0xf]
    // 0xae2300: r1 = 6
    //     0xae2300: movz            x1, #0x6
    // 0xae2304: StoreField: r0->field_b = r1
    //     0xae2304: stur            w1, [x0, #0xb]
    // 0xae2308: LeaveFrame
    //     0xae2308: mov             SP, fp
    //     0xae230c: ldp             fp, lr, [SP], #0x10
    // 0xae2310: ret
    //     0xae2310: ret             
    // 0xae2314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2318: b               #0xae20c8
    // 0xae231c: r9 = tabController
    //     0xae231c: add             x9, PP, #0x30, lsl #12  ; [pp+0x304d0] Field <DonationNewsController.tabController>: late (offset: 0x2c)
    //     0xae2320: ldr             x9, [x9, #0x4d0]
    // 0xae2324: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae2324: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DonationNewsListBuilder <anonymous closure>(dynamic, DonationNewsListBuilderController) {
    // ** addr: 0xae234c, size: 0xc
    // 0xae234c: r0 = Instance_DonationNewsListBuilder
    //     0xae234c: add             x0, PP, #0x30, lsl #12  ; [pp+0x304e8] Obj!DonationNewsListBuilder@e21361
    //     0xae2350: ldr             x0, [x0, #0x4e8]
    // 0xae2354: ret
    //     0xae2354: ret             
  }
  [closure] DonationNewsListBuilder <anonymous closure>(dynamic, DonationNewsListBuilderController) {
    // ** addr: 0xae2358, size: 0xc
    // 0xae2358: r0 = Instance_DonationNewsListBuilder
    //     0xae2358: add             x0, PP, #0x30, lsl #12  ; [pp+0x304f0] Obj!DonationNewsListBuilder@e21381
    //     0xae235c: ldr             x0, [x0, #0x4f0]
    // 0xae2360: ret
    //     0xae2360: ret             
  }
}
