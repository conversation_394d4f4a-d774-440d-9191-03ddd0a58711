// lib: , url: package:nuonline/app/modules/donation/views/donation_tab_view.dart

// class id: 1050222, size: 0x8
class :: {
}

// class id: 5045, size: 0x14, field offset: 0xc
//   const constructor, 
class DonationTabView extends StatelessWidget {

  Merchant field_c;
  PaymentType field_10;

  _ build(/* No info */) {
    // ** addr: 0xb8d30c, size: 0xcc
    // 0xb8d30c: EnterFrame
    //     0xb8d30c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d310: mov             fp, SP
    // 0xb8d314: AllocStack(0x20)
    //     0xb8d314: sub             SP, SP, #0x20
    // 0xb8d318: CheckStackOverflow
    //     0xb8d318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d31c: cmp             SP, x16
    //     0xb8d320: b.ls            #0xb8d3d0
    // 0xb8d324: LoadField: r0 = r1->field_f
    //     0xb8d324: ldur            w0, [x1, #0xf]
    // 0xb8d328: DecompressPointer r0
    //     0xb8d328: add             x0, x0, HEAP, lsl #32
    // 0xb8d32c: stur            x0, [fp, #-0x18]
    // 0xb8d330: LoadField: r2 = r0->field_13
    //     0xb8d330: ldur            w2, [x0, #0x13]
    // 0xb8d334: DecompressPointer r2
    //     0xb8d334: add             x2, x2, HEAP, lsl #32
    // 0xb8d338: stur            x2, [fp, #-0x10]
    // 0xb8d33c: LoadField: r3 = r1->field_b
    //     0xb8d33c: ldur            w3, [x1, #0xb]
    // 0xb8d340: DecompressPointer r3
    //     0xb8d340: add             x3, x3, HEAP, lsl #32
    // 0xb8d344: stur            x3, [fp, #-8]
    // 0xb8d348: r0 = DonationTabController()
    //     0xb8d348: bl              #0xb8d3d8  ; AllocateDonationTabControllerStub -> DonationTabController (size=0x28)
    // 0xb8d34c: mov             x2, x0
    // 0xb8d350: ldur            x0, [fp, #-8]
    // 0xb8d354: stur            x2, [fp, #-0x20]
    // 0xb8d358: StoreField: r2->field_1f = r0
    //     0xb8d358: stur            w0, [x2, #0x1f]
    // 0xb8d35c: ldur            x0, [fp, #-0x18]
    // 0xb8d360: StoreField: r2->field_23 = r0
    //     0xb8d360: stur            w0, [x2, #0x23]
    // 0xb8d364: mov             x1, x2
    // 0xb8d368: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb8d368: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb8d36c: r1 = <DonationTabController>
    //     0xb8d36c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f548] TypeArguments: <DonationTabController>
    //     0xb8d370: ldr             x1, [x1, #0x548]
    // 0xb8d374: r0 = GetBuilder()
    //     0xb8d374: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb8d378: mov             x3, x0
    // 0xb8d37c: ldur            x0, [fp, #-0x20]
    // 0xb8d380: stur            x3, [fp, #-8]
    // 0xb8d384: StoreField: r3->field_3b = r0
    //     0xb8d384: stur            w0, [x3, #0x3b]
    // 0xb8d388: r0 = true
    //     0xb8d388: add             x0, NULL, #0x20  ; true
    // 0xb8d38c: StoreField: r3->field_13 = r0
    //     0xb8d38c: stur            w0, [x3, #0x13]
    // 0xb8d390: r1 = Function '<anonymous closure>':.
    //     0xb8d390: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f550] AnonymousClosure: (0xb8d3e4), in [package:nuonline/app/modules/donation/views/donation_tab_view.dart] DonationTabView::build (0xb8d30c)
    //     0xb8d394: ldr             x1, [x1, #0x550]
    // 0xb8d398: r2 = Null
    //     0xb8d398: mov             x2, NULL
    // 0xb8d39c: r0 = AllocateClosure()
    //     0xb8d39c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d3a0: mov             x1, x0
    // 0xb8d3a4: ldur            x0, [fp, #-8]
    // 0xb8d3a8: StoreField: r0->field_f = r1
    //     0xb8d3a8: stur            w1, [x0, #0xf]
    // 0xb8d3ac: r1 = true
    //     0xb8d3ac: add             x1, NULL, #0x20  ; true
    // 0xb8d3b0: StoreField: r0->field_1f = r1
    //     0xb8d3b0: stur            w1, [x0, #0x1f]
    // 0xb8d3b4: r1 = false
    //     0xb8d3b4: add             x1, NULL, #0x30  ; false
    // 0xb8d3b8: StoreField: r0->field_23 = r1
    //     0xb8d3b8: stur            w1, [x0, #0x23]
    // 0xb8d3bc: ldur            x1, [fp, #-0x10]
    // 0xb8d3c0: StoreField: r0->field_1b = r1
    //     0xb8d3c0: stur            w1, [x0, #0x1b]
    // 0xb8d3c4: LeaveFrame
    //     0xb8d3c4: mov             SP, fp
    //     0xb8d3c8: ldp             fp, lr, [SP], #0x10
    // 0xb8d3cc: ret
    //     0xb8d3cc: ret             
    // 0xb8d3d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d3d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d3d4: b               #0xb8d324
  }
  [closure] Scaffold <anonymous closure>(dynamic, DonationTabController) {
    // ** addr: 0xb8d3e4, size: 0x2c4
    // 0xb8d3e4: EnterFrame
    //     0xb8d3e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d3e8: mov             fp, SP
    // 0xb8d3ec: AllocStack(0x38)
    //     0xb8d3ec: sub             SP, SP, #0x38
    // 0xb8d3f0: SetupParameters()
    //     0xb8d3f0: ldr             x0, [fp, #0x18]
    //     0xb8d3f4: ldur            w1, [x0, #0x17]
    //     0xb8d3f8: add             x1, x1, HEAP, lsl #32
    //     0xb8d3fc: stur            x1, [fp, #-8]
    // 0xb8d400: CheckStackOverflow
    //     0xb8d400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d404: cmp             SP, x16
    //     0xb8d408: b.ls            #0xb8d6a0
    // 0xb8d40c: r1 = 1
    //     0xb8d40c: movz            x1, #0x1
    // 0xb8d410: r0 = AllocateContext()
    //     0xb8d410: bl              #0xec126c  ; AllocateContextStub
    // 0xb8d414: mov             x1, x0
    // 0xb8d418: ldur            x0, [fp, #-8]
    // 0xb8d41c: stur            x1, [fp, #-0x10]
    // 0xb8d420: StoreField: r1->field_b = r0
    //     0xb8d420: stur            w0, [x1, #0xb]
    // 0xb8d424: ldr             x0, [fp, #0x10]
    // 0xb8d428: StoreField: r1->field_f = r0
    //     0xb8d428: stur            w0, [x1, #0xf]
    // 0xb8d42c: LoadField: r2 = r0->field_23
    //     0xb8d42c: ldur            w2, [x0, #0x23]
    // 0xb8d430: DecompressPointer r2
    //     0xb8d430: add             x2, x2, HEAP, lsl #32
    // 0xb8d434: LoadField: r0 = r2->field_13
    //     0xb8d434: ldur            w0, [x2, #0x13]
    // 0xb8d438: DecompressPointer r0
    //     0xb8d438: add             x0, x0, HEAP, lsl #32
    // 0xb8d43c: stur            x0, [fp, #-8]
    // 0xb8d440: r0 = Text()
    //     0xb8d440: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8d444: mov             x1, x0
    // 0xb8d448: ldur            x0, [fp, #-8]
    // 0xb8d44c: stur            x1, [fp, #-0x18]
    // 0xb8d450: StoreField: r1->field_b = r0
    //     0xb8d450: stur            w0, [x1, #0xb]
    // 0xb8d454: r0 = IconButton()
    //     0xb8d454: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb8d458: mov             x3, x0
    // 0xb8d45c: r0 = Instance_EdgeInsets
    //     0xb8d45c: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb8d460: stur            x3, [fp, #-8]
    // 0xb8d464: StoreField: r3->field_13 = r0
    //     0xb8d464: stur            w0, [x3, #0x13]
    // 0xb8d468: ldur            x2, [fp, #-0x10]
    // 0xb8d46c: r1 = Function '<anonymous closure>':.
    //     0xb8d46c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f558] AnonymousClosure: (0xb8d6a8), in [package:nuonline/app/modules/donation/views/donation_tab_view.dart] DonationTabView::build (0xb8d30c)
    //     0xb8d470: ldr             x1, [x1, #0x558]
    // 0xb8d474: r0 = AllocateClosure()
    //     0xb8d474: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d478: mov             x1, x0
    // 0xb8d47c: ldur            x0, [fp, #-8]
    // 0xb8d480: StoreField: r0->field_3b = r1
    //     0xb8d480: stur            w1, [x0, #0x3b]
    // 0xb8d484: r3 = false
    //     0xb8d484: add             x3, NULL, #0x30  ; false
    // 0xb8d488: StoreField: r0->field_47 = r3
    //     0xb8d488: stur            w3, [x0, #0x47]
    // 0xb8d48c: r1 = Instance_Icon
    //     0xb8d48c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aec8] Obj!Icon@e24531
    //     0xb8d490: ldr             x1, [x1, #0xec8]
    // 0xb8d494: StoreField: r0->field_1f = r1
    //     0xb8d494: stur            w1, [x0, #0x1f]
    // 0xb8d498: r1 = Instance__IconButtonVariant
    //     0xb8d498: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb8d49c: ldr             x1, [x1, #0xf78]
    // 0xb8d4a0: StoreField: r0->field_63 = r1
    //     0xb8d4a0: stur            w1, [x0, #0x63]
    // 0xb8d4a4: r1 = Null
    //     0xb8d4a4: mov             x1, NULL
    // 0xb8d4a8: r2 = 2
    //     0xb8d4a8: movz            x2, #0x2
    // 0xb8d4ac: r0 = AllocateArray()
    //     0xb8d4ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8d4b0: mov             x2, x0
    // 0xb8d4b4: ldur            x0, [fp, #-8]
    // 0xb8d4b8: stur            x2, [fp, #-0x20]
    // 0xb8d4bc: StoreField: r2->field_f = r0
    //     0xb8d4bc: stur            w0, [x2, #0xf]
    // 0xb8d4c0: r1 = <Widget>
    //     0xb8d4c0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8d4c4: r0 = AllocateGrowableArray()
    //     0xb8d4c4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8d4c8: mov             x1, x0
    // 0xb8d4cc: ldur            x0, [fp, #-0x20]
    // 0xb8d4d0: stur            x1, [fp, #-8]
    // 0xb8d4d4: StoreField: r1->field_f = r0
    //     0xb8d4d4: stur            w0, [x1, #0xf]
    // 0xb8d4d8: r0 = 2
    //     0xb8d4d8: movz            x0, #0x2
    // 0xb8d4dc: StoreField: r1->field_b = r0
    //     0xb8d4dc: stur            w0, [x1, #0xb]
    // 0xb8d4e0: r0 = AppBar()
    //     0xb8d4e0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb8d4e4: stur            x0, [fp, #-0x20]
    // 0xb8d4e8: ldur            x16, [fp, #-0x18]
    // 0xb8d4ec: ldur            lr, [fp, #-8]
    // 0xb8d4f0: stp             lr, x16, [SP]
    // 0xb8d4f4: mov             x1, x0
    // 0xb8d4f8: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xb8d4f8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xb8d4fc: ldr             x4, [x4, #0xf88]
    // 0xb8d500: r0 = AppBar()
    //     0xb8d500: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb8d504: ldur            x2, [fp, #-0x10]
    // 0xb8d508: LoadField: r0 = r2->field_f
    //     0xb8d508: ldur            w0, [x2, #0xf]
    // 0xb8d50c: DecompressPointer r0
    //     0xb8d50c: add             x0, x0, HEAP, lsl #32
    // 0xb8d510: LoadField: r1 = r0->field_1f
    //     0xb8d510: ldur            w1, [x0, #0x1f]
    // 0xb8d514: DecompressPointer r1
    //     0xb8d514: add             x1, x1, HEAP, lsl #32
    // 0xb8d518: stur            x1, [fp, #-0x18]
    // 0xb8d51c: LoadField: r3 = r0->field_23
    //     0xb8d51c: ldur            w3, [x0, #0x23]
    // 0xb8d520: DecompressPointer r3
    //     0xb8d520: add             x3, x3, HEAP, lsl #32
    // 0xb8d524: stur            x3, [fp, #-8]
    // 0xb8d528: r0 = MerchantInfo()
    //     0xb8d528: bl              #0xae2340  ; AllocateMerchantInfoStub -> MerchantInfo (size=0x1c)
    // 0xb8d52c: mov             x1, x0
    // 0xb8d530: ldur            x0, [fp, #-0x18]
    // 0xb8d534: stur            x1, [fp, #-0x28]
    // 0xb8d538: StoreField: r1->field_b = r0
    //     0xb8d538: stur            w0, [x1, #0xb]
    // 0xb8d53c: ldur            x0, [fp, #-8]
    // 0xb8d540: StoreField: r1->field_f = r0
    //     0xb8d540: stur            w0, [x1, #0xf]
    // 0xb8d544: r2 = true
    //     0xb8d544: add             x2, NULL, #0x20  ; true
    // 0xb8d548: StoreField: r1->field_13 = r2
    //     0xb8d548: stur            w2, [x1, #0x13]
    // 0xb8d54c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb8d54c: stur            w2, [x1, #0x17]
    // 0xb8d550: r0 = TransactionHistory()
    //     0xb8d550: bl              #0xadee74  ; AllocateTransactionHistoryStub -> TransactionHistory (size=0x14)
    // 0xb8d554: mov             x1, x0
    // 0xb8d558: ldur            x0, [fp, #-8]
    // 0xb8d55c: stur            x1, [fp, #-0x18]
    // 0xb8d560: StoreField: r1->field_b = r0
    //     0xb8d560: stur            w0, [x1, #0xb]
    // 0xb8d564: r0 = NCardListHeader()
    //     0xb8d564: bl              #0xadee68  ; AllocateNCardListHeaderStub -> NCardListHeader (size=0x2c)
    // 0xb8d568: mov             x3, x0
    // 0xb8d56c: r0 = "Lihat Kabar Terbaru"
    //     0xb8d56c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f560] "Lihat Kabar Terbaru"
    //     0xb8d570: ldr             x0, [x0, #0x560]
    // 0xb8d574: stur            x3, [fp, #-8]
    // 0xb8d578: StoreField: r3->field_f = r0
    //     0xb8d578: stur            w0, [x3, #0xf]
    // 0xb8d57c: ldur            x2, [fp, #-0x10]
    // 0xb8d580: r1 = Function '<anonymous closure>':.
    //     0xb8d580: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f568] AnonymousClosure: (0xb8d6a8), in [package:nuonline/app/modules/donation/views/donation_tab_view.dart] DonationTabView::build (0xb8d30c)
    //     0xb8d584: ldr             x1, [x1, #0x568]
    // 0xb8d588: r0 = AllocateClosure()
    //     0xb8d588: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d58c: mov             x1, x0
    // 0xb8d590: ldur            x0, [fp, #-8]
    // 0xb8d594: StoreField: r0->field_1b = r1
    //     0xb8d594: stur            w1, [x0, #0x1b]
    // 0xb8d598: r1 = false
    //     0xb8d598: add             x1, NULL, #0x30  ; false
    // 0xb8d59c: StoreField: r0->field_1f = r1
    //     0xb8d59c: stur            w1, [x0, #0x1f]
    // 0xb8d5a0: StoreField: r0->field_23 = r1
    //     0xb8d5a0: stur            w1, [x0, #0x23]
    // 0xb8d5a4: r0 = Padding()
    //     0xb8d5a4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8d5a8: mov             x3, x0
    // 0xb8d5ac: r0 = Instance_EdgeInsets
    //     0xb8d5ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f570] Obj!EdgeInsets@e134e1
    //     0xb8d5b0: ldr             x0, [x0, #0x570]
    // 0xb8d5b4: stur            x3, [fp, #-0x10]
    // 0xb8d5b8: StoreField: r3->field_f = r0
    //     0xb8d5b8: stur            w0, [x3, #0xf]
    // 0xb8d5bc: ldur            x0, [fp, #-8]
    // 0xb8d5c0: StoreField: r3->field_b = r0
    //     0xb8d5c0: stur            w0, [x3, #0xb]
    // 0xb8d5c4: r1 = Null
    //     0xb8d5c4: mov             x1, NULL
    // 0xb8d5c8: r2 = 10
    //     0xb8d5c8: movz            x2, #0xa
    // 0xb8d5cc: r0 = AllocateArray()
    //     0xb8d5cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8d5d0: mov             x2, x0
    // 0xb8d5d4: ldur            x0, [fp, #-0x28]
    // 0xb8d5d8: stur            x2, [fp, #-8]
    // 0xb8d5dc: StoreField: r2->field_f = r0
    //     0xb8d5dc: stur            w0, [x2, #0xf]
    // 0xb8d5e0: r16 = Instance_NSectionDivider
    //     0xb8d5e0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xb8d5e4: ldr             x16, [x16, #0x38]
    // 0xb8d5e8: StoreField: r2->field_13 = r16
    //     0xb8d5e8: stur            w16, [x2, #0x13]
    // 0xb8d5ec: ldur            x0, [fp, #-0x18]
    // 0xb8d5f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8d5f0: stur            w0, [x2, #0x17]
    // 0xb8d5f4: r16 = Instance_NSectionDivider
    //     0xb8d5f4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xb8d5f8: ldr             x16, [x16, #0x38]
    // 0xb8d5fc: StoreField: r2->field_1b = r16
    //     0xb8d5fc: stur            w16, [x2, #0x1b]
    // 0xb8d600: ldur            x0, [fp, #-0x10]
    // 0xb8d604: StoreField: r2->field_1f = r0
    //     0xb8d604: stur            w0, [x2, #0x1f]
    // 0xb8d608: r1 = <Widget>
    //     0xb8d608: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8d60c: r0 = AllocateGrowableArray()
    //     0xb8d60c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8d610: mov             x1, x0
    // 0xb8d614: ldur            x0, [fp, #-8]
    // 0xb8d618: stur            x1, [fp, #-0x10]
    // 0xb8d61c: StoreField: r1->field_f = r0
    //     0xb8d61c: stur            w0, [x1, #0xf]
    // 0xb8d620: r0 = 10
    //     0xb8d620: movz            x0, #0xa
    // 0xb8d624: StoreField: r1->field_b = r0
    //     0xb8d624: stur            w0, [x1, #0xb]
    // 0xb8d628: r0 = ListView()
    //     0xb8d628: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb8d62c: stur            x0, [fp, #-8]
    // 0xb8d630: r16 = Instance_EdgeInsets
    //     0xb8d630: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f578] Obj!EdgeInsets@e134b1
    //     0xb8d634: ldr             x16, [x16, #0x578]
    // 0xb8d638: str             x16, [SP]
    // 0xb8d63c: mov             x1, x0
    // 0xb8d640: ldur            x2, [fp, #-0x10]
    // 0xb8d644: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb8d644: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb8d648: ldr             x4, [x4, #0xa0]
    // 0xb8d64c: r0 = ListView()
    //     0xb8d64c: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb8d650: r0 = Scaffold()
    //     0xb8d650: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb8d654: ldur            x1, [fp, #-0x20]
    // 0xb8d658: StoreField: r0->field_13 = r1
    //     0xb8d658: stur            w1, [x0, #0x13]
    // 0xb8d65c: ldur            x1, [fp, #-8]
    // 0xb8d660: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8d660: stur            w1, [x0, #0x17]
    // 0xb8d664: r1 = Instance_AlignmentDirectional
    //     0xb8d664: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb8d668: ldr             x1, [x1, #0x758]
    // 0xb8d66c: StoreField: r0->field_2b = r1
    //     0xb8d66c: stur            w1, [x0, #0x2b]
    // 0xb8d670: r1 = true
    //     0xb8d670: add             x1, NULL, #0x20  ; true
    // 0xb8d674: StoreField: r0->field_53 = r1
    //     0xb8d674: stur            w1, [x0, #0x53]
    // 0xb8d678: r2 = Instance_DragStartBehavior
    //     0xb8d678: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb8d67c: StoreField: r0->field_57 = r2
    //     0xb8d67c: stur            w2, [x0, #0x57]
    // 0xb8d680: r2 = false
    //     0xb8d680: add             x2, NULL, #0x30  ; false
    // 0xb8d684: StoreField: r0->field_b = r2
    //     0xb8d684: stur            w2, [x0, #0xb]
    // 0xb8d688: StoreField: r0->field_f = r2
    //     0xb8d688: stur            w2, [x0, #0xf]
    // 0xb8d68c: StoreField: r0->field_5f = r1
    //     0xb8d68c: stur            w1, [x0, #0x5f]
    // 0xb8d690: StoreField: r0->field_63 = r1
    //     0xb8d690: stur            w1, [x0, #0x63]
    // 0xb8d694: LeaveFrame
    //     0xb8d694: mov             SP, fp
    //     0xb8d698: ldp             fp, lr, [SP], #0x10
    // 0xb8d69c: ret
    //     0xb8d69c: ret             
    // 0xb8d6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d6a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d6a4: b               #0xb8d40c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8d6a8, size: 0x9c
    // 0xb8d6a8: EnterFrame
    //     0xb8d6a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d6ac: mov             fp, SP
    // 0xb8d6b0: AllocStack(0x18)
    //     0xb8d6b0: sub             SP, SP, #0x18
    // 0xb8d6b4: SetupParameters()
    //     0xb8d6b4: ldr             x0, [fp, #0x10]
    //     0xb8d6b8: ldur            w1, [x0, #0x17]
    //     0xb8d6bc: add             x1, x1, HEAP, lsl #32
    //     0xb8d6c0: stur            x1, [fp, #-8]
    // 0xb8d6c4: CheckStackOverflow
    //     0xb8d6c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d6c8: cmp             SP, x16
    //     0xb8d6cc: b.ls            #0xb8d73c
    // 0xb8d6d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d6d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d6d4: ldr             x0, [x0, #0x2670]
    //     0xb8d6d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8d6dc: cmp             w0, w16
    //     0xb8d6e0: b.ne            #0xb8d6ec
    //     0xb8d6e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8d6e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8d6ec: ldur            x0, [fp, #-8]
    // 0xb8d6f0: LoadField: r1 = r0->field_f
    //     0xb8d6f0: ldur            w1, [x0, #0xf]
    // 0xb8d6f4: DecompressPointer r1
    //     0xb8d6f4: add             x1, x1, HEAP, lsl #32
    // 0xb8d6f8: LoadField: r0 = r1->field_23
    //     0xb8d6f8: ldur            w0, [x1, #0x23]
    // 0xb8d6fc: DecompressPointer r0
    //     0xb8d6fc: add             x0, x0, HEAP, lsl #32
    // 0xb8d700: r16 = Instance_PaymentType
    //     0xb8d700: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb8d704: ldr             x16, [x16, #0x5a8]
    // 0xb8d708: cmp             w0, w16
    // 0xb8d70c: b.ne            #0xb8d71c
    // 0xb8d710: r0 = "/donation/donation-news"
    //     0xb8d710: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f580] "/donation/donation-news"
    //     0xb8d714: ldr             x0, [x0, #0x580]
    // 0xb8d718: b               #0xb8d724
    // 0xb8d71c: r0 = "/donation/zakat-news"
    //     0xb8d71c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f588] "/donation/zakat-news"
    //     0xb8d720: ldr             x0, [x0, #0x588]
    // 0xb8d724: stp             x0, NULL, [SP]
    // 0xb8d728: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb8d728: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8d72c: r0 = GetNavigation.toNamed()
    //     0xb8d72c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8d730: LeaveFrame
    //     0xb8d730: mov             SP, fp
    //     0xb8d734: ldp             fp, lr, [SP], #0x10
    // 0xb8d738: ret
    //     0xb8d738: ret             
    // 0xb8d73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d740: b               #0xb8d6d0
  }
}
