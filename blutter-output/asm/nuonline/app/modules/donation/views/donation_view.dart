// lib: , url: package:nuonline/app/modules/donation/views/donation_view.dart

// class id: 1050223, size: 0x8
class :: {
}

// class id: 5044, size: 0x1c, field offset: 0xc
//   const constructor, 
class _Menu extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8d744, size: 0xe8
    // 0xb8d744: EnterFrame
    //     0xb8d744: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d748: mov             fp, SP
    // 0xb8d74c: AllocStack(0x48)
    //     0xb8d74c: sub             SP, SP, #0x48
    // 0xb8d750: SetupParameters(_Menu this /* r1 => r1, fp-0x28 */)
    //     0xb8d750: stur            x1, [fp, #-0x28]
    // 0xb8d754: CheckStackOverflow
    //     0xb8d754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d758: cmp             SP, x16
    //     0xb8d75c: b.ls            #0xb8d824
    // 0xb8d760: LoadField: r0 = r1->field_b
    //     0xb8d760: ldur            w0, [x1, #0xb]
    // 0xb8d764: DecompressPointer r0
    //     0xb8d764: add             x0, x0, HEAP, lsl #32
    // 0xb8d768: stur            x0, [fp, #-0x20]
    // 0xb8d76c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb8d76c: ldur            w2, [x1, #0x17]
    // 0xb8d770: DecompressPointer r2
    //     0xb8d770: add             x2, x2, HEAP, lsl #32
    // 0xb8d774: stur            x2, [fp, #-0x18]
    // 0xb8d778: cmp             w2, NULL
    // 0xb8d77c: r16 = true
    //     0xb8d77c: add             x16, NULL, #0x20  ; true
    // 0xb8d780: r17 = false
    //     0xb8d780: add             x17, NULL, #0x30  ; false
    // 0xb8d784: csel            x3, x16, x17, ne
    // 0xb8d788: stur            x3, [fp, #-0x10]
    // 0xb8d78c: LoadField: r4 = r1->field_f
    //     0xb8d78c: ldur            w4, [x1, #0xf]
    // 0xb8d790: DecompressPointer r4
    //     0xb8d790: add             x4, x4, HEAP, lsl #32
    // 0xb8d794: stur            x4, [fp, #-8]
    // 0xb8d798: r0 = SvgPicture()
    //     0xb8d798: bl              #0xacfad4  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8d79c: stur            x0, [fp, #-0x30]
    // 0xb8d7a0: r16 = "nuikit"
    //     0xb8d7a0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25798] "nuikit"
    //     0xb8d7a4: ldr             x16, [x16, #0x798]
    // 0xb8d7a8: r30 = 52.000000
    //     0xb8d7a8: add             lr, PP, #0x33, lsl #12  ; [pp+0x33cd8] 52
    //     0xb8d7ac: ldr             lr, [lr, #0xcd8]
    // 0xb8d7b0: stp             lr, x16, [SP, #8]
    // 0xb8d7b4: r16 = 52.000000
    //     0xb8d7b4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33cd8] 52
    //     0xb8d7b8: ldr             x16, [x16, #0xcd8]
    // 0xb8d7bc: str             x16, [SP]
    // 0xb8d7c0: mov             x1, x0
    // 0xb8d7c4: ldur            x2, [fp, #-8]
    // 0xb8d7c8: r4 = const [0, 0x5, 0x3, 0x2, height, 0x3, package, 0x2, width, 0x4, null]
    //     0xb8d7c8: add             x4, PP, #0x34, lsl #12  ; [pp+0x34f98] List(11) [0, 0x5, 0x3, 0x2, "height", 0x3, "package", 0x2, "width", 0x4, Null]
    //     0xb8d7cc: ldr             x4, [x4, #0xf98]
    // 0xb8d7d0: r0 = SvgPicture.asset()
    //     0xb8d7d0: bl              #0xabab8c  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8d7d4: ldur            x0, [fp, #-0x28]
    // 0xb8d7d8: LoadField: r1 = r0->field_13
    //     0xb8d7d8: ldur            w1, [x0, #0x13]
    // 0xb8d7dc: DecompressPointer r1
    //     0xb8d7dc: add             x1, x1, HEAP, lsl #32
    // 0xb8d7e0: stur            x1, [fp, #-8]
    // 0xb8d7e4: r0 = NMenuGridTile()
    //     0xb8d7e4: bl              #0xb8d82c  ; AllocateNMenuGridTileStub -> NMenuGridTile (size=0x24)
    // 0xb8d7e8: ldur            x1, [fp, #-0x30]
    // 0xb8d7ec: StoreField: r0->field_b = r1
    //     0xb8d7ec: stur            w1, [x0, #0xb]
    // 0xb8d7f0: ldur            x1, [fp, #-0x20]
    // 0xb8d7f4: StoreField: r0->field_f = r1
    //     0xb8d7f4: stur            w1, [x0, #0xf]
    // 0xb8d7f8: ldur            x1, [fp, #-8]
    // 0xb8d7fc: StoreField: r0->field_1f = r1
    //     0xb8d7fc: stur            w1, [x0, #0x1f]
    // 0xb8d800: r1 = false
    //     0xb8d800: add             x1, NULL, #0x30  ; false
    // 0xb8d804: StoreField: r0->field_13 = r1
    //     0xb8d804: stur            w1, [x0, #0x13]
    // 0xb8d808: ldur            x1, [fp, #-0x18]
    // 0xb8d80c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8d80c: stur            w1, [x0, #0x17]
    // 0xb8d810: ldur            x1, [fp, #-0x10]
    // 0xb8d814: StoreField: r0->field_1b = r1
    //     0xb8d814: stur            w1, [x0, #0x1b]
    // 0xb8d818: LeaveFrame
    //     0xb8d818: mov             SP, fp
    //     0xb8d81c: ldp             fp, lr, [SP], #0x10
    // 0xb8d820: ret
    //     0xb8d820: ret             
    // 0xb8d824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d824: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d828: b               #0xb8d760
  }
}

// class id: 5293, size: 0x14, field offset: 0x14
class DonationView extends GetView<dynamic> {

  [closure] NCampaignCardSkeleton <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xae0260, size: 0xc
    // 0xae0260: r0 = Instance_NCampaignCardSkeleton
    //     0xae0260: add             x0, PP, #0x30, lsl #12  ; [pp+0x30420] Obj!NCampaignCardSkeleton@e20dd1
    //     0xae0264: ldr             x0, [x0, #0x420]
    // 0xae0268: ret
    //     0xae0268: ret             
  }
  [closure] ListView <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xae026c, size: 0x8c
    // 0xae026c: EnterFrame
    //     0xae026c: stp             fp, lr, [SP, #-0x10]!
    //     0xae0270: mov             fp, SP
    // 0xae0274: AllocStack(0x28)
    //     0xae0274: sub             SP, SP, #0x28
    // 0xae0278: CheckStackOverflow
    //     0xae0278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae027c: cmp             SP, x16
    //     0xae0280: b.ls            #0xae02f0
    // 0xae0284: r1 = Function '<anonymous closure>':.
    //     0xae0284: add             x1, PP, #0x30, lsl #12  ; [pp+0x30410] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xae0288: ldr             x1, [x1, #0x410]
    // 0xae028c: r2 = Null
    //     0xae028c: mov             x2, NULL
    // 0xae0290: r0 = AllocateClosure()
    //     0xae0290: bl              #0xec1630  ; AllocateClosureStub
    // 0xae0294: r1 = Function '<anonymous closure>':.
    //     0xae0294: add             x1, PP, #0x30, lsl #12  ; [pp+0x30418] AnonymousClosure: (0xae0260), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae0298: ldr             x1, [x1, #0x418]
    // 0xae029c: r2 = Null
    //     0xae029c: mov             x2, NULL
    // 0xae02a0: stur            x0, [fp, #-8]
    // 0xae02a4: r0 = AllocateClosure()
    //     0xae02a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae02a8: stur            x0, [fp, #-0x10]
    // 0xae02ac: r0 = ListView()
    //     0xae02ac: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae02b0: stur            x0, [fp, #-0x18]
    // 0xae02b4: r16 = true
    //     0xae02b4: add             x16, NULL, #0x20  ; true
    // 0xae02b8: r30 = Instance_NeverScrollableScrollPhysics
    //     0xae02b8: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xae02bc: ldr             lr, [lr, #0x290]
    // 0xae02c0: stp             lr, x16, [SP]
    // 0xae02c4: mov             x1, x0
    // 0xae02c8: ldur            x2, [fp, #-0x10]
    // 0xae02cc: ldur            x5, [fp, #-8]
    // 0xae02d0: r3 = 5
    //     0xae02d0: movz            x3, #0x5
    // 0xae02d4: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xae02d4: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xae02d8: ldr             x4, [x4, #0x298]
    // 0xae02dc: r0 = ListView.separated()
    //     0xae02dc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xae02e0: ldur            x0, [fp, #-0x18]
    // 0xae02e4: LeaveFrame
    //     0xae02e4: mov             SP, fp
    //     0xae02e8: ldp             fp, lr, [SP], #0x10
    // 0xae02ec: ret
    //     0xae02ec: ret             
    // 0xae02f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae02f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae02f4: b               #0xae0284
  }
  [closure] Widget <anonymous closure>(dynamic, CampaignListController) {
    // ** addr: 0xae02f8, size: 0x80
    // 0xae02f8: EnterFrame
    //     0xae02f8: stp             fp, lr, [SP, #-0x10]!
    //     0xae02fc: mov             fp, SP
    // 0xae0300: AllocStack(0x20)
    //     0xae0300: sub             SP, SP, #0x20
    // 0xae0304: CheckStackOverflow
    //     0xae0304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0308: cmp             SP, x16
    //     0xae030c: b.ls            #0xae0370
    // 0xae0310: r1 = Function '<anonymous closure>':.
    //     0xae0310: add             x1, PP, #0x30, lsl #12  ; [pp+0x303f8] AnonymousClosure: (0xae0378), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae0314: ldr             x1, [x1, #0x3f8]
    // 0xae0318: r2 = Null
    //     0xae0318: mov             x2, NULL
    // 0xae031c: r0 = AllocateClosure()
    //     0xae031c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae0320: r1 = Function '<anonymous closure>':.
    //     0xae0320: add             x1, PP, #0x30, lsl #12  ; [pp+0x30400] AnonymousClosure: (0xae026c), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae0324: ldr             x1, [x1, #0x400]
    // 0xae0328: r2 = Null
    //     0xae0328: mov             x2, NULL
    // 0xae032c: stur            x0, [fp, #-8]
    // 0xae0330: r0 = AllocateClosure()
    //     0xae0330: bl              #0xec1630  ; AllocateClosureStub
    // 0xae0334: r16 = true
    //     0xae0334: add             x16, NULL, #0x20  ; true
    // 0xae0338: r30 = Instance_NeverScrollableScrollPhysics
    //     0xae0338: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xae033c: ldr             lr, [lr, #0x290]
    // 0xae0340: stp             lr, x16, [SP, #8]
    // 0xae0344: str             x0, [SP]
    // 0xae0348: ldr             x1, [fp, #0x10]
    // 0xae034c: ldur            x2, [fp, #-8]
    // 0xae0350: r3 = Instance_Divider
    //     0xae0350: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xae0354: ldr             x3, [x3, #0xc28]
    // 0xae0358: r4 = const [0, 0x6, 0x3, 0x3, firstPageLoading, 0x5, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xae0358: add             x4, PP, #0x30, lsl #12  ; [pp+0x30408] List(11) [0, 0x6, 0x3, 0x3, "firstPageLoading", 0x5, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xae035c: ldr             x4, [x4, #0x408]
    // 0xae0360: r0 = paginate()
    //     0xae0360: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xae0364: LeaveFrame
    //     0xae0364: mov             SP, fp
    //     0xae0368: ldp             fp, lr, [SP], #0x10
    // 0xae036c: ret
    //     0xae036c: ret             
    // 0xae0370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0374: b               #0xae0310
  }
  [closure] NCampaignCard <anonymous closure>(dynamic, BuildContext, Campaign, int) {
    // ** addr: 0xae0378, size: 0x16c
    // 0xae0378: EnterFrame
    //     0xae0378: stp             fp, lr, [SP, #-0x10]!
    //     0xae037c: mov             fp, SP
    // 0xae0380: AllocStack(0x50)
    //     0xae0380: sub             SP, SP, #0x50
    // 0xae0384: SetupParameters()
    //     0xae0384: ldr             x0, [fp, #0x28]
    //     0xae0388: ldur            w1, [x0, #0x17]
    //     0xae038c: add             x1, x1, HEAP, lsl #32
    //     0xae0390: stur            x1, [fp, #-8]
    // 0xae0394: CheckStackOverflow
    //     0xae0394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0398: cmp             SP, x16
    //     0xae039c: b.ls            #0xae04dc
    // 0xae03a0: r1 = 1
    //     0xae03a0: movz            x1, #0x1
    // 0xae03a4: r0 = AllocateContext()
    //     0xae03a4: bl              #0xec126c  ; AllocateContextStub
    // 0xae03a8: mov             x2, x0
    // 0xae03ac: ldur            x0, [fp, #-8]
    // 0xae03b0: stur            x2, [fp, #-0x20]
    // 0xae03b4: StoreField: r2->field_b = r0
    //     0xae03b4: stur            w0, [x2, #0xb]
    // 0xae03b8: ldr             x0, [fp, #0x18]
    // 0xae03bc: StoreField: r2->field_f = r0
    //     0xae03bc: stur            w0, [x2, #0xf]
    // 0xae03c0: LoadField: r3 = r0->field_1b
    //     0xae03c0: ldur            w3, [x0, #0x1b]
    // 0xae03c4: DecompressPointer r3
    //     0xae03c4: add             x3, x3, HEAP, lsl #32
    // 0xae03c8: stur            x3, [fp, #-0x18]
    // 0xae03cc: LoadField: r1 = r0->field_37
    //     0xae03cc: ldur            w1, [x0, #0x37]
    // 0xae03d0: DecompressPointer r1
    //     0xae03d0: add             x1, x1, HEAP, lsl #32
    // 0xae03d4: cmp             w1, NULL
    // 0xae03d8: b.ne            #0xae03e4
    // 0xae03dc: r1 = Null
    //     0xae03dc: mov             x1, NULL
    // 0xae03e0: b               #0xae03f0
    // 0xae03e4: LoadField: r4 = r1->field_f
    //     0xae03e4: ldur            w4, [x1, #0xf]
    // 0xae03e8: DecompressPointer r4
    //     0xae03e8: add             x4, x4, HEAP, lsl #32
    // 0xae03ec: mov             x1, x4
    // 0xae03f0: cmp             w1, NULL
    // 0xae03f4: b.ne            #0xae0400
    // 0xae03f8: r4 = ""
    //     0xae03f8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae03fc: b               #0xae0404
    // 0xae0400: mov             x4, x1
    // 0xae0404: stur            x4, [fp, #-0x10]
    // 0xae0408: LoadField: r5 = r0->field_f
    //     0xae0408: ldur            w5, [x0, #0xf]
    // 0xae040c: DecompressPointer r5
    //     0xae040c: add             x5, x5, HEAP, lsl #32
    // 0xae0410: mov             x1, x0
    // 0xae0414: stur            x5, [fp, #-8]
    // 0xae0418: r0 = amountRaised()
    //     0xae0418: bl              #0xadf16c  ; [package:nuonline/app/data/models/campaign.dart] Campaign::amountRaised
    // 0xae041c: ldr             x1, [fp, #0x18]
    // 0xae0420: stur            x0, [fp, #-0x28]
    // 0xae0424: r0 = remainingDay()
    //     0xae0424: bl              #0xadeef0  ; [package:nuonline/app/data/models/campaign.dart] Campaign::remainingDay
    // 0xae0428: mov             x1, x0
    // 0xae042c: ldr             x0, [fp, #0x18]
    // 0xae0430: stur            x1, [fp, #-0x30]
    // 0xae0434: LoadField: r2 = r0->field_27
    //     0xae0434: ldur            w2, [x0, #0x27]
    // 0xae0438: DecompressPointer r2
    //     0xae0438: add             x2, x2, HEAP, lsl #32
    // 0xae043c: LoadField: r3 = r0->field_23
    //     0xae043c: ldur            w3, [x0, #0x23]
    // 0xae0440: DecompressPointer r3
    //     0xae0440: add             x3, x3, HEAP, lsl #32
    // 0xae0444: r0 = 60
    //     0xae0444: movz            x0, #0x3c
    // 0xae0448: branchIfSmi(r2, 0xae0454)
    //     0xae0448: tbz             w2, #0, #0xae0454
    // 0xae044c: r0 = LoadClassIdInstr(r2)
    //     0xae044c: ldur            x0, [x2, #-1]
    //     0xae0450: ubfx            x0, x0, #0xc, #0x14
    // 0xae0454: stp             x3, x2, [SP]
    // 0xae0458: r0 = GDT[cid_x0 + -0xff7]()
    //     0xae0458: sub             lr, x0, #0xff7
    //     0xae045c: ldr             lr, [x21, lr, lsl #3]
    //     0xae0460: blr             lr
    // 0xae0464: stur            x0, [fp, #-0x38]
    // 0xae0468: r0 = NCampaignCard()
    //     0xae0468: bl              #0xae04e4  ; AllocateNCampaignCardStub -> NCampaignCard (size=0x3c)
    // 0xae046c: mov             x3, x0
    // 0xae0470: ldur            x0, [fp, #-0x18]
    // 0xae0474: stur            x3, [fp, #-0x40]
    // 0xae0478: StoreField: r3->field_b = r0
    //     0xae0478: stur            w0, [x3, #0xb]
    // 0xae047c: ldur            x0, [fp, #-0x10]
    // 0xae0480: StoreField: r3->field_f = r0
    //     0xae0480: stur            w0, [x3, #0xf]
    // 0xae0484: ldur            x0, [fp, #-8]
    // 0xae0488: StoreField: r3->field_13 = r0
    //     0xae0488: stur            w0, [x3, #0x13]
    // 0xae048c: ldur            x0, [fp, #-0x28]
    // 0xae0490: StoreField: r3->field_1b = r0
    //     0xae0490: stur            w0, [x3, #0x1b]
    // 0xae0494: ldur            x0, [fp, #-0x30]
    // 0xae0498: StoreField: r3->field_1f = r0
    //     0xae0498: stur            w0, [x3, #0x1f]
    // 0xae049c: ldur            x0, [fp, #-0x38]
    // 0xae04a0: LoadField: d0 = r0->field_7
    //     0xae04a0: ldur            d0, [x0, #7]
    // 0xae04a4: StoreField: r3->field_23 = d0
    //     0xae04a4: stur            d0, [x3, #0x23]
    // 0xae04a8: ldur            x2, [fp, #-0x20]
    // 0xae04ac: r1 = Function '<anonymous closure>':.
    //     0xae04ac: add             x1, PP, #0x30, lsl #12  ; [pp+0x30428] AnonymousClosure: (0xae04f0), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae04b0: ldr             x1, [x1, #0x428]
    // 0xae04b4: r0 = AllocateClosure()
    //     0xae04b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae04b8: mov             x1, x0
    // 0xae04bc: ldur            x0, [fp, #-0x40]
    // 0xae04c0: StoreField: r0->field_2b = r1
    //     0xae04c0: stur            w1, [x0, #0x2b]
    // 0xae04c4: r1 = true
    //     0xae04c4: add             x1, NULL, #0x20  ; true
    // 0xae04c8: StoreField: r0->field_2f = r1
    //     0xae04c8: stur            w1, [x0, #0x2f]
    // 0xae04cc: StoreField: r0->field_33 = r1
    //     0xae04cc: stur            w1, [x0, #0x33]
    // 0xae04d0: LeaveFrame
    //     0xae04d0: mov             SP, fp
    //     0xae04d4: ldp             fp, lr, [SP], #0x10
    // 0xae04d8: ret
    //     0xae04d8: ret             
    // 0xae04dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae04dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae04e0: b               #0xae03a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae04f0, size: 0xc4
    // 0xae04f0: EnterFrame
    //     0xae04f0: stp             fp, lr, [SP, #-0x10]!
    //     0xae04f4: mov             fp, SP
    // 0xae04f8: AllocStack(0x20)
    //     0xae04f8: sub             SP, SP, #0x20
    // 0xae04fc: SetupParameters()
    //     0xae04fc: ldr             x0, [fp, #0x10]
    //     0xae0500: ldur            w1, [x0, #0x17]
    //     0xae0504: add             x1, x1, HEAP, lsl #32
    //     0xae0508: stur            x1, [fp, #-8]
    // 0xae050c: CheckStackOverflow
    //     0xae050c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0510: cmp             SP, x16
    //     0xae0514: b.ls            #0xae05ac
    // 0xae0518: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae0518: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae051c: ldr             x0, [x0, #0x2670]
    //     0xae0520: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae0524: cmp             w0, w16
    //     0xae0528: b.ne            #0xae0534
    //     0xae052c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae0530: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae0534: r1 = Null
    //     0xae0534: mov             x1, NULL
    // 0xae0538: r2 = 4
    //     0xae0538: movz            x2, #0x4
    // 0xae053c: r0 = AllocateArray()
    //     0xae053c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae0540: mov             x2, x0
    // 0xae0544: r16 = "id"
    //     0xae0544: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xae0548: ldr             x16, [x16, #0x740]
    // 0xae054c: StoreField: r2->field_f = r16
    //     0xae054c: stur            w16, [x2, #0xf]
    // 0xae0550: ldur            x0, [fp, #-8]
    // 0xae0554: LoadField: r1 = r0->field_f
    //     0xae0554: ldur            w1, [x0, #0xf]
    // 0xae0558: DecompressPointer r1
    //     0xae0558: add             x1, x1, HEAP, lsl #32
    // 0xae055c: LoadField: r3 = r1->field_7
    //     0xae055c: ldur            x3, [x1, #7]
    // 0xae0560: r0 = BoxInt64Instr(r3)
    //     0xae0560: sbfiz           x0, x3, #1, #0x1f
    //     0xae0564: cmp             x3, x0, asr #1
    //     0xae0568: b.eq            #0xae0574
    //     0xae056c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae0570: stur            x3, [x0, #7]
    // 0xae0574: StoreField: r2->field_13 = r0
    //     0xae0574: stur            w0, [x2, #0x13]
    // 0xae0578: r16 = <String, int>
    //     0xae0578: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xae057c: stp             x2, x16, [SP]
    // 0xae0580: r0 = Map._fromLiteral()
    //     0xae0580: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xae0584: r16 = "/donation/campaign-detail"
    //     0xae0584: add             x16, PP, #0x30, lsl #12  ; [pp+0x30430] "/donation/campaign-detail"
    //     0xae0588: ldr             x16, [x16, #0x430]
    // 0xae058c: stp             x16, NULL, [SP, #8]
    // 0xae0590: str             x0, [SP]
    // 0xae0594: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae0594: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae0598: ldr             x4, [x4, #0x478]
    // 0xae059c: r0 = GetNavigation.toNamed()
    //     0xae059c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae05a0: LeaveFrame
    //     0xae05a0: mov             SP, fp
    //     0xae05a4: ldp             fp, lr, [SP], #0x10
    // 0xae05a8: ret
    //     0xae05a8: ret             
    // 0xae05ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae05ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae05b0: b               #0xae0518
  }
  _ build(/* No info */) {
    // ** addr: 0xae2364, size: 0x760
    // 0xae2364: EnterFrame
    //     0xae2364: stp             fp, lr, [SP, #-0x10]!
    //     0xae2368: mov             fp, SP
    // 0xae236c: AllocStack(0x88)
    //     0xae236c: sub             SP, SP, #0x88
    // 0xae2370: SetupParameters(DonationView this /* r1 => r1, fp-0x8 */)
    //     0xae2370: stur            x1, [fp, #-8]
    // 0xae2374: CheckStackOverflow
    //     0xae2374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2378: cmp             SP, x16
    //     0xae237c: b.ls            #0xae2abc
    // 0xae2380: r0 = AppBar()
    //     0xae2380: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae2384: stur            x0, [fp, #-0x10]
    // 0xae2388: r16 = Instance_Text
    //     0xae2388: add             x16, PP, #0x30, lsl #12  ; [pp+0x302d0] Obj!Text@e21af1
    //     0xae238c: ldr             x16, [x16, #0x2d0]
    // 0xae2390: str             x16, [SP]
    // 0xae2394: mov             x1, x0
    // 0xae2398: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae2398: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae239c: ldr             x4, [x4, #0x6e8]
    // 0xae23a0: r0 = AppBar()
    //     0xae23a0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae23a4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae23a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae23a8: ldr             x0, [x0, #0x2670]
    //     0xae23ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae23b0: cmp             w0, w16
    //     0xae23b4: b.ne            #0xae23c0
    //     0xae23b8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae23bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae23c0: r0 = GetNavigation.width()
    //     0xae23c0: bl              #0x7daa60  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xae23c4: mov             v1.16b, v0.16b
    // 0xae23c8: d0 = 768.000000
    //     0xae23c8: add             x17, PP, #0x2d, lsl #12  ; [pp+0x2d560] IMM: double(768) from 0x4088000000000000
    //     0xae23cc: ldr             d0, [x17, #0x560]
    // 0xae23d0: fcmp            d1, d0
    // 0xae23d4: b.le            #0xae23e0
    // 0xae23d8: r3 = 8
    //     0xae23d8: movz            x3, #0x8
    // 0xae23dc: b               #0xae23e4
    // 0xae23e0: r3 = 4
    //     0xae23e0: movz            x3, #0x4
    // 0xae23e4: stur            x3, [fp, #-0x18]
    // 0xae23e8: r0 = _Menu()
    //     0xae23e8: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae23ec: mov             x3, x0
    // 0xae23f0: r0 = "Infak & Sedekah"
    //     0xae23f0: add             x0, PP, #0x30, lsl #12  ; [pp+0x302d8] "Infak & Sedekah"
    //     0xae23f4: ldr             x0, [x0, #0x2d8]
    // 0xae23f8: stur            x3, [fp, #-0x20]
    // 0xae23fc: StoreField: r3->field_b = r0
    //     0xae23fc: stur            w0, [x3, #0xb]
    // 0xae2400: r0 = "assets/images/feature/donasi.svg"
    //     0xae2400: add             x0, PP, #0x30, lsl #12  ; [pp+0x302e0] "assets/images/feature/donasi.svg"
    //     0xae2404: ldr             x0, [x0, #0x2e0]
    // 0xae2408: StoreField: r3->field_f = r0
    //     0xae2408: stur            w0, [x3, #0xf]
    // 0xae240c: r1 = Function '<anonymous closure>':.
    //     0xae240c: add             x1, PP, #0x30, lsl #12  ; [pp+0x302e8] AnonymousClosure: (0xae347c), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2410: ldr             x1, [x1, #0x2e8]
    // 0xae2414: r2 = Null
    //     0xae2414: mov             x2, NULL
    // 0xae2418: r0 = AllocateClosure()
    //     0xae2418: bl              #0xec1630  ; AllocateClosureStub
    // 0xae241c: mov             x1, x0
    // 0xae2420: ldur            x0, [fp, #-0x20]
    // 0xae2424: StoreField: r0->field_13 = r1
    //     0xae2424: stur            w1, [x0, #0x13]
    // 0xae2428: r0 = _Menu()
    //     0xae2428: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae242c: mov             x3, x0
    // 0xae2430: r0 = "Zakat Mal"
    //     0xae2430: add             x0, PP, #0x30, lsl #12  ; [pp+0x302f0] "Zakat Mal"
    //     0xae2434: ldr             x0, [x0, #0x2f0]
    // 0xae2438: stur            x3, [fp, #-0x28]
    // 0xae243c: StoreField: r3->field_b = r0
    //     0xae243c: stur            w0, [x3, #0xb]
    // 0xae2440: r0 = "assets/images/feature/zakat_mal.svg"
    //     0xae2440: add             x0, PP, #0x30, lsl #12  ; [pp+0x302f8] "assets/images/feature/zakat_mal.svg"
    //     0xae2444: ldr             x0, [x0, #0x2f8]
    // 0xae2448: StoreField: r3->field_f = r0
    //     0xae2448: stur            w0, [x3, #0xf]
    // 0xae244c: r1 = Function '<anonymous closure>':.
    //     0xae244c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30300] AnonymousClosure: (0xae3420), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2450: ldr             x1, [x1, #0x300]
    // 0xae2454: r2 = Null
    //     0xae2454: mov             x2, NULL
    // 0xae2458: r0 = AllocateClosure()
    //     0xae2458: bl              #0xec1630  ; AllocateClosureStub
    // 0xae245c: mov             x1, x0
    // 0xae2460: ldur            x0, [fp, #-0x28]
    // 0xae2464: StoreField: r0->field_13 = r1
    //     0xae2464: stur            w1, [x0, #0x13]
    // 0xae2468: r0 = _Menu()
    //     0xae2468: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae246c: mov             x3, x0
    // 0xae2470: r0 = "Zakat Fitrah & Fidyah"
    //     0xae2470: add             x0, PP, #0x30, lsl #12  ; [pp+0x30308] "Zakat Fitrah & Fidyah"
    //     0xae2474: ldr             x0, [x0, #0x308]
    // 0xae2478: stur            x3, [fp, #-0x30]
    // 0xae247c: StoreField: r3->field_b = r0
    //     0xae247c: stur            w0, [x3, #0xb]
    // 0xae2480: r0 = "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xae2480: add             x0, PP, #0x30, lsl #12  ; [pp+0x30310] "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xae2484: ldr             x0, [x0, #0x310]
    // 0xae2488: StoreField: r3->field_f = r0
    //     0xae2488: stur            w0, [x3, #0xf]
    // 0xae248c: r1 = Function '<anonymous closure>':.
    //     0xae248c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30318] AnonymousClosure: (0xae33c4), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2490: ldr             x1, [x1, #0x318]
    // 0xae2494: r2 = Null
    //     0xae2494: mov             x2, NULL
    // 0xae2498: r0 = AllocateClosure()
    //     0xae2498: bl              #0xec1630  ; AllocateClosureStub
    // 0xae249c: mov             x1, x0
    // 0xae24a0: ldur            x0, [fp, #-0x30]
    // 0xae24a4: StoreField: r0->field_13 = r1
    //     0xae24a4: stur            w1, [x0, #0x13]
    // 0xae24a8: r0 = _Menu()
    //     0xae24a8: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae24ac: mov             x3, x0
    // 0xae24b0: r0 = "Qurban"
    //     0xae24b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f5e8] "Qurban"
    //     0xae24b4: ldr             x0, [x0, #0x5e8]
    // 0xae24b8: stur            x3, [fp, #-0x38]
    // 0xae24bc: StoreField: r3->field_b = r0
    //     0xae24bc: stur            w0, [x3, #0xb]
    // 0xae24c0: r0 = "assets/images/feature/qurban.svg"
    //     0xae24c0: add             x0, PP, #0x30, lsl #12  ; [pp+0x30320] "assets/images/feature/qurban.svg"
    //     0xae24c4: ldr             x0, [x0, #0x320]
    // 0xae24c8: StoreField: r3->field_f = r0
    //     0xae24c8: stur            w0, [x3, #0xf]
    // 0xae24cc: r1 = Function '<anonymous closure>':.
    //     0xae24cc: add             x1, PP, #0x30, lsl #12  ; [pp+0x30328] AnonymousClosure: (0xae3368), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae24d0: ldr             x1, [x1, #0x328]
    // 0xae24d4: r2 = Null
    //     0xae24d4: mov             x2, NULL
    // 0xae24d8: r0 = AllocateClosure()
    //     0xae24d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae24dc: mov             x1, x0
    // 0xae24e0: ldur            x0, [fp, #-0x38]
    // 0xae24e4: StoreField: r0->field_13 = r1
    //     0xae24e4: stur            w1, [x0, #0x13]
    // 0xae24e8: r0 = _Menu()
    //     0xae24e8: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae24ec: mov             x3, x0
    // 0xae24f0: r0 = "Koin NU"
    //     0xae24f0: add             x0, PP, #0x30, lsl #12  ; [pp+0x30330] "Koin NU"
    //     0xae24f4: ldr             x0, [x0, #0x330]
    // 0xae24f8: stur            x3, [fp, #-0x40]
    // 0xae24fc: StoreField: r3->field_b = r0
    //     0xae24fc: stur            w0, [x3, #0xb]
    // 0xae2500: r0 = "assets/images/feature/koin_nu.svg"
    //     0xae2500: add             x0, PP, #0x30, lsl #12  ; [pp+0x30338] "assets/images/feature/koin_nu.svg"
    //     0xae2504: ldr             x0, [x0, #0x338]
    // 0xae2508: StoreField: r3->field_f = r0
    //     0xae2508: stur            w0, [x3, #0xf]
    // 0xae250c: r1 = Function '<anonymous closure>':.
    //     0xae250c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30340] AnonymousClosure: (0xae330c), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2510: ldr             x1, [x1, #0x340]
    // 0xae2514: r2 = Null
    //     0xae2514: mov             x2, NULL
    // 0xae2518: r0 = AllocateClosure()
    //     0xae2518: bl              #0xec1630  ; AllocateClosureStub
    // 0xae251c: mov             x1, x0
    // 0xae2520: ldur            x0, [fp, #-0x40]
    // 0xae2524: StoreField: r0->field_13 = r1
    //     0xae2524: stur            w1, [x0, #0x13]
    // 0xae2528: r0 = _Menu()
    //     0xae2528: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae252c: mov             x3, x0
    // 0xae2530: r0 = "Galang Dana"
    //     0xae2530: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xae2534: ldr             x0, [x0, #0x348]
    // 0xae2538: stur            x3, [fp, #-0x48]
    // 0xae253c: StoreField: r3->field_b = r0
    //     0xae253c: stur            w0, [x3, #0xb]
    // 0xae2540: r1 = "assets/images/feature/galang_dana.svg"
    //     0xae2540: add             x1, PP, #0x30, lsl #12  ; [pp+0x30350] "assets/images/feature/galang_dana.svg"
    //     0xae2544: ldr             x1, [x1, #0x350]
    // 0xae2548: StoreField: r3->field_f = r1
    //     0xae2548: stur            w1, [x3, #0xf]
    // 0xae254c: r1 = Function '<anonymous closure>':.
    //     0xae254c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30358] AnonymousClosure: (0xae3088), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2550: ldr             x1, [x1, #0x358]
    // 0xae2554: r2 = Null
    //     0xae2554: mov             x2, NULL
    // 0xae2558: r0 = AllocateClosure()
    //     0xae2558: bl              #0xec1630  ; AllocateClosureStub
    // 0xae255c: mov             x1, x0
    // 0xae2560: ldur            x0, [fp, #-0x48]
    // 0xae2564: StoreField: r0->field_13 = r1
    //     0xae2564: stur            w1, [x0, #0x13]
    // 0xae2568: r0 = _Menu()
    //     0xae2568: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae256c: mov             x3, x0
    // 0xae2570: r0 = "Kalkulator Zakat"
    //     0xae2570: add             x0, PP, #0xf, lsl #12  ; [pp+0xfce8] "Kalkulator Zakat"
    //     0xae2574: ldr             x0, [x0, #0xce8]
    // 0xae2578: stur            x3, [fp, #-0x50]
    // 0xae257c: StoreField: r3->field_b = r0
    //     0xae257c: stur            w0, [x3, #0xb]
    // 0xae2580: r0 = "assets/images/feature/zakat.svg"
    //     0xae2580: add             x0, PP, #0x30, lsl #12  ; [pp+0x30360] "assets/images/feature/zakat.svg"
    //     0xae2584: ldr             x0, [x0, #0x360]
    // 0xae2588: StoreField: r3->field_f = r0
    //     0xae2588: stur            w0, [x3, #0xf]
    // 0xae258c: r1 = Function '<anonymous closure>':.
    //     0xae258c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30368] AnonymousClosure: (0xae32b0), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2590: ldr             x1, [x1, #0x368]
    // 0xae2594: r2 = Null
    //     0xae2594: mov             x2, NULL
    // 0xae2598: r0 = AllocateClosure()
    //     0xae2598: bl              #0xec1630  ; AllocateClosureStub
    // 0xae259c: mov             x1, x0
    // 0xae25a0: ldur            x0, [fp, #-0x50]
    // 0xae25a4: StoreField: r0->field_13 = r1
    //     0xae25a4: stur            w1, [x0, #0x13]
    // 0xae25a8: ldur            x1, [fp, #-8]
    // 0xae25ac: r0 = controller()
    //     0xae25ac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae25b0: r1 = Function '<anonymous closure>':.
    //     0xae25b0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30370] AnonymousClosure: (0xae3140), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae25b4: ldr             x1, [x1, #0x370]
    // 0xae25b8: r2 = Null
    //     0xae25b8: mov             x2, NULL
    // 0xae25bc: stur            x0, [fp, #-8]
    // 0xae25c0: r0 = AllocateClosure()
    //     0xae25c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xae25c4: r1 = Function '<anonymous closure>':.
    //     0xae25c4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30378] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xae25c8: ldr             x1, [x1, #0x378]
    // 0xae25cc: r2 = Null
    //     0xae25cc: mov             x2, NULL
    // 0xae25d0: stur            x0, [fp, #-0x58]
    // 0xae25d4: r0 = AllocateClosure()
    //     0xae25d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae25d8: r16 = <List<Transaction>>
    //     0xae25d8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b090] TypeArguments: <List<Transaction>>
    //     0xae25dc: ldr             x16, [x16, #0x90]
    // 0xae25e0: ldur            lr, [fp, #-8]
    // 0xae25e4: stp             lr, x16, [SP, #0x20]
    // 0xae25e8: ldur            x16, [fp, #-0x58]
    // 0xae25ec: r30 = Instance_SizedBox
    //     0xae25ec: add             lr, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae25f0: ldr             lr, [lr, #0xc40]
    // 0xae25f4: stp             lr, x16, [SP, #0x10]
    // 0xae25f8: r16 = Instance_SizedBox
    //     0xae25f8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae25fc: ldr             x16, [x16, #0xc40]
    // 0xae2600: stp             x0, x16, [SP]
    // 0xae2604: r4 = const [0x1, 0x5, 0x5, 0x2, empty, 0x3, error, 0x4, loading, 0x2, null]
    //     0xae2604: add             x4, PP, #0x30, lsl #12  ; [pp+0x30380] List(11) [0x1, 0x5, 0x5, 0x2, "empty", 0x3, "error", 0x4, "loading", 0x2, Null]
    //     0xae2608: ldr             x4, [x4, #0x380]
    // 0xae260c: r0 = StateMixinExt.when()
    //     0xae260c: bl              #0xae2d2c  ; [package:nuonline/common/extensions/state_extension.dart] ::StateMixinExt.when
    // 0xae2610: stur            x0, [fp, #-8]
    // 0xae2614: r0 = _Menu()
    //     0xae2614: bl              #0xae307c  ; Allocate_MenuStub -> _Menu (size=0x1c)
    // 0xae2618: mov             x3, x0
    // 0xae261c: r0 = "Riwayat Pembayaran"
    //     0xae261c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30388] "Riwayat Pembayaran"
    //     0xae2620: ldr             x0, [x0, #0x388]
    // 0xae2624: stur            x3, [fp, #-0x58]
    // 0xae2628: StoreField: r3->field_b = r0
    //     0xae2628: stur            w0, [x3, #0xb]
    // 0xae262c: r0 = "assets/images/feature/payment_history.svg"
    //     0xae262c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30390] "assets/images/feature/payment_history.svg"
    //     0xae2630: ldr             x0, [x0, #0x390]
    // 0xae2634: StoreField: r3->field_f = r0
    //     0xae2634: stur            w0, [x3, #0xf]
    // 0xae2638: r1 = Function '<anonymous closure>':.
    //     0xae2638: add             x1, PP, #0x30, lsl #12  ; [pp+0x30398] AnonymousClosure: (0xae30e4), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae263c: ldr             x1, [x1, #0x398]
    // 0xae2640: r2 = Null
    //     0xae2640: mov             x2, NULL
    // 0xae2644: r0 = AllocateClosure()
    //     0xae2644: bl              #0xec1630  ; AllocateClosureStub
    // 0xae2648: mov             x1, x0
    // 0xae264c: ldur            x0, [fp, #-0x58]
    // 0xae2650: StoreField: r0->field_13 = r1
    //     0xae2650: stur            w1, [x0, #0x13]
    // 0xae2654: ldur            x1, [fp, #-8]
    // 0xae2658: ArrayStore: r0[0] = r1  ; List_4
    //     0xae2658: stur            w1, [x0, #0x17]
    // 0xae265c: r1 = Null
    //     0xae265c: mov             x1, NULL
    // 0xae2660: r2 = 16
    //     0xae2660: movz            x2, #0x10
    // 0xae2664: r0 = AllocateArray()
    //     0xae2664: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae2668: mov             x2, x0
    // 0xae266c: ldur            x0, [fp, #-0x20]
    // 0xae2670: stur            x2, [fp, #-8]
    // 0xae2674: StoreField: r2->field_f = r0
    //     0xae2674: stur            w0, [x2, #0xf]
    // 0xae2678: ldur            x0, [fp, #-0x28]
    // 0xae267c: StoreField: r2->field_13 = r0
    //     0xae267c: stur            w0, [x2, #0x13]
    // 0xae2680: ldur            x0, [fp, #-0x30]
    // 0xae2684: ArrayStore: r2[0] = r0  ; List_4
    //     0xae2684: stur            w0, [x2, #0x17]
    // 0xae2688: ldur            x0, [fp, #-0x38]
    // 0xae268c: StoreField: r2->field_1b = r0
    //     0xae268c: stur            w0, [x2, #0x1b]
    // 0xae2690: ldur            x0, [fp, #-0x40]
    // 0xae2694: StoreField: r2->field_1f = r0
    //     0xae2694: stur            w0, [x2, #0x1f]
    // 0xae2698: ldur            x0, [fp, #-0x48]
    // 0xae269c: StoreField: r2->field_23 = r0
    //     0xae269c: stur            w0, [x2, #0x23]
    // 0xae26a0: ldur            x0, [fp, #-0x50]
    // 0xae26a4: StoreField: r2->field_27 = r0
    //     0xae26a4: stur            w0, [x2, #0x27]
    // 0xae26a8: ldur            x0, [fp, #-0x58]
    // 0xae26ac: StoreField: r2->field_2b = r0
    //     0xae26ac: stur            w0, [x2, #0x2b]
    // 0xae26b0: r1 = <Widget>
    //     0xae26b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae26b4: r0 = AllocateGrowableArray()
    //     0xae26b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae26b8: mov             x1, x0
    // 0xae26bc: ldur            x0, [fp, #-8]
    // 0xae26c0: stur            x1, [fp, #-0x20]
    // 0xae26c4: StoreField: r1->field_f = r0
    //     0xae26c4: stur            w0, [x1, #0xf]
    // 0xae26c8: r0 = 16
    //     0xae26c8: movz            x0, #0x10
    // 0xae26cc: StoreField: r1->field_b = r0
    //     0xae26cc: stur            w0, [x1, #0xb]
    // 0xae26d0: r0 = GridView()
    //     0xae26d0: bl              #0xad81c0  ; AllocateGridViewStub -> GridView (size=0x5c)
    // 0xae26d4: stur            x0, [fp, #-8]
    // 0xae26d8: r16 = Instance_EdgeInsets
    //     0xae26d8: add             x16, PP, #0x30, lsl #12  ; [pp+0x303a0] Obj!EdgeInsets@e127c1
    //     0xae26dc: ldr             x16, [x16, #0x3a0]
    // 0xae26e0: r30 = 8.000000
    //     0xae26e0: add             lr, PP, #0x30, lsl #12  ; [pp+0x303a8] 8
    //     0xae26e4: ldr             lr, [lr, #0x3a8]
    // 0xae26e8: stp             lr, x16, [SP, #8]
    // 0xae26ec: r16 = 8.000000
    //     0xae26ec: add             x16, PP, #0x30, lsl #12  ; [pp+0x303a8] 8
    //     0xae26f0: ldr             x16, [x16, #0x3a8]
    // 0xae26f4: str             x16, [SP]
    // 0xae26f8: mov             x1, x0
    // 0xae26fc: ldur            x2, [fp, #-0x20]
    // 0xae2700: ldur            x3, [fp, #-0x18]
    // 0xae2704: d0 = 1.030488
    //     0xae2704: add             x17, PP, #0x30, lsl #12  ; [pp+0x303b0] IMM: double(1.0304878048780488) from 0x3ff07ce0c7ce0c7d
    //     0xae2708: ldr             d0, [x17, #0x3b0]
    // 0xae270c: r5 = Instance_NeverScrollableScrollPhysics
    //     0xae270c: add             x5, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xae2710: ldr             x5, [x5, #0x290]
    // 0xae2714: r4 = const [0, 0x8, 0x3, 0x5, crossAxisSpacing, 0x6, mainAxisSpacing, 0x7, padding, 0x5, null]
    //     0xae2714: add             x4, PP, #0x30, lsl #12  ; [pp+0x303b8] List(11) [0, 0x8, 0x3, 0x5, "crossAxisSpacing", 0x6, "mainAxisSpacing", 0x7, "padding", 0x5, Null]
    //     0xae2718: ldr             x4, [x4, #0x3b8]
    // 0xae271c: r0 = GridView.count()
    //     0xae271c: bl              #0xae2ac4  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.count
    // 0xae2720: r16 = <DonationRepository>
    //     0xae2720: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xae2724: ldr             x16, [x16, #0xb0]
    // 0xae2728: r30 = "donation_repo"
    //     0xae2728: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xae272c: ldr             lr, [lr, #0xb8]
    // 0xae2730: stp             lr, x16, [SP]
    // 0xae2734: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae2734: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae2738: r0 = Inst.find()
    //     0xae2738: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae273c: r1 = <List<Campaign>, Campaign>
    //     0xae273c: add             x1, PP, #0x30, lsl #12  ; [pp+0x303c0] TypeArguments: <List<Campaign>, Campaign>
    //     0xae2740: ldr             x1, [x1, #0x3c0]
    // 0xae2744: stur            x0, [fp, #-0x20]
    // 0xae2748: r0 = CampaignListController()
    //     0xae2748: bl              #0x81089c  ; AllocateCampaignListControllerStub -> CampaignListController (size=0x40)
    // 0xae274c: stur            x0, [fp, #-0x28]
    // 0xae2750: r16 = true
    //     0xae2750: add             x16, NULL, #0x20  ; true
    // 0xae2754: str             x16, [SP]
    // 0xae2758: mov             x1, x0
    // 0xae275c: ldur            x2, [fp, #-0x20]
    // 0xae2760: r4 = const [0, 0x3, 0x1, 0x2, isSummary, 0x2, null]
    //     0xae2760: add             x4, PP, #0x30, lsl #12  ; [pp+0x303c8] List(7) [0, 0x3, 0x1, 0x2, "isSummary", 0x2, Null]
    //     0xae2764: ldr             x4, [x4, #0x3c8]
    // 0xae2768: r0 = CampaignListController()
    //     0xae2768: bl              #0x810750  ; [package:nuonline/app/modules/donation/controllers/campaign_list_controller.dart] CampaignListController::CampaignListController
    // 0xae276c: r1 = <CampaignListController>
    //     0xae276c: add             x1, PP, #0x24, lsl #12  ; [pp+0x248e8] TypeArguments: <CampaignListController>
    //     0xae2770: ldr             x1, [x1, #0x8e8]
    // 0xae2774: r0 = GetBuilder()
    //     0xae2774: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae2778: mov             x3, x0
    // 0xae277c: ldur            x0, [fp, #-0x28]
    // 0xae2780: stur            x3, [fp, #-0x20]
    // 0xae2784: StoreField: r3->field_3b = r0
    //     0xae2784: stur            w0, [x3, #0x3b]
    // 0xae2788: r0 = true
    //     0xae2788: add             x0, NULL, #0x20  ; true
    // 0xae278c: StoreField: r3->field_13 = r0
    //     0xae278c: stur            w0, [x3, #0x13]
    // 0xae2790: r1 = Function '<anonymous closure>':.
    //     0xae2790: add             x1, PP, #0x30, lsl #12  ; [pp+0x303d0] AnonymousClosure: (0xae02f8), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae2794: ldr             x1, [x1, #0x3d0]
    // 0xae2798: r2 = Null
    //     0xae2798: mov             x2, NULL
    // 0xae279c: r0 = AllocateClosure()
    //     0xae279c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae27a0: mov             x1, x0
    // 0xae27a4: ldur            x0, [fp, #-0x20]
    // 0xae27a8: StoreField: r0->field_f = r1
    //     0xae27a8: stur            w1, [x0, #0xf]
    // 0xae27ac: r1 = true
    //     0xae27ac: add             x1, NULL, #0x20  ; true
    // 0xae27b0: StoreField: r0->field_1f = r1
    //     0xae27b0: stur            w1, [x0, #0x1f]
    // 0xae27b4: r2 = false
    //     0xae27b4: add             x2, NULL, #0x30  ; false
    // 0xae27b8: StoreField: r0->field_23 = r2
    //     0xae27b8: stur            w2, [x0, #0x23]
    // 0xae27bc: r3 = "campaign_list_summary"
    //     0xae27bc: add             x3, PP, #0x30, lsl #12  ; [pp+0x303d8] "campaign_list_summary"
    //     0xae27c0: ldr             x3, [x3, #0x3d8]
    // 0xae27c4: StoreField: r0->field_1b = r3
    //     0xae27c4: stur            w3, [x0, #0x1b]
    // 0xae27c8: r0 = GetNavigation.textTheme()
    //     0xae27c8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae27cc: LoadField: r1 = r0->field_23
    //     0xae27cc: ldur            w1, [x0, #0x23]
    // 0xae27d0: DecompressPointer r1
    //     0xae27d0: add             x1, x1, HEAP, lsl #32
    // 0xae27d4: stur            x1, [fp, #-0x28]
    // 0xae27d8: cmp             w1, NULL
    // 0xae27dc: b.ne            #0xae27e8
    // 0xae27e0: r3 = Null
    //     0xae27e0: mov             x3, NULL
    // 0xae27e4: b               #0xae281c
    // 0xae27e8: r0 = GetNavigation.theme()
    //     0xae27e8: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xae27ec: LoadField: r1 = r0->field_3f
    //     0xae27ec: ldur            w1, [x0, #0x3f]
    // 0xae27f0: DecompressPointer r1
    //     0xae27f0: add             x1, x1, HEAP, lsl #32
    // 0xae27f4: LoadField: r0 = r1->field_b
    //     0xae27f4: ldur            w0, [x1, #0xb]
    // 0xae27f8: DecompressPointer r0
    //     0xae27f8: add             x0, x0, HEAP, lsl #32
    // 0xae27fc: r16 = Instance_FontWeight
    //     0xae27fc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xae2800: ldr             x16, [x16, #0xe20]
    // 0xae2804: stp             x16, x0, [SP]
    // 0xae2808: ldur            x1, [fp, #-0x28]
    // 0xae280c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontWeight, 0x2, null]
    //     0xae280c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f668] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontWeight", 0x2, Null]
    //     0xae2810: ldr             x4, [x4, #0x668]
    // 0xae2814: r0 = copyWith()
    //     0xae2814: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae2818: mov             x3, x0
    // 0xae281c: ldur            x2, [fp, #-0x10]
    // 0xae2820: ldur            x1, [fp, #-8]
    // 0xae2824: ldur            x0, [fp, #-0x20]
    // 0xae2828: stur            x3, [fp, #-0x28]
    // 0xae282c: r0 = Text()
    //     0xae282c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae2830: mov             x1, x0
    // 0xae2834: r0 = "Lihat Selengkapnya"
    //     0xae2834: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c908] "Lihat Selengkapnya"
    //     0xae2838: ldr             x0, [x0, #0x908]
    // 0xae283c: stur            x1, [fp, #-0x30]
    // 0xae2840: StoreField: r1->field_b = r0
    //     0xae2840: stur            w0, [x1, #0xb]
    // 0xae2844: ldur            x0, [fp, #-0x28]
    // 0xae2848: StoreField: r1->field_13 = r0
    //     0xae2848: stur            w0, [x1, #0x13]
    // 0xae284c: r0 = Instance_TextAlign
    //     0xae284c: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xae2850: StoreField: r1->field_1b = r0
    //     0xae2850: stur            w0, [x1, #0x1b]
    // 0xae2854: r0 = InkWell()
    //     0xae2854: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xae2858: mov             x3, x0
    // 0xae285c: ldur            x0, [fp, #-0x30]
    // 0xae2860: stur            x3, [fp, #-0x28]
    // 0xae2864: StoreField: r3->field_b = r0
    //     0xae2864: stur            w0, [x3, #0xb]
    // 0xae2868: r1 = Function '<anonymous closure>':.
    //     0xae2868: add             x1, PP, #0x30, lsl #12  ; [pp+0x303e0] AnonymousClosure: (0xae3088), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae286c: ldr             x1, [x1, #0x3e0]
    // 0xae2870: r2 = Null
    //     0xae2870: mov             x2, NULL
    // 0xae2874: r0 = AllocateClosure()
    //     0xae2874: bl              #0xec1630  ; AllocateClosureStub
    // 0xae2878: mov             x1, x0
    // 0xae287c: ldur            x0, [fp, #-0x28]
    // 0xae2880: StoreField: r0->field_f = r1
    //     0xae2880: stur            w1, [x0, #0xf]
    // 0xae2884: r3 = true
    //     0xae2884: add             x3, NULL, #0x20  ; true
    // 0xae2888: StoreField: r0->field_43 = r3
    //     0xae2888: stur            w3, [x0, #0x43]
    // 0xae288c: r1 = Instance_BoxShape
    //     0xae288c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xae2890: ldr             x1, [x1, #0xca8]
    // 0xae2894: StoreField: r0->field_47 = r1
    //     0xae2894: stur            w1, [x0, #0x47]
    // 0xae2898: StoreField: r0->field_6f = r3
    //     0xae2898: stur            w3, [x0, #0x6f]
    // 0xae289c: r4 = false
    //     0xae289c: add             x4, NULL, #0x30  ; false
    // 0xae28a0: StoreField: r0->field_73 = r4
    //     0xae28a0: stur            w4, [x0, #0x73]
    // 0xae28a4: StoreField: r0->field_83 = r3
    //     0xae28a4: stur            w3, [x0, #0x83]
    // 0xae28a8: StoreField: r0->field_7b = r4
    //     0xae28a8: stur            w4, [x0, #0x7b]
    // 0xae28ac: r1 = Null
    //     0xae28ac: mov             x1, NULL
    // 0xae28b0: r2 = 2
    //     0xae28b0: movz            x2, #0x2
    // 0xae28b4: r0 = AllocateArray()
    //     0xae28b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae28b8: mov             x2, x0
    // 0xae28bc: ldur            x0, [fp, #-0x28]
    // 0xae28c0: stur            x2, [fp, #-0x30]
    // 0xae28c4: StoreField: r2->field_f = r0
    //     0xae28c4: stur            w0, [x2, #0xf]
    // 0xae28c8: r1 = <Widget>
    //     0xae28c8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae28cc: r0 = AllocateGrowableArray()
    //     0xae28cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae28d0: mov             x1, x0
    // 0xae28d4: ldur            x0, [fp, #-0x30]
    // 0xae28d8: stur            x1, [fp, #-0x28]
    // 0xae28dc: StoreField: r1->field_f = r0
    //     0xae28dc: stur            w0, [x1, #0xf]
    // 0xae28e0: r0 = 2
    //     0xae28e0: movz            x0, #0x2
    // 0xae28e4: StoreField: r1->field_b = r0
    //     0xae28e4: stur            w0, [x1, #0xb]
    // 0xae28e8: r0 = Row()
    //     0xae28e8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae28ec: mov             x3, x0
    // 0xae28f0: r0 = Instance_Axis
    //     0xae28f0: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xae28f4: stur            x3, [fp, #-0x30]
    // 0xae28f8: StoreField: r3->field_f = r0
    //     0xae28f8: stur            w0, [x3, #0xf]
    // 0xae28fc: r0 = Instance_MainAxisAlignment
    //     0xae28fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xae2900: ldr             x0, [x0, #0x290]
    // 0xae2904: StoreField: r3->field_13 = r0
    //     0xae2904: stur            w0, [x3, #0x13]
    // 0xae2908: r0 = Instance_MainAxisSize
    //     0xae2908: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae290c: ldr             x0, [x0, #0x738]
    // 0xae2910: ArrayStore: r3[0] = r0  ; List_4
    //     0xae2910: stur            w0, [x3, #0x17]
    // 0xae2914: r0 = Instance_CrossAxisAlignment
    //     0xae2914: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae2918: ldr             x0, [x0, #0x740]
    // 0xae291c: StoreField: r3->field_1b = r0
    //     0xae291c: stur            w0, [x3, #0x1b]
    // 0xae2920: r0 = Instance_VerticalDirection
    //     0xae2920: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae2924: ldr             x0, [x0, #0x748]
    // 0xae2928: StoreField: r3->field_23 = r0
    //     0xae2928: stur            w0, [x3, #0x23]
    // 0xae292c: r0 = Instance_Clip
    //     0xae292c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae2930: ldr             x0, [x0, #0x750]
    // 0xae2934: StoreField: r3->field_2b = r0
    //     0xae2934: stur            w0, [x3, #0x2b]
    // 0xae2938: StoreField: r3->field_2f = rZR
    //     0xae2938: stur            xzr, [x3, #0x2f]
    // 0xae293c: ldur            x0, [fp, #-0x28]
    // 0xae2940: StoreField: r3->field_b = r0
    //     0xae2940: stur            w0, [x3, #0xb]
    // 0xae2944: r1 = Null
    //     0xae2944: mov             x1, NULL
    // 0xae2948: r2 = 8
    //     0xae2948: movz            x2, #0x8
    // 0xae294c: r0 = AllocateArray()
    //     0xae294c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae2950: mov             x2, x0
    // 0xae2954: ldur            x0, [fp, #-0x20]
    // 0xae2958: stur            x2, [fp, #-0x28]
    // 0xae295c: StoreField: r2->field_f = r0
    //     0xae295c: stur            w0, [x2, #0xf]
    // 0xae2960: r16 = Instance_Divider
    //     0xae2960: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xae2964: ldr             x16, [x16, #0xc28]
    // 0xae2968: StoreField: r2->field_13 = r16
    //     0xae2968: stur            w16, [x2, #0x13]
    // 0xae296c: r16 = Instance_SizedBox
    //     0xae296c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae2970: ldr             x16, [x16, #0xfe8]
    // 0xae2974: ArrayStore: r2[0] = r16  ; List_4
    //     0xae2974: stur            w16, [x2, #0x17]
    // 0xae2978: ldur            x0, [fp, #-0x30]
    // 0xae297c: StoreField: r2->field_1b = r0
    //     0xae297c: stur            w0, [x2, #0x1b]
    // 0xae2980: r1 = <Widget>
    //     0xae2980: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae2984: r0 = AllocateGrowableArray()
    //     0xae2984: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae2988: mov             x1, x0
    // 0xae298c: ldur            x0, [fp, #-0x28]
    // 0xae2990: stur            x1, [fp, #-0x20]
    // 0xae2994: StoreField: r1->field_f = r0
    //     0xae2994: stur            w0, [x1, #0xf]
    // 0xae2998: r0 = 8
    //     0xae2998: movz            x0, #0x8
    // 0xae299c: StoreField: r1->field_b = r0
    //     0xae299c: stur            w0, [x1, #0xb]
    // 0xae29a0: r0 = NSection()
    //     0xae29a0: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae29a4: mov             x3, x0
    // 0xae29a8: r0 = "Galang Dana"
    //     0xae29a8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30348] "Galang Dana"
    //     0xae29ac: ldr             x0, [x0, #0x348]
    // 0xae29b0: stur            x3, [fp, #-0x28]
    // 0xae29b4: StoreField: r3->field_b = r0
    //     0xae29b4: stur            w0, [x3, #0xb]
    // 0xae29b8: ldur            x0, [fp, #-0x20]
    // 0xae29bc: StoreField: r3->field_f = r0
    //     0xae29bc: stur            w0, [x3, #0xf]
    // 0xae29c0: r0 = true
    //     0xae29c0: add             x0, NULL, #0x20  ; true
    // 0xae29c4: StoreField: r3->field_27 = r0
    //     0xae29c4: stur            w0, [x3, #0x27]
    // 0xae29c8: StoreField: r3->field_2b = r0
    //     0xae29c8: stur            w0, [x3, #0x2b]
    // 0xae29cc: r1 = Instance_Icon
    //     0xae29cc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cfd8] Obj!Icon@e23fb1
    //     0xae29d0: ldr             x1, [x1, #0xfd8]
    // 0xae29d4: StoreField: r3->field_2f = r1
    //     0xae29d4: stur            w1, [x3, #0x2f]
    // 0xae29d8: r1 = Function '<anonymous closure>':.
    //     0xae29d8: add             x1, PP, #0x30, lsl #12  ; [pp+0x303e8] AnonymousClosure: (0xae3088), in [package:nuonline/app/modules/donation/views/donation_view.dart] DonationView::build (0xae2364)
    //     0xae29dc: ldr             x1, [x1, #0x3e8]
    // 0xae29e0: r2 = Null
    //     0xae29e0: mov             x2, NULL
    // 0xae29e4: r0 = AllocateClosure()
    //     0xae29e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae29e8: mov             x1, x0
    // 0xae29ec: ldur            x0, [fp, #-0x28]
    // 0xae29f0: StoreField: r0->field_33 = r1
    //     0xae29f0: stur            w1, [x0, #0x33]
    // 0xae29f4: r1 = Null
    //     0xae29f4: mov             x1, NULL
    // 0xae29f8: r2 = 6
    //     0xae29f8: movz            x2, #0x6
    // 0xae29fc: r0 = AllocateArray()
    //     0xae29fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae2a00: mov             x2, x0
    // 0xae2a04: ldur            x0, [fp, #-8]
    // 0xae2a08: stur            x2, [fp, #-0x20]
    // 0xae2a0c: StoreField: r2->field_f = r0
    //     0xae2a0c: stur            w0, [x2, #0xf]
    // 0xae2a10: r16 = Instance_NSectionDivider
    //     0xae2a10: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xae2a14: ldr             x16, [x16, #0x38]
    // 0xae2a18: StoreField: r2->field_13 = r16
    //     0xae2a18: stur            w16, [x2, #0x13]
    // 0xae2a1c: ldur            x0, [fp, #-0x28]
    // 0xae2a20: ArrayStore: r2[0] = r0  ; List_4
    //     0xae2a20: stur            w0, [x2, #0x17]
    // 0xae2a24: r1 = <Widget>
    //     0xae2a24: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae2a28: r0 = AllocateGrowableArray()
    //     0xae2a28: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae2a2c: mov             x1, x0
    // 0xae2a30: ldur            x0, [fp, #-0x20]
    // 0xae2a34: stur            x1, [fp, #-8]
    // 0xae2a38: StoreField: r1->field_f = r0
    //     0xae2a38: stur            w0, [x1, #0xf]
    // 0xae2a3c: r0 = 6
    //     0xae2a3c: movz            x0, #0x6
    // 0xae2a40: StoreField: r1->field_b = r0
    //     0xae2a40: stur            w0, [x1, #0xb]
    // 0xae2a44: r0 = ListView()
    //     0xae2a44: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae2a48: stur            x0, [fp, #-0x20]
    // 0xae2a4c: r16 = Instance_ClampingScrollPhysics
    //     0xae2a4c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xae2a50: ldr             x16, [x16, #0x410]
    // 0xae2a54: str             x16, [SP]
    // 0xae2a58: mov             x1, x0
    // 0xae2a5c: ldur            x2, [fp, #-8]
    // 0xae2a60: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xae2a60: add             x4, PP, #0x28, lsl #12  ; [pp+0x28418] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xae2a64: ldr             x4, [x4, #0x418]
    // 0xae2a68: r0 = ListView()
    //     0xae2a68: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xae2a6c: r0 = Scaffold()
    //     0xae2a6c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae2a70: ldur            x1, [fp, #-0x10]
    // 0xae2a74: StoreField: r0->field_13 = r1
    //     0xae2a74: stur            w1, [x0, #0x13]
    // 0xae2a78: ldur            x1, [fp, #-0x20]
    // 0xae2a7c: ArrayStore: r0[0] = r1  ; List_4
    //     0xae2a7c: stur            w1, [x0, #0x17]
    // 0xae2a80: r1 = Instance_AlignmentDirectional
    //     0xae2a80: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae2a84: ldr             x1, [x1, #0x758]
    // 0xae2a88: StoreField: r0->field_2b = r1
    //     0xae2a88: stur            w1, [x0, #0x2b]
    // 0xae2a8c: r1 = true
    //     0xae2a8c: add             x1, NULL, #0x20  ; true
    // 0xae2a90: StoreField: r0->field_53 = r1
    //     0xae2a90: stur            w1, [x0, #0x53]
    // 0xae2a94: r2 = Instance_DragStartBehavior
    //     0xae2a94: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae2a98: StoreField: r0->field_57 = r2
    //     0xae2a98: stur            w2, [x0, #0x57]
    // 0xae2a9c: r2 = false
    //     0xae2a9c: add             x2, NULL, #0x30  ; false
    // 0xae2aa0: StoreField: r0->field_b = r2
    //     0xae2aa0: stur            w2, [x0, #0xb]
    // 0xae2aa4: StoreField: r0->field_f = r2
    //     0xae2aa4: stur            w2, [x0, #0xf]
    // 0xae2aa8: StoreField: r0->field_5f = r1
    //     0xae2aa8: stur            w1, [x0, #0x5f]
    // 0xae2aac: StoreField: r0->field_63 = r1
    //     0xae2aac: stur            w1, [x0, #0x63]
    // 0xae2ab0: LeaveFrame
    //     0xae2ab0: mov             SP, fp
    //     0xae2ab4: ldp             fp, lr, [SP], #0x10
    // 0xae2ab8: ret
    //     0xae2ab8: ret             
    // 0xae2abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2ac0: b               #0xae2380
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3088, size: 0x5c
    // 0xae3088: EnterFrame
    //     0xae3088: stp             fp, lr, [SP, #-0x10]!
    //     0xae308c: mov             fp, SP
    // 0xae3090: AllocStack(0x10)
    //     0xae3090: sub             SP, SP, #0x10
    // 0xae3094: CheckStackOverflow
    //     0xae3094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3098: cmp             SP, x16
    //     0xae309c: b.ls            #0xae30dc
    // 0xae30a0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae30a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae30a4: ldr             x0, [x0, #0x2670]
    //     0xae30a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae30ac: cmp             w0, w16
    //     0xae30b0: b.ne            #0xae30bc
    //     0xae30b4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae30b8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae30bc: r16 = "/donation/campaign-list"
    //     0xae30bc: add             x16, PP, #0x30, lsl #12  ; [pp+0x303f0] "/donation/campaign-list"
    //     0xae30c0: ldr             x16, [x16, #0x3f0]
    // 0xae30c4: stp             x16, NULL, [SP]
    // 0xae30c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae30c8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae30cc: r0 = GetNavigation.toNamed()
    //     0xae30cc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae30d0: LeaveFrame
    //     0xae30d0: mov             SP, fp
    //     0xae30d4: ldp             fp, lr, [SP], #0x10
    // 0xae30d8: ret
    //     0xae30d8: ret             
    // 0xae30dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae30dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae30e0: b               #0xae30a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae30e4, size: 0x5c
    // 0xae30e4: EnterFrame
    //     0xae30e4: stp             fp, lr, [SP, #-0x10]!
    //     0xae30e8: mov             fp, SP
    // 0xae30ec: AllocStack(0x10)
    //     0xae30ec: sub             SP, SP, #0x10
    // 0xae30f0: CheckStackOverflow
    //     0xae30f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae30f4: cmp             SP, x16
    //     0xae30f8: b.ls            #0xae3138
    // 0xae30fc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae30fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae3100: ldr             x0, [x0, #0x2670]
    //     0xae3104: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae3108: cmp             w0, w16
    //     0xae310c: b.ne            #0xae3118
    //     0xae3110: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae3114: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae3118: r16 = "/donation/tab/riwayat"
    //     0xae3118: add             x16, PP, #0x30, lsl #12  ; [pp+0x30438] "/donation/tab/riwayat"
    //     0xae311c: ldr             x16, [x16, #0x438]
    // 0xae3120: stp             x16, NULL, [SP]
    // 0xae3124: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3124: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3128: r0 = GetNavigation.toNamed()
    //     0xae3128: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae312c: LeaveFrame
    //     0xae312c: mov             SP, fp
    //     0xae3130: ldp             fp, lr, [SP], #0x10
    // 0xae3134: ret
    //     0xae3134: ret             
    // 0xae3138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae313c: b               #0xae30fc
  }
  [closure] Widget <anonymous closure>(dynamic, List<Transaction>) {
    // ** addr: 0xae3140, size: 0xb0
    // 0xae3140: EnterFrame
    //     0xae3140: stp             fp, lr, [SP, #-0x10]!
    //     0xae3144: mov             fp, SP
    // 0xae3148: AllocStack(0x18)
    //     0xae3148: sub             SP, SP, #0x18
    // 0xae314c: CheckStackOverflow
    //     0xae314c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3150: cmp             SP, x16
    //     0xae3154: b.ls            #0xae31e8
    // 0xae3158: ldr             x2, [fp, #0x10]
    // 0xae315c: r0 = LoadClassIdInstr(r2)
    //     0xae315c: ldur            x0, [x2, #-1]
    //     0xae3160: ubfx            x0, x0, #0xc, #0x14
    // 0xae3164: mov             x1, x2
    // 0xae3168: r0 = GDT[cid_x0 + 0xe879]()
    //     0xae3168: movz            x17, #0xe879
    //     0xae316c: add             lr, x0, x17
    //     0xae3170: ldr             lr, [x21, lr, lsl #3]
    //     0xae3174: blr             lr
    // 0xae3178: tbnz            w0, #4, #0xae3190
    // 0xae317c: r0 = Instance_SizedBox
    //     0xae317c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae3180: ldr             x0, [x0, #0xc40]
    // 0xae3184: LeaveFrame
    //     0xae3184: mov             SP, fp
    //     0xae3188: ldp             fp, lr, [SP], #0x10
    // 0xae318c: ret
    //     0xae318c: ret             
    // 0xae3190: ldr             x0, [fp, #0x10]
    // 0xae3194: r1 = LoadClassIdInstr(r0)
    //     0xae3194: ldur            x1, [x0, #-1]
    //     0xae3198: ubfx            x1, x1, #0xc, #0x14
    // 0xae319c: str             x0, [SP]
    // 0xae31a0: mov             x0, x1
    // 0xae31a4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xae31a4: movz            x17, #0xc834
    //     0xae31a8: add             lr, x0, x17
    //     0xae31ac: ldr             lr, [x21, lr, lsl #3]
    //     0xae31b0: blr             lr
    // 0xae31b4: r2 = LoadInt32Instr(r0)
    //     0xae31b4: sbfx            x2, x0, #1, #0x1f
    //     0xae31b8: tbz             w0, #0, #0xae31c0
    //     0xae31bc: ldur            x2, [x0, #7]
    // 0xae31c0: stur            x2, [fp, #-8]
    // 0xae31c4: r0 = Badge()
    //     0xae31c4: bl              #0xae32a4  ; AllocateBadgeStub -> Badge (size=0x2c)
    // 0xae31c8: mov             x1, x0
    // 0xae31cc: ldur            x2, [fp, #-8]
    // 0xae31d0: stur            x0, [fp, #-0x10]
    // 0xae31d4: r0 = Badge.count()
    //     0xae31d4: bl              #0xae31f0  ; [package:flutter/src/material/badge.dart] Badge::Badge.count
    // 0xae31d8: ldur            x0, [fp, #-0x10]
    // 0xae31dc: LeaveFrame
    //     0xae31dc: mov             SP, fp
    //     0xae31e0: ldp             fp, lr, [SP], #0x10
    // 0xae31e4: ret
    //     0xae31e4: ret             
    // 0xae31e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae31e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae31ec: b               #0xae3158
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae32b0, size: 0x5c
    // 0xae32b0: EnterFrame
    //     0xae32b0: stp             fp, lr, [SP, #-0x10]!
    //     0xae32b4: mov             fp, SP
    // 0xae32b8: AllocStack(0x10)
    //     0xae32b8: sub             SP, SP, #0x10
    // 0xae32bc: CheckStackOverflow
    //     0xae32bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae32c0: cmp             SP, x16
    //     0xae32c4: b.ls            #0xae3304
    // 0xae32c8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae32c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae32cc: ldr             x0, [x0, #0x2670]
    //     0xae32d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae32d4: cmp             w0, w16
    //     0xae32d8: b.ne            #0xae32e4
    //     0xae32dc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae32e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae32e4: r16 = "/zakat"
    //     0xae32e4: add             x16, PP, #0xf, lsl #12  ; [pp+0xfcf8] "/zakat"
    //     0xae32e8: ldr             x16, [x16, #0xcf8]
    // 0xae32ec: stp             x16, NULL, [SP]
    // 0xae32f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae32f0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae32f4: r0 = GetNavigation.toNamed()
    //     0xae32f4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae32f8: LeaveFrame
    //     0xae32f8: mov             SP, fp
    //     0xae32fc: ldp             fp, lr, [SP], #0x10
    // 0xae3300: ret
    //     0xae3300: ret             
    // 0xae3304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3308: b               #0xae32c8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae330c, size: 0x5c
    // 0xae330c: EnterFrame
    //     0xae330c: stp             fp, lr, [SP, #-0x10]!
    //     0xae3310: mov             fp, SP
    // 0xae3314: AllocStack(0x10)
    //     0xae3314: sub             SP, SP, #0x10
    // 0xae3318: CheckStackOverflow
    //     0xae3318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae331c: cmp             SP, x16
    //     0xae3320: b.ls            #0xae3360
    // 0xae3324: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae3324: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae3328: ldr             x0, [x0, #0x2670]
    //     0xae332c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae3330: cmp             w0, w16
    //     0xae3334: b.ne            #0xae3340
    //     0xae3338: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae333c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae3340: r16 = "/donation/koin-nu-list"
    //     0xae3340: add             x16, PP, #0x30, lsl #12  ; [pp+0x30440] "/donation/koin-nu-list"
    //     0xae3344: ldr             x16, [x16, #0x440]
    // 0xae3348: stp             x16, NULL, [SP]
    // 0xae334c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae334c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3350: r0 = GetNavigation.toNamed()
    //     0xae3350: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae3354: LeaveFrame
    //     0xae3354: mov             SP, fp
    //     0xae3358: ldp             fp, lr, [SP], #0x10
    // 0xae335c: ret
    //     0xae335c: ret             
    // 0xae3360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3364: b               #0xae3324
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3368, size: 0x5c
    // 0xae3368: EnterFrame
    //     0xae3368: stp             fp, lr, [SP, #-0x10]!
    //     0xae336c: mov             fp, SP
    // 0xae3370: AllocStack(0x10)
    //     0xae3370: sub             SP, SP, #0x10
    // 0xae3374: CheckStackOverflow
    //     0xae3374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3378: cmp             SP, x16
    //     0xae337c: b.ls            #0xae33bc
    // 0xae3380: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae3380: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae3384: ldr             x0, [x0, #0x2670]
    //     0xae3388: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae338c: cmp             w0, w16
    //     0xae3390: b.ne            #0xae339c
    //     0xae3394: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae3398: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae339c: r16 = "/qurban"
    //     0xae339c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24820] "/qurban"
    //     0xae33a0: ldr             x16, [x16, #0x820]
    // 0xae33a4: stp             x16, NULL, [SP]
    // 0xae33a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae33a8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae33ac: r0 = GetNavigation.toNamed()
    //     0xae33ac: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae33b0: LeaveFrame
    //     0xae33b0: mov             SP, fp
    //     0xae33b4: ldp             fp, lr, [SP], #0x10
    // 0xae33b8: ret
    //     0xae33b8: ret             
    // 0xae33bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae33bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae33c0: b               #0xae3380
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae33c4, size: 0x5c
    // 0xae33c4: EnterFrame
    //     0xae33c4: stp             fp, lr, [SP, #-0x10]!
    //     0xae33c8: mov             fp, SP
    // 0xae33cc: AllocStack(0x10)
    //     0xae33cc: sub             SP, SP, #0x10
    // 0xae33d0: CheckStackOverflow
    //     0xae33d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae33d4: cmp             SP, x16
    //     0xae33d8: b.ls            #0xae3418
    // 0xae33dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae33dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae33e0: ldr             x0, [x0, #0x2670]
    //     0xae33e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae33e8: cmp             w0, w16
    //     0xae33ec: b.ne            #0xae33f8
    //     0xae33f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae33f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae33f8: r16 = "/zakat-fitrah"
    //     0xae33f8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24850] "/zakat-fitrah"
    //     0xae33fc: ldr             x16, [x16, #0x850]
    // 0xae3400: stp             x16, NULL, [SP]
    // 0xae3404: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3404: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3408: r0 = GetNavigation.toNamed()
    //     0xae3408: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae340c: LeaveFrame
    //     0xae340c: mov             SP, fp
    //     0xae3410: ldp             fp, lr, [SP], #0x10
    // 0xae3414: ret
    //     0xae3414: ret             
    // 0xae3418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae341c: b               #0xae33dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3420, size: 0x5c
    // 0xae3420: EnterFrame
    //     0xae3420: stp             fp, lr, [SP, #-0x10]!
    //     0xae3424: mov             fp, SP
    // 0xae3428: AllocStack(0x10)
    //     0xae3428: sub             SP, SP, #0x10
    // 0xae342c: CheckStackOverflow
    //     0xae342c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3430: cmp             SP, x16
    //     0xae3434: b.ls            #0xae3474
    // 0xae3438: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae3438: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae343c: ldr             x0, [x0, #0x2670]
    //     0xae3440: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae3444: cmp             w0, w16
    //     0xae3448: b.ne            #0xae3454
    //     0xae344c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae3450: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae3454: r16 = "/donation/tab/donasi"
    //     0xae3454: add             x16, PP, #0x30, lsl #12  ; [pp+0x30448] "/donation/tab/donasi"
    //     0xae3458: ldr             x16, [x16, #0x448]
    // 0xae345c: stp             x16, NULL, [SP]
    // 0xae3460: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3460: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3464: r0 = GetNavigation.toNamed()
    //     0xae3464: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae3468: LeaveFrame
    //     0xae3468: mov             SP, fp
    //     0xae346c: ldp             fp, lr, [SP], #0x10
    // 0xae3470: ret
    //     0xae3470: ret             
    // 0xae3474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3478: b               #0xae3438
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae347c, size: 0x5c
    // 0xae347c: EnterFrame
    //     0xae347c: stp             fp, lr, [SP, #-0x10]!
    //     0xae3480: mov             fp, SP
    // 0xae3484: AllocStack(0x10)
    //     0xae3484: sub             SP, SP, #0x10
    // 0xae3488: CheckStackOverflow
    //     0xae3488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae348c: cmp             SP, x16
    //     0xae3490: b.ls            #0xae34d0
    // 0xae3494: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae3494: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae3498: ldr             x0, [x0, #0x2670]
    //     0xae349c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae34a0: cmp             w0, w16
    //     0xae34a4: b.ne            #0xae34b0
    //     0xae34a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae34ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae34b0: r16 = "/donation/tab/zakat"
    //     0xae34b0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30450] "/donation/tab/zakat"
    //     0xae34b4: ldr             x16, [x16, #0x450]
    // 0xae34b8: stp             x16, NULL, [SP]
    // 0xae34bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae34bc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae34c0: r0 = GetNavigation.toNamed()
    //     0xae34c0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae34c4: LeaveFrame
    //     0xae34c4: mov             SP, fp
    //     0xae34c8: ldp             fp, lr, [SP], #0x10
    // 0xae34cc: ret
    //     0xae34cc: ret             
    // 0xae34d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae34d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae34d4: b               #0xae3494
  }
}
