// lib: , url: package:nuonline/app/modules/donation/views/history_tab_view.dart

// class id: 1050224, size: 0x8
class :: {
}

// class id: 5041, size: 0xc, field offset: 0xc
//   const constructor, 
class DonationHistoryEmptyState extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8ef20, size: 0x19c
    // 0xb8ef20: EnterFrame
    //     0xb8ef20: stp             fp, lr, [SP, #-0x10]!
    //     0xb8ef24: mov             fp, SP
    // 0xb8ef28: AllocStack(0x20)
    //     0xb8ef28: sub             SP, SP, #0x20
    // 0xb8ef2c: CheckStackOverflow
    //     0xb8ef2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ef30: cmp             SP, x16
    //     0xb8ef34: b.ls            #0xb8f0b4
    // 0xb8ef38: r1 = Function '<anonymous closure>':.
    //     0xb8ef38: add             x1, PP, #0x40, lsl #12  ; [pp+0x40108] AnonymousClosure: (0xb8f118), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] DonationHistoryEmptyState::build (0xb8ef20)
    //     0xb8ef3c: ldr             x1, [x1, #0x108]
    // 0xb8ef40: r2 = Null
    //     0xb8ef40: mov             x2, NULL
    // 0xb8ef44: r0 = AllocateClosure()
    //     0xb8ef44: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ef48: stur            x0, [fp, #-8]
    // 0xb8ef4c: r0 = TextButton()
    //     0xb8ef4c: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb8ef50: mov             x1, x0
    // 0xb8ef54: ldur            x0, [fp, #-8]
    // 0xb8ef58: stur            x1, [fp, #-0x10]
    // 0xb8ef5c: StoreField: r1->field_b = r0
    //     0xb8ef5c: stur            w0, [x1, #0xb]
    // 0xb8ef60: r0 = false
    //     0xb8ef60: add             x0, NULL, #0x30  ; false
    // 0xb8ef64: StoreField: r1->field_27 = r0
    //     0xb8ef64: stur            w0, [x1, #0x27]
    // 0xb8ef68: r2 = true
    //     0xb8ef68: add             x2, NULL, #0x20  ; true
    // 0xb8ef6c: StoreField: r1->field_2f = r2
    //     0xb8ef6c: stur            w2, [x1, #0x2f]
    // 0xb8ef70: r3 = Instance_Text
    //     0xb8ef70: add             x3, PP, #0x40, lsl #12  ; [pp+0x40110] Obj!Text@e23ad1
    //     0xb8ef74: ldr             x3, [x3, #0x110]
    // 0xb8ef78: StoreField: r1->field_37 = r3
    //     0xb8ef78: stur            w3, [x1, #0x37]
    // 0xb8ef7c: r0 = SizedBox()
    //     0xb8ef7c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb8ef80: mov             x3, x0
    // 0xb8ef84: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb8ef84: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb8ef88: ldr             x0, [x0, #0xc58]
    // 0xb8ef8c: stur            x3, [fp, #-8]
    // 0xb8ef90: StoreField: r3->field_f = r0
    //     0xb8ef90: stur            w0, [x3, #0xf]
    // 0xb8ef94: ldur            x1, [fp, #-0x10]
    // 0xb8ef98: StoreField: r3->field_b = r1
    //     0xb8ef98: stur            w1, [x3, #0xb]
    // 0xb8ef9c: r1 = Function '<anonymous closure>':.
    //     0xb8ef9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40118] AnonymousClosure: (0xb8f0bc), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] DonationHistoryEmptyState::build (0xb8ef20)
    //     0xb8efa0: ldr             x1, [x1, #0x118]
    // 0xb8efa4: r2 = Null
    //     0xb8efa4: mov             x2, NULL
    // 0xb8efa8: r0 = AllocateClosure()
    //     0xb8efa8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8efac: stur            x0, [fp, #-0x10]
    // 0xb8efb0: r0 = OutlinedButton()
    //     0xb8efb0: bl              #0xa3b670  ; AllocateOutlinedButtonStub -> OutlinedButton (size=0x3c)
    // 0xb8efb4: mov             x1, x0
    // 0xb8efb8: ldur            x0, [fp, #-0x10]
    // 0xb8efbc: stur            x1, [fp, #-0x18]
    // 0xb8efc0: StoreField: r1->field_b = r0
    //     0xb8efc0: stur            w0, [x1, #0xb]
    // 0xb8efc4: r0 = false
    //     0xb8efc4: add             x0, NULL, #0x30  ; false
    // 0xb8efc8: StoreField: r1->field_27 = r0
    //     0xb8efc8: stur            w0, [x1, #0x27]
    // 0xb8efcc: r0 = true
    //     0xb8efcc: add             x0, NULL, #0x20  ; true
    // 0xb8efd0: StoreField: r1->field_2f = r0
    //     0xb8efd0: stur            w0, [x1, #0x2f]
    // 0xb8efd4: r0 = Instance_Text
    //     0xb8efd4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40120] Obj!Text@e23a81
    //     0xb8efd8: ldr             x0, [x0, #0x120]
    // 0xb8efdc: StoreField: r1->field_37 = r0
    //     0xb8efdc: stur            w0, [x1, #0x37]
    // 0xb8efe0: r0 = SizedBox()
    //     0xb8efe0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb8efe4: mov             x3, x0
    // 0xb8efe8: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb8efe8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb8efec: ldr             x0, [x0, #0xc58]
    // 0xb8eff0: stur            x3, [fp, #-0x10]
    // 0xb8eff4: StoreField: r3->field_f = r0
    //     0xb8eff4: stur            w0, [x3, #0xf]
    // 0xb8eff8: ldur            x0, [fp, #-0x18]
    // 0xb8effc: StoreField: r3->field_b = r0
    //     0xb8effc: stur            w0, [x3, #0xb]
    // 0xb8f000: r1 = Null
    //     0xb8f000: mov             x1, NULL
    // 0xb8f004: r2 = 8
    //     0xb8f004: movz            x2, #0x8
    // 0xb8f008: r0 = AllocateArray()
    //     0xb8f008: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8f00c: stur            x0, [fp, #-0x18]
    // 0xb8f010: r16 = Instance_SizedBox
    //     0xb8f010: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb8f014: ldr             x16, [x16, #0x950]
    // 0xb8f018: StoreField: r0->field_f = r16
    //     0xb8f018: stur            w16, [x0, #0xf]
    // 0xb8f01c: ldur            x1, [fp, #-8]
    // 0xb8f020: StoreField: r0->field_13 = r1
    //     0xb8f020: stur            w1, [x0, #0x13]
    // 0xb8f024: r16 = Instance_SizedBox
    //     0xb8f024: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb8f028: ldr             x16, [x16, #0x950]
    // 0xb8f02c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8f02c: stur            w16, [x0, #0x17]
    // 0xb8f030: ldur            x1, [fp, #-0x10]
    // 0xb8f034: StoreField: r0->field_1b = r1
    //     0xb8f034: stur            w1, [x0, #0x1b]
    // 0xb8f038: r1 = <Widget>
    //     0xb8f038: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8f03c: r0 = AllocateGrowableArray()
    //     0xb8f03c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8f040: mov             x1, x0
    // 0xb8f044: ldur            x0, [fp, #-0x18]
    // 0xb8f048: stur            x1, [fp, #-8]
    // 0xb8f04c: StoreField: r1->field_f = r0
    //     0xb8f04c: stur            w0, [x1, #0xf]
    // 0xb8f050: r0 = 8
    //     0xb8f050: movz            x0, #0x8
    // 0xb8f054: StoreField: r1->field_b = r0
    //     0xb8f054: stur            w0, [x1, #0xb]
    // 0xb8f058: r0 = NEmptyState()
    //     0xb8f058: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xb8f05c: stur            x0, [fp, #-0x10]
    // 0xb8f060: ldur            x16, [fp, #-8]
    // 0xb8f064: str             x16, [SP]
    // 0xb8f068: mov             x1, x0
    // 0xb8f06c: r2 = "Mari tunaikan kewajiban sekaligus berbagi manfaat dengan berzakat atau bersedekah melalui NU Online Super App"
    //     0xb8f06c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40128] "Mari tunaikan kewajiban sekaligus berbagi manfaat dengan berzakat atau bersedekah melalui NU Online Super App"
    //     0xb8f070: ldr             x2, [x2, #0x128]
    // 0xb8f074: r3 = "assets/images/illustration/no_donation.svg"
    //     0xb8f074: add             x3, PP, #0x40, lsl #12  ; [pp+0x40130] "assets/images/illustration/no_donation.svg"
    //     0xb8f078: ldr             x3, [x3, #0x130]
    // 0xb8f07c: r5 = "Belum Ada Riwayat Zakat dan Donasi"
    //     0xb8f07c: add             x5, PP, #0x40, lsl #12  ; [pp+0x40138] "Belum Ada Riwayat Zakat dan Donasi"
    //     0xb8f080: ldr             x5, [x5, #0x138]
    // 0xb8f084: r4 = const [0, 0x5, 0x1, 0x4, children, 0x4, null]
    //     0xb8f084: add             x4, PP, #0x29, lsl #12  ; [pp+0x29818] List(7) [0, 0x5, 0x1, 0x4, "children", 0x4, Null]
    //     0xb8f088: ldr             x4, [x4, #0x818]
    // 0xb8f08c: r0 = NEmptyState.svg()
    //     0xb8f08c: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xb8f090: r0 = Center()
    //     0xb8f090: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8f094: r1 = Instance_Alignment
    //     0xb8f094: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb8f098: ldr             x1, [x1, #0x898]
    // 0xb8f09c: StoreField: r0->field_f = r1
    //     0xb8f09c: stur            w1, [x0, #0xf]
    // 0xb8f0a0: ldur            x1, [fp, #-0x10]
    // 0xb8f0a4: StoreField: r0->field_b = r1
    //     0xb8f0a4: stur            w1, [x0, #0xb]
    // 0xb8f0a8: LeaveFrame
    //     0xb8f0a8: mov             SP, fp
    //     0xb8f0ac: ldp             fp, lr, [SP], #0x10
    // 0xb8f0b0: ret
    //     0xb8f0b0: ret             
    // 0xb8f0b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f0b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f0b8: b               #0xb8ef38
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8f0bc, size: 0x5c
    // 0xb8f0bc: EnterFrame
    //     0xb8f0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f0c0: mov             fp, SP
    // 0xb8f0c4: AllocStack(0x10)
    //     0xb8f0c4: sub             SP, SP, #0x10
    // 0xb8f0c8: CheckStackOverflow
    //     0xb8f0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f0cc: cmp             SP, x16
    //     0xb8f0d0: b.ls            #0xb8f110
    // 0xb8f0d4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8f0d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8f0d8: ldr             x0, [x0, #0x2670]
    //     0xb8f0dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8f0e0: cmp             w0, w16
    //     0xb8f0e4: b.ne            #0xb8f0f0
    //     0xb8f0e8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8f0ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8f0f0: r16 = "/donation/pay-donation"
    //     0xb8f0f0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35560] "/donation/pay-donation"
    //     0xb8f0f4: ldr             x16, [x16, #0x560]
    // 0xb8f0f8: stp             x16, NULL, [SP]
    // 0xb8f0fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb8f0fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8f100: r0 = GetNavigation.toNamed()
    //     0xb8f100: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8f104: LeaveFrame
    //     0xb8f104: mov             SP, fp
    //     0xb8f108: ldp             fp, lr, [SP], #0x10
    // 0xb8f10c: ret
    //     0xb8f10c: ret             
    // 0xb8f110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f114: b               #0xb8f0d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8f118, size: 0x5c
    // 0xb8f118: EnterFrame
    //     0xb8f118: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f11c: mov             fp, SP
    // 0xb8f120: AllocStack(0x10)
    //     0xb8f120: sub             SP, SP, #0x10
    // 0xb8f124: CheckStackOverflow
    //     0xb8f124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f128: cmp             SP, x16
    //     0xb8f12c: b.ls            #0xb8f16c
    // 0xb8f130: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8f130: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8f134: ldr             x0, [x0, #0x2670]
    //     0xb8f138: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8f13c: cmp             w0, w16
    //     0xb8f140: b.ne            #0xb8f14c
    //     0xb8f144: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8f148: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8f14c: r16 = "/donation/pay-zakat"
    //     0xb8f14c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27138] "/donation/pay-zakat"
    //     0xb8f150: ldr             x16, [x16, #0x138]
    // 0xb8f154: stp             x16, NULL, [SP]
    // 0xb8f158: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb8f158: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8f15c: r0 = GetNavigation.toNamed()
    //     0xb8f15c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8f160: LeaveFrame
    //     0xb8f160: mov             SP, fp
    //     0xb8f164: ldp             fp, lr, [SP], #0x10
    // 0xb8f168: ret
    //     0xb8f168: ret             
    // 0xb8f16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f16c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f170: b               #0xb8f130
  }
}

// class id: 5042, size: 0x10, field offset: 0xc
//   const constructor, 
class DonationHistoryItem extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8d9c0, size: 0x998
    // 0xb8d9c0: EnterFrame
    //     0xb8d9c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d9c4: mov             fp, SP
    // 0xb8d9c8: AllocStack(0x70)
    //     0xb8d9c8: sub             SP, SP, #0x70
    // 0xb8d9cc: SetupParameters(DonationHistoryItem this /* r1 => r1, fp-0x8 */)
    //     0xb8d9cc: stur            x1, [fp, #-8]
    // 0xb8d9d0: CheckStackOverflow
    //     0xb8d9d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d9d4: cmp             SP, x16
    //     0xb8d9d8: b.ls            #0xb8e350
    // 0xb8d9dc: r1 = 1
    //     0xb8d9dc: movz            x1, #0x1
    // 0xb8d9e0: r0 = AllocateContext()
    //     0xb8d9e0: bl              #0xec126c  ; AllocateContextStub
    // 0xb8d9e4: mov             x1, x0
    // 0xb8d9e8: ldur            x0, [fp, #-8]
    // 0xb8d9ec: stur            x1, [fp, #-0x10]
    // 0xb8d9f0: StoreField: r1->field_f = r0
    //     0xb8d9f0: stur            w0, [x1, #0xf]
    // 0xb8d9f4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d9f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d9f8: ldr             x0, [x0, #0x2670]
    //     0xb8d9fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8da00: cmp             w0, w16
    //     0xb8da04: b.ne            #0xb8da10
    //     0xb8da08: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8da0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8da10: r0 = GetNavigation.theme()
    //     0xb8da10: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb8da14: LoadField: r1 = r0->field_4b
    //     0xb8da14: ldur            w1, [x0, #0x4b]
    // 0xb8da18: DecompressPointer r1
    //     0xb8da18: add             x1, x1, HEAP, lsl #32
    // 0xb8da1c: str             x1, [SP]
    // 0xb8da20: r1 = Null
    //     0xb8da20: mov             x1, NULL
    // 0xb8da24: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb8da24: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb8da28: ldr             x4, [x4, #0x228]
    // 0xb8da2c: r0 = Border.all()
    //     0xb8da2c: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb8da30: stur            x0, [fp, #-0x18]
    // 0xb8da34: r0 = Radius()
    //     0xb8da34: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8da38: d0 = 8.000000
    //     0xb8da38: fmov            d0, #8.00000000
    // 0xb8da3c: stur            x0, [fp, #-0x20]
    // 0xb8da40: StoreField: r0->field_7 = d0
    //     0xb8da40: stur            d0, [x0, #7]
    // 0xb8da44: StoreField: r0->field_f = d0
    //     0xb8da44: stur            d0, [x0, #0xf]
    // 0xb8da48: r0 = BorderRadius()
    //     0xb8da48: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8da4c: mov             x1, x0
    // 0xb8da50: ldur            x0, [fp, #-0x20]
    // 0xb8da54: stur            x1, [fp, #-0x28]
    // 0xb8da58: StoreField: r1->field_7 = r0
    //     0xb8da58: stur            w0, [x1, #7]
    // 0xb8da5c: StoreField: r1->field_b = r0
    //     0xb8da5c: stur            w0, [x1, #0xb]
    // 0xb8da60: StoreField: r1->field_f = r0
    //     0xb8da60: stur            w0, [x1, #0xf]
    // 0xb8da64: StoreField: r1->field_13 = r0
    //     0xb8da64: stur            w0, [x1, #0x13]
    // 0xb8da68: r0 = BoxDecoration()
    //     0xb8da68: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8da6c: mov             x1, x0
    // 0xb8da70: ldur            x0, [fp, #-0x18]
    // 0xb8da74: stur            x1, [fp, #-0x20]
    // 0xb8da78: StoreField: r1->field_f = r0
    //     0xb8da78: stur            w0, [x1, #0xf]
    // 0xb8da7c: ldur            x0, [fp, #-0x28]
    // 0xb8da80: StoreField: r1->field_13 = r0
    //     0xb8da80: stur            w0, [x1, #0x13]
    // 0xb8da84: r0 = Instance_BoxShape
    //     0xb8da84: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8da88: ldr             x0, [x0, #0xca8]
    // 0xb8da8c: StoreField: r1->field_23 = r0
    //     0xb8da8c: stur            w0, [x1, #0x23]
    // 0xb8da90: ldur            x0, [fp, #-8]
    // 0xb8da94: LoadField: r2 = r0->field_b
    //     0xb8da94: ldur            w2, [x0, #0xb]
    // 0xb8da98: DecompressPointer r2
    //     0xb8da98: add             x2, x2, HEAP, lsl #32
    // 0xb8da9c: stur            x2, [fp, #-0x18]
    // 0xb8daa0: LoadField: r0 = r2->field_27
    //     0xb8daa0: ldur            w0, [x2, #0x27]
    // 0xb8daa4: DecompressPointer r0
    //     0xb8daa4: add             x0, x0, HEAP, lsl #32
    // 0xb8daa8: stur            x0, [fp, #-8]
    // 0xb8daac: r16 = "SUCCESS"
    //     0xb8daac: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8dab0: ldr             x16, [x16, #0x2c0]
    // 0xb8dab4: stp             x0, x16, [SP]
    // 0xb8dab8: r0 = ==()
    //     0xb8dab8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8dabc: tbnz            w0, #4, #0xb8dacc
    // 0xb8dac0: r2 = "Berhasil"
    //     0xb8dac0: add             x2, PP, #0x34, lsl #12  ; [pp+0x34f78] "Berhasil"
    //     0xb8dac4: ldr             x2, [x2, #0xf78]
    // 0xb8dac8: b               #0xb8db1c
    // 0xb8dacc: r16 = "PENDING"
    //     0xb8dacc: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8dad0: ldr             x16, [x16, #0x2c8]
    // 0xb8dad4: ldur            lr, [fp, #-8]
    // 0xb8dad8: stp             lr, x16, [SP]
    // 0xb8dadc: r0 = ==()
    //     0xb8dadc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8dae0: tbnz            w0, #4, #0xb8daf0
    // 0xb8dae4: r2 = "Menunggu"
    //     0xb8dae4: add             x2, PP, #0x34, lsl #12  ; [pp+0x34f80] "Menunggu"
    //     0xb8dae8: ldr             x2, [x2, #0xf80]
    // 0xb8daec: b               #0xb8db1c
    // 0xb8daf0: r16 = "EXPIRED"
    //     0xb8daf0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8daf4: ldr             x16, [x16, #0xf88]
    // 0xb8daf8: ldur            lr, [fp, #-8]
    // 0xb8dafc: stp             lr, x16, [SP]
    // 0xb8db00: r0 = ==()
    //     0xb8db00: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8db04: tbnz            w0, #4, #0xb8db14
    // 0xb8db08: r2 = "Dibatalkan"
    //     0xb8db08: add             x2, PP, #0x34, lsl #12  ; [pp+0x34f90] "Dibatalkan"
    //     0xb8db0c: ldr             x2, [x2, #0xf90]
    // 0xb8db10: b               #0xb8db1c
    // 0xb8db14: r2 = "Menunggu"
    //     0xb8db14: add             x2, PP, #0x34, lsl #12  ; [pp+0x34f80] "Menunggu"
    //     0xb8db18: ldr             x2, [x2, #0xf80]
    // 0xb8db1c: ldur            x0, [fp, #-0x18]
    // 0xb8db20: mov             x1, x0
    // 0xb8db24: stur            x2, [fp, #-0x28]
    // 0xb8db28: r0 = progressBackgroundColor()
    //     0xb8db28: bl              #0xb8eaec  ; [package:nuonline/app/data/models/transaction.dart] Transaction::progressBackgroundColor
    // 0xb8db2c: ldur            x1, [fp, #-0x18]
    // 0xb8db30: stur            x0, [fp, #-0x30]
    // 0xb8db34: r0 = progressBorderColor()
    //     0xb8db34: bl              #0xb8e9a4  ; [package:nuonline/app/data/models/transaction.dart] Transaction::progressBorderColor
    // 0xb8db38: ldur            x1, [fp, #-0x18]
    // 0xb8db3c: stur            x0, [fp, #-0x38]
    // 0xb8db40: r0 = progressTextColor()
    //     0xb8db40: bl              #0xb8e85c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::progressTextColor
    // 0xb8db44: stur            x0, [fp, #-0x40]
    // 0xb8db48: r0 = NLabelButton()
    //     0xb8db48: bl              #0xadf214  ; AllocateNLabelButtonStub -> NLabelButton (size=0x24)
    // 0xb8db4c: mov             x2, x0
    // 0xb8db50: ldur            x0, [fp, #-0x28]
    // 0xb8db54: stur            x2, [fp, #-0x48]
    // 0xb8db58: StoreField: r2->field_b = r0
    //     0xb8db58: stur            w0, [x2, #0xb]
    // 0xb8db5c: ldur            x0, [fp, #-0x30]
    // 0xb8db60: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8db60: stur            w0, [x2, #0x17]
    // 0xb8db64: ldur            x0, [fp, #-0x38]
    // 0xb8db68: StoreField: r2->field_1b = r0
    //     0xb8db68: stur            w0, [x2, #0x1b]
    // 0xb8db6c: ldur            x0, [fp, #-0x40]
    // 0xb8db70: StoreField: r2->field_1f = r0
    //     0xb8db70: stur            w0, [x2, #0x1f]
    // 0xb8db74: ldur            x0, [fp, #-0x18]
    // 0xb8db78: LoadField: r1 = r0->field_43
    //     0xb8db78: ldur            w1, [x0, #0x43]
    // 0xb8db7c: DecompressPointer r1
    //     0xb8db7c: add             x1, x1, HEAP, lsl #32
    // 0xb8db80: r0 = format()
    //     0xb8db80: bl              #0xb36788  ; [package:timeago/src/timeago.dart] ::format
    // 0xb8db84: stur            x0, [fp, #-0x28]
    // 0xb8db88: r0 = GetNavigation.textTheme()
    //     0xb8db88: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8db8c: LoadField: r1 = r0->field_27
    //     0xb8db8c: ldur            w1, [x0, #0x27]
    // 0xb8db90: DecompressPointer r1
    //     0xb8db90: add             x1, x1, HEAP, lsl #32
    // 0xb8db94: stur            x1, [fp, #-0x30]
    // 0xb8db98: r0 = Text()
    //     0xb8db98: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8db9c: mov             x3, x0
    // 0xb8dba0: ldur            x0, [fp, #-0x28]
    // 0xb8dba4: stur            x3, [fp, #-0x38]
    // 0xb8dba8: StoreField: r3->field_b = r0
    //     0xb8dba8: stur            w0, [x3, #0xb]
    // 0xb8dbac: ldur            x0, [fp, #-0x30]
    // 0xb8dbb0: StoreField: r3->field_13 = r0
    //     0xb8dbb0: stur            w0, [x3, #0x13]
    // 0xb8dbb4: r1 = Null
    //     0xb8dbb4: mov             x1, NULL
    // 0xb8dbb8: r2 = 4
    //     0xb8dbb8: movz            x2, #0x4
    // 0xb8dbbc: r0 = AllocateArray()
    //     0xb8dbbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8dbc0: mov             x2, x0
    // 0xb8dbc4: ldur            x0, [fp, #-0x48]
    // 0xb8dbc8: stur            x2, [fp, #-0x28]
    // 0xb8dbcc: StoreField: r2->field_f = r0
    //     0xb8dbcc: stur            w0, [x2, #0xf]
    // 0xb8dbd0: ldur            x0, [fp, #-0x38]
    // 0xb8dbd4: StoreField: r2->field_13 = r0
    //     0xb8dbd4: stur            w0, [x2, #0x13]
    // 0xb8dbd8: r1 = <Widget>
    //     0xb8dbd8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8dbdc: r0 = AllocateGrowableArray()
    //     0xb8dbdc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8dbe0: mov             x1, x0
    // 0xb8dbe4: ldur            x0, [fp, #-0x28]
    // 0xb8dbe8: stur            x1, [fp, #-0x30]
    // 0xb8dbec: StoreField: r1->field_f = r0
    //     0xb8dbec: stur            w0, [x1, #0xf]
    // 0xb8dbf0: r2 = 4
    //     0xb8dbf0: movz            x2, #0x4
    // 0xb8dbf4: StoreField: r1->field_b = r2
    //     0xb8dbf4: stur            w2, [x1, #0xb]
    // 0xb8dbf8: r0 = Row()
    //     0xb8dbf8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8dbfc: mov             x2, x0
    // 0xb8dc00: r0 = Instance_Axis
    //     0xb8dc00: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8dc04: stur            x2, [fp, #-0x28]
    // 0xb8dc08: StoreField: r2->field_f = r0
    //     0xb8dc08: stur            w0, [x2, #0xf]
    // 0xb8dc0c: r3 = Instance_MainAxisAlignment
    //     0xb8dc0c: add             x3, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb8dc10: ldr             x3, [x3, #0xae8]
    // 0xb8dc14: StoreField: r2->field_13 = r3
    //     0xb8dc14: stur            w3, [x2, #0x13]
    // 0xb8dc18: r4 = Instance_MainAxisSize
    //     0xb8dc18: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8dc1c: ldr             x4, [x4, #0x738]
    // 0xb8dc20: ArrayStore: r2[0] = r4  ; List_4
    //     0xb8dc20: stur            w4, [x2, #0x17]
    // 0xb8dc24: r5 = Instance_CrossAxisAlignment
    //     0xb8dc24: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8dc28: ldr             x5, [x5, #0x740]
    // 0xb8dc2c: StoreField: r2->field_1b = r5
    //     0xb8dc2c: stur            w5, [x2, #0x1b]
    // 0xb8dc30: r6 = Instance_VerticalDirection
    //     0xb8dc30: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8dc34: ldr             x6, [x6, #0x748]
    // 0xb8dc38: StoreField: r2->field_23 = r6
    //     0xb8dc38: stur            w6, [x2, #0x23]
    // 0xb8dc3c: r7 = Instance_Clip
    //     0xb8dc3c: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8dc40: ldr             x7, [x7, #0x750]
    // 0xb8dc44: StoreField: r2->field_2b = r7
    //     0xb8dc44: stur            w7, [x2, #0x2b]
    // 0xb8dc48: StoreField: r2->field_2f = rZR
    //     0xb8dc48: stur            xzr, [x2, #0x2f]
    // 0xb8dc4c: ldur            x1, [fp, #-0x30]
    // 0xb8dc50: StoreField: r2->field_b = r1
    //     0xb8dc50: stur            w1, [x2, #0xb]
    // 0xb8dc54: ldur            x1, [fp, #-0x18]
    // 0xb8dc58: r0 = iconUrl()
    //     0xb8dc58: bl              #0xb8e6a0  ; [package:nuonline/app/data/models/transaction.dart] Transaction::iconUrl
    // 0xb8dc5c: stur            x0, [fp, #-0x30]
    // 0xb8dc60: r0 = SvgPicture()
    //     0xb8dc60: bl              #0xacfad4  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8dc64: stur            x0, [fp, #-0x38]
    // 0xb8dc68: r16 = "nuikit"
    //     0xb8dc68: add             x16, PP, #0x25, lsl #12  ; [pp+0x25798] "nuikit"
    //     0xb8dc6c: ldr             x16, [x16, #0x798]
    // 0xb8dc70: r30 = 40.000000
    //     0xb8dc70: add             lr, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xb8dc74: ldr             lr, [lr, #0x1b8]
    // 0xb8dc78: stp             lr, x16, [SP, #8]
    // 0xb8dc7c: r16 = 40.000000
    //     0xb8dc7c: add             x16, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xb8dc80: ldr             x16, [x16, #0x1b8]
    // 0xb8dc84: str             x16, [SP]
    // 0xb8dc88: mov             x1, x0
    // 0xb8dc8c: ldur            x2, [fp, #-0x30]
    // 0xb8dc90: r4 = const [0, 0x5, 0x3, 0x2, height, 0x3, package, 0x2, width, 0x4, null]
    //     0xb8dc90: add             x4, PP, #0x34, lsl #12  ; [pp+0x34f98] List(11) [0, 0x5, 0x3, 0x2, "height", 0x3, "package", 0x2, "width", 0x4, Null]
    //     0xb8dc94: ldr             x4, [x4, #0xf98]
    // 0xb8dc98: r0 = SvgPicture.asset()
    //     0xb8dc98: bl              #0xabab8c  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8dc9c: ldur            x1, [fp, #-0x18]
    // 0xb8dca0: r0 = title()
    //     0xb8dca0: bl              #0xb8e3d4  ; [package:nuonline/app/data/models/transaction.dart] Transaction::title
    // 0xb8dca4: stur            x0, [fp, #-0x30]
    // 0xb8dca8: r0 = GetNavigation.textTheme()
    //     0xb8dca8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8dcac: LoadField: r1 = r0->field_23
    //     0xb8dcac: ldur            w1, [x0, #0x23]
    // 0xb8dcb0: DecompressPointer r1
    //     0xb8dcb0: add             x1, x1, HEAP, lsl #32
    // 0xb8dcb4: cmp             w1, NULL
    // 0xb8dcb8: b.ne            #0xb8dcc4
    // 0xb8dcbc: r4 = Null
    //     0xb8dcbc: mov             x4, NULL
    // 0xb8dcc0: b               #0xb8dce0
    // 0xb8dcc4: r16 = Instance_FontWeight
    //     0xb8dcc4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb8dcc8: ldr             x16, [x16, #0xe20]
    // 0xb8dccc: str             x16, [SP]
    // 0xb8dcd0: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb8dcd0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb8dcd4: ldr             x4, [x4, #0xfe0]
    // 0xb8dcd8: r0 = copyWith()
    //     0xb8dcd8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8dcdc: mov             x4, x0
    // 0xb8dce0: ldur            x2, [fp, #-0x18]
    // 0xb8dce4: ldur            x1, [fp, #-0x38]
    // 0xb8dce8: ldur            x0, [fp, #-0x30]
    // 0xb8dcec: ldur            x3, [fp, #-8]
    // 0xb8dcf0: stur            x4, [fp, #-0x40]
    // 0xb8dcf4: r0 = Text()
    //     0xb8dcf4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8dcf8: mov             x2, x0
    // 0xb8dcfc: ldur            x0, [fp, #-0x30]
    // 0xb8dd00: stur            x2, [fp, #-0x48]
    // 0xb8dd04: StoreField: r2->field_b = r0
    //     0xb8dd04: stur            w0, [x2, #0xb]
    // 0xb8dd08: ldur            x0, [fp, #-0x40]
    // 0xb8dd0c: StoreField: r2->field_13 = r0
    //     0xb8dd0c: stur            w0, [x2, #0x13]
    // 0xb8dd10: r0 = Instance_TextOverflow
    //     0xb8dd10: add             x0, PP, #0x27, lsl #12  ; [pp+0x27888] Obj!TextOverflow@e35cc1
    //     0xb8dd14: ldr             x0, [x0, #0x888]
    // 0xb8dd18: StoreField: r2->field_2b = r0
    //     0xb8dd18: stur            w0, [x2, #0x2b]
    // 0xb8dd1c: r0 = 4
    //     0xb8dd1c: movz            x0, #0x4
    // 0xb8dd20: StoreField: r2->field_37 = r0
    //     0xb8dd20: stur            w0, [x2, #0x37]
    // 0xb8dd24: ldur            x3, [fp, #-0x18]
    // 0xb8dd28: LoadField: r1 = r3->field_1b
    //     0xb8dd28: ldur            x1, [x3, #0x1b]
    // 0xb8dd2c: r0 = IntExtension.idr()
    //     0xb8dd2c: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb8dd30: stur            x0, [fp, #-0x30]
    // 0xb8dd34: r0 = GetNavigation.textTheme()
    //     0xb8dd34: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8dd38: LoadField: r1 = r0->field_23
    //     0xb8dd38: ldur            w1, [x0, #0x23]
    // 0xb8dd3c: DecompressPointer r1
    //     0xb8dd3c: add             x1, x1, HEAP, lsl #32
    // 0xb8dd40: stur            x1, [fp, #-0x40]
    // 0xb8dd44: r0 = Text()
    //     0xb8dd44: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8dd48: mov             x1, x0
    // 0xb8dd4c: ldur            x0, [fp, #-0x30]
    // 0xb8dd50: stur            x1, [fp, #-0x50]
    // 0xb8dd54: StoreField: r1->field_b = r0
    //     0xb8dd54: stur            w0, [x1, #0xb]
    // 0xb8dd58: ldur            x0, [fp, #-0x40]
    // 0xb8dd5c: StoreField: r1->field_13 = r0
    //     0xb8dd5c: stur            w0, [x1, #0x13]
    // 0xb8dd60: r0 = GetNavigation.theme()
    //     0xb8dd60: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb8dd64: LoadField: r1 = r0->field_4b
    //     0xb8dd64: ldur            w1, [x0, #0x4b]
    // 0xb8dd68: DecompressPointer r1
    //     0xb8dd68: add             x1, x1, HEAP, lsl #32
    // 0xb8dd6c: stur            x1, [fp, #-0x30]
    // 0xb8dd70: r0 = BorderSide()
    //     0xb8dd70: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb8dd74: mov             x1, x0
    // 0xb8dd78: ldur            x0, [fp, #-0x30]
    // 0xb8dd7c: stur            x1, [fp, #-0x40]
    // 0xb8dd80: StoreField: r1->field_7 = r0
    //     0xb8dd80: stur            w0, [x1, #7]
    // 0xb8dd84: d0 = 1.000000
    //     0xb8dd84: fmov            d0, #1.00000000
    // 0xb8dd88: StoreField: r1->field_b = d0
    //     0xb8dd88: stur            d0, [x1, #0xb]
    // 0xb8dd8c: r0 = Instance_BorderStyle
    //     0xb8dd8c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xb8dd90: ldr             x0, [x0, #0x2d0]
    // 0xb8dd94: StoreField: r1->field_13 = r0
    //     0xb8dd94: stur            w0, [x1, #0x13]
    // 0xb8dd98: d0 = -1.000000
    //     0xb8dd98: fmov            d0, #-1.00000000
    // 0xb8dd9c: ArrayStore: r1[0] = d0  ; List_8
    //     0xb8dd9c: stur            d0, [x1, #0x17]
    // 0xb8dda0: r0 = Border()
    //     0xb8dda0: bl              #0x87dce8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xb8dda4: mov             x1, x0
    // 0xb8dda8: r0 = Instance_BorderSide
    //     0xb8dda8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca0] Obj!BorderSide@e1c111
    //     0xb8ddac: ldr             x0, [x0, #0xca0]
    // 0xb8ddb0: stur            x1, [fp, #-0x30]
    // 0xb8ddb4: StoreField: r1->field_7 = r0
    //     0xb8ddb4: stur            w0, [x1, #7]
    // 0xb8ddb8: StoreField: r1->field_b = r0
    //     0xb8ddb8: stur            w0, [x1, #0xb]
    // 0xb8ddbc: ldur            x2, [fp, #-0x40]
    // 0xb8ddc0: StoreField: r1->field_f = r2
    //     0xb8ddc0: stur            w2, [x1, #0xf]
    // 0xb8ddc4: StoreField: r1->field_13 = r0
    //     0xb8ddc4: stur            w0, [x1, #0x13]
    // 0xb8ddc8: r0 = ListTile()
    //     0xb8ddc8: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xb8ddcc: mov             x3, x0
    // 0xb8ddd0: ldur            x0, [fp, #-0x38]
    // 0xb8ddd4: stur            x3, [fp, #-0x40]
    // 0xb8ddd8: StoreField: r3->field_b = r0
    //     0xb8ddd8: stur            w0, [x3, #0xb]
    // 0xb8dddc: ldur            x0, [fp, #-0x48]
    // 0xb8dde0: StoreField: r3->field_f = r0
    //     0xb8dde0: stur            w0, [x3, #0xf]
    // 0xb8dde4: ldur            x0, [fp, #-0x50]
    // 0xb8dde8: StoreField: r3->field_13 = r0
    //     0xb8dde8: stur            w0, [x3, #0x13]
    // 0xb8ddec: r0 = false
    //     0xb8ddec: add             x0, NULL, #0x30  ; false
    // 0xb8ddf0: StoreField: r3->field_1b = r0
    //     0xb8ddf0: stur            w0, [x3, #0x1b]
    // 0xb8ddf4: ldur            x1, [fp, #-0x30]
    // 0xb8ddf8: StoreField: r3->field_27 = r1
    //     0xb8ddf8: stur            w1, [x3, #0x27]
    // 0xb8ddfc: r1 = Instance_EdgeInsets
    //     0xb8ddfc: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb8de00: StoreField: r3->field_47 = r1
    //     0xb8de00: stur            w1, [x3, #0x47]
    // 0xb8de04: StoreField: r3->field_4b = r0
    //     0xb8de04: stur            w0, [x3, #0x4b]
    // 0xb8de08: StoreField: r3->field_5f = r0
    //     0xb8de08: stur            w0, [x3, #0x5f]
    // 0xb8de0c: StoreField: r3->field_73 = r0
    //     0xb8de0c: stur            w0, [x3, #0x73]
    // 0xb8de10: r1 = 12.000000
    //     0xb8de10: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xb8de14: ldr             x1, [x1, #0xc60]
    // 0xb8de18: StoreField: r3->field_83 = r1
    //     0xb8de18: stur            w1, [x3, #0x83]
    // 0xb8de1c: r4 = true
    //     0xb8de1c: add             x4, NULL, #0x20  ; true
    // 0xb8de20: StoreField: r3->field_97 = r4
    //     0xb8de20: stur            w4, [x3, #0x97]
    // 0xb8de24: r1 = <Widget>
    //     0xb8de24: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8de28: r2 = 0
    //     0xb8de28: movz            x2, #0
    // 0xb8de2c: r0 = _GrowableList()
    //     0xb8de2c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8de30: mov             x2, x0
    // 0xb8de34: ldur            x1, [fp, #-8]
    // 0xb8de38: stur            x2, [fp, #-0x30]
    // 0xb8de3c: r0 = LoadClassIdInstr(r1)
    //     0xb8de3c: ldur            x0, [x1, #-1]
    //     0xb8de40: ubfx            x0, x0, #0xc, #0x14
    // 0xb8de44: r16 = "PENDING"
    //     0xb8de44: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8de48: ldr             x16, [x16, #0x2c8]
    // 0xb8de4c: stp             x16, x1, [SP]
    // 0xb8de50: mov             lr, x0
    // 0xb8de54: ldr             lr, [x21, lr, lsl #3]
    // 0xb8de58: blr             lr
    // 0xb8de5c: tbnz            w0, #4, #0xb8e060
    // 0xb8de60: r0 = GetNavigation.textTheme()
    //     0xb8de60: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8de64: LoadField: r1 = r0->field_27
    //     0xb8de64: ldur            w1, [x0, #0x27]
    // 0xb8de68: DecompressPointer r1
    //     0xb8de68: add             x1, x1, HEAP, lsl #32
    // 0xb8de6c: cmp             w1, NULL
    // 0xb8de70: b.ne            #0xb8de7c
    // 0xb8de74: r1 = Null
    //     0xb8de74: mov             x1, NULL
    // 0xb8de78: b               #0xb8de98
    // 0xb8de7c: r16 = 14.000000
    //     0xb8de7c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb8de80: ldr             x16, [x16, #0x9a0]
    // 0xb8de84: str             x16, [SP]
    // 0xb8de88: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb8de88: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb8de8c: ldr             x4, [x4, #0x88]
    // 0xb8de90: r0 = copyWith()
    //     0xb8de90: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8de94: mov             x1, x0
    // 0xb8de98: ldur            x0, [fp, #-0x18]
    // 0xb8de9c: stur            x1, [fp, #-0x38]
    // 0xb8dea0: r0 = Text()
    //     0xb8dea0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8dea4: mov             x2, x0
    // 0xb8dea8: r0 = "Transfer sebelum"
    //     0xb8dea8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34fa0] "Transfer sebelum"
    //     0xb8deac: ldr             x0, [x0, #0xfa0]
    // 0xb8deb0: stur            x2, [fp, #-0x48]
    // 0xb8deb4: StoreField: r2->field_b = r0
    //     0xb8deb4: stur            w0, [x2, #0xb]
    // 0xb8deb8: ldur            x0, [fp, #-0x38]
    // 0xb8debc: StoreField: r2->field_13 = r0
    //     0xb8debc: stur            w0, [x2, #0x13]
    // 0xb8dec0: ldur            x0, [fp, #-0x18]
    // 0xb8dec4: LoadField: r1 = r0->field_4b
    //     0xb8dec4: ldur            w1, [x0, #0x4b]
    // 0xb8dec8: DecompressPointer r1
    //     0xb8dec8: add             x1, x1, HEAP, lsl #32
    // 0xb8decc: r0 = DateTimeExtensions.humanizeShortDateTimeWIB()
    //     0xb8decc: bl              #0xb8e358  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanizeShortDateTimeWIB
    // 0xb8ded0: stur            x0, [fp, #-0x18]
    // 0xb8ded4: r0 = GetNavigation.textTheme()
    //     0xb8ded4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8ded8: LoadField: r1 = r0->field_27
    //     0xb8ded8: ldur            w1, [x0, #0x27]
    // 0xb8dedc: DecompressPointer r1
    //     0xb8dedc: add             x1, x1, HEAP, lsl #32
    // 0xb8dee0: cmp             w1, NULL
    // 0xb8dee4: b.ne            #0xb8def0
    // 0xb8dee8: r3 = Null
    //     0xb8dee8: mov             x3, NULL
    // 0xb8deec: b               #0xb8df14
    // 0xb8def0: r16 = 14.000000
    //     0xb8def0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb8def4: ldr             x16, [x16, #0x9a0]
    // 0xb8def8: r30 = Instance_FontWeight
    //     0xb8def8: add             lr, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb8defc: ldr             lr, [lr, #0xe20]
    // 0xb8df00: stp             lr, x16, [SP]
    // 0xb8df04: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, fontWeight, 0x2, null]
    //     0xb8df04: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cdf8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0xb8df08: ldr             x4, [x4, #0xdf8]
    // 0xb8df0c: r0 = copyWith()
    //     0xb8df0c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8df10: mov             x3, x0
    // 0xb8df14: ldur            x2, [fp, #-0x30]
    // 0xb8df18: ldur            x1, [fp, #-0x48]
    // 0xb8df1c: ldur            x0, [fp, #-0x18]
    // 0xb8df20: stur            x3, [fp, #-0x38]
    // 0xb8df24: r0 = Text()
    //     0xb8df24: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8df28: mov             x3, x0
    // 0xb8df2c: ldur            x0, [fp, #-0x18]
    // 0xb8df30: stur            x3, [fp, #-0x50]
    // 0xb8df34: StoreField: r3->field_b = r0
    //     0xb8df34: stur            w0, [x3, #0xb]
    // 0xb8df38: ldur            x0, [fp, #-0x38]
    // 0xb8df3c: StoreField: r3->field_13 = r0
    //     0xb8df3c: stur            w0, [x3, #0x13]
    // 0xb8df40: r1 = Null
    //     0xb8df40: mov             x1, NULL
    // 0xb8df44: r2 = 4
    //     0xb8df44: movz            x2, #0x4
    // 0xb8df48: r0 = AllocateArray()
    //     0xb8df48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8df4c: mov             x2, x0
    // 0xb8df50: ldur            x0, [fp, #-0x48]
    // 0xb8df54: stur            x2, [fp, #-0x18]
    // 0xb8df58: StoreField: r2->field_f = r0
    //     0xb8df58: stur            w0, [x2, #0xf]
    // 0xb8df5c: ldur            x0, [fp, #-0x50]
    // 0xb8df60: StoreField: r2->field_13 = r0
    //     0xb8df60: stur            w0, [x2, #0x13]
    // 0xb8df64: r1 = <Widget>
    //     0xb8df64: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8df68: r0 = AllocateGrowableArray()
    //     0xb8df68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8df6c: mov             x1, x0
    // 0xb8df70: ldur            x0, [fp, #-0x18]
    // 0xb8df74: stur            x1, [fp, #-0x38]
    // 0xb8df78: StoreField: r1->field_f = r0
    //     0xb8df78: stur            w0, [x1, #0xf]
    // 0xb8df7c: r0 = 4
    //     0xb8df7c: movz            x0, #0x4
    // 0xb8df80: StoreField: r1->field_b = r0
    //     0xb8df80: stur            w0, [x1, #0xb]
    // 0xb8df84: r0 = Column()
    //     0xb8df84: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8df88: mov             x2, x0
    // 0xb8df8c: r0 = Instance_Axis
    //     0xb8df8c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8df90: stur            x2, [fp, #-0x18]
    // 0xb8df94: StoreField: r2->field_f = r0
    //     0xb8df94: stur            w0, [x2, #0xf]
    // 0xb8df98: r3 = Instance_MainAxisAlignment
    //     0xb8df98: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8df9c: ldr             x3, [x3, #0x730]
    // 0xb8dfa0: StoreField: r2->field_13 = r3
    //     0xb8dfa0: stur            w3, [x2, #0x13]
    // 0xb8dfa4: r4 = Instance_MainAxisSize
    //     0xb8dfa4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8dfa8: ldr             x4, [x4, #0x738]
    // 0xb8dfac: ArrayStore: r2[0] = r4  ; List_4
    //     0xb8dfac: stur            w4, [x2, #0x17]
    // 0xb8dfb0: r1 = Instance_CrossAxisAlignment
    //     0xb8dfb0: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb8dfb4: ldr             x1, [x1, #0x68]
    // 0xb8dfb8: StoreField: r2->field_1b = r1
    //     0xb8dfb8: stur            w1, [x2, #0x1b]
    // 0xb8dfbc: r5 = Instance_VerticalDirection
    //     0xb8dfbc: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8dfc0: ldr             x5, [x5, #0x748]
    // 0xb8dfc4: StoreField: r2->field_23 = r5
    //     0xb8dfc4: stur            w5, [x2, #0x23]
    // 0xb8dfc8: r6 = Instance_Clip
    //     0xb8dfc8: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8dfcc: ldr             x6, [x6, #0x750]
    // 0xb8dfd0: StoreField: r2->field_2b = r6
    //     0xb8dfd0: stur            w6, [x2, #0x2b]
    // 0xb8dfd4: StoreField: r2->field_2f = rZR
    //     0xb8dfd4: stur            xzr, [x2, #0x2f]
    // 0xb8dfd8: ldur            x1, [fp, #-0x38]
    // 0xb8dfdc: StoreField: r2->field_b = r1
    //     0xb8dfdc: stur            w1, [x2, #0xb]
    // 0xb8dfe0: ldur            x7, [fp, #-0x30]
    // 0xb8dfe4: LoadField: r1 = r7->field_b
    //     0xb8dfe4: ldur            w1, [x7, #0xb]
    // 0xb8dfe8: LoadField: r8 = r7->field_f
    //     0xb8dfe8: ldur            w8, [x7, #0xf]
    // 0xb8dfec: DecompressPointer r8
    //     0xb8dfec: add             x8, x8, HEAP, lsl #32
    // 0xb8dff0: LoadField: r9 = r8->field_b
    //     0xb8dff0: ldur            w9, [x8, #0xb]
    // 0xb8dff4: r8 = LoadInt32Instr(r1)
    //     0xb8dff4: sbfx            x8, x1, #1, #0x1f
    // 0xb8dff8: stur            x8, [fp, #-0x58]
    // 0xb8dffc: r1 = LoadInt32Instr(r9)
    //     0xb8dffc: sbfx            x1, x9, #1, #0x1f
    // 0xb8e000: cmp             x8, x1
    // 0xb8e004: b.ne            #0xb8e010
    // 0xb8e008: mov             x1, x7
    // 0xb8e00c: r0 = _growToNextCapacity()
    //     0xb8e00c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e010: ldur            x2, [fp, #-0x30]
    // 0xb8e014: ldur            x3, [fp, #-0x58]
    // 0xb8e018: add             x0, x3, #1
    // 0xb8e01c: lsl             x1, x0, #1
    // 0xb8e020: StoreField: r2->field_b = r1
    //     0xb8e020: stur            w1, [x2, #0xb]
    // 0xb8e024: LoadField: r1 = r2->field_f
    //     0xb8e024: ldur            w1, [x2, #0xf]
    // 0xb8e028: DecompressPointer r1
    //     0xb8e028: add             x1, x1, HEAP, lsl #32
    // 0xb8e02c: ldur            x0, [fp, #-0x18]
    // 0xb8e030: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8e030: add             x25, x1, x3, lsl #2
    //     0xb8e034: add             x25, x25, #0xf
    //     0xb8e038: str             w0, [x25]
    //     0xb8e03c: tbz             w0, #0, #0xb8e058
    //     0xb8e040: ldurb           w16, [x1, #-1]
    //     0xb8e044: ldurb           w17, [x0, #-1]
    //     0xb8e048: and             x16, x17, x16, lsr #2
    //     0xb8e04c: tst             x16, HEAP, lsr #32
    //     0xb8e050: b.eq            #0xb8e058
    //     0xb8e054: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8e058: mov             x1, x2
    // 0xb8e05c: b               #0xb8e0bc
    // 0xb8e060: ldur            x2, [fp, #-0x30]
    // 0xb8e064: LoadField: r0 = r2->field_b
    //     0xb8e064: ldur            w0, [x2, #0xb]
    // 0xb8e068: LoadField: r1 = r2->field_f
    //     0xb8e068: ldur            w1, [x2, #0xf]
    // 0xb8e06c: DecompressPointer r1
    //     0xb8e06c: add             x1, x1, HEAP, lsl #32
    // 0xb8e070: LoadField: r3 = r1->field_b
    //     0xb8e070: ldur            w3, [x1, #0xb]
    // 0xb8e074: r4 = LoadInt32Instr(r0)
    //     0xb8e074: sbfx            x4, x0, #1, #0x1f
    // 0xb8e078: stur            x4, [fp, #-0x58]
    // 0xb8e07c: r0 = LoadInt32Instr(r3)
    //     0xb8e07c: sbfx            x0, x3, #1, #0x1f
    // 0xb8e080: cmp             x4, x0
    // 0xb8e084: b.ne            #0xb8e090
    // 0xb8e088: mov             x1, x2
    // 0xb8e08c: r0 = _growToNextCapacity()
    //     0xb8e08c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e090: ldur            x1, [fp, #-0x30]
    // 0xb8e094: ldur            x0, [fp, #-0x58]
    // 0xb8e098: add             x2, x0, #1
    // 0xb8e09c: lsl             x3, x2, #1
    // 0xb8e0a0: StoreField: r1->field_b = r3
    //     0xb8e0a0: stur            w3, [x1, #0xb]
    // 0xb8e0a4: LoadField: r2 = r1->field_f
    //     0xb8e0a4: ldur            w2, [x1, #0xf]
    // 0xb8e0a8: DecompressPointer r2
    //     0xb8e0a8: add             x2, x2, HEAP, lsl #32
    // 0xb8e0ac: add             x3, x2, x0, lsl #2
    // 0xb8e0b0: r16 = Instance_SizedBox
    //     0xb8e0b0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb8e0b4: ldr             x16, [x16, #0xc40]
    // 0xb8e0b8: StoreField: r3->field_f = r16
    //     0xb8e0b8: stur            w16, [x3, #0xf]
    // 0xb8e0bc: r16 = Instance_Size
    //     0xb8e0bc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34fa8] Obj!Size@e2c221
    //     0xb8e0c0: ldr             x16, [x16, #0xfa8]
    // 0xb8e0c4: r30 = Instance_EdgeInsets
    //     0xb8e0c4: add             lr, PP, #0x31, lsl #12  ; [pp+0x31b00] Obj!EdgeInsets@e131e1
    //     0xb8e0c8: ldr             lr, [lr, #0xb00]
    // 0xb8e0cc: stp             lr, x16, [SP]
    // 0xb8e0d0: r4 = const [0, 0x2, 0x2, 0, maximumSize, 0, padding, 0x1, null]
    //     0xb8e0d0: add             x4, PP, #0x34, lsl #12  ; [pp+0x34fb0] List(9) [0, 0x2, 0x2, 0, "maximumSize", 0, "padding", 0x1, Null]
    //     0xb8e0d4: ldr             x4, [x4, #0xfb0]
    // 0xb8e0d8: r0 = styleFrom()
    //     0xb8e0d8: bl              #0xa9bb70  ; [package:flutter/src/material/text_button.dart] TextButton::styleFrom
    // 0xb8e0dc: stur            x0, [fp, #-0x18]
    // 0xb8e0e0: r16 = "PENDING"
    //     0xb8e0e0: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8e0e4: ldr             x16, [x16, #0x2c8]
    // 0xb8e0e8: ldur            lr, [fp, #-8]
    // 0xb8e0ec: stp             lr, x16, [SP]
    // 0xb8e0f0: r0 = ==()
    //     0xb8e0f0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8e0f4: tbnz            w0, #4, #0xb8e104
    // 0xb8e0f8: r2 = "Bayar Sekarang"
    //     0xb8e0f8: add             x2, PP, #0x34, lsl #12  ; [pp+0x347d8] "Bayar Sekarang"
    //     0xb8e0fc: ldr             x2, [x2, #0x7d8]
    // 0xb8e100: b               #0xb8e10c
    // 0xb8e104: r2 = "Lihat Rincian"
    //     0xb8e104: add             x2, PP, #0x34, lsl #12  ; [pp+0x34fb8] "Lihat Rincian"
    //     0xb8e108: ldr             x2, [x2, #0xfb8]
    // 0xb8e10c: ldur            x1, [fp, #-0x30]
    // 0xb8e110: ldur            x0, [fp, #-0x18]
    // 0xb8e114: stur            x2, [fp, #-8]
    // 0xb8e118: r0 = Text()
    //     0xb8e118: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8e11c: mov             x3, x0
    // 0xb8e120: ldur            x0, [fp, #-8]
    // 0xb8e124: stur            x3, [fp, #-0x38]
    // 0xb8e128: StoreField: r3->field_b = r0
    //     0xb8e128: stur            w0, [x3, #0xb]
    // 0xb8e12c: r0 = Instance_TextStyle
    //     0xb8e12c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbf0] Obj!TextStyle@e1b651
    //     0xb8e130: ldr             x0, [x0, #0xbf0]
    // 0xb8e134: StoreField: r3->field_13 = r0
    //     0xb8e134: stur            w0, [x3, #0x13]
    // 0xb8e138: ldur            x2, [fp, #-0x10]
    // 0xb8e13c: r1 = Function '<anonymous closure>':.
    //     0xb8e13c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34fc0] AnonymousClosure: (0xb8ec34), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] DonationHistoryItem::build (0xb8d9c0)
    //     0xb8e140: ldr             x1, [x1, #0xfc0]
    // 0xb8e144: r0 = AllocateClosure()
    //     0xb8e144: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8e148: stur            x0, [fp, #-8]
    // 0xb8e14c: r0 = TextButton()
    //     0xb8e14c: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb8e150: mov             x2, x0
    // 0xb8e154: ldur            x0, [fp, #-8]
    // 0xb8e158: stur            x2, [fp, #-0x10]
    // 0xb8e15c: StoreField: r2->field_b = r0
    //     0xb8e15c: stur            w0, [x2, #0xb]
    // 0xb8e160: ldur            x0, [fp, #-0x18]
    // 0xb8e164: StoreField: r2->field_1b = r0
    //     0xb8e164: stur            w0, [x2, #0x1b]
    // 0xb8e168: r0 = false
    //     0xb8e168: add             x0, NULL, #0x30  ; false
    // 0xb8e16c: StoreField: r2->field_27 = r0
    //     0xb8e16c: stur            w0, [x2, #0x27]
    // 0xb8e170: r0 = true
    //     0xb8e170: add             x0, NULL, #0x20  ; true
    // 0xb8e174: StoreField: r2->field_2f = r0
    //     0xb8e174: stur            w0, [x2, #0x2f]
    // 0xb8e178: ldur            x0, [fp, #-0x38]
    // 0xb8e17c: StoreField: r2->field_37 = r0
    //     0xb8e17c: stur            w0, [x2, #0x37]
    // 0xb8e180: ldur            x0, [fp, #-0x30]
    // 0xb8e184: LoadField: r1 = r0->field_b
    //     0xb8e184: ldur            w1, [x0, #0xb]
    // 0xb8e188: LoadField: r3 = r0->field_f
    //     0xb8e188: ldur            w3, [x0, #0xf]
    // 0xb8e18c: DecompressPointer r3
    //     0xb8e18c: add             x3, x3, HEAP, lsl #32
    // 0xb8e190: LoadField: r4 = r3->field_b
    //     0xb8e190: ldur            w4, [x3, #0xb]
    // 0xb8e194: r3 = LoadInt32Instr(r1)
    //     0xb8e194: sbfx            x3, x1, #1, #0x1f
    // 0xb8e198: stur            x3, [fp, #-0x58]
    // 0xb8e19c: r1 = LoadInt32Instr(r4)
    //     0xb8e19c: sbfx            x1, x4, #1, #0x1f
    // 0xb8e1a0: cmp             x3, x1
    // 0xb8e1a4: b.ne            #0xb8e1b0
    // 0xb8e1a8: mov             x1, x0
    // 0xb8e1ac: r0 = _growToNextCapacity()
    //     0xb8e1ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e1b0: ldur            x5, [fp, #-0x28]
    // 0xb8e1b4: ldur            x4, [fp, #-0x40]
    // 0xb8e1b8: ldur            x2, [fp, #-0x30]
    // 0xb8e1bc: ldur            x3, [fp, #-0x58]
    // 0xb8e1c0: add             x0, x3, #1
    // 0xb8e1c4: lsl             x1, x0, #1
    // 0xb8e1c8: StoreField: r2->field_b = r1
    //     0xb8e1c8: stur            w1, [x2, #0xb]
    // 0xb8e1cc: LoadField: r1 = r2->field_f
    //     0xb8e1cc: ldur            w1, [x2, #0xf]
    // 0xb8e1d0: DecompressPointer r1
    //     0xb8e1d0: add             x1, x1, HEAP, lsl #32
    // 0xb8e1d4: ldur            x0, [fp, #-0x10]
    // 0xb8e1d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8e1d8: add             x25, x1, x3, lsl #2
    //     0xb8e1dc: add             x25, x25, #0xf
    //     0xb8e1e0: str             w0, [x25]
    //     0xb8e1e4: tbz             w0, #0, #0xb8e200
    //     0xb8e1e8: ldurb           w16, [x1, #-1]
    //     0xb8e1ec: ldurb           w17, [x0, #-1]
    //     0xb8e1f0: and             x16, x17, x16, lsr #2
    //     0xb8e1f4: tst             x16, HEAP, lsr #32
    //     0xb8e1f8: b.eq            #0xb8e200
    //     0xb8e1fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8e200: r0 = Row()
    //     0xb8e200: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8e204: mov             x3, x0
    // 0xb8e208: r0 = Instance_Axis
    //     0xb8e208: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8e20c: stur            x3, [fp, #-8]
    // 0xb8e210: StoreField: r3->field_f = r0
    //     0xb8e210: stur            w0, [x3, #0xf]
    // 0xb8e214: r0 = Instance_MainAxisAlignment
    //     0xb8e214: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb8e218: ldr             x0, [x0, #0xae8]
    // 0xb8e21c: StoreField: r3->field_13 = r0
    //     0xb8e21c: stur            w0, [x3, #0x13]
    // 0xb8e220: r0 = Instance_MainAxisSize
    //     0xb8e220: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8e224: ldr             x0, [x0, #0x738]
    // 0xb8e228: ArrayStore: r3[0] = r0  ; List_4
    //     0xb8e228: stur            w0, [x3, #0x17]
    // 0xb8e22c: r4 = Instance_CrossAxisAlignment
    //     0xb8e22c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8e230: ldr             x4, [x4, #0x740]
    // 0xb8e234: StoreField: r3->field_1b = r4
    //     0xb8e234: stur            w4, [x3, #0x1b]
    // 0xb8e238: r5 = Instance_VerticalDirection
    //     0xb8e238: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8e23c: ldr             x5, [x5, #0x748]
    // 0xb8e240: StoreField: r3->field_23 = r5
    //     0xb8e240: stur            w5, [x3, #0x23]
    // 0xb8e244: r6 = Instance_Clip
    //     0xb8e244: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8e248: ldr             x6, [x6, #0x750]
    // 0xb8e24c: StoreField: r3->field_2b = r6
    //     0xb8e24c: stur            w6, [x3, #0x2b]
    // 0xb8e250: StoreField: r3->field_2f = rZR
    //     0xb8e250: stur            xzr, [x3, #0x2f]
    // 0xb8e254: ldur            x1, [fp, #-0x30]
    // 0xb8e258: StoreField: r3->field_b = r1
    //     0xb8e258: stur            w1, [x3, #0xb]
    // 0xb8e25c: r1 = Null
    //     0xb8e25c: mov             x1, NULL
    // 0xb8e260: r2 = 8
    //     0xb8e260: movz            x2, #0x8
    // 0xb8e264: r0 = AllocateArray()
    //     0xb8e264: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8e268: mov             x2, x0
    // 0xb8e26c: ldur            x0, [fp, #-0x28]
    // 0xb8e270: stur            x2, [fp, #-0x10]
    // 0xb8e274: StoreField: r2->field_f = r0
    //     0xb8e274: stur            w0, [x2, #0xf]
    // 0xb8e278: ldur            x0, [fp, #-0x40]
    // 0xb8e27c: StoreField: r2->field_13 = r0
    //     0xb8e27c: stur            w0, [x2, #0x13]
    // 0xb8e280: r16 = Instance_SizedBox
    //     0xb8e280: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb8e284: ldr             x16, [x16, #0x950]
    // 0xb8e288: ArrayStore: r2[0] = r16  ; List_4
    //     0xb8e288: stur            w16, [x2, #0x17]
    // 0xb8e28c: ldur            x0, [fp, #-8]
    // 0xb8e290: StoreField: r2->field_1b = r0
    //     0xb8e290: stur            w0, [x2, #0x1b]
    // 0xb8e294: r1 = <Widget>
    //     0xb8e294: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8e298: r0 = AllocateGrowableArray()
    //     0xb8e298: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8e29c: mov             x1, x0
    // 0xb8e2a0: ldur            x0, [fp, #-0x10]
    // 0xb8e2a4: stur            x1, [fp, #-8]
    // 0xb8e2a8: StoreField: r1->field_f = r0
    //     0xb8e2a8: stur            w0, [x1, #0xf]
    // 0xb8e2ac: r0 = 8
    //     0xb8e2ac: movz            x0, #0x8
    // 0xb8e2b0: StoreField: r1->field_b = r0
    //     0xb8e2b0: stur            w0, [x1, #0xb]
    // 0xb8e2b4: r0 = Column()
    //     0xb8e2b4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8e2b8: mov             x1, x0
    // 0xb8e2bc: r0 = Instance_Axis
    //     0xb8e2bc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8e2c0: stur            x1, [fp, #-0x10]
    // 0xb8e2c4: StoreField: r1->field_f = r0
    //     0xb8e2c4: stur            w0, [x1, #0xf]
    // 0xb8e2c8: r0 = Instance_MainAxisAlignment
    //     0xb8e2c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8e2cc: ldr             x0, [x0, #0x730]
    // 0xb8e2d0: StoreField: r1->field_13 = r0
    //     0xb8e2d0: stur            w0, [x1, #0x13]
    // 0xb8e2d4: r0 = Instance_MainAxisSize
    //     0xb8e2d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8e2d8: ldr             x0, [x0, #0x738]
    // 0xb8e2dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8e2dc: stur            w0, [x1, #0x17]
    // 0xb8e2e0: r0 = Instance_CrossAxisAlignment
    //     0xb8e2e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb8e2e4: ldr             x0, [x0, #0x740]
    // 0xb8e2e8: StoreField: r1->field_1b = r0
    //     0xb8e2e8: stur            w0, [x1, #0x1b]
    // 0xb8e2ec: r0 = Instance_VerticalDirection
    //     0xb8e2ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8e2f0: ldr             x0, [x0, #0x748]
    // 0xb8e2f4: StoreField: r1->field_23 = r0
    //     0xb8e2f4: stur            w0, [x1, #0x23]
    // 0xb8e2f8: r0 = Instance_Clip
    //     0xb8e2f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8e2fc: ldr             x0, [x0, #0x750]
    // 0xb8e300: StoreField: r1->field_2b = r0
    //     0xb8e300: stur            w0, [x1, #0x2b]
    // 0xb8e304: StoreField: r1->field_2f = rZR
    //     0xb8e304: stur            xzr, [x1, #0x2f]
    // 0xb8e308: ldur            x0, [fp, #-8]
    // 0xb8e30c: StoreField: r1->field_b = r0
    //     0xb8e30c: stur            w0, [x1, #0xb]
    // 0xb8e310: r0 = Container()
    //     0xb8e310: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8e314: stur            x0, [fp, #-8]
    // 0xb8e318: ldur            x16, [fp, #-0x20]
    // 0xb8e31c: r30 = Instance_EdgeInsets
    //     0xb8e31c: add             lr, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb8e320: ldr             lr, [lr, #0x768]
    // 0xb8e324: stp             lr, x16, [SP, #8]
    // 0xb8e328: ldur            x16, [fp, #-0x10]
    // 0xb8e32c: str             x16, [SP]
    // 0xb8e330: mov             x1, x0
    // 0xb8e334: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb8e334: add             x4, PP, #0x32, lsl #12  ; [pp+0x323e0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb8e338: ldr             x4, [x4, #0x3e0]
    // 0xb8e33c: r0 = Container()
    //     0xb8e33c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8e340: ldur            x0, [fp, #-8]
    // 0xb8e344: LeaveFrame
    //     0xb8e344: mov             SP, fp
    //     0xb8e348: ldp             fp, lr, [SP], #0x10
    // 0xb8e34c: ret
    //     0xb8e34c: ret             
    // 0xb8e350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e354: b               #0xb8d9dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8ec34, size: 0x100
    // 0xb8ec34: EnterFrame
    //     0xb8ec34: stp             fp, lr, [SP, #-0x10]!
    //     0xb8ec38: mov             fp, SP
    // 0xb8ec3c: AllocStack(0x20)
    //     0xb8ec3c: sub             SP, SP, #0x20
    // 0xb8ec40: SetupParameters()
    //     0xb8ec40: ldr             x0, [fp, #0x10]
    //     0xb8ec44: ldur            w1, [x0, #0x17]
    //     0xb8ec48: add             x1, x1, HEAP, lsl #32
    //     0xb8ec4c: stur            x1, [fp, #-8]
    // 0xb8ec50: CheckStackOverflow
    //     0xb8ec50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ec54: cmp             SP, x16
    //     0xb8ec58: b.ls            #0xb8ed2c
    // 0xb8ec5c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8ec5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8ec60: ldr             x0, [x0, #0x2670]
    //     0xb8ec64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8ec68: cmp             w0, w16
    //     0xb8ec6c: b.ne            #0xb8ec78
    //     0xb8ec70: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8ec74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8ec78: r1 = Null
    //     0xb8ec78: mov             x1, NULL
    // 0xb8ec7c: r2 = 4
    //     0xb8ec7c: movz            x2, #0x4
    // 0xb8ec80: r0 = AllocateArray()
    //     0xb8ec80: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8ec84: mov             x2, x0
    // 0xb8ec88: r16 = "id"
    //     0xb8ec88: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8ec8c: ldr             x16, [x16, #0x740]
    // 0xb8ec90: StoreField: r2->field_f = r16
    //     0xb8ec90: stur            w16, [x2, #0xf]
    // 0xb8ec94: ldur            x0, [fp, #-8]
    // 0xb8ec98: LoadField: r1 = r0->field_f
    //     0xb8ec98: ldur            w1, [x0, #0xf]
    // 0xb8ec9c: DecompressPointer r1
    //     0xb8ec9c: add             x1, x1, HEAP, lsl #32
    // 0xb8eca0: LoadField: r0 = r1->field_b
    //     0xb8eca0: ldur            w0, [x1, #0xb]
    // 0xb8eca4: DecompressPointer r0
    //     0xb8eca4: add             x0, x0, HEAP, lsl #32
    // 0xb8eca8: LoadField: r3 = r0->field_7
    //     0xb8eca8: ldur            x3, [x0, #7]
    // 0xb8ecac: r0 = BoxInt64Instr(r3)
    //     0xb8ecac: sbfiz           x0, x3, #1, #0x1f
    //     0xb8ecb0: cmp             x3, x0, asr #1
    //     0xb8ecb4: b.eq            #0xb8ecc0
    //     0xb8ecb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8ecbc: stur            x3, [x0, #7]
    // 0xb8ecc0: StoreField: r2->field_13 = r0
    //     0xb8ecc0: stur            w0, [x2, #0x13]
    // 0xb8ecc4: r16 = <String, int>
    //     0xb8ecc4: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb8ecc8: stp             x2, x16, [SP]
    // 0xb8eccc: r0 = Map._fromLiteral()
    //     0xb8eccc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8ecd0: r16 = "/donation/pay-donation-detail"
    //     0xb8ecd0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34fc8] "/donation/pay-donation-detail"
    //     0xb8ecd4: ldr             x16, [x16, #0xfc8]
    // 0xb8ecd8: stp             x16, NULL, [SP, #8]
    // 0xb8ecdc: str             x0, [SP]
    // 0xb8ece0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8ece0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8ece4: ldr             x4, [x4, #0x478]
    // 0xb8ece8: r0 = GetNavigation.toNamed()
    //     0xb8ece8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8ecec: stur            x0, [fp, #-8]
    // 0xb8ecf0: cmp             w0, NULL
    // 0xb8ecf4: b.ne            #0xb8ed00
    // 0xb8ecf8: r0 = Null
    //     0xb8ecf8: mov             x0, NULL
    // 0xb8ecfc: b               #0xb8ed20
    // 0xb8ed00: r0 = find()
    //     0xb8ed00: bl              #0xb8ed34  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::find
    // 0xb8ed04: mov             x2, x0
    // 0xb8ed08: r1 = Function 'onPageRefresh':.
    //     0xb8ed08: add             x1, PP, #0x34, lsl #12  ; [pp+0x34fd0] AnonymousClosure: (0xb8ed90), in [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryTabController::onPageRefresh (0xb8edc8)
    //     0xb8ed0c: ldr             x1, [x1, #0xfd0]
    // 0xb8ed10: r0 = AllocateClosure()
    //     0xb8ed10: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8ed14: ldur            x1, [fp, #-8]
    // 0xb8ed18: mov             x2, x0
    // 0xb8ed1c: r0 = whenComplete()
    //     0xb8ed1c: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0xb8ed20: LeaveFrame
    //     0xb8ed20: mov             SP, fp
    //     0xb8ed24: ldp             fp, lr, [SP], #0x10
    // 0xb8ed28: ret
    //     0xb8ed28: ret             
    // 0xb8ed2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ed2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8ed30: b               #0xb8ec5c
  }
}

// class id: 5043, size: 0x10, field offset: 0xc
//   const constructor, 
class _HistoryBuilder extends StatelessWidget {

  HistoryStatus field_c;

  _ build(/* No info */) {
    // ** addr: 0xb8d838, size: 0x104
    // 0xb8d838: EnterFrame
    //     0xb8d838: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d83c: mov             fp, SP
    // 0xb8d840: AllocStack(0x30)
    //     0xb8d840: sub             SP, SP, #0x30
    // 0xb8d844: CheckStackOverflow
    //     0xb8d844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d848: cmp             SP, x16
    //     0xb8d84c: b.ls            #0xb8d934
    // 0xb8d850: LoadField: r0 = r1->field_b
    //     0xb8d850: ldur            w0, [x1, #0xb]
    // 0xb8d854: DecompressPointer r0
    //     0xb8d854: add             x0, x0, HEAP, lsl #32
    // 0xb8d858: stur            x0, [fp, #-0x10]
    // 0xb8d85c: LoadField: r1 = r0->field_f
    //     0xb8d85c: ldur            w1, [x0, #0xf]
    // 0xb8d860: DecompressPointer r1
    //     0xb8d860: add             x1, x1, HEAP, lsl #32
    // 0xb8d864: stur            x1, [fp, #-8]
    // 0xb8d868: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8d868: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8d86c: ldr             x0, [x0, #0x2670]
    //     0xb8d870: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8d874: cmp             w0, w16
    //     0xb8d878: b.ne            #0xb8d884
    //     0xb8d87c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8d880: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8d884: r16 = <DonationRepository>
    //     0xb8d884: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xb8d888: ldr             x16, [x16, #0xb0]
    // 0xb8d88c: r30 = "donation_repo"
    //     0xb8d88c: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xb8d890: ldr             lr, [lr, #0xb8]
    // 0xb8d894: stp             lr, x16, [SP]
    // 0xb8d898: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb8d898: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb8d89c: r0 = Inst.find()
    //     0xb8d89c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb8d8a0: r1 = <List<Transaction>, Transaction>
    //     0xb8d8a0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af18] TypeArguments: <List<Transaction>, Transaction>
    //     0xb8d8a4: ldr             x1, [x1, #0xf18]
    // 0xb8d8a8: stur            x0, [fp, #-0x18]
    // 0xb8d8ac: r0 = HistoryController()
    //     0xb8d8ac: bl              #0xb3e584  ; AllocateHistoryControllerStub -> HistoryController (size=0x40)
    // 0xb8d8b0: stur            x0, [fp, #-0x20]
    // 0xb8d8b4: ldur            x16, [fp, #-0x10]
    // 0xb8d8b8: str             x16, [SP]
    // 0xb8d8bc: mov             x1, x0
    // 0xb8d8c0: ldur            x2, [fp, #-0x18]
    // 0xb8d8c4: r4 = const [0, 0x3, 0x1, 0x2, status, 0x2, null]
    //     0xb8d8c4: add             x4, PP, #0x34, lsl #12  ; [pp+0x34f50] List(7) [0, 0x3, 0x1, 0x2, "status", 0x2, Null]
    //     0xb8d8c8: ldr             x4, [x4, #0xf50]
    // 0xb8d8cc: r0 = HistoryController()
    //     0xb8d8cc: bl              #0xb3e438  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryController::HistoryController
    // 0xb8d8d0: r1 = <HistoryController>
    //     0xb8d8d0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb8d8d4: ldr             x1, [x1, #0xf28]
    // 0xb8d8d8: r0 = GetBuilder()
    //     0xb8d8d8: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb8d8dc: mov             x3, x0
    // 0xb8d8e0: ldur            x0, [fp, #-0x20]
    // 0xb8d8e4: stur            x3, [fp, #-0x10]
    // 0xb8d8e8: StoreField: r3->field_3b = r0
    //     0xb8d8e8: stur            w0, [x3, #0x3b]
    // 0xb8d8ec: r0 = true
    //     0xb8d8ec: add             x0, NULL, #0x20  ; true
    // 0xb8d8f0: StoreField: r3->field_13 = r0
    //     0xb8d8f0: stur            w0, [x3, #0x13]
    // 0xb8d8f4: r1 = Function '<anonymous closure>':.
    //     0xb8d8f4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f58] AnonymousClosure: (0xb8d93c), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] _HistoryBuilder::build (0xb8d838)
    //     0xb8d8f8: ldr             x1, [x1, #0xf58]
    // 0xb8d8fc: r2 = Null
    //     0xb8d8fc: mov             x2, NULL
    // 0xb8d900: r0 = AllocateClosure()
    //     0xb8d900: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d904: mov             x1, x0
    // 0xb8d908: ldur            x0, [fp, #-0x10]
    // 0xb8d90c: StoreField: r0->field_f = r1
    //     0xb8d90c: stur            w1, [x0, #0xf]
    // 0xb8d910: r1 = true
    //     0xb8d910: add             x1, NULL, #0x20  ; true
    // 0xb8d914: StoreField: r0->field_1f = r1
    //     0xb8d914: stur            w1, [x0, #0x1f]
    // 0xb8d918: r1 = false
    //     0xb8d918: add             x1, NULL, #0x30  ; false
    // 0xb8d91c: StoreField: r0->field_23 = r1
    //     0xb8d91c: stur            w1, [x0, #0x23]
    // 0xb8d920: ldur            x1, [fp, #-8]
    // 0xb8d924: StoreField: r0->field_1b = r1
    //     0xb8d924: stur            w1, [x0, #0x1b]
    // 0xb8d928: LeaveFrame
    //     0xb8d928: mov             SP, fp
    //     0xb8d92c: ldp             fp, lr, [SP], #0x10
    // 0xb8d930: ret
    //     0xb8d930: ret             
    // 0xb8d934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d938: b               #0xb8d850
  }
  [closure] Widget <anonymous closure>(dynamic, HistoryController) {
    // ** addr: 0xb8d93c, size: 0x78
    // 0xb8d93c: EnterFrame
    //     0xb8d93c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d940: mov             fp, SP
    // 0xb8d944: AllocStack(0x18)
    //     0xb8d944: sub             SP, SP, #0x18
    // 0xb8d948: CheckStackOverflow
    //     0xb8d948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d94c: cmp             SP, x16
    //     0xb8d950: b.ls            #0xb8d9ac
    // 0xb8d954: r1 = Function '<anonymous closure>':.
    //     0xb8d954: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f60] AnonymousClosure: (0xb3e6e0), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb8d958: ldr             x1, [x1, #0xf60]
    // 0xb8d95c: r2 = Null
    //     0xb8d95c: mov             x2, NULL
    // 0xb8d960: r0 = AllocateClosure()
    //     0xb8d960: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d964: r1 = Function '<anonymous closure>':.
    //     0xb8d964: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f68] AnonymousClosure: (0xb8d9b4), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] _HistoryBuilder::build (0xb8d838)
    //     0xb8d968: ldr             x1, [x1, #0xf68]
    // 0xb8d96c: r2 = Null
    //     0xb8d96c: mov             x2, NULL
    // 0xb8d970: stur            x0, [fp, #-8]
    // 0xb8d974: r0 = AllocateClosure()
    //     0xb8d974: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8d978: r16 = Instance_EdgeInsets
    //     0xb8d978: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af58] Obj!EdgeInsets@e125e1
    //     0xb8d97c: ldr             x16, [x16, #0xf58]
    // 0xb8d980: stp             x0, x16, [SP]
    // 0xb8d984: ldr             x1, [fp, #0x10]
    // 0xb8d988: ldur            x2, [fp, #-8]
    // 0xb8d98c: r3 = Instance_SizedBox
    //     0xb8d98c: add             x3, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb8d990: ldr             x3, [x3, #0x950]
    // 0xb8d994: r4 = const [0, 0x5, 0x2, 0x3, empty, 0x4, padding, 0x3, null]
    //     0xb8d994: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2af60] List(9) [0, 0x5, 0x2, 0x3, "empty", 0x4, "padding", 0x3, Null]
    //     0xb8d998: ldr             x4, [x4, #0xf60]
    // 0xb8d99c: r0 = paginate()
    //     0xb8d99c: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xb8d9a0: LeaveFrame
    //     0xb8d9a0: mov             SP, fp
    //     0xb8d9a4: ldp             fp, lr, [SP], #0x10
    // 0xb8d9a8: ret
    //     0xb8d9a8: ret             
    // 0xb8d9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d9b0: b               #0xb8d954
  }
  [closure] DonationHistoryEmptyState <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb8d9b4, size: 0xc
    // 0xb8d9b4: r0 = Instance_DonationHistoryEmptyState
    //     0xb8d9b4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f70] Obj!DonationHistoryEmptyState@e1fa41
    //     0xb8d9b8: ldr             x0, [x0, #0xf70]
    // 0xb8d9bc: ret
    //     0xb8d9bc: ret             
  }
}

// class id: 5292, size: 0x14, field offset: 0x14
class HistoryTabView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae34d8, size: 0x2f4
    // 0xae34d8: EnterFrame
    //     0xae34d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae34dc: mov             fp, SP
    // 0xae34e0: AllocStack(0x30)
    //     0xae34e0: sub             SP, SP, #0x30
    // 0xae34e4: SetupParameters(HistoryTabView this /* r1 => r1, fp-0x8 */)
    //     0xae34e4: stur            x1, [fp, #-8]
    // 0xae34e8: CheckStackOverflow
    //     0xae34e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae34ec: cmp             SP, x16
    //     0xae34f0: b.ls            #0xae37c4
    // 0xae34f4: r1 = 1
    //     0xae34f4: movz            x1, #0x1
    // 0xae34f8: r0 = AllocateContext()
    //     0xae34f8: bl              #0xec126c  ; AllocateContextStub
    // 0xae34fc: ldur            x1, [fp, #-8]
    // 0xae3500: stur            x0, [fp, #-0x10]
    // 0xae3504: StoreField: r0->field_f = r1
    //     0xae3504: stur            w1, [x0, #0xf]
    // 0xae3508: r0 = AppBar()
    //     0xae3508: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae350c: stur            x0, [fp, #-0x18]
    // 0xae3510: r16 = Instance_Text
    //     0xae3510: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f508] Obj!Text@e21b91
    //     0xae3514: ldr             x16, [x16, #0x508]
    // 0xae3518: str             x16, [SP]
    // 0xae351c: mov             x1, x0
    // 0xae3520: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae3520: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae3524: ldr             x4, [x4, #0x6e8]
    // 0xae3528: r0 = AppBar()
    //     0xae3528: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae352c: ldur            x1, [fp, #-8]
    // 0xae3530: r0 = controller()
    //     0xae3530: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3534: mov             x1, x0
    // 0xae3538: LoadField: r0 = r1->field_27
    //     0xae3538: ldur            w0, [x1, #0x27]
    // 0xae353c: DecompressPointer r0
    //     0xae353c: add             x0, x0, HEAP, lsl #32
    // 0xae3540: r16 = Sentinel
    //     0xae3540: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3544: cmp             w0, w16
    // 0xae3548: b.ne            #0xae3558
    // 0xae354c: r2 = tabController
    //     0xae354c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f510] Field <HistoryTabController.tabController>: late final (offset: 0x28)
    //     0xae3550: ldr             x2, [x2, #0x510]
    // 0xae3554: r0 = InitLateFinalInstanceField()
    //     0xae3554: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3558: stur            x0, [fp, #-0x20]
    // 0xae355c: r0 = Obx()
    //     0xae355c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae3560: ldur            x2, [fp, #-0x10]
    // 0xae3564: r1 = Function '<anonymous closure>':.
    //     0xae3564: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f518] AnonymousClosure: (0xae37d8), in [package:nuonline/app/modules/donation/views/history_tab_view.dart] HistoryTabView::build (0xae34d8)
    //     0xae3568: ldr             x1, [x1, #0x518]
    // 0xae356c: stur            x0, [fp, #-0x10]
    // 0xae3570: r0 = AllocateClosure()
    //     0xae3570: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3574: mov             x1, x0
    // 0xae3578: ldur            x0, [fp, #-0x10]
    // 0xae357c: StoreField: r0->field_b = r1
    //     0xae357c: stur            w1, [x0, #0xb]
    // 0xae3580: r0 = Tab()
    //     0xae3580: bl              #0xae37cc  ; AllocateTabStub -> Tab (size=0x20)
    // 0xae3584: mov             x3, x0
    // 0xae3588: ldur            x0, [fp, #-0x10]
    // 0xae358c: stur            x3, [fp, #-0x28]
    // 0xae3590: StoreField: r3->field_f = r0
    //     0xae3590: stur            w0, [x3, #0xf]
    // 0xae3594: r1 = Null
    //     0xae3594: mov             x1, NULL
    // 0xae3598: r2 = 6
    //     0xae3598: movz            x2, #0x6
    // 0xae359c: r0 = AllocateArray()
    //     0xae359c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae35a0: stur            x0, [fp, #-0x10]
    // 0xae35a4: r16 = Instance_Tab
    //     0xae35a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f520] Obj!Tab@e25471
    //     0xae35a8: ldr             x16, [x16, #0x520]
    // 0xae35ac: StoreField: r0->field_f = r16
    //     0xae35ac: stur            w16, [x0, #0xf]
    // 0xae35b0: ldur            x1, [fp, #-0x28]
    // 0xae35b4: StoreField: r0->field_13 = r1
    //     0xae35b4: stur            w1, [x0, #0x13]
    // 0xae35b8: r16 = Instance_Tab
    //     0xae35b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f528] Obj!Tab@e25451
    //     0xae35bc: ldr             x16, [x16, #0x528]
    // 0xae35c0: ArrayStore: r0[0] = r16  ; List_4
    //     0xae35c0: stur            w16, [x0, #0x17]
    // 0xae35c4: r1 = <Widget>
    //     0xae35c4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae35c8: r0 = AllocateGrowableArray()
    //     0xae35c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae35cc: mov             x1, x0
    // 0xae35d0: ldur            x0, [fp, #-0x10]
    // 0xae35d4: stur            x1, [fp, #-0x28]
    // 0xae35d8: StoreField: r1->field_f = r0
    //     0xae35d8: stur            w0, [x1, #0xf]
    // 0xae35dc: r0 = 6
    //     0xae35dc: movz            x0, #0x6
    // 0xae35e0: StoreField: r1->field_b = r0
    //     0xae35e0: stur            w0, [x1, #0xb]
    // 0xae35e4: r0 = TabBar()
    //     0xae35e4: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xae35e8: mov             x2, x0
    // 0xae35ec: ldur            x0, [fp, #-0x28]
    // 0xae35f0: stur            x2, [fp, #-0x10]
    // 0xae35f4: StoreField: r2->field_b = r0
    //     0xae35f4: stur            w0, [x2, #0xb]
    // 0xae35f8: ldur            x0, [fp, #-0x20]
    // 0xae35fc: StoreField: r2->field_f = r0
    //     0xae35fc: stur            w0, [x2, #0xf]
    // 0xae3600: r0 = false
    //     0xae3600: add             x0, NULL, #0x30  ; false
    // 0xae3604: StoreField: r2->field_13 = r0
    //     0xae3604: stur            w0, [x2, #0x13]
    // 0xae3608: r3 = true
    //     0xae3608: add             x3, NULL, #0x20  ; true
    // 0xae360c: StoreField: r2->field_2f = r3
    //     0xae360c: stur            w3, [x2, #0x2f]
    // 0xae3610: d0 = 2.000000
    //     0xae3610: fmov            d0, #2.00000000
    // 0xae3614: StoreField: r2->field_1f = d0
    //     0xae3614: stur            d0, [x2, #0x1f]
    // 0xae3618: r1 = Instance_EdgeInsets
    //     0xae3618: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xae361c: StoreField: r2->field_27 = r1
    //     0xae361c: stur            w1, [x2, #0x27]
    // 0xae3620: r1 = Instance_TabBarIndicatorSize
    //     0xae3620: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b900] Obj!TabBarIndicatorSize@e361a1
    //     0xae3624: ldr             x1, [x1, #0x900]
    // 0xae3628: StoreField: r2->field_33 = r1
    //     0xae3628: stur            w1, [x2, #0x33]
    // 0xae362c: r4 = Instance_DragStartBehavior
    //     0xae362c: ldr             x4, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae3630: StoreField: r2->field_57 = r4
    //     0xae3630: stur            w4, [x2, #0x57]
    // 0xae3634: StoreField: r2->field_7f = r3
    //     0xae3634: stur            w3, [x2, #0x7f]
    // 0xae3638: ldur            x1, [fp, #-8]
    // 0xae363c: r0 = controller()
    //     0xae363c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3640: mov             x1, x0
    // 0xae3644: LoadField: r0 = r1->field_27
    //     0xae3644: ldur            w0, [x1, #0x27]
    // 0xae3648: DecompressPointer r0
    //     0xae3648: add             x0, x0, HEAP, lsl #32
    // 0xae364c: r16 = Sentinel
    //     0xae364c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3650: cmp             w0, w16
    // 0xae3654: b.ne            #0xae3664
    // 0xae3658: r2 = tabController
    //     0xae3658: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f510] Field <HistoryTabController.tabController>: late final (offset: 0x28)
    //     0xae365c: ldr             x2, [x2, #0x510]
    // 0xae3660: r0 = InitLateFinalInstanceField()
    //     0xae3660: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3664: stur            x0, [fp, #-8]
    // 0xae3668: r0 = TabBarView()
    //     0xae3668: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xae366c: mov             x2, x0
    // 0xae3670: r0 = const [Instance of '_HistoryBuilder', Instance of '_HistoryBuilder', Instance of '_HistoryBuilder']
    //     0xae3670: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f530] List<Widget>(3)
    //     0xae3674: ldr             x0, [x0, #0x530]
    // 0xae3678: stur            x2, [fp, #-0x20]
    // 0xae367c: StoreField: r2->field_f = r0
    //     0xae367c: stur            w0, [x2, #0xf]
    // 0xae3680: ldur            x0, [fp, #-8]
    // 0xae3684: StoreField: r2->field_b = r0
    //     0xae3684: stur            w0, [x2, #0xb]
    // 0xae3688: r0 = Instance_DragStartBehavior
    //     0xae3688: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae368c: ArrayStore: r2[0] = r0  ; List_4
    //     0xae368c: stur            w0, [x2, #0x17]
    // 0xae3690: d0 = 1.000000
    //     0xae3690: fmov            d0, #1.00000000
    // 0xae3694: StoreField: r2->field_1b = d0
    //     0xae3694: stur            d0, [x2, #0x1b]
    // 0xae3698: r1 = Instance_Clip
    //     0xae3698: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xae369c: ldr             x1, [x1, #0x7c0]
    // 0xae36a0: StoreField: r2->field_23 = r1
    //     0xae36a0: stur            w1, [x2, #0x23]
    // 0xae36a4: r1 = <FlexParentData>
    //     0xae36a4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xae36a8: ldr             x1, [x1, #0x720]
    // 0xae36ac: r0 = Expanded()
    //     0xae36ac: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae36b0: mov             x3, x0
    // 0xae36b4: r0 = 1
    //     0xae36b4: movz            x0, #0x1
    // 0xae36b8: stur            x3, [fp, #-8]
    // 0xae36bc: StoreField: r3->field_13 = r0
    //     0xae36bc: stur            x0, [x3, #0x13]
    // 0xae36c0: r0 = Instance_FlexFit
    //     0xae36c0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xae36c4: ldr             x0, [x0, #0x728]
    // 0xae36c8: StoreField: r3->field_1b = r0
    //     0xae36c8: stur            w0, [x3, #0x1b]
    // 0xae36cc: ldur            x0, [fp, #-0x20]
    // 0xae36d0: StoreField: r3->field_b = r0
    //     0xae36d0: stur            w0, [x3, #0xb]
    // 0xae36d4: r1 = Null
    //     0xae36d4: mov             x1, NULL
    // 0xae36d8: r2 = 4
    //     0xae36d8: movz            x2, #0x4
    // 0xae36dc: r0 = AllocateArray()
    //     0xae36dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae36e0: mov             x2, x0
    // 0xae36e4: ldur            x0, [fp, #-0x10]
    // 0xae36e8: stur            x2, [fp, #-0x20]
    // 0xae36ec: StoreField: r2->field_f = r0
    //     0xae36ec: stur            w0, [x2, #0xf]
    // 0xae36f0: ldur            x0, [fp, #-8]
    // 0xae36f4: StoreField: r2->field_13 = r0
    //     0xae36f4: stur            w0, [x2, #0x13]
    // 0xae36f8: r1 = <Widget>
    //     0xae36f8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae36fc: r0 = AllocateGrowableArray()
    //     0xae36fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae3700: mov             x1, x0
    // 0xae3704: ldur            x0, [fp, #-0x20]
    // 0xae3708: stur            x1, [fp, #-8]
    // 0xae370c: StoreField: r1->field_f = r0
    //     0xae370c: stur            w0, [x1, #0xf]
    // 0xae3710: r0 = 4
    //     0xae3710: movz            x0, #0x4
    // 0xae3714: StoreField: r1->field_b = r0
    //     0xae3714: stur            w0, [x1, #0xb]
    // 0xae3718: r0 = Column()
    //     0xae3718: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae371c: mov             x1, x0
    // 0xae3720: r0 = Instance_Axis
    //     0xae3720: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae3724: stur            x1, [fp, #-0x10]
    // 0xae3728: StoreField: r1->field_f = r0
    //     0xae3728: stur            w0, [x1, #0xf]
    // 0xae372c: r0 = Instance_MainAxisAlignment
    //     0xae372c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae3730: ldr             x0, [x0, #0x730]
    // 0xae3734: StoreField: r1->field_13 = r0
    //     0xae3734: stur            w0, [x1, #0x13]
    // 0xae3738: r0 = Instance_MainAxisSize
    //     0xae3738: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae373c: ldr             x0, [x0, #0x738]
    // 0xae3740: ArrayStore: r1[0] = r0  ; List_4
    //     0xae3740: stur            w0, [x1, #0x17]
    // 0xae3744: r0 = Instance_CrossAxisAlignment
    //     0xae3744: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae3748: ldr             x0, [x0, #0x740]
    // 0xae374c: StoreField: r1->field_1b = r0
    //     0xae374c: stur            w0, [x1, #0x1b]
    // 0xae3750: r0 = Instance_VerticalDirection
    //     0xae3750: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae3754: ldr             x0, [x0, #0x748]
    // 0xae3758: StoreField: r1->field_23 = r0
    //     0xae3758: stur            w0, [x1, #0x23]
    // 0xae375c: r0 = Instance_Clip
    //     0xae375c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae3760: ldr             x0, [x0, #0x750]
    // 0xae3764: StoreField: r1->field_2b = r0
    //     0xae3764: stur            w0, [x1, #0x2b]
    // 0xae3768: StoreField: r1->field_2f = rZR
    //     0xae3768: stur            xzr, [x1, #0x2f]
    // 0xae376c: ldur            x0, [fp, #-8]
    // 0xae3770: StoreField: r1->field_b = r0
    //     0xae3770: stur            w0, [x1, #0xb]
    // 0xae3774: r0 = Scaffold()
    //     0xae3774: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae3778: ldur            x1, [fp, #-0x18]
    // 0xae377c: StoreField: r0->field_13 = r1
    //     0xae377c: stur            w1, [x0, #0x13]
    // 0xae3780: ldur            x1, [fp, #-0x10]
    // 0xae3784: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3784: stur            w1, [x0, #0x17]
    // 0xae3788: r1 = Instance_AlignmentDirectional
    //     0xae3788: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae378c: ldr             x1, [x1, #0x758]
    // 0xae3790: StoreField: r0->field_2b = r1
    //     0xae3790: stur            w1, [x0, #0x2b]
    // 0xae3794: r1 = true
    //     0xae3794: add             x1, NULL, #0x20  ; true
    // 0xae3798: StoreField: r0->field_53 = r1
    //     0xae3798: stur            w1, [x0, #0x53]
    // 0xae379c: r2 = Instance_DragStartBehavior
    //     0xae379c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae37a0: StoreField: r0->field_57 = r2
    //     0xae37a0: stur            w2, [x0, #0x57]
    // 0xae37a4: r2 = false
    //     0xae37a4: add             x2, NULL, #0x30  ; false
    // 0xae37a8: StoreField: r0->field_b = r2
    //     0xae37a8: stur            w2, [x0, #0xb]
    // 0xae37ac: StoreField: r0->field_f = r2
    //     0xae37ac: stur            w2, [x0, #0xf]
    // 0xae37b0: StoreField: r0->field_5f = r1
    //     0xae37b0: stur            w1, [x0, #0x5f]
    // 0xae37b4: StoreField: r0->field_63 = r1
    //     0xae37b4: stur            w1, [x0, #0x63]
    // 0xae37b8: LeaveFrame
    //     0xae37b8: mov             SP, fp
    //     0xae37bc: ldp             fp, lr, [SP], #0x10
    // 0xae37c0: ret
    //     0xae37c0: ret             
    // 0xae37c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae37c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae37c8: b               #0xae34f4
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0xae37d8, size: 0x190
    // 0xae37d8: EnterFrame
    //     0xae37d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae37dc: mov             fp, SP
    // 0xae37e0: AllocStack(0x20)
    //     0xae37e0: sub             SP, SP, #0x20
    // 0xae37e4: SetupParameters()
    //     0xae37e4: movz            x0, #0x2
    //     0xae37e8: ldr             x1, [fp, #0x10]
    //     0xae37ec: ldur            w3, [x1, #0x17]
    //     0xae37f0: add             x3, x3, HEAP, lsl #32
    //     0xae37f4: stur            x3, [fp, #-8]
    // 0xae37e4: r0 = 2
    // 0xae37f8: CheckStackOverflow
    //     0xae37f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae37fc: cmp             SP, x16
    //     0xae3800: b.ls            #0xae3960
    // 0xae3804: mov             x2, x0
    // 0xae3808: r1 = Null
    //     0xae3808: mov             x1, NULL
    // 0xae380c: r0 = AllocateArray()
    //     0xae380c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae3810: stur            x0, [fp, #-0x10]
    // 0xae3814: r16 = Instance_Text
    //     0xae3814: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f538] Obj!Text@e21b41
    //     0xae3818: ldr             x16, [x16, #0x538]
    // 0xae381c: StoreField: r0->field_f = r16
    //     0xae381c: stur            w16, [x0, #0xf]
    // 0xae3820: r1 = <Widget>
    //     0xae3820: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae3824: r0 = AllocateGrowableArray()
    //     0xae3824: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae3828: mov             x2, x0
    // 0xae382c: ldur            x0, [fp, #-0x10]
    // 0xae3830: stur            x2, [fp, #-0x18]
    // 0xae3834: StoreField: r2->field_f = r0
    //     0xae3834: stur            w0, [x2, #0xf]
    // 0xae3838: r0 = 2
    //     0xae3838: movz            x0, #0x2
    // 0xae383c: StoreField: r2->field_b = r0
    //     0xae383c: stur            w0, [x2, #0xb]
    // 0xae3840: ldur            x0, [fp, #-8]
    // 0xae3844: LoadField: r1 = r0->field_f
    //     0xae3844: ldur            w1, [x0, #0xf]
    // 0xae3848: DecompressPointer r1
    //     0xae3848: add             x1, x1, HEAP, lsl #32
    // 0xae384c: r0 = controller()
    //     0xae384c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3850: LoadField: r1 = r0->field_2b
    //     0xae3850: ldur            w1, [x0, #0x2b]
    // 0xae3854: DecompressPointer r1
    //     0xae3854: add             x1, x1, HEAP, lsl #32
    // 0xae3858: r0 = value()
    //     0xae3858: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae385c: r1 = LoadInt32Instr(r0)
    //     0xae385c: sbfx            x1, x0, #1, #0x1f
    //     0xae3860: tbz             w0, #0, #0xae3868
    //     0xae3864: ldur            x1, [x0, #7]
    // 0xae3868: cmp             x1, #0
    // 0xae386c: b.le            #0xae38fc
    // 0xae3870: ldur            x0, [fp, #-8]
    // 0xae3874: LoadField: r1 = r0->field_f
    //     0xae3874: ldur            w1, [x0, #0xf]
    // 0xae3878: DecompressPointer r1
    //     0xae3878: add             x1, x1, HEAP, lsl #32
    // 0xae387c: r0 = controller()
    //     0xae387c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3880: LoadField: r1 = r0->field_2b
    //     0xae3880: ldur            w1, [x0, #0x2b]
    // 0xae3884: DecompressPointer r1
    //     0xae3884: add             x1, x1, HEAP, lsl #32
    // 0xae3888: r0 = value()
    //     0xae3888: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae388c: r2 = LoadInt32Instr(r0)
    //     0xae388c: sbfx            x2, x0, #1, #0x1f
    //     0xae3890: tbz             w0, #0, #0xae3898
    //     0xae3894: ldur            x2, [x0, #7]
    // 0xae3898: stur            x2, [fp, #-0x20]
    // 0xae389c: r0 = Badge()
    //     0xae389c: bl              #0xae32a4  ; AllocateBadgeStub -> Badge (size=0x2c)
    // 0xae38a0: mov             x1, x0
    // 0xae38a4: ldur            x2, [fp, #-0x20]
    // 0xae38a8: stur            x0, [fp, #-8]
    // 0xae38ac: r0 = Badge.count()
    //     0xae38ac: bl              #0xae31f0  ; [package:flutter/src/material/badge.dart] Badge::Badge.count
    // 0xae38b0: r1 = Null
    //     0xae38b0: mov             x1, NULL
    // 0xae38b4: r2 = 4
    //     0xae38b4: movz            x2, #0x4
    // 0xae38b8: r0 = AllocateArray()
    //     0xae38b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae38bc: stur            x0, [fp, #-0x10]
    // 0xae38c0: r16 = Instance_SizedBox
    //     0xae38c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xae38c4: ldr             x16, [x16, #0xbd8]
    // 0xae38c8: StoreField: r0->field_f = r16
    //     0xae38c8: stur            w16, [x0, #0xf]
    // 0xae38cc: ldur            x1, [fp, #-8]
    // 0xae38d0: StoreField: r0->field_13 = r1
    //     0xae38d0: stur            w1, [x0, #0x13]
    // 0xae38d4: r1 = <Widget>
    //     0xae38d4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae38d8: r0 = AllocateGrowableArray()
    //     0xae38d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae38dc: mov             x1, x0
    // 0xae38e0: ldur            x0, [fp, #-0x10]
    // 0xae38e4: StoreField: r1->field_f = r0
    //     0xae38e4: stur            w0, [x1, #0xf]
    // 0xae38e8: r0 = 4
    //     0xae38e8: movz            x0, #0x4
    // 0xae38ec: StoreField: r1->field_b = r0
    //     0xae38ec: stur            w0, [x1, #0xb]
    // 0xae38f0: mov             x2, x1
    // 0xae38f4: ldur            x1, [fp, #-0x18]
    // 0xae38f8: r0 = addAll()
    //     0xae38f8: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xae38fc: ldur            x0, [fp, #-0x18]
    // 0xae3900: r0 = Row()
    //     0xae3900: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae3904: r1 = Instance_Axis
    //     0xae3904: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xae3908: StoreField: r0->field_f = r1
    //     0xae3908: stur            w1, [x0, #0xf]
    // 0xae390c: r1 = Instance_MainAxisAlignment
    //     0xae390c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae3910: ldr             x1, [x1, #0x730]
    // 0xae3914: StoreField: r0->field_13 = r1
    //     0xae3914: stur            w1, [x0, #0x13]
    // 0xae3918: r1 = Instance_MainAxisSize
    //     0xae3918: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xae391c: ldr             x1, [x1, #0xe88]
    // 0xae3920: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3920: stur            w1, [x0, #0x17]
    // 0xae3924: r1 = Instance_CrossAxisAlignment
    //     0xae3924: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae3928: ldr             x1, [x1, #0x740]
    // 0xae392c: StoreField: r0->field_1b = r1
    //     0xae392c: stur            w1, [x0, #0x1b]
    // 0xae3930: r1 = Instance_VerticalDirection
    //     0xae3930: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae3934: ldr             x1, [x1, #0x748]
    // 0xae3938: StoreField: r0->field_23 = r1
    //     0xae3938: stur            w1, [x0, #0x23]
    // 0xae393c: r1 = Instance_Clip
    //     0xae393c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae3940: ldr             x1, [x1, #0x750]
    // 0xae3944: StoreField: r0->field_2b = r1
    //     0xae3944: stur            w1, [x0, #0x2b]
    // 0xae3948: StoreField: r0->field_2f = rZR
    //     0xae3948: stur            xzr, [x0, #0x2f]
    // 0xae394c: ldur            x1, [fp, #-0x18]
    // 0xae3950: StoreField: r0->field_b = r1
    //     0xae3950: stur            w1, [x0, #0xb]
    // 0xae3954: LeaveFrame
    //     0xae3954: mov             SP, fp
    //     0xae3958: ldp             fp, lr, [SP], #0x10
    // 0xae395c: ret
    //     0xae395c: ret             
    // 0xae3960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3964: b               #0xae3804
  }
}
