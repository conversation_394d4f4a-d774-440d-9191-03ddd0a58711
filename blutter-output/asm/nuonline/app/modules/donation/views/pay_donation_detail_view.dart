// lib: , url: package:nuonline/app/modules/donation/views/pay_donation_detail_view.dart

// class id: 1050225, size: 0x8
class :: {
}

// class id: 5291, size: 0x14, field offset: 0x14
//   const constructor, 
class PayDonationDetailView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae3968, size: 0xf4
    // 0xae3968: EnterFrame
    //     0xae3968: stp             fp, lr, [SP, #-0x10]!
    //     0xae396c: mov             fp, SP
    // 0xae3970: AllocStack(0x40)
    //     0xae3970: sub             SP, SP, #0x40
    // 0xae3974: SetupParameters(PayDonationDetailView this /* r1 => r1, fp-0x8 */)
    //     0xae3974: stur            x1, [fp, #-8]
    // 0xae3978: CheckStackOverflow
    //     0xae3978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae397c: cmp             SP, x16
    //     0xae3980: b.ls            #0xae3a54
    // 0xae3984: r1 = 1
    //     0xae3984: movz            x1, #0x1
    // 0xae3988: r0 = AllocateContext()
    //     0xae3988: bl              #0xec126c  ; AllocateContextStub
    // 0xae398c: ldur            x1, [fp, #-8]
    // 0xae3990: stur            x0, [fp, #-0x10]
    // 0xae3994: StoreField: r0->field_f = r1
    //     0xae3994: stur            w1, [x0, #0xf]
    // 0xae3998: r0 = controller()
    //     0xae3998: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae399c: stur            x0, [fp, #-8]
    // 0xae39a0: r0 = AppBar()
    //     0xae39a0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae39a4: stur            x0, [fp, #-0x18]
    // 0xae39a8: r16 = Instance_Text
    //     0xae39a8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30288] Obj!Text@e21641
    //     0xae39ac: ldr             x16, [x16, #0x288]
    // 0xae39b0: str             x16, [SP]
    // 0xae39b4: mov             x1, x0
    // 0xae39b8: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae39b8: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae39bc: ldr             x4, [x4, #0x6e8]
    // 0xae39c0: r0 = AppBar()
    //     0xae39c0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae39c4: r0 = Scaffold()
    //     0xae39c4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae39c8: mov             x3, x0
    // 0xae39cc: ldur            x0, [fp, #-0x18]
    // 0xae39d0: stur            x3, [fp, #-0x20]
    // 0xae39d4: StoreField: r3->field_13 = r0
    //     0xae39d4: stur            w0, [x3, #0x13]
    // 0xae39d8: r0 = Instance_Center
    //     0xae39d8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30290] Obj!Center@e1e6e1
    //     0xae39dc: ldr             x0, [x0, #0x290]
    // 0xae39e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xae39e0: stur            w0, [x3, #0x17]
    // 0xae39e4: r0 = Instance_AlignmentDirectional
    //     0xae39e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae39e8: ldr             x0, [x0, #0x758]
    // 0xae39ec: StoreField: r3->field_2b = r0
    //     0xae39ec: stur            w0, [x3, #0x2b]
    // 0xae39f0: r0 = true
    //     0xae39f0: add             x0, NULL, #0x20  ; true
    // 0xae39f4: StoreField: r3->field_53 = r0
    //     0xae39f4: stur            w0, [x3, #0x53]
    // 0xae39f8: r1 = Instance_DragStartBehavior
    //     0xae39f8: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae39fc: StoreField: r3->field_57 = r1
    //     0xae39fc: stur            w1, [x3, #0x57]
    // 0xae3a00: r1 = false
    //     0xae3a00: add             x1, NULL, #0x30  ; false
    // 0xae3a04: StoreField: r3->field_b = r1
    //     0xae3a04: stur            w1, [x3, #0xb]
    // 0xae3a08: StoreField: r3->field_f = r1
    //     0xae3a08: stur            w1, [x3, #0xf]
    // 0xae3a0c: StoreField: r3->field_5f = r0
    //     0xae3a0c: stur            w0, [x3, #0x5f]
    // 0xae3a10: StoreField: r3->field_63 = r0
    //     0xae3a10: stur            w0, [x3, #0x63]
    // 0xae3a14: ldur            x2, [fp, #-0x10]
    // 0xae3a18: r1 = Function '<anonymous closure>':.
    //     0xae3a18: add             x1, PP, #0x30, lsl #12  ; [pp+0x30298] AnonymousClosure: (0xae3a5c), in [package:nuonline/app/modules/donation/views/pay_donation_detail_view.dart] PayDonationDetailView::build (0xae3968)
    //     0xae3a1c: ldr             x1, [x1, #0x298]
    // 0xae3a20: r0 = AllocateClosure()
    //     0xae3a20: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3a24: r16 = <Transaction>
    //     0xae3a24: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0xae3a28: ldr             x16, [x16, #0x28]
    // 0xae3a2c: ldur            lr, [fp, #-8]
    // 0xae3a30: stp             lr, x16, [SP, #0x10]
    // 0xae3a34: ldur            x16, [fp, #-0x20]
    // 0xae3a38: stp             x16, x0, [SP]
    // 0xae3a3c: r4 = const [0x1, 0x3, 0x3, 0x2, onLoading, 0x2, null]
    //     0xae3a3c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25718] List(7) [0x1, 0x3, 0x3, 0x2, "onLoading", 0x2, Null]
    //     0xae3a40: ldr             x4, [x4, #0x718]
    // 0xae3a44: r0 = StateExt.obx()
    //     0xae3a44: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xae3a48: LeaveFrame
    //     0xae3a48: mov             SP, fp
    //     0xae3a4c: ldp             fp, lr, [SP], #0x10
    // 0xae3a50: ret
    //     0xae3a50: ret             
    // 0xae3a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3a54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3a58: b               #0xae3984
  }
  [closure] Widget <anonymous closure>(dynamic, Transaction?) {
    // ** addr: 0xae3a5c, size: 0xf0
    // 0xae3a5c: EnterFrame
    //     0xae3a5c: stp             fp, lr, [SP, #-0x10]!
    //     0xae3a60: mov             fp, SP
    // 0xae3a64: AllocStack(0x10)
    //     0xae3a64: sub             SP, SP, #0x10
    // 0xae3a68: SetupParameters()
    //     0xae3a68: ldr             x0, [fp, #0x18]
    //     0xae3a6c: ldur            w2, [x0, #0x17]
    //     0xae3a70: add             x2, x2, HEAP, lsl #32
    //     0xae3a74: stur            x2, [fp, #-8]
    // 0xae3a78: CheckStackOverflow
    //     0xae3a78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3a7c: cmp             SP, x16
    //     0xae3a80: b.ls            #0xae3b40
    // 0xae3a84: ldr             x0, [fp, #0x10]
    // 0xae3a88: cmp             w0, NULL
    // 0xae3a8c: b.eq            #0xae3b48
    // 0xae3a90: mov             x1, x0
    // 0xae3a94: r0 = isPending()
    //     0xae3a94: bl              #0xae3bc8  ; [package:nuonline/app/data/models/transaction.dart] Transaction::isPending
    // 0xae3a98: tbnz            w0, #4, #0xae3af0
    // 0xae3a9c: ldr             x2, [fp, #0x10]
    // 0xae3aa0: ldur            x0, [fp, #-8]
    // 0xae3aa4: LoadField: r1 = r0->field_f
    //     0xae3aa4: ldur            w1, [x0, #0xf]
    // 0xae3aa8: DecompressPointer r1
    //     0xae3aa8: add             x1, x1, HEAP, lsl #32
    // 0xae3aac: r0 = controller()
    //     0xae3aac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3ab0: stur            x0, [fp, #-8]
    // 0xae3ab4: r0 = TransactionPending()
    //     0xae3ab4: bl              #0xae3bbc  ; AllocateTransactionPendingStub -> TransactionPending (size=0x14)
    // 0xae3ab8: mov             x3, x0
    // 0xae3abc: ldr             x0, [fp, #0x10]
    // 0xae3ac0: stur            x3, [fp, #-0x10]
    // 0xae3ac4: StoreField: r3->field_b = r0
    //     0xae3ac4: stur            w0, [x3, #0xb]
    // 0xae3ac8: ldur            x2, [fp, #-8]
    // 0xae3acc: r1 = Function 'onRefresh':.
    //     0xae3acc: add             x1, PP, #0x30, lsl #12  ; [pp+0x302a0] AnonymousClosure: (0xae3c20), in [package:nuonline/app/modules/donation/controllers/pay_donation_detail_controller.dart] PayDonationDetailController::onRefresh (0xae3c58)
    //     0xae3ad0: ldr             x1, [x1, #0x2a0]
    // 0xae3ad4: r0 = AllocateClosure()
    //     0xae3ad4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3ad8: mov             x1, x0
    // 0xae3adc: ldur            x0, [fp, #-0x10]
    // 0xae3ae0: StoreField: r0->field_f = r1
    //     0xae3ae0: stur            w1, [x0, #0xf]
    // 0xae3ae4: LeaveFrame
    //     0xae3ae4: mov             SP, fp
    //     0xae3ae8: ldp             fp, lr, [SP], #0x10
    // 0xae3aec: ret
    //     0xae3aec: ret             
    // 0xae3af0: ldr             x0, [fp, #0x10]
    // 0xae3af4: mov             x1, x0
    // 0xae3af8: r0 = isSuccess()
    //     0xae3af8: bl              #0xae3b64  ; [package:nuonline/app/data/models/transaction.dart] Transaction::isSuccess
    // 0xae3afc: tbnz            w0, #4, #0xae3b24
    // 0xae3b00: ldr             x0, [fp, #0x10]
    // 0xae3b04: r0 = TransactionSuccess()
    //     0xae3b04: bl              #0xae3b58  ; AllocateTransactionSuccessStub -> TransactionSuccess (size=0x10)
    // 0xae3b08: mov             x1, x0
    // 0xae3b0c: ldr             x0, [fp, #0x10]
    // 0xae3b10: StoreField: r1->field_b = r0
    //     0xae3b10: stur            w0, [x1, #0xb]
    // 0xae3b14: mov             x0, x1
    // 0xae3b18: LeaveFrame
    //     0xae3b18: mov             SP, fp
    //     0xae3b1c: ldp             fp, lr, [SP], #0x10
    // 0xae3b20: ret
    //     0xae3b20: ret             
    // 0xae3b24: ldr             x0, [fp, #0x10]
    // 0xae3b28: r0 = TransactionCanceled()
    //     0xae3b28: bl              #0xae3b4c  ; AllocateTransactionCanceledStub -> TransactionCanceled (size=0x10)
    // 0xae3b2c: ldr             x1, [fp, #0x10]
    // 0xae3b30: StoreField: r0->field_b = r1
    //     0xae3b30: stur            w1, [x0, #0xb]
    // 0xae3b34: LeaveFrame
    //     0xae3b34: mov             SP, fp
    //     0xae3b38: ldp             fp, lr, [SP], #0x10
    // 0xae3b3c: ret
    //     0xae3b3c: ret             
    // 0xae3b40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3b40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3b44: b               #0xae3a84
    // 0xae3b48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3b48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
