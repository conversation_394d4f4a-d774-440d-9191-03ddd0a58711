// lib: , url: package:nuonline/app/modules/donation/views/pay_donation_view.dart

// class id: 1050226, size: 0x8
class :: {
}

// class id: 4118, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class ___KeepAliveState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x92bb08, size: 0x40
    // 0x92bb08: EnterFrame
    //     0x92bb08: stp             fp, lr, [SP, #-0x10]!
    //     0x92bb0c: mov             fp, SP
    // 0x92bb10: CheckStackOverflow
    //     0x92bb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92bb14: cmp             SP, x16
    //     0x92bb18: b.ls            #0x92bb40
    // 0x92bb1c: LoadField: r0 = r1->field_13
    //     0x92bb1c: ldur            w0, [x1, #0x13]
    // 0x92bb20: DecompressPointer r0
    //     0x92bb20: add             x0, x0, HEAP, lsl #32
    // 0x92bb24: cmp             w0, NULL
    // 0x92bb28: b.eq            #0x92bb30
    // 0x92bb2c: r0 = _releaseKeepAlive()
    //     0x92bb2c: bl              #0x92b178  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x92bb30: r0 = Null
    //     0x92bb30: mov             x0, NULL
    // 0x92bb34: LeaveFrame
    //     0x92bb34: mov             SP, fp
    //     0x92bb38: ldp             fp, lr, [SP], #0x10
    // 0x92bb3c: ret
    //     0x92bb3c: ret             
    // 0x92bb40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bb40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bb44: b               #0x92bb1c
  }
  _ initState(/* No info */) {
    // ** addr: 0x9653cc, size: 0x30
    // 0x9653cc: EnterFrame
    //     0x9653cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9653d0: mov             fp, SP
    // 0x9653d4: CheckStackOverflow
    //     0x9653d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9653d8: cmp             SP, x16
    //     0x9653dc: b.ls            #0x9653f4
    // 0x9653e0: r0 = _ensureKeepAlive()
    //     0x9653e0: bl              #0x933d08  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x9653e4: r0 = Null
    //     0x9653e4: mov             x0, NULL
    // 0x9653e8: LeaveFrame
    //     0x9653e8: mov             SP, fp
    //     0x9653ec: ldp             fp, lr, [SP], #0x10
    // 0x9653f0: ret
    //     0x9653f0: ret             
    // 0x9653f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9653f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9653f8: b               #0x9653e0
  }
  _ build(/* No info */) {
    // ** addr: 0xa371a4, size: 0x44
    // 0xa371a4: EnterFrame
    //     0xa371a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa371a8: mov             fp, SP
    // 0xa371ac: CheckStackOverflow
    //     0xa371ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa371b0: cmp             SP, x16
    //     0xa371b4: b.ls            #0xa371e0
    // 0xa371b8: LoadField: r0 = r1->field_13
    //     0xa371b8: ldur            w0, [x1, #0x13]
    // 0xa371bc: DecompressPointer r0
    //     0xa371bc: add             x0, x0, HEAP, lsl #32
    // 0xa371c0: cmp             w0, NULL
    // 0xa371c4: b.ne            #0xa371cc
    // 0xa371c8: r0 = _ensureKeepAlive()
    //     0xa371c8: bl              #0x933d08  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xa371cc: r0 = Instance__NullWidget
    //     0xa371cc: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d8] Obj!_NullWidget@e25381
    //     0xa371d0: ldr             x0, [x0, #0x7d8]
    // 0xa371d4: LeaveFrame
    //     0xa371d4: mov             SP, fp
    //     0xa371d8: ldp             fp, lr, [SP], #0x10
    // 0xa371dc: ret
    //     0xa371dc: ret             
    // 0xa371e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa371e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa371e4: b               #0xa371b8
  }
}

// class id: 4119, size: 0x18, field offset: 0x18
class __KeepAliveState extends ___KeepAliveState&State&AutomaticKeepAliveClientMixin {

  _ build(/* No info */) {
    // ** addr: 0xa37148, size: 0x5c
    // 0xa37148: EnterFrame
    //     0xa37148: stp             fp, lr, [SP, #-0x10]!
    //     0xa3714c: mov             fp, SP
    // 0xa37150: AllocStack(0x8)
    //     0xa37150: sub             SP, SP, #8
    // 0xa37154: SetupParameters(__KeepAliveState this /* r1 => r0, fp-0x8 */)
    //     0xa37154: mov             x0, x1
    //     0xa37158: stur            x1, [fp, #-8]
    // 0xa3715c: CheckStackOverflow
    //     0xa3715c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37160: cmp             SP, x16
    //     0xa37164: b.ls            #0xa37198
    // 0xa37168: mov             x1, x0
    // 0xa3716c: r0 = build()
    //     0xa3716c: bl              #0xa371a4  ; [package:nuonline/app/modules/donation/views/pay_donation_view.dart] ___KeepAliveState&State&AutomaticKeepAliveClientMixin::build
    // 0xa37170: ldur            x1, [fp, #-8]
    // 0xa37174: LoadField: r2 = r1->field_b
    //     0xa37174: ldur            w2, [x1, #0xb]
    // 0xa37178: DecompressPointer r2
    //     0xa37178: add             x2, x2, HEAP, lsl #32
    // 0xa3717c: cmp             w2, NULL
    // 0xa37180: b.eq            #0xa371a0
    // 0xa37184: LoadField: r0 = r2->field_b
    //     0xa37184: ldur            w0, [x2, #0xb]
    // 0xa37188: DecompressPointer r0
    //     0xa37188: add             x0, x0, HEAP, lsl #32
    // 0xa3718c: LeaveFrame
    //     0xa3718c: mov             SP, fp
    //     0xa37190: ldp             fp, lr, [SP], #0x10
    // 0xa37194: ret
    //     0xa37194: ret             
    // 0xa37198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa37198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3719c: b               #0xa37168
    // 0xa371a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa371a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4715, size: 0x10, field offset: 0xc
//   const constructor, 
class _KeepAlive extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9453c, size: 0x24
    // 0xa9453c: EnterFrame
    //     0xa9453c: stp             fp, lr, [SP, #-0x10]!
    //     0xa94540: mov             fp, SP
    // 0xa94544: mov             x0, x1
    // 0xa94548: r1 = <_KeepAlive>
    //     0xa94548: add             x1, PP, #0x36, lsl #12  ; [pp+0x360b0] TypeArguments: <_KeepAlive>
    //     0xa9454c: ldr             x1, [x1, #0xb0]
    // 0xa94550: r0 = __KeepAliveState()
    //     0xa94550: bl              #0xa94560  ; Allocate__KeepAliveStateStub -> __KeepAliveState (size=0x18)
    // 0xa94554: LeaveFrame
    //     0xa94554: mov             SP, fp
    //     0xa94558: ldp             fp, lr, [SP], #0x10
    // 0xa9455c: ret
    //     0xa9455c: ret             
  }
}

// class id: 5290, size: 0x14, field offset: 0x14
//   const constructor, 
class PayDonationView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae3c98, size: 0xec8
    // 0xae3c98: EnterFrame
    //     0xae3c98: stp             fp, lr, [SP, #-0x10]!
    //     0xae3c9c: mov             fp, SP
    // 0xae3ca0: AllocStack(0xa0)
    //     0xae3ca0: sub             SP, SP, #0xa0
    // 0xae3ca4: SetupParameters(PayDonationView this /* r1 => r1, fp-0x8 */)
    //     0xae3ca4: stur            x1, [fp, #-8]
    // 0xae3ca8: CheckStackOverflow
    //     0xae3ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3cac: cmp             SP, x16
    //     0xae3cb0: b.ls            #0xae4b58
    // 0xae3cb4: r1 = 1
    //     0xae3cb4: movz            x1, #0x1
    // 0xae3cb8: r0 = AllocateContext()
    //     0xae3cb8: bl              #0xec126c  ; AllocateContextStub
    // 0xae3cbc: mov             x2, x0
    // 0xae3cc0: ldur            x0, [fp, #-8]
    // 0xae3cc4: stur            x2, [fp, #-0x10]
    // 0xae3cc8: StoreField: r2->field_f = r0
    //     0xae3cc8: stur            w0, [x2, #0xf]
    // 0xae3ccc: mov             x1, x0
    // 0xae3cd0: r0 = controller()
    //     0xae3cd0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3cd4: mov             x1, x0
    // 0xae3cd8: r0 = title()
    //     0xae3cd8: bl              #0xae5024  ; [package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart] PayDonationController::title
    // 0xae3cdc: stur            x0, [fp, #-0x18]
    // 0xae3ce0: r0 = Text()
    //     0xae3ce0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae3ce4: mov             x1, x0
    // 0xae3ce8: ldur            x0, [fp, #-0x18]
    // 0xae3cec: stur            x1, [fp, #-0x20]
    // 0xae3cf0: StoreField: r1->field_b = r0
    //     0xae3cf0: stur            w0, [x1, #0xb]
    // 0xae3cf4: r0 = AppBar()
    //     0xae3cf4: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae3cf8: stur            x0, [fp, #-0x18]
    // 0xae3cfc: ldur            x16, [fp, #-0x20]
    // 0xae3d00: str             x16, [SP]
    // 0xae3d04: mov             x1, x0
    // 0xae3d08: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xae3d08: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xae3d0c: ldr             x4, [x4, #0x6e8]
    // 0xae3d10: r0 = AppBar()
    //     0xae3d10: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae3d14: ldur            x1, [fp, #-8]
    // 0xae3d18: r0 = controller()
    //     0xae3d18: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3d1c: LoadField: r2 = r0->field_37
    //     0xae3d1c: ldur            w2, [x0, #0x37]
    // 0xae3d20: DecompressPointer r2
    //     0xae3d20: add             x2, x2, HEAP, lsl #32
    // 0xae3d24: ldur            x1, [fp, #-8]
    // 0xae3d28: stur            x2, [fp, #-0x20]
    // 0xae3d2c: r0 = controller()
    //     0xae3d2c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3d30: LoadField: r1 = r0->field_1f
    //     0xae3d30: ldur            w1, [x0, #0x1f]
    // 0xae3d34: DecompressPointer r1
    //     0xae3d34: add             x1, x1, HEAP, lsl #32
    // 0xae3d38: r16 = Instance_PaymentType
    //     0xae3d38: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xae3d3c: ldr             x16, [x16, #0x5c0]
    // 0xae3d40: cmp             w1, w16
    // 0xae3d44: b.ne            #0xae3e10
    // 0xae3d48: ldur            x1, [fp, #-8]
    // 0xae3d4c: r0 = controller()
    //     0xae3d4c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3d50: mov             x1, x0
    // 0xae3d54: LoadField: r0 = r1->field_3f
    //     0xae3d54: ldur            w0, [x1, #0x3f]
    // 0xae3d58: DecompressPointer r0
    //     0xae3d58: add             x0, x0, HEAP, lsl #32
    // 0xae3d5c: r16 = Sentinel
    //     0xae3d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3d60: cmp             w0, w16
    // 0xae3d64: b.ne            #0xae3d74
    // 0xae3d68: r2 = form
    //     0xae3d68: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae3d6c: ldr             x2, [x2, #0xc60]
    // 0xae3d70: r0 = InitLateFinalInstanceField()
    //     0xae3d70: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3d74: mov             x3, x0
    // 0xae3d78: r2 = Null
    //     0xae3d78: mov             x2, NULL
    // 0xae3d7c: r1 = Null
    //     0xae3d7c: mov             x1, NULL
    // 0xae3d80: stur            x3, [fp, #-0x28]
    // 0xae3d84: r4 = LoadClassIdInstr(r0)
    //     0xae3d84: ldur            x4, [x0, #-1]
    //     0xae3d88: ubfx            x4, x4, #0xc, #0x14
    // 0xae3d8c: cmp             x4, #0x776
    // 0xae3d90: b.eq            #0xae3da8
    // 0xae3d94: r8 = ZakatFormController
    //     0xae3d94: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fc68] Type: ZakatFormController
    //     0xae3d98: ldr             x8, [x8, #0xc68]
    // 0xae3d9c: r3 = Null
    //     0xae3d9c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fc70] Null
    //     0xae3da0: ldr             x3, [x3, #0xc70]
    // 0xae3da4: r0 = DefaultTypeTest()
    //     0xae3da4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae3da8: r1 = <ZakatFormController>
    //     0xae3da8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc80] TypeArguments: <ZakatFormController>
    //     0xae3dac: ldr             x1, [x1, #0xc80]
    // 0xae3db0: r0 = GetBuilder()
    //     0xae3db0: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae3db4: mov             x3, x0
    // 0xae3db8: ldur            x0, [fp, #-0x28]
    // 0xae3dbc: stur            x3, [fp, #-0x30]
    // 0xae3dc0: StoreField: r3->field_3b = r0
    //     0xae3dc0: stur            w0, [x3, #0x3b]
    // 0xae3dc4: r0 = true
    //     0xae3dc4: add             x0, NULL, #0x20  ; true
    // 0xae3dc8: StoreField: r3->field_13 = r0
    //     0xae3dc8: stur            w0, [x3, #0x13]
    // 0xae3dcc: r1 = Function '<anonymous closure>':.
    //     0xae3dcc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc88] AnonymousClosure: (0xae6428), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae3dd0: ldr             x1, [x1, #0xc88]
    // 0xae3dd4: r2 = Null
    //     0xae3dd4: mov             x2, NULL
    // 0xae3dd8: r0 = AllocateClosure()
    //     0xae3dd8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3ddc: mov             x1, x0
    // 0xae3de0: ldur            x0, [fp, #-0x30]
    // 0xae3de4: StoreField: r0->field_f = r1
    //     0xae3de4: stur            w1, [x0, #0xf]
    // 0xae3de8: r2 = true
    //     0xae3de8: add             x2, NULL, #0x20  ; true
    // 0xae3dec: StoreField: r0->field_1f = r2
    //     0xae3dec: stur            w2, [x0, #0x1f]
    // 0xae3df0: r3 = false
    //     0xae3df0: add             x3, NULL, #0x30  ; false
    // 0xae3df4: StoreField: r0->field_23 = r3
    //     0xae3df4: stur            w3, [x0, #0x23]
    // 0xae3df8: r1 = "zakat_form"
    //     0xae3df8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc90] "zakat_form"
    //     0xae3dfc: ldr             x1, [x1, #0xc90]
    // 0xae3e00: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3e00: stur            w1, [x0, #0x17]
    // 0xae3e04: mov             x4, x3
    // 0xae3e08: mov             x3, x2
    // 0xae3e0c: b               #0xae4204
    // 0xae3e10: r2 = true
    //     0xae3e10: add             x2, NULL, #0x20  ; true
    // 0xae3e14: r3 = false
    //     0xae3e14: add             x3, NULL, #0x30  ; false
    // 0xae3e18: r16 = Instance_PaymentType
    //     0xae3e18: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xae3e1c: ldr             x16, [x16, #0x5a8]
    // 0xae3e20: cmp             w1, w16
    // 0xae3e24: b.ne            #0xae3ec0
    // 0xae3e28: ldur            x1, [fp, #-8]
    // 0xae3e2c: r0 = controller()
    //     0xae3e2c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3e30: mov             x1, x0
    // 0xae3e34: LoadField: r0 = r1->field_3f
    //     0xae3e34: ldur            w0, [x1, #0x3f]
    // 0xae3e38: DecompressPointer r0
    //     0xae3e38: add             x0, x0, HEAP, lsl #32
    // 0xae3e3c: r16 = Sentinel
    //     0xae3e3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3e40: cmp             w0, w16
    // 0xae3e44: b.ne            #0xae3e54
    // 0xae3e48: r2 = form
    //     0xae3e48: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae3e4c: ldr             x2, [x2, #0xc60]
    // 0xae3e50: r0 = InitLateFinalInstanceField()
    //     0xae3e50: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3e54: r1 = <DonationFormController>
    //     0xae3e54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc98] TypeArguments: <DonationFormController>
    //     0xae3e58: ldr             x1, [x1, #0xc98]
    // 0xae3e5c: stur            x0, [fp, #-0x28]
    // 0xae3e60: r0 = GetBuilder()
    //     0xae3e60: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae3e64: mov             x3, x0
    // 0xae3e68: ldur            x0, [fp, #-0x28]
    // 0xae3e6c: stur            x3, [fp, #-0x30]
    // 0xae3e70: StoreField: r3->field_3b = r0
    //     0xae3e70: stur            w0, [x3, #0x3b]
    // 0xae3e74: r0 = true
    //     0xae3e74: add             x0, NULL, #0x20  ; true
    // 0xae3e78: StoreField: r3->field_13 = r0
    //     0xae3e78: stur            w0, [x3, #0x13]
    // 0xae3e7c: r1 = Function '<anonymous closure>':.
    //     0xae3e7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fca0] AnonymousClosure: (0xae641c), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae3e80: ldr             x1, [x1, #0xca0]
    // 0xae3e84: r2 = Null
    //     0xae3e84: mov             x2, NULL
    // 0xae3e88: r0 = AllocateClosure()
    //     0xae3e88: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3e8c: mov             x1, x0
    // 0xae3e90: ldur            x0, [fp, #-0x30]
    // 0xae3e94: StoreField: r0->field_f = r1
    //     0xae3e94: stur            w1, [x0, #0xf]
    // 0xae3e98: r2 = true
    //     0xae3e98: add             x2, NULL, #0x20  ; true
    // 0xae3e9c: StoreField: r0->field_1f = r2
    //     0xae3e9c: stur            w2, [x0, #0x1f]
    // 0xae3ea0: r3 = false
    //     0xae3ea0: add             x3, NULL, #0x30  ; false
    // 0xae3ea4: StoreField: r0->field_23 = r3
    //     0xae3ea4: stur            w3, [x0, #0x23]
    // 0xae3ea8: r1 = "donation_form"
    //     0xae3ea8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fca8] "donation_form"
    //     0xae3eac: ldr             x1, [x1, #0xca8]
    // 0xae3eb0: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3eb0: stur            w1, [x0, #0x17]
    // 0xae3eb4: mov             x4, x3
    // 0xae3eb8: mov             x3, x2
    // 0xae3ebc: b               #0xae4204
    // 0xae3ec0: r16 = Instance_PaymentType
    //     0xae3ec0: add             x16, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0xae3ec4: ldr             x16, [x16, #0x5d8]
    // 0xae3ec8: cmp             w1, w16
    // 0xae3ecc: b.ne            #0xae3f98
    // 0xae3ed0: ldur            x1, [fp, #-8]
    // 0xae3ed4: r0 = controller()
    //     0xae3ed4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3ed8: mov             x1, x0
    // 0xae3edc: LoadField: r0 = r1->field_3f
    //     0xae3edc: ldur            w0, [x1, #0x3f]
    // 0xae3ee0: DecompressPointer r0
    //     0xae3ee0: add             x0, x0, HEAP, lsl #32
    // 0xae3ee4: r16 = Sentinel
    //     0xae3ee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3ee8: cmp             w0, w16
    // 0xae3eec: b.ne            #0xae3efc
    // 0xae3ef0: r2 = form
    //     0xae3ef0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae3ef4: ldr             x2, [x2, #0xc60]
    // 0xae3ef8: r0 = InitLateFinalInstanceField()
    //     0xae3ef8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3efc: mov             x3, x0
    // 0xae3f00: r2 = Null
    //     0xae3f00: mov             x2, NULL
    // 0xae3f04: r1 = Null
    //     0xae3f04: mov             x1, NULL
    // 0xae3f08: stur            x3, [fp, #-0x28]
    // 0xae3f0c: r4 = LoadClassIdInstr(r0)
    //     0xae3f0c: ldur            x4, [x0, #-1]
    //     0xae3f10: ubfx            x4, x4, #0xc, #0x14
    // 0xae3f14: cmp             x4, #0x77a
    // 0xae3f18: b.eq            #0xae3f30
    // 0xae3f1c: r8 = CampaignFormController
    //     0xae3f1c: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fcb0] Type: CampaignFormController
    //     0xae3f20: ldr             x8, [x8, #0xcb0]
    // 0xae3f24: r3 = Null
    //     0xae3f24: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fcb8] Null
    //     0xae3f28: ldr             x3, [x3, #0xcb8]
    // 0xae3f2c: r0 = DefaultTypeTest()
    //     0xae3f2c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae3f30: r1 = <CampaignFormController>
    //     0xae3f30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcc8] TypeArguments: <CampaignFormController>
    //     0xae3f34: ldr             x1, [x1, #0xcc8]
    // 0xae3f38: r0 = GetBuilder()
    //     0xae3f38: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae3f3c: mov             x3, x0
    // 0xae3f40: ldur            x0, [fp, #-0x28]
    // 0xae3f44: stur            x3, [fp, #-0x30]
    // 0xae3f48: StoreField: r3->field_3b = r0
    //     0xae3f48: stur            w0, [x3, #0x3b]
    // 0xae3f4c: r0 = true
    //     0xae3f4c: add             x0, NULL, #0x20  ; true
    // 0xae3f50: StoreField: r3->field_13 = r0
    //     0xae3f50: stur            w0, [x3, #0x13]
    // 0xae3f54: r1 = Function '<anonymous closure>':.
    //     0xae3f54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcd0] AnonymousClosure: (0xae6410), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae3f58: ldr             x1, [x1, #0xcd0]
    // 0xae3f5c: r2 = Null
    //     0xae3f5c: mov             x2, NULL
    // 0xae3f60: r0 = AllocateClosure()
    //     0xae3f60: bl              #0xec1630  ; AllocateClosureStub
    // 0xae3f64: mov             x1, x0
    // 0xae3f68: ldur            x0, [fp, #-0x30]
    // 0xae3f6c: StoreField: r0->field_f = r1
    //     0xae3f6c: stur            w1, [x0, #0xf]
    // 0xae3f70: r2 = true
    //     0xae3f70: add             x2, NULL, #0x20  ; true
    // 0xae3f74: StoreField: r0->field_1f = r2
    //     0xae3f74: stur            w2, [x0, #0x1f]
    // 0xae3f78: r3 = false
    //     0xae3f78: add             x3, NULL, #0x30  ; false
    // 0xae3f7c: StoreField: r0->field_23 = r3
    //     0xae3f7c: stur            w3, [x0, #0x23]
    // 0xae3f80: r1 = "campaign_form"
    //     0xae3f80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcd8] "campaign_form"
    //     0xae3f84: ldr             x1, [x1, #0xcd8]
    // 0xae3f88: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3f88: stur            w1, [x0, #0x17]
    // 0xae3f8c: mov             x4, x3
    // 0xae3f90: mov             x3, x2
    // 0xae3f94: b               #0xae4204
    // 0xae3f98: r16 = Instance_PaymentType
    //     0xae3f98: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xae3f9c: ldr             x16, [x16, #0x608]
    // 0xae3fa0: cmp             w1, w16
    // 0xae3fa4: b.ne            #0xae4070
    // 0xae3fa8: ldur            x1, [fp, #-8]
    // 0xae3fac: r0 = controller()
    //     0xae3fac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae3fb0: mov             x1, x0
    // 0xae3fb4: LoadField: r0 = r1->field_3f
    //     0xae3fb4: ldur            w0, [x1, #0x3f]
    // 0xae3fb8: DecompressPointer r0
    //     0xae3fb8: add             x0, x0, HEAP, lsl #32
    // 0xae3fbc: r16 = Sentinel
    //     0xae3fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae3fc0: cmp             w0, w16
    // 0xae3fc4: b.ne            #0xae3fd4
    // 0xae3fc8: r2 = form
    //     0xae3fc8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae3fcc: ldr             x2, [x2, #0xc60]
    // 0xae3fd0: r0 = InitLateFinalInstanceField()
    //     0xae3fd0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae3fd4: mov             x3, x0
    // 0xae3fd8: r2 = Null
    //     0xae3fd8: mov             x2, NULL
    // 0xae3fdc: r1 = Null
    //     0xae3fdc: mov             x1, NULL
    // 0xae3fe0: stur            x3, [fp, #-0x28]
    // 0xae3fe4: r4 = LoadClassIdInstr(r0)
    //     0xae3fe4: ldur            x4, [x0, #-1]
    //     0xae3fe8: ubfx            x4, x4, #0xc, #0x14
    // 0xae3fec: cmp             x4, #0x77a
    // 0xae3ff0: b.eq            #0xae4008
    // 0xae3ff4: r8 = CampaignFormController
    //     0xae3ff4: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fcb0] Type: CampaignFormController
    //     0xae3ff8: ldr             x8, [x8, #0xcb0]
    // 0xae3ffc: r3 = Null
    //     0xae3ffc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fce0] Null
    //     0xae4000: ldr             x3, [x3, #0xce0]
    // 0xae4004: r0 = DefaultTypeTest()
    //     0xae4004: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae4008: r1 = <CampaignFormController>
    //     0xae4008: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcc8] TypeArguments: <CampaignFormController>
    //     0xae400c: ldr             x1, [x1, #0xcc8]
    // 0xae4010: r0 = GetBuilder()
    //     0xae4010: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae4014: mov             x3, x0
    // 0xae4018: ldur            x0, [fp, #-0x28]
    // 0xae401c: stur            x3, [fp, #-0x30]
    // 0xae4020: StoreField: r3->field_3b = r0
    //     0xae4020: stur            w0, [x3, #0x3b]
    // 0xae4024: r0 = true
    //     0xae4024: add             x0, NULL, #0x20  ; true
    // 0xae4028: StoreField: r3->field_13 = r0
    //     0xae4028: stur            w0, [x3, #0x13]
    // 0xae402c: r1 = Function '<anonymous closure>':.
    //     0xae402c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] AnonymousClosure: (0xae6410), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae4030: ldr             x1, [x1, #0xcf0]
    // 0xae4034: r2 = Null
    //     0xae4034: mov             x2, NULL
    // 0xae4038: r0 = AllocateClosure()
    //     0xae4038: bl              #0xec1630  ; AllocateClosureStub
    // 0xae403c: mov             x1, x0
    // 0xae4040: ldur            x0, [fp, #-0x30]
    // 0xae4044: StoreField: r0->field_f = r1
    //     0xae4044: stur            w1, [x0, #0xf]
    // 0xae4048: r2 = true
    //     0xae4048: add             x2, NULL, #0x20  ; true
    // 0xae404c: StoreField: r0->field_1f = r2
    //     0xae404c: stur            w2, [x0, #0x1f]
    // 0xae4050: r3 = false
    //     0xae4050: add             x3, NULL, #0x30  ; false
    // 0xae4054: StoreField: r0->field_23 = r3
    //     0xae4054: stur            w3, [x0, #0x23]
    // 0xae4058: r1 = "koin_nu_form"
    //     0xae4058: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fcf8] "koin_nu_form"
    //     0xae405c: ldr             x1, [x1, #0xcf8]
    // 0xae4060: ArrayStore: r0[0] = r1  ; List_4
    //     0xae4060: stur            w1, [x0, #0x17]
    // 0xae4064: mov             x4, x3
    // 0xae4068: mov             x3, x2
    // 0xae406c: b               #0xae4204
    // 0xae4070: r16 = Instance_PaymentType
    //     0xae4070: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xae4074: ldr             x16, [x16, #0x638]
    // 0xae4078: cmp             w1, w16
    // 0xae407c: b.ne            #0xae4148
    // 0xae4080: ldur            x1, [fp, #-8]
    // 0xae4084: r0 = controller()
    //     0xae4084: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4088: mov             x1, x0
    // 0xae408c: LoadField: r0 = r1->field_3f
    //     0xae408c: ldur            w0, [x1, #0x3f]
    // 0xae4090: DecompressPointer r0
    //     0xae4090: add             x0, x0, HEAP, lsl #32
    // 0xae4094: r16 = Sentinel
    //     0xae4094: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4098: cmp             w0, w16
    // 0xae409c: b.ne            #0xae40ac
    // 0xae40a0: r2 = form
    //     0xae40a0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae40a4: ldr             x2, [x2, #0xc60]
    // 0xae40a8: r0 = InitLateFinalInstanceField()
    //     0xae40a8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae40ac: mov             x3, x0
    // 0xae40b0: r2 = Null
    //     0xae40b0: mov             x2, NULL
    // 0xae40b4: r1 = Null
    //     0xae40b4: mov             x1, NULL
    // 0xae40b8: stur            x3, [fp, #-0x28]
    // 0xae40bc: r4 = LoadClassIdInstr(r0)
    //     0xae40bc: ldur            x4, [x0, #-1]
    //     0xae40c0: ubfx            x4, x4, #0xc, #0x14
    // 0xae40c4: cmp             x4, #0x779
    // 0xae40c8: b.eq            #0xae40e0
    // 0xae40cc: r8 = FidyahFormController
    //     0xae40cc: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fd00] Type: FidyahFormController
    //     0xae40d0: ldr             x8, [x8, #0xd00]
    // 0xae40d4: r3 = Null
    //     0xae40d4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Null
    //     0xae40d8: ldr             x3, [x3, #0xd08]
    // 0xae40dc: r0 = DefaultTypeTest()
    //     0xae40dc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae40e0: r1 = <FidyahFormController>
    //     0xae40e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd18] TypeArguments: <FidyahFormController>
    //     0xae40e4: ldr             x1, [x1, #0xd18]
    // 0xae40e8: r0 = GetBuilder()
    //     0xae40e8: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae40ec: mov             x3, x0
    // 0xae40f0: ldur            x0, [fp, #-0x28]
    // 0xae40f4: stur            x3, [fp, #-0x30]
    // 0xae40f8: StoreField: r3->field_3b = r0
    //     0xae40f8: stur            w0, [x3, #0x3b]
    // 0xae40fc: r0 = true
    //     0xae40fc: add             x0, NULL, #0x20  ; true
    // 0xae4100: StoreField: r3->field_13 = r0
    //     0xae4100: stur            w0, [x3, #0x13]
    // 0xae4104: r1 = Function '<anonymous closure>':.
    //     0xae4104: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd20] AnonymousClosure: (0xae6404), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae4108: ldr             x1, [x1, #0xd20]
    // 0xae410c: r2 = Null
    //     0xae410c: mov             x2, NULL
    // 0xae4110: r0 = AllocateClosure()
    //     0xae4110: bl              #0xec1630  ; AllocateClosureStub
    // 0xae4114: mov             x1, x0
    // 0xae4118: ldur            x0, [fp, #-0x30]
    // 0xae411c: StoreField: r0->field_f = r1
    //     0xae411c: stur            w1, [x0, #0xf]
    // 0xae4120: r2 = true
    //     0xae4120: add             x2, NULL, #0x20  ; true
    // 0xae4124: StoreField: r0->field_1f = r2
    //     0xae4124: stur            w2, [x0, #0x1f]
    // 0xae4128: r3 = false
    //     0xae4128: add             x3, NULL, #0x30  ; false
    // 0xae412c: StoreField: r0->field_23 = r3
    //     0xae412c: stur            w3, [x0, #0x23]
    // 0xae4130: r1 = "fidyah_form"
    //     0xae4130: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd28] "fidyah_form"
    //     0xae4134: ldr             x1, [x1, #0xd28]
    // 0xae4138: ArrayStore: r0[0] = r1  ; List_4
    //     0xae4138: stur            w1, [x0, #0x17]
    // 0xae413c: mov             x4, x3
    // 0xae4140: mov             x3, x2
    // 0xae4144: b               #0xae4204
    // 0xae4148: ldur            x1, [fp, #-8]
    // 0xae414c: r0 = controller()
    //     0xae414c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4150: mov             x1, x0
    // 0xae4154: LoadField: r0 = r1->field_3f
    //     0xae4154: ldur            w0, [x1, #0x3f]
    // 0xae4158: DecompressPointer r0
    //     0xae4158: add             x0, x0, HEAP, lsl #32
    // 0xae415c: r16 = Sentinel
    //     0xae415c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4160: cmp             w0, w16
    // 0xae4164: b.ne            #0xae4174
    // 0xae4168: r2 = form
    //     0xae4168: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fc60] Field <PayDonationController.form>: late final (offset: 0x40)
    //     0xae416c: ldr             x2, [x2, #0xc60]
    // 0xae4170: r0 = InitLateFinalInstanceField()
    //     0xae4170: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xae4174: mov             x3, x0
    // 0xae4178: r2 = Null
    //     0xae4178: mov             x2, NULL
    // 0xae417c: r1 = Null
    //     0xae417c: mov             x1, NULL
    // 0xae4180: stur            x3, [fp, #-0x28]
    // 0xae4184: r4 = LoadClassIdInstr(r0)
    //     0xae4184: ldur            x4, [x0, #-1]
    //     0xae4188: ubfx            x4, x4, #0xc, #0x14
    // 0xae418c: cmp             x4, #0x777
    // 0xae4190: b.eq            #0xae41a8
    // 0xae4194: r8 = QurbanFormController
    //     0xae4194: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2fd30] Type: QurbanFormController
    //     0xae4198: ldr             x8, [x8, #0xd30]
    // 0xae419c: r3 = Null
    //     0xae419c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd38] Null
    //     0xae41a0: ldr             x3, [x3, #0xd38]
    // 0xae41a4: r0 = DefaultTypeTest()
    //     0xae41a4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae41a8: r1 = <QurbanFormController>
    //     0xae41a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd48] TypeArguments: <QurbanFormController>
    //     0xae41ac: ldr             x1, [x1, #0xd48]
    // 0xae41b0: r0 = GetBuilder()
    //     0xae41b0: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xae41b4: mov             x3, x0
    // 0xae41b8: ldur            x0, [fp, #-0x28]
    // 0xae41bc: stur            x3, [fp, #-0x30]
    // 0xae41c0: StoreField: r3->field_3b = r0
    //     0xae41c0: stur            w0, [x3, #0x3b]
    // 0xae41c4: r0 = true
    //     0xae41c4: add             x0, NULL, #0x20  ; true
    // 0xae41c8: StoreField: r3->field_13 = r0
    //     0xae41c8: stur            w0, [x3, #0x13]
    // 0xae41cc: r1 = Function '<anonymous closure>':.
    //     0xae41cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd50] AnonymousClosure: (0xae63f8), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae41d0: ldr             x1, [x1, #0xd50]
    // 0xae41d4: r2 = Null
    //     0xae41d4: mov             x2, NULL
    // 0xae41d8: r0 = AllocateClosure()
    //     0xae41d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae41dc: mov             x1, x0
    // 0xae41e0: ldur            x0, [fp, #-0x30]
    // 0xae41e4: StoreField: r0->field_f = r1
    //     0xae41e4: stur            w1, [x0, #0xf]
    // 0xae41e8: r3 = true
    //     0xae41e8: add             x3, NULL, #0x20  ; true
    // 0xae41ec: StoreField: r0->field_1f = r3
    //     0xae41ec: stur            w3, [x0, #0x1f]
    // 0xae41f0: r4 = false
    //     0xae41f0: add             x4, NULL, #0x30  ; false
    // 0xae41f4: StoreField: r0->field_23 = r4
    //     0xae41f4: stur            w4, [x0, #0x23]
    // 0xae41f8: r1 = "qurban_form"
    //     0xae41f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "qurban_form"
    //     0xae41fc: ldr             x1, [x1, #0xd58]
    // 0xae4200: ArrayStore: r0[0] = r1  ; List_4
    //     0xae4200: stur            w1, [x0, #0x17]
    // 0xae4204: stur            x0, [fp, #-0x28]
    // 0xae4208: r1 = Null
    //     0xae4208: mov             x1, NULL
    // 0xae420c: r2 = 4
    //     0xae420c: movz            x2, #0x4
    // 0xae4210: r0 = AllocateArray()
    //     0xae4210: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae4214: stur            x0, [fp, #-0x30]
    // 0xae4218: r16 = "Kontak "
    //     0xae4218: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd60] "Kontak "
    //     0xae421c: ldr             x16, [x16, #0xd60]
    // 0xae4220: StoreField: r0->field_f = r16
    //     0xae4220: stur            w16, [x0, #0xf]
    // 0xae4224: r1 = Null
    //     0xae4224: mov             x1, NULL
    // 0xae4228: r2 = 6
    //     0xae4228: movz            x2, #0x6
    // 0xae422c: r0 = AllocateArray()
    //     0xae422c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae4230: stur            x0, [fp, #-0x38]
    // 0xae4234: r16 = Instance_PaymentType
    //     0xae4234: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xae4238: ldr             x16, [x16, #0x5f0]
    // 0xae423c: StoreField: r0->field_f = r16
    //     0xae423c: stur            w16, [x0, #0xf]
    // 0xae4240: r16 = Instance_PaymentType
    //     0xae4240: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xae4244: ldr             x16, [x16, #0x5c0]
    // 0xae4248: StoreField: r0->field_13 = r16
    //     0xae4248: stur            w16, [x0, #0x13]
    // 0xae424c: r16 = Instance_PaymentType
    //     0xae424c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xae4250: ldr             x16, [x16, #0x638]
    // 0xae4254: ArrayStore: r0[0] = r16  ; List_4
    //     0xae4254: stur            w16, [x0, #0x17]
    // 0xae4258: r1 = <PaymentType>
    //     0xae4258: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd68] TypeArguments: <PaymentType>
    //     0xae425c: ldr             x1, [x1, #0xd68]
    // 0xae4260: r0 = AllocateGrowableArray()
    //     0xae4260: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae4264: mov             x2, x0
    // 0xae4268: ldur            x0, [fp, #-0x38]
    // 0xae426c: stur            x2, [fp, #-0x40]
    // 0xae4270: StoreField: r2->field_f = r0
    //     0xae4270: stur            w0, [x2, #0xf]
    // 0xae4274: r0 = 6
    //     0xae4274: movz            x0, #0x6
    // 0xae4278: StoreField: r2->field_b = r0
    //     0xae4278: stur            w0, [x2, #0xb]
    // 0xae427c: ldur            x1, [fp, #-8]
    // 0xae4280: r0 = controller()
    //     0xae4280: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4284: LoadField: r2 = r0->field_1f
    //     0xae4284: ldur            w2, [x0, #0x1f]
    // 0xae4288: DecompressPointer r2
    //     0xae4288: add             x2, x2, HEAP, lsl #32
    // 0xae428c: ldur            x1, [fp, #-0x40]
    // 0xae4290: r0 = contains()
    //     0xae4290: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xae4294: tbz             w0, #4, #0xae42a4
    // 0xae4298: r0 = "(Opsional)"
    //     0xae4298: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd70] "(Opsional)"
    //     0xae429c: ldr             x0, [x0, #0xd70]
    // 0xae42a0: b               #0xae42a8
    // 0xae42a4: r0 = ""
    //     0xae42a4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae42a8: ldur            x1, [fp, #-0x30]
    // 0xae42ac: ArrayStore: r1[1] = r0  ; List_4
    //     0xae42ac: add             x25, x1, #0x13
    //     0xae42b0: str             w0, [x25]
    //     0xae42b4: tbz             w0, #0, #0xae42d0
    //     0xae42b8: ldurb           w16, [x1, #-1]
    //     0xae42bc: ldurb           w17, [x0, #-1]
    //     0xae42c0: and             x16, x17, x16, lsr #2
    //     0xae42c4: tst             x16, HEAP, lsr #32
    //     0xae42c8: b.eq            #0xae42d0
    //     0xae42cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae42d0: ldur            x16, [fp, #-0x30]
    // 0xae42d4: str             x16, [SP]
    // 0xae42d8: r0 = _interpolate()
    //     0xae42d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae42dc: r1 = Null
    //     0xae42dc: mov             x1, NULL
    // 0xae42e0: r2 = 6
    //     0xae42e0: movz            x2, #0x6
    // 0xae42e4: stur            x0, [fp, #-0x30]
    // 0xae42e8: r0 = AllocateArray()
    //     0xae42e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae42ec: stur            x0, [fp, #-0x38]
    // 0xae42f0: r16 = Instance_PaymentType
    //     0xae42f0: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xae42f4: ldr             x16, [x16, #0x5f0]
    // 0xae42f8: StoreField: r0->field_f = r16
    //     0xae42f8: stur            w16, [x0, #0xf]
    // 0xae42fc: r16 = Instance_PaymentType
    //     0xae42fc: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xae4300: ldr             x16, [x16, #0x5c0]
    // 0xae4304: StoreField: r0->field_13 = r16
    //     0xae4304: stur            w16, [x0, #0x13]
    // 0xae4308: r16 = Instance_PaymentType
    //     0xae4308: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xae430c: ldr             x16, [x16, #0x638]
    // 0xae4310: ArrayStore: r0[0] = r16  ; List_4
    //     0xae4310: stur            w16, [x0, #0x17]
    // 0xae4314: r1 = <PaymentType>
    //     0xae4314: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd68] TypeArguments: <PaymentType>
    //     0xae4318: ldr             x1, [x1, #0xd68]
    // 0xae431c: r0 = AllocateGrowableArray()
    //     0xae431c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae4320: mov             x2, x0
    // 0xae4324: ldur            x0, [fp, #-0x38]
    // 0xae4328: stur            x2, [fp, #-0x40]
    // 0xae432c: StoreField: r2->field_f = r0
    //     0xae432c: stur            w0, [x2, #0xf]
    // 0xae4330: r0 = 6
    //     0xae4330: movz            x0, #0x6
    // 0xae4334: StoreField: r2->field_b = r0
    //     0xae4334: stur            w0, [x2, #0xb]
    // 0xae4338: ldur            x1, [fp, #-8]
    // 0xae433c: r0 = controller()
    //     0xae433c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4340: LoadField: r2 = r0->field_1f
    //     0xae4340: ldur            w2, [x0, #0x1f]
    // 0xae4344: DecompressPointer r2
    //     0xae4344: add             x2, x2, HEAP, lsl #32
    // 0xae4348: ldur            x1, [fp, #-0x40]
    // 0xae434c: r0 = contains()
    //     0xae434c: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xae4350: stur            x0, [fp, #-0x38]
    // 0xae4354: r0 = NLabelTextField()
    //     0xae4354: bl              #0xa433d4  ; AllocateNLabelTextFieldStub -> NLabelTextField (size=0x1c)
    // 0xae4358: mov             x2, x0
    // 0xae435c: r0 = "Nomor HP"
    //     0xae435c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd78] "Nomor HP"
    //     0xae4360: ldr             x0, [x0, #0xd78]
    // 0xae4364: stur            x2, [fp, #-0x40]
    // 0xae4368: StoreField: r2->field_b = r0
    //     0xae4368: stur            w0, [x2, #0xb]
    // 0xae436c: ldur            x0, [fp, #-0x38]
    // 0xae4370: StoreField: r2->field_f = r0
    //     0xae4370: stur            w0, [x2, #0xf]
    // 0xae4374: r0 = Instance_EdgeInsets
    //     0xae4374: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd80] Obj!EdgeInsets@e127f1
    //     0xae4378: ldr             x0, [x0, #0xd80]
    // 0xae437c: ArrayStore: r2[0] = r0  ; List_4
    //     0xae437c: stur            w0, [x2, #0x17]
    // 0xae4380: ldur            x1, [fp, #-8]
    // 0xae4384: r0 = controller()
    //     0xae4384: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4388: LoadField: r2 = r0->field_4b
    //     0xae4388: ldur            w2, [x0, #0x4b]
    // 0xae438c: DecompressPointer r2
    //     0xae438c: add             x2, x2, HEAP, lsl #32
    // 0xae4390: ldur            x1, [fp, #-8]
    // 0xae4394: stur            x2, [fp, #-0x38]
    // 0xae4398: r0 = controller()
    //     0xae4398: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae439c: LoadField: r2 = r0->field_43
    //     0xae439c: ldur            w2, [x0, #0x43]
    // 0xae43a0: DecompressPointer r2
    //     0xae43a0: add             x2, x2, HEAP, lsl #32
    // 0xae43a4: LoadField: r3 = r2->field_7
    //     0xae43a4: ldur            w3, [x2, #7]
    // 0xae43a8: DecompressPointer r3
    //     0xae43a8: add             x3, x3, HEAP, lsl #32
    // 0xae43ac: r1 = Function 'call':.
    //     0xae43ac: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae43b0: ldr             x1, [x1, #0x310]
    // 0xae43b4: r0 = AllocateClosureTA()
    //     0xae43b4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae43b8: mov             x3, x0
    // 0xae43bc: r2 = Null
    //     0xae43bc: mov             x2, NULL
    // 0xae43c0: r1 = Null
    //     0xae43c0: mov             x1, NULL
    // 0xae43c4: stur            x3, [fp, #-0x48]
    // 0xae43c8: r8 = (dynamic this, String?) => String
    //     0xae43c8: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xae43cc: ldr             x8, [x8, #0x40]
    // 0xae43d0: r3 = Null
    //     0xae43d0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd88] Null
    //     0xae43d4: ldr             x3, [x3, #0xd88]
    // 0xae43d8: r0 = DefaultTypeTest()
    //     0xae43d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae43dc: r1 = <(dynamic this, String?) => String?>
    //     0xae43dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xae43e0: ldr             x1, [x1, #0xd98]
    // 0xae43e4: r2 = 0
    //     0xae43e4: movz            x2, #0
    // 0xae43e8: r0 = _GrowableList()
    //     0xae43e8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xae43ec: r1 = Null
    //     0xae43ec: mov             x1, NULL
    // 0xae43f0: r2 = 4
    //     0xae43f0: movz            x2, #0x4
    // 0xae43f4: stur            x0, [fp, #-0x50]
    // 0xae43f8: r0 = AllocateArray()
    //     0xae43f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae43fc: stur            x0, [fp, #-0x58]
    // 0xae4400: r16 = Instance_PaymentType
    //     0xae4400: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xae4404: ldr             x16, [x16, #0x5f0]
    // 0xae4408: StoreField: r0->field_f = r16
    //     0xae4408: stur            w16, [x0, #0xf]
    // 0xae440c: r16 = Instance_PaymentType
    //     0xae440c: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xae4410: ldr             x16, [x16, #0x5c0]
    // 0xae4414: StoreField: r0->field_13 = r16
    //     0xae4414: stur            w16, [x0, #0x13]
    // 0xae4418: r1 = <PaymentType>
    //     0xae4418: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd68] TypeArguments: <PaymentType>
    //     0xae441c: ldr             x1, [x1, #0xd68]
    // 0xae4420: r0 = AllocateGrowableArray()
    //     0xae4420: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae4424: mov             x2, x0
    // 0xae4428: ldur            x0, [fp, #-0x58]
    // 0xae442c: stur            x2, [fp, #-0x60]
    // 0xae4430: StoreField: r2->field_f = r0
    //     0xae4430: stur            w0, [x2, #0xf]
    // 0xae4434: r0 = 4
    //     0xae4434: movz            x0, #0x4
    // 0xae4438: StoreField: r2->field_b = r0
    //     0xae4438: stur            w0, [x2, #0xb]
    // 0xae443c: ldur            x1, [fp, #-8]
    // 0xae4440: r0 = controller()
    //     0xae4440: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4444: LoadField: r2 = r0->field_1f
    //     0xae4444: ldur            w2, [x0, #0x1f]
    // 0xae4448: DecompressPointer r2
    //     0xae4448: add             x2, x2, HEAP, lsl #32
    // 0xae444c: ldur            x1, [fp, #-0x60]
    // 0xae4450: r0 = contains()
    //     0xae4450: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xae4454: tbnz            w0, #4, #0xae44f8
    // 0xae4458: ldur            x0, [fp, #-0x50]
    // 0xae445c: r1 = Function '<anonymous closure>': static.
    //     0xae445c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae4460: ldr             x1, [x1, #0xda0]
    // 0xae4464: r2 = Null
    //     0xae4464: mov             x2, NULL
    // 0xae4468: r0 = AllocateClosure()
    //     0xae4468: bl              #0xec1630  ; AllocateClosureStub
    // 0xae446c: mov             x2, x0
    // 0xae4470: r0 = <String>
    //     0xae4470: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae4474: stur            x2, [fp, #-0x58]
    // 0xae4478: StoreField: r2->field_b = r0
    //     0xae4478: stur            w0, [x2, #0xb]
    // 0xae447c: ldur            x3, [fp, #-0x50]
    // 0xae4480: LoadField: r1 = r3->field_b
    //     0xae4480: ldur            w1, [x3, #0xb]
    // 0xae4484: LoadField: r4 = r3->field_f
    //     0xae4484: ldur            w4, [x3, #0xf]
    // 0xae4488: DecompressPointer r4
    //     0xae4488: add             x4, x4, HEAP, lsl #32
    // 0xae448c: LoadField: r5 = r4->field_b
    //     0xae448c: ldur            w5, [x4, #0xb]
    // 0xae4490: r4 = LoadInt32Instr(r1)
    //     0xae4490: sbfx            x4, x1, #1, #0x1f
    // 0xae4494: stur            x4, [fp, #-0x68]
    // 0xae4498: r1 = LoadInt32Instr(r5)
    //     0xae4498: sbfx            x1, x5, #1, #0x1f
    // 0xae449c: cmp             x4, x1
    // 0xae44a0: b.ne            #0xae44ac
    // 0xae44a4: mov             x1, x3
    // 0xae44a8: r0 = _growToNextCapacity()
    //     0xae44a8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae44ac: ldur            x2, [fp, #-0x50]
    // 0xae44b0: ldur            x3, [fp, #-0x68]
    // 0xae44b4: add             x0, x3, #1
    // 0xae44b8: lsl             x1, x0, #1
    // 0xae44bc: StoreField: r2->field_b = r1
    //     0xae44bc: stur            w1, [x2, #0xb]
    // 0xae44c0: LoadField: r1 = r2->field_f
    //     0xae44c0: ldur            w1, [x2, #0xf]
    // 0xae44c4: DecompressPointer r1
    //     0xae44c4: add             x1, x1, HEAP, lsl #32
    // 0xae44c8: ldur            x0, [fp, #-0x58]
    // 0xae44cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae44cc: add             x25, x1, x3, lsl #2
    //     0xae44d0: add             x25, x25, #0xf
    //     0xae44d4: str             w0, [x25]
    //     0xae44d8: tbz             w0, #0, #0xae44f4
    //     0xae44dc: ldurb           w16, [x1, #-1]
    //     0xae44e0: ldurb           w17, [x0, #-1]
    //     0xae44e4: and             x16, x17, x16, lsr #2
    //     0xae44e8: tst             x16, HEAP, lsr #32
    //     0xae44ec: b.eq            #0xae44f4
    //     0xae44f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae44f4: b               #0xae44fc
    // 0xae44f8: ldur            x2, [fp, #-0x50]
    // 0xae44fc: r16 = <String>
    //     0xae44fc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae4500: str             x16, [SP]
    // 0xae4504: r4 = const [0x1, 0, 0, 0, null]
    //     0xae4504: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xae4508: r0 = minLength()
    //     0xae4508: bl              #0xae4e2c  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::minLength
    // 0xae450c: mov             x2, x0
    // 0xae4510: ldur            x0, [fp, #-0x50]
    // 0xae4514: stur            x2, [fp, #-0x58]
    // 0xae4518: LoadField: r1 = r0->field_b
    //     0xae4518: ldur            w1, [x0, #0xb]
    // 0xae451c: LoadField: r3 = r0->field_f
    //     0xae451c: ldur            w3, [x0, #0xf]
    // 0xae4520: DecompressPointer r3
    //     0xae4520: add             x3, x3, HEAP, lsl #32
    // 0xae4524: LoadField: r4 = r3->field_b
    //     0xae4524: ldur            w4, [x3, #0xb]
    // 0xae4528: r3 = LoadInt32Instr(r1)
    //     0xae4528: sbfx            x3, x1, #1, #0x1f
    // 0xae452c: stur            x3, [fp, #-0x68]
    // 0xae4530: r1 = LoadInt32Instr(r4)
    //     0xae4530: sbfx            x1, x4, #1, #0x1f
    // 0xae4534: cmp             x3, x1
    // 0xae4538: b.ne            #0xae4544
    // 0xae453c: mov             x1, x0
    // 0xae4540: r0 = _growToNextCapacity()
    //     0xae4540: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae4544: ldur            x2, [fp, #-0x50]
    // 0xae4548: ldur            x3, [fp, #-0x68]
    // 0xae454c: r4 = 13
    //     0xae454c: movz            x4, #0xd
    // 0xae4550: add             x0, x3, #1
    // 0xae4554: lsl             x1, x0, #1
    // 0xae4558: StoreField: r2->field_b = r1
    //     0xae4558: stur            w1, [x2, #0xb]
    // 0xae455c: LoadField: r1 = r2->field_f
    //     0xae455c: ldur            w1, [x2, #0xf]
    // 0xae4560: DecompressPointer r1
    //     0xae4560: add             x1, x1, HEAP, lsl #32
    // 0xae4564: ldur            x0, [fp, #-0x58]
    // 0xae4568: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae4568: add             x25, x1, x3, lsl #2
    //     0xae456c: add             x25, x25, #0xf
    //     0xae4570: str             w0, [x25]
    //     0xae4574: tbz             w0, #0, #0xae4590
    //     0xae4578: ldurb           w16, [x1, #-1]
    //     0xae457c: ldurb           w17, [x0, #-1]
    //     0xae4580: and             x16, x17, x16, lsr #2
    //     0xae4584: tst             x16, HEAP, lsr #32
    //     0xae4588: b.eq            #0xae4590
    //     0xae458c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae4590: r16 = <String>
    //     0xae4590: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae4594: stp             x4, x16, [SP]
    // 0xae4598: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae4598: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae459c: r0 = maxLength()
    //     0xae459c: bl              #0xae4b84  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::maxLength
    // 0xae45a0: mov             x2, x0
    // 0xae45a4: ldur            x0, [fp, #-0x50]
    // 0xae45a8: stur            x2, [fp, #-0x58]
    // 0xae45ac: LoadField: r1 = r0->field_b
    //     0xae45ac: ldur            w1, [x0, #0xb]
    // 0xae45b0: LoadField: r3 = r0->field_f
    //     0xae45b0: ldur            w3, [x0, #0xf]
    // 0xae45b4: DecompressPointer r3
    //     0xae45b4: add             x3, x3, HEAP, lsl #32
    // 0xae45b8: LoadField: r4 = r3->field_b
    //     0xae45b8: ldur            w4, [x3, #0xb]
    // 0xae45bc: r3 = LoadInt32Instr(r1)
    //     0xae45bc: sbfx            x3, x1, #1, #0x1f
    // 0xae45c0: stur            x3, [fp, #-0x68]
    // 0xae45c4: r1 = LoadInt32Instr(r4)
    //     0xae45c4: sbfx            x1, x4, #1, #0x1f
    // 0xae45c8: cmp             x3, x1
    // 0xae45cc: b.ne            #0xae45d8
    // 0xae45d0: mov             x1, x0
    // 0xae45d4: r0 = _growToNextCapacity()
    //     0xae45d4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae45d8: ldur            x8, [fp, #-0x18]
    // 0xae45dc: ldur            x7, [fp, #-0x20]
    // 0xae45e0: ldur            x6, [fp, #-0x28]
    // 0xae45e4: ldur            x5, [fp, #-0x30]
    // 0xae45e8: ldur            x4, [fp, #-0x40]
    // 0xae45ec: ldur            x2, [fp, #-0x50]
    // 0xae45f0: ldur            x3, [fp, #-0x68]
    // 0xae45f4: add             x0, x3, #1
    // 0xae45f8: lsl             x1, x0, #1
    // 0xae45fc: StoreField: r2->field_b = r1
    //     0xae45fc: stur            w1, [x2, #0xb]
    // 0xae4600: LoadField: r1 = r2->field_f
    //     0xae4600: ldur            w1, [x2, #0xf]
    // 0xae4604: DecompressPointer r1
    //     0xae4604: add             x1, x1, HEAP, lsl #32
    // 0xae4608: ldur            x0, [fp, #-0x58]
    // 0xae460c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae460c: add             x25, x1, x3, lsl #2
    //     0xae4610: add             x25, x25, #0xf
    //     0xae4614: str             w0, [x25]
    //     0xae4618: tbz             w0, #0, #0xae4634
    //     0xae461c: ldurb           w16, [x1, #-1]
    //     0xae4620: ldurb           w17, [x0, #-1]
    //     0xae4624: and             x16, x17, x16, lsr #2
    //     0xae4628: tst             x16, HEAP, lsr #32
    //     0xae462c: b.eq            #0xae4634
    //     0xae4630: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae4634: r1 = 1
    //     0xae4634: movz            x1, #0x1
    // 0xae4638: r0 = AllocateContext()
    //     0xae4638: bl              #0xec126c  ; AllocateContextStub
    // 0xae463c: mov             x1, x0
    // 0xae4640: ldur            x0, [fp, #-0x50]
    // 0xae4644: StoreField: r1->field_f = r0
    //     0xae4644: stur            w0, [x1, #0xf]
    // 0xae4648: mov             x2, x1
    // 0xae464c: r1 = Function '<anonymous closure>': static.
    //     0xae464c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae4650: ldr             x1, [x1, #0xda8]
    // 0xae4654: r0 = AllocateClosure()
    //     0xae4654: bl              #0xec1630  ; AllocateClosureStub
    // 0xae4658: r1 = <String>
    //     0xae4658: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae465c: stur            x0, [fp, #-0x50]
    // 0xae4660: StoreField: r0->field_b = r1
    //     0xae4660: stur            w1, [x0, #0xb]
    // 0xae4664: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xae4664: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae4668: ldr             x0, [x0, #0xdd8]
    //     0xae466c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae4670: cmp             w0, w16
    //     0xae4674: b.ne            #0xae4684
    //     0xae4678: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xae467c: ldr             x2, [x2, #0x780]
    //     0xae4680: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae4684: stur            x0, [fp, #-0x58]
    // 0xae4688: r0 = LengthLimitingTextInputFormatter()
    //     0xae4688: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xae468c: mov             x3, x0
    // 0xae4690: r0 = 26
    //     0xae4690: movz            x0, #0x1a
    // 0xae4694: stur            x3, [fp, #-0x60]
    // 0xae4698: StoreField: r3->field_7 = r0
    //     0xae4698: stur            w0, [x3, #7]
    // 0xae469c: r1 = Null
    //     0xae469c: mov             x1, NULL
    // 0xae46a0: r2 = 4
    //     0xae46a0: movz            x2, #0x4
    // 0xae46a4: r0 = AllocateArray()
    //     0xae46a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae46a8: mov             x2, x0
    // 0xae46ac: ldur            x0, [fp, #-0x58]
    // 0xae46b0: stur            x2, [fp, #-0x70]
    // 0xae46b4: StoreField: r2->field_f = r0
    //     0xae46b4: stur            w0, [x2, #0xf]
    // 0xae46b8: ldur            x0, [fp, #-0x60]
    // 0xae46bc: StoreField: r2->field_13 = r0
    //     0xae46bc: stur            w0, [x2, #0x13]
    // 0xae46c0: r1 = <TextInputFormatter>
    //     0xae46c0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xae46c4: ldr             x1, [x1, #0x788]
    // 0xae46c8: r0 = AllocateGrowableArray()
    //     0xae46c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae46cc: mov             x2, x0
    // 0xae46d0: ldur            x0, [fp, #-0x70]
    // 0xae46d4: stur            x2, [fp, #-0x58]
    // 0xae46d8: StoreField: r2->field_f = r0
    //     0xae46d8: stur            w0, [x2, #0xf]
    // 0xae46dc: r0 = 4
    //     0xae46dc: movz            x0, #0x4
    // 0xae46e0: StoreField: r2->field_b = r0
    //     0xae46e0: stur            w0, [x2, #0xb]
    // 0xae46e4: ldur            x1, [fp, #-8]
    // 0xae46e8: r0 = controller()
    //     0xae46e8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae46ec: LoadField: r1 = r0->field_43
    //     0xae46ec: ldur            w1, [x0, #0x43]
    // 0xae46f0: DecompressPointer r1
    //     0xae46f0: add             x1, x1, HEAP, lsl #32
    // 0xae46f4: r0 = value()
    //     0xae46f4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae46f8: r1 = <String>
    //     0xae46f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae46fc: stur            x0, [fp, #-0x60]
    // 0xae4700: r0 = TextFormField()
    //     0xae4700: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae4704: stur            x0, [fp, #-0x70]
    // 0xae4708: ldur            x16, [fp, #-0x38]
    // 0xae470c: ldur            lr, [fp, #-0x48]
    // 0xae4710: stp             lr, x16, [SP, #0x20]
    // 0xae4714: ldur            x16, [fp, #-0x50]
    // 0xae4718: r30 = Instance_TextInputType
    //     0xae4718: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fdb0] Obj!TextInputType@e10e11
    //     0xae471c: ldr             lr, [lr, #0xdb0]
    // 0xae4720: stp             lr, x16, [SP, #0x10]
    // 0xae4724: ldur            x16, [fp, #-0x58]
    // 0xae4728: ldur            lr, [fp, #-0x60]
    // 0xae472c: stp             lr, x16, [SP]
    // 0xae4730: mov             x1, x0
    // 0xae4734: r2 = Instance_InputDecoration
    //     0xae4734: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdb8] Obj!InputDecoration@e14161
    //     0xae4738: ldr             x2, [x2, #0xdb8]
    // 0xae473c: r4 = const [0, 0x8, 0x6, 0x2, focusNode, 0x2, initialValue, 0x7, inputFormatters, 0x6, keyboardType, 0x5, onChanged, 0x3, validator, 0x4, null]
    //     0xae473c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] List(17) [0, 0x8, 0x6, 0x2, "focusNode", 0x2, "initialValue", 0x7, "inputFormatters", 0x6, "keyboardType", 0x5, "onChanged", 0x3, "validator", 0x4, Null]
    //     0xae4740: ldr             x4, [x4, #0xdc0]
    // 0xae4744: r0 = TextFormField()
    //     0xae4744: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae4748: r0 = _KeepAlive()
    //     0xae4748: bl              #0xae4b78  ; Allocate_KeepAliveStub -> _KeepAlive (size=0x10)
    // 0xae474c: mov             x2, x0
    // 0xae4750: ldur            x0, [fp, #-0x70]
    // 0xae4754: stur            x2, [fp, #-0x38]
    // 0xae4758: StoreField: r2->field_b = r0
    //     0xae4758: stur            w0, [x2, #0xb]
    // 0xae475c: ldur            x1, [fp, #-8]
    // 0xae4760: r0 = controller()
    //     0xae4760: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae4764: LoadField: r2 = r0->field_47
    //     0xae4764: ldur            w2, [x0, #0x47]
    // 0xae4768: DecompressPointer r2
    //     0xae4768: add             x2, x2, HEAP, lsl #32
    // 0xae476c: LoadField: r3 = r2->field_7
    //     0xae476c: ldur            w3, [x2, #7]
    // 0xae4770: DecompressPointer r3
    //     0xae4770: add             x3, x3, HEAP, lsl #32
    // 0xae4774: r1 = Function 'call':.
    //     0xae4774: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae4778: ldr             x1, [x1, #0x310]
    // 0xae477c: r0 = AllocateClosureTA()
    //     0xae477c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae4780: mov             x3, x0
    // 0xae4784: r2 = Null
    //     0xae4784: mov             x2, NULL
    // 0xae4788: r1 = Null
    //     0xae4788: mov             x1, NULL
    // 0xae478c: stur            x3, [fp, #-0x48]
    // 0xae4790: r8 = (dynamic this, String?) => String
    //     0xae4790: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xae4794: ldr             x8, [x8, #0x40]
    // 0xae4798: r3 = Null
    //     0xae4798: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdc8] Null
    //     0xae479c: ldr             x3, [x3, #0xdc8]
    // 0xae47a0: r0 = DefaultTypeTest()
    //     0xae47a0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae47a4: ldur            x1, [fp, #-8]
    // 0xae47a8: r0 = controller()
    //     0xae47a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae47ac: LoadField: r1 = r0->field_47
    //     0xae47ac: ldur            w1, [x0, #0x47]
    // 0xae47b0: DecompressPointer r1
    //     0xae47b0: add             x1, x1, HEAP, lsl #32
    // 0xae47b4: r0 = value()
    //     0xae47b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae47b8: r1 = <String>
    //     0xae47b8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae47bc: stur            x0, [fp, #-0x50]
    // 0xae47c0: r0 = TextFormField()
    //     0xae47c0: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae47c4: r1 = Function '<anonymous closure>': static.
    //     0xae47c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd8] AnonymousClosure: static (0xae5f5c), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae47c8: ldr             x1, [x1, #0xdd8]
    // 0xae47cc: r2 = Null
    //     0xae47cc: mov             x2, NULL
    // 0xae47d0: stur            x0, [fp, #-0x58]
    // 0xae47d4: r0 = AllocateClosure()
    //     0xae47d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae47d8: ldur            x16, [fp, #-0x48]
    // 0xae47dc: stp             x0, x16, [SP, #0x10]
    // 0xae47e0: r16 = Instance_TextInputType
    //     0xae47e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fde0] Obj!TextInputType@e10df1
    //     0xae47e4: ldr             x16, [x16, #0xde0]
    // 0xae47e8: ldur            lr, [fp, #-0x50]
    // 0xae47ec: stp             lr, x16, [SP]
    // 0xae47f0: ldur            x1, [fp, #-0x58]
    // 0xae47f4: r2 = Instance_InputDecoration
    //     0xae47f4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fde8] Obj!InputDecoration@e14081
    //     0xae47f8: ldr             x2, [x2, #0xde8]
    // 0xae47fc: r4 = const [0, 0x6, 0x4, 0x2, initialValue, 0x5, keyboardType, 0x4, onChanged, 0x2, validator, 0x3, null]
    //     0xae47fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdf0] List(13) [0, 0x6, 0x4, 0x2, "initialValue", 0x5, "keyboardType", 0x4, "onChanged", 0x2, "validator", 0x3, Null]
    //     0xae4800: ldr             x4, [x4, #0xdf0]
    // 0xae4804: r0 = TextFormField()
    //     0xae4804: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae4808: r1 = Null
    //     0xae4808: mov             x1, NULL
    // 0xae480c: r2 = 10
    //     0xae480c: movz            x2, #0xa
    // 0xae4810: r0 = AllocateArray()
    //     0xae4810: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae4814: mov             x2, x0
    // 0xae4818: ldur            x0, [fp, #-0x40]
    // 0xae481c: stur            x2, [fp, #-0x48]
    // 0xae4820: StoreField: r2->field_f = r0
    //     0xae4820: stur            w0, [x2, #0xf]
    // 0xae4824: ldur            x0, [fp, #-0x38]
    // 0xae4828: StoreField: r2->field_13 = r0
    //     0xae4828: stur            w0, [x2, #0x13]
    // 0xae482c: r16 = Instance_SizedBox
    //     0xae482c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae4830: ldr             x16, [x16, #0x950]
    // 0xae4834: ArrayStore: r2[0] = r16  ; List_4
    //     0xae4834: stur            w16, [x2, #0x17]
    // 0xae4838: r16 = Instance_NLabelTextField
    //     0xae4838: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!NLabelTextField@e1fbb1
    //     0xae483c: ldr             x16, [x16, #0xdf8]
    // 0xae4840: StoreField: r2->field_1b = r16
    //     0xae4840: stur            w16, [x2, #0x1b]
    // 0xae4844: ldur            x0, [fp, #-0x58]
    // 0xae4848: StoreField: r2->field_1f = r0
    //     0xae4848: stur            w0, [x2, #0x1f]
    // 0xae484c: r1 = <Widget>
    //     0xae484c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae4850: r0 = AllocateGrowableArray()
    //     0xae4850: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae4854: mov             x1, x0
    // 0xae4858: ldur            x0, [fp, #-0x48]
    // 0xae485c: stur            x1, [fp, #-0x38]
    // 0xae4860: StoreField: r1->field_f = r0
    //     0xae4860: stur            w0, [x1, #0xf]
    // 0xae4864: r2 = 10
    //     0xae4864: movz            x2, #0xa
    // 0xae4868: StoreField: r1->field_b = r2
    //     0xae4868: stur            w2, [x1, #0xb]
    // 0xae486c: r0 = NSection()
    //     0xae486c: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae4870: mov             x1, x0
    // 0xae4874: ldur            x0, [fp, #-0x30]
    // 0xae4878: stur            x1, [fp, #-0x40]
    // 0xae487c: StoreField: r1->field_b = r0
    //     0xae487c: stur            w0, [x1, #0xb]
    // 0xae4880: ldur            x0, [fp, #-0x38]
    // 0xae4884: StoreField: r1->field_f = r0
    //     0xae4884: stur            w0, [x1, #0xf]
    // 0xae4888: r0 = false
    //     0xae4888: add             x0, NULL, #0x30  ; false
    // 0xae488c: StoreField: r1->field_27 = r0
    //     0xae488c: stur            w0, [x1, #0x27]
    // 0xae4890: StoreField: r1->field_2b = r0
    //     0xae4890: stur            w0, [x1, #0x2b]
    // 0xae4894: r0 = Obx()
    //     0xae4894: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae4898: ldur            x2, [fp, #-0x10]
    // 0xae489c: r1 = Function '<anonymous closure>':.
    //     0xae489c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] AnonymousClosure: (0xae5cc0), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae48a0: ldr             x1, [x1, #0xe00]
    // 0xae48a4: stur            x0, [fp, #-0x10]
    // 0xae48a8: r0 = AllocateClosure()
    //     0xae48a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae48ac: mov             x1, x0
    // 0xae48b0: ldur            x0, [fp, #-0x10]
    // 0xae48b4: StoreField: r0->field_b = r1
    //     0xae48b4: stur            w1, [x0, #0xb]
    // 0xae48b8: r1 = Null
    //     0xae48b8: mov             x1, NULL
    // 0xae48bc: r2 = 2
    //     0xae48bc: movz            x2, #0x2
    // 0xae48c0: r0 = AllocateArray()
    //     0xae48c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae48c4: mov             x2, x0
    // 0xae48c8: ldur            x0, [fp, #-0x10]
    // 0xae48cc: stur            x2, [fp, #-0x30]
    // 0xae48d0: StoreField: r2->field_f = r0
    //     0xae48d0: stur            w0, [x2, #0xf]
    // 0xae48d4: r1 = <Widget>
    //     0xae48d4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae48d8: r0 = AllocateGrowableArray()
    //     0xae48d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae48dc: mov             x1, x0
    // 0xae48e0: ldur            x0, [fp, #-0x30]
    // 0xae48e4: stur            x1, [fp, #-0x10]
    // 0xae48e8: StoreField: r1->field_f = r0
    //     0xae48e8: stur            w0, [x1, #0xf]
    // 0xae48ec: r0 = 2
    //     0xae48ec: movz            x0, #0x2
    // 0xae48f0: StoreField: r1->field_b = r0
    //     0xae48f0: stur            w0, [x1, #0xb]
    // 0xae48f4: r0 = NSection()
    //     0xae48f4: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae48f8: mov             x3, x0
    // 0xae48fc: r0 = "Metode Pembayaran"
    //     0xae48fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] "Metode Pembayaran"
    //     0xae4900: ldr             x0, [x0, #0xe08]
    // 0xae4904: stur            x3, [fp, #-0x30]
    // 0xae4908: StoreField: r3->field_b = r0
    //     0xae4908: stur            w0, [x3, #0xb]
    // 0xae490c: ldur            x0, [fp, #-0x10]
    // 0xae4910: StoreField: r3->field_f = r0
    //     0xae4910: stur            w0, [x3, #0xf]
    // 0xae4914: r0 = false
    //     0xae4914: add             x0, NULL, #0x30  ; false
    // 0xae4918: StoreField: r3->field_27 = r0
    //     0xae4918: stur            w0, [x3, #0x27]
    // 0xae491c: StoreField: r3->field_2b = r0
    //     0xae491c: stur            w0, [x3, #0x2b]
    // 0xae4920: r1 = Null
    //     0xae4920: mov             x1, NULL
    // 0xae4924: r2 = 10
    //     0xae4924: movz            x2, #0xa
    // 0xae4928: r0 = AllocateArray()
    //     0xae4928: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae492c: mov             x2, x0
    // 0xae4930: ldur            x0, [fp, #-0x28]
    // 0xae4934: stur            x2, [fp, #-0x10]
    // 0xae4938: StoreField: r2->field_f = r0
    //     0xae4938: stur            w0, [x2, #0xf]
    // 0xae493c: r16 = Instance_NSectionDivider
    //     0xae493c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!NSectionDivider@e20a81
    //     0xae4940: ldr             x16, [x16, #0xe10]
    // 0xae4944: StoreField: r2->field_13 = r16
    //     0xae4944: stur            w16, [x2, #0x13]
    // 0xae4948: ldur            x0, [fp, #-0x40]
    // 0xae494c: ArrayStore: r2[0] = r0  ; List_4
    //     0xae494c: stur            w0, [x2, #0x17]
    // 0xae4950: r16 = Instance_NSectionDivider
    //     0xae4950: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!NSectionDivider@e20a81
    //     0xae4954: ldr             x16, [x16, #0xe10]
    // 0xae4958: StoreField: r2->field_1b = r16
    //     0xae4958: stur            w16, [x2, #0x1b]
    // 0xae495c: ldur            x0, [fp, #-0x30]
    // 0xae4960: StoreField: r2->field_1f = r0
    //     0xae4960: stur            w0, [x2, #0x1f]
    // 0xae4964: r1 = <Widget>
    //     0xae4964: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae4968: r0 = AllocateGrowableArray()
    //     0xae4968: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae496c: mov             x1, x0
    // 0xae4970: ldur            x0, [fp, #-0x10]
    // 0xae4974: stur            x1, [fp, #-0x28]
    // 0xae4978: StoreField: r1->field_f = r0
    //     0xae4978: stur            w0, [x1, #0xf]
    // 0xae497c: r0 = 10
    //     0xae497c: movz            x0, #0xa
    // 0xae4980: StoreField: r1->field_b = r0
    //     0xae4980: stur            w0, [x1, #0xb]
    // 0xae4984: r0 = ListView()
    //     0xae4984: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae4988: mov             x1, x0
    // 0xae498c: ldur            x2, [fp, #-0x28]
    // 0xae4990: stur            x0, [fp, #-0x10]
    // 0xae4994: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae4994: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae4998: r0 = ListView()
    //     0xae4998: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xae499c: r0 = Form()
    //     0xae499c: bl              #0xae4b6c  ; AllocateFormStub -> Form (size=0x28)
    // 0xae49a0: mov             x2, x0
    // 0xae49a4: ldur            x0, [fp, #-0x10]
    // 0xae49a8: stur            x2, [fp, #-0x28]
    // 0xae49ac: StoreField: r2->field_b = r0
    //     0xae49ac: stur            w0, [x2, #0xb]
    // 0xae49b0: r0 = Instance_AutovalidateMode
    //     0xae49b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!AutovalidateMode@e34441
    //     0xae49b4: ldr             x0, [x0, #0xe18]
    // 0xae49b8: StoreField: r2->field_23 = r0
    //     0xae49b8: stur            w0, [x2, #0x23]
    // 0xae49bc: ldur            x0, [fp, #-0x20]
    // 0xae49c0: StoreField: r2->field_7 = r0
    //     0xae49c0: stur            w0, [x2, #7]
    // 0xae49c4: r1 = <FlexParentData>
    //     0xae49c4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xae49c8: ldr             x1, [x1, #0x720]
    // 0xae49cc: r0 = Expanded()
    //     0xae49cc: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae49d0: mov             x2, x0
    // 0xae49d4: r0 = 1
    //     0xae49d4: movz            x0, #0x1
    // 0xae49d8: stur            x2, [fp, #-0x10]
    // 0xae49dc: StoreField: r2->field_13 = r0
    //     0xae49dc: stur            x0, [x2, #0x13]
    // 0xae49e0: r0 = Instance_FlexFit
    //     0xae49e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xae49e4: ldr             x0, [x0, #0x728]
    // 0xae49e8: StoreField: r2->field_1b = r0
    //     0xae49e8: stur            w0, [x2, #0x1b]
    // 0xae49ec: ldur            x0, [fp, #-0x28]
    // 0xae49f0: StoreField: r2->field_b = r0
    //     0xae49f0: stur            w0, [x2, #0xb]
    // 0xae49f4: ldur            x1, [fp, #-8]
    // 0xae49f8: r0 = controller()
    //     0xae49f8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae49fc: mov             x2, x0
    // 0xae4a00: r1 = Function 'next':.
    //     0xae4a00: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] AnonymousClosure: (0xae50c8), in [package:nuonline/app/modules/donation/controllers/pay_donation_controller.dart] PayDonationController::next (0xae5100)
    //     0xae4a04: ldr             x1, [x1, #0xe20]
    // 0xae4a08: r0 = AllocateClosure()
    //     0xae4a08: bl              #0xec1630  ; AllocateClosureStub
    // 0xae4a0c: stur            x0, [fp, #-8]
    // 0xae4a10: r0 = TextButton()
    //     0xae4a10: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae4a14: mov             x1, x0
    // 0xae4a18: ldur            x0, [fp, #-8]
    // 0xae4a1c: stur            x1, [fp, #-0x20]
    // 0xae4a20: StoreField: r1->field_b = r0
    //     0xae4a20: stur            w0, [x1, #0xb]
    // 0xae4a24: r0 = false
    //     0xae4a24: add             x0, NULL, #0x30  ; false
    // 0xae4a28: StoreField: r1->field_27 = r0
    //     0xae4a28: stur            w0, [x1, #0x27]
    // 0xae4a2c: r2 = true
    //     0xae4a2c: add             x2, NULL, #0x20  ; true
    // 0xae4a30: StoreField: r1->field_2f = r2
    //     0xae4a30: stur            w2, [x1, #0x2f]
    // 0xae4a34: r3 = Instance_Text
    //     0xae4a34: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe28] Obj!Text@e21be1
    //     0xae4a38: ldr             x3, [x3, #0xe28]
    // 0xae4a3c: StoreField: r1->field_37 = r3
    //     0xae4a3c: stur            w3, [x1, #0x37]
    // 0xae4a40: r0 = NPersistentFooterButton()
    //     0xae4a40: bl              #0xadd6a8  ; AllocateNPersistentFooterButtonStub -> NPersistentFooterButton (size=0x10)
    // 0xae4a44: mov             x3, x0
    // 0xae4a48: ldur            x0, [fp, #-0x20]
    // 0xae4a4c: stur            x3, [fp, #-8]
    // 0xae4a50: StoreField: r3->field_b = r0
    //     0xae4a50: stur            w0, [x3, #0xb]
    // 0xae4a54: r1 = Null
    //     0xae4a54: mov             x1, NULL
    // 0xae4a58: r2 = 4
    //     0xae4a58: movz            x2, #0x4
    // 0xae4a5c: r0 = AllocateArray()
    //     0xae4a5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae4a60: mov             x2, x0
    // 0xae4a64: ldur            x0, [fp, #-0x10]
    // 0xae4a68: stur            x2, [fp, #-0x20]
    // 0xae4a6c: StoreField: r2->field_f = r0
    //     0xae4a6c: stur            w0, [x2, #0xf]
    // 0xae4a70: ldur            x0, [fp, #-8]
    // 0xae4a74: StoreField: r2->field_13 = r0
    //     0xae4a74: stur            w0, [x2, #0x13]
    // 0xae4a78: r1 = <Widget>
    //     0xae4a78: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae4a7c: r0 = AllocateGrowableArray()
    //     0xae4a7c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae4a80: mov             x1, x0
    // 0xae4a84: ldur            x0, [fp, #-0x20]
    // 0xae4a88: stur            x1, [fp, #-8]
    // 0xae4a8c: StoreField: r1->field_f = r0
    //     0xae4a8c: stur            w0, [x1, #0xf]
    // 0xae4a90: r0 = 4
    //     0xae4a90: movz            x0, #0x4
    // 0xae4a94: StoreField: r1->field_b = r0
    //     0xae4a94: stur            w0, [x1, #0xb]
    // 0xae4a98: r0 = Column()
    //     0xae4a98: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae4a9c: mov             x1, x0
    // 0xae4aa0: r0 = Instance_Axis
    //     0xae4aa0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae4aa4: stur            x1, [fp, #-0x10]
    // 0xae4aa8: StoreField: r1->field_f = r0
    //     0xae4aa8: stur            w0, [x1, #0xf]
    // 0xae4aac: r0 = Instance_MainAxisAlignment
    //     0xae4aac: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae4ab0: ldr             x0, [x0, #0x730]
    // 0xae4ab4: StoreField: r1->field_13 = r0
    //     0xae4ab4: stur            w0, [x1, #0x13]
    // 0xae4ab8: r0 = Instance_MainAxisSize
    //     0xae4ab8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae4abc: ldr             x0, [x0, #0x738]
    // 0xae4ac0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae4ac0: stur            w0, [x1, #0x17]
    // 0xae4ac4: r0 = Instance_CrossAxisAlignment
    //     0xae4ac4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae4ac8: ldr             x0, [x0, #0x740]
    // 0xae4acc: StoreField: r1->field_1b = r0
    //     0xae4acc: stur            w0, [x1, #0x1b]
    // 0xae4ad0: r0 = Instance_VerticalDirection
    //     0xae4ad0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae4ad4: ldr             x0, [x0, #0x748]
    // 0xae4ad8: StoreField: r1->field_23 = r0
    //     0xae4ad8: stur            w0, [x1, #0x23]
    // 0xae4adc: r0 = Instance_Clip
    //     0xae4adc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae4ae0: ldr             x0, [x0, #0x750]
    // 0xae4ae4: StoreField: r1->field_2b = r0
    //     0xae4ae4: stur            w0, [x1, #0x2b]
    // 0xae4ae8: StoreField: r1->field_2f = rZR
    //     0xae4ae8: stur            xzr, [x1, #0x2f]
    // 0xae4aec: ldur            x0, [fp, #-8]
    // 0xae4af0: StoreField: r1->field_b = r0
    //     0xae4af0: stur            w0, [x1, #0xb]
    // 0xae4af4: r0 = Scaffold()
    //     0xae4af4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae4af8: mov             x1, x0
    // 0xae4afc: ldur            x0, [fp, #-0x18]
    // 0xae4b00: stur            x1, [fp, #-8]
    // 0xae4b04: StoreField: r1->field_13 = r0
    //     0xae4b04: stur            w0, [x1, #0x13]
    // 0xae4b08: ldur            x0, [fp, #-0x10]
    // 0xae4b0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae4b0c: stur            w0, [x1, #0x17]
    // 0xae4b10: r0 = Instance_AlignmentDirectional
    //     0xae4b10: add             x0, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae4b14: ldr             x0, [x0, #0x758]
    // 0xae4b18: StoreField: r1->field_2b = r0
    //     0xae4b18: stur            w0, [x1, #0x2b]
    // 0xae4b1c: r0 = true
    //     0xae4b1c: add             x0, NULL, #0x20  ; true
    // 0xae4b20: StoreField: r1->field_53 = r0
    //     0xae4b20: stur            w0, [x1, #0x53]
    // 0xae4b24: r2 = Instance_DragStartBehavior
    //     0xae4b24: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae4b28: StoreField: r1->field_57 = r2
    //     0xae4b28: stur            w2, [x1, #0x57]
    // 0xae4b2c: r2 = false
    //     0xae4b2c: add             x2, NULL, #0x30  ; false
    // 0xae4b30: StoreField: r1->field_b = r2
    //     0xae4b30: stur            w2, [x1, #0xb]
    // 0xae4b34: StoreField: r1->field_f = r2
    //     0xae4b34: stur            w2, [x1, #0xf]
    // 0xae4b38: StoreField: r1->field_5f = r0
    //     0xae4b38: stur            w0, [x1, #0x5f]
    // 0xae4b3c: StoreField: r1->field_63 = r0
    //     0xae4b3c: stur            w0, [x1, #0x63]
    // 0xae4b40: r0 = NDismissableKeyboard()
    //     0xae4b40: bl              #0xae4b60  ; AllocateNDismissableKeyboardStub -> NDismissableKeyboard (size=0x10)
    // 0xae4b44: ldur            x1, [fp, #-8]
    // 0xae4b48: StoreField: r0->field_b = r1
    //     0xae4b48: stur            w1, [x0, #0xb]
    // 0xae4b4c: LeaveFrame
    //     0xae4b4c: mov             SP, fp
    //     0xae4b50: ldp             fp, lr, [SP], #0x10
    // 0xae4b54: ret
    //     0xae4b54: ret             
    // 0xae4b58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4b58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4b5c: b               #0xae3cb4
  }
  [closure] NPaymentMethodListTile <anonymous closure>(dynamic) {
    // ** addr: 0xae5cc0, size: 0x1b8
    // 0xae5cc0: EnterFrame
    //     0xae5cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xae5cc4: mov             fp, SP
    // 0xae5cc8: AllocStack(0x28)
    //     0xae5cc8: sub             SP, SP, #0x28
    // 0xae5ccc: SetupParameters()
    //     0xae5ccc: ldr             x0, [fp, #0x10]
    //     0xae5cd0: ldur            w2, [x0, #0x17]
    //     0xae5cd4: add             x2, x2, HEAP, lsl #32
    //     0xae5cd8: stur            x2, [fp, #-8]
    // 0xae5cdc: CheckStackOverflow
    //     0xae5cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5ce0: cmp             SP, x16
    //     0xae5ce4: b.ls            #0xae5e70
    // 0xae5ce8: LoadField: r1 = r2->field_f
    //     0xae5ce8: ldur            w1, [x2, #0xf]
    // 0xae5cec: DecompressPointer r1
    //     0xae5cec: add             x1, x1, HEAP, lsl #32
    // 0xae5cf0: r0 = controller()
    //     0xae5cf0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae5cf4: LoadField: r1 = r0->field_33
    //     0xae5cf4: ldur            w1, [x0, #0x33]
    // 0xae5cf8: DecompressPointer r1
    //     0xae5cf8: add             x1, x1, HEAP, lsl #32
    // 0xae5cfc: r0 = value()
    //     0xae5cfc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae5d00: LoadField: r1 = r0->field_7
    //     0xae5d00: ldur            x1, [x0, #7]
    // 0xae5d04: cmp             x1, #1
    // 0xae5d08: b.gt            #0xae5d2c
    // 0xae5d0c: cmp             x1, #0
    // 0xae5d10: b.gt            #0xae5d20
    // 0xae5d14: r0 = "assets/images/qris.svg"
    //     0xae5d14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff30] "assets/images/qris.svg"
    //     0xae5d18: ldr             x0, [x0, #0xf30]
    // 0xae5d1c: b               #0xae5d48
    // 0xae5d20: r0 = "assets/images/bni.svg"
    //     0xae5d20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff38] "assets/images/bni.svg"
    //     0xae5d24: ldr             x0, [x0, #0xf38]
    // 0xae5d28: b               #0xae5d48
    // 0xae5d2c: cmp             x1, #2
    // 0xae5d30: b.gt            #0xae5d40
    // 0xae5d34: r0 = "assets/images/bsi.svg"
    //     0xae5d34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff40] "assets/images/bsi.svg"
    //     0xae5d38: ldr             x0, [x0, #0xf40]
    // 0xae5d3c: b               #0xae5d48
    // 0xae5d40: r0 = "assets/images/bri.svg"
    //     0xae5d40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff48] "assets/images/bri.svg"
    //     0xae5d44: ldr             x0, [x0, #0xf48]
    // 0xae5d48: ldur            x2, [fp, #-8]
    // 0xae5d4c: stur            x0, [fp, #-0x10]
    // 0xae5d50: LoadField: r1 = r2->field_f
    //     0xae5d50: ldur            w1, [x2, #0xf]
    // 0xae5d54: DecompressPointer r1
    //     0xae5d54: add             x1, x1, HEAP, lsl #32
    // 0xae5d58: r0 = controller()
    //     0xae5d58: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae5d5c: LoadField: r1 = r0->field_33
    //     0xae5d5c: ldur            w1, [x0, #0x33]
    // 0xae5d60: DecompressPointer r1
    //     0xae5d60: add             x1, x1, HEAP, lsl #32
    // 0xae5d64: r0 = value()
    //     0xae5d64: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae5d68: LoadField: r1 = r0->field_7
    //     0xae5d68: ldur            x1, [x0, #7]
    // 0xae5d6c: cmp             x1, #1
    // 0xae5d70: b.gt            #0xae5d94
    // 0xae5d74: cmp             x1, #0
    // 0xae5d78: b.gt            #0xae5d88
    // 0xae5d7c: r0 = "QRIS"
    //     0xae5d7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xae5d80: ldr             x0, [x0, #0xf50]
    // 0xae5d84: b               #0xae5db0
    // 0xae5d88: r0 = "BNI Virtual Account"
    //     0xae5d88: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xae5d8c: ldr             x0, [x0, #0xf58]
    // 0xae5d90: b               #0xae5db0
    // 0xae5d94: cmp             x1, #2
    // 0xae5d98: b.gt            #0xae5da8
    // 0xae5d9c: r0 = "BSI Virtual Account"
    //     0xae5d9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xae5da0: ldr             x0, [x0, #0xf60]
    // 0xae5da4: b               #0xae5db0
    // 0xae5da8: r0 = "BRI Virtual Account"
    //     0xae5da8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xae5dac: ldr             x0, [x0, #0xf68]
    // 0xae5db0: ldur            x2, [fp, #-8]
    // 0xae5db4: stur            x0, [fp, #-0x18]
    // 0xae5db8: LoadField: r1 = r2->field_f
    //     0xae5db8: ldur            w1, [x2, #0xf]
    // 0xae5dbc: DecompressPointer r1
    //     0xae5dbc: add             x1, x1, HEAP, lsl #32
    // 0xae5dc0: r0 = controller()
    //     0xae5dc0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae5dc4: LoadField: r1 = r0->field_33
    //     0xae5dc4: ldur            w1, [x0, #0x33]
    // 0xae5dc8: DecompressPointer r1
    //     0xae5dc8: add             x1, x1, HEAP, lsl #32
    // 0xae5dcc: r0 = value()
    //     0xae5dcc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae5dd0: LoadField: r1 = r0->field_7
    //     0xae5dd0: ldur            x1, [x0, #7]
    // 0xae5dd4: cmp             x1, #1
    // 0xae5dd8: b.gt            #0xae5dfc
    // 0xae5ddc: cmp             x1, #0
    // 0xae5de0: b.gt            #0xae5df0
    // 0xae5de4: r2 = "Biaya transaksi gratis!"
    //     0xae5de4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2ff70] "Biaya transaksi gratis!"
    //     0xae5de8: ldr             x2, [x2, #0xf70]
    // 0xae5dec: b               #0xae5e18
    // 0xae5df0: r2 = "Biaya transaksi Rp2.500"
    //     0xae5df0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2ff78] "Biaya transaksi Rp2.500"
    //     0xae5df4: ldr             x2, [x2, #0xf78]
    // 0xae5df8: b               #0xae5e18
    // 0xae5dfc: cmp             x1, #2
    // 0xae5e00: b.gt            #0xae5e10
    // 0xae5e04: r2 = "Biaya transaksi Rp2.500"
    //     0xae5e04: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2ff78] "Biaya transaksi Rp2.500"
    //     0xae5e08: ldr             x2, [x2, #0xf78]
    // 0xae5e0c: b               #0xae5e18
    // 0xae5e10: r2 = "Biaya transaksi Rp3.500"
    //     0xae5e10: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2ff80] "Biaya transaksi Rp3.500"
    //     0xae5e14: ldr             x2, [x2, #0xf80]
    // 0xae5e18: ldur            x1, [fp, #-0x10]
    // 0xae5e1c: ldur            x0, [fp, #-0x18]
    // 0xae5e20: stur            x2, [fp, #-0x20]
    // 0xae5e24: r0 = NPaymentMethodListTile()
    //     0xae5e24: bl              #0xae5e78  ; AllocateNPaymentMethodListTileStub -> NPaymentMethodListTile (size=0x1c)
    // 0xae5e28: mov             x3, x0
    // 0xae5e2c: ldur            x0, [fp, #-0x10]
    // 0xae5e30: stur            x3, [fp, #-0x28]
    // 0xae5e34: StoreField: r3->field_b = r0
    //     0xae5e34: stur            w0, [x3, #0xb]
    // 0xae5e38: ldur            x0, [fp, #-0x18]
    // 0xae5e3c: StoreField: r3->field_f = r0
    //     0xae5e3c: stur            w0, [x3, #0xf]
    // 0xae5e40: ldur            x0, [fp, #-0x20]
    // 0xae5e44: StoreField: r3->field_13 = r0
    //     0xae5e44: stur            w0, [x3, #0x13]
    // 0xae5e48: ldur            x2, [fp, #-8]
    // 0xae5e4c: r1 = Function '<anonymous closure>':.
    //     0xae5e4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2ff88] AnonymousClosure: (0xae5e84), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae5e50: ldr             x1, [x1, #0xf88]
    // 0xae5e54: r0 = AllocateClosure()
    //     0xae5e54: bl              #0xec1630  ; AllocateClosureStub
    // 0xae5e58: mov             x1, x0
    // 0xae5e5c: ldur            x0, [fp, #-0x28]
    // 0xae5e60: ArrayStore: r0[0] = r1  ; List_4
    //     0xae5e60: stur            w1, [x0, #0x17]
    // 0xae5e64: LeaveFrame
    //     0xae5e64: mov             SP, fp
    //     0xae5e68: ldp             fp, lr, [SP], #0x10
    // 0xae5e6c: ret
    //     0xae5e6c: ret             
    // 0xae5e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5e74: b               #0xae5ce8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae5e84, size: 0x74
    // 0xae5e84: EnterFrame
    //     0xae5e84: stp             fp, lr, [SP, #-0x10]!
    //     0xae5e88: mov             fp, SP
    // 0xae5e8c: AllocStack(0x18)
    //     0xae5e8c: sub             SP, SP, #0x18
    // 0xae5e90: SetupParameters()
    //     0xae5e90: ldr             x0, [fp, #0x10]
    //     0xae5e94: ldur            w2, [x0, #0x17]
    //     0xae5e98: add             x2, x2, HEAP, lsl #32
    //     0xae5e9c: stur            x2, [fp, #-8]
    // 0xae5ea0: CheckStackOverflow
    //     0xae5ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5ea4: cmp             SP, x16
    //     0xae5ea8: b.ls            #0xae5ef0
    // 0xae5eac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae5eac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae5eb0: ldr             x0, [x0, #0x2670]
    //     0xae5eb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae5eb8: cmp             w0, w16
    //     0xae5ebc: b.ne            #0xae5ec8
    //     0xae5ec0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae5ec4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae5ec8: ldur            x2, [fp, #-8]
    // 0xae5ecc: r1 = Function '<anonymous closure>':.
    //     0xae5ecc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2ff90] AnonymousClosure: (0xae5ef8), in [package:nuonline/app/modules/donation/views/pay_donation_view.dart] PayDonationView::build (0xae3c98)
    //     0xae5ed0: ldr             x1, [x1, #0xf90]
    // 0xae5ed4: r0 = AllocateClosure()
    //     0xae5ed4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae5ed8: stp             x0, NULL, [SP]
    // 0xae5edc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae5edc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae5ee0: r0 = GetNavigation.to()
    //     0xae5ee0: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xae5ee4: LeaveFrame
    //     0xae5ee4: mov             SP, fp
    //     0xae5ee8: ldp             fp, lr, [SP], #0x10
    // 0xae5eec: ret
    //     0xae5eec: ret             
    // 0xae5ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5ef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5ef4: b               #0xae5eac
  }
  [closure] PaymentMethodView <anonymous closure>(dynamic) {
    // ** addr: 0xae5ef8, size: 0x58
    // 0xae5ef8: EnterFrame
    //     0xae5ef8: stp             fp, lr, [SP, #-0x10]!
    //     0xae5efc: mov             fp, SP
    // 0xae5f00: AllocStack(0x8)
    //     0xae5f00: sub             SP, SP, #8
    // 0xae5f04: SetupParameters()
    //     0xae5f04: ldr             x0, [fp, #0x10]
    //     0xae5f08: ldur            w1, [x0, #0x17]
    //     0xae5f0c: add             x1, x1, HEAP, lsl #32
    // 0xae5f10: CheckStackOverflow
    //     0xae5f10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5f14: cmp             SP, x16
    //     0xae5f18: b.ls            #0xae5f48
    // 0xae5f1c: LoadField: r0 = r1->field_f
    //     0xae5f1c: ldur            w0, [x1, #0xf]
    // 0xae5f20: DecompressPointer r0
    //     0xae5f20: add             x0, x0, HEAP, lsl #32
    // 0xae5f24: mov             x1, x0
    // 0xae5f28: r0 = controller()
    //     0xae5f28: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae5f2c: stur            x0, [fp, #-8]
    // 0xae5f30: r0 = PaymentMethodView()
    //     0xae5f30: bl              #0xae5f50  ; AllocatePaymentMethodViewStub -> PaymentMethodView (size=0x10)
    // 0xae5f34: ldur            x1, [fp, #-8]
    // 0xae5f38: StoreField: r0->field_b = r1
    //     0xae5f38: stur            w1, [x0, #0xb]
    // 0xae5f3c: LeaveFrame
    //     0xae5f3c: mov             SP, fp
    //     0xae5f40: ldp             fp, lr, [SP], #0x10
    // 0xae5f44: ret
    //     0xae5f44: ret             
    // 0xae5f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5f4c: b               #0xae5f1c
  }
  [closure] QurbanFormWidget <anonymous closure>(dynamic, QurbanFormController) {
    // ** addr: 0xae63f8, size: 0xc
    // 0xae63f8: r0 = Instance_QurbanFormWidget
    //     0xae63f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ffe8] Obj!QurbanFormWidget@e21321
    //     0xae63fc: ldr             x0, [x0, #0xfe8]
    // 0xae6400: ret
    //     0xae6400: ret             
  }
  [closure] FidyahForm <anonymous closure>(dynamic, FidyahFormController) {
    // ** addr: 0xae6404, size: 0xc
    // 0xae6404: r0 = Instance_FidyahForm
    //     0xae6404: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fff0] Obj!FidyahForm@e21341
    //     0xae6408: ldr             x0, [x0, #0xff0]
    // 0xae640c: ret
    //     0xae640c: ret             
  }
  [closure] CampaignFormWidget <anonymous closure>(dynamic, CampaignFormController) {
    // ** addr: 0xae6410, size: 0xc
    // 0xae6410: r0 = Instance_CampaignFormWidget
    //     0xae6410: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fff8] Obj!CampaignFormWidget@e213e1
    //     0xae6414: ldr             x0, [x0, #0xff8]
    // 0xae6418: ret
    //     0xae6418: ret             
  }
  [closure] DonationFormWidget <anonymous closure>(dynamic, DonationFormController) {
    // ** addr: 0xae641c, size: 0xc
    // 0xae641c: r0 = Instance_DonationFormWidget
    //     0xae641c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30000] Obj!DonationFormWidget@e213a1
    //     0xae6420: ldr             x0, [x0]
    // 0xae6424: ret
    //     0xae6424: ret             
  }
  [closure] ZakatFormWidget <anonymous closure>(dynamic, ZakatFormController) {
    // ** addr: 0xae6428, size: 0xc
    // 0xae6428: r0 = Instance_ZakatFormWidget
    //     0xae6428: add             x0, PP, #0x30, lsl #12  ; [pp+0x30008] Obj!ZakatFormWidget@e21301
    //     0xae642c: ldr             x0, [x0, #8]
    // 0xae6430: ret
    //     0xae6430: ret             
  }
}
