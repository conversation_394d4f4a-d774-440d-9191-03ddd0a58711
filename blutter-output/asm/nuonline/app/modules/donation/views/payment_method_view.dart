// lib: , url: package:nuonline/app/modules/donation/views/payment_method_view.dart

// class id: 1050227, size: 0x8
class :: {
}

// class id: 4117, size: 0x20, field offset: 0x14
class _PaymentMethodViewState extends State<dynamic> {

  late PaymentMethod selected; // offset: 0x1c

  _ build(/* No info */) {
    // ** addr: 0xa37240, size: 0x2d8
    // 0xa37240: EnterFrame
    //     0xa37240: stp             fp, lr, [SP, #-0x10]!
    //     0xa37244: mov             fp, SP
    // 0xa37248: AllocStack(0x40)
    //     0xa37248: sub             SP, SP, #0x40
    // 0xa3724c: SetupParameters(_PaymentMethodViewState this /* r1 => r0, fp-0x8 */)
    //     0xa3724c: mov             x0, x1
    //     0xa37250: stur            x1, [fp, #-8]
    // 0xa37254: CheckStackOverflow
    //     0xa37254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37258: cmp             SP, x16
    //     0xa3725c: b.ls            #0xa37510
    // 0xa37260: r1 = 1
    //     0xa37260: movz            x1, #0x1
    // 0xa37264: r0 = AllocateContext()
    //     0xa37264: bl              #0xec126c  ; AllocateContextStub
    // 0xa37268: ldur            x2, [fp, #-8]
    // 0xa3726c: stur            x0, [fp, #-0x10]
    // 0xa37270: StoreField: r0->field_f = r2
    //     0xa37270: stur            w2, [x0, #0xf]
    // 0xa37274: r0 = AppBar()
    //     0xa37274: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xa37278: stur            x0, [fp, #-0x18]
    // 0xa3727c: r16 = Instance_Text
    //     0xa3727c: add             x16, PP, #0x40, lsl #12  ; [pp+0x400b8] Obj!Text@e21551
    //     0xa37280: ldr             x16, [x16, #0xb8]
    // 0xa37284: str             x16, [SP]
    // 0xa37288: mov             x1, x0
    // 0xa3728c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xa3728c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xa37290: ldr             x4, [x4, #0x6e8]
    // 0xa37294: r0 = AppBar()
    //     0xa37294: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xa37298: ldur            x0, [fp, #-8]
    // 0xa3729c: LoadField: r3 = r0->field_13
    //     0xa3729c: ldur            w3, [x0, #0x13]
    // 0xa372a0: DecompressPointer r3
    //     0xa372a0: add             x3, x3, HEAP, lsl #32
    // 0xa372a4: ldur            x2, [fp, #-0x10]
    // 0xa372a8: stur            x3, [fp, #-0x20]
    // 0xa372ac: r1 = Function '<anonymous closure>':.
    //     0xa372ac: add             x1, PP, #0x40, lsl #12  ; [pp+0x400c0] AnonymousClosure: (0xa382f0), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::build (0xa37240)
    //     0xa372b0: ldr             x1, [x1, #0xc0]
    // 0xa372b4: r0 = AllocateClosure()
    //     0xa372b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa372b8: r16 = <_PaymentMethodItem>
    //     0xa372b8: add             x16, PP, #0x40, lsl #12  ; [pp+0x400c8] TypeArguments: <_PaymentMethodItem>
    //     0xa372bc: ldr             x16, [x16, #0xc8]
    // 0xa372c0: ldur            lr, [fp, #-0x20]
    // 0xa372c4: stp             lr, x16, [SP, #8]
    // 0xa372c8: str             x0, [SP]
    // 0xa372cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa372cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa372d0: r0 = map()
    //     0xa372d0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa372d4: LoadField: r1 = r0->field_7
    //     0xa372d4: ldur            w1, [x0, #7]
    // 0xa372d8: DecompressPointer r1
    //     0xa372d8: add             x1, x1, HEAP, lsl #32
    // 0xa372dc: mov             x2, x0
    // 0xa372e0: r0 = _GrowableList.of()
    //     0xa372e0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa372e4: stur            x0, [fp, #-0x20]
    // 0xa372e8: r0 = NSection()
    //     0xa372e8: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xa372ec: mov             x3, x0
    // 0xa372f0: r0 = ""
    //     0xa372f0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa372f4: stur            x3, [fp, #-0x28]
    // 0xa372f8: StoreField: r3->field_b = r0
    //     0xa372f8: stur            w0, [x3, #0xb]
    // 0xa372fc: ldur            x0, [fp, #-0x20]
    // 0xa37300: StoreField: r3->field_f = r0
    //     0xa37300: stur            w0, [x3, #0xf]
    // 0xa37304: r0 = false
    //     0xa37304: add             x0, NULL, #0x30  ; false
    // 0xa37308: StoreField: r3->field_27 = r0
    //     0xa37308: stur            w0, [x3, #0x27]
    // 0xa3730c: StoreField: r3->field_2b = r0
    //     0xa3730c: stur            w0, [x3, #0x2b]
    // 0xa37310: ldur            x4, [fp, #-8]
    // 0xa37314: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xa37314: ldur            w5, [x4, #0x17]
    // 0xa37318: DecompressPointer r5
    //     0xa37318: add             x5, x5, HEAP, lsl #32
    // 0xa3731c: ldur            x2, [fp, #-0x10]
    // 0xa37320: stur            x5, [fp, #-0x20]
    // 0xa37324: r1 = Function '<anonymous closure>':.
    //     0xa37324: add             x1, PP, #0x40, lsl #12  ; [pp+0x400d0] AnonymousClosure: (0xa38104), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::build (0xa37240)
    //     0xa37328: ldr             x1, [x1, #0xd0]
    // 0xa3732c: r0 = AllocateClosure()
    //     0xa3732c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa37330: r16 = <_PaymentMethodItem>
    //     0xa37330: add             x16, PP, #0x40, lsl #12  ; [pp+0x400c8] TypeArguments: <_PaymentMethodItem>
    //     0xa37334: ldr             x16, [x16, #0xc8]
    // 0xa37338: ldur            lr, [fp, #-0x20]
    // 0xa3733c: stp             lr, x16, [SP, #8]
    // 0xa37340: str             x0, [SP]
    // 0xa37344: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa37344: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa37348: r0 = map()
    //     0xa37348: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa3734c: LoadField: r1 = r0->field_7
    //     0xa3734c: ldur            w1, [x0, #7]
    // 0xa37350: DecompressPointer r1
    //     0xa37350: add             x1, x1, HEAP, lsl #32
    // 0xa37354: mov             x2, x0
    // 0xa37358: r0 = _GrowableList.of()
    //     0xa37358: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa3735c: stur            x0, [fp, #-0x10]
    // 0xa37360: r0 = NSection()
    //     0xa37360: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xa37364: mov             x3, x0
    // 0xa37368: r0 = "Virtual Account"
    //     0xa37368: add             x0, PP, #0x40, lsl #12  ; [pp+0x400d8] "Virtual Account"
    //     0xa3736c: ldr             x0, [x0, #0xd8]
    // 0xa37370: stur            x3, [fp, #-0x20]
    // 0xa37374: StoreField: r3->field_b = r0
    //     0xa37374: stur            w0, [x3, #0xb]
    // 0xa37378: ldur            x0, [fp, #-0x10]
    // 0xa3737c: StoreField: r3->field_f = r0
    //     0xa3737c: stur            w0, [x3, #0xf]
    // 0xa37380: r0 = false
    //     0xa37380: add             x0, NULL, #0x30  ; false
    // 0xa37384: StoreField: r3->field_27 = r0
    //     0xa37384: stur            w0, [x3, #0x27]
    // 0xa37388: StoreField: r3->field_2b = r0
    //     0xa37388: stur            w0, [x3, #0x2b]
    // 0xa3738c: r1 = Null
    //     0xa3738c: mov             x1, NULL
    // 0xa37390: r2 = 6
    //     0xa37390: movz            x2, #0x6
    // 0xa37394: r0 = AllocateArray()
    //     0xa37394: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa37398: mov             x2, x0
    // 0xa3739c: ldur            x0, [fp, #-0x28]
    // 0xa373a0: stur            x2, [fp, #-0x10]
    // 0xa373a4: StoreField: r2->field_f = r0
    //     0xa373a4: stur            w0, [x2, #0xf]
    // 0xa373a8: r16 = Instance_NSectionDivider
    //     0xa373a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!NSectionDivider@e20a81
    //     0xa373ac: ldr             x16, [x16, #0xe10]
    // 0xa373b0: StoreField: r2->field_13 = r16
    //     0xa373b0: stur            w16, [x2, #0x13]
    // 0xa373b4: ldur            x0, [fp, #-0x20]
    // 0xa373b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa373b8: stur            w0, [x2, #0x17]
    // 0xa373bc: r1 = <Widget>
    //     0xa373bc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa373c0: r0 = AllocateGrowableArray()
    //     0xa373c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa373c4: mov             x1, x0
    // 0xa373c8: ldur            x0, [fp, #-0x10]
    // 0xa373cc: stur            x1, [fp, #-0x20]
    // 0xa373d0: StoreField: r1->field_f = r0
    //     0xa373d0: stur            w0, [x1, #0xf]
    // 0xa373d4: r0 = 6
    //     0xa373d4: movz            x0, #0x6
    // 0xa373d8: StoreField: r1->field_b = r0
    //     0xa373d8: stur            w0, [x1, #0xb]
    // 0xa373dc: r0 = ListView()
    //     0xa373dc: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xa373e0: stur            x0, [fp, #-0x10]
    // 0xa373e4: r16 = Instance_ClampingScrollPhysics
    //     0xa373e4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xa373e8: ldr             x16, [x16, #0x410]
    // 0xa373ec: str             x16, [SP]
    // 0xa373f0: mov             x1, x0
    // 0xa373f4: ldur            x2, [fp, #-0x20]
    // 0xa373f8: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xa373f8: add             x4, PP, #0x28, lsl #12  ; [pp+0x28418] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xa373fc: ldr             x4, [x4, #0x418]
    // 0xa37400: r0 = ListView()
    //     0xa37400: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xa37404: ldur            x2, [fp, #-8]
    // 0xa37408: r1 = Function 'onApplyPressed':.
    //     0xa37408: add             x1, PP, #0x40, lsl #12  ; [pp+0x400e0] AnonymousClosure: (0xa37fcc), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::onApplyPressed (0xa38004)
    //     0xa3740c: ldr             x1, [x1, #0xe0]
    // 0xa37410: r0 = AllocateClosure()
    //     0xa37410: bl              #0xec1630  ; AllocateClosureStub
    // 0xa37414: stur            x0, [fp, #-8]
    // 0xa37418: r0 = TextButton()
    //     0xa37418: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa3741c: mov             x1, x0
    // 0xa37420: ldur            x0, [fp, #-8]
    // 0xa37424: stur            x1, [fp, #-0x20]
    // 0xa37428: StoreField: r1->field_b = r0
    //     0xa37428: stur            w0, [x1, #0xb]
    // 0xa3742c: r0 = false
    //     0xa3742c: add             x0, NULL, #0x30  ; false
    // 0xa37430: StoreField: r1->field_27 = r0
    //     0xa37430: stur            w0, [x1, #0x27]
    // 0xa37434: r2 = true
    //     0xa37434: add             x2, NULL, #0x20  ; true
    // 0xa37438: StoreField: r1->field_2f = r2
    //     0xa37438: stur            w2, [x1, #0x2f]
    // 0xa3743c: r3 = Instance_Text
    //     0xa3743c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cf30] Obj!Text@e215a1
    //     0xa37440: ldr             x3, [x3, #0xf30]
    // 0xa37444: StoreField: r1->field_37 = r3
    //     0xa37444: stur            w3, [x1, #0x37]
    // 0xa37448: r0 = Container()
    //     0xa37448: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa3744c: stur            x0, [fp, #-8]
    // 0xa37450: r16 = Instance_EdgeInsets
    //     0xa37450: add             x16, PP, #0x28, lsl #12  ; [pp+0x28050] Obj!EdgeInsets@e12341
    //     0xa37454: ldr             x16, [x16, #0x50]
    // 0xa37458: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xa37458: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xa3745c: ldr             lr, [lr, #0xc58]
    // 0xa37460: stp             lr, x16, [SP, #8]
    // 0xa37464: ldur            x16, [fp, #-0x20]
    // 0xa37468: str             x16, [SP]
    // 0xa3746c: mov             x1, x0
    // 0xa37470: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0xa37470: add             x4, PP, #0x28, lsl #12  ; [pp+0x28058] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xa37474: ldr             x4, [x4, #0x58]
    // 0xa37478: r0 = Container()
    //     0xa37478: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa3747c: r1 = Null
    //     0xa3747c: mov             x1, NULL
    // 0xa37480: r2 = 2
    //     0xa37480: movz            x2, #0x2
    // 0xa37484: r0 = AllocateArray()
    //     0xa37484: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa37488: mov             x2, x0
    // 0xa3748c: ldur            x0, [fp, #-8]
    // 0xa37490: stur            x2, [fp, #-0x20]
    // 0xa37494: StoreField: r2->field_f = r0
    //     0xa37494: stur            w0, [x2, #0xf]
    // 0xa37498: r1 = <Widget>
    //     0xa37498: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3749c: r0 = AllocateGrowableArray()
    //     0xa3749c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa374a0: mov             x1, x0
    // 0xa374a4: ldur            x0, [fp, #-0x20]
    // 0xa374a8: stur            x1, [fp, #-8]
    // 0xa374ac: StoreField: r1->field_f = r0
    //     0xa374ac: stur            w0, [x1, #0xf]
    // 0xa374b0: r0 = 2
    //     0xa374b0: movz            x0, #0x2
    // 0xa374b4: StoreField: r1->field_b = r0
    //     0xa374b4: stur            w0, [x1, #0xb]
    // 0xa374b8: r0 = Scaffold()
    //     0xa374b8: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xa374bc: ldur            x1, [fp, #-0x18]
    // 0xa374c0: StoreField: r0->field_13 = r1
    //     0xa374c0: stur            w1, [x0, #0x13]
    // 0xa374c4: ldur            x1, [fp, #-0x10]
    // 0xa374c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xa374c8: stur            w1, [x0, #0x17]
    // 0xa374cc: ldur            x1, [fp, #-8]
    // 0xa374d0: StoreField: r0->field_27 = r1
    //     0xa374d0: stur            w1, [x0, #0x27]
    // 0xa374d4: r1 = Instance_AlignmentDirectional
    //     0xa374d4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xa374d8: ldr             x1, [x1, #0x758]
    // 0xa374dc: StoreField: r0->field_2b = r1
    //     0xa374dc: stur            w1, [x0, #0x2b]
    // 0xa374e0: r1 = true
    //     0xa374e0: add             x1, NULL, #0x20  ; true
    // 0xa374e4: StoreField: r0->field_53 = r1
    //     0xa374e4: stur            w1, [x0, #0x53]
    // 0xa374e8: r2 = Instance_DragStartBehavior
    //     0xa374e8: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa374ec: StoreField: r0->field_57 = r2
    //     0xa374ec: stur            w2, [x0, #0x57]
    // 0xa374f0: r2 = false
    //     0xa374f0: add             x2, NULL, #0x30  ; false
    // 0xa374f4: StoreField: r0->field_b = r2
    //     0xa374f4: stur            w2, [x0, #0xb]
    // 0xa374f8: StoreField: r0->field_f = r2
    //     0xa374f8: stur            w2, [x0, #0xf]
    // 0xa374fc: StoreField: r0->field_5f = r1
    //     0xa374fc: stur            w1, [x0, #0x5f]
    // 0xa37500: StoreField: r0->field_63 = r1
    //     0xa37500: stur            w1, [x0, #0x63]
    // 0xa37504: LeaveFrame
    //     0xa37504: mov             SP, fp
    //     0xa37508: ldp             fp, lr, [SP], #0x10
    // 0xa3750c: ret
    //     0xa3750c: ret             
    // 0xa37510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa37510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa37514: b               #0xa37260
  }
  [closure] void onApplyPressed(dynamic) {
    // ** addr: 0xa37fcc, size: 0x38
    // 0xa37fcc: EnterFrame
    //     0xa37fcc: stp             fp, lr, [SP, #-0x10]!
    //     0xa37fd0: mov             fp, SP
    // 0xa37fd4: ldr             x0, [fp, #0x10]
    // 0xa37fd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa37fd8: ldur            w1, [x0, #0x17]
    // 0xa37fdc: DecompressPointer r1
    //     0xa37fdc: add             x1, x1, HEAP, lsl #32
    // 0xa37fe0: CheckStackOverflow
    //     0xa37fe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37fe4: cmp             SP, x16
    //     0xa37fe8: b.ls            #0xa37ffc
    // 0xa37fec: r0 = onApplyPressed()
    //     0xa37fec: bl              #0xa38004  ; [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::onApplyPressed
    // 0xa37ff0: LeaveFrame
    //     0xa37ff0: mov             SP, fp
    //     0xa37ff4: ldp             fp, lr, [SP], #0x10
    // 0xa37ff8: ret
    //     0xa37ff8: ret             
    // 0xa37ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa37ffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38000: b               #0xa37fec
  }
  _ onApplyPressed(/* No info */) {
    // ** addr: 0xa38004, size: 0xac
    // 0xa38004: EnterFrame
    //     0xa38004: stp             fp, lr, [SP, #-0x10]!
    //     0xa38008: mov             fp, SP
    // 0xa3800c: AllocStack(0x10)
    //     0xa3800c: sub             SP, SP, #0x10
    // 0xa38010: CheckStackOverflow
    //     0xa38010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38014: cmp             SP, x16
    //     0xa38018: b.ls            #0xa380a4
    // 0xa3801c: LoadField: r0 = r1->field_b
    //     0xa3801c: ldur            w0, [x1, #0xb]
    // 0xa38020: DecompressPointer r0
    //     0xa38020: add             x0, x0, HEAP, lsl #32
    // 0xa38024: cmp             w0, NULL
    // 0xa38028: b.eq            #0xa380ac
    // 0xa3802c: LoadField: r2 = r0->field_b
    //     0xa3802c: ldur            w2, [x0, #0xb]
    // 0xa38030: DecompressPointer r2
    //     0xa38030: add             x2, x2, HEAP, lsl #32
    // 0xa38034: LoadField: r0 = r2->field_33
    //     0xa38034: ldur            w0, [x2, #0x33]
    // 0xa38038: DecompressPointer r0
    //     0xa38038: add             x0, x0, HEAP, lsl #32
    // 0xa3803c: stur            x0, [fp, #-8]
    // 0xa38040: LoadField: r0 = r1->field_1b
    //     0xa38040: ldur            w0, [x1, #0x1b]
    // 0xa38044: DecompressPointer r0
    //     0xa38044: add             x0, x0, HEAP, lsl #32
    // 0xa38048: r16 = Sentinel
    //     0xa38048: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3804c: cmp             w0, w16
    // 0xa38050: b.ne            #0xa38060
    // 0xa38054: r2 = selected
    //     0xa38054: add             x2, PP, #0x40, lsl #12  ; [pp+0x400e8] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xa38058: ldr             x2, [x2, #0xe8]
    // 0xa3805c: r0 = InitLateInstanceField()
    //     0xa3805c: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xa38060: ldur            x1, [fp, #-8]
    // 0xa38064: mov             x2, x0
    // 0xa38068: r0 = value=()
    //     0xa38068: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa3806c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa3806c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa38070: ldr             x0, [x0, #0x2670]
    //     0xa38074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa38078: cmp             w0, w16
    //     0xa3807c: b.ne            #0xa38088
    //     0xa38080: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa38084: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa38088: str             NULL, [SP]
    // 0xa3808c: r4 = const [0x1, 0, 0, 0, null]
    //     0xa3808c: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xa38090: r0 = GetNavigation.back()
    //     0xa38090: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa38094: r0 = Null
    //     0xa38094: mov             x0, NULL
    // 0xa38098: LeaveFrame
    //     0xa38098: mov             SP, fp
    //     0xa3809c: ldp             fp, lr, [SP], #0x10
    // 0xa380a0: ret
    //     0xa380a0: ret             
    // 0xa380a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa380a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa380a8: b               #0xa3801c
    // 0xa380ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa380ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  PaymentMethod selected(_PaymentMethodViewState) {
    // ** addr: 0xa380b0, size: 0x54
    // 0xa380b0: EnterFrame
    //     0xa380b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa380b4: mov             fp, SP
    // 0xa380b8: CheckStackOverflow
    //     0xa380b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa380bc: cmp             SP, x16
    //     0xa380c0: b.ls            #0xa380f8
    // 0xa380c4: ldr             x0, [fp, #0x10]
    // 0xa380c8: LoadField: r1 = r0->field_b
    //     0xa380c8: ldur            w1, [x0, #0xb]
    // 0xa380cc: DecompressPointer r1
    //     0xa380cc: add             x1, x1, HEAP, lsl #32
    // 0xa380d0: cmp             w1, NULL
    // 0xa380d4: b.eq            #0xa38100
    // 0xa380d8: LoadField: r0 = r1->field_b
    //     0xa380d8: ldur            w0, [x1, #0xb]
    // 0xa380dc: DecompressPointer r0
    //     0xa380dc: add             x0, x0, HEAP, lsl #32
    // 0xa380e0: LoadField: r1 = r0->field_33
    //     0xa380e0: ldur            w1, [x0, #0x33]
    // 0xa380e4: DecompressPointer r1
    //     0xa380e4: add             x1, x1, HEAP, lsl #32
    // 0xa380e8: r0 = value()
    //     0xa380e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa380ec: LeaveFrame
    //     0xa380ec: mov             SP, fp
    //     0xa380f0: ldp             fp, lr, [SP], #0x10
    // 0xa380f4: ret
    //     0xa380f4: ret             
    // 0xa380f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa380f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa380fc: b               #0xa380c4
    // 0xa38100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38100: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] _PaymentMethodItem <anonymous closure>(dynamic, PaymentMethod) {
    // ** addr: 0xa38104, size: 0xc8
    // 0xa38104: EnterFrame
    //     0xa38104: stp             fp, lr, [SP, #-0x10]!
    //     0xa38108: mov             fp, SP
    // 0xa3810c: AllocStack(0x18)
    //     0xa3810c: sub             SP, SP, #0x18
    // 0xa38110: SetupParameters()
    //     0xa38110: ldr             x0, [fp, #0x18]
    //     0xa38114: ldur            w1, [x0, #0x17]
    //     0xa38118: add             x1, x1, HEAP, lsl #32
    //     0xa3811c: stur            x1, [fp, #-8]
    // 0xa38120: CheckStackOverflow
    //     0xa38120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38124: cmp             SP, x16
    //     0xa38128: b.ls            #0xa381c4
    // 0xa3812c: r1 = 1
    //     0xa3812c: movz            x1, #0x1
    // 0xa38130: r0 = AllocateContext()
    //     0xa38130: bl              #0xec126c  ; AllocateContextStub
    // 0xa38134: mov             x2, x0
    // 0xa38138: ldur            x0, [fp, #-8]
    // 0xa3813c: stur            x2, [fp, #-0x10]
    // 0xa38140: StoreField: r2->field_b = r0
    //     0xa38140: stur            w0, [x2, #0xb]
    // 0xa38144: ldr             x3, [fp, #0x10]
    // 0xa38148: StoreField: r2->field_f = r3
    //     0xa38148: stur            w3, [x2, #0xf]
    // 0xa3814c: LoadField: r1 = r0->field_f
    //     0xa3814c: ldur            w1, [x0, #0xf]
    // 0xa38150: DecompressPointer r1
    //     0xa38150: add             x1, x1, HEAP, lsl #32
    // 0xa38154: LoadField: r0 = r1->field_1b
    //     0xa38154: ldur            w0, [x1, #0x1b]
    // 0xa38158: DecompressPointer r0
    //     0xa38158: add             x0, x0, HEAP, lsl #32
    // 0xa3815c: r16 = Sentinel
    //     0xa3815c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa38160: cmp             w0, w16
    // 0xa38164: b.ne            #0xa38174
    // 0xa38168: r2 = selected
    //     0xa38168: add             x2, PP, #0x40, lsl #12  ; [pp+0x400e8] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xa3816c: ldr             x2, [x2, #0xe8]
    // 0xa38170: r0 = InitLateInstanceField()
    //     0xa38170: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xa38174: stur            x0, [fp, #-8]
    // 0xa38178: r0 = _PaymentMethodItem()
    //     0xa38178: bl              #0xa381cc  ; Allocate_PaymentMethodItemStub -> _PaymentMethodItem (size=0x1c)
    // 0xa3817c: mov             x3, x0
    // 0xa38180: ldur            x0, [fp, #-8]
    // 0xa38184: stur            x3, [fp, #-0x18]
    // 0xa38188: StoreField: r3->field_b = r0
    //     0xa38188: stur            w0, [x3, #0xb]
    // 0xa3818c: ldr             x0, [fp, #0x10]
    // 0xa38190: StoreField: r3->field_f = r0
    //     0xa38190: stur            w0, [x3, #0xf]
    // 0xa38194: r0 = false
    //     0xa38194: add             x0, NULL, #0x30  ; false
    // 0xa38198: StoreField: r3->field_13 = r0
    //     0xa38198: stur            w0, [x3, #0x13]
    // 0xa3819c: ldur            x2, [fp, #-0x10]
    // 0xa381a0: r1 = Function '<anonymous closure>':.
    //     0xa381a0: add             x1, PP, #0x40, lsl #12  ; [pp+0x400f0] AnonymousClosure: (0xa381d8), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::build (0xa37240)
    //     0xa381a4: ldr             x1, [x1, #0xf0]
    // 0xa381a8: r0 = AllocateClosure()
    //     0xa381a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa381ac: mov             x1, x0
    // 0xa381b0: ldur            x0, [fp, #-0x18]
    // 0xa381b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa381b4: stur            w1, [x0, #0x17]
    // 0xa381b8: LeaveFrame
    //     0xa381b8: mov             SP, fp
    //     0xa381bc: ldp             fp, lr, [SP], #0x10
    // 0xa381c0: ret
    //     0xa381c0: ret             
    // 0xa381c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa381c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa381c8: b               #0xa3812c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa381d8, size: 0x5c
    // 0xa381d8: EnterFrame
    //     0xa381d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa381dc: mov             fp, SP
    // 0xa381e0: ldr             x0, [fp, #0x10]
    // 0xa381e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa381e4: ldur            w1, [x0, #0x17]
    // 0xa381e8: DecompressPointer r1
    //     0xa381e8: add             x1, x1, HEAP, lsl #32
    // 0xa381ec: CheckStackOverflow
    //     0xa381ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa381f0: cmp             SP, x16
    //     0xa381f4: b.ls            #0xa3822c
    // 0xa381f8: LoadField: r0 = r1->field_b
    //     0xa381f8: ldur            w0, [x1, #0xb]
    // 0xa381fc: DecompressPointer r0
    //     0xa381fc: add             x0, x0, HEAP, lsl #32
    // 0xa38200: LoadField: r2 = r0->field_f
    //     0xa38200: ldur            w2, [x0, #0xf]
    // 0xa38204: DecompressPointer r2
    //     0xa38204: add             x2, x2, HEAP, lsl #32
    // 0xa38208: LoadField: r0 = r1->field_f
    //     0xa38208: ldur            w0, [x1, #0xf]
    // 0xa3820c: DecompressPointer r0
    //     0xa3820c: add             x0, x0, HEAP, lsl #32
    // 0xa38210: mov             x1, x2
    // 0xa38214: mov             x2, x0
    // 0xa38218: r0 = onItemPressed()
    //     0xa38218: bl              #0xa38234  ; [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::onItemPressed
    // 0xa3821c: r0 = Null
    //     0xa3821c: mov             x0, NULL
    // 0xa38220: LeaveFrame
    //     0xa38220: mov             SP, fp
    //     0xa38224: ldp             fp, lr, [SP], #0x10
    // 0xa38228: ret
    //     0xa38228: ret             
    // 0xa3822c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3822c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38230: b               #0xa381f8
  }
  _ onItemPressed(/* No info */) {
    // ** addr: 0xa38234, size: 0x70
    // 0xa38234: EnterFrame
    //     0xa38234: stp             fp, lr, [SP, #-0x10]!
    //     0xa38238: mov             fp, SP
    // 0xa3823c: AllocStack(0x10)
    //     0xa3823c: sub             SP, SP, #0x10
    // 0xa38240: SetupParameters(_PaymentMethodViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa38240: stur            x1, [fp, #-8]
    //     0xa38244: stur            x2, [fp, #-0x10]
    // 0xa38248: CheckStackOverflow
    //     0xa38248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3824c: cmp             SP, x16
    //     0xa38250: b.ls            #0xa3829c
    // 0xa38254: r1 = 2
    //     0xa38254: movz            x1, #0x2
    // 0xa38258: r0 = AllocateContext()
    //     0xa38258: bl              #0xec126c  ; AllocateContextStub
    // 0xa3825c: mov             x1, x0
    // 0xa38260: ldur            x0, [fp, #-8]
    // 0xa38264: StoreField: r1->field_f = r0
    //     0xa38264: stur            w0, [x1, #0xf]
    // 0xa38268: ldur            x2, [fp, #-0x10]
    // 0xa3826c: StoreField: r1->field_13 = r2
    //     0xa3826c: stur            w2, [x1, #0x13]
    // 0xa38270: mov             x2, x1
    // 0xa38274: r1 = Function '<anonymous closure>':.
    //     0xa38274: add             x1, PP, #0x40, lsl #12  ; [pp+0x400f8] AnonymousClosure: (0xa382a4), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::onItemPressed (0xa38234)
    //     0xa38278: ldr             x1, [x1, #0xf8]
    // 0xa3827c: r0 = AllocateClosure()
    //     0xa3827c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa38280: ldur            x1, [fp, #-8]
    // 0xa38284: mov             x2, x0
    // 0xa38288: r0 = setState()
    //     0xa38288: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa3828c: r0 = Null
    //     0xa3828c: mov             x0, NULL
    // 0xa38290: LeaveFrame
    //     0xa38290: mov             SP, fp
    //     0xa38294: ldp             fp, lr, [SP], #0x10
    // 0xa38298: ret
    //     0xa38298: ret             
    // 0xa3829c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3829c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa382a0: b               #0xa38254
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa382a4, size: 0x4c
    // 0xa382a4: ldr             x1, [SP]
    // 0xa382a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa382a8: ldur            w2, [x1, #0x17]
    // 0xa382ac: DecompressPointer r2
    //     0xa382ac: add             x2, x2, HEAP, lsl #32
    // 0xa382b0: LoadField: r1 = r2->field_f
    //     0xa382b0: ldur            w1, [x2, #0xf]
    // 0xa382b4: DecompressPointer r1
    //     0xa382b4: add             x1, x1, HEAP, lsl #32
    // 0xa382b8: LoadField: r3 = r2->field_13
    //     0xa382b8: ldur            w3, [x2, #0x13]
    // 0xa382bc: DecompressPointer r3
    //     0xa382bc: add             x3, x3, HEAP, lsl #32
    // 0xa382c0: mov             x0, x3
    // 0xa382c4: StoreField: r1->field_1b = r0
    //     0xa382c4: stur            w0, [x1, #0x1b]
    //     0xa382c8: ldurb           w16, [x1, #-1]
    //     0xa382cc: ldurb           w17, [x0, #-1]
    //     0xa382d0: and             x16, x17, x16, lsr #2
    //     0xa382d4: tst             x16, HEAP, lsr #32
    //     0xa382d8: b.eq            #0xa382e8
    //     0xa382dc: str             lr, [SP, #-8]!
    //     0xa382e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xa382e4: ldr             lr, [SP], #8
    // 0xa382e8: mov             x0, x3
    // 0xa382ec: ret
    //     0xa382ec: ret             
  }
  [closure] _PaymentMethodItem <anonymous closure>(dynamic, PaymentMethod) {
    // ** addr: 0xa382f0, size: 0x108
    // 0xa382f0: EnterFrame
    //     0xa382f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa382f4: mov             fp, SP
    // 0xa382f8: AllocStack(0x20)
    //     0xa382f8: sub             SP, SP, #0x20
    // 0xa382fc: SetupParameters()
    //     0xa382fc: ldr             x0, [fp, #0x18]
    //     0xa38300: ldur            w1, [x0, #0x17]
    //     0xa38304: add             x1, x1, HEAP, lsl #32
    //     0xa38308: stur            x1, [fp, #-8]
    // 0xa3830c: CheckStackOverflow
    //     0xa3830c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38310: cmp             SP, x16
    //     0xa38314: b.ls            #0xa383ec
    // 0xa38318: r1 = 1
    //     0xa38318: movz            x1, #0x1
    // 0xa3831c: r0 = AllocateContext()
    //     0xa3831c: bl              #0xec126c  ; AllocateContextStub
    // 0xa38320: mov             x2, x0
    // 0xa38324: ldur            x0, [fp, #-8]
    // 0xa38328: stur            x2, [fp, #-0x10]
    // 0xa3832c: StoreField: r2->field_b = r0
    //     0xa3832c: stur            w0, [x2, #0xb]
    // 0xa38330: ldr             x3, [fp, #0x10]
    // 0xa38334: StoreField: r2->field_f = r3
    //     0xa38334: stur            w3, [x2, #0xf]
    // 0xa38338: LoadField: r1 = r0->field_f
    //     0xa38338: ldur            w1, [x0, #0xf]
    // 0xa3833c: DecompressPointer r1
    //     0xa3833c: add             x1, x1, HEAP, lsl #32
    // 0xa38340: LoadField: r0 = r1->field_1b
    //     0xa38340: ldur            w0, [x1, #0x1b]
    // 0xa38344: DecompressPointer r0
    //     0xa38344: add             x0, x0, HEAP, lsl #32
    // 0xa38348: r16 = Sentinel
    //     0xa38348: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3834c: cmp             w0, w16
    // 0xa38350: b.ne            #0xa38360
    // 0xa38354: r2 = selected
    //     0xa38354: add             x2, PP, #0x40, lsl #12  ; [pp+0x400e8] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xa38358: ldr             x2, [x2, #0xe8]
    // 0xa3835c: r0 = InitLateInstanceField()
    //     0xa3835c: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xa38360: mov             x2, x0
    // 0xa38364: ldur            x0, [fp, #-8]
    // 0xa38368: stur            x2, [fp, #-0x18]
    // 0xa3836c: LoadField: r1 = r0->field_f
    //     0xa3836c: ldur            w1, [x0, #0xf]
    // 0xa38370: DecompressPointer r1
    //     0xa38370: add             x1, x1, HEAP, lsl #32
    // 0xa38374: LoadField: r0 = r1->field_b
    //     0xa38374: ldur            w0, [x1, #0xb]
    // 0xa38378: DecompressPointer r0
    //     0xa38378: add             x0, x0, HEAP, lsl #32
    // 0xa3837c: cmp             w0, NULL
    // 0xa38380: b.eq            #0xa383f4
    // 0xa38384: LoadField: r1 = r0->field_b
    //     0xa38384: ldur            w1, [x0, #0xb]
    // 0xa38388: DecompressPointer r1
    //     0xa38388: add             x1, x1, HEAP, lsl #32
    // 0xa3838c: LoadField: r0 = r1->field_3b
    //     0xa3838c: ldur            w0, [x1, #0x3b]
    // 0xa38390: DecompressPointer r0
    //     0xa38390: add             x0, x0, HEAP, lsl #32
    // 0xa38394: mov             x1, x0
    // 0xa38398: r0 = value()
    //     0xa38398: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa3839c: stur            x0, [fp, #-8]
    // 0xa383a0: r0 = _PaymentMethodItem()
    //     0xa383a0: bl              #0xa381cc  ; Allocate_PaymentMethodItemStub -> _PaymentMethodItem (size=0x1c)
    // 0xa383a4: mov             x3, x0
    // 0xa383a8: ldur            x0, [fp, #-0x18]
    // 0xa383ac: stur            x3, [fp, #-0x20]
    // 0xa383b0: StoreField: r3->field_b = r0
    //     0xa383b0: stur            w0, [x3, #0xb]
    // 0xa383b4: ldr             x0, [fp, #0x10]
    // 0xa383b8: StoreField: r3->field_f = r0
    //     0xa383b8: stur            w0, [x3, #0xf]
    // 0xa383bc: ldur            x0, [fp, #-8]
    // 0xa383c0: StoreField: r3->field_13 = r0
    //     0xa383c0: stur            w0, [x3, #0x13]
    // 0xa383c4: ldur            x2, [fp, #-0x10]
    // 0xa383c8: r1 = Function '<anonymous closure>':.
    //     0xa383c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40100] AnonymousClosure: (0xa381d8), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::build (0xa37240)
    //     0xa383cc: ldr             x1, [x1, #0x100]
    // 0xa383d0: r0 = AllocateClosure()
    //     0xa383d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa383d4: mov             x1, x0
    // 0xa383d8: ldur            x0, [fp, #-0x20]
    // 0xa383dc: ArrayStore: r0[0] = r1  ; List_4
    //     0xa383dc: stur            w1, [x0, #0x17]
    // 0xa383e0: LeaveFrame
    //     0xa383e0: mov             SP, fp
    //     0xa383e4: ldp             fp, lr, [SP], #0x10
    // 0xa383e8: ret
    //     0xa383e8: ret             
    // 0xa383ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa383ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa383f0: b               #0xa38318
    // 0xa383f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa383f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _PaymentMethodViewState(/* No info */) {
    // ** addr: 0xa945b4, size: 0xe0
    // 0xa945b4: EnterFrame
    //     0xa945b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa945b8: mov             fp, SP
    // 0xa945bc: AllocStack(0x8)
    //     0xa945bc: sub             SP, SP, #8
    // 0xa945c0: r0 = Sentinel
    //     0xa945c0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa945c4: mov             x3, x1
    // 0xa945c8: stur            x1, [fp, #-8]
    // 0xa945cc: CheckStackOverflow
    //     0xa945cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa945d0: cmp             SP, x16
    //     0xa945d4: b.ls            #0xa9468c
    // 0xa945d8: StoreField: r3->field_1b = r0
    //     0xa945d8: stur            w0, [x3, #0x1b]
    // 0xa945dc: r1 = Function '<anonymous closure>':.
    //     0xa945dc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d70] AnonymousClosure: (0xa946bc), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::_PaymentMethodViewState (0xa945b4)
    //     0xa945e0: ldr             x1, [x1, #0xd70]
    // 0xa945e4: r2 = Null
    //     0xa945e4: mov             x2, NULL
    // 0xa945e8: r0 = AllocateClosure()
    //     0xa945e8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa945ec: mov             x2, x0
    // 0xa945f0: r1 = const [Instance of 'PaymentMethod', Instance of 'PaymentMethod', Instance of 'PaymentMethod', Instance of 'PaymentMethod']
    //     0xa945f0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d78] List<PaymentMethod>(4)
    //     0xa945f4: ldr             x1, [x1, #0xd78]
    // 0xa945f8: r0 = where()
    //     0xa945f8: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa945fc: LoadField: r1 = r0->field_7
    //     0xa945fc: ldur            w1, [x0, #7]
    // 0xa94600: DecompressPointer r1
    //     0xa94600: add             x1, x1, HEAP, lsl #32
    // 0xa94604: mov             x2, x0
    // 0xa94608: r0 = _GrowableList.of()
    //     0xa94608: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa9460c: ldur            x3, [fp, #-8]
    // 0xa94610: StoreField: r3->field_13 = r0
    //     0xa94610: stur            w0, [x3, #0x13]
    //     0xa94614: ldurb           w16, [x3, #-1]
    //     0xa94618: ldurb           w17, [x0, #-1]
    //     0xa9461c: and             x16, x17, x16, lsr #2
    //     0xa94620: tst             x16, HEAP, lsr #32
    //     0xa94624: b.eq            #0xa9462c
    //     0xa94628: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa9462c: r1 = Function '<anonymous closure>':.
    //     0xa9462c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d80] AnonymousClosure: (0xa94694), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::_PaymentMethodViewState (0xa945b4)
    //     0xa94630: ldr             x1, [x1, #0xd80]
    // 0xa94634: r2 = Null
    //     0xa94634: mov             x2, NULL
    // 0xa94638: r0 = AllocateClosure()
    //     0xa94638: bl              #0xec1630  ; AllocateClosureStub
    // 0xa9463c: mov             x2, x0
    // 0xa94640: r1 = const [Instance of 'PaymentMethod', Instance of 'PaymentMethod', Instance of 'PaymentMethod', Instance of 'PaymentMethod']
    //     0xa94640: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d78] List<PaymentMethod>(4)
    //     0xa94644: ldr             x1, [x1, #0xd78]
    // 0xa94648: r0 = where()
    //     0xa94648: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa9464c: LoadField: r1 = r0->field_7
    //     0xa9464c: ldur            w1, [x0, #7]
    // 0xa94650: DecompressPointer r1
    //     0xa94650: add             x1, x1, HEAP, lsl #32
    // 0xa94654: mov             x2, x0
    // 0xa94658: r0 = _GrowableList.of()
    //     0xa94658: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa9465c: ldur            x1, [fp, #-8]
    // 0xa94660: ArrayStore: r1[0] = r0  ; List_4
    //     0xa94660: stur            w0, [x1, #0x17]
    //     0xa94664: ldurb           w16, [x1, #-1]
    //     0xa94668: ldurb           w17, [x0, #-1]
    //     0xa9466c: and             x16, x17, x16, lsr #2
    //     0xa94670: tst             x16, HEAP, lsr #32
    //     0xa94674: b.eq            #0xa9467c
    //     0xa94678: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa9467c: r0 = Null
    //     0xa9467c: mov             x0, NULL
    // 0xa94680: LeaveFrame
    //     0xa94680: mov             SP, fp
    //     0xa94684: ldp             fp, lr, [SP], #0x10
    // 0xa94688: ret
    //     0xa94688: ret             
    // 0xa9468c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9468c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94690: b               #0xa945d8
  }
  [closure] bool <anonymous closure>(dynamic, PaymentMethod) {
    // ** addr: 0xa94694, size: 0x28
    // 0xa94694: ldr             x1, [SP]
    // 0xa94698: LoadField: r2 = r1->field_13
    //     0xa94698: ldur            w2, [x1, #0x13]
    // 0xa9469c: DecompressPointer r2
    //     0xa9469c: add             x2, x2, HEAP, lsl #32
    // 0xa946a0: r16 = Instance_PaymentMethodGroup
    //     0xa946a0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34d88] Obj!PaymentMethodGroup@e30d41
    //     0xa946a4: ldr             x16, [x16, #0xd88]
    // 0xa946a8: cmp             w2, w16
    // 0xa946ac: r16 = true
    //     0xa946ac: add             x16, NULL, #0x20  ; true
    // 0xa946b0: r17 = false
    //     0xa946b0: add             x17, NULL, #0x30  ; false
    // 0xa946b4: csel            x0, x16, x17, eq
    // 0xa946b8: ret
    //     0xa946b8: ret             
  }
  [closure] bool <anonymous closure>(dynamic, PaymentMethod) {
    // ** addr: 0xa946bc, size: 0x28
    // 0xa946bc: ldr             x1, [SP]
    // 0xa946c0: LoadField: r2 = r1->field_13
    //     0xa946c0: ldur            w2, [x1, #0x13]
    // 0xa946c4: DecompressPointer r2
    //     0xa946c4: add             x2, x2, HEAP, lsl #32
    // 0xa946c8: r16 = Instance_PaymentMethodGroup
    //     0xa946c8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34d90] Obj!PaymentMethodGroup@e30d61
    //     0xa946cc: ldr             x16, [x16, #0xd90]
    // 0xa946d0: cmp             w2, w16
    // 0xa946d4: r16 = true
    //     0xa946d4: add             x16, NULL, #0x20  ; true
    // 0xa946d8: r17 = false
    //     0xa946d8: add             x17, NULL, #0x30  ; false
    // 0xa946dc: csel            x0, x16, x17, eq
    // 0xa946e0: ret
    //     0xa946e0: ret             
  }
}

// class id: 4714, size: 0x10, field offset: 0xc
//   const constructor, 
class PaymentMethodView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9456c, size: 0x48
    // 0xa9456c: EnterFrame
    //     0xa9456c: stp             fp, lr, [SP, #-0x10]!
    //     0xa94570: mov             fp, SP
    // 0xa94574: AllocStack(0x8)
    //     0xa94574: sub             SP, SP, #8
    // 0xa94578: CheckStackOverflow
    //     0xa94578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9457c: cmp             SP, x16
    //     0xa94580: b.ls            #0xa945ac
    // 0xa94584: r1 = <PaymentMethodView>
    //     0xa94584: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d68] TypeArguments: <PaymentMethodView>
    //     0xa94588: ldr             x1, [x1, #0xd68]
    // 0xa9458c: r0 = _PaymentMethodViewState()
    //     0xa9458c: bl              #0xa946e4  ; Allocate_PaymentMethodViewStateStub -> _PaymentMethodViewState (size=0x20)
    // 0xa94590: mov             x1, x0
    // 0xa94594: stur            x0, [fp, #-8]
    // 0xa94598: r0 = _PaymentMethodViewState()
    //     0xa94598: bl              #0xa945b4  ; [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::_PaymentMethodViewState
    // 0xa9459c: ldur            x0, [fp, #-8]
    // 0xa945a0: LeaveFrame
    //     0xa945a0: mov             SP, fp
    //     0xa945a4: ldp             fp, lr, [SP], #0x10
    // 0xa945a8: ret
    //     0xa945a8: ret             
    // 0xa945ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa945ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa945b0: b               #0xa94584
  }
}

// class id: 5040, size: 0x1c, field offset: 0xc
//   const constructor, 
class _PaymentMethodItem extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8f174, size: 0x150
    // 0xb8f174: EnterFrame
    //     0xb8f174: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f178: mov             fp, SP
    // 0xb8f17c: AllocStack(0x38)
    //     0xb8f17c: sub             SP, SP, #0x38
    // 0xb8f180: SetupParameters(_PaymentMethodItem this /* r1 => r1, fp-0x8 */)
    //     0xb8f180: stur            x1, [fp, #-8]
    // 0xb8f184: r1 = 1
    //     0xb8f184: movz            x1, #0x1
    // 0xb8f188: r0 = AllocateContext()
    //     0xb8f188: bl              #0xec126c  ; AllocateContextStub
    // 0xb8f18c: mov             x2, x0
    // 0xb8f190: ldur            x0, [fp, #-8]
    // 0xb8f194: stur            x2, [fp, #-0x38]
    // 0xb8f198: StoreField: r2->field_f = r0
    //     0xb8f198: stur            w0, [x2, #0xf]
    // 0xb8f19c: LoadField: r3 = r0->field_f
    //     0xb8f19c: ldur            w3, [x0, #0xf]
    // 0xb8f1a0: DecompressPointer r3
    //     0xb8f1a0: add             x3, x3, HEAP, lsl #32
    // 0xb8f1a4: stur            x3, [fp, #-0x30]
    // 0xb8f1a8: LoadField: r1 = r3->field_7
    //     0xb8f1a8: ldur            x1, [x3, #7]
    // 0xb8f1ac: cmp             x1, #1
    // 0xb8f1b0: b.gt            #0xb8f1d4
    // 0xb8f1b4: cmp             x1, #0
    // 0xb8f1b8: b.gt            #0xb8f1c8
    // 0xb8f1bc: r4 = "QRIS"
    //     0xb8f1bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xb8f1c0: ldr             x4, [x4, #0xf50]
    // 0xb8f1c4: b               #0xb8f1f0
    // 0xb8f1c8: r4 = "BNI Virtual Account"
    //     0xb8f1c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xb8f1cc: ldr             x4, [x4, #0xf58]
    // 0xb8f1d0: b               #0xb8f1f0
    // 0xb8f1d4: cmp             x1, #2
    // 0xb8f1d8: b.gt            #0xb8f1e8
    // 0xb8f1dc: r4 = "BSI Virtual Account"
    //     0xb8f1dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xb8f1e0: ldr             x4, [x4, #0xf60]
    // 0xb8f1e4: b               #0xb8f1f0
    // 0xb8f1e8: r4 = "BRI Virtual Account"
    //     0xb8f1e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xb8f1ec: ldr             x4, [x4, #0xf68]
    // 0xb8f1f0: stur            x4, [fp, #-0x28]
    // 0xb8f1f4: LoadField: r5 = r0->field_13
    //     0xb8f1f4: ldur            w5, [x0, #0x13]
    // 0xb8f1f8: DecompressPointer r5
    //     0xb8f1f8: add             x5, x5, HEAP, lsl #32
    // 0xb8f1fc: stur            x5, [fp, #-0x20]
    // 0xb8f200: cmp             x1, #1
    // 0xb8f204: b.gt            #0xb8f228
    // 0xb8f208: cmp             x1, #0
    // 0xb8f20c: b.gt            #0xb8f21c
    // 0xb8f210: r6 = "assets/images/qris.svg"
    //     0xb8f210: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2ff30] "assets/images/qris.svg"
    //     0xb8f214: ldr             x6, [x6, #0xf30]
    // 0xb8f218: b               #0xb8f244
    // 0xb8f21c: r6 = "assets/images/bni.svg"
    //     0xb8f21c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2ff38] "assets/images/bni.svg"
    //     0xb8f220: ldr             x6, [x6, #0xf38]
    // 0xb8f224: b               #0xb8f244
    // 0xb8f228: cmp             x1, #2
    // 0xb8f22c: b.gt            #0xb8f23c
    // 0xb8f230: r6 = "assets/images/bsi.svg"
    //     0xb8f230: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2ff40] "assets/images/bsi.svg"
    //     0xb8f234: ldr             x6, [x6, #0xf40]
    // 0xb8f238: b               #0xb8f244
    // 0xb8f23c: r6 = "assets/images/bri.svg"
    //     0xb8f23c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2ff48] "assets/images/bri.svg"
    //     0xb8f240: ldr             x6, [x6, #0xf48]
    // 0xb8f244: stur            x6, [fp, #-0x18]
    // 0xb8f248: LoadField: r7 = r0->field_b
    //     0xb8f248: ldur            w7, [x0, #0xb]
    // 0xb8f24c: DecompressPointer r7
    //     0xb8f24c: add             x7, x7, HEAP, lsl #32
    // 0xb8f250: stur            x7, [fp, #-0x10]
    // 0xb8f254: r1 = <PaymentMethod>
    //     0xb8f254: add             x1, PP, #0x36, lsl #12  ; [pp+0x36138] TypeArguments: <PaymentMethod>
    //     0xb8f258: ldr             x1, [x1, #0x138]
    // 0xb8f25c: r0 = ZakatRadioListTile()
    //     0xb8f25c: bl              #0xb68170  ; AllocateZakatRadioListTileStub -> ZakatRadioListTile<X0> (size=0x34)
    // 0xb8f260: mov             x3, x0
    // 0xb8f264: ldur            x0, [fp, #-0x28]
    // 0xb8f268: stur            x3, [fp, #-8]
    // 0xb8f26c: StoreField: r3->field_f = r0
    //     0xb8f26c: stur            w0, [x3, #0xf]
    // 0xb8f270: ldur            x0, [fp, #-0x30]
    // 0xb8f274: StoreField: r3->field_1b = r0
    //     0xb8f274: stur            w0, [x3, #0x1b]
    // 0xb8f278: ldur            x0, [fp, #-0x10]
    // 0xb8f27c: StoreField: r3->field_1f = r0
    //     0xb8f27c: stur            w0, [x3, #0x1f]
    // 0xb8f280: ldur            x2, [fp, #-0x38]
    // 0xb8f284: r1 = Function '<anonymous closure>':.
    //     0xb8f284: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ad8] AnonymousClosure: (0xb8f2c4), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodItem::build (0xb8f174)
    //     0xb8f288: ldr             x1, [x1, #0xad8]
    // 0xb8f28c: r0 = AllocateClosure()
    //     0xb8f28c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8f290: mov             x1, x0
    // 0xb8f294: ldur            x0, [fp, #-8]
    // 0xb8f298: StoreField: r0->field_23 = r1
    //     0xb8f298: stur            w1, [x0, #0x23]
    // 0xb8f29c: ldur            x1, [fp, #-0x18]
    // 0xb8f2a0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8f2a0: stur            w1, [x0, #0x17]
    // 0xb8f2a4: r1 = Instance_EdgeInsets
    //     0xb8f2a4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f00] Obj!EdgeInsets@e129a1
    //     0xb8f2a8: ldr             x1, [x1, #0xf00]
    // 0xb8f2ac: StoreField: r0->field_27 = r1
    //     0xb8f2ac: stur            w1, [x0, #0x27]
    // 0xb8f2b0: ldur            x1, [fp, #-0x20]
    // 0xb8f2b4: StoreField: r0->field_2b = r1
    //     0xb8f2b4: stur            w1, [x0, #0x2b]
    // 0xb8f2b8: LeaveFrame
    //     0xb8f2b8: mov             SP, fp
    //     0xb8f2bc: ldp             fp, lr, [SP], #0x10
    // 0xb8f2c0: ret
    //     0xb8f2c0: ret             
  }
  [closure] void <anonymous closure>(dynamic, PaymentMethod?) {
    // ** addr: 0xb8f2c4, size: 0x68
    // 0xb8f2c4: EnterFrame
    //     0xb8f2c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f2c8: mov             fp, SP
    // 0xb8f2cc: AllocStack(0x8)
    //     0xb8f2cc: sub             SP, SP, #8
    // 0xb8f2d0: SetupParameters()
    //     0xb8f2d0: ldr             x0, [fp, #0x18]
    //     0xb8f2d4: ldur            w1, [x0, #0x17]
    //     0xb8f2d8: add             x1, x1, HEAP, lsl #32
    // 0xb8f2dc: CheckStackOverflow
    //     0xb8f2dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f2e0: cmp             SP, x16
    //     0xb8f2e4: b.ls            #0xb8f320
    // 0xb8f2e8: LoadField: r0 = r1->field_f
    //     0xb8f2e8: ldur            w0, [x1, #0xf]
    // 0xb8f2ec: DecompressPointer r0
    //     0xb8f2ec: add             x0, x0, HEAP, lsl #32
    // 0xb8f2f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8f2f0: ldur            w1, [x0, #0x17]
    // 0xb8f2f4: DecompressPointer r1
    //     0xb8f2f4: add             x1, x1, HEAP, lsl #32
    // 0xb8f2f8: cmp             w1, NULL
    // 0xb8f2fc: b.eq            #0xb8f328
    // 0xb8f300: str             x1, [SP]
    // 0xb8f304: mov             x0, x1
    // 0xb8f308: ClosureCall
    //     0xb8f308: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xb8f30c: ldur            x2, [x0, #0x1f]
    //     0xb8f310: blr             x2
    // 0xb8f314: LeaveFrame
    //     0xb8f314: mov             SP, fp
    //     0xb8f318: ldp             fp, lr, [SP], #0x10
    // 0xb8f31c: ret
    //     0xb8f31c: ret             
    // 0xb8f320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f324: b               #0xb8f2e8
    // 0xb8f328: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb8f328: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}
