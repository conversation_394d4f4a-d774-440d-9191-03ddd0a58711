// lib: , url: package:nuonline/app/modules/donation/views/transaction_history_view.dart

// class id: 1050228, size: 0x8
class :: {
}

// class id: 5289, size: 0x14, field offset: 0x14
//   const constructor, 
class TransactionHistoryView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae6434, size: 0x2a8
    // 0xae6434: EnterFrame
    //     0xae6434: stp             fp, lr, [SP, #-0x10]!
    //     0xae6438: mov             fp, SP
    // 0xae643c: AllocStack(0x40)
    //     0xae643c: sub             SP, SP, #0x40
    // 0xae6440: SetupParameters(TransactionHistoryView this /* r1 => r1, fp-0x8 */)
    //     0xae6440: stur            x1, [fp, #-8]
    // 0xae6444: CheckStackOverflow
    //     0xae6444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6448: cmp             SP, x16
    //     0xae644c: b.ls            #0xae66d4
    // 0xae6450: r1 = 1
    //     0xae6450: movz            x1, #0x1
    // 0xae6454: r0 = AllocateContext()
    //     0xae6454: bl              #0xec126c  ; AllocateContextStub
    // 0xae6458: mov             x3, x0
    // 0xae645c: ldur            x0, [fp, #-8]
    // 0xae6460: stur            x3, [fp, #-0x10]
    // 0xae6464: StoreField: r3->field_f = r0
    //     0xae6464: stur            w0, [x3, #0xf]
    // 0xae6468: r1 = Null
    //     0xae6468: mov             x1, NULL
    // 0xae646c: r2 = 4
    //     0xae646c: movz            x2, #0x4
    // 0xae6470: r0 = AllocateArray()
    //     0xae6470: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae6474: stur            x0, [fp, #-0x18]
    // 0xae6478: r16 = "Riwayat "
    //     0xae6478: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] "Riwayat "
    //     0xae647c: ldr             x16, [x16, #0xbc0]
    // 0xae6480: StoreField: r0->field_f = r16
    //     0xae6480: stur            w16, [x0, #0xf]
    // 0xae6484: ldur            x1, [fp, #-8]
    // 0xae6488: r0 = controller()
    //     0xae6488: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae648c: LoadField: r1 = r0->field_37
    //     0xae648c: ldur            w1, [x0, #0x37]
    // 0xae6490: DecompressPointer r1
    //     0xae6490: add             x1, x1, HEAP, lsl #32
    // 0xae6494: LoadField: r0 = r1->field_13
    //     0xae6494: ldur            w0, [x1, #0x13]
    // 0xae6498: DecompressPointer r0
    //     0xae6498: add             x0, x0, HEAP, lsl #32
    // 0xae649c: ldur            x1, [fp, #-0x18]
    // 0xae64a0: ArrayStore: r1[1] = r0  ; List_4
    //     0xae64a0: add             x25, x1, #0x13
    //     0xae64a4: str             w0, [x25]
    //     0xae64a8: tbz             w0, #0, #0xae64c4
    //     0xae64ac: ldurb           w16, [x1, #-1]
    //     0xae64b0: ldurb           w17, [x0, #-1]
    //     0xae64b4: and             x16, x17, x16, lsr #2
    //     0xae64b8: tst             x16, HEAP, lsr #32
    //     0xae64bc: b.eq            #0xae64c4
    //     0xae64c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae64c4: ldur            x16, [fp, #-0x18]
    // 0xae64c8: str             x16, [SP]
    // 0xae64cc: r0 = _interpolate()
    //     0xae64cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xae64d0: stur            x0, [fp, #-0x18]
    // 0xae64d4: r0 = Text()
    //     0xae64d4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae64d8: mov             x3, x0
    // 0xae64dc: ldur            x0, [fp, #-0x18]
    // 0xae64e0: stur            x3, [fp, #-0x20]
    // 0xae64e4: StoreField: r3->field_b = r0
    //     0xae64e4: stur            w0, [x3, #0xb]
    // 0xae64e8: r1 = <Widget>
    //     0xae64e8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae64ec: r2 = 0
    //     0xae64ec: movz            x2, #0
    // 0xae64f0: r0 = _GrowableList()
    //     0xae64f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xae64f4: ldur            x1, [fp, #-8]
    // 0xae64f8: stur            x0, [fp, #-0x18]
    // 0xae64fc: r0 = controller()
    //     0xae64fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6500: LoadField: r1 = r0->field_37
    //     0xae6500: ldur            w1, [x0, #0x37]
    // 0xae6504: DecompressPointer r1
    //     0xae6504: add             x1, x1, HEAP, lsl #32
    // 0xae6508: r16 = Instance_PaymentType
    //     0xae6508: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xae650c: ldr             x16, [x16, #0x5a8]
    // 0xae6510: cmp             w1, w16
    // 0xae6514: b.ne            #0xae65bc
    // 0xae6518: ldur            x1, [fp, #-0x18]
    // 0xae651c: r0 = Obx()
    //     0xae651c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae6520: ldur            x2, [fp, #-0x10]
    // 0xae6524: r1 = Function '<anonymous closure>':.
    //     0xae6524: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] AnonymousClosure: (0xae6a8c), in [package:nuonline/app/modules/donation/views/transaction_history_view.dart] TransactionHistoryView::build (0xae6434)
    //     0xae6528: ldr             x1, [x1, #0xbc8]
    // 0xae652c: stur            x0, [fp, #-0x28]
    // 0xae6530: r0 = AllocateClosure()
    //     0xae6530: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6534: mov             x1, x0
    // 0xae6538: ldur            x0, [fp, #-0x28]
    // 0xae653c: StoreField: r0->field_b = r1
    //     0xae653c: stur            w1, [x0, #0xb]
    // 0xae6540: ldur            x2, [fp, #-0x18]
    // 0xae6544: LoadField: r1 = r2->field_b
    //     0xae6544: ldur            w1, [x2, #0xb]
    // 0xae6548: LoadField: r3 = r2->field_f
    //     0xae6548: ldur            w3, [x2, #0xf]
    // 0xae654c: DecompressPointer r3
    //     0xae654c: add             x3, x3, HEAP, lsl #32
    // 0xae6550: LoadField: r4 = r3->field_b
    //     0xae6550: ldur            w4, [x3, #0xb]
    // 0xae6554: r3 = LoadInt32Instr(r1)
    //     0xae6554: sbfx            x3, x1, #1, #0x1f
    // 0xae6558: stur            x3, [fp, #-0x30]
    // 0xae655c: r1 = LoadInt32Instr(r4)
    //     0xae655c: sbfx            x1, x4, #1, #0x1f
    // 0xae6560: cmp             x3, x1
    // 0xae6564: b.ne            #0xae6570
    // 0xae6568: mov             x1, x2
    // 0xae656c: r0 = _growToNextCapacity()
    //     0xae656c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae6570: ldur            x2, [fp, #-0x18]
    // 0xae6574: ldur            x3, [fp, #-0x30]
    // 0xae6578: add             x0, x3, #1
    // 0xae657c: lsl             x1, x0, #1
    // 0xae6580: StoreField: r2->field_b = r1
    //     0xae6580: stur            w1, [x2, #0xb]
    // 0xae6584: LoadField: r1 = r2->field_f
    //     0xae6584: ldur            w1, [x2, #0xf]
    // 0xae6588: DecompressPointer r1
    //     0xae6588: add             x1, x1, HEAP, lsl #32
    // 0xae658c: ldur            x0, [fp, #-0x28]
    // 0xae6590: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae6590: add             x25, x1, x3, lsl #2
    //     0xae6594: add             x25, x25, #0xf
    //     0xae6598: str             w0, [x25]
    //     0xae659c: tbz             w0, #0, #0xae65b8
    //     0xae65a0: ldurb           w16, [x1, #-1]
    //     0xae65a4: ldurb           w17, [x0, #-1]
    //     0xae65a8: and             x16, x17, x16, lsr #2
    //     0xae65ac: tst             x16, HEAP, lsr #32
    //     0xae65b0: b.eq            #0xae65b8
    //     0xae65b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae65b8: b               #0xae65c0
    // 0xae65bc: ldur            x2, [fp, #-0x18]
    // 0xae65c0: r0 = AppBar()
    //     0xae65c0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xae65c4: stur            x0, [fp, #-0x28]
    // 0xae65c8: ldur            x16, [fp, #-0x20]
    // 0xae65cc: ldur            lr, [fp, #-0x18]
    // 0xae65d0: stp             lr, x16, [SP]
    // 0xae65d4: mov             x1, x0
    // 0xae65d8: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xae65d8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xae65dc: ldr             x4, [x4, #0xf88]
    // 0xae65e0: r0 = AppBar()
    //     0xae65e0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xae65e4: ldur            x1, [fp, #-8]
    // 0xae65e8: r0 = controller()
    //     0xae65e8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae65ec: stur            x0, [fp, #-8]
    // 0xae65f0: r0 = Obx()
    //     0xae65f0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae65f4: ldur            x2, [fp, #-0x10]
    // 0xae65f8: r1 = Function '<anonymous closure>':.
    //     0xae65f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbd0] AnonymousClosure: (0xae66dc), in [package:nuonline/app/modules/donation/views/transaction_history_view.dart] TransactionHistoryView::build (0xae6434)
    //     0xae65fc: ldr             x1, [x1, #0xbd0]
    // 0xae6600: stur            x0, [fp, #-0x10]
    // 0xae6604: r0 = AllocateClosure()
    //     0xae6604: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6608: mov             x1, x0
    // 0xae660c: ldur            x0, [fp, #-0x10]
    // 0xae6610: StoreField: r0->field_b = r1
    //     0xae6610: stur            w1, [x0, #0xb]
    // 0xae6614: r0 = RefreshIndicator()
    //     0xae6614: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xae6618: mov             x3, x0
    // 0xae661c: ldur            x0, [fp, #-0x10]
    // 0xae6620: stur            x3, [fp, #-0x18]
    // 0xae6624: StoreField: r3->field_b = r0
    //     0xae6624: stur            w0, [x3, #0xb]
    // 0xae6628: d0 = 40.000000
    //     0xae6628: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xae662c: StoreField: r3->field_f = d0
    //     0xae662c: stur            d0, [x3, #0xf]
    // 0xae6630: ArrayStore: r3[0] = rZR  ; List_8
    //     0xae6630: stur            xzr, [x3, #0x17]
    // 0xae6634: ldur            x2, [fp, #-8]
    // 0xae6638: r1 = Function 'onPageRefresh':.
    //     0xae6638: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b020] AnonymousClosure: (0xae7024), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageRefresh (0xae6ebc)
    //     0xae663c: ldr             x1, [x1, #0x20]
    // 0xae6640: r0 = AllocateClosure()
    //     0xae6640: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6644: mov             x1, x0
    // 0xae6648: ldur            x0, [fp, #-0x18]
    // 0xae664c: StoreField: r0->field_1f = r1
    //     0xae664c: stur            w1, [x0, #0x1f]
    // 0xae6650: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xae6650: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xae6654: ldr             x1, [x1, #0xf58]
    // 0xae6658: StoreField: r0->field_2f = r1
    //     0xae6658: stur            w1, [x0, #0x2f]
    // 0xae665c: d0 = 2.500000
    //     0xae665c: fmov            d0, #2.50000000
    // 0xae6660: StoreField: r0->field_3b = d0
    //     0xae6660: stur            d0, [x0, #0x3b]
    // 0xae6664: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xae6664: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xae6668: ldr             x1, [x1, #0xa68]
    // 0xae666c: StoreField: r0->field_47 = r1
    //     0xae666c: stur            w1, [x0, #0x47]
    // 0xae6670: d0 = 2.000000
    //     0xae6670: fmov            d0, #2.00000000
    // 0xae6674: StoreField: r0->field_4b = d0
    //     0xae6674: stur            d0, [x0, #0x4b]
    // 0xae6678: r1 = Instance__IndicatorType
    //     0xae6678: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xae667c: ldr             x1, [x1, #0xa70]
    // 0xae6680: StoreField: r0->field_43 = r1
    //     0xae6680: stur            w1, [x0, #0x43]
    // 0xae6684: r0 = Scaffold()
    //     0xae6684: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xae6688: ldur            x1, [fp, #-0x28]
    // 0xae668c: StoreField: r0->field_13 = r1
    //     0xae668c: stur            w1, [x0, #0x13]
    // 0xae6690: ldur            x1, [fp, #-0x18]
    // 0xae6694: ArrayStore: r0[0] = r1  ; List_4
    //     0xae6694: stur            w1, [x0, #0x17]
    // 0xae6698: r1 = Instance_AlignmentDirectional
    //     0xae6698: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xae669c: ldr             x1, [x1, #0x758]
    // 0xae66a0: StoreField: r0->field_2b = r1
    //     0xae66a0: stur            w1, [x0, #0x2b]
    // 0xae66a4: r1 = true
    //     0xae66a4: add             x1, NULL, #0x20  ; true
    // 0xae66a8: StoreField: r0->field_53 = r1
    //     0xae66a8: stur            w1, [x0, #0x53]
    // 0xae66ac: r2 = Instance_DragStartBehavior
    //     0xae66ac: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xae66b0: StoreField: r0->field_57 = r2
    //     0xae66b0: stur            w2, [x0, #0x57]
    // 0xae66b4: r2 = false
    //     0xae66b4: add             x2, NULL, #0x30  ; false
    // 0xae66b8: StoreField: r0->field_b = r2
    //     0xae66b8: stur            w2, [x0, #0xb]
    // 0xae66bc: StoreField: r0->field_f = r2
    //     0xae66bc: stur            w2, [x0, #0xf]
    // 0xae66c0: StoreField: r0->field_5f = r1
    //     0xae66c0: stur            w1, [x0, #0x5f]
    // 0xae66c4: StoreField: r0->field_63 = r1
    //     0xae66c4: stur            w1, [x0, #0x63]
    // 0xae66c8: LeaveFrame
    //     0xae66c8: mov             SP, fp
    //     0xae66cc: ldp             fp, lr, [SP], #0x10
    // 0xae66d0: ret
    //     0xae66d0: ret             
    // 0xae66d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae66d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae66d8: b               #0xae6450
  }
  [closure] NotificationListener<ScrollNotification> <anonymous closure>(dynamic) {
    // ** addr: 0xae66dc, size: 0xf0
    // 0xae66dc: EnterFrame
    //     0xae66dc: stp             fp, lr, [SP, #-0x10]!
    //     0xae66e0: mov             fp, SP
    // 0xae66e4: AllocStack(0x30)
    //     0xae66e4: sub             SP, SP, #0x30
    // 0xae66e8: SetupParameters()
    //     0xae66e8: ldr             x0, [fp, #0x10]
    //     0xae66ec: ldur            w2, [x0, #0x17]
    //     0xae66f0: add             x2, x2, HEAP, lsl #32
    //     0xae66f4: stur            x2, [fp, #-8]
    // 0xae66f8: CheckStackOverflow
    //     0xae66f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae66fc: cmp             SP, x16
    //     0xae6700: b.ls            #0xae67c4
    // 0xae6704: LoadField: r1 = r2->field_f
    //     0xae6704: ldur            w1, [x2, #0xf]
    // 0xae6708: DecompressPointer r1
    //     0xae6708: add             x1, x1, HEAP, lsl #32
    // 0xae670c: r0 = controller()
    //     0xae670c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6710: ldur            x2, [fp, #-8]
    // 0xae6714: stur            x0, [fp, #-0x10]
    // 0xae6718: LoadField: r1 = r2->field_f
    //     0xae6718: ldur            w1, [x2, #0xf]
    // 0xae671c: DecompressPointer r1
    //     0xae671c: add             x1, x1, HEAP, lsl #32
    // 0xae6720: r0 = controller()
    //     0xae6720: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6724: mov             x1, x0
    // 0xae6728: r0 = itemsCount()
    //     0xae6728: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xae672c: r1 = Function '<anonymous closure>':.
    //     0xae672c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbd8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xae6730: ldr             x1, [x1, #0xbd8]
    // 0xae6734: r2 = Null
    //     0xae6734: mov             x2, NULL
    // 0xae6738: stur            x0, [fp, #-0x18]
    // 0xae673c: r0 = AllocateClosure()
    //     0xae673c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6740: ldur            x2, [fp, #-8]
    // 0xae6744: r1 = Function '<anonymous closure>':.
    //     0xae6744: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbe0] AnonymousClosure: (0xae67cc), in [package:nuonline/app/modules/donation/views/transaction_history_view.dart] TransactionHistoryView::build (0xae6434)
    //     0xae6748: ldr             x1, [x1, #0xbe0]
    // 0xae674c: stur            x0, [fp, #-8]
    // 0xae6750: r0 = AllocateClosure()
    //     0xae6750: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6754: stur            x0, [fp, #-0x20]
    // 0xae6758: r0 = ListView()
    //     0xae6758: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xae675c: stur            x0, [fp, #-0x28]
    // 0xae6760: r16 = Instance_EdgeInsets
    //     0xae6760: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xae6764: ldr             x16, [x16, #0x360]
    // 0xae6768: str             x16, [SP]
    // 0xae676c: mov             x1, x0
    // 0xae6770: ldur            x2, [fp, #-0x20]
    // 0xae6774: ldur            x3, [fp, #-0x18]
    // 0xae6778: ldur            x5, [fp, #-8]
    // 0xae677c: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xae677c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xae6780: ldr             x4, [x4, #0x700]
    // 0xae6784: r0 = ListView.separated()
    //     0xae6784: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xae6788: ldur            x2, [fp, #-0x10]
    // 0xae678c: r1 = Function 'onPageScrolled':.
    //     0xae678c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b2b8] AnonymousClosure: (0xae6984), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageScrolled (0xae69c0)
    //     0xae6790: ldr             x1, [x1, #0x2b8]
    // 0xae6794: r0 = AllocateClosure()
    //     0xae6794: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6798: r1 = <ScrollNotification>
    //     0xae6798: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xae679c: ldr             x1, [x1, #0x110]
    // 0xae67a0: stur            x0, [fp, #-8]
    // 0xae67a4: r0 = NotificationListener()
    //     0xae67a4: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xae67a8: ldur            x1, [fp, #-8]
    // 0xae67ac: StoreField: r0->field_13 = r1
    //     0xae67ac: stur            w1, [x0, #0x13]
    // 0xae67b0: ldur            x1, [fp, #-0x28]
    // 0xae67b4: StoreField: r0->field_b = r1
    //     0xae67b4: stur            w1, [x0, #0xb]
    // 0xae67b8: LeaveFrame
    //     0xae67b8: mov             SP, fp
    //     0xae67bc: ldp             fp, lr, [SP], #0x10
    // 0xae67c0: ret
    //     0xae67c0: ret             
    // 0xae67c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae67c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae67c8: b               #0xae6704
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xae67cc, size: 0x8c
    // 0xae67cc: EnterFrame
    //     0xae67cc: stp             fp, lr, [SP, #-0x10]!
    //     0xae67d0: mov             fp, SP
    // 0xae67d4: AllocStack(0x8)
    //     0xae67d4: sub             SP, SP, #8
    // 0xae67d8: SetupParameters()
    //     0xae67d8: ldr             x0, [fp, #0x20]
    //     0xae67dc: ldur            w1, [x0, #0x17]
    //     0xae67e0: add             x1, x1, HEAP, lsl #32
    // 0xae67e4: CheckStackOverflow
    //     0xae67e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae67e8: cmp             SP, x16
    //     0xae67ec: b.ls            #0xae6850
    // 0xae67f0: LoadField: r0 = r1->field_f
    //     0xae67f0: ldur            w0, [x1, #0xf]
    // 0xae67f4: DecompressPointer r0
    //     0xae67f4: add             x0, x0, HEAP, lsl #32
    // 0xae67f8: mov             x1, x0
    // 0xae67fc: r0 = controller()
    //     0xae67fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6800: mov             x1, x0
    // 0xae6804: ldr             x0, [fp, #0x10]
    // 0xae6808: r2 = LoadInt32Instr(r0)
    //     0xae6808: sbfx            x2, x0, #1, #0x1f
    //     0xae680c: tbz             w0, #0, #0xae6814
    //     0xae6810: ldur            x2, [x0, #7]
    // 0xae6814: r0 = find()
    //     0xae6814: bl              #0xae6864  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::find
    // 0xae6818: stur            x0, [fp, #-8]
    // 0xae681c: cmp             w0, NULL
    // 0xae6820: b.ne            #0xae6838
    // 0xae6824: r0 = Instance_NSkeleton
    //     0xae6824: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b2c0] Obj!NSkeleton@e20941
    //     0xae6828: ldr             x0, [x0, #0x2c0]
    // 0xae682c: LeaveFrame
    //     0xae682c: mov             SP, fp
    //     0xae6830: ldp             fp, lr, [SP], #0x10
    // 0xae6834: ret
    //     0xae6834: ret             
    // 0xae6838: r0 = TransactionHistoryListTile()
    //     0xae6838: bl              #0xae6858  ; AllocateTransactionHistoryListTileStub -> TransactionHistoryListTile (size=0x10)
    // 0xae683c: ldur            x1, [fp, #-8]
    // 0xae6840: StoreField: r0->field_b = r1
    //     0xae6840: stur            w1, [x0, #0xb]
    // 0xae6844: LeaveFrame
    //     0xae6844: mov             SP, fp
    //     0xae6848: ldp             fp, lr, [SP], #0x10
    // 0xae684c: ret
    //     0xae684c: ret             
    // 0xae6850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6854: b               #0xae67f0
  }
  [closure] TextButton <anonymous closure>(dynamic) {
    // ** addr: 0xae6a8c, size: 0x128
    // 0xae6a8c: EnterFrame
    //     0xae6a8c: stp             fp, lr, [SP, #-0x10]!
    //     0xae6a90: mov             fp, SP
    // 0xae6a94: AllocStack(0x28)
    //     0xae6a94: sub             SP, SP, #0x28
    // 0xae6a98: SetupParameters()
    //     0xae6a98: ldr             x0, [fp, #0x10]
    //     0xae6a9c: ldur            w2, [x0, #0x17]
    //     0xae6aa0: add             x2, x2, HEAP, lsl #32
    //     0xae6aa4: stur            x2, [fp, #-8]
    // 0xae6aa8: CheckStackOverflow
    //     0xae6aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6aac: cmp             SP, x16
    //     0xae6ab0: b.ls            #0xae6bac
    // 0xae6ab4: LoadField: r1 = r2->field_f
    //     0xae6ab4: ldur            w1, [x2, #0xf]
    // 0xae6ab8: DecompressPointer r1
    //     0xae6ab8: add             x1, x1, HEAP, lsl #32
    // 0xae6abc: r0 = controller()
    //     0xae6abc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6ac0: mov             x2, x0
    // 0xae6ac4: ldur            x0, [fp, #-8]
    // 0xae6ac8: stur            x2, [fp, #-0x10]
    // 0xae6acc: LoadField: r1 = r0->field_f
    //     0xae6acc: ldur            w1, [x0, #0xf]
    // 0xae6ad0: DecompressPointer r1
    //     0xae6ad0: add             x1, x1, HEAP, lsl #32
    // 0xae6ad4: r0 = controller()
    //     0xae6ad4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6ad8: LoadField: r1 = r0->field_47
    //     0xae6ad8: ldur            w1, [x0, #0x47]
    // 0xae6adc: DecompressPointer r1
    //     0xae6adc: add             x1, x1, HEAP, lsl #32
    // 0xae6ae0: r0 = value()
    //     0xae6ae0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6ae4: r0 = Icon()
    //     0xae6ae4: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xae6ae8: mov             x2, x0
    // 0xae6aec: r0 = Instance_IconData
    //     0xae6aec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbe8] Obj!IconData@e102b1
    //     0xae6af0: ldr             x0, [x0, #0xbe8]
    // 0xae6af4: stur            x2, [fp, #-0x18]
    // 0xae6af8: StoreField: r2->field_b = r0
    //     0xae6af8: stur            w0, [x2, #0xb]
    // 0xae6afc: r0 = 16.000000
    //     0xae6afc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xae6b00: ldr             x0, [x0, #0x80]
    // 0xae6b04: StoreField: r2->field_f = r0
    //     0xae6b04: stur            w0, [x2, #0xf]
    // 0xae6b08: ldur            x0, [fp, #-8]
    // 0xae6b0c: LoadField: r1 = r0->field_f
    //     0xae6b0c: ldur            w1, [x0, #0xf]
    // 0xae6b10: DecompressPointer r1
    //     0xae6b10: add             x1, x1, HEAP, lsl #32
    // 0xae6b14: r0 = controller()
    //     0xae6b14: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae6b18: LoadField: r1 = r0->field_47
    //     0xae6b18: ldur            w1, [x0, #0x47]
    // 0xae6b1c: DecompressPointer r1
    //     0xae6b1c: add             x1, x1, HEAP, lsl #32
    // 0xae6b20: r0 = value()
    //     0xae6b20: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae6b24: LoadField: r1 = r0->field_7
    //     0xae6b24: ldur            w1, [x0, #7]
    // 0xae6b28: DecompressPointer r1
    //     0xae6b28: add             x1, x1, HEAP, lsl #32
    // 0xae6b2c: r0 = OrderByExtension.title()
    //     0xae6b2c: bl              #0xae6bb4  ; [package:nuonline/app/data/enums/sort_ordering_enum.dart] ::OrderByExtension.title
    // 0xae6b30: stur            x0, [fp, #-8]
    // 0xae6b34: r0 = Text()
    //     0xae6b34: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae6b38: mov             x1, x0
    // 0xae6b3c: ldur            x0, [fp, #-8]
    // 0xae6b40: stur            x1, [fp, #-0x20]
    // 0xae6b44: StoreField: r1->field_b = r0
    //     0xae6b44: stur            w0, [x1, #0xb]
    // 0xae6b48: r0 = Instance_TextStyle
    //     0xae6b48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbf0] Obj!TextStyle@e1b651
    //     0xae6b4c: ldr             x0, [x0, #0xbf0]
    // 0xae6b50: StoreField: r1->field_13 = r0
    //     0xae6b50: stur            w0, [x1, #0x13]
    // 0xae6b54: r16 = Instance_Color
    //     0xae6b54: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xae6b58: str             x16, [SP]
    // 0xae6b5c: r4 = const [0, 0x1, 0x1, 0, backgroundColor, 0, null]
    //     0xae6b5c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c2b8] List(7) [0, 0x1, 0x1, 0, "backgroundColor", 0, Null]
    //     0xae6b60: ldr             x4, [x4, #0x2b8]
    // 0xae6b64: r0 = styleFrom()
    //     0xae6b64: bl              #0xa9bb70  ; [package:flutter/src/material/text_button.dart] TextButton::styleFrom
    // 0xae6b68: ldur            x2, [fp, #-0x10]
    // 0xae6b6c: r1 = Function 'selectOrderBy':.
    //     0xae6b6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbf8] AnonymousClosure: (0xae6c34), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::selectOrderBy (0xae6c6c)
    //     0xae6b70: ldr             x1, [x1, #0xbf8]
    // 0xae6b74: stur            x0, [fp, #-8]
    // 0xae6b78: r0 = AllocateClosure()
    //     0xae6b78: bl              #0xec1630  ; AllocateClosureStub
    // 0xae6b7c: ldur            x16, [fp, #-8]
    // 0xae6b80: str             x16, [SP]
    // 0xae6b84: ldur            x2, [fp, #-0x18]
    // 0xae6b88: ldur            x3, [fp, #-0x20]
    // 0xae6b8c: mov             x5, x0
    // 0xae6b90: r1 = Null
    //     0xae6b90: mov             x1, NULL
    // 0xae6b94: r4 = const [0, 0x5, 0x1, 0x4, style, 0x4, null]
    //     0xae6b94: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc00] List(7) [0, 0x5, 0x1, 0x4, "style", 0x4, Null]
    //     0xae6b98: ldr             x4, [x4, #0xc00]
    // 0xae6b9c: r0 = TextButton.icon()
    //     0xae6b9c: bl              #0xae179c  ; [package:flutter/src/material/text_button.dart] TextButton::TextButton.icon
    // 0xae6ba0: LeaveFrame
    //     0xae6ba0: mov             SP, fp
    //     0xae6ba4: ldp             fp, lr, [SP], #0x10
    // 0xae6ba8: ret
    //     0xae6ba8: ret             
    // 0xae6bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6bac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6bb0: b               #0xae6ab4
  }
}
