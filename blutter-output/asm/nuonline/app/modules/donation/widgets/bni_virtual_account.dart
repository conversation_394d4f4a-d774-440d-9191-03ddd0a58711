// lib: , url: package:nuonline/app/modules/donation/widgets/bni_virtual_account.dart

// class id: 1050229, size: 0x8
class :: {
}

// class id: 5039, size: 0x1c, field offset: 0xc
//   const constructor, 
class BNIVirtualAccount extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8f32c, size: 0x214
    // 0xb8f32c: EnterFrame
    //     0xb8f32c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f330: mov             fp, SP
    // 0xb8f334: AllocStack(0x38)
    //     0xb8f334: sub             SP, SP, #0x38
    // 0xb8f338: SetupParameters(BNIVirtualAccount this /* r1 => r1, fp-0x10 */)
    //     0xb8f338: stur            x1, [fp, #-0x10]
    // 0xb8f33c: CheckStackOverflow
    //     0xb8f33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f340: cmp             SP, x16
    //     0xb8f344: b.ls            #0xb8f534
    // 0xb8f348: LoadField: r0 = r1->field_b
    //     0xb8f348: ldur            w0, [x1, #0xb]
    // 0xb8f34c: DecompressPointer r0
    //     0xb8f34c: add             x0, x0, HEAP, lsl #32
    // 0xb8f350: stur            x0, [fp, #-8]
    // 0xb8f354: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8f354: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8f358: ldr             x0, [x0, #0x2670]
    //     0xb8f35c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8f360: cmp             w0, w16
    //     0xb8f364: b.ne            #0xb8f370
    //     0xb8f368: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8f36c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8f370: r0 = GetNavigation.textTheme()
    //     0xb8f370: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8f374: LoadField: r1 = r0->field_23
    //     0xb8f374: ldur            w1, [x0, #0x23]
    // 0xb8f378: DecompressPointer r1
    //     0xb8f378: add             x1, x1, HEAP, lsl #32
    // 0xb8f37c: cmp             w1, NULL
    // 0xb8f380: b.eq            #0xb8f53c
    // 0xb8f384: r16 = Instance_FontWeight
    //     0xb8f384: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb8f388: ldr             x16, [x16, #0xe20]
    // 0xb8f38c: str             x16, [SP]
    // 0xb8f390: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb8f390: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb8f394: ldr             x4, [x4, #0xfe0]
    // 0xb8f398: r0 = copyWith()
    //     0xb8f398: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f39c: stur            x0, [fp, #-0x18]
    // 0xb8f3a0: r0 = Text()
    //     0xb8f3a0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8f3a4: mov             x1, x0
    // 0xb8f3a8: ldur            x0, [fp, #-8]
    // 0xb8f3ac: stur            x1, [fp, #-0x20]
    // 0xb8f3b0: StoreField: r1->field_b = r0
    //     0xb8f3b0: stur            w0, [x1, #0xb]
    // 0xb8f3b4: ldur            x0, [fp, #-0x18]
    // 0xb8f3b8: StoreField: r1->field_13 = r0
    //     0xb8f3b8: stur            w0, [x1, #0x13]
    // 0xb8f3bc: ldur            x0, [fp, #-0x10]
    // 0xb8f3c0: LoadField: r2 = r0->field_f
    //     0xb8f3c0: ldur            w2, [x0, #0xf]
    // 0xb8f3c4: DecompressPointer r2
    //     0xb8f3c4: add             x2, x2, HEAP, lsl #32
    // 0xb8f3c8: stur            x2, [fp, #-8]
    // 0xb8f3cc: r0 = NCopyTextField()
    //     0xb8f3cc: bl              #0xb8f540  ; AllocateNCopyTextFieldStub -> NCopyTextField (size=0x14)
    // 0xb8f3d0: mov             x2, x0
    // 0xb8f3d4: ldur            x0, [fp, #-8]
    // 0xb8f3d8: stur            x2, [fp, #-0x18]
    // 0xb8f3dc: StoreField: r2->field_b = r0
    //     0xb8f3dc: stur            w0, [x2, #0xb]
    // 0xb8f3e0: ldur            x0, [fp, #-0x10]
    // 0xb8f3e4: LoadField: r3 = r0->field_13
    //     0xb8f3e4: ldur            x3, [x0, #0x13]
    // 0xb8f3e8: stur            x3, [fp, #-0x28]
    // 0xb8f3ec: r0 = BoxInt64Instr(r3)
    //     0xb8f3ec: sbfiz           x0, x3, #1, #0x1f
    //     0xb8f3f0: cmp             x3, x0, asr #1
    //     0xb8f3f4: b.eq            #0xb8f400
    //     0xb8f3f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8f3fc: stur            x3, [x0, #7]
    // 0xb8f400: r1 = 60
    //     0xb8f400: movz            x1, #0x3c
    // 0xb8f404: branchIfSmi(r0, 0xb8f410)
    //     0xb8f404: tbz             w0, #0, #0xb8f410
    // 0xb8f408: r1 = LoadClassIdInstr(r0)
    //     0xb8f408: ldur            x1, [x0, #-1]
    //     0xb8f40c: ubfx            x1, x1, #0xc, #0x14
    // 0xb8f410: str             x0, [SP]
    // 0xb8f414: mov             x0, x1
    // 0xb8f418: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb8f418: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb8f41c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb8f41c: movz            x17, #0x2b03
    //     0xb8f420: add             lr, x0, x17
    //     0xb8f424: ldr             lr, [x21, lr, lsl #3]
    //     0xb8f428: blr             lr
    // 0xb8f42c: ldur            x1, [fp, #-0x28]
    // 0xb8f430: stur            x0, [fp, #-8]
    // 0xb8f434: r0 = IntExtension.idr()
    //     0xb8f434: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb8f438: stur            x0, [fp, #-0x10]
    // 0xb8f43c: r0 = NCopyTextField()
    //     0xb8f43c: bl              #0xb8f540  ; AllocateNCopyTextFieldStub -> NCopyTextField (size=0x14)
    // 0xb8f440: mov             x3, x0
    // 0xb8f444: ldur            x0, [fp, #-8]
    // 0xb8f448: stur            x3, [fp, #-0x30]
    // 0xb8f44c: StoreField: r3->field_b = r0
    //     0xb8f44c: stur            w0, [x3, #0xb]
    // 0xb8f450: ldur            x0, [fp, #-0x10]
    // 0xb8f454: StoreField: r3->field_f = r0
    //     0xb8f454: stur            w0, [x3, #0xf]
    // 0xb8f458: r1 = Null
    //     0xb8f458: mov             x1, NULL
    // 0xb8f45c: r2 = 14
    //     0xb8f45c: movz            x2, #0xe
    // 0xb8f460: r0 = AllocateArray()
    //     0xb8f460: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8f464: mov             x2, x0
    // 0xb8f468: ldur            x0, [fp, #-0x20]
    // 0xb8f46c: stur            x2, [fp, #-8]
    // 0xb8f470: StoreField: r2->field_f = r0
    //     0xb8f470: stur            w0, [x2, #0xf]
    // 0xb8f474: r16 = Instance_SizedBox
    //     0xb8f474: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8f478: ldr             x16, [x16, #0xfe8]
    // 0xb8f47c: StoreField: r2->field_13 = r16
    //     0xb8f47c: stur            w16, [x2, #0x13]
    // 0xb8f480: r16 = Instance_NLabelTextField
    //     0xb8f480: add             x16, PP, #0x47, lsl #12  ; [pp+0x47bb0] Obj!NLabelTextField@e1fcf1
    //     0xb8f484: ldr             x16, [x16, #0xbb0]
    // 0xb8f488: ArrayStore: r2[0] = r16  ; List_4
    //     0xb8f488: stur            w16, [x2, #0x17]
    // 0xb8f48c: ldur            x0, [fp, #-0x18]
    // 0xb8f490: StoreField: r2->field_1b = r0
    //     0xb8f490: stur            w0, [x2, #0x1b]
    // 0xb8f494: r16 = Instance_SizedBox
    //     0xb8f494: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb8f498: ldr             x16, [x16, #0xfe8]
    // 0xb8f49c: StoreField: r2->field_1f = r16
    //     0xb8f49c: stur            w16, [x2, #0x1f]
    // 0xb8f4a0: r16 = Instance_NLabelTextField
    //     0xb8f4a0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47bb8] Obj!NLabelTextField@e1fcd1
    //     0xb8f4a4: ldr             x16, [x16, #0xbb8]
    // 0xb8f4a8: StoreField: r2->field_23 = r16
    //     0xb8f4a8: stur            w16, [x2, #0x23]
    // 0xb8f4ac: ldur            x0, [fp, #-0x30]
    // 0xb8f4b0: StoreField: r2->field_27 = r0
    //     0xb8f4b0: stur            w0, [x2, #0x27]
    // 0xb8f4b4: r1 = <Widget>
    //     0xb8f4b4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8f4b8: r0 = AllocateGrowableArray()
    //     0xb8f4b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8f4bc: mov             x1, x0
    // 0xb8f4c0: ldur            x0, [fp, #-8]
    // 0xb8f4c4: stur            x1, [fp, #-0x10]
    // 0xb8f4c8: StoreField: r1->field_f = r0
    //     0xb8f4c8: stur            w0, [x1, #0xf]
    // 0xb8f4cc: r0 = 14
    //     0xb8f4cc: movz            x0, #0xe
    // 0xb8f4d0: StoreField: r1->field_b = r0
    //     0xb8f4d0: stur            w0, [x1, #0xb]
    // 0xb8f4d4: r0 = Column()
    //     0xb8f4d4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8f4d8: r1 = Instance_Axis
    //     0xb8f4d8: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8f4dc: StoreField: r0->field_f = r1
    //     0xb8f4dc: stur            w1, [x0, #0xf]
    // 0xb8f4e0: r1 = Instance_MainAxisAlignment
    //     0xb8f4e0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8f4e4: ldr             x1, [x1, #0x730]
    // 0xb8f4e8: StoreField: r0->field_13 = r1
    //     0xb8f4e8: stur            w1, [x0, #0x13]
    // 0xb8f4ec: r1 = Instance_MainAxisSize
    //     0xb8f4ec: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8f4f0: ldr             x1, [x1, #0x738]
    // 0xb8f4f4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8f4f4: stur            w1, [x0, #0x17]
    // 0xb8f4f8: r1 = Instance_CrossAxisAlignment
    //     0xb8f4f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xb8f4fc: ldr             x1, [x1, #0xf50]
    // 0xb8f500: StoreField: r0->field_1b = r1
    //     0xb8f500: stur            w1, [x0, #0x1b]
    // 0xb8f504: r1 = Instance_VerticalDirection
    //     0xb8f504: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8f508: ldr             x1, [x1, #0x748]
    // 0xb8f50c: StoreField: r0->field_23 = r1
    //     0xb8f50c: stur            w1, [x0, #0x23]
    // 0xb8f510: r1 = Instance_Clip
    //     0xb8f510: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8f514: ldr             x1, [x1, #0x750]
    // 0xb8f518: StoreField: r0->field_2b = r1
    //     0xb8f518: stur            w1, [x0, #0x2b]
    // 0xb8f51c: StoreField: r0->field_2f = rZR
    //     0xb8f51c: stur            xzr, [x0, #0x2f]
    // 0xb8f520: ldur            x1, [fp, #-0x10]
    // 0xb8f524: StoreField: r0->field_b = r1
    //     0xb8f524: stur            w1, [x0, #0xb]
    // 0xb8f528: LeaveFrame
    //     0xb8f528: mov             SP, fp
    //     0xb8f52c: ldp             fp, lr, [SP], #0x10
    // 0xb8f530: ret
    //     0xb8f530: ret             
    // 0xb8f534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f538: b               #0xb8f348
    // 0xb8f53c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8f53c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
