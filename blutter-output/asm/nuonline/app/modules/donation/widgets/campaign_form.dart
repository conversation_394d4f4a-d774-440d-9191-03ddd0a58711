// lib: , url: package:nuonline/app/modules/donation/widgets/campaign_form.dart

// class id: 1050230, size: 0x8
class :: {
}

// class id: 5287, size: 0x14, field offset: 0x14
//   const constructor, 
class _KoinNuLocation extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae8290, size: 0x1f8
    // 0xae8290: EnterFrame
    //     0xae8290: stp             fp, lr, [SP, #-0x10]!
    //     0xae8294: mov             fp, SP
    // 0xae8298: AllocStack(0x18)
    //     0xae8298: sub             SP, SP, #0x18
    // 0xae829c: SetupParameters(_KoinNuLocation this /* r1 => r0, fp-0x8 */)
    //     0xae829c: mov             x0, x1
    //     0xae82a0: stur            x1, [fp, #-8]
    // 0xae82a4: CheckStackOverflow
    //     0xae82a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae82a8: cmp             SP, x16
    //     0xae82ac: b.ls            #0xae8480
    // 0xae82b0: mov             x1, x0
    // 0xae82b4: r0 = controller()
    //     0xae82b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae82b8: LoadField: r1 = r0->field_3f
    //     0xae82b8: ldur            w1, [x0, #0x3f]
    // 0xae82bc: DecompressPointer r1
    //     0xae82bc: add             x1, x1, HEAP, lsl #32
    // 0xae82c0: cmp             w1, NULL
    // 0xae82c4: b.ne            #0xae82d0
    // 0xae82c8: r0 = Null
    //     0xae82c8: mov             x0, NULL
    // 0xae82cc: b               #0xae8308
    // 0xae82d0: LoadField: r0 = r1->field_3b
    //     0xae82d0: ldur            w0, [x1, #0x3b]
    // 0xae82d4: DecompressPointer r0
    //     0xae82d4: add             x0, x0, HEAP, lsl #32
    // 0xae82d8: LoadField: r1 = r0->field_27
    //     0xae82d8: ldur            w1, [x0, #0x27]
    // 0xae82dc: DecompressPointer r1
    //     0xae82dc: add             x1, x1, HEAP, lsl #32
    // 0xae82e0: cmp             w1, NULL
    // 0xae82e4: b.ne            #0xae82f0
    // 0xae82e8: r0 = Null
    //     0xae82e8: mov             x0, NULL
    // 0xae82ec: b               #0xae8308
    // 0xae82f0: LoadField: r2 = r1->field_7
    //     0xae82f0: ldur            x2, [x1, #7]
    // 0xae82f4: r0 = BoxInt64Instr(r2)
    //     0xae82f4: sbfiz           x0, x2, #1, #0x1f
    //     0xae82f8: cmp             x2, x0, asr #1
    //     0xae82fc: b.eq            #0xae8308
    //     0xae8300: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8304: stur            x2, [x0, #7]
    // 0xae8308: cmp             w0, NULL
    // 0xae830c: b.ne            #0xae8324
    // 0xae8310: r0 = Instance_SizedBox
    //     0xae8310: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae8314: ldr             x0, [x0, #0xc40]
    // 0xae8318: LeaveFrame
    //     0xae8318: mov             SP, fp
    //     0xae831c: ldp             fp, lr, [SP], #0x10
    // 0xae8320: ret
    //     0xae8320: ret             
    // 0xae8324: r2 = LoadInt32Instr(r0)
    //     0xae8324: sbfx            x2, x0, #1, #0x1f
    //     0xae8328: tbz             w0, #0, #0xae8330
    //     0xae832c: ldur            x2, [x0, #7]
    // 0xae8330: ldur            x1, [fp, #-8]
    // 0xae8334: r0 = getFields()
    //     0xae8334: bl              #0xae8488  ; [package:nuonline/app/modules/donation/widgets/campaign_form.dart] _KoinNuLocation::getFields
    // 0xae8338: stur            x0, [fp, #-0x10]
    // 0xae833c: LoadField: r1 = r0->field_b
    //     0xae833c: ldur            w1, [x0, #0xb]
    // 0xae8340: cbnz            w1, #0xae8358
    // 0xae8344: r0 = Instance_SizedBox
    //     0xae8344: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xae8348: ldr             x0, [x0, #0xc40]
    // 0xae834c: LeaveFrame
    //     0xae834c: mov             SP, fp
    //     0xae8350: ldp             fp, lr, [SP], #0x10
    // 0xae8354: ret
    //     0xae8354: ret             
    // 0xae8358: ldur            x1, [fp, #-8]
    // 0xae835c: r0 = controller()
    //     0xae835c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae8360: LoadField: r1 = r0->field_3f
    //     0xae8360: ldur            w1, [x0, #0x3f]
    // 0xae8364: DecompressPointer r1
    //     0xae8364: add             x1, x1, HEAP, lsl #32
    // 0xae8368: cmp             w1, NULL
    // 0xae836c: b.ne            #0xae8378
    // 0xae8370: r0 = Null
    //     0xae8370: mov             x0, NULL
    // 0xae8374: b               #0xae838c
    // 0xae8378: LoadField: r0 = r1->field_3b
    //     0xae8378: ldur            w0, [x1, #0x3b]
    // 0xae837c: DecompressPointer r0
    //     0xae837c: add             x0, x0, HEAP, lsl #32
    // 0xae8380: LoadField: r1 = r0->field_2b
    //     0xae8380: ldur            w1, [x0, #0x2b]
    // 0xae8384: DecompressPointer r1
    //     0xae8384: add             x1, x1, HEAP, lsl #32
    // 0xae8388: mov             x0, x1
    // 0xae838c: cmp             w0, NULL
    // 0xae8390: b.eq            #0xae8398
    // 0xae8394: tbz             w0, #4, #0xae83a4
    // 0xae8398: r1 = "Asal Wilayah (Opsional)"
    //     0xae8398: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ffe0] "Asal Wilayah (Opsional)"
    //     0xae839c: ldr             x1, [x1, #0xfe0]
    // 0xae83a0: b               #0xae83ac
    // 0xae83a4: r1 = "Asal Wilayah"
    //     0xae83a4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ffe8] "Asal Wilayah"
    //     0xae83a8: ldr             x1, [x1, #0xfe8]
    // 0xae83ac: ldur            x0, [fp, #-0x10]
    // 0xae83b0: stur            x1, [fp, #-8]
    // 0xae83b4: r0 = NSection()
    //     0xae83b4: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae83b8: mov             x3, x0
    // 0xae83bc: ldur            x0, [fp, #-8]
    // 0xae83c0: stur            x3, [fp, #-0x18]
    // 0xae83c4: StoreField: r3->field_b = r0
    //     0xae83c4: stur            w0, [x3, #0xb]
    // 0xae83c8: ldur            x0, [fp, #-0x10]
    // 0xae83cc: StoreField: r3->field_f = r0
    //     0xae83cc: stur            w0, [x3, #0xf]
    // 0xae83d0: r0 = false
    //     0xae83d0: add             x0, NULL, #0x30  ; false
    // 0xae83d4: StoreField: r3->field_27 = r0
    //     0xae83d4: stur            w0, [x3, #0x27]
    // 0xae83d8: StoreField: r3->field_2b = r0
    //     0xae83d8: stur            w0, [x3, #0x2b]
    // 0xae83dc: r1 = Null
    //     0xae83dc: mov             x1, NULL
    // 0xae83e0: r2 = 4
    //     0xae83e0: movz            x2, #0x4
    // 0xae83e4: r0 = AllocateArray()
    //     0xae83e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae83e8: stur            x0, [fp, #-8]
    // 0xae83ec: r16 = Instance_NSectionDivider
    //     0xae83ec: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xae83f0: ldr             x16, [x16, #0x38]
    // 0xae83f4: StoreField: r0->field_f = r16
    //     0xae83f4: stur            w16, [x0, #0xf]
    // 0xae83f8: ldur            x1, [fp, #-0x18]
    // 0xae83fc: StoreField: r0->field_13 = r1
    //     0xae83fc: stur            w1, [x0, #0x13]
    // 0xae8400: r1 = <Widget>
    //     0xae8400: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae8404: r0 = AllocateGrowableArray()
    //     0xae8404: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae8408: mov             x1, x0
    // 0xae840c: ldur            x0, [fp, #-8]
    // 0xae8410: stur            x1, [fp, #-0x10]
    // 0xae8414: StoreField: r1->field_f = r0
    //     0xae8414: stur            w0, [x1, #0xf]
    // 0xae8418: r0 = 4
    //     0xae8418: movz            x0, #0x4
    // 0xae841c: StoreField: r1->field_b = r0
    //     0xae841c: stur            w0, [x1, #0xb]
    // 0xae8420: r0 = Column()
    //     0xae8420: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae8424: r1 = Instance_Axis
    //     0xae8424: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae8428: StoreField: r0->field_f = r1
    //     0xae8428: stur            w1, [x0, #0xf]
    // 0xae842c: r1 = Instance_MainAxisAlignment
    //     0xae842c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae8430: ldr             x1, [x1, #0x730]
    // 0xae8434: StoreField: r0->field_13 = r1
    //     0xae8434: stur            w1, [x0, #0x13]
    // 0xae8438: r1 = Instance_MainAxisSize
    //     0xae8438: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae843c: ldr             x1, [x1, #0x738]
    // 0xae8440: ArrayStore: r0[0] = r1  ; List_4
    //     0xae8440: stur            w1, [x0, #0x17]
    // 0xae8444: r1 = Instance_CrossAxisAlignment
    //     0xae8444: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae8448: ldr             x1, [x1, #0x740]
    // 0xae844c: StoreField: r0->field_1b = r1
    //     0xae844c: stur            w1, [x0, #0x1b]
    // 0xae8450: r1 = Instance_VerticalDirection
    //     0xae8450: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae8454: ldr             x1, [x1, #0x748]
    // 0xae8458: StoreField: r0->field_23 = r1
    //     0xae8458: stur            w1, [x0, #0x23]
    // 0xae845c: r1 = Instance_Clip
    //     0xae845c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae8460: ldr             x1, [x1, #0x750]
    // 0xae8464: StoreField: r0->field_2b = r1
    //     0xae8464: stur            w1, [x0, #0x2b]
    // 0xae8468: StoreField: r0->field_2f = rZR
    //     0xae8468: stur            xzr, [x0, #0x2f]
    // 0xae846c: ldur            x1, [fp, #-0x10]
    // 0xae8470: StoreField: r0->field_b = r1
    //     0xae8470: stur            w1, [x0, #0xb]
    // 0xae8474: LeaveFrame
    //     0xae8474: mov             SP, fp
    //     0xae8478: ldp             fp, lr, [SP], #0x10
    // 0xae847c: ret
    //     0xae847c: ret             
    // 0xae8480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8484: b               #0xae82b0
  }
  _ getFields(/* No info */) {
    // ** addr: 0xae8488, size: 0x518
    // 0xae8488: EnterFrame
    //     0xae8488: stp             fp, lr, [SP, #-0x10]!
    //     0xae848c: mov             fp, SP
    // 0xae8490: AllocStack(0x40)
    //     0xae8490: sub             SP, SP, #0x40
    // 0xae8494: SetupParameters(_KoinNuLocation this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae8494: stur            x1, [fp, #-8]
    //     0xae8498: stur            x2, [fp, #-0x10]
    // 0xae849c: CheckStackOverflow
    //     0xae849c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae84a0: cmp             SP, x16
    //     0xae84a4: b.ls            #0xae8998
    // 0xae84a8: r1 = 2
    //     0xae84a8: movz            x1, #0x2
    // 0xae84ac: r0 = AllocateContext()
    //     0xae84ac: bl              #0xec126c  ; AllocateContextStub
    // 0xae84b0: ldur            x1, [fp, #-8]
    // 0xae84b4: stur            x0, [fp, #-0x18]
    // 0xae84b8: StoreField: r0->field_f = r1
    //     0xae84b8: stur            w1, [x0, #0xf]
    // 0xae84bc: r0 = controller()
    //     0xae84bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae84c0: LoadField: r1 = r0->field_3f
    //     0xae84c0: ldur            w1, [x0, #0x3f]
    // 0xae84c4: DecompressPointer r1
    //     0xae84c4: add             x1, x1, HEAP, lsl #32
    // 0xae84c8: cmp             w1, NULL
    // 0xae84cc: b.ne            #0xae84d8
    // 0xae84d0: r0 = Null
    //     0xae84d0: mov             x0, NULL
    // 0xae84d4: b               #0xae84ec
    // 0xae84d8: LoadField: r0 = r1->field_3b
    //     0xae84d8: ldur            w0, [x1, #0x3b]
    // 0xae84dc: DecompressPointer r0
    //     0xae84dc: add             x0, x0, HEAP, lsl #32
    // 0xae84e0: LoadField: r1 = r0->field_2b
    //     0xae84e0: ldur            w1, [x0, #0x2b]
    // 0xae84e4: DecompressPointer r1
    //     0xae84e4: add             x1, x1, HEAP, lsl #32
    // 0xae84e8: mov             x0, x1
    // 0xae84ec: cmp             w0, NULL
    // 0xae84f0: b.ne            #0xae84f8
    // 0xae84f4: r0 = false
    //     0xae84f4: add             x0, NULL, #0x30  ; false
    // 0xae84f8: stur            x0, [fp, #-8]
    // 0xae84fc: tbnz            w0, #4, #0xae8514
    // 0xae8500: r1 = Function '<anonymous closure>': static.
    //     0xae8500: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae8504: ldr             x1, [x1, #0xda0]
    // 0xae8508: r2 = Null
    //     0xae8508: mov             x2, NULL
    // 0xae850c: r0 = AllocateClosure()
    //     0xae850c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8510: b               #0xae8518
    // 0xae8514: r0 = Null
    //     0xae8514: mov             x0, NULL
    // 0xae8518: ldur            x2, [fp, #-0x18]
    // 0xae851c: ldur            x1, [fp, #-8]
    // 0xae8520: StoreField: r2->field_13 = r0
    //     0xae8520: stur            w0, [x2, #0x13]
    //     0xae8524: ldurb           w16, [x2, #-1]
    //     0xae8528: ldurb           w17, [x0, #-1]
    //     0xae852c: and             x16, x17, x16, lsr #2
    //     0xae8530: tst             x16, HEAP, lsr #32
    //     0xae8534: b.eq            #0xae853c
    //     0xae8538: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xae853c: r0 = NLabelTextField()
    //     0xae853c: bl              #0xa433d4  ; AllocateNLabelTextFieldStub -> NLabelTextField (size=0x1c)
    // 0xae8540: mov             x1, x0
    // 0xae8544: r0 = "Wilayah"
    //     0xae8544: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fff0] "Wilayah"
    //     0xae8548: ldr             x0, [x0, #0xff0]
    // 0xae854c: stur            x1, [fp, #-0x20]
    // 0xae8550: StoreField: r1->field_b = r0
    //     0xae8550: stur            w0, [x1, #0xb]
    // 0xae8554: ldur            x0, [fp, #-8]
    // 0xae8558: StoreField: r1->field_f = r0
    //     0xae8558: stur            w0, [x1, #0xf]
    // 0xae855c: r2 = Instance_EdgeInsets
    //     0xae855c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd80] Obj!EdgeInsets@e127f1
    //     0xae8560: ldr             x2, [x2, #0xd80]
    // 0xae8564: ArrayStore: r1[0] = r2  ; List_4
    //     0xae8564: stur            w2, [x1, #0x17]
    // 0xae8568: r0 = Obx()
    //     0xae8568: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae856c: ldur            x2, [fp, #-0x18]
    // 0xae8570: r1 = Function '<anonymous closure>':.
    //     0xae8570: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fff8] AnonymousClosure: (0xae9578), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] _KoinNuLocation::getFields (0xae8488)
    //     0xae8574: ldr             x1, [x1, #0xff8]
    // 0xae8578: stur            x0, [fp, #-0x28]
    // 0xae857c: r0 = AllocateClosure()
    //     0xae857c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8580: mov             x1, x0
    // 0xae8584: ldur            x0, [fp, #-0x28]
    // 0xae8588: StoreField: r0->field_b = r1
    //     0xae8588: stur            w1, [x0, #0xb]
    // 0xae858c: r1 = Null
    //     0xae858c: mov             x1, NULL
    // 0xae8590: r2 = 6
    //     0xae8590: movz            x2, #0x6
    // 0xae8594: r0 = AllocateArray()
    //     0xae8594: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae8598: mov             x2, x0
    // 0xae859c: ldur            x0, [fp, #-0x20]
    // 0xae85a0: stur            x2, [fp, #-0x30]
    // 0xae85a4: StoreField: r2->field_f = r0
    //     0xae85a4: stur            w0, [x2, #0xf]
    // 0xae85a8: ldur            x0, [fp, #-0x28]
    // 0xae85ac: StoreField: r2->field_13 = r0
    //     0xae85ac: stur            w0, [x2, #0x13]
    // 0xae85b0: r16 = Instance_SizedBox
    //     0xae85b0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae85b4: ldr             x16, [x16, #0xfe8]
    // 0xae85b8: ArrayStore: r2[0] = r16  ; List_4
    //     0xae85b8: stur            w16, [x2, #0x17]
    // 0xae85bc: r1 = <Widget>
    //     0xae85bc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae85c0: r0 = AllocateGrowableArray()
    //     0xae85c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae85c4: mov             x1, x0
    // 0xae85c8: ldur            x0, [fp, #-0x30]
    // 0xae85cc: stur            x1, [fp, #-0x20]
    // 0xae85d0: StoreField: r1->field_f = r0
    //     0xae85d0: stur            w0, [x1, #0xf]
    // 0xae85d4: r2 = 6
    //     0xae85d4: movz            x2, #0x6
    // 0xae85d8: StoreField: r1->field_b = r2
    //     0xae85d8: stur            w2, [x1, #0xb]
    // 0xae85dc: r0 = Column()
    //     0xae85dc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae85e0: mov             x1, x0
    // 0xae85e4: r0 = Instance_Axis
    //     0xae85e4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae85e8: stur            x1, [fp, #-0x28]
    // 0xae85ec: StoreField: r1->field_f = r0
    //     0xae85ec: stur            w0, [x1, #0xf]
    // 0xae85f0: r2 = Instance_MainAxisAlignment
    //     0xae85f0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae85f4: ldr             x2, [x2, #0x730]
    // 0xae85f8: StoreField: r1->field_13 = r2
    //     0xae85f8: stur            w2, [x1, #0x13]
    // 0xae85fc: r3 = Instance_MainAxisSize
    //     0xae85fc: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae8600: ldr             x3, [x3, #0x738]
    // 0xae8604: ArrayStore: r1[0] = r3  ; List_4
    //     0xae8604: stur            w3, [x1, #0x17]
    // 0xae8608: r4 = Instance_CrossAxisAlignment
    //     0xae8608: add             x4, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xae860c: ldr             x4, [x4, #0x68]
    // 0xae8610: StoreField: r1->field_1b = r4
    //     0xae8610: stur            w4, [x1, #0x1b]
    // 0xae8614: r5 = Instance_VerticalDirection
    //     0xae8614: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae8618: ldr             x5, [x5, #0x748]
    // 0xae861c: StoreField: r1->field_23 = r5
    //     0xae861c: stur            w5, [x1, #0x23]
    // 0xae8620: r6 = Instance_Clip
    //     0xae8620: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae8624: ldr             x6, [x6, #0x750]
    // 0xae8628: StoreField: r1->field_2b = r6
    //     0xae8628: stur            w6, [x1, #0x2b]
    // 0xae862c: StoreField: r1->field_2f = rZR
    //     0xae862c: stur            xzr, [x1, #0x2f]
    // 0xae8630: ldur            x7, [fp, #-0x20]
    // 0xae8634: StoreField: r1->field_b = r7
    //     0xae8634: stur            w7, [x1, #0xb]
    // 0xae8638: r0 = NLabelTextField()
    //     0xae8638: bl              #0xa433d4  ; AllocateNLabelTextFieldStub -> NLabelTextField (size=0x1c)
    // 0xae863c: mov             x1, x0
    // 0xae8640: r0 = "Cabang"
    //     0xae8640: add             x0, PP, #0x40, lsl #12  ; [pp+0x40000] "Cabang"
    //     0xae8644: ldr             x0, [x0]
    // 0xae8648: stur            x1, [fp, #-0x20]
    // 0xae864c: StoreField: r1->field_b = r0
    //     0xae864c: stur            w0, [x1, #0xb]
    // 0xae8650: ldur            x0, [fp, #-8]
    // 0xae8654: StoreField: r1->field_f = r0
    //     0xae8654: stur            w0, [x1, #0xf]
    // 0xae8658: r2 = Instance_EdgeInsets
    //     0xae8658: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd80] Obj!EdgeInsets@e127f1
    //     0xae865c: ldr             x2, [x2, #0xd80]
    // 0xae8660: ArrayStore: r1[0] = r2  ; List_4
    //     0xae8660: stur            w2, [x1, #0x17]
    // 0xae8664: r0 = Obx()
    //     0xae8664: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae8668: ldur            x2, [fp, #-0x18]
    // 0xae866c: r1 = Function '<anonymous closure>':.
    //     0xae866c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40008] AnonymousClosure: (0xae915c), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] _KoinNuLocation::getFields (0xae8488)
    //     0xae8670: ldr             x1, [x1, #8]
    // 0xae8674: stur            x0, [fp, #-0x30]
    // 0xae8678: r0 = AllocateClosure()
    //     0xae8678: bl              #0xec1630  ; AllocateClosureStub
    // 0xae867c: mov             x1, x0
    // 0xae8680: ldur            x0, [fp, #-0x30]
    // 0xae8684: StoreField: r0->field_b = r1
    //     0xae8684: stur            w1, [x0, #0xb]
    // 0xae8688: r1 = Null
    //     0xae8688: mov             x1, NULL
    // 0xae868c: r2 = 6
    //     0xae868c: movz            x2, #0x6
    // 0xae8690: r0 = AllocateArray()
    //     0xae8690: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae8694: mov             x2, x0
    // 0xae8698: ldur            x0, [fp, #-0x20]
    // 0xae869c: stur            x2, [fp, #-0x38]
    // 0xae86a0: StoreField: r2->field_f = r0
    //     0xae86a0: stur            w0, [x2, #0xf]
    // 0xae86a4: ldur            x0, [fp, #-0x30]
    // 0xae86a8: StoreField: r2->field_13 = r0
    //     0xae86a8: stur            w0, [x2, #0x13]
    // 0xae86ac: r16 = Instance_SizedBox
    //     0xae86ac: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae86b0: ldr             x16, [x16, #0xfe8]
    // 0xae86b4: ArrayStore: r2[0] = r16  ; List_4
    //     0xae86b4: stur            w16, [x2, #0x17]
    // 0xae86b8: r1 = <Widget>
    //     0xae86b8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae86bc: r0 = AllocateGrowableArray()
    //     0xae86bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae86c0: mov             x1, x0
    // 0xae86c4: ldur            x0, [fp, #-0x38]
    // 0xae86c8: stur            x1, [fp, #-0x20]
    // 0xae86cc: StoreField: r1->field_f = r0
    //     0xae86cc: stur            w0, [x1, #0xf]
    // 0xae86d0: r2 = 6
    //     0xae86d0: movz            x2, #0x6
    // 0xae86d4: StoreField: r1->field_b = r2
    //     0xae86d4: stur            w2, [x1, #0xb]
    // 0xae86d8: r0 = Column()
    //     0xae86d8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae86dc: mov             x1, x0
    // 0xae86e0: r0 = Instance_Axis
    //     0xae86e0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae86e4: stur            x1, [fp, #-0x30]
    // 0xae86e8: StoreField: r1->field_f = r0
    //     0xae86e8: stur            w0, [x1, #0xf]
    // 0xae86ec: r2 = Instance_MainAxisAlignment
    //     0xae86ec: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae86f0: ldr             x2, [x2, #0x730]
    // 0xae86f4: StoreField: r1->field_13 = r2
    //     0xae86f4: stur            w2, [x1, #0x13]
    // 0xae86f8: r3 = Instance_MainAxisSize
    //     0xae86f8: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae86fc: ldr             x3, [x3, #0x738]
    // 0xae8700: ArrayStore: r1[0] = r3  ; List_4
    //     0xae8700: stur            w3, [x1, #0x17]
    // 0xae8704: r4 = Instance_CrossAxisAlignment
    //     0xae8704: add             x4, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xae8708: ldr             x4, [x4, #0x68]
    // 0xae870c: StoreField: r1->field_1b = r4
    //     0xae870c: stur            w4, [x1, #0x1b]
    // 0xae8710: r5 = Instance_VerticalDirection
    //     0xae8710: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae8714: ldr             x5, [x5, #0x748]
    // 0xae8718: StoreField: r1->field_23 = r5
    //     0xae8718: stur            w5, [x1, #0x23]
    // 0xae871c: r6 = Instance_Clip
    //     0xae871c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae8720: ldr             x6, [x6, #0x750]
    // 0xae8724: StoreField: r1->field_2b = r6
    //     0xae8724: stur            w6, [x1, #0x2b]
    // 0xae8728: StoreField: r1->field_2f = rZR
    //     0xae8728: stur            xzr, [x1, #0x2f]
    // 0xae872c: ldur            x7, [fp, #-0x20]
    // 0xae8730: StoreField: r1->field_b = r7
    //     0xae8730: stur            w7, [x1, #0xb]
    // 0xae8734: r0 = NLabelTextField()
    //     0xae8734: bl              #0xa433d4  ; AllocateNLabelTextFieldStub -> NLabelTextField (size=0x1c)
    // 0xae8738: mov             x1, x0
    // 0xae873c: r0 = "Majelis Wakil Cabang (MWC)"
    //     0xae873c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40010] "Majelis Wakil Cabang (MWC)"
    //     0xae8740: ldr             x0, [x0, #0x10]
    // 0xae8744: stur            x1, [fp, #-0x20]
    // 0xae8748: StoreField: r1->field_b = r0
    //     0xae8748: stur            w0, [x1, #0xb]
    // 0xae874c: ldur            x0, [fp, #-8]
    // 0xae8750: StoreField: r1->field_f = r0
    //     0xae8750: stur            w0, [x1, #0xf]
    // 0xae8754: r2 = Instance_EdgeInsets
    //     0xae8754: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd80] Obj!EdgeInsets@e127f1
    //     0xae8758: ldr             x2, [x2, #0xd80]
    // 0xae875c: ArrayStore: r1[0] = r2  ; List_4
    //     0xae875c: stur            w2, [x1, #0x17]
    // 0xae8760: r0 = Obx()
    //     0xae8760: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae8764: ldur            x2, [fp, #-0x18]
    // 0xae8768: r1 = Function '<anonymous closure>':.
    //     0xae8768: add             x1, PP, #0x40, lsl #12  ; [pp+0x40018] AnonymousClosure: (0xae8d68), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] _KoinNuLocation::getFields (0xae8488)
    //     0xae876c: ldr             x1, [x1, #0x18]
    // 0xae8770: stur            x0, [fp, #-0x38]
    // 0xae8774: r0 = AllocateClosure()
    //     0xae8774: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8778: mov             x1, x0
    // 0xae877c: ldur            x0, [fp, #-0x38]
    // 0xae8780: StoreField: r0->field_b = r1
    //     0xae8780: stur            w1, [x0, #0xb]
    // 0xae8784: r1 = Null
    //     0xae8784: mov             x1, NULL
    // 0xae8788: r2 = 6
    //     0xae8788: movz            x2, #0x6
    // 0xae878c: r0 = AllocateArray()
    //     0xae878c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae8790: mov             x2, x0
    // 0xae8794: ldur            x0, [fp, #-0x20]
    // 0xae8798: stur            x2, [fp, #-0x40]
    // 0xae879c: StoreField: r2->field_f = r0
    //     0xae879c: stur            w0, [x2, #0xf]
    // 0xae87a0: ldur            x0, [fp, #-0x38]
    // 0xae87a4: StoreField: r2->field_13 = r0
    //     0xae87a4: stur            w0, [x2, #0x13]
    // 0xae87a8: r16 = Instance_SizedBox
    //     0xae87a8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae87ac: ldr             x16, [x16, #0xfe8]
    // 0xae87b0: ArrayStore: r2[0] = r16  ; List_4
    //     0xae87b0: stur            w16, [x2, #0x17]
    // 0xae87b4: r1 = <Widget>
    //     0xae87b4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae87b8: r0 = AllocateGrowableArray()
    //     0xae87b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae87bc: mov             x1, x0
    // 0xae87c0: ldur            x0, [fp, #-0x40]
    // 0xae87c4: stur            x1, [fp, #-0x20]
    // 0xae87c8: StoreField: r1->field_f = r0
    //     0xae87c8: stur            w0, [x1, #0xf]
    // 0xae87cc: r2 = 6
    //     0xae87cc: movz            x2, #0x6
    // 0xae87d0: StoreField: r1->field_b = r2
    //     0xae87d0: stur            w2, [x1, #0xb]
    // 0xae87d4: r0 = Column()
    //     0xae87d4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae87d8: mov             x1, x0
    // 0xae87dc: r0 = Instance_Axis
    //     0xae87dc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae87e0: stur            x1, [fp, #-0x38]
    // 0xae87e4: StoreField: r1->field_f = r0
    //     0xae87e4: stur            w0, [x1, #0xf]
    // 0xae87e8: r2 = Instance_MainAxisAlignment
    //     0xae87e8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae87ec: ldr             x2, [x2, #0x730]
    // 0xae87f0: StoreField: r1->field_13 = r2
    //     0xae87f0: stur            w2, [x1, #0x13]
    // 0xae87f4: r3 = Instance_MainAxisSize
    //     0xae87f4: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae87f8: ldr             x3, [x3, #0x738]
    // 0xae87fc: ArrayStore: r1[0] = r3  ; List_4
    //     0xae87fc: stur            w3, [x1, #0x17]
    // 0xae8800: r4 = Instance_CrossAxisAlignment
    //     0xae8800: add             x4, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xae8804: ldr             x4, [x4, #0x68]
    // 0xae8808: StoreField: r1->field_1b = r4
    //     0xae8808: stur            w4, [x1, #0x1b]
    // 0xae880c: r5 = Instance_VerticalDirection
    //     0xae880c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae8810: ldr             x5, [x5, #0x748]
    // 0xae8814: StoreField: r1->field_23 = r5
    //     0xae8814: stur            w5, [x1, #0x23]
    // 0xae8818: r6 = Instance_Clip
    //     0xae8818: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae881c: ldr             x6, [x6, #0x750]
    // 0xae8820: StoreField: r1->field_2b = r6
    //     0xae8820: stur            w6, [x1, #0x2b]
    // 0xae8824: StoreField: r1->field_2f = rZR
    //     0xae8824: stur            xzr, [x1, #0x2f]
    // 0xae8828: ldur            x7, [fp, #-0x20]
    // 0xae882c: StoreField: r1->field_b = r7
    //     0xae882c: stur            w7, [x1, #0xb]
    // 0xae8830: r0 = NLabelTextField()
    //     0xae8830: bl              #0xa433d4  ; AllocateNLabelTextFieldStub -> NLabelTextField (size=0x1c)
    // 0xae8834: mov             x1, x0
    // 0xae8838: r0 = "Ranting"
    //     0xae8838: add             x0, PP, #0x40, lsl #12  ; [pp+0x40020] "Ranting"
    //     0xae883c: ldr             x0, [x0, #0x20]
    // 0xae8840: stur            x1, [fp, #-0x20]
    // 0xae8844: StoreField: r1->field_b = r0
    //     0xae8844: stur            w0, [x1, #0xb]
    // 0xae8848: ldur            x0, [fp, #-8]
    // 0xae884c: StoreField: r1->field_f = r0
    //     0xae884c: stur            w0, [x1, #0xf]
    // 0xae8850: r0 = Instance_EdgeInsets
    //     0xae8850: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd80] Obj!EdgeInsets@e127f1
    //     0xae8854: ldr             x0, [x0, #0xd80]
    // 0xae8858: ArrayStore: r1[0] = r0  ; List_4
    //     0xae8858: stur            w0, [x1, #0x17]
    // 0xae885c: r0 = Obx()
    //     0xae885c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae8860: ldur            x2, [fp, #-0x18]
    // 0xae8864: r1 = Function '<anonymous closure>':.
    //     0xae8864: add             x1, PP, #0x40, lsl #12  ; [pp+0x40028] AnonymousClosure: (0xae89a0), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] _KoinNuLocation::getFields (0xae8488)
    //     0xae8868: ldr             x1, [x1, #0x28]
    // 0xae886c: stur            x0, [fp, #-8]
    // 0xae8870: r0 = AllocateClosure()
    //     0xae8870: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8874: mov             x1, x0
    // 0xae8878: ldur            x0, [fp, #-8]
    // 0xae887c: StoreField: r0->field_b = r1
    //     0xae887c: stur            w1, [x0, #0xb]
    // 0xae8880: r1 = Null
    //     0xae8880: mov             x1, NULL
    // 0xae8884: r2 = 6
    //     0xae8884: movz            x2, #0x6
    // 0xae8888: r0 = AllocateArray()
    //     0xae8888: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae888c: mov             x2, x0
    // 0xae8890: ldur            x0, [fp, #-0x20]
    // 0xae8894: stur            x2, [fp, #-0x18]
    // 0xae8898: StoreField: r2->field_f = r0
    //     0xae8898: stur            w0, [x2, #0xf]
    // 0xae889c: ldur            x0, [fp, #-8]
    // 0xae88a0: StoreField: r2->field_13 = r0
    //     0xae88a0: stur            w0, [x2, #0x13]
    // 0xae88a4: r16 = Instance_SizedBox
    //     0xae88a4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xae88a8: ldr             x16, [x16, #0xfe8]
    // 0xae88ac: ArrayStore: r2[0] = r16  ; List_4
    //     0xae88ac: stur            w16, [x2, #0x17]
    // 0xae88b0: r1 = <Widget>
    //     0xae88b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae88b4: r0 = AllocateGrowableArray()
    //     0xae88b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae88b8: mov             x1, x0
    // 0xae88bc: ldur            x0, [fp, #-0x18]
    // 0xae88c0: stur            x1, [fp, #-8]
    // 0xae88c4: StoreField: r1->field_f = r0
    //     0xae88c4: stur            w0, [x1, #0xf]
    // 0xae88c8: r0 = 6
    //     0xae88c8: movz            x0, #0x6
    // 0xae88cc: StoreField: r1->field_b = r0
    //     0xae88cc: stur            w0, [x1, #0xb]
    // 0xae88d0: r0 = Column()
    //     0xae88d0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae88d4: mov             x3, x0
    // 0xae88d8: r0 = Instance_Axis
    //     0xae88d8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae88dc: stur            x3, [fp, #-0x18]
    // 0xae88e0: StoreField: r3->field_f = r0
    //     0xae88e0: stur            w0, [x3, #0xf]
    // 0xae88e4: r0 = Instance_MainAxisAlignment
    //     0xae88e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae88e8: ldr             x0, [x0, #0x730]
    // 0xae88ec: StoreField: r3->field_13 = r0
    //     0xae88ec: stur            w0, [x3, #0x13]
    // 0xae88f0: r0 = Instance_MainAxisSize
    //     0xae88f0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae88f4: ldr             x0, [x0, #0x738]
    // 0xae88f8: ArrayStore: r3[0] = r0  ; List_4
    //     0xae88f8: stur            w0, [x3, #0x17]
    // 0xae88fc: r0 = Instance_CrossAxisAlignment
    //     0xae88fc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xae8900: ldr             x0, [x0, #0x68]
    // 0xae8904: StoreField: r3->field_1b = r0
    //     0xae8904: stur            w0, [x3, #0x1b]
    // 0xae8908: r0 = Instance_VerticalDirection
    //     0xae8908: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae890c: ldr             x0, [x0, #0x748]
    // 0xae8910: StoreField: r3->field_23 = r0
    //     0xae8910: stur            w0, [x3, #0x23]
    // 0xae8914: r0 = Instance_Clip
    //     0xae8914: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae8918: ldr             x0, [x0, #0x750]
    // 0xae891c: StoreField: r3->field_2b = r0
    //     0xae891c: stur            w0, [x3, #0x2b]
    // 0xae8920: StoreField: r3->field_2f = rZR
    //     0xae8920: stur            xzr, [x3, #0x2f]
    // 0xae8924: ldur            x0, [fp, #-8]
    // 0xae8928: StoreField: r3->field_b = r0
    //     0xae8928: stur            w0, [x3, #0xb]
    // 0xae892c: r1 = Null
    //     0xae892c: mov             x1, NULL
    // 0xae8930: r2 = 8
    //     0xae8930: movz            x2, #0x8
    // 0xae8934: r0 = AllocateArray()
    //     0xae8934: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae8938: mov             x2, x0
    // 0xae893c: ldur            x0, [fp, #-0x28]
    // 0xae8940: stur            x2, [fp, #-8]
    // 0xae8944: StoreField: r2->field_f = r0
    //     0xae8944: stur            w0, [x2, #0xf]
    // 0xae8948: ldur            x0, [fp, #-0x30]
    // 0xae894c: StoreField: r2->field_13 = r0
    //     0xae894c: stur            w0, [x2, #0x13]
    // 0xae8950: ldur            x0, [fp, #-0x38]
    // 0xae8954: ArrayStore: r2[0] = r0  ; List_4
    //     0xae8954: stur            w0, [x2, #0x17]
    // 0xae8958: ldur            x0, [fp, #-0x18]
    // 0xae895c: StoreField: r2->field_1b = r0
    //     0xae895c: stur            w0, [x2, #0x1b]
    // 0xae8960: r1 = <Column>
    //     0xae8960: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b348] TypeArguments: <Column>
    //     0xae8964: ldr             x1, [x1, #0x348]
    // 0xae8968: r0 = AllocateGrowableArray()
    //     0xae8968: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae896c: mov             x1, x0
    // 0xae8970: ldur            x0, [fp, #-8]
    // 0xae8974: StoreField: r1->field_f = r0
    //     0xae8974: stur            w0, [x1, #0xf]
    // 0xae8978: r0 = 8
    //     0xae8978: movz            x0, #0x8
    // 0xae897c: StoreField: r1->field_b = r0
    //     0xae897c: stur            w0, [x1, #0xb]
    // 0xae8980: ldur            x2, [fp, #-0x10]
    // 0xae8984: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae8984: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae8988: r0 = sublist()
    //     0xae8988: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xae898c: LeaveFrame
    //     0xae898c: mov             SP, fp
    //     0xae8990: ldp             fp, lr, [SP], #0x10
    // 0xae8994: ret
    //     0xae8994: ret             
    // 0xae8998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8998: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae899c: b               #0xae84a8
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xae89a0, size: 0x1d4
    // 0xae89a0: EnterFrame
    //     0xae89a0: stp             fp, lr, [SP, #-0x10]!
    //     0xae89a4: mov             fp, SP
    // 0xae89a8: AllocStack(0x48)
    //     0xae89a8: sub             SP, SP, #0x48
    // 0xae89ac: SetupParameters()
    //     0xae89ac: ldr             x0, [fp, #0x10]
    //     0xae89b0: ldur            w2, [x0, #0x17]
    //     0xae89b4: add             x2, x2, HEAP, lsl #32
    //     0xae89b8: stur            x2, [fp, #-8]
    // 0xae89bc: CheckStackOverflow
    //     0xae89bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae89c0: cmp             SP, x16
    //     0xae89c4: b.ls            #0xae8b6c
    // 0xae89c8: LoadField: r1 = r2->field_f
    //     0xae89c8: ldur            w1, [x2, #0xf]
    // 0xae89cc: DecompressPointer r1
    //     0xae89cc: add             x1, x1, HEAP, lsl #32
    // 0xae89d0: r0 = controller()
    //     0xae89d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae89d4: LoadField: r2 = r0->field_5f
    //     0xae89d4: ldur            w2, [x0, #0x5f]
    // 0xae89d8: DecompressPointer r2
    //     0xae89d8: add             x2, x2, HEAP, lsl #32
    // 0xae89dc: ldur            x0, [fp, #-8]
    // 0xae89e0: stur            x2, [fp, #-0x10]
    // 0xae89e4: LoadField: r1 = r0->field_f
    //     0xae89e4: ldur            w1, [x0, #0xf]
    // 0xae89e8: DecompressPointer r1
    //     0xae89e8: add             x1, x1, HEAP, lsl #32
    // 0xae89ec: r0 = controller()
    //     0xae89ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae89f0: LoadField: r1 = r0->field_4f
    //     0xae89f0: ldur            w1, [x0, #0x4f]
    // 0xae89f4: DecompressPointer r1
    //     0xae89f4: add             x1, x1, HEAP, lsl #32
    // 0xae89f8: r0 = value()
    //     0xae89f8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae89fc: cmp             w0, NULL
    // 0xae8a00: b.ne            #0xae8a0c
    // 0xae8a04: r0 = Null
    //     0xae8a04: mov             x0, NULL
    // 0xae8a08: b               #0xae8a18
    // 0xae8a0c: LoadField: r1 = r0->field_f
    //     0xae8a0c: ldur            w1, [x0, #0xf]
    // 0xae8a10: DecompressPointer r1
    //     0xae8a10: add             x1, x1, HEAP, lsl #32
    // 0xae8a14: mov             x0, x1
    // 0xae8a18: cmp             w0, NULL
    // 0xae8a1c: b.ne            #0xae8a28
    // 0xae8a20: r0 = "Pilih Ranting"
    //     0xae8a20: add             x0, PP, #0x40, lsl #12  ; [pp+0x40030] "Pilih Ranting"
    //     0xae8a24: ldr             x0, [x0, #0x30]
    // 0xae8a28: stur            x0, [fp, #-0x18]
    // 0xae8a2c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8a2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8a30: ldr             x0, [x0, #0x2670]
    //     0xae8a34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae8a38: cmp             w0, w16
    //     0xae8a3c: b.ne            #0xae8a48
    //     0xae8a40: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae8a44: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae8a48: r0 = GetNavigation.textTheme()
    //     0xae8a48: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae8a4c: LoadField: r3 = r0->field_2f
    //     0xae8a4c: ldur            w3, [x0, #0x2f]
    // 0xae8a50: DecompressPointer r3
    //     0xae8a50: add             x3, x3, HEAP, lsl #32
    // 0xae8a54: stur            x3, [fp, #-0x20]
    // 0xae8a58: cmp             w3, NULL
    // 0xae8a5c: b.ne            #0xae8a68
    // 0xae8a60: r2 = Null
    //     0xae8a60: mov             x2, NULL
    // 0xae8a64: b               #0xae8aac
    // 0xae8a68: r1 = _ConstMap len:3
    //     0xae8a68: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xae8a6c: ldr             x1, [x1, #0xbe8]
    // 0xae8a70: r2 = 6
    //     0xae8a70: movz            x2, #0x6
    // 0xae8a74: r0 = []()
    //     0xae8a74: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae8a78: r16 = <Color?>
    //     0xae8a78: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae8a7c: ldr             x16, [x16, #0x98]
    // 0xae8a80: r30 = Instance_Color
    //     0xae8a80: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae8a84: stp             lr, x16, [SP, #8]
    // 0xae8a88: str             x0, [SP]
    // 0xae8a8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae8a8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae8a90: r0 = mode()
    //     0xae8a90: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae8a94: str             x0, [SP]
    // 0xae8a98: ldur            x1, [fp, #-0x20]
    // 0xae8a9c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xae8a9c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xae8aa0: ldr             x4, [x4, #0x228]
    // 0xae8aa4: r0 = copyWith()
    //     0xae8aa4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae8aa8: mov             x2, x0
    // 0xae8aac: ldur            x1, [fp, #-8]
    // 0xae8ab0: ldur            x0, [fp, #-0x18]
    // 0xae8ab4: stur            x2, [fp, #-0x20]
    // 0xae8ab8: r0 = InputDecoration()
    //     0xae8ab8: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xae8abc: mov             x2, x0
    // 0xae8ac0: ldur            x0, [fp, #-0x18]
    // 0xae8ac4: stur            x2, [fp, #-0x28]
    // 0xae8ac8: StoreField: r2->field_2f = r0
    //     0xae8ac8: stur            w0, [x2, #0x2f]
    // 0xae8acc: ldur            x0, [fp, #-0x20]
    // 0xae8ad0: StoreField: r2->field_33 = r0
    //     0xae8ad0: stur            w0, [x2, #0x33]
    // 0xae8ad4: r0 = true
    //     0xae8ad4: add             x0, NULL, #0x20  ; true
    // 0xae8ad8: StoreField: r2->field_43 = r0
    //     0xae8ad8: stur            w0, [x2, #0x43]
    // 0xae8adc: r1 = Instance_Icon
    //     0xae8adc: add             x1, PP, #0x35, lsl #12  ; [pp+0x352b8] Obj!Icon@e24571
    //     0xae8ae0: ldr             x1, [x1, #0x2b8]
    // 0xae8ae4: StoreField: r2->field_83 = r1
    //     0xae8ae4: stur            w1, [x2, #0x83]
    // 0xae8ae8: StoreField: r2->field_cf = r0
    //     0xae8ae8: stur            w0, [x2, #0xcf]
    // 0xae8aec: ldur            x0, [fp, #-8]
    // 0xae8af0: LoadField: r1 = r0->field_f
    //     0xae8af0: ldur            w1, [x0, #0xf]
    // 0xae8af4: DecompressPointer r1
    //     0xae8af4: add             x1, x1, HEAP, lsl #32
    // 0xae8af8: r0 = controller()
    //     0xae8af8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae8afc: mov             x1, x0
    // 0xae8b00: ldur            x0, [fp, #-8]
    // 0xae8b04: LoadField: r3 = r0->field_13
    //     0xae8b04: ldur            w3, [x0, #0x13]
    // 0xae8b08: DecompressPointer r3
    //     0xae8b08: add             x3, x3, HEAP, lsl #32
    // 0xae8b0c: mov             x2, x1
    // 0xae8b10: stur            x3, [fp, #-0x18]
    // 0xae8b14: r1 = Function 'selectLocality':.
    //     0xae8b14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40038] AnonymousClosure: (0xae8b74), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectLocality (0xae8bac)
    //     0xae8b18: ldr             x1, [x1, #0x38]
    // 0xae8b1c: r0 = AllocateClosure()
    //     0xae8b1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8b20: r1 = <String>
    //     0xae8b20: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae8b24: stur            x0, [fp, #-8]
    // 0xae8b28: r0 = TextFormField()
    //     0xae8b28: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae8b2c: stur            x0, [fp, #-0x20]
    // 0xae8b30: r16 = true
    //     0xae8b30: add             x16, NULL, #0x20  ; true
    // 0xae8b34: ldur            lr, [fp, #-0x10]
    // 0xae8b38: stp             lr, x16, [SP, #0x10]
    // 0xae8b3c: ldur            x16, [fp, #-8]
    // 0xae8b40: ldur            lr, [fp, #-0x18]
    // 0xae8b44: stp             lr, x16, [SP]
    // 0xae8b48: mov             x1, x0
    // 0xae8b4c: ldur            x2, [fp, #-0x28]
    // 0xae8b50: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x3, onTap, 0x4, readOnly, 0x2, validator, 0x5, null]
    //     0xae8b50: add             x4, PP, #0x40, lsl #12  ; [pp+0x40040] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x3, "onTap", 0x4, "readOnly", 0x2, "validator", 0x5, Null]
    //     0xae8b54: ldr             x4, [x4, #0x40]
    // 0xae8b58: r0 = TextFormField()
    //     0xae8b58: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae8b5c: ldur            x0, [fp, #-0x20]
    // 0xae8b60: LeaveFrame
    //     0xae8b60: mov             SP, fp
    //     0xae8b64: ldp             fp, lr, [SP], #0x10
    // 0xae8b68: ret
    //     0xae8b68: ret             
    // 0xae8b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8b6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8b70: b               #0xae89c8
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xae8d68, size: 0x1d4
    // 0xae8d68: EnterFrame
    //     0xae8d68: stp             fp, lr, [SP, #-0x10]!
    //     0xae8d6c: mov             fp, SP
    // 0xae8d70: AllocStack(0x48)
    //     0xae8d70: sub             SP, SP, #0x48
    // 0xae8d74: SetupParameters()
    //     0xae8d74: ldr             x0, [fp, #0x10]
    //     0xae8d78: ldur            w2, [x0, #0x17]
    //     0xae8d7c: add             x2, x2, HEAP, lsl #32
    //     0xae8d80: stur            x2, [fp, #-8]
    // 0xae8d84: CheckStackOverflow
    //     0xae8d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8d88: cmp             SP, x16
    //     0xae8d8c: b.ls            #0xae8f34
    // 0xae8d90: LoadField: r1 = r2->field_f
    //     0xae8d90: ldur            w1, [x2, #0xf]
    // 0xae8d94: DecompressPointer r1
    //     0xae8d94: add             x1, x1, HEAP, lsl #32
    // 0xae8d98: r0 = controller()
    //     0xae8d98: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae8d9c: LoadField: r2 = r0->field_5b
    //     0xae8d9c: ldur            w2, [x0, #0x5b]
    // 0xae8da0: DecompressPointer r2
    //     0xae8da0: add             x2, x2, HEAP, lsl #32
    // 0xae8da4: ldur            x0, [fp, #-8]
    // 0xae8da8: stur            x2, [fp, #-0x10]
    // 0xae8dac: LoadField: r1 = r0->field_f
    //     0xae8dac: ldur            w1, [x0, #0xf]
    // 0xae8db0: DecompressPointer r1
    //     0xae8db0: add             x1, x1, HEAP, lsl #32
    // 0xae8db4: r0 = controller()
    //     0xae8db4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae8db8: LoadField: r1 = r0->field_4b
    //     0xae8db8: ldur            w1, [x0, #0x4b]
    // 0xae8dbc: DecompressPointer r1
    //     0xae8dbc: add             x1, x1, HEAP, lsl #32
    // 0xae8dc0: r0 = value()
    //     0xae8dc0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae8dc4: cmp             w0, NULL
    // 0xae8dc8: b.ne            #0xae8dd4
    // 0xae8dcc: r0 = Null
    //     0xae8dcc: mov             x0, NULL
    // 0xae8dd0: b               #0xae8de0
    // 0xae8dd4: LoadField: r1 = r0->field_f
    //     0xae8dd4: ldur            w1, [x0, #0xf]
    // 0xae8dd8: DecompressPointer r1
    //     0xae8dd8: add             x1, x1, HEAP, lsl #32
    // 0xae8ddc: mov             x0, x1
    // 0xae8de0: cmp             w0, NULL
    // 0xae8de4: b.ne            #0xae8df0
    // 0xae8de8: r0 = "Pilih MWC"
    //     0xae8de8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40050] "Pilih MWC"
    //     0xae8dec: ldr             x0, [x0, #0x50]
    // 0xae8df0: stur            x0, [fp, #-0x18]
    // 0xae8df4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8df4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8df8: ldr             x0, [x0, #0x2670]
    //     0xae8dfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae8e00: cmp             w0, w16
    //     0xae8e04: b.ne            #0xae8e10
    //     0xae8e08: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae8e0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae8e10: r0 = GetNavigation.textTheme()
    //     0xae8e10: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae8e14: LoadField: r3 = r0->field_2f
    //     0xae8e14: ldur            w3, [x0, #0x2f]
    // 0xae8e18: DecompressPointer r3
    //     0xae8e18: add             x3, x3, HEAP, lsl #32
    // 0xae8e1c: stur            x3, [fp, #-0x20]
    // 0xae8e20: cmp             w3, NULL
    // 0xae8e24: b.ne            #0xae8e30
    // 0xae8e28: r2 = Null
    //     0xae8e28: mov             x2, NULL
    // 0xae8e2c: b               #0xae8e74
    // 0xae8e30: r1 = _ConstMap len:3
    //     0xae8e30: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xae8e34: ldr             x1, [x1, #0xbe8]
    // 0xae8e38: r2 = 6
    //     0xae8e38: movz            x2, #0x6
    // 0xae8e3c: r0 = []()
    //     0xae8e3c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae8e40: r16 = <Color?>
    //     0xae8e40: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae8e44: ldr             x16, [x16, #0x98]
    // 0xae8e48: r30 = Instance_Color
    //     0xae8e48: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae8e4c: stp             lr, x16, [SP, #8]
    // 0xae8e50: str             x0, [SP]
    // 0xae8e54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae8e54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae8e58: r0 = mode()
    //     0xae8e58: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae8e5c: str             x0, [SP]
    // 0xae8e60: ldur            x1, [fp, #-0x20]
    // 0xae8e64: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xae8e64: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xae8e68: ldr             x4, [x4, #0x228]
    // 0xae8e6c: r0 = copyWith()
    //     0xae8e6c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae8e70: mov             x2, x0
    // 0xae8e74: ldur            x1, [fp, #-8]
    // 0xae8e78: ldur            x0, [fp, #-0x18]
    // 0xae8e7c: stur            x2, [fp, #-0x20]
    // 0xae8e80: r0 = InputDecoration()
    //     0xae8e80: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xae8e84: mov             x2, x0
    // 0xae8e88: ldur            x0, [fp, #-0x18]
    // 0xae8e8c: stur            x2, [fp, #-0x28]
    // 0xae8e90: StoreField: r2->field_2f = r0
    //     0xae8e90: stur            w0, [x2, #0x2f]
    // 0xae8e94: ldur            x0, [fp, #-0x20]
    // 0xae8e98: StoreField: r2->field_33 = r0
    //     0xae8e98: stur            w0, [x2, #0x33]
    // 0xae8e9c: r0 = true
    //     0xae8e9c: add             x0, NULL, #0x20  ; true
    // 0xae8ea0: StoreField: r2->field_43 = r0
    //     0xae8ea0: stur            w0, [x2, #0x43]
    // 0xae8ea4: r1 = Instance_Icon
    //     0xae8ea4: add             x1, PP, #0x35, lsl #12  ; [pp+0x352b8] Obj!Icon@e24571
    //     0xae8ea8: ldr             x1, [x1, #0x2b8]
    // 0xae8eac: StoreField: r2->field_83 = r1
    //     0xae8eac: stur            w1, [x2, #0x83]
    // 0xae8eb0: StoreField: r2->field_cf = r0
    //     0xae8eb0: stur            w0, [x2, #0xcf]
    // 0xae8eb4: ldur            x0, [fp, #-8]
    // 0xae8eb8: LoadField: r1 = r0->field_f
    //     0xae8eb8: ldur            w1, [x0, #0xf]
    // 0xae8ebc: DecompressPointer r1
    //     0xae8ebc: add             x1, x1, HEAP, lsl #32
    // 0xae8ec0: r0 = controller()
    //     0xae8ec0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae8ec4: mov             x1, x0
    // 0xae8ec8: ldur            x0, [fp, #-8]
    // 0xae8ecc: LoadField: r3 = r0->field_13
    //     0xae8ecc: ldur            w3, [x0, #0x13]
    // 0xae8ed0: DecompressPointer r3
    //     0xae8ed0: add             x3, x3, HEAP, lsl #32
    // 0xae8ed4: mov             x2, x1
    // 0xae8ed8: stur            x3, [fp, #-0x18]
    // 0xae8edc: r1 = Function 'selectDistrict':.
    //     0xae8edc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40058] AnonymousClosure: (0xae8f3c), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectDistrict (0xae8f74)
    //     0xae8ee0: ldr             x1, [x1, #0x58]
    // 0xae8ee4: r0 = AllocateClosure()
    //     0xae8ee4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8ee8: r1 = <String>
    //     0xae8ee8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae8eec: stur            x0, [fp, #-8]
    // 0xae8ef0: r0 = TextFormField()
    //     0xae8ef0: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae8ef4: stur            x0, [fp, #-0x20]
    // 0xae8ef8: r16 = true
    //     0xae8ef8: add             x16, NULL, #0x20  ; true
    // 0xae8efc: ldur            lr, [fp, #-0x10]
    // 0xae8f00: stp             lr, x16, [SP, #0x10]
    // 0xae8f04: ldur            x16, [fp, #-8]
    // 0xae8f08: ldur            lr, [fp, #-0x18]
    // 0xae8f0c: stp             lr, x16, [SP]
    // 0xae8f10: mov             x1, x0
    // 0xae8f14: ldur            x2, [fp, #-0x28]
    // 0xae8f18: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x3, onTap, 0x4, readOnly, 0x2, validator, 0x5, null]
    //     0xae8f18: add             x4, PP, #0x40, lsl #12  ; [pp+0x40040] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x3, "onTap", 0x4, "readOnly", 0x2, "validator", 0x5, Null]
    //     0xae8f1c: ldr             x4, [x4, #0x40]
    // 0xae8f20: r0 = TextFormField()
    //     0xae8f20: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae8f24: ldur            x0, [fp, #-0x20]
    // 0xae8f28: LeaveFrame
    //     0xae8f28: mov             SP, fp
    //     0xae8f2c: ldp             fp, lr, [SP], #0x10
    // 0xae8f30: ret
    //     0xae8f30: ret             
    // 0xae8f34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8f34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8f38: b               #0xae8d90
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xae915c, size: 0x1d4
    // 0xae915c: EnterFrame
    //     0xae915c: stp             fp, lr, [SP, #-0x10]!
    //     0xae9160: mov             fp, SP
    // 0xae9164: AllocStack(0x48)
    //     0xae9164: sub             SP, SP, #0x48
    // 0xae9168: SetupParameters()
    //     0xae9168: ldr             x0, [fp, #0x10]
    //     0xae916c: ldur            w2, [x0, #0x17]
    //     0xae9170: add             x2, x2, HEAP, lsl #32
    //     0xae9174: stur            x2, [fp, #-8]
    // 0xae9178: CheckStackOverflow
    //     0xae9178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae917c: cmp             SP, x16
    //     0xae9180: b.ls            #0xae9328
    // 0xae9184: LoadField: r1 = r2->field_f
    //     0xae9184: ldur            w1, [x2, #0xf]
    // 0xae9188: DecompressPointer r1
    //     0xae9188: add             x1, x1, HEAP, lsl #32
    // 0xae918c: r0 = controller()
    //     0xae918c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9190: LoadField: r2 = r0->field_57
    //     0xae9190: ldur            w2, [x0, #0x57]
    // 0xae9194: DecompressPointer r2
    //     0xae9194: add             x2, x2, HEAP, lsl #32
    // 0xae9198: ldur            x0, [fp, #-8]
    // 0xae919c: stur            x2, [fp, #-0x10]
    // 0xae91a0: LoadField: r1 = r0->field_f
    //     0xae91a0: ldur            w1, [x0, #0xf]
    // 0xae91a4: DecompressPointer r1
    //     0xae91a4: add             x1, x1, HEAP, lsl #32
    // 0xae91a8: r0 = controller()
    //     0xae91a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae91ac: LoadField: r1 = r0->field_47
    //     0xae91ac: ldur            w1, [x0, #0x47]
    // 0xae91b0: DecompressPointer r1
    //     0xae91b0: add             x1, x1, HEAP, lsl #32
    // 0xae91b4: r0 = value()
    //     0xae91b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae91b8: cmp             w0, NULL
    // 0xae91bc: b.ne            #0xae91c8
    // 0xae91c0: r0 = Null
    //     0xae91c0: mov             x0, NULL
    // 0xae91c4: b               #0xae91d4
    // 0xae91c8: LoadField: r1 = r0->field_f
    //     0xae91c8: ldur            w1, [x0, #0xf]
    // 0xae91cc: DecompressPointer r1
    //     0xae91cc: add             x1, x1, HEAP, lsl #32
    // 0xae91d0: mov             x0, x1
    // 0xae91d4: cmp             w0, NULL
    // 0xae91d8: b.ne            #0xae91e4
    // 0xae91dc: r0 = "Pilih Cabang"
    //     0xae91dc: add             x0, PP, #0x40, lsl #12  ; [pp+0x40070] "Pilih Cabang"
    //     0xae91e0: ldr             x0, [x0, #0x70]
    // 0xae91e4: stur            x0, [fp, #-0x18]
    // 0xae91e8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae91e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae91ec: ldr             x0, [x0, #0x2670]
    //     0xae91f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae91f4: cmp             w0, w16
    //     0xae91f8: b.ne            #0xae9204
    //     0xae91fc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae9200: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae9204: r0 = GetNavigation.textTheme()
    //     0xae9204: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae9208: LoadField: r3 = r0->field_2f
    //     0xae9208: ldur            w3, [x0, #0x2f]
    // 0xae920c: DecompressPointer r3
    //     0xae920c: add             x3, x3, HEAP, lsl #32
    // 0xae9210: stur            x3, [fp, #-0x20]
    // 0xae9214: cmp             w3, NULL
    // 0xae9218: b.ne            #0xae9224
    // 0xae921c: r2 = Null
    //     0xae921c: mov             x2, NULL
    // 0xae9220: b               #0xae9268
    // 0xae9224: r1 = _ConstMap len:3
    //     0xae9224: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xae9228: ldr             x1, [x1, #0xbe8]
    // 0xae922c: r2 = 6
    //     0xae922c: movz            x2, #0x6
    // 0xae9230: r0 = []()
    //     0xae9230: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae9234: r16 = <Color?>
    //     0xae9234: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae9238: ldr             x16, [x16, #0x98]
    // 0xae923c: r30 = Instance_Color
    //     0xae923c: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae9240: stp             lr, x16, [SP, #8]
    // 0xae9244: str             x0, [SP]
    // 0xae9248: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae9248: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae924c: r0 = mode()
    //     0xae924c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae9250: str             x0, [SP]
    // 0xae9254: ldur            x1, [fp, #-0x20]
    // 0xae9258: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xae9258: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xae925c: ldr             x4, [x4, #0x228]
    // 0xae9260: r0 = copyWith()
    //     0xae9260: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae9264: mov             x2, x0
    // 0xae9268: ldur            x1, [fp, #-8]
    // 0xae926c: ldur            x0, [fp, #-0x18]
    // 0xae9270: stur            x2, [fp, #-0x20]
    // 0xae9274: r0 = InputDecoration()
    //     0xae9274: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xae9278: mov             x2, x0
    // 0xae927c: ldur            x0, [fp, #-0x18]
    // 0xae9280: stur            x2, [fp, #-0x28]
    // 0xae9284: StoreField: r2->field_2f = r0
    //     0xae9284: stur            w0, [x2, #0x2f]
    // 0xae9288: ldur            x0, [fp, #-0x20]
    // 0xae928c: StoreField: r2->field_33 = r0
    //     0xae928c: stur            w0, [x2, #0x33]
    // 0xae9290: r0 = true
    //     0xae9290: add             x0, NULL, #0x20  ; true
    // 0xae9294: StoreField: r2->field_43 = r0
    //     0xae9294: stur            w0, [x2, #0x43]
    // 0xae9298: r1 = Instance_Icon
    //     0xae9298: add             x1, PP, #0x35, lsl #12  ; [pp+0x352b8] Obj!Icon@e24571
    //     0xae929c: ldr             x1, [x1, #0x2b8]
    // 0xae92a0: StoreField: r2->field_83 = r1
    //     0xae92a0: stur            w1, [x2, #0x83]
    // 0xae92a4: StoreField: r2->field_cf = r0
    //     0xae92a4: stur            w0, [x2, #0xcf]
    // 0xae92a8: ldur            x0, [fp, #-8]
    // 0xae92ac: LoadField: r1 = r0->field_f
    //     0xae92ac: ldur            w1, [x0, #0xf]
    // 0xae92b0: DecompressPointer r1
    //     0xae92b0: add             x1, x1, HEAP, lsl #32
    // 0xae92b4: r0 = controller()
    //     0xae92b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae92b8: mov             x1, x0
    // 0xae92bc: ldur            x0, [fp, #-8]
    // 0xae92c0: LoadField: r3 = r0->field_13
    //     0xae92c0: ldur            w3, [x0, #0x13]
    // 0xae92c4: DecompressPointer r3
    //     0xae92c4: add             x3, x3, HEAP, lsl #32
    // 0xae92c8: mov             x2, x1
    // 0xae92cc: stur            x3, [fp, #-0x18]
    // 0xae92d0: r1 = Function 'selectRegency':.
    //     0xae92d0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40078] AnonymousClosure: (0xae9330), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectRegency (0xae9368)
    //     0xae92d4: ldr             x1, [x1, #0x78]
    // 0xae92d8: r0 = AllocateClosure()
    //     0xae92d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae92dc: r1 = <String>
    //     0xae92dc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae92e0: stur            x0, [fp, #-8]
    // 0xae92e4: r0 = TextFormField()
    //     0xae92e4: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae92e8: stur            x0, [fp, #-0x20]
    // 0xae92ec: r16 = true
    //     0xae92ec: add             x16, NULL, #0x20  ; true
    // 0xae92f0: ldur            lr, [fp, #-0x10]
    // 0xae92f4: stp             lr, x16, [SP, #0x10]
    // 0xae92f8: ldur            x16, [fp, #-8]
    // 0xae92fc: ldur            lr, [fp, #-0x18]
    // 0xae9300: stp             lr, x16, [SP]
    // 0xae9304: mov             x1, x0
    // 0xae9308: ldur            x2, [fp, #-0x28]
    // 0xae930c: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x3, onTap, 0x4, readOnly, 0x2, validator, 0x5, null]
    //     0xae930c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40040] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x3, "onTap", 0x4, "readOnly", 0x2, "validator", 0x5, Null]
    //     0xae9310: ldr             x4, [x4, #0x40]
    // 0xae9314: r0 = TextFormField()
    //     0xae9314: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae9318: ldur            x0, [fp, #-0x20]
    // 0xae931c: LeaveFrame
    //     0xae931c: mov             SP, fp
    //     0xae9320: ldp             fp, lr, [SP], #0x10
    // 0xae9324: ret
    //     0xae9324: ret             
    // 0xae9328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae932c: b               #0xae9184
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xae9578, size: 0x1d4
    // 0xae9578: EnterFrame
    //     0xae9578: stp             fp, lr, [SP, #-0x10]!
    //     0xae957c: mov             fp, SP
    // 0xae9580: AllocStack(0x48)
    //     0xae9580: sub             SP, SP, #0x48
    // 0xae9584: SetupParameters()
    //     0xae9584: ldr             x0, [fp, #0x10]
    //     0xae9588: ldur            w2, [x0, #0x17]
    //     0xae958c: add             x2, x2, HEAP, lsl #32
    //     0xae9590: stur            x2, [fp, #-8]
    // 0xae9594: CheckStackOverflow
    //     0xae9594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9598: cmp             SP, x16
    //     0xae959c: b.ls            #0xae9744
    // 0xae95a0: LoadField: r1 = r2->field_f
    //     0xae95a0: ldur            w1, [x2, #0xf]
    // 0xae95a4: DecompressPointer r1
    //     0xae95a4: add             x1, x1, HEAP, lsl #32
    // 0xae95a8: r0 = controller()
    //     0xae95a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae95ac: LoadField: r2 = r0->field_53
    //     0xae95ac: ldur            w2, [x0, #0x53]
    // 0xae95b0: DecompressPointer r2
    //     0xae95b0: add             x2, x2, HEAP, lsl #32
    // 0xae95b4: ldur            x0, [fp, #-8]
    // 0xae95b8: stur            x2, [fp, #-0x10]
    // 0xae95bc: LoadField: r1 = r0->field_f
    //     0xae95bc: ldur            w1, [x0, #0xf]
    // 0xae95c0: DecompressPointer r1
    //     0xae95c0: add             x1, x1, HEAP, lsl #32
    // 0xae95c4: r0 = controller()
    //     0xae95c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae95c8: LoadField: r1 = r0->field_43
    //     0xae95c8: ldur            w1, [x0, #0x43]
    // 0xae95cc: DecompressPointer r1
    //     0xae95cc: add             x1, x1, HEAP, lsl #32
    // 0xae95d0: r0 = value()
    //     0xae95d0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae95d4: cmp             w0, NULL
    // 0xae95d8: b.ne            #0xae95e4
    // 0xae95dc: r0 = Null
    //     0xae95dc: mov             x0, NULL
    // 0xae95e0: b               #0xae95f0
    // 0xae95e4: LoadField: r1 = r0->field_1b
    //     0xae95e4: ldur            w1, [x0, #0x1b]
    // 0xae95e8: DecompressPointer r1
    //     0xae95e8: add             x1, x1, HEAP, lsl #32
    // 0xae95ec: mov             x0, x1
    // 0xae95f0: cmp             w0, NULL
    // 0xae95f4: b.ne            #0xae9600
    // 0xae95f8: r0 = "Pilih Wilayah"
    //     0xae95f8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40090] "Pilih Wilayah"
    //     0xae95fc: ldr             x0, [x0, #0x90]
    // 0xae9600: stur            x0, [fp, #-0x18]
    // 0xae9604: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae9604: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae9608: ldr             x0, [x0, #0x2670]
    //     0xae960c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae9610: cmp             w0, w16
    //     0xae9614: b.ne            #0xae9620
    //     0xae9618: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xae961c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xae9620: r0 = GetNavigation.textTheme()
    //     0xae9620: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xae9624: LoadField: r3 = r0->field_2f
    //     0xae9624: ldur            w3, [x0, #0x2f]
    // 0xae9628: DecompressPointer r3
    //     0xae9628: add             x3, x3, HEAP, lsl #32
    // 0xae962c: stur            x3, [fp, #-0x20]
    // 0xae9630: cmp             w3, NULL
    // 0xae9634: b.ne            #0xae9640
    // 0xae9638: r2 = Null
    //     0xae9638: mov             x2, NULL
    // 0xae963c: b               #0xae9684
    // 0xae9640: r1 = _ConstMap len:3
    //     0xae9640: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xae9644: ldr             x1, [x1, #0xbe8]
    // 0xae9648: r2 = 6
    //     0xae9648: movz            x2, #0x6
    // 0xae964c: r0 = []()
    //     0xae964c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae9650: r16 = <Color?>
    //     0xae9650: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae9654: ldr             x16, [x16, #0x98]
    // 0xae9658: r30 = Instance_Color
    //     0xae9658: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae965c: stp             lr, x16, [SP, #8]
    // 0xae9660: str             x0, [SP]
    // 0xae9664: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae9664: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae9668: r0 = mode()
    //     0xae9668: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae966c: str             x0, [SP]
    // 0xae9670: ldur            x1, [fp, #-0x20]
    // 0xae9674: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xae9674: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xae9678: ldr             x4, [x4, #0x228]
    // 0xae967c: r0 = copyWith()
    //     0xae967c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae9680: mov             x2, x0
    // 0xae9684: ldur            x1, [fp, #-8]
    // 0xae9688: ldur            x0, [fp, #-0x18]
    // 0xae968c: stur            x2, [fp, #-0x20]
    // 0xae9690: r0 = InputDecoration()
    //     0xae9690: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xae9694: mov             x2, x0
    // 0xae9698: ldur            x0, [fp, #-0x18]
    // 0xae969c: stur            x2, [fp, #-0x28]
    // 0xae96a0: StoreField: r2->field_2f = r0
    //     0xae96a0: stur            w0, [x2, #0x2f]
    // 0xae96a4: ldur            x0, [fp, #-0x20]
    // 0xae96a8: StoreField: r2->field_33 = r0
    //     0xae96a8: stur            w0, [x2, #0x33]
    // 0xae96ac: r0 = true
    //     0xae96ac: add             x0, NULL, #0x20  ; true
    // 0xae96b0: StoreField: r2->field_43 = r0
    //     0xae96b0: stur            w0, [x2, #0x43]
    // 0xae96b4: r1 = Instance_Icon
    //     0xae96b4: add             x1, PP, #0x35, lsl #12  ; [pp+0x352b8] Obj!Icon@e24571
    //     0xae96b8: ldr             x1, [x1, #0x2b8]
    // 0xae96bc: StoreField: r2->field_83 = r1
    //     0xae96bc: stur            w1, [x2, #0x83]
    // 0xae96c0: StoreField: r2->field_cf = r0
    //     0xae96c0: stur            w0, [x2, #0xcf]
    // 0xae96c4: ldur            x0, [fp, #-8]
    // 0xae96c8: LoadField: r1 = r0->field_f
    //     0xae96c8: ldur            w1, [x0, #0xf]
    // 0xae96cc: DecompressPointer r1
    //     0xae96cc: add             x1, x1, HEAP, lsl #32
    // 0xae96d0: r0 = controller()
    //     0xae96d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae96d4: mov             x1, x0
    // 0xae96d8: ldur            x0, [fp, #-8]
    // 0xae96dc: LoadField: r3 = r0->field_13
    //     0xae96dc: ldur            w3, [x0, #0x13]
    // 0xae96e0: DecompressPointer r3
    //     0xae96e0: add             x3, x3, HEAP, lsl #32
    // 0xae96e4: mov             x2, x1
    // 0xae96e8: stur            x3, [fp, #-0x18]
    // 0xae96ec: r1 = Function 'selectProvince':.
    //     0xae96ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40098] AnonymousClosure: (0xae974c), in [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::selectProvince (0xae9784)
    //     0xae96f0: ldr             x1, [x1, #0x98]
    // 0xae96f4: r0 = AllocateClosure()
    //     0xae96f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae96f8: r1 = <String>
    //     0xae96f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae96fc: stur            x0, [fp, #-8]
    // 0xae9700: r0 = TextFormField()
    //     0xae9700: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae9704: stur            x0, [fp, #-0x20]
    // 0xae9708: r16 = true
    //     0xae9708: add             x16, NULL, #0x20  ; true
    // 0xae970c: ldur            lr, [fp, #-0x10]
    // 0xae9710: stp             lr, x16, [SP, #0x10]
    // 0xae9714: ldur            x16, [fp, #-8]
    // 0xae9718: ldur            lr, [fp, #-0x18]
    // 0xae971c: stp             lr, x16, [SP]
    // 0xae9720: mov             x1, x0
    // 0xae9724: ldur            x2, [fp, #-0x28]
    // 0xae9728: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x3, onTap, 0x4, readOnly, 0x2, validator, 0x5, null]
    //     0xae9728: add             x4, PP, #0x40, lsl #12  ; [pp+0x40040] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x3, "onTap", 0x4, "readOnly", 0x2, "validator", 0x5, Null]
    //     0xae972c: ldr             x4, [x4, #0x40]
    // 0xae9730: r0 = TextFormField()
    //     0xae9730: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae9734: ldur            x0, [fp, #-0x20]
    // 0xae9738: LeaveFrame
    //     0xae9738: mov             SP, fp
    //     0xae973c: ldp             fp, lr, [SP], #0x10
    // 0xae9740: ret
    //     0xae9740: ret             
    // 0xae9744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9748: b               #0xae95a0
  }
}

// class id: 5288, size: 0x14, field offset: 0x14
//   const constructor, 
class CampaignFormWidget extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae705c, size: 0x5b4
    // 0xae705c: EnterFrame
    //     0xae705c: stp             fp, lr, [SP, #-0x10]!
    //     0xae7060: mov             fp, SP
    // 0xae7064: AllocStack(0x88)
    //     0xae7064: sub             SP, SP, #0x88
    // 0xae7068: SetupParameters(CampaignFormWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae7068: stur            x1, [fp, #-8]
    //     0xae706c: stur            x2, [fp, #-0x10]
    // 0xae7070: CheckStackOverflow
    //     0xae7070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7074: cmp             SP, x16
    //     0xae7078: b.ls            #0xae7608
    // 0xae707c: r1 = 2
    //     0xae707c: movz            x1, #0x2
    // 0xae7080: r0 = AllocateContext()
    //     0xae7080: bl              #0xec126c  ; AllocateContextStub
    // 0xae7084: mov             x2, x0
    // 0xae7088: ldur            x0, [fp, #-8]
    // 0xae708c: stur            x2, [fp, #-0x18]
    // 0xae7090: StoreField: r2->field_f = r0
    //     0xae7090: stur            w0, [x2, #0xf]
    // 0xae7094: ldur            x1, [fp, #-0x10]
    // 0xae7098: StoreField: r2->field_13 = r1
    //     0xae7098: stur            w1, [x2, #0x13]
    // 0xae709c: mov             x1, x0
    // 0xae70a0: r0 = controller()
    //     0xae70a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae70a4: mov             x1, x0
    // 0xae70a8: r0 = title()
    //     0xae70a8: bl              #0xae78d0  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::title
    // 0xae70ac: ldur            x1, [fp, #-8]
    // 0xae70b0: stur            x0, [fp, #-0x10]
    // 0xae70b4: r0 = controller()
    //     0xae70b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae70b8: LoadField: r2 = r0->field_33
    //     0xae70b8: ldur            w2, [x0, #0x33]
    // 0xae70bc: DecompressPointer r2
    //     0xae70bc: add             x2, x2, HEAP, lsl #32
    // 0xae70c0: ldur            x1, [fp, #-8]
    // 0xae70c4: stur            x2, [fp, #-0x20]
    // 0xae70c8: r0 = controller()
    //     0xae70c8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae70cc: LoadField: r2 = r0->field_23
    //     0xae70cc: ldur            w2, [x0, #0x23]
    // 0xae70d0: DecompressPointer r2
    //     0xae70d0: add             x2, x2, HEAP, lsl #32
    // 0xae70d4: LoadField: r3 = r2->field_7
    //     0xae70d4: ldur            w3, [x2, #7]
    // 0xae70d8: DecompressPointer r3
    //     0xae70d8: add             x3, x3, HEAP, lsl #32
    // 0xae70dc: r1 = Function 'call':.
    //     0xae70dc: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae70e0: ldr             x1, [x1, #0x310]
    // 0xae70e4: r0 = AllocateClosureTA()
    //     0xae70e4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae70e8: mov             x3, x0
    // 0xae70ec: r2 = Null
    //     0xae70ec: mov             x2, NULL
    // 0xae70f0: r1 = Null
    //     0xae70f0: mov             x1, NULL
    // 0xae70f4: stur            x3, [fp, #-0x28]
    // 0xae70f8: r8 = (dynamic this, int?) => int
    //     0xae70f8: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a0a0] FunctionType: (dynamic this, int?) => int
    //     0xae70fc: ldr             x8, [x8, #0xa0]
    // 0xae7100: r3 = Null
    //     0xae7100: add             x3, PP, #0x34, lsl #12  ; [pp+0x34cc0] Null
    //     0xae7104: ldr             x3, [x3, #0xcc0]
    // 0xae7108: r0 = DefaultTypeTest()
    //     0xae7108: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae710c: r1 = Function '<anonymous closure>': static.
    //     0xae710c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae7110: ldr             x1, [x1, #0xda0]
    // 0xae7114: r2 = Null
    //     0xae7114: mov             x2, NULL
    // 0xae7118: r0 = AllocateClosure()
    //     0xae7118: bl              #0xec1630  ; AllocateClosureStub
    // 0xae711c: r1 = <String>
    //     0xae711c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae7120: stur            x0, [fp, #-0x30]
    // 0xae7124: StoreField: r0->field_b = r1
    //     0xae7124: stur            w1, [x0, #0xb]
    // 0xae7128: r16 = <String>
    //     0xae7128: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae712c: str             x16, [SP, #0x10]
    // 0xae7130: r2 = 10000
    //     0xae7130: movz            x2, #0x2710
    // 0xae7134: r16 = "Minimal sedekah Rp10.000"
    //     0xae7134: add             x16, PP, #0x34, lsl #12  ; [pp+0x34cd0] "Minimal sedekah Rp10.000"
    //     0xae7138: ldr             x16, [x16, #0xcd0]
    // 0xae713c: stp             x16, x2, [SP]
    // 0xae7140: r4 = const [0x1, 0x2, 0x2, 0x1, errorText, 0x1, null]
    //     0xae7140: add             x4, PP, #0x34, lsl #12  ; [pp+0x34cd8] List(7) [0x1, 0x2, 0x2, 0x1, "errorText", 0x1, Null]
    //     0xae7144: ldr             x4, [x4, #0xcd8]
    // 0xae7148: r0 = min()
    //     0xae7148: bl              #0xae7668  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::min
    // 0xae714c: r1 = Null
    //     0xae714c: mov             x1, NULL
    // 0xae7150: r2 = 4
    //     0xae7150: movz            x2, #0x4
    // 0xae7154: stur            x0, [fp, #-0x38]
    // 0xae7158: r0 = AllocateArray()
    //     0xae7158: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae715c: mov             x2, x0
    // 0xae7160: ldur            x0, [fp, #-0x30]
    // 0xae7164: stur            x2, [fp, #-0x40]
    // 0xae7168: StoreField: r2->field_f = r0
    //     0xae7168: stur            w0, [x2, #0xf]
    // 0xae716c: ldur            x0, [fp, #-0x38]
    // 0xae7170: StoreField: r2->field_13 = r0
    //     0xae7170: stur            w0, [x2, #0x13]
    // 0xae7174: r1 = <(dynamic this, String?) => String?>
    //     0xae7174: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xae7178: ldr             x1, [x1, #0xd98]
    // 0xae717c: r0 = AllocateGrowableArray()
    //     0xae717c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae7180: mov             x1, x0
    // 0xae7184: ldur            x0, [fp, #-0x40]
    // 0xae7188: stur            x1, [fp, #-0x30]
    // 0xae718c: StoreField: r1->field_f = r0
    //     0xae718c: stur            w0, [x1, #0xf]
    // 0xae7190: r2 = 4
    //     0xae7190: movz            x2, #0x4
    // 0xae7194: StoreField: r1->field_b = r2
    //     0xae7194: stur            w2, [x1, #0xb]
    // 0xae7198: r1 = 1
    //     0xae7198: movz            x1, #0x1
    // 0xae719c: r0 = AllocateContext()
    //     0xae719c: bl              #0xec126c  ; AllocateContextStub
    // 0xae71a0: mov             x1, x0
    // 0xae71a4: ldur            x0, [fp, #-0x30]
    // 0xae71a8: StoreField: r1->field_f = r0
    //     0xae71a8: stur            w0, [x1, #0xf]
    // 0xae71ac: mov             x2, x1
    // 0xae71b0: r1 = Function '<anonymous closure>': static.
    //     0xae71b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae71b4: ldr             x1, [x1, #0xda8]
    // 0xae71b8: r0 = AllocateClosure()
    //     0xae71b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xae71bc: r1 = <String>
    //     0xae71bc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae71c0: stur            x0, [fp, #-0x30]
    // 0xae71c4: StoreField: r0->field_b = r1
    //     0xae71c4: stur            w1, [x0, #0xb]
    // 0xae71c8: r0 = NZakatTextField()
    //     0xae71c8: bl              #0xae765c  ; AllocateNZakatTextFieldStub -> NZakatTextField (size=0x34)
    // 0xae71cc: mov             x1, x0
    // 0xae71d0: ldur            x0, [fp, #-0x20]
    // 0xae71d4: stur            x1, [fp, #-0x38]
    // 0xae71d8: StoreField: r1->field_b = r0
    //     0xae71d8: stur            w0, [x1, #0xb]
    // 0xae71dc: r0 = "Nominal Donasi (min Rp10.000)"
    //     0xae71dc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ce0] "Nominal Donasi (min Rp10.000)"
    //     0xae71e0: ldr             x0, [x0, #0xce0]
    // 0xae71e4: StoreField: r1->field_f = r0
    //     0xae71e4: stur            w0, [x1, #0xf]
    // 0xae71e8: ldur            x0, [fp, #-0x28]
    // 0xae71ec: StoreField: r1->field_1b = r0
    //     0xae71ec: stur            w0, [x1, #0x1b]
    // 0xae71f0: r0 = false
    //     0xae71f0: add             x0, NULL, #0x30  ; false
    // 0xae71f4: StoreField: r1->field_1f = r0
    //     0xae71f4: stur            w0, [x1, #0x1f]
    // 0xae71f8: r2 = true
    //     0xae71f8: add             x2, NULL, #0x20  ; true
    // 0xae71fc: StoreField: r1->field_27 = r2
    //     0xae71fc: stur            w2, [x1, #0x27]
    // 0xae7200: ldur            x3, [fp, #-0x30]
    // 0xae7204: StoreField: r1->field_2b = r3
    //     0xae7204: stur            w3, [x1, #0x2b]
    // 0xae7208: StoreField: r1->field_2f = r0
    //     0xae7208: stur            w0, [x1, #0x2f]
    // 0xae720c: r0 = Obx()
    //     0xae720c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae7210: ldur            x2, [fp, #-0x18]
    // 0xae7214: r1 = Function '<anonymous closure>':.
    //     0xae7214: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ce8] AnonymousClosure: (0xae7a70), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] CampaignFormWidget::build (0xae705c)
    //     0xae7218: ldr             x1, [x1, #0xce8]
    // 0xae721c: stur            x0, [fp, #-0x20]
    // 0xae7220: r0 = AllocateClosure()
    //     0xae7220: bl              #0xec1630  ; AllocateClosureStub
    // 0xae7224: mov             x1, x0
    // 0xae7228: ldur            x0, [fp, #-0x20]
    // 0xae722c: StoreField: r0->field_b = r1
    //     0xae722c: stur            w1, [x0, #0xb]
    // 0xae7230: r1 = Function '<anonymous closure>': static.
    //     0xae7230: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae7234: ldr             x1, [x1, #0xda0]
    // 0xae7238: r2 = Null
    //     0xae7238: mov             x2, NULL
    // 0xae723c: r0 = AllocateClosure()
    //     0xae723c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae7240: mov             x2, x0
    // 0xae7244: r0 = <String>
    //     0xae7244: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae7248: stur            x2, [fp, #-0x28]
    // 0xae724c: StoreField: r2->field_b = r0
    //     0xae724c: stur            w0, [x2, #0xb]
    // 0xae7250: ldur            x1, [fp, #-8]
    // 0xae7254: r0 = controller()
    //     0xae7254: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7258: LoadField: r2 = r0->field_27
    //     0xae7258: ldur            w2, [x0, #0x27]
    // 0xae725c: DecompressPointer r2
    //     0xae725c: add             x2, x2, HEAP, lsl #32
    // 0xae7260: LoadField: r3 = r2->field_7
    //     0xae7260: ldur            w3, [x2, #7]
    // 0xae7264: DecompressPointer r3
    //     0xae7264: add             x3, x3, HEAP, lsl #32
    // 0xae7268: r1 = Function 'call':.
    //     0xae7268: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae726c: ldr             x1, [x1, #0x310]
    // 0xae7270: r0 = AllocateClosureTA()
    //     0xae7270: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae7274: mov             x3, x0
    // 0xae7278: r2 = Null
    //     0xae7278: mov             x2, NULL
    // 0xae727c: r1 = Null
    //     0xae727c: mov             x1, NULL
    // 0xae7280: stur            x3, [fp, #-0x30]
    // 0xae7284: r8 = (dynamic this, String?) => String
    //     0xae7284: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xae7288: ldr             x8, [x8, #0x40]
    // 0xae728c: r3 = Null
    //     0xae728c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34cf0] Null
    //     0xae7290: ldr             x3, [x3, #0xcf0]
    // 0xae7294: r0 = DefaultTypeTest()
    //     0xae7294: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae7298: r16 = "[a-zA-Z ]"
    //     0xae7298: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xae729c: ldr             x16, [x16, #0xb20]
    // 0xae72a0: stp             x16, NULL, [SP, #0x20]
    // 0xae72a4: r16 = false
    //     0xae72a4: add             x16, NULL, #0x30  ; false
    // 0xae72a8: r30 = true
    //     0xae72a8: add             lr, NULL, #0x20  ; true
    // 0xae72ac: stp             lr, x16, [SP, #0x10]
    // 0xae72b0: r16 = false
    //     0xae72b0: add             x16, NULL, #0x30  ; false
    // 0xae72b4: r30 = false
    //     0xae72b4: add             lr, NULL, #0x30  ; false
    // 0xae72b8: stp             lr, x16, [SP]
    // 0xae72bc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xae72bc: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xae72c0: r0 = _RegExp()
    //     0xae72c0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xae72c4: stur            x0, [fp, #-0x40]
    // 0xae72c8: r0 = FilteringTextInputFormatter()
    //     0xae72c8: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xae72cc: mov             x3, x0
    // 0xae72d0: ldur            x0, [fp, #-0x40]
    // 0xae72d4: stur            x3, [fp, #-0x48]
    // 0xae72d8: StoreField: r3->field_7 = r0
    //     0xae72d8: stur            w0, [x3, #7]
    // 0xae72dc: r0 = true
    //     0xae72dc: add             x0, NULL, #0x20  ; true
    // 0xae72e0: StoreField: r3->field_b = r0
    //     0xae72e0: stur            w0, [x3, #0xb]
    // 0xae72e4: r0 = ""
    //     0xae72e4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae72e8: StoreField: r3->field_f = r0
    //     0xae72e8: stur            w0, [x3, #0xf]
    // 0xae72ec: r1 = Null
    //     0xae72ec: mov             x1, NULL
    // 0xae72f0: r2 = 2
    //     0xae72f0: movz            x2, #0x2
    // 0xae72f4: r0 = AllocateArray()
    //     0xae72f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae72f8: mov             x2, x0
    // 0xae72fc: ldur            x0, [fp, #-0x48]
    // 0xae7300: stur            x2, [fp, #-0x40]
    // 0xae7304: StoreField: r2->field_f = r0
    //     0xae7304: stur            w0, [x2, #0xf]
    // 0xae7308: r1 = <TextInputFormatter>
    //     0xae7308: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xae730c: ldr             x1, [x1, #0x788]
    // 0xae7310: r0 = AllocateGrowableArray()
    //     0xae7310: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae7314: mov             x2, x0
    // 0xae7318: ldur            x0, [fp, #-0x40]
    // 0xae731c: stur            x2, [fp, #-0x48]
    // 0xae7320: StoreField: r2->field_f = r0
    //     0xae7320: stur            w0, [x2, #0xf]
    // 0xae7324: r0 = 2
    //     0xae7324: movz            x0, #0x2
    // 0xae7328: StoreField: r2->field_b = r0
    //     0xae7328: stur            w0, [x2, #0xb]
    // 0xae732c: ldur            x1, [fp, #-8]
    // 0xae7330: r0 = controller()
    //     0xae7330: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7334: LoadField: r1 = r0->field_27
    //     0xae7334: ldur            w1, [x0, #0x27]
    // 0xae7338: DecompressPointer r1
    //     0xae7338: add             x1, x1, HEAP, lsl #32
    // 0xae733c: r0 = value()
    //     0xae733c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae7340: r1 = <String>
    //     0xae7340: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae7344: stur            x0, [fp, #-0x40]
    // 0xae7348: r0 = TextFormField()
    //     0xae7348: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae734c: stur            x0, [fp, #-0x50]
    // 0xae7350: ldur            x16, [fp, #-0x28]
    // 0xae7354: ldur            lr, [fp, #-0x30]
    // 0xae7358: stp             lr, x16, [SP, #0x10]
    // 0xae735c: ldur            x16, [fp, #-0x48]
    // 0xae7360: ldur            lr, [fp, #-0x40]
    // 0xae7364: stp             lr, x16, [SP]
    // 0xae7368: mov             x1, x0
    // 0xae736c: r2 = Instance_InputDecoration
    //     0xae736c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xae7370: ldr             x2, [x2, #0xb28]
    // 0xae7374: r4 = const [0, 0x6, 0x4, 0x2, initialValue, 0x5, inputFormatters, 0x4, onChanged, 0x3, validator, 0x2, null]
    //     0xae7374: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b30] List(13) [0, 0x6, 0x4, 0x2, "initialValue", 0x5, "inputFormatters", 0x4, "onChanged", 0x3, "validator", 0x2, Null]
    //     0xae7378: ldr             x4, [x4, #0xb30]
    // 0xae737c: r0 = TextFormField()
    //     0xae737c: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae7380: r0 = Obx()
    //     0xae7380: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae7384: ldur            x2, [fp, #-0x18]
    // 0xae7388: r1 = Function '<anonymous closure>':.
    //     0xae7388: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d00] AnonymousClosure: (0xae7958), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] CampaignFormWidget::build (0xae705c)
    //     0xae738c: ldr             x1, [x1, #0xd00]
    // 0xae7390: stur            x0, [fp, #-0x18]
    // 0xae7394: r0 = AllocateClosure()
    //     0xae7394: bl              #0xec1630  ; AllocateClosureStub
    // 0xae7398: mov             x1, x0
    // 0xae739c: ldur            x0, [fp, #-0x18]
    // 0xae73a0: StoreField: r0->field_b = r1
    //     0xae73a0: stur            w1, [x0, #0xb]
    // 0xae73a4: r1 = Null
    //     0xae73a4: mov             x1, NULL
    // 0xae73a8: r2 = 4
    //     0xae73a8: movz            x2, #0x4
    // 0xae73ac: r0 = AllocateArray()
    //     0xae73ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae73b0: mov             x2, x0
    // 0xae73b4: ldur            x0, [fp, #-0x18]
    // 0xae73b8: stur            x2, [fp, #-0x28]
    // 0xae73bc: StoreField: r2->field_f = r0
    //     0xae73bc: stur            w0, [x2, #0xf]
    // 0xae73c0: r16 = Instance_Text
    //     0xae73c0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b50] Obj!Text@e21c31
    //     0xae73c4: ldr             x16, [x16, #0xb50]
    // 0xae73c8: StoreField: r2->field_13 = r16
    //     0xae73c8: stur            w16, [x2, #0x13]
    // 0xae73cc: r1 = <Widget>
    //     0xae73cc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae73d0: r0 = AllocateGrowableArray()
    //     0xae73d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae73d4: mov             x1, x0
    // 0xae73d8: ldur            x0, [fp, #-0x28]
    // 0xae73dc: stur            x1, [fp, #-0x18]
    // 0xae73e0: StoreField: r1->field_f = r0
    //     0xae73e0: stur            w0, [x1, #0xf]
    // 0xae73e4: r0 = 4
    //     0xae73e4: movz            x0, #0x4
    // 0xae73e8: StoreField: r1->field_b = r0
    //     0xae73e8: stur            w0, [x1, #0xb]
    // 0xae73ec: r0 = Row()
    //     0xae73ec: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae73f0: mov             x3, x0
    // 0xae73f4: r0 = Instance_Axis
    //     0xae73f4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xae73f8: stur            x3, [fp, #-0x28]
    // 0xae73fc: StoreField: r3->field_f = r0
    //     0xae73fc: stur            w0, [x3, #0xf]
    // 0xae7400: r0 = Instance_MainAxisAlignment
    //     0xae7400: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae7404: ldr             x0, [x0, #0x730]
    // 0xae7408: StoreField: r3->field_13 = r0
    //     0xae7408: stur            w0, [x3, #0x13]
    // 0xae740c: r4 = Instance_MainAxisSize
    //     0xae740c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae7410: ldr             x4, [x4, #0x738]
    // 0xae7414: ArrayStore: r3[0] = r4  ; List_4
    //     0xae7414: stur            w4, [x3, #0x17]
    // 0xae7418: r5 = Instance_CrossAxisAlignment
    //     0xae7418: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae741c: ldr             x5, [x5, #0x740]
    // 0xae7420: StoreField: r3->field_1b = r5
    //     0xae7420: stur            w5, [x3, #0x1b]
    // 0xae7424: r6 = Instance_VerticalDirection
    //     0xae7424: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae7428: ldr             x6, [x6, #0x748]
    // 0xae742c: StoreField: r3->field_23 = r6
    //     0xae742c: stur            w6, [x3, #0x23]
    // 0xae7430: r7 = Instance_Clip
    //     0xae7430: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae7434: ldr             x7, [x7, #0x750]
    // 0xae7438: StoreField: r3->field_2b = r7
    //     0xae7438: stur            w7, [x3, #0x2b]
    // 0xae743c: StoreField: r3->field_2f = rZR
    //     0xae743c: stur            xzr, [x3, #0x2f]
    // 0xae7440: ldur            x1, [fp, #-0x18]
    // 0xae7444: StoreField: r3->field_b = r1
    //     0xae7444: stur            w1, [x3, #0xb]
    // 0xae7448: r1 = Null
    //     0xae7448: mov             x1, NULL
    // 0xae744c: r2 = 16
    //     0xae744c: movz            x2, #0x10
    // 0xae7450: r0 = AllocateArray()
    //     0xae7450: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae7454: mov             x2, x0
    // 0xae7458: ldur            x0, [fp, #-0x38]
    // 0xae745c: stur            x2, [fp, #-0x18]
    // 0xae7460: StoreField: r2->field_f = r0
    //     0xae7460: stur            w0, [x2, #0xf]
    // 0xae7464: r16 = Instance_SizedBox
    //     0xae7464: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae7468: ldr             x16, [x16, #0x950]
    // 0xae746c: StoreField: r2->field_13 = r16
    //     0xae746c: stur            w16, [x2, #0x13]
    // 0xae7470: ldur            x0, [fp, #-0x20]
    // 0xae7474: ArrayStore: r2[0] = r0  ; List_4
    //     0xae7474: stur            w0, [x2, #0x17]
    // 0xae7478: r16 = Instance_SizedBox
    //     0xae7478: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae747c: ldr             x16, [x16, #0x950]
    // 0xae7480: StoreField: r2->field_1b = r16
    //     0xae7480: stur            w16, [x2, #0x1b]
    // 0xae7484: r16 = Instance_NLabelTextField
    //     0xae7484: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b08] Obj!NLabelTextField@e1fbd1
    //     0xae7488: ldr             x16, [x16, #0xb08]
    // 0xae748c: StoreField: r2->field_1f = r16
    //     0xae748c: stur            w16, [x2, #0x1f]
    // 0xae7490: ldur            x0, [fp, #-0x50]
    // 0xae7494: StoreField: r2->field_23 = r0
    //     0xae7494: stur            w0, [x2, #0x23]
    // 0xae7498: r16 = Instance_SizedBox
    //     0xae7498: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae749c: ldr             x16, [x16, #0x950]
    // 0xae74a0: StoreField: r2->field_27 = r16
    //     0xae74a0: stur            w16, [x2, #0x27]
    // 0xae74a4: ldur            x0, [fp, #-0x28]
    // 0xae74a8: StoreField: r2->field_2b = r0
    //     0xae74a8: stur            w0, [x2, #0x2b]
    // 0xae74ac: r1 = <Widget>
    //     0xae74ac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae74b0: r0 = AllocateGrowableArray()
    //     0xae74b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae74b4: mov             x1, x0
    // 0xae74b8: ldur            x0, [fp, #-0x18]
    // 0xae74bc: stur            x1, [fp, #-0x20]
    // 0xae74c0: StoreField: r1->field_f = r0
    //     0xae74c0: stur            w0, [x1, #0xf]
    // 0xae74c4: r0 = 16
    //     0xae74c4: movz            x0, #0x10
    // 0xae74c8: StoreField: r1->field_b = r0
    //     0xae74c8: stur            w0, [x1, #0xb]
    // 0xae74cc: r0 = NSection()
    //     0xae74cc: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae74d0: mov             x3, x0
    // 0xae74d4: ldur            x0, [fp, #-0x10]
    // 0xae74d8: stur            x3, [fp, #-0x18]
    // 0xae74dc: StoreField: r3->field_b = r0
    //     0xae74dc: stur            w0, [x3, #0xb]
    // 0xae74e0: ldur            x0, [fp, #-0x20]
    // 0xae74e4: StoreField: r3->field_f = r0
    //     0xae74e4: stur            w0, [x3, #0xf]
    // 0xae74e8: r0 = false
    //     0xae74e8: add             x0, NULL, #0x30  ; false
    // 0xae74ec: StoreField: r3->field_27 = r0
    //     0xae74ec: stur            w0, [x3, #0x27]
    // 0xae74f0: StoreField: r3->field_2b = r0
    //     0xae74f0: stur            w0, [x3, #0x2b]
    // 0xae74f4: r1 = Null
    //     0xae74f4: mov             x1, NULL
    // 0xae74f8: r2 = 2
    //     0xae74f8: movz            x2, #0x2
    // 0xae74fc: r0 = AllocateArray()
    //     0xae74fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae7500: mov             x2, x0
    // 0xae7504: ldur            x0, [fp, #-0x18]
    // 0xae7508: stur            x2, [fp, #-0x10]
    // 0xae750c: StoreField: r2->field_f = r0
    //     0xae750c: stur            w0, [x2, #0xf]
    // 0xae7510: r1 = <Widget>
    //     0xae7510: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae7514: r0 = AllocateGrowableArray()
    //     0xae7514: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae7518: mov             x2, x0
    // 0xae751c: ldur            x0, [fp, #-0x10]
    // 0xae7520: stur            x2, [fp, #-0x18]
    // 0xae7524: StoreField: r2->field_f = r0
    //     0xae7524: stur            w0, [x2, #0xf]
    // 0xae7528: r0 = 2
    //     0xae7528: movz            x0, #0x2
    // 0xae752c: StoreField: r2->field_b = r0
    //     0xae752c: stur            w0, [x2, #0xb]
    // 0xae7530: ldur            x1, [fp, #-8]
    // 0xae7534: r0 = controller()
    //     0xae7534: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7538: mov             x1, x0
    // 0xae753c: r0 = isKoinNu()
    //     0xae753c: bl              #0xae7610  ; [package:nuonline/app/modules/donation/controllers/campaign_form_controller.dart] CampaignFormController::isKoinNu
    // 0xae7540: tbnz            w0, #4, #0xae75a4
    // 0xae7544: ldur            x0, [fp, #-0x18]
    // 0xae7548: LoadField: r1 = r0->field_b
    //     0xae7548: ldur            w1, [x0, #0xb]
    // 0xae754c: LoadField: r2 = r0->field_f
    //     0xae754c: ldur            w2, [x0, #0xf]
    // 0xae7550: DecompressPointer r2
    //     0xae7550: add             x2, x2, HEAP, lsl #32
    // 0xae7554: LoadField: r3 = r2->field_b
    //     0xae7554: ldur            w3, [x2, #0xb]
    // 0xae7558: r2 = LoadInt32Instr(r1)
    //     0xae7558: sbfx            x2, x1, #1, #0x1f
    // 0xae755c: stur            x2, [fp, #-0x58]
    // 0xae7560: r1 = LoadInt32Instr(r3)
    //     0xae7560: sbfx            x1, x3, #1, #0x1f
    // 0xae7564: cmp             x2, x1
    // 0xae7568: b.ne            #0xae7574
    // 0xae756c: mov             x1, x0
    // 0xae7570: r0 = _growToNextCapacity()
    //     0xae7570: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae7574: ldur            x0, [fp, #-0x18]
    // 0xae7578: ldur            x1, [fp, #-0x58]
    // 0xae757c: add             x2, x1, #1
    // 0xae7580: lsl             x3, x2, #1
    // 0xae7584: StoreField: r0->field_b = r3
    //     0xae7584: stur            w3, [x0, #0xb]
    // 0xae7588: LoadField: r2 = r0->field_f
    //     0xae7588: ldur            w2, [x0, #0xf]
    // 0xae758c: DecompressPointer r2
    //     0xae758c: add             x2, x2, HEAP, lsl #32
    // 0xae7590: add             x3, x2, x1, lsl #2
    // 0xae7594: r16 = Instance__KoinNuLocation
    //     0xae7594: add             x16, PP, #0x34, lsl #12  ; [pp+0x34d08] Obj!_KoinNuLocation@e213c1
    //     0xae7598: ldr             x16, [x16, #0xd08]
    // 0xae759c: StoreField: r3->field_f = r16
    //     0xae759c: stur            w16, [x3, #0xf]
    // 0xae75a0: b               #0xae75a8
    // 0xae75a4: ldur            x0, [fp, #-0x18]
    // 0xae75a8: r0 = Column()
    //     0xae75a8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae75ac: r1 = Instance_Axis
    //     0xae75ac: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae75b0: StoreField: r0->field_f = r1
    //     0xae75b0: stur            w1, [x0, #0xf]
    // 0xae75b4: r1 = Instance_MainAxisAlignment
    //     0xae75b4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae75b8: ldr             x1, [x1, #0x730]
    // 0xae75bc: StoreField: r0->field_13 = r1
    //     0xae75bc: stur            w1, [x0, #0x13]
    // 0xae75c0: r1 = Instance_MainAxisSize
    //     0xae75c0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae75c4: ldr             x1, [x1, #0x738]
    // 0xae75c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xae75c8: stur            w1, [x0, #0x17]
    // 0xae75cc: r1 = Instance_CrossAxisAlignment
    //     0xae75cc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae75d0: ldr             x1, [x1, #0x740]
    // 0xae75d4: StoreField: r0->field_1b = r1
    //     0xae75d4: stur            w1, [x0, #0x1b]
    // 0xae75d8: r1 = Instance_VerticalDirection
    //     0xae75d8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae75dc: ldr             x1, [x1, #0x748]
    // 0xae75e0: StoreField: r0->field_23 = r1
    //     0xae75e0: stur            w1, [x0, #0x23]
    // 0xae75e4: r1 = Instance_Clip
    //     0xae75e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae75e8: ldr             x1, [x1, #0x750]
    // 0xae75ec: StoreField: r0->field_2b = r1
    //     0xae75ec: stur            w1, [x0, #0x2b]
    // 0xae75f0: StoreField: r0->field_2f = rZR
    //     0xae75f0: stur            xzr, [x0, #0x2f]
    // 0xae75f4: ldur            x1, [fp, #-0x18]
    // 0xae75f8: StoreField: r0->field_b = r1
    //     0xae75f8: stur            w1, [x0, #0xb]
    // 0xae75fc: LeaveFrame
    //     0xae75fc: mov             SP, fp
    //     0xae7600: ldp             fp, lr, [SP], #0x10
    // 0xae7604: ret
    //     0xae7604: ret             
    // 0xae7608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae760c: b               #0xae707c
  }
  [closure] Checkbox <anonymous closure>(dynamic) {
    // ** addr: 0xae7958, size: 0x118
    // 0xae7958: EnterFrame
    //     0xae7958: stp             fp, lr, [SP, #-0x10]!
    //     0xae795c: mov             fp, SP
    // 0xae7960: AllocStack(0x30)
    //     0xae7960: sub             SP, SP, #0x30
    // 0xae7964: SetupParameters()
    //     0xae7964: ldr             x0, [fp, #0x10]
    //     0xae7968: ldur            w2, [x0, #0x17]
    //     0xae796c: add             x2, x2, HEAP, lsl #32
    //     0xae7970: stur            x2, [fp, #-8]
    // 0xae7974: CheckStackOverflow
    //     0xae7974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7978: cmp             SP, x16
    //     0xae797c: b.ls            #0xae7a68
    // 0xae7980: LoadField: r1 = r2->field_f
    //     0xae7980: ldur            w1, [x2, #0xf]
    // 0xae7984: DecompressPointer r1
    //     0xae7984: add             x1, x1, HEAP, lsl #32
    // 0xae7988: r0 = controller()
    //     0xae7988: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae798c: LoadField: r1 = r0->field_2b
    //     0xae798c: ldur            w1, [x0, #0x2b]
    // 0xae7990: DecompressPointer r1
    //     0xae7990: add             x1, x1, HEAP, lsl #32
    // 0xae7994: r0 = value()
    //     0xae7994: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae7998: mov             x2, x0
    // 0xae799c: ldur            x0, [fp, #-8]
    // 0xae79a0: stur            x2, [fp, #-0x10]
    // 0xae79a4: LoadField: r1 = r0->field_f
    //     0xae79a4: ldur            w1, [x0, #0xf]
    // 0xae79a8: DecompressPointer r1
    //     0xae79a8: add             x1, x1, HEAP, lsl #32
    // 0xae79ac: r0 = controller()
    //     0xae79ac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae79b0: LoadField: r2 = r0->field_2b
    //     0xae79b0: ldur            w2, [x0, #0x2b]
    // 0xae79b4: DecompressPointer r2
    //     0xae79b4: add             x2, x2, HEAP, lsl #32
    // 0xae79b8: LoadField: r3 = r2->field_7
    //     0xae79b8: ldur            w3, [x2, #7]
    // 0xae79bc: DecompressPointer r3
    //     0xae79bc: add             x3, x3, HEAP, lsl #32
    // 0xae79c0: r1 = Function 'call':.
    //     0xae79c0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae79c4: ldr             x1, [x1, #0x310]
    // 0xae79c8: r0 = AllocateClosureTA()
    //     0xae79c8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae79cc: mov             x3, x0
    // 0xae79d0: r2 = Null
    //     0xae79d0: mov             x2, NULL
    // 0xae79d4: r1 = Null
    //     0xae79d4: mov             x1, NULL
    // 0xae79d8: stur            x3, [fp, #-8]
    // 0xae79dc: r8 = (dynamic this, bool?) => bool
    //     0xae79dc: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xae79e0: ldr             x8, [x8, #0x318]
    // 0xae79e4: r3 = Null
    //     0xae79e4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34d10] Null
    //     0xae79e8: ldr             x3, [x3, #0xd10]
    // 0xae79ec: r0 = DefaultTypeTest()
    //     0xae79ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae79f0: r16 = <Color?>
    //     0xae79f0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae79f4: ldr             x16, [x16, #0x98]
    // 0xae79f8: r30 = Instance_MaterialColor
    //     0xae79f8: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xae79fc: ldr             lr, [lr, #0xbf0]
    // 0xae7a00: stp             lr, x16, [SP, #8]
    // 0xae7a04: r16 = Instance_Color
    //     0xae7a04: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae7a08: str             x16, [SP]
    // 0xae7a0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae7a0c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae7a10: r0 = mode()
    //     0xae7a10: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae7a14: stur            x0, [fp, #-0x18]
    // 0xae7a18: r0 = Checkbox()
    //     0xae7a18: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xae7a1c: ldur            x1, [fp, #-0x10]
    // 0xae7a20: StoreField: r0->field_b = r1
    //     0xae7a20: stur            w1, [x0, #0xb]
    // 0xae7a24: r1 = false
    //     0xae7a24: add             x1, NULL, #0x30  ; false
    // 0xae7a28: StoreField: r0->field_23 = r1
    //     0xae7a28: stur            w1, [x0, #0x23]
    // 0xae7a2c: ldur            x2, [fp, #-8]
    // 0xae7a30: StoreField: r0->field_f = r2
    //     0xae7a30: stur            w2, [x0, #0xf]
    // 0xae7a34: ldur            x2, [fp, #-0x18]
    // 0xae7a38: StoreField: r0->field_1f = r2
    //     0xae7a38: stur            w2, [x0, #0x1f]
    // 0xae7a3c: r2 = Instance_VisualDensity
    //     0xae7a3c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xae7a40: ldr             x2, [x2, #0xb68]
    // 0xae7a44: StoreField: r0->field_2b = r2
    //     0xae7a44: stur            w2, [x0, #0x2b]
    // 0xae7a48: StoreField: r0->field_43 = r1
    //     0xae7a48: stur            w1, [x0, #0x43]
    // 0xae7a4c: StoreField: r0->field_4f = r1
    //     0xae7a4c: stur            w1, [x0, #0x4f]
    // 0xae7a50: r1 = Instance__CheckboxType
    //     0xae7a50: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xae7a54: ldr             x1, [x1, #0xb70]
    // 0xae7a58: StoreField: r0->field_57 = r1
    //     0xae7a58: stur            w1, [x0, #0x57]
    // 0xae7a5c: LeaveFrame
    //     0xae7a5c: mov             SP, fp
    //     0xae7a60: ldp             fp, lr, [SP], #0x10
    // 0xae7a64: ret
    //     0xae7a64: ret             
    // 0xae7a68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7a68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7a6c: b               #0xae7980
  }
  [closure] GridView <anonymous closure>(dynamic) {
    // ** addr: 0xae7a70, size: 0x110
    // 0xae7a70: EnterFrame
    //     0xae7a70: stp             fp, lr, [SP, #-0x10]!
    //     0xae7a74: mov             fp, SP
    // 0xae7a78: AllocStack(0x28)
    //     0xae7a78: sub             SP, SP, #0x28
    // 0xae7a7c: SetupParameters()
    //     0xae7a7c: ldr             x0, [fp, #0x10]
    //     0xae7a80: ldur            w2, [x0, #0x17]
    //     0xae7a84: add             x2, x2, HEAP, lsl #32
    //     0xae7a88: stur            x2, [fp, #-8]
    // 0xae7a8c: CheckStackOverflow
    //     0xae7a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7a90: cmp             SP, x16
    //     0xae7a94: b.ls            #0xae7b78
    // 0xae7a98: LoadField: r1 = r2->field_13
    //     0xae7a98: ldur            w1, [x2, #0x13]
    // 0xae7a9c: DecompressPointer r1
    //     0xae7a9c: add             x1, x1, HEAP, lsl #32
    // 0xae7aa0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae7aa0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae7aa4: r0 = _of()
    //     0xae7aa4: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae7aa8: LoadField: r1 = r0->field_7
    //     0xae7aa8: ldur            w1, [x0, #7]
    // 0xae7aac: DecompressPointer r1
    //     0xae7aac: add             x1, x1, HEAP, lsl #32
    // 0xae7ab0: r0 = shortestSide()
    //     0xae7ab0: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0xae7ab4: mov             v1.16b, v0.16b
    // 0xae7ab8: d0 = 600.000000
    //     0xae7ab8: add             x17, PP, #0x34, lsl #12  ; [pp+0x34d20] IMM: double(600) from 0x4082c00000000000
    //     0xae7abc: ldr             d0, [x17, #0xd20]
    // 0xae7ac0: fcmp            d1, d0
    // 0xae7ac4: b.le            #0xae7ad0
    // 0xae7ac8: d0 = 4.000000
    //     0xae7ac8: fmov            d0, #4.00000000
    // 0xae7acc: b               #0xae7ad4
    // 0xae7ad0: d0 = 2.000000
    //     0xae7ad0: fmov            d0, #2.00000000
    // 0xae7ad4: ldur            x2, [fp, #-8]
    // 0xae7ad8: stur            d0, [fp, #-0x10]
    // 0xae7adc: LoadField: r1 = r2->field_f
    //     0xae7adc: ldur            w1, [x2, #0xf]
    // 0xae7ae0: DecompressPointer r1
    //     0xae7ae0: add             x1, x1, HEAP, lsl #32
    // 0xae7ae4: r0 = controller()
    //     0xae7ae4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7ae8: r1 = const [0x2710, 0x61a8, 0x88b8, 0xc350, 0x11170, 0x186a0]
    //     0xae7ae8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30060] List<int>(6)
    //     0xae7aec: ldr             x1, [x1, #0x60]
    // 0xae7af0: r0 = asMap()
    //     0xae7af0: bl              #0x6dc898  ; [dart:collection] ListBase::asMap
    // 0xae7af4: mov             x1, x0
    // 0xae7af8: r0 = entries()
    //     0xae7af8: bl              #0x7f814c  ; [dart:collection] MapBase::entries
    // 0xae7afc: ldur            x2, [fp, #-8]
    // 0xae7b00: r1 = Function '<anonymous closure>':.
    //     0xae7b00: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d28] AnonymousClosure: (0xae7b80), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] CampaignFormWidget::build (0xae705c)
    //     0xae7b04: ldr             x1, [x1, #0xd28]
    // 0xae7b08: stur            x0, [fp, #-8]
    // 0xae7b0c: r0 = AllocateClosure()
    //     0xae7b0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae7b10: r16 = <InkWell>
    //     0xae7b10: add             x16, PP, #0x34, lsl #12  ; [pp+0x34d30] TypeArguments: <InkWell>
    //     0xae7b14: ldr             x16, [x16, #0xd30]
    // 0xae7b18: ldur            lr, [fp, #-8]
    // 0xae7b1c: stp             lr, x16, [SP, #8]
    // 0xae7b20: str             x0, [SP]
    // 0xae7b24: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae7b24: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae7b28: r0 = map()
    //     0xae7b28: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0xae7b2c: LoadField: r1 = r0->field_7
    //     0xae7b2c: ldur            w1, [x0, #7]
    // 0xae7b30: DecompressPointer r1
    //     0xae7b30: add             x1, x1, HEAP, lsl #32
    // 0xae7b34: mov             x2, x0
    // 0xae7b38: r0 = _GrowableList.of()
    //     0xae7b38: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xae7b3c: stur            x0, [fp, #-8]
    // 0xae7b40: r0 = GridView()
    //     0xae7b40: bl              #0xad81c0  ; AllocateGridViewStub -> GridView (size=0x5c)
    // 0xae7b44: mov             x1, x0
    // 0xae7b48: ldur            d0, [fp, #-0x10]
    // 0xae7b4c: ldur            x2, [fp, #-8]
    // 0xae7b50: r3 = 3
    //     0xae7b50: movz            x3, #0x3
    // 0xae7b54: r5 = Instance_ScrollPhysics
    //     0xae7b54: add             x5, PP, #0x34, lsl #12  ; [pp+0x34d38] Obj!ScrollPhysics@e0fd31
    //     0xae7b58: ldr             x5, [x5, #0xd38]
    // 0xae7b5c: stur            x0, [fp, #-8]
    // 0xae7b60: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xae7b60: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xae7b64: r0 = GridView.count()
    //     0xae7b64: bl              #0xae2ac4  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.count
    // 0xae7b68: ldur            x0, [fp, #-8]
    // 0xae7b6c: LeaveFrame
    //     0xae7b6c: mov             SP, fp
    //     0xae7b70: ldp             fp, lr, [SP], #0x10
    // 0xae7b74: ret
    //     0xae7b74: ret             
    // 0xae7b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7b7c: b               #0xae7a98
  }
  [closure] InkWell <anonymous closure>(dynamic, MapEntry<int, int>) {
    // ** addr: 0xae7b80, size: 0x55c
    // 0xae7b80: EnterFrame
    //     0xae7b80: stp             fp, lr, [SP, #-0x10]!
    //     0xae7b84: mov             fp, SP
    // 0xae7b88: AllocStack(0x70)
    //     0xae7b88: sub             SP, SP, #0x70
    // 0xae7b8c: SetupParameters()
    //     0xae7b8c: ldr             x0, [fp, #0x18]
    //     0xae7b90: ldur            w1, [x0, #0x17]
    //     0xae7b94: add             x1, x1, HEAP, lsl #32
    //     0xae7b98: stur            x1, [fp, #-8]
    // 0xae7b9c: CheckStackOverflow
    //     0xae7b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7ba0: cmp             SP, x16
    //     0xae7ba4: b.ls            #0xae80c0
    // 0xae7ba8: r1 = 1
    //     0xae7ba8: movz            x1, #0x1
    // 0xae7bac: r0 = AllocateContext()
    //     0xae7bac: bl              #0xec126c  ; AllocateContextStub
    // 0xae7bb0: mov             x2, x0
    // 0xae7bb4: ldur            x0, [fp, #-8]
    // 0xae7bb8: stur            x2, [fp, #-0x10]
    // 0xae7bbc: StoreField: r2->field_b = r0
    //     0xae7bbc: stur            w0, [x2, #0xb]
    // 0xae7bc0: ldr             x3, [fp, #0x10]
    // 0xae7bc4: StoreField: r2->field_f = r3
    //     0xae7bc4: stur            w3, [x2, #0xf]
    // 0xae7bc8: LoadField: r1 = r0->field_f
    //     0xae7bc8: ldur            w1, [x0, #0xf]
    // 0xae7bcc: DecompressPointer r1
    //     0xae7bcc: add             x1, x1, HEAP, lsl #32
    // 0xae7bd0: r0 = controller()
    //     0xae7bd0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7bd4: LoadField: r1 = r0->field_23
    //     0xae7bd4: ldur            w1, [x0, #0x23]
    // 0xae7bd8: DecompressPointer r1
    //     0xae7bd8: add             x1, x1, HEAP, lsl #32
    // 0xae7bdc: r0 = value()
    //     0xae7bdc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae7be0: mov             x1, x0
    // 0xae7be4: ldr             x0, [fp, #0x10]
    // 0xae7be8: LoadField: r2 = r0->field_f
    //     0xae7be8: ldur            w2, [x0, #0xf]
    // 0xae7bec: DecompressPointer r2
    //     0xae7bec: add             x2, x2, HEAP, lsl #32
    // 0xae7bf0: stur            x2, [fp, #-0x18]
    // 0xae7bf4: stp             x2, x1, [SP]
    // 0xae7bf8: r0 = ==()
    //     0xae7bf8: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xae7bfc: r1 = _ConstMap len:10
    //     0xae7bfc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xae7c00: ldr             x1, [x1, #0xc08]
    // 0xae7c04: r2 = 100
    //     0xae7c04: movz            x2, #0x64
    // 0xae7c08: stur            x0, [fp, #-0x20]
    // 0xae7c0c: r0 = []()
    //     0xae7c0c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7c10: r1 = _ConstMap len:10
    //     0xae7c10: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xae7c14: ldr             x1, [x1, #0xc08]
    // 0xae7c18: r2 = 1800
    //     0xae7c18: movz            x2, #0x708
    // 0xae7c1c: stur            x0, [fp, #-0x28]
    // 0xae7c20: r0 = []()
    //     0xae7c20: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7c24: r16 = <Color?>
    //     0xae7c24: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae7c28: ldr             x16, [x16, #0x98]
    // 0xae7c2c: stp             x0, x16, [SP, #8]
    // 0xae7c30: ldur            x16, [fp, #-0x28]
    // 0xae7c34: str             x16, [SP]
    // 0xae7c38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae7c38: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae7c3c: r0 = mode()
    //     0xae7c3c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae7c40: mov             x1, x0
    // 0xae7c44: ldur            x0, [fp, #-0x20]
    // 0xae7c48: tbnz            w0, #4, #0xae7c54
    // 0xae7c4c: mov             x3, x1
    // 0xae7c50: b               #0xae7c58
    // 0xae7c54: r3 = Null
    //     0xae7c54: mov             x3, NULL
    // 0xae7c58: ldr             x0, [fp, #0x10]
    // 0xae7c5c: ldur            x2, [fp, #-8]
    // 0xae7c60: stur            x3, [fp, #-0x20]
    // 0xae7c64: LoadField: r1 = r2->field_f
    //     0xae7c64: ldur            w1, [x2, #0xf]
    // 0xae7c68: DecompressPointer r1
    //     0xae7c68: add             x1, x1, HEAP, lsl #32
    // 0xae7c6c: r0 = controller()
    //     0xae7c6c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7c70: LoadField: r1 = r0->field_23
    //     0xae7c70: ldur            w1, [x0, #0x23]
    // 0xae7c74: DecompressPointer r1
    //     0xae7c74: add             x1, x1, HEAP, lsl #32
    // 0xae7c78: r0 = value()
    //     0xae7c78: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae7c7c: ldur            x16, [fp, #-0x18]
    // 0xae7c80: stp             x16, x0, [SP]
    // 0xae7c84: r0 = ==()
    //     0xae7c84: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xae7c88: r1 = _ConstMap len:10
    //     0xae7c88: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xae7c8c: ldr             x1, [x1, #0xc08]
    // 0xae7c90: r2 = 600
    //     0xae7c90: movz            x2, #0x258
    // 0xae7c94: stur            x0, [fp, #-0x28]
    // 0xae7c98: r0 = []()
    //     0xae7c98: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7c9c: cmp             w0, NULL
    // 0xae7ca0: b.eq            #0xae80c8
    // 0xae7ca4: r16 = <Color>
    //     0xae7ca4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xae7ca8: ldr             x16, [x16, #0x158]
    // 0xae7cac: stp             x0, x16, [SP, #8]
    // 0xae7cb0: r16 = Instance_MaterialColor
    //     0xae7cb0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xae7cb4: ldr             x16, [x16, #0xcf0]
    // 0xae7cb8: str             x16, [SP]
    // 0xae7cbc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae7cbc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae7cc0: r0 = mode()
    //     0xae7cc0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae7cc4: r16 = 1.500000
    //     0xae7cc4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c58] 1.5
    //     0xae7cc8: ldr             x16, [x16, #0xc58]
    // 0xae7ccc: stp             x16, x0, [SP]
    // 0xae7cd0: r1 = Null
    //     0xae7cd0: mov             x1, NULL
    // 0xae7cd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, width, 0x2, null]
    //     0xae7cd4: add             x4, PP, #0x33, lsl #12  ; [pp+0x333a8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "width", 0x2, Null]
    //     0xae7cd8: ldr             x4, [x4, #0x3a8]
    // 0xae7cdc: r0 = Border.all()
    //     0xae7cdc: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xae7ce0: r1 = _ConstMap len:6
    //     0xae7ce0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xae7ce4: ldr             x1, [x1, #0xc20]
    // 0xae7ce8: r2 = 4
    //     0xae7ce8: movz            x2, #0x4
    // 0xae7cec: stur            x0, [fp, #-0x30]
    // 0xae7cf0: r0 = []()
    //     0xae7cf0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7cf4: stur            x0, [fp, #-0x38]
    // 0xae7cf8: cmp             w0, NULL
    // 0xae7cfc: b.eq            #0xae80cc
    // 0xae7d00: r0 = BorderSide()
    //     0xae7d00: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xae7d04: mov             x3, x0
    // 0xae7d08: ldur            x0, [fp, #-0x38]
    // 0xae7d0c: stur            x3, [fp, #-0x40]
    // 0xae7d10: StoreField: r3->field_7 = r0
    //     0xae7d10: stur            w0, [x3, #7]
    // 0xae7d14: d0 = 1.000000
    //     0xae7d14: fmov            d0, #1.00000000
    // 0xae7d18: StoreField: r3->field_b = d0
    //     0xae7d18: stur            d0, [x3, #0xb]
    // 0xae7d1c: r0 = Instance_BorderStyle
    //     0xae7d1c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xae7d20: ldr             x0, [x0, #0x2d0]
    // 0xae7d24: StoreField: r3->field_13 = r0
    //     0xae7d24: stur            w0, [x3, #0x13]
    // 0xae7d28: d1 = -1.000000
    //     0xae7d28: fmov            d1, #-1.00000000
    // 0xae7d2c: ArrayStore: r3[0] = d1  ; List_8
    //     0xae7d2c: stur            d1, [x3, #0x17]
    // 0xae7d30: r1 = _ConstMap len:6
    //     0xae7d30: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xae7d34: ldr             x1, [x1, #0xc20]
    // 0xae7d38: r2 = 4
    //     0xae7d38: movz            x2, #0x4
    // 0xae7d3c: r0 = []()
    //     0xae7d3c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7d40: stur            x0, [fp, #-0x38]
    // 0xae7d44: cmp             w0, NULL
    // 0xae7d48: b.eq            #0xae80d0
    // 0xae7d4c: r0 = BorderSide()
    //     0xae7d4c: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xae7d50: mov             x1, x0
    // 0xae7d54: ldur            x0, [fp, #-0x38]
    // 0xae7d58: stur            x1, [fp, #-0x48]
    // 0xae7d5c: StoreField: r1->field_7 = r0
    //     0xae7d5c: stur            w0, [x1, #7]
    // 0xae7d60: d0 = 1.000000
    //     0xae7d60: fmov            d0, #1.00000000
    // 0xae7d64: StoreField: r1->field_b = d0
    //     0xae7d64: stur            d0, [x1, #0xb]
    // 0xae7d68: r2 = Instance_BorderStyle
    //     0xae7d68: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xae7d6c: ldr             x2, [x2, #0x2d0]
    // 0xae7d70: StoreField: r1->field_13 = r2
    //     0xae7d70: stur            w2, [x1, #0x13]
    // 0xae7d74: d1 = -1.000000
    //     0xae7d74: fmov            d1, #-1.00000000
    // 0xae7d78: ArrayStore: r1[0] = d1  ; List_8
    //     0xae7d78: stur            d1, [x1, #0x17]
    // 0xae7d7c: ldr             x0, [fp, #0x10]
    // 0xae7d80: LoadField: r3 = r0->field_b
    //     0xae7d80: ldur            w3, [x0, #0xb]
    // 0xae7d84: DecompressPointer r3
    //     0xae7d84: add             x3, x3, HEAP, lsl #32
    // 0xae7d88: stur            x3, [fp, #-0x38]
    // 0xae7d8c: r0 = 60
    //     0xae7d8c: movz            x0, #0x3c
    // 0xae7d90: branchIfSmi(r3, 0xae7d9c)
    //     0xae7d90: tbz             w3, #0, #0xae7d9c
    // 0xae7d94: r0 = LoadClassIdInstr(r3)
    //     0xae7d94: ldur            x0, [x3, #-1]
    //     0xae7d98: ubfx            x0, x0, #0xc, #0x14
    // 0xae7d9c: r16 = 4
    //     0xae7d9c: movz            x16, #0x4
    // 0xae7da0: stp             x16, x3, [SP]
    // 0xae7da4: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xae7da4: sub             lr, x0, #0xfe6
    //     0xae7da8: ldr             lr, [x21, lr, lsl #3]
    //     0xae7dac: blr             lr
    // 0xae7db0: r1 = _ConstMap len:6
    //     0xae7db0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xae7db4: ldr             x1, [x1, #0xc20]
    // 0xae7db8: r2 = 4
    //     0xae7db8: movz            x2, #0x4
    // 0xae7dbc: stur            x0, [fp, #-0x50]
    // 0xae7dc0: r0 = []()
    //     0xae7dc0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7dc4: stur            x0, [fp, #-0x58]
    // 0xae7dc8: cmp             w0, NULL
    // 0xae7dcc: b.eq            #0xae80d4
    // 0xae7dd0: r0 = BorderSide()
    //     0xae7dd0: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xae7dd4: mov             x1, x0
    // 0xae7dd8: ldur            x0, [fp, #-0x58]
    // 0xae7ddc: StoreField: r1->field_7 = r0
    //     0xae7ddc: stur            w0, [x1, #7]
    // 0xae7de0: d0 = 1.000000
    //     0xae7de0: fmov            d0, #1.00000000
    // 0xae7de4: StoreField: r1->field_b = d0
    //     0xae7de4: stur            d0, [x1, #0xb]
    // 0xae7de8: r0 = Instance_BorderStyle
    //     0xae7de8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xae7dec: ldr             x0, [x0, #0x2d0]
    // 0xae7df0: StoreField: r1->field_13 = r0
    //     0xae7df0: stur            w0, [x1, #0x13]
    // 0xae7df4: d1 = -1.000000
    //     0xae7df4: fmov            d1, #-1.00000000
    // 0xae7df8: ArrayStore: r1[0] = d1  ; List_8
    //     0xae7df8: stur            d1, [x1, #0x17]
    // 0xae7dfc: ldur            x2, [fp, #-0x50]
    // 0xae7e00: tbnz            w2, #4, #0xae7e0c
    // 0xae7e04: mov             x3, x1
    // 0xae7e08: b               #0xae7e14
    // 0xae7e0c: r3 = Instance_BorderSide
    //     0xae7e0c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34d40] Obj!BorderSide@e1c131
    //     0xae7e10: ldr             x3, [x3, #0xd40]
    // 0xae7e14: ldur            x1, [fp, #-0x38]
    // 0xae7e18: stur            x3, [fp, #-0x50]
    // 0xae7e1c: cbnz            w1, #0xae7e28
    // 0xae7e20: r4 = true
    //     0xae7e20: add             x4, NULL, #0x20  ; true
    // 0xae7e24: b               #0xae7e3c
    // 0xae7e28: cmp             w1, #6
    // 0xae7e2c: r16 = true
    //     0xae7e2c: add             x16, NULL, #0x20  ; true
    // 0xae7e30: r17 = false
    //     0xae7e30: add             x17, NULL, #0x30  ; false
    // 0xae7e34: csel            x2, x16, x17, eq
    // 0xae7e38: mov             x4, x2
    // 0xae7e3c: stur            x4, [fp, #-0x38]
    // 0xae7e40: r1 = _ConstMap len:6
    //     0xae7e40: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xae7e44: ldr             x1, [x1, #0xc20]
    // 0xae7e48: r2 = 4
    //     0xae7e48: movz            x2, #0x4
    // 0xae7e4c: r0 = []()
    //     0xae7e4c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xae7e50: stur            x0, [fp, #-0x58]
    // 0xae7e54: cmp             w0, NULL
    // 0xae7e58: b.eq            #0xae80d8
    // 0xae7e5c: r0 = BorderSide()
    //     0xae7e5c: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xae7e60: mov             x1, x0
    // 0xae7e64: ldur            x0, [fp, #-0x58]
    // 0xae7e68: StoreField: r1->field_7 = r0
    //     0xae7e68: stur            w0, [x1, #7]
    // 0xae7e6c: d0 = 1.000000
    //     0xae7e6c: fmov            d0, #1.00000000
    // 0xae7e70: StoreField: r1->field_b = d0
    //     0xae7e70: stur            d0, [x1, #0xb]
    // 0xae7e74: r0 = Instance_BorderStyle
    //     0xae7e74: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xae7e78: ldr             x0, [x0, #0x2d0]
    // 0xae7e7c: StoreField: r1->field_13 = r0
    //     0xae7e7c: stur            w0, [x1, #0x13]
    // 0xae7e80: d0 = -1.000000
    //     0xae7e80: fmov            d0, #-1.00000000
    // 0xae7e84: ArrayStore: r1[0] = d0  ; List_8
    //     0xae7e84: stur            d0, [x1, #0x17]
    // 0xae7e88: ldur            x0, [fp, #-0x38]
    // 0xae7e8c: tbnz            w0, #4, #0xae7e98
    // 0xae7e90: mov             x4, x1
    // 0xae7e94: b               #0xae7ea0
    // 0xae7e98: r4 = Instance_BorderSide
    //     0xae7e98: add             x4, PP, #0x34, lsl #12  ; [pp+0x34d40] Obj!BorderSide@e1c131
    //     0xae7e9c: ldr             x4, [x4, #0xd40]
    // 0xae7ea0: ldur            x3, [fp, #-0x28]
    // 0xae7ea4: ldur            x2, [fp, #-0x40]
    // 0xae7ea8: ldur            x1, [fp, #-0x48]
    // 0xae7eac: ldur            x0, [fp, #-0x50]
    // 0xae7eb0: stur            x4, [fp, #-0x38]
    // 0xae7eb4: r0 = Border()
    //     0xae7eb4: bl              #0x87dce8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xae7eb8: mov             x1, x0
    // 0xae7ebc: ldur            x0, [fp, #-0x50]
    // 0xae7ec0: StoreField: r1->field_7 = r0
    //     0xae7ec0: stur            w0, [x1, #7]
    // 0xae7ec4: ldur            x0, [fp, #-0x48]
    // 0xae7ec8: StoreField: r1->field_b = r0
    //     0xae7ec8: stur            w0, [x1, #0xb]
    // 0xae7ecc: ldur            x0, [fp, #-0x40]
    // 0xae7ed0: StoreField: r1->field_f = r0
    //     0xae7ed0: stur            w0, [x1, #0xf]
    // 0xae7ed4: ldur            x0, [fp, #-0x38]
    // 0xae7ed8: StoreField: r1->field_13 = r0
    //     0xae7ed8: stur            w0, [x1, #0x13]
    // 0xae7edc: ldur            x0, [fp, #-0x28]
    // 0xae7ee0: tbnz            w0, #4, #0xae7eec
    // 0xae7ee4: ldur            x2, [fp, #-0x30]
    // 0xae7ee8: b               #0xae7ef0
    // 0xae7eec: mov             x2, x1
    // 0xae7ef0: ldur            x0, [fp, #-8]
    // 0xae7ef4: ldur            x1, [fp, #-0x20]
    // 0xae7ef8: stur            x2, [fp, #-0x28]
    // 0xae7efc: r0 = BoxDecoration()
    //     0xae7efc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae7f00: mov             x2, x0
    // 0xae7f04: ldur            x0, [fp, #-0x20]
    // 0xae7f08: stur            x2, [fp, #-0x30]
    // 0xae7f0c: StoreField: r2->field_7 = r0
    //     0xae7f0c: stur            w0, [x2, #7]
    // 0xae7f10: ldur            x0, [fp, #-0x28]
    // 0xae7f14: StoreField: r2->field_f = r0
    //     0xae7f14: stur            w0, [x2, #0xf]
    // 0xae7f18: r0 = Instance_BoxShape
    //     0xae7f18: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xae7f1c: ldr             x0, [x0, #0xca8]
    // 0xae7f20: StoreField: r2->field_23 = r0
    //     0xae7f20: stur            w0, [x2, #0x23]
    // 0xae7f24: ldur            x1, [fp, #-0x18]
    // 0xae7f28: r0 = IntExtension.currency()
    //     0xae7f28: bl              #0xae80dc  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.currency
    // 0xae7f2c: mov             x2, x0
    // 0xae7f30: ldur            x0, [fp, #-8]
    // 0xae7f34: stur            x2, [fp, #-0x20]
    // 0xae7f38: LoadField: r1 = r0->field_f
    //     0xae7f38: ldur            w1, [x0, #0xf]
    // 0xae7f3c: DecompressPointer r1
    //     0xae7f3c: add             x1, x1, HEAP, lsl #32
    // 0xae7f40: r0 = controller()
    //     0xae7f40: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae7f44: LoadField: r1 = r0->field_23
    //     0xae7f44: ldur            w1, [x0, #0x23]
    // 0xae7f48: DecompressPointer r1
    //     0xae7f48: add             x1, x1, HEAP, lsl #32
    // 0xae7f4c: r0 = value()
    //     0xae7f4c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae7f50: ldur            x16, [fp, #-0x18]
    // 0xae7f54: stp             x16, x0, [SP]
    // 0xae7f58: r0 = ==()
    //     0xae7f58: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xae7f5c: tbnz            w0, #4, #0xae7f6c
    // 0xae7f60: r1 = Instance_TextStyle
    //     0xae7f60: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d48] Obj!TextStyle@e1b6c1
    //     0xae7f64: ldr             x1, [x1, #0xd48]
    // 0xae7f68: b               #0xae7f70
    // 0xae7f6c: r1 = Null
    //     0xae7f6c: mov             x1, NULL
    // 0xae7f70: ldur            x0, [fp, #-0x20]
    // 0xae7f74: stur            x1, [fp, #-8]
    // 0xae7f78: r0 = Text()
    //     0xae7f78: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae7f7c: mov             x3, x0
    // 0xae7f80: ldur            x0, [fp, #-0x20]
    // 0xae7f84: stur            x3, [fp, #-0x18]
    // 0xae7f88: StoreField: r3->field_b = r0
    //     0xae7f88: stur            w0, [x3, #0xb]
    // 0xae7f8c: ldur            x0, [fp, #-8]
    // 0xae7f90: StoreField: r3->field_13 = r0
    //     0xae7f90: stur            w0, [x3, #0x13]
    // 0xae7f94: r1 = Null
    //     0xae7f94: mov             x1, NULL
    // 0xae7f98: r2 = 2
    //     0xae7f98: movz            x2, #0x2
    // 0xae7f9c: r0 = AllocateArray()
    //     0xae7f9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae7fa0: mov             x2, x0
    // 0xae7fa4: ldur            x0, [fp, #-0x18]
    // 0xae7fa8: stur            x2, [fp, #-8]
    // 0xae7fac: StoreField: r2->field_f = r0
    //     0xae7fac: stur            w0, [x2, #0xf]
    // 0xae7fb0: r1 = <Widget>
    //     0xae7fb0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae7fb4: r0 = AllocateGrowableArray()
    //     0xae7fb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae7fb8: mov             x1, x0
    // 0xae7fbc: ldur            x0, [fp, #-8]
    // 0xae7fc0: stur            x1, [fp, #-0x18]
    // 0xae7fc4: StoreField: r1->field_f = r0
    //     0xae7fc4: stur            w0, [x1, #0xf]
    // 0xae7fc8: r0 = 2
    //     0xae7fc8: movz            x0, #0x2
    // 0xae7fcc: StoreField: r1->field_b = r0
    //     0xae7fcc: stur            w0, [x1, #0xb]
    // 0xae7fd0: r0 = Column()
    //     0xae7fd0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae7fd4: mov             x1, x0
    // 0xae7fd8: r0 = Instance_Axis
    //     0xae7fd8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xae7fdc: stur            x1, [fp, #-8]
    // 0xae7fe0: StoreField: r1->field_f = r0
    //     0xae7fe0: stur            w0, [x1, #0xf]
    // 0xae7fe4: r0 = Instance_MainAxisAlignment
    //     0xae7fe4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xae7fe8: ldr             x0, [x0, #0x290]
    // 0xae7fec: StoreField: r1->field_13 = r0
    //     0xae7fec: stur            w0, [x1, #0x13]
    // 0xae7ff0: r0 = Instance_MainAxisSize
    //     0xae7ff0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae7ff4: ldr             x0, [x0, #0x738]
    // 0xae7ff8: ArrayStore: r1[0] = r0  ; List_4
    //     0xae7ff8: stur            w0, [x1, #0x17]
    // 0xae7ffc: r0 = Instance_CrossAxisAlignment
    //     0xae7ffc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae8000: ldr             x0, [x0, #0x740]
    // 0xae8004: StoreField: r1->field_1b = r0
    //     0xae8004: stur            w0, [x1, #0x1b]
    // 0xae8008: r0 = Instance_VerticalDirection
    //     0xae8008: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae800c: ldr             x0, [x0, #0x748]
    // 0xae8010: StoreField: r1->field_23 = r0
    //     0xae8010: stur            w0, [x1, #0x23]
    // 0xae8014: r0 = Instance_Clip
    //     0xae8014: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae8018: ldr             x0, [x0, #0x750]
    // 0xae801c: StoreField: r1->field_2b = r0
    //     0xae801c: stur            w0, [x1, #0x2b]
    // 0xae8020: StoreField: r1->field_2f = rZR
    //     0xae8020: stur            xzr, [x1, #0x2f]
    // 0xae8024: ldur            x0, [fp, #-0x18]
    // 0xae8028: StoreField: r1->field_b = r0
    //     0xae8028: stur            w0, [x1, #0xb]
    // 0xae802c: r0 = Container()
    //     0xae802c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae8030: stur            x0, [fp, #-0x18]
    // 0xae8034: ldur            x16, [fp, #-0x30]
    // 0xae8038: r30 = Instance_EdgeInsets
    //     0xae8038: add             lr, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xae803c: ldr             lr, [lr, #0xde8]
    // 0xae8040: stp             lr, x16, [SP, #8]
    // 0xae8044: ldur            x16, [fp, #-8]
    // 0xae8048: str             x16, [SP]
    // 0xae804c: mov             x1, x0
    // 0xae8050: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xae8050: add             x4, PP, #0x32, lsl #12  ; [pp+0x323e0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xae8054: ldr             x4, [x4, #0x3e0]
    // 0xae8058: r0 = Container()
    //     0xae8058: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae805c: r0 = InkWell()
    //     0xae805c: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xae8060: mov             x3, x0
    // 0xae8064: ldur            x0, [fp, #-0x18]
    // 0xae8068: stur            x3, [fp, #-8]
    // 0xae806c: StoreField: r3->field_b = r0
    //     0xae806c: stur            w0, [x3, #0xb]
    // 0xae8070: ldur            x2, [fp, #-0x10]
    // 0xae8074: r1 = Function '<anonymous closure>':.
    //     0xae8074: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d50] AnonymousClosure: (0xae812c), in [package:nuonline/app/modules/donation/widgets/campaign_form.dart] CampaignFormWidget::build (0xae705c)
    //     0xae8078: ldr             x1, [x1, #0xd50]
    // 0xae807c: r0 = AllocateClosure()
    //     0xae807c: bl              #0xec1630  ; AllocateClosureStub
    // 0xae8080: mov             x1, x0
    // 0xae8084: ldur            x0, [fp, #-8]
    // 0xae8088: StoreField: r0->field_f = r1
    //     0xae8088: stur            w1, [x0, #0xf]
    // 0xae808c: r1 = true
    //     0xae808c: add             x1, NULL, #0x20  ; true
    // 0xae8090: StoreField: r0->field_43 = r1
    //     0xae8090: stur            w1, [x0, #0x43]
    // 0xae8094: r2 = Instance_BoxShape
    //     0xae8094: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xae8098: ldr             x2, [x2, #0xca8]
    // 0xae809c: StoreField: r0->field_47 = r2
    //     0xae809c: stur            w2, [x0, #0x47]
    // 0xae80a0: StoreField: r0->field_6f = r1
    //     0xae80a0: stur            w1, [x0, #0x6f]
    // 0xae80a4: r2 = false
    //     0xae80a4: add             x2, NULL, #0x30  ; false
    // 0xae80a8: StoreField: r0->field_73 = r2
    //     0xae80a8: stur            w2, [x0, #0x73]
    // 0xae80ac: StoreField: r0->field_83 = r1
    //     0xae80ac: stur            w1, [x0, #0x83]
    // 0xae80b0: StoreField: r0->field_7b = r2
    //     0xae80b0: stur            w2, [x0, #0x7b]
    // 0xae80b4: LeaveFrame
    //     0xae80b4: mov             SP, fp
    //     0xae80b8: ldp             fp, lr, [SP], #0x10
    // 0xae80bc: ret
    //     0xae80bc: ret             
    // 0xae80c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae80c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae80c4: b               #0xae7ba8
    // 0xae80c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae80c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae80cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae80cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae80d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae80d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae80d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae80d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae80d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae80d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae812c, size: 0xb4
    // 0xae812c: EnterFrame
    //     0xae812c: stp             fp, lr, [SP, #-0x10]!
    //     0xae8130: mov             fp, SP
    // 0xae8134: AllocStack(0x18)
    //     0xae8134: sub             SP, SP, #0x18
    // 0xae8138: SetupParameters()
    //     0xae8138: ldr             x0, [fp, #0x10]
    //     0xae813c: ldur            w2, [x0, #0x17]
    //     0xae8140: add             x2, x2, HEAP, lsl #32
    //     0xae8144: stur            x2, [fp, #-0x10]
    // 0xae8148: CheckStackOverflow
    //     0xae8148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae814c: cmp             SP, x16
    //     0xae8150: b.ls            #0xae81d8
    // 0xae8154: LoadField: r0 = r2->field_b
    //     0xae8154: ldur            w0, [x2, #0xb]
    // 0xae8158: DecompressPointer r0
    //     0xae8158: add             x0, x0, HEAP, lsl #32
    // 0xae815c: stur            x0, [fp, #-8]
    // 0xae8160: LoadField: r1 = r0->field_f
    //     0xae8160: ldur            w1, [x0, #0xf]
    // 0xae8164: DecompressPointer r1
    //     0xae8164: add             x1, x1, HEAP, lsl #32
    // 0xae8168: r0 = controller()
    //     0xae8168: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae816c: mov             x1, x0
    // 0xae8170: ldur            x0, [fp, #-0x10]
    // 0xae8174: LoadField: r2 = r0->field_f
    //     0xae8174: ldur            w2, [x0, #0xf]
    // 0xae8178: DecompressPointer r2
    //     0xae8178: add             x2, x2, HEAP, lsl #32
    // 0xae817c: LoadField: r0 = r2->field_f
    //     0xae817c: ldur            w0, [x2, #0xf]
    // 0xae8180: DecompressPointer r0
    //     0xae8180: add             x0, x0, HEAP, lsl #32
    // 0xae8184: mov             x2, x0
    // 0xae8188: r0 = onSuggestionChanged()
    //     0xae8188: bl              #0xae821c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onSuggestionChanged
    // 0xae818c: ldur            x0, [fp, #-8]
    // 0xae8190: LoadField: r1 = r0->field_13
    //     0xae8190: ldur            w1, [x0, #0x13]
    // 0xae8194: DecompressPointer r1
    //     0xae8194: add             x1, x1, HEAP, lsl #32
    // 0xae8198: r0 = of()
    //     0xae8198: bl              #0x98f804  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xae819c: stur            x0, [fp, #-8]
    // 0xae81a0: r0 = FocusNode()
    //     0xae81a0: bl              #0x811904  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xae81a4: mov             x1, x0
    // 0xae81a8: stur            x0, [fp, #-0x10]
    // 0xae81ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae81ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae81b0: r0 = FocusNode()
    //     0xae81b0: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xae81b4: ldur            x16, [fp, #-0x10]
    // 0xae81b8: str             x16, [SP]
    // 0xae81bc: ldur            x1, [fp, #-8]
    // 0xae81c0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xae81c0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xae81c4: r0 = requestFocus()
    //     0xae81c4: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xae81c8: r0 = Null
    //     0xae81c8: mov             x0, NULL
    // 0xae81cc: LeaveFrame
    //     0xae81cc: mov             SP, fp
    //     0xae81d0: ldp             fp, lr, [SP], #0x10
    // 0xae81d4: ret
    //     0xae81d4: ret             
    // 0xae81d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae81d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae81dc: b               #0xae8154
  }
}
