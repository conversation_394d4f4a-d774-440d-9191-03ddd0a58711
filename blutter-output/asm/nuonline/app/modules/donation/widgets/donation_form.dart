// lib: , url: package:nuonline/app/modules/donation/widgets/donation_form.dart

// class id: 1050231, size: 0x8
class :: {
}

// class id: 5286, size: 0x14, field offset: 0x14
//   const constructor, 
class DonationFormWidget extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae98d4, size: 0x584
    // 0xae98d4: EnterFrame
    //     0xae98d4: stp             fp, lr, [SP, #-0x10]!
    //     0xae98d8: mov             fp, SP
    // 0xae98dc: AllocStack(0x70)
    //     0xae98dc: sub             SP, SP, #0x70
    // 0xae98e0: SetupParameters(DonationFormWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae98e0: stur            x1, [fp, #-8]
    //     0xae98e4: stur            x2, [fp, #-0x10]
    // 0xae98e8: CheckStackOverflow
    //     0xae98e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae98ec: cmp             SP, x16
    //     0xae98f0: b.ls            #0xae9e50
    // 0xae98f4: r1 = 2
    //     0xae98f4: movz            x1, #0x2
    // 0xae98f8: r0 = AllocateContext()
    //     0xae98f8: bl              #0xec126c  ; AllocateContextStub
    // 0xae98fc: mov             x3, x0
    // 0xae9900: ldur            x0, [fp, #-8]
    // 0xae9904: stur            x3, [fp, #-0x18]
    // 0xae9908: StoreField: r3->field_f = r0
    //     0xae9908: stur            w0, [x3, #0xf]
    // 0xae990c: ldur            x1, [fp, #-0x10]
    // 0xae9910: StoreField: r3->field_13 = r1
    //     0xae9910: stur            w1, [x3, #0x13]
    // 0xae9914: r1 = <Widget>
    //     0xae9914: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae9918: r2 = 22
    //     0xae9918: movz            x2, #0x16
    // 0xae991c: r0 = AllocateArray()
    //     0xae991c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae9920: stur            x0, [fp, #-0x10]
    // 0xae9924: r16 = Instance_NLabelTextField
    //     0xae9924: add             x16, PP, #0x35, lsl #12  ; [pp+0x35568] Obj!NLabelTextField@e1fbf1
    //     0xae9928: ldr             x16, [x16, #0x568]
    // 0xae992c: StoreField: r0->field_f = r16
    //     0xae992c: stur            w16, [x0, #0xf]
    // 0xae9930: r0 = Obx()
    //     0xae9930: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae9934: ldur            x2, [fp, #-0x18]
    // 0xae9938: r1 = Function '<anonymous closure>':.
    //     0xae9938: add             x1, PP, #0x35, lsl #12  ; [pp+0x35570] AnonymousClosure: (0xaea6c0), in [package:nuonline/app/modules/donation/widgets/donation_form.dart] DonationFormWidget::build (0xae98d4)
    //     0xae993c: ldr             x1, [x1, #0x570]
    // 0xae9940: stur            x0, [fp, #-0x20]
    // 0xae9944: r0 = AllocateClosure()
    //     0xae9944: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9948: mov             x1, x0
    // 0xae994c: ldur            x0, [fp, #-0x20]
    // 0xae9950: StoreField: r0->field_b = r1
    //     0xae9950: stur            w1, [x0, #0xb]
    // 0xae9954: ldur            x1, [fp, #-0x10]
    // 0xae9958: ArrayStore: r1[1] = r0  ; List_4
    //     0xae9958: add             x25, x1, #0x13
    //     0xae995c: str             w0, [x25]
    //     0xae9960: tbz             w0, #0, #0xae997c
    //     0xae9964: ldurb           w16, [x1, #-1]
    //     0xae9968: ldurb           w17, [x0, #-1]
    //     0xae996c: and             x16, x17, x16, lsr #2
    //     0xae9970: tst             x16, HEAP, lsr #32
    //     0xae9974: b.eq            #0xae997c
    //     0xae9978: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae997c: ldur            x0, [fp, #-0x10]
    // 0xae9980: r16 = Instance_SizedBox
    //     0xae9980: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae9984: ldr             x16, [x16, #0x950]
    // 0xae9988: ArrayStore: r0[0] = r16  ; List_4
    //     0xae9988: stur            w16, [x0, #0x17]
    // 0xae998c: ldur            x1, [fp, #-8]
    // 0xae9990: r0 = controller()
    //     0xae9990: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9994: LoadField: r2 = r0->field_33
    //     0xae9994: ldur            w2, [x0, #0x33]
    // 0xae9998: DecompressPointer r2
    //     0xae9998: add             x2, x2, HEAP, lsl #32
    // 0xae999c: ldur            x1, [fp, #-8]
    // 0xae99a0: stur            x2, [fp, #-0x20]
    // 0xae99a4: r0 = controller()
    //     0xae99a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae99a8: LoadField: r2 = r0->field_23
    //     0xae99a8: ldur            w2, [x0, #0x23]
    // 0xae99ac: DecompressPointer r2
    //     0xae99ac: add             x2, x2, HEAP, lsl #32
    // 0xae99b0: LoadField: r3 = r2->field_7
    //     0xae99b0: ldur            w3, [x2, #7]
    // 0xae99b4: DecompressPointer r3
    //     0xae99b4: add             x3, x3, HEAP, lsl #32
    // 0xae99b8: r1 = Function 'call':.
    //     0xae99b8: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae99bc: ldr             x1, [x1, #0x310]
    // 0xae99c0: r0 = AllocateClosureTA()
    //     0xae99c0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae99c4: mov             x3, x0
    // 0xae99c8: r2 = Null
    //     0xae99c8: mov             x2, NULL
    // 0xae99cc: r1 = Null
    //     0xae99cc: mov             x1, NULL
    // 0xae99d0: stur            x3, [fp, #-0x28]
    // 0xae99d4: r8 = (dynamic this, int?) => int
    //     0xae99d4: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a0a0] FunctionType: (dynamic this, int?) => int
    //     0xae99d8: ldr             x8, [x8, #0xa0]
    // 0xae99dc: r3 = Null
    //     0xae99dc: add             x3, PP, #0x35, lsl #12  ; [pp+0x35578] Null
    //     0xae99e0: ldr             x3, [x3, #0x578]
    // 0xae99e4: r0 = DefaultTypeTest()
    //     0xae99e4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae99e8: r1 = Function '<anonymous closure>': static.
    //     0xae99e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae99ec: ldr             x1, [x1, #0xda0]
    // 0xae99f0: r2 = Null
    //     0xae99f0: mov             x2, NULL
    // 0xae99f4: r0 = AllocateClosure()
    //     0xae99f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xae99f8: r1 = <String>
    //     0xae99f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae99fc: stur            x0, [fp, #-0x30]
    // 0xae9a00: StoreField: r0->field_b = r1
    //     0xae9a00: stur            w1, [x0, #0xb]
    // 0xae9a04: r16 = <String>
    //     0xae9a04: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae9a08: str             x16, [SP, #0x10]
    // 0xae9a0c: r2 = 10000
    //     0xae9a0c: movz            x2, #0x2710
    // 0xae9a10: r16 = "Minimal sedekah Rp10.000"
    //     0xae9a10: add             x16, PP, #0x34, lsl #12  ; [pp+0x34cd0] "Minimal sedekah Rp10.000"
    //     0xae9a14: ldr             x16, [x16, #0xcd0]
    // 0xae9a18: stp             x16, x2, [SP]
    // 0xae9a1c: r4 = const [0x1, 0x2, 0x2, 0x1, errorText, 0x1, null]
    //     0xae9a1c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34cd8] List(7) [0x1, 0x2, 0x2, 0x1, "errorText", 0x1, Null]
    //     0xae9a20: ldr             x4, [x4, #0xcd8]
    // 0xae9a24: r0 = min()
    //     0xae9a24: bl              #0xae7668  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::min
    // 0xae9a28: r1 = Null
    //     0xae9a28: mov             x1, NULL
    // 0xae9a2c: r2 = 4
    //     0xae9a2c: movz            x2, #0x4
    // 0xae9a30: stur            x0, [fp, #-0x38]
    // 0xae9a34: r0 = AllocateArray()
    //     0xae9a34: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae9a38: mov             x2, x0
    // 0xae9a3c: ldur            x0, [fp, #-0x30]
    // 0xae9a40: stur            x2, [fp, #-0x40]
    // 0xae9a44: StoreField: r2->field_f = r0
    //     0xae9a44: stur            w0, [x2, #0xf]
    // 0xae9a48: ldur            x0, [fp, #-0x38]
    // 0xae9a4c: StoreField: r2->field_13 = r0
    //     0xae9a4c: stur            w0, [x2, #0x13]
    // 0xae9a50: r1 = <(dynamic this, String?) => String?>
    //     0xae9a50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xae9a54: ldr             x1, [x1, #0xd98]
    // 0xae9a58: r0 = AllocateGrowableArray()
    //     0xae9a58: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae9a5c: mov             x1, x0
    // 0xae9a60: ldur            x0, [fp, #-0x40]
    // 0xae9a64: stur            x1, [fp, #-0x30]
    // 0xae9a68: StoreField: r1->field_f = r0
    //     0xae9a68: stur            w0, [x1, #0xf]
    // 0xae9a6c: r2 = 4
    //     0xae9a6c: movz            x2, #0x4
    // 0xae9a70: StoreField: r1->field_b = r2
    //     0xae9a70: stur            w2, [x1, #0xb]
    // 0xae9a74: r1 = 1
    //     0xae9a74: movz            x1, #0x1
    // 0xae9a78: r0 = AllocateContext()
    //     0xae9a78: bl              #0xec126c  ; AllocateContextStub
    // 0xae9a7c: mov             x1, x0
    // 0xae9a80: ldur            x0, [fp, #-0x30]
    // 0xae9a84: StoreField: r1->field_f = r0
    //     0xae9a84: stur            w0, [x1, #0xf]
    // 0xae9a88: mov             x2, x1
    // 0xae9a8c: r1 = Function '<anonymous closure>': static.
    //     0xae9a8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae9a90: ldr             x1, [x1, #0xda8]
    // 0xae9a94: r0 = AllocateClosure()
    //     0xae9a94: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9a98: r1 = <String>
    //     0xae9a98: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae9a9c: stur            x0, [fp, #-0x30]
    // 0xae9aa0: StoreField: r0->field_b = r1
    //     0xae9aa0: stur            w1, [x0, #0xb]
    // 0xae9aa4: r0 = NZakatTextField()
    //     0xae9aa4: bl              #0xae765c  ; AllocateNZakatTextFieldStub -> NZakatTextField (size=0x34)
    // 0xae9aa8: mov             x1, x0
    // 0xae9aac: ldur            x0, [fp, #-0x20]
    // 0xae9ab0: StoreField: r1->field_b = r0
    //     0xae9ab0: stur            w0, [x1, #0xb]
    // 0xae9ab4: r0 = "Nominal Sedekah (min Rp10.000)"
    //     0xae9ab4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35588] "Nominal Sedekah (min Rp10.000)"
    //     0xae9ab8: ldr             x0, [x0, #0x588]
    // 0xae9abc: StoreField: r1->field_f = r0
    //     0xae9abc: stur            w0, [x1, #0xf]
    // 0xae9ac0: ldur            x0, [fp, #-0x28]
    // 0xae9ac4: StoreField: r1->field_1b = r0
    //     0xae9ac4: stur            w0, [x1, #0x1b]
    // 0xae9ac8: r2 = false
    //     0xae9ac8: add             x2, NULL, #0x30  ; false
    // 0xae9acc: StoreField: r1->field_1f = r2
    //     0xae9acc: stur            w2, [x1, #0x1f]
    // 0xae9ad0: r3 = true
    //     0xae9ad0: add             x3, NULL, #0x20  ; true
    // 0xae9ad4: StoreField: r1->field_27 = r3
    //     0xae9ad4: stur            w3, [x1, #0x27]
    // 0xae9ad8: ldur            x0, [fp, #-0x30]
    // 0xae9adc: StoreField: r1->field_2b = r0
    //     0xae9adc: stur            w0, [x1, #0x2b]
    // 0xae9ae0: StoreField: r1->field_2f = r2
    //     0xae9ae0: stur            w2, [x1, #0x2f]
    // 0xae9ae4: mov             x0, x1
    // 0xae9ae8: ldur            x1, [fp, #-0x10]
    // 0xae9aec: ArrayStore: r1[3] = r0  ; List_4
    //     0xae9aec: add             x25, x1, #0x1b
    //     0xae9af0: str             w0, [x25]
    //     0xae9af4: tbz             w0, #0, #0xae9b10
    //     0xae9af8: ldurb           w16, [x1, #-1]
    //     0xae9afc: ldurb           w17, [x0, #-1]
    //     0xae9b00: and             x16, x17, x16, lsr #2
    //     0xae9b04: tst             x16, HEAP, lsr #32
    //     0xae9b08: b.eq            #0xae9b10
    //     0xae9b0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae9b10: ldur            x1, [fp, #-0x10]
    // 0xae9b14: r16 = Instance_SizedBox
    //     0xae9b14: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae9b18: ldr             x16, [x16, #0x950]
    // 0xae9b1c: StoreField: r1->field_1f = r16
    //     0xae9b1c: stur            w16, [x1, #0x1f]
    // 0xae9b20: r0 = Obx()
    //     0xae9b20: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae9b24: ldur            x2, [fp, #-0x18]
    // 0xae9b28: r1 = Function '<anonymous closure>':.
    //     0xae9b28: add             x1, PP, #0x35, lsl #12  ; [pp+0x35590] AnonymousClosure: (0xae9f70), in [package:nuonline/app/modules/donation/widgets/donation_form.dart] DonationFormWidget::build (0xae98d4)
    //     0xae9b2c: ldr             x1, [x1, #0x590]
    // 0xae9b30: stur            x0, [fp, #-0x20]
    // 0xae9b34: r0 = AllocateClosure()
    //     0xae9b34: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9b38: mov             x1, x0
    // 0xae9b3c: ldur            x0, [fp, #-0x20]
    // 0xae9b40: StoreField: r0->field_b = r1
    //     0xae9b40: stur            w1, [x0, #0xb]
    // 0xae9b44: ldur            x1, [fp, #-0x10]
    // 0xae9b48: ArrayStore: r1[5] = r0  ; List_4
    //     0xae9b48: add             x25, x1, #0x23
    //     0xae9b4c: str             w0, [x25]
    //     0xae9b50: tbz             w0, #0, #0xae9b6c
    //     0xae9b54: ldurb           w16, [x1, #-1]
    //     0xae9b58: ldurb           w17, [x0, #-1]
    //     0xae9b5c: and             x16, x17, x16, lsr #2
    //     0xae9b60: tst             x16, HEAP, lsr #32
    //     0xae9b64: b.eq            #0xae9b6c
    //     0xae9b68: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae9b6c: ldur            x0, [fp, #-0x10]
    // 0xae9b70: r16 = Instance_SizedBox
    //     0xae9b70: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae9b74: ldr             x16, [x16, #0x950]
    // 0xae9b78: StoreField: r0->field_27 = r16
    //     0xae9b78: stur            w16, [x0, #0x27]
    // 0xae9b7c: r16 = Instance_NLabelTextField
    //     0xae9b7c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b08] Obj!NLabelTextField@e1fbd1
    //     0xae9b80: ldr             x16, [x16, #0xb08]
    // 0xae9b84: StoreField: r0->field_2b = r16
    //     0xae9b84: stur            w16, [x0, #0x2b]
    // 0xae9b88: r1 = Function '<anonymous closure>': static.
    //     0xae9b88: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xae9b8c: ldr             x1, [x1, #0xda0]
    // 0xae9b90: r2 = Null
    //     0xae9b90: mov             x2, NULL
    // 0xae9b94: r0 = AllocateClosure()
    //     0xae9b94: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9b98: mov             x2, x0
    // 0xae9b9c: r0 = <String>
    //     0xae9b9c: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae9ba0: stur            x2, [fp, #-0x20]
    // 0xae9ba4: StoreField: r2->field_b = r0
    //     0xae9ba4: stur            w0, [x2, #0xb]
    // 0xae9ba8: ldur            x1, [fp, #-8]
    // 0xae9bac: r0 = controller()
    //     0xae9bac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9bb0: LoadField: r2 = r0->field_27
    //     0xae9bb0: ldur            w2, [x0, #0x27]
    // 0xae9bb4: DecompressPointer r2
    //     0xae9bb4: add             x2, x2, HEAP, lsl #32
    // 0xae9bb8: LoadField: r3 = r2->field_7
    //     0xae9bb8: ldur            w3, [x2, #7]
    // 0xae9bbc: DecompressPointer r3
    //     0xae9bbc: add             x3, x3, HEAP, lsl #32
    // 0xae9bc0: r1 = Function 'call':.
    //     0xae9bc0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae9bc4: ldr             x1, [x1, #0x310]
    // 0xae9bc8: r0 = AllocateClosureTA()
    //     0xae9bc8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae9bcc: mov             x3, x0
    // 0xae9bd0: r2 = Null
    //     0xae9bd0: mov             x2, NULL
    // 0xae9bd4: r1 = Null
    //     0xae9bd4: mov             x1, NULL
    // 0xae9bd8: stur            x3, [fp, #-0x28]
    // 0xae9bdc: r8 = (dynamic this, String?) => String
    //     0xae9bdc: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xae9be0: ldr             x8, [x8, #0x40]
    // 0xae9be4: r3 = Null
    //     0xae9be4: add             x3, PP, #0x35, lsl #12  ; [pp+0x35598] Null
    //     0xae9be8: ldr             x3, [x3, #0x598]
    // 0xae9bec: r0 = DefaultTypeTest()
    //     0xae9bec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae9bf0: r16 = "[a-zA-Z ]"
    //     0xae9bf0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xae9bf4: ldr             x16, [x16, #0xb20]
    // 0xae9bf8: stp             x16, NULL, [SP, #0x20]
    // 0xae9bfc: r16 = false
    //     0xae9bfc: add             x16, NULL, #0x30  ; false
    // 0xae9c00: r30 = true
    //     0xae9c00: add             lr, NULL, #0x20  ; true
    // 0xae9c04: stp             lr, x16, [SP, #0x10]
    // 0xae9c08: r16 = false
    //     0xae9c08: add             x16, NULL, #0x30  ; false
    // 0xae9c0c: r30 = false
    //     0xae9c0c: add             lr, NULL, #0x30  ; false
    // 0xae9c10: stp             lr, x16, [SP]
    // 0xae9c14: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xae9c14: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xae9c18: r0 = _RegExp()
    //     0xae9c18: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xae9c1c: stur            x0, [fp, #-0x30]
    // 0xae9c20: r0 = FilteringTextInputFormatter()
    //     0xae9c20: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xae9c24: mov             x3, x0
    // 0xae9c28: ldur            x0, [fp, #-0x30]
    // 0xae9c2c: stur            x3, [fp, #-0x38]
    // 0xae9c30: StoreField: r3->field_7 = r0
    //     0xae9c30: stur            w0, [x3, #7]
    // 0xae9c34: r0 = true
    //     0xae9c34: add             x0, NULL, #0x20  ; true
    // 0xae9c38: StoreField: r3->field_b = r0
    //     0xae9c38: stur            w0, [x3, #0xb]
    // 0xae9c3c: r0 = ""
    //     0xae9c3c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae9c40: StoreField: r3->field_f = r0
    //     0xae9c40: stur            w0, [x3, #0xf]
    // 0xae9c44: r1 = Null
    //     0xae9c44: mov             x1, NULL
    // 0xae9c48: r2 = 2
    //     0xae9c48: movz            x2, #0x2
    // 0xae9c4c: r0 = AllocateArray()
    //     0xae9c4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae9c50: mov             x2, x0
    // 0xae9c54: ldur            x0, [fp, #-0x38]
    // 0xae9c58: stur            x2, [fp, #-0x30]
    // 0xae9c5c: StoreField: r2->field_f = r0
    //     0xae9c5c: stur            w0, [x2, #0xf]
    // 0xae9c60: r1 = <TextInputFormatter>
    //     0xae9c60: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xae9c64: ldr             x1, [x1, #0x788]
    // 0xae9c68: r0 = AllocateGrowableArray()
    //     0xae9c68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae9c6c: mov             x2, x0
    // 0xae9c70: ldur            x0, [fp, #-0x30]
    // 0xae9c74: stur            x2, [fp, #-0x38]
    // 0xae9c78: StoreField: r2->field_f = r0
    //     0xae9c78: stur            w0, [x2, #0xf]
    // 0xae9c7c: r0 = 2
    //     0xae9c7c: movz            x0, #0x2
    // 0xae9c80: StoreField: r2->field_b = r0
    //     0xae9c80: stur            w0, [x2, #0xb]
    // 0xae9c84: ldur            x1, [fp, #-8]
    // 0xae9c88: r0 = controller()
    //     0xae9c88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9c8c: LoadField: r1 = r0->field_27
    //     0xae9c8c: ldur            w1, [x0, #0x27]
    // 0xae9c90: DecompressPointer r1
    //     0xae9c90: add             x1, x1, HEAP, lsl #32
    // 0xae9c94: r0 = value()
    //     0xae9c94: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae9c98: r1 = <String>
    //     0xae9c98: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xae9c9c: stur            x0, [fp, #-8]
    // 0xae9ca0: r0 = TextFormField()
    //     0xae9ca0: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xae9ca4: stur            x0, [fp, #-0x30]
    // 0xae9ca8: ldur            x16, [fp, #-0x20]
    // 0xae9cac: ldur            lr, [fp, #-0x28]
    // 0xae9cb0: stp             lr, x16, [SP, #0x10]
    // 0xae9cb4: ldur            x16, [fp, #-0x38]
    // 0xae9cb8: ldur            lr, [fp, #-8]
    // 0xae9cbc: stp             lr, x16, [SP]
    // 0xae9cc0: mov             x1, x0
    // 0xae9cc4: r2 = Instance_InputDecoration
    //     0xae9cc4: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xae9cc8: ldr             x2, [x2, #0xb28]
    // 0xae9ccc: r4 = const [0, 0x6, 0x4, 0x2, initialValue, 0x5, inputFormatters, 0x4, onChanged, 0x3, validator, 0x2, null]
    //     0xae9ccc: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b30] List(13) [0, 0x6, 0x4, 0x2, "initialValue", 0x5, "inputFormatters", 0x4, "onChanged", 0x3, "validator", 0x2, Null]
    //     0xae9cd0: ldr             x4, [x4, #0xb30]
    // 0xae9cd4: r0 = TextFormField()
    //     0xae9cd4: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xae9cd8: ldur            x1, [fp, #-0x10]
    // 0xae9cdc: ldur            x0, [fp, #-0x30]
    // 0xae9ce0: ArrayStore: r1[8] = r0  ; List_4
    //     0xae9ce0: add             x25, x1, #0x2f
    //     0xae9ce4: str             w0, [x25]
    //     0xae9ce8: tbz             w0, #0, #0xae9d04
    //     0xae9cec: ldurb           w16, [x1, #-1]
    //     0xae9cf0: ldurb           w17, [x0, #-1]
    //     0xae9cf4: and             x16, x17, x16, lsr #2
    //     0xae9cf8: tst             x16, HEAP, lsr #32
    //     0xae9cfc: b.eq            #0xae9d04
    //     0xae9d00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae9d04: ldur            x1, [fp, #-0x10]
    // 0xae9d08: r16 = Instance_SizedBox
    //     0xae9d08: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xae9d0c: ldr             x16, [x16, #0x950]
    // 0xae9d10: StoreField: r1->field_33 = r16
    //     0xae9d10: stur            w16, [x1, #0x33]
    // 0xae9d14: r0 = Obx()
    //     0xae9d14: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xae9d18: ldur            x2, [fp, #-0x18]
    // 0xae9d1c: r1 = Function '<anonymous closure>':.
    //     0xae9d1c: add             x1, PP, #0x35, lsl #12  ; [pp+0x355a8] AnonymousClosure: (0xae9e58), in [package:nuonline/app/modules/donation/widgets/donation_form.dart] DonationFormWidget::build (0xae98d4)
    //     0xae9d20: ldr             x1, [x1, #0x5a8]
    // 0xae9d24: stur            x0, [fp, #-8]
    // 0xae9d28: r0 = AllocateClosure()
    //     0xae9d28: bl              #0xec1630  ; AllocateClosureStub
    // 0xae9d2c: mov             x1, x0
    // 0xae9d30: ldur            x0, [fp, #-8]
    // 0xae9d34: StoreField: r0->field_b = r1
    //     0xae9d34: stur            w1, [x0, #0xb]
    // 0xae9d38: r1 = Null
    //     0xae9d38: mov             x1, NULL
    // 0xae9d3c: r2 = 4
    //     0xae9d3c: movz            x2, #0x4
    // 0xae9d40: r0 = AllocateArray()
    //     0xae9d40: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae9d44: mov             x2, x0
    // 0xae9d48: ldur            x0, [fp, #-8]
    // 0xae9d4c: stur            x2, [fp, #-0x18]
    // 0xae9d50: StoreField: r2->field_f = r0
    //     0xae9d50: stur            w0, [x2, #0xf]
    // 0xae9d54: r16 = Instance_Text
    //     0xae9d54: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b50] Obj!Text@e21c31
    //     0xae9d58: ldr             x16, [x16, #0xb50]
    // 0xae9d5c: StoreField: r2->field_13 = r16
    //     0xae9d5c: stur            w16, [x2, #0x13]
    // 0xae9d60: r1 = <Widget>
    //     0xae9d60: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae9d64: r0 = AllocateGrowableArray()
    //     0xae9d64: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae9d68: mov             x1, x0
    // 0xae9d6c: ldur            x0, [fp, #-0x18]
    // 0xae9d70: stur            x1, [fp, #-8]
    // 0xae9d74: StoreField: r1->field_f = r0
    //     0xae9d74: stur            w0, [x1, #0xf]
    // 0xae9d78: r0 = 4
    //     0xae9d78: movz            x0, #0x4
    // 0xae9d7c: StoreField: r1->field_b = r0
    //     0xae9d7c: stur            w0, [x1, #0xb]
    // 0xae9d80: r0 = Row()
    //     0xae9d80: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae9d84: mov             x1, x0
    // 0xae9d88: r0 = Instance_Axis
    //     0xae9d88: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xae9d8c: StoreField: r1->field_f = r0
    //     0xae9d8c: stur            w0, [x1, #0xf]
    // 0xae9d90: r0 = Instance_MainAxisAlignment
    //     0xae9d90: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xae9d94: ldr             x0, [x0, #0x730]
    // 0xae9d98: StoreField: r1->field_13 = r0
    //     0xae9d98: stur            w0, [x1, #0x13]
    // 0xae9d9c: r0 = Instance_MainAxisSize
    //     0xae9d9c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xae9da0: ldr             x0, [x0, #0x738]
    // 0xae9da4: ArrayStore: r1[0] = r0  ; List_4
    //     0xae9da4: stur            w0, [x1, #0x17]
    // 0xae9da8: r0 = Instance_CrossAxisAlignment
    //     0xae9da8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xae9dac: ldr             x0, [x0, #0x740]
    // 0xae9db0: StoreField: r1->field_1b = r0
    //     0xae9db0: stur            w0, [x1, #0x1b]
    // 0xae9db4: r0 = Instance_VerticalDirection
    //     0xae9db4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xae9db8: ldr             x0, [x0, #0x748]
    // 0xae9dbc: StoreField: r1->field_23 = r0
    //     0xae9dbc: stur            w0, [x1, #0x23]
    // 0xae9dc0: r0 = Instance_Clip
    //     0xae9dc0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xae9dc4: ldr             x0, [x0, #0x750]
    // 0xae9dc8: StoreField: r1->field_2b = r0
    //     0xae9dc8: stur            w0, [x1, #0x2b]
    // 0xae9dcc: StoreField: r1->field_2f = rZR
    //     0xae9dcc: stur            xzr, [x1, #0x2f]
    // 0xae9dd0: ldur            x0, [fp, #-8]
    // 0xae9dd4: StoreField: r1->field_b = r0
    //     0xae9dd4: stur            w0, [x1, #0xb]
    // 0xae9dd8: mov             x0, x1
    // 0xae9ddc: ldur            x1, [fp, #-0x10]
    // 0xae9de0: ArrayStore: r1[10] = r0  ; List_4
    //     0xae9de0: add             x25, x1, #0x37
    //     0xae9de4: str             w0, [x25]
    //     0xae9de8: tbz             w0, #0, #0xae9e04
    //     0xae9dec: ldurb           w16, [x1, #-1]
    //     0xae9df0: ldurb           w17, [x0, #-1]
    //     0xae9df4: and             x16, x17, x16, lsr #2
    //     0xae9df8: tst             x16, HEAP, lsr #32
    //     0xae9dfc: b.eq            #0xae9e04
    //     0xae9e00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xae9e04: r1 = <Widget>
    //     0xae9e04: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xae9e08: r0 = AllocateGrowableArray()
    //     0xae9e08: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xae9e0c: mov             x1, x0
    // 0xae9e10: ldur            x0, [fp, #-0x10]
    // 0xae9e14: stur            x1, [fp, #-8]
    // 0xae9e18: StoreField: r1->field_f = r0
    //     0xae9e18: stur            w0, [x1, #0xf]
    // 0xae9e1c: r0 = 22
    //     0xae9e1c: movz            x0, #0x16
    // 0xae9e20: StoreField: r1->field_b = r0
    //     0xae9e20: stur            w0, [x1, #0xb]
    // 0xae9e24: r0 = NSection()
    //     0xae9e24: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xae9e28: r1 = ""
    //     0xae9e28: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xae9e2c: StoreField: r0->field_b = r1
    //     0xae9e2c: stur            w1, [x0, #0xb]
    // 0xae9e30: ldur            x1, [fp, #-8]
    // 0xae9e34: StoreField: r0->field_f = r1
    //     0xae9e34: stur            w1, [x0, #0xf]
    // 0xae9e38: r1 = false
    //     0xae9e38: add             x1, NULL, #0x30  ; false
    // 0xae9e3c: StoreField: r0->field_27 = r1
    //     0xae9e3c: stur            w1, [x0, #0x27]
    // 0xae9e40: StoreField: r0->field_2b = r1
    //     0xae9e40: stur            w1, [x0, #0x2b]
    // 0xae9e44: LeaveFrame
    //     0xae9e44: mov             SP, fp
    //     0xae9e48: ldp             fp, lr, [SP], #0x10
    // 0xae9e4c: ret
    //     0xae9e4c: ret             
    // 0xae9e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9e50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9e54: b               #0xae98f4
  }
  [closure] Checkbox <anonymous closure>(dynamic) {
    // ** addr: 0xae9e58, size: 0x118
    // 0xae9e58: EnterFrame
    //     0xae9e58: stp             fp, lr, [SP, #-0x10]!
    //     0xae9e5c: mov             fp, SP
    // 0xae9e60: AllocStack(0x30)
    //     0xae9e60: sub             SP, SP, #0x30
    // 0xae9e64: SetupParameters()
    //     0xae9e64: ldr             x0, [fp, #0x10]
    //     0xae9e68: ldur            w2, [x0, #0x17]
    //     0xae9e6c: add             x2, x2, HEAP, lsl #32
    //     0xae9e70: stur            x2, [fp, #-8]
    // 0xae9e74: CheckStackOverflow
    //     0xae9e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9e78: cmp             SP, x16
    //     0xae9e7c: b.ls            #0xae9f68
    // 0xae9e80: LoadField: r1 = r2->field_f
    //     0xae9e80: ldur            w1, [x2, #0xf]
    // 0xae9e84: DecompressPointer r1
    //     0xae9e84: add             x1, x1, HEAP, lsl #32
    // 0xae9e88: r0 = controller()
    //     0xae9e88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9e8c: LoadField: r1 = r0->field_2b
    //     0xae9e8c: ldur            w1, [x0, #0x2b]
    // 0xae9e90: DecompressPointer r1
    //     0xae9e90: add             x1, x1, HEAP, lsl #32
    // 0xae9e94: r0 = value()
    //     0xae9e94: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xae9e98: mov             x2, x0
    // 0xae9e9c: ldur            x0, [fp, #-8]
    // 0xae9ea0: stur            x2, [fp, #-0x10]
    // 0xae9ea4: LoadField: r1 = r0->field_f
    //     0xae9ea4: ldur            w1, [x0, #0xf]
    // 0xae9ea8: DecompressPointer r1
    //     0xae9ea8: add             x1, x1, HEAP, lsl #32
    // 0xae9eac: r0 = controller()
    //     0xae9eac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9eb0: LoadField: r2 = r0->field_2b
    //     0xae9eb0: ldur            w2, [x0, #0x2b]
    // 0xae9eb4: DecompressPointer r2
    //     0xae9eb4: add             x2, x2, HEAP, lsl #32
    // 0xae9eb8: LoadField: r3 = r2->field_7
    //     0xae9eb8: ldur            w3, [x2, #7]
    // 0xae9ebc: DecompressPointer r3
    //     0xae9ebc: add             x3, x3, HEAP, lsl #32
    // 0xae9ec0: r1 = Function 'call':.
    //     0xae9ec0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xae9ec4: ldr             x1, [x1, #0x310]
    // 0xae9ec8: r0 = AllocateClosureTA()
    //     0xae9ec8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xae9ecc: mov             x3, x0
    // 0xae9ed0: r2 = Null
    //     0xae9ed0: mov             x2, NULL
    // 0xae9ed4: r1 = Null
    //     0xae9ed4: mov             x1, NULL
    // 0xae9ed8: stur            x3, [fp, #-8]
    // 0xae9edc: r8 = (dynamic this, bool?) => bool
    //     0xae9edc: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xae9ee0: ldr             x8, [x8, #0x318]
    // 0xae9ee4: r3 = Null
    //     0xae9ee4: add             x3, PP, #0x35, lsl #12  ; [pp+0x355b0] Null
    //     0xae9ee8: ldr             x3, [x3, #0x5b0]
    // 0xae9eec: r0 = DefaultTypeTest()
    //     0xae9eec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xae9ef0: r16 = <Color?>
    //     0xae9ef0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xae9ef4: ldr             x16, [x16, #0x98]
    // 0xae9ef8: r30 = Instance_MaterialColor
    //     0xae9ef8: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xae9efc: ldr             lr, [lr, #0xbf0]
    // 0xae9f00: stp             lr, x16, [SP, #8]
    // 0xae9f04: r16 = Instance_Color
    //     0xae9f04: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae9f08: str             x16, [SP]
    // 0xae9f0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae9f0c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae9f10: r0 = mode()
    //     0xae9f10: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xae9f14: stur            x0, [fp, #-0x18]
    // 0xae9f18: r0 = Checkbox()
    //     0xae9f18: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xae9f1c: ldur            x1, [fp, #-0x10]
    // 0xae9f20: StoreField: r0->field_b = r1
    //     0xae9f20: stur            w1, [x0, #0xb]
    // 0xae9f24: r1 = false
    //     0xae9f24: add             x1, NULL, #0x30  ; false
    // 0xae9f28: StoreField: r0->field_23 = r1
    //     0xae9f28: stur            w1, [x0, #0x23]
    // 0xae9f2c: ldur            x2, [fp, #-8]
    // 0xae9f30: StoreField: r0->field_f = r2
    //     0xae9f30: stur            w2, [x0, #0xf]
    // 0xae9f34: ldur            x2, [fp, #-0x18]
    // 0xae9f38: StoreField: r0->field_1f = r2
    //     0xae9f38: stur            w2, [x0, #0x1f]
    // 0xae9f3c: r2 = Instance_VisualDensity
    //     0xae9f3c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xae9f40: ldr             x2, [x2, #0xb68]
    // 0xae9f44: StoreField: r0->field_2b = r2
    //     0xae9f44: stur            w2, [x0, #0x2b]
    // 0xae9f48: StoreField: r0->field_43 = r1
    //     0xae9f48: stur            w1, [x0, #0x43]
    // 0xae9f4c: StoreField: r0->field_4f = r1
    //     0xae9f4c: stur            w1, [x0, #0x4f]
    // 0xae9f50: r1 = Instance__CheckboxType
    //     0xae9f50: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xae9f54: ldr             x1, [x1, #0xb70]
    // 0xae9f58: StoreField: r0->field_57 = r1
    //     0xae9f58: stur            w1, [x0, #0x57]
    // 0xae9f5c: LeaveFrame
    //     0xae9f5c: mov             SP, fp
    //     0xae9f60: ldp             fp, lr, [SP], #0x10
    // 0xae9f64: ret
    //     0xae9f64: ret             
    // 0xae9f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9f6c: b               #0xae9e80
  }
  [closure] GridView <anonymous closure>(dynamic) {
    // ** addr: 0xae9f70, size: 0x110
    // 0xae9f70: EnterFrame
    //     0xae9f70: stp             fp, lr, [SP, #-0x10]!
    //     0xae9f74: mov             fp, SP
    // 0xae9f78: AllocStack(0x28)
    //     0xae9f78: sub             SP, SP, #0x28
    // 0xae9f7c: SetupParameters()
    //     0xae9f7c: ldr             x0, [fp, #0x10]
    //     0xae9f80: ldur            w2, [x0, #0x17]
    //     0xae9f84: add             x2, x2, HEAP, lsl #32
    //     0xae9f88: stur            x2, [fp, #-8]
    // 0xae9f8c: CheckStackOverflow
    //     0xae9f8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9f90: cmp             SP, x16
    //     0xae9f94: b.ls            #0xaea078
    // 0xae9f98: LoadField: r1 = r2->field_13
    //     0xae9f98: ldur            w1, [x2, #0x13]
    // 0xae9f9c: DecompressPointer r1
    //     0xae9f9c: add             x1, x1, HEAP, lsl #32
    // 0xae9fa0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae9fa0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae9fa4: r0 = _of()
    //     0xae9fa4: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae9fa8: LoadField: r1 = r0->field_7
    //     0xae9fa8: ldur            w1, [x0, #7]
    // 0xae9fac: DecompressPointer r1
    //     0xae9fac: add             x1, x1, HEAP, lsl #32
    // 0xae9fb0: r0 = shortestSide()
    //     0xae9fb0: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0xae9fb4: mov             v1.16b, v0.16b
    // 0xae9fb8: d0 = 600.000000
    //     0xae9fb8: add             x17, PP, #0x34, lsl #12  ; [pp+0x34d20] IMM: double(600) from 0x4082c00000000000
    //     0xae9fbc: ldr             d0, [x17, #0xd20]
    // 0xae9fc0: fcmp            d1, d0
    // 0xae9fc4: b.le            #0xae9fd0
    // 0xae9fc8: d0 = 4.000000
    //     0xae9fc8: fmov            d0, #4.00000000
    // 0xae9fcc: b               #0xae9fd4
    // 0xae9fd0: d0 = 2.000000
    //     0xae9fd0: fmov            d0, #2.00000000
    // 0xae9fd4: ldur            x2, [fp, #-8]
    // 0xae9fd8: stur            d0, [fp, #-0x10]
    // 0xae9fdc: LoadField: r1 = r2->field_f
    //     0xae9fdc: ldur            w1, [x2, #0xf]
    // 0xae9fe0: DecompressPointer r1
    //     0xae9fe0: add             x1, x1, HEAP, lsl #32
    // 0xae9fe4: r0 = controller()
    //     0xae9fe4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xae9fe8: r1 = const [0x2710, 0x61a8, 0x88b8, 0xc350, 0x11170, 0x186a0]
    //     0xae9fe8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30060] List<int>(6)
    //     0xae9fec: ldr             x1, [x1, #0x60]
    // 0xae9ff0: r0 = asMap()
    //     0xae9ff0: bl              #0x6dc898  ; [dart:collection] ListBase::asMap
    // 0xae9ff4: mov             x1, x0
    // 0xae9ff8: r0 = entries()
    //     0xae9ff8: bl              #0x7f814c  ; [dart:collection] MapBase::entries
    // 0xae9ffc: ldur            x2, [fp, #-8]
    // 0xaea000: r1 = Function '<anonymous closure>':.
    //     0xaea000: add             x1, PP, #0x35, lsl #12  ; [pp+0x355c0] AnonymousClosure: (0xaea080), in [package:nuonline/app/modules/donation/widgets/donation_form.dart] DonationFormWidget::build (0xae98d4)
    //     0xaea004: ldr             x1, [x1, #0x5c0]
    // 0xaea008: stur            x0, [fp, #-8]
    // 0xaea00c: r0 = AllocateClosure()
    //     0xaea00c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaea010: r16 = <InkWell>
    //     0xaea010: add             x16, PP, #0x34, lsl #12  ; [pp+0x34d30] TypeArguments: <InkWell>
    //     0xaea014: ldr             x16, [x16, #0xd30]
    // 0xaea018: ldur            lr, [fp, #-8]
    // 0xaea01c: stp             lr, x16, [SP, #8]
    // 0xaea020: str             x0, [SP]
    // 0xaea024: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaea024: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaea028: r0 = map()
    //     0xaea028: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0xaea02c: LoadField: r1 = r0->field_7
    //     0xaea02c: ldur            w1, [x0, #7]
    // 0xaea030: DecompressPointer r1
    //     0xaea030: add             x1, x1, HEAP, lsl #32
    // 0xaea034: mov             x2, x0
    // 0xaea038: r0 = _GrowableList.of()
    //     0xaea038: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xaea03c: stur            x0, [fp, #-8]
    // 0xaea040: r0 = GridView()
    //     0xaea040: bl              #0xad81c0  ; AllocateGridViewStub -> GridView (size=0x5c)
    // 0xaea044: mov             x1, x0
    // 0xaea048: ldur            d0, [fp, #-0x10]
    // 0xaea04c: ldur            x2, [fp, #-8]
    // 0xaea050: r3 = 3
    //     0xaea050: movz            x3, #0x3
    // 0xaea054: r5 = Instance_ScrollPhysics
    //     0xaea054: add             x5, PP, #0x34, lsl #12  ; [pp+0x34d38] Obj!ScrollPhysics@e0fd31
    //     0xaea058: ldr             x5, [x5, #0xd38]
    // 0xaea05c: stur            x0, [fp, #-8]
    // 0xaea060: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xaea060: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xaea064: r0 = GridView.count()
    //     0xaea064: bl              #0xae2ac4  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.count
    // 0xaea068: ldur            x0, [fp, #-8]
    // 0xaea06c: LeaveFrame
    //     0xaea06c: mov             SP, fp
    //     0xaea070: ldp             fp, lr, [SP], #0x10
    // 0xaea074: ret
    //     0xaea074: ret             
    // 0xaea078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaea078: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaea07c: b               #0xae9f98
  }
  [closure] InkWell <anonymous closure>(dynamic, MapEntry<int, int>) {
    // ** addr: 0xaea080, size: 0x55c
    // 0xaea080: EnterFrame
    //     0xaea080: stp             fp, lr, [SP, #-0x10]!
    //     0xaea084: mov             fp, SP
    // 0xaea088: AllocStack(0x70)
    //     0xaea088: sub             SP, SP, #0x70
    // 0xaea08c: SetupParameters()
    //     0xaea08c: ldr             x0, [fp, #0x18]
    //     0xaea090: ldur            w1, [x0, #0x17]
    //     0xaea094: add             x1, x1, HEAP, lsl #32
    //     0xaea098: stur            x1, [fp, #-8]
    // 0xaea09c: CheckStackOverflow
    //     0xaea09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaea0a0: cmp             SP, x16
    //     0xaea0a4: b.ls            #0xaea5c0
    // 0xaea0a8: r1 = 1
    //     0xaea0a8: movz            x1, #0x1
    // 0xaea0ac: r0 = AllocateContext()
    //     0xaea0ac: bl              #0xec126c  ; AllocateContextStub
    // 0xaea0b0: mov             x2, x0
    // 0xaea0b4: ldur            x0, [fp, #-8]
    // 0xaea0b8: stur            x2, [fp, #-0x10]
    // 0xaea0bc: StoreField: r2->field_b = r0
    //     0xaea0bc: stur            w0, [x2, #0xb]
    // 0xaea0c0: ldr             x3, [fp, #0x10]
    // 0xaea0c4: StoreField: r2->field_f = r3
    //     0xaea0c4: stur            w3, [x2, #0xf]
    // 0xaea0c8: LoadField: r1 = r0->field_f
    //     0xaea0c8: ldur            w1, [x0, #0xf]
    // 0xaea0cc: DecompressPointer r1
    //     0xaea0cc: add             x1, x1, HEAP, lsl #32
    // 0xaea0d0: r0 = controller()
    //     0xaea0d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea0d4: LoadField: r1 = r0->field_23
    //     0xaea0d4: ldur            w1, [x0, #0x23]
    // 0xaea0d8: DecompressPointer r1
    //     0xaea0d8: add             x1, x1, HEAP, lsl #32
    // 0xaea0dc: r0 = value()
    //     0xaea0dc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaea0e0: mov             x1, x0
    // 0xaea0e4: ldr             x0, [fp, #0x10]
    // 0xaea0e8: LoadField: r2 = r0->field_f
    //     0xaea0e8: ldur            w2, [x0, #0xf]
    // 0xaea0ec: DecompressPointer r2
    //     0xaea0ec: add             x2, x2, HEAP, lsl #32
    // 0xaea0f0: stur            x2, [fp, #-0x18]
    // 0xaea0f4: stp             x2, x1, [SP]
    // 0xaea0f8: r0 = ==()
    //     0xaea0f8: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xaea0fc: r1 = _ConstMap len:10
    //     0xaea0fc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaea100: ldr             x1, [x1, #0xc08]
    // 0xaea104: r2 = 100
    //     0xaea104: movz            x2, #0x64
    // 0xaea108: stur            x0, [fp, #-0x20]
    // 0xaea10c: r0 = []()
    //     0xaea10c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea110: r1 = _ConstMap len:10
    //     0xaea110: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaea114: ldr             x1, [x1, #0xc08]
    // 0xaea118: r2 = 1800
    //     0xaea118: movz            x2, #0x708
    // 0xaea11c: stur            x0, [fp, #-0x28]
    // 0xaea120: r0 = []()
    //     0xaea120: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea124: r16 = <Color?>
    //     0xaea124: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaea128: ldr             x16, [x16, #0x98]
    // 0xaea12c: stp             x0, x16, [SP, #8]
    // 0xaea130: ldur            x16, [fp, #-0x28]
    // 0xaea134: str             x16, [SP]
    // 0xaea138: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaea138: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaea13c: r0 = mode()
    //     0xaea13c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaea140: mov             x1, x0
    // 0xaea144: ldur            x0, [fp, #-0x20]
    // 0xaea148: tbnz            w0, #4, #0xaea154
    // 0xaea14c: mov             x3, x1
    // 0xaea150: b               #0xaea158
    // 0xaea154: r3 = Null
    //     0xaea154: mov             x3, NULL
    // 0xaea158: ldr             x0, [fp, #0x10]
    // 0xaea15c: ldur            x2, [fp, #-8]
    // 0xaea160: stur            x3, [fp, #-0x20]
    // 0xaea164: LoadField: r1 = r2->field_f
    //     0xaea164: ldur            w1, [x2, #0xf]
    // 0xaea168: DecompressPointer r1
    //     0xaea168: add             x1, x1, HEAP, lsl #32
    // 0xaea16c: r0 = controller()
    //     0xaea16c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea170: LoadField: r1 = r0->field_23
    //     0xaea170: ldur            w1, [x0, #0x23]
    // 0xaea174: DecompressPointer r1
    //     0xaea174: add             x1, x1, HEAP, lsl #32
    // 0xaea178: r0 = value()
    //     0xaea178: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaea17c: ldur            x16, [fp, #-0x18]
    // 0xaea180: stp             x16, x0, [SP]
    // 0xaea184: r0 = ==()
    //     0xaea184: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xaea188: r1 = _ConstMap len:10
    //     0xaea188: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaea18c: ldr             x1, [x1, #0xc08]
    // 0xaea190: r2 = 600
    //     0xaea190: movz            x2, #0x258
    // 0xaea194: stur            x0, [fp, #-0x28]
    // 0xaea198: r0 = []()
    //     0xaea198: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea19c: cmp             w0, NULL
    // 0xaea1a0: b.eq            #0xaea5c8
    // 0xaea1a4: r16 = <Color>
    //     0xaea1a4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xaea1a8: ldr             x16, [x16, #0x158]
    // 0xaea1ac: stp             x0, x16, [SP, #8]
    // 0xaea1b0: r16 = Instance_MaterialColor
    //     0xaea1b0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaea1b4: ldr             x16, [x16, #0xcf0]
    // 0xaea1b8: str             x16, [SP]
    // 0xaea1bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaea1bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaea1c0: r0 = mode()
    //     0xaea1c0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaea1c4: r16 = 1.500000
    //     0xaea1c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c58] 1.5
    //     0xaea1c8: ldr             x16, [x16, #0xc58]
    // 0xaea1cc: stp             x16, x0, [SP]
    // 0xaea1d0: r1 = Null
    //     0xaea1d0: mov             x1, NULL
    // 0xaea1d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, width, 0x2, null]
    //     0xaea1d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x333a8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "width", 0x2, Null]
    //     0xaea1d8: ldr             x4, [x4, #0x3a8]
    // 0xaea1dc: r0 = Border.all()
    //     0xaea1dc: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xaea1e0: r1 = _ConstMap len:6
    //     0xaea1e0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaea1e4: ldr             x1, [x1, #0xc20]
    // 0xaea1e8: r2 = 4
    //     0xaea1e8: movz            x2, #0x4
    // 0xaea1ec: stur            x0, [fp, #-0x30]
    // 0xaea1f0: r0 = []()
    //     0xaea1f0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea1f4: stur            x0, [fp, #-0x38]
    // 0xaea1f8: cmp             w0, NULL
    // 0xaea1fc: b.eq            #0xaea5cc
    // 0xaea200: r0 = BorderSide()
    //     0xaea200: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xaea204: mov             x3, x0
    // 0xaea208: ldur            x0, [fp, #-0x38]
    // 0xaea20c: stur            x3, [fp, #-0x40]
    // 0xaea210: StoreField: r3->field_7 = r0
    //     0xaea210: stur            w0, [x3, #7]
    // 0xaea214: d0 = 1.000000
    //     0xaea214: fmov            d0, #1.00000000
    // 0xaea218: StoreField: r3->field_b = d0
    //     0xaea218: stur            d0, [x3, #0xb]
    // 0xaea21c: r0 = Instance_BorderStyle
    //     0xaea21c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xaea220: ldr             x0, [x0, #0x2d0]
    // 0xaea224: StoreField: r3->field_13 = r0
    //     0xaea224: stur            w0, [x3, #0x13]
    // 0xaea228: d1 = -1.000000
    //     0xaea228: fmov            d1, #-1.00000000
    // 0xaea22c: ArrayStore: r3[0] = d1  ; List_8
    //     0xaea22c: stur            d1, [x3, #0x17]
    // 0xaea230: r1 = _ConstMap len:6
    //     0xaea230: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaea234: ldr             x1, [x1, #0xc20]
    // 0xaea238: r2 = 4
    //     0xaea238: movz            x2, #0x4
    // 0xaea23c: r0 = []()
    //     0xaea23c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea240: stur            x0, [fp, #-0x38]
    // 0xaea244: cmp             w0, NULL
    // 0xaea248: b.eq            #0xaea5d0
    // 0xaea24c: r0 = BorderSide()
    //     0xaea24c: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xaea250: mov             x1, x0
    // 0xaea254: ldur            x0, [fp, #-0x38]
    // 0xaea258: stur            x1, [fp, #-0x48]
    // 0xaea25c: StoreField: r1->field_7 = r0
    //     0xaea25c: stur            w0, [x1, #7]
    // 0xaea260: d0 = 1.000000
    //     0xaea260: fmov            d0, #1.00000000
    // 0xaea264: StoreField: r1->field_b = d0
    //     0xaea264: stur            d0, [x1, #0xb]
    // 0xaea268: r2 = Instance_BorderStyle
    //     0xaea268: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xaea26c: ldr             x2, [x2, #0x2d0]
    // 0xaea270: StoreField: r1->field_13 = r2
    //     0xaea270: stur            w2, [x1, #0x13]
    // 0xaea274: d1 = -1.000000
    //     0xaea274: fmov            d1, #-1.00000000
    // 0xaea278: ArrayStore: r1[0] = d1  ; List_8
    //     0xaea278: stur            d1, [x1, #0x17]
    // 0xaea27c: ldr             x3, [fp, #0x10]
    // 0xaea280: LoadField: r4 = r3->field_b
    //     0xaea280: ldur            w4, [x3, #0xb]
    // 0xaea284: DecompressPointer r4
    //     0xaea284: add             x4, x4, HEAP, lsl #32
    // 0xaea288: stur            x4, [fp, #-0x38]
    // 0xaea28c: r0 = 60
    //     0xaea28c: movz            x0, #0x3c
    // 0xaea290: branchIfSmi(r4, 0xaea29c)
    //     0xaea290: tbz             w4, #0, #0xaea29c
    // 0xaea294: r0 = LoadClassIdInstr(r4)
    //     0xaea294: ldur            x0, [x4, #-1]
    //     0xaea298: ubfx            x0, x0, #0xc, #0x14
    // 0xaea29c: r16 = 4
    //     0xaea29c: movz            x16, #0x4
    // 0xaea2a0: stp             x16, x4, [SP]
    // 0xaea2a4: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xaea2a4: sub             lr, x0, #0xfe6
    //     0xaea2a8: ldr             lr, [x21, lr, lsl #3]
    //     0xaea2ac: blr             lr
    // 0xaea2b0: r1 = _ConstMap len:6
    //     0xaea2b0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaea2b4: ldr             x1, [x1, #0xc20]
    // 0xaea2b8: r2 = 4
    //     0xaea2b8: movz            x2, #0x4
    // 0xaea2bc: stur            x0, [fp, #-0x50]
    // 0xaea2c0: r0 = []()
    //     0xaea2c0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea2c4: stur            x0, [fp, #-0x58]
    // 0xaea2c8: cmp             w0, NULL
    // 0xaea2cc: b.eq            #0xaea5d4
    // 0xaea2d0: r0 = BorderSide()
    //     0xaea2d0: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xaea2d4: mov             x1, x0
    // 0xaea2d8: ldur            x0, [fp, #-0x58]
    // 0xaea2dc: StoreField: r1->field_7 = r0
    //     0xaea2dc: stur            w0, [x1, #7]
    // 0xaea2e0: d0 = 1.000000
    //     0xaea2e0: fmov            d0, #1.00000000
    // 0xaea2e4: StoreField: r1->field_b = d0
    //     0xaea2e4: stur            d0, [x1, #0xb]
    // 0xaea2e8: r0 = Instance_BorderStyle
    //     0xaea2e8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xaea2ec: ldr             x0, [x0, #0x2d0]
    // 0xaea2f0: StoreField: r1->field_13 = r0
    //     0xaea2f0: stur            w0, [x1, #0x13]
    // 0xaea2f4: d1 = -1.000000
    //     0xaea2f4: fmov            d1, #-1.00000000
    // 0xaea2f8: ArrayStore: r1[0] = d1  ; List_8
    //     0xaea2f8: stur            d1, [x1, #0x17]
    // 0xaea2fc: ldur            x2, [fp, #-0x50]
    // 0xaea300: tbnz            w2, #4, #0xaea30c
    // 0xaea304: mov             x3, x1
    // 0xaea308: b               #0xaea314
    // 0xaea30c: r3 = Instance_BorderSide
    //     0xaea30c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34d40] Obj!BorderSide@e1c131
    //     0xaea310: ldr             x3, [x3, #0xd40]
    // 0xaea314: ldur            x1, [fp, #-0x38]
    // 0xaea318: stur            x3, [fp, #-0x50]
    // 0xaea31c: cbnz            w1, #0xaea328
    // 0xaea320: r4 = true
    //     0xaea320: add             x4, NULL, #0x20  ; true
    // 0xaea324: b               #0xaea33c
    // 0xaea328: cmp             w1, #6
    // 0xaea32c: r16 = true
    //     0xaea32c: add             x16, NULL, #0x20  ; true
    // 0xaea330: r17 = false
    //     0xaea330: add             x17, NULL, #0x30  ; false
    // 0xaea334: csel            x2, x16, x17, eq
    // 0xaea338: mov             x4, x2
    // 0xaea33c: stur            x4, [fp, #-0x38]
    // 0xaea340: r1 = _ConstMap len:6
    //     0xaea340: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaea344: ldr             x1, [x1, #0xc20]
    // 0xaea348: r2 = 4
    //     0xaea348: movz            x2, #0x4
    // 0xaea34c: r0 = []()
    //     0xaea34c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaea350: stur            x0, [fp, #-0x58]
    // 0xaea354: cmp             w0, NULL
    // 0xaea358: b.eq            #0xaea5d8
    // 0xaea35c: r0 = BorderSide()
    //     0xaea35c: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xaea360: mov             x1, x0
    // 0xaea364: ldur            x0, [fp, #-0x58]
    // 0xaea368: StoreField: r1->field_7 = r0
    //     0xaea368: stur            w0, [x1, #7]
    // 0xaea36c: d0 = 1.000000
    //     0xaea36c: fmov            d0, #1.00000000
    // 0xaea370: StoreField: r1->field_b = d0
    //     0xaea370: stur            d0, [x1, #0xb]
    // 0xaea374: r0 = Instance_BorderStyle
    //     0xaea374: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xaea378: ldr             x0, [x0, #0x2d0]
    // 0xaea37c: StoreField: r1->field_13 = r0
    //     0xaea37c: stur            w0, [x1, #0x13]
    // 0xaea380: d0 = -1.000000
    //     0xaea380: fmov            d0, #-1.00000000
    // 0xaea384: ArrayStore: r1[0] = d0  ; List_8
    //     0xaea384: stur            d0, [x1, #0x17]
    // 0xaea388: ldur            x0, [fp, #-0x38]
    // 0xaea38c: tbnz            w0, #4, #0xaea398
    // 0xaea390: mov             x4, x1
    // 0xaea394: b               #0xaea3a0
    // 0xaea398: r4 = Instance_BorderSide
    //     0xaea398: add             x4, PP, #0x34, lsl #12  ; [pp+0x34d40] Obj!BorderSide@e1c131
    //     0xaea39c: ldr             x4, [x4, #0xd40]
    // 0xaea3a0: ldur            x3, [fp, #-0x28]
    // 0xaea3a4: ldur            x2, [fp, #-0x40]
    // 0xaea3a8: ldur            x1, [fp, #-0x48]
    // 0xaea3ac: ldur            x0, [fp, #-0x50]
    // 0xaea3b0: stur            x4, [fp, #-0x38]
    // 0xaea3b4: r0 = Border()
    //     0xaea3b4: bl              #0x87dce8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xaea3b8: mov             x1, x0
    // 0xaea3bc: ldur            x0, [fp, #-0x50]
    // 0xaea3c0: StoreField: r1->field_7 = r0
    //     0xaea3c0: stur            w0, [x1, #7]
    // 0xaea3c4: ldur            x0, [fp, #-0x48]
    // 0xaea3c8: StoreField: r1->field_b = r0
    //     0xaea3c8: stur            w0, [x1, #0xb]
    // 0xaea3cc: ldur            x0, [fp, #-0x40]
    // 0xaea3d0: StoreField: r1->field_f = r0
    //     0xaea3d0: stur            w0, [x1, #0xf]
    // 0xaea3d4: ldur            x0, [fp, #-0x38]
    // 0xaea3d8: StoreField: r1->field_13 = r0
    //     0xaea3d8: stur            w0, [x1, #0x13]
    // 0xaea3dc: ldur            x0, [fp, #-0x28]
    // 0xaea3e0: tbnz            w0, #4, #0xaea3ec
    // 0xaea3e4: ldur            x2, [fp, #-0x30]
    // 0xaea3e8: b               #0xaea3f0
    // 0xaea3ec: mov             x2, x1
    // 0xaea3f0: ldur            x0, [fp, #-8]
    // 0xaea3f4: ldur            x1, [fp, #-0x20]
    // 0xaea3f8: stur            x2, [fp, #-0x28]
    // 0xaea3fc: r0 = BoxDecoration()
    //     0xaea3fc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaea400: mov             x2, x0
    // 0xaea404: ldur            x0, [fp, #-0x20]
    // 0xaea408: stur            x2, [fp, #-0x30]
    // 0xaea40c: StoreField: r2->field_7 = r0
    //     0xaea40c: stur            w0, [x2, #7]
    // 0xaea410: ldur            x0, [fp, #-0x28]
    // 0xaea414: StoreField: r2->field_f = r0
    //     0xaea414: stur            w0, [x2, #0xf]
    // 0xaea418: r0 = Instance_BoxShape
    //     0xaea418: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xaea41c: ldr             x0, [x0, #0xca8]
    // 0xaea420: StoreField: r2->field_23 = r0
    //     0xaea420: stur            w0, [x2, #0x23]
    // 0xaea424: ldur            x1, [fp, #-0x18]
    // 0xaea428: r0 = IntExtension.currency()
    //     0xaea428: bl              #0xae80dc  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.currency
    // 0xaea42c: mov             x2, x0
    // 0xaea430: ldur            x0, [fp, #-8]
    // 0xaea434: stur            x2, [fp, #-0x18]
    // 0xaea438: LoadField: r1 = r0->field_f
    //     0xaea438: ldur            w1, [x0, #0xf]
    // 0xaea43c: DecompressPointer r1
    //     0xaea43c: add             x1, x1, HEAP, lsl #32
    // 0xaea440: r0 = controller()
    //     0xaea440: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea444: LoadField: r1 = r0->field_23
    //     0xaea444: ldur            w1, [x0, #0x23]
    // 0xaea448: DecompressPointer r1
    //     0xaea448: add             x1, x1, HEAP, lsl #32
    // 0xaea44c: r0 = value()
    //     0xaea44c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaea450: ldr             x16, [fp, #0x10]
    // 0xaea454: stp             x16, x0, [SP]
    // 0xaea458: r0 = ==()
    //     0xaea458: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xaea45c: tbnz            w0, #4, #0xaea46c
    // 0xaea460: r1 = Instance_TextStyle
    //     0xaea460: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d48] Obj!TextStyle@e1b6c1
    //     0xaea464: ldr             x1, [x1, #0xd48]
    // 0xaea468: b               #0xaea470
    // 0xaea46c: r1 = Null
    //     0xaea46c: mov             x1, NULL
    // 0xaea470: ldur            x0, [fp, #-0x18]
    // 0xaea474: stur            x1, [fp, #-8]
    // 0xaea478: r0 = Text()
    //     0xaea478: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaea47c: mov             x3, x0
    // 0xaea480: ldur            x0, [fp, #-0x18]
    // 0xaea484: stur            x3, [fp, #-0x20]
    // 0xaea488: StoreField: r3->field_b = r0
    //     0xaea488: stur            w0, [x3, #0xb]
    // 0xaea48c: ldur            x0, [fp, #-8]
    // 0xaea490: StoreField: r3->field_13 = r0
    //     0xaea490: stur            w0, [x3, #0x13]
    // 0xaea494: r1 = Null
    //     0xaea494: mov             x1, NULL
    // 0xaea498: r2 = 2
    //     0xaea498: movz            x2, #0x2
    // 0xaea49c: r0 = AllocateArray()
    //     0xaea49c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaea4a0: mov             x2, x0
    // 0xaea4a4: ldur            x0, [fp, #-0x20]
    // 0xaea4a8: stur            x2, [fp, #-8]
    // 0xaea4ac: StoreField: r2->field_f = r0
    //     0xaea4ac: stur            w0, [x2, #0xf]
    // 0xaea4b0: r1 = <Widget>
    //     0xaea4b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaea4b4: r0 = AllocateGrowableArray()
    //     0xaea4b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaea4b8: mov             x1, x0
    // 0xaea4bc: ldur            x0, [fp, #-8]
    // 0xaea4c0: stur            x1, [fp, #-0x18]
    // 0xaea4c4: StoreField: r1->field_f = r0
    //     0xaea4c4: stur            w0, [x1, #0xf]
    // 0xaea4c8: r0 = 2
    //     0xaea4c8: movz            x0, #0x2
    // 0xaea4cc: StoreField: r1->field_b = r0
    //     0xaea4cc: stur            w0, [x1, #0xb]
    // 0xaea4d0: r0 = Column()
    //     0xaea4d0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaea4d4: mov             x1, x0
    // 0xaea4d8: r0 = Instance_Axis
    //     0xaea4d8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaea4dc: stur            x1, [fp, #-8]
    // 0xaea4e0: StoreField: r1->field_f = r0
    //     0xaea4e0: stur            w0, [x1, #0xf]
    // 0xaea4e4: r0 = Instance_MainAxisAlignment
    //     0xaea4e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xaea4e8: ldr             x0, [x0, #0x290]
    // 0xaea4ec: StoreField: r1->field_13 = r0
    //     0xaea4ec: stur            w0, [x1, #0x13]
    // 0xaea4f0: r0 = Instance_MainAxisSize
    //     0xaea4f0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaea4f4: ldr             x0, [x0, #0x738]
    // 0xaea4f8: ArrayStore: r1[0] = r0  ; List_4
    //     0xaea4f8: stur            w0, [x1, #0x17]
    // 0xaea4fc: r0 = Instance_CrossAxisAlignment
    //     0xaea4fc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaea500: ldr             x0, [x0, #0x740]
    // 0xaea504: StoreField: r1->field_1b = r0
    //     0xaea504: stur            w0, [x1, #0x1b]
    // 0xaea508: r0 = Instance_VerticalDirection
    //     0xaea508: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaea50c: ldr             x0, [x0, #0x748]
    // 0xaea510: StoreField: r1->field_23 = r0
    //     0xaea510: stur            w0, [x1, #0x23]
    // 0xaea514: r0 = Instance_Clip
    //     0xaea514: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaea518: ldr             x0, [x0, #0x750]
    // 0xaea51c: StoreField: r1->field_2b = r0
    //     0xaea51c: stur            w0, [x1, #0x2b]
    // 0xaea520: StoreField: r1->field_2f = rZR
    //     0xaea520: stur            xzr, [x1, #0x2f]
    // 0xaea524: ldur            x0, [fp, #-0x18]
    // 0xaea528: StoreField: r1->field_b = r0
    //     0xaea528: stur            w0, [x1, #0xb]
    // 0xaea52c: r0 = Container()
    //     0xaea52c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaea530: stur            x0, [fp, #-0x18]
    // 0xaea534: ldur            x16, [fp, #-0x30]
    // 0xaea538: r30 = Instance_EdgeInsets
    //     0xaea538: add             lr, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xaea53c: ldr             lr, [lr, #0xde8]
    // 0xaea540: stp             lr, x16, [SP, #8]
    // 0xaea544: ldur            x16, [fp, #-8]
    // 0xaea548: str             x16, [SP]
    // 0xaea54c: mov             x1, x0
    // 0xaea550: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xaea550: add             x4, PP, #0x32, lsl #12  ; [pp+0x323e0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xaea554: ldr             x4, [x4, #0x3e0]
    // 0xaea558: r0 = Container()
    //     0xaea558: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaea55c: r0 = InkWell()
    //     0xaea55c: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaea560: mov             x3, x0
    // 0xaea564: ldur            x0, [fp, #-0x18]
    // 0xaea568: stur            x3, [fp, #-8]
    // 0xaea56c: StoreField: r3->field_b = r0
    //     0xaea56c: stur            w0, [x3, #0xb]
    // 0xaea570: ldur            x2, [fp, #-0x10]
    // 0xaea574: r1 = Function '<anonymous closure>':.
    //     0xaea574: add             x1, PP, #0x35, lsl #12  ; [pp+0x355c8] AnonymousClosure: (0xaea5dc), in [package:nuonline/app/modules/donation/widgets/donation_form.dart] DonationFormWidget::build (0xae98d4)
    //     0xaea578: ldr             x1, [x1, #0x5c8]
    // 0xaea57c: r0 = AllocateClosure()
    //     0xaea57c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaea580: mov             x1, x0
    // 0xaea584: ldur            x0, [fp, #-8]
    // 0xaea588: StoreField: r0->field_f = r1
    //     0xaea588: stur            w1, [x0, #0xf]
    // 0xaea58c: r1 = true
    //     0xaea58c: add             x1, NULL, #0x20  ; true
    // 0xaea590: StoreField: r0->field_43 = r1
    //     0xaea590: stur            w1, [x0, #0x43]
    // 0xaea594: r2 = Instance_BoxShape
    //     0xaea594: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xaea598: ldr             x2, [x2, #0xca8]
    // 0xaea59c: StoreField: r0->field_47 = r2
    //     0xaea59c: stur            w2, [x0, #0x47]
    // 0xaea5a0: StoreField: r0->field_6f = r1
    //     0xaea5a0: stur            w1, [x0, #0x6f]
    // 0xaea5a4: r2 = false
    //     0xaea5a4: add             x2, NULL, #0x30  ; false
    // 0xaea5a8: StoreField: r0->field_73 = r2
    //     0xaea5a8: stur            w2, [x0, #0x73]
    // 0xaea5ac: StoreField: r0->field_83 = r1
    //     0xaea5ac: stur            w1, [x0, #0x83]
    // 0xaea5b0: StoreField: r0->field_7b = r2
    //     0xaea5b0: stur            w2, [x0, #0x7b]
    // 0xaea5b4: LeaveFrame
    //     0xaea5b4: mov             SP, fp
    //     0xaea5b8: ldp             fp, lr, [SP], #0x10
    // 0xaea5bc: ret
    //     0xaea5bc: ret             
    // 0xaea5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaea5c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaea5c4: b               #0xaea0a8
    // 0xaea5c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaea5c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaea5cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaea5cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaea5d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaea5d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaea5d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaea5d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaea5d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaea5d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaea5dc, size: 0xe4
    // 0xaea5dc: EnterFrame
    //     0xaea5dc: stp             fp, lr, [SP, #-0x10]!
    //     0xaea5e0: mov             fp, SP
    // 0xaea5e4: AllocStack(0x18)
    //     0xaea5e4: sub             SP, SP, #0x18
    // 0xaea5e8: SetupParameters()
    //     0xaea5e8: ldr             x0, [fp, #0x10]
    //     0xaea5ec: ldur            w2, [x0, #0x17]
    //     0xaea5f0: add             x2, x2, HEAP, lsl #32
    //     0xaea5f4: stur            x2, [fp, #-0x10]
    // 0xaea5f8: CheckStackOverflow
    //     0xaea5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaea5fc: cmp             SP, x16
    //     0xaea600: b.ls            #0xaea6b8
    // 0xaea604: LoadField: r0 = r2->field_b
    //     0xaea604: ldur            w0, [x2, #0xb]
    // 0xaea608: DecompressPointer r0
    //     0xaea608: add             x0, x0, HEAP, lsl #32
    // 0xaea60c: stur            x0, [fp, #-8]
    // 0xaea610: LoadField: r1 = r0->field_f
    //     0xaea610: ldur            w1, [x0, #0xf]
    // 0xaea614: DecompressPointer r1
    //     0xaea614: add             x1, x1, HEAP, lsl #32
    // 0xaea618: r0 = controller()
    //     0xaea618: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea61c: r1 = 60
    //     0xaea61c: movz            x1, #0x3c
    // 0xaea620: branchIfSmi(r0, 0xaea62c)
    //     0xaea620: tbz             w0, #0, #0xaea62c
    // 0xaea624: r1 = LoadClassIdInstr(r0)
    //     0xaea624: ldur            x1, [x0, #-1]
    //     0xaea628: ubfx            x1, x1, #0xc, #0x14
    // 0xaea62c: str             x0, [SP]
    // 0xaea630: mov             x0, x1
    // 0xaea634: r0 = GDT[cid_x0 + -0xf1e]()
    //     0xaea634: sub             lr, x0, #0xf1e
    //     0xaea638: ldr             lr, [x21, lr, lsl #3]
    //     0xaea63c: blr             lr
    // 0xaea640: mov             x1, x0
    // 0xaea644: ldur            x0, [fp, #-0x10]
    // 0xaea648: LoadField: r2 = r0->field_f
    //     0xaea648: ldur            w2, [x0, #0xf]
    // 0xaea64c: DecompressPointer r2
    //     0xaea64c: add             x2, x2, HEAP, lsl #32
    // 0xaea650: LoadField: r0 = r2->field_f
    //     0xaea650: ldur            w0, [x2, #0xf]
    // 0xaea654: DecompressPointer r0
    //     0xaea654: add             x0, x0, HEAP, lsl #32
    // 0xaea658: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaea658: ldur            w2, [x1, #0x17]
    // 0xaea65c: DecompressPointer r2
    //     0xaea65c: add             x2, x2, HEAP, lsl #32
    // 0xaea660: mov             x1, x2
    // 0xaea664: mov             x2, x0
    // 0xaea668: r0 = onSuggestionChanged()
    //     0xaea668: bl              #0xae821c  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::onSuggestionChanged
    // 0xaea66c: ldur            x0, [fp, #-8]
    // 0xaea670: LoadField: r1 = r0->field_13
    //     0xaea670: ldur            w1, [x0, #0x13]
    // 0xaea674: DecompressPointer r1
    //     0xaea674: add             x1, x1, HEAP, lsl #32
    // 0xaea678: r0 = of()
    //     0xaea678: bl              #0x98f804  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xaea67c: stur            x0, [fp, #-8]
    // 0xaea680: r0 = FocusNode()
    //     0xaea680: bl              #0x811904  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xaea684: mov             x1, x0
    // 0xaea688: stur            x0, [fp, #-0x10]
    // 0xaea68c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaea68c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaea690: r0 = FocusNode()
    //     0xaea690: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xaea694: ldur            x16, [fp, #-0x10]
    // 0xaea698: str             x16, [SP]
    // 0xaea69c: ldur            x1, [fp, #-8]
    // 0xaea6a0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xaea6a0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xaea6a4: r0 = requestFocus()
    //     0xaea6a4: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xaea6a8: r0 = Null
    //     0xaea6a8: mov             x0, NULL
    // 0xaea6ac: LeaveFrame
    //     0xaea6ac: mov             SP, fp
    //     0xaea6b0: ldp             fp, lr, [SP], #0x10
    // 0xaea6b4: ret
    //     0xaea6b4: ret             
    // 0xaea6b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaea6b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaea6bc: b               #0xaea604
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xaea6c0, size: 0x32c
    // 0xaea6c0: EnterFrame
    //     0xaea6c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaea6c4: mov             fp, SP
    // 0xaea6c8: AllocStack(0x30)
    //     0xaea6c8: sub             SP, SP, #0x30
    // 0xaea6cc: SetupParameters()
    //     0xaea6cc: ldr             x0, [fp, #0x10]
    //     0xaea6d0: ldur            w3, [x0, #0x17]
    //     0xaea6d4: add             x3, x3, HEAP, lsl #32
    //     0xaea6d8: stur            x3, [fp, #-8]
    // 0xaea6dc: CheckStackOverflow
    //     0xaea6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaea6e0: cmp             SP, x16
    //     0xaea6e4: b.ls            #0xaea9e4
    // 0xaea6e8: r1 = <Widget>
    //     0xaea6e8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaea6ec: r2 = 0
    //     0xaea6ec: movz            x2, #0
    // 0xaea6f0: r0 = _GrowableList()
    //     0xaea6f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaea6f4: mov             x2, x0
    // 0xaea6f8: ldur            x0, [fp, #-8]
    // 0xaea6fc: stur            x2, [fp, #-0x10]
    // 0xaea700: LoadField: r1 = r0->field_f
    //     0xaea700: ldur            w1, [x0, #0xf]
    // 0xaea704: DecompressPointer r1
    //     0xaea704: add             x1, x1, HEAP, lsl #32
    // 0xaea708: r0 = controller()
    //     0xaea708: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea70c: mov             x1, x0
    // 0xaea710: r0 = showDonationNuonline()
    //     0xaea710: bl              #0x8f7f60  ; [package:nuonline/app/modules/donation/controllers/donation_form_controller.dart] DonationFormController::showDonationNuonline
    // 0xaea714: tbnz            w0, #4, #0xaea818
    // 0xaea718: ldur            x0, [fp, #-8]
    // 0xaea71c: LoadField: r1 = r0->field_f
    //     0xaea71c: ldur            w1, [x0, #0xf]
    // 0xaea720: DecompressPointer r1
    //     0xaea720: add             x1, x1, HEAP, lsl #32
    // 0xaea724: r0 = controller()
    //     0xaea724: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea728: LoadField: r1 = r0->field_2f
    //     0xaea728: ldur            w1, [x0, #0x2f]
    // 0xaea72c: DecompressPointer r1
    //     0xaea72c: add             x1, x1, HEAP, lsl #32
    // 0xaea730: r0 = value()
    //     0xaea730: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaea734: mov             x2, x0
    // 0xaea738: ldur            x0, [fp, #-8]
    // 0xaea73c: stur            x2, [fp, #-0x18]
    // 0xaea740: LoadField: r1 = r0->field_f
    //     0xaea740: ldur            w1, [x0, #0xf]
    // 0xaea744: DecompressPointer r1
    //     0xaea744: add             x1, x1, HEAP, lsl #32
    // 0xaea748: r0 = controller()
    //     0xaea748: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea74c: LoadField: r2 = r0->field_2f
    //     0xaea74c: ldur            w2, [x0, #0x2f]
    // 0xaea750: DecompressPointer r2
    //     0xaea750: add             x2, x2, HEAP, lsl #32
    // 0xaea754: LoadField: r3 = r2->field_7
    //     0xaea754: ldur            w3, [x2, #7]
    // 0xaea758: DecompressPointer r3
    //     0xaea758: add             x3, x3, HEAP, lsl #32
    // 0xaea75c: r1 = Function 'call':.
    //     0xaea75c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaea760: ldr             x1, [x1, #0x310]
    // 0xaea764: r0 = AllocateClosureTA()
    //     0xaea764: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaea768: mov             x3, x0
    // 0xaea76c: r2 = Null
    //     0xaea76c: mov             x2, NULL
    // 0xaea770: r1 = Null
    //     0xaea770: mov             x1, NULL
    // 0xaea774: stur            x3, [fp, #-0x20]
    // 0xaea778: r8 = (dynamic this, ZakatTypes?) => ZakatTypes
    //     0xaea778: add             x8, PP, #0x35, lsl #12  ; [pp+0x355d0] FunctionType: (dynamic this, ZakatTypes?) => ZakatTypes
    //     0xaea77c: ldr             x8, [x8, #0x5d0]
    // 0xaea780: r3 = Null
    //     0xaea780: add             x3, PP, #0x35, lsl #12  ; [pp+0x355d8] Null
    //     0xaea784: ldr             x3, [x3, #0x5d8]
    // 0xaea788: r0 = DefaultTypeTest()
    //     0xaea788: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaea78c: r1 = <ZakatTypes>
    //     0xaea78c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xaea790: ldr             x1, [x1, #0x68]
    // 0xaea794: r0 = NRadioLeftListTile()
    //     0xaea794: bl              #0xaea9ec  ; AllocateNRadioLeftListTileStub -> NRadioLeftListTile<X0> (size=0x20)
    // 0xaea798: mov             x3, x0
    // 0xaea79c: r0 = "Pengembangan NU Online"
    //     0xaea79c: add             x0, PP, #0x35, lsl #12  ; [pp+0x355e8] "Pengembangan NU Online"
    //     0xaea7a0: ldr             x0, [x0, #0x5e8]
    // 0xaea7a4: stur            x3, [fp, #-0x28]
    // 0xaea7a8: StoreField: r3->field_f = r0
    //     0xaea7a8: stur            w0, [x3, #0xf]
    // 0xaea7ac: r0 = Instance_ZakatTypes
    //     0xaea7ac: add             x0, PP, #0x30, lsl #12  ; [pp+0x30070] Obj!ZakatTypes@e307c1
    //     0xaea7b0: ldr             x0, [x0, #0x70]
    // 0xaea7b4: StoreField: r3->field_13 = r0
    //     0xaea7b4: stur            w0, [x3, #0x13]
    // 0xaea7b8: ldur            x0, [fp, #-0x18]
    // 0xaea7bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xaea7bc: stur            w0, [x3, #0x17]
    // 0xaea7c0: ldur            x0, [fp, #-0x20]
    // 0xaea7c4: StoreField: r3->field_1b = r0
    //     0xaea7c4: stur            w0, [x3, #0x1b]
    // 0xaea7c8: r1 = Null
    //     0xaea7c8: mov             x1, NULL
    // 0xaea7cc: r2 = 4
    //     0xaea7cc: movz            x2, #0x4
    // 0xaea7d0: r0 = AllocateArray()
    //     0xaea7d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaea7d4: mov             x2, x0
    // 0xaea7d8: ldur            x0, [fp, #-0x28]
    // 0xaea7dc: stur            x2, [fp, #-0x18]
    // 0xaea7e0: StoreField: r2->field_f = r0
    //     0xaea7e0: stur            w0, [x2, #0xf]
    // 0xaea7e4: r16 = Instance_SizedBox
    //     0xaea7e4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaea7e8: ldr             x16, [x16, #0x950]
    // 0xaea7ec: StoreField: r2->field_13 = r16
    //     0xaea7ec: stur            w16, [x2, #0x13]
    // 0xaea7f0: r1 = <Widget>
    //     0xaea7f0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaea7f4: r0 = AllocateGrowableArray()
    //     0xaea7f4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaea7f8: mov             x1, x0
    // 0xaea7fc: ldur            x0, [fp, #-0x18]
    // 0xaea800: StoreField: r1->field_f = r0
    //     0xaea800: stur            w0, [x1, #0xf]
    // 0xaea804: r0 = 4
    //     0xaea804: movz            x0, #0x4
    // 0xaea808: StoreField: r1->field_b = r0
    //     0xaea808: stur            w0, [x1, #0xb]
    // 0xaea80c: mov             x2, x1
    // 0xaea810: ldur            x1, [fp, #-0x10]
    // 0xaea814: r0 = addAll()
    //     0xaea814: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaea818: ldur            x0, [fp, #-8]
    // 0xaea81c: LoadField: r1 = r0->field_f
    //     0xaea81c: ldur            w1, [x0, #0xf]
    // 0xaea820: DecompressPointer r1
    //     0xaea820: add             x1, x1, HEAP, lsl #32
    // 0xaea824: r0 = controller()
    //     0xaea824: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea828: mov             x1, x0
    // 0xaea82c: LoadField: r0 = r1->field_3b
    //     0xaea82c: ldur            w0, [x1, #0x3b]
    // 0xaea830: DecompressPointer r0
    //     0xaea830: add             x0, x0, HEAP, lsl #32
    // 0xaea834: r16 = Sentinel
    //     0xaea834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaea838: cmp             w0, w16
    // 0xaea83c: b.ne            #0xaea84c
    // 0xaea840: r2 = showDonationNuonlineOnly
    //     0xaea840: add             x2, PP, #0x35, lsl #12  ; [pp+0x355f0] Field <DonationFormController.showDonationNuonlineOnly>: late final (offset: 0x3c)
    //     0xaea844: ldr             x2, [x2, #0x5f0]
    // 0xaea848: r0 = InitLateFinalInstanceField()
    //     0xaea848: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xaea84c: tbz             w0, #4, #0xaea980
    // 0xaea850: ldur            x0, [fp, #-8]
    // 0xaea854: ldur            x2, [fp, #-0x10]
    // 0xaea858: LoadField: r1 = r0->field_f
    //     0xaea858: ldur            w1, [x0, #0xf]
    // 0xaea85c: DecompressPointer r1
    //     0xaea85c: add             x1, x1, HEAP, lsl #32
    // 0xaea860: r0 = controller()
    //     0xaea860: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea864: LoadField: r1 = r0->field_2f
    //     0xaea864: ldur            w1, [x0, #0x2f]
    // 0xaea868: DecompressPointer r1
    //     0xaea868: add             x1, x1, HEAP, lsl #32
    // 0xaea86c: r0 = value()
    //     0xaea86c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaea870: mov             x2, x0
    // 0xaea874: ldur            x0, [fp, #-8]
    // 0xaea878: stur            x2, [fp, #-0x18]
    // 0xaea87c: LoadField: r1 = r0->field_f
    //     0xaea87c: ldur            w1, [x0, #0xf]
    // 0xaea880: DecompressPointer r1
    //     0xaea880: add             x1, x1, HEAP, lsl #32
    // 0xaea884: r0 = controller()
    //     0xaea884: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaea888: LoadField: r2 = r0->field_2f
    //     0xaea888: ldur            w2, [x0, #0x2f]
    // 0xaea88c: DecompressPointer r2
    //     0xaea88c: add             x2, x2, HEAP, lsl #32
    // 0xaea890: LoadField: r3 = r2->field_7
    //     0xaea890: ldur            w3, [x2, #7]
    // 0xaea894: DecompressPointer r3
    //     0xaea894: add             x3, x3, HEAP, lsl #32
    // 0xaea898: r1 = Function 'call':.
    //     0xaea898: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaea89c: ldr             x1, [x1, #0x310]
    // 0xaea8a0: r0 = AllocateClosureTA()
    //     0xaea8a0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaea8a4: mov             x3, x0
    // 0xaea8a8: r2 = Null
    //     0xaea8a8: mov             x2, NULL
    // 0xaea8ac: r1 = Null
    //     0xaea8ac: mov             x1, NULL
    // 0xaea8b0: stur            x3, [fp, #-8]
    // 0xaea8b4: r8 = (dynamic this, ZakatTypes?) => ZakatTypes
    //     0xaea8b4: add             x8, PP, #0x35, lsl #12  ; [pp+0x355d0] FunctionType: (dynamic this, ZakatTypes?) => ZakatTypes
    //     0xaea8b8: ldr             x8, [x8, #0x5d0]
    // 0xaea8bc: r3 = Null
    //     0xaea8bc: add             x3, PP, #0x35, lsl #12  ; [pp+0x355f8] Null
    //     0xaea8c0: ldr             x3, [x3, #0x5f8]
    // 0xaea8c4: r0 = DefaultTypeTest()
    //     0xaea8c4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaea8c8: r1 = <ZakatTypes>
    //     0xaea8c8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xaea8cc: ldr             x1, [x1, #0x68]
    // 0xaea8d0: r0 = NRadioLeftListTile()
    //     0xaea8d0: bl              #0xaea9ec  ; AllocateNRadioLeftListTileStub -> NRadioLeftListTile<X0> (size=0x20)
    // 0xaea8d4: mov             x2, x0
    // 0xaea8d8: r0 = "Infak & Sedekah"
    //     0xaea8d8: add             x0, PP, #0x30, lsl #12  ; [pp+0x302d8] "Infak & Sedekah"
    //     0xaea8dc: ldr             x0, [x0, #0x2d8]
    // 0xaea8e0: stur            x2, [fp, #-0x20]
    // 0xaea8e4: StoreField: r2->field_f = r0
    //     0xaea8e4: stur            w0, [x2, #0xf]
    // 0xaea8e8: r0 = Instance_ZakatTypes
    //     0xaea8e8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30528] Obj!ZakatTypes@e307a1
    //     0xaea8ec: ldr             x0, [x0, #0x528]
    // 0xaea8f0: StoreField: r2->field_13 = r0
    //     0xaea8f0: stur            w0, [x2, #0x13]
    // 0xaea8f4: ldur            x0, [fp, #-0x18]
    // 0xaea8f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaea8f8: stur            w0, [x2, #0x17]
    // 0xaea8fc: ldur            x0, [fp, #-8]
    // 0xaea900: StoreField: r2->field_1b = r0
    //     0xaea900: stur            w0, [x2, #0x1b]
    // 0xaea904: ldur            x0, [fp, #-0x10]
    // 0xaea908: LoadField: r1 = r0->field_b
    //     0xaea908: ldur            w1, [x0, #0xb]
    // 0xaea90c: LoadField: r3 = r0->field_f
    //     0xaea90c: ldur            w3, [x0, #0xf]
    // 0xaea910: DecompressPointer r3
    //     0xaea910: add             x3, x3, HEAP, lsl #32
    // 0xaea914: LoadField: r4 = r3->field_b
    //     0xaea914: ldur            w4, [x3, #0xb]
    // 0xaea918: r3 = LoadInt32Instr(r1)
    //     0xaea918: sbfx            x3, x1, #1, #0x1f
    // 0xaea91c: stur            x3, [fp, #-0x30]
    // 0xaea920: r1 = LoadInt32Instr(r4)
    //     0xaea920: sbfx            x1, x4, #1, #0x1f
    // 0xaea924: cmp             x3, x1
    // 0xaea928: b.ne            #0xaea934
    // 0xaea92c: mov             x1, x0
    // 0xaea930: r0 = _growToNextCapacity()
    //     0xaea930: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaea934: ldur            x2, [fp, #-0x10]
    // 0xaea938: ldur            x3, [fp, #-0x30]
    // 0xaea93c: add             x0, x3, #1
    // 0xaea940: lsl             x1, x0, #1
    // 0xaea944: StoreField: r2->field_b = r1
    //     0xaea944: stur            w1, [x2, #0xb]
    // 0xaea948: LoadField: r1 = r2->field_f
    //     0xaea948: ldur            w1, [x2, #0xf]
    // 0xaea94c: DecompressPointer r1
    //     0xaea94c: add             x1, x1, HEAP, lsl #32
    // 0xaea950: ldur            x0, [fp, #-0x20]
    // 0xaea954: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaea954: add             x25, x1, x3, lsl #2
    //     0xaea958: add             x25, x25, #0xf
    //     0xaea95c: str             w0, [x25]
    //     0xaea960: tbz             w0, #0, #0xaea97c
    //     0xaea964: ldurb           w16, [x1, #-1]
    //     0xaea968: ldurb           w17, [x0, #-1]
    //     0xaea96c: and             x16, x17, x16, lsr #2
    //     0xaea970: tst             x16, HEAP, lsr #32
    //     0xaea974: b.eq            #0xaea97c
    //     0xaea978: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaea97c: b               #0xaea984
    // 0xaea980: ldur            x2, [fp, #-0x10]
    // 0xaea984: r0 = Column()
    //     0xaea984: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaea988: r1 = Instance_Axis
    //     0xaea988: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaea98c: StoreField: r0->field_f = r1
    //     0xaea98c: stur            w1, [x0, #0xf]
    // 0xaea990: r1 = Instance_MainAxisAlignment
    //     0xaea990: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaea994: ldr             x1, [x1, #0x730]
    // 0xaea998: StoreField: r0->field_13 = r1
    //     0xaea998: stur            w1, [x0, #0x13]
    // 0xaea99c: r1 = Instance_MainAxisSize
    //     0xaea99c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaea9a0: ldr             x1, [x1, #0x738]
    // 0xaea9a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xaea9a4: stur            w1, [x0, #0x17]
    // 0xaea9a8: r1 = Instance_CrossAxisAlignment
    //     0xaea9a8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaea9ac: ldr             x1, [x1, #0x740]
    // 0xaea9b0: StoreField: r0->field_1b = r1
    //     0xaea9b0: stur            w1, [x0, #0x1b]
    // 0xaea9b4: r1 = Instance_VerticalDirection
    //     0xaea9b4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaea9b8: ldr             x1, [x1, #0x748]
    // 0xaea9bc: StoreField: r0->field_23 = r1
    //     0xaea9bc: stur            w1, [x0, #0x23]
    // 0xaea9c0: r1 = Instance_Clip
    //     0xaea9c0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaea9c4: ldr             x1, [x1, #0x750]
    // 0xaea9c8: StoreField: r0->field_2b = r1
    //     0xaea9c8: stur            w1, [x0, #0x2b]
    // 0xaea9cc: StoreField: r0->field_2f = rZR
    //     0xaea9cc: stur            xzr, [x0, #0x2f]
    // 0xaea9d0: ldur            x1, [fp, #-0x10]
    // 0xaea9d4: StoreField: r0->field_b = r1
    //     0xaea9d4: stur            w1, [x0, #0xb]
    // 0xaea9d8: LeaveFrame
    //     0xaea9d8: mov             SP, fp
    //     0xaea9dc: ldp             fp, lr, [SP], #0x10
    // 0xaea9e0: ret
    //     0xaea9e0: ret             
    // 0xaea9e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaea9e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaea9e8: b               #0xaea6e8
  }
}
