// lib: , url: package:nuonline/app/modules/donation/widgets/donation_news_builder.dart

// class id: 1050232, size: 0x8
class :: {
}

// class id: 5285, size: 0x18, field offset: 0x14
//   const constructor, 
class DonationNewsListBuilder extends GetView<dynamic> {

  _OneByteString field_14;

  _ build(/* No info */) {
    // ** addr: 0xaeaaa0, size: 0x12c
    // 0xaeaaa0: EnterFrame
    //     0xaeaaa0: stp             fp, lr, [SP, #-0x10]!
    //     0xaeaaa4: mov             fp, SP
    // 0xaeaaa8: AllocStack(0x20)
    //     0xaeaaa8: sub             SP, SP, #0x20
    // 0xaeaaac: SetupParameters(DonationNewsListBuilder this /* r1 => r1, fp-0x8 */)
    //     0xaeaaac: stur            x1, [fp, #-8]
    // 0xaeaab0: CheckStackOverflow
    //     0xaeaab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeaab4: cmp             SP, x16
    //     0xaeaab8: b.ls            #0xaeabc4
    // 0xaeaabc: r1 = 1
    //     0xaeaabc: movz            x1, #0x1
    // 0xaeaac0: r0 = AllocateContext()
    //     0xaeaac0: bl              #0xec126c  ; AllocateContextStub
    // 0xaeaac4: mov             x2, x0
    // 0xaeaac8: ldur            x0, [fp, #-8]
    // 0xaeaacc: stur            x2, [fp, #-0x10]
    // 0xaeaad0: StoreField: r2->field_f = r0
    //     0xaeaad0: stur            w0, [x2, #0xf]
    // 0xaeaad4: mov             x1, x0
    // 0xaeaad8: r0 = controller()
    //     0xaeaad8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeaadc: ldur            x1, [fp, #-8]
    // 0xaeaae0: stur            x0, [fp, #-8]
    // 0xaeaae4: r0 = controller()
    //     0xaeaae4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeaae8: stur            x0, [fp, #-0x18]
    // 0xaeaaec: r0 = Obx()
    //     0xaeaaec: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeaaf0: ldur            x2, [fp, #-0x10]
    // 0xaeaaf4: r1 = Function '<anonymous closure>':.
    //     0xaeaaf4: add             x1, PP, #0x35, lsl #12  ; [pp+0x350e0] AnonymousClosure: (0xaeadc0), in [package:nuonline/app/modules/donation/widgets/donation_news_builder.dart] DonationNewsListBuilder::build (0xaeaaa0)
    //     0xaeaaf8: ldr             x1, [x1, #0xe0]
    // 0xaeaafc: stur            x0, [fp, #-0x10]
    // 0xaeab00: r0 = AllocateClosure()
    //     0xaeab00: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeab04: mov             x1, x0
    // 0xaeab08: ldur            x0, [fp, #-0x10]
    // 0xaeab0c: StoreField: r0->field_b = r1
    //     0xaeab0c: stur            w1, [x0, #0xb]
    // 0xaeab10: ldur            x2, [fp, #-0x18]
    // 0xaeab14: r1 = Function 'onPageScrolled':.
    //     0xaeab14: add             x1, PP, #0x35, lsl #12  ; [pp+0x350e8] AnonymousClosure: (0xaeacb8), in [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageScrolled (0xaeacf4)
    //     0xaeab18: ldr             x1, [x1, #0xe8]
    // 0xaeab1c: r0 = AllocateClosure()
    //     0xaeab1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeab20: r1 = <ScrollNotification>
    //     0xaeab20: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xaeab24: ldr             x1, [x1, #0x110]
    // 0xaeab28: stur            x0, [fp, #-0x18]
    // 0xaeab2c: r0 = NotificationListener()
    //     0xaeab2c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xaeab30: mov             x1, x0
    // 0xaeab34: ldur            x0, [fp, #-0x18]
    // 0xaeab38: stur            x1, [fp, #-0x20]
    // 0xaeab3c: StoreField: r1->field_13 = r0
    //     0xaeab3c: stur            w0, [x1, #0x13]
    // 0xaeab40: ldur            x0, [fp, #-0x10]
    // 0xaeab44: StoreField: r1->field_b = r0
    //     0xaeab44: stur            w0, [x1, #0xb]
    // 0xaeab48: r0 = RefreshIndicator()
    //     0xaeab48: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xaeab4c: mov             x3, x0
    // 0xaeab50: ldur            x0, [fp, #-0x20]
    // 0xaeab54: stur            x3, [fp, #-0x10]
    // 0xaeab58: StoreField: r3->field_b = r0
    //     0xaeab58: stur            w0, [x3, #0xb]
    // 0xaeab5c: d0 = 40.000000
    //     0xaeab5c: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xaeab60: StoreField: r3->field_f = d0
    //     0xaeab60: stur            d0, [x3, #0xf]
    // 0xaeab64: ArrayStore: r3[0] = rZR  ; List_8
    //     0xaeab64: stur            xzr, [x3, #0x17]
    // 0xaeab68: ldur            x2, [fp, #-8]
    // 0xaeab6c: r1 = Function 'onPageRefresh':.
    //     0xaeab6c: add             x1, PP, #0x35, lsl #12  ; [pp+0x350f0] AnonymousClosure: (0xaeabcc), in [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageRefresh (0xaeac04)
    //     0xaeab70: ldr             x1, [x1, #0xf0]
    // 0xaeab74: r0 = AllocateClosure()
    //     0xaeab74: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeab78: mov             x1, x0
    // 0xaeab7c: ldur            x0, [fp, #-0x10]
    // 0xaeab80: StoreField: r0->field_1f = r1
    //     0xaeab80: stur            w1, [x0, #0x1f]
    // 0xaeab84: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xaeab84: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xaeab88: ldr             x1, [x1, #0xf58]
    // 0xaeab8c: StoreField: r0->field_2f = r1
    //     0xaeab8c: stur            w1, [x0, #0x2f]
    // 0xaeab90: d0 = 2.500000
    //     0xaeab90: fmov            d0, #2.50000000
    // 0xaeab94: StoreField: r0->field_3b = d0
    //     0xaeab94: stur            d0, [x0, #0x3b]
    // 0xaeab98: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xaeab98: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xaeab9c: ldr             x1, [x1, #0xa68]
    // 0xaeaba0: StoreField: r0->field_47 = r1
    //     0xaeaba0: stur            w1, [x0, #0x47]
    // 0xaeaba4: d0 = 2.000000
    //     0xaeaba4: fmov            d0, #2.00000000
    // 0xaeaba8: StoreField: r0->field_4b = d0
    //     0xaeaba8: stur            d0, [x0, #0x4b]
    // 0xaeabac: r1 = Instance__IndicatorType
    //     0xaeabac: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xaeabb0: ldr             x1, [x1, #0xa70]
    // 0xaeabb4: StoreField: r0->field_43 = r1
    //     0xaeabb4: stur            w1, [x0, #0x43]
    // 0xaeabb8: LeaveFrame
    //     0xaeabb8: mov             SP, fp
    //     0xaeabbc: ldp             fp, lr, [SP], #0x10
    // 0xaeabc0: ret
    //     0xaeabc0: ret             
    // 0xaeabc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeabc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeabc8: b               #0xaeaabc
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0xaeadc0, size: 0x100
    // 0xaeadc0: EnterFrame
    //     0xaeadc0: stp             fp, lr, [SP, #-0x10]!
    //     0xaeadc4: mov             fp, SP
    // 0xaeadc8: AllocStack(0x30)
    //     0xaeadc8: sub             SP, SP, #0x30
    // 0xaeadcc: SetupParameters()
    //     0xaeadcc: ldr             x0, [fp, #0x10]
    //     0xaeadd0: ldur            w2, [x0, #0x17]
    //     0xaeadd4: add             x2, x2, HEAP, lsl #32
    //     0xaeadd8: stur            x2, [fp, #-8]
    // 0xaeaddc: CheckStackOverflow
    //     0xaeaddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeade0: cmp             SP, x16
    //     0xaeade4: b.ls            #0xaeaeb8
    // 0xaeade8: LoadField: r1 = r2->field_f
    //     0xaeade8: ldur            w1, [x2, #0xf]
    // 0xaeadec: DecompressPointer r1
    //     0xaeadec: add             x1, x1, HEAP, lsl #32
    // 0xaeadf0: r0 = controller()
    //     0xaeadf0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeadf4: mov             x1, x0
    // 0xaeadf8: r0 = query()
    //     0xaeadf8: bl              #0xada394  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::query
    // 0xaeadfc: tbnz            w0, #4, #0xaeae3c
    // 0xaeae00: r0 = NEmptyState()
    //     0xaeae00: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xaeae04: mov             x1, x0
    // 0xaeae08: r2 = "Jika tersedia, kabar terbaru akan ditampilkan pada halaman ini."
    //     0xaeae08: add             x2, PP, #0x35, lsl #12  ; [pp+0x350f8] "Jika tersedia, kabar terbaru akan ditampilkan pada halaman ini."
    //     0xaeae0c: ldr             x2, [x2, #0xf8]
    // 0xaeae10: r3 = "assets/images/illustration/no_search.svg"
    //     0xaeae10: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xaeae14: ldr             x3, [x3, #0x138]
    // 0xaeae18: r5 = "Belum Ada Kabar Terbaru"
    //     0xaeae18: add             x5, PP, #0x35, lsl #12  ; [pp+0x35100] "Belum Ada Kabar Terbaru"
    //     0xaeae1c: ldr             x5, [x5, #0x100]
    // 0xaeae20: stur            x0, [fp, #-0x10]
    // 0xaeae24: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xaeae24: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xaeae28: r0 = NEmptyState.svg()
    //     0xaeae28: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xaeae2c: ldur            x0, [fp, #-0x10]
    // 0xaeae30: LeaveFrame
    //     0xaeae30: mov             SP, fp
    //     0xaeae34: ldp             fp, lr, [SP], #0x10
    // 0xaeae38: ret
    //     0xaeae38: ret             
    // 0xaeae3c: ldur            x2, [fp, #-8]
    // 0xaeae40: LoadField: r1 = r2->field_f
    //     0xaeae40: ldur            w1, [x2, #0xf]
    // 0xaeae44: DecompressPointer r1
    //     0xaeae44: add             x1, x1, HEAP, lsl #32
    // 0xaeae48: r0 = controller()
    //     0xaeae48: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeae4c: mov             x1, x0
    // 0xaeae50: r0 = itemsCount()
    //     0xaeae50: bl              #0xaeaec0  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::itemsCount
    // 0xaeae54: ldur            x2, [fp, #-8]
    // 0xaeae58: r1 = Function '<anonymous closure>':.
    //     0xaeae58: add             x1, PP, #0x35, lsl #12  ; [pp+0x35108] AnonymousClosure: (0xaeafa4), in [package:nuonline/app/modules/donation/widgets/donation_news_builder.dart] DonationNewsListBuilder::build (0xaeaaa0)
    //     0xaeae5c: ldr             x1, [x1, #0x108]
    // 0xaeae60: stur            x0, [fp, #-0x18]
    // 0xaeae64: r0 = AllocateClosure()
    //     0xaeae64: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeae68: stur            x0, [fp, #-8]
    // 0xaeae6c: r0 = ListView()
    //     0xaeae6c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaeae70: stur            x0, [fp, #-0x10]
    // 0xaeae74: r16 = true
    //     0xaeae74: add             x16, NULL, #0x20  ; true
    // 0xaeae78: r30 = Instance_NeverScrollableScrollPhysics
    //     0xaeae78: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xaeae7c: ldr             lr, [lr, #0x290]
    // 0xaeae80: stp             lr, x16, [SP, #8]
    // 0xaeae84: r16 = Instance_EdgeInsets
    //     0xaeae84: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bce8] Obj!EdgeInsets@e123d1
    //     0xaeae88: ldr             x16, [x16, #0xce8]
    // 0xaeae8c: str             x16, [SP]
    // 0xaeae90: mov             x1, x0
    // 0xaeae94: ldur            x2, [fp, #-8]
    // 0xaeae98: ldur            x3, [fp, #-0x18]
    // 0xaeae9c: r4 = const [0, 0x6, 0x3, 0x3, padding, 0x5, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xaeae9c: add             x4, PP, #0x35, lsl #12  ; [pp+0x35110] List(11) [0, 0x6, 0x3, 0x3, "padding", 0x5, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xaeaea0: ldr             x4, [x4, #0x110]
    // 0xaeaea4: r0 = ListView.builder()
    //     0xaeaea4: bl              #0xa2f6f8  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaeaea8: ldur            x0, [fp, #-0x10]
    // 0xaeaeac: LeaveFrame
    //     0xaeaeac: mov             SP, fp
    //     0xaeaeb0: ldp             fp, lr, [SP], #0x10
    // 0xaeaeb4: ret
    //     0xaeaeb4: ret             
    // 0xaeaeb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeaeb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeaebc: b               #0xaeade8
  }
  [closure] TimelineNewsCard <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaeafa4, size: 0xb8
    // 0xaeafa4: EnterFrame
    //     0xaeafa4: stp             fp, lr, [SP, #-0x10]!
    //     0xaeafa8: mov             fp, SP
    // 0xaeafac: AllocStack(0x18)
    //     0xaeafac: sub             SP, SP, #0x18
    // 0xaeafb0: SetupParameters()
    //     0xaeafb0: ldr             x0, [fp, #0x20]
    //     0xaeafb4: ldur            w2, [x0, #0x17]
    //     0xaeafb8: add             x2, x2, HEAP, lsl #32
    //     0xaeafbc: stur            x2, [fp, #-8]
    // 0xaeafc0: CheckStackOverflow
    //     0xaeafc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeafc4: cmp             SP, x16
    //     0xaeafc8: b.ls            #0xaeb054
    // 0xaeafcc: LoadField: r1 = r2->field_f
    //     0xaeafcc: ldur            w1, [x2, #0xf]
    // 0xaeafd0: DecompressPointer r1
    //     0xaeafd0: add             x1, x1, HEAP, lsl #32
    // 0xaeafd4: r0 = controller()
    //     0xaeafd4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeafd8: mov             x1, x0
    // 0xaeafdc: ldr             x0, [fp, #0x10]
    // 0xaeafe0: r3 = LoadInt32Instr(r0)
    //     0xaeafe0: sbfx            x3, x0, #1, #0x1f
    //     0xaeafe4: tbz             w0, #0, #0xaeafec
    //     0xaeafe8: ldur            x3, [x0, #7]
    // 0xaeafec: mov             x2, x3
    // 0xaeaff0: stur            x3, [fp, #-0x10]
    // 0xaeaff4: r0 = find()
    //     0xaeaff4: bl              #0xaeb068  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::find
    // 0xaeaff8: mov             x2, x0
    // 0xaeaffc: ldur            x0, [fp, #-8]
    // 0xaeb000: stur            x2, [fp, #-0x18]
    // 0xaeb004: LoadField: r1 = r0->field_f
    //     0xaeb004: ldur            w1, [x0, #0xf]
    // 0xaeb008: DecompressPointer r1
    //     0xaeb008: add             x1, x1, HEAP, lsl #32
    // 0xaeb00c: r0 = controller()
    //     0xaeb00c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb010: mov             x1, x0
    // 0xaeb014: r0 = itemsCount()
    //     0xaeb014: bl              #0xaeaec0  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::itemsCount
    // 0xaeb018: sub             x1, x0, #1
    // 0xaeb01c: ldur            x0, [fp, #-0x10]
    // 0xaeb020: cmp             x0, x1
    // 0xaeb024: r16 = true
    //     0xaeb024: add             x16, NULL, #0x20  ; true
    // 0xaeb028: r17 = false
    //     0xaeb028: add             x17, NULL, #0x30  ; false
    // 0xaeb02c: csel            x2, x16, x17, eq
    // 0xaeb030: stur            x2, [fp, #-8]
    // 0xaeb034: r0 = TimelineNewsCard()
    //     0xaeb034: bl              #0xaeb05c  ; AllocateTimelineNewsCardStub -> TimelineNewsCard (size=0x14)
    // 0xaeb038: ldur            x1, [fp, #-0x18]
    // 0xaeb03c: StoreField: r0->field_b = r1
    //     0xaeb03c: stur            w1, [x0, #0xb]
    // 0xaeb040: ldur            x1, [fp, #-8]
    // 0xaeb044: StoreField: r0->field_f = r1
    //     0xaeb044: stur            w1, [x0, #0xf]
    // 0xaeb048: LeaveFrame
    //     0xaeb048: mov             SP, fp
    //     0xaeb04c: ldp             fp, lr, [SP], #0x10
    // 0xaeb050: ret
    //     0xaeb050: ret             
    // 0xaeb054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb054: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb058: b               #0xaeafcc
  }
}
