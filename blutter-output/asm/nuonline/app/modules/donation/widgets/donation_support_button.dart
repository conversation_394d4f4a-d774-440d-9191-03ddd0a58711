// lib: , url: package:nuonline/app/modules/donation/widgets/donation_support_button.dart

// class id: 1050233, size: 0x8
class :: {
}

// class id: 1892, size: 0x20, field offset: 0x20
//   transformed mixin,
abstract class _DonationSupportButtonController&GetxController&AnalyticMixin extends GetxController
     with AnalyticMixin {
}

// class id: 1897, size: 0x28, field offset: 0x20
class DonationSupportButtonController extends _DonationSupportButtonController&GetxController&AnalyticMixin {

  get _ title(/* No info */) {
    // ** addr: 0xb8f9c4, size: 0x44
    // 0xb8f9c4: EnterFrame
    //     0xb8f9c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f9c8: mov             fp, SP
    // 0xb8f9cc: CheckStackOverflow
    //     0xb8f9cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f9d0: cmp             SP, x16
    //     0xb8f9d4: b.ls            #0xb8fa00
    // 0xb8f9d8: LoadField: r0 = r1->field_23
    //     0xb8f9d8: ldur            w0, [x1, #0x23]
    // 0xb8f9dc: DecompressPointer r0
    //     0xb8f9dc: add             x0, x0, HEAP, lsl #32
    // 0xb8f9e0: mov             x1, x0
    // 0xb8f9e4: r0 = donationSupportButton()
    //     0xb8f9e4: bl              #0xb8fa08  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::donationSupportButton
    // 0xb8f9e8: LoadField: r1 = r0->field_7
    //     0xb8f9e8: ldur            w1, [x0, #7]
    // 0xb8f9ec: DecompressPointer r1
    //     0xb8f9ec: add             x1, x1, HEAP, lsl #32
    // 0xb8f9f0: mov             x0, x1
    // 0xb8f9f4: LeaveFrame
    //     0xb8f9f4: mov             SP, fp
    //     0xb8f9f8: ldp             fp, lr, [SP], #0x10
    // 0xb8f9fc: ret
    //     0xb8f9fc: ret             
    // 0xb8fa00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fa00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fa04: b               #0xb8f9d8
  }
  get _ show(/* No info */) {
    // ** addr: 0xb8fc60, size: 0x80
    // 0xb8fc60: EnterFrame
    //     0xb8fc60: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fc64: mov             fp, SP
    // 0xb8fc68: AllocStack(0x8)
    //     0xb8fc68: sub             SP, SP, #8
    // 0xb8fc6c: SetupParameters(DonationSupportButtonController this /* r1 => r0, fp-0x8 */)
    //     0xb8fc6c: mov             x0, x1
    //     0xb8fc70: stur            x1, [fp, #-8]
    // 0xb8fc74: CheckStackOverflow
    //     0xb8fc74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fc78: cmp             SP, x16
    //     0xb8fc7c: b.ls            #0xb8fcd8
    // 0xb8fc80: LoadField: r1 = r0->field_23
    //     0xb8fc80: ldur            w1, [x0, #0x23]
    // 0xb8fc84: DecompressPointer r1
    //     0xb8fc84: add             x1, x1, HEAP, lsl #32
    // 0xb8fc88: r0 = donationSupportButton()
    //     0xb8fc88: bl              #0xb8fa08  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::donationSupportButton
    // 0xb8fc8c: LoadField: r1 = r0->field_b
    //     0xb8fc8c: ldur            w1, [x0, #0xb]
    // 0xb8fc90: DecompressPointer r1
    //     0xb8fc90: add             x1, x1, HEAP, lsl #32
    // 0xb8fc94: ldur            x0, [fp, #-8]
    // 0xb8fc98: LoadField: r2 = r0->field_1f
    //     0xb8fc98: ldur            w2, [x0, #0x1f]
    // 0xb8fc9c: DecompressPointer r2
    //     0xb8fc9c: add             x2, x2, HEAP, lsl #32
    // 0xb8fca0: LoadField: r0 = r2->field_f
    //     0xb8fca0: ldur            w0, [x2, #0xf]
    // 0xb8fca4: DecompressPointer r0
    //     0xb8fca4: add             x0, x0, HEAP, lsl #32
    // 0xb8fca8: r2 = LoadClassIdInstr(r1)
    //     0xb8fca8: ldur            x2, [x1, #-1]
    //     0xb8fcac: ubfx            x2, x2, #0xc, #0x14
    // 0xb8fcb0: mov             x16, x0
    // 0xb8fcb4: mov             x0, x2
    // 0xb8fcb8: mov             x2, x16
    // 0xb8fcbc: r0 = GDT[cid_x0 + 0xf20c]()
    //     0xb8fcbc: movz            x17, #0xf20c
    //     0xb8fcc0: add             lr, x0, x17
    //     0xb8fcc4: ldr             lr, [x21, lr, lsl #3]
    //     0xb8fcc8: blr             lr
    // 0xb8fccc: LeaveFrame
    //     0xb8fccc: mov             SP, fp
    //     0xb8fcd0: ldp             fp, lr, [SP], #0x10
    // 0xb8fcd4: ret
    //     0xb8fcd4: ret             
    // 0xb8fcd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fcd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fcdc: b               #0xb8fc80
  }
  [closure] void onPressed(dynamic) {
    // ** addr: 0xb8fce0, size: 0x38
    // 0xb8fce0: EnterFrame
    //     0xb8fce0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fce4: mov             fp, SP
    // 0xb8fce8: ldr             x0, [fp, #0x10]
    // 0xb8fcec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8fcec: ldur            w1, [x0, #0x17]
    // 0xb8fcf0: DecompressPointer r1
    //     0xb8fcf0: add             x1, x1, HEAP, lsl #32
    // 0xb8fcf4: CheckStackOverflow
    //     0xb8fcf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fcf8: cmp             SP, x16
    //     0xb8fcfc: b.ls            #0xb8fd10
    // 0xb8fd00: r0 = onPressed()
    //     0xb8fd00: bl              #0xb8fd18  ; [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButtonController::onPressed
    // 0xb8fd04: LeaveFrame
    //     0xb8fd04: mov             SP, fp
    //     0xb8fd08: ldp             fp, lr, [SP], #0x10
    // 0xb8fd0c: ret
    //     0xb8fd0c: ret             
    // 0xb8fd10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fd10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fd14: b               #0xb8fd00
  }
  _ onPressed(/* No info */) {
    // ** addr: 0xb8fd18, size: 0xd0
    // 0xb8fd18: EnterFrame
    //     0xb8fd18: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fd1c: mov             fp, SP
    // 0xb8fd20: AllocStack(0x28)
    //     0xb8fd20: sub             SP, SP, #0x28
    // 0xb8fd24: SetupParameters(DonationSupportButtonController this /* r1 => r1, fp-0x8 */)
    //     0xb8fd24: stur            x1, [fp, #-8]
    // 0xb8fd28: CheckStackOverflow
    //     0xb8fd28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fd2c: cmp             SP, x16
    //     0xb8fd30: b.ls            #0xb8fde0
    // 0xb8fd34: r1 = 1
    //     0xb8fd34: movz            x1, #0x1
    // 0xb8fd38: r0 = AllocateContext()
    //     0xb8fd38: bl              #0xec126c  ; AllocateContextStub
    // 0xb8fd3c: ldur            x1, [fp, #-8]
    // 0xb8fd40: stur            x0, [fp, #-0x10]
    // 0xb8fd44: StoreField: r0->field_f = r1
    //     0xb8fd44: stur            w1, [x0, #0xf]
    // 0xb8fd48: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8fd48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8fd4c: ldr             x0, [x0, #0x2670]
    //     0xb8fd50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8fd54: cmp             w0, w16
    //     0xb8fd58: b.ne            #0xb8fd64
    //     0xb8fd5c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8fd60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8fd64: r1 = Null
    //     0xb8fd64: mov             x1, NULL
    // 0xb8fd68: r2 = 4
    //     0xb8fd68: movz            x2, #0x4
    // 0xb8fd6c: r0 = AllocateArray()
    //     0xb8fd6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8fd70: r16 = "target"
    //     0xb8fd70: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a9a8] "target"
    //     0xb8fd74: ldr             x16, [x16, #0x9a8]
    // 0xb8fd78: StoreField: r0->field_f = r16
    //     0xb8fd78: stur            w16, [x0, #0xf]
    // 0xb8fd7c: r16 = "contribution"
    //     0xb8fd7c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35608] "contribution"
    //     0xb8fd80: ldr             x16, [x16, #0x608]
    // 0xb8fd84: StoreField: r0->field_13 = r16
    //     0xb8fd84: stur            w16, [x0, #0x13]
    // 0xb8fd88: r16 = <String, String>
    //     0xb8fd88: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb8fd8c: ldr             x16, [x16, #0x668]
    // 0xb8fd90: stp             x0, x16, [SP]
    // 0xb8fd94: r0 = Map._fromLiteral()
    //     0xb8fd94: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8fd98: r16 = "/donation/pay-donation"
    //     0xb8fd98: add             x16, PP, #0x35, lsl #12  ; [pp+0x35560] "/donation/pay-donation"
    //     0xb8fd9c: ldr             x16, [x16, #0x560]
    // 0xb8fda0: stp             x16, NULL, [SP, #8]
    // 0xb8fda4: str             x0, [SP]
    // 0xb8fda8: r4 = const [0x1, 0x2, 0x2, 0x1, parameters, 0x1, null]
    //     0xb8fda8: add             x4, PP, #0x27, lsl #12  ; [pp+0x277d0] List(7) [0x1, 0x2, 0x2, 0x1, "parameters", 0x1, Null]
    //     0xb8fdac: ldr             x4, [x4, #0x7d0]
    // 0xb8fdb0: r0 = GetNavigation.toNamed()
    //     0xb8fdb0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8fdb4: ldur            x2, [fp, #-0x10]
    // 0xb8fdb8: r1 = Function '<anonymous closure>':.
    //     0xb8fdb8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36078] AnonymousClosure: (0xb8fde8), in [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButtonController::onPressed (0xb8fd18)
    //     0xb8fdbc: ldr             x1, [x1, #0x78]
    // 0xb8fdc0: r0 = AllocateClosure()
    //     0xb8fdc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8fdc4: ldur            x1, [fp, #-8]
    // 0xb8fdc8: mov             x2, x0
    // 0xb8fdcc: r0 = withAnalytic()
    //     0xb8fdcc: bl              #0x8abac8  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin::withAnalytic
    // 0xb8fdd0: r0 = Null
    //     0xb8fdd0: mov             x0, NULL
    // 0xb8fdd4: LeaveFrame
    //     0xb8fdd4: mov             SP, fp
    //     0xb8fdd8: ldp             fp, lr, [SP], #0x10
    // 0xb8fddc: ret
    //     0xb8fddc: ret             
    // 0xb8fde0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fde0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fde4: b               #0xb8fd34
  }
  [closure] void <anonymous closure>(dynamic, AnalyticService) {
    // ** addr: 0xb8fde8, size: 0x58
    // 0xb8fde8: EnterFrame
    //     0xb8fde8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fdec: mov             fp, SP
    // 0xb8fdf0: ldr             x0, [fp, #0x18]
    // 0xb8fdf4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8fdf4: ldur            w1, [x0, #0x17]
    // 0xb8fdf8: DecompressPointer r1
    //     0xb8fdf8: add             x1, x1, HEAP, lsl #32
    // 0xb8fdfc: CheckStackOverflow
    //     0xb8fdfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8fe00: cmp             SP, x16
    //     0xb8fe04: b.ls            #0xb8fe38
    // 0xb8fe08: LoadField: r0 = r1->field_f
    //     0xb8fe08: ldur            w0, [x1, #0xf]
    // 0xb8fe0c: DecompressPointer r0
    //     0xb8fe0c: add             x0, x0, HEAP, lsl #32
    // 0xb8fe10: LoadField: r1 = r0->field_1f
    //     0xb8fe10: ldur            w1, [x0, #0x1f]
    // 0xb8fe14: DecompressPointer r1
    //     0xb8fe14: add             x1, x1, HEAP, lsl #32
    // 0xb8fe18: LoadField: r2 = r1->field_f
    //     0xb8fe18: ldur            w2, [x1, #0xf]
    // 0xb8fe1c: DecompressPointer r2
    //     0xb8fe1c: add             x2, x2, HEAP, lsl #32
    // 0xb8fe20: ldr             x1, [fp, #0x10]
    // 0xb8fe24: r0 = sendDonationSupport()
    //     0xb8fe24: bl              #0xb8fe40  ; [package:nuonline/services/analytic_service.dart] AnalyticService::sendDonationSupport
    // 0xb8fe28: r0 = Null
    //     0xb8fe28: mov             x0, NULL
    // 0xb8fe2c: LeaveFrame
    //     0xb8fe2c: mov             SP, fp
    //     0xb8fe30: ldp             fp, lr, [SP], #0x10
    // 0xb8fe34: ret
    //     0xb8fe34: ret             
    // 0xb8fe38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8fe38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8fe3c: b               #0xb8fe08
  }
}

// class id: 5038, size: 0x10, field offset: 0xc
//   const constructor, 
class DonationSupportButton extends StatelessWidget {

  DonationSupportPlacement field_c;

  _ build(/* No info */) {
    // ** addr: 0xb8f54c, size: 0xc8
    // 0xb8f54c: EnterFrame
    //     0xb8f54c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f550: mov             fp, SP
    // 0xb8f554: AllocStack(0x20)
    //     0xb8f554: sub             SP, SP, #0x20
    // 0xb8f558: CheckStackOverflow
    //     0xb8f558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f55c: cmp             SP, x16
    //     0xb8f560: b.ls            #0xb8f60c
    // 0xb8f564: LoadField: r0 = r1->field_b
    //     0xb8f564: ldur            w0, [x1, #0xb]
    // 0xb8f568: DecompressPointer r0
    //     0xb8f568: add             x0, x0, HEAP, lsl #32
    // 0xb8f56c: stur            x0, [fp, #-0x10]
    // 0xb8f570: LoadField: r1 = r0->field_f
    //     0xb8f570: ldur            w1, [x0, #0xf]
    // 0xb8f574: DecompressPointer r1
    //     0xb8f574: add             x1, x1, HEAP, lsl #32
    // 0xb8f578: stur            x1, [fp, #-8]
    // 0xb8f57c: r0 = find()
    //     0xb8f57c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xb8f580: stur            x0, [fp, #-0x18]
    // 0xb8f584: r0 = DonationSupportButtonController()
    //     0xb8f584: bl              #0xb8f614  ; AllocateDonationSupportButtonControllerStub -> DonationSupportButtonController (size=0x28)
    // 0xb8f588: mov             x2, x0
    // 0xb8f58c: ldur            x0, [fp, #-0x10]
    // 0xb8f590: stur            x2, [fp, #-0x20]
    // 0xb8f594: StoreField: r2->field_1f = r0
    //     0xb8f594: stur            w0, [x2, #0x1f]
    // 0xb8f598: ldur            x0, [fp, #-0x18]
    // 0xb8f59c: StoreField: r2->field_23 = r0
    //     0xb8f59c: stur            w0, [x2, #0x23]
    // 0xb8f5a0: mov             x1, x2
    // 0xb8f5a4: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb8f5a4: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb8f5a8: r1 = <DonationSupportButtonController>
    //     0xb8f5a8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36048] TypeArguments: <DonationSupportButtonController>
    //     0xb8f5ac: ldr             x1, [x1, #0x48]
    // 0xb8f5b0: r0 = GetBuilder()
    //     0xb8f5b0: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb8f5b4: mov             x3, x0
    // 0xb8f5b8: ldur            x0, [fp, #-0x20]
    // 0xb8f5bc: stur            x3, [fp, #-0x10]
    // 0xb8f5c0: StoreField: r3->field_3b = r0
    //     0xb8f5c0: stur            w0, [x3, #0x3b]
    // 0xb8f5c4: r0 = true
    //     0xb8f5c4: add             x0, NULL, #0x20  ; true
    // 0xb8f5c8: StoreField: r3->field_13 = r0
    //     0xb8f5c8: stur            w0, [x3, #0x13]
    // 0xb8f5cc: r1 = Function '<anonymous closure>':.
    //     0xb8f5cc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36050] AnonymousClosure: (0xb8f620), in [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButton::build (0xb8f54c)
    //     0xb8f5d0: ldr             x1, [x1, #0x50]
    // 0xb8f5d4: r2 = Null
    //     0xb8f5d4: mov             x2, NULL
    // 0xb8f5d8: r0 = AllocateClosure()
    //     0xb8f5d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8f5dc: mov             x1, x0
    // 0xb8f5e0: ldur            x0, [fp, #-0x10]
    // 0xb8f5e4: StoreField: r0->field_f = r1
    //     0xb8f5e4: stur            w1, [x0, #0xf]
    // 0xb8f5e8: r1 = true
    //     0xb8f5e8: add             x1, NULL, #0x20  ; true
    // 0xb8f5ec: StoreField: r0->field_1f = r1
    //     0xb8f5ec: stur            w1, [x0, #0x1f]
    // 0xb8f5f0: r1 = false
    //     0xb8f5f0: add             x1, NULL, #0x30  ; false
    // 0xb8f5f4: StoreField: r0->field_23 = r1
    //     0xb8f5f4: stur            w1, [x0, #0x23]
    // 0xb8f5f8: ldur            x1, [fp, #-8]
    // 0xb8f5fc: StoreField: r0->field_1b = r1
    //     0xb8f5fc: stur            w1, [x0, #0x1b]
    // 0xb8f600: LeaveFrame
    //     0xb8f600: mov             SP, fp
    //     0xb8f604: ldp             fp, lr, [SP], #0x10
    // 0xb8f608: ret
    //     0xb8f608: ret             
    // 0xb8f60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f60c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f610: b               #0xb8f564
  }
  [closure] Widget <anonymous closure>(dynamic, DonationSupportButtonController) {
    // ** addr: 0xb8f620, size: 0x3a4
    // 0xb8f620: EnterFrame
    //     0xb8f620: stp             fp, lr, [SP, #-0x10]!
    //     0xb8f624: mov             fp, SP
    // 0xb8f628: AllocStack(0x40)
    //     0xb8f628: sub             SP, SP, #0x40
    // 0xb8f62c: CheckStackOverflow
    //     0xb8f62c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8f630: cmp             SP, x16
    //     0xb8f634: b.ls            #0xb8f9b4
    // 0xb8f638: ldr             x1, [fp, #0x10]
    // 0xb8f63c: r0 = show()
    //     0xb8f63c: bl              #0xb8fc60  ; [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButtonController::show
    // 0xb8f640: tbz             w0, #4, #0xb8f658
    // 0xb8f644: r0 = Instance_SizedBox
    //     0xb8f644: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb8f648: ldr             x0, [x0, #0xc40]
    // 0xb8f64c: LeaveFrame
    //     0xb8f64c: mov             SP, fp
    //     0xb8f650: ldp             fp, lr, [SP], #0x10
    // 0xb8f654: ret
    //     0xb8f654: ret             
    // 0xb8f658: r1 = _ConstMap len:3
    //     0xb8f658: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb8f65c: ldr             x1, [x1, #0xbe8]
    // 0xb8f660: r2 = 2
    //     0xb8f660: movz            x2, #0x2
    // 0xb8f664: r0 = []()
    //     0xb8f664: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8f668: r16 = <Color?>
    //     0xb8f668: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8f66c: ldr             x16, [x16, #0x98]
    // 0xb8f670: stp             x0, x16, [SP, #8]
    // 0xb8f674: r16 = Instance_Color
    //     0xb8f674: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb8f678: str             x16, [SP]
    // 0xb8f67c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8f67c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8f680: r0 = mode()
    //     0xb8f680: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8f684: r1 = _ConstMap len:6
    //     0xb8f684: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb8f688: ldr             x1, [x1, #0xc20]
    // 0xb8f68c: r2 = 2
    //     0xb8f68c: movz            x2, #0x2
    // 0xb8f690: stur            x0, [fp, #-8]
    // 0xb8f694: r0 = []()
    //     0xb8f694: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8f698: stur            x0, [fp, #-0x10]
    // 0xb8f69c: cmp             w0, NULL
    // 0xb8f6a0: b.eq            #0xb8f9bc
    // 0xb8f6a4: r1 = _ConstMap len:6
    //     0xb8f6a4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb8f6a8: ldr             x1, [x1, #0xc20]
    // 0xb8f6ac: r2 = 8
    //     0xb8f6ac: movz            x2, #0x8
    // 0xb8f6b0: r0 = []()
    //     0xb8f6b0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8f6b4: cmp             w0, NULL
    // 0xb8f6b8: b.eq            #0xb8f9c0
    // 0xb8f6bc: r16 = <Color>
    //     0xb8f6bc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8f6c0: ldr             x16, [x16, #0x158]
    // 0xb8f6c4: stp             x0, x16, [SP, #8]
    // 0xb8f6c8: ldur            x16, [fp, #-0x10]
    // 0xb8f6cc: str             x16, [SP]
    // 0xb8f6d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8f6d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8f6d4: r0 = mode()
    //     0xb8f6d4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8f6d8: str             x0, [SP]
    // 0xb8f6dc: r1 = Null
    //     0xb8f6dc: mov             x1, NULL
    // 0xb8f6e0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb8f6e0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb8f6e4: ldr             x4, [x4, #0x228]
    // 0xb8f6e8: r0 = Border.all()
    //     0xb8f6e8: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb8f6ec: stur            x0, [fp, #-0x10]
    // 0xb8f6f0: r0 = Radius()
    //     0xb8f6f0: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8f6f4: d0 = 8.000000
    //     0xb8f6f4: fmov            d0, #8.00000000
    // 0xb8f6f8: stur            x0, [fp, #-0x18]
    // 0xb8f6fc: StoreField: r0->field_7 = d0
    //     0xb8f6fc: stur            d0, [x0, #7]
    // 0xb8f700: StoreField: r0->field_f = d0
    //     0xb8f700: stur            d0, [x0, #0xf]
    // 0xb8f704: r0 = BorderRadius()
    //     0xb8f704: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8f708: mov             x1, x0
    // 0xb8f70c: ldur            x0, [fp, #-0x18]
    // 0xb8f710: stur            x1, [fp, #-0x20]
    // 0xb8f714: StoreField: r1->field_7 = r0
    //     0xb8f714: stur            w0, [x1, #7]
    // 0xb8f718: StoreField: r1->field_b = r0
    //     0xb8f718: stur            w0, [x1, #0xb]
    // 0xb8f71c: StoreField: r1->field_f = r0
    //     0xb8f71c: stur            w0, [x1, #0xf]
    // 0xb8f720: StoreField: r1->field_13 = r0
    //     0xb8f720: stur            w0, [x1, #0x13]
    // 0xb8f724: r0 = BoxDecoration()
    //     0xb8f724: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8f728: mov             x1, x0
    // 0xb8f72c: ldur            x0, [fp, #-8]
    // 0xb8f730: stur            x1, [fp, #-0x18]
    // 0xb8f734: StoreField: r1->field_7 = r0
    //     0xb8f734: stur            w0, [x1, #7]
    // 0xb8f738: ldur            x0, [fp, #-0x10]
    // 0xb8f73c: StoreField: r1->field_f = r0
    //     0xb8f73c: stur            w0, [x1, #0xf]
    // 0xb8f740: ldur            x0, [fp, #-0x20]
    // 0xb8f744: StoreField: r1->field_13 = r0
    //     0xb8f744: stur            w0, [x1, #0x13]
    // 0xb8f748: r0 = Instance_BoxShape
    //     0xb8f748: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8f74c: ldr             x0, [x0, #0xca8]
    // 0xb8f750: StoreField: r1->field_23 = r0
    //     0xb8f750: stur            w0, [x1, #0x23]
    // 0xb8f754: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8f754: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8f758: ldr             x0, [x0, #0x2670]
    //     0xb8f75c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8f760: cmp             w0, w16
    //     0xb8f764: b.ne            #0xb8f770
    //     0xb8f768: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8f76c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8f770: r0 = GetNavigation.theme()
    //     0xb8f770: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb8f774: LoadField: r1 = r0->field_3f
    //     0xb8f774: ldur            w1, [x0, #0x3f]
    // 0xb8f778: DecompressPointer r1
    //     0xb8f778: add             x1, x1, HEAP, lsl #32
    // 0xb8f77c: LoadField: r0 = r1->field_2b
    //     0xb8f77c: ldur            w0, [x1, #0x2b]
    // 0xb8f780: DecompressPointer r0
    //     0xb8f780: add             x0, x0, HEAP, lsl #32
    // 0xb8f784: stur            x0, [fp, #-8]
    // 0xb8f788: r0 = Icon()
    //     0xb8f788: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb8f78c: mov             x2, x0
    // 0xb8f790: r0 = Instance_IconData
    //     0xb8f790: add             x0, PP, #0x36, lsl #12  ; [pp+0x36058] Obj!IconData@e108f1
    //     0xb8f794: ldr             x0, [x0, #0x58]
    // 0xb8f798: stur            x2, [fp, #-0x10]
    // 0xb8f79c: StoreField: r2->field_b = r0
    //     0xb8f79c: stur            w0, [x2, #0xb]
    // 0xb8f7a0: r0 = 20.000000
    //     0xb8f7a0: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xb8f7a4: ldr             x0, [x0, #0x430]
    // 0xb8f7a8: StoreField: r2->field_f = r0
    //     0xb8f7a8: stur            w0, [x2, #0xf]
    // 0xb8f7ac: ldur            x0, [fp, #-8]
    // 0xb8f7b0: StoreField: r2->field_23 = r0
    //     0xb8f7b0: stur            w0, [x2, #0x23]
    // 0xb8f7b4: ldr             x1, [fp, #0x10]
    // 0xb8f7b8: r0 = title()
    //     0xb8f7b8: bl              #0xb8f9c4  ; [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButtonController::title
    // 0xb8f7bc: stur            x0, [fp, #-8]
    // 0xb8f7c0: r0 = GetNavigation.textTheme()
    //     0xb8f7c0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8f7c4: LoadField: r1 = r0->field_23
    //     0xb8f7c4: ldur            w1, [x0, #0x23]
    // 0xb8f7c8: DecompressPointer r1
    //     0xb8f7c8: add             x1, x1, HEAP, lsl #32
    // 0xb8f7cc: stur            x1, [fp, #-0x20]
    // 0xb8f7d0: cmp             w1, NULL
    // 0xb8f7d4: b.ne            #0xb8f7e0
    // 0xb8f7d8: r2 = Null
    //     0xb8f7d8: mov             x2, NULL
    // 0xb8f7dc: b               #0xb8f814
    // 0xb8f7e0: r0 = GetNavigation.theme()
    //     0xb8f7e0: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb8f7e4: LoadField: r1 = r0->field_3f
    //     0xb8f7e4: ldur            w1, [x0, #0x3f]
    // 0xb8f7e8: DecompressPointer r1
    //     0xb8f7e8: add             x1, x1, HEAP, lsl #32
    // 0xb8f7ec: LoadField: r0 = r1->field_2b
    //     0xb8f7ec: ldur            w0, [x1, #0x2b]
    // 0xb8f7f0: DecompressPointer r0
    //     0xb8f7f0: add             x0, x0, HEAP, lsl #32
    // 0xb8f7f4: r16 = Instance_FontWeight
    //     0xb8f7f4: add             x16, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xb8f7f8: ldr             x16, [x16, #0xcc0]
    // 0xb8f7fc: stp             x16, x0, [SP]
    // 0xb8f800: ldur            x1, [fp, #-0x20]
    // 0xb8f804: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontWeight, 0x2, null]
    //     0xb8f804: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f668] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontWeight", 0x2, Null]
    //     0xb8f808: ldr             x4, [x4, #0x668]
    // 0xb8f80c: r0 = copyWith()
    //     0xb8f80c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8f810: mov             x2, x0
    // 0xb8f814: ldur            x1, [fp, #-0x10]
    // 0xb8f818: ldur            x0, [fp, #-8]
    // 0xb8f81c: stur            x2, [fp, #-0x20]
    // 0xb8f820: r0 = Text()
    //     0xb8f820: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb8f824: mov             x2, x0
    // 0xb8f828: ldur            x0, [fp, #-8]
    // 0xb8f82c: stur            x2, [fp, #-0x28]
    // 0xb8f830: StoreField: r2->field_b = r0
    //     0xb8f830: stur            w0, [x2, #0xb]
    // 0xb8f834: ldur            x0, [fp, #-0x20]
    // 0xb8f838: StoreField: r2->field_13 = r0
    //     0xb8f838: stur            w0, [x2, #0x13]
    // 0xb8f83c: r0 = Instance__LinearTextScaler
    //     0xb8f83c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb8f840: StoreField: r2->field_33 = r0
    //     0xb8f840: stur            w0, [x2, #0x33]
    // 0xb8f844: r1 = <FlexParentData>
    //     0xb8f844: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb8f848: ldr             x1, [x1, #0x720]
    // 0xb8f84c: r0 = Flexible()
    //     0xb8f84c: bl              #0x9e6a68  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb8f850: mov             x3, x0
    // 0xb8f854: r0 = 1
    //     0xb8f854: movz            x0, #0x1
    // 0xb8f858: stur            x3, [fp, #-8]
    // 0xb8f85c: StoreField: r3->field_13 = r0
    //     0xb8f85c: stur            x0, [x3, #0x13]
    // 0xb8f860: r0 = Instance_FlexFit
    //     0xb8f860: add             x0, PP, #0x29, lsl #12  ; [pp+0x29d68] Obj!FlexFit@e35b61
    //     0xb8f864: ldr             x0, [x0, #0xd68]
    // 0xb8f868: StoreField: r3->field_1b = r0
    //     0xb8f868: stur            w0, [x3, #0x1b]
    // 0xb8f86c: ldur            x0, [fp, #-0x28]
    // 0xb8f870: StoreField: r3->field_b = r0
    //     0xb8f870: stur            w0, [x3, #0xb]
    // 0xb8f874: r1 = Null
    //     0xb8f874: mov             x1, NULL
    // 0xb8f878: r2 = 6
    //     0xb8f878: movz            x2, #0x6
    // 0xb8f87c: r0 = AllocateArray()
    //     0xb8f87c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8f880: mov             x2, x0
    // 0xb8f884: ldur            x0, [fp, #-0x10]
    // 0xb8f888: stur            x2, [fp, #-0x20]
    // 0xb8f88c: StoreField: r2->field_f = r0
    //     0xb8f88c: stur            w0, [x2, #0xf]
    // 0xb8f890: r16 = Instance_SizedBox
    //     0xb8f890: add             x16, PP, #0x36, lsl #12  ; [pp+0x36060] Obj!SizedBox@e1e581
    //     0xb8f894: ldr             x16, [x16, #0x60]
    // 0xb8f898: StoreField: r2->field_13 = r16
    //     0xb8f898: stur            w16, [x2, #0x13]
    // 0xb8f89c: ldur            x0, [fp, #-8]
    // 0xb8f8a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8f8a0: stur            w0, [x2, #0x17]
    // 0xb8f8a4: r1 = <Widget>
    //     0xb8f8a4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8f8a8: r0 = AllocateGrowableArray()
    //     0xb8f8a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8f8ac: mov             x1, x0
    // 0xb8f8b0: ldur            x0, [fp, #-0x20]
    // 0xb8f8b4: stur            x1, [fp, #-8]
    // 0xb8f8b8: StoreField: r1->field_f = r0
    //     0xb8f8b8: stur            w0, [x1, #0xf]
    // 0xb8f8bc: r0 = 6
    //     0xb8f8bc: movz            x0, #0x6
    // 0xb8f8c0: StoreField: r1->field_b = r0
    //     0xb8f8c0: stur            w0, [x1, #0xb]
    // 0xb8f8c4: r0 = Row()
    //     0xb8f8c4: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8f8c8: mov             x1, x0
    // 0xb8f8cc: r0 = Instance_Axis
    //     0xb8f8cc: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb8f8d0: stur            x1, [fp, #-0x10]
    // 0xb8f8d4: StoreField: r1->field_f = r0
    //     0xb8f8d4: stur            w0, [x1, #0xf]
    // 0xb8f8d8: r0 = Instance_MainAxisAlignment
    //     0xb8f8d8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb8f8dc: ldr             x0, [x0, #0x290]
    // 0xb8f8e0: StoreField: r1->field_13 = r0
    //     0xb8f8e0: stur            w0, [x1, #0x13]
    // 0xb8f8e4: r0 = Instance_MainAxisSize
    //     0xb8f8e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb8f8e8: ldr             x0, [x0, #0x738]
    // 0xb8f8ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8f8ec: stur            w0, [x1, #0x17]
    // 0xb8f8f0: r0 = Instance_CrossAxisAlignment
    //     0xb8f8f0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb8f8f4: ldr             x0, [x0, #0x68]
    // 0xb8f8f8: StoreField: r1->field_1b = r0
    //     0xb8f8f8: stur            w0, [x1, #0x1b]
    // 0xb8f8fc: r0 = Instance_VerticalDirection
    //     0xb8f8fc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb8f900: ldr             x0, [x0, #0x748]
    // 0xb8f904: StoreField: r1->field_23 = r0
    //     0xb8f904: stur            w0, [x1, #0x23]
    // 0xb8f908: r0 = Instance_Clip
    //     0xb8f908: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8f90c: ldr             x0, [x0, #0x750]
    // 0xb8f910: StoreField: r1->field_2b = r0
    //     0xb8f910: stur            w0, [x1, #0x2b]
    // 0xb8f914: StoreField: r1->field_2f = rZR
    //     0xb8f914: stur            xzr, [x1, #0x2f]
    // 0xb8f918: ldur            x0, [fp, #-8]
    // 0xb8f91c: StoreField: r1->field_b = r0
    //     0xb8f91c: stur            w0, [x1, #0xb]
    // 0xb8f920: r0 = Container()
    //     0xb8f920: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8f924: stur            x0, [fp, #-8]
    // 0xb8f928: ldur            x16, [fp, #-0x18]
    // 0xb8f92c: r30 = Instance_EdgeInsets
    //     0xb8f92c: add             lr, PP, #0x36, lsl #12  ; [pp+0x36068] Obj!EdgeInsets@e12401
    //     0xb8f930: ldr             lr, [lr, #0x68]
    // 0xb8f934: stp             lr, x16, [SP, #8]
    // 0xb8f938: ldur            x16, [fp, #-0x10]
    // 0xb8f93c: str             x16, [SP]
    // 0xb8f940: mov             x1, x0
    // 0xb8f944: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb8f944: add             x4, PP, #0x32, lsl #12  ; [pp+0x323e0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb8f948: ldr             x4, [x4, #0x3e0]
    // 0xb8f94c: r0 = Container()
    //     0xb8f94c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8f950: r0 = InkWell()
    //     0xb8f950: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8f954: mov             x3, x0
    // 0xb8f958: ldur            x0, [fp, #-8]
    // 0xb8f95c: stur            x3, [fp, #-0x10]
    // 0xb8f960: StoreField: r3->field_b = r0
    //     0xb8f960: stur            w0, [x3, #0xb]
    // 0xb8f964: ldr             x2, [fp, #0x10]
    // 0xb8f968: r1 = Function 'onPressed':.
    //     0xb8f968: add             x1, PP, #0x36, lsl #12  ; [pp+0x36070] AnonymousClosure: (0xb8fce0), in [package:nuonline/app/modules/donation/widgets/donation_support_button.dart] DonationSupportButtonController::onPressed (0xb8fd18)
    //     0xb8f96c: ldr             x1, [x1, #0x70]
    // 0xb8f970: r0 = AllocateClosure()
    //     0xb8f970: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8f974: mov             x1, x0
    // 0xb8f978: ldur            x0, [fp, #-0x10]
    // 0xb8f97c: StoreField: r0->field_f = r1
    //     0xb8f97c: stur            w1, [x0, #0xf]
    // 0xb8f980: r1 = true
    //     0xb8f980: add             x1, NULL, #0x20  ; true
    // 0xb8f984: StoreField: r0->field_43 = r1
    //     0xb8f984: stur            w1, [x0, #0x43]
    // 0xb8f988: r2 = Instance_BoxShape
    //     0xb8f988: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8f98c: ldr             x2, [x2, #0xca8]
    // 0xb8f990: StoreField: r0->field_47 = r2
    //     0xb8f990: stur            w2, [x0, #0x47]
    // 0xb8f994: StoreField: r0->field_6f = r1
    //     0xb8f994: stur            w1, [x0, #0x6f]
    // 0xb8f998: r2 = false
    //     0xb8f998: add             x2, NULL, #0x30  ; false
    // 0xb8f99c: StoreField: r0->field_73 = r2
    //     0xb8f99c: stur            w2, [x0, #0x73]
    // 0xb8f9a0: StoreField: r0->field_83 = r1
    //     0xb8f9a0: stur            w1, [x0, #0x83]
    // 0xb8f9a4: StoreField: r0->field_7b = r2
    //     0xb8f9a4: stur            w2, [x0, #0x7b]
    // 0xb8f9a8: LeaveFrame
    //     0xb8f9a8: mov             SP, fp
    //     0xb8f9ac: ldp             fp, lr, [SP], #0x10
    // 0xb8f9b0: ret
    //     0xb8f9b0: ret             
    // 0xb8f9b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8f9b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8f9b8: b               #0xb8f638
    // 0xb8f9bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8f9bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8f9c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8f9c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
