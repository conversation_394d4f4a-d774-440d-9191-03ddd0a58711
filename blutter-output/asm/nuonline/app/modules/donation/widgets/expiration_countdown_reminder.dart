// lib: , url: package:nuonline/app/modules/donation/widgets/expiration_countdown_reminder.dart

// class id: 1050234, size: 0x8
class :: {
}

// class id: 4381, size: 0x30, field offset: 0x30
//   const constructor, 
class TextSpanCountdown extends TextSpan {
}

// class id: 5037, size: 0x10, field offset: 0xc
//   const constructor, 
class ExpirationCountdownReminder extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8fef0, size: 0x280
    // 0xb8fef0: EnterFrame
    //     0xb8fef0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8fef4: mov             fp, SP
    // 0xb8fef8: AllocStack(0x40)
    //     0xb8fef8: sub             SP, SP, #0x40
    // 0xb8fefc: SetupParameters(ExpirationCountdownReminder this /* r1 => r0, fp-0x8 */)
    //     0xb8fefc: mov             x0, x1
    //     0xb8ff00: stur            x1, [fp, #-8]
    // 0xb8ff04: CheckStackOverflow
    //     0xb8ff04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8ff08: cmp             SP, x16
    //     0xb8ff0c: b.ls            #0xb90168
    // 0xb8ff10: r1 = _ConstMap len:10
    //     0xb8ff10: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xb8ff14: ldr             x1, [x1, #0x6c8]
    // 0xb8ff18: r2 = 100
    //     0xb8ff18: movz            x2, #0x64
    // 0xb8ff1c: r0 = []()
    //     0xb8ff1c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8ff20: r1 = _ConstMap len:10
    //     0xb8ff20: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb8ff24: ldr             x1, [x1, #0xc08]
    // 0xb8ff28: r2 = 1800
    //     0xb8ff28: movz            x2, #0x708
    // 0xb8ff2c: stur            x0, [fp, #-0x10]
    // 0xb8ff30: r0 = []()
    //     0xb8ff30: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8ff34: r16 = <Color?>
    //     0xb8ff34: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8ff38: ldr             x16, [x16, #0x98]
    // 0xb8ff3c: stp             x0, x16, [SP, #8]
    // 0xb8ff40: ldur            x16, [fp, #-0x10]
    // 0xb8ff44: str             x16, [SP]
    // 0xb8ff48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8ff48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8ff4c: r0 = mode()
    //     0xb8ff4c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8ff50: stur            x0, [fp, #-0x10]
    // 0xb8ff54: r0 = Radius()
    //     0xb8ff54: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8ff58: d0 = 8.000000
    //     0xb8ff58: fmov            d0, #8.00000000
    // 0xb8ff5c: stur            x0, [fp, #-0x18]
    // 0xb8ff60: StoreField: r0->field_7 = d0
    //     0xb8ff60: stur            d0, [x0, #7]
    // 0xb8ff64: StoreField: r0->field_f = d0
    //     0xb8ff64: stur            d0, [x0, #0xf]
    // 0xb8ff68: r0 = BorderRadius()
    //     0xb8ff68: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8ff6c: mov             x1, x0
    // 0xb8ff70: ldur            x0, [fp, #-0x18]
    // 0xb8ff74: stur            x1, [fp, #-0x20]
    // 0xb8ff78: StoreField: r1->field_7 = r0
    //     0xb8ff78: stur            w0, [x1, #7]
    // 0xb8ff7c: StoreField: r1->field_b = r0
    //     0xb8ff7c: stur            w0, [x1, #0xb]
    // 0xb8ff80: StoreField: r1->field_f = r0
    //     0xb8ff80: stur            w0, [x1, #0xf]
    // 0xb8ff84: StoreField: r1->field_13 = r0
    //     0xb8ff84: stur            w0, [x1, #0x13]
    // 0xb8ff88: r0 = BoxDecoration()
    //     0xb8ff88: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8ff8c: mov             x3, x0
    // 0xb8ff90: ldur            x0, [fp, #-0x10]
    // 0xb8ff94: stur            x3, [fp, #-0x18]
    // 0xb8ff98: StoreField: r3->field_7 = r0
    //     0xb8ff98: stur            w0, [x3, #7]
    // 0xb8ff9c: ldur            x0, [fp, #-0x20]
    // 0xb8ffa0: StoreField: r3->field_13 = r0
    //     0xb8ffa0: stur            w0, [x3, #0x13]
    // 0xb8ffa4: r0 = Instance_BoxShape
    //     0xb8ffa4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb8ffa8: ldr             x0, [x0, #0xca8]
    // 0xb8ffac: StoreField: r3->field_23 = r0
    //     0xb8ffac: stur            w0, [x3, #0x23]
    // 0xb8ffb0: r1 = _ConstMap len:10
    //     0xb8ffb0: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xb8ffb4: ldr             x1, [x1, #0x6c8]
    // 0xb8ffb8: r2 = 1400
    //     0xb8ffb8: movz            x2, #0x578
    // 0xb8ffbc: r0 = []()
    //     0xb8ffbc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8ffc0: r1 = _ConstMap len:10
    //     0xb8ffc0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb8ffc4: ldr             x1, [x1, #0xc08]
    // 0xb8ffc8: r2 = 100
    //     0xb8ffc8: movz            x2, #0x64
    // 0xb8ffcc: stur            x0, [fp, #-0x10]
    // 0xb8ffd0: r0 = []()
    //     0xb8ffd0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb8ffd4: r16 = <Color?>
    //     0xb8ffd4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8ffd8: ldr             x16, [x16, #0x98]
    // 0xb8ffdc: stp             x0, x16, [SP, #8]
    // 0xb8ffe0: ldur            x16, [fp, #-0x10]
    // 0xb8ffe4: str             x16, [SP]
    // 0xb8ffe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8ffe8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8ffec: r0 = mode()
    //     0xb8ffec: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb8fff0: stur            x0, [fp, #-0x10]
    // 0xb8fff4: r0 = Icon()
    //     0xb8fff4: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb8fff8: mov             x3, x0
    // 0xb8fffc: r0 = Instance_IconData
    //     0xb8fffc: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b98] Obj!IconData@e105f1
    //     0xb90000: ldr             x0, [x0, #0xb98]
    // 0xb90004: stur            x3, [fp, #-0x20]
    // 0xb90008: StoreField: r3->field_b = r0
    //     0xb90008: stur            w0, [x3, #0xb]
    // 0xb9000c: ldur            x0, [fp, #-0x10]
    // 0xb90010: StoreField: r3->field_23 = r0
    //     0xb90010: stur            w0, [x3, #0x23]
    // 0xb90014: ldur            x0, [fp, #-8]
    // 0xb90018: LoadField: r1 = r0->field_b
    //     0xb90018: ldur            w1, [x0, #0xb]
    // 0xb9001c: DecompressPointer r1
    //     0xb9001c: add             x1, x1, HEAP, lsl #32
    // 0xb90020: LoadField: r0 = r1->field_b
    //     0xb90020: ldur            w0, [x1, #0xb]
    // 0xb90024: DecompressPointer r0
    //     0xb90024: add             x0, x0, HEAP, lsl #32
    // 0xb90028: LoadField: r1 = r0->field_7
    //     0xb90028: ldur            x1, [x0, #7]
    // 0xb9002c: tbz             x1, #0x3f, #0xb90038
    // 0xb90030: r2 = 999
    //     0xb90030: movz            x2, #0x3e7
    // 0xb90034: b               #0xb9003c
    // 0xb90038: r2 = 0
    //     0xb90038: movz            x2, #0
    // 0xb9003c: r0 = 1000
    //     0xb9003c: movz            x0, #0x3e8
    // 0xb90040: sub             x4, x1, x2
    // 0xb90044: sdiv            x5, x4, x0
    // 0xb90048: stur            x5, [fp, #-0x28]
    // 0xb9004c: r1 = Function '<anonymous closure>':.
    //     0xb9004c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ba0] AnonymousClosure: (0xb9017c), in [package:nuonline/app/modules/donation/widgets/expiration_countdown_reminder.dart] ExpirationCountdownReminder::build (0xb8fef0)
    //     0xb90050: ldr             x1, [x1, #0xba0]
    // 0xb90054: r2 = Null
    //     0xb90054: mov             x2, NULL
    // 0xb90058: r0 = AllocateClosure()
    //     0xb90058: bl              #0xec1630  ; AllocateClosureStub
    // 0xb9005c: stur            x0, [fp, #-8]
    // 0xb90060: r0 = NCountdownTimer()
    //     0xb90060: bl              #0xb90170  ; AllocateNCountdownTimerStub -> NCountdownTimer (size=0x28)
    // 0xb90064: mov             x3, x0
    // 0xb90068: ldur            x0, [fp, #-8]
    // 0xb9006c: stur            x3, [fp, #-0x10]
    // 0xb90070: StoreField: r3->field_f = r0
    //     0xb90070: stur            w0, [x3, #0xf]
    // 0xb90074: ldur            x0, [fp, #-0x28]
    // 0xb90078: StoreField: r3->field_1f = r0
    //     0xb90078: stur            x0, [x3, #0x1f]
    // 0xb9007c: r1 = Null
    //     0xb9007c: mov             x1, NULL
    // 0xb90080: r2 = 6
    //     0xb90080: movz            x2, #0x6
    // 0xb90084: r0 = AllocateArray()
    //     0xb90084: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90088: mov             x2, x0
    // 0xb9008c: ldur            x0, [fp, #-0x20]
    // 0xb90090: stur            x2, [fp, #-8]
    // 0xb90094: StoreField: r2->field_f = r0
    //     0xb90094: stur            w0, [x2, #0xf]
    // 0xb90098: r16 = Instance_SizedBox
    //     0xb90098: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xb9009c: ldr             x16, [x16, #0x340]
    // 0xb900a0: StoreField: r2->field_13 = r16
    //     0xb900a0: stur            w16, [x2, #0x13]
    // 0xb900a4: ldur            x0, [fp, #-0x10]
    // 0xb900a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb900a8: stur            w0, [x2, #0x17]
    // 0xb900ac: r1 = <Widget>
    //     0xb900ac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb900b0: r0 = AllocateGrowableArray()
    //     0xb900b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb900b4: mov             x1, x0
    // 0xb900b8: ldur            x0, [fp, #-8]
    // 0xb900bc: stur            x1, [fp, #-0x10]
    // 0xb900c0: StoreField: r1->field_f = r0
    //     0xb900c0: stur            w0, [x1, #0xf]
    // 0xb900c4: r0 = 6
    //     0xb900c4: movz            x0, #0x6
    // 0xb900c8: StoreField: r1->field_b = r0
    //     0xb900c8: stur            w0, [x1, #0xb]
    // 0xb900cc: r0 = Row()
    //     0xb900cc: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb900d0: mov             x1, x0
    // 0xb900d4: r0 = Instance_Axis
    //     0xb900d4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb900d8: stur            x1, [fp, #-8]
    // 0xb900dc: StoreField: r1->field_f = r0
    //     0xb900dc: stur            w0, [x1, #0xf]
    // 0xb900e0: r0 = Instance_MainAxisAlignment
    //     0xb900e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb900e4: ldr             x0, [x0, #0x730]
    // 0xb900e8: StoreField: r1->field_13 = r0
    //     0xb900e8: stur            w0, [x1, #0x13]
    // 0xb900ec: r0 = Instance_MainAxisSize
    //     0xb900ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb900f0: ldr             x0, [x0, #0x738]
    // 0xb900f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb900f4: stur            w0, [x1, #0x17]
    // 0xb900f8: r0 = Instance_CrossAxisAlignment
    //     0xb900f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb900fc: ldr             x0, [x0, #0x740]
    // 0xb90100: StoreField: r1->field_1b = r0
    //     0xb90100: stur            w0, [x1, #0x1b]
    // 0xb90104: r0 = Instance_VerticalDirection
    //     0xb90104: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90108: ldr             x0, [x0, #0x748]
    // 0xb9010c: StoreField: r1->field_23 = r0
    //     0xb9010c: stur            w0, [x1, #0x23]
    // 0xb90110: r0 = Instance_Clip
    //     0xb90110: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90114: ldr             x0, [x0, #0x750]
    // 0xb90118: StoreField: r1->field_2b = r0
    //     0xb90118: stur            w0, [x1, #0x2b]
    // 0xb9011c: StoreField: r1->field_2f = rZR
    //     0xb9011c: stur            xzr, [x1, #0x2f]
    // 0xb90120: ldur            x0, [fp, #-0x10]
    // 0xb90124: StoreField: r1->field_b = r0
    //     0xb90124: stur            w0, [x1, #0xb]
    // 0xb90128: r0 = Container()
    //     0xb90128: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb9012c: stur            x0, [fp, #-0x10]
    // 0xb90130: r16 = Instance_EdgeInsets
    //     0xb90130: add             x16, PP, #0x32, lsl #12  ; [pp+0x323d8] Obj!EdgeInsets@e13241
    //     0xb90134: ldr             x16, [x16, #0x3d8]
    // 0xb90138: ldur            lr, [fp, #-0x18]
    // 0xb9013c: stp             lr, x16, [SP, #8]
    // 0xb90140: ldur            x16, [fp, #-8]
    // 0xb90144: str             x16, [SP]
    // 0xb90148: mov             x1, x0
    // 0xb9014c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb9014c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb90150: ldr             x4, [x4, #0xe8]
    // 0xb90154: r0 = Container()
    //     0xb90154: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb90158: ldur            x0, [fp, #-0x10]
    // 0xb9015c: LeaveFrame
    //     0xb9015c: mov             SP, fp
    //     0xb90160: ldp             fp, lr, [SP], #0x10
    // 0xb90164: ret
    //     0xb90164: ret             
    // 0xb90168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9016c: b               #0xb8ff10
  }
  [closure] Text <anonymous closure>(dynamic, BuildContext, CurrentRemainingTime?) {
    // ** addr: 0xb9017c, size: 0x16c
    // 0xb9017c: EnterFrame
    //     0xb9017c: stp             fp, lr, [SP, #-0x10]!
    //     0xb90180: mov             fp, SP
    // 0xb90184: AllocStack(0x30)
    //     0xb90184: sub             SP, SP, #0x30
    // 0xb90188: CheckStackOverflow
    //     0xb90188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9018c: cmp             SP, x16
    //     0xb90190: b.ls            #0xb902dc
    // 0xb90194: ldr             x1, [fp, #0x10]
    // 0xb90198: r0 = remainingTextBuilder()
    //     0xb90198: bl              #0xb902f4  ; [package:nuikit/src/widgets/builtin/countdown_timer/countdown_timer.dart] NCountdownTimer::remainingTextBuilder
    // 0xb9019c: stur            x0, [fp, #-8]
    // 0xb901a0: r0 = TextSpanCountdown()
    //     0xb901a0: bl              #0xb902e8  ; AllocateTextSpanCountdownStub -> TextSpanCountdown (size=0x30)
    // 0xb901a4: mov             x3, x0
    // 0xb901a8: ldur            x0, [fp, #-8]
    // 0xb901ac: stur            x3, [fp, #-0x10]
    // 0xb901b0: StoreField: r3->field_b = r0
    //     0xb901b0: stur            w0, [x3, #0xb]
    // 0xb901b4: r0 = Instance__DeferringMouseCursor
    //     0xb901b4: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb901b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb901b8: stur            w0, [x3, #0x17]
    // 0xb901bc: r1 = Instance_TextStyle
    //     0xb901bc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34be0] Obj!TextStyle@e1ae71
    //     0xb901c0: ldr             x1, [x1, #0xbe0]
    // 0xb901c4: StoreField: r3->field_7 = r1
    //     0xb901c4: stur            w1, [x3, #7]
    // 0xb901c8: r1 = Null
    //     0xb901c8: mov             x1, NULL
    // 0xb901cc: r2 = 2
    //     0xb901cc: movz            x2, #0x2
    // 0xb901d0: r0 = AllocateArray()
    //     0xb901d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb901d4: mov             x2, x0
    // 0xb901d8: ldur            x0, [fp, #-0x10]
    // 0xb901dc: stur            x2, [fp, #-8]
    // 0xb901e0: StoreField: r2->field_f = r0
    //     0xb901e0: stur            w0, [x2, #0xf]
    // 0xb901e4: r1 = <InlineSpan>
    //     0xb901e4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xb901e8: ldr             x1, [x1, #0x5f0]
    // 0xb901ec: r0 = AllocateGrowableArray()
    //     0xb901ec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb901f0: mov             x1, x0
    // 0xb901f4: ldur            x0, [fp, #-8]
    // 0xb901f8: stur            x1, [fp, #-0x10]
    // 0xb901fc: StoreField: r1->field_f = r0
    //     0xb901fc: stur            w0, [x1, #0xf]
    // 0xb90200: r0 = 2
    //     0xb90200: movz            x0, #0x2
    // 0xb90204: StoreField: r1->field_b = r0
    //     0xb90204: stur            w0, [x1, #0xb]
    // 0xb90208: r0 = TextSpan()
    //     0xb90208: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb9020c: mov             x1, x0
    // 0xb90210: r0 = "Sisa waktu pembayaran: "
    //     0xb90210: add             x0, PP, #0x47, lsl #12  ; [pp+0x47ba8] "Sisa waktu pembayaran: "
    //     0xb90214: ldr             x0, [x0, #0xba8]
    // 0xb90218: stur            x1, [fp, #-8]
    // 0xb9021c: StoreField: r1->field_b = r0
    //     0xb9021c: stur            w0, [x1, #0xb]
    // 0xb90220: ldur            x0, [fp, #-0x10]
    // 0xb90224: StoreField: r1->field_f = r0
    //     0xb90224: stur            w0, [x1, #0xf]
    // 0xb90228: r0 = Instance__DeferringMouseCursor
    //     0xb90228: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb9022c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9022c: stur            w0, [x1, #0x17]
    // 0xb90230: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb90230: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb90234: ldr             x0, [x0, #0x2670]
    //     0xb90238: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9023c: cmp             w0, w16
    //     0xb90240: b.ne            #0xb9024c
    //     0xb90244: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb90248: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb9024c: r0 = GetNavigation.textTheme()
    //     0xb9024c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb90250: LoadField: r3 = r0->field_23
    //     0xb90250: ldur            w3, [x0, #0x23]
    // 0xb90254: DecompressPointer r3
    //     0xb90254: add             x3, x3, HEAP, lsl #32
    // 0xb90258: stur            x3, [fp, #-0x10]
    // 0xb9025c: cmp             w3, NULL
    // 0xb90260: b.eq            #0xb902e4
    // 0xb90264: r1 = _ConstMap len:10
    //     0xb90264: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xb90268: ldr             x1, [x1, #0x6c8]
    // 0xb9026c: r2 = 1400
    //     0xb9026c: movz            x2, #0x578
    // 0xb90270: r0 = []()
    //     0xb90270: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb90274: r1 = _ConstMap len:10
    //     0xb90274: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb90278: ldr             x1, [x1, #0xc08]
    // 0xb9027c: r2 = 100
    //     0xb9027c: movz            x2, #0x64
    // 0xb90280: stur            x0, [fp, #-0x18]
    // 0xb90284: r0 = []()
    //     0xb90284: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb90288: r16 = <Color?>
    //     0xb90288: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb9028c: ldr             x16, [x16, #0x98]
    // 0xb90290: stp             x0, x16, [SP, #8]
    // 0xb90294: ldur            x16, [fp, #-0x18]
    // 0xb90298: str             x16, [SP]
    // 0xb9029c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9029c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb902a0: r0 = mode()
    //     0xb902a0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb902a4: str             x0, [SP]
    // 0xb902a8: ldur            x1, [fp, #-0x10]
    // 0xb902ac: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb902ac: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb902b0: ldr             x4, [x4, #0x228]
    // 0xb902b4: r0 = copyWith()
    //     0xb902b4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb902b8: stur            x0, [fp, #-0x10]
    // 0xb902bc: r0 = Text()
    //     0xb902bc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb902c0: ldur            x1, [fp, #-8]
    // 0xb902c4: StoreField: r0->field_f = r1
    //     0xb902c4: stur            w1, [x0, #0xf]
    // 0xb902c8: ldur            x1, [fp, #-0x10]
    // 0xb902cc: StoreField: r0->field_13 = r1
    //     0xb902cc: stur            w1, [x0, #0x13]
    // 0xb902d0: LeaveFrame
    //     0xb902d0: mov             SP, fp
    //     0xb902d4: ldp             fp, lr, [SP], #0x10
    // 0xb902d8: ret
    //     0xb902d8: ret             
    // 0xb902dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb902dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb902e0: b               #0xb90194
    // 0xb902e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb902e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
