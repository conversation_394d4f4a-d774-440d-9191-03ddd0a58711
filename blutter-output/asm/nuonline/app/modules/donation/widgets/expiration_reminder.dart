// lib: , url: package:nuonline/app/modules/donation/widgets/expiration_reminder.dart

// class id: 1050235, size: 0x8
class :: {
}

// class id: 5036, size: 0x10, field offset: 0xc
//   const constructor, 
class ExpirationReminder extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb904f8, size: 0x11c
    // 0xb904f8: EnterFrame
    //     0xb904f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb904fc: mov             fp, SP
    // 0xb90500: AllocStack(0x10)
    //     0xb90500: sub             SP, SP, #0x10
    // 0xb90504: CheckStackOverflow
    //     0xb90504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90508: cmp             SP, x16
    //     0xb9050c: b.ls            #0xb9060c
    // 0xb90510: LoadField: r0 = r1->field_b
    //     0xb90510: ldur            w0, [x1, #0xb]
    // 0xb90514: DecompressPointer r0
    //     0xb90514: add             x0, x0, HEAP, lsl #32
    // 0xb90518: mov             x1, x0
    // 0xb9051c: r0 = DateTimeExtensions.humanizeDateTimeWIB()
    //     0xb9051c: bl              #0xaf6544  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanizeDateTimeWIB
    // 0xb90520: stur            x0, [fp, #-8]
    // 0xb90524: r0 = TextSpan()
    //     0xb90524: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb90528: mov             x3, x0
    // 0xb9052c: ldur            x0, [fp, #-8]
    // 0xb90530: stur            x3, [fp, #-0x10]
    // 0xb90534: StoreField: r3->field_b = r0
    //     0xb90534: stur            w0, [x3, #0xb]
    // 0xb90538: r0 = Instance__DeferringMouseCursor
    //     0xb90538: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb9053c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb9053c: stur            w0, [x3, #0x17]
    // 0xb90540: r1 = Instance_TextStyle
    //     0xb90540: add             x1, PP, #0x34, lsl #12  ; [pp+0x34be0] Obj!TextStyle@e1ae71
    //     0xb90544: ldr             x1, [x1, #0xbe0]
    // 0xb90548: StoreField: r3->field_7 = r1
    //     0xb90548: stur            w1, [x3, #7]
    // 0xb9054c: r1 = Null
    //     0xb9054c: mov             x1, NULL
    // 0xb90550: r2 = 4
    //     0xb90550: movz            x2, #0x4
    // 0xb90554: r0 = AllocateArray()
    //     0xb90554: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90558: mov             x2, x0
    // 0xb9055c: ldur            x0, [fp, #-0x10]
    // 0xb90560: stur            x2, [fp, #-8]
    // 0xb90564: StoreField: r2->field_f = r0
    //     0xb90564: stur            w0, [x2, #0xf]
    // 0xb90568: r16 = Instance_TextSpan
    //     0xb90568: add             x16, PP, #0x47, lsl #12  ; [pp+0x47b88] Obj!TextSpan@e1dc51
    //     0xb9056c: ldr             x16, [x16, #0xb88]
    // 0xb90570: StoreField: r2->field_13 = r16
    //     0xb90570: stur            w16, [x2, #0x13]
    // 0xb90574: r1 = <InlineSpan>
    //     0xb90574: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xb90578: ldr             x1, [x1, #0x5f0]
    // 0xb9057c: r0 = AllocateGrowableArray()
    //     0xb9057c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90580: mov             x1, x0
    // 0xb90584: ldur            x0, [fp, #-8]
    // 0xb90588: stur            x1, [fp, #-0x10]
    // 0xb9058c: StoreField: r1->field_f = r0
    //     0xb9058c: stur            w0, [x1, #0xf]
    // 0xb90590: r0 = 4
    //     0xb90590: movz            x0, #0x4
    // 0xb90594: StoreField: r1->field_b = r0
    //     0xb90594: stur            w0, [x1, #0xb]
    // 0xb90598: r0 = TextSpan()
    //     0xb90598: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb9059c: mov             x1, x0
    // 0xb905a0: r0 = "Transfer sebelum "
    //     0xb905a0: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b90] "Transfer sebelum "
    //     0xb905a4: ldr             x0, [x0, #0xb90]
    // 0xb905a8: stur            x1, [fp, #-8]
    // 0xb905ac: StoreField: r1->field_b = r0
    //     0xb905ac: stur            w0, [x1, #0xb]
    // 0xb905b0: ldur            x0, [fp, #-0x10]
    // 0xb905b4: StoreField: r1->field_f = r0
    //     0xb905b4: stur            w0, [x1, #0xf]
    // 0xb905b8: r0 = Instance__DeferringMouseCursor
    //     0xb905b8: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb905bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb905bc: stur            w0, [x1, #0x17]
    // 0xb905c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb905c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb905c4: ldr             x0, [x0, #0x2670]
    //     0xb905c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb905cc: cmp             w0, w16
    //     0xb905d0: b.ne            #0xb905dc
    //     0xb905d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb905d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb905dc: r0 = GetNavigation.textTheme()
    //     0xb905dc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb905e0: LoadField: r1 = r0->field_2f
    //     0xb905e0: ldur            w1, [x0, #0x2f]
    // 0xb905e4: DecompressPointer r1
    //     0xb905e4: add             x1, x1, HEAP, lsl #32
    // 0xb905e8: stur            x1, [fp, #-0x10]
    // 0xb905ec: r0 = Text()
    //     0xb905ec: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb905f0: ldur            x1, [fp, #-8]
    // 0xb905f4: StoreField: r0->field_f = r1
    //     0xb905f4: stur            w1, [x0, #0xf]
    // 0xb905f8: ldur            x1, [fp, #-0x10]
    // 0xb905fc: StoreField: r0->field_13 = r1
    //     0xb905fc: stur            w1, [x0, #0x13]
    // 0xb90600: LeaveFrame
    //     0xb90600: mov             SP, fp
    //     0xb90604: ldp             fp, lr, [SP], #0x10
    // 0xb90608: ret
    //     0xb90608: ret             
    // 0xb9060c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9060c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90610: b               #0xb90510
  }
}
