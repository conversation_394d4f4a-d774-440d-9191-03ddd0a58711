// lib: , url: package:nuonline/app/modules/donation/widgets/fidyah_form.dart

// class id: 1050236, size: 0x8
class :: {
}

// class id: 5035, size: 0x2c, field offset: 0xc
//   const constructor, 
class _FidyahItemWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb90614, size: 0xa3c
    // 0xb90614: EnterFrame
    //     0xb90614: stp             fp, lr, [SP, #-0x10]!
    //     0xb90618: mov             fp, SP
    // 0xb9061c: AllocStack(0x70)
    //     0xb9061c: sub             SP, SP, #0x70
    // 0xb90620: SetupParameters(_FidyahItemWidget this /* r1 => r1, fp-0x8 */)
    //     0xb90620: stur            x1, [fp, #-8]
    // 0xb90624: CheckStackOverflow
    //     0xb90624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90628: cmp             SP, x16
    //     0xb9062c: b.ls            #0xb91048
    // 0xb90630: r1 = 1
    //     0xb90630: movz            x1, #0x1
    // 0xb90634: r0 = AllocateContext()
    //     0xb90634: bl              #0xec126c  ; AllocateContextStub
    // 0xb90638: mov             x3, x0
    // 0xb9063c: ldur            x0, [fp, #-8]
    // 0xb90640: stur            x3, [fp, #-0x20]
    // 0xb90644: StoreField: r3->field_f = r0
    //     0xb90644: stur            w0, [x3, #0xf]
    // 0xb90648: LoadField: r4 = r0->field_b
    //     0xb90648: ldur            w4, [x0, #0xb]
    // 0xb9064c: DecompressPointer r4
    //     0xb9064c: add             x4, x4, HEAP, lsl #32
    // 0xb90650: stur            x4, [fp, #-0x18]
    // 0xb90654: LoadField: r5 = r0->field_1f
    //     0xb90654: ldur            w5, [x0, #0x1f]
    // 0xb90658: DecompressPointer r5
    //     0xb90658: add             x5, x5, HEAP, lsl #32
    // 0xb9065c: stur            x5, [fp, #-0x10]
    // 0xb90660: r1 = Function '<anonymous closure>': static.
    //     0xb90660: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xb90664: ldr             x1, [x1, #0xda0]
    // 0xb90668: r2 = Null
    //     0xb90668: mov             x2, NULL
    // 0xb9066c: r0 = AllocateClosure()
    //     0xb9066c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb90670: r1 = <String>
    //     0xb90670: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90674: stur            x0, [fp, #-0x28]
    // 0xb90678: StoreField: r0->field_b = r1
    //     0xb90678: stur            w1, [x0, #0xb]
    // 0xb9067c: r16 = <String>
    //     0xb9067c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90680: str             x16, [SP, #8]
    // 0xb90684: r2 = 55
    //     0xb90684: movz            x2, #0x37
    // 0xb90688: str             x2, [SP]
    // 0xb9068c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb9068c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb90690: r0 = maxLength()
    //     0xb90690: bl              #0xae4b84  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::maxLength
    // 0xb90694: r1 = Null
    //     0xb90694: mov             x1, NULL
    // 0xb90698: r2 = 4
    //     0xb90698: movz            x2, #0x4
    // 0xb9069c: stur            x0, [fp, #-0x30]
    // 0xb906a0: r0 = AllocateArray()
    //     0xb906a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb906a4: mov             x2, x0
    // 0xb906a8: ldur            x0, [fp, #-0x28]
    // 0xb906ac: stur            x2, [fp, #-0x38]
    // 0xb906b0: StoreField: r2->field_f = r0
    //     0xb906b0: stur            w0, [x2, #0xf]
    // 0xb906b4: ldur            x0, [fp, #-0x30]
    // 0xb906b8: StoreField: r2->field_13 = r0
    //     0xb906b8: stur            w0, [x2, #0x13]
    // 0xb906bc: r1 = <(dynamic this, String?) => String?>
    //     0xb906bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xb906c0: ldr             x1, [x1, #0xd98]
    // 0xb906c4: r0 = AllocateGrowableArray()
    //     0xb906c4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb906c8: mov             x1, x0
    // 0xb906cc: ldur            x0, [fp, #-0x38]
    // 0xb906d0: stur            x1, [fp, #-0x28]
    // 0xb906d4: StoreField: r1->field_f = r0
    //     0xb906d4: stur            w0, [x1, #0xf]
    // 0xb906d8: r2 = 4
    //     0xb906d8: movz            x2, #0x4
    // 0xb906dc: StoreField: r1->field_b = r2
    //     0xb906dc: stur            w2, [x1, #0xb]
    // 0xb906e0: r1 = 1
    //     0xb906e0: movz            x1, #0x1
    // 0xb906e4: r0 = AllocateContext()
    //     0xb906e4: bl              #0xec126c  ; AllocateContextStub
    // 0xb906e8: mov             x1, x0
    // 0xb906ec: ldur            x0, [fp, #-0x28]
    // 0xb906f0: StoreField: r1->field_f = r0
    //     0xb906f0: stur            w0, [x1, #0xf]
    // 0xb906f4: mov             x2, x1
    // 0xb906f8: r1 = Function '<anonymous closure>': static.
    //     0xb906f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xb906fc: ldr             x1, [x1, #0xda8]
    // 0xb90700: r0 = AllocateClosure()
    //     0xb90700: bl              #0xec1630  ; AllocateClosureStub
    // 0xb90704: r1 = <String>
    //     0xb90704: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90708: stur            x0, [fp, #-0x28]
    // 0xb9070c: StoreField: r0->field_b = r1
    //     0xb9070c: stur            w1, [x0, #0xb]
    // 0xb90710: r16 = "[a-zA-Z ]"
    //     0xb90710: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xb90714: ldr             x16, [x16, #0xb20]
    // 0xb90718: stp             x16, NULL, [SP, #0x20]
    // 0xb9071c: r16 = false
    //     0xb9071c: add             x16, NULL, #0x30  ; false
    // 0xb90720: r30 = true
    //     0xb90720: add             lr, NULL, #0x20  ; true
    // 0xb90724: stp             lr, x16, [SP, #0x10]
    // 0xb90728: r16 = false
    //     0xb90728: add             x16, NULL, #0x30  ; false
    // 0xb9072c: r30 = false
    //     0xb9072c: add             lr, NULL, #0x30  ; false
    // 0xb90730: stp             lr, x16, [SP]
    // 0xb90734: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb90734: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb90738: r0 = _RegExp()
    //     0xb90738: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xb9073c: stur            x0, [fp, #-0x30]
    // 0xb90740: r0 = FilteringTextInputFormatter()
    //     0xb90740: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb90744: mov             x3, x0
    // 0xb90748: ldur            x0, [fp, #-0x30]
    // 0xb9074c: stur            x3, [fp, #-0x38]
    // 0xb90750: StoreField: r3->field_7 = r0
    //     0xb90750: stur            w0, [x3, #7]
    // 0xb90754: r0 = true
    //     0xb90754: add             x0, NULL, #0x20  ; true
    // 0xb90758: StoreField: r3->field_b = r0
    //     0xb90758: stur            w0, [x3, #0xb]
    // 0xb9075c: r0 = ""
    //     0xb9075c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb90760: StoreField: r3->field_f = r0
    //     0xb90760: stur            w0, [x3, #0xf]
    // 0xb90764: r1 = Null
    //     0xb90764: mov             x1, NULL
    // 0xb90768: r2 = 2
    //     0xb90768: movz            x2, #0x2
    // 0xb9076c: r0 = AllocateArray()
    //     0xb9076c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90770: mov             x2, x0
    // 0xb90774: ldur            x0, [fp, #-0x38]
    // 0xb90778: stur            x2, [fp, #-0x30]
    // 0xb9077c: StoreField: r2->field_f = r0
    //     0xb9077c: stur            w0, [x2, #0xf]
    // 0xb90780: r1 = <TextInputFormatter>
    //     0xb90780: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb90784: ldr             x1, [x1, #0x788]
    // 0xb90788: r0 = AllocateGrowableArray()
    //     0xb90788: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb9078c: mov             x2, x0
    // 0xb90790: ldur            x0, [fp, #-0x30]
    // 0xb90794: stur            x2, [fp, #-0x38]
    // 0xb90798: StoreField: r2->field_f = r0
    //     0xb90798: stur            w0, [x2, #0xf]
    // 0xb9079c: r0 = 2
    //     0xb9079c: movz            x0, #0x2
    // 0xb907a0: StoreField: r2->field_b = r0
    //     0xb907a0: stur            w0, [x2, #0xb]
    // 0xb907a4: r1 = <String>
    //     0xb907a4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb907a8: r0 = TextFormField()
    //     0xb907a8: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xb907ac: stur            x0, [fp, #-0x30]
    // 0xb907b0: ldur            x16, [fp, #-0x18]
    // 0xb907b4: ldur            lr, [fp, #-0x10]
    // 0xb907b8: stp             lr, x16, [SP, #0x18]
    // 0xb907bc: r16 = false
    //     0xb907bc: add             x16, NULL, #0x30  ; false
    // 0xb907c0: ldur            lr, [fp, #-0x28]
    // 0xb907c4: stp             lr, x16, [SP, #8]
    // 0xb907c8: ldur            x16, [fp, #-0x38]
    // 0xb907cc: str             x16, [SP]
    // 0xb907d0: mov             x1, x0
    // 0xb907d4: r2 = Instance_InputDecoration
    //     0xb907d4: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xb907d8: ldr             x2, [x2, #0xb28]
    // 0xb907dc: r4 = const [0, 0x7, 0x5, 0x2, autocorrect, 0x4, initialValue, 0x2, inputFormatters, 0x6, onChanged, 0x3, validator, 0x5, null]
    //     0xb907dc: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3ffa0] List(15) [0, 0x7, 0x5, 0x2, "autocorrect", 0x4, "initialValue", 0x2, "inputFormatters", 0x6, "onChanged", 0x3, "validator", 0x5, Null]
    //     0xb907e0: ldr             x4, [x4, #0xfa0]
    // 0xb907e4: r0 = TextFormField()
    //     0xb907e4: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb907e8: r1 = Null
    //     0xb907e8: mov             x1, NULL
    // 0xb907ec: r2 = 4
    //     0xb907ec: movz            x2, #0x4
    // 0xb907f0: r0 = AllocateArray()
    //     0xb907f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb907f4: stur            x0, [fp, #-0x10]
    // 0xb907f8: r16 = Instance_NLabelTextField
    //     0xb907f8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b08] Obj!NLabelTextField@e1fbd1
    //     0xb907fc: ldr             x16, [x16, #0xb08]
    // 0xb90800: StoreField: r0->field_f = r16
    //     0xb90800: stur            w16, [x0, #0xf]
    // 0xb90804: ldur            x1, [fp, #-0x30]
    // 0xb90808: StoreField: r0->field_13 = r1
    //     0xb90808: stur            w1, [x0, #0x13]
    // 0xb9080c: r1 = <Widget>
    //     0xb9080c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90810: r0 = AllocateGrowableArray()
    //     0xb90810: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90814: mov             x1, x0
    // 0xb90818: ldur            x0, [fp, #-0x10]
    // 0xb9081c: stur            x1, [fp, #-0x18]
    // 0xb90820: StoreField: r1->field_f = r0
    //     0xb90820: stur            w0, [x1, #0xf]
    // 0xb90824: r2 = 4
    //     0xb90824: movz            x2, #0x4
    // 0xb90828: StoreField: r1->field_b = r2
    //     0xb90828: stur            w2, [x1, #0xb]
    // 0xb9082c: r0 = Column()
    //     0xb9082c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb90830: mov             x2, x0
    // 0xb90834: r0 = Instance_Axis
    //     0xb90834: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb90838: stur            x2, [fp, #-0x10]
    // 0xb9083c: StoreField: r2->field_f = r0
    //     0xb9083c: stur            w0, [x2, #0xf]
    // 0xb90840: r3 = Instance_MainAxisAlignment
    //     0xb90840: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb90844: ldr             x3, [x3, #0x730]
    // 0xb90848: StoreField: r2->field_13 = r3
    //     0xb90848: stur            w3, [x2, #0x13]
    // 0xb9084c: r4 = Instance_MainAxisSize
    //     0xb9084c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb90850: ldr             x4, [x4, #0x738]
    // 0xb90854: ArrayStore: r2[0] = r4  ; List_4
    //     0xb90854: stur            w4, [x2, #0x17]
    // 0xb90858: r5 = Instance_CrossAxisAlignment
    //     0xb90858: add             x5, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb9085c: ldr             x5, [x5, #0x68]
    // 0xb90860: StoreField: r2->field_1b = r5
    //     0xb90860: stur            w5, [x2, #0x1b]
    // 0xb90864: r6 = Instance_VerticalDirection
    //     0xb90864: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90868: ldr             x6, [x6, #0x748]
    // 0xb9086c: StoreField: r2->field_23 = r6
    //     0xb9086c: stur            w6, [x2, #0x23]
    // 0xb90870: r7 = Instance_Clip
    //     0xb90870: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90874: ldr             x7, [x7, #0x750]
    // 0xb90878: StoreField: r2->field_2b = r7
    //     0xb90878: stur            w7, [x2, #0x2b]
    // 0xb9087c: StoreField: r2->field_2f = rZR
    //     0xb9087c: stur            xzr, [x2, #0x2f]
    // 0xb90880: ldur            x1, [fp, #-0x18]
    // 0xb90884: StoreField: r2->field_b = r1
    //     0xb90884: stur            w1, [x2, #0xb]
    // 0xb90888: r1 = <FlexParentData>
    //     0xb90888: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb9088c: ldr             x1, [x1, #0x720]
    // 0xb90890: r0 = Expanded()
    //     0xb90890: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb90894: mov             x3, x0
    // 0xb90898: r2 = 3
    //     0xb90898: movz            x2, #0x3
    // 0xb9089c: stur            x3, [fp, #-0x18]
    // 0xb908a0: StoreField: r3->field_13 = r2
    //     0xb908a0: stur            x2, [x3, #0x13]
    // 0xb908a4: r4 = Instance_FlexFit
    //     0xb908a4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb908a8: ldr             x4, [x4, #0x728]
    // 0xb908ac: StoreField: r3->field_1b = r4
    //     0xb908ac: stur            w4, [x3, #0x1b]
    // 0xb908b0: ldur            x0, [fp, #-0x10]
    // 0xb908b4: StoreField: r3->field_b = r0
    //     0xb908b4: stur            w0, [x3, #0xb]
    // 0xb908b8: ldur            x5, [fp, #-8]
    // 0xb908bc: LoadField: r6 = r5->field_f
    //     0xb908bc: ldur            x6, [x5, #0xf]
    // 0xb908c0: r0 = BoxInt64Instr(r6)
    //     0xb908c0: sbfiz           x0, x6, #1, #0x1f
    //     0xb908c4: cmp             x6, x0, asr #1
    //     0xb908c8: b.eq            #0xb908d4
    //     0xb908cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb908d0: stur            x6, [x0, #7]
    // 0xb908d4: r1 = 60
    //     0xb908d4: movz            x1, #0x3c
    // 0xb908d8: branchIfSmi(r0, 0xb908e4)
    //     0xb908d8: tbz             w0, #0, #0xb908e4
    // 0xb908dc: r1 = LoadClassIdInstr(r0)
    //     0xb908dc: ldur            x1, [x0, #-1]
    //     0xb908e0: ubfx            x1, x1, #0xc, #0x14
    // 0xb908e4: str             x0, [SP]
    // 0xb908e8: mov             x0, x1
    // 0xb908ec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb908ec: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb908f0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb908f0: movz            x17, #0x2b03
    //     0xb908f4: add             lr, x0, x17
    //     0xb908f8: ldr             lr, [x21, lr, lsl #3]
    //     0xb908fc: blr             lr
    // 0xb90900: r1 = Function '<anonymous closure>': static.
    //     0xb90900: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xb90904: ldr             x1, [x1, #0xda0]
    // 0xb90908: r2 = Null
    //     0xb90908: mov             x2, NULL
    // 0xb9090c: stur            x0, [fp, #-0x10]
    // 0xb90910: r0 = AllocateClosure()
    //     0xb90910: bl              #0xec1630  ; AllocateClosureStub
    // 0xb90914: r1 = <String>
    //     0xb90914: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90918: stur            x0, [fp, #-0x28]
    // 0xb9091c: StoreField: r0->field_b = r1
    //     0xb9091c: stur            w1, [x0, #0xb]
    // 0xb90920: r16 = <String>
    //     0xb90920: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90924: str             x16, [SP, #8]
    // 0xb90928: r2 = 1
    //     0xb90928: movz            x2, #0x1
    // 0xb9092c: str             x2, [SP]
    // 0xb90930: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb90930: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb90934: r0 = min()
    //     0xb90934: bl              #0xae7668  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::min
    // 0xb90938: r1 = Null
    //     0xb90938: mov             x1, NULL
    // 0xb9093c: r2 = 4
    //     0xb9093c: movz            x2, #0x4
    // 0xb90940: stur            x0, [fp, #-0x30]
    // 0xb90944: r0 = AllocateArray()
    //     0xb90944: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90948: mov             x2, x0
    // 0xb9094c: ldur            x0, [fp, #-0x28]
    // 0xb90950: stur            x2, [fp, #-0x38]
    // 0xb90954: StoreField: r2->field_f = r0
    //     0xb90954: stur            w0, [x2, #0xf]
    // 0xb90958: ldur            x0, [fp, #-0x30]
    // 0xb9095c: StoreField: r2->field_13 = r0
    //     0xb9095c: stur            w0, [x2, #0x13]
    // 0xb90960: r1 = <(dynamic this, String?) => String?>
    //     0xb90960: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xb90964: ldr             x1, [x1, #0xd98]
    // 0xb90968: r0 = AllocateGrowableArray()
    //     0xb90968: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb9096c: mov             x1, x0
    // 0xb90970: ldur            x0, [fp, #-0x38]
    // 0xb90974: stur            x1, [fp, #-0x28]
    // 0xb90978: StoreField: r1->field_f = r0
    //     0xb90978: stur            w0, [x1, #0xf]
    // 0xb9097c: r2 = 4
    //     0xb9097c: movz            x2, #0x4
    // 0xb90980: StoreField: r1->field_b = r2
    //     0xb90980: stur            w2, [x1, #0xb]
    // 0xb90984: r1 = 1
    //     0xb90984: movz            x1, #0x1
    // 0xb90988: r0 = AllocateContext()
    //     0xb90988: bl              #0xec126c  ; AllocateContextStub
    // 0xb9098c: mov             x1, x0
    // 0xb90990: ldur            x0, [fp, #-0x28]
    // 0xb90994: StoreField: r1->field_f = r0
    //     0xb90994: stur            w0, [x1, #0xf]
    // 0xb90998: mov             x2, x1
    // 0xb9099c: r1 = Function '<anonymous closure>': static.
    //     0xb9099c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xb909a0: ldr             x1, [x1, #0xda8]
    // 0xb909a4: r0 = AllocateClosure()
    //     0xb909a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb909a8: r1 = <String>
    //     0xb909a8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb909ac: stur            x0, [fp, #-0x28]
    // 0xb909b0: StoreField: r0->field_b = r1
    //     0xb909b0: stur            w1, [x0, #0xb]
    // 0xb909b4: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb909b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb909b8: ldr             x0, [x0, #0xdd8]
    //     0xb909bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb909c0: cmp             w0, w16
    //     0xb909c4: b.ne            #0xb909d4
    //     0xb909c8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb909cc: ldr             x2, [x2, #0x780]
    //     0xb909d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb909d4: r1 = Null
    //     0xb909d4: mov             x1, NULL
    // 0xb909d8: r2 = 2
    //     0xb909d8: movz            x2, #0x2
    // 0xb909dc: stur            x0, [fp, #-0x30]
    // 0xb909e0: r0 = AllocateArray()
    //     0xb909e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb909e4: mov             x2, x0
    // 0xb909e8: ldur            x0, [fp, #-0x30]
    // 0xb909ec: stur            x2, [fp, #-0x38]
    // 0xb909f0: StoreField: r2->field_f = r0
    //     0xb909f0: stur            w0, [x2, #0xf]
    // 0xb909f4: r1 = <TextInputFormatter>
    //     0xb909f4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb909f8: ldr             x1, [x1, #0x788]
    // 0xb909fc: r0 = AllocateGrowableArray()
    //     0xb909fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90a00: mov             x2, x0
    // 0xb90a04: ldur            x0, [fp, #-0x38]
    // 0xb90a08: stur            x2, [fp, #-0x30]
    // 0xb90a0c: StoreField: r2->field_f = r0
    //     0xb90a0c: stur            w0, [x2, #0xf]
    // 0xb90a10: r0 = 2
    //     0xb90a10: movz            x0, #0x2
    // 0xb90a14: StoreField: r2->field_b = r0
    //     0xb90a14: stur            w0, [x2, #0xb]
    // 0xb90a18: r1 = <String>
    //     0xb90a18: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb90a1c: r0 = TextFormField()
    //     0xb90a1c: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xb90a20: ldur            x2, [fp, #-0x20]
    // 0xb90a24: r1 = Function '<anonymous closure>':.
    //     0xb90a24: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ffa8] AnonymousClosure: (0xb91050), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] _FidyahItemWidget::build (0xb90614)
    //     0xb90a28: ldr             x1, [x1, #0xfa8]
    // 0xb90a2c: stur            x0, [fp, #-0x20]
    // 0xb90a30: r0 = AllocateClosure()
    //     0xb90a30: bl              #0xec1630  ; AllocateClosureStub
    // 0xb90a34: ldur            x16, [fp, #-0x10]
    // 0xb90a38: stp             x0, x16, [SP, #0x18]
    // 0xb90a3c: ldur            x16, [fp, #-0x28]
    // 0xb90a40: r30 = Instance_TextInputType
    //     0xb90a40: add             lr, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb90a44: ldr             lr, [lr, #0x7a0]
    // 0xb90a48: stp             lr, x16, [SP, #8]
    // 0xb90a4c: ldur            x16, [fp, #-0x30]
    // 0xb90a50: str             x16, [SP]
    // 0xb90a54: ldur            x1, [fp, #-0x20]
    // 0xb90a58: r2 = Instance_InputDecoration
    //     0xb90a58: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3ffb0] Obj!InputDecoration@e14401
    //     0xb90a5c: ldr             x2, [x2, #0xfb0]
    // 0xb90a60: r4 = const [0, 0x7, 0x5, 0x2, initialValue, 0x2, inputFormatters, 0x6, keyboardType, 0x5, onChanged, 0x3, validator, 0x4, null]
    //     0xb90a60: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3ffb8] List(15) [0, 0x7, 0x5, 0x2, "initialValue", 0x2, "inputFormatters", 0x6, "keyboardType", 0x5, "onChanged", 0x3, "validator", 0x4, Null]
    //     0xb90a64: ldr             x4, [x4, #0xfb8]
    // 0xb90a68: r0 = TextFormField()
    //     0xb90a68: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb90a6c: r1 = Null
    //     0xb90a6c: mov             x1, NULL
    // 0xb90a70: r2 = 4
    //     0xb90a70: movz            x2, #0x4
    // 0xb90a74: r0 = AllocateArray()
    //     0xb90a74: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90a78: stur            x0, [fp, #-0x10]
    // 0xb90a7c: r16 = Instance_NLabelTextField
    //     0xb90a7c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ffc0] Obj!NLabelTextField@e1fd51
    //     0xb90a80: ldr             x16, [x16, #0xfc0]
    // 0xb90a84: StoreField: r0->field_f = r16
    //     0xb90a84: stur            w16, [x0, #0xf]
    // 0xb90a88: ldur            x1, [fp, #-0x20]
    // 0xb90a8c: StoreField: r0->field_13 = r1
    //     0xb90a8c: stur            w1, [x0, #0x13]
    // 0xb90a90: r1 = <Widget>
    //     0xb90a90: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90a94: r0 = AllocateGrowableArray()
    //     0xb90a94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90a98: mov             x1, x0
    // 0xb90a9c: ldur            x0, [fp, #-0x10]
    // 0xb90aa0: stur            x1, [fp, #-0x20]
    // 0xb90aa4: StoreField: r1->field_f = r0
    //     0xb90aa4: stur            w0, [x1, #0xf]
    // 0xb90aa8: r2 = 4
    //     0xb90aa8: movz            x2, #0x4
    // 0xb90aac: StoreField: r1->field_b = r2
    //     0xb90aac: stur            w2, [x1, #0xb]
    // 0xb90ab0: r0 = Column()
    //     0xb90ab0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb90ab4: mov             x2, x0
    // 0xb90ab8: r0 = Instance_Axis
    //     0xb90ab8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb90abc: stur            x2, [fp, #-0x10]
    // 0xb90ac0: StoreField: r2->field_f = r0
    //     0xb90ac0: stur            w0, [x2, #0xf]
    // 0xb90ac4: r3 = Instance_MainAxisAlignment
    //     0xb90ac4: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb90ac8: ldr             x3, [x3, #0x730]
    // 0xb90acc: StoreField: r2->field_13 = r3
    //     0xb90acc: stur            w3, [x2, #0x13]
    // 0xb90ad0: r4 = Instance_MainAxisSize
    //     0xb90ad0: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb90ad4: ldr             x4, [x4, #0x738]
    // 0xb90ad8: ArrayStore: r2[0] = r4  ; List_4
    //     0xb90ad8: stur            w4, [x2, #0x17]
    // 0xb90adc: r5 = Instance_CrossAxisAlignment
    //     0xb90adc: add             x5, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb90ae0: ldr             x5, [x5, #0x68]
    // 0xb90ae4: StoreField: r2->field_1b = r5
    //     0xb90ae4: stur            w5, [x2, #0x1b]
    // 0xb90ae8: r6 = Instance_VerticalDirection
    //     0xb90ae8: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90aec: ldr             x6, [x6, #0x748]
    // 0xb90af0: StoreField: r2->field_23 = r6
    //     0xb90af0: stur            w6, [x2, #0x23]
    // 0xb90af4: r7 = Instance_Clip
    //     0xb90af4: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90af8: ldr             x7, [x7, #0x750]
    // 0xb90afc: StoreField: r2->field_2b = r7
    //     0xb90afc: stur            w7, [x2, #0x2b]
    // 0xb90b00: StoreField: r2->field_2f = rZR
    //     0xb90b00: stur            xzr, [x2, #0x2f]
    // 0xb90b04: ldur            x1, [fp, #-0x20]
    // 0xb90b08: StoreField: r2->field_b = r1
    //     0xb90b08: stur            w1, [x2, #0xb]
    // 0xb90b0c: r1 = <FlexParentData>
    //     0xb90b0c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb90b10: ldr             x1, [x1, #0x720]
    // 0xb90b14: r0 = Expanded()
    //     0xb90b14: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb90b18: mov             x1, x0
    // 0xb90b1c: r0 = 2
    //     0xb90b1c: movz            x0, #0x2
    // 0xb90b20: stur            x1, [fp, #-0x20]
    // 0xb90b24: StoreField: r1->field_13 = r0
    //     0xb90b24: stur            x0, [x1, #0x13]
    // 0xb90b28: r0 = Instance_FlexFit
    //     0xb90b28: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb90b2c: ldr             x0, [x0, #0x728]
    // 0xb90b30: StoreField: r1->field_1b = r0
    //     0xb90b30: stur            w0, [x1, #0x1b]
    // 0xb90b34: ldur            x2, [fp, #-0x10]
    // 0xb90b38: StoreField: r1->field_b = r2
    //     0xb90b38: stur            w2, [x1, #0xb]
    // 0xb90b3c: r0 = Radius()
    //     0xb90b3c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb90b40: d0 = 8.000000
    //     0xb90b40: fmov            d0, #8.00000000
    // 0xb90b44: stur            x0, [fp, #-0x10]
    // 0xb90b48: StoreField: r0->field_7 = d0
    //     0xb90b48: stur            d0, [x0, #7]
    // 0xb90b4c: StoreField: r0->field_f = d0
    //     0xb90b4c: stur            d0, [x0, #0xf]
    // 0xb90b50: r0 = BorderRadius()
    //     0xb90b50: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb90b54: mov             x3, x0
    // 0xb90b58: ldur            x0, [fp, #-0x10]
    // 0xb90b5c: stur            x3, [fp, #-0x28]
    // 0xb90b60: StoreField: r3->field_7 = r0
    //     0xb90b60: stur            w0, [x3, #7]
    // 0xb90b64: StoreField: r3->field_b = r0
    //     0xb90b64: stur            w0, [x3, #0xb]
    // 0xb90b68: StoreField: r3->field_f = r0
    //     0xb90b68: stur            w0, [x3, #0xf]
    // 0xb90b6c: StoreField: r3->field_13 = r0
    //     0xb90b6c: stur            w0, [x3, #0x13]
    // 0xb90b70: r1 = _ConstMap len:3
    //     0xb90b70: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb90b74: ldr             x1, [x1, #0xbe8]
    // 0xb90b78: r2 = 2
    //     0xb90b78: movz            x2, #0x2
    // 0xb90b7c: r0 = []()
    //     0xb90b7c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb90b80: r16 = <Color?>
    //     0xb90b80: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb90b84: ldr             x16, [x16, #0x98]
    // 0xb90b88: stp             x0, x16, [SP, #8]
    // 0xb90b8c: r16 = Instance_MaterialColor
    //     0xb90b8c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb90b90: ldr             x16, [x16, #0xe38]
    // 0xb90b94: str             x16, [SP]
    // 0xb90b98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb90b98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb90b9c: r0 = mode()
    //     0xb90b9c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb90ba0: stur            x0, [fp, #-0x10]
    // 0xb90ba4: r0 = BoxDecoration()
    //     0xb90ba4: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb90ba8: mov             x1, x0
    // 0xb90bac: ldur            x0, [fp, #-0x10]
    // 0xb90bb0: stur            x1, [fp, #-0x30]
    // 0xb90bb4: StoreField: r1->field_7 = r0
    //     0xb90bb4: stur            w0, [x1, #7]
    // 0xb90bb8: ldur            x0, [fp, #-0x28]
    // 0xb90bbc: StoreField: r1->field_13 = r0
    //     0xb90bbc: stur            w0, [x1, #0x13]
    // 0xb90bc0: r0 = Instance_BoxShape
    //     0xb90bc0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb90bc4: ldr             x0, [x0, #0xca8]
    // 0xb90bc8: StoreField: r1->field_23 = r0
    //     0xb90bc8: stur            w0, [x1, #0x23]
    // 0xb90bcc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb90bcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb90bd0: ldr             x0, [x0, #0x2670]
    //     0xb90bd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb90bd8: cmp             w0, w16
    //     0xb90bdc: b.ne            #0xb90be8
    //     0xb90be0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb90be4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb90be8: r0 = GetNavigation.textTheme()
    //     0xb90be8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb90bec: LoadField: r1 = r0->field_23
    //     0xb90bec: ldur            w1, [x0, #0x23]
    // 0xb90bf0: DecompressPointer r1
    //     0xb90bf0: add             x1, x1, HEAP, lsl #32
    // 0xb90bf4: stur            x1, [fp, #-0x10]
    // 0xb90bf8: r0 = Text()
    //     0xb90bf8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb90bfc: mov             x2, x0
    // 0xb90c00: r0 = "Rp"
    //     0xb90c00: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xb90c04: ldr             x0, [x0, #0xf90]
    // 0xb90c08: stur            x2, [fp, #-0x28]
    // 0xb90c0c: StoreField: r2->field_b = r0
    //     0xb90c0c: stur            w0, [x2, #0xb]
    // 0xb90c10: ldur            x0, [fp, #-0x10]
    // 0xb90c14: StoreField: r2->field_13 = r0
    //     0xb90c14: stur            w0, [x2, #0x13]
    // 0xb90c18: ldur            x0, [fp, #-8]
    // 0xb90c1c: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xb90c1c: ldur            x1, [x0, #0x17]
    // 0xb90c20: r0 = IntExtension.idrNumber()
    //     0xb90c20: bl              #0xb3c280  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idrNumber
    // 0xb90c24: stur            x0, [fp, #-0x10]
    // 0xb90c28: r0 = GetNavigation.textTheme()
    //     0xb90c28: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb90c2c: LoadField: r1 = r0->field_23
    //     0xb90c2c: ldur            w1, [x0, #0x23]
    // 0xb90c30: DecompressPointer r1
    //     0xb90c30: add             x1, x1, HEAP, lsl #32
    // 0xb90c34: cmp             w1, NULL
    // 0xb90c38: b.ne            #0xb90c44
    // 0xb90c3c: r5 = Null
    //     0xb90c3c: mov             x5, NULL
    // 0xb90c40: b               #0xb90c60
    // 0xb90c44: r16 = Instance_FontWeight
    //     0xb90c44: add             x16, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xb90c48: ldr             x16, [x16, #0xcc0]
    // 0xb90c4c: str             x16, [SP]
    // 0xb90c50: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb90c50: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb90c54: ldr             x4, [x4, #0xfe0]
    // 0xb90c58: r0 = copyWith()
    //     0xb90c58: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb90c5c: mov             x5, x0
    // 0xb90c60: ldur            x2, [fp, #-8]
    // 0xb90c64: ldur            x4, [fp, #-0x18]
    // 0xb90c68: ldur            x3, [fp, #-0x20]
    // 0xb90c6c: ldur            x1, [fp, #-0x28]
    // 0xb90c70: ldur            x0, [fp, #-0x10]
    // 0xb90c74: stur            x5, [fp, #-0x38]
    // 0xb90c78: r0 = Text()
    //     0xb90c78: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb90c7c: mov             x3, x0
    // 0xb90c80: ldur            x0, [fp, #-0x10]
    // 0xb90c84: stur            x3, [fp, #-0x40]
    // 0xb90c88: StoreField: r3->field_b = r0
    //     0xb90c88: stur            w0, [x3, #0xb]
    // 0xb90c8c: ldur            x0, [fp, #-0x38]
    // 0xb90c90: StoreField: r3->field_13 = r0
    //     0xb90c90: stur            w0, [x3, #0x13]
    // 0xb90c94: r1 = Null
    //     0xb90c94: mov             x1, NULL
    // 0xb90c98: r2 = 4
    //     0xb90c98: movz            x2, #0x4
    // 0xb90c9c: r0 = AllocateArray()
    //     0xb90c9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90ca0: mov             x2, x0
    // 0xb90ca4: ldur            x0, [fp, #-0x28]
    // 0xb90ca8: stur            x2, [fp, #-0x10]
    // 0xb90cac: StoreField: r2->field_f = r0
    //     0xb90cac: stur            w0, [x2, #0xf]
    // 0xb90cb0: ldur            x0, [fp, #-0x40]
    // 0xb90cb4: StoreField: r2->field_13 = r0
    //     0xb90cb4: stur            w0, [x2, #0x13]
    // 0xb90cb8: r1 = <Widget>
    //     0xb90cb8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90cbc: r0 = AllocateGrowableArray()
    //     0xb90cbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90cc0: mov             x1, x0
    // 0xb90cc4: ldur            x0, [fp, #-0x10]
    // 0xb90cc8: stur            x1, [fp, #-0x28]
    // 0xb90ccc: StoreField: r1->field_f = r0
    //     0xb90ccc: stur            w0, [x1, #0xf]
    // 0xb90cd0: r2 = 4
    //     0xb90cd0: movz            x2, #0x4
    // 0xb90cd4: StoreField: r1->field_b = r2
    //     0xb90cd4: stur            w2, [x1, #0xb]
    // 0xb90cd8: r0 = Row()
    //     0xb90cd8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb90cdc: mov             x1, x0
    // 0xb90ce0: r0 = Instance_Axis
    //     0xb90ce0: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb90ce4: stur            x1, [fp, #-0x10]
    // 0xb90ce8: StoreField: r1->field_f = r0
    //     0xb90ce8: stur            w0, [x1, #0xf]
    // 0xb90cec: r2 = Instance_MainAxisAlignment
    //     0xb90cec: add             x2, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb90cf0: ldr             x2, [x2, #0xae8]
    // 0xb90cf4: StoreField: r1->field_13 = r2
    //     0xb90cf4: stur            w2, [x1, #0x13]
    // 0xb90cf8: r2 = Instance_MainAxisSize
    //     0xb90cf8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb90cfc: ldr             x2, [x2, #0x738]
    // 0xb90d00: ArrayStore: r1[0] = r2  ; List_4
    //     0xb90d00: stur            w2, [x1, #0x17]
    // 0xb90d04: r3 = Instance_CrossAxisAlignment
    //     0xb90d04: add             x3, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb90d08: ldr             x3, [x3, #0x740]
    // 0xb90d0c: StoreField: r1->field_1b = r3
    //     0xb90d0c: stur            w3, [x1, #0x1b]
    // 0xb90d10: r4 = Instance_VerticalDirection
    //     0xb90d10: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90d14: ldr             x4, [x4, #0x748]
    // 0xb90d18: StoreField: r1->field_23 = r4
    //     0xb90d18: stur            w4, [x1, #0x23]
    // 0xb90d1c: r5 = Instance_Clip
    //     0xb90d1c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90d20: ldr             x5, [x5, #0x750]
    // 0xb90d24: StoreField: r1->field_2b = r5
    //     0xb90d24: stur            w5, [x1, #0x2b]
    // 0xb90d28: StoreField: r1->field_2f = rZR
    //     0xb90d28: stur            xzr, [x1, #0x2f]
    // 0xb90d2c: ldur            x6, [fp, #-0x28]
    // 0xb90d30: StoreField: r1->field_b = r6
    //     0xb90d30: stur            w6, [x1, #0xb]
    // 0xb90d34: r0 = Container()
    //     0xb90d34: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb90d38: stur            x0, [fp, #-0x28]
    // 0xb90d3c: r16 = Instance_EdgeInsets
    //     0xb90d3c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ffc8] Obj!EdgeInsets@e13511
    //     0xb90d40: ldr             x16, [x16, #0xfc8]
    // 0xb90d44: ldur            lr, [fp, #-0x30]
    // 0xb90d48: stp             lr, x16, [SP, #8]
    // 0xb90d4c: ldur            x16, [fp, #-0x10]
    // 0xb90d50: str             x16, [SP]
    // 0xb90d54: mov             x1, x0
    // 0xb90d58: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb90d58: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb90d5c: ldr             x4, [x4, #0xe8]
    // 0xb90d60: r0 = Container()
    //     0xb90d60: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb90d64: r1 = Null
    //     0xb90d64: mov             x1, NULL
    // 0xb90d68: r2 = 4
    //     0xb90d68: movz            x2, #0x4
    // 0xb90d6c: r0 = AllocateArray()
    //     0xb90d6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90d70: stur            x0, [fp, #-0x10]
    // 0xb90d74: r16 = Instance_NLabelTextField
    //     0xb90d74: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ffd0] Obj!NLabelTextField@e1fd31
    //     0xb90d78: ldr             x16, [x16, #0xfd0]
    // 0xb90d7c: StoreField: r0->field_f = r16
    //     0xb90d7c: stur            w16, [x0, #0xf]
    // 0xb90d80: ldur            x1, [fp, #-0x28]
    // 0xb90d84: StoreField: r0->field_13 = r1
    //     0xb90d84: stur            w1, [x0, #0x13]
    // 0xb90d88: r1 = <Widget>
    //     0xb90d88: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90d8c: r0 = AllocateGrowableArray()
    //     0xb90d8c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90d90: mov             x1, x0
    // 0xb90d94: ldur            x0, [fp, #-0x10]
    // 0xb90d98: stur            x1, [fp, #-0x28]
    // 0xb90d9c: StoreField: r1->field_f = r0
    //     0xb90d9c: stur            w0, [x1, #0xf]
    // 0xb90da0: r2 = 4
    //     0xb90da0: movz            x2, #0x4
    // 0xb90da4: StoreField: r1->field_b = r2
    //     0xb90da4: stur            w2, [x1, #0xb]
    // 0xb90da8: r0 = Column()
    //     0xb90da8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb90dac: mov             x2, x0
    // 0xb90db0: r0 = Instance_Axis
    //     0xb90db0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb90db4: stur            x2, [fp, #-0x10]
    // 0xb90db8: StoreField: r2->field_f = r0
    //     0xb90db8: stur            w0, [x2, #0xf]
    // 0xb90dbc: r3 = Instance_MainAxisAlignment
    //     0xb90dbc: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb90dc0: ldr             x3, [x3, #0x730]
    // 0xb90dc4: StoreField: r2->field_13 = r3
    //     0xb90dc4: stur            w3, [x2, #0x13]
    // 0xb90dc8: r4 = Instance_MainAxisSize
    //     0xb90dc8: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb90dcc: ldr             x4, [x4, #0x738]
    // 0xb90dd0: ArrayStore: r2[0] = r4  ; List_4
    //     0xb90dd0: stur            w4, [x2, #0x17]
    // 0xb90dd4: r5 = Instance_CrossAxisAlignment
    //     0xb90dd4: add             x5, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb90dd8: ldr             x5, [x5, #0x68]
    // 0xb90ddc: StoreField: r2->field_1b = r5
    //     0xb90ddc: stur            w5, [x2, #0x1b]
    // 0xb90de0: r6 = Instance_VerticalDirection
    //     0xb90de0: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90de4: ldr             x6, [x6, #0x748]
    // 0xb90de8: StoreField: r2->field_23 = r6
    //     0xb90de8: stur            w6, [x2, #0x23]
    // 0xb90dec: r7 = Instance_Clip
    //     0xb90dec: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90df0: ldr             x7, [x7, #0x750]
    // 0xb90df4: StoreField: r2->field_2b = r7
    //     0xb90df4: stur            w7, [x2, #0x2b]
    // 0xb90df8: StoreField: r2->field_2f = rZR
    //     0xb90df8: stur            xzr, [x2, #0x2f]
    // 0xb90dfc: ldur            x1, [fp, #-0x28]
    // 0xb90e00: StoreField: r2->field_b = r1
    //     0xb90e00: stur            w1, [x2, #0xb]
    // 0xb90e04: r1 = <FlexParentData>
    //     0xb90e04: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb90e08: ldr             x1, [x1, #0x720]
    // 0xb90e0c: r0 = Expanded()
    //     0xb90e0c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb90e10: mov             x3, x0
    // 0xb90e14: r0 = 3
    //     0xb90e14: movz            x0, #0x3
    // 0xb90e18: stur            x3, [fp, #-0x28]
    // 0xb90e1c: StoreField: r3->field_13 = r0
    //     0xb90e1c: stur            x0, [x3, #0x13]
    // 0xb90e20: r0 = Instance_FlexFit
    //     0xb90e20: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb90e24: ldr             x0, [x0, #0x728]
    // 0xb90e28: StoreField: r3->field_1b = r0
    //     0xb90e28: stur            w0, [x3, #0x1b]
    // 0xb90e2c: ldur            x0, [fp, #-0x10]
    // 0xb90e30: StoreField: r3->field_b = r0
    //     0xb90e30: stur            w0, [x3, #0xb]
    // 0xb90e34: r1 = Null
    //     0xb90e34: mov             x1, NULL
    // 0xb90e38: r2 = 10
    //     0xb90e38: movz            x2, #0xa
    // 0xb90e3c: r0 = AllocateArray()
    //     0xb90e3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90e40: mov             x2, x0
    // 0xb90e44: ldur            x0, [fp, #-0x18]
    // 0xb90e48: stur            x2, [fp, #-0x10]
    // 0xb90e4c: StoreField: r2->field_f = r0
    //     0xb90e4c: stur            w0, [x2, #0xf]
    // 0xb90e50: r16 = Instance_SizedBox
    //     0xb90e50: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb90e54: ldr             x16, [x16, #0x538]
    // 0xb90e58: StoreField: r2->field_13 = r16
    //     0xb90e58: stur            w16, [x2, #0x13]
    // 0xb90e5c: ldur            x0, [fp, #-0x20]
    // 0xb90e60: ArrayStore: r2[0] = r0  ; List_4
    //     0xb90e60: stur            w0, [x2, #0x17]
    // 0xb90e64: r16 = Instance_SizedBox
    //     0xb90e64: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb90e68: ldr             x16, [x16, #0x538]
    // 0xb90e6c: StoreField: r2->field_1b = r16
    //     0xb90e6c: stur            w16, [x2, #0x1b]
    // 0xb90e70: ldur            x0, [fp, #-0x28]
    // 0xb90e74: StoreField: r2->field_1f = r0
    //     0xb90e74: stur            w0, [x2, #0x1f]
    // 0xb90e78: r1 = <Widget>
    //     0xb90e78: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90e7c: r0 = AllocateGrowableArray()
    //     0xb90e7c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90e80: mov             x1, x0
    // 0xb90e84: ldur            x0, [fp, #-0x10]
    // 0xb90e88: stur            x1, [fp, #-0x18]
    // 0xb90e8c: StoreField: r1->field_f = r0
    //     0xb90e8c: stur            w0, [x1, #0xf]
    // 0xb90e90: r0 = 10
    //     0xb90e90: movz            x0, #0xa
    // 0xb90e94: StoreField: r1->field_b = r0
    //     0xb90e94: stur            w0, [x1, #0xb]
    // 0xb90e98: ldur            x0, [fp, #-8]
    // 0xb90e9c: LoadField: r2 = r0->field_27
    //     0xb90e9c: ldur            w2, [x0, #0x27]
    // 0xb90ea0: DecompressPointer r2
    //     0xb90ea0: add             x2, x2, HEAP, lsl #32
    // 0xb90ea4: stur            x2, [fp, #-0x10]
    // 0xb90ea8: cmp             w2, NULL
    // 0xb90eac: b.eq            #0xb90fe4
    // 0xb90eb0: r0 = IconButton()
    //     0xb90eb0: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb90eb4: mov             x3, x0
    // 0xb90eb8: r0 = 20.000000
    //     0xb90eb8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xb90ebc: ldr             x0, [x0, #0x430]
    // 0xb90ec0: stur            x3, [fp, #-8]
    // 0xb90ec4: StoreField: r3->field_b = r0
    //     0xb90ec4: stur            w0, [x3, #0xb]
    // 0xb90ec8: r0 = Instance_VisualDensity
    //     0xb90ec8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34b98] Obj!VisualDensity@e1c331
    //     0xb90ecc: ldr             x0, [x0, #0xb98]
    // 0xb90ed0: StoreField: r3->field_f = r0
    //     0xb90ed0: stur            w0, [x3, #0xf]
    // 0xb90ed4: ldur            x0, [fp, #-0x10]
    // 0xb90ed8: StoreField: r3->field_3b = r0
    //     0xb90ed8: stur            w0, [x3, #0x3b]
    // 0xb90edc: r0 = false
    //     0xb90edc: add             x0, NULL, #0x30  ; false
    // 0xb90ee0: StoreField: r3->field_47 = r0
    //     0xb90ee0: stur            w0, [x3, #0x47]
    // 0xb90ee4: r0 = Instance_Icon
    //     0xb90ee4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ba8] Obj!Icon@e245b1
    //     0xb90ee8: ldr             x0, [x0, #0xba8]
    // 0xb90eec: StoreField: r3->field_1f = r0
    //     0xb90eec: stur            w0, [x3, #0x1f]
    // 0xb90ef0: r0 = Instance__IconButtonVariant
    //     0xb90ef0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb90ef4: ldr             x0, [x0, #0xf78]
    // 0xb90ef8: StoreField: r3->field_63 = r0
    //     0xb90ef8: stur            w0, [x3, #0x63]
    // 0xb90efc: r1 = Null
    //     0xb90efc: mov             x1, NULL
    // 0xb90f00: r2 = 4
    //     0xb90f00: movz            x2, #0x4
    // 0xb90f04: r0 = AllocateArray()
    //     0xb90f04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb90f08: stur            x0, [fp, #-0x10]
    // 0xb90f0c: r16 = Instance_NLabelTextField
    //     0xb90f0c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ffd8] Obj!NLabelTextField@e1fd11
    //     0xb90f10: ldr             x16, [x16, #0xfd8]
    // 0xb90f14: StoreField: r0->field_f = r16
    //     0xb90f14: stur            w16, [x0, #0xf]
    // 0xb90f18: ldur            x1, [fp, #-8]
    // 0xb90f1c: StoreField: r0->field_13 = r1
    //     0xb90f1c: stur            w1, [x0, #0x13]
    // 0xb90f20: r1 = <Widget>
    //     0xb90f20: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb90f24: r0 = AllocateGrowableArray()
    //     0xb90f24: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb90f28: mov             x1, x0
    // 0xb90f2c: ldur            x0, [fp, #-0x10]
    // 0xb90f30: stur            x1, [fp, #-8]
    // 0xb90f34: StoreField: r1->field_f = r0
    //     0xb90f34: stur            w0, [x1, #0xf]
    // 0xb90f38: r0 = 4
    //     0xb90f38: movz            x0, #0x4
    // 0xb90f3c: StoreField: r1->field_b = r0
    //     0xb90f3c: stur            w0, [x1, #0xb]
    // 0xb90f40: r0 = Column()
    //     0xb90f40: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb90f44: mov             x2, x0
    // 0xb90f48: r0 = Instance_Axis
    //     0xb90f48: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb90f4c: stur            x2, [fp, #-0x10]
    // 0xb90f50: StoreField: r2->field_f = r0
    //     0xb90f50: stur            w0, [x2, #0xf]
    // 0xb90f54: r0 = Instance_MainAxisAlignment
    //     0xb90f54: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb90f58: ldr             x0, [x0, #0x730]
    // 0xb90f5c: StoreField: r2->field_13 = r0
    //     0xb90f5c: stur            w0, [x2, #0x13]
    // 0xb90f60: r3 = Instance_MainAxisSize
    //     0xb90f60: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb90f64: ldr             x3, [x3, #0x738]
    // 0xb90f68: ArrayStore: r2[0] = r3  ; List_4
    //     0xb90f68: stur            w3, [x2, #0x17]
    // 0xb90f6c: r1 = Instance_CrossAxisAlignment
    //     0xb90f6c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb90f70: ldr             x1, [x1, #0x740]
    // 0xb90f74: StoreField: r2->field_1b = r1
    //     0xb90f74: stur            w1, [x2, #0x1b]
    // 0xb90f78: r4 = Instance_VerticalDirection
    //     0xb90f78: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb90f7c: ldr             x4, [x4, #0x748]
    // 0xb90f80: StoreField: r2->field_23 = r4
    //     0xb90f80: stur            w4, [x2, #0x23]
    // 0xb90f84: r5 = Instance_Clip
    //     0xb90f84: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb90f88: ldr             x5, [x5, #0x750]
    // 0xb90f8c: StoreField: r2->field_2b = r5
    //     0xb90f8c: stur            w5, [x2, #0x2b]
    // 0xb90f90: StoreField: r2->field_2f = rZR
    //     0xb90f90: stur            xzr, [x2, #0x2f]
    // 0xb90f94: ldur            x1, [fp, #-8]
    // 0xb90f98: StoreField: r2->field_b = r1
    //     0xb90f98: stur            w1, [x2, #0xb]
    // 0xb90f9c: ldur            x1, [fp, #-0x18]
    // 0xb90fa0: r0 = _growToNextCapacity()
    //     0xb90fa0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb90fa4: ldur            x2, [fp, #-0x18]
    // 0xb90fa8: r0 = 12
    //     0xb90fa8: movz            x0, #0xc
    // 0xb90fac: StoreField: r2->field_b = r0
    //     0xb90fac: stur            w0, [x2, #0xb]
    // 0xb90fb0: LoadField: r1 = r2->field_f
    //     0xb90fb0: ldur            w1, [x2, #0xf]
    // 0xb90fb4: DecompressPointer r1
    //     0xb90fb4: add             x1, x1, HEAP, lsl #32
    // 0xb90fb8: ldur            x0, [fp, #-0x10]
    // 0xb90fbc: ArrayStore: r1[5] = r0  ; List_4
    //     0xb90fbc: add             x25, x1, #0x23
    //     0xb90fc0: str             w0, [x25]
    //     0xb90fc4: tbz             w0, #0, #0xb90fe0
    //     0xb90fc8: ldurb           w16, [x1, #-1]
    //     0xb90fcc: ldurb           w17, [x0, #-1]
    //     0xb90fd0: and             x16, x17, x16, lsr #2
    //     0xb90fd4: tst             x16, HEAP, lsr #32
    //     0xb90fd8: b.eq            #0xb90fe0
    //     0xb90fdc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb90fe0: b               #0xb90fe8
    // 0xb90fe4: mov             x2, x1
    // 0xb90fe8: r0 = Row()
    //     0xb90fe8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb90fec: r1 = Instance_Axis
    //     0xb90fec: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb90ff0: StoreField: r0->field_f = r1
    //     0xb90ff0: stur            w1, [x0, #0xf]
    // 0xb90ff4: r1 = Instance_MainAxisAlignment
    //     0xb90ff4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb90ff8: ldr             x1, [x1, #0x730]
    // 0xb90ffc: StoreField: r0->field_13 = r1
    //     0xb90ffc: stur            w1, [x0, #0x13]
    // 0xb91000: r1 = Instance_MainAxisSize
    //     0xb91000: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb91004: ldr             x1, [x1, #0x738]
    // 0xb91008: ArrayStore: r0[0] = r1  ; List_4
    //     0xb91008: stur            w1, [x0, #0x17]
    // 0xb9100c: r1 = Instance_CrossAxisAlignment
    //     0xb9100c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb91010: ldr             x1, [x1, #0x68]
    // 0xb91014: StoreField: r0->field_1b = r1
    //     0xb91014: stur            w1, [x0, #0x1b]
    // 0xb91018: r1 = Instance_VerticalDirection
    //     0xb91018: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9101c: ldr             x1, [x1, #0x748]
    // 0xb91020: StoreField: r0->field_23 = r1
    //     0xb91020: stur            w1, [x0, #0x23]
    // 0xb91024: r1 = Instance_Clip
    //     0xb91024: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb91028: ldr             x1, [x1, #0x750]
    // 0xb9102c: StoreField: r0->field_2b = r1
    //     0xb9102c: stur            w1, [x0, #0x2b]
    // 0xb91030: StoreField: r0->field_2f = rZR
    //     0xb91030: stur            xzr, [x0, #0x2f]
    // 0xb91034: ldur            x1, [fp, #-0x18]
    // 0xb91038: StoreField: r0->field_b = r1
    //     0xb91038: stur            w1, [x0, #0xb]
    // 0xb9103c: LeaveFrame
    //     0xb9103c: mov             SP, fp
    //     0xb91040: ldp             fp, lr, [SP], #0x10
    // 0xb91044: ret
    //     0xb91044: ret             
    // 0xb91048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9104c: b               #0xb90630
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xb91050, size: 0xb4
    // 0xb91050: EnterFrame
    //     0xb91050: stp             fp, lr, [SP, #-0x10]!
    //     0xb91054: mov             fp, SP
    // 0xb91058: AllocStack(0x18)
    //     0xb91058: sub             SP, SP, #0x18
    // 0xb9105c: SetupParameters()
    //     0xb9105c: ldr             x0, [fp, #0x18]
    //     0xb91060: ldur            w2, [x0, #0x17]
    //     0xb91064: add             x2, x2, HEAP, lsl #32
    //     0xb91068: stur            x2, [fp, #-8]
    // 0xb9106c: CheckStackOverflow
    //     0xb9106c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91070: cmp             SP, x16
    //     0xb91074: b.ls            #0xb910f8
    // 0xb91078: ldr             x1, [fp, #0x10]
    // 0xb9107c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb9107c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb91080: r0 = tryParse()
    //     0xb91080: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0xb91084: cmp             w0, NULL
    // 0xb91088: b.ne            #0xb91094
    // 0xb9108c: r2 = 0
    //     0xb9108c: movz            x2, #0
    // 0xb91090: b               #0xb910a4
    // 0xb91094: r1 = LoadInt32Instr(r0)
    //     0xb91094: sbfx            x1, x0, #1, #0x1f
    //     0xb91098: tbz             w0, #0, #0xb910a0
    //     0xb9109c: ldur            x1, [x0, #7]
    // 0xb910a0: mov             x2, x1
    // 0xb910a4: ldur            x0, [fp, #-8]
    // 0xb910a8: LoadField: r1 = r0->field_f
    //     0xb910a8: ldur            w1, [x0, #0xf]
    // 0xb910ac: DecompressPointer r1
    //     0xb910ac: add             x1, x1, HEAP, lsl #32
    // 0xb910b0: LoadField: r3 = r1->field_23
    //     0xb910b0: ldur            w3, [x1, #0x23]
    // 0xb910b4: DecompressPointer r3
    //     0xb910b4: add             x3, x3, HEAP, lsl #32
    // 0xb910b8: cmp             w3, NULL
    // 0xb910bc: b.eq            #0xb91100
    // 0xb910c0: r0 = BoxInt64Instr(r2)
    //     0xb910c0: sbfiz           x0, x2, #1, #0x1f
    //     0xb910c4: cmp             x2, x0, asr #1
    //     0xb910c8: b.eq            #0xb910d4
    //     0xb910cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb910d0: stur            x2, [x0, #7]
    // 0xb910d4: stp             x0, x3, [SP]
    // 0xb910d8: mov             x0, x3
    // 0xb910dc: ClosureCall
    //     0xb910dc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb910e0: ldur            x2, [x0, #0x1f]
    //     0xb910e4: blr             x2
    // 0xb910e8: r0 = Null
    //     0xb910e8: mov             x0, NULL
    // 0xb910ec: LeaveFrame
    //     0xb910ec: mov             SP, fp
    //     0xb910f0: ldp             fp, lr, [SP], #0x10
    // 0xb910f4: ret
    //     0xb910f4: ret             
    // 0xb910f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb910f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb910fc: b               #0xb91078
    // 0xb91100: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb91100: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 5284, size: 0x14, field offset: 0x14
//   const constructor, 
class FidyahForm extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaeb188, size: 0x74
    // 0xaeb188: EnterFrame
    //     0xaeb188: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb18c: mov             fp, SP
    // 0xaeb190: AllocStack(0x28)
    //     0xaeb190: sub             SP, SP, #0x28
    // 0xaeb194: SetupParameters(FidyahForm this /* r1 => r1, fp-0x8 */)
    //     0xaeb194: stur            x1, [fp, #-8]
    // 0xaeb198: CheckStackOverflow
    //     0xaeb198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb19c: cmp             SP, x16
    //     0xaeb1a0: b.ls            #0xaeb1f4
    // 0xaeb1a4: r1 = 1
    //     0xaeb1a4: movz            x1, #0x1
    // 0xaeb1a8: r0 = AllocateContext()
    //     0xaeb1a8: bl              #0xec126c  ; AllocateContextStub
    // 0xaeb1ac: ldur            x1, [fp, #-8]
    // 0xaeb1b0: stur            x0, [fp, #-0x10]
    // 0xaeb1b4: StoreField: r0->field_f = r1
    //     0xaeb1b4: stur            w1, [x0, #0xf]
    // 0xaeb1b8: r0 = controller()
    //     0xaeb1b8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb1bc: ldur            x2, [fp, #-0x10]
    // 0xaeb1c0: r1 = Function '<anonymous closure>':.
    //     0xaeb1c0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c18] AnonymousClosure: (0xaeb1fc), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb1c4: ldr             x1, [x1, #0xc18]
    // 0xaeb1c8: stur            x0, [fp, #-8]
    // 0xaeb1cc: r0 = AllocateClosure()
    //     0xaeb1cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb1d0: r16 = <int>
    //     0xaeb1d0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaeb1d4: ldur            lr, [fp, #-8]
    // 0xaeb1d8: stp             lr, x16, [SP, #8]
    // 0xaeb1dc: str             x0, [SP]
    // 0xaeb1e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaeb1e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaeb1e4: r0 = StateExt.obx()
    //     0xaeb1e4: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaeb1e8: LeaveFrame
    //     0xaeb1e8: mov             SP, fp
    //     0xaeb1ec: ldp             fp, lr, [SP], #0x10
    // 0xaeb1f0: ret
    //     0xaeb1f0: ret             
    // 0xaeb1f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb1f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb1f8: b               #0xaeb1a4
  }
  [closure] Widget <anonymous closure>(dynamic, int?) {
    // ** addr: 0xaeb1fc, size: 0x3d8
    // 0xaeb1fc: EnterFrame
    //     0xaeb1fc: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb200: mov             fp, SP
    // 0xaeb204: AllocStack(0x50)
    //     0xaeb204: sub             SP, SP, #0x50
    // 0xaeb208: SetupParameters()
    //     0xaeb208: ldr             x0, [fp, #0x18]
    //     0xaeb20c: ldur            w1, [x0, #0x17]
    //     0xaeb210: add             x1, x1, HEAP, lsl #32
    //     0xaeb214: stur            x1, [fp, #-8]
    // 0xaeb218: CheckStackOverflow
    //     0xaeb218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb21c: cmp             SP, x16
    //     0xaeb220: b.ls            #0xaeb5cc
    // 0xaeb224: r1 = 1
    //     0xaeb224: movz            x1, #0x1
    // 0xaeb228: r0 = AllocateContext()
    //     0xaeb228: bl              #0xec126c  ; AllocateContextStub
    // 0xaeb22c: mov             x2, x0
    // 0xaeb230: ldur            x0, [fp, #-8]
    // 0xaeb234: stur            x2, [fp, #-0x10]
    // 0xaeb238: StoreField: r2->field_b = r0
    //     0xaeb238: stur            w0, [x2, #0xb]
    // 0xaeb23c: ldr             x0, [fp, #0x10]
    // 0xaeb240: StoreField: r2->field_f = r0
    //     0xaeb240: stur            w0, [x2, #0xf]
    // 0xaeb244: cmp             w0, NULL
    // 0xaeb248: b.ne            #0xaeb254
    // 0xaeb24c: r1 = 0
    //     0xaeb24c: movz            x1, #0
    // 0xaeb250: b               #0xaeb260
    // 0xaeb254: r1 = LoadInt32Instr(r0)
    //     0xaeb254: sbfx            x1, x0, #1, #0x1f
    //     0xaeb258: tbz             w0, #0, #0xaeb260
    //     0xaeb25c: ldur            x1, [x0, #7]
    // 0xaeb260: cmp             x1, #0
    // 0xaeb264: b.gt            #0xaeb27c
    // 0xaeb268: r0 = Instance_SizedBox
    //     0xaeb268: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xaeb26c: ldr             x0, [x0, #0xc40]
    // 0xaeb270: LeaveFrame
    //     0xaeb270: mov             SP, fp
    //     0xaeb274: ldp             fp, lr, [SP], #0x10
    // 0xaeb278: ret
    //     0xaeb278: ret             
    // 0xaeb27c: cmp             w0, NULL
    // 0xaeb280: b.ne            #0xaeb28c
    // 0xaeb284: r0 = Null
    //     0xaeb284: mov             x0, NULL
    // 0xaeb288: b               #0xaeb29c
    // 0xaeb28c: r1 = LoadInt32Instr(r0)
    //     0xaeb28c: sbfx            x1, x0, #1, #0x1f
    //     0xaeb290: tbz             w0, #0, #0xaeb298
    //     0xaeb294: ldur            x1, [x0, #7]
    // 0xaeb298: r0 = IntExtension.idr()
    //     0xaeb298: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xaeb29c: stur            x0, [fp, #-8]
    // 0xaeb2a0: r1 = Null
    //     0xaeb2a0: mov             x1, NULL
    // 0xaeb2a4: r2 = 4
    //     0xaeb2a4: movz            x2, #0x4
    // 0xaeb2a8: r0 = AllocateArray()
    //     0xaeb2a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaeb2ac: mov             x1, x0
    // 0xaeb2b0: ldur            x0, [fp, #-8]
    // 0xaeb2b4: StoreField: r1->field_f = r0
    //     0xaeb2b4: stur            w0, [x1, #0xf]
    // 0xaeb2b8: r16 = "/orang/hari"
    //     0xaeb2b8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34c20] "/orang/hari"
    //     0xaeb2bc: ldr             x16, [x16, #0xc20]
    // 0xaeb2c0: StoreField: r1->field_13 = r16
    //     0xaeb2c0: stur            w16, [x1, #0x13]
    // 0xaeb2c4: str             x1, [SP]
    // 0xaeb2c8: r0 = _interpolate()
    //     0xaeb2c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaeb2cc: stur            x0, [fp, #-8]
    // 0xaeb2d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaeb2d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaeb2d4: ldr             x0, [x0, #0x2670]
    //     0xaeb2d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaeb2dc: cmp             w0, w16
    //     0xaeb2e0: b.ne            #0xaeb2ec
    //     0xaeb2e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaeb2e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaeb2ec: r0 = GetNavigation.textTheme()
    //     0xaeb2ec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaeb2f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaeb2f0: ldur            w1, [x0, #0x17]
    // 0xaeb2f4: DecompressPointer r1
    //     0xaeb2f4: add             x1, x1, HEAP, lsl #32
    // 0xaeb2f8: stur            x1, [fp, #-0x18]
    // 0xaeb2fc: cmp             w1, NULL
    // 0xaeb300: b.ne            #0xaeb30c
    // 0xaeb304: r1 = Null
    //     0xaeb304: mov             x1, NULL
    // 0xaeb308: b               #0xaeb338
    // 0xaeb30c: r0 = GetNavigation.theme()
    //     0xaeb30c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaeb310: LoadField: r1 = r0->field_3f
    //     0xaeb310: ldur            w1, [x0, #0x3f]
    // 0xaeb314: DecompressPointer r1
    //     0xaeb314: add             x1, x1, HEAP, lsl #32
    // 0xaeb318: LoadField: r0 = r1->field_b
    //     0xaeb318: ldur            w0, [x1, #0xb]
    // 0xaeb31c: DecompressPointer r0
    //     0xaeb31c: add             x0, x0, HEAP, lsl #32
    // 0xaeb320: str             x0, [SP]
    // 0xaeb324: ldur            x1, [fp, #-0x18]
    // 0xaeb328: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaeb328: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaeb32c: ldr             x4, [x4, #0x228]
    // 0xaeb330: r0 = copyWith()
    //     0xaeb330: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaeb334: mov             x1, x0
    // 0xaeb338: ldur            x0, [fp, #-8]
    // 0xaeb33c: stur            x1, [fp, #-0x18]
    // 0xaeb340: r0 = Text()
    //     0xaeb340: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaeb344: mov             x3, x0
    // 0xaeb348: ldur            x0, [fp, #-8]
    // 0xaeb34c: stur            x3, [fp, #-0x20]
    // 0xaeb350: StoreField: r3->field_b = r0
    //     0xaeb350: stur            w0, [x3, #0xb]
    // 0xaeb354: ldur            x0, [fp, #-0x18]
    // 0xaeb358: StoreField: r3->field_13 = r0
    //     0xaeb358: stur            w0, [x3, #0x13]
    // 0xaeb35c: r1 = _ConstMap len:3
    //     0xaeb35c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaeb360: ldr             x1, [x1, #0xbe8]
    // 0xaeb364: r2 = 2
    //     0xaeb364: movz            x2, #0x2
    // 0xaeb368: r0 = []()
    //     0xaeb368: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaeb36c: r16 = <Color?>
    //     0xaeb36c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaeb370: ldr             x16, [x16, #0x98]
    // 0xaeb374: stp             x0, x16, [SP, #8]
    // 0xaeb378: r16 = Instance_MaterialColor
    //     0xaeb378: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xaeb37c: ldr             x16, [x16, #0xe38]
    // 0xaeb380: str             x16, [SP]
    // 0xaeb384: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaeb384: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaeb388: r0 = mode()
    //     0xaeb388: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaeb38c: stur            x0, [fp, #-8]
    // 0xaeb390: r0 = Obx()
    //     0xaeb390: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeb394: ldur            x2, [fp, #-0x10]
    // 0xaeb398: r1 = Function '<anonymous closure>':.
    //     0xaeb398: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c28] AnonymousClosure: (0xaec704), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb39c: ldr             x1, [x1, #0xc28]
    // 0xaeb3a0: stur            x0, [fp, #-0x18]
    // 0xaeb3a4: r0 = AllocateClosure()
    //     0xaeb3a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb3a8: mov             x1, x0
    // 0xaeb3ac: ldur            x0, [fp, #-0x18]
    // 0xaeb3b0: StoreField: r0->field_b = r1
    //     0xaeb3b0: stur            w1, [x0, #0xb]
    // 0xaeb3b4: r0 = Center()
    //     0xaeb3b4: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaeb3b8: mov             x1, x0
    // 0xaeb3bc: r0 = Instance_Alignment
    //     0xaeb3bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xaeb3c0: ldr             x0, [x0, #0x898]
    // 0xaeb3c4: stur            x1, [fp, #-0x28]
    // 0xaeb3c8: StoreField: r1->field_f = r0
    //     0xaeb3c8: stur            w0, [x1, #0xf]
    // 0xaeb3cc: ldur            x0, [fp, #-0x18]
    // 0xaeb3d0: StoreField: r1->field_b = r0
    //     0xaeb3d0: stur            w0, [x1, #0xb]
    // 0xaeb3d4: r0 = Container()
    //     0xaeb3d4: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaeb3d8: stur            x0, [fp, #-0x18]
    // 0xaeb3dc: r16 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xaeb3dc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xaeb3e0: ldr             x16, [x16, #0xc58]
    // 0xaeb3e4: ldur            lr, [fp, #-8]
    // 0xaeb3e8: stp             lr, x16, [SP, #0x10]
    // 0xaeb3ec: r16 = Instance_EdgeInsets
    //     0xaeb3ec: add             x16, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xaeb3f0: ldr             x16, [x16, #0x768]
    // 0xaeb3f4: ldur            lr, [fp, #-0x28]
    // 0xaeb3f8: stp             lr, x16, [SP]
    // 0xaeb3fc: mov             x1, x0
    // 0xaeb400: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x2, padding, 0x3, width, 0x1, null]
    //     0xaeb400: add             x4, PP, #0x32, lsl #12  ; [pp+0x32f50] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xaeb404: ldr             x4, [x4, #0xf50]
    // 0xaeb408: r0 = Container()
    //     0xaeb408: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaeb40c: r0 = Obx()
    //     0xaeb40c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeb410: ldur            x2, [fp, #-0x10]
    // 0xaeb414: r1 = Function '<anonymous closure>':.
    //     0xaeb414: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c30] AnonymousClosure: (0xaeb948), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb418: ldr             x1, [x1, #0xc30]
    // 0xaeb41c: stur            x0, [fp, #-8]
    // 0xaeb420: r0 = AllocateClosure()
    //     0xaeb420: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb424: mov             x1, x0
    // 0xaeb428: ldur            x0, [fp, #-8]
    // 0xaeb42c: StoreField: r0->field_b = r1
    //     0xaeb42c: stur            w1, [x0, #0xb]
    // 0xaeb430: r0 = Obx()
    //     0xaeb430: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeb434: ldur            x2, [fp, #-0x10]
    // 0xaeb438: r1 = Function '<anonymous closure>':.
    //     0xaeb438: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c38] AnonymousClosure: (0xaeb764), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb43c: ldr             x1, [x1, #0xc38]
    // 0xaeb440: stur            x0, [fp, #-0x28]
    // 0xaeb444: r0 = AllocateClosure()
    //     0xaeb444: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb448: mov             x1, x0
    // 0xaeb44c: ldur            x0, [fp, #-0x28]
    // 0xaeb450: StoreField: r0->field_b = r1
    //     0xaeb450: stur            w1, [x0, #0xb]
    // 0xaeb454: r0 = Obx()
    //     0xaeb454: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeb458: ldur            x2, [fp, #-0x10]
    // 0xaeb45c: r1 = Function '<anonymous closure>':.
    //     0xaeb45c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c40] AnonymousClosure: (0xaeb644), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb460: ldr             x1, [x1, #0xc40]
    // 0xaeb464: stur            x0, [fp, #-0x10]
    // 0xaeb468: r0 = AllocateClosure()
    //     0xaeb468: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb46c: mov             x1, x0
    // 0xaeb470: ldur            x0, [fp, #-0x10]
    // 0xaeb474: StoreField: r0->field_b = r1
    //     0xaeb474: stur            w1, [x0, #0xb]
    // 0xaeb478: r1 = Null
    //     0xaeb478: mov             x1, NULL
    // 0xaeb47c: r2 = 4
    //     0xaeb47c: movz            x2, #0x4
    // 0xaeb480: r0 = AllocateArray()
    //     0xaeb480: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaeb484: mov             x2, x0
    // 0xaeb488: ldur            x0, [fp, #-0x10]
    // 0xaeb48c: stur            x2, [fp, #-0x30]
    // 0xaeb490: StoreField: r2->field_f = r0
    //     0xaeb490: stur            w0, [x2, #0xf]
    // 0xaeb494: r16 = Instance_Text
    //     0xaeb494: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b50] Obj!Text@e21c31
    //     0xaeb498: ldr             x16, [x16, #0xb50]
    // 0xaeb49c: StoreField: r2->field_13 = r16
    //     0xaeb49c: stur            w16, [x2, #0x13]
    // 0xaeb4a0: r1 = <Widget>
    //     0xaeb4a0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaeb4a4: r0 = AllocateGrowableArray()
    //     0xaeb4a4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaeb4a8: mov             x1, x0
    // 0xaeb4ac: ldur            x0, [fp, #-0x30]
    // 0xaeb4b0: stur            x1, [fp, #-0x10]
    // 0xaeb4b4: StoreField: r1->field_f = r0
    //     0xaeb4b4: stur            w0, [x1, #0xf]
    // 0xaeb4b8: r0 = 4
    //     0xaeb4b8: movz            x0, #0x4
    // 0xaeb4bc: StoreField: r1->field_b = r0
    //     0xaeb4bc: stur            w0, [x1, #0xb]
    // 0xaeb4c0: r0 = Row()
    //     0xaeb4c0: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaeb4c4: mov             x3, x0
    // 0xaeb4c8: r0 = Instance_Axis
    //     0xaeb4c8: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaeb4cc: stur            x3, [fp, #-0x30]
    // 0xaeb4d0: StoreField: r3->field_f = r0
    //     0xaeb4d0: stur            w0, [x3, #0xf]
    // 0xaeb4d4: r0 = Instance_MainAxisAlignment
    //     0xaeb4d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaeb4d8: ldr             x0, [x0, #0x730]
    // 0xaeb4dc: StoreField: r3->field_13 = r0
    //     0xaeb4dc: stur            w0, [x3, #0x13]
    // 0xaeb4e0: r0 = Instance_MainAxisSize
    //     0xaeb4e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaeb4e4: ldr             x0, [x0, #0x738]
    // 0xaeb4e8: ArrayStore: r3[0] = r0  ; List_4
    //     0xaeb4e8: stur            w0, [x3, #0x17]
    // 0xaeb4ec: r0 = Instance_CrossAxisAlignment
    //     0xaeb4ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaeb4f0: ldr             x0, [x0, #0x740]
    // 0xaeb4f4: StoreField: r3->field_1b = r0
    //     0xaeb4f4: stur            w0, [x3, #0x1b]
    // 0xaeb4f8: r0 = Instance_VerticalDirection
    //     0xaeb4f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaeb4fc: ldr             x0, [x0, #0x748]
    // 0xaeb500: StoreField: r3->field_23 = r0
    //     0xaeb500: stur            w0, [x3, #0x23]
    // 0xaeb504: r0 = Instance_Clip
    //     0xaeb504: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaeb508: ldr             x0, [x0, #0x750]
    // 0xaeb50c: StoreField: r3->field_2b = r0
    //     0xaeb50c: stur            w0, [x3, #0x2b]
    // 0xaeb510: StoreField: r3->field_2f = rZR
    //     0xaeb510: stur            xzr, [x3, #0x2f]
    // 0xaeb514: ldur            x0, [fp, #-0x10]
    // 0xaeb518: StoreField: r3->field_b = r0
    //     0xaeb518: stur            w0, [x3, #0xb]
    // 0xaeb51c: r1 = Null
    //     0xaeb51c: mov             x1, NULL
    // 0xaeb520: r2 = 16
    //     0xaeb520: movz            x2, #0x10
    // 0xaeb524: r0 = AllocateArray()
    //     0xaeb524: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaeb528: mov             x2, x0
    // 0xaeb52c: ldur            x0, [fp, #-0x20]
    // 0xaeb530: stur            x2, [fp, #-0x10]
    // 0xaeb534: StoreField: r2->field_f = r0
    //     0xaeb534: stur            w0, [x2, #0xf]
    // 0xaeb538: r16 = Instance_SizedBox
    //     0xaeb538: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaeb53c: ldr             x16, [x16, #0x950]
    // 0xaeb540: StoreField: r2->field_13 = r16
    //     0xaeb540: stur            w16, [x2, #0x13]
    // 0xaeb544: ldur            x0, [fp, #-0x18]
    // 0xaeb548: ArrayStore: r2[0] = r0  ; List_4
    //     0xaeb548: stur            w0, [x2, #0x17]
    // 0xaeb54c: r16 = Instance_SizedBox
    //     0xaeb54c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaeb550: ldr             x16, [x16, #0x950]
    // 0xaeb554: StoreField: r2->field_1b = r16
    //     0xaeb554: stur            w16, [x2, #0x1b]
    // 0xaeb558: ldur            x0, [fp, #-8]
    // 0xaeb55c: StoreField: r2->field_1f = r0
    //     0xaeb55c: stur            w0, [x2, #0x1f]
    // 0xaeb560: r16 = Instance_SizedBox
    //     0xaeb560: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaeb564: ldr             x16, [x16, #0x950]
    // 0xaeb568: StoreField: r2->field_23 = r16
    //     0xaeb568: stur            w16, [x2, #0x23]
    // 0xaeb56c: ldur            x0, [fp, #-0x28]
    // 0xaeb570: StoreField: r2->field_27 = r0
    //     0xaeb570: stur            w0, [x2, #0x27]
    // 0xaeb574: ldur            x0, [fp, #-0x30]
    // 0xaeb578: StoreField: r2->field_2b = r0
    //     0xaeb578: stur            w0, [x2, #0x2b]
    // 0xaeb57c: r1 = <Widget>
    //     0xaeb57c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaeb580: r0 = AllocateGrowableArray()
    //     0xaeb580: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaeb584: mov             x1, x0
    // 0xaeb588: ldur            x0, [fp, #-0x10]
    // 0xaeb58c: stur            x1, [fp, #-8]
    // 0xaeb590: StoreField: r1->field_f = r0
    //     0xaeb590: stur            w0, [x1, #0xf]
    // 0xaeb594: r0 = 16
    //     0xaeb594: movz            x0, #0x10
    // 0xaeb598: StoreField: r1->field_b = r0
    //     0xaeb598: stur            w0, [x1, #0xb]
    // 0xaeb59c: r0 = NSection()
    //     0xaeb59c: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xaeb5a0: r1 = "Fidyah"
    //     0xaeb5a0: add             x1, PP, #0x30, lsl #12  ; [pp+0x308b8] "Fidyah"
    //     0xaeb5a4: ldr             x1, [x1, #0x8b8]
    // 0xaeb5a8: StoreField: r0->field_b = r1
    //     0xaeb5a8: stur            w1, [x0, #0xb]
    // 0xaeb5ac: ldur            x1, [fp, #-8]
    // 0xaeb5b0: StoreField: r0->field_f = r1
    //     0xaeb5b0: stur            w1, [x0, #0xf]
    // 0xaeb5b4: r1 = false
    //     0xaeb5b4: add             x1, NULL, #0x30  ; false
    // 0xaeb5b8: StoreField: r0->field_27 = r1
    //     0xaeb5b8: stur            w1, [x0, #0x27]
    // 0xaeb5bc: StoreField: r0->field_2b = r1
    //     0xaeb5bc: stur            w1, [x0, #0x2b]
    // 0xaeb5c0: LeaveFrame
    //     0xaeb5c0: mov             SP, fp
    //     0xaeb5c4: ldp             fp, lr, [SP], #0x10
    // 0xaeb5c8: ret
    //     0xaeb5c8: ret             
    // 0xaeb5cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb5cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb5d0: b               #0xaeb224
  }
  [closure] Checkbox <anonymous closure>(dynamic) {
    // ** addr: 0xaeb644, size: 0x120
    // 0xaeb644: EnterFrame
    //     0xaeb644: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb648: mov             fp, SP
    // 0xaeb64c: AllocStack(0x30)
    //     0xaeb64c: sub             SP, SP, #0x30
    // 0xaeb650: SetupParameters()
    //     0xaeb650: ldr             x0, [fp, #0x10]
    //     0xaeb654: ldur            w1, [x0, #0x17]
    //     0xaeb658: add             x1, x1, HEAP, lsl #32
    // 0xaeb65c: CheckStackOverflow
    //     0xaeb65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb660: cmp             SP, x16
    //     0xaeb664: b.ls            #0xaeb75c
    // 0xaeb668: LoadField: r0 = r1->field_b
    //     0xaeb668: ldur            w0, [x1, #0xb]
    // 0xaeb66c: DecompressPointer r0
    //     0xaeb66c: add             x0, x0, HEAP, lsl #32
    // 0xaeb670: stur            x0, [fp, #-8]
    // 0xaeb674: LoadField: r1 = r0->field_f
    //     0xaeb674: ldur            w1, [x0, #0xf]
    // 0xaeb678: DecompressPointer r1
    //     0xaeb678: add             x1, x1, HEAP, lsl #32
    // 0xaeb67c: r0 = controller()
    //     0xaeb67c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb680: LoadField: r1 = r0->field_2b
    //     0xaeb680: ldur            w1, [x0, #0x2b]
    // 0xaeb684: DecompressPointer r1
    //     0xaeb684: add             x1, x1, HEAP, lsl #32
    // 0xaeb688: r0 = value()
    //     0xaeb688: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaeb68c: mov             x2, x0
    // 0xaeb690: ldur            x0, [fp, #-8]
    // 0xaeb694: stur            x2, [fp, #-0x10]
    // 0xaeb698: LoadField: r1 = r0->field_f
    //     0xaeb698: ldur            w1, [x0, #0xf]
    // 0xaeb69c: DecompressPointer r1
    //     0xaeb69c: add             x1, x1, HEAP, lsl #32
    // 0xaeb6a0: r0 = controller()
    //     0xaeb6a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb6a4: LoadField: r2 = r0->field_2b
    //     0xaeb6a4: ldur            w2, [x0, #0x2b]
    // 0xaeb6a8: DecompressPointer r2
    //     0xaeb6a8: add             x2, x2, HEAP, lsl #32
    // 0xaeb6ac: LoadField: r3 = r2->field_7
    //     0xaeb6ac: ldur            w3, [x2, #7]
    // 0xaeb6b0: DecompressPointer r3
    //     0xaeb6b0: add             x3, x3, HEAP, lsl #32
    // 0xaeb6b4: r1 = Function 'call':.
    //     0xaeb6b4: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaeb6b8: ldr             x1, [x1, #0x310]
    // 0xaeb6bc: r0 = AllocateClosureTA()
    //     0xaeb6bc: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaeb6c0: mov             x3, x0
    // 0xaeb6c4: r2 = Null
    //     0xaeb6c4: mov             x2, NULL
    // 0xaeb6c8: r1 = Null
    //     0xaeb6c8: mov             x1, NULL
    // 0xaeb6cc: stur            x3, [fp, #-8]
    // 0xaeb6d0: r8 = (dynamic this, bool?) => bool
    //     0xaeb6d0: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xaeb6d4: ldr             x8, [x8, #0x318]
    // 0xaeb6d8: r3 = Null
    //     0xaeb6d8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34c48] Null
    //     0xaeb6dc: ldr             x3, [x3, #0xc48]
    // 0xaeb6e0: r0 = DefaultTypeTest()
    //     0xaeb6e0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaeb6e4: r16 = <Color?>
    //     0xaeb6e4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaeb6e8: ldr             x16, [x16, #0x98]
    // 0xaeb6ec: r30 = Instance_MaterialColor
    //     0xaeb6ec: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xaeb6f0: ldr             lr, [lr, #0xbf0]
    // 0xaeb6f4: stp             lr, x16, [SP, #8]
    // 0xaeb6f8: r16 = Instance_Color
    //     0xaeb6f8: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaeb6fc: str             x16, [SP]
    // 0xaeb700: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaeb700: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaeb704: r0 = mode()
    //     0xaeb704: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaeb708: stur            x0, [fp, #-0x18]
    // 0xaeb70c: r0 = Checkbox()
    //     0xaeb70c: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xaeb710: ldur            x1, [fp, #-0x10]
    // 0xaeb714: StoreField: r0->field_b = r1
    //     0xaeb714: stur            w1, [x0, #0xb]
    // 0xaeb718: r1 = false
    //     0xaeb718: add             x1, NULL, #0x30  ; false
    // 0xaeb71c: StoreField: r0->field_23 = r1
    //     0xaeb71c: stur            w1, [x0, #0x23]
    // 0xaeb720: ldur            x2, [fp, #-8]
    // 0xaeb724: StoreField: r0->field_f = r2
    //     0xaeb724: stur            w2, [x0, #0xf]
    // 0xaeb728: ldur            x2, [fp, #-0x18]
    // 0xaeb72c: StoreField: r0->field_1f = r2
    //     0xaeb72c: stur            w2, [x0, #0x1f]
    // 0xaeb730: r2 = Instance_VisualDensity
    //     0xaeb730: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xaeb734: ldr             x2, [x2, #0xb68]
    // 0xaeb738: StoreField: r0->field_2b = r2
    //     0xaeb738: stur            w2, [x0, #0x2b]
    // 0xaeb73c: StoreField: r0->field_43 = r1
    //     0xaeb73c: stur            w1, [x0, #0x43]
    // 0xaeb740: StoreField: r0->field_4f = r1
    //     0xaeb740: stur            w1, [x0, #0x4f]
    // 0xaeb744: r1 = Instance__CheckboxType
    //     0xaeb744: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xaeb748: ldr             x1, [x1, #0xb70]
    // 0xaeb74c: StoreField: r0->field_57 = r1
    //     0xaeb74c: stur            w1, [x0, #0x57]
    // 0xaeb750: LeaveFrame
    //     0xaeb750: mov             SP, fp
    //     0xaeb754: ldp             fp, lr, [SP], #0x10
    // 0xaeb758: ret
    //     0xaeb758: ret             
    // 0xaeb75c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb75c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb760: b               #0xaeb668
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0xaeb764, size: 0x164
    // 0xaeb764: EnterFrame
    //     0xaeb764: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb768: mov             fp, SP
    // 0xaeb76c: AllocStack(0x28)
    //     0xaeb76c: sub             SP, SP, #0x28
    // 0xaeb770: SetupParameters()
    //     0xaeb770: ldr             x0, [fp, #0x10]
    //     0xaeb774: ldur            w1, [x0, #0x17]
    //     0xaeb778: add             x1, x1, HEAP, lsl #32
    // 0xaeb77c: CheckStackOverflow
    //     0xaeb77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb780: cmp             SP, x16
    //     0xaeb784: b.ls            #0xaeb8c0
    // 0xaeb788: LoadField: r0 = r1->field_b
    //     0xaeb788: ldur            w0, [x1, #0xb]
    // 0xaeb78c: DecompressPointer r0
    //     0xaeb78c: add             x0, x0, HEAP, lsl #32
    // 0xaeb790: stur            x0, [fp, #-8]
    // 0xaeb794: LoadField: r1 = r0->field_f
    //     0xaeb794: ldur            w1, [x0, #0xf]
    // 0xaeb798: DecompressPointer r1
    //     0xaeb798: add             x1, x1, HEAP, lsl #32
    // 0xaeb79c: r0 = controller()
    //     0xaeb79c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb7a0: LoadField: r1 = r0->field_4b
    //     0xaeb7a0: ldur            w1, [x0, #0x4b]
    // 0xaeb7a4: DecompressPointer r1
    //     0xaeb7a4: add             x1, x1, HEAP, lsl #32
    // 0xaeb7a8: r0 = value()
    //     0xaeb7a8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeb7ac: r1 = LoadClassIdInstr(r0)
    //     0xaeb7ac: ldur            x1, [x0, #-1]
    //     0xaeb7b0: ubfx            x1, x1, #0xc, #0x14
    // 0xaeb7b4: str             x0, [SP]
    // 0xaeb7b8: mov             x0, x1
    // 0xaeb7bc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeb7bc: movz            x17, #0xc834
    //     0xaeb7c0: add             lr, x0, x17
    //     0xaeb7c4: ldr             lr, [x21, lr, lsl #3]
    //     0xaeb7c8: blr             lr
    // 0xaeb7cc: r1 = LoadInt32Instr(r0)
    //     0xaeb7cc: sbfx            x1, x0, #1, #0x1f
    //     0xaeb7d0: tbz             w0, #0, #0xaeb7d8
    //     0xaeb7d4: ldur            x1, [x0, #7]
    // 0xaeb7d8: cmp             x1, #0xa
    // 0xaeb7dc: b.lt            #0xaeb7f4
    // 0xaeb7e0: r0 = Instance_SizedBox
    //     0xaeb7e0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xaeb7e4: ldr             x0, [x0, #0xc40]
    // 0xaeb7e8: LeaveFrame
    //     0xaeb7e8: mov             SP, fp
    //     0xaeb7ec: ldp             fp, lr, [SP], #0x10
    // 0xaeb7f0: ret
    //     0xaeb7f0: ret             
    // 0xaeb7f4: ldur            x0, [fp, #-8]
    // 0xaeb7f8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaeb7f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaeb7fc: ldr             x0, [x0, #0x2670]
    //     0xaeb800: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaeb804: cmp             w0, w16
    //     0xaeb808: b.ne            #0xaeb814
    //     0xaeb80c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaeb810: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaeb814: r0 = GetNavigation.theme()
    //     0xaeb814: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaeb818: LoadField: r1 = r0->field_3f
    //     0xaeb818: ldur            w1, [x0, #0x3f]
    // 0xaeb81c: DecompressPointer r1
    //     0xaeb81c: add             x1, x1, HEAP, lsl #32
    // 0xaeb820: LoadField: r0 = r1->field_b
    //     0xaeb820: ldur            w0, [x1, #0xb]
    // 0xaeb824: DecompressPointer r0
    //     0xaeb824: add             x0, x0, HEAP, lsl #32
    // 0xaeb828: r16 = Instance_Color
    //     0xaeb828: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xaeb82c: stp             x0, x16, [SP, #8]
    // 0xaeb830: r16 = Instance_TextStyle
    //     0xaeb830: add             x16, PP, #0x34, lsl #12  ; [pp+0x34c58] Obj!TextStyle@e1b731
    //     0xaeb834: ldr             x16, [x16, #0xc58]
    // 0xaeb838: str             x16, [SP]
    // 0xaeb83c: r4 = const [0, 0x3, 0x3, 0, backgroundColor, 0, foregroundColor, 0x1, textStyle, 0x2, null]
    //     0xaeb83c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34c60] List(11) [0, 0x3, 0x3, 0, "backgroundColor", 0, "foregroundColor", 0x1, "textStyle", 0x2, Null]
    //     0xaeb840: ldr             x4, [x4, #0xc60]
    // 0xaeb844: r0 = styleFrom()
    //     0xaeb844: bl              #0xa9bb70  ; [package:flutter/src/material/text_button.dart] TextButton::styleFrom
    // 0xaeb848: mov             x2, x0
    // 0xaeb84c: ldur            x0, [fp, #-8]
    // 0xaeb850: stur            x2, [fp, #-0x10]
    // 0xaeb854: LoadField: r1 = r0->field_f
    //     0xaeb854: ldur            w1, [x0, #0xf]
    // 0xaeb858: DecompressPointer r1
    //     0xaeb858: add             x1, x1, HEAP, lsl #32
    // 0xaeb85c: r0 = controller()
    //     0xaeb85c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb860: mov             x2, x0
    // 0xaeb864: r1 = Function 'addItem':.
    //     0xaeb864: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c68] AnonymousClosure: (0xaeb8c8), in [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::addItem (0xaeb900)
    //     0xaeb868: ldr             x1, [x1, #0xc68]
    // 0xaeb86c: r0 = AllocateClosure()
    //     0xaeb86c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb870: stur            x0, [fp, #-8]
    // 0xaeb874: r0 = _TextButtonWithIcon()
    //     0xaeb874: bl              #0xae192c  ; Allocate_TextButtonWithIconStub -> _TextButtonWithIcon (size=0x3c)
    // 0xaeb878: mov             x1, x0
    // 0xaeb87c: ldur            x5, [fp, #-8]
    // 0xaeb880: ldur            x6, [fp, #-0x10]
    // 0xaeb884: r2 = Instance_Icon
    //     0xaeb884: add             x2, PP, #0x34, lsl #12  ; [pp+0x34c70] Obj!Icon@e24131
    //     0xaeb888: ldr             x2, [x2, #0xc70]
    // 0xaeb88c: r3 = Instance_Text
    //     0xaeb88c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34c78] Obj!Text@e21c81
    //     0xaeb890: ldr             x3, [x3, #0xc78]
    // 0xaeb894: stur            x0, [fp, #-8]
    // 0xaeb898: r0 = _TextButtonWithIcon()
    //     0xaeb898: bl              #0xae1838  ; [package:flutter/src/material/text_button.dart] _TextButtonWithIcon::_TextButtonWithIcon
    // 0xaeb89c: r0 = SizedBox()
    //     0xaeb89c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaeb8a0: r1 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xaeb8a0: add             x1, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xaeb8a4: ldr             x1, [x1, #0xc58]
    // 0xaeb8a8: StoreField: r0->field_f = r1
    //     0xaeb8a8: stur            w1, [x0, #0xf]
    // 0xaeb8ac: ldur            x1, [fp, #-8]
    // 0xaeb8b0: StoreField: r0->field_b = r1
    //     0xaeb8b0: stur            w1, [x0, #0xb]
    // 0xaeb8b4: LeaveFrame
    //     0xaeb8b4: mov             SP, fp
    //     0xaeb8b8: ldp             fp, lr, [SP], #0x10
    // 0xaeb8bc: ret
    //     0xaeb8bc: ret             
    // 0xaeb8c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeb8c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeb8c4: b               #0xaeb788
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0xaeb948, size: 0xec
    // 0xaeb948: EnterFrame
    //     0xaeb948: stp             fp, lr, [SP, #-0x10]!
    //     0xaeb94c: mov             fp, SP
    // 0xaeb950: AllocStack(0x30)
    //     0xaeb950: sub             SP, SP, #0x30
    // 0xaeb954: SetupParameters()
    //     0xaeb954: ldr             x0, [fp, #0x10]
    //     0xaeb958: ldur            w2, [x0, #0x17]
    //     0xaeb95c: add             x2, x2, HEAP, lsl #32
    //     0xaeb960: stur            x2, [fp, #-8]
    // 0xaeb964: CheckStackOverflow
    //     0xaeb964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeb968: cmp             SP, x16
    //     0xaeb96c: b.ls            #0xaeba2c
    // 0xaeb970: LoadField: r0 = r2->field_b
    //     0xaeb970: ldur            w0, [x2, #0xb]
    // 0xaeb974: DecompressPointer r0
    //     0xaeb974: add             x0, x0, HEAP, lsl #32
    // 0xaeb978: LoadField: r1 = r0->field_f
    //     0xaeb978: ldur            w1, [x0, #0xf]
    // 0xaeb97c: DecompressPointer r1
    //     0xaeb97c: add             x1, x1, HEAP, lsl #32
    // 0xaeb980: r0 = controller()
    //     0xaeb980: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeb984: LoadField: r1 = r0->field_4b
    //     0xaeb984: ldur            w1, [x0, #0x4b]
    // 0xaeb988: DecompressPointer r1
    //     0xaeb988: add             x1, x1, HEAP, lsl #32
    // 0xaeb98c: r0 = value()
    //     0xaeb98c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeb990: r1 = LoadClassIdInstr(r0)
    //     0xaeb990: ldur            x1, [x0, #-1]
    //     0xaeb994: ubfx            x1, x1, #0xc, #0x14
    // 0xaeb998: str             x0, [SP]
    // 0xaeb99c: mov             x0, x1
    // 0xaeb9a0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaeb9a0: movz            x17, #0xc834
    //     0xaeb9a4: add             lr, x0, x17
    //     0xaeb9a8: ldr             lr, [x21, lr, lsl #3]
    //     0xaeb9ac: blr             lr
    // 0xaeb9b0: r3 = LoadInt32Instr(r0)
    //     0xaeb9b0: sbfx            x3, x0, #1, #0x1f
    //     0xaeb9b4: tbz             w0, #0, #0xaeb9bc
    //     0xaeb9b8: ldur            x3, [x0, #7]
    // 0xaeb9bc: stur            x3, [fp, #-0x10]
    // 0xaeb9c0: r1 = Function '<anonymous closure>':.
    //     0xaeb9c0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c80] AnonymousClosure: (0xaebfd4), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaeb9c4: ldr             x1, [x1, #0xc80]
    // 0xaeb9c8: r2 = Null
    //     0xaeb9c8: mov             x2, NULL
    // 0xaeb9cc: r0 = AllocateClosure()
    //     0xaeb9cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb9d0: ldur            x2, [fp, #-8]
    // 0xaeb9d4: r1 = Function '<anonymous closure>':.
    //     0xaeb9d4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c88] AnonymousClosure: (0xaeba34), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaeb9d8: ldr             x1, [x1, #0xc88]
    // 0xaeb9dc: stur            x0, [fp, #-8]
    // 0xaeb9e0: r0 = AllocateClosure()
    //     0xaeb9e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeb9e4: stur            x0, [fp, #-0x18]
    // 0xaeb9e8: r0 = ListView()
    //     0xaeb9e8: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaeb9ec: stur            x0, [fp, #-0x20]
    // 0xaeb9f0: r16 = true
    //     0xaeb9f0: add             x16, NULL, #0x20  ; true
    // 0xaeb9f4: r30 = Instance_NeverScrollableScrollPhysics
    //     0xaeb9f4: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xaeb9f8: ldr             lr, [lr, #0x290]
    // 0xaeb9fc: stp             lr, x16, [SP]
    // 0xaeba00: mov             x1, x0
    // 0xaeba04: ldur            x2, [fp, #-0x18]
    // 0xaeba08: ldur            x3, [fp, #-0x10]
    // 0xaeba0c: ldur            x5, [fp, #-8]
    // 0xaeba10: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xaeba10: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xaeba14: ldr             x4, [x4, #0x298]
    // 0xaeba18: r0 = ListView.separated()
    //     0xaeba18: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaeba1c: ldur            x0, [fp, #-0x20]
    // 0xaeba20: LeaveFrame
    //     0xaeba20: mov             SP, fp
    //     0xaeba24: ldp             fp, lr, [SP], #0x10
    // 0xaeba28: ret
    //     0xaeba28: ret             
    // 0xaeba2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeba2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeba30: b               #0xaeb970
  }
  [closure] _FidyahItemWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaeba34, size: 0x1d4
    // 0xaeba34: EnterFrame
    //     0xaeba34: stp             fp, lr, [SP, #-0x10]!
    //     0xaeba38: mov             fp, SP
    // 0xaeba3c: AllocStack(0x40)
    //     0xaeba3c: sub             SP, SP, #0x40
    // 0xaeba40: SetupParameters()
    //     0xaeba40: ldr             x0, [fp, #0x20]
    //     0xaeba44: ldur            w1, [x0, #0x17]
    //     0xaeba48: add             x1, x1, HEAP, lsl #32
    //     0xaeba4c: stur            x1, [fp, #-8]
    // 0xaeba50: CheckStackOverflow
    //     0xaeba50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeba54: cmp             SP, x16
    //     0xaeba58: b.ls            #0xaebc00
    // 0xaeba5c: r1 = 2
    //     0xaeba5c: movz            x1, #0x2
    // 0xaeba60: r0 = AllocateContext()
    //     0xaeba60: bl              #0xec126c  ; AllocateContextStub
    // 0xaeba64: mov             x2, x0
    // 0xaeba68: ldur            x0, [fp, #-8]
    // 0xaeba6c: stur            x2, [fp, #-0x18]
    // 0xaeba70: StoreField: r2->field_b = r0
    //     0xaeba70: stur            w0, [x2, #0xb]
    // 0xaeba74: ldr             x1, [fp, #0x10]
    // 0xaeba78: StoreField: r2->field_f = r1
    //     0xaeba78: stur            w1, [x2, #0xf]
    // 0xaeba7c: LoadField: r3 = r0->field_b
    //     0xaeba7c: ldur            w3, [x0, #0xb]
    // 0xaeba80: DecompressPointer r3
    //     0xaeba80: add             x3, x3, HEAP, lsl #32
    // 0xaeba84: stur            x3, [fp, #-0x10]
    // 0xaeba88: LoadField: r1 = r3->field_f
    //     0xaeba88: ldur            w1, [x3, #0xf]
    // 0xaeba8c: DecompressPointer r1
    //     0xaeba8c: add             x1, x1, HEAP, lsl #32
    // 0xaeba90: r0 = controller()
    //     0xaeba90: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeba94: LoadField: r1 = r0->field_4b
    //     0xaeba94: ldur            w1, [x0, #0x4b]
    // 0xaeba98: DecompressPointer r1
    //     0xaeba98: add             x1, x1, HEAP, lsl #32
    // 0xaeba9c: ldur            x2, [fp, #-0x18]
    // 0xaebaa0: LoadField: r0 = r2->field_f
    //     0xaebaa0: ldur            w0, [x2, #0xf]
    // 0xaebaa4: DecompressPointer r0
    //     0xaebaa4: add             x0, x0, HEAP, lsl #32
    // 0xaebaa8: stur            x0, [fp, #-0x20]
    // 0xaebaac: r0 = value()
    //     0xaebaac: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaebab0: r1 = LoadClassIdInstr(r0)
    //     0xaebab0: ldur            x1, [x0, #-1]
    //     0xaebab4: ubfx            x1, x1, #0xc, #0x14
    // 0xaebab8: ldur            x16, [fp, #-0x20]
    // 0xaebabc: stp             x16, x0, [SP]
    // 0xaebac0: mov             x0, x1
    // 0xaebac4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaebac4: movz            x17, #0x3037
    //     0xaebac8: movk            x17, #0x1, lsl #16
    //     0xaebacc: add             lr, x0, x17
    //     0xaebad0: ldr             lr, [x21, lr, lsl #3]
    //     0xaebad4: blr             lr
    // 0xaebad8: mov             x1, x0
    // 0xaebadc: ldur            x2, [fp, #-0x18]
    // 0xaebae0: StoreField: r2->field_13 = r0
    //     0xaebae0: stur            w0, [x2, #0x13]
    //     0xaebae4: ldurb           w16, [x2, #-1]
    //     0xaebae8: ldurb           w17, [x0, #-1]
    //     0xaebaec: and             x16, x17, x16, lsr #2
    //     0xaebaf0: tst             x16, HEAP, lsr #32
    //     0xaebaf4: b.eq            #0xaebafc
    //     0xaebaf8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaebafc: LoadField: r0 = r1->field_7
    //     0xaebafc: ldur            w0, [x1, #7]
    // 0xaebb00: DecompressPointer r0
    //     0xaebb00: add             x0, x0, HEAP, lsl #32
    // 0xaebb04: stur            x0, [fp, #-0x20]
    // 0xaebb08: LoadField: r3 = r1->field_b
    //     0xaebb08: ldur            x3, [x1, #0xb]
    // 0xaebb0c: ldur            x1, [fp, #-8]
    // 0xaebb10: stur            x3, [fp, #-0x30]
    // 0xaebb14: LoadField: r4 = r1->field_f
    //     0xaebb14: ldur            w4, [x1, #0xf]
    // 0xaebb18: DecompressPointer r4
    //     0xaebb18: add             x4, x4, HEAP, lsl #32
    // 0xaebb1c: cmp             w4, NULL
    // 0xaebb20: b.ne            #0xaebb2c
    // 0xaebb24: r4 = 0
    //     0xaebb24: movz            x4, #0
    // 0xaebb28: b               #0xaebb3c
    // 0xaebb2c: r1 = LoadInt32Instr(r4)
    //     0xaebb2c: sbfx            x1, x4, #1, #0x1f
    //     0xaebb30: tbz             w4, #0, #0xaebb38
    //     0xaebb34: ldur            x1, [x4, #7]
    // 0xaebb38: mov             x4, x1
    // 0xaebb3c: ldur            x1, [fp, #-0x10]
    // 0xaebb40: mul             x5, x4, x3
    // 0xaebb44: stur            x5, [fp, #-0x28]
    // 0xaebb48: LoadField: r4 = r1->field_f
    //     0xaebb48: ldur            w4, [x1, #0xf]
    // 0xaebb4c: DecompressPointer r4
    //     0xaebb4c: add             x4, x4, HEAP, lsl #32
    // 0xaebb50: mov             x1, x4
    // 0xaebb54: r0 = controller()
    //     0xaebb54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaebb58: mov             x1, x0
    // 0xaebb5c: r0 = canDeleteItem()
    //     0xaebb5c: bl              #0xaebc14  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::canDeleteItem
    // 0xaebb60: tbnz            w0, #4, #0xaebb7c
    // 0xaebb64: ldur            x2, [fp, #-0x18]
    // 0xaebb68: r1 = Function '<anonymous closure>':.
    //     0xaebb68: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c90] AnonymousClosure: (0xaebf50), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaebb6c: ldr             x1, [x1, #0xc90]
    // 0xaebb70: r0 = AllocateClosure()
    //     0xaebb70: bl              #0xec1630  ; AllocateClosureStub
    // 0xaebb74: mov             x3, x0
    // 0xaebb78: b               #0xaebb80
    // 0xaebb7c: r3 = Null
    //     0xaebb7c: mov             x3, NULL
    // 0xaebb80: ldur            x0, [fp, #-0x20]
    // 0xaebb84: ldur            x1, [fp, #-0x30]
    // 0xaebb88: ldur            x2, [fp, #-0x28]
    // 0xaebb8c: stur            x3, [fp, #-8]
    // 0xaebb90: r0 = _FidyahItemWidget()
    //     0xaebb90: bl              #0xaebc08  ; Allocate_FidyahItemWidgetStub -> _FidyahItemWidget (size=0x2c)
    // 0xaebb94: mov             x3, x0
    // 0xaebb98: ldur            x0, [fp, #-0x20]
    // 0xaebb9c: stur            x3, [fp, #-0x10]
    // 0xaebba0: StoreField: r3->field_b = r0
    //     0xaebba0: stur            w0, [x3, #0xb]
    // 0xaebba4: ldur            x0, [fp, #-0x30]
    // 0xaebba8: StoreField: r3->field_f = r0
    //     0xaebba8: stur            x0, [x3, #0xf]
    // 0xaebbac: ldur            x0, [fp, #-0x28]
    // 0xaebbb0: ArrayStore: r3[0] = r0  ; List_8
    //     0xaebbb0: stur            x0, [x3, #0x17]
    // 0xaebbb4: ldur            x2, [fp, #-0x18]
    // 0xaebbb8: r1 = Function '<anonymous closure>':.
    //     0xaebbb8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34c98] AnonymousClosure: (0xaebea0), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaebbbc: ldr             x1, [x1, #0xc98]
    // 0xaebbc0: r0 = AllocateClosure()
    //     0xaebbc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaebbc4: mov             x1, x0
    // 0xaebbc8: ldur            x0, [fp, #-0x10]
    // 0xaebbcc: StoreField: r0->field_1f = r1
    //     0xaebbcc: stur            w1, [x0, #0x1f]
    // 0xaebbd0: ldur            x2, [fp, #-0x18]
    // 0xaebbd4: r1 = Function '<anonymous closure>':.
    //     0xaebbd4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ca0] AnonymousClosure: (0xaebc8c), in [package:nuonline/app/modules/donation/widgets/fidyah_form.dart] FidyahForm::build (0xaeb188)
    //     0xaebbd8: ldr             x1, [x1, #0xca0]
    // 0xaebbdc: r0 = AllocateClosure()
    //     0xaebbdc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaebbe0: mov             x1, x0
    // 0xaebbe4: ldur            x0, [fp, #-0x10]
    // 0xaebbe8: StoreField: r0->field_23 = r1
    //     0xaebbe8: stur            w1, [x0, #0x23]
    // 0xaebbec: ldur            x1, [fp, #-8]
    // 0xaebbf0: StoreField: r0->field_27 = r1
    //     0xaebbf0: stur            w1, [x0, #0x27]
    // 0xaebbf4: LeaveFrame
    //     0xaebbf4: mov             SP, fp
    //     0xaebbf8: ldp             fp, lr, [SP], #0x10
    // 0xaebbfc: ret
    //     0xaebbfc: ret             
    // 0xaebc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebc00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebc04: b               #0xaeba5c
  }
  [closure] void <anonymous closure>(dynamic, int?) {
    // ** addr: 0xaebc8c, size: 0xb4
    // 0xaebc8c: EnterFrame
    //     0xaebc8c: stp             fp, lr, [SP, #-0x10]!
    //     0xaebc90: mov             fp, SP
    // 0xaebc94: AllocStack(0x20)
    //     0xaebc94: sub             SP, SP, #0x20
    // 0xaebc98: SetupParameters()
    //     0xaebc98: ldr             x0, [fp, #0x18]
    //     0xaebc9c: ldur            w2, [x0, #0x17]
    //     0xaebca0: add             x2, x2, HEAP, lsl #32
    //     0xaebca4: stur            x2, [fp, #-8]
    // 0xaebca8: CheckStackOverflow
    //     0xaebca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebcac: cmp             SP, x16
    //     0xaebcb0: b.ls            #0xaebd38
    // 0xaebcb4: LoadField: r0 = r2->field_b
    //     0xaebcb4: ldur            w0, [x2, #0xb]
    // 0xaebcb8: DecompressPointer r0
    //     0xaebcb8: add             x0, x0, HEAP, lsl #32
    // 0xaebcbc: LoadField: r1 = r0->field_b
    //     0xaebcbc: ldur            w1, [x0, #0xb]
    // 0xaebcc0: DecompressPointer r1
    //     0xaebcc0: add             x1, x1, HEAP, lsl #32
    // 0xaebcc4: LoadField: r0 = r1->field_f
    //     0xaebcc4: ldur            w0, [x1, #0xf]
    // 0xaebcc8: DecompressPointer r0
    //     0xaebcc8: add             x0, x0, HEAP, lsl #32
    // 0xaebccc: mov             x1, x0
    // 0xaebcd0: r0 = controller()
    //     0xaebcd0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaebcd4: mov             x2, x0
    // 0xaebcd8: ldur            x0, [fp, #-8]
    // 0xaebcdc: stur            x2, [fp, #-0x18]
    // 0xaebce0: LoadField: r3 = r0->field_f
    //     0xaebce0: ldur            w3, [x0, #0xf]
    // 0xaebce4: DecompressPointer r3
    //     0xaebce4: add             x3, x3, HEAP, lsl #32
    // 0xaebce8: stur            x3, [fp, #-0x10]
    // 0xaebcec: LoadField: r1 = r0->field_13
    //     0xaebcec: ldur            w1, [x0, #0x13]
    // 0xaebcf0: DecompressPointer r1
    //     0xaebcf0: add             x1, x1, HEAP, lsl #32
    // 0xaebcf4: ldr             x16, [fp, #0x10]
    // 0xaebcf8: str             x16, [SP]
    // 0xaebcfc: r4 = const [0, 0x2, 0x1, 0x1, day, 0x1, null]
    //     0xaebcfc: add             x4, PP, #0x34, lsl #12  ; [pp+0x34ca8] List(7) [0, 0x2, 0x1, 0x1, "day", 0x1, Null]
    //     0xaebd00: ldr             x4, [x4, #0xca8]
    // 0xaebd04: r0 = copyWith()
    //     0xaebd04: bl              #0xaebda4  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahItem::copyWith
    // 0xaebd08: mov             x1, x0
    // 0xaebd0c: ldur            x0, [fp, #-0x10]
    // 0xaebd10: r2 = LoadInt32Instr(r0)
    //     0xaebd10: sbfx            x2, x0, #1, #0x1f
    //     0xaebd14: tbz             w0, #0, #0xaebd1c
    //     0xaebd18: ldur            x2, [x0, #7]
    // 0xaebd1c: mov             x3, x1
    // 0xaebd20: ldur            x1, [fp, #-0x18]
    // 0xaebd24: r0 = changeItem()
    //     0xaebd24: bl              #0xaebd40  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::changeItem
    // 0xaebd28: r0 = Null
    //     0xaebd28: mov             x0, NULL
    // 0xaebd2c: LeaveFrame
    //     0xaebd2c: mov             SP, fp
    //     0xaebd30: ldp             fp, lr, [SP], #0x10
    // 0xaebd34: ret
    //     0xaebd34: ret             
    // 0xaebd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebd3c: b               #0xaebcb4
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xaebea0, size: 0xb0
    // 0xaebea0: EnterFrame
    //     0xaebea0: stp             fp, lr, [SP, #-0x10]!
    //     0xaebea4: mov             fp, SP
    // 0xaebea8: AllocStack(0x20)
    //     0xaebea8: sub             SP, SP, #0x20
    // 0xaebeac: SetupParameters()
    //     0xaebeac: ldr             x0, [fp, #0x18]
    //     0xaebeb0: ldur            w2, [x0, #0x17]
    //     0xaebeb4: add             x2, x2, HEAP, lsl #32
    //     0xaebeb8: stur            x2, [fp, #-8]
    // 0xaebebc: CheckStackOverflow
    //     0xaebebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebec0: cmp             SP, x16
    //     0xaebec4: b.ls            #0xaebf48
    // 0xaebec8: LoadField: r0 = r2->field_b
    //     0xaebec8: ldur            w0, [x2, #0xb]
    // 0xaebecc: DecompressPointer r0
    //     0xaebecc: add             x0, x0, HEAP, lsl #32
    // 0xaebed0: LoadField: r1 = r0->field_b
    //     0xaebed0: ldur            w1, [x0, #0xb]
    // 0xaebed4: DecompressPointer r1
    //     0xaebed4: add             x1, x1, HEAP, lsl #32
    // 0xaebed8: LoadField: r0 = r1->field_f
    //     0xaebed8: ldur            w0, [x1, #0xf]
    // 0xaebedc: DecompressPointer r0
    //     0xaebedc: add             x0, x0, HEAP, lsl #32
    // 0xaebee0: mov             x1, x0
    // 0xaebee4: r0 = controller()
    //     0xaebee4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaebee8: mov             x2, x0
    // 0xaebeec: ldur            x0, [fp, #-8]
    // 0xaebef0: stur            x2, [fp, #-0x18]
    // 0xaebef4: LoadField: r3 = r0->field_f
    //     0xaebef4: ldur            w3, [x0, #0xf]
    // 0xaebef8: DecompressPointer r3
    //     0xaebef8: add             x3, x3, HEAP, lsl #32
    // 0xaebefc: stur            x3, [fp, #-0x10]
    // 0xaebf00: LoadField: r1 = r0->field_13
    //     0xaebf00: ldur            w1, [x0, #0x13]
    // 0xaebf04: DecompressPointer r1
    //     0xaebf04: add             x1, x1, HEAP, lsl #32
    // 0xaebf08: ldr             x16, [fp, #0x10]
    // 0xaebf0c: str             x16, [SP]
    // 0xaebf10: r4 = const [0, 0x2, 0x1, 0x1, name, 0x1, null]
    //     0xaebf10: ldr             x4, [PP, #0x248]  ; [pp+0x248] List(7) [0, 0x2, 0x1, 0x1, "name", 0x1, Null]
    // 0xaebf14: r0 = copyWith()
    //     0xaebf14: bl              #0xaebda4  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahItem::copyWith
    // 0xaebf18: mov             x1, x0
    // 0xaebf1c: ldur            x0, [fp, #-0x10]
    // 0xaebf20: r2 = LoadInt32Instr(r0)
    //     0xaebf20: sbfx            x2, x0, #1, #0x1f
    //     0xaebf24: tbz             w0, #0, #0xaebf2c
    //     0xaebf28: ldur            x2, [x0, #7]
    // 0xaebf2c: mov             x3, x1
    // 0xaebf30: ldur            x1, [fp, #-0x18]
    // 0xaebf34: r0 = changeItem()
    //     0xaebf34: bl              #0xaebd40  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::changeItem
    // 0xaebf38: r0 = Null
    //     0xaebf38: mov             x0, NULL
    // 0xaebf3c: LeaveFrame
    //     0xaebf3c: mov             SP, fp
    //     0xaebf40: ldp             fp, lr, [SP], #0x10
    // 0xaebf44: ret
    //     0xaebf44: ret             
    // 0xaebf48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebf48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebf4c: b               #0xaebec8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaebf50, size: 0x84
    // 0xaebf50: EnterFrame
    //     0xaebf50: stp             fp, lr, [SP, #-0x10]!
    //     0xaebf54: mov             fp, SP
    // 0xaebf58: AllocStack(0x8)
    //     0xaebf58: sub             SP, SP, #8
    // 0xaebf5c: SetupParameters()
    //     0xaebf5c: ldr             x0, [fp, #0x10]
    //     0xaebf60: ldur            w2, [x0, #0x17]
    //     0xaebf64: add             x2, x2, HEAP, lsl #32
    //     0xaebf68: stur            x2, [fp, #-8]
    // 0xaebf6c: CheckStackOverflow
    //     0xaebf6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaebf70: cmp             SP, x16
    //     0xaebf74: b.ls            #0xaebfcc
    // 0xaebf78: LoadField: r0 = r2->field_b
    //     0xaebf78: ldur            w0, [x2, #0xb]
    // 0xaebf7c: DecompressPointer r0
    //     0xaebf7c: add             x0, x0, HEAP, lsl #32
    // 0xaebf80: LoadField: r1 = r0->field_b
    //     0xaebf80: ldur            w1, [x0, #0xb]
    // 0xaebf84: DecompressPointer r1
    //     0xaebf84: add             x1, x1, HEAP, lsl #32
    // 0xaebf88: LoadField: r0 = r1->field_f
    //     0xaebf88: ldur            w0, [x1, #0xf]
    // 0xaebf8c: DecompressPointer r0
    //     0xaebf8c: add             x0, x0, HEAP, lsl #32
    // 0xaebf90: mov             x1, x0
    // 0xaebf94: r0 = controller()
    //     0xaebf94: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaebf98: mov             x1, x0
    // 0xaebf9c: ldur            x0, [fp, #-8]
    // 0xaebfa0: LoadField: r2 = r0->field_f
    //     0xaebfa0: ldur            w2, [x0, #0xf]
    // 0xaebfa4: DecompressPointer r2
    //     0xaebfa4: add             x2, x2, HEAP, lsl #32
    // 0xaebfa8: r0 = LoadInt32Instr(r2)
    //     0xaebfa8: sbfx            x0, x2, #1, #0x1f
    //     0xaebfac: tbz             w2, #0, #0xaebfb4
    //     0xaebfb0: ldur            x0, [x2, #7]
    // 0xaebfb4: mov             x2, x0
    // 0xaebfb8: r0 = deleteHistory()
    //     0xaebfb8: bl              #0xad2cf0  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::deleteHistory
    // 0xaebfbc: r0 = Null
    //     0xaebfbc: mov             x0, NULL
    // 0xaebfc0: LeaveFrame
    //     0xaebfc0: mov             SP, fp
    //     0xaebfc4: ldp             fp, lr, [SP], #0x10
    // 0xaebfc8: ret
    //     0xaebfc8: ret             
    // 0xaebfcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaebfcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaebfd0: b               #0xaebf78
  }
  [closure] Text <anonymous closure>(dynamic) {
    // ** addr: 0xaec704, size: 0x154
    // 0xaec704: EnterFrame
    //     0xaec704: stp             fp, lr, [SP, #-0x10]!
    //     0xaec708: mov             fp, SP
    // 0xaec70c: AllocStack(0x18)
    //     0xaec70c: sub             SP, SP, #0x18
    // 0xaec710: SetupParameters()
    //     0xaec710: ldr             x0, [fp, #0x10]
    //     0xaec714: ldur            w1, [x0, #0x17]
    //     0xaec718: add             x1, x1, HEAP, lsl #32
    // 0xaec71c: CheckStackOverflow
    //     0xaec71c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec720: cmp             SP, x16
    //     0xaec724: b.ls            #0xaec84c
    // 0xaec728: LoadField: r0 = r1->field_b
    //     0xaec728: ldur            w0, [x1, #0xb]
    // 0xaec72c: DecompressPointer r0
    //     0xaec72c: add             x0, x0, HEAP, lsl #32
    // 0xaec730: LoadField: r1 = r0->field_f
    //     0xaec730: ldur            w1, [x0, #0xf]
    // 0xaec734: DecompressPointer r1
    //     0xaec734: add             x1, x1, HEAP, lsl #32
    // 0xaec738: r0 = controller()
    //     0xaec738: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec73c: mov             x1, x0
    // 0xaec740: r0 = totalAmount()
    //     0xaec740: bl              #0xaec858  ; [package:nuonline/app/modules/donation/controllers/fidyah_form_controller.dart] FidyahFormController::totalAmount
    // 0xaec744: mov             x1, x0
    // 0xaec748: r0 = IntExtension.idr()
    //     0xaec748: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xaec74c: stur            x0, [fp, #-8]
    // 0xaec750: r0 = TextSpan()
    //     0xaec750: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaec754: mov             x3, x0
    // 0xaec758: ldur            x0, [fp, #-8]
    // 0xaec75c: stur            x3, [fp, #-0x10]
    // 0xaec760: StoreField: r3->field_b = r0
    //     0xaec760: stur            w0, [x3, #0xb]
    // 0xaec764: r0 = Instance__DeferringMouseCursor
    //     0xaec764: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaec768: ArrayStore: r3[0] = r0  ; List_4
    //     0xaec768: stur            w0, [x3, #0x17]
    // 0xaec76c: r1 = Instance_TextStyle
    //     0xaec76c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34be0] Obj!TextStyle@e1ae71
    //     0xaec770: ldr             x1, [x1, #0xbe0]
    // 0xaec774: StoreField: r3->field_7 = r1
    //     0xaec774: stur            w1, [x3, #7]
    // 0xaec778: r1 = Null
    //     0xaec778: mov             x1, NULL
    // 0xaec77c: r2 = 2
    //     0xaec77c: movz            x2, #0x2
    // 0xaec780: r0 = AllocateArray()
    //     0xaec780: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaec784: mov             x2, x0
    // 0xaec788: ldur            x0, [fp, #-0x10]
    // 0xaec78c: stur            x2, [fp, #-8]
    // 0xaec790: StoreField: r2->field_f = r0
    //     0xaec790: stur            w0, [x2, #0xf]
    // 0xaec794: r1 = <InlineSpan>
    //     0xaec794: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xaec798: ldr             x1, [x1, #0x5f0]
    // 0xaec79c: r0 = AllocateGrowableArray()
    //     0xaec79c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaec7a0: mov             x1, x0
    // 0xaec7a4: ldur            x0, [fp, #-8]
    // 0xaec7a8: stur            x1, [fp, #-0x10]
    // 0xaec7ac: StoreField: r1->field_f = r0
    //     0xaec7ac: stur            w0, [x1, #0xf]
    // 0xaec7b0: r0 = 2
    //     0xaec7b0: movz            x0, #0x2
    // 0xaec7b4: StoreField: r1->field_b = r0
    //     0xaec7b4: stur            w0, [x1, #0xb]
    // 0xaec7b8: r0 = TextSpan()
    //     0xaec7b8: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaec7bc: mov             x1, x0
    // 0xaec7c0: r0 = "Total "
    //     0xaec7c0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34be8] "Total "
    //     0xaec7c4: ldr             x0, [x0, #0xbe8]
    // 0xaec7c8: stur            x1, [fp, #-8]
    // 0xaec7cc: StoreField: r1->field_b = r0
    //     0xaec7cc: stur            w0, [x1, #0xb]
    // 0xaec7d0: ldur            x0, [fp, #-0x10]
    // 0xaec7d4: StoreField: r1->field_f = r0
    //     0xaec7d4: stur            w0, [x1, #0xf]
    // 0xaec7d8: r0 = Instance__DeferringMouseCursor
    //     0xaec7d8: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaec7dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xaec7dc: stur            w0, [x1, #0x17]
    // 0xaec7e0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaec7e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaec7e4: ldr             x0, [x0, #0x2670]
    //     0xaec7e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaec7ec: cmp             w0, w16
    //     0xaec7f0: b.ne            #0xaec7fc
    //     0xaec7f4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaec7f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaec7fc: r0 = GetNavigation.textTheme()
    //     0xaec7fc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaec800: LoadField: r1 = r0->field_2f
    //     0xaec800: ldur            w1, [x0, #0x2f]
    // 0xaec804: DecompressPointer r1
    //     0xaec804: add             x1, x1, HEAP, lsl #32
    // 0xaec808: cmp             w1, NULL
    // 0xaec80c: b.eq            #0xaec854
    // 0xaec810: r16 = Instance_FontWeight
    //     0xaec810: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xaec814: ldr             x16, [x16, #0xc50]
    // 0xaec818: str             x16, [SP]
    // 0xaec81c: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xaec81c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xaec820: ldr             x4, [x4, #0xfe0]
    // 0xaec824: r0 = copyWith()
    //     0xaec824: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaec828: stur            x0, [fp, #-0x10]
    // 0xaec82c: r0 = Text()
    //     0xaec82c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaec830: ldur            x1, [fp, #-8]
    // 0xaec834: StoreField: r0->field_f = r1
    //     0xaec834: stur            w1, [x0, #0xf]
    // 0xaec838: ldur            x1, [fp, #-0x10]
    // 0xaec83c: StoreField: r0->field_13 = r1
    //     0xaec83c: stur            w1, [x0, #0x13]
    // 0xaec840: LeaveFrame
    //     0xaec840: mov             SP, fp
    //     0xaec844: ldp             fp, lr, [SP], #0x10
    // 0xaec848: ret
    //     0xaec848: ret             
    // 0xaec84c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec84c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec850: b               #0xaec728
    // 0xaec854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaec854: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
