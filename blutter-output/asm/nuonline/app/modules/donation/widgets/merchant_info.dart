// lib: , url: package:nuonline/app/modules/donation/widgets/merchant_info.dart

// class id: 1050237, size: 0x8
class :: {
}

// class id: 5034, size: 0x1c, field offset: 0xc
//   const constructor, 
class MerchantInfo extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb91104, size: 0x5a0
    // 0xb91104: EnterFrame
    //     0xb91104: stp             fp, lr, [SP, #-0x10]!
    //     0xb91108: mov             fp, SP
    // 0xb9110c: AllocStack(0x48)
    //     0xb9110c: sub             SP, SP, #0x48
    // 0xb91110: SetupParameters(MerchantInfo this /* r1 => r1, fp-0x8 */)
    //     0xb91110: stur            x1, [fp, #-8]
    // 0xb91114: CheckStackOverflow
    //     0xb91114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91118: cmp             SP, x16
    //     0xb9111c: b.ls            #0xb9169c
    // 0xb91120: r1 = 1
    //     0xb91120: movz            x1, #0x1
    // 0xb91124: r0 = AllocateContext()
    //     0xb91124: bl              #0xec126c  ; AllocateContextStub
    // 0xb91128: mov             x1, x0
    // 0xb9112c: ldur            x0, [fp, #-8]
    // 0xb91130: stur            x1, [fp, #-0x10]
    // 0xb91134: StoreField: r1->field_f = r0
    //     0xb91134: stur            w0, [x1, #0xf]
    // 0xb91138: r0 = Radius()
    //     0xb91138: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb9113c: d0 = 8.000000
    //     0xb9113c: fmov            d0, #8.00000000
    // 0xb91140: stur            x0, [fp, #-0x18]
    // 0xb91144: StoreField: r0->field_7 = d0
    //     0xb91144: stur            d0, [x0, #7]
    // 0xb91148: StoreField: r0->field_f = d0
    //     0xb91148: stur            d0, [x0, #0xf]
    // 0xb9114c: r0 = BorderRadius()
    //     0xb9114c: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb91150: mov             x2, x0
    // 0xb91154: ldur            x0, [fp, #-0x18]
    // 0xb91158: stur            x2, [fp, #-0x28]
    // 0xb9115c: StoreField: r2->field_7 = r0
    //     0xb9115c: stur            w0, [x2, #7]
    // 0xb91160: StoreField: r2->field_b = r0
    //     0xb91160: stur            w0, [x2, #0xb]
    // 0xb91164: StoreField: r2->field_f = r0
    //     0xb91164: stur            w0, [x2, #0xf]
    // 0xb91168: StoreField: r2->field_13 = r0
    //     0xb91168: stur            w0, [x2, #0x13]
    // 0xb9116c: ldur            x0, [fp, #-8]
    // 0xb91170: LoadField: r3 = r0->field_b
    //     0xb91170: ldur            w3, [x0, #0xb]
    // 0xb91174: DecompressPointer r3
    //     0xb91174: add             x3, x3, HEAP, lsl #32
    // 0xb91178: stur            x3, [fp, #-0x20]
    // 0xb9117c: LoadField: r4 = r3->field_f
    //     0xb9117c: ldur            w4, [x3, #0xf]
    // 0xb91180: DecompressPointer r4
    //     0xb91180: add             x4, x4, HEAP, lsl #32
    // 0xb91184: mov             x1, x4
    // 0xb91188: stur            x4, [fp, #-0x18]
    // 0xb9118c: r0 = hasMatch()
    //     0xb9118c: bl              #0x91087c  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::hasMatch
    // 0xb91190: tbnz            w0, #4, #0xb911d0
    // 0xb91194: ldur            x2, [fp, #-0x18]
    // 0xb91198: r0 = NFadeInImageNetwork()
    //     0xb91198: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xb9119c: ldur            x2, [fp, #-0x18]
    // 0xb911a0: StoreField: r0->field_b = r2
    //     0xb911a0: stur            w2, [x0, #0xb]
    // 0xb911a4: r1 = "packages/nuikit/assets/images/icons/image_load_light.png"
    //     0xb911a4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35508] "packages/nuikit/assets/images/icons/image_load_light.png"
    //     0xb911a8: ldr             x1, [x1, #0x508]
    // 0xb911ac: StoreField: r0->field_f = r1
    //     0xb911ac: stur            w1, [x0, #0xf]
    // 0xb911b0: r1 = "packages/nuikit/assets/images/icons/image_load_dark.png"
    //     0xb911b0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35510] "packages/nuikit/assets/images/icons/image_load_dark.png"
    //     0xb911b4: ldr             x1, [x1, #0x510]
    // 0xb911b8: StoreField: r0->field_13 = r1
    //     0xb911b8: stur            w1, [x0, #0x13]
    // 0xb911bc: r1 = Instance_BoxFit
    //     0xb911bc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xb911c0: ldr             x1, [x1, #0xa28]
    // 0xb911c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb911c4: stur            w1, [x0, #0x17]
    // 0xb911c8: mov             x2, x0
    // 0xb911cc: b               #0xb91200
    // 0xb911d0: ldur            x2, [fp, #-0x18]
    // 0xb911d4: r0 = Image()
    //     0xb911d4: bl              #0x92219c  ; AllocateImageStub -> Image (size=0x58)
    // 0xb911d8: stur            x0, [fp, #-0x30]
    // 0xb911dc: r16 = Instance_BoxFit
    //     0xb911dc: add             x16, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xb911e0: ldr             x16, [x16, #0xa28]
    // 0xb911e4: str             x16, [SP]
    // 0xb911e8: mov             x1, x0
    // 0xb911ec: ldur            x2, [fp, #-0x18]
    // 0xb911f0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb911f0: add             x4, PP, #0x35, lsl #12  ; [pp+0x35518] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb911f4: ldr             x4, [x4, #0x518]
    // 0xb911f8: r0 = Image.asset()
    //     0xb911f8: bl              #0x921e44  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xb911fc: ldur            x2, [fp, #-0x30]
    // 0xb91200: ldur            x1, [fp, #-8]
    // 0xb91204: ldur            x0, [fp, #-0x28]
    // 0xb91208: stur            x2, [fp, #-0x18]
    // 0xb9120c: r0 = ClipRRect()
    //     0xb9120c: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb91210: mov             x1, x0
    // 0xb91214: ldur            x0, [fp, #-0x28]
    // 0xb91218: stur            x1, [fp, #-0x30]
    // 0xb9121c: StoreField: r1->field_f = r0
    //     0xb9121c: stur            w0, [x1, #0xf]
    // 0xb91220: r0 = Instance_Clip
    //     0xb91220: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xb91224: ldr             x0, [x0, #0x4f8]
    // 0xb91228: ArrayStore: r1[0] = r0  ; List_4
    //     0xb91228: stur            w0, [x1, #0x17]
    // 0xb9122c: ldur            x0, [fp, #-0x18]
    // 0xb91230: StoreField: r1->field_b = r0
    //     0xb91230: stur            w0, [x1, #0xb]
    // 0xb91234: r0 = ConstrainedBox()
    //     0xb91234: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb91238: mov             x3, x0
    // 0xb9123c: r0 = Instance_BoxConstraints
    //     0xb9123c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35520] Obj!BoxConstraints@e11a51
    //     0xb91240: ldr             x0, [x0, #0x520]
    // 0xb91244: stur            x3, [fp, #-0x28]
    // 0xb91248: StoreField: r3->field_f = r0
    //     0xb91248: stur            w0, [x3, #0xf]
    // 0xb9124c: ldur            x0, [fp, #-0x30]
    // 0xb91250: StoreField: r3->field_b = r0
    //     0xb91250: stur            w0, [x3, #0xb]
    // 0xb91254: ldur            x0, [fp, #-8]
    // 0xb91258: LoadField: r4 = r0->field_f
    //     0xb91258: ldur            w4, [x0, #0xf]
    // 0xb9125c: DecompressPointer r4
    //     0xb9125c: add             x4, x4, HEAP, lsl #32
    // 0xb91260: stur            x4, [fp, #-0x18]
    // 0xb91264: r16 = Instance_PaymentType
    //     0xb91264: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb91268: ldr             x16, [x16, #0x5a8]
    // 0xb9126c: cmp             w4, w16
    // 0xb91270: b.ne            #0xb91288
    // 0xb91274: ldur            x1, [fp, #-0x20]
    // 0xb91278: mov             x0, x4
    // 0xb9127c: r2 = "Infak & Sedekah"
    //     0xb9127c: add             x2, PP, #0x30, lsl #12  ; [pp+0x302d8] "Infak & Sedekah"
    //     0xb91280: ldr             x2, [x2, #0x2d8]
    // 0xb91284: b               #0xb9132c
    // 0xb91288: r16 = Instance_PaymentType
    //     0xb91288: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xb9128c: ldr             x16, [x16, #0x5c0]
    // 0xb91290: cmp             w4, w16
    // 0xb91294: b.ne            #0xb912fc
    // 0xb91298: ldur            x5, [fp, #-0x20]
    // 0xb9129c: LoadField: r1 = r5->field_7
    //     0xb9129c: ldur            w1, [x5, #7]
    // 0xb912a0: DecompressPointer r1
    //     0xb912a0: add             x1, x1, HEAP, lsl #32
    // 0xb912a4: cmp             w1, #4
    // 0xb912a8: b.ne            #0xb912bc
    // 0xb912ac: LoadField: r1 = r5->field_b
    //     0xb912ac: ldur            w1, [x5, #0xb]
    // 0xb912b0: DecompressPointer r1
    //     0xb912b0: add             x1, x1, HEAP, lsl #32
    // 0xb912b4: mov             x0, x1
    // 0xb912b8: b               #0xb912ec
    // 0xb912bc: r1 = Null
    //     0xb912bc: mov             x1, NULL
    // 0xb912c0: r2 = 4
    //     0xb912c0: movz            x2, #0x4
    // 0xb912c4: r0 = AllocateArray()
    //     0xb912c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb912c8: r16 = "Bayar Zakat ke \n"
    //     0xb912c8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35528] "Bayar Zakat ke \n"
    //     0xb912cc: ldr             x16, [x16, #0x528]
    // 0xb912d0: StoreField: r0->field_f = r16
    //     0xb912d0: stur            w16, [x0, #0xf]
    // 0xb912d4: ldur            x1, [fp, #-0x20]
    // 0xb912d8: LoadField: r2 = r1->field_b
    //     0xb912d8: ldur            w2, [x1, #0xb]
    // 0xb912dc: DecompressPointer r2
    //     0xb912dc: add             x2, x2, HEAP, lsl #32
    // 0xb912e0: StoreField: r0->field_13 = r2
    //     0xb912e0: stur            w2, [x0, #0x13]
    // 0xb912e4: str             x0, [SP]
    // 0xb912e8: r0 = _interpolate()
    //     0xb912e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb912ec: mov             x2, x0
    // 0xb912f0: ldur            x1, [fp, #-0x20]
    // 0xb912f4: ldur            x0, [fp, #-0x18]
    // 0xb912f8: b               #0xb9132c
    // 0xb912fc: mov             x0, x4
    // 0xb91300: r16 = Instance_PaymentType
    //     0xb91300: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xb91304: ldr             x16, [x16, #0x5f0]
    // 0xb91308: cmp             w0, w16
    // 0xb9130c: b.ne            #0xb91320
    // 0xb91310: ldur            x1, [fp, #-0x20]
    // 0xb91314: r2 = "Laporan Qurban melalui NU Online Super App"
    //     0xb91314: add             x2, PP, #0x35, lsl #12  ; [pp+0x35530] "Laporan Qurban melalui NU Online Super App"
    //     0xb91318: ldr             x2, [x2, #0x530]
    // 0xb9131c: b               #0xb9132c
    // 0xb91320: ldur            x1, [fp, #-0x20]
    // 0xb91324: LoadField: r2 = r1->field_b
    //     0xb91324: ldur            w2, [x1, #0xb]
    // 0xb91328: DecompressPointer r2
    //     0xb91328: add             x2, x2, HEAP, lsl #32
    // 0xb9132c: stur            x2, [fp, #-0x30]
    // 0xb91330: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb91330: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb91334: ldr             x0, [x0, #0x2670]
    //     0xb91338: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9133c: cmp             w0, w16
    //     0xb91340: b.ne            #0xb9134c
    //     0xb91344: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb91348: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb9134c: r0 = GetNavigation.textTheme()
    //     0xb9134c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb91350: LoadField: r1 = r0->field_f
    //     0xb91350: ldur            w1, [x0, #0xf]
    // 0xb91354: DecompressPointer r1
    //     0xb91354: add             x1, x1, HEAP, lsl #32
    // 0xb91358: cmp             w1, NULL
    // 0xb9135c: b.ne            #0xb91368
    // 0xb91360: r3 = Null
    //     0xb91360: mov             x3, NULL
    // 0xb91364: b               #0xb91384
    // 0xb91368: r16 = 1.200000
    //     0xb91368: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d378] 1.2
    //     0xb9136c: ldr             x16, [x16, #0x378]
    // 0xb91370: str             x16, [SP]
    // 0xb91374: r4 = const [0, 0x2, 0x1, 0x1, height, 0x1, null]
    //     0xb91374: add             x4, PP, #0x35, lsl #12  ; [pp+0x35538] List(7) [0, 0x2, 0x1, 0x1, "height", 0x1, Null]
    //     0xb91378: ldr             x4, [x4, #0x538]
    // 0xb9137c: r0 = copyWith()
    //     0xb9137c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91380: mov             x3, x0
    // 0xb91384: ldur            x2, [fp, #-8]
    // 0xb91388: ldur            x1, [fp, #-0x28]
    // 0xb9138c: ldur            x0, [fp, #-0x30]
    // 0xb91390: stur            x3, [fp, #-0x38]
    // 0xb91394: r0 = Text()
    //     0xb91394: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb91398: mov             x2, x0
    // 0xb9139c: ldur            x0, [fp, #-0x30]
    // 0xb913a0: stur            x2, [fp, #-0x40]
    // 0xb913a4: StoreField: r2->field_b = r0
    //     0xb913a4: stur            w0, [x2, #0xb]
    // 0xb913a8: ldur            x0, [fp, #-0x38]
    // 0xb913ac: StoreField: r2->field_13 = r0
    //     0xb913ac: stur            w0, [x2, #0x13]
    // 0xb913b0: r1 = <FlexParentData>
    //     0xb913b0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb913b4: ldr             x1, [x1, #0x720]
    // 0xb913b8: r0 = Expanded()
    //     0xb913b8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb913bc: mov             x3, x0
    // 0xb913c0: r0 = 1
    //     0xb913c0: movz            x0, #0x1
    // 0xb913c4: stur            x3, [fp, #-0x30]
    // 0xb913c8: StoreField: r3->field_13 = r0
    //     0xb913c8: stur            x0, [x3, #0x13]
    // 0xb913cc: r0 = Instance_FlexFit
    //     0xb913cc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb913d0: ldr             x0, [x0, #0x728]
    // 0xb913d4: StoreField: r3->field_1b = r0
    //     0xb913d4: stur            w0, [x3, #0x1b]
    // 0xb913d8: ldur            x0, [fp, #-0x40]
    // 0xb913dc: StoreField: r3->field_b = r0
    //     0xb913dc: stur            w0, [x3, #0xb]
    // 0xb913e0: r1 = Null
    //     0xb913e0: mov             x1, NULL
    // 0xb913e4: r2 = 6
    //     0xb913e4: movz            x2, #0x6
    // 0xb913e8: r0 = AllocateArray()
    //     0xb913e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb913ec: mov             x2, x0
    // 0xb913f0: ldur            x0, [fp, #-0x28]
    // 0xb913f4: stur            x2, [fp, #-0x38]
    // 0xb913f8: StoreField: r2->field_f = r0
    //     0xb913f8: stur            w0, [x2, #0xf]
    // 0xb913fc: r16 = Instance_SizedBox
    //     0xb913fc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35540] Obj!SizedBox@e1e5a1
    //     0xb91400: ldr             x16, [x16, #0x540]
    // 0xb91404: StoreField: r2->field_13 = r16
    //     0xb91404: stur            w16, [x2, #0x13]
    // 0xb91408: ldur            x0, [fp, #-0x30]
    // 0xb9140c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9140c: stur            w0, [x2, #0x17]
    // 0xb91410: r1 = <Widget>
    //     0xb91410: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91414: r0 = AllocateGrowableArray()
    //     0xb91414: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91418: mov             x1, x0
    // 0xb9141c: ldur            x0, [fp, #-0x38]
    // 0xb91420: stur            x1, [fp, #-0x28]
    // 0xb91424: StoreField: r1->field_f = r0
    //     0xb91424: stur            w0, [x1, #0xf]
    // 0xb91428: r0 = 6
    //     0xb91428: movz            x0, #0x6
    // 0xb9142c: StoreField: r1->field_b = r0
    //     0xb9142c: stur            w0, [x1, #0xb]
    // 0xb91430: r0 = Row()
    //     0xb91430: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb91434: mov             x3, x0
    // 0xb91438: r0 = Instance_Axis
    //     0xb91438: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb9143c: stur            x3, [fp, #-0x30]
    // 0xb91440: StoreField: r3->field_f = r0
    //     0xb91440: stur            w0, [x3, #0xf]
    // 0xb91444: r0 = Instance_MainAxisAlignment
    //     0xb91444: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb91448: ldr             x0, [x0, #0x730]
    // 0xb9144c: StoreField: r3->field_13 = r0
    //     0xb9144c: stur            w0, [x3, #0x13]
    // 0xb91450: r0 = Instance_MainAxisSize
    //     0xb91450: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb91454: ldr             x0, [x0, #0x738]
    // 0xb91458: ArrayStore: r3[0] = r0  ; List_4
    //     0xb91458: stur            w0, [x3, #0x17]
    // 0xb9145c: r0 = Instance_CrossAxisAlignment
    //     0xb9145c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb91460: ldr             x0, [x0, #0x740]
    // 0xb91464: StoreField: r3->field_1b = r0
    //     0xb91464: stur            w0, [x3, #0x1b]
    // 0xb91468: r0 = Instance_VerticalDirection
    //     0xb91468: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9146c: ldr             x0, [x0, #0x748]
    // 0xb91470: StoreField: r3->field_23 = r0
    //     0xb91470: stur            w0, [x3, #0x23]
    // 0xb91474: r0 = Instance_Clip
    //     0xb91474: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb91478: ldr             x0, [x0, #0x750]
    // 0xb9147c: StoreField: r3->field_2b = r0
    //     0xb9147c: stur            w0, [x3, #0x2b]
    // 0xb91480: StoreField: r3->field_2f = rZR
    //     0xb91480: stur            xzr, [x3, #0x2f]
    // 0xb91484: ldur            x0, [fp, #-0x28]
    // 0xb91488: StoreField: r3->field_b = r0
    //     0xb91488: stur            w0, [x3, #0xb]
    // 0xb9148c: r1 = Null
    //     0xb9148c: mov             x1, NULL
    // 0xb91490: r2 = 2
    //     0xb91490: movz            x2, #0x2
    // 0xb91494: r0 = AllocateArray()
    //     0xb91494: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91498: mov             x2, x0
    // 0xb9149c: ldur            x0, [fp, #-0x30]
    // 0xb914a0: stur            x2, [fp, #-0x28]
    // 0xb914a4: StoreField: r2->field_f = r0
    //     0xb914a4: stur            w0, [x2, #0xf]
    // 0xb914a8: r1 = <Widget>
    //     0xb914a8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb914ac: r0 = AllocateGrowableArray()
    //     0xb914ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb914b0: mov             x1, x0
    // 0xb914b4: ldur            x0, [fp, #-0x28]
    // 0xb914b8: stur            x1, [fp, #-0x30]
    // 0xb914bc: StoreField: r1->field_f = r0
    //     0xb914bc: stur            w0, [x1, #0xf]
    // 0xb914c0: r0 = 2
    //     0xb914c0: movz            x0, #0x2
    // 0xb914c4: StoreField: r1->field_b = r0
    //     0xb914c4: stur            w0, [x1, #0xb]
    // 0xb914c8: ldur            x0, [fp, #-8]
    // 0xb914cc: LoadField: r2 = r0->field_13
    //     0xb914cc: ldur            w2, [x0, #0x13]
    // 0xb914d0: DecompressPointer r2
    //     0xb914d0: add             x2, x2, HEAP, lsl #32
    // 0xb914d4: tbnz            w2, #4, #0xb91568
    // 0xb914d8: ldur            x2, [fp, #-0x20]
    // 0xb914dc: LoadField: r3 = r2->field_13
    //     0xb914dc: ldur            w3, [x2, #0x13]
    // 0xb914e0: DecompressPointer r3
    //     0xb914e0: add             x3, x3, HEAP, lsl #32
    // 0xb914e4: stur            x3, [fp, #-0x28]
    // 0xb914e8: cmp             w3, NULL
    // 0xb914ec: b.eq            #0xb91568
    // 0xb914f0: r0 = GetNavigation.textTheme()
    //     0xb914f0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb914f4: LoadField: r1 = r0->field_27
    //     0xb914f4: ldur            w1, [x0, #0x27]
    // 0xb914f8: DecompressPointer r1
    //     0xb914f8: add             x1, x1, HEAP, lsl #32
    // 0xb914fc: stur            x1, [fp, #-0x20]
    // 0xb91500: r0 = Text()
    //     0xb91500: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb91504: mov             x3, x0
    // 0xb91508: ldur            x0, [fp, #-0x28]
    // 0xb9150c: stur            x3, [fp, #-0x38]
    // 0xb91510: StoreField: r3->field_b = r0
    //     0xb91510: stur            w0, [x3, #0xb]
    // 0xb91514: ldur            x0, [fp, #-0x20]
    // 0xb91518: StoreField: r3->field_13 = r0
    //     0xb91518: stur            w0, [x3, #0x13]
    // 0xb9151c: r1 = Null
    //     0xb9151c: mov             x1, NULL
    // 0xb91520: r2 = 4
    //     0xb91520: movz            x2, #0x4
    // 0xb91524: r0 = AllocateArray()
    //     0xb91524: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91528: stur            x0, [fp, #-0x20]
    // 0xb9152c: r16 = Instance_SizedBox
    //     0xb9152c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb91530: ldr             x16, [x16, #0x448]
    // 0xb91534: StoreField: r0->field_f = r16
    //     0xb91534: stur            w16, [x0, #0xf]
    // 0xb91538: ldur            x1, [fp, #-0x38]
    // 0xb9153c: StoreField: r0->field_13 = r1
    //     0xb9153c: stur            w1, [x0, #0x13]
    // 0xb91540: r1 = <Widget>
    //     0xb91540: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91544: r0 = AllocateGrowableArray()
    //     0xb91544: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91548: mov             x1, x0
    // 0xb9154c: ldur            x0, [fp, #-0x20]
    // 0xb91550: StoreField: r1->field_f = r0
    //     0xb91550: stur            w0, [x1, #0xf]
    // 0xb91554: r0 = 4
    //     0xb91554: movz            x0, #0x4
    // 0xb91558: StoreField: r1->field_b = r0
    //     0xb91558: stur            w0, [x1, #0xb]
    // 0xb9155c: mov             x2, x1
    // 0xb91560: ldur            x1, [fp, #-0x30]
    // 0xb91564: r0 = addAll()
    //     0xb91564: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb91568: ldur            x0, [fp, #-8]
    // 0xb9156c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb9156c: ldur            w1, [x0, #0x17]
    // 0xb91570: DecompressPointer r1
    //     0xb91570: add             x1, x1, HEAP, lsl #32
    // 0xb91574: tbnz            w1, #4, #0xb9166c
    // 0xb91578: ldur            x0, [fp, #-0x18]
    // 0xb9157c: r16 = Instance_PaymentType
    //     0xb9157c: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb91580: ldr             x16, [x16, #0x5a8]
    // 0xb91584: cmp             w0, w16
    // 0xb91588: b.ne            #0xb91598
    // 0xb9158c: r0 = "Sedekah Sekarang"
    //     0xb9158c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35548] "Sedekah Sekarang"
    //     0xb91590: ldr             x0, [x0, #0x548]
    // 0xb91594: b               #0xb915a0
    // 0xb91598: r0 = "Bayar Zakat"
    //     0xb91598: add             x0, PP, #0x35, lsl #12  ; [pp+0x35550] "Bayar Zakat"
    //     0xb9159c: ldr             x0, [x0, #0x550]
    // 0xb915a0: stur            x0, [fp, #-8]
    // 0xb915a4: r0 = Text()
    //     0xb915a4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb915a8: mov             x3, x0
    // 0xb915ac: ldur            x0, [fp, #-8]
    // 0xb915b0: stur            x3, [fp, #-0x18]
    // 0xb915b4: StoreField: r3->field_b = r0
    //     0xb915b4: stur            w0, [x3, #0xb]
    // 0xb915b8: r0 = Instance__LinearTextScaler
    //     0xb915b8: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb915bc: StoreField: r3->field_33 = r0
    //     0xb915bc: stur            w0, [x3, #0x33]
    // 0xb915c0: ldur            x2, [fp, #-0x10]
    // 0xb915c4: r1 = Function '<anonymous closure>':.
    //     0xb915c4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35558] AnonymousClosure: (0xb916a4), in [package:nuonline/app/modules/donation/widgets/merchant_info.dart] MerchantInfo::build (0xb91104)
    //     0xb915c8: ldr             x1, [x1, #0x558]
    // 0xb915cc: r0 = AllocateClosure()
    //     0xb915cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb915d0: stur            x0, [fp, #-8]
    // 0xb915d4: r0 = TextButton()
    //     0xb915d4: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb915d8: mov             x1, x0
    // 0xb915dc: ldur            x0, [fp, #-8]
    // 0xb915e0: stur            x1, [fp, #-0x10]
    // 0xb915e4: StoreField: r1->field_b = r0
    //     0xb915e4: stur            w0, [x1, #0xb]
    // 0xb915e8: r0 = false
    //     0xb915e8: add             x0, NULL, #0x30  ; false
    // 0xb915ec: StoreField: r1->field_27 = r0
    //     0xb915ec: stur            w0, [x1, #0x27]
    // 0xb915f0: r2 = true
    //     0xb915f0: add             x2, NULL, #0x20  ; true
    // 0xb915f4: StoreField: r1->field_2f = r2
    //     0xb915f4: stur            w2, [x1, #0x2f]
    // 0xb915f8: ldur            x2, [fp, #-0x18]
    // 0xb915fc: StoreField: r1->field_37 = r2
    //     0xb915fc: stur            w2, [x1, #0x37]
    // 0xb91600: r0 = SizedBox()
    //     0xb91600: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb91604: mov             x3, x0
    // 0xb91608: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb91608: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb9160c: ldr             x0, [x0, #0xc58]
    // 0xb91610: stur            x3, [fp, #-8]
    // 0xb91614: StoreField: r3->field_f = r0
    //     0xb91614: stur            w0, [x3, #0xf]
    // 0xb91618: ldur            x0, [fp, #-0x10]
    // 0xb9161c: StoreField: r3->field_b = r0
    //     0xb9161c: stur            w0, [x3, #0xb]
    // 0xb91620: r1 = Null
    //     0xb91620: mov             x1, NULL
    // 0xb91624: r2 = 4
    //     0xb91624: movz            x2, #0x4
    // 0xb91628: r0 = AllocateArray()
    //     0xb91628: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb9162c: stur            x0, [fp, #-0x10]
    // 0xb91630: r16 = Instance_SizedBox
    //     0xb91630: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xb91634: ldr             x16, [x16, #0x540]
    // 0xb91638: StoreField: r0->field_f = r16
    //     0xb91638: stur            w16, [x0, #0xf]
    // 0xb9163c: ldur            x1, [fp, #-8]
    // 0xb91640: StoreField: r0->field_13 = r1
    //     0xb91640: stur            w1, [x0, #0x13]
    // 0xb91644: r1 = <Widget>
    //     0xb91644: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91648: r0 = AllocateGrowableArray()
    //     0xb91648: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb9164c: mov             x1, x0
    // 0xb91650: ldur            x0, [fp, #-0x10]
    // 0xb91654: StoreField: r1->field_f = r0
    //     0xb91654: stur            w0, [x1, #0xf]
    // 0xb91658: r0 = 4
    //     0xb91658: movz            x0, #0x4
    // 0xb9165c: StoreField: r1->field_b = r0
    //     0xb9165c: stur            w0, [x1, #0xb]
    // 0xb91660: mov             x2, x1
    // 0xb91664: ldur            x1, [fp, #-0x30]
    // 0xb91668: r0 = addAll()
    //     0xb91668: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb9166c: ldur            x0, [fp, #-0x30]
    // 0xb91670: r0 = NSection()
    //     0xb91670: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb91674: r1 = ""
    //     0xb91674: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb91678: StoreField: r0->field_b = r1
    //     0xb91678: stur            w1, [x0, #0xb]
    // 0xb9167c: ldur            x1, [fp, #-0x30]
    // 0xb91680: StoreField: r0->field_f = r1
    //     0xb91680: stur            w1, [x0, #0xf]
    // 0xb91684: r1 = false
    //     0xb91684: add             x1, NULL, #0x30  ; false
    // 0xb91688: StoreField: r0->field_27 = r1
    //     0xb91688: stur            w1, [x0, #0x27]
    // 0xb9168c: StoreField: r0->field_2b = r1
    //     0xb9168c: stur            w1, [x0, #0x2b]
    // 0xb91690: LeaveFrame
    //     0xb91690: mov             SP, fp
    //     0xb91694: ldp             fp, lr, [SP], #0x10
    // 0xb91698: ret
    //     0xb91698: ret             
    // 0xb9169c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9169c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb916a0: b               #0xb91120
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb916a4, size: 0x9c
    // 0xb916a4: EnterFrame
    //     0xb916a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb916a8: mov             fp, SP
    // 0xb916ac: AllocStack(0x18)
    //     0xb916ac: sub             SP, SP, #0x18
    // 0xb916b0: SetupParameters()
    //     0xb916b0: ldr             x0, [fp, #0x10]
    //     0xb916b4: ldur            w1, [x0, #0x17]
    //     0xb916b8: add             x1, x1, HEAP, lsl #32
    //     0xb916bc: stur            x1, [fp, #-8]
    // 0xb916c0: CheckStackOverflow
    //     0xb916c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb916c4: cmp             SP, x16
    //     0xb916c8: b.ls            #0xb91738
    // 0xb916cc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb916cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb916d0: ldr             x0, [x0, #0x2670]
    //     0xb916d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb916d8: cmp             w0, w16
    //     0xb916dc: b.ne            #0xb916e8
    //     0xb916e0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb916e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb916e8: ldur            x0, [fp, #-8]
    // 0xb916ec: LoadField: r1 = r0->field_f
    //     0xb916ec: ldur            w1, [x0, #0xf]
    // 0xb916f0: DecompressPointer r1
    //     0xb916f0: add             x1, x1, HEAP, lsl #32
    // 0xb916f4: LoadField: r0 = r1->field_f
    //     0xb916f4: ldur            w0, [x1, #0xf]
    // 0xb916f8: DecompressPointer r0
    //     0xb916f8: add             x0, x0, HEAP, lsl #32
    // 0xb916fc: r16 = Instance_PaymentType
    //     0xb916fc: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb91700: ldr             x16, [x16, #0x5a8]
    // 0xb91704: cmp             w0, w16
    // 0xb91708: b.ne            #0xb91718
    // 0xb9170c: r0 = "/donation/pay-donation"
    //     0xb9170c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35560] "/donation/pay-donation"
    //     0xb91710: ldr             x0, [x0, #0x560]
    // 0xb91714: b               #0xb91720
    // 0xb91718: r0 = "/donation/pay-zakat"
    //     0xb91718: add             x0, PP, #0x27, lsl #12  ; [pp+0x27138] "/donation/pay-zakat"
    //     0xb9171c: ldr             x0, [x0, #0x138]
    // 0xb91720: stp             x0, NULL, [SP]
    // 0xb91724: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb91724: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb91728: r0 = GetNavigation.toNamed()
    //     0xb91728: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb9172c: LeaveFrame
    //     0xb9172c: mov             SP, fp
    //     0xb91730: ldp             fp, lr, [SP], #0x10
    // 0xb91734: ret
    //     0xb91734: ret             
    // 0xb91738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9173c: b               #0xb916cc
  }
}
