// lib: , url: package:nuonline/app/modules/donation/widgets/niat_fidyah.dart

// class id: 1050238, size: 0x8
class :: {
}

// class id: 5033, size: 0xc, field offset: 0xc
//   const constructor, 
class NiatFidyah extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb91740, size: 0x13c
    // 0xb91740: EnterFrame
    //     0xb91740: stp             fp, lr, [SP, #-0x10]!
    //     0xb91744: mov             fp, SP
    // 0xb91748: AllocStack(0x20)
    //     0xb91748: sub             SP, SP, #0x20
    // 0xb9174c: CheckStackOverflow
    //     0xb9174c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91750: cmp             SP, x16
    //     0xb91754: b.ls            #0xb91870
    // 0xb91758: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb91758: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9175c: ldr             x0, [x0, #0x2670]
    //     0xb91760: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb91764: cmp             w0, w16
    //     0xb91768: b.ne            #0xb91774
    //     0xb9176c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb91770: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb91774: r0 = GetNavigation.textTheme()
    //     0xb91774: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb91778: LoadField: r1 = r0->field_f
    //     0xb91778: ldur            w1, [x0, #0xf]
    // 0xb9177c: DecompressPointer r1
    //     0xb9177c: add             x1, x1, HEAP, lsl #32
    // 0xb91780: cmp             w1, NULL
    // 0xb91784: b.eq            #0xb91878
    // 0xb91788: r16 = 16.000000
    //     0xb91788: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb9178c: ldr             x16, [x16, #0x80]
    // 0xb91790: str             x16, [SP]
    // 0xb91794: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb91794: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb91798: ldr             x4, [x4, #0x88]
    // 0xb9179c: r0 = copyWith()
    //     0xb9179c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb917a0: stur            x0, [fp, #-8]
    // 0xb917a4: r0 = NDoaListTile()
    //     0xb917a4: bl              #0xb8a284  ; AllocateNDoaListTileStub -> NDoaListTile (size=0x28)
    // 0xb917a8: mov             x3, x0
    // 0xb917ac: r0 = "نَوَيْتُ أَنْ أُخْرِجَ هَذِهِ الْفِدْيَةَ لإِفْطَارِ صَوْمِ رَمَضَانَ فَرْضًــا لِلّٰهِ تَعَــالٰى"
    //     0xb917ac: add             x0, PP, #0x35, lsl #12  ; [pp+0x354e8] "نَوَيْتُ أَنْ أُخْرِجَ هَذِهِ الْفِدْيَةَ لإِفْطَارِ صَوْمِ رَمَضَانَ فَرْضًــا لِلّٰهِ تَعَــالٰى"
    //     0xb917b0: ldr             x0, [x0, #0x4e8]
    // 0xb917b4: stur            x3, [fp, #-0x10]
    // 0xb917b8: StoreField: r3->field_b = r0
    //     0xb917b8: stur            w0, [x3, #0xb]
    // 0xb917bc: r0 = "Nawaitu an ukhrija hâdzihil fidyata li-ifthâri shaumi Ramadhâna fardhan lillâhi ta‘âlâ"
    //     0xb917bc: add             x0, PP, #0x35, lsl #12  ; [pp+0x354f0] "Nawaitu an ukhrija hâdzihil fidyata li-ifthâri shaumi Ramadhâna fardhan lillâhi ta‘âlâ"
    //     0xb917c0: ldr             x0, [x0, #0x4f0]
    // 0xb917c4: StoreField: r3->field_f = r0
    //     0xb917c4: stur            w0, [x3, #0xf]
    // 0xb917c8: r0 = "Aku niat mengeluarkan fidyah ini karena berbuka puasa di bulan Ramadhan, fardu karena Allah yang Mahaluhur."
    //     0xb917c8: add             x0, PP, #0x35, lsl #12  ; [pp+0x354f8] "Aku niat mengeluarkan fidyah ini karena berbuka puasa di bulan Ramadhan, fardu karena Allah yang Mahaluhur."
    //     0xb917cc: ldr             x0, [x0, #0x4f8]
    // 0xb917d0: StoreField: r3->field_13 = r0
    //     0xb917d0: stur            w0, [x3, #0x13]
    // 0xb917d4: r0 = false
    //     0xb917d4: add             x0, NULL, #0x30  ; false
    // 0xb917d8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb917d8: stur            w0, [x3, #0x17]
    // 0xb917dc: StoreField: r3->field_1b = r0
    //     0xb917dc: stur            w0, [x3, #0x1b]
    // 0xb917e0: r0 = Instance_ReadingPref
    //     0xb917e0: add             x0, PP, #0x35, lsl #12  ; [pp+0x354b8] Obj!ReadingPref@e25c11
    //     0xb917e4: ldr             x0, [x0, #0x4b8]
    // 0xb917e8: StoreField: r3->field_1f = r0
    //     0xb917e8: stur            w0, [x3, #0x1f]
    // 0xb917ec: r0 = Instance_EdgeInsets
    //     0xb917ec: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb917f0: StoreField: r3->field_23 = r0
    //     0xb917f0: stur            w0, [x3, #0x23]
    // 0xb917f4: r1 = Null
    //     0xb917f4: mov             x1, NULL
    // 0xb917f8: r2 = 4
    //     0xb917f8: movz            x2, #0x4
    // 0xb917fc: r0 = AllocateArray()
    //     0xb917fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91800: stur            x0, [fp, #-0x18]
    // 0xb91804: r16 = Instance_SizedBox
    //     0xb91804: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb91808: ldr             x16, [x16, #0xfe8]
    // 0xb9180c: StoreField: r0->field_f = r16
    //     0xb9180c: stur            w16, [x0, #0xf]
    // 0xb91810: ldur            x1, [fp, #-0x10]
    // 0xb91814: StoreField: r0->field_13 = r1
    //     0xb91814: stur            w1, [x0, #0x13]
    // 0xb91818: r1 = <Widget>
    //     0xb91818: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb9181c: r0 = AllocateGrowableArray()
    //     0xb9181c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91820: mov             x1, x0
    // 0xb91824: ldur            x0, [fp, #-0x18]
    // 0xb91828: stur            x1, [fp, #-0x10]
    // 0xb9182c: StoreField: r1->field_f = r0
    //     0xb9182c: stur            w0, [x1, #0xf]
    // 0xb91830: r0 = 4
    //     0xb91830: movz            x0, #0x4
    // 0xb91834: StoreField: r1->field_b = r0
    //     0xb91834: stur            w0, [x1, #0xb]
    // 0xb91838: r0 = NSection()
    //     0xb91838: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb9183c: r1 = "Niat Fidyah"
    //     0xb9183c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35500] "Niat Fidyah"
    //     0xb91840: ldr             x1, [x1, #0x500]
    // 0xb91844: StoreField: r0->field_b = r1
    //     0xb91844: stur            w1, [x0, #0xb]
    // 0xb91848: ldur            x1, [fp, #-0x10]
    // 0xb9184c: StoreField: r0->field_f = r1
    //     0xb9184c: stur            w1, [x0, #0xf]
    // 0xb91850: ldur            x1, [fp, #-8]
    // 0xb91854: StoreField: r0->field_1f = r1
    //     0xb91854: stur            w1, [x0, #0x1f]
    // 0xb91858: r1 = true
    //     0xb91858: add             x1, NULL, #0x20  ; true
    // 0xb9185c: StoreField: r0->field_27 = r1
    //     0xb9185c: stur            w1, [x0, #0x27]
    // 0xb91860: StoreField: r0->field_2b = r1
    //     0xb91860: stur            w1, [x0, #0x2b]
    // 0xb91864: LeaveFrame
    //     0xb91864: mov             SP, fp
    //     0xb91868: ldp             fp, lr, [SP], #0x10
    // 0xb9186c: ret
    //     0xb9186c: ret             
    // 0xb91870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb91874: b               #0xb91758
    // 0xb91878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb91878: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
