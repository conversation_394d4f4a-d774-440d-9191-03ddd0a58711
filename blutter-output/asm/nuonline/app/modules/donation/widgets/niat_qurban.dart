// lib: , url: package:nuonline/app/modules/donation/widgets/niat_qurban.dart

// class id: 1050239, size: 0x8
class :: {
}

// class id: 5032, size: 0xc, field offset: 0xc
//   const constructor, 
class NiatQurban extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9187c, size: 0x13c
    // 0xb9187c: EnterFrame
    //     0xb9187c: stp             fp, lr, [SP, #-0x10]!
    //     0xb91880: mov             fp, SP
    // 0xb91884: AllocStack(0x20)
    //     0xb91884: sub             SP, SP, #0x20
    // 0xb91888: CheckStackOverflow
    //     0xb91888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9188c: cmp             SP, x16
    //     0xb91890: b.ls            #0xb919ac
    // 0xb91894: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb91894: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb91898: ldr             x0, [x0, #0x2670]
    //     0xb9189c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb918a0: cmp             w0, w16
    //     0xb918a4: b.ne            #0xb918b0
    //     0xb918a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb918ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb918b0: r0 = GetNavigation.textTheme()
    //     0xb918b0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb918b4: LoadField: r1 = r0->field_f
    //     0xb918b4: ldur            w1, [x0, #0xf]
    // 0xb918b8: DecompressPointer r1
    //     0xb918b8: add             x1, x1, HEAP, lsl #32
    // 0xb918bc: cmp             w1, NULL
    // 0xb918c0: b.eq            #0xb919b4
    // 0xb918c4: r16 = 16.000000
    //     0xb918c4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb918c8: ldr             x16, [x16, #0x80]
    // 0xb918cc: str             x16, [SP]
    // 0xb918d0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb918d0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb918d4: ldr             x4, [x4, #0x88]
    // 0xb918d8: r0 = copyWith()
    //     0xb918d8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb918dc: stur            x0, [fp, #-8]
    // 0xb918e0: r0 = NDoaListTile()
    //     0xb918e0: bl              #0xb8a284  ; AllocateNDoaListTileStub -> NDoaListTile (size=0x28)
    // 0xb918e4: mov             x3, x0
    // 0xb918e8: r0 = "نَوَيْتُ الْأُضْحِيَّةَ لِلّٰهِ تَعَالَى"
    //     0xb918e8: add             x0, PP, #0x35, lsl #12  ; [pp+0x354c8] "نَوَيْتُ الْأُضْحِيَّةَ لِلّٰهِ تَعَالَى"
    //     0xb918ec: ldr             x0, [x0, #0x4c8]
    // 0xb918f0: stur            x3, [fp, #-0x10]
    // 0xb918f4: StoreField: r3->field_b = r0
    //     0xb918f4: stur            w0, [x3, #0xb]
    // 0xb918f8: r0 = "Nawaitul-udlḫiyyata lillâhi ta‘âlâ"
    //     0xb918f8: add             x0, PP, #0x35, lsl #12  ; [pp+0x354d0] "Nawaitul-udlḫiyyata lillâhi ta‘âlâ"
    //     0xb918fc: ldr             x0, [x0, #0x4d0]
    // 0xb91900: StoreField: r3->field_f = r0
    //     0xb91900: stur            w0, [x3, #0xf]
    // 0xb91904: r0 = "Aku niat berkurban karena Allah Yang Mahaluhur"
    //     0xb91904: add             x0, PP, #0x35, lsl #12  ; [pp+0x354d8] "Aku niat berkurban karena Allah Yang Mahaluhur"
    //     0xb91908: ldr             x0, [x0, #0x4d8]
    // 0xb9190c: StoreField: r3->field_13 = r0
    //     0xb9190c: stur            w0, [x3, #0x13]
    // 0xb91910: r0 = false
    //     0xb91910: add             x0, NULL, #0x30  ; false
    // 0xb91914: ArrayStore: r3[0] = r0  ; List_4
    //     0xb91914: stur            w0, [x3, #0x17]
    // 0xb91918: StoreField: r3->field_1b = r0
    //     0xb91918: stur            w0, [x3, #0x1b]
    // 0xb9191c: r0 = Instance_ReadingPref
    //     0xb9191c: add             x0, PP, #0x35, lsl #12  ; [pp+0x354b8] Obj!ReadingPref@e25c11
    //     0xb91920: ldr             x0, [x0, #0x4b8]
    // 0xb91924: StoreField: r3->field_1f = r0
    //     0xb91924: stur            w0, [x3, #0x1f]
    // 0xb91928: r0 = Instance_EdgeInsets
    //     0xb91928: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb9192c: StoreField: r3->field_23 = r0
    //     0xb9192c: stur            w0, [x3, #0x23]
    // 0xb91930: r1 = Null
    //     0xb91930: mov             x1, NULL
    // 0xb91934: r2 = 4
    //     0xb91934: movz            x2, #0x4
    // 0xb91938: r0 = AllocateArray()
    //     0xb91938: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb9193c: stur            x0, [fp, #-0x18]
    // 0xb91940: r16 = Instance_SizedBox
    //     0xb91940: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb91944: ldr             x16, [x16, #0xfe8]
    // 0xb91948: StoreField: r0->field_f = r16
    //     0xb91948: stur            w16, [x0, #0xf]
    // 0xb9194c: ldur            x1, [fp, #-0x10]
    // 0xb91950: StoreField: r0->field_13 = r1
    //     0xb91950: stur            w1, [x0, #0x13]
    // 0xb91954: r1 = <Widget>
    //     0xb91954: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91958: r0 = AllocateGrowableArray()
    //     0xb91958: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb9195c: mov             x1, x0
    // 0xb91960: ldur            x0, [fp, #-0x18]
    // 0xb91964: stur            x1, [fp, #-0x10]
    // 0xb91968: StoreField: r1->field_f = r0
    //     0xb91968: stur            w0, [x1, #0xf]
    // 0xb9196c: r0 = 4
    //     0xb9196c: movz            x0, #0x4
    // 0xb91970: StoreField: r1->field_b = r0
    //     0xb91970: stur            w0, [x1, #0xb]
    // 0xb91974: r0 = NSection()
    //     0xb91974: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb91978: r1 = "Niat Qurban"
    //     0xb91978: add             x1, PP, #0x35, lsl #12  ; [pp+0x354e0] "Niat Qurban"
    //     0xb9197c: ldr             x1, [x1, #0x4e0]
    // 0xb91980: StoreField: r0->field_b = r1
    //     0xb91980: stur            w1, [x0, #0xb]
    // 0xb91984: ldur            x1, [fp, #-0x10]
    // 0xb91988: StoreField: r0->field_f = r1
    //     0xb91988: stur            w1, [x0, #0xf]
    // 0xb9198c: ldur            x1, [fp, #-8]
    // 0xb91990: StoreField: r0->field_1f = r1
    //     0xb91990: stur            w1, [x0, #0x1f]
    // 0xb91994: r1 = true
    //     0xb91994: add             x1, NULL, #0x20  ; true
    // 0xb91998: StoreField: r0->field_27 = r1
    //     0xb91998: stur            w1, [x0, #0x27]
    // 0xb9199c: StoreField: r0->field_2b = r1
    //     0xb9199c: stur            w1, [x0, #0x2b]
    // 0xb919a0: LeaveFrame
    //     0xb919a0: mov             SP, fp
    //     0xb919a4: ldp             fp, lr, [SP], #0x10
    // 0xb919a8: ret
    //     0xb919a8: ret             
    // 0xb919ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb919ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb919b0: b               #0xb91894
    // 0xb919b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb919b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
