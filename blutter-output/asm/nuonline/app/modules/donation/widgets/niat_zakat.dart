// lib: , url: package:nuonline/app/modules/donation/widgets/niat_zakat.dart

// class id: 1050240, size: 0x8
class :: {
}

// class id: 5031, size: 0x14, field offset: 0xc
//   const constructor, 
class NiatZakat extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb919b8, size: 0x204
    // 0xb919b8: EnterFrame
    //     0xb919b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb919bc: mov             fp, SP
    // 0xb919c0: AllocStack(0x30)
    //     0xb919c0: sub             SP, SP, #0x30
    // 0xb919c4: SetupParameters(NiatZakat this /* r1 => r1, fp-0x8 */)
    //     0xb919c4: stur            x1, [fp, #-8]
    // 0xb919c8: CheckStackOverflow
    //     0xb919c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb919cc: cmp             SP, x16
    //     0xb919d0: b.ls            #0xb91bb0
    // 0xb919d4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb919d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb919d8: ldr             x0, [x0, #0x2670]
    //     0xb919dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb919e0: cmp             w0, w16
    //     0xb919e4: b.ne            #0xb919f0
    //     0xb919e8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb919ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb919f0: r0 = GetNavigation.textTheme()
    //     0xb919f0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb919f4: LoadField: r1 = r0->field_f
    //     0xb919f4: ldur            w1, [x0, #0xf]
    // 0xb919f8: DecompressPointer r1
    //     0xb919f8: add             x1, x1, HEAP, lsl #32
    // 0xb919fc: cmp             w1, NULL
    // 0xb91a00: b.eq            #0xb91bb8
    // 0xb91a04: r16 = 16.000000
    //     0xb91a04: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb91a08: ldr             x16, [x16, #0x80]
    // 0xb91a0c: str             x16, [SP]
    // 0xb91a10: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb91a10: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb91a14: ldr             x4, [x4, #0x88]
    // 0xb91a18: r0 = copyWith()
    //     0xb91a18: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91a1c: mov             x1, x0
    // 0xb91a20: ldur            x0, [fp, #-8]
    // 0xb91a24: stur            x1, [fp, #-0x20]
    // 0xb91a28: LoadField: r2 = r0->field_b
    //     0xb91a28: ldur            w2, [x0, #0xb]
    // 0xb91a2c: DecompressPointer r2
    //     0xb91a2c: add             x2, x2, HEAP, lsl #32
    // 0xb91a30: r16 = Instance_ZakatTypes
    //     0xb91a30: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xb91a34: ldr             x16, [x16, #0x620]
    // 0xb91a38: cmp             w2, w16
    // 0xb91a3c: b.ne            #0xb91a64
    // 0xb91a40: LoadField: r3 = r0->field_f
    //     0xb91a40: ldur            w3, [x0, #0xf]
    // 0xb91a44: DecompressPointer r3
    //     0xb91a44: add             x3, x3, HEAP, lsl #32
    // 0xb91a48: tbnz            w3, #4, #0xb91a58
    // 0xb91a4c: r3 = "نَوَيْتُ أَنْ أُخْرِجَ زَكَاةَ الْفِطْرِ عَنِّيْ وَعَنْ جَمِيْعِ مَا يَلْزَمُنِيْ نَفَقَاتُهُمْ شَرْعًــا فَرْضًا لِلّٰهِ تَعَالٰى"
    //     0xb91a4c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35470] "نَوَيْتُ أَنْ أُخْرِجَ زَكَاةَ الْفِطْرِ عَنِّيْ وَعَنْ جَمِيْعِ مَا يَلْزَمُنِيْ نَفَقَاتُهُمْ شَرْعًــا فَرْضًا لِلّٰهِ تَعَالٰى"
    //     0xb91a50: ldr             x3, [x3, #0x470]
    // 0xb91a54: b               #0xb91a6c
    // 0xb91a58: r3 = "نَوَيْتُ أَنْ أُخْرِجَ زَكَاةَ الْفِطْرِ عَنْ نَفْسِيْ فَرْضًــا لِلّٰهِ تَعَــالٰى"
    //     0xb91a58: add             x3, PP, #0x35, lsl #12  ; [pp+0x35478] "نَوَيْتُ أَنْ أُخْرِجَ زَكَاةَ الْفِطْرِ عَنْ نَفْسِيْ فَرْضًــا لِلّٰهِ تَعَــالٰى"
    //     0xb91a5c: ldr             x3, [x3, #0x478]
    // 0xb91a60: b               #0xb91a6c
    // 0xb91a64: r3 = " نَوَيْتُ اَنْ اُخْرِجَ زَكَاةَ الْمَالِ فَرْضًا لِلّٰه تَعَالٰى"
    //     0xb91a64: add             x3, PP, #0x35, lsl #12  ; [pp+0x35480] " نَوَيْتُ اَنْ اُخْرِجَ زَكَاةَ الْمَالِ فَرْضًا لِلّٰه تَعَالٰى"
    //     0xb91a68: ldr             x3, [x3, #0x480]
    // 0xb91a6c: stur            x3, [fp, #-0x18]
    // 0xb91a70: r16 = Instance_ZakatTypes
    //     0xb91a70: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xb91a74: ldr             x16, [x16, #0x620]
    // 0xb91a78: cmp             w2, w16
    // 0xb91a7c: b.ne            #0xb91aa4
    // 0xb91a80: LoadField: r4 = r0->field_f
    //     0xb91a80: ldur            w4, [x0, #0xf]
    // 0xb91a84: DecompressPointer r4
    //     0xb91a84: add             x4, x4, HEAP, lsl #32
    // 0xb91a88: tbnz            w4, #4, #0xb91a98
    // 0xb91a8c: r4 = "Aku niat mengeluarkan zakat fitrah untuk diriku dan seluruh orang yang nafkahnya menjadi tanggunganku, fardu karena Allah ta‘âlâ."
    //     0xb91a8c: add             x4, PP, #0x35, lsl #12  ; [pp+0x35488] "Aku niat mengeluarkan zakat fitrah untuk diriku dan seluruh orang yang nafkahnya menjadi tanggunganku, fardu karena Allah ta‘âlâ."
    //     0xb91a90: ldr             x4, [x4, #0x488]
    // 0xb91a94: b               #0xb91aac
    // 0xb91a98: r4 = "Aku niat mengeluarkan zakat fitrah untuk diriku sendiri, fardu karena Allah ta‘âlâ."
    //     0xb91a98: add             x4, PP, #0x35, lsl #12  ; [pp+0x35490] "Aku niat mengeluarkan zakat fitrah untuk diriku sendiri, fardu karena Allah ta‘âlâ."
    //     0xb91a9c: ldr             x4, [x4, #0x490]
    // 0xb91aa0: b               #0xb91aac
    // 0xb91aa4: r4 = "Aku niat mengeluarkan zakat hartaku, fardu karena Allah ta‘âlâ."
    //     0xb91aa4: add             x4, PP, #0x35, lsl #12  ; [pp+0x35498] "Aku niat mengeluarkan zakat hartaku, fardu karena Allah ta‘âlâ."
    //     0xb91aa8: ldr             x4, [x4, #0x498]
    // 0xb91aac: stur            x4, [fp, #-0x10]
    // 0xb91ab0: r16 = Instance_ZakatTypes
    //     0xb91ab0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xb91ab4: ldr             x16, [x16, #0x620]
    // 0xb91ab8: cmp             w2, w16
    // 0xb91abc: b.ne            #0xb91ae4
    // 0xb91ac0: LoadField: r2 = r0->field_f
    //     0xb91ac0: ldur            w2, [x0, #0xf]
    // 0xb91ac4: DecompressPointer r2
    //     0xb91ac4: add             x2, x2, HEAP, lsl #32
    // 0xb91ac8: tbnz            w2, #4, #0xb91ad8
    // 0xb91acc: r0 = "Nawaitu an ukhrija zakâtal fithri ‘annî wa ‘an jamî‘i mâ yalzamunî nafaqâtuhum syar‘an fardlan li-Llâhi ta‘âlâ"
    //     0xb91acc: add             x0, PP, #0x35, lsl #12  ; [pp+0x354a0] "Nawaitu an ukhrija zakâtal fithri ‘annî wa ‘an jamî‘i mâ yalzamunî nafaqâtuhum syar‘an fardlan li-Llâhi ta‘âlâ"
    //     0xb91ad0: ldr             x0, [x0, #0x4a0]
    // 0xb91ad4: b               #0xb91aec
    // 0xb91ad8: r0 = "Nawaitu an ukhrija zakâtal fithri ‘an nafsî fardlan li-Llâhi ta‘âlâ"
    //     0xb91ad8: add             x0, PP, #0x35, lsl #12  ; [pp+0x354a8] "Nawaitu an ukhrija zakâtal fithri ‘an nafsî fardlan li-Llâhi ta‘âlâ"
    //     0xb91adc: ldr             x0, [x0, #0x4a8]
    // 0xb91ae0: b               #0xb91aec
    // 0xb91ae4: r0 = "Nawaitu an ukhrija zakâtal mâli fardlan li-Llâhi ta‘âlâ"
    //     0xb91ae4: add             x0, PP, #0x35, lsl #12  ; [pp+0x354b0] "Nawaitu an ukhrija zakâtal mâli fardlan li-Llâhi ta‘âlâ"
    //     0xb91ae8: ldr             x0, [x0, #0x4b0]
    // 0xb91aec: stur            x0, [fp, #-8]
    // 0xb91af0: r0 = NDoaListTile()
    //     0xb91af0: bl              #0xb8a284  ; AllocateNDoaListTileStub -> NDoaListTile (size=0x28)
    // 0xb91af4: mov             x3, x0
    // 0xb91af8: ldur            x0, [fp, #-0x18]
    // 0xb91afc: stur            x3, [fp, #-0x28]
    // 0xb91b00: StoreField: r3->field_b = r0
    //     0xb91b00: stur            w0, [x3, #0xb]
    // 0xb91b04: ldur            x0, [fp, #-8]
    // 0xb91b08: StoreField: r3->field_f = r0
    //     0xb91b08: stur            w0, [x3, #0xf]
    // 0xb91b0c: ldur            x0, [fp, #-0x10]
    // 0xb91b10: StoreField: r3->field_13 = r0
    //     0xb91b10: stur            w0, [x3, #0x13]
    // 0xb91b14: r0 = false
    //     0xb91b14: add             x0, NULL, #0x30  ; false
    // 0xb91b18: ArrayStore: r3[0] = r0  ; List_4
    //     0xb91b18: stur            w0, [x3, #0x17]
    // 0xb91b1c: StoreField: r3->field_1b = r0
    //     0xb91b1c: stur            w0, [x3, #0x1b]
    // 0xb91b20: r0 = Instance_ReadingPref
    //     0xb91b20: add             x0, PP, #0x35, lsl #12  ; [pp+0x354b8] Obj!ReadingPref@e25c11
    //     0xb91b24: ldr             x0, [x0, #0x4b8]
    // 0xb91b28: StoreField: r3->field_1f = r0
    //     0xb91b28: stur            w0, [x3, #0x1f]
    // 0xb91b2c: r0 = Instance_EdgeInsets
    //     0xb91b2c: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb91b30: StoreField: r3->field_23 = r0
    //     0xb91b30: stur            w0, [x3, #0x23]
    // 0xb91b34: r1 = Null
    //     0xb91b34: mov             x1, NULL
    // 0xb91b38: r2 = 4
    //     0xb91b38: movz            x2, #0x4
    // 0xb91b3c: r0 = AllocateArray()
    //     0xb91b3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91b40: stur            x0, [fp, #-8]
    // 0xb91b44: r16 = Instance_SizedBox
    //     0xb91b44: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb91b48: ldr             x16, [x16, #0xfe8]
    // 0xb91b4c: StoreField: r0->field_f = r16
    //     0xb91b4c: stur            w16, [x0, #0xf]
    // 0xb91b50: ldur            x1, [fp, #-0x28]
    // 0xb91b54: StoreField: r0->field_13 = r1
    //     0xb91b54: stur            w1, [x0, #0x13]
    // 0xb91b58: r1 = <Widget>
    //     0xb91b58: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91b5c: r0 = AllocateGrowableArray()
    //     0xb91b5c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91b60: mov             x1, x0
    // 0xb91b64: ldur            x0, [fp, #-8]
    // 0xb91b68: stur            x1, [fp, #-0x10]
    // 0xb91b6c: StoreField: r1->field_f = r0
    //     0xb91b6c: stur            w0, [x1, #0xf]
    // 0xb91b70: r0 = 4
    //     0xb91b70: movz            x0, #0x4
    // 0xb91b74: StoreField: r1->field_b = r0
    //     0xb91b74: stur            w0, [x1, #0xb]
    // 0xb91b78: r0 = NSection()
    //     0xb91b78: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb91b7c: r1 = "Niat Zakat"
    //     0xb91b7c: add             x1, PP, #0x35, lsl #12  ; [pp+0x354c0] "Niat Zakat"
    //     0xb91b80: ldr             x1, [x1, #0x4c0]
    // 0xb91b84: StoreField: r0->field_b = r1
    //     0xb91b84: stur            w1, [x0, #0xb]
    // 0xb91b88: ldur            x1, [fp, #-0x10]
    // 0xb91b8c: StoreField: r0->field_f = r1
    //     0xb91b8c: stur            w1, [x0, #0xf]
    // 0xb91b90: ldur            x1, [fp, #-0x20]
    // 0xb91b94: StoreField: r0->field_1f = r1
    //     0xb91b94: stur            w1, [x0, #0x1f]
    // 0xb91b98: r1 = true
    //     0xb91b98: add             x1, NULL, #0x20  ; true
    // 0xb91b9c: StoreField: r0->field_27 = r1
    //     0xb91b9c: stur            w1, [x0, #0x27]
    // 0xb91ba0: StoreField: r0->field_2b = r1
    //     0xb91ba0: stur            w1, [x0, #0x2b]
    // 0xb91ba4: LeaveFrame
    //     0xb91ba4: mov             SP, fp
    //     0xb91ba8: ldp             fp, lr, [SP], #0x10
    // 0xb91bac: ret
    //     0xb91bac: ret             
    // 0xb91bb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91bb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb91bb4: b               #0xb919d4
    // 0xb91bb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb91bb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
