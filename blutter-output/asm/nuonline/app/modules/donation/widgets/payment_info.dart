// lib: , url: package:nuonline/app/modules/donation/widgets/payment_info.dart

// class id: 1050241, size: 0x8
class :: {
}

// class id: 5030, size: 0x18, field offset: 0xc
//   const constructor, 
class PaymentInfo extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb91bbc, size: 0x28c
    // 0xb91bbc: EnterFrame
    //     0xb91bbc: stp             fp, lr, [SP, #-0x10]!
    //     0xb91bc0: mov             fp, SP
    // 0xb91bc4: AllocStack(0x48)
    //     0xb91bc4: sub             SP, SP, #0x48
    // 0xb91bc8: SetupParameters(PaymentInfo this /* r1 => r0, fp-0x8 */)
    //     0xb91bc8: mov             x0, x1
    //     0xb91bcc: stur            x1, [fp, #-8]
    // 0xb91bd0: CheckStackOverflow
    //     0xb91bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91bd4: cmp             SP, x16
    //     0xb91bd8: b.ls            #0xb91e38
    // 0xb91bdc: r1 = _ConstMap len:3
    //     0xb91bdc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb91be0: ldr             x1, [x1, #0xbe8]
    // 0xb91be4: r2 = 2
    //     0xb91be4: movz            x2, #0x2
    // 0xb91be8: r0 = []()
    //     0xb91be8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb91bec: r16 = <Color?>
    //     0xb91bec: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb91bf0: ldr             x16, [x16, #0x98]
    // 0xb91bf4: stp             x0, x16, [SP, #8]
    // 0xb91bf8: r16 = Instance_MaterialColor
    //     0xb91bf8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb91bfc: ldr             x16, [x16, #0xe38]
    // 0xb91c00: str             x16, [SP]
    // 0xb91c04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb91c04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb91c08: r0 = mode()
    //     0xb91c08: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb91c0c: stur            x0, [fp, #-0x10]
    // 0xb91c10: r0 = Radius()
    //     0xb91c10: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb91c14: d0 = 8.000000
    //     0xb91c14: fmov            d0, #8.00000000
    // 0xb91c18: stur            x0, [fp, #-0x18]
    // 0xb91c1c: StoreField: r0->field_7 = d0
    //     0xb91c1c: stur            d0, [x0, #7]
    // 0xb91c20: StoreField: r0->field_f = d0
    //     0xb91c20: stur            d0, [x0, #0xf]
    // 0xb91c24: r0 = BorderRadius()
    //     0xb91c24: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb91c28: mov             x1, x0
    // 0xb91c2c: ldur            x0, [fp, #-0x18]
    // 0xb91c30: stur            x1, [fp, #-0x20]
    // 0xb91c34: StoreField: r1->field_7 = r0
    //     0xb91c34: stur            w0, [x1, #7]
    // 0xb91c38: StoreField: r1->field_b = r0
    //     0xb91c38: stur            w0, [x1, #0xb]
    // 0xb91c3c: StoreField: r1->field_f = r0
    //     0xb91c3c: stur            w0, [x1, #0xf]
    // 0xb91c40: StoreField: r1->field_13 = r0
    //     0xb91c40: stur            w0, [x1, #0x13]
    // 0xb91c44: r0 = BoxDecoration()
    //     0xb91c44: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb91c48: mov             x1, x0
    // 0xb91c4c: ldur            x0, [fp, #-0x10]
    // 0xb91c50: stur            x1, [fp, #-0x18]
    // 0xb91c54: StoreField: r1->field_7 = r0
    //     0xb91c54: stur            w0, [x1, #7]
    // 0xb91c58: ldur            x0, [fp, #-0x20]
    // 0xb91c5c: StoreField: r1->field_13 = r0
    //     0xb91c5c: stur            w0, [x1, #0x13]
    // 0xb91c60: r0 = Instance_BoxShape
    //     0xb91c60: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb91c64: ldr             x0, [x0, #0xca8]
    // 0xb91c68: StoreField: r1->field_23 = r0
    //     0xb91c68: stur            w0, [x1, #0x23]
    // 0xb91c6c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb91c6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb91c70: ldr             x0, [x0, #0x2670]
    //     0xb91c74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb91c78: cmp             w0, w16
    //     0xb91c7c: b.ne            #0xb91c88
    //     0xb91c80: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb91c84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb91c88: r0 = GetNavigation.textTheme()
    //     0xb91c88: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb91c8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb91c8c: ldur            w1, [x0, #0x17]
    // 0xb91c90: DecompressPointer r1
    //     0xb91c90: add             x1, x1, HEAP, lsl #32
    // 0xb91c94: cmp             w1, NULL
    // 0xb91c98: b.eq            #0xb91e40
    // 0xb91c9c: r16 = Instance_FontWeight
    //     0xb91c9c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb91ca0: ldr             x16, [x16, #0xe20]
    // 0xb91ca4: str             x16, [SP]
    // 0xb91ca8: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb91ca8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb91cac: ldr             x4, [x4, #0xfe0]
    // 0xb91cb0: r0 = copyWith()
    //     0xb91cb0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91cb4: stur            x0, [fp, #-0x10]
    // 0xb91cb8: r0 = Text()
    //     0xb91cb8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb91cbc: mov             x2, x0
    // 0xb91cc0: r0 = "Nominal"
    //     0xb91cc0: add             x0, PP, #0x30, lsl #12  ; [pp+0x30500] "Nominal"
    //     0xb91cc4: ldr             x0, [x0, #0x500]
    // 0xb91cc8: stur            x2, [fp, #-0x20]
    // 0xb91ccc: StoreField: r2->field_b = r0
    //     0xb91ccc: stur            w0, [x2, #0xb]
    // 0xb91cd0: ldur            x0, [fp, #-0x10]
    // 0xb91cd4: StoreField: r2->field_13 = r0
    //     0xb91cd4: stur            w0, [x2, #0x13]
    // 0xb91cd8: ldur            x0, [fp, #-8]
    // 0xb91cdc: LoadField: r1 = r0->field_f
    //     0xb91cdc: ldur            x1, [x0, #0xf]
    // 0xb91ce0: r0 = IntExtension.idr()
    //     0xb91ce0: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb91ce4: stur            x0, [fp, #-8]
    // 0xb91ce8: r0 = GetNavigation.textTheme()
    //     0xb91ce8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb91cec: LoadField: r1 = r0->field_f
    //     0xb91cec: ldur            w1, [x0, #0xf]
    // 0xb91cf0: DecompressPointer r1
    //     0xb91cf0: add             x1, x1, HEAP, lsl #32
    // 0xb91cf4: stur            x1, [fp, #-0x10]
    // 0xb91cf8: cmp             w1, NULL
    // 0xb91cfc: b.eq            #0xb91e44
    // 0xb91d00: r0 = GetNavigation.theme()
    //     0xb91d00: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb91d04: LoadField: r1 = r0->field_3f
    //     0xb91d04: ldur            w1, [x0, #0x3f]
    // 0xb91d08: DecompressPointer r1
    //     0xb91d08: add             x1, x1, HEAP, lsl #32
    // 0xb91d0c: LoadField: r0 = r1->field_2b
    //     0xb91d0c: ldur            w0, [x1, #0x2b]
    // 0xb91d10: DecompressPointer r0
    //     0xb91d10: add             x0, x0, HEAP, lsl #32
    // 0xb91d14: r16 = 24.000000
    //     0xb91d14: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xb91d18: ldr             x16, [x16, #0x368]
    // 0xb91d1c: stp             x0, x16, [SP]
    // 0xb91d20: ldur            x1, [fp, #-0x10]
    // 0xb91d24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb91d24: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb91d28: ldr             x4, [x4, #0xaa0]
    // 0xb91d2c: r0 = copyWith()
    //     0xb91d2c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb91d30: stur            x0, [fp, #-0x10]
    // 0xb91d34: r0 = Text()
    //     0xb91d34: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb91d38: mov             x3, x0
    // 0xb91d3c: ldur            x0, [fp, #-8]
    // 0xb91d40: stur            x3, [fp, #-0x28]
    // 0xb91d44: StoreField: r3->field_b = r0
    //     0xb91d44: stur            w0, [x3, #0xb]
    // 0xb91d48: ldur            x0, [fp, #-0x10]
    // 0xb91d4c: StoreField: r3->field_13 = r0
    //     0xb91d4c: stur            w0, [x3, #0x13]
    // 0xb91d50: r1 = Null
    //     0xb91d50: mov             x1, NULL
    // 0xb91d54: r2 = 4
    //     0xb91d54: movz            x2, #0x4
    // 0xb91d58: r0 = AllocateArray()
    //     0xb91d58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91d5c: mov             x2, x0
    // 0xb91d60: ldur            x0, [fp, #-0x20]
    // 0xb91d64: stur            x2, [fp, #-8]
    // 0xb91d68: StoreField: r2->field_f = r0
    //     0xb91d68: stur            w0, [x2, #0xf]
    // 0xb91d6c: ldur            x0, [fp, #-0x28]
    // 0xb91d70: StoreField: r2->field_13 = r0
    //     0xb91d70: stur            w0, [x2, #0x13]
    // 0xb91d74: r1 = <Widget>
    //     0xb91d74: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91d78: r0 = AllocateGrowableArray()
    //     0xb91d78: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91d7c: mov             x1, x0
    // 0xb91d80: ldur            x0, [fp, #-8]
    // 0xb91d84: stur            x1, [fp, #-0x10]
    // 0xb91d88: StoreField: r1->field_f = r0
    //     0xb91d88: stur            w0, [x1, #0xf]
    // 0xb91d8c: r0 = 4
    //     0xb91d8c: movz            x0, #0x4
    // 0xb91d90: StoreField: r1->field_b = r0
    //     0xb91d90: stur            w0, [x1, #0xb]
    // 0xb91d94: r0 = Column()
    //     0xb91d94: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb91d98: mov             x1, x0
    // 0xb91d9c: r0 = Instance_Axis
    //     0xb91d9c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb91da0: stur            x1, [fp, #-8]
    // 0xb91da4: StoreField: r1->field_f = r0
    //     0xb91da4: stur            w0, [x1, #0xf]
    // 0xb91da8: r0 = Instance_MainAxisAlignment
    //     0xb91da8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb91dac: ldr             x0, [x0, #0x730]
    // 0xb91db0: StoreField: r1->field_13 = r0
    //     0xb91db0: stur            w0, [x1, #0x13]
    // 0xb91db4: r0 = Instance_MainAxisSize
    //     0xb91db4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb91db8: ldr             x0, [x0, #0x738]
    // 0xb91dbc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb91dbc: stur            w0, [x1, #0x17]
    // 0xb91dc0: r0 = Instance_CrossAxisAlignment
    //     0xb91dc0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb91dc4: ldr             x0, [x0, #0x740]
    // 0xb91dc8: StoreField: r1->field_1b = r0
    //     0xb91dc8: stur            w0, [x1, #0x1b]
    // 0xb91dcc: r0 = Instance_VerticalDirection
    //     0xb91dcc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb91dd0: ldr             x0, [x0, #0x748]
    // 0xb91dd4: StoreField: r1->field_23 = r0
    //     0xb91dd4: stur            w0, [x1, #0x23]
    // 0xb91dd8: r0 = Instance_Clip
    //     0xb91dd8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb91ddc: ldr             x0, [x0, #0x750]
    // 0xb91de0: StoreField: r1->field_2b = r0
    //     0xb91de0: stur            w0, [x1, #0x2b]
    // 0xb91de4: StoreField: r1->field_2f = rZR
    //     0xb91de4: stur            xzr, [x1, #0x2f]
    // 0xb91de8: ldur            x0, [fp, #-0x10]
    // 0xb91dec: StoreField: r1->field_b = r0
    //     0xb91dec: stur            w0, [x1, #0xb]
    // 0xb91df0: r0 = Container()
    //     0xb91df0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb91df4: stur            x0, [fp, #-0x10]
    // 0xb91df8: r16 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb91df8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb91dfc: ldr             x16, [x16, #0xc58]
    // 0xb91e00: r30 = Instance_EdgeInsets
    //     0xb91e00: add             lr, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xb91e04: ldr             lr, [lr, #0xde8]
    // 0xb91e08: stp             lr, x16, [SP, #0x10]
    // 0xb91e0c: ldur            x16, [fp, #-0x18]
    // 0xb91e10: ldur            lr, [fp, #-8]
    // 0xb91e14: stp             lr, x16, [SP]
    // 0xb91e18: mov             x1, x0
    // 0xb91e1c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb91e1c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f650] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb91e20: ldr             x4, [x4, #0x650]
    // 0xb91e24: r0 = Container()
    //     0xb91e24: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb91e28: ldur            x0, [fp, #-0x10]
    // 0xb91e2c: LeaveFrame
    //     0xb91e2c: mov             SP, fp
    //     0xb91e30: ldp             fp, lr, [SP], #0x10
    // 0xb91e34: ret
    //     0xb91e34: ret             
    // 0xb91e38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91e38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb91e3c: b               #0xb91bdc
    // 0xb91e40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb91e40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb91e44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb91e44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
