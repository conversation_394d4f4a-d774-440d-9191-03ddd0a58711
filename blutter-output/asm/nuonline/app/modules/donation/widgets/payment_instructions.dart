// lib: , url: package:nuonline/app/modules/donation/widgets/payment_instructions.dart

// class id: 1050242, size: 0x8
class :: {
}

// class id: 5028, size: 0x18, field offset: 0xc
//   const constructor, 
class Steps extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb922c8, size: 0x810
    // 0xb922c8: EnterFrame
    //     0xb922c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb922cc: mov             fp, SP
    // 0xb922d0: AllocStack(0x68)
    //     0xb922d0: sub             SP, SP, #0x68
    // 0xb922d4: SetupParameters(Steps this /* r1 => r0, fp-0x8 */)
    //     0xb922d4: mov             x0, x1
    //     0xb922d8: stur            x1, [fp, #-8]
    // 0xb922dc: CheckStackOverflow
    //     0xb922dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb922e0: cmp             SP, x16
    //     0xb922e4: b.ls            #0xb92ab4
    // 0xb922e8: r1 = <Widget>
    //     0xb922e8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb922ec: r2 = 0
    //     0xb922ec: movz            x2, #0
    // 0xb922f0: r0 = _GrowableList()
    //     0xb922f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb922f4: mov             x2, x0
    // 0xb922f8: ldur            x0, [fp, #-8]
    // 0xb922fc: stur            x2, [fp, #-0x18]
    // 0xb92300: LoadField: r1 = r0->field_f
    //     0xb92300: ldur            w1, [x0, #0xf]
    // 0xb92304: DecompressPointer r1
    //     0xb92304: add             x1, x1, HEAP, lsl #32
    // 0xb92308: tbnz            w1, #4, #0xb92368
    // 0xb9230c: LoadField: r1 = r2->field_b
    //     0xb9230c: ldur            w1, [x2, #0xb]
    // 0xb92310: LoadField: r3 = r2->field_f
    //     0xb92310: ldur            w3, [x2, #0xf]
    // 0xb92314: DecompressPointer r3
    //     0xb92314: add             x3, x3, HEAP, lsl #32
    // 0xb92318: LoadField: r4 = r3->field_b
    //     0xb92318: ldur            w4, [x3, #0xb]
    // 0xb9231c: r3 = LoadInt32Instr(r1)
    //     0xb9231c: sbfx            x3, x1, #1, #0x1f
    // 0xb92320: stur            x3, [fp, #-0x10]
    // 0xb92324: r1 = LoadInt32Instr(r4)
    //     0xb92324: sbfx            x1, x4, #1, #0x1f
    // 0xb92328: cmp             x3, x1
    // 0xb9232c: b.ne            #0xb92338
    // 0xb92330: mov             x1, x2
    // 0xb92334: r0 = _growToNextCapacity()
    //     0xb92334: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb92338: ldur            x3, [fp, #-0x18]
    // 0xb9233c: ldur            x0, [fp, #-0x10]
    // 0xb92340: add             x1, x0, #1
    // 0xb92344: lsl             x2, x1, #1
    // 0xb92348: StoreField: r3->field_b = r2
    //     0xb92348: stur            w2, [x3, #0xb]
    // 0xb9234c: LoadField: r1 = r3->field_f
    //     0xb9234c: ldur            w1, [x3, #0xf]
    // 0xb92350: DecompressPointer r1
    //     0xb92350: add             x1, x1, HEAP, lsl #32
    // 0xb92354: add             x2, x1, x0, lsl #2
    // 0xb92358: r16 = Instance_Padding
    //     0xb92358: add             x16, PP, #0x40, lsl #12  ; [pp+0x40330] Obj!Padding@e1e981
    //     0xb9235c: ldr             x16, [x16, #0x330]
    // 0xb92360: StoreField: r2->field_f = r16
    //     0xb92360: stur            w16, [x2, #0xf]
    // 0xb92364: b               #0xb9236c
    // 0xb92368: mov             x3, x2
    // 0xb9236c: ldur            x4, [fp, #-8]
    // 0xb92370: LoadField: r5 = r4->field_b
    //     0xb92370: ldur            w5, [x4, #0xb]
    // 0xb92374: DecompressPointer r5
    //     0xb92374: add             x5, x5, HEAP, lsl #32
    // 0xb92378: stur            x5, [fp, #-0x28]
    // 0xb9237c: r6 = 0
    //     0xb9237c: movz            x6, #0
    // 0xb92380: stur            x6, [fp, #-0x10]
    // 0xb92384: CheckStackOverflow
    //     0xb92384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb92388: cmp             SP, x16
    //     0xb9238c: b.ls            #0xb92abc
    // 0xb92390: LoadField: r0 = r5->field_b
    //     0xb92390: ldur            w0, [x5, #0xb]
    // 0xb92394: r1 = LoadInt32Instr(r0)
    //     0xb92394: sbfx            x1, x0, #1, #0x1f
    // 0xb92398: cmp             x6, x1
    // 0xb9239c: b.ge            #0xb92a04
    // 0xb923a0: LoadField: r0 = r5->field_f
    //     0xb923a0: ldur            w0, [x5, #0xf]
    // 0xb923a4: DecompressPointer r0
    //     0xb923a4: add             x0, x0, HEAP, lsl #32
    // 0xb923a8: ArrayLoad: r7 = r0[r6]  ; Unknown_4
    //     0xb923a8: add             x16, x0, x6, lsl #2
    //     0xb923ac: ldur            w7, [x16, #0xf]
    // 0xb923b0: DecompressPointer r7
    //     0xb923b0: add             x7, x7, HEAP, lsl #32
    // 0xb923b4: stur            x7, [fp, #-0x20]
    // 0xb923b8: r0 = LoadClassIdInstr(r7)
    //     0xb923b8: ldur            x0, [x7, #-1]
    //     0xb923bc: ubfx            x0, x0, #0xc, #0x14
    // 0xb923c0: mov             x1, x7
    // 0xb923c4: r2 = "#end_title#"
    //     0xb923c4: add             x2, PP, #0x40, lsl #12  ; [pp+0x40338] "#end_title#"
    //     0xb923c8: ldr             x2, [x2, #0x338]
    // 0xb923cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb923cc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb923d0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb923d0: sub             lr, x0, #0xffc
    //     0xb923d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb923d8: blr             lr
    // 0xb923dc: tbnz            w0, #4, #0xb924a8
    // 0xb923e0: ldur            x3, [fp, #-0x20]
    // 0xb923e4: r0 = LoadClassIdInstr(r3)
    //     0xb923e4: ldur            x0, [x3, #-1]
    //     0xb923e8: ubfx            x0, x0, #0xc, #0x14
    // 0xb923ec: mov             x1, x3
    // 0xb923f0: r2 = "#end_title#"
    //     0xb923f0: add             x2, PP, #0x40, lsl #12  ; [pp+0x40338] "#end_title#"
    //     0xb923f4: ldr             x2, [x2, #0x338]
    // 0xb923f8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb923f8: sub             lr, x0, #1, lsl #12
    //     0xb923fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb92400: blr             lr
    // 0xb92404: mov             x2, x0
    // 0xb92408: LoadField: r0 = r2->field_b
    //     0xb92408: ldur            w0, [x2, #0xb]
    // 0xb9240c: r1 = LoadInt32Instr(r0)
    //     0xb9240c: sbfx            x1, x0, #1, #0x1f
    // 0xb92410: cmp             x1, #0
    // 0xb92414: b.le            #0xb92aa8
    // 0xb92418: ldur            x3, [fp, #-0x20]
    // 0xb9241c: mov             x0, x1
    // 0xb92420: r1 = 0
    //     0xb92420: movz            x1, #0
    // 0xb92424: cmp             x1, x0
    // 0xb92428: b.hs            #0xb92ac4
    // 0xb9242c: LoadField: r0 = r2->field_f
    //     0xb9242c: ldur            w0, [x2, #0xf]
    // 0xb92430: DecompressPointer r0
    //     0xb92430: add             x0, x0, HEAP, lsl #32
    // 0xb92434: LoadField: r4 = r0->field_f
    //     0xb92434: ldur            w4, [x0, #0xf]
    // 0xb92438: DecompressPointer r4
    //     0xb92438: add             x4, x4, HEAP, lsl #32
    // 0xb9243c: stur            x4, [fp, #-0x30]
    // 0xb92440: r0 = LoadClassIdInstr(r3)
    //     0xb92440: ldur            x0, [x3, #-1]
    //     0xb92444: ubfx            x0, x0, #0xc, #0x14
    // 0xb92448: mov             x1, x3
    // 0xb9244c: r2 = "#end_title#"
    //     0xb9244c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40338] "#end_title#"
    //     0xb92450: ldr             x2, [x2, #0x338]
    // 0xb92454: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb92454: sub             lr, x0, #1, lsl #12
    //     0xb92458: ldr             lr, [x21, lr, lsl #3]
    //     0xb9245c: blr             lr
    // 0xb92460: mov             x2, x0
    // 0xb92464: LoadField: r0 = r2->field_b
    //     0xb92464: ldur            w0, [x2, #0xb]
    // 0xb92468: r1 = LoadInt32Instr(r0)
    //     0xb92468: sbfx            x1, x0, #1, #0x1f
    // 0xb9246c: cmp             x1, #0
    // 0xb92470: b.le            #0xb92a9c
    // 0xb92474: sub             x3, x1, #1
    // 0xb92478: mov             x0, x1
    // 0xb9247c: mov             x1, x3
    // 0xb92480: cmp             x1, x0
    // 0xb92484: b.hs            #0xb92ac8
    // 0xb92488: LoadField: r0 = r2->field_f
    //     0xb92488: ldur            w0, [x2, #0xf]
    // 0xb9248c: DecompressPointer r0
    //     0xb9248c: add             x0, x0, HEAP, lsl #32
    // 0xb92490: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb92490: add             x16, x0, x3, lsl #2
    //     0xb92494: ldur            w1, [x16, #0xf]
    // 0xb92498: DecompressPointer r1
    //     0xb92498: add             x1, x1, HEAP, lsl #32
    // 0xb9249c: ldur            x4, [fp, #-0x30]
    // 0xb924a0: mov             x3, x1
    // 0xb924a4: b               #0xb924b0
    // 0xb924a8: ldur            x3, [fp, #-0x20]
    // 0xb924ac: r4 = Null
    //     0xb924ac: mov             x4, NULL
    // 0xb924b0: ldur            x0, [fp, #-0x10]
    // 0xb924b4: stur            x4, [fp, #-0x30]
    // 0xb924b8: stur            x3, [fp, #-0x40]
    // 0xb924bc: add             x6, x0, #1
    // 0xb924c0: stur            x6, [fp, #-0x38]
    // 0xb924c4: lsl             x0, x6, #1
    // 0xb924c8: stur            x0, [fp, #-0x20]
    // 0xb924cc: r1 = Null
    //     0xb924cc: mov             x1, NULL
    // 0xb924d0: r2 = 4
    //     0xb924d0: movz            x2, #0x4
    // 0xb924d4: r0 = AllocateArray()
    //     0xb924d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb924d8: mov             x1, x0
    // 0xb924dc: ldur            x0, [fp, #-0x20]
    // 0xb924e0: StoreField: r1->field_f = r0
    //     0xb924e0: stur            w0, [x1, #0xf]
    // 0xb924e4: r16 = "."
    //     0xb924e4: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xb924e8: StoreField: r1->field_13 = r16
    //     0xb924e8: stur            w16, [x1, #0x13]
    // 0xb924ec: str             x1, [SP]
    // 0xb924f0: r0 = _interpolate()
    //     0xb924f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb924f4: stur            x0, [fp, #-0x20]
    // 0xb924f8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb924f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb924fc: ldr             x0, [x0, #0x2670]
    //     0xb92500: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb92504: cmp             w0, w16
    //     0xb92508: b.ne            #0xb92514
    //     0xb9250c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb92510: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb92514: r0 = GetNavigation.theme()
    //     0xb92514: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb92518: LoadField: r1 = r0->field_8f
    //     0xb92518: ldur            w1, [x0, #0x8f]
    // 0xb9251c: DecompressPointer r1
    //     0xb9251c: add             x1, x1, HEAP, lsl #32
    // 0xb92520: LoadField: r0 = r1->field_23
    //     0xb92520: ldur            w0, [x1, #0x23]
    // 0xb92524: DecompressPointer r0
    //     0xb92524: add             x0, x0, HEAP, lsl #32
    // 0xb92528: stur            x0, [fp, #-0x48]
    // 0xb9252c: cmp             w0, NULL
    // 0xb92530: b.eq            #0xb92acc
    // 0xb92534: r1 = _ConstMap len:3
    //     0xb92534: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb92538: ldr             x1, [x1, #0xbe8]
    // 0xb9253c: r2 = 6
    //     0xb9253c: movz            x2, #0x6
    // 0xb92540: r0 = []()
    //     0xb92540: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92544: stur            x0, [fp, #-0x50]
    // 0xb92548: r0 = GetNavigation.isDarkMode()
    //     0xb92548: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb9254c: tbnz            w0, #4, #0xb92558
    // 0xb92550: r1 = Instance_Color
    //     0xb92550: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb92554: b               #0xb9255c
    // 0xb92558: ldur            x1, [fp, #-0x50]
    // 0xb9255c: ldur            x2, [fp, #-0x30]
    // 0xb92560: ldur            x0, [fp, #-0x20]
    // 0xb92564: str             x1, [SP]
    // 0xb92568: ldur            x1, [fp, #-0x48]
    // 0xb9256c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb9256c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb92570: ldr             x4, [x4, #0x228]
    // 0xb92574: r0 = copyWith()
    //     0xb92574: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92578: stur            x0, [fp, #-0x48]
    // 0xb9257c: r0 = Text()
    //     0xb9257c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92580: mov             x1, x0
    // 0xb92584: ldur            x0, [fp, #-0x20]
    // 0xb92588: stur            x1, [fp, #-0x50]
    // 0xb9258c: StoreField: r1->field_b = r0
    //     0xb9258c: stur            w0, [x1, #0xb]
    // 0xb92590: ldur            x0, [fp, #-0x48]
    // 0xb92594: StoreField: r1->field_13 = r0
    //     0xb92594: stur            w0, [x1, #0x13]
    // 0xb92598: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb92598: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9259c: ldr             x0, [x0]
    //     0xb925a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb925a4: cmp             w0, w16
    //     0xb925a8: b.ne            #0xb925b4
    //     0xb925ac: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb925b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb925b4: r1 = <Widget>
    //     0xb925b4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb925b8: stur            x0, [fp, #-0x20]
    // 0xb925bc: r0 = AllocateGrowableArray()
    //     0xb925bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb925c0: mov             x1, x0
    // 0xb925c4: ldur            x0, [fp, #-0x20]
    // 0xb925c8: stur            x1, [fp, #-0x48]
    // 0xb925cc: StoreField: r1->field_f = r0
    //     0xb925cc: stur            w0, [x1, #0xf]
    // 0xb925d0: StoreField: r1->field_b = rZR
    //     0xb925d0: stur            wzr, [x1, #0xb]
    // 0xb925d4: ldur            x0, [fp, #-0x30]
    // 0xb925d8: cmp             w0, NULL
    // 0xb925dc: b.ne            #0xb925e8
    // 0xb925e0: r2 = Null
    //     0xb925e0: mov             x2, NULL
    // 0xb925e4: b               #0xb92600
    // 0xb925e8: LoadField: r2 = r0->field_7
    //     0xb925e8: ldur            w2, [x0, #7]
    // 0xb925ec: cbnz            w2, #0xb925f8
    // 0xb925f0: r3 = false
    //     0xb925f0: add             x3, NULL, #0x30  ; false
    // 0xb925f4: b               #0xb925fc
    // 0xb925f8: r3 = true
    //     0xb925f8: add             x3, NULL, #0x20  ; true
    // 0xb925fc: mov             x2, x3
    // 0xb92600: cmp             w2, NULL
    // 0xb92604: b.ne            #0xb92610
    // 0xb92608: mov             x2, x1
    // 0xb9260c: b               #0xb92730
    // 0xb92610: tbnz            w2, #4, #0xb9272c
    // 0xb92614: cmp             w0, NULL
    // 0xb92618: b.ne            #0xb92620
    // 0xb9261c: r0 = ""
    //     0xb9261c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb92620: stur            x0, [fp, #-0x20]
    // 0xb92624: r0 = GetNavigation.theme()
    //     0xb92624: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb92628: LoadField: r1 = r0->field_8f
    //     0xb92628: ldur            w1, [x0, #0x8f]
    // 0xb9262c: DecompressPointer r1
    //     0xb9262c: add             x1, x1, HEAP, lsl #32
    // 0xb92630: LoadField: r0 = r1->field_23
    //     0xb92630: ldur            w0, [x1, #0x23]
    // 0xb92634: DecompressPointer r0
    //     0xb92634: add             x0, x0, HEAP, lsl #32
    // 0xb92638: stur            x0, [fp, #-0x30]
    // 0xb9263c: cmp             w0, NULL
    // 0xb92640: b.eq            #0xb92ad0
    // 0xb92644: r1 = _ConstMap len:3
    //     0xb92644: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb92648: ldr             x1, [x1, #0xbe8]
    // 0xb9264c: r2 = 6
    //     0xb9264c: movz            x2, #0x6
    // 0xb92650: r0 = []()
    //     0xb92650: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92654: stur            x0, [fp, #-0x58]
    // 0xb92658: r0 = GetNavigation.isDarkMode()
    //     0xb92658: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb9265c: tbnz            w0, #4, #0xb92668
    // 0xb92660: r1 = Instance_Color
    //     0xb92660: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb92664: b               #0xb9266c
    // 0xb92668: ldur            x1, [fp, #-0x58]
    // 0xb9266c: ldur            x2, [fp, #-0x20]
    // 0xb92670: ldur            x0, [fp, #-0x48]
    // 0xb92674: r16 = Instance_FontWeight
    //     0xb92674: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb92678: ldr             x16, [x16, #0xe20]
    // 0xb9267c: stp             x16, x1, [SP]
    // 0xb92680: ldur            x1, [fp, #-0x30]
    // 0xb92684: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontWeight, 0x2, null]
    //     0xb92684: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f668] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontWeight", 0x2, Null]
    //     0xb92688: ldr             x4, [x4, #0x668]
    // 0xb9268c: r0 = copyWith()
    //     0xb9268c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92690: stur            x0, [fp, #-0x30]
    // 0xb92694: r0 = Text()
    //     0xb92694: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92698: mov             x2, x0
    // 0xb9269c: ldur            x0, [fp, #-0x20]
    // 0xb926a0: stur            x2, [fp, #-0x58]
    // 0xb926a4: StoreField: r2->field_b = r0
    //     0xb926a4: stur            w0, [x2, #0xb]
    // 0xb926a8: ldur            x0, [fp, #-0x30]
    // 0xb926ac: StoreField: r2->field_13 = r0
    //     0xb926ac: stur            w0, [x2, #0x13]
    // 0xb926b0: ldur            x0, [fp, #-0x48]
    // 0xb926b4: LoadField: r1 = r0->field_b
    //     0xb926b4: ldur            w1, [x0, #0xb]
    // 0xb926b8: LoadField: r3 = r0->field_f
    //     0xb926b8: ldur            w3, [x0, #0xf]
    // 0xb926bc: DecompressPointer r3
    //     0xb926bc: add             x3, x3, HEAP, lsl #32
    // 0xb926c0: LoadField: r4 = r3->field_b
    //     0xb926c0: ldur            w4, [x3, #0xb]
    // 0xb926c4: r3 = LoadInt32Instr(r1)
    //     0xb926c4: sbfx            x3, x1, #1, #0x1f
    // 0xb926c8: stur            x3, [fp, #-0x10]
    // 0xb926cc: r1 = LoadInt32Instr(r4)
    //     0xb926cc: sbfx            x1, x4, #1, #0x1f
    // 0xb926d0: cmp             x3, x1
    // 0xb926d4: b.ne            #0xb926e0
    // 0xb926d8: mov             x1, x0
    // 0xb926dc: r0 = _growToNextCapacity()
    //     0xb926dc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb926e0: ldur            x2, [fp, #-0x48]
    // 0xb926e4: ldur            x3, [fp, #-0x10]
    // 0xb926e8: add             x0, x3, #1
    // 0xb926ec: lsl             x1, x0, #1
    // 0xb926f0: StoreField: r2->field_b = r1
    //     0xb926f0: stur            w1, [x2, #0xb]
    // 0xb926f4: LoadField: r1 = r2->field_f
    //     0xb926f4: ldur            w1, [x2, #0xf]
    // 0xb926f8: DecompressPointer r1
    //     0xb926f8: add             x1, x1, HEAP, lsl #32
    // 0xb926fc: ldur            x0, [fp, #-0x58]
    // 0xb92700: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb92700: add             x25, x1, x3, lsl #2
    //     0xb92704: add             x25, x25, #0xf
    //     0xb92708: str             w0, [x25]
    //     0xb9270c: tbz             w0, #0, #0xb92728
    //     0xb92710: ldurb           w16, [x1, #-1]
    //     0xb92714: ldurb           w17, [x0, #-1]
    //     0xb92718: and             x16, x17, x16, lsr #2
    //     0xb9271c: tst             x16, HEAP, lsr #32
    //     0xb92720: b.eq            #0xb92728
    //     0xb92724: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb92728: b               #0xb92730
    // 0xb9272c: mov             x2, x1
    // 0xb92730: r0 = GetNavigation.theme()
    //     0xb92730: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb92734: LoadField: r1 = r0->field_8f
    //     0xb92734: ldur            w1, [x0, #0x8f]
    // 0xb92738: DecompressPointer r1
    //     0xb92738: add             x1, x1, HEAP, lsl #32
    // 0xb9273c: LoadField: r0 = r1->field_23
    //     0xb9273c: ldur            w0, [x1, #0x23]
    // 0xb92740: DecompressPointer r0
    //     0xb92740: add             x0, x0, HEAP, lsl #32
    // 0xb92744: stur            x0, [fp, #-0x20]
    // 0xb92748: cmp             w0, NULL
    // 0xb9274c: b.eq            #0xb92ad4
    // 0xb92750: r1 = _ConstMap len:3
    //     0xb92750: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb92754: ldr             x1, [x1, #0xbe8]
    // 0xb92758: r2 = 6
    //     0xb92758: movz            x2, #0x6
    // 0xb9275c: r0 = []()
    //     0xb9275c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92760: stur            x0, [fp, #-0x30]
    // 0xb92764: r0 = GetNavigation.isDarkMode()
    //     0xb92764: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb92768: tbnz            w0, #4, #0xb92774
    // 0xb9276c: r1 = Instance_Color
    //     0xb9276c: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb92770: b               #0xb92778
    // 0xb92774: ldur            x1, [fp, #-0x30]
    // 0xb92778: ldur            x2, [fp, #-0x40]
    // 0xb9277c: ldur            x0, [fp, #-0x48]
    // 0xb92780: str             x1, [SP]
    // 0xb92784: ldur            x1, [fp, #-0x20]
    // 0xb92788: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb92788: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb9278c: ldr             x4, [x4, #0x228]
    // 0xb92790: r0 = copyWith()
    //     0xb92790: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92794: stur            x0, [fp, #-0x20]
    // 0xb92798: r0 = Text()
    //     0xb92798: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb9279c: mov             x2, x0
    // 0xb927a0: ldur            x0, [fp, #-0x40]
    // 0xb927a4: stur            x2, [fp, #-0x30]
    // 0xb927a8: StoreField: r2->field_b = r0
    //     0xb927a8: stur            w0, [x2, #0xb]
    // 0xb927ac: ldur            x0, [fp, #-0x20]
    // 0xb927b0: StoreField: r2->field_13 = r0
    //     0xb927b0: stur            w0, [x2, #0x13]
    // 0xb927b4: ldur            x0, [fp, #-0x48]
    // 0xb927b8: LoadField: r1 = r0->field_b
    //     0xb927b8: ldur            w1, [x0, #0xb]
    // 0xb927bc: LoadField: r3 = r0->field_f
    //     0xb927bc: ldur            w3, [x0, #0xf]
    // 0xb927c0: DecompressPointer r3
    //     0xb927c0: add             x3, x3, HEAP, lsl #32
    // 0xb927c4: LoadField: r4 = r3->field_b
    //     0xb927c4: ldur            w4, [x3, #0xb]
    // 0xb927c8: r3 = LoadInt32Instr(r1)
    //     0xb927c8: sbfx            x3, x1, #1, #0x1f
    // 0xb927cc: stur            x3, [fp, #-0x10]
    // 0xb927d0: r1 = LoadInt32Instr(r4)
    //     0xb927d0: sbfx            x1, x4, #1, #0x1f
    // 0xb927d4: cmp             x3, x1
    // 0xb927d8: b.ne            #0xb927e4
    // 0xb927dc: mov             x1, x0
    // 0xb927e0: r0 = _growToNextCapacity()
    //     0xb927e0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb927e4: ldur            x5, [fp, #-0x18]
    // 0xb927e8: ldur            x4, [fp, #-0x50]
    // 0xb927ec: ldur            x2, [fp, #-0x48]
    // 0xb927f0: ldur            x3, [fp, #-0x10]
    // 0xb927f4: add             x0, x3, #1
    // 0xb927f8: lsl             x1, x0, #1
    // 0xb927fc: StoreField: r2->field_b = r1
    //     0xb927fc: stur            w1, [x2, #0xb]
    // 0xb92800: LoadField: r1 = r2->field_f
    //     0xb92800: ldur            w1, [x2, #0xf]
    // 0xb92804: DecompressPointer r1
    //     0xb92804: add             x1, x1, HEAP, lsl #32
    // 0xb92808: ldur            x0, [fp, #-0x30]
    // 0xb9280c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb9280c: add             x25, x1, x3, lsl #2
    //     0xb92810: add             x25, x25, #0xf
    //     0xb92814: str             w0, [x25]
    //     0xb92818: tbz             w0, #0, #0xb92834
    //     0xb9281c: ldurb           w16, [x1, #-1]
    //     0xb92820: ldurb           w17, [x0, #-1]
    //     0xb92824: and             x16, x17, x16, lsr #2
    //     0xb92828: tst             x16, HEAP, lsr #32
    //     0xb9282c: b.eq            #0xb92834
    //     0xb92830: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb92834: r0 = Column()
    //     0xb92834: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92838: mov             x2, x0
    // 0xb9283c: r0 = Instance_Axis
    //     0xb9283c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb92840: stur            x2, [fp, #-0x20]
    // 0xb92844: StoreField: r2->field_f = r0
    //     0xb92844: stur            w0, [x2, #0xf]
    // 0xb92848: r3 = Instance_MainAxisAlignment
    //     0xb92848: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb9284c: ldr             x3, [x3, #0x730]
    // 0xb92850: StoreField: r2->field_13 = r3
    //     0xb92850: stur            w3, [x2, #0x13]
    // 0xb92854: r4 = Instance_MainAxisSize
    //     0xb92854: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb92858: ldr             x4, [x4, #0x738]
    // 0xb9285c: ArrayStore: r2[0] = r4  ; List_4
    //     0xb9285c: stur            w4, [x2, #0x17]
    // 0xb92860: r5 = Instance_CrossAxisAlignment
    //     0xb92860: add             x5, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb92864: ldr             x5, [x5, #0x68]
    // 0xb92868: StoreField: r2->field_1b = r5
    //     0xb92868: stur            w5, [x2, #0x1b]
    // 0xb9286c: r6 = Instance_VerticalDirection
    //     0xb9286c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb92870: ldr             x6, [x6, #0x748]
    // 0xb92874: StoreField: r2->field_23 = r6
    //     0xb92874: stur            w6, [x2, #0x23]
    // 0xb92878: r7 = Instance_Clip
    //     0xb92878: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb9287c: ldr             x7, [x7, #0x750]
    // 0xb92880: StoreField: r2->field_2b = r7
    //     0xb92880: stur            w7, [x2, #0x2b]
    // 0xb92884: StoreField: r2->field_2f = rZR
    //     0xb92884: stur            xzr, [x2, #0x2f]
    // 0xb92888: ldur            x1, [fp, #-0x48]
    // 0xb9288c: StoreField: r2->field_b = r1
    //     0xb9288c: stur            w1, [x2, #0xb]
    // 0xb92890: r1 = <FlexParentData>
    //     0xb92890: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb92894: ldr             x1, [x1, #0x720]
    // 0xb92898: r0 = Expanded()
    //     0xb92898: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9289c: mov             x3, x0
    // 0xb928a0: r0 = 1
    //     0xb928a0: movz            x0, #0x1
    // 0xb928a4: stur            x3, [fp, #-0x30]
    // 0xb928a8: StoreField: r3->field_13 = r0
    //     0xb928a8: stur            x0, [x3, #0x13]
    // 0xb928ac: r4 = Instance_FlexFit
    //     0xb928ac: add             x4, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb928b0: ldr             x4, [x4, #0x728]
    // 0xb928b4: StoreField: r3->field_1b = r4
    //     0xb928b4: stur            w4, [x3, #0x1b]
    // 0xb928b8: ldur            x1, [fp, #-0x20]
    // 0xb928bc: StoreField: r3->field_b = r1
    //     0xb928bc: stur            w1, [x3, #0xb]
    // 0xb928c0: r1 = Null
    //     0xb928c0: mov             x1, NULL
    // 0xb928c4: r2 = 8
    //     0xb928c4: movz            x2, #0x8
    // 0xb928c8: r0 = AllocateArray()
    //     0xb928c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb928cc: mov             x2, x0
    // 0xb928d0: ldur            x0, [fp, #-0x50]
    // 0xb928d4: stur            x2, [fp, #-0x20]
    // 0xb928d8: StoreField: r2->field_f = r0
    //     0xb928d8: stur            w0, [x2, #0xf]
    // 0xb928dc: r16 = Instance_SizedBox
    //     0xb928dc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xb928e0: ldr             x16, [x16, #0x340]
    // 0xb928e4: StoreField: r2->field_13 = r16
    //     0xb928e4: stur            w16, [x2, #0x13]
    // 0xb928e8: ldur            x0, [fp, #-0x30]
    // 0xb928ec: ArrayStore: r2[0] = r0  ; List_4
    //     0xb928ec: stur            w0, [x2, #0x17]
    // 0xb928f0: r16 = Instance_SizedBox
    //     0xb928f0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb928f4: ldr             x16, [x16, #0xfb0]
    // 0xb928f8: StoreField: r2->field_1b = r16
    //     0xb928f8: stur            w16, [x2, #0x1b]
    // 0xb928fc: r1 = <Widget>
    //     0xb928fc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb92900: r0 = AllocateGrowableArray()
    //     0xb92900: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb92904: mov             x1, x0
    // 0xb92908: ldur            x0, [fp, #-0x20]
    // 0xb9290c: stur            x1, [fp, #-0x30]
    // 0xb92910: StoreField: r1->field_f = r0
    //     0xb92910: stur            w0, [x1, #0xf]
    // 0xb92914: r0 = 8
    //     0xb92914: movz            x0, #0x8
    // 0xb92918: StoreField: r1->field_b = r0
    //     0xb92918: stur            w0, [x1, #0xb]
    // 0xb9291c: r0 = Row()
    //     0xb9291c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb92920: mov             x2, x0
    // 0xb92924: r0 = Instance_Axis
    //     0xb92924: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb92928: stur            x2, [fp, #-0x20]
    // 0xb9292c: StoreField: r2->field_f = r0
    //     0xb9292c: stur            w0, [x2, #0xf]
    // 0xb92930: r3 = Instance_MainAxisAlignment
    //     0xb92930: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb92934: ldr             x3, [x3, #0x730]
    // 0xb92938: StoreField: r2->field_13 = r3
    //     0xb92938: stur            w3, [x2, #0x13]
    // 0xb9293c: r4 = Instance_MainAxisSize
    //     0xb9293c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb92940: ldr             x4, [x4, #0x738]
    // 0xb92944: ArrayStore: r2[0] = r4  ; List_4
    //     0xb92944: stur            w4, [x2, #0x17]
    // 0xb92948: r5 = Instance_CrossAxisAlignment
    //     0xb92948: add             x5, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb9294c: ldr             x5, [x5, #0x68]
    // 0xb92950: StoreField: r2->field_1b = r5
    //     0xb92950: stur            w5, [x2, #0x1b]
    // 0xb92954: r6 = Instance_VerticalDirection
    //     0xb92954: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb92958: ldr             x6, [x6, #0x748]
    // 0xb9295c: StoreField: r2->field_23 = r6
    //     0xb9295c: stur            w6, [x2, #0x23]
    // 0xb92960: r7 = Instance_Clip
    //     0xb92960: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb92964: ldr             x7, [x7, #0x750]
    // 0xb92968: StoreField: r2->field_2b = r7
    //     0xb92968: stur            w7, [x2, #0x2b]
    // 0xb9296c: StoreField: r2->field_2f = rZR
    //     0xb9296c: stur            xzr, [x2, #0x2f]
    // 0xb92970: ldur            x1, [fp, #-0x30]
    // 0xb92974: StoreField: r2->field_b = r1
    //     0xb92974: stur            w1, [x2, #0xb]
    // 0xb92978: ldur            x8, [fp, #-0x18]
    // 0xb9297c: LoadField: r1 = r8->field_b
    //     0xb9297c: ldur            w1, [x8, #0xb]
    // 0xb92980: LoadField: r9 = r8->field_f
    //     0xb92980: ldur            w9, [x8, #0xf]
    // 0xb92984: DecompressPointer r9
    //     0xb92984: add             x9, x9, HEAP, lsl #32
    // 0xb92988: LoadField: r10 = r9->field_b
    //     0xb92988: ldur            w10, [x9, #0xb]
    // 0xb9298c: r9 = LoadInt32Instr(r1)
    //     0xb9298c: sbfx            x9, x1, #1, #0x1f
    // 0xb92990: stur            x9, [fp, #-0x10]
    // 0xb92994: r1 = LoadInt32Instr(r10)
    //     0xb92994: sbfx            x1, x10, #1, #0x1f
    // 0xb92998: cmp             x9, x1
    // 0xb9299c: b.ne            #0xb929a8
    // 0xb929a0: mov             x1, x8
    // 0xb929a4: r0 = _growToNextCapacity()
    //     0xb929a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb929a8: ldur            x2, [fp, #-0x18]
    // 0xb929ac: ldur            x3, [fp, #-0x10]
    // 0xb929b0: add             x0, x3, #1
    // 0xb929b4: lsl             x1, x0, #1
    // 0xb929b8: StoreField: r2->field_b = r1
    //     0xb929b8: stur            w1, [x2, #0xb]
    // 0xb929bc: LoadField: r1 = r2->field_f
    //     0xb929bc: ldur            w1, [x2, #0xf]
    // 0xb929c0: DecompressPointer r1
    //     0xb929c0: add             x1, x1, HEAP, lsl #32
    // 0xb929c4: ldur            x0, [fp, #-0x20]
    // 0xb929c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb929c8: add             x25, x1, x3, lsl #2
    //     0xb929cc: add             x25, x25, #0xf
    //     0xb929d0: str             w0, [x25]
    //     0xb929d4: tbz             w0, #0, #0xb929f0
    //     0xb929d8: ldurb           w16, [x1, #-1]
    //     0xb929dc: ldurb           w17, [x0, #-1]
    //     0xb929e0: and             x16, x17, x16, lsr #2
    //     0xb929e4: tst             x16, HEAP, lsr #32
    //     0xb929e8: b.eq            #0xb929f0
    //     0xb929ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb929f0: ldur            x6, [fp, #-0x38]
    // 0xb929f4: ldur            x4, [fp, #-8]
    // 0xb929f8: mov             x3, x2
    // 0xb929fc: ldur            x5, [fp, #-0x28]
    // 0xb92a00: b               #0xb92380
    // 0xb92a04: mov             x0, x4
    // 0xb92a08: mov             x2, x3
    // 0xb92a0c: LoadField: r1 = r0->field_13
    //     0xb92a0c: ldur            w1, [x0, #0x13]
    // 0xb92a10: DecompressPointer r1
    //     0xb92a10: add             x1, x1, HEAP, lsl #32
    // 0xb92a14: stur            x1, [fp, #-0x20]
    // 0xb92a18: r0 = Column()
    //     0xb92a18: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92a1c: mov             x1, x0
    // 0xb92a20: r0 = Instance_Axis
    //     0xb92a20: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb92a24: stur            x1, [fp, #-8]
    // 0xb92a28: StoreField: r1->field_f = r0
    //     0xb92a28: stur            w0, [x1, #0xf]
    // 0xb92a2c: r0 = Instance_MainAxisAlignment
    //     0xb92a2c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb92a30: ldr             x0, [x0, #0x730]
    // 0xb92a34: StoreField: r1->field_13 = r0
    //     0xb92a34: stur            w0, [x1, #0x13]
    // 0xb92a38: r0 = Instance_MainAxisSize
    //     0xb92a38: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb92a3c: ldr             x0, [x0, #0x738]
    // 0xb92a40: ArrayStore: r1[0] = r0  ; List_4
    //     0xb92a40: stur            w0, [x1, #0x17]
    // 0xb92a44: r0 = Instance_CrossAxisAlignment
    //     0xb92a44: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb92a48: ldr             x0, [x0, #0x740]
    // 0xb92a4c: StoreField: r1->field_1b = r0
    //     0xb92a4c: stur            w0, [x1, #0x1b]
    // 0xb92a50: r0 = Instance_VerticalDirection
    //     0xb92a50: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb92a54: ldr             x0, [x0, #0x748]
    // 0xb92a58: StoreField: r1->field_23 = r0
    //     0xb92a58: stur            w0, [x1, #0x23]
    // 0xb92a5c: r0 = Instance_Clip
    //     0xb92a5c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb92a60: ldr             x0, [x0, #0x750]
    // 0xb92a64: StoreField: r1->field_2b = r0
    //     0xb92a64: stur            w0, [x1, #0x2b]
    // 0xb92a68: StoreField: r1->field_2f = rZR
    //     0xb92a68: stur            xzr, [x1, #0x2f]
    // 0xb92a6c: ldur            x0, [fp, #-0x18]
    // 0xb92a70: StoreField: r1->field_b = r0
    //     0xb92a70: stur            w0, [x1, #0xb]
    // 0xb92a74: r0 = Padding()
    //     0xb92a74: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb92a78: mov             x1, x0
    // 0xb92a7c: ldur            x0, [fp, #-0x20]
    // 0xb92a80: StoreField: r1->field_f = r0
    //     0xb92a80: stur            w0, [x1, #0xf]
    // 0xb92a84: ldur            x0, [fp, #-8]
    // 0xb92a88: StoreField: r1->field_b = r0
    //     0xb92a88: stur            w0, [x1, #0xb]
    // 0xb92a8c: mov             x0, x1
    // 0xb92a90: LeaveFrame
    //     0xb92a90: mov             SP, fp
    //     0xb92a94: ldp             fp, lr, [SP], #0x10
    // 0xb92a98: ret
    //     0xb92a98: ret             
    // 0xb92a9c: r0 = noElement()
    //     0xb92a9c: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xb92aa0: r0 = Throw()
    //     0xb92aa0: bl              #0xec04b8  ; ThrowStub
    // 0xb92aa4: brk             #0
    // 0xb92aa8: r0 = noElement()
    //     0xb92aa8: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xb92aac: r0 = Throw()
    //     0xb92aac: bl              #0xec04b8  ; ThrowStub
    // 0xb92ab0: brk             #0
    // 0xb92ab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb92ab4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb92ab8: b               #0xb922e8
    // 0xb92abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb92abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb92ac0: b               #0xb92390
    // 0xb92ac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb92ac4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb92ac8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb92ac8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb92acc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92acc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb92ad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92ad0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb92ad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92ad4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5029, size: 0x10, field offset: 0xc
//   const constructor, 
class PaymentInstruction extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb91e48, size: 0x468
    // 0xb91e48: EnterFrame
    //     0xb91e48: stp             fp, lr, [SP, #-0x10]!
    //     0xb91e4c: mov             fp, SP
    // 0xb91e50: AllocStack(0x68)
    //     0xb91e50: sub             SP, SP, #0x68
    // 0xb91e54: CheckStackOverflow
    //     0xb91e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91e58: cmp             SP, x16
    //     0xb91e5c: b.ls            #0xb9229c
    // 0xb91e60: LoadField: r0 = r1->field_b
    //     0xb91e60: ldur            w0, [x1, #0xb]
    // 0xb91e64: DecompressPointer r0
    //     0xb91e64: add             x0, x0, HEAP, lsl #32
    // 0xb91e68: stur            x0, [fp, #-0x10]
    // 0xb91e6c: LoadField: r1 = r0->field_b
    //     0xb91e6c: ldur            w1, [x0, #0xb]
    // 0xb91e70: cmp             w1, #2
    // 0xb91e74: b.ne            #0xb91ec0
    // 0xb91e78: mov             x1, x0
    // 0xb91e7c: r0 = first()
    //     0xb91e7c: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xb91e80: LoadField: r1 = r0->field_b
    //     0xb91e80: ldur            w1, [x0, #0xb]
    // 0xb91e84: DecompressPointer r1
    //     0xb91e84: add             x1, x1, HEAP, lsl #32
    // 0xb91e88: stur            x1, [fp, #-8]
    // 0xb91e8c: r0 = Steps()
    //     0xb91e8c: bl              #0xb922bc  ; AllocateStepsStub -> Steps (size=0x18)
    // 0xb91e90: mov             x1, x0
    // 0xb91e94: ldur            x0, [fp, #-8]
    // 0xb91e98: StoreField: r1->field_b = r0
    //     0xb91e98: stur            w0, [x1, #0xb]
    // 0xb91e9c: r3 = false
    //     0xb91e9c: add             x3, NULL, #0x30  ; false
    // 0xb91ea0: StoreField: r1->field_f = r3
    //     0xb91ea0: stur            w3, [x1, #0xf]
    // 0xb91ea4: r0 = Instance_EdgeInsets
    //     0xb91ea4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f648] Obj!EdgeInsets@e12761
    //     0xb91ea8: ldr             x0, [x0, #0x648]
    // 0xb91eac: StoreField: r1->field_13 = r0
    //     0xb91eac: stur            w0, [x1, #0x13]
    // 0xb91eb0: mov             x0, x1
    // 0xb91eb4: LeaveFrame
    //     0xb91eb4: mov             SP, fp
    //     0xb91eb8: ldp             fp, lr, [SP], #0x10
    // 0xb91ebc: ret
    //     0xb91ebc: ret             
    // 0xb91ec0: r4 = 2
    //     0xb91ec0: movz            x4, #0x2
    // 0xb91ec4: r3 = false
    //     0xb91ec4: add             x3, NULL, #0x30  ; false
    // 0xb91ec8: mov             x2, x4
    // 0xb91ecc: r1 = Null
    //     0xb91ecc: mov             x1, NULL
    // 0xb91ed0: r0 = AllocateArray()
    //     0xb91ed0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb91ed4: stur            x0, [fp, #-8]
    // 0xb91ed8: r16 = Instance_SizedBox
    //     0xb91ed8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb91edc: ldr             x16, [x16, #0x950]
    // 0xb91ee0: StoreField: r0->field_f = r16
    //     0xb91ee0: stur            w16, [x0, #0xf]
    // 0xb91ee4: r1 = <Widget>
    //     0xb91ee4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb91ee8: r0 = AllocateGrowableArray()
    //     0xb91ee8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb91eec: mov             x1, x0
    // 0xb91ef0: ldur            x0, [fp, #-8]
    // 0xb91ef4: stur            x1, [fp, #-0x28]
    // 0xb91ef8: StoreField: r1->field_f = r0
    //     0xb91ef8: stur            w0, [x1, #0xf]
    // 0xb91efc: r0 = 2
    //     0xb91efc: movz            x0, #0x2
    // 0xb91f00: StoreField: r1->field_b = r0
    //     0xb91f00: stur            w0, [x1, #0xb]
    // 0xb91f04: ldur            x0, [fp, #-0x10]
    // 0xb91f08: LoadField: r2 = r0->field_b
    //     0xb91f08: ldur            w2, [x0, #0xb]
    // 0xb91f0c: r3 = LoadInt32Instr(r2)
    //     0xb91f0c: sbfx            x3, x2, #1, #0x1f
    // 0xb91f10: stur            x3, [fp, #-0x20]
    // 0xb91f14: r2 = 0
    //     0xb91f14: movz            x2, #0
    // 0xb91f18: CheckStackOverflow
    //     0xb91f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91f1c: cmp             SP, x16
    //     0xb91f20: b.ls            #0xb922a4
    // 0xb91f24: LoadField: r4 = r0->field_b
    //     0xb91f24: ldur            w4, [x0, #0xb]
    // 0xb91f28: r5 = LoadInt32Instr(r4)
    //     0xb91f28: sbfx            x5, x4, #1, #0x1f
    // 0xb91f2c: cmp             x3, x5
    // 0xb91f30: b.ne            #0xb92280
    // 0xb91f34: cmp             x2, x5
    // 0xb91f38: b.ge            #0xb92214
    // 0xb91f3c: LoadField: r4 = r0->field_f
    //     0xb91f3c: ldur            w4, [x0, #0xf]
    // 0xb91f40: DecompressPointer r4
    //     0xb91f40: add             x4, x4, HEAP, lsl #32
    // 0xb91f44: ArrayLoad: r5 = r4[r2]  ; Unknown_4
    //     0xb91f44: add             x16, x4, x2, lsl #2
    //     0xb91f48: ldur            w5, [x16, #0xf]
    // 0xb91f4c: DecompressPointer r5
    //     0xb91f4c: add             x5, x5, HEAP, lsl #32
    // 0xb91f50: stur            x5, [fp, #-8]
    // 0xb91f54: add             x4, x2, #1
    // 0xb91f58: stur            x4, [fp, #-0x18]
    // 0xb91f5c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb91f5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb91f60: ldr             x0, [x0, #0x2670]
    //     0xb91f64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb91f68: cmp             w0, w16
    //     0xb91f6c: b.ne            #0xb91f78
    //     0xb91f70: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb91f74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb91f78: r16 = Instance_Brightness
    //     0xb91f78: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xb91f7c: stp             NULL, x16, [SP]
    // 0xb91f80: r1 = Null
    //     0xb91f80: mov             x1, NULL
    // 0xb91f84: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xb91f84: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xb91f88: r0 = ThemeData()
    //     0xb91f88: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xb91f8c: stur            x0, [fp, #-0x30]
    // 0xb91f90: r0 = GetNavigation.context()
    //     0xb91f90: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xb91f94: cmp             w0, NULL
    // 0xb91f98: b.eq            #0xb91fb4
    // 0xb91f9c: r0 = GetNavigation.context()
    //     0xb91f9c: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xb91fa0: cmp             w0, NULL
    // 0xb91fa4: b.eq            #0xb922ac
    // 0xb91fa8: mov             x1, x0
    // 0xb91fac: r0 = of()
    //     0xb91fac: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb91fb0: b               #0xb91fb8
    // 0xb91fb4: ldur            x0, [fp, #-0x30]
    // 0xb91fb8: LoadField: r3 = r0->field_77
    //     0xb91fb8: ldur            w3, [x0, #0x77]
    // 0xb91fbc: DecompressPointer r3
    //     0xb91fbc: add             x3, x3, HEAP, lsl #32
    // 0xb91fc0: stur            x3, [fp, #-0x30]
    // 0xb91fc4: r1 = _ConstMap len:6
    //     0xb91fc4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb91fc8: ldr             x1, [x1, #0xc20]
    // 0xb91fcc: r2 = 8
    //     0xb91fcc: movz            x2, #0x8
    // 0xb91fd0: r0 = []()
    //     0xb91fd0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb91fd4: r1 = _ConstMap len:6
    //     0xb91fd4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb91fd8: ldr             x1, [x1, #0xc20]
    // 0xb91fdc: r2 = 6
    //     0xb91fdc: movz            x2, #0x6
    // 0xb91fe0: stur            x0, [fp, #-0x38]
    // 0xb91fe4: r0 = []()
    //     0xb91fe4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb91fe8: stur            x0, [fp, #-0x40]
    // 0xb91fec: r0 = GetNavigation.isDarkMode()
    //     0xb91fec: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb91ff0: tbnz            w0, #4, #0xb91ffc
    // 0xb91ff4: ldur            x3, [fp, #-0x40]
    // 0xb91ff8: b               #0xb92000
    // 0xb91ffc: ldur            x3, [fp, #-0x38]
    // 0xb92000: ldur            x0, [fp, #-0x30]
    // 0xb92004: ldur            x1, [fp, #-0x28]
    // 0xb92008: ldur            x2, [fp, #-8]
    // 0xb9200c: stur            x3, [fp, #-0x38]
    // 0xb92010: r0 = ExpandableThemeData()
    //     0xb92010: bl              #0x92d270  ; AllocateExpandableThemeDataStub -> ExpandableThemeData (size=0x5c)
    // 0xb92014: mov             x1, x0
    // 0xb92018: ldur            x0, [fp, #-0x38]
    // 0xb9201c: stur            x1, [fp, #-0x40]
    // 0xb92020: StoreField: r1->field_7 = r0
    //     0xb92020: stur            w0, [x1, #7]
    // 0xb92024: r0 = Instance_ExpandablePanelHeaderAlignment
    //     0xb92024: add             x0, PP, #0x35, lsl #12  ; [pp+0x35450] Obj!ExpandablePanelHeaderAlignment@e373e1
    //     0xb92028: ldr             x0, [x0, #0x450]
    // 0xb9202c: StoreField: r1->field_27 = r0
    //     0xb9202c: stur            w0, [x1, #0x27]
    // 0xb92030: r2 = 16.000000
    //     0xb92030: add             x2, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb92034: ldr             x2, [x2, #0x80]
    // 0xb92038: StoreField: r1->field_43 = r2
    //     0xb92038: stur            w2, [x1, #0x43]
    // 0xb9203c: r3 = Instance_IconData
    //     0xb9203c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35458] Obj!IconData@e10011
    //     0xb92040: ldr             x3, [x3, #0x458]
    // 0xb92044: StoreField: r1->field_4f = r3
    //     0xb92044: stur            w3, [x1, #0x4f]
    // 0xb92048: r4 = Instance_IconData
    //     0xb92048: add             x4, PP, #0x35, lsl #12  ; [pp+0x35460] Obj!IconData@e10911
    //     0xb9204c: ldr             x4, [x4, #0x460]
    // 0xb92050: StoreField: r1->field_53 = r4
    //     0xb92050: stur            w4, [x1, #0x53]
    // 0xb92054: ldur            x5, [fp, #-8]
    // 0xb92058: LoadField: r6 = r5->field_7
    //     0xb92058: ldur            w6, [x5, #7]
    // 0xb9205c: DecompressPointer r6
    //     0xb9205c: add             x6, x6, HEAP, lsl #32
    // 0xb92060: stur            x6, [fp, #-0x38]
    // 0xb92064: r0 = GetNavigation.theme()
    //     0xb92064: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb92068: LoadField: r1 = r0->field_8f
    //     0xb92068: ldur            w1, [x0, #0x8f]
    // 0xb9206c: DecompressPointer r1
    //     0xb9206c: add             x1, x1, HEAP, lsl #32
    // 0xb92070: LoadField: r0 = r1->field_23
    //     0xb92070: ldur            w0, [x1, #0x23]
    // 0xb92074: DecompressPointer r0
    //     0xb92074: add             x0, x0, HEAP, lsl #32
    // 0xb92078: stur            x0, [fp, #-0x48]
    // 0xb9207c: r0 = Text()
    //     0xb9207c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92080: mov             x1, x0
    // 0xb92084: ldur            x0, [fp, #-0x38]
    // 0xb92088: stur            x1, [fp, #-0x50]
    // 0xb9208c: StoreField: r1->field_b = r0
    //     0xb9208c: stur            w0, [x1, #0xb]
    // 0xb92090: ldur            x0, [fp, #-0x48]
    // 0xb92094: StoreField: r1->field_13 = r0
    //     0xb92094: stur            w0, [x1, #0x13]
    // 0xb92098: r0 = ListTile()
    //     0xb92098: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xb9209c: mov             x1, x0
    // 0xb920a0: ldur            x0, [fp, #-0x50]
    // 0xb920a4: stur            x1, [fp, #-0x48]
    // 0xb920a8: StoreField: r1->field_f = r0
    //     0xb920a8: stur            w0, [x1, #0xf]
    // 0xb920ac: r0 = false
    //     0xb920ac: add             x0, NULL, #0x30  ; false
    // 0xb920b0: StoreField: r1->field_1b = r0
    //     0xb920b0: stur            w0, [x1, #0x1b]
    // 0xb920b4: r2 = Instance_EdgeInsets
    //     0xb920b4: add             x2, PP, #0x31, lsl #12  ; [pp+0x31b00] Obj!EdgeInsets@e131e1
    //     0xb920b8: ldr             x2, [x2, #0xb00]
    // 0xb920bc: StoreField: r1->field_47 = r2
    //     0xb920bc: stur            w2, [x1, #0x47]
    // 0xb920c0: r3 = true
    //     0xb920c0: add             x3, NULL, #0x20  ; true
    // 0xb920c4: StoreField: r1->field_4b = r3
    //     0xb920c4: stur            w3, [x1, #0x4b]
    // 0xb920c8: StoreField: r1->field_5f = r0
    //     0xb920c8: stur            w0, [x1, #0x5f]
    // 0xb920cc: StoreField: r1->field_73 = r0
    //     0xb920cc: stur            w0, [x1, #0x73]
    // 0xb920d0: StoreField: r1->field_97 = r3
    //     0xb920d0: stur            w3, [x1, #0x97]
    // 0xb920d4: ldur            x4, [fp, #-8]
    // 0xb920d8: LoadField: r5 = r4->field_b
    //     0xb920d8: ldur            w5, [x4, #0xb]
    // 0xb920dc: DecompressPointer r5
    //     0xb920dc: add             x5, x5, HEAP, lsl #32
    // 0xb920e0: stur            x5, [fp, #-0x38]
    // 0xb920e4: r0 = Steps()
    //     0xb920e4: bl              #0xb922bc  ; AllocateStepsStub -> Steps (size=0x18)
    // 0xb920e8: mov             x1, x0
    // 0xb920ec: ldur            x0, [fp, #-0x38]
    // 0xb920f0: stur            x1, [fp, #-8]
    // 0xb920f4: StoreField: r1->field_b = r0
    //     0xb920f4: stur            w0, [x1, #0xb]
    // 0xb920f8: r0 = true
    //     0xb920f8: add             x0, NULL, #0x20  ; true
    // 0xb920fc: StoreField: r1->field_f = r0
    //     0xb920fc: stur            w0, [x1, #0xf]
    // 0xb92100: r2 = Instance_EdgeInsets
    //     0xb92100: add             x2, PP, #0x35, lsl #12  ; [pp+0x35468] Obj!EdgeInsets@e13541
    //     0xb92104: ldr             x2, [x2, #0x468]
    // 0xb92108: StoreField: r1->field_13 = r2
    //     0xb92108: stur            w2, [x1, #0x13]
    // 0xb9210c: r0 = ExpandablePanel()
    //     0xb9210c: bl              #0xb922b0  ; AllocateExpandablePanelStub -> ExpandablePanel (size=0x24)
    // 0xb92110: mov             x1, x0
    // 0xb92114: ldur            x0, [fp, #-0x48]
    // 0xb92118: stur            x1, [fp, #-0x38]
    // 0xb9211c: StoreField: r1->field_b = r0
    //     0xb9211c: stur            w0, [x1, #0xb]
    // 0xb92120: r0 = Instance_SizedBox
    //     0xb92120: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb92124: ldr             x0, [x0, #0xc40]
    // 0xb92128: StoreField: r1->field_f = r0
    //     0xb92128: stur            w0, [x1, #0xf]
    // 0xb9212c: ldur            x2, [fp, #-8]
    // 0xb92130: StoreField: r1->field_13 = r2
    //     0xb92130: stur            w2, [x1, #0x13]
    // 0xb92134: ldur            x2, [fp, #-0x40]
    // 0xb92138: StoreField: r1->field_1f = r2
    //     0xb92138: stur            w2, [x1, #0x1f]
    // 0xb9213c: r0 = Card()
    //     0xb9213c: bl              #0xad7cd4  ; AllocateCardStub -> Card (size=0x38)
    // 0xb92140: mov             x2, x0
    // 0xb92144: ldur            x0, [fp, #-0x30]
    // 0xb92148: stur            x2, [fp, #-8]
    // 0xb9214c: StoreField: r2->field_f = r0
    //     0xb9214c: stur            w0, [x2, #0xf]
    // 0xb92150: r0 = 4.000000
    //     0xb92150: add             x0, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0xb92154: ldr             x0, [x0, #0x770]
    // 0xb92158: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92158: stur            w0, [x2, #0x17]
    // 0xb9215c: r3 = true
    //     0xb9215c: add             x3, NULL, #0x20  ; true
    // 0xb92160: StoreField: r2->field_1f = r3
    //     0xb92160: stur            w3, [x2, #0x1f]
    // 0xb92164: r4 = Instance_EdgeInsets
    //     0xb92164: add             x4, PP, #0x28, lsl #12  ; [pp+0x283e8] Obj!EdgeInsets@e12851
    //     0xb92168: ldr             x4, [x4, #0x3e8]
    // 0xb9216c: StoreField: r2->field_27 = r4
    //     0xb9216c: stur            w4, [x2, #0x27]
    // 0xb92170: ldur            x1, [fp, #-0x38]
    // 0xb92174: StoreField: r2->field_2f = r1
    //     0xb92174: stur            w1, [x2, #0x2f]
    // 0xb92178: StoreField: r2->field_2b = r3
    //     0xb92178: stur            w3, [x2, #0x2b]
    // 0xb9217c: r5 = Instance__CardVariant
    //     0xb9217c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25778] Obj!_CardVariant@e36a41
    //     0xb92180: ldr             x5, [x5, #0x778]
    // 0xb92184: StoreField: r2->field_33 = r5
    //     0xb92184: stur            w5, [x2, #0x33]
    // 0xb92188: ldur            x6, [fp, #-0x28]
    // 0xb9218c: LoadField: r1 = r6->field_b
    //     0xb9218c: ldur            w1, [x6, #0xb]
    // 0xb92190: LoadField: r7 = r6->field_f
    //     0xb92190: ldur            w7, [x6, #0xf]
    // 0xb92194: DecompressPointer r7
    //     0xb92194: add             x7, x7, HEAP, lsl #32
    // 0xb92198: LoadField: r8 = r7->field_b
    //     0xb92198: ldur            w8, [x7, #0xb]
    // 0xb9219c: r7 = LoadInt32Instr(r1)
    //     0xb9219c: sbfx            x7, x1, #1, #0x1f
    // 0xb921a0: stur            x7, [fp, #-0x58]
    // 0xb921a4: r1 = LoadInt32Instr(r8)
    //     0xb921a4: sbfx            x1, x8, #1, #0x1f
    // 0xb921a8: cmp             x7, x1
    // 0xb921ac: b.ne            #0xb921b8
    // 0xb921b0: mov             x1, x6
    // 0xb921b4: r0 = _growToNextCapacity()
    //     0xb921b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb921b8: ldur            x3, [fp, #-0x28]
    // 0xb921bc: ldur            x2, [fp, #-0x58]
    // 0xb921c0: add             x0, x2, #1
    // 0xb921c4: lsl             x1, x0, #1
    // 0xb921c8: StoreField: r3->field_b = r1
    //     0xb921c8: stur            w1, [x3, #0xb]
    // 0xb921cc: LoadField: r1 = r3->field_f
    //     0xb921cc: ldur            w1, [x3, #0xf]
    // 0xb921d0: DecompressPointer r1
    //     0xb921d0: add             x1, x1, HEAP, lsl #32
    // 0xb921d4: ldur            x0, [fp, #-8]
    // 0xb921d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb921d8: add             x25, x1, x2, lsl #2
    //     0xb921dc: add             x25, x25, #0xf
    //     0xb921e0: str             w0, [x25]
    //     0xb921e4: tbz             w0, #0, #0xb92200
    //     0xb921e8: ldurb           w16, [x1, #-1]
    //     0xb921ec: ldurb           w17, [x0, #-1]
    //     0xb921f0: and             x16, x17, x16, lsr #2
    //     0xb921f4: tst             x16, HEAP, lsr #32
    //     0xb921f8: b.eq            #0xb92200
    //     0xb921fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb92200: ldur            x2, [fp, #-0x18]
    // 0xb92204: ldur            x0, [fp, #-0x10]
    // 0xb92208: mov             x1, x3
    // 0xb9220c: ldur            x3, [fp, #-0x20]
    // 0xb92210: b               #0xb91f18
    // 0xb92214: mov             x3, x1
    // 0xb92218: r0 = Column()
    //     0xb92218: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9221c: mov             x1, x0
    // 0xb92220: r0 = Instance_Axis
    //     0xb92220: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb92224: StoreField: r1->field_f = r0
    //     0xb92224: stur            w0, [x1, #0xf]
    // 0xb92228: r0 = Instance_MainAxisAlignment
    //     0xb92228: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb9222c: ldr             x0, [x0, #0x730]
    // 0xb92230: StoreField: r1->field_13 = r0
    //     0xb92230: stur            w0, [x1, #0x13]
    // 0xb92234: r0 = Instance_MainAxisSize
    //     0xb92234: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb92238: ldr             x0, [x0, #0x738]
    // 0xb9223c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9223c: stur            w0, [x1, #0x17]
    // 0xb92240: r0 = Instance_CrossAxisAlignment
    //     0xb92240: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb92244: ldr             x0, [x0, #0x740]
    // 0xb92248: StoreField: r1->field_1b = r0
    //     0xb92248: stur            w0, [x1, #0x1b]
    // 0xb9224c: r0 = Instance_VerticalDirection
    //     0xb9224c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb92250: ldr             x0, [x0, #0x748]
    // 0xb92254: StoreField: r1->field_23 = r0
    //     0xb92254: stur            w0, [x1, #0x23]
    // 0xb92258: r0 = Instance_Clip
    //     0xb92258: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb9225c: ldr             x0, [x0, #0x750]
    // 0xb92260: StoreField: r1->field_2b = r0
    //     0xb92260: stur            w0, [x1, #0x2b]
    // 0xb92264: StoreField: r1->field_2f = rZR
    //     0xb92264: stur            xzr, [x1, #0x2f]
    // 0xb92268: ldur            x0, [fp, #-0x28]
    // 0xb9226c: StoreField: r1->field_b = r0
    //     0xb9226c: stur            w0, [x1, #0xb]
    // 0xb92270: mov             x0, x1
    // 0xb92274: LeaveFrame
    //     0xb92274: mov             SP, fp
    //     0xb92278: ldp             fp, lr, [SP], #0x10
    // 0xb9227c: ret
    //     0xb9227c: ret             
    // 0xb92280: r0 = ConcurrentModificationError()
    //     0xb92280: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb92284: mov             x1, x0
    // 0xb92288: ldur            x0, [fp, #-0x10]
    // 0xb9228c: StoreField: r1->field_b = r0
    //     0xb9228c: stur            w0, [x1, #0xb]
    // 0xb92290: mov             x0, x1
    // 0xb92294: r0 = Throw()
    //     0xb92294: bl              #0xec04b8  ; ThrowStub
    // 0xb92298: brk             #0
    // 0xb9229c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9229c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb922a0: b               #0xb91e60
    // 0xb922a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb922a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb922a8: b               #0xb91f24
    // 0xb922ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb922ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
