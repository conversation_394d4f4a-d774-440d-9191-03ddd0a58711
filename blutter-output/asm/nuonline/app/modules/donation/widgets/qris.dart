// lib: , url: package:nuonline/app/modules/donation/widgets/qris.dart

// class id: 1050243, size: 0x8
class :: {
}

// class id: 5027, size: 0x1c, field offset: 0xc
//   const constructor, 
class QRis extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb92ad8, size: 0x380
    // 0xb92ad8: EnterFrame
    //     0xb92ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xb92adc: mov             fp, SP
    // 0xb92ae0: AllocStack(0x40)
    //     0xb92ae0: sub             SP, SP, #0x40
    // 0xb92ae4: SetupParameters(QRis this /* r1 => r1, fp-0x8 */)
    //     0xb92ae4: stur            x1, [fp, #-8]
    // 0xb92ae8: CheckStackOverflow
    //     0xb92ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb92aec: cmp             SP, x16
    //     0xb92af0: b.ls            #0xb92e44
    // 0xb92af4: r0 = Image()
    //     0xb92af4: bl              #0x92219c  ; AllocateImageStub -> Image (size=0x58)
    // 0xb92af8: mov             x1, x0
    // 0xb92afc: r2 = "assets/images/qris_cover.png"
    //     0xb92afc: add             x2, PP, #0x47, lsl #12  ; [pp+0x47b58] "assets/images/qris_cover.png"
    //     0xb92b00: ldr             x2, [x2, #0xb58]
    // 0xb92b04: stur            x0, [fp, #-0x10]
    // 0xb92b08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb92b08: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb92b0c: r0 = Image.asset()
    //     0xb92b0c: bl              #0x921e44  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xb92b10: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb92b10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb92b14: ldr             x0, [x0, #0x2670]
    //     0xb92b18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb92b1c: cmp             w0, w16
    //     0xb92b20: b.ne            #0xb92b2c
    //     0xb92b24: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb92b28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb92b2c: r0 = GetNavigation.textTheme()
    //     0xb92b2c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb92b30: LoadField: r3 = r0->field_f
    //     0xb92b30: ldur            w3, [x0, #0xf]
    // 0xb92b34: DecompressPointer r3
    //     0xb92b34: add             x3, x3, HEAP, lsl #32
    // 0xb92b38: stur            x3, [fp, #-0x18]
    // 0xb92b3c: cmp             w3, NULL
    // 0xb92b40: b.eq            #0xb92e4c
    // 0xb92b44: r1 = _ConstMap len:3
    //     0xb92b44: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb92b48: ldr             x1, [x1, #0xbe8]
    // 0xb92b4c: r2 = 6
    //     0xb92b4c: movz            x2, #0x6
    // 0xb92b50: r0 = []()
    //     0xb92b50: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92b54: str             x0, [SP]
    // 0xb92b58: ldur            x1, [fp, #-0x18]
    // 0xb92b5c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb92b5c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb92b60: ldr             x4, [x4, #0x228]
    // 0xb92b64: r0 = copyWith()
    //     0xb92b64: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92b68: stur            x0, [fp, #-0x18]
    // 0xb92b6c: r0 = Text()
    //     0xb92b6c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92b70: mov             x1, x0
    // 0xb92b74: r0 = "NU Online"
    //     0xb92b74: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "NU Online"
    //     0xb92b78: ldr             x0, [x0, #0xba0]
    // 0xb92b7c: stur            x1, [fp, #-0x20]
    // 0xb92b80: StoreField: r1->field_b = r0
    //     0xb92b80: stur            w0, [x1, #0xb]
    // 0xb92b84: ldur            x0, [fp, #-0x18]
    // 0xb92b88: StoreField: r1->field_13 = r0
    //     0xb92b88: stur            w0, [x1, #0x13]
    // 0xb92b8c: ldur            x0, [fp, #-8]
    // 0xb92b90: LoadField: r2 = r0->field_b
    //     0xb92b90: ldur            w2, [x0, #0xb]
    // 0xb92b94: DecompressPointer r2
    //     0xb92b94: add             x2, x2, HEAP, lsl #32
    // 0xb92b98: stur            x2, [fp, #-0x18]
    // 0xb92b9c: r0 = GetNavigation.textTheme()
    //     0xb92b9c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb92ba0: LoadField: r3 = r0->field_23
    //     0xb92ba0: ldur            w3, [x0, #0x23]
    // 0xb92ba4: DecompressPointer r3
    //     0xb92ba4: add             x3, x3, HEAP, lsl #32
    // 0xb92ba8: stur            x3, [fp, #-0x28]
    // 0xb92bac: cmp             w3, NULL
    // 0xb92bb0: b.eq            #0xb92e50
    // 0xb92bb4: r1 = _ConstMap len:6
    //     0xb92bb4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb92bb8: ldr             x1, [x1, #0xc20]
    // 0xb92bbc: r2 = 8
    //     0xb92bbc: movz            x2, #0x8
    // 0xb92bc0: r0 = []()
    //     0xb92bc0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92bc4: str             x0, [SP]
    // 0xb92bc8: ldur            x1, [fp, #-0x28]
    // 0xb92bcc: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb92bcc: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb92bd0: ldr             x4, [x4, #0x228]
    // 0xb92bd4: r0 = copyWith()
    //     0xb92bd4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92bd8: stur            x0, [fp, #-0x28]
    // 0xb92bdc: r0 = Text()
    //     0xb92bdc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92be0: mov             x2, x0
    // 0xb92be4: ldur            x0, [fp, #-0x18]
    // 0xb92be8: stur            x2, [fp, #-0x30]
    // 0xb92bec: StoreField: r2->field_b = r0
    //     0xb92bec: stur            w0, [x2, #0xb]
    // 0xb92bf0: ldur            x0, [fp, #-0x28]
    // 0xb92bf4: StoreField: r2->field_13 = r0
    //     0xb92bf4: stur            w0, [x2, #0x13]
    // 0xb92bf8: ldur            x0, [fp, #-8]
    // 0xb92bfc: LoadField: r1 = r0->field_f
    //     0xb92bfc: ldur            x1, [x0, #0xf]
    // 0xb92c00: r0 = IntExtension.idr()
    //     0xb92c00: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb92c04: stur            x0, [fp, #-0x18]
    // 0xb92c08: r0 = GetNavigation.textTheme()
    //     0xb92c08: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb92c0c: LoadField: r3 = r0->field_f
    //     0xb92c0c: ldur            w3, [x0, #0xf]
    // 0xb92c10: DecompressPointer r3
    //     0xb92c10: add             x3, x3, HEAP, lsl #32
    // 0xb92c14: stur            x3, [fp, #-0x28]
    // 0xb92c18: cmp             w3, NULL
    // 0xb92c1c: b.eq            #0xb92e54
    // 0xb92c20: r1 = _ConstMap len:3
    //     0xb92c20: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb92c24: ldr             x1, [x1, #0xbe8]
    // 0xb92c28: r2 = 6
    //     0xb92c28: movz            x2, #0x6
    // 0xb92c2c: r0 = []()
    //     0xb92c2c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92c30: str             x0, [SP]
    // 0xb92c34: ldur            x1, [fp, #-0x28]
    // 0xb92c38: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb92c38: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb92c3c: ldr             x4, [x4, #0x228]
    // 0xb92c40: r0 = copyWith()
    //     0xb92c40: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb92c44: stur            x0, [fp, #-0x28]
    // 0xb92c48: r0 = Text()
    //     0xb92c48: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb92c4c: mov             x1, x0
    // 0xb92c50: ldur            x0, [fp, #-0x18]
    // 0xb92c54: stur            x1, [fp, #-0x38]
    // 0xb92c58: StoreField: r1->field_b = r0
    //     0xb92c58: stur            w0, [x1, #0xb]
    // 0xb92c5c: ldur            x0, [fp, #-0x28]
    // 0xb92c60: StoreField: r1->field_13 = r0
    //     0xb92c60: stur            w0, [x1, #0x13]
    // 0xb92c64: ldur            x0, [fp, #-8]
    // 0xb92c68: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb92c68: ldur            w2, [x0, #0x17]
    // 0xb92c6c: DecompressPointer r2
    //     0xb92c6c: add             x2, x2, HEAP, lsl #32
    // 0xb92c70: stur            x2, [fp, #-0x18]
    // 0xb92c74: r0 = QrImageView()
    //     0xb92c74: bl              #0xb92f04  ; AllocateQrImageViewStub -> QrImageView (size=0x5c)
    // 0xb92c78: mov             x1, x0
    // 0xb92c7c: ldur            x2, [fp, #-0x18]
    // 0xb92c80: stur            x0, [fp, #-8]
    // 0xb92c84: r0 = QrImageView()
    //     0xb92c84: bl              #0xb92e58  ; [package:qr_flutter/src/qr_image_view.dart] QrImageView::QrImageView
    // 0xb92c88: r1 = Null
    //     0xb92c88: mov             x1, NULL
    // 0xb92c8c: r2 = 14
    //     0xb92c8c: movz            x2, #0xe
    // 0xb92c90: r0 = AllocateArray()
    //     0xb92c90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb92c94: mov             x2, x0
    // 0xb92c98: ldur            x0, [fp, #-0x20]
    // 0xb92c9c: stur            x2, [fp, #-0x18]
    // 0xb92ca0: StoreField: r2->field_f = r0
    //     0xb92ca0: stur            w0, [x2, #0xf]
    // 0xb92ca4: r16 = Instance_SizedBox
    //     0xb92ca4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb92ca8: ldr             x16, [x16, #0xfb0]
    // 0xb92cac: StoreField: r2->field_13 = r16
    //     0xb92cac: stur            w16, [x2, #0x13]
    // 0xb92cb0: ldur            x0, [fp, #-0x30]
    // 0xb92cb4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92cb4: stur            w0, [x2, #0x17]
    // 0xb92cb8: r16 = Instance_SizedBox
    //     0xb92cb8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb92cbc: ldr             x16, [x16, #0xfb0]
    // 0xb92cc0: StoreField: r2->field_1b = r16
    //     0xb92cc0: stur            w16, [x2, #0x1b]
    // 0xb92cc4: ldur            x0, [fp, #-0x38]
    // 0xb92cc8: StoreField: r2->field_1f = r0
    //     0xb92cc8: stur            w0, [x2, #0x1f]
    // 0xb92ccc: r16 = Instance_SizedBox
    //     0xb92ccc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb92cd0: ldr             x16, [x16, #0x448]
    // 0xb92cd4: StoreField: r2->field_23 = r16
    //     0xb92cd4: stur            w16, [x2, #0x23]
    // 0xb92cd8: ldur            x0, [fp, #-8]
    // 0xb92cdc: StoreField: r2->field_27 = r0
    //     0xb92cdc: stur            w0, [x2, #0x27]
    // 0xb92ce0: r1 = <Widget>
    //     0xb92ce0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb92ce4: r0 = AllocateGrowableArray()
    //     0xb92ce4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb92ce8: mov             x1, x0
    // 0xb92cec: ldur            x0, [fp, #-0x18]
    // 0xb92cf0: stur            x1, [fp, #-8]
    // 0xb92cf4: StoreField: r1->field_f = r0
    //     0xb92cf4: stur            w0, [x1, #0xf]
    // 0xb92cf8: r0 = 14
    //     0xb92cf8: movz            x0, #0xe
    // 0xb92cfc: StoreField: r1->field_b = r0
    //     0xb92cfc: stur            w0, [x1, #0xb]
    // 0xb92d00: r0 = Column()
    //     0xb92d00: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb92d04: mov             x2, x0
    // 0xb92d08: r0 = Instance_Axis
    //     0xb92d08: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb92d0c: stur            x2, [fp, #-0x18]
    // 0xb92d10: StoreField: r2->field_f = r0
    //     0xb92d10: stur            w0, [x2, #0xf]
    // 0xb92d14: r0 = Instance_MainAxisAlignment
    //     0xb92d14: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb92d18: ldr             x0, [x0, #0x730]
    // 0xb92d1c: StoreField: r2->field_13 = r0
    //     0xb92d1c: stur            w0, [x2, #0x13]
    // 0xb92d20: r0 = Instance_MainAxisSize
    //     0xb92d20: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb92d24: ldr             x0, [x0, #0x738]
    // 0xb92d28: ArrayStore: r2[0] = r0  ; List_4
    //     0xb92d28: stur            w0, [x2, #0x17]
    // 0xb92d2c: r0 = Instance_CrossAxisAlignment
    //     0xb92d2c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb92d30: ldr             x0, [x0, #0x740]
    // 0xb92d34: StoreField: r2->field_1b = r0
    //     0xb92d34: stur            w0, [x2, #0x1b]
    // 0xb92d38: r0 = Instance_VerticalDirection
    //     0xb92d38: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb92d3c: ldr             x0, [x0, #0x748]
    // 0xb92d40: StoreField: r2->field_23 = r0
    //     0xb92d40: stur            w0, [x2, #0x23]
    // 0xb92d44: r0 = Instance_Clip
    //     0xb92d44: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb92d48: ldr             x0, [x0, #0x750]
    // 0xb92d4c: StoreField: r2->field_2b = r0
    //     0xb92d4c: stur            w0, [x2, #0x2b]
    // 0xb92d50: StoreField: r2->field_2f = rZR
    //     0xb92d50: stur            xzr, [x2, #0x2f]
    // 0xb92d54: ldur            x0, [fp, #-8]
    // 0xb92d58: StoreField: r2->field_b = r0
    //     0xb92d58: stur            w0, [x2, #0xb]
    // 0xb92d5c: r1 = <StackParentData>
    //     0xb92d5c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xb92d60: ldr             x1, [x1, #0x780]
    // 0xb92d64: r0 = Positioned()
    //     0xb92d64: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb92d68: mov             x3, x0
    // 0xb92d6c: r0 = 63.000000
    //     0xb92d6c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38350] 63
    //     0xb92d70: ldr             x0, [x0, #0x350]
    // 0xb92d74: stur            x3, [fp, #-8]
    // 0xb92d78: ArrayStore: r3[0] = r0  ; List_4
    //     0xb92d78: stur            w0, [x3, #0x17]
    // 0xb92d7c: ldur            x0, [fp, #-0x18]
    // 0xb92d80: StoreField: r3->field_b = r0
    //     0xb92d80: stur            w0, [x3, #0xb]
    // 0xb92d84: r1 = Null
    //     0xb92d84: mov             x1, NULL
    // 0xb92d88: r2 = 4
    //     0xb92d88: movz            x2, #0x4
    // 0xb92d8c: r0 = AllocateArray()
    //     0xb92d8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb92d90: mov             x2, x0
    // 0xb92d94: ldur            x0, [fp, #-0x10]
    // 0xb92d98: stur            x2, [fp, #-0x18]
    // 0xb92d9c: StoreField: r2->field_f = r0
    //     0xb92d9c: stur            w0, [x2, #0xf]
    // 0xb92da0: ldur            x0, [fp, #-8]
    // 0xb92da4: StoreField: r2->field_13 = r0
    //     0xb92da4: stur            w0, [x2, #0x13]
    // 0xb92da8: r1 = <Widget>
    //     0xb92da8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb92dac: r0 = AllocateGrowableArray()
    //     0xb92dac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb92db0: mov             x1, x0
    // 0xb92db4: ldur            x0, [fp, #-0x18]
    // 0xb92db8: stur            x1, [fp, #-8]
    // 0xb92dbc: StoreField: r1->field_f = r0
    //     0xb92dbc: stur            w0, [x1, #0xf]
    // 0xb92dc0: r0 = 4
    //     0xb92dc0: movz            x0, #0x4
    // 0xb92dc4: StoreField: r1->field_b = r0
    //     0xb92dc4: stur            w0, [x1, #0xb]
    // 0xb92dc8: r0 = Stack()
    //     0xb92dc8: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb92dcc: mov             x1, x0
    // 0xb92dd0: r0 = Instance_Alignment
    //     0xb92dd0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb92dd4: ldr             x0, [x0, #0x898]
    // 0xb92dd8: stur            x1, [fp, #-0x10]
    // 0xb92ddc: StoreField: r1->field_f = r0
    //     0xb92ddc: stur            w0, [x1, #0xf]
    // 0xb92de0: r0 = Instance_StackFit
    //     0xb92de0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xb92de4: ldr             x0, [x0, #0x7b8]
    // 0xb92de8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb92de8: stur            w0, [x1, #0x17]
    // 0xb92dec: r0 = Instance_Clip
    //     0xb92dec: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb92df0: ldr             x0, [x0, #0x7c0]
    // 0xb92df4: StoreField: r1->field_1b = r0
    //     0xb92df4: stur            w0, [x1, #0x1b]
    // 0xb92df8: ldur            x0, [fp, #-8]
    // 0xb92dfc: StoreField: r1->field_b = r0
    //     0xb92dfc: stur            w0, [x1, #0xb]
    // 0xb92e00: r0 = Card()
    //     0xb92e00: bl              #0xad7cd4  ; AllocateCardStub -> Card (size=0x38)
    // 0xb92e04: r1 = 4.000000
    //     0xb92e04: add             x1, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0xb92e08: ldr             x1, [x1, #0x770]
    // 0xb92e0c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb92e0c: stur            w1, [x0, #0x17]
    // 0xb92e10: r1 = true
    //     0xb92e10: add             x1, NULL, #0x20  ; true
    // 0xb92e14: StoreField: r0->field_1f = r1
    //     0xb92e14: stur            w1, [x0, #0x1f]
    // 0xb92e18: r2 = Instance_EdgeInsets
    //     0xb92e18: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb92e1c: StoreField: r0->field_27 = r2
    //     0xb92e1c: stur            w2, [x0, #0x27]
    // 0xb92e20: ldur            x2, [fp, #-0x10]
    // 0xb92e24: StoreField: r0->field_2f = r2
    //     0xb92e24: stur            w2, [x0, #0x2f]
    // 0xb92e28: StoreField: r0->field_2b = r1
    //     0xb92e28: stur            w1, [x0, #0x2b]
    // 0xb92e2c: r1 = Instance__CardVariant
    //     0xb92e2c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25778] Obj!_CardVariant@e36a41
    //     0xb92e30: ldr             x1, [x1, #0x778]
    // 0xb92e34: StoreField: r0->field_33 = r1
    //     0xb92e34: stur            w1, [x0, #0x33]
    // 0xb92e38: LeaveFrame
    //     0xb92e38: mov             SP, fp
    //     0xb92e3c: ldp             fp, lr, [SP], #0x10
    // 0xb92e40: ret
    //     0xb92e40: ret             
    // 0xb92e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb92e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb92e48: b               #0xb92af4
    // 0xb92e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92e4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb92e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92e50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb92e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb92e54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
