// lib: , url: package:nuonline/app/modules/donation/widgets/qurban_form.dart

// class id: 1050244, size: 0x8
class :: {
}

// class id: 5283, size: 0x14, field offset: 0x14
//   const constructor, 
class QurbanFormWidget extends GetView<dynamic> {

  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaebfd4, size: 0xc
    // 0xaebfd4: r0 = Instance_SizedBox
    //     0xaebfd4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaebfd8: ldr             x0, [x0, #0x950]
    // 0xaebfdc: ret
    //     0xaebfdc: ret             
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0xaebfe0, size: 0xf0
    // 0xaebfe0: EnterFrame
    //     0xaebfe0: stp             fp, lr, [SP, #-0x10]!
    //     0xaebfe4: mov             fp, SP
    // 0xaebfe8: AllocStack(0x38)
    //     0xaebfe8: sub             SP, SP, #0x38
    // 0xaebfec: SetupParameters()
    //     0xaebfec: ldr             x0, [fp, #0x10]
    //     0xaebff0: ldur            w2, [x0, #0x17]
    //     0xaebff4: add             x2, x2, HEAP, lsl #32
    //     0xaebff8: stur            x2, [fp, #-8]
    // 0xaebffc: CheckStackOverflow
    //     0xaebffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec000: cmp             SP, x16
    //     0xaec004: b.ls            #0xaec0c8
    // 0xaec008: LoadField: r1 = r2->field_f
    //     0xaec008: ldur            w1, [x2, #0xf]
    // 0xaec00c: DecompressPointer r1
    //     0xaec00c: add             x1, x1, HEAP, lsl #32
    // 0xaec010: r0 = controller()
    //     0xaec010: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec014: LoadField: r1 = r0->field_47
    //     0xaec014: ldur            w1, [x0, #0x47]
    // 0xaec018: DecompressPointer r1
    //     0xaec018: add             x1, x1, HEAP, lsl #32
    // 0xaec01c: r0 = value()
    //     0xaec01c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaec020: r1 = LoadClassIdInstr(r0)
    //     0xaec020: ldur            x1, [x0, #-1]
    //     0xaec024: ubfx            x1, x1, #0xc, #0x14
    // 0xaec028: str             x0, [SP]
    // 0xaec02c: mov             x0, x1
    // 0xaec030: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaec030: movz            x17, #0xc834
    //     0xaec034: add             lr, x0, x17
    //     0xaec038: ldr             lr, [x21, lr, lsl #3]
    //     0xaec03c: blr             lr
    // 0xaec040: r3 = LoadInt32Instr(r0)
    //     0xaec040: sbfx            x3, x0, #1, #0x1f
    //     0xaec044: tbz             w0, #0, #0xaec04c
    //     0xaec048: ldur            x3, [x0, #7]
    // 0xaec04c: stur            x3, [fp, #-0x10]
    // 0xaec050: r1 = Function '<anonymous closure>':.
    //     0xaec050: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b78] AnonymousClosure: (0xaebfd4), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaec054: ldr             x1, [x1, #0xb78]
    // 0xaec058: r2 = Null
    //     0xaec058: mov             x2, NULL
    // 0xaec05c: r0 = AllocateClosure()
    //     0xaec05c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec060: ldur            x2, [fp, #-8]
    // 0xaec064: r1 = Function '<anonymous closure>':.
    //     0xaec064: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b80] AnonymousClosure: (0xaec0d0), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaec068: ldr             x1, [x1, #0xb80]
    // 0xaec06c: stur            x0, [fp, #-8]
    // 0xaec070: r0 = AllocateClosure()
    //     0xaec070: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec074: stur            x0, [fp, #-0x18]
    // 0xaec078: r0 = ListView()
    //     0xaec078: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaec07c: stur            x0, [fp, #-0x20]
    // 0xaec080: r16 = true
    //     0xaec080: add             x16, NULL, #0x20  ; true
    // 0xaec084: r30 = Instance_NeverScrollableScrollPhysics
    //     0xaec084: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xaec088: ldr             lr, [lr, #0x290]
    // 0xaec08c: stp             lr, x16, [SP, #8]
    // 0xaec090: r16 = Instance_EdgeInsets
    //     0xaec090: add             x16, PP, #0x28, lsl #12  ; [pp+0x283e8] Obj!EdgeInsets@e12851
    //     0xaec094: ldr             x16, [x16, #0x3e8]
    // 0xaec098: str             x16, [SP]
    // 0xaec09c: mov             x1, x0
    // 0xaec0a0: ldur            x2, [fp, #-0x18]
    // 0xaec0a4: ldur            x3, [fp, #-0x10]
    // 0xaec0a8: ldur            x5, [fp, #-8]
    // 0xaec0ac: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x6, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xaec0ac: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b88] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x6, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xaec0b0: ldr             x4, [x4, #0xb88]
    // 0xaec0b4: r0 = ListView.separated()
    //     0xaec0b4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaec0b8: ldur            x0, [fp, #-0x20]
    // 0xaec0bc: LeaveFrame
    //     0xaec0bc: mov             SP, fp
    //     0xaec0c0: ldp             fp, lr, [SP], #0x10
    // 0xaec0c4: ret
    //     0xaec0c4: ret             
    // 0xaec0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec0c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec0cc: b               #0xaec008
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaec0d0, size: 0x194
    // 0xaec0d0: EnterFrame
    //     0xaec0d0: stp             fp, lr, [SP, #-0x10]!
    //     0xaec0d4: mov             fp, SP
    // 0xaec0d8: AllocStack(0x18)
    //     0xaec0d8: sub             SP, SP, #0x18
    // 0xaec0dc: SetupParameters()
    //     0xaec0dc: ldr             x0, [fp, #0x20]
    //     0xaec0e0: ldur            w1, [x0, #0x17]
    //     0xaec0e4: add             x1, x1, HEAP, lsl #32
    //     0xaec0e8: stur            x1, [fp, #-8]
    // 0xaec0ec: r1 = 1
    //     0xaec0ec: movz            x1, #0x1
    // 0xaec0f0: r0 = AllocateContext()
    //     0xaec0f0: bl              #0xec126c  ; AllocateContextStub
    // 0xaec0f4: mov             x1, x0
    // 0xaec0f8: ldur            x0, [fp, #-8]
    // 0xaec0fc: stur            x1, [fp, #-0x10]
    // 0xaec100: StoreField: r1->field_b = r0
    //     0xaec100: stur            w0, [x1, #0xb]
    // 0xaec104: ldr             x0, [fp, #0x10]
    // 0xaec108: StoreField: r1->field_f = r0
    //     0xaec108: stur            w0, [x1, #0xf]
    // 0xaec10c: r0 = Obx()
    //     0xaec10c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaec110: ldur            x2, [fp, #-0x10]
    // 0xaec114: r1 = Function '<anonymous closure>':.
    //     0xaec114: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b90] AnonymousClosure: (0xaec368), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaec118: ldr             x1, [x1, #0xb90]
    // 0xaec11c: stur            x0, [fp, #-8]
    // 0xaec120: r0 = AllocateClosure()
    //     0xaec120: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec124: mov             x1, x0
    // 0xaec128: ldur            x0, [fp, #-8]
    // 0xaec12c: StoreField: r0->field_b = r1
    //     0xaec12c: stur            w1, [x0, #0xb]
    // 0xaec130: r1 = <FlexParentData>
    //     0xaec130: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaec134: ldr             x1, [x1, #0x720]
    // 0xaec138: r0 = Expanded()
    //     0xaec138: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaec13c: mov             x1, x0
    // 0xaec140: r0 = 1
    //     0xaec140: movz            x0, #0x1
    // 0xaec144: stur            x1, [fp, #-0x18]
    // 0xaec148: StoreField: r1->field_13 = r0
    //     0xaec148: stur            x0, [x1, #0x13]
    // 0xaec14c: r0 = Instance_FlexFit
    //     0xaec14c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaec150: ldr             x0, [x0, #0x728]
    // 0xaec154: StoreField: r1->field_1b = r0
    //     0xaec154: stur            w0, [x1, #0x1b]
    // 0xaec158: ldur            x0, [fp, #-8]
    // 0xaec15c: StoreField: r1->field_b = r0
    //     0xaec15c: stur            w0, [x1, #0xb]
    // 0xaec160: r0 = IconButton()
    //     0xaec160: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xaec164: mov             x3, x0
    // 0xaec168: r0 = 20.000000
    //     0xaec168: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xaec16c: ldr             x0, [x0, #0x430]
    // 0xaec170: stur            x3, [fp, #-8]
    // 0xaec174: StoreField: r3->field_b = r0
    //     0xaec174: stur            w0, [x3, #0xb]
    // 0xaec178: r0 = Instance_VisualDensity
    //     0xaec178: add             x0, PP, #0x34, lsl #12  ; [pp+0x34b98] Obj!VisualDensity@e1c331
    //     0xaec17c: ldr             x0, [x0, #0xb98]
    // 0xaec180: StoreField: r3->field_f = r0
    //     0xaec180: stur            w0, [x3, #0xf]
    // 0xaec184: ldur            x2, [fp, #-0x10]
    // 0xaec188: r1 = Function '<anonymous closure>':.
    //     0xaec188: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ba0] AnonymousClosure: (0xaec264), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaec18c: ldr             x1, [x1, #0xba0]
    // 0xaec190: r0 = AllocateClosure()
    //     0xaec190: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec194: mov             x1, x0
    // 0xaec198: ldur            x0, [fp, #-8]
    // 0xaec19c: StoreField: r0->field_3b = r1
    //     0xaec19c: stur            w1, [x0, #0x3b]
    // 0xaec1a0: r1 = false
    //     0xaec1a0: add             x1, NULL, #0x30  ; false
    // 0xaec1a4: StoreField: r0->field_47 = r1
    //     0xaec1a4: stur            w1, [x0, #0x47]
    // 0xaec1a8: r1 = Instance_Icon
    //     0xaec1a8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ba8] Obj!Icon@e245b1
    //     0xaec1ac: ldr             x1, [x1, #0xba8]
    // 0xaec1b0: StoreField: r0->field_1f = r1
    //     0xaec1b0: stur            w1, [x0, #0x1f]
    // 0xaec1b4: r1 = Instance__IconButtonVariant
    //     0xaec1b4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xaec1b8: ldr             x1, [x1, #0xf78]
    // 0xaec1bc: StoreField: r0->field_63 = r1
    //     0xaec1bc: stur            w1, [x0, #0x63]
    // 0xaec1c0: r1 = Null
    //     0xaec1c0: mov             x1, NULL
    // 0xaec1c4: r2 = 4
    //     0xaec1c4: movz            x2, #0x4
    // 0xaec1c8: r0 = AllocateArray()
    //     0xaec1c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaec1cc: mov             x2, x0
    // 0xaec1d0: ldur            x0, [fp, #-0x18]
    // 0xaec1d4: stur            x2, [fp, #-0x10]
    // 0xaec1d8: StoreField: r2->field_f = r0
    //     0xaec1d8: stur            w0, [x2, #0xf]
    // 0xaec1dc: ldur            x0, [fp, #-8]
    // 0xaec1e0: StoreField: r2->field_13 = r0
    //     0xaec1e0: stur            w0, [x2, #0x13]
    // 0xaec1e4: r1 = <Widget>
    //     0xaec1e4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaec1e8: r0 = AllocateGrowableArray()
    //     0xaec1e8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaec1ec: mov             x1, x0
    // 0xaec1f0: ldur            x0, [fp, #-0x10]
    // 0xaec1f4: stur            x1, [fp, #-8]
    // 0xaec1f8: StoreField: r1->field_f = r0
    //     0xaec1f8: stur            w0, [x1, #0xf]
    // 0xaec1fc: r0 = 4
    //     0xaec1fc: movz            x0, #0x4
    // 0xaec200: StoreField: r1->field_b = r0
    //     0xaec200: stur            w0, [x1, #0xb]
    // 0xaec204: r0 = Row()
    //     0xaec204: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaec208: r1 = Instance_Axis
    //     0xaec208: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaec20c: StoreField: r0->field_f = r1
    //     0xaec20c: stur            w1, [x0, #0xf]
    // 0xaec210: r1 = Instance_MainAxisAlignment
    //     0xaec210: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaec214: ldr             x1, [x1, #0x730]
    // 0xaec218: StoreField: r0->field_13 = r1
    //     0xaec218: stur            w1, [x0, #0x13]
    // 0xaec21c: r1 = Instance_MainAxisSize
    //     0xaec21c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaec220: ldr             x1, [x1, #0x738]
    // 0xaec224: ArrayStore: r0[0] = r1  ; List_4
    //     0xaec224: stur            w1, [x0, #0x17]
    // 0xaec228: r1 = Instance_CrossAxisAlignment
    //     0xaec228: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaec22c: ldr             x1, [x1, #0x740]
    // 0xaec230: StoreField: r0->field_1b = r1
    //     0xaec230: stur            w1, [x0, #0x1b]
    // 0xaec234: r1 = Instance_VerticalDirection
    //     0xaec234: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaec238: ldr             x1, [x1, #0x748]
    // 0xaec23c: StoreField: r0->field_23 = r1
    //     0xaec23c: stur            w1, [x0, #0x23]
    // 0xaec240: r1 = Instance_Clip
    //     0xaec240: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaec244: ldr             x1, [x1, #0x750]
    // 0xaec248: StoreField: r0->field_2b = r1
    //     0xaec248: stur            w1, [x0, #0x2b]
    // 0xaec24c: StoreField: r0->field_2f = rZR
    //     0xaec24c: stur            xzr, [x0, #0x2f]
    // 0xaec250: ldur            x1, [fp, #-8]
    // 0xaec254: StoreField: r0->field_b = r1
    //     0xaec254: stur            w1, [x0, #0xb]
    // 0xaec258: LeaveFrame
    //     0xaec258: mov             SP, fp
    //     0xaec25c: ldp             fp, lr, [SP], #0x10
    // 0xaec260: ret
    //     0xaec260: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaec264, size: 0x78
    // 0xaec264: EnterFrame
    //     0xaec264: stp             fp, lr, [SP, #-0x10]!
    //     0xaec268: mov             fp, SP
    // 0xaec26c: AllocStack(0x8)
    //     0xaec26c: sub             SP, SP, #8
    // 0xaec270: SetupParameters()
    //     0xaec270: ldr             x0, [fp, #0x10]
    //     0xaec274: ldur            w2, [x0, #0x17]
    //     0xaec278: add             x2, x2, HEAP, lsl #32
    //     0xaec27c: stur            x2, [fp, #-8]
    // 0xaec280: CheckStackOverflow
    //     0xaec280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec284: cmp             SP, x16
    //     0xaec288: b.ls            #0xaec2d4
    // 0xaec28c: LoadField: r0 = r2->field_b
    //     0xaec28c: ldur            w0, [x2, #0xb]
    // 0xaec290: DecompressPointer r0
    //     0xaec290: add             x0, x0, HEAP, lsl #32
    // 0xaec294: LoadField: r1 = r0->field_f
    //     0xaec294: ldur            w1, [x0, #0xf]
    // 0xaec298: DecompressPointer r1
    //     0xaec298: add             x1, x1, HEAP, lsl #32
    // 0xaec29c: r0 = controller()
    //     0xaec29c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec2a0: mov             x1, x0
    // 0xaec2a4: ldur            x0, [fp, #-8]
    // 0xaec2a8: LoadField: r2 = r0->field_f
    //     0xaec2a8: ldur            w2, [x0, #0xf]
    // 0xaec2ac: DecompressPointer r2
    //     0xaec2ac: add             x2, x2, HEAP, lsl #32
    // 0xaec2b0: r0 = LoadInt32Instr(r2)
    //     0xaec2b0: sbfx            x0, x2, #1, #0x1f
    //     0xaec2b4: tbz             w2, #0, #0xaec2bc
    //     0xaec2b8: ldur            x0, [x2, #7]
    // 0xaec2bc: mov             x2, x0
    // 0xaec2c0: r0 = removePassenger()
    //     0xaec2c0: bl              #0xaec2dc  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::removePassenger
    // 0xaec2c4: r0 = Null
    //     0xaec2c4: mov             x0, NULL
    // 0xaec2c8: LeaveFrame
    //     0xaec2c8: mov             SP, fp
    //     0xaec2cc: ldp             fp, lr, [SP], #0x10
    // 0xaec2d0: ret
    //     0xaec2d0: ret             
    // 0xaec2d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec2d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec2d8: b               #0xaec28c
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xaec368, size: 0x2c8
    // 0xaec368: EnterFrame
    //     0xaec368: stp             fp, lr, [SP, #-0x10]!
    //     0xaec36c: mov             fp, SP
    // 0xaec370: AllocStack(0x60)
    //     0xaec370: sub             SP, SP, #0x60
    // 0xaec374: SetupParameters()
    //     0xaec374: ldr             x0, [fp, #0x10]
    //     0xaec378: ldur            w2, [x0, #0x17]
    //     0xaec37c: add             x2, x2, HEAP, lsl #32
    //     0xaec380: stur            x2, [fp, #-0x10]
    // 0xaec384: CheckStackOverflow
    //     0xaec384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec388: cmp             SP, x16
    //     0xaec38c: b.ls            #0xaec628
    // 0xaec390: LoadField: r0 = r2->field_b
    //     0xaec390: ldur            w0, [x2, #0xb]
    // 0xaec394: DecompressPointer r0
    //     0xaec394: add             x0, x0, HEAP, lsl #32
    // 0xaec398: stur            x0, [fp, #-8]
    // 0xaec39c: LoadField: r1 = r0->field_f
    //     0xaec39c: ldur            w1, [x0, #0xf]
    // 0xaec3a0: DecompressPointer r1
    //     0xaec3a0: add             x1, x1, HEAP, lsl #32
    // 0xaec3a4: r0 = controller()
    //     0xaec3a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec3a8: LoadField: r1 = r0->field_47
    //     0xaec3a8: ldur            w1, [x0, #0x47]
    // 0xaec3ac: DecompressPointer r1
    //     0xaec3ac: add             x1, x1, HEAP, lsl #32
    // 0xaec3b0: r0 = value()
    //     0xaec3b0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaec3b4: r1 = LoadClassIdInstr(r0)
    //     0xaec3b4: ldur            x1, [x0, #-1]
    //     0xaec3b8: ubfx            x1, x1, #0xc, #0x14
    // 0xaec3bc: str             x0, [SP]
    // 0xaec3c0: mov             x0, x1
    // 0xaec3c4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaec3c4: movz            x17, #0xc834
    //     0xaec3c8: add             lr, x0, x17
    //     0xaec3cc: ldr             lr, [x21, lr, lsl #3]
    //     0xaec3d0: blr             lr
    // 0xaec3d4: ldur            x2, [fp, #-0x10]
    // 0xaec3d8: LoadField: r1 = r2->field_f
    //     0xaec3d8: ldur            w1, [x2, #0xf]
    // 0xaec3dc: DecompressPointer r1
    //     0xaec3dc: add             x1, x1, HEAP, lsl #32
    // 0xaec3e0: r3 = LoadInt32Instr(r0)
    //     0xaec3e0: sbfx            x3, x0, #1, #0x1f
    //     0xaec3e4: tbz             w0, #0, #0xaec3ec
    //     0xaec3e8: ldur            x3, [x0, #7]
    // 0xaec3ec: r0 = LoadInt32Instr(r1)
    //     0xaec3ec: sbfx            x0, x1, #1, #0x1f
    //     0xaec3f0: tbz             w1, #0, #0xaec3f8
    //     0xaec3f4: ldur            x0, [x1, #7]
    // 0xaec3f8: add             x4, x3, x0
    // 0xaec3fc: r0 = BoxInt64Instr(r4)
    //     0xaec3fc: sbfiz           x0, x4, #1, #0x1f
    //     0xaec400: cmp             x4, x0, asr #1
    //     0xaec404: b.eq            #0xaec410
    //     0xaec408: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaec40c: stur            x4, [x0, #7]
    // 0xaec410: r1 = <int>
    //     0xaec410: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaec414: stur            x0, [fp, #-0x18]
    // 0xaec418: r0 = ValueKey()
    //     0xaec418: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xaec41c: mov             x2, x0
    // 0xaec420: ldur            x0, [fp, #-0x18]
    // 0xaec424: stur            x2, [fp, #-0x20]
    // 0xaec428: StoreField: r2->field_b = r0
    //     0xaec428: stur            w0, [x2, #0xb]
    // 0xaec42c: ldur            x0, [fp, #-8]
    // 0xaec430: LoadField: r1 = r0->field_f
    //     0xaec430: ldur            w1, [x0, #0xf]
    // 0xaec434: DecompressPointer r1
    //     0xaec434: add             x1, x1, HEAP, lsl #32
    // 0xaec438: r0 = controller()
    //     0xaec438: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec43c: LoadField: r1 = r0->field_47
    //     0xaec43c: ldur            w1, [x0, #0x47]
    // 0xaec440: DecompressPointer r1
    //     0xaec440: add             x1, x1, HEAP, lsl #32
    // 0xaec444: ldur            x2, [fp, #-0x10]
    // 0xaec448: LoadField: r0 = r2->field_f
    //     0xaec448: ldur            w0, [x2, #0xf]
    // 0xaec44c: DecompressPointer r0
    //     0xaec44c: add             x0, x0, HEAP, lsl #32
    // 0xaec450: stur            x0, [fp, #-8]
    // 0xaec454: r0 = value()
    //     0xaec454: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaec458: r1 = LoadClassIdInstr(r0)
    //     0xaec458: ldur            x1, [x0, #-1]
    //     0xaec45c: ubfx            x1, x1, #0xc, #0x14
    // 0xaec460: ldur            x16, [fp, #-8]
    // 0xaec464: stp             x16, x0, [SP]
    // 0xaec468: mov             x0, x1
    // 0xaec46c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaec46c: movz            x17, #0x3037
    //     0xaec470: movk            x17, #0x1, lsl #16
    //     0xaec474: add             lr, x0, x17
    //     0xaec478: ldr             lr, [x21, lr, lsl #3]
    //     0xaec47c: blr             lr
    // 0xaec480: r1 = Function '<anonymous closure>': static.
    //     0xaec480: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaec484: ldr             x1, [x1, #0xda0]
    // 0xaec488: r2 = Null
    //     0xaec488: mov             x2, NULL
    // 0xaec48c: stur            x0, [fp, #-8]
    // 0xaec490: r0 = AllocateClosure()
    //     0xaec490: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec494: r1 = <String>
    //     0xaec494: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaec498: stur            x0, [fp, #-0x18]
    // 0xaec49c: StoreField: r0->field_b = r1
    //     0xaec49c: stur            w1, [x0, #0xb]
    // 0xaec4a0: r16 = <String>
    //     0xaec4a0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaec4a4: str             x16, [SP, #8]
    // 0xaec4a8: r2 = 18
    //     0xaec4a8: movz            x2, #0x12
    // 0xaec4ac: str             x2, [SP]
    // 0xaec4b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaec4b0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaec4b4: r0 = maxLength()
    //     0xaec4b4: bl              #0xae4b84  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::maxLength
    // 0xaec4b8: r1 = Null
    //     0xaec4b8: mov             x1, NULL
    // 0xaec4bc: r2 = 4
    //     0xaec4bc: movz            x2, #0x4
    // 0xaec4c0: stur            x0, [fp, #-0x28]
    // 0xaec4c4: r0 = AllocateArray()
    //     0xaec4c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaec4c8: mov             x2, x0
    // 0xaec4cc: ldur            x0, [fp, #-0x18]
    // 0xaec4d0: stur            x2, [fp, #-0x30]
    // 0xaec4d4: StoreField: r2->field_f = r0
    //     0xaec4d4: stur            w0, [x2, #0xf]
    // 0xaec4d8: ldur            x0, [fp, #-0x28]
    // 0xaec4dc: StoreField: r2->field_13 = r0
    //     0xaec4dc: stur            w0, [x2, #0x13]
    // 0xaec4e0: r1 = <(dynamic this, String?) => String?>
    //     0xaec4e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xaec4e4: ldr             x1, [x1, #0xd98]
    // 0xaec4e8: r0 = AllocateGrowableArray()
    //     0xaec4e8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaec4ec: mov             x1, x0
    // 0xaec4f0: ldur            x0, [fp, #-0x30]
    // 0xaec4f4: stur            x1, [fp, #-0x18]
    // 0xaec4f8: StoreField: r1->field_f = r0
    //     0xaec4f8: stur            w0, [x1, #0xf]
    // 0xaec4fc: r0 = 4
    //     0xaec4fc: movz            x0, #0x4
    // 0xaec500: StoreField: r1->field_b = r0
    //     0xaec500: stur            w0, [x1, #0xb]
    // 0xaec504: r1 = 1
    //     0xaec504: movz            x1, #0x1
    // 0xaec508: r0 = AllocateContext()
    //     0xaec508: bl              #0xec126c  ; AllocateContextStub
    // 0xaec50c: mov             x1, x0
    // 0xaec510: ldur            x0, [fp, #-0x18]
    // 0xaec514: StoreField: r1->field_f = r0
    //     0xaec514: stur            w0, [x1, #0xf]
    // 0xaec518: mov             x2, x1
    // 0xaec51c: r1 = Function '<anonymous closure>': static.
    //     0xaec51c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaec520: ldr             x1, [x1, #0xda8]
    // 0xaec524: r0 = AllocateClosure()
    //     0xaec524: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec528: r1 = <String>
    //     0xaec528: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaec52c: stur            x0, [fp, #-0x18]
    // 0xaec530: StoreField: r0->field_b = r1
    //     0xaec530: stur            w1, [x0, #0xb]
    // 0xaec534: r16 = "[a-zA-Z ]"
    //     0xaec534: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xaec538: ldr             x16, [x16, #0xb20]
    // 0xaec53c: stp             x16, NULL, [SP, #0x20]
    // 0xaec540: r16 = false
    //     0xaec540: add             x16, NULL, #0x30  ; false
    // 0xaec544: r30 = true
    //     0xaec544: add             lr, NULL, #0x20  ; true
    // 0xaec548: stp             lr, x16, [SP, #0x10]
    // 0xaec54c: r16 = false
    //     0xaec54c: add             x16, NULL, #0x30  ; false
    // 0xaec550: r30 = false
    //     0xaec550: add             lr, NULL, #0x30  ; false
    // 0xaec554: stp             lr, x16, [SP]
    // 0xaec558: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xaec558: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xaec55c: r0 = _RegExp()
    //     0xaec55c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xaec560: stur            x0, [fp, #-0x28]
    // 0xaec564: r0 = FilteringTextInputFormatter()
    //     0xaec564: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xaec568: mov             x3, x0
    // 0xaec56c: ldur            x0, [fp, #-0x28]
    // 0xaec570: stur            x3, [fp, #-0x30]
    // 0xaec574: StoreField: r3->field_7 = r0
    //     0xaec574: stur            w0, [x3, #7]
    // 0xaec578: r0 = true
    //     0xaec578: add             x0, NULL, #0x20  ; true
    // 0xaec57c: StoreField: r3->field_b = r0
    //     0xaec57c: stur            w0, [x3, #0xb]
    // 0xaec580: r0 = ""
    //     0xaec580: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaec584: StoreField: r3->field_f = r0
    //     0xaec584: stur            w0, [x3, #0xf]
    // 0xaec588: r1 = Null
    //     0xaec588: mov             x1, NULL
    // 0xaec58c: r2 = 2
    //     0xaec58c: movz            x2, #0x2
    // 0xaec590: r0 = AllocateArray()
    //     0xaec590: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaec594: mov             x2, x0
    // 0xaec598: ldur            x0, [fp, #-0x30]
    // 0xaec59c: stur            x2, [fp, #-0x28]
    // 0xaec5a0: StoreField: r2->field_f = r0
    //     0xaec5a0: stur            w0, [x2, #0xf]
    // 0xaec5a4: r1 = <TextInputFormatter>
    //     0xaec5a4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xaec5a8: ldr             x1, [x1, #0x788]
    // 0xaec5ac: r0 = AllocateGrowableArray()
    //     0xaec5ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaec5b0: mov             x2, x0
    // 0xaec5b4: ldur            x0, [fp, #-0x28]
    // 0xaec5b8: stur            x2, [fp, #-0x30]
    // 0xaec5bc: StoreField: r2->field_f = r0
    //     0xaec5bc: stur            w0, [x2, #0xf]
    // 0xaec5c0: r0 = 2
    //     0xaec5c0: movz            x0, #0x2
    // 0xaec5c4: StoreField: r2->field_b = r0
    //     0xaec5c4: stur            w0, [x2, #0xb]
    // 0xaec5c8: r1 = <String>
    //     0xaec5c8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaec5cc: r0 = TextFormField()
    //     0xaec5cc: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xaec5d0: ldur            x2, [fp, #-0x10]
    // 0xaec5d4: r1 = Function '<anonymous closure>':.
    //     0xaec5d4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34bb0] AnonymousClosure: (0xaec630), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaec5d8: ldr             x1, [x1, #0xbb0]
    // 0xaec5dc: stur            x0, [fp, #-0x10]
    // 0xaec5e0: r0 = AllocateClosure()
    //     0xaec5e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec5e4: ldur            x16, [fp, #-0x20]
    // 0xaec5e8: ldur            lr, [fp, #-8]
    // 0xaec5ec: stp             lr, x16, [SP, #0x18]
    // 0xaec5f0: ldur            x16, [fp, #-0x18]
    // 0xaec5f4: stp             x16, x0, [SP, #8]
    // 0xaec5f8: ldur            x16, [fp, #-0x30]
    // 0xaec5fc: str             x16, [SP]
    // 0xaec600: ldur            x1, [fp, #-0x10]
    // 0xaec604: r2 = Instance_InputDecoration
    //     0xaec604: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xaec608: ldr             x2, [x2, #0xb28]
    // 0xaec60c: r4 = const [0, 0x7, 0x5, 0x2, initialValue, 0x3, inputFormatters, 0x6, key, 0x2, onChanged, 0x4, validator, 0x5, null]
    //     0xaec60c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34bb8] List(15) [0, 0x7, 0x5, 0x2, "initialValue", 0x3, "inputFormatters", 0x6, "key", 0x2, "onChanged", 0x4, "validator", 0x5, Null]
    //     0xaec610: ldr             x4, [x4, #0xbb8]
    // 0xaec614: r0 = TextFormField()
    //     0xaec614: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xaec618: ldur            x0, [fp, #-0x10]
    // 0xaec61c: LeaveFrame
    //     0xaec61c: mov             SP, fp
    //     0xaec620: ldp             fp, lr, [SP], #0x10
    // 0xaec624: ret
    //     0xaec624: ret             
    // 0xaec628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec62c: b               #0xaec390
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xaec630, size: 0x7c
    // 0xaec630: EnterFrame
    //     0xaec630: stp             fp, lr, [SP, #-0x10]!
    //     0xaec634: mov             fp, SP
    // 0xaec638: AllocStack(0x8)
    //     0xaec638: sub             SP, SP, #8
    // 0xaec63c: SetupParameters()
    //     0xaec63c: ldr             x0, [fp, #0x18]
    //     0xaec640: ldur            w2, [x0, #0x17]
    //     0xaec644: add             x2, x2, HEAP, lsl #32
    //     0xaec648: stur            x2, [fp, #-8]
    // 0xaec64c: CheckStackOverflow
    //     0xaec64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec650: cmp             SP, x16
    //     0xaec654: b.ls            #0xaec6a4
    // 0xaec658: LoadField: r0 = r2->field_b
    //     0xaec658: ldur            w0, [x2, #0xb]
    // 0xaec65c: DecompressPointer r0
    //     0xaec65c: add             x0, x0, HEAP, lsl #32
    // 0xaec660: LoadField: r1 = r0->field_f
    //     0xaec660: ldur            w1, [x0, #0xf]
    // 0xaec664: DecompressPointer r1
    //     0xaec664: add             x1, x1, HEAP, lsl #32
    // 0xaec668: r0 = controller()
    //     0xaec668: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaec66c: mov             x1, x0
    // 0xaec670: ldur            x0, [fp, #-8]
    // 0xaec674: LoadField: r2 = r0->field_f
    //     0xaec674: ldur            w2, [x0, #0xf]
    // 0xaec678: DecompressPointer r2
    //     0xaec678: add             x2, x2, HEAP, lsl #32
    // 0xaec67c: r0 = LoadInt32Instr(r2)
    //     0xaec67c: sbfx            x0, x2, #1, #0x1f
    //     0xaec680: tbz             w2, #0, #0xaec688
    //     0xaec684: ldur            x0, [x2, #7]
    // 0xaec688: mov             x2, x0
    // 0xaec68c: ldr             x3, [fp, #0x10]
    // 0xaec690: r0 = updatePassenger()
    //     0xaec690: bl              #0xaec6ac  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::updatePassenger
    // 0xaec694: r0 = Null
    //     0xaec694: mov             x0, NULL
    // 0xaec698: LeaveFrame
    //     0xaec698: mov             SP, fp
    //     0xaec69c: ldp             fp, lr, [SP], #0x10
    // 0xaec6a0: ret
    //     0xaec6a0: ret             
    // 0xaec6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec6a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec6a8: b               #0xaec658
  }
  _ build(/* No info */) {
    // ** addr: 0xaeca2c, size: 0x864
    // 0xaeca2c: EnterFrame
    //     0xaeca2c: stp             fp, lr, [SP, #-0x10]!
    //     0xaeca30: mov             fp, SP
    // 0xaeca34: AllocStack(0x68)
    //     0xaeca34: sub             SP, SP, #0x68
    // 0xaeca38: SetupParameters(QurbanFormWidget this /* r1 => r1, fp-0x8 */)
    //     0xaeca38: stur            x1, [fp, #-8]
    // 0xaeca3c: CheckStackOverflow
    //     0xaeca3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeca40: cmp             SP, x16
    //     0xaeca44: b.ls            #0xaed288
    // 0xaeca48: r1 = 1
    //     0xaeca48: movz            x1, #0x1
    // 0xaeca4c: r0 = AllocateContext()
    //     0xaeca4c: bl              #0xec126c  ; AllocateContextStub
    // 0xaeca50: mov             x2, x0
    // 0xaeca54: ldur            x0, [fp, #-8]
    // 0xaeca58: stur            x2, [fp, #-0x10]
    // 0xaeca5c: StoreField: r2->field_f = r0
    //     0xaeca5c: stur            w0, [x2, #0xf]
    // 0xaeca60: mov             x1, x0
    // 0xaeca64: r0 = controller()
    //     0xaeca64: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeca68: LoadField: r1 = r0->field_3f
    //     0xaeca68: ldur            w1, [x0, #0x3f]
    // 0xaeca6c: DecompressPointer r1
    //     0xaeca6c: add             x1, x1, HEAP, lsl #32
    // 0xaeca70: cmp             w1, NULL
    // 0xaeca74: b.ne            #0xaeca80
    // 0xaeca78: r0 = Null
    //     0xaeca78: mov             x0, NULL
    // 0xaeca7c: b               #0xaeca88
    // 0xaeca80: LoadField: r0 = r1->field_f
    //     0xaeca80: ldur            w0, [x1, #0xf]
    // 0xaeca84: DecompressPointer r0
    //     0xaeca84: add             x0, x0, HEAP, lsl #32
    // 0xaeca88: cmp             w0, NULL
    // 0xaeca8c: b.ne            #0xaeca94
    // 0xaeca90: r0 = ""
    //     0xaeca90: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaeca94: stur            x0, [fp, #-0x18]
    // 0xaeca98: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaeca98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaeca9c: ldr             x0, [x0, #0x2670]
    //     0xaecaa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaecaa4: cmp             w0, w16
    //     0xaecaa8: b.ne            #0xaecab4
    //     0xaecaac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaecab0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaecab4: r0 = GetNavigation.textTheme()
    //     0xaecab4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaecab8: LoadField: r1 = r0->field_f
    //     0xaecab8: ldur            w1, [x0, #0xf]
    // 0xaecabc: DecompressPointer r1
    //     0xaecabc: add             x1, x1, HEAP, lsl #32
    // 0xaecac0: stur            x1, [fp, #-0x20]
    // 0xaecac4: r0 = Text()
    //     0xaecac4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaecac8: mov             x3, x0
    // 0xaecacc: ldur            x0, [fp, #-0x18]
    // 0xaecad0: stur            x3, [fp, #-0x28]
    // 0xaecad4: StoreField: r3->field_b = r0
    //     0xaecad4: stur            w0, [x3, #0xb]
    // 0xaecad8: ldur            x0, [fp, #-0x20]
    // 0xaecadc: StoreField: r3->field_13 = r0
    //     0xaecadc: stur            w0, [x3, #0x13]
    // 0xaecae0: r1 = <Widget>
    //     0xaecae0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaecae4: r2 = 28
    //     0xaecae4: movz            x2, #0x1c
    // 0xaecae8: r0 = AllocateArray()
    //     0xaecae8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaecaec: mov             x2, x0
    // 0xaecaf0: ldur            x0, [fp, #-0x28]
    // 0xaecaf4: stur            x2, [fp, #-0x18]
    // 0xaecaf8: StoreField: r2->field_f = r0
    //     0xaecaf8: stur            w0, [x2, #0xf]
    // 0xaecafc: r16 = Instance_SizedBox
    //     0xaecafc: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xaecb00: ldr             x16, [x16, #0x4a0]
    // 0xaecb04: StoreField: r2->field_13 = r16
    //     0xaecb04: stur            w16, [x2, #0x13]
    // 0xaecb08: ldur            x1, [fp, #-8]
    // 0xaecb0c: r0 = controller()
    //     0xaecb0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaecb10: mov             x1, x0
    // 0xaecb14: r0 = qurban()
    //     0xaecb14: bl              #0x8f4cfc  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::qurban
    // 0xaecb18: cmp             w0, NULL
    // 0xaecb1c: b.ne            #0xaecb28
    // 0xaecb20: r0 = Null
    //     0xaecb20: mov             x0, NULL
    // 0xaecb24: b               #0xaecb34
    // 0xaecb28: LoadField: r1 = r0->field_7
    //     0xaecb28: ldur            w1, [x0, #7]
    // 0xaecb2c: DecompressPointer r1
    //     0xaecb2c: add             x1, x1, HEAP, lsl #32
    // 0xaecb30: mov             x0, x1
    // 0xaecb34: cmp             w0, NULL
    // 0xaecb38: b.ne            #0xaecb44
    // 0xaecb3c: r0 = "Bobot 25-28 Kg"
    //     0xaecb3c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ae8] "Bobot 25-28 Kg"
    //     0xaecb40: ldr             x0, [x0, #0xae8]
    // 0xaecb44: ldur            x1, [fp, #-0x18]
    // 0xaecb48: stur            x0, [fp, #-0x20]
    // 0xaecb4c: r0 = GetNavigation.textTheme()
    //     0xaecb4c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaecb50: LoadField: r1 = r0->field_27
    //     0xaecb50: ldur            w1, [x0, #0x27]
    // 0xaecb54: DecompressPointer r1
    //     0xaecb54: add             x1, x1, HEAP, lsl #32
    // 0xaecb58: stur            x1, [fp, #-0x28]
    // 0xaecb5c: r0 = Text()
    //     0xaecb5c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaecb60: mov             x1, x0
    // 0xaecb64: ldur            x0, [fp, #-0x20]
    // 0xaecb68: StoreField: r1->field_b = r0
    //     0xaecb68: stur            w0, [x1, #0xb]
    // 0xaecb6c: ldur            x0, [fp, #-0x28]
    // 0xaecb70: StoreField: r1->field_13 = r0
    //     0xaecb70: stur            w0, [x1, #0x13]
    // 0xaecb74: mov             x0, x1
    // 0xaecb78: ldur            x1, [fp, #-0x18]
    // 0xaecb7c: ArrayStore: r1[2] = r0  ; List_4
    //     0xaecb7c: add             x25, x1, #0x17
    //     0xaecb80: str             w0, [x25]
    //     0xaecb84: tbz             w0, #0, #0xaecba0
    //     0xaecb88: ldurb           w16, [x1, #-1]
    //     0xaecb8c: ldurb           w17, [x0, #-1]
    //     0xaecb90: and             x16, x17, x16, lsr #2
    //     0xaecb94: tst             x16, HEAP, lsr #32
    //     0xaecb98: b.eq            #0xaecba0
    //     0xaecb9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaecba0: ldur            x0, [fp, #-0x18]
    // 0xaecba4: r16 = Instance_SizedBox
    //     0xaecba4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xaecba8: ldr             x16, [x16, #0xfb0]
    // 0xaecbac: StoreField: r0->field_1b = r16
    //     0xaecbac: stur            w16, [x0, #0x1b]
    // 0xaecbb0: r1 = Null
    //     0xaecbb0: mov             x1, NULL
    // 0xaecbb4: r2 = 4
    //     0xaecbb4: movz            x2, #0x4
    // 0xaecbb8: r0 = AllocateArray()
    //     0xaecbb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaecbbc: stur            x0, [fp, #-0x20]
    // 0xaecbc0: r16 = "Rp"
    //     0xaecbc0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xaecbc4: ldr             x16, [x16, #0xf90]
    // 0xaecbc8: StoreField: r0->field_f = r16
    //     0xaecbc8: stur            w16, [x0, #0xf]
    // 0xaecbcc: ldur            x1, [fp, #-8]
    // 0xaecbd0: r0 = controller()
    //     0xaecbd0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaecbd4: mov             x1, x0
    // 0xaecbd8: r0 = qurban()
    //     0xaecbd8: bl              #0x8f4cfc  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::qurban
    // 0xaecbdc: cmp             w0, NULL
    // 0xaecbe0: b.ne            #0xaecbec
    // 0xaecbe4: r0 = Null
    //     0xaecbe4: mov             x0, NULL
    // 0xaecbe8: b               #0xaecc0c
    // 0xaecbec: LoadField: r1 = r0->field_b
    //     0xaecbec: ldur            w1, [x0, #0xb]
    // 0xaecbf0: DecompressPointer r1
    //     0xaecbf0: add             x1, x1, HEAP, lsl #32
    // 0xaecbf4: cmp             w1, NULL
    // 0xaecbf8: b.ne            #0xaecc04
    // 0xaecbfc: r0 = Null
    //     0xaecbfc: mov             x0, NULL
    // 0xaecc00: b               #0xaecc0c
    // 0xaecc04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaecc04: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaecc08: r0 = NumExtension.idr()
    //     0xaecc08: bl              #0xadf068  ; [package:nuonline/common/extensions/int_extension.dart] ::NumExtension.idr
    // 0xaecc0c: cmp             w0, NULL
    // 0xaecc10: b.ne            #0xaecc1c
    // 0xaecc14: r0 = 5200000
    //     0xaecc14: movz            x0, #0x5880
    //     0xaecc18: movk            x0, #0x4f, lsl #16
    // 0xaecc1c: ldur            x1, [fp, #-0x20]
    // 0xaecc20: ArrayStore: r1[1] = r0  ; List_4
    //     0xaecc20: add             x25, x1, #0x13
    //     0xaecc24: str             w0, [x25]
    //     0xaecc28: tbz             w0, #0, #0xaecc44
    //     0xaecc2c: ldurb           w16, [x1, #-1]
    //     0xaecc30: ldurb           w17, [x0, #-1]
    //     0xaecc34: and             x16, x17, x16, lsr #2
    //     0xaecc38: tst             x16, HEAP, lsr #32
    //     0xaecc3c: b.eq            #0xaecc44
    //     0xaecc40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaecc44: ldur            x16, [fp, #-0x20]
    // 0xaecc48: str             x16, [SP]
    // 0xaecc4c: r0 = _interpolate()
    //     0xaecc4c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaecc50: stur            x0, [fp, #-0x20]
    // 0xaecc54: r0 = GetNavigation.textTheme()
    //     0xaecc54: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaecc58: LoadField: r1 = r0->field_f
    //     0xaecc58: ldur            w1, [x0, #0xf]
    // 0xaecc5c: DecompressPointer r1
    //     0xaecc5c: add             x1, x1, HEAP, lsl #32
    // 0xaecc60: stur            x1, [fp, #-0x28]
    // 0xaecc64: cmp             w1, NULL
    // 0xaecc68: b.ne            #0xaecc74
    // 0xaecc6c: r2 = Null
    //     0xaecc6c: mov             x2, NULL
    // 0xaecc70: b               #0xaecca8
    // 0xaecc74: r0 = GetNavigation.theme()
    //     0xaecc74: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaecc78: LoadField: r1 = r0->field_3f
    //     0xaecc78: ldur            w1, [x0, #0x3f]
    // 0xaecc7c: DecompressPointer r1
    //     0xaecc7c: add             x1, x1, HEAP, lsl #32
    // 0xaecc80: LoadField: r0 = r1->field_b
    //     0xaecc80: ldur            w0, [x1, #0xb]
    // 0xaecc84: DecompressPointer r0
    //     0xaecc84: add             x0, x0, HEAP, lsl #32
    // 0xaecc88: r16 = 18.000000
    //     0xaecc88: add             x16, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xaecc8c: ldr             x16, [x16, #0x958]
    // 0xaecc90: stp             x0, x16, [SP]
    // 0xaecc94: ldur            x1, [fp, #-0x28]
    // 0xaecc98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaecc98: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaecc9c: ldr             x4, [x4, #0xaa0]
    // 0xaecca0: r0 = copyWith()
    //     0xaecca0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaecca4: mov             x2, x0
    // 0xaecca8: ldur            x1, [fp, #-0x18]
    // 0xaeccac: ldur            x0, [fp, #-0x20]
    // 0xaeccb0: stur            x2, [fp, #-0x28]
    // 0xaeccb4: r0 = Text()
    //     0xaeccb4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaeccb8: mov             x2, x0
    // 0xaeccbc: ldur            x0, [fp, #-0x20]
    // 0xaeccc0: stur            x2, [fp, #-0x30]
    // 0xaeccc4: StoreField: r2->field_b = r0
    //     0xaeccc4: stur            w0, [x2, #0xb]
    // 0xaeccc8: ldur            x0, [fp, #-0x28]
    // 0xaecccc: StoreField: r2->field_13 = r0
    //     0xaecccc: stur            w0, [x2, #0x13]
    // 0xaeccd0: r1 = <FlexParentData>
    //     0xaeccd0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaeccd4: ldr             x1, [x1, #0x720]
    // 0xaeccd8: r0 = Expanded()
    //     0xaeccd8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaeccdc: mov             x1, x0
    // 0xaecce0: r0 = 1
    //     0xaecce0: movz            x0, #0x1
    // 0xaecce4: stur            x1, [fp, #-0x20]
    // 0xaecce8: StoreField: r1->field_13 = r0
    //     0xaecce8: stur            x0, [x1, #0x13]
    // 0xaeccec: r0 = Instance_FlexFit
    //     0xaeccec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaeccf0: ldr             x0, [x0, #0x728]
    // 0xaeccf4: StoreField: r1->field_1b = r0
    //     0xaeccf4: stur            w0, [x1, #0x1b]
    // 0xaeccf8: ldur            x0, [fp, #-0x30]
    // 0xaeccfc: StoreField: r1->field_b = r0
    //     0xaeccfc: stur            w0, [x1, #0xb]
    // 0xaecd00: r0 = Obx()
    //     0xaecd00: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaecd04: ldur            x2, [fp, #-0x10]
    // 0xaecd08: r1 = Function '<anonymous closure>':.
    //     0xaecd08: add             x1, PP, #0x34, lsl #12  ; [pp+0x34af0] AnonymousClosure: (0xaed978), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaecd0c: ldr             x1, [x1, #0xaf0]
    // 0xaecd10: stur            x0, [fp, #-0x28]
    // 0xaecd14: r0 = AllocateClosure()
    //     0xaecd14: bl              #0xec1630  ; AllocateClosureStub
    // 0xaecd18: mov             x1, x0
    // 0xaecd1c: ldur            x0, [fp, #-0x28]
    // 0xaecd20: StoreField: r0->field_b = r1
    //     0xaecd20: stur            w1, [x0, #0xb]
    // 0xaecd24: r1 = Null
    //     0xaecd24: mov             x1, NULL
    // 0xaecd28: r2 = 4
    //     0xaecd28: movz            x2, #0x4
    // 0xaecd2c: r0 = AllocateArray()
    //     0xaecd2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaecd30: mov             x2, x0
    // 0xaecd34: ldur            x0, [fp, #-0x20]
    // 0xaecd38: stur            x2, [fp, #-0x30]
    // 0xaecd3c: StoreField: r2->field_f = r0
    //     0xaecd3c: stur            w0, [x2, #0xf]
    // 0xaecd40: ldur            x0, [fp, #-0x28]
    // 0xaecd44: StoreField: r2->field_13 = r0
    //     0xaecd44: stur            w0, [x2, #0x13]
    // 0xaecd48: r1 = <Widget>
    //     0xaecd48: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaecd4c: r0 = AllocateGrowableArray()
    //     0xaecd4c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaecd50: mov             x1, x0
    // 0xaecd54: ldur            x0, [fp, #-0x30]
    // 0xaecd58: stur            x1, [fp, #-0x20]
    // 0xaecd5c: StoreField: r1->field_f = r0
    //     0xaecd5c: stur            w0, [x1, #0xf]
    // 0xaecd60: r2 = 4
    //     0xaecd60: movz            x2, #0x4
    // 0xaecd64: StoreField: r1->field_b = r2
    //     0xaecd64: stur            w2, [x1, #0xb]
    // 0xaecd68: r0 = Row()
    //     0xaecd68: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaecd6c: r3 = Instance_Axis
    //     0xaecd6c: ldr             x3, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaecd70: StoreField: r0->field_f = r3
    //     0xaecd70: stur            w3, [x0, #0xf]
    // 0xaecd74: r4 = Instance_MainAxisAlignment
    //     0xaecd74: add             x4, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaecd78: ldr             x4, [x4, #0x730]
    // 0xaecd7c: StoreField: r0->field_13 = r4
    //     0xaecd7c: stur            w4, [x0, #0x13]
    // 0xaecd80: r5 = Instance_MainAxisSize
    //     0xaecd80: add             x5, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaecd84: ldr             x5, [x5, #0x738]
    // 0xaecd88: ArrayStore: r0[0] = r5  ; List_4
    //     0xaecd88: stur            w5, [x0, #0x17]
    // 0xaecd8c: r6 = Instance_CrossAxisAlignment
    //     0xaecd8c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaecd90: ldr             x6, [x6, #0x740]
    // 0xaecd94: StoreField: r0->field_1b = r6
    //     0xaecd94: stur            w6, [x0, #0x1b]
    // 0xaecd98: r7 = Instance_VerticalDirection
    //     0xaecd98: add             x7, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaecd9c: ldr             x7, [x7, #0x748]
    // 0xaecda0: StoreField: r0->field_23 = r7
    //     0xaecda0: stur            w7, [x0, #0x23]
    // 0xaecda4: r8 = Instance_Clip
    //     0xaecda4: add             x8, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaecda8: ldr             x8, [x8, #0x750]
    // 0xaecdac: StoreField: r0->field_2b = r8
    //     0xaecdac: stur            w8, [x0, #0x2b]
    // 0xaecdb0: StoreField: r0->field_2f = rZR
    //     0xaecdb0: stur            xzr, [x0, #0x2f]
    // 0xaecdb4: ldur            x1, [fp, #-0x20]
    // 0xaecdb8: StoreField: r0->field_b = r1
    //     0xaecdb8: stur            w1, [x0, #0xb]
    // 0xaecdbc: ldur            x1, [fp, #-0x18]
    // 0xaecdc0: ArrayStore: r1[4] = r0  ; List_4
    //     0xaecdc0: add             x25, x1, #0x1f
    //     0xaecdc4: str             w0, [x25]
    //     0xaecdc8: tbz             w0, #0, #0xaecde4
    //     0xaecdcc: ldurb           w16, [x1, #-1]
    //     0xaecdd0: ldurb           w17, [x0, #-1]
    //     0xaecdd4: and             x16, x17, x16, lsr #2
    //     0xaecdd8: tst             x16, HEAP, lsr #32
    //     0xaecddc: b.eq            #0xaecde4
    //     0xaecde0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaecde4: ldur            x0, [fp, #-0x18]
    // 0xaecde8: r16 = Instance_SizedBox
    //     0xaecde8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaecdec: ldr             x16, [x16, #0x950]
    // 0xaecdf0: StoreField: r0->field_23 = r16
    //     0xaecdf0: stur            w16, [x0, #0x23]
    // 0xaecdf4: r1 = _ConstMap len:3
    //     0xaecdf4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaecdf8: ldr             x1, [x1, #0xbe8]
    // 0xaecdfc: r2 = 2
    //     0xaecdfc: movz            x2, #0x2
    // 0xaece00: r0 = []()
    //     0xaece00: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaece04: r16 = <Color?>
    //     0xaece04: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaece08: ldr             x16, [x16, #0x98]
    // 0xaece0c: stp             x0, x16, [SP, #8]
    // 0xaece10: r16 = Instance_MaterialColor
    //     0xaece10: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xaece14: ldr             x16, [x16, #0xe38]
    // 0xaece18: str             x16, [SP]
    // 0xaece1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaece1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaece20: r0 = mode()
    //     0xaece20: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaece24: stur            x0, [fp, #-0x20]
    // 0xaece28: r0 = Obx()
    //     0xaece28: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaece2c: ldur            x2, [fp, #-0x10]
    // 0xaece30: r1 = Function '<anonymous closure>':.
    //     0xaece30: add             x1, PP, #0x34, lsl #12  ; [pp+0x34af8] AnonymousClosure: (0xaed7cc), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaece34: ldr             x1, [x1, #0xaf8]
    // 0xaece38: stur            x0, [fp, #-0x28]
    // 0xaece3c: r0 = AllocateClosure()
    //     0xaece3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaece40: mov             x1, x0
    // 0xaece44: ldur            x0, [fp, #-0x28]
    // 0xaece48: StoreField: r0->field_b = r1
    //     0xaece48: stur            w1, [x0, #0xb]
    // 0xaece4c: r0 = Center()
    //     0xaece4c: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaece50: mov             x1, x0
    // 0xaece54: r0 = Instance_Alignment
    //     0xaece54: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xaece58: ldr             x0, [x0, #0x898]
    // 0xaece5c: stur            x1, [fp, #-0x30]
    // 0xaece60: StoreField: r1->field_f = r0
    //     0xaece60: stur            w0, [x1, #0xf]
    // 0xaece64: ldur            x0, [fp, #-0x28]
    // 0xaece68: StoreField: r1->field_b = r0
    //     0xaece68: stur            w0, [x1, #0xb]
    // 0xaece6c: r0 = Container()
    //     0xaece6c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaece70: stur            x0, [fp, #-0x28]
    // 0xaece74: r16 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xaece74: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xaece78: ldr             x16, [x16, #0xc58]
    // 0xaece7c: ldur            lr, [fp, #-0x20]
    // 0xaece80: stp             lr, x16, [SP, #0x10]
    // 0xaece84: r16 = Instance_EdgeInsets
    //     0xaece84: add             x16, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xaece88: ldr             x16, [x16, #0x768]
    // 0xaece8c: ldur            lr, [fp, #-0x30]
    // 0xaece90: stp             lr, x16, [SP]
    // 0xaece94: mov             x1, x0
    // 0xaece98: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x2, padding, 0x3, width, 0x1, null]
    //     0xaece98: add             x4, PP, #0x32, lsl #12  ; [pp+0x32f50] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xaece9c: ldr             x4, [x4, #0xf50]
    // 0xaecea0: r0 = Container()
    //     0xaecea0: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaecea4: ldur            x1, [fp, #-0x18]
    // 0xaecea8: ldur            x0, [fp, #-0x28]
    // 0xaeceac: ArrayStore: r1[6] = r0  ; List_4
    //     0xaeceac: add             x25, x1, #0x27
    //     0xaeceb0: str             w0, [x25]
    //     0xaeceb4: tbz             w0, #0, #0xaeced0
    //     0xaeceb8: ldurb           w16, [x1, #-1]
    //     0xaecebc: ldurb           w17, [x0, #-1]
    //     0xaecec0: and             x16, x17, x16, lsr #2
    //     0xaecec4: tst             x16, HEAP, lsr #32
    //     0xaecec8: b.eq            #0xaeced0
    //     0xaececc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaeced0: r0 = Obx()
    //     0xaeced0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaeced4: ldur            x2, [fp, #-0x10]
    // 0xaeced8: r1 = Function '<anonymous closure>':.
    //     0xaeced8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b00] AnonymousClosure: (0xaed474), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaecedc: ldr             x1, [x1, #0xb00]
    // 0xaecee0: stur            x0, [fp, #-0x20]
    // 0xaecee4: r0 = AllocateClosure()
    //     0xaecee4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaecee8: mov             x1, x0
    // 0xaeceec: ldur            x0, [fp, #-0x20]
    // 0xaecef0: StoreField: r0->field_b = r1
    //     0xaecef0: stur            w1, [x0, #0xb]
    // 0xaecef4: ldur            x1, [fp, #-0x18]
    // 0xaecef8: ArrayStore: r1[7] = r0  ; List_4
    //     0xaecef8: add             x25, x1, #0x2b
    //     0xaecefc: str             w0, [x25]
    //     0xaecf00: tbz             w0, #0, #0xaecf1c
    //     0xaecf04: ldurb           w16, [x1, #-1]
    //     0xaecf08: ldurb           w17, [x0, #-1]
    //     0xaecf0c: and             x16, x17, x16, lsr #2
    //     0xaecf10: tst             x16, HEAP, lsr #32
    //     0xaecf14: b.eq            #0xaecf1c
    //     0xaecf18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaecf1c: ldur            x0, [fp, #-0x18]
    // 0xaecf20: r16 = Instance_SizedBox
    //     0xaecf20: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaecf24: ldr             x16, [x16, #0x950]
    // 0xaecf28: StoreField: r0->field_2f = r16
    //     0xaecf28: stur            w16, [x0, #0x2f]
    // 0xaecf2c: r16 = Instance_NLabelTextField
    //     0xaecf2c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b08] Obj!NLabelTextField@e1fbd1
    //     0xaecf30: ldr             x16, [x16, #0xb08]
    // 0xaecf34: StoreField: r0->field_33 = r16
    //     0xaecf34: stur            w16, [x0, #0x33]
    // 0xaecf38: r1 = Function '<anonymous closure>': static.
    //     0xaecf38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaecf3c: ldr             x1, [x1, #0xda0]
    // 0xaecf40: r2 = Null
    //     0xaecf40: mov             x2, NULL
    // 0xaecf44: r0 = AllocateClosure()
    //     0xaecf44: bl              #0xec1630  ; AllocateClosureStub
    // 0xaecf48: mov             x2, x0
    // 0xaecf4c: r0 = <String>
    //     0xaecf4c: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaecf50: stur            x2, [fp, #-0x20]
    // 0xaecf54: StoreField: r2->field_b = r0
    //     0xaecf54: stur            w0, [x2, #0xb]
    // 0xaecf58: ldur            x1, [fp, #-8]
    // 0xaecf5c: r0 = controller()
    //     0xaecf5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaecf60: LoadField: r2 = r0->field_27
    //     0xaecf60: ldur            w2, [x0, #0x27]
    // 0xaecf64: DecompressPointer r2
    //     0xaecf64: add             x2, x2, HEAP, lsl #32
    // 0xaecf68: LoadField: r3 = r2->field_7
    //     0xaecf68: ldur            w3, [x2, #7]
    // 0xaecf6c: DecompressPointer r3
    //     0xaecf6c: add             x3, x3, HEAP, lsl #32
    // 0xaecf70: r1 = Function 'call':.
    //     0xaecf70: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaecf74: ldr             x1, [x1, #0x310]
    // 0xaecf78: r0 = AllocateClosureTA()
    //     0xaecf78: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaecf7c: mov             x3, x0
    // 0xaecf80: r2 = Null
    //     0xaecf80: mov             x2, NULL
    // 0xaecf84: r1 = Null
    //     0xaecf84: mov             x1, NULL
    // 0xaecf88: stur            x3, [fp, #-0x28]
    // 0xaecf8c: r8 = (dynamic this, String?) => String
    //     0xaecf8c: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xaecf90: ldr             x8, [x8, #0x40]
    // 0xaecf94: r3 = Null
    //     0xaecf94: add             x3, PP, #0x34, lsl #12  ; [pp+0x34b10] Null
    //     0xaecf98: ldr             x3, [x3, #0xb10]
    // 0xaecf9c: r0 = DefaultTypeTest()
    //     0xaecf9c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaecfa0: r16 = "[a-zA-Z ]"
    //     0xaecfa0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xaecfa4: ldr             x16, [x16, #0xb20]
    // 0xaecfa8: stp             x16, NULL, [SP, #0x20]
    // 0xaecfac: r16 = false
    //     0xaecfac: add             x16, NULL, #0x30  ; false
    // 0xaecfb0: r30 = true
    //     0xaecfb0: add             lr, NULL, #0x20  ; true
    // 0xaecfb4: stp             lr, x16, [SP, #0x10]
    // 0xaecfb8: r16 = false
    //     0xaecfb8: add             x16, NULL, #0x30  ; false
    // 0xaecfbc: r30 = false
    //     0xaecfbc: add             lr, NULL, #0x30  ; false
    // 0xaecfc0: stp             lr, x16, [SP]
    // 0xaecfc4: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xaecfc4: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xaecfc8: r0 = _RegExp()
    //     0xaecfc8: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xaecfcc: stur            x0, [fp, #-0x30]
    // 0xaecfd0: r0 = FilteringTextInputFormatter()
    //     0xaecfd0: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xaecfd4: mov             x3, x0
    // 0xaecfd8: ldur            x0, [fp, #-0x30]
    // 0xaecfdc: stur            x3, [fp, #-0x38]
    // 0xaecfe0: StoreField: r3->field_7 = r0
    //     0xaecfe0: stur            w0, [x3, #7]
    // 0xaecfe4: r0 = true
    //     0xaecfe4: add             x0, NULL, #0x20  ; true
    // 0xaecfe8: StoreField: r3->field_b = r0
    //     0xaecfe8: stur            w0, [x3, #0xb]
    // 0xaecfec: r0 = ""
    //     0xaecfec: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaecff0: StoreField: r3->field_f = r0
    //     0xaecff0: stur            w0, [x3, #0xf]
    // 0xaecff4: r1 = Null
    //     0xaecff4: mov             x1, NULL
    // 0xaecff8: r2 = 2
    //     0xaecff8: movz            x2, #0x2
    // 0xaecffc: r0 = AllocateArray()
    //     0xaecffc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaed000: mov             x2, x0
    // 0xaed004: ldur            x0, [fp, #-0x38]
    // 0xaed008: stur            x2, [fp, #-0x30]
    // 0xaed00c: StoreField: r2->field_f = r0
    //     0xaed00c: stur            w0, [x2, #0xf]
    // 0xaed010: r1 = <TextInputFormatter>
    //     0xaed010: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xaed014: ldr             x1, [x1, #0x788]
    // 0xaed018: r0 = AllocateGrowableArray()
    //     0xaed018: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaed01c: mov             x2, x0
    // 0xaed020: ldur            x0, [fp, #-0x30]
    // 0xaed024: stur            x2, [fp, #-0x38]
    // 0xaed028: StoreField: r2->field_f = r0
    //     0xaed028: stur            w0, [x2, #0xf]
    // 0xaed02c: r0 = 2
    //     0xaed02c: movz            x0, #0x2
    // 0xaed030: StoreField: r2->field_b = r0
    //     0xaed030: stur            w0, [x2, #0xb]
    // 0xaed034: ldur            x1, [fp, #-8]
    // 0xaed038: r0 = controller()
    //     0xaed038: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed03c: LoadField: r1 = r0->field_27
    //     0xaed03c: ldur            w1, [x0, #0x27]
    // 0xaed040: DecompressPointer r1
    //     0xaed040: add             x1, x1, HEAP, lsl #32
    // 0xaed044: r0 = value()
    //     0xaed044: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaed048: r1 = <String>
    //     0xaed048: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaed04c: stur            x0, [fp, #-8]
    // 0xaed050: r0 = TextFormField()
    //     0xaed050: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xaed054: stur            x0, [fp, #-0x30]
    // 0xaed058: ldur            x16, [fp, #-0x20]
    // 0xaed05c: ldur            lr, [fp, #-0x28]
    // 0xaed060: stp             lr, x16, [SP, #0x10]
    // 0xaed064: ldur            x16, [fp, #-0x38]
    // 0xaed068: ldur            lr, [fp, #-8]
    // 0xaed06c: stp             lr, x16, [SP]
    // 0xaed070: mov             x1, x0
    // 0xaed074: r2 = Instance_InputDecoration
    //     0xaed074: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xaed078: ldr             x2, [x2, #0xb28]
    // 0xaed07c: r4 = const [0, 0x6, 0x4, 0x2, initialValue, 0x5, inputFormatters, 0x4, onChanged, 0x3, validator, 0x2, null]
    //     0xaed07c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b30] List(13) [0, 0x6, 0x4, 0x2, "initialValue", 0x5, "inputFormatters", 0x4, "onChanged", 0x3, "validator", 0x2, Null]
    //     0xaed080: ldr             x4, [x4, #0xb30]
    // 0xaed084: r0 = TextFormField()
    //     0xaed084: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xaed088: ldur            x1, [fp, #-0x18]
    // 0xaed08c: ldur            x0, [fp, #-0x30]
    // 0xaed090: ArrayStore: r1[10] = r0  ; List_4
    //     0xaed090: add             x25, x1, #0x37
    //     0xaed094: str             w0, [x25]
    //     0xaed098: tbz             w0, #0, #0xaed0b4
    //     0xaed09c: ldurb           w16, [x1, #-1]
    //     0xaed0a0: ldurb           w17, [x0, #-1]
    //     0xaed0a4: and             x16, x17, x16, lsr #2
    //     0xaed0a8: tst             x16, HEAP, lsr #32
    //     0xaed0ac: b.eq            #0xaed0b4
    //     0xaed0b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaed0b4: r0 = Obx()
    //     0xaed0b4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaed0b8: ldur            x2, [fp, #-0x10]
    // 0xaed0bc: r1 = Function '<anonymous closure>':.
    //     0xaed0bc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b38] AnonymousClosure: (0xaed3a8), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaed0c0: ldr             x1, [x1, #0xb38]
    // 0xaed0c4: stur            x0, [fp, #-8]
    // 0xaed0c8: r0 = AllocateClosure()
    //     0xaed0c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaed0cc: mov             x1, x0
    // 0xaed0d0: ldur            x0, [fp, #-8]
    // 0xaed0d4: StoreField: r0->field_b = r1
    //     0xaed0d4: stur            w1, [x0, #0xb]
    // 0xaed0d8: ldur            x1, [fp, #-0x18]
    // 0xaed0dc: ArrayStore: r1[11] = r0  ; List_4
    //     0xaed0dc: add             x25, x1, #0x3b
    //     0xaed0e0: str             w0, [x25]
    //     0xaed0e4: tbz             w0, #0, #0xaed100
    //     0xaed0e8: ldurb           w16, [x1, #-1]
    //     0xaed0ec: ldurb           w17, [x0, #-1]
    //     0xaed0f0: and             x16, x17, x16, lsr #2
    //     0xaed0f4: tst             x16, HEAP, lsr #32
    //     0xaed0f8: b.eq            #0xaed100
    //     0xaed0fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaed100: r0 = Obx()
    //     0xaed100: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaed104: ldur            x2, [fp, #-0x10]
    // 0xaed108: r1 = Function '<anonymous closure>':.
    //     0xaed108: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b40] AnonymousClosure: (0xaebfe0), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaed10c: ldr             x1, [x1, #0xb40]
    // 0xaed110: stur            x0, [fp, #-8]
    // 0xaed114: r0 = AllocateClosure()
    //     0xaed114: bl              #0xec1630  ; AllocateClosureStub
    // 0xaed118: mov             x1, x0
    // 0xaed11c: ldur            x0, [fp, #-8]
    // 0xaed120: StoreField: r0->field_b = r1
    //     0xaed120: stur            w1, [x0, #0xb]
    // 0xaed124: ldur            x1, [fp, #-0x18]
    // 0xaed128: ArrayStore: r1[12] = r0  ; List_4
    //     0xaed128: add             x25, x1, #0x3f
    //     0xaed12c: str             w0, [x25]
    //     0xaed130: tbz             w0, #0, #0xaed14c
    //     0xaed134: ldurb           w16, [x1, #-1]
    //     0xaed138: ldurb           w17, [x0, #-1]
    //     0xaed13c: and             x16, x17, x16, lsr #2
    //     0xaed140: tst             x16, HEAP, lsr #32
    //     0xaed144: b.eq            #0xaed14c
    //     0xaed148: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaed14c: r0 = Obx()
    //     0xaed14c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaed150: ldur            x2, [fp, #-0x10]
    // 0xaed154: r1 = Function '<anonymous closure>':.
    //     0xaed154: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b48] AnonymousClosure: (0xaed290), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaed158: ldr             x1, [x1, #0xb48]
    // 0xaed15c: stur            x0, [fp, #-8]
    // 0xaed160: r0 = AllocateClosure()
    //     0xaed160: bl              #0xec1630  ; AllocateClosureStub
    // 0xaed164: mov             x1, x0
    // 0xaed168: ldur            x0, [fp, #-8]
    // 0xaed16c: StoreField: r0->field_b = r1
    //     0xaed16c: stur            w1, [x0, #0xb]
    // 0xaed170: r1 = Null
    //     0xaed170: mov             x1, NULL
    // 0xaed174: r2 = 4
    //     0xaed174: movz            x2, #0x4
    // 0xaed178: r0 = AllocateArray()
    //     0xaed178: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaed17c: mov             x2, x0
    // 0xaed180: ldur            x0, [fp, #-8]
    // 0xaed184: stur            x2, [fp, #-0x10]
    // 0xaed188: StoreField: r2->field_f = r0
    //     0xaed188: stur            w0, [x2, #0xf]
    // 0xaed18c: r16 = Instance_Text
    //     0xaed18c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b50] Obj!Text@e21c31
    //     0xaed190: ldr             x16, [x16, #0xb50]
    // 0xaed194: StoreField: r2->field_13 = r16
    //     0xaed194: stur            w16, [x2, #0x13]
    // 0xaed198: r1 = <Widget>
    //     0xaed198: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaed19c: r0 = AllocateGrowableArray()
    //     0xaed19c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaed1a0: mov             x1, x0
    // 0xaed1a4: ldur            x0, [fp, #-0x10]
    // 0xaed1a8: stur            x1, [fp, #-8]
    // 0xaed1ac: StoreField: r1->field_f = r0
    //     0xaed1ac: stur            w0, [x1, #0xf]
    // 0xaed1b0: r0 = 4
    //     0xaed1b0: movz            x0, #0x4
    // 0xaed1b4: StoreField: r1->field_b = r0
    //     0xaed1b4: stur            w0, [x1, #0xb]
    // 0xaed1b8: r0 = Row()
    //     0xaed1b8: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaed1bc: mov             x1, x0
    // 0xaed1c0: r0 = Instance_Axis
    //     0xaed1c0: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaed1c4: StoreField: r1->field_f = r0
    //     0xaed1c4: stur            w0, [x1, #0xf]
    // 0xaed1c8: r0 = Instance_MainAxisAlignment
    //     0xaed1c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaed1cc: ldr             x0, [x0, #0x730]
    // 0xaed1d0: StoreField: r1->field_13 = r0
    //     0xaed1d0: stur            w0, [x1, #0x13]
    // 0xaed1d4: r0 = Instance_MainAxisSize
    //     0xaed1d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaed1d8: ldr             x0, [x0, #0x738]
    // 0xaed1dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xaed1dc: stur            w0, [x1, #0x17]
    // 0xaed1e0: r0 = Instance_CrossAxisAlignment
    //     0xaed1e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaed1e4: ldr             x0, [x0, #0x740]
    // 0xaed1e8: StoreField: r1->field_1b = r0
    //     0xaed1e8: stur            w0, [x1, #0x1b]
    // 0xaed1ec: r0 = Instance_VerticalDirection
    //     0xaed1ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaed1f0: ldr             x0, [x0, #0x748]
    // 0xaed1f4: StoreField: r1->field_23 = r0
    //     0xaed1f4: stur            w0, [x1, #0x23]
    // 0xaed1f8: r0 = Instance_Clip
    //     0xaed1f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaed1fc: ldr             x0, [x0, #0x750]
    // 0xaed200: StoreField: r1->field_2b = r0
    //     0xaed200: stur            w0, [x1, #0x2b]
    // 0xaed204: StoreField: r1->field_2f = rZR
    //     0xaed204: stur            xzr, [x1, #0x2f]
    // 0xaed208: ldur            x0, [fp, #-8]
    // 0xaed20c: StoreField: r1->field_b = r0
    //     0xaed20c: stur            w0, [x1, #0xb]
    // 0xaed210: mov             x0, x1
    // 0xaed214: ldur            x1, [fp, #-0x18]
    // 0xaed218: ArrayStore: r1[13] = r0  ; List_4
    //     0xaed218: add             x25, x1, #0x43
    //     0xaed21c: str             w0, [x25]
    //     0xaed220: tbz             w0, #0, #0xaed23c
    //     0xaed224: ldurb           w16, [x1, #-1]
    //     0xaed228: ldurb           w17, [x0, #-1]
    //     0xaed22c: and             x16, x17, x16, lsr #2
    //     0xaed230: tst             x16, HEAP, lsr #32
    //     0xaed234: b.eq            #0xaed23c
    //     0xaed238: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaed23c: r1 = <Widget>
    //     0xaed23c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaed240: r0 = AllocateGrowableArray()
    //     0xaed240: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaed244: mov             x1, x0
    // 0xaed248: ldur            x0, [fp, #-0x18]
    // 0xaed24c: stur            x1, [fp, #-8]
    // 0xaed250: StoreField: r1->field_f = r0
    //     0xaed250: stur            w0, [x1, #0xf]
    // 0xaed254: r0 = 28
    //     0xaed254: movz            x0, #0x1c
    // 0xaed258: StoreField: r1->field_b = r0
    //     0xaed258: stur            w0, [x1, #0xb]
    // 0xaed25c: r0 = NSection()
    //     0xaed25c: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xaed260: r1 = ""
    //     0xaed260: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaed264: StoreField: r0->field_b = r1
    //     0xaed264: stur            w1, [x0, #0xb]
    // 0xaed268: ldur            x1, [fp, #-8]
    // 0xaed26c: StoreField: r0->field_f = r1
    //     0xaed26c: stur            w1, [x0, #0xf]
    // 0xaed270: r1 = false
    //     0xaed270: add             x1, NULL, #0x30  ; false
    // 0xaed274: StoreField: r0->field_27 = r1
    //     0xaed274: stur            w1, [x0, #0x27]
    // 0xaed278: StoreField: r0->field_2b = r1
    //     0xaed278: stur            w1, [x0, #0x2b]
    // 0xaed27c: LeaveFrame
    //     0xaed27c: mov             SP, fp
    //     0xaed280: ldp             fp, lr, [SP], #0x10
    // 0xaed284: ret
    //     0xaed284: ret             
    // 0xaed288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed28c: b               #0xaeca48
  }
  [closure] Checkbox <anonymous closure>(dynamic) {
    // ** addr: 0xaed290, size: 0x118
    // 0xaed290: EnterFrame
    //     0xaed290: stp             fp, lr, [SP, #-0x10]!
    //     0xaed294: mov             fp, SP
    // 0xaed298: AllocStack(0x30)
    //     0xaed298: sub             SP, SP, #0x30
    // 0xaed29c: SetupParameters()
    //     0xaed29c: ldr             x0, [fp, #0x10]
    //     0xaed2a0: ldur            w2, [x0, #0x17]
    //     0xaed2a4: add             x2, x2, HEAP, lsl #32
    //     0xaed2a8: stur            x2, [fp, #-8]
    // 0xaed2ac: CheckStackOverflow
    //     0xaed2ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed2b0: cmp             SP, x16
    //     0xaed2b4: b.ls            #0xaed3a0
    // 0xaed2b8: LoadField: r1 = r2->field_f
    //     0xaed2b8: ldur            w1, [x2, #0xf]
    // 0xaed2bc: DecompressPointer r1
    //     0xaed2bc: add             x1, x1, HEAP, lsl #32
    // 0xaed2c0: r0 = controller()
    //     0xaed2c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed2c4: LoadField: r1 = r0->field_2b
    //     0xaed2c4: ldur            w1, [x0, #0x2b]
    // 0xaed2c8: DecompressPointer r1
    //     0xaed2c8: add             x1, x1, HEAP, lsl #32
    // 0xaed2cc: r0 = value()
    //     0xaed2cc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaed2d0: mov             x2, x0
    // 0xaed2d4: ldur            x0, [fp, #-8]
    // 0xaed2d8: stur            x2, [fp, #-0x10]
    // 0xaed2dc: LoadField: r1 = r0->field_f
    //     0xaed2dc: ldur            w1, [x0, #0xf]
    // 0xaed2e0: DecompressPointer r1
    //     0xaed2e0: add             x1, x1, HEAP, lsl #32
    // 0xaed2e4: r0 = controller()
    //     0xaed2e4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed2e8: LoadField: r2 = r0->field_2b
    //     0xaed2e8: ldur            w2, [x0, #0x2b]
    // 0xaed2ec: DecompressPointer r2
    //     0xaed2ec: add             x2, x2, HEAP, lsl #32
    // 0xaed2f0: LoadField: r3 = r2->field_7
    //     0xaed2f0: ldur            w3, [x2, #7]
    // 0xaed2f4: DecompressPointer r3
    //     0xaed2f4: add             x3, x3, HEAP, lsl #32
    // 0xaed2f8: r1 = Function 'call':.
    //     0xaed2f8: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaed2fc: ldr             x1, [x1, #0x310]
    // 0xaed300: r0 = AllocateClosureTA()
    //     0xaed300: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaed304: mov             x3, x0
    // 0xaed308: r2 = Null
    //     0xaed308: mov             x2, NULL
    // 0xaed30c: r1 = Null
    //     0xaed30c: mov             x1, NULL
    // 0xaed310: stur            x3, [fp, #-8]
    // 0xaed314: r8 = (dynamic this, bool?) => bool
    //     0xaed314: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xaed318: ldr             x8, [x8, #0x318]
    // 0xaed31c: r3 = Null
    //     0xaed31c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34b58] Null
    //     0xaed320: ldr             x3, [x3, #0xb58]
    // 0xaed324: r0 = DefaultTypeTest()
    //     0xaed324: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaed328: r16 = <Color?>
    //     0xaed328: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaed32c: ldr             x16, [x16, #0x98]
    // 0xaed330: r30 = Instance_MaterialColor
    //     0xaed330: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xaed334: ldr             lr, [lr, #0xbf0]
    // 0xaed338: stp             lr, x16, [SP, #8]
    // 0xaed33c: r16 = Instance_Color
    //     0xaed33c: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaed340: str             x16, [SP]
    // 0xaed344: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaed344: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaed348: r0 = mode()
    //     0xaed348: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaed34c: stur            x0, [fp, #-0x18]
    // 0xaed350: r0 = Checkbox()
    //     0xaed350: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xaed354: ldur            x1, [fp, #-0x10]
    // 0xaed358: StoreField: r0->field_b = r1
    //     0xaed358: stur            w1, [x0, #0xb]
    // 0xaed35c: r1 = false
    //     0xaed35c: add             x1, NULL, #0x30  ; false
    // 0xaed360: StoreField: r0->field_23 = r1
    //     0xaed360: stur            w1, [x0, #0x23]
    // 0xaed364: ldur            x2, [fp, #-8]
    // 0xaed368: StoreField: r0->field_f = r2
    //     0xaed368: stur            w2, [x0, #0xf]
    // 0xaed36c: ldur            x2, [fp, #-0x18]
    // 0xaed370: StoreField: r0->field_1f = r2
    //     0xaed370: stur            w2, [x0, #0x1f]
    // 0xaed374: r2 = Instance_VisualDensity
    //     0xaed374: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xaed378: ldr             x2, [x2, #0xb68]
    // 0xaed37c: StoreField: r0->field_2b = r2
    //     0xaed37c: stur            w2, [x0, #0x2b]
    // 0xaed380: StoreField: r0->field_43 = r1
    //     0xaed380: stur            w1, [x0, #0x43]
    // 0xaed384: StoreField: r0->field_4f = r1
    //     0xaed384: stur            w1, [x0, #0x4f]
    // 0xaed388: r1 = Instance__CheckboxType
    //     0xaed388: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xaed38c: ldr             x1, [x1, #0xb70]
    // 0xaed390: StoreField: r0->field_57 = r1
    //     0xaed390: stur            w1, [x0, #0x57]
    // 0xaed394: LeaveFrame
    //     0xaed394: mov             SP, fp
    //     0xaed398: ldp             fp, lr, [SP], #0x10
    // 0xaed39c: ret
    //     0xaed39c: ret             
    // 0xaed3a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed3a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed3a4: b               #0xaed2b8
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0xaed3a8, size: 0xcc
    // 0xaed3a8: EnterFrame
    //     0xaed3a8: stp             fp, lr, [SP, #-0x10]!
    //     0xaed3ac: mov             fp, SP
    // 0xaed3b0: AllocStack(0x10)
    //     0xaed3b0: sub             SP, SP, #0x10
    // 0xaed3b4: SetupParameters()
    //     0xaed3b4: ldr             x0, [fp, #0x10]
    //     0xaed3b8: ldur            w1, [x0, #0x17]
    //     0xaed3bc: add             x1, x1, HEAP, lsl #32
    // 0xaed3c0: CheckStackOverflow
    //     0xaed3c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed3c4: cmp             SP, x16
    //     0xaed3c8: b.ls            #0xaed45c
    // 0xaed3cc: LoadField: r0 = r1->field_f
    //     0xaed3cc: ldur            w0, [x1, #0xf]
    // 0xaed3d0: DecompressPointer r0
    //     0xaed3d0: add             x0, x0, HEAP, lsl #32
    // 0xaed3d4: mov             x1, x0
    // 0xaed3d8: r0 = controller()
    //     0xaed3d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed3dc: LoadField: r1 = r0->field_47
    //     0xaed3dc: ldur            w1, [x0, #0x47]
    // 0xaed3e0: DecompressPointer r1
    //     0xaed3e0: add             x1, x1, HEAP, lsl #32
    // 0xaed3e4: r0 = value()
    //     0xaed3e4: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaed3e8: r1 = LoadClassIdInstr(r0)
    //     0xaed3e8: ldur            x1, [x0, #-1]
    //     0xaed3ec: ubfx            x1, x1, #0xc, #0x14
    // 0xaed3f0: str             x0, [SP]
    // 0xaed3f4: mov             x0, x1
    // 0xaed3f8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaed3f8: movz            x17, #0xc834
    //     0xaed3fc: add             lr, x0, x17
    //     0xaed400: ldr             lr, [x21, lr, lsl #3]
    //     0xaed404: blr             lr
    // 0xaed408: cbz             w0, #0xaed414
    // 0xaed40c: d0 = 12.000000
    //     0xaed40c: fmov            d0, #12.00000000
    // 0xaed410: b               #0xaed418
    // 0xaed414: d0 = 0.000000
    //     0xaed414: eor             v0.16b, v0.16b, v0.16b
    // 0xaed418: r0 = inline_Allocate_Double()
    //     0xaed418: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaed41c: add             x0, x0, #0x10
    //     0xaed420: cmp             x1, x0
    //     0xaed424: b.ls            #0xaed464
    //     0xaed428: str             x0, [THR, #0x50]  ; THR::top
    //     0xaed42c: sub             x0, x0, #0xf
    //     0xaed430: movz            x1, #0xe15c
    //     0xaed434: movk            x1, #0x3, lsl #16
    //     0xaed438: stur            x1, [x0, #-1]
    // 0xaed43c: StoreField: r0->field_7 = d0
    //     0xaed43c: stur            d0, [x0, #7]
    // 0xaed440: stur            x0, [fp, #-8]
    // 0xaed444: r0 = SizedBox()
    //     0xaed444: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaed448: ldur            x1, [fp, #-8]
    // 0xaed44c: StoreField: r0->field_13 = r1
    //     0xaed44c: stur            w1, [x0, #0x13]
    // 0xaed450: LeaveFrame
    //     0xaed450: mov             SP, fp
    //     0xaed454: ldp             fp, lr, [SP], #0x10
    // 0xaed458: ret
    //     0xaed458: ret             
    // 0xaed45c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed45c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed460: b               #0xaed3cc
    // 0xaed464: SaveReg d0
    //     0xaed464: str             q0, [SP, #-0x10]!
    // 0xaed468: r0 = AllocateDouble()
    //     0xaed468: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaed46c: RestoreReg d0
    //     0xaed46c: ldr             q0, [SP], #0x10
    // 0xaed470: b               #0xaed43c
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xaed474, size: 0x218
    // 0xaed474: EnterFrame
    //     0xaed474: stp             fp, lr, [SP, #-0x10]!
    //     0xaed478: mov             fp, SP
    // 0xaed47c: AllocStack(0x38)
    //     0xaed47c: sub             SP, SP, #0x38
    // 0xaed480: SetupParameters()
    //     0xaed480: ldr             x0, [fp, #0x10]
    //     0xaed484: ldur            w2, [x0, #0x17]
    //     0xaed488: add             x2, x2, HEAP, lsl #32
    //     0xaed48c: stur            x2, [fp, #-8]
    // 0xaed490: CheckStackOverflow
    //     0xaed490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed494: cmp             SP, x16
    //     0xaed498: b.ls            #0xaed684
    // 0xaed49c: LoadField: r1 = r2->field_f
    //     0xaed49c: ldur            w1, [x2, #0xf]
    // 0xaed4a0: DecompressPointer r1
    //     0xaed4a0: add             x1, x1, HEAP, lsl #32
    // 0xaed4a4: r0 = controller()
    //     0xaed4a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed4a8: LoadField: r1 = r0->field_43
    //     0xaed4a8: ldur            w1, [x0, #0x43]
    // 0xaed4ac: DecompressPointer r1
    //     0xaed4ac: add             x1, x1, HEAP, lsl #32
    // 0xaed4b0: r16 = <int>
    //     0xaed4b0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaed4b4: stp             x1, x16, [SP]
    // 0xaed4b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaed4b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaed4bc: r0 = RxNumExt.<=()
    //     0xaed4bc: bl              #0xaed770  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxNumExt.<=
    // 0xaed4c0: tbnz            w0, #4, #0xaed4d8
    // 0xaed4c4: r0 = Instance_SizedBox
    //     0xaed4c4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xaed4c8: ldr             x0, [x0, #0xc40]
    // 0xaed4cc: LeaveFrame
    //     0xaed4cc: mov             SP, fp
    //     0xaed4d0: ldp             fp, lr, [SP], #0x10
    // 0xaed4d4: ret
    //     0xaed4d4: ret             
    // 0xaed4d8: ldur            x0, [fp, #-8]
    // 0xaed4dc: LoadField: r1 = r0->field_f
    //     0xaed4dc: ldur            w1, [x0, #0xf]
    // 0xaed4e0: DecompressPointer r1
    //     0xaed4e0: add             x1, x1, HEAP, lsl #32
    // 0xaed4e4: r0 = controller()
    //     0xaed4e4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed4e8: LoadField: r1 = r0->field_4b
    //     0xaed4e8: ldur            w1, [x0, #0x4b]
    // 0xaed4ec: DecompressPointer r1
    //     0xaed4ec: add             x1, x1, HEAP, lsl #32
    // 0xaed4f0: r0 = value()
    //     0xaed4f0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaed4f4: mov             x2, x0
    // 0xaed4f8: ldur            x0, [fp, #-8]
    // 0xaed4fc: stur            x2, [fp, #-0x10]
    // 0xaed500: LoadField: r1 = r0->field_f
    //     0xaed500: ldur            w1, [x0, #0xf]
    // 0xaed504: DecompressPointer r1
    //     0xaed504: add             x1, x1, HEAP, lsl #32
    // 0xaed508: r0 = controller()
    //     0xaed508: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed50c: LoadField: r2 = r0->field_4b
    //     0xaed50c: ldur            w2, [x0, #0x4b]
    // 0xaed510: DecompressPointer r2
    //     0xaed510: add             x2, x2, HEAP, lsl #32
    // 0xaed514: LoadField: r3 = r2->field_7
    //     0xaed514: ldur            w3, [x2, #7]
    // 0xaed518: DecompressPointer r3
    //     0xaed518: add             x3, x3, HEAP, lsl #32
    // 0xaed51c: r1 = Function 'call':.
    //     0xaed51c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaed520: ldr             x1, [x1, #0x310]
    // 0xaed524: r0 = AllocateClosureTA()
    //     0xaed524: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaed528: mov             x3, x0
    // 0xaed52c: r2 = Null
    //     0xaed52c: mov             x2, NULL
    // 0xaed530: r1 = Null
    //     0xaed530: mov             x1, NULL
    // 0xaed534: stur            x3, [fp, #-8]
    // 0xaed538: r8 = (dynamic this, bool?) => bool
    //     0xaed538: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xaed53c: ldr             x8, [x8, #0x318]
    // 0xaed540: r3 = Null
    //     0xaed540: add             x3, PP, #0x34, lsl #12  ; [pp+0x34bc0] Null
    //     0xaed544: ldr             x3, [x3, #0xbc0]
    // 0xaed548: r0 = DefaultTypeTest()
    //     0xaed548: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaed54c: r16 = <Color?>
    //     0xaed54c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaed550: ldr             x16, [x16, #0x98]
    // 0xaed554: r30 = Instance_MaterialColor
    //     0xaed554: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xaed558: ldr             lr, [lr, #0xbf0]
    // 0xaed55c: stp             lr, x16, [SP, #8]
    // 0xaed560: r16 = Instance_Color
    //     0xaed560: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaed564: str             x16, [SP]
    // 0xaed568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaed568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaed56c: r0 = mode()
    //     0xaed56c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaed570: stur            x0, [fp, #-0x18]
    // 0xaed574: r0 = Checkbox()
    //     0xaed574: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xaed578: mov             x3, x0
    // 0xaed57c: ldur            x0, [fp, #-0x10]
    // 0xaed580: stur            x3, [fp, #-0x20]
    // 0xaed584: StoreField: r3->field_b = r0
    //     0xaed584: stur            w0, [x3, #0xb]
    // 0xaed588: r0 = false
    //     0xaed588: add             x0, NULL, #0x30  ; false
    // 0xaed58c: StoreField: r3->field_23 = r0
    //     0xaed58c: stur            w0, [x3, #0x23]
    // 0xaed590: ldur            x1, [fp, #-8]
    // 0xaed594: StoreField: r3->field_f = r1
    //     0xaed594: stur            w1, [x3, #0xf]
    // 0xaed598: ldur            x1, [fp, #-0x18]
    // 0xaed59c: StoreField: r3->field_1f = r1
    //     0xaed59c: stur            w1, [x3, #0x1f]
    // 0xaed5a0: r1 = Instance_VisualDensity
    //     0xaed5a0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xaed5a4: ldr             x1, [x1, #0xb68]
    // 0xaed5a8: StoreField: r3->field_2b = r1
    //     0xaed5a8: stur            w1, [x3, #0x2b]
    // 0xaed5ac: StoreField: r3->field_43 = r0
    //     0xaed5ac: stur            w0, [x3, #0x43]
    // 0xaed5b0: StoreField: r3->field_4f = r0
    //     0xaed5b0: stur            w0, [x3, #0x4f]
    // 0xaed5b4: r0 = Instance__CheckboxType
    //     0xaed5b4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xaed5b8: ldr             x0, [x0, #0xb70]
    // 0xaed5bc: StoreField: r3->field_57 = r0
    //     0xaed5bc: stur            w0, [x3, #0x57]
    // 0xaed5c0: r1 = Null
    //     0xaed5c0: mov             x1, NULL
    // 0xaed5c4: r2 = 4
    //     0xaed5c4: movz            x2, #0x4
    // 0xaed5c8: r0 = AllocateArray()
    //     0xaed5c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaed5cc: mov             x2, x0
    // 0xaed5d0: ldur            x0, [fp, #-0x20]
    // 0xaed5d4: stur            x2, [fp, #-8]
    // 0xaed5d8: StoreField: r2->field_f = r0
    //     0xaed5d8: stur            w0, [x2, #0xf]
    // 0xaed5dc: r16 = Instance_Text
    //     0xaed5dc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34bd0] Obj!Text@e21cd1
    //     0xaed5e0: ldr             x16, [x16, #0xbd0]
    // 0xaed5e4: StoreField: r2->field_13 = r16
    //     0xaed5e4: stur            w16, [x2, #0x13]
    // 0xaed5e8: r1 = <Widget>
    //     0xaed5e8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaed5ec: r0 = AllocateGrowableArray()
    //     0xaed5ec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaed5f0: mov             x1, x0
    // 0xaed5f4: ldur            x0, [fp, #-8]
    // 0xaed5f8: stur            x1, [fp, #-0x10]
    // 0xaed5fc: StoreField: r1->field_f = r0
    //     0xaed5fc: stur            w0, [x1, #0xf]
    // 0xaed600: r0 = 4
    //     0xaed600: movz            x0, #0x4
    // 0xaed604: StoreField: r1->field_b = r0
    //     0xaed604: stur            w0, [x1, #0xb]
    // 0xaed608: r0 = Row()
    //     0xaed608: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaed60c: mov             x1, x0
    // 0xaed610: r0 = Instance_Axis
    //     0xaed610: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaed614: StoreField: r1->field_f = r0
    //     0xaed614: stur            w0, [x1, #0xf]
    // 0xaed618: r0 = Instance_MainAxisAlignment
    //     0xaed618: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaed61c: ldr             x0, [x0, #0x730]
    // 0xaed620: StoreField: r1->field_13 = r0
    //     0xaed620: stur            w0, [x1, #0x13]
    // 0xaed624: r0 = Instance_MainAxisSize
    //     0xaed624: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaed628: ldr             x0, [x0, #0x738]
    // 0xaed62c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaed62c: stur            w0, [x1, #0x17]
    // 0xaed630: r0 = Instance_CrossAxisAlignment
    //     0xaed630: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaed634: ldr             x0, [x0, #0x740]
    // 0xaed638: StoreField: r1->field_1b = r0
    //     0xaed638: stur            w0, [x1, #0x1b]
    // 0xaed63c: r0 = Instance_VerticalDirection
    //     0xaed63c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaed640: ldr             x0, [x0, #0x748]
    // 0xaed644: StoreField: r1->field_23 = r0
    //     0xaed644: stur            w0, [x1, #0x23]
    // 0xaed648: r0 = Instance_Clip
    //     0xaed648: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaed64c: ldr             x0, [x0, #0x750]
    // 0xaed650: StoreField: r1->field_2b = r0
    //     0xaed650: stur            w0, [x1, #0x2b]
    // 0xaed654: StoreField: r1->field_2f = rZR
    //     0xaed654: stur            xzr, [x1, #0x2f]
    // 0xaed658: ldur            x0, [fp, #-0x10]
    // 0xaed65c: StoreField: r1->field_b = r0
    //     0xaed65c: stur            w0, [x1, #0xb]
    // 0xaed660: r16 = 12.000000
    //     0xaed660: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xaed664: ldr             x16, [x16, #0xc60]
    // 0xaed668: str             x16, [SP]
    // 0xaed66c: r4 = const [0, 0x2, 0x1, 0x1, top, 0x1, null]
    //     0xaed66c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34bd8] List(7) [0, 0x2, 0x1, 0x1, "top", 0x1, Null]
    //     0xaed670: ldr             x4, [x4, #0xbd8]
    // 0xaed674: r0 = WidgetPaddingX.paddingOnly()
    //     0xaed674: bl              #0xaed68c  ; [package:get/get_utils/src/extensions/widget_extensions.dart] ::WidgetPaddingX.paddingOnly
    // 0xaed678: LeaveFrame
    //     0xaed678: mov             SP, fp
    //     0xaed67c: ldp             fp, lr, [SP], #0x10
    // 0xaed680: ret
    //     0xaed680: ret             
    // 0xaed684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed688: b               #0xaed49c
  }
  [closure] Text <anonymous closure>(dynamic) {
    // ** addr: 0xaed7cc, size: 0x1ac
    // 0xaed7cc: EnterFrame
    //     0xaed7cc: stp             fp, lr, [SP, #-0x10]!
    //     0xaed7d0: mov             fp, SP
    // 0xaed7d4: AllocStack(0x18)
    //     0xaed7d4: sub             SP, SP, #0x18
    // 0xaed7d8: SetupParameters()
    //     0xaed7d8: ldr             x0, [fp, #0x10]
    //     0xaed7dc: ldur            w3, [x0, #0x17]
    //     0xaed7e0: add             x3, x3, HEAP, lsl #32
    //     0xaed7e4: stur            x3, [fp, #-8]
    // 0xaed7e8: CheckStackOverflow
    //     0xaed7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed7ec: cmp             SP, x16
    //     0xaed7f0: b.ls            #0xaed96c
    // 0xaed7f4: r1 = Null
    //     0xaed7f4: mov             x1, NULL
    // 0xaed7f8: r2 = 4
    //     0xaed7f8: movz            x2, #0x4
    // 0xaed7fc: r0 = AllocateArray()
    //     0xaed7fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaed800: stur            x0, [fp, #-0x10]
    // 0xaed804: r16 = "Rp"
    //     0xaed804: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xaed808: ldr             x16, [x16, #0xf90]
    // 0xaed80c: StoreField: r0->field_f = r16
    //     0xaed80c: stur            w16, [x0, #0xf]
    // 0xaed810: ldur            x1, [fp, #-8]
    // 0xaed814: LoadField: r2 = r1->field_f
    //     0xaed814: ldur            w2, [x1, #0xf]
    // 0xaed818: DecompressPointer r2
    //     0xaed818: add             x2, x2, HEAP, lsl #32
    // 0xaed81c: mov             x1, x2
    // 0xaed820: r0 = controller()
    //     0xaed820: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed824: mov             x1, x0
    // 0xaed828: r0 = subtotal()
    //     0xaed828: bl              #0x8f4c50  ; [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::subtotal
    // 0xaed82c: mov             x1, x0
    // 0xaed830: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaed830: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaed834: r0 = NumExtension.idr()
    //     0xaed834: bl              #0xadf068  ; [package:nuonline/common/extensions/int_extension.dart] ::NumExtension.idr
    // 0xaed838: ldur            x1, [fp, #-0x10]
    // 0xaed83c: ArrayStore: r1[1] = r0  ; List_4
    //     0xaed83c: add             x25, x1, #0x13
    //     0xaed840: str             w0, [x25]
    //     0xaed844: tbz             w0, #0, #0xaed860
    //     0xaed848: ldurb           w16, [x1, #-1]
    //     0xaed84c: ldurb           w17, [x0, #-1]
    //     0xaed850: and             x16, x17, x16, lsr #2
    //     0xaed854: tst             x16, HEAP, lsr #32
    //     0xaed858: b.eq            #0xaed860
    //     0xaed85c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaed860: ldur            x16, [fp, #-0x10]
    // 0xaed864: str             x16, [SP]
    // 0xaed868: r0 = _interpolate()
    //     0xaed868: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaed86c: stur            x0, [fp, #-8]
    // 0xaed870: r0 = TextSpan()
    //     0xaed870: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaed874: mov             x3, x0
    // 0xaed878: ldur            x0, [fp, #-8]
    // 0xaed87c: stur            x3, [fp, #-0x10]
    // 0xaed880: StoreField: r3->field_b = r0
    //     0xaed880: stur            w0, [x3, #0xb]
    // 0xaed884: r0 = Instance__DeferringMouseCursor
    //     0xaed884: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaed888: ArrayStore: r3[0] = r0  ; List_4
    //     0xaed888: stur            w0, [x3, #0x17]
    // 0xaed88c: r1 = Instance_TextStyle
    //     0xaed88c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34be0] Obj!TextStyle@e1ae71
    //     0xaed890: ldr             x1, [x1, #0xbe0]
    // 0xaed894: StoreField: r3->field_7 = r1
    //     0xaed894: stur            w1, [x3, #7]
    // 0xaed898: r1 = Null
    //     0xaed898: mov             x1, NULL
    // 0xaed89c: r2 = 2
    //     0xaed89c: movz            x2, #0x2
    // 0xaed8a0: r0 = AllocateArray()
    //     0xaed8a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaed8a4: mov             x2, x0
    // 0xaed8a8: ldur            x0, [fp, #-0x10]
    // 0xaed8ac: stur            x2, [fp, #-8]
    // 0xaed8b0: StoreField: r2->field_f = r0
    //     0xaed8b0: stur            w0, [x2, #0xf]
    // 0xaed8b4: r1 = <InlineSpan>
    //     0xaed8b4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xaed8b8: ldr             x1, [x1, #0x5f0]
    // 0xaed8bc: r0 = AllocateGrowableArray()
    //     0xaed8bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaed8c0: mov             x1, x0
    // 0xaed8c4: ldur            x0, [fp, #-8]
    // 0xaed8c8: stur            x1, [fp, #-0x10]
    // 0xaed8cc: StoreField: r1->field_f = r0
    //     0xaed8cc: stur            w0, [x1, #0xf]
    // 0xaed8d0: r0 = 2
    //     0xaed8d0: movz            x0, #0x2
    // 0xaed8d4: StoreField: r1->field_b = r0
    //     0xaed8d4: stur            w0, [x1, #0xb]
    // 0xaed8d8: r0 = TextSpan()
    //     0xaed8d8: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaed8dc: mov             x1, x0
    // 0xaed8e0: r0 = "Total "
    //     0xaed8e0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34be8] "Total "
    //     0xaed8e4: ldr             x0, [x0, #0xbe8]
    // 0xaed8e8: stur            x1, [fp, #-8]
    // 0xaed8ec: StoreField: r1->field_b = r0
    //     0xaed8ec: stur            w0, [x1, #0xb]
    // 0xaed8f0: ldur            x0, [fp, #-0x10]
    // 0xaed8f4: StoreField: r1->field_f = r0
    //     0xaed8f4: stur            w0, [x1, #0xf]
    // 0xaed8f8: r0 = Instance__DeferringMouseCursor
    //     0xaed8f8: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaed8fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xaed8fc: stur            w0, [x1, #0x17]
    // 0xaed900: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaed900: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaed904: ldr             x0, [x0, #0x2670]
    //     0xaed908: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaed90c: cmp             w0, w16
    //     0xaed910: b.ne            #0xaed91c
    //     0xaed914: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaed918: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaed91c: r0 = GetNavigation.textTheme()
    //     0xaed91c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaed920: LoadField: r1 = r0->field_f
    //     0xaed920: ldur            w1, [x0, #0xf]
    // 0xaed924: DecompressPointer r1
    //     0xaed924: add             x1, x1, HEAP, lsl #32
    // 0xaed928: cmp             w1, NULL
    // 0xaed92c: b.eq            #0xaed974
    // 0xaed930: r16 = Instance_FontWeight
    //     0xaed930: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xaed934: ldr             x16, [x16, #0xc50]
    // 0xaed938: str             x16, [SP]
    // 0xaed93c: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xaed93c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xaed940: ldr             x4, [x4, #0xfe0]
    // 0xaed944: r0 = copyWith()
    //     0xaed944: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaed948: stur            x0, [fp, #-0x10]
    // 0xaed94c: r0 = Text()
    //     0xaed94c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaed950: ldur            x1, [fp, #-8]
    // 0xaed954: StoreField: r0->field_f = r1
    //     0xaed954: stur            w1, [x0, #0xf]
    // 0xaed958: ldur            x1, [fp, #-0x10]
    // 0xaed95c: StoreField: r0->field_13 = r1
    //     0xaed95c: stur            w1, [x0, #0x13]
    // 0xaed960: LeaveFrame
    //     0xaed960: mov             SP, fp
    //     0xaed964: ldp             fp, lr, [SP], #0x10
    // 0xaed968: ret
    //     0xaed968: ret             
    // 0xaed96c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaed96c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaed970: b               #0xaed7f4
    // 0xaed974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaed974: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] NCounterButton <anonymous closure>(dynamic) {
    // ** addr: 0xaed978, size: 0x138
    // 0xaed978: EnterFrame
    //     0xaed978: stp             fp, lr, [SP, #-0x10]!
    //     0xaed97c: mov             fp, SP
    // 0xaed980: AllocStack(0x28)
    //     0xaed980: sub             SP, SP, #0x28
    // 0xaed984: SetupParameters()
    //     0xaed984: ldr             x0, [fp, #0x10]
    //     0xaed988: ldur            w2, [x0, #0x17]
    //     0xaed98c: add             x2, x2, HEAP, lsl #32
    //     0xaed990: stur            x2, [fp, #-8]
    // 0xaed994: CheckStackOverflow
    //     0xaed994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaed998: cmp             SP, x16
    //     0xaed99c: b.ls            #0xaedaa8
    // 0xaed9a0: LoadField: r1 = r2->field_f
    //     0xaed9a0: ldur            w1, [x2, #0xf]
    // 0xaed9a4: DecompressPointer r1
    //     0xaed9a4: add             x1, x1, HEAP, lsl #32
    // 0xaed9a8: r0 = controller()
    //     0xaed9a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed9ac: LoadField: r1 = r0->field_43
    //     0xaed9ac: ldur            w1, [x0, #0x43]
    // 0xaed9b0: DecompressPointer r1
    //     0xaed9b0: add             x1, x1, HEAP, lsl #32
    // 0xaed9b4: r0 = value()
    //     0xaed9b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaed9b8: mov             x2, x0
    // 0xaed9bc: ldur            x0, [fp, #-8]
    // 0xaed9c0: stur            x2, [fp, #-0x10]
    // 0xaed9c4: LoadField: r1 = r0->field_f
    //     0xaed9c4: ldur            w1, [x0, #0xf]
    // 0xaed9c8: DecompressPointer r1
    //     0xaed9c8: add             x1, x1, HEAP, lsl #32
    // 0xaed9cc: r0 = controller()
    //     0xaed9cc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaed9d0: LoadField: r1 = r0->field_43
    //     0xaed9d0: ldur            w1, [x0, #0x43]
    // 0xaed9d4: DecompressPointer r1
    //     0xaed9d4: add             x1, x1, HEAP, lsl #32
    // 0xaed9d8: r0 = value()
    //     0xaed9d8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaed9dc: r1 = LoadInt32Instr(r0)
    //     0xaed9dc: sbfx            x1, x0, #1, #0x1f
    //     0xaed9e0: tbz             w0, #0, #0xaed9e8
    //     0xaed9e4: ldur            x1, [x0, #7]
    // 0xaed9e8: cmp             x1, #1
    // 0xaed9ec: r16 = true
    //     0xaed9ec: add             x16, NULL, #0x20  ; true
    // 0xaed9f0: r17 = false
    //     0xaed9f0: add             x17, NULL, #0x30  ; false
    // 0xaed9f4: csel            x0, x16, x17, le
    // 0xaed9f8: ldur            x2, [fp, #-8]
    // 0xaed9fc: stur            x0, [fp, #-0x18]
    // 0xaeda00: LoadField: r1 = r2->field_f
    //     0xaeda00: ldur            w1, [x2, #0xf]
    // 0xaeda04: DecompressPointer r1
    //     0xaeda04: add             x1, x1, HEAP, lsl #32
    // 0xaeda08: r0 = controller()
    //     0xaeda08: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeda0c: mov             x2, x0
    // 0xaeda10: ldur            x0, [fp, #-8]
    // 0xaeda14: stur            x2, [fp, #-0x20]
    // 0xaeda18: LoadField: r1 = r0->field_f
    //     0xaeda18: ldur            w1, [x0, #0xf]
    // 0xaeda1c: DecompressPointer r1
    //     0xaeda1c: add             x1, x1, HEAP, lsl #32
    // 0xaeda20: r0 = controller()
    //     0xaeda20: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeda24: mov             x1, x0
    // 0xaeda28: ldur            x0, [fp, #-0x10]
    // 0xaeda2c: stur            x1, [fp, #-8]
    // 0xaeda30: r2 = LoadInt32Instr(r0)
    //     0xaeda30: sbfx            x2, x0, #1, #0x1f
    //     0xaeda34: tbz             w0, #0, #0xaeda3c
    //     0xaeda38: ldur            x2, [x0, #7]
    // 0xaeda3c: stur            x2, [fp, #-0x28]
    // 0xaeda40: r0 = NCounterButton()
    //     0xaeda40: bl              #0xaedab0  ; AllocateNCounterButtonStub -> NCounterButton (size=0x24)
    // 0xaeda44: mov             x3, x0
    // 0xaeda48: ldur            x0, [fp, #-0x28]
    // 0xaeda4c: stur            x3, [fp, #-0x10]
    // 0xaeda50: StoreField: r3->field_b = r0
    //     0xaeda50: stur            x0, [x3, #0xb]
    // 0xaeda54: ldur            x2, [fp, #-0x20]
    // 0xaeda58: r1 = Function 'onDecrementPressed':.
    //     0xaeda58: add             x1, PP, #0x34, lsl #12  ; [pp+0x34bf0] AnonymousClosure: (0xaedb9c), in [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onDecrementPressed (0xaedbd4)
    //     0xaeda5c: ldr             x1, [x1, #0xbf0]
    // 0xaeda60: r0 = AllocateClosure()
    //     0xaeda60: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeda64: mov             x1, x0
    // 0xaeda68: ldur            x0, [fp, #-0x10]
    // 0xaeda6c: StoreField: r0->field_13 = r1
    //     0xaeda6c: stur            w1, [x0, #0x13]
    // 0xaeda70: ldur            x2, [fp, #-8]
    // 0xaeda74: r1 = Function 'onIncrementPressed':.
    //     0xaeda74: add             x1, PP, #0x34, lsl #12  ; [pp+0x34bf8] AnonymousClosure: (0xaedabc), in [package:nuonline/app/modules/donation/controllers/qurban_form_controller.dart] QurbanFormController::onIncrementPressed (0xaedaf4)
    //     0xaeda78: ldr             x1, [x1, #0xbf8]
    // 0xaeda7c: r0 = AllocateClosure()
    //     0xaeda7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeda80: mov             x1, x0
    // 0xaeda84: ldur            x0, [fp, #-0x10]
    // 0xaeda88: ArrayStore: r0[0] = r1  ; List_4
    //     0xaeda88: stur            w1, [x0, #0x17]
    // 0xaeda8c: ldur            x1, [fp, #-0x18]
    // 0xaeda90: StoreField: r0->field_1b = r1
    //     0xaeda90: stur            w1, [x0, #0x1b]
    // 0xaeda94: r1 = false
    //     0xaeda94: add             x1, NULL, #0x30  ; false
    // 0xaeda98: StoreField: r0->field_1f = r1
    //     0xaeda98: stur            w1, [x0, #0x1f]
    // 0xaeda9c: LeaveFrame
    //     0xaeda9c: mov             SP, fp
    //     0xaedaa0: ldp             fp, lr, [SP], #0x10
    // 0xaedaa4: ret
    //     0xaedaa4: ret             
    // 0xaedaa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedaa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedaac: b               #0xaed9a0
  }
}
