// lib: , url: package:nuonline/app/modules/donation/widgets/timeline_news_card.dart

// class id: 1050245, size: 0x8
class :: {
}

// class id: 5024, size: 0x10, field offset: 0xc
//   const constructor, 
class TimelineNewsContent extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb935f0, size: 0x388
    // 0xb935f0: EnterFrame
    //     0xb935f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb935f4: mov             fp, SP
    // 0xb935f8: AllocStack(0x40)
    //     0xb935f8: sub             SP, SP, #0x40
    // 0xb935fc: CheckStackOverflow
    //     0xb935fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb93600: cmp             SP, x16
    //     0xb93604: b.ls            #0xb93970
    // 0xb93608: LoadField: r0 = r1->field_b
    //     0xb93608: ldur            w0, [x1, #0xb]
    // 0xb9360c: DecompressPointer r0
    //     0xb9360c: add             x0, x0, HEAP, lsl #32
    // 0xb93610: stur            x0, [fp, #-0x10]
    // 0xb93614: LoadField: r3 = r0->field_7
    //     0xb93614: ldur            w3, [x0, #7]
    // 0xb93618: DecompressPointer r3
    //     0xb93618: add             x3, x3, HEAP, lsl #32
    // 0xb9361c: mov             x1, x3
    // 0xb93620: stur            x3, [fp, #-8]
    // 0xb93624: r2 = "LIST_"
    //     0xb93624: add             x2, PP, #0x47, lsl #12  ; [pp+0x47b08] "LIST_"
    //     0xb93628: ldr             x2, [x2, #0xb08]
    // 0xb9362c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb9362c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb93630: r0 = startsWith()
    //     0xb93630: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xb93634: tbnz            w0, #4, #0xb93780
    // 0xb93638: ldur            x1, [fp, #-0x10]
    // 0xb9363c: ldur            x2, [fp, #-8]
    // 0xb93640: LoadField: r3 = r1->field_b
    //     0xb93640: ldur            w3, [x1, #0xb]
    // 0xb93644: DecompressPointer r3
    //     0xb93644: add             x3, x3, HEAP, lsl #32
    // 0xb93648: stur            x3, [fp, #-0x18]
    // 0xb9364c: r0 = LoadClassIdInstr(r2)
    //     0xb9364c: ldur            x0, [x2, #-1]
    //     0xb93650: ubfx            x0, x0, #0xc, #0x14
    // 0xb93654: r16 = "LIST_CURRENCY"
    //     0xb93654: add             x16, PP, #0x47, lsl #12  ; [pp+0x47b10] "LIST_CURRENCY"
    //     0xb93658: ldr             x16, [x16, #0xb10]
    // 0xb9365c: stp             x16, x2, [SP]
    // 0xb93660: mov             lr, x0
    // 0xb93664: ldr             lr, [x21, lr, lsl #3]
    // 0xb93668: blr             lr
    // 0xb9366c: mov             x3, x0
    // 0xb93670: ldur            x1, [fp, #-0x10]
    // 0xb93674: stur            x3, [fp, #-0x28]
    // 0xb93678: LoadField: r4 = r1->field_f
    //     0xb93678: ldur            w4, [x1, #0xf]
    // 0xb9367c: DecompressPointer r4
    //     0xb9367c: add             x4, x4, HEAP, lsl #32
    // 0xb93680: stur            x4, [fp, #-0x20]
    // 0xb93684: r0 = 60
    //     0xb93684: movz            x0, #0x3c
    // 0xb93688: branchIfSmi(r4, 0xb93694)
    //     0xb93688: tbz             w4, #0, #0xb93694
    // 0xb9368c: r0 = LoadClassIdInstr(r4)
    //     0xb9368c: ldur            x0, [x4, #-1]
    //     0xb93690: ubfx            x0, x0, #0xc, #0x14
    // 0xb93694: sub             x16, x0, #0x3c
    // 0xb93698: cmp             x16, #1
    // 0xb9369c: b.hi            #0xb936f0
    // 0xb936a0: r4 as int
    //     0xb936a0: mov             x0, x4
    //     0xb936a4: mov             x2, NULL
    //     0xb936a8: mov             x1, NULL
    //     0xb936ac: tbz             w0, #0, #0xb936d4
    //     0xb936b0: ldur            x4, [x0, #-1]
    //     0xb936b4: ubfx            x4, x4, #0xc, #0x14
    //     0xb936b8: sub             x4, x4, #0x3c
    //     0xb936bc: cmp             x4, #1
    //     0xb936c0: b.ls            #0xb936d4
    //     0xb936c4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    //     0xb936c8: add             x3, PP, #0x47, lsl #12  ; [pp+0x47b18] Null
    //     0xb936cc: ldr             x3, [x3, #0xb18]
    //     0xb936d0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xb936d4: ldur            x0, [fp, #-0x20]
    // 0xb936d8: r1 = LoadInt32Instr(r0)
    //     0xb936d8: sbfx            x1, x0, #1, #0x1f
    //     0xb936dc: tbz             w0, #0, #0xb936e4
    //     0xb936e0: ldur            x1, [x0, #7]
    // 0xb936e4: r0 = IntExtension.idr()
    //     0xb936e4: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb936e8: mov             x2, x0
    // 0xb936ec: b               #0xb936f4
    // 0xb936f0: r2 = "-"
    //     0xb936f0: ldr             x2, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb936f4: ldur            x1, [fp, #-0x28]
    // 0xb936f8: ldur            x0, [fp, #-0x20]
    // 0xb936fc: stur            x2, [fp, #-0x30]
    // 0xb93700: r3 = 60
    //     0xb93700: movz            x3, #0x3c
    // 0xb93704: branchIfSmi(r0, 0xb93710)
    //     0xb93704: tbz             w0, #0, #0xb93710
    // 0xb93708: r3 = LoadClassIdInstr(r0)
    //     0xb93708: ldur            x3, [x0, #-1]
    //     0xb9370c: ubfx            x3, x3, #0xc, #0x14
    // 0xb93710: str             x0, [SP]
    // 0xb93714: mov             x0, x3
    // 0xb93718: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb93718: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb9371c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb9371c: movz            x17, #0x2b03
    //     0xb93720: add             lr, x0, x17
    //     0xb93724: ldr             lr, [x21, lr, lsl #3]
    //     0xb93728: blr             lr
    // 0xb9372c: mov             x1, x0
    // 0xb93730: ldur            x0, [fp, #-0x28]
    // 0xb93734: tbnz            w0, #4, #0xb9373c
    // 0xb93738: ldur            x1, [fp, #-0x30]
    // 0xb9373c: ldur            x0, [fp, #-0x18]
    // 0xb93740: stur            x1, [fp, #-0x20]
    // 0xb93744: r0 = UserInfo()
    //     0xb93744: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93748: mov             x1, x0
    // 0xb9374c: ldur            x0, [fp, #-0x18]
    // 0xb93750: StoreField: r1->field_b = r0
    //     0xb93750: stur            w0, [x1, #0xb]
    // 0xb93754: ldur            x0, [fp, #-0x20]
    // 0xb93758: StoreField: r1->field_f = r0
    //     0xb93758: stur            w0, [x1, #0xf]
    // 0xb9375c: r0 = false
    //     0xb9375c: add             x0, NULL, #0x30  ; false
    // 0xb93760: ArrayStore: r1[0] = r0  ; List_4
    //     0xb93760: stur            w0, [x1, #0x17]
    // 0xb93764: r3 = Instance_EdgeInsets
    //     0xb93764: add             x3, PP, #0x47, lsl #12  ; [pp+0x47b28] Obj!EdgeInsets@e13121
    //     0xb93768: ldr             x3, [x3, #0xb28]
    // 0xb9376c: StoreField: r1->field_1f = r3
    //     0xb9376c: stur            w3, [x1, #0x1f]
    // 0xb93770: mov             x0, x1
    // 0xb93774: LeaveFrame
    //     0xb93774: mov             SP, fp
    //     0xb93778: ldp             fp, lr, [SP], #0x10
    // 0xb9377c: ret
    //     0xb9377c: ret             
    // 0xb93780: ldur            x1, [fp, #-0x10]
    // 0xb93784: ldur            x2, [fp, #-8]
    // 0xb93788: r3 = Instance_EdgeInsets
    //     0xb93788: add             x3, PP, #0x47, lsl #12  ; [pp+0x47b28] Obj!EdgeInsets@e13121
    //     0xb9378c: ldr             x3, [x3, #0xb28]
    // 0xb93790: r0 = LoadClassIdInstr(r2)
    //     0xb93790: ldur            x0, [x2, #-1]
    //     0xb93794: ubfx            x0, x0, #0xc, #0x14
    // 0xb93798: r16 = "PARAGRAPH"
    //     0xb93798: add             x16, PP, #0x47, lsl #12  ; [pp+0x47b30] "PARAGRAPH"
    //     0xb9379c: ldr             x16, [x16, #0xb30]
    // 0xb937a0: stp             x16, x2, [SP]
    // 0xb937a4: mov             lr, x0
    // 0xb937a8: ldr             lr, [x21, lr, lsl #3]
    // 0xb937ac: blr             lr
    // 0xb937b0: tbnz            w0, #4, #0xb93890
    // 0xb937b4: ldur            x1, [fp, #-0x10]
    // 0xb937b8: LoadField: r0 = r1->field_f
    //     0xb937b8: ldur            w0, [x1, #0xf]
    // 0xb937bc: DecompressPointer r0
    //     0xb937bc: add             x0, x0, HEAP, lsl #32
    // 0xb937c0: r1 = 60
    //     0xb937c0: movz            x1, #0x3c
    // 0xb937c4: branchIfSmi(r0, 0xb937d0)
    //     0xb937c4: tbz             w0, #0, #0xb937d0
    // 0xb937c8: r1 = LoadClassIdInstr(r0)
    //     0xb937c8: ldur            x1, [x0, #-1]
    //     0xb937cc: ubfx            x1, x1, #0xc, #0x14
    // 0xb937d0: str             x0, [SP]
    // 0xb937d4: mov             x0, x1
    // 0xb937d8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb937d8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb937dc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb937dc: movz            x17, #0x2b03
    //     0xb937e0: add             lr, x0, x17
    //     0xb937e4: ldr             lr, [x21, lr, lsl #3]
    //     0xb937e8: blr             lr
    // 0xb937ec: stur            x0, [fp, #-0x18]
    // 0xb937f0: r0 = Linkify()
    //     0xb937f0: bl              #0xb93978  ; AllocateLinkifyStub -> Linkify (size=0x54)
    // 0xb937f4: mov             x3, x0
    // 0xb937f8: ldur            x0, [fp, #-0x18]
    // 0xb937fc: stur            x3, [fp, #-0x20]
    // 0xb93800: StoreField: r3->field_b = r0
    //     0xb93800: stur            w0, [x3, #0xb]
    // 0xb93804: r0 = const [Instance of 'UrlLinkifier', Instance of 'EmailLinkifier']
    //     0xb93804: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b38] List<Linkifier>(2)
    //     0xb93808: ldr             x0, [x0, #0xb38]
    // 0xb9380c: StoreField: r3->field_f = r0
    //     0xb9380c: stur            w0, [x3, #0xf]
    // 0xb93810: r1 = Function '<anonymous closure>':.
    //     0xb93810: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b40] AnonymousClosure: (0xb93984), in [package:nuonline/app/modules/donation/widgets/timeline_news_card.dart] TimelineNewsContent::build (0xb935f0)
    //     0xb93814: ldr             x1, [x1, #0xb40]
    // 0xb93818: r2 = Null
    //     0xb93818: mov             x2, NULL
    // 0xb9381c: r0 = AllocateClosure()
    //     0xb9381c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb93820: mov             x1, x0
    // 0xb93824: ldur            x0, [fp, #-0x20]
    // 0xb93828: StoreField: r0->field_13 = r1
    //     0xb93828: stur            w1, [x0, #0x13]
    // 0xb9382c: r1 = Instance_LinkifyOptions
    //     0xb9382c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b48] Obj!LinkifyOptions@e0e6d1
    //     0xb93830: ldr             x1, [x1, #0xb48]
    // 0xb93834: ArrayStore: r0[0] = r1  ; List_4
    //     0xb93834: stur            w1, [x0, #0x17]
    // 0xb93838: r1 = Instance_TextAlign
    //     0xb93838: ldr             x1, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb9383c: StoreField: r0->field_23 = r1
    //     0xb9383c: stur            w1, [x0, #0x23]
    // 0xb93840: r1 = Instance_TextOverflow
    //     0xb93840: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xb93844: ldr             x1, [x1, #0xc60]
    // 0xb93848: StoreField: r0->field_2f = r1
    //     0xb93848: stur            w1, [x0, #0x2f]
    // 0xb9384c: d0 = 1.000000
    //     0xb9384c: fmov            d0, #1.00000000
    // 0xb93850: StoreField: r0->field_33 = d0
    //     0xb93850: stur            d0, [x0, #0x33]
    // 0xb93854: r1 = true
    //     0xb93854: add             x1, NULL, #0x20  ; true
    // 0xb93858: StoreField: r0->field_3b = r1
    //     0xb93858: stur            w1, [x0, #0x3b]
    // 0xb9385c: r2 = Instance_TextWidthBasis
    //     0xb9385c: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xb93860: ldr             x2, [x2, #0x1d8]
    // 0xb93864: StoreField: r0->field_47 = r2
    //     0xb93864: stur            w2, [x0, #0x47]
    // 0xb93868: StoreField: r0->field_4f = r1
    //     0xb93868: stur            w1, [x0, #0x4f]
    // 0xb9386c: r0 = Padding()
    //     0xb9386c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb93870: r2 = Instance_EdgeInsets
    //     0xb93870: add             x2, PP, #0x47, lsl #12  ; [pp+0x47b28] Obj!EdgeInsets@e13121
    //     0xb93874: ldr             x2, [x2, #0xb28]
    // 0xb93878: StoreField: r0->field_f = r2
    //     0xb93878: stur            w2, [x0, #0xf]
    // 0xb9387c: ldur            x1, [fp, #-0x20]
    // 0xb93880: StoreField: r0->field_b = r1
    //     0xb93880: stur            w1, [x0, #0xb]
    // 0xb93884: LeaveFrame
    //     0xb93884: mov             SP, fp
    //     0xb93888: ldp             fp, lr, [SP], #0x10
    // 0xb9388c: ret
    //     0xb9388c: ret             
    // 0xb93890: ldur            x1, [fp, #-0x10]
    // 0xb93894: ldur            x0, [fp, #-8]
    // 0xb93898: r2 = Instance_EdgeInsets
    //     0xb93898: add             x2, PP, #0x47, lsl #12  ; [pp+0x47b28] Obj!EdgeInsets@e13121
    //     0xb9389c: ldr             x2, [x2, #0xb28]
    // 0xb938a0: r3 = LoadClassIdInstr(r0)
    //     0xb938a0: ldur            x3, [x0, #-1]
    //     0xb938a4: ubfx            x3, x3, #0xc, #0x14
    // 0xb938a8: r16 = "IMAGE"
    //     0xb938a8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47b50] "IMAGE"
    //     0xb938ac: ldr             x16, [x16, #0xb50]
    // 0xb938b0: stp             x16, x0, [SP]
    // 0xb938b4: mov             x0, x3
    // 0xb938b8: mov             lr, x0
    // 0xb938bc: ldr             lr, [x21, lr, lsl #3]
    // 0xb938c0: blr             lr
    // 0xb938c4: tbnz            w0, #4, #0xb9395c
    // 0xb938c8: ldur            x0, [fp, #-0x10]
    // 0xb938cc: LoadField: r1 = r0->field_f
    //     0xb938cc: ldur            w1, [x0, #0xf]
    // 0xb938d0: DecompressPointer r1
    //     0xb938d0: add             x1, x1, HEAP, lsl #32
    // 0xb938d4: r0 = 60
    //     0xb938d4: movz            x0, #0x3c
    // 0xb938d8: branchIfSmi(r1, 0xb938e4)
    //     0xb938d8: tbz             w1, #0, #0xb938e4
    // 0xb938dc: r0 = LoadClassIdInstr(r1)
    //     0xb938dc: ldur            x0, [x1, #-1]
    //     0xb938e0: ubfx            x0, x0, #0xc, #0x14
    // 0xb938e4: str             x1, [SP]
    // 0xb938e8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb938e8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb938ec: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb938ec: movz            x17, #0x2b03
    //     0xb938f0: add             lr, x0, x17
    //     0xb938f4: ldr             lr, [x21, lr, lsl #3]
    //     0xb938f8: blr             lr
    // 0xb938fc: stur            x0, [fp, #-8]
    // 0xb93900: r0 = NFadeInImageNetwork()
    //     0xb93900: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xb93904: mov             x1, x0
    // 0xb93908: ldur            x0, [fp, #-8]
    // 0xb9390c: stur            x1, [fp, #-0x10]
    // 0xb93910: StoreField: r1->field_b = r0
    //     0xb93910: stur            w0, [x1, #0xb]
    // 0xb93914: r0 = "packages/nuikit/assets/images/icons/image_load_light.png"
    //     0xb93914: add             x0, PP, #0x35, lsl #12  ; [pp+0x35508] "packages/nuikit/assets/images/icons/image_load_light.png"
    //     0xb93918: ldr             x0, [x0, #0x508]
    // 0xb9391c: StoreField: r1->field_f = r0
    //     0xb9391c: stur            w0, [x1, #0xf]
    // 0xb93920: r0 = "packages/nuikit/assets/images/icons/image_load_dark.png"
    //     0xb93920: add             x0, PP, #0x35, lsl #12  ; [pp+0x35510] "packages/nuikit/assets/images/icons/image_load_dark.png"
    //     0xb93924: ldr             x0, [x0, #0x510]
    // 0xb93928: StoreField: r1->field_13 = r0
    //     0xb93928: stur            w0, [x1, #0x13]
    // 0xb9392c: r0 = Instance_BoxFit
    //     0xb9392c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xb93930: ldr             x0, [x0, #0xa28]
    // 0xb93934: ArrayStore: r1[0] = r0  ; List_4
    //     0xb93934: stur            w0, [x1, #0x17]
    // 0xb93938: r0 = Padding()
    //     0xb93938: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9393c: r1 = Instance_EdgeInsets
    //     0xb9393c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b28] Obj!EdgeInsets@e13121
    //     0xb93940: ldr             x1, [x1, #0xb28]
    // 0xb93944: StoreField: r0->field_f = r1
    //     0xb93944: stur            w1, [x0, #0xf]
    // 0xb93948: ldur            x1, [fp, #-0x10]
    // 0xb9394c: StoreField: r0->field_b = r1
    //     0xb9394c: stur            w1, [x0, #0xb]
    // 0xb93950: LeaveFrame
    //     0xb93950: mov             SP, fp
    //     0xb93954: ldp             fp, lr, [SP], #0x10
    // 0xb93958: ret
    //     0xb93958: ret             
    // 0xb9395c: r0 = Instance_SizedBox
    //     0xb9395c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb93960: ldr             x0, [x0, #0xc40]
    // 0xb93964: LeaveFrame
    //     0xb93964: mov             SP, fp
    //     0xb93968: ldp             fp, lr, [SP], #0x10
    // 0xb9396c: ret
    //     0xb9396c: ret             
    // 0xb93970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb93970: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb93974: b               #0xb93608
  }
  [closure] Future<void> <anonymous closure>(dynamic, LinkableElement) async {
    // ** addr: 0xb93984, size: 0x68
    // 0xb93984: EnterFrame
    //     0xb93984: stp             fp, lr, [SP, #-0x10]!
    //     0xb93988: mov             fp, SP
    // 0xb9398c: AllocStack(0x18)
    //     0xb9398c: sub             SP, SP, #0x18
    // 0xb93990: SetupParameters(TimelineNewsContent this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xb93990: stur            NULL, [fp, #-8]
    //     0xb93994: movz            x0, #0
    //     0xb93998: add             x1, fp, w0, sxtw #2
    //     0xb9399c: ldr             x1, [x1, #0x18]
    //     0xb939a0: add             x2, fp, w0, sxtw #2
    //     0xb939a4: ldr             x2, [x2, #0x10]
    //     0xb939a8: stur            x2, [fp, #-0x18]
    //     0xb939ac: ldur            w3, [x1, #0x17]
    //     0xb939b0: add             x3, x3, HEAP, lsl #32
    //     0xb939b4: stur            x3, [fp, #-0x10]
    // 0xb939b8: CheckStackOverflow
    //     0xb939b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb939bc: cmp             SP, x16
    //     0xb939c0: b.ls            #0xb939e4
    // 0xb939c4: InitAsync() -> Future<void?>
    //     0xb939c4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb939c8: bl              #0x661298  ; InitAsyncStub
    // 0xb939cc: ldur            x0, [fp, #-0x18]
    // 0xb939d0: LoadField: r1 = r0->field_f
    //     0xb939d0: ldur            w1, [x0, #0xf]
    // 0xb939d4: DecompressPointer r1
    //     0xb939d4: add             x1, x1, HEAP, lsl #32
    // 0xb939d8: r0 = handle()
    //     0xb939d8: bl              #0xa3d0f4  ; [package:nuonline/app/modules/url_redirector/url_redirector.dart] UrlRedirector::handle
    // 0xb939dc: r0 = Null
    //     0xb939dc: mov             x0, NULL
    // 0xb939e0: r0 = ReturnAsyncNotFuture()
    //     0xb939e0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb939e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb939e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb939e8: b               #0xb939c4
  }
}

// class id: 5025, size: 0x14, field offset: 0xc
//   const constructor, 
class TimelineNewsCard extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9320c, size: 0xf0
    // 0xb9320c: EnterFrame
    //     0xb9320c: stp             fp, lr, [SP, #-0x10]!
    //     0xb93210: mov             fp, SP
    // 0xb93214: AllocStack(0x40)
    //     0xb93214: sub             SP, SP, #0x40
    // 0xb93218: CheckStackOverflow
    //     0xb93218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9321c: cmp             SP, x16
    //     0xb93220: b.ls            #0xb932f4
    // 0xb93224: LoadField: r0 = r1->field_b
    //     0xb93224: ldur            w0, [x1, #0xb]
    // 0xb93228: DecompressPointer r0
    //     0xb93228: add             x0, x0, HEAP, lsl #32
    // 0xb9322c: stur            x0, [fp, #-0x18]
    // 0xb93230: cmp             w0, NULL
    // 0xb93234: b.ne            #0xb9324c
    // 0xb93238: r1 = Null
    //     0xb93238: mov             x1, NULL
    // 0xb9323c: r0 = TimelineCard.loading()
    //     0xb9323c: bl              #0xb932fc  ; [package:nuonline/app/modules/donation/widgets/timeline_news_card.dart] TimelineCard::TimelineCard.loading
    // 0xb93240: LeaveFrame
    //     0xb93240: mov             SP, fp
    //     0xb93244: ldp             fp, lr, [SP], #0x10
    // 0xb93248: ret
    //     0xb93248: ret             
    // 0xb9324c: LoadField: r3 = r1->field_f
    //     0xb9324c: ldur            w3, [x1, #0xf]
    // 0xb93250: DecompressPointer r3
    //     0xb93250: add             x3, x3, HEAP, lsl #32
    // 0xb93254: stur            x3, [fp, #-0x10]
    // 0xb93258: LoadField: r5 = r0->field_f
    //     0xb93258: ldur            w5, [x0, #0xf]
    // 0xb9325c: DecompressPointer r5
    //     0xb9325c: add             x5, x5, HEAP, lsl #32
    // 0xb93260: stur            x5, [fp, #-8]
    // 0xb93264: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb93264: ldur            w1, [x0, #0x17]
    // 0xb93268: DecompressPointer r1
    //     0xb93268: add             x1, x1, HEAP, lsl #32
    // 0xb9326c: r0 = DateTimeExtensions.gregorian()
    //     0xb9326c: bl              #0x81f220  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.gregorian
    // 0xb93270: mov             x3, x0
    // 0xb93274: ldur            x0, [fp, #-0x18]
    // 0xb93278: stur            x3, [fp, #-0x28]
    // 0xb9327c: LoadField: r4 = r0->field_13
    //     0xb9327c: ldur            w4, [x0, #0x13]
    // 0xb93280: DecompressPointer r4
    //     0xb93280: add             x4, x4, HEAP, lsl #32
    // 0xb93284: stur            x4, [fp, #-0x20]
    // 0xb93288: r1 = Function '<anonymous closure>':.
    //     0xb93288: add             x1, PP, #0x40, lsl #12  ; [pp+0x402d0] AnonymousClosure: (0xb935c4), in [package:nuonline/app/modules/donation/widgets/timeline_news_card.dart] TimelineNewsCard::build (0xb9320c)
    //     0xb9328c: ldr             x1, [x1, #0x2d0]
    // 0xb93290: r2 = Null
    //     0xb93290: mov             x2, NULL
    // 0xb93294: r0 = AllocateClosure()
    //     0xb93294: bl              #0xec1630  ; AllocateClosureStub
    // 0xb93298: r16 = <TimelineNewsContent>
    //     0xb93298: add             x16, PP, #0x40, lsl #12  ; [pp+0x402d8] TypeArguments: <TimelineNewsContent>
    //     0xb9329c: ldr             x16, [x16, #0x2d8]
    // 0xb932a0: ldur            lr, [fp, #-0x20]
    // 0xb932a4: stp             lr, x16, [SP, #8]
    // 0xb932a8: str             x0, [SP]
    // 0xb932ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb932ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb932b0: r0 = map()
    //     0xb932b0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb932b4: LoadField: r1 = r0->field_7
    //     0xb932b4: ldur            w1, [x0, #7]
    // 0xb932b8: DecompressPointer r1
    //     0xb932b8: add             x1, x1, HEAP, lsl #32
    // 0xb932bc: mov             x2, x0
    // 0xb932c0: r0 = _GrowableList.of()
    //     0xb932c0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb932c4: ldur            x16, [fp, #-0x28]
    // 0xb932c8: str             x16, [SP]
    // 0xb932cc: mov             x2, x0
    // 0xb932d0: ldur            x3, [fp, #-0x10]
    // 0xb932d4: ldur            x5, [fp, #-8]
    // 0xb932d8: r1 = Null
    //     0xb932d8: mov             x1, NULL
    // 0xb932dc: r4 = const [0, 0x5, 0x1, 0x4, subtitle, 0x4, null]
    //     0xb932dc: add             x4, PP, #0x40, lsl #12  ; [pp+0x402e0] List(7) [0, 0x5, 0x1, 0x4, "subtitle", 0x4, Null]
    //     0xb932e0: ldr             x4, [x4, #0x2e0]
    // 0xb932e4: r0 = TimelineCard.withHeader()
    //     0xb932e4: bl              #0xb3ae4c  ; [package:nuonline/app/modules/donation/widgets/timeline_news_card.dart] TimelineCard::TimelineCard.withHeader
    // 0xb932e8: LeaveFrame
    //     0xb932e8: mov             SP, fp
    //     0xb932ec: ldp             fp, lr, [SP], #0x10
    // 0xb932f0: ret
    //     0xb932f0: ret             
    // 0xb932f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb932f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb932f8: b               #0xb93224
  }
  [closure] TimelineNewsContent <anonymous closure>(dynamic, DonationNewsContent) {
    // ** addr: 0xb935c4, size: 0x20
    // 0xb935c4: EnterFrame
    //     0xb935c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb935c8: mov             fp, SP
    // 0xb935cc: r0 = TimelineNewsContent()
    //     0xb935cc: bl              #0xb935e4  ; AllocateTimelineNewsContentStub -> TimelineNewsContent (size=0x10)
    // 0xb935d0: ldr             x1, [fp, #0x10]
    // 0xb935d4: StoreField: r0->field_b = r1
    //     0xb935d4: stur            w1, [x0, #0xb]
    // 0xb935d8: LeaveFrame
    //     0xb935d8: mov             SP, fp
    //     0xb935dc: ldp             fp, lr, [SP], #0x10
    // 0xb935e0: ret
    //     0xb935e0: ret             
  }
}

// class id: 5026, size: 0x18, field offset: 0xc
//   const constructor, 
class TimelineCard extends StatelessWidget {

  factory _ TimelineCard.withHeader(/* No info */) {
    // ** addr: 0xb3ae4c, size: 0x4b4
    // 0xb3ae4c: EnterFrame
    //     0xb3ae4c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3ae50: mov             fp, SP
    // 0xb3ae54: AllocStack(0x68)
    //     0xb3ae54: sub             SP, SP, #0x68
    // 0xb3ae58: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */, dynamic _ /* r5 => r5, fp-0x30 */, {dynamic color = Null /* r6, fp-0x18 */, dynamic icon = Null /* r7 */, dynamic subtitle = Null /* r0, fp-0x10 */})
    //     0xb3ae58: stur            x2, [fp, #-0x20]
    //     0xb3ae5c: stur            x3, [fp, #-0x28]
    //     0xb3ae60: stur            x5, [fp, #-0x30]
    //     0xb3ae64: ldur            w0, [x4, #0x13]
    //     0xb3ae68: ldur            w1, [x4, #0x1f]
    //     0xb3ae6c: add             x1, x1, HEAP, lsl #32
    //     0xb3ae70: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    //     0xb3ae74: cmp             w1, w16
    //     0xb3ae78: b.ne            #0xb3ae9c
    //     0xb3ae7c: ldur            w1, [x4, #0x23]
    //     0xb3ae80: add             x1, x1, HEAP, lsl #32
    //     0xb3ae84: sub             w6, w0, w1
    //     0xb3ae88: add             x1, fp, w6, sxtw #2
    //     0xb3ae8c: ldr             x1, [x1, #8]
    //     0xb3ae90: mov             x6, x1
    //     0xb3ae94: movz            x1, #0x1
    //     0xb3ae98: b               #0xb3aea4
    //     0xb3ae9c: mov             x6, NULL
    //     0xb3aea0: movz            x1, #0
    //     0xb3aea4: stur            x6, [fp, #-0x18]
    //     0xb3aea8: lsl             x7, x1, #1
    //     0xb3aeac: lsl             w8, w7, #1
    //     0xb3aeb0: add             w9, w8, #8
    //     0xb3aeb4: add             x16, x4, w9, sxtw #1
    //     0xb3aeb8: ldur            w10, [x16, #0xf]
    //     0xb3aebc: add             x10, x10, HEAP, lsl #32
    //     0xb3aec0: add             x16, PP, #8, lsl #12  ; [pp+0x8828] "icon"
    //     0xb3aec4: ldr             x16, [x16, #0x828]
    //     0xb3aec8: cmp             w10, w16
    //     0xb3aecc: b.ne            #0xb3af00
    //     0xb3aed0: add             w1, w8, #0xa
    //     0xb3aed4: add             x16, x4, w1, sxtw #1
    //     0xb3aed8: ldur            w8, [x16, #0xf]
    //     0xb3aedc: add             x8, x8, HEAP, lsl #32
    //     0xb3aee0: sub             w1, w0, w8
    //     0xb3aee4: add             x8, fp, w1, sxtw #2
    //     0xb3aee8: ldr             x8, [x8, #8]
    //     0xb3aeec: add             w1, w7, #2
    //     0xb3aef0: sbfx            x7, x1, #1, #0x1f
    //     0xb3aef4: mov             x1, x7
    //     0xb3aef8: mov             x7, x8
    //     0xb3aefc: b               #0xb3af04
    //     0xb3af00: mov             x7, NULL
    //     0xb3af04: lsl             x8, x1, #1
    //     0xb3af08: lsl             w1, w8, #1
    //     0xb3af0c: add             w8, w1, #8
    //     0xb3af10: add             x16, x4, w8, sxtw #1
    //     0xb3af14: ldur            w9, [x16, #0xf]
    //     0xb3af18: add             x9, x9, HEAP, lsl #32
    //     0xb3af1c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc568] "subtitle"
    //     0xb3af20: ldr             x16, [x16, #0x568]
    //     0xb3af24: cmp             w9, w16
    //     0xb3af28: b.ne            #0xb3af4c
    //     0xb3af2c: add             w8, w1, #0xa
    //     0xb3af30: add             x16, x4, w8, sxtw #1
    //     0xb3af34: ldur            w1, [x16, #0xf]
    //     0xb3af38: add             x1, x1, HEAP, lsl #32
    //     0xb3af3c: sub             w4, w0, w1
    //     0xb3af40: add             x0, fp, w4, sxtw #2
    //     0xb3af44: ldr             x0, [x0, #8]
    //     0xb3af48: b               #0xb3af50
    //     0xb3af4c: mov             x0, NULL
    //     0xb3af50: stur            x0, [fp, #-0x10]
    // 0xb3af54: CheckStackOverflow
    //     0xb3af54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3af58: cmp             SP, x16
    //     0xb3af5c: b.ls            #0xb3b2f0
    // 0xb3af60: cmp             w7, NULL
    // 0xb3af64: b.ne            #0xb3af74
    // 0xb3af68: r1 = Instance_SizedBox
    //     0xb3af68: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb3af6c: ldr             x1, [x1, #0xc40]
    // 0xb3af70: b               #0xb3af78
    // 0xb3af74: mov             x1, x7
    // 0xb3af78: stur            x1, [fp, #-8]
    // 0xb3af7c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3af7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3af80: ldr             x0, [x0, #0x2670]
    //     0xb3af84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3af88: cmp             w0, w16
    //     0xb3af8c: b.ne            #0xb3af98
    //     0xb3af90: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb3af94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb3af98: r0 = GetNavigation.textTheme()
    //     0xb3af98: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb3af9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb3af9c: ldur            w1, [x0, #0x17]
    // 0xb3afa0: DecompressPointer r1
    //     0xb3afa0: add             x1, x1, HEAP, lsl #32
    // 0xb3afa4: cmp             w1, NULL
    // 0xb3afa8: b.eq            #0xb3b2f8
    // 0xb3afac: r16 = Instance_FontWeight
    //     0xb3afac: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb3afb0: ldr             x16, [x16, #0xe20]
    // 0xb3afb4: str             x16, [SP]
    // 0xb3afb8: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb3afb8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb3afbc: ldr             x4, [x4, #0xfe0]
    // 0xb3afc0: r0 = copyWith()
    //     0xb3afc0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3afc4: stur            x0, [fp, #-0x38]
    // 0xb3afc8: r0 = Text()
    //     0xb3afc8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb3afcc: mov             x2, x0
    // 0xb3afd0: ldur            x0, [fp, #-0x30]
    // 0xb3afd4: stur            x2, [fp, #-0x40]
    // 0xb3afd8: StoreField: r2->field_b = r0
    //     0xb3afd8: stur            w0, [x2, #0xb]
    // 0xb3afdc: ldur            x0, [fp, #-0x38]
    // 0xb3afe0: StoreField: r2->field_13 = r0
    //     0xb3afe0: stur            w0, [x2, #0x13]
    // 0xb3afe4: r0 = Instance__LinearTextScaler
    //     0xb3afe4: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb3afe8: StoreField: r2->field_33 = r0
    //     0xb3afe8: stur            w0, [x2, #0x33]
    // 0xb3afec: r1 = <FlexParentData>
    //     0xb3afec: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb3aff0: ldr             x1, [x1, #0x720]
    // 0xb3aff4: r0 = Expanded()
    //     0xb3aff4: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3aff8: mov             x3, x0
    // 0xb3affc: r0 = 1
    //     0xb3affc: movz            x0, #0x1
    // 0xb3b000: stur            x3, [fp, #-0x30]
    // 0xb3b004: StoreField: r3->field_13 = r0
    //     0xb3b004: stur            x0, [x3, #0x13]
    // 0xb3b008: r0 = Instance_FlexFit
    //     0xb3b008: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb3b00c: ldr             x0, [x0, #0x728]
    // 0xb3b010: StoreField: r3->field_1b = r0
    //     0xb3b010: stur            w0, [x3, #0x1b]
    // 0xb3b014: ldur            x0, [fp, #-0x40]
    // 0xb3b018: StoreField: r3->field_b = r0
    //     0xb3b018: stur            w0, [x3, #0xb]
    // 0xb3b01c: r1 = Null
    //     0xb3b01c: mov             x1, NULL
    // 0xb3b020: r2 = 4
    //     0xb3b020: movz            x2, #0x4
    // 0xb3b024: r0 = AllocateArray()
    //     0xb3b024: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3b028: mov             x2, x0
    // 0xb3b02c: ldur            x0, [fp, #-8]
    // 0xb3b030: stur            x2, [fp, #-0x38]
    // 0xb3b034: StoreField: r2->field_f = r0
    //     0xb3b034: stur            w0, [x2, #0xf]
    // 0xb3b038: ldur            x0, [fp, #-0x30]
    // 0xb3b03c: StoreField: r2->field_13 = r0
    //     0xb3b03c: stur            w0, [x2, #0x13]
    // 0xb3b040: r1 = <Widget>
    //     0xb3b040: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3b044: r0 = AllocateGrowableArray()
    //     0xb3b044: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3b048: mov             x1, x0
    // 0xb3b04c: ldur            x0, [fp, #-0x38]
    // 0xb3b050: stur            x1, [fp, #-8]
    // 0xb3b054: StoreField: r1->field_f = r0
    //     0xb3b054: stur            w0, [x1, #0xf]
    // 0xb3b058: r2 = 4
    //     0xb3b058: movz            x2, #0x4
    // 0xb3b05c: StoreField: r1->field_b = r2
    //     0xb3b05c: stur            w2, [x1, #0xb]
    // 0xb3b060: r0 = Row()
    //     0xb3b060: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3b064: mov             x3, x0
    // 0xb3b068: r0 = Instance_Axis
    //     0xb3b068: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb3b06c: stur            x3, [fp, #-0x30]
    // 0xb3b070: StoreField: r3->field_f = r0
    //     0xb3b070: stur            w0, [x3, #0xf]
    // 0xb3b074: r0 = Instance_MainAxisAlignment
    //     0xb3b074: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb3b078: ldr             x0, [x0, #0x730]
    // 0xb3b07c: StoreField: r3->field_13 = r0
    //     0xb3b07c: stur            w0, [x3, #0x13]
    // 0xb3b080: r4 = Instance_MainAxisSize
    //     0xb3b080: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb3b084: ldr             x4, [x4, #0x738]
    // 0xb3b088: ArrayStore: r3[0] = r4  ; List_4
    //     0xb3b088: stur            w4, [x3, #0x17]
    // 0xb3b08c: r1 = Instance_CrossAxisAlignment
    //     0xb3b08c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb3b090: ldr             x1, [x1, #0x740]
    // 0xb3b094: StoreField: r3->field_1b = r1
    //     0xb3b094: stur            w1, [x3, #0x1b]
    // 0xb3b098: r5 = Instance_VerticalDirection
    //     0xb3b098: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb3b09c: ldr             x5, [x5, #0x748]
    // 0xb3b0a0: StoreField: r3->field_23 = r5
    //     0xb3b0a0: stur            w5, [x3, #0x23]
    // 0xb3b0a4: r6 = Instance_Clip
    //     0xb3b0a4: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb3b0a8: ldr             x6, [x6, #0x750]
    // 0xb3b0ac: StoreField: r3->field_2b = r6
    //     0xb3b0ac: stur            w6, [x3, #0x2b]
    // 0xb3b0b0: StoreField: r3->field_2f = rZR
    //     0xb3b0b0: stur            xzr, [x3, #0x2f]
    // 0xb3b0b4: ldur            x1, [fp, #-8]
    // 0xb3b0b8: StoreField: r3->field_b = r1
    //     0xb3b0b8: stur            w1, [x3, #0xb]
    // 0xb3b0bc: r1 = Null
    //     0xb3b0bc: mov             x1, NULL
    // 0xb3b0c0: r2 = 2
    //     0xb3b0c0: movz            x2, #0x2
    // 0xb3b0c4: r0 = AllocateArray()
    //     0xb3b0c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3b0c8: mov             x2, x0
    // 0xb3b0cc: ldur            x0, [fp, #-0x30]
    // 0xb3b0d0: stur            x2, [fp, #-8]
    // 0xb3b0d4: StoreField: r2->field_f = r0
    //     0xb3b0d4: stur            w0, [x2, #0xf]
    // 0xb3b0d8: r1 = <Widget>
    //     0xb3b0d8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3b0dc: r0 = AllocateGrowableArray()
    //     0xb3b0dc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3b0e0: mov             x1, x0
    // 0xb3b0e4: ldur            x0, [fp, #-8]
    // 0xb3b0e8: stur            x1, [fp, #-0x30]
    // 0xb3b0ec: StoreField: r1->field_f = r0
    //     0xb3b0ec: stur            w0, [x1, #0xf]
    // 0xb3b0f0: r0 = 2
    //     0xb3b0f0: movz            x0, #0x2
    // 0xb3b0f4: StoreField: r1->field_b = r0
    //     0xb3b0f4: stur            w0, [x1, #0xb]
    // 0xb3b0f8: ldur            x0, [fp, #-0x10]
    // 0xb3b0fc: cmp             w0, NULL
    // 0xb3b100: b.eq            #0xb3b1b4
    // 0xb3b104: r0 = GetNavigation.textTheme()
    //     0xb3b104: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb3b108: LoadField: r1 = r0->field_27
    //     0xb3b108: ldur            w1, [x0, #0x27]
    // 0xb3b10c: DecompressPointer r1
    //     0xb3b10c: add             x1, x1, HEAP, lsl #32
    // 0xb3b110: cmp             w1, NULL
    // 0xb3b114: b.eq            #0xb3b2fc
    // 0xb3b118: r16 = Instance_FontWeight
    //     0xb3b118: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xb3b11c: ldr             x16, [x16, #0xc50]
    // 0xb3b120: r30 = 10.000000
    //     0xb3b120: ldr             lr, [PP, #0x6a00]  ; [pp+0x6a00] 10
    // 0xb3b124: stp             lr, x16, [SP, #8]
    // 0xb3b128: r16 = 1.200000
    //     0xb3b128: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d378] 1.2
    //     0xb3b12c: ldr             x16, [x16, #0x378]
    // 0xb3b130: str             x16, [SP]
    // 0xb3b134: r4 = const [0, 0x4, 0x3, 0x1, fontSize, 0x2, fontWeight, 0x1, height, 0x3, null]
    //     0xb3b134: add             x4, PP, #0x32, lsl #12  ; [pp+0x328f0] List(11) [0, 0x4, 0x3, 0x1, "fontSize", 0x2, "fontWeight", 0x1, "height", 0x3, Null]
    //     0xb3b138: ldr             x4, [x4, #0x8f0]
    // 0xb3b13c: r0 = copyWith()
    //     0xb3b13c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3b140: stur            x0, [fp, #-8]
    // 0xb3b144: r0 = Text()
    //     0xb3b144: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb3b148: mov             x3, x0
    // 0xb3b14c: ldur            x0, [fp, #-0x10]
    // 0xb3b150: stur            x3, [fp, #-0x38]
    // 0xb3b154: StoreField: r3->field_b = r0
    //     0xb3b154: stur            w0, [x3, #0xb]
    // 0xb3b158: ldur            x0, [fp, #-8]
    // 0xb3b15c: StoreField: r3->field_13 = r0
    //     0xb3b15c: stur            w0, [x3, #0x13]
    // 0xb3b160: r0 = Instance__LinearTextScaler
    //     0xb3b160: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb3b164: StoreField: r3->field_33 = r0
    //     0xb3b164: stur            w0, [x3, #0x33]
    // 0xb3b168: r1 = Null
    //     0xb3b168: mov             x1, NULL
    // 0xb3b16c: r2 = 4
    //     0xb3b16c: movz            x2, #0x4
    // 0xb3b170: r0 = AllocateArray()
    //     0xb3b170: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3b174: stur            x0, [fp, #-8]
    // 0xb3b178: r16 = Instance_SizedBox
    //     0xb3b178: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb3b17c: ldr             x16, [x16, #0x4a0]
    // 0xb3b180: StoreField: r0->field_f = r16
    //     0xb3b180: stur            w16, [x0, #0xf]
    // 0xb3b184: ldur            x1, [fp, #-0x38]
    // 0xb3b188: StoreField: r0->field_13 = r1
    //     0xb3b188: stur            w1, [x0, #0x13]
    // 0xb3b18c: r1 = <Widget>
    //     0xb3b18c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3b190: r0 = AllocateGrowableArray()
    //     0xb3b190: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3b194: mov             x1, x0
    // 0xb3b198: ldur            x0, [fp, #-8]
    // 0xb3b19c: StoreField: r1->field_f = r0
    //     0xb3b19c: stur            w0, [x1, #0xf]
    // 0xb3b1a0: r0 = 4
    //     0xb3b1a0: movz            x0, #0x4
    // 0xb3b1a4: StoreField: r1->field_b = r0
    //     0xb3b1a4: stur            w0, [x1, #0xb]
    // 0xb3b1a8: mov             x2, x1
    // 0xb3b1ac: ldur            x1, [fp, #-0x30]
    // 0xb3b1b0: r0 = addAll()
    //     0xb3b1b0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb3b1b4: ldur            x0, [fp, #-0x30]
    // 0xb3b1b8: LoadField: r1 = r0->field_b
    //     0xb3b1b8: ldur            w1, [x0, #0xb]
    // 0xb3b1bc: LoadField: r2 = r0->field_f
    //     0xb3b1bc: ldur            w2, [x0, #0xf]
    // 0xb3b1c0: DecompressPointer r2
    //     0xb3b1c0: add             x2, x2, HEAP, lsl #32
    // 0xb3b1c4: LoadField: r3 = r2->field_b
    //     0xb3b1c4: ldur            w3, [x2, #0xb]
    // 0xb3b1c8: r2 = LoadInt32Instr(r1)
    //     0xb3b1c8: sbfx            x2, x1, #1, #0x1f
    // 0xb3b1cc: stur            x2, [fp, #-0x48]
    // 0xb3b1d0: r1 = LoadInt32Instr(r3)
    //     0xb3b1d0: sbfx            x1, x3, #1, #0x1f
    // 0xb3b1d4: cmp             x2, x1
    // 0xb3b1d8: b.ne            #0xb3b1e4
    // 0xb3b1dc: mov             x1, x0
    // 0xb3b1e0: r0 = _growToNextCapacity()
    //     0xb3b1e0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb3b1e4: ldur            x0, [fp, #-0x30]
    // 0xb3b1e8: ldur            x1, [fp, #-0x48]
    // 0xb3b1ec: add             x2, x1, #1
    // 0xb3b1f0: stur            x2, [fp, #-0x50]
    // 0xb3b1f4: lsl             x3, x2, #1
    // 0xb3b1f8: StoreField: r0->field_b = r3
    //     0xb3b1f8: stur            w3, [x0, #0xb]
    // 0xb3b1fc: LoadField: r3 = r0->field_f
    //     0xb3b1fc: ldur            w3, [x0, #0xf]
    // 0xb3b200: DecompressPointer r3
    //     0xb3b200: add             x3, x3, HEAP, lsl #32
    // 0xb3b204: add             x4, x3, x1, lsl #2
    // 0xb3b208: r16 = Instance_SizedBox
    //     0xb3b208: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bcd8] Obj!SizedBox@e1e1c1
    //     0xb3b20c: ldr             x16, [x16, #0xcd8]
    // 0xb3b210: StoreField: r4->field_f = r16
    //     0xb3b210: stur            w16, [x4, #0xf]
    // 0xb3b214: LoadField: r1 = r3->field_b
    //     0xb3b214: ldur            w1, [x3, #0xb]
    // 0xb3b218: r3 = LoadInt32Instr(r1)
    //     0xb3b218: sbfx            x3, x1, #1, #0x1f
    // 0xb3b21c: cmp             x2, x3
    // 0xb3b220: b.ne            #0xb3b22c
    // 0xb3b224: mov             x1, x0
    // 0xb3b228: r0 = _growToNextCapacity()
    //     0xb3b228: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb3b22c: ldur            x3, [fp, #-0x28]
    // 0xb3b230: ldur            x4, [fp, #-0x18]
    // 0xb3b234: ldur            x1, [fp, #-0x50]
    // 0xb3b238: ldur            x0, [fp, #-0x30]
    // 0xb3b23c: add             x2, x1, #1
    // 0xb3b240: lsl             x5, x2, #1
    // 0xb3b244: StoreField: r0->field_b = r5
    //     0xb3b244: stur            w5, [x0, #0xb]
    // 0xb3b248: LoadField: r2 = r0->field_f
    //     0xb3b248: ldur            w2, [x0, #0xf]
    // 0xb3b24c: DecompressPointer r2
    //     0xb3b24c: add             x2, x2, HEAP, lsl #32
    // 0xb3b250: add             x5, x2, x1, lsl #2
    // 0xb3b254: r16 = Instance_Divider
    //     0xb3b254: add             x16, PP, #0x32, lsl #12  ; [pp+0x328f8] Obj!Divider@e257c1
    //     0xb3b258: ldr             x16, [x16, #0x8f8]
    // 0xb3b25c: StoreField: r5->field_f = r16
    //     0xb3b25c: stur            w16, [x5, #0xf]
    // 0xb3b260: mov             x1, x0
    // 0xb3b264: ldur            x2, [fp, #-0x20]
    // 0xb3b268: r0 = addAll()
    //     0xb3b268: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb3b26c: r0 = Column()
    //     0xb3b26c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3b270: mov             x1, x0
    // 0xb3b274: r0 = Instance_Axis
    //     0xb3b274: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb3b278: stur            x1, [fp, #-8]
    // 0xb3b27c: StoreField: r1->field_f = r0
    //     0xb3b27c: stur            w0, [x1, #0xf]
    // 0xb3b280: r0 = Instance_MainAxisAlignment
    //     0xb3b280: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb3b284: ldr             x0, [x0, #0x730]
    // 0xb3b288: StoreField: r1->field_13 = r0
    //     0xb3b288: stur            w0, [x1, #0x13]
    // 0xb3b28c: r0 = Instance_MainAxisSize
    //     0xb3b28c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb3b290: ldr             x0, [x0, #0x738]
    // 0xb3b294: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3b294: stur            w0, [x1, #0x17]
    // 0xb3b298: r0 = Instance_CrossAxisAlignment
    //     0xb3b298: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xb3b29c: ldr             x0, [x0, #0xf50]
    // 0xb3b2a0: StoreField: r1->field_1b = r0
    //     0xb3b2a0: stur            w0, [x1, #0x1b]
    // 0xb3b2a4: r0 = Instance_VerticalDirection
    //     0xb3b2a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb3b2a8: ldr             x0, [x0, #0x748]
    // 0xb3b2ac: StoreField: r1->field_23 = r0
    //     0xb3b2ac: stur            w0, [x1, #0x23]
    // 0xb3b2b0: r0 = Instance_Clip
    //     0xb3b2b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb3b2b4: ldr             x0, [x0, #0x750]
    // 0xb3b2b8: StoreField: r1->field_2b = r0
    //     0xb3b2b8: stur            w0, [x1, #0x2b]
    // 0xb3b2bc: StoreField: r1->field_2f = rZR
    //     0xb3b2bc: stur            xzr, [x1, #0x2f]
    // 0xb3b2c0: ldur            x0, [fp, #-0x30]
    // 0xb3b2c4: StoreField: r1->field_b = r0
    //     0xb3b2c4: stur            w0, [x1, #0xb]
    // 0xb3b2c8: r0 = TimelineCard()
    //     0xb3b2c8: bl              #0xb3b300  ; AllocateTimelineCardStub -> TimelineCard (size=0x18)
    // 0xb3b2cc: ldur            x1, [fp, #-0x28]
    // 0xb3b2d0: StoreField: r0->field_b = r1
    //     0xb3b2d0: stur            w1, [x0, #0xb]
    // 0xb3b2d4: ldur            x1, [fp, #-8]
    // 0xb3b2d8: StoreField: r0->field_f = r1
    //     0xb3b2d8: stur            w1, [x0, #0xf]
    // 0xb3b2dc: ldur            x1, [fp, #-0x18]
    // 0xb3b2e0: StoreField: r0->field_13 = r1
    //     0xb3b2e0: stur            w1, [x0, #0x13]
    // 0xb3b2e4: LeaveFrame
    //     0xb3b2e4: mov             SP, fp
    //     0xb3b2e8: ldp             fp, lr, [SP], #0x10
    // 0xb3b2ec: ret
    //     0xb3b2ec: ret             
    // 0xb3b2f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3b2f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3b2f4: b               #0xb3af60
    // 0xb3b2f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b2f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3b2fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3b2fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb92f10, size: 0x2d8
    // 0xb92f10: EnterFrame
    //     0xb92f10: stp             fp, lr, [SP, #-0x10]!
    //     0xb92f14: mov             fp, SP
    // 0xb92f18: AllocStack(0x60)
    //     0xb92f18: sub             SP, SP, #0x60
    // 0xb92f1c: SetupParameters(TimelineCard this /* r1 => r1, fp-0x10 */)
    //     0xb92f1c: stur            x1, [fp, #-0x10]
    // 0xb92f20: CheckStackOverflow
    //     0xb92f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb92f24: cmp             SP, x16
    //     0xb92f28: b.ls            #0xb931d0
    // 0xb92f2c: LoadField: r0 = r1->field_b
    //     0xb92f2c: ldur            w0, [x1, #0xb]
    // 0xb92f30: DecompressPointer r0
    //     0xb92f30: add             x0, x0, HEAP, lsl #32
    // 0xb92f34: stur            x0, [fp, #-8]
    // 0xb92f38: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb92f38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb92f3c: ldr             x0, [x0, #0x2670]
    //     0xb92f40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb92f44: cmp             w0, w16
    //     0xb92f48: b.ne            #0xb92f54
    //     0xb92f4c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb92f50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb92f54: r0 = GetNavigation.theme()
    //     0xb92f54: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb92f58: LoadField: r1 = r0->field_63
    //     0xb92f58: ldur            w1, [x0, #0x63]
    // 0xb92f5c: DecompressPointer r1
    //     0xb92f5c: add             x1, x1, HEAP, lsl #32
    // 0xb92f60: stur            x1, [fp, #-0x18]
    // 0xb92f64: r0 = IndicatorStyle()
    //     0xb92f64: bl              #0xb93200  ; AllocateIndicatorStyleStub -> IndicatorStyle (size=0x34)
    // 0xb92f68: d0 = 9.000000
    //     0xb92f68: fmov            d0, #9.00000000
    // 0xb92f6c: stur            x0, [fp, #-0x20]
    // 0xb92f70: StoreField: r0->field_7 = d0
    //     0xb92f70: stur            d0, [x0, #7]
    // 0xb92f74: StoreField: r0->field_f = d0
    //     0xb92f74: stur            d0, [x0, #0xf]
    // 0xb92f78: r1 = Instance_EdgeInsets
    //     0xb92f78: add             x1, PP, #0x40, lsl #12  ; [pp+0x40308] Obj!EdgeInsets@e135a1
    //     0xb92f7c: ldr             x1, [x1, #0x308]
    // 0xb92f80: StoreField: r0->field_1b = r1
    //     0xb92f80: stur            w1, [x0, #0x1b]
    // 0xb92f84: ldur            x1, [fp, #-0x18]
    // 0xb92f88: StoreField: r0->field_1f = r1
    //     0xb92f88: stur            w1, [x0, #0x1f]
    // 0xb92f8c: d0 = 0.180000
    //     0xb92f8c: add             x17, PP, #0x40, lsl #12  ; [pp+0x40310] IMM: double(0.18) from 0x3fc70a3d70a3d70a
    //     0xb92f90: ldr             d0, [x17, #0x310]
    // 0xb92f94: StoreField: r0->field_27 = d0
    //     0xb92f94: stur            d0, [x0, #0x27]
    // 0xb92f98: r3 = false
    //     0xb92f98: add             x3, NULL, #0x30  ; false
    // 0xb92f9c: StoreField: r0->field_2f = r3
    //     0xb92f9c: stur            w3, [x0, #0x2f]
    // 0xb92fa0: r1 = _ConstMap len:6
    //     0xb92fa0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb92fa4: ldr             x1, [x1, #0xc20]
    // 0xb92fa8: r2 = 2
    //     0xb92fa8: movz            x2, #0x2
    // 0xb92fac: r0 = []()
    //     0xb92fac: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92fb0: stur            x0, [fp, #-0x18]
    // 0xb92fb4: cmp             w0, NULL
    // 0xb92fb8: b.eq            #0xb931d8
    // 0xb92fbc: r1 = _ConstMap len:6
    //     0xb92fbc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb92fc0: ldr             x1, [x1, #0xc20]
    // 0xb92fc4: r2 = 8
    //     0xb92fc4: movz            x2, #0x8
    // 0xb92fc8: r0 = []()
    //     0xb92fc8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb92fcc: cmp             w0, NULL
    // 0xb92fd0: b.eq            #0xb931dc
    // 0xb92fd4: r16 = <Color>
    //     0xb92fd4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb92fd8: ldr             x16, [x16, #0x158]
    // 0xb92fdc: stp             x0, x16, [SP, #8]
    // 0xb92fe0: ldur            x16, [fp, #-0x18]
    // 0xb92fe4: str             x16, [SP]
    // 0xb92fe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb92fe8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb92fec: r0 = mode()
    //     0xb92fec: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb92ff0: stur            x0, [fp, #-0x18]
    // 0xb92ff4: r0 = LineStyle()
    //     0xb92ff4: bl              #0xb931f4  ; AllocateLineStyleStub -> LineStyle (size=0x14)
    // 0xb92ff8: mov             x1, x0
    // 0xb92ffc: ldur            x0, [fp, #-0x18]
    // 0xb93000: stur            x1, [fp, #-0x28]
    // 0xb93004: StoreField: r1->field_7 = r0
    //     0xb93004: stur            w0, [x1, #7]
    // 0xb93008: d0 = 0.750000
    //     0xb93008: fmov            d0, #0.75000000
    // 0xb9300c: StoreField: r1->field_b = d0
    //     0xb9300c: stur            d0, [x1, #0xb]
    // 0xb93010: ldur            x0, [fp, #-0x10]
    // 0xb93014: LoadField: r2 = r0->field_13
    //     0xb93014: ldur            w2, [x0, #0x13]
    // 0xb93018: DecompressPointer r2
    //     0xb93018: add             x2, x2, HEAP, lsl #32
    // 0xb9301c: stur            x2, [fp, #-0x18]
    // 0xb93020: r0 = Radius()
    //     0xb93020: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb93024: d0 = 8.000000
    //     0xb93024: fmov            d0, #8.00000000
    // 0xb93028: stur            x0, [fp, #-0x30]
    // 0xb9302c: StoreField: r0->field_7 = d0
    //     0xb9302c: stur            d0, [x0, #7]
    // 0xb93030: StoreField: r0->field_f = d0
    //     0xb93030: stur            d0, [x0, #0xf]
    // 0xb93034: r0 = BorderRadius()
    //     0xb93034: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb93038: mov             x3, x0
    // 0xb9303c: ldur            x0, [fp, #-0x30]
    // 0xb93040: stur            x3, [fp, #-0x38]
    // 0xb93044: StoreField: r3->field_7 = r0
    //     0xb93044: stur            w0, [x3, #7]
    // 0xb93048: StoreField: r3->field_b = r0
    //     0xb93048: stur            w0, [x3, #0xb]
    // 0xb9304c: StoreField: r3->field_f = r0
    //     0xb9304c: stur            w0, [x3, #0xf]
    // 0xb93050: StoreField: r3->field_13 = r0
    //     0xb93050: stur            w0, [x3, #0x13]
    // 0xb93054: r1 = _ConstMap len:6
    //     0xb93054: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb93058: ldr             x1, [x1, #0xc20]
    // 0xb9305c: r2 = 2
    //     0xb9305c: movz            x2, #0x2
    // 0xb93060: r0 = []()
    //     0xb93060: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb93064: stur            x0, [fp, #-0x30]
    // 0xb93068: cmp             w0, NULL
    // 0xb9306c: b.eq            #0xb931e0
    // 0xb93070: r1 = _ConstMap len:6
    //     0xb93070: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb93074: ldr             x1, [x1, #0xc20]
    // 0xb93078: r2 = 8
    //     0xb93078: movz            x2, #0x8
    // 0xb9307c: r0 = []()
    //     0xb9307c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb93080: cmp             w0, NULL
    // 0xb93084: b.eq            #0xb931e4
    // 0xb93088: r16 = <Color>
    //     0xb93088: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb9308c: ldr             x16, [x16, #0x158]
    // 0xb93090: stp             x0, x16, [SP, #8]
    // 0xb93094: ldur            x16, [fp, #-0x30]
    // 0xb93098: str             x16, [SP]
    // 0xb9309c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9309c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb930a0: r0 = mode()
    //     0xb930a0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb930a4: stur            x0, [fp, #-0x30]
    // 0xb930a8: r0 = BorderSide()
    //     0xb930a8: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb930ac: mov             x1, x0
    // 0xb930b0: ldur            x0, [fp, #-0x30]
    // 0xb930b4: stur            x1, [fp, #-0x40]
    // 0xb930b8: StoreField: r1->field_7 = r0
    //     0xb930b8: stur            w0, [x1, #7]
    // 0xb930bc: d0 = 1.000000
    //     0xb930bc: fmov            d0, #1.00000000
    // 0xb930c0: StoreField: r1->field_b = d0
    //     0xb930c0: stur            d0, [x1, #0xb]
    // 0xb930c4: r0 = Instance_BorderStyle
    //     0xb930c4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xb930c8: ldr             x0, [x0, #0x2d0]
    // 0xb930cc: StoreField: r1->field_13 = r0
    //     0xb930cc: stur            w0, [x1, #0x13]
    // 0xb930d0: d0 = -1.000000
    //     0xb930d0: fmov            d0, #-1.00000000
    // 0xb930d4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb930d4: stur            d0, [x1, #0x17]
    // 0xb930d8: r0 = RoundedRectangleBorder()
    //     0xb930d8: bl              #0x87deec  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb930dc: mov             x1, x0
    // 0xb930e0: ldur            x0, [fp, #-0x38]
    // 0xb930e4: stur            x1, [fp, #-0x48]
    // 0xb930e8: StoreField: r1->field_b = r0
    //     0xb930e8: stur            w0, [x1, #0xb]
    // 0xb930ec: ldur            x0, [fp, #-0x40]
    // 0xb930f0: StoreField: r1->field_7 = r0
    //     0xb930f0: stur            w0, [x1, #7]
    // 0xb930f4: ldur            x0, [fp, #-0x10]
    // 0xb930f8: LoadField: r2 = r0->field_f
    //     0xb930f8: ldur            w2, [x0, #0xf]
    // 0xb930fc: DecompressPointer r2
    //     0xb930fc: add             x2, x2, HEAP, lsl #32
    // 0xb93100: stur            x2, [fp, #-0x30]
    // 0xb93104: r0 = Padding()
    //     0xb93104: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb93108: mov             x1, x0
    // 0xb9310c: r0 = Instance_EdgeInsets
    //     0xb9310c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb93110: ldr             x0, [x0, #0x768]
    // 0xb93114: stur            x1, [fp, #-0x10]
    // 0xb93118: StoreField: r1->field_f = r0
    //     0xb93118: stur            w0, [x1, #0xf]
    // 0xb9311c: ldur            x0, [fp, #-0x30]
    // 0xb93120: StoreField: r1->field_b = r0
    //     0xb93120: stur            w0, [x1, #0xb]
    // 0xb93124: r0 = Card()
    //     0xb93124: bl              #0xad7cd4  ; AllocateCardStub -> Card (size=0x38)
    // 0xb93128: mov             x1, x0
    // 0xb9312c: ldur            x0, [fp, #-0x18]
    // 0xb93130: stur            x1, [fp, #-0x30]
    // 0xb93134: StoreField: r1->field_b = r0
    //     0xb93134: stur            w0, [x1, #0xb]
    // 0xb93138: r0 = 0.000000
    //     0xb93138: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb9313c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9313c: stur            w0, [x1, #0x17]
    // 0xb93140: ldur            x0, [fp, #-0x48]
    // 0xb93144: StoreField: r1->field_1b = r0
    //     0xb93144: stur            w0, [x1, #0x1b]
    // 0xb93148: r0 = true
    //     0xb93148: add             x0, NULL, #0x20  ; true
    // 0xb9314c: StoreField: r1->field_1f = r0
    //     0xb9314c: stur            w0, [x1, #0x1f]
    // 0xb93150: r2 = Instance_EdgeInsets
    //     0xb93150: add             x2, PP, #0x40, lsl #12  ; [pp+0x40318] Obj!EdgeInsets@e13571
    //     0xb93154: ldr             x2, [x2, #0x318]
    // 0xb93158: StoreField: r1->field_27 = r2
    //     0xb93158: stur            w2, [x1, #0x27]
    // 0xb9315c: ldur            x2, [fp, #-0x10]
    // 0xb93160: StoreField: r1->field_2f = r2
    //     0xb93160: stur            w2, [x1, #0x2f]
    // 0xb93164: StoreField: r1->field_2b = r0
    //     0xb93164: stur            w0, [x1, #0x2b]
    // 0xb93168: r2 = Instance__CardVariant
    //     0xb93168: add             x2, PP, #0x25, lsl #12  ; [pp+0x25778] Obj!_CardVariant@e36a41
    //     0xb9316c: ldr             x2, [x2, #0x778]
    // 0xb93170: StoreField: r1->field_33 = r2
    //     0xb93170: stur            w2, [x1, #0x33]
    // 0xb93174: r0 = TimelineTile()
    //     0xb93174: bl              #0xb931e8  ; AllocateTimelineTileStub -> TimelineTile (size=0x38)
    // 0xb93178: r1 = Instance_TimelineAxis
    //     0xb93178: add             x1, PP, #0x40, lsl #12  ; [pp+0x40320] Obj!TimelineAxis@e2df21
    //     0xb9317c: ldr             x1, [x1, #0x320]
    // 0xb93180: StoreField: r0->field_b = r1
    //     0xb93180: stur            w1, [x0, #0xb]
    // 0xb93184: r1 = Instance_TimelineAlign
    //     0xb93184: add             x1, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!TimelineAlign@e2df01
    //     0xb93188: ldr             x1, [x1, #0x328]
    // 0xb9318c: StoreField: r0->field_f = r1
    //     0xb9318c: stur            w1, [x0, #0xf]
    // 0xb93190: ldur            x1, [fp, #-0x30]
    // 0xb93194: ArrayStore: r0[0] = r1  ; List_4
    //     0xb93194: stur            w1, [x0, #0x17]
    // 0xb93198: r1 = true
    //     0xb93198: add             x1, NULL, #0x20  ; true
    // 0xb9319c: StoreField: r0->field_1f = r1
    //     0xb9319c: stur            w1, [x0, #0x1f]
    // 0xb931a0: r1 = false
    //     0xb931a0: add             x1, NULL, #0x30  ; false
    // 0xb931a4: StoreField: r0->field_23 = r1
    //     0xb931a4: stur            w1, [x0, #0x23]
    // 0xb931a8: ldur            x1, [fp, #-8]
    // 0xb931ac: StoreField: r0->field_27 = r1
    //     0xb931ac: stur            w1, [x0, #0x27]
    // 0xb931b0: ldur            x1, [fp, #-0x20]
    // 0xb931b4: StoreField: r0->field_2b = r1
    //     0xb931b4: stur            w1, [x0, #0x2b]
    // 0xb931b8: ldur            x1, [fp, #-0x28]
    // 0xb931bc: StoreField: r0->field_2f = r1
    //     0xb931bc: stur            w1, [x0, #0x2f]
    // 0xb931c0: StoreField: r0->field_33 = r1
    //     0xb931c0: stur            w1, [x0, #0x33]
    // 0xb931c4: LeaveFrame
    //     0xb931c4: mov             SP, fp
    //     0xb931c8: ldp             fp, lr, [SP], #0x10
    // 0xb931cc: ret
    //     0xb931cc: ret             
    // 0xb931d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb931d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb931d4: b               #0xb92f2c
    // 0xb931d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb931d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb931dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb931dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb931e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb931e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb931e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb931e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  factory _ TimelineCard.loading(/* No info */) {
    // ** addr: 0xb932fc, size: 0x22c
    // 0xb932fc: EnterFrame
    //     0xb932fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb93300: mov             fp, SP
    // 0xb93304: AllocStack(0x10)
    //     0xb93304: sub             SP, SP, #0x10
    // 0xb93308: CheckStackOverflow
    //     0xb93308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9330c: cmp             SP, x16
    //     0xb93310: b.ls            #0xb93520
    // 0xb93314: d0 = 200.000000
    //     0xb93314: add             x17, PP, #0x40, lsl #12  ; [pp+0x402e8] IMM: double(200) from 0x4069000000000000
    //     0xb93318: ldr             d0, [x17, #0x2e8]
    // 0xb9331c: r0 = fixed()
    //     0xb9331c: bl              #0xb93528  ; [package:nuikit/src/widgets/skeleton/skeleton_item.dart] NSkeletonItem::fixed
    // 0xb93320: r1 = <Widget>
    //     0xb93320: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb93324: r2 = 22
    //     0xb93324: movz            x2, #0x16
    // 0xb93328: stur            x0, [fp, #-8]
    // 0xb9332c: r0 = AllocateArray()
    //     0xb9332c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb93330: mov             x1, x0
    // 0xb93334: ldur            x0, [fp, #-8]
    // 0xb93338: stur            x1, [fp, #-0x10]
    // 0xb9333c: StoreField: r1->field_f = r0
    //     0xb9333c: stur            w0, [x1, #0xf]
    // 0xb93340: r16 = Instance_SizedBox
    //     0xb93340: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb93344: ldr             x16, [x16, #0xfb0]
    // 0xb93348: StoreField: r1->field_13 = r16
    //     0xb93348: stur            w16, [x1, #0x13]
    // 0xb9334c: d0 = 60.000000
    //     0xb9334c: ldr             d0, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0xb93350: r0 = fixed()
    //     0xb93350: bl              #0xb93528  ; [package:nuikit/src/widgets/skeleton/skeleton_item.dart] NSkeletonItem::fixed
    // 0xb93354: ldur            x1, [fp, #-0x10]
    // 0xb93358: ArrayStore: r1[2] = r0  ; List_4
    //     0xb93358: add             x25, x1, #0x17
    //     0xb9335c: str             w0, [x25]
    //     0xb93360: tbz             w0, #0, #0xb9337c
    //     0xb93364: ldurb           w16, [x1, #-1]
    //     0xb93368: ldurb           w17, [x0, #-1]
    //     0xb9336c: and             x16, x17, x16, lsr #2
    //     0xb93370: tst             x16, HEAP, lsr #32
    //     0xb93374: b.eq            #0xb9337c
    //     0xb93378: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb9337c: ldur            x1, [fp, #-0x10]
    // 0xb93380: r16 = Instance_SizedBox
    //     0xb93380: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb93384: ldr             x16, [x16, #0x950]
    // 0xb93388: StoreField: r1->field_1b = r16
    //     0xb93388: stur            w16, [x1, #0x1b]
    // 0xb9338c: r16 = Instance_Divider
    //     0xb9338c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb93390: ldr             x16, [x16, #0xc28]
    // 0xb93394: StoreField: r1->field_1f = r16
    //     0xb93394: stur            w16, [x1, #0x1f]
    // 0xb93398: r16 = Instance_SizedBox
    //     0xb93398: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb9339c: ldr             x16, [x16, #0x950]
    // 0xb933a0: StoreField: r1->field_23 = r16
    //     0xb933a0: stur            w16, [x1, #0x23]
    // 0xb933a4: d0 = 225.000000
    //     0xb933a4: add             x17, PP, #0x40, lsl #12  ; [pp+0x402f0] IMM: double(225) from 0x406c200000000000
    //     0xb933a8: ldr             d0, [x17, #0x2f0]
    // 0xb933ac: r0 = fixed()
    //     0xb933ac: bl              #0xb93528  ; [package:nuikit/src/widgets/skeleton/skeleton_item.dart] NSkeletonItem::fixed
    // 0xb933b0: ldur            x1, [fp, #-0x10]
    // 0xb933b4: ArrayStore: r1[6] = r0  ; List_4
    //     0xb933b4: add             x25, x1, #0x27
    //     0xb933b8: str             w0, [x25]
    //     0xb933bc: tbz             w0, #0, #0xb933d8
    //     0xb933c0: ldurb           w16, [x1, #-1]
    //     0xb933c4: ldurb           w17, [x0, #-1]
    //     0xb933c8: and             x16, x17, x16, lsr #2
    //     0xb933cc: tst             x16, HEAP, lsr #32
    //     0xb933d0: b.eq            #0xb933d8
    //     0xb933d4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb933d8: ldur            x1, [fp, #-0x10]
    // 0xb933dc: r16 = Instance_SizedBox
    //     0xb933dc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb933e0: ldr             x16, [x16, #0xfe8]
    // 0xb933e4: StoreField: r1->field_2b = r16
    //     0xb933e4: stur            w16, [x1, #0x2b]
    // 0xb933e8: d0 = 265.000000
    //     0xb933e8: add             x17, PP, #0x40, lsl #12  ; [pp+0x402f8] IMM: double(265) from 0x4070900000000000
    //     0xb933ec: ldr             d0, [x17, #0x2f8]
    // 0xb933f0: r0 = fixed()
    //     0xb933f0: bl              #0xb93528  ; [package:nuikit/src/widgets/skeleton/skeleton_item.dart] NSkeletonItem::fixed
    // 0xb933f4: ldur            x1, [fp, #-0x10]
    // 0xb933f8: ArrayStore: r1[8] = r0  ; List_4
    //     0xb933f8: add             x25, x1, #0x2f
    //     0xb933fc: str             w0, [x25]
    //     0xb93400: tbz             w0, #0, #0xb9341c
    //     0xb93404: ldurb           w16, [x1, #-1]
    //     0xb93408: ldurb           w17, [x0, #-1]
    //     0xb9340c: and             x16, x17, x16, lsr #2
    //     0xb93410: tst             x16, HEAP, lsr #32
    //     0xb93414: b.eq            #0xb9341c
    //     0xb93418: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb9341c: ldur            x1, [fp, #-0x10]
    // 0xb93420: r16 = Instance_SizedBox
    //     0xb93420: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb93424: ldr             x16, [x16, #0xfe8]
    // 0xb93428: StoreField: r1->field_33 = r16
    //     0xb93428: stur            w16, [x1, #0x33]
    // 0xb9342c: d0 = 65.000000
    //     0xb9342c: ldr             d0, [PP, #0x6960]  ; [pp+0x6960] IMM: double(65) from 0x4050400000000000
    // 0xb93430: r0 = fixed()
    //     0xb93430: bl              #0xb93528  ; [package:nuikit/src/widgets/skeleton/skeleton_item.dart] NSkeletonItem::fixed
    // 0xb93434: ldur            x1, [fp, #-0x10]
    // 0xb93438: ArrayStore: r1[10] = r0  ; List_4
    //     0xb93438: add             x25, x1, #0x37
    //     0xb9343c: str             w0, [x25]
    //     0xb93440: tbz             w0, #0, #0xb9345c
    //     0xb93444: ldurb           w16, [x1, #-1]
    //     0xb93448: ldurb           w17, [x0, #-1]
    //     0xb9344c: and             x16, x17, x16, lsr #2
    //     0xb93450: tst             x16, HEAP, lsr #32
    //     0xb93454: b.eq            #0xb9345c
    //     0xb93458: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb9345c: r1 = <Widget>
    //     0xb9345c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb93460: r0 = AllocateGrowableArray()
    //     0xb93460: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb93464: mov             x1, x0
    // 0xb93468: ldur            x0, [fp, #-0x10]
    // 0xb9346c: stur            x1, [fp, #-8]
    // 0xb93470: StoreField: r1->field_f = r0
    //     0xb93470: stur            w0, [x1, #0xf]
    // 0xb93474: r0 = 22
    //     0xb93474: movz            x0, #0x16
    // 0xb93478: StoreField: r1->field_b = r0
    //     0xb93478: stur            w0, [x1, #0xb]
    // 0xb9347c: r0 = Column()
    //     0xb9347c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb93480: mov             x1, x0
    // 0xb93484: r0 = Instance_Axis
    //     0xb93484: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb93488: stur            x1, [fp, #-0x10]
    // 0xb9348c: StoreField: r1->field_f = r0
    //     0xb9348c: stur            w0, [x1, #0xf]
    // 0xb93490: r0 = Instance_MainAxisAlignment
    //     0xb93490: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb93494: ldr             x0, [x0, #0x730]
    // 0xb93498: StoreField: r1->field_13 = r0
    //     0xb93498: stur            w0, [x1, #0x13]
    // 0xb9349c: r0 = Instance_MainAxisSize
    //     0xb9349c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb934a0: ldr             x0, [x0, #0x738]
    // 0xb934a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb934a4: stur            w0, [x1, #0x17]
    // 0xb934a8: r0 = Instance_CrossAxisAlignment
    //     0xb934a8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb934ac: ldr             x0, [x0, #0x68]
    // 0xb934b0: StoreField: r1->field_1b = r0
    //     0xb934b0: stur            w0, [x1, #0x1b]
    // 0xb934b4: r0 = Instance_VerticalDirection
    //     0xb934b4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb934b8: ldr             x0, [x0, #0x748]
    // 0xb934bc: StoreField: r1->field_23 = r0
    //     0xb934bc: stur            w0, [x1, #0x23]
    // 0xb934c0: r0 = Instance_Clip
    //     0xb934c0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb934c4: ldr             x0, [x0, #0x750]
    // 0xb934c8: StoreField: r1->field_2b = r0
    //     0xb934c8: stur            w0, [x1, #0x2b]
    // 0xb934cc: StoreField: r1->field_2f = rZR
    //     0xb934cc: stur            xzr, [x1, #0x2f]
    // 0xb934d0: ldur            x0, [fp, #-8]
    // 0xb934d4: StoreField: r1->field_b = r0
    //     0xb934d4: stur            w0, [x1, #0xb]
    // 0xb934d8: r0 = NSkeleton()
    //     0xb934d8: bl              #0xb3d8c4  ; AllocateNSkeletonStub -> NSkeleton (size=0x10)
    // 0xb934dc: mov             x1, x0
    // 0xb934e0: ldur            x0, [fp, #-0x10]
    // 0xb934e4: stur            x1, [fp, #-8]
    // 0xb934e8: StoreField: r1->field_b = r0
    //     0xb934e8: stur            w0, [x1, #0xb]
    // 0xb934ec: r0 = SizedBox()
    //     0xb934ec: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb934f0: mov             x1, x0
    // 0xb934f4: ldur            x0, [fp, #-8]
    // 0xb934f8: stur            x1, [fp, #-0x10]
    // 0xb934fc: StoreField: r1->field_b = r0
    //     0xb934fc: stur            w0, [x1, #0xb]
    // 0xb93500: r0 = TimelineCard()
    //     0xb93500: bl              #0xb3b300  ; AllocateTimelineCardStub -> TimelineCard (size=0x18)
    // 0xb93504: r1 = false
    //     0xb93504: add             x1, NULL, #0x30  ; false
    // 0xb93508: StoreField: r0->field_b = r1
    //     0xb93508: stur            w1, [x0, #0xb]
    // 0xb9350c: ldur            x1, [fp, #-0x10]
    // 0xb93510: StoreField: r0->field_f = r1
    //     0xb93510: stur            w1, [x0, #0xf]
    // 0xb93514: LeaveFrame
    //     0xb93514: mov             SP, fp
    //     0xb93518: ldp             fp, lr, [SP], #0x10
    // 0xb9351c: ret
    //     0xb9351c: ret             
    // 0xb93520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb93520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb93524: b               #0xb93314
  }
}
