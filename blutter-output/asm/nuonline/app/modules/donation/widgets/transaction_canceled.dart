// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_canceled.dart

// class id: 1050246, size: 0x8
class :: {
}

// class id: 5023, size: 0x10, field offset: 0xc
//   const constructor, 
class TransactionCanceled extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb939ec, size: 0x1c8
    // 0xb939ec: EnterFrame
    //     0xb939ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb939f0: mov             fp, SP
    // 0xb939f4: AllocStack(0x40)
    //     0xb939f4: sub             SP, SP, #0x40
    // 0xb939f8: SetupParameters(TransactionCanceled this /* r1 => r0, fp-0x8 */)
    //     0xb939f8: mov             x0, x1
    //     0xb939fc: stur            x1, [fp, #-8]
    // 0xb93a00: CheckStackOverflow
    //     0xb93a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb93a04: cmp             SP, x16
    //     0xb93a08: b.ls            #0xb93ba8
    // 0xb93a0c: r1 = _ConstMap len:3
    //     0xb93a0c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb93a10: ldr             x1, [x1, #0xbe8]
    // 0xb93a14: r2 = 2
    //     0xb93a14: movz            x2, #0x2
    // 0xb93a18: r0 = []()
    //     0xb93a18: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb93a1c: r16 = <Color?>
    //     0xb93a1c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb93a20: ldr             x16, [x16, #0x98]
    // 0xb93a24: stp             NULL, x16, [SP, #8]
    // 0xb93a28: str             x0, [SP]
    // 0xb93a2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb93a2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb93a30: r0 = mode()
    //     0xb93a30: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb93a34: stur            x0, [fp, #-0x10]
    // 0xb93a38: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb93a38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb93a3c: ldr             x0, [x0, #0x2670]
    //     0xb93a40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb93a44: cmp             w0, w16
    //     0xb93a48: b.ne            #0xb93a54
    //     0xb93a4c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb93a50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb93a54: r0 = GetNavigation.textTheme()
    //     0xb93a54: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb93a58: LoadField: r1 = r0->field_f
    //     0xb93a58: ldur            w1, [x0, #0xf]
    // 0xb93a5c: DecompressPointer r1
    //     0xb93a5c: add             x1, x1, HEAP, lsl #32
    // 0xb93a60: cmp             w1, NULL
    // 0xb93a64: b.eq            #0xb93bb0
    // 0xb93a68: r16 = 24.000000
    //     0xb93a68: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xb93a6c: ldr             x16, [x16, #0x368]
    // 0xb93a70: r30 = Instance_Color
    //     0xb93a70: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb93a74: stp             lr, x16, [SP]
    // 0xb93a78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb93a78: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb93a7c: ldr             x4, [x4, #0xaa0]
    // 0xb93a80: r0 = copyWith()
    //     0xb93a80: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb93a84: stur            x0, [fp, #-0x18]
    // 0xb93a88: r0 = Text()
    //     0xb93a88: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb93a8c: mov             x1, x0
    // 0xb93a90: r0 = "Pembayaran Dibatalkan"
    //     0xb93a90: add             x0, PP, #0x35, lsl #12  ; [pp+0x35428] "Pembayaran Dibatalkan"
    //     0xb93a94: ldr             x0, [x0, #0x428]
    // 0xb93a98: stur            x1, [fp, #-0x20]
    // 0xb93a9c: StoreField: r1->field_b = r0
    //     0xb93a9c: stur            w0, [x1, #0xb]
    // 0xb93aa0: ldur            x0, [fp, #-0x18]
    // 0xb93aa4: StoreField: r1->field_13 = r0
    //     0xb93aa4: stur            w0, [x1, #0x13]
    // 0xb93aa8: r0 = Container()
    //     0xb93aa8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb93aac: stur            x0, [fp, #-0x18]
    // 0xb93ab0: r16 = 70.000000
    //     0xb93ab0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35430] 70
    //     0xb93ab4: ldr             x16, [x16, #0x430]
    // 0xb93ab8: r30 = Instance_EdgeInsets
    //     0xb93ab8: ldr             lr, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb93abc: stp             lr, x16, [SP, #0x10]
    // 0xb93ac0: r16 = Instance_Alignment
    //     0xb93ac0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0xb93ac4: ldr             x16, [x16, #0x370]
    // 0xb93ac8: ldur            lr, [fp, #-0x20]
    // 0xb93acc: stp             lr, x16, [SP]
    // 0xb93ad0: mov             x1, x0
    // 0xb93ad4: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x3, child, 0x4, height, 0x1, padding, 0x2, null]
    //     0xb93ad4: add             x4, PP, #0x35, lsl #12  ; [pp+0x35438] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x3, "child", 0x4, "height", 0x1, "padding", 0x2, Null]
    //     0xb93ad8: ldr             x4, [x4, #0x438]
    // 0xb93adc: r0 = Container()
    //     0xb93adc: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb93ae0: r0 = PreferredSize()
    //     0xb93ae0: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xb93ae4: mov             x1, x0
    // 0xb93ae8: r0 = Instance_Size
    //     0xb93ae8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35440] Obj!Size@e2c241
    //     0xb93aec: ldr             x0, [x0, #0x440]
    // 0xb93af0: stur            x1, [fp, #-0x20]
    // 0xb93af4: StoreField: r1->field_f = r0
    //     0xb93af4: stur            w0, [x1, #0xf]
    // 0xb93af8: ldur            x0, [fp, #-0x18]
    // 0xb93afc: StoreField: r1->field_b = r0
    //     0xb93afc: stur            w0, [x1, #0xb]
    // 0xb93b00: r0 = AppBar()
    //     0xb93b00: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb93b04: stur            x0, [fp, #-0x18]
    // 0xb93b08: r16 = Instance_Text
    //     0xb93b08: add             x16, PP, #0x30, lsl #12  ; [pp+0x30288] Obj!Text@e21641
    //     0xb93b0c: ldr             x16, [x16, #0x288]
    // 0xb93b10: r30 = 0.000000
    //     0xb93b10: ldr             lr, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb93b14: stp             lr, x16, [SP, #0x10]
    // 0xb93b18: ldur            x16, [fp, #-0x10]
    // 0xb93b1c: ldur            lr, [fp, #-0x20]
    // 0xb93b20: stp             lr, x16, [SP]
    // 0xb93b24: mov             x1, x0
    // 0xb93b28: r4 = const [0, 0x5, 0x4, 0x1, backgroundColor, 0x3, bottom, 0x4, elevation, 0x2, title, 0x1, null]
    //     0xb93b28: add             x4, PP, #0x35, lsl #12  ; [pp+0x35448] List(13) [0, 0x5, 0x4, 0x1, "backgroundColor", 0x3, "bottom", 0x4, "elevation", 0x2, "title", 0x1, Null]
    //     0xb93b2c: ldr             x4, [x4, #0x448]
    // 0xb93b30: r0 = AppBar()
    //     0xb93b30: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb93b34: ldur            x0, [fp, #-8]
    // 0xb93b38: LoadField: r1 = r0->field_b
    //     0xb93b38: ldur            w1, [x0, #0xb]
    // 0xb93b3c: DecompressPointer r1
    //     0xb93b3c: add             x1, x1, HEAP, lsl #32
    // 0xb93b40: stur            x1, [fp, #-0x10]
    // 0xb93b44: r0 = TransactionDetail()
    //     0xb93b44: bl              #0xa3b67c  ; AllocateTransactionDetailStub -> TransactionDetail (size=0x10)
    // 0xb93b48: mov             x1, x0
    // 0xb93b4c: ldur            x0, [fp, #-0x10]
    // 0xb93b50: stur            x1, [fp, #-8]
    // 0xb93b54: StoreField: r1->field_b = r0
    //     0xb93b54: stur            w0, [x1, #0xb]
    // 0xb93b58: r0 = Scaffold()
    //     0xb93b58: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb93b5c: ldur            x1, [fp, #-0x18]
    // 0xb93b60: StoreField: r0->field_13 = r1
    //     0xb93b60: stur            w1, [x0, #0x13]
    // 0xb93b64: ldur            x1, [fp, #-8]
    // 0xb93b68: ArrayStore: r0[0] = r1  ; List_4
    //     0xb93b68: stur            w1, [x0, #0x17]
    // 0xb93b6c: r1 = Instance_AlignmentDirectional
    //     0xb93b6c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb93b70: ldr             x1, [x1, #0x758]
    // 0xb93b74: StoreField: r0->field_2b = r1
    //     0xb93b74: stur            w1, [x0, #0x2b]
    // 0xb93b78: r1 = true
    //     0xb93b78: add             x1, NULL, #0x20  ; true
    // 0xb93b7c: StoreField: r0->field_53 = r1
    //     0xb93b7c: stur            w1, [x0, #0x53]
    // 0xb93b80: r2 = Instance_DragStartBehavior
    //     0xb93b80: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb93b84: StoreField: r0->field_57 = r2
    //     0xb93b84: stur            w2, [x0, #0x57]
    // 0xb93b88: r2 = false
    //     0xb93b88: add             x2, NULL, #0x30  ; false
    // 0xb93b8c: StoreField: r0->field_b = r2
    //     0xb93b8c: stur            w2, [x0, #0xb]
    // 0xb93b90: StoreField: r0->field_f = r2
    //     0xb93b90: stur            w2, [x0, #0xf]
    // 0xb93b94: StoreField: r0->field_5f = r1
    //     0xb93b94: stur            w1, [x0, #0x5f]
    // 0xb93b98: StoreField: r0->field_63 = r1
    //     0xb93b98: stur            w1, [x0, #0x63]
    // 0xb93b9c: LeaveFrame
    //     0xb93b9c: mov             SP, fp
    //     0xb93ba0: ldp             fp, lr, [SP], #0x10
    // 0xb93ba4: ret
    //     0xb93ba4: ret             
    // 0xb93ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb93ba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb93bac: b               #0xb93a0c
    // 0xb93bb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb93bb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
