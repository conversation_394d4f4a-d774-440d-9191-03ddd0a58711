// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_detail.dart

// class id: 1050247, size: 0x8
class :: {
}

// class id: 5022, size: 0x10, field offset: 0xc
//   const constructor, 
class TransactionDetail extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb93bb4, size: 0x764
    // 0xb93bb4: EnterFrame
    //     0xb93bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xb93bb8: mov             fp, SP
    // 0xb93bbc: AllocStack(0x60)
    //     0xb93bbc: sub             SP, SP, #0x60
    // 0xb93bc0: SetupParameters(TransactionDetail this /* r1 => r1, fp-0x8 */)
    //     0xb93bc0: stur            x1, [fp, #-8]
    // 0xb93bc4: CheckStackOverflow
    //     0xb93bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb93bc8: cmp             SP, x16
    //     0xb93bcc: b.ls            #0xb9430c
    // 0xb93bd0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb93bd0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb93bd4: ldr             x0, [x0, #0x2670]
    //     0xb93bd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb93bdc: cmp             w0, w16
    //     0xb93be0: b.ne            #0xb93bec
    //     0xb93be4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb93be8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb93bec: r0 = GetNavigation.textTheme()
    //     0xb93bec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb93bf0: LoadField: r1 = r0->field_f
    //     0xb93bf0: ldur            w1, [x0, #0xf]
    // 0xb93bf4: DecompressPointer r1
    //     0xb93bf4: add             x1, x1, HEAP, lsl #32
    // 0xb93bf8: cmp             w1, NULL
    // 0xb93bfc: b.eq            #0xb94314
    // 0xb93c00: r16 = 16.000000
    //     0xb93c00: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb93c04: ldr             x16, [x16, #0x80]
    // 0xb93c08: str             x16, [SP]
    // 0xb93c0c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb93c0c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb93c10: ldr             x4, [x4, #0x88]
    // 0xb93c14: r0 = copyWith()
    //     0xb93c14: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb93c18: mov             x1, x0
    // 0xb93c1c: ldur            x0, [fp, #-8]
    // 0xb93c20: stur            x1, [fp, #-0x18]
    // 0xb93c24: LoadField: r2 = r0->field_b
    //     0xb93c24: ldur            w2, [x0, #0xb]
    // 0xb93c28: DecompressPointer r2
    //     0xb93c28: add             x2, x2, HEAP, lsl #32
    // 0xb93c2c: stur            x2, [fp, #-0x10]
    // 0xb93c30: LoadField: r0 = r2->field_f
    //     0xb93c30: ldur            w0, [x2, #0xf]
    // 0xb93c34: DecompressPointer r0
    //     0xb93c34: add             x0, x0, HEAP, lsl #32
    // 0xb93c38: stur            x0, [fp, #-8]
    // 0xb93c3c: r0 = UserInfo()
    //     0xb93c3c: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93c40: mov             x3, x0
    // 0xb93c44: r0 = "ID Transaksi"
    //     0xb93c44: add             x0, PP, #0x40, lsl #12  ; [pp+0x40298] "ID Transaksi"
    //     0xb93c48: ldr             x0, [x0, #0x298]
    // 0xb93c4c: stur            x3, [fp, #-0x20]
    // 0xb93c50: StoreField: r3->field_b = r0
    //     0xb93c50: stur            w0, [x3, #0xb]
    // 0xb93c54: ldur            x0, [fp, #-8]
    // 0xb93c58: StoreField: r3->field_f = r0
    //     0xb93c58: stur            w0, [x3, #0xf]
    // 0xb93c5c: r0 = false
    //     0xb93c5c: add             x0, NULL, #0x30  ; false
    // 0xb93c60: ArrayStore: r3[0] = r0  ; List_4
    //     0xb93c60: stur            w0, [x3, #0x17]
    // 0xb93c64: r1 = Null
    //     0xb93c64: mov             x1, NULL
    // 0xb93c68: r2 = 4
    //     0xb93c68: movz            x2, #0x4
    // 0xb93c6c: r0 = AllocateArray()
    //     0xb93c6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb93c70: stur            x0, [fp, #-8]
    // 0xb93c74: r16 = Instance_SizedBox
    //     0xb93c74: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb93c78: ldr             x16, [x16, #0xfb0]
    // 0xb93c7c: StoreField: r0->field_f = r16
    //     0xb93c7c: stur            w16, [x0, #0xf]
    // 0xb93c80: ldur            x1, [fp, #-0x20]
    // 0xb93c84: StoreField: r0->field_13 = r1
    //     0xb93c84: stur            w1, [x0, #0x13]
    // 0xb93c88: r1 = <Widget>
    //     0xb93c88: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb93c8c: r0 = AllocateGrowableArray()
    //     0xb93c8c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb93c90: mov             x1, x0
    // 0xb93c94: ldur            x0, [fp, #-8]
    // 0xb93c98: stur            x1, [fp, #-0x28]
    // 0xb93c9c: StoreField: r1->field_f = r0
    //     0xb93c9c: stur            w0, [x1, #0xf]
    // 0xb93ca0: r2 = 4
    //     0xb93ca0: movz            x2, #0x4
    // 0xb93ca4: StoreField: r1->field_b = r2
    //     0xb93ca4: stur            w2, [x1, #0xb]
    // 0xb93ca8: ldur            x0, [fp, #-0x10]
    // 0xb93cac: LoadField: r3 = r0->field_3f
    //     0xb93cac: ldur            w3, [x0, #0x3f]
    // 0xb93cb0: DecompressPointer r3
    //     0xb93cb0: add             x3, x3, HEAP, lsl #32
    // 0xb93cb4: stur            x3, [fp, #-0x20]
    // 0xb93cb8: r16 = Instance_PaymentMethod
    //     0xb93cb8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xb93cbc: ldr             x16, [x16, #0x220]
    // 0xb93cc0: cmp             w3, w16
    // 0xb93cc4: b.ne            #0xb93d44
    // 0xb93cc8: LoadField: r4 = r0->field_13
    //     0xb93cc8: ldur            w4, [x0, #0x13]
    // 0xb93ccc: DecompressPointer r4
    //     0xb93ccc: add             x4, x4, HEAP, lsl #32
    // 0xb93cd0: stur            x4, [fp, #-8]
    // 0xb93cd4: r0 = UserInfo()
    //     0xb93cd4: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93cd8: mov             x2, x0
    // 0xb93cdc: r0 = "Bill Number"
    //     0xb93cdc: add             x0, PP, #0x40, lsl #12  ; [pp+0x402a0] "Bill Number"
    //     0xb93ce0: ldr             x0, [x0, #0x2a0]
    // 0xb93ce4: stur            x2, [fp, #-0x30]
    // 0xb93ce8: StoreField: r2->field_b = r0
    //     0xb93ce8: stur            w0, [x2, #0xb]
    // 0xb93cec: ldur            x0, [fp, #-8]
    // 0xb93cf0: StoreField: r2->field_f = r0
    //     0xb93cf0: stur            w0, [x2, #0xf]
    // 0xb93cf4: r0 = false
    //     0xb93cf4: add             x0, NULL, #0x30  ; false
    // 0xb93cf8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93cf8: stur            w0, [x2, #0x17]
    // 0xb93cfc: ldur            x1, [fp, #-0x28]
    // 0xb93d00: r0 = _growToNextCapacity()
    //     0xb93d00: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93d04: ldur            x2, [fp, #-0x28]
    // 0xb93d08: r3 = 6
    //     0xb93d08: movz            x3, #0x6
    // 0xb93d0c: StoreField: r2->field_b = r3
    //     0xb93d0c: stur            w3, [x2, #0xb]
    // 0xb93d10: LoadField: r1 = r2->field_f
    //     0xb93d10: ldur            w1, [x2, #0xf]
    // 0xb93d14: DecompressPointer r1
    //     0xb93d14: add             x1, x1, HEAP, lsl #32
    // 0xb93d18: ldur            x0, [fp, #-0x30]
    // 0xb93d1c: ArrayStore: r1[2] = r0  ; List_4
    //     0xb93d1c: add             x25, x1, #0x17
    //     0xb93d20: str             w0, [x25]
    //     0xb93d24: tbz             w0, #0, #0xb93d40
    //     0xb93d28: ldurb           w16, [x1, #-1]
    //     0xb93d2c: ldurb           w17, [x0, #-1]
    //     0xb93d30: and             x16, x17, x16, lsr #2
    //     0xb93d34: tst             x16, HEAP, lsr #32
    //     0xb93d38: b.eq            #0xb93d40
    //     0xb93d3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb93d40: b               #0xb93d4c
    // 0xb93d44: mov             x2, x1
    // 0xb93d48: r3 = 6
    //     0xb93d48: movz            x3, #0x6
    // 0xb93d4c: ldur            x0, [fp, #-0x10]
    // 0xb93d50: LoadField: r1 = r0->field_43
    //     0xb93d50: ldur            w1, [x0, #0x43]
    // 0xb93d54: DecompressPointer r1
    //     0xb93d54: add             x1, x1, HEAP, lsl #32
    // 0xb93d58: r0 = DateTimeExtensions.humanizeDateTime()
    //     0xb93d58: bl              #0xb943c0  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanizeDateTime
    // 0xb93d5c: stur            x0, [fp, #-8]
    // 0xb93d60: r0 = UserInfo()
    //     0xb93d60: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93d64: mov             x2, x0
    // 0xb93d68: r0 = "Tanggal Transaksi"
    //     0xb93d68: add             x0, PP, #0x40, lsl #12  ; [pp+0x402a8] "Tanggal Transaksi"
    //     0xb93d6c: ldr             x0, [x0, #0x2a8]
    // 0xb93d70: stur            x2, [fp, #-0x30]
    // 0xb93d74: StoreField: r2->field_b = r0
    //     0xb93d74: stur            w0, [x2, #0xb]
    // 0xb93d78: ldur            x0, [fp, #-8]
    // 0xb93d7c: StoreField: r2->field_f = r0
    //     0xb93d7c: stur            w0, [x2, #0xf]
    // 0xb93d80: r0 = false
    //     0xb93d80: add             x0, NULL, #0x30  ; false
    // 0xb93d84: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93d84: stur            w0, [x2, #0x17]
    // 0xb93d88: ldur            x3, [fp, #-0x28]
    // 0xb93d8c: LoadField: r1 = r3->field_b
    //     0xb93d8c: ldur            w1, [x3, #0xb]
    // 0xb93d90: LoadField: r4 = r3->field_f
    //     0xb93d90: ldur            w4, [x3, #0xf]
    // 0xb93d94: DecompressPointer r4
    //     0xb93d94: add             x4, x4, HEAP, lsl #32
    // 0xb93d98: LoadField: r5 = r4->field_b
    //     0xb93d98: ldur            w5, [x4, #0xb]
    // 0xb93d9c: r4 = LoadInt32Instr(r1)
    //     0xb93d9c: sbfx            x4, x1, #1, #0x1f
    // 0xb93da0: stur            x4, [fp, #-0x38]
    // 0xb93da4: r1 = LoadInt32Instr(r5)
    //     0xb93da4: sbfx            x1, x5, #1, #0x1f
    // 0xb93da8: cmp             x4, x1
    // 0xb93dac: b.ne            #0xb93db8
    // 0xb93db0: mov             x1, x3
    // 0xb93db4: r0 = _growToNextCapacity()
    //     0xb93db4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93db8: ldur            x4, [fp, #-0x10]
    // 0xb93dbc: ldur            x2, [fp, #-0x28]
    // 0xb93dc0: ldur            x3, [fp, #-0x38]
    // 0xb93dc4: add             x0, x3, #1
    // 0xb93dc8: lsl             x1, x0, #1
    // 0xb93dcc: StoreField: r2->field_b = r1
    //     0xb93dcc: stur            w1, [x2, #0xb]
    // 0xb93dd0: LoadField: r1 = r2->field_f
    //     0xb93dd0: ldur            w1, [x2, #0xf]
    // 0xb93dd4: DecompressPointer r1
    //     0xb93dd4: add             x1, x1, HEAP, lsl #32
    // 0xb93dd8: ldur            x0, [fp, #-0x30]
    // 0xb93ddc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb93ddc: add             x25, x1, x3, lsl #2
    //     0xb93de0: add             x25, x25, #0xf
    //     0xb93de4: str             w0, [x25]
    //     0xb93de8: tbz             w0, #0, #0xb93e04
    //     0xb93dec: ldurb           w16, [x1, #-1]
    //     0xb93df0: ldurb           w17, [x0, #-1]
    //     0xb93df4: and             x16, x17, x16, lsr #2
    //     0xb93df8: tst             x16, HEAP, lsr #32
    //     0xb93dfc: b.eq            #0xb93e04
    //     0xb93e00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb93e04: LoadField: r1 = r4->field_2b
    //     0xb93e04: ldur            w1, [x4, #0x2b]
    // 0xb93e08: DecompressPointer r1
    //     0xb93e08: add             x1, x1, HEAP, lsl #32
    // 0xb93e0c: r0 = categoryTitle()
    //     0xb93e0c: bl              #0xb94318  ; [package:nuonline/app/data/models/transaction.dart] Donation::categoryTitle
    // 0xb93e10: stur            x0, [fp, #-8]
    // 0xb93e14: r0 = UserInfo()
    //     0xb93e14: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93e18: mov             x2, x0
    // 0xb93e1c: r0 = "Deskripsi"
    //     0xb93e1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f658] "Deskripsi"
    //     0xb93e20: ldr             x0, [x0, #0x658]
    // 0xb93e24: stur            x2, [fp, #-0x30]
    // 0xb93e28: StoreField: r2->field_b = r0
    //     0xb93e28: stur            w0, [x2, #0xb]
    // 0xb93e2c: ldur            x0, [fp, #-8]
    // 0xb93e30: StoreField: r2->field_f = r0
    //     0xb93e30: stur            w0, [x2, #0xf]
    // 0xb93e34: r0 = false
    //     0xb93e34: add             x0, NULL, #0x30  ; false
    // 0xb93e38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93e38: stur            w0, [x2, #0x17]
    // 0xb93e3c: ldur            x3, [fp, #-0x28]
    // 0xb93e40: LoadField: r1 = r3->field_b
    //     0xb93e40: ldur            w1, [x3, #0xb]
    // 0xb93e44: LoadField: r4 = r3->field_f
    //     0xb93e44: ldur            w4, [x3, #0xf]
    // 0xb93e48: DecompressPointer r4
    //     0xb93e48: add             x4, x4, HEAP, lsl #32
    // 0xb93e4c: LoadField: r5 = r4->field_b
    //     0xb93e4c: ldur            w5, [x4, #0xb]
    // 0xb93e50: r4 = LoadInt32Instr(r1)
    //     0xb93e50: sbfx            x4, x1, #1, #0x1f
    // 0xb93e54: stur            x4, [fp, #-0x38]
    // 0xb93e58: r1 = LoadInt32Instr(r5)
    //     0xb93e58: sbfx            x1, x5, #1, #0x1f
    // 0xb93e5c: cmp             x4, x1
    // 0xb93e60: b.ne            #0xb93e6c
    // 0xb93e64: mov             x1, x3
    // 0xb93e68: r0 = _growToNextCapacity()
    //     0xb93e68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93e6c: ldur            x4, [fp, #-0x10]
    // 0xb93e70: ldur            x2, [fp, #-0x28]
    // 0xb93e74: ldur            x3, [fp, #-0x38]
    // 0xb93e78: add             x5, x3, #1
    // 0xb93e7c: stur            x5, [fp, #-0x48]
    // 0xb93e80: lsl             x0, x5, #1
    // 0xb93e84: StoreField: r2->field_b = r0
    //     0xb93e84: stur            w0, [x2, #0xb]
    // 0xb93e88: LoadField: r6 = r2->field_f
    //     0xb93e88: ldur            w6, [x2, #0xf]
    // 0xb93e8c: DecompressPointer r6
    //     0xb93e8c: add             x6, x6, HEAP, lsl #32
    // 0xb93e90: mov             x1, x6
    // 0xb93e94: ldur            x0, [fp, #-0x30]
    // 0xb93e98: stur            x6, [fp, #-0x40]
    // 0xb93e9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb93e9c: add             x25, x1, x3, lsl #2
    //     0xb93ea0: add             x25, x25, #0xf
    //     0xb93ea4: str             w0, [x25]
    //     0xb93ea8: tbz             w0, #0, #0xb93ec4
    //     0xb93eac: ldurb           w16, [x1, #-1]
    //     0xb93eb0: ldurb           w17, [x0, #-1]
    //     0xb93eb4: and             x16, x17, x16, lsr #2
    //     0xb93eb8: tst             x16, HEAP, lsr #32
    //     0xb93ebc: b.eq            #0xb93ec4
    //     0xb93ec0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb93ec4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb93ec4: ldur            w0, [x4, #0x17]
    // 0xb93ec8: DecompressPointer r0
    //     0xb93ec8: add             x0, x0, HEAP, lsl #32
    // 0xb93ecc: stur            x0, [fp, #-8]
    // 0xb93ed0: r0 = UserInfo()
    //     0xb93ed0: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93ed4: mov             x2, x0
    // 0xb93ed8: r0 = "Nama"
    //     0xb93ed8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30510] "Nama"
    //     0xb93edc: ldr             x0, [x0, #0x510]
    // 0xb93ee0: stur            x2, [fp, #-0x30]
    // 0xb93ee4: StoreField: r2->field_b = r0
    //     0xb93ee4: stur            w0, [x2, #0xb]
    // 0xb93ee8: ldur            x0, [fp, #-8]
    // 0xb93eec: StoreField: r2->field_f = r0
    //     0xb93eec: stur            w0, [x2, #0xf]
    // 0xb93ef0: r0 = false
    //     0xb93ef0: add             x0, NULL, #0x30  ; false
    // 0xb93ef4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93ef4: stur            w0, [x2, #0x17]
    // 0xb93ef8: ldur            x1, [fp, #-0x40]
    // 0xb93efc: LoadField: r3 = r1->field_b
    //     0xb93efc: ldur            w3, [x1, #0xb]
    // 0xb93f00: r1 = LoadInt32Instr(r3)
    //     0xb93f00: sbfx            x1, x3, #1, #0x1f
    // 0xb93f04: ldur            x3, [fp, #-0x48]
    // 0xb93f08: cmp             x3, x1
    // 0xb93f0c: b.ne            #0xb93f18
    // 0xb93f10: ldur            x1, [fp, #-0x28]
    // 0xb93f14: r0 = _growToNextCapacity()
    //     0xb93f14: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb93f18: ldur            x4, [fp, #-0x20]
    // 0xb93f1c: ldur            x2, [fp, #-0x48]
    // 0xb93f20: ldur            x3, [fp, #-0x28]
    // 0xb93f24: add             x5, x2, #1
    // 0xb93f28: stur            x5, [fp, #-0x38]
    // 0xb93f2c: lsl             x0, x5, #1
    // 0xb93f30: StoreField: r3->field_b = r0
    //     0xb93f30: stur            w0, [x3, #0xb]
    // 0xb93f34: LoadField: r6 = r3->field_f
    //     0xb93f34: ldur            w6, [x3, #0xf]
    // 0xb93f38: DecompressPointer r6
    //     0xb93f38: add             x6, x6, HEAP, lsl #32
    // 0xb93f3c: mov             x1, x6
    // 0xb93f40: ldur            x0, [fp, #-0x30]
    // 0xb93f44: stur            x6, [fp, #-0x40]
    // 0xb93f48: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb93f48: add             x25, x1, x2, lsl #2
    //     0xb93f4c: add             x25, x25, #0xf
    //     0xb93f50: str             w0, [x25]
    //     0xb93f54: tbz             w0, #0, #0xb93f70
    //     0xb93f58: ldurb           w16, [x1, #-1]
    //     0xb93f5c: ldurb           w17, [x0, #-1]
    //     0xb93f60: and             x16, x17, x16, lsr #2
    //     0xb93f64: tst             x16, HEAP, lsr #32
    //     0xb93f68: b.eq            #0xb93f70
    //     0xb93f6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb93f70: LoadField: r0 = r4->field_7
    //     0xb93f70: ldur            x0, [x4, #7]
    // 0xb93f74: cmp             x0, #1
    // 0xb93f78: b.gt            #0xb93f9c
    // 0xb93f7c: cmp             x0, #0
    // 0xb93f80: b.gt            #0xb93f90
    // 0xb93f84: r0 = "QRIS"
    //     0xb93f84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xb93f88: ldr             x0, [x0, #0xf50]
    // 0xb93f8c: b               #0xb93fb8
    // 0xb93f90: r0 = "BNI Virtual Account"
    //     0xb93f90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xb93f94: ldr             x0, [x0, #0xf58]
    // 0xb93f98: b               #0xb93fb8
    // 0xb93f9c: cmp             x0, #2
    // 0xb93fa0: b.gt            #0xb93fb0
    // 0xb93fa4: r0 = "BSI Virtual Account"
    //     0xb93fa4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xb93fa8: ldr             x0, [x0, #0xf60]
    // 0xb93fac: b               #0xb93fb8
    // 0xb93fb0: r0 = "BRI Virtual Account"
    //     0xb93fb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xb93fb4: ldr             x0, [x0, #0xf68]
    // 0xb93fb8: stur            x0, [fp, #-8]
    // 0xb93fbc: r0 = UserInfo()
    //     0xb93fbc: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb93fc0: mov             x2, x0
    // 0xb93fc4: r0 = "Metode Pembayaran"
    //     0xb93fc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] "Metode Pembayaran"
    //     0xb93fc8: ldr             x0, [x0, #0xe08]
    // 0xb93fcc: stur            x2, [fp, #-0x20]
    // 0xb93fd0: StoreField: r2->field_b = r0
    //     0xb93fd0: stur            w0, [x2, #0xb]
    // 0xb93fd4: ldur            x0, [fp, #-8]
    // 0xb93fd8: StoreField: r2->field_f = r0
    //     0xb93fd8: stur            w0, [x2, #0xf]
    // 0xb93fdc: r0 = false
    //     0xb93fdc: add             x0, NULL, #0x30  ; false
    // 0xb93fe0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb93fe0: stur            w0, [x2, #0x17]
    // 0xb93fe4: ldur            x1, [fp, #-0x40]
    // 0xb93fe8: LoadField: r3 = r1->field_b
    //     0xb93fe8: ldur            w3, [x1, #0xb]
    // 0xb93fec: r1 = LoadInt32Instr(r3)
    //     0xb93fec: sbfx            x1, x3, #1, #0x1f
    // 0xb93ff0: ldur            x3, [fp, #-0x38]
    // 0xb93ff4: cmp             x3, x1
    // 0xb93ff8: b.ne            #0xb94004
    // 0xb93ffc: ldur            x1, [fp, #-0x28]
    // 0xb94000: r0 = _growToNextCapacity()
    //     0xb94000: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb94004: ldur            x4, [fp, #-0x10]
    // 0xb94008: ldur            x2, [fp, #-0x38]
    // 0xb9400c: ldur            x3, [fp, #-0x28]
    // 0xb94010: add             x0, x2, #1
    // 0xb94014: lsl             x1, x0, #1
    // 0xb94018: StoreField: r3->field_b = r1
    //     0xb94018: stur            w1, [x3, #0xb]
    // 0xb9401c: LoadField: r1 = r3->field_f
    //     0xb9401c: ldur            w1, [x3, #0xf]
    // 0xb94020: DecompressPointer r1
    //     0xb94020: add             x1, x1, HEAP, lsl #32
    // 0xb94024: ldur            x0, [fp, #-0x20]
    // 0xb94028: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb94028: add             x25, x1, x2, lsl #2
    //     0xb9402c: add             x25, x25, #0xf
    //     0xb94030: str             w0, [x25]
    //     0xb94034: tbz             w0, #0, #0xb94050
    //     0xb94038: ldurb           w16, [x1, #-1]
    //     0xb9403c: ldurb           w17, [x0, #-1]
    //     0xb94040: and             x16, x17, x16, lsr #2
    //     0xb94044: tst             x16, HEAP, lsr #32
    //     0xb94048: b.eq            #0xb94050
    //     0xb9404c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb94050: LoadField: r1 = r4->field_1b
    //     0xb94050: ldur            x1, [x4, #0x1b]
    // 0xb94054: r0 = IntExtension.idr()
    //     0xb94054: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb94058: stur            x0, [fp, #-8]
    // 0xb9405c: r0 = UserInfo()
    //     0xb9405c: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb94060: mov             x2, x0
    // 0xb94064: r0 = "Nominal"
    //     0xb94064: add             x0, PP, #0x30, lsl #12  ; [pp+0x30500] "Nominal"
    //     0xb94068: ldr             x0, [x0, #0x500]
    // 0xb9406c: stur            x2, [fp, #-0x20]
    // 0xb94070: StoreField: r2->field_b = r0
    //     0xb94070: stur            w0, [x2, #0xb]
    // 0xb94074: ldur            x0, [fp, #-8]
    // 0xb94078: StoreField: r2->field_f = r0
    //     0xb94078: stur            w0, [x2, #0xf]
    // 0xb9407c: r0 = false
    //     0xb9407c: add             x0, NULL, #0x30  ; false
    // 0xb94080: ArrayStore: r2[0] = r0  ; List_4
    //     0xb94080: stur            w0, [x2, #0x17]
    // 0xb94084: ldur            x3, [fp, #-0x28]
    // 0xb94088: LoadField: r1 = r3->field_b
    //     0xb94088: ldur            w1, [x3, #0xb]
    // 0xb9408c: LoadField: r4 = r3->field_f
    //     0xb9408c: ldur            w4, [x3, #0xf]
    // 0xb94090: DecompressPointer r4
    //     0xb94090: add             x4, x4, HEAP, lsl #32
    // 0xb94094: LoadField: r5 = r4->field_b
    //     0xb94094: ldur            w5, [x4, #0xb]
    // 0xb94098: r4 = LoadInt32Instr(r1)
    //     0xb94098: sbfx            x4, x1, #1, #0x1f
    // 0xb9409c: stur            x4, [fp, #-0x38]
    // 0xb940a0: r1 = LoadInt32Instr(r5)
    //     0xb940a0: sbfx            x1, x5, #1, #0x1f
    // 0xb940a4: cmp             x4, x1
    // 0xb940a8: b.ne            #0xb940b4
    // 0xb940ac: mov             x1, x3
    // 0xb940b0: r0 = _growToNextCapacity()
    //     0xb940b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb940b4: ldur            x4, [fp, #-0x10]
    // 0xb940b8: ldur            x2, [fp, #-0x28]
    // 0xb940bc: ldur            x3, [fp, #-0x38]
    // 0xb940c0: add             x0, x3, #1
    // 0xb940c4: lsl             x1, x0, #1
    // 0xb940c8: StoreField: r2->field_b = r1
    //     0xb940c8: stur            w1, [x2, #0xb]
    // 0xb940cc: LoadField: r1 = r2->field_f
    //     0xb940cc: ldur            w1, [x2, #0xf]
    // 0xb940d0: DecompressPointer r1
    //     0xb940d0: add             x1, x1, HEAP, lsl #32
    // 0xb940d4: ldur            x0, [fp, #-0x20]
    // 0xb940d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb940d8: add             x25, x1, x3, lsl #2
    //     0xb940dc: add             x25, x25, #0xf
    //     0xb940e0: str             w0, [x25]
    //     0xb940e4: tbz             w0, #0, #0xb94100
    //     0xb940e8: ldurb           w16, [x1, #-1]
    //     0xb940ec: ldurb           w17, [x0, #-1]
    //     0xb940f0: and             x16, x17, x16, lsr #2
    //     0xb940f4: tst             x16, HEAP, lsr #32
    //     0xb940f8: b.eq            #0xb94100
    //     0xb940fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb94100: LoadField: r0 = r4->field_27
    //     0xb94100: ldur            w0, [x4, #0x27]
    // 0xb94104: DecompressPointer r0
    //     0xb94104: add             x0, x0, HEAP, lsl #32
    // 0xb94108: stur            x0, [fp, #-8]
    // 0xb9410c: r16 = "SUCCESS"
    //     0xb9410c: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb94110: ldr             x16, [x16, #0x2c0]
    // 0xb94114: stp             x0, x16, [SP]
    // 0xb94118: r0 = ==()
    //     0xb94118: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9411c: tbnz            w0, #4, #0xb9412c
    // 0xb94120: r1 = "Berhasil"
    //     0xb94120: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f78] "Berhasil"
    //     0xb94124: ldr             x1, [x1, #0xf78]
    // 0xb94128: b               #0xb9417c
    // 0xb9412c: r16 = "PENDING"
    //     0xb9412c: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb94130: ldr             x16, [x16, #0x2c8]
    // 0xb94134: ldur            lr, [fp, #-8]
    // 0xb94138: stp             lr, x16, [SP]
    // 0xb9413c: r0 = ==()
    //     0xb9413c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb94140: tbnz            w0, #4, #0xb94150
    // 0xb94144: r1 = "Menunggu"
    //     0xb94144: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f80] "Menunggu"
    //     0xb94148: ldr             x1, [x1, #0xf80]
    // 0xb9414c: b               #0xb9417c
    // 0xb94150: r16 = "EXPIRED"
    //     0xb94150: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb94154: ldr             x16, [x16, #0xf88]
    // 0xb94158: ldur            lr, [fp, #-8]
    // 0xb9415c: stp             lr, x16, [SP]
    // 0xb94160: r0 = ==()
    //     0xb94160: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb94164: tbnz            w0, #4, #0xb94174
    // 0xb94168: r1 = "Dibatalkan"
    //     0xb94168: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f90] "Dibatalkan"
    //     0xb9416c: ldr             x1, [x1, #0xf90]
    // 0xb94170: b               #0xb9417c
    // 0xb94174: r1 = "Menunggu"
    //     0xb94174: add             x1, PP, #0x34, lsl #12  ; [pp+0x34f80] "Menunggu"
    //     0xb94178: ldr             x1, [x1, #0xf80]
    // 0xb9417c: ldur            x0, [fp, #-8]
    // 0xb94180: stur            x1, [fp, #-0x10]
    // 0xb94184: r2 = LoadClassIdInstr(r0)
    //     0xb94184: ldur            x2, [x0, #-1]
    //     0xb94188: ubfx            x2, x2, #0xc, #0x14
    // 0xb9418c: r16 = "SUCCESS"
    //     0xb9418c: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb94190: ldr             x16, [x16, #0x2c0]
    // 0xb94194: stp             x16, x0, [SP]
    // 0xb94198: mov             x0, x2
    // 0xb9419c: mov             lr, x0
    // 0xb941a0: ldr             lr, [x21, lr, lsl #3]
    // 0xb941a4: blr             lr
    // 0xb941a8: stur            x0, [fp, #-8]
    // 0xb941ac: r0 = GetNavigation.theme()
    //     0xb941ac: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb941b0: LoadField: r1 = r0->field_3f
    //     0xb941b0: ldur            w1, [x0, #0x3f]
    // 0xb941b4: DecompressPointer r1
    //     0xb941b4: add             x1, x1, HEAP, lsl #32
    // 0xb941b8: LoadField: r0 = r1->field_2b
    //     0xb941b8: ldur            w0, [x1, #0x2b]
    // 0xb941bc: DecompressPointer r0
    //     0xb941bc: add             x0, x0, HEAP, lsl #32
    // 0xb941c0: stur            x0, [fp, #-0x20]
    // 0xb941c4: r1 = _ConstMap len:3
    //     0xb941c4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb941c8: ldr             x1, [x1, #0xcd0]
    // 0xb941cc: r2 = 6
    //     0xb941cc: movz            x2, #0x6
    // 0xb941d0: r0 = []()
    //     0xb941d0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb941d4: r1 = _ConstMap len:3
    //     0xb941d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb941d8: ldr             x1, [x1, #0xcd0]
    // 0xb941dc: r2 = 4
    //     0xb941dc: movz            x2, #0x4
    // 0xb941e0: stur            x0, [fp, #-0x30]
    // 0xb941e4: r0 = []()
    //     0xb941e4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb941e8: r16 = <Color?>
    //     0xb941e8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb941ec: ldr             x16, [x16, #0x98]
    // 0xb941f0: stp             x0, x16, [SP, #8]
    // 0xb941f4: ldur            x16, [fp, #-0x30]
    // 0xb941f8: str             x16, [SP]
    // 0xb941fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb941fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb94200: r0 = mode()
    //     0xb94200: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb94204: mov             x1, x0
    // 0xb94208: ldur            x0, [fp, #-8]
    // 0xb9420c: tbnz            w0, #4, #0xb94218
    // 0xb94210: ldur            x2, [fp, #-0x20]
    // 0xb94214: b               #0xb9421c
    // 0xb94218: mov             x2, x1
    // 0xb9421c: ldur            x0, [fp, #-0x10]
    // 0xb94220: ldur            x1, [fp, #-0x28]
    // 0xb94224: stur            x2, [fp, #-8]
    // 0xb94228: r0 = UserInfo()
    //     0xb94228: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb9422c: mov             x2, x0
    // 0xb94230: r0 = "Status"
    //     0xb94230: add             x0, PP, #0x40, lsl #12  ; [pp+0x402b0] "Status"
    //     0xb94234: ldr             x0, [x0, #0x2b0]
    // 0xb94238: stur            x2, [fp, #-0x20]
    // 0xb9423c: StoreField: r2->field_b = r0
    //     0xb9423c: stur            w0, [x2, #0xb]
    // 0xb94240: ldur            x0, [fp, #-0x10]
    // 0xb94244: StoreField: r2->field_f = r0
    //     0xb94244: stur            w0, [x2, #0xf]
    // 0xb94248: r0 = false
    //     0xb94248: add             x0, NULL, #0x30  ; false
    // 0xb9424c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9424c: stur            w0, [x2, #0x17]
    // 0xb94250: ldur            x0, [fp, #-8]
    // 0xb94254: StoreField: r2->field_1b = r0
    //     0xb94254: stur            w0, [x2, #0x1b]
    // 0xb94258: ldur            x0, [fp, #-0x28]
    // 0xb9425c: LoadField: r1 = r0->field_b
    //     0xb9425c: ldur            w1, [x0, #0xb]
    // 0xb94260: LoadField: r3 = r0->field_f
    //     0xb94260: ldur            w3, [x0, #0xf]
    // 0xb94264: DecompressPointer r3
    //     0xb94264: add             x3, x3, HEAP, lsl #32
    // 0xb94268: LoadField: r4 = r3->field_b
    //     0xb94268: ldur            w4, [x3, #0xb]
    // 0xb9426c: r3 = LoadInt32Instr(r1)
    //     0xb9426c: sbfx            x3, x1, #1, #0x1f
    // 0xb94270: stur            x3, [fp, #-0x38]
    // 0xb94274: r1 = LoadInt32Instr(r4)
    //     0xb94274: sbfx            x1, x4, #1, #0x1f
    // 0xb94278: cmp             x3, x1
    // 0xb9427c: b.ne            #0xb94288
    // 0xb94280: mov             x1, x0
    // 0xb94284: r0 = _growToNextCapacity()
    //     0xb94284: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb94288: ldur            x4, [fp, #-0x18]
    // 0xb9428c: ldur            x2, [fp, #-0x28]
    // 0xb94290: ldur            x3, [fp, #-0x38]
    // 0xb94294: add             x0, x3, #1
    // 0xb94298: lsl             x1, x0, #1
    // 0xb9429c: StoreField: r2->field_b = r1
    //     0xb9429c: stur            w1, [x2, #0xb]
    // 0xb942a0: LoadField: r1 = r2->field_f
    //     0xb942a0: ldur            w1, [x2, #0xf]
    // 0xb942a4: DecompressPointer r1
    //     0xb942a4: add             x1, x1, HEAP, lsl #32
    // 0xb942a8: ldur            x0, [fp, #-0x20]
    // 0xb942ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb942ac: add             x25, x1, x3, lsl #2
    //     0xb942b0: add             x25, x25, #0xf
    //     0xb942b4: str             w0, [x25]
    //     0xb942b8: tbz             w0, #0, #0xb942d4
    //     0xb942bc: ldurb           w16, [x1, #-1]
    //     0xb942c0: ldurb           w17, [x0, #-1]
    //     0xb942c4: and             x16, x17, x16, lsr #2
    //     0xb942c8: tst             x16, HEAP, lsr #32
    //     0xb942cc: b.eq            #0xb942d4
    //     0xb942d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb942d4: r0 = NSection()
    //     0xb942d4: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb942d8: r1 = "Detail Transaksi"
    //     0xb942d8: add             x1, PP, #0x40, lsl #12  ; [pp+0x402b8] "Detail Transaksi"
    //     0xb942dc: ldr             x1, [x1, #0x2b8]
    // 0xb942e0: StoreField: r0->field_b = r1
    //     0xb942e0: stur            w1, [x0, #0xb]
    // 0xb942e4: ldur            x1, [fp, #-0x28]
    // 0xb942e8: StoreField: r0->field_f = r1
    //     0xb942e8: stur            w1, [x0, #0xf]
    // 0xb942ec: ldur            x1, [fp, #-0x18]
    // 0xb942f0: StoreField: r0->field_1f = r1
    //     0xb942f0: stur            w1, [x0, #0x1f]
    // 0xb942f4: r1 = true
    //     0xb942f4: add             x1, NULL, #0x20  ; true
    // 0xb942f8: StoreField: r0->field_27 = r1
    //     0xb942f8: stur            w1, [x0, #0x27]
    // 0xb942fc: StoreField: r0->field_2b = r1
    //     0xb942fc: stur            w1, [x0, #0x2b]
    // 0xb94300: LeaveFrame
    //     0xb94300: mov             SP, fp
    //     0xb94304: ldp             fp, lr, [SP], #0x10
    // 0xb94308: ret
    //     0xb94308: ret             
    // 0xb9430c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9430c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94310: b               #0xb93bd0
    // 0xb94314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
