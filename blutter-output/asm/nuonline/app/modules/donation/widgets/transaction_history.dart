// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_history.dart

// class id: 1050248, size: 0x8
class :: {
}

// class id: 5020, size: 0x10, field offset: 0xc
//   const constructor, 
class TransactionHistoryListTile extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb94fc8, size: 0x6b4
    // 0xb94fc8: EnterFrame
    //     0xb94fc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb94fcc: mov             fp, SP
    // 0xb94fd0: AllocStack(0x58)
    //     0xb94fd0: sub             SP, SP, #0x58
    // 0xb94fd4: SetupParameters(TransactionHistoryListTile this /* r1 => r0, fp-0x8 */)
    //     0xb94fd4: mov             x0, x1
    //     0xb94fd8: stur            x1, [fp, #-8]
    // 0xb94fdc: CheckStackOverflow
    //     0xb94fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94fe0: cmp             SP, x16
    //     0xb94fe4: b.ls            #0xb95670
    // 0xb94fe8: r1 = _ConstMap len:6
    //     0xb94fe8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94fec: ldr             x1, [x1, #0xc20]
    // 0xb94ff0: r2 = 2
    //     0xb94ff0: movz            x2, #0x2
    // 0xb94ff4: r0 = []()
    //     0xb94ff4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94ff8: r1 = _ConstMap len:3
    //     0xb94ff8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb94ffc: ldr             x1, [x1, #0xbe8]
    // 0xb95000: r2 = 2
    //     0xb95000: movz            x2, #0x2
    // 0xb95004: stur            x0, [fp, #-0x10]
    // 0xb95008: r0 = []()
    //     0xb95008: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb9500c: r16 = <Color?>
    //     0xb9500c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb95010: ldr             x16, [x16, #0x98]
    // 0xb95014: stp             x0, x16, [SP, #8]
    // 0xb95018: ldur            x16, [fp, #-0x10]
    // 0xb9501c: str             x16, [SP]
    // 0xb95020: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb95020: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb95024: r0 = mode()
    //     0xb95024: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb95028: mov             x2, x0
    // 0xb9502c: ldur            x0, [fp, #-8]
    // 0xb95030: stur            x2, [fp, #-0x18]
    // 0xb95034: LoadField: r3 = r0->field_b
    //     0xb95034: ldur            w3, [x0, #0xb]
    // 0xb95038: DecompressPointer r3
    //     0xb95038: add             x3, x3, HEAP, lsl #32
    // 0xb9503c: mov             x1, x3
    // 0xb95040: stur            x3, [fp, #-0x10]
    // 0xb95044: r0 = alias()
    //     0xb95044: bl              #0xb9592c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::alias
    // 0xb95048: mov             x1, x0
    // 0xb9504c: r0 = StringExtension.avatar()
    //     0xb9504c: bl              #0xb9578c  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.avatar
    // 0xb95050: stur            x0, [fp, #-8]
    // 0xb95054: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb95054: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb95058: ldr             x0, [x0, #0x2670]
    //     0xb9505c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb95060: cmp             w0, w16
    //     0xb95064: b.ne            #0xb95070
    //     0xb95068: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb9506c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb95070: r0 = GetNavigation.textTheme()
    //     0xb95070: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95074: LoadField: r3 = r0->field_f
    //     0xb95074: ldur            w3, [x0, #0xf]
    // 0xb95078: DecompressPointer r3
    //     0xb95078: add             x3, x3, HEAP, lsl #32
    // 0xb9507c: stur            x3, [fp, #-0x20]
    // 0xb95080: cmp             w3, NULL
    // 0xb95084: b.eq            #0xb95678
    // 0xb95088: r1 = _ConstMap len:6
    //     0xb95088: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb9508c: ldr             x1, [x1, #0xc20]
    // 0xb95090: r2 = 8
    //     0xb95090: movz            x2, #0x8
    // 0xb95094: r0 = []()
    //     0xb95094: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb95098: r1 = _ConstMap len:6
    //     0xb95098: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb9509c: ldr             x1, [x1, #0xc20]
    // 0xb950a0: r2 = 6
    //     0xb950a0: movz            x2, #0x6
    // 0xb950a4: stur            x0, [fp, #-0x28]
    // 0xb950a8: r0 = []()
    //     0xb950a8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb950ac: r16 = <Color?>
    //     0xb950ac: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb950b0: ldr             x16, [x16, #0x98]
    // 0xb950b4: stp             x0, x16, [SP, #8]
    // 0xb950b8: ldur            x16, [fp, #-0x28]
    // 0xb950bc: str             x16, [SP]
    // 0xb950c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb950c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb950c4: r0 = mode()
    //     0xb950c4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb950c8: r16 = Instance_FontWeight
    //     0xb950c8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e28] Obj!FontWeight@e26531
    //     0xb950cc: ldr             x16, [x16, #0xe28]
    // 0xb950d0: stp             x0, x16, [SP]
    // 0xb950d4: ldur            x1, [fp, #-0x20]
    // 0xb950d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0xb950d8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27ff8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0xb950dc: ldr             x4, [x4, #0xff8]
    // 0xb950e0: r0 = copyWith()
    //     0xb950e0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb950e4: stur            x0, [fp, #-0x20]
    // 0xb950e8: r0 = Text()
    //     0xb950e8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb950ec: mov             x1, x0
    // 0xb950f0: ldur            x0, [fp, #-8]
    // 0xb950f4: stur            x1, [fp, #-0x28]
    // 0xb950f8: StoreField: r1->field_b = r0
    //     0xb950f8: stur            w0, [x1, #0xb]
    // 0xb950fc: ldur            x0, [fp, #-0x20]
    // 0xb95100: StoreField: r1->field_13 = r0
    //     0xb95100: stur            w0, [x1, #0x13]
    // 0xb95104: r0 = CircleAvatar()
    //     0xb95104: bl              #0xb95780  ; AllocateCircleAvatarStub -> CircleAvatar (size=0x2c)
    // 0xb95108: mov             x1, x0
    // 0xb9510c: ldur            x0, [fp, #-0x28]
    // 0xb95110: stur            x1, [fp, #-8]
    // 0xb95114: StoreField: r1->field_b = r0
    //     0xb95114: stur            w0, [x1, #0xb]
    // 0xb95118: ldur            x0, [fp, #-0x18]
    // 0xb9511c: StoreField: r1->field_f = r0
    //     0xb9511c: stur            w0, [x1, #0xf]
    // 0xb95120: r0 = SizedBox()
    //     0xb95120: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb95124: mov             x2, x0
    // 0xb95128: r0 = 48.000000
    //     0xb95128: add             x0, PP, #0x31, lsl #12  ; [pp+0x31558] 48
    //     0xb9512c: ldr             x0, [x0, #0x558]
    // 0xb95130: stur            x2, [fp, #-0x18]
    // 0xb95134: StoreField: r2->field_f = r0
    //     0xb95134: stur            w0, [x2, #0xf]
    // 0xb95138: StoreField: r2->field_13 = r0
    //     0xb95138: stur            w0, [x2, #0x13]
    // 0xb9513c: ldur            x0, [fp, #-8]
    // 0xb95140: StoreField: r2->field_b = r0
    //     0xb95140: stur            w0, [x2, #0xb]
    // 0xb95144: ldur            x0, [fp, #-0x10]
    // 0xb95148: LoadField: r3 = r0->field_23
    //     0xb95148: ldur            w3, [x0, #0x23]
    // 0xb9514c: DecompressPointer r3
    //     0xb9514c: add             x3, x3, HEAP, lsl #32
    // 0xb95150: mov             x1, x0
    // 0xb95154: stur            x3, [fp, #-8]
    // 0xb95158: r0 = anonName()
    //     0xb95158: bl              #0xb9567c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::anonName
    // 0xb9515c: mov             x1, x0
    // 0xb95160: ldur            x0, [fp, #-0x10]
    // 0xb95164: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb95164: ldur            w2, [x0, #0x17]
    // 0xb95168: DecompressPointer r2
    //     0xb95168: add             x2, x2, HEAP, lsl #32
    // 0xb9516c: ldur            x3, [fp, #-8]
    // 0xb95170: tbz             w3, #4, #0xb95178
    // 0xb95174: mov             x1, x2
    // 0xb95178: stur            x1, [fp, #-8]
    // 0xb9517c: r0 = GetNavigation.textTheme()
    //     0xb9517c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95180: LoadField: r1 = r0->field_f
    //     0xb95180: ldur            w1, [x0, #0xf]
    // 0xb95184: DecompressPointer r1
    //     0xb95184: add             x1, x1, HEAP, lsl #32
    // 0xb95188: stur            x1, [fp, #-0x20]
    // 0xb9518c: cmp             w1, NULL
    // 0xb95190: b.ne            #0xb9519c
    // 0xb95194: r2 = Null
    //     0xb95194: mov             x2, NULL
    // 0xb95198: b               #0xb951d0
    // 0xb9519c: r0 = GetNavigation.theme()
    //     0xb9519c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb951a0: LoadField: r1 = r0->field_3f
    //     0xb951a0: ldur            w1, [x0, #0x3f]
    // 0xb951a4: DecompressPointer r1
    //     0xb951a4: add             x1, x1, HEAP, lsl #32
    // 0xb951a8: LoadField: r0 = r1->field_2b
    //     0xb951a8: ldur            w0, [x1, #0x2b]
    // 0xb951ac: DecompressPointer r0
    //     0xb951ac: add             x0, x0, HEAP, lsl #32
    // 0xb951b0: r16 = 14.000000
    //     0xb951b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb951b4: ldr             x16, [x16, #0x9a0]
    // 0xb951b8: stp             x0, x16, [SP]
    // 0xb951bc: ldur            x1, [fp, #-0x20]
    // 0xb951c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb951c0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb951c4: ldr             x4, [x4, #0xaa0]
    // 0xb951c8: r0 = copyWith()
    //     0xb951c8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb951cc: mov             x2, x0
    // 0xb951d0: ldur            x0, [fp, #-0x10]
    // 0xb951d4: ldur            x1, [fp, #-8]
    // 0xb951d8: stur            x2, [fp, #-0x20]
    // 0xb951dc: r0 = Text()
    //     0xb951dc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb951e0: mov             x4, x0
    // 0xb951e4: ldur            x0, [fp, #-8]
    // 0xb951e8: stur            x4, [fp, #-0x28]
    // 0xb951ec: StoreField: r4->field_b = r0
    //     0xb951ec: stur            w0, [x4, #0xb]
    // 0xb951f0: ldur            x0, [fp, #-0x20]
    // 0xb951f4: StoreField: r4->field_13 = r0
    //     0xb951f4: stur            w0, [x4, #0x13]
    // 0xb951f8: ldur            x0, [fp, #-0x10]
    // 0xb951fc: LoadField: r1 = r0->field_2b
    //     0xb951fc: ldur            w1, [x0, #0x2b]
    // 0xb95200: DecompressPointer r1
    //     0xb95200: add             x1, x1, HEAP, lsl #32
    // 0xb95204: LoadField: r2 = r1->field_13
    //     0xb95204: ldur            w2, [x1, #0x13]
    // 0xb95208: DecompressPointer r2
    //     0xb95208: add             x2, x2, HEAP, lsl #32
    // 0xb9520c: LoadField: r5 = r2->field_7
    //     0xb9520c: ldur            x5, [x2, #7]
    // 0xb95210: stur            x5, [fp, #-0x30]
    // 0xb95214: cmp             x5, #2
    // 0xb95218: b.ne            #0xb95228
    // 0xb9521c: r6 = "Bersedekah sebesar"
    //     0xb9521c: add             x6, PP, #0x35, lsl #12  ; [pp+0x353c8] "Bersedekah sebesar"
    //     0xb95220: ldr             x6, [x6, #0x3c8]
    // 0xb95224: b               #0xb9526c
    // 0xb95228: cmp             x5, #1
    // 0xb9522c: b.ne            #0xb9523c
    // 0xb95230: r6 = "Membayar Zakat"
    //     0xb95230: add             x6, PP, #0x35, lsl #12  ; [pp+0x353d0] "Membayar Zakat"
    //     0xb95234: ldr             x6, [x6, #0x3d0]
    // 0xb95238: b               #0xb9526c
    // 0xb9523c: cmp             x5, #4
    // 0xb95240: b.ne            #0xb95250
    // 0xb95244: r6 = "Berqurban"
    //     0xb95244: add             x6, PP, #0x35, lsl #12  ; [pp+0x353d8] "Berqurban"
    //     0xb95248: ldr             x6, [x6, #0x3d8]
    // 0xb9524c: b               #0xb9526c
    // 0xb95250: cmp             x5, #5
    // 0xb95254: b.ne            #0xb95264
    // 0xb95258: r6 = "Membayar"
    //     0xb95258: add             x6, PP, #0x35, lsl #12  ; [pp+0x353e0] "Membayar"
    //     0xb9525c: ldr             x6, [x6, #0x3e0]
    // 0xb95260: b               #0xb9526c
    // 0xb95264: r6 = "Berdonasi sebesar"
    //     0xb95264: add             x6, PP, #0x35, lsl #12  ; [pp+0x353e8] "Berdonasi sebesar"
    //     0xb95268: ldr             x6, [x6, #0x3e8]
    // 0xb9526c: stur            x6, [fp, #-8]
    // 0xb95270: cmp             x5, #1
    // 0xb95274: b.ne            #0xb952c0
    // 0xb95278: LoadField: r2 = r1->field_f
    //     0xb95278: ldur            w2, [x1, #0xf]
    // 0xb9527c: DecompressPointer r2
    //     0xb9527c: add             x2, x2, HEAP, lsl #32
    // 0xb95280: mov             x1, x2
    // 0xb95284: r2 = "Simpanan Emas, Perak, dan Perhiasan"
    //     0xb95284: add             x2, PP, #0x35, lsl #12  ; [pp+0x353f0] "Simpanan Emas, Perak, dan Perhiasan"
    //     0xb95288: ldr             x2, [x2, #0x3f0]
    // 0xb9528c: r3 = "simpanan perhiasan"
    //     0xb9528c: add             x3, PP, #0x35, lsl #12  ; [pp+0x353f8] "simpanan perhiasan"
    //     0xb95290: ldr             x3, [x3, #0x3f8]
    // 0xb95294: r0 = replaceAll()
    //     0xb95294: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb95298: r1 = LoadClassIdInstr(r0)
    //     0xb95298: ldur            x1, [x0, #-1]
    //     0xb9529c: ubfx            x1, x1, #0xc, #0x14
    // 0xb952a0: str             x0, [SP]
    // 0xb952a4: mov             x0, x1
    // 0xb952a8: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb952a8: sub             lr, x0, #0xffe
    //     0xb952ac: ldr             lr, [x21, lr, lsl #3]
    //     0xb952b0: blr             lr
    // 0xb952b4: mov             x1, x0
    // 0xb952b8: ldur            x0, [fp, #-0x30]
    // 0xb952bc: b               #0xb9531c
    // 0xb952c0: mov             x0, x5
    // 0xb952c4: cmp             x0, #4
    // 0xb952c8: b.ne            #0xb952e8
    // 0xb952cc: ldur            x2, [fp, #-0x10]
    // 0xb952d0: LoadField: r1 = r2->field_37
    //     0xb952d0: ldur            w1, [x2, #0x37]
    // 0xb952d4: DecompressPointer r1
    //     0xb952d4: add             x1, x1, HEAP, lsl #32
    // 0xb952d8: cmp             w1, NULL
    // 0xb952dc: b.ne            #0xb9531c
    // 0xb952e0: r1 = ""
    //     0xb952e0: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb952e4: b               #0xb9531c
    // 0xb952e8: ldur            x2, [fp, #-0x10]
    // 0xb952ec: cmp             x0, #5
    // 0xb952f0: b.ne            #0xb9530c
    // 0xb952f4: LoadField: r1 = r2->field_37
    //     0xb952f4: ldur            w1, [x2, #0x37]
    // 0xb952f8: DecompressPointer r1
    //     0xb952f8: add             x1, x1, HEAP, lsl #32
    // 0xb952fc: cmp             w1, NULL
    // 0xb95300: b.ne            #0xb9531c
    // 0xb95304: r1 = ""
    //     0xb95304: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb95308: b               #0xb9531c
    // 0xb9530c: LoadField: r1 = r2->field_1b
    //     0xb9530c: ldur            x1, [x2, #0x1b]
    // 0xb95310: r0 = IntExtension.idr()
    //     0xb95310: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb95314: mov             x1, x0
    // 0xb95318: ldur            x0, [fp, #-0x30]
    // 0xb9531c: stur            x1, [fp, #-0x38]
    // 0xb95320: cmp             x0, #1
    // 0xb95324: b.eq            #0xb95334
    // 0xb95328: r2 = Instance_FontWeight
    //     0xb95328: add             x2, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb9532c: ldr             x2, [x2, #0xe20]
    // 0xb95330: b               #0xb95338
    // 0xb95334: r2 = Null
    //     0xb95334: mov             x2, NULL
    // 0xb95338: ldur            x0, [fp, #-8]
    // 0xb9533c: stur            x2, [fp, #-0x20]
    // 0xb95340: r0 = TextStyle()
    //     0xb95340: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb95344: mov             x1, x0
    // 0xb95348: r0 = true
    //     0xb95348: add             x0, NULL, #0x20  ; true
    // 0xb9534c: stur            x1, [fp, #-0x40]
    // 0xb95350: StoreField: r1->field_7 = r0
    //     0xb95350: stur            w0, [x1, #7]
    // 0xb95354: ldur            x0, [fp, #-0x20]
    // 0xb95358: StoreField: r1->field_23 = r0
    //     0xb95358: stur            w0, [x1, #0x23]
    // 0xb9535c: r0 = TextSpan()
    //     0xb9535c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb95360: mov             x3, x0
    // 0xb95364: ldur            x0, [fp, #-0x38]
    // 0xb95368: stur            x3, [fp, #-0x20]
    // 0xb9536c: StoreField: r3->field_b = r0
    //     0xb9536c: stur            w0, [x3, #0xb]
    // 0xb95370: r0 = Instance__DeferringMouseCursor
    //     0xb95370: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb95374: ArrayStore: r3[0] = r0  ; List_4
    //     0xb95374: stur            w0, [x3, #0x17]
    // 0xb95378: ldur            x1, [fp, #-0x40]
    // 0xb9537c: StoreField: r3->field_7 = r1
    //     0xb9537c: stur            w1, [x3, #7]
    // 0xb95380: r1 = Null
    //     0xb95380: mov             x1, NULL
    // 0xb95384: r2 = 4
    //     0xb95384: movz            x2, #0x4
    // 0xb95388: r0 = AllocateArray()
    //     0xb95388: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb9538c: stur            x0, [fp, #-0x38]
    // 0xb95390: r16 = Instance_TextSpan
    //     0xb95390: add             x16, PP, #0x35, lsl #12  ; [pp+0x35400] Obj!TextSpan@e1dc81
    //     0xb95394: ldr             x16, [x16, #0x400]
    // 0xb95398: StoreField: r0->field_f = r16
    //     0xb95398: stur            w16, [x0, #0xf]
    // 0xb9539c: ldur            x1, [fp, #-0x20]
    // 0xb953a0: StoreField: r0->field_13 = r1
    //     0xb953a0: stur            w1, [x0, #0x13]
    // 0xb953a4: r1 = <InlineSpan>
    //     0xb953a4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xb953a8: ldr             x1, [x1, #0x5f0]
    // 0xb953ac: r0 = AllocateGrowableArray()
    //     0xb953ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb953b0: mov             x1, x0
    // 0xb953b4: ldur            x0, [fp, #-0x38]
    // 0xb953b8: stur            x1, [fp, #-0x20]
    // 0xb953bc: StoreField: r1->field_f = r0
    //     0xb953bc: stur            w0, [x1, #0xf]
    // 0xb953c0: r0 = 4
    //     0xb953c0: movz            x0, #0x4
    // 0xb953c4: StoreField: r1->field_b = r0
    //     0xb953c4: stur            w0, [x1, #0xb]
    // 0xb953c8: r0 = TextSpan()
    //     0xb953c8: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb953cc: mov             x1, x0
    // 0xb953d0: ldur            x0, [fp, #-8]
    // 0xb953d4: stur            x1, [fp, #-0x38]
    // 0xb953d8: StoreField: r1->field_b = r0
    //     0xb953d8: stur            w0, [x1, #0xb]
    // 0xb953dc: ldur            x0, [fp, #-0x20]
    // 0xb953e0: StoreField: r1->field_f = r0
    //     0xb953e0: stur            w0, [x1, #0xf]
    // 0xb953e4: r0 = Instance__DeferringMouseCursor
    //     0xb953e4: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb953e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb953e8: stur            w0, [x1, #0x17]
    // 0xb953ec: r0 = GetNavigation.textTheme()
    //     0xb953ec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb953f0: LoadField: r1 = r0->field_27
    //     0xb953f0: ldur            w1, [x0, #0x27]
    // 0xb953f4: DecompressPointer r1
    //     0xb953f4: add             x1, x1, HEAP, lsl #32
    // 0xb953f8: cmp             w1, NULL
    // 0xb953fc: b.ne            #0xb95408
    // 0xb95400: r2 = Null
    //     0xb95400: mov             x2, NULL
    // 0xb95404: b               #0xb95424
    // 0xb95408: r16 = 12.000000
    //     0xb95408: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xb9540c: ldr             x16, [x16, #0xc60]
    // 0xb95410: str             x16, [SP]
    // 0xb95414: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb95414: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb95418: ldr             x4, [x4, #0x88]
    // 0xb9541c: r0 = copyWith()
    //     0xb9541c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95420: mov             x2, x0
    // 0xb95424: ldur            x1, [fp, #-0x10]
    // 0xb95428: ldur            x0, [fp, #-0x38]
    // 0xb9542c: stur            x2, [fp, #-8]
    // 0xb95430: r0 = Text()
    //     0xb95430: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95434: mov             x2, x0
    // 0xb95438: ldur            x0, [fp, #-0x38]
    // 0xb9543c: stur            x2, [fp, #-0x20]
    // 0xb95440: StoreField: r2->field_f = r0
    //     0xb95440: stur            w0, [x2, #0xf]
    // 0xb95444: ldur            x0, [fp, #-8]
    // 0xb95448: StoreField: r2->field_13 = r0
    //     0xb95448: stur            w0, [x2, #0x13]
    // 0xb9544c: ldur            x0, [fp, #-0x10]
    // 0xb95450: LoadField: r1 = r0->field_43
    //     0xb95450: ldur            w1, [x0, #0x43]
    // 0xb95454: DecompressPointer r1
    //     0xb95454: add             x1, x1, HEAP, lsl #32
    // 0xb95458: r0 = DateTimeExtensions.gregorian()
    //     0xb95458: bl              #0x81f220  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.gregorian
    // 0xb9545c: stur            x0, [fp, #-8]
    // 0xb95460: r0 = GetNavigation.textTheme()
    //     0xb95460: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95464: LoadField: r1 = r0->field_27
    //     0xb95464: ldur            w1, [x0, #0x27]
    // 0xb95468: DecompressPointer r1
    //     0xb95468: add             x1, x1, HEAP, lsl #32
    // 0xb9546c: cmp             w1, NULL
    // 0xb95470: b.ne            #0xb9547c
    // 0xb95474: r4 = Null
    //     0xb95474: mov             x4, NULL
    // 0xb95478: b               #0xb95498
    // 0xb9547c: r16 = 11.000000
    //     0xb9547c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1a8] 11
    //     0xb95480: ldr             x16, [x16, #0x1a8]
    // 0xb95484: str             x16, [SP]
    // 0xb95488: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb95488: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb9548c: ldr             x4, [x4, #0x88]
    // 0xb95490: r0 = copyWith()
    //     0xb95490: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95494: mov             x4, x0
    // 0xb95498: ldur            x3, [fp, #-0x18]
    // 0xb9549c: ldur            x2, [fp, #-0x28]
    // 0xb954a0: ldur            x1, [fp, #-0x20]
    // 0xb954a4: ldur            x0, [fp, #-8]
    // 0xb954a8: stur            x4, [fp, #-0x10]
    // 0xb954ac: r0 = Text()
    //     0xb954ac: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb954b0: mov             x3, x0
    // 0xb954b4: ldur            x0, [fp, #-8]
    // 0xb954b8: stur            x3, [fp, #-0x38]
    // 0xb954bc: StoreField: r3->field_b = r0
    //     0xb954bc: stur            w0, [x3, #0xb]
    // 0xb954c0: ldur            x0, [fp, #-0x10]
    // 0xb954c4: StoreField: r3->field_13 = r0
    //     0xb954c4: stur            w0, [x3, #0x13]
    // 0xb954c8: r1 = Null
    //     0xb954c8: mov             x1, NULL
    // 0xb954cc: r2 = 6
    //     0xb954cc: movz            x2, #0x6
    // 0xb954d0: r0 = AllocateArray()
    //     0xb954d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb954d4: mov             x2, x0
    // 0xb954d8: ldur            x0, [fp, #-0x28]
    // 0xb954dc: stur            x2, [fp, #-8]
    // 0xb954e0: StoreField: r2->field_f = r0
    //     0xb954e0: stur            w0, [x2, #0xf]
    // 0xb954e4: ldur            x0, [fp, #-0x20]
    // 0xb954e8: StoreField: r2->field_13 = r0
    //     0xb954e8: stur            w0, [x2, #0x13]
    // 0xb954ec: ldur            x0, [fp, #-0x38]
    // 0xb954f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb954f0: stur            w0, [x2, #0x17]
    // 0xb954f4: r1 = <Widget>
    //     0xb954f4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb954f8: r0 = AllocateGrowableArray()
    //     0xb954f8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb954fc: mov             x1, x0
    // 0xb95500: ldur            x0, [fp, #-8]
    // 0xb95504: stur            x1, [fp, #-0x10]
    // 0xb95508: StoreField: r1->field_f = r0
    //     0xb95508: stur            w0, [x1, #0xf]
    // 0xb9550c: r2 = 6
    //     0xb9550c: movz            x2, #0x6
    // 0xb95510: StoreField: r1->field_b = r2
    //     0xb95510: stur            w2, [x1, #0xb]
    // 0xb95514: r0 = Column()
    //     0xb95514: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb95518: mov             x2, x0
    // 0xb9551c: r0 = Instance_Axis
    //     0xb9551c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb95520: stur            x2, [fp, #-8]
    // 0xb95524: StoreField: r2->field_f = r0
    //     0xb95524: stur            w0, [x2, #0xf]
    // 0xb95528: r0 = Instance_MainAxisAlignment
    //     0xb95528: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb9552c: ldr             x0, [x0, #0x730]
    // 0xb95530: StoreField: r2->field_13 = r0
    //     0xb95530: stur            w0, [x2, #0x13]
    // 0xb95534: r1 = Instance_MainAxisSize
    //     0xb95534: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb95538: ldr             x1, [x1, #0xe88]
    // 0xb9553c: ArrayStore: r2[0] = r1  ; List_4
    //     0xb9553c: stur            w1, [x2, #0x17]
    // 0xb95540: r3 = Instance_CrossAxisAlignment
    //     0xb95540: add             x3, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb95544: ldr             x3, [x3, #0x68]
    // 0xb95548: StoreField: r2->field_1b = r3
    //     0xb95548: stur            w3, [x2, #0x1b]
    // 0xb9554c: r4 = Instance_VerticalDirection
    //     0xb9554c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb95550: ldr             x4, [x4, #0x748]
    // 0xb95554: StoreField: r2->field_23 = r4
    //     0xb95554: stur            w4, [x2, #0x23]
    // 0xb95558: r5 = Instance_Clip
    //     0xb95558: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb9555c: ldr             x5, [x5, #0x750]
    // 0xb95560: StoreField: r2->field_2b = r5
    //     0xb95560: stur            w5, [x2, #0x2b]
    // 0xb95564: StoreField: r2->field_2f = rZR
    //     0xb95564: stur            xzr, [x2, #0x2f]
    // 0xb95568: ldur            x1, [fp, #-0x10]
    // 0xb9556c: StoreField: r2->field_b = r1
    //     0xb9556c: stur            w1, [x2, #0xb]
    // 0xb95570: r1 = <FlexParentData>
    //     0xb95570: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb95574: ldr             x1, [x1, #0x720]
    // 0xb95578: r0 = Expanded()
    //     0xb95578: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9557c: mov             x3, x0
    // 0xb95580: r0 = 1
    //     0xb95580: movz            x0, #0x1
    // 0xb95584: stur            x3, [fp, #-0x10]
    // 0xb95588: StoreField: r3->field_13 = r0
    //     0xb95588: stur            x0, [x3, #0x13]
    // 0xb9558c: r0 = Instance_FlexFit
    //     0xb9558c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb95590: ldr             x0, [x0, #0x728]
    // 0xb95594: StoreField: r3->field_1b = r0
    //     0xb95594: stur            w0, [x3, #0x1b]
    // 0xb95598: ldur            x0, [fp, #-8]
    // 0xb9559c: StoreField: r3->field_b = r0
    //     0xb9559c: stur            w0, [x3, #0xb]
    // 0xb955a0: r1 = Null
    //     0xb955a0: mov             x1, NULL
    // 0xb955a4: r2 = 6
    //     0xb955a4: movz            x2, #0x6
    // 0xb955a8: r0 = AllocateArray()
    //     0xb955a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb955ac: mov             x2, x0
    // 0xb955b0: ldur            x0, [fp, #-0x18]
    // 0xb955b4: stur            x2, [fp, #-8]
    // 0xb955b8: StoreField: r2->field_f = r0
    //     0xb955b8: stur            w0, [x2, #0xf]
    // 0xb955bc: r16 = Instance_SizedBox
    //     0xb955bc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ae0] Obj!SizedBox@e1e241
    //     0xb955c0: ldr             x16, [x16, #0xae0]
    // 0xb955c4: StoreField: r2->field_13 = r16
    //     0xb955c4: stur            w16, [x2, #0x13]
    // 0xb955c8: ldur            x0, [fp, #-0x10]
    // 0xb955cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb955cc: stur            w0, [x2, #0x17]
    // 0xb955d0: r1 = <Widget>
    //     0xb955d0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb955d4: r0 = AllocateGrowableArray()
    //     0xb955d4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb955d8: mov             x1, x0
    // 0xb955dc: ldur            x0, [fp, #-8]
    // 0xb955e0: stur            x1, [fp, #-0x10]
    // 0xb955e4: StoreField: r1->field_f = r0
    //     0xb955e4: stur            w0, [x1, #0xf]
    // 0xb955e8: r0 = 6
    //     0xb955e8: movz            x0, #0x6
    // 0xb955ec: StoreField: r1->field_b = r0
    //     0xb955ec: stur            w0, [x1, #0xb]
    // 0xb955f0: r0 = Row()
    //     0xb955f0: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb955f4: mov             x1, x0
    // 0xb955f8: r0 = Instance_Axis
    //     0xb955f8: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb955fc: stur            x1, [fp, #-8]
    // 0xb95600: StoreField: r1->field_f = r0
    //     0xb95600: stur            w0, [x1, #0xf]
    // 0xb95604: r0 = Instance_MainAxisAlignment
    //     0xb95604: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb95608: ldr             x0, [x0, #0x730]
    // 0xb9560c: StoreField: r1->field_13 = r0
    //     0xb9560c: stur            w0, [x1, #0x13]
    // 0xb95610: r0 = Instance_MainAxisSize
    //     0xb95610: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb95614: ldr             x0, [x0, #0x738]
    // 0xb95618: ArrayStore: r1[0] = r0  ; List_4
    //     0xb95618: stur            w0, [x1, #0x17]
    // 0xb9561c: r0 = Instance_CrossAxisAlignment
    //     0xb9561c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb95620: ldr             x0, [x0, #0x68]
    // 0xb95624: StoreField: r1->field_1b = r0
    //     0xb95624: stur            w0, [x1, #0x1b]
    // 0xb95628: r0 = Instance_VerticalDirection
    //     0xb95628: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9562c: ldr             x0, [x0, #0x748]
    // 0xb95630: StoreField: r1->field_23 = r0
    //     0xb95630: stur            w0, [x1, #0x23]
    // 0xb95634: r0 = Instance_Clip
    //     0xb95634: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb95638: ldr             x0, [x0, #0x750]
    // 0xb9563c: StoreField: r1->field_2b = r0
    //     0xb9563c: stur            w0, [x1, #0x2b]
    // 0xb95640: StoreField: r1->field_2f = rZR
    //     0xb95640: stur            xzr, [x1, #0x2f]
    // 0xb95644: ldur            x0, [fp, #-0x10]
    // 0xb95648: StoreField: r1->field_b = r0
    //     0xb95648: stur            w0, [x1, #0xb]
    // 0xb9564c: r0 = Padding()
    //     0xb9564c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb95650: r1 = Instance_EdgeInsets
    //     0xb95650: add             x1, PP, #0x35, lsl #12  ; [pp+0x35408] Obj!EdgeInsets@e135d1
    //     0xb95654: ldr             x1, [x1, #0x408]
    // 0xb95658: StoreField: r0->field_f = r1
    //     0xb95658: stur            w1, [x0, #0xf]
    // 0xb9565c: ldur            x1, [fp, #-8]
    // 0xb95660: StoreField: r0->field_b = r1
    //     0xb95660: stur            w1, [x0, #0xb]
    // 0xb95664: LeaveFrame
    //     0xb95664: mov             SP, fp
    //     0xb95668: ldp             fp, lr, [SP], #0x10
    // 0xb9566c: ret
    //     0xb9566c: ret             
    // 0xb95670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb95670: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb95674: b               #0xb94fe8
    // 0xb95678: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb95678: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5021, size: 0x14, field offset: 0xc
//   const constructor, 
class TransactionHistory extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9443c, size: 0x12c
    // 0xb9443c: EnterFrame
    //     0xb9443c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94440: mov             fp, SP
    // 0xb94444: AllocStack(0x40)
    //     0xb94444: sub             SP, SP, #0x40
    // 0xb94448: SetupParameters(TransactionHistory this /* r1 => r1, fp-0x8 */)
    //     0xb94448: stur            x1, [fp, #-8]
    // 0xb9444c: CheckStackOverflow
    //     0xb9444c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94450: cmp             SP, x16
    //     0xb94454: b.ls            #0xb94560
    // 0xb94458: r1 = 1
    //     0xb94458: movz            x1, #0x1
    // 0xb9445c: r0 = AllocateContext()
    //     0xb9445c: bl              #0xec126c  ; AllocateContextStub
    // 0xb94460: mov             x1, x0
    // 0xb94464: ldur            x0, [fp, #-8]
    // 0xb94468: stur            x1, [fp, #-0x28]
    // 0xb9446c: StoreField: r1->field_f = r0
    //     0xb9446c: stur            w0, [x1, #0xf]
    // 0xb94470: LoadField: r3 = r0->field_b
    //     0xb94470: ldur            w3, [x0, #0xb]
    // 0xb94474: DecompressPointer r3
    //     0xb94474: add             x3, x3, HEAP, lsl #32
    // 0xb94478: stur            x3, [fp, #-0x20]
    // 0xb9447c: LoadField: r2 = r3->field_13
    //     0xb9447c: ldur            w2, [x3, #0x13]
    // 0xb94480: DecompressPointer r2
    //     0xb94480: add             x2, x2, HEAP, lsl #32
    // 0xb94484: stur            x2, [fp, #-0x18]
    // 0xb94488: LoadField: r4 = r0->field_f
    //     0xb94488: ldur            w4, [x0, #0xf]
    // 0xb9448c: DecompressPointer r4
    //     0xb9448c: add             x4, x4, HEAP, lsl #32
    // 0xb94490: stur            x4, [fp, #-0x10]
    // 0xb94494: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94494: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94498: ldr             x0, [x0, #0x2670]
    //     0xb9449c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb944a0: cmp             w0, w16
    //     0xb944a4: b.ne            #0xb944b0
    //     0xb944a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb944ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb944b0: r16 = <DonationRepository>
    //     0xb944b0: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xb944b4: ldr             x16, [x16, #0xb0]
    // 0xb944b8: r30 = "donation_repo"
    //     0xb944b8: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xb944bc: ldr             lr, [lr, #0xb8]
    // 0xb944c0: stp             lr, x16, [SP]
    // 0xb944c4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb944c4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb944c8: r0 = Inst.find()
    //     0xb944c8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb944cc: stur            x0, [fp, #-8]
    // 0xb944d0: r0 = TransactionHistoryController()
    //     0xb944d0: bl              #0x811f60  ; AllocateTransactionHistoryControllerStub -> TransactionHistoryController (size=0x4c)
    // 0xb944d4: stur            x0, [fp, #-0x30]
    // 0xb944d8: ldur            x16, [fp, #-0x10]
    // 0xb944dc: r30 = false
    //     0xb944dc: add             lr, NULL, #0x30  ; false
    // 0xb944e0: stp             lr, x16, [SP]
    // 0xb944e4: mov             x1, x0
    // 0xb944e8: ldur            x2, [fp, #-8]
    // 0xb944ec: ldur            x3, [fp, #-0x20]
    // 0xb944f0: r4 = const [0, 0x5, 0x2, 0x3, donationId, 0x3, infiniteScroll, 0x4, null]
    //     0xb944f0: add             x4, PP, #0x35, lsl #12  ; [pp+0x35338] List(9) [0, 0x5, 0x2, 0x3, "donationId", 0x3, "infiniteScroll", 0x4, Null]
    //     0xb944f4: ldr             x4, [x4, #0x338]
    // 0xb944f8: r0 = TransactionHistoryController()
    //     0xb944f8: bl              #0x811c3c  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::TransactionHistoryController
    // 0xb944fc: r1 = <TransactionHistoryController>
    //     0xb944fc: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af08] TypeArguments: <TransactionHistoryController>
    //     0xb94500: ldr             x1, [x1, #0xf08]
    // 0xb94504: r0 = GetBuilder()
    //     0xb94504: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb94508: mov             x3, x0
    // 0xb9450c: ldur            x0, [fp, #-0x30]
    // 0xb94510: stur            x3, [fp, #-8]
    // 0xb94514: StoreField: r3->field_3b = r0
    //     0xb94514: stur            w0, [x3, #0x3b]
    // 0xb94518: r0 = true
    //     0xb94518: add             x0, NULL, #0x20  ; true
    // 0xb9451c: StoreField: r3->field_13 = r0
    //     0xb9451c: stur            w0, [x3, #0x13]
    // 0xb94520: ldur            x2, [fp, #-0x28]
    // 0xb94524: r1 = Function '<anonymous closure>':.
    //     0xb94524: add             x1, PP, #0x35, lsl #12  ; [pp+0x35340] AnonymousClosure: (0xb94568), in [package:nuonline/app/modules/donation/widgets/transaction_history.dart] TransactionHistory::build (0xb9443c)
    //     0xb94528: ldr             x1, [x1, #0x340]
    // 0xb9452c: r0 = AllocateClosure()
    //     0xb9452c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94530: mov             x1, x0
    // 0xb94534: ldur            x0, [fp, #-8]
    // 0xb94538: StoreField: r0->field_f = r1
    //     0xb94538: stur            w1, [x0, #0xf]
    // 0xb9453c: r1 = true
    //     0xb9453c: add             x1, NULL, #0x20  ; true
    // 0xb94540: StoreField: r0->field_1f = r1
    //     0xb94540: stur            w1, [x0, #0x1f]
    // 0xb94544: r1 = false
    //     0xb94544: add             x1, NULL, #0x30  ; false
    // 0xb94548: StoreField: r0->field_23 = r1
    //     0xb94548: stur            w1, [x0, #0x23]
    // 0xb9454c: ldur            x1, [fp, #-0x18]
    // 0xb94550: StoreField: r0->field_1b = r1
    //     0xb94550: stur            w1, [x0, #0x1b]
    // 0xb94554: LeaveFrame
    //     0xb94554: mov             SP, fp
    //     0xb94558: ldp             fp, lr, [SP], #0x10
    // 0xb9455c: ret
    //     0xb9455c: ret             
    // 0xb94560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94564: b               #0xb94458
  }
  [closure] NSection <anonymous closure>(dynamic, TransactionHistoryController) {
    // ** addr: 0xb94568, size: 0x288
    // 0xb94568: EnterFrame
    //     0xb94568: stp             fp, lr, [SP, #-0x10]!
    //     0xb9456c: mov             fp, SP
    // 0xb94570: AllocStack(0x38)
    //     0xb94570: sub             SP, SP, #0x38
    // 0xb94574: SetupParameters()
    //     0xb94574: ldr             x0, [fp, #0x18]
    //     0xb94578: ldur            w1, [x0, #0x17]
    //     0xb9457c: add             x1, x1, HEAP, lsl #32
    //     0xb94580: stur            x1, [fp, #-8]
    // 0xb94584: CheckStackOverflow
    //     0xb94584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94588: cmp             SP, x16
    //     0xb9458c: b.ls            #0xb947e8
    // 0xb94590: r1 = 1
    //     0xb94590: movz            x1, #0x1
    // 0xb94594: r0 = AllocateContext()
    //     0xb94594: bl              #0xec126c  ; AllocateContextStub
    // 0xb94598: mov             x3, x0
    // 0xb9459c: ldur            x0, [fp, #-8]
    // 0xb945a0: stur            x3, [fp, #-0x10]
    // 0xb945a4: StoreField: r3->field_b = r0
    //     0xb945a4: stur            w0, [x3, #0xb]
    // 0xb945a8: ldr             x1, [fp, #0x10]
    // 0xb945ac: StoreField: r3->field_f = r1
    //     0xb945ac: stur            w1, [x3, #0xf]
    // 0xb945b0: r1 = Null
    //     0xb945b0: mov             x1, NULL
    // 0xb945b4: r2 = 4
    //     0xb945b4: movz            x2, #0x4
    // 0xb945b8: r0 = AllocateArray()
    //     0xb945b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb945bc: r16 = "Riwayat "
    //     0xb945bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fbc0] "Riwayat "
    //     0xb945c0: ldr             x16, [x16, #0xbc0]
    // 0xb945c4: StoreField: r0->field_f = r16
    //     0xb945c4: stur            w16, [x0, #0xf]
    // 0xb945c8: ldur            x1, [fp, #-8]
    // 0xb945cc: LoadField: r2 = r1->field_f
    //     0xb945cc: ldur            w2, [x1, #0xf]
    // 0xb945d0: DecompressPointer r2
    //     0xb945d0: add             x2, x2, HEAP, lsl #32
    // 0xb945d4: LoadField: r3 = r2->field_b
    //     0xb945d4: ldur            w3, [x2, #0xb]
    // 0xb945d8: DecompressPointer r3
    //     0xb945d8: add             x3, x3, HEAP, lsl #32
    // 0xb945dc: LoadField: r2 = r3->field_13
    //     0xb945dc: ldur            w2, [x3, #0x13]
    // 0xb945e0: DecompressPointer r2
    //     0xb945e0: add             x2, x2, HEAP, lsl #32
    // 0xb945e4: StoreField: r0->field_13 = r2
    //     0xb945e4: stur            w2, [x0, #0x13]
    // 0xb945e8: str             x0, [SP]
    // 0xb945ec: r0 = _interpolate()
    //     0xb945ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb945f0: mov             x1, x0
    // 0xb945f4: ldur            x0, [fp, #-8]
    // 0xb945f8: stur            x1, [fp, #-0x20]
    // 0xb945fc: LoadField: r2 = r0->field_f
    //     0xb945fc: ldur            w2, [x0, #0xf]
    // 0xb94600: DecompressPointer r2
    //     0xb94600: add             x2, x2, HEAP, lsl #32
    // 0xb94604: LoadField: r0 = r2->field_b
    //     0xb94604: ldur            w0, [x2, #0xb]
    // 0xb94608: DecompressPointer r0
    //     0xb94608: add             x0, x0, HEAP, lsl #32
    // 0xb9460c: ldur            x2, [fp, #-0x10]
    // 0xb94610: stur            x0, [fp, #-0x18]
    // 0xb94614: LoadField: r3 = r2->field_f
    //     0xb94614: ldur            w3, [x2, #0xf]
    // 0xb94618: DecompressPointer r3
    //     0xb94618: add             x3, x3, HEAP, lsl #32
    // 0xb9461c: stur            x3, [fp, #-8]
    // 0xb94620: r0 = Obx()
    //     0xb94620: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb94624: ldur            x2, [fp, #-0x10]
    // 0xb94628: r1 = Function '<anonymous closure>':.
    //     0xb94628: add             x1, PP, #0x35, lsl #12  ; [pp+0x35348] AnonymousClosure: (0xb94d8c), in [package:nuonline/app/modules/donation/widgets/transaction_history.dart] TransactionHistory::build (0xb9443c)
    //     0xb9462c: ldr             x1, [x1, #0x348]
    // 0xb94630: stur            x0, [fp, #-0x28]
    // 0xb94634: r0 = AllocateClosure()
    //     0xb94634: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94638: mov             x1, x0
    // 0xb9463c: ldur            x0, [fp, #-0x28]
    // 0xb94640: StoreField: r0->field_b = r1
    //     0xb94640: stur            w1, [x0, #0xb]
    // 0xb94644: r0 = InkWell()
    //     0xb94644: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb94648: mov             x3, x0
    // 0xb9464c: ldur            x0, [fp, #-0x28]
    // 0xb94650: stur            x3, [fp, #-0x30]
    // 0xb94654: StoreField: r3->field_b = r0
    //     0xb94654: stur            w0, [x3, #0xb]
    // 0xb94658: ldur            x2, [fp, #-8]
    // 0xb9465c: r1 = Function 'selectOrderBy':.
    //     0xb9465c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fbf8] AnonymousClosure: (0xae6c34), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::selectOrderBy (0xae6c6c)
    //     0xb94660: ldr             x1, [x1, #0xbf8]
    // 0xb94664: r0 = AllocateClosure()
    //     0xb94664: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94668: mov             x1, x0
    // 0xb9466c: ldur            x0, [fp, #-0x30]
    // 0xb94670: StoreField: r0->field_f = r1
    //     0xb94670: stur            w1, [x0, #0xf]
    // 0xb94674: r1 = true
    //     0xb94674: add             x1, NULL, #0x20  ; true
    // 0xb94678: StoreField: r0->field_43 = r1
    //     0xb94678: stur            w1, [x0, #0x43]
    // 0xb9467c: r2 = Instance_BoxShape
    //     0xb9467c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb94680: ldr             x2, [x2, #0xca8]
    // 0xb94684: StoreField: r0->field_47 = r2
    //     0xb94684: stur            w2, [x0, #0x47]
    // 0xb94688: StoreField: r0->field_6f = r1
    //     0xb94688: stur            w1, [x0, #0x6f]
    // 0xb9468c: r2 = false
    //     0xb9468c: add             x2, NULL, #0x30  ; false
    // 0xb94690: StoreField: r0->field_73 = r2
    //     0xb94690: stur            w2, [x0, #0x73]
    // 0xb94694: StoreField: r0->field_83 = r1
    //     0xb94694: stur            w1, [x0, #0x83]
    // 0xb94698: StoreField: r0->field_7b = r2
    //     0xb94698: stur            w2, [x0, #0x7b]
    // 0xb9469c: ldur            x2, [fp, #-0x18]
    // 0xb946a0: r16 = Instance_PaymentType
    //     0xb946a0: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb946a4: ldr             x16, [x16, #0x5a8]
    // 0xb946a8: cmp             w2, w16
    // 0xb946ac: b.ne            #0xb946b8
    // 0xb946b0: mov             x2, x0
    // 0xb946b4: b               #0xb946bc
    // 0xb946b8: r2 = Null
    //     0xb946b8: mov             x2, NULL
    // 0xb946bc: ldur            x0, [fp, #-8]
    // 0xb946c0: stur            x2, [fp, #-0x18]
    // 0xb946c4: r0 = Obx()
    //     0xb946c4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb946c8: ldur            x2, [fp, #-0x10]
    // 0xb946cc: r1 = Function '<anonymous closure>':.
    //     0xb946cc: add             x1, PP, #0x35, lsl #12  ; [pp+0x35350] AnonymousClosure: (0xb94b1c), in [package:nuonline/app/modules/donation/widgets/transaction_history.dart] TransactionHistory::build (0xb9443c)
    //     0xb946d0: ldr             x1, [x1, #0x350]
    // 0xb946d4: stur            x0, [fp, #-0x28]
    // 0xb946d8: r0 = AllocateClosure()
    //     0xb946d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb946dc: mov             x1, x0
    // 0xb946e0: ldur            x0, [fp, #-0x28]
    // 0xb946e4: StoreField: r0->field_b = r1
    //     0xb946e4: stur            w1, [x0, #0xb]
    // 0xb946e8: r1 = Null
    //     0xb946e8: mov             x1, NULL
    // 0xb946ec: r2 = 4
    //     0xb946ec: movz            x2, #0x4
    // 0xb946f0: r0 = AllocateArray()
    //     0xb946f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb946f4: stur            x0, [fp, #-0x30]
    // 0xb946f8: r16 = Instance_SizedBox
    //     0xb946f8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb946fc: ldr             x16, [x16, #0xfb0]
    // 0xb94700: StoreField: r0->field_f = r16
    //     0xb94700: stur            w16, [x0, #0xf]
    // 0xb94704: ldur            x1, [fp, #-0x28]
    // 0xb94708: StoreField: r0->field_13 = r1
    //     0xb94708: stur            w1, [x0, #0x13]
    // 0xb9470c: r1 = <Widget>
    //     0xb9470c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb94710: r0 = AllocateGrowableArray()
    //     0xb94710: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb94714: mov             x1, x0
    // 0xb94718: ldur            x0, [fp, #-0x30]
    // 0xb9471c: stur            x1, [fp, #-0x28]
    // 0xb94720: StoreField: r1->field_f = r0
    //     0xb94720: stur            w0, [x1, #0xf]
    // 0xb94724: r0 = 4
    //     0xb94724: movz            x0, #0x4
    // 0xb94728: StoreField: r1->field_b = r0
    //     0xb94728: stur            w0, [x1, #0xb]
    // 0xb9472c: ldur            x0, [fp, #-8]
    // 0xb94730: LoadField: r2 = r0->field_43
    //     0xb94730: ldur            w2, [x0, #0x43]
    // 0xb94734: DecompressPointer r2
    //     0xb94734: add             x2, x2, HEAP, lsl #32
    // 0xb94738: tbz             w2, #4, #0xb947a8
    // 0xb9473c: r0 = Obx()
    //     0xb9473c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb94740: ldur            x2, [fp, #-0x10]
    // 0xb94744: r1 = Function '<anonymous closure>':.
    //     0xb94744: add             x1, PP, #0x35, lsl #12  ; [pp+0x35358] AnonymousClosure: (0xb947f0), in [package:nuonline/app/modules/donation/widgets/transaction_history.dart] TransactionHistory::build (0xb9443c)
    //     0xb94748: ldr             x1, [x1, #0x358]
    // 0xb9474c: stur            x0, [fp, #-8]
    // 0xb94750: r0 = AllocateClosure()
    //     0xb94750: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94754: mov             x1, x0
    // 0xb94758: ldur            x0, [fp, #-8]
    // 0xb9475c: StoreField: r0->field_b = r1
    //     0xb9475c: stur            w1, [x0, #0xb]
    // 0xb94760: ldur            x1, [fp, #-0x28]
    // 0xb94764: r0 = _growToNextCapacity()
    //     0xb94764: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb94768: ldur            x2, [fp, #-0x28]
    // 0xb9476c: r0 = 6
    //     0xb9476c: movz            x0, #0x6
    // 0xb94770: StoreField: r2->field_b = r0
    //     0xb94770: stur            w0, [x2, #0xb]
    // 0xb94774: LoadField: r1 = r2->field_f
    //     0xb94774: ldur            w1, [x2, #0xf]
    // 0xb94778: DecompressPointer r1
    //     0xb94778: add             x1, x1, HEAP, lsl #32
    // 0xb9477c: ldur            x0, [fp, #-8]
    // 0xb94780: ArrayStore: r1[2] = r0  ; List_4
    //     0xb94780: add             x25, x1, #0x17
    //     0xb94784: str             w0, [x25]
    //     0xb94788: tbz             w0, #0, #0xb947a4
    //     0xb9478c: ldurb           w16, [x1, #-1]
    //     0xb94790: ldurb           w17, [x0, #-1]
    //     0xb94794: and             x16, x17, x16, lsr #2
    //     0xb94798: tst             x16, HEAP, lsr #32
    //     0xb9479c: b.eq            #0xb947a4
    //     0xb947a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb947a4: b               #0xb947ac
    // 0xb947a8: mov             x2, x1
    // 0xb947ac: ldur            x1, [fp, #-0x20]
    // 0xb947b0: ldur            x0, [fp, #-0x18]
    // 0xb947b4: r0 = NSection()
    //     0xb947b4: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb947b8: ldur            x1, [fp, #-0x20]
    // 0xb947bc: StoreField: r0->field_b = r1
    //     0xb947bc: stur            w1, [x0, #0xb]
    // 0xb947c0: ldur            x1, [fp, #-0x28]
    // 0xb947c4: StoreField: r0->field_f = r1
    //     0xb947c4: stur            w1, [x0, #0xf]
    // 0xb947c8: r1 = true
    //     0xb947c8: add             x1, NULL, #0x20  ; true
    // 0xb947cc: StoreField: r0->field_27 = r1
    //     0xb947cc: stur            w1, [x0, #0x27]
    // 0xb947d0: StoreField: r0->field_2b = r1
    //     0xb947d0: stur            w1, [x0, #0x2b]
    // 0xb947d4: ldur            x1, [fp, #-0x18]
    // 0xb947d8: StoreField: r0->field_2f = r1
    //     0xb947d8: stur            w1, [x0, #0x2f]
    // 0xb947dc: LeaveFrame
    //     0xb947dc: mov             SP, fp
    //     0xb947e0: ldp             fp, lr, [SP], #0x10
    // 0xb947e4: ret
    //     0xb947e4: ret             
    // 0xb947e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb947e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb947ec: b               #0xb94590
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb947f0, size: 0x204
    // 0xb947f0: EnterFrame
    //     0xb947f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb947f4: mov             fp, SP
    // 0xb947f8: AllocStack(0x40)
    //     0xb947f8: sub             SP, SP, #0x40
    // 0xb947fc: SetupParameters()
    //     0xb947fc: ldr             x0, [fp, #0x10]
    //     0xb94800: ldur            w2, [x0, #0x17]
    //     0xb94804: add             x2, x2, HEAP, lsl #32
    //     0xb94808: stur            x2, [fp, #-8]
    // 0xb9480c: CheckStackOverflow
    //     0xb9480c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94810: cmp             SP, x16
    //     0xb94814: b.ls            #0xb949ec
    // 0xb94818: LoadField: r0 = r2->field_f
    //     0xb94818: ldur            w0, [x2, #0xf]
    // 0xb9481c: DecompressPointer r0
    //     0xb9481c: add             x0, x0, HEAP, lsl #32
    // 0xb94820: LoadField: r1 = r0->field_2f
    //     0xb94820: ldur            w1, [x0, #0x2f]
    // 0xb94824: DecompressPointer r1
    //     0xb94824: add             x1, x1, HEAP, lsl #32
    // 0xb94828: r0 = value()
    //     0xb94828: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb9482c: r1 = LoadClassIdInstr(r0)
    //     0xb9482c: ldur            x1, [x0, #-1]
    //     0xb94830: ubfx            x1, x1, #0xc, #0x14
    // 0xb94834: str             x0, [SP]
    // 0xb94838: mov             x0, x1
    // 0xb9483c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb9483c: movz            x17, #0xc834
    //     0xb94840: add             lr, x0, x17
    //     0xb94844: ldr             lr, [x21, lr, lsl #3]
    //     0xb94848: blr             lr
    // 0xb9484c: stur            x0, [fp, #-0x10]
    // 0xb94850: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94850: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94854: ldr             x0, [x0, #0x2670]
    //     0xb94858: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9485c: cmp             w0, w16
    //     0xb94860: b.ne            #0xb9486c
    //     0xb94864: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb94868: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb9486c: r0 = GetNavigation.theme()
    //     0xb9486c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb94870: LoadField: r1 = r0->field_3f
    //     0xb94870: ldur            w1, [x0, #0x3f]
    // 0xb94874: DecompressPointer r1
    //     0xb94874: add             x1, x1, HEAP, lsl #32
    // 0xb94878: LoadField: r0 = r1->field_2b
    //     0xb94878: ldur            w0, [x1, #0x2b]
    // 0xb9487c: DecompressPointer r0
    //     0xb9487c: add             x0, x0, HEAP, lsl #32
    // 0xb94880: stur            x0, [fp, #-0x18]
    // 0xb94884: r0 = GetNavigation.textTheme()
    //     0xb94884: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb94888: LoadField: r1 = r0->field_f
    //     0xb94888: ldur            w1, [x0, #0xf]
    // 0xb9488c: DecompressPointer r1
    //     0xb9488c: add             x1, x1, HEAP, lsl #32
    // 0xb94890: cmp             w1, NULL
    // 0xb94894: b.ne            #0xb948a0
    // 0xb94898: r1 = Null
    //     0xb94898: mov             x1, NULL
    // 0xb9489c: b               #0xb948bc
    // 0xb948a0: r16 = 16.000000
    //     0xb948a0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb948a4: ldr             x16, [x16, #0x80]
    // 0xb948a8: str             x16, [SP]
    // 0xb948ac: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb948ac: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb948b0: ldr             x4, [x4, #0x88]
    // 0xb948b4: r0 = copyWith()
    //     0xb948b4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb948b8: mov             x1, x0
    // 0xb948bc: ldur            x0, [fp, #-0x10]
    // 0xb948c0: ldur            x16, [fp, #-0x18]
    // 0xb948c4: r30 = Instance_BorderSide
    //     0xb948c4: add             lr, PP, #0x23, lsl #12  ; [pp+0x23ca0] Obj!BorderSide@e1c111
    //     0xb948c8: ldr             lr, [lr, #0xca0]
    // 0xb948cc: stp             lr, x16, [SP, #0x10]
    // 0xb948d0: r16 = Instance__NoSplashFactory
    //     0xb948d0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35170] Obj!_NoSplashFactory@e14771
    //     0xb948d4: ldr             x16, [x16, #0x170]
    // 0xb948d8: stp             x1, x16, [SP]
    // 0xb948dc: r4 = const [0, 0x4, 0x4, 0, foregroundColor, 0, side, 0x1, splashFactory, 0x2, textStyle, 0x3, null]
    //     0xb948dc: add             x4, PP, #0x35, lsl #12  ; [pp+0x35360] List(13) [0, 0x4, 0x4, 0, "foregroundColor", 0, "side", 0x1, "splashFactory", 0x2, "textStyle", 0x3, Null]
    //     0xb948e0: ldr             x4, [x4, #0x360]
    // 0xb948e4: r0 = styleFrom()
    //     0xb948e4: bl              #0xaee9e8  ; [package:flutter/src/material/outlined_button.dart] OutlinedButton::styleFrom
    // 0xb948e8: ldur            x2, [fp, #-8]
    // 0xb948ec: r1 = Function '<anonymous closure>':.
    //     0xb948ec: add             x1, PP, #0x35, lsl #12  ; [pp+0x35368] AnonymousClosure: (0xb949f4), in [package:nuonline/app/modules/donation/widgets/transaction_history.dart] TransactionHistory::build (0xb9443c)
    //     0xb948f0: ldr             x1, [x1, #0x368]
    // 0xb948f4: stur            x0, [fp, #-8]
    // 0xb948f8: r0 = AllocateClosure()
    //     0xb948f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb948fc: stur            x0, [fp, #-0x18]
    // 0xb94900: r0 = OutlinedButton()
    //     0xb94900: bl              #0xa3b670  ; AllocateOutlinedButtonStub -> OutlinedButton (size=0x3c)
    // 0xb94904: mov             x3, x0
    // 0xb94908: ldur            x0, [fp, #-0x18]
    // 0xb9490c: stur            x3, [fp, #-0x20]
    // 0xb94910: StoreField: r3->field_b = r0
    //     0xb94910: stur            w0, [x3, #0xb]
    // 0xb94914: ldur            x0, [fp, #-8]
    // 0xb94918: StoreField: r3->field_1b = r0
    //     0xb94918: stur            w0, [x3, #0x1b]
    // 0xb9491c: r0 = false
    //     0xb9491c: add             x0, NULL, #0x30  ; false
    // 0xb94920: StoreField: r3->field_27 = r0
    //     0xb94920: stur            w0, [x3, #0x27]
    // 0xb94924: r0 = true
    //     0xb94924: add             x0, NULL, #0x20  ; true
    // 0xb94928: StoreField: r3->field_2f = r0
    //     0xb94928: stur            w0, [x3, #0x2f]
    // 0xb9492c: r0 = Instance_Text
    //     0xb9492c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35370] Obj!Text@e23b21
    //     0xb94930: ldr             x0, [x0, #0x370]
    // 0xb94934: StoreField: r3->field_37 = r0
    //     0xb94934: stur            w0, [x3, #0x37]
    // 0xb94938: r1 = Null
    //     0xb94938: mov             x1, NULL
    // 0xb9493c: r2 = 4
    //     0xb9493c: movz            x2, #0x4
    // 0xb94940: r0 = AllocateArray()
    //     0xb94940: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb94944: stur            x0, [fp, #-8]
    // 0xb94948: r16 = Instance_Divider
    //     0xb94948: add             x16, PP, #0x35, lsl #12  ; [pp+0x35378] Obj!Divider@e25821
    //     0xb9494c: ldr             x16, [x16, #0x378]
    // 0xb94950: StoreField: r0->field_f = r16
    //     0xb94950: stur            w16, [x0, #0xf]
    // 0xb94954: ldur            x1, [fp, #-0x20]
    // 0xb94958: StoreField: r0->field_13 = r1
    //     0xb94958: stur            w1, [x0, #0x13]
    // 0xb9495c: r1 = <Widget>
    //     0xb9495c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb94960: r0 = AllocateGrowableArray()
    //     0xb94960: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb94964: mov             x1, x0
    // 0xb94968: ldur            x0, [fp, #-8]
    // 0xb9496c: stur            x1, [fp, #-0x18]
    // 0xb94970: StoreField: r1->field_f = r0
    //     0xb94970: stur            w0, [x1, #0xf]
    // 0xb94974: r0 = 4
    //     0xb94974: movz            x0, #0x4
    // 0xb94978: StoreField: r1->field_b = r0
    //     0xb94978: stur            w0, [x1, #0xb]
    // 0xb9497c: r0 = Column()
    //     0xb9497c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb94980: r1 = Instance_Axis
    //     0xb94980: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb94984: StoreField: r0->field_f = r1
    //     0xb94984: stur            w1, [x0, #0xf]
    // 0xb94988: r1 = Instance_MainAxisAlignment
    //     0xb94988: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb9498c: ldr             x1, [x1, #0x730]
    // 0xb94990: StoreField: r0->field_13 = r1
    //     0xb94990: stur            w1, [x0, #0x13]
    // 0xb94994: r1 = Instance_MainAxisSize
    //     0xb94994: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb94998: ldr             x1, [x1, #0x738]
    // 0xb9499c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb9499c: stur            w1, [x0, #0x17]
    // 0xb949a0: r1 = Instance_CrossAxisAlignment
    //     0xb949a0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb949a4: ldr             x1, [x1, #0x740]
    // 0xb949a8: StoreField: r0->field_1b = r1
    //     0xb949a8: stur            w1, [x0, #0x1b]
    // 0xb949ac: r1 = Instance_VerticalDirection
    //     0xb949ac: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb949b0: ldr             x1, [x1, #0x748]
    // 0xb949b4: StoreField: r0->field_23 = r1
    //     0xb949b4: stur            w1, [x0, #0x23]
    // 0xb949b8: r1 = Instance_Clip
    //     0xb949b8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb949bc: ldr             x1, [x1, #0x750]
    // 0xb949c0: StoreField: r0->field_2b = r1
    //     0xb949c0: stur            w1, [x0, #0x2b]
    // 0xb949c4: StoreField: r0->field_2f = rZR
    //     0xb949c4: stur            xzr, [x0, #0x2f]
    // 0xb949c8: ldur            x1, [fp, #-0x18]
    // 0xb949cc: StoreField: r0->field_b = r1
    //     0xb949cc: stur            w1, [x0, #0xb]
    // 0xb949d0: ldur            x1, [fp, #-0x10]
    // 0xb949d4: cbnz            w1, #0xb949e0
    // 0xb949d8: r0 = Instance_SizedBox
    //     0xb949d8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb949dc: ldr             x0, [x0, #0xc40]
    // 0xb949e0: LeaveFrame
    //     0xb949e0: mov             SP, fp
    //     0xb949e4: ldp             fp, lr, [SP], #0x10
    // 0xb949e8: ret
    //     0xb949e8: ret             
    // 0xb949ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb949ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb949f0: b               #0xb94818
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb949f4, size: 0x128
    // 0xb949f4: EnterFrame
    //     0xb949f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb949f8: mov             fp, SP
    // 0xb949fc: AllocStack(0x20)
    //     0xb949fc: sub             SP, SP, #0x20
    // 0xb94a00: SetupParameters()
    //     0xb94a00: ldr             x0, [fp, #0x10]
    //     0xb94a04: ldur            w1, [x0, #0x17]
    //     0xb94a08: add             x1, x1, HEAP, lsl #32
    //     0xb94a0c: stur            x1, [fp, #-8]
    // 0xb94a10: CheckStackOverflow
    //     0xb94a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94a14: cmp             SP, x16
    //     0xb94a18: b.ls            #0xb94b14
    // 0xb94a1c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94a1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94a20: ldr             x0, [x0, #0x2670]
    //     0xb94a24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb94a28: cmp             w0, w16
    //     0xb94a2c: b.ne            #0xb94a38
    //     0xb94a30: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb94a34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb94a38: ldur            x0, [fp, #-8]
    // 0xb94a3c: LoadField: r1 = r0->field_f
    //     0xb94a3c: ldur            w1, [x0, #0xf]
    // 0xb94a40: DecompressPointer r1
    //     0xb94a40: add             x1, x1, HEAP, lsl #32
    // 0xb94a44: LoadField: r2 = r1->field_37
    //     0xb94a44: ldur            w2, [x1, #0x37]
    // 0xb94a48: DecompressPointer r2
    //     0xb94a48: add             x2, x2, HEAP, lsl #32
    // 0xb94a4c: r16 = Instance_PaymentType
    //     0xb94a4c: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xb94a50: ldr             x16, [x16, #0x5c0]
    // 0xb94a54: cmp             w2, w16
    // 0xb94a58: b.ne            #0xb94a68
    // 0xb94a5c: r1 = "/donation/zakat-history"
    //     0xb94a5c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35380] "/donation/zakat-history"
    //     0xb94a60: ldr             x1, [x1, #0x380]
    // 0xb94a64: b               #0xb94adc
    // 0xb94a68: r16 = Instance_PaymentType
    //     0xb94a68: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb94a6c: ldr             x16, [x16, #0x5a8]
    // 0xb94a70: cmp             w2, w16
    // 0xb94a74: b.ne            #0xb94a84
    // 0xb94a78: r1 = "/donation/donation-history"
    //     0xb94a78: add             x1, PP, #0x35, lsl #12  ; [pp+0x35388] "/donation/donation-history"
    //     0xb94a7c: ldr             x1, [x1, #0x388]
    // 0xb94a80: b               #0xb94adc
    // 0xb94a84: r16 = Instance_PaymentType
    //     0xb94a84: add             x16, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0xb94a88: ldr             x16, [x16, #0x5d8]
    // 0xb94a8c: cmp             w2, w16
    // 0xb94a90: b.ne            #0xb94aa0
    // 0xb94a94: r1 = "/donation/campaign-history"
    //     0xb94a94: add             x1, PP, #0x35, lsl #12  ; [pp+0x35390] "/donation/campaign-history"
    //     0xb94a98: ldr             x1, [x1, #0x390]
    // 0xb94a9c: b               #0xb94adc
    // 0xb94aa0: r16 = Instance_PaymentType
    //     0xb94aa0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xb94aa4: ldr             x16, [x16, #0x608]
    // 0xb94aa8: cmp             w2, w16
    // 0xb94aac: b.ne            #0xb94abc
    // 0xb94ab0: r1 = "/donation/koin-nu-history"
    //     0xb94ab0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35398] "/donation/koin-nu-history"
    //     0xb94ab4: ldr             x1, [x1, #0x398]
    // 0xb94ab8: b               #0xb94adc
    // 0xb94abc: r16 = Instance_PaymentType
    //     0xb94abc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xb94ac0: ldr             x16, [x16, #0x638]
    // 0xb94ac4: cmp             w2, w16
    // 0xb94ac8: b.ne            #0xb94ad8
    // 0xb94acc: r1 = "/donation/fidyah-history"
    //     0xb94acc: add             x1, PP, #0x35, lsl #12  ; [pp+0x353a0] "/donation/fidyah-history"
    //     0xb94ad0: ldr             x1, [x1, #0x3a0]
    // 0xb94ad4: b               #0xb94adc
    // 0xb94ad8: r1 = ""
    //     0xb94ad8: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb94adc: LoadField: r2 = r0->field_b
    //     0xb94adc: ldur            w2, [x0, #0xb]
    // 0xb94ae0: DecompressPointer r2
    //     0xb94ae0: add             x2, x2, HEAP, lsl #32
    // 0xb94ae4: LoadField: r0 = r2->field_f
    //     0xb94ae4: ldur            w0, [x2, #0xf]
    // 0xb94ae8: DecompressPointer r0
    //     0xb94ae8: add             x0, x0, HEAP, lsl #32
    // 0xb94aec: LoadField: r2 = r0->field_f
    //     0xb94aec: ldur            w2, [x0, #0xf]
    // 0xb94af0: DecompressPointer r2
    //     0xb94af0: add             x2, x2, HEAP, lsl #32
    // 0xb94af4: stp             x1, NULL, [SP, #8]
    // 0xb94af8: str             x2, [SP]
    // 0xb94afc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb94afc: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb94b00: ldr             x4, [x4, #0x478]
    // 0xb94b04: r0 = GetNavigation.toNamed()
    //     0xb94b04: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb94b08: LeaveFrame
    //     0xb94b08: mov             SP, fp
    //     0xb94b0c: ldp             fp, lr, [SP], #0x10
    // 0xb94b10: ret
    //     0xb94b10: ret             
    // 0xb94b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94b18: b               #0xb94a1c
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb94b1c, size: 0x270
    // 0xb94b1c: EnterFrame
    //     0xb94b1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94b20: mov             fp, SP
    // 0xb94b24: AllocStack(0x48)
    //     0xb94b24: sub             SP, SP, #0x48
    // 0xb94b28: SetupParameters()
    //     0xb94b28: ldr             x0, [fp, #0x10]
    //     0xb94b2c: ldur            w2, [x0, #0x17]
    //     0xb94b30: add             x2, x2, HEAP, lsl #32
    //     0xb94b34: stur            x2, [fp, #-8]
    // 0xb94b38: CheckStackOverflow
    //     0xb94b38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94b3c: cmp             SP, x16
    //     0xb94b40: b.ls            #0xb94d68
    // 0xb94b44: LoadField: r0 = r2->field_f
    //     0xb94b44: ldur            w0, [x2, #0xf]
    // 0xb94b48: DecompressPointer r0
    //     0xb94b48: add             x0, x0, HEAP, lsl #32
    // 0xb94b4c: LoadField: r1 = r0->field_2b
    //     0xb94b4c: ldur            w1, [x0, #0x2b]
    // 0xb94b50: DecompressPointer r1
    //     0xb94b50: add             x1, x1, HEAP, lsl #32
    // 0xb94b54: r0 = value()
    //     0xb94b54: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb94b58: tbnz            w0, #4, #0xb94ce0
    // 0xb94b5c: ldur            x2, [fp, #-8]
    // 0xb94b60: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94b60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94b64: ldr             x0, [x0, #0x2670]
    //     0xb94b68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb94b6c: cmp             w0, w16
    //     0xb94b70: b.ne            #0xb94b7c
    //     0xb94b74: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb94b78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb94b7c: r0 = GetNavigation.width()
    //     0xb94b7c: bl              #0x7daa60  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb94b80: mov             v1.16b, v0.16b
    // 0xb94b84: d0 = 2.000000
    //     0xb94b84: fmov            d0, #2.00000000
    // 0xb94b88: fdiv            d2, d1, d0
    // 0xb94b8c: stur            d2, [fp, #-0x30]
    // 0xb94b90: r1 = Null
    //     0xb94b90: mov             x1, NULL
    // 0xb94b94: r2 = 4
    //     0xb94b94: movz            x2, #0x4
    // 0xb94b98: r0 = AllocateArray()
    //     0xb94b98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb94b9c: r16 = "Belum ada riwayat "
    //     0xb94b9c: add             x16, PP, #0x35, lsl #12  ; [pp+0x353a8] "Belum ada riwayat "
    //     0xb94ba0: ldr             x16, [x16, #0x3a8]
    // 0xb94ba4: StoreField: r0->field_f = r16
    //     0xb94ba4: stur            w16, [x0, #0xf]
    // 0xb94ba8: ldur            x2, [fp, #-8]
    // 0xb94bac: LoadField: r1 = r2->field_b
    //     0xb94bac: ldur            w1, [x2, #0xb]
    // 0xb94bb0: DecompressPointer r1
    //     0xb94bb0: add             x1, x1, HEAP, lsl #32
    // 0xb94bb4: LoadField: r2 = r1->field_f
    //     0xb94bb4: ldur            w2, [x1, #0xf]
    // 0xb94bb8: DecompressPointer r2
    //     0xb94bb8: add             x2, x2, HEAP, lsl #32
    // 0xb94bbc: LoadField: r1 = r2->field_b
    //     0xb94bbc: ldur            w1, [x2, #0xb]
    // 0xb94bc0: DecompressPointer r1
    //     0xb94bc0: add             x1, x1, HEAP, lsl #32
    // 0xb94bc4: LoadField: r2 = r1->field_13
    //     0xb94bc4: ldur            w2, [x1, #0x13]
    // 0xb94bc8: DecompressPointer r2
    //     0xb94bc8: add             x2, x2, HEAP, lsl #32
    // 0xb94bcc: StoreField: r0->field_13 = r2
    //     0xb94bcc: stur            w2, [x0, #0x13]
    // 0xb94bd0: str             x0, [SP]
    // 0xb94bd4: r0 = _interpolate()
    //     0xb94bd4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb94bd8: stur            x0, [fp, #-0x10]
    // 0xb94bdc: r0 = GetNavigation.textTheme()
    //     0xb94bdc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb94be0: LoadField: r3 = r0->field_23
    //     0xb94be0: ldur            w3, [x0, #0x23]
    // 0xb94be4: DecompressPointer r3
    //     0xb94be4: add             x3, x3, HEAP, lsl #32
    // 0xb94be8: stur            x3, [fp, #-0x18]
    // 0xb94bec: cmp             w3, NULL
    // 0xb94bf0: b.eq            #0xb94d70
    // 0xb94bf4: r1 = _ConstMap len:6
    //     0xb94bf4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94bf8: ldr             x1, [x1, #0xc20]
    // 0xb94bfc: r2 = 6
    //     0xb94bfc: movz            x2, #0x6
    // 0xb94c00: r0 = []()
    //     0xb94c00: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94c04: r1 = _ConstMap len:6
    //     0xb94c04: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94c08: ldr             x1, [x1, #0xc20]
    // 0xb94c0c: r2 = 8
    //     0xb94c0c: movz            x2, #0x8
    // 0xb94c10: stur            x0, [fp, #-0x20]
    // 0xb94c14: r0 = []()
    //     0xb94c14: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94c18: r16 = <Color?>
    //     0xb94c18: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb94c1c: ldr             x16, [x16, #0x98]
    // 0xb94c20: stp             x0, x16, [SP, #8]
    // 0xb94c24: ldur            x16, [fp, #-0x20]
    // 0xb94c28: str             x16, [SP]
    // 0xb94c2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb94c2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb94c30: r0 = mode()
    //     0xb94c30: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb94c34: str             x0, [SP]
    // 0xb94c38: ldur            x1, [fp, #-0x18]
    // 0xb94c3c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb94c3c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb94c40: ldr             x4, [x4, #0x228]
    // 0xb94c44: r0 = copyWith()
    //     0xb94c44: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb94c48: stur            x0, [fp, #-0x18]
    // 0xb94c4c: r0 = Text()
    //     0xb94c4c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb94c50: mov             x1, x0
    // 0xb94c54: ldur            x0, [fp, #-0x10]
    // 0xb94c58: stur            x1, [fp, #-0x20]
    // 0xb94c5c: StoreField: r1->field_b = r0
    //     0xb94c5c: stur            w0, [x1, #0xb]
    // 0xb94c60: ldur            x0, [fp, #-0x18]
    // 0xb94c64: StoreField: r1->field_13 = r0
    //     0xb94c64: stur            w0, [x1, #0x13]
    // 0xb94c68: r0 = Center()
    //     0xb94c68: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb94c6c: mov             x1, x0
    // 0xb94c70: r0 = Instance_Alignment
    //     0xb94c70: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb94c74: ldr             x0, [x0, #0x898]
    // 0xb94c78: stur            x1, [fp, #-0x18]
    // 0xb94c7c: StoreField: r1->field_f = r0
    //     0xb94c7c: stur            w0, [x1, #0xf]
    // 0xb94c80: ldur            x0, [fp, #-0x20]
    // 0xb94c84: StoreField: r1->field_b = r0
    //     0xb94c84: stur            w0, [x1, #0xb]
    // 0xb94c88: ldur            d0, [fp, #-0x30]
    // 0xb94c8c: r0 = inline_Allocate_Double()
    //     0xb94c8c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb94c90: add             x0, x0, #0x10
    //     0xb94c94: cmp             x2, x0
    //     0xb94c98: b.ls            #0xb94d74
    //     0xb94c9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb94ca0: sub             x0, x0, #0xf
    //     0xb94ca4: movz            x2, #0xe15c
    //     0xb94ca8: movk            x2, #0x3, lsl #16
    //     0xb94cac: stur            x2, [x0, #-1]
    // 0xb94cb0: StoreField: r0->field_7 = d0
    //     0xb94cb0: stur            d0, [x0, #7]
    // 0xb94cb4: stur            x0, [fp, #-0x10]
    // 0xb94cb8: r0 = SizedBox()
    //     0xb94cb8: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb94cbc: mov             x1, x0
    // 0xb94cc0: ldur            x0, [fp, #-0x10]
    // 0xb94cc4: StoreField: r1->field_13 = r0
    //     0xb94cc4: stur            w0, [x1, #0x13]
    // 0xb94cc8: ldur            x0, [fp, #-0x18]
    // 0xb94ccc: StoreField: r1->field_b = r0
    //     0xb94ccc: stur            w0, [x1, #0xb]
    // 0xb94cd0: mov             x0, x1
    // 0xb94cd4: LeaveFrame
    //     0xb94cd4: mov             SP, fp
    //     0xb94cd8: ldp             fp, lr, [SP], #0x10
    // 0xb94cdc: ret
    //     0xb94cdc: ret             
    // 0xb94ce0: ldur            x2, [fp, #-8]
    // 0xb94ce4: LoadField: r1 = r2->field_f
    //     0xb94ce4: ldur            w1, [x2, #0xf]
    // 0xb94ce8: DecompressPointer r1
    //     0xb94ce8: add             x1, x1, HEAP, lsl #32
    // 0xb94cec: r0 = itemsCount()
    //     0xb94cec: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xb94cf0: ldur            x2, [fp, #-8]
    // 0xb94cf4: r1 = Function '<anonymous closure>':.
    //     0xb94cf4: add             x1, PP, #0x35, lsl #12  ; [pp+0x353b0] AnonymousClosure: (0xb3e914), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb94cf8: ldr             x1, [x1, #0x3b0]
    // 0xb94cfc: stur            x0, [fp, #-0x28]
    // 0xb94d00: r0 = AllocateClosure()
    //     0xb94d00: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94d04: r1 = Function '<anonymous closure>':.
    //     0xb94d04: add             x1, PP, #0x35, lsl #12  ; [pp+0x353b8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb94d08: ldr             x1, [x1, #0x3b8]
    // 0xb94d0c: r2 = Null
    //     0xb94d0c: mov             x2, NULL
    // 0xb94d10: stur            x0, [fp, #-8]
    // 0xb94d14: r0 = AllocateClosure()
    //     0xb94d14: bl              #0xec1630  ; AllocateClosureStub
    // 0xb94d18: stur            x0, [fp, #-0x10]
    // 0xb94d1c: r0 = ListView()
    //     0xb94d1c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb94d20: stur            x0, [fp, #-0x18]
    // 0xb94d24: r16 = Instance_ClampingScrollPhysics
    //     0xb94d24: add             x16, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xb94d28: ldr             x16, [x16, #0x410]
    // 0xb94d2c: r30 = Instance_EdgeInsets
    //     0xb94d2c: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb94d30: stp             lr, x16, [SP, #8]
    // 0xb94d34: r16 = true
    //     0xb94d34: add             x16, NULL, #0x20  ; true
    // 0xb94d38: str             x16, [SP]
    // 0xb94d3c: mov             x1, x0
    // 0xb94d40: ldur            x2, [fp, #-8]
    // 0xb94d44: ldur            x3, [fp, #-0x28]
    // 0xb94d48: ldur            x5, [fp, #-0x10]
    // 0xb94d4c: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x5, physics, 0x4, shrinkWrap, 0x6, null]
    //     0xb94d4c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34048] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x5, "physics", 0x4, "shrinkWrap", 0x6, Null]
    //     0xb94d50: ldr             x4, [x4, #0x48]
    // 0xb94d54: r0 = ListView.separated()
    //     0xb94d54: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb94d58: ldur            x0, [fp, #-0x18]
    // 0xb94d5c: LeaveFrame
    //     0xb94d5c: mov             SP, fp
    //     0xb94d60: ldp             fp, lr, [SP], #0x10
    // 0xb94d64: ret
    //     0xb94d64: ret             
    // 0xb94d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94d68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94d6c: b               #0xb94b44
    // 0xb94d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94d70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb94d74: SaveReg d0
    //     0xb94d74: str             q0, [SP, #-0x10]!
    // 0xb94d78: SaveReg r1
    //     0xb94d78: str             x1, [SP, #-8]!
    // 0xb94d7c: r0 = AllocateDouble()
    //     0xb94d7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb94d80: RestoreReg r1
    //     0xb94d80: ldr             x1, [SP], #8
    // 0xb94d84: RestoreReg d0
    //     0xb94d84: ldr             q0, [SP], #0x10
    // 0xb94d88: b               #0xb94cb0
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0xb94d8c, size: 0x23c
    // 0xb94d8c: EnterFrame
    //     0xb94d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94d90: mov             fp, SP
    // 0xb94d94: AllocStack(0x38)
    //     0xb94d94: sub             SP, SP, #0x38
    // 0xb94d98: SetupParameters()
    //     0xb94d98: ldr             x0, [fp, #0x10]
    //     0xb94d9c: ldur            w2, [x0, #0x17]
    //     0xb94da0: add             x2, x2, HEAP, lsl #32
    //     0xb94da4: stur            x2, [fp, #-8]
    // 0xb94da8: CheckStackOverflow
    //     0xb94da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94dac: cmp             SP, x16
    //     0xb94db0: b.ls            #0xb94fbc
    // 0xb94db4: LoadField: r0 = r2->field_f
    //     0xb94db4: ldur            w0, [x2, #0xf]
    // 0xb94db8: DecompressPointer r0
    //     0xb94db8: add             x0, x0, HEAP, lsl #32
    // 0xb94dbc: LoadField: r1 = r0->field_47
    //     0xb94dbc: ldur            w1, [x0, #0x47]
    // 0xb94dc0: DecompressPointer r1
    //     0xb94dc0: add             x1, x1, HEAP, lsl #32
    // 0xb94dc4: r0 = value()
    //     0xb94dc4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb94dc8: r1 = _ConstMap len:6
    //     0xb94dc8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94dcc: ldr             x1, [x1, #0xc20]
    // 0xb94dd0: r2 = 8
    //     0xb94dd0: movz            x2, #0x8
    // 0xb94dd4: r0 = []()
    //     0xb94dd4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94dd8: r1 = _ConstMap len:6
    //     0xb94dd8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94ddc: ldr             x1, [x1, #0xc20]
    // 0xb94de0: r2 = 6
    //     0xb94de0: movz            x2, #0x6
    // 0xb94de4: stur            x0, [fp, #-0x10]
    // 0xb94de8: r0 = []()
    //     0xb94de8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94dec: r16 = <Color?>
    //     0xb94dec: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb94df0: ldr             x16, [x16, #0x98]
    // 0xb94df4: stp             x0, x16, [SP, #8]
    // 0xb94df8: ldur            x16, [fp, #-0x10]
    // 0xb94dfc: str             x16, [SP]
    // 0xb94e00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb94e00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb94e04: r0 = mode()
    //     0xb94e04: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb94e08: stur            x0, [fp, #-0x10]
    // 0xb94e0c: r0 = Icon()
    //     0xb94e0c: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb94e10: mov             x2, x0
    // 0xb94e14: r0 = Instance_IconData
    //     0xb94e14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbe8] Obj!IconData@e102b1
    //     0xb94e18: ldr             x0, [x0, #0xbe8]
    // 0xb94e1c: stur            x2, [fp, #-0x18]
    // 0xb94e20: StoreField: r2->field_b = r0
    //     0xb94e20: stur            w0, [x2, #0xb]
    // 0xb94e24: r0 = 16.000000
    //     0xb94e24: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb94e28: ldr             x0, [x0, #0x80]
    // 0xb94e2c: StoreField: r2->field_f = r0
    //     0xb94e2c: stur            w0, [x2, #0xf]
    // 0xb94e30: ldur            x0, [fp, #-0x10]
    // 0xb94e34: StoreField: r2->field_23 = r0
    //     0xb94e34: stur            w0, [x2, #0x23]
    // 0xb94e38: ldur            x0, [fp, #-8]
    // 0xb94e3c: LoadField: r1 = r0->field_f
    //     0xb94e3c: ldur            w1, [x0, #0xf]
    // 0xb94e40: DecompressPointer r1
    //     0xb94e40: add             x1, x1, HEAP, lsl #32
    // 0xb94e44: LoadField: r0 = r1->field_47
    //     0xb94e44: ldur            w0, [x1, #0x47]
    // 0xb94e48: DecompressPointer r0
    //     0xb94e48: add             x0, x0, HEAP, lsl #32
    // 0xb94e4c: mov             x1, x0
    // 0xb94e50: r0 = value()
    //     0xb94e50: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb94e54: LoadField: r1 = r0->field_7
    //     0xb94e54: ldur            w1, [x0, #7]
    // 0xb94e58: DecompressPointer r1
    //     0xb94e58: add             x1, x1, HEAP, lsl #32
    // 0xb94e5c: r0 = OrderByExtension.title()
    //     0xb94e5c: bl              #0xae6bb4  ; [package:nuonline/app/data/enums/sort_ordering_enum.dart] ::OrderByExtension.title
    // 0xb94e60: stur            x0, [fp, #-8]
    // 0xb94e64: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94e64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94e68: ldr             x0, [x0, #0x2670]
    //     0xb94e6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb94e70: cmp             w0, w16
    //     0xb94e74: b.ne            #0xb94e80
    //     0xb94e78: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb94e7c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb94e80: r0 = GetNavigation.textTheme()
    //     0xb94e80: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb94e84: LoadField: r3 = r0->field_2f
    //     0xb94e84: ldur            w3, [x0, #0x2f]
    // 0xb94e88: DecompressPointer r3
    //     0xb94e88: add             x3, x3, HEAP, lsl #32
    // 0xb94e8c: stur            x3, [fp, #-0x10]
    // 0xb94e90: cmp             w3, NULL
    // 0xb94e94: b.eq            #0xb94fc4
    // 0xb94e98: r1 = _ConstMap len:6
    //     0xb94e98: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94e9c: ldr             x1, [x1, #0xc20]
    // 0xb94ea0: r2 = 8
    //     0xb94ea0: movz            x2, #0x8
    // 0xb94ea4: r0 = []()
    //     0xb94ea4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94ea8: r1 = _ConstMap len:6
    //     0xb94ea8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb94eac: ldr             x1, [x1, #0xc20]
    // 0xb94eb0: r2 = 6
    //     0xb94eb0: movz            x2, #0x6
    // 0xb94eb4: stur            x0, [fp, #-0x20]
    // 0xb94eb8: r0 = []()
    //     0xb94eb8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb94ebc: r16 = <Color?>
    //     0xb94ebc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb94ec0: ldr             x16, [x16, #0x98]
    // 0xb94ec4: stp             x0, x16, [SP, #8]
    // 0xb94ec8: ldur            x16, [fp, #-0x20]
    // 0xb94ecc: str             x16, [SP]
    // 0xb94ed0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb94ed0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb94ed4: r0 = mode()
    //     0xb94ed4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb94ed8: str             x0, [SP]
    // 0xb94edc: ldur            x1, [fp, #-0x10]
    // 0xb94ee0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb94ee0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb94ee4: ldr             x4, [x4, #0x228]
    // 0xb94ee8: r0 = copyWith()
    //     0xb94ee8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb94eec: stur            x0, [fp, #-0x10]
    // 0xb94ef0: r0 = Text()
    //     0xb94ef0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb94ef4: mov             x3, x0
    // 0xb94ef8: ldur            x0, [fp, #-8]
    // 0xb94efc: stur            x3, [fp, #-0x20]
    // 0xb94f00: StoreField: r3->field_b = r0
    //     0xb94f00: stur            w0, [x3, #0xb]
    // 0xb94f04: ldur            x0, [fp, #-0x10]
    // 0xb94f08: StoreField: r3->field_13 = r0
    //     0xb94f08: stur            w0, [x3, #0x13]
    // 0xb94f0c: r1 = Null
    //     0xb94f0c: mov             x1, NULL
    // 0xb94f10: r2 = 6
    //     0xb94f10: movz            x2, #0x6
    // 0xb94f14: r0 = AllocateArray()
    //     0xb94f14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb94f18: mov             x2, x0
    // 0xb94f1c: ldur            x0, [fp, #-0x18]
    // 0xb94f20: stur            x2, [fp, #-8]
    // 0xb94f24: StoreField: r2->field_f = r0
    //     0xb94f24: stur            w0, [x2, #0xf]
    // 0xb94f28: r16 = Instance_SizedBox
    //     0xb94f28: add             x16, PP, #0x35, lsl #12  ; [pp+0x353c0] Obj!SizedBox@e1e461
    //     0xb94f2c: ldr             x16, [x16, #0x3c0]
    // 0xb94f30: StoreField: r2->field_13 = r16
    //     0xb94f30: stur            w16, [x2, #0x13]
    // 0xb94f34: ldur            x0, [fp, #-0x20]
    // 0xb94f38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb94f38: stur            w0, [x2, #0x17]
    // 0xb94f3c: r1 = <Widget>
    //     0xb94f3c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb94f40: r0 = AllocateGrowableArray()
    //     0xb94f40: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb94f44: mov             x1, x0
    // 0xb94f48: ldur            x0, [fp, #-8]
    // 0xb94f4c: stur            x1, [fp, #-0x10]
    // 0xb94f50: StoreField: r1->field_f = r0
    //     0xb94f50: stur            w0, [x1, #0xf]
    // 0xb94f54: r0 = 6
    //     0xb94f54: movz            x0, #0x6
    // 0xb94f58: StoreField: r1->field_b = r0
    //     0xb94f58: stur            w0, [x1, #0xb]
    // 0xb94f5c: r0 = Row()
    //     0xb94f5c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb94f60: r1 = Instance_Axis
    //     0xb94f60: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb94f64: StoreField: r0->field_f = r1
    //     0xb94f64: stur            w1, [x0, #0xf]
    // 0xb94f68: r1 = Instance_MainAxisAlignment
    //     0xb94f68: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb94f6c: ldr             x1, [x1, #0x730]
    // 0xb94f70: StoreField: r0->field_13 = r1
    //     0xb94f70: stur            w1, [x0, #0x13]
    // 0xb94f74: r1 = Instance_MainAxisSize
    //     0xb94f74: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb94f78: ldr             x1, [x1, #0x738]
    // 0xb94f7c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb94f7c: stur            w1, [x0, #0x17]
    // 0xb94f80: r1 = Instance_CrossAxisAlignment
    //     0xb94f80: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb94f84: ldr             x1, [x1, #0x740]
    // 0xb94f88: StoreField: r0->field_1b = r1
    //     0xb94f88: stur            w1, [x0, #0x1b]
    // 0xb94f8c: r1 = Instance_VerticalDirection
    //     0xb94f8c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb94f90: ldr             x1, [x1, #0x748]
    // 0xb94f94: StoreField: r0->field_23 = r1
    //     0xb94f94: stur            w1, [x0, #0x23]
    // 0xb94f98: r1 = Instance_Clip
    //     0xb94f98: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb94f9c: ldr             x1, [x1, #0x750]
    // 0xb94fa0: StoreField: r0->field_2b = r1
    //     0xb94fa0: stur            w1, [x0, #0x2b]
    // 0xb94fa4: StoreField: r0->field_2f = rZR
    //     0xb94fa4: stur            xzr, [x0, #0x2f]
    // 0xb94fa8: ldur            x1, [fp, #-0x10]
    // 0xb94fac: StoreField: r0->field_b = r1
    //     0xb94fac: stur            w1, [x0, #0xb]
    // 0xb94fb0: LeaveFrame
    //     0xb94fb0: mov             SP, fp
    //     0xb94fb4: ldp             fp, lr, [SP], #0x10
    // 0xb94fb8: ret
    //     0xb94fb8: ret             
    // 0xb94fbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94fbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94fc0: b               #0xb94db4
    // 0xb94fc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94fc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
