// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_pending.dart

// class id: 1050249, size: 0x8
class :: {
}

// class id: 4116, size: 0x18, field offset: 0x14
class _TransactionPendingState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa383f8, size: 0x780
    // 0xa383f8: EnterFrame
    //     0xa383f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa383fc: mov             fp, SP
    // 0xa38400: AllocStack(0x98)
    //     0xa38400: sub             SP, SP, #0x98
    // 0xa38404: SetupParameters(_TransactionPendingState this /* r1 => r1, fp-0x8 */)
    //     0xa38404: stur            x1, [fp, #-8]
    // 0xa38408: CheckStackOverflow
    //     0xa38408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3840c: cmp             SP, x16
    //     0xa38410: b.ls            #0xa38b54
    // 0xa38414: r1 = 1
    //     0xa38414: movz            x1, #0x1
    // 0xa38418: r0 = AllocateContext()
    //     0xa38418: bl              #0xec126c  ; AllocateContextStub
    // 0xa3841c: mov             x1, x0
    // 0xa38420: ldur            x0, [fp, #-8]
    // 0xa38424: stur            x1, [fp, #-0x10]
    // 0xa38428: StoreField: r1->field_f = r0
    //     0xa38428: stur            w0, [x1, #0xf]
    // 0xa3842c: r0 = AppBar()
    //     0xa3842c: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xa38430: stur            x0, [fp, #-0x18]
    // 0xa38434: r16 = Instance_Text
    //     0xa38434: add             x16, PP, #0x30, lsl #12  ; [pp+0x30288] Obj!Text@e21641
    //     0xa38438: ldr             x16, [x16, #0x288]
    // 0xa3843c: str             x16, [SP]
    // 0xa38440: mov             x1, x0
    // 0xa38444: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xa38444: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xa38448: ldr             x4, [x4, #0x6e8]
    // 0xa3844c: r0 = AppBar()
    //     0xa3844c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xa38450: ldur            x0, [fp, #-8]
    // 0xa38454: LoadField: r1 = r0->field_b
    //     0xa38454: ldur            w1, [x0, #0xb]
    // 0xa38458: DecompressPointer r1
    //     0xa38458: add             x1, x1, HEAP, lsl #32
    // 0xa3845c: cmp             w1, NULL
    // 0xa38460: b.eq            #0xa38b5c
    // 0xa38464: LoadField: r2 = r1->field_f
    //     0xa38464: ldur            w2, [x1, #0xf]
    // 0xa38468: DecompressPointer r2
    //     0xa38468: add             x2, x2, HEAP, lsl #32
    // 0xa3846c: stur            x2, [fp, #-0x30]
    // 0xa38470: LoadField: r3 = r1->field_b
    //     0xa38470: ldur            w3, [x1, #0xb]
    // 0xa38474: DecompressPointer r3
    //     0xa38474: add             x3, x3, HEAP, lsl #32
    // 0xa38478: stur            x3, [fp, #-0x28]
    // 0xa3847c: LoadField: r1 = r3->field_4b
    //     0xa3847c: ldur            w1, [x3, #0x4b]
    // 0xa38480: DecompressPointer r1
    //     0xa38480: add             x1, x1, HEAP, lsl #32
    // 0xa38484: stur            x1, [fp, #-0x20]
    // 0xa38488: r0 = ExpirationCountdownReminder()
    //     0xa38488: bl              #0xa39778  ; AllocateExpirationCountdownReminderStub -> ExpirationCountdownReminder (size=0x10)
    // 0xa3848c: mov             x3, x0
    // 0xa38490: ldur            x0, [fp, #-0x20]
    // 0xa38494: stur            x3, [fp, #-0x38]
    // 0xa38498: StoreField: r3->field_b = r0
    //     0xa38498: stur            w0, [x3, #0xb]
    // 0xa3849c: r1 = Null
    //     0xa3849c: mov             x1, NULL
    // 0xa384a0: r2 = 2
    //     0xa384a0: movz            x2, #0x2
    // 0xa384a4: r0 = AllocateArray()
    //     0xa384a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa384a8: mov             x2, x0
    // 0xa384ac: ldur            x0, [fp, #-0x38]
    // 0xa384b0: stur            x2, [fp, #-0x20]
    // 0xa384b4: StoreField: r2->field_f = r0
    //     0xa384b4: stur            w0, [x2, #0xf]
    // 0xa384b8: r1 = <Widget>
    //     0xa384b8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa384bc: r0 = AllocateGrowableArray()
    //     0xa384bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa384c0: mov             x1, x0
    // 0xa384c4: ldur            x0, [fp, #-0x20]
    // 0xa384c8: stur            x1, [fp, #-0x38]
    // 0xa384cc: StoreField: r1->field_f = r0
    //     0xa384cc: stur            w0, [x1, #0xf]
    // 0xa384d0: r2 = 2
    //     0xa384d0: movz            x2, #0x2
    // 0xa384d4: StoreField: r1->field_b = r2
    //     0xa384d4: stur            w2, [x1, #0xb]
    // 0xa384d8: r0 = NSection()
    //     0xa384d8: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xa384dc: mov             x1, x0
    // 0xa384e0: r0 = ""
    //     0xa384e0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa384e4: stur            x1, [fp, #-0x40]
    // 0xa384e8: StoreField: r1->field_b = r0
    //     0xa384e8: stur            w0, [x1, #0xb]
    // 0xa384ec: ldur            x0, [fp, #-0x38]
    // 0xa384f0: StoreField: r1->field_f = r0
    //     0xa384f0: stur            w0, [x1, #0xf]
    // 0xa384f4: r0 = false
    //     0xa384f4: add             x0, NULL, #0x30  ; false
    // 0xa384f8: StoreField: r1->field_27 = r0
    //     0xa384f8: stur            w0, [x1, #0x27]
    // 0xa384fc: StoreField: r1->field_2b = r0
    //     0xa384fc: stur            w0, [x1, #0x2b]
    // 0xa38500: ldur            x2, [fp, #-0x28]
    // 0xa38504: LoadField: r3 = r2->field_f
    //     0xa38504: ldur            w3, [x2, #0xf]
    // 0xa38508: DecompressPointer r3
    //     0xa38508: add             x3, x3, HEAP, lsl #32
    // 0xa3850c: stur            x3, [fp, #-0x20]
    // 0xa38510: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa38510: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa38514: ldr             x0, [x0, #0x2670]
    //     0xa38518: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3851c: cmp             w0, w16
    //     0xa38520: b.ne            #0xa3852c
    //     0xa38524: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa38528: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa3852c: r0 = GetNavigation.textTheme()
    //     0xa3852c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa38530: LoadField: r1 = r0->field_23
    //     0xa38530: ldur            w1, [x0, #0x23]
    // 0xa38534: DecompressPointer r1
    //     0xa38534: add             x1, x1, HEAP, lsl #32
    // 0xa38538: stur            x1, [fp, #-0x28]
    // 0xa3853c: r0 = GetNavigation.textTheme()
    //     0xa3853c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa38540: LoadField: r1 = r0->field_23
    //     0xa38540: ldur            w1, [x0, #0x23]
    // 0xa38544: DecompressPointer r1
    //     0xa38544: add             x1, x1, HEAP, lsl #32
    // 0xa38548: cmp             w1, NULL
    // 0xa3854c: b.eq            #0xa38b60
    // 0xa38550: r16 = Instance_FontWeight
    //     0xa38550: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xa38554: ldr             x16, [x16, #0xe20]
    // 0xa38558: str             x16, [SP]
    // 0xa3855c: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xa3855c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xa38560: ldr             x4, [x4, #0xfe0]
    // 0xa38564: r0 = copyWith()
    //     0xa38564: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa38568: mov             x1, x0
    // 0xa3856c: ldur            x0, [fp, #-8]
    // 0xa38570: stur            x1, [fp, #-0x70]
    // 0xa38574: LoadField: r2 = r0->field_b
    //     0xa38574: ldur            w2, [x0, #0xb]
    // 0xa38578: DecompressPointer r2
    //     0xa38578: add             x2, x2, HEAP, lsl #32
    // 0xa3857c: cmp             w2, NULL
    // 0xa38580: b.eq            #0xa38b64
    // 0xa38584: LoadField: r3 = r2->field_b
    //     0xa38584: ldur            w3, [x2, #0xb]
    // 0xa38588: DecompressPointer r3
    //     0xa38588: add             x3, x3, HEAP, lsl #32
    // 0xa3858c: LoadField: r2 = r3->field_3f
    //     0xa3858c: ldur            w2, [x3, #0x3f]
    // 0xa38590: DecompressPointer r2
    //     0xa38590: add             x2, x2, HEAP, lsl #32
    // 0xa38594: stur            x2, [fp, #-0x68]
    // 0xa38598: LoadField: r4 = r0->field_13
    //     0xa38598: ldur            w4, [x0, #0x13]
    // 0xa3859c: DecompressPointer r4
    //     0xa3859c: add             x4, x4, HEAP, lsl #32
    // 0xa385a0: stur            x4, [fp, #-0x60]
    // 0xa385a4: LoadField: r5 = r3->field_2b
    //     0xa385a4: ldur            w5, [x3, #0x2b]
    // 0xa385a8: DecompressPointer r5
    //     0xa385a8: add             x5, x5, HEAP, lsl #32
    // 0xa385ac: stur            x5, [fp, #-0x58]
    // 0xa385b0: LoadField: r6 = r5->field_13
    //     0xa385b0: ldur            w6, [x5, #0x13]
    // 0xa385b4: DecompressPointer r6
    //     0xa385b4: add             x6, x6, HEAP, lsl #32
    // 0xa385b8: LoadField: r7 = r6->field_f
    //     0xa385b8: ldur            w7, [x6, #0xf]
    // 0xa385bc: DecompressPointer r7
    //     0xa385bc: add             x7, x7, HEAP, lsl #32
    // 0xa385c0: stur            x7, [fp, #-0x50]
    // 0xa385c4: LoadField: r6 = r3->field_1b
    //     0xa385c4: ldur            x6, [x3, #0x1b]
    // 0xa385c8: stur            x6, [fp, #-0x48]
    // 0xa385cc: LoadField: r8 = r3->field_3b
    //     0xa385cc: ldur            w8, [x3, #0x3b]
    // 0xa385d0: DecompressPointer r8
    //     0xa385d0: add             x8, x8, HEAP, lsl #32
    // 0xa385d4: stur            x8, [fp, #-0x38]
    // 0xa385d8: r0 = QRis()
    //     0xa385d8: bl              #0xa3976c  ; AllocateQRisStub -> QRis (size=0x1c)
    // 0xa385dc: mov             x1, x0
    // 0xa385e0: ldur            x0, [fp, #-0x50]
    // 0xa385e4: stur            x1, [fp, #-0x78]
    // 0xa385e8: StoreField: r1->field_b = r0
    //     0xa385e8: stur            w0, [x1, #0xb]
    // 0xa385ec: ldur            x2, [fp, #-0x48]
    // 0xa385f0: StoreField: r1->field_f = r2
    //     0xa385f0: stur            x2, [x1, #0xf]
    // 0xa385f4: ldur            x3, [fp, #-0x38]
    // 0xa385f8: ArrayStore: r1[0] = r3  ; List_4
    //     0xa385f8: stur            w3, [x1, #0x17]
    // 0xa385fc: r0 = Screenshot()
    //     0xa385fc: bl              #0xa39760  ; AllocateScreenshotStub -> Screenshot (size=0x14)
    // 0xa38600: mov             x3, x0
    // 0xa38604: ldur            x0, [fp, #-0x78]
    // 0xa38608: stur            x3, [fp, #-0x80]
    // 0xa3860c: StoreField: r3->field_b = r0
    //     0xa3860c: stur            w0, [x3, #0xb]
    // 0xa38610: ldur            x0, [fp, #-0x60]
    // 0xa38614: StoreField: r3->field_f = r0
    //     0xa38614: stur            w0, [x3, #0xf]
    // 0xa38618: ldur            x0, [fp, #-0x58]
    // 0xa3861c: LoadField: r4 = r0->field_f
    //     0xa3861c: ldur            w4, [x0, #0xf]
    // 0xa38620: DecompressPointer r4
    //     0xa38620: add             x4, x4, HEAP, lsl #32
    // 0xa38624: stur            x4, [fp, #-0x60]
    // 0xa38628: r1 = Null
    //     0xa38628: mov             x1, NULL
    // 0xa3862c: r2 = 6
    //     0xa3862c: movz            x2, #0x6
    // 0xa38630: r0 = AllocateArray()
    //     0xa38630: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38634: mov             x2, x0
    // 0xa38638: ldur            x0, [fp, #-0x50]
    // 0xa3863c: stur            x2, [fp, #-0x58]
    // 0xa38640: StoreField: r2->field_f = r0
    //     0xa38640: stur            w0, [x2, #0xf]
    // 0xa38644: r16 = "ke"
    //     0xa38644: add             x16, PP, #0x40, lsl #12  ; [pp+0x40278] "ke"
    //     0xa38648: ldr             x16, [x16, #0x278]
    // 0xa3864c: StoreField: r2->field_13 = r16
    //     0xa3864c: stur            w16, [x2, #0x13]
    // 0xa38650: ldur            x0, [fp, #-0x60]
    // 0xa38654: ArrayStore: r2[0] = r0  ; List_4
    //     0xa38654: stur            w0, [x2, #0x17]
    // 0xa38658: r1 = <String>
    //     0xa38658: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3865c: r0 = AllocateGrowableArray()
    //     0xa3865c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38660: mov             x1, x0
    // 0xa38664: ldur            x0, [fp, #-0x58]
    // 0xa38668: StoreField: r1->field_f = r0
    //     0xa38668: stur            w0, [x1, #0xf]
    // 0xa3866c: r0 = 6
    //     0xa3866c: movz            x0, #0x6
    // 0xa38670: StoreField: r1->field_b = r0
    //     0xa38670: stur            w0, [x1, #0xb]
    // 0xa38674: r16 = " "
    //     0xa38674: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xa38678: str             x16, [SP]
    // 0xa3867c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa3867c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa38680: r0 = join()
    //     0xa38680: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xa38684: stur            x0, [fp, #-0x50]
    // 0xa38688: r0 = BNIVirtualAccount()
    //     0xa38688: bl              #0xa39754  ; AllocateBNIVirtualAccountStub -> BNIVirtualAccount (size=0x1c)
    // 0xa3868c: mov             x1, x0
    // 0xa38690: ldur            x0, [fp, #-0x50]
    // 0xa38694: StoreField: r1->field_b = r0
    //     0xa38694: stur            w0, [x1, #0xb]
    // 0xa38698: ldur            x0, [fp, #-0x38]
    // 0xa3869c: StoreField: r1->field_f = r0
    //     0xa3869c: stur            w0, [x1, #0xf]
    // 0xa386a0: ldur            x0, [fp, #-0x48]
    // 0xa386a4: StoreField: r1->field_13 = r0
    //     0xa386a4: stur            x0, [x1, #0x13]
    // 0xa386a8: ldur            x0, [fp, #-0x68]
    // 0xa386ac: r16 = Instance_PaymentMethod
    //     0xa386ac: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xa386b0: ldr             x16, [x16, #0x220]
    // 0xa386b4: cmp             w0, w16
    // 0xa386b8: b.ne            #0xa386c4
    // 0xa386bc: ldur            x4, [fp, #-0x80]
    // 0xa386c0: b               #0xa386c8
    // 0xa386c4: mov             x4, x1
    // 0xa386c8: ldur            x0, [fp, #-8]
    // 0xa386cc: ldur            x3, [fp, #-0x20]
    // 0xa386d0: ldur            x2, [fp, #-0x28]
    // 0xa386d4: ldur            x1, [fp, #-0x70]
    // 0xa386d8: stur            x4, [fp, #-0x58]
    // 0xa386dc: LoadField: r5 = r0->field_b
    //     0xa386dc: ldur            w5, [x0, #0xb]
    // 0xa386e0: DecompressPointer r5
    //     0xa386e0: add             x5, x5, HEAP, lsl #32
    // 0xa386e4: cmp             w5, NULL
    // 0xa386e8: b.eq            #0xa38b68
    // 0xa386ec: LoadField: r6 = r5->field_b
    //     0xa386ec: ldur            w6, [x5, #0xb]
    // 0xa386f0: DecompressPointer r6
    //     0xa386f0: add             x6, x6, HEAP, lsl #32
    // 0xa386f4: stur            x6, [fp, #-0x50]
    // 0xa386f8: LoadField: r5 = r6->field_4b
    //     0xa386f8: ldur            w5, [x6, #0x4b]
    // 0xa386fc: DecompressPointer r5
    //     0xa386fc: add             x5, x5, HEAP, lsl #32
    // 0xa38700: stur            x5, [fp, #-0x38]
    // 0xa38704: r0 = ExpirationReminder()
    //     0xa38704: bl              #0xa39748  ; AllocateExpirationReminderStub -> ExpirationReminder (size=0x10)
    // 0xa38708: mov             x3, x0
    // 0xa3870c: ldur            x0, [fp, #-0x38]
    // 0xa38710: stur            x3, [fp, #-0x60]
    // 0xa38714: StoreField: r3->field_b = r0
    //     0xa38714: stur            w0, [x3, #0xb]
    // 0xa38718: r1 = Null
    //     0xa38718: mov             x1, NULL
    // 0xa3871c: r2 = 8
    //     0xa3871c: movz            x2, #0x8
    // 0xa38720: r0 = AllocateArray()
    //     0xa38720: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38724: stur            x0, [fp, #-0x38]
    // 0xa38728: r16 = Instance_SizedBox
    //     0xa38728: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xa3872c: ldr             x16, [x16, #0xfe8]
    // 0xa38730: StoreField: r0->field_f = r16
    //     0xa38730: stur            w16, [x0, #0xf]
    // 0xa38734: ldur            x1, [fp, #-0x58]
    // 0xa38738: StoreField: r0->field_13 = r1
    //     0xa38738: stur            w1, [x0, #0x13]
    // 0xa3873c: r16 = Instance_SizedBox
    //     0xa3873c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xa38740: ldr             x16, [x16, #0x448]
    // 0xa38744: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38744: stur            w16, [x0, #0x17]
    // 0xa38748: ldur            x1, [fp, #-0x60]
    // 0xa3874c: StoreField: r0->field_1b = r1
    //     0xa3874c: stur            w1, [x0, #0x1b]
    // 0xa38750: r1 = <Widget>
    //     0xa38750: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa38754: r0 = AllocateGrowableArray()
    //     0xa38754: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38758: mov             x1, x0
    // 0xa3875c: ldur            x0, [fp, #-0x38]
    // 0xa38760: stur            x1, [fp, #-0x58]
    // 0xa38764: StoreField: r1->field_f = r0
    //     0xa38764: stur            w0, [x1, #0xf]
    // 0xa38768: r0 = 8
    //     0xa38768: movz            x0, #0x8
    // 0xa3876c: StoreField: r1->field_b = r0
    //     0xa3876c: stur            w0, [x1, #0xb]
    // 0xa38770: r0 = NSection()
    //     0xa38770: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xa38774: mov             x3, x0
    // 0xa38778: r0 = "ID Transaksi:"
    //     0xa38778: add             x0, PP, #0x40, lsl #12  ; [pp+0x40280] "ID Transaksi:"
    //     0xa3877c: ldr             x0, [x0, #0x280]
    // 0xa38780: stur            x3, [fp, #-0x38]
    // 0xa38784: StoreField: r3->field_b = r0
    //     0xa38784: stur            w0, [x3, #0xb]
    // 0xa38788: ldur            x0, [fp, #-0x58]
    // 0xa3878c: StoreField: r3->field_f = r0
    //     0xa3878c: stur            w0, [x3, #0xf]
    // 0xa38790: ldur            x0, [fp, #-0x20]
    // 0xa38794: StoreField: r3->field_13 = r0
    //     0xa38794: stur            w0, [x3, #0x13]
    // 0xa38798: ldur            x0, [fp, #-0x28]
    // 0xa3879c: StoreField: r3->field_1f = r0
    //     0xa3879c: stur            w0, [x3, #0x1f]
    // 0xa387a0: ldur            x0, [fp, #-0x70]
    // 0xa387a4: StoreField: r3->field_23 = r0
    //     0xa387a4: stur            w0, [x3, #0x23]
    // 0xa387a8: r0 = true
    //     0xa387a8: add             x0, NULL, #0x20  ; true
    // 0xa387ac: StoreField: r3->field_27 = r0
    //     0xa387ac: stur            w0, [x3, #0x27]
    // 0xa387b0: StoreField: r3->field_2b = r0
    //     0xa387b0: stur            w0, [x3, #0x2b]
    // 0xa387b4: r1 = Null
    //     0xa387b4: mov             x1, NULL
    // 0xa387b8: r2 = 4
    //     0xa387b8: movz            x2, #0x4
    // 0xa387bc: r0 = AllocateArray()
    //     0xa387bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa387c0: r16 = "Cara Pembayaran dengan "
    //     0xa387c0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30540] "Cara Pembayaran dengan "
    //     0xa387c4: ldr             x16, [x16, #0x540]
    // 0xa387c8: StoreField: r0->field_f = r16
    //     0xa387c8: stur            w16, [x0, #0xf]
    // 0xa387cc: ldur            x1, [fp, #-0x50]
    // 0xa387d0: LoadField: r2 = r1->field_3f
    //     0xa387d0: ldur            w2, [x1, #0x3f]
    // 0xa387d4: DecompressPointer r2
    //     0xa387d4: add             x2, x2, HEAP, lsl #32
    // 0xa387d8: LoadField: r1 = r2->field_7
    //     0xa387d8: ldur            x1, [x2, #7]
    // 0xa387dc: cmp             x1, #1
    // 0xa387e0: b.gt            #0xa38804
    // 0xa387e4: cmp             x1, #0
    // 0xa387e8: b.gt            #0xa387f8
    // 0xa387ec: r5 = "QRIS"
    //     0xa387ec: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xa387f0: ldr             x5, [x5, #0xf50]
    // 0xa387f4: b               #0xa38820
    // 0xa387f8: r5 = "BNI Virtual Account"
    //     0xa387f8: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xa387fc: ldr             x5, [x5, #0xf58]
    // 0xa38800: b               #0xa38820
    // 0xa38804: cmp             x1, #2
    // 0xa38808: b.gt            #0xa38818
    // 0xa3880c: r5 = "BSI Virtual Account"
    //     0xa3880c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xa38810: ldr             x5, [x5, #0xf60]
    // 0xa38814: b               #0xa38820
    // 0xa38818: r5 = "BRI Virtual Account"
    //     0xa38818: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xa3881c: ldr             x5, [x5, #0xf68]
    // 0xa38820: ldur            x2, [fp, #-8]
    // 0xa38824: ldur            x4, [fp, #-0x30]
    // 0xa38828: ldur            x3, [fp, #-0x40]
    // 0xa3882c: ldur            x1, [fp, #-0x38]
    // 0xa38830: StoreField: r0->field_13 = r5
    //     0xa38830: stur            w5, [x0, #0x13]
    // 0xa38834: str             x0, [SP]
    // 0xa38838: r0 = _interpolate()
    //     0xa38838: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa3883c: stur            x0, [fp, #-0x20]
    // 0xa38840: r0 = GetNavigation.textTheme()
    //     0xa38840: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa38844: LoadField: r1 = r0->field_f
    //     0xa38844: ldur            w1, [x0, #0xf]
    // 0xa38848: DecompressPointer r1
    //     0xa38848: add             x1, x1, HEAP, lsl #32
    // 0xa3884c: cmp             w1, NULL
    // 0xa38850: b.eq            #0xa38b6c
    // 0xa38854: r16 = 16.000000
    //     0xa38854: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xa38858: ldr             x16, [x16, #0x80]
    // 0xa3885c: str             x16, [SP]
    // 0xa38860: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xa38860: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xa38864: ldr             x4, [x4, #0x88]
    // 0xa38868: r0 = copyWith()
    //     0xa38868: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa3886c: mov             x2, x0
    // 0xa38870: ldur            x0, [fp, #-8]
    // 0xa38874: stur            x2, [fp, #-0x28]
    // 0xa38878: LoadField: r1 = r0->field_b
    //     0xa38878: ldur            w1, [x0, #0xb]
    // 0xa3887c: DecompressPointer r1
    //     0xa3887c: add             x1, x1, HEAP, lsl #32
    // 0xa38880: cmp             w1, NULL
    // 0xa38884: b.eq            #0xa38b70
    // 0xa38888: LoadField: r3 = r1->field_b
    //     0xa38888: ldur            w3, [x1, #0xb]
    // 0xa3888c: DecompressPointer r3
    //     0xa3888c: add             x3, x3, HEAP, lsl #32
    // 0xa38890: LoadField: r1 = r3->field_3f
    //     0xa38890: ldur            w1, [x3, #0x3f]
    // 0xa38894: DecompressPointer r1
    //     0xa38894: add             x1, x1, HEAP, lsl #32
    // 0xa38898: r0 = PaymentMethodExtension.instructions()
    //     0xa38898: bl              #0xa38bb4  ; [package:nuonline/app/data/enums/payment_enum.dart] ::PaymentMethodExtension.instructions
    // 0xa3889c: stur            x0, [fp, #-0x50]
    // 0xa388a0: r0 = PaymentInstruction()
    //     0xa388a0: bl              #0xa38ba8  ; AllocatePaymentInstructionStub -> PaymentInstruction (size=0x10)
    // 0xa388a4: mov             x3, x0
    // 0xa388a8: ldur            x0, [fp, #-0x50]
    // 0xa388ac: stur            x3, [fp, #-0x58]
    // 0xa388b0: StoreField: r3->field_b = r0
    //     0xa388b0: stur            w0, [x3, #0xb]
    // 0xa388b4: r1 = Null
    //     0xa388b4: mov             x1, NULL
    // 0xa388b8: r2 = 2
    //     0xa388b8: movz            x2, #0x2
    // 0xa388bc: r0 = AllocateArray()
    //     0xa388bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa388c0: mov             x2, x0
    // 0xa388c4: ldur            x0, [fp, #-0x58]
    // 0xa388c8: stur            x2, [fp, #-0x50]
    // 0xa388cc: StoreField: r2->field_f = r0
    //     0xa388cc: stur            w0, [x2, #0xf]
    // 0xa388d0: r1 = <Widget>
    //     0xa388d0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa388d4: r0 = AllocateGrowableArray()
    //     0xa388d4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa388d8: mov             x1, x0
    // 0xa388dc: ldur            x0, [fp, #-0x50]
    // 0xa388e0: stur            x1, [fp, #-0x58]
    // 0xa388e4: StoreField: r1->field_f = r0
    //     0xa388e4: stur            w0, [x1, #0xf]
    // 0xa388e8: r2 = 2
    //     0xa388e8: movz            x2, #0x2
    // 0xa388ec: StoreField: r1->field_b = r2
    //     0xa388ec: stur            w2, [x1, #0xb]
    // 0xa388f0: r0 = NSection()
    //     0xa388f0: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xa388f4: mov             x3, x0
    // 0xa388f8: ldur            x0, [fp, #-0x20]
    // 0xa388fc: stur            x3, [fp, #-0x50]
    // 0xa38900: StoreField: r3->field_b = r0
    //     0xa38900: stur            w0, [x3, #0xb]
    // 0xa38904: ldur            x0, [fp, #-0x58]
    // 0xa38908: StoreField: r3->field_f = r0
    //     0xa38908: stur            w0, [x3, #0xf]
    // 0xa3890c: ldur            x0, [fp, #-0x28]
    // 0xa38910: StoreField: r3->field_1f = r0
    //     0xa38910: stur            w0, [x3, #0x1f]
    // 0xa38914: r0 = true
    //     0xa38914: add             x0, NULL, #0x20  ; true
    // 0xa38918: StoreField: r3->field_27 = r0
    //     0xa38918: stur            w0, [x3, #0x27]
    // 0xa3891c: StoreField: r3->field_2b = r0
    //     0xa3891c: stur            w0, [x3, #0x2b]
    // 0xa38920: r1 = Null
    //     0xa38920: mov             x1, NULL
    // 0xa38924: r2 = 10
    //     0xa38924: movz            x2, #0xa
    // 0xa38928: r0 = AllocateArray()
    //     0xa38928: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3892c: mov             x2, x0
    // 0xa38930: ldur            x0, [fp, #-0x40]
    // 0xa38934: stur            x2, [fp, #-0x20]
    // 0xa38938: StoreField: r2->field_f = r0
    //     0xa38938: stur            w0, [x2, #0xf]
    // 0xa3893c: r16 = Instance_NSectionDivider
    //     0xa3893c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xa38940: ldr             x16, [x16, #0x38]
    // 0xa38944: StoreField: r2->field_13 = r16
    //     0xa38944: stur            w16, [x2, #0x13]
    // 0xa38948: ldur            x0, [fp, #-0x38]
    // 0xa3894c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3894c: stur            w0, [x2, #0x17]
    // 0xa38950: r16 = Instance_NSectionDivider
    //     0xa38950: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xa38954: ldr             x16, [x16, #0x38]
    // 0xa38958: StoreField: r2->field_1b = r16
    //     0xa38958: stur            w16, [x2, #0x1b]
    // 0xa3895c: ldur            x0, [fp, #-0x50]
    // 0xa38960: StoreField: r2->field_1f = r0
    //     0xa38960: stur            w0, [x2, #0x1f]
    // 0xa38964: r1 = <Widget>
    //     0xa38964: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa38968: r0 = AllocateGrowableArray()
    //     0xa38968: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3896c: mov             x1, x0
    // 0xa38970: ldur            x0, [fp, #-0x20]
    // 0xa38974: stur            x1, [fp, #-0x28]
    // 0xa38978: StoreField: r1->field_f = r0
    //     0xa38978: stur            w0, [x1, #0xf]
    // 0xa3897c: r0 = 10
    //     0xa3897c: movz            x0, #0xa
    // 0xa38980: StoreField: r1->field_b = r0
    //     0xa38980: stur            w0, [x1, #0xb]
    // 0xa38984: r0 = ListView()
    //     0xa38984: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xa38988: mov             x1, x0
    // 0xa3898c: ldur            x2, [fp, #-0x28]
    // 0xa38990: stur            x0, [fp, #-0x20]
    // 0xa38994: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa38994: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa38998: r0 = ListView()
    //     0xa38998: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xa3899c: r0 = RefreshIndicator()
    //     0xa3899c: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xa389a0: mov             x3, x0
    // 0xa389a4: ldur            x0, [fp, #-0x20]
    // 0xa389a8: stur            x3, [fp, #-0x28]
    // 0xa389ac: StoreField: r3->field_b = r0
    //     0xa389ac: stur            w0, [x3, #0xb]
    // 0xa389b0: d0 = 40.000000
    //     0xa389b0: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa389b4: StoreField: r3->field_f = d0
    //     0xa389b4: stur            d0, [x3, #0xf]
    // 0xa389b8: ArrayStore: r3[0] = rZR  ; List_8
    //     0xa389b8: stur            xzr, [x3, #0x17]
    // 0xa389bc: ldur            x0, [fp, #-0x30]
    // 0xa389c0: StoreField: r3->field_1f = r0
    //     0xa389c0: stur            w0, [x3, #0x1f]
    // 0xa389c4: r0 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xa389c4: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xa389c8: ldr             x0, [x0, #0xf58]
    // 0xa389cc: StoreField: r3->field_2f = r0
    //     0xa389cc: stur            w0, [x3, #0x2f]
    // 0xa389d0: d0 = 2.500000
    //     0xa389d0: fmov            d0, #2.50000000
    // 0xa389d4: StoreField: r3->field_3b = d0
    //     0xa389d4: stur            d0, [x3, #0x3b]
    // 0xa389d8: r0 = Instance_RefreshIndicatorTriggerMode
    //     0xa389d8: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xa389dc: ldr             x0, [x0, #0xa68]
    // 0xa389e0: StoreField: r3->field_47 = r0
    //     0xa389e0: stur            w0, [x3, #0x47]
    // 0xa389e4: d0 = 2.000000
    //     0xa389e4: fmov            d0, #2.00000000
    // 0xa389e8: StoreField: r3->field_4b = d0
    //     0xa389e8: stur            d0, [x3, #0x4b]
    // 0xa389ec: r0 = Instance__IndicatorType
    //     0xa389ec: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xa389f0: ldr             x0, [x0, #0xa70]
    // 0xa389f4: StoreField: r3->field_43 = r0
    //     0xa389f4: stur            w0, [x3, #0x43]
    // 0xa389f8: ldur            x0, [fp, #-8]
    // 0xa389fc: LoadField: r1 = r0->field_b
    //     0xa389fc: ldur            w1, [x0, #0xb]
    // 0xa38a00: DecompressPointer r1
    //     0xa38a00: add             x1, x1, HEAP, lsl #32
    // 0xa38a04: cmp             w1, NULL
    // 0xa38a08: b.eq            #0xa38b74
    // 0xa38a0c: LoadField: r0 = r1->field_b
    //     0xa38a0c: ldur            w0, [x1, #0xb]
    // 0xa38a10: DecompressPointer r0
    //     0xa38a10: add             x0, x0, HEAP, lsl #32
    // 0xa38a14: LoadField: r4 = r0->field_3f
    //     0xa38a14: ldur            w4, [x0, #0x3f]
    // 0xa38a18: DecompressPointer r4
    //     0xa38a18: add             x4, x4, HEAP, lsl #32
    // 0xa38a1c: ldur            x2, [fp, #-0x10]
    // 0xa38a20: stur            x4, [fp, #-8]
    // 0xa38a24: r1 = Function '<anonymous closure>':.
    //     0xa38a24: add             x1, PP, #0x40, lsl #12  ; [pp+0x40288] AnonymousClosure: (0xa39784), in [package:nuonline/app/modules/donation/widgets/transaction_pending.dart] _TransactionPendingState::build (0xa383f8)
    //     0xa38a28: ldr             x1, [x1, #0x288]
    // 0xa38a2c: r0 = AllocateClosure()
    //     0xa38a2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa38a30: stur            x0, [fp, #-0x10]
    // 0xa38a34: r0 = TextButton()
    //     0xa38a34: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa38a38: mov             x1, x0
    // 0xa38a3c: ldur            x0, [fp, #-0x10]
    // 0xa38a40: stur            x1, [fp, #-0x20]
    // 0xa38a44: StoreField: r1->field_b = r0
    //     0xa38a44: stur            w0, [x1, #0xb]
    // 0xa38a48: r0 = false
    //     0xa38a48: add             x0, NULL, #0x30  ; false
    // 0xa38a4c: StoreField: r1->field_27 = r0
    //     0xa38a4c: stur            w0, [x1, #0x27]
    // 0xa38a50: r2 = true
    //     0xa38a50: add             x2, NULL, #0x20  ; true
    // 0xa38a54: StoreField: r1->field_2f = r2
    //     0xa38a54: stur            w2, [x1, #0x2f]
    // 0xa38a58: r3 = Instance_Text
    //     0xa38a58: add             x3, PP, #0x40, lsl #12  ; [pp+0x40290] Obj!Text@e215f1
    //     0xa38a5c: ldr             x3, [x3, #0x290]
    // 0xa38a60: StoreField: r1->field_37 = r3
    //     0xa38a60: stur            w3, [x1, #0x37]
    // 0xa38a64: r0 = Container()
    //     0xa38a64: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa38a68: stur            x0, [fp, #-0x10]
    // 0xa38a6c: r16 = Instance_EdgeInsets
    //     0xa38a6c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28050] Obj!EdgeInsets@e12341
    //     0xa38a70: ldr             x16, [x16, #0x50]
    // 0xa38a74: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xa38a74: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xa38a78: ldr             lr, [lr, #0xc58]
    // 0xa38a7c: stp             lr, x16, [SP, #8]
    // 0xa38a80: ldur            x16, [fp, #-0x20]
    // 0xa38a84: str             x16, [SP]
    // 0xa38a88: mov             x1, x0
    // 0xa38a8c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0xa38a8c: add             x4, PP, #0x28, lsl #12  ; [pp+0x28058] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xa38a90: ldr             x4, [x4, #0x58]
    // 0xa38a94: r0 = Container()
    //     0xa38a94: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa38a98: r1 = Null
    //     0xa38a98: mov             x1, NULL
    // 0xa38a9c: r2 = 2
    //     0xa38a9c: movz            x2, #0x2
    // 0xa38aa0: r0 = AllocateArray()
    //     0xa38aa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38aa4: mov             x2, x0
    // 0xa38aa8: ldur            x0, [fp, #-0x10]
    // 0xa38aac: stur            x2, [fp, #-0x20]
    // 0xa38ab0: StoreField: r2->field_f = r0
    //     0xa38ab0: stur            w0, [x2, #0xf]
    // 0xa38ab4: r1 = <Widget>
    //     0xa38ab4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa38ab8: r0 = AllocateGrowableArray()
    //     0xa38ab8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38abc: mov             x1, x0
    // 0xa38ac0: ldur            x0, [fp, #-0x20]
    // 0xa38ac4: StoreField: r1->field_f = r0
    //     0xa38ac4: stur            w0, [x1, #0xf]
    // 0xa38ac8: r0 = 2
    //     0xa38ac8: movz            x0, #0x2
    // 0xa38acc: StoreField: r1->field_b = r0
    //     0xa38acc: stur            w0, [x1, #0xb]
    // 0xa38ad0: ldur            x0, [fp, #-8]
    // 0xa38ad4: r16 = Instance_PaymentMethod
    //     0xa38ad4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0xa38ad8: ldr             x16, [x16, #0x220]
    // 0xa38adc: cmp             w0, w16
    // 0xa38ae0: b.ne            #0xa38aec
    // 0xa38ae4: mov             x2, x1
    // 0xa38ae8: b               #0xa38af0
    // 0xa38aec: r2 = Null
    //     0xa38aec: mov             x2, NULL
    // 0xa38af0: ldur            x1, [fp, #-0x18]
    // 0xa38af4: ldur            x0, [fp, #-0x28]
    // 0xa38af8: stur            x2, [fp, #-8]
    // 0xa38afc: r0 = Scaffold()
    //     0xa38afc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xa38b00: ldur            x1, [fp, #-0x18]
    // 0xa38b04: StoreField: r0->field_13 = r1
    //     0xa38b04: stur            w1, [x0, #0x13]
    // 0xa38b08: ldur            x1, [fp, #-0x28]
    // 0xa38b0c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa38b0c: stur            w1, [x0, #0x17]
    // 0xa38b10: ldur            x1, [fp, #-8]
    // 0xa38b14: StoreField: r0->field_27 = r1
    //     0xa38b14: stur            w1, [x0, #0x27]
    // 0xa38b18: r1 = Instance_AlignmentDirectional
    //     0xa38b18: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xa38b1c: ldr             x1, [x1, #0x758]
    // 0xa38b20: StoreField: r0->field_2b = r1
    //     0xa38b20: stur            w1, [x0, #0x2b]
    // 0xa38b24: r1 = true
    //     0xa38b24: add             x1, NULL, #0x20  ; true
    // 0xa38b28: StoreField: r0->field_53 = r1
    //     0xa38b28: stur            w1, [x0, #0x53]
    // 0xa38b2c: r2 = Instance_DragStartBehavior
    //     0xa38b2c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa38b30: StoreField: r0->field_57 = r2
    //     0xa38b30: stur            w2, [x0, #0x57]
    // 0xa38b34: r2 = false
    //     0xa38b34: add             x2, NULL, #0x30  ; false
    // 0xa38b38: StoreField: r0->field_b = r2
    //     0xa38b38: stur            w2, [x0, #0xb]
    // 0xa38b3c: StoreField: r0->field_f = r2
    //     0xa38b3c: stur            w2, [x0, #0xf]
    // 0xa38b40: StoreField: r0->field_5f = r1
    //     0xa38b40: stur            w1, [x0, #0x5f]
    // 0xa38b44: StoreField: r0->field_63 = r1
    //     0xa38b44: stur            w1, [x0, #0x63]
    // 0xa38b48: LeaveFrame
    //     0xa38b48: mov             SP, fp
    //     0xa38b4c: ldp             fp, lr, [SP], #0x10
    // 0xa38b50: ret
    //     0xa38b50: ret             
    // 0xa38b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa38b54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38b58: b               #0xa38414
    // 0xa38b5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa38b74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa38b74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xa39784, size: 0xd0
    // 0xa39784: EnterFrame
    //     0xa39784: stp             fp, lr, [SP, #-0x10]!
    //     0xa39788: mov             fp, SP
    // 0xa3978c: AllocStack(0x18)
    //     0xa3978c: sub             SP, SP, #0x18
    // 0xa39790: SetupParameters(_TransactionPendingState this /* r1 */)
    //     0xa39790: stur            NULL, [fp, #-8]
    //     0xa39794: movz            x0, #0
    //     0xa39798: add             x1, fp, w0, sxtw #2
    //     0xa3979c: ldr             x1, [x1, #0x10]
    //     0xa397a0: ldur            w2, [x1, #0x17]
    //     0xa397a4: add             x2, x2, HEAP, lsl #32
    //     0xa397a8: stur            x2, [fp, #-0x10]
    // 0xa397ac: CheckStackOverflow
    //     0xa397ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa397b0: cmp             SP, x16
    //     0xa397b4: b.ls            #0xa3984c
    // 0xa397b8: InitAsync() -> Future<void?>
    //     0xa397b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa397bc: bl              #0x661298  ; InitAsyncStub
    // 0xa397c0: ldur            x0, [fp, #-0x10]
    // 0xa397c4: LoadField: r1 = r0->field_f
    //     0xa397c4: ldur            w1, [x0, #0xf]
    // 0xa397c8: DecompressPointer r1
    //     0xa397c8: add             x1, x1, HEAP, lsl #32
    // 0xa397cc: r0 = getReceiptImage()
    //     0xa397cc: bl              #0xa398fc  ; [package:nuonline/app/modules/donation/widgets/transaction_pending.dart] _TransactionPendingState::getReceiptImage
    // 0xa397d0: mov             x1, x0
    // 0xa397d4: stur            x1, [fp, #-0x18]
    // 0xa397d8: r0 = Await()
    //     0xa397d8: bl              #0x661044  ; AwaitStub
    // 0xa397dc: cmp             w0, NULL
    // 0xa397e0: b.eq            #0xa39830
    // 0xa397e4: r1 = LoadClassIdInstr(r0)
    //     0xa397e4: ldur            x1, [x0, #-1]
    //     0xa397e8: ubfx            x1, x1, #0xc, #0x14
    // 0xa397ec: mov             x16, x0
    // 0xa397f0: mov             x0, x1
    // 0xa397f4: mov             x1, x16
    // 0xa397f8: r0 = GDT[cid_x0 + -0xe06]()
    //     0xa397f8: sub             lr, x0, #0xe06
    //     0xa397fc: ldr             lr, [x21, lr, lsl #3]
    //     0xa39800: blr             lr
    // 0xa39804: mov             x1, x0
    // 0xa39808: r0 = saveFile()
    //     0xa39808: bl              #0xa39854  ; [package:image_gallery_saver/image_gallery_saver.dart] ImageGallerySaver::saveFile
    // 0xa3980c: mov             x1, x0
    // 0xa39810: stur            x1, [fp, #-0x18]
    // 0xa39814: r0 = Await()
    //     0xa39814: bl              #0x661044  ; AwaitStub
    // 0xa39818: r1 = "Berhasil menyimpan gambar!"
    //     0xa39818: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d270] "Berhasil menyimpan gambar!"
    //     0xa3981c: ldr             x1, [x1, #0x270]
    // 0xa39820: r2 = Instance_IconData
    //     0xa39820: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xa39824: ldr             x2, [x2, #0x590]
    // 0xa39828: r0 = show()
    //     0xa39828: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xa3982c: b               #0xa39844
    // 0xa39830: r1 = "Gagal menyimpan gambar!"
    //     0xa39830: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d268] "Gagal menyimpan gambar!"
    //     0xa39834: ldr             x1, [x1, #0x268]
    // 0xa39838: r2 = Instance_IconData
    //     0xa39838: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xa3983c: ldr             x2, [x2, #0x590]
    // 0xa39840: r0 = show()
    //     0xa39840: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xa39844: r0 = Null
    //     0xa39844: mov             x0, NULL
    // 0xa39848: r0 = ReturnAsyncNotFuture()
    //     0xa39848: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa3984c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3984c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa39850: b               #0xa397b8
  }
  _ getReceiptImage(/* No info */) async {
    // ** addr: 0xa398fc, size: 0x134
    // 0xa398fc: EnterFrame
    //     0xa398fc: stp             fp, lr, [SP, #-0x10]!
    //     0xa39900: mov             fp, SP
    // 0xa39904: AllocStack(0x20)
    //     0xa39904: sub             SP, SP, #0x20
    // 0xa39908: SetupParameters(_TransactionPendingState this /* r1 => r1, fp-0x10 */)
    //     0xa39908: stur            NULL, [fp, #-8]
    //     0xa3990c: stur            x1, [fp, #-0x10]
    // 0xa39910: CheckStackOverflow
    //     0xa39910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa39914: cmp             SP, x16
    //     0xa39918: b.ls            #0xa39a28
    // 0xa3991c: InitAsync() -> Future<File?>
    //     0xa3991c: add             x0, PP, #0x40, lsl #12  ; [pp+0x401c0] TypeArguments: <File?>
    //     0xa39920: ldr             x0, [x0, #0x1c0]
    //     0xa39924: bl              #0x661298  ; InitAsyncStub
    // 0xa39928: ldur            x0, [fp, #-0x10]
    // 0xa3992c: LoadField: r1 = r0->field_13
    //     0xa3992c: ldur            w1, [x0, #0x13]
    // 0xa39930: DecompressPointer r1
    //     0xa39930: add             x1, x1, HEAP, lsl #32
    // 0xa39934: r0 = capture()
    //     0xa39934: bl              #0xa39a30  ; [package:screenshot/screenshot.dart] ScreenshotController::capture
    // 0xa39938: mov             x1, x0
    // 0xa3993c: stur            x1, [fp, #-0x10]
    // 0xa39940: r0 = Await()
    //     0xa39940: bl              #0x661044  ; AwaitStub
    // 0xa39944: stur            x0, [fp, #-0x10]
    // 0xa39948: cmp             w0, NULL
    // 0xa3994c: b.eq            #0xa39a20
    // 0xa39950: r0 = getApplicationDocumentsDirectory()
    //     0xa39950: bl              #0x7ed868  ; [package:path_provider/path_provider.dart] ::getApplicationDocumentsDirectory
    // 0xa39954: mov             x1, x0
    // 0xa39958: stur            x1, [fp, #-0x18]
    // 0xa3995c: r0 = Await()
    //     0xa3995c: bl              #0x661044  ; AwaitStub
    // 0xa39960: r1 = LoadClassIdInstr(r0)
    //     0xa39960: ldur            x1, [x0, #-1]
    //     0xa39964: ubfx            x1, x1, #0xc, #0x14
    // 0xa39968: mov             x16, x0
    // 0xa3996c: mov             x0, x1
    // 0xa39970: mov             x1, x16
    // 0xa39974: r0 = GDT[cid_x0 + -0xe06]()
    //     0xa39974: sub             lr, x0, #0xe06
    //     0xa39978: ldr             lr, [x21, lr, lsl #3]
    //     0xa3997c: blr             lr
    // 0xa39980: r1 = Null
    //     0xa39980: mov             x1, NULL
    // 0xa39984: r2 = 4
    //     0xa39984: movz            x2, #0x4
    // 0xa39988: stur            x0, [fp, #-0x18]
    // 0xa3998c: r0 = AllocateArray()
    //     0xa3998c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa39990: mov             x1, x0
    // 0xa39994: ldur            x0, [fp, #-0x18]
    // 0xa39998: StoreField: r1->field_f = r0
    //     0xa39998: stur            w0, [x1, #0xf]
    // 0xa3999c: r16 = "/image.png"
    //     0xa3999c: add             x16, PP, #0x40, lsl #12  ; [pp+0x401c8] "/image.png"
    //     0xa399a0: ldr             x16, [x16, #0x1c8]
    // 0xa399a4: StoreField: r1->field_13 = r16
    //     0xa399a4: stur            w16, [x1, #0x13]
    // 0xa399a8: str             x1, [SP]
    // 0xa399ac: r0 = _interpolate()
    //     0xa399ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa399b0: stur            x0, [fp, #-0x18]
    // 0xa399b4: r0 = current()
    //     0xa399b4: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0xa399b8: r0 = _File()
    //     0xa399b8: bl              #0x60add8  ; Allocate_FileStub -> _File (size=0x10)
    // 0xa399bc: mov             x1, x0
    // 0xa399c0: ldur            x2, [fp, #-0x18]
    // 0xa399c4: stur            x0, [fp, #-0x18]
    // 0xa399c8: r0 = _File()
    //     0xa399c8: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0xa399cc: ldur            x1, [fp, #-0x18]
    // 0xa399d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa399d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa399d4: r0 = create()
    //     0xa399d4: bl              #0x835900  ; [dart:io] _File::create
    // 0xa399d8: mov             x1, x0
    // 0xa399dc: stur            x1, [fp, #-0x18]
    // 0xa399e0: r0 = Await()
    //     0xa399e0: bl              #0x661044  ; AwaitStub
    // 0xa399e4: mov             x3, x0
    // 0xa399e8: stur            x3, [fp, #-0x18]
    // 0xa399ec: r0 = LoadClassIdInstr(r3)
    //     0xa399ec: ldur            x0, [x3, #-1]
    //     0xa399f0: ubfx            x0, x0, #0xc, #0x14
    // 0xa399f4: mov             x1, x3
    // 0xa399f8: ldur            x2, [fp, #-0x10]
    // 0xa399fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa399fc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa39a00: r0 = GDT[cid_x0 + -0xf6c]()
    //     0xa39a00: sub             lr, x0, #0xf6c
    //     0xa39a04: ldr             lr, [x21, lr, lsl #3]
    //     0xa39a08: blr             lr
    // 0xa39a0c: mov             x1, x0
    // 0xa39a10: stur            x1, [fp, #-0x10]
    // 0xa39a14: r0 = Await()
    //     0xa39a14: bl              #0x661044  ; AwaitStub
    // 0xa39a18: ldur            x0, [fp, #-0x18]
    // 0xa39a1c: r0 = ReturnAsync()
    //     0xa39a1c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xa39a20: r0 = Null
    //     0xa39a20: mov             x0, NULL
    // 0xa39a24: r0 = ReturnAsyncNotFuture()
    //     0xa39a24: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa39a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa39a28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa39a2c: b               #0xa3991c
  }
}

// class id: 4713, size: 0x14, field offset: 0xc
//   const constructor, 
class TransactionPending extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa946f0, size: 0x48
    // 0xa946f0: EnterFrame
    //     0xa946f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa946f4: mov             fp, SP
    // 0xa946f8: AllocStack(0x8)
    //     0xa946f8: sub             SP, SP, #8
    // 0xa946fc: r0 = ScreenshotController()
    //     0xa946fc: bl              #0xa94744  ; AllocateScreenshotControllerStub -> ScreenshotController (size=0xc)
    // 0xa94700: r1 = <State<StatefulWidget>>
    //     0xa94700: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa94704: stur            x0, [fp, #-8]
    // 0xa94708: r0 = LabeledGlobalKey()
    //     0xa94708: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa9470c: mov             x1, x0
    // 0xa94710: ldur            x0, [fp, #-8]
    // 0xa94714: StoreField: r0->field_7 = r1
    //     0xa94714: stur            w1, [x0, #7]
    // 0xa94718: r1 = <TransactionPending>
    //     0xa94718: add             x1, PP, #0x35, lsl #12  ; [pp+0x35330] TypeArguments: <TransactionPending>
    //     0xa9471c: ldr             x1, [x1, #0x330]
    // 0xa94720: r0 = _TransactionPendingState()
    //     0xa94720: bl              #0xa94738  ; Allocate_TransactionPendingStateStub -> _TransactionPendingState (size=0x18)
    // 0xa94724: ldur            x1, [fp, #-8]
    // 0xa94728: StoreField: r0->field_13 = r1
    //     0xa94728: stur            w1, [x0, #0x13]
    // 0xa9472c: LeaveFrame
    //     0xa9472c: mov             SP, fp
    //     0xa94730: ldp             fp, lr, [SP], #0x10
    // 0xa94734: ret
    //     0xa94734: ret             
  }
}
