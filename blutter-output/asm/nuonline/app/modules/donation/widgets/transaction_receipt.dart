// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_receipt.dart

// class id: 1050250, size: 0x8
class :: {
}

// class id: 5019, size: 0x10, field offset: 0xc
//   const constructor, 
class TransactionReceipt extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb959d8, size: 0xa98
    // 0xb959d8: EnterFrame
    //     0xb959d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb959dc: mov             fp, SP
    // 0xb959e0: AllocStack(0x60)
    //     0xb959e0: sub             SP, SP, #0x60
    // 0xb959e4: SetupParameters(TransactionReceipt this /* r1 => r0, fp-0x8 */)
    //     0xb959e4: mov             x0, x1
    //     0xb959e8: stur            x1, [fp, #-8]
    // 0xb959ec: CheckStackOverflow
    //     0xb959ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb959f0: cmp             SP, x16
    //     0xb959f4: b.ls            #0xb96448
    // 0xb959f8: r16 = Instance_Brightness
    //     0xb959f8: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xb959fc: stp             NULL, x16, [SP]
    // 0xb95a00: r1 = Null
    //     0xb95a00: mov             x1, NULL
    // 0xb95a04: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xb95a04: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xb95a08: r0 = ThemeData()
    //     0xb95a08: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xb95a0c: stur            x0, [fp, #-0x10]
    // 0xb95a10: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb95a10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb95a14: ldr             x0, [x0, #0x2670]
    //     0xb95a18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb95a1c: cmp             w0, w16
    //     0xb95a20: b.ne            #0xb95a2c
    //     0xb95a24: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb95a28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb95a2c: r0 = GetNavigation.theme()
    //     0xb95a2c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb95a30: LoadField: r2 = r0->field_63
    //     0xb95a30: ldur            w2, [x0, #0x63]
    // 0xb95a34: DecompressPointer r2
    //     0xb95a34: add             x2, x2, HEAP, lsl #32
    // 0xb95a38: stur            x2, [fp, #-0x18]
    // 0xb95a3c: r1 = Null
    //     0xb95a3c: mov             x1, NULL
    // 0xb95a40: r0 = NLogo.darkOnly()
    //     0xb95a40: bl              #0xb96470  ; [package:nuikit/src/widgets/builtin/logo/logo.dart] NLogo::NLogo.darkOnly
    // 0xb95a44: mov             x1, x0
    // 0xb95a48: ldur            x0, [fp, #-8]
    // 0xb95a4c: stur            x1, [fp, #-0x28]
    // 0xb95a50: LoadField: r2 = r0->field_b
    //     0xb95a50: ldur            w2, [x0, #0xb]
    // 0xb95a54: DecompressPointer r2
    //     0xb95a54: add             x2, x2, HEAP, lsl #32
    // 0xb95a58: stur            x2, [fp, #-0x20]
    // 0xb95a5c: LoadField: r0 = r2->field_f
    //     0xb95a5c: ldur            w0, [x2, #0xf]
    // 0xb95a60: DecompressPointer r0
    //     0xb95a60: add             x0, x0, HEAP, lsl #32
    // 0xb95a64: stur            x0, [fp, #-8]
    // 0xb95a68: r0 = GetNavigation.textTheme()
    //     0xb95a68: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95a6c: LoadField: r1 = r0->field_2f
    //     0xb95a6c: ldur            w1, [x0, #0x2f]
    // 0xb95a70: DecompressPointer r1
    //     0xb95a70: add             x1, x1, HEAP, lsl #32
    // 0xb95a74: cmp             w1, NULL
    // 0xb95a78: b.eq            #0xb96450
    // 0xb95a7c: r16 = Instance_FontWeight
    //     0xb95a7c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb95a80: ldr             x16, [x16, #0xe20]
    // 0xb95a84: r30 = Instance_Color
    //     0xb95a84: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb95a88: stp             lr, x16, [SP]
    // 0xb95a8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0xb95a8c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27ff8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0xb95a90: ldr             x4, [x4, #0xff8]
    // 0xb95a94: r0 = copyWith()
    //     0xb95a94: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95a98: stur            x0, [fp, #-0x30]
    // 0xb95a9c: r0 = Text()
    //     0xb95a9c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95aa0: mov             x3, x0
    // 0xb95aa4: ldur            x0, [fp, #-8]
    // 0xb95aa8: stur            x3, [fp, #-0x38]
    // 0xb95aac: StoreField: r3->field_b = r0
    //     0xb95aac: stur            w0, [x3, #0xb]
    // 0xb95ab0: ldur            x0, [fp, #-0x30]
    // 0xb95ab4: StoreField: r3->field_13 = r0
    //     0xb95ab4: stur            w0, [x3, #0x13]
    // 0xb95ab8: r1 = Null
    //     0xb95ab8: mov             x1, NULL
    // 0xb95abc: r2 = 4
    //     0xb95abc: movz            x2, #0x4
    // 0xb95ac0: r0 = AllocateArray()
    //     0xb95ac0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb95ac4: mov             x2, x0
    // 0xb95ac8: ldur            x0, [fp, #-0x28]
    // 0xb95acc: stur            x2, [fp, #-8]
    // 0xb95ad0: StoreField: r2->field_f = r0
    //     0xb95ad0: stur            w0, [x2, #0xf]
    // 0xb95ad4: ldur            x0, [fp, #-0x38]
    // 0xb95ad8: StoreField: r2->field_13 = r0
    //     0xb95ad8: stur            w0, [x2, #0x13]
    // 0xb95adc: r1 = <Widget>
    //     0xb95adc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb95ae0: r0 = AllocateGrowableArray()
    //     0xb95ae0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb95ae4: mov             x1, x0
    // 0xb95ae8: ldur            x0, [fp, #-8]
    // 0xb95aec: stur            x1, [fp, #-0x28]
    // 0xb95af0: StoreField: r1->field_f = r0
    //     0xb95af0: stur            w0, [x1, #0xf]
    // 0xb95af4: r2 = 4
    //     0xb95af4: movz            x2, #0x4
    // 0xb95af8: StoreField: r1->field_b = r2
    //     0xb95af8: stur            w2, [x1, #0xb]
    // 0xb95afc: r0 = Row()
    //     0xb95afc: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb95b00: mov             x1, x0
    // 0xb95b04: r0 = Instance_Axis
    //     0xb95b04: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb95b08: stur            x1, [fp, #-8]
    // 0xb95b0c: StoreField: r1->field_f = r0
    //     0xb95b0c: stur            w0, [x1, #0xf]
    // 0xb95b10: r0 = Instance_MainAxisAlignment
    //     0xb95b10: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb95b14: ldr             x0, [x0, #0xae8]
    // 0xb95b18: StoreField: r1->field_13 = r0
    //     0xb95b18: stur            w0, [x1, #0x13]
    // 0xb95b1c: r0 = Instance_MainAxisSize
    //     0xb95b1c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb95b20: ldr             x0, [x0, #0x738]
    // 0xb95b24: ArrayStore: r1[0] = r0  ; List_4
    //     0xb95b24: stur            w0, [x1, #0x17]
    // 0xb95b28: r2 = Instance_CrossAxisAlignment
    //     0xb95b28: add             x2, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb95b2c: ldr             x2, [x2, #0x740]
    // 0xb95b30: StoreField: r1->field_1b = r2
    //     0xb95b30: stur            w2, [x1, #0x1b]
    // 0xb95b34: r3 = Instance_VerticalDirection
    //     0xb95b34: add             x3, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb95b38: ldr             x3, [x3, #0x748]
    // 0xb95b3c: StoreField: r1->field_23 = r3
    //     0xb95b3c: stur            w3, [x1, #0x23]
    // 0xb95b40: r4 = Instance_Clip
    //     0xb95b40: add             x4, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb95b44: ldr             x4, [x4, #0x750]
    // 0xb95b48: StoreField: r1->field_2b = r4
    //     0xb95b48: stur            w4, [x1, #0x2b]
    // 0xb95b4c: StoreField: r1->field_2f = rZR
    //     0xb95b4c: stur            xzr, [x1, #0x2f]
    // 0xb95b50: ldur            x5, [fp, #-0x28]
    // 0xb95b54: StoreField: r1->field_b = r5
    //     0xb95b54: stur            w5, [x1, #0xb]
    // 0xb95b58: r0 = Container()
    //     0xb95b58: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb95b5c: stur            x0, [fp, #-0x28]
    // 0xb95b60: ldur            x16, [fp, #-0x18]
    // 0xb95b64: r30 = Instance_EdgeInsets
    //     0xb95b64: add             lr, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb95b68: ldr             lr, [lr, #0x768]
    // 0xb95b6c: stp             lr, x16, [SP, #8]
    // 0xb95b70: ldur            x16, [fp, #-8]
    // 0xb95b74: str             x16, [SP]
    // 0xb95b78: mov             x1, x0
    // 0xb95b7c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, padding, 0x2, null]
    //     0xb95b7c: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b4f8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "padding", 0x2, Null]
    //     0xb95b80: ldr             x4, [x4, #0x4f8]
    // 0xb95b84: r0 = Container()
    //     0xb95b84: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb95b88: r0 = GetNavigation.textTheme()
    //     0xb95b88: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95b8c: LoadField: r3 = r0->field_27
    //     0xb95b8c: ldur            w3, [x0, #0x27]
    // 0xb95b90: DecompressPointer r3
    //     0xb95b90: add             x3, x3, HEAP, lsl #32
    // 0xb95b94: stur            x3, [fp, #-8]
    // 0xb95b98: cmp             w3, NULL
    // 0xb95b9c: b.eq            #0xb96454
    // 0xb95ba0: r1 = _ConstMap len:6
    //     0xb95ba0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb95ba4: ldr             x1, [x1, #0xc20]
    // 0xb95ba8: r2 = 8
    //     0xb95ba8: movz            x2, #0x8
    // 0xb95bac: r0 = []()
    //     0xb95bac: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb95bb0: str             x0, [SP]
    // 0xb95bb4: ldur            x1, [fp, #-8]
    // 0xb95bb8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb95bb8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb95bbc: ldr             x4, [x4, #0x228]
    // 0xb95bc0: r0 = copyWith()
    //     0xb95bc0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95bc4: stur            x0, [fp, #-8]
    // 0xb95bc8: r0 = Text()
    //     0xb95bc8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95bcc: mov             x3, x0
    // 0xb95bd0: r0 = "Waktu Pembayaran"
    //     0xb95bd0: add             x0, PP, #0x47, lsl #12  ; [pp+0x47ae0] "Waktu Pembayaran"
    //     0xb95bd4: ldr             x0, [x0, #0xae0]
    // 0xb95bd8: stur            x3, [fp, #-0x18]
    // 0xb95bdc: StoreField: r3->field_b = r0
    //     0xb95bdc: stur            w0, [x3, #0xb]
    // 0xb95be0: ldur            x0, [fp, #-8]
    // 0xb95be4: StoreField: r3->field_13 = r0
    //     0xb95be4: stur            w0, [x3, #0x13]
    // 0xb95be8: r1 = <Widget>
    //     0xb95be8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb95bec: r2 = 24
    //     0xb95bec: movz            x2, #0x18
    // 0xb95bf0: r0 = AllocateArray()
    //     0xb95bf0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb95bf4: mov             x2, x0
    // 0xb95bf8: ldur            x0, [fp, #-0x18]
    // 0xb95bfc: stur            x2, [fp, #-8]
    // 0xb95c00: StoreField: r2->field_f = r0
    //     0xb95c00: stur            w0, [x2, #0xf]
    // 0xb95c04: ldur            x0, [fp, #-0x20]
    // 0xb95c08: LoadField: r1 = r0->field_47
    //     0xb95c08: ldur            w1, [x0, #0x47]
    // 0xb95c0c: DecompressPointer r1
    //     0xb95c0c: add             x1, x1, HEAP, lsl #32
    // 0xb95c10: r0 = DateTimeExtensions.humanizeDateTimeWIB()
    //     0xb95c10: bl              #0xaf6544  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanizeDateTimeWIB
    // 0xb95c14: stur            x0, [fp, #-0x18]
    // 0xb95c18: r0 = GetNavigation.textTheme()
    //     0xb95c18: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95c1c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb95c1c: ldur            w3, [x0, #0x17]
    // 0xb95c20: DecompressPointer r3
    //     0xb95c20: add             x3, x3, HEAP, lsl #32
    // 0xb95c24: stur            x3, [fp, #-0x30]
    // 0xb95c28: cmp             w3, NULL
    // 0xb95c2c: b.eq            #0xb96458
    // 0xb95c30: r1 = _ConstMap len:3
    //     0xb95c30: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb95c34: ldr             x1, [x1, #0xbe8]
    // 0xb95c38: r2 = 6
    //     0xb95c38: movz            x2, #0x6
    // 0xb95c3c: r0 = []()
    //     0xb95c3c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb95c40: str             x0, [SP]
    // 0xb95c44: ldur            x1, [fp, #-0x30]
    // 0xb95c48: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb95c48: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb95c4c: ldr             x4, [x4, #0x228]
    // 0xb95c50: r0 = copyWith()
    //     0xb95c50: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95c54: stur            x0, [fp, #-0x30]
    // 0xb95c58: r0 = Text()
    //     0xb95c58: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95c5c: mov             x1, x0
    // 0xb95c60: ldur            x0, [fp, #-0x18]
    // 0xb95c64: StoreField: r1->field_b = r0
    //     0xb95c64: stur            w0, [x1, #0xb]
    // 0xb95c68: ldur            x0, [fp, #-0x30]
    // 0xb95c6c: StoreField: r1->field_13 = r0
    //     0xb95c6c: stur            w0, [x1, #0x13]
    // 0xb95c70: mov             x0, x1
    // 0xb95c74: ldur            x1, [fp, #-8]
    // 0xb95c78: ArrayStore: r1[1] = r0  ; List_4
    //     0xb95c78: add             x25, x1, #0x13
    //     0xb95c7c: str             w0, [x25]
    //     0xb95c80: tbz             w0, #0, #0xb95c9c
    //     0xb95c84: ldurb           w16, [x1, #-1]
    //     0xb95c88: ldurb           w17, [x0, #-1]
    //     0xb95c8c: and             x16, x17, x16, lsr #2
    //     0xb95c90: tst             x16, HEAP, lsr #32
    //     0xb95c94: b.eq            #0xb95c9c
    //     0xb95c98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95c9c: ldur            x1, [fp, #-8]
    // 0xb95ca0: r16 = Instance_SizedBox
    //     0xb95ca0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb95ca4: ldr             x16, [x16, #0x950]
    // 0xb95ca8: ArrayStore: r1[0] = r16  ; List_4
    //     0xb95ca8: stur            w16, [x1, #0x17]
    // 0xb95cac: r16 = Instance_NDottedDivider
    //     0xb95cac: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ae8] Obj!NDottedDivider@e20d31
    //     0xb95cb0: ldr             x16, [x16, #0xae8]
    // 0xb95cb4: StoreField: r1->field_1b = r16
    //     0xb95cb4: stur            w16, [x1, #0x1b]
    // 0xb95cb8: r16 = Instance_SizedBox
    //     0xb95cb8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb95cbc: ldr             x16, [x16, #0x950]
    // 0xb95cc0: StoreField: r1->field_1f = r16
    //     0xb95cc0: stur            w16, [x1, #0x1f]
    // 0xb95cc4: r0 = GetNavigation.textTheme()
    //     0xb95cc4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95cc8: LoadField: r3 = r0->field_27
    //     0xb95cc8: ldur            w3, [x0, #0x27]
    // 0xb95ccc: DecompressPointer r3
    //     0xb95ccc: add             x3, x3, HEAP, lsl #32
    // 0xb95cd0: stur            x3, [fp, #-0x18]
    // 0xb95cd4: cmp             w3, NULL
    // 0xb95cd8: b.eq            #0xb9645c
    // 0xb95cdc: r1 = _ConstMap len:6
    //     0xb95cdc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb95ce0: ldr             x1, [x1, #0xc20]
    // 0xb95ce4: r2 = 8
    //     0xb95ce4: movz            x2, #0x8
    // 0xb95ce8: r0 = []()
    //     0xb95ce8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb95cec: str             x0, [SP]
    // 0xb95cf0: ldur            x1, [fp, #-0x18]
    // 0xb95cf4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb95cf4: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb95cf8: ldr             x4, [x4, #0x228]
    // 0xb95cfc: r0 = copyWith()
    //     0xb95cfc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95d00: stur            x0, [fp, #-0x18]
    // 0xb95d04: r0 = Text()
    //     0xb95d04: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95d08: mov             x1, x0
    // 0xb95d0c: r0 = "Pembayaran Berhasil"
    //     0xb95d0c: add             x0, PP, #0x47, lsl #12  ; [pp+0x47af0] "Pembayaran Berhasil"
    //     0xb95d10: ldr             x0, [x0, #0xaf0]
    // 0xb95d14: StoreField: r1->field_b = r0
    //     0xb95d14: stur            w0, [x1, #0xb]
    // 0xb95d18: ldur            x0, [fp, #-0x18]
    // 0xb95d1c: StoreField: r1->field_13 = r0
    //     0xb95d1c: stur            w0, [x1, #0x13]
    // 0xb95d20: r2 = Instance_TextAlign
    //     0xb95d20: ldr             x2, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xb95d24: StoreField: r1->field_1b = r2
    //     0xb95d24: stur            w2, [x1, #0x1b]
    // 0xb95d28: mov             x0, x1
    // 0xb95d2c: ldur            x1, [fp, #-8]
    // 0xb95d30: ArrayStore: r1[5] = r0  ; List_4
    //     0xb95d30: add             x25, x1, #0x23
    //     0xb95d34: str             w0, [x25]
    //     0xb95d38: tbz             w0, #0, #0xb95d54
    //     0xb95d3c: ldurb           w16, [x1, #-1]
    //     0xb95d40: ldurb           w17, [x0, #-1]
    //     0xb95d44: and             x16, x17, x16, lsr #2
    //     0xb95d48: tst             x16, HEAP, lsr #32
    //     0xb95d4c: b.eq            #0xb95d54
    //     0xb95d50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95d54: ldur            x0, [fp, #-0x20]
    // 0xb95d58: LoadField: r1 = r0->field_1b
    //     0xb95d58: ldur            x1, [x0, #0x1b]
    // 0xb95d5c: r0 = IntExtension.idr()
    //     0xb95d5c: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb95d60: stur            x0, [fp, #-0x18]
    // 0xb95d64: r0 = GetNavigation.textTheme()
    //     0xb95d64: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb95d68: LoadField: r1 = r0->field_f
    //     0xb95d68: ldur            w1, [x0, #0xf]
    // 0xb95d6c: DecompressPointer r1
    //     0xb95d6c: add             x1, x1, HEAP, lsl #32
    // 0xb95d70: stur            x1, [fp, #-0x30]
    // 0xb95d74: cmp             w1, NULL
    // 0xb95d78: b.eq            #0xb96460
    // 0xb95d7c: r0 = GetNavigation.theme()
    //     0xb95d7c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb95d80: LoadField: r1 = r0->field_63
    //     0xb95d80: ldur            w1, [x0, #0x63]
    // 0xb95d84: DecompressPointer r1
    //     0xb95d84: add             x1, x1, HEAP, lsl #32
    // 0xb95d88: r16 = 24.000000
    //     0xb95d88: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xb95d8c: ldr             x16, [x16, #0x368]
    // 0xb95d90: stp             x1, x16, [SP]
    // 0xb95d94: ldur            x1, [fp, #-0x30]
    // 0xb95d98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95d98: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb95d9c: ldr             x4, [x4, #0xaa0]
    // 0xb95da0: r0 = copyWith()
    //     0xb95da0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95da4: stur            x0, [fp, #-0x30]
    // 0xb95da8: r0 = Text()
    //     0xb95da8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb95dac: mov             x1, x0
    // 0xb95db0: ldur            x0, [fp, #-0x18]
    // 0xb95db4: StoreField: r1->field_b = r0
    //     0xb95db4: stur            w0, [x1, #0xb]
    // 0xb95db8: ldur            x0, [fp, #-0x30]
    // 0xb95dbc: StoreField: r1->field_13 = r0
    //     0xb95dbc: stur            w0, [x1, #0x13]
    // 0xb95dc0: r0 = Instance_TextAlign
    //     0xb95dc0: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xb95dc4: StoreField: r1->field_1b = r0
    //     0xb95dc4: stur            w0, [x1, #0x1b]
    // 0xb95dc8: mov             x0, x1
    // 0xb95dcc: ldur            x1, [fp, #-8]
    // 0xb95dd0: ArrayStore: r1[6] = r0  ; List_4
    //     0xb95dd0: add             x25, x1, #0x27
    //     0xb95dd4: str             w0, [x25]
    //     0xb95dd8: tbz             w0, #0, #0xb95df4
    //     0xb95ddc: ldurb           w16, [x1, #-1]
    //     0xb95de0: ldurb           w17, [x0, #-1]
    //     0xb95de4: and             x16, x17, x16, lsr #2
    //     0xb95de8: tst             x16, HEAP, lsr #32
    //     0xb95dec: b.eq            #0xb95df4
    //     0xb95df0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95df4: ldur            x0, [fp, #-8]
    // 0xb95df8: r16 = Instance_SizedBox
    //     0xb95df8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb95dfc: ldr             x16, [x16, #0x950]
    // 0xb95e00: StoreField: r0->field_2b = r16
    //     0xb95e00: stur            w16, [x0, #0x2b]
    // 0xb95e04: r16 = Instance_NDottedDivider
    //     0xb95e04: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ae8] Obj!NDottedDivider@e20d31
    //     0xb95e08: ldr             x16, [x16, #0xae8]
    // 0xb95e0c: StoreField: r0->field_2f = r16
    //     0xb95e0c: stur            w16, [x0, #0x2f]
    // 0xb95e10: r16 = Instance_SizedBox
    //     0xb95e10: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb95e14: ldr             x16, [x16, #0x950]
    // 0xb95e18: StoreField: r0->field_33 = r16
    //     0xb95e18: stur            w16, [x0, #0x33]
    // 0xb95e1c: ldur            x2, [fp, #-0x20]
    // 0xb95e20: LoadField: r1 = r2->field_2b
    //     0xb95e20: ldur            w1, [x2, #0x2b]
    // 0xb95e24: DecompressPointer r1
    //     0xb95e24: add             x1, x1, HEAP, lsl #32
    // 0xb95e28: LoadField: r3 = r1->field_7
    //     0xb95e28: ldur            x3, [x1, #7]
    // 0xb95e2c: cmp             x3, #0xd
    // 0xb95e30: b.eq            #0xb95e40
    // 0xb95e34: r0 = categoryTitle()
    //     0xb95e34: bl              #0xb94318  ; [package:nuonline/app/data/models/transaction.dart] Donation::categoryTitle
    // 0xb95e38: mov             x2, x0
    // 0xb95e3c: b               #0xb95e48
    // 0xb95e40: r2 = "Pengembangan NU Online"
    //     0xb95e40: add             x2, PP, #0x35, lsl #12  ; [pp+0x355e8] "Pengembangan NU Online"
    //     0xb95e44: ldr             x2, [x2, #0x5e8]
    // 0xb95e48: ldur            x0, [fp, #-0x20]
    // 0xb95e4c: ldur            x1, [fp, #-8]
    // 0xb95e50: stur            x2, [fp, #-0x18]
    // 0xb95e54: r0 = UserInfo()
    //     0xb95e54: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb95e58: mov             x1, x0
    // 0xb95e5c: r0 = "Deskripsi"
    //     0xb95e5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f658] "Deskripsi"
    //     0xb95e60: ldr             x0, [x0, #0x658]
    // 0xb95e64: StoreField: r1->field_b = r0
    //     0xb95e64: stur            w0, [x1, #0xb]
    // 0xb95e68: ldur            x0, [fp, #-0x18]
    // 0xb95e6c: StoreField: r1->field_f = r0
    //     0xb95e6c: stur            w0, [x1, #0xf]
    // 0xb95e70: r2 = 14.000000
    //     0xb95e70: add             x2, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb95e74: ldr             x2, [x2, #0x9a0]
    // 0xb95e78: StoreField: r1->field_13 = r2
    //     0xb95e78: stur            w2, [x1, #0x13]
    // 0xb95e7c: r3 = true
    //     0xb95e7c: add             x3, NULL, #0x20  ; true
    // 0xb95e80: ArrayStore: r1[0] = r3  ; List_4
    //     0xb95e80: stur            w3, [x1, #0x17]
    // 0xb95e84: mov             x0, x1
    // 0xb95e88: ldur            x1, [fp, #-8]
    // 0xb95e8c: ArrayStore: r1[10] = r0  ; List_4
    //     0xb95e8c: add             x25, x1, #0x37
    //     0xb95e90: str             w0, [x25]
    //     0xb95e94: tbz             w0, #0, #0xb95eb0
    //     0xb95e98: ldurb           w16, [x1, #-1]
    //     0xb95e9c: ldurb           w17, [x0, #-1]
    //     0xb95ea0: and             x16, x17, x16, lsr #2
    //     0xb95ea4: tst             x16, HEAP, lsr #32
    //     0xb95ea8: b.eq            #0xb95eb0
    //     0xb95eac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95eb0: ldur            x0, [fp, #-0x20]
    // 0xb95eb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb95eb4: ldur            w1, [x0, #0x17]
    // 0xb95eb8: DecompressPointer r1
    //     0xb95eb8: add             x1, x1, HEAP, lsl #32
    // 0xb95ebc: stur            x1, [fp, #-0x18]
    // 0xb95ec0: r0 = UserInfo()
    //     0xb95ec0: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb95ec4: mov             x1, x0
    // 0xb95ec8: r0 = "Nama"
    //     0xb95ec8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30510] "Nama"
    //     0xb95ecc: ldr             x0, [x0, #0x510]
    // 0xb95ed0: StoreField: r1->field_b = r0
    //     0xb95ed0: stur            w0, [x1, #0xb]
    // 0xb95ed4: ldur            x0, [fp, #-0x18]
    // 0xb95ed8: StoreField: r1->field_f = r0
    //     0xb95ed8: stur            w0, [x1, #0xf]
    // 0xb95edc: r2 = 14.000000
    //     0xb95edc: add             x2, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb95ee0: ldr             x2, [x2, #0x9a0]
    // 0xb95ee4: StoreField: r1->field_13 = r2
    //     0xb95ee4: stur            w2, [x1, #0x13]
    // 0xb95ee8: r3 = true
    //     0xb95ee8: add             x3, NULL, #0x20  ; true
    // 0xb95eec: ArrayStore: r1[0] = r3  ; List_4
    //     0xb95eec: stur            w3, [x1, #0x17]
    // 0xb95ef0: mov             x0, x1
    // 0xb95ef4: ldur            x1, [fp, #-8]
    // 0xb95ef8: ArrayStore: r1[11] = r0  ; List_4
    //     0xb95ef8: add             x25, x1, #0x3b
    //     0xb95efc: str             w0, [x25]
    //     0xb95f00: tbz             w0, #0, #0xb95f1c
    //     0xb95f04: ldurb           w16, [x1, #-1]
    //     0xb95f08: ldurb           w17, [x0, #-1]
    //     0xb95f0c: and             x16, x17, x16, lsr #2
    //     0xb95f10: tst             x16, HEAP, lsr #32
    //     0xb95f14: b.eq            #0xb95f1c
    //     0xb95f18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95f1c: r1 = <Widget>
    //     0xb95f1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb95f20: r0 = AllocateGrowableArray()
    //     0xb95f20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb95f24: mov             x1, x0
    // 0xb95f28: ldur            x0, [fp, #-8]
    // 0xb95f2c: stur            x1, [fp, #-0x30]
    // 0xb95f30: StoreField: r1->field_f = r0
    //     0xb95f30: stur            w0, [x1, #0xf]
    // 0xb95f34: r2 = 24
    //     0xb95f34: movz            x2, #0x18
    // 0xb95f38: StoreField: r1->field_b = r2
    //     0xb95f38: stur            w2, [x1, #0xb]
    // 0xb95f3c: ldur            x2, [fp, #-0x20]
    // 0xb95f40: LoadField: r3 = r2->field_33
    //     0xb95f40: ldur            w3, [x2, #0x33]
    // 0xb95f44: DecompressPointer r3
    //     0xb95f44: add             x3, x3, HEAP, lsl #32
    // 0xb95f48: stur            x3, [fp, #-0x18]
    // 0xb95f4c: cmp             w3, NULL
    // 0xb95f50: b.eq            #0xb95fd8
    // 0xb95f54: r0 = UserInfo()
    //     0xb95f54: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb95f58: mov             x2, x0
    // 0xb95f5c: r0 = "Email"
    //     0xb95f5c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30518] "Email"
    //     0xb95f60: ldr             x0, [x0, #0x518]
    // 0xb95f64: stur            x2, [fp, #-0x38]
    // 0xb95f68: StoreField: r2->field_b = r0
    //     0xb95f68: stur            w0, [x2, #0xb]
    // 0xb95f6c: ldur            x0, [fp, #-0x18]
    // 0xb95f70: StoreField: r2->field_f = r0
    //     0xb95f70: stur            w0, [x2, #0xf]
    // 0xb95f74: r0 = 14.000000
    //     0xb95f74: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb95f78: ldr             x0, [x0, #0x9a0]
    // 0xb95f7c: StoreField: r2->field_13 = r0
    //     0xb95f7c: stur            w0, [x2, #0x13]
    // 0xb95f80: r3 = true
    //     0xb95f80: add             x3, NULL, #0x20  ; true
    // 0xb95f84: ArrayStore: r2[0] = r3  ; List_4
    //     0xb95f84: stur            w3, [x2, #0x17]
    // 0xb95f88: ldur            x1, [fp, #-0x30]
    // 0xb95f8c: r0 = _growToNextCapacity()
    //     0xb95f8c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb95f90: ldur            x2, [fp, #-0x30]
    // 0xb95f94: r0 = 26
    //     0xb95f94: movz            x0, #0x1a
    // 0xb95f98: StoreField: r2->field_b = r0
    //     0xb95f98: stur            w0, [x2, #0xb]
    // 0xb95f9c: LoadField: r3 = r2->field_f
    //     0xb95f9c: ldur            w3, [x2, #0xf]
    // 0xb95fa0: DecompressPointer r3
    //     0xb95fa0: add             x3, x3, HEAP, lsl #32
    // 0xb95fa4: mov             x1, x3
    // 0xb95fa8: ldur            x0, [fp, #-0x38]
    // 0xb95fac: ArrayStore: r1[12] = r0  ; List_4
    //     0xb95fac: add             x25, x1, #0x3f
    //     0xb95fb0: str             w0, [x25]
    //     0xb95fb4: tbz             w0, #0, #0xb95fd0
    //     0xb95fb8: ldurb           w16, [x1, #-1]
    //     0xb95fbc: ldurb           w17, [x0, #-1]
    //     0xb95fc0: and             x16, x17, x16, lsr #2
    //     0xb95fc4: tst             x16, HEAP, lsr #32
    //     0xb95fc8: b.eq            #0xb95fd0
    //     0xb95fcc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95fd0: r1 = 13
    //     0xb95fd0: movz            x1, #0xd
    // 0xb95fd4: b               #0xb95fe4
    // 0xb95fd8: mov             x2, x1
    // 0xb95fdc: mov             x3, x0
    // 0xb95fe0: r1 = 12
    //     0xb95fe0: movz            x1, #0xc
    // 0xb95fe4: ldur            x0, [fp, #-0x20]
    // 0xb95fe8: stur            x3, [fp, #-0x18]
    // 0xb95fec: stur            x1, [fp, #-0x40]
    // 0xb95ff0: LoadField: r4 = r0->field_2f
    //     0xb95ff0: ldur            w4, [x0, #0x2f]
    // 0xb95ff4: DecompressPointer r4
    //     0xb95ff4: add             x4, x4, HEAP, lsl #32
    // 0xb95ff8: stur            x4, [fp, #-8]
    // 0xb95ffc: cmp             w4, NULL
    // 0xb96000: b.eq            #0xb960c0
    // 0xb96004: r0 = UserInfo()
    //     0xb96004: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb96008: mov             x2, x0
    // 0xb9600c: r0 = "No. HP"
    //     0xb9600c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30520] "No. HP"
    //     0xb96010: ldr             x0, [x0, #0x520]
    // 0xb96014: stur            x2, [fp, #-0x38]
    // 0xb96018: StoreField: r2->field_b = r0
    //     0xb96018: stur            w0, [x2, #0xb]
    // 0xb9601c: ldur            x0, [fp, #-8]
    // 0xb96020: StoreField: r2->field_f = r0
    //     0xb96020: stur            w0, [x2, #0xf]
    // 0xb96024: r0 = 14.000000
    //     0xb96024: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb96028: ldr             x0, [x0, #0x9a0]
    // 0xb9602c: StoreField: r2->field_13 = r0
    //     0xb9602c: stur            w0, [x2, #0x13]
    // 0xb96030: r3 = true
    //     0xb96030: add             x3, NULL, #0x20  ; true
    // 0xb96034: ArrayStore: r2[0] = r3  ; List_4
    //     0xb96034: stur            w3, [x2, #0x17]
    // 0xb96038: ldur            x1, [fp, #-0x18]
    // 0xb9603c: LoadField: r4 = r1->field_b
    //     0xb9603c: ldur            w4, [x1, #0xb]
    // 0xb96040: r1 = LoadInt32Instr(r4)
    //     0xb96040: sbfx            x1, x4, #1, #0x1f
    // 0xb96044: ldur            x4, [fp, #-0x40]
    // 0xb96048: cmp             x4, x1
    // 0xb9604c: b.ne            #0xb96058
    // 0xb96050: ldur            x1, [fp, #-0x30]
    // 0xb96054: r0 = _growToNextCapacity()
    //     0xb96054: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96058: ldur            x3, [fp, #-0x30]
    // 0xb9605c: ldur            x2, [fp, #-0x40]
    // 0xb96060: add             x4, x2, #1
    // 0xb96064: lsl             x0, x4, #1
    // 0xb96068: StoreField: r3->field_b = r0
    //     0xb96068: stur            w0, [x3, #0xb]
    // 0xb9606c: mov             x0, x4
    // 0xb96070: mov             x1, x2
    // 0xb96074: cmp             x1, x0
    // 0xb96078: b.hs            #0xb96464
    // 0xb9607c: LoadField: r5 = r3->field_f
    //     0xb9607c: ldur            w5, [x3, #0xf]
    // 0xb96080: DecompressPointer r5
    //     0xb96080: add             x5, x5, HEAP, lsl #32
    // 0xb96084: mov             x1, x5
    // 0xb96088: ldur            x0, [fp, #-0x38]
    // 0xb9608c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb9608c: add             x25, x1, x2, lsl #2
    //     0xb96090: add             x25, x25, #0xf
    //     0xb96094: str             w0, [x25]
    //     0xb96098: tbz             w0, #0, #0xb960b4
    //     0xb9609c: ldurb           w16, [x1, #-1]
    //     0xb960a0: ldurb           w17, [x0, #-1]
    //     0xb960a4: and             x16, x17, x16, lsr #2
    //     0xb960a8: tst             x16, HEAP, lsr #32
    //     0xb960ac: b.eq            #0xb960b4
    //     0xb960b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb960b4: mov             x2, x5
    // 0xb960b8: mov             x1, x4
    // 0xb960bc: b               #0xb960e4
    // 0xb960c0: mov             x16, x1
    // 0xb960c4: mov             x1, x2
    // 0xb960c8: mov             x2, x16
    // 0xb960cc: mov             x16, x3
    // 0xb960d0: mov             x3, x1
    // 0xb960d4: mov             x1, x16
    // 0xb960d8: mov             x16, x2
    // 0xb960dc: mov             x2, x1
    // 0xb960e0: mov             x1, x16
    // 0xb960e4: ldur            x0, [fp, #-0x20]
    // 0xb960e8: stur            x2, [fp, #-0x18]
    // 0xb960ec: stur            x1, [fp, #-0x40]
    // 0xb960f0: LoadField: r4 = r0->field_3f
    //     0xb960f0: ldur            w4, [x0, #0x3f]
    // 0xb960f4: DecompressPointer r4
    //     0xb960f4: add             x4, x4, HEAP, lsl #32
    // 0xb960f8: LoadField: r0 = r4->field_7
    //     0xb960f8: ldur            x0, [x4, #7]
    // 0xb960fc: cmp             x0, #1
    // 0xb96100: b.gt            #0xb96124
    // 0xb96104: cmp             x0, #0
    // 0xb96108: b.gt            #0xb96118
    // 0xb9610c: r0 = "QRIS"
    //     0xb9610c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff50] "QRIS"
    //     0xb96110: ldr             x0, [x0, #0xf50]
    // 0xb96114: b               #0xb96140
    // 0xb96118: r0 = "BNI Virtual Account"
    //     0xb96118: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff58] "BNI Virtual Account"
    //     0xb9611c: ldr             x0, [x0, #0xf58]
    // 0xb96120: b               #0xb96140
    // 0xb96124: cmp             x0, #2
    // 0xb96128: b.gt            #0xb96138
    // 0xb9612c: r0 = "BSI Virtual Account"
    //     0xb9612c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff60] "BSI Virtual Account"
    //     0xb96130: ldr             x0, [x0, #0xf60]
    // 0xb96134: b               #0xb96140
    // 0xb96138: r0 = "BRI Virtual Account"
    //     0xb96138: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2ff68] "BRI Virtual Account"
    //     0xb9613c: ldr             x0, [x0, #0xf68]
    // 0xb96140: stur            x0, [fp, #-8]
    // 0xb96144: r0 = UserInfo()
    //     0xb96144: bl              #0xae1944  ; AllocateUserInfoStub -> UserInfo (size=0x24)
    // 0xb96148: mov             x2, x0
    // 0xb9614c: r0 = "Metode Pembayaran"
    //     0xb9614c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] "Metode Pembayaran"
    //     0xb96150: ldr             x0, [x0, #0xe08]
    // 0xb96154: stur            x2, [fp, #-0x20]
    // 0xb96158: StoreField: r2->field_b = r0
    //     0xb96158: stur            w0, [x2, #0xb]
    // 0xb9615c: ldur            x0, [fp, #-8]
    // 0xb96160: StoreField: r2->field_f = r0
    //     0xb96160: stur            w0, [x2, #0xf]
    // 0xb96164: r0 = 14.000000
    //     0xb96164: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb96168: ldr             x0, [x0, #0x9a0]
    // 0xb9616c: StoreField: r2->field_13 = r0
    //     0xb9616c: stur            w0, [x2, #0x13]
    // 0xb96170: r0 = true
    //     0xb96170: add             x0, NULL, #0x20  ; true
    // 0xb96174: ArrayStore: r2[0] = r0  ; List_4
    //     0xb96174: stur            w0, [x2, #0x17]
    // 0xb96178: ldur            x1, [fp, #-0x18]
    // 0xb9617c: LoadField: r3 = r1->field_b
    //     0xb9617c: ldur            w3, [x1, #0xb]
    // 0xb96180: r1 = LoadInt32Instr(r3)
    //     0xb96180: sbfx            x1, x3, #1, #0x1f
    // 0xb96184: ldur            x3, [fp, #-0x40]
    // 0xb96188: cmp             x3, x1
    // 0xb9618c: b.ne            #0xb96198
    // 0xb96190: ldur            x1, [fp, #-0x30]
    // 0xb96194: r0 = _growToNextCapacity()
    //     0xb96194: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96198: ldur            x3, [fp, #-0x30]
    // 0xb9619c: ldur            x2, [fp, #-0x40]
    // 0xb961a0: add             x4, x2, #1
    // 0xb961a4: stur            x4, [fp, #-0x48]
    // 0xb961a8: lsl             x0, x4, #1
    // 0xb961ac: StoreField: r3->field_b = r0
    //     0xb961ac: stur            w0, [x3, #0xb]
    // 0xb961b0: mov             x0, x4
    // 0xb961b4: mov             x1, x2
    // 0xb961b8: cmp             x1, x0
    // 0xb961bc: b.hs            #0xb96468
    // 0xb961c0: LoadField: r5 = r3->field_f
    //     0xb961c0: ldur            w5, [x3, #0xf]
    // 0xb961c4: DecompressPointer r5
    //     0xb961c4: add             x5, x5, HEAP, lsl #32
    // 0xb961c8: mov             x1, x5
    // 0xb961cc: ldur            x0, [fp, #-0x20]
    // 0xb961d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb961d0: add             x25, x1, x2, lsl #2
    //     0xb961d4: add             x25, x25, #0xf
    //     0xb961d8: str             w0, [x25]
    //     0xb961dc: tbz             w0, #0, #0xb961f8
    //     0xb961e0: ldurb           w16, [x1, #-1]
    //     0xb961e4: ldurb           w17, [x0, #-1]
    //     0xb961e8: and             x16, x17, x16, lsr #2
    //     0xb961ec: tst             x16, HEAP, lsr #32
    //     0xb961f0: b.eq            #0xb961f8
    //     0xb961f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb961f8: LoadField: r0 = r5->field_b
    //     0xb961f8: ldur            w0, [x5, #0xb]
    // 0xb961fc: r1 = LoadInt32Instr(r0)
    //     0xb961fc: sbfx            x1, x0, #1, #0x1f
    // 0xb96200: cmp             x4, x1
    // 0xb96204: b.ne            #0xb96210
    // 0xb96208: mov             x1, x3
    // 0xb9620c: r0 = _growToNextCapacity()
    //     0xb9620c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96210: ldur            x4, [fp, #-0x28]
    // 0xb96214: ldur            x3, [fp, #-0x48]
    // 0xb96218: ldur            x5, [fp, #-0x10]
    // 0xb9621c: ldur            x2, [fp, #-0x30]
    // 0xb96220: add             x0, x3, #1
    // 0xb96224: lsl             x1, x0, #1
    // 0xb96228: StoreField: r2->field_b = r1
    //     0xb96228: stur            w1, [x2, #0xb]
    // 0xb9622c: mov             x1, x3
    // 0xb96230: cmp             x1, x0
    // 0xb96234: b.hs            #0xb9646c
    // 0xb96238: LoadField: r0 = r2->field_f
    //     0xb96238: ldur            w0, [x2, #0xf]
    // 0xb9623c: DecompressPointer r0
    //     0xb9623c: add             x0, x0, HEAP, lsl #32
    // 0xb96240: add             x1, x0, x3, lsl #2
    // 0xb96244: r16 = Instance_SizedBox
    //     0xb96244: add             x16, PP, #0x47, lsl #12  ; [pp+0x47af8] Obj!SizedBox@e1e5c1
    //     0xb96248: ldr             x16, [x16, #0xaf8]
    // 0xb9624c: StoreField: r1->field_f = r16
    //     0xb9624c: stur            w16, [x1, #0xf]
    // 0xb96250: r0 = Column()
    //     0xb96250: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb96254: mov             x1, x0
    // 0xb96258: r0 = Instance_Axis
    //     0xb96258: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb9625c: stur            x1, [fp, #-8]
    // 0xb96260: StoreField: r1->field_f = r0
    //     0xb96260: stur            w0, [x1, #0xf]
    // 0xb96264: r2 = Instance_MainAxisAlignment
    //     0xb96264: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb96268: ldr             x2, [x2, #0x730]
    // 0xb9626c: StoreField: r1->field_13 = r2
    //     0xb9626c: stur            w2, [x1, #0x13]
    // 0xb96270: r3 = Instance_MainAxisSize
    //     0xb96270: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb96274: ldr             x3, [x3, #0x738]
    // 0xb96278: ArrayStore: r1[0] = r3  ; List_4
    //     0xb96278: stur            w3, [x1, #0x17]
    // 0xb9627c: r4 = Instance_CrossAxisAlignment
    //     0xb9627c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xb96280: ldr             x4, [x4, #0xf50]
    // 0xb96284: StoreField: r1->field_1b = r4
    //     0xb96284: stur            w4, [x1, #0x1b]
    // 0xb96288: r4 = Instance_VerticalDirection
    //     0xb96288: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9628c: ldr             x4, [x4, #0x748]
    // 0xb96290: StoreField: r1->field_23 = r4
    //     0xb96290: stur            w4, [x1, #0x23]
    // 0xb96294: r5 = Instance_Clip
    //     0xb96294: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb96298: ldr             x5, [x5, #0x750]
    // 0xb9629c: StoreField: r1->field_2b = r5
    //     0xb9629c: stur            w5, [x1, #0x2b]
    // 0xb962a0: StoreField: r1->field_2f = rZR
    //     0xb962a0: stur            xzr, [x1, #0x2f]
    // 0xb962a4: ldur            x6, [fp, #-0x30]
    // 0xb962a8: StoreField: r1->field_b = r6
    //     0xb962a8: stur            w6, [x1, #0xb]
    // 0xb962ac: r0 = Padding()
    //     0xb962ac: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb962b0: mov             x3, x0
    // 0xb962b4: r0 = Instance_EdgeInsets
    //     0xb962b4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb962b8: ldr             x0, [x0, #0x768]
    // 0xb962bc: stur            x3, [fp, #-0x18]
    // 0xb962c0: StoreField: r3->field_f = r0
    //     0xb962c0: stur            w0, [x3, #0xf]
    // 0xb962c4: ldur            x0, [fp, #-8]
    // 0xb962c8: StoreField: r3->field_b = r0
    //     0xb962c8: stur            w0, [x3, #0xb]
    // 0xb962cc: r1 = Null
    //     0xb962cc: mov             x1, NULL
    // 0xb962d0: r2 = 4
    //     0xb962d0: movz            x2, #0x4
    // 0xb962d4: r0 = AllocateArray()
    //     0xb962d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb962d8: mov             x2, x0
    // 0xb962dc: ldur            x0, [fp, #-0x28]
    // 0xb962e0: stur            x2, [fp, #-8]
    // 0xb962e4: StoreField: r2->field_f = r0
    //     0xb962e4: stur            w0, [x2, #0xf]
    // 0xb962e8: ldur            x0, [fp, #-0x18]
    // 0xb962ec: StoreField: r2->field_13 = r0
    //     0xb962ec: stur            w0, [x2, #0x13]
    // 0xb962f0: r1 = <Widget>
    //     0xb962f0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb962f4: r0 = AllocateGrowableArray()
    //     0xb962f4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb962f8: mov             x1, x0
    // 0xb962fc: ldur            x0, [fp, #-8]
    // 0xb96300: stur            x1, [fp, #-0x18]
    // 0xb96304: StoreField: r1->field_f = r0
    //     0xb96304: stur            w0, [x1, #0xf]
    // 0xb96308: r0 = 4
    //     0xb96308: movz            x0, #0x4
    // 0xb9630c: StoreField: r1->field_b = r0
    //     0xb9630c: stur            w0, [x1, #0xb]
    // 0xb96310: r0 = Column()
    //     0xb96310: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb96314: mov             x1, x0
    // 0xb96318: r0 = Instance_Axis
    //     0xb96318: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb9631c: stur            x1, [fp, #-8]
    // 0xb96320: StoreField: r1->field_f = r0
    //     0xb96320: stur            w0, [x1, #0xf]
    // 0xb96324: r0 = Instance_MainAxisAlignment
    //     0xb96324: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb96328: ldr             x0, [x0, #0x730]
    // 0xb9632c: StoreField: r1->field_13 = r0
    //     0xb9632c: stur            w0, [x1, #0x13]
    // 0xb96330: r0 = Instance_MainAxisSize
    //     0xb96330: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb96334: ldr             x0, [x0, #0x738]
    // 0xb96338: ArrayStore: r1[0] = r0  ; List_4
    //     0xb96338: stur            w0, [x1, #0x17]
    // 0xb9633c: r0 = Instance_CrossAxisAlignment
    //     0xb9633c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb96340: ldr             x0, [x0, #0x740]
    // 0xb96344: StoreField: r1->field_1b = r0
    //     0xb96344: stur            w0, [x1, #0x1b]
    // 0xb96348: r0 = Instance_VerticalDirection
    //     0xb96348: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9634c: ldr             x0, [x0, #0x748]
    // 0xb96350: StoreField: r1->field_23 = r0
    //     0xb96350: stur            w0, [x1, #0x23]
    // 0xb96354: r0 = Instance_Clip
    //     0xb96354: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb96358: ldr             x0, [x0, #0x750]
    // 0xb9635c: StoreField: r1->field_2b = r0
    //     0xb9635c: stur            w0, [x1, #0x2b]
    // 0xb96360: StoreField: r1->field_2f = rZR
    //     0xb96360: stur            xzr, [x1, #0x2f]
    // 0xb96364: ldur            x0, [fp, #-0x18]
    // 0xb96368: StoreField: r1->field_b = r0
    //     0xb96368: stur            w0, [x1, #0xb]
    // 0xb9636c: r0 = DecoratedBox()
    //     0xb9636c: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb96370: mov             x1, x0
    // 0xb96374: r0 = Instance_BoxDecoration
    //     0xb96374: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b00] Obj!BoxDecoration@e1d481
    //     0xb96378: ldr             x0, [x0, #0xb00]
    // 0xb9637c: stur            x1, [fp, #-0x18]
    // 0xb96380: StoreField: r1->field_f = r0
    //     0xb96380: stur            w0, [x1, #0xf]
    // 0xb96384: r0 = Instance_DecorationPosition
    //     0xb96384: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb96388: ldr             x0, [x0, #0xb28]
    // 0xb9638c: StoreField: r1->field_13 = r0
    //     0xb9638c: stur            w0, [x1, #0x13]
    // 0xb96390: ldur            x0, [fp, #-8]
    // 0xb96394: StoreField: r1->field_b = r0
    //     0xb96394: stur            w0, [x1, #0xb]
    // 0xb96398: r0 = Card()
    //     0xb96398: bl              #0xad7cd4  ; AllocateCardStub -> Card (size=0x38)
    // 0xb9639c: mov             x1, x0
    // 0xb963a0: r0 = true
    //     0xb963a0: add             x0, NULL, #0x20  ; true
    // 0xb963a4: stur            x1, [fp, #-8]
    // 0xb963a8: StoreField: r1->field_1f = r0
    //     0xb963a8: stur            w0, [x1, #0x1f]
    // 0xb963ac: ldur            x2, [fp, #-0x18]
    // 0xb963b0: StoreField: r1->field_2f = r2
    //     0xb963b0: stur            w2, [x1, #0x2f]
    // 0xb963b4: StoreField: r1->field_2b = r0
    //     0xb963b4: stur            w0, [x1, #0x2b]
    // 0xb963b8: r0 = Instance__CardVariant
    //     0xb963b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25778] Obj!_CardVariant@e36a41
    //     0xb963bc: ldr             x0, [x0, #0x778]
    // 0xb963c0: StoreField: r1->field_33 = r0
    //     0xb963c0: stur            w0, [x1, #0x33]
    // 0xb963c4: r0 = Theme()
    //     0xb963c4: bl              #0x91a640  ; AllocateThemeStub -> Theme (size=0x14)
    // 0xb963c8: mov             x3, x0
    // 0xb963cc: ldur            x0, [fp, #-0x10]
    // 0xb963d0: stur            x3, [fp, #-0x18]
    // 0xb963d4: StoreField: r3->field_b = r0
    //     0xb963d4: stur            w0, [x3, #0xb]
    // 0xb963d8: ldur            x0, [fp, #-8]
    // 0xb963dc: StoreField: r3->field_f = r0
    //     0xb963dc: stur            w0, [x3, #0xf]
    // 0xb963e0: r1 = Null
    //     0xb963e0: mov             x1, NULL
    // 0xb963e4: r2 = 2
    //     0xb963e4: movz            x2, #0x2
    // 0xb963e8: r0 = AllocateArray()
    //     0xb963e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb963ec: mov             x2, x0
    // 0xb963f0: ldur            x0, [fp, #-0x18]
    // 0xb963f4: stur            x2, [fp, #-8]
    // 0xb963f8: StoreField: r2->field_f = r0
    //     0xb963f8: stur            w0, [x2, #0xf]
    // 0xb963fc: r1 = <Widget>
    //     0xb963fc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb96400: r0 = AllocateGrowableArray()
    //     0xb96400: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb96404: mov             x1, x0
    // 0xb96408: ldur            x0, [fp, #-8]
    // 0xb9640c: stur            x1, [fp, #-0x10]
    // 0xb96410: StoreField: r1->field_f = r0
    //     0xb96410: stur            w0, [x1, #0xf]
    // 0xb96414: r0 = 2
    //     0xb96414: movz            x0, #0x2
    // 0xb96418: StoreField: r1->field_b = r0
    //     0xb96418: stur            w0, [x1, #0xb]
    // 0xb9641c: r0 = NSection()
    //     0xb9641c: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xb96420: r1 = ""
    //     0xb96420: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb96424: StoreField: r0->field_b = r1
    //     0xb96424: stur            w1, [x0, #0xb]
    // 0xb96428: ldur            x1, [fp, #-0x10]
    // 0xb9642c: StoreField: r0->field_f = r1
    //     0xb9642c: stur            w1, [x0, #0xf]
    // 0xb96430: r1 = false
    //     0xb96430: add             x1, NULL, #0x30  ; false
    // 0xb96434: StoreField: r0->field_27 = r1
    //     0xb96434: stur            w1, [x0, #0x27]
    // 0xb96438: StoreField: r0->field_2b = r1
    //     0xb96438: stur            w1, [x0, #0x2b]
    // 0xb9643c: LeaveFrame
    //     0xb9643c: mov             SP, fp
    //     0xb96440: ldp             fp, lr, [SP], #0x10
    // 0xb96444: ret
    //     0xb96444: ret             
    // 0xb96448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb96448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9644c: b               #0xb959f8
    // 0xb96450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96450: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96454: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96458: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9645c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9645c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96460: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96464: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96464: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96468: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96468: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb9646c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9646c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
