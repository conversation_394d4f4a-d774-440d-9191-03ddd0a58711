// lib: , url: package:nuonline/app/modules/donation/widgets/transaction_success.dart

// class id: 1050251, size: 0x8
class :: {
}

// class id: 4115, size: 0x18, field offset: 0x14
class _TransactionSuccessState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa3aeec, size: 0x760
    // 0xa3aeec: EnterFrame
    //     0xa3aeec: stp             fp, lr, [SP, #-0x10]!
    //     0xa3aef0: mov             fp, SP
    // 0xa3aef4: AllocStack(0x68)
    //     0xa3aef4: sub             SP, SP, #0x68
    // 0xa3aef8: SetupParameters(_TransactionSuccessState this /* r1 => r1, fp-0x8 */)
    //     0xa3aef8: stur            x1, [fp, #-8]
    // 0xa3aefc: CheckStackOverflow
    //     0xa3aefc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3af00: cmp             SP, x16
    //     0xa3af04: b.ls            #0xa3b630
    // 0xa3af08: r1 = 1
    //     0xa3af08: movz            x1, #0x1
    // 0xa3af0c: r0 = AllocateContext()
    //     0xa3af0c: bl              #0xec126c  ; AllocateContextStub
    // 0xa3af10: mov             x3, x0
    // 0xa3af14: ldur            x0, [fp, #-8]
    // 0xa3af18: stur            x3, [fp, #-0x10]
    // 0xa3af1c: StoreField: r3->field_f = r0
    //     0xa3af1c: stur            w0, [x3, #0xf]
    // 0xa3af20: r1 = _ConstMap len:10
    //     0xa3af20: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xa3af24: ldr             x1, [x1, #0xc08]
    // 0xa3af28: r2 = 1800
    //     0xa3af28: movz            x2, #0x708
    // 0xa3af2c: r0 = []()
    //     0xa3af2c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xa3af30: r16 = <Color?>
    //     0xa3af30: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa3af34: ldr             x16, [x16, #0x98]
    // 0xa3af38: stp             x0, x16, [SP, #8]
    // 0xa3af3c: str             NULL, [SP]
    // 0xa3af40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa3af40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa3af44: r0 = mode()
    //     0xa3af44: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xa3af48: stur            x0, [fp, #-0x18]
    // 0xa3af4c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa3af4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3af50: ldr             x0, [x0, #0x2670]
    //     0xa3af54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3af58: cmp             w0, w16
    //     0xa3af5c: b.ne            #0xa3af68
    //     0xa3af60: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa3af64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa3af68: r0 = GetNavigation.textTheme()
    //     0xa3af68: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa3af6c: LoadField: r1 = r0->field_f
    //     0xa3af6c: ldur            w1, [x0, #0xf]
    // 0xa3af70: DecompressPointer r1
    //     0xa3af70: add             x1, x1, HEAP, lsl #32
    // 0xa3af74: cmp             w1, NULL
    // 0xa3af78: b.eq            #0xa3b638
    // 0xa3af7c: r16 = 24.000000
    //     0xa3af7c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xa3af80: ldr             x16, [x16, #0x368]
    // 0xa3af84: r30 = Instance_Color
    //     0xa3af84: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xa3af88: stp             lr, x16, [SP]
    // 0xa3af8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa3af8c: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa3af90: ldr             x4, [x4, #0xaa0]
    // 0xa3af94: r0 = copyWith()
    //     0xa3af94: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa3af98: stur            x0, [fp, #-0x20]
    // 0xa3af9c: r0 = Text()
    //     0xa3af9c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa3afa0: mov             x3, x0
    // 0xa3afa4: r0 = "Pembayaran Berhasil!"
    //     0xa3afa4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40150] "Pembayaran Berhasil!"
    //     0xa3afa8: ldr             x0, [x0, #0x150]
    // 0xa3afac: stur            x3, [fp, #-0x28]
    // 0xa3afb0: StoreField: r3->field_b = r0
    //     0xa3afb0: stur            w0, [x3, #0xb]
    // 0xa3afb4: ldur            x0, [fp, #-0x20]
    // 0xa3afb8: StoreField: r3->field_13 = r0
    //     0xa3afb8: stur            w0, [x3, #0x13]
    // 0xa3afbc: r1 = Null
    //     0xa3afbc: mov             x1, NULL
    // 0xa3afc0: r2 = 2
    //     0xa3afc0: movz            x2, #0x2
    // 0xa3afc4: r0 = AllocateArray()
    //     0xa3afc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3afc8: stur            x0, [fp, #-0x20]
    // 0xa3afcc: r16 = "Terimakasih telah "
    //     0xa3afcc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40158] "Terimakasih telah "
    //     0xa3afd0: ldr             x16, [x16, #0x158]
    // 0xa3afd4: StoreField: r0->field_f = r16
    //     0xa3afd4: stur            w16, [x0, #0xf]
    // 0xa3afd8: r1 = <String>
    //     0xa3afd8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3afdc: r0 = AllocateGrowableArray()
    //     0xa3afdc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3afe0: mov             x1, x0
    // 0xa3afe4: ldur            x0, [fp, #-0x20]
    // 0xa3afe8: stur            x1, [fp, #-0x30]
    // 0xa3afec: StoreField: r1->field_f = r0
    //     0xa3afec: stur            w0, [x1, #0xf]
    // 0xa3aff0: r2 = 2
    //     0xa3aff0: movz            x2, #0x2
    // 0xa3aff4: StoreField: r1->field_b = r2
    //     0xa3aff4: stur            w2, [x1, #0xb]
    // 0xa3aff8: ldur            x3, [fp, #-8]
    // 0xa3affc: LoadField: r0 = r3->field_b
    //     0xa3affc: ldur            w0, [x3, #0xb]
    // 0xa3b000: DecompressPointer r0
    //     0xa3b000: add             x0, x0, HEAP, lsl #32
    // 0xa3b004: cmp             w0, NULL
    // 0xa3b008: b.eq            #0xa3b63c
    // 0xa3b00c: LoadField: r4 = r0->field_b
    //     0xa3b00c: ldur            w4, [x0, #0xb]
    // 0xa3b010: DecompressPointer r4
    //     0xa3b010: add             x4, x4, HEAP, lsl #32
    // 0xa3b014: LoadField: r0 = r4->field_2b
    //     0xa3b014: ldur            w0, [x4, #0x2b]
    // 0xa3b018: DecompressPointer r0
    //     0xa3b018: add             x0, x0, HEAP, lsl #32
    // 0xa3b01c: LoadField: r4 = r0->field_13
    //     0xa3b01c: ldur            w4, [x0, #0x13]
    // 0xa3b020: DecompressPointer r4
    //     0xa3b020: add             x4, x4, HEAP, lsl #32
    // 0xa3b024: LoadField: r0 = r4->field_f
    //     0xa3b024: ldur            w0, [x4, #0xf]
    // 0xa3b028: DecompressPointer r0
    //     0xa3b028: add             x0, x0, HEAP, lsl #32
    // 0xa3b02c: r4 = LoadClassIdInstr(r0)
    //     0xa3b02c: ldur            x4, [x0, #-1]
    //     0xa3b030: ubfx            x4, x4, #0xc, #0x14
    // 0xa3b034: r16 = "Zakat"
    //     0xa3b034: add             x16, PP, #0x40, lsl #12  ; [pp+0x40160] "Zakat"
    //     0xa3b038: ldr             x16, [x16, #0x160]
    // 0xa3b03c: stp             x16, x0, [SP]
    // 0xa3b040: mov             x0, x4
    // 0xa3b044: mov             lr, x0
    // 0xa3b048: ldr             lr, [x21, lr, lsl #3]
    // 0xa3b04c: blr             lr
    // 0xa3b050: tbnz            w0, #4, #0xa3b0b8
    // 0xa3b054: ldur            x0, [fp, #-0x30]
    // 0xa3b058: LoadField: r1 = r0->field_b
    //     0xa3b058: ldur            w1, [x0, #0xb]
    // 0xa3b05c: LoadField: r2 = r0->field_f
    //     0xa3b05c: ldur            w2, [x0, #0xf]
    // 0xa3b060: DecompressPointer r2
    //     0xa3b060: add             x2, x2, HEAP, lsl #32
    // 0xa3b064: LoadField: r3 = r2->field_b
    //     0xa3b064: ldur            w3, [x2, #0xb]
    // 0xa3b068: r2 = LoadInt32Instr(r1)
    //     0xa3b068: sbfx            x2, x1, #1, #0x1f
    // 0xa3b06c: stur            x2, [fp, #-0x38]
    // 0xa3b070: r1 = LoadInt32Instr(r3)
    //     0xa3b070: sbfx            x1, x3, #1, #0x1f
    // 0xa3b074: cmp             x2, x1
    // 0xa3b078: b.ne            #0xa3b084
    // 0xa3b07c: mov             x1, x0
    // 0xa3b080: r0 = _growToNextCapacity()
    //     0xa3b080: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3b084: ldur            x0, [fp, #-0x30]
    // 0xa3b088: ldur            x1, [fp, #-0x38]
    // 0xa3b08c: add             x2, x1, #1
    // 0xa3b090: lsl             x3, x2, #1
    // 0xa3b094: StoreField: r0->field_b = r3
    //     0xa3b094: stur            w3, [x0, #0xb]
    // 0xa3b098: LoadField: r3 = r0->field_f
    //     0xa3b098: ldur            w3, [x0, #0xf]
    // 0xa3b09c: DecompressPointer r3
    //     0xa3b09c: add             x3, x3, HEAP, lsl #32
    // 0xa3b0a0: add             x4, x3, x1, lsl #2
    // 0xa3b0a4: r16 = "membayar zakat"
    //     0xa3b0a4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40168] "membayar zakat"
    //     0xa3b0a8: ldr             x16, [x16, #0x168]
    // 0xa3b0ac: StoreField: r4->field_f = r16
    //     0xa3b0ac: stur            w16, [x4, #0xf]
    // 0xa3b0b0: mov             x1, x3
    // 0xa3b0b4: b               #0xa3b118
    // 0xa3b0b8: ldur            x0, [fp, #-0x30]
    // 0xa3b0bc: LoadField: r1 = r0->field_b
    //     0xa3b0bc: ldur            w1, [x0, #0xb]
    // 0xa3b0c0: LoadField: r2 = r0->field_f
    //     0xa3b0c0: ldur            w2, [x0, #0xf]
    // 0xa3b0c4: DecompressPointer r2
    //     0xa3b0c4: add             x2, x2, HEAP, lsl #32
    // 0xa3b0c8: LoadField: r3 = r2->field_b
    //     0xa3b0c8: ldur            w3, [x2, #0xb]
    // 0xa3b0cc: r2 = LoadInt32Instr(r1)
    //     0xa3b0cc: sbfx            x2, x1, #1, #0x1f
    // 0xa3b0d0: stur            x2, [fp, #-0x38]
    // 0xa3b0d4: r1 = LoadInt32Instr(r3)
    //     0xa3b0d4: sbfx            x1, x3, #1, #0x1f
    // 0xa3b0d8: cmp             x2, x1
    // 0xa3b0dc: b.ne            #0xa3b0e8
    // 0xa3b0e0: mov             x1, x0
    // 0xa3b0e4: r0 = _growToNextCapacity()
    //     0xa3b0e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3b0e8: ldur            x0, [fp, #-0x30]
    // 0xa3b0ec: ldur            x1, [fp, #-0x38]
    // 0xa3b0f0: add             x2, x1, #1
    // 0xa3b0f4: lsl             x3, x2, #1
    // 0xa3b0f8: StoreField: r0->field_b = r3
    //     0xa3b0f8: stur            w3, [x0, #0xb]
    // 0xa3b0fc: LoadField: r3 = r0->field_f
    //     0xa3b0fc: ldur            w3, [x0, #0xf]
    // 0xa3b100: DecompressPointer r3
    //     0xa3b100: add             x3, x3, HEAP, lsl #32
    // 0xa3b104: add             x4, x3, x1, lsl #2
    // 0xa3b108: r16 = "bersedekah"
    //     0xa3b108: add             x16, PP, #0x40, lsl #12  ; [pp+0x40170] "bersedekah"
    //     0xa3b10c: ldr             x16, [x16, #0x170]
    // 0xa3b110: StoreField: r4->field_f = r16
    //     0xa3b110: stur            w16, [x4, #0xf]
    // 0xa3b114: mov             x1, x3
    // 0xa3b118: stur            x2, [fp, #-0x38]
    // 0xa3b11c: LoadField: r3 = r1->field_b
    //     0xa3b11c: ldur            w3, [x1, #0xb]
    // 0xa3b120: r1 = LoadInt32Instr(r3)
    //     0xa3b120: sbfx            x1, x3, #1, #0x1f
    // 0xa3b124: cmp             x2, x1
    // 0xa3b128: b.ne            #0xa3b134
    // 0xa3b12c: mov             x1, x0
    // 0xa3b130: r0 = _growToNextCapacity()
    //     0xa3b130: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3b134: ldur            x4, [fp, #-8]
    // 0xa3b138: ldur            x5, [fp, #-0x28]
    // 0xa3b13c: ldur            x2, [fp, #-0x30]
    // 0xa3b140: ldur            x3, [fp, #-0x38]
    // 0xa3b144: add             x0, x3, #1
    // 0xa3b148: lsl             x1, x0, #1
    // 0xa3b14c: StoreField: r2->field_b = r1
    //     0xa3b14c: stur            w1, [x2, #0xb]
    // 0xa3b150: mov             x1, x3
    // 0xa3b154: cmp             x1, x0
    // 0xa3b158: b.hs            #0xa3b640
    // 0xa3b15c: LoadField: r0 = r2->field_f
    //     0xa3b15c: ldur            w0, [x2, #0xf]
    // 0xa3b160: DecompressPointer r0
    //     0xa3b160: add             x0, x0, HEAP, lsl #32
    // 0xa3b164: add             x1, x0, x3, lsl #2
    // 0xa3b168: r16 = " melalui NU Online Super App"
    //     0xa3b168: add             x16, PP, #0x40, lsl #12  ; [pp+0x40178] " melalui NU Online Super App"
    //     0xa3b16c: ldr             x16, [x16, #0x178]
    // 0xa3b170: StoreField: r1->field_f = r16
    //     0xa3b170: stur            w16, [x1, #0xf]
    // 0xa3b174: r16 = " "
    //     0xa3b174: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xa3b178: str             x16, [SP]
    // 0xa3b17c: mov             x1, x2
    // 0xa3b180: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa3b180: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa3b184: r0 = join()
    //     0xa3b184: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xa3b188: stur            x0, [fp, #-0x20]
    // 0xa3b18c: r0 = GetNavigation.textTheme()
    //     0xa3b18c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa3b190: LoadField: r1 = r0->field_23
    //     0xa3b190: ldur            w1, [x0, #0x23]
    // 0xa3b194: DecompressPointer r1
    //     0xa3b194: add             x1, x1, HEAP, lsl #32
    // 0xa3b198: cmp             w1, NULL
    // 0xa3b19c: b.eq            #0xa3b644
    // 0xa3b1a0: r16 = Instance_Color
    //     0xa3b1a0: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xa3b1a4: str             x16, [SP]
    // 0xa3b1a8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa3b1a8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa3b1ac: ldr             x4, [x4, #0x228]
    // 0xa3b1b0: r0 = copyWith()
    //     0xa3b1b0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa3b1b4: stur            x0, [fp, #-0x30]
    // 0xa3b1b8: r0 = Text()
    //     0xa3b1b8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa3b1bc: mov             x3, x0
    // 0xa3b1c0: ldur            x0, [fp, #-0x20]
    // 0xa3b1c4: stur            x3, [fp, #-0x40]
    // 0xa3b1c8: StoreField: r3->field_b = r0
    //     0xa3b1c8: stur            w0, [x3, #0xb]
    // 0xa3b1cc: ldur            x0, [fp, #-0x30]
    // 0xa3b1d0: StoreField: r3->field_13 = r0
    //     0xa3b1d0: stur            w0, [x3, #0x13]
    // 0xa3b1d4: r1 = Null
    //     0xa3b1d4: mov             x1, NULL
    // 0xa3b1d8: r2 = 6
    //     0xa3b1d8: movz            x2, #0x6
    // 0xa3b1dc: r0 = AllocateArray()
    //     0xa3b1dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b1e0: mov             x2, x0
    // 0xa3b1e4: ldur            x0, [fp, #-0x28]
    // 0xa3b1e8: stur            x2, [fp, #-0x20]
    // 0xa3b1ec: StoreField: r2->field_f = r0
    //     0xa3b1ec: stur            w0, [x2, #0xf]
    // 0xa3b1f0: r16 = Instance_SizedBox
    //     0xa3b1f0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xa3b1f4: ldr             x16, [x16, #0x950]
    // 0xa3b1f8: StoreField: r2->field_13 = r16
    //     0xa3b1f8: stur            w16, [x2, #0x13]
    // 0xa3b1fc: ldur            x0, [fp, #-0x40]
    // 0xa3b200: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3b200: stur            w0, [x2, #0x17]
    // 0xa3b204: r1 = <Widget>
    //     0xa3b204: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3b208: r0 = AllocateGrowableArray()
    //     0xa3b208: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b20c: mov             x1, x0
    // 0xa3b210: ldur            x0, [fp, #-0x20]
    // 0xa3b214: stur            x1, [fp, #-0x28]
    // 0xa3b218: StoreField: r1->field_f = r0
    //     0xa3b218: stur            w0, [x1, #0xf]
    // 0xa3b21c: r2 = 6
    //     0xa3b21c: movz            x2, #0x6
    // 0xa3b220: StoreField: r1->field_b = r2
    //     0xa3b220: stur            w2, [x1, #0xb]
    // 0xa3b224: r0 = Column()
    //     0xa3b224: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa3b228: mov             x1, x0
    // 0xa3b22c: r0 = Instance_Axis
    //     0xa3b22c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa3b230: stur            x1, [fp, #-0x20]
    // 0xa3b234: StoreField: r1->field_f = r0
    //     0xa3b234: stur            w0, [x1, #0xf]
    // 0xa3b238: r0 = Instance_MainAxisAlignment
    //     0xa3b238: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa3b23c: ldr             x0, [x0, #0x730]
    // 0xa3b240: StoreField: r1->field_13 = r0
    //     0xa3b240: stur            w0, [x1, #0x13]
    // 0xa3b244: r2 = Instance_MainAxisSize
    //     0xa3b244: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa3b248: ldr             x2, [x2, #0x738]
    // 0xa3b24c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa3b24c: stur            w2, [x1, #0x17]
    // 0xa3b250: r3 = Instance_CrossAxisAlignment
    //     0xa3b250: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xa3b254: ldr             x3, [x3, #0xf50]
    // 0xa3b258: StoreField: r1->field_1b = r3
    //     0xa3b258: stur            w3, [x1, #0x1b]
    // 0xa3b25c: r3 = Instance_VerticalDirection
    //     0xa3b25c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa3b260: ldr             x3, [x3, #0x748]
    // 0xa3b264: StoreField: r1->field_23 = r3
    //     0xa3b264: stur            w3, [x1, #0x23]
    // 0xa3b268: r4 = Instance_Clip
    //     0xa3b268: add             x4, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa3b26c: ldr             x4, [x4, #0x750]
    // 0xa3b270: StoreField: r1->field_2b = r4
    //     0xa3b270: stur            w4, [x1, #0x2b]
    // 0xa3b274: StoreField: r1->field_2f = rZR
    //     0xa3b274: stur            xzr, [x1, #0x2f]
    // 0xa3b278: ldur            x5, [fp, #-0x28]
    // 0xa3b27c: StoreField: r1->field_b = r5
    //     0xa3b27c: stur            w5, [x1, #0xb]
    // 0xa3b280: r0 = Container()
    //     0xa3b280: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa3b284: stur            x0, [fp, #-0x28]
    // 0xa3b288: r16 = 144.000000
    //     0xa3b288: add             x16, PP, #0x38, lsl #12  ; [pp+0x38190] 144
    //     0xa3b28c: ldr             x16, [x16, #0x190]
    // 0xa3b290: r30 = Instance_EdgeInsets
    //     0xa3b290: ldr             lr, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xa3b294: stp             lr, x16, [SP, #8]
    // 0xa3b298: ldur            x16, [fp, #-0x20]
    // 0xa3b29c: str             x16, [SP]
    // 0xa3b2a0: mov             x1, x0
    // 0xa3b2a4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xa3b2a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x331e0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xa3b2a8: ldr             x4, [x4, #0x1e0]
    // 0xa3b2ac: r0 = Container()
    //     0xa3b2ac: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa3b2b0: r0 = PreferredSize()
    //     0xa3b2b0: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xa3b2b4: mov             x1, x0
    // 0xa3b2b8: r0 = Instance_Size
    //     0xa3b2b8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40180] Obj!Size@e2c1a1
    //     0xa3b2bc: ldr             x0, [x0, #0x180]
    // 0xa3b2c0: stur            x1, [fp, #-0x20]
    // 0xa3b2c4: StoreField: r1->field_f = r0
    //     0xa3b2c4: stur            w0, [x1, #0xf]
    // 0xa3b2c8: ldur            x0, [fp, #-0x28]
    // 0xa3b2cc: StoreField: r1->field_b = r0
    //     0xa3b2cc: stur            w0, [x1, #0xb]
    // 0xa3b2d0: r0 = AppBar()
    //     0xa3b2d0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xa3b2d4: stur            x0, [fp, #-0x28]
    // 0xa3b2d8: r16 = Instance_Text
    //     0xa3b2d8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30288] Obj!Text@e21641
    //     0xa3b2dc: ldr             x16, [x16, #0x288]
    // 0xa3b2e0: r30 = 0.000000
    //     0xa3b2e0: ldr             lr, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa3b2e4: stp             lr, x16, [SP, #0x18]
    // 0xa3b2e8: ldur            x16, [fp, #-0x18]
    // 0xa3b2ec: r30 = Instance_RoundedRectangleBorder
    //     0xa3b2ec: add             lr, PP, #0x40, lsl #12  ; [pp+0x40188] Obj!RoundedRectangleBorder@e146d1
    //     0xa3b2f0: ldr             lr, [lr, #0x188]
    // 0xa3b2f4: stp             lr, x16, [SP, #8]
    // 0xa3b2f8: ldur            x16, [fp, #-0x20]
    // 0xa3b2fc: str             x16, [SP]
    // 0xa3b300: mov             x1, x0
    // 0xa3b304: r4 = const [0, 0x6, 0x5, 0x1, backgroundColor, 0x3, bottom, 0x5, elevation, 0x2, shape, 0x4, title, 0x1, null]
    //     0xa3b304: add             x4, PP, #0x40, lsl #12  ; [pp+0x40190] List(15) [0, 0x6, 0x5, 0x1, "backgroundColor", 0x3, "bottom", 0x5, "elevation", 0x2, "shape", 0x4, "title", 0x1, Null]
    //     0xa3b308: ldr             x4, [x4, #0x190]
    // 0xa3b30c: r0 = AppBar()
    //     0xa3b30c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xa3b310: ldur            x0, [fp, #-8]
    // 0xa3b314: LoadField: r1 = r0->field_13
    //     0xa3b314: ldur            w1, [x0, #0x13]
    // 0xa3b318: DecompressPointer r1
    //     0xa3b318: add             x1, x1, HEAP, lsl #32
    // 0xa3b31c: stur            x1, [fp, #-0x18]
    // 0xa3b320: LoadField: r2 = r0->field_b
    //     0xa3b320: ldur            w2, [x0, #0xb]
    // 0xa3b324: DecompressPointer r2
    //     0xa3b324: add             x2, x2, HEAP, lsl #32
    // 0xa3b328: cmp             w2, NULL
    // 0xa3b32c: b.eq            #0xa3b648
    // 0xa3b330: LoadField: r0 = r2->field_b
    //     0xa3b330: ldur            w0, [x2, #0xb]
    // 0xa3b334: DecompressPointer r0
    //     0xa3b334: add             x0, x0, HEAP, lsl #32
    // 0xa3b338: stur            x0, [fp, #-8]
    // 0xa3b33c: r0 = TransactionReceipt()
    //     0xa3b33c: bl              #0xa3b688  ; AllocateTransactionReceiptStub -> TransactionReceipt (size=0x10)
    // 0xa3b340: mov             x1, x0
    // 0xa3b344: ldur            x0, [fp, #-8]
    // 0xa3b348: stur            x1, [fp, #-0x20]
    // 0xa3b34c: StoreField: r1->field_b = r0
    //     0xa3b34c: stur            w0, [x1, #0xb]
    // 0xa3b350: r0 = Screenshot()
    //     0xa3b350: bl              #0xa39760  ; AllocateScreenshotStub -> Screenshot (size=0x14)
    // 0xa3b354: mov             x1, x0
    // 0xa3b358: ldur            x0, [fp, #-0x20]
    // 0xa3b35c: stur            x1, [fp, #-0x30]
    // 0xa3b360: StoreField: r1->field_b = r0
    //     0xa3b360: stur            w0, [x1, #0xb]
    // 0xa3b364: ldur            x0, [fp, #-0x18]
    // 0xa3b368: StoreField: r1->field_f = r0
    //     0xa3b368: stur            w0, [x1, #0xf]
    // 0xa3b36c: r0 = TransactionDetail()
    //     0xa3b36c: bl              #0xa3b67c  ; AllocateTransactionDetailStub -> TransactionDetail (size=0x10)
    // 0xa3b370: mov             x3, x0
    // 0xa3b374: ldur            x0, [fp, #-8]
    // 0xa3b378: stur            x3, [fp, #-0x18]
    // 0xa3b37c: StoreField: r3->field_b = r0
    //     0xa3b37c: stur            w0, [x3, #0xb]
    // 0xa3b380: r1 = Null
    //     0xa3b380: mov             x1, NULL
    // 0xa3b384: r2 = 6
    //     0xa3b384: movz            x2, #0x6
    // 0xa3b388: r0 = AllocateArray()
    //     0xa3b388: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b38c: mov             x2, x0
    // 0xa3b390: ldur            x0, [fp, #-0x30]
    // 0xa3b394: stur            x2, [fp, #-8]
    // 0xa3b398: StoreField: r2->field_f = r0
    //     0xa3b398: stur            w0, [x2, #0xf]
    // 0xa3b39c: r16 = Instance_NSectionDivider
    //     0xa3b39c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xa3b3a0: ldr             x16, [x16, #0x38]
    // 0xa3b3a4: StoreField: r2->field_13 = r16
    //     0xa3b3a4: stur            w16, [x2, #0x13]
    // 0xa3b3a8: ldur            x0, [fp, #-0x18]
    // 0xa3b3ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3b3ac: stur            w0, [x2, #0x17]
    // 0xa3b3b0: r1 = <Widget>
    //     0xa3b3b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3b3b4: r0 = AllocateGrowableArray()
    //     0xa3b3b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b3b8: mov             x1, x0
    // 0xa3b3bc: ldur            x0, [fp, #-8]
    // 0xa3b3c0: stur            x1, [fp, #-0x18]
    // 0xa3b3c4: StoreField: r1->field_f = r0
    //     0xa3b3c4: stur            w0, [x1, #0xf]
    // 0xa3b3c8: r2 = 6
    //     0xa3b3c8: movz            x2, #0x6
    // 0xa3b3cc: StoreField: r1->field_b = r2
    //     0xa3b3cc: stur            w2, [x1, #0xb]
    // 0xa3b3d0: r0 = ListView()
    //     0xa3b3d0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xa3b3d4: mov             x1, x0
    // 0xa3b3d8: ldur            x2, [fp, #-0x18]
    // 0xa3b3dc: stur            x0, [fp, #-8]
    // 0xa3b3e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa3b3e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa3b3e4: r0 = ListView()
    //     0xa3b3e4: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xa3b3e8: ldur            x2, [fp, #-0x10]
    // 0xa3b3ec: r1 = Function '<anonymous closure>':.
    //     0xa3b3ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40198] AnonymousClosure: (0xa3b770), in [package:nuonline/app/modules/donation/widgets/transaction_success.dart] _TransactionSuccessState::build (0xa3aeec)
    //     0xa3b3f0: ldr             x1, [x1, #0x198]
    // 0xa3b3f4: r0 = AllocateClosure()
    //     0xa3b3f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3b3f8: stur            x0, [fp, #-0x18]
    // 0xa3b3fc: r0 = OutlinedButton()
    //     0xa3b3fc: bl              #0xa3b670  ; AllocateOutlinedButtonStub -> OutlinedButton (size=0x3c)
    // 0xa3b400: mov             x2, x0
    // 0xa3b404: ldur            x0, [fp, #-0x18]
    // 0xa3b408: stur            x2, [fp, #-0x20]
    // 0xa3b40c: StoreField: r2->field_b = r0
    //     0xa3b40c: stur            w0, [x2, #0xb]
    // 0xa3b410: r0 = false
    //     0xa3b410: add             x0, NULL, #0x30  ; false
    // 0xa3b414: StoreField: r2->field_27 = r0
    //     0xa3b414: stur            w0, [x2, #0x27]
    // 0xa3b418: r3 = true
    //     0xa3b418: add             x3, NULL, #0x20  ; true
    // 0xa3b41c: StoreField: r2->field_2f = r3
    //     0xa3b41c: stur            w3, [x2, #0x2f]
    // 0xa3b420: r1 = Instance_Text
    //     0xa3b420: add             x1, PP, #0x40, lsl #12  ; [pp+0x401a0] Obj!Text@e21691
    //     0xa3b424: ldr             x1, [x1, #0x1a0]
    // 0xa3b428: StoreField: r2->field_37 = r1
    //     0xa3b428: stur            w1, [x2, #0x37]
    // 0xa3b42c: r1 = <FlexParentData>
    //     0xa3b42c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xa3b430: ldr             x1, [x1, #0x720]
    // 0xa3b434: r0 = Expanded()
    //     0xa3b434: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa3b438: mov             x3, x0
    // 0xa3b43c: r0 = 1
    //     0xa3b43c: movz            x0, #0x1
    // 0xa3b440: stur            x3, [fp, #-0x18]
    // 0xa3b444: StoreField: r3->field_13 = r0
    //     0xa3b444: stur            x0, [x3, #0x13]
    // 0xa3b448: r4 = Instance_FlexFit
    //     0xa3b448: add             x4, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xa3b44c: ldr             x4, [x4, #0x728]
    // 0xa3b450: StoreField: r3->field_1b = r4
    //     0xa3b450: stur            w4, [x3, #0x1b]
    // 0xa3b454: ldur            x1, [fp, #-0x20]
    // 0xa3b458: StoreField: r3->field_b = r1
    //     0xa3b458: stur            w1, [x3, #0xb]
    // 0xa3b45c: ldur            x2, [fp, #-0x10]
    // 0xa3b460: r1 = Function '<anonymous closure>':.
    //     0xa3b460: add             x1, PP, #0x40, lsl #12  ; [pp+0x401a8] AnonymousClosure: (0xa3b6a0), in [package:nuonline/app/modules/donation/widgets/transaction_success.dart] _TransactionSuccessState::build (0xa3aeec)
    //     0xa3b464: ldr             x1, [x1, #0x1a8]
    // 0xa3b468: r0 = AllocateClosure()
    //     0xa3b468: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3b46c: stur            x0, [fp, #-0x10]
    // 0xa3b470: r0 = TextButton()
    //     0xa3b470: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa3b474: mov             x2, x0
    // 0xa3b478: ldur            x0, [fp, #-0x10]
    // 0xa3b47c: stur            x2, [fp, #-0x20]
    // 0xa3b480: StoreField: r2->field_b = r0
    //     0xa3b480: stur            w0, [x2, #0xb]
    // 0xa3b484: r0 = false
    //     0xa3b484: add             x0, NULL, #0x30  ; false
    // 0xa3b488: StoreField: r2->field_27 = r0
    //     0xa3b488: stur            w0, [x2, #0x27]
    // 0xa3b48c: r3 = true
    //     0xa3b48c: add             x3, NULL, #0x20  ; true
    // 0xa3b490: StoreField: r2->field_2f = r3
    //     0xa3b490: stur            w3, [x2, #0x2f]
    // 0xa3b494: r1 = Instance_Text
    //     0xa3b494: add             x1, PP, #0x32, lsl #12  ; [pp+0x32760] Obj!Text@e216e1
    //     0xa3b498: ldr             x1, [x1, #0x760]
    // 0xa3b49c: StoreField: r2->field_37 = r1
    //     0xa3b49c: stur            w1, [x2, #0x37]
    // 0xa3b4a0: r1 = <FlexParentData>
    //     0xa3b4a0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xa3b4a4: ldr             x1, [x1, #0x720]
    // 0xa3b4a8: r0 = Expanded()
    //     0xa3b4a8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa3b4ac: mov             x3, x0
    // 0xa3b4b0: r0 = 1
    //     0xa3b4b0: movz            x0, #0x1
    // 0xa3b4b4: stur            x3, [fp, #-0x10]
    // 0xa3b4b8: StoreField: r3->field_13 = r0
    //     0xa3b4b8: stur            x0, [x3, #0x13]
    // 0xa3b4bc: r0 = Instance_FlexFit
    //     0xa3b4bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xa3b4c0: ldr             x0, [x0, #0x728]
    // 0xa3b4c4: StoreField: r3->field_1b = r0
    //     0xa3b4c4: stur            w0, [x3, #0x1b]
    // 0xa3b4c8: ldur            x0, [fp, #-0x20]
    // 0xa3b4cc: StoreField: r3->field_b = r0
    //     0xa3b4cc: stur            w0, [x3, #0xb]
    // 0xa3b4d0: r1 = Null
    //     0xa3b4d0: mov             x1, NULL
    // 0xa3b4d4: r2 = 6
    //     0xa3b4d4: movz            x2, #0x6
    // 0xa3b4d8: r0 = AllocateArray()
    //     0xa3b4d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b4dc: mov             x2, x0
    // 0xa3b4e0: ldur            x0, [fp, #-0x18]
    // 0xa3b4e4: stur            x2, [fp, #-0x20]
    // 0xa3b4e8: StoreField: r2->field_f = r0
    //     0xa3b4e8: stur            w0, [x2, #0xf]
    // 0xa3b4ec: r16 = Instance_SizedBox
    //     0xa3b4ec: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xa3b4f0: ldr             x16, [x16, #0x340]
    // 0xa3b4f4: StoreField: r2->field_13 = r16
    //     0xa3b4f4: stur            w16, [x2, #0x13]
    // 0xa3b4f8: ldur            x0, [fp, #-0x10]
    // 0xa3b4fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3b4fc: stur            w0, [x2, #0x17]
    // 0xa3b500: r1 = <Widget>
    //     0xa3b500: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3b504: r0 = AllocateGrowableArray()
    //     0xa3b504: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b508: mov             x1, x0
    // 0xa3b50c: ldur            x0, [fp, #-0x20]
    // 0xa3b510: stur            x1, [fp, #-0x10]
    // 0xa3b514: StoreField: r1->field_f = r0
    //     0xa3b514: stur            w0, [x1, #0xf]
    // 0xa3b518: r0 = 6
    //     0xa3b518: movz            x0, #0x6
    // 0xa3b51c: StoreField: r1->field_b = r0
    //     0xa3b51c: stur            w0, [x1, #0xb]
    // 0xa3b520: r0 = Row()
    //     0xa3b520: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa3b524: mov             x1, x0
    // 0xa3b528: r0 = Instance_Axis
    //     0xa3b528: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa3b52c: stur            x1, [fp, #-0x18]
    // 0xa3b530: StoreField: r1->field_f = r0
    //     0xa3b530: stur            w0, [x1, #0xf]
    // 0xa3b534: r0 = Instance_MainAxisAlignment
    //     0xa3b534: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa3b538: ldr             x0, [x0, #0x730]
    // 0xa3b53c: StoreField: r1->field_13 = r0
    //     0xa3b53c: stur            w0, [x1, #0x13]
    // 0xa3b540: r0 = Instance_MainAxisSize
    //     0xa3b540: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa3b544: ldr             x0, [x0, #0x738]
    // 0xa3b548: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3b548: stur            w0, [x1, #0x17]
    // 0xa3b54c: r0 = Instance_CrossAxisAlignment
    //     0xa3b54c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa3b550: ldr             x0, [x0, #0x740]
    // 0xa3b554: StoreField: r1->field_1b = r0
    //     0xa3b554: stur            w0, [x1, #0x1b]
    // 0xa3b558: r0 = Instance_VerticalDirection
    //     0xa3b558: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa3b55c: ldr             x0, [x0, #0x748]
    // 0xa3b560: StoreField: r1->field_23 = r0
    //     0xa3b560: stur            w0, [x1, #0x23]
    // 0xa3b564: r0 = Instance_Clip
    //     0xa3b564: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa3b568: ldr             x0, [x0, #0x750]
    // 0xa3b56c: StoreField: r1->field_2b = r0
    //     0xa3b56c: stur            w0, [x1, #0x2b]
    // 0xa3b570: StoreField: r1->field_2f = rZR
    //     0xa3b570: stur            xzr, [x1, #0x2f]
    // 0xa3b574: ldur            x0, [fp, #-0x10]
    // 0xa3b578: StoreField: r1->field_b = r0
    //     0xa3b578: stur            w0, [x1, #0xb]
    // 0xa3b57c: r0 = Padding()
    //     0xa3b57c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa3b580: mov             x3, x0
    // 0xa3b584: r0 = Instance_EdgeInsets
    //     0xa3b584: add             x0, PP, #0x28, lsl #12  ; [pp+0x28050] Obj!EdgeInsets@e12341
    //     0xa3b588: ldr             x0, [x0, #0x50]
    // 0xa3b58c: stur            x3, [fp, #-0x10]
    // 0xa3b590: StoreField: r3->field_f = r0
    //     0xa3b590: stur            w0, [x3, #0xf]
    // 0xa3b594: ldur            x0, [fp, #-0x18]
    // 0xa3b598: StoreField: r3->field_b = r0
    //     0xa3b598: stur            w0, [x3, #0xb]
    // 0xa3b59c: r1 = Null
    //     0xa3b59c: mov             x1, NULL
    // 0xa3b5a0: r2 = 2
    //     0xa3b5a0: movz            x2, #0x2
    // 0xa3b5a4: r0 = AllocateArray()
    //     0xa3b5a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b5a8: mov             x2, x0
    // 0xa3b5ac: ldur            x0, [fp, #-0x10]
    // 0xa3b5b0: stur            x2, [fp, #-0x18]
    // 0xa3b5b4: StoreField: r2->field_f = r0
    //     0xa3b5b4: stur            w0, [x2, #0xf]
    // 0xa3b5b8: r1 = <Widget>
    //     0xa3b5b8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3b5bc: r0 = AllocateGrowableArray()
    //     0xa3b5bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b5c0: mov             x1, x0
    // 0xa3b5c4: ldur            x0, [fp, #-0x18]
    // 0xa3b5c8: stur            x1, [fp, #-0x10]
    // 0xa3b5cc: StoreField: r1->field_f = r0
    //     0xa3b5cc: stur            w0, [x1, #0xf]
    // 0xa3b5d0: r0 = 2
    //     0xa3b5d0: movz            x0, #0x2
    // 0xa3b5d4: StoreField: r1->field_b = r0
    //     0xa3b5d4: stur            w0, [x1, #0xb]
    // 0xa3b5d8: r0 = Scaffold()
    //     0xa3b5d8: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xa3b5dc: ldur            x1, [fp, #-0x28]
    // 0xa3b5e0: StoreField: r0->field_13 = r1
    //     0xa3b5e0: stur            w1, [x0, #0x13]
    // 0xa3b5e4: ldur            x1, [fp, #-8]
    // 0xa3b5e8: ArrayStore: r0[0] = r1  ; List_4
    //     0xa3b5e8: stur            w1, [x0, #0x17]
    // 0xa3b5ec: ldur            x1, [fp, #-0x10]
    // 0xa3b5f0: StoreField: r0->field_27 = r1
    //     0xa3b5f0: stur            w1, [x0, #0x27]
    // 0xa3b5f4: r1 = Instance_AlignmentDirectional
    //     0xa3b5f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xa3b5f8: ldr             x1, [x1, #0x758]
    // 0xa3b5fc: StoreField: r0->field_2b = r1
    //     0xa3b5fc: stur            w1, [x0, #0x2b]
    // 0xa3b600: r1 = true
    //     0xa3b600: add             x1, NULL, #0x20  ; true
    // 0xa3b604: StoreField: r0->field_53 = r1
    //     0xa3b604: stur            w1, [x0, #0x53]
    // 0xa3b608: r2 = Instance_DragStartBehavior
    //     0xa3b608: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa3b60c: StoreField: r0->field_57 = r2
    //     0xa3b60c: stur            w2, [x0, #0x57]
    // 0xa3b610: r2 = false
    //     0xa3b610: add             x2, NULL, #0x30  ; false
    // 0xa3b614: StoreField: r0->field_b = r2
    //     0xa3b614: stur            w2, [x0, #0xb]
    // 0xa3b618: StoreField: r0->field_f = r2
    //     0xa3b618: stur            w2, [x0, #0xf]
    // 0xa3b61c: StoreField: r0->field_5f = r1
    //     0xa3b61c: stur            w1, [x0, #0x5f]
    // 0xa3b620: StoreField: r0->field_63 = r1
    //     0xa3b620: stur            w1, [x0, #0x63]
    // 0xa3b624: LeaveFrame
    //     0xa3b624: mov             SP, fp
    //     0xa3b628: ldp             fp, lr, [SP], #0x10
    // 0xa3b62c: ret
    //     0xa3b62c: ret             
    // 0xa3b630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b634: b               #0xa3af08
    // 0xa3b638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b638: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b63c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b63c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b640: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa3b640: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa3b644: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b644: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b648: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xa3b6a0, size: 0xd0
    // 0xa3b6a0: EnterFrame
    //     0xa3b6a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3b6a4: mov             fp, SP
    // 0xa3b6a8: AllocStack(0x18)
    //     0xa3b6a8: sub             SP, SP, #0x18
    // 0xa3b6ac: SetupParameters(_TransactionSuccessState this /* r1 */)
    //     0xa3b6ac: stur            NULL, [fp, #-8]
    //     0xa3b6b0: movz            x0, #0
    //     0xa3b6b4: add             x1, fp, w0, sxtw #2
    //     0xa3b6b8: ldr             x1, [x1, #0x10]
    //     0xa3b6bc: ldur            w2, [x1, #0x17]
    //     0xa3b6c0: add             x2, x2, HEAP, lsl #32
    //     0xa3b6c4: stur            x2, [fp, #-0x10]
    // 0xa3b6c8: CheckStackOverflow
    //     0xa3b6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3b6cc: cmp             SP, x16
    //     0xa3b6d0: b.ls            #0xa3b768
    // 0xa3b6d4: InitAsync() -> Future<void?>
    //     0xa3b6d4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa3b6d8: bl              #0x661298  ; InitAsyncStub
    // 0xa3b6dc: ldur            x0, [fp, #-0x10]
    // 0xa3b6e0: LoadField: r1 = r0->field_f
    //     0xa3b6e0: ldur            w1, [x0, #0xf]
    // 0xa3b6e4: DecompressPointer r1
    //     0xa3b6e4: add             x1, x1, HEAP, lsl #32
    // 0xa3b6e8: r0 = getReceiptImage()
    //     0xa3b6e8: bl              #0xa398fc  ; [package:nuonline/app/modules/donation/widgets/transaction_pending.dart] _TransactionPendingState::getReceiptImage
    // 0xa3b6ec: mov             x1, x0
    // 0xa3b6f0: stur            x1, [fp, #-0x18]
    // 0xa3b6f4: r0 = Await()
    //     0xa3b6f4: bl              #0x661044  ; AwaitStub
    // 0xa3b6f8: cmp             w0, NULL
    // 0xa3b6fc: b.eq            #0xa3b74c
    // 0xa3b700: r1 = LoadClassIdInstr(r0)
    //     0xa3b700: ldur            x1, [x0, #-1]
    //     0xa3b704: ubfx            x1, x1, #0xc, #0x14
    // 0xa3b708: mov             x16, x0
    // 0xa3b70c: mov             x0, x1
    // 0xa3b710: mov             x1, x16
    // 0xa3b714: r0 = GDT[cid_x0 + -0xe06]()
    //     0xa3b714: sub             lr, x0, #0xe06
    //     0xa3b718: ldr             lr, [x21, lr, lsl #3]
    //     0xa3b71c: blr             lr
    // 0xa3b720: mov             x1, x0
    // 0xa3b724: r0 = saveFile()
    //     0xa3b724: bl              #0xa39854  ; [package:image_gallery_saver/image_gallery_saver.dart] ImageGallerySaver::saveFile
    // 0xa3b728: mov             x1, x0
    // 0xa3b72c: stur            x1, [fp, #-0x18]
    // 0xa3b730: r0 = Await()
    //     0xa3b730: bl              #0x661044  ; AwaitStub
    // 0xa3b734: r1 = "Berhasil menyimpan"
    //     0xa3b734: add             x1, PP, #0x40, lsl #12  ; [pp+0x401b0] "Berhasil menyimpan"
    //     0xa3b738: ldr             x1, [x1, #0x1b0]
    // 0xa3b73c: r2 = Instance_IconData
    //     0xa3b73c: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xa3b740: ldr             x2, [x2, #0x590]
    // 0xa3b744: r0 = show()
    //     0xa3b744: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xa3b748: b               #0xa3b760
    // 0xa3b74c: r1 = "Gagal menyimpan"
    //     0xa3b74c: add             x1, PP, #0x40, lsl #12  ; [pp+0x401b8] "Gagal menyimpan"
    //     0xa3b750: ldr             x1, [x1, #0x1b8]
    // 0xa3b754: r2 = Instance_IconData
    //     0xa3b754: add             x2, PP, #0x31, lsl #12  ; [pp+0x31590] Obj!IconData@e0feb1
    //     0xa3b758: ldr             x2, [x2, #0x590]
    // 0xa3b75c: r0 = show()
    //     0xa3b75c: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xa3b760: r0 = Null
    //     0xa3b760: mov             x0, NULL
    // 0xa3b764: r0 = ReturnAsyncNotFuture()
    //     0xa3b764: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa3b768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b768: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b76c: b               #0xa3b6d4
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xa3b770, size: 0x140
    // 0xa3b770: EnterFrame
    //     0xa3b770: stp             fp, lr, [SP, #-0x10]!
    //     0xa3b774: mov             fp, SP
    // 0xa3b778: AllocStack(0x28)
    //     0xa3b778: sub             SP, SP, #0x28
    // 0xa3b77c: SetupParameters(_TransactionSuccessState this /* r1 */)
    //     0xa3b77c: stur            NULL, [fp, #-8]
    //     0xa3b780: movz            x0, #0
    //     0xa3b784: add             x1, fp, w0, sxtw #2
    //     0xa3b788: ldr             x1, [x1, #0x10]
    //     0xa3b78c: ldur            w2, [x1, #0x17]
    //     0xa3b790: add             x2, x2, HEAP, lsl #32
    //     0xa3b794: stur            x2, [fp, #-0x10]
    // 0xa3b798: CheckStackOverflow
    //     0xa3b798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3b79c: cmp             SP, x16
    //     0xa3b7a0: b.ls            #0xa3b8a8
    // 0xa3b7a4: InitAsync() -> Future<void?>
    //     0xa3b7a4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa3b7a8: bl              #0x661298  ; InitAsyncStub
    // 0xa3b7ac: ldur            x0, [fp, #-0x10]
    // 0xa3b7b0: LoadField: r1 = r0->field_f
    //     0xa3b7b0: ldur            w1, [x0, #0xf]
    // 0xa3b7b4: DecompressPointer r1
    //     0xa3b7b4: add             x1, x1, HEAP, lsl #32
    // 0xa3b7b8: r0 = getReceiptImage()
    //     0xa3b7b8: bl              #0xa398fc  ; [package:nuonline/app/modules/donation/widgets/transaction_pending.dart] _TransactionPendingState::getReceiptImage
    // 0xa3b7bc: mov             x1, x0
    // 0xa3b7c0: stur            x1, [fp, #-0x18]
    // 0xa3b7c4: r0 = Await()
    //     0xa3b7c4: bl              #0x661044  ; AwaitStub
    // 0xa3b7c8: cmp             w0, NULL
    // 0xa3b7cc: b.eq            #0xa3b8a0
    // 0xa3b7d0: r1 = LoadClassIdInstr(r0)
    //     0xa3b7d0: ldur            x1, [x0, #-1]
    //     0xa3b7d4: ubfx            x1, x1, #0xc, #0x14
    // 0xa3b7d8: mov             x16, x0
    // 0xa3b7dc: mov             x0, x1
    // 0xa3b7e0: mov             x1, x16
    // 0xa3b7e4: r0 = GDT[cid_x0 + -0xe06]()
    //     0xa3b7e4: sub             lr, x0, #0xe06
    //     0xa3b7e8: ldr             lr, [x21, lr, lsl #3]
    //     0xa3b7ec: blr             lr
    // 0xa3b7f0: r1 = Null
    //     0xa3b7f0: mov             x1, NULL
    // 0xa3b7f4: r2 = 2
    //     0xa3b7f4: movz            x2, #0x2
    // 0xa3b7f8: stur            x0, [fp, #-0x18]
    // 0xa3b7fc: r0 = AllocateArray()
    //     0xa3b7fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b800: mov             x2, x0
    // 0xa3b804: ldur            x0, [fp, #-0x18]
    // 0xa3b808: stur            x2, [fp, #-0x20]
    // 0xa3b80c: StoreField: r2->field_f = r0
    //     0xa3b80c: stur            w0, [x2, #0xf]
    // 0xa3b810: r1 = <String>
    //     0xa3b810: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3b814: r0 = AllocateGrowableArray()
    //     0xa3b814: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b818: mov             x3, x0
    // 0xa3b81c: ldur            x0, [fp, #-0x20]
    // 0xa3b820: stur            x3, [fp, #-0x18]
    // 0xa3b824: StoreField: r3->field_f = r0
    //     0xa3b824: stur            w0, [x3, #0xf]
    // 0xa3b828: r0 = 2
    //     0xa3b828: movz            x0, #0x2
    // 0xa3b82c: StoreField: r3->field_b = r0
    //     0xa3b82c: stur            w0, [x3, #0xb]
    // 0xa3b830: r1 = Null
    //     0xa3b830: mov             x1, NULL
    // 0xa3b834: r2 = 4
    //     0xa3b834: movz            x2, #0x4
    // 0xa3b838: r0 = AllocateArray()
    //     0xa3b838: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b83c: stur            x0, [fp, #-0x20]
    // 0xa3b840: r16 = "Ayo berzakat dan bersedekah melalui NU Online Super App!"
    //     0xa3b840: add             x16, PP, #0x40, lsl #12  ; [pp+0x40268] "Ayo berzakat dan bersedekah melalui NU Online Super App!"
    //     0xa3b844: ldr             x16, [x16, #0x268]
    // 0xa3b848: StoreField: r0->field_f = r16
    //     0xa3b848: stur            w16, [x0, #0xf]
    // 0xa3b84c: r16 = "https://nu.or.id/superapp"
    //     0xa3b84c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40270] "https://nu.or.id/superapp"
    //     0xa3b850: ldr             x16, [x16, #0x270]
    // 0xa3b854: StoreField: r0->field_13 = r16
    //     0xa3b854: stur            w16, [x0, #0x13]
    // 0xa3b858: r1 = <String>
    //     0xa3b858: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3b85c: r0 = AllocateGrowableArray()
    //     0xa3b85c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3b860: mov             x1, x0
    // 0xa3b864: ldur            x0, [fp, #-0x20]
    // 0xa3b868: StoreField: r1->field_f = r0
    //     0xa3b868: stur            w0, [x1, #0xf]
    // 0xa3b86c: r0 = 4
    //     0xa3b86c: movz            x0, #0x4
    // 0xa3b870: StoreField: r1->field_b = r0
    //     0xa3b870: stur            w0, [x1, #0xb]
    // 0xa3b874: r16 = "\n"
    //     0xa3b874: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xa3b878: str             x16, [SP]
    // 0xa3b87c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa3b87c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa3b880: r0 = join()
    //     0xa3b880: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xa3b884: ldur            x1, [fp, #-0x18]
    // 0xa3b888: mov             x2, x0
    // 0xa3b88c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa3b88c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa3b890: r0 = shareFiles()
    //     0xa3b890: bl              #0xa3b8b0  ; [package:share_plus/share_plus.dart] Share::shareFiles
    // 0xa3b894: mov             x1, x0
    // 0xa3b898: stur            x1, [fp, #-0x18]
    // 0xa3b89c: r0 = Await()
    //     0xa3b89c: bl              #0x661044  ; AwaitStub
    // 0xa3b8a0: r0 = Null
    //     0xa3b8a0: mov             x0, NULL
    // 0xa3b8a4: r0 = ReturnAsyncNotFuture()
    //     0xa3b8a4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa3b8a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b8a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b8ac: b               #0xa3b7a4
  }
}

// class id: 4712, size: 0x10, field offset: 0xc
//   const constructor, 
class TransactionSuccess extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94750, size: 0x48
    // 0xa94750: EnterFrame
    //     0xa94750: stp             fp, lr, [SP, #-0x10]!
    //     0xa94754: mov             fp, SP
    // 0xa94758: AllocStack(0x8)
    //     0xa94758: sub             SP, SP, #8
    // 0xa9475c: r0 = ScreenshotController()
    //     0xa9475c: bl              #0xa94744  ; AllocateScreenshotControllerStub -> ScreenshotController (size=0xc)
    // 0xa94760: r1 = <State<StatefulWidget>>
    //     0xa94760: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa94764: stur            x0, [fp, #-8]
    // 0xa94768: r0 = LabeledGlobalKey()
    //     0xa94768: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa9476c: mov             x1, x0
    // 0xa94770: ldur            x0, [fp, #-8]
    // 0xa94774: StoreField: r0->field_7 = r1
    //     0xa94774: stur            w1, [x0, #7]
    // 0xa94778: r1 = <TransactionSuccess>
    //     0xa94778: add             x1, PP, #0x35, lsl #12  ; [pp+0x35328] TypeArguments: <TransactionSuccess>
    //     0xa9477c: ldr             x1, [x1, #0x328]
    // 0xa94780: r0 = _TransactionSuccessState()
    //     0xa94780: bl              #0xa94798  ; Allocate_TransactionSuccessStateStub -> _TransactionSuccessState (size=0x18)
    // 0xa94784: ldur            x1, [fp, #-8]
    // 0xa94788: StoreField: r0->field_13 = r1
    //     0xa94788: stur            w1, [x0, #0x13]
    // 0xa9478c: LeaveFrame
    //     0xa9478c: mov             SP, fp
    //     0xa94790: ldp             fp, lr, [SP], #0x10
    // 0xa94794: ret
    //     0xa94794: ret             
  }
}
