// lib: , url: package:nuonline/app/modules/donation/widgets/user_info.dart

// class id: 1050252, size: 0x8
class :: {
}

// class id: 5018, size: 0x24, field offset: 0xc
//   const constructor, 
class UserInfo extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb964a8, size: 0x50c
    // 0xb964a8: EnterFrame
    //     0xb964a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb964ac: mov             fp, SP
    // 0xb964b0: AllocStack(0x78)
    //     0xb964b0: sub             SP, SP, #0x78
    // 0xb964b4: SetupParameters(UserInfo this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xb964b4: mov             x0, x1
    //     0xb964b8: stur            x1, [fp, #-8]
    //     0xb964bc: mov             x1, x2
    // 0xb964c0: CheckStackOverflow
    //     0xb964c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb964c4: cmp             SP, x16
    //     0xb964c8: b.ls            #0xb969a0
    // 0xb964cc: r0 = of()
    //     0xb964cc: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb964d0: mov             x1, x0
    // 0xb964d4: ldur            x0, [fp, #-8]
    // 0xb964d8: stur            x1, [fp, #-0x10]
    // 0xb964dc: LoadField: r2 = r0->field_1f
    //     0xb964dc: ldur            w2, [x0, #0x1f]
    // 0xb964e0: DecompressPointer r2
    //     0xb964e0: add             x2, x2, HEAP, lsl #32
    // 0xb964e4: cmp             w2, NULL
    // 0xb964e8: b.ne            #0xb96528
    // 0xb964ec: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb964ec: ldur            w2, [x0, #0x17]
    // 0xb964f0: DecompressPointer r2
    //     0xb964f0: add             x2, x2, HEAP, lsl #32
    // 0xb964f4: tbnz            w2, #4, #0xb96500
    // 0xb964f8: d0 = 4.000000
    //     0xb964f8: fmov            d0, #4.00000000
    // 0xb964fc: b               #0xb96504
    // 0xb96500: d0 = 8.000000
    //     0xb96500: fmov            d0, #8.00000000
    // 0xb96504: stur            d0, [fp, #-0x60]
    // 0xb96508: r0 = EdgeInsets()
    //     0xb96508: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb9650c: StoreField: r0->field_7 = rZR
    //     0xb9650c: stur            xzr, [x0, #7]
    // 0xb96510: StoreField: r0->field_f = rZR
    //     0xb96510: stur            xzr, [x0, #0xf]
    // 0xb96514: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb96514: stur            xzr, [x0, #0x17]
    // 0xb96518: ldur            d0, [fp, #-0x60]
    // 0xb9651c: StoreField: r0->field_1f = d0
    //     0xb9651c: stur            d0, [x0, #0x1f]
    // 0xb96520: mov             x3, x0
    // 0xb96524: b               #0xb9652c
    // 0xb96528: mov             x3, x2
    // 0xb9652c: ldur            x0, [fp, #-8]
    // 0xb96530: stur            x3, [fp, #-0x18]
    // 0xb96534: r1 = <Widget>
    //     0xb96534: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb96538: r2 = 0
    //     0xb96538: movz            x2, #0
    // 0xb9653c: r0 = _GrowableList()
    //     0xb9653c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb96540: mov             x3, x0
    // 0xb96544: ldur            x0, [fp, #-8]
    // 0xb96548: stur            x3, [fp, #-0x40]
    // 0xb9654c: LoadField: r4 = r0->field_b
    //     0xb9654c: ldur            w4, [x0, #0xb]
    // 0xb96550: DecompressPointer r4
    //     0xb96550: add             x4, x4, HEAP, lsl #32
    // 0xb96554: stur            x4, [fp, #-0x38]
    // 0xb96558: cmp             w4, NULL
    // 0xb9655c: b.eq            #0xb966b0
    // 0xb96560: ldur            x5, [fp, #-0x10]
    // 0xb96564: LoadField: r1 = r5->field_8f
    //     0xb96564: ldur            w1, [x5, #0x8f]
    // 0xb96568: DecompressPointer r1
    //     0xb96568: add             x1, x1, HEAP, lsl #32
    // 0xb9656c: LoadField: r6 = r1->field_23
    //     0xb9656c: ldur            w6, [x1, #0x23]
    // 0xb96570: DecompressPointer r6
    //     0xb96570: add             x6, x6, HEAP, lsl #32
    // 0xb96574: stur            x6, [fp, #-0x30]
    // 0xb96578: cmp             w6, NULL
    // 0xb9657c: b.eq            #0xb969a8
    // 0xb96580: LoadField: r7 = r0->field_13
    //     0xb96580: ldur            w7, [x0, #0x13]
    // 0xb96584: DecompressPointer r7
    //     0xb96584: add             x7, x7, HEAP, lsl #32
    // 0xb96588: stur            x7, [fp, #-0x28]
    // 0xb9658c: LoadField: r1 = r5->field_3f
    //     0xb9658c: ldur            w1, [x5, #0x3f]
    // 0xb96590: DecompressPointer r1
    //     0xb96590: add             x1, x1, HEAP, lsl #32
    // 0xb96594: LoadField: r8 = r1->field_7
    //     0xb96594: ldur            w8, [x1, #7]
    // 0xb96598: DecompressPointer r8
    //     0xb96598: add             x8, x8, HEAP, lsl #32
    // 0xb9659c: stur            x8, [fp, #-0x20]
    // 0xb965a0: r1 = _ConstMap len:6
    //     0xb965a0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb965a4: ldr             x1, [x1, #0xc20]
    // 0xb965a8: r2 = 8
    //     0xb965a8: movz            x2, #0x8
    // 0xb965ac: r0 = []()
    //     0xb965ac: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb965b0: r1 = _ConstMap len:6
    //     0xb965b0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb965b4: ldr             x1, [x1, #0xc20]
    // 0xb965b8: r2 = 2
    //     0xb965b8: movz            x2, #0x2
    // 0xb965bc: stur            x0, [fp, #-0x48]
    // 0xb965c0: r0 = []()
    //     0xb965c0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb965c4: mov             x1, x0
    // 0xb965c8: ldur            x0, [fp, #-0x20]
    // 0xb965cc: r16 = Instance_Brightness
    //     0xb965cc: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xb965d0: cmp             w0, w16
    // 0xb965d4: b.ne            #0xb965dc
    // 0xb965d8: ldur            x1, [fp, #-0x48]
    // 0xb965dc: ldur            x0, [fp, #-0x40]
    // 0xb965e0: ldur            x2, [fp, #-0x38]
    // 0xb965e4: ldur            x16, [fp, #-0x28]
    // 0xb965e8: stp             x1, x16, [SP]
    // 0xb965ec: ldur            x1, [fp, #-0x30]
    // 0xb965f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb965f0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb965f4: ldr             x4, [x4, #0xaa0]
    // 0xb965f8: r0 = copyWith()
    //     0xb965f8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb965fc: stur            x0, [fp, #-0x20]
    // 0xb96600: r0 = Text()
    //     0xb96600: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb96604: mov             x2, x0
    // 0xb96608: ldur            x0, [fp, #-0x38]
    // 0xb9660c: stur            x2, [fp, #-0x28]
    // 0xb96610: StoreField: r2->field_b = r0
    //     0xb96610: stur            w0, [x2, #0xb]
    // 0xb96614: ldur            x0, [fp, #-0x20]
    // 0xb96618: StoreField: r2->field_13 = r0
    //     0xb96618: stur            w0, [x2, #0x13]
    // 0xb9661c: r0 = Instance__LinearTextScaler
    //     0xb9661c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb96620: StoreField: r2->field_33 = r0
    //     0xb96620: stur            w0, [x2, #0x33]
    // 0xb96624: ldur            x3, [fp, #-0x40]
    // 0xb96628: LoadField: r1 = r3->field_b
    //     0xb96628: ldur            w1, [x3, #0xb]
    // 0xb9662c: LoadField: r4 = r3->field_f
    //     0xb9662c: ldur            w4, [x3, #0xf]
    // 0xb96630: DecompressPointer r4
    //     0xb96630: add             x4, x4, HEAP, lsl #32
    // 0xb96634: LoadField: r5 = r4->field_b
    //     0xb96634: ldur            w5, [x4, #0xb]
    // 0xb96638: r4 = LoadInt32Instr(r1)
    //     0xb96638: sbfx            x4, x1, #1, #0x1f
    // 0xb9663c: stur            x4, [fp, #-0x50]
    // 0xb96640: r1 = LoadInt32Instr(r5)
    //     0xb96640: sbfx            x1, x5, #1, #0x1f
    // 0xb96644: cmp             x4, x1
    // 0xb96648: b.ne            #0xb96654
    // 0xb9664c: mov             x1, x3
    // 0xb96650: r0 = _growToNextCapacity()
    //     0xb96650: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96654: ldur            x2, [fp, #-0x40]
    // 0xb96658: ldur            x3, [fp, #-0x50]
    // 0xb9665c: add             x4, x3, #1
    // 0xb96660: lsl             x0, x4, #1
    // 0xb96664: StoreField: r2->field_b = r0
    //     0xb96664: stur            w0, [x2, #0xb]
    // 0xb96668: LoadField: r5 = r2->field_f
    //     0xb96668: ldur            w5, [x2, #0xf]
    // 0xb9666c: DecompressPointer r5
    //     0xb9666c: add             x5, x5, HEAP, lsl #32
    // 0xb96670: mov             x1, x5
    // 0xb96674: ldur            x0, [fp, #-0x28]
    // 0xb96678: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb96678: add             x25, x1, x3, lsl #2
    //     0xb9667c: add             x25, x25, #0xf
    //     0xb96680: str             w0, [x25]
    //     0xb96684: tbz             w0, #0, #0xb966a0
    //     0xb96688: ldurb           w16, [x1, #-1]
    //     0xb9668c: ldurb           w17, [x0, #-1]
    //     0xb96690: and             x16, x17, x16, lsr #2
    //     0xb96694: tst             x16, HEAP, lsr #32
    //     0xb96698: b.eq            #0xb966a0
    //     0xb9669c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb966a0: mov             x3, x4
    // 0xb966a4: mov             x0, x2
    // 0xb966a8: mov             x2, x5
    // 0xb966ac: b               #0xb96718
    // 0xb966b0: mov             x2, x3
    // 0xb966b4: LoadField: r0 = r2->field_b
    //     0xb966b4: ldur            w0, [x2, #0xb]
    // 0xb966b8: LoadField: r1 = r2->field_f
    //     0xb966b8: ldur            w1, [x2, #0xf]
    // 0xb966bc: DecompressPointer r1
    //     0xb966bc: add             x1, x1, HEAP, lsl #32
    // 0xb966c0: LoadField: r3 = r1->field_b
    //     0xb966c0: ldur            w3, [x1, #0xb]
    // 0xb966c4: r4 = LoadInt32Instr(r0)
    //     0xb966c4: sbfx            x4, x0, #1, #0x1f
    // 0xb966c8: stur            x4, [fp, #-0x50]
    // 0xb966cc: r0 = LoadInt32Instr(r3)
    //     0xb966cc: sbfx            x0, x3, #1, #0x1f
    // 0xb966d0: cmp             x4, x0
    // 0xb966d4: b.ne            #0xb966e0
    // 0xb966d8: mov             x1, x2
    // 0xb966dc: r0 = _growToNextCapacity()
    //     0xb966dc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb966e0: ldur            x0, [fp, #-0x40]
    // 0xb966e4: ldur            x1, [fp, #-0x50]
    // 0xb966e8: add             x2, x1, #1
    // 0xb966ec: lsl             x3, x2, #1
    // 0xb966f0: StoreField: r0->field_b = r3
    //     0xb966f0: stur            w3, [x0, #0xb]
    // 0xb966f4: LoadField: r3 = r0->field_f
    //     0xb966f4: ldur            w3, [x0, #0xf]
    // 0xb966f8: DecompressPointer r3
    //     0xb966f8: add             x3, x3, HEAP, lsl #32
    // 0xb966fc: add             x4, x3, x1, lsl #2
    // 0xb96700: r16 = Instance_SizedBox
    //     0xb96700: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb96704: ldr             x16, [x16, #0xc40]
    // 0xb96708: StoreField: r4->field_f = r16
    //     0xb96708: stur            w16, [x4, #0xf]
    // 0xb9670c: mov             x16, x3
    // 0xb96710: mov             x3, x2
    // 0xb96714: mov             x2, x16
    // 0xb96718: ldur            x1, [fp, #-8]
    // 0xb9671c: stur            x3, [fp, #-0x58]
    // 0xb96720: LoadField: r4 = r1->field_f
    //     0xb96720: ldur            w4, [x1, #0xf]
    // 0xb96724: DecompressPointer r4
    //     0xb96724: add             x4, x4, HEAP, lsl #32
    // 0xb96728: stur            x4, [fp, #-0x30]
    // 0xb9672c: cmp             w4, NULL
    // 0xb96730: b.eq            #0xb968cc
    // 0xb96734: ldur            x2, [fp, #-0x10]
    // 0xb96738: LoadField: r3 = r2->field_8f
    //     0xb96738: ldur            w3, [x2, #0x8f]
    // 0xb9673c: DecompressPointer r3
    //     0xb9673c: add             x3, x3, HEAP, lsl #32
    // 0xb96740: LoadField: r5 = r3->field_23
    //     0xb96740: ldur            w5, [x3, #0x23]
    // 0xb96744: DecompressPointer r5
    //     0xb96744: add             x5, x5, HEAP, lsl #32
    // 0xb96748: stur            x5, [fp, #-0x28]
    // 0xb9674c: cmp             w5, NULL
    // 0xb96750: b.eq            #0xb969ac
    // 0xb96754: LoadField: r3 = r1->field_13
    //     0xb96754: ldur            w3, [x1, #0x13]
    // 0xb96758: DecompressPointer r3
    //     0xb96758: add             x3, x3, HEAP, lsl #32
    // 0xb9675c: stur            x3, [fp, #-0x20]
    // 0xb96760: LoadField: r6 = r1->field_1b
    //     0xb96760: ldur            w6, [x1, #0x1b]
    // 0xb96764: DecompressPointer r6
    //     0xb96764: add             x6, x6, HEAP, lsl #32
    // 0xb96768: cmp             w6, NULL
    // 0xb9676c: b.ne            #0xb967bc
    // 0xb96770: LoadField: r1 = r2->field_3f
    //     0xb96770: ldur            w1, [x2, #0x3f]
    // 0xb96774: DecompressPointer r1
    //     0xb96774: add             x1, x1, HEAP, lsl #32
    // 0xb96778: LoadField: r6 = r1->field_7
    //     0xb96778: ldur            w6, [x1, #7]
    // 0xb9677c: DecompressPointer r6
    //     0xb9677c: add             x6, x6, HEAP, lsl #32
    // 0xb96780: stur            x6, [fp, #-8]
    // 0xb96784: r1 = _ConstMap len:3
    //     0xb96784: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb96788: ldr             x1, [x1, #0xbe8]
    // 0xb9678c: r2 = 6
    //     0xb9678c: movz            x2, #0x6
    // 0xb96790: r0 = []()
    //     0xb96790: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96794: mov             x1, x0
    // 0xb96798: ldur            x0, [fp, #-8]
    // 0xb9679c: r16 = Instance_Brightness
    //     0xb9679c: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xb967a0: cmp             w0, w16
    // 0xb967a4: b.ne            #0xb967b0
    // 0xb967a8: mov             x0, x1
    // 0xb967ac: b               #0xb967b4
    // 0xb967b0: r0 = Instance_Color
    //     0xb967b0: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb967b4: mov             x1, x0
    // 0xb967b8: b               #0xb967c0
    // 0xb967bc: mov             x1, x6
    // 0xb967c0: ldur            x0, [fp, #-0x40]
    // 0xb967c4: ldur            x2, [fp, #-0x30]
    // 0xb967c8: r16 = Instance_FontWeight
    //     0xb967c8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e28] Obj!FontWeight@e26531
    //     0xb967cc: ldr             x16, [x16, #0xe28]
    // 0xb967d0: ldur            lr, [fp, #-0x20]
    // 0xb967d4: stp             lr, x16, [SP, #8]
    // 0xb967d8: str             x1, [SP]
    // 0xb967dc: ldur            x1, [fp, #-0x28]
    // 0xb967e0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb967e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e998] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb967e4: ldr             x4, [x4, #0x998]
    // 0xb967e8: r0 = copyWith()
    //     0xb967e8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb967ec: stur            x0, [fp, #-8]
    // 0xb967f0: r0 = Text()
    //     0xb967f0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb967f4: mov             x2, x0
    // 0xb967f8: ldur            x0, [fp, #-0x30]
    // 0xb967fc: stur            x2, [fp, #-0x10]
    // 0xb96800: StoreField: r2->field_b = r0
    //     0xb96800: stur            w0, [x2, #0xb]
    // 0xb96804: ldur            x0, [fp, #-8]
    // 0xb96808: StoreField: r2->field_13 = r0
    //     0xb96808: stur            w0, [x2, #0x13]
    // 0xb9680c: r0 = Instance_TextAlign
    //     0xb9680c: ldr             x0, [PP, #0x4940]  ; [pp+0x4940] Obj!TextAlign@e39461
    // 0xb96810: StoreField: r2->field_1b = r0
    //     0xb96810: stur            w0, [x2, #0x1b]
    // 0xb96814: r0 = Instance__LinearTextScaler
    //     0xb96814: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb96818: StoreField: r2->field_33 = r0
    //     0xb96818: stur            w0, [x2, #0x33]
    // 0xb9681c: r1 = <FlexParentData>
    //     0xb9681c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb96820: ldr             x1, [x1, #0x720]
    // 0xb96824: r0 = Flexible()
    //     0xb96824: bl              #0x9e6a68  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb96828: mov             x2, x0
    // 0xb9682c: r0 = 1
    //     0xb9682c: movz            x0, #0x1
    // 0xb96830: stur            x2, [fp, #-8]
    // 0xb96834: StoreField: r2->field_13 = r0
    //     0xb96834: stur            x0, [x2, #0x13]
    // 0xb96838: r0 = Instance_FlexFit
    //     0xb96838: add             x0, PP, #0x29, lsl #12  ; [pp+0x29d68] Obj!FlexFit@e35b61
    //     0xb9683c: ldr             x0, [x0, #0xd68]
    // 0xb96840: StoreField: r2->field_1b = r0
    //     0xb96840: stur            w0, [x2, #0x1b]
    // 0xb96844: ldur            x0, [fp, #-0x10]
    // 0xb96848: StoreField: r2->field_b = r0
    //     0xb96848: stur            w0, [x2, #0xb]
    // 0xb9684c: ldur            x0, [fp, #-0x40]
    // 0xb96850: LoadField: r1 = r0->field_b
    //     0xb96850: ldur            w1, [x0, #0xb]
    // 0xb96854: LoadField: r3 = r0->field_f
    //     0xb96854: ldur            w3, [x0, #0xf]
    // 0xb96858: DecompressPointer r3
    //     0xb96858: add             x3, x3, HEAP, lsl #32
    // 0xb9685c: LoadField: r4 = r3->field_b
    //     0xb9685c: ldur            w4, [x3, #0xb]
    // 0xb96860: r3 = LoadInt32Instr(r1)
    //     0xb96860: sbfx            x3, x1, #1, #0x1f
    // 0xb96864: stur            x3, [fp, #-0x50]
    // 0xb96868: r1 = LoadInt32Instr(r4)
    //     0xb96868: sbfx            x1, x4, #1, #0x1f
    // 0xb9686c: cmp             x3, x1
    // 0xb96870: b.ne            #0xb9687c
    // 0xb96874: mov             x1, x0
    // 0xb96878: r0 = _growToNextCapacity()
    //     0xb96878: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9687c: ldur            x4, [fp, #-0x40]
    // 0xb96880: ldur            x2, [fp, #-0x50]
    // 0xb96884: add             x0, x2, #1
    // 0xb96888: lsl             x1, x0, #1
    // 0xb9688c: StoreField: r4->field_b = r1
    //     0xb9688c: stur            w1, [x4, #0xb]
    // 0xb96890: LoadField: r1 = r4->field_f
    //     0xb96890: ldur            w1, [x4, #0xf]
    // 0xb96894: DecompressPointer r1
    //     0xb96894: add             x1, x1, HEAP, lsl #32
    // 0xb96898: ldur            x0, [fp, #-8]
    // 0xb9689c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb9689c: add             x25, x1, x2, lsl #2
    //     0xb968a0: add             x25, x25, #0xf
    //     0xb968a4: str             w0, [x25]
    //     0xb968a8: tbz             w0, #0, #0xb968c4
    //     0xb968ac: ldurb           w16, [x1, #-1]
    //     0xb968b0: ldurb           w17, [x0, #-1]
    //     0xb968b4: and             x16, x17, x16, lsr #2
    //     0xb968b8: tst             x16, HEAP, lsr #32
    //     0xb968bc: b.eq            #0xb968c4
    //     0xb968c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb968c4: mov             x3, x4
    // 0xb968c8: b               #0xb96920
    // 0xb968cc: mov             x4, x0
    // 0xb968d0: LoadField: r0 = r2->field_b
    //     0xb968d0: ldur            w0, [x2, #0xb]
    // 0xb968d4: r1 = LoadInt32Instr(r0)
    //     0xb968d4: sbfx            x1, x0, #1, #0x1f
    // 0xb968d8: cmp             x3, x1
    // 0xb968dc: b.ne            #0xb968e8
    // 0xb968e0: mov             x1, x4
    // 0xb968e4: r0 = _growToNextCapacity()
    //     0xb968e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb968e8: ldur            x3, [fp, #-0x40]
    // 0xb968ec: ldur            x2, [fp, #-0x58]
    // 0xb968f0: add             x0, x2, #1
    // 0xb968f4: lsl             x1, x0, #1
    // 0xb968f8: StoreField: r3->field_b = r1
    //     0xb968f8: stur            w1, [x3, #0xb]
    // 0xb968fc: mov             x1, x2
    // 0xb96900: cmp             x1, x0
    // 0xb96904: b.hs            #0xb969b0
    // 0xb96908: LoadField: r0 = r3->field_f
    //     0xb96908: ldur            w0, [x3, #0xf]
    // 0xb9690c: DecompressPointer r0
    //     0xb9690c: add             x0, x0, HEAP, lsl #32
    // 0xb96910: add             x1, x0, x2, lsl #2
    // 0xb96914: r16 = Instance_SizedBox
    //     0xb96914: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb96918: ldr             x16, [x16, #0xc40]
    // 0xb9691c: StoreField: r1->field_f = r16
    //     0xb9691c: stur            w16, [x1, #0xf]
    // 0xb96920: ldur            x0, [fp, #-0x18]
    // 0xb96924: r0 = Row()
    //     0xb96924: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb96928: mov             x1, x0
    // 0xb9692c: r0 = Instance_Axis
    //     0xb9692c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb96930: stur            x1, [fp, #-8]
    // 0xb96934: StoreField: r1->field_f = r0
    //     0xb96934: stur            w0, [x1, #0xf]
    // 0xb96938: r0 = Instance_MainAxisAlignment
    //     0xb96938: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb9693c: ldr             x0, [x0, #0xae8]
    // 0xb96940: StoreField: r1->field_13 = r0
    //     0xb96940: stur            w0, [x1, #0x13]
    // 0xb96944: r0 = Instance_MainAxisSize
    //     0xb96944: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb96948: ldr             x0, [x0, #0x738]
    // 0xb9694c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9694c: stur            w0, [x1, #0x17]
    // 0xb96950: r0 = Instance_CrossAxisAlignment
    //     0xb96950: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb96954: ldr             x0, [x0, #0x68]
    // 0xb96958: StoreField: r1->field_1b = r0
    //     0xb96958: stur            w0, [x1, #0x1b]
    // 0xb9695c: r0 = Instance_VerticalDirection
    //     0xb9695c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb96960: ldr             x0, [x0, #0x748]
    // 0xb96964: StoreField: r1->field_23 = r0
    //     0xb96964: stur            w0, [x1, #0x23]
    // 0xb96968: r0 = Instance_Clip
    //     0xb96968: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb9696c: ldr             x0, [x0, #0x750]
    // 0xb96970: StoreField: r1->field_2b = r0
    //     0xb96970: stur            w0, [x1, #0x2b]
    // 0xb96974: StoreField: r1->field_2f = rZR
    //     0xb96974: stur            xzr, [x1, #0x2f]
    // 0xb96978: ldur            x0, [fp, #-0x40]
    // 0xb9697c: StoreField: r1->field_b = r0
    //     0xb9697c: stur            w0, [x1, #0xb]
    // 0xb96980: r0 = Padding()
    //     0xb96980: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb96984: ldur            x1, [fp, #-0x18]
    // 0xb96988: StoreField: r0->field_f = r1
    //     0xb96988: stur            w1, [x0, #0xf]
    // 0xb9698c: ldur            x1, [fp, #-8]
    // 0xb96990: StoreField: r0->field_b = r1
    //     0xb96990: stur            w1, [x0, #0xb]
    // 0xb96994: LeaveFrame
    //     0xb96994: mov             SP, fp
    //     0xb96998: ldp             fp, lr, [SP], #0x10
    // 0xb9699c: ret
    //     0xb9699c: ret             
    // 0xb969a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb969a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb969a4: b               #0xb964cc
    // 0xb969a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb969a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb969ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb969ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb969b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb969b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
