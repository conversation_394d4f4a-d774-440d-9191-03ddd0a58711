// lib: , url: package:nuonline/app/modules/donation/widgets/zakat_form.dart

// class id: 1050253, size: 0x8
class :: {
}

// class id: 5282, size: 0x14, field offset: 0x14
//   const constructor, 
class ZakatFormWidget extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaedc98, size: 0x84
    // 0xaedc98: EnterFrame
    //     0xaedc98: stp             fp, lr, [SP, #-0x10]!
    //     0xaedc9c: mov             fp, SP
    // 0xaedca0: AllocStack(0x30)
    //     0xaedca0: sub             SP, SP, #0x30
    // 0xaedca4: SetupParameters(ZakatFormWidget this /* r1 => r1, fp-0x8 */)
    //     0xaedca4: stur            x1, [fp, #-8]
    // 0xaedca8: CheckStackOverflow
    //     0xaedca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedcac: cmp             SP, x16
    //     0xaedcb0: b.ls            #0xaedd14
    // 0xaedcb4: r1 = 1
    //     0xaedcb4: movz            x1, #0x1
    // 0xaedcb8: r0 = AllocateContext()
    //     0xaedcb8: bl              #0xec126c  ; AllocateContextStub
    // 0xaedcbc: ldur            x1, [fp, #-8]
    // 0xaedcc0: stur            x0, [fp, #-0x10]
    // 0xaedcc4: StoreField: r0->field_f = r1
    //     0xaedcc4: stur            w1, [x0, #0xf]
    // 0xaedcc8: r0 = controller()
    //     0xaedcc8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaedccc: ldur            x2, [fp, #-0x10]
    // 0xaedcd0: r1 = Function '<anonymous closure>':.
    //     0xaedcd0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35118] AnonymousClosure: (0xaedd1c), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaedcd4: ldr             x1, [x1, #0x118]
    // 0xaedcd8: stur            x0, [fp, #-8]
    // 0xaedcdc: r0 = AllocateClosure()
    //     0xaedcdc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaedce0: r16 = <ZakatSetting>
    //     0xaedce0: add             x16, PP, #0x34, lsl #12  ; [pp+0x347a0] TypeArguments: <ZakatSetting>
    //     0xaedce4: ldr             x16, [x16, #0x7a0]
    // 0xaedce8: ldur            lr, [fp, #-8]
    // 0xaedcec: stp             lr, x16, [SP, #0x10]
    // 0xaedcf0: r16 = Instance_NSection
    //     0xaedcf0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35120] Obj!NSection@e20ae1
    //     0xaedcf4: ldr             x16, [x16, #0x120]
    // 0xaedcf8: stp             x16, x0, [SP]
    // 0xaedcfc: r4 = const [0x1, 0x3, 0x3, 0x2, onLoading, 0x2, null]
    //     0xaedcfc: add             x4, PP, #0x25, lsl #12  ; [pp+0x25718] List(7) [0x1, 0x3, 0x3, 0x2, "onLoading", 0x2, Null]
    //     0xaedd00: ldr             x4, [x4, #0x718]
    // 0xaedd04: r0 = StateExt.obx()
    //     0xaedd04: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaedd08: LeaveFrame
    //     0xaedd08: mov             SP, fp
    //     0xaedd0c: ldp             fp, lr, [SP], #0x10
    // 0xaedd10: ret
    //     0xaedd10: ret             
    // 0xaedd14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaedd14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaedd18: b               #0xaedcb4
  }
  [closure] NSection <anonymous closure>(dynamic, ZakatSetting?) {
    // ** addr: 0xaedd1c, size: 0x724
    // 0xaedd1c: EnterFrame
    //     0xaedd1c: stp             fp, lr, [SP, #-0x10]!
    //     0xaedd20: mov             fp, SP
    // 0xaedd24: AllocStack(0x78)
    //     0xaedd24: sub             SP, SP, #0x78
    // 0xaedd28: SetupParameters()
    //     0xaedd28: ldr             x0, [fp, #0x18]
    //     0xaedd2c: ldur            w3, [x0, #0x17]
    //     0xaedd30: add             x3, x3, HEAP, lsl #32
    //     0xaedd34: stur            x3, [fp, #-8]
    // 0xaedd38: CheckStackOverflow
    //     0xaedd38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaedd3c: cmp             SP, x16
    //     0xaedd40: b.ls            #0xaee438
    // 0xaedd44: r1 = <Widget>
    //     0xaedd44: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaedd48: r2 = 0
    //     0xaedd48: movz            x2, #0
    // 0xaedd4c: r0 = _GrowableList()
    //     0xaedd4c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaedd50: ldur            x2, [fp, #-8]
    // 0xaedd54: stur            x0, [fp, #-0x10]
    // 0xaedd58: LoadField: r1 = r2->field_f
    //     0xaedd58: ldur            w1, [x2, #0xf]
    // 0xaedd5c: DecompressPointer r1
    //     0xaedd5c: add             x1, x1, HEAP, lsl #32
    // 0xaedd60: r0 = controller()
    //     0xaedd60: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaedd64: mov             x1, x0
    // 0xaedd68: r0 = preType()
    //     0xaedd68: bl              #0x8f5570  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::preType
    // 0xaedd6c: r16 = Instance_ZakatTypes
    //     0xaedd6c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xaedd70: ldr             x16, [x16, #0x620]
    // 0xaedd74: cmp             w0, w16
    // 0xaedd78: b.eq            #0xaeddf8
    // 0xaedd7c: r0 = Obx()
    //     0xaedd7c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaedd80: ldur            x2, [fp, #-8]
    // 0xaedd84: r1 = Function '<anonymous closure>':.
    //     0xaedd84: add             x1, PP, #0x35, lsl #12  ; [pp+0x35128] AnonymousClosure: (0xaf0f70), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaedd88: ldr             x1, [x1, #0x128]
    // 0xaedd8c: stur            x0, [fp, #-0x18]
    // 0xaedd90: r0 = AllocateClosure()
    //     0xaedd90: bl              #0xec1630  ; AllocateClosureStub
    // 0xaedd94: mov             x1, x0
    // 0xaedd98: ldur            x0, [fp, #-0x18]
    // 0xaedd9c: StoreField: r0->field_b = r1
    //     0xaedd9c: stur            w1, [x0, #0xb]
    // 0xaedda0: r1 = Null
    //     0xaedda0: mov             x1, NULL
    // 0xaedda4: r2 = 6
    //     0xaedda4: movz            x2, #0x6
    // 0xaedda8: r0 = AllocateArray()
    //     0xaedda8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaeddac: stur            x0, [fp, #-0x20]
    // 0xaeddb0: r16 = Instance_NLabelTextField
    //     0xaeddb0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35130] Obj!NLabelTextField@e1fc31
    //     0xaeddb4: ldr             x16, [x16, #0x130]
    // 0xaeddb8: StoreField: r0->field_f = r16
    //     0xaeddb8: stur            w16, [x0, #0xf]
    // 0xaeddbc: ldur            x1, [fp, #-0x18]
    // 0xaeddc0: StoreField: r0->field_13 = r1
    //     0xaeddc0: stur            w1, [x0, #0x13]
    // 0xaeddc4: r16 = Instance_SizedBox
    //     0xaeddc4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xaeddc8: ldr             x16, [x16, #0xfe8]
    // 0xaeddcc: ArrayStore: r0[0] = r16  ; List_4
    //     0xaeddcc: stur            w16, [x0, #0x17]
    // 0xaeddd0: r1 = <Widget>
    //     0xaeddd0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaeddd4: r0 = AllocateGrowableArray()
    //     0xaeddd4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaeddd8: mov             x1, x0
    // 0xaedddc: ldur            x0, [fp, #-0x20]
    // 0xaedde0: StoreField: r1->field_f = r0
    //     0xaedde0: stur            w0, [x1, #0xf]
    // 0xaedde4: r0 = 6
    //     0xaedde4: movz            x0, #0x6
    // 0xaedde8: StoreField: r1->field_b = r0
    //     0xaedde8: stur            w0, [x1, #0xb]
    // 0xaeddec: mov             x2, x1
    // 0xaeddf0: ldur            x1, [fp, #-0x10]
    // 0xaeddf4: r0 = addAll()
    //     0xaeddf4: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaeddf8: ldur            x1, [fp, #-0x10]
    // 0xaeddfc: r0 = Obx()
    //     0xaeddfc: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaede00: ldur            x2, [fp, #-8]
    // 0xaede04: r1 = Function '<anonymous closure>':.
    //     0xaede04: add             x1, PP, #0x35, lsl #12  ; [pp+0x35138] AnonymousClosure: (0xaf01d8), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaede08: ldr             x1, [x1, #0x138]
    // 0xaede0c: stur            x0, [fp, #-0x18]
    // 0xaede10: r0 = AllocateClosure()
    //     0xaede10: bl              #0xec1630  ; AllocateClosureStub
    // 0xaede14: mov             x1, x0
    // 0xaede18: ldur            x0, [fp, #-0x18]
    // 0xaede1c: StoreField: r0->field_b = r1
    //     0xaede1c: stur            w1, [x0, #0xb]
    // 0xaede20: ldur            x2, [fp, #-0x10]
    // 0xaede24: LoadField: r1 = r2->field_b
    //     0xaede24: ldur            w1, [x2, #0xb]
    // 0xaede28: LoadField: r3 = r2->field_f
    //     0xaede28: ldur            w3, [x2, #0xf]
    // 0xaede2c: DecompressPointer r3
    //     0xaede2c: add             x3, x3, HEAP, lsl #32
    // 0xaede30: LoadField: r4 = r3->field_b
    //     0xaede30: ldur            w4, [x3, #0xb]
    // 0xaede34: r3 = LoadInt32Instr(r1)
    //     0xaede34: sbfx            x3, x1, #1, #0x1f
    // 0xaede38: stur            x3, [fp, #-0x28]
    // 0xaede3c: r1 = LoadInt32Instr(r4)
    //     0xaede3c: sbfx            x1, x4, #1, #0x1f
    // 0xaede40: cmp             x3, x1
    // 0xaede44: b.ne            #0xaede50
    // 0xaede48: mov             x1, x2
    // 0xaede4c: r0 = _growToNextCapacity()
    //     0xaede4c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaede50: ldur            x2, [fp, #-0x10]
    // 0xaede54: ldur            x3, [fp, #-0x28]
    // 0xaede58: add             x4, x3, #1
    // 0xaede5c: stur            x4, [fp, #-0x30]
    // 0xaede60: lsl             x0, x4, #1
    // 0xaede64: StoreField: r2->field_b = r0
    //     0xaede64: stur            w0, [x2, #0xb]
    // 0xaede68: LoadField: r5 = r2->field_f
    //     0xaede68: ldur            w5, [x2, #0xf]
    // 0xaede6c: DecompressPointer r5
    //     0xaede6c: add             x5, x5, HEAP, lsl #32
    // 0xaede70: mov             x1, x5
    // 0xaede74: ldur            x0, [fp, #-0x18]
    // 0xaede78: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaede78: add             x25, x1, x3, lsl #2
    //     0xaede7c: add             x25, x25, #0xf
    //     0xaede80: str             w0, [x25]
    //     0xaede84: tbz             w0, #0, #0xaedea0
    //     0xaede88: ldurb           w16, [x1, #-1]
    //     0xaede8c: ldurb           w17, [x0, #-1]
    //     0xaede90: and             x16, x17, x16, lsr #2
    //     0xaede94: tst             x16, HEAP, lsr #32
    //     0xaede98: b.eq            #0xaedea0
    //     0xaede9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaedea0: LoadField: r0 = r5->field_b
    //     0xaedea0: ldur            w0, [x5, #0xb]
    // 0xaedea4: r1 = LoadInt32Instr(r0)
    //     0xaedea4: sbfx            x1, x0, #1, #0x1f
    // 0xaedea8: cmp             x4, x1
    // 0xaedeac: b.ne            #0xaedeb8
    // 0xaedeb0: mov             x1, x2
    // 0xaedeb4: r0 = _growToNextCapacity()
    //     0xaedeb4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaedeb8: ldur            x3, [fp, #-8]
    // 0xaedebc: ldur            x0, [fp, #-0x10]
    // 0xaedec0: ldur            x1, [fp, #-0x30]
    // 0xaedec4: add             x2, x1, #1
    // 0xaedec8: lsl             x4, x2, #1
    // 0xaedecc: StoreField: r0->field_b = r4
    //     0xaedecc: stur            w4, [x0, #0xb]
    // 0xaeded0: LoadField: r2 = r0->field_f
    //     0xaeded0: ldur            w2, [x0, #0xf]
    // 0xaeded4: DecompressPointer r2
    //     0xaeded4: add             x2, x2, HEAP, lsl #32
    // 0xaeded8: add             x4, x2, x1, lsl #2
    // 0xaededc: r16 = Instance_NLabelTextField
    //     0xaededc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b08] Obj!NLabelTextField@e1fbd1
    //     0xaedee0: ldr             x16, [x16, #0xb08]
    // 0xaedee4: StoreField: r4->field_f = r16
    //     0xaedee4: stur            w16, [x4, #0xf]
    // 0xaedee8: r1 = Function '<anonymous closure>': static.
    //     0xaedee8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaedeec: ldr             x1, [x1, #0xda0]
    // 0xaedef0: r2 = Null
    //     0xaedef0: mov             x2, NULL
    // 0xaedef4: r0 = AllocateClosure()
    //     0xaedef4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaedef8: r1 = <String>
    //     0xaedef8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaedefc: stur            x0, [fp, #-0x18]
    // 0xaedf00: StoreField: r0->field_b = r1
    //     0xaedf00: stur            w1, [x0, #0xb]
    // 0xaedf04: r16 = <String>
    //     0xaedf04: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaedf08: str             x16, [SP, #8]
    // 0xaedf0c: r2 = 55
    //     0xaedf0c: movz            x2, #0x37
    // 0xaedf10: str             x2, [SP]
    // 0xaedf14: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaedf14: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaedf18: r0 = maxLength()
    //     0xaedf18: bl              #0xae4b84  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::maxLength
    // 0xaedf1c: r1 = Null
    //     0xaedf1c: mov             x1, NULL
    // 0xaedf20: r2 = 4
    //     0xaedf20: movz            x2, #0x4
    // 0xaedf24: stur            x0, [fp, #-0x20]
    // 0xaedf28: r0 = AllocateArray()
    //     0xaedf28: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaedf2c: mov             x2, x0
    // 0xaedf30: ldur            x0, [fp, #-0x18]
    // 0xaedf34: stur            x2, [fp, #-0x38]
    // 0xaedf38: StoreField: r2->field_f = r0
    //     0xaedf38: stur            w0, [x2, #0xf]
    // 0xaedf3c: ldur            x0, [fp, #-0x20]
    // 0xaedf40: StoreField: r2->field_13 = r0
    //     0xaedf40: stur            w0, [x2, #0x13]
    // 0xaedf44: r1 = <(dynamic this, String?) => String?>
    //     0xaedf44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xaedf48: ldr             x1, [x1, #0xd98]
    // 0xaedf4c: r0 = AllocateGrowableArray()
    //     0xaedf4c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaedf50: mov             x1, x0
    // 0xaedf54: ldur            x0, [fp, #-0x38]
    // 0xaedf58: stur            x1, [fp, #-0x18]
    // 0xaedf5c: StoreField: r1->field_f = r0
    //     0xaedf5c: stur            w0, [x1, #0xf]
    // 0xaedf60: r2 = 4
    //     0xaedf60: movz            x2, #0x4
    // 0xaedf64: StoreField: r1->field_b = r2
    //     0xaedf64: stur            w2, [x1, #0xb]
    // 0xaedf68: r1 = 1
    //     0xaedf68: movz            x1, #0x1
    // 0xaedf6c: r0 = AllocateContext()
    //     0xaedf6c: bl              #0xec126c  ; AllocateContextStub
    // 0xaedf70: mov             x1, x0
    // 0xaedf74: ldur            x0, [fp, #-0x18]
    // 0xaedf78: StoreField: r1->field_f = r0
    //     0xaedf78: stur            w0, [x1, #0xf]
    // 0xaedf7c: mov             x2, x1
    // 0xaedf80: r1 = Function '<anonymous closure>': static.
    //     0xaedf80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaedf84: ldr             x1, [x1, #0xda8]
    // 0xaedf88: r0 = AllocateClosure()
    //     0xaedf88: bl              #0xec1630  ; AllocateClosureStub
    // 0xaedf8c: mov             x2, x0
    // 0xaedf90: r0 = <String>
    //     0xaedf90: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaedf94: stur            x2, [fp, #-0x18]
    // 0xaedf98: StoreField: r2->field_b = r0
    //     0xaedf98: stur            w0, [x2, #0xb]
    // 0xaedf9c: ldur            x3, [fp, #-8]
    // 0xaedfa0: LoadField: r1 = r3->field_f
    //     0xaedfa0: ldur            w1, [x3, #0xf]
    // 0xaedfa4: DecompressPointer r1
    //     0xaedfa4: add             x1, x1, HEAP, lsl #32
    // 0xaedfa8: r0 = controller()
    //     0xaedfa8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaedfac: LoadField: r2 = r0->field_27
    //     0xaedfac: ldur            w2, [x0, #0x27]
    // 0xaedfb0: DecompressPointer r2
    //     0xaedfb0: add             x2, x2, HEAP, lsl #32
    // 0xaedfb4: LoadField: r3 = r2->field_7
    //     0xaedfb4: ldur            w3, [x2, #7]
    // 0xaedfb8: DecompressPointer r3
    //     0xaedfb8: add             x3, x3, HEAP, lsl #32
    // 0xaedfbc: r1 = Function 'call':.
    //     0xaedfbc: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaedfc0: ldr             x1, [x1, #0x310]
    // 0xaedfc4: r0 = AllocateClosureTA()
    //     0xaedfc4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaedfc8: mov             x3, x0
    // 0xaedfcc: r2 = Null
    //     0xaedfcc: mov             x2, NULL
    // 0xaedfd0: r1 = Null
    //     0xaedfd0: mov             x1, NULL
    // 0xaedfd4: stur            x3, [fp, #-0x20]
    // 0xaedfd8: r8 = (dynamic this, String?) => String
    //     0xaedfd8: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xaedfdc: ldr             x8, [x8, #0x40]
    // 0xaedfe0: r3 = Null
    //     0xaedfe0: add             x3, PP, #0x35, lsl #12  ; [pp+0x35140] Null
    //     0xaedfe4: ldr             x3, [x3, #0x140]
    // 0xaedfe8: r0 = DefaultTypeTest()
    //     0xaedfe8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaedfec: r16 = "[a-zA-Z ]"
    //     0xaedfec: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xaedff0: ldr             x16, [x16, #0xb20]
    // 0xaedff4: stp             x16, NULL, [SP, #0x20]
    // 0xaedff8: r16 = false
    //     0xaedff8: add             x16, NULL, #0x30  ; false
    // 0xaedffc: r30 = true
    //     0xaedffc: add             lr, NULL, #0x20  ; true
    // 0xaee000: stp             lr, x16, [SP, #0x10]
    // 0xaee004: r16 = false
    //     0xaee004: add             x16, NULL, #0x30  ; false
    // 0xaee008: r30 = false
    //     0xaee008: add             lr, NULL, #0x30  ; false
    // 0xaee00c: stp             lr, x16, [SP]
    // 0xaee010: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xaee010: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xaee014: r0 = _RegExp()
    //     0xaee014: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xaee018: stur            x0, [fp, #-0x38]
    // 0xaee01c: r0 = FilteringTextInputFormatter()
    //     0xaee01c: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xaee020: mov             x3, x0
    // 0xaee024: ldur            x0, [fp, #-0x38]
    // 0xaee028: stur            x3, [fp, #-0x40]
    // 0xaee02c: StoreField: r3->field_7 = r0
    //     0xaee02c: stur            w0, [x3, #7]
    // 0xaee030: r0 = true
    //     0xaee030: add             x0, NULL, #0x20  ; true
    // 0xaee034: StoreField: r3->field_b = r0
    //     0xaee034: stur            w0, [x3, #0xb]
    // 0xaee038: r0 = ""
    //     0xaee038: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaee03c: StoreField: r3->field_f = r0
    //     0xaee03c: stur            w0, [x3, #0xf]
    // 0xaee040: r1 = Null
    //     0xaee040: mov             x1, NULL
    // 0xaee044: r2 = 2
    //     0xaee044: movz            x2, #0x2
    // 0xaee048: r0 = AllocateArray()
    //     0xaee048: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaee04c: mov             x2, x0
    // 0xaee050: ldur            x0, [fp, #-0x40]
    // 0xaee054: stur            x2, [fp, #-0x38]
    // 0xaee058: StoreField: r2->field_f = r0
    //     0xaee058: stur            w0, [x2, #0xf]
    // 0xaee05c: r1 = <TextInputFormatter>
    //     0xaee05c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xaee060: ldr             x1, [x1, #0x788]
    // 0xaee064: r0 = AllocateGrowableArray()
    //     0xaee064: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaee068: mov             x2, x0
    // 0xaee06c: ldur            x0, [fp, #-0x38]
    // 0xaee070: stur            x2, [fp, #-0x40]
    // 0xaee074: StoreField: r2->field_f = r0
    //     0xaee074: stur            w0, [x2, #0xf]
    // 0xaee078: r0 = 2
    //     0xaee078: movz            x0, #0x2
    // 0xaee07c: StoreField: r2->field_b = r0
    //     0xaee07c: stur            w0, [x2, #0xb]
    // 0xaee080: ldur            x0, [fp, #-8]
    // 0xaee084: LoadField: r1 = r0->field_f
    //     0xaee084: ldur            w1, [x0, #0xf]
    // 0xaee088: DecompressPointer r1
    //     0xaee088: add             x1, x1, HEAP, lsl #32
    // 0xaee08c: r0 = controller()
    //     0xaee08c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaee090: LoadField: r1 = r0->field_27
    //     0xaee090: ldur            w1, [x0, #0x27]
    // 0xaee094: DecompressPointer r1
    //     0xaee094: add             x1, x1, HEAP, lsl #32
    // 0xaee098: r0 = value()
    //     0xaee098: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaee09c: r1 = <String>
    //     0xaee09c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaee0a0: stur            x0, [fp, #-0x38]
    // 0xaee0a4: r0 = TextFormField()
    //     0xaee0a4: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xaee0a8: stur            x0, [fp, #-0x48]
    // 0xaee0ac: ldur            x16, [fp, #-0x18]
    // 0xaee0b0: ldur            lr, [fp, #-0x20]
    // 0xaee0b4: stp             lr, x16, [SP, #0x10]
    // 0xaee0b8: ldur            x16, [fp, #-0x40]
    // 0xaee0bc: ldur            lr, [fp, #-0x38]
    // 0xaee0c0: stp             lr, x16, [SP]
    // 0xaee0c4: mov             x1, x0
    // 0xaee0c8: r2 = Instance_InputDecoration
    //     0xaee0c8: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xaee0cc: ldr             x2, [x2, #0xb28]
    // 0xaee0d0: r4 = const [0, 0x6, 0x4, 0x2, initialValue, 0x5, inputFormatters, 0x4, onChanged, 0x3, validator, 0x2, null]
    //     0xaee0d0: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b30] List(13) [0, 0x6, 0x4, 0x2, "initialValue", 0x5, "inputFormatters", 0x4, "onChanged", 0x3, "validator", 0x2, Null]
    //     0xaee0d4: ldr             x4, [x4, #0xb30]
    // 0xaee0d8: r0 = TextFormField()
    //     0xaee0d8: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xaee0dc: ldur            x0, [fp, #-0x10]
    // 0xaee0e0: LoadField: r1 = r0->field_b
    //     0xaee0e0: ldur            w1, [x0, #0xb]
    // 0xaee0e4: LoadField: r2 = r0->field_f
    //     0xaee0e4: ldur            w2, [x0, #0xf]
    // 0xaee0e8: DecompressPointer r2
    //     0xaee0e8: add             x2, x2, HEAP, lsl #32
    // 0xaee0ec: LoadField: r3 = r2->field_b
    //     0xaee0ec: ldur            w3, [x2, #0xb]
    // 0xaee0f0: r2 = LoadInt32Instr(r1)
    //     0xaee0f0: sbfx            x2, x1, #1, #0x1f
    // 0xaee0f4: stur            x2, [fp, #-0x28]
    // 0xaee0f8: r1 = LoadInt32Instr(r3)
    //     0xaee0f8: sbfx            x1, x3, #1, #0x1f
    // 0xaee0fc: cmp             x2, x1
    // 0xaee100: b.ne            #0xaee10c
    // 0xaee104: mov             x1, x0
    // 0xaee108: r0 = _growToNextCapacity()
    //     0xaee108: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee10c: ldur            x2, [fp, #-0x10]
    // 0xaee110: ldur            x3, [fp, #-0x28]
    // 0xaee114: add             x4, x3, #1
    // 0xaee118: stur            x4, [fp, #-0x30]
    // 0xaee11c: lsl             x0, x4, #1
    // 0xaee120: StoreField: r2->field_b = r0
    //     0xaee120: stur            w0, [x2, #0xb]
    // 0xaee124: LoadField: r5 = r2->field_f
    //     0xaee124: ldur            w5, [x2, #0xf]
    // 0xaee128: DecompressPointer r5
    //     0xaee128: add             x5, x5, HEAP, lsl #32
    // 0xaee12c: mov             x1, x5
    // 0xaee130: ldur            x0, [fp, #-0x48]
    // 0xaee134: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaee134: add             x25, x1, x3, lsl #2
    //     0xaee138: add             x25, x25, #0xf
    //     0xaee13c: str             w0, [x25]
    //     0xaee140: tbz             w0, #0, #0xaee15c
    //     0xaee144: ldurb           w16, [x1, #-1]
    //     0xaee148: ldurb           w17, [x0, #-1]
    //     0xaee14c: and             x16, x17, x16, lsr #2
    //     0xaee150: tst             x16, HEAP, lsr #32
    //     0xaee154: b.eq            #0xaee15c
    //     0xaee158: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee15c: LoadField: r0 = r5->field_b
    //     0xaee15c: ldur            w0, [x5, #0xb]
    // 0xaee160: r1 = LoadInt32Instr(r0)
    //     0xaee160: sbfx            x1, x0, #1, #0x1f
    // 0xaee164: cmp             x4, x1
    // 0xaee168: b.ne            #0xaee174
    // 0xaee16c: mov             x1, x2
    // 0xaee170: r0 = _growToNextCapacity()
    //     0xaee170: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee174: ldur            x1, [fp, #-0x10]
    // 0xaee178: ldur            x0, [fp, #-0x30]
    // 0xaee17c: add             x2, x0, #1
    // 0xaee180: stur            x2, [fp, #-0x28]
    // 0xaee184: lsl             x3, x2, #1
    // 0xaee188: StoreField: r1->field_b = r3
    //     0xaee188: stur            w3, [x1, #0xb]
    // 0xaee18c: LoadField: r3 = r1->field_f
    //     0xaee18c: ldur            w3, [x1, #0xf]
    // 0xaee190: DecompressPointer r3
    //     0xaee190: add             x3, x3, HEAP, lsl #32
    // 0xaee194: stur            x3, [fp, #-0x18]
    // 0xaee198: add             x4, x3, x0, lsl #2
    // 0xaee19c: r16 = Instance_SizedBox
    //     0xaee19c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaee1a0: ldr             x16, [x16, #0x950]
    // 0xaee1a4: StoreField: r4->field_f = r16
    //     0xaee1a4: stur            w16, [x4, #0xf]
    // 0xaee1a8: r0 = Obx()
    //     0xaee1a8: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaee1ac: ldur            x2, [fp, #-8]
    // 0xaee1b0: r1 = Function '<anonymous closure>':.
    //     0xaee1b0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35150] AnonymousClosure: (0xaefac4), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaee1b4: ldr             x1, [x1, #0x150]
    // 0xaee1b8: stur            x0, [fp, #-0x20]
    // 0xaee1bc: r0 = AllocateClosure()
    //     0xaee1bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaee1c0: mov             x1, x0
    // 0xaee1c4: ldur            x0, [fp, #-0x20]
    // 0xaee1c8: StoreField: r0->field_b = r1
    //     0xaee1c8: stur            w1, [x0, #0xb]
    // 0xaee1cc: ldur            x1, [fp, #-0x18]
    // 0xaee1d0: LoadField: r2 = r1->field_b
    //     0xaee1d0: ldur            w2, [x1, #0xb]
    // 0xaee1d4: r1 = LoadInt32Instr(r2)
    //     0xaee1d4: sbfx            x1, x2, #1, #0x1f
    // 0xaee1d8: ldur            x2, [fp, #-0x28]
    // 0xaee1dc: cmp             x2, x1
    // 0xaee1e0: b.ne            #0xaee1ec
    // 0xaee1e4: ldur            x1, [fp, #-0x10]
    // 0xaee1e8: r0 = _growToNextCapacity()
    //     0xaee1e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee1ec: ldur            x3, [fp, #-0x10]
    // 0xaee1f0: ldur            x2, [fp, #-0x28]
    // 0xaee1f4: add             x4, x2, #1
    // 0xaee1f8: stur            x4, [fp, #-0x30]
    // 0xaee1fc: lsl             x0, x4, #1
    // 0xaee200: StoreField: r3->field_b = r0
    //     0xaee200: stur            w0, [x3, #0xb]
    // 0xaee204: LoadField: r5 = r3->field_f
    //     0xaee204: ldur            w5, [x3, #0xf]
    // 0xaee208: DecompressPointer r5
    //     0xaee208: add             x5, x5, HEAP, lsl #32
    // 0xaee20c: mov             x1, x5
    // 0xaee210: ldur            x0, [fp, #-0x20]
    // 0xaee214: stur            x5, [fp, #-0x18]
    // 0xaee218: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaee218: add             x25, x1, x2, lsl #2
    //     0xaee21c: add             x25, x25, #0xf
    //     0xaee220: str             w0, [x25]
    //     0xaee224: tbz             w0, #0, #0xaee240
    //     0xaee228: ldurb           w16, [x1, #-1]
    //     0xaee22c: ldurb           w17, [x0, #-1]
    //     0xaee230: and             x16, x17, x16, lsr #2
    //     0xaee234: tst             x16, HEAP, lsr #32
    //     0xaee238: b.eq            #0xaee240
    //     0xaee23c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee240: r0 = Obx()
    //     0xaee240: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaee244: ldur            x2, [fp, #-8]
    // 0xaee248: r1 = Function '<anonymous closure>':.
    //     0xaee248: add             x1, PP, #0x35, lsl #12  ; [pp+0x35158] AnonymousClosure: (0xaef9ac), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaee24c: ldr             x1, [x1, #0x158]
    // 0xaee250: stur            x0, [fp, #-0x20]
    // 0xaee254: r0 = AllocateClosure()
    //     0xaee254: bl              #0xec1630  ; AllocateClosureStub
    // 0xaee258: mov             x1, x0
    // 0xaee25c: ldur            x0, [fp, #-0x20]
    // 0xaee260: StoreField: r0->field_b = r1
    //     0xaee260: stur            w1, [x0, #0xb]
    // 0xaee264: r1 = Null
    //     0xaee264: mov             x1, NULL
    // 0xaee268: r2 = 4
    //     0xaee268: movz            x2, #0x4
    // 0xaee26c: r0 = AllocateArray()
    //     0xaee26c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaee270: mov             x2, x0
    // 0xaee274: ldur            x0, [fp, #-0x20]
    // 0xaee278: stur            x2, [fp, #-0x38]
    // 0xaee27c: StoreField: r2->field_f = r0
    //     0xaee27c: stur            w0, [x2, #0xf]
    // 0xaee280: r16 = Instance_Text
    //     0xaee280: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b50] Obj!Text@e21c31
    //     0xaee284: ldr             x16, [x16, #0xb50]
    // 0xaee288: StoreField: r2->field_13 = r16
    //     0xaee288: stur            w16, [x2, #0x13]
    // 0xaee28c: r1 = <Widget>
    //     0xaee28c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaee290: r0 = AllocateGrowableArray()
    //     0xaee290: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaee294: mov             x1, x0
    // 0xaee298: ldur            x0, [fp, #-0x38]
    // 0xaee29c: stur            x1, [fp, #-0x20]
    // 0xaee2a0: StoreField: r1->field_f = r0
    //     0xaee2a0: stur            w0, [x1, #0xf]
    // 0xaee2a4: r0 = 4
    //     0xaee2a4: movz            x0, #0x4
    // 0xaee2a8: StoreField: r1->field_b = r0
    //     0xaee2a8: stur            w0, [x1, #0xb]
    // 0xaee2ac: r0 = Row()
    //     0xaee2ac: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaee2b0: mov             x2, x0
    // 0xaee2b4: r0 = Instance_Axis
    //     0xaee2b4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaee2b8: stur            x2, [fp, #-0x38]
    // 0xaee2bc: StoreField: r2->field_f = r0
    //     0xaee2bc: stur            w0, [x2, #0xf]
    // 0xaee2c0: r0 = Instance_MainAxisAlignment
    //     0xaee2c0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaee2c4: ldr             x0, [x0, #0x730]
    // 0xaee2c8: StoreField: r2->field_13 = r0
    //     0xaee2c8: stur            w0, [x2, #0x13]
    // 0xaee2cc: r0 = Instance_MainAxisSize
    //     0xaee2cc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaee2d0: ldr             x0, [x0, #0x738]
    // 0xaee2d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xaee2d4: stur            w0, [x2, #0x17]
    // 0xaee2d8: r0 = Instance_CrossAxisAlignment
    //     0xaee2d8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaee2dc: ldr             x0, [x0, #0x740]
    // 0xaee2e0: StoreField: r2->field_1b = r0
    //     0xaee2e0: stur            w0, [x2, #0x1b]
    // 0xaee2e4: r0 = Instance_VerticalDirection
    //     0xaee2e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaee2e8: ldr             x0, [x0, #0x748]
    // 0xaee2ec: StoreField: r2->field_23 = r0
    //     0xaee2ec: stur            w0, [x2, #0x23]
    // 0xaee2f0: r0 = Instance_Clip
    //     0xaee2f0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaee2f4: ldr             x0, [x0, #0x750]
    // 0xaee2f8: StoreField: r2->field_2b = r0
    //     0xaee2f8: stur            w0, [x2, #0x2b]
    // 0xaee2fc: StoreField: r2->field_2f = rZR
    //     0xaee2fc: stur            xzr, [x2, #0x2f]
    // 0xaee300: ldur            x0, [fp, #-0x20]
    // 0xaee304: StoreField: r2->field_b = r0
    //     0xaee304: stur            w0, [x2, #0xb]
    // 0xaee308: ldur            x0, [fp, #-0x18]
    // 0xaee30c: LoadField: r1 = r0->field_b
    //     0xaee30c: ldur            w1, [x0, #0xb]
    // 0xaee310: r0 = LoadInt32Instr(r1)
    //     0xaee310: sbfx            x0, x1, #1, #0x1f
    // 0xaee314: ldur            x3, [fp, #-0x30]
    // 0xaee318: cmp             x3, x0
    // 0xaee31c: b.ne            #0xaee328
    // 0xaee320: ldur            x1, [fp, #-0x10]
    // 0xaee324: r0 = _growToNextCapacity()
    //     0xaee324: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee328: ldur            x3, [fp, #-0x10]
    // 0xaee32c: ldur            x2, [fp, #-0x30]
    // 0xaee330: add             x4, x2, #1
    // 0xaee334: stur            x4, [fp, #-0x28]
    // 0xaee338: lsl             x0, x4, #1
    // 0xaee33c: StoreField: r3->field_b = r0
    //     0xaee33c: stur            w0, [x3, #0xb]
    // 0xaee340: LoadField: r5 = r3->field_f
    //     0xaee340: ldur            w5, [x3, #0xf]
    // 0xaee344: DecompressPointer r5
    //     0xaee344: add             x5, x5, HEAP, lsl #32
    // 0xaee348: mov             x1, x5
    // 0xaee34c: ldur            x0, [fp, #-0x38]
    // 0xaee350: stur            x5, [fp, #-0x18]
    // 0xaee354: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaee354: add             x25, x1, x2, lsl #2
    //     0xaee358: add             x25, x25, #0xf
    //     0xaee35c: str             w0, [x25]
    //     0xaee360: tbz             w0, #0, #0xaee37c
    //     0xaee364: ldurb           w16, [x1, #-1]
    //     0xaee368: ldurb           w17, [x0, #-1]
    //     0xaee36c: and             x16, x17, x16, lsr #2
    //     0xaee370: tst             x16, HEAP, lsr #32
    //     0xaee374: b.eq            #0xaee37c
    //     0xaee378: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee37c: r0 = Obx()
    //     0xaee37c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaee380: ldur            x2, [fp, #-8]
    // 0xaee384: r1 = Function '<anonymous closure>':.
    //     0xaee384: add             x1, PP, #0x35, lsl #12  ; [pp+0x35160] AnonymousClosure: (0xaee440), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaee388: ldr             x1, [x1, #0x160]
    // 0xaee38c: stur            x0, [fp, #-8]
    // 0xaee390: r0 = AllocateClosure()
    //     0xaee390: bl              #0xec1630  ; AllocateClosureStub
    // 0xaee394: mov             x1, x0
    // 0xaee398: ldur            x0, [fp, #-8]
    // 0xaee39c: StoreField: r0->field_b = r1
    //     0xaee39c: stur            w1, [x0, #0xb]
    // 0xaee3a0: ldur            x1, [fp, #-0x18]
    // 0xaee3a4: LoadField: r2 = r1->field_b
    //     0xaee3a4: ldur            w2, [x1, #0xb]
    // 0xaee3a8: r1 = LoadInt32Instr(r2)
    //     0xaee3a8: sbfx            x1, x2, #1, #0x1f
    // 0xaee3ac: ldur            x2, [fp, #-0x28]
    // 0xaee3b0: cmp             x2, x1
    // 0xaee3b4: b.ne            #0xaee3c0
    // 0xaee3b8: ldur            x1, [fp, #-0x10]
    // 0xaee3bc: r0 = _growToNextCapacity()
    //     0xaee3bc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee3c0: ldur            x3, [fp, #-0x10]
    // 0xaee3c4: ldur            x2, [fp, #-0x28]
    // 0xaee3c8: add             x0, x2, #1
    // 0xaee3cc: lsl             x1, x0, #1
    // 0xaee3d0: StoreField: r3->field_b = r1
    //     0xaee3d0: stur            w1, [x3, #0xb]
    // 0xaee3d4: LoadField: r1 = r3->field_f
    //     0xaee3d4: ldur            w1, [x3, #0xf]
    // 0xaee3d8: DecompressPointer r1
    //     0xaee3d8: add             x1, x1, HEAP, lsl #32
    // 0xaee3dc: ldur            x0, [fp, #-8]
    // 0xaee3e0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaee3e0: add             x25, x1, x2, lsl #2
    //     0xaee3e4: add             x25, x25, #0xf
    //     0xaee3e8: str             w0, [x25]
    //     0xaee3ec: tbz             w0, #0, #0xaee408
    //     0xaee3f0: ldurb           w16, [x1, #-1]
    //     0xaee3f4: ldurb           w17, [x0, #-1]
    //     0xaee3f8: and             x16, x17, x16, lsr #2
    //     0xaee3fc: tst             x16, HEAP, lsr #32
    //     0xaee400: b.eq            #0xaee408
    //     0xaee404: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee408: r0 = NSection()
    //     0xaee408: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xaee40c: r1 = "Zakat ke NU Care-LAZISNU"
    //     0xaee40c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35168] "Zakat ke NU Care-LAZISNU"
    //     0xaee410: ldr             x1, [x1, #0x168]
    // 0xaee414: StoreField: r0->field_b = r1
    //     0xaee414: stur            w1, [x0, #0xb]
    // 0xaee418: ldur            x1, [fp, #-0x10]
    // 0xaee41c: StoreField: r0->field_f = r1
    //     0xaee41c: stur            w1, [x0, #0xf]
    // 0xaee420: r1 = false
    //     0xaee420: add             x1, NULL, #0x30  ; false
    // 0xaee424: StoreField: r0->field_27 = r1
    //     0xaee424: stur            w1, [x0, #0x27]
    // 0xaee428: StoreField: r0->field_2b = r1
    //     0xaee428: stur            w1, [x0, #0x2b]
    // 0xaee42c: LeaveFrame
    //     0xaee42c: mov             SP, fp
    //     0xaee430: ldp             fp, lr, [SP], #0x10
    // 0xaee434: ret
    //     0xaee434: ret             
    // 0xaee438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaee438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaee43c: b               #0xaedd44
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xaee440, size: 0x448
    // 0xaee440: EnterFrame
    //     0xaee440: stp             fp, lr, [SP, #-0x10]!
    //     0xaee444: mov             fp, SP
    // 0xaee448: AllocStack(0x60)
    //     0xaee448: sub             SP, SP, #0x60
    // 0xaee44c: SetupParameters()
    //     0xaee44c: ldr             x0, [fp, #0x10]
    //     0xaee450: ldur            w2, [x0, #0x17]
    //     0xaee454: add             x2, x2, HEAP, lsl #32
    //     0xaee458: stur            x2, [fp, #-8]
    // 0xaee45c: CheckStackOverflow
    //     0xaee45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaee460: cmp             SP, x16
    //     0xaee464: b.ls            #0xaee880
    // 0xaee468: LoadField: r1 = r2->field_f
    //     0xaee468: ldur            w1, [x2, #0xf]
    // 0xaee46c: DecompressPointer r1
    //     0xaee46c: add             x1, x1, HEAP, lsl #32
    // 0xaee470: r0 = controller()
    //     0xaee470: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaee474: LoadField: r1 = r0->field_2f
    //     0xaee474: ldur            w1, [x0, #0x2f]
    // 0xaee478: DecompressPointer r1
    //     0xaee478: add             x1, x1, HEAP, lsl #32
    // 0xaee47c: r0 = value()
    //     0xaee47c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaee480: r1 = <Widget>
    //     0xaee480: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaee484: r2 = 0
    //     0xaee484: movz            x2, #0
    // 0xaee488: stur            x0, [fp, #-0x10]
    // 0xaee48c: r0 = _GrowableList()
    //     0xaee48c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaee490: r1 = <Widget>
    //     0xaee490: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaee494: r2 = 0
    //     0xaee494: movz            x2, #0
    // 0xaee498: stur            x0, [fp, #-0x18]
    // 0xaee49c: r0 = _GrowableList()
    //     0xaee49c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaee4a0: ldur            x2, [fp, #-8]
    // 0xaee4a4: stur            x0, [fp, #-0x20]
    // 0xaee4a8: LoadField: r1 = r2->field_f
    //     0xaee4a8: ldur            w1, [x2, #0xf]
    // 0xaee4ac: DecompressPointer r1
    //     0xaee4ac: add             x1, x1, HEAP, lsl #32
    // 0xaee4b0: r0 = controller()
    //     0xaee4b0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaee4b4: LoadField: r1 = r0->field_57
    //     0xaee4b4: ldur            w1, [x0, #0x57]
    // 0xaee4b8: DecompressPointer r1
    //     0xaee4b8: add             x1, x1, HEAP, lsl #32
    // 0xaee4bc: r0 = value()
    //     0xaee4bc: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaee4c0: r1 = LoadClassIdInstr(r0)
    //     0xaee4c0: ldur            x1, [x0, #-1]
    //     0xaee4c4: ubfx            x1, x1, #0xc, #0x14
    // 0xaee4c8: str             x0, [SP]
    // 0xaee4cc: mov             x0, x1
    // 0xaee4d0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaee4d0: movz            x17, #0xc834
    //     0xaee4d4: add             lr, x0, x17
    //     0xaee4d8: ldr             lr, [x21, lr, lsl #3]
    //     0xaee4dc: blr             lr
    // 0xaee4e0: r1 = LoadInt32Instr(r0)
    //     0xaee4e0: sbfx            x1, x0, #1, #0x1f
    //     0xaee4e4: tbz             w0, #0, #0xaee4ec
    //     0xaee4e8: ldur            x1, [x0, #7]
    // 0xaee4ec: cmp             x1, #0xa
    // 0xaee4f0: b.ge            #0xaee678
    // 0xaee4f4: ldur            x2, [fp, #-8]
    // 0xaee4f8: ldur            x0, [fp, #-0x20]
    // 0xaee4fc: LoadField: r1 = r2->field_f
    //     0xaee4fc: ldur            w1, [x2, #0xf]
    // 0xaee500: DecompressPointer r1
    //     0xaee500: add             x1, x1, HEAP, lsl #32
    // 0xaee504: r0 = controller()
    //     0xaee504: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaee508: stur            x0, [fp, #-0x28]
    // 0xaee50c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaee50c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaee510: ldr             x0, [x0, #0x2670]
    //     0xaee514: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaee518: cmp             w0, w16
    //     0xaee51c: b.ne            #0xaee528
    //     0xaee520: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaee524: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaee528: r0 = GetNavigation.theme()
    //     0xaee528: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaee52c: LoadField: r1 = r0->field_3f
    //     0xaee52c: ldur            w1, [x0, #0x3f]
    // 0xaee530: DecompressPointer r1
    //     0xaee530: add             x1, x1, HEAP, lsl #32
    // 0xaee534: LoadField: r0 = r1->field_2b
    //     0xaee534: ldur            w0, [x1, #0x2b]
    // 0xaee538: DecompressPointer r0
    //     0xaee538: add             x0, x0, HEAP, lsl #32
    // 0xaee53c: stur            x0, [fp, #-0x30]
    // 0xaee540: r0 = Icon()
    //     0xaee540: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xaee544: mov             x1, x0
    // 0xaee548: r0 = Instance_IconData
    //     0xaee548: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b60] Obj!IconData@e101b1
    //     0xaee54c: ldr             x0, [x0, #0xb60]
    // 0xaee550: stur            x1, [fp, #-0x38]
    // 0xaee554: StoreField: r1->field_b = r0
    //     0xaee554: stur            w0, [x1, #0xb]
    // 0xaee558: r0 = 20.000000
    //     0xaee558: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xaee55c: ldr             x0, [x0, #0x430]
    // 0xaee560: StoreField: r1->field_f = r0
    //     0xaee560: stur            w0, [x1, #0xf]
    // 0xaee564: ldur            x0, [fp, #-0x30]
    // 0xaee568: StoreField: r1->field_23 = r0
    //     0xaee568: stur            w0, [x1, #0x23]
    // 0xaee56c: r0 = GetNavigation.textTheme()
    //     0xaee56c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaee570: LoadField: r1 = r0->field_2f
    //     0xaee570: ldur            w1, [x0, #0x2f]
    // 0xaee574: DecompressPointer r1
    //     0xaee574: add             x1, x1, HEAP, lsl #32
    // 0xaee578: r16 = Instance_BorderSide
    //     0xaee578: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ca0] Obj!BorderSide@e1c111
    //     0xaee57c: ldr             x16, [x16, #0xca0]
    // 0xaee580: stp             x1, x16, [SP, #8]
    // 0xaee584: r16 = Instance__NoSplashFactory
    //     0xaee584: add             x16, PP, #0x35, lsl #12  ; [pp+0x35170] Obj!_NoSplashFactory@e14771
    //     0xaee588: ldr             x16, [x16, #0x170]
    // 0xaee58c: str             x16, [SP]
    // 0xaee590: r4 = const [0, 0x3, 0x3, 0, side, 0, splashFactory, 0x2, textStyle, 0x1, null]
    //     0xaee590: add             x4, PP, #0x35, lsl #12  ; [pp+0x35178] List(11) [0, 0x3, 0x3, 0, "side", 0, "splashFactory", 0x2, "textStyle", 0x1, Null]
    //     0xaee594: ldr             x4, [x4, #0x178]
    // 0xaee598: r0 = styleFrom()
    //     0xaee598: bl              #0xaee9e8  ; [package:flutter/src/material/outlined_button.dart] OutlinedButton::styleFrom
    // 0xaee59c: ldur            x2, [fp, #-0x28]
    // 0xaee5a0: r1 = Function 'addPassenger':.
    //     0xaee5a0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35180] AnonymousClosure: (0xaef930), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::addPassenger (0xaef968)
    //     0xaee5a4: ldr             x1, [x1, #0x180]
    // 0xaee5a8: stur            x0, [fp, #-0x28]
    // 0xaee5ac: r0 = AllocateClosure()
    //     0xaee5ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xaee5b0: stur            x0, [fp, #-0x30]
    // 0xaee5b4: r0 = _OutlinedButtonWithIcon()
    //     0xaee5b4: bl              #0xaee9dc  ; Allocate_OutlinedButtonWithIconStub -> _OutlinedButtonWithIcon (size=0x3c)
    // 0xaee5b8: mov             x1, x0
    // 0xaee5bc: ldur            x2, [fp, #-0x38]
    // 0xaee5c0: ldur            x5, [fp, #-0x30]
    // 0xaee5c4: ldur            x6, [fp, #-0x28]
    // 0xaee5c8: r3 = Instance_Text
    //     0xaee5c8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35188] Obj!Text@e21d21
    //     0xaee5cc: ldr             x3, [x3, #0x188]
    // 0xaee5d0: stur            x0, [fp, #-0x28]
    // 0xaee5d4: r0 = _OutlinedButtonWithIcon()
    //     0xaee5d4: bl              #0xaee8e8  ; [package:flutter/src/material/outlined_button.dart] _OutlinedButtonWithIcon::_OutlinedButtonWithIcon
    // 0xaee5d8: r0 = Center()
    //     0xaee5d8: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaee5dc: mov             x2, x0
    // 0xaee5e0: r0 = Instance_Alignment
    //     0xaee5e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xaee5e4: ldr             x0, [x0, #0x898]
    // 0xaee5e8: stur            x2, [fp, #-0x30]
    // 0xaee5ec: StoreField: r2->field_f = r0
    //     0xaee5ec: stur            w0, [x2, #0xf]
    // 0xaee5f0: ldur            x1, [fp, #-0x28]
    // 0xaee5f4: StoreField: r2->field_b = r1
    //     0xaee5f4: stur            w1, [x2, #0xb]
    // 0xaee5f8: ldur            x3, [fp, #-0x20]
    // 0xaee5fc: LoadField: r1 = r3->field_b
    //     0xaee5fc: ldur            w1, [x3, #0xb]
    // 0xaee600: LoadField: r4 = r3->field_f
    //     0xaee600: ldur            w4, [x3, #0xf]
    // 0xaee604: DecompressPointer r4
    //     0xaee604: add             x4, x4, HEAP, lsl #32
    // 0xaee608: LoadField: r5 = r4->field_b
    //     0xaee608: ldur            w5, [x4, #0xb]
    // 0xaee60c: r4 = LoadInt32Instr(r1)
    //     0xaee60c: sbfx            x4, x1, #1, #0x1f
    // 0xaee610: stur            x4, [fp, #-0x40]
    // 0xaee614: r1 = LoadInt32Instr(r5)
    //     0xaee614: sbfx            x1, x5, #1, #0x1f
    // 0xaee618: cmp             x4, x1
    // 0xaee61c: b.ne            #0xaee628
    // 0xaee620: mov             x1, x3
    // 0xaee624: r0 = _growToNextCapacity()
    //     0xaee624: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee628: ldur            x2, [fp, #-0x20]
    // 0xaee62c: ldur            x3, [fp, #-0x40]
    // 0xaee630: add             x0, x3, #1
    // 0xaee634: lsl             x1, x0, #1
    // 0xaee638: StoreField: r2->field_b = r1
    //     0xaee638: stur            w1, [x2, #0xb]
    // 0xaee63c: LoadField: r1 = r2->field_f
    //     0xaee63c: ldur            w1, [x2, #0xf]
    // 0xaee640: DecompressPointer r1
    //     0xaee640: add             x1, x1, HEAP, lsl #32
    // 0xaee644: ldur            x0, [fp, #-0x30]
    // 0xaee648: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaee648: add             x25, x1, x3, lsl #2
    //     0xaee64c: add             x25, x25, #0xf
    //     0xaee650: str             w0, [x25]
    //     0xaee654: tbz             w0, #0, #0xaee670
    //     0xaee658: ldurb           w16, [x1, #-1]
    //     0xaee65c: ldurb           w17, [x0, #-1]
    //     0xaee660: and             x16, x17, x16, lsr #2
    //     0xaee664: tst             x16, HEAP, lsr #32
    //     0xaee668: b.eq            #0xaee670
    //     0xaee66c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee670: mov             x0, x2
    // 0xaee674: b               #0xaee6d4
    // 0xaee678: ldur            x2, [fp, #-0x20]
    // 0xaee67c: LoadField: r0 = r2->field_b
    //     0xaee67c: ldur            w0, [x2, #0xb]
    // 0xaee680: LoadField: r1 = r2->field_f
    //     0xaee680: ldur            w1, [x2, #0xf]
    // 0xaee684: DecompressPointer r1
    //     0xaee684: add             x1, x1, HEAP, lsl #32
    // 0xaee688: LoadField: r3 = r1->field_b
    //     0xaee688: ldur            w3, [x1, #0xb]
    // 0xaee68c: r4 = LoadInt32Instr(r0)
    //     0xaee68c: sbfx            x4, x0, #1, #0x1f
    // 0xaee690: stur            x4, [fp, #-0x40]
    // 0xaee694: r0 = LoadInt32Instr(r3)
    //     0xaee694: sbfx            x0, x3, #1, #0x1f
    // 0xaee698: cmp             x4, x0
    // 0xaee69c: b.ne            #0xaee6a8
    // 0xaee6a0: mov             x1, x2
    // 0xaee6a4: r0 = _growToNextCapacity()
    //     0xaee6a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee6a8: ldur            x0, [fp, #-0x20]
    // 0xaee6ac: ldur            x1, [fp, #-0x40]
    // 0xaee6b0: add             x2, x1, #1
    // 0xaee6b4: lsl             x3, x2, #1
    // 0xaee6b8: StoreField: r0->field_b = r3
    //     0xaee6b8: stur            w3, [x0, #0xb]
    // 0xaee6bc: LoadField: r2 = r0->field_f
    //     0xaee6bc: ldur            w2, [x0, #0xf]
    // 0xaee6c0: DecompressPointer r2
    //     0xaee6c0: add             x2, x2, HEAP, lsl #32
    // 0xaee6c4: add             x3, x2, x1, lsl #2
    // 0xaee6c8: r16 = Instance_SizedBox
    //     0xaee6c8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaee6cc: ldr             x16, [x16, #0x950]
    // 0xaee6d0: StoreField: r3->field_f = r16
    //     0xaee6d0: stur            w16, [x3, #0xf]
    // 0xaee6d4: r1 = _ConstMap len:3
    //     0xaee6d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaee6d8: ldr             x1, [x1, #0xbe8]
    // 0xaee6dc: r2 = 2
    //     0xaee6dc: movz            x2, #0x2
    // 0xaee6e0: r0 = []()
    //     0xaee6e0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaee6e4: r16 = <Color?>
    //     0xaee6e4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaee6e8: ldr             x16, [x16, #0x98]
    // 0xaee6ec: stp             x0, x16, [SP, #8]
    // 0xaee6f0: r16 = Instance_MaterialColor
    //     0xaee6f0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xaee6f4: ldr             x16, [x16, #0xe38]
    // 0xaee6f8: str             x16, [SP]
    // 0xaee6fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaee6fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaee700: r0 = mode()
    //     0xaee700: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaee704: stur            x0, [fp, #-0x28]
    // 0xaee708: r0 = Obx()
    //     0xaee708: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaee70c: ldur            x2, [fp, #-8]
    // 0xaee710: r1 = Function '<anonymous closure>':.
    //     0xaee710: add             x1, PP, #0x35, lsl #12  ; [pp+0x35190] AnonymousClosure: (0xaef62c), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaee714: ldr             x1, [x1, #0x190]
    // 0xaee718: stur            x0, [fp, #-8]
    // 0xaee71c: r0 = AllocateClosure()
    //     0xaee71c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaee720: mov             x1, x0
    // 0xaee724: ldur            x0, [fp, #-8]
    // 0xaee728: StoreField: r0->field_b = r1
    //     0xaee728: stur            w1, [x0, #0xb]
    // 0xaee72c: r0 = Center()
    //     0xaee72c: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaee730: mov             x1, x0
    // 0xaee734: r0 = Instance_Alignment
    //     0xaee734: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xaee738: ldr             x0, [x0, #0x898]
    // 0xaee73c: stur            x1, [fp, #-0x30]
    // 0xaee740: StoreField: r1->field_f = r0
    //     0xaee740: stur            w0, [x1, #0xf]
    // 0xaee744: ldur            x0, [fp, #-8]
    // 0xaee748: StoreField: r1->field_b = r0
    //     0xaee748: stur            w0, [x1, #0xb]
    // 0xaee74c: r0 = Container()
    //     0xaee74c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaee750: stur            x0, [fp, #-8]
    // 0xaee754: r16 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xaee754: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xaee758: ldr             x16, [x16, #0xc58]
    // 0xaee75c: ldur            lr, [fp, #-0x28]
    // 0xaee760: stp             lr, x16, [SP, #0x10]
    // 0xaee764: r16 = Instance_EdgeInsets
    //     0xaee764: add             x16, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xaee768: ldr             x16, [x16, #0x768]
    // 0xaee76c: ldur            lr, [fp, #-0x30]
    // 0xaee770: stp             lr, x16, [SP]
    // 0xaee774: mov             x1, x0
    // 0xaee778: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x2, padding, 0x3, width, 0x1, null]
    //     0xaee778: add             x4, PP, #0x32, lsl #12  ; [pp+0x32f50] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xaee77c: ldr             x4, [x4, #0xf50]
    // 0xaee780: r0 = Container()
    //     0xaee780: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaee784: ldur            x0, [fp, #-0x20]
    // 0xaee788: LoadField: r1 = r0->field_b
    //     0xaee788: ldur            w1, [x0, #0xb]
    // 0xaee78c: LoadField: r2 = r0->field_f
    //     0xaee78c: ldur            w2, [x0, #0xf]
    // 0xaee790: DecompressPointer r2
    //     0xaee790: add             x2, x2, HEAP, lsl #32
    // 0xaee794: LoadField: r3 = r2->field_b
    //     0xaee794: ldur            w3, [x2, #0xb]
    // 0xaee798: r2 = LoadInt32Instr(r1)
    //     0xaee798: sbfx            x2, x1, #1, #0x1f
    // 0xaee79c: stur            x2, [fp, #-0x40]
    // 0xaee7a0: r1 = LoadInt32Instr(r3)
    //     0xaee7a0: sbfx            x1, x3, #1, #0x1f
    // 0xaee7a4: cmp             x2, x1
    // 0xaee7a8: b.ne            #0xaee7b4
    // 0xaee7ac: mov             x1, x0
    // 0xaee7b0: r0 = _growToNextCapacity()
    //     0xaee7b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaee7b4: ldur            x4, [fp, #-0x10]
    // 0xaee7b8: ldur            x2, [fp, #-0x20]
    // 0xaee7bc: ldur            x3, [fp, #-0x40]
    // 0xaee7c0: add             x0, x3, #1
    // 0xaee7c4: lsl             x1, x0, #1
    // 0xaee7c8: StoreField: r2->field_b = r1
    //     0xaee7c8: stur            w1, [x2, #0xb]
    // 0xaee7cc: LoadField: r1 = r2->field_f
    //     0xaee7cc: ldur            w1, [x2, #0xf]
    // 0xaee7d0: DecompressPointer r1
    //     0xaee7d0: add             x1, x1, HEAP, lsl #32
    // 0xaee7d4: ldur            x0, [fp, #-8]
    // 0xaee7d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaee7d8: add             x25, x1, x3, lsl #2
    //     0xaee7dc: add             x25, x25, #0xf
    //     0xaee7e0: str             w0, [x25]
    //     0xaee7e4: tbz             w0, #0, #0xaee800
    //     0xaee7e8: ldurb           w16, [x1, #-1]
    //     0xaee7ec: ldurb           w17, [x0, #-1]
    //     0xaee7f0: and             x16, x17, x16, lsr #2
    //     0xaee7f4: tst             x16, HEAP, lsr #32
    //     0xaee7f8: b.eq            #0xaee800
    //     0xaee7fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaee800: r16 = Instance_ZakatTypes
    //     0xaee800: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xaee804: ldr             x16, [x16, #0x620]
    // 0xaee808: cmp             w4, w16
    // 0xaee80c: b.ne            #0xaee818
    // 0xaee810: mov             x0, x2
    // 0xaee814: b               #0xaee81c
    // 0xaee818: ldur            x0, [fp, #-0x18]
    // 0xaee81c: stur            x0, [fp, #-8]
    // 0xaee820: r0 = Column()
    //     0xaee820: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaee824: r1 = Instance_Axis
    //     0xaee824: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaee828: StoreField: r0->field_f = r1
    //     0xaee828: stur            w1, [x0, #0xf]
    // 0xaee82c: r1 = Instance_MainAxisAlignment
    //     0xaee82c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaee830: ldr             x1, [x1, #0x730]
    // 0xaee834: StoreField: r0->field_13 = r1
    //     0xaee834: stur            w1, [x0, #0x13]
    // 0xaee838: r1 = Instance_MainAxisSize
    //     0xaee838: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaee83c: ldr             x1, [x1, #0x738]
    // 0xaee840: ArrayStore: r0[0] = r1  ; List_4
    //     0xaee840: stur            w1, [x0, #0x17]
    // 0xaee844: r1 = Instance_CrossAxisAlignment
    //     0xaee844: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaee848: ldr             x1, [x1, #0x740]
    // 0xaee84c: StoreField: r0->field_1b = r1
    //     0xaee84c: stur            w1, [x0, #0x1b]
    // 0xaee850: r1 = Instance_VerticalDirection
    //     0xaee850: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaee854: ldr             x1, [x1, #0x748]
    // 0xaee858: StoreField: r0->field_23 = r1
    //     0xaee858: stur            w1, [x0, #0x23]
    // 0xaee85c: r1 = Instance_Clip
    //     0xaee85c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaee860: ldr             x1, [x1, #0x750]
    // 0xaee864: StoreField: r0->field_2b = r1
    //     0xaee864: stur            w1, [x0, #0x2b]
    // 0xaee868: StoreField: r0->field_2f = rZR
    //     0xaee868: stur            xzr, [x0, #0x2f]
    // 0xaee86c: ldur            x1, [fp, #-8]
    // 0xaee870: StoreField: r0->field_b = r1
    //     0xaee870: stur            w1, [x0, #0xb]
    // 0xaee874: LeaveFrame
    //     0xaee874: mov             SP, fp
    //     0xaee878: ldp             fp, lr, [SP], #0x10
    // 0xaee87c: ret
    //     0xaee87c: ret             
    // 0xaee880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaee880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaee884: b               #0xaee468
  }
  [closure] Text <anonymous closure>(dynamic) {
    // ** addr: 0xaef62c, size: 0x16c
    // 0xaef62c: EnterFrame
    //     0xaef62c: stp             fp, lr, [SP, #-0x10]!
    //     0xaef630: mov             fp, SP
    // 0xaef634: AllocStack(0x20)
    //     0xaef634: sub             SP, SP, #0x20
    // 0xaef638: SetupParameters()
    //     0xaef638: ldr             x0, [fp, #0x10]
    //     0xaef63c: ldur            w2, [x0, #0x17]
    //     0xaef640: add             x2, x2, HEAP, lsl #32
    //     0xaef644: stur            x2, [fp, #-8]
    // 0xaef648: CheckStackOverflow
    //     0xaef648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef64c: cmp             SP, x16
    //     0xaef650: b.ls            #0xaef78c
    // 0xaef654: LoadField: r1 = r2->field_f
    //     0xaef654: ldur            w1, [x2, #0xf]
    // 0xaef658: DecompressPointer r1
    //     0xaef658: add             x1, x1, HEAP, lsl #32
    // 0xaef65c: r0 = controller()
    //     0xaef65c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaef660: mov             x1, x0
    // 0xaef664: r0 = subtotalCalculation()
    //     0xaef664: bl              #0xaef860  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::subtotalCalculation
    // 0xaef668: mov             x2, x0
    // 0xaef66c: ldur            x0, [fp, #-8]
    // 0xaef670: stur            x2, [fp, #-0x10]
    // 0xaef674: LoadField: r1 = r0->field_f
    //     0xaef674: ldur            w1, [x0, #0xf]
    // 0xaef678: DecompressPointer r1
    //     0xaef678: add             x1, x1, HEAP, lsl #32
    // 0xaef67c: r0 = controller()
    //     0xaef67c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaef680: mov             x1, x0
    // 0xaef684: r0 = subtotal()
    //     0xaef684: bl              #0xaef798  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::subtotal
    // 0xaef688: mov             x1, x0
    // 0xaef68c: r0 = IntExtension.idr()
    //     0xaef68c: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xaef690: stur            x0, [fp, #-8]
    // 0xaef694: r0 = TextSpan()
    //     0xaef694: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaef698: mov             x3, x0
    // 0xaef69c: ldur            x0, [fp, #-8]
    // 0xaef6a0: stur            x3, [fp, #-0x18]
    // 0xaef6a4: StoreField: r3->field_b = r0
    //     0xaef6a4: stur            w0, [x3, #0xb]
    // 0xaef6a8: r0 = Instance__DeferringMouseCursor
    //     0xaef6a8: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaef6ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xaef6ac: stur            w0, [x3, #0x17]
    // 0xaef6b0: r1 = Instance_TextStyle
    //     0xaef6b0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34be0] Obj!TextStyle@e1ae71
    //     0xaef6b4: ldr             x1, [x1, #0xbe0]
    // 0xaef6b8: StoreField: r3->field_7 = r1
    //     0xaef6b8: stur            w1, [x3, #7]
    // 0xaef6bc: r1 = Null
    //     0xaef6bc: mov             x1, NULL
    // 0xaef6c0: r2 = 2
    //     0xaef6c0: movz            x2, #0x2
    // 0xaef6c4: r0 = AllocateArray()
    //     0xaef6c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaef6c8: mov             x2, x0
    // 0xaef6cc: ldur            x0, [fp, #-0x18]
    // 0xaef6d0: stur            x2, [fp, #-8]
    // 0xaef6d4: StoreField: r2->field_f = r0
    //     0xaef6d4: stur            w0, [x2, #0xf]
    // 0xaef6d8: r1 = <InlineSpan>
    //     0xaef6d8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xaef6dc: ldr             x1, [x1, #0x5f0]
    // 0xaef6e0: r0 = AllocateGrowableArray()
    //     0xaef6e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaef6e4: mov             x1, x0
    // 0xaef6e8: ldur            x0, [fp, #-8]
    // 0xaef6ec: stur            x1, [fp, #-0x18]
    // 0xaef6f0: StoreField: r1->field_f = r0
    //     0xaef6f0: stur            w0, [x1, #0xf]
    // 0xaef6f4: r0 = 2
    //     0xaef6f4: movz            x0, #0x2
    // 0xaef6f8: StoreField: r1->field_b = r0
    //     0xaef6f8: stur            w0, [x1, #0xb]
    // 0xaef6fc: r0 = TextSpan()
    //     0xaef6fc: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaef700: mov             x1, x0
    // 0xaef704: ldur            x0, [fp, #-0x10]
    // 0xaef708: stur            x1, [fp, #-8]
    // 0xaef70c: StoreField: r1->field_b = r0
    //     0xaef70c: stur            w0, [x1, #0xb]
    // 0xaef710: ldur            x0, [fp, #-0x18]
    // 0xaef714: StoreField: r1->field_f = r0
    //     0xaef714: stur            w0, [x1, #0xf]
    // 0xaef718: r0 = Instance__DeferringMouseCursor
    //     0xaef718: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaef71c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaef71c: stur            w0, [x1, #0x17]
    // 0xaef720: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaef720: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaef724: ldr             x0, [x0, #0x2670]
    //     0xaef728: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaef72c: cmp             w0, w16
    //     0xaef730: b.ne            #0xaef73c
    //     0xaef734: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaef738: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaef73c: r0 = GetNavigation.textTheme()
    //     0xaef73c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaef740: LoadField: r1 = r0->field_f
    //     0xaef740: ldur            w1, [x0, #0xf]
    // 0xaef744: DecompressPointer r1
    //     0xaef744: add             x1, x1, HEAP, lsl #32
    // 0xaef748: cmp             w1, NULL
    // 0xaef74c: b.eq            #0xaef794
    // 0xaef750: r16 = Instance_FontWeight
    //     0xaef750: add             x16, PP, #0x23, lsl #12  ; [pp+0x23c50] Obj!FontWeight@e26571
    //     0xaef754: ldr             x16, [x16, #0xc50]
    // 0xaef758: str             x16, [SP]
    // 0xaef75c: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xaef75c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xaef760: ldr             x4, [x4, #0xfe0]
    // 0xaef764: r0 = copyWith()
    //     0xaef764: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaef768: stur            x0, [fp, #-0x10]
    // 0xaef76c: r0 = Text()
    //     0xaef76c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaef770: ldur            x1, [fp, #-8]
    // 0xaef774: StoreField: r0->field_f = r1
    //     0xaef774: stur            w1, [x0, #0xf]
    // 0xaef778: ldur            x1, [fp, #-0x10]
    // 0xaef77c: StoreField: r0->field_13 = r1
    //     0xaef77c: stur            w1, [x0, #0x13]
    // 0xaef780: LeaveFrame
    //     0xaef780: mov             SP, fp
    //     0xaef784: ldp             fp, lr, [SP], #0x10
    // 0xaef788: ret
    //     0xaef788: ret             
    // 0xaef78c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaef78c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaef790: b               #0xaef654
    // 0xaef794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaef794: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Checkbox <anonymous closure>(dynamic) {
    // ** addr: 0xaef9ac, size: 0x118
    // 0xaef9ac: EnterFrame
    //     0xaef9ac: stp             fp, lr, [SP, #-0x10]!
    //     0xaef9b0: mov             fp, SP
    // 0xaef9b4: AllocStack(0x30)
    //     0xaef9b4: sub             SP, SP, #0x30
    // 0xaef9b8: SetupParameters()
    //     0xaef9b8: ldr             x0, [fp, #0x10]
    //     0xaef9bc: ldur            w2, [x0, #0x17]
    //     0xaef9c0: add             x2, x2, HEAP, lsl #32
    //     0xaef9c4: stur            x2, [fp, #-8]
    // 0xaef9c8: CheckStackOverflow
    //     0xaef9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaef9cc: cmp             SP, x16
    //     0xaef9d0: b.ls            #0xaefabc
    // 0xaef9d4: LoadField: r1 = r2->field_f
    //     0xaef9d4: ldur            w1, [x2, #0xf]
    // 0xaef9d8: DecompressPointer r1
    //     0xaef9d8: add             x1, x1, HEAP, lsl #32
    // 0xaef9dc: r0 = controller()
    //     0xaef9dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaef9e0: LoadField: r1 = r0->field_2b
    //     0xaef9e0: ldur            w1, [x0, #0x2b]
    // 0xaef9e4: DecompressPointer r1
    //     0xaef9e4: add             x1, x1, HEAP, lsl #32
    // 0xaef9e8: r0 = value()
    //     0xaef9e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaef9ec: mov             x2, x0
    // 0xaef9f0: ldur            x0, [fp, #-8]
    // 0xaef9f4: stur            x2, [fp, #-0x10]
    // 0xaef9f8: LoadField: r1 = r0->field_f
    //     0xaef9f8: ldur            w1, [x0, #0xf]
    // 0xaef9fc: DecompressPointer r1
    //     0xaef9fc: add             x1, x1, HEAP, lsl #32
    // 0xaefa00: r0 = controller()
    //     0xaefa00: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaefa04: LoadField: r2 = r0->field_2b
    //     0xaefa04: ldur            w2, [x0, #0x2b]
    // 0xaefa08: DecompressPointer r2
    //     0xaefa08: add             x2, x2, HEAP, lsl #32
    // 0xaefa0c: LoadField: r3 = r2->field_7
    //     0xaefa0c: ldur            w3, [x2, #7]
    // 0xaefa10: DecompressPointer r3
    //     0xaefa10: add             x3, x3, HEAP, lsl #32
    // 0xaefa14: r1 = Function 'call':.
    //     0xaefa14: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaefa18: ldr             x1, [x1, #0x310]
    // 0xaefa1c: r0 = AllocateClosureTA()
    //     0xaefa1c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaefa20: mov             x3, x0
    // 0xaefa24: r2 = Null
    //     0xaefa24: mov             x2, NULL
    // 0xaefa28: r1 = Null
    //     0xaefa28: mov             x1, NULL
    // 0xaefa2c: stur            x3, [fp, #-8]
    // 0xaefa30: r8 = (dynamic this, bool?) => bool
    //     0xaefa30: add             x8, PP, #0x28, lsl #12  ; [pp+0x28318] FunctionType: (dynamic this, bool?) => bool
    //     0xaefa34: ldr             x8, [x8, #0x318]
    // 0xaefa38: r3 = Null
    //     0xaefa38: add             x3, PP, #0x35, lsl #12  ; [pp+0x351a0] Null
    //     0xaefa3c: ldr             x3, [x3, #0x1a0]
    // 0xaefa40: r0 = DefaultTypeTest()
    //     0xaefa40: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaefa44: r16 = <Color?>
    //     0xaefa44: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaefa48: ldr             x16, [x16, #0x98]
    // 0xaefa4c: r30 = Instance_MaterialColor
    //     0xaefa4c: add             lr, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xaefa50: ldr             lr, [lr, #0xbf0]
    // 0xaefa54: stp             lr, x16, [SP, #8]
    // 0xaefa58: r16 = Instance_Color
    //     0xaefa58: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaefa5c: str             x16, [SP]
    // 0xaefa60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaefa60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaefa64: r0 = mode()
    //     0xaefa64: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaefa68: stur            x0, [fp, #-0x18]
    // 0xaefa6c: r0 = Checkbox()
    //     0xaefa6c: bl              #0xa9b0e8  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xaefa70: ldur            x1, [fp, #-0x10]
    // 0xaefa74: StoreField: r0->field_b = r1
    //     0xaefa74: stur            w1, [x0, #0xb]
    // 0xaefa78: r1 = false
    //     0xaefa78: add             x1, NULL, #0x30  ; false
    // 0xaefa7c: StoreField: r0->field_23 = r1
    //     0xaefa7c: stur            w1, [x0, #0x23]
    // 0xaefa80: ldur            x2, [fp, #-8]
    // 0xaefa84: StoreField: r0->field_f = r2
    //     0xaefa84: stur            w2, [x0, #0xf]
    // 0xaefa88: ldur            x2, [fp, #-0x18]
    // 0xaefa8c: StoreField: r0->field_1f = r2
    //     0xaefa8c: stur            w2, [x0, #0x1f]
    // 0xaefa90: r2 = Instance_VisualDensity
    //     0xaefa90: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b68] Obj!VisualDensity@e1c311
    //     0xaefa94: ldr             x2, [x2, #0xb68]
    // 0xaefa98: StoreField: r0->field_2b = r2
    //     0xaefa98: stur            w2, [x0, #0x2b]
    // 0xaefa9c: StoreField: r0->field_43 = r1
    //     0xaefa9c: stur            w1, [x0, #0x43]
    // 0xaefaa0: StoreField: r0->field_4f = r1
    //     0xaefaa0: stur            w1, [x0, #0x4f]
    // 0xaefaa4: r1 = Instance__CheckboxType
    //     0xaefaa4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34b70] Obj!_CheckboxType@e36a21
    //     0xaefaa8: ldr             x1, [x1, #0xb70]
    // 0xaefaac: StoreField: r0->field_57 = r1
    //     0xaefaac: stur            w1, [x0, #0x57]
    // 0xaefab0: LeaveFrame
    //     0xaefab0: mov             SP, fp
    //     0xaefab4: ldp             fp, lr, [SP], #0x10
    // 0xaefab8: ret
    //     0xaefab8: ret             
    // 0xaefabc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefabc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefac0: b               #0xaef9d4
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xaefac4, size: 0x130
    // 0xaefac4: EnterFrame
    //     0xaefac4: stp             fp, lr, [SP, #-0x10]!
    //     0xaefac8: mov             fp, SP
    // 0xaefacc: AllocStack(0x40)
    //     0xaefacc: sub             SP, SP, #0x40
    // 0xaefad0: SetupParameters()
    //     0xaefad0: ldr             x0, [fp, #0x10]
    //     0xaefad4: ldur            w2, [x0, #0x17]
    //     0xaefad8: add             x2, x2, HEAP, lsl #32
    //     0xaefadc: stur            x2, [fp, #-8]
    // 0xaefae0: CheckStackOverflow
    //     0xaefae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefae4: cmp             SP, x16
    //     0xaefae8: b.ls            #0xaefbec
    // 0xaefaec: LoadField: r1 = r2->field_f
    //     0xaefaec: ldur            w1, [x2, #0xf]
    // 0xaefaf0: DecompressPointer r1
    //     0xaefaf0: add             x1, x1, HEAP, lsl #32
    // 0xaefaf4: r0 = controller()
    //     0xaefaf4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaefaf8: LoadField: r1 = r0->field_2f
    //     0xaefaf8: ldur            w1, [x0, #0x2f]
    // 0xaefafc: DecompressPointer r1
    //     0xaefafc: add             x1, x1, HEAP, lsl #32
    // 0xaefb00: r0 = value()
    //     0xaefb00: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaefb04: ldur            x2, [fp, #-8]
    // 0xaefb08: stur            x0, [fp, #-0x10]
    // 0xaefb0c: LoadField: r1 = r2->field_f
    //     0xaefb0c: ldur            w1, [x2, #0xf]
    // 0xaefb10: DecompressPointer r1
    //     0xaefb10: add             x1, x1, HEAP, lsl #32
    // 0xaefb14: r0 = controller()
    //     0xaefb14: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaefb18: LoadField: r1 = r0->field_57
    //     0xaefb18: ldur            w1, [x0, #0x57]
    // 0xaefb1c: DecompressPointer r1
    //     0xaefb1c: add             x1, x1, HEAP, lsl #32
    // 0xaefb20: r0 = value()
    //     0xaefb20: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaefb24: r1 = LoadClassIdInstr(r0)
    //     0xaefb24: ldur            x1, [x0, #-1]
    //     0xaefb28: ubfx            x1, x1, #0xc, #0x14
    // 0xaefb2c: str             x0, [SP]
    // 0xaefb30: mov             x0, x1
    // 0xaefb34: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaefb34: movz            x17, #0xc834
    //     0xaefb38: add             lr, x0, x17
    //     0xaefb3c: ldr             lr, [x21, lr, lsl #3]
    //     0xaefb40: blr             lr
    // 0xaefb44: r3 = LoadInt32Instr(r0)
    //     0xaefb44: sbfx            x3, x0, #1, #0x1f
    //     0xaefb48: tbz             w0, #0, #0xaefb50
    //     0xaefb4c: ldur            x3, [x0, #7]
    // 0xaefb50: stur            x3, [fp, #-0x18]
    // 0xaefb54: r1 = Function '<anonymous closure>':.
    //     0xaefb54: add             x1, PP, #0x35, lsl #12  ; [pp+0x351b0] AnonymousClosure: (0xaebfd4), in [package:nuonline/app/modules/donation/widgets/qurban_form.dart] QurbanFormWidget::build (0xaeca2c)
    //     0xaefb58: ldr             x1, [x1, #0x1b0]
    // 0xaefb5c: r2 = Null
    //     0xaefb5c: mov             x2, NULL
    // 0xaefb60: r0 = AllocateClosure()
    //     0xaefb60: bl              #0xec1630  ; AllocateClosureStub
    // 0xaefb64: ldur            x2, [fp, #-8]
    // 0xaefb68: r1 = Function '<anonymous closure>':.
    //     0xaefb68: add             x1, PP, #0x35, lsl #12  ; [pp+0x351b8] AnonymousClosure: (0xaefbf4), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaefb6c: ldr             x1, [x1, #0x1b8]
    // 0xaefb70: stur            x0, [fp, #-8]
    // 0xaefb74: r0 = AllocateClosure()
    //     0xaefb74: bl              #0xec1630  ; AllocateClosureStub
    // 0xaefb78: stur            x0, [fp, #-0x20]
    // 0xaefb7c: r0 = ListView()
    //     0xaefb7c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaefb80: stur            x0, [fp, #-0x28]
    // 0xaefb84: r16 = true
    //     0xaefb84: add             x16, NULL, #0x20  ; true
    // 0xaefb88: r30 = Instance_NeverScrollableScrollPhysics
    //     0xaefb88: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xaefb8c: ldr             lr, [lr, #0x290]
    // 0xaefb90: stp             lr, x16, [SP, #8]
    // 0xaefb94: r16 = Instance_EdgeInsets
    //     0xaefb94: add             x16, PP, #0x28, lsl #12  ; [pp+0x283e8] Obj!EdgeInsets@e12851
    //     0xaefb98: ldr             x16, [x16, #0x3e8]
    // 0xaefb9c: str             x16, [SP]
    // 0xaefba0: mov             x1, x0
    // 0xaefba4: ldur            x2, [fp, #-0x20]
    // 0xaefba8: ldur            x3, [fp, #-0x18]
    // 0xaefbac: ldur            x5, [fp, #-8]
    // 0xaefbb0: r4 = const [0, 0x7, 0x3, 0x4, padding, 0x6, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xaefbb0: add             x4, PP, #0x34, lsl #12  ; [pp+0x34b88] List(11) [0, 0x7, 0x3, 0x4, "padding", 0x6, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xaefbb4: ldr             x4, [x4, #0xb88]
    // 0xaefbb8: r0 = ListView.separated()
    //     0xaefbb8: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaefbbc: ldur            x1, [fp, #-0x10]
    // 0xaefbc0: r16 = Instance_ZakatTypes
    //     0xaefbc0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xaefbc4: ldr             x16, [x16, #0x620]
    // 0xaefbc8: cmp             w1, w16
    // 0xaefbcc: b.ne            #0xaefbd8
    // 0xaefbd0: ldur            x0, [fp, #-0x28]
    // 0xaefbd4: b               #0xaefbe0
    // 0xaefbd8: r0 = Instance_SizedBox
    //     0xaefbd8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xaefbdc: ldr             x0, [x0, #0xc40]
    // 0xaefbe0: LeaveFrame
    //     0xaefbe0: mov             SP, fp
    //     0xaefbe4: ldp             fp, lr, [SP], #0x10
    // 0xaefbe8: ret
    //     0xaefbe8: ret             
    // 0xaefbec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefbec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefbf0: b               #0xaefaec
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaefbf4, size: 0x194
    // 0xaefbf4: EnterFrame
    //     0xaefbf4: stp             fp, lr, [SP, #-0x10]!
    //     0xaefbf8: mov             fp, SP
    // 0xaefbfc: AllocStack(0x18)
    //     0xaefbfc: sub             SP, SP, #0x18
    // 0xaefc00: SetupParameters()
    //     0xaefc00: ldr             x0, [fp, #0x20]
    //     0xaefc04: ldur            w1, [x0, #0x17]
    //     0xaefc08: add             x1, x1, HEAP, lsl #32
    //     0xaefc0c: stur            x1, [fp, #-8]
    // 0xaefc10: r1 = 1
    //     0xaefc10: movz            x1, #0x1
    // 0xaefc14: r0 = AllocateContext()
    //     0xaefc14: bl              #0xec126c  ; AllocateContextStub
    // 0xaefc18: mov             x1, x0
    // 0xaefc1c: ldur            x0, [fp, #-8]
    // 0xaefc20: stur            x1, [fp, #-0x10]
    // 0xaefc24: StoreField: r1->field_b = r0
    //     0xaefc24: stur            w0, [x1, #0xb]
    // 0xaefc28: ldr             x0, [fp, #0x10]
    // 0xaefc2c: StoreField: r1->field_f = r0
    //     0xaefc2c: stur            w0, [x1, #0xf]
    // 0xaefc30: r0 = Obx()
    //     0xaefc30: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaefc34: ldur            x2, [fp, #-0x10]
    // 0xaefc38: r1 = Function '<anonymous closure>':.
    //     0xaefc38: add             x1, PP, #0x35, lsl #12  ; [pp+0x351c0] AnonymousClosure: (0xaefe3c), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaefc3c: ldr             x1, [x1, #0x1c0]
    // 0xaefc40: stur            x0, [fp, #-8]
    // 0xaefc44: r0 = AllocateClosure()
    //     0xaefc44: bl              #0xec1630  ; AllocateClosureStub
    // 0xaefc48: mov             x1, x0
    // 0xaefc4c: ldur            x0, [fp, #-8]
    // 0xaefc50: StoreField: r0->field_b = r1
    //     0xaefc50: stur            w1, [x0, #0xb]
    // 0xaefc54: r1 = <FlexParentData>
    //     0xaefc54: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaefc58: ldr             x1, [x1, #0x720]
    // 0xaefc5c: r0 = Expanded()
    //     0xaefc5c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaefc60: mov             x1, x0
    // 0xaefc64: r0 = 1
    //     0xaefc64: movz            x0, #0x1
    // 0xaefc68: stur            x1, [fp, #-0x18]
    // 0xaefc6c: StoreField: r1->field_13 = r0
    //     0xaefc6c: stur            x0, [x1, #0x13]
    // 0xaefc70: r0 = Instance_FlexFit
    //     0xaefc70: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaefc74: ldr             x0, [x0, #0x728]
    // 0xaefc78: StoreField: r1->field_1b = r0
    //     0xaefc78: stur            w0, [x1, #0x1b]
    // 0xaefc7c: ldur            x0, [fp, #-8]
    // 0xaefc80: StoreField: r1->field_b = r0
    //     0xaefc80: stur            w0, [x1, #0xb]
    // 0xaefc84: r0 = IconButton()
    //     0xaefc84: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xaefc88: mov             x3, x0
    // 0xaefc8c: r0 = 20.000000
    //     0xaefc8c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xaefc90: ldr             x0, [x0, #0x430]
    // 0xaefc94: stur            x3, [fp, #-8]
    // 0xaefc98: StoreField: r3->field_b = r0
    //     0xaefc98: stur            w0, [x3, #0xb]
    // 0xaefc9c: r0 = Instance_VisualDensity
    //     0xaefc9c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34b98] Obj!VisualDensity@e1c331
    //     0xaefca0: ldr             x0, [x0, #0xb98]
    // 0xaefca4: StoreField: r3->field_f = r0
    //     0xaefca4: stur            w0, [x3, #0xf]
    // 0xaefca8: ldur            x2, [fp, #-0x10]
    // 0xaefcac: r1 = Function '<anonymous closure>':.
    //     0xaefcac: add             x1, PP, #0x35, lsl #12  ; [pp+0x351c8] AnonymousClosure: (0xaefd88), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaefcb0: ldr             x1, [x1, #0x1c8]
    // 0xaefcb4: r0 = AllocateClosure()
    //     0xaefcb4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaefcb8: mov             x1, x0
    // 0xaefcbc: ldur            x0, [fp, #-8]
    // 0xaefcc0: StoreField: r0->field_3b = r1
    //     0xaefcc0: stur            w1, [x0, #0x3b]
    // 0xaefcc4: r1 = false
    //     0xaefcc4: add             x1, NULL, #0x30  ; false
    // 0xaefcc8: StoreField: r0->field_47 = r1
    //     0xaefcc8: stur            w1, [x0, #0x47]
    // 0xaefccc: r1 = Instance_Icon
    //     0xaefccc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ba8] Obj!Icon@e245b1
    //     0xaefcd0: ldr             x1, [x1, #0xba8]
    // 0xaefcd4: StoreField: r0->field_1f = r1
    //     0xaefcd4: stur            w1, [x0, #0x1f]
    // 0xaefcd8: r1 = Instance__IconButtonVariant
    //     0xaefcd8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xaefcdc: ldr             x1, [x1, #0xf78]
    // 0xaefce0: StoreField: r0->field_63 = r1
    //     0xaefce0: stur            w1, [x0, #0x63]
    // 0xaefce4: r1 = Null
    //     0xaefce4: mov             x1, NULL
    // 0xaefce8: r2 = 4
    //     0xaefce8: movz            x2, #0x4
    // 0xaefcec: r0 = AllocateArray()
    //     0xaefcec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaefcf0: mov             x2, x0
    // 0xaefcf4: ldur            x0, [fp, #-0x18]
    // 0xaefcf8: stur            x2, [fp, #-0x10]
    // 0xaefcfc: StoreField: r2->field_f = r0
    //     0xaefcfc: stur            w0, [x2, #0xf]
    // 0xaefd00: ldur            x0, [fp, #-8]
    // 0xaefd04: StoreField: r2->field_13 = r0
    //     0xaefd04: stur            w0, [x2, #0x13]
    // 0xaefd08: r1 = <Widget>
    //     0xaefd08: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaefd0c: r0 = AllocateGrowableArray()
    //     0xaefd0c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaefd10: mov             x1, x0
    // 0xaefd14: ldur            x0, [fp, #-0x10]
    // 0xaefd18: stur            x1, [fp, #-8]
    // 0xaefd1c: StoreField: r1->field_f = r0
    //     0xaefd1c: stur            w0, [x1, #0xf]
    // 0xaefd20: r0 = 4
    //     0xaefd20: movz            x0, #0x4
    // 0xaefd24: StoreField: r1->field_b = r0
    //     0xaefd24: stur            w0, [x1, #0xb]
    // 0xaefd28: r0 = Row()
    //     0xaefd28: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaefd2c: r1 = Instance_Axis
    //     0xaefd2c: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaefd30: StoreField: r0->field_f = r1
    //     0xaefd30: stur            w1, [x0, #0xf]
    // 0xaefd34: r1 = Instance_MainAxisAlignment
    //     0xaefd34: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaefd38: ldr             x1, [x1, #0x730]
    // 0xaefd3c: StoreField: r0->field_13 = r1
    //     0xaefd3c: stur            w1, [x0, #0x13]
    // 0xaefd40: r1 = Instance_MainAxisSize
    //     0xaefd40: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaefd44: ldr             x1, [x1, #0x738]
    // 0xaefd48: ArrayStore: r0[0] = r1  ; List_4
    //     0xaefd48: stur            w1, [x0, #0x17]
    // 0xaefd4c: r1 = Instance_CrossAxisAlignment
    //     0xaefd4c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaefd50: ldr             x1, [x1, #0x740]
    // 0xaefd54: StoreField: r0->field_1b = r1
    //     0xaefd54: stur            w1, [x0, #0x1b]
    // 0xaefd58: r1 = Instance_VerticalDirection
    //     0xaefd58: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaefd5c: ldr             x1, [x1, #0x748]
    // 0xaefd60: StoreField: r0->field_23 = r1
    //     0xaefd60: stur            w1, [x0, #0x23]
    // 0xaefd64: r1 = Instance_Clip
    //     0xaefd64: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaefd68: ldr             x1, [x1, #0x750]
    // 0xaefd6c: StoreField: r0->field_2b = r1
    //     0xaefd6c: stur            w1, [x0, #0x2b]
    // 0xaefd70: StoreField: r0->field_2f = rZR
    //     0xaefd70: stur            xzr, [x0, #0x2f]
    // 0xaefd74: ldur            x1, [fp, #-8]
    // 0xaefd78: StoreField: r0->field_b = r1
    //     0xaefd78: stur            w1, [x0, #0xb]
    // 0xaefd7c: LeaveFrame
    //     0xaefd7c: mov             SP, fp
    //     0xaefd80: ldp             fp, lr, [SP], #0x10
    // 0xaefd84: ret
    //     0xaefd84: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaefd88, size: 0x78
    // 0xaefd88: EnterFrame
    //     0xaefd88: stp             fp, lr, [SP, #-0x10]!
    //     0xaefd8c: mov             fp, SP
    // 0xaefd90: AllocStack(0x8)
    //     0xaefd90: sub             SP, SP, #8
    // 0xaefd94: SetupParameters()
    //     0xaefd94: ldr             x0, [fp, #0x10]
    //     0xaefd98: ldur            w2, [x0, #0x17]
    //     0xaefd9c: add             x2, x2, HEAP, lsl #32
    //     0xaefda0: stur            x2, [fp, #-8]
    // 0xaefda4: CheckStackOverflow
    //     0xaefda4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefda8: cmp             SP, x16
    //     0xaefdac: b.ls            #0xaefdf8
    // 0xaefdb0: LoadField: r0 = r2->field_b
    //     0xaefdb0: ldur            w0, [x2, #0xb]
    // 0xaefdb4: DecompressPointer r0
    //     0xaefdb4: add             x0, x0, HEAP, lsl #32
    // 0xaefdb8: LoadField: r1 = r0->field_f
    //     0xaefdb8: ldur            w1, [x0, #0xf]
    // 0xaefdbc: DecompressPointer r1
    //     0xaefdbc: add             x1, x1, HEAP, lsl #32
    // 0xaefdc0: r0 = controller()
    //     0xaefdc0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaefdc4: mov             x1, x0
    // 0xaefdc8: ldur            x0, [fp, #-8]
    // 0xaefdcc: LoadField: r2 = r0->field_f
    //     0xaefdcc: ldur            w2, [x0, #0xf]
    // 0xaefdd0: DecompressPointer r2
    //     0xaefdd0: add             x2, x2, HEAP, lsl #32
    // 0xaefdd4: r0 = LoadInt32Instr(r2)
    //     0xaefdd4: sbfx            x0, x2, #1, #0x1f
    //     0xaefdd8: tbz             w2, #0, #0xaefde0
    //     0xaefddc: ldur            x0, [x2, #7]
    // 0xaefde0: mov             x2, x0
    // 0xaefde4: r0 = removePassenger()
    //     0xaefde4: bl              #0xaefe00  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::removePassenger
    // 0xaefde8: r0 = Null
    //     0xaefde8: mov             x0, NULL
    // 0xaefdec: LeaveFrame
    //     0xaefdec: mov             SP, fp
    //     0xaefdf0: ldp             fp, lr, [SP], #0x10
    // 0xaefdf4: ret
    //     0xaefdf4: ret             
    // 0xaefdf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaefdf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaefdfc: b               #0xaefdb0
  }
  [closure] TextFormField <anonymous closure>(dynamic) {
    // ** addr: 0xaefe3c, size: 0x2c8
    // 0xaefe3c: EnterFrame
    //     0xaefe3c: stp             fp, lr, [SP, #-0x10]!
    //     0xaefe40: mov             fp, SP
    // 0xaefe44: AllocStack(0x60)
    //     0xaefe44: sub             SP, SP, #0x60
    // 0xaefe48: SetupParameters()
    //     0xaefe48: ldr             x0, [fp, #0x10]
    //     0xaefe4c: ldur            w2, [x0, #0x17]
    //     0xaefe50: add             x2, x2, HEAP, lsl #32
    //     0xaefe54: stur            x2, [fp, #-0x10]
    // 0xaefe58: CheckStackOverflow
    //     0xaefe58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaefe5c: cmp             SP, x16
    //     0xaefe60: b.ls            #0xaf00fc
    // 0xaefe64: LoadField: r0 = r2->field_b
    //     0xaefe64: ldur            w0, [x2, #0xb]
    // 0xaefe68: DecompressPointer r0
    //     0xaefe68: add             x0, x0, HEAP, lsl #32
    // 0xaefe6c: stur            x0, [fp, #-8]
    // 0xaefe70: LoadField: r1 = r0->field_f
    //     0xaefe70: ldur            w1, [x0, #0xf]
    // 0xaefe74: DecompressPointer r1
    //     0xaefe74: add             x1, x1, HEAP, lsl #32
    // 0xaefe78: r0 = controller()
    //     0xaefe78: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaefe7c: LoadField: r1 = r0->field_57
    //     0xaefe7c: ldur            w1, [x0, #0x57]
    // 0xaefe80: DecompressPointer r1
    //     0xaefe80: add             x1, x1, HEAP, lsl #32
    // 0xaefe84: r0 = value()
    //     0xaefe84: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaefe88: r1 = LoadClassIdInstr(r0)
    //     0xaefe88: ldur            x1, [x0, #-1]
    //     0xaefe8c: ubfx            x1, x1, #0xc, #0x14
    // 0xaefe90: str             x0, [SP]
    // 0xaefe94: mov             x0, x1
    // 0xaefe98: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaefe98: movz            x17, #0xc834
    //     0xaefe9c: add             lr, x0, x17
    //     0xaefea0: ldr             lr, [x21, lr, lsl #3]
    //     0xaefea4: blr             lr
    // 0xaefea8: ldur            x2, [fp, #-0x10]
    // 0xaefeac: LoadField: r1 = r2->field_f
    //     0xaefeac: ldur            w1, [x2, #0xf]
    // 0xaefeb0: DecompressPointer r1
    //     0xaefeb0: add             x1, x1, HEAP, lsl #32
    // 0xaefeb4: r3 = LoadInt32Instr(r0)
    //     0xaefeb4: sbfx            x3, x0, #1, #0x1f
    //     0xaefeb8: tbz             w0, #0, #0xaefec0
    //     0xaefebc: ldur            x3, [x0, #7]
    // 0xaefec0: r0 = LoadInt32Instr(r1)
    //     0xaefec0: sbfx            x0, x1, #1, #0x1f
    //     0xaefec4: tbz             w1, #0, #0xaefecc
    //     0xaefec8: ldur            x0, [x1, #7]
    // 0xaefecc: add             x4, x3, x0
    // 0xaefed0: r0 = BoxInt64Instr(r4)
    //     0xaefed0: sbfiz           x0, x4, #1, #0x1f
    //     0xaefed4: cmp             x4, x0, asr #1
    //     0xaefed8: b.eq            #0xaefee4
    //     0xaefedc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaefee0: stur            x4, [x0, #7]
    // 0xaefee4: r1 = <int>
    //     0xaefee4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaefee8: stur            x0, [fp, #-0x18]
    // 0xaefeec: r0 = ValueKey()
    //     0xaefeec: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xaefef0: mov             x2, x0
    // 0xaefef4: ldur            x0, [fp, #-0x18]
    // 0xaefef8: stur            x2, [fp, #-0x20]
    // 0xaefefc: StoreField: r2->field_b = r0
    //     0xaefefc: stur            w0, [x2, #0xb]
    // 0xaeff00: ldur            x0, [fp, #-8]
    // 0xaeff04: LoadField: r1 = r0->field_f
    //     0xaeff04: ldur            w1, [x0, #0xf]
    // 0xaeff08: DecompressPointer r1
    //     0xaeff08: add             x1, x1, HEAP, lsl #32
    // 0xaeff0c: r0 = controller()
    //     0xaeff0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaeff10: LoadField: r1 = r0->field_57
    //     0xaeff10: ldur            w1, [x0, #0x57]
    // 0xaeff14: DecompressPointer r1
    //     0xaeff14: add             x1, x1, HEAP, lsl #32
    // 0xaeff18: ldur            x2, [fp, #-0x10]
    // 0xaeff1c: LoadField: r0 = r2->field_f
    //     0xaeff1c: ldur            w0, [x2, #0xf]
    // 0xaeff20: DecompressPointer r0
    //     0xaeff20: add             x0, x0, HEAP, lsl #32
    // 0xaeff24: stur            x0, [fp, #-8]
    // 0xaeff28: r0 = value()
    //     0xaeff28: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaeff2c: r1 = LoadClassIdInstr(r0)
    //     0xaeff2c: ldur            x1, [x0, #-1]
    //     0xaeff30: ubfx            x1, x1, #0xc, #0x14
    // 0xaeff34: ldur            x16, [fp, #-8]
    // 0xaeff38: stp             x16, x0, [SP]
    // 0xaeff3c: mov             x0, x1
    // 0xaeff40: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaeff40: movz            x17, #0x3037
    //     0xaeff44: movk            x17, #0x1, lsl #16
    //     0xaeff48: add             lr, x0, x17
    //     0xaeff4c: ldr             lr, [x21, lr, lsl #3]
    //     0xaeff50: blr             lr
    // 0xaeff54: r1 = Function '<anonymous closure>': static.
    //     0xaeff54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaeff58: ldr             x1, [x1, #0xda0]
    // 0xaeff5c: r2 = Null
    //     0xaeff5c: mov             x2, NULL
    // 0xaeff60: stur            x0, [fp, #-8]
    // 0xaeff64: r0 = AllocateClosure()
    //     0xaeff64: bl              #0xec1630  ; AllocateClosureStub
    // 0xaeff68: r1 = <String>
    //     0xaeff68: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaeff6c: stur            x0, [fp, #-0x18]
    // 0xaeff70: StoreField: r0->field_b = r1
    //     0xaeff70: stur            w1, [x0, #0xb]
    // 0xaeff74: r16 = <String>
    //     0xaeff74: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaeff78: str             x16, [SP, #8]
    // 0xaeff7c: r2 = 18
    //     0xaeff7c: movz            x2, #0x12
    // 0xaeff80: str             x2, [SP]
    // 0xaeff84: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaeff84: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaeff88: r0 = maxLength()
    //     0xaeff88: bl              #0xae4b84  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::maxLength
    // 0xaeff8c: r1 = Null
    //     0xaeff8c: mov             x1, NULL
    // 0xaeff90: r2 = 4
    //     0xaeff90: movz            x2, #0x4
    // 0xaeff94: stur            x0, [fp, #-0x28]
    // 0xaeff98: r0 = AllocateArray()
    //     0xaeff98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaeff9c: mov             x2, x0
    // 0xaeffa0: ldur            x0, [fp, #-0x18]
    // 0xaeffa4: stur            x2, [fp, #-0x30]
    // 0xaeffa8: StoreField: r2->field_f = r0
    //     0xaeffa8: stur            w0, [x2, #0xf]
    // 0xaeffac: ldur            x0, [fp, #-0x28]
    // 0xaeffb0: StoreField: r2->field_13 = r0
    //     0xaeffb0: stur            w0, [x2, #0x13]
    // 0xaeffb4: r1 = <(dynamic this, String?) => String?>
    //     0xaeffb4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xaeffb8: ldr             x1, [x1, #0xd98]
    // 0xaeffbc: r0 = AllocateGrowableArray()
    //     0xaeffbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaeffc0: mov             x1, x0
    // 0xaeffc4: ldur            x0, [fp, #-0x30]
    // 0xaeffc8: stur            x1, [fp, #-0x18]
    // 0xaeffcc: StoreField: r1->field_f = r0
    //     0xaeffcc: stur            w0, [x1, #0xf]
    // 0xaeffd0: r0 = 4
    //     0xaeffd0: movz            x0, #0x4
    // 0xaeffd4: StoreField: r1->field_b = r0
    //     0xaeffd4: stur            w0, [x1, #0xb]
    // 0xaeffd8: r1 = 1
    //     0xaeffd8: movz            x1, #0x1
    // 0xaeffdc: r0 = AllocateContext()
    //     0xaeffdc: bl              #0xec126c  ; AllocateContextStub
    // 0xaeffe0: mov             x1, x0
    // 0xaeffe4: ldur            x0, [fp, #-0x18]
    // 0xaeffe8: StoreField: r1->field_f = r0
    //     0xaeffe8: stur            w0, [x1, #0xf]
    // 0xaeffec: mov             x2, x1
    // 0xaefff0: r1 = Function '<anonymous closure>': static.
    //     0xaefff0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaefff4: ldr             x1, [x1, #0xda8]
    // 0xaefff8: r0 = AllocateClosure()
    //     0xaefff8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaefffc: r1 = <String>
    //     0xaefffc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaf0000: stur            x0, [fp, #-0x18]
    // 0xaf0004: StoreField: r0->field_b = r1
    //     0xaf0004: stur            w1, [x0, #0xb]
    // 0xaf0008: r16 = "[a-zA-Z ]"
    //     0xaf0008: add             x16, PP, #0x34, lsl #12  ; [pp+0x34b20] "[a-zA-Z ]"
    //     0xaf000c: ldr             x16, [x16, #0xb20]
    // 0xaf0010: stp             x16, NULL, [SP, #0x20]
    // 0xaf0014: r16 = false
    //     0xaf0014: add             x16, NULL, #0x30  ; false
    // 0xaf0018: r30 = true
    //     0xaf0018: add             lr, NULL, #0x20  ; true
    // 0xaf001c: stp             lr, x16, [SP, #0x10]
    // 0xaf0020: r16 = false
    //     0xaf0020: add             x16, NULL, #0x30  ; false
    // 0xaf0024: r30 = false
    //     0xaf0024: add             lr, NULL, #0x30  ; false
    // 0xaf0028: stp             lr, x16, [SP]
    // 0xaf002c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xaf002c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xaf0030: r0 = _RegExp()
    //     0xaf0030: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xaf0034: stur            x0, [fp, #-0x28]
    // 0xaf0038: r0 = FilteringTextInputFormatter()
    //     0xaf0038: bl              #0xa0c738  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xaf003c: mov             x3, x0
    // 0xaf0040: ldur            x0, [fp, #-0x28]
    // 0xaf0044: stur            x3, [fp, #-0x30]
    // 0xaf0048: StoreField: r3->field_7 = r0
    //     0xaf0048: stur            w0, [x3, #7]
    // 0xaf004c: r0 = true
    //     0xaf004c: add             x0, NULL, #0x20  ; true
    // 0xaf0050: StoreField: r3->field_b = r0
    //     0xaf0050: stur            w0, [x3, #0xb]
    // 0xaf0054: r0 = ""
    //     0xaf0054: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf0058: StoreField: r3->field_f = r0
    //     0xaf0058: stur            w0, [x3, #0xf]
    // 0xaf005c: r1 = Null
    //     0xaf005c: mov             x1, NULL
    // 0xaf0060: r2 = 2
    //     0xaf0060: movz            x2, #0x2
    // 0xaf0064: r0 = AllocateArray()
    //     0xaf0064: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0068: mov             x2, x0
    // 0xaf006c: ldur            x0, [fp, #-0x30]
    // 0xaf0070: stur            x2, [fp, #-0x28]
    // 0xaf0074: StoreField: r2->field_f = r0
    //     0xaf0074: stur            w0, [x2, #0xf]
    // 0xaf0078: r1 = <TextInputFormatter>
    //     0xaf0078: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xaf007c: ldr             x1, [x1, #0x788]
    // 0xaf0080: r0 = AllocateGrowableArray()
    //     0xaf0080: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf0084: mov             x2, x0
    // 0xaf0088: ldur            x0, [fp, #-0x28]
    // 0xaf008c: stur            x2, [fp, #-0x30]
    // 0xaf0090: StoreField: r2->field_f = r0
    //     0xaf0090: stur            w0, [x2, #0xf]
    // 0xaf0094: r0 = 2
    //     0xaf0094: movz            x0, #0x2
    // 0xaf0098: StoreField: r2->field_b = r0
    //     0xaf0098: stur            w0, [x2, #0xb]
    // 0xaf009c: r1 = <String>
    //     0xaf009c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaf00a0: r0 = TextFormField()
    //     0xaf00a0: bl              #0xa40610  ; AllocateTextFormFieldStub -> TextFormField (size=0x34)
    // 0xaf00a4: ldur            x2, [fp, #-0x10]
    // 0xaf00a8: r1 = Function '<anonymous closure>':.
    //     0xaf00a8: add             x1, PP, #0x35, lsl #12  ; [pp+0x351d0] AnonymousClosure: (0xaf0104), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaf00ac: ldr             x1, [x1, #0x1d0]
    // 0xaf00b0: stur            x0, [fp, #-0x10]
    // 0xaf00b4: r0 = AllocateClosure()
    //     0xaf00b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf00b8: ldur            x16, [fp, #-0x20]
    // 0xaf00bc: ldur            lr, [fp, #-8]
    // 0xaf00c0: stp             lr, x16, [SP, #0x18]
    // 0xaf00c4: ldur            x16, [fp, #-0x18]
    // 0xaf00c8: stp             x16, x0, [SP, #8]
    // 0xaf00cc: ldur            x16, [fp, #-0x30]
    // 0xaf00d0: str             x16, [SP]
    // 0xaf00d4: ldur            x1, [fp, #-0x10]
    // 0xaf00d8: r2 = Instance_InputDecoration
    //     0xaf00d8: add             x2, PP, #0x34, lsl #12  ; [pp+0x34b28] Obj!InputDecoration@e14241
    //     0xaf00dc: ldr             x2, [x2, #0xb28]
    // 0xaf00e0: r4 = const [0, 0x7, 0x5, 0x2, initialValue, 0x3, inputFormatters, 0x6, key, 0x2, onChanged, 0x4, validator, 0x5, null]
    //     0xaf00e0: add             x4, PP, #0x34, lsl #12  ; [pp+0x34bb8] List(15) [0, 0x7, 0x5, 0x2, "initialValue", 0x3, "inputFormatters", 0x6, "key", 0x2, "onChanged", 0x4, "validator", 0x5, Null]
    //     0xaf00e4: ldr             x4, [x4, #0xbb8]
    // 0xaf00e8: r0 = TextFormField()
    //     0xaf00e8: bl              #0xa3d5e0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xaf00ec: ldur            x0, [fp, #-0x10]
    // 0xaf00f0: LeaveFrame
    //     0xaf00f0: mov             SP, fp
    //     0xaf00f4: ldp             fp, lr, [SP], #0x10
    // 0xaf00f8: ret
    //     0xaf00f8: ret             
    // 0xaf00fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf00fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0100: b               #0xaefe64
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xaf0104, size: 0x7c
    // 0xaf0104: EnterFrame
    //     0xaf0104: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0108: mov             fp, SP
    // 0xaf010c: AllocStack(0x8)
    //     0xaf010c: sub             SP, SP, #8
    // 0xaf0110: SetupParameters()
    //     0xaf0110: ldr             x0, [fp, #0x18]
    //     0xaf0114: ldur            w2, [x0, #0x17]
    //     0xaf0118: add             x2, x2, HEAP, lsl #32
    //     0xaf011c: stur            x2, [fp, #-8]
    // 0xaf0120: CheckStackOverflow
    //     0xaf0120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0124: cmp             SP, x16
    //     0xaf0128: b.ls            #0xaf0178
    // 0xaf012c: LoadField: r0 = r2->field_b
    //     0xaf012c: ldur            w0, [x2, #0xb]
    // 0xaf0130: DecompressPointer r0
    //     0xaf0130: add             x0, x0, HEAP, lsl #32
    // 0xaf0134: LoadField: r1 = r0->field_f
    //     0xaf0134: ldur            w1, [x0, #0xf]
    // 0xaf0138: DecompressPointer r1
    //     0xaf0138: add             x1, x1, HEAP, lsl #32
    // 0xaf013c: r0 = controller()
    //     0xaf013c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0140: mov             x1, x0
    // 0xaf0144: ldur            x0, [fp, #-8]
    // 0xaf0148: LoadField: r2 = r0->field_f
    //     0xaf0148: ldur            w2, [x0, #0xf]
    // 0xaf014c: DecompressPointer r2
    //     0xaf014c: add             x2, x2, HEAP, lsl #32
    // 0xaf0150: r0 = LoadInt32Instr(r2)
    //     0xaf0150: sbfx            x0, x2, #1, #0x1f
    //     0xaf0154: tbz             w2, #0, #0xaf015c
    //     0xaf0158: ldur            x0, [x2, #7]
    // 0xaf015c: mov             x2, x0
    // 0xaf0160: ldr             x3, [fp, #0x10]
    // 0xaf0164: r0 = updatePassenger()
    //     0xaf0164: bl              #0xaf0180  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::updatePassenger
    // 0xaf0168: r0 = Null
    //     0xaf0168: mov             x0, NULL
    // 0xaf016c: LeaveFrame
    //     0xaf016c: mov             SP, fp
    //     0xaf0170: ldp             fp, lr, [SP], #0x10
    // 0xaf0174: ret
    //     0xaf0174: ret             
    // 0xaf0178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0178: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf017c: b               #0xaf012c
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xaf01d8, size: 0x740
    // 0xaf01d8: EnterFrame
    //     0xaf01d8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf01dc: mov             fp, SP
    // 0xaf01e0: AllocStack(0x58)
    //     0xaf01e0: sub             SP, SP, #0x58
    // 0xaf01e4: SetupParameters()
    //     0xaf01e4: ldr             x0, [fp, #0x10]
    //     0xaf01e8: ldur            w2, [x0, #0x17]
    //     0xaf01ec: add             x2, x2, HEAP, lsl #32
    //     0xaf01f0: stur            x2, [fp, #-8]
    // 0xaf01f4: CheckStackOverflow
    //     0xaf01f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf01f8: cmp             SP, x16
    //     0xaf01fc: b.ls            #0xaf0910
    // 0xaf0200: LoadField: r1 = r2->field_f
    //     0xaf0200: ldur            w1, [x2, #0xf]
    // 0xaf0204: DecompressPointer r1
    //     0xaf0204: add             x1, x1, HEAP, lsl #32
    // 0xaf0208: r0 = controller()
    //     0xaf0208: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf020c: LoadField: r1 = r0->field_2f
    //     0xaf020c: ldur            w1, [x0, #0x2f]
    // 0xaf0210: DecompressPointer r1
    //     0xaf0210: add             x1, x1, HEAP, lsl #32
    // 0xaf0214: r0 = value()
    //     0xaf0214: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0218: ldur            x2, [fp, #-8]
    // 0xaf021c: stur            x0, [fp, #-0x10]
    // 0xaf0220: LoadField: r1 = r2->field_f
    //     0xaf0220: ldur            w1, [x2, #0xf]
    // 0xaf0224: DecompressPointer r1
    //     0xaf0224: add             x1, x1, HEAP, lsl #32
    // 0xaf0228: r0 = controller()
    //     0xaf0228: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf022c: LoadField: r1 = r0->field_4f
    //     0xaf022c: ldur            w1, [x0, #0x4f]
    // 0xaf0230: DecompressPointer r1
    //     0xaf0230: add             x1, x1, HEAP, lsl #32
    // 0xaf0234: r0 = value()
    //     0xaf0234: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf0238: ldur            x2, [fp, #-8]
    // 0xaf023c: r1 = Function '<anonymous closure>':.
    //     0xaf023c: add             x1, PP, #0x35, lsl #12  ; [pp+0x351d8] AnonymousClosure: (0xaf0e38), in [package:nuonline/app/modules/donation/widgets/zakat_form.dart] ZakatFormWidget::build (0xaedc98)
    //     0xaf0240: ldr             x1, [x1, #0x1d8]
    // 0xaf0244: stur            x0, [fp, #-0x18]
    // 0xaf0248: r0 = AllocateClosure()
    //     0xaf0248: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf024c: mov             x1, x0
    // 0xaf0250: ldur            x0, [fp, #-0x18]
    // 0xaf0254: r2 = LoadClassIdInstr(r0)
    //     0xaf0254: ldur            x2, [x0, #-1]
    //     0xaf0258: ubfx            x2, x2, #0xc, #0x14
    // 0xaf025c: r16 = <Widget>
    //     0xaf025c: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf0260: stp             x0, x16, [SP, #8]
    // 0xaf0264: str             x1, [SP]
    // 0xaf0268: mov             x0, x2
    // 0xaf026c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf026c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf0270: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xaf0270: movz            x17, #0xf28c
    //     0xaf0274: add             lr, x0, x17
    //     0xaf0278: ldr             lr, [x21, lr, lsl #3]
    //     0xaf027c: blr             lr
    // 0xaf0280: r1 = LoadClassIdInstr(r0)
    //     0xaf0280: ldur            x1, [x0, #-1]
    //     0xaf0284: ubfx            x1, x1, #0xc, #0x14
    // 0xaf0288: mov             x16, x0
    // 0xaf028c: mov             x0, x1
    // 0xaf0290: mov             x1, x16
    // 0xaf0294: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf0294: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf0298: r0 = GDT[cid_x0 + 0xd889]()
    //     0xaf0298: movz            x17, #0xd889
    //     0xaf029c: add             lr, x0, x17
    //     0xaf02a0: ldr             lr, [x21, lr, lsl #3]
    //     0xaf02a4: blr             lr
    // 0xaf02a8: mov             x2, x0
    // 0xaf02ac: ldur            x0, [fp, #-8]
    // 0xaf02b0: stur            x2, [fp, #-0x18]
    // 0xaf02b4: LoadField: r1 = r0->field_f
    //     0xaf02b4: ldur            w1, [x0, #0xf]
    // 0xaf02b8: DecompressPointer r1
    //     0xaf02b8: add             x1, x1, HEAP, lsl #32
    // 0xaf02bc: r0 = controller()
    //     0xaf02bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf02c0: LoadField: r1 = r0->field_4f
    //     0xaf02c0: ldur            w1, [x0, #0x4f]
    // 0xaf02c4: DecompressPointer r1
    //     0xaf02c4: add             x1, x1, HEAP, lsl #32
    // 0xaf02c8: r0 = value()
    //     0xaf02c8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf02cc: r1 = LoadClassIdInstr(r0)
    //     0xaf02cc: ldur            x1, [x0, #-1]
    //     0xaf02d0: ubfx            x1, x1, #0xc, #0x14
    // 0xaf02d4: str             x0, [SP]
    // 0xaf02d8: mov             x0, x1
    // 0xaf02dc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf02dc: movz            x17, #0xc834
    //     0xaf02e0: add             lr, x0, x17
    //     0xaf02e4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf02e8: blr             lr
    // 0xaf02ec: r1 = LoadInt32Instr(r0)
    //     0xaf02ec: sbfx            x1, x0, #1, #0x1f
    //     0xaf02f0: tbz             w0, #0, #0xaf02f8
    //     0xaf02f4: ldur            x1, [x0, #7]
    // 0xaf02f8: cmp             x1, #1
    // 0xaf02fc: r16 = true
    //     0xaf02fc: add             x16, NULL, #0x20  ; true
    // 0xaf0300: r17 = false
    //     0xaf0300: add             x17, NULL, #0x30  ; false
    // 0xaf0304: csel            x0, x16, x17, gt
    // 0xaf0308: r16 = <Widget>
    //     0xaf0308: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf030c: ldur            lr, [fp, #-0x18]
    // 0xaf0310: stp             lr, x16, [SP, #8]
    // 0xaf0314: str             x0, [SP]
    // 0xaf0318: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf0318: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf031c: r0 = ListExtension.insertIf()
    //     0xaf031c: bl              #0xaf09a8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.insertIf
    // 0xaf0320: r0 = Row()
    //     0xaf0320: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf0324: mov             x3, x0
    // 0xaf0328: r0 = Instance_Axis
    //     0xaf0328: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaf032c: stur            x3, [fp, #-0x20]
    // 0xaf0330: StoreField: r3->field_f = r0
    //     0xaf0330: stur            w0, [x3, #0xf]
    // 0xaf0334: r0 = Instance_MainAxisAlignment
    //     0xaf0334: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf0338: ldr             x0, [x0, #0x730]
    // 0xaf033c: StoreField: r3->field_13 = r0
    //     0xaf033c: stur            w0, [x3, #0x13]
    // 0xaf0340: r4 = Instance_MainAxisSize
    //     0xaf0340: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf0344: ldr             x4, [x4, #0x738]
    // 0xaf0348: ArrayStore: r3[0] = r4  ; List_4
    //     0xaf0348: stur            w4, [x3, #0x17]
    // 0xaf034c: r1 = Instance_CrossAxisAlignment
    //     0xaf034c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf0350: ldr             x1, [x1, #0x740]
    // 0xaf0354: StoreField: r3->field_1b = r1
    //     0xaf0354: stur            w1, [x3, #0x1b]
    // 0xaf0358: r5 = Instance_VerticalDirection
    //     0xaf0358: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf035c: ldr             x5, [x5, #0x748]
    // 0xaf0360: StoreField: r3->field_23 = r5
    //     0xaf0360: stur            w5, [x3, #0x23]
    // 0xaf0364: r6 = Instance_Clip
    //     0xaf0364: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf0368: ldr             x6, [x6, #0x750]
    // 0xaf036c: StoreField: r3->field_2b = r6
    //     0xaf036c: stur            w6, [x3, #0x2b]
    // 0xaf0370: StoreField: r3->field_2f = rZR
    //     0xaf0370: stur            xzr, [x3, #0x2f]
    // 0xaf0374: ldur            x1, [fp, #-0x18]
    // 0xaf0378: StoreField: r3->field_b = r1
    //     0xaf0378: stur            w1, [x3, #0xb]
    // 0xaf037c: r1 = Null
    //     0xaf037c: mov             x1, NULL
    // 0xaf0380: r2 = 6
    //     0xaf0380: movz            x2, #0x6
    // 0xaf0384: r0 = AllocateArray()
    //     0xaf0384: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0388: stur            x0, [fp, #-0x18]
    // 0xaf038c: r16 = Instance_NLabelTextField
    //     0xaf038c: add             x16, PP, #0x35, lsl #12  ; [pp+0x351e0] Obj!NLabelTextField@e1fc11
    //     0xaf0390: ldr             x16, [x16, #0x1e0]
    // 0xaf0394: StoreField: r0->field_f = r16
    //     0xaf0394: stur            w16, [x0, #0xf]
    // 0xaf0398: ldur            x1, [fp, #-0x20]
    // 0xaf039c: StoreField: r0->field_13 = r1
    //     0xaf039c: stur            w1, [x0, #0x13]
    // 0xaf03a0: r16 = Instance_SizedBox
    //     0xaf03a0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xaf03a4: ldr             x16, [x16, #0x540]
    // 0xaf03a8: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf03a8: stur            w16, [x0, #0x17]
    // 0xaf03ac: r1 = <Widget>
    //     0xaf03ac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf03b0: r0 = AllocateGrowableArray()
    //     0xaf03b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf03b4: mov             x2, x0
    // 0xaf03b8: ldur            x0, [fp, #-0x18]
    // 0xaf03bc: stur            x2, [fp, #-0x20]
    // 0xaf03c0: StoreField: r2->field_f = r0
    //     0xaf03c0: stur            w0, [x2, #0xf]
    // 0xaf03c4: r0 = 6
    //     0xaf03c4: movz            x0, #0x6
    // 0xaf03c8: StoreField: r2->field_b = r0
    //     0xaf03c8: stur            w0, [x2, #0xb]
    // 0xaf03cc: ldur            x0, [fp, #-8]
    // 0xaf03d0: LoadField: r1 = r0->field_f
    //     0xaf03d0: ldur            w1, [x0, #0xf]
    // 0xaf03d4: DecompressPointer r1
    //     0xaf03d4: add             x1, x1, HEAP, lsl #32
    // 0xaf03d8: r0 = controller()
    //     0xaf03d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf03dc: LoadField: r3 = r0->field_33
    //     0xaf03dc: ldur            w3, [x0, #0x33]
    // 0xaf03e0: DecompressPointer r3
    //     0xaf03e0: add             x3, x3, HEAP, lsl #32
    // 0xaf03e4: stur            x3, [fp, #-0x18]
    // 0xaf03e8: r1 = Function '<anonymous closure>': static.
    //     0xaf03e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda0] AnonymousClosure: static (0xae61d0), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaf03ec: ldr             x1, [x1, #0xda0]
    // 0xaf03f0: r2 = Null
    //     0xaf03f0: mov             x2, NULL
    // 0xaf03f4: r0 = AllocateClosure()
    //     0xaf03f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf03f8: mov             x1, x0
    // 0xaf03fc: r0 = <String>
    //     0xaf03fc: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaf0400: stur            x1, [fp, #-0x28]
    // 0xaf0404: StoreField: r1->field_b = r0
    //     0xaf0404: stur            w0, [x1, #0xb]
    // 0xaf0408: r16 = <String>
    //     0xaf0408: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaf040c: str             x16, [SP, #0x10]
    // 0xaf0410: r2 = 10000
    //     0xaf0410: movz            x2, #0x2710
    // 0xaf0414: r16 = "Minimal zakat Rp10.000"
    //     0xaf0414: add             x16, PP, #0x35, lsl #12  ; [pp+0x351e8] "Minimal zakat Rp10.000"
    //     0xaf0418: ldr             x16, [x16, #0x1e8]
    // 0xaf041c: stp             x16, x2, [SP]
    // 0xaf0420: r4 = const [0x1, 0x2, 0x2, 0x1, errorText, 0x1, null]
    //     0xaf0420: add             x4, PP, #0x34, lsl #12  ; [pp+0x34cd8] List(7) [0x1, 0x2, 0x2, 0x1, "errorText", 0x1, Null]
    //     0xaf0424: ldr             x4, [x4, #0xcd8]
    // 0xaf0428: r0 = min()
    //     0xaf0428: bl              #0xae7668  ; [package:nuonline/common/utils/form_validators.dart] FormValidators::min
    // 0xaf042c: r1 = Null
    //     0xaf042c: mov             x1, NULL
    // 0xaf0430: r2 = 4
    //     0xaf0430: movz            x2, #0x4
    // 0xaf0434: stur            x0, [fp, #-0x30]
    // 0xaf0438: r0 = AllocateArray()
    //     0xaf0438: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf043c: mov             x2, x0
    // 0xaf0440: ldur            x0, [fp, #-0x28]
    // 0xaf0444: stur            x2, [fp, #-0x38]
    // 0xaf0448: StoreField: r2->field_f = r0
    //     0xaf0448: stur            w0, [x2, #0xf]
    // 0xaf044c: ldur            x0, [fp, #-0x30]
    // 0xaf0450: StoreField: r2->field_13 = r0
    //     0xaf0450: stur            w0, [x2, #0x13]
    // 0xaf0454: r1 = <(dynamic this, String?) => String?>
    //     0xaf0454: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd98] TypeArguments: <(dynamic this, String?) => String?>
    //     0xaf0458: ldr             x1, [x1, #0xd98]
    // 0xaf045c: r0 = AllocateGrowableArray()
    //     0xaf045c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf0460: mov             x1, x0
    // 0xaf0464: ldur            x0, [fp, #-0x38]
    // 0xaf0468: stur            x1, [fp, #-0x28]
    // 0xaf046c: StoreField: r1->field_f = r0
    //     0xaf046c: stur            w0, [x1, #0xf]
    // 0xaf0470: r2 = 4
    //     0xaf0470: movz            x2, #0x4
    // 0xaf0474: StoreField: r1->field_b = r2
    //     0xaf0474: stur            w2, [x1, #0xb]
    // 0xaf0478: r1 = 1
    //     0xaf0478: movz            x1, #0x1
    // 0xaf047c: r0 = AllocateContext()
    //     0xaf047c: bl              #0xec126c  ; AllocateContextStub
    // 0xaf0480: mov             x1, x0
    // 0xaf0484: ldur            x0, [fp, #-0x28]
    // 0xaf0488: StoreField: r1->field_f = r0
    //     0xaf0488: stur            w0, [x1, #0xf]
    // 0xaf048c: mov             x2, x1
    // 0xaf0490: r1 = Function '<anonymous closure>': static.
    //     0xaf0490: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fda8] AnonymousClosure: static (0xae60d4), of [package:form_builder_validators/src/form_builder_validators.dart] FormBuilderValidators
    //     0xaf0494: ldr             x1, [x1, #0xda8]
    // 0xaf0498: r0 = AllocateClosure()
    //     0xaf0498: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf049c: mov             x2, x0
    // 0xaf04a0: r0 = <String>
    //     0xaf04a0: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xaf04a4: stur            x2, [fp, #-0x28]
    // 0xaf04a8: StoreField: r2->field_b = r0
    //     0xaf04a8: stur            w0, [x2, #0xb]
    // 0xaf04ac: ldur            x0, [fp, #-8]
    // 0xaf04b0: LoadField: r1 = r0->field_f
    //     0xaf04b0: ldur            w1, [x0, #0xf]
    // 0xaf04b4: DecompressPointer r1
    //     0xaf04b4: add             x1, x1, HEAP, lsl #32
    // 0xaf04b8: r0 = controller()
    //     0xaf04b8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf04bc: LoadField: r2 = r0->field_23
    //     0xaf04bc: ldur            w2, [x0, #0x23]
    // 0xaf04c0: DecompressPointer r2
    //     0xaf04c0: add             x2, x2, HEAP, lsl #32
    // 0xaf04c4: LoadField: r3 = r2->field_7
    //     0xaf04c4: ldur            w3, [x2, #7]
    // 0xaf04c8: DecompressPointer r3
    //     0xaf04c8: add             x3, x3, HEAP, lsl #32
    // 0xaf04cc: r1 = Function 'call':.
    //     0xaf04cc: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaf04d0: ldr             x1, [x1, #0x310]
    // 0xaf04d4: r0 = AllocateClosureTA()
    //     0xaf04d4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaf04d8: mov             x3, x0
    // 0xaf04dc: r2 = Null
    //     0xaf04dc: mov             x2, NULL
    // 0xaf04e0: r1 = Null
    //     0xaf04e0: mov             x1, NULL
    // 0xaf04e4: stur            x3, [fp, #-0x30]
    // 0xaf04e8: r8 = (dynamic this, int?) => int
    //     0xaf04e8: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a0a0] FunctionType: (dynamic this, int?) => int
    //     0xaf04ec: ldr             x8, [x8, #0xa0]
    // 0xaf04f0: r3 = Null
    //     0xaf04f0: add             x3, PP, #0x35, lsl #12  ; [pp+0x351f0] Null
    //     0xaf04f4: ldr             x3, [x3, #0x1f0]
    // 0xaf04f8: r0 = DefaultTypeTest()
    //     0xaf04f8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaf04fc: r0 = NZakatTextField()
    //     0xaf04fc: bl              #0xae765c  ; AllocateNZakatTextFieldStub -> NZakatTextField (size=0x34)
    // 0xaf0500: mov             x3, x0
    // 0xaf0504: ldur            x0, [fp, #-0x18]
    // 0xaf0508: stur            x3, [fp, #-0x38]
    // 0xaf050c: StoreField: r3->field_b = r0
    //     0xaf050c: stur            w0, [x3, #0xb]
    // 0xaf0510: r0 = "Jumlah Zakat"
    //     0xaf0510: add             x0, PP, #0x35, lsl #12  ; [pp+0x35200] "Jumlah Zakat"
    //     0xaf0514: ldr             x0, [x0, #0x200]
    // 0xaf0518: StoreField: r3->field_f = r0
    //     0xaf0518: stur            w0, [x3, #0xf]
    // 0xaf051c: ldur            x0, [fp, #-0x30]
    // 0xaf0520: StoreField: r3->field_1b = r0
    //     0xaf0520: stur            w0, [x3, #0x1b]
    // 0xaf0524: r0 = false
    //     0xaf0524: add             x0, NULL, #0x30  ; false
    // 0xaf0528: StoreField: r3->field_1f = r0
    //     0xaf0528: stur            w0, [x3, #0x1f]
    // 0xaf052c: r1 = true
    //     0xaf052c: add             x1, NULL, #0x20  ; true
    // 0xaf0530: StoreField: r3->field_27 = r1
    //     0xaf0530: stur            w1, [x3, #0x27]
    // 0xaf0534: ldur            x1, [fp, #-0x28]
    // 0xaf0538: StoreField: r3->field_2b = r1
    //     0xaf0538: stur            w1, [x3, #0x2b]
    // 0xaf053c: StoreField: r3->field_2f = r0
    //     0xaf053c: stur            w0, [x3, #0x2f]
    // 0xaf0540: r1 = Null
    //     0xaf0540: mov             x1, NULL
    // 0xaf0544: r2 = 4
    //     0xaf0544: movz            x2, #0x4
    // 0xaf0548: r0 = AllocateArray()
    //     0xaf0548: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf054c: mov             x2, x0
    // 0xaf0550: ldur            x0, [fp, #-0x38]
    // 0xaf0554: stur            x2, [fp, #-0x18]
    // 0xaf0558: StoreField: r2->field_f = r0
    //     0xaf0558: stur            w0, [x2, #0xf]
    // 0xaf055c: r16 = Instance_SizedBox
    //     0xaf055c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaf0560: ldr             x16, [x16, #0x950]
    // 0xaf0564: StoreField: r2->field_13 = r16
    //     0xaf0564: stur            w16, [x2, #0x13]
    // 0xaf0568: r1 = <Widget>
    //     0xaf0568: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf056c: r0 = AllocateGrowableArray()
    //     0xaf056c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf0570: mov             x2, x0
    // 0xaf0574: ldur            x0, [fp, #-0x18]
    // 0xaf0578: stur            x2, [fp, #-0x28]
    // 0xaf057c: StoreField: r2->field_f = r0
    //     0xaf057c: stur            w0, [x2, #0xf]
    // 0xaf0580: r0 = 4
    //     0xaf0580: movz            x0, #0x4
    // 0xaf0584: StoreField: r2->field_b = r0
    //     0xaf0584: stur            w0, [x2, #0xb]
    // 0xaf0588: ldur            x3, [fp, #-8]
    // 0xaf058c: LoadField: r1 = r3->field_f
    //     0xaf058c: ldur            w1, [x3, #0xf]
    // 0xaf0590: DecompressPointer r1
    //     0xaf0590: add             x1, x1, HEAP, lsl #32
    // 0xaf0594: r0 = controller()
    //     0xaf0594: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0598: mov             x1, x0
    // 0xaf059c: r0 = showCalcInfo()
    //     0xaf059c: bl              #0xaf0918  ; [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::showCalcInfo
    // 0xaf05a0: tbnz            w0, #4, #0xaf0838
    // 0xaf05a4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf05a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf05a8: ldr             x0, [x0, #0x2670]
    //     0xaf05ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf05b0: cmp             w0, w16
    //     0xaf05b4: b.ne            #0xaf05c0
    //     0xaf05b8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf05bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf05c0: r0 = GetNavigation.textTheme()
    //     0xaf05c0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf05c4: LoadField: r1 = r0->field_27
    //     0xaf05c4: ldur            w1, [x0, #0x27]
    // 0xaf05c8: DecompressPointer r1
    //     0xaf05c8: add             x1, x1, HEAP, lsl #32
    // 0xaf05cc: stur            x1, [fp, #-0x18]
    // 0xaf05d0: cmp             w1, NULL
    // 0xaf05d4: b.ne            #0xaf05e0
    // 0xaf05d8: r0 = Null
    //     0xaf05d8: mov             x0, NULL
    // 0xaf05dc: b               #0xaf0608
    // 0xaf05e0: r0 = GetNavigation.theme()
    //     0xaf05e0: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaf05e4: LoadField: r1 = r0->field_83
    //     0xaf05e4: ldur            w1, [x0, #0x83]
    // 0xaf05e8: DecompressPointer r1
    //     0xaf05e8: add             x1, x1, HEAP, lsl #32
    // 0xaf05ec: LoadField: r0 = r1->field_1b
    //     0xaf05ec: ldur            w0, [x1, #0x1b]
    // 0xaf05f0: DecompressPointer r0
    //     0xaf05f0: add             x0, x0, HEAP, lsl #32
    // 0xaf05f4: str             x0, [SP]
    // 0xaf05f8: ldur            x1, [fp, #-0x18]
    // 0xaf05fc: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaf05fc: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaf0600: ldr             x4, [x4, #0x228]
    // 0xaf0604: r0 = copyWith()
    //     0xaf0604: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0608: stur            x0, [fp, #-0x18]
    // 0xaf060c: r0 = TextSpan()
    //     0xaf060c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaf0610: mov             x1, x0
    // 0xaf0614: r0 = "Belum tahu nominal zakatmu\? Buka "
    //     0xaf0614: add             x0, PP, #0x35, lsl #12  ; [pp+0x35208] "Belum tahu nominal zakatmu\? Buka "
    //     0xaf0618: ldr             x0, [x0, #0x208]
    // 0xaf061c: stur            x1, [fp, #-0x30]
    // 0xaf0620: StoreField: r1->field_b = r0
    //     0xaf0620: stur            w0, [x1, #0xb]
    // 0xaf0624: r0 = Instance__DeferringMouseCursor
    //     0xaf0624: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaf0628: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf0628: stur            w0, [x1, #0x17]
    // 0xaf062c: ldur            x2, [fp, #-0x18]
    // 0xaf0630: StoreField: r1->field_7 = r2
    //     0xaf0630: stur            w2, [x1, #7]
    // 0xaf0634: r0 = GetNavigation.textTheme()
    //     0xaf0634: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf0638: LoadField: r1 = r0->field_27
    //     0xaf0638: ldur            w1, [x0, #0x27]
    // 0xaf063c: DecompressPointer r1
    //     0xaf063c: add             x1, x1, HEAP, lsl #32
    // 0xaf0640: stur            x1, [fp, #-0x18]
    // 0xaf0644: cmp             w1, NULL
    // 0xaf0648: b.ne            #0xaf0654
    // 0xaf064c: r3 = Null
    //     0xaf064c: mov             x3, NULL
    // 0xaf0650: b               #0xaf0688
    // 0xaf0654: r0 = GetNavigation.theme()
    //     0xaf0654: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xaf0658: LoadField: r1 = r0->field_3f
    //     0xaf0658: ldur            w1, [x0, #0x3f]
    // 0xaf065c: DecompressPointer r1
    //     0xaf065c: add             x1, x1, HEAP, lsl #32
    // 0xaf0660: LoadField: r0 = r1->field_2b
    //     0xaf0660: ldur            w0, [x1, #0x2b]
    // 0xaf0664: DecompressPointer r0
    //     0xaf0664: add             x0, x0, HEAP, lsl #32
    // 0xaf0668: r16 = Instance_FontWeight
    //     0xaf0668: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xaf066c: ldr             x16, [x16, #0xe20]
    // 0xaf0670: stp             x16, x0, [SP]
    // 0xaf0674: ldur            x1, [fp, #-0x18]
    // 0xaf0678: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontWeight, 0x2, null]
    //     0xaf0678: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f668] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontWeight", 0x2, Null]
    //     0xaf067c: ldr             x4, [x4, #0x668]
    // 0xaf0680: r0 = copyWith()
    //     0xaf0680: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0684: mov             x3, x0
    // 0xaf0688: ldur            x2, [fp, #-8]
    // 0xaf068c: ldur            x0, [fp, #-0x30]
    // 0xaf0690: ldur            x1, [fp, #-0x28]
    // 0xaf0694: stur            x3, [fp, #-0x18]
    // 0xaf0698: r0 = TapGestureRecognizer()
    //     0xaf0698: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0xaf069c: mov             x4, x0
    // 0xaf06a0: r0 = false
    //     0xaf06a0: add             x0, NULL, #0x30  ; false
    // 0xaf06a4: stur            x4, [fp, #-0x38]
    // 0xaf06a8: StoreField: r4->field_47 = r0
    //     0xaf06a8: stur            w0, [x4, #0x47]
    // 0xaf06ac: StoreField: r4->field_4b = r0
    //     0xaf06ac: stur            w0, [x4, #0x4b]
    // 0xaf06b0: mov             x1, x4
    // 0xaf06b4: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xaf06b4: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xaf06b8: ldr             x2, [x2, #0x3d8]
    // 0xaf06bc: r3 = Instance_Duration
    //     0xaf06bc: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xaf06c0: r5 = Null
    //     0xaf06c0: mov             x5, NULL
    // 0xaf06c4: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xaf06c4: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xaf06c8: r0 = PrimaryPointerGestureRecognizer()
    //     0xaf06c8: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xaf06cc: ldur            x0, [fp, #-8]
    // 0xaf06d0: LoadField: r1 = r0->field_f
    //     0xaf06d0: ldur            w1, [x0, #0xf]
    // 0xaf06d4: DecompressPointer r1
    //     0xaf06d4: add             x1, x1, HEAP, lsl #32
    // 0xaf06d8: r0 = controller()
    //     0xaf06d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf06dc: mov             x2, x0
    // 0xaf06e0: r1 = Function 'onCalcPressed':.
    //     0xaf06e0: add             x1, PP, #0x35, lsl #12  ; [pp+0x35210] AnonymousClosure: (0xaf0a00), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::onCalcPressed (0xaf0a38)
    //     0xaf06e4: ldr             x1, [x1, #0x210]
    // 0xaf06e8: r0 = AllocateClosure()
    //     0xaf06e8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf06ec: ldur            x1, [fp, #-0x38]
    // 0xaf06f0: StoreField: r1->field_5f = r0
    //     0xaf06f0: stur            w0, [x1, #0x5f]
    //     0xaf06f4: ldurb           w16, [x1, #-1]
    //     0xaf06f8: ldurb           w17, [x0, #-1]
    //     0xaf06fc: and             x16, x17, x16, lsr #2
    //     0xaf0700: tst             x16, HEAP, lsr #32
    //     0xaf0704: b.eq            #0xaf070c
    //     0xaf0708: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaf070c: r0 = TextSpan()
    //     0xaf070c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaf0710: mov             x3, x0
    // 0xaf0714: r0 = "Kalkulator Zakat"
    //     0xaf0714: add             x0, PP, #0xf, lsl #12  ; [pp+0xfce8] "Kalkulator Zakat"
    //     0xaf0718: ldr             x0, [x0, #0xce8]
    // 0xaf071c: stur            x3, [fp, #-8]
    // 0xaf0720: StoreField: r3->field_b = r0
    //     0xaf0720: stur            w0, [x3, #0xb]
    // 0xaf0724: ldur            x0, [fp, #-0x38]
    // 0xaf0728: StoreField: r3->field_13 = r0
    //     0xaf0728: stur            w0, [x3, #0x13]
    // 0xaf072c: r0 = Instance_SystemMouseCursor
    //     0xaf072c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xaf0730: ldr             x0, [x0, #0xbf0]
    // 0xaf0734: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf0734: stur            w0, [x3, #0x17]
    // 0xaf0738: ldur            x0, [fp, #-0x18]
    // 0xaf073c: StoreField: r3->field_7 = r0
    //     0xaf073c: stur            w0, [x3, #7]
    // 0xaf0740: r1 = Null
    //     0xaf0740: mov             x1, NULL
    // 0xaf0744: r2 = 4
    //     0xaf0744: movz            x2, #0x4
    // 0xaf0748: r0 = AllocateArray()
    //     0xaf0748: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf074c: mov             x2, x0
    // 0xaf0750: ldur            x0, [fp, #-0x30]
    // 0xaf0754: stur            x2, [fp, #-0x18]
    // 0xaf0758: StoreField: r2->field_f = r0
    //     0xaf0758: stur            w0, [x2, #0xf]
    // 0xaf075c: ldur            x0, [fp, #-8]
    // 0xaf0760: StoreField: r2->field_13 = r0
    //     0xaf0760: stur            w0, [x2, #0x13]
    // 0xaf0764: r1 = <InlineSpan>
    //     0xaf0764: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xaf0768: ldr             x1, [x1, #0x5f0]
    // 0xaf076c: r0 = AllocateGrowableArray()
    //     0xaf076c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf0770: mov             x1, x0
    // 0xaf0774: ldur            x0, [fp, #-0x18]
    // 0xaf0778: stur            x1, [fp, #-8]
    // 0xaf077c: StoreField: r1->field_f = r0
    //     0xaf077c: stur            w0, [x1, #0xf]
    // 0xaf0780: r0 = 4
    //     0xaf0780: movz            x0, #0x4
    // 0xaf0784: StoreField: r1->field_b = r0
    //     0xaf0784: stur            w0, [x1, #0xb]
    // 0xaf0788: r0 = TextSpan()
    //     0xaf0788: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xaf078c: mov             x1, x0
    // 0xaf0790: ldur            x0, [fp, #-8]
    // 0xaf0794: stur            x1, [fp, #-0x18]
    // 0xaf0798: StoreField: r1->field_f = r0
    //     0xaf0798: stur            w0, [x1, #0xf]
    // 0xaf079c: r0 = Instance__DeferringMouseCursor
    //     0xaf079c: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaf07a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf07a0: stur            w0, [x1, #0x17]
    // 0xaf07a4: r0 = RichText()
    //     0xaf07a4: bl              #0xaa3ce4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaf07a8: mov             x1, x0
    // 0xaf07ac: ldur            x2, [fp, #-0x18]
    // 0xaf07b0: stur            x0, [fp, #-8]
    // 0xaf07b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaf07b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaf07b8: r0 = RichText()
    //     0xaf07b8: bl              #0xaa3744  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaf07bc: ldur            x0, [fp, #-0x28]
    // 0xaf07c0: LoadField: r1 = r0->field_b
    //     0xaf07c0: ldur            w1, [x0, #0xb]
    // 0xaf07c4: LoadField: r2 = r0->field_f
    //     0xaf07c4: ldur            w2, [x0, #0xf]
    // 0xaf07c8: DecompressPointer r2
    //     0xaf07c8: add             x2, x2, HEAP, lsl #32
    // 0xaf07cc: LoadField: r3 = r2->field_b
    //     0xaf07cc: ldur            w3, [x2, #0xb]
    // 0xaf07d0: r2 = LoadInt32Instr(r1)
    //     0xaf07d0: sbfx            x2, x1, #1, #0x1f
    // 0xaf07d4: stur            x2, [fp, #-0x40]
    // 0xaf07d8: r1 = LoadInt32Instr(r3)
    //     0xaf07d8: sbfx            x1, x3, #1, #0x1f
    // 0xaf07dc: cmp             x2, x1
    // 0xaf07e0: b.ne            #0xaf07ec
    // 0xaf07e4: mov             x1, x0
    // 0xaf07e8: r0 = _growToNextCapacity()
    //     0xaf07e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf07ec: ldur            x2, [fp, #-0x28]
    // 0xaf07f0: ldur            x3, [fp, #-0x40]
    // 0xaf07f4: add             x0, x3, #1
    // 0xaf07f8: lsl             x1, x0, #1
    // 0xaf07fc: StoreField: r2->field_b = r1
    //     0xaf07fc: stur            w1, [x2, #0xb]
    // 0xaf0800: LoadField: r1 = r2->field_f
    //     0xaf0800: ldur            w1, [x2, #0xf]
    // 0xaf0804: DecompressPointer r1
    //     0xaf0804: add             x1, x1, HEAP, lsl #32
    // 0xaf0808: ldur            x0, [fp, #-8]
    // 0xaf080c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf080c: add             x25, x1, x3, lsl #2
    //     0xaf0810: add             x25, x25, #0xf
    //     0xaf0814: str             w0, [x25]
    //     0xaf0818: tbz             w0, #0, #0xaf0834
    //     0xaf081c: ldurb           w16, [x1, #-1]
    //     0xaf0820: ldurb           w17, [x0, #-1]
    //     0xaf0824: and             x16, x17, x16, lsr #2
    //     0xaf0828: tst             x16, HEAP, lsr #32
    //     0xaf082c: b.eq            #0xaf0834
    //     0xaf0830: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf0834: b               #0xaf083c
    // 0xaf0838: ldur            x2, [fp, #-0x28]
    // 0xaf083c: LoadField: r0 = r2->field_b
    //     0xaf083c: ldur            w0, [x2, #0xb]
    // 0xaf0840: LoadField: r1 = r2->field_f
    //     0xaf0840: ldur            w1, [x2, #0xf]
    // 0xaf0844: DecompressPointer r1
    //     0xaf0844: add             x1, x1, HEAP, lsl #32
    // 0xaf0848: LoadField: r3 = r1->field_b
    //     0xaf0848: ldur            w3, [x1, #0xb]
    // 0xaf084c: r4 = LoadInt32Instr(r0)
    //     0xaf084c: sbfx            x4, x0, #1, #0x1f
    // 0xaf0850: stur            x4, [fp, #-0x40]
    // 0xaf0854: r0 = LoadInt32Instr(r3)
    //     0xaf0854: sbfx            x0, x3, #1, #0x1f
    // 0xaf0858: cmp             x4, x0
    // 0xaf085c: b.ne            #0xaf0868
    // 0xaf0860: mov             x1, x2
    // 0xaf0864: r0 = _growToNextCapacity()
    //     0xaf0864: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf0868: ldur            x2, [fp, #-0x10]
    // 0xaf086c: ldur            x0, [fp, #-0x28]
    // 0xaf0870: ldur            x1, [fp, #-0x40]
    // 0xaf0874: add             x3, x1, #1
    // 0xaf0878: lsl             x4, x3, #1
    // 0xaf087c: StoreField: r0->field_b = r4
    //     0xaf087c: stur            w4, [x0, #0xb]
    // 0xaf0880: LoadField: r3 = r0->field_f
    //     0xaf0880: ldur            w3, [x0, #0xf]
    // 0xaf0884: DecompressPointer r3
    //     0xaf0884: add             x3, x3, HEAP, lsl #32
    // 0xaf0888: add             x4, x3, x1, lsl #2
    // 0xaf088c: r16 = Instance_SizedBox
    //     0xaf088c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xaf0890: ldr             x16, [x16, #0x950]
    // 0xaf0894: StoreField: r4->field_f = r16
    //     0xaf0894: stur            w16, [x4, #0xf]
    // 0xaf0898: r16 = Instance_ZakatTypes
    //     0xaf0898: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xaf089c: ldr             x16, [x16, #0x620]
    // 0xaf08a0: cmp             w2, w16
    // 0xaf08a4: b.ne            #0xaf08ac
    // 0xaf08a8: ldur            x0, [fp, #-0x20]
    // 0xaf08ac: stur            x0, [fp, #-8]
    // 0xaf08b0: r0 = Column()
    //     0xaf08b0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf08b4: r1 = Instance_Axis
    //     0xaf08b4: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf08b8: StoreField: r0->field_f = r1
    //     0xaf08b8: stur            w1, [x0, #0xf]
    // 0xaf08bc: r1 = Instance_MainAxisAlignment
    //     0xaf08bc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf08c0: ldr             x1, [x1, #0x730]
    // 0xaf08c4: StoreField: r0->field_13 = r1
    //     0xaf08c4: stur            w1, [x0, #0x13]
    // 0xaf08c8: r1 = Instance_MainAxisSize
    //     0xaf08c8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf08cc: ldr             x1, [x1, #0x738]
    // 0xaf08d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf08d0: stur            w1, [x0, #0x17]
    // 0xaf08d4: r1 = Instance_CrossAxisAlignment
    //     0xaf08d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xaf08d8: ldr             x1, [x1, #0xf50]
    // 0xaf08dc: StoreField: r0->field_1b = r1
    //     0xaf08dc: stur            w1, [x0, #0x1b]
    // 0xaf08e0: r1 = Instance_VerticalDirection
    //     0xaf08e0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf08e4: ldr             x1, [x1, #0x748]
    // 0xaf08e8: StoreField: r0->field_23 = r1
    //     0xaf08e8: stur            w1, [x0, #0x23]
    // 0xaf08ec: r1 = Instance_Clip
    //     0xaf08ec: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf08f0: ldr             x1, [x1, #0x750]
    // 0xaf08f4: StoreField: r0->field_2b = r1
    //     0xaf08f4: stur            w1, [x0, #0x2b]
    // 0xaf08f8: StoreField: r0->field_2f = rZR
    //     0xaf08f8: stur            xzr, [x0, #0x2f]
    // 0xaf08fc: ldur            x1, [fp, #-8]
    // 0xaf0900: StoreField: r0->field_b = r1
    //     0xaf0900: stur            w1, [x0, #0xb]
    // 0xaf0904: LeaveFrame
    //     0xaf0904: mov             SP, fp
    //     0xaf0908: ldp             fp, lr, [SP], #0x10
    // 0xaf090c: ret
    //     0xaf090c: ret             
    // 0xaf0910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0914: b               #0xaf0200
  }
  [closure] ZakatQualityRadioCard <anonymous closure>(dynamic, ZakatQuality) {
    // ** addr: 0xaf0e38, size: 0x12c
    // 0xaf0e38: EnterFrame
    //     0xaf0e38: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0e3c: mov             fp, SP
    // 0xaf0e40: AllocStack(0x20)
    //     0xaf0e40: sub             SP, SP, #0x20
    // 0xaf0e44: SetupParameters()
    //     0xaf0e44: ldr             x0, [fp, #0x18]
    //     0xaf0e48: ldur            w2, [x0, #0x17]
    //     0xaf0e4c: add             x2, x2, HEAP, lsl #32
    //     0xaf0e50: stur            x2, [fp, #-8]
    // 0xaf0e54: CheckStackOverflow
    //     0xaf0e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0e58: cmp             SP, x16
    //     0xaf0e5c: b.ls            #0xaf0f5c
    // 0xaf0e60: LoadField: r1 = r2->field_f
    //     0xaf0e60: ldur            w1, [x2, #0xf]
    // 0xaf0e64: DecompressPointer r1
    //     0xaf0e64: add             x1, x1, HEAP, lsl #32
    // 0xaf0e68: r0 = controller()
    //     0xaf0e68: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0e6c: LoadField: r1 = r0->field_53
    //     0xaf0e6c: ldur            w1, [x0, #0x53]
    // 0xaf0e70: DecompressPointer r1
    //     0xaf0e70: add             x1, x1, HEAP, lsl #32
    // 0xaf0e74: r0 = value()
    //     0xaf0e74: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0e78: mov             x2, x0
    // 0xaf0e7c: ldur            x0, [fp, #-8]
    // 0xaf0e80: stur            x2, [fp, #-0x10]
    // 0xaf0e84: LoadField: r1 = r0->field_f
    //     0xaf0e84: ldur            w1, [x0, #0xf]
    // 0xaf0e88: DecompressPointer r1
    //     0xaf0e88: add             x1, x1, HEAP, lsl #32
    // 0xaf0e8c: r0 = controller()
    //     0xaf0e8c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0e90: LoadField: r2 = r0->field_53
    //     0xaf0e90: ldur            w2, [x0, #0x53]
    // 0xaf0e94: DecompressPointer r2
    //     0xaf0e94: add             x2, x2, HEAP, lsl #32
    // 0xaf0e98: LoadField: r3 = r2->field_7
    //     0xaf0e98: ldur            w3, [x2, #7]
    // 0xaf0e9c: DecompressPointer r3
    //     0xaf0e9c: add             x3, x3, HEAP, lsl #32
    // 0xaf0ea0: r1 = Function 'call':.
    //     0xaf0ea0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaf0ea4: ldr             x1, [x1, #0x310]
    // 0xaf0ea8: r0 = AllocateClosureTA()
    //     0xaf0ea8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaf0eac: mov             x3, x0
    // 0xaf0eb0: r2 = Null
    //     0xaf0eb0: mov             x2, NULL
    // 0xaf0eb4: r1 = Null
    //     0xaf0eb4: mov             x1, NULL
    // 0xaf0eb8: stur            x3, [fp, #-0x18]
    // 0xaf0ebc: r8 = (dynamic this, ZakatQuality?) => ZakatQuality
    //     0xaf0ebc: add             x8, PP, #0x35, lsl #12  ; [pp+0x35290] FunctionType: (dynamic this, ZakatQuality?) => ZakatQuality
    //     0xaf0ec0: ldr             x8, [x8, #0x290]
    // 0xaf0ec4: r3 = Null
    //     0xaf0ec4: add             x3, PP, #0x35, lsl #12  ; [pp+0x35298] Null
    //     0xaf0ec8: ldr             x3, [x3, #0x298]
    // 0xaf0ecc: r0 = DefaultTypeTest()
    //     0xaf0ecc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaf0ed0: ldur            x0, [fp, #-8]
    // 0xaf0ed4: LoadField: r1 = r0->field_f
    //     0xaf0ed4: ldur            w1, [x0, #0xf]
    // 0xaf0ed8: DecompressPointer r1
    //     0xaf0ed8: add             x1, x1, HEAP, lsl #32
    // 0xaf0edc: r0 = controller()
    //     0xaf0edc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0ee0: LoadField: r1 = r0->field_4f
    //     0xaf0ee0: ldur            w1, [x0, #0x4f]
    // 0xaf0ee4: DecompressPointer r1
    //     0xaf0ee4: add             x1, x1, HEAP, lsl #32
    // 0xaf0ee8: r0 = value()
    //     0xaf0ee8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf0eec: r1 = LoadClassIdInstr(r0)
    //     0xaf0eec: ldur            x1, [x0, #-1]
    //     0xaf0ef0: ubfx            x1, x1, #0xc, #0x14
    // 0xaf0ef4: str             x0, [SP]
    // 0xaf0ef8: mov             x0, x1
    // 0xaf0efc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf0efc: movz            x17, #0xc834
    //     0xaf0f00: add             lr, x0, x17
    //     0xaf0f04: ldr             lr, [x21, lr, lsl #3]
    //     0xaf0f08: blr             lr
    // 0xaf0f0c: r1 = LoadInt32Instr(r0)
    //     0xaf0f0c: sbfx            x1, x0, #1, #0x1f
    //     0xaf0f10: tbz             w0, #0, #0xaf0f18
    //     0xaf0f14: ldur            x1, [x0, #7]
    // 0xaf0f18: cmp             x1, #1
    // 0xaf0f1c: r16 = true
    //     0xaf0f1c: add             x16, NULL, #0x20  ; true
    // 0xaf0f20: r17 = false
    //     0xaf0f20: add             x17, NULL, #0x30  ; false
    // 0xaf0f24: csel            x0, x16, x17, gt
    // 0xaf0f28: stur            x0, [fp, #-8]
    // 0xaf0f2c: r0 = ZakatQualityRadioCard()
    //     0xaf0f2c: bl              #0xaf0f64  ; AllocateZakatQualityRadioCardStub -> ZakatQualityRadioCard (size=0x1c)
    // 0xaf0f30: ldr             x1, [fp, #0x10]
    // 0xaf0f34: StoreField: r0->field_b = r1
    //     0xaf0f34: stur            w1, [x0, #0xb]
    // 0xaf0f38: ldur            x1, [fp, #-0x18]
    // 0xaf0f3c: StoreField: r0->field_13 = r1
    //     0xaf0f3c: stur            w1, [x0, #0x13]
    // 0xaf0f40: ldur            x1, [fp, #-0x10]
    // 0xaf0f44: StoreField: r0->field_f = r1
    //     0xaf0f44: stur            w1, [x0, #0xf]
    // 0xaf0f48: ldur            x1, [fp, #-8]
    // 0xaf0f4c: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf0f4c: stur            w1, [x0, #0x17]
    // 0xaf0f50: LeaveFrame
    //     0xaf0f50: mov             SP, fp
    //     0xaf0f54: ldp             fp, lr, [SP], #0x10
    // 0xaf0f58: ret
    //     0xaf0f58: ret             
    // 0xaf0f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0f5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0f60: b               #0xaf0e60
  }
  [closure] TextField <anonymous closure>(dynamic) {
    // ** addr: 0xaf0f70, size: 0x244
    // 0xaf0f70: EnterFrame
    //     0xaf0f70: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0f74: mov             fp, SP
    // 0xaf0f78: AllocStack(0x38)
    //     0xaf0f78: sub             SP, SP, #0x38
    // 0xaf0f7c: SetupParameters()
    //     0xaf0f7c: ldr             x0, [fp, #0x10]
    //     0xaf0f80: ldur            w2, [x0, #0x17]
    //     0xaf0f84: add             x2, x2, HEAP, lsl #32
    //     0xaf0f88: stur            x2, [fp, #-8]
    // 0xaf0f8c: CheckStackOverflow
    //     0xaf0f8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0f90: cmp             SP, x16
    //     0xaf0f94: b.ls            #0xaf11ac
    // 0xaf0f98: LoadField: r1 = r2->field_f
    //     0xaf0f98: ldur            w1, [x2, #0xf]
    // 0xaf0f9c: DecompressPointer r1
    //     0xaf0f9c: add             x1, x1, HEAP, lsl #32
    // 0xaf0fa0: r0 = controller()
    //     0xaf0fa0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf0fa4: LoadField: r1 = r0->field_2f
    //     0xaf0fa4: ldur            w1, [x0, #0x2f]
    // 0xaf0fa8: DecompressPointer r1
    //     0xaf0fa8: add             x1, x1, HEAP, lsl #32
    // 0xaf0fac: r0 = value()
    //     0xaf0fac: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf0fb0: mov             x1, x0
    // 0xaf0fb4: r0 = ZakatTypesExtension.title()
    //     0xaf0fb4: bl              #0xaf11b4  ; [package:nuonline/app/data/enums/zakat_enum.dart] ::ZakatTypesExtension.title
    // 0xaf0fb8: stur            x0, [fp, #-0x10]
    // 0xaf0fbc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf0fbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf0fc0: ldr             x0, [x0, #0x2670]
    //     0xaf0fc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf0fc8: cmp             w0, w16
    //     0xaf0fcc: b.ne            #0xaf0fd8
    //     0xaf0fd0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf0fd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf0fd8: r0 = GetNavigation.textTheme()
    //     0xaf0fd8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf0fdc: LoadField: r3 = r0->field_2f
    //     0xaf0fdc: ldur            w3, [x0, #0x2f]
    // 0xaf0fe0: DecompressPointer r3
    //     0xaf0fe0: add             x3, x3, HEAP, lsl #32
    // 0xaf0fe4: stur            x3, [fp, #-0x18]
    // 0xaf0fe8: cmp             w3, NULL
    // 0xaf0fec: b.ne            #0xaf0ff8
    // 0xaf0ff0: r2 = Null
    //     0xaf0ff0: mov             x2, NULL
    // 0xaf0ff4: b               #0xaf103c
    // 0xaf0ff8: r1 = _ConstMap len:3
    //     0xaf0ff8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaf0ffc: ldr             x1, [x1, #0xbe8]
    // 0xaf1000: r2 = 6
    //     0xaf1000: movz            x2, #0x6
    // 0xaf1004: r0 = []()
    //     0xaf1004: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf1008: r16 = <Color?>
    //     0xaf1008: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf100c: ldr             x16, [x16, #0x98]
    // 0xaf1010: r30 = Instance_Color
    //     0xaf1010: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaf1014: stp             lr, x16, [SP, #8]
    // 0xaf1018: str             x0, [SP]
    // 0xaf101c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf101c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf1020: r0 = mode()
    //     0xaf1020: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf1024: str             x0, [SP]
    // 0xaf1028: ldur            x1, [fp, #-0x18]
    // 0xaf102c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaf102c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaf1030: ldr             x4, [x4, #0x228]
    // 0xaf1034: r0 = copyWith()
    //     0xaf1034: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf1038: mov             x2, x0
    // 0xaf103c: ldur            x1, [fp, #-8]
    // 0xaf1040: ldur            x0, [fp, #-0x10]
    // 0xaf1044: stur            x2, [fp, #-0x18]
    // 0xaf1048: r0 = InputDecoration()
    //     0xaf1048: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xaf104c: mov             x2, x0
    // 0xaf1050: ldur            x0, [fp, #-0x10]
    // 0xaf1054: stur            x2, [fp, #-0x20]
    // 0xaf1058: StoreField: r2->field_2f = r0
    //     0xaf1058: stur            w0, [x2, #0x2f]
    // 0xaf105c: ldur            x0, [fp, #-0x18]
    // 0xaf1060: StoreField: r2->field_33 = r0
    //     0xaf1060: stur            w0, [x2, #0x33]
    // 0xaf1064: r0 = true
    //     0xaf1064: add             x0, NULL, #0x20  ; true
    // 0xaf1068: StoreField: r2->field_43 = r0
    //     0xaf1068: stur            w0, [x2, #0x43]
    // 0xaf106c: r1 = Instance_Icon
    //     0xaf106c: add             x1, PP, #0x35, lsl #12  ; [pp+0x352b8] Obj!Icon@e24571
    //     0xaf1070: ldr             x1, [x1, #0x2b8]
    // 0xaf1074: StoreField: r2->field_83 = r1
    //     0xaf1074: stur            w1, [x2, #0x83]
    // 0xaf1078: StoreField: r2->field_cf = r0
    //     0xaf1078: stur            w0, [x2, #0xcf]
    // 0xaf107c: ldur            x1, [fp, #-8]
    // 0xaf1080: LoadField: r3 = r1->field_f
    //     0xaf1080: ldur            w3, [x1, #0xf]
    // 0xaf1084: DecompressPointer r3
    //     0xaf1084: add             x3, x3, HEAP, lsl #32
    // 0xaf1088: mov             x1, x3
    // 0xaf108c: r0 = controller()
    //     0xaf108c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1090: stur            x0, [fp, #-8]
    // 0xaf1094: r0 = TextField()
    //     0xaf1094: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xaf1098: mov             x3, x0
    // 0xaf109c: r0 = EditableText
    //     0xaf109c: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xaf10a0: stur            x3, [fp, #-0x10]
    // 0xaf10a4: StoreField: r3->field_f = r0
    //     0xaf10a4: stur            w0, [x3, #0xf]
    // 0xaf10a8: ldur            x0, [fp, #-0x20]
    // 0xaf10ac: StoreField: r3->field_1b = r0
    //     0xaf10ac: stur            w0, [x3, #0x1b]
    // 0xaf10b0: r0 = Instance_TextCapitalization
    //     0xaf10b0: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xaf10b4: StoreField: r3->field_27 = r0
    //     0xaf10b4: stur            w0, [x3, #0x27]
    // 0xaf10b8: r0 = Instance_TextAlign
    //     0xaf10b8: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xaf10bc: StoreField: r3->field_33 = r0
    //     0xaf10bc: stur            w0, [x3, #0x33]
    // 0xaf10c0: r0 = true
    //     0xaf10c0: add             x0, NULL, #0x20  ; true
    // 0xaf10c4: StoreField: r3->field_6f = r0
    //     0xaf10c4: stur            w0, [x3, #0x6f]
    // 0xaf10c8: r4 = false
    //     0xaf10c8: add             x4, NULL, #0x30  ; false
    // 0xaf10cc: StoreField: r3->field_3f = r4
    //     0xaf10cc: stur            w4, [x3, #0x3f]
    // 0xaf10d0: r1 = "•"
    //     0xaf10d0: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xaf10d4: ldr             x1, [x1, #0x4c8]
    // 0xaf10d8: StoreField: r3->field_47 = r1
    //     0xaf10d8: stur            w1, [x3, #0x47]
    // 0xaf10dc: StoreField: r3->field_4b = r4
    //     0xaf10dc: stur            w4, [x3, #0x4b]
    // 0xaf10e0: StoreField: r3->field_4f = r0
    //     0xaf10e0: stur            w0, [x3, #0x4f]
    // 0xaf10e4: StoreField: r3->field_5b = r0
    //     0xaf10e4: stur            w0, [x3, #0x5b]
    // 0xaf10e8: r1 = 1
    //     0xaf10e8: movz            x1, #0x1
    // 0xaf10ec: StoreField: r3->field_5f = r1
    //     0xaf10ec: stur            x1, [x3, #0x5f]
    // 0xaf10f0: StoreField: r3->field_6b = r4
    //     0xaf10f0: stur            w4, [x3, #0x6b]
    // 0xaf10f4: d0 = 2.000000
    //     0xaf10f4: fmov            d0, #2.00000000
    // 0xaf10f8: StoreField: r3->field_9f = d0
    //     0xaf10f8: stur            d0, [x3, #0x9f]
    // 0xaf10fc: r1 = Instance_BoxHeightStyle
    //     0xaf10fc: ldr             x1, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xaf1100: StoreField: r3->field_bb = r1
    //     0xaf1100: stur            w1, [x3, #0xbb]
    // 0xaf1104: r1 = Instance_BoxWidthStyle
    //     0xaf1104: ldr             x1, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xaf1108: StoreField: r3->field_bf = r1
    //     0xaf1108: stur            w1, [x3, #0xbf]
    // 0xaf110c: r1 = Instance_EdgeInsets
    //     0xaf110c: ldr             x1, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xaf1110: StoreField: r3->field_c7 = r1
    //     0xaf1110: stur            w1, [x3, #0xc7]
    // 0xaf1114: r1 = Instance_DragStartBehavior
    //     0xaf1114: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf1118: StoreField: r3->field_d3 = r1
    //     0xaf1118: stur            w1, [x3, #0xd3]
    // 0xaf111c: ldur            x2, [fp, #-8]
    // 0xaf1120: r1 = Function 'selectType':.
    //     0xaf1120: add             x1, PP, #0x35, lsl #12  ; [pp+0x352c0] AnonymousClosure: (0xaf134c), in [package:nuonline/app/modules/donation/controllers/zakat_form_controller.dart] ZakatFormController::selectType (0xaf1384)
    //     0xaf1124: ldr             x1, [x1, #0x2c0]
    // 0xaf1128: r0 = AllocateClosure()
    //     0xaf1128: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf112c: mov             x1, x0
    // 0xaf1130: ldur            x0, [fp, #-0x10]
    // 0xaf1134: StoreField: r0->field_d7 = r1
    //     0xaf1134: stur            w1, [x0, #0xd7]
    // 0xaf1138: r1 = false
    //     0xaf1138: add             x1, NULL, #0x30  ; false
    // 0xaf113c: StoreField: r0->field_db = r1
    //     0xaf113c: stur            w1, [x0, #0xdb]
    // 0xaf1140: r1 = const []
    //     0xaf1140: ldr             x1, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xaf1144: StoreField: r0->field_f3 = r1
    //     0xaf1144: stur            w1, [x0, #0xf3]
    // 0xaf1148: r1 = Instance_Clip
    //     0xaf1148: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xaf114c: ldr             x1, [x1, #0x7c0]
    // 0xaf1150: StoreField: r0->field_f7 = r1
    //     0xaf1150: stur            w1, [x0, #0xf7]
    // 0xaf1154: r1 = true
    //     0xaf1154: add             x1, NULL, #0x20  ; true
    // 0xaf1158: StoreField: r0->field_ff = r1
    //     0xaf1158: stur            w1, [x0, #0xff]
    // 0xaf115c: r17 = 259
    //     0xaf115c: movz            x17, #0x103
    // 0xaf1160: str             w1, [x0, x17]
    // 0xaf1164: r2 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xaf1164: add             x2, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xaf1168: ldr             x2, [x2, #0x4d8]
    // 0xaf116c: r17 = 267
    //     0xaf116c: movz            x17, #0x10b
    // 0xaf1170: str             w2, [x0, x17]
    // 0xaf1174: r17 = 271
    //     0xaf1174: movz            x17, #0x10f
    // 0xaf1178: str             w1, [x0, x17]
    // 0xaf117c: r2 = Instance_SmartDashesType
    //     0xaf117c: ldr             x2, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xaf1180: StoreField: r0->field_53 = r2
    //     0xaf1180: stur            w2, [x0, #0x53]
    // 0xaf1184: r2 = Instance_SmartQuotesType
    //     0xaf1184: add             x2, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xaf1188: ldr             x2, [x2, #0x4e0]
    // 0xaf118c: StoreField: r0->field_57 = r2
    //     0xaf118c: stur            w2, [x0, #0x57]
    // 0xaf1190: r2 = Instance_TextInputType
    //     0xaf1190: add             x2, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xaf1194: ldr             x2, [x2, #0x4e8]
    // 0xaf1198: StoreField: r0->field_1f = r2
    //     0xaf1198: stur            w2, [x0, #0x1f]
    // 0xaf119c: StoreField: r0->field_cb = r1
    //     0xaf119c: stur            w1, [x0, #0xcb]
    // 0xaf11a0: LeaveFrame
    //     0xaf11a0: mov             SP, fp
    //     0xaf11a4: ldp             fp, lr, [SP], #0x10
    // 0xaf11a8: ret
    //     0xaf11a8: ret             
    // 0xaf11ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf11ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf11b0: b               #0xaf0f98
  }
}
