// lib: , url: package:nuonline/app/modules/donation/widgets/zakat_quality_radio_card.dart

// class id: 1050254, size: 0x8
class :: {
}

// class id: 5017, size: 0x1c, field offset: 0xc
//   const constructor, 
class ZakatQualityRadioCard extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb969b4, size: 0x614
    // 0xb969b4: EnterFrame
    //     0xb969b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb969b8: mov             fp, SP
    // 0xb969bc: AllocStack(0x60)
    //     0xb969bc: sub             SP, SP, #0x60
    // 0xb969c0: SetupParameters(ZakatQualityRadioCard this /* r1 => r1, fp-0x8 */)
    //     0xb969c0: stur            x1, [fp, #-8]
    // 0xb969c4: CheckStackOverflow
    //     0xb969c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb969c8: cmp             SP, x16
    //     0xb969cc: b.ls            #0xb96fa8
    // 0xb969d0: r1 = 1
    //     0xb969d0: movz            x1, #0x1
    // 0xb969d4: r0 = AllocateContext()
    //     0xb969d4: bl              #0xec126c  ; AllocateContextStub
    // 0xb969d8: mov             x1, x0
    // 0xb969dc: ldur            x0, [fp, #-8]
    // 0xb969e0: stur            x1, [fp, #-0x20]
    // 0xb969e4: StoreField: r1->field_f = r0
    //     0xb969e4: stur            w0, [x1, #0xf]
    // 0xb969e8: LoadField: r2 = r0->field_b
    //     0xb969e8: ldur            w2, [x0, #0xb]
    // 0xb969ec: DecompressPointer r2
    //     0xb969ec: add             x2, x2, HEAP, lsl #32
    // 0xb969f0: stur            x2, [fp, #-0x18]
    // 0xb969f4: LoadField: r3 = r0->field_f
    //     0xb969f4: ldur            w3, [x0, #0xf]
    // 0xb969f8: DecompressPointer r3
    //     0xb969f8: add             x3, x3, HEAP, lsl #32
    // 0xb969fc: stur            x3, [fp, #-0x10]
    // 0xb96a00: stp             x3, x2, [SP]
    // 0xb96a04: r0 = ==()
    //     0xb96a04: bl              #0xd3f134  ; [package:equatable/src/equatable.dart] Equatable::==
    // 0xb96a08: r1 = _ConstMap len:10
    //     0xb96a08: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb96a0c: ldr             x1, [x1, #0xc08]
    // 0xb96a10: r2 = 100
    //     0xb96a10: movz            x2, #0x64
    // 0xb96a14: stur            x0, [fp, #-0x28]
    // 0xb96a18: r0 = []()
    //     0xb96a18: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96a1c: r1 = _ConstMap len:10
    //     0xb96a1c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb96a20: ldr             x1, [x1, #0xc08]
    // 0xb96a24: r2 = 1800
    //     0xb96a24: movz            x2, #0x708
    // 0xb96a28: stur            x0, [fp, #-0x30]
    // 0xb96a2c: r0 = []()
    //     0xb96a2c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96a30: r16 = <Color?>
    //     0xb96a30: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb96a34: ldr             x16, [x16, #0x98]
    // 0xb96a38: stp             x0, x16, [SP, #8]
    // 0xb96a3c: ldur            x16, [fp, #-0x30]
    // 0xb96a40: str             x16, [SP]
    // 0xb96a44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb96a44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb96a48: r0 = mode()
    //     0xb96a48: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb96a4c: mov             x1, x0
    // 0xb96a50: ldur            x0, [fp, #-0x28]
    // 0xb96a54: tbnz            w0, #4, #0xb96a60
    // 0xb96a58: mov             x0, x1
    // 0xb96a5c: b               #0xb96a64
    // 0xb96a60: r0 = Null
    //     0xb96a60: mov             x0, NULL
    // 0xb96a64: stur            x0, [fp, #-0x28]
    // 0xb96a68: r0 = Radius()
    //     0xb96a68: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb96a6c: d0 = 8.000000
    //     0xb96a6c: fmov            d0, #8.00000000
    // 0xb96a70: stur            x0, [fp, #-0x30]
    // 0xb96a74: StoreField: r0->field_7 = d0
    //     0xb96a74: stur            d0, [x0, #7]
    // 0xb96a78: StoreField: r0->field_f = d0
    //     0xb96a78: stur            d0, [x0, #0xf]
    // 0xb96a7c: r0 = BorderRadius()
    //     0xb96a7c: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb96a80: mov             x1, x0
    // 0xb96a84: ldur            x0, [fp, #-0x30]
    // 0xb96a88: stur            x1, [fp, #-0x38]
    // 0xb96a8c: StoreField: r1->field_7 = r0
    //     0xb96a8c: stur            w0, [x1, #7]
    // 0xb96a90: StoreField: r1->field_b = r0
    //     0xb96a90: stur            w0, [x1, #0xb]
    // 0xb96a94: StoreField: r1->field_f = r0
    //     0xb96a94: stur            w0, [x1, #0xf]
    // 0xb96a98: StoreField: r1->field_13 = r0
    //     0xb96a98: stur            w0, [x1, #0x13]
    // 0xb96a9c: ldur            x16, [fp, #-0x18]
    // 0xb96aa0: ldur            lr, [fp, #-0x10]
    // 0xb96aa4: stp             lr, x16, [SP]
    // 0xb96aa8: r0 = ==()
    //     0xb96aa8: bl              #0xd3f134  ; [package:equatable/src/equatable.dart] Equatable::==
    // 0xb96aac: r1 = _ConstMap len:10
    //     0xb96aac: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb96ab0: ldr             x1, [x1, #0xc08]
    // 0xb96ab4: r2 = 600
    //     0xb96ab4: movz            x2, #0x258
    // 0xb96ab8: stur            x0, [fp, #-0x10]
    // 0xb96abc: r0 = []()
    //     0xb96abc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96ac0: cmp             w0, NULL
    // 0xb96ac4: b.eq            #0xb96fb0
    // 0xb96ac8: r16 = <Color>
    //     0xb96ac8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb96acc: ldr             x16, [x16, #0x158]
    // 0xb96ad0: stp             x0, x16, [SP, #8]
    // 0xb96ad4: r16 = Instance_MaterialColor
    //     0xb96ad4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb96ad8: ldr             x16, [x16, #0xcf0]
    // 0xb96adc: str             x16, [SP]
    // 0xb96ae0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb96ae0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb96ae4: r0 = mode()
    //     0xb96ae4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb96ae8: r1 = _ConstMap len:6
    //     0xb96ae8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb96aec: ldr             x1, [x1, #0xc20]
    // 0xb96af0: r2 = 6
    //     0xb96af0: movz            x2, #0x6
    // 0xb96af4: stur            x0, [fp, #-0x30]
    // 0xb96af8: r0 = []()
    //     0xb96af8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96afc: stur            x0, [fp, #-0x40]
    // 0xb96b00: cmp             w0, NULL
    // 0xb96b04: b.eq            #0xb96fb4
    // 0xb96b08: r1 = _ConstMap len:6
    //     0xb96b08: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb96b0c: ldr             x1, [x1, #0xc20]
    // 0xb96b10: r2 = 8
    //     0xb96b10: movz            x2, #0x8
    // 0xb96b14: r0 = []()
    //     0xb96b14: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb96b18: cmp             w0, NULL
    // 0xb96b1c: b.eq            #0xb96fb8
    // 0xb96b20: r16 = <Color>
    //     0xb96b20: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb96b24: ldr             x16, [x16, #0x158]
    // 0xb96b28: stp             x0, x16, [SP, #8]
    // 0xb96b2c: ldur            x16, [fp, #-0x40]
    // 0xb96b30: str             x16, [SP]
    // 0xb96b34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb96b34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb96b38: r0 = mode()
    //     0xb96b38: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb96b3c: mov             x1, x0
    // 0xb96b40: ldur            x0, [fp, #-0x10]
    // 0xb96b44: tbnz            w0, #4, #0xb96b4c
    // 0xb96b48: ldur            x1, [fp, #-0x30]
    // 0xb96b4c: ldur            x3, [fp, #-8]
    // 0xb96b50: ldur            x0, [fp, #-0x38]
    // 0xb96b54: ldur            x2, [fp, #-0x28]
    // 0xb96b58: str             x1, [SP]
    // 0xb96b5c: r1 = Null
    //     0xb96b5c: mov             x1, NULL
    // 0xb96b60: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb96b60: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb96b64: ldr             x4, [x4, #0x228]
    // 0xb96b68: r0 = Border.all()
    //     0xb96b68: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb96b6c: stur            x0, [fp, #-0x10]
    // 0xb96b70: r0 = BoxDecoration()
    //     0xb96b70: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb96b74: mov             x3, x0
    // 0xb96b78: ldur            x0, [fp, #-0x28]
    // 0xb96b7c: stur            x3, [fp, #-0x30]
    // 0xb96b80: StoreField: r3->field_7 = r0
    //     0xb96b80: stur            w0, [x3, #7]
    // 0xb96b84: ldur            x0, [fp, #-0x10]
    // 0xb96b88: StoreField: r3->field_f = r0
    //     0xb96b88: stur            w0, [x3, #0xf]
    // 0xb96b8c: ldur            x0, [fp, #-0x38]
    // 0xb96b90: StoreField: r3->field_13 = r0
    //     0xb96b90: stur            w0, [x3, #0x13]
    // 0xb96b94: r0 = Instance_BoxShape
    //     0xb96b94: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb96b98: ldr             x0, [x0, #0xca8]
    // 0xb96b9c: StoreField: r3->field_23 = r0
    //     0xb96b9c: stur            w0, [x3, #0x23]
    // 0xb96ba0: r1 = <Widget>
    //     0xb96ba0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb96ba4: r2 = 0
    //     0xb96ba4: movz            x2, #0
    // 0xb96ba8: r0 = _GrowableList()
    //     0xb96ba8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb96bac: mov             x1, x0
    // 0xb96bb0: ldur            x0, [fp, #-8]
    // 0xb96bb4: stur            x1, [fp, #-0x10]
    // 0xb96bb8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb96bb8: ldur            w2, [x0, #0x17]
    // 0xb96bbc: DecompressPointer r2
    //     0xb96bbc: add             x2, x2, HEAP, lsl #32
    // 0xb96bc0: tbnz            w2, #4, #0xb96cb8
    // 0xb96bc4: ldur            x0, [fp, #-0x18]
    // 0xb96bc8: LoadField: r2 = r0->field_7
    //     0xb96bc8: ldur            w2, [x0, #7]
    // 0xb96bcc: DecompressPointer r2
    //     0xb96bcc: add             x2, x2, HEAP, lsl #32
    // 0xb96bd0: stur            x2, [fp, #-8]
    // 0xb96bd4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb96bd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb96bd8: ldr             x0, [x0, #0x2670]
    //     0xb96bdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb96be0: cmp             w0, w16
    //     0xb96be4: b.ne            #0xb96bf0
    //     0xb96be8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb96bec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb96bf0: r0 = GetNavigation.textTheme()
    //     0xb96bf0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb96bf4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb96bf4: ldur            w1, [x0, #0x17]
    // 0xb96bf8: DecompressPointer r1
    //     0xb96bf8: add             x1, x1, HEAP, lsl #32
    // 0xb96bfc: cmp             w1, NULL
    // 0xb96c00: b.eq            #0xb96fbc
    // 0xb96c04: r16 = Instance_FontWeight
    //     0xb96c04: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb96c08: ldr             x16, [x16, #0xe20]
    // 0xb96c0c: str             x16, [SP]
    // 0xb96c10: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb96c10: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb96c14: ldr             x4, [x4, #0xfe0]
    // 0xb96c18: r0 = copyWith()
    //     0xb96c18: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb96c1c: stur            x0, [fp, #-0x28]
    // 0xb96c20: r0 = Text()
    //     0xb96c20: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb96c24: mov             x2, x0
    // 0xb96c28: ldur            x0, [fp, #-8]
    // 0xb96c2c: stur            x2, [fp, #-0x38]
    // 0xb96c30: StoreField: r2->field_b = r0
    //     0xb96c30: stur            w0, [x2, #0xb]
    // 0xb96c34: ldur            x0, [fp, #-0x28]
    // 0xb96c38: StoreField: r2->field_13 = r0
    //     0xb96c38: stur            w0, [x2, #0x13]
    // 0xb96c3c: ldur            x0, [fp, #-0x10]
    // 0xb96c40: LoadField: r1 = r0->field_b
    //     0xb96c40: ldur            w1, [x0, #0xb]
    // 0xb96c44: LoadField: r3 = r0->field_f
    //     0xb96c44: ldur            w3, [x0, #0xf]
    // 0xb96c48: DecompressPointer r3
    //     0xb96c48: add             x3, x3, HEAP, lsl #32
    // 0xb96c4c: LoadField: r4 = r3->field_b
    //     0xb96c4c: ldur            w4, [x3, #0xb]
    // 0xb96c50: r3 = LoadInt32Instr(r1)
    //     0xb96c50: sbfx            x3, x1, #1, #0x1f
    // 0xb96c54: stur            x3, [fp, #-0x48]
    // 0xb96c58: r1 = LoadInt32Instr(r4)
    //     0xb96c58: sbfx            x1, x4, #1, #0x1f
    // 0xb96c5c: cmp             x3, x1
    // 0xb96c60: b.ne            #0xb96c6c
    // 0xb96c64: mov             x1, x0
    // 0xb96c68: r0 = _growToNextCapacity()
    //     0xb96c68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96c6c: ldur            x2, [fp, #-0x10]
    // 0xb96c70: ldur            x3, [fp, #-0x48]
    // 0xb96c74: add             x0, x3, #1
    // 0xb96c78: lsl             x1, x0, #1
    // 0xb96c7c: StoreField: r2->field_b = r1
    //     0xb96c7c: stur            w1, [x2, #0xb]
    // 0xb96c80: LoadField: r1 = r2->field_f
    //     0xb96c80: ldur            w1, [x2, #0xf]
    // 0xb96c84: DecompressPointer r1
    //     0xb96c84: add             x1, x1, HEAP, lsl #32
    // 0xb96c88: ldur            x0, [fp, #-0x38]
    // 0xb96c8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb96c8c: add             x25, x1, x3, lsl #2
    //     0xb96c90: add             x25, x25, #0xf
    //     0xb96c94: str             w0, [x25]
    //     0xb96c98: tbz             w0, #0, #0xb96cb4
    //     0xb96c9c: ldurb           w16, [x1, #-1]
    //     0xb96ca0: ldurb           w17, [x0, #-1]
    //     0xb96ca4: and             x16, x17, x16, lsr #2
    //     0xb96ca8: tst             x16, HEAP, lsr #32
    //     0xb96cac: b.eq            #0xb96cb4
    //     0xb96cb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb96cb4: b               #0xb96cbc
    // 0xb96cb8: mov             x2, x1
    // 0xb96cbc: ldur            x0, [fp, #-0x18]
    // 0xb96cc0: LoadField: r1 = r0->field_b
    //     0xb96cc0: ldur            x1, [x0, #0xb]
    // 0xb96cc4: r0 = IntExtension.idr()
    //     0xb96cc4: bl              #0xaeb5d4  ; [package:nuonline/common/extensions/int_extension.dart] ::IntExtension.idr
    // 0xb96cc8: stur            x0, [fp, #-8]
    // 0xb96ccc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb96ccc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb96cd0: ldr             x0, [x0, #0x2670]
    //     0xb96cd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb96cd8: cmp             w0, w16
    //     0xb96cdc: b.ne            #0xb96ce8
    //     0xb96ce0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb96ce4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb96ce8: r0 = GetNavigation.textTheme()
    //     0xb96ce8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb96cec: LoadField: r1 = r0->field_f
    //     0xb96cec: ldur            w1, [x0, #0xf]
    // 0xb96cf0: DecompressPointer r1
    //     0xb96cf0: add             x1, x1, HEAP, lsl #32
    // 0xb96cf4: stur            x1, [fp, #-0x18]
    // 0xb96cf8: cmp             w1, NULL
    // 0xb96cfc: b.eq            #0xb96fc0
    // 0xb96d00: r0 = GetNavigation.theme()
    //     0xb96d00: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb96d04: LoadField: r1 = r0->field_3f
    //     0xb96d04: ldur            w1, [x0, #0x3f]
    // 0xb96d08: DecompressPointer r1
    //     0xb96d08: add             x1, x1, HEAP, lsl #32
    // 0xb96d0c: LoadField: r0 = r1->field_2b
    //     0xb96d0c: ldur            w0, [x1, #0x2b]
    // 0xb96d10: DecompressPointer r0
    //     0xb96d10: add             x0, x0, HEAP, lsl #32
    // 0xb96d14: r16 = 24.000000
    //     0xb96d14: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xb96d18: ldr             x16, [x16, #0x368]
    // 0xb96d1c: stp             x0, x16, [SP]
    // 0xb96d20: ldur            x1, [fp, #-0x18]
    // 0xb96d24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb96d24: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb96d28: ldr             x4, [x4, #0xaa0]
    // 0xb96d2c: r0 = copyWith()
    //     0xb96d2c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb96d30: stur            x0, [fp, #-0x18]
    // 0xb96d34: r0 = Text()
    //     0xb96d34: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb96d38: mov             x2, x0
    // 0xb96d3c: ldur            x0, [fp, #-8]
    // 0xb96d40: stur            x2, [fp, #-0x28]
    // 0xb96d44: StoreField: r2->field_b = r0
    //     0xb96d44: stur            w0, [x2, #0xb]
    // 0xb96d48: ldur            x0, [fp, #-0x18]
    // 0xb96d4c: StoreField: r2->field_13 = r0
    //     0xb96d4c: stur            w0, [x2, #0x13]
    // 0xb96d50: ldur            x0, [fp, #-0x10]
    // 0xb96d54: LoadField: r1 = r0->field_b
    //     0xb96d54: ldur            w1, [x0, #0xb]
    // 0xb96d58: LoadField: r3 = r0->field_f
    //     0xb96d58: ldur            w3, [x0, #0xf]
    // 0xb96d5c: DecompressPointer r3
    //     0xb96d5c: add             x3, x3, HEAP, lsl #32
    // 0xb96d60: LoadField: r4 = r3->field_b
    //     0xb96d60: ldur            w4, [x3, #0xb]
    // 0xb96d64: r3 = LoadInt32Instr(r1)
    //     0xb96d64: sbfx            x3, x1, #1, #0x1f
    // 0xb96d68: stur            x3, [fp, #-0x48]
    // 0xb96d6c: r1 = LoadInt32Instr(r4)
    //     0xb96d6c: sbfx            x1, x4, #1, #0x1f
    // 0xb96d70: cmp             x3, x1
    // 0xb96d74: b.ne            #0xb96d80
    // 0xb96d78: mov             x1, x0
    // 0xb96d7c: r0 = _growToNextCapacity()
    //     0xb96d7c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96d80: ldur            x2, [fp, #-0x10]
    // 0xb96d84: ldur            x3, [fp, #-0x48]
    // 0xb96d88: add             x0, x3, #1
    // 0xb96d8c: lsl             x1, x0, #1
    // 0xb96d90: StoreField: r2->field_b = r1
    //     0xb96d90: stur            w1, [x2, #0xb]
    // 0xb96d94: LoadField: r1 = r2->field_f
    //     0xb96d94: ldur            w1, [x2, #0xf]
    // 0xb96d98: DecompressPointer r1
    //     0xb96d98: add             x1, x1, HEAP, lsl #32
    // 0xb96d9c: ldur            x0, [fp, #-0x28]
    // 0xb96da0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb96da0: add             x25, x1, x3, lsl #2
    //     0xb96da4: add             x25, x25, #0xf
    //     0xb96da8: str             w0, [x25]
    //     0xb96dac: tbz             w0, #0, #0xb96dc8
    //     0xb96db0: ldurb           w16, [x1, #-1]
    //     0xb96db4: ldurb           w17, [x0, #-1]
    //     0xb96db8: and             x16, x17, x16, lsr #2
    //     0xb96dbc: tst             x16, HEAP, lsr #32
    //     0xb96dc0: b.eq            #0xb96dc8
    //     0xb96dc4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb96dc8: r0 = GetNavigation.textTheme()
    //     0xb96dc8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb96dcc: LoadField: r1 = r0->field_23
    //     0xb96dcc: ldur            w1, [x0, #0x23]
    // 0xb96dd0: DecompressPointer r1
    //     0xb96dd0: add             x1, x1, HEAP, lsl #32
    // 0xb96dd4: cmp             w1, NULL
    // 0xb96dd8: b.eq            #0xb96fc4
    // 0xb96ddc: r16 = 14.000000
    //     0xb96ddc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb96de0: ldr             x16, [x16, #0x9a0]
    // 0xb96de4: str             x16, [SP]
    // 0xb96de8: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb96de8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb96dec: ldr             x4, [x4, #0x88]
    // 0xb96df0: r0 = copyWith()
    //     0xb96df0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb96df4: stur            x0, [fp, #-8]
    // 0xb96df8: r0 = Text()
    //     0xb96df8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb96dfc: mov             x2, x0
    // 0xb96e00: r0 = "Per Orang"
    //     0xb96e00: add             x0, PP, #0x40, lsl #12  ; [pp+0x40140] "Per Orang"
    //     0xb96e04: ldr             x0, [x0, #0x140]
    // 0xb96e08: stur            x2, [fp, #-0x18]
    // 0xb96e0c: StoreField: r2->field_b = r0
    //     0xb96e0c: stur            w0, [x2, #0xb]
    // 0xb96e10: ldur            x0, [fp, #-8]
    // 0xb96e14: StoreField: r2->field_13 = r0
    //     0xb96e14: stur            w0, [x2, #0x13]
    // 0xb96e18: ldur            x0, [fp, #-0x10]
    // 0xb96e1c: LoadField: r1 = r0->field_b
    //     0xb96e1c: ldur            w1, [x0, #0xb]
    // 0xb96e20: LoadField: r3 = r0->field_f
    //     0xb96e20: ldur            w3, [x0, #0xf]
    // 0xb96e24: DecompressPointer r3
    //     0xb96e24: add             x3, x3, HEAP, lsl #32
    // 0xb96e28: LoadField: r4 = r3->field_b
    //     0xb96e28: ldur            w4, [x3, #0xb]
    // 0xb96e2c: r3 = LoadInt32Instr(r1)
    //     0xb96e2c: sbfx            x3, x1, #1, #0x1f
    // 0xb96e30: stur            x3, [fp, #-0x48]
    // 0xb96e34: r1 = LoadInt32Instr(r4)
    //     0xb96e34: sbfx            x1, x4, #1, #0x1f
    // 0xb96e38: cmp             x3, x1
    // 0xb96e3c: b.ne            #0xb96e48
    // 0xb96e40: mov             x1, x0
    // 0xb96e44: r0 = _growToNextCapacity()
    //     0xb96e44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96e48: ldur            x2, [fp, #-0x10]
    // 0xb96e4c: ldur            x3, [fp, #-0x48]
    // 0xb96e50: add             x0, x3, #1
    // 0xb96e54: lsl             x1, x0, #1
    // 0xb96e58: StoreField: r2->field_b = r1
    //     0xb96e58: stur            w1, [x2, #0xb]
    // 0xb96e5c: LoadField: r1 = r2->field_f
    //     0xb96e5c: ldur            w1, [x2, #0xf]
    // 0xb96e60: DecompressPointer r1
    //     0xb96e60: add             x1, x1, HEAP, lsl #32
    // 0xb96e64: ldur            x0, [fp, #-0x18]
    // 0xb96e68: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb96e68: add             x25, x1, x3, lsl #2
    //     0xb96e6c: add             x25, x25, #0xf
    //     0xb96e70: str             w0, [x25]
    //     0xb96e74: tbz             w0, #0, #0xb96e90
    //     0xb96e78: ldurb           w16, [x1, #-1]
    //     0xb96e7c: ldurb           w17, [x0, #-1]
    //     0xb96e80: and             x16, x17, x16, lsr #2
    //     0xb96e84: tst             x16, HEAP, lsr #32
    //     0xb96e88: b.eq            #0xb96e90
    //     0xb96e8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb96e90: r0 = Column()
    //     0xb96e90: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb96e94: mov             x1, x0
    // 0xb96e98: r0 = Instance_Axis
    //     0xb96e98: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb96e9c: stur            x1, [fp, #-8]
    // 0xb96ea0: StoreField: r1->field_f = r0
    //     0xb96ea0: stur            w0, [x1, #0xf]
    // 0xb96ea4: r0 = Instance_MainAxisAlignment
    //     0xb96ea4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb96ea8: ldr             x0, [x0, #0x730]
    // 0xb96eac: StoreField: r1->field_13 = r0
    //     0xb96eac: stur            w0, [x1, #0x13]
    // 0xb96eb0: r0 = Instance_MainAxisSize
    //     0xb96eb0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb96eb4: ldr             x0, [x0, #0x738]
    // 0xb96eb8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb96eb8: stur            w0, [x1, #0x17]
    // 0xb96ebc: r0 = Instance_CrossAxisAlignment
    //     0xb96ebc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb96ec0: ldr             x0, [x0, #0x740]
    // 0xb96ec4: StoreField: r1->field_1b = r0
    //     0xb96ec4: stur            w0, [x1, #0x1b]
    // 0xb96ec8: r0 = Instance_VerticalDirection
    //     0xb96ec8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb96ecc: ldr             x0, [x0, #0x748]
    // 0xb96ed0: StoreField: r1->field_23 = r0
    //     0xb96ed0: stur            w0, [x1, #0x23]
    // 0xb96ed4: r0 = Instance_Clip
    //     0xb96ed4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb96ed8: ldr             x0, [x0, #0x750]
    // 0xb96edc: StoreField: r1->field_2b = r0
    //     0xb96edc: stur            w0, [x1, #0x2b]
    // 0xb96ee0: StoreField: r1->field_2f = rZR
    //     0xb96ee0: stur            xzr, [x1, #0x2f]
    // 0xb96ee4: ldur            x0, [fp, #-0x10]
    // 0xb96ee8: StoreField: r1->field_b = r0
    //     0xb96ee8: stur            w0, [x1, #0xb]
    // 0xb96eec: r0 = Container()
    //     0xb96eec: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb96ef0: stur            x0, [fp, #-0x10]
    // 0xb96ef4: r16 = Instance_EdgeInsets
    //     0xb96ef4: add             x16, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xb96ef8: ldr             x16, [x16, #0xde8]
    // 0xb96efc: ldur            lr, [fp, #-0x30]
    // 0xb96f00: stp             lr, x16, [SP, #8]
    // 0xb96f04: ldur            x16, [fp, #-8]
    // 0xb96f08: str             x16, [SP]
    // 0xb96f0c: mov             x1, x0
    // 0xb96f10: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb96f10: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb96f14: ldr             x4, [x4, #0xe8]
    // 0xb96f18: r0 = Container()
    //     0xb96f18: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb96f1c: r0 = InkWell()
    //     0xb96f1c: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb96f20: mov             x3, x0
    // 0xb96f24: ldur            x0, [fp, #-0x10]
    // 0xb96f28: stur            x3, [fp, #-8]
    // 0xb96f2c: StoreField: r3->field_b = r0
    //     0xb96f2c: stur            w0, [x3, #0xb]
    // 0xb96f30: ldur            x2, [fp, #-0x20]
    // 0xb96f34: r1 = Function '<anonymous closure>':.
    //     0xb96f34: add             x1, PP, #0x40, lsl #12  ; [pp+0x40148] AnonymousClosure: (0xb96fc8), in [package:nuonline/app/modules/donation/widgets/zakat_quality_radio_card.dart] ZakatQualityRadioCard::build (0xb969b4)
    //     0xb96f38: ldr             x1, [x1, #0x148]
    // 0xb96f3c: r0 = AllocateClosure()
    //     0xb96f3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb96f40: mov             x1, x0
    // 0xb96f44: ldur            x0, [fp, #-8]
    // 0xb96f48: StoreField: r0->field_f = r1
    //     0xb96f48: stur            w1, [x0, #0xf]
    // 0xb96f4c: r1 = true
    //     0xb96f4c: add             x1, NULL, #0x20  ; true
    // 0xb96f50: StoreField: r0->field_43 = r1
    //     0xb96f50: stur            w1, [x0, #0x43]
    // 0xb96f54: r2 = Instance_BoxShape
    //     0xb96f54: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb96f58: ldr             x2, [x2, #0xca8]
    // 0xb96f5c: StoreField: r0->field_47 = r2
    //     0xb96f5c: stur            w2, [x0, #0x47]
    // 0xb96f60: StoreField: r0->field_6f = r1
    //     0xb96f60: stur            w1, [x0, #0x6f]
    // 0xb96f64: r2 = false
    //     0xb96f64: add             x2, NULL, #0x30  ; false
    // 0xb96f68: StoreField: r0->field_73 = r2
    //     0xb96f68: stur            w2, [x0, #0x73]
    // 0xb96f6c: StoreField: r0->field_83 = r1
    //     0xb96f6c: stur            w1, [x0, #0x83]
    // 0xb96f70: StoreField: r0->field_7b = r2
    //     0xb96f70: stur            w2, [x0, #0x7b]
    // 0xb96f74: r1 = <FlexParentData>
    //     0xb96f74: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb96f78: ldr             x1, [x1, #0x720]
    // 0xb96f7c: r0 = Expanded()
    //     0xb96f7c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb96f80: r1 = 1
    //     0xb96f80: movz            x1, #0x1
    // 0xb96f84: StoreField: r0->field_13 = r1
    //     0xb96f84: stur            x1, [x0, #0x13]
    // 0xb96f88: r1 = Instance_FlexFit
    //     0xb96f88: add             x1, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb96f8c: ldr             x1, [x1, #0x728]
    // 0xb96f90: StoreField: r0->field_1b = r1
    //     0xb96f90: stur            w1, [x0, #0x1b]
    // 0xb96f94: ldur            x1, [fp, #-8]
    // 0xb96f98: StoreField: r0->field_b = r1
    //     0xb96f98: stur            w1, [x0, #0xb]
    // 0xb96f9c: LeaveFrame
    //     0xb96f9c: mov             SP, fp
    //     0xb96fa0: ldp             fp, lr, [SP], #0x10
    // 0xb96fa4: ret
    //     0xb96fa4: ret             
    // 0xb96fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb96fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb96fac: b               #0xb969d0
    // 0xb96fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96fc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96fc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96fc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb96fc8, size: 0x68
    // 0xb96fc8: EnterFrame
    //     0xb96fc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb96fcc: mov             fp, SP
    // 0xb96fd0: AllocStack(0x8)
    //     0xb96fd0: sub             SP, SP, #8
    // 0xb96fd4: SetupParameters()
    //     0xb96fd4: ldr             x0, [fp, #0x10]
    //     0xb96fd8: ldur            w1, [x0, #0x17]
    //     0xb96fdc: add             x1, x1, HEAP, lsl #32
    // 0xb96fe0: CheckStackOverflow
    //     0xb96fe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb96fe4: cmp             SP, x16
    //     0xb96fe8: b.ls            #0xb97028
    // 0xb96fec: LoadField: r0 = r1->field_f
    //     0xb96fec: ldur            w0, [x1, #0xf]
    // 0xb96ff0: DecompressPointer r0
    //     0xb96ff0: add             x0, x0, HEAP, lsl #32
    // 0xb96ff4: LoadField: r1 = r0->field_b
    //     0xb96ff4: ldur            w1, [x0, #0xb]
    // 0xb96ff8: DecompressPointer r1
    //     0xb96ff8: add             x1, x1, HEAP, lsl #32
    // 0xb96ffc: LoadField: r2 = r0->field_13
    //     0xb96ffc: ldur            w2, [x0, #0x13]
    // 0xb97000: DecompressPointer r2
    //     0xb97000: add             x2, x2, HEAP, lsl #32
    // 0xb97004: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb97004: ldur            w0, [x2, #0x17]
    // 0xb97008: DecompressPointer r0
    //     0xb97008: add             x0, x0, HEAP, lsl #32
    // 0xb9700c: str             x1, [SP]
    // 0xb97010: mov             x1, x0
    // 0xb97014: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb97014: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb97018: r0 = call()
    //     0xb97018: bl              #0x8a9554  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call
    // 0xb9701c: LeaveFrame
    //     0xb9701c: mov             SP, fp
    //     0xb97020: ldp             fp, lr, [SP], #0x10
    // 0xb97024: ret
    //     0xb97024: ret             
    // 0xb97028: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97028: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9702c: b               #0xb96fec
  }
}
