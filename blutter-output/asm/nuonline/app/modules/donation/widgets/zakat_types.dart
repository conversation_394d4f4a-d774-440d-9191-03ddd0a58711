// lib: , url: package:nuonline/app/modules/donation/widgets/zakat_types.dart

// class id: 1050255, size: 0x8
class :: {
}

// class id: 5016, size: 0x14, field offset: 0xc
//   const constructor, 
class ZakatTypesWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb97030, size: 0x15c
    // 0xb97030: EnterFrame
    //     0xb97030: stp             fp, lr, [SP, #-0x10]!
    //     0xb97034: mov             fp, SP
    // 0xb97038: AllocStack(0x30)
    //     0xb97038: sub             SP, SP, #0x30
    // 0xb9703c: SetupParameters(ZakatTypesWidget this /* r1 => r1, fp-0x8 */)
    //     0xb9703c: stur            x1, [fp, #-8]
    // 0xb97040: CheckStackOverflow
    //     0xb97040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97044: cmp             SP, x16
    //     0xb97048: b.ls            #0xb97184
    // 0xb9704c: r1 = 1
    //     0xb9704c: movz            x1, #0x1
    // 0xb97050: r0 = AllocateContext()
    //     0xb97050: bl              #0xec126c  ; AllocateContextStub
    // 0xb97054: mov             x1, x0
    // 0xb97058: ldur            x0, [fp, #-8]
    // 0xb9705c: stur            x1, [fp, #-0x10]
    // 0xb97060: StoreField: r1->field_f = r0
    //     0xb97060: stur            w0, [x1, #0xf]
    // 0xb97064: r0 = AppBar()
    //     0xb97064: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb97068: stur            x0, [fp, #-8]
    // 0xb9706c: r16 = Instance_Text
    //     0xb9706c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40340] Obj!Text@e23b71
    //     0xb97070: ldr             x16, [x16, #0x340]
    // 0xb97074: str             x16, [SP]
    // 0xb97078: mov             x1, x0
    // 0xb9707c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xb9707c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xb97080: ldr             x4, [x4, #0x6e8]
    // 0xb97084: r0 = AppBar()
    //     0xb97084: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb97088: r1 = Function '<anonymous closure>':.
    //     0xb97088: add             x1, PP, #0x40, lsl #12  ; [pp+0x40348] AnonymousClosure: (0xb97610), in [package:nuonline/app/modules/donation/widgets/zakat_types.dart] ZakatTypesWidget::build (0xb97030)
    //     0xb9708c: ldr             x1, [x1, #0x348]
    // 0xb97090: r2 = Null
    //     0xb97090: mov             x2, NULL
    // 0xb97094: r0 = AllocateClosure()
    //     0xb97094: bl              #0xec1630  ; AllocateClosureStub
    // 0xb97098: mov             x2, x0
    // 0xb9709c: r1 = const [Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes', Instance of 'ZakatTypes']
    //     0xb9709c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40350] List<ZakatTypes>(13)
    //     0xb970a0: ldr             x1, [x1, #0x350]
    // 0xb970a4: r0 = where()
    //     0xb970a4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xb970a8: ldur            x2, [fp, #-0x10]
    // 0xb970ac: r1 = Function '<anonymous closure>':.
    //     0xb970ac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40358] AnonymousClosure: (0xb9718c), in [package:nuonline/app/modules/donation/widgets/zakat_types.dart] ZakatTypesWidget::build (0xb97030)
    //     0xb970b0: ldr             x1, [x1, #0x358]
    // 0xb970b4: stur            x0, [fp, #-0x10]
    // 0xb970b8: r0 = AllocateClosure()
    //     0xb970b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb970bc: r16 = <RenderObjectWidget>
    //     0xb970bc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40360] TypeArguments: <RenderObjectWidget>
    //     0xb970c0: ldr             x16, [x16, #0x360]
    // 0xb970c4: ldur            lr, [fp, #-0x10]
    // 0xb970c8: stp             lr, x16, [SP, #8]
    // 0xb970cc: str             x0, [SP]
    // 0xb970d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb970d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb970d4: r0 = map()
    //     0xb970d4: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0xb970d8: LoadField: r1 = r0->field_7
    //     0xb970d8: ldur            w1, [x0, #7]
    // 0xb970dc: DecompressPointer r1
    //     0xb970dc: add             x1, x1, HEAP, lsl #32
    // 0xb970e0: mov             x2, x0
    // 0xb970e4: r0 = _GrowableList.of()
    //     0xb970e4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb970e8: stur            x0, [fp, #-0x10]
    // 0xb970ec: r0 = ListView()
    //     0xb970ec: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb970f0: stur            x0, [fp, #-0x18]
    // 0xb970f4: r16 = Instance_EdgeInsets
    //     0xb970f4: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb970f8: str             x16, [SP]
    // 0xb970fc: mov             x1, x0
    // 0xb97100: ldur            x2, [fp, #-0x10]
    // 0xb97104: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb97104: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb97108: ldr             x4, [x4, #0xa0]
    // 0xb9710c: r0 = ListView()
    //     0xb9710c: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb97110: r0 = ListTileTheme()
    //     0xb97110: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xb97114: mov             x1, x0
    // 0xb97118: r0 = Instance_EdgeInsets
    //     0xb97118: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb9711c: stur            x1, [fp, #-0x10]
    // 0xb97120: StoreField: r1->field_2b = r0
    //     0xb97120: stur            w0, [x1, #0x2b]
    // 0xb97124: r0 = 0.000000
    //     0xb97124: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb97128: StoreField: r1->field_37 = r0
    //     0xb97128: stur            w0, [x1, #0x37]
    // 0xb9712c: ldur            x0, [fp, #-0x18]
    // 0xb97130: StoreField: r1->field_b = r0
    //     0xb97130: stur            w0, [x1, #0xb]
    // 0xb97134: r0 = Scaffold()
    //     0xb97134: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb97138: ldur            x1, [fp, #-8]
    // 0xb9713c: StoreField: r0->field_13 = r1
    //     0xb9713c: stur            w1, [x0, #0x13]
    // 0xb97140: ldur            x1, [fp, #-0x10]
    // 0xb97144: ArrayStore: r0[0] = r1  ; List_4
    //     0xb97144: stur            w1, [x0, #0x17]
    // 0xb97148: r1 = Instance_AlignmentDirectional
    //     0xb97148: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb9714c: ldr             x1, [x1, #0x758]
    // 0xb97150: StoreField: r0->field_2b = r1
    //     0xb97150: stur            w1, [x0, #0x2b]
    // 0xb97154: r1 = true
    //     0xb97154: add             x1, NULL, #0x20  ; true
    // 0xb97158: StoreField: r0->field_53 = r1
    //     0xb97158: stur            w1, [x0, #0x53]
    // 0xb9715c: r2 = Instance_DragStartBehavior
    //     0xb9715c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb97160: StoreField: r0->field_57 = r2
    //     0xb97160: stur            w2, [x0, #0x57]
    // 0xb97164: r2 = false
    //     0xb97164: add             x2, NULL, #0x30  ; false
    // 0xb97168: StoreField: r0->field_b = r2
    //     0xb97168: stur            w2, [x0, #0xb]
    // 0xb9716c: StoreField: r0->field_f = r2
    //     0xb9716c: stur            w2, [x0, #0xf]
    // 0xb97170: StoreField: r0->field_5f = r1
    //     0xb97170: stur            w1, [x0, #0x5f]
    // 0xb97174: StoreField: r0->field_63 = r1
    //     0xb97174: stur            w1, [x0, #0x63]
    // 0xb97178: LeaveFrame
    //     0xb97178: mov             SP, fp
    //     0xb9717c: ldp             fp, lr, [SP], #0x10
    // 0xb97180: ret
    //     0xb97180: ret             
    // 0xb97184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97184: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97188: b               #0xb9704c
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic, ZakatTypes) {
    // ** addr: 0xb9718c, size: 0x404
    // 0xb9718c: EnterFrame
    //     0xb9718c: stp             fp, lr, [SP, #-0x10]!
    //     0xb97190: mov             fp, SP
    // 0xb97194: AllocStack(0x60)
    //     0xb97194: sub             SP, SP, #0x60
    // 0xb97198: SetupParameters()
    //     0xb97198: ldr             x0, [fp, #0x18]
    //     0xb9719c: ldur            w1, [x0, #0x17]
    //     0xb971a0: add             x1, x1, HEAP, lsl #32
    //     0xb971a4: stur            x1, [fp, #-8]
    // 0xb971a8: CheckStackOverflow
    //     0xb971a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb971ac: cmp             SP, x16
    //     0xb971b0: b.ls            #0xb97584
    // 0xb971b4: r1 = 1
    //     0xb971b4: movz            x1, #0x1
    // 0xb971b8: r0 = AllocateContext()
    //     0xb971b8: bl              #0xec126c  ; AllocateContextStub
    // 0xb971bc: mov             x3, x0
    // 0xb971c0: ldur            x0, [fp, #-8]
    // 0xb971c4: stur            x3, [fp, #-0x10]
    // 0xb971c8: StoreField: r3->field_b = r0
    //     0xb971c8: stur            w0, [x3, #0xb]
    // 0xb971cc: ldr             x1, [fp, #0x10]
    // 0xb971d0: StoreField: r3->field_f = r1
    //     0xb971d0: stur            w1, [x3, #0xf]
    // 0xb971d4: r16 = Instance_ZakatTypes
    //     0xb971d4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24620] Obj!ZakatTypes@e307e1
    //     0xb971d8: ldr             x16, [x16, #0x620]
    // 0xb971dc: cmp             w1, w16
    // 0xb971e0: b.ne            #0xb9720c
    // 0xb971e4: LoadField: r1 = r0->field_f
    //     0xb971e4: ldur            w1, [x0, #0xf]
    // 0xb971e8: DecompressPointer r1
    //     0xb971e8: add             x1, x1, HEAP, lsl #32
    // 0xb971ec: LoadField: r2 = r1->field_f
    //     0xb971ec: ldur            w2, [x1, #0xf]
    // 0xb971f0: DecompressPointer r2
    //     0xb971f0: add             x2, x2, HEAP, lsl #32
    // 0xb971f4: tbz             w2, #4, #0xb9720c
    // 0xb971f8: r0 = Instance_SizedBox
    //     0xb971f8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb971fc: ldr             x0, [x0, #0xc40]
    // 0xb97200: LeaveFrame
    //     0xb97200: mov             SP, fp
    //     0xb97204: ldp             fp, lr, [SP], #0x10
    // 0xb97208: ret
    //     0xb97208: ret             
    // 0xb9720c: r1 = <Widget>
    //     0xb9720c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb97210: r2 = 0
    //     0xb97210: movz            x2, #0
    // 0xb97214: r0 = _GrowableList()
    //     0xb97214: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb97218: mov             x3, x0
    // 0xb9721c: ldur            x0, [fp, #-0x10]
    // 0xb97220: stur            x3, [fp, #-0x18]
    // 0xb97224: LoadField: r1 = r0->field_f
    //     0xb97224: ldur            w1, [x0, #0xf]
    // 0xb97228: DecompressPointer r1
    //     0xb97228: add             x1, x1, HEAP, lsl #32
    // 0xb9722c: r16 = Instance_ZakatTypes
    //     0xb9722c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27130] Obj!ZakatTypes@e30781
    //     0xb97230: ldr             x16, [x16, #0x130]
    // 0xb97234: cmp             w1, w16
    // 0xb97238: b.ne            #0xb973b0
    // 0xb9723c: r1 = _ConstMap len:3
    //     0xb9723c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb97240: ldr             x1, [x1, #0xbe8]
    // 0xb97244: r2 = 2
    //     0xb97244: movz            x2, #0x2
    // 0xb97248: r0 = []()
    //     0xb97248: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb9724c: r16 = <Color?>
    //     0xb9724c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb97250: ldr             x16, [x16, #0x98]
    // 0xb97254: stp             x0, x16, [SP, #8]
    // 0xb97258: r16 = Instance_MaterialColor
    //     0xb97258: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb9725c: ldr             x16, [x16, #0xe38]
    // 0xb97260: str             x16, [SP]
    // 0xb97264: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb97264: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb97268: r0 = mode()
    //     0xb97268: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb9726c: stur            x0, [fp, #-0x20]
    // 0xb97270: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb97270: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb97274: ldr             x0, [x0, #0x2670]
    //     0xb97278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9727c: cmp             w0, w16
    //     0xb97280: b.ne            #0xb9728c
    //     0xb97284: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb97288: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb9728c: r0 = GetNavigation.textTheme()
    //     0xb9728c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb97290: LoadField: r1 = r0->field_23
    //     0xb97290: ldur            w1, [x0, #0x23]
    // 0xb97294: DecompressPointer r1
    //     0xb97294: add             x1, x1, HEAP, lsl #32
    // 0xb97298: cmp             w1, NULL
    // 0xb9729c: b.eq            #0xb9758c
    // 0xb972a0: r16 = Instance_FontWeight
    //     0xb972a0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb972a4: ldr             x16, [x16, #0xe20]
    // 0xb972a8: str             x16, [SP]
    // 0xb972ac: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xb972ac: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xb972b0: ldr             x4, [x4, #0xfe0]
    // 0xb972b4: r0 = copyWith()
    //     0xb972b4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb972b8: stur            x0, [fp, #-0x28]
    // 0xb972bc: r0 = Text()
    //     0xb972bc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb972c0: mov             x1, x0
    // 0xb972c4: r0 = "Zakat Mal"
    //     0xb972c4: add             x0, PP, #0x30, lsl #12  ; [pp+0x302f0] "Zakat Mal"
    //     0xb972c8: ldr             x0, [x0, #0x2f0]
    // 0xb972cc: stur            x1, [fp, #-0x30]
    // 0xb972d0: StoreField: r1->field_b = r0
    //     0xb972d0: stur            w0, [x1, #0xb]
    // 0xb972d4: ldur            x0, [fp, #-0x28]
    // 0xb972d8: StoreField: r1->field_13 = r0
    //     0xb972d8: stur            w0, [x1, #0x13]
    // 0xb972dc: r0 = Center()
    //     0xb972dc: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb972e0: mov             x1, x0
    // 0xb972e4: r0 = Instance_Alignment
    //     0xb972e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb972e8: ldr             x0, [x0, #0x898]
    // 0xb972ec: stur            x1, [fp, #-0x28]
    // 0xb972f0: StoreField: r1->field_f = r0
    //     0xb972f0: stur            w0, [x1, #0xf]
    // 0xb972f4: ldur            x0, [fp, #-0x30]
    // 0xb972f8: StoreField: r1->field_b = r0
    //     0xb972f8: stur            w0, [x1, #0xb]
    // 0xb972fc: r0 = Container()
    //     0xb972fc: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb97300: stur            x0, [fp, #-0x30]
    // 0xb97304: r16 = 36.000000
    //     0xb97304: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb97308: ldr             x16, [x16, #0x478]
    // 0xb9730c: r30 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xb9730c: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xb97310: ldr             lr, [lr, #0xc58]
    // 0xb97314: stp             lr, x16, [SP, #0x10]
    // 0xb97318: ldur            x16, [fp, #-0x20]
    // 0xb9731c: ldur            lr, [fp, #-0x28]
    // 0xb97320: stp             lr, x16, [SP]
    // 0xb97324: mov             x1, x0
    // 0xb97328: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xb97328: add             x4, PP, #0x35, lsl #12  ; [pp+0x35fc0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb9732c: ldr             x4, [x4, #0xfc0]
    // 0xb97330: r0 = Container()
    //     0xb97330: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb97334: ldur            x0, [fp, #-0x18]
    // 0xb97338: LoadField: r1 = r0->field_b
    //     0xb97338: ldur            w1, [x0, #0xb]
    // 0xb9733c: LoadField: r2 = r0->field_f
    //     0xb9733c: ldur            w2, [x0, #0xf]
    // 0xb97340: DecompressPointer r2
    //     0xb97340: add             x2, x2, HEAP, lsl #32
    // 0xb97344: LoadField: r3 = r2->field_b
    //     0xb97344: ldur            w3, [x2, #0xb]
    // 0xb97348: r2 = LoadInt32Instr(r1)
    //     0xb97348: sbfx            x2, x1, #1, #0x1f
    // 0xb9734c: stur            x2, [fp, #-0x38]
    // 0xb97350: r1 = LoadInt32Instr(r3)
    //     0xb97350: sbfx            x1, x3, #1, #0x1f
    // 0xb97354: cmp             x2, x1
    // 0xb97358: b.ne            #0xb97364
    // 0xb9735c: mov             x1, x0
    // 0xb97360: r0 = _growToNextCapacity()
    //     0xb97360: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97364: ldur            x2, [fp, #-0x18]
    // 0xb97368: ldur            x3, [fp, #-0x38]
    // 0xb9736c: add             x0, x3, #1
    // 0xb97370: lsl             x1, x0, #1
    // 0xb97374: StoreField: r2->field_b = r1
    //     0xb97374: stur            w1, [x2, #0xb]
    // 0xb97378: LoadField: r1 = r2->field_f
    //     0xb97378: ldur            w1, [x2, #0xf]
    // 0xb9737c: DecompressPointer r1
    //     0xb9737c: add             x1, x1, HEAP, lsl #32
    // 0xb97380: ldur            x0, [fp, #-0x30]
    // 0xb97384: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97384: add             x25, x1, x3, lsl #2
    //     0xb97388: add             x25, x25, #0xf
    //     0xb9738c: str             w0, [x25]
    //     0xb97390: tbz             w0, #0, #0xb973ac
    //     0xb97394: ldurb           w16, [x1, #-1]
    //     0xb97398: ldurb           w17, [x0, #-1]
    //     0xb9739c: and             x16, x17, x16, lsr #2
    //     0xb973a0: tst             x16, HEAP, lsr #32
    //     0xb973a4: b.eq            #0xb973ac
    //     0xb973a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb973ac: b               #0xb973b4
    // 0xb973b0: mov             x2, x3
    // 0xb973b4: ldur            x1, [fp, #-8]
    // 0xb973b8: ldur            x0, [fp, #-0x10]
    // 0xb973bc: LoadField: r3 = r0->field_f
    //     0xb973bc: ldur            w3, [x0, #0xf]
    // 0xb973c0: DecompressPointer r3
    //     0xb973c0: add             x3, x3, HEAP, lsl #32
    // 0xb973c4: stur            x3, [fp, #-0x20]
    // 0xb973c8: LoadField: r4 = r1->field_f
    //     0xb973c8: ldur            w4, [x1, #0xf]
    // 0xb973cc: DecompressPointer r4
    //     0xb973cc: add             x4, x4, HEAP, lsl #32
    // 0xb973d0: LoadField: r5 = r4->field_b
    //     0xb973d0: ldur            w5, [x4, #0xb]
    // 0xb973d4: DecompressPointer r5
    //     0xb973d4: add             x5, x5, HEAP, lsl #32
    // 0xb973d8: mov             x1, x3
    // 0xb973dc: stur            x5, [fp, #-8]
    // 0xb973e0: r0 = ZakatTypesExtension.title()
    //     0xb973e0: bl              #0xaf11b4  ; [package:nuonline/app/data/enums/zakat_enum.dart] ::ZakatTypesExtension.title
    // 0xb973e4: stur            x0, [fp, #-0x28]
    // 0xb973e8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb973e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb973ec: ldr             x0, [x0, #0x2670]
    //     0xb973f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb973f4: cmp             w0, w16
    //     0xb973f8: b.ne            #0xb97404
    //     0xb973fc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb97400: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb97404: r0 = GetNavigation.textTheme()
    //     0xb97404: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb97408: LoadField: r1 = r0->field_23
    //     0xb97408: ldur            w1, [x0, #0x23]
    // 0xb9740c: DecompressPointer r1
    //     0xb9740c: add             x1, x1, HEAP, lsl #32
    // 0xb97410: stur            x1, [fp, #-0x30]
    // 0xb97414: r0 = Text()
    //     0xb97414: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb97418: mov             x2, x0
    // 0xb9741c: ldur            x0, [fp, #-0x28]
    // 0xb97420: stur            x2, [fp, #-0x40]
    // 0xb97424: StoreField: r2->field_b = r0
    //     0xb97424: stur            w0, [x2, #0xb]
    // 0xb97428: ldur            x0, [fp, #-0x30]
    // 0xb9742c: StoreField: r2->field_13 = r0
    //     0xb9742c: stur            w0, [x2, #0x13]
    // 0xb97430: r1 = <ZakatTypes>
    //     0xb97430: add             x1, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xb97434: ldr             x1, [x1, #0x68]
    // 0xb97438: r0 = RadioListTile()
    //     0xb97438: bl              #0xb430ac  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0x88)
    // 0xb9743c: mov             x3, x0
    // 0xb97440: ldur            x0, [fp, #-0x20]
    // 0xb97444: stur            x3, [fp, #-0x28]
    // 0xb97448: StoreField: r3->field_f = r0
    //     0xb97448: stur            w0, [x3, #0xf]
    // 0xb9744c: ldur            x0, [fp, #-8]
    // 0xb97450: StoreField: r3->field_13 = r0
    //     0xb97450: stur            w0, [x3, #0x13]
    // 0xb97454: ldur            x2, [fp, #-0x10]
    // 0xb97458: r1 = Function '<anonymous closure>':.
    //     0xb97458: add             x1, PP, #0x40, lsl #12  ; [pp+0x40368] AnonymousClosure: (0xb97590), in [package:nuonline/app/modules/donation/widgets/zakat_types.dart] ZakatTypesWidget::build (0xb97030)
    //     0xb9745c: ldr             x1, [x1, #0x368]
    // 0xb97460: r0 = AllocateClosure()
    //     0xb97460: bl              #0xec1630  ; AllocateClosureStub
    // 0xb97464: mov             x1, x0
    // 0xb97468: ldur            x0, [fp, #-0x28]
    // 0xb9746c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb9746c: stur            w1, [x0, #0x17]
    // 0xb97470: r1 = false
    //     0xb97470: add             x1, NULL, #0x30  ; false
    // 0xb97474: StoreField: r0->field_1f = r1
    //     0xb97474: stur            w1, [x0, #0x1f]
    // 0xb97478: ldur            x2, [fp, #-0x40]
    // 0xb9747c: StoreField: r0->field_3b = r2
    //     0xb9747c: stur            w2, [x0, #0x3b]
    // 0xb97480: StoreField: r0->field_47 = r1
    //     0xb97480: stur            w1, [x0, #0x47]
    // 0xb97484: StoreField: r0->field_4f = r1
    //     0xb97484: stur            w1, [x0, #0x4f]
    // 0xb97488: r2 = Instance_ListTileControlAffinity
    //     0xb97488: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2a058] Obj!ListTileControlAffinity@e366c1
    //     0xb9748c: ldr             x2, [x2, #0x58]
    // 0xb97490: StoreField: r0->field_53 = r2
    //     0xb97490: stur            w2, [x0, #0x53]
    // 0xb97494: StoreField: r0->field_57 = r1
    //     0xb97494: stur            w1, [x0, #0x57]
    // 0xb97498: StoreField: r0->field_7f = r1
    //     0xb97498: stur            w1, [x0, #0x7f]
    // 0xb9749c: r2 = Instance__RadioType
    //     0xb9749c: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2a060] Obj!_RadioType@e36461
    //     0xb974a0: ldr             x2, [x2, #0x60]
    // 0xb974a4: StoreField: r0->field_7b = r2
    //     0xb974a4: stur            w2, [x0, #0x7b]
    // 0xb974a8: StoreField: r0->field_83 = r1
    //     0xb974a8: stur            w1, [x0, #0x83]
    // 0xb974ac: ldur            x2, [fp, #-0x18]
    // 0xb974b0: LoadField: r1 = r2->field_b
    //     0xb974b0: ldur            w1, [x2, #0xb]
    // 0xb974b4: LoadField: r3 = r2->field_f
    //     0xb974b4: ldur            w3, [x2, #0xf]
    // 0xb974b8: DecompressPointer r3
    //     0xb974b8: add             x3, x3, HEAP, lsl #32
    // 0xb974bc: LoadField: r4 = r3->field_b
    //     0xb974bc: ldur            w4, [x3, #0xb]
    // 0xb974c0: r3 = LoadInt32Instr(r1)
    //     0xb974c0: sbfx            x3, x1, #1, #0x1f
    // 0xb974c4: stur            x3, [fp, #-0x38]
    // 0xb974c8: r1 = LoadInt32Instr(r4)
    //     0xb974c8: sbfx            x1, x4, #1, #0x1f
    // 0xb974cc: cmp             x3, x1
    // 0xb974d0: b.ne            #0xb974dc
    // 0xb974d4: mov             x1, x2
    // 0xb974d8: r0 = _growToNextCapacity()
    //     0xb974d8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb974dc: ldur            x2, [fp, #-0x18]
    // 0xb974e0: ldur            x3, [fp, #-0x38]
    // 0xb974e4: add             x0, x3, #1
    // 0xb974e8: lsl             x1, x0, #1
    // 0xb974ec: StoreField: r2->field_b = r1
    //     0xb974ec: stur            w1, [x2, #0xb]
    // 0xb974f0: LoadField: r1 = r2->field_f
    //     0xb974f0: ldur            w1, [x2, #0xf]
    // 0xb974f4: DecompressPointer r1
    //     0xb974f4: add             x1, x1, HEAP, lsl #32
    // 0xb974f8: ldur            x0, [fp, #-0x28]
    // 0xb974fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb974fc: add             x25, x1, x3, lsl #2
    //     0xb97500: add             x25, x25, #0xf
    //     0xb97504: str             w0, [x25]
    //     0xb97508: tbz             w0, #0, #0xb97524
    //     0xb9750c: ldurb           w16, [x1, #-1]
    //     0xb97510: ldurb           w17, [x0, #-1]
    //     0xb97514: and             x16, x17, x16, lsr #2
    //     0xb97518: tst             x16, HEAP, lsr #32
    //     0xb9751c: b.eq            #0xb97524
    //     0xb97520: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb97524: r0 = Column()
    //     0xb97524: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb97528: r1 = Instance_Axis
    //     0xb97528: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb9752c: StoreField: r0->field_f = r1
    //     0xb9752c: stur            w1, [x0, #0xf]
    // 0xb97530: r1 = Instance_MainAxisAlignment
    //     0xb97530: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb97534: ldr             x1, [x1, #0x730]
    // 0xb97538: StoreField: r0->field_13 = r1
    //     0xb97538: stur            w1, [x0, #0x13]
    // 0xb9753c: r1 = Instance_MainAxisSize
    //     0xb9753c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb97540: ldr             x1, [x1, #0x738]
    // 0xb97544: ArrayStore: r0[0] = r1  ; List_4
    //     0xb97544: stur            w1, [x0, #0x17]
    // 0xb97548: r1 = Instance_CrossAxisAlignment
    //     0xb97548: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb9754c: ldr             x1, [x1, #0x740]
    // 0xb97550: StoreField: r0->field_1b = r1
    //     0xb97550: stur            w1, [x0, #0x1b]
    // 0xb97554: r1 = Instance_VerticalDirection
    //     0xb97554: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb97558: ldr             x1, [x1, #0x748]
    // 0xb9755c: StoreField: r0->field_23 = r1
    //     0xb9755c: stur            w1, [x0, #0x23]
    // 0xb97560: r1 = Instance_Clip
    //     0xb97560: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb97564: ldr             x1, [x1, #0x750]
    // 0xb97568: StoreField: r0->field_2b = r1
    //     0xb97568: stur            w1, [x0, #0x2b]
    // 0xb9756c: StoreField: r0->field_2f = rZR
    //     0xb9756c: stur            xzr, [x0, #0x2f]
    // 0xb97570: ldur            x1, [fp, #-0x18]
    // 0xb97574: StoreField: r0->field_b = r1
    //     0xb97574: stur            w1, [x0, #0xb]
    // 0xb97578: LeaveFrame
    //     0xb97578: mov             SP, fp
    //     0xb9757c: ldp             fp, lr, [SP], #0x10
    // 0xb97580: ret
    //     0xb97580: ret             
    // 0xb97584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97588: b               #0xb971b4
    // 0xb9758c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9758c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, ZakatTypes?) {
    // ** addr: 0xb97590, size: 0x80
    // 0xb97590: EnterFrame
    //     0xb97590: stp             fp, lr, [SP, #-0x10]!
    //     0xb97594: mov             fp, SP
    // 0xb97598: AllocStack(0x18)
    //     0xb97598: sub             SP, SP, #0x18
    // 0xb9759c: SetupParameters()
    //     0xb9759c: ldr             x0, [fp, #0x18]
    //     0xb975a0: ldur            w1, [x0, #0x17]
    //     0xb975a4: add             x1, x1, HEAP, lsl #32
    //     0xb975a8: stur            x1, [fp, #-8]
    // 0xb975ac: CheckStackOverflow
    //     0xb975ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb975b0: cmp             SP, x16
    //     0xb975b4: b.ls            #0xb97608
    // 0xb975b8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb975b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb975bc: ldr             x0, [x0, #0x2670]
    //     0xb975c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb975c4: cmp             w0, w16
    //     0xb975c8: b.ne            #0xb975d4
    //     0xb975cc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb975d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb975d4: ldur            x0, [fp, #-8]
    // 0xb975d8: LoadField: r1 = r0->field_f
    //     0xb975d8: ldur            w1, [x0, #0xf]
    // 0xb975dc: DecompressPointer r1
    //     0xb975dc: add             x1, x1, HEAP, lsl #32
    // 0xb975e0: r16 = <ZakatTypes>
    //     0xb975e0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] TypeArguments: <ZakatTypes>
    //     0xb975e4: ldr             x16, [x16, #0x68]
    // 0xb975e8: stp             x1, x16, [SP]
    // 0xb975ec: r4 = const [0x1, 0x1, 0x1, 0, result, 0, null]
    //     0xb975ec: add             x4, PP, #0x25, lsl #12  ; [pp+0x257f0] List(7) [0x1, 0x1, 0x1, 0, "result", 0, Null]
    //     0xb975f0: ldr             x4, [x4, #0x7f0]
    // 0xb975f4: r0 = GetNavigation.back()
    //     0xb975f4: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb975f8: r0 = Null
    //     0xb975f8: mov             x0, NULL
    // 0xb975fc: LeaveFrame
    //     0xb975fc: mov             SP, fp
    //     0xb97600: ldp             fp, lr, [SP], #0x10
    // 0xb97604: ret
    //     0xb97604: ret             
    // 0xb97608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9760c: b               #0xb975b8
  }
  [closure] bool <anonymous closure>(dynamic, ZakatTypes) {
    // ** addr: 0xb97610, size: 0x3c
    // 0xb97610: ldr             x1, [SP]
    // 0xb97614: r16 = Instance_ZakatTypes
    //     0xb97614: add             x16, PP, #0x30, lsl #12  ; [pp+0x30528] Obj!ZakatTypes@e307a1
    //     0xb97618: ldr             x16, [x16, #0x528]
    // 0xb9761c: cmp             w1, w16
    // 0xb97620: b.eq            #0xb97644
    // 0xb97624: r16 = Instance_ZakatTypes
    //     0xb97624: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] Obj!ZakatTypes@e307c1
    //     0xb97628: ldr             x16, [x16, #0x70]
    // 0xb9762c: cmp             w1, w16
    // 0xb97630: r16 = true
    //     0xb97630: add             x16, NULL, #0x20  ; true
    // 0xb97634: r17 = false
    //     0xb97634: add             x17, NULL, #0x30  ; false
    // 0xb97638: csel            x2, x16, x17, ne
    // 0xb9763c: mov             x0, x2
    // 0xb97640: b               #0xb97648
    // 0xb97644: r0 = false
    //     0xb97644: add             x0, NULL, #0x30  ; false
    // 0xb97648: ret
    //     0xb97648: ret             
  }
}
