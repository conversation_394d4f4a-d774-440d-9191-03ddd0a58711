// lib: , url: package:nuonline/app/modules/encyclopedia/bindings/encyclopedia_binding.dart

// class id: 1050256, size: 0x8
class :: {
}

// class id: 2172, size: 0x8, field offset: 0x8
class EncyclopediaBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x811f6c, size: 0x70
    // 0x811f6c: EnterFrame
    //     0x811f6c: stp             fp, lr, [SP, #-0x10]!
    //     0x811f70: mov             fp, SP
    // 0x811f74: AllocStack(0x10)
    //     0x811f74: sub             SP, SP, #0x10
    // 0x811f78: CheckStackOverflow
    //     0x811f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811f7c: cmp             SP, x16
    //     0x811f80: b.ls            #0x811fd4
    // 0x811f84: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x811f84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x811f88: ldr             x0, [x0, #0x2670]
    //     0x811f8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x811f90: cmp             w0, w16
    //     0x811f94: b.ne            #0x811fa0
    //     0x811f98: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x811f9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x811fa0: r1 = Function '<anonymous closure>':.
    //     0x811fa0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ac8] AnonymousClosure: (0x811fdc), in [package:nuonline/app/modules/encyclopedia/bindings/encyclopedia_binding.dart] EncyclopediaBinding::dependencies (0x811f6c)
    //     0x811fa4: ldr             x1, [x1, #0xac8]
    // 0x811fa8: r2 = Null
    //     0x811fa8: mov             x2, NULL
    // 0x811fac: r0 = AllocateClosure()
    //     0x811fac: bl              #0xec1630  ; AllocateClosureStub
    // 0x811fb0: r16 = <EncyclopediaController>
    //     0x811fb0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a80] TypeArguments: <EncyclopediaController>
    //     0x811fb4: ldr             x16, [x16, #0xa80]
    // 0x811fb8: stp             x0, x16, [SP]
    // 0x811fbc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x811fbc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x811fc0: r0 = Inst.lazyPut()
    //     0x811fc0: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x811fc4: r0 = Null
    //     0x811fc4: mov             x0, NULL
    // 0x811fc8: LeaveFrame
    //     0x811fc8: mov             SP, fp
    //     0x811fcc: ldp             fp, lr, [SP], #0x10
    // 0x811fd0: ret
    //     0x811fd0: ret             
    // 0x811fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811fd8: b               #0x811f84
  }
  [closure] EncyclopediaController <anonymous closure>(dynamic) {
    // ** addr: 0x811fdc, size: 0x7c
    // 0x811fdc: EnterFrame
    //     0x811fdc: stp             fp, lr, [SP, #-0x10]!
    //     0x811fe0: mov             fp, SP
    // 0x811fe4: AllocStack(0x20)
    //     0x811fe4: sub             SP, SP, #0x20
    // 0x811fe8: CheckStackOverflow
    //     0x811fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811fec: cmp             SP, x16
    //     0x811ff0: b.ls            #0x812050
    // 0x811ff4: r0 = find()
    //     0x811ff4: bl              #0x81214c  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::find
    // 0x811ff8: stur            x0, [fp, #-8]
    // 0x811ffc: r0 = find()
    //     0x811ffc: bl              #0x8120e8  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_remote_repository.dart] EncyclopediaRemoteRepository::find
    // 0x812000: stur            x0, [fp, #-0x10]
    // 0x812004: r0 = find()
    //     0x812004: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0x812008: r1 = <List<EncyclopediaCategory>>
    //     0x812008: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f128] TypeArguments: <List<EncyclopediaCategory>>
    //     0x81200c: ldr             x1, [x1, #0x128]
    // 0x812010: stur            x0, [fp, #-0x18]
    // 0x812014: r0 = EncyclopediaController()
    //     0x812014: bl              #0x812058  ; AllocateEncyclopediaControllerStub -> EncyclopediaController (size=0x38)
    // 0x812018: mov             x2, x0
    // 0x81201c: ldur            x0, [fp, #-8]
    // 0x812020: stur            x2, [fp, #-0x20]
    // 0x812024: StoreField: r2->field_2b = r0
    //     0x812024: stur            w0, [x2, #0x2b]
    // 0x812028: ldur            x0, [fp, #-0x10]
    // 0x81202c: StoreField: r2->field_2f = r0
    //     0x81202c: stur            w0, [x2, #0x2f]
    // 0x812030: ldur            x0, [fp, #-0x18]
    // 0x812034: StoreField: r2->field_33 = r0
    //     0x812034: stur            w0, [x2, #0x33]
    // 0x812038: mov             x1, x2
    // 0x81203c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x81203c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x812040: ldur            x0, [fp, #-0x20]
    // 0x812044: LeaveFrame
    //     0x812044: mov             SP, fp
    //     0x812048: ldp             fp, lr, [SP], #0x10
    // 0x81204c: ret
    //     0x81204c: ret             
    // 0x812050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812054: b               #0x811ff4
  }
}
