// lib: , url: package:nuonline/app/modules/encyclopedia/controllers/encyclopedia_controller.dart

// class id: 1050257, size: 0x8
class :: {
}

// class id: 1984, size: 0x2c, field offset: 0x24
//   transformed mixin,
abstract class _EncyclopediaController&OfflineFirstController&StateMixin extends OfflineFirstController<dynamic>
     with StateMixin<X0> {
}

// class id: 1985, size: 0x38, field offset: 0x2c
class EncyclopediaController extends _EncyclopediaController&OfflineFirstController&StateMixin {

  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef3dc, size: 0x58
    // 0xbef3dc: EnterFrame
    //     0xbef3dc: stp             fp, lr, [SP, #-0x10]!
    //     0xbef3e0: mov             fp, SP
    // 0xbef3e4: AllocStack(0x10)
    //     0xbef3e4: sub             SP, SP, #0x10
    // 0xbef3e8: SetupParameters(EncyclopediaController this /* r1 => r1, fp-0x10 */)
    //     0xbef3e8: stur            NULL, [fp, #-8]
    //     0xbef3ec: stur            x1, [fp, #-0x10]
    // 0xbef3f0: CheckStackOverflow
    //     0xbef3f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef3f4: cmp             SP, x16
    //     0xbef3f8: b.ls            #0xbef42c
    // 0xbef3fc: InitAsync() -> Future<ApiResult<List<EncyclopediaCategory>>>
    //     0xbef3fc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff68] TypeArguments: <ApiResult<List<EncyclopediaCategory>>>
    //     0xbef400: ldr             x0, [x0, #0xf68]
    //     0xbef404: bl              #0x661298  ; InitAsyncStub
    // 0xbef408: ldur            x0, [fp, #-0x10]
    // 0xbef40c: LoadField: r1 = r0->field_2b
    //     0xbef40c: ldur            w1, [x0, #0x2b]
    // 0xbef410: DecompressPointer r1
    //     0xbef410: add             x1, x1, HEAP, lsl #32
    // 0xbef414: r0 = LoadClassIdInstr(r1)
    //     0xbef414: ldur            x0, [x1, #-1]
    //     0xbef418: ubfx            x0, x0, #0xc, #0x14
    // 0xbef41c: r0 = GDT[cid_x0 + -0xfe2]()
    //     0xbef41c: sub             lr, x0, #0xfe2
    //     0xbef420: ldr             lr, [x21, lr, lsl #3]
    //     0xbef424: blr             lr
    // 0xbef428: r0 = ReturnAsync()
    //     0xbef428: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef42c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef42c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef430: b               #0xbef3fc
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf5e54, size: 0xbc
    // 0xbf5e54: EnterFrame
    //     0xbf5e54: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5e58: mov             fp, SP
    // 0xbf5e5c: AllocStack(0x18)
    //     0xbf5e5c: sub             SP, SP, #0x18
    // 0xbf5e60: SetupParameters(EncyclopediaController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xbf5e60: stur            NULL, [fp, #-8]
    //     0xbf5e64: stur            x1, [fp, #-0x10]
    //     0xbf5e68: mov             x16, x2
    //     0xbf5e6c: mov             x2, x1
    //     0xbf5e70: mov             x1, x16
    //     0xbf5e74: stur            x1, [fp, #-0x18]
    // 0xbf5e78: CheckStackOverflow
    //     0xbf5e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5e7c: cmp             SP, x16
    //     0xbf5e80: b.ls            #0xbf5f08
    // 0xbf5e84: InitAsync() -> Future<void?>
    //     0xbf5e84: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf5e88: bl              #0x661298  ; InitAsyncStub
    // 0xbf5e8c: ldur            x2, [fp, #-0x18]
    // 0xbf5e90: r0 = LoadClassIdInstr(r2)
    //     0xbf5e90: ldur            x0, [x2, #-1]
    //     0xbf5e94: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5e98: mov             x1, x2
    // 0xbf5e9c: r0 = GDT[cid_x0 + 0xe879]()
    //     0xbf5e9c: movz            x17, #0xe879
    //     0xbf5ea0: add             lr, x0, x17
    //     0xbf5ea4: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5ea8: blr             lr
    // 0xbf5eac: tbnz            w0, #4, #0xbf5ed4
    // 0xbf5eb0: r0 = RxStatus()
    //     0xbf5eb0: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5eb4: mov             x1, x0
    // 0xbf5eb8: r0 = true
    //     0xbf5eb8: add             x0, NULL, #0x20  ; true
    // 0xbf5ebc: StoreField: r1->field_f = r0
    //     0xbf5ebc: stur            w0, [x1, #0xf]
    // 0xbf5ec0: r0 = false
    //     0xbf5ec0: add             x0, NULL, #0x30  ; false
    // 0xbf5ec4: StoreField: r1->field_7 = r0
    //     0xbf5ec4: stur            w0, [x1, #7]
    // 0xbf5ec8: StoreField: r1->field_b = r0
    //     0xbf5ec8: stur            w0, [x1, #0xb]
    // 0xbf5ecc: mov             x3, x1
    // 0xbf5ed0: b               #0xbf5ef4
    // 0xbf5ed4: r0 = false
    //     0xbf5ed4: add             x0, NULL, #0x30  ; false
    // 0xbf5ed8: r0 = RxStatus()
    //     0xbf5ed8: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5edc: mov             x1, x0
    // 0xbf5ee0: r0 = false
    //     0xbf5ee0: add             x0, NULL, #0x30  ; false
    // 0xbf5ee4: StoreField: r1->field_f = r0
    //     0xbf5ee4: stur            w0, [x1, #0xf]
    // 0xbf5ee8: StoreField: r1->field_7 = r0
    //     0xbf5ee8: stur            w0, [x1, #7]
    // 0xbf5eec: StoreField: r1->field_b = r0
    //     0xbf5eec: stur            w0, [x1, #0xb]
    // 0xbf5ef0: mov             x3, x1
    // 0xbf5ef4: ldur            x1, [fp, #-0x10]
    // 0xbf5ef8: ldur            x2, [fp, #-0x18]
    // 0xbf5efc: r0 = change()
    //     0xbf5efc: bl              #0xb537b8  ; [package:nuonline/app/modules/doa/controllers/doa_category_builder_controller.dart] _DoaCategoryBuilderController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbf5f00: r0 = Null
    //     0xbf5f00: mov             x0, NULL
    // 0xbf5f04: r0 = ReturnAsyncNotFuture()
    //     0xbf5f04: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf5f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5f0c: b               #0xbf5e84
  }
  _ onOfflineModeFailure(/* No info */) async {
    // ** addr: 0xc2a144, size: 0x5c
    // 0xc2a144: EnterFrame
    //     0xc2a144: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a148: mov             fp, SP
    // 0xc2a14c: AllocStack(0x18)
    //     0xc2a14c: sub             SP, SP, #0x18
    // 0xc2a150: SetupParameters(EncyclopediaController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xc2a150: stur            NULL, [fp, #-8]
    //     0xc2a154: stur            x1, [fp, #-0x10]
    //     0xc2a158: mov             x16, x2
    //     0xc2a15c: mov             x2, x1
    //     0xc2a160: mov             x1, x16
    //     0xc2a164: stur            x1, [fp, #-0x18]
    // 0xc2a168: CheckStackOverflow
    //     0xc2a168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a16c: cmp             SP, x16
    //     0xc2a170: b.ls            #0xc2a198
    // 0xc2a174: InitAsync() -> Future<void?>
    //     0xc2a174: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc2a178: bl              #0x661298  ; InitAsyncStub
    // 0xc2a17c: r0 = hide()
    //     0xc2a17c: bl              #0xb0b868  ; [package:nuikit/src/widgets/loading/loading.dart] NLoadingDialog::hide
    // 0xc2a180: ldur            x1, [fp, #-0x18]
    // 0xc2a184: r2 = Instance_IconData
    //     0xc2a184: add             x2, PP, #0x28, lsl #12  ; [pp+0x28080] Obj!IconData@e0fe91
    //     0xc2a188: ldr             x2, [x2, #0x80]
    // 0xc2a18c: r0 = show()
    //     0xc2a18c: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xc2a190: r0 = Null
    //     0xc2a190: mov             x0, NULL
    // 0xc2a194: r0 = ReturnAsyncNotFuture()
    //     0xc2a194: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc2a198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a19c: b               #0xc2a174
  }
  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0xe34e28, size: 0x60
    // 0xe34e28: EnterFrame
    //     0xe34e28: stp             fp, lr, [SP, #-0x10]!
    //     0xe34e2c: mov             fp, SP
    // 0xe34e30: AllocStack(0x18)
    //     0xe34e30: sub             SP, SP, #0x18
    // 0xe34e34: SetupParameters(EncyclopediaController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe34e34: stur            NULL, [fp, #-8]
    //     0xe34e38: stur            x1, [fp, #-0x10]
    //     0xe34e3c: stur            x2, [fp, #-0x18]
    // 0xe34e40: CheckStackOverflow
    //     0xe34e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34e44: cmp             SP, x16
    //     0xe34e48: b.ls            #0xe34e80
    // 0xe34e4c: InitAsync() -> Future<ApiResult<List<EncyclopediaCategory>>>
    //     0xe34e4c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff68] TypeArguments: <ApiResult<List<EncyclopediaCategory>>>
    //     0xe34e50: ldr             x0, [x0, #0xf68]
    //     0xe34e54: bl              #0x661298  ; InitAsyncStub
    // 0xe34e58: ldur            x0, [fp, #-0x10]
    // 0xe34e5c: LoadField: r1 = r0->field_2f
    //     0xe34e5c: ldur            w1, [x0, #0x2f]
    // 0xe34e60: DecompressPointer r1
    //     0xe34e60: add             x1, x1, HEAP, lsl #32
    // 0xe34e64: r0 = LoadClassIdInstr(r1)
    //     0xe34e64: ldur            x0, [x1, #-1]
    //     0xe34e68: ubfx            x0, x0, #0xc, #0x14
    // 0xe34e6c: ldur            x2, [fp, #-0x18]
    // 0xe34e70: r0 = GDT[cid_x0 + -0xfea]()
    //     0xe34e70: sub             lr, x0, #0xfea
    //     0xe34e74: ldr             lr, [x21, lr, lsl #3]
    //     0xe34e78: blr             lr
    // 0xe34e7c: r0 = ReturnAsync()
    //     0xe34e7c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe34e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34e84: b               #0xe34e4c
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0xe34e88, size: 0x94
    // 0xe34e88: EnterFrame
    //     0xe34e88: stp             fp, lr, [SP, #-0x10]!
    //     0xe34e8c: mov             fp, SP
    // 0xe34e90: AllocStack(0x18)
    //     0xe34e90: sub             SP, SP, #0x18
    // 0xe34e94: SetupParameters(EncyclopediaController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xe34e94: stur            NULL, [fp, #-8]
    //     0xe34e98: stur            x1, [fp, #-0x10]
    //     0xe34e9c: mov             x16, x2
    //     0xe34ea0: mov             x2, x1
    //     0xe34ea4: mov             x1, x16
    //     0xe34ea8: stur            x1, [fp, #-0x18]
    // 0xe34eac: CheckStackOverflow
    //     0xe34eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34eb0: cmp             SP, x16
    //     0xe34eb4: b.ls            #0xe34f14
    // 0xe34eb8: InitAsync() -> Future<void?>
    //     0xe34eb8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe34ebc: bl              #0x661298  ; InitAsyncStub
    // 0xe34ec0: ldur            x2, [fp, #-0x18]
    // 0xe34ec4: r0 = LoadClassIdInstr(r2)
    //     0xe34ec4: ldur            x0, [x2, #-1]
    //     0xe34ec8: ubfx            x0, x0, #0xc, #0x14
    // 0xe34ecc: mov             x1, x2
    // 0xe34ed0: r0 = GDT[cid_x0 + 0xd488]()
    //     0xe34ed0: movz            x17, #0xd488
    //     0xe34ed4: add             lr, x0, x17
    //     0xe34ed8: ldr             lr, [x21, lr, lsl #3]
    //     0xe34edc: blr             lr
    // 0xe34ee0: tbnz            w0, #4, #0xe34f0c
    // 0xe34ee4: ldur            x0, [fp, #-0x10]
    // 0xe34ee8: LoadField: r1 = r0->field_2b
    //     0xe34ee8: ldur            w1, [x0, #0x2b]
    // 0xe34eec: DecompressPointer r1
    //     0xe34eec: add             x1, x1, HEAP, lsl #32
    // 0xe34ef0: r0 = LoadClassIdInstr(r1)
    //     0xe34ef0: ldur            x0, [x1, #-1]
    //     0xe34ef4: ubfx            x0, x0, #0xc, #0x14
    // 0xe34ef8: ldur            x2, [fp, #-0x18]
    // 0xe34efc: r0 = GDT[cid_x0 + -0xfeb]()
    //     0xe34efc: sub             lr, x0, #0xfeb
    //     0xe34f00: ldr             lr, [x21, lr, lsl #3]
    //     0xe34f04: blr             lr
    // 0xe34f08: r0 = ReturnAsync()
    //     0xe34f08: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe34f0c: r0 = Null
    //     0xe34f0c: mov             x0, NULL
    // 0xe34f10: r0 = ReturnAsyncNotFuture()
    //     0xe34f10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe34f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34f18: b               #0xe34eb8
  }
  get _ lastUpdatedAt(/* No info */) async {
    // ** addr: 0xe37cd4, size: 0x80
    // 0xe37cd4: EnterFrame
    //     0xe37cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe37cd8: mov             fp, SP
    // 0xe37cdc: AllocStack(0x28)
    //     0xe37cdc: sub             SP, SP, #0x28
    // 0xe37ce0: SetupParameters(EncyclopediaController this /* r1 => r1, fp-0x10 */)
    //     0xe37ce0: stur            NULL, [fp, #-8]
    //     0xe37ce4: stur            x1, [fp, #-0x10]
    // 0xe37ce8: CheckStackOverflow
    //     0xe37ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37cec: cmp             SP, x16
    //     0xe37cf0: b.ls            #0xe37d4c
    // 0xe37cf4: InitAsync() -> Future<String?>
    //     0xe37cf4: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0xe37cf8: bl              #0x661298  ; InitAsyncStub
    // 0xe37cfc: ldur            x0, [fp, #-0x10]
    // 0xe37d00: LoadField: r1 = r0->field_2b
    //     0xe37d00: ldur            w1, [x0, #0x2b]
    // 0xe37d04: DecompressPointer r1
    //     0xe37d04: add             x1, x1, HEAP, lsl #32
    // 0xe37d08: r0 = LoadClassIdInstr(r1)
    //     0xe37d08: ldur            x0, [x1, #-1]
    //     0xe37d0c: ubfx            x0, x0, #0xc, #0x14
    // 0xe37d10: r0 = GDT[cid_x0 + -0xfef]()
    //     0xe37d10: sub             lr, x0, #0xfef
    //     0xe37d14: ldr             lr, [x21, lr, lsl #3]
    //     0xe37d18: blr             lr
    // 0xe37d1c: r1 = Function '<anonymous closure>':.
    //     0xe37d1c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ab0] AnonymousClosure: (0xe37d54), in [package:nuonline/app/modules/encyclopedia/controllers/encyclopedia_controller.dart] EncyclopediaController::lastUpdatedAt (0xe37cd4)
    //     0xe37d20: ldr             x1, [x1, #0xab0]
    // 0xe37d24: r2 = Null
    //     0xe37d24: mov             x2, NULL
    // 0xe37d28: stur            x0, [fp, #-0x10]
    // 0xe37d2c: r0 = AllocateClosure()
    //     0xe37d2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37d30: r16 = <String?>
    //     0xe37d30: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xe37d34: ldur            lr, [fp, #-0x10]
    // 0xe37d38: stp             lr, x16, [SP, #8]
    // 0xe37d3c: str             x0, [SP]
    // 0xe37d40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe37d40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe37d44: r0 = then()
    //     0xe37d44: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe37d48: r0 = ReturnAsync()
    //     0xe37d48: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe37d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37d4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37d50: b               #0xe37cf4
  }
  [closure] String <anonymous closure>(dynamic, EncyclopediaCategory?) {
    // ** addr: 0xe37d54, size: 0x38
    // 0xe37d54: ldr             x1, [SP]
    // 0xe37d58: cmp             w1, NULL
    // 0xe37d5c: b.ne            #0xe37d68
    // 0xe37d60: r1 = Null
    //     0xe37d60: mov             x1, NULL
    // 0xe37d64: b               #0xe37d74
    // 0xe37d68: LoadField: r2 = r1->field_1b
    //     0xe37d68: ldur            w2, [x1, #0x1b]
    // 0xe37d6c: DecompressPointer r2
    //     0xe37d6c: add             x2, x2, HEAP, lsl #32
    // 0xe37d70: mov             x1, x2
    // 0xe37d74: cmp             w1, NULL
    // 0xe37d78: b.ne            #0xe37d84
    // 0xe37d7c: r0 = ""
    //     0xe37d7c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe37d80: b               #0xe37d88
    // 0xe37d84: mov             x0, x1
    // 0xe37d88: ret
    //     0xe37d88: ret             
  }
}
