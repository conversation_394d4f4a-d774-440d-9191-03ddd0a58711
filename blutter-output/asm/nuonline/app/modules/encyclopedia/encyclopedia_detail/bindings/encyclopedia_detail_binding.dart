// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_detail/bindings/encyclopedia_detail_binding.dart

// class id: 1050258, size: 0x8
class :: {
}

// class id: 2171, size: 0x8, field offset: 0x8
class EncyclopediaDetailBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x81227c, size: 0x7c
    // 0x81227c: EnterFrame
    //     0x81227c: stp             fp, lr, [SP, #-0x10]!
    //     0x812280: mov             fp, SP
    // 0x812284: AllocStack(0x10)
    //     0x812284: sub             SP, SP, #0x10
    // 0x812288: CheckStackOverflow
    //     0x812288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81228c: cmp             SP, x16
    //     0x812290: b.ls            #0x8122f0
    // 0x812294: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812294: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812298: ldr             x0, [x0, #0x2670]
    //     0x81229c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8122a0: cmp             w0, w16
    //     0x8122a4: b.ne            #0x8122b0
    //     0x8122a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8122ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8122b0: r1 = Function '<anonymous closure>':.
    //     0x8122b0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a80] AnonymousClosure: (0x8122f8), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/bindings/encyclopedia_detail_binding.dart] EncyclopediaDetailBinding::dependencies (0x81227c)
    //     0x8122b4: ldr             x1, [x1, #0xa80]
    // 0x8122b8: r2 = Null
    //     0x8122b8: mov             x2, NULL
    // 0x8122bc: r0 = AllocateClosure()
    //     0x8122bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8122c0: r16 = <EncyclopediaDetailController>
    //     0x8122c0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b80] TypeArguments: <EncyclopediaDetailController>
    //     0x8122c4: ldr             x16, [x16, #0xb80]
    // 0x8122c8: stp             x0, x16, [SP]
    // 0x8122cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8122cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8122d0: r0 = Inst.lazyPut()
    //     0x8122d0: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8122d4: r0 = ReadingPreferenceBinding()
    //     0x8122d4: bl              #0x80da0c  ; AllocateReadingPreferenceBindingStub -> ReadingPreferenceBinding (size=0x8)
    // 0x8122d8: mov             x1, x0
    // 0x8122dc: r0 = dependencies()
    //     0x8122dc: bl              #0x8423bc  ; [package:nuonline/app/modules/setting/reading_preference/bindings/reading_preference_binding.dart] ReadingPreferenceBinding::dependencies
    // 0x8122e0: r0 = Null
    //     0x8122e0: mov             x0, NULL
    // 0x8122e4: LeaveFrame
    //     0x8122e4: mov             SP, fp
    //     0x8122e8: ldp             fp, lr, [SP], #0x10
    // 0x8122ec: ret
    //     0x8122ec: ret             
    // 0x8122f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8122f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8122f4: b               #0x812294
  }
  [closure] EncyclopediaDetailController <anonymous closure>(dynamic) {
    // ** addr: 0x8122f8, size: 0x15c
    // 0x8122f8: EnterFrame
    //     0x8122f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8122fc: mov             fp, SP
    // 0x812300: AllocStack(0x38)
    //     0x812300: sub             SP, SP, #0x38
    // 0x812304: CheckStackOverflow
    //     0x812304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812308: cmp             SP, x16
    //     0x81230c: b.ls            #0x81244c
    // 0x812310: r0 = find()
    //     0x812310: bl              #0x81214c  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::find
    // 0x812314: stur            x0, [fp, #-8]
    // 0x812318: r0 = find()
    //     0x812318: bl              #0x8120e8  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_remote_repository.dart] EncyclopediaRemoteRepository::find
    // 0x81231c: stur            x0, [fp, #-0x10]
    // 0x812320: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812320: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812324: ldr             x0, [x0, #0x2670]
    //     0x812328: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81232c: cmp             w0, w16
    //     0x812330: b.ne            #0x81233c
    //     0x812334: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812338: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81233c: r0 = GetNavigation.arguments()
    //     0x81233c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x812340: r16 = "id"
    //     0x812340: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x812344: ldr             x16, [x16, #0x740]
    // 0x812348: stp             x16, x0, [SP]
    // 0x81234c: r4 = 0
    //     0x81234c: movz            x4, #0
    // 0x812350: ldr             x0, [SP, #8]
    // 0x812354: r16 = UnlinkedCall_0x5f3c08
    //     0x812354: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a88] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x812358: add             x16, x16, #0xa88
    // 0x81235c: ldp             x5, lr, [x16]
    // 0x812360: blr             lr
    // 0x812364: mov             x3, x0
    // 0x812368: r2 = Null
    //     0x812368: mov             x2, NULL
    // 0x81236c: r1 = Null
    //     0x81236c: mov             x1, NULL
    // 0x812370: stur            x3, [fp, #-0x18]
    // 0x812374: branchIfSmi(r0, 0x81239c)
    //     0x812374: tbz             w0, #0, #0x81239c
    // 0x812378: r4 = LoadClassIdInstr(r0)
    //     0x812378: ldur            x4, [x0, #-1]
    //     0x81237c: ubfx            x4, x4, #0xc, #0x14
    // 0x812380: sub             x4, x4, #0x3c
    // 0x812384: cmp             x4, #1
    // 0x812388: b.ls            #0x81239c
    // 0x81238c: r8 = int
    //     0x81238c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x812390: r3 = Null
    //     0x812390: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a98] Null
    //     0x812394: ldr             x3, [x3, #0xa98]
    // 0x812398: r0 = int()
    //     0x812398: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x81239c: r0 = GetNavigation.arguments()
    //     0x81239c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8123a0: r16 = "title"
    //     0x8123a0: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8123a4: ldr             x16, [x16, #0x748]
    // 0x8123a8: stp             x16, x0, [SP]
    // 0x8123ac: r4 = 0
    //     0x8123ac: movz            x4, #0
    // 0x8123b0: ldr             x0, [SP, #8]
    // 0x8123b4: r16 = UnlinkedCall_0x5f3c08
    //     0x8123b4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34aa8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8123b8: add             x16, x16, #0xaa8
    // 0x8123bc: ldp             x5, lr, [x16]
    // 0x8123c0: blr             lr
    // 0x8123c4: mov             x3, x0
    // 0x8123c8: r2 = Null
    //     0x8123c8: mov             x2, NULL
    // 0x8123cc: r1 = Null
    //     0x8123cc: mov             x1, NULL
    // 0x8123d0: stur            x3, [fp, #-0x20]
    // 0x8123d4: r4 = 60
    //     0x8123d4: movz            x4, #0x3c
    // 0x8123d8: branchIfSmi(r0, 0x8123e4)
    //     0x8123d8: tbz             w0, #0, #0x8123e4
    // 0x8123dc: r4 = LoadClassIdInstr(r0)
    //     0x8123dc: ldur            x4, [x0, #-1]
    //     0x8123e0: ubfx            x4, x4, #0xc, #0x14
    // 0x8123e4: sub             x4, x4, #0x5e
    // 0x8123e8: cmp             x4, #1
    // 0x8123ec: b.ls            #0x812400
    // 0x8123f0: r8 = String
    //     0x8123f0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8123f4: r3 = Null
    //     0x8123f4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34ab8] Null
    //     0x8123f8: ldr             x3, [x3, #0xab8]
    // 0x8123fc: r0 = String()
    //     0x8123fc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x812400: r0 = find()
    //     0x812400: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0x812404: ldur            x0, [fp, #-0x18]
    // 0x812408: r2 = LoadInt32Instr(r0)
    //     0x812408: sbfx            x2, x0, #1, #0x1f
    //     0x81240c: tbz             w0, #0, #0x812414
    //     0x812410: ldur            x2, [x0, #7]
    // 0x812414: stur            x2, [fp, #-0x28]
    // 0x812418: r1 = <Encyclopedia>
    //     0x812418: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0x81241c: r0 = EncyclopediaDetailController()
    //     0x81241c: bl              #0x81253c  ; AllocateEncyclopediaDetailControllerStub -> EncyclopediaDetailController (size=0x44)
    // 0x812420: mov             x1, x0
    // 0x812424: ldur            x2, [fp, #-0x28]
    // 0x812428: ldur            x3, [fp, #-8]
    // 0x81242c: ldur            x5, [fp, #-0x10]
    // 0x812430: ldur            x6, [fp, #-0x20]
    // 0x812434: stur            x0, [fp, #-8]
    // 0x812438: r0 = EncyclopediaDetailController()
    //     0x812438: bl              #0x812454  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart] EncyclopediaDetailController::EncyclopediaDetailController
    // 0x81243c: ldur            x0, [fp, #-8]
    // 0x812440: LeaveFrame
    //     0x812440: mov             SP, fp
    //     0x812444: ldp             fp, lr, [SP], #0x10
    // 0x812448: ret
    //     0x812448: ret             
    // 0x81244c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81244c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812450: b               #0x812310
  }
}
