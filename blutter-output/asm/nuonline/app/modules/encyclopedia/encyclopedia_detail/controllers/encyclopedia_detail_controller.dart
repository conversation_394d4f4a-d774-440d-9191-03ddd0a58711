// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart

// class id: 1050259, size: 0x8
class :: {
}

// class id: 1992, size: 0x2c, field offset: 0x24
//   transformed mixin,
abstract class _EncyclopediaDetailController&OfflineFirstNoSyncController&StateMixin extends OfflineFirstNoSyncController<dynamic>
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x91d394, size: 0xa8
    // 0x91d394: EnterFrame
    //     0x91d394: stp             fp, lr, [SP, #-0x10]!
    //     0x91d398: mov             fp, SP
    // 0x91d39c: AllocStack(0x20)
    //     0x91d39c: sub             SP, SP, #0x20
    // 0x91d3a0: SetupParameters(_EncyclopediaDetailController&OfflineFirstNoSyncController&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x91d3a0: stur            x1, [fp, #-8]
    //     0x91d3a4: mov             x16, x2
    //     0x91d3a8: mov             x2, x1
    //     0x91d3ac: mov             x1, x16
    //     0x91d3b0: mov             x0, x3
    //     0x91d3b4: stur            x1, [fp, #-0x10]
    // 0x91d3b8: CheckStackOverflow
    //     0x91d3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d3bc: cmp             SP, x16
    //     0x91d3c0: b.ls            #0x91d434
    // 0x91d3c4: StoreField: r2->field_27 = r0
    //     0x91d3c4: stur            w0, [x2, #0x27]
    //     0x91d3c8: ldurb           w16, [x2, #-1]
    //     0x91d3cc: ldurb           w17, [x0, #-1]
    //     0x91d3d0: and             x16, x17, x16, lsr #2
    //     0x91d3d4: tst             x16, HEAP, lsr #32
    //     0x91d3d8: b.eq            #0x91d3e0
    //     0x91d3dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x91d3e0: LoadField: r0 = r2->field_23
    //     0x91d3e0: ldur            w0, [x2, #0x23]
    // 0x91d3e4: DecompressPointer r0
    //     0x91d3e4: add             x0, x0, HEAP, lsl #32
    // 0x91d3e8: stp             x0, x1, [SP]
    // 0x91d3ec: r0 = ==()
    //     0x91d3ec: bl              #0xd3f134  ; [package:equatable/src/equatable.dart] Equatable::==
    // 0x91d3f0: tbz             w0, #4, #0x91d41c
    // 0x91d3f4: ldur            x1, [fp, #-8]
    // 0x91d3f8: ldur            x0, [fp, #-0x10]
    // 0x91d3fc: StoreField: r1->field_23 = r0
    //     0x91d3fc: stur            w0, [x1, #0x23]
    //     0x91d400: ldurb           w16, [x1, #-1]
    //     0x91d404: ldurb           w17, [x0, #-1]
    //     0x91d408: and             x16, x17, x16, lsr #2
    //     0x91d40c: tst             x16, HEAP, lsr #32
    //     0x91d410: b.eq            #0x91d418
    //     0x91d414: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x91d418: b               #0x91d420
    // 0x91d41c: ldur            x1, [fp, #-8]
    // 0x91d420: r0 = _notifyUpdate()
    //     0x91d420: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x91d424: r0 = Null
    //     0x91d424: mov             x0, NULL
    // 0x91d428: LeaveFrame
    //     0x91d428: mov             SP, fp
    //     0x91d42c: ldp             fp, lr, [SP], #0x10
    // 0x91d430: ret
    //     0x91d430: ret             
    // 0x91d434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d438: b               #0x91d3c4
  }
}

// class id: 1993, size: 0x44, field offset: 0x2c
class EncyclopediaDetailController extends _EncyclopediaDetailController&OfflineFirstNoSyncController&StateMixin {

  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0x6fb478, size: 0x5c
    // 0x6fb478: EnterFrame
    //     0x6fb478: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb47c: mov             fp, SP
    // 0x6fb480: AllocStack(0x10)
    //     0x6fb480: sub             SP, SP, #0x10
    // 0x6fb484: SetupParameters(EncyclopediaDetailController this /* r1 => r1, fp-0x10 */)
    //     0x6fb484: stur            NULL, [fp, #-8]
    //     0x6fb488: stur            x1, [fp, #-0x10]
    // 0x6fb48c: CheckStackOverflow
    //     0x6fb48c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fb490: cmp             SP, x16
    //     0x6fb494: b.ls            #0x6fb4cc
    // 0x6fb498: InitAsync() -> Future<ApiResult<Encyclopedia>>
    //     0x6fb498: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff58] TypeArguments: <ApiResult<Encyclopedia>>
    //     0x6fb49c: ldr             x0, [x0, #0xf58]
    //     0x6fb4a0: bl              #0x661298  ; InitAsyncStub
    // 0x6fb4a4: ldur            x0, [fp, #-0x10]
    // 0x6fb4a8: LoadField: r1 = r0->field_2f
    //     0x6fb4a8: ldur            w1, [x0, #0x2f]
    // 0x6fb4ac: DecompressPointer r1
    //     0x6fb4ac: add             x1, x1, HEAP, lsl #32
    // 0x6fb4b0: LoadField: r2 = r0->field_33
    //     0x6fb4b0: ldur            x2, [x0, #0x33]
    // 0x6fb4b4: r0 = LoadClassIdInstr(r1)
    //     0x6fb4b4: ldur            x0, [x1, #-1]
    //     0x6fb4b8: ubfx            x0, x0, #0xc, #0x14
    // 0x6fb4bc: r0 = GDT[cid_x0 + -0xfff]()
    //     0x6fb4bc: sub             lr, x0, #0xfff
    //     0x6fb4c0: ldr             lr, [x21, lr, lsl #3]
    //     0x6fb4c4: blr             lr
    // 0x6fb4c8: r0 = ReturnAsync()
    //     0x6fb4c8: b               #0x6576a4  ; ReturnAsyncStub
    // 0x6fb4cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fb4cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fb4d0: b               #0x6fb498
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0x7f5e34, size: 0xc4
    // 0x7f5e34: EnterFrame
    //     0x7f5e34: stp             fp, lr, [SP, #-0x10]!
    //     0x7f5e38: mov             fp, SP
    // 0x7f5e3c: AllocStack(0x28)
    //     0x7f5e3c: sub             SP, SP, #0x28
    // 0x7f5e40: SetupParameters(EncyclopediaDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7f5e40: stur            NULL, [fp, #-8]
    //     0x7f5e44: stur            x1, [fp, #-0x10]
    //     0x7f5e48: stur            x2, [fp, #-0x18]
    //     0x7f5e4c: stur            x3, [fp, #-0x20]
    // 0x7f5e50: CheckStackOverflow
    //     0x7f5e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f5e54: cmp             SP, x16
    //     0x7f5e58: b.ls            #0x7f5ef0
    // 0x7f5e5c: InitAsync() -> Future<void?>
    //     0x7f5e5c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7f5e60: bl              #0x661298  ; InitAsyncStub
    // 0x7f5e64: ldur            x0, [fp, #-0x10]
    // 0x7f5e68: LoadField: r3 = r0->field_2b
    //     0x7f5e68: ldur            w3, [x0, #0x2b]
    // 0x7f5e6c: DecompressPointer r3
    //     0x7f5e6c: add             x3, x3, HEAP, lsl #32
    // 0x7f5e70: stur            x3, [fp, #-0x28]
    // 0x7f5e74: r1 = Null
    //     0x7f5e74: mov             x1, NULL
    // 0x7f5e78: r2 = 2
    //     0x7f5e78: movz            x2, #0x2
    // 0x7f5e7c: r0 = AllocateArray()
    //     0x7f5e7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7f5e80: mov             x2, x0
    // 0x7f5e84: ldur            x0, [fp, #-0x18]
    // 0x7f5e88: stur            x2, [fp, #-0x10]
    // 0x7f5e8c: StoreField: r2->field_f = r0
    //     0x7f5e8c: stur            w0, [x2, #0xf]
    // 0x7f5e90: r1 = <Encyclopedia>
    //     0x7f5e90: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0x7f5e94: r0 = AllocateGrowableArray()
    //     0x7f5e94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7f5e98: mov             x1, x0
    // 0x7f5e9c: ldur            x0, [fp, #-0x10]
    // 0x7f5ea0: StoreField: r1->field_f = r0
    //     0x7f5ea0: stur            w0, [x1, #0xf]
    // 0x7f5ea4: r0 = 2
    //     0x7f5ea4: movz            x0, #0x2
    // 0x7f5ea8: StoreField: r1->field_b = r0
    //     0x7f5ea8: stur            w0, [x1, #0xb]
    // 0x7f5eac: ldur            x0, [fp, #-0x28]
    // 0x7f5eb0: r2 = LoadClassIdInstr(r0)
    //     0x7f5eb0: ldur            x2, [x0, #-1]
    //     0x7f5eb4: ubfx            x2, x2, #0xc, #0x14
    // 0x7f5eb8: mov             x16, x1
    // 0x7f5ebc: mov             x1, x2
    // 0x7f5ec0: mov             x2, x16
    // 0x7f5ec4: mov             x16, x0
    // 0x7f5ec8: mov             x0, x1
    // 0x7f5ecc: mov             x1, x16
    // 0x7f5ed0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7f5ed0: sub             lr, x0, #1, lsl #12
    //     0x7f5ed4: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5ed8: blr             lr
    // 0x7f5edc: mov             x1, x0
    // 0x7f5ee0: stur            x1, [fp, #-0x10]
    // 0x7f5ee4: r0 = Await()
    //     0x7f5ee4: bl              #0x661044  ; AwaitStub
    // 0x7f5ee8: r0 = Null
    //     0x7f5ee8: mov             x0, NULL
    // 0x7f5eec: r0 = ReturnAsyncNotFuture()
    //     0x7f5eec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7f5ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f5ef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5ef4: b               #0x7f5e5c
  }
  _ EncyclopediaDetailController(/* No info */) {
    // ** addr: 0x812454, size: 0xe8
    // 0x812454: EnterFrame
    //     0x812454: stp             fp, lr, [SP, #-0x10]!
    //     0x812458: mov             fp, SP
    // 0x81245c: AllocStack(0x28)
    //     0x81245c: sub             SP, SP, #0x28
    // 0x812460: SetupParameters(EncyclopediaDetailController this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */)
    //     0x812460: mov             x4, x2
    //     0x812464: stur            x2, [fp, #-0x10]
    //     0x812468: mov             x2, x5
    //     0x81246c: stur            x5, [fp, #-0x20]
    //     0x812470: mov             x5, x1
    //     0x812474: mov             x0, x6
    //     0x812478: stur            x1, [fp, #-8]
    //     0x81247c: stur            x3, [fp, #-0x18]
    //     0x812480: stur            x6, [fp, #-0x28]
    // 0x812484: CheckStackOverflow
    //     0x812484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812488: cmp             SP, x16
    //     0x81248c: b.ls            #0x812534
    // 0x812490: r1 = true
    //     0x812490: add             x1, NULL, #0x20  ; true
    // 0x812494: r0 = BoolExtension.obs()
    //     0x812494: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x812498: ldur            x1, [fp, #-8]
    // 0x81249c: StoreField: r1->field_3f = r0
    //     0x81249c: stur            w0, [x1, #0x3f]
    //     0x8124a0: ldurb           w16, [x1, #-1]
    //     0x8124a4: ldurb           w17, [x0, #-1]
    //     0x8124a8: and             x16, x17, x16, lsr #2
    //     0x8124ac: tst             x16, HEAP, lsr #32
    //     0x8124b0: b.eq            #0x8124b8
    //     0x8124b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8124b8: ldur            x0, [fp, #-0x18]
    // 0x8124bc: StoreField: r1->field_2b = r0
    //     0x8124bc: stur            w0, [x1, #0x2b]
    //     0x8124c0: ldurb           w16, [x1, #-1]
    //     0x8124c4: ldurb           w17, [x0, #-1]
    //     0x8124c8: and             x16, x17, x16, lsr #2
    //     0x8124cc: tst             x16, HEAP, lsr #32
    //     0x8124d0: b.eq            #0x8124d8
    //     0x8124d4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8124d8: ldur            x0, [fp, #-0x20]
    // 0x8124dc: StoreField: r1->field_2f = r0
    //     0x8124dc: stur            w0, [x1, #0x2f]
    //     0x8124e0: ldurb           w16, [x1, #-1]
    //     0x8124e4: ldurb           w17, [x0, #-1]
    //     0x8124e8: and             x16, x17, x16, lsr #2
    //     0x8124ec: tst             x16, HEAP, lsr #32
    //     0x8124f0: b.eq            #0x8124f8
    //     0x8124f4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8124f8: ldur            x0, [fp, #-0x10]
    // 0x8124fc: StoreField: r1->field_33 = r0
    //     0x8124fc: stur            x0, [x1, #0x33]
    // 0x812500: ldur            x0, [fp, #-0x28]
    // 0x812504: StoreField: r1->field_3b = r0
    //     0x812504: stur            w0, [x1, #0x3b]
    //     0x812508: ldurb           w16, [x1, #-1]
    //     0x81250c: ldurb           w17, [x0, #-1]
    //     0x812510: and             x16, x17, x16, lsr #2
    //     0x812514: tst             x16, HEAP, lsr #32
    //     0x812518: b.eq            #0x812520
    //     0x81251c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812520: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x812520: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x812524: r0 = Null
    //     0x812524: mov             x0, NULL
    // 0x812528: LeaveFrame
    //     0x812528: mov             SP, fp
    //     0x81252c: ldp             fp, lr, [SP], #0x10
    // 0x812530: ret
    //     0x812530: ret             
    // 0x812534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812538: b               #0x812490
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8efa78, size: 0x5c
    // 0x8efa78: EnterFrame
    //     0x8efa78: stp             fp, lr, [SP, #-0x10]!
    //     0x8efa7c: mov             fp, SP
    // 0x8efa80: AllocStack(0x8)
    //     0x8efa80: sub             SP, SP, #8
    // 0x8efa84: SetupParameters(EncyclopediaDetailController this /* r1 => r0, fp-0x8 */)
    //     0x8efa84: mov             x0, x1
    //     0x8efa88: stur            x1, [fp, #-8]
    // 0x8efa8c: CheckStackOverflow
    //     0x8efa8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8efa90: cmp             SP, x16
    //     0x8efa94: b.ls            #0x8efacc
    // 0x8efa98: mov             x1, x0
    // 0x8efa9c: r0 = onInit()
    //     0x8efa9c: bl              #0x8ef418  ; [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onInit
    // 0x8efaa0: r0 = find()
    //     0x8efaa0: bl              #0x8efcf4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::find
    // 0x8efaa4: mov             x1, x0
    // 0x8efaa8: ldur            x0, [fp, #-8]
    // 0x8efaac: LoadField: r2 = r0->field_33
    //     0x8efaac: ldur            x2, [x0, #0x33]
    // 0x8efab0: r3 = Instance_CounterType
    //     0x8efab0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3ff60] Obj!CounterType@e31001
    //     0x8efab4: ldr             x3, [x3, #0xf60]
    // 0x8efab8: r0 = record()
    //     0x8efab8: bl              #0x8efad4  ; [package:nuonline/app/data/repositories/counter_repository.dart] CounterRepository::record
    // 0x8efabc: r0 = Null
    //     0x8efabc: mov             x0, NULL
    // 0x8efac0: LeaveFrame
    //     0x8efac0: mov             SP, fp
    //     0x8efac4: ldp             fp, lr, [SP], #0x10
    // 0x8efac8: ret
    //     0x8efac8: ret             
    // 0x8efacc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8efacc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8efad0: b               #0x8efa98
  }
  [closure] bool onScrollNotificationUpdated(dynamic, ScrollUpdateNotification) {
    // ** addr: 0xaf1c20, size: 0x3c
    // 0xaf1c20: EnterFrame
    //     0xaf1c20: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1c24: mov             fp, SP
    // 0xaf1c28: ldr             x0, [fp, #0x18]
    // 0xaf1c2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf1c2c: ldur            w1, [x0, #0x17]
    // 0xaf1c30: DecompressPointer r1
    //     0xaf1c30: add             x1, x1, HEAP, lsl #32
    // 0xaf1c34: CheckStackOverflow
    //     0xaf1c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1c38: cmp             SP, x16
    //     0xaf1c3c: b.ls            #0xaf1c54
    // 0xaf1c40: ldr             x2, [fp, #0x10]
    // 0xaf1c44: r0 = onScrollNotificationUpdated()
    //     0xaf1c44: bl              #0xaf1c5c  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart] EncyclopediaDetailController::onScrollNotificationUpdated
    // 0xaf1c48: LeaveFrame
    //     0xaf1c48: mov             SP, fp
    //     0xaf1c4c: ldp             fp, lr, [SP], #0x10
    // 0xaf1c50: ret
    //     0xaf1c50: ret             
    // 0xaf1c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf1c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1c58: b               #0xaf1c40
  }
  _ onScrollNotificationUpdated(/* No info */) {
    // ** addr: 0xaf1c5c, size: 0x84
    // 0xaf1c5c: EnterFrame
    //     0xaf1c5c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1c60: mov             fp, SP
    // 0xaf1c64: d0 = 26.000000
    //     0xaf1c64: fmov            d0, #26.00000000
    // 0xaf1c68: CheckStackOverflow
    //     0xaf1c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1c6c: cmp             SP, x16
    //     0xaf1c70: b.ls            #0xaf1cd4
    // 0xaf1c74: LoadField: r0 = r2->field_f
    //     0xaf1c74: ldur            w0, [x2, #0xf]
    // 0xaf1c78: DecompressPointer r0
    //     0xaf1c78: add             x0, x0, HEAP, lsl #32
    // 0xaf1c7c: LoadField: r2 = r0->field_f
    //     0xaf1c7c: ldur            w2, [x0, #0xf]
    // 0xaf1c80: DecompressPointer r2
    //     0xaf1c80: add             x2, x2, HEAP, lsl #32
    // 0xaf1c84: cmp             w2, NULL
    // 0xaf1c88: b.eq            #0xaf1cdc
    // 0xaf1c8c: LoadField: d1 = r2->field_7
    //     0xaf1c8c: ldur            d1, [x2, #7]
    // 0xaf1c90: fcmp            d1, d0
    // 0xaf1c94: b.le            #0xaf1cb0
    // 0xaf1c98: LoadField: r0 = r1->field_3f
    //     0xaf1c98: ldur            w0, [x1, #0x3f]
    // 0xaf1c9c: DecompressPointer r0
    //     0xaf1c9c: add             x0, x0, HEAP, lsl #32
    // 0xaf1ca0: mov             x1, x0
    // 0xaf1ca4: r2 = false
    //     0xaf1ca4: add             x2, NULL, #0x30  ; false
    // 0xaf1ca8: r0 = value=()
    //     0xaf1ca8: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf1cac: b               #0xaf1cc4
    // 0xaf1cb0: LoadField: r0 = r1->field_3f
    //     0xaf1cb0: ldur            w0, [x1, #0x3f]
    // 0xaf1cb4: DecompressPointer r0
    //     0xaf1cb4: add             x0, x0, HEAP, lsl #32
    // 0xaf1cb8: mov             x1, x0
    // 0xaf1cbc: r2 = true
    //     0xaf1cbc: add             x2, NULL, #0x20  ; true
    // 0xaf1cc0: r0 = value=()
    //     0xaf1cc0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf1cc4: r0 = true
    //     0xaf1cc4: add             x0, NULL, #0x20  ; true
    // 0xaf1cc8: LeaveFrame
    //     0xaf1cc8: mov             SP, fp
    //     0xaf1ccc: ldp             fp, lr, [SP], #0x10
    // 0xaf1cd0: ret
    //     0xaf1cd0: ret             
    // 0xaf1cd4: r0 = StackOverflowSharedWithFPURegs()
    //     0xaf1cd4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xaf1cd8: b               #0xaf1c74
    // 0xaf1cdc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaf1cdc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ onOfflineModeRequested(/* No info */) async {
    // ** addr: 0xbef2bc, size: 0x5c
    // 0xbef2bc: EnterFrame
    //     0xbef2bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbef2c0: mov             fp, SP
    // 0xbef2c4: AllocStack(0x10)
    //     0xbef2c4: sub             SP, SP, #0x10
    // 0xbef2c8: SetupParameters(EncyclopediaDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbef2c8: stur            NULL, [fp, #-8]
    //     0xbef2cc: stur            x1, [fp, #-0x10]
    // 0xbef2d0: CheckStackOverflow
    //     0xbef2d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef2d4: cmp             SP, x16
    //     0xbef2d8: b.ls            #0xbef310
    // 0xbef2dc: InitAsync() -> Future<ApiResult<Encyclopedia>>
    //     0xbef2dc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff58] TypeArguments: <ApiResult<Encyclopedia>>
    //     0xbef2e0: ldr             x0, [x0, #0xf58]
    //     0xbef2e4: bl              #0x661298  ; InitAsyncStub
    // 0xbef2e8: ldur            x0, [fp, #-0x10]
    // 0xbef2ec: LoadField: r1 = r0->field_2b
    //     0xbef2ec: ldur            w1, [x0, #0x2b]
    // 0xbef2f0: DecompressPointer r1
    //     0xbef2f0: add             x1, x1, HEAP, lsl #32
    // 0xbef2f4: LoadField: r2 = r0->field_33
    //     0xbef2f4: ldur            x2, [x0, #0x33]
    // 0xbef2f8: r0 = LoadClassIdInstr(r1)
    //     0xbef2f8: ldur            x0, [x1, #-1]
    //     0xbef2fc: ubfx            x0, x0, #0xc, #0x14
    // 0xbef300: r0 = GDT[cid_x0 + -0xfff]()
    //     0xbef300: sub             lr, x0, #0xfff
    //     0xbef304: ldr             lr, [x21, lr, lsl #3]
    //     0xbef308: blr             lr
    // 0xbef30c: r0 = ReturnAsync()
    //     0xbef30c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xbef310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef314: b               #0xbef2dc
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf5c4c, size: 0x64
    // 0xbf5c4c: EnterFrame
    //     0xbf5c4c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5c50: mov             fp, SP
    // 0xbf5c54: AllocStack(0x18)
    //     0xbf5c54: sub             SP, SP, #0x18
    // 0xbf5c58: SetupParameters(EncyclopediaDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbf5c58: stur            NULL, [fp, #-8]
    //     0xbf5c5c: stur            x1, [fp, #-0x10]
    //     0xbf5c60: stur            x2, [fp, #-0x18]
    // 0xbf5c64: CheckStackOverflow
    //     0xbf5c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5c68: cmp             SP, x16
    //     0xbf5c6c: b.ls            #0xbf5ca8
    // 0xbf5c70: InitAsync() -> Future<void?>
    //     0xbf5c70: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf5c74: bl              #0x661298  ; InitAsyncStub
    // 0xbf5c78: r0 = RxStatus()
    //     0xbf5c78: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0xbf5c7c: mov             x1, x0
    // 0xbf5c80: r0 = false
    //     0xbf5c80: add             x0, NULL, #0x30  ; false
    // 0xbf5c84: StoreField: r1->field_f = r0
    //     0xbf5c84: stur            w0, [x1, #0xf]
    // 0xbf5c88: StoreField: r1->field_7 = r0
    //     0xbf5c88: stur            w0, [x1, #7]
    // 0xbf5c8c: StoreField: r1->field_b = r0
    //     0xbf5c8c: stur            w0, [x1, #0xb]
    // 0xbf5c90: mov             x3, x1
    // 0xbf5c94: ldur            x1, [fp, #-0x10]
    // 0xbf5c98: ldur            x2, [fp, #-0x18]
    // 0xbf5c9c: r0 = change()
    //     0xbf5c9c: bl              #0x91d394  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart] _EncyclopediaDetailController&OfflineFirstNoSyncController&StateMixin::change
    // 0xbf5ca0: r0 = Null
    //     0xbf5ca0: mov             x0, NULL
    // 0xbf5ca4: r0 = ReturnAsyncNotFuture()
    //     0xbf5ca4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf5ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5cac: b               #0xbf5c70
  }
  _ onOnlineModeFailure(/* No info */) async {
    // ** addr: 0xdc715c, size: 0x40
    // 0xdc715c: EnterFrame
    //     0xdc715c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc7160: mov             fp, SP
    // 0xdc7164: AllocStack(0x18)
    //     0xdc7164: sub             SP, SP, #0x18
    // 0xdc7168: SetupParameters(EncyclopediaDetailController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xdc7168: stur            NULL, [fp, #-8]
    //     0xdc716c: stur            x1, [fp, #-0x10]
    //     0xdc7170: stur            x2, [fp, #-0x18]
    // 0xdc7174: CheckStackOverflow
    //     0xdc7174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc7178: cmp             SP, x16
    //     0xdc717c: b.ls            #0xdc7194
    // 0xdc7180: InitAsync() -> Future<void?>
    //     0xdc7180: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xdc7184: bl              #0x661298  ; InitAsyncStub
    // 0xdc7188: r0 = UnimplementedError()
    //     0xdc7188: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xdc718c: r0 = Throw()
    //     0xdc718c: bl              #0xec04b8  ; ThrowStub
    // 0xdc7190: brk             #0
    // 0xdc7194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc7194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc7198: b               #0xdc7180
  }
}
