// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_detail/views/encyclopedia_detail_view.dart

// class id: 1050260, size: 0x8
class :: {
}

// class id: 5015, size: 0x38, field offset: 0xc
//   const constructor, 
class EncylopediaDetailLayout extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9764c, size: 0x92c
    // 0xb9764c: EnterFrame
    //     0xb9764c: stp             fp, lr, [SP, #-0x10]!
    //     0xb97650: mov             fp, SP
    // 0xb97654: AllocStack(0x90)
    //     0xb97654: sub             SP, SP, #0x90
    // 0xb97658: SetupParameters(EncylopediaDetailLayout this /* r1 => r1, fp-0x8 */)
    //     0xb97658: stur            x1, [fp, #-8]
    // 0xb9765c: CheckStackOverflow
    //     0xb9765c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97660: cmp             SP, x16
    //     0xb97664: b.ls            #0xb97f54
    // 0xb97668: r1 = 1
    //     0xb97668: movz            x1, #0x1
    // 0xb9766c: r0 = AllocateContext()
    //     0xb9766c: bl              #0xec126c  ; AllocateContextStub
    // 0xb97670: mov             x1, x0
    // 0xb97674: ldur            x0, [fp, #-8]
    // 0xb97678: stur            x1, [fp, #-0x18]
    // 0xb9767c: StoreField: r1->field_f = r0
    //     0xb9767c: stur            w0, [x1, #0xf]
    // 0xb97680: LoadField: r2 = r0->field_b
    //     0xb97680: ldur            w2, [x0, #0xb]
    // 0xb97684: DecompressPointer r2
    //     0xb97684: add             x2, x2, HEAP, lsl #32
    // 0xb97688: stur            x2, [fp, #-0x10]
    // 0xb9768c: tbnz            w2, #4, #0xb9769c
    // 0xb97690: r3 = Instance_SystemUiOverlayStyle
    //     0xb97690: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d208] Obj!SystemUiOverlayStyle@e10e31
    //     0xb97694: ldr             x3, [x3, #0x208]
    // 0xb97698: b               #0xb976a4
    // 0xb9769c: r3 = Instance_SystemUiOverlayStyle
    //     0xb9769c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d200] Obj!SystemUiOverlayStyle@e10e61
    //     0xb976a0: ldr             x3, [x3, #0x200]
    // 0xb976a4: r16 = <SystemUiOverlayStyle?>
    //     0xb976a4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d1f8] TypeArguments: <SystemUiOverlayStyle?>
    //     0xb976a8: ldr             x16, [x16, #0x1f8]
    // 0xb976ac: r30 = Instance_SystemUiOverlayStyle
    //     0xb976ac: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2d200] Obj!SystemUiOverlayStyle@e10e61
    //     0xb976b0: ldr             lr, [lr, #0x200]
    // 0xb976b4: stp             lr, x16, [SP, #8]
    // 0xb976b8: str             x3, [SP]
    // 0xb976bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb976bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb976c0: r0 = mode()
    //     0xb976c0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb976c4: mov             x1, x0
    // 0xb976c8: ldur            x0, [fp, #-0x10]
    // 0xb976cc: stur            x1, [fp, #-0x20]
    // 0xb976d0: tbnz            w0, #4, #0xb9772c
    // 0xb976d4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb976d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb976d8: ldr             x0, [x0, #0x2670]
    //     0xb976dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb976e0: cmp             w0, w16
    //     0xb976e4: b.ne            #0xb976f0
    //     0xb976e8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb976ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb976f0: r0 = GetNavigation.textTheme()
    //     0xb976f0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb976f4: LoadField: r1 = r0->field_f
    //     0xb976f4: ldur            w1, [x0, #0xf]
    // 0xb976f8: DecompressPointer r1
    //     0xb976f8: add             x1, x1, HEAP, lsl #32
    // 0xb976fc: cmp             w1, NULL
    // 0xb97700: b.ne            #0xb9770c
    // 0xb97704: r0 = Null
    //     0xb97704: mov             x0, NULL
    // 0xb97708: b               #0xb97714
    // 0xb9770c: LoadField: r0 = r1->field_b
    //     0xb9770c: ldur            w0, [x1, #0xb]
    // 0xb97710: DecompressPointer r0
    //     0xb97710: add             x0, x0, HEAP, lsl #32
    // 0xb97714: stur            x0, [fp, #-0x28]
    // 0xb97718: r0 = IconThemeData()
    //     0xb97718: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0xb9771c: mov             x1, x0
    // 0xb97720: ldur            x0, [fp, #-0x28]
    // 0xb97724: StoreField: r1->field_1b = r0
    //     0xb97724: stur            w0, [x1, #0x1b]
    // 0xb97728: b               #0xb97730
    // 0xb9772c: r1 = Null
    //     0xb9772c: mov             x1, NULL
    // 0xb97730: ldur            x0, [fp, #-0x10]
    // 0xb97734: stur            x1, [fp, #-0x28]
    // 0xb97738: tbnz            w0, #4, #0xb97744
    // 0xb9773c: d0 = 0.000000
    //     0xb9773c: eor             v0.16b, v0.16b, v0.16b
    // 0xb97740: b               #0xb97748
    // 0xb97744: d0 = 4.000000
    //     0xb97744: fmov            d0, #4.00000000
    // 0xb97748: stur            d0, [fp, #-0x68]
    // 0xb9774c: tbnz            w0, #4, #0xb97758
    // 0xb97750: r1 = Instance_Color
    //     0xb97750: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb97754: b               #0xb97790
    // 0xb97758: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb97758: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9775c: ldr             x0, [x0, #0x2670]
    //     0xb97760: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb97764: cmp             w0, w16
    //     0xb97768: b.ne            #0xb97774
    //     0xb9776c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb97770: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb97774: r0 = GetNavigation.theme()
    //     0xb97774: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb97778: LoadField: r1 = r0->field_9b
    //     0xb97778: ldur            w1, [x0, #0x9b]
    // 0xb9777c: DecompressPointer r1
    //     0xb9777c: add             x1, x1, HEAP, lsl #32
    // 0xb97780: LoadField: r0 = r1->field_7
    //     0xb97780: ldur            w0, [x1, #7]
    // 0xb97784: DecompressPointer r0
    //     0xb97784: add             x0, x0, HEAP, lsl #32
    // 0xb97788: mov             x1, x0
    // 0xb9778c: ldur            x0, [fp, #-0x10]
    // 0xb97790: stur            x1, [fp, #-0x30]
    // 0xb97794: tbnz            w0, #4, #0xb977a0
    // 0xb97798: r1 = Null
    //     0xb97798: mov             x1, NULL
    // 0xb9779c: b               #0xb977c0
    // 0xb977a0: ldur            x0, [fp, #-8]
    // 0xb977a4: LoadField: r2 = r0->field_f
    //     0xb977a4: ldur            w2, [x0, #0xf]
    // 0xb977a8: DecompressPointer r2
    //     0xb977a8: add             x2, x2, HEAP, lsl #32
    // 0xb977ac: stur            x2, [fp, #-0x10]
    // 0xb977b0: r0 = Text()
    //     0xb977b0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb977b4: mov             x1, x0
    // 0xb977b8: ldur            x0, [fp, #-0x10]
    // 0xb977bc: StoreField: r1->field_b = r0
    //     0xb977bc: stur            w0, [x1, #0xb]
    // 0xb977c0: ldur            x0, [fp, #-8]
    // 0xb977c4: ldur            d0, [fp, #-0x68]
    // 0xb977c8: stur            x1, [fp, #-0x38]
    // 0xb977cc: r2 = inline_Allocate_Double()
    //     0xb977cc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb977d0: add             x2, x2, #0x10
    //     0xb977d4: cmp             x3, x2
    //     0xb977d8: b.ls            #0xb97f5c
    //     0xb977dc: str             x2, [THR, #0x50]  ; THR::top
    //     0xb977e0: sub             x2, x2, #0xf
    //     0xb977e4: movz            x3, #0xe15c
    //     0xb977e8: movk            x3, #0x3, lsl #16
    //     0xb977ec: stur            x3, [x2, #-1]
    // 0xb977f0: StoreField: r2->field_7 = d0
    //     0xb977f0: stur            d0, [x2, #7]
    // 0xb977f4: stur            x2, [fp, #-0x10]
    // 0xb977f8: r0 = AppBar()
    //     0xb977f8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb977fc: stur            x0, [fp, #-0x40]
    // 0xb97800: ldur            x16, [fp, #-0x20]
    // 0xb97804: ldur            lr, [fp, #-0x28]
    // 0xb97808: stp             lr, x16, [SP, #0x18]
    // 0xb9780c: ldur            x16, [fp, #-0x10]
    // 0xb97810: ldur            lr, [fp, #-0x30]
    // 0xb97814: stp             lr, x16, [SP, #8]
    // 0xb97818: ldur            x16, [fp, #-0x38]
    // 0xb9781c: str             x16, [SP]
    // 0xb97820: mov             x1, x0
    // 0xb97824: r4 = const [0, 0x6, 0x5, 0x1, backgroundColor, 0x4, elevation, 0x3, iconTheme, 0x2, systemOverlayStyle, 0x1, title, 0x5, null]
    //     0xb97824: add             x4, PP, #0x34, lsl #12  ; [pp+0x34a60] List(15) [0, 0x6, 0x5, 0x1, "backgroundColor", 0x4, "elevation", 0x3, "iconTheme", 0x2, "systemOverlayStyle", 0x1, "title", 0x5, Null]
    //     0xb97828: ldr             x4, [x4, #0xa60]
    // 0xb9782c: r0 = AppBar()
    //     0xb9782c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb97830: r0 = PreferredSize()
    //     0xb97830: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xb97834: mov             x1, x0
    // 0xb97838: r0 = Instance_Size
    //     0xb97838: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xb9783c: ldr             x0, [x0, #0xc88]
    // 0xb97840: stur            x1, [fp, #-0x30]
    // 0xb97844: StoreField: r1->field_f = r0
    //     0xb97844: stur            w0, [x1, #0xf]
    // 0xb97848: ldur            x0, [fp, #-0x40]
    // 0xb9784c: StoreField: r1->field_b = r0
    //     0xb9784c: stur            w0, [x1, #0xb]
    // 0xb97850: ldur            x0, [fp, #-8]
    // 0xb97854: LoadField: r2 = r0->field_13
    //     0xb97854: ldur            w2, [x0, #0x13]
    // 0xb97858: DecompressPointer r2
    //     0xb97858: add             x2, x2, HEAP, lsl #32
    // 0xb9785c: stur            x2, [fp, #-0x28]
    // 0xb97860: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb97860: ldur            w3, [x0, #0x17]
    // 0xb97864: DecompressPointer r3
    //     0xb97864: add             x3, x3, HEAP, lsl #32
    // 0xb97868: stur            x3, [fp, #-0x20]
    // 0xb9786c: LoadField: r4 = r0->field_f
    //     0xb9786c: ldur            w4, [x0, #0xf]
    // 0xb97870: DecompressPointer r4
    //     0xb97870: add             x4, x4, HEAP, lsl #32
    // 0xb97874: stur            x4, [fp, #-0x10]
    // 0xb97878: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb97878: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9787c: ldr             x0, [x0, #0x2670]
    //     0xb97880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb97884: cmp             w0, w16
    //     0xb97888: b.ne            #0xb97894
    //     0xb9788c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb97890: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb97894: r0 = GetNavigation.textTheme()
    //     0xb97894: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb97898: LoadField: r1 = r0->field_f
    //     0xb97898: ldur            w1, [x0, #0xf]
    // 0xb9789c: DecompressPointer r1
    //     0xb9789c: add             x1, x1, HEAP, lsl #32
    // 0xb978a0: cmp             w1, NULL
    // 0xb978a4: b.ne            #0xb978b0
    // 0xb978a8: r2 = Null
    //     0xb978a8: mov             x2, NULL
    // 0xb978ac: b               #0xb978cc
    // 0xb978b0: r16 = 22.000000
    //     0xb978b0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25d68] 22
    //     0xb978b4: ldr             x16, [x16, #0xd68]
    // 0xb978b8: str             x16, [SP]
    // 0xb978bc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb978bc: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb978c0: ldr             x4, [x4, #0x88]
    // 0xb978c4: r0 = copyWith()
    //     0xb978c4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb978c8: mov             x2, x0
    // 0xb978cc: ldur            x0, [fp, #-8]
    // 0xb978d0: ldur            x1, [fp, #-0x10]
    // 0xb978d4: stur            x2, [fp, #-0x38]
    // 0xb978d8: r0 = Text()
    //     0xb978d8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb978dc: mov             x3, x0
    // 0xb978e0: ldur            x0, [fp, #-0x10]
    // 0xb978e4: stur            x3, [fp, #-0x40]
    // 0xb978e8: StoreField: r3->field_b = r0
    //     0xb978e8: stur            w0, [x3, #0xb]
    // 0xb978ec: ldur            x0, [fp, #-0x38]
    // 0xb978f0: StoreField: r3->field_13 = r0
    //     0xb978f0: stur            w0, [x3, #0x13]
    // 0xb978f4: r1 = Null
    //     0xb978f4: mov             x1, NULL
    // 0xb978f8: r2 = 4
    //     0xb978f8: movz            x2, #0x4
    // 0xb978fc: r0 = AllocateArray()
    //     0xb978fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb97900: mov             x2, x0
    // 0xb97904: ldur            x0, [fp, #-0x40]
    // 0xb97908: stur            x2, [fp, #-0x10]
    // 0xb9790c: StoreField: r2->field_f = r0
    //     0xb9790c: stur            w0, [x2, #0xf]
    // 0xb97910: r16 = Instance_SizedBox
    //     0xb97910: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb97914: ldr             x16, [x16, #0x448]
    // 0xb97918: StoreField: r2->field_13 = r16
    //     0xb97918: stur            w16, [x2, #0x13]
    // 0xb9791c: r1 = <Widget>
    //     0xb9791c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb97920: r0 = AllocateGrowableArray()
    //     0xb97920: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb97924: mov             x2, x0
    // 0xb97928: ldur            x0, [fp, #-0x10]
    // 0xb9792c: stur            x2, [fp, #-0x38]
    // 0xb97930: StoreField: r2->field_f = r0
    //     0xb97930: stur            w0, [x2, #0xf]
    // 0xb97934: r0 = 4
    //     0xb97934: movz            x0, #0x4
    // 0xb97938: StoreField: r2->field_b = r0
    //     0xb97938: stur            w0, [x2, #0xb]
    // 0xb9793c: ldur            x3, [fp, #-8]
    // 0xb97940: LoadField: r4 = r3->field_2b
    //     0xb97940: ldur            w4, [x3, #0x2b]
    // 0xb97944: DecompressPointer r4
    //     0xb97944: add             x4, x4, HEAP, lsl #32
    // 0xb97948: stur            x4, [fp, #-0x10]
    // 0xb9794c: tbnz            w4, #4, #0xb9797c
    // 0xb97950: mov             x1, x2
    // 0xb97954: r0 = _growToNextCapacity()
    //     0xb97954: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97958: ldur            x0, [fp, #-0x38]
    // 0xb9795c: r1 = 6
    //     0xb9795c: movz            x1, #0x6
    // 0xb97960: StoreField: r0->field_b = r1
    //     0xb97960: stur            w1, [x0, #0xb]
    // 0xb97964: LoadField: r1 = r0->field_f
    //     0xb97964: ldur            w1, [x0, #0xf]
    // 0xb97968: DecompressPointer r1
    //     0xb97968: add             x1, x1, HEAP, lsl #32
    // 0xb9796c: r16 = Instance_NSkeleton
    //     0xb9796c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!NSkeleton@e20a21
    //     0xb97970: ldr             x16, [x16, #0xa68]
    // 0xb97974: ArrayStore: r1[0] = r16  ; List_4
    //     0xb97974: stur            w16, [x1, #0x17]
    // 0xb97978: b               #0xb97980
    // 0xb9797c: mov             x0, x2
    // 0xb97980: ldur            x1, [fp, #-0x10]
    // 0xb97984: tbz             w1, #4, #0xb97d18
    // 0xb97988: ldur            x3, [fp, #-8]
    // 0xb9798c: r1 = <Widget>
    //     0xb9798c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb97990: r2 = 0
    //     0xb97990: movz            x2, #0
    // 0xb97994: r0 = _GrowableList()
    //     0xb97994: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb97998: mov             x1, x0
    // 0xb9799c: ldur            x0, [fp, #-8]
    // 0xb979a0: stur            x1, [fp, #-0x40]
    // 0xb979a4: LoadField: r2 = r0->field_27
    //     0xb979a4: ldur            w2, [x0, #0x27]
    // 0xb979a8: DecompressPointer r2
    //     0xb979a8: add             x2, x2, HEAP, lsl #32
    // 0xb979ac: stur            x2, [fp, #-0x10]
    // 0xb979b0: LoadField: r3 = r2->field_7
    //     0xb979b0: ldur            w3, [x2, #7]
    // 0xb979b4: cbz             w3, #0xb97adc
    // 0xb979b8: r4 = LoadInt32Instr(r3)
    //     0xb979b8: sbfx            x4, x3, #1, #0x1f
    // 0xb979bc: sub             x3, x4, #1
    // 0xb979c0: lsl             x4, x3, #1
    // 0xb979c4: stp             x4, x2, [SP, #8]
    // 0xb979c8: r16 = "/"
    //     0xb979c8: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xb979cc: str             x16, [SP]
    // 0xb979d0: r0 = _substringMatches()
    //     0xb979d0: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xb979d4: tbz             w0, #4, #0xb97adc
    // 0xb979d8: ldur            x0, [fp, #-0x10]
    // 0xb979dc: r0 = Radius()
    //     0xb979dc: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb979e0: d0 = 8.000000
    //     0xb979e0: fmov            d0, #8.00000000
    // 0xb979e4: stur            x0, [fp, #-0x48]
    // 0xb979e8: StoreField: r0->field_7 = d0
    //     0xb979e8: stur            d0, [x0, #7]
    // 0xb979ec: StoreField: r0->field_f = d0
    //     0xb979ec: stur            d0, [x0, #0xf]
    // 0xb979f0: r0 = BorderRadius()
    //     0xb979f0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb979f4: mov             x1, x0
    // 0xb979f8: ldur            x0, [fp, #-0x48]
    // 0xb979fc: stur            x1, [fp, #-0x50]
    // 0xb97a00: StoreField: r1->field_7 = r0
    //     0xb97a00: stur            w0, [x1, #7]
    // 0xb97a04: StoreField: r1->field_b = r0
    //     0xb97a04: stur            w0, [x1, #0xb]
    // 0xb97a08: StoreField: r1->field_f = r0
    //     0xb97a08: stur            w0, [x1, #0xf]
    // 0xb97a0c: StoreField: r1->field_13 = r0
    //     0xb97a0c: stur            w0, [x1, #0x13]
    // 0xb97a10: r0 = NFadeInImageNetwork()
    //     0xb97a10: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xb97a14: mov             x1, x0
    // 0xb97a18: ldur            x0, [fp, #-0x10]
    // 0xb97a1c: stur            x1, [fp, #-0x48]
    // 0xb97a20: StoreField: r1->field_b = r0
    //     0xb97a20: stur            w0, [x1, #0xb]
    // 0xb97a24: r0 = "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xb97a24: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a18] "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xb97a28: ldr             x0, [x0, #0xa18]
    // 0xb97a2c: StoreField: r1->field_f = r0
    //     0xb97a2c: stur            w0, [x1, #0xf]
    // 0xb97a30: r0 = "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xb97a30: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a20] "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xb97a34: ldr             x0, [x0, #0xa20]
    // 0xb97a38: StoreField: r1->field_13 = r0
    //     0xb97a38: stur            w0, [x1, #0x13]
    // 0xb97a3c: r0 = Instance_BoxFit
    //     0xb97a3c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xb97a40: ldr             x0, [x0, #0xa28]
    // 0xb97a44: ArrayStore: r1[0] = r0  ; List_4
    //     0xb97a44: stur            w0, [x1, #0x17]
    // 0xb97a48: r0 = ClipRRect()
    //     0xb97a48: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb97a4c: mov             x1, x0
    // 0xb97a50: ldur            x0, [fp, #-0x50]
    // 0xb97a54: stur            x1, [fp, #-0x10]
    // 0xb97a58: StoreField: r1->field_f = r0
    //     0xb97a58: stur            w0, [x1, #0xf]
    // 0xb97a5c: r0 = Instance_Clip
    //     0xb97a5c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xb97a60: ldr             x0, [x0, #0x4f8]
    // 0xb97a64: ArrayStore: r1[0] = r0  ; List_4
    //     0xb97a64: stur            w0, [x1, #0x17]
    // 0xb97a68: ldur            x0, [fp, #-0x48]
    // 0xb97a6c: StoreField: r1->field_b = r0
    //     0xb97a6c: stur            w0, [x1, #0xb]
    // 0xb97a70: r0 = AspectRatio()
    //     0xb97a70: bl              #0x9d2c98  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb97a74: d0 = 1.780952
    //     0xb97a74: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2ef20] IMM: double(1.7809523809523808) from 0x3ffc7ec7ec7ec7ec
    //     0xb97a78: ldr             d0, [x17, #0xf20]
    // 0xb97a7c: stur            x0, [fp, #-0x48]
    // 0xb97a80: StoreField: r0->field_f = d0
    //     0xb97a80: stur            d0, [x0, #0xf]
    // 0xb97a84: ldur            x1, [fp, #-0x10]
    // 0xb97a88: StoreField: r0->field_b = r1
    //     0xb97a88: stur            w1, [x0, #0xb]
    // 0xb97a8c: r1 = Null
    //     0xb97a8c: mov             x1, NULL
    // 0xb97a90: r2 = 4
    //     0xb97a90: movz            x2, #0x4
    // 0xb97a94: r0 = AllocateArray()
    //     0xb97a94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb97a98: mov             x2, x0
    // 0xb97a9c: ldur            x0, [fp, #-0x48]
    // 0xb97aa0: stur            x2, [fp, #-0x10]
    // 0xb97aa4: StoreField: r2->field_f = r0
    //     0xb97aa4: stur            w0, [x2, #0xf]
    // 0xb97aa8: r16 = Instance_SizedBox
    //     0xb97aa8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb97aac: ldr             x16, [x16, #0x448]
    // 0xb97ab0: StoreField: r2->field_13 = r16
    //     0xb97ab0: stur            w16, [x2, #0x13]
    // 0xb97ab4: r1 = <Widget>
    //     0xb97ab4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb97ab8: r0 = AllocateGrowableArray()
    //     0xb97ab8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb97abc: mov             x1, x0
    // 0xb97ac0: ldur            x0, [fp, #-0x10]
    // 0xb97ac4: StoreField: r1->field_f = r0
    //     0xb97ac4: stur            w0, [x1, #0xf]
    // 0xb97ac8: r0 = 4
    //     0xb97ac8: movz            x0, #0x4
    // 0xb97acc: StoreField: r1->field_b = r0
    //     0xb97acc: stur            w0, [x1, #0xb]
    // 0xb97ad0: mov             x2, x1
    // 0xb97ad4: ldur            x1, [fp, #-0x40]
    // 0xb97ad8: r0 = addAll()
    //     0xb97ad8: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb97adc: ldur            x0, [fp, #-0x40]
    // 0xb97ae0: ldur            x2, [fp, #-0x18]
    // 0xb97ae4: r1 = Function '<anonymous closure>':.
    //     0xb97ae4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a70] AnonymousClosure: (0xb97f78), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/views/encyclopedia_detail_view.dart] EncylopediaDetailLayout::build (0xb9764c)
    //     0xb97ae8: ldr             x1, [x1, #0xa70]
    // 0xb97aec: r0 = AllocateClosure()
    //     0xb97aec: bl              #0xec1630  ; AllocateClosureStub
    // 0xb97af0: r1 = <ReadingPreferenceController>
    //     0xb97af0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb97af4: ldr             x1, [x1, #0xe10]
    // 0xb97af8: stur            x0, [fp, #-0x10]
    // 0xb97afc: r0 = ReadingPreferenceWidget()
    //     0xb97afc: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb97b00: mov             x2, x0
    // 0xb97b04: ldur            x0, [fp, #-0x10]
    // 0xb97b08: stur            x2, [fp, #-0x18]
    // 0xb97b0c: StoreField: r2->field_13 = r0
    //     0xb97b0c: stur            w0, [x2, #0x13]
    // 0xb97b10: ldur            x0, [fp, #-0x40]
    // 0xb97b14: LoadField: r1 = r0->field_b
    //     0xb97b14: ldur            w1, [x0, #0xb]
    // 0xb97b18: LoadField: r3 = r0->field_f
    //     0xb97b18: ldur            w3, [x0, #0xf]
    // 0xb97b1c: DecompressPointer r3
    //     0xb97b1c: add             x3, x3, HEAP, lsl #32
    // 0xb97b20: LoadField: r4 = r3->field_b
    //     0xb97b20: ldur            w4, [x3, #0xb]
    // 0xb97b24: r3 = LoadInt32Instr(r1)
    //     0xb97b24: sbfx            x3, x1, #1, #0x1f
    // 0xb97b28: stur            x3, [fp, #-0x58]
    // 0xb97b2c: r1 = LoadInt32Instr(r4)
    //     0xb97b2c: sbfx            x1, x4, #1, #0x1f
    // 0xb97b30: cmp             x3, x1
    // 0xb97b34: b.ne            #0xb97b40
    // 0xb97b38: mov             x1, x0
    // 0xb97b3c: r0 = _growToNextCapacity()
    //     0xb97b3c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97b40: ldur            x4, [fp, #-8]
    // 0xb97b44: ldur            x2, [fp, #-0x40]
    // 0xb97b48: ldur            x3, [fp, #-0x58]
    // 0xb97b4c: add             x5, x3, #1
    // 0xb97b50: stur            x5, [fp, #-0x60]
    // 0xb97b54: lsl             x0, x5, #1
    // 0xb97b58: StoreField: r2->field_b = r0
    //     0xb97b58: stur            w0, [x2, #0xb]
    // 0xb97b5c: LoadField: r6 = r2->field_f
    //     0xb97b5c: ldur            w6, [x2, #0xf]
    // 0xb97b60: DecompressPointer r6
    //     0xb97b60: add             x6, x6, HEAP, lsl #32
    // 0xb97b64: mov             x1, x6
    // 0xb97b68: ldur            x0, [fp, #-0x18]
    // 0xb97b6c: stur            x6, [fp, #-0x48]
    // 0xb97b70: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97b70: add             x25, x1, x3, lsl #2
    //     0xb97b74: add             x25, x25, #0xf
    //     0xb97b78: str             w0, [x25]
    //     0xb97b7c: tbz             w0, #0, #0xb97b98
    //     0xb97b80: ldurb           w16, [x1, #-1]
    //     0xb97b84: ldurb           w17, [x0, #-1]
    //     0xb97b88: and             x16, x17, x16, lsr #2
    //     0xb97b8c: tst             x16, HEAP, lsr #32
    //     0xb97b90: b.eq            #0xb97b98
    //     0xb97b94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb97b98: LoadField: r0 = r4->field_2f
    //     0xb97b98: ldur            w0, [x4, #0x2f]
    // 0xb97b9c: DecompressPointer r0
    //     0xb97b9c: add             x0, x0, HEAP, lsl #32
    // 0xb97ba0: stur            x0, [fp, #-0x18]
    // 0xb97ba4: LoadField: r1 = r4->field_33
    //     0xb97ba4: ldur            w1, [x4, #0x33]
    // 0xb97ba8: DecompressPointer r1
    //     0xb97ba8: add             x1, x1, HEAP, lsl #32
    // 0xb97bac: stur            x1, [fp, #-0x10]
    // 0xb97bb0: r0 = RelatedContentWidget()
    //     0xb97bb0: bl              #0xaf9214  ; AllocateRelatedContentWidgetStub -> RelatedContentWidget (size=0x18)
    // 0xb97bb4: mov             x2, x0
    // 0xb97bb8: ldur            x0, [fp, #-0x18]
    // 0xb97bbc: stur            x2, [fp, #-8]
    // 0xb97bc0: StoreField: r2->field_b = r0
    //     0xb97bc0: stur            w0, [x2, #0xb]
    // 0xb97bc4: ldur            x0, [fp, #-0x10]
    // 0xb97bc8: StoreField: r2->field_f = r0
    //     0xb97bc8: stur            w0, [x2, #0xf]
    // 0xb97bcc: r0 = Instance_EdgeInsets
    //     0xb97bcc: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb97bd0: StoreField: r2->field_13 = r0
    //     0xb97bd0: stur            w0, [x2, #0x13]
    // 0xb97bd4: ldur            x0, [fp, #-0x48]
    // 0xb97bd8: LoadField: r1 = r0->field_b
    //     0xb97bd8: ldur            w1, [x0, #0xb]
    // 0xb97bdc: r0 = LoadInt32Instr(r1)
    //     0xb97bdc: sbfx            x0, x1, #1, #0x1f
    // 0xb97be0: ldur            x3, [fp, #-0x60]
    // 0xb97be4: cmp             x3, x0
    // 0xb97be8: b.ne            #0xb97bf4
    // 0xb97bec: ldur            x1, [fp, #-0x40]
    // 0xb97bf0: r0 = _growToNextCapacity()
    //     0xb97bf0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97bf4: ldur            x3, [fp, #-0x40]
    // 0xb97bf8: ldur            x2, [fp, #-0x60]
    // 0xb97bfc: ldur            x4, [fp, #-0x38]
    // 0xb97c00: add             x0, x2, #1
    // 0xb97c04: lsl             x1, x0, #1
    // 0xb97c08: StoreField: r3->field_b = r1
    //     0xb97c08: stur            w1, [x3, #0xb]
    // 0xb97c0c: LoadField: r1 = r3->field_f
    //     0xb97c0c: ldur            w1, [x3, #0xf]
    // 0xb97c10: DecompressPointer r1
    //     0xb97c10: add             x1, x1, HEAP, lsl #32
    // 0xb97c14: ldur            x0, [fp, #-8]
    // 0xb97c18: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb97c18: add             x25, x1, x2, lsl #2
    //     0xb97c1c: add             x25, x25, #0xf
    //     0xb97c20: str             w0, [x25]
    //     0xb97c24: tbz             w0, #0, #0xb97c40
    //     0xb97c28: ldurb           w16, [x1, #-1]
    //     0xb97c2c: ldurb           w17, [x0, #-1]
    //     0xb97c30: and             x16, x17, x16, lsr #2
    //     0xb97c34: tst             x16, HEAP, lsr #32
    //     0xb97c38: b.eq            #0xb97c40
    //     0xb97c3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb97c40: r0 = Column()
    //     0xb97c40: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb97c44: mov             x2, x0
    // 0xb97c48: r0 = Instance_Axis
    //     0xb97c48: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb97c4c: stur            x2, [fp, #-8]
    // 0xb97c50: StoreField: r2->field_f = r0
    //     0xb97c50: stur            w0, [x2, #0xf]
    // 0xb97c54: r3 = Instance_MainAxisAlignment
    //     0xb97c54: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb97c58: ldr             x3, [x3, #0x730]
    // 0xb97c5c: StoreField: r2->field_13 = r3
    //     0xb97c5c: stur            w3, [x2, #0x13]
    // 0xb97c60: r4 = Instance_MainAxisSize
    //     0xb97c60: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb97c64: ldr             x4, [x4, #0x738]
    // 0xb97c68: ArrayStore: r2[0] = r4  ; List_4
    //     0xb97c68: stur            w4, [x2, #0x17]
    // 0xb97c6c: r5 = Instance_CrossAxisAlignment
    //     0xb97c6c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb97c70: ldr             x5, [x5, #0x740]
    // 0xb97c74: StoreField: r2->field_1b = r5
    //     0xb97c74: stur            w5, [x2, #0x1b]
    // 0xb97c78: r6 = Instance_VerticalDirection
    //     0xb97c78: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb97c7c: ldr             x6, [x6, #0x748]
    // 0xb97c80: StoreField: r2->field_23 = r6
    //     0xb97c80: stur            w6, [x2, #0x23]
    // 0xb97c84: r7 = Instance_Clip
    //     0xb97c84: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb97c88: ldr             x7, [x7, #0x750]
    // 0xb97c8c: StoreField: r2->field_2b = r7
    //     0xb97c8c: stur            w7, [x2, #0x2b]
    // 0xb97c90: StoreField: r2->field_2f = rZR
    //     0xb97c90: stur            xzr, [x2, #0x2f]
    // 0xb97c94: ldur            x1, [fp, #-0x40]
    // 0xb97c98: StoreField: r2->field_b = r1
    //     0xb97c98: stur            w1, [x2, #0xb]
    // 0xb97c9c: ldur            x8, [fp, #-0x38]
    // 0xb97ca0: LoadField: r1 = r8->field_b
    //     0xb97ca0: ldur            w1, [x8, #0xb]
    // 0xb97ca4: LoadField: r9 = r8->field_f
    //     0xb97ca4: ldur            w9, [x8, #0xf]
    // 0xb97ca8: DecompressPointer r9
    //     0xb97ca8: add             x9, x9, HEAP, lsl #32
    // 0xb97cac: LoadField: r10 = r9->field_b
    //     0xb97cac: ldur            w10, [x9, #0xb]
    // 0xb97cb0: r9 = LoadInt32Instr(r1)
    //     0xb97cb0: sbfx            x9, x1, #1, #0x1f
    // 0xb97cb4: stur            x9, [fp, #-0x58]
    // 0xb97cb8: r1 = LoadInt32Instr(r10)
    //     0xb97cb8: sbfx            x1, x10, #1, #0x1f
    // 0xb97cbc: cmp             x9, x1
    // 0xb97cc0: b.ne            #0xb97ccc
    // 0xb97cc4: mov             x1, x8
    // 0xb97cc8: r0 = _growToNextCapacity()
    //     0xb97cc8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97ccc: ldur            x2, [fp, #-0x38]
    // 0xb97cd0: ldur            x3, [fp, #-0x58]
    // 0xb97cd4: add             x0, x3, #1
    // 0xb97cd8: lsl             x1, x0, #1
    // 0xb97cdc: StoreField: r2->field_b = r1
    //     0xb97cdc: stur            w1, [x2, #0xb]
    // 0xb97ce0: LoadField: r1 = r2->field_f
    //     0xb97ce0: ldur            w1, [x2, #0xf]
    // 0xb97ce4: DecompressPointer r1
    //     0xb97ce4: add             x1, x1, HEAP, lsl #32
    // 0xb97ce8: ldur            x0, [fp, #-8]
    // 0xb97cec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97cec: add             x25, x1, x3, lsl #2
    //     0xb97cf0: add             x25, x25, #0xf
    //     0xb97cf4: str             w0, [x25]
    //     0xb97cf8: tbz             w0, #0, #0xb97d14
    //     0xb97cfc: ldurb           w16, [x1, #-1]
    //     0xb97d00: ldurb           w17, [x0, #-1]
    //     0xb97d04: and             x16, x17, x16, lsr #2
    //     0xb97d08: tst             x16, HEAP, lsr #32
    //     0xb97d0c: b.eq            #0xb97d14
    //     0xb97d10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb97d14: b               #0xb97d1c
    // 0xb97d18: mov             x2, x0
    // 0xb97d1c: ldur            x0, [fp, #-0x30]
    // 0xb97d20: ldur            x1, [fp, #-0x28]
    // 0xb97d24: ldur            x3, [fp, #-0x20]
    // 0xb97d28: r0 = ListView()
    //     0xb97d28: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb97d2c: stur            x0, [fp, #-8]
    // 0xb97d30: r16 = Instance_EdgeInsets
    //     0xb97d30: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb97d34: str             x16, [SP]
    // 0xb97d38: mov             x1, x0
    // 0xb97d3c: ldur            x2, [fp, #-0x38]
    // 0xb97d40: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb97d40: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb97d44: ldr             x4, [x4, #0xa0]
    // 0xb97d48: r0 = ListView()
    //     0xb97d48: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb97d4c: r0 = Scrollbar()
    //     0xb97d4c: bl              #0xaf9208  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0xb97d50: mov             x2, x0
    // 0xb97d54: ldur            x0, [fp, #-8]
    // 0xb97d58: stur            x2, [fp, #-0x10]
    // 0xb97d5c: StoreField: r2->field_b = r0
    //     0xb97d5c: stur            w0, [x2, #0xb]
    // 0xb97d60: r1 = <ScrollUpdateNotification>
    //     0xb97d60: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da78] TypeArguments: <ScrollUpdateNotification>
    //     0xb97d64: ldr             x1, [x1, #0xa78]
    // 0xb97d68: r0 = NotificationListener()
    //     0xb97d68: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xb97d6c: mov             x1, x0
    // 0xb97d70: ldur            x0, [fp, #-0x20]
    // 0xb97d74: stur            x1, [fp, #-8]
    // 0xb97d78: StoreField: r1->field_13 = r0
    //     0xb97d78: stur            w0, [x1, #0x13]
    // 0xb97d7c: ldur            x0, [fp, #-0x10]
    // 0xb97d80: StoreField: r1->field_b = r0
    //     0xb97d80: stur            w0, [x1, #0xb]
    // 0xb97d84: r0 = RefreshIndicator()
    //     0xb97d84: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xb97d88: mov             x2, x0
    // 0xb97d8c: ldur            x0, [fp, #-8]
    // 0xb97d90: stur            x2, [fp, #-0x10]
    // 0xb97d94: StoreField: r2->field_b = r0
    //     0xb97d94: stur            w0, [x2, #0xb]
    // 0xb97d98: d0 = 40.000000
    //     0xb97d98: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xb97d9c: StoreField: r2->field_f = d0
    //     0xb97d9c: stur            d0, [x2, #0xf]
    // 0xb97da0: ArrayStore: r2[0] = rZR  ; List_8
    //     0xb97da0: stur            xzr, [x2, #0x17]
    // 0xb97da4: ldur            x0, [fp, #-0x28]
    // 0xb97da8: StoreField: r2->field_1f = r0
    //     0xb97da8: stur            w0, [x2, #0x1f]
    // 0xb97dac: r0 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xb97dac: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xb97db0: ldr             x0, [x0, #0xf58]
    // 0xb97db4: StoreField: r2->field_2f = r0
    //     0xb97db4: stur            w0, [x2, #0x2f]
    // 0xb97db8: d0 = 2.500000
    //     0xb97db8: fmov            d0, #2.50000000
    // 0xb97dbc: StoreField: r2->field_3b = d0
    //     0xb97dbc: stur            d0, [x2, #0x3b]
    // 0xb97dc0: r0 = Instance_RefreshIndicatorTriggerMode
    //     0xb97dc0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xb97dc4: ldr             x0, [x0, #0xa68]
    // 0xb97dc8: StoreField: r2->field_47 = r0
    //     0xb97dc8: stur            w0, [x2, #0x47]
    // 0xb97dcc: d0 = 2.000000
    //     0xb97dcc: fmov            d0, #2.00000000
    // 0xb97dd0: StoreField: r2->field_4b = d0
    //     0xb97dd0: stur            d0, [x2, #0x4b]
    // 0xb97dd4: r0 = Instance__IndicatorType
    //     0xb97dd4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xb97dd8: ldr             x0, [x0, #0xa70]
    // 0xb97ddc: StoreField: r2->field_43 = r0
    //     0xb97ddc: stur            w0, [x2, #0x43]
    // 0xb97de0: r1 = <FlexParentData>
    //     0xb97de0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb97de4: ldr             x1, [x1, #0x720]
    // 0xb97de8: r0 = Expanded()
    //     0xb97de8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb97dec: mov             x3, x0
    // 0xb97df0: r0 = 1
    //     0xb97df0: movz            x0, #0x1
    // 0xb97df4: stur            x3, [fp, #-8]
    // 0xb97df8: StoreField: r3->field_13 = r0
    //     0xb97df8: stur            x0, [x3, #0x13]
    // 0xb97dfc: r0 = Instance_FlexFit
    //     0xb97dfc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb97e00: ldr             x0, [x0, #0x728]
    // 0xb97e04: StoreField: r3->field_1b = r0
    //     0xb97e04: stur            w0, [x3, #0x1b]
    // 0xb97e08: ldur            x0, [fp, #-0x10]
    // 0xb97e0c: StoreField: r3->field_b = r0
    //     0xb97e0c: stur            w0, [x3, #0xb]
    // 0xb97e10: r1 = Null
    //     0xb97e10: mov             x1, NULL
    // 0xb97e14: r2 = 2
    //     0xb97e14: movz            x2, #0x2
    // 0xb97e18: r0 = AllocateArray()
    //     0xb97e18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb97e1c: mov             x2, x0
    // 0xb97e20: ldur            x0, [fp, #-8]
    // 0xb97e24: stur            x2, [fp, #-0x10]
    // 0xb97e28: StoreField: r2->field_f = r0
    //     0xb97e28: stur            w0, [x2, #0xf]
    // 0xb97e2c: r1 = <Widget>
    //     0xb97e2c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb97e30: r0 = AllocateGrowableArray()
    //     0xb97e30: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb97e34: mov             x1, x0
    // 0xb97e38: ldur            x0, [fp, #-0x10]
    // 0xb97e3c: stur            x1, [fp, #-8]
    // 0xb97e40: StoreField: r1->field_f = r0
    //     0xb97e40: stur            w0, [x1, #0xf]
    // 0xb97e44: r0 = 2
    //     0xb97e44: movz            x0, #0x2
    // 0xb97e48: StoreField: r1->field_b = r0
    //     0xb97e48: stur            w0, [x1, #0xb]
    // 0xb97e4c: r0 = AdmobBannerWidget()
    //     0xb97e4c: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xb97e50: mov             x2, x0
    // 0xb97e54: r0 = "gam"
    //     0xb97e54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e7d0] "gam"
    //     0xb97e58: ldr             x0, [x0, #0x7d0]
    // 0xb97e5c: stur            x2, [fp, #-0x10]
    // 0xb97e60: StoreField: r2->field_b = r0
    //     0xb97e60: stur            w0, [x2, #0xb]
    // 0xb97e64: ldur            x1, [fp, #-8]
    // 0xb97e68: r0 = _growToNextCapacity()
    //     0xb97e68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97e6c: ldur            x2, [fp, #-8]
    // 0xb97e70: r0 = 4
    //     0xb97e70: movz            x0, #0x4
    // 0xb97e74: StoreField: r2->field_b = r0
    //     0xb97e74: stur            w0, [x2, #0xb]
    // 0xb97e78: LoadField: r1 = r2->field_f
    //     0xb97e78: ldur            w1, [x2, #0xf]
    // 0xb97e7c: DecompressPointer r1
    //     0xb97e7c: add             x1, x1, HEAP, lsl #32
    // 0xb97e80: ldur            x0, [fp, #-0x10]
    // 0xb97e84: ArrayStore: r1[1] = r0  ; List_4
    //     0xb97e84: add             x25, x1, #0x13
    //     0xb97e88: str             w0, [x25]
    //     0xb97e8c: tbz             w0, #0, #0xb97ea8
    //     0xb97e90: ldurb           w16, [x1, #-1]
    //     0xb97e94: ldurb           w17, [x0, #-1]
    //     0xb97e98: and             x16, x17, x16, lsr #2
    //     0xb97e9c: tst             x16, HEAP, lsr #32
    //     0xb97ea0: b.eq            #0xb97ea8
    //     0xb97ea4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb97ea8: r0 = Column()
    //     0xb97ea8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb97eac: mov             x1, x0
    // 0xb97eb0: r0 = Instance_Axis
    //     0xb97eb0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb97eb4: stur            x1, [fp, #-0x10]
    // 0xb97eb8: StoreField: r1->field_f = r0
    //     0xb97eb8: stur            w0, [x1, #0xf]
    // 0xb97ebc: r0 = Instance_MainAxisAlignment
    //     0xb97ebc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb97ec0: ldr             x0, [x0, #0x730]
    // 0xb97ec4: StoreField: r1->field_13 = r0
    //     0xb97ec4: stur            w0, [x1, #0x13]
    // 0xb97ec8: r0 = Instance_MainAxisSize
    //     0xb97ec8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb97ecc: ldr             x0, [x0, #0x738]
    // 0xb97ed0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb97ed0: stur            w0, [x1, #0x17]
    // 0xb97ed4: r0 = Instance_CrossAxisAlignment
    //     0xb97ed4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb97ed8: ldr             x0, [x0, #0x740]
    // 0xb97edc: StoreField: r1->field_1b = r0
    //     0xb97edc: stur            w0, [x1, #0x1b]
    // 0xb97ee0: r0 = Instance_VerticalDirection
    //     0xb97ee0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb97ee4: ldr             x0, [x0, #0x748]
    // 0xb97ee8: StoreField: r1->field_23 = r0
    //     0xb97ee8: stur            w0, [x1, #0x23]
    // 0xb97eec: r0 = Instance_Clip
    //     0xb97eec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb97ef0: ldr             x0, [x0, #0x750]
    // 0xb97ef4: StoreField: r1->field_2b = r0
    //     0xb97ef4: stur            w0, [x1, #0x2b]
    // 0xb97ef8: StoreField: r1->field_2f = rZR
    //     0xb97ef8: stur            xzr, [x1, #0x2f]
    // 0xb97efc: ldur            x0, [fp, #-8]
    // 0xb97f00: StoreField: r1->field_b = r0
    //     0xb97f00: stur            w0, [x1, #0xb]
    // 0xb97f04: r0 = Scaffold()
    //     0xb97f04: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb97f08: ldur            x1, [fp, #-0x30]
    // 0xb97f0c: StoreField: r0->field_13 = r1
    //     0xb97f0c: stur            w1, [x0, #0x13]
    // 0xb97f10: ldur            x1, [fp, #-0x10]
    // 0xb97f14: ArrayStore: r0[0] = r1  ; List_4
    //     0xb97f14: stur            w1, [x0, #0x17]
    // 0xb97f18: r1 = Instance_AlignmentDirectional
    //     0xb97f18: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb97f1c: ldr             x1, [x1, #0x758]
    // 0xb97f20: StoreField: r0->field_2b = r1
    //     0xb97f20: stur            w1, [x0, #0x2b]
    // 0xb97f24: r1 = true
    //     0xb97f24: add             x1, NULL, #0x20  ; true
    // 0xb97f28: StoreField: r0->field_53 = r1
    //     0xb97f28: stur            w1, [x0, #0x53]
    // 0xb97f2c: r2 = Instance_DragStartBehavior
    //     0xb97f2c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb97f30: StoreField: r0->field_57 = r2
    //     0xb97f30: stur            w2, [x0, #0x57]
    // 0xb97f34: r2 = false
    //     0xb97f34: add             x2, NULL, #0x30  ; false
    // 0xb97f38: StoreField: r0->field_b = r2
    //     0xb97f38: stur            w2, [x0, #0xb]
    // 0xb97f3c: StoreField: r0->field_f = r2
    //     0xb97f3c: stur            w2, [x0, #0xf]
    // 0xb97f40: StoreField: r0->field_5f = r1
    //     0xb97f40: stur            w1, [x0, #0x5f]
    // 0xb97f44: StoreField: r0->field_63 = r1
    //     0xb97f44: stur            w1, [x0, #0x63]
    // 0xb97f48: LeaveFrame
    //     0xb97f48: mov             SP, fp
    //     0xb97f4c: ldp             fp, lr, [SP], #0x10
    // 0xb97f50: ret
    //     0xb97f50: ret             
    // 0xb97f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97f58: b               #0xb97668
    // 0xb97f5c: SaveReg d0
    //     0xb97f5c: str             q0, [SP, #-0x10]!
    // 0xb97f60: stp             x0, x1, [SP, #-0x10]!
    // 0xb97f64: r0 = AllocateDouble()
    //     0xb97f64: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb97f68: mov             x2, x0
    // 0xb97f6c: ldp             x0, x1, [SP], #0x10
    // 0xb97f70: RestoreReg d0
    //     0xb97f70: ldr             q0, [SP], #0x10
    // 0xb97f74: b               #0xb977f0
  }
  [closure] NotificationListener<Notification> <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb97f78, size: 0xb4
    // 0xb97f78: EnterFrame
    //     0xb97f78: stp             fp, lr, [SP, #-0x10]!
    //     0xb97f7c: mov             fp, SP
    // 0xb97f80: AllocStack(0x10)
    //     0xb97f80: sub             SP, SP, #0x10
    // 0xb97f84: SetupParameters()
    //     0xb97f84: ldr             x0, [fp, #0x20]
    //     0xb97f88: ldur            w1, [x0, #0x17]
    //     0xb97f8c: add             x1, x1, HEAP, lsl #32
    // 0xb97f90: LoadField: r0 = r1->field_f
    //     0xb97f90: ldur            w0, [x1, #0xf]
    // 0xb97f94: DecompressPointer r0
    //     0xb97f94: add             x0, x0, HEAP, lsl #32
    // 0xb97f98: LoadField: r1 = r0->field_23
    //     0xb97f98: ldur            w1, [x0, #0x23]
    // 0xb97f9c: DecompressPointer r1
    //     0xb97f9c: add             x1, x1, HEAP, lsl #32
    // 0xb97fa0: stur            x1, [fp, #-8]
    // 0xb97fa4: r0 = ArticleContentHtml()
    //     0xb97fa4: bl              #0xa36cb4  ; AllocateArticleContentHtmlStub -> ArticleContentHtml (size=0x30)
    // 0xb97fa8: mov             x3, x0
    // 0xb97fac: ldur            x0, [fp, #-8]
    // 0xb97fb0: stur            x3, [fp, #-0x10]
    // 0xb97fb4: StoreField: r3->field_b = r0
    //     0xb97fb4: stur            w0, [x3, #0xb]
    // 0xb97fb8: ldr             x0, [fp, #0x18]
    // 0xb97fbc: StoreField: r3->field_f = r0
    //     0xb97fbc: stur            w0, [x3, #0xf]
    // 0xb97fc0: r0 = true
    //     0xb97fc0: add             x0, NULL, #0x20  ; true
    // 0xb97fc4: StoreField: r3->field_13 = r0
    //     0xb97fc4: stur            w0, [x3, #0x13]
    // 0xb97fc8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb97fc8: stur            w0, [x3, #0x17]
    // 0xb97fcc: r0 = const []
    //     0xb97fcc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d530] List<ArticleInsertion>(0)
    //     0xb97fd0: ldr             x0, [x0, #0x530]
    // 0xb97fd4: StoreField: r3->field_23 = r0
    //     0xb97fd4: stur            w0, [x3, #0x23]
    // 0xb97fd8: StoreField: r3->field_27 = r0
    //     0xb97fd8: stur            w0, [x3, #0x27]
    // 0xb97fdc: r0 = const []
    //     0xb97fdc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d538] List<Author>(0)
    //     0xb97fe0: ldr             x0, [x0, #0x538]
    // 0xb97fe4: StoreField: r3->field_1f = r0
    //     0xb97fe4: stur            w0, [x3, #0x1f]
    // 0xb97fe8: r0 = false
    //     0xb97fe8: add             x0, NULL, #0x30  ; false
    // 0xb97fec: StoreField: r3->field_2b = r0
    //     0xb97fec: stur            w0, [x3, #0x2b]
    // 0xb97ff0: r1 = Function '<anonymous closure>':.
    //     0xb97ff0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a78] Function: [dart:core] Object::_simpleInstanceOfTrue (0xebbd8c)
    //     0xb97ff4: ldr             x1, [x1, #0xa78]
    // 0xb97ff8: r2 = Null
    //     0xb97ff8: mov             x2, NULL
    // 0xb97ffc: r0 = AllocateClosure()
    //     0xb97ffc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb98000: r1 = <Notification>
    //     0xb98000: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] TypeArguments: <Notification>
    //     0xb98004: ldr             x1, [x1, #0x9b8]
    // 0xb98008: stur            x0, [fp, #-8]
    // 0xb9800c: r0 = NotificationListener()
    //     0xb9800c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xb98010: ldur            x1, [fp, #-8]
    // 0xb98014: StoreField: r0->field_13 = r1
    //     0xb98014: stur            w1, [x0, #0x13]
    // 0xb98018: ldur            x1, [fp, #-0x10]
    // 0xb9801c: StoreField: r0->field_b = r1
    //     0xb9801c: stur            w1, [x0, #0xb]
    // 0xb98020: LeaveFrame
    //     0xb98020: mov             SP, fp
    //     0xb98024: ldp             fp, lr, [SP], #0x10
    // 0xb98028: ret
    //     0xb98028: ret             
  }
}

// class id: 5281, size: 0x14, field offset: 0x14
class EncyclopediaDetailView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf17e8, size: 0x15c
    // 0xaf17e8: EnterFrame
    //     0xaf17e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf17ec: mov             fp, SP
    // 0xaf17f0: AllocStack(0x58)
    //     0xaf17f0: sub             SP, SP, #0x58
    // 0xaf17f4: SetupParameters(EncyclopediaDetailView this /* r1 => r1, fp-0x8 */)
    //     0xaf17f4: stur            x1, [fp, #-8]
    // 0xaf17f8: CheckStackOverflow
    //     0xaf17f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf17fc: cmp             SP, x16
    //     0xaf1800: b.ls            #0xaf193c
    // 0xaf1804: r1 = 1
    //     0xaf1804: movz            x1, #0x1
    // 0xaf1808: r0 = AllocateContext()
    //     0xaf1808: bl              #0xec126c  ; AllocateContextStub
    // 0xaf180c: mov             x2, x0
    // 0xaf1810: ldur            x0, [fp, #-8]
    // 0xaf1814: stur            x2, [fp, #-0x10]
    // 0xaf1818: StoreField: r2->field_f = r0
    //     0xaf1818: stur            w0, [x2, #0xf]
    // 0xaf181c: mov             x1, x0
    // 0xaf1820: r0 = controller()
    //     0xaf1820: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1824: ldur            x1, [fp, #-8]
    // 0xaf1828: stur            x0, [fp, #-0x18]
    // 0xaf182c: r0 = controller()
    //     0xaf182c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1830: LoadField: r2 = r0->field_3b
    //     0xaf1830: ldur            w2, [x0, #0x3b]
    // 0xaf1834: DecompressPointer r2
    //     0xaf1834: add             x2, x2, HEAP, lsl #32
    // 0xaf1838: ldur            x1, [fp, #-8]
    // 0xaf183c: stur            x2, [fp, #-0x20]
    // 0xaf1840: r0 = controller()
    //     0xaf1840: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1844: ldur            x1, [fp, #-8]
    // 0xaf1848: stur            x0, [fp, #-8]
    // 0xaf184c: r0 = controller()
    //     0xaf184c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1850: stur            x0, [fp, #-0x28]
    // 0xaf1854: r0 = EncylopediaDetailLayout()
    //     0xaf1854: bl              #0xaf1944  ; AllocateEncylopediaDetailLayoutStub -> EncylopediaDetailLayout (size=0x38)
    // 0xaf1858: mov             x3, x0
    // 0xaf185c: r0 = true
    //     0xaf185c: add             x0, NULL, #0x20  ; true
    // 0xaf1860: stur            x3, [fp, #-0x30]
    // 0xaf1864: StoreField: r3->field_b = r0
    //     0xaf1864: stur            w0, [x3, #0xb]
    // 0xaf1868: ldur            x1, [fp, #-0x20]
    // 0xaf186c: StoreField: r3->field_f = r1
    //     0xaf186c: stur            w1, [x3, #0xf]
    // 0xaf1870: ldur            x2, [fp, #-8]
    // 0xaf1874: r1 = Function 'onRefresh':.
    //     0xaf1874: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f258] AnonymousClosure: (0xada8f0), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xaf1878: ldr             x1, [x1, #0x258]
    // 0xaf187c: r0 = AllocateClosure()
    //     0xaf187c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf1880: mov             x1, x0
    // 0xaf1884: ldur            x0, [fp, #-0x30]
    // 0xaf1888: StoreField: r0->field_13 = r1
    //     0xaf1888: stur            w1, [x0, #0x13]
    // 0xaf188c: ldur            x2, [fp, #-0x28]
    // 0xaf1890: r1 = Function 'onScrollNotificationUpdated':.
    //     0xaf1890: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f260] AnonymousClosure: (0xaf1c20), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart] EncyclopediaDetailController::onScrollNotificationUpdated (0xaf1c5c)
    //     0xaf1894: ldr             x1, [x1, #0x260]
    // 0xaf1898: r0 = AllocateClosure()
    //     0xaf1898: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf189c: mov             x1, x0
    // 0xaf18a0: ldur            x0, [fp, #-0x30]
    // 0xaf18a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf18a4: stur            w1, [x0, #0x17]
    // 0xaf18a8: r1 = ""
    //     0xaf18a8: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf18ac: StoreField: r0->field_23 = r1
    //     0xaf18ac: stur            w1, [x0, #0x23]
    // 0xaf18b0: r2 = true
    //     0xaf18b0: add             x2, NULL, #0x20  ; true
    // 0xaf18b4: StoreField: r0->field_1b = r2
    //     0xaf18b4: stur            w2, [x0, #0x1b]
    // 0xaf18b8: r3 = "gam"
    //     0xaf18b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e7d0] "gam"
    //     0xaf18bc: ldr             x3, [x3, #0x7d0]
    // 0xaf18c0: StoreField: r0->field_1f = r3
    //     0xaf18c0: stur            w3, [x0, #0x1f]
    // 0xaf18c4: StoreField: r0->field_27 = r1
    //     0xaf18c4: stur            w1, [x0, #0x27]
    // 0xaf18c8: StoreField: r0->field_2b = r2
    //     0xaf18c8: stur            w2, [x0, #0x2b]
    // 0xaf18cc: r1 = const []
    //     0xaf18cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7d8] List<RelatedContent<int>>(0)
    //     0xaf18d0: ldr             x1, [x1, #0x7d8]
    // 0xaf18d4: StoreField: r0->field_2f = r1
    //     0xaf18d4: stur            w1, [x0, #0x2f]
    // 0xaf18d8: r1 = const []
    //     0xaf18d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7e0] List<RelatedContent<String>>(0)
    //     0xaf18dc: ldr             x1, [x1, #0x7e0]
    // 0xaf18e0: StoreField: r0->field_33 = r1
    //     0xaf18e0: stur            w1, [x0, #0x33]
    // 0xaf18e4: ldur            x2, [fp, #-0x10]
    // 0xaf18e8: r1 = Function '<anonymous closure>':.
    //     0xaf18e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f268] AnonymousClosure: (0xaf1a24), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/views/encyclopedia_detail_view.dart] EncyclopediaDetailView::build (0xaf17e8)
    //     0xaf18ec: ldr             x1, [x1, #0x268]
    // 0xaf18f0: r0 = AllocateClosure()
    //     0xaf18f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf18f4: ldur            x2, [fp, #-0x10]
    // 0xaf18f8: r1 = Function '<anonymous closure>':.
    //     0xaf18f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f270] AnonymousClosure: (0xaf1950), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/views/encyclopedia_detail_view.dart] EncyclopediaDetailView::build (0xaf17e8)
    //     0xaf18fc: ldr             x1, [x1, #0x270]
    // 0xaf1900: stur            x0, [fp, #-8]
    // 0xaf1904: r0 = AllocateClosure()
    //     0xaf1904: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf1908: r16 = <Encyclopedia>
    //     0xaf1908: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xaf190c: ldur            lr, [fp, #-0x18]
    // 0xaf1910: stp             lr, x16, [SP, #0x18]
    // 0xaf1914: ldur            x16, [fp, #-8]
    // 0xaf1918: ldur            lr, [fp, #-0x30]
    // 0xaf191c: stp             lr, x16, [SP, #8]
    // 0xaf1920: str             x0, [SP]
    // 0xaf1924: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xaf1924: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xaf1928: ldr             x4, [x4, #0x728]
    // 0xaf192c: r0 = StateExt.obx()
    //     0xaf192c: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaf1930: LeaveFrame
    //     0xaf1930: mov             SP, fp
    //     0xaf1934: ldp             fp, lr, [SP], #0x10
    // 0xaf1938: ret
    //     0xaf1938: ret             
    // 0xaf193c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf193c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1940: b               #0xaf1804
  }
  [closure] NEmptyState <anonymous closure>(dynamic, String?) {
    // ** addr: 0xaf1950, size: 0xd4
    // 0xaf1950: EnterFrame
    //     0xaf1950: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1954: mov             fp, SP
    // 0xaf1958: AllocStack(0x18)
    //     0xaf1958: sub             SP, SP, #0x18
    // 0xaf195c: SetupParameters()
    //     0xaf195c: ldr             x0, [fp, #0x18]
    //     0xaf1960: ldur            w1, [x0, #0x17]
    //     0xaf1964: add             x1, x1, HEAP, lsl #32
    //     0xaf1968: stur            x1, [fp, #-8]
    // 0xaf196c: CheckStackOverflow
    //     0xaf196c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1970: cmp             SP, x16
    //     0xaf1974: b.ls            #0xaf1a1c
    // 0xaf1978: r16 = "ApiError.notFound"
    //     0xaf1978: add             x16, PP, #0x29, lsl #12  ; [pp+0x29730] "ApiError.notFound"
    //     0xaf197c: ldr             x16, [x16, #0x730]
    // 0xaf1980: ldr             lr, [fp, #0x10]
    // 0xaf1984: stp             lr, x16, [SP]
    // 0xaf1988: r0 = ==()
    //     0xaf1988: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xaf198c: tbz             w0, #4, #0xaf19e0
    // 0xaf1990: r16 = "ApiError.noConnection"
    //     0xaf1990: add             x16, PP, #0x29, lsl #12  ; [pp+0x29738] "ApiError.noConnection"
    //     0xaf1994: ldr             x16, [x16, #0x738]
    // 0xaf1998: ldr             lr, [fp, #0x10]
    // 0xaf199c: stp             lr, x16, [SP]
    // 0xaf19a0: r0 = ==()
    //     0xaf19a0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xaf19a4: tbnz            w0, #4, #0xaf19e0
    // 0xaf19a8: ldur            x0, [fp, #-8]
    // 0xaf19ac: LoadField: r1 = r0->field_f
    //     0xaf19ac: ldur            w1, [x0, #0xf]
    // 0xaf19b0: DecompressPointer r1
    //     0xaf19b0: add             x1, x1, HEAP, lsl #32
    // 0xaf19b4: r0 = controller()
    //     0xaf19b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf19b8: mov             x2, x0
    // 0xaf19bc: r1 = Function 'onRefresh':.
    //     0xaf19bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f258] AnonymousClosure: (0xada8f0), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xaf19c0: ldr             x1, [x1, #0x258]
    // 0xaf19c4: r0 = AllocateClosure()
    //     0xaf19c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf19c8: mov             x2, x0
    // 0xaf19cc: r1 = Null
    //     0xaf19cc: mov             x1, NULL
    // 0xaf19d0: r0 = NEmptyState.notConnection()
    //     0xaf19d0: bl              #0xad9f3c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.notConnection
    // 0xaf19d4: LeaveFrame
    //     0xaf19d4: mov             SP, fp
    //     0xaf19d8: ldp             fp, lr, [SP], #0x10
    // 0xaf19dc: ret
    //     0xaf19dc: ret             
    // 0xaf19e0: r0 = NEmptyState()
    //     0xaf19e0: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xaf19e4: mov             x1, x0
    // 0xaf19e8: r2 = "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xaf19e8: add             x2, PP, #0x29, lsl #12  ; [pp+0x297e8] "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xaf19ec: ldr             x2, [x2, #0x7e8]
    // 0xaf19f0: r3 = "assets/images/illustration/no_search.svg"
    //     0xaf19f0: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xaf19f4: ldr             x3, [x3, #0x138]
    // 0xaf19f8: r5 = "Halaman Tidak Ditemukan"
    //     0xaf19f8: add             x5, PP, #0x29, lsl #12  ; [pp+0x292c8] "Halaman Tidak Ditemukan"
    //     0xaf19fc: ldr             x5, [x5, #0x2c8]
    // 0xaf1a00: stur            x0, [fp, #-8]
    // 0xaf1a04: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xaf1a04: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xaf1a08: r0 = NEmptyState.svg()
    //     0xaf1a08: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xaf1a0c: ldur            x0, [fp, #-8]
    // 0xaf1a10: LeaveFrame
    //     0xaf1a10: mov             SP, fp
    //     0xaf1a14: ldp             fp, lr, [SP], #0x10
    // 0xaf1a18: ret
    //     0xaf1a18: ret             
    // 0xaf1a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf1a1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1a20: b               #0xaf1978
  }
  [closure] Obx <anonymous closure>(dynamic, Encyclopedia?) {
    // ** addr: 0xaf1a24, size: 0x6c
    // 0xaf1a24: EnterFrame
    //     0xaf1a24: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1a28: mov             fp, SP
    // 0xaf1a2c: AllocStack(0x10)
    //     0xaf1a2c: sub             SP, SP, #0x10
    // 0xaf1a30: SetupParameters()
    //     0xaf1a30: ldr             x0, [fp, #0x18]
    //     0xaf1a34: ldur            w1, [x0, #0x17]
    //     0xaf1a38: add             x1, x1, HEAP, lsl #32
    //     0xaf1a3c: stur            x1, [fp, #-8]
    // 0xaf1a40: r1 = 1
    //     0xaf1a40: movz            x1, #0x1
    // 0xaf1a44: r0 = AllocateContext()
    //     0xaf1a44: bl              #0xec126c  ; AllocateContextStub
    // 0xaf1a48: mov             x1, x0
    // 0xaf1a4c: ldur            x0, [fp, #-8]
    // 0xaf1a50: stur            x1, [fp, #-0x10]
    // 0xaf1a54: StoreField: r1->field_b = r0
    //     0xaf1a54: stur            w0, [x1, #0xb]
    // 0xaf1a58: ldr             x0, [fp, #0x10]
    // 0xaf1a5c: StoreField: r1->field_f = r0
    //     0xaf1a5c: stur            w0, [x1, #0xf]
    // 0xaf1a60: r0 = Obx()
    //     0xaf1a60: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf1a64: ldur            x2, [fp, #-0x10]
    // 0xaf1a68: r1 = Function '<anonymous closure>':.
    //     0xaf1a68: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f278] AnonymousClosure: (0xaf1a90), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/views/encyclopedia_detail_view.dart] EncyclopediaDetailView::build (0xaf17e8)
    //     0xaf1a6c: ldr             x1, [x1, #0x278]
    // 0xaf1a70: stur            x0, [fp, #-8]
    // 0xaf1a74: r0 = AllocateClosure()
    //     0xaf1a74: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf1a78: mov             x1, x0
    // 0xaf1a7c: ldur            x0, [fp, #-8]
    // 0xaf1a80: StoreField: r0->field_b = r1
    //     0xaf1a80: stur            w1, [x0, #0xb]
    // 0xaf1a84: LeaveFrame
    //     0xaf1a84: mov             SP, fp
    //     0xaf1a88: ldp             fp, lr, [SP], #0x10
    // 0xaf1a8c: ret
    //     0xaf1a8c: ret             
  }
  [closure] EncylopediaDetailLayout <anonymous closure>(dynamic) {
    // ** addr: 0xaf1a90, size: 0x190
    // 0xaf1a90: EnterFrame
    //     0xaf1a90: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1a94: mov             fp, SP
    // 0xaf1a98: AllocStack(0x30)
    //     0xaf1a98: sub             SP, SP, #0x30
    // 0xaf1a9c: SetupParameters()
    //     0xaf1a9c: ldr             x0, [fp, #0x10]
    //     0xaf1aa0: ldur            w2, [x0, #0x17]
    //     0xaf1aa4: add             x2, x2, HEAP, lsl #32
    //     0xaf1aa8: stur            x2, [fp, #-0x10]
    // 0xaf1aac: CheckStackOverflow
    //     0xaf1aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1ab0: cmp             SP, x16
    //     0xaf1ab4: b.ls            #0xaf1c18
    // 0xaf1ab8: LoadField: r0 = r2->field_b
    //     0xaf1ab8: ldur            w0, [x2, #0xb]
    // 0xaf1abc: DecompressPointer r0
    //     0xaf1abc: add             x0, x0, HEAP, lsl #32
    // 0xaf1ac0: stur            x0, [fp, #-8]
    // 0xaf1ac4: LoadField: r1 = r0->field_f
    //     0xaf1ac4: ldur            w1, [x0, #0xf]
    // 0xaf1ac8: DecompressPointer r1
    //     0xaf1ac8: add             x1, x1, HEAP, lsl #32
    // 0xaf1acc: r0 = controller()
    //     0xaf1acc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1ad0: LoadField: r1 = r0->field_3f
    //     0xaf1ad0: ldur            w1, [x0, #0x3f]
    // 0xaf1ad4: DecompressPointer r1
    //     0xaf1ad4: add             x1, x1, HEAP, lsl #32
    // 0xaf1ad8: r0 = value()
    //     0xaf1ad8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf1adc: mov             x2, x0
    // 0xaf1ae0: ldur            x0, [fp, #-8]
    // 0xaf1ae4: stur            x2, [fp, #-0x18]
    // 0xaf1ae8: LoadField: r1 = r0->field_f
    //     0xaf1ae8: ldur            w1, [x0, #0xf]
    // 0xaf1aec: DecompressPointer r1
    //     0xaf1aec: add             x1, x1, HEAP, lsl #32
    // 0xaf1af0: r0 = controller()
    //     0xaf1af0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1af4: LoadField: r2 = r0->field_3b
    //     0xaf1af4: ldur            w2, [x0, #0x3b]
    // 0xaf1af8: DecompressPointer r2
    //     0xaf1af8: add             x2, x2, HEAP, lsl #32
    // 0xaf1afc: ldur            x0, [fp, #-8]
    // 0xaf1b00: stur            x2, [fp, #-0x20]
    // 0xaf1b04: LoadField: r1 = r0->field_f
    //     0xaf1b04: ldur            w1, [x0, #0xf]
    // 0xaf1b08: DecompressPointer r1
    //     0xaf1b08: add             x1, x1, HEAP, lsl #32
    // 0xaf1b0c: r0 = controller()
    //     0xaf1b0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1b10: mov             x2, x0
    // 0xaf1b14: ldur            x0, [fp, #-8]
    // 0xaf1b18: stur            x2, [fp, #-0x28]
    // 0xaf1b1c: LoadField: r1 = r0->field_f
    //     0xaf1b1c: ldur            w1, [x0, #0xf]
    // 0xaf1b20: DecompressPointer r1
    //     0xaf1b20: add             x1, x1, HEAP, lsl #32
    // 0xaf1b24: r0 = controller()
    //     0xaf1b24: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf1b28: mov             x1, x0
    // 0xaf1b2c: ldur            x0, [fp, #-0x10]
    // 0xaf1b30: stur            x1, [fp, #-0x30]
    // 0xaf1b34: LoadField: r2 = r0->field_f
    //     0xaf1b34: ldur            w2, [x0, #0xf]
    // 0xaf1b38: DecompressPointer r2
    //     0xaf1b38: add             x2, x2, HEAP, lsl #32
    // 0xaf1b3c: cmp             w2, NULL
    // 0xaf1b40: b.ne            #0xaf1b4c
    // 0xaf1b44: r0 = Null
    //     0xaf1b44: mov             x0, NULL
    // 0xaf1b48: b               #0xaf1b54
    // 0xaf1b4c: LoadField: r0 = r2->field_1b
    //     0xaf1b4c: ldur            w0, [x2, #0x1b]
    // 0xaf1b50: DecompressPointer r0
    //     0xaf1b50: add             x0, x0, HEAP, lsl #32
    // 0xaf1b54: cmp             w0, NULL
    // 0xaf1b58: b.ne            #0xaf1b64
    // 0xaf1b5c: r3 = ""
    //     0xaf1b5c: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf1b60: b               #0xaf1b68
    // 0xaf1b64: mov             x3, x0
    // 0xaf1b68: ldur            x2, [fp, #-0x18]
    // 0xaf1b6c: ldur            x0, [fp, #-0x20]
    // 0xaf1b70: stur            x3, [fp, #-8]
    // 0xaf1b74: r0 = EncylopediaDetailLayout()
    //     0xaf1b74: bl              #0xaf1944  ; AllocateEncylopediaDetailLayoutStub -> EncylopediaDetailLayout (size=0x38)
    // 0xaf1b78: mov             x3, x0
    // 0xaf1b7c: ldur            x0, [fp, #-0x18]
    // 0xaf1b80: stur            x3, [fp, #-0x10]
    // 0xaf1b84: StoreField: r3->field_b = r0
    //     0xaf1b84: stur            w0, [x3, #0xb]
    // 0xaf1b88: ldur            x0, [fp, #-0x20]
    // 0xaf1b8c: StoreField: r3->field_f = r0
    //     0xaf1b8c: stur            w0, [x3, #0xf]
    // 0xaf1b90: ldur            x2, [fp, #-0x28]
    // 0xaf1b94: r1 = Function 'onRefresh':.
    //     0xaf1b94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f258] AnonymousClosure: (0xada8f0), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xaf1b98: ldr             x1, [x1, #0x258]
    // 0xaf1b9c: r0 = AllocateClosure()
    //     0xaf1b9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf1ba0: mov             x1, x0
    // 0xaf1ba4: ldur            x0, [fp, #-0x10]
    // 0xaf1ba8: StoreField: r0->field_13 = r1
    //     0xaf1ba8: stur            w1, [x0, #0x13]
    // 0xaf1bac: ldur            x2, [fp, #-0x30]
    // 0xaf1bb0: r1 = Function 'onScrollNotificationUpdated':.
    //     0xaf1bb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f260] AnonymousClosure: (0xaf1c20), in [package:nuonline/app/modules/encyclopedia/encyclopedia_detail/controllers/encyclopedia_detail_controller.dart] EncyclopediaDetailController::onScrollNotificationUpdated (0xaf1c5c)
    //     0xaf1bb4: ldr             x1, [x1, #0x260]
    // 0xaf1bb8: r0 = AllocateClosure()
    //     0xaf1bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf1bbc: mov             x1, x0
    // 0xaf1bc0: ldur            x0, [fp, #-0x10]
    // 0xaf1bc4: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf1bc4: stur            w1, [x0, #0x17]
    // 0xaf1bc8: ldur            x1, [fp, #-8]
    // 0xaf1bcc: StoreField: r0->field_23 = r1
    //     0xaf1bcc: stur            w1, [x0, #0x23]
    // 0xaf1bd0: r1 = true
    //     0xaf1bd0: add             x1, NULL, #0x20  ; true
    // 0xaf1bd4: StoreField: r0->field_1b = r1
    //     0xaf1bd4: stur            w1, [x0, #0x1b]
    // 0xaf1bd8: r1 = "gam"
    //     0xaf1bd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7d0] "gam"
    //     0xaf1bdc: ldr             x1, [x1, #0x7d0]
    // 0xaf1be0: StoreField: r0->field_1f = r1
    //     0xaf1be0: stur            w1, [x0, #0x1f]
    // 0xaf1be4: r1 = ""
    //     0xaf1be4: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf1be8: StoreField: r0->field_27 = r1
    //     0xaf1be8: stur            w1, [x0, #0x27]
    // 0xaf1bec: r1 = false
    //     0xaf1bec: add             x1, NULL, #0x30  ; false
    // 0xaf1bf0: StoreField: r0->field_2b = r1
    //     0xaf1bf0: stur            w1, [x0, #0x2b]
    // 0xaf1bf4: r1 = const []
    //     0xaf1bf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7d8] List<RelatedContent<int>>(0)
    //     0xaf1bf8: ldr             x1, [x1, #0x7d8]
    // 0xaf1bfc: StoreField: r0->field_2f = r1
    //     0xaf1bfc: stur            w1, [x0, #0x2f]
    // 0xaf1c00: r1 = const []
    //     0xaf1c00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7e0] List<RelatedContent<String>>(0)
    //     0xaf1c04: ldr             x1, [x1, #0x7e0]
    // 0xaf1c08: StoreField: r0->field_33 = r1
    //     0xaf1c08: stur            w1, [x0, #0x33]
    // 0xaf1c0c: LeaveFrame
    //     0xaf1c0c: mov             SP, fp
    //     0xaf1c10: ldp             fp, lr, [SP], #0x10
    // 0xaf1c14: ret
    //     0xaf1c14: ret             
    // 0xaf1c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf1c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1c1c: b               #0xaf1ab8
  }
}
