// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_info/bindings/encyclopedia_info_binding.dart

// class id: 1050261, size: 0x8
class :: {
}

// class id: 2170, size: 0x8, field offset: 0x8
class EncyclopediaInfoBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x812548, size: 0x70
    // 0x812548: EnterFrame
    //     0x812548: stp             fp, lr, [SP, #-0x10]!
    //     0x81254c: mov             fp, SP
    // 0x812550: AllocStack(0x10)
    //     0x812550: sub             SP, SP, #0x10
    // 0x812554: CheckStackOverflow
    //     0x812554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812558: cmp             SP, x16
    //     0x81255c: b.ls            #0x8125b0
    // 0x812560: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812560: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812564: ldr             x0, [x0, #0x2670]
    //     0x812568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81256c: cmp             w0, w16
    //     0x812570: b.ne            #0x81257c
    //     0x812574: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812578: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81257c: r1 = Function '<anonymous closure>':.
    //     0x81257c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a58] AnonymousClosure: (0x8125b8), in [package:nuonline/app/modules/encyclopedia/encyclopedia_info/bindings/encyclopedia_info_binding.dart] EncyclopediaInfoBinding::dependencies (0x812548)
    //     0x812580: ldr             x1, [x1, #0xa58]
    // 0x812584: r2 = Null
    //     0x812584: mov             x2, NULL
    // 0x812588: r0 = AllocateClosure()
    //     0x812588: bl              #0xec1630  ; AllocateClosureStub
    // 0x81258c: r16 = <EncyclopediaInfoController>
    //     0x81258c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a88] TypeArguments: <EncyclopediaInfoController>
    //     0x812590: ldr             x16, [x16, #0xa88]
    // 0x812594: stp             x0, x16, [SP]
    // 0x812598: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812598: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x81259c: r0 = Inst.lazyPut()
    //     0x81259c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8125a0: r0 = Null
    //     0x8125a0: mov             x0, NULL
    // 0x8125a4: LeaveFrame
    //     0x8125a4: mov             SP, fp
    //     0x8125a8: ldp             fp, lr, [SP], #0x10
    // 0x8125ac: ret
    //     0x8125ac: ret             
    // 0x8125b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8125b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8125b4: b               #0x812560
  }
  [closure] EncyclopediaInfoController <anonymous closure>(dynamic) {
    // ** addr: 0x8125b8, size: 0x40
    // 0x8125b8: EnterFrame
    //     0x8125b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8125bc: mov             fp, SP
    // 0x8125c0: AllocStack(0x8)
    //     0x8125c0: sub             SP, SP, #8
    // 0x8125c4: CheckStackOverflow
    //     0x8125c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8125c8: cmp             SP, x16
    //     0x8125cc: b.ls            #0x8125f0
    // 0x8125d0: r0 = EncyclopediaInfoController()
    //     0x8125d0: bl              #0x8125f8  ; AllocateEncyclopediaInfoControllerStub -> EncyclopediaInfoController (size=0x20)
    // 0x8125d4: mov             x1, x0
    // 0x8125d8: stur            x0, [fp, #-8]
    // 0x8125dc: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8125dc: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8125e0: ldur            x0, [fp, #-8]
    // 0x8125e4: LeaveFrame
    //     0x8125e4: mov             SP, fp
    //     0x8125e8: ldp             fp, lr, [SP], #0x10
    // 0x8125ec: ret
    //     0x8125ec: ret             
    // 0x8125f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8125f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8125f4: b               #0x8125d0
  }
}
