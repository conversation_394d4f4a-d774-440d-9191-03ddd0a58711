// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_info/views/encyclopedia_info_view.dart

// class id: 1050263, size: 0x8
class :: {
}

// class id: 5280, size: 0x1c, field offset: 0x14
class EncyclopediaInfoView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf1ce0, size: 0x184
    // 0xaf1ce0: EnterFrame
    //     0xaf1ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1ce4: mov             fp, SP
    // 0xaf1ce8: AllocStack(0x28)
    //     0xaf1ce8: sub             SP, SP, #0x28
    // 0xaf1cec: SetupParameters(EncyclopediaInfoView this /* r1 => r1, fp-0x8 */)
    //     0xaf1cec: stur            x1, [fp, #-8]
    // 0xaf1cf0: CheckStackOverflow
    //     0xaf1cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1cf4: cmp             SP, x16
    //     0xaf1cf8: b.ls            #0xaf1e5c
    // 0xaf1cfc: r0 = AppBar()
    //     0xaf1cfc: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf1d00: stur            x0, [fp, #-0x10]
    // 0xaf1d04: r16 = Instance_Text
    //     0xaf1d04: add             x16, PP, #0x28, lsl #12  ; [pp+0x28358] Obj!Text@e219b1
    //     0xaf1d08: ldr             x16, [x16, #0x358]
    // 0xaf1d0c: str             x16, [SP]
    // 0xaf1d10: mov             x1, x0
    // 0xaf1d14: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaf1d14: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaf1d18: ldr             x4, [x4, #0x6e8]
    // 0xaf1d1c: r0 = AppBar()
    //     0xaf1d1c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf1d20: r1 = Null
    //     0xaf1d20: mov             x1, NULL
    // 0xaf1d24: r2 = 8
    //     0xaf1d24: movz            x2, #0x8
    // 0xaf1d28: r0 = AllocateArray()
    //     0xaf1d28: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf1d2c: r16 = "p"
    //     0xaf1d2c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cac0] "p"
    //     0xaf1d30: ldr             x16, [x16, #0xac0]
    // 0xaf1d34: StoreField: r0->field_f = r16
    //     0xaf1d34: stur            w16, [x0, #0xf]
    // 0xaf1d38: ldur            x1, [fp, #-8]
    // 0xaf1d3c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaf1d3c: ldur            w2, [x1, #0x17]
    // 0xaf1d40: DecompressPointer r2
    //     0xaf1d40: add             x2, x2, HEAP, lsl #32
    // 0xaf1d44: StoreField: r0->field_13 = r2
    //     0xaf1d44: stur            w2, [x0, #0x13]
    // 0xaf1d48: r16 = "li"
    //     0xaf1d48: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cac8] "li"
    //     0xaf1d4c: ldr             x16, [x16, #0xac8]
    // 0xaf1d50: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf1d50: stur            w16, [x0, #0x17]
    // 0xaf1d54: StoreField: r0->field_1b = r2
    //     0xaf1d54: stur            w2, [x0, #0x1b]
    // 0xaf1d58: r16 = <String, Style>
    //     0xaf1d58: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cad8] TypeArguments: <String, Style>
    //     0xaf1d5c: ldr             x16, [x16, #0xad8]
    // 0xaf1d60: stp             x0, x16, [SP]
    // 0xaf1d64: r0 = Map._fromLiteral()
    //     0xaf1d64: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf1d68: stur            x0, [fp, #-8]
    // 0xaf1d6c: r0 = Html()
    //     0xaf1d6c: bl              #0xaf1fd0  ; AllocateHtmlStub -> Html (size=0x38)
    // 0xaf1d70: mov             x2, x0
    // 0xaf1d74: r0 = "<p><strong>NUpedia&nbsp;</strong>atau ensiklopedia NU versi digital merupakan bentuk pengembangan dari buku <em>Ensiklopedia Nahdlatul Ulama: Sejarah, Tokoh, dan Khazanah Pesantren </em>yang terdiri dari empat jilid. Buku ini cetak pertama kali tahun 2014, dan disusun secara kolektif oleh M Imam Aziz selaku penanggung jawab dan pemimpin redaksi bersama lebih dari 30 intelektual dan aktivis NU lainnya.</p>\r\n\r\n<p>Mereka yang terlibat menulis adalah A Khoirul Anam, A Zuhdi Muhdlor, Abdul Mun&#39;im DZ, Abdullah Alawi, Ahmad Baso, Ahmad Makki, Akhmad Muhaimin Azzet, Alamsyah M Djafar, Ali Usman, Hairus Salim HS, Hamzah Sahal, Heru Prasetia, Iip D Yahya, M Imam Aziz, Miftah Farid, A Mukafi Niam, Nur Kholik Ridwan, Syaifullah Amin, Tri Chandra Aprianto, dan Ulil Abshar Hadrawi.</p>\r\n\r\n<p>Pelaksana program dan sebagian anggota tim penulis tersebut adalah kru <em>NU Online</em>. Kini, dalam NUpedia&nbsp;yang dirilis Januari 2021, <em>NU Online </em>memasukkan sejumlah entri baru dan data-data mutakhir yang luput ditulis pada versi cetak.</p>\r\n\r\n<p>Dalam NUpedia&nbsp;ini juga ditambahkan fitur &quot;Khittah&quot; yang berisi dokumen-dokumen bersejarah hasil keputusan resmi NU yang dipedomani hingga sekarang. Juga fitur &quot;Kronik&quot; yang memudahkan pembaca dalam melihat secara singkat sejarah perjalanan NU dari masa ke masa berdasar urutan tahun.</p>\r\n\r\n<p>Pembaruan akan terus dilakukan, baik atas inisiatif tim <em>NU Online</em> maupun masukan dan saran dari berbagai pihak. Kami mengajak kepada Anda semua untuk berpartisipasi dalam pengembangan dan penyempurnaan ensiklopedia NU versi digital ini.</p>\r\n\r\n<p>Silakan kirimkan usulan entri baru, komentar, atau kritik dan saran ke email: <EMAIL></p>"
    //     0xaf1d74: add             x0, PP, #0x24, lsl #12  ; [pp+0x24a90] "<p><strong>NUpedia&nbsp;</strong>atau ensiklopedia NU versi digital merupakan bentuk pengembangan dari buku <em>Ensiklopedia Nahdlatul Ulama: Sejarah, Tokoh, dan Khazanah Pesantren </em>yang terdiri dari empat jilid. Buku ini cetak pertama kali tahun 2014, dan disusun secara kolektif oleh M Imam Aziz selaku penanggung jawab dan pemimpin redaksi bersama lebih dari 30 intelektual dan aktivis NU lainnya.</p>\r\n\r\n<p>Mereka yang terlibat menulis adalah A Khoirul Anam, A Zuhdi Muhdlor, Abdul Mun&#39;im DZ, Abdullah Alawi, Ahmad Baso, Ahmad Makki, Akhmad Muhaimin Azzet, Alamsyah M Djafar, Ali Usman, Hairus Salim HS, Hamzah Sahal, Heru Prasetia, Iip D Yahya, M Imam Aziz, Miftah Farid, A Mukafi Niam, Nur Kholik Ridwan, Syaifullah Amin, Tri Chandra Aprianto, dan Ulil Abshar Hadrawi.</p>\r\n\r\n<p>Pelaksana program dan sebagian anggota tim penulis tersebut adalah kru <em>NU Online</em>. Kini, dalam NUpedia&nbsp;yang dirilis Januari 2021, <em>NU Online </em>memasukkan sejumlah entri baru dan data-data mutakhir yang luput ditulis pada versi cetak.</p>\r\n\r\n<p>Dalam NUpedia&nbsp;ini juga ditambahkan fitur &quot;Khittah&quot; yang berisi dokumen-dokumen bersejarah hasil keputusan resmi NU yang dipedomani hingga sekarang. Juga fitur &quot;Kronik&quot; yang memudahkan pembaca dalam melihat secara singkat sejarah perjalanan NU dari masa ke masa berdasar urutan tahun.</p>\r\n\r\n<p>Pembaruan akan terus dilakukan, baik atas inisiatif tim <em>NU Online</em> maupun masukan dan saran dari berbagai pihak. Kami mengajak kepada Anda semua untuk berpartisipasi dalam pengembangan dan penyempurnaan ensiklopedia NU versi digital ini.</p>\r\n\r\n<p>Silakan kirimkan usulan entri baru, komentar, atau kritik dan saran ke email: <EMAIL></p>"
    //     0xaf1d78: ldr             x0, [x0, #0xa90]
    // 0xaf1d7c: stur            x2, [fp, #-0x18]
    // 0xaf1d80: StoreField: r2->field_f = r0
    //     0xaf1d80: stur            w0, [x2, #0xf]
    // 0xaf1d84: r0 = const []
    //     0xaf1d84: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cae0] List<HtmlExtension>(0)
    //     0xaf1d88: ldr             x0, [x0, #0xae0]
    // 0xaf1d8c: StoreField: r2->field_2f = r0
    //     0xaf1d8c: stur            w0, [x2, #0x2f]
    // 0xaf1d90: r0 = false
    //     0xaf1d90: add             x0, NULL, #0x30  ; false
    // 0xaf1d94: StoreField: r2->field_23 = r0
    //     0xaf1d94: stur            w0, [x2, #0x23]
    // 0xaf1d98: ldur            x1, [fp, #-8]
    // 0xaf1d9c: StoreField: r2->field_33 = r1
    //     0xaf1d9c: stur            w1, [x2, #0x33]
    // 0xaf1da0: r1 = <State<StatefulWidget>>
    //     0xaf1da0: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xaf1da4: r0 = LabeledGlobalKey()
    //     0xaf1da4: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xaf1da8: mov             x1, x0
    // 0xaf1dac: ldur            x0, [fp, #-0x18]
    // 0xaf1db0: StoreField: r0->field_b = r1
    //     0xaf1db0: stur            w1, [x0, #0xb]
    // 0xaf1db4: r0 = SingleChildScrollView()
    //     0xaf1db4: bl              #0xa06cec  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xaf1db8: mov             x1, x0
    // 0xaf1dbc: r0 = Instance_Axis
    //     0xaf1dbc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf1dc0: stur            x1, [fp, #-8]
    // 0xaf1dc4: StoreField: r1->field_b = r0
    //     0xaf1dc4: stur            w0, [x1, #0xb]
    // 0xaf1dc8: r0 = false
    //     0xaf1dc8: add             x0, NULL, #0x30  ; false
    // 0xaf1dcc: StoreField: r1->field_f = r0
    //     0xaf1dcc: stur            w0, [x1, #0xf]
    // 0xaf1dd0: r2 = Instance_EdgeInsets
    //     0xaf1dd0: ldr             x2, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xaf1dd4: StoreField: r1->field_13 = r2
    //     0xaf1dd4: stur            w2, [x1, #0x13]
    // 0xaf1dd8: ldur            x2, [fp, #-0x18]
    // 0xaf1ddc: StoreField: r1->field_23 = r2
    //     0xaf1ddc: stur            w2, [x1, #0x23]
    // 0xaf1de0: r2 = Instance_DragStartBehavior
    //     0xaf1de0: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf1de4: StoreField: r1->field_27 = r2
    //     0xaf1de4: stur            w2, [x1, #0x27]
    // 0xaf1de8: r3 = Instance_Clip
    //     0xaf1de8: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xaf1dec: ldr             x3, [x3, #0x7c0]
    // 0xaf1df0: StoreField: r1->field_2b = r3
    //     0xaf1df0: stur            w3, [x1, #0x2b]
    // 0xaf1df4: r3 = Instance_HitTestBehavior
    //     0xaf1df4: add             x3, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xaf1df8: ldr             x3, [x3, #0x1c8]
    // 0xaf1dfc: StoreField: r1->field_2f = r3
    //     0xaf1dfc: stur            w3, [x1, #0x2f]
    // 0xaf1e00: r3 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xaf1e00: add             x3, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xaf1e04: ldr             x3, [x3, #0xf00]
    // 0xaf1e08: StoreField: r1->field_37 = r3
    //     0xaf1e08: stur            w3, [x1, #0x37]
    // 0xaf1e0c: r0 = Scaffold()
    //     0xaf1e0c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf1e10: ldur            x1, [fp, #-0x10]
    // 0xaf1e14: StoreField: r0->field_13 = r1
    //     0xaf1e14: stur            w1, [x0, #0x13]
    // 0xaf1e18: ldur            x1, [fp, #-8]
    // 0xaf1e1c: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf1e1c: stur            w1, [x0, #0x17]
    // 0xaf1e20: r1 = Instance_AlignmentDirectional
    //     0xaf1e20: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf1e24: ldr             x1, [x1, #0x758]
    // 0xaf1e28: StoreField: r0->field_2b = r1
    //     0xaf1e28: stur            w1, [x0, #0x2b]
    // 0xaf1e2c: r1 = true
    //     0xaf1e2c: add             x1, NULL, #0x20  ; true
    // 0xaf1e30: StoreField: r0->field_53 = r1
    //     0xaf1e30: stur            w1, [x0, #0x53]
    // 0xaf1e34: r2 = Instance_DragStartBehavior
    //     0xaf1e34: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf1e38: StoreField: r0->field_57 = r2
    //     0xaf1e38: stur            w2, [x0, #0x57]
    // 0xaf1e3c: r2 = false
    //     0xaf1e3c: add             x2, NULL, #0x30  ; false
    // 0xaf1e40: StoreField: r0->field_b = r2
    //     0xaf1e40: stur            w2, [x0, #0xb]
    // 0xaf1e44: StoreField: r0->field_f = r2
    //     0xaf1e44: stur            w2, [x0, #0xf]
    // 0xaf1e48: StoreField: r0->field_5f = r1
    //     0xaf1e48: stur            w1, [x0, #0x5f]
    // 0xaf1e4c: StoreField: r0->field_63 = r1
    //     0xaf1e4c: stur            w1, [x0, #0x63]
    // 0xaf1e50: LeaveFrame
    //     0xaf1e50: mov             SP, fp
    //     0xaf1e54: ldp             fp, lr, [SP], #0x10
    // 0xaf1e58: ret
    //     0xaf1e58: ret             
    // 0xaf1e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf1e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1e60: b               #0xaf1cfc
  }
  _ EncyclopediaInfoView(/* No info */) {
    // ** addr: 0xb2d9d8, size: 0xe8
    // 0xb2d9d8: EnterFrame
    //     0xb2d9d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d9dc: mov             fp, SP
    // 0xb2d9e0: AllocStack(0x38)
    //     0xb2d9e0: sub             SP, SP, #0x38
    // 0xb2d9e4: r0 = "<p><strong>NUpedia&nbsp;</strong>atau ensiklopedia NU versi digital merupakan bentuk pengembangan dari buku <em>Ensiklopedia Nahdlatul Ulama: Sejarah, Tokoh, dan Khazanah Pesantren </em>yang terdiri dari empat jilid. Buku ini cetak pertama kali tahun 2014, dan disusun secara kolektif oleh M Imam Aziz selaku penanggung jawab dan pemimpin redaksi bersama lebih dari 30 intelektual dan aktivis NU lainnya.</p>\r\n\r\n<p>Mereka yang terlibat menulis adalah A Khoirul Anam, A Zuhdi Muhdlor, Abdul Mun&#39;im DZ, Abdullah Alawi, Ahmad Baso, Ahmad Makki, Akhmad Muhaimin Azzet, Alamsyah M Djafar, Ali Usman, Hairus Salim HS, Hamzah Sahal, Heru Prasetia, Iip D Yahya, M Imam Aziz, Miftah Farid, A Mukafi Niam, Nur Kholik Ridwan, Syaifullah Amin, Tri Chandra Aprianto, dan Ulil Abshar Hadrawi.</p>\r\n\r\n<p>Pelaksana program dan sebagian anggota tim penulis tersebut adalah kru <em>NU Online</em>. Kini, dalam NUpedia&nbsp;yang dirilis Januari 2021, <em>NU Online </em>memasukkan sejumlah entri baru dan data-data mutakhir yang luput ditulis pada versi cetak.</p>\r\n\r\n<p>Dalam NUpedia&nbsp;ini juga ditambahkan fitur &quot;Khittah&quot; yang berisi dokumen-dokumen bersejarah hasil keputusan resmi NU yang dipedomani hingga sekarang. Juga fitur &quot;Kronik&quot; yang memudahkan pembaca dalam melihat secara singkat sejarah perjalanan NU dari masa ke masa berdasar urutan tahun.</p>\r\n\r\n<p>Pembaruan akan terus dilakukan, baik atas inisiatif tim <em>NU Online</em> maupun masukan dan saran dari berbagai pihak. Kami mengajak kepada Anda semua untuk berpartisipasi dalam pengembangan dan penyempurnaan ensiklopedia NU versi digital ini.</p>\r\n\r\n<p>Silakan kirimkan usulan entri baru, komentar, atau kritik dan saran ke email: <EMAIL></p>"
    //     0xb2d9e4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24a90] "<p><strong>NUpedia&nbsp;</strong>atau ensiklopedia NU versi digital merupakan bentuk pengembangan dari buku <em>Ensiklopedia Nahdlatul Ulama: Sejarah, Tokoh, dan Khazanah Pesantren </em>yang terdiri dari empat jilid. Buku ini cetak pertama kali tahun 2014, dan disusun secara kolektif oleh M Imam Aziz selaku penanggung jawab dan pemimpin redaksi bersama lebih dari 30 intelektual dan aktivis NU lainnya.</p>\r\n\r\n<p>Mereka yang terlibat menulis adalah A Khoirul Anam, A Zuhdi Muhdlor, Abdul Mun&#39;im DZ, Abdullah Alawi, Ahmad Baso, Ahmad Makki, Akhmad Muhaimin Azzet, Alamsyah M Djafar, Ali Usman, Hairus Salim HS, Hamzah Sahal, Heru Prasetia, Iip D Yahya, M Imam Aziz, Miftah Farid, A Mukafi Niam, Nur Kholik Ridwan, Syaifullah Amin, Tri Chandra Aprianto, dan Ulil Abshar Hadrawi.</p>\r\n\r\n<p>Pelaksana program dan sebagian anggota tim penulis tersebut adalah kru <em>NU Online</em>. Kini, dalam NUpedia&nbsp;yang dirilis Januari 2021, <em>NU Online </em>memasukkan sejumlah entri baru dan data-data mutakhir yang luput ditulis pada versi cetak.</p>\r\n\r\n<p>Dalam NUpedia&nbsp;ini juga ditambahkan fitur &quot;Khittah&quot; yang berisi dokumen-dokumen bersejarah hasil keputusan resmi NU yang dipedomani hingga sekarang. Juga fitur &quot;Kronik&quot; yang memudahkan pembaca dalam melihat secara singkat sejarah perjalanan NU dari masa ke masa berdasar urutan tahun.</p>\r\n\r\n<p>Pembaruan akan terus dilakukan, baik atas inisiatif tim <em>NU Online</em> maupun masukan dan saran dari berbagai pihak. Kami mengajak kepada Anda semua untuk berpartisipasi dalam pengembangan dan penyempurnaan ensiklopedia NU versi digital ini.</p>\r\n\r\n<p>Silakan kirimkan usulan entri baru, komentar, atau kritik dan saran ke email: <EMAIL></p>"
    //     0xb2d9e8: ldr             x0, [x0, #0xa90]
    // 0xb2d9ec: stur            x1, [fp, #-8]
    // 0xb2d9f0: CheckStackOverflow
    //     0xb2d9f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d9f4: cmp             SP, x16
    //     0xb2d9f8: b.ls            #0xb2dab8
    // 0xb2d9fc: StoreField: r1->field_13 = r0
    //     0xb2d9fc: stur            w0, [x1, #0x13]
    // 0xb2da00: r0 = FontSize()
    //     0xb2da00: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb2da04: d0 = 16.000000
    //     0xb2da04: fmov            d0, #16.00000000
    // 0xb2da08: stur            x0, [fp, #-0x10]
    // 0xb2da0c: StoreField: r0->field_7 = d0
    //     0xb2da0c: stur            d0, [x0, #7]
    // 0xb2da10: r1 = Instance_Unit
    //     0xb2da10: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb2da14: ldr             x1, [x1, #0xa98]
    // 0xb2da18: StoreField: r0->field_f = r1
    //     0xb2da18: stur            w1, [x0, #0xf]
    // 0xb2da1c: r1 = _ConstMap len:3
    //     0xb2da1c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb2da20: ldr             x1, [x1, #0xbe8]
    // 0xb2da24: r2 = 2
    //     0xb2da24: movz            x2, #0x2
    // 0xb2da28: r0 = []()
    //     0xb2da28: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb2da2c: r1 = _ConstMap len:6
    //     0xb2da2c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb2da30: ldr             x1, [x1, #0xc20]
    // 0xb2da34: r2 = 2
    //     0xb2da34: movz            x2, #0x2
    // 0xb2da38: stur            x0, [fp, #-0x18]
    // 0xb2da3c: r0 = []()
    //     0xb2da3c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb2da40: r16 = <Color?>
    //     0xb2da40: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb2da44: ldr             x16, [x16, #0x98]
    // 0xb2da48: stp             x0, x16, [SP, #8]
    // 0xb2da4c: ldur            x16, [fp, #-0x18]
    // 0xb2da50: str             x16, [SP]
    // 0xb2da54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb2da54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb2da58: r0 = mode()
    //     0xb2da58: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb2da5c: stur            x0, [fp, #-0x18]
    // 0xb2da60: r0 = Style()
    //     0xb2da60: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xb2da64: stur            x0, [fp, #-0x20]
    // 0xb2da68: ldur            x16, [fp, #-0x10]
    // 0xb2da6c: ldur            lr, [fp, #-0x18]
    // 0xb2da70: stp             lr, x16, [SP]
    // 0xb2da74: mov             x1, x0
    // 0xb2da78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2da78: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2da7c: ldr             x4, [x4, #0xaa0]
    // 0xb2da80: r0 = Style()
    //     0xb2da80: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xb2da84: ldur            x0, [fp, #-0x20]
    // 0xb2da88: ldur            x1, [fp, #-8]
    // 0xb2da8c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2da8c: stur            w0, [x1, #0x17]
    //     0xb2da90: ldurb           w16, [x1, #-1]
    //     0xb2da94: ldurb           w17, [x0, #-1]
    //     0xb2da98: and             x16, x17, x16, lsr #2
    //     0xb2da9c: tst             x16, HEAP, lsr #32
    //     0xb2daa0: b.eq            #0xb2daa8
    //     0xb2daa4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb2daa8: r0 = Null
    //     0xb2daa8: mov             x0, NULL
    // 0xb2daac: LeaveFrame
    //     0xb2daac: mov             SP, fp
    //     0xb2dab0: ldp             fp, lr, [SP], #0x10
    // 0xb2dab4: ret
    //     0xb2dab4: ret             
    // 0xb2dab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2dab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2dabc: b               #0xb2d9fc
  }
}
