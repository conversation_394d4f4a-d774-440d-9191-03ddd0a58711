// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_list/bindings/encyclopedia_list_binding.dart

// class id: 1050264, size: 0x8
class :: {
}

// class id: 2169, size: 0x8, field offset: 0x8
class EncyclopediaListBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x812624, size: 0x70
    // 0x812624: EnterFrame
    //     0x812624: stp             fp, lr, [SP, #-0x10]!
    //     0x812628: mov             fp, SP
    // 0x81262c: AllocStack(0x10)
    //     0x81262c: sub             SP, SP, #0x10
    // 0x812630: CheckStackOverflow
    //     0x812630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812634: cmp             SP, x16
    //     0x812638: b.ls            #0x81268c
    // 0x81263c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x81263c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812640: ldr             x0, [x0, #0x2670]
    //     0x812644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812648: cmp             w0, w16
    //     0x81264c: b.ne            #0x812658
    //     0x812650: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812654: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812658: r1 = Function '<anonymous closure>':.
    //     0x812658: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a20] AnonymousClosure: (0x812694), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/bindings/encyclopedia_list_binding.dart] EncyclopediaListBinding::dependencies (0x812624)
    //     0x81265c: ldr             x1, [x1, #0xa20]
    // 0x812660: r2 = Null
    //     0x812660: mov             x2, NULL
    // 0x812664: r0 = AllocateClosure()
    //     0x812664: bl              #0xec1630  ; AllocateClosureStub
    // 0x812668: r16 = <EncyclopediaListController>
    //     0x812668: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b88] TypeArguments: <EncyclopediaListController>
    //     0x81266c: ldr             x16, [x16, #0xb88]
    // 0x812670: stp             x0, x16, [SP]
    // 0x812674: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812674: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812678: r0 = Inst.lazyPut()
    //     0x812678: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x81267c: r0 = Null
    //     0x81267c: mov             x0, NULL
    // 0x812680: LeaveFrame
    //     0x812680: mov             SP, fp
    //     0x812684: ldp             fp, lr, [SP], #0x10
    // 0x812688: ret
    //     0x812688: ret             
    // 0x81268c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81268c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812690: b               #0x81263c
  }
  [closure] EncyclopediaListController <anonymous closure>(dynamic) {
    // ** addr: 0x812694, size: 0xf4
    // 0x812694: EnterFrame
    //     0x812694: stp             fp, lr, [SP, #-0x10]!
    //     0x812698: mov             fp, SP
    // 0x81269c: AllocStack(0x30)
    //     0x81269c: sub             SP, SP, #0x30
    // 0x8126a0: CheckStackOverflow
    //     0x8126a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8126a4: cmp             SP, x16
    //     0x8126a8: b.ls            #0x812780
    // 0x8126ac: r0 = find()
    //     0x8126ac: bl              #0x81214c  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::find
    // 0x8126b0: stur            x0, [fp, #-8]
    // 0x8126b4: r0 = find()
    //     0x8126b4: bl              #0x8120e8  ; [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_remote_repository.dart] EncyclopediaRemoteRepository::find
    // 0x8126b8: stur            x0, [fp, #-0x10]
    // 0x8126bc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8126bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8126c0: ldr             x0, [x0, #0x2670]
    //     0x8126c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8126c8: cmp             w0, w16
    //     0x8126cc: b.ne            #0x8126d8
    //     0x8126d0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8126d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8126d8: r0 = GetNavigation.arguments()
    //     0x8126d8: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8126dc: r16 = "category"
    //     0x8126dc: add             x16, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x8126e0: ldr             x16, [x16, #0x960]
    // 0x8126e4: stp             x16, x0, [SP]
    // 0x8126e8: r4 = 0
    //     0x8126e8: movz            x4, #0
    // 0x8126ec: ldr             x0, [SP, #8]
    // 0x8126f0: r16 = UnlinkedCall_0x5f3c08
    //     0x8126f0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34a28] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8126f4: add             x16, x16, #0xa28
    // 0x8126f8: ldp             x5, lr, [x16]
    // 0x8126fc: blr             lr
    // 0x812700: mov             x3, x0
    // 0x812704: r2 = Null
    //     0x812704: mov             x2, NULL
    // 0x812708: r1 = Null
    //     0x812708: mov             x1, NULL
    // 0x81270c: stur            x3, [fp, #-0x18]
    // 0x812710: r4 = 60
    //     0x812710: movz            x4, #0x3c
    // 0x812714: branchIfSmi(r0, 0x812720)
    //     0x812714: tbz             w0, #0, #0x812720
    // 0x812718: r4 = LoadClassIdInstr(r0)
    //     0x812718: ldur            x4, [x0, #-1]
    //     0x81271c: ubfx            x4, x4, #0xc, #0x14
    // 0x812720: r17 = 5587
    //     0x812720: movz            x17, #0x15d3
    // 0x812724: cmp             x4, x17
    // 0x812728: b.eq            #0x812740
    // 0x81272c: r8 = EncyclopediaCategory?
    //     0x81272c: add             x8, PP, #0x34, lsl #12  ; [pp+0x34a38] Type: EncyclopediaCategory?
    //     0x812730: ldr             x8, [x8, #0xa38]
    // 0x812734: r3 = Null
    //     0x812734: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a40] Null
    //     0x812738: ldr             x3, [x3, #0xa40]
    // 0x81273c: r0 = DefaultNullableTypeTest()
    //     0x81273c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x812740: r0 = find()
    //     0x812740: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0x812744: r1 = <List<Encyclopedia>>
    //     0x812744: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a50] TypeArguments: <List<Encyclopedia>>
    //     0x812748: ldr             x1, [x1, #0xa50]
    // 0x81274c: stur            x0, [fp, #-0x20]
    // 0x812750: r0 = EncyclopediaListController()
    //     0x812750: bl              #0x812ac4  ; AllocateEncyclopediaListControllerStub -> EncyclopediaListController (size=0x60)
    // 0x812754: mov             x1, x0
    // 0x812758: ldur            x2, [fp, #-0x18]
    // 0x81275c: ldur            x3, [fp, #-8]
    // 0x812760: ldur            x5, [fp, #-0x10]
    // 0x812764: ldur            x6, [fp, #-0x20]
    // 0x812768: stur            x0, [fp, #-8]
    // 0x81276c: r0 = EncyclopediaListController()
    //     0x81276c: bl              #0x812788  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::EncyclopediaListController
    // 0x812770: ldur            x0, [fp, #-8]
    // 0x812774: LeaveFrame
    //     0x812774: mov             SP, fp
    //     0x812778: ldp             fp, lr, [SP], #0x10
    // 0x81277c: ret
    //     0x81277c: ret             
    // 0x812780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812784: b               #0x8126ac
  }
}
