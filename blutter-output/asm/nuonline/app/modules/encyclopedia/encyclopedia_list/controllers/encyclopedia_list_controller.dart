// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart

// class id: 1050265, size: 0x8
class :: {
}

// class id: 1980, size: 0x3c, field offset: 0x24
//   transformed mixin,
abstract class _EncyclopediaListController&OfflineFirstController&PagingMixin extends OfflineFirstController<dynamic>
     with PagingMixin<X0> {

  _ _EncyclopediaListController&OfflineFirstController&PagingMixin(/* No info */) {
    // ** addr: 0x812984, size: 0x140
    // 0x812984: EnterFrame
    //     0x812984: stp             fp, lr, [SP, #-0x10]!
    //     0x812988: mov             fp, SP
    // 0x81298c: AllocStack(0x18)
    //     0x81298c: sub             SP, SP, #0x18
    // 0x812990: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x812990: mov             x0, x1
    //     0x812994: stur            x1, [fp, #-8]
    // 0x812998: CheckStackOverflow
    //     0x812998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81299c: cmp             SP, x16
    //     0x8129a0: b.ls            #0x812abc
    // 0x8129a4: r1 = 1
    //     0x8129a4: movz            x1, #0x1
    // 0x8129a8: r0 = IntExtension.obs()
    //     0x8129a8: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x8129ac: ldur            x2, [fp, #-8]
    // 0x8129b0: StoreField: r2->field_23 = r0
    //     0x8129b0: stur            w0, [x2, #0x23]
    //     0x8129b4: ldurb           w16, [x2, #-1]
    //     0x8129b8: ldurb           w17, [x0, #-1]
    //     0x8129bc: and             x16, x17, x16, lsr #2
    //     0x8129c0: tst             x16, HEAP, lsr #32
    //     0x8129c4: b.eq            #0x8129cc
    //     0x8129c8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8129cc: r1 = true
    //     0x8129cc: add             x1, NULL, #0x20  ; true
    // 0x8129d0: r0 = BoolExtension.obs()
    //     0x8129d0: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x8129d4: ldur            x2, [fp, #-8]
    // 0x8129d8: StoreField: r2->field_27 = r0
    //     0x8129d8: stur            w0, [x2, #0x27]
    //     0x8129dc: ldurb           w16, [x2, #-1]
    //     0x8129e0: ldurb           w17, [x0, #-1]
    //     0x8129e4: and             x16, x17, x16, lsr #2
    //     0x8129e8: tst             x16, HEAP, lsr #32
    //     0x8129ec: b.eq            #0x8129f4
    //     0x8129f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8129f4: r1 = false
    //     0x8129f4: add             x1, NULL, #0x30  ; false
    // 0x8129f8: r0 = BoolExtension.obs()
    //     0x8129f8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x8129fc: ldur            x2, [fp, #-8]
    // 0x812a00: StoreField: r2->field_2b = r0
    //     0x812a00: stur            w0, [x2, #0x2b]
    //     0x812a04: ldurb           w16, [x2, #-1]
    //     0x812a08: ldurb           w17, [x0, #-1]
    //     0x812a0c: and             x16, x17, x16, lsr #2
    //     0x812a10: tst             x16, HEAP, lsr #32
    //     0x812a14: b.eq            #0x812a1c
    //     0x812a18: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812a1c: r1 = false
    //     0x812a1c: add             x1, NULL, #0x30  ; false
    // 0x812a20: r0 = BoolExtension.obs()
    //     0x812a20: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x812a24: ldur            x3, [fp, #-8]
    // 0x812a28: StoreField: r3->field_2f = r0
    //     0x812a28: stur            w0, [x3, #0x2f]
    //     0x812a2c: ldurb           w16, [x3, #-1]
    //     0x812a30: ldurb           w17, [x0, #-1]
    //     0x812a34: and             x16, x17, x16, lsr #2
    //     0x812a38: tst             x16, HEAP, lsr #32
    //     0x812a3c: b.eq            #0x812a44
    //     0x812a40: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x812a44: r1 = <Encyclopedia>
    //     0x812a44: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0x812a48: r2 = 0
    //     0x812a48: movz            x2, #0
    // 0x812a4c: r0 = _GrowableList()
    //     0x812a4c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x812a50: r16 = <Encyclopedia>
    //     0x812a50: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0x812a54: stp             x0, x16, [SP]
    // 0x812a58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812a58: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812a5c: r0 = ListExtension.obs()
    //     0x812a5c: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x812a60: ldur            x2, [fp, #-8]
    // 0x812a64: StoreField: r2->field_33 = r0
    //     0x812a64: stur            w0, [x2, #0x33]
    //     0x812a68: ldurb           w16, [x2, #-1]
    //     0x812a6c: ldurb           w17, [x0, #-1]
    //     0x812a70: and             x16, x17, x16, lsr #2
    //     0x812a74: tst             x16, HEAP, lsr #32
    //     0x812a78: b.eq            #0x812a80
    //     0x812a7c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812a80: r1 = false
    //     0x812a80: add             x1, NULL, #0x30  ; false
    // 0x812a84: r0 = BoolExtension.obs()
    //     0x812a84: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x812a88: ldur            x1, [fp, #-8]
    // 0x812a8c: StoreField: r1->field_37 = r0
    //     0x812a8c: stur            w0, [x1, #0x37]
    //     0x812a90: ldurb           w16, [x1, #-1]
    //     0x812a94: ldurb           w17, [x0, #-1]
    //     0x812a98: and             x16, x17, x16, lsr #2
    //     0x812a9c: tst             x16, HEAP, lsr #32
    //     0x812aa0: b.eq            #0x812aa8
    //     0x812aa4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812aa8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x812aa8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x812aac: r0 = Null
    //     0x812aac: mov             x0, NULL
    // 0x812ab0: LeaveFrame
    //     0x812ab0: mov             SP, fp
    //     0x812ab4: ldp             fp, lr, [SP], #0x10
    // 0x812ab8: ret
    //     0x812ab8: ret             
    // 0x812abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812ac0: b               #0x8129a4
  }
  _ onPageRefresh(/* No info */) async {
    // ** addr: 0x8f04b8, size: 0xb0
    // 0x8f04b8: EnterFrame
    //     0x8f04b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f04bc: mov             fp, SP
    // 0x8f04c0: AllocStack(0x18)
    //     0x8f04c0: sub             SP, SP, #0x18
    // 0x8f04c4: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin this /* r1 => r1, fp-0x10 */)
    //     0x8f04c4: stur            NULL, [fp, #-8]
    //     0x8f04c8: stur            x1, [fp, #-0x10]
    // 0x8f04cc: CheckStackOverflow
    //     0x8f04cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f04d0: cmp             SP, x16
    //     0x8f04d4: b.ls            #0x8f0560
    // 0x8f04d8: InitAsync() -> Future<void?>
    //     0x8f04d8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f04dc: bl              #0x661298  ; InitAsyncStub
    // 0x8f04e0: ldur            x0, [fp, #-0x10]
    // 0x8f04e4: LoadField: r3 = r0->field_33
    //     0x8f04e4: ldur            w3, [x0, #0x33]
    // 0x8f04e8: DecompressPointer r3
    //     0x8f04e8: add             x3, x3, HEAP, lsl #32
    // 0x8f04ec: stur            x3, [fp, #-0x18]
    // 0x8f04f0: r1 = <Encyclopedia>
    //     0x8f04f0: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0x8f04f4: r2 = 0
    //     0x8f04f4: movz            x2, #0
    // 0x8f04f8: r0 = _GrowableList()
    //     0x8f04f8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8f04fc: ldur            x1, [fp, #-0x18]
    // 0x8f0500: mov             x2, x0
    // 0x8f0504: r0 = value=()
    //     0x8f0504: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8f0508: ldur            x0, [fp, #-0x10]
    // 0x8f050c: LoadField: r1 = r0->field_23
    //     0x8f050c: ldur            w1, [x0, #0x23]
    // 0x8f0510: DecompressPointer r1
    //     0x8f0510: add             x1, x1, HEAP, lsl #32
    // 0x8f0514: r2 = 2
    //     0x8f0514: movz            x2, #0x2
    // 0x8f0518: r0 = value=()
    //     0x8f0518: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f051c: ldur            x0, [fp, #-0x10]
    // 0x8f0520: LoadField: r1 = r0->field_27
    //     0x8f0520: ldur            w1, [x0, #0x27]
    // 0x8f0524: DecompressPointer r1
    //     0x8f0524: add             x1, x1, HEAP, lsl #32
    // 0x8f0528: r2 = false
    //     0x8f0528: add             x2, NULL, #0x30  ; false
    // 0x8f052c: r0 = value=()
    //     0x8f052c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f0530: ldur            x0, [fp, #-0x10]
    // 0x8f0534: LoadField: r1 = r0->field_2b
    //     0x8f0534: ldur            w1, [x0, #0x2b]
    // 0x8f0538: DecompressPointer r1
    //     0x8f0538: add             x1, x1, HEAP, lsl #32
    // 0x8f053c: r2 = false
    //     0x8f053c: add             x2, NULL, #0x30  ; false
    // 0x8f0540: r0 = value=()
    //     0x8f0540: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8f0544: ldur            x1, [fp, #-0x10]
    // 0x8f0548: r0 = page()
    //     0x8f0548: bl              #0x7da3b8  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::page
    // 0x8f054c: ldur            x1, [fp, #-0x10]
    // 0x8f0550: mov             x2, x0
    // 0x8f0554: r0 = onPageRequest()
    //     0x8f0554: bl              #0xe34c20  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onPageRequest
    // 0x8f0558: r0 = Null
    //     0x8f0558: mov             x0, NULL
    // 0x8f055c: r0 = ReturnAsyncNotFuture()
    //     0x8f055c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f0560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0564: b               #0x8f04d8
  }
  [closure] Future<void> onPageRefresh(dynamic) {
    // ** addr: 0x8f0568, size: 0x38
    // 0x8f0568: EnterFrame
    //     0x8f0568: stp             fp, lr, [SP, #-0x10]!
    //     0x8f056c: mov             fp, SP
    // 0x8f0570: ldr             x0, [fp, #0x10]
    // 0x8f0574: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0574: ldur            w1, [x0, #0x17]
    // 0x8f0578: DecompressPointer r1
    //     0x8f0578: add             x1, x1, HEAP, lsl #32
    // 0x8f057c: CheckStackOverflow
    //     0x8f057c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0580: cmp             SP, x16
    //     0x8f0584: b.ls            #0x8f0598
    // 0x8f0588: r0 = onPageRefresh()
    //     0x8f0588: bl              #0x8f04b8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh
    // 0x8f058c: LeaveFrame
    //     0x8f058c: mov             SP, fp
    //     0x8f0590: ldp             fp, lr, [SP], #0x10
    // 0x8f0594: ret
    //     0x8f0594: ret             
    // 0x8f0598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f059c: b               #0x8f0588
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91e038, size: 0x58
    // 0x91e038: EnterFrame
    //     0x91e038: stp             fp, lr, [SP, #-0x10]!
    //     0x91e03c: mov             fp, SP
    // 0x91e040: AllocStack(0x8)
    //     0x91e040: sub             SP, SP, #8
    // 0x91e044: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x91e044: mov             x0, x1
    //     0x91e048: stur            x1, [fp, #-8]
    // 0x91e04c: CheckStackOverflow
    //     0x91e04c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e050: cmp             SP, x16
    //     0x91e054: b.ls            #0x91e088
    // 0x91e058: LoadField: r1 = r0->field_23
    //     0x91e058: ldur            w1, [x0, #0x23]
    // 0x91e05c: DecompressPointer r1
    //     0x91e05c: add             x1, x1, HEAP, lsl #32
    // 0x91e060: r0 = value()
    //     0x91e060: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x91e064: r2 = LoadInt32Instr(r0)
    //     0x91e064: sbfx            x2, x0, #1, #0x1f
    //     0x91e068: tbz             w0, #0, #0x91e070
    //     0x91e06c: ldur            x2, [x0, #7]
    // 0x91e070: ldur            x1, [fp, #-8]
    // 0x91e074: r0 = onPageRequest()
    //     0x91e074: bl              #0xe34c20  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onPageRequest
    // 0x91e078: r0 = Null
    //     0x91e078: mov             x0, NULL
    // 0x91e07c: LeaveFrame
    //     0x91e07c: mov             SP, fp
    //     0x91e080: ldp             fp, lr, [SP], #0x10
    // 0x91e084: ret
    //     0x91e084: ret             
    // 0x91e088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e08c: b               #0x91e058
  }
  [closure] bool onPageScrolled(dynamic, ScrollNotification) {
    // ** addr: 0xaf2708, size: 0x3c
    // 0xaf2708: EnterFrame
    //     0xaf2708: stp             fp, lr, [SP, #-0x10]!
    //     0xaf270c: mov             fp, SP
    // 0xaf2710: ldr             x0, [fp, #0x18]
    // 0xaf2714: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf2714: ldur            w1, [x0, #0x17]
    // 0xaf2718: DecompressPointer r1
    //     0xaf2718: add             x1, x1, HEAP, lsl #32
    // 0xaf271c: CheckStackOverflow
    //     0xaf271c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2720: cmp             SP, x16
    //     0xaf2724: b.ls            #0xaf273c
    // 0xaf2728: ldr             x2, [fp, #0x10]
    // 0xaf272c: r0 = onPageScrolled()
    //     0xaf272c: bl              #0xaf2744  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageScrolled
    // 0xaf2730: LeaveFrame
    //     0xaf2730: mov             SP, fp
    //     0xaf2734: ldp             fp, lr, [SP], #0x10
    // 0xaf2738: ret
    //     0xaf2738: ret             
    // 0xaf273c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf273c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2740: b               #0xaf2728
  }
  _ onPageScrolled(/* No info */) {
    // ** addr: 0xaf2744, size: 0xcc
    // 0xaf2744: EnterFrame
    //     0xaf2744: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2748: mov             fp, SP
    // 0xaf274c: AllocStack(0x10)
    //     0xaf274c: sub             SP, SP, #0x10
    // 0xaf2750: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaf2750: mov             x0, x1
    //     0xaf2754: stur            x1, [fp, #-8]
    //     0xaf2758: stur            x2, [fp, #-0x10]
    // 0xaf275c: CheckStackOverflow
    //     0xaf275c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2760: cmp             SP, x16
    //     0xaf2764: b.ls            #0xaf2800
    // 0xaf2768: mov             x1, x0
    // 0xaf276c: r0 = isFetching()
    //     0xaf276c: bl              #0xad3170  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::isFetching
    // 0xaf2770: tbz             w0, #4, #0xaf2784
    // 0xaf2774: r0 = true
    //     0xaf2774: add             x0, NULL, #0x20  ; true
    // 0xaf2778: LeaveFrame
    //     0xaf2778: mov             SP, fp
    //     0xaf277c: ldp             fp, lr, [SP], #0x10
    // 0xaf2780: ret
    //     0xaf2780: ret             
    // 0xaf2784: ldur            x0, [fp, #-0x10]
    // 0xaf2788: LoadField: r1 = r0->field_f
    //     0xaf2788: ldur            w1, [x0, #0xf]
    // 0xaf278c: DecompressPointer r1
    //     0xaf278c: add             x1, x1, HEAP, lsl #32
    // 0xaf2790: LoadField: r0 = r1->field_f
    //     0xaf2790: ldur            w0, [x1, #0xf]
    // 0xaf2794: DecompressPointer r0
    //     0xaf2794: add             x0, x0, HEAP, lsl #32
    // 0xaf2798: cmp             w0, NULL
    // 0xaf279c: b.eq            #0xaf2808
    // 0xaf27a0: LoadField: r2 = r1->field_b
    //     0xaf27a0: ldur            w2, [x1, #0xb]
    // 0xaf27a4: DecompressPointer r2
    //     0xaf27a4: add             x2, x2, HEAP, lsl #32
    // 0xaf27a8: cmp             w2, NULL
    // 0xaf27ac: b.eq            #0xaf280c
    // 0xaf27b0: LoadField: d0 = r0->field_7
    //     0xaf27b0: ldur            d0, [x0, #7]
    // 0xaf27b4: LoadField: d1 = r2->field_7
    //     0xaf27b4: ldur            d1, [x2, #7]
    // 0xaf27b8: fcmp            d0, d1
    // 0xaf27bc: b.ne            #0xaf27f0
    // 0xaf27c0: ldur            x1, [fp, #-8]
    // 0xaf27c4: r0 = hasError()
    //     0xaf27c4: bl              #0xad1aa0  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError
    // 0xaf27c8: tbz             w0, #4, #0xaf27f0
    // 0xaf27cc: ldur            x0, [fp, #-8]
    // 0xaf27d0: LoadField: r1 = r0->field_23
    //     0xaf27d0: ldur            w1, [x0, #0x23]
    // 0xaf27d4: DecompressPointer r1
    //     0xaf27d4: add             x1, x1, HEAP, lsl #32
    // 0xaf27d8: r0 = value()
    //     0xaf27d8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf27dc: r2 = LoadInt32Instr(r0)
    //     0xaf27dc: sbfx            x2, x0, #1, #0x1f
    //     0xaf27e0: tbz             w0, #0, #0xaf27e8
    //     0xaf27e4: ldur            x2, [x0, #7]
    // 0xaf27e8: ldur            x1, [fp, #-8]
    // 0xaf27ec: r0 = onPageRequest()
    //     0xaf27ec: bl              #0xe34c20  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onPageRequest
    // 0xaf27f0: r0 = false
    //     0xaf27f0: add             x0, NULL, #0x30  ; false
    // 0xaf27f4: LeaveFrame
    //     0xaf27f4: mov             SP, fp
    //     0xaf27f8: ldp             fp, lr, [SP], #0x10
    // 0xaf27fc: ret
    //     0xaf27fc: ret             
    // 0xaf2800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2804: b               #0xaf2768
    // 0xaf2808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2808: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf280c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf280c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ find(/* No info */) {
    // ** addr: 0xaf2aec, size: 0x120
    // 0xaf2aec: EnterFrame
    //     0xaf2aec: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2af0: mov             fp, SP
    // 0xaf2af4: AllocStack(0x68)
    //     0xaf2af4: sub             SP, SP, #0x68
    // 0xaf2af8: SetupParameters(dynamic _ /* r2 => r2, fp-0x58 */)
    //     0xaf2af8: stur            x2, [fp, #-0x58]
    // 0xaf2afc: CheckStackOverflow
    //     0xaf2afc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2b00: cmp             SP, x16
    //     0xaf2b04: b.ls            #0xaf2c04
    // 0xaf2b08: LoadField: r0 = r1->field_33
    //     0xaf2b08: ldur            w0, [x1, #0x33]
    // 0xaf2b0c: DecompressPointer r0
    //     0xaf2b0c: add             x0, x0, HEAP, lsl #32
    // 0xaf2b10: mov             x1, x0
    // 0xaf2b14: stur            x0, [fp, #-0x50]
    // 0xaf2b18: r0 = value()
    //     0xaf2b18: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2b1c: r1 = LoadClassIdInstr(r0)
    //     0xaf2b1c: ldur            x1, [x0, #-1]
    //     0xaf2b20: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2b24: str             x0, [SP]
    // 0xaf2b28: mov             x0, x1
    // 0xaf2b2c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf2b2c: movz            x17, #0xc834
    //     0xaf2b30: add             lr, x0, x17
    //     0xaf2b34: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2b38: blr             lr
    // 0xaf2b3c: cbz             w0, #0xaf2be0
    // 0xaf2b40: ldur            x0, [fp, #-0x58]
    // 0xaf2b44: ldur            x1, [fp, #-0x50]
    // 0xaf2b48: r0 = value()
    //     0xaf2b48: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2b4c: r1 = LoadClassIdInstr(r0)
    //     0xaf2b4c: ldur            x1, [x0, #-1]
    //     0xaf2b50: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2b54: str             x0, [SP]
    // 0xaf2b58: mov             x0, x1
    // 0xaf2b5c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf2b5c: movz            x17, #0xc834
    //     0xaf2b60: add             lr, x0, x17
    //     0xaf2b64: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2b68: blr             lr
    // 0xaf2b6c: r1 = LoadInt32Instr(r0)
    //     0xaf2b6c: sbfx            x1, x0, #1, #0x1f
    //     0xaf2b70: tbz             w0, #0, #0xaf2b78
    //     0xaf2b74: ldur            x1, [x0, #7]
    // 0xaf2b78: ldur            x0, [fp, #-0x58]
    // 0xaf2b7c: cmp             x1, x0
    // 0xaf2b80: b.le            #0xaf2bd0
    // 0xaf2b84: ldur            x1, [fp, #-0x50]
    // 0xaf2b88: r0 = value()
    //     0xaf2b88: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2b8c: mov             x3, x0
    // 0xaf2b90: ldur            x2, [fp, #-0x58]
    // 0xaf2b94: r0 = BoxInt64Instr(r2)
    //     0xaf2b94: sbfiz           x0, x2, #1, #0x1f
    //     0xaf2b98: cmp             x2, x0, asr #1
    //     0xaf2b9c: b.eq            #0xaf2ba8
    //     0xaf2ba0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf2ba4: stur            x2, [x0, #7]
    // 0xaf2ba8: r1 = LoadClassIdInstr(r3)
    //     0xaf2ba8: ldur            x1, [x3, #-1]
    //     0xaf2bac: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2bb0: stp             x0, x3, [SP]
    // 0xaf2bb4: mov             x0, x1
    // 0xaf2bb8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf2bb8: movz            x17, #0x3037
    //     0xaf2bbc: movk            x17, #0x1, lsl #16
    //     0xaf2bc0: add             lr, x0, x17
    //     0xaf2bc4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2bc8: blr             lr
    // 0xaf2bcc: b               #0xaf2bd4
    // 0xaf2bd0: r0 = Null
    //     0xaf2bd0: mov             x0, NULL
    // 0xaf2bd4: LeaveFrame
    //     0xaf2bd4: mov             SP, fp
    //     0xaf2bd8: ldp             fp, lr, [SP], #0x10
    // 0xaf2bdc: ret
    //     0xaf2bdc: ret             
    // 0xaf2be0: r0 = Null
    //     0xaf2be0: mov             x0, NULL
    // 0xaf2be4: LeaveFrame
    //     0xaf2be4: mov             SP, fp
    //     0xaf2be8: ldp             fp, lr, [SP], #0x10
    // 0xaf2bec: ret
    //     0xaf2bec: ret             
    // 0xaf2bf0: sub             SP, fp, #0x68
    // 0xaf2bf4: r0 = Null
    //     0xaf2bf4: mov             x0, NULL
    // 0xaf2bf8: LeaveFrame
    //     0xaf2bf8: mov             SP, fp
    //     0xaf2bfc: ldp             fp, lr, [SP], #0x10
    // 0xaf2c00: ret
    //     0xaf2c00: ret             
    // 0xaf2c04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2c04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2c08: b               #0xaf2b08
  }
  get _ offset(/* No info */) {
    // ** addr: 0xbef57c, size: 0x50
    // 0xbef57c: EnterFrame
    //     0xbef57c: stp             fp, lr, [SP, #-0x10]!
    //     0xbef580: mov             fp, SP
    // 0xbef584: CheckStackOverflow
    //     0xbef584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef588: cmp             SP, x16
    //     0xbef58c: b.ls            #0xbef5c4
    // 0xbef590: LoadField: r0 = r1->field_23
    //     0xbef590: ldur            w0, [x1, #0x23]
    // 0xbef594: DecompressPointer r0
    //     0xbef594: add             x0, x0, HEAP, lsl #32
    // 0xbef598: mov             x1, x0
    // 0xbef59c: r0 = value()
    //     0xbef59c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbef5a0: r1 = LoadInt32Instr(r0)
    //     0xbef5a0: sbfx            x1, x0, #1, #0x1f
    //     0xbef5a4: tbz             w0, #0, #0xbef5ac
    //     0xbef5a8: ldur            x1, [x0, #7]
    // 0xbef5ac: sub             x2, x1, #1
    // 0xbef5b0: r16 = 20
    //     0xbef5b0: movz            x16, #0x14
    // 0xbef5b4: mul             x0, x2, x16
    // 0xbef5b8: LeaveFrame
    //     0xbef5b8: mov             SP, fp
    //     0xbef5bc: ldp             fp, lr, [SP], #0x10
    // 0xbef5c0: ret
    //     0xbef5c0: ret             
    // 0xbef5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef5c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef5c8: b               #0xbef590
  }
  _ appendToPage(/* No info */) {
    // ** addr: 0xbf5fb8, size: 0x118
    // 0xbf5fb8: EnterFrame
    //     0xbf5fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5fbc: mov             fp, SP
    // 0xbf5fc0: AllocStack(0x20)
    //     0xbf5fc0: sub             SP, SP, #0x20
    // 0xbf5fc4: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xbf5fc4: mov             x4, x1
    //     0xbf5fc8: mov             x0, x3
    //     0xbf5fcc: stur            x3, [fp, #-0x18]
    //     0xbf5fd0: mov             x3, x2
    //     0xbf5fd4: stur            x1, [fp, #-8]
    //     0xbf5fd8: stur            x2, [fp, #-0x10]
    // 0xbf5fdc: CheckStackOverflow
    //     0xbf5fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5fe0: cmp             SP, x16
    //     0xbf5fe4: b.ls            #0xbf60c8
    // 0xbf5fe8: LoadField: r1 = r4->field_2b
    //     0xbf5fe8: ldur            w1, [x4, #0x2b]
    // 0xbf5fec: DecompressPointer r1
    //     0xbf5fec: add             x1, x1, HEAP, lsl #32
    // 0xbf5ff0: r2 = false
    //     0xbf5ff0: add             x2, NULL, #0x30  ; false
    // 0xbf5ff4: r0 = value=()
    //     0xbf5ff4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf5ff8: ldur            x0, [fp, #-8]
    // 0xbf5ffc: LoadField: r1 = r0->field_27
    //     0xbf5ffc: ldur            w1, [x0, #0x27]
    // 0xbf6000: DecompressPointer r1
    //     0xbf6000: add             x1, x1, HEAP, lsl #32
    // 0xbf6004: ldur            x2, [fp, #-0x18]
    // 0xbf6008: r0 = value=()
    //     0xbf6008: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf600c: ldur            x0, [fp, #-8]
    // 0xbf6010: LoadField: r2 = r0->field_23
    //     0xbf6010: ldur            w2, [x0, #0x23]
    // 0xbf6014: DecompressPointer r2
    //     0xbf6014: add             x2, x2, HEAP, lsl #32
    // 0xbf6018: mov             x1, x2
    // 0xbf601c: stur            x2, [fp, #-0x20]
    // 0xbf6020: r0 = value()
    //     0xbf6020: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf6024: cmp             w0, #2
    // 0xbf6028: b.ne            #0xbf6064
    // 0xbf602c: ldur            x2, [fp, #-0x10]
    // 0xbf6030: r0 = LoadClassIdInstr(r2)
    //     0xbf6030: ldur            x0, [x2, #-1]
    //     0xbf6034: ubfx            x0, x0, #0xc, #0x14
    // 0xbf6038: mov             x1, x2
    // 0xbf603c: r0 = GDT[cid_x0 + 0xe879]()
    //     0xbf603c: movz            x17, #0xe879
    //     0xbf6040: add             lr, x0, x17
    //     0xbf6044: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6048: blr             lr
    // 0xbf604c: tbnz            w0, #4, #0xbf6064
    // 0xbf6050: ldur            x0, [fp, #-8]
    // 0xbf6054: LoadField: r1 = r0->field_37
    //     0xbf6054: ldur            w1, [x0, #0x37]
    // 0xbf6058: DecompressPointer r1
    //     0xbf6058: add             x1, x1, HEAP, lsl #32
    // 0xbf605c: r2 = true
    //     0xbf605c: add             x2, NULL, #0x20  ; true
    // 0xbf6060: r0 = value=()
    //     0xbf6060: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf6064: ldur            x0, [fp, #-0x18]
    // 0xbf6068: tbnz            w0, #4, #0xbf60a4
    // 0xbf606c: ldur            x1, [fp, #-0x20]
    // 0xbf6070: r0 = value()
    //     0xbf6070: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf6074: r1 = LoadInt32Instr(r0)
    //     0xbf6074: sbfx            x1, x0, #1, #0x1f
    //     0xbf6078: tbz             w0, #0, #0xbf6080
    //     0xbf607c: ldur            x1, [x0, #7]
    // 0xbf6080: add             x2, x1, #1
    // 0xbf6084: r0 = BoxInt64Instr(r2)
    //     0xbf6084: sbfiz           x0, x2, #1, #0x1f
    //     0xbf6088: cmp             x2, x0, asr #1
    //     0xbf608c: b.eq            #0xbf6098
    //     0xbf6090: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf6094: stur            x2, [x0, #7]
    // 0xbf6098: ldur            x1, [fp, #-0x20]
    // 0xbf609c: mov             x2, x0
    // 0xbf60a0: r0 = value=()
    //     0xbf60a0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbf60a4: ldur            x0, [fp, #-8]
    // 0xbf60a8: LoadField: r1 = r0->field_33
    //     0xbf60a8: ldur            w1, [x0, #0x33]
    // 0xbf60ac: DecompressPointer r1
    //     0xbf60ac: add             x1, x1, HEAP, lsl #32
    // 0xbf60b0: ldur            x2, [fp, #-0x10]
    // 0xbf60b4: r0 = addAll()
    //     0xbf60b4: bl              #0x667efc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::addAll
    // 0xbf60b8: r0 = Null
    //     0xbf60b8: mov             x0, NULL
    // 0xbf60bc: LeaveFrame
    //     0xbf60bc: mov             SP, fp
    //     0xbf60c0: ldp             fp, lr, [SP], #0x10
    // 0xbf60c4: ret
    //     0xbf60c4: ret             
    // 0xbf60c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf60c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf60cc: b               #0xbf5fe8
  }
}

// class id: 1981, size: 0x44, field offset: 0x3c
//   transformed mixin,
abstract class _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin extends _EncyclopediaListController&OfflineFirstController&PagingMixin
     with SearchMixin {

  late TextEditingController searchController; // offset: 0x3c

  _ _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin(/* No info */) {
    // ** addr: 0x8128f0, size: 0x94
    // 0x8128f0: EnterFrame
    //     0x8128f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8128f4: mov             fp, SP
    // 0x8128f8: AllocStack(0x18)
    //     0x8128f8: sub             SP, SP, #0x18
    // 0x8128fc: r0 = Sentinel
    //     0x8128fc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x812900: mov             x2, x1
    // 0x812904: stur            x1, [fp, #-8]
    // 0x812908: CheckStackOverflow
    //     0x812908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81290c: cmp             SP, x16
    //     0x812910: b.ls            #0x81297c
    // 0x812914: StoreField: r2->field_3b = r0
    //     0x812914: stur            w0, [x2, #0x3b]
    // 0x812918: r1 = ""
    //     0x812918: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x81291c: r0 = StringExtension.obs()
    //     0x81291c: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x812920: ldur            x3, [fp, #-8]
    // 0x812924: StoreField: r3->field_3f = r0
    //     0x812924: stur            w0, [x3, #0x3f]
    //     0x812928: ldurb           w16, [x3, #-1]
    //     0x81292c: ldurb           w17, [x0, #-1]
    //     0x812930: and             x16, x17, x16, lsr #2
    //     0x812934: tst             x16, HEAP, lsr #32
    //     0x812938: b.eq            #0x812940
    //     0x81293c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x812940: r1 = <String>
    //     0x812940: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x812944: r2 = 0
    //     0x812944: movz            x2, #0
    // 0x812948: r0 = _GrowableList()
    //     0x812948: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x81294c: r16 = <String>
    //     0x81294c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x812950: stp             x0, x16, [SP]
    // 0x812954: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812954: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812958: r0 = ListExtension.obs()
    //     0x812958: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x81295c: r1 = true
    //     0x81295c: add             x1, NULL, #0x20  ; true
    // 0x812960: r0 = BoolExtension.obs()
    //     0x812960: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x812964: ldur            x1, [fp, #-8]
    // 0x812968: r0 = _EncyclopediaListController&OfflineFirstController&PagingMixin()
    //     0x812968: bl              #0x812984  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::_EncyclopediaListController&OfflineFirstController&PagingMixin
    // 0x81296c: r0 = Null
    //     0x81296c: mov             x0, NULL
    // 0x812970: LeaveFrame
    //     0x812970: mov             SP, fp
    //     0x812974: ldp             fp, lr, [SP], #0x10
    // 0x812978: ret
    //     0x812978: ret             
    // 0x81297c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81297c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812980: b               #0x812914
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f03f8, size: 0x7c
    // 0x8f03f8: EnterFrame
    //     0x8f03f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f03fc: mov             fp, SP
    // 0x8f0400: AllocStack(0x10)
    //     0x8f0400: sub             SP, SP, #0x10
    // 0x8f0404: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin this /* r1 => r0, fp-0x8 */)
    //     0x8f0404: mov             x0, x1
    //     0x8f0408: stur            x1, [fp, #-8]
    // 0x8f040c: CheckStackOverflow
    //     0x8f040c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0410: cmp             SP, x16
    //     0x8f0414: b.ls            #0x8f046c
    // 0x8f0418: mov             x1, x0
    // 0x8f041c: r0 = onInit()
    //     0x8f041c: bl              #0x8efe30  ; [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstController::onInit
    // 0x8f0420: r1 = <TextEditingValue>
    //     0x8f0420: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f0424: r0 = TextEditingController()
    //     0x8f0424: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f0428: mov             x1, x0
    // 0x8f042c: stur            x0, [fp, #-0x10]
    // 0x8f0430: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f0430: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f0434: r0 = TextEditingController()
    //     0x8f0434: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f0438: ldur            x0, [fp, #-0x10]
    // 0x8f043c: ldur            x1, [fp, #-8]
    // 0x8f0440: StoreField: r1->field_3b = r0
    //     0x8f0440: stur            w0, [x1, #0x3b]
    //     0x8f0444: ldurb           w16, [x1, #-1]
    //     0x8f0448: ldurb           w17, [x0, #-1]
    //     0x8f044c: and             x16, x17, x16, lsr #2
    //     0x8f0450: tst             x16, HEAP, lsr #32
    //     0x8f0454: b.eq            #0x8f045c
    //     0x8f0458: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8f045c: r0 = Null
    //     0x8f045c: mov             x0, NULL
    // 0x8f0460: LeaveFrame
    //     0x8f0460: mov             SP, fp
    //     0x8f0464: ldp             fp, lr, [SP], #0x10
    // 0x8f0468: ret
    //     0x8f0468: ret             
    // 0x8f046c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f046c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0470: b               #0x8f0418
  }
  get _ query(/* No info */) {
    // ** addr: 0xaf2ab4, size: 0x38
    // 0xaf2ab4: EnterFrame
    //     0xaf2ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2ab8: mov             fp, SP
    // 0xaf2abc: CheckStackOverflow
    //     0xaf2abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2ac0: cmp             SP, x16
    //     0xaf2ac4: b.ls            #0xaf2ae4
    // 0xaf2ac8: LoadField: r0 = r1->field_3f
    //     0xaf2ac8: ldur            w0, [x1, #0x3f]
    // 0xaf2acc: DecompressPointer r0
    //     0xaf2acc: add             x0, x0, HEAP, lsl #32
    // 0xaf2ad0: mov             x1, x0
    // 0xaf2ad4: r0 = value()
    //     0xaf2ad4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf2ad8: LeaveFrame
    //     0xaf2ad8: mov             SP, fp
    //     0xaf2adc: ldp             fp, lr, [SP], #0x10
    // 0xaf2ae0: ret
    //     0xaf2ae0: ret             
    // 0xaf2ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2ae8: b               #0xaf2ac8
  }
  _ onSearchCanceled(/* No info */) {
    // ** addr: 0xaf3280, size: 0x40
    // 0xaf3280: EnterFrame
    //     0xaf3280: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3284: mov             fp, SP
    // 0xaf3288: CheckStackOverflow
    //     0xaf3288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf328c: cmp             SP, x16
    //     0xaf3290: b.ls            #0xaf32b8
    // 0xaf3294: LoadField: r0 = r1->field_3f
    //     0xaf3294: ldur            w0, [x1, #0x3f]
    // 0xaf3298: DecompressPointer r0
    //     0xaf3298: add             x0, x0, HEAP, lsl #32
    // 0xaf329c: mov             x1, x0
    // 0xaf32a0: r2 = ""
    //     0xaf32a0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf32a4: r0 = value=()
    //     0xaf32a4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf32a8: r0 = ""
    //     0xaf32a8: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf32ac: LeaveFrame
    //     0xaf32ac: mov             SP, fp
    //     0xaf32b0: ldp             fp, lr, [SP], #0x10
    // 0xaf32b4: ret
    //     0xaf32b4: ret             
    // 0xaf32b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf32b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf32bc: b               #0xaf3294
  }
  [closure] void onQueryChanged(dynamic, String) {
    // ** addr: 0xaf32c0, size: 0x3c
    // 0xaf32c0: EnterFrame
    //     0xaf32c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf32c4: mov             fp, SP
    // 0xaf32c8: ldr             x0, [fp, #0x18]
    // 0xaf32cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf32cc: ldur            w1, [x0, #0x17]
    // 0xaf32d0: DecompressPointer r1
    //     0xaf32d0: add             x1, x1, HEAP, lsl #32
    // 0xaf32d4: CheckStackOverflow
    //     0xaf32d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf32d8: cmp             SP, x16
    //     0xaf32dc: b.ls            #0xaf32f4
    // 0xaf32e0: ldr             x2, [fp, #0x10]
    // 0xaf32e4: r0 = onQueryChanged()
    //     0xaf32e4: bl              #0xaf32fc  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::onQueryChanged
    // 0xaf32e8: LeaveFrame
    //     0xaf32e8: mov             SP, fp
    //     0xaf32ec: ldp             fp, lr, [SP], #0x10
    // 0xaf32f0: ret
    //     0xaf32f0: ret             
    // 0xaf32f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf32f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf32f8: b               #0xaf32e0
  }
  _ onQueryChanged(/* No info */) {
    // ** addr: 0xaf32fc, size: 0x4c
    // 0xaf32fc: EnterFrame
    //     0xaf32fc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3300: mov             fp, SP
    // 0xaf3304: AllocStack(0x8)
    //     0xaf3304: sub             SP, SP, #8
    // 0xaf3308: SetupParameters(_EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin this /* r1 => r0, fp-0x8 */)
    //     0xaf3308: mov             x0, x1
    //     0xaf330c: stur            x1, [fp, #-8]
    // 0xaf3310: CheckStackOverflow
    //     0xaf3310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3314: cmp             SP, x16
    //     0xaf3318: b.ls            #0xaf3340
    // 0xaf331c: LoadField: r1 = r0->field_3f
    //     0xaf331c: ldur            w1, [x0, #0x3f]
    // 0xaf3320: DecompressPointer r1
    //     0xaf3320: add             x1, x1, HEAP, lsl #32
    // 0xaf3324: r0 = value=()
    //     0xaf3324: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf3328: ldur            x1, [fp, #-8]
    // 0xaf332c: r0 = onPageRefresh()
    //     0xaf332c: bl              #0x8f04b8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh
    // 0xaf3330: r0 = Null
    //     0xaf3330: mov             x0, NULL
    // 0xaf3334: LeaveFrame
    //     0xaf3334: mov             SP, fp
    //     0xaf3338: ldp             fp, lr, [SP], #0x10
    // 0xaf333c: ret
    //     0xaf333c: ret             
    // 0xaf3340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3344: b               #0xaf331c
  }
}

// class id: 1982, size: 0x44, field offset: 0x44
//   transformed mixin,
abstract class _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin&AnalyticMixin extends _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin
     with AnalyticMixin {
}

// class id: 1983, size: 0x60, field offset: 0x44
class EncyclopediaListController extends _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin&AnalyticMixin {

  late TextEditingController textController; // offset: 0x54

  _ EncyclopediaListController(/* No info */) {
    // ** addr: 0x812788, size: 0x148
    // 0x812788: EnterFrame
    //     0x812788: stp             fp, lr, [SP, #-0x10]!
    //     0x81278c: mov             fp, SP
    // 0x812790: AllocStack(0x38)
    //     0x812790: sub             SP, SP, #0x38
    // 0x812794: r0 = Sentinel
    //     0x812794: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x812798: mov             x4, x3
    // 0x81279c: stur            x3, [fp, #-0x18]
    // 0x8127a0: mov             x3, x5
    // 0x8127a4: stur            x5, [fp, #-0x20]
    // 0x8127a8: mov             x5, x2
    // 0x8127ac: stur            x2, [fp, #-0x10]
    // 0x8127b0: mov             x2, x6
    // 0x8127b4: stur            x6, [fp, #-0x28]
    // 0x8127b8: mov             x6, x1
    // 0x8127bc: stur            x1, [fp, #-8]
    // 0x8127c0: CheckStackOverflow
    //     0x8127c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8127c4: cmp             SP, x16
    //     0x8127c8: b.ls            #0x8128c8
    // 0x8127cc: StoreField: r6->field_53 = r0
    //     0x8127cc: stur            w0, [x6, #0x53]
    // 0x8127d0: r1 = "#"
    //     0x8127d0: ldr             x1, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x8127d4: r0 = StringExtension.obs()
    //     0x8127d4: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x8127d8: ldur            x3, [fp, #-8]
    // 0x8127dc: StoreField: r3->field_57 = r0
    //     0x8127dc: stur            w0, [x3, #0x57]
    //     0x8127e0: ldurb           w16, [x3, #-1]
    //     0x8127e4: ldurb           w17, [x0, #-1]
    //     0x8127e8: and             x16, x17, x16, lsr #2
    //     0x8127ec: tst             x16, HEAP, lsr #32
    //     0x8127f0: b.eq            #0x8127f8
    //     0x8127f4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8127f8: r1 = <String>
    //     0x8127f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8127fc: r2 = 0
    //     0x8127fc: movz            x2, #0
    // 0x812800: r0 = _GrowableList()
    //     0x812800: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x812804: r16 = <String>
    //     0x812804: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x812808: stp             x0, x16, [SP]
    // 0x81280c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x81280c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812810: r0 = ListExtension.obs()
    //     0x812810: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x812814: ldur            x1, [fp, #-8]
    // 0x812818: StoreField: r1->field_5b = r0
    //     0x812818: stur            w0, [x1, #0x5b]
    //     0x81281c: ldurb           w16, [x1, #-1]
    //     0x812820: ldurb           w17, [x0, #-1]
    //     0x812824: and             x16, x17, x16, lsr #2
    //     0x812828: tst             x16, HEAP, lsr #32
    //     0x81282c: b.eq            #0x812834
    //     0x812830: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812834: ldur            x0, [fp, #-0x18]
    // 0x812838: StoreField: r1->field_43 = r0
    //     0x812838: stur            w0, [x1, #0x43]
    //     0x81283c: ldurb           w16, [x1, #-1]
    //     0x812840: ldurb           w17, [x0, #-1]
    //     0x812844: and             x16, x17, x16, lsr #2
    //     0x812848: tst             x16, HEAP, lsr #32
    //     0x81284c: b.eq            #0x812854
    //     0x812850: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812854: ldur            x0, [fp, #-0x20]
    // 0x812858: StoreField: r1->field_47 = r0
    //     0x812858: stur            w0, [x1, #0x47]
    //     0x81285c: ldurb           w16, [x1, #-1]
    //     0x812860: ldurb           w17, [x0, #-1]
    //     0x812864: and             x16, x17, x16, lsr #2
    //     0x812868: tst             x16, HEAP, lsr #32
    //     0x81286c: b.eq            #0x812874
    //     0x812870: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812874: ldur            x0, [fp, #-0x28]
    // 0x812878: StoreField: r1->field_4f = r0
    //     0x812878: stur            w0, [x1, #0x4f]
    //     0x81287c: ldurb           w16, [x1, #-1]
    //     0x812880: ldurb           w17, [x0, #-1]
    //     0x812884: and             x16, x17, x16, lsr #2
    //     0x812888: tst             x16, HEAP, lsr #32
    //     0x81288c: b.eq            #0x812894
    //     0x812890: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812894: ldur            x0, [fp, #-0x10]
    // 0x812898: StoreField: r1->field_4b = r0
    //     0x812898: stur            w0, [x1, #0x4b]
    //     0x81289c: ldurb           w16, [x1, #-1]
    //     0x8128a0: ldurb           w17, [x0, #-1]
    //     0x8128a4: and             x16, x17, x16, lsr #2
    //     0x8128a8: tst             x16, HEAP, lsr #32
    //     0x8128ac: b.eq            #0x8128b4
    //     0x8128b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8128b4: r0 = _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin()
    //     0x8128b4: bl              #0x8128f0  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::_EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin
    // 0x8128b8: r0 = Null
    //     0x8128b8: mov             x0, NULL
    // 0x8128bc: LeaveFrame
    //     0x8128bc: mov             SP, fp
    //     0x8128c0: ldp             fp, lr, [SP], #0x10
    // 0x8128c4: ret
    //     0x8128c4: ret             
    // 0x8128c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8128c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8128cc: b               #0x8127cc
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0x8eff90, size: 0x84
    // 0x8eff90: EnterFrame
    //     0x8eff90: stp             fp, lr, [SP, #-0x10]!
    //     0x8eff94: mov             fp, SP
    // 0x8eff98: AllocStack(0x10)
    //     0x8eff98: sub             SP, SP, #0x10
    // 0x8eff9c: SetupParameters()
    //     0x8eff9c: ldr             x0, [fp, #0x18]
    //     0x8effa0: ldur            w3, [x0, #0x17]
    //     0x8effa4: add             x3, x3, HEAP, lsl #32
    //     0x8effa8: stur            x3, [fp, #-8]
    // 0x8effac: CheckStackOverflow
    //     0x8effac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8effb0: cmp             SP, x16
    //     0x8effb4: b.ls            #0x8f000c
    // 0x8effb8: ldr             x2, [fp, #0x10]
    // 0x8effbc: cmp             w2, NULL
    // 0x8effc0: b.eq            #0x8efffc
    // 0x8effc4: LoadField: r1 = r3->field_f
    //     0x8effc4: ldur            w1, [x3, #0xf]
    // 0x8effc8: DecompressPointer r1
    //     0x8effc8: add             x1, x1, HEAP, lsl #32
    // 0x8effcc: r0 = executeOnlineMode()
    //     0x8effcc: bl              #0x8f0014  ; [package:nuonline/common/mixins/offline_first_mixin.dart] _OfflineFirstController&GetxController&OfflineMixin&OnlineSyncMixin::executeOnlineMode
    // 0x8effd0: mov             x3, x0
    // 0x8effd4: ldur            x0, [fp, #-8]
    // 0x8effd8: stur            x3, [fp, #-0x10]
    // 0x8effdc: LoadField: r2 = r0->field_f
    //     0x8effdc: ldur            w2, [x0, #0xf]
    // 0x8effe0: DecompressPointer r2
    //     0x8effe0: add             x2, x2, HEAP, lsl #32
    // 0x8effe4: r1 = Function 'executeOfflineMode':.
    //     0x8effe4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff30] AnonymousClosure: (0x8efa40), in [package:nuonline/common/mixins/offline_first_mixin.dart] _OfflineFirstController&GetxController&OfflineMixin::executeOfflineMode (0x8ef81c)
    //     0x8effe8: ldr             x1, [x1, #0xf30]
    // 0x8effec: r0 = AllocateClosure()
    //     0x8effec: bl              #0xec1630  ; AllocateClosureStub
    // 0x8efff0: ldur            x1, [fp, #-0x10]
    // 0x8efff4: mov             x2, x0
    // 0x8efff8: r0 = whenComplete()
    //     0x8efff8: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0x8efffc: r0 = Null
    //     0x8efffc: mov             x0, NULL
    // 0x8f0000: LeaveFrame
    //     0x8f0000: mov             SP, fp
    //     0x8f0004: ldp             fp, lr, [SP], #0x10
    // 0x8f0008: ret
    //     0x8f0008: ret             
    // 0x8f000c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f000c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0010: b               #0x8effb8
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f02f8, size: 0x100
    // 0x8f02f8: EnterFrame
    //     0x8f02f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f02fc: mov             fp, SP
    // 0x8f0300: AllocStack(0x30)
    //     0x8f0300: sub             SP, SP, #0x30
    // 0x8f0304: SetupParameters(EncyclopediaListController this /* r1 => r1, fp-0x8 */)
    //     0x8f0304: stur            x1, [fp, #-8]
    // 0x8f0308: CheckStackOverflow
    //     0x8f0308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f030c: cmp             SP, x16
    //     0x8f0310: b.ls            #0x8f03f0
    // 0x8f0314: r1 = 1
    //     0x8f0314: movz            x1, #0x1
    // 0x8f0318: r0 = AllocateContext()
    //     0x8f0318: bl              #0xec126c  ; AllocateContextStub
    // 0x8f031c: mov             x2, x0
    // 0x8f0320: ldur            x0, [fp, #-8]
    // 0x8f0324: stur            x2, [fp, #-0x10]
    // 0x8f0328: StoreField: r2->field_f = r0
    //     0x8f0328: stur            w0, [x2, #0xf]
    // 0x8f032c: mov             x1, x0
    // 0x8f0330: r0 = onInit()
    //     0x8f0330: bl              #0x8f03f8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::onInit
    // 0x8f0334: r1 = <TextEditingValue>
    //     0x8f0334: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8f0338: r0 = TextEditingController()
    //     0x8f0338: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8f033c: mov             x1, x0
    // 0x8f0340: stur            x0, [fp, #-0x18]
    // 0x8f0344: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8f0344: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8f0348: r0 = TextEditingController()
    //     0x8f0348: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8f034c: ldur            x0, [fp, #-0x18]
    // 0x8f0350: ldur            x3, [fp, #-8]
    // 0x8f0354: StoreField: r3->field_53 = r0
    //     0x8f0354: stur            w0, [x3, #0x53]
    //     0x8f0358: ldurb           w16, [x3, #-1]
    //     0x8f035c: ldurb           w17, [x0, #-1]
    //     0x8f0360: and             x16, x17, x16, lsr #2
    //     0x8f0364: tst             x16, HEAP, lsr #32
    //     0x8f0368: b.eq            #0x8f0370
    //     0x8f036c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8f0370: LoadField: r0 = r3->field_4b
    //     0x8f0370: ldur            w0, [x3, #0x4b]
    // 0x8f0374: DecompressPointer r0
    //     0x8f0374: add             x0, x0, HEAP, lsl #32
    // 0x8f0378: cmp             w0, NULL
    // 0x8f037c: b.ne            #0x8f03ac
    // 0x8f0380: LoadField: r0 = r3->field_57
    //     0x8f0380: ldur            w0, [x3, #0x57]
    // 0x8f0384: DecompressPointer r0
    //     0x8f0384: add             x0, x0, HEAP, lsl #32
    // 0x8f0388: ldur            x2, [fp, #-0x10]
    // 0x8f038c: stur            x0, [fp, #-0x18]
    // 0x8f0390: r1 = Function '<anonymous closure>':.
    //     0x8f0390: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff20] AnonymousClosure: (0x8f0474), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onInit (0x8f02f8)
    //     0x8f0394: ldr             x1, [x1, #0xf20]
    // 0x8f0398: r0 = AllocateClosure()
    //     0x8f0398: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f039c: ldur            x1, [fp, #-0x18]
    // 0x8f03a0: mov             x2, x0
    // 0x8f03a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f03a4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f03a8: r0 = listen()
    //     0x8f03a8: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8f03ac: ldur            x1, [fp, #-8]
    // 0x8f03b0: r0 = lastUpdatedAt()
    //     0x8f03b0: bl              #0xe37d8c  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::lastUpdatedAt
    // 0x8f03b4: ldur            x2, [fp, #-0x10]
    // 0x8f03b8: r1 = Function '<anonymous closure>':.
    //     0x8f03b8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff28] AnonymousClosure: (0x8eff90), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onInit (0x8f02f8)
    //     0x8f03bc: ldr             x1, [x1, #0xf28]
    // 0x8f03c0: stur            x0, [fp, #-8]
    // 0x8f03c4: r0 = AllocateClosure()
    //     0x8f03c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f03c8: r16 = <Null?>
    //     0x8f03c8: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x8f03cc: ldur            lr, [fp, #-8]
    // 0x8f03d0: stp             lr, x16, [SP, #8]
    // 0x8f03d4: str             x0, [SP]
    // 0x8f03d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f03d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f03dc: r0 = then()
    //     0x8f03dc: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8f03e0: r0 = Null
    //     0x8f03e0: mov             x0, NULL
    // 0x8f03e4: LeaveFrame
    //     0x8f03e4: mov             SP, fp
    //     0x8f03e8: ldp             fp, lr, [SP], #0x10
    // 0x8f03ec: ret
    //     0x8f03ec: ret             
    // 0x8f03f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f03f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f03f4: b               #0x8f0314
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x8f0474, size: 0x44
    // 0x8f0474: EnterFrame
    //     0x8f0474: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0478: mov             fp, SP
    // 0x8f047c: ldr             x0, [fp, #0x18]
    // 0x8f0480: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f0480: ldur            w1, [x0, #0x17]
    // 0x8f0484: DecompressPointer r1
    //     0x8f0484: add             x1, x1, HEAP, lsl #32
    // 0x8f0488: CheckStackOverflow
    //     0x8f0488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f048c: cmp             SP, x16
    //     0x8f0490: b.ls            #0x8f04b0
    // 0x8f0494: LoadField: r0 = r1->field_f
    //     0x8f0494: ldur            w0, [x1, #0xf]
    // 0x8f0498: DecompressPointer r0
    //     0x8f0498: add             x0, x0, HEAP, lsl #32
    // 0x8f049c: mov             x1, x0
    // 0x8f04a0: r0 = onPageRefresh()
    //     0x8f04a0: bl              #0x8f04b8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh
    // 0x8f04a4: LeaveFrame
    //     0x8f04a4: mov             SP, fp
    //     0x8f04a8: ldp             fp, lr, [SP], #0x10
    // 0x8f04ac: ret
    //     0x8f04ac: ret             
    // 0x8f04b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f04b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f04b4: b               #0x8f0494
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9271c8, size: 0x54
    // 0x9271c8: EnterFrame
    //     0x9271c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9271cc: mov             fp, SP
    // 0x9271d0: CheckStackOverflow
    //     0x9271d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9271d4: cmp             SP, x16
    //     0x9271d8: b.ls            #0x927208
    // 0x9271dc: LoadField: r0 = r1->field_53
    //     0x9271dc: ldur            w0, [x1, #0x53]
    // 0x9271e0: DecompressPointer r0
    //     0x9271e0: add             x0, x0, HEAP, lsl #32
    // 0x9271e4: r16 = Sentinel
    //     0x9271e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9271e8: cmp             w0, w16
    // 0x9271ec: b.eq            #0x927210
    // 0x9271f0: mov             x1, x0
    // 0x9271f4: r0 = dispose()
    //     0x9271f4: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9271f8: r0 = Null
    //     0x9271f8: mov             x0, NULL
    // 0x9271fc: LeaveFrame
    //     0x9271fc: mov             SP, fp
    //     0x927200: ldp             fp, lr, [SP], #0x10
    // 0x927204: ret
    //     0x927204: ret             
    // 0x927208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927208: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92720c: b               #0x9271dc
    // 0x927210: r9 = textController
    //     0x927210: add             x9, PP, #0x3f, lsl #12  ; [pp+0x3ff18] Field <EncyclopediaListController.textController>: late (offset: 0x54)
    //     0x927214: ldr             x9, [x9, #0xf18]
    // 0x927218: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x927218: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void onSearchSubmitted(dynamic, String, {bool saveHistory}) {
    // ** addr: 0xaf31b8, size: 0x48
    // 0xaf31b8: EnterFrame
    //     0xaf31b8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf31bc: mov             fp, SP
    // 0xaf31c0: LoadField: r0 = r4->field_13
    //     0xaf31c0: ldur            w0, [x4, #0x13]
    // 0xaf31c4: sub             x1, x0, #4
    // 0xaf31c8: add             x0, fp, w1, sxtw #2
    // 0xaf31cc: ldr             x0, [x0, #0x18]
    // 0xaf31d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf31d0: ldur            w1, [x0, #0x17]
    // 0xaf31d4: DecompressPointer r1
    //     0xaf31d4: add             x1, x1, HEAP, lsl #32
    // 0xaf31d8: CheckStackOverflow
    //     0xaf31d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf31dc: cmp             SP, x16
    //     0xaf31e0: b.ls            #0xaf31f8
    // 0xaf31e4: r0 = onPageRefresh()
    //     0xaf31e4: bl              #0x8f04b8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh
    // 0xaf31e8: r0 = Null
    //     0xaf31e8: mov             x0, NULL
    // 0xaf31ec: LeaveFrame
    //     0xaf31ec: mov             SP, fp
    //     0xaf31f0: ldp             fp, lr, [SP], #0x10
    // 0xaf31f4: ret
    //     0xaf31f4: ret             
    // 0xaf31f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf31f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf31fc: b               #0xaf31e4
  }
  [closure] void onSearchCanceled(dynamic) {
    // ** addr: 0xaf3200, size: 0x38
    // 0xaf3200: EnterFrame
    //     0xaf3200: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3204: mov             fp, SP
    // 0xaf3208: ldr             x0, [fp, #0x10]
    // 0xaf320c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf320c: ldur            w1, [x0, #0x17]
    // 0xaf3210: DecompressPointer r1
    //     0xaf3210: add             x1, x1, HEAP, lsl #32
    // 0xaf3214: CheckStackOverflow
    //     0xaf3214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3218: cmp             SP, x16
    //     0xaf321c: b.ls            #0xaf3230
    // 0xaf3220: r0 = onSearchCanceled()
    //     0xaf3220: bl              #0xaf3238  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onSearchCanceled
    // 0xaf3224: LeaveFrame
    //     0xaf3224: mov             SP, fp
    //     0xaf3228: ldp             fp, lr, [SP], #0x10
    // 0xaf322c: ret
    //     0xaf322c: ret             
    // 0xaf3230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3230: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3234: b               #0xaf3220
  }
  _ onSearchCanceled(/* No info */) {
    // ** addr: 0xaf3238, size: 0x48
    // 0xaf3238: EnterFrame
    //     0xaf3238: stp             fp, lr, [SP, #-0x10]!
    //     0xaf323c: mov             fp, SP
    // 0xaf3240: AllocStack(0x8)
    //     0xaf3240: sub             SP, SP, #8
    // 0xaf3244: SetupParameters(EncyclopediaListController this /* r1 => r0, fp-0x8 */)
    //     0xaf3244: mov             x0, x1
    //     0xaf3248: stur            x1, [fp, #-8]
    // 0xaf324c: CheckStackOverflow
    //     0xaf324c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3250: cmp             SP, x16
    //     0xaf3254: b.ls            #0xaf3278
    // 0xaf3258: mov             x1, x0
    // 0xaf325c: r0 = onSearchCanceled()
    //     0xaf325c: bl              #0xaf3280  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::onSearchCanceled
    // 0xaf3260: ldur            x1, [fp, #-8]
    // 0xaf3264: r0 = onPageRefresh()
    //     0xaf3264: bl              #0x8f04b8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh
    // 0xaf3268: r0 = Null
    //     0xaf3268: mov             x0, NULL
    // 0xaf326c: LeaveFrame
    //     0xaf326c: mov             SP, fp
    //     0xaf3270: ldp             fp, lr, [SP], #0x10
    // 0xaf3274: ret
    //     0xaf3274: ret             
    // 0xaf3278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf327c: b               #0xaf3258
  }
  _ onOfflineModeRequested(/* No info */) {
    // ** addr: 0xbef434, size: 0x148
    // 0xbef434: EnterFrame
    //     0xbef434: stp             fp, lr, [SP, #-0x10]!
    //     0xbef438: mov             fp, SP
    // 0xbef43c: AllocStack(0x38)
    //     0xbef43c: sub             SP, SP, #0x38
    // 0xbef440: SetupParameters(EncyclopediaListController this /* r1 => r1, fp-0x8 */)
    //     0xbef440: stur            x1, [fp, #-8]
    // 0xbef444: CheckStackOverflow
    //     0xbef444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef448: cmp             SP, x16
    //     0xbef44c: b.ls            #0xbef574
    // 0xbef450: r1 = 1
    //     0xbef450: movz            x1, #0x1
    // 0xbef454: r0 = AllocateContext()
    //     0xbef454: bl              #0xec126c  ; AllocateContextStub
    // 0xbef458: mov             x3, x0
    // 0xbef45c: ldur            x2, [fp, #-8]
    // 0xbef460: stur            x3, [fp, #-0x18]
    // 0xbef464: StoreField: r3->field_f = r2
    //     0xbef464: stur            w2, [x3, #0xf]
    // 0xbef468: LoadField: r4 = r2->field_4b
    //     0xbef468: ldur            w4, [x2, #0x4b]
    // 0xbef46c: DecompressPointer r4
    //     0xbef46c: add             x4, x4, HEAP, lsl #32
    // 0xbef470: stur            x4, [fp, #-0x10]
    // 0xbef474: cmp             w4, NULL
    // 0xbef478: b.ne            #0xbef4c8
    // 0xbef47c: LoadField: r1 = r2->field_43
    //     0xbef47c: ldur            w1, [x2, #0x43]
    // 0xbef480: DecompressPointer r1
    //     0xbef480: add             x1, x1, HEAP, lsl #32
    // 0xbef484: r0 = LoadClassIdInstr(r1)
    //     0xbef484: ldur            x0, [x1, #-1]
    //     0xbef488: ubfx            x0, x0, #0xc, #0x14
    // 0xbef48c: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xbef48c: sub             lr, x0, #0xfe6
    //     0xbef490: ldr             lr, [x21, lr, lsl #3]
    //     0xbef494: blr             lr
    // 0xbef498: ldur            x2, [fp, #-0x18]
    // 0xbef49c: r1 = Function '<anonymous closure>':.
    //     0xbef49c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fef8] AnonymousClosure: (0xbef5cc), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onOfflineModeRequested (0xbef434)
    //     0xbef4a0: ldr             x1, [x1, #0xef8]
    // 0xbef4a4: stur            x0, [fp, #-0x18]
    // 0xbef4a8: r0 = AllocateClosure()
    //     0xbef4a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbef4ac: r16 = <List<String>?>
    //     0xbef4ac: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ff00] TypeArguments: <List<String>?>
    //     0xbef4b0: ldr             x16, [x16, #0xf00]
    // 0xbef4b4: ldur            lr, [fp, #-0x18]
    // 0xbef4b8: stp             lr, x16, [SP, #8]
    // 0xbef4bc: str             x0, [SP]
    // 0xbef4c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbef4c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbef4c4: r0 = then()
    //     0xbef4c4: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xbef4c8: ldur            x2, [fp, #-8]
    // 0xbef4cc: ldur            x0, [fp, #-0x10]
    // 0xbef4d0: LoadField: r3 = r2->field_43
    //     0xbef4d0: ldur            w3, [x2, #0x43]
    // 0xbef4d4: DecompressPointer r3
    //     0xbef4d4: add             x3, x3, HEAP, lsl #32
    // 0xbef4d8: stur            x3, [fp, #-0x18]
    // 0xbef4dc: cmp             w0, NULL
    // 0xbef4e0: b.ne            #0xbef4ec
    // 0xbef4e4: r0 = Null
    //     0xbef4e4: mov             x0, NULL
    // 0xbef4e8: b               #0xbef504
    // 0xbef4ec: LoadField: r4 = r0->field_7
    //     0xbef4ec: ldur            x4, [x0, #7]
    // 0xbef4f0: r0 = BoxInt64Instr(r4)
    //     0xbef4f0: sbfiz           x0, x4, #1, #0x1f
    //     0xbef4f4: cmp             x4, x0, asr #1
    //     0xbef4f8: b.eq            #0xbef504
    //     0xbef4fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef500: stur            x4, [x0, #7]
    // 0xbef504: stur            x0, [fp, #-0x10]
    // 0xbef508: LoadField: r1 = r2->field_57
    //     0xbef508: ldur            w1, [x2, #0x57]
    // 0xbef50c: DecompressPointer r1
    //     0xbef50c: add             x1, x1, HEAP, lsl #32
    // 0xbef510: r0 = value()
    //     0xbef510: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbef514: mov             x2, x0
    // 0xbef518: ldur            x0, [fp, #-8]
    // 0xbef51c: stur            x2, [fp, #-0x20]
    // 0xbef520: LoadField: r1 = r0->field_3f
    //     0xbef520: ldur            w1, [x0, #0x3f]
    // 0xbef524: DecompressPointer r1
    //     0xbef524: add             x1, x1, HEAP, lsl #32
    // 0xbef528: r0 = value()
    //     0xbef528: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbef52c: ldur            x1, [fp, #-8]
    // 0xbef530: stur            x0, [fp, #-8]
    // 0xbef534: r0 = offset()
    //     0xbef534: bl              #0xbef57c  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::offset
    // 0xbef538: ldur            x1, [fp, #-0x18]
    // 0xbef53c: r2 = LoadClassIdInstr(r1)
    //     0xbef53c: ldur            x2, [x1, #-1]
    //     0xbef540: ubfx            x2, x2, #0xc, #0x14
    // 0xbef544: mov             x6, x0
    // 0xbef548: mov             x0, x2
    // 0xbef54c: ldur            x2, [fp, #-0x10]
    // 0xbef550: ldur            x3, [fp, #-0x20]
    // 0xbef554: ldur            x7, [fp, #-8]
    // 0xbef558: r5 = 20
    //     0xbef558: movz            x5, #0x14
    // 0xbef55c: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xbef55c: sub             lr, x0, #0xfdf
    //     0xbef560: ldr             lr, [x21, lr, lsl #3]
    //     0xbef564: blr             lr
    // 0xbef568: LeaveFrame
    //     0xbef568: mov             SP, fp
    //     0xbef56c: ldp             fp, lr, [SP], #0x10
    // 0xbef570: ret
    //     0xbef570: ret             
    // 0xbef574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef578: b               #0xbef450
  }
  [closure] List<String>? <anonymous closure>(dynamic, ApiResult<List<String>>) {
    // ** addr: 0xbef5cc, size: 0x90
    // 0xbef5cc: EnterFrame
    //     0xbef5cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbef5d0: mov             fp, SP
    // 0xbef5d4: AllocStack(0x28)
    //     0xbef5d4: sub             SP, SP, #0x28
    // 0xbef5d8: SetupParameters()
    //     0xbef5d8: ldr             x0, [fp, #0x18]
    //     0xbef5dc: ldur            w2, [x0, #0x17]
    //     0xbef5e0: add             x2, x2, HEAP, lsl #32
    // 0xbef5e4: CheckStackOverflow
    //     0xbef5e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef5e8: cmp             SP, x16
    //     0xbef5ec: b.ls            #0xbef654
    // 0xbef5f0: r1 = Function '<anonymous closure>':.
    //     0xbef5f0: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff08] AnonymousClosure: (0xbef65c), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onOfflineModeRequested (0xbef434)
    //     0xbef5f4: ldr             x1, [x1, #0xf08]
    // 0xbef5f8: r0 = AllocateClosure()
    //     0xbef5f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbef5fc: r1 = Function '<anonymous closure>':.
    //     0xbef5fc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff10] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xbef600: ldr             x1, [x1, #0xf10]
    // 0xbef604: r2 = Null
    //     0xbef604: mov             x2, NULL
    // 0xbef608: stur            x0, [fp, #-8]
    // 0xbef60c: r0 = AllocateClosure()
    //     0xbef60c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbef610: mov             x1, x0
    // 0xbef614: ldr             x0, [fp, #0x10]
    // 0xbef618: r2 = LoadClassIdInstr(r0)
    //     0xbef618: ldur            x2, [x0, #-1]
    //     0xbef61c: ubfx            x2, x2, #0xc, #0x14
    // 0xbef620: r16 = <List<String>?>
    //     0xbef620: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3ff00] TypeArguments: <List<String>?>
    //     0xbef624: ldr             x16, [x16, #0xf00]
    // 0xbef628: stp             x0, x16, [SP, #0x10]
    // 0xbef62c: ldur            x16, [fp, #-8]
    // 0xbef630: stp             x16, x1, [SP]
    // 0xbef634: mov             x0, x2
    // 0xbef638: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xbef638: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xbef63c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbef63c: sub             lr, x0, #1, lsl #12
    //     0xbef640: ldr             lr, [x21, lr, lsl #3]
    //     0xbef644: blr             lr
    // 0xbef648: LeaveFrame
    //     0xbef648: mov             SP, fp
    //     0xbef64c: ldp             fp, lr, [SP], #0x10
    // 0xbef650: ret
    //     0xbef650: ret             
    // 0xbef654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef654: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef658: b               #0xbef5f0
  }
  [closure] List<String> <anonymous closure>(dynamic, List<String>, Pagination?) {
    // ** addr: 0xbef65c, size: 0x88
    // 0xbef65c: EnterFrame
    //     0xbef65c: stp             fp, lr, [SP, #-0x10]!
    //     0xbef660: mov             fp, SP
    // 0xbef664: AllocStack(0x8)
    //     0xbef664: sub             SP, SP, #8
    // 0xbef668: SetupParameters()
    //     0xbef668: ldr             x0, [fp, #0x20]
    //     0xbef66c: ldur            w1, [x0, #0x17]
    //     0xbef670: add             x1, x1, HEAP, lsl #32
    // 0xbef674: CheckStackOverflow
    //     0xbef674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbef678: cmp             SP, x16
    //     0xbef67c: b.ls            #0xbef6dc
    // 0xbef680: LoadField: r0 = r1->field_f
    //     0xbef680: ldur            w0, [x1, #0xf]
    // 0xbef684: DecompressPointer r0
    //     0xbef684: add             x0, x0, HEAP, lsl #32
    // 0xbef688: LoadField: r4 = r0->field_5b
    //     0xbef688: ldur            w4, [x0, #0x5b]
    // 0xbef68c: DecompressPointer r4
    //     0xbef68c: add             x4, x4, HEAP, lsl #32
    // 0xbef690: ldr             x5, [fp, #0x18]
    // 0xbef694: stur            x4, [fp, #-8]
    // 0xbef698: r0 = LoadClassIdInstr(r5)
    //     0xbef698: ldur            x0, [x5, #-1]
    //     0xbef69c: ubfx            x0, x0, #0xc, #0x14
    // 0xbef6a0: mov             x1, x5
    // 0xbef6a4: r2 = 0
    //     0xbef6a4: movz            x2, #0
    // 0xbef6a8: r3 = "#"
    //     0xbef6a8: ldr             x3, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0xbef6ac: r0 = GDT[cid_x0 + 0x13434]()
    //     0xbef6ac: movz            x17, #0x3434
    //     0xbef6b0: movk            x17, #0x1, lsl #16
    //     0xbef6b4: add             lr, x0, x17
    //     0xbef6b8: ldr             lr, [x21, lr, lsl #3]
    //     0xbef6bc: blr             lr
    // 0xbef6c0: ldur            x1, [fp, #-8]
    // 0xbef6c4: ldr             x2, [fp, #0x18]
    // 0xbef6c8: r0 = value=()
    //     0xbef6c8: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0xbef6cc: ldr             x0, [fp, #0x18]
    // 0xbef6d0: LeaveFrame
    //     0xbef6d0: mov             SP, fp
    //     0xbef6d4: ldp             fp, lr, [SP], #0x10
    // 0xbef6d8: ret
    //     0xbef6d8: ret             
    // 0xbef6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbef6dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbef6e0: b               #0xbef680
  }
  _ onOfflineModeLoaded(/* No info */) async {
    // ** addr: 0xbf5f10, size: 0xa8
    // 0xbf5f10: EnterFrame
    //     0xbf5f10: stp             fp, lr, [SP, #-0x10]!
    //     0xbf5f14: mov             fp, SP
    // 0xbf5f18: AllocStack(0x20)
    //     0xbf5f18: sub             SP, SP, #0x20
    // 0xbf5f1c: SetupParameters(EncyclopediaListController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xbf5f1c: stur            NULL, [fp, #-8]
    //     0xbf5f20: stur            x1, [fp, #-0x10]
    //     0xbf5f24: mov             x16, x2
    //     0xbf5f28: mov             x2, x1
    //     0xbf5f2c: mov             x1, x16
    //     0xbf5f30: stur            x1, [fp, #-0x18]
    // 0xbf5f34: CheckStackOverflow
    //     0xbf5f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf5f38: cmp             SP, x16
    //     0xbf5f3c: b.ls            #0xbf5fb0
    // 0xbf5f40: InitAsync() -> Future<void?>
    //     0xbf5f40: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbf5f44: bl              #0x661298  ; InitAsyncStub
    // 0xbf5f48: ldur            x2, [fp, #-0x18]
    // 0xbf5f4c: r0 = LoadClassIdInstr(r2)
    //     0xbf5f4c: ldur            x0, [x2, #-1]
    //     0xbf5f50: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5f54: mov             x1, x2
    // 0xbf5f58: r0 = GDT[cid_x0 + 0xd488]()
    //     0xbf5f58: movz            x17, #0xd488
    //     0xbf5f5c: add             lr, x0, x17
    //     0xbf5f60: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5f64: blr             lr
    // 0xbf5f68: tbnz            w0, #4, #0xbf5fa8
    // 0xbf5f6c: ldur            x2, [fp, #-0x18]
    // 0xbf5f70: r0 = LoadClassIdInstr(r2)
    //     0xbf5f70: ldur            x0, [x2, #-1]
    //     0xbf5f74: ubfx            x0, x0, #0xc, #0x14
    // 0xbf5f78: str             x2, [SP]
    // 0xbf5f7c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xbf5f7c: movz            x17, #0xc834
    //     0xbf5f80: add             lr, x0, x17
    //     0xbf5f84: ldr             lr, [x21, lr, lsl #3]
    //     0xbf5f88: blr             lr
    // 0xbf5f8c: cmp             w0, #0x28
    // 0xbf5f90: r16 = true
    //     0xbf5f90: add             x16, NULL, #0x20  ; true
    // 0xbf5f94: r17 = false
    //     0xbf5f94: add             x17, NULL, #0x30  ; false
    // 0xbf5f98: csel            x3, x16, x17, eq
    // 0xbf5f9c: ldur            x1, [fp, #-0x10]
    // 0xbf5fa0: ldur            x2, [fp, #-0x18]
    // 0xbf5fa4: r0 = appendToPage()
    //     0xbf5fa4: bl              #0xbf5fb8  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::appendToPage
    // 0xbf5fa8: r0 = Null
    //     0xbf5fa8: mov             x0, NULL
    // 0xbf5fac: r0 = ReturnAsyncNotFuture()
    //     0xbf5fac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbf5fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf5fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf5fb4: b               #0xbf5f40
  }
  _ onOfflineModeFailure(/* No info */) async {
    // ** addr: 0xc2a1a0, size: 0x48
    // 0xc2a1a0: EnterFrame
    //     0xc2a1a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a1a4: mov             fp, SP
    // 0xc2a1a8: AllocStack(0x18)
    //     0xc2a1a8: sub             SP, SP, #0x18
    // 0xc2a1ac: SetupParameters(EncyclopediaListController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc2a1ac: stur            NULL, [fp, #-8]
    //     0xc2a1b0: stur            x1, [fp, #-0x10]
    //     0xc2a1b4: stur            x2, [fp, #-0x18]
    // 0xc2a1b8: CheckStackOverflow
    //     0xc2a1b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a1bc: cmp             SP, x16
    //     0xc2a1c0: b.ls            #0xc2a1e0
    // 0xc2a1c4: InitAsync() -> Future<void?>
    //     0xc2a1c4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc2a1c8: bl              #0x661298  ; InitAsyncStub
    // 0xc2a1cc: ldur            x1, [fp, #-0x10]
    // 0xc2a1d0: r2 = true
    //     0xc2a1d0: add             x2, NULL, #0x20  ; true
    // 0xc2a1d4: r0 = hasError=()
    //     0xc2a1d4: bl              #0x7da75c  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::hasError=
    // 0xc2a1d8: r0 = Null
    //     0xc2a1d8: mov             x0, NULL
    // 0xc2a1dc: r0 = ReturnAsyncNotFuture()
    //     0xc2a1dc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc2a1e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a1e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a1e4: b               #0xc2a1c4
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe34c20, size: 0x48
    // 0xe34c20: EnterFrame
    //     0xe34c20: stp             fp, lr, [SP, #-0x10]!
    //     0xe34c24: mov             fp, SP
    // 0xe34c28: AllocStack(0x8)
    //     0xe34c28: sub             SP, SP, #8
    // 0xe34c2c: SetupParameters(EncyclopediaListController this /* r1 => r0, fp-0x8 */)
    //     0xe34c2c: mov             x0, x1
    //     0xe34c30: stur            x1, [fp, #-8]
    // 0xe34c34: CheckStackOverflow
    //     0xe34c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34c38: cmp             SP, x16
    //     0xe34c3c: b.ls            #0xe34c60
    // 0xe34c40: mov             x1, x0
    // 0xe34c44: r0 = onPageRequest()
    //     0xe34c44: bl              #0xe346cc  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::onPageRequest
    // 0xe34c48: ldur            x1, [fp, #-8]
    // 0xe34c4c: r0 = executeOfflineMode()
    //     0xe34c4c: bl              #0x8ef81c  ; [package:nuonline/common/mixins/offline_first_mixin.dart] _OfflineFirstController&GetxController&OfflineMixin::executeOfflineMode
    // 0xe34c50: r0 = Null
    //     0xe34c50: mov             x0, NULL
    // 0xe34c54: LeaveFrame
    //     0xe34c54: mov             SP, fp
    //     0xe34c58: ldp             fp, lr, [SP], #0x10
    // 0xe34c5c: ret
    //     0xe34c5c: ret             
    // 0xe34c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34c60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34c64: b               #0xe34c40
  }
  _ onOnlineModeRequested(/* No info */) async {
    // ** addr: 0xe34f1c, size: 0x60
    // 0xe34f1c: EnterFrame
    //     0xe34f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34f20: mov             fp, SP
    // 0xe34f24: AllocStack(0x18)
    //     0xe34f24: sub             SP, SP, #0x18
    // 0xe34f28: SetupParameters(EncyclopediaListController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe34f28: stur            NULL, [fp, #-8]
    //     0xe34f2c: stur            x1, [fp, #-0x10]
    //     0xe34f30: stur            x2, [fp, #-0x18]
    // 0xe34f34: CheckStackOverflow
    //     0xe34f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34f38: cmp             SP, x16
    //     0xe34f3c: b.ls            #0xe34f74
    // 0xe34f40: InitAsync() -> Future<ApiResult<List<Encyclopedia>>>
    //     0xe34f40: add             x0, PP, #0x47, lsl #12  ; [pp+0x47aa8] TypeArguments: <ApiResult<List<Encyclopedia>>>
    //     0xe34f44: ldr             x0, [x0, #0xaa8]
    //     0xe34f48: bl              #0x661298  ; InitAsyncStub
    // 0xe34f4c: ldur            x0, [fp, #-0x10]
    // 0xe34f50: LoadField: r1 = r0->field_47
    //     0xe34f50: ldur            w1, [x0, #0x47]
    // 0xe34f54: DecompressPointer r1
    //     0xe34f54: add             x1, x1, HEAP, lsl #32
    // 0xe34f58: r0 = LoadClassIdInstr(r1)
    //     0xe34f58: ldur            x0, [x1, #-1]
    //     0xe34f5c: ubfx            x0, x0, #0xc, #0x14
    // 0xe34f60: ldur            x2, [fp, #-0x18]
    // 0xe34f64: r0 = GDT[cid_x0 + -0xfe7]()
    //     0xe34f64: sub             lr, x0, #0xfe7
    //     0xe34f68: ldr             lr, [x21, lr, lsl #3]
    //     0xe34f6c: blr             lr
    // 0xe34f70: r0 = ReturnAsync()
    //     0xe34f70: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe34f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34f78: b               #0xe34f40
  }
  _ onOnlineModeLoaded(/* No info */) async {
    // ** addr: 0xe34f7c, size: 0x94
    // 0xe34f7c: EnterFrame
    //     0xe34f7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34f80: mov             fp, SP
    // 0xe34f84: AllocStack(0x18)
    //     0xe34f84: sub             SP, SP, #0x18
    // 0xe34f88: SetupParameters(EncyclopediaListController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xe34f88: stur            NULL, [fp, #-8]
    //     0xe34f8c: stur            x1, [fp, #-0x10]
    //     0xe34f90: mov             x16, x2
    //     0xe34f94: mov             x2, x1
    //     0xe34f98: mov             x1, x16
    //     0xe34f9c: stur            x1, [fp, #-0x18]
    // 0xe34fa0: CheckStackOverflow
    //     0xe34fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34fa4: cmp             SP, x16
    //     0xe34fa8: b.ls            #0xe35008
    // 0xe34fac: InitAsync() -> Future<void?>
    //     0xe34fac: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe34fb0: bl              #0x661298  ; InitAsyncStub
    // 0xe34fb4: ldur            x2, [fp, #-0x18]
    // 0xe34fb8: r0 = LoadClassIdInstr(r2)
    //     0xe34fb8: ldur            x0, [x2, #-1]
    //     0xe34fbc: ubfx            x0, x0, #0xc, #0x14
    // 0xe34fc0: mov             x1, x2
    // 0xe34fc4: r0 = GDT[cid_x0 + 0xd488]()
    //     0xe34fc4: movz            x17, #0xd488
    //     0xe34fc8: add             lr, x0, x17
    //     0xe34fcc: ldr             lr, [x21, lr, lsl #3]
    //     0xe34fd0: blr             lr
    // 0xe34fd4: tbnz            w0, #4, #0xe35000
    // 0xe34fd8: ldur            x0, [fp, #-0x10]
    // 0xe34fdc: LoadField: r1 = r0->field_43
    //     0xe34fdc: ldur            w1, [x0, #0x43]
    // 0xe34fe0: DecompressPointer r1
    //     0xe34fe0: add             x1, x1, HEAP, lsl #32
    // 0xe34fe4: r0 = LoadClassIdInstr(r1)
    //     0xe34fe4: ldur            x0, [x1, #-1]
    //     0xe34fe8: ubfx            x0, x0, #0xc, #0x14
    // 0xe34fec: ldur            x2, [fp, #-0x18]
    // 0xe34ff0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe34ff0: sub             lr, x0, #1, lsl #12
    //     0xe34ff4: ldr             lr, [x21, lr, lsl #3]
    //     0xe34ff8: blr             lr
    // 0xe34ffc: r0 = ReturnAsync()
    //     0xe34ffc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe35000: r0 = Null
    //     0xe35000: mov             x0, NULL
    // 0xe35004: r0 = ReturnAsyncNotFuture()
    //     0xe35004: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe35008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe35008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3500c: b               #0xe34fac
  }
  get _ lastUpdatedAt(/* No info */) async {
    // ** addr: 0xe37d8c, size: 0x80
    // 0xe37d8c: EnterFrame
    //     0xe37d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xe37d90: mov             fp, SP
    // 0xe37d94: AllocStack(0x28)
    //     0xe37d94: sub             SP, SP, #0x28
    // 0xe37d98: SetupParameters(EncyclopediaListController this /* r1 => r1, fp-0x10 */)
    //     0xe37d98: stur            NULL, [fp, #-8]
    //     0xe37d9c: stur            x1, [fp, #-0x10]
    // 0xe37da0: CheckStackOverflow
    //     0xe37da0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37da4: cmp             SP, x16
    //     0xe37da8: b.ls            #0xe37e04
    // 0xe37dac: InitAsync() -> Future<String?>
    //     0xe37dac: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0xe37db0: bl              #0x661298  ; InitAsyncStub
    // 0xe37db4: ldur            x0, [fp, #-0x10]
    // 0xe37db8: LoadField: r1 = r0->field_43
    //     0xe37db8: ldur            w1, [x0, #0x43]
    // 0xe37dbc: DecompressPointer r1
    //     0xe37dbc: add             x1, x1, HEAP, lsl #32
    // 0xe37dc0: r0 = LoadClassIdInstr(r1)
    //     0xe37dc0: ldur            x0, [x1, #-1]
    //     0xe37dc4: ubfx            x0, x0, #0xc, #0x14
    // 0xe37dc8: r0 = GDT[cid_x0 + -0xfee]()
    //     0xe37dc8: sub             lr, x0, #0xfee
    //     0xe37dcc: ldr             lr, [x21, lr, lsl #3]
    //     0xe37dd0: blr             lr
    // 0xe37dd4: r1 = Function '<anonymous closure>':.
    //     0xe37dd4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3ff50] AnonymousClosure: (0xe37e0c), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::lastUpdatedAt (0xe37d8c)
    //     0xe37dd8: ldr             x1, [x1, #0xf50]
    // 0xe37ddc: r2 = Null
    //     0xe37ddc: mov             x2, NULL
    // 0xe37de0: stur            x0, [fp, #-0x10]
    // 0xe37de4: r0 = AllocateClosure()
    //     0xe37de4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe37de8: r16 = <String?>
    //     0xe37de8: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xe37dec: ldur            lr, [fp, #-0x10]
    // 0xe37df0: stp             lr, x16, [SP, #8]
    // 0xe37df4: str             x0, [SP]
    // 0xe37df8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe37df8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe37dfc: r0 = then()
    //     0xe37dfc: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe37e00: r0 = ReturnAsync()
    //     0xe37e00: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe37e04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37e04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37e08: b               #0xe37dac
  }
  [closure] String? <anonymous closure>(dynamic, Encyclopedia?) {
    // ** addr: 0xe37e0c, size: 0x24
    // 0xe37e0c: ldr             x1, [SP]
    // 0xe37e10: cmp             w1, NULL
    // 0xe37e14: b.ne            #0xe37e20
    // 0xe37e18: r0 = Null
    //     0xe37e18: mov             x0, NULL
    // 0xe37e1c: b               #0xe37e2c
    // 0xe37e20: LoadField: r2 = r1->field_1f
    //     0xe37e20: ldur            w2, [x1, #0x1f]
    // 0xe37e24: DecompressPointer r2
    //     0xe37e24: add             x2, x2, HEAP, lsl #32
    // 0xe37e28: mov             x0, x2
    // 0xe37e2c: ret
    //     0xe37e2c: ret             
  }
}
