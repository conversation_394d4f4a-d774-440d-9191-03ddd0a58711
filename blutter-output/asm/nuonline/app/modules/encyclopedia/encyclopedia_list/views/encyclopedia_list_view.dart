// lib: , url: package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart

// class id: 1050266, size: 0x8
class :: {
}

// class id: 5012, size: 0xc, field offset: 0xc
//   const constructor, 
class _ItemSkeleton extends StatelessWidget {
}

// class id: 5013, size: 0xc, field offset: 0xc
//   const constructor, 
class _FilterSkeleton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb982c0, size: 0x8c
    // 0xb982c0: EnterFrame
    //     0xb982c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb982c4: mov             fp, SP
    // 0xb982c8: AllocStack(0x28)
    //     0xb982c8: sub             SP, SP, #0x28
    // 0xb982cc: CheckStackOverflow
    //     0xb982cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb982d0: cmp             SP, x16
    //     0xb982d4: b.ls            #0xb98344
    // 0xb982d8: r1 = Function '<anonymous closure>':.
    //     0xb982d8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a08] AnonymousClosure: (0xaf3054), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xb982dc: ldr             x1, [x1, #0xa08]
    // 0xb982e0: r2 = Null
    //     0xb982e0: mov             x2, NULL
    // 0xb982e4: r0 = AllocateClosure()
    //     0xb982e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb982e8: r1 = Function '<anonymous closure>':.
    //     0xb982e8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a10] AnonymousClosure: (0xb9834c), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] _FilterSkeleton::build (0xb982c0)
    //     0xb982ec: ldr             x1, [x1, #0xa10]
    // 0xb982f0: r2 = Null
    //     0xb982f0: mov             x2, NULL
    // 0xb982f4: stur            x0, [fp, #-8]
    // 0xb982f8: r0 = AllocateClosure()
    //     0xb982f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb982fc: stur            x0, [fp, #-0x10]
    // 0xb98300: r0 = ListView()
    //     0xb98300: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb98304: stur            x0, [fp, #-0x18]
    // 0xb98308: r16 = Instance_Axis
    //     0xb98308: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb9830c: r30 = Instance_EdgeInsets
    //     0xb9830c: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb98310: ldr             lr, [lr, #0x360]
    // 0xb98314: stp             lr, x16, [SP]
    // 0xb98318: mov             x1, x0
    // 0xb9831c: ldur            x2, [fp, #-0x10]
    // 0xb98320: ldur            x5, [fp, #-8]
    // 0xb98324: r3 = 10
    //     0xb98324: movz            x3, #0xa
    // 0xb98328: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, scrollDirection, 0x4, null]
    //     0xb98328: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "scrollDirection", 0x4, Null]
    //     0xb9832c: ldr             x4, [x4, #0x220]
    // 0xb98330: r0 = ListView.separated()
    //     0xb98330: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb98334: ldur            x0, [fp, #-0x18]
    // 0xb98338: LeaveFrame
    //     0xb98338: mov             SP, fp
    //     0xb9833c: ldp             fp, lr, [SP], #0x10
    // 0xb98340: ret
    //     0xb98340: ret             
    // 0xb98344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb98344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb98348: b               #0xb982d8
  }
  [closure] NSkeleton <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb9834c, size: 0xc
    // 0xb9834c: r0 = Instance_NSkeleton
    //     0xb9834c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a18] Obj!NSkeleton@e20a31
    //     0xb98350: ldr             x0, [x0, #0xa18]
    // 0xb98354: ret
    //     0xb98354: ret             
  }
}

// class id: 5014, size: 0x18, field offset: 0xc
//   const constructor, 
class NCircleButton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9802c, size: 0x294
    // 0xb9802c: EnterFrame
    //     0xb9802c: stp             fp, lr, [SP, #-0x10]!
    //     0xb98030: mov             fp, SP
    // 0xb98034: AllocStack(0x50)
    //     0xb98034: sub             SP, SP, #0x50
    // 0xb98038: SetupParameters(NCircleButton this /* r1 => r0, fp-0x20 */)
    //     0xb98038: mov             x0, x1
    //     0xb9803c: stur            x1, [fp, #-0x20]
    // 0xb98040: CheckStackOverflow
    //     0xb98040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb98044: cmp             SP, x16
    //     0xb98048: b.ls            #0xb982b0
    // 0xb9804c: LoadField: r3 = r0->field_f
    //     0xb9804c: ldur            w3, [x0, #0xf]
    // 0xb98050: DecompressPointer r3
    //     0xb98050: add             x3, x3, HEAP, lsl #32
    // 0xb98054: stur            x3, [fp, #-0x18]
    // 0xb98058: LoadField: r4 = r0->field_13
    //     0xb98058: ldur            w4, [x0, #0x13]
    // 0xb9805c: DecompressPointer r4
    //     0xb9805c: add             x4, x4, HEAP, lsl #32
    // 0xb98060: stur            x4, [fp, #-0x10]
    // 0xb98064: tbnz            w4, #4, #0xb98074
    // 0xb98068: r5 = Instance_MaterialColor
    //     0xb98068: add             x5, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb9806c: ldr             x5, [x5, #0xcf0]
    // 0xb98070: b               #0xb98078
    // 0xb98074: r5 = Instance_Color
    //     0xb98074: ldr             x5, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb98078: stur            x5, [fp, #-8]
    // 0xb9807c: r1 = _ConstMap len:6
    //     0xb9807c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb98080: ldr             x1, [x1, #0xc20]
    // 0xb98084: r2 = 2
    //     0xb98084: movz            x2, #0x2
    // 0xb98088: r0 = []()
    //     0xb98088: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb9808c: stur            x0, [fp, #-0x28]
    // 0xb98090: cmp             w0, NULL
    // 0xb98094: b.eq            #0xb982b8
    // 0xb98098: r1 = _ConstMap len:3
    //     0xb98098: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb9809c: ldr             x1, [x1, #0xbe8]
    // 0xb980a0: r2 = 2
    //     0xb980a0: movz            x2, #0x2
    // 0xb980a4: r0 = []()
    //     0xb980a4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb980a8: cmp             w0, NULL
    // 0xb980ac: b.eq            #0xb982bc
    // 0xb980b0: r16 = <Color>
    //     0xb980b0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb980b4: ldr             x16, [x16, #0x158]
    // 0xb980b8: stp             x0, x16, [SP, #8]
    // 0xb980bc: ldur            x16, [fp, #-0x28]
    // 0xb980c0: str             x16, [SP]
    // 0xb980c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb980c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb980c8: r0 = mode()
    //     0xb980c8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb980cc: mov             x1, x0
    // 0xb980d0: ldur            x0, [fp, #-0x10]
    // 0xb980d4: tbnz            w0, #4, #0xb980dc
    // 0xb980d8: r1 = Instance_Color
    //     0xb980d8: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb980dc: ldur            x2, [fp, #-0x20]
    // 0xb980e0: ldur            x3, [fp, #-8]
    // 0xb980e4: str             x1, [SP]
    // 0xb980e8: r1 = Null
    //     0xb980e8: mov             x1, NULL
    // 0xb980ec: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb980ec: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb980f0: ldr             x4, [x4, #0x228]
    // 0xb980f4: r0 = Border.all()
    //     0xb980f4: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb980f8: stur            x0, [fp, #-0x28]
    // 0xb980fc: r0 = BoxDecoration()
    //     0xb980fc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb98100: mov             x1, x0
    // 0xb98104: ldur            x0, [fp, #-8]
    // 0xb98108: stur            x1, [fp, #-0x30]
    // 0xb9810c: StoreField: r1->field_7 = r0
    //     0xb9810c: stur            w0, [x1, #7]
    // 0xb98110: ldur            x0, [fp, #-0x28]
    // 0xb98114: StoreField: r1->field_f = r0
    //     0xb98114: stur            w0, [x1, #0xf]
    // 0xb98118: r0 = Instance_BoxShape
    //     0xb98118: add             x0, PP, #0x34, lsl #12  ; [pp+0x349f0] Obj!BoxShape@e35e21
    //     0xb9811c: ldr             x0, [x0, #0x9f0]
    // 0xb98120: StoreField: r1->field_23 = r0
    //     0xb98120: stur            w0, [x1, #0x23]
    // 0xb98124: ldur            x0, [fp, #-0x20]
    // 0xb98128: LoadField: r2 = r0->field_b
    //     0xb98128: ldur            w2, [x0, #0xb]
    // 0xb9812c: DecompressPointer r2
    //     0xb9812c: add             x2, x2, HEAP, lsl #32
    // 0xb98130: stur            x2, [fp, #-8]
    // 0xb98134: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb98134: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb98138: ldr             x0, [x0, #0x2670]
    //     0xb9813c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb98140: cmp             w0, w16
    //     0xb98144: b.ne            #0xb98150
    //     0xb98148: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb9814c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb98150: r0 = GetNavigation.textTheme()
    //     0xb98150: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb98154: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb98154: ldur            w3, [x0, #0x17]
    // 0xb98158: DecompressPointer r3
    //     0xb98158: add             x3, x3, HEAP, lsl #32
    // 0xb9815c: stur            x3, [fp, #-0x20]
    // 0xb98160: cmp             w3, NULL
    // 0xb98164: b.ne            #0xb98170
    // 0xb98168: r2 = Null
    //     0xb98168: mov             x2, NULL
    // 0xb9816c: b               #0xb981e0
    // 0xb98170: ldur            x0, [fp, #-0x10]
    // 0xb98174: r1 = _ConstMap len:3
    //     0xb98174: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb98178: ldr             x1, [x1, #0xbe8]
    // 0xb9817c: r2 = 4
    //     0xb9817c: movz            x2, #0x4
    // 0xb98180: r0 = []()
    //     0xb98180: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb98184: r16 = <Color?>
    //     0xb98184: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb98188: ldr             x16, [x16, #0x98]
    // 0xb9818c: r30 = Instance_Color
    //     0xb9818c: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb98190: stp             lr, x16, [SP, #8]
    // 0xb98194: str             x0, [SP]
    // 0xb98198: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb98198: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb9819c: r0 = mode()
    //     0xb9819c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb981a0: mov             x1, x0
    // 0xb981a4: ldur            x0, [fp, #-0x10]
    // 0xb981a8: tbnz            w0, #4, #0xb981b4
    // 0xb981ac: r0 = Instance_Color
    //     0xb981ac: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb981b0: b               #0xb981b8
    // 0xb981b4: mov             x0, x1
    // 0xb981b8: r16 = 1.000000
    //     0xb981b8: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xb981bc: r30 = 18.000000
    //     0xb981bc: add             lr, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb981c0: ldr             lr, [lr, #0x958]
    // 0xb981c4: stp             lr, x16, [SP, #8]
    // 0xb981c8: str             x0, [SP]
    // 0xb981cc: ldur            x1, [fp, #-0x20]
    // 0xb981d0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x2, height, 0x1, null]
    //     0xb981d0: add             x4, PP, #0x34, lsl #12  ; [pp+0x349f8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x2, "height", 0x1, Null]
    //     0xb981d4: ldr             x4, [x4, #0x9f8]
    // 0xb981d8: r0 = copyWith()
    //     0xb981d8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb981dc: mov             x2, x0
    // 0xb981e0: ldur            x1, [fp, #-0x18]
    // 0xb981e4: ldur            x0, [fp, #-8]
    // 0xb981e8: stur            x2, [fp, #-0x10]
    // 0xb981ec: r0 = Text()
    //     0xb981ec: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb981f0: mov             x1, x0
    // 0xb981f4: ldur            x0, [fp, #-8]
    // 0xb981f8: stur            x1, [fp, #-0x20]
    // 0xb981fc: StoreField: r1->field_b = r0
    //     0xb981fc: stur            w0, [x1, #0xb]
    // 0xb98200: ldur            x0, [fp, #-0x10]
    // 0xb98204: StoreField: r1->field_13 = r0
    //     0xb98204: stur            w0, [x1, #0x13]
    // 0xb98208: r0 = Instance__LinearTextScaler
    //     0xb98208: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb9820c: StoreField: r1->field_33 = r0
    //     0xb9820c: stur            w0, [x1, #0x33]
    // 0xb98210: r0 = Center()
    //     0xb98210: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb98214: mov             x1, x0
    // 0xb98218: r0 = Instance_Alignment
    //     0xb98218: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb9821c: ldr             x0, [x0, #0x898]
    // 0xb98220: stur            x1, [fp, #-8]
    // 0xb98224: StoreField: r1->field_f = r0
    //     0xb98224: stur            w0, [x1, #0xf]
    // 0xb98228: ldur            x0, [fp, #-0x20]
    // 0xb9822c: StoreField: r1->field_b = r0
    //     0xb9822c: stur            w0, [x1, #0xb]
    // 0xb98230: r0 = Container()
    //     0xb98230: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb98234: stur            x0, [fp, #-0x10]
    // 0xb98238: r16 = 36.000000
    //     0xb98238: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb9823c: ldr             x16, [x16, #0x478]
    // 0xb98240: r30 = 36.000000
    //     0xb98240: add             lr, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb98244: ldr             lr, [lr, #0x478]
    // 0xb98248: stp             lr, x16, [SP, #0x10]
    // 0xb9824c: ldur            x16, [fp, #-0x30]
    // 0xb98250: ldur            lr, [fp, #-8]
    // 0xb98254: stp             lr, x16, [SP]
    // 0xb98258: mov             x1, x0
    // 0xb9825c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb9825c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34a00] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb98260: ldr             x4, [x4, #0xa00]
    // 0xb98264: r0 = Container()
    //     0xb98264: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb98268: r0 = InkWell()
    //     0xb98268: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb9826c: ldur            x1, [fp, #-0x10]
    // 0xb98270: StoreField: r0->field_b = r1
    //     0xb98270: stur            w1, [x0, #0xb]
    // 0xb98274: ldur            x1, [fp, #-0x18]
    // 0xb98278: StoreField: r0->field_f = r1
    //     0xb98278: stur            w1, [x0, #0xf]
    // 0xb9827c: r1 = true
    //     0xb9827c: add             x1, NULL, #0x20  ; true
    // 0xb98280: StoreField: r0->field_43 = r1
    //     0xb98280: stur            w1, [x0, #0x43]
    // 0xb98284: r2 = Instance_BoxShape
    //     0xb98284: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb98288: ldr             x2, [x2, #0xca8]
    // 0xb9828c: StoreField: r0->field_47 = r2
    //     0xb9828c: stur            w2, [x0, #0x47]
    // 0xb98290: StoreField: r0->field_6f = r1
    //     0xb98290: stur            w1, [x0, #0x6f]
    // 0xb98294: r2 = false
    //     0xb98294: add             x2, NULL, #0x30  ; false
    // 0xb98298: StoreField: r0->field_73 = r2
    //     0xb98298: stur            w2, [x0, #0x73]
    // 0xb9829c: StoreField: r0->field_83 = r1
    //     0xb9829c: stur            w1, [x0, #0x83]
    // 0xb982a0: StoreField: r0->field_7b = r2
    //     0xb982a0: stur            w2, [x0, #0x7b]
    // 0xb982a4: LeaveFrame
    //     0xb982a4: mov             SP, fp
    //     0xb982a8: ldp             fp, lr, [SP], #0x10
    // 0xb982ac: ret
    //     0xb982ac: ret             
    // 0xb982b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb982b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb982b4: b               #0xb9804c
    // 0xb982b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb982b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb982bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb982bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5279, size: 0x14, field offset: 0x14
class EncyclopediaListView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf1fdc, size: 0x5c4
    // 0xaf1fdc: EnterFrame
    //     0xaf1fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1fe0: mov             fp, SP
    // 0xaf1fe4: AllocStack(0x48)
    //     0xaf1fe4: sub             SP, SP, #0x48
    // 0xaf1fe8: SetupParameters(EncyclopediaListView this /* r1 => r1, fp-0x8 */)
    //     0xaf1fe8: stur            x1, [fp, #-8]
    // 0xaf1fec: CheckStackOverflow
    //     0xaf1fec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1ff0: cmp             SP, x16
    //     0xaf1ff4: b.ls            #0xaf2598
    // 0xaf1ff8: r1 = 1
    //     0xaf1ff8: movz            x1, #0x1
    // 0xaf1ffc: r0 = AllocateContext()
    //     0xaf1ffc: bl              #0xec126c  ; AllocateContextStub
    // 0xaf2000: mov             x2, x0
    // 0xaf2004: ldur            x0, [fp, #-8]
    // 0xaf2008: stur            x2, [fp, #-0x10]
    // 0xaf200c: StoreField: r2->field_f = r0
    //     0xaf200c: stur            w0, [x2, #0xf]
    // 0xaf2010: mov             x1, x0
    // 0xaf2014: r0 = controller()
    //     0xaf2014: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2018: LoadField: r1 = r0->field_4b
    //     0xaf2018: ldur            w1, [x0, #0x4b]
    // 0xaf201c: DecompressPointer r1
    //     0xaf201c: add             x1, x1, HEAP, lsl #32
    // 0xaf2020: cmp             w1, NULL
    // 0xaf2024: b.ne            #0xaf2030
    // 0xaf2028: r0 = Null
    //     0xaf2028: mov             x0, NULL
    // 0xaf202c: b               #0xaf2038
    // 0xaf2030: LoadField: r0 = r1->field_f
    //     0xaf2030: ldur            w0, [x1, #0xf]
    // 0xaf2034: DecompressPointer r0
    //     0xaf2034: add             x0, x0, HEAP, lsl #32
    // 0xaf2038: cmp             w0, NULL
    // 0xaf203c: b.ne            #0xaf2048
    // 0xaf2040: r0 = "Ensiklopedia"
    //     0xaf2040: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] "Ensiklopedia"
    //     0xaf2044: ldr             x0, [x0, #0x1b0]
    // 0xaf2048: stur            x0, [fp, #-0x18]
    // 0xaf204c: r0 = Text()
    //     0xaf204c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf2050: mov             x1, x0
    // 0xaf2054: ldur            x0, [fp, #-0x18]
    // 0xaf2058: stur            x1, [fp, #-0x20]
    // 0xaf205c: StoreField: r1->field_b = r0
    //     0xaf205c: stur            w0, [x1, #0xb]
    // 0xaf2060: r0 = AppBar()
    //     0xaf2060: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf2064: stur            x0, [fp, #-0x18]
    // 0xaf2068: ldur            x16, [fp, #-0x20]
    // 0xaf206c: str             x16, [SP]
    // 0xaf2070: mov             x1, x0
    // 0xaf2074: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaf2074: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaf2078: ldr             x4, [x4, #0x6e8]
    // 0xaf207c: r0 = AppBar()
    //     0xaf207c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf2080: ldur            x1, [fp, #-8]
    // 0xaf2084: r0 = controller()
    //     0xaf2084: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2088: stur            x0, [fp, #-0x20]
    // 0xaf208c: r0 = Obx()
    //     0xaf208c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf2090: ldur            x2, [fp, #-0x10]
    // 0xaf2094: r1 = Function '<anonymous closure>':.
    //     0xaf2094: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1b8] AnonymousClosure: (0xaf3060), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2098: ldr             x1, [x1, #0x1b8]
    // 0xaf209c: stur            x0, [fp, #-0x28]
    // 0xaf20a0: r0 = AllocateClosure()
    //     0xaf20a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf20a4: mov             x1, x0
    // 0xaf20a8: ldur            x0, [fp, #-0x28]
    // 0xaf20ac: StoreField: r0->field_b = r1
    //     0xaf20ac: stur            w1, [x0, #0xb]
    // 0xaf20b0: r0 = Padding()
    //     0xaf20b0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf20b4: mov             x3, x0
    // 0xaf20b8: r0 = Instance_EdgeInsets
    //     0xaf20b8: add             x0, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xaf20bc: ldr             x0, [x0, #0xde8]
    // 0xaf20c0: stur            x3, [fp, #-0x30]
    // 0xaf20c4: StoreField: r3->field_f = r0
    //     0xaf20c4: stur            w0, [x3, #0xf]
    // 0xaf20c8: ldur            x0, [fp, #-0x28]
    // 0xaf20cc: StoreField: r3->field_b = r0
    //     0xaf20cc: stur            w0, [x3, #0xb]
    // 0xaf20d0: r1 = Null
    //     0xaf20d0: mov             x1, NULL
    // 0xaf20d4: r2 = 2
    //     0xaf20d4: movz            x2, #0x2
    // 0xaf20d8: r0 = AllocateArray()
    //     0xaf20d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf20dc: mov             x2, x0
    // 0xaf20e0: ldur            x0, [fp, #-0x30]
    // 0xaf20e4: stur            x2, [fp, #-0x28]
    // 0xaf20e8: StoreField: r2->field_f = r0
    //     0xaf20e8: stur            w0, [x2, #0xf]
    // 0xaf20ec: r1 = <Widget>
    //     0xaf20ec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf20f0: r0 = AllocateGrowableArray()
    //     0xaf20f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf20f4: mov             x2, x0
    // 0xaf20f8: ldur            x0, [fp, #-0x28]
    // 0xaf20fc: stur            x2, [fp, #-0x30]
    // 0xaf2100: StoreField: r2->field_f = r0
    //     0xaf2100: stur            w0, [x2, #0xf]
    // 0xaf2104: r0 = 2
    //     0xaf2104: movz            x0, #0x2
    // 0xaf2108: StoreField: r2->field_b = r0
    //     0xaf2108: stur            w0, [x2, #0xb]
    // 0xaf210c: ldur            x1, [fp, #-8]
    // 0xaf2110: r0 = controller()
    //     0xaf2110: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2114: LoadField: r1 = r0->field_4b
    //     0xaf2114: ldur            w1, [x0, #0x4b]
    // 0xaf2118: DecompressPointer r1
    //     0xaf2118: add             x1, x1, HEAP, lsl #32
    // 0xaf211c: cmp             w1, NULL
    // 0xaf2120: b.ne            #0xaf21c4
    // 0xaf2124: r0 = Obx()
    //     0xaf2124: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf2128: ldur            x2, [fp, #-0x10]
    // 0xaf212c: r1 = Function '<anonymous closure>':.
    //     0xaf212c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1c0] AnonymousClosure: (0xaf2cf8), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2130: ldr             x1, [x1, #0x1c0]
    // 0xaf2134: stur            x0, [fp, #-0x28]
    // 0xaf2138: r0 = AllocateClosure()
    //     0xaf2138: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf213c: mov             x1, x0
    // 0xaf2140: ldur            x0, [fp, #-0x28]
    // 0xaf2144: StoreField: r0->field_b = r1
    //     0xaf2144: stur            w1, [x0, #0xb]
    // 0xaf2148: r0 = SizedBox()
    //     0xaf2148: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf214c: mov             x3, x0
    // 0xaf2150: r0 = 36.000000
    //     0xaf2150: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xaf2154: ldr             x0, [x0, #0x478]
    // 0xaf2158: stur            x3, [fp, #-0x38]
    // 0xaf215c: StoreField: r3->field_13 = r0
    //     0xaf215c: stur            w0, [x3, #0x13]
    // 0xaf2160: ldur            x0, [fp, #-0x28]
    // 0xaf2164: StoreField: r3->field_b = r0
    //     0xaf2164: stur            w0, [x3, #0xb]
    // 0xaf2168: r1 = Null
    //     0xaf2168: mov             x1, NULL
    // 0xaf216c: r2 = 6
    //     0xaf216c: movz            x2, #0x6
    // 0xaf2170: r0 = AllocateArray()
    //     0xaf2170: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf2174: mov             x2, x0
    // 0xaf2178: ldur            x0, [fp, #-0x38]
    // 0xaf217c: stur            x2, [fp, #-0x28]
    // 0xaf2180: StoreField: r2->field_f = r0
    //     0xaf2180: stur            w0, [x2, #0xf]
    // 0xaf2184: r16 = Instance_SizedBox
    //     0xaf2184: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xaf2188: ldr             x16, [x16, #0xfe8]
    // 0xaf218c: StoreField: r2->field_13 = r16
    //     0xaf218c: stur            w16, [x2, #0x13]
    // 0xaf2190: r16 = Instance_Divider
    //     0xaf2190: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xaf2194: ldr             x16, [x16, #0xc28]
    // 0xaf2198: ArrayStore: r2[0] = r16  ; List_4
    //     0xaf2198: stur            w16, [x2, #0x17]
    // 0xaf219c: r1 = <Widget>
    //     0xaf219c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf21a0: r0 = AllocateGrowableArray()
    //     0xaf21a0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf21a4: mov             x1, x0
    // 0xaf21a8: ldur            x0, [fp, #-0x28]
    // 0xaf21ac: StoreField: r1->field_f = r0
    //     0xaf21ac: stur            w0, [x1, #0xf]
    // 0xaf21b0: r0 = 6
    //     0xaf21b0: movz            x0, #0x6
    // 0xaf21b4: StoreField: r1->field_b = r0
    //     0xaf21b4: stur            w0, [x1, #0xb]
    // 0xaf21b8: mov             x2, x1
    // 0xaf21bc: ldur            x1, [fp, #-0x30]
    // 0xaf21c0: r0 = addAll()
    //     0xaf21c0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xaf21c4: ldur            x1, [fp, #-0x30]
    // 0xaf21c8: r0 = Obx()
    //     0xaf21c8: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf21cc: ldur            x2, [fp, #-0x10]
    // 0xaf21d0: r1 = Function '<anonymous closure>':.
    //     0xaf21d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] AnonymousClosure: (0xaf25e4), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf21d4: ldr             x1, [x1, #0x1c8]
    // 0xaf21d8: stur            x0, [fp, #-0x10]
    // 0xaf21dc: r0 = AllocateClosure()
    //     0xaf21dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf21e0: mov             x1, x0
    // 0xaf21e4: ldur            x0, [fp, #-0x10]
    // 0xaf21e8: StoreField: r0->field_b = r1
    //     0xaf21e8: stur            w1, [x0, #0xb]
    // 0xaf21ec: r0 = ListTileTheme()
    //     0xaf21ec: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xaf21f0: mov             x2, x0
    // 0xaf21f4: r0 = Instance_EdgeInsets
    //     0xaf21f4: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf21f8: stur            x2, [fp, #-0x28]
    // 0xaf21fc: StoreField: r2->field_2b = r0
    //     0xaf21fc: stur            w0, [x2, #0x2b]
    // 0xaf2200: ldur            x0, [fp, #-0x10]
    // 0xaf2204: StoreField: r2->field_b = r0
    //     0xaf2204: stur            w0, [x2, #0xb]
    // 0xaf2208: r1 = <FlexParentData>
    //     0xaf2208: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf220c: ldr             x1, [x1, #0x720]
    // 0xaf2210: r0 = Expanded()
    //     0xaf2210: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf2214: mov             x2, x0
    // 0xaf2218: r0 = 1
    //     0xaf2218: movz            x0, #0x1
    // 0xaf221c: stur            x2, [fp, #-0x10]
    // 0xaf2220: StoreField: r2->field_13 = r0
    //     0xaf2220: stur            x0, [x2, #0x13]
    // 0xaf2224: r3 = Instance_FlexFit
    //     0xaf2224: add             x3, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf2228: ldr             x3, [x3, #0x728]
    // 0xaf222c: StoreField: r2->field_1b = r3
    //     0xaf222c: stur            w3, [x2, #0x1b]
    // 0xaf2230: ldur            x1, [fp, #-0x28]
    // 0xaf2234: StoreField: r2->field_b = r1
    //     0xaf2234: stur            w1, [x2, #0xb]
    // 0xaf2238: ldur            x4, [fp, #-0x30]
    // 0xaf223c: LoadField: r1 = r4->field_b
    //     0xaf223c: ldur            w1, [x4, #0xb]
    // 0xaf2240: LoadField: r5 = r4->field_f
    //     0xaf2240: ldur            w5, [x4, #0xf]
    // 0xaf2244: DecompressPointer r5
    //     0xaf2244: add             x5, x5, HEAP, lsl #32
    // 0xaf2248: LoadField: r6 = r5->field_b
    //     0xaf2248: ldur            w6, [x5, #0xb]
    // 0xaf224c: r5 = LoadInt32Instr(r1)
    //     0xaf224c: sbfx            x5, x1, #1, #0x1f
    // 0xaf2250: stur            x5, [fp, #-0x40]
    // 0xaf2254: r1 = LoadInt32Instr(r6)
    //     0xaf2254: sbfx            x1, x6, #1, #0x1f
    // 0xaf2258: cmp             x5, x1
    // 0xaf225c: b.ne            #0xaf2268
    // 0xaf2260: mov             x1, x4
    // 0xaf2264: r0 = _growToNextCapacity()
    //     0xaf2264: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf2268: ldur            x2, [fp, #-0x30]
    // 0xaf226c: ldur            x3, [fp, #-0x40]
    // 0xaf2270: add             x0, x3, #1
    // 0xaf2274: lsl             x1, x0, #1
    // 0xaf2278: StoreField: r2->field_b = r1
    //     0xaf2278: stur            w1, [x2, #0xb]
    // 0xaf227c: LoadField: r1 = r2->field_f
    //     0xaf227c: ldur            w1, [x2, #0xf]
    // 0xaf2280: DecompressPointer r1
    //     0xaf2280: add             x1, x1, HEAP, lsl #32
    // 0xaf2284: ldur            x0, [fp, #-0x10]
    // 0xaf2288: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf2288: add             x25, x1, x3, lsl #2
    //     0xaf228c: add             x25, x25, #0xf
    //     0xaf2290: str             w0, [x25]
    //     0xaf2294: tbz             w0, #0, #0xaf22b0
    //     0xaf2298: ldurb           w16, [x1, #-1]
    //     0xaf229c: ldurb           w17, [x0, #-1]
    //     0xaf22a0: and             x16, x17, x16, lsr #2
    //     0xaf22a4: tst             x16, HEAP, lsr #32
    //     0xaf22a8: b.eq            #0xaf22b0
    //     0xaf22ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf22b0: r0 = Column()
    //     0xaf22b0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf22b4: mov             x1, x0
    // 0xaf22b8: r0 = Instance_Axis
    //     0xaf22b8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf22bc: stur            x1, [fp, #-0x10]
    // 0xaf22c0: StoreField: r1->field_f = r0
    //     0xaf22c0: stur            w0, [x1, #0xf]
    // 0xaf22c4: r2 = Instance_MainAxisAlignment
    //     0xaf22c4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf22c8: ldr             x2, [x2, #0x730]
    // 0xaf22cc: StoreField: r1->field_13 = r2
    //     0xaf22cc: stur            w2, [x1, #0x13]
    // 0xaf22d0: r3 = Instance_MainAxisSize
    //     0xaf22d0: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf22d4: ldr             x3, [x3, #0x738]
    // 0xaf22d8: ArrayStore: r1[0] = r3  ; List_4
    //     0xaf22d8: stur            w3, [x1, #0x17]
    // 0xaf22dc: r4 = Instance_CrossAxisAlignment
    //     0xaf22dc: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf22e0: ldr             x4, [x4, #0x740]
    // 0xaf22e4: StoreField: r1->field_1b = r4
    //     0xaf22e4: stur            w4, [x1, #0x1b]
    // 0xaf22e8: r5 = Instance_VerticalDirection
    //     0xaf22e8: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf22ec: ldr             x5, [x5, #0x748]
    // 0xaf22f0: StoreField: r1->field_23 = r5
    //     0xaf22f0: stur            w5, [x1, #0x23]
    // 0xaf22f4: r6 = Instance_Clip
    //     0xaf22f4: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf22f8: ldr             x6, [x6, #0x750]
    // 0xaf22fc: StoreField: r1->field_2b = r6
    //     0xaf22fc: stur            w6, [x1, #0x2b]
    // 0xaf2300: StoreField: r1->field_2f = rZR
    //     0xaf2300: stur            xzr, [x1, #0x2f]
    // 0xaf2304: ldur            x7, [fp, #-0x30]
    // 0xaf2308: StoreField: r1->field_b = r7
    //     0xaf2308: stur            w7, [x1, #0xb]
    // 0xaf230c: r0 = RefreshIndicator()
    //     0xaf230c: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xaf2310: mov             x3, x0
    // 0xaf2314: ldur            x0, [fp, #-0x10]
    // 0xaf2318: stur            x3, [fp, #-0x28]
    // 0xaf231c: StoreField: r3->field_b = r0
    //     0xaf231c: stur            w0, [x3, #0xb]
    // 0xaf2320: d0 = 40.000000
    //     0xaf2320: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xaf2324: StoreField: r3->field_f = d0
    //     0xaf2324: stur            d0, [x3, #0xf]
    // 0xaf2328: ArrayStore: r3[0] = rZR  ; List_8
    //     0xaf2328: stur            xzr, [x3, #0x17]
    // 0xaf232c: ldur            x2, [fp, #-0x20]
    // 0xaf2330: r1 = Function 'onPageRefresh':.
    //     0xaf2330: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1d0] AnonymousClosure: (0x8f0568), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageRefresh (0x8f04b8)
    //     0xaf2334: ldr             x1, [x1, #0x1d0]
    // 0xaf2338: r0 = AllocateClosure()
    //     0xaf2338: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf233c: mov             x1, x0
    // 0xaf2340: ldur            x0, [fp, #-0x28]
    // 0xaf2344: StoreField: r0->field_1f = r1
    //     0xaf2344: stur            w1, [x0, #0x1f]
    // 0xaf2348: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xaf2348: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xaf234c: ldr             x1, [x1, #0xf58]
    // 0xaf2350: StoreField: r0->field_2f = r1
    //     0xaf2350: stur            w1, [x0, #0x2f]
    // 0xaf2354: d0 = 2.500000
    //     0xaf2354: fmov            d0, #2.50000000
    // 0xaf2358: StoreField: r0->field_3b = d0
    //     0xaf2358: stur            d0, [x0, #0x3b]
    // 0xaf235c: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xaf235c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xaf2360: ldr             x1, [x1, #0xa68]
    // 0xaf2364: StoreField: r0->field_47 = r1
    //     0xaf2364: stur            w1, [x0, #0x47]
    // 0xaf2368: d0 = 2.000000
    //     0xaf2368: fmov            d0, #2.00000000
    // 0xaf236c: StoreField: r0->field_4b = d0
    //     0xaf236c: stur            d0, [x0, #0x4b]
    // 0xaf2370: r1 = Instance__IndicatorType
    //     0xaf2370: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xaf2374: ldr             x1, [x1, #0xa70]
    // 0xaf2378: StoreField: r0->field_43 = r1
    //     0xaf2378: stur            w1, [x0, #0x43]
    // 0xaf237c: r1 = <FlexParentData>
    //     0xaf237c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf2380: ldr             x1, [x1, #0x720]
    // 0xaf2384: r0 = Expanded()
    //     0xaf2384: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf2388: mov             x3, x0
    // 0xaf238c: r0 = 1
    //     0xaf238c: movz            x0, #0x1
    // 0xaf2390: stur            x3, [fp, #-0x10]
    // 0xaf2394: StoreField: r3->field_13 = r0
    //     0xaf2394: stur            x0, [x3, #0x13]
    // 0xaf2398: r0 = Instance_FlexFit
    //     0xaf2398: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf239c: ldr             x0, [x0, #0x728]
    // 0xaf23a0: StoreField: r3->field_1b = r0
    //     0xaf23a0: stur            w0, [x3, #0x1b]
    // 0xaf23a4: ldur            x0, [fp, #-0x28]
    // 0xaf23a8: StoreField: r3->field_b = r0
    //     0xaf23a8: stur            w0, [x3, #0xb]
    // 0xaf23ac: r1 = Null
    //     0xaf23ac: mov             x1, NULL
    // 0xaf23b0: r2 = 2
    //     0xaf23b0: movz            x2, #0x2
    // 0xaf23b4: r0 = AllocateArray()
    //     0xaf23b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf23b8: mov             x2, x0
    // 0xaf23bc: ldur            x0, [fp, #-0x10]
    // 0xaf23c0: stur            x2, [fp, #-0x20]
    // 0xaf23c4: StoreField: r2->field_f = r0
    //     0xaf23c4: stur            w0, [x2, #0xf]
    // 0xaf23c8: r1 = <Widget>
    //     0xaf23c8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf23cc: r0 = AllocateGrowableArray()
    //     0xaf23cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf23d0: mov             x2, x0
    // 0xaf23d4: ldur            x0, [fp, #-0x20]
    // 0xaf23d8: stur            x2, [fp, #-0x10]
    // 0xaf23dc: StoreField: r2->field_f = r0
    //     0xaf23dc: stur            w0, [x2, #0xf]
    // 0xaf23e0: r0 = 2
    //     0xaf23e0: movz            x0, #0x2
    // 0xaf23e4: StoreField: r2->field_b = r0
    //     0xaf23e4: stur            w0, [x2, #0xb]
    // 0xaf23e8: ldur            x1, [fp, #-8]
    // 0xaf23ec: r0 = controller()
    //     0xaf23ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf23f0: LoadField: r1 = r0->field_4f
    //     0xaf23f0: ldur            w1, [x0, #0x4f]
    // 0xaf23f4: DecompressPointer r1
    //     0xaf23f4: add             x1, x1, HEAP, lsl #32
    // 0xaf23f8: r0 = _adsVisibility()
    //     0xaf23f8: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xaf23fc: mov             x2, x0
    // 0xaf2400: r1 = Null
    //     0xaf2400: mov             x1, NULL
    // 0xaf2404: r0 = AdsConfig.fromJson()
    //     0xaf2404: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xaf2408: LoadField: r1 = r0->field_1f
    //     0xaf2408: ldur            w1, [x0, #0x1f]
    // 0xaf240c: DecompressPointer r1
    //     0xaf240c: add             x1, x1, HEAP, lsl #32
    // 0xaf2410: LoadField: r0 = r1->field_7
    //     0xaf2410: ldur            w0, [x1, #7]
    // 0xaf2414: DecompressPointer r0
    //     0xaf2414: add             x0, x0, HEAP, lsl #32
    // 0xaf2418: tbnz            w0, #4, #0xaf24e4
    // 0xaf241c: ldur            x0, [fp, #-0x10]
    // 0xaf2420: ldur            x1, [fp, #-8]
    // 0xaf2424: r0 = controller()
    //     0xaf2424: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2428: LoadField: r1 = r0->field_4f
    //     0xaf2428: ldur            w1, [x0, #0x4f]
    // 0xaf242c: DecompressPointer r1
    //     0xaf242c: add             x1, x1, HEAP, lsl #32
    // 0xaf2430: r0 = _adsVisibility()
    //     0xaf2430: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xaf2434: mov             x2, x0
    // 0xaf2438: r1 = Null
    //     0xaf2438: mov             x1, NULL
    // 0xaf243c: r0 = AdsConfig.fromJson()
    //     0xaf243c: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xaf2440: LoadField: r1 = r0->field_1f
    //     0xaf2440: ldur            w1, [x0, #0x1f]
    // 0xaf2444: DecompressPointer r1
    //     0xaf2444: add             x1, x1, HEAP, lsl #32
    // 0xaf2448: LoadField: r0 = r1->field_f
    //     0xaf2448: ldur            w0, [x1, #0xf]
    // 0xaf244c: DecompressPointer r0
    //     0xaf244c: add             x0, x0, HEAP, lsl #32
    // 0xaf2450: stur            x0, [fp, #-8]
    // 0xaf2454: r0 = AdmobBannerWidget()
    //     0xaf2454: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xaf2458: mov             x2, x0
    // 0xaf245c: ldur            x0, [fp, #-8]
    // 0xaf2460: stur            x2, [fp, #-0x20]
    // 0xaf2464: StoreField: r2->field_b = r0
    //     0xaf2464: stur            w0, [x2, #0xb]
    // 0xaf2468: ldur            x0, [fp, #-0x10]
    // 0xaf246c: LoadField: r1 = r0->field_b
    //     0xaf246c: ldur            w1, [x0, #0xb]
    // 0xaf2470: LoadField: r3 = r0->field_f
    //     0xaf2470: ldur            w3, [x0, #0xf]
    // 0xaf2474: DecompressPointer r3
    //     0xaf2474: add             x3, x3, HEAP, lsl #32
    // 0xaf2478: LoadField: r4 = r3->field_b
    //     0xaf2478: ldur            w4, [x3, #0xb]
    // 0xaf247c: r3 = LoadInt32Instr(r1)
    //     0xaf247c: sbfx            x3, x1, #1, #0x1f
    // 0xaf2480: stur            x3, [fp, #-0x40]
    // 0xaf2484: r1 = LoadInt32Instr(r4)
    //     0xaf2484: sbfx            x1, x4, #1, #0x1f
    // 0xaf2488: cmp             x3, x1
    // 0xaf248c: b.ne            #0xaf2498
    // 0xaf2490: mov             x1, x0
    // 0xaf2494: r0 = _growToNextCapacity()
    //     0xaf2494: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf2498: ldur            x2, [fp, #-0x10]
    // 0xaf249c: ldur            x3, [fp, #-0x40]
    // 0xaf24a0: add             x0, x3, #1
    // 0xaf24a4: lsl             x1, x0, #1
    // 0xaf24a8: StoreField: r2->field_b = r1
    //     0xaf24a8: stur            w1, [x2, #0xb]
    // 0xaf24ac: LoadField: r1 = r2->field_f
    //     0xaf24ac: ldur            w1, [x2, #0xf]
    // 0xaf24b0: DecompressPointer r1
    //     0xaf24b0: add             x1, x1, HEAP, lsl #32
    // 0xaf24b4: ldur            x0, [fp, #-0x20]
    // 0xaf24b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf24b8: add             x25, x1, x3, lsl #2
    //     0xaf24bc: add             x25, x25, #0xf
    //     0xaf24c0: str             w0, [x25]
    //     0xaf24c4: tbz             w0, #0, #0xaf24e0
    //     0xaf24c8: ldurb           w16, [x1, #-1]
    //     0xaf24cc: ldurb           w17, [x0, #-1]
    //     0xaf24d0: and             x16, x17, x16, lsr #2
    //     0xaf24d4: tst             x16, HEAP, lsr #32
    //     0xaf24d8: b.eq            #0xaf24e0
    //     0xaf24dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf24e0: b               #0xaf24e8
    // 0xaf24e4: ldur            x2, [fp, #-0x10]
    // 0xaf24e8: ldur            x0, [fp, #-0x18]
    // 0xaf24ec: r0 = Column()
    //     0xaf24ec: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf24f0: mov             x1, x0
    // 0xaf24f4: r0 = Instance_Axis
    //     0xaf24f4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf24f8: stur            x1, [fp, #-8]
    // 0xaf24fc: StoreField: r1->field_f = r0
    //     0xaf24fc: stur            w0, [x1, #0xf]
    // 0xaf2500: r0 = Instance_MainAxisAlignment
    //     0xaf2500: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf2504: ldr             x0, [x0, #0x730]
    // 0xaf2508: StoreField: r1->field_13 = r0
    //     0xaf2508: stur            w0, [x1, #0x13]
    // 0xaf250c: r0 = Instance_MainAxisSize
    //     0xaf250c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf2510: ldr             x0, [x0, #0x738]
    // 0xaf2514: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf2514: stur            w0, [x1, #0x17]
    // 0xaf2518: r0 = Instance_CrossAxisAlignment
    //     0xaf2518: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf251c: ldr             x0, [x0, #0x740]
    // 0xaf2520: StoreField: r1->field_1b = r0
    //     0xaf2520: stur            w0, [x1, #0x1b]
    // 0xaf2524: r0 = Instance_VerticalDirection
    //     0xaf2524: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf2528: ldr             x0, [x0, #0x748]
    // 0xaf252c: StoreField: r1->field_23 = r0
    //     0xaf252c: stur            w0, [x1, #0x23]
    // 0xaf2530: r0 = Instance_Clip
    //     0xaf2530: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf2534: ldr             x0, [x0, #0x750]
    // 0xaf2538: StoreField: r1->field_2b = r0
    //     0xaf2538: stur            w0, [x1, #0x2b]
    // 0xaf253c: StoreField: r1->field_2f = rZR
    //     0xaf253c: stur            xzr, [x1, #0x2f]
    // 0xaf2540: ldur            x0, [fp, #-0x10]
    // 0xaf2544: StoreField: r1->field_b = r0
    //     0xaf2544: stur            w0, [x1, #0xb]
    // 0xaf2548: r0 = Scaffold()
    //     0xaf2548: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf254c: ldur            x1, [fp, #-0x18]
    // 0xaf2550: StoreField: r0->field_13 = r1
    //     0xaf2550: stur            w1, [x0, #0x13]
    // 0xaf2554: ldur            x1, [fp, #-8]
    // 0xaf2558: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf2558: stur            w1, [x0, #0x17]
    // 0xaf255c: r1 = Instance_AlignmentDirectional
    //     0xaf255c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf2560: ldr             x1, [x1, #0x758]
    // 0xaf2564: StoreField: r0->field_2b = r1
    //     0xaf2564: stur            w1, [x0, #0x2b]
    // 0xaf2568: r1 = true
    //     0xaf2568: add             x1, NULL, #0x20  ; true
    // 0xaf256c: StoreField: r0->field_53 = r1
    //     0xaf256c: stur            w1, [x0, #0x53]
    // 0xaf2570: r2 = Instance_DragStartBehavior
    //     0xaf2570: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf2574: StoreField: r0->field_57 = r2
    //     0xaf2574: stur            w2, [x0, #0x57]
    // 0xaf2578: r2 = false
    //     0xaf2578: add             x2, NULL, #0x30  ; false
    // 0xaf257c: StoreField: r0->field_b = r2
    //     0xaf257c: stur            w2, [x0, #0xb]
    // 0xaf2580: StoreField: r0->field_f = r2
    //     0xaf2580: stur            w2, [x0, #0xf]
    // 0xaf2584: StoreField: r0->field_5f = r1
    //     0xaf2584: stur            w1, [x0, #0x5f]
    // 0xaf2588: StoreField: r0->field_63 = r1
    //     0xaf2588: stur            w1, [x0, #0x63]
    // 0xaf258c: LeaveFrame
    //     0xaf258c: mov             SP, fp
    //     0xaf2590: ldp             fp, lr, [SP], #0x10
    // 0xaf2594: ret
    //     0xaf2594: ret             
    // 0xaf2598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf259c: b               #0xaf1ff8
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xaf25e4, size: 0x124
    // 0xaf25e4: EnterFrame
    //     0xaf25e4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf25e8: mov             fp, SP
    // 0xaf25ec: AllocStack(0x38)
    //     0xaf25ec: sub             SP, SP, #0x38
    // 0xaf25f0: SetupParameters()
    //     0xaf25f0: ldr             x0, [fp, #0x10]
    //     0xaf25f4: ldur            w2, [x0, #0x17]
    //     0xaf25f8: add             x2, x2, HEAP, lsl #32
    //     0xaf25fc: stur            x2, [fp, #-8]
    // 0xaf2600: CheckStackOverflow
    //     0xaf2600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2604: cmp             SP, x16
    //     0xaf2608: b.ls            #0xaf2700
    // 0xaf260c: LoadField: r1 = r2->field_f
    //     0xaf260c: ldur            w1, [x2, #0xf]
    // 0xaf2610: DecompressPointer r1
    //     0xaf2610: add             x1, x1, HEAP, lsl #32
    // 0xaf2614: r0 = controller()
    //     0xaf2614: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2618: mov             x1, x0
    // 0xaf261c: r0 = query()
    //     0xaf261c: bl              #0xada394  ; [package:nuonline/app/modules/doa/doa_list/controllers/doa_list_controller.dart] _DoaListController&OfflineFirstController&StateMixin&AnalyticMixin&SearchMixin::query
    // 0xaf2620: tbnz            w0, #4, #0xaf2638
    // 0xaf2624: r0 = Instance_SizedBox
    //     0xaf2624: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xaf2628: ldr             x0, [x0, #0xc40]
    // 0xaf262c: LeaveFrame
    //     0xaf262c: mov             SP, fp
    //     0xaf2630: ldp             fp, lr, [SP], #0x10
    // 0xaf2634: ret
    //     0xaf2634: ret             
    // 0xaf2638: ldur            x2, [fp, #-8]
    // 0xaf263c: LoadField: r1 = r2->field_f
    //     0xaf263c: ldur            w1, [x2, #0xf]
    // 0xaf2640: DecompressPointer r1
    //     0xaf2640: add             x1, x1, HEAP, lsl #32
    // 0xaf2644: r0 = controller()
    //     0xaf2644: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2648: ldur            x2, [fp, #-8]
    // 0xaf264c: stur            x0, [fp, #-0x10]
    // 0xaf2650: LoadField: r1 = r2->field_f
    //     0xaf2650: ldur            w1, [x2, #0xf]
    // 0xaf2654: DecompressPointer r1
    //     0xaf2654: add             x1, x1, HEAP, lsl #32
    // 0xaf2658: r0 = controller()
    //     0xaf2658: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf265c: mov             x1, x0
    // 0xaf2660: r0 = itemsCount()
    //     0xaf2660: bl              #0xaeaec0  ; [package:nuonline/app/modules/donation/controllers/donation_news_builder_controller.dart] _DonationNewsListBuilderController&FetchController&GetSingleTickerProviderStateMixin&PagingMixin::itemsCount
    // 0xaf2664: r1 = Function '<anonymous closure>':.
    //     0xaf2664: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xaf2668: ldr             x1, [x1, #0x1d8]
    // 0xaf266c: r2 = Null
    //     0xaf266c: mov             x2, NULL
    // 0xaf2670: stur            x0, [fp, #-0x18]
    // 0xaf2674: r0 = AllocateClosure()
    //     0xaf2674: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2678: ldur            x2, [fp, #-8]
    // 0xaf267c: r1 = Function '<anonymous closure>':.
    //     0xaf267c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] AnonymousClosure: (0xaf2810), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2680: ldr             x1, [x1, #0x1e0]
    // 0xaf2684: stur            x0, [fp, #-8]
    // 0xaf2688: r0 = AllocateClosure()
    //     0xaf2688: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf268c: stur            x0, [fp, #-0x20]
    // 0xaf2690: r0 = ListView()
    //     0xaf2690: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf2694: stur            x0, [fp, #-0x28]
    // 0xaf2698: r16 = true
    //     0xaf2698: add             x16, NULL, #0x20  ; true
    // 0xaf269c: r30 = Instance_EdgeInsets
    //     0xaf269c: add             lr, PP, #0x29, lsl #12  ; [pp+0x29900] Obj!EdgeInsets@e12641
    //     0xaf26a0: ldr             lr, [lr, #0x900]
    // 0xaf26a4: stp             lr, x16, [SP]
    // 0xaf26a8: mov             x1, x0
    // 0xaf26ac: ldur            x2, [fp, #-0x20]
    // 0xaf26b0: ldur            x3, [fp, #-0x18]
    // 0xaf26b4: ldur            x5, [fp, #-8]
    // 0xaf26b8: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xaf26b8: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xaf26bc: ldr             x4, [x4, #0x100]
    // 0xaf26c0: r0 = ListView.separated()
    //     0xaf26c0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf26c4: ldur            x2, [fp, #-0x10]
    // 0xaf26c8: r1 = Function 'onPageScrolled':.
    //     0xaf26c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] AnonymousClosure: (0xaf2708), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::onPageScrolled (0xaf2744)
    //     0xaf26cc: ldr             x1, [x1, #0x1e8]
    // 0xaf26d0: r0 = AllocateClosure()
    //     0xaf26d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf26d4: r1 = <ScrollNotification>
    //     0xaf26d4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xaf26d8: ldr             x1, [x1, #0x110]
    // 0xaf26dc: stur            x0, [fp, #-8]
    // 0xaf26e0: r0 = NotificationListener()
    //     0xaf26e0: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xaf26e4: ldur            x1, [fp, #-8]
    // 0xaf26e8: StoreField: r0->field_13 = r1
    //     0xaf26e8: stur            w1, [x0, #0x13]
    // 0xaf26ec: ldur            x1, [fp, #-0x28]
    // 0xaf26f0: StoreField: r0->field_b = r1
    //     0xaf26f0: stur            w1, [x0, #0xb]
    // 0xaf26f4: LeaveFrame
    //     0xaf26f4: mov             SP, fp
    //     0xaf26f8: ldp             fp, lr, [SP], #0x10
    // 0xaf26fc: ret
    //     0xaf26fc: ret             
    // 0xaf2700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2704: b               #0xaf260c
  }
  [closure] StatelessWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf2810, size: 0x2a4
    // 0xaf2810: EnterFrame
    //     0xaf2810: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2814: mov             fp, SP
    // 0xaf2818: AllocStack(0x58)
    //     0xaf2818: sub             SP, SP, #0x58
    // 0xaf281c: SetupParameters()
    //     0xaf281c: ldr             x0, [fp, #0x20]
    //     0xaf2820: ldur            w1, [x0, #0x17]
    //     0xaf2824: add             x1, x1, HEAP, lsl #32
    //     0xaf2828: stur            x1, [fp, #-8]
    // 0xaf282c: CheckStackOverflow
    //     0xaf282c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2830: cmp             SP, x16
    //     0xaf2834: b.ls            #0xaf2aa8
    // 0xaf2838: r1 = 1
    //     0xaf2838: movz            x1, #0x1
    // 0xaf283c: r0 = AllocateContext()
    //     0xaf283c: bl              #0xec126c  ; AllocateContextStub
    // 0xaf2840: mov             x2, x0
    // 0xaf2844: ldur            x0, [fp, #-8]
    // 0xaf2848: stur            x2, [fp, #-0x10]
    // 0xaf284c: StoreField: r2->field_b = r0
    //     0xaf284c: stur            w0, [x2, #0xb]
    // 0xaf2850: LoadField: r1 = r0->field_f
    //     0xaf2850: ldur            w1, [x0, #0xf]
    // 0xaf2854: DecompressPointer r1
    //     0xaf2854: add             x1, x1, HEAP, lsl #32
    // 0xaf2858: r0 = controller()
    //     0xaf2858: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf285c: mov             x1, x0
    // 0xaf2860: ldr             x0, [fp, #0x10]
    // 0xaf2864: r2 = LoadInt32Instr(r0)
    //     0xaf2864: sbfx            x2, x0, #1, #0x1f
    //     0xaf2868: tbz             w0, #0, #0xaf2870
    //     0xaf286c: ldur            x2, [x0, #7]
    // 0xaf2870: r0 = find()
    //     0xaf2870: bl              #0xaf2aec  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin::find
    // 0xaf2874: mov             x1, x0
    // 0xaf2878: ldur            x2, [fp, #-0x10]
    // 0xaf287c: stur            x1, [fp, #-0x20]
    // 0xaf2880: StoreField: r2->field_f = r0
    //     0xaf2880: stur            w0, [x2, #0xf]
    //     0xaf2884: ldurb           w16, [x2, #-1]
    //     0xaf2888: ldurb           w17, [x0, #-1]
    //     0xaf288c: and             x16, x17, x16, lsr #2
    //     0xaf2890: tst             x16, HEAP, lsr #32
    //     0xaf2894: b.eq            #0xaf289c
    //     0xaf2898: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaf289c: cmp             w1, NULL
    // 0xaf28a0: b.ne            #0xaf28b8
    // 0xaf28a4: r0 = Instance__ItemSkeleton
    //     0xaf28a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!_ItemSkeleton@e1f9c1
    //     0xaf28a8: ldr             x0, [x0, #0x1f0]
    // 0xaf28ac: LeaveFrame
    //     0xaf28ac: mov             SP, fp
    //     0xaf28b0: ldp             fp, lr, [SP], #0x10
    // 0xaf28b4: ret
    //     0xaf28b4: ret             
    // 0xaf28b8: ldur            x0, [fp, #-8]
    // 0xaf28bc: LoadField: r3 = r1->field_f
    //     0xaf28bc: ldur            w3, [x1, #0xf]
    // 0xaf28c0: DecompressPointer r3
    //     0xaf28c0: add             x3, x3, HEAP, lsl #32
    // 0xaf28c4: stur            x3, [fp, #-0x18]
    // 0xaf28c8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf28c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf28cc: ldr             x0, [x0, #0x2670]
    //     0xaf28d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf28d4: cmp             w0, w16
    //     0xaf28d8: b.ne            #0xaf28e4
    //     0xaf28dc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf28e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf28e4: r0 = GetNavigation.textTheme()
    //     0xaf28e4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf28e8: LoadField: r2 = r0->field_23
    //     0xaf28e8: ldur            w2, [x0, #0x23]
    // 0xaf28ec: DecompressPointer r2
    //     0xaf28ec: add             x2, x2, HEAP, lsl #32
    // 0xaf28f0: stur            x2, [fp, #-0x28]
    // 0xaf28f4: cmp             w2, NULL
    // 0xaf28f8: b.eq            #0xaf2ab0
    // 0xaf28fc: ldur            x0, [fp, #-8]
    // 0xaf2900: LoadField: r1 = r0->field_f
    //     0xaf2900: ldur            w1, [x0, #0xf]
    // 0xaf2904: DecompressPointer r1
    //     0xaf2904: add             x1, x1, HEAP, lsl #32
    // 0xaf2908: r0 = controller()
    //     0xaf2908: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf290c: mov             x1, x0
    // 0xaf2910: r0 = query()
    //     0xaf2910: bl              #0xaf2ab4  ; [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::query
    // 0xaf2914: r1 = LoadClassIdInstr(r0)
    //     0xaf2914: ldur            x1, [x0, #-1]
    //     0xaf2918: ubfx            x1, x1, #0xc, #0x14
    // 0xaf291c: mov             x16, x0
    // 0xaf2920: mov             x0, x1
    // 0xaf2924: mov             x1, x16
    // 0xaf2928: r2 = " "
    //     0xaf2928: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xaf292c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaf292c: sub             lr, x0, #1, lsl #12
    //     0xaf2930: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2934: blr             lr
    // 0xaf2938: r1 = _ConstMap len:10
    //     0xaf2938: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaf293c: ldr             x1, [x1, #0xc08]
    // 0xaf2940: r2 = 600
    //     0xaf2940: movz            x2, #0x258
    // 0xaf2944: stur            x0, [fp, #-0x30]
    // 0xaf2948: r0 = []()
    //     0xaf2948: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf294c: r16 = <Color?>
    //     0xaf294c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf2950: ldr             x16, [x16, #0x98]
    // 0xaf2954: stp             x0, x16, [SP, #8]
    // 0xaf2958: r16 = Instance_MaterialColor
    //     0xaf2958: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaf295c: ldr             x16, [x16, #0xcf0]
    // 0xaf2960: str             x16, [SP]
    // 0xaf2964: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf2964: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf2968: r0 = mode()
    //     0xaf2968: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf296c: stur            x0, [fp, #-0x38]
    // 0xaf2970: r0 = TextStyle()
    //     0xaf2970: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf2974: mov             x1, x0
    // 0xaf2978: r0 = true
    //     0xaf2978: add             x0, NULL, #0x20  ; true
    // 0xaf297c: stur            x1, [fp, #-0x40]
    // 0xaf2980: StoreField: r1->field_7 = r0
    //     0xaf2980: stur            w0, [x1, #7]
    // 0xaf2984: ldur            x2, [fp, #-0x38]
    // 0xaf2988: StoreField: r1->field_b = r2
    //     0xaf2988: stur            w2, [x1, #0xb]
    // 0xaf298c: r0 = SubstringHighlight()
    //     0xaf298c: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xaf2990: mov             x2, x0
    // 0xaf2994: r0 = false
    //     0xaf2994: add             x0, NULL, #0x30  ; false
    // 0xaf2998: stur            x2, [fp, #-0x38]
    // 0xaf299c: StoreField: r2->field_b = r0
    //     0xaf299c: stur            w0, [x2, #0xb]
    // 0xaf29a0: r1 = Instance_TextOverflow
    //     0xaf29a0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xaf29a4: ldr             x1, [x1, #0xc60]
    // 0xaf29a8: StoreField: r2->field_f = r1
    //     0xaf29a8: stur            w1, [x2, #0xf]
    // 0xaf29ac: ldur            x1, [fp, #-0x30]
    // 0xaf29b0: StoreField: r2->field_1b = r1
    //     0xaf29b0: stur            w1, [x2, #0x1b]
    // 0xaf29b4: ldur            x1, [fp, #-0x18]
    // 0xaf29b8: StoreField: r2->field_1f = r1
    //     0xaf29b8: stur            w1, [x2, #0x1f]
    // 0xaf29bc: r1 = Instance_TextAlign
    //     0xaf29bc: ldr             x1, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xaf29c0: StoreField: r2->field_23 = r1
    //     0xaf29c0: stur            w1, [x2, #0x23]
    // 0xaf29c4: ldur            x1, [fp, #-0x28]
    // 0xaf29c8: StoreField: r2->field_27 = r1
    //     0xaf29c8: stur            w1, [x2, #0x27]
    // 0xaf29cc: ldur            x1, [fp, #-0x40]
    // 0xaf29d0: StoreField: r2->field_2b = r1
    //     0xaf29d0: stur            w1, [x2, #0x2b]
    // 0xaf29d4: StoreField: r2->field_2f = r0
    //     0xaf29d4: stur            w0, [x2, #0x2f]
    // 0xaf29d8: ldur            x1, [fp, #-8]
    // 0xaf29dc: LoadField: r3 = r1->field_f
    //     0xaf29dc: ldur            w3, [x1, #0xf]
    // 0xaf29e0: DecompressPointer r3
    //     0xaf29e0: add             x3, x3, HEAP, lsl #32
    // 0xaf29e4: mov             x1, x3
    // 0xaf29e8: r0 = controller()
    //     0xaf29e8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf29ec: LoadField: r1 = r0->field_4b
    //     0xaf29ec: ldur            w1, [x0, #0x4b]
    // 0xaf29f0: DecompressPointer r1
    //     0xaf29f0: add             x1, x1, HEAP, lsl #32
    // 0xaf29f4: cmp             w1, NULL
    // 0xaf29f8: b.ne            #0xaf2a28
    // 0xaf29fc: ldur            x0, [fp, #-0x20]
    // 0xaf2a00: LoadField: r1 = r0->field_13
    //     0xaf2a00: ldur            w1, [x0, #0x13]
    // 0xaf2a04: DecompressPointer r1
    //     0xaf2a04: add             x1, x1, HEAP, lsl #32
    // 0xaf2a08: LoadField: r0 = r1->field_f
    //     0xaf2a08: ldur            w0, [x1, #0xf]
    // 0xaf2a0c: DecompressPointer r0
    //     0xaf2a0c: add             x0, x0, HEAP, lsl #32
    // 0xaf2a10: stur            x0, [fp, #-8]
    // 0xaf2a14: r0 = Text()
    //     0xaf2a14: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf2a18: mov             x1, x0
    // 0xaf2a1c: ldur            x0, [fp, #-8]
    // 0xaf2a20: StoreField: r1->field_b = r0
    //     0xaf2a20: stur            w0, [x1, #0xb]
    // 0xaf2a24: b               #0xaf2a2c
    // 0xaf2a28: r1 = Null
    //     0xaf2a28: mov             x1, NULL
    // 0xaf2a2c: ldur            x0, [fp, #-0x38]
    // 0xaf2a30: stur            x1, [fp, #-8]
    // 0xaf2a34: r0 = ListTile()
    //     0xaf2a34: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xaf2a38: mov             x3, x0
    // 0xaf2a3c: ldur            x0, [fp, #-0x38]
    // 0xaf2a40: stur            x3, [fp, #-0x18]
    // 0xaf2a44: StoreField: r3->field_f = r0
    //     0xaf2a44: stur            w0, [x3, #0xf]
    // 0xaf2a48: ldur            x0, [fp, #-8]
    // 0xaf2a4c: StoreField: r3->field_13 = r0
    //     0xaf2a4c: stur            w0, [x3, #0x13]
    // 0xaf2a50: r0 = Instance_Icon
    //     0xaf2a50: add             x0, PP, #0x29, lsl #12  ; [pp+0x29908] Obj!Icon@e245f1
    //     0xaf2a54: ldr             x0, [x0, #0x908]
    // 0xaf2a58: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf2a58: stur            w0, [x3, #0x17]
    // 0xaf2a5c: r0 = false
    //     0xaf2a5c: add             x0, NULL, #0x30  ; false
    // 0xaf2a60: StoreField: r3->field_1b = r0
    //     0xaf2a60: stur            w0, [x3, #0x1b]
    // 0xaf2a64: r4 = true
    //     0xaf2a64: add             x4, NULL, #0x20  ; true
    // 0xaf2a68: StoreField: r3->field_4b = r4
    //     0xaf2a68: stur            w4, [x3, #0x4b]
    // 0xaf2a6c: ldur            x2, [fp, #-0x10]
    // 0xaf2a70: r1 = Function '<anonymous closure>':.
    //     0xaf2a70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f8] AnonymousClosure: (0xaf2c0c), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2a74: ldr             x1, [x1, #0x1f8]
    // 0xaf2a78: r0 = AllocateClosure()
    //     0xaf2a78: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2a7c: mov             x1, x0
    // 0xaf2a80: ldur            x0, [fp, #-0x18]
    // 0xaf2a84: StoreField: r0->field_4f = r1
    //     0xaf2a84: stur            w1, [x0, #0x4f]
    // 0xaf2a88: r1 = false
    //     0xaf2a88: add             x1, NULL, #0x30  ; false
    // 0xaf2a8c: StoreField: r0->field_5f = r1
    //     0xaf2a8c: stur            w1, [x0, #0x5f]
    // 0xaf2a90: StoreField: r0->field_73 = r1
    //     0xaf2a90: stur            w1, [x0, #0x73]
    // 0xaf2a94: r1 = true
    //     0xaf2a94: add             x1, NULL, #0x20  ; true
    // 0xaf2a98: StoreField: r0->field_97 = r1
    //     0xaf2a98: stur            w1, [x0, #0x97]
    // 0xaf2a9c: LeaveFrame
    //     0xaf2a9c: mov             SP, fp
    //     0xaf2aa0: ldp             fp, lr, [SP], #0x10
    // 0xaf2aa4: ret
    //     0xaf2aa4: ret             
    // 0xaf2aa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2aa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2aac: b               #0xaf2838
    // 0xaf2ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2ab0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf2c0c, size: 0xec
    // 0xaf2c0c: EnterFrame
    //     0xaf2c0c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2c10: mov             fp, SP
    // 0xaf2c14: AllocStack(0x20)
    //     0xaf2c14: sub             SP, SP, #0x20
    // 0xaf2c18: SetupParameters()
    //     0xaf2c18: ldr             x0, [fp, #0x10]
    //     0xaf2c1c: ldur            w1, [x0, #0x17]
    //     0xaf2c20: add             x1, x1, HEAP, lsl #32
    //     0xaf2c24: stur            x1, [fp, #-8]
    // 0xaf2c28: CheckStackOverflow
    //     0xaf2c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2c2c: cmp             SP, x16
    //     0xaf2c30: b.ls            #0xaf2cec
    // 0xaf2c34: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf2c34: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf2c38: ldr             x0, [x0, #0x2670]
    //     0xaf2c3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf2c40: cmp             w0, w16
    //     0xaf2c44: b.ne            #0xaf2c50
    //     0xaf2c48: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf2c4c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf2c50: r1 = Null
    //     0xaf2c50: mov             x1, NULL
    // 0xaf2c54: r2 = 8
    //     0xaf2c54: movz            x2, #0x8
    // 0xaf2c58: r0 = AllocateArray()
    //     0xaf2c58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf2c5c: mov             x2, x0
    // 0xaf2c60: r16 = "id"
    //     0xaf2c60: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xaf2c64: ldr             x16, [x16, #0x740]
    // 0xaf2c68: StoreField: r2->field_f = r16
    //     0xaf2c68: stur            w16, [x2, #0xf]
    // 0xaf2c6c: ldur            x0, [fp, #-8]
    // 0xaf2c70: LoadField: r3 = r0->field_f
    //     0xaf2c70: ldur            w3, [x0, #0xf]
    // 0xaf2c74: DecompressPointer r3
    //     0xaf2c74: add             x3, x3, HEAP, lsl #32
    // 0xaf2c78: cmp             w3, NULL
    // 0xaf2c7c: b.eq            #0xaf2cf4
    // 0xaf2c80: LoadField: r4 = r3->field_7
    //     0xaf2c80: ldur            x4, [x3, #7]
    // 0xaf2c84: r0 = BoxInt64Instr(r4)
    //     0xaf2c84: sbfiz           x0, x4, #1, #0x1f
    //     0xaf2c88: cmp             x4, x0, asr #1
    //     0xaf2c8c: b.eq            #0xaf2c98
    //     0xaf2c90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf2c94: stur            x4, [x0, #7]
    // 0xaf2c98: StoreField: r2->field_13 = r0
    //     0xaf2c98: stur            w0, [x2, #0x13]
    // 0xaf2c9c: r16 = "title"
    //     0xaf2c9c: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xaf2ca0: ldr             x16, [x16, #0x748]
    // 0xaf2ca4: ArrayStore: r2[0] = r16  ; List_4
    //     0xaf2ca4: stur            w16, [x2, #0x17]
    // 0xaf2ca8: LoadField: r0 = r3->field_f
    //     0xaf2ca8: ldur            w0, [x3, #0xf]
    // 0xaf2cac: DecompressPointer r0
    //     0xaf2cac: add             x0, x0, HEAP, lsl #32
    // 0xaf2cb0: StoreField: r2->field_1b = r0
    //     0xaf2cb0: stur            w0, [x2, #0x1b]
    // 0xaf2cb4: r16 = <String, Object>
    //     0xaf2cb4: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xaf2cb8: ldr             x16, [x16, #0x790]
    // 0xaf2cbc: stp             x2, x16, [SP]
    // 0xaf2cc0: r0 = Map._fromLiteral()
    //     0xaf2cc0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf2cc4: r16 = "/encyclopedia/encyclopedia-detail"
    //     0xaf2cc4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f200] "/encyclopedia/encyclopedia-detail"
    //     0xaf2cc8: ldr             x16, [x16, #0x200]
    // 0xaf2ccc: stp             x16, NULL, [SP, #8]
    // 0xaf2cd0: str             x0, [SP]
    // 0xaf2cd4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xaf2cd4: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xaf2cd8: ldr             x4, [x4, #0x478]
    // 0xaf2cdc: r0 = GetNavigation.toNamed()
    //     0xaf2cdc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf2ce0: LeaveFrame
    //     0xaf2ce0: mov             SP, fp
    //     0xaf2ce4: ldp             fp, lr, [SP], #0x10
    // 0xaf2ce8: ret
    //     0xaf2ce8: ret             
    // 0xaf2cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2cf0: b               #0xaf2c34
    // 0xaf2cf4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaf2cf4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0xaf2cf8, size: 0x138
    // 0xaf2cf8: EnterFrame
    //     0xaf2cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2cfc: mov             fp, SP
    // 0xaf2d00: AllocStack(0x30)
    //     0xaf2d00: sub             SP, SP, #0x30
    // 0xaf2d04: SetupParameters()
    //     0xaf2d04: ldr             x0, [fp, #0x10]
    //     0xaf2d08: ldur            w2, [x0, #0x17]
    //     0xaf2d0c: add             x2, x2, HEAP, lsl #32
    //     0xaf2d10: stur            x2, [fp, #-8]
    // 0xaf2d14: CheckStackOverflow
    //     0xaf2d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2d18: cmp             SP, x16
    //     0xaf2d1c: b.ls            #0xaf2e28
    // 0xaf2d20: LoadField: r1 = r2->field_f
    //     0xaf2d20: ldur            w1, [x2, #0xf]
    // 0xaf2d24: DecompressPointer r1
    //     0xaf2d24: add             x1, x1, HEAP, lsl #32
    // 0xaf2d28: r0 = controller()
    //     0xaf2d28: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2d2c: LoadField: r1 = r0->field_5b
    //     0xaf2d2c: ldur            w1, [x0, #0x5b]
    // 0xaf2d30: DecompressPointer r1
    //     0xaf2d30: add             x1, x1, HEAP, lsl #32
    // 0xaf2d34: r0 = value()
    //     0xaf2d34: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2d38: r1 = LoadClassIdInstr(r0)
    //     0xaf2d38: ldur            x1, [x0, #-1]
    //     0xaf2d3c: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2d40: str             x0, [SP]
    // 0xaf2d44: mov             x0, x1
    // 0xaf2d48: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf2d48: movz            x17, #0xc834
    //     0xaf2d4c: add             lr, x0, x17
    //     0xaf2d50: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2d54: blr             lr
    // 0xaf2d58: cbnz            w0, #0xaf2d70
    // 0xaf2d5c: r0 = Instance__FilterSkeleton
    //     0xaf2d5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f208] Obj!_FilterSkeleton@e1f9d1
    //     0xaf2d60: ldr             x0, [x0, #0x208]
    // 0xaf2d64: LeaveFrame
    //     0xaf2d64: mov             SP, fp
    //     0xaf2d68: ldp             fp, lr, [SP], #0x10
    // 0xaf2d6c: ret
    //     0xaf2d6c: ret             
    // 0xaf2d70: ldur            x2, [fp, #-8]
    // 0xaf2d74: LoadField: r1 = r2->field_f
    //     0xaf2d74: ldur            w1, [x2, #0xf]
    // 0xaf2d78: DecompressPointer r1
    //     0xaf2d78: add             x1, x1, HEAP, lsl #32
    // 0xaf2d7c: r0 = controller()
    //     0xaf2d7c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2d80: LoadField: r1 = r0->field_5b
    //     0xaf2d80: ldur            w1, [x0, #0x5b]
    // 0xaf2d84: DecompressPointer r1
    //     0xaf2d84: add             x1, x1, HEAP, lsl #32
    // 0xaf2d88: r0 = value()
    //     0xaf2d88: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2d8c: r1 = LoadClassIdInstr(r0)
    //     0xaf2d8c: ldur            x1, [x0, #-1]
    //     0xaf2d90: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2d94: str             x0, [SP]
    // 0xaf2d98: mov             x0, x1
    // 0xaf2d9c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf2d9c: movz            x17, #0xc834
    //     0xaf2da0: add             lr, x0, x17
    //     0xaf2da4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2da8: blr             lr
    // 0xaf2dac: r3 = LoadInt32Instr(r0)
    //     0xaf2dac: sbfx            x3, x0, #1, #0x1f
    //     0xaf2db0: tbz             w0, #0, #0xaf2db8
    //     0xaf2db4: ldur            x3, [x0, #7]
    // 0xaf2db8: stur            x3, [fp, #-0x10]
    // 0xaf2dbc: r1 = Function '<anonymous closure>':.
    //     0xaf2dbc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f210] AnonymousClosure: (0xaf3054), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2dc0: ldr             x1, [x1, #0x210]
    // 0xaf2dc4: r2 = Null
    //     0xaf2dc4: mov             x2, NULL
    // 0xaf2dc8: r0 = AllocateClosure()
    //     0xaf2dc8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2dcc: ldur            x2, [fp, #-8]
    // 0xaf2dd0: r1 = Function '<anonymous closure>':.
    //     0xaf2dd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f218] AnonymousClosure: (0xaf2e30), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2dd4: ldr             x1, [x1, #0x218]
    // 0xaf2dd8: stur            x0, [fp, #-8]
    // 0xaf2ddc: r0 = AllocateClosure()
    //     0xaf2ddc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2de0: stur            x0, [fp, #-0x18]
    // 0xaf2de4: r0 = ListView()
    //     0xaf2de4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf2de8: stur            x0, [fp, #-0x20]
    // 0xaf2dec: r16 = Instance_Axis
    //     0xaf2dec: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaf2df0: r30 = Instance_EdgeInsets
    //     0xaf2df0: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xaf2df4: ldr             lr, [lr, #0x360]
    // 0xaf2df8: stp             lr, x16, [SP]
    // 0xaf2dfc: mov             x1, x0
    // 0xaf2e00: ldur            x2, [fp, #-0x18]
    // 0xaf2e04: ldur            x3, [fp, #-0x10]
    // 0xaf2e08: ldur            x5, [fp, #-8]
    // 0xaf2e0c: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, scrollDirection, 0x4, null]
    //     0xaf2e0c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "scrollDirection", 0x4, Null]
    //     0xaf2e10: ldr             x4, [x4, #0x220]
    // 0xaf2e14: r0 = ListView.separated()
    //     0xaf2e14: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf2e18: ldur            x0, [fp, #-0x20]
    // 0xaf2e1c: LeaveFrame
    //     0xaf2e1c: mov             SP, fp
    //     0xaf2e20: ldp             fp, lr, [SP], #0x10
    // 0xaf2e24: ret
    //     0xaf2e24: ret             
    // 0xaf2e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2e2c: b               #0xaf2d20
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf2e30, size: 0xdc
    // 0xaf2e30: EnterFrame
    //     0xaf2e30: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2e34: mov             fp, SP
    // 0xaf2e38: AllocStack(0x20)
    //     0xaf2e38: sub             SP, SP, #0x20
    // 0xaf2e3c: SetupParameters()
    //     0xaf2e3c: ldr             x0, [fp, #0x20]
    //     0xaf2e40: ldur            w1, [x0, #0x17]
    //     0xaf2e44: add             x1, x1, HEAP, lsl #32
    //     0xaf2e48: stur            x1, [fp, #-8]
    // 0xaf2e4c: CheckStackOverflow
    //     0xaf2e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2e50: cmp             SP, x16
    //     0xaf2e54: b.ls            #0xaf2f04
    // 0xaf2e58: r1 = 1
    //     0xaf2e58: movz            x1, #0x1
    // 0xaf2e5c: r0 = AllocateContext()
    //     0xaf2e5c: bl              #0xec126c  ; AllocateContextStub
    // 0xaf2e60: mov             x2, x0
    // 0xaf2e64: ldur            x0, [fp, #-8]
    // 0xaf2e68: stur            x2, [fp, #-0x10]
    // 0xaf2e6c: StoreField: r2->field_b = r0
    //     0xaf2e6c: stur            w0, [x2, #0xb]
    // 0xaf2e70: LoadField: r1 = r0->field_f
    //     0xaf2e70: ldur            w1, [x0, #0xf]
    // 0xaf2e74: DecompressPointer r1
    //     0xaf2e74: add             x1, x1, HEAP, lsl #32
    // 0xaf2e78: r0 = controller()
    //     0xaf2e78: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2e7c: LoadField: r1 = r0->field_5b
    //     0xaf2e7c: ldur            w1, [x0, #0x5b]
    // 0xaf2e80: DecompressPointer r1
    //     0xaf2e80: add             x1, x1, HEAP, lsl #32
    // 0xaf2e84: r0 = value()
    //     0xaf2e84: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xaf2e88: r1 = LoadClassIdInstr(r0)
    //     0xaf2e88: ldur            x1, [x0, #-1]
    //     0xaf2e8c: ubfx            x1, x1, #0xc, #0x14
    // 0xaf2e90: ldr             x16, [fp, #0x10]
    // 0xaf2e94: stp             x16, x0, [SP]
    // 0xaf2e98: mov             x0, x1
    // 0xaf2e9c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf2e9c: movz            x17, #0x3037
    //     0xaf2ea0: movk            x17, #0x1, lsl #16
    //     0xaf2ea4: add             lr, x0, x17
    //     0xaf2ea8: ldr             lr, [x21, lr, lsl #3]
    //     0xaf2eac: blr             lr
    // 0xaf2eb0: ldur            x2, [fp, #-0x10]
    // 0xaf2eb4: StoreField: r2->field_f = r0
    //     0xaf2eb4: stur            w0, [x2, #0xf]
    //     0xaf2eb8: tbz             w0, #0, #0xaf2ed4
    //     0xaf2ebc: ldurb           w16, [x2, #-1]
    //     0xaf2ec0: ldurb           w17, [x0, #-1]
    //     0xaf2ec4: and             x16, x17, x16, lsr #2
    //     0xaf2ec8: tst             x16, HEAP, lsr #32
    //     0xaf2ecc: b.eq            #0xaf2ed4
    //     0xaf2ed0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaf2ed4: r0 = Obx()
    //     0xaf2ed4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf2ed8: ldur            x2, [fp, #-0x10]
    // 0xaf2edc: r1 = Function '<anonymous closure>':.
    //     0xaf2edc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f228] AnonymousClosure: (0xaf2f0c), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2ee0: ldr             x1, [x1, #0x228]
    // 0xaf2ee4: stur            x0, [fp, #-8]
    // 0xaf2ee8: r0 = AllocateClosure()
    //     0xaf2ee8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2eec: mov             x1, x0
    // 0xaf2ef0: ldur            x0, [fp, #-8]
    // 0xaf2ef4: StoreField: r0->field_b = r1
    //     0xaf2ef4: stur            w1, [x0, #0xb]
    // 0xaf2ef8: LeaveFrame
    //     0xaf2ef8: mov             SP, fp
    //     0xaf2efc: ldp             fp, lr, [SP], #0x10
    // 0xaf2f00: ret
    //     0xaf2f00: ret             
    // 0xaf2f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2f04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2f08: b               #0xaf2e58
  }
  [closure] NCircleButton <anonymous closure>(dynamic) {
    // ** addr: 0xaf2f0c, size: 0xc8
    // 0xaf2f0c: EnterFrame
    //     0xaf2f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2f10: mov             fp, SP
    // 0xaf2f14: AllocStack(0x30)
    //     0xaf2f14: sub             SP, SP, #0x30
    // 0xaf2f18: SetupParameters()
    //     0xaf2f18: ldr             x0, [fp, #0x10]
    //     0xaf2f1c: ldur            w2, [x0, #0x17]
    //     0xaf2f20: add             x2, x2, HEAP, lsl #32
    //     0xaf2f24: stur            x2, [fp, #-0x10]
    // 0xaf2f28: CheckStackOverflow
    //     0xaf2f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2f2c: cmp             SP, x16
    //     0xaf2f30: b.ls            #0xaf2fcc
    // 0xaf2f34: LoadField: r0 = r2->field_f
    //     0xaf2f34: ldur            w0, [x2, #0xf]
    // 0xaf2f38: DecompressPointer r0
    //     0xaf2f38: add             x0, x0, HEAP, lsl #32
    // 0xaf2f3c: stur            x0, [fp, #-8]
    // 0xaf2f40: LoadField: r1 = r2->field_b
    //     0xaf2f40: ldur            w1, [x2, #0xb]
    // 0xaf2f44: DecompressPointer r1
    //     0xaf2f44: add             x1, x1, HEAP, lsl #32
    // 0xaf2f48: LoadField: r3 = r1->field_f
    //     0xaf2f48: ldur            w3, [x1, #0xf]
    // 0xaf2f4c: DecompressPointer r3
    //     0xaf2f4c: add             x3, x3, HEAP, lsl #32
    // 0xaf2f50: mov             x1, x3
    // 0xaf2f54: r0 = controller()
    //     0xaf2f54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf2f58: LoadField: r1 = r0->field_57
    //     0xaf2f58: ldur            w1, [x0, #0x57]
    // 0xaf2f5c: DecompressPointer r1
    //     0xaf2f5c: add             x1, x1, HEAP, lsl #32
    // 0xaf2f60: r0 = value()
    //     0xaf2f60: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf2f64: ldur            x1, [fp, #-8]
    // 0xaf2f68: r2 = LoadClassIdInstr(r1)
    //     0xaf2f68: ldur            x2, [x1, #-1]
    //     0xaf2f6c: ubfx            x2, x2, #0xc, #0x14
    // 0xaf2f70: stp             x0, x1, [SP]
    // 0xaf2f74: mov             x0, x2
    // 0xaf2f78: mov             lr, x0
    // 0xaf2f7c: ldr             lr, [x21, lr, lsl #3]
    // 0xaf2f80: blr             lr
    // 0xaf2f84: stur            x0, [fp, #-0x18]
    // 0xaf2f88: r0 = NCircleButton()
    //     0xaf2f88: bl              #0xaf2fd4  ; AllocateNCircleButtonStub -> NCircleButton (size=0x18)
    // 0xaf2f8c: mov             x3, x0
    // 0xaf2f90: ldur            x0, [fp, #-8]
    // 0xaf2f94: stur            x3, [fp, #-0x20]
    // 0xaf2f98: StoreField: r3->field_b = r0
    //     0xaf2f98: stur            w0, [x3, #0xb]
    // 0xaf2f9c: ldur            x2, [fp, #-0x10]
    // 0xaf2fa0: r1 = Function '<anonymous closure>':.
    //     0xaf2fa0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f230] AnonymousClosure: (0xaf2fe0), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/views/encyclopedia_list_view.dart] EncyclopediaListView::build (0xaf1fdc)
    //     0xaf2fa4: ldr             x1, [x1, #0x230]
    // 0xaf2fa8: r0 = AllocateClosure()
    //     0xaf2fa8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf2fac: mov             x1, x0
    // 0xaf2fb0: ldur            x0, [fp, #-0x20]
    // 0xaf2fb4: StoreField: r0->field_f = r1
    //     0xaf2fb4: stur            w1, [x0, #0xf]
    // 0xaf2fb8: ldur            x1, [fp, #-0x18]
    // 0xaf2fbc: StoreField: r0->field_13 = r1
    //     0xaf2fbc: stur            w1, [x0, #0x13]
    // 0xaf2fc0: LeaveFrame
    //     0xaf2fc0: mov             SP, fp
    //     0xaf2fc4: ldp             fp, lr, [SP], #0x10
    // 0xaf2fc8: ret
    //     0xaf2fc8: ret             
    // 0xaf2fcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2fcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2fd0: b               #0xaf2f34
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf2fe0, size: 0x74
    // 0xaf2fe0: EnterFrame
    //     0xaf2fe0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2fe4: mov             fp, SP
    // 0xaf2fe8: AllocStack(0x10)
    //     0xaf2fe8: sub             SP, SP, #0x10
    // 0xaf2fec: SetupParameters()
    //     0xaf2fec: ldr             x0, [fp, #0x10]
    //     0xaf2ff0: ldur            w2, [x0, #0x17]
    //     0xaf2ff4: add             x2, x2, HEAP, lsl #32
    //     0xaf2ff8: stur            x2, [fp, #-8]
    // 0xaf2ffc: CheckStackOverflow
    //     0xaf2ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3000: cmp             SP, x16
    //     0xaf3004: b.ls            #0xaf304c
    // 0xaf3008: LoadField: r0 = r2->field_b
    //     0xaf3008: ldur            w0, [x2, #0xb]
    // 0xaf300c: DecompressPointer r0
    //     0xaf300c: add             x0, x0, HEAP, lsl #32
    // 0xaf3010: LoadField: r1 = r0->field_f
    //     0xaf3010: ldur            w1, [x0, #0xf]
    // 0xaf3014: DecompressPointer r1
    //     0xaf3014: add             x1, x1, HEAP, lsl #32
    // 0xaf3018: r0 = controller()
    //     0xaf3018: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf301c: LoadField: r1 = r0->field_57
    //     0xaf301c: ldur            w1, [x0, #0x57]
    // 0xaf3020: DecompressPointer r1
    //     0xaf3020: add             x1, x1, HEAP, lsl #32
    // 0xaf3024: ldur            x0, [fp, #-8]
    // 0xaf3028: LoadField: r3 = r0->field_f
    //     0xaf3028: ldur            w3, [x0, #0xf]
    // 0xaf302c: DecompressPointer r3
    //     0xaf302c: add             x3, x3, HEAP, lsl #32
    // 0xaf3030: mov             x2, x3
    // 0xaf3034: stur            x3, [fp, #-0x10]
    // 0xaf3038: r0 = value=()
    //     0xaf3038: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf303c: ldur            x0, [fp, #-0x10]
    // 0xaf3040: LeaveFrame
    //     0xaf3040: mov             SP, fp
    //     0xaf3044: ldp             fp, lr, [SP], #0x10
    // 0xaf3048: ret
    //     0xaf3048: ret             
    // 0xaf304c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf304c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3050: b               #0xaf3008
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf3054, size: 0xc
    // 0xaf3054: r0 = Instance_SizedBox
    //     0xaf3054: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae0] Obj!SizedBox@e1e241
    //     0xaf3058: ldr             x0, [x0, #0xae0]
    // 0xaf305c: ret
    //     0xaf305c: ret             
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xaf3060, size: 0x158
    // 0xaf3060: EnterFrame
    //     0xaf3060: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3064: mov             fp, SP
    // 0xaf3068: AllocStack(0x30)
    //     0xaf3068: sub             SP, SP, #0x30
    // 0xaf306c: SetupParameters()
    //     0xaf306c: ldr             x0, [fp, #0x10]
    //     0xaf3070: ldur            w2, [x0, #0x17]
    //     0xaf3074: add             x2, x2, HEAP, lsl #32
    //     0xaf3078: stur            x2, [fp, #-8]
    // 0xaf307c: CheckStackOverflow
    //     0xaf307c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3080: cmp             SP, x16
    //     0xaf3084: b.ls            #0xaf31a4
    // 0xaf3088: LoadField: r1 = r2->field_f
    //     0xaf3088: ldur            w1, [x2, #0xf]
    // 0xaf308c: DecompressPointer r1
    //     0xaf308c: add             x1, x1, HEAP, lsl #32
    // 0xaf3090: r0 = controller()
    //     0xaf3090: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf3094: LoadField: r1 = r0->field_3f
    //     0xaf3094: ldur            w1, [x0, #0x3f]
    // 0xaf3098: DecompressPointer r1
    //     0xaf3098: add             x1, x1, HEAP, lsl #32
    // 0xaf309c: r0 = value()
    //     0xaf309c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf30a0: mov             x2, x0
    // 0xaf30a4: ldur            x0, [fp, #-8]
    // 0xaf30a8: stur            x2, [fp, #-0x10]
    // 0xaf30ac: LoadField: r1 = r0->field_f
    //     0xaf30ac: ldur            w1, [x0, #0xf]
    // 0xaf30b0: DecompressPointer r1
    //     0xaf30b0: add             x1, x1, HEAP, lsl #32
    // 0xaf30b4: r0 = controller()
    //     0xaf30b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf30b8: LoadField: r2 = r0->field_3b
    //     0xaf30b8: ldur            w2, [x0, #0x3b]
    // 0xaf30bc: DecompressPointer r2
    //     0xaf30bc: add             x2, x2, HEAP, lsl #32
    // 0xaf30c0: r16 = Sentinel
    //     0xaf30c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf30c4: cmp             w2, w16
    // 0xaf30c8: b.eq            #0xaf31ac
    // 0xaf30cc: ldur            x0, [fp, #-8]
    // 0xaf30d0: stur            x2, [fp, #-0x18]
    // 0xaf30d4: LoadField: r1 = r0->field_f
    //     0xaf30d4: ldur            w1, [x0, #0xf]
    // 0xaf30d8: DecompressPointer r1
    //     0xaf30d8: add             x1, x1, HEAP, lsl #32
    // 0xaf30dc: r0 = controller()
    //     0xaf30dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf30e0: mov             x2, x0
    // 0xaf30e4: ldur            x0, [fp, #-8]
    // 0xaf30e8: stur            x2, [fp, #-0x20]
    // 0xaf30ec: LoadField: r1 = r0->field_f
    //     0xaf30ec: ldur            w1, [x0, #0xf]
    // 0xaf30f0: DecompressPointer r1
    //     0xaf30f0: add             x1, x1, HEAP, lsl #32
    // 0xaf30f4: r0 = controller()
    //     0xaf30f4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf30f8: mov             x2, x0
    // 0xaf30fc: ldur            x0, [fp, #-8]
    // 0xaf3100: stur            x2, [fp, #-0x28]
    // 0xaf3104: LoadField: r1 = r0->field_f
    //     0xaf3104: ldur            w1, [x0, #0xf]
    // 0xaf3108: DecompressPointer r1
    //     0xaf3108: add             x1, x1, HEAP, lsl #32
    // 0xaf310c: r0 = controller()
    //     0xaf310c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf3110: stur            x0, [fp, #-8]
    // 0xaf3114: r0 = NSearchTextField()
    //     0xaf3114: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xaf3118: mov             x3, x0
    // 0xaf311c: ldur            x0, [fp, #-0x18]
    // 0xaf3120: stur            x3, [fp, #-0x30]
    // 0xaf3124: StoreField: r3->field_f = r0
    //     0xaf3124: stur            w0, [x3, #0xf]
    // 0xaf3128: ldur            x0, [fp, #-0x10]
    // 0xaf312c: StoreField: r3->field_b = r0
    //     0xaf312c: stur            w0, [x3, #0xb]
    // 0xaf3130: ldur            x2, [fp, #-0x20]
    // 0xaf3134: r1 = Function 'onQueryChanged':.
    //     0xaf3134: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f238] AnonymousClosure: (0xaf32c0), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] _EncyclopediaListController&OfflineFirstController&PagingMixin&SearchMixin::onQueryChanged (0xaf32fc)
    //     0xaf3138: ldr             x1, [x1, #0x238]
    // 0xaf313c: r0 = AllocateClosure()
    //     0xaf313c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3140: mov             x1, x0
    // 0xaf3144: ldur            x0, [fp, #-0x30]
    // 0xaf3148: StoreField: r0->field_13 = r1
    //     0xaf3148: stur            w1, [x0, #0x13]
    // 0xaf314c: ldur            x2, [fp, #-0x28]
    // 0xaf3150: r1 = Function 'onSearchCanceled':.
    //     0xaf3150: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f240] AnonymousClosure: (0xaf3200), in [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController::onSearchCanceled (0xaf3238)
    //     0xaf3154: ldr             x1, [x1, #0x240]
    // 0xaf3158: r0 = AllocateClosure()
    //     0xaf3158: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf315c: mov             x1, x0
    // 0xaf3160: ldur            x0, [fp, #-0x30]
    // 0xaf3164: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf3164: stur            w1, [x0, #0x17]
    // 0xaf3168: r1 = "Cari"
    //     0xaf3168: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac20] "Cari"
    //     0xaf316c: ldr             x1, [x1, #0xc20]
    // 0xaf3170: StoreField: r0->field_23 = r1
    //     0xaf3170: stur            w1, [x0, #0x23]
    // 0xaf3174: ldur            x2, [fp, #-8]
    // 0xaf3178: r1 = Function 'onSearchSubmitted':.
    //     0xaf3178: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f248] AnonymousClosure: (0xaf31b8), of [package:nuonline/app/modules/encyclopedia/encyclopedia_list/controllers/encyclopedia_list_controller.dart] EncyclopediaListController
    //     0xaf317c: ldr             x1, [x1, #0x248]
    // 0xaf3180: r0 = AllocateClosure()
    //     0xaf3180: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3184: mov             x1, x0
    // 0xaf3188: ldur            x0, [fp, #-0x30]
    // 0xaf318c: StoreField: r0->field_1b = r1
    //     0xaf318c: stur            w1, [x0, #0x1b]
    // 0xaf3190: r1 = false
    //     0xaf3190: add             x1, NULL, #0x30  ; false
    // 0xaf3194: StoreField: r0->field_27 = r1
    //     0xaf3194: stur            w1, [x0, #0x27]
    // 0xaf3198: LeaveFrame
    //     0xaf3198: mov             SP, fp
    //     0xaf319c: ldp             fp, lr, [SP], #0x10
    // 0xaf31a0: ret
    //     0xaf31a0: ret             
    // 0xaf31a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf31a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf31a8: b               #0xaf3088
    // 0xaf31ac: r9 = searchController
    //     0xaf31ac: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f250] Field <_EncyclopediaListController&OfflineFirstController&PagingMixin&<EMAIL>>: late (offset: 0x3c)
    //     0xaf31b0: ldr             x9, [x9, #0x250]
    // 0xaf31b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf31b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
