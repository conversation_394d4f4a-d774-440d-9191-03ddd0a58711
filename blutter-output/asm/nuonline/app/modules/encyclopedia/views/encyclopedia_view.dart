// lib: , url: package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart

// class id: 1050267, size: 0x8
class :: {
}

// class id: 5278, size: 0x14, field offset: 0x14
class EncyclopediaView extends GetView<dynamic> {

  [closure] NEmptyState <anonymous closure>(dynamic, String?) {
    // ** addr: 0xad9d70, size: 0xd4
    // 0xad9d70: EnterFrame
    //     0xad9d70: stp             fp, lr, [SP, #-0x10]!
    //     0xad9d74: mov             fp, SP
    // 0xad9d78: AllocStack(0x18)
    //     0xad9d78: sub             SP, SP, #0x18
    // 0xad9d7c: SetupParameters()
    //     0xad9d7c: ldr             x0, [fp, #0x18]
    //     0xad9d80: ldur            w1, [x0, #0x17]
    //     0xad9d84: add             x1, x1, HEAP, lsl #32
    //     0xad9d88: stur            x1, [fp, #-8]
    // 0xad9d8c: CheckStackOverflow
    //     0xad9d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9d90: cmp             SP, x16
    //     0xad9d94: b.ls            #0xad9e3c
    // 0xad9d98: r16 = "ApiError.notFound"
    //     0xad9d98: add             x16, PP, #0x29, lsl #12  ; [pp+0x29730] "ApiError.notFound"
    //     0xad9d9c: ldr             x16, [x16, #0x730]
    // 0xad9da0: ldr             lr, [fp, #0x10]
    // 0xad9da4: stp             lr, x16, [SP]
    // 0xad9da8: r0 = ==()
    //     0xad9da8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xad9dac: tbz             w0, #4, #0xad9e00
    // 0xad9db0: r16 = "ApiError.noConnection"
    //     0xad9db0: add             x16, PP, #0x29, lsl #12  ; [pp+0x29738] "ApiError.noConnection"
    //     0xad9db4: ldr             x16, [x16, #0x738]
    // 0xad9db8: ldr             lr, [fp, #0x10]
    // 0xad9dbc: stp             lr, x16, [SP]
    // 0xad9dc0: r0 = ==()
    //     0xad9dc0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xad9dc4: tbnz            w0, #4, #0xad9e00
    // 0xad9dc8: ldur            x0, [fp, #-8]
    // 0xad9dcc: LoadField: r1 = r0->field_f
    //     0xad9dcc: ldur            w1, [x0, #0xf]
    // 0xad9dd0: DecompressPointer r1
    //     0xad9dd0: add             x1, x1, HEAP, lsl #32
    // 0xad9dd4: r0 = controller()
    //     0xad9dd4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad9dd8: mov             x2, x0
    // 0xad9ddc: r1 = Function 'onRefresh':.
    //     0xad9ddc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f130] AnonymousClosure: (0xada87c), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xad9de0: ldr             x1, [x1, #0x130]
    // 0xad9de4: r0 = AllocateClosure()
    //     0xad9de4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad9de8: mov             x2, x0
    // 0xad9dec: r1 = Null
    //     0xad9dec: mov             x1, NULL
    // 0xad9df0: r0 = NEmptyState.notConnection()
    //     0xad9df0: bl              #0xad9f3c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.notConnection
    // 0xad9df4: LeaveFrame
    //     0xad9df4: mov             SP, fp
    //     0xad9df8: ldp             fp, lr, [SP], #0x10
    // 0xad9dfc: ret
    //     0xad9dfc: ret             
    // 0xad9e00: r0 = NEmptyState()
    //     0xad9e00: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xad9e04: mov             x1, x0
    // 0xad9e08: r2 = "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xad9e08: add             x2, PP, #0x29, lsl #12  ; [pp+0x297e8] "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xad9e0c: ldr             x2, [x2, #0x7e8]
    // 0xad9e10: r3 = "assets/images/illustration/no_search.svg"
    //     0xad9e10: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xad9e14: ldr             x3, [x3, #0x138]
    // 0xad9e18: r5 = "Halaman Tidak Ditemukan"
    //     0xad9e18: add             x5, PP, #0x29, lsl #12  ; [pp+0x292c8] "Halaman Tidak Ditemukan"
    //     0xad9e1c: ldr             x5, [x5, #0x2c8]
    // 0xad9e20: stur            x0, [fp, #-8]
    // 0xad9e24: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xad9e24: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xad9e28: r0 = NEmptyState.svg()
    //     0xad9e28: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xad9e2c: ldur            x0, [fp, #-8]
    // 0xad9e30: LeaveFrame
    //     0xad9e30: mov             SP, fp
    //     0xad9e34: ldp             fp, lr, [SP], #0x10
    // 0xad9e38: ret
    //     0xad9e38: ret             
    // 0xad9e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9e3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9e40: b               #0xad9d98
  }
  _ build(/* No info */) {
    // ** addr: 0xaf3348, size: 0x448
    // 0xaf3348: EnterFrame
    //     0xaf3348: stp             fp, lr, [SP, #-0x10]!
    //     0xaf334c: mov             fp, SP
    // 0xaf3350: AllocStack(0x70)
    //     0xaf3350: sub             SP, SP, #0x70
    // 0xaf3354: SetupParameters(EncyclopediaView this /* r1 => r1, fp-0x8 */)
    //     0xaf3354: stur            x1, [fp, #-8]
    // 0xaf3358: CheckStackOverflow
    //     0xaf3358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf335c: cmp             SP, x16
    //     0xaf3360: b.ls            #0xaf3788
    // 0xaf3364: r1 = 1
    //     0xaf3364: movz            x1, #0x1
    // 0xaf3368: r0 = AllocateContext()
    //     0xaf3368: bl              #0xec126c  ; AllocateContextStub
    // 0xaf336c: mov             x3, x0
    // 0xaf3370: ldur            x0, [fp, #-8]
    // 0xaf3374: stur            x3, [fp, #-0x10]
    // 0xaf3378: StoreField: r3->field_f = r0
    //     0xaf3378: stur            w0, [x3, #0xf]
    // 0xaf337c: r1 = Function '<anonymous closure>':.
    //     0xaf337c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0f8] AnonymousClosure: (0xaf40e8), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf3380: ldr             x1, [x1, #0xf8]
    // 0xaf3384: r2 = Null
    //     0xaf3384: mov             x2, NULL
    // 0xaf3388: r0 = AllocateClosure()
    //     0xaf3388: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf338c: stur            x0, [fp, #-0x18]
    // 0xaf3390: r0 = IconButton()
    //     0xaf3390: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xaf3394: mov             x3, x0
    // 0xaf3398: ldur            x0, [fp, #-0x18]
    // 0xaf339c: stur            x3, [fp, #-0x20]
    // 0xaf33a0: StoreField: r3->field_3b = r0
    //     0xaf33a0: stur            w0, [x3, #0x3b]
    // 0xaf33a4: r0 = false
    //     0xaf33a4: add             x0, NULL, #0x30  ; false
    // 0xaf33a8: StoreField: r3->field_47 = r0
    //     0xaf33a8: stur            w0, [x3, #0x47]
    // 0xaf33ac: r1 = Instance_Icon
    //     0xaf33ac: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f70] Obj!Icon@e24431
    //     0xaf33b0: ldr             x1, [x1, #0xf70]
    // 0xaf33b4: StoreField: r3->field_1f = r1
    //     0xaf33b4: stur            w1, [x3, #0x1f]
    // 0xaf33b8: r1 = Instance__IconButtonVariant
    //     0xaf33b8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xaf33bc: ldr             x1, [x1, #0xf78]
    // 0xaf33c0: StoreField: r3->field_63 = r1
    //     0xaf33c0: stur            w1, [x3, #0x63]
    // 0xaf33c4: r1 = Null
    //     0xaf33c4: mov             x1, NULL
    // 0xaf33c8: r2 = 2
    //     0xaf33c8: movz            x2, #0x2
    // 0xaf33cc: r0 = AllocateArray()
    //     0xaf33cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf33d0: mov             x2, x0
    // 0xaf33d4: ldur            x0, [fp, #-0x20]
    // 0xaf33d8: stur            x2, [fp, #-0x18]
    // 0xaf33dc: StoreField: r2->field_f = r0
    //     0xaf33dc: stur            w0, [x2, #0xf]
    // 0xaf33e0: r1 = <Widget>
    //     0xaf33e0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf33e4: r0 = AllocateGrowableArray()
    //     0xaf33e4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf33e8: mov             x1, x0
    // 0xaf33ec: ldur            x0, [fp, #-0x18]
    // 0xaf33f0: stur            x1, [fp, #-0x20]
    // 0xaf33f4: StoreField: r1->field_f = r0
    //     0xaf33f4: stur            w0, [x1, #0xf]
    // 0xaf33f8: r2 = 2
    //     0xaf33f8: movz            x2, #0x2
    // 0xaf33fc: StoreField: r1->field_b = r2
    //     0xaf33fc: stur            w2, [x1, #0xb]
    // 0xaf3400: r0 = AppBar()
    //     0xaf3400: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf3404: stur            x0, [fp, #-0x18]
    // 0xaf3408: r16 = Instance_Text
    //     0xaf3408: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f100] Obj!Text@e21d71
    //     0xaf340c: ldr             x16, [x16, #0x100]
    // 0xaf3410: ldur            lr, [fp, #-0x20]
    // 0xaf3414: stp             lr, x16, [SP]
    // 0xaf3418: mov             x1, x0
    // 0xaf341c: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xaf341c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xaf3420: ldr             x4, [x4, #0xf88]
    // 0xaf3424: r0 = AppBar()
    //     0xaf3424: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf3428: ldur            x1, [fp, #-8]
    // 0xaf342c: r0 = controller()
    //     0xaf342c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf3430: ldur            x1, [fp, #-8]
    // 0xaf3434: stur            x0, [fp, #-0x20]
    // 0xaf3438: r0 = controller()
    //     0xaf3438: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf343c: r1 = Function '<anonymous closure>':.
    //     0xaf343c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f108] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xaf3440: ldr             x1, [x1, #0x108]
    // 0xaf3444: r2 = Null
    //     0xaf3444: mov             x2, NULL
    // 0xaf3448: stur            x0, [fp, #-0x28]
    // 0xaf344c: r0 = AllocateClosure()
    //     0xaf344c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3450: r1 = Function '<anonymous closure>':.
    //     0xaf3450: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f110] AnonymousClosure: (0xaf40dc), in [package:nuonline/app/modules/tutorial/views/tutorial_view.dart] TutorialView::build (0xb59c7c)
    //     0xaf3454: ldr             x1, [x1, #0x110]
    // 0xaf3458: r2 = Null
    //     0xaf3458: mov             x2, NULL
    // 0xaf345c: stur            x0, [fp, #-0x30]
    // 0xaf3460: r0 = AllocateClosure()
    //     0xaf3460: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3464: stur            x0, [fp, #-0x38]
    // 0xaf3468: r0 = ListView()
    //     0xaf3468: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf346c: stur            x0, [fp, #-0x40]
    // 0xaf3470: r16 = Instance_EdgeInsets
    //     0xaf3470: add             x16, PP, #0x29, lsl #12  ; [pp+0x29708] Obj!EdgeInsets@e128b1
    //     0xaf3474: ldr             x16, [x16, #0x708]
    // 0xaf3478: str             x16, [SP]
    // 0xaf347c: mov             x1, x0
    // 0xaf3480: ldur            x2, [fp, #-0x38]
    // 0xaf3484: ldur            x5, [fp, #-0x30]
    // 0xaf3488: r3 = 5
    //     0xaf3488: movz            x3, #0x5
    // 0xaf348c: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xaf348c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xaf3490: ldr             x4, [x4, #0x700]
    // 0xaf3494: r0 = ListView.separated()
    //     0xaf3494: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf3498: r1 = Function '<anonymous closure>':.
    //     0xaf3498: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f118] AnonymousClosure: (0xaf3790), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf349c: ldr             x1, [x1, #0x118]
    // 0xaf34a0: r2 = Null
    //     0xaf34a0: mov             x2, NULL
    // 0xaf34a4: r0 = AllocateClosure()
    //     0xaf34a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf34a8: ldur            x2, [fp, #-0x10]
    // 0xaf34ac: r1 = Function '<anonymous closure>':.
    //     0xaf34ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f120] AnonymousClosure: (0xad9d70), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf34b0: ldr             x1, [x1, #0x120]
    // 0xaf34b4: stur            x0, [fp, #-0x10]
    // 0xaf34b8: r0 = AllocateClosure()
    //     0xaf34b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf34bc: r16 = <List<EncyclopediaCategory>>
    //     0xaf34bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f128] TypeArguments: <List<EncyclopediaCategory>>
    //     0xaf34c0: ldr             x16, [x16, #0x128]
    // 0xaf34c4: ldur            lr, [fp, #-0x28]
    // 0xaf34c8: stp             lr, x16, [SP, #0x18]
    // 0xaf34cc: ldur            x16, [fp, #-0x10]
    // 0xaf34d0: ldur            lr, [fp, #-0x40]
    // 0xaf34d4: stp             lr, x16, [SP, #8]
    // 0xaf34d8: str             x0, [SP]
    // 0xaf34dc: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xaf34dc: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xaf34e0: ldr             x4, [x4, #0x728]
    // 0xaf34e4: r0 = StateExt.obx()
    //     0xaf34e4: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaf34e8: stur            x0, [fp, #-0x10]
    // 0xaf34ec: r0 = ListTileTheme()
    //     0xaf34ec: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xaf34f0: mov             x1, x0
    // 0xaf34f4: r0 = Instance_EdgeInsets
    //     0xaf34f4: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf34f8: stur            x1, [fp, #-0x28]
    // 0xaf34fc: StoreField: r1->field_2b = r0
    //     0xaf34fc: stur            w0, [x1, #0x2b]
    // 0xaf3500: r0 = 0.000000
    //     0xaf3500: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xaf3504: StoreField: r1->field_3b = r0
    //     0xaf3504: stur            w0, [x1, #0x3b]
    // 0xaf3508: ldur            x0, [fp, #-0x10]
    // 0xaf350c: StoreField: r1->field_b = r0
    //     0xaf350c: stur            w0, [x1, #0xb]
    // 0xaf3510: r0 = RefreshIndicator()
    //     0xaf3510: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xaf3514: mov             x3, x0
    // 0xaf3518: ldur            x0, [fp, #-0x28]
    // 0xaf351c: stur            x3, [fp, #-0x10]
    // 0xaf3520: StoreField: r3->field_b = r0
    //     0xaf3520: stur            w0, [x3, #0xb]
    // 0xaf3524: d0 = 40.000000
    //     0xaf3524: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xaf3528: StoreField: r3->field_f = d0
    //     0xaf3528: stur            d0, [x3, #0xf]
    // 0xaf352c: ArrayStore: r3[0] = rZR  ; List_8
    //     0xaf352c: stur            xzr, [x3, #0x17]
    // 0xaf3530: ldur            x2, [fp, #-0x20]
    // 0xaf3534: r1 = Function 'onRefresh':.
    //     0xaf3534: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f130] AnonymousClosure: (0xada87c), in [package:nuonline/common/mixins/offline_first_mixin.dart] OfflineFirstNoSyncController::onRefresh (0xada8b4)
    //     0xaf3538: ldr             x1, [x1, #0x130]
    // 0xaf353c: r0 = AllocateClosure()
    //     0xaf353c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3540: mov             x1, x0
    // 0xaf3544: ldur            x0, [fp, #-0x10]
    // 0xaf3548: StoreField: r0->field_1f = r1
    //     0xaf3548: stur            w1, [x0, #0x1f]
    // 0xaf354c: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xaf354c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xaf3550: ldr             x1, [x1, #0xf58]
    // 0xaf3554: StoreField: r0->field_2f = r1
    //     0xaf3554: stur            w1, [x0, #0x2f]
    // 0xaf3558: d0 = 2.500000
    //     0xaf3558: fmov            d0, #2.50000000
    // 0xaf355c: StoreField: r0->field_3b = d0
    //     0xaf355c: stur            d0, [x0, #0x3b]
    // 0xaf3560: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xaf3560: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xaf3564: ldr             x1, [x1, #0xa68]
    // 0xaf3568: StoreField: r0->field_47 = r1
    //     0xaf3568: stur            w1, [x0, #0x47]
    // 0xaf356c: d0 = 2.000000
    //     0xaf356c: fmov            d0, #2.00000000
    // 0xaf3570: StoreField: r0->field_4b = d0
    //     0xaf3570: stur            d0, [x0, #0x4b]
    // 0xaf3574: r1 = Instance__IndicatorType
    //     0xaf3574: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xaf3578: ldr             x1, [x1, #0xa70]
    // 0xaf357c: StoreField: r0->field_43 = r1
    //     0xaf357c: stur            w1, [x0, #0x43]
    // 0xaf3580: r1 = <FlexParentData>
    //     0xaf3580: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf3584: ldr             x1, [x1, #0x720]
    // 0xaf3588: r0 = Expanded()
    //     0xaf3588: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf358c: mov             x3, x0
    // 0xaf3590: r0 = 1
    //     0xaf3590: movz            x0, #0x1
    // 0xaf3594: stur            x3, [fp, #-0x20]
    // 0xaf3598: StoreField: r3->field_13 = r0
    //     0xaf3598: stur            x0, [x3, #0x13]
    // 0xaf359c: r0 = Instance_FlexFit
    //     0xaf359c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf35a0: ldr             x0, [x0, #0x728]
    // 0xaf35a4: StoreField: r3->field_1b = r0
    //     0xaf35a4: stur            w0, [x3, #0x1b]
    // 0xaf35a8: ldur            x0, [fp, #-0x10]
    // 0xaf35ac: StoreField: r3->field_b = r0
    //     0xaf35ac: stur            w0, [x3, #0xb]
    // 0xaf35b0: r1 = Null
    //     0xaf35b0: mov             x1, NULL
    // 0xaf35b4: r2 = 2
    //     0xaf35b4: movz            x2, #0x2
    // 0xaf35b8: r0 = AllocateArray()
    //     0xaf35b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf35bc: mov             x2, x0
    // 0xaf35c0: ldur            x0, [fp, #-0x20]
    // 0xaf35c4: stur            x2, [fp, #-0x10]
    // 0xaf35c8: StoreField: r2->field_f = r0
    //     0xaf35c8: stur            w0, [x2, #0xf]
    // 0xaf35cc: r1 = <Widget>
    //     0xaf35cc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf35d0: r0 = AllocateGrowableArray()
    //     0xaf35d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf35d4: mov             x2, x0
    // 0xaf35d8: ldur            x0, [fp, #-0x10]
    // 0xaf35dc: stur            x2, [fp, #-0x20]
    // 0xaf35e0: StoreField: r2->field_f = r0
    //     0xaf35e0: stur            w0, [x2, #0xf]
    // 0xaf35e4: r0 = 2
    //     0xaf35e4: movz            x0, #0x2
    // 0xaf35e8: StoreField: r2->field_b = r0
    //     0xaf35e8: stur            w0, [x2, #0xb]
    // 0xaf35ec: ldur            x1, [fp, #-8]
    // 0xaf35f0: r0 = controller()
    //     0xaf35f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf35f4: LoadField: r1 = r0->field_33
    //     0xaf35f4: ldur            w1, [x0, #0x33]
    // 0xaf35f8: DecompressPointer r1
    //     0xaf35f8: add             x1, x1, HEAP, lsl #32
    // 0xaf35fc: r0 = _adsVisibility()
    //     0xaf35fc: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xaf3600: mov             x2, x0
    // 0xaf3604: r1 = Null
    //     0xaf3604: mov             x1, NULL
    // 0xaf3608: r0 = AdsConfig.fromJson()
    //     0xaf3608: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xaf360c: LoadField: r1 = r0->field_1f
    //     0xaf360c: ldur            w1, [x0, #0x1f]
    // 0xaf3610: DecompressPointer r1
    //     0xaf3610: add             x1, x1, HEAP, lsl #32
    // 0xaf3614: LoadField: r0 = r1->field_7
    //     0xaf3614: ldur            w0, [x1, #7]
    // 0xaf3618: DecompressPointer r0
    //     0xaf3618: add             x0, x0, HEAP, lsl #32
    // 0xaf361c: tbnz            w0, #4, #0xaf36d4
    // 0xaf3620: ldur            x0, [fp, #-0x20]
    // 0xaf3624: ldur            x1, [fp, #-8]
    // 0xaf3628: r0 = controller()
    //     0xaf3628: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf362c: LoadField: r1 = r0->field_33
    //     0xaf362c: ldur            w1, [x0, #0x33]
    // 0xaf3630: DecompressPointer r1
    //     0xaf3630: add             x1, x1, HEAP, lsl #32
    // 0xaf3634: r0 = nupediaBanner()
    //     0xaf3634: bl              #0xaf25a0  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::nupediaBanner
    // 0xaf3638: LoadField: r1 = r0->field_f
    //     0xaf3638: ldur            w1, [x0, #0xf]
    // 0xaf363c: DecompressPointer r1
    //     0xaf363c: add             x1, x1, HEAP, lsl #32
    // 0xaf3640: stur            x1, [fp, #-8]
    // 0xaf3644: r0 = AdmobBannerWidget()
    //     0xaf3644: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xaf3648: mov             x2, x0
    // 0xaf364c: ldur            x0, [fp, #-8]
    // 0xaf3650: stur            x2, [fp, #-0x10]
    // 0xaf3654: StoreField: r2->field_b = r0
    //     0xaf3654: stur            w0, [x2, #0xb]
    // 0xaf3658: ldur            x0, [fp, #-0x20]
    // 0xaf365c: LoadField: r1 = r0->field_b
    //     0xaf365c: ldur            w1, [x0, #0xb]
    // 0xaf3660: LoadField: r3 = r0->field_f
    //     0xaf3660: ldur            w3, [x0, #0xf]
    // 0xaf3664: DecompressPointer r3
    //     0xaf3664: add             x3, x3, HEAP, lsl #32
    // 0xaf3668: LoadField: r4 = r3->field_b
    //     0xaf3668: ldur            w4, [x3, #0xb]
    // 0xaf366c: r3 = LoadInt32Instr(r1)
    //     0xaf366c: sbfx            x3, x1, #1, #0x1f
    // 0xaf3670: stur            x3, [fp, #-0x48]
    // 0xaf3674: r1 = LoadInt32Instr(r4)
    //     0xaf3674: sbfx            x1, x4, #1, #0x1f
    // 0xaf3678: cmp             x3, x1
    // 0xaf367c: b.ne            #0xaf3688
    // 0xaf3680: mov             x1, x0
    // 0xaf3684: r0 = _growToNextCapacity()
    //     0xaf3684: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf3688: ldur            x2, [fp, #-0x20]
    // 0xaf368c: ldur            x3, [fp, #-0x48]
    // 0xaf3690: add             x0, x3, #1
    // 0xaf3694: lsl             x1, x0, #1
    // 0xaf3698: StoreField: r2->field_b = r1
    //     0xaf3698: stur            w1, [x2, #0xb]
    // 0xaf369c: LoadField: r1 = r2->field_f
    //     0xaf369c: ldur            w1, [x2, #0xf]
    // 0xaf36a0: DecompressPointer r1
    //     0xaf36a0: add             x1, x1, HEAP, lsl #32
    // 0xaf36a4: ldur            x0, [fp, #-0x10]
    // 0xaf36a8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf36a8: add             x25, x1, x3, lsl #2
    //     0xaf36ac: add             x25, x25, #0xf
    //     0xaf36b0: str             w0, [x25]
    //     0xaf36b4: tbz             w0, #0, #0xaf36d0
    //     0xaf36b8: ldurb           w16, [x1, #-1]
    //     0xaf36bc: ldurb           w17, [x0, #-1]
    //     0xaf36c0: and             x16, x17, x16, lsr #2
    //     0xaf36c4: tst             x16, HEAP, lsr #32
    //     0xaf36c8: b.eq            #0xaf36d0
    //     0xaf36cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf36d0: b               #0xaf36d8
    // 0xaf36d4: ldur            x2, [fp, #-0x20]
    // 0xaf36d8: ldur            x0, [fp, #-0x18]
    // 0xaf36dc: r0 = Column()
    //     0xaf36dc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf36e0: mov             x1, x0
    // 0xaf36e4: r0 = Instance_Axis
    //     0xaf36e4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf36e8: stur            x1, [fp, #-8]
    // 0xaf36ec: StoreField: r1->field_f = r0
    //     0xaf36ec: stur            w0, [x1, #0xf]
    // 0xaf36f0: r0 = Instance_MainAxisAlignment
    //     0xaf36f0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf36f4: ldr             x0, [x0, #0x730]
    // 0xaf36f8: StoreField: r1->field_13 = r0
    //     0xaf36f8: stur            w0, [x1, #0x13]
    // 0xaf36fc: r0 = Instance_MainAxisSize
    //     0xaf36fc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf3700: ldr             x0, [x0, #0x738]
    // 0xaf3704: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf3704: stur            w0, [x1, #0x17]
    // 0xaf3708: r0 = Instance_CrossAxisAlignment
    //     0xaf3708: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf370c: ldr             x0, [x0, #0x740]
    // 0xaf3710: StoreField: r1->field_1b = r0
    //     0xaf3710: stur            w0, [x1, #0x1b]
    // 0xaf3714: r0 = Instance_VerticalDirection
    //     0xaf3714: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf3718: ldr             x0, [x0, #0x748]
    // 0xaf371c: StoreField: r1->field_23 = r0
    //     0xaf371c: stur            w0, [x1, #0x23]
    // 0xaf3720: r0 = Instance_Clip
    //     0xaf3720: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf3724: ldr             x0, [x0, #0x750]
    // 0xaf3728: StoreField: r1->field_2b = r0
    //     0xaf3728: stur            w0, [x1, #0x2b]
    // 0xaf372c: StoreField: r1->field_2f = rZR
    //     0xaf372c: stur            xzr, [x1, #0x2f]
    // 0xaf3730: ldur            x0, [fp, #-0x20]
    // 0xaf3734: StoreField: r1->field_b = r0
    //     0xaf3734: stur            w0, [x1, #0xb]
    // 0xaf3738: r0 = Scaffold()
    //     0xaf3738: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf373c: ldur            x1, [fp, #-0x18]
    // 0xaf3740: StoreField: r0->field_13 = r1
    //     0xaf3740: stur            w1, [x0, #0x13]
    // 0xaf3744: ldur            x1, [fp, #-8]
    // 0xaf3748: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf3748: stur            w1, [x0, #0x17]
    // 0xaf374c: r1 = Instance_AlignmentDirectional
    //     0xaf374c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf3750: ldr             x1, [x1, #0x758]
    // 0xaf3754: StoreField: r0->field_2b = r1
    //     0xaf3754: stur            w1, [x0, #0x2b]
    // 0xaf3758: r1 = true
    //     0xaf3758: add             x1, NULL, #0x20  ; true
    // 0xaf375c: StoreField: r0->field_53 = r1
    //     0xaf375c: stur            w1, [x0, #0x53]
    // 0xaf3760: r2 = Instance_DragStartBehavior
    //     0xaf3760: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf3764: StoreField: r0->field_57 = r2
    //     0xaf3764: stur            w2, [x0, #0x57]
    // 0xaf3768: r2 = false
    //     0xaf3768: add             x2, NULL, #0x30  ; false
    // 0xaf376c: StoreField: r0->field_b = r2
    //     0xaf376c: stur            w2, [x0, #0xb]
    // 0xaf3770: StoreField: r0->field_f = r2
    //     0xaf3770: stur            w2, [x0, #0xf]
    // 0xaf3774: StoreField: r0->field_5f = r1
    //     0xaf3774: stur            w1, [x0, #0x5f]
    // 0xaf3778: StoreField: r0->field_63 = r1
    //     0xaf3778: stur            w1, [x0, #0x63]
    // 0xaf377c: LeaveFrame
    //     0xaf377c: mov             SP, fp
    //     0xaf3780: ldp             fp, lr, [SP], #0x10
    // 0xaf3784: ret
    //     0xaf3784: ret             
    // 0xaf3788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf378c: b               #0xaf3364
  }
  [closure] ListView <anonymous closure>(dynamic, List<EncyclopediaCategory>?) {
    // ** addr: 0xaf3790, size: 0xf4
    // 0xaf3790: EnterFrame
    //     0xaf3790: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3794: mov             fp, SP
    // 0xaf3798: AllocStack(0x28)
    //     0xaf3798: sub             SP, SP, #0x28
    // 0xaf379c: SetupParameters()
    //     0xaf379c: ldr             x0, [fp, #0x18]
    //     0xaf37a0: ldur            w1, [x0, #0x17]
    //     0xaf37a4: add             x1, x1, HEAP, lsl #32
    //     0xaf37a8: stur            x1, [fp, #-8]
    // 0xaf37ac: CheckStackOverflow
    //     0xaf37ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf37b0: cmp             SP, x16
    //     0xaf37b4: b.ls            #0xaf3878
    // 0xaf37b8: r1 = 1
    //     0xaf37b8: movz            x1, #0x1
    // 0xaf37bc: r0 = AllocateContext()
    //     0xaf37bc: bl              #0xec126c  ; AllocateContextStub
    // 0xaf37c0: mov             x1, x0
    // 0xaf37c4: ldur            x0, [fp, #-8]
    // 0xaf37c8: stur            x1, [fp, #-0x10]
    // 0xaf37cc: StoreField: r1->field_b = r0
    //     0xaf37cc: stur            w0, [x1, #0xb]
    // 0xaf37d0: ldr             x0, [fp, #0x10]
    // 0xaf37d4: StoreField: r1->field_f = r0
    //     0xaf37d4: stur            w0, [x1, #0xf]
    // 0xaf37d8: cmp             w0, NULL
    // 0xaf37dc: b.eq            #0xaf3880
    // 0xaf37e0: r2 = LoadClassIdInstr(r0)
    //     0xaf37e0: ldur            x2, [x0, #-1]
    //     0xaf37e4: ubfx            x2, x2, #0xc, #0x14
    // 0xaf37e8: str             x0, [SP]
    // 0xaf37ec: mov             x0, x2
    // 0xaf37f0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf37f0: movz            x17, #0xc834
    //     0xaf37f4: add             lr, x0, x17
    //     0xaf37f8: ldr             lr, [x21, lr, lsl #3]
    //     0xaf37fc: blr             lr
    // 0xaf3800: r1 = LoadInt32Instr(r0)
    //     0xaf3800: sbfx            x1, x0, #1, #0x1f
    //     0xaf3804: tbz             w0, #0, #0xaf380c
    //     0xaf3808: ldur            x1, [x0, #7]
    // 0xaf380c: add             x3, x1, #1
    // 0xaf3810: stur            x3, [fp, #-0x18]
    // 0xaf3814: r1 = Function '<anonymous closure>':.
    //     0xaf3814: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] AnonymousClosure: (0xaf3ca0), in [package:nuonline/app/modules/tutorial/views/tutorial_view.dart] TutorialView::build (0xb59c7c)
    //     0xaf3818: ldr             x1, [x1, #0x150]
    // 0xaf381c: r2 = Null
    //     0xaf381c: mov             x2, NULL
    // 0xaf3820: r0 = AllocateClosure()
    //     0xaf3820: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3824: ldur            x2, [fp, #-0x10]
    // 0xaf3828: r1 = Function '<anonymous closure>':.
    //     0xaf3828: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f158] AnonymousClosure: (0xaf3884), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf382c: ldr             x1, [x1, #0x158]
    // 0xaf3830: stur            x0, [fp, #-8]
    // 0xaf3834: r0 = AllocateClosure()
    //     0xaf3834: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3838: stur            x0, [fp, #-0x10]
    // 0xaf383c: r0 = ListView()
    //     0xaf383c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf3840: stur            x0, [fp, #-0x20]
    // 0xaf3844: r16 = Instance_EdgeInsets
    //     0xaf3844: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xaf3848: str             x16, [SP]
    // 0xaf384c: mov             x1, x0
    // 0xaf3850: ldur            x2, [fp, #-0x10]
    // 0xaf3854: ldur            x3, [fp, #-0x18]
    // 0xaf3858: ldur            x5, [fp, #-8]
    // 0xaf385c: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xaf385c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xaf3860: ldr             x4, [x4, #0x700]
    // 0xaf3864: r0 = ListView.separated()
    //     0xaf3864: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf3868: ldur            x0, [fp, #-0x20]
    // 0xaf386c: LeaveFrame
    //     0xaf386c: mov             SP, fp
    //     0xaf3870: ldp             fp, lr, [SP], #0x10
    // 0xaf3874: ret
    //     0xaf3874: ret             
    // 0xaf3878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf387c: b               #0xaf37b8
    // 0xaf3880: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3880: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] NCardListNumber <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf3884, size: 0x290
    // 0xaf3884: EnterFrame
    //     0xaf3884: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3888: mov             fp, SP
    // 0xaf388c: AllocStack(0x58)
    //     0xaf388c: sub             SP, SP, #0x58
    // 0xaf3890: SetupParameters()
    //     0xaf3890: ldr             x0, [fp, #0x20]
    //     0xaf3894: ldur            w1, [x0, #0x17]
    //     0xaf3898: add             x1, x1, HEAP, lsl #32
    //     0xaf389c: stur            x1, [fp, #-8]
    // 0xaf38a0: CheckStackOverflow
    //     0xaf38a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf38a4: cmp             SP, x16
    //     0xaf38a8: b.ls            #0xaf3b0c
    // 0xaf38ac: r1 = 1
    //     0xaf38ac: movz            x1, #0x1
    // 0xaf38b0: r0 = AllocateContext()
    //     0xaf38b0: bl              #0xec126c  ; AllocateContextStub
    // 0xaf38b4: mov             x2, x0
    // 0xaf38b8: ldur            x0, [fp, #-8]
    // 0xaf38bc: stur            x2, [fp, #-0x28]
    // 0xaf38c0: StoreField: r2->field_b = r0
    //     0xaf38c0: stur            w0, [x2, #0xb]
    // 0xaf38c4: ldr             x1, [fp, #0x10]
    // 0xaf38c8: r3 = LoadInt32Instr(r1)
    //     0xaf38c8: sbfx            x3, x1, #1, #0x1f
    //     0xaf38cc: tbz             w1, #0, #0xaf38d4
    //     0xaf38d0: ldur            x3, [x1, #7]
    // 0xaf38d4: stur            x3, [fp, #-0x20]
    // 0xaf38d8: cbnz            x3, #0xaf39c0
    // 0xaf38dc: LoadField: r3 = r0->field_f
    //     0xaf38dc: ldur            w3, [x0, #0xf]
    // 0xaf38e0: DecompressPointer r3
    //     0xaf38e0: add             x3, x3, HEAP, lsl #32
    // 0xaf38e4: stur            x3, [fp, #-0x10]
    // 0xaf38e8: r1 = Function '<anonymous closure>':.
    //     0xaf38e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f160] AnonymousClosure: (0xaf3c5c), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf38ec: ldr             x1, [x1, #0x160]
    // 0xaf38f0: r2 = Null
    //     0xaf38f0: mov             x2, NULL
    // 0xaf38f4: r0 = AllocateClosure()
    //     0xaf38f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf38f8: mov             x1, x0
    // 0xaf38fc: ldur            x0, [fp, #-0x10]
    // 0xaf3900: r2 = LoadClassIdInstr(r0)
    //     0xaf3900: ldur            x2, [x0, #-1]
    //     0xaf3904: ubfx            x2, x2, #0xc, #0x14
    // 0xaf3908: r16 = <int>
    //     0xaf3908: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaf390c: stp             x0, x16, [SP, #0x10]
    // 0xaf3910: stp             x1, xzr, [SP]
    // 0xaf3914: mov             x0, x2
    // 0xaf3918: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xaf3918: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xaf391c: r0 = GDT[cid_x0 + 0xed1f]()
    //     0xaf391c: movz            x17, #0xed1f
    //     0xaf3920: add             lr, x0, x17
    //     0xaf3924: ldr             lr, [x21, lr, lsl #3]
    //     0xaf3928: blr             lr
    // 0xaf392c: r1 = Null
    //     0xaf392c: mov             x1, NULL
    // 0xaf3930: r2 = 4
    //     0xaf3930: movz            x2, #0x4
    // 0xaf3934: stur            x0, [fp, #-0x10]
    // 0xaf3938: r0 = AllocateArray()
    //     0xaf3938: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf393c: mov             x1, x0
    // 0xaf3940: ldur            x0, [fp, #-0x10]
    // 0xaf3944: StoreField: r1->field_f = r0
    //     0xaf3944: stur            w0, [x1, #0xf]
    // 0xaf3948: r16 = " Data"
    //     0xaf3948: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f170] " Data"
    //     0xaf394c: ldr             x16, [x16, #0x170]
    // 0xaf3950: StoreField: r1->field_13 = r16
    //     0xaf3950: stur            w16, [x1, #0x13]
    // 0xaf3954: str             x1, [SP]
    // 0xaf3958: r0 = _interpolate()
    //     0xaf3958: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf395c: stur            x0, [fp, #-0x10]
    // 0xaf3960: r0 = NCardListNumber()
    //     0xaf3960: bl              #0xaf3b14  ; AllocateNCardListNumberStub -> NCardListNumber (size=0x30)
    // 0xaf3964: mov             x3, x0
    // 0xaf3968: r0 = 2
    //     0xaf3968: movz            x0, #0x2
    // 0xaf396c: stur            x3, [fp, #-0x18]
    // 0xaf3970: StoreField: r3->field_b = r0
    //     0xaf3970: stur            w0, [x3, #0xb]
    // 0xaf3974: r0 = "Semua Ensiklopedia"
    //     0xaf3974: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f178] "Semua Ensiklopedia"
    //     0xaf3978: ldr             x0, [x0, #0x178]
    // 0xaf397c: StoreField: r3->field_f = r0
    //     0xaf397c: stur            w0, [x3, #0xf]
    // 0xaf3980: ldur            x0, [fp, #-0x10]
    // 0xaf3984: StoreField: r3->field_13 = r0
    //     0xaf3984: stur            w0, [x3, #0x13]
    // 0xaf3988: r0 = false
    //     0xaf3988: add             x0, NULL, #0x30  ; false
    // 0xaf398c: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf398c: stur            w0, [x3, #0x17]
    // 0xaf3990: r1 = Function '<anonymous closure>':.
    //     0xaf3990: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f180] AnonymousClosure: (0xaf3bcc), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf3994: ldr             x1, [x1, #0x180]
    // 0xaf3998: r2 = Null
    //     0xaf3998: mov             x2, NULL
    // 0xaf399c: r0 = AllocateClosure()
    //     0xaf399c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf39a0: mov             x1, x0
    // 0xaf39a4: ldur            x0, [fp, #-0x18]
    // 0xaf39a8: StoreField: r0->field_1b = r1
    //     0xaf39a8: stur            w1, [x0, #0x1b]
    // 0xaf39ac: r4 = false
    //     0xaf39ac: add             x4, NULL, #0x30  ; false
    // 0xaf39b0: StoreField: r0->field_1f = r4
    //     0xaf39b0: stur            w4, [x0, #0x1f]
    // 0xaf39b4: LeaveFrame
    //     0xaf39b4: mov             SP, fp
    //     0xaf39b8: ldp             fp, lr, [SP], #0x10
    // 0xaf39bc: ret
    //     0xaf39bc: ret             
    // 0xaf39c0: r4 = false
    //     0xaf39c0: add             x4, NULL, #0x30  ; false
    // 0xaf39c4: LoadField: r5 = r0->field_f
    //     0xaf39c4: ldur            w5, [x0, #0xf]
    // 0xaf39c8: DecompressPointer r5
    //     0xaf39c8: add             x5, x5, HEAP, lsl #32
    // 0xaf39cc: sub             x6, x3, #1
    // 0xaf39d0: r0 = BoxInt64Instr(r6)
    //     0xaf39d0: sbfiz           x0, x6, #1, #0x1f
    //     0xaf39d4: cmp             x6, x0, asr #1
    //     0xaf39d8: b.eq            #0xaf39e4
    //     0xaf39dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf39e0: stur            x6, [x0, #7]
    // 0xaf39e4: r1 = LoadClassIdInstr(r5)
    //     0xaf39e4: ldur            x1, [x5, #-1]
    //     0xaf39e8: ubfx            x1, x1, #0xc, #0x14
    // 0xaf39ec: stp             x0, x5, [SP]
    // 0xaf39f0: mov             x0, x1
    // 0xaf39f4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf39f4: movz            x17, #0x3037
    //     0xaf39f8: movk            x17, #0x1, lsl #16
    //     0xaf39fc: add             lr, x0, x17
    //     0xaf3a00: ldr             lr, [x21, lr, lsl #3]
    //     0xaf3a04: blr             lr
    // 0xaf3a08: mov             x1, x0
    // 0xaf3a0c: ldur            x3, [fp, #-0x28]
    // 0xaf3a10: StoreField: r3->field_f = r0
    //     0xaf3a10: stur            w0, [x3, #0xf]
    //     0xaf3a14: ldurb           w16, [x3, #-1]
    //     0xaf3a18: ldurb           w17, [x0, #-1]
    //     0xaf3a1c: and             x16, x17, x16, lsr #2
    //     0xaf3a20: tst             x16, HEAP, lsr #32
    //     0xaf3a24: b.eq            #0xaf3a2c
    //     0xaf3a28: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaf3a2c: ldur            x0, [fp, #-0x20]
    // 0xaf3a30: add             x4, x0, #1
    // 0xaf3a34: stur            x4, [fp, #-0x30]
    // 0xaf3a38: LoadField: r5 = r1->field_f
    //     0xaf3a38: ldur            w5, [x1, #0xf]
    // 0xaf3a3c: DecompressPointer r5
    //     0xaf3a3c: add             x5, x5, HEAP, lsl #32
    // 0xaf3a40: stur            x5, [fp, #-0x10]
    // 0xaf3a44: LoadField: r2 = r1->field_13
    //     0xaf3a44: ldur            x2, [x1, #0x13]
    // 0xaf3a48: r0 = BoxInt64Instr(r2)
    //     0xaf3a48: sbfiz           x0, x2, #1, #0x1f
    //     0xaf3a4c: cmp             x2, x0, asr #1
    //     0xaf3a50: b.eq            #0xaf3a5c
    //     0xaf3a54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf3a58: stur            x2, [x0, #7]
    // 0xaf3a5c: r1 = Null
    //     0xaf3a5c: mov             x1, NULL
    // 0xaf3a60: r2 = 4
    //     0xaf3a60: movz            x2, #0x4
    // 0xaf3a64: stur            x0, [fp, #-8]
    // 0xaf3a68: r0 = AllocateArray()
    //     0xaf3a68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf3a6c: mov             x1, x0
    // 0xaf3a70: ldur            x0, [fp, #-8]
    // 0xaf3a74: StoreField: r1->field_f = r0
    //     0xaf3a74: stur            w0, [x1, #0xf]
    // 0xaf3a78: r16 = " Data"
    //     0xaf3a78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f170] " Data"
    //     0xaf3a7c: ldr             x16, [x16, #0x170]
    // 0xaf3a80: StoreField: r1->field_13 = r16
    //     0xaf3a80: stur            w16, [x1, #0x13]
    // 0xaf3a84: str             x1, [SP]
    // 0xaf3a88: r0 = _interpolate()
    //     0xaf3a88: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf3a8c: mov             x3, x0
    // 0xaf3a90: ldur            x2, [fp, #-0x30]
    // 0xaf3a94: stur            x3, [fp, #-0x18]
    // 0xaf3a98: r0 = BoxInt64Instr(r2)
    //     0xaf3a98: sbfiz           x0, x2, #1, #0x1f
    //     0xaf3a9c: cmp             x2, x0, asr #1
    //     0xaf3aa0: b.eq            #0xaf3aac
    //     0xaf3aa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf3aa8: stur            x2, [x0, #7]
    // 0xaf3aac: stur            x0, [fp, #-8]
    // 0xaf3ab0: r0 = NCardListNumber()
    //     0xaf3ab0: bl              #0xaf3b14  ; AllocateNCardListNumberStub -> NCardListNumber (size=0x30)
    // 0xaf3ab4: mov             x3, x0
    // 0xaf3ab8: ldur            x0, [fp, #-8]
    // 0xaf3abc: stur            x3, [fp, #-0x38]
    // 0xaf3ac0: StoreField: r3->field_b = r0
    //     0xaf3ac0: stur            w0, [x3, #0xb]
    // 0xaf3ac4: ldur            x0, [fp, #-0x10]
    // 0xaf3ac8: StoreField: r3->field_f = r0
    //     0xaf3ac8: stur            w0, [x3, #0xf]
    // 0xaf3acc: ldur            x0, [fp, #-0x18]
    // 0xaf3ad0: StoreField: r3->field_13 = r0
    //     0xaf3ad0: stur            w0, [x3, #0x13]
    // 0xaf3ad4: r0 = false
    //     0xaf3ad4: add             x0, NULL, #0x30  ; false
    // 0xaf3ad8: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf3ad8: stur            w0, [x3, #0x17]
    // 0xaf3adc: ldur            x2, [fp, #-0x28]
    // 0xaf3ae0: r1 = Function '<anonymous closure>':.
    //     0xaf3ae0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f188] AnonymousClosure: (0xaf3b20), in [package:nuonline/app/modules/encyclopedia/views/encyclopedia_view.dart] EncyclopediaView::build (0xaf3348)
    //     0xaf3ae4: ldr             x1, [x1, #0x188]
    // 0xaf3ae8: r0 = AllocateClosure()
    //     0xaf3ae8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf3aec: mov             x1, x0
    // 0xaf3af0: ldur            x0, [fp, #-0x38]
    // 0xaf3af4: StoreField: r0->field_1b = r1
    //     0xaf3af4: stur            w1, [x0, #0x1b]
    // 0xaf3af8: r1 = false
    //     0xaf3af8: add             x1, NULL, #0x30  ; false
    // 0xaf3afc: StoreField: r0->field_1f = r1
    //     0xaf3afc: stur            w1, [x0, #0x1f]
    // 0xaf3b00: LeaveFrame
    //     0xaf3b00: mov             SP, fp
    //     0xaf3b04: ldp             fp, lr, [SP], #0x10
    // 0xaf3b08: ret
    //     0xaf3b08: ret             
    // 0xaf3b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3b0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3b10: b               #0xaf38ac
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf3b20, size: 0xac
    // 0xaf3b20: EnterFrame
    //     0xaf3b20: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3b24: mov             fp, SP
    // 0xaf3b28: AllocStack(0x20)
    //     0xaf3b28: sub             SP, SP, #0x20
    // 0xaf3b2c: SetupParameters()
    //     0xaf3b2c: ldr             x0, [fp, #0x10]
    //     0xaf3b30: ldur            w1, [x0, #0x17]
    //     0xaf3b34: add             x1, x1, HEAP, lsl #32
    //     0xaf3b38: stur            x1, [fp, #-8]
    // 0xaf3b3c: CheckStackOverflow
    //     0xaf3b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3b40: cmp             SP, x16
    //     0xaf3b44: b.ls            #0xaf3bc4
    // 0xaf3b48: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf3b48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf3b4c: ldr             x0, [x0, #0x2670]
    //     0xaf3b50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf3b54: cmp             w0, w16
    //     0xaf3b58: b.ne            #0xaf3b64
    //     0xaf3b5c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf3b60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf3b64: r1 = Null
    //     0xaf3b64: mov             x1, NULL
    // 0xaf3b68: r2 = 4
    //     0xaf3b68: movz            x2, #0x4
    // 0xaf3b6c: r0 = AllocateArray()
    //     0xaf3b6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf3b70: r16 = "category"
    //     0xaf3b70: add             x16, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xaf3b74: ldr             x16, [x16, #0x960]
    // 0xaf3b78: StoreField: r0->field_f = r16
    //     0xaf3b78: stur            w16, [x0, #0xf]
    // 0xaf3b7c: ldur            x1, [fp, #-8]
    // 0xaf3b80: LoadField: r2 = r1->field_f
    //     0xaf3b80: ldur            w2, [x1, #0xf]
    // 0xaf3b84: DecompressPointer r2
    //     0xaf3b84: add             x2, x2, HEAP, lsl #32
    // 0xaf3b88: StoreField: r0->field_13 = r2
    //     0xaf3b88: stur            w2, [x0, #0x13]
    // 0xaf3b8c: r16 = <String, EncyclopediaCategory>
    //     0xaf3b8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f190] TypeArguments: <String, EncyclopediaCategory>
    //     0xaf3b90: ldr             x16, [x16, #0x190]
    // 0xaf3b94: stp             x0, x16, [SP]
    // 0xaf3b98: r0 = Map._fromLiteral()
    //     0xaf3b98: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf3b9c: r16 = "/encyclopedia/encyclopedia-list"
    //     0xaf3b9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f198] "/encyclopedia/encyclopedia-list"
    //     0xaf3ba0: ldr             x16, [x16, #0x198]
    // 0xaf3ba4: stp             x16, NULL, [SP, #8]
    // 0xaf3ba8: str             x0, [SP]
    // 0xaf3bac: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xaf3bac: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xaf3bb0: ldr             x4, [x4, #0x478]
    // 0xaf3bb4: r0 = GetNavigation.toNamed()
    //     0xaf3bb4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf3bb8: LeaveFrame
    //     0xaf3bb8: mov             SP, fp
    //     0xaf3bbc: ldp             fp, lr, [SP], #0x10
    // 0xaf3bc0: ret
    //     0xaf3bc0: ret             
    // 0xaf3bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3bc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3bc8: b               #0xaf3b48
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf3bcc, size: 0x90
    // 0xaf3bcc: EnterFrame
    //     0xaf3bcc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3bd0: mov             fp, SP
    // 0xaf3bd4: AllocStack(0x18)
    //     0xaf3bd4: sub             SP, SP, #0x18
    // 0xaf3bd8: CheckStackOverflow
    //     0xaf3bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3bdc: cmp             SP, x16
    //     0xaf3be0: b.ls            #0xaf3c54
    // 0xaf3be4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf3be4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf3be8: ldr             x0, [x0, #0x2670]
    //     0xaf3bec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf3bf0: cmp             w0, w16
    //     0xaf3bf4: b.ne            #0xaf3c00
    //     0xaf3bf8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf3bfc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf3c00: r1 = Null
    //     0xaf3c00: mov             x1, NULL
    // 0xaf3c04: r2 = 4
    //     0xaf3c04: movz            x2, #0x4
    // 0xaf3c08: r0 = AllocateArray()
    //     0xaf3c08: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf3c0c: r16 = "category"
    //     0xaf3c0c: add             x16, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xaf3c10: ldr             x16, [x16, #0x960]
    // 0xaf3c14: StoreField: r0->field_f = r16
    //     0xaf3c14: stur            w16, [x0, #0xf]
    // 0xaf3c18: StoreField: r0->field_13 = rNULL
    //     0xaf3c18: stur            NULL, [x0, #0x13]
    // 0xaf3c1c: r16 = <String, Null?>
    //     0xaf3c1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1a0] TypeArguments: <String, Null?>
    //     0xaf3c20: ldr             x16, [x16, #0x1a0]
    // 0xaf3c24: stp             x0, x16, [SP]
    // 0xaf3c28: r0 = Map._fromLiteral()
    //     0xaf3c28: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf3c2c: r16 = "/encyclopedia/encyclopedia-list"
    //     0xaf3c2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f198] "/encyclopedia/encyclopedia-list"
    //     0xaf3c30: ldr             x16, [x16, #0x198]
    // 0xaf3c34: stp             x16, NULL, [SP, #8]
    // 0xaf3c38: str             x0, [SP]
    // 0xaf3c3c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xaf3c3c: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xaf3c40: ldr             x4, [x4, #0x478]
    // 0xaf3c44: r0 = GetNavigation.toNamed()
    //     0xaf3c44: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf3c48: LeaveFrame
    //     0xaf3c48: mov             SP, fp
    //     0xaf3c4c: ldp             fp, lr, [SP], #0x10
    // 0xaf3c50: ret
    //     0xaf3c50: ret             
    // 0xaf3c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3c58: b               #0xaf3be4
  }
  [closure] int <anonymous closure>(dynamic, int, EncyclopediaCategory) {
    // ** addr: 0xaf3c5c, size: 0x44
    // 0xaf3c5c: ldr             x2, [SP]
    // 0xaf3c60: LoadField: r3 = r2->field_13
    //     0xaf3c60: ldur            x3, [x2, #0x13]
    // 0xaf3c64: ldr             x2, [SP, #8]
    // 0xaf3c68: r4 = LoadInt32Instr(r2)
    //     0xaf3c68: sbfx            x4, x2, #1, #0x1f
    //     0xaf3c6c: tbz             w2, #0, #0xaf3c74
    //     0xaf3c70: ldur            x4, [x2, #7]
    // 0xaf3c74: add             x2, x4, x3
    // 0xaf3c78: r0 = BoxInt64Instr(r2)
    //     0xaf3c78: sbfiz           x0, x2, #1, #0x1f
    //     0xaf3c7c: cmp             x2, x0, asr #1
    //     0xaf3c80: b.eq            #0xaf3c9c
    //     0xaf3c84: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3c88: mov             fp, SP
    //     0xaf3c8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf3c90: mov             SP, fp
    //     0xaf3c94: ldp             fp, lr, [SP], #0x10
    //     0xaf3c98: stur            x2, [x0, #7]
    // 0xaf3c9c: ret
    //     0xaf3c9c: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf40e8, size: 0x5c
    // 0xaf40e8: EnterFrame
    //     0xaf40e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf40ec: mov             fp, SP
    // 0xaf40f0: AllocStack(0x10)
    //     0xaf40f0: sub             SP, SP, #0x10
    // 0xaf40f4: CheckStackOverflow
    //     0xaf40f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf40f8: cmp             SP, x16
    //     0xaf40fc: b.ls            #0xaf413c
    // 0xaf4100: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf4100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf4104: ldr             x0, [x0, #0x2670]
    //     0xaf4108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf410c: cmp             w0, w16
    //     0xaf4110: b.ne            #0xaf411c
    //     0xaf4114: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf4118: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf411c: r16 = "/encyclopedia/encyclopedia-info"
    //     0xaf411c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1a8] "/encyclopedia/encyclopedia-info"
    //     0xaf4120: ldr             x16, [x16, #0x1a8]
    // 0xaf4124: stp             x16, NULL, [SP]
    // 0xaf4128: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf4128: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf412c: r0 = GetNavigation.toNamed()
    //     0xaf412c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf4130: LeaveFrame
    //     0xaf4130: mov             SP, fp
    //     0xaf4134: ldp             fp, lr, [SP], #0x10
    // 0xaf4138: ret
    //     0xaf4138: ret             
    // 0xaf413c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf413c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4140: b               #0xaf4100
  }
}
