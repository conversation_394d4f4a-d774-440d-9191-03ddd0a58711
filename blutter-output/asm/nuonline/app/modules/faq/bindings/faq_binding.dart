// lib: , url: package:nuonline/app/modules/faq/bindings/faq_binding.dart

// class id: 1050268, size: 0x8
class :: {
}

// class id: 2168, size: 0x8, field offset: 0x8
class FaqBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x812b78, size: 0x70
    // 0x812b78: EnterFrame
    //     0x812b78: stp             fp, lr, [SP, #-0x10]!
    //     0x812b7c: mov             fp, SP
    // 0x812b80: AllocStack(0x10)
    //     0x812b80: sub             SP, SP, #0x10
    // 0x812b84: CheckStackOverflow
    //     0x812b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812b88: cmp             SP, x16
    //     0x812b8c: b.ls            #0x812be0
    // 0x812b90: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812b90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812b94: ldr             x0, [x0, #0x2670]
    //     0x812b98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812b9c: cmp             w0, w16
    //     0x812ba0: b.ne            #0x812bac
    //     0x812ba4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812ba8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812bac: r1 = Function '<anonymous closure>':.
    //     0x812bac: add             x1, PP, #0x34, lsl #12  ; [pp+0x349e0] AnonymousClosure: (0x812be8), in [package:nuonline/app/modules/faq/bindings/faq_binding.dart] FaqBinding::dependencies (0x812b78)
    //     0x812bb0: ldr             x1, [x1, #0x9e0]
    // 0x812bb4: r2 = Null
    //     0x812bb4: mov             x2, NULL
    // 0x812bb8: r0 = AllocateClosure()
    //     0x812bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x812bbc: r16 = <FaqController>
    //     0x812bbc: add             x16, PP, #0x34, lsl #12  ; [pp+0x349e8] TypeArguments: <FaqController>
    //     0x812bc0: ldr             x16, [x16, #0x9e8]
    // 0x812bc4: stp             x0, x16, [SP]
    // 0x812bc8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812bc8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812bcc: r0 = Inst.lazyPut()
    //     0x812bcc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x812bd0: r0 = Null
    //     0x812bd0: mov             x0, NULL
    // 0x812bd4: LeaveFrame
    //     0x812bd4: mov             SP, fp
    //     0x812bd8: ldp             fp, lr, [SP], #0x10
    // 0x812bdc: ret
    //     0x812bdc: ret             
    // 0x812be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812be4: b               #0x812b90
  }
  [closure] FaqController <anonymous closure>(dynamic) {
    // ** addr: 0x812be8, size: 0x90
    // 0x812be8: EnterFrame
    //     0x812be8: stp             fp, lr, [SP, #-0x10]!
    //     0x812bec: mov             fp, SP
    // 0x812bf0: AllocStack(0x20)
    //     0x812bf0: sub             SP, SP, #0x20
    // 0x812bf4: CheckStackOverflow
    //     0x812bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812bf8: cmp             SP, x16
    //     0x812bfc: b.ls            #0x812c70
    // 0x812c00: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812c00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812c04: ldr             x0, [x0, #0x2670]
    //     0x812c08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812c0c: cmp             w0, w16
    //     0x812c10: b.ne            #0x812c1c
    //     0x812c14: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812c18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812c1c: r16 = <FaqRepository>
    //     0x812c1c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10230] TypeArguments: <FaqRepository>
    //     0x812c20: ldr             x16, [x16, #0x230]
    // 0x812c24: r30 = "faq_repo"
    //     0x812c24: add             lr, PP, #0x10, lsl #12  ; [pp+0x10238] "faq_repo"
    //     0x812c28: ldr             lr, [lr, #0x238]
    // 0x812c2c: stp             lr, x16, [SP]
    // 0x812c30: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x812c30: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x812c34: r0 = Inst.find()
    //     0x812c34: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x812c38: r1 = <List<FaqCategory>>
    //     0x812c38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f018] TypeArguments: <List<FaqCategory>>
    //     0x812c3c: ldr             x1, [x1, #0x18]
    // 0x812c40: stur            x0, [fp, #-8]
    // 0x812c44: r0 = FaqController()
    //     0x812c44: bl              #0x812c78  ; AllocateFaqControllerStub -> FaqController (size=0x30)
    // 0x812c48: mov             x2, x0
    // 0x812c4c: ldur            x0, [fp, #-8]
    // 0x812c50: stur            x2, [fp, #-0x10]
    // 0x812c54: StoreField: r2->field_2b = r0
    //     0x812c54: stur            w0, [x2, #0x2b]
    // 0x812c58: mov             x1, x2
    // 0x812c5c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x812c5c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x812c60: ldur            x0, [fp, #-0x10]
    // 0x812c64: LeaveFrame
    //     0x812c64: mov             SP, fp
    //     0x812c68: ldp             fp, lr, [SP], #0x10
    // 0x812c6c: ret
    //     0x812c6c: ret             
    // 0x812c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812c74: b               #0x812c00
  }
}
