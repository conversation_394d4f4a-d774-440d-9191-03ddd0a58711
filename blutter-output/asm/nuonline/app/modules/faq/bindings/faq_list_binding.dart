// lib: , url: package:nuonline/app/modules/faq/bindings/faq_list_binding.dart

// class id: 1050269, size: 0x8
class :: {
}

// class id: 2167, size: 0x8, field offset: 0x8
class FaqListBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x812c84, size: 0x70
    // 0x812c84: EnterFrame
    //     0x812c84: stp             fp, lr, [SP, #-0x10]!
    //     0x812c88: mov             fp, SP
    // 0x812c8c: AllocStack(0x10)
    //     0x812c8c: sub             SP, SP, #0x10
    // 0x812c90: CheckStackOverflow
    //     0x812c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812c94: cmp             SP, x16
    //     0x812c98: b.ls            #0x812cec
    // 0x812c9c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812c9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812ca0: ldr             x0, [x0, #0x2670]
    //     0x812ca4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812ca8: cmp             w0, w16
    //     0x812cac: b.ne            #0x812cb8
    //     0x812cb0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812cb4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812cb8: r1 = Function '<anonymous closure>':.
    //     0x812cb8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34998] AnonymousClosure: (0x812cf4), in [package:nuonline/app/modules/faq/bindings/faq_list_binding.dart] FaqListBinding::dependencies (0x812c84)
    //     0x812cbc: ldr             x1, [x1, #0x998]
    // 0x812cc0: r2 = Null
    //     0x812cc0: mov             x2, NULL
    // 0x812cc4: r0 = AllocateClosure()
    //     0x812cc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x812cc8: r16 = <FaqListController>
    //     0x812cc8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f048] TypeArguments: <FaqListController>
    //     0x812ccc: ldr             x16, [x16, #0x48]
    // 0x812cd0: stp             x0, x16, [SP]
    // 0x812cd4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x812cd4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x812cd8: r0 = Inst.lazyPut()
    //     0x812cd8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x812cdc: r0 = Null
    //     0x812cdc: mov             x0, NULL
    // 0x812ce0: LeaveFrame
    //     0x812ce0: mov             SP, fp
    //     0x812ce4: ldp             fp, lr, [SP], #0x10
    // 0x812ce8: ret
    //     0x812ce8: ret             
    // 0x812cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812cf0: b               #0x812c9c
  }
  [closure] FaqListController <anonymous closure>(dynamic) {
    // ** addr: 0x812cf4, size: 0x198
    // 0x812cf4: EnterFrame
    //     0x812cf4: stp             fp, lr, [SP, #-0x10]!
    //     0x812cf8: mov             fp, SP
    // 0x812cfc: AllocStack(0x30)
    //     0x812cfc: sub             SP, SP, #0x30
    // 0x812d00: CheckStackOverflow
    //     0x812d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812d04: cmp             SP, x16
    //     0x812d08: b.ls            #0x812e84
    // 0x812d0c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812d0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812d10: ldr             x0, [x0, #0x2670]
    //     0x812d14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812d18: cmp             w0, w16
    //     0x812d1c: b.ne            #0x812d28
    //     0x812d20: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812d24: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812d28: r0 = GetNavigation.arguments()
    //     0x812d28: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x812d2c: r16 = "title"
    //     0x812d2c: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x812d30: ldr             x16, [x16, #0x748]
    // 0x812d34: stp             x16, x0, [SP]
    // 0x812d38: r4 = 0
    //     0x812d38: movz            x4, #0
    // 0x812d3c: ldr             x0, [SP, #8]
    // 0x812d40: r16 = UnlinkedCall_0x5f3c08
    //     0x812d40: add             x16, PP, #0x34, lsl #12  ; [pp+0x349a0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x812d44: add             x16, x16, #0x9a0
    // 0x812d48: ldp             x5, lr, [x16]
    // 0x812d4c: blr             lr
    // 0x812d50: mov             x3, x0
    // 0x812d54: r2 = Null
    //     0x812d54: mov             x2, NULL
    // 0x812d58: r1 = Null
    //     0x812d58: mov             x1, NULL
    // 0x812d5c: stur            x3, [fp, #-8]
    // 0x812d60: r4 = 60
    //     0x812d60: movz            x4, #0x3c
    // 0x812d64: branchIfSmi(r0, 0x812d70)
    //     0x812d64: tbz             w0, #0, #0x812d70
    // 0x812d68: r4 = LoadClassIdInstr(r0)
    //     0x812d68: ldur            x4, [x0, #-1]
    //     0x812d6c: ubfx            x4, x4, #0xc, #0x14
    // 0x812d70: sub             x4, x4, #0x5e
    // 0x812d74: cmp             x4, #1
    // 0x812d78: b.ls            #0x812d8c
    // 0x812d7c: r8 = String?
    //     0x812d7c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x812d80: r3 = Null
    //     0x812d80: add             x3, PP, #0x34, lsl #12  ; [pp+0x349b0] Null
    //     0x812d84: ldr             x3, [x3, #0x9b0]
    // 0x812d88: r0 = String?()
    //     0x812d88: bl              #0x600324  ; IsType_String?_Stub
    // 0x812d8c: ldur            x0, [fp, #-8]
    // 0x812d90: cmp             w0, NULL
    // 0x812d94: b.ne            #0x812da0
    // 0x812d98: r0 = "FAQ"
    //     0x812d98: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f070] "FAQ"
    //     0x812d9c: ldr             x0, [x0, #0x70]
    // 0x812da0: stur            x0, [fp, #-8]
    // 0x812da4: r0 = GetNavigation.arguments()
    //     0x812da4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x812da8: r16 = "category_slug"
    //     0x812da8: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a6f0] "category_slug"
    //     0x812dac: ldr             x16, [x16, #0x6f0]
    // 0x812db0: stp             x16, x0, [SP]
    // 0x812db4: r4 = 0
    //     0x812db4: movz            x4, #0
    // 0x812db8: ldr             x0, [SP, #8]
    // 0x812dbc: r16 = UnlinkedCall_0x5f3c08
    //     0x812dbc: add             x16, PP, #0x34, lsl #12  ; [pp+0x349c0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x812dc0: add             x16, x16, #0x9c0
    // 0x812dc4: ldp             x5, lr, [x16]
    // 0x812dc8: blr             lr
    // 0x812dcc: mov             x3, x0
    // 0x812dd0: r2 = Null
    //     0x812dd0: mov             x2, NULL
    // 0x812dd4: r1 = Null
    //     0x812dd4: mov             x1, NULL
    // 0x812dd8: stur            x3, [fp, #-0x10]
    // 0x812ddc: r4 = 60
    //     0x812ddc: movz            x4, #0x3c
    // 0x812de0: branchIfSmi(r0, 0x812dec)
    //     0x812de0: tbz             w0, #0, #0x812dec
    // 0x812de4: r4 = LoadClassIdInstr(r0)
    //     0x812de4: ldur            x4, [x0, #-1]
    //     0x812de8: ubfx            x4, x4, #0xc, #0x14
    // 0x812dec: sub             x4, x4, #0x5e
    // 0x812df0: cmp             x4, #1
    // 0x812df4: b.ls            #0x812e08
    // 0x812df8: r8 = String?
    //     0x812df8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x812dfc: r3 = Null
    //     0x812dfc: add             x3, PP, #0x34, lsl #12  ; [pp+0x349d0] Null
    //     0x812e00: ldr             x3, [x3, #0x9d0]
    // 0x812e04: r0 = String?()
    //     0x812e04: bl              #0x600324  ; IsType_String?_Stub
    // 0x812e08: ldur            x0, [fp, #-0x10]
    // 0x812e0c: cmp             w0, NULL
    // 0x812e10: b.ne            #0x812e1c
    // 0x812e14: r2 = ""
    //     0x812e14: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0x812e18: b               #0x812e20
    // 0x812e1c: mov             x2, x0
    // 0x812e20: stur            x2, [fp, #-0x10]
    // 0x812e24: r16 = <FaqRepository>
    //     0x812e24: add             x16, PP, #0x10, lsl #12  ; [pp+0x10230] TypeArguments: <FaqRepository>
    //     0x812e28: ldr             x16, [x16, #0x230]
    // 0x812e2c: r30 = "faq_repo"
    //     0x812e2c: add             lr, PP, #0x10, lsl #12  ; [pp+0x10238] "faq_repo"
    //     0x812e30: ldr             lr, [lr, #0x238]
    // 0x812e34: stp             lr, x16, [SP]
    // 0x812e38: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x812e38: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x812e3c: r0 = Inst.find()
    //     0x812e3c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x812e40: r1 = <List<Faq>, Faq>
    //     0x812e40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f040] TypeArguments: <List<Faq>, Faq>
    //     0x812e44: ldr             x1, [x1, #0x40]
    // 0x812e48: stur            x0, [fp, #-0x18]
    // 0x812e4c: r0 = FaqListController()
    //     0x812e4c: bl              #0x813108  ; AllocateFaqListControllerStub -> FaqListController (size=0x44)
    // 0x812e50: stur            x0, [fp, #-0x20]
    // 0x812e54: ldur            x16, [fp, #-8]
    // 0x812e58: str             x16, [SP]
    // 0x812e5c: mov             x1, x0
    // 0x812e60: ldur            x2, [fp, #-0x10]
    // 0x812e64: ldur            x3, [fp, #-0x18]
    // 0x812e68: r4 = const [0, 0x4, 0x1, 0x3, title, 0x3, null]
    //     0x812e68: add             x4, PP, #0x32, lsl #12  ; [pp+0x322b0] List(7) [0, 0x4, 0x1, 0x3, "title", 0x3, Null]
    //     0x812e6c: ldr             x4, [x4, #0x2b0]
    // 0x812e70: r0 = FaqListController()
    //     0x812e70: bl              #0x812e8c  ; [package:nuonline/app/modules/faq/controllers/faq_list_controller.dart] FaqListController::FaqListController
    // 0x812e74: ldur            x0, [fp, #-0x20]
    // 0x812e78: LeaveFrame
    //     0x812e78: mov             SP, fp
    //     0x812e7c: ldp             fp, lr, [SP], #0x10
    // 0x812e80: ret
    //     0x812e80: ret             
    // 0x812e84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812e84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812e88: b               #0x812d0c
  }
}
