// lib: , url: package:nuonline/app/modules/faq/bindings/faq_search_binding.dart

// class id: 1050270, size: 0x8
class :: {
}

// class id: 2166, size: 0x8, field offset: 0x8
class FaqSearchBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x813114, size: 0x70
    // 0x813114: EnterFrame
    //     0x813114: stp             fp, lr, [SP, #-0x10]!
    //     0x813118: mov             fp, SP
    // 0x81311c: AllocStack(0x10)
    //     0x81311c: sub             SP, SP, #0x10
    // 0x813120: CheckStackOverflow
    //     0x813120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813124: cmp             SP, x16
    //     0x813128: b.ls            #0x81317c
    // 0x81312c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x81312c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x813130: ldr             x0, [x0, #0x2670]
    //     0x813134: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x813138: cmp             w0, w16
    //     0x81313c: b.ne            #0x813148
    //     0x813140: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x813144: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x813148: r1 = Function '<anonymous closure>':.
    //     0x813148: add             x1, PP, #0x34, lsl #12  ; [pp+0x34868] AnonymousClosure: (0x813184), in [package:nuonline/app/modules/faq/bindings/faq_search_binding.dart] FaqSearchBinding::dependencies (0x813114)
    //     0x81314c: ldr             x1, [x1, #0x868]
    // 0x813150: r2 = Null
    //     0x813150: mov             x2, NULL
    // 0x813154: r0 = AllocateClosure()
    //     0x813154: bl              #0xec1630  ; AllocateClosureStub
    // 0x813158: r16 = <FaqSearchController>
    //     0x813158: add             x16, PP, #0x34, lsl #12  ; [pp+0x34870] TypeArguments: <FaqSearchController>
    //     0x81315c: ldr             x16, [x16, #0x870]
    // 0x813160: stp             x0, x16, [SP]
    // 0x813164: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x813164: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x813168: r0 = Inst.lazyPut()
    //     0x813168: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x81316c: r0 = Null
    //     0x81316c: mov             x0, NULL
    // 0x813170: LeaveFrame
    //     0x813170: mov             SP, fp
    //     0x813174: ldp             fp, lr, [SP], #0x10
    // 0x813178: ret
    //     0x813178: ret             
    // 0x81317c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81317c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x813180: b               #0x81312c
  }
  [closure] FaqSearchController <anonymous closure>(dynamic) {
    // ** addr: 0x813184, size: 0x88
    // 0x813184: EnterFrame
    //     0x813184: stp             fp, lr, [SP, #-0x10]!
    //     0x813188: mov             fp, SP
    // 0x81318c: AllocStack(0x18)
    //     0x81318c: sub             SP, SP, #0x18
    // 0x813190: CheckStackOverflow
    //     0x813190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813194: cmp             SP, x16
    //     0x813198: b.ls            #0x813204
    // 0x81319c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x81319c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8131a0: ldr             x0, [x0, #0x2670]
    //     0x8131a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8131a8: cmp             w0, w16
    //     0x8131ac: b.ne            #0x8131b8
    //     0x8131b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8131b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8131b8: r16 = <FaqRepository>
    //     0x8131b8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10230] TypeArguments: <FaqRepository>
    //     0x8131bc: ldr             x16, [x16, #0x230]
    // 0x8131c0: r30 = "faq_repo"
    //     0x8131c0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10238] "faq_repo"
    //     0x8131c4: ldr             lr, [lr, #0x238]
    // 0x8131c8: stp             lr, x16, [SP]
    // 0x8131cc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8131cc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8131d0: r0 = Inst.find()
    //     0x8131d0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8131d4: r1 = <List<Faq>, Faq>
    //     0x8131d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f040] TypeArguments: <List<Faq>, Faq>
    //     0x8131d8: ldr             x1, [x1, #0x40]
    // 0x8131dc: stur            x0, [fp, #-8]
    // 0x8131e0: r0 = FaqSearchController()
    //     0x8131e0: bl              #0x8132e4  ; AllocateFaqSearchControllerStub -> FaqSearchController (size=0x3c)
    // 0x8131e4: mov             x1, x0
    // 0x8131e8: ldur            x2, [fp, #-8]
    // 0x8131ec: stur            x0, [fp, #-8]
    // 0x8131f0: r0 = FaqSearchController()
    //     0x8131f0: bl              #0x81320c  ; [package:nuonline/app/modules/faq/controllers/faq_search_controller.dart] FaqSearchController::FaqSearchController
    // 0x8131f4: ldur            x0, [fp, #-8]
    // 0x8131f8: LeaveFrame
    //     0x8131f8: mov             SP, fp
    //     0x8131fc: ldp             fp, lr, [SP], #0x10
    // 0x813200: ret
    //     0x813200: ret             
    // 0x813204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x813204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x813208: b               #0x81319c
  }
}
