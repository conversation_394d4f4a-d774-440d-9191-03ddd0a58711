// lib: , url: package:nuonline/app/modules/faq/controllers/faq_controller.dart

// class id: 1050271, size: 0x8
class :: {
}

// class id: 1932, size: 0x30, field offset: 0x2c
class FaqController extends SimpleFetchController<dynamic> {

  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e8f3c, size: 0x38
    // 0x7e8f3c: EnterFrame
    //     0x7e8f3c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8f40: mov             fp, SP
    // 0x7e8f44: CheckStackOverflow
    //     0x7e8f44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8f48: cmp             SP, x16
    //     0x7e8f4c: b.ls            #0x7e8f6c
    // 0x7e8f50: LoadField: r0 = r1->field_2b
    //     0x7e8f50: ldur            w0, [x1, #0x2b]
    // 0x7e8f54: Decompress<PERSON>ointer r0
    //     0x7e8f54: add             x0, x0, HEAP, lsl #32
    // 0x7e8f58: mov             x1, x0
    // 0x7e8f5c: r0 = findAllCategory()
    //     0x7e8f5c: bl              #0x7e9018  ; [package:nuonline/app/data/repositories/faq_repository.dart] FaqRepository::findAllCategory
    // 0x7e8f60: LeaveFrame
    //     0x7e8f60: mov             SP, fp
    //     0x7e8f64: ldp             fp, lr, [SP], #0x10
    // 0x7e8f68: ret
    //     0x7e8f68: ret             
    // 0x7e8f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8f70: b               #0x7e8f50
  }
}
