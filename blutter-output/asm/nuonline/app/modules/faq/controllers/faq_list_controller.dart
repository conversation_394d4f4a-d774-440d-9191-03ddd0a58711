// lib: , url: package:nuonline/app/modules/faq/controllers/faq_list_controller.dart

// class id: 1050272, size: 0x8
class :: {
}

// class id: 1936, size: 0x44, field offset: 0x34
class FaqListController extends PaginatedFetchController<dynamic> {

  _ onFetchRequested(/* No info */) async {
    // ** addr: 0x7e8298, size: 0x88
    // 0x7e8298: EnterFrame
    //     0x7e8298: stp             fp, lr, [SP, #-0x10]!
    //     0x7e829c: mov             fp, SP
    // 0x7e82a0: AllocStack(0x30)
    //     0x7e82a0: sub             SP, SP, #0x30
    // 0x7e82a4: SetupParameters(FaqListController this /* r1 => r1, fp-0x10 */)
    //     0x7e82a4: stur            NULL, [fp, #-8]
    //     0x7e82a8: stur            x1, [fp, #-0x10]
    // 0x7e82ac: CheckStackOverflow
    //     0x7e82ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e82b0: cmp             SP, x16
    //     0x7e82b4: b.ls            #0x7e8318
    // 0x7e82b8: InitAsync() -> Future<ApiResult<List<Faq>>>
    //     0x7e82b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34878] TypeArguments: <ApiResult<List<Faq>>>
    //     0x7e82bc: ldr             x0, [x0, #0x878]
    //     0x7e82c0: bl              #0x661298  ; InitAsyncStub
    // 0x7e82c4: ldur            x0, [fp, #-0x10]
    // 0x7e82c8: LoadField: r2 = r0->field_3b
    //     0x7e82c8: ldur            w2, [x0, #0x3b]
    // 0x7e82cc: DecompressPointer r2
    //     0x7e82cc: add             x2, x2, HEAP, lsl #32
    // 0x7e82d0: stur            x2, [fp, #-0x28]
    // 0x7e82d4: LoadField: r3 = r0->field_37
    //     0x7e82d4: ldur            w3, [x0, #0x37]
    // 0x7e82d8: DecompressPointer r3
    //     0x7e82d8: add             x3, x3, HEAP, lsl #32
    // 0x7e82dc: stur            x3, [fp, #-0x20]
    // 0x7e82e0: LoadField: r4 = r0->field_23
    //     0x7e82e0: ldur            x4, [x0, #0x23]
    // 0x7e82e4: stur            x4, [fp, #-0x18]
    // 0x7e82e8: LoadField: r1 = r0->field_2f
    //     0x7e82e8: ldur            w1, [x0, #0x2f]
    // 0x7e82ec: DecompressPointer r1
    //     0x7e82ec: add             x1, x1, HEAP, lsl #32
    // 0x7e82f0: r0 = value()
    //     0x7e82f0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7e82f4: ldur            x16, [fp, #-0x20]
    // 0x7e82f8: str             x16, [SP]
    // 0x7e82fc: ldur            x1, [fp, #-0x28]
    // 0x7e8300: ldur            x2, [fp, #-0x18]
    // 0x7e8304: mov             x3, x0
    // 0x7e8308: r4 = const [0, 0x4, 0x1, 0x3, slug, 0x3, null]
    //     0x7e8308: add             x4, PP, #0x34, lsl #12  ; [pp+0x34880] List(7) [0, 0x4, 0x1, 0x3, "slug", 0x3, Null]
    //     0x7e830c: ldr             x4, [x4, #0x880]
    // 0x7e8310: r0 = findAll()
    //     0x7e8310: bl              #0x7e8320  ; [package:nuonline/app/data/repositories/faq_repository.dart] FaqRepository::findAll
    // 0x7e8314: r0 = ReturnAsync()
    //     0x7e8314: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7e8318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8318: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e831c: b               #0x7e82b8
  }
  _ FaqListController(/* No info */) {
    // ** addr: 0x812e8c, size: 0x160
    // 0x812e8c: EnterFrame
    //     0x812e8c: stp             fp, lr, [SP, #-0x10]!
    //     0x812e90: mov             fp, SP
    // 0x812e94: AllocStack(0x28)
    //     0x812e94: sub             SP, SP, #0x28
    // 0x812e98: SetupParameters(FaqListController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */, {dynamic title = "FAQ" /* r4, fp-0x8 */})
    //     0x812e98: mov             x0, x3
    //     0x812e9c: stur            x3, [fp, #-0x20]
    //     0x812ea0: mov             x3, x1
    //     0x812ea4: stur            x1, [fp, #-0x10]
    //     0x812ea8: stur            x2, [fp, #-0x18]
    //     0x812eac: ldur            w1, [x4, #0x13]
    //     0x812eb0: ldur            w5, [x4, #0x1f]
    //     0x812eb4: add             x5, x5, HEAP, lsl #32
    //     0x812eb8: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x812ebc: ldr             x16, [x16, #0x748]
    //     0x812ec0: cmp             w5, w16
    //     0x812ec4: b.ne            #0x812ee4
    //     0x812ec8: ldur            w5, [x4, #0x23]
    //     0x812ecc: add             x5, x5, HEAP, lsl #32
    //     0x812ed0: sub             w4, w1, w5
    //     0x812ed4: add             x1, fp, w4, sxtw #2
    //     0x812ed8: ldr             x1, [x1, #8]
    //     0x812edc: mov             x4, x1
    //     0x812ee0: b               #0x812eec
    //     0x812ee4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f070] "FAQ"
    //     0x812ee8: ldr             x4, [x4, #0x70]
    //     0x812eec: stur            x4, [fp, #-8]
    // 0x812ef0: CheckStackOverflow
    //     0x812ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x812ef4: cmp             SP, x16
    //     0x812ef8: b.ls            #0x812fe4
    // 0x812efc: r1 = <TextEditingValue>
    //     0x812efc: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x812f00: r0 = TextEditingController()
    //     0x812f00: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x812f04: mov             x1, x0
    // 0x812f08: stur            x0, [fp, #-0x28]
    // 0x812f0c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x812f0c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x812f10: r0 = TextEditingController()
    //     0x812f10: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x812f14: ldur            x0, [fp, #-0x28]
    // 0x812f18: ldur            x2, [fp, #-0x10]
    // 0x812f1c: StoreField: r2->field_3f = r0
    //     0x812f1c: stur            w0, [x2, #0x3f]
    //     0x812f20: ldurb           w16, [x2, #-1]
    //     0x812f24: ldurb           w17, [x0, #-1]
    //     0x812f28: and             x16, x17, x16, lsr #2
    //     0x812f2c: tst             x16, HEAP, lsr #32
    //     0x812f30: b.eq            #0x812f38
    //     0x812f34: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812f38: ldur            x0, [fp, #-8]
    // 0x812f3c: StoreField: r2->field_33 = r0
    //     0x812f3c: stur            w0, [x2, #0x33]
    //     0x812f40: ldurb           w16, [x2, #-1]
    //     0x812f44: ldurb           w17, [x0, #-1]
    //     0x812f48: and             x16, x17, x16, lsr #2
    //     0x812f4c: tst             x16, HEAP, lsr #32
    //     0x812f50: b.eq            #0x812f58
    //     0x812f54: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812f58: ldur            x0, [fp, #-0x18]
    // 0x812f5c: StoreField: r2->field_37 = r0
    //     0x812f5c: stur            w0, [x2, #0x37]
    //     0x812f60: ldurb           w16, [x2, #-1]
    //     0x812f64: ldurb           w17, [x0, #-1]
    //     0x812f68: and             x16, x17, x16, lsr #2
    //     0x812f6c: tst             x16, HEAP, lsr #32
    //     0x812f70: b.eq            #0x812f78
    //     0x812f74: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812f78: ldur            x0, [fp, #-0x20]
    // 0x812f7c: StoreField: r2->field_3b = r0
    //     0x812f7c: stur            w0, [x2, #0x3b]
    //     0x812f80: ldurb           w16, [x2, #-1]
    //     0x812f84: ldurb           w17, [x0, #-1]
    //     0x812f88: and             x16, x17, x16, lsr #2
    //     0x812f8c: tst             x16, HEAP, lsr #32
    //     0x812f90: b.eq            #0x812f98
    //     0x812f94: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x812f98: r0 = 1
    //     0x812f98: movz            x0, #0x1
    // 0x812f9c: StoreField: r2->field_23 = r0
    //     0x812f9c: stur            x0, [x2, #0x23]
    // 0x812fa0: r0 = Sentinel
    //     0x812fa0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x812fa4: StoreField: r2->field_2b = r0
    //     0x812fa4: stur            w0, [x2, #0x2b]
    // 0x812fa8: r1 = ""
    //     0x812fa8: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x812fac: r0 = StringExtension.obs()
    //     0x812fac: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x812fb0: ldur            x1, [fp, #-0x10]
    // 0x812fb4: StoreField: r1->field_2f = r0
    //     0x812fb4: stur            w0, [x1, #0x2f]
    //     0x812fb8: ldurb           w16, [x1, #-1]
    //     0x812fbc: ldurb           w17, [x0, #-1]
    //     0x812fc0: and             x16, x17, x16, lsr #2
    //     0x812fc4: tst             x16, HEAP, lsr #32
    //     0x812fc8: b.eq            #0x812fd0
    //     0x812fcc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x812fd0: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x812fd0: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x812fd4: r0 = Null
    //     0x812fd4: mov             x0, NULL
    // 0x812fd8: LeaveFrame
    //     0x812fd8: mov             SP, fp
    //     0x812fdc: ldp             fp, lr, [SP], #0x10
    // 0x812fe0: ret
    //     0x812fe0: ret             
    // 0x812fe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812fe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812fe8: b               #0x812efc
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9278a4, size: 0x4c
    // 0x9278a4: EnterFrame
    //     0x9278a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9278a8: mov             fp, SP
    // 0x9278ac: AllocStack(0x8)
    //     0x9278ac: sub             SP, SP, #8
    // 0x9278b0: SetupParameters(FaqListController this /* r1 => r0, fp-0x8 */)
    //     0x9278b0: mov             x0, x1
    //     0x9278b4: stur            x1, [fp, #-8]
    // 0x9278b8: CheckStackOverflow
    //     0x9278b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9278bc: cmp             SP, x16
    //     0x9278c0: b.ls            #0x9278e8
    // 0x9278c4: LoadField: r1 = r0->field_3f
    //     0x9278c4: ldur            w1, [x0, #0x3f]
    // 0x9278c8: DecompressPointer r1
    //     0x9278c8: add             x1, x1, HEAP, lsl #32
    // 0x9278cc: r0 = dispose()
    //     0x9278cc: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x9278d0: ldur            x1, [fp, #-8]
    // 0x9278d4: r0 = onClose()
    //     0x9278d4: bl              #0x927850  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onClose
    // 0x9278d8: r0 = Null
    //     0x9278d8: mov             x0, NULL
    // 0x9278dc: LeaveFrame
    //     0x9278dc: mov             SP, fp
    //     0x9278e0: ldp             fp, lr, [SP], #0x10
    // 0x9278e4: ret
    //     0x9278e4: ret             
    // 0x9278e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9278e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9278ec: b               #0x9278c4
  }
  [closure] void clear(dynamic) {
    // ** addr: 0xaf4e04, size: 0x38
    // 0xaf4e04: EnterFrame
    //     0xaf4e04: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4e08: mov             fp, SP
    // 0xaf4e0c: ldr             x0, [fp, #0x10]
    // 0xaf4e10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf4e10: ldur            w1, [x0, #0x17]
    // 0xaf4e14: DecompressPointer r1
    //     0xaf4e14: add             x1, x1, HEAP, lsl #32
    // 0xaf4e18: CheckStackOverflow
    //     0xaf4e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4e1c: cmp             SP, x16
    //     0xaf4e20: b.ls            #0xaf4e34
    // 0xaf4e24: r0 = clear()
    //     0xaf4e24: bl              #0xaf4e3c  ; [package:nuonline/app/modules/faq/controllers/faq_list_controller.dart] FaqListController::clear
    // 0xaf4e28: LeaveFrame
    //     0xaf4e28: mov             SP, fp
    //     0xaf4e2c: ldp             fp, lr, [SP], #0x10
    // 0xaf4e30: ret
    //     0xaf4e30: ret             
    // 0xaf4e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4e38: b               #0xaf4e24
  }
  _ clear(/* No info */) {
    // ** addr: 0xaf4e3c, size: 0x5c
    // 0xaf4e3c: EnterFrame
    //     0xaf4e3c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4e40: mov             fp, SP
    // 0xaf4e44: AllocStack(0x8)
    //     0xaf4e44: sub             SP, SP, #8
    // 0xaf4e48: SetupParameters(FaqListController this /* r1 => r0, fp-0x8 */)
    //     0xaf4e48: mov             x0, x1
    //     0xaf4e4c: stur            x1, [fp, #-8]
    // 0xaf4e50: CheckStackOverflow
    //     0xaf4e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4e54: cmp             SP, x16
    //     0xaf4e58: b.ls            #0xaf4e90
    // 0xaf4e5c: LoadField: r1 = r0->field_3f
    //     0xaf4e5c: ldur            w1, [x0, #0x3f]
    // 0xaf4e60: DecompressPointer r1
    //     0xaf4e60: add             x1, x1, HEAP, lsl #32
    // 0xaf4e64: r2 = ""
    //     0xaf4e64: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf4e68: r0 = text=()
    //     0xaf4e68: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xaf4e6c: ldur            x0, [fp, #-8]
    // 0xaf4e70: LoadField: r1 = r0->field_2f
    //     0xaf4e70: ldur            w1, [x0, #0x2f]
    // 0xaf4e74: DecompressPointer r1
    //     0xaf4e74: add             x1, x1, HEAP, lsl #32
    // 0xaf4e78: r2 = ""
    //     0xaf4e78: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf4e7c: r0 = value=()
    //     0xaf4e7c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf4e80: r0 = Null
    //     0xaf4e80: mov             x0, NULL
    // 0xaf4e84: LeaveFrame
    //     0xaf4e84: mov             SP, fp
    //     0xaf4e88: ldp             fp, lr, [SP], #0x10
    // 0xaf4e8c: ret
    //     0xaf4e8c: ret             
    // 0xaf4e90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4e90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4e94: b               #0xaf4e5c
  }
}
