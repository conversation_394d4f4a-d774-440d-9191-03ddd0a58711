// lib: , url: package:nuonline/app/modules/faq/controllers/faq_search_controller.dart

// class id: 1050273, size: 0x8
class :: {
}

// class id: 1935, size: 0x3c, field offset: 0x34
class FaqSearchController extends PaginatedFetchController<dynamic> {

  _ onFetchRequested(/* No info */) async {
    // ** addr: 0x7e8df4, size: 0x70
    // 0x7e8df4: EnterFrame
    //     0x7e8df4: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8df8: mov             fp, SP
    // 0x7e8dfc: AllocStack(0x20)
    //     0x7e8dfc: sub             SP, SP, #0x20
    // 0x7e8e00: SetupParameters(FaqSearchController this /* r1 => r1, fp-0x10 */)
    //     0x7e8e00: stur            NULL, [fp, #-8]
    //     0x7e8e04: stur            x1, [fp, #-0x10]
    // 0x7e8e08: CheckStackOverflow
    //     0x7e8e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8e0c: cmp             SP, x16
    //     0x7e8e10: b.ls            #0x7e8e5c
    // 0x7e8e14: InitAsync() -> Future<ApiResult<List<Faq>>>
    //     0x7e8e14: add             x0, PP, #0x34, lsl #12  ; [pp+0x34878] TypeArguments: <ApiResult<List<Faq>>>
    //     0x7e8e18: ldr             x0, [x0, #0x878]
    //     0x7e8e1c: bl              #0x661298  ; InitAsyncStub
    // 0x7e8e20: ldur            x0, [fp, #-0x10]
    // 0x7e8e24: LoadField: r2 = r0->field_33
    //     0x7e8e24: ldur            w2, [x0, #0x33]
    // 0x7e8e28: DecompressPointer r2
    //     0x7e8e28: add             x2, x2, HEAP, lsl #32
    // 0x7e8e2c: stur            x2, [fp, #-0x20]
    // 0x7e8e30: LoadField: r3 = r0->field_23
    //     0x7e8e30: ldur            x3, [x0, #0x23]
    // 0x7e8e34: stur            x3, [fp, #-0x18]
    // 0x7e8e38: LoadField: r1 = r0->field_2f
    //     0x7e8e38: ldur            w1, [x0, #0x2f]
    // 0x7e8e3c: DecompressPointer r1
    //     0x7e8e3c: add             x1, x1, HEAP, lsl #32
    // 0x7e8e40: r0 = value()
    //     0x7e8e40: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7e8e44: ldur            x1, [fp, #-0x20]
    // 0x7e8e48: ldur            x2, [fp, #-0x18]
    // 0x7e8e4c: mov             x3, x0
    // 0x7e8e50: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7e8e50: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7e8e54: r0 = findAll()
    //     0x7e8e54: bl              #0x7e8320  ; [package:nuonline/app/data/repositories/faq_repository.dart] FaqRepository::findAll
    // 0x7e8e58: r0 = ReturnAsync()
    //     0x7e8e58: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7e8e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8e60: b               #0x7e8e14
  }
  _ FaqSearchController(/* No info */) {
    // ** addr: 0x81320c, size: 0xd8
    // 0x81320c: EnterFrame
    //     0x81320c: stp             fp, lr, [SP, #-0x10]!
    //     0x813210: mov             fp, SP
    // 0x813214: AllocStack(0x18)
    //     0x813214: sub             SP, SP, #0x18
    // 0x813218: SetupParameters(FaqSearchController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x813218: mov             x0, x2
    //     0x81321c: stur            x2, [fp, #-0x10]
    //     0x813220: mov             x2, x1
    //     0x813224: stur            x1, [fp, #-8]
    // 0x813228: CheckStackOverflow
    //     0x813228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81322c: cmp             SP, x16
    //     0x813230: b.ls            #0x8132dc
    // 0x813234: r1 = <TextEditingValue>
    //     0x813234: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x813238: r0 = TextEditingController()
    //     0x813238: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x81323c: mov             x1, x0
    // 0x813240: stur            x0, [fp, #-0x18]
    // 0x813244: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x813244: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x813248: r0 = TextEditingController()
    //     0x813248: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x81324c: ldur            x0, [fp, #-0x18]
    // 0x813250: ldur            x2, [fp, #-8]
    // 0x813254: StoreField: r2->field_37 = r0
    //     0x813254: stur            w0, [x2, #0x37]
    //     0x813258: ldurb           w16, [x2, #-1]
    //     0x81325c: ldurb           w17, [x0, #-1]
    //     0x813260: and             x16, x17, x16, lsr #2
    //     0x813264: tst             x16, HEAP, lsr #32
    //     0x813268: b.eq            #0x813270
    //     0x81326c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x813270: ldur            x0, [fp, #-0x10]
    // 0x813274: StoreField: r2->field_33 = r0
    //     0x813274: stur            w0, [x2, #0x33]
    //     0x813278: ldurb           w16, [x2, #-1]
    //     0x81327c: ldurb           w17, [x0, #-1]
    //     0x813280: and             x16, x17, x16, lsr #2
    //     0x813284: tst             x16, HEAP, lsr #32
    //     0x813288: b.eq            #0x813290
    //     0x81328c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x813290: r0 = 1
    //     0x813290: movz            x0, #0x1
    // 0x813294: StoreField: r2->field_23 = r0
    //     0x813294: stur            x0, [x2, #0x23]
    // 0x813298: r0 = Sentinel
    //     0x813298: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81329c: StoreField: r2->field_2b = r0
    //     0x81329c: stur            w0, [x2, #0x2b]
    // 0x8132a0: r1 = ""
    //     0x8132a0: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8132a4: r0 = StringExtension.obs()
    //     0x8132a4: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x8132a8: ldur            x1, [fp, #-8]
    // 0x8132ac: StoreField: r1->field_2f = r0
    //     0x8132ac: stur            w0, [x1, #0x2f]
    //     0x8132b0: ldurb           w16, [x1, #-1]
    //     0x8132b4: ldurb           w17, [x0, #-1]
    //     0x8132b8: and             x16, x17, x16, lsr #2
    //     0x8132bc: tst             x16, HEAP, lsr #32
    //     0x8132c0: b.eq            #0x8132c8
    //     0x8132c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8132c8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x8132c8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x8132cc: r0 = Null
    //     0x8132cc: mov             x0, NULL
    // 0x8132d0: LeaveFrame
    //     0x8132d0: mov             SP, fp
    //     0x8132d4: ldp             fp, lr, [SP], #0x10
    // 0x8132d8: ret
    //     0x8132d8: ret             
    // 0x8132dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8132dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8132e0: b               #0x813234
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9278f0, size: 0x4c
    // 0x9278f0: EnterFrame
    //     0x9278f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9278f4: mov             fp, SP
    // 0x9278f8: AllocStack(0x8)
    //     0x9278f8: sub             SP, SP, #8
    // 0x9278fc: SetupParameters(FaqSearchController this /* r1 => r0, fp-0x8 */)
    //     0x9278fc: mov             x0, x1
    //     0x927900: stur            x1, [fp, #-8]
    // 0x927904: CheckStackOverflow
    //     0x927904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927908: cmp             SP, x16
    //     0x92790c: b.ls            #0x927934
    // 0x927910: LoadField: r1 = r0->field_37
    //     0x927910: ldur            w1, [x0, #0x37]
    // 0x927914: DecompressPointer r1
    //     0x927914: add             x1, x1, HEAP, lsl #32
    // 0x927918: r0 = dispose()
    //     0x927918: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x92791c: ldur            x1, [fp, #-8]
    // 0x927920: r0 = onClose()
    //     0x927920: bl              #0x927850  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onClose
    // 0x927924: r0 = Null
    //     0x927924: mov             x0, NULL
    // 0x927928: LeaveFrame
    //     0x927928: mov             SP, fp
    //     0x92792c: ldp             fp, lr, [SP], #0x10
    // 0x927930: ret
    //     0x927930: ret             
    // 0x927934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927938: b               #0x927910
  }
  [closure] void clear(dynamic) {
    // ** addr: 0xaf53b4, size: 0x38
    // 0xaf53b4: EnterFrame
    //     0xaf53b4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf53b8: mov             fp, SP
    // 0xaf53bc: ldr             x0, [fp, #0x10]
    // 0xaf53c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf53c0: ldur            w1, [x0, #0x17]
    // 0xaf53c4: DecompressPointer r1
    //     0xaf53c4: add             x1, x1, HEAP, lsl #32
    // 0xaf53c8: CheckStackOverflow
    //     0xaf53c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf53cc: cmp             SP, x16
    //     0xaf53d0: b.ls            #0xaf53e4
    // 0xaf53d4: r0 = clear()
    //     0xaf53d4: bl              #0xaf53ec  ; [package:nuonline/app/modules/faq/controllers/faq_search_controller.dart] FaqSearchController::clear
    // 0xaf53d8: LeaveFrame
    //     0xaf53d8: mov             SP, fp
    //     0xaf53dc: ldp             fp, lr, [SP], #0x10
    // 0xaf53e0: ret
    //     0xaf53e0: ret             
    // 0xaf53e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf53e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf53e8: b               #0xaf53d4
  }
  _ clear(/* No info */) {
    // ** addr: 0xaf53ec, size: 0x5c
    // 0xaf53ec: EnterFrame
    //     0xaf53ec: stp             fp, lr, [SP, #-0x10]!
    //     0xaf53f0: mov             fp, SP
    // 0xaf53f4: AllocStack(0x8)
    //     0xaf53f4: sub             SP, SP, #8
    // 0xaf53f8: SetupParameters(FaqSearchController this /* r1 => r0, fp-0x8 */)
    //     0xaf53f8: mov             x0, x1
    //     0xaf53fc: stur            x1, [fp, #-8]
    // 0xaf5400: CheckStackOverflow
    //     0xaf5400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5404: cmp             SP, x16
    //     0xaf5408: b.ls            #0xaf5440
    // 0xaf540c: LoadField: r1 = r0->field_37
    //     0xaf540c: ldur            w1, [x0, #0x37]
    // 0xaf5410: DecompressPointer r1
    //     0xaf5410: add             x1, x1, HEAP, lsl #32
    // 0xaf5414: r2 = ""
    //     0xaf5414: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf5418: r0 = text=()
    //     0xaf5418: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xaf541c: ldur            x0, [fp, #-8]
    // 0xaf5420: LoadField: r1 = r0->field_2f
    //     0xaf5420: ldur            w1, [x0, #0x2f]
    // 0xaf5424: DecompressPointer r1
    //     0xaf5424: add             x1, x1, HEAP, lsl #32
    // 0xaf5428: r2 = ""
    //     0xaf5428: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xaf542c: r0 = value=()
    //     0xaf542c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf5430: r0 = Null
    //     0xaf5430: mov             x0, NULL
    // 0xaf5434: LeaveFrame
    //     0xaf5434: mov             SP, fp
    //     0xaf5438: ldp             fp, lr, [SP], #0x10
    // 0xaf543c: ret
    //     0xaf543c: ret             
    // 0xaf5440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5444: b               #0xaf540c
  }
}
