// lib: , url: package:nuonline/app/modules/faq/views/faq_detail_view.dart

// class id: 1050274, size: 0x8
class :: {
}

// class id: 5011, size: 0x10, field offset: 0xc
//   const constructor, 
class FaqDetailView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb98364, size: 0x138
    // 0xb98364: EnterFrame
    //     0xb98364: stp             fp, lr, [SP, #-0x10]!
    //     0xb98368: mov             fp, SP
    // 0xb9836c: AllocStack(0x30)
    //     0xb9836c: sub             SP, SP, #0x30
    // 0xb98370: SetupParameters(FaqDetailView this /* r1 => r1, fp-0x8 */)
    //     0xb98370: stur            x1, [fp, #-8]
    // 0xb98374: CheckStackOverflow
    //     0xb98374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb98378: cmp             SP, x16
    //     0xb9837c: b.ls            #0xb98494
    // 0xb98380: r1 = 1
    //     0xb98380: movz            x1, #0x1
    // 0xb98384: r0 = AllocateContext()
    //     0xb98384: bl              #0xec126c  ; AllocateContextStub
    // 0xb98388: mov             x1, x0
    // 0xb9838c: ldur            x0, [fp, #-8]
    // 0xb98390: stur            x1, [fp, #-0x10]
    // 0xb98394: StoreField: r1->field_f = r0
    //     0xb98394: stur            w0, [x1, #0xf]
    // 0xb98398: r0 = AppBar()
    //     0xb98398: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb9839c: mov             x1, x0
    // 0xb983a0: stur            x0, [fp, #-8]
    // 0xb983a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb983a4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb983a8: r0 = AppBar()
    //     0xb983a8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb983ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb983ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb983b0: ldr             x0, [x0, #0x2670]
    //     0xb983b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb983b8: cmp             w0, w16
    //     0xb983bc: b.ne            #0xb983c8
    //     0xb983c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb983c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb983c8: r16 = <AppStorage>
    //     0xb983c8: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0xb983cc: r30 = "app_storage"
    //     0xb983cc: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0xb983d0: stp             lr, x16, [SP]
    // 0xb983d4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb983d4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb983d8: r0 = Inst.find()
    //     0xb983d8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb983dc: stur            x0, [fp, #-0x18]
    // 0xb983e0: r0 = ReadingPreferenceController()
    //     0xb983e0: bl              #0x8425f4  ; AllocateReadingPreferenceControllerStub -> ReadingPreferenceController (size=0x30)
    // 0xb983e4: mov             x1, x0
    // 0xb983e8: ldur            x2, [fp, #-0x18]
    // 0xb983ec: stur            x0, [fp, #-0x18]
    // 0xb983f0: r0 = ReadingPreferenceController()
    //     0xb983f0: bl              #0x8424a4  ; [package:nuonline/app/modules/setting/reading_preference/controllers/reading_preference_controller.dart] ReadingPreferenceController::ReadingPreferenceController
    // 0xb983f4: r1 = <ReadingPreferenceController>
    //     0xb983f4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb983f8: ldr             x1, [x1, #0xe10]
    // 0xb983fc: r0 = GetBuilder()
    //     0xb983fc: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb98400: mov             x3, x0
    // 0xb98404: ldur            x0, [fp, #-0x18]
    // 0xb98408: stur            x3, [fp, #-0x20]
    // 0xb9840c: StoreField: r3->field_3b = r0
    //     0xb9840c: stur            w0, [x3, #0x3b]
    // 0xb98410: r0 = true
    //     0xb98410: add             x0, NULL, #0x20  ; true
    // 0xb98414: StoreField: r3->field_13 = r0
    //     0xb98414: stur            w0, [x3, #0x13]
    // 0xb98418: ldur            x2, [fp, #-0x10]
    // 0xb9841c: r1 = Function '<anonymous closure>':.
    //     0xb9841c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34848] AnonymousClosure: (0xb9849c), in [package:nuonline/app/modules/faq/views/faq_detail_view.dart] FaqDetailView::build (0xb98364)
    //     0xb98420: ldr             x1, [x1, #0x848]
    // 0xb98424: r0 = AllocateClosure()
    //     0xb98424: bl              #0xec1630  ; AllocateClosureStub
    // 0xb98428: mov             x1, x0
    // 0xb9842c: ldur            x0, [fp, #-0x20]
    // 0xb98430: StoreField: r0->field_f = r1
    //     0xb98430: stur            w1, [x0, #0xf]
    // 0xb98434: r1 = true
    //     0xb98434: add             x1, NULL, #0x20  ; true
    // 0xb98438: StoreField: r0->field_1f = r1
    //     0xb98438: stur            w1, [x0, #0x1f]
    // 0xb9843c: r2 = false
    //     0xb9843c: add             x2, NULL, #0x30  ; false
    // 0xb98440: StoreField: r0->field_23 = r2
    //     0xb98440: stur            w2, [x0, #0x23]
    // 0xb98444: r0 = Scaffold()
    //     0xb98444: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb98448: ldur            x1, [fp, #-8]
    // 0xb9844c: StoreField: r0->field_13 = r1
    //     0xb9844c: stur            w1, [x0, #0x13]
    // 0xb98450: ldur            x1, [fp, #-0x20]
    // 0xb98454: ArrayStore: r0[0] = r1  ; List_4
    //     0xb98454: stur            w1, [x0, #0x17]
    // 0xb98458: r1 = Instance_AlignmentDirectional
    //     0xb98458: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb9845c: ldr             x1, [x1, #0x758]
    // 0xb98460: StoreField: r0->field_2b = r1
    //     0xb98460: stur            w1, [x0, #0x2b]
    // 0xb98464: r1 = true
    //     0xb98464: add             x1, NULL, #0x20  ; true
    // 0xb98468: StoreField: r0->field_53 = r1
    //     0xb98468: stur            w1, [x0, #0x53]
    // 0xb9846c: r2 = Instance_DragStartBehavior
    //     0xb9846c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb98470: StoreField: r0->field_57 = r2
    //     0xb98470: stur            w2, [x0, #0x57]
    // 0xb98474: r2 = false
    //     0xb98474: add             x2, NULL, #0x30  ; false
    // 0xb98478: StoreField: r0->field_b = r2
    //     0xb98478: stur            w2, [x0, #0xb]
    // 0xb9847c: StoreField: r0->field_f = r2
    //     0xb9847c: stur            w2, [x0, #0xf]
    // 0xb98480: StoreField: r0->field_5f = r1
    //     0xb98480: stur            w1, [x0, #0x5f]
    // 0xb98484: StoreField: r0->field_63 = r1
    //     0xb98484: stur            w1, [x0, #0x63]
    // 0xb98488: LeaveFrame
    //     0xb98488: mov             SP, fp
    //     0xb9848c: ldp             fp, lr, [SP], #0x10
    // 0xb98490: ret
    //     0xb98490: ret             
    // 0xb98494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb98494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb98498: b               #0xb98380
  }
  [closure] ReadingPreferenceWidget <anonymous closure>(dynamic, ReadingPreferenceController) {
    // ** addr: 0xb9849c, size: 0x48
    // 0xb9849c: EnterFrame
    //     0xb9849c: stp             fp, lr, [SP, #-0x10]!
    //     0xb984a0: mov             fp, SP
    // 0xb984a4: AllocStack(0x8)
    //     0xb984a4: sub             SP, SP, #8
    // 0xb984a8: SetupParameters()
    //     0xb984a8: ldr             x0, [fp, #0x18]
    //     0xb984ac: ldur            w2, [x0, #0x17]
    //     0xb984b0: add             x2, x2, HEAP, lsl #32
    // 0xb984b4: r1 = Function '<anonymous closure>':.
    //     0xb984b4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34850] AnonymousClosure: (0xb984e4), in [package:nuonline/app/modules/faq/views/faq_detail_view.dart] FaqDetailView::build (0xb98364)
    //     0xb984b8: ldr             x1, [x1, #0x850]
    // 0xb984bc: r0 = AllocateClosure()
    //     0xb984bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb984c0: r1 = <ReadingPreferenceController>
    //     0xb984c0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb984c4: ldr             x1, [x1, #0xe10]
    // 0xb984c8: stur            x0, [fp, #-8]
    // 0xb984cc: r0 = ReadingPreferenceWidget()
    //     0xb984cc: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb984d0: ldur            x1, [fp, #-8]
    // 0xb984d4: StoreField: r0->field_13 = r1
    //     0xb984d4: stur            w1, [x0, #0x13]
    // 0xb984d8: LeaveFrame
    //     0xb984d8: mov             SP, fp
    //     0xb984dc: ldp             fp, lr, [SP], #0x10
    // 0xb984e0: ret
    //     0xb984e0: ret             
  }
  [closure] ListView <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb984e4, size: 0x30c
    // 0xb984e4: EnterFrame
    //     0xb984e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb984e8: mov             fp, SP
    // 0xb984ec: AllocStack(0x40)
    //     0xb984ec: sub             SP, SP, #0x40
    // 0xb984f0: SetupParameters()
    //     0xb984f0: ldr             x0, [fp, #0x20]
    //     0xb984f4: ldur            w1, [x0, #0x17]
    //     0xb984f8: add             x1, x1, HEAP, lsl #32
    //     0xb984fc: stur            x1, [fp, #-8]
    // 0xb98500: CheckStackOverflow
    //     0xb98500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb98504: cmp             SP, x16
    //     0xb98508: b.ls            #0xb987c8
    // 0xb9850c: LoadField: r0 = r1->field_f
    //     0xb9850c: ldur            w0, [x1, #0xf]
    // 0xb98510: DecompressPointer r0
    //     0xb98510: add             x0, x0, HEAP, lsl #32
    // 0xb98514: LoadField: r2 = r0->field_b
    //     0xb98514: ldur            w2, [x0, #0xb]
    // 0xb98518: DecompressPointer r2
    //     0xb98518: add             x2, x2, HEAP, lsl #32
    // 0xb9851c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb9851c: ldur            w0, [x2, #0x17]
    // 0xb98520: DecompressPointer r0
    //     0xb98520: add             x0, x0, HEAP, lsl #32
    // 0xb98524: LoadField: r2 = r0->field_f
    //     0xb98524: ldur            w2, [x0, #0xf]
    // 0xb98528: DecompressPointer r2
    //     0xb98528: add             x2, x2, HEAP, lsl #32
    // 0xb9852c: r0 = LoadClassIdInstr(r2)
    //     0xb9852c: ldur            x0, [x2, #-1]
    //     0xb98530: ubfx            x0, x0, #0xc, #0x14
    // 0xb98534: str             x2, [SP]
    // 0xb98538: r0 = GDT[cid_x0 + -0xff6]()
    //     0xb98538: sub             lr, x0, #0xff6
    //     0xb9853c: ldr             lr, [x21, lr, lsl #3]
    //     0xb98540: blr             lr
    // 0xb98544: stur            x0, [fp, #-0x10]
    // 0xb98548: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb98548: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9854c: ldr             x0, [x0, #0x2670]
    //     0xb98550: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb98554: cmp             w0, w16
    //     0xb98558: b.ne            #0xb98564
    //     0xb9855c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb98560: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb98564: r0 = GetNavigation.textTheme()
    //     0xb98564: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb98568: LoadField: r1 = r0->field_f
    //     0xb98568: ldur            w1, [x0, #0xf]
    // 0xb9856c: DecompressPointer r1
    //     0xb9856c: add             x1, x1, HEAP, lsl #32
    // 0xb98570: stur            x1, [fp, #-0x18]
    // 0xb98574: cmp             w1, NULL
    // 0xb98578: b.eq            #0xb987d0
    // 0xb9857c: r0 = GetNavigation.theme()
    //     0xb9857c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb98580: LoadField: r1 = r0->field_3f
    //     0xb98580: ldur            w1, [x0, #0x3f]
    // 0xb98584: DecompressPointer r1
    //     0xb98584: add             x1, x1, HEAP, lsl #32
    // 0xb98588: LoadField: r0 = r1->field_2b
    //     0xb98588: ldur            w0, [x1, #0x2b]
    // 0xb9858c: DecompressPointer r0
    //     0xb9858c: add             x0, x0, HEAP, lsl #32
    // 0xb98590: r16 = 14.000000
    //     0xb98590: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb98594: ldr             x16, [x16, #0x9a0]
    // 0xb98598: stp             x0, x16, [SP]
    // 0xb9859c: ldur            x1, [fp, #-0x18]
    // 0xb985a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb985a0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb985a4: ldr             x4, [x4, #0xaa0]
    // 0xb985a8: r0 = copyWith()
    //     0xb985a8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb985ac: stur            x0, [fp, #-0x18]
    // 0xb985b0: r0 = Text()
    //     0xb985b0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb985b4: mov             x1, x0
    // 0xb985b8: ldur            x0, [fp, #-0x10]
    // 0xb985bc: stur            x1, [fp, #-0x20]
    // 0xb985c0: StoreField: r1->field_b = r0
    //     0xb985c0: stur            w0, [x1, #0xb]
    // 0xb985c4: ldur            x0, [fp, #-0x18]
    // 0xb985c8: StoreField: r1->field_13 = r0
    //     0xb985c8: stur            w0, [x1, #0x13]
    // 0xb985cc: ldur            x0, [fp, #-8]
    // 0xb985d0: LoadField: r2 = r0->field_f
    //     0xb985d0: ldur            w2, [x0, #0xf]
    // 0xb985d4: DecompressPointer r2
    //     0xb985d4: add             x2, x2, HEAP, lsl #32
    // 0xb985d8: LoadField: r3 = r2->field_b
    //     0xb985d8: ldur            w3, [x2, #0xb]
    // 0xb985dc: DecompressPointer r3
    //     0xb985dc: add             x3, x3, HEAP, lsl #32
    // 0xb985e0: LoadField: r2 = r3->field_f
    //     0xb985e0: ldur            w2, [x3, #0xf]
    // 0xb985e4: DecompressPointer r2
    //     0xb985e4: add             x2, x2, HEAP, lsl #32
    // 0xb985e8: stur            x2, [fp, #-0x10]
    // 0xb985ec: r0 = GetNavigation.textTheme()
    //     0xb985ec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb985f0: LoadField: r1 = r0->field_f
    //     0xb985f0: ldur            w1, [x0, #0xf]
    // 0xb985f4: DecompressPointer r1
    //     0xb985f4: add             x1, x1, HEAP, lsl #32
    // 0xb985f8: cmp             w1, NULL
    // 0xb985fc: b.ne            #0xb98608
    // 0xb98600: r4 = Null
    //     0xb98600: mov             x4, NULL
    // 0xb98604: b               #0xb9864c
    // 0xb98608: ldr             x0, [fp, #0x18]
    // 0xb9860c: LoadField: d0 = r0->field_f
    //     0xb9860c: ldur            d0, [x0, #0xf]
    // 0xb98610: r2 = inline_Allocate_Double()
    //     0xb98610: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb98614: add             x2, x2, #0x10
    //     0xb98618: cmp             x3, x2
    //     0xb9861c: b.ls            #0xb987d4
    //     0xb98620: str             x2, [THR, #0x50]  ; THR::top
    //     0xb98624: sub             x2, x2, #0xf
    //     0xb98628: movz            x3, #0xe15c
    //     0xb9862c: movk            x3, #0x3, lsl #16
    //     0xb98630: stur            x3, [x2, #-1]
    // 0xb98634: StoreField: r2->field_7 = d0
    //     0xb98634: stur            d0, [x2, #7]
    // 0xb98638: str             x2, [SP]
    // 0xb9863c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb9863c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb98640: ldr             x4, [x4, #0x88]
    // 0xb98644: r0 = copyWith()
    //     0xb98644: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb98648: mov             x4, x0
    // 0xb9864c: ldr             x0, [fp, #0x18]
    // 0xb98650: ldur            x2, [fp, #-8]
    // 0xb98654: ldur            x1, [fp, #-0x20]
    // 0xb98658: ldur            x3, [fp, #-0x10]
    // 0xb9865c: stur            x4, [fp, #-0x18]
    // 0xb98660: r0 = Text()
    //     0xb98660: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb98664: mov             x1, x0
    // 0xb98668: ldur            x0, [fp, #-0x10]
    // 0xb9866c: stur            x1, [fp, #-0x28]
    // 0xb98670: StoreField: r1->field_b = r0
    //     0xb98670: stur            w0, [x1, #0xb]
    // 0xb98674: ldur            x0, [fp, #-0x18]
    // 0xb98678: StoreField: r1->field_13 = r0
    //     0xb98678: stur            w0, [x1, #0x13]
    // 0xb9867c: ldur            x0, [fp, #-8]
    // 0xb98680: LoadField: r2 = r0->field_f
    //     0xb98680: ldur            w2, [x0, #0xf]
    // 0xb98684: DecompressPointer r2
    //     0xb98684: add             x2, x2, HEAP, lsl #32
    // 0xb98688: LoadField: r0 = r2->field_b
    //     0xb98688: ldur            w0, [x2, #0xb]
    // 0xb9868c: DecompressPointer r0
    //     0xb9868c: add             x0, x0, HEAP, lsl #32
    // 0xb98690: stur            x0, [fp, #-0x10]
    // 0xb98694: LoadField: r2 = r0->field_13
    //     0xb98694: ldur            w2, [x0, #0x13]
    // 0xb98698: DecompressPointer r2
    //     0xb98698: add             x2, x2, HEAP, lsl #32
    // 0xb9869c: stur            x2, [fp, #-8]
    // 0xb986a0: r0 = ArticleContentHtml()
    //     0xb986a0: bl              #0xa36cb4  ; AllocateArticleContentHtmlStub -> ArticleContentHtml (size=0x30)
    // 0xb986a4: mov             x1, x0
    // 0xb986a8: ldur            x0, [fp, #-8]
    // 0xb986ac: stur            x1, [fp, #-0x30]
    // 0xb986b0: StoreField: r1->field_b = r0
    //     0xb986b0: stur            w0, [x1, #0xb]
    // 0xb986b4: ldr             x0, [fp, #0x18]
    // 0xb986b8: StoreField: r1->field_f = r0
    //     0xb986b8: stur            w0, [x1, #0xf]
    // 0xb986bc: r0 = true
    //     0xb986bc: add             x0, NULL, #0x20  ; true
    // 0xb986c0: StoreField: r1->field_13 = r0
    //     0xb986c0: stur            w0, [x1, #0x13]
    // 0xb986c4: r0 = false
    //     0xb986c4: add             x0, NULL, #0x30  ; false
    // 0xb986c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb986c8: stur            w0, [x1, #0x17]
    // 0xb986cc: r2 = const []
    //     0xb986cc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d530] List<ArticleInsertion>(0)
    //     0xb986d0: ldr             x2, [x2, #0x530]
    // 0xb986d4: StoreField: r1->field_23 = r2
    //     0xb986d4: stur            w2, [x1, #0x23]
    // 0xb986d8: StoreField: r1->field_27 = r2
    //     0xb986d8: stur            w2, [x1, #0x27]
    // 0xb986dc: r2 = const []
    //     0xb986dc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d538] List<Author>(0)
    //     0xb986e0: ldr             x2, [x2, #0x538]
    // 0xb986e4: StoreField: r1->field_1f = r2
    //     0xb986e4: stur            w2, [x1, #0x1f]
    // 0xb986e8: StoreField: r1->field_2b = r0
    //     0xb986e8: stur            w0, [x1, #0x2b]
    // 0xb986ec: ldur            x0, [fp, #-0x10]
    // 0xb986f0: LoadField: r2 = r0->field_1b
    //     0xb986f0: ldur            w2, [x0, #0x1b]
    // 0xb986f4: DecompressPointer r2
    //     0xb986f4: add             x2, x2, HEAP, lsl #32
    // 0xb986f8: stur            x2, [fp, #-0x18]
    // 0xb986fc: LoadField: r3 = r0->field_1f
    //     0xb986fc: ldur            w3, [x0, #0x1f]
    // 0xb98700: DecompressPointer r3
    //     0xb98700: add             x3, x3, HEAP, lsl #32
    // 0xb98704: stur            x3, [fp, #-8]
    // 0xb98708: r0 = RelatedContentWidget()
    //     0xb98708: bl              #0xaf9214  ; AllocateRelatedContentWidgetStub -> RelatedContentWidget (size=0x18)
    // 0xb9870c: mov             x3, x0
    // 0xb98710: ldur            x0, [fp, #-0x18]
    // 0xb98714: stur            x3, [fp, #-0x10]
    // 0xb98718: StoreField: r3->field_b = r0
    //     0xb98718: stur            w0, [x3, #0xb]
    // 0xb9871c: ldur            x0, [fp, #-8]
    // 0xb98720: StoreField: r3->field_f = r0
    //     0xb98720: stur            w0, [x3, #0xf]
    // 0xb98724: r0 = Instance_EdgeInsets
    //     0xb98724: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb98728: StoreField: r3->field_13 = r0
    //     0xb98728: stur            w0, [x3, #0x13]
    // 0xb9872c: r1 = Null
    //     0xb9872c: mov             x1, NULL
    // 0xb98730: r2 = 10
    //     0xb98730: movz            x2, #0xa
    // 0xb98734: r0 = AllocateArray()
    //     0xb98734: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb98738: mov             x2, x0
    // 0xb9873c: ldur            x0, [fp, #-0x20]
    // 0xb98740: stur            x2, [fp, #-8]
    // 0xb98744: StoreField: r2->field_f = r0
    //     0xb98744: stur            w0, [x2, #0xf]
    // 0xb98748: r16 = Instance_SizedBox
    //     0xb98748: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb9874c: ldr             x16, [x16, #0xfb0]
    // 0xb98750: StoreField: r2->field_13 = r16
    //     0xb98750: stur            w16, [x2, #0x13]
    // 0xb98754: ldur            x0, [fp, #-0x28]
    // 0xb98758: ArrayStore: r2[0] = r0  ; List_4
    //     0xb98758: stur            w0, [x2, #0x17]
    // 0xb9875c: ldur            x0, [fp, #-0x30]
    // 0xb98760: StoreField: r2->field_1b = r0
    //     0xb98760: stur            w0, [x2, #0x1b]
    // 0xb98764: ldur            x0, [fp, #-0x10]
    // 0xb98768: StoreField: r2->field_1f = r0
    //     0xb98768: stur            w0, [x2, #0x1f]
    // 0xb9876c: r1 = <Widget>
    //     0xb9876c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb98770: r0 = AllocateGrowableArray()
    //     0xb98770: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb98774: mov             x1, x0
    // 0xb98778: ldur            x0, [fp, #-8]
    // 0xb9877c: stur            x1, [fp, #-0x10]
    // 0xb98780: StoreField: r1->field_f = r0
    //     0xb98780: stur            w0, [x1, #0xf]
    // 0xb98784: r0 = 10
    //     0xb98784: movz            x0, #0xa
    // 0xb98788: StoreField: r1->field_b = r0
    //     0xb98788: stur            w0, [x1, #0xb]
    // 0xb9878c: r0 = ListView()
    //     0xb9878c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb98790: stur            x0, [fp, #-8]
    // 0xb98794: r16 = Instance_ClampingScrollPhysics
    //     0xb98794: add             x16, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xb98798: ldr             x16, [x16, #0x410]
    // 0xb9879c: r30 = Instance_EdgeInsets
    //     0xb9879c: ldr             lr, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb987a0: stp             lr, x16, [SP]
    // 0xb987a4: mov             x1, x0
    // 0xb987a8: ldur            x2, [fp, #-0x10]
    // 0xb987ac: r4 = const [0, 0x4, 0x2, 0x2, padding, 0x3, physics, 0x2, null]
    //     0xb987ac: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b350] List(9) [0, 0x4, 0x2, 0x2, "padding", 0x3, "physics", 0x2, Null]
    //     0xb987b0: ldr             x4, [x4, #0x350]
    // 0xb987b4: r0 = ListView()
    //     0xb987b4: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb987b8: ldur            x0, [fp, #-8]
    // 0xb987bc: LeaveFrame
    //     0xb987bc: mov             SP, fp
    //     0xb987c0: ldp             fp, lr, [SP], #0x10
    // 0xb987c4: ret
    //     0xb987c4: ret             
    // 0xb987c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb987c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb987cc: b               #0xb9850c
    // 0xb987d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb987d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb987d4: SaveReg d0
    //     0xb987d4: str             q0, [SP, #-0x10]!
    // 0xb987d8: stp             x0, x1, [SP, #-0x10]!
    // 0xb987dc: r0 = AllocateDouble()
    //     0xb987dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb987e0: mov             x2, x0
    // 0xb987e4: ldp             x0, x1, [SP], #0x10
    // 0xb987e8: RestoreReg d0
    //     0xb987e8: ldr             q0, [SP], #0x10
    // 0xb987ec: b               #0xb98634
  }
}
