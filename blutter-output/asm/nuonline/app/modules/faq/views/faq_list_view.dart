// lib: , url: package:nuonline/app/modules/faq/views/faq_list_view.dart

// class id: 1050275, size: 0x8
class :: {
}

// class id: 5277, size: 0x14, field offset: 0x14
//   const constructor, 
class FaqListView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf4144, size: 0x214
    // 0xaf4144: EnterFrame
    //     0xaf4144: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4148: mov             fp, SP
    // 0xaf414c: AllocStack(0x28)
    //     0xaf414c: sub             SP, SP, #0x28
    // 0xaf4150: SetupParameters(FaqListView this /* r1 => r1, fp-0x8 */)
    //     0xaf4150: stur            x1, [fp, #-8]
    // 0xaf4154: CheckStackOverflow
    //     0xaf4154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4158: cmp             SP, x16
    //     0xaf415c: b.ls            #0xaf4350
    // 0xaf4160: r1 = 1
    //     0xaf4160: movz            x1, #0x1
    // 0xaf4164: r0 = AllocateContext()
    //     0xaf4164: bl              #0xec126c  ; AllocateContextStub
    // 0xaf4168: mov             x2, x0
    // 0xaf416c: ldur            x0, [fp, #-8]
    // 0xaf4170: stur            x2, [fp, #-0x10]
    // 0xaf4174: StoreField: r2->field_f = r0
    //     0xaf4174: stur            w0, [x2, #0xf]
    // 0xaf4178: mov             x1, x0
    // 0xaf417c: r0 = controller()
    //     0xaf417c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4180: LoadField: r1 = r0->field_33
    //     0xaf4180: ldur            w1, [x0, #0x33]
    // 0xaf4184: DecompressPointer r1
    //     0xaf4184: add             x1, x1, HEAP, lsl #32
    // 0xaf4188: stur            x1, [fp, #-0x18]
    // 0xaf418c: r0 = Text()
    //     0xaf418c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf4190: mov             x1, x0
    // 0xaf4194: ldur            x0, [fp, #-0x18]
    // 0xaf4198: stur            x1, [fp, #-0x20]
    // 0xaf419c: StoreField: r1->field_b = r0
    //     0xaf419c: stur            w0, [x1, #0xb]
    // 0xaf41a0: r0 = AppBar()
    //     0xaf41a0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf41a4: stur            x0, [fp, #-0x18]
    // 0xaf41a8: ldur            x16, [fp, #-0x20]
    // 0xaf41ac: str             x16, [SP]
    // 0xaf41b0: mov             x1, x0
    // 0xaf41b4: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaf41b4: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaf41b8: ldr             x4, [x4, #0x6e8]
    // 0xaf41bc: r0 = AppBar()
    //     0xaf41bc: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf41c0: r0 = Obx()
    //     0xaf41c0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf41c4: ldur            x2, [fp, #-0x10]
    // 0xaf41c8: r1 = Function '<anonymous closure>':.
    //     0xaf41c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b8] AnonymousClosure: (0xaf4cd4), in [package:nuonline/app/modules/faq/views/faq_list_view.dart] FaqListView::build (0xaf4144)
    //     0xaf41cc: ldr             x1, [x1, #0xb8]
    // 0xaf41d0: stur            x0, [fp, #-0x20]
    // 0xaf41d4: r0 = AllocateClosure()
    //     0xaf41d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf41d8: mov             x1, x0
    // 0xaf41dc: ldur            x0, [fp, #-0x20]
    // 0xaf41e0: StoreField: r0->field_b = r1
    //     0xaf41e0: stur            w1, [x0, #0xb]
    // 0xaf41e4: ldur            x1, [fp, #-8]
    // 0xaf41e8: r0 = controller()
    //     0xaf41e8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf41ec: ldur            x2, [fp, #-0x10]
    // 0xaf41f0: r1 = Function '<anonymous closure>':.
    //     0xaf41f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0c0] AnonymousClosure: (0xaf4358), in [package:nuonline/app/modules/faq/views/faq_list_view.dart] FaqListView::build (0xaf4144)
    //     0xaf41f4: ldr             x1, [x1, #0xc0]
    // 0xaf41f8: stur            x0, [fp, #-8]
    // 0xaf41fc: r0 = AllocateClosure()
    //     0xaf41fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4200: ldur            x1, [fp, #-8]
    // 0xaf4204: mov             x2, x0
    // 0xaf4208: r3 = Instance_Divider
    //     0xaf4208: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xaf420c: ldr             x3, [x3, #0xc28]
    // 0xaf4210: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xaf4210: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xaf4214: r0 = paginate()
    //     0xaf4214: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xaf4218: r1 = <FlexParentData>
    //     0xaf4218: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf421c: ldr             x1, [x1, #0x720]
    // 0xaf4220: stur            x0, [fp, #-8]
    // 0xaf4224: r0 = Expanded()
    //     0xaf4224: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf4228: mov             x3, x0
    // 0xaf422c: r0 = 1
    //     0xaf422c: movz            x0, #0x1
    // 0xaf4230: stur            x3, [fp, #-0x10]
    // 0xaf4234: StoreField: r3->field_13 = r0
    //     0xaf4234: stur            x0, [x3, #0x13]
    // 0xaf4238: r0 = Instance_FlexFit
    //     0xaf4238: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf423c: ldr             x0, [x0, #0x728]
    // 0xaf4240: StoreField: r3->field_1b = r0
    //     0xaf4240: stur            w0, [x3, #0x1b]
    // 0xaf4244: ldur            x0, [fp, #-8]
    // 0xaf4248: StoreField: r3->field_b = r0
    //     0xaf4248: stur            w0, [x3, #0xb]
    // 0xaf424c: r1 = Null
    //     0xaf424c: mov             x1, NULL
    // 0xaf4250: r2 = 4
    //     0xaf4250: movz            x2, #0x4
    // 0xaf4254: r0 = AllocateArray()
    //     0xaf4254: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf4258: mov             x2, x0
    // 0xaf425c: ldur            x0, [fp, #-0x20]
    // 0xaf4260: stur            x2, [fp, #-8]
    // 0xaf4264: StoreField: r2->field_f = r0
    //     0xaf4264: stur            w0, [x2, #0xf]
    // 0xaf4268: ldur            x0, [fp, #-0x10]
    // 0xaf426c: StoreField: r2->field_13 = r0
    //     0xaf426c: stur            w0, [x2, #0x13]
    // 0xaf4270: r1 = <Widget>
    //     0xaf4270: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf4274: r0 = AllocateGrowableArray()
    //     0xaf4274: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf4278: mov             x1, x0
    // 0xaf427c: ldur            x0, [fp, #-8]
    // 0xaf4280: stur            x1, [fp, #-0x10]
    // 0xaf4284: StoreField: r1->field_f = r0
    //     0xaf4284: stur            w0, [x1, #0xf]
    // 0xaf4288: r0 = 4
    //     0xaf4288: movz            x0, #0x4
    // 0xaf428c: StoreField: r1->field_b = r0
    //     0xaf428c: stur            w0, [x1, #0xb]
    // 0xaf4290: r0 = Column()
    //     0xaf4290: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf4294: mov             x1, x0
    // 0xaf4298: r0 = Instance_Axis
    //     0xaf4298: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf429c: stur            x1, [fp, #-8]
    // 0xaf42a0: StoreField: r1->field_f = r0
    //     0xaf42a0: stur            w0, [x1, #0xf]
    // 0xaf42a4: r0 = Instance_MainAxisAlignment
    //     0xaf42a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf42a8: ldr             x0, [x0, #0x730]
    // 0xaf42ac: StoreField: r1->field_13 = r0
    //     0xaf42ac: stur            w0, [x1, #0x13]
    // 0xaf42b0: r0 = Instance_MainAxisSize
    //     0xaf42b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf42b4: ldr             x0, [x0, #0x738]
    // 0xaf42b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf42b8: stur            w0, [x1, #0x17]
    // 0xaf42bc: r0 = Instance_CrossAxisAlignment
    //     0xaf42bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf42c0: ldr             x0, [x0, #0x740]
    // 0xaf42c4: StoreField: r1->field_1b = r0
    //     0xaf42c4: stur            w0, [x1, #0x1b]
    // 0xaf42c8: r0 = Instance_VerticalDirection
    //     0xaf42c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf42cc: ldr             x0, [x0, #0x748]
    // 0xaf42d0: StoreField: r1->field_23 = r0
    //     0xaf42d0: stur            w0, [x1, #0x23]
    // 0xaf42d4: r0 = Instance_Clip
    //     0xaf42d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf42d8: ldr             x0, [x0, #0x750]
    // 0xaf42dc: StoreField: r1->field_2b = r0
    //     0xaf42dc: stur            w0, [x1, #0x2b]
    // 0xaf42e0: StoreField: r1->field_2f = rZR
    //     0xaf42e0: stur            xzr, [x1, #0x2f]
    // 0xaf42e4: ldur            x0, [fp, #-0x10]
    // 0xaf42e8: StoreField: r1->field_b = r0
    //     0xaf42e8: stur            w0, [x1, #0xb]
    // 0xaf42ec: r0 = Scaffold()
    //     0xaf42ec: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf42f0: mov             x1, x0
    // 0xaf42f4: ldur            x0, [fp, #-0x18]
    // 0xaf42f8: stur            x1, [fp, #-0x10]
    // 0xaf42fc: StoreField: r1->field_13 = r0
    //     0xaf42fc: stur            w0, [x1, #0x13]
    // 0xaf4300: ldur            x0, [fp, #-8]
    // 0xaf4304: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf4304: stur            w0, [x1, #0x17]
    // 0xaf4308: r0 = Instance_AlignmentDirectional
    //     0xaf4308: add             x0, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf430c: ldr             x0, [x0, #0x758]
    // 0xaf4310: StoreField: r1->field_2b = r0
    //     0xaf4310: stur            w0, [x1, #0x2b]
    // 0xaf4314: r0 = true
    //     0xaf4314: add             x0, NULL, #0x20  ; true
    // 0xaf4318: StoreField: r1->field_53 = r0
    //     0xaf4318: stur            w0, [x1, #0x53]
    // 0xaf431c: r2 = Instance_DragStartBehavior
    //     0xaf431c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf4320: StoreField: r1->field_57 = r2
    //     0xaf4320: stur            w2, [x1, #0x57]
    // 0xaf4324: r2 = false
    //     0xaf4324: add             x2, NULL, #0x30  ; false
    // 0xaf4328: StoreField: r1->field_b = r2
    //     0xaf4328: stur            w2, [x1, #0xb]
    // 0xaf432c: StoreField: r1->field_f = r2
    //     0xaf432c: stur            w2, [x1, #0xf]
    // 0xaf4330: StoreField: r1->field_5f = r0
    //     0xaf4330: stur            w0, [x1, #0x5f]
    // 0xaf4334: StoreField: r1->field_63 = r0
    //     0xaf4334: stur            w0, [x1, #0x63]
    // 0xaf4338: r0 = NDismissableKeyboard()
    //     0xaf4338: bl              #0xae4b60  ; AllocateNDismissableKeyboardStub -> NDismissableKeyboard (size=0x10)
    // 0xaf433c: ldur            x1, [fp, #-0x10]
    // 0xaf4340: StoreField: r0->field_b = r1
    //     0xaf4340: stur            w1, [x0, #0xb]
    // 0xaf4344: LeaveFrame
    //     0xaf4344: mov             SP, fp
    //     0xaf4348: ldp             fp, lr, [SP], #0x10
    // 0xaf434c: ret
    //     0xaf434c: ret             
    // 0xaf4350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4354: b               #0xaf4160
  }
  [closure] NNumberListTile <anonymous closure>(dynamic, BuildContext, Faq, int) {
    // ** addr: 0xaf4358, size: 0xcc
    // 0xaf4358: EnterFrame
    //     0xaf4358: stp             fp, lr, [SP, #-0x10]!
    //     0xaf435c: mov             fp, SP
    // 0xaf4360: AllocStack(0x20)
    //     0xaf4360: sub             SP, SP, #0x20
    // 0xaf4364: SetupParameters()
    //     0xaf4364: ldr             x0, [fp, #0x28]
    //     0xaf4368: ldur            w1, [x0, #0x17]
    //     0xaf436c: add             x1, x1, HEAP, lsl #32
    //     0xaf4370: stur            x1, [fp, #-8]
    // 0xaf4374: r1 = 1
    //     0xaf4374: movz            x1, #0x1
    // 0xaf4378: r0 = AllocateContext()
    //     0xaf4378: bl              #0xec126c  ; AllocateContextStub
    // 0xaf437c: mov             x1, x0
    // 0xaf4380: ldur            x0, [fp, #-8]
    // 0xaf4384: stur            x1, [fp, #-0x18]
    // 0xaf4388: StoreField: r1->field_b = r0
    //     0xaf4388: stur            w0, [x1, #0xb]
    // 0xaf438c: ldr             x0, [fp, #0x18]
    // 0xaf4390: StoreField: r1->field_f = r0
    //     0xaf4390: stur            w0, [x1, #0xf]
    // 0xaf4394: ldr             x2, [fp, #0x10]
    // 0xaf4398: r3 = LoadInt32Instr(r2)
    //     0xaf4398: sbfx            x3, x2, #1, #0x1f
    //     0xaf439c: tbz             w2, #0, #0xaf43a4
    //     0xaf43a0: ldur            x3, [x2, #7]
    // 0xaf43a4: add             x2, x3, #1
    // 0xaf43a8: stur            x2, [fp, #-0x10]
    // 0xaf43ac: LoadField: r3 = r0->field_f
    //     0xaf43ac: ldur            w3, [x0, #0xf]
    // 0xaf43b0: DecompressPointer r3
    //     0xaf43b0: add             x3, x3, HEAP, lsl #32
    // 0xaf43b4: stur            x3, [fp, #-8]
    // 0xaf43b8: r0 = NNumberListTile()
    //     0xaf43b8: bl              #0xada214  ; AllocateNNumberListTileStub -> NNumberListTile (size=0x2c)
    // 0xaf43bc: mov             x3, x0
    // 0xaf43c0: ldur            x0, [fp, #-0x10]
    // 0xaf43c4: stur            x3, [fp, #-0x20]
    // 0xaf43c8: StoreField: r3->field_b = r0
    //     0xaf43c8: stur            x0, [x3, #0xb]
    // 0xaf43cc: ldur            x0, [fp, #-8]
    // 0xaf43d0: StoreField: r3->field_13 = r0
    //     0xaf43d0: stur            w0, [x3, #0x13]
    // 0xaf43d4: ldur            x2, [fp, #-0x18]
    // 0xaf43d8: r1 = Function '<anonymous closure>':.
    //     0xaf43d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0c8] AnonymousClosure: (0xaf4574), in [package:nuonline/app/modules/faq/views/faq_list_view.dart] FaqListView::build (0xaf4144)
    //     0xaf43dc: ldr             x1, [x1, #0xc8]
    // 0xaf43e0: r0 = AllocateClosure()
    //     0xaf43e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf43e4: mov             x1, x0
    // 0xaf43e8: ldur            x0, [fp, #-0x20]
    // 0xaf43ec: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf43ec: stur            w1, [x0, #0x17]
    // 0xaf43f0: r1 = false
    //     0xaf43f0: add             x1, NULL, #0x30  ; false
    // 0xaf43f4: StoreField: r0->field_1b = r1
    //     0xaf43f4: stur            w1, [x0, #0x1b]
    // 0xaf43f8: StoreField: r0->field_1f = r1
    //     0xaf43f8: stur            w1, [x0, #0x1f]
    // 0xaf43fc: ldur            x2, [fp, #-0x18]
    // 0xaf4400: r1 = Function '<anonymous closure>':.
    //     0xaf4400: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] AnonymousClosure: (0xaf4424), in [package:nuonline/app/modules/faq/views/faq_list_view.dart] FaqListView::build (0xaf4144)
    //     0xaf4404: ldr             x1, [x1, #0xd0]
    // 0xaf4408: r0 = AllocateClosure()
    //     0xaf4408: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf440c: mov             x1, x0
    // 0xaf4410: ldur            x0, [fp, #-0x20]
    // 0xaf4414: StoreField: r0->field_27 = r1
    //     0xaf4414: stur            w1, [x0, #0x27]
    // 0xaf4418: LeaveFrame
    //     0xaf4418: mov             SP, fp
    //     0xaf441c: ldp             fp, lr, [SP], #0x10
    // 0xaf4420: ret
    //     0xaf4420: ret             
  }
  [closure] SubstringHighlight <anonymous closure>(dynamic, String) {
    // ** addr: 0xaf4424, size: 0x150
    // 0xaf4424: EnterFrame
    //     0xaf4424: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4428: mov             fp, SP
    // 0xaf442c: AllocStack(0x38)
    //     0xaf442c: sub             SP, SP, #0x38
    // 0xaf4430: SetupParameters()
    //     0xaf4430: ldr             x0, [fp, #0x18]
    //     0xaf4434: ldur            w1, [x0, #0x17]
    //     0xaf4438: add             x1, x1, HEAP, lsl #32
    //     0xaf443c: stur            x1, [fp, #-8]
    // 0xaf4440: CheckStackOverflow
    //     0xaf4440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4444: cmp             SP, x16
    //     0xaf4448: b.ls            #0xaf4568
    // 0xaf444c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf444c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf4450: ldr             x0, [x0, #0x2670]
    //     0xaf4454: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf4458: cmp             w0, w16
    //     0xaf445c: b.ne            #0xaf4468
    //     0xaf4460: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf4464: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf4468: r0 = GetNavigation.textTheme()
    //     0xaf4468: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf446c: LoadField: r1 = r0->field_23
    //     0xaf446c: ldur            w1, [x0, #0x23]
    // 0xaf4470: DecompressPointer r1
    //     0xaf4470: add             x1, x1, HEAP, lsl #32
    // 0xaf4474: cmp             w1, NULL
    // 0xaf4478: b.eq            #0xaf4570
    // 0xaf447c: r16 = Instance_FontWeight
    //     0xaf447c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xaf4480: ldr             x16, [x16, #0xcc0]
    // 0xaf4484: str             x16, [SP]
    // 0xaf4488: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xaf4488: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xaf448c: ldr             x4, [x4, #0xfe0]
    // 0xaf4490: r0 = copyWith()
    //     0xaf4490: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf4494: mov             x2, x0
    // 0xaf4498: ldur            x0, [fp, #-8]
    // 0xaf449c: stur            x2, [fp, #-0x10]
    // 0xaf44a0: LoadField: r1 = r0->field_b
    //     0xaf44a0: ldur            w1, [x0, #0xb]
    // 0xaf44a4: DecompressPointer r1
    //     0xaf44a4: add             x1, x1, HEAP, lsl #32
    // 0xaf44a8: LoadField: r0 = r1->field_f
    //     0xaf44a8: ldur            w0, [x1, #0xf]
    // 0xaf44ac: DecompressPointer r0
    //     0xaf44ac: add             x0, x0, HEAP, lsl #32
    // 0xaf44b0: mov             x1, x0
    // 0xaf44b4: r0 = controller()
    //     0xaf44b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf44b8: LoadField: r1 = r0->field_2f
    //     0xaf44b8: ldur            w1, [x0, #0x2f]
    // 0xaf44bc: DecompressPointer r1
    //     0xaf44bc: add             x1, x1, HEAP, lsl #32
    // 0xaf44c0: r0 = RxStringExt.split()
    //     0xaf44c0: bl              #0x624ca4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.split
    // 0xaf44c4: r1 = _ConstMap len:10
    //     0xaf44c4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaf44c8: ldr             x1, [x1, #0xc08]
    // 0xaf44cc: r2 = 600
    //     0xaf44cc: movz            x2, #0x258
    // 0xaf44d0: stur            x0, [fp, #-8]
    // 0xaf44d4: r0 = []()
    //     0xaf44d4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf44d8: r16 = <Color?>
    //     0xaf44d8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf44dc: ldr             x16, [x16, #0x98]
    // 0xaf44e0: stp             x0, x16, [SP, #8]
    // 0xaf44e4: r16 = Instance_MaterialColor
    //     0xaf44e4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaf44e8: ldr             x16, [x16, #0xcf0]
    // 0xaf44ec: str             x16, [SP]
    // 0xaf44f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf44f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf44f4: r0 = mode()
    //     0xaf44f4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf44f8: stur            x0, [fp, #-0x18]
    // 0xaf44fc: r0 = TextStyle()
    //     0xaf44fc: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf4500: mov             x1, x0
    // 0xaf4504: r0 = true
    //     0xaf4504: add             x0, NULL, #0x20  ; true
    // 0xaf4508: stur            x1, [fp, #-0x20]
    // 0xaf450c: StoreField: r1->field_7 = r0
    //     0xaf450c: stur            w0, [x1, #7]
    // 0xaf4510: ldur            x0, [fp, #-0x18]
    // 0xaf4514: StoreField: r1->field_b = r0
    //     0xaf4514: stur            w0, [x1, #0xb]
    // 0xaf4518: r0 = SubstringHighlight()
    //     0xaf4518: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xaf451c: r1 = false
    //     0xaf451c: add             x1, NULL, #0x30  ; false
    // 0xaf4520: StoreField: r0->field_b = r1
    //     0xaf4520: stur            w1, [x0, #0xb]
    // 0xaf4524: r2 = Instance_TextOverflow
    //     0xaf4524: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xaf4528: ldr             x2, [x2, #0xc60]
    // 0xaf452c: StoreField: r0->field_f = r2
    //     0xaf452c: stur            w2, [x0, #0xf]
    // 0xaf4530: ldur            x2, [fp, #-8]
    // 0xaf4534: StoreField: r0->field_1b = r2
    //     0xaf4534: stur            w2, [x0, #0x1b]
    // 0xaf4538: ldr             x2, [fp, #0x10]
    // 0xaf453c: StoreField: r0->field_1f = r2
    //     0xaf453c: stur            w2, [x0, #0x1f]
    // 0xaf4540: r2 = Instance_TextAlign
    //     0xaf4540: ldr             x2, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xaf4544: StoreField: r0->field_23 = r2
    //     0xaf4544: stur            w2, [x0, #0x23]
    // 0xaf4548: ldur            x2, [fp, #-0x10]
    // 0xaf454c: StoreField: r0->field_27 = r2
    //     0xaf454c: stur            w2, [x0, #0x27]
    // 0xaf4550: ldur            x2, [fp, #-0x20]
    // 0xaf4554: StoreField: r0->field_2b = r2
    //     0xaf4554: stur            w2, [x0, #0x2b]
    // 0xaf4558: StoreField: r0->field_2f = r1
    //     0xaf4558: stur            w1, [x0, #0x2f]
    // 0xaf455c: LeaveFrame
    //     0xaf455c: mov             SP, fp
    //     0xaf4560: ldp             fp, lr, [SP], #0x10
    // 0xaf4564: ret
    //     0xaf4564: ret             
    // 0xaf4568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4568: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf456c: b               #0xaf444c
    // 0xaf4570: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf4570: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf4574, size: 0x74
    // 0xaf4574: EnterFrame
    //     0xaf4574: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4578: mov             fp, SP
    // 0xaf457c: AllocStack(0x18)
    //     0xaf457c: sub             SP, SP, #0x18
    // 0xaf4580: SetupParameters()
    //     0xaf4580: ldr             x0, [fp, #0x10]
    //     0xaf4584: ldur            w2, [x0, #0x17]
    //     0xaf4588: add             x2, x2, HEAP, lsl #32
    //     0xaf458c: stur            x2, [fp, #-8]
    // 0xaf4590: CheckStackOverflow
    //     0xaf4590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4594: cmp             SP, x16
    //     0xaf4598: b.ls            #0xaf45e0
    // 0xaf459c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf459c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf45a0: ldr             x0, [x0, #0x2670]
    //     0xaf45a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf45a8: cmp             w0, w16
    //     0xaf45ac: b.ne            #0xaf45b8
    //     0xaf45b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf45b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf45b8: ldur            x2, [fp, #-8]
    // 0xaf45bc: r1 = Function '<anonymous closure>':.
    //     0xaf45bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d8] AnonymousClosure: (0xaf45e8), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf45c0: ldr             x1, [x1, #0xd8]
    // 0xaf45c4: r0 = AllocateClosure()
    //     0xaf45c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf45c8: stp             x0, NULL, [SP]
    // 0xaf45cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf45cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf45d0: r0 = GetNavigation.to()
    //     0xaf45d0: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xaf45d4: LeaveFrame
    //     0xaf45d4: mov             SP, fp
    //     0xaf45d8: ldp             fp, lr, [SP], #0x10
    // 0xaf45dc: ret
    //     0xaf45dc: ret             
    // 0xaf45e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf45e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf45e4: b               #0xaf459c
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xaf4cd4, size: 0x130
    // 0xaf4cd4: EnterFrame
    //     0xaf4cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4cd8: mov             fp, SP
    // 0xaf4cdc: AllocStack(0x28)
    //     0xaf4cdc: sub             SP, SP, #0x28
    // 0xaf4ce0: SetupParameters()
    //     0xaf4ce0: ldr             x0, [fp, #0x10]
    //     0xaf4ce4: ldur            w2, [x0, #0x17]
    //     0xaf4ce8: add             x2, x2, HEAP, lsl #32
    //     0xaf4cec: stur            x2, [fp, #-8]
    // 0xaf4cf0: CheckStackOverflow
    //     0xaf4cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4cf4: cmp             SP, x16
    //     0xaf4cf8: b.ls            #0xaf4dfc
    // 0xaf4cfc: LoadField: r1 = r2->field_f
    //     0xaf4cfc: ldur            w1, [x2, #0xf]
    // 0xaf4d00: DecompressPointer r1
    //     0xaf4d00: add             x1, x1, HEAP, lsl #32
    // 0xaf4d04: r0 = controller()
    //     0xaf4d04: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4d08: LoadField: r2 = r0->field_3f
    //     0xaf4d08: ldur            w2, [x0, #0x3f]
    // 0xaf4d0c: DecompressPointer r2
    //     0xaf4d0c: add             x2, x2, HEAP, lsl #32
    // 0xaf4d10: ldur            x0, [fp, #-8]
    // 0xaf4d14: stur            x2, [fp, #-0x10]
    // 0xaf4d18: LoadField: r1 = r0->field_f
    //     0xaf4d18: ldur            w1, [x0, #0xf]
    // 0xaf4d1c: DecompressPointer r1
    //     0xaf4d1c: add             x1, x1, HEAP, lsl #32
    // 0xaf4d20: r0 = controller()
    //     0xaf4d20: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4d24: LoadField: r2 = r0->field_2f
    //     0xaf4d24: ldur            w2, [x0, #0x2f]
    // 0xaf4d28: DecompressPointer r2
    //     0xaf4d28: add             x2, x2, HEAP, lsl #32
    // 0xaf4d2c: LoadField: r3 = r2->field_7
    //     0xaf4d2c: ldur            w3, [x2, #7]
    // 0xaf4d30: DecompressPointer r3
    //     0xaf4d30: add             x3, x3, HEAP, lsl #32
    // 0xaf4d34: r1 = Function 'call':.
    //     0xaf4d34: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaf4d38: ldr             x1, [x1, #0x310]
    // 0xaf4d3c: r0 = AllocateClosureTA()
    //     0xaf4d3c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaf4d40: mov             x3, x0
    // 0xaf4d44: r2 = Null
    //     0xaf4d44: mov             x2, NULL
    // 0xaf4d48: r1 = Null
    //     0xaf4d48: mov             x1, NULL
    // 0xaf4d4c: stur            x3, [fp, #-0x18]
    // 0xaf4d50: r8 = (dynamic this, String?) => String
    //     0xaf4d50: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xaf4d54: ldr             x8, [x8, #0x40]
    // 0xaf4d58: r3 = Null
    //     0xaf4d58: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0e0] Null
    //     0xaf4d5c: ldr             x3, [x3, #0xe0]
    // 0xaf4d60: r0 = DefaultTypeTest()
    //     0xaf4d60: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaf4d64: ldur            x0, [fp, #-8]
    // 0xaf4d68: LoadField: r1 = r0->field_f
    //     0xaf4d68: ldur            w1, [x0, #0xf]
    // 0xaf4d6c: DecompressPointer r1
    //     0xaf4d6c: add             x1, x1, HEAP, lsl #32
    // 0xaf4d70: r0 = controller()
    //     0xaf4d70: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4d74: mov             x2, x0
    // 0xaf4d78: ldur            x0, [fp, #-8]
    // 0xaf4d7c: stur            x2, [fp, #-0x20]
    // 0xaf4d80: LoadField: r1 = r0->field_f
    //     0xaf4d80: ldur            w1, [x0, #0xf]
    // 0xaf4d84: DecompressPointer r1
    //     0xaf4d84: add             x1, x1, HEAP, lsl #32
    // 0xaf4d88: r0 = controller()
    //     0xaf4d88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4d8c: LoadField: r1 = r0->field_2f
    //     0xaf4d8c: ldur            w1, [x0, #0x2f]
    // 0xaf4d90: DecompressPointer r1
    //     0xaf4d90: add             x1, x1, HEAP, lsl #32
    // 0xaf4d94: r0 = value()
    //     0xaf4d94: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf4d98: stur            x0, [fp, #-8]
    // 0xaf4d9c: r0 = NSearchTextField()
    //     0xaf4d9c: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xaf4da0: mov             x3, x0
    // 0xaf4da4: ldur            x0, [fp, #-0x10]
    // 0xaf4da8: stur            x3, [fp, #-0x28]
    // 0xaf4dac: StoreField: r3->field_f = r0
    //     0xaf4dac: stur            w0, [x3, #0xf]
    // 0xaf4db0: ldur            x0, [fp, #-8]
    // 0xaf4db4: StoreField: r3->field_b = r0
    //     0xaf4db4: stur            w0, [x3, #0xb]
    // 0xaf4db8: ldur            x0, [fp, #-0x18]
    // 0xaf4dbc: StoreField: r3->field_13 = r0
    //     0xaf4dbc: stur            w0, [x3, #0x13]
    // 0xaf4dc0: ldur            x2, [fp, #-0x20]
    // 0xaf4dc4: r1 = Function 'clear':.
    //     0xaf4dc4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0f0] AnonymousClosure: (0xaf4e04), in [package:nuonline/app/modules/faq/controllers/faq_list_controller.dart] FaqListController::clear (0xaf4e3c)
    //     0xaf4dc8: ldr             x1, [x1, #0xf0]
    // 0xaf4dcc: r0 = AllocateClosure()
    //     0xaf4dcc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4dd0: mov             x1, x0
    // 0xaf4dd4: ldur            x0, [fp, #-0x28]
    // 0xaf4dd8: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf4dd8: stur            w1, [x0, #0x17]
    // 0xaf4ddc: r1 = "Cari"
    //     0xaf4ddc: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac20] "Cari"
    //     0xaf4de0: ldr             x1, [x1, #0xc20]
    // 0xaf4de4: StoreField: r0->field_23 = r1
    //     0xaf4de4: stur            w1, [x0, #0x23]
    // 0xaf4de8: r1 = false
    //     0xaf4de8: add             x1, NULL, #0x30  ; false
    // 0xaf4dec: StoreField: r0->field_27 = r1
    //     0xaf4dec: stur            w1, [x0, #0x27]
    // 0xaf4df0: LeaveFrame
    //     0xaf4df0: mov             SP, fp
    //     0xaf4df4: ldp             fp, lr, [SP], #0x10
    // 0xaf4df8: ret
    //     0xaf4df8: ret             
    // 0xaf4dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4e00: b               #0xaf4cfc
  }
}
