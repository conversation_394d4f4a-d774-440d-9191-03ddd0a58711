// lib: , url: package:nuonline/app/modules/faq/views/faq_search_view.dart

// class id: 1050276, size: 0x8
class :: {
}

// class id: 5276, size: 0x14, field offset: 0x14
//   const constructor, 
class FaqSearchView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf4e98, size: 0x114
    // 0xaf4e98: EnterFrame
    //     0xaf4e98: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4e9c: mov             fp, SP
    // 0xaf4ea0: AllocStack(0x28)
    //     0xaf4ea0: sub             SP, SP, #0x28
    // 0xaf4ea4: SetupParameters(FaqSearchView this /* r1 => r1, fp-0x8 */)
    //     0xaf4ea4: stur            x1, [fp, #-8]
    // 0xaf4ea8: CheckStackOverflow
    //     0xaf4ea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4eac: cmp             SP, x16
    //     0xaf4eb0: b.ls            #0xaf4fa4
    // 0xaf4eb4: r1 = 1
    //     0xaf4eb4: movz            x1, #0x1
    // 0xaf4eb8: r0 = AllocateContext()
    //     0xaf4eb8: bl              #0xec126c  ; AllocateContextStub
    // 0xaf4ebc: ldur            x1, [fp, #-8]
    // 0xaf4ec0: stur            x0, [fp, #-0x10]
    // 0xaf4ec4: StoreField: r0->field_f = r1
    //     0xaf4ec4: stur            w1, [x0, #0xf]
    // 0xaf4ec8: r0 = Obx()
    //     0xaf4ec8: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf4ecc: ldur            x2, [fp, #-0x10]
    // 0xaf4ed0: r1 = Function '<anonymous closure>':.
    //     0xaf4ed0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f080] AnonymousClosure: (0xaf5284), in [package:nuonline/app/modules/faq/views/faq_search_view.dart] FaqSearchView::build (0xaf4e98)
    //     0xaf4ed4: ldr             x1, [x1, #0x80]
    // 0xaf4ed8: stur            x0, [fp, #-0x18]
    // 0xaf4edc: r0 = AllocateClosure()
    //     0xaf4edc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4ee0: mov             x1, x0
    // 0xaf4ee4: ldur            x0, [fp, #-0x18]
    // 0xaf4ee8: StoreField: r0->field_b = r1
    //     0xaf4ee8: stur            w1, [x0, #0xb]
    // 0xaf4eec: r0 = AppBar()
    //     0xaf4eec: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf4ef0: stur            x0, [fp, #-0x20]
    // 0xaf4ef4: ldur            x16, [fp, #-0x18]
    // 0xaf4ef8: str             x16, [SP]
    // 0xaf4efc: mov             x1, x0
    // 0xaf4f00: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaf4f00: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaf4f04: ldr             x4, [x4, #0x6e8]
    // 0xaf4f08: r0 = AppBar()
    //     0xaf4f08: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf4f0c: ldur            x1, [fp, #-8]
    // 0xaf4f10: r0 = controller()
    //     0xaf4f10: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf4f14: ldur            x2, [fp, #-0x10]
    // 0xaf4f18: r1 = Function '<anonymous closure>':.
    //     0xaf4f18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f088] AnonymousClosure: (0xaf4fac), in [package:nuonline/app/modules/faq/views/faq_search_view.dart] FaqSearchView::build (0xaf4e98)
    //     0xaf4f1c: ldr             x1, [x1, #0x88]
    // 0xaf4f20: stur            x0, [fp, #-8]
    // 0xaf4f24: r0 = AllocateClosure()
    //     0xaf4f24: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4f28: r16 = Instance_EdgeInsets
    //     0xaf4f28: add             x16, PP, #0x28, lsl #12  ; [pp+0x28560] Obj!EdgeInsets@e128e1
    //     0xaf4f2c: ldr             x16, [x16, #0x560]
    // 0xaf4f30: str             x16, [SP]
    // 0xaf4f34: ldur            x1, [fp, #-8]
    // 0xaf4f38: mov             x2, x0
    // 0xaf4f3c: r3 = Instance_Divider
    //     0xaf4f3c: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xaf4f40: ldr             x3, [x3, #0xc28]
    // 0xaf4f44: r4 = const [0, 0x4, 0x1, 0x3, padding, 0x3, null]
    //     0xaf4f44: add             x4, PP, #0x29, lsl #12  ; [pp+0x29660] List(7) [0, 0x4, 0x1, 0x3, "padding", 0x3, Null]
    //     0xaf4f48: ldr             x4, [x4, #0x660]
    // 0xaf4f4c: r0 = paginate()
    //     0xaf4f4c: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xaf4f50: stur            x0, [fp, #-8]
    // 0xaf4f54: r0 = Scaffold()
    //     0xaf4f54: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf4f58: ldur            x1, [fp, #-0x20]
    // 0xaf4f5c: StoreField: r0->field_13 = r1
    //     0xaf4f5c: stur            w1, [x0, #0x13]
    // 0xaf4f60: ldur            x1, [fp, #-8]
    // 0xaf4f64: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf4f64: stur            w1, [x0, #0x17]
    // 0xaf4f68: r1 = Instance_AlignmentDirectional
    //     0xaf4f68: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf4f6c: ldr             x1, [x1, #0x758]
    // 0xaf4f70: StoreField: r0->field_2b = r1
    //     0xaf4f70: stur            w1, [x0, #0x2b]
    // 0xaf4f74: r1 = true
    //     0xaf4f74: add             x1, NULL, #0x20  ; true
    // 0xaf4f78: StoreField: r0->field_53 = r1
    //     0xaf4f78: stur            w1, [x0, #0x53]
    // 0xaf4f7c: r2 = Instance_DragStartBehavior
    //     0xaf4f7c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf4f80: StoreField: r0->field_57 = r2
    //     0xaf4f80: stur            w2, [x0, #0x57]
    // 0xaf4f84: r2 = false
    //     0xaf4f84: add             x2, NULL, #0x30  ; false
    // 0xaf4f88: StoreField: r0->field_b = r2
    //     0xaf4f88: stur            w2, [x0, #0xb]
    // 0xaf4f8c: StoreField: r0->field_f = r2
    //     0xaf4f8c: stur            w2, [x0, #0xf]
    // 0xaf4f90: StoreField: r0->field_5f = r1
    //     0xaf4f90: stur            w1, [x0, #0x5f]
    // 0xaf4f94: StoreField: r0->field_63 = r1
    //     0xaf4f94: stur            w1, [x0, #0x63]
    // 0xaf4f98: LeaveFrame
    //     0xaf4f98: mov             SP, fp
    //     0xaf4f9c: ldp             fp, lr, [SP], #0x10
    // 0xaf4fa0: ret
    //     0xaf4fa0: ret             
    // 0xaf4fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4fa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4fa8: b               #0xaf4eb4
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, Faq, int) {
    // ** addr: 0xaf4fac, size: 0x264
    // 0xaf4fac: EnterFrame
    //     0xaf4fac: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4fb0: mov             fp, SP
    // 0xaf4fb4: AllocStack(0x48)
    //     0xaf4fb4: sub             SP, SP, #0x48
    // 0xaf4fb8: SetupParameters()
    //     0xaf4fb8: ldr             x0, [fp, #0x28]
    //     0xaf4fbc: ldur            w1, [x0, #0x17]
    //     0xaf4fc0: add             x1, x1, HEAP, lsl #32
    //     0xaf4fc4: stur            x1, [fp, #-8]
    // 0xaf4fc8: CheckStackOverflow
    //     0xaf4fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4fcc: cmp             SP, x16
    //     0xaf4fd0: b.ls            #0xaf5204
    // 0xaf4fd4: r1 = 1
    //     0xaf4fd4: movz            x1, #0x1
    // 0xaf4fd8: r0 = AllocateContext()
    //     0xaf4fd8: bl              #0xec126c  ; AllocateContextStub
    // 0xaf4fdc: mov             x1, x0
    // 0xaf4fe0: ldur            x0, [fp, #-8]
    // 0xaf4fe4: stur            x1, [fp, #-0x18]
    // 0xaf4fe8: StoreField: r1->field_b = r0
    //     0xaf4fe8: stur            w0, [x1, #0xb]
    // 0xaf4fec: ldr             x2, [fp, #0x18]
    // 0xaf4ff0: StoreField: r1->field_f = r2
    //     0xaf4ff0: stur            w2, [x1, #0xf]
    // 0xaf4ff4: LoadField: r3 = r2->field_f
    //     0xaf4ff4: ldur            w3, [x2, #0xf]
    // 0xaf4ff8: DecompressPointer r3
    //     0xaf4ff8: add             x3, x3, HEAP, lsl #32
    // 0xaf4ffc: stur            x3, [fp, #-0x10]
    // 0xaf5000: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf5000: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf5004: ldr             x0, [x0, #0x2670]
    //     0xaf5008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf500c: cmp             w0, w16
    //     0xaf5010: b.ne            #0xaf501c
    //     0xaf5014: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf5018: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf501c: r0 = GetNavigation.textTheme()
    //     0xaf501c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf5020: LoadField: r2 = r0->field_23
    //     0xaf5020: ldur            w2, [x0, #0x23]
    // 0xaf5024: DecompressPointer r2
    //     0xaf5024: add             x2, x2, HEAP, lsl #32
    // 0xaf5028: stur            x2, [fp, #-0x20]
    // 0xaf502c: cmp             w2, NULL
    // 0xaf5030: b.eq            #0xaf520c
    // 0xaf5034: ldur            x0, [fp, #-8]
    // 0xaf5038: LoadField: r1 = r0->field_f
    //     0xaf5038: ldur            w1, [x0, #0xf]
    // 0xaf503c: DecompressPointer r1
    //     0xaf503c: add             x1, x1, HEAP, lsl #32
    // 0xaf5040: r0 = controller()
    //     0xaf5040: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf5044: LoadField: r1 = r0->field_2f
    //     0xaf5044: ldur            w1, [x0, #0x2f]
    // 0xaf5048: DecompressPointer r1
    //     0xaf5048: add             x1, x1, HEAP, lsl #32
    // 0xaf504c: r0 = RxStringExt.split()
    //     0xaf504c: bl              #0x624ca4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.split
    // 0xaf5050: r1 = _ConstMap len:10
    //     0xaf5050: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaf5054: ldr             x1, [x1, #0xc08]
    // 0xaf5058: r2 = 600
    //     0xaf5058: movz            x2, #0x258
    // 0xaf505c: stur            x0, [fp, #-8]
    // 0xaf5060: r0 = []()
    //     0xaf5060: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf5064: r16 = <Color?>
    //     0xaf5064: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf5068: ldr             x16, [x16, #0x98]
    // 0xaf506c: stp             x0, x16, [SP, #8]
    // 0xaf5070: r16 = Instance_MaterialColor
    //     0xaf5070: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaf5074: ldr             x16, [x16, #0xcf0]
    // 0xaf5078: str             x16, [SP]
    // 0xaf507c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf507c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf5080: r0 = mode()
    //     0xaf5080: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf5084: stur            x0, [fp, #-0x28]
    // 0xaf5088: r0 = TextStyle()
    //     0xaf5088: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf508c: mov             x1, x0
    // 0xaf5090: r0 = true
    //     0xaf5090: add             x0, NULL, #0x20  ; true
    // 0xaf5094: stur            x1, [fp, #-0x30]
    // 0xaf5098: StoreField: r1->field_7 = r0
    //     0xaf5098: stur            w0, [x1, #7]
    // 0xaf509c: ldur            x2, [fp, #-0x28]
    // 0xaf50a0: StoreField: r1->field_b = r2
    //     0xaf50a0: stur            w2, [x1, #0xb]
    // 0xaf50a4: r0 = SubstringHighlight()
    //     0xaf50a4: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xaf50a8: mov             x1, x0
    // 0xaf50ac: r0 = false
    //     0xaf50ac: add             x0, NULL, #0x30  ; false
    // 0xaf50b0: stur            x1, [fp, #-0x28]
    // 0xaf50b4: StoreField: r1->field_b = r0
    //     0xaf50b4: stur            w0, [x1, #0xb]
    // 0xaf50b8: r2 = Instance_TextOverflow
    //     0xaf50b8: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xaf50bc: ldr             x2, [x2, #0xc60]
    // 0xaf50c0: StoreField: r1->field_f = r2
    //     0xaf50c0: stur            w2, [x1, #0xf]
    // 0xaf50c4: ldur            x2, [fp, #-8]
    // 0xaf50c8: StoreField: r1->field_1b = r2
    //     0xaf50c8: stur            w2, [x1, #0x1b]
    // 0xaf50cc: ldur            x2, [fp, #-0x10]
    // 0xaf50d0: StoreField: r1->field_1f = r2
    //     0xaf50d0: stur            w2, [x1, #0x1f]
    // 0xaf50d4: r2 = Instance_TextAlign
    //     0xaf50d4: ldr             x2, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xaf50d8: StoreField: r1->field_23 = r2
    //     0xaf50d8: stur            w2, [x1, #0x23]
    // 0xaf50dc: ldur            x2, [fp, #-0x20]
    // 0xaf50e0: StoreField: r1->field_27 = r2
    //     0xaf50e0: stur            w2, [x1, #0x27]
    // 0xaf50e4: ldur            x2, [fp, #-0x30]
    // 0xaf50e8: StoreField: r1->field_2b = r2
    //     0xaf50e8: stur            w2, [x1, #0x2b]
    // 0xaf50ec: StoreField: r1->field_2f = r0
    //     0xaf50ec: stur            w0, [x1, #0x2f]
    // 0xaf50f0: ldr             x2, [fp, #0x18]
    // 0xaf50f4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xaf50f4: ldur            w3, [x2, #0x17]
    // 0xaf50f8: DecompressPointer r3
    //     0xaf50f8: add             x3, x3, HEAP, lsl #32
    // 0xaf50fc: LoadField: r2 = r3->field_f
    //     0xaf50fc: ldur            w2, [x3, #0xf]
    // 0xaf5100: DecompressPointer r2
    //     0xaf5100: add             x2, x2, HEAP, lsl #32
    // 0xaf5104: stur            x2, [fp, #-8]
    // 0xaf5108: r0 = Text()
    //     0xaf5108: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf510c: mov             x3, x0
    // 0xaf5110: ldur            x0, [fp, #-8]
    // 0xaf5114: stur            x3, [fp, #-0x10]
    // 0xaf5118: StoreField: r3->field_b = r0
    //     0xaf5118: stur            w0, [x3, #0xb]
    // 0xaf511c: r1 = _ConstMap len:6
    //     0xaf511c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaf5120: ldr             x1, [x1, #0xc20]
    // 0xaf5124: r2 = 6
    //     0xaf5124: movz            x2, #0x6
    // 0xaf5128: r0 = []()
    //     0xaf5128: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf512c: r1 = _ConstMap len:6
    //     0xaf512c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaf5130: ldr             x1, [x1, #0xc20]
    // 0xaf5134: r2 = 8
    //     0xaf5134: movz            x2, #0x8
    // 0xaf5138: stur            x0, [fp, #-8]
    // 0xaf513c: r0 = []()
    //     0xaf513c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf5140: r16 = <Color?>
    //     0xaf5140: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf5144: ldr             x16, [x16, #0x98]
    // 0xaf5148: stp             x0, x16, [SP, #8]
    // 0xaf514c: ldur            x16, [fp, #-8]
    // 0xaf5150: str             x16, [SP]
    // 0xaf5154: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf5154: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf5158: r0 = mode()
    //     0xaf5158: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf515c: stur            x0, [fp, #-8]
    // 0xaf5160: r0 = Icon()
    //     0xaf5160: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xaf5164: mov             x1, x0
    // 0xaf5168: r0 = Instance_IconData
    //     0xaf5168: add             x0, PP, #0x29, lsl #12  ; [pp+0x29fa8] Obj!IconData@e0fe71
    //     0xaf516c: ldr             x0, [x0, #0xfa8]
    // 0xaf5170: stur            x1, [fp, #-0x20]
    // 0xaf5174: StoreField: r1->field_b = r0
    //     0xaf5174: stur            w0, [x1, #0xb]
    // 0xaf5178: r0 = 20.000000
    //     0xaf5178: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xaf517c: ldr             x0, [x0, #0x430]
    // 0xaf5180: StoreField: r1->field_f = r0
    //     0xaf5180: stur            w0, [x1, #0xf]
    // 0xaf5184: ldur            x0, [fp, #-8]
    // 0xaf5188: StoreField: r1->field_23 = r0
    //     0xaf5188: stur            w0, [x1, #0x23]
    // 0xaf518c: r0 = ListTile()
    //     0xaf518c: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xaf5190: mov             x3, x0
    // 0xaf5194: ldur            x0, [fp, #-0x28]
    // 0xaf5198: stur            x3, [fp, #-8]
    // 0xaf519c: StoreField: r3->field_f = r0
    //     0xaf519c: stur            w0, [x3, #0xf]
    // 0xaf51a0: ldur            x0, [fp, #-0x10]
    // 0xaf51a4: StoreField: r3->field_13 = r0
    //     0xaf51a4: stur            w0, [x3, #0x13]
    // 0xaf51a8: ldur            x0, [fp, #-0x20]
    // 0xaf51ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf51ac: stur            w0, [x3, #0x17]
    // 0xaf51b0: r0 = false
    //     0xaf51b0: add             x0, NULL, #0x30  ; false
    // 0xaf51b4: StoreField: r3->field_1b = r0
    //     0xaf51b4: stur            w0, [x3, #0x1b]
    // 0xaf51b8: r1 = Instance_EdgeInsets
    //     0xaf51b8: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf51bc: StoreField: r3->field_47 = r1
    //     0xaf51bc: stur            w1, [x3, #0x47]
    // 0xaf51c0: r4 = true
    //     0xaf51c0: add             x4, NULL, #0x20  ; true
    // 0xaf51c4: StoreField: r3->field_4b = r4
    //     0xaf51c4: stur            w4, [x3, #0x4b]
    // 0xaf51c8: ldur            x2, [fp, #-0x18]
    // 0xaf51cc: r1 = Function '<anonymous closure>':.
    //     0xaf51cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f090] AnonymousClosure: (0xaf5210), in [package:nuonline/app/modules/faq/views/faq_search_view.dart] FaqSearchView::build (0xaf4e98)
    //     0xaf51d0: ldr             x1, [x1, #0x90]
    // 0xaf51d4: r0 = AllocateClosure()
    //     0xaf51d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf51d8: mov             x1, x0
    // 0xaf51dc: ldur            x0, [fp, #-8]
    // 0xaf51e0: StoreField: r0->field_4f = r1
    //     0xaf51e0: stur            w1, [x0, #0x4f]
    // 0xaf51e4: r1 = false
    //     0xaf51e4: add             x1, NULL, #0x30  ; false
    // 0xaf51e8: StoreField: r0->field_5f = r1
    //     0xaf51e8: stur            w1, [x0, #0x5f]
    // 0xaf51ec: StoreField: r0->field_73 = r1
    //     0xaf51ec: stur            w1, [x0, #0x73]
    // 0xaf51f0: r1 = true
    //     0xaf51f0: add             x1, NULL, #0x20  ; true
    // 0xaf51f4: StoreField: r0->field_97 = r1
    //     0xaf51f4: stur            w1, [x0, #0x97]
    // 0xaf51f8: LeaveFrame
    //     0xaf51f8: mov             SP, fp
    //     0xaf51fc: ldp             fp, lr, [SP], #0x10
    // 0xaf5200: ret
    //     0xaf5200: ret             
    // 0xaf5204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5208: b               #0xaf4fd4
    // 0xaf520c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf520c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5210, size: 0x74
    // 0xaf5210: EnterFrame
    //     0xaf5210: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5214: mov             fp, SP
    // 0xaf5218: AllocStack(0x18)
    //     0xaf5218: sub             SP, SP, #0x18
    // 0xaf521c: SetupParameters()
    //     0xaf521c: ldr             x0, [fp, #0x10]
    //     0xaf5220: ldur            w2, [x0, #0x17]
    //     0xaf5224: add             x2, x2, HEAP, lsl #32
    //     0xaf5228: stur            x2, [fp, #-8]
    // 0xaf522c: CheckStackOverflow
    //     0xaf522c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5230: cmp             SP, x16
    //     0xaf5234: b.ls            #0xaf527c
    // 0xaf5238: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf5238: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf523c: ldr             x0, [x0, #0x2670]
    //     0xaf5240: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf5244: cmp             w0, w16
    //     0xaf5248: b.ne            #0xaf5254
    //     0xaf524c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf5250: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf5254: ldur            x2, [fp, #-8]
    // 0xaf5258: r1 = Function '<anonymous closure>':.
    //     0xaf5258: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f098] AnonymousClosure: (0xaf45e8), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf525c: ldr             x1, [x1, #0x98]
    // 0xaf5260: r0 = AllocateClosure()
    //     0xaf5260: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf5264: stp             x0, NULL, [SP]
    // 0xaf5268: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf5268: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf526c: r0 = GetNavigation.to()
    //     0xaf526c: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xaf5270: LeaveFrame
    //     0xaf5270: mov             SP, fp
    //     0xaf5274: ldp             fp, lr, [SP], #0x10
    // 0xaf5278: ret
    //     0xaf5278: ret             
    // 0xaf527c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf527c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5280: b               #0xaf5238
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xaf5284, size: 0x130
    // 0xaf5284: EnterFrame
    //     0xaf5284: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5288: mov             fp, SP
    // 0xaf528c: AllocStack(0x28)
    //     0xaf528c: sub             SP, SP, #0x28
    // 0xaf5290: SetupParameters()
    //     0xaf5290: ldr             x0, [fp, #0x10]
    //     0xaf5294: ldur            w2, [x0, #0x17]
    //     0xaf5298: add             x2, x2, HEAP, lsl #32
    //     0xaf529c: stur            x2, [fp, #-8]
    // 0xaf52a0: CheckStackOverflow
    //     0xaf52a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf52a4: cmp             SP, x16
    //     0xaf52a8: b.ls            #0xaf53ac
    // 0xaf52ac: LoadField: r1 = r2->field_f
    //     0xaf52ac: ldur            w1, [x2, #0xf]
    // 0xaf52b0: DecompressPointer r1
    //     0xaf52b0: add             x1, x1, HEAP, lsl #32
    // 0xaf52b4: r0 = controller()
    //     0xaf52b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf52b8: LoadField: r2 = r0->field_37
    //     0xaf52b8: ldur            w2, [x0, #0x37]
    // 0xaf52bc: DecompressPointer r2
    //     0xaf52bc: add             x2, x2, HEAP, lsl #32
    // 0xaf52c0: ldur            x0, [fp, #-8]
    // 0xaf52c4: stur            x2, [fp, #-0x10]
    // 0xaf52c8: LoadField: r1 = r0->field_f
    //     0xaf52c8: ldur            w1, [x0, #0xf]
    // 0xaf52cc: DecompressPointer r1
    //     0xaf52cc: add             x1, x1, HEAP, lsl #32
    // 0xaf52d0: r0 = controller()
    //     0xaf52d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf52d4: LoadField: r2 = r0->field_2f
    //     0xaf52d4: ldur            w2, [x0, #0x2f]
    // 0xaf52d8: DecompressPointer r2
    //     0xaf52d8: add             x2, x2, HEAP, lsl #32
    // 0xaf52dc: LoadField: r3 = r2->field_7
    //     0xaf52dc: ldur            w3, [x2, #7]
    // 0xaf52e0: DecompressPointer r3
    //     0xaf52e0: add             x3, x3, HEAP, lsl #32
    // 0xaf52e4: r1 = Function 'call':.
    //     0xaf52e4: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xaf52e8: ldr             x1, [x1, #0x310]
    // 0xaf52ec: r0 = AllocateClosureTA()
    //     0xaf52ec: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xaf52f0: mov             x3, x0
    // 0xaf52f4: r2 = Null
    //     0xaf52f4: mov             x2, NULL
    // 0xaf52f8: r1 = Null
    //     0xaf52f8: mov             x1, NULL
    // 0xaf52fc: stur            x3, [fp, #-0x18]
    // 0xaf5300: r8 = (dynamic this, String?) => String
    //     0xaf5300: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a040] FunctionType: (dynamic this, String?) => String
    //     0xaf5304: ldr             x8, [x8, #0x40]
    // 0xaf5308: r3 = Null
    //     0xaf5308: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a0] Null
    //     0xaf530c: ldr             x3, [x3, #0xa0]
    // 0xaf5310: r0 = DefaultTypeTest()
    //     0xaf5310: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xaf5314: ldur            x0, [fp, #-8]
    // 0xaf5318: LoadField: r1 = r0->field_f
    //     0xaf5318: ldur            w1, [x0, #0xf]
    // 0xaf531c: DecompressPointer r1
    //     0xaf531c: add             x1, x1, HEAP, lsl #32
    // 0xaf5320: r0 = controller()
    //     0xaf5320: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf5324: mov             x2, x0
    // 0xaf5328: ldur            x0, [fp, #-8]
    // 0xaf532c: stur            x2, [fp, #-0x20]
    // 0xaf5330: LoadField: r1 = r0->field_f
    //     0xaf5330: ldur            w1, [x0, #0xf]
    // 0xaf5334: DecompressPointer r1
    //     0xaf5334: add             x1, x1, HEAP, lsl #32
    // 0xaf5338: r0 = controller()
    //     0xaf5338: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf533c: LoadField: r1 = r0->field_2f
    //     0xaf533c: ldur            w1, [x0, #0x2f]
    // 0xaf5340: DecompressPointer r1
    //     0xaf5340: add             x1, x1, HEAP, lsl #32
    // 0xaf5344: r0 = value()
    //     0xaf5344: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf5348: stur            x0, [fp, #-8]
    // 0xaf534c: r0 = NSearchTextField()
    //     0xaf534c: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xaf5350: mov             x3, x0
    // 0xaf5354: ldur            x0, [fp, #-0x10]
    // 0xaf5358: stur            x3, [fp, #-0x28]
    // 0xaf535c: StoreField: r3->field_f = r0
    //     0xaf535c: stur            w0, [x3, #0xf]
    // 0xaf5360: ldur            x0, [fp, #-8]
    // 0xaf5364: StoreField: r3->field_b = r0
    //     0xaf5364: stur            w0, [x3, #0xb]
    // 0xaf5368: ldur            x0, [fp, #-0x18]
    // 0xaf536c: StoreField: r3->field_13 = r0
    //     0xaf536c: stur            w0, [x3, #0x13]
    // 0xaf5370: ldur            x2, [fp, #-0x20]
    // 0xaf5374: r1 = Function 'clear':.
    //     0xaf5374: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] AnonymousClosure: (0xaf53b4), in [package:nuonline/app/modules/faq/controllers/faq_search_controller.dart] FaqSearchController::clear (0xaf53ec)
    //     0xaf5378: ldr             x1, [x1, #0xb0]
    // 0xaf537c: r0 = AllocateClosure()
    //     0xaf537c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf5380: mov             x1, x0
    // 0xaf5384: ldur            x0, [fp, #-0x28]
    // 0xaf5388: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf5388: stur            w1, [x0, #0x17]
    // 0xaf538c: r1 = "Cari Pusat Bantuan Kamu"
    //     0xaf538c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eff8] "Cari Pusat Bantuan Kamu"
    //     0xaf5390: ldr             x1, [x1, #0xff8]
    // 0xaf5394: StoreField: r0->field_23 = r1
    //     0xaf5394: stur            w1, [x0, #0x23]
    // 0xaf5398: r1 = false
    //     0xaf5398: add             x1, NULL, #0x30  ; false
    // 0xaf539c: StoreField: r0->field_27 = r1
    //     0xaf539c: stur            w1, [x0, #0x27]
    // 0xaf53a0: LeaveFrame
    //     0xaf53a0: mov             SP, fp
    //     0xaf53a4: ldp             fp, lr, [SP], #0x10
    // 0xaf53a8: ret
    //     0xaf53a8: ret             
    // 0xaf53ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf53ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf53b0: b               #0xaf52ac
  }
}
