// lib: , url: package:nuonline/app/modules/faq/views/faq_view.dart

// class id: 1050277, size: 0x8
class :: {
}

// class id: 5275, size: 0x14, field offset: 0x14
//   const constructor, 
class FaqView extends GetView<dynamic> {

  [closure] FaqDetailView <anonymous closure>(dynamic) {
    // ** addr: 0xaf45e8, size: 0x3c
    // 0xaf45e8: EnterFrame
    //     0xaf45e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf45ec: mov             fp, SP
    // 0xaf45f0: AllocStack(0x8)
    //     0xaf45f0: sub             SP, SP, #8
    // 0xaf45f4: SetupParameters()
    //     0xaf45f4: ldr             x0, [fp, #0x10]
    //     0xaf45f8: ldur            w1, [x0, #0x17]
    //     0xaf45fc: add             x1, x1, HEAP, lsl #32
    // 0xaf4600: LoadField: r0 = r1->field_f
    //     0xaf4600: ldur            w0, [x1, #0xf]
    // 0xaf4604: DecompressPointer r0
    //     0xaf4604: add             x0, x0, HEAP, lsl #32
    // 0xaf4608: stur            x0, [fp, #-8]
    // 0xaf460c: r0 = FaqDetailView()
    //     0xaf460c: bl              #0xaf4cc8  ; AllocateFaqDetailViewStub -> FaqDetailView (size=0x10)
    // 0xaf4610: ldur            x1, [fp, #-8]
    // 0xaf4614: StoreField: r0->field_b = r1
    //     0xaf4614: stur            w1, [x0, #0xb]
    // 0xaf4618: LeaveFrame
    //     0xaf4618: mov             SP, fp
    //     0xaf461c: ldp             fp, lr, [SP], #0x10
    // 0xaf4620: ret
    //     0xaf4620: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf4624, size: 0x74
    // 0xaf4624: EnterFrame
    //     0xaf4624: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4628: mov             fp, SP
    // 0xaf462c: AllocStack(0x18)
    //     0xaf462c: sub             SP, SP, #0x18
    // 0xaf4630: SetupParameters()
    //     0xaf4630: ldr             x0, [fp, #0x10]
    //     0xaf4634: ldur            w2, [x0, #0x17]
    //     0xaf4638: add             x2, x2, HEAP, lsl #32
    //     0xaf463c: stur            x2, [fp, #-8]
    // 0xaf4640: CheckStackOverflow
    //     0xaf4640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4644: cmp             SP, x16
    //     0xaf4648: b.ls            #0xaf4690
    // 0xaf464c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf464c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf4650: ldr             x0, [x0, #0x2670]
    //     0xaf4654: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf4658: cmp             w0, w16
    //     0xaf465c: b.ne            #0xaf4668
    //     0xaf4660: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf4664: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf4668: ldur            x2, [fp, #-8]
    // 0xaf466c: r1 = Function '<anonymous closure>':.
    //     0xaf466c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f068] AnonymousClosure: (0xaf45e8), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf4670: ldr             x1, [x1, #0x68]
    // 0xaf4674: r0 = AllocateClosure()
    //     0xaf4674: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4678: stp             x0, NULL, [SP]
    // 0xaf467c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf467c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf4680: r0 = GetNavigation.to()
    //     0xaf4680: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xaf4684: LeaveFrame
    //     0xaf4684: mov             SP, fp
    //     0xaf4688: ldp             fp, lr, [SP], #0x10
    // 0xaf468c: ret
    //     0xaf468c: ret             
    // 0xaf4690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4694: b               #0xaf464c
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, Faq, int) {
    // ** addr: 0xaf4698, size: 0x184
    // 0xaf4698: EnterFrame
    //     0xaf4698: stp             fp, lr, [SP, #-0x10]!
    //     0xaf469c: mov             fp, SP
    // 0xaf46a0: AllocStack(0x38)
    //     0xaf46a0: sub             SP, SP, #0x38
    // 0xaf46a4: SetupParameters()
    //     0xaf46a4: ldr             x0, [fp, #0x28]
    //     0xaf46a8: ldur            w1, [x0, #0x17]
    //     0xaf46ac: add             x1, x1, HEAP, lsl #32
    //     0xaf46b0: stur            x1, [fp, #-8]
    // 0xaf46b4: CheckStackOverflow
    //     0xaf46b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf46b8: cmp             SP, x16
    //     0xaf46bc: b.ls            #0xaf4814
    // 0xaf46c0: r1 = 1
    //     0xaf46c0: movz            x1, #0x1
    // 0xaf46c4: r0 = AllocateContext()
    //     0xaf46c4: bl              #0xec126c  ; AllocateContextStub
    // 0xaf46c8: mov             x1, x0
    // 0xaf46cc: ldur            x0, [fp, #-8]
    // 0xaf46d0: stur            x1, [fp, #-0x10]
    // 0xaf46d4: StoreField: r1->field_b = r0
    //     0xaf46d4: stur            w0, [x1, #0xb]
    // 0xaf46d8: ldr             x0, [fp, #0x18]
    // 0xaf46dc: StoreField: r1->field_f = r0
    //     0xaf46dc: stur            w0, [x1, #0xf]
    // 0xaf46e0: LoadField: r2 = r0->field_f
    //     0xaf46e0: ldur            w2, [x0, #0xf]
    // 0xaf46e4: DecompressPointer r2
    //     0xaf46e4: add             x2, x2, HEAP, lsl #32
    // 0xaf46e8: stur            x2, [fp, #-8]
    // 0xaf46ec: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf46ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf46f0: ldr             x0, [x0, #0x2670]
    //     0xaf46f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf46f8: cmp             w0, w16
    //     0xaf46fc: b.ne            #0xaf4708
    //     0xaf4700: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf4704: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf4708: r0 = GetNavigation.textTheme()
    //     0xaf4708: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf470c: LoadField: r1 = r0->field_23
    //     0xaf470c: ldur            w1, [x0, #0x23]
    // 0xaf4710: DecompressPointer r1
    //     0xaf4710: add             x1, x1, HEAP, lsl #32
    // 0xaf4714: stur            x1, [fp, #-0x18]
    // 0xaf4718: r0 = Text()
    //     0xaf4718: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf471c: mov             x3, x0
    // 0xaf4720: ldur            x0, [fp, #-8]
    // 0xaf4724: stur            x3, [fp, #-0x20]
    // 0xaf4728: StoreField: r3->field_b = r0
    //     0xaf4728: stur            w0, [x3, #0xb]
    // 0xaf472c: ldur            x0, [fp, #-0x18]
    // 0xaf4730: StoreField: r3->field_13 = r0
    //     0xaf4730: stur            w0, [x3, #0x13]
    // 0xaf4734: r1 = _ConstMap len:6
    //     0xaf4734: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaf4738: ldr             x1, [x1, #0xc20]
    // 0xaf473c: r2 = 6
    //     0xaf473c: movz            x2, #0x6
    // 0xaf4740: r0 = []()
    //     0xaf4740: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf4744: r1 = _ConstMap len:6
    //     0xaf4744: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xaf4748: ldr             x1, [x1, #0xc20]
    // 0xaf474c: r2 = 8
    //     0xaf474c: movz            x2, #0x8
    // 0xaf4750: stur            x0, [fp, #-8]
    // 0xaf4754: r0 = []()
    //     0xaf4754: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf4758: r16 = <Color?>
    //     0xaf4758: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf475c: ldr             x16, [x16, #0x98]
    // 0xaf4760: stp             x0, x16, [SP, #8]
    // 0xaf4764: ldur            x16, [fp, #-8]
    // 0xaf4768: str             x16, [SP]
    // 0xaf476c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf476c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf4770: r0 = mode()
    //     0xaf4770: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf4774: stur            x0, [fp, #-8]
    // 0xaf4778: r0 = Icon()
    //     0xaf4778: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xaf477c: mov             x1, x0
    // 0xaf4780: r0 = Instance_IconData
    //     0xaf4780: add             x0, PP, #0x29, lsl #12  ; [pp+0x29fa8] Obj!IconData@e0fe71
    //     0xaf4784: ldr             x0, [x0, #0xfa8]
    // 0xaf4788: stur            x1, [fp, #-0x18]
    // 0xaf478c: StoreField: r1->field_b = r0
    //     0xaf478c: stur            w0, [x1, #0xb]
    // 0xaf4790: r0 = 20.000000
    //     0xaf4790: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xaf4794: ldr             x0, [x0, #0x430]
    // 0xaf4798: StoreField: r1->field_f = r0
    //     0xaf4798: stur            w0, [x1, #0xf]
    // 0xaf479c: ldur            x0, [fp, #-8]
    // 0xaf47a0: StoreField: r1->field_23 = r0
    //     0xaf47a0: stur            w0, [x1, #0x23]
    // 0xaf47a4: r0 = ListTile()
    //     0xaf47a4: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xaf47a8: mov             x3, x0
    // 0xaf47ac: ldur            x0, [fp, #-0x20]
    // 0xaf47b0: stur            x3, [fp, #-8]
    // 0xaf47b4: StoreField: r3->field_f = r0
    //     0xaf47b4: stur            w0, [x3, #0xf]
    // 0xaf47b8: ldur            x0, [fp, #-0x18]
    // 0xaf47bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf47bc: stur            w0, [x3, #0x17]
    // 0xaf47c0: r0 = false
    //     0xaf47c0: add             x0, NULL, #0x30  ; false
    // 0xaf47c4: StoreField: r3->field_1b = r0
    //     0xaf47c4: stur            w0, [x3, #0x1b]
    // 0xaf47c8: r1 = Instance_EdgeInsets
    //     0xaf47c8: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf47cc: StoreField: r3->field_47 = r1
    //     0xaf47cc: stur            w1, [x3, #0x47]
    // 0xaf47d0: r4 = true
    //     0xaf47d0: add             x4, NULL, #0x20  ; true
    // 0xaf47d4: StoreField: r3->field_4b = r4
    //     0xaf47d4: stur            w4, [x3, #0x4b]
    // 0xaf47d8: ldur            x2, [fp, #-0x10]
    // 0xaf47dc: r1 = Function '<anonymous closure>':.
    //     0xaf47dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f060] AnonymousClosure: (0xaf4624), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf47e0: ldr             x1, [x1, #0x60]
    // 0xaf47e4: r0 = AllocateClosure()
    //     0xaf47e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf47e8: mov             x1, x0
    // 0xaf47ec: ldur            x0, [fp, #-8]
    // 0xaf47f0: StoreField: r0->field_4f = r1
    //     0xaf47f0: stur            w1, [x0, #0x4f]
    // 0xaf47f4: r1 = false
    //     0xaf47f4: add             x1, NULL, #0x30  ; false
    // 0xaf47f8: StoreField: r0->field_5f = r1
    //     0xaf47f8: stur            w1, [x0, #0x5f]
    // 0xaf47fc: StoreField: r0->field_73 = r1
    //     0xaf47fc: stur            w1, [x0, #0x73]
    // 0xaf4800: r1 = true
    //     0xaf4800: add             x1, NULL, #0x20  ; true
    // 0xaf4804: StoreField: r0->field_97 = r1
    //     0xaf4804: stur            w1, [x0, #0x97]
    // 0xaf4808: LeaveFrame
    //     0xaf4808: mov             SP, fp
    //     0xaf480c: ldp             fp, lr, [SP], #0x10
    // 0xaf4810: ret
    //     0xaf4810: ret             
    // 0xaf4814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4818: b               #0xaf46c0
  }
  [closure] Widget <anonymous closure>(dynamic, FaqListController) {
    // ** addr: 0xaf481c, size: 0x64
    // 0xaf481c: EnterFrame
    //     0xaf481c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4820: mov             fp, SP
    // 0xaf4824: AllocStack(0x8)
    //     0xaf4824: sub             SP, SP, #8
    // 0xaf4828: CheckStackOverflow
    //     0xaf4828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf482c: cmp             SP, x16
    //     0xaf4830: b.ls            #0xaf4878
    // 0xaf4834: r1 = Function '<anonymous closure>':.
    //     0xaf4834: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f058] AnonymousClosure: (0xaf4698), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf4838: ldr             x1, [x1, #0x58]
    // 0xaf483c: r2 = Null
    //     0xaf483c: mov             x2, NULL
    // 0xaf4840: r0 = AllocateClosure()
    //     0xaf4840: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4844: r16 = Instance_EdgeInsets
    //     0xaf4844: add             x16, PP, #0x28, lsl #12  ; [pp+0x28560] Obj!EdgeInsets@e128e1
    //     0xaf4848: ldr             x16, [x16, #0x560]
    // 0xaf484c: str             x16, [SP]
    // 0xaf4850: ldr             x1, [fp, #0x10]
    // 0xaf4854: mov             x2, x0
    // 0xaf4858: r3 = Instance_Divider
    //     0xaf4858: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xaf485c: ldr             x3, [x3, #0xc28]
    // 0xaf4860: r4 = const [0, 0x4, 0x1, 0x3, padding, 0x3, null]
    //     0xaf4860: add             x4, PP, #0x29, lsl #12  ; [pp+0x29660] List(7) [0, 0x4, 0x1, 0x3, "padding", 0x3, Null]
    //     0xaf4864: ldr             x4, [x4, #0x660]
    // 0xaf4868: r0 = paginate()
    //     0xaf4868: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xaf486c: LeaveFrame
    //     0xaf486c: mov             SP, fp
    //     0xaf4870: ldp             fp, lr, [SP], #0x10
    // 0xaf4874: ret
    //     0xaf4874: ret             
    // 0xaf4878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf487c: b               #0xaf4834
  }
  [closure] GetBuilder<FaqListController> <anonymous closure>(dynamic, FaqCategory) {
    // ** addr: 0xaf4880, size: 0xf4
    // 0xaf4880: EnterFrame
    //     0xaf4880: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4884: mov             fp, SP
    // 0xaf4888: AllocStack(0x28)
    //     0xaf4888: sub             SP, SP, #0x28
    // 0xaf488c: CheckStackOverflow
    //     0xaf488c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4890: cmp             SP, x16
    //     0xaf4894: b.ls            #0xaf496c
    // 0xaf4898: ldr             x0, [fp, #0x10]
    // 0xaf489c: LoadField: r2 = r0->field_13
    //     0xaf489c: ldur            w2, [x0, #0x13]
    // 0xaf48a0: DecompressPointer r2
    //     0xaf48a0: add             x2, x2, HEAP, lsl #32
    // 0xaf48a4: stur            x2, [fp, #-8]
    // 0xaf48a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf48a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf48ac: ldr             x0, [x0, #0x2670]
    //     0xaf48b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf48b4: cmp             w0, w16
    //     0xaf48b8: b.ne            #0xaf48c4
    //     0xaf48bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf48c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf48c4: r16 = <FaqRepository>
    //     0xaf48c4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10230] TypeArguments: <FaqRepository>
    //     0xaf48c8: ldr             x16, [x16, #0x230]
    // 0xaf48cc: r30 = "faq_repo"
    //     0xaf48cc: add             lr, PP, #0x10, lsl #12  ; [pp+0x10238] "faq_repo"
    //     0xaf48d0: ldr             lr, [lr, #0x238]
    // 0xaf48d4: stp             lr, x16, [SP]
    // 0xaf48d8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xaf48d8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xaf48dc: r0 = Inst.find()
    //     0xaf48dc: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xaf48e0: r1 = <List<Faq>, Faq>
    //     0xaf48e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f040] TypeArguments: <List<Faq>, Faq>
    //     0xaf48e4: ldr             x1, [x1, #0x40]
    // 0xaf48e8: stur            x0, [fp, #-0x10]
    // 0xaf48ec: r0 = FaqListController()
    //     0xaf48ec: bl              #0x813108  ; AllocateFaqListControllerStub -> FaqListController (size=0x44)
    // 0xaf48f0: mov             x1, x0
    // 0xaf48f4: ldur            x2, [fp, #-8]
    // 0xaf48f8: ldur            x3, [fp, #-0x10]
    // 0xaf48fc: stur            x0, [fp, #-0x10]
    // 0xaf4900: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xaf4900: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xaf4904: r0 = FaqListController()
    //     0xaf4904: bl              #0x812e8c  ; [package:nuonline/app/modules/faq/controllers/faq_list_controller.dart] FaqListController::FaqListController
    // 0xaf4908: r1 = <FaqListController>
    //     0xaf4908: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f048] TypeArguments: <FaqListController>
    //     0xaf490c: ldr             x1, [x1, #0x48]
    // 0xaf4910: r0 = GetBuilder()
    //     0xaf4910: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xaf4914: mov             x3, x0
    // 0xaf4918: ldur            x0, [fp, #-0x10]
    // 0xaf491c: stur            x3, [fp, #-0x18]
    // 0xaf4920: StoreField: r3->field_3b = r0
    //     0xaf4920: stur            w0, [x3, #0x3b]
    // 0xaf4924: r0 = false
    //     0xaf4924: add             x0, NULL, #0x30  ; false
    // 0xaf4928: StoreField: r3->field_13 = r0
    //     0xaf4928: stur            w0, [x3, #0x13]
    // 0xaf492c: r1 = Function '<anonymous closure>':.
    //     0xaf492c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] AnonymousClosure: (0xaf481c), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf4930: ldr             x1, [x1, #0x50]
    // 0xaf4934: r2 = Null
    //     0xaf4934: mov             x2, NULL
    // 0xaf4938: r0 = AllocateClosure()
    //     0xaf4938: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf493c: mov             x1, x0
    // 0xaf4940: ldur            x0, [fp, #-0x18]
    // 0xaf4944: StoreField: r0->field_f = r1
    //     0xaf4944: stur            w1, [x0, #0xf]
    // 0xaf4948: r1 = true
    //     0xaf4948: add             x1, NULL, #0x20  ; true
    // 0xaf494c: StoreField: r0->field_1f = r1
    //     0xaf494c: stur            w1, [x0, #0x1f]
    // 0xaf4950: r1 = false
    //     0xaf4950: add             x1, NULL, #0x30  ; false
    // 0xaf4954: StoreField: r0->field_23 = r1
    //     0xaf4954: stur            w1, [x0, #0x23]
    // 0xaf4958: ldur            x1, [fp, #-8]
    // 0xaf495c: StoreField: r0->field_1b = r1
    //     0xaf495c: stur            w1, [x0, #0x1b]
    // 0xaf4960: LeaveFrame
    //     0xaf4960: mov             SP, fp
    //     0xaf4964: ldp             fp, lr, [SP], #0x10
    // 0xaf4968: ret
    //     0xaf4968: ret             
    // 0xaf496c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf496c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4970: b               #0xaf4898
  }
  [closure] DefaultTabController <anonymous closure>(dynamic, List<FaqCategory>) {
    // ** addr: 0xaf4974, size: 0x314
    // 0xaf4974: EnterFrame
    //     0xaf4974: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4978: mov             fp, SP
    // 0xaf497c: AllocStack(0x40)
    //     0xaf497c: sub             SP, SP, #0x40
    // 0xaf4980: CheckStackOverflow
    //     0xaf4980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4984: cmp             SP, x16
    //     0xaf4988: b.ls            #0xaf4c7c
    // 0xaf498c: ldr             x1, [fp, #0x10]
    // 0xaf4990: r0 = LoadClassIdInstr(r1)
    //     0xaf4990: ldur            x0, [x1, #-1]
    //     0xaf4994: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4998: str             x1, [SP]
    // 0xaf499c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf499c: movz            x17, #0xc834
    //     0xaf49a0: add             lr, x0, x17
    //     0xaf49a4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf49a8: blr             lr
    // 0xaf49ac: r1 = _ConstMap len:3
    //     0xaf49ac: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaf49b0: ldr             x1, [x1, #0xbe8]
    // 0xaf49b4: r2 = 2
    //     0xaf49b4: movz            x2, #0x2
    // 0xaf49b8: stur            x0, [fp, #-8]
    // 0xaf49bc: r0 = []()
    //     0xaf49bc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf49c0: cmp             w0, NULL
    // 0xaf49c4: b.eq            #0xaf4c84
    // 0xaf49c8: r16 = <Color>
    //     0xaf49c8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xaf49cc: ldr             x16, [x16, #0x158]
    // 0xaf49d0: stp             x0, x16, [SP, #8]
    // 0xaf49d4: r16 = Instance_MaterialColor
    //     0xaf49d4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xaf49d8: ldr             x16, [x16, #0xe38]
    // 0xaf49dc: str             x16, [SP]
    // 0xaf49e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf49e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf49e4: r0 = mode()
    //     0xaf49e4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf49e8: r1 = Function '<anonymous closure>':.
    //     0xaf49e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f020] AnonymousClosure: (0xaf4c94), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf49ec: ldr             x1, [x1, #0x20]
    // 0xaf49f0: r2 = Null
    //     0xaf49f0: mov             x2, NULL
    // 0xaf49f4: stur            x0, [fp, #-0x10]
    // 0xaf49f8: r0 = AllocateClosure()
    //     0xaf49f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf49fc: ldr             x1, [fp, #0x10]
    // 0xaf4a00: r2 = LoadClassIdInstr(r1)
    //     0xaf4a00: ldur            x2, [x1, #-1]
    //     0xaf4a04: ubfx            x2, x2, #0xc, #0x14
    // 0xaf4a08: r16 = <Tab>
    //     0xaf4a08: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f028] TypeArguments: <Tab>
    //     0xaf4a0c: ldr             x16, [x16, #0x28]
    // 0xaf4a10: stp             x1, x16, [SP, #8]
    // 0xaf4a14: str             x0, [SP]
    // 0xaf4a18: mov             x0, x2
    // 0xaf4a1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf4a1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf4a20: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xaf4a20: movz            x17, #0xf28c
    //     0xaf4a24: add             lr, x0, x17
    //     0xaf4a28: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4a2c: blr             lr
    // 0xaf4a30: r1 = LoadClassIdInstr(r0)
    //     0xaf4a30: ldur            x1, [x0, #-1]
    //     0xaf4a34: ubfx            x1, x1, #0xc, #0x14
    // 0xaf4a38: mov             x16, x0
    // 0xaf4a3c: mov             x0, x1
    // 0xaf4a40: mov             x1, x16
    // 0xaf4a44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf4a44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf4a48: r0 = GDT[cid_x0 + 0xd889]()
    //     0xaf4a48: movz            x17, #0xd889
    //     0xaf4a4c: add             lr, x0, x17
    //     0xaf4a50: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4a54: blr             lr
    // 0xaf4a58: stur            x0, [fp, #-0x18]
    // 0xaf4a5c: r0 = TabBar()
    //     0xaf4a5c: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xaf4a60: mov             x1, x0
    // 0xaf4a64: ldur            x0, [fp, #-0x18]
    // 0xaf4a68: stur            x1, [fp, #-0x20]
    // 0xaf4a6c: StoreField: r1->field_b = r0
    //     0xaf4a6c: stur            w0, [x1, #0xb]
    // 0xaf4a70: r0 = true
    //     0xaf4a70: add             x0, NULL, #0x20  ; true
    // 0xaf4a74: StoreField: r1->field_13 = r0
    //     0xaf4a74: stur            w0, [x1, #0x13]
    // 0xaf4a78: r2 = Instance_EdgeInsets
    //     0xaf4a78: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf4a7c: ArrayStore: r1[0] = r2  ; List_4
    //     0xaf4a7c: stur            w2, [x1, #0x17]
    // 0xaf4a80: StoreField: r1->field_2f = r0
    //     0xaf4a80: stur            w0, [x1, #0x2f]
    // 0xaf4a84: d0 = 2.000000
    //     0xaf4a84: fmov            d0, #2.00000000
    // 0xaf4a88: StoreField: r1->field_1f = d0
    //     0xaf4a88: stur            d0, [x1, #0x1f]
    // 0xaf4a8c: StoreField: r1->field_27 = r2
    //     0xaf4a8c: stur            w2, [x1, #0x27]
    // 0xaf4a90: r2 = Instance_DragStartBehavior
    //     0xaf4a90: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf4a94: StoreField: r1->field_57 = r2
    //     0xaf4a94: stur            w2, [x1, #0x57]
    // 0xaf4a98: r3 = Instance_ClampingScrollPhysics
    //     0xaf4a98: add             x3, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xaf4a9c: ldr             x3, [x3, #0x410]
    // 0xaf4aa0: StoreField: r1->field_67 = r3
    //     0xaf4aa0: stur            w3, [x1, #0x67]
    // 0xaf4aa4: r3 = Instance_TabAlignment
    //     0xaf4aa4: add             x3, PP, #0x1d, lsl #12  ; [pp+0x1d0b8] Obj!TabAlignment@e36121
    //     0xaf4aa8: ldr             x3, [x3, #0xb8]
    // 0xaf4aac: StoreField: r1->field_73 = r3
    //     0xaf4aac: stur            w3, [x1, #0x73]
    // 0xaf4ab0: StoreField: r1->field_7f = r0
    //     0xaf4ab0: stur            w0, [x1, #0x7f]
    // 0xaf4ab4: r0 = ColoredBox()
    //     0xaf4ab4: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xaf4ab8: mov             x3, x0
    // 0xaf4abc: ldur            x0, [fp, #-0x10]
    // 0xaf4ac0: stur            x3, [fp, #-0x18]
    // 0xaf4ac4: StoreField: r3->field_f = r0
    //     0xaf4ac4: stur            w0, [x3, #0xf]
    // 0xaf4ac8: ldur            x0, [fp, #-0x20]
    // 0xaf4acc: StoreField: r3->field_b = r0
    //     0xaf4acc: stur            w0, [x3, #0xb]
    // 0xaf4ad0: r1 = Function '<anonymous closure>':.
    //     0xaf4ad0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f030] AnonymousClosure: (0xaf4880), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf4ad4: ldr             x1, [x1, #0x30]
    // 0xaf4ad8: r2 = Null
    //     0xaf4ad8: mov             x2, NULL
    // 0xaf4adc: r0 = AllocateClosure()
    //     0xaf4adc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf4ae0: mov             x1, x0
    // 0xaf4ae4: ldr             x0, [fp, #0x10]
    // 0xaf4ae8: r2 = LoadClassIdInstr(r0)
    //     0xaf4ae8: ldur            x2, [x0, #-1]
    //     0xaf4aec: ubfx            x2, x2, #0xc, #0x14
    // 0xaf4af0: r16 = <GetBuilder<FaqListController>>
    //     0xaf4af0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f038] TypeArguments: <GetBuilder<FaqListController>>
    //     0xaf4af4: ldr             x16, [x16, #0x38]
    // 0xaf4af8: stp             x0, x16, [SP, #8]
    // 0xaf4afc: str             x1, [SP]
    // 0xaf4b00: mov             x0, x2
    // 0xaf4b04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf4b04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf4b08: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xaf4b08: movz            x17, #0xf28c
    //     0xaf4b0c: add             lr, x0, x17
    //     0xaf4b10: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4b14: blr             lr
    // 0xaf4b18: r1 = LoadClassIdInstr(r0)
    //     0xaf4b18: ldur            x1, [x0, #-1]
    //     0xaf4b1c: ubfx            x1, x1, #0xc, #0x14
    // 0xaf4b20: mov             x16, x0
    // 0xaf4b24: mov             x0, x1
    // 0xaf4b28: mov             x1, x16
    // 0xaf4b2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf4b2c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf4b30: r0 = GDT[cid_x0 + 0xd889]()
    //     0xaf4b30: movz            x17, #0xd889
    //     0xaf4b34: add             lr, x0, x17
    //     0xaf4b38: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4b3c: blr             lr
    // 0xaf4b40: stur            x0, [fp, #-0x10]
    // 0xaf4b44: r0 = TabBarView()
    //     0xaf4b44: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xaf4b48: mov             x2, x0
    // 0xaf4b4c: ldur            x0, [fp, #-0x10]
    // 0xaf4b50: stur            x2, [fp, #-0x20]
    // 0xaf4b54: StoreField: r2->field_f = r0
    //     0xaf4b54: stur            w0, [x2, #0xf]
    // 0xaf4b58: r0 = Instance_DragStartBehavior
    //     0xaf4b58: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf4b5c: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf4b5c: stur            w0, [x2, #0x17]
    // 0xaf4b60: d0 = 1.000000
    //     0xaf4b60: fmov            d0, #1.00000000
    // 0xaf4b64: StoreField: r2->field_1b = d0
    //     0xaf4b64: stur            d0, [x2, #0x1b]
    // 0xaf4b68: r0 = Instance_Clip
    //     0xaf4b68: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xaf4b6c: ldr             x0, [x0, #0x7c0]
    // 0xaf4b70: StoreField: r2->field_23 = r0
    //     0xaf4b70: stur            w0, [x2, #0x23]
    // 0xaf4b74: r1 = <FlexParentData>
    //     0xaf4b74: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf4b78: ldr             x1, [x1, #0x720]
    // 0xaf4b7c: r0 = Expanded()
    //     0xaf4b7c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf4b80: mov             x3, x0
    // 0xaf4b84: r0 = 1
    //     0xaf4b84: movz            x0, #0x1
    // 0xaf4b88: stur            x3, [fp, #-0x10]
    // 0xaf4b8c: StoreField: r3->field_13 = r0
    //     0xaf4b8c: stur            x0, [x3, #0x13]
    // 0xaf4b90: r0 = Instance_FlexFit
    //     0xaf4b90: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf4b94: ldr             x0, [x0, #0x728]
    // 0xaf4b98: StoreField: r3->field_1b = r0
    //     0xaf4b98: stur            w0, [x3, #0x1b]
    // 0xaf4b9c: ldur            x0, [fp, #-0x20]
    // 0xaf4ba0: StoreField: r3->field_b = r0
    //     0xaf4ba0: stur            w0, [x3, #0xb]
    // 0xaf4ba4: r1 = Null
    //     0xaf4ba4: mov             x1, NULL
    // 0xaf4ba8: r2 = 4
    //     0xaf4ba8: movz            x2, #0x4
    // 0xaf4bac: r0 = AllocateArray()
    //     0xaf4bac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf4bb0: mov             x2, x0
    // 0xaf4bb4: ldur            x0, [fp, #-0x18]
    // 0xaf4bb8: stur            x2, [fp, #-0x20]
    // 0xaf4bbc: StoreField: r2->field_f = r0
    //     0xaf4bbc: stur            w0, [x2, #0xf]
    // 0xaf4bc0: ldur            x0, [fp, #-0x10]
    // 0xaf4bc4: StoreField: r2->field_13 = r0
    //     0xaf4bc4: stur            w0, [x2, #0x13]
    // 0xaf4bc8: r1 = <Widget>
    //     0xaf4bc8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf4bcc: r0 = AllocateGrowableArray()
    //     0xaf4bcc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf4bd0: mov             x1, x0
    // 0xaf4bd4: ldur            x0, [fp, #-0x20]
    // 0xaf4bd8: stur            x1, [fp, #-0x10]
    // 0xaf4bdc: StoreField: r1->field_f = r0
    //     0xaf4bdc: stur            w0, [x1, #0xf]
    // 0xaf4be0: r0 = 4
    //     0xaf4be0: movz            x0, #0x4
    // 0xaf4be4: StoreField: r1->field_b = r0
    //     0xaf4be4: stur            w0, [x1, #0xb]
    // 0xaf4be8: r0 = Column()
    //     0xaf4be8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf4bec: mov             x1, x0
    // 0xaf4bf0: r0 = Instance_Axis
    //     0xaf4bf0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf4bf4: stur            x1, [fp, #-0x18]
    // 0xaf4bf8: StoreField: r1->field_f = r0
    //     0xaf4bf8: stur            w0, [x1, #0xf]
    // 0xaf4bfc: r0 = Instance_MainAxisAlignment
    //     0xaf4bfc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf4c00: ldr             x0, [x0, #0x730]
    // 0xaf4c04: StoreField: r1->field_13 = r0
    //     0xaf4c04: stur            w0, [x1, #0x13]
    // 0xaf4c08: r0 = Instance_MainAxisSize
    //     0xaf4c08: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf4c0c: ldr             x0, [x0, #0x738]
    // 0xaf4c10: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf4c10: stur            w0, [x1, #0x17]
    // 0xaf4c14: r0 = Instance_CrossAxisAlignment
    //     0xaf4c14: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf4c18: ldr             x0, [x0, #0x740]
    // 0xaf4c1c: StoreField: r1->field_1b = r0
    //     0xaf4c1c: stur            w0, [x1, #0x1b]
    // 0xaf4c20: r0 = Instance_VerticalDirection
    //     0xaf4c20: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf4c24: ldr             x0, [x0, #0x748]
    // 0xaf4c28: StoreField: r1->field_23 = r0
    //     0xaf4c28: stur            w0, [x1, #0x23]
    // 0xaf4c2c: r0 = Instance_Clip
    //     0xaf4c2c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf4c30: ldr             x0, [x0, #0x750]
    // 0xaf4c34: StoreField: r1->field_2b = r0
    //     0xaf4c34: stur            w0, [x1, #0x2b]
    // 0xaf4c38: StoreField: r1->field_2f = rZR
    //     0xaf4c38: stur            xzr, [x1, #0x2f]
    // 0xaf4c3c: ldur            x0, [fp, #-0x10]
    // 0xaf4c40: StoreField: r1->field_b = r0
    //     0xaf4c40: stur            w0, [x1, #0xb]
    // 0xaf4c44: ldur            x0, [fp, #-8]
    // 0xaf4c48: r2 = LoadInt32Instr(r0)
    //     0xaf4c48: sbfx            x2, x0, #1, #0x1f
    //     0xaf4c4c: tbz             w0, #0, #0xaf4c54
    //     0xaf4c50: ldur            x2, [x0, #7]
    // 0xaf4c54: stur            x2, [fp, #-0x28]
    // 0xaf4c58: r0 = DefaultTabController()
    //     0xaf4c58: bl              #0xaf4c88  ; AllocateDefaultTabControllerStub -> DefaultTabController (size=0x24)
    // 0xaf4c5c: ldur            x1, [fp, #-0x28]
    // 0xaf4c60: StoreField: r0->field_b = r1
    //     0xaf4c60: stur            x1, [x0, #0xb]
    // 0xaf4c64: StoreField: r0->field_13 = rZR
    //     0xaf4c64: stur            xzr, [x0, #0x13]
    // 0xaf4c68: ldur            x1, [fp, #-0x18]
    // 0xaf4c6c: StoreField: r0->field_1f = r1
    //     0xaf4c6c: stur            w1, [x0, #0x1f]
    // 0xaf4c70: LeaveFrame
    //     0xaf4c70: mov             SP, fp
    //     0xaf4c74: ldp             fp, lr, [SP], #0x10
    // 0xaf4c78: ret
    //     0xaf4c78: ret             
    // 0xaf4c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf4c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf4c80: b               #0xaf498c
    // 0xaf4c84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf4c84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Tab <anonymous closure>(dynamic, FaqCategory) {
    // ** addr: 0xaf4c94, size: 0x34
    // 0xaf4c94: EnterFrame
    //     0xaf4c94: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4c98: mov             fp, SP
    // 0xaf4c9c: AllocStack(0x8)
    //     0xaf4c9c: sub             SP, SP, #8
    // 0xaf4ca0: ldr             x0, [fp, #0x10]
    // 0xaf4ca4: LoadField: r1 = r0->field_f
    //     0xaf4ca4: ldur            w1, [x0, #0xf]
    // 0xaf4ca8: DecompressPointer r1
    //     0xaf4ca8: add             x1, x1, HEAP, lsl #32
    // 0xaf4cac: stur            x1, [fp, #-8]
    // 0xaf4cb0: r0 = Tab()
    //     0xaf4cb0: bl              #0xae37cc  ; AllocateTabStub -> Tab (size=0x20)
    // 0xaf4cb4: ldur            x1, [fp, #-8]
    // 0xaf4cb8: StoreField: r0->field_b = r1
    //     0xaf4cb8: stur            w1, [x0, #0xb]
    // 0xaf4cbc: LeaveFrame
    //     0xaf4cbc: mov             SP, fp
    //     0xaf4cc0: ldp             fp, lr, [SP], #0x10
    // 0xaf4cc4: ret
    //     0xaf4cc4: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xaf5448, size: 0x12c
    // 0xaf5448: EnterFrame
    //     0xaf5448: stp             fp, lr, [SP, #-0x10]!
    //     0xaf544c: mov             fp, SP
    // 0xaf5450: AllocStack(0x30)
    //     0xaf5450: sub             SP, SP, #0x30
    // 0xaf5454: SetupParameters(FaqView this /* r1 => r1, fp-0x8 */)
    //     0xaf5454: stur            x1, [fp, #-8]
    // 0xaf5458: CheckStackOverflow
    //     0xaf5458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf545c: cmp             SP, x16
    //     0xaf5460: b.ls            #0xaf556c
    // 0xaf5464: r0 = NSearchTextFieldDisabled()
    //     0xaf5464: bl              #0xaf5574  ; AllocateNSearchTextFieldDisabledStub -> NSearchTextFieldDisabled (size=0x14)
    // 0xaf5468: mov             x3, x0
    // 0xaf546c: r0 = "Cari Pusat Bantuan Kamu"
    //     0xaf546c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eff8] "Cari Pusat Bantuan Kamu"
    //     0xaf5470: ldr             x0, [x0, #0xff8]
    // 0xaf5474: stur            x3, [fp, #-0x10]
    // 0xaf5478: StoreField: r3->field_f = r0
    //     0xaf5478: stur            w0, [x3, #0xf]
    // 0xaf547c: r1 = Function '<anonymous closure>':.
    //     0xaf547c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f000] AnonymousClosure: (0xaf5580), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf5480: ldr             x1, [x1]
    // 0xaf5484: r2 = Null
    //     0xaf5484: mov             x2, NULL
    // 0xaf5488: r0 = AllocateClosure()
    //     0xaf5488: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf548c: mov             x1, x0
    // 0xaf5490: ldur            x0, [fp, #-0x10]
    // 0xaf5494: StoreField: r0->field_b = r1
    //     0xaf5494: stur            w1, [x0, #0xb]
    // 0xaf5498: r0 = PreferredSize()
    //     0xaf5498: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xaf549c: mov             x1, x0
    // 0xaf54a0: r0 = Instance_Size
    //     0xaf54a0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xaf54a4: ldr             x0, [x0, #0xc88]
    // 0xaf54a8: stur            x1, [fp, #-0x18]
    // 0xaf54ac: StoreField: r1->field_f = r0
    //     0xaf54ac: stur            w0, [x1, #0xf]
    // 0xaf54b0: ldur            x0, [fp, #-0x10]
    // 0xaf54b4: StoreField: r1->field_b = r0
    //     0xaf54b4: stur            w0, [x1, #0xb]
    // 0xaf54b8: r0 = AppBar()
    //     0xaf54b8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf54bc: stur            x0, [fp, #-0x10]
    // 0xaf54c0: r16 = Instance_Text
    //     0xaf54c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f008] Obj!Text@e21dc1
    //     0xaf54c4: ldr             x16, [x16, #8]
    // 0xaf54c8: ldur            lr, [fp, #-0x18]
    // 0xaf54cc: stp             lr, x16, [SP]
    // 0xaf54d0: mov             x1, x0
    // 0xaf54d4: r4 = const [0, 0x3, 0x2, 0x1, bottom, 0x2, title, 0x1, null]
    //     0xaf54d4: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2def0] List(9) [0, 0x3, 0x2, 0x1, "bottom", 0x2, "title", 0x1, Null]
    //     0xaf54d8: ldr             x4, [x4, #0xef0]
    // 0xaf54dc: r0 = AppBar()
    //     0xaf54dc: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf54e0: ldur            x1, [fp, #-8]
    // 0xaf54e4: r0 = controller()
    //     0xaf54e4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf54e8: r1 = Function '<anonymous closure>':.
    //     0xaf54e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f010] AnonymousClosure: (0xaf4974), in [package:nuonline/app/modules/faq/views/faq_view.dart] FaqView::build (0xaf5448)
    //     0xaf54ec: ldr             x1, [x1, #0x10]
    // 0xaf54f0: r2 = Null
    //     0xaf54f0: mov             x2, NULL
    // 0xaf54f4: stur            x0, [fp, #-8]
    // 0xaf54f8: r0 = AllocateClosure()
    //     0xaf54f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf54fc: r16 = <List<FaqCategory>>
    //     0xaf54fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f018] TypeArguments: <List<FaqCategory>>
    //     0xaf5500: ldr             x16, [x16, #0x18]
    // 0xaf5504: ldur            lr, [fp, #-8]
    // 0xaf5508: stp             lr, x16, [SP, #8]
    // 0xaf550c: str             x0, [SP]
    // 0xaf5510: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf5510: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf5514: r0 = StateMixinExt.when()
    //     0xaf5514: bl              #0xae2d2c  ; [package:nuonline/common/extensions/state_extension.dart] ::StateMixinExt.when
    // 0xaf5518: stur            x0, [fp, #-8]
    // 0xaf551c: r0 = Scaffold()
    //     0xaf551c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf5520: ldur            x1, [fp, #-0x10]
    // 0xaf5524: StoreField: r0->field_13 = r1
    //     0xaf5524: stur            w1, [x0, #0x13]
    // 0xaf5528: ldur            x1, [fp, #-8]
    // 0xaf552c: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf552c: stur            w1, [x0, #0x17]
    // 0xaf5530: r1 = Instance_AlignmentDirectional
    //     0xaf5530: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf5534: ldr             x1, [x1, #0x758]
    // 0xaf5538: StoreField: r0->field_2b = r1
    //     0xaf5538: stur            w1, [x0, #0x2b]
    // 0xaf553c: r1 = true
    //     0xaf553c: add             x1, NULL, #0x20  ; true
    // 0xaf5540: StoreField: r0->field_53 = r1
    //     0xaf5540: stur            w1, [x0, #0x53]
    // 0xaf5544: r2 = Instance_DragStartBehavior
    //     0xaf5544: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf5548: StoreField: r0->field_57 = r2
    //     0xaf5548: stur            w2, [x0, #0x57]
    // 0xaf554c: r2 = false
    //     0xaf554c: add             x2, NULL, #0x30  ; false
    // 0xaf5550: StoreField: r0->field_b = r2
    //     0xaf5550: stur            w2, [x0, #0xb]
    // 0xaf5554: StoreField: r0->field_f = r2
    //     0xaf5554: stur            w2, [x0, #0xf]
    // 0xaf5558: StoreField: r0->field_5f = r1
    //     0xaf5558: stur            w1, [x0, #0x5f]
    // 0xaf555c: StoreField: r0->field_63 = r1
    //     0xaf555c: stur            w1, [x0, #0x63]
    // 0xaf5560: LeaveFrame
    //     0xaf5560: mov             SP, fp
    //     0xaf5564: ldp             fp, lr, [SP], #0x10
    // 0xaf5568: ret
    //     0xaf5568: ret             
    // 0xaf556c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf556c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5570: b               #0xaf5464
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5580, size: 0x5c
    // 0xaf5580: EnterFrame
    //     0xaf5580: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5584: mov             fp, SP
    // 0xaf5588: AllocStack(0x10)
    //     0xaf5588: sub             SP, SP, #0x10
    // 0xaf558c: CheckStackOverflow
    //     0xaf558c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5590: cmp             SP, x16
    //     0xaf5594: b.ls            #0xaf55d4
    // 0xaf5598: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf5598: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf559c: ldr             x0, [x0, #0x2670]
    //     0xaf55a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf55a4: cmp             w0, w16
    //     0xaf55a8: b.ne            #0xaf55b4
    //     0xaf55ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf55b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf55b4: r16 = "/faq/faq-search"
    //     0xaf55b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f078] "/faq/faq-search"
    //     0xaf55b8: ldr             x16, [x16, #0x78]
    // 0xaf55bc: stp             x16, NULL, [SP]
    // 0xaf55c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf55c0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf55c4: r0 = GetNavigation.toNamed()
    //     0xaf55c4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf55c8: LeaveFrame
    //     0xaf55c8: mov             SP, fp
    //     0xaf55cc: ldp             fp, lr, [SP], #0x10
    // 0xaf55d0: ret
    //     0xaf55d0: ret             
    // 0xaf55d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf55d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf55d8: b               #0xaf5598
  }
}
