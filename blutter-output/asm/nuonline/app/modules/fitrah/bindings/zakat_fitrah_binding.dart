// lib: , url: package:nuonline/app/modules/fitrah/bindings/zakat_fitrah_binding.dart

// class id: 1050278, size: 0x8
class :: {
}

// class id: 2165, size: 0x8, field offset: 0x8
class ZakatFitrahBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x8132f0, size: 0x70
    // 0x8132f0: EnterFrame
    //     0x8132f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8132f4: mov             fp, SP
    // 0x8132f8: AllocStack(0x10)
    //     0x8132f8: sub             SP, SP, #0x10
    // 0x8132fc: CheckStackOverflow
    //     0x8132fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813300: cmp             SP, x16
    //     0x813304: b.ls            #0x813358
    // 0x813308: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x813308: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81330c: ldr             x0, [x0, #0x2670]
    //     0x813310: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x813314: cmp             w0, w16
    //     0x813318: b.ne            #0x813324
    //     0x81331c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x813320: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x813324: r1 = Function '<anonymous closure>':.
    //     0x813324: add             x1, PP, #0x34, lsl #12  ; [pp+0x34840] AnonymousClosure: (0x813360), in [package:nuonline/app/modules/fitrah/bindings/zakat_fitrah_binding.dart] ZakatFitrahBinding::dependencies (0x8132f0)
    //     0x813328: ldr             x1, [x1, #0x840]
    // 0x81332c: r2 = Null
    //     0x81332c: mov             x2, NULL
    // 0x813330: r0 = AllocateClosure()
    //     0x813330: bl              #0xec1630  ; AllocateClosureStub
    // 0x813334: r16 = <ZakatFitrahController>
    //     0x813334: add             x16, PP, #0x24, lsl #12  ; [pp+0x24858] TypeArguments: <ZakatFitrahController>
    //     0x813338: ldr             x16, [x16, #0x858]
    // 0x81333c: stp             x0, x16, [SP]
    // 0x813340: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x813340: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x813344: r0 = Inst.lazyPut()
    //     0x813344: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x813348: r0 = Null
    //     0x813348: mov             x0, NULL
    // 0x81334c: LeaveFrame
    //     0x81334c: mov             SP, fp
    //     0x813350: ldp             fp, lr, [SP], #0x10
    // 0x813354: ret
    //     0x813354: ret             
    // 0x813358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x813358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81335c: b               #0x813308
  }
  [closure] ZakatFitrahController <anonymous closure>(dynamic) {
    // ** addr: 0x813360, size: 0x64
    // 0x813360: EnterFrame
    //     0x813360: stp             fp, lr, [SP, #-0x10]!
    //     0x813364: mov             fp, SP
    // 0x813368: AllocStack(0x10)
    //     0x813368: sub             SP, SP, #0x10
    // 0x81336c: CheckStackOverflow
    //     0x81336c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813370: cmp             SP, x16
    //     0x813374: b.ls            #0x8133bc
    // 0x813378: r0 = find()
    //     0x813378: bl              #0x813400  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::find
    // 0x81337c: r1 = <ZakatSetting>
    //     0x81337c: add             x1, PP, #0x34, lsl #12  ; [pp+0x347a0] TypeArguments: <ZakatSetting>
    //     0x813380: ldr             x1, [x1, #0x7a0]
    // 0x813384: stur            x0, [fp, #-8]
    // 0x813388: r0 = ZakatFitrahController()
    //     0x813388: bl              #0x8133f4  ; AllocateZakatFitrahControllerStub -> ZakatFitrahController (size=0x38)
    // 0x81338c: mov             x2, x0
    // 0x813390: r0 = Sentinel
    //     0x813390: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x813394: stur            x2, [fp, #-0x10]
    // 0x813398: StoreField: r2->field_33 = r0
    //     0x813398: stur            w0, [x2, #0x33]
    // 0x81339c: ldur            x0, [fp, #-8]
    // 0x8133a0: StoreField: r2->field_2f = r0
    //     0x8133a0: stur            w0, [x2, #0x2f]
    // 0x8133a4: mov             x1, x2
    // 0x8133a8: r0 = GetxController()
    //     0x8133a8: bl              #0x8133c4  ; [package:get/get_state_manager/src/simple/get_controllers.dart] GetxController::GetxController
    // 0x8133ac: ldur            x0, [fp, #-0x10]
    // 0x8133b0: LeaveFrame
    //     0x8133b0: mov             SP, fp
    //     0x8133b4: ldp             fp, lr, [SP], #0x10
    // 0x8133b8: ret
    //     0x8133b8: ret             
    // 0x8133bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8133bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8133c0: b               #0x813378
  }
}
