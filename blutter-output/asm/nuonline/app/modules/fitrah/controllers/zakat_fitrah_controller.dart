// lib: , url: package:nuonline/app/modules/fitrah/controllers/zakat_fitrah_controller.dart

// class id: 1050279, size: 0x8
class :: {
}

// class id: 1930, size: 0x30, field offset: 0x2c
//   transformed mixin,
abstract class _ZakatFitrahController&SimpleFetchController&GetSingleTickerProviderStateMixin extends SimpleFetchController<dynamic>
     with GetSingleTickerProviderStateMixin {

  _ createTicker(/* No info */) {
    // ** addr: 0x7607a4, size: 0x60
    // 0x7607a4: EnterFrame
    //     0x7607a4: stp             fp, lr, [SP, #-0x10]!
    //     0x7607a8: mov             fp, SP
    // 0x7607ac: AllocStack(0x10)
    //     0x7607ac: sub             SP, SP, #0x10
    // 0x7607b0: SetupParameters(_ZakatFitrahController&SimpleFetchController&GetSingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7607b0: stur            x1, [fp, #-8]
    //     0x7607b4: stur            x2, [fp, #-0x10]
    // 0x7607b8: r0 = Ticker()
    //     0x7607b8: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x7607bc: mov             x2, x0
    // 0x7607c0: r1 = false
    //     0x7607c0: add             x1, NULL, #0x30  ; false
    // 0x7607c4: StoreField: r2->field_b = r1
    //     0x7607c4: stur            w1, [x2, #0xb]
    // 0x7607c8: ldur            x1, [fp, #-0x10]
    // 0x7607cc: StoreField: r2->field_13 = r1
    //     0x7607cc: stur            w1, [x2, #0x13]
    // 0x7607d0: mov             x0, x2
    // 0x7607d4: ldur            x1, [fp, #-8]
    // 0x7607d8: StoreField: r1->field_2b = r0
    //     0x7607d8: stur            w0, [x1, #0x2b]
    //     0x7607dc: ldurb           w16, [x1, #-1]
    //     0x7607e0: ldurb           w17, [x0, #-1]
    //     0x7607e4: and             x16, x17, x16, lsr #2
    //     0x7607e8: tst             x16, HEAP, lsr #32
    //     0x7607ec: b.eq            #0x7607f4
    //     0x7607f0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7607f4: mov             x0, x2
    // 0x7607f8: LeaveFrame
    //     0x7607f8: mov             SP, fp
    //     0x7607fc: ldp             fp, lr, [SP], #0x10
    // 0x760800: ret
    //     0x760800: ret             
  }
}

// class id: 1931, size: 0x38, field offset: 0x30
class ZakatFitrahController extends _ZakatFitrahController&SimpleFetchController&GetSingleTickerProviderStateMixin {

  late final TabController tabController; // offset: 0x34

  _ onFetchRequested(/* No info */) {
    // ** addr: 0x7e91ec, size: 0x38
    // 0x7e91ec: EnterFrame
    //     0x7e91ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7e91f0: mov             fp, SP
    // 0x7e91f4: CheckStackOverflow
    //     0x7e91f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e91f8: cmp             SP, x16
    //     0x7e91fc: b.ls            #0x7e921c
    // 0x7e9200: LoadField: r0 = r1->field_2f
    //     0x7e9200: ldur            w0, [x1, #0x2f]
    // 0x7e9204: DecompressPointer r0
    //     0x7e9204: add             x0, x0, HEAP, lsl #32
    // 0x7e9208: mov             x1, x0
    // 0x7e920c: r0 = findSetting()
    //     0x7e920c: bl              #0x7e9244  ; [package:nuonline/app/data/repositories/zakat_repository.dart] ZakatRepository::findSetting
    // 0x7e9210: LeaveFrame
    //     0x7e9210: mov             SP, fp
    //     0x7e9214: ldp             fp, lr, [SP], #0x10
    // 0x7e9218: ret
    //     0x7e9218: ret             
    // 0x7e921c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e921c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9220: b               #0x7e9200
  }
  TabController tabController(ZakatFitrahController) {
    // ** addr: 0x8c0444, size: 0x48
    // 0x8c0444: EnterFrame
    //     0x8c0444: stp             fp, lr, [SP, #-0x10]!
    //     0x8c0448: mov             fp, SP
    // 0x8c044c: AllocStack(0x8)
    //     0x8c044c: sub             SP, SP, #8
    // 0x8c0450: CheckStackOverflow
    //     0x8c0450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0454: cmp             SP, x16
    //     0x8c0458: b.ls            #0x8c0484
    // 0x8c045c: r0 = TabController()
    //     0x8c045c: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8c0460: mov             x1, x0
    // 0x8c0464: ldr             x3, [fp, #0x10]
    // 0x8c0468: r2 = 2
    //     0x8c0468: movz            x2, #0x2
    // 0x8c046c: stur            x0, [fp, #-8]
    // 0x8c0470: r0 = TabController()
    //     0x8c0470: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8c0474: ldur            x0, [fp, #-8]
    // 0x8c0478: LeaveFrame
    //     0x8c0478: mov             SP, fp
    //     0x8c047c: ldp             fp, lr, [SP], #0x10
    // 0x8c0480: ret
    //     0x8c0480: ret             
    // 0x8c0484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0488: b               #0x8c045c
  }
  _ onClose(/* No info */) {
    // ** addr: 0x92793c, size: 0x54
    // 0x92793c: EnterFrame
    //     0x92793c: stp             fp, lr, [SP, #-0x10]!
    //     0x927940: mov             fp, SP
    // 0x927944: CheckStackOverflow
    //     0x927944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927948: cmp             SP, x16
    //     0x92794c: b.ls            #0x927988
    // 0x927950: LoadField: r0 = r1->field_33
    //     0x927950: ldur            w0, [x1, #0x33]
    // 0x927954: DecompressPointer r0
    //     0x927954: add             x0, x0, HEAP, lsl #32
    // 0x927958: r16 = Sentinel
    //     0x927958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92795c: cmp             w0, w16
    // 0x927960: b.ne            #0x927970
    // 0x927964: r2 = tabController
    //     0x927964: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2efe0] Field <ZakatFitrahController.tabController>: late final (offset: 0x34)
    //     0x927968: ldr             x2, [x2, #0xfe0]
    // 0x92796c: r0 = InitLateFinalInstanceField()
    //     0x92796c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x927970: mov             x1, x0
    // 0x927974: r0 = dispose()
    //     0x927974: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x927978: r0 = Null
    //     0x927978: mov             x0, NULL
    // 0x92797c: LeaveFrame
    //     0x92797c: mov             SP, fp
    //     0x927980: ldp             fp, lr, [SP], #0x10
    // 0x927984: ret
    //     0x927984: ret             
    // 0x927988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92798c: b               #0x927950
  }
}
