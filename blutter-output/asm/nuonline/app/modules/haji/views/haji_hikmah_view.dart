// lib: , url: package:nuonline/app/modules/haji/views/haji_hikmah_view.dart

// class id: 1050299, size: 0x8
class :: {
}

// class id: 5270, size: 0x14, field offset: 0x14
//   const constructor, 
class HajiHikmahView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf749c, size: 0x350
    // 0xaf749c: EnterFrame
    //     0xaf749c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf74a0: mov             fp, SP
    // 0xaf74a4: AllocStack(0x68)
    //     0xaf74a4: sub             SP, SP, #0x68
    // 0xaf74a8: SetupParameters(HajiHikmahView this /* r1 => r1, fp-0x8 */)
    //     0xaf74a8: stur            x1, [fp, #-8]
    // 0xaf74ac: CheckStackOverflow
    //     0xaf74ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf74b0: cmp             SP, x16
    //     0xaf74b4: b.ls            #0xaf77e4
    // 0xaf74b8: r1 = 1
    //     0xaf74b8: movz            x1, #0x1
    // 0xaf74bc: r0 = AllocateContext()
    //     0xaf74bc: bl              #0xec126c  ; AllocateContextStub
    // 0xaf74c0: mov             x2, x0
    // 0xaf74c4: ldur            x0, [fp, #-8]
    // 0xaf74c8: stur            x2, [fp, #-0x10]
    // 0xaf74cc: StoreField: r2->field_f = r0
    //     0xaf74cc: stur            w0, [x2, #0xf]
    // 0xaf74d0: mov             x1, x0
    // 0xaf74d4: r0 = controller()
    //     0xaf74d4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf74d8: LoadField: r1 = r0->field_33
    //     0xaf74d8: ldur            w1, [x0, #0x33]
    // 0xaf74dc: DecompressPointer r1
    //     0xaf74dc: add             x1, x1, HEAP, lsl #32
    // 0xaf74e0: stur            x1, [fp, #-0x18]
    // 0xaf74e4: r0 = Text()
    //     0xaf74e4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaf74e8: mov             x1, x0
    // 0xaf74ec: ldur            x0, [fp, #-0x18]
    // 0xaf74f0: stur            x1, [fp, #-0x20]
    // 0xaf74f4: StoreField: r1->field_b = r0
    //     0xaf74f4: stur            w0, [x1, #0xb]
    // 0xaf74f8: r0 = AppBar()
    //     0xaf74f8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf74fc: stur            x0, [fp, #-0x18]
    // 0xaf7500: ldur            x16, [fp, #-0x20]
    // 0xaf7504: str             x16, [SP]
    // 0xaf7508: mov             x1, x0
    // 0xaf750c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaf750c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaf7510: ldr             x4, [x4, #0x6e8]
    // 0xaf7514: r0 = AppBar()
    //     0xaf7514: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf7518: ldur            x1, [fp, #-8]
    // 0xaf751c: r0 = controller()
    //     0xaf751c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf7520: stur            x0, [fp, #-0x20]
    // 0xaf7524: r0 = Obx()
    //     0xaf7524: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf7528: ldur            x2, [fp, #-0x10]
    // 0xaf752c: r1 = Function '<anonymous closure>':.
    //     0xaf752c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea28] AnonymousClosure: (0xaf8210), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf7530: ldr             x1, [x1, #0xa28]
    // 0xaf7534: stur            x0, [fp, #-0x28]
    // 0xaf7538: r0 = AllocateClosure()
    //     0xaf7538: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf753c: mov             x1, x0
    // 0xaf7540: ldur            x0, [fp, #-0x28]
    // 0xaf7544: StoreField: r0->field_b = r1
    //     0xaf7544: stur            w1, [x0, #0xb]
    // 0xaf7548: r0 = Padding()
    //     0xaf7548: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf754c: mov             x2, x0
    // 0xaf7550: r0 = Instance_EdgeInsets
    //     0xaf7550: add             x0, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xaf7554: ldr             x0, [x0, #0xde8]
    // 0xaf7558: stur            x2, [fp, #-0x30]
    // 0xaf755c: StoreField: r2->field_f = r0
    //     0xaf755c: stur            w0, [x2, #0xf]
    // 0xaf7560: ldur            x0, [fp, #-0x28]
    // 0xaf7564: StoreField: r2->field_b = r0
    //     0xaf7564: stur            w0, [x2, #0xb]
    // 0xaf7568: ldur            x1, [fp, #-8]
    // 0xaf756c: r0 = controller()
    //     0xaf756c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf7570: r1 = Function '<anonymous closure>':.
    //     0xaf7570: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea30] AnonymousClosure: (0xb98358), in [package:nuonline/app/modules/tutorial/tutorial_bookmark/views/tutorial_bookmark_view.dart] TutorialBookmarkView::build (0xb593b4)
    //     0xaf7574: ldr             x1, [x1, #0xa30]
    // 0xaf7578: r2 = Null
    //     0xaf7578: mov             x2, NULL
    // 0xaf757c: stur            x0, [fp, #-8]
    // 0xaf7580: r0 = AllocateClosure()
    //     0xaf7580: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7584: r1 = Function '<anonymous closure>':.
    //     0xaf7584: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea38] AnonymousClosure: (0xad792c), in [package:nuonline/app/modules/zakat/views/zakat_view.dart] ZakatView::build (0xb6ae74)
    //     0xaf7588: ldr             x1, [x1, #0xa38]
    // 0xaf758c: r2 = Null
    //     0xaf758c: mov             x2, NULL
    // 0xaf7590: stur            x0, [fp, #-0x28]
    // 0xaf7594: r0 = AllocateClosure()
    //     0xaf7594: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7598: stur            x0, [fp, #-0x38]
    // 0xaf759c: r0 = ListView()
    //     0xaf759c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf75a0: stur            x0, [fp, #-0x40]
    // 0xaf75a4: r16 = Instance_EdgeInsets
    //     0xaf75a4: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xaf75a8: str             x16, [SP]
    // 0xaf75ac: mov             x1, x0
    // 0xaf75b0: ldur            x2, [fp, #-0x28]
    // 0xaf75b4: ldur            x5, [fp, #-0x38]
    // 0xaf75b8: r3 = 8
    //     0xaf75b8: movz            x3, #0x8
    // 0xaf75bc: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xaf75bc: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xaf75c0: ldr             x4, [x4, #0x700]
    // 0xaf75c4: r0 = ListView.separated()
    //     0xaf75c4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf75c8: ldur            x2, [fp, #-0x10]
    // 0xaf75cc: r1 = Function '<anonymous closure>':.
    //     0xaf75cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea40] AnonymousClosure: (0xaf7938), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf75d0: ldr             x1, [x1, #0xa40]
    // 0xaf75d4: r0 = AllocateClosure()
    //     0xaf75d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf75d8: ldur            x2, [fp, #-0x10]
    // 0xaf75dc: r1 = Function '<anonymous closure>':.
    //     0xaf75dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea48] AnonymousClosure: (0xaf7864), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf75e0: ldr             x1, [x1, #0xa48]
    // 0xaf75e4: stur            x0, [fp, #-0x10]
    // 0xaf75e8: r0 = AllocateClosure()
    //     0xaf75e8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf75ec: r16 = <List<Hikmah>>
    //     0xaf75ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea50] TypeArguments: <List<Hikmah>>
    //     0xaf75f0: ldr             x16, [x16, #0xa50]
    // 0xaf75f4: ldur            lr, [fp, #-8]
    // 0xaf75f8: stp             lr, x16, [SP, #0x18]
    // 0xaf75fc: ldur            x16, [fp, #-0x10]
    // 0xaf7600: ldur            lr, [fp, #-0x40]
    // 0xaf7604: stp             lr, x16, [SP, #8]
    // 0xaf7608: str             x0, [SP]
    // 0xaf760c: r4 = const [0x1, 0x4, 0x4, 0x2, onError, 0x3, onLoading, 0x2, null]
    //     0xaf760c: add             x4, PP, #0x29, lsl #12  ; [pp+0x29728] List(9) [0x1, 0x4, 0x4, 0x2, "onError", 0x3, "onLoading", 0x2, Null]
    //     0xaf7610: ldr             x4, [x4, #0x728]
    // 0xaf7614: r0 = StateExt.obx()
    //     0xaf7614: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaf7618: stur            x0, [fp, #-8]
    // 0xaf761c: r0 = ListTileTheme()
    //     0xaf761c: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xaf7620: mov             x2, x0
    // 0xaf7624: r0 = Instance_EdgeInsets
    //     0xaf7624: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2abd8] Obj!EdgeInsets@e126d1
    //     0xaf7628: ldr             x0, [x0, #0xbd8]
    // 0xaf762c: stur            x2, [fp, #-0x10]
    // 0xaf7630: StoreField: r2->field_2b = r0
    //     0xaf7630: stur            w0, [x2, #0x2b]
    // 0xaf7634: r0 = 16.000000
    //     0xaf7634: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xaf7638: ldr             x0, [x0, #0x80]
    // 0xaf763c: StoreField: r2->field_37 = r0
    //     0xaf763c: stur            w0, [x2, #0x37]
    // 0xaf7640: r0 = 24.000000
    //     0xaf7640: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xaf7644: ldr             x0, [x0, #0x368]
    // 0xaf7648: StoreField: r2->field_3f = r0
    //     0xaf7648: stur            w0, [x2, #0x3f]
    // 0xaf764c: ldur            x0, [fp, #-8]
    // 0xaf7650: StoreField: r2->field_b = r0
    //     0xaf7650: stur            w0, [x2, #0xb]
    // 0xaf7654: r1 = <FlexParentData>
    //     0xaf7654: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xaf7658: ldr             x1, [x1, #0x720]
    // 0xaf765c: r0 = Expanded()
    //     0xaf765c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf7660: mov             x3, x0
    // 0xaf7664: r0 = 1
    //     0xaf7664: movz            x0, #0x1
    // 0xaf7668: stur            x3, [fp, #-8]
    // 0xaf766c: StoreField: r3->field_13 = r0
    //     0xaf766c: stur            x0, [x3, #0x13]
    // 0xaf7670: r0 = Instance_FlexFit
    //     0xaf7670: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xaf7674: ldr             x0, [x0, #0x728]
    // 0xaf7678: StoreField: r3->field_1b = r0
    //     0xaf7678: stur            w0, [x3, #0x1b]
    // 0xaf767c: ldur            x0, [fp, #-0x10]
    // 0xaf7680: StoreField: r3->field_b = r0
    //     0xaf7680: stur            w0, [x3, #0xb]
    // 0xaf7684: r1 = Null
    //     0xaf7684: mov             x1, NULL
    // 0xaf7688: r2 = 4
    //     0xaf7688: movz            x2, #0x4
    // 0xaf768c: r0 = AllocateArray()
    //     0xaf768c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf7690: mov             x2, x0
    // 0xaf7694: ldur            x0, [fp, #-0x30]
    // 0xaf7698: stur            x2, [fp, #-0x10]
    // 0xaf769c: StoreField: r2->field_f = r0
    //     0xaf769c: stur            w0, [x2, #0xf]
    // 0xaf76a0: ldur            x0, [fp, #-8]
    // 0xaf76a4: StoreField: r2->field_13 = r0
    //     0xaf76a4: stur            w0, [x2, #0x13]
    // 0xaf76a8: r1 = <Widget>
    //     0xaf76a8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf76ac: r0 = AllocateGrowableArray()
    //     0xaf76ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf76b0: mov             x1, x0
    // 0xaf76b4: ldur            x0, [fp, #-0x10]
    // 0xaf76b8: stur            x1, [fp, #-8]
    // 0xaf76bc: StoreField: r1->field_f = r0
    //     0xaf76bc: stur            w0, [x1, #0xf]
    // 0xaf76c0: r0 = 4
    //     0xaf76c0: movz            x0, #0x4
    // 0xaf76c4: StoreField: r1->field_b = r0
    //     0xaf76c4: stur            w0, [x1, #0xb]
    // 0xaf76c8: r0 = Column()
    //     0xaf76c8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf76cc: mov             x1, x0
    // 0xaf76d0: r0 = Instance_Axis
    //     0xaf76d0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaf76d4: stur            x1, [fp, #-0x10]
    // 0xaf76d8: StoreField: r1->field_f = r0
    //     0xaf76d8: stur            w0, [x1, #0xf]
    // 0xaf76dc: r0 = Instance_MainAxisAlignment
    //     0xaf76dc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xaf76e0: ldr             x0, [x0, #0x730]
    // 0xaf76e4: StoreField: r1->field_13 = r0
    //     0xaf76e4: stur            w0, [x1, #0x13]
    // 0xaf76e8: r0 = Instance_MainAxisSize
    //     0xaf76e8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xaf76ec: ldr             x0, [x0, #0x738]
    // 0xaf76f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf76f0: stur            w0, [x1, #0x17]
    // 0xaf76f4: r0 = Instance_CrossAxisAlignment
    //     0xaf76f4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xaf76f8: ldr             x0, [x0, #0x740]
    // 0xaf76fc: StoreField: r1->field_1b = r0
    //     0xaf76fc: stur            w0, [x1, #0x1b]
    // 0xaf7700: r0 = Instance_VerticalDirection
    //     0xaf7700: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xaf7704: ldr             x0, [x0, #0x748]
    // 0xaf7708: StoreField: r1->field_23 = r0
    //     0xaf7708: stur            w0, [x1, #0x23]
    // 0xaf770c: r0 = Instance_Clip
    //     0xaf770c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaf7710: ldr             x0, [x0, #0x750]
    // 0xaf7714: StoreField: r1->field_2b = r0
    //     0xaf7714: stur            w0, [x1, #0x2b]
    // 0xaf7718: StoreField: r1->field_2f = rZR
    //     0xaf7718: stur            xzr, [x1, #0x2f]
    // 0xaf771c: ldur            x0, [fp, #-8]
    // 0xaf7720: StoreField: r1->field_b = r0
    //     0xaf7720: stur            w0, [x1, #0xb]
    // 0xaf7724: r0 = RefreshIndicator()
    //     0xaf7724: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xaf7728: mov             x3, x0
    // 0xaf772c: ldur            x0, [fp, #-0x10]
    // 0xaf7730: stur            x3, [fp, #-8]
    // 0xaf7734: StoreField: r3->field_b = r0
    //     0xaf7734: stur            w0, [x3, #0xb]
    // 0xaf7738: d0 = 40.000000
    //     0xaf7738: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xaf773c: StoreField: r3->field_f = d0
    //     0xaf773c: stur            d0, [x3, #0xf]
    // 0xaf7740: ArrayStore: r3[0] = rZR  ; List_8
    //     0xaf7740: stur            xzr, [x3, #0x17]
    // 0xaf7744: ldur            x2, [fp, #-0x20]
    // 0xaf7748: r1 = Function 'onRefresh':.
    //     0xaf7748: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea58] AnonymousClosure: (0xaf77ec), in [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] HajiHikmahController::onRefresh (0xaf7824)
    //     0xaf774c: ldr             x1, [x1, #0xa58]
    // 0xaf7750: r0 = AllocateClosure()
    //     0xaf7750: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7754: mov             x1, x0
    // 0xaf7758: ldur            x0, [fp, #-8]
    // 0xaf775c: StoreField: r0->field_1f = r1
    //     0xaf775c: stur            w1, [x0, #0x1f]
    // 0xaf7760: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xaf7760: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xaf7764: ldr             x1, [x1, #0xf58]
    // 0xaf7768: StoreField: r0->field_2f = r1
    //     0xaf7768: stur            w1, [x0, #0x2f]
    // 0xaf776c: d0 = 2.500000
    //     0xaf776c: fmov            d0, #2.50000000
    // 0xaf7770: StoreField: r0->field_3b = d0
    //     0xaf7770: stur            d0, [x0, #0x3b]
    // 0xaf7774: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xaf7774: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xaf7778: ldr             x1, [x1, #0xa68]
    // 0xaf777c: StoreField: r0->field_47 = r1
    //     0xaf777c: stur            w1, [x0, #0x47]
    // 0xaf7780: d0 = 2.000000
    //     0xaf7780: fmov            d0, #2.00000000
    // 0xaf7784: StoreField: r0->field_4b = d0
    //     0xaf7784: stur            d0, [x0, #0x4b]
    // 0xaf7788: r1 = Instance__IndicatorType
    //     0xaf7788: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xaf778c: ldr             x1, [x1, #0xa70]
    // 0xaf7790: StoreField: r0->field_43 = r1
    //     0xaf7790: stur            w1, [x0, #0x43]
    // 0xaf7794: r0 = Scaffold()
    //     0xaf7794: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaf7798: ldur            x1, [fp, #-0x18]
    // 0xaf779c: StoreField: r0->field_13 = r1
    //     0xaf779c: stur            w1, [x0, #0x13]
    // 0xaf77a0: ldur            x1, [fp, #-8]
    // 0xaf77a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf77a4: stur            w1, [x0, #0x17]
    // 0xaf77a8: r1 = Instance_AlignmentDirectional
    //     0xaf77a8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xaf77ac: ldr             x1, [x1, #0x758]
    // 0xaf77b0: StoreField: r0->field_2b = r1
    //     0xaf77b0: stur            w1, [x0, #0x2b]
    // 0xaf77b4: r1 = true
    //     0xaf77b4: add             x1, NULL, #0x20  ; true
    // 0xaf77b8: StoreField: r0->field_53 = r1
    //     0xaf77b8: stur            w1, [x0, #0x53]
    // 0xaf77bc: r2 = Instance_DragStartBehavior
    //     0xaf77bc: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xaf77c0: StoreField: r0->field_57 = r2
    //     0xaf77c0: stur            w2, [x0, #0x57]
    // 0xaf77c4: r2 = false
    //     0xaf77c4: add             x2, NULL, #0x30  ; false
    // 0xaf77c8: StoreField: r0->field_b = r2
    //     0xaf77c8: stur            w2, [x0, #0xb]
    // 0xaf77cc: StoreField: r0->field_f = r2
    //     0xaf77cc: stur            w2, [x0, #0xf]
    // 0xaf77d0: StoreField: r0->field_5f = r1
    //     0xaf77d0: stur            w1, [x0, #0x5f]
    // 0xaf77d4: StoreField: r0->field_63 = r1
    //     0xaf77d4: stur            w1, [x0, #0x63]
    // 0xaf77d8: LeaveFrame
    //     0xaf77d8: mov             SP, fp
    //     0xaf77dc: ldp             fp, lr, [SP], #0x10
    // 0xaf77e0: ret
    //     0xaf77e0: ret             
    // 0xaf77e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf77e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf77e8: b               #0xaf74b8
  }
  [closure] NEmptyState <anonymous closure>(dynamic, String?) {
    // ** addr: 0xaf7864, size: 0xd4
    // 0xaf7864: EnterFrame
    //     0xaf7864: stp             fp, lr, [SP, #-0x10]!
    //     0xaf7868: mov             fp, SP
    // 0xaf786c: AllocStack(0x18)
    //     0xaf786c: sub             SP, SP, #0x18
    // 0xaf7870: SetupParameters()
    //     0xaf7870: ldr             x0, [fp, #0x18]
    //     0xaf7874: ldur            w1, [x0, #0x17]
    //     0xaf7878: add             x1, x1, HEAP, lsl #32
    //     0xaf787c: stur            x1, [fp, #-8]
    // 0xaf7880: CheckStackOverflow
    //     0xaf7880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf7884: cmp             SP, x16
    //     0xaf7888: b.ls            #0xaf7930
    // 0xaf788c: r16 = "ApiError.notFound"
    //     0xaf788c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29730] "ApiError.notFound"
    //     0xaf7890: ldr             x16, [x16, #0x730]
    // 0xaf7894: ldr             lr, [fp, #0x10]
    // 0xaf7898: stp             lr, x16, [SP]
    // 0xaf789c: r0 = ==()
    //     0xaf789c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xaf78a0: tbz             w0, #4, #0xaf78f4
    // 0xaf78a4: r16 = "ApiError.noConnection"
    //     0xaf78a4: add             x16, PP, #0x29, lsl #12  ; [pp+0x29738] "ApiError.noConnection"
    //     0xaf78a8: ldr             x16, [x16, #0x738]
    // 0xaf78ac: ldr             lr, [fp, #0x10]
    // 0xaf78b0: stp             lr, x16, [SP]
    // 0xaf78b4: r0 = ==()
    //     0xaf78b4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xaf78b8: tbnz            w0, #4, #0xaf78f4
    // 0xaf78bc: ldur            x0, [fp, #-8]
    // 0xaf78c0: LoadField: r1 = r0->field_f
    //     0xaf78c0: ldur            w1, [x0, #0xf]
    // 0xaf78c4: DecompressPointer r1
    //     0xaf78c4: add             x1, x1, HEAP, lsl #32
    // 0xaf78c8: r0 = controller()
    //     0xaf78c8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf78cc: mov             x2, x0
    // 0xaf78d0: r1 = Function 'onRefresh':.
    //     0xaf78d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea58] AnonymousClosure: (0xaf77ec), in [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] HajiHikmahController::onRefresh (0xaf7824)
    //     0xaf78d4: ldr             x1, [x1, #0xa58]
    // 0xaf78d8: r0 = AllocateClosure()
    //     0xaf78d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf78dc: mov             x2, x0
    // 0xaf78e0: r1 = Null
    //     0xaf78e0: mov             x1, NULL
    // 0xaf78e4: r0 = NEmptyState.notConnection()
    //     0xaf78e4: bl              #0xad9f3c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.notConnection
    // 0xaf78e8: LeaveFrame
    //     0xaf78e8: mov             SP, fp
    //     0xaf78ec: ldp             fp, lr, [SP], #0x10
    // 0xaf78f0: ret
    //     0xaf78f0: ret             
    // 0xaf78f4: r0 = NEmptyState()
    //     0xaf78f4: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xaf78f8: mov             x1, x0
    // 0xaf78fc: r2 = "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xaf78fc: add             x2, PP, #0x29, lsl #12  ; [pp+0x297e8] "Terdapat kendala saat membuka halaman, silakan coba lagi nanti."
    //     0xaf7900: ldr             x2, [x2, #0x7e8]
    // 0xaf7904: r3 = "assets/images/illustration/no_search.svg"
    //     0xaf7904: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xaf7908: ldr             x3, [x3, #0x138]
    // 0xaf790c: r5 = "Halaman Tidak Ditemukan"
    //     0xaf790c: add             x5, PP, #0x29, lsl #12  ; [pp+0x292c8] "Halaman Tidak Ditemukan"
    //     0xaf7910: ldr             x5, [x5, #0x2c8]
    // 0xaf7914: stur            x0, [fp, #-8]
    // 0xaf7918: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xaf7918: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xaf791c: r0 = NEmptyState.svg()
    //     0xaf791c: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xaf7920: ldur            x0, [fp, #-8]
    // 0xaf7924: LeaveFrame
    //     0xaf7924: mov             SP, fp
    //     0xaf7928: ldp             fp, lr, [SP], #0x10
    // 0xaf792c: ret
    //     0xaf792c: ret             
    // 0xaf7930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf7930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf7934: b               #0xaf788c
  }
  [closure] ListView <anonymous closure>(dynamic, List<Hikmah>?) {
    // ** addr: 0xaf7938, size: 0xf4
    // 0xaf7938: EnterFrame
    //     0xaf7938: stp             fp, lr, [SP, #-0x10]!
    //     0xaf793c: mov             fp, SP
    // 0xaf7940: AllocStack(0x28)
    //     0xaf7940: sub             SP, SP, #0x28
    // 0xaf7944: SetupParameters()
    //     0xaf7944: ldr             x0, [fp, #0x18]
    //     0xaf7948: ldur            w1, [x0, #0x17]
    //     0xaf794c: add             x1, x1, HEAP, lsl #32
    //     0xaf7950: stur            x1, [fp, #-8]
    // 0xaf7954: CheckStackOverflow
    //     0xaf7954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf7958: cmp             SP, x16
    //     0xaf795c: b.ls            #0xaf7a20
    // 0xaf7960: r1 = 1
    //     0xaf7960: movz            x1, #0x1
    // 0xaf7964: r0 = AllocateContext()
    //     0xaf7964: bl              #0xec126c  ; AllocateContextStub
    // 0xaf7968: mov             x1, x0
    // 0xaf796c: ldur            x0, [fp, #-8]
    // 0xaf7970: stur            x1, [fp, #-0x10]
    // 0xaf7974: StoreField: r1->field_b = r0
    //     0xaf7974: stur            w0, [x1, #0xb]
    // 0xaf7978: ldr             x0, [fp, #0x10]
    // 0xaf797c: StoreField: r1->field_f = r0
    //     0xaf797c: stur            w0, [x1, #0xf]
    // 0xaf7980: cmp             w0, NULL
    // 0xaf7984: b.eq            #0xaf7a28
    // 0xaf7988: r2 = LoadClassIdInstr(r0)
    //     0xaf7988: ldur            x2, [x0, #-1]
    //     0xaf798c: ubfx            x2, x2, #0xc, #0x14
    // 0xaf7990: str             x0, [SP]
    // 0xaf7994: mov             x0, x2
    // 0xaf7998: r0 = GDT[cid_x0 + 0xc834]()
    //     0xaf7998: movz            x17, #0xc834
    //     0xaf799c: add             lr, x0, x17
    //     0xaf79a0: ldr             lr, [x21, lr, lsl #3]
    //     0xaf79a4: blr             lr
    // 0xaf79a8: r3 = LoadInt32Instr(r0)
    //     0xaf79a8: sbfx            x3, x0, #1, #0x1f
    //     0xaf79ac: tbz             w0, #0, #0xaf79b4
    //     0xaf79b0: ldur            x3, [x0, #7]
    // 0xaf79b4: stur            x3, [fp, #-0x18]
    // 0xaf79b8: r1 = Function '<anonymous closure>':.
    //     0xaf79b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb78] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xaf79bc: ldr             x1, [x1, #0xb78]
    // 0xaf79c0: r2 = Null
    //     0xaf79c0: mov             x2, NULL
    // 0xaf79c4: r0 = AllocateClosure()
    //     0xaf79c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf79c8: ldur            x2, [fp, #-0x10]
    // 0xaf79cc: r1 = Function '<anonymous closure>':.
    //     0xaf79cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb80] AnonymousClosure: (0xaf7a2c), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf79d0: ldr             x1, [x1, #0xb80]
    // 0xaf79d4: stur            x0, [fp, #-8]
    // 0xaf79d8: r0 = AllocateClosure()
    //     0xaf79d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf79dc: stur            x0, [fp, #-0x10]
    // 0xaf79e0: r0 = ListView()
    //     0xaf79e0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf79e4: stur            x0, [fp, #-0x20]
    // 0xaf79e8: r16 = Instance_EdgeInsets
    //     0xaf79e8: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ac48] Obj!EdgeInsets@e126a1
    //     0xaf79ec: ldr             x16, [x16, #0xc48]
    // 0xaf79f0: str             x16, [SP]
    // 0xaf79f4: mov             x1, x0
    // 0xaf79f8: ldur            x2, [fp, #-0x10]
    // 0xaf79fc: ldur            x3, [fp, #-0x18]
    // 0xaf7a00: ldur            x5, [fp, #-8]
    // 0xaf7a04: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xaf7a04: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xaf7a08: ldr             x4, [x4, #0x700]
    // 0xaf7a0c: r0 = ListView.separated()
    //     0xaf7a0c: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xaf7a10: ldur            x0, [fp, #-0x20]
    // 0xaf7a14: LeaveFrame
    //     0xaf7a14: mov             SP, fp
    //     0xaf7a18: ldp             fp, lr, [SP], #0x10
    // 0xaf7a1c: ret
    //     0xaf7a1c: ret             
    // 0xaf7a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf7a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf7a24: b               #0xaf7960
    // 0xaf7a28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf7a28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf7a2c, size: 0x6c
    // 0xaf7a2c: EnterFrame
    //     0xaf7a2c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf7a30: mov             fp, SP
    // 0xaf7a34: AllocStack(0x10)
    //     0xaf7a34: sub             SP, SP, #0x10
    // 0xaf7a38: SetupParameters()
    //     0xaf7a38: ldr             x0, [fp, #0x20]
    //     0xaf7a3c: ldur            w1, [x0, #0x17]
    //     0xaf7a40: add             x1, x1, HEAP, lsl #32
    //     0xaf7a44: stur            x1, [fp, #-8]
    // 0xaf7a48: r1 = 1
    //     0xaf7a48: movz            x1, #0x1
    // 0xaf7a4c: r0 = AllocateContext()
    //     0xaf7a4c: bl              #0xec126c  ; AllocateContextStub
    // 0xaf7a50: mov             x1, x0
    // 0xaf7a54: ldur            x0, [fp, #-8]
    // 0xaf7a58: stur            x1, [fp, #-0x10]
    // 0xaf7a5c: StoreField: r1->field_b = r0
    //     0xaf7a5c: stur            w0, [x1, #0xb]
    // 0xaf7a60: ldr             x0, [fp, #0x10]
    // 0xaf7a64: StoreField: r1->field_f = r0
    //     0xaf7a64: stur            w0, [x1, #0xf]
    // 0xaf7a68: r0 = Obx()
    //     0xaf7a68: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf7a6c: ldur            x2, [fp, #-0x10]
    // 0xaf7a70: r1 = Function '<anonymous closure>':.
    //     0xaf7a70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb88] AnonymousClosure: (0xaf7a98), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf7a74: ldr             x1, [x1, #0xb88]
    // 0xaf7a78: stur            x0, [fp, #-8]
    // 0xaf7a7c: r0 = AllocateClosure()
    //     0xaf7a7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7a80: mov             x1, x0
    // 0xaf7a84: ldur            x0, [fp, #-8]
    // 0xaf7a88: StoreField: r0->field_b = r1
    //     0xaf7a88: stur            w1, [x0, #0xb]
    // 0xaf7a8c: LeaveFrame
    //     0xaf7a8c: mov             SP, fp
    //     0xaf7a90: ldp             fp, lr, [SP], #0x10
    // 0xaf7a94: ret
    //     0xaf7a94: ret             
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0xaf7a98, size: 0x18c
    // 0xaf7a98: EnterFrame
    //     0xaf7a98: stp             fp, lr, [SP, #-0x10]!
    //     0xaf7a9c: mov             fp, SP
    // 0xaf7aa0: AllocStack(0x30)
    //     0xaf7aa0: sub             SP, SP, #0x30
    // 0xaf7aa4: SetupParameters()
    //     0xaf7aa4: ldr             x0, [fp, #0x10]
    //     0xaf7aa8: ldur            w2, [x0, #0x17]
    //     0xaf7aac: add             x2, x2, HEAP, lsl #32
    //     0xaf7ab0: stur            x2, [fp, #-0x10]
    // 0xaf7ab4: CheckStackOverflow
    //     0xaf7ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf7ab8: cmp             SP, x16
    //     0xaf7abc: b.ls            #0xaf7c1c
    // 0xaf7ac0: LoadField: r0 = r2->field_b
    //     0xaf7ac0: ldur            w0, [x2, #0xb]
    // 0xaf7ac4: DecompressPointer r0
    //     0xaf7ac4: add             x0, x0, HEAP, lsl #32
    // 0xaf7ac8: stur            x0, [fp, #-8]
    // 0xaf7acc: LoadField: r1 = r0->field_b
    //     0xaf7acc: ldur            w1, [x0, #0xb]
    // 0xaf7ad0: DecompressPointer r1
    //     0xaf7ad0: add             x1, x1, HEAP, lsl #32
    // 0xaf7ad4: LoadField: r3 = r1->field_f
    //     0xaf7ad4: ldur            w3, [x1, #0xf]
    // 0xaf7ad8: DecompressPointer r3
    //     0xaf7ad8: add             x3, x3, HEAP, lsl #32
    // 0xaf7adc: mov             x1, x3
    // 0xaf7ae0: r0 = controller()
    //     0xaf7ae0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf7ae4: mov             x2, x0
    // 0xaf7ae8: ldur            x1, [fp, #-8]
    // 0xaf7aec: stur            x2, [fp, #-0x18]
    // 0xaf7af0: LoadField: r0 = r1->field_f
    //     0xaf7af0: ldur            w0, [x1, #0xf]
    // 0xaf7af4: DecompressPointer r0
    //     0xaf7af4: add             x0, x0, HEAP, lsl #32
    // 0xaf7af8: ldur            x3, [fp, #-0x10]
    // 0xaf7afc: LoadField: r4 = r3->field_f
    //     0xaf7afc: ldur            w4, [x3, #0xf]
    // 0xaf7b00: DecompressPointer r4
    //     0xaf7b00: add             x4, x4, HEAP, lsl #32
    // 0xaf7b04: r5 = LoadClassIdInstr(r0)
    //     0xaf7b04: ldur            x5, [x0, #-1]
    //     0xaf7b08: ubfx            x5, x5, #0xc, #0x14
    // 0xaf7b0c: stp             x4, x0, [SP]
    // 0xaf7b10: mov             x0, x5
    // 0xaf7b14: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf7b14: movz            x17, #0x3037
    //     0xaf7b18: movk            x17, #0x1, lsl #16
    //     0xaf7b1c: add             lr, x0, x17
    //     0xaf7b20: ldr             lr, [x21, lr, lsl #3]
    //     0xaf7b24: blr             lr
    // 0xaf7b28: LoadField: r2 = r0->field_f
    //     0xaf7b28: ldur            w2, [x0, #0xf]
    // 0xaf7b2c: DecompressPointer r2
    //     0xaf7b2c: add             x2, x2, HEAP, lsl #32
    // 0xaf7b30: ldur            x1, [fp, #-0x18]
    // 0xaf7b34: r0 = isQueryMatch()
    //     0xaf7b34: bl              #0xaf7c24  ; [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] _HajiHikmahController&GetxController&OnlineMixin&StateMixin&SearchMixin::isQueryMatch
    // 0xaf7b38: tbnz            w0, #4, #0xaf7bf8
    // 0xaf7b3c: ldur            x2, [fp, #-0x10]
    // 0xaf7b40: ldur            x0, [fp, #-8]
    // 0xaf7b44: LoadField: r1 = r2->field_f
    //     0xaf7b44: ldur            w1, [x2, #0xf]
    // 0xaf7b48: DecompressPointer r1
    //     0xaf7b48: add             x1, x1, HEAP, lsl #32
    // 0xaf7b4c: r3 = LoadInt32Instr(r1)
    //     0xaf7b4c: sbfx            x3, x1, #1, #0x1f
    //     0xaf7b50: tbz             w1, #0, #0xaf7b58
    //     0xaf7b54: ldur            x3, [x1, #7]
    // 0xaf7b58: add             x4, x3, #1
    // 0xaf7b5c: stur            x4, [fp, #-0x20]
    // 0xaf7b60: LoadField: r3 = r0->field_f
    //     0xaf7b60: ldur            w3, [x0, #0xf]
    // 0xaf7b64: DecompressPointer r3
    //     0xaf7b64: add             x3, x3, HEAP, lsl #32
    // 0xaf7b68: r0 = LoadClassIdInstr(r3)
    //     0xaf7b68: ldur            x0, [x3, #-1]
    //     0xaf7b6c: ubfx            x0, x0, #0xc, #0x14
    // 0xaf7b70: stp             x1, x3, [SP]
    // 0xaf7b74: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf7b74: movz            x17, #0x3037
    //     0xaf7b78: movk            x17, #0x1, lsl #16
    //     0xaf7b7c: add             lr, x0, x17
    //     0xaf7b80: ldr             lr, [x21, lr, lsl #3]
    //     0xaf7b84: blr             lr
    // 0xaf7b88: LoadField: r1 = r0->field_f
    //     0xaf7b88: ldur            w1, [x0, #0xf]
    // 0xaf7b8c: DecompressPointer r1
    //     0xaf7b8c: add             x1, x1, HEAP, lsl #32
    // 0xaf7b90: stur            x1, [fp, #-8]
    // 0xaf7b94: r0 = NNumberListTile()
    //     0xaf7b94: bl              #0xada214  ; AllocateNNumberListTileStub -> NNumberListTile (size=0x2c)
    // 0xaf7b98: mov             x3, x0
    // 0xaf7b9c: ldur            x0, [fp, #-0x20]
    // 0xaf7ba0: stur            x3, [fp, #-0x18]
    // 0xaf7ba4: StoreField: r3->field_b = r0
    //     0xaf7ba4: stur            x0, [x3, #0xb]
    // 0xaf7ba8: ldur            x0, [fp, #-8]
    // 0xaf7bac: StoreField: r3->field_13 = r0
    //     0xaf7bac: stur            w0, [x3, #0x13]
    // 0xaf7bb0: ldur            x2, [fp, #-0x10]
    // 0xaf7bb4: r1 = Function '<anonymous closure>':.
    //     0xaf7bb4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb90] AnonymousClosure: (0xaf7e64), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf7bb8: ldr             x1, [x1, #0xb90]
    // 0xaf7bbc: r0 = AllocateClosure()
    //     0xaf7bbc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7bc0: mov             x1, x0
    // 0xaf7bc4: ldur            x0, [fp, #-0x18]
    // 0xaf7bc8: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf7bc8: stur            w1, [x0, #0x17]
    // 0xaf7bcc: r1 = false
    //     0xaf7bcc: add             x1, NULL, #0x30  ; false
    // 0xaf7bd0: StoreField: r0->field_1b = r1
    //     0xaf7bd0: stur            w1, [x0, #0x1b]
    // 0xaf7bd4: StoreField: r0->field_1f = r1
    //     0xaf7bd4: stur            w1, [x0, #0x1f]
    // 0xaf7bd8: ldur            x2, [fp, #-0x10]
    // 0xaf7bdc: r1 = Function '<anonymous closure>':.
    //     0xaf7bdc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb98] AnonymousClosure: (0xaf7cf0), in [package:nuonline/app/modules/haji/views/haji_hikmah_view.dart] HajiHikmahView::build (0xaf749c)
    //     0xaf7be0: ldr             x1, [x1, #0xb98]
    // 0xaf7be4: r0 = AllocateClosure()
    //     0xaf7be4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf7be8: mov             x1, x0
    // 0xaf7bec: ldur            x0, [fp, #-0x18]
    // 0xaf7bf0: StoreField: r0->field_27 = r1
    //     0xaf7bf0: stur            w1, [x0, #0x27]
    // 0xaf7bf4: b               #0xaf7c10
    // 0xaf7bf8: r0 = Container()
    //     0xaf7bf8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf7bfc: mov             x1, x0
    // 0xaf7c00: stur            x0, [fp, #-8]
    // 0xaf7c04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf7c04: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf7c08: r0 = Container()
    //     0xaf7c08: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf7c0c: ldur            x0, [fp, #-8]
    // 0xaf7c10: LeaveFrame
    //     0xaf7c10: mov             SP, fp
    //     0xaf7c14: ldp             fp, lr, [SP], #0x10
    // 0xaf7c18: ret
    //     0xaf7c18: ret             
    // 0xaf7c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf7c1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf7c20: b               #0xaf7ac0
  }
  [closure] SubstringHighlight <anonymous closure>(dynamic, String) {
    // ** addr: 0xaf7cf0, size: 0x174
    // 0xaf7cf0: EnterFrame
    //     0xaf7cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf7cf4: mov             fp, SP
    // 0xaf7cf8: AllocStack(0x38)
    //     0xaf7cf8: sub             SP, SP, #0x38
    // 0xaf7cfc: SetupParameters()
    //     0xaf7cfc: ldr             x0, [fp, #0x18]
    //     0xaf7d00: ldur            w1, [x0, #0x17]
    //     0xaf7d04: add             x1, x1, HEAP, lsl #32
    //     0xaf7d08: stur            x1, [fp, #-8]
    // 0xaf7d0c: CheckStackOverflow
    //     0xaf7d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf7d10: cmp             SP, x16
    //     0xaf7d14: b.ls            #0xaf7e58
    // 0xaf7d18: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf7d18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf7d1c: ldr             x0, [x0, #0x2670]
    //     0xaf7d20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf7d24: cmp             w0, w16
    //     0xaf7d28: b.ne            #0xaf7d34
    //     0xaf7d2c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf7d30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf7d34: r0 = GetNavigation.textTheme()
    //     0xaf7d34: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xaf7d38: LoadField: r1 = r0->field_23
    //     0xaf7d38: ldur            w1, [x0, #0x23]
    // 0xaf7d3c: DecompressPointer r1
    //     0xaf7d3c: add             x1, x1, HEAP, lsl #32
    // 0xaf7d40: cmp             w1, NULL
    // 0xaf7d44: b.eq            #0xaf7e60
    // 0xaf7d48: r16 = Instance_FontWeight
    //     0xaf7d48: add             x16, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xaf7d4c: ldr             x16, [x16, #0xcc0]
    // 0xaf7d50: str             x16, [SP]
    // 0xaf7d54: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xaf7d54: add             x4, PP, #0x27, lsl #12  ; [pp+0x27fe0] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xaf7d58: ldr             x4, [x4, #0xfe0]
    // 0xaf7d5c: r0 = copyWith()
    //     0xaf7d5c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf7d60: mov             x2, x0
    // 0xaf7d64: ldur            x0, [fp, #-8]
    // 0xaf7d68: stur            x2, [fp, #-0x10]
    // 0xaf7d6c: LoadField: r1 = r0->field_b
    //     0xaf7d6c: ldur            w1, [x0, #0xb]
    // 0xaf7d70: DecompressPointer r1
    //     0xaf7d70: add             x1, x1, HEAP, lsl #32
    // 0xaf7d74: LoadField: r0 = r1->field_b
    //     0xaf7d74: ldur            w0, [x1, #0xb]
    // 0xaf7d78: DecompressPointer r0
    //     0xaf7d78: add             x0, x0, HEAP, lsl #32
    // 0xaf7d7c: LoadField: r1 = r0->field_f
    //     0xaf7d7c: ldur            w1, [x0, #0xf]
    // 0xaf7d80: DecompressPointer r1
    //     0xaf7d80: add             x1, x1, HEAP, lsl #32
    // 0xaf7d84: r0 = controller()
    //     0xaf7d84: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf7d88: mov             x1, x0
    // 0xaf7d8c: r0 = hasError()
    //     0xaf7d8c: bl              #0xad1aa0  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError
    // 0xaf7d90: r1 = LoadClassIdInstr(r0)
    //     0xaf7d90: ldur            x1, [x0, #-1]
    //     0xaf7d94: ubfx            x1, x1, #0xc, #0x14
    // 0xaf7d98: mov             x16, x0
    // 0xaf7d9c: mov             x0, x1
    // 0xaf7da0: mov             x1, x16
    // 0xaf7da4: r2 = " "
    //     0xaf7da4: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xaf7da8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaf7da8: sub             lr, x0, #1, lsl #12
    //     0xaf7dac: ldr             lr, [x21, lr, lsl #3]
    //     0xaf7db0: blr             lr
    // 0xaf7db4: r1 = _ConstMap len:10
    //     0xaf7db4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaf7db8: ldr             x1, [x1, #0xc08]
    // 0xaf7dbc: r2 = 600
    //     0xaf7dbc: movz            x2, #0x258
    // 0xaf7dc0: stur            x0, [fp, #-8]
    // 0xaf7dc4: r0 = []()
    //     0xaf7dc4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf7dc8: r16 = <Color?>
    //     0xaf7dc8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf7dcc: ldr             x16, [x16, #0x98]
    // 0xaf7dd0: stp             x0, x16, [SP, #8]
    // 0xaf7dd4: r16 = Instance_MaterialColor
    //     0xaf7dd4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaf7dd8: ldr             x16, [x16, #0xcf0]
    // 0xaf7ddc: str             x16, [SP]
    // 0xaf7de0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf7de0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf7de4: r0 = mode()
    //     0xaf7de4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf7de8: stur            x0, [fp, #-0x18]
    // 0xaf7dec: r0 = TextStyle()
    //     0xaf7dec: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf7df0: mov             x1, x0
    // 0xaf7df4: r0 = true
    //     0xaf7df4: add             x0, NULL, #0x20  ; true
    // 0xaf7df8: stur            x1, [fp, #-0x20]
    // 0xaf7dfc: StoreField: r1->field_7 = r0
    //     0xaf7dfc: stur            w0, [x1, #7]
    // 0xaf7e00: ldur            x0, [fp, #-0x18]
    // 0xaf7e04: StoreField: r1->field_b = r0
    //     0xaf7e04: stur            w0, [x1, #0xb]
    // 0xaf7e08: r0 = SubstringHighlight()
    //     0xaf7e08: bl              #0x624c98  ; AllocateSubstringHighlightStub -> SubstringHighlight (size=0x34)
    // 0xaf7e0c: r1 = false
    //     0xaf7e0c: add             x1, NULL, #0x30  ; false
    // 0xaf7e10: StoreField: r0->field_b = r1
    //     0xaf7e10: stur            w1, [x0, #0xb]
    // 0xaf7e14: r2 = Instance_TextOverflow
    //     0xaf7e14: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xaf7e18: ldr             x2, [x2, #0xc60]
    // 0xaf7e1c: StoreField: r0->field_f = r2
    //     0xaf7e1c: stur            w2, [x0, #0xf]
    // 0xaf7e20: ldur            x2, [fp, #-8]
    // 0xaf7e24: StoreField: r0->field_1b = r2
    //     0xaf7e24: stur            w2, [x0, #0x1b]
    // 0xaf7e28: ldr             x2, [fp, #0x10]
    // 0xaf7e2c: StoreField: r0->field_1f = r2
    //     0xaf7e2c: stur            w2, [x0, #0x1f]
    // 0xaf7e30: r2 = Instance_TextAlign
    //     0xaf7e30: ldr             x2, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0xaf7e34: StoreField: r0->field_23 = r2
    //     0xaf7e34: stur            w2, [x0, #0x23]
    // 0xaf7e38: ldur            x2, [fp, #-0x10]
    // 0xaf7e3c: StoreField: r0->field_27 = r2
    //     0xaf7e3c: stur            w2, [x0, #0x27]
    // 0xaf7e40: ldur            x2, [fp, #-0x20]
    // 0xaf7e44: StoreField: r0->field_2b = r2
    //     0xaf7e44: stur            w2, [x0, #0x2b]
    // 0xaf7e48: StoreField: r0->field_2f = r1
    //     0xaf7e48: stur            w1, [x0, #0x2f]
    // 0xaf7e4c: LeaveFrame
    //     0xaf7e4c: mov             SP, fp
    //     0xaf7e50: ldp             fp, lr, [SP], #0x10
    // 0xaf7e54: ret
    //     0xaf7e54: ret             
    // 0xaf7e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf7e58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf7e5c: b               #0xaf7d18
    // 0xaf7e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf7e60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf7e64, size: 0x3ac
    // 0xaf7e64: EnterFrame
    //     0xaf7e64: stp             fp, lr, [SP, #-0x10]!
    //     0xaf7e68: mov             fp, SP
    // 0xaf7e6c: AllocStack(0x30)
    //     0xaf7e6c: sub             SP, SP, #0x30
    // 0xaf7e70: SetupParameters()
    //     0xaf7e70: ldr             x0, [fp, #0x10]
    //     0xaf7e74: ldur            w1, [x0, #0x17]
    //     0xaf7e78: add             x1, x1, HEAP, lsl #32
    //     0xaf7e7c: stur            x1, [fp, #-8]
    // 0xaf7e80: CheckStackOverflow
    //     0xaf7e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf7e84: cmp             SP, x16
    //     0xaf7e88: b.ls            #0xaf8208
    // 0xaf7e8c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf7e8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf7e90: ldr             x0, [x0, #0x2670]
    //     0xaf7e94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf7e98: cmp             w0, w16
    //     0xaf7e9c: b.ne            #0xaf7ea8
    //     0xaf7ea0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xaf7ea4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaf7ea8: r1 = Null
    //     0xaf7ea8: mov             x1, NULL
    // 0xaf7eac: r2 = 24
    //     0xaf7eac: movz            x2, #0x18
    // 0xaf7eb0: r0 = AllocateArray()
    //     0xaf7eb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf7eb4: mov             x1, x0
    // 0xaf7eb8: stur            x1, [fp, #-0x18]
    // 0xaf7ebc: r16 = "id"
    //     0xaf7ebc: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xaf7ec0: ldr             x16, [x16, #0x740]
    // 0xaf7ec4: StoreField: r1->field_f = r16
    //     0xaf7ec4: stur            w16, [x1, #0xf]
    // 0xaf7ec8: ldur            x2, [fp, #-8]
    // 0xaf7ecc: LoadField: r3 = r2->field_b
    //     0xaf7ecc: ldur            w3, [x2, #0xb]
    // 0xaf7ed0: DecompressPointer r3
    //     0xaf7ed0: add             x3, x3, HEAP, lsl #32
    // 0xaf7ed4: stur            x3, [fp, #-0x10]
    // 0xaf7ed8: LoadField: r0 = r3->field_f
    //     0xaf7ed8: ldur            w0, [x3, #0xf]
    // 0xaf7edc: DecompressPointer r0
    //     0xaf7edc: add             x0, x0, HEAP, lsl #32
    // 0xaf7ee0: LoadField: r4 = r2->field_f
    //     0xaf7ee0: ldur            w4, [x2, #0xf]
    // 0xaf7ee4: DecompressPointer r4
    //     0xaf7ee4: add             x4, x4, HEAP, lsl #32
    // 0xaf7ee8: r5 = LoadClassIdInstr(r0)
    //     0xaf7ee8: ldur            x5, [x0, #-1]
    //     0xaf7eec: ubfx            x5, x5, #0xc, #0x14
    // 0xaf7ef0: stp             x4, x0, [SP]
    // 0xaf7ef4: mov             x0, x5
    // 0xaf7ef8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf7ef8: movz            x17, #0x3037
    //     0xaf7efc: movk            x17, #0x1, lsl #16
    //     0xaf7f00: add             lr, x0, x17
    //     0xaf7f04: ldr             lr, [x21, lr, lsl #3]
    //     0xaf7f08: blr             lr
    // 0xaf7f0c: LoadField: r2 = r0->field_7
    //     0xaf7f0c: ldur            x2, [x0, #7]
    // 0xaf7f10: r0 = BoxInt64Instr(r2)
    //     0xaf7f10: sbfiz           x0, x2, #1, #0x1f
    //     0xaf7f14: cmp             x2, x0, asr #1
    //     0xaf7f18: b.eq            #0xaf7f24
    //     0xaf7f1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf7f20: stur            x2, [x0, #7]
    // 0xaf7f24: ldur            x1, [fp, #-0x18]
    // 0xaf7f28: ArrayStore: r1[1] = r0  ; List_4
    //     0xaf7f28: add             x25, x1, #0x13
    //     0xaf7f2c: str             w0, [x25]
    //     0xaf7f30: tbz             w0, #0, #0xaf7f4c
    //     0xaf7f34: ldurb           w16, [x1, #-1]
    //     0xaf7f38: ldurb           w17, [x0, #-1]
    //     0xaf7f3c: and             x16, x17, x16, lsr #2
    //     0xaf7f40: tst             x16, HEAP, lsr #32
    //     0xaf7f44: b.eq            #0xaf7f4c
    //     0xaf7f48: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf7f4c: ldur            x1, [fp, #-0x18]
    // 0xaf7f50: r16 = "title"
    //     0xaf7f50: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xaf7f54: ldr             x16, [x16, #0x748]
    // 0xaf7f58: ArrayStore: r1[0] = r16  ; List_4
    //     0xaf7f58: stur            w16, [x1, #0x17]
    // 0xaf7f5c: ldur            x2, [fp, #-0x10]
    // 0xaf7f60: LoadField: r0 = r2->field_f
    //     0xaf7f60: ldur            w0, [x2, #0xf]
    // 0xaf7f64: DecompressPointer r0
    //     0xaf7f64: add             x0, x0, HEAP, lsl #32
    // 0xaf7f68: ldur            x3, [fp, #-8]
    // 0xaf7f6c: LoadField: r4 = r3->field_f
    //     0xaf7f6c: ldur            w4, [x3, #0xf]
    // 0xaf7f70: DecompressPointer r4
    //     0xaf7f70: add             x4, x4, HEAP, lsl #32
    // 0xaf7f74: r5 = LoadClassIdInstr(r0)
    //     0xaf7f74: ldur            x5, [x0, #-1]
    //     0xaf7f78: ubfx            x5, x5, #0xc, #0x14
    // 0xaf7f7c: stp             x4, x0, [SP]
    // 0xaf7f80: mov             x0, x5
    // 0xaf7f84: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf7f84: movz            x17, #0x3037
    //     0xaf7f88: movk            x17, #0x1, lsl #16
    //     0xaf7f8c: add             lr, x0, x17
    //     0xaf7f90: ldr             lr, [x21, lr, lsl #3]
    //     0xaf7f94: blr             lr
    // 0xaf7f98: LoadField: r1 = r0->field_f
    //     0xaf7f98: ldur            w1, [x0, #0xf]
    // 0xaf7f9c: DecompressPointer r1
    //     0xaf7f9c: add             x1, x1, HEAP, lsl #32
    // 0xaf7fa0: mov             x0, x1
    // 0xaf7fa4: ldur            x1, [fp, #-0x18]
    // 0xaf7fa8: ArrayStore: r1[3] = r0  ; List_4
    //     0xaf7fa8: add             x25, x1, #0x1b
    //     0xaf7fac: str             w0, [x25]
    //     0xaf7fb0: tbz             w0, #0, #0xaf7fcc
    //     0xaf7fb4: ldurb           w16, [x1, #-1]
    //     0xaf7fb8: ldurb           w17, [x0, #-1]
    //     0xaf7fbc: and             x16, x17, x16, lsr #2
    //     0xaf7fc0: tst             x16, HEAP, lsr #32
    //     0xaf7fc4: b.eq            #0xaf7fcc
    //     0xaf7fc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf7fcc: ldur            x1, [fp, #-0x18]
    // 0xaf7fd0: r16 = "content"
    //     0xaf7fd0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0xaf7fd4: ldr             x16, [x16, #0xf58]
    // 0xaf7fd8: StoreField: r1->field_1f = r16
    //     0xaf7fd8: stur            w16, [x1, #0x1f]
    // 0xaf7fdc: ldur            x2, [fp, #-0x10]
    // 0xaf7fe0: LoadField: r0 = r2->field_f
    //     0xaf7fe0: ldur            w0, [x2, #0xf]
    // 0xaf7fe4: DecompressPointer r0
    //     0xaf7fe4: add             x0, x0, HEAP, lsl #32
    // 0xaf7fe8: ldur            x3, [fp, #-8]
    // 0xaf7fec: LoadField: r4 = r3->field_f
    //     0xaf7fec: ldur            w4, [x3, #0xf]
    // 0xaf7ff0: DecompressPointer r4
    //     0xaf7ff0: add             x4, x4, HEAP, lsl #32
    // 0xaf7ff4: r5 = LoadClassIdInstr(r0)
    //     0xaf7ff4: ldur            x5, [x0, #-1]
    //     0xaf7ff8: ubfx            x5, x5, #0xc, #0x14
    // 0xaf7ffc: stp             x4, x0, [SP]
    // 0xaf8000: mov             x0, x5
    // 0xaf8004: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf8004: movz            x17, #0x3037
    //     0xaf8008: movk            x17, #0x1, lsl #16
    //     0xaf800c: add             lr, x0, x17
    //     0xaf8010: ldr             lr, [x21, lr, lsl #3]
    //     0xaf8014: blr             lr
    // 0xaf8018: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf8018: ldur            w1, [x0, #0x17]
    // 0xaf801c: DecompressPointer r1
    //     0xaf801c: add             x1, x1, HEAP, lsl #32
    // 0xaf8020: mov             x0, x1
    // 0xaf8024: ldur            x1, [fp, #-0x18]
    // 0xaf8028: ArrayStore: r1[5] = r0  ; List_4
    //     0xaf8028: add             x25, x1, #0x23
    //     0xaf802c: str             w0, [x25]
    //     0xaf8030: tbz             w0, #0, #0xaf804c
    //     0xaf8034: ldurb           w16, [x1, #-1]
    //     0xaf8038: ldurb           w17, [x0, #-1]
    //     0xaf803c: and             x16, x17, x16, lsr #2
    //     0xaf8040: tst             x16, HEAP, lsr #32
    //     0xaf8044: b.eq            #0xaf804c
    //     0xaf8048: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf804c: ldur            x1, [fp, #-0x18]
    // 0xaf8050: r16 = "image"
    //     0xaf8050: add             x16, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xaf8054: ldr             x16, [x16, #0x520]
    // 0xaf8058: StoreField: r1->field_27 = r16
    //     0xaf8058: stur            w16, [x1, #0x27]
    // 0xaf805c: ldur            x2, [fp, #-0x10]
    // 0xaf8060: LoadField: r0 = r2->field_f
    //     0xaf8060: ldur            w0, [x2, #0xf]
    // 0xaf8064: DecompressPointer r0
    //     0xaf8064: add             x0, x0, HEAP, lsl #32
    // 0xaf8068: ldur            x3, [fp, #-8]
    // 0xaf806c: LoadField: r4 = r3->field_f
    //     0xaf806c: ldur            w4, [x3, #0xf]
    // 0xaf8070: DecompressPointer r4
    //     0xaf8070: add             x4, x4, HEAP, lsl #32
    // 0xaf8074: r5 = LoadClassIdInstr(r0)
    //     0xaf8074: ldur            x5, [x0, #-1]
    //     0xaf8078: ubfx            x5, x5, #0xc, #0x14
    // 0xaf807c: stp             x4, x0, [SP]
    // 0xaf8080: mov             x0, x5
    // 0xaf8084: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf8084: movz            x17, #0x3037
    //     0xaf8088: movk            x17, #0x1, lsl #16
    //     0xaf808c: add             lr, x0, x17
    //     0xaf8090: ldr             lr, [x21, lr, lsl #3]
    //     0xaf8094: blr             lr
    // 0xaf8098: LoadField: r1 = r0->field_13
    //     0xaf8098: ldur            w1, [x0, #0x13]
    // 0xaf809c: DecompressPointer r1
    //     0xaf809c: add             x1, x1, HEAP, lsl #32
    // 0xaf80a0: LoadField: r0 = r1->field_f
    //     0xaf80a0: ldur            w0, [x1, #0xf]
    // 0xaf80a4: DecompressPointer r0
    //     0xaf80a4: add             x0, x0, HEAP, lsl #32
    // 0xaf80a8: ldur            x1, [fp, #-0x18]
    // 0xaf80ac: ArrayStore: r1[7] = r0  ; List_4
    //     0xaf80ac: add             x25, x1, #0x2b
    //     0xaf80b0: str             w0, [x25]
    //     0xaf80b4: tbz             w0, #0, #0xaf80d0
    //     0xaf80b8: ldurb           w16, [x1, #-1]
    //     0xaf80bc: ldurb           w17, [x0, #-1]
    //     0xaf80c0: and             x16, x17, x16, lsr #2
    //     0xaf80c4: tst             x16, HEAP, lsr #32
    //     0xaf80c8: b.eq            #0xaf80d0
    //     0xaf80cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf80d0: ldur            x1, [fp, #-0x18]
    // 0xaf80d4: r16 = "articles"
    //     0xaf80d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0xaf80d8: ldr             x16, [x16, #0x8b8]
    // 0xaf80dc: StoreField: r1->field_2f = r16
    //     0xaf80dc: stur            w16, [x1, #0x2f]
    // 0xaf80e0: ldur            x2, [fp, #-0x10]
    // 0xaf80e4: LoadField: r0 = r2->field_f
    //     0xaf80e4: ldur            w0, [x2, #0xf]
    // 0xaf80e8: DecompressPointer r0
    //     0xaf80e8: add             x0, x0, HEAP, lsl #32
    // 0xaf80ec: ldur            x3, [fp, #-8]
    // 0xaf80f0: LoadField: r4 = r3->field_f
    //     0xaf80f0: ldur            w4, [x3, #0xf]
    // 0xaf80f4: DecompressPointer r4
    //     0xaf80f4: add             x4, x4, HEAP, lsl #32
    // 0xaf80f8: r5 = LoadClassIdInstr(r0)
    //     0xaf80f8: ldur            x5, [x0, #-1]
    //     0xaf80fc: ubfx            x5, x5, #0xc, #0x14
    // 0xaf8100: stp             x4, x0, [SP]
    // 0xaf8104: mov             x0, x5
    // 0xaf8108: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf8108: movz            x17, #0x3037
    //     0xaf810c: movk            x17, #0x1, lsl #16
    //     0xaf8110: add             lr, x0, x17
    //     0xaf8114: ldr             lr, [x21, lr, lsl #3]
    //     0xaf8118: blr             lr
    // 0xaf811c: LoadField: r1 = r0->field_1b
    //     0xaf811c: ldur            w1, [x0, #0x1b]
    // 0xaf8120: DecompressPointer r1
    //     0xaf8120: add             x1, x1, HEAP, lsl #32
    // 0xaf8124: mov             x0, x1
    // 0xaf8128: ldur            x1, [fp, #-0x18]
    // 0xaf812c: ArrayStore: r1[9] = r0  ; List_4
    //     0xaf812c: add             x25, x1, #0x33
    //     0xaf8130: str             w0, [x25]
    //     0xaf8134: tbz             w0, #0, #0xaf8150
    //     0xaf8138: ldurb           w16, [x1, #-1]
    //     0xaf813c: ldurb           w17, [x0, #-1]
    //     0xaf8140: and             x16, x17, x16, lsr #2
    //     0xaf8144: tst             x16, HEAP, lsr #32
    //     0xaf8148: b.eq            #0xaf8150
    //     0xaf814c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf8150: ldur            x1, [fp, #-0x18]
    // 0xaf8154: r16 = "videos"
    //     0xaf8154: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0xaf8158: ldr             x16, [x16, #0x8d8]
    // 0xaf815c: StoreField: r1->field_37 = r16
    //     0xaf815c: stur            w16, [x1, #0x37]
    // 0xaf8160: ldur            x0, [fp, #-0x10]
    // 0xaf8164: LoadField: r2 = r0->field_f
    //     0xaf8164: ldur            w2, [x0, #0xf]
    // 0xaf8168: DecompressPointer r2
    //     0xaf8168: add             x2, x2, HEAP, lsl #32
    // 0xaf816c: ldur            x0, [fp, #-8]
    // 0xaf8170: LoadField: r3 = r0->field_f
    //     0xaf8170: ldur            w3, [x0, #0xf]
    // 0xaf8174: DecompressPointer r3
    //     0xaf8174: add             x3, x3, HEAP, lsl #32
    // 0xaf8178: r0 = LoadClassIdInstr(r2)
    //     0xaf8178: ldur            x0, [x2, #-1]
    //     0xaf817c: ubfx            x0, x0, #0xc, #0x14
    // 0xaf8180: stp             x3, x2, [SP]
    // 0xaf8184: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaf8184: movz            x17, #0x3037
    //     0xaf8188: movk            x17, #0x1, lsl #16
    //     0xaf818c: add             lr, x0, x17
    //     0xaf8190: ldr             lr, [x21, lr, lsl #3]
    //     0xaf8194: blr             lr
    // 0xaf8198: LoadField: r1 = r0->field_1f
    //     0xaf8198: ldur            w1, [x0, #0x1f]
    // 0xaf819c: DecompressPointer r1
    //     0xaf819c: add             x1, x1, HEAP, lsl #32
    // 0xaf81a0: mov             x0, x1
    // 0xaf81a4: ldur            x1, [fp, #-0x18]
    // 0xaf81a8: ArrayStore: r1[11] = r0  ; List_4
    //     0xaf81a8: add             x25, x1, #0x3b
    //     0xaf81ac: str             w0, [x25]
    //     0xaf81b0: tbz             w0, #0, #0xaf81cc
    //     0xaf81b4: ldurb           w16, [x1, #-1]
    //     0xaf81b8: ldurb           w17, [x0, #-1]
    //     0xaf81bc: and             x16, x17, x16, lsr #2
    //     0xaf81c0: tst             x16, HEAP, lsr #32
    //     0xaf81c4: b.eq            #0xaf81cc
    //     0xaf81c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaf81cc: r16 = <String, Object>
    //     0xaf81cc: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xaf81d0: ldr             x16, [x16, #0x790]
    // 0xaf81d4: ldur            lr, [fp, #-0x18]
    // 0xaf81d8: stp             lr, x16, [SP]
    // 0xaf81dc: r0 = Map._fromLiteral()
    //     0xaf81dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaf81e0: r16 = "/haji/hikmah/detail"
    //     0xaf81e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "/haji/hikmah/detail"
    //     0xaf81e4: ldr             x16, [x16, #0xba0]
    // 0xaf81e8: stp             x16, NULL, [SP, #8]
    // 0xaf81ec: str             x0, [SP]
    // 0xaf81f0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xaf81f0: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xaf81f4: ldr             x4, [x4, #0x478]
    // 0xaf81f8: r0 = GetNavigation.toNamed()
    //     0xaf81f8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xaf81fc: LeaveFrame
    //     0xaf81fc: mov             SP, fp
    //     0xaf8200: ldp             fp, lr, [SP], #0x10
    // 0xaf8204: ret
    //     0xaf8204: ret             
    // 0xaf8208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf8208: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf820c: b               #0xaf7e8c
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xaf8210, size: 0x158
    // 0xaf8210: EnterFrame
    //     0xaf8210: stp             fp, lr, [SP, #-0x10]!
    //     0xaf8214: mov             fp, SP
    // 0xaf8218: AllocStack(0x30)
    //     0xaf8218: sub             SP, SP, #0x30
    // 0xaf821c: SetupParameters()
    //     0xaf821c: ldr             x0, [fp, #0x10]
    //     0xaf8220: ldur            w2, [x0, #0x17]
    //     0xaf8224: add             x2, x2, HEAP, lsl #32
    //     0xaf8228: stur            x2, [fp, #-8]
    // 0xaf822c: CheckStackOverflow
    //     0xaf822c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf8230: cmp             SP, x16
    //     0xaf8234: b.ls            #0xaf8354
    // 0xaf8238: LoadField: r1 = r2->field_f
    //     0xaf8238: ldur            w1, [x2, #0xf]
    // 0xaf823c: DecompressPointer r1
    //     0xaf823c: add             x1, x1, HEAP, lsl #32
    // 0xaf8240: r0 = controller()
    //     0xaf8240: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf8244: LoadField: r1 = r0->field_2b
    //     0xaf8244: ldur            w1, [x0, #0x2b]
    // 0xaf8248: DecompressPointer r1
    //     0xaf8248: add             x1, x1, HEAP, lsl #32
    // 0xaf824c: r0 = value()
    //     0xaf824c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf8250: mov             x2, x0
    // 0xaf8254: ldur            x0, [fp, #-8]
    // 0xaf8258: stur            x2, [fp, #-0x10]
    // 0xaf825c: LoadField: r1 = r0->field_f
    //     0xaf825c: ldur            w1, [x0, #0xf]
    // 0xaf8260: DecompressPointer r1
    //     0xaf8260: add             x1, x1, HEAP, lsl #32
    // 0xaf8264: r0 = controller()
    //     0xaf8264: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf8268: LoadField: r2 = r0->field_27
    //     0xaf8268: ldur            w2, [x0, #0x27]
    // 0xaf826c: DecompressPointer r2
    //     0xaf826c: add             x2, x2, HEAP, lsl #32
    // 0xaf8270: r16 = Sentinel
    //     0xaf8270: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf8274: cmp             w2, w16
    // 0xaf8278: b.eq            #0xaf835c
    // 0xaf827c: ldur            x0, [fp, #-8]
    // 0xaf8280: stur            x2, [fp, #-0x18]
    // 0xaf8284: LoadField: r1 = r0->field_f
    //     0xaf8284: ldur            w1, [x0, #0xf]
    // 0xaf8288: DecompressPointer r1
    //     0xaf8288: add             x1, x1, HEAP, lsl #32
    // 0xaf828c: r0 = controller()
    //     0xaf828c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf8290: mov             x2, x0
    // 0xaf8294: ldur            x0, [fp, #-8]
    // 0xaf8298: stur            x2, [fp, #-0x20]
    // 0xaf829c: LoadField: r1 = r0->field_f
    //     0xaf829c: ldur            w1, [x0, #0xf]
    // 0xaf82a0: DecompressPointer r1
    //     0xaf82a0: add             x1, x1, HEAP, lsl #32
    // 0xaf82a4: r0 = controller()
    //     0xaf82a4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf82a8: mov             x2, x0
    // 0xaf82ac: ldur            x0, [fp, #-8]
    // 0xaf82b0: stur            x2, [fp, #-0x28]
    // 0xaf82b4: LoadField: r1 = r0->field_f
    //     0xaf82b4: ldur            w1, [x0, #0xf]
    // 0xaf82b8: DecompressPointer r1
    //     0xaf82b8: add             x1, x1, HEAP, lsl #32
    // 0xaf82bc: r0 = controller()
    //     0xaf82bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf82c0: stur            x0, [fp, #-8]
    // 0xaf82c4: r0 = NSearchTextField()
    //     0xaf82c4: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xaf82c8: mov             x3, x0
    // 0xaf82cc: ldur            x0, [fp, #-0x18]
    // 0xaf82d0: stur            x3, [fp, #-0x30]
    // 0xaf82d4: StoreField: r3->field_f = r0
    //     0xaf82d4: stur            w0, [x3, #0xf]
    // 0xaf82d8: ldur            x0, [fp, #-0x10]
    // 0xaf82dc: StoreField: r3->field_b = r0
    //     0xaf82dc: stur            w0, [x3, #0xb]
    // 0xaf82e0: ldur            x2, [fp, #-0x20]
    // 0xaf82e4: r1 = Function 'onQueryChanged':.
    //     0xaf82e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eba8] AnonymousClosure: (0xaf83e0), in [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] _HajiHikmahController&GetxController&OnlineMixin&StateMixin&SearchMixin::onQueryChanged (0xaf841c)
    //     0xaf82e8: ldr             x1, [x1, #0xba8]
    // 0xaf82ec: r0 = AllocateClosure()
    //     0xaf82ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf82f0: mov             x1, x0
    // 0xaf82f4: ldur            x0, [fp, #-0x30]
    // 0xaf82f8: StoreField: r0->field_13 = r1
    //     0xaf82f8: stur            w1, [x0, #0x13]
    // 0xaf82fc: ldur            x2, [fp, #-0x28]
    // 0xaf8300: r1 = Function 'onSearchCanceled':.
    //     0xaf8300: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ebb0] AnonymousClosure: (0xaf8368), in [package:nuonline/app/modules/haji/controllers/haji_hikmah_controller.dart] _HajiHikmahController&GetxController&OnlineMixin&StateMixin&SearchMixin::onSearchCanceled (0xaf83a0)
    //     0xaf8304: ldr             x1, [x1, #0xbb0]
    // 0xaf8308: r0 = AllocateClosure()
    //     0xaf8308: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf830c: mov             x1, x0
    // 0xaf8310: ldur            x0, [fp, #-0x30]
    // 0xaf8314: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf8314: stur            w1, [x0, #0x17]
    // 0xaf8318: r1 = "Cari"
    //     0xaf8318: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac20] "Cari"
    //     0xaf831c: ldr             x1, [x1, #0xc20]
    // 0xaf8320: StoreField: r0->field_23 = r1
    //     0xaf8320: stur            w1, [x0, #0x23]
    // 0xaf8324: ldur            x2, [fp, #-8]
    // 0xaf8328: r1 = Function 'onSearchSubmitted':.
    //     0xaf8328: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ebb8] AnonymousClosure: (0x6a1e7c), of [dart:ui] PointerData
    //     0xaf832c: ldr             x1, [x1, #0xbb8]
    // 0xaf8330: r0 = AllocateClosure()
    //     0xaf8330: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf8334: mov             x1, x0
    // 0xaf8338: ldur            x0, [fp, #-0x30]
    // 0xaf833c: StoreField: r0->field_1b = r1
    //     0xaf833c: stur            w1, [x0, #0x1b]
    // 0xaf8340: r1 = false
    //     0xaf8340: add             x1, NULL, #0x30  ; false
    // 0xaf8344: StoreField: r0->field_27 = r1
    //     0xaf8344: stur            w1, [x0, #0x27]
    // 0xaf8348: LeaveFrame
    //     0xaf8348: mov             SP, fp
    //     0xaf834c: ldp             fp, lr, [SP], #0x10
    // 0xaf8350: ret
    //     0xaf8350: ret             
    // 0xaf8354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf8354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf8358: b               #0xaf8238
    // 0xaf835c: r9 = searchController
    //     0xaf835c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ebc0] Field <_HajiHikmahController&GetxController&OnlineMixin&StateMixin&<EMAIL>>: late (offset: 0x28)
    //     0xaf8360: ldr             x9, [x9, #0xbc0]
    // 0xaf8364: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf8364: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
