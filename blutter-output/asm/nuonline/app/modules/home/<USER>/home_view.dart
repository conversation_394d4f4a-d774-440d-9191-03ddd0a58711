// lib: , url: package:nuonline/app/modules/home/<USER>/home_view.dart

// class id: 1050309, size: 0x8
class :: {
}

// class id: 5265, size: 0x14, field offset: 0x14
class HomeView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf9d94, size: 0x398
    // 0xaf9d94: EnterFrame
    //     0xaf9d94: stp             fp, lr, [SP, #-0x10]!
    //     0xaf9d98: mov             fp, SP
    // 0xaf9d9c: AllocStack(0x60)
    //     0xaf9d9c: sub             SP, SP, #0x60
    // 0xaf9da0: SetupParameters(HomeView this /* r1 => r1, fp-0x8 */)
    //     0xaf9da0: stur            x1, [fp, #-8]
    // 0xaf9da4: CheckStackOverflow
    //     0xaf9da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf9da8: cmp             SP, x16
    //     0xaf9dac: b.ls            #0xafa118
    // 0xaf9db0: r1 = 1
    //     0xaf9db0: movz            x1, #0x1
    // 0xaf9db4: r0 = AllocateContext()
    //     0xaf9db4: bl              #0xec126c  ; AllocateContextStub
    // 0xaf9db8: mov             x3, x0
    // 0xaf9dbc: ldur            x0, [fp, #-8]
    // 0xaf9dc0: stur            x3, [fp, #-0x10]
    // 0xaf9dc4: StoreField: r3->field_f = r0
    //     0xaf9dc4: stur            w0, [x3, #0xf]
    // 0xaf9dc8: r1 = _ConstMap len:3
    //     0xaf9dc8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaf9dcc: ldr             x1, [x1, #0xbe8]
    // 0xaf9dd0: r2 = 6
    //     0xaf9dd0: movz            x2, #0x6
    // 0xaf9dd4: r0 = []()
    //     0xaf9dd4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf9dd8: r16 = <Color?>
    //     0xaf9dd8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf9ddc: ldr             x16, [x16, #0x98]
    // 0xaf9de0: stp             x0, x16, [SP, #8]
    // 0xaf9de4: r16 = Instance_MaterialColor
    //     0xaf9de4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xaf9de8: ldr             x16, [x16, #0xe38]
    // 0xaf9dec: str             x16, [SP]
    // 0xaf9df0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf9df0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf9df4: r0 = mode()
    //     0xaf9df4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf9df8: ldur            x1, [fp, #-8]
    // 0xaf9dfc: stur            x0, [fp, #-0x18]
    // 0xaf9e00: r0 = controller()
    //     0xaf9e00: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf9e04: LoadField: r3 = r0->field_3f
    //     0xaf9e04: ldur            w3, [x0, #0x3f]
    // 0xaf9e08: DecompressPointer r3
    //     0xaf9e08: add             x3, x3, HEAP, lsl #32
    // 0xaf9e0c: r16 = Sentinel
    //     0xaf9e0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf9e10: cmp             w3, w16
    // 0xaf9e14: b.eq            #0xafa120
    // 0xaf9e18: stur            x3, [fp, #-0x20]
    // 0xaf9e1c: r1 = _ConstMap len:10
    //     0xaf9e1c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xaf9e20: ldr             x1, [x1, #0xc08]
    // 0xaf9e24: r2 = 1600
    //     0xaf9e24: movz            x2, #0x640
    // 0xaf9e28: r0 = []()
    //     0xaf9e28: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaf9e2c: r16 = <Color?>
    //     0xaf9e2c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xaf9e30: ldr             x16, [x16, #0x98]
    // 0xaf9e34: stp             x0, x16, [SP, #8]
    // 0xaf9e38: r16 = Instance_MaterialColor
    //     0xaf9e38: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xaf9e3c: ldr             x16, [x16, #0xcf0]
    // 0xaf9e40: str             x16, [SP]
    // 0xaf9e44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf9e44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf9e48: r0 = mode()
    //     0xaf9e48: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xaf9e4c: r1 = Function '<anonymous closure>':.
    //     0xaf9e4c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da30] AnonymousClosure: (0xafbdd8), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xaf9e50: ldr             x1, [x1, #0xa30]
    // 0xaf9e54: r2 = Null
    //     0xaf9e54: mov             x2, NULL
    // 0xaf9e58: stur            x0, [fp, #-0x28]
    // 0xaf9e5c: r0 = AllocateClosure()
    //     0xaf9e5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf9e60: r1 = <UpcomingPrayerTimeController>
    //     0xaf9e60: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da38] TypeArguments: <UpcomingPrayerTimeController>
    //     0xaf9e64: ldr             x1, [x1, #0xa38]
    // 0xaf9e68: stur            x0, [fp, #-0x30]
    // 0xaf9e6c: r0 = UpcomingPrayerTimeBuilder()
    //     0xaf9e6c: bl              #0xafa168  ; AllocateUpcomingPrayerTimeBuilderStub -> UpcomingPrayerTimeBuilder (size=0x18)
    // 0xaf9e70: mov             x1, x0
    // 0xaf9e74: ldur            x0, [fp, #-0x30]
    // 0xaf9e78: stur            x1, [fp, #-0x38]
    // 0xaf9e7c: StoreField: r1->field_13 = r0
    //     0xaf9e7c: stur            w0, [x1, #0x13]
    // 0xaf9e80: r0 = AppBar()
    //     0xaf9e80: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaf9e84: stur            x0, [fp, #-0x30]
    // 0xaf9e88: r0 = UniqueKey()
    //     0xaf9e88: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xaf9e8c: r16 = Instance_Color
    //     0xaf9e8c: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xaf9e90: stp             x16, x0, [SP, #0x18]
    // 0xaf9e94: ldur            x16, [fp, #-0x28]
    // 0xaf9e98: r30 = 108.000000
    //     0xaf9e98: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2da40] 108
    //     0xaf9e9c: ldr             lr, [lr, #0xa40]
    // 0xaf9ea0: stp             lr, x16, [SP, #8]
    // 0xaf9ea4: ldur            x16, [fp, #-0x38]
    // 0xaf9ea8: str             x16, [SP]
    // 0xaf9eac: ldur            x1, [fp, #-0x30]
    // 0xaf9eb0: r4 = const [0, 0x6, 0x5, 0x1, backgroundColor, 0x3, key, 0x1, surfaceTintColor, 0x2, title, 0x5, toolbarHeight, 0x4, null]
    //     0xaf9eb0: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2da48] List(15) [0, 0x6, 0x5, 0x1, "backgroundColor", 0x3, "key", 0x1, "surfaceTintColor", 0x2, "title", 0x5, "toolbarHeight", 0x4, Null]
    //     0xaf9eb4: ldr             x4, [x4, #0xa48]
    // 0xaf9eb8: r0 = AppBar()
    //     0xaf9eb8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaf9ebc: r0 = NSlidingAppBar()
    //     0xaf9ebc: bl              #0xafa15c  ; AllocateNSlidingAppBarStub -> NSlidingAppBar (size=0x14)
    // 0xaf9ec0: mov             x2, x0
    // 0xaf9ec4: ldur            x0, [fp, #-0x30]
    // 0xaf9ec8: stur            x2, [fp, #-0x28]
    // 0xaf9ecc: StoreField: r2->field_b = r0
    //     0xaf9ecc: stur            w0, [x2, #0xb]
    // 0xaf9ed0: ldur            x0, [fp, #-0x20]
    // 0xaf9ed4: StoreField: r2->field_f = r0
    //     0xaf9ed4: stur            w0, [x2, #0xf]
    // 0xaf9ed8: ldur            x1, [fp, #-8]
    // 0xaf9edc: r0 = controller()
    //     0xaf9edc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf9ee0: ldur            x1, [fp, #-8]
    // 0xaf9ee4: stur            x0, [fp, #-8]
    // 0xaf9ee8: r0 = controller()
    //     0xaf9ee8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf9eec: r1 = Function '<anonymous closure>':.
    //     0xaf9eec: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da50] AnonymousClosure: (0xafbd74), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xaf9ef0: ldr             x1, [x1, #0xa50]
    // 0xaf9ef4: r2 = Null
    //     0xaf9ef4: mov             x2, NULL
    // 0xaf9ef8: stur            x0, [fp, #-0x20]
    // 0xaf9efc: r0 = AllocateClosure()
    //     0xaf9efc: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf9f00: r1 = <UpcomingPrayerTimeController>
    //     0xaf9f00: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da38] TypeArguments: <UpcomingPrayerTimeController>
    //     0xaf9f04: ldr             x1, [x1, #0xa38]
    // 0xaf9f08: stur            x0, [fp, #-0x30]
    // 0xaf9f0c: r0 = UpcomingPrayerTimeBuilder()
    //     0xaf9f0c: bl              #0xafa168  ; AllocateUpcomingPrayerTimeBuilderStub -> UpcomingPrayerTimeBuilder (size=0x18)
    // 0xaf9f10: mov             x1, x0
    // 0xaf9f14: ldur            x0, [fp, #-0x30]
    // 0xaf9f18: stur            x1, [fp, #-0x38]
    // 0xaf9f1c: StoreField: r1->field_13 = r0
    //     0xaf9f1c: stur            w0, [x1, #0x13]
    // 0xaf9f20: r0 = HomeDomeWidget()
    //     0xaf9f20: bl              #0xafa150  ; AllocateHomeDomeWidgetStub -> HomeDomeWidget (size=0x10)
    // 0xaf9f24: mov             x1, x0
    // 0xaf9f28: ldur            x0, [fp, #-0x38]
    // 0xaf9f2c: stur            x1, [fp, #-0x30]
    // 0xaf9f30: StoreField: r1->field_b = r0
    //     0xaf9f30: stur            w0, [x1, #0xb]
    // 0xaf9f34: r0 = UniqueKey()
    //     0xaf9f34: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xaf9f38: mov             x1, x0
    // 0xaf9f3c: ldur            x0, [fp, #-0x30]
    // 0xaf9f40: StoreField: r0->field_7 = r1
    //     0xaf9f40: stur            w1, [x0, #7]
    // 0xaf9f44: r0 = Obx()
    //     0xaf9f44: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf9f48: ldur            x2, [fp, #-0x10]
    // 0xaf9f4c: r1 = Function '<anonymous closure>':.
    //     0xaf9f4c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da58] AnonymousClosure: (0xafa39c), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xaf9f50: ldr             x1, [x1, #0xa58]
    // 0xaf9f54: stur            x0, [fp, #-0x10]
    // 0xaf9f58: r0 = AllocateClosure()
    //     0xaf9f58: bl              #0xec1630  ; AllocateClosureStub
    // 0xaf9f5c: mov             x1, x0
    // 0xaf9f60: ldur            x0, [fp, #-0x10]
    // 0xaf9f64: StoreField: r0->field_b = r1
    //     0xaf9f64: stur            w1, [x0, #0xb]
    // 0xaf9f68: r1 = Null
    //     0xaf9f68: mov             x1, NULL
    // 0xaf9f6c: r2 = 12
    //     0xaf9f6c: movz            x2, #0xc
    // 0xaf9f70: r0 = AllocateArray()
    //     0xaf9f70: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf9f74: mov             x1, x0
    // 0xaf9f78: ldur            x0, [fp, #-0x30]
    // 0xaf9f7c: stur            x1, [fp, #-0x38]
    // 0xaf9f80: StoreField: r1->field_f = r0
    //     0xaf9f80: stur            w0, [x1, #0xf]
    // 0xaf9f84: r0 = HomeMenusWidget()
    //     0xaf9f84: bl              #0xafa144  ; AllocateHomeMenusWidgetStub -> HomeMenusWidget (size=0xc)
    // 0xaf9f88: mov             x1, x0
    // 0xaf9f8c: ldur            x0, [fp, #-0x38]
    // 0xaf9f90: StoreField: r0->field_13 = r1
    //     0xaf9f90: stur            w1, [x0, #0x13]
    // 0xaf9f94: r0 = HomeSearchWidget()
    //     0xaf9f94: bl              #0xafa138  ; AllocateHomeSearchWidgetStub -> HomeSearchWidget (size=0xc)
    // 0xaf9f98: mov             x1, x0
    // 0xaf9f9c: ldur            x0, [fp, #-0x38]
    // 0xaf9fa0: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf9fa0: stur            w1, [x0, #0x17]
    // 0xaf9fa4: r16 = Instance_SizedBox
    //     0xaf9fa4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xaf9fa8: ldr             x16, [x16, #0xfb0]
    // 0xaf9fac: StoreField: r0->field_1b = r16
    //     0xaf9fac: stur            w16, [x0, #0x1b]
    // 0xaf9fb0: r1 = <CountdownController>
    //     0xaf9fb0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da60] TypeArguments: <CountdownController>
    //     0xaf9fb4: ldr             x1, [x1, #0xa60]
    // 0xaf9fb8: r0 = HomeGeneralCountdown()
    //     0xaf9fb8: bl              #0xafa12c  ; AllocateHomeGeneralCountdownStub -> HomeGeneralCountdown (size=0x14)
    // 0xaf9fbc: mov             x1, x0
    // 0xaf9fc0: ldur            x0, [fp, #-0x38]
    // 0xaf9fc4: StoreField: r0->field_1f = r1
    //     0xaf9fc4: stur            w1, [x0, #0x1f]
    // 0xaf9fc8: ldur            x1, [fp, #-0x10]
    // 0xaf9fcc: StoreField: r0->field_23 = r1
    //     0xaf9fcc: stur            w1, [x0, #0x23]
    // 0xaf9fd0: r1 = <Widget>
    //     0xaf9fd0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xaf9fd4: r0 = AllocateGrowableArray()
    //     0xaf9fd4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaf9fd8: mov             x1, x0
    // 0xaf9fdc: ldur            x0, [fp, #-0x38]
    // 0xaf9fe0: stur            x1, [fp, #-0x10]
    // 0xaf9fe4: StoreField: r1->field_f = r0
    //     0xaf9fe4: stur            w0, [x1, #0xf]
    // 0xaf9fe8: r0 = 12
    //     0xaf9fe8: movz            x0, #0xc
    // 0xaf9fec: StoreField: r1->field_b = r0
    //     0xaf9fec: stur            w0, [x1, #0xb]
    // 0xaf9ff0: r0 = ListView()
    //     0xaf9ff0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaf9ff4: stur            x0, [fp, #-0x30]
    // 0xaf9ff8: r16 = Instance_EdgeInsets
    //     0xaf9ff8: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaf9ffc: r30 = Instance_ClampingScrollPhysics
    //     0xaf9ffc: add             lr, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xafa000: ldr             lr, [lr, #0x410]
    // 0xafa004: stp             lr, x16, [SP]
    // 0xafa008: mov             x1, x0
    // 0xafa00c: ldur            x2, [fp, #-0x10]
    // 0xafa010: r4 = const [0, 0x4, 0x2, 0x2, padding, 0x2, physics, 0x3, null]
    //     0xafa010: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2da68] List(9) [0, 0x4, 0x2, 0x2, "padding", 0x2, "physics", 0x3, Null]
    //     0xafa014: ldr             x4, [x4, #0xa68]
    // 0xafa018: r0 = ListView()
    //     0xafa018: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xafa01c: ldur            x2, [fp, #-0x20]
    // 0xafa020: r1 = Function 'onScrollNotificationUpdated':.
    //     0xafa020: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da70] AnonymousClosure: (0xafa21c), in [package:nuonline/app/modules/home/<USER>/home_controller.dart] HomeController::onScrollNotificationUpdated (0xafa258)
    //     0xafa024: ldr             x1, [x1, #0xa70]
    // 0xafa028: r0 = AllocateClosure()
    //     0xafa028: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa02c: r1 = <ScrollUpdateNotification>
    //     0xafa02c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da78] TypeArguments: <ScrollUpdateNotification>
    //     0xafa030: ldr             x1, [x1, #0xa78]
    // 0xafa034: stur            x0, [fp, #-0x10]
    // 0xafa038: r0 = NotificationListener()
    //     0xafa038: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xafa03c: mov             x1, x0
    // 0xafa040: ldur            x0, [fp, #-0x10]
    // 0xafa044: stur            x1, [fp, #-0x20]
    // 0xafa048: StoreField: r1->field_13 = r0
    //     0xafa048: stur            w0, [x1, #0x13]
    // 0xafa04c: ldur            x0, [fp, #-0x30]
    // 0xafa050: StoreField: r1->field_b = r0
    //     0xafa050: stur            w0, [x1, #0xb]
    // 0xafa054: r0 = RefreshIndicator()
    //     0xafa054: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xafa058: mov             x3, x0
    // 0xafa05c: ldur            x0, [fp, #-0x20]
    // 0xafa060: stur            x3, [fp, #-0x10]
    // 0xafa064: StoreField: r3->field_b = r0
    //     0xafa064: stur            w0, [x3, #0xb]
    // 0xafa068: d0 = 40.000000
    //     0xafa068: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xafa06c: StoreField: r3->field_f = d0
    //     0xafa06c: stur            d0, [x3, #0xf]
    // 0xafa070: ArrayStore: r3[0] = rZR  ; List_8
    //     0xafa070: stur            xzr, [x3, #0x17]
    // 0xafa074: ldur            x2, [fp, #-8]
    // 0xafa078: r1 = Function 'onRefresh':.
    //     0xafa078: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da80] AnonymousClosure: (0xafa174), in [package:nuonline/app/modules/home/<USER>/home_controller.dart] HomeController::onRefresh (0xafa1ac)
    //     0xafa07c: ldr             x1, [x1, #0xa80]
    // 0xafa080: r0 = AllocateClosure()
    //     0xafa080: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa084: mov             x1, x0
    // 0xafa088: ldur            x0, [fp, #-0x10]
    // 0xafa08c: StoreField: r0->field_1f = r1
    //     0xafa08c: stur            w1, [x0, #0x1f]
    // 0xafa090: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xafa090: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xafa094: ldr             x1, [x1, #0xf58]
    // 0xafa098: StoreField: r0->field_2f = r1
    //     0xafa098: stur            w1, [x0, #0x2f]
    // 0xafa09c: d0 = 2.500000
    //     0xafa09c: fmov            d0, #2.50000000
    // 0xafa0a0: StoreField: r0->field_3b = d0
    //     0xafa0a0: stur            d0, [x0, #0x3b]
    // 0xafa0a4: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xafa0a4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xafa0a8: ldr             x1, [x1, #0xa68]
    // 0xafa0ac: StoreField: r0->field_47 = r1
    //     0xafa0ac: stur            w1, [x0, #0x47]
    // 0xafa0b0: d0 = 2.000000
    //     0xafa0b0: fmov            d0, #2.00000000
    // 0xafa0b4: StoreField: r0->field_4b = d0
    //     0xafa0b4: stur            d0, [x0, #0x4b]
    // 0xafa0b8: r1 = Instance__IndicatorType
    //     0xafa0b8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xafa0bc: ldr             x1, [x1, #0xa70]
    // 0xafa0c0: StoreField: r0->field_43 = r1
    //     0xafa0c0: stur            w1, [x0, #0x43]
    // 0xafa0c4: r0 = Scaffold()
    //     0xafa0c4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xafa0c8: ldur            x1, [fp, #-0x28]
    // 0xafa0cc: StoreField: r0->field_13 = r1
    //     0xafa0cc: stur            w1, [x0, #0x13]
    // 0xafa0d0: ldur            x1, [fp, #-0x10]
    // 0xafa0d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xafa0d4: stur            w1, [x0, #0x17]
    // 0xafa0d8: r1 = Instance_AlignmentDirectional
    //     0xafa0d8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xafa0dc: ldr             x1, [x1, #0x758]
    // 0xafa0e0: StoreField: r0->field_2b = r1
    //     0xafa0e0: stur            w1, [x0, #0x2b]
    // 0xafa0e4: ldur            x1, [fp, #-0x18]
    // 0xafa0e8: StoreField: r0->field_43 = r1
    //     0xafa0e8: stur            w1, [x0, #0x43]
    // 0xafa0ec: r1 = true
    //     0xafa0ec: add             x1, NULL, #0x20  ; true
    // 0xafa0f0: StoreField: r0->field_53 = r1
    //     0xafa0f0: stur            w1, [x0, #0x53]
    // 0xafa0f4: r2 = Instance_DragStartBehavior
    //     0xafa0f4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xafa0f8: StoreField: r0->field_57 = r2
    //     0xafa0f8: stur            w2, [x0, #0x57]
    // 0xafa0fc: StoreField: r0->field_b = r1
    //     0xafa0fc: stur            w1, [x0, #0xb]
    // 0xafa100: StoreField: r0->field_f = r1
    //     0xafa100: stur            w1, [x0, #0xf]
    // 0xafa104: StoreField: r0->field_5f = r1
    //     0xafa104: stur            w1, [x0, #0x5f]
    // 0xafa108: StoreField: r0->field_63 = r1
    //     0xafa108: stur            w1, [x0, #0x63]
    // 0xafa10c: LeaveFrame
    //     0xafa10c: mov             SP, fp
    //     0xafa110: ldp             fp, lr, [SP], #0x10
    // 0xafa114: ret
    //     0xafa114: ret             
    // 0xafa118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafa118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafa11c: b               #0xaf9db0
    // 0xafa120: r9 = controller
    //     0xafa120: add             x9, PP, #0x2d, lsl #12  ; [pp+0x2da88] Field <HomeController.controller>: late (offset: 0x40)
    //     0xafa124: ldr             x9, [x9, #0xa88]
    // 0xafa128: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xafa128: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xafa39c, size: 0xdd0
    // 0xafa39c: EnterFrame
    //     0xafa39c: stp             fp, lr, [SP, #-0x10]!
    //     0xafa3a0: mov             fp, SP
    // 0xafa3a4: AllocStack(0x60)
    //     0xafa3a4: sub             SP, SP, #0x60
    // 0xafa3a8: SetupParameters()
    //     0xafa3a8: ldr             x0, [fp, #0x10]
    //     0xafa3ac: ldur            w3, [x0, #0x17]
    //     0xafa3b0: add             x3, x3, HEAP, lsl #32
    //     0xafa3b4: stur            x3, [fp, #-8]
    // 0xafa3b8: CheckStackOverflow
    //     0xafa3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafa3bc: cmp             SP, x16
    //     0xafa3c0: b.ls            #0xafb14c
    // 0xafa3c4: r1 = <Widget>
    //     0xafa3c4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xafa3c8: r2 = 0
    //     0xafa3c8: movz            x2, #0
    // 0xafa3cc: r0 = _GrowableList()
    //     0xafa3cc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xafa3d0: mov             x2, x0
    // 0xafa3d4: ldur            x0, [fp, #-8]
    // 0xafa3d8: stur            x2, [fp, #-0x10]
    // 0xafa3dc: LoadField: r1 = r0->field_f
    //     0xafa3dc: ldur            w1, [x0, #0xf]
    // 0xafa3e0: DecompressPointer r1
    //     0xafa3e0: add             x1, x1, HEAP, lsl #32
    // 0xafa3e4: r0 = controller()
    //     0xafa3e4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xafa3e8: LoadField: r1 = r0->field_43
    //     0xafa3e8: ldur            w1, [x0, #0x43]
    // 0xafa3ec: DecompressPointer r1
    //     0xafa3ec: add             x1, x1, HEAP, lsl #32
    // 0xafa3f0: r0 = value()
    //     0xafa3f0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xafa3f4: r1 = LoadClassIdInstr(r0)
    //     0xafa3f4: ldur            x1, [x0, #-1]
    //     0xafa3f8: ubfx            x1, x1, #0xc, #0x14
    // 0xafa3fc: mov             x16, x0
    // 0xafa400: mov             x0, x1
    // 0xafa404: mov             x1, x16
    // 0xafa408: r0 = GDT[cid_x0 + 0xe879]()
    //     0xafa408: movz            x17, #0xe879
    //     0xafa40c: add             lr, x0, x17
    //     0xafa410: ldr             lr, [x21, lr, lsl #3]
    //     0xafa414: blr             lr
    // 0xafa418: tbnz            w0, #4, #0xafa508
    // 0xafa41c: r0 = Summary()
    //     0xafa41c: bl              #0x91b5e0  ; AllocateSummaryStub -> Summary (size=0x3c)
    // 0xafa420: mov             x1, x0
    // 0xafa424: r2 = "[]"
    //     0xafa424: add             x2, PP, #0x1e, lsl #12  ; [pp+0x1e228] "[]"
    //     0xafa428: ldr             x2, [x2, #0x228]
    // 0xafa42c: r3 = ""
    //     0xafa42c: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xafa430: r5 = 0
    //     0xafa430: movz            x5, #0
    // 0xafa434: r6 = ""
    //     0xafa434: ldr             x6, [PP, #0x288]  ; [pp+0x288] ""
    // 0xafa438: stur            x0, [fp, #-0x18]
    // 0xafa43c: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xafa43c: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xafa440: r0 = Summary()
    //     0xafa440: bl              #0x91b2c0  ; [package:nuonline/app/data/models/summary.dart] Summary::Summary
    // 0xafa444: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafa444: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafa448: ldr             x0, [x0, #0x2670]
    //     0xafa44c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafa450: cmp             w0, w16
    //     0xafa454: b.ne            #0xafa460
    //     0xafa458: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xafa45c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xafa460: r0 = GetNavigation.theme()
    //     0xafa460: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xafa464: LoadField: r2 = r0->field_37
    //     0xafa464: ldur            w2, [x0, #0x37]
    // 0xafa468: DecompressPointer r2
    //     0xafa468: add             x2, x2, HEAP, lsl #32
    // 0xafa46c: stur            x2, [fp, #-0x20]
    // 0xafa470: r1 = <Article>
    //     0xafa470: ldr             x1, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0xafa474: r0 = HomeSummaryWidget()
    //     0xafa474: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafa478: mov             x3, x0
    // 0xafa47c: ldur            x0, [fp, #-0x18]
    // 0xafa480: stur            x3, [fp, #-0x28]
    // 0xafa484: StoreField: r3->field_f = r0
    //     0xafa484: stur            w0, [x3, #0xf]
    // 0xafa488: r1 = Function '<anonymous closure>':.
    //     0xafa488: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc58] AnonymousClosure: (0xafbcc0), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa48c: ldr             x1, [x1, #0xc58]
    // 0xafa490: r2 = Null
    //     0xafa490: mov             x2, NULL
    // 0xafa494: r0 = AllocateClosure()
    //     0xafa494: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa498: mov             x1, x0
    // 0xafa49c: ldur            x0, [fp, #-0x28]
    // 0xafa4a0: StoreField: r0->field_13 = r1
    //     0xafa4a0: stur            w1, [x0, #0x13]
    // 0xafa4a4: r1 = Function '<anonymous closure>':.
    //     0xafa4a4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc60] AnonymousClosure: (0xafbcf4), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa4a8: ldr             x1, [x1, #0xc60]
    // 0xafa4ac: r2 = Null
    //     0xafa4ac: mov             x2, NULL
    // 0xafa4b0: r0 = AllocateClosure()
    //     0xafa4b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa4b4: mov             x1, x0
    // 0xafa4b8: ldur            x0, [fp, #-0x28]
    // 0xafa4bc: StoreField: r0->field_1b = r1
    //     0xafa4bc: stur            w1, [x0, #0x1b]
    // 0xafa4c0: ldur            x1, [fp, #-0x20]
    // 0xafa4c4: StoreField: r0->field_23 = r1
    //     0xafa4c4: stur            w1, [x0, #0x23]
    // 0xafa4c8: r2 = Instance_Divider
    //     0xafa4c8: add             x2, PP, #0x29, lsl #12  ; [pp+0x29190] Obj!Divider@e25781
    //     0xafa4cc: ldr             x2, [x2, #0x190]
    // 0xafa4d0: StoreField: r0->field_27 = r2
    //     0xafa4d0: stur            w2, [x0, #0x27]
    // 0xafa4d4: r3 = Instance_EdgeInsets
    //     0xafa4d4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc68] Obj!EdgeInsets@e12971
    //     0xafa4d8: ldr             x3, [x3, #0xc68]
    // 0xafa4dc: StoreField: r0->field_2b = r3
    //     0xafa4dc: stur            w3, [x0, #0x2b]
    // 0xafa4e0: r4 = Instance_EdgeInsets
    //     0xafa4e0: ldr             x4, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xafa4e4: StoreField: r0->field_2f = r4
    //     0xafa4e4: stur            w4, [x0, #0x2f]
    // 0xafa4e8: r1 = false
    //     0xafa4e8: add             x1, NULL, #0x30  ; false
    // 0xafa4ec: StoreField: r0->field_33 = r1
    //     0xafa4ec: stur            w1, [x0, #0x33]
    // 0xafa4f0: r5 = const []
    //     0xafa4f0: add             x5, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafa4f4: ldr             x5, [x5, #0x148]
    // 0xafa4f8: StoreField: r0->field_37 = r5
    //     0xafa4f8: stur            w5, [x0, #0x37]
    // 0xafa4fc: LeaveFrame
    //     0xafa4fc: mov             SP, fp
    //     0xafa500: ldp             fp, lr, [SP], #0x10
    // 0xafa504: ret
    //     0xafa504: ret             
    // 0xafa508: ldur            x0, [fp, #-8]
    // 0xafa50c: r4 = Instance_EdgeInsets
    //     0xafa50c: ldr             x4, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xafa510: r3 = Instance_EdgeInsets
    //     0xafa510: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc68] Obj!EdgeInsets@e12971
    //     0xafa514: ldr             x3, [x3, #0xc68]
    // 0xafa518: r5 = const []
    //     0xafa518: add             x5, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafa51c: ldr             x5, [x5, #0x148]
    // 0xafa520: r2 = Instance_Divider
    //     0xafa520: add             x2, PP, #0x29, lsl #12  ; [pp+0x29190] Obj!Divider@e25781
    //     0xafa524: ldr             x2, [x2, #0x190]
    // 0xafa528: LoadField: r1 = r0->field_f
    //     0xafa528: ldur            w1, [x0, #0xf]
    // 0xafa52c: DecompressPointer r1
    //     0xafa52c: add             x1, x1, HEAP, lsl #32
    // 0xafa530: r0 = controller()
    //     0xafa530: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xafa534: mov             x1, x0
    // 0xafa538: r0 = summaries()
    //     0xafa538: bl              #0x8a6828  ; [package:nuonline/app/modules/home/<USER>/home_controller.dart] HomeController::summaries
    // 0xafa53c: r1 = LoadClassIdInstr(r0)
    //     0xafa53c: ldur            x1, [x0, #-1]
    //     0xafa540: ubfx            x1, x1, #0xc, #0x14
    // 0xafa544: mov             x16, x0
    // 0xafa548: mov             x0, x1
    // 0xafa54c: mov             x1, x16
    // 0xafa550: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xafa550: movz            x17, #0xd35d
    //     0xafa554: add             lr, x0, x17
    //     0xafa558: ldr             lr, [x21, lr, lsl #3]
    //     0xafa55c: blr             lr
    // 0xafa560: mov             x2, x0
    // 0xafa564: stur            x2, [fp, #-0x18]
    // 0xafa568: ldur            x4, [fp, #-0x10]
    // 0xafa56c: ldur            x3, [fp, #-8]
    // 0xafa570: CheckStackOverflow
    //     0xafa570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafa574: cmp             SP, x16
    //     0xafa578: b.ls            #0xafb154
    // 0xafa57c: r0 = LoadClassIdInstr(r2)
    //     0xafa57c: ldur            x0, [x2, #-1]
    //     0xafa580: ubfx            x0, x0, #0xc, #0x14
    // 0xafa584: mov             x1, x2
    // 0xafa588: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xafa588: movz            x17, #0x292d
    //     0xafa58c: movk            x17, #0x1, lsl #16
    //     0xafa590: add             lr, x0, x17
    //     0xafa594: ldr             lr, [x21, lr, lsl #3]
    //     0xafa598: blr             lr
    // 0xafa59c: tbnz            w0, #4, #0xafb038
    // 0xafa5a0: ldur            x0, [fp, #-8]
    // 0xafa5a4: ldur            x1, [fp, #-0x18]
    // 0xafa5a8: r1 = 1
    //     0xafa5a8: movz            x1, #0x1
    // 0xafa5ac: r0 = AllocateContext()
    //     0xafa5ac: bl              #0xec126c  ; AllocateContextStub
    // 0xafa5b0: mov             x3, x0
    // 0xafa5b4: ldur            x2, [fp, #-8]
    // 0xafa5b8: stur            x3, [fp, #-0x20]
    // 0xafa5bc: StoreField: r3->field_b = r2
    //     0xafa5bc: stur            w2, [x3, #0xb]
    // 0xafa5c0: ldur            x4, [fp, #-0x18]
    // 0xafa5c4: r0 = LoadClassIdInstr(r4)
    //     0xafa5c4: ldur            x0, [x4, #-1]
    //     0xafa5c8: ubfx            x0, x0, #0xc, #0x14
    // 0xafa5cc: mov             x1, x4
    // 0xafa5d0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xafa5d0: movz            x17, #0x384d
    //     0xafa5d4: movk            x17, #0x1, lsl #16
    //     0xafa5d8: add             lr, x0, x17
    //     0xafa5dc: ldr             lr, [x21, lr, lsl #3]
    //     0xafa5e0: blr             lr
    // 0xafa5e4: mov             x1, x0
    // 0xafa5e8: ldur            x2, [fp, #-0x20]
    // 0xafa5ec: stur            x1, [fp, #-0x28]
    // 0xafa5f0: StoreField: r2->field_f = r0
    //     0xafa5f0: stur            w0, [x2, #0xf]
    //     0xafa5f4: ldurb           w16, [x2, #-1]
    //     0xafa5f8: ldurb           w17, [x0, #-1]
    //     0xafa5fc: and             x16, x17, x16, lsr #2
    //     0xafa600: tst             x16, HEAP, lsr #32
    //     0xafa604: b.eq            #0xafa60c
    //     0xafa608: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xafa60c: LoadField: r0 = r1->field_1f
    //     0xafa60c: ldur            w0, [x1, #0x1f]
    // 0xafa610: DecompressPointer r0
    //     0xafa610: add             x0, x0, HEAP, lsl #32
    // 0xafa614: r3 = LoadClassIdInstr(r0)
    //     0xafa614: ldur            x3, [x0, #-1]
    //     0xafa618: ubfx            x3, x3, #0xc, #0x14
    // 0xafa61c: r16 = "article"
    //     0xafa61c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ed8] "article"
    //     0xafa620: ldr             x16, [x16, #0xed8]
    // 0xafa624: stp             x16, x0, [SP]
    // 0xafa628: mov             x0, x3
    // 0xafa62c: mov             lr, x0
    // 0xafa630: ldr             lr, [x21, lr, lsl #3]
    // 0xafa634: blr             lr
    // 0xafa638: tbnz            w0, #4, #0xafa7b8
    // 0xafa63c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafa63c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafa640: ldr             x0, [x0, #0x2670]
    //     0xafa644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafa648: cmp             w0, w16
    //     0xafa64c: b.ne            #0xafa658
    //     0xafa650: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xafa654: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xafa658: r16 = Instance_Brightness
    //     0xafa658: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xafa65c: stp             NULL, x16, [SP]
    // 0xafa660: r1 = Null
    //     0xafa660: mov             x1, NULL
    // 0xafa664: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xafa664: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xafa668: r0 = ThemeData()
    //     0xafa668: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xafa66c: stur            x0, [fp, #-0x30]
    // 0xafa670: r0 = GetNavigation.context()
    //     0xafa670: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafa674: cmp             w0, NULL
    // 0xafa678: b.eq            #0xafa698
    // 0xafa67c: r0 = GetNavigation.context()
    //     0xafa67c: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafa680: cmp             w0, NULL
    // 0xafa684: b.eq            #0xafb15c
    // 0xafa688: mov             x1, x0
    // 0xafa68c: r0 = of()
    //     0xafa68c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa690: mov             x1, x0
    // 0xafa694: b               #0xafa69c
    // 0xafa698: ldur            x1, [fp, #-0x30]
    // 0xafa69c: ldur            x2, [fp, #-0x10]
    // 0xafa6a0: ldur            x0, [fp, #-0x28]
    // 0xafa6a4: LoadField: r3 = r1->field_37
    //     0xafa6a4: ldur            w3, [x1, #0x37]
    // 0xafa6a8: DecompressPointer r3
    //     0xafa6a8: add             x3, x3, HEAP, lsl #32
    // 0xafa6ac: stur            x3, [fp, #-0x30]
    // 0xafa6b0: r1 = <Article>
    //     0xafa6b0: ldr             x1, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0xafa6b4: r0 = HomeSummaryWidget()
    //     0xafa6b4: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafa6b8: mov             x3, x0
    // 0xafa6bc: ldur            x0, [fp, #-0x28]
    // 0xafa6c0: stur            x3, [fp, #-0x38]
    // 0xafa6c4: StoreField: r3->field_f = r0
    //     0xafa6c4: stur            w0, [x3, #0xf]
    // 0xafa6c8: r1 = Function '<anonymous closure>':.
    //     0xafa6c8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc70] AnonymousClosure: (0xafbcc0), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa6cc: ldr             x1, [x1, #0xc70]
    // 0xafa6d0: r2 = Null
    //     0xafa6d0: mov             x2, NULL
    // 0xafa6d4: r0 = AllocateClosure()
    //     0xafa6d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa6d8: mov             x1, x0
    // 0xafa6dc: ldur            x0, [fp, #-0x38]
    // 0xafa6e0: StoreField: r0->field_13 = r1
    //     0xafa6e0: stur            w1, [x0, #0x13]
    // 0xafa6e4: r1 = Function '<anonymous closure>':.
    //     0xafa6e4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc78] AnonymousClosure: (0xafbc94), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa6e8: ldr             x1, [x1, #0xc78]
    // 0xafa6ec: r2 = Null
    //     0xafa6ec: mov             x2, NULL
    // 0xafa6f0: r0 = AllocateClosure()
    //     0xafa6f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa6f4: mov             x1, x0
    // 0xafa6f8: ldur            x0, [fp, #-0x38]
    // 0xafa6fc: StoreField: r0->field_1b = r1
    //     0xafa6fc: stur            w1, [x0, #0x1b]
    // 0xafa700: ldur            x1, [fp, #-0x30]
    // 0xafa704: StoreField: r0->field_23 = r1
    //     0xafa704: stur            w1, [x0, #0x23]
    // 0xafa708: r2 = Instance_Divider
    //     0xafa708: add             x2, PP, #0x29, lsl #12  ; [pp+0x29190] Obj!Divider@e25781
    //     0xafa70c: ldr             x2, [x2, #0x190]
    // 0xafa710: StoreField: r0->field_27 = r2
    //     0xafa710: stur            w2, [x0, #0x27]
    // 0xafa714: r3 = Instance_EdgeInsets
    //     0xafa714: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc68] Obj!EdgeInsets@e12971
    //     0xafa718: ldr             x3, [x3, #0xc68]
    // 0xafa71c: StoreField: r0->field_2b = r3
    //     0xafa71c: stur            w3, [x0, #0x2b]
    // 0xafa720: r4 = Instance_EdgeInsets
    //     0xafa720: ldr             x4, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xafa724: StoreField: r0->field_2f = r4
    //     0xafa724: stur            w4, [x0, #0x2f]
    // 0xafa728: r5 = true
    //     0xafa728: add             x5, NULL, #0x20  ; true
    // 0xafa72c: StoreField: r0->field_33 = r5
    //     0xafa72c: stur            w5, [x0, #0x33]
    // 0xafa730: r6 = const []
    //     0xafa730: add             x6, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafa734: ldr             x6, [x6, #0x148]
    // 0xafa738: StoreField: r0->field_37 = r6
    //     0xafa738: stur            w6, [x0, #0x37]
    // 0xafa73c: ldur            x7, [fp, #-0x10]
    // 0xafa740: LoadField: r1 = r7->field_b
    //     0xafa740: ldur            w1, [x7, #0xb]
    // 0xafa744: LoadField: r8 = r7->field_f
    //     0xafa744: ldur            w8, [x7, #0xf]
    // 0xafa748: DecompressPointer r8
    //     0xafa748: add             x8, x8, HEAP, lsl #32
    // 0xafa74c: LoadField: r9 = r8->field_b
    //     0xafa74c: ldur            w9, [x8, #0xb]
    // 0xafa750: r8 = LoadInt32Instr(r1)
    //     0xafa750: sbfx            x8, x1, #1, #0x1f
    // 0xafa754: stur            x8, [fp, #-0x40]
    // 0xafa758: r1 = LoadInt32Instr(r9)
    //     0xafa758: sbfx            x1, x9, #1, #0x1f
    // 0xafa75c: cmp             x8, x1
    // 0xafa760: b.ne            #0xafa76c
    // 0xafa764: mov             x1, x7
    // 0xafa768: r0 = _growToNextCapacity()
    //     0xafa768: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafa76c: ldur            x2, [fp, #-0x10]
    // 0xafa770: ldur            x3, [fp, #-0x40]
    // 0xafa774: add             x0, x3, #1
    // 0xafa778: lsl             x1, x0, #1
    // 0xafa77c: StoreField: r2->field_b = r1
    //     0xafa77c: stur            w1, [x2, #0xb]
    // 0xafa780: LoadField: r1 = r2->field_f
    //     0xafa780: ldur            w1, [x2, #0xf]
    // 0xafa784: DecompressPointer r1
    //     0xafa784: add             x1, x1, HEAP, lsl #32
    // 0xafa788: ldur            x0, [fp, #-0x38]
    // 0xafa78c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafa78c: add             x25, x1, x3, lsl #2
    //     0xafa790: add             x25, x25, #0xf
    //     0xafa794: str             w0, [x25]
    //     0xafa798: tbz             w0, #0, #0xafa7b4
    //     0xafa79c: ldurb           w16, [x1, #-1]
    //     0xafa7a0: ldurb           w17, [x0, #-1]
    //     0xafa7a4: and             x16, x17, x16, lsr #2
    //     0xafa7a8: tst             x16, HEAP, lsr #32
    //     0xafa7ac: b.eq            #0xafa7b4
    //     0xafa7b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafa7b4: b               #0xafa7bc
    // 0xafa7b8: ldur            x2, [fp, #-0x10]
    // 0xafa7bc: ldur            x1, [fp, #-0x28]
    // 0xafa7c0: LoadField: r0 = r1->field_1f
    //     0xafa7c0: ldur            w0, [x1, #0x1f]
    // 0xafa7c4: DecompressPointer r0
    //     0xafa7c4: add             x0, x0, HEAP, lsl #32
    // 0xafa7c8: r3 = LoadClassIdInstr(r0)
    //     0xafa7c8: ldur            x3, [x0, #-1]
    //     0xafa7cc: ubfx            x3, x3, #0xc, #0x14
    // 0xafa7d0: r16 = "topic"
    //     0xafa7d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x124a8] "topic"
    //     0xafa7d4: ldr             x16, [x16, #0x4a8]
    // 0xafa7d8: stp             x16, x0, [SP]
    // 0xafa7dc: mov             x0, x3
    // 0xafa7e0: mov             lr, x0
    // 0xafa7e4: ldr             lr, [x21, lr, lsl #3]
    // 0xafa7e8: blr             lr
    // 0xafa7ec: tbnz            w0, #4, #0xafa900
    // 0xafa7f0: ldur            x0, [fp, #-0x10]
    // 0xafa7f4: ldur            x2, [fp, #-0x28]
    // 0xafa7f8: r1 = <Topic>
    //     0xafa7f8: add             x1, PP, #0x29, lsl #12  ; [pp+0x299a8] TypeArguments: <Topic>
    //     0xafa7fc: ldr             x1, [x1, #0x9a8]
    // 0xafa800: r0 = HomeSummaryWidget()
    //     0xafa800: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafa804: mov             x3, x0
    // 0xafa808: ldur            x0, [fp, #-0x28]
    // 0xafa80c: stur            x3, [fp, #-0x30]
    // 0xafa810: StoreField: r3->field_f = r0
    //     0xafa810: stur            w0, [x3, #0xf]
    // 0xafa814: r1 = Function '<anonymous closure>':.
    //     0xafa814: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc80] AnonymousClosure: (0xafba7c), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa818: ldr             x1, [x1, #0xc80]
    // 0xafa81c: r2 = Null
    //     0xafa81c: mov             x2, NULL
    // 0xafa820: r0 = AllocateClosure()
    //     0xafa820: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa824: mov             x1, x0
    // 0xafa828: ldur            x0, [fp, #-0x30]
    // 0xafa82c: StoreField: r0->field_13 = r1
    //     0xafa82c: stur            w1, [x0, #0x13]
    // 0xafa830: r1 = Function '<anonymous closure>':.
    //     0xafa830: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc88] AnonymousClosure: (0xafb9a8), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa834: ldr             x1, [x1, #0xc88]
    // 0xafa838: r2 = Null
    //     0xafa838: mov             x2, NULL
    // 0xafa83c: r0 = AllocateClosure()
    //     0xafa83c: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa840: mov             x1, x0
    // 0xafa844: ldur            x0, [fp, #-0x30]
    // 0xafa848: StoreField: r0->field_1b = r1
    //     0xafa848: stur            w1, [x0, #0x1b]
    // 0xafa84c: r2 = Instance_SizedBox
    //     0xafa84c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafa850: ldr             x2, [x2, #0xc40]
    // 0xafa854: StoreField: r0->field_27 = r2
    //     0xafa854: stur            w2, [x0, #0x27]
    // 0xafa858: r3 = Instance_EdgeInsets
    //     0xafa858: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc90] Obj!EdgeInsets@e12941
    //     0xafa85c: ldr             x3, [x3, #0xc90]
    // 0xafa860: StoreField: r0->field_2b = r3
    //     0xafa860: stur            w3, [x0, #0x2b]
    // 0xafa864: r4 = Instance_EdgeInsets
    //     0xafa864: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dc98] Obj!EdgeInsets@e12581
    //     0xafa868: ldr             x4, [x4, #0xc98]
    // 0xafa86c: StoreField: r0->field_2f = r4
    //     0xafa86c: stur            w4, [x0, #0x2f]
    // 0xafa870: r5 = true
    //     0xafa870: add             x5, NULL, #0x20  ; true
    // 0xafa874: StoreField: r0->field_33 = r5
    //     0xafa874: stur            w5, [x0, #0x33]
    // 0xafa878: r6 = const []
    //     0xafa878: add             x6, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafa87c: ldr             x6, [x6, #0x148]
    // 0xafa880: StoreField: r0->field_37 = r6
    //     0xafa880: stur            w6, [x0, #0x37]
    // 0xafa884: ldur            x7, [fp, #-0x10]
    // 0xafa888: LoadField: r1 = r7->field_b
    //     0xafa888: ldur            w1, [x7, #0xb]
    // 0xafa88c: LoadField: r8 = r7->field_f
    //     0xafa88c: ldur            w8, [x7, #0xf]
    // 0xafa890: DecompressPointer r8
    //     0xafa890: add             x8, x8, HEAP, lsl #32
    // 0xafa894: LoadField: r9 = r8->field_b
    //     0xafa894: ldur            w9, [x8, #0xb]
    // 0xafa898: r8 = LoadInt32Instr(r1)
    //     0xafa898: sbfx            x8, x1, #1, #0x1f
    // 0xafa89c: stur            x8, [fp, #-0x40]
    // 0xafa8a0: r1 = LoadInt32Instr(r9)
    //     0xafa8a0: sbfx            x1, x9, #1, #0x1f
    // 0xafa8a4: cmp             x8, x1
    // 0xafa8a8: b.ne            #0xafa8b4
    // 0xafa8ac: mov             x1, x7
    // 0xafa8b0: r0 = _growToNextCapacity()
    //     0xafa8b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafa8b4: ldur            x2, [fp, #-0x10]
    // 0xafa8b8: ldur            x3, [fp, #-0x40]
    // 0xafa8bc: add             x0, x3, #1
    // 0xafa8c0: lsl             x1, x0, #1
    // 0xafa8c4: StoreField: r2->field_b = r1
    //     0xafa8c4: stur            w1, [x2, #0xb]
    // 0xafa8c8: LoadField: r1 = r2->field_f
    //     0xafa8c8: ldur            w1, [x2, #0xf]
    // 0xafa8cc: DecompressPointer r1
    //     0xafa8cc: add             x1, x1, HEAP, lsl #32
    // 0xafa8d0: ldur            x0, [fp, #-0x30]
    // 0xafa8d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafa8d4: add             x25, x1, x3, lsl #2
    //     0xafa8d8: add             x25, x25, #0xf
    //     0xafa8dc: str             w0, [x25]
    //     0xafa8e0: tbz             w0, #0, #0xafa8fc
    //     0xafa8e4: ldurb           w16, [x1, #-1]
    //     0xafa8e8: ldurb           w17, [x0, #-1]
    //     0xafa8ec: and             x16, x17, x16, lsr #2
    //     0xafa8f0: tst             x16, HEAP, lsr #32
    //     0xafa8f4: b.eq            #0xafa8fc
    //     0xafa8f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafa8fc: b               #0xafa904
    // 0xafa900: ldur            x2, [fp, #-0x10]
    // 0xafa904: ldur            x1, [fp, #-0x28]
    // 0xafa908: LoadField: r0 = r1->field_1f
    //     0xafa908: ldur            w0, [x1, #0x1f]
    // 0xafa90c: DecompressPointer r0
    //     0xafa90c: add             x0, x0, HEAP, lsl #32
    // 0xafa910: r3 = LoadClassIdInstr(r0)
    //     0xafa910: ldur            x3, [x0, #-1]
    //     0xafa914: ubfx            x3, x3, #0xc, #0x14
    // 0xafa918: r16 = "video"
    //     0xafa918: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dca0] "video"
    //     0xafa91c: ldr             x16, [x16, #0xca0]
    // 0xafa920: stp             x16, x0, [SP]
    // 0xafa924: mov             x0, x3
    // 0xafa928: mov             lr, x0
    // 0xafa92c: ldr             lr, [x21, lr, lsl #3]
    // 0xafa930: blr             lr
    // 0xafa934: tbnz            w0, #4, #0xafaa48
    // 0xafa938: ldur            x0, [fp, #-0x10]
    // 0xafa93c: ldur            x2, [fp, #-0x28]
    // 0xafa940: r1 = <Video>
    //     0xafa940: add             x1, PP, #0x29, lsl #12  ; [pp+0x290c0] TypeArguments: <Video>
    //     0xafa944: ldr             x1, [x1, #0xc0]
    // 0xafa948: r0 = HomeSummaryWidget()
    //     0xafa948: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafa94c: mov             x3, x0
    // 0xafa950: ldur            x0, [fp, #-0x28]
    // 0xafa954: stur            x3, [fp, #-0x30]
    // 0xafa958: StoreField: r3->field_f = r0
    //     0xafa958: stur            w0, [x3, #0xf]
    // 0xafa95c: r1 = Function '<anonymous closure>':.
    //     0xafa95c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dca8] AnonymousClosure: (0xafb974), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa960: ldr             x1, [x1, #0xca8]
    // 0xafa964: r2 = Null
    //     0xafa964: mov             x2, NULL
    // 0xafa968: r0 = AllocateClosure()
    //     0xafa968: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa96c: mov             x1, x0
    // 0xafa970: ldur            x0, [fp, #-0x30]
    // 0xafa974: StoreField: r0->field_13 = r1
    //     0xafa974: stur            w1, [x0, #0x13]
    // 0xafa978: r1 = Function '<anonymous closure>':.
    //     0xafa978: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] AnonymousClosure: (0xafb948), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafa97c: ldr             x1, [x1, #0xcb0]
    // 0xafa980: r2 = Null
    //     0xafa980: mov             x2, NULL
    // 0xafa984: r0 = AllocateClosure()
    //     0xafa984: bl              #0xec1630  ; AllocateClosureStub
    // 0xafa988: mov             x1, x0
    // 0xafa98c: ldur            x0, [fp, #-0x30]
    // 0xafa990: StoreField: r0->field_1b = r1
    //     0xafa990: stur            w1, [x0, #0x1b]
    // 0xafa994: r2 = Instance_SizedBox
    //     0xafa994: add             x2, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafa998: ldr             x2, [x2, #0xc40]
    // 0xafa99c: StoreField: r0->field_27 = r2
    //     0xafa99c: stur            w2, [x0, #0x27]
    // 0xafa9a0: r3 = Instance_EdgeInsets
    //     0xafa9a0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc90] Obj!EdgeInsets@e12941
    //     0xafa9a4: ldr             x3, [x3, #0xc90]
    // 0xafa9a8: StoreField: r0->field_2b = r3
    //     0xafa9a8: stur            w3, [x0, #0x2b]
    // 0xafa9ac: r4 = Instance_EdgeInsets
    //     0xafa9ac: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dc98] Obj!EdgeInsets@e12581
    //     0xafa9b0: ldr             x4, [x4, #0xc98]
    // 0xafa9b4: StoreField: r0->field_2f = r4
    //     0xafa9b4: stur            w4, [x0, #0x2f]
    // 0xafa9b8: r5 = true
    //     0xafa9b8: add             x5, NULL, #0x20  ; true
    // 0xafa9bc: StoreField: r0->field_33 = r5
    //     0xafa9bc: stur            w5, [x0, #0x33]
    // 0xafa9c0: r6 = const []
    //     0xafa9c0: add             x6, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafa9c4: ldr             x6, [x6, #0x148]
    // 0xafa9c8: StoreField: r0->field_37 = r6
    //     0xafa9c8: stur            w6, [x0, #0x37]
    // 0xafa9cc: ldur            x7, [fp, #-0x10]
    // 0xafa9d0: LoadField: r1 = r7->field_b
    //     0xafa9d0: ldur            w1, [x7, #0xb]
    // 0xafa9d4: LoadField: r8 = r7->field_f
    //     0xafa9d4: ldur            w8, [x7, #0xf]
    // 0xafa9d8: DecompressPointer r8
    //     0xafa9d8: add             x8, x8, HEAP, lsl #32
    // 0xafa9dc: LoadField: r9 = r8->field_b
    //     0xafa9dc: ldur            w9, [x8, #0xb]
    // 0xafa9e0: r8 = LoadInt32Instr(r1)
    //     0xafa9e0: sbfx            x8, x1, #1, #0x1f
    // 0xafa9e4: stur            x8, [fp, #-0x40]
    // 0xafa9e8: r1 = LoadInt32Instr(r9)
    //     0xafa9e8: sbfx            x1, x9, #1, #0x1f
    // 0xafa9ec: cmp             x8, x1
    // 0xafa9f0: b.ne            #0xafa9fc
    // 0xafa9f4: mov             x1, x7
    // 0xafa9f8: r0 = _growToNextCapacity()
    //     0xafa9f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafa9fc: ldur            x2, [fp, #-0x10]
    // 0xafaa00: ldur            x3, [fp, #-0x40]
    // 0xafaa04: add             x0, x3, #1
    // 0xafaa08: lsl             x1, x0, #1
    // 0xafaa0c: StoreField: r2->field_b = r1
    //     0xafaa0c: stur            w1, [x2, #0xb]
    // 0xafaa10: LoadField: r1 = r2->field_f
    //     0xafaa10: ldur            w1, [x2, #0xf]
    // 0xafaa14: DecompressPointer r1
    //     0xafaa14: add             x1, x1, HEAP, lsl #32
    // 0xafaa18: ldur            x0, [fp, #-0x30]
    // 0xafaa1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafaa1c: add             x25, x1, x3, lsl #2
    //     0xafaa20: add             x25, x25, #0xf
    //     0xafaa24: str             w0, [x25]
    //     0xafaa28: tbz             w0, #0, #0xafaa44
    //     0xafaa2c: ldurb           w16, [x1, #-1]
    //     0xafaa30: ldurb           w17, [x0, #-1]
    //     0xafaa34: and             x16, x17, x16, lsr #2
    //     0xafaa38: tst             x16, HEAP, lsr #32
    //     0xafaa3c: b.eq            #0xafaa44
    //     0xafaa40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafaa44: b               #0xafaa4c
    // 0xafaa48: ldur            x2, [fp, #-0x10]
    // 0xafaa4c: ldur            x1, [fp, #-0x28]
    // 0xafaa50: LoadField: r0 = r1->field_1f
    //     0xafaa50: ldur            w0, [x1, #0x1f]
    // 0xafaa54: DecompressPointer r0
    //     0xafaa54: add             x0, x0, HEAP, lsl #32
    // 0xafaa58: r3 = LoadClassIdInstr(r0)
    //     0xafaa58: ldur            x3, [x0, #-1]
    //     0xafaa5c: ubfx            x3, x3, #0xc, #0x14
    // 0xafaa60: r16 = "kalam"
    //     0xafaa60: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dcb8] "kalam"
    //     0xafaa64: ldr             x16, [x16, #0xcb8]
    // 0xafaa68: stp             x16, x0, [SP]
    // 0xafaa6c: mov             x0, x3
    // 0xafaa70: mov             lr, x0
    // 0xafaa74: ldr             lr, [x21, lr, lsl #3]
    // 0xafaa78: blr             lr
    // 0xafaa7c: tbnz            w0, #4, #0xafab90
    // 0xafaa80: ldur            x0, [fp, #-0x10]
    // 0xafaa84: ldur            x2, [fp, #-0x28]
    // 0xafaa88: r1 = <Kalam>
    //     0xafaa88: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d478] TypeArguments: <Kalam>
    //     0xafaa8c: ldr             x1, [x1, #0x478]
    // 0xafaa90: r0 = HomeSummaryWidget()
    //     0xafaa90: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafaa94: mov             x3, x0
    // 0xafaa98: ldur            x0, [fp, #-0x28]
    // 0xafaa9c: stur            x3, [fp, #-0x30]
    // 0xafaaa0: StoreField: r3->field_f = r0
    //     0xafaaa0: stur            w0, [x3, #0xf]
    // 0xafaaa4: r1 = Function '<anonymous closure>':.
    //     0xafaaa4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcc0] AnonymousClosure: (0xafb914), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafaaa8: ldr             x1, [x1, #0xcc0]
    // 0xafaaac: r2 = Null
    //     0xafaaac: mov             x2, NULL
    // 0xafaab0: r0 = AllocateClosure()
    //     0xafaab0: bl              #0xec1630  ; AllocateClosureStub
    // 0xafaab4: mov             x1, x0
    // 0xafaab8: ldur            x0, [fp, #-0x30]
    // 0xafaabc: StoreField: r0->field_13 = r1
    //     0xafaabc: stur            w1, [x0, #0x13]
    // 0xafaac0: r1 = Function '<anonymous closure>':.
    //     0xafaac0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcc8] AnonymousClosure: (0xafb8e8), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafaac4: ldr             x1, [x1, #0xcc8]
    // 0xafaac8: r2 = Null
    //     0xafaac8: mov             x2, NULL
    // 0xafaacc: r0 = AllocateClosure()
    //     0xafaacc: bl              #0xec1630  ; AllocateClosureStub
    // 0xafaad0: mov             x1, x0
    // 0xafaad4: ldur            x0, [fp, #-0x30]
    // 0xafaad8: StoreField: r0->field_1b = r1
    //     0xafaad8: stur            w1, [x0, #0x1b]
    // 0xafaadc: r2 = Instance_SizedBox
    //     0xafaadc: add             x2, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafaae0: ldr             x2, [x2, #0xc40]
    // 0xafaae4: StoreField: r0->field_27 = r2
    //     0xafaae4: stur            w2, [x0, #0x27]
    // 0xafaae8: r3 = Instance_EdgeInsets
    //     0xafaae8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc90] Obj!EdgeInsets@e12941
    //     0xafaaec: ldr             x3, [x3, #0xc90]
    // 0xafaaf0: StoreField: r0->field_2b = r3
    //     0xafaaf0: stur            w3, [x0, #0x2b]
    // 0xafaaf4: r4 = Instance_EdgeInsets
    //     0xafaaf4: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dc98] Obj!EdgeInsets@e12581
    //     0xafaaf8: ldr             x4, [x4, #0xc98]
    // 0xafaafc: StoreField: r0->field_2f = r4
    //     0xafaafc: stur            w4, [x0, #0x2f]
    // 0xafab00: r5 = true
    //     0xafab00: add             x5, NULL, #0x20  ; true
    // 0xafab04: StoreField: r0->field_33 = r5
    //     0xafab04: stur            w5, [x0, #0x33]
    // 0xafab08: r6 = const []
    //     0xafab08: add             x6, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafab0c: ldr             x6, [x6, #0x148]
    // 0xafab10: StoreField: r0->field_37 = r6
    //     0xafab10: stur            w6, [x0, #0x37]
    // 0xafab14: ldur            x7, [fp, #-0x10]
    // 0xafab18: LoadField: r1 = r7->field_b
    //     0xafab18: ldur            w1, [x7, #0xb]
    // 0xafab1c: LoadField: r8 = r7->field_f
    //     0xafab1c: ldur            w8, [x7, #0xf]
    // 0xafab20: DecompressPointer r8
    //     0xafab20: add             x8, x8, HEAP, lsl #32
    // 0xafab24: LoadField: r9 = r8->field_b
    //     0xafab24: ldur            w9, [x8, #0xb]
    // 0xafab28: r8 = LoadInt32Instr(r1)
    //     0xafab28: sbfx            x8, x1, #1, #0x1f
    // 0xafab2c: stur            x8, [fp, #-0x40]
    // 0xafab30: r1 = LoadInt32Instr(r9)
    //     0xafab30: sbfx            x1, x9, #1, #0x1f
    // 0xafab34: cmp             x8, x1
    // 0xafab38: b.ne            #0xafab44
    // 0xafab3c: mov             x1, x7
    // 0xafab40: r0 = _growToNextCapacity()
    //     0xafab40: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafab44: ldur            x2, [fp, #-0x10]
    // 0xafab48: ldur            x3, [fp, #-0x40]
    // 0xafab4c: add             x0, x3, #1
    // 0xafab50: lsl             x1, x0, #1
    // 0xafab54: StoreField: r2->field_b = r1
    //     0xafab54: stur            w1, [x2, #0xb]
    // 0xafab58: LoadField: r1 = r2->field_f
    //     0xafab58: ldur            w1, [x2, #0xf]
    // 0xafab5c: DecompressPointer r1
    //     0xafab5c: add             x1, x1, HEAP, lsl #32
    // 0xafab60: ldur            x0, [fp, #-0x30]
    // 0xafab64: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafab64: add             x25, x1, x3, lsl #2
    //     0xafab68: add             x25, x25, #0xf
    //     0xafab6c: str             w0, [x25]
    //     0xafab70: tbz             w0, #0, #0xafab8c
    //     0xafab74: ldurb           w16, [x1, #-1]
    //     0xafab78: ldurb           w17, [x0, #-1]
    //     0xafab7c: and             x16, x17, x16, lsr #2
    //     0xafab80: tst             x16, HEAP, lsr #32
    //     0xafab84: b.eq            #0xafab8c
    //     0xafab88: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafab8c: b               #0xafab94
    // 0xafab90: ldur            x2, [fp, #-0x10]
    // 0xafab94: ldur            x1, [fp, #-0x28]
    // 0xafab98: LoadField: r0 = r1->field_1f
    //     0xafab98: ldur            w0, [x1, #0x1f]
    // 0xafab9c: DecompressPointer r0
    //     0xafab9c: add             x0, x0, HEAP, lsl #32
    // 0xafaba0: r3 = LoadClassIdInstr(r0)
    //     0xafaba0: ldur            x3, [x0, #-1]
    //     0xafaba4: ubfx            x3, x3, #0xc, #0x14
    // 0xafaba8: r16 = "ads"
    //     0xafaba8: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dcd0] "ads"
    //     0xafabac: ldr             x16, [x16, #0xcd0]
    // 0xafabb0: stp             x16, x0, [SP]
    // 0xafabb4: mov             x0, x3
    // 0xafabb8: mov             lr, x0
    // 0xafabbc: ldr             lr, [x21, lr, lsl #3]
    // 0xafabc0: blr             lr
    // 0xafabc4: tbnz            w0, #4, #0xafad48
    // 0xafabc8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafabc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafabcc: ldr             x0, [x0, #0x2670]
    //     0xafabd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafabd4: cmp             w0, w16
    //     0xafabd8: b.ne            #0xafabe4
    //     0xafabdc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xafabe0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xafabe4: r16 = Instance_Brightness
    //     0xafabe4: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xafabe8: stp             NULL, x16, [SP]
    // 0xafabec: r1 = Null
    //     0xafabec: mov             x1, NULL
    // 0xafabf0: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xafabf0: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xafabf4: r0 = ThemeData()
    //     0xafabf4: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xafabf8: stur            x0, [fp, #-0x30]
    // 0xafabfc: r0 = GetNavigation.context()
    //     0xafabfc: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafac00: cmp             w0, NULL
    // 0xafac04: b.eq            #0xafac24
    // 0xafac08: r0 = GetNavigation.context()
    //     0xafac08: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafac0c: cmp             w0, NULL
    // 0xafac10: b.eq            #0xafb160
    // 0xafac14: mov             x1, x0
    // 0xafac18: r0 = of()
    //     0xafac18: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafac1c: mov             x1, x0
    // 0xafac20: b               #0xafac28
    // 0xafac24: ldur            x1, [fp, #-0x30]
    // 0xafac28: ldur            x0, [fp, #-0x10]
    // 0xafac2c: ldur            x2, [fp, #-0x28]
    // 0xafac30: LoadField: r3 = r1->field_37
    //     0xafac30: ldur            w3, [x1, #0x37]
    // 0xafac34: DecompressPointer r3
    //     0xafac34: add             x3, x3, HEAP, lsl #32
    // 0xafac38: stur            x3, [fp, #-0x30]
    // 0xafac3c: r1 = <Ad>
    //     0xafac3c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcd8] TypeArguments: <Ad>
    //     0xafac40: ldr             x1, [x1, #0xcd8]
    // 0xafac44: r0 = HomeSummaryWidget()
    //     0xafac44: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafac48: mov             x3, x0
    // 0xafac4c: ldur            x0, [fp, #-0x28]
    // 0xafac50: stur            x3, [fp, #-0x38]
    // 0xafac54: StoreField: r3->field_f = r0
    //     0xafac54: stur            w0, [x3, #0xf]
    // 0xafac58: r1 = Function '<anonymous closure>':.
    //     0xafac58: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dce0] AnonymousClosure: (0xafb714), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafac5c: ldr             x1, [x1, #0xce0]
    // 0xafac60: r2 = Null
    //     0xafac60: mov             x2, NULL
    // 0xafac64: r0 = AllocateClosure()
    //     0xafac64: bl              #0xec1630  ; AllocateClosureStub
    // 0xafac68: mov             x1, x0
    // 0xafac6c: ldur            x0, [fp, #-0x38]
    // 0xafac70: StoreField: r0->field_13 = r1
    //     0xafac70: stur            w1, [x0, #0x13]
    // 0xafac74: r1 = Function '<anonymous closure>':.
    //     0xafac74: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dce8] AnonymousClosure: (0xafb6e8), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafac78: ldr             x1, [x1, #0xce8]
    // 0xafac7c: r2 = Null
    //     0xafac7c: mov             x2, NULL
    // 0xafac80: r0 = AllocateClosure()
    //     0xafac80: bl              #0xec1630  ; AllocateClosureStub
    // 0xafac84: mov             x1, x0
    // 0xafac88: ldur            x0, [fp, #-0x38]
    // 0xafac8c: StoreField: r0->field_1b = r1
    //     0xafac8c: stur            w1, [x0, #0x1b]
    // 0xafac90: ldur            x1, [fp, #-0x30]
    // 0xafac94: StoreField: r0->field_23 = r1
    //     0xafac94: stur            w1, [x0, #0x23]
    // 0xafac98: r2 = Instance_SizedBox
    //     0xafac98: add             x2, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafac9c: ldr             x2, [x2, #0xc40]
    // 0xafaca0: StoreField: r0->field_27 = r2
    //     0xafaca0: stur            w2, [x0, #0x27]
    // 0xafaca4: r3 = Instance_EdgeInsets
    //     0xafaca4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc68] Obj!EdgeInsets@e12971
    //     0xafaca8: ldr             x3, [x3, #0xc68]
    // 0xafacac: StoreField: r0->field_2b = r3
    //     0xafacac: stur            w3, [x0, #0x2b]
    // 0xafacb0: r4 = Instance_EdgeInsets
    //     0xafacb0: ldr             x4, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xafacb4: StoreField: r0->field_2f = r4
    //     0xafacb4: stur            w4, [x0, #0x2f]
    // 0xafacb8: r5 = true
    //     0xafacb8: add             x5, NULL, #0x20  ; true
    // 0xafacbc: StoreField: r0->field_33 = r5
    //     0xafacbc: stur            w5, [x0, #0x33]
    // 0xafacc0: r6 = const []
    //     0xafacc0: add             x6, PP, #0x29, lsl #12  ; [pp+0x29148] List<Widget>(0)
    //     0xafacc4: ldr             x6, [x6, #0x148]
    // 0xafacc8: StoreField: r0->field_37 = r6
    //     0xafacc8: stur            w6, [x0, #0x37]
    // 0xafaccc: ldur            x7, [fp, #-0x10]
    // 0xafacd0: LoadField: r1 = r7->field_b
    //     0xafacd0: ldur            w1, [x7, #0xb]
    // 0xafacd4: LoadField: r8 = r7->field_f
    //     0xafacd4: ldur            w8, [x7, #0xf]
    // 0xafacd8: DecompressPointer r8
    //     0xafacd8: add             x8, x8, HEAP, lsl #32
    // 0xafacdc: LoadField: r9 = r8->field_b
    //     0xafacdc: ldur            w9, [x8, #0xb]
    // 0xaface0: r8 = LoadInt32Instr(r1)
    //     0xaface0: sbfx            x8, x1, #1, #0x1f
    // 0xaface4: stur            x8, [fp, #-0x40]
    // 0xaface8: r1 = LoadInt32Instr(r9)
    //     0xaface8: sbfx            x1, x9, #1, #0x1f
    // 0xafacec: cmp             x8, x1
    // 0xafacf0: b.ne            #0xafacfc
    // 0xafacf4: mov             x1, x7
    // 0xafacf8: r0 = _growToNextCapacity()
    //     0xafacf8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafacfc: ldur            x2, [fp, #-0x10]
    // 0xafad00: ldur            x3, [fp, #-0x40]
    // 0xafad04: add             x0, x3, #1
    // 0xafad08: lsl             x1, x0, #1
    // 0xafad0c: StoreField: r2->field_b = r1
    //     0xafad0c: stur            w1, [x2, #0xb]
    // 0xafad10: LoadField: r1 = r2->field_f
    //     0xafad10: ldur            w1, [x2, #0xf]
    // 0xafad14: DecompressPointer r1
    //     0xafad14: add             x1, x1, HEAP, lsl #32
    // 0xafad18: ldur            x0, [fp, #-0x38]
    // 0xafad1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafad1c: add             x25, x1, x3, lsl #2
    //     0xafad20: add             x25, x25, #0xf
    //     0xafad24: str             w0, [x25]
    //     0xafad28: tbz             w0, #0, #0xafad44
    //     0xafad2c: ldurb           w16, [x1, #-1]
    //     0xafad30: ldurb           w17, [x0, #-1]
    //     0xafad34: and             x16, x17, x16, lsr #2
    //     0xafad38: tst             x16, HEAP, lsr #32
    //     0xafad3c: b.eq            #0xafad44
    //     0xafad40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafad44: b               #0xafad4c
    // 0xafad48: ldur            x2, [fp, #-0x10]
    // 0xafad4c: ldur            x1, [fp, #-0x28]
    // 0xafad50: LoadField: r0 = r1->field_1f
    //     0xafad50: ldur            w0, [x1, #0x1f]
    // 0xafad54: DecompressPointer r0
    //     0xafad54: add             x0, x0, HEAP, lsl #32
    // 0xafad58: r3 = LoadClassIdInstr(r0)
    //     0xafad58: ldur            x3, [x0, #-1]
    //     0xafad5c: ubfx            x3, x3, #0xc, #0x14
    // 0xafad60: r16 = "event"
    //     0xafad60: add             x16, PP, #9, lsl #12  ; [pp+0x91e8] "event"
    //     0xafad64: ldr             x16, [x16, #0x1e8]
    // 0xafad68: stp             x16, x0, [SP]
    // 0xafad6c: mov             x0, x3
    // 0xafad70: mov             lr, x0
    // 0xafad74: ldr             lr, [x21, lr, lsl #3]
    // 0xafad78: blr             lr
    // 0xafad7c: tbnz            w0, #4, #0xafae54
    // 0xafad80: ldur            x1, [fp, #-0x10]
    // 0xafad84: ldur            x0, [fp, #-0x28]
    // 0xafad88: LoadField: r2 = r0->field_1b
    //     0xafad88: ldur            w2, [x0, #0x1b]
    // 0xafad8c: DecompressPointer r2
    //     0xafad8c: add             x2, x2, HEAP, lsl #32
    // 0xafad90: stur            x2, [fp, #-0x48]
    // 0xafad94: cmp             w2, NULL
    // 0xafad98: b.eq            #0xafb164
    // 0xafad9c: LoadField: r3 = r0->field_33
    //     0xafad9c: ldur            w3, [x0, #0x33]
    // 0xafada0: DecompressPointer r3
    //     0xafada0: add             x3, x3, HEAP, lsl #32
    // 0xafada4: stur            x3, [fp, #-0x38]
    // 0xafada8: LoadField: r4 = r0->field_2b
    //     0xafada8: ldur            w4, [x0, #0x2b]
    // 0xafadac: DecompressPointer r4
    //     0xafadac: add             x4, x4, HEAP, lsl #32
    // 0xafadb0: stur            x4, [fp, #-0x30]
    // 0xafadb4: r0 = HomeEventWidget()
    //     0xafadb4: bl              #0xafb1bc  ; AllocateHomeEventWidgetStub -> HomeEventWidget (size=0x18)
    // 0xafadb8: mov             x2, x0
    // 0xafadbc: ldur            x0, [fp, #-0x48]
    // 0xafadc0: stur            x2, [fp, #-0x50]
    // 0xafadc4: StoreField: r2->field_b = r0
    //     0xafadc4: stur            w0, [x2, #0xb]
    // 0xafadc8: ldur            x0, [fp, #-0x38]
    // 0xafadcc: StoreField: r2->field_f = r0
    //     0xafadcc: stur            w0, [x2, #0xf]
    // 0xafadd0: ldur            x0, [fp, #-0x30]
    // 0xafadd4: StoreField: r2->field_13 = r0
    //     0xafadd4: stur            w0, [x2, #0x13]
    // 0xafadd8: ldur            x0, [fp, #-0x10]
    // 0xafaddc: LoadField: r1 = r0->field_b
    //     0xafaddc: ldur            w1, [x0, #0xb]
    // 0xafade0: LoadField: r3 = r0->field_f
    //     0xafade0: ldur            w3, [x0, #0xf]
    // 0xafade4: DecompressPointer r3
    //     0xafade4: add             x3, x3, HEAP, lsl #32
    // 0xafade8: LoadField: r4 = r3->field_b
    //     0xafade8: ldur            w4, [x3, #0xb]
    // 0xafadec: r3 = LoadInt32Instr(r1)
    //     0xafadec: sbfx            x3, x1, #1, #0x1f
    // 0xafadf0: stur            x3, [fp, #-0x40]
    // 0xafadf4: r1 = LoadInt32Instr(r4)
    //     0xafadf4: sbfx            x1, x4, #1, #0x1f
    // 0xafadf8: cmp             x3, x1
    // 0xafadfc: b.ne            #0xafae08
    // 0xafae00: mov             x1, x0
    // 0xafae04: r0 = _growToNextCapacity()
    //     0xafae04: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafae08: ldur            x2, [fp, #-0x10]
    // 0xafae0c: ldur            x3, [fp, #-0x40]
    // 0xafae10: add             x0, x3, #1
    // 0xafae14: lsl             x1, x0, #1
    // 0xafae18: StoreField: r2->field_b = r1
    //     0xafae18: stur            w1, [x2, #0xb]
    // 0xafae1c: LoadField: r1 = r2->field_f
    //     0xafae1c: ldur            w1, [x2, #0xf]
    // 0xafae20: DecompressPointer r1
    //     0xafae20: add             x1, x1, HEAP, lsl #32
    // 0xafae24: ldur            x0, [fp, #-0x50]
    // 0xafae28: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafae28: add             x25, x1, x3, lsl #2
    //     0xafae2c: add             x25, x25, #0xf
    //     0xafae30: str             w0, [x25]
    //     0xafae34: tbz             w0, #0, #0xafae50
    //     0xafae38: ldurb           w16, [x1, #-1]
    //     0xafae3c: ldurb           w17, [x0, #-1]
    //     0xafae40: and             x16, x17, x16, lsr #2
    //     0xafae44: tst             x16, HEAP, lsr #32
    //     0xafae48: b.eq            #0xafae50
    //     0xafae4c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafae50: b               #0xafae58
    // 0xafae54: ldur            x2, [fp, #-0x10]
    // 0xafae58: ldur            x1, [fp, #-0x28]
    // 0xafae5c: LoadField: r0 = r1->field_1f
    //     0xafae5c: ldur            w0, [x1, #0x1f]
    // 0xafae60: DecompressPointer r0
    //     0xafae60: add             x0, x0, HEAP, lsl #32
    // 0xafae64: r3 = LoadClassIdInstr(r0)
    //     0xafae64: ldur            x3, [x0, #-1]
    //     0xafae68: ubfx            x3, x3, #0xc, #0x14
    // 0xafae6c: r16 = "banner"
    //     0xafae6c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dcf0] "banner"
    //     0xafae70: ldr             x16, [x16, #0xcf0]
    // 0xafae74: stp             x16, x0, [SP]
    // 0xafae78: mov             x0, x3
    // 0xafae7c: mov             lr, x0
    // 0xafae80: ldr             lr, [x21, lr, lsl #3]
    // 0xafae84: blr             lr
    // 0xafae88: tbnz            w0, #4, #0xafb028
    // 0xafae8c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafae8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafae90: ldr             x0, [x0, #0x2670]
    //     0xafae94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafae98: cmp             w0, w16
    //     0xafae9c: b.ne            #0xafaea8
    //     0xafaea0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xafaea4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xafaea8: r16 = Instance_Brightness
    //     0xafaea8: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xafaeac: stp             NULL, x16, [SP]
    // 0xafaeb0: r1 = Null
    //     0xafaeb0: mov             x1, NULL
    // 0xafaeb4: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xafaeb4: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xafaeb8: r0 = ThemeData()
    //     0xafaeb8: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xafaebc: stur            x0, [fp, #-0x30]
    // 0xafaec0: r0 = GetNavigation.context()
    //     0xafaec0: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafaec4: cmp             w0, NULL
    // 0xafaec8: b.eq            #0xafaee8
    // 0xafaecc: r0 = GetNavigation.context()
    //     0xafaecc: bl              #0x639c18  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xafaed0: cmp             w0, NULL
    // 0xafaed4: b.eq            #0xafb168
    // 0xafaed8: mov             x1, x0
    // 0xafaedc: r0 = of()
    //     0xafaedc: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafaee0: mov             x1, x0
    // 0xafaee4: b               #0xafaeec
    // 0xafaee8: ldur            x1, [fp, #-0x30]
    // 0xafaeec: ldur            x0, [fp, #-0x10]
    // 0xafaef0: ldur            x2, [fp, #-0x28]
    // 0xafaef4: LoadField: r3 = r1->field_37
    //     0xafaef4: ldur            w3, [x1, #0x37]
    // 0xafaef8: DecompressPointer r3
    //     0xafaef8: add             x3, x3, HEAP, lsl #32
    // 0xafaefc: stur            x3, [fp, #-0x30]
    // 0xafaf00: r1 = <Banner>
    //     0xafaf00: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcf8] TypeArguments: <Banner>
    //     0xafaf04: ldr             x1, [x1, #0xcf8]
    // 0xafaf08: r0 = HomeSummaryWidget()
    //     0xafaf08: bl              #0xafb1c8  ; AllocateHomeSummaryWidgetStub -> HomeSummaryWidget<X0> (size=0x3c)
    // 0xafaf0c: mov             x3, x0
    // 0xafaf10: ldur            x0, [fp, #-0x28]
    // 0xafaf14: stur            x3, [fp, #-0x38]
    // 0xafaf18: StoreField: r3->field_f = r0
    //     0xafaf18: stur            w0, [x3, #0xf]
    // 0xafaf1c: r1 = Function '<anonymous closure>':.
    //     0xafaf1c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd00] AnonymousClosure: (0xafb4dc), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafaf20: ldr             x1, [x1, #0xd00]
    // 0xafaf24: r2 = Null
    //     0xafaf24: mov             x2, NULL
    // 0xafaf28: r0 = AllocateClosure()
    //     0xafaf28: bl              #0xec1630  ; AllocateClosureStub
    // 0xafaf2c: mov             x1, x0
    // 0xafaf30: ldur            x0, [fp, #-0x38]
    // 0xafaf34: StoreField: r0->field_13 = r1
    //     0xafaf34: stur            w1, [x0, #0x13]
    // 0xafaf38: ldur            x2, [fp, #-0x20]
    // 0xafaf3c: r1 = Function '<anonymous closure>':.
    //     0xafaf3c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd08] AnonymousClosure: (0xafb2b8), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafaf40: ldr             x1, [x1, #0xd08]
    // 0xafaf44: r0 = AllocateClosure()
    //     0xafaf44: bl              #0xec1630  ; AllocateClosureStub
    // 0xafaf48: mov             x1, x0
    // 0xafaf4c: ldur            x0, [fp, #-0x38]
    // 0xafaf50: StoreField: r0->field_1b = r1
    //     0xafaf50: stur            w1, [x0, #0x1b]
    // 0xafaf54: r1 = Function '<anonymous closure>':.
    //     0xafaf54: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd10] AnonymousClosure: (0xafb1d4), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafaf58: ldr             x1, [x1, #0xd10]
    // 0xafaf5c: r2 = Null
    //     0xafaf5c: mov             x2, NULL
    // 0xafaf60: r0 = AllocateClosure()
    //     0xafaf60: bl              #0xec1630  ; AllocateClosureStub
    // 0xafaf64: mov             x1, x0
    // 0xafaf68: ldur            x0, [fp, #-0x38]
    // 0xafaf6c: ArrayStore: r0[0] = r1  ; List_4
    //     0xafaf6c: stur            w1, [x0, #0x17]
    // 0xafaf70: ldur            x1, [fp, #-0x30]
    // 0xafaf74: StoreField: r0->field_23 = r1
    //     0xafaf74: stur            w1, [x0, #0x23]
    // 0xafaf78: r2 = Instance_SizedBox
    //     0xafaf78: add             x2, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafaf7c: ldr             x2, [x2, #0xc40]
    // 0xafaf80: StoreField: r0->field_27 = r2
    //     0xafaf80: stur            w2, [x0, #0x27]
    // 0xafaf84: r3 = Instance_EdgeInsets
    //     0xafaf84: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc68] Obj!EdgeInsets@e12971
    //     0xafaf88: ldr             x3, [x3, #0xc68]
    // 0xafaf8c: StoreField: r0->field_2b = r3
    //     0xafaf8c: stur            w3, [x0, #0x2b]
    // 0xafaf90: r4 = Instance_EdgeInsets
    //     0xafaf90: ldr             x4, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xafaf94: StoreField: r0->field_2f = r4
    //     0xafaf94: stur            w4, [x0, #0x2f]
    // 0xafaf98: r5 = true
    //     0xafaf98: add             x5, NULL, #0x20  ; true
    // 0xafaf9c: StoreField: r0->field_33 = r5
    //     0xafaf9c: stur            w5, [x0, #0x33]
    // 0xafafa0: r6 = const [Instance of 'SizedBox']
    //     0xafafa0: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dd18] List<Widget>(1)
    //     0xafafa4: ldr             x6, [x6, #0xd18]
    // 0xafafa8: StoreField: r0->field_37 = r6
    //     0xafafa8: stur            w6, [x0, #0x37]
    // 0xafafac: ldur            x7, [fp, #-0x10]
    // 0xafafb0: LoadField: r1 = r7->field_b
    //     0xafafb0: ldur            w1, [x7, #0xb]
    // 0xafafb4: LoadField: r8 = r7->field_f
    //     0xafafb4: ldur            w8, [x7, #0xf]
    // 0xafafb8: DecompressPointer r8
    //     0xafafb8: add             x8, x8, HEAP, lsl #32
    // 0xafafbc: LoadField: r9 = r8->field_b
    //     0xafafbc: ldur            w9, [x8, #0xb]
    // 0xafafc0: r8 = LoadInt32Instr(r1)
    //     0xafafc0: sbfx            x8, x1, #1, #0x1f
    // 0xafafc4: stur            x8, [fp, #-0x40]
    // 0xafafc8: r1 = LoadInt32Instr(r9)
    //     0xafafc8: sbfx            x1, x9, #1, #0x1f
    // 0xafafcc: cmp             x8, x1
    // 0xafafd0: b.ne            #0xafafdc
    // 0xafafd4: mov             x1, x7
    // 0xafafd8: r0 = _growToNextCapacity()
    //     0xafafd8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafafdc: ldur            x2, [fp, #-0x10]
    // 0xafafe0: ldur            x3, [fp, #-0x40]
    // 0xafafe4: add             x0, x3, #1
    // 0xafafe8: lsl             x1, x0, #1
    // 0xafafec: StoreField: r2->field_b = r1
    //     0xafafec: stur            w1, [x2, #0xb]
    // 0xafaff0: LoadField: r1 = r2->field_f
    //     0xafaff0: ldur            w1, [x2, #0xf]
    // 0xafaff4: DecompressPointer r1
    //     0xafaff4: add             x1, x1, HEAP, lsl #32
    // 0xafaff8: ldur            x0, [fp, #-0x38]
    // 0xafaffc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafaffc: add             x25, x1, x3, lsl #2
    //     0xafb000: add             x25, x25, #0xf
    //     0xafb004: str             w0, [x25]
    //     0xafb008: tbz             w0, #0, #0xafb024
    //     0xafb00c: ldurb           w16, [x1, #-1]
    //     0xafb010: ldurb           w17, [x0, #-1]
    //     0xafb014: and             x16, x17, x16, lsr #2
    //     0xafb018: tst             x16, HEAP, lsr #32
    //     0xafb01c: b.eq            #0xafb024
    //     0xafb020: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xafb024: b               #0xafb02c
    // 0xafb028: ldur            x2, [fp, #-0x10]
    // 0xafb02c: mov             x4, x2
    // 0xafb030: ldur            x2, [fp, #-0x18]
    // 0xafb034: b               #0xafa56c
    // 0xafb038: ldur            x0, [fp, #-8]
    // 0xafb03c: ldur            x2, [fp, #-0x10]
    // 0xafb040: LoadField: r1 = r0->field_f
    //     0xafb040: ldur            w1, [x0, #0xf]
    // 0xafb044: DecompressPointer r1
    //     0xafb044: add             x1, x1, HEAP, lsl #32
    // 0xafb048: r0 = controller()
    //     0xafb048: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xafb04c: LoadField: r1 = r0->field_2f
    //     0xafb04c: ldur            w1, [x0, #0x2f]
    // 0xafb050: DecompressPointer r1
    //     0xafb050: add             x1, x1, HEAP, lsl #32
    // 0xafb054: r0 = homeNative()
    //     0xafb054: bl              #0xafb178  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeNative
    // 0xafb058: LoadField: r1 = r0->field_7
    //     0xafb058: ldur            w1, [x0, #7]
    // 0xafb05c: DecompressPointer r1
    //     0xafb05c: add             x1, x1, HEAP, lsl #32
    // 0xafb060: tbnz            w1, #4, #0xafb0e8
    // 0xafb064: ldur            x1, [fp, #-0x10]
    // 0xafb068: LoadField: r0 = r1->field_b
    //     0xafb068: ldur            w0, [x1, #0xb]
    // 0xafb06c: r2 = LoadInt32Instr(r0)
    //     0xafb06c: sbfx            x2, x0, #1, #0x1f
    // 0xafb070: sub             x0, x2, #1
    // 0xafb074: stur            x0, [fp, #-0x40]
    // 0xafb078: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafb078: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafb07c: ldr             x0, [x0, #0x2670]
    //     0xafb080: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafb084: cmp             w0, w16
    //     0xafb088: b.ne            #0xafb094
    //     0xafb08c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xafb090: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xafb094: r0 = GetNavigation.theme()
    //     0xafb094: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xafb098: LoadField: r1 = r0->field_3f
    //     0xafb098: ldur            w1, [x0, #0x3f]
    // 0xafb09c: DecompressPointer r1
    //     0xafb09c: add             x1, x1, HEAP, lsl #32
    // 0xafb0a0: LoadField: r0 = r1->field_7
    //     0xafb0a0: ldur            w0, [x1, #7]
    // 0xafb0a4: DecompressPointer r0
    //     0xafb0a4: add             x0, x0, HEAP, lsl #32
    // 0xafb0a8: stur            x0, [fp, #-8]
    // 0xafb0ac: r1 = <Brightness>
    //     0xafb0ac: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd20] TypeArguments: <Brightness>
    //     0xafb0b0: ldr             x1, [x1, #0xd20]
    // 0xafb0b4: r0 = ValueKey()
    //     0xafb0b4: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xafb0b8: mov             x1, x0
    // 0xafb0bc: ldur            x0, [fp, #-8]
    // 0xafb0c0: stur            x1, [fp, #-0x18]
    // 0xafb0c4: StoreField: r1->field_b = r0
    //     0xafb0c4: stur            w0, [x1, #0xb]
    // 0xafb0c8: r0 = HomeNativeAdsWidget()
    //     0xafb0c8: bl              #0xafb16c  ; AllocateHomeNativeAdsWidgetStub -> HomeNativeAdsWidget (size=0xc)
    // 0xafb0cc: mov             x1, x0
    // 0xafb0d0: ldur            x0, [fp, #-0x18]
    // 0xafb0d4: StoreField: r1->field_7 = r0
    //     0xafb0d4: stur            w0, [x1, #7]
    // 0xafb0d8: mov             x3, x1
    // 0xafb0dc: ldur            x1, [fp, #-0x10]
    // 0xafb0e0: ldur            x2, [fp, #-0x40]
    // 0xafb0e4: r0 = insert()
    //     0xafb0e4: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xafb0e8: ldur            x0, [fp, #-0x10]
    // 0xafb0ec: r0 = Column()
    //     0xafb0ec: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafb0f0: r1 = Instance_Axis
    //     0xafb0f0: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xafb0f4: StoreField: r0->field_f = r1
    //     0xafb0f4: stur            w1, [x0, #0xf]
    // 0xafb0f8: r1 = Instance_MainAxisAlignment
    //     0xafb0f8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xafb0fc: ldr             x1, [x1, #0x730]
    // 0xafb100: StoreField: r0->field_13 = r1
    //     0xafb100: stur            w1, [x0, #0x13]
    // 0xafb104: r1 = Instance_MainAxisSize
    //     0xafb104: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xafb108: ldr             x1, [x1, #0x738]
    // 0xafb10c: ArrayStore: r0[0] = r1  ; List_4
    //     0xafb10c: stur            w1, [x0, #0x17]
    // 0xafb110: r1 = Instance_CrossAxisAlignment
    //     0xafb110: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xafb114: ldr             x1, [x1, #0x740]
    // 0xafb118: StoreField: r0->field_1b = r1
    //     0xafb118: stur            w1, [x0, #0x1b]
    // 0xafb11c: r1 = Instance_VerticalDirection
    //     0xafb11c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xafb120: ldr             x1, [x1, #0x748]
    // 0xafb124: StoreField: r0->field_23 = r1
    //     0xafb124: stur            w1, [x0, #0x23]
    // 0xafb128: r1 = Instance_Clip
    //     0xafb128: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xafb12c: ldr             x1, [x1, #0x750]
    // 0xafb130: StoreField: r0->field_2b = r1
    //     0xafb130: stur            w1, [x0, #0x2b]
    // 0xafb134: StoreField: r0->field_2f = rZR
    //     0xafb134: stur            xzr, [x0, #0x2f]
    // 0xafb138: ldur            x1, [fp, #-0x10]
    // 0xafb13c: StoreField: r0->field_b = r1
    //     0xafb13c: stur            w1, [x0, #0xb]
    // 0xafb140: LeaveFrame
    //     0xafb140: mov             SP, fp
    //     0xafb144: ldp             fp, lr, [SP], #0x10
    // 0xafb148: ret
    //     0xafb148: ret             
    // 0xafb14c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb14c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb150: b               #0xafa3c4
    // 0xafb154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb158: b               #0xafa57c
    // 0xafb15c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafb15c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafb160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafb160: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafb164: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafb164: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafb168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafb168: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, List<Banner>) {
    // ** addr: 0xafb1d4, size: 0x6c
    // 0xafb1d4: EnterFrame
    //     0xafb1d4: stp             fp, lr, [SP, #-0x10]!
    //     0xafb1d8: mov             fp, SP
    // 0xafb1dc: AllocStack(0x8)
    //     0xafb1dc: sub             SP, SP, #8
    // 0xafb1e0: CheckStackOverflow
    //     0xafb1e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb1e4: cmp             SP, x16
    //     0xafb1e8: b.ls            #0xafb238
    // 0xafb1ec: r1 = Function '<anonymous closure>':.
    //     0xafb1ec: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd28] AnonymousClosure: (0xafb240), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xafb1f0: ldr             x1, [x1, #0xd28]
    // 0xafb1f4: r2 = Null
    //     0xafb1f4: mov             x2, NULL
    // 0xafb1f8: r0 = AllocateClosure()
    //     0xafb1f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xafb1fc: ldr             x1, [fp, #0x10]
    // 0xafb200: r2 = LoadClassIdInstr(r1)
    //     0xafb200: ldur            x2, [x1, #-1]
    //     0xafb204: ubfx            x2, x2, #0xc, #0x14
    // 0xafb208: str             x0, [SP]
    // 0xafb20c: mov             x0, x2
    // 0xafb210: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xafb210: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xafb214: r0 = GDT[cid_x0 + 0x133e4]()
    //     0xafb214: movz            x17, #0x33e4
    //     0xafb218: movk            x17, #0x1, lsl #16
    //     0xafb21c: add             lr, x0, x17
    //     0xafb220: ldr             lr, [x21, lr, lsl #3]
    //     0xafb224: blr             lr
    // 0xafb228: r0 = Null
    //     0xafb228: mov             x0, NULL
    // 0xafb22c: LeaveFrame
    //     0xafb22c: mov             SP, fp
    //     0xafb230: ldp             fp, lr, [SP], #0x10
    // 0xafb234: ret
    //     0xafb234: ret             
    // 0xafb238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb23c: b               #0xafb1ec
  }
  [closure] int <anonymous closure>(dynamic, Banner, Banner) {
    // ** addr: 0xafb240, size: 0x78
    // 0xafb240: EnterFrame
    //     0xafb240: stp             fp, lr, [SP, #-0x10]!
    //     0xafb244: mov             fp, SP
    // 0xafb248: CheckStackOverflow
    //     0xafb248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb24c: cmp             SP, x16
    //     0xafb250: b.ls            #0xafb2b0
    // 0xafb254: ldr             x0, [fp, #0x18]
    // 0xafb258: LoadField: r2 = r0->field_7
    //     0xafb258: ldur            x2, [x0, #7]
    // 0xafb25c: ldr             x0, [fp, #0x10]
    // 0xafb260: LoadField: r3 = r0->field_7
    //     0xafb260: ldur            x3, [x0, #7]
    // 0xafb264: r0 = BoxInt64Instr(r2)
    //     0xafb264: sbfiz           x0, x2, #1, #0x1f
    //     0xafb268: cmp             x2, x0, asr #1
    //     0xafb26c: b.eq            #0xafb278
    //     0xafb270: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xafb274: stur            x2, [x0, #7]
    // 0xafb278: mov             x2, x0
    // 0xafb27c: r0 = BoxInt64Instr(r3)
    //     0xafb27c: sbfiz           x0, x3, #1, #0x1f
    //     0xafb280: cmp             x3, x0, asr #1
    //     0xafb284: b.eq            #0xafb290
    //     0xafb288: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xafb28c: stur            x3, [x0, #7]
    // 0xafb290: mov             x1, x2
    // 0xafb294: mov             x2, x0
    // 0xafb298: r0 = compareTo()
    //     0xafb298: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0xafb29c: lsl             x1, x0, #1
    // 0xafb2a0: mov             x0, x1
    // 0xafb2a4: LeaveFrame
    //     0xafb2a4: mov             SP, fp
    //     0xafb2a8: ldp             fp, lr, [SP], #0x10
    // 0xafb2ac: ret
    //     0xafb2ac: ret             
    // 0xafb2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb2b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb2b4: b               #0xafb254
  }
  [closure] HomeBannerWidget <anonymous closure>(dynamic, List<Banner>) {
    // ** addr: 0xafb2b8, size: 0x68
    // 0xafb2b8: EnterFrame
    //     0xafb2b8: stp             fp, lr, [SP, #-0x10]!
    //     0xafb2bc: mov             fp, SP
    // 0xafb2c0: AllocStack(0x8)
    //     0xafb2c0: sub             SP, SP, #8
    // 0xafb2c4: SetupParameters()
    //     0xafb2c4: ldr             x0, [fp, #0x18]
    //     0xafb2c8: ldur            w1, [x0, #0x17]
    //     0xafb2cc: add             x1, x1, HEAP, lsl #32
    // 0xafb2d0: CheckStackOverflow
    //     0xafb2d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb2d4: cmp             SP, x16
    //     0xafb2d8: b.ls            #0xafb318
    // 0xafb2dc: LoadField: r0 = r1->field_f
    //     0xafb2dc: ldur            w0, [x1, #0xf]
    // 0xafb2e0: DecompressPointer r0
    //     0xafb2e0: add             x0, x0, HEAP, lsl #32
    // 0xafb2e4: LoadField: r2 = r0->field_37
    //     0xafb2e4: ldur            w2, [x0, #0x37]
    // 0xafb2e8: DecompressPointer r2
    //     0xafb2e8: add             x2, x2, HEAP, lsl #32
    // 0xafb2ec: r1 = Null
    //     0xafb2ec: mov             x1, NULL
    // 0xafb2f0: r0 = BannerOptions.fromMap()
    //     0xafb2f0: bl              #0xafb32c  ; [package:nuonline/app/data/models/banner.dart] BannerOptions::BannerOptions.fromMap
    // 0xafb2f4: stur            x0, [fp, #-8]
    // 0xafb2f8: r0 = HomeBannerWidget()
    //     0xafb2f8: bl              #0xafb320  ; AllocateHomeBannerWidgetStub -> HomeBannerWidget (size=0x14)
    // 0xafb2fc: ldr             x1, [fp, #0x10]
    // 0xafb300: StoreField: r0->field_b = r1
    //     0xafb300: stur            w1, [x0, #0xb]
    // 0xafb304: ldur            x1, [fp, #-8]
    // 0xafb308: StoreField: r0->field_f = r1
    //     0xafb308: stur            w1, [x0, #0xf]
    // 0xafb30c: LeaveFrame
    //     0xafb30c: mov             SP, fp
    //     0xafb310: ldp             fp, lr, [SP], #0x10
    // 0xafb314: ret
    //     0xafb314: ret             
    // 0xafb318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb318: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb31c: b               #0xafb2dc
  }
  [closure] Banner <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb4dc, size: 0x34
    // 0xafb4dc: EnterFrame
    //     0xafb4dc: stp             fp, lr, [SP, #-0x10]!
    //     0xafb4e0: mov             fp, SP
    // 0xafb4e4: CheckStackOverflow
    //     0xafb4e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb4e8: cmp             SP, x16
    //     0xafb4ec: b.ls            #0xafb508
    // 0xafb4f0: ldr             x2, [fp, #0x10]
    // 0xafb4f4: r1 = Null
    //     0xafb4f4: mov             x1, NULL
    // 0xafb4f8: r0 = Banner.fromMap()
    //     0xafb4f8: bl              #0xafb510  ; [package:nuonline/app/data/models/banner.dart] Banner::Banner.fromMap
    // 0xafb4fc: LeaveFrame
    //     0xafb4fc: mov             SP, fp
    //     0xafb500: ldp             fp, lr, [SP], #0x10
    // 0xafb504: ret
    //     0xafb504: ret             
    // 0xafb508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb50c: b               #0xafb4f0
  }
  [closure] HomeAdsWidget <anonymous closure>(dynamic, List<Ad>) {
    // ** addr: 0xafb6e8, size: 0x20
    // 0xafb6e8: EnterFrame
    //     0xafb6e8: stp             fp, lr, [SP, #-0x10]!
    //     0xafb6ec: mov             fp, SP
    // 0xafb6f0: r0 = HomeAdsWidget()
    //     0xafb6f0: bl              #0xafb708  ; AllocateHomeAdsWidgetStub -> HomeAdsWidget (size=0x10)
    // 0xafb6f4: ldr             x1, [fp, #0x10]
    // 0xafb6f8: StoreField: r0->field_b = r1
    //     0xafb6f8: stur            w1, [x0, #0xb]
    // 0xafb6fc: LeaveFrame
    //     0xafb6fc: mov             SP, fp
    //     0xafb700: ldp             fp, lr, [SP], #0x10
    // 0xafb704: ret
    //     0xafb704: ret             
  }
  [closure] Ad <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb714, size: 0x34
    // 0xafb714: EnterFrame
    //     0xafb714: stp             fp, lr, [SP, #-0x10]!
    //     0xafb718: mov             fp, SP
    // 0xafb71c: CheckStackOverflow
    //     0xafb71c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb720: cmp             SP, x16
    //     0xafb724: b.ls            #0xafb740
    // 0xafb728: ldr             x2, [fp, #0x10]
    // 0xafb72c: r1 = Null
    //     0xafb72c: mov             x1, NULL
    // 0xafb730: r0 = Ad.fromMap()
    //     0xafb730: bl              #0xafb748  ; [package:nuonline/app/data/models/ad.dart] Ad::Ad.fromMap
    // 0xafb734: LeaveFrame
    //     0xafb734: mov             SP, fp
    //     0xafb738: ldp             fp, lr, [SP], #0x10
    // 0xafb73c: ret
    //     0xafb73c: ret             
    // 0xafb740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb744: b               #0xafb728
  }
  [closure] HomeKalamWidget <anonymous closure>(dynamic, List<Kalam>) {
    // ** addr: 0xafb8e8, size: 0x20
    // 0xafb8e8: EnterFrame
    //     0xafb8e8: stp             fp, lr, [SP, #-0x10]!
    //     0xafb8ec: mov             fp, SP
    // 0xafb8f0: r0 = HomeKalamWidget()
    //     0xafb8f0: bl              #0xafb908  ; AllocateHomeKalamWidgetStub -> HomeKalamWidget (size=0x10)
    // 0xafb8f4: ldr             x1, [fp, #0x10]
    // 0xafb8f8: StoreField: r0->field_b = r1
    //     0xafb8f8: stur            w1, [x0, #0xb]
    // 0xafb8fc: LeaveFrame
    //     0xafb8fc: mov             SP, fp
    //     0xafb900: ldp             fp, lr, [SP], #0x10
    // 0xafb904: ret
    //     0xafb904: ret             
  }
  [closure] Kalam <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb914, size: 0x34
    // 0xafb914: EnterFrame
    //     0xafb914: stp             fp, lr, [SP, #-0x10]!
    //     0xafb918: mov             fp, SP
    // 0xafb91c: CheckStackOverflow
    //     0xafb91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb920: cmp             SP, x16
    //     0xafb924: b.ls            #0xafb940
    // 0xafb928: ldr             x2, [fp, #0x10]
    // 0xafb92c: r1 = Null
    //     0xafb92c: mov             x1, NULL
    // 0xafb930: r0 = Kalam.fromMap()
    //     0xafb930: bl              #0x8fde5c  ; [package:nuonline/app/data/models/kalam.dart] Kalam::Kalam.fromMap
    // 0xafb934: LeaveFrame
    //     0xafb934: mov             SP, fp
    //     0xafb938: ldp             fp, lr, [SP], #0x10
    // 0xafb93c: ret
    //     0xafb93c: ret             
    // 0xafb940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb944: b               #0xafb928
  }
  [closure] HomeVideosWidget <anonymous closure>(dynamic, List<Video>) {
    // ** addr: 0xafb948, size: 0x20
    // 0xafb948: EnterFrame
    //     0xafb948: stp             fp, lr, [SP, #-0x10]!
    //     0xafb94c: mov             fp, SP
    // 0xafb950: r0 = HomeVideosWidget()
    //     0xafb950: bl              #0xafb968  ; AllocateHomeVideosWidgetStub -> HomeVideosWidget (size=0x10)
    // 0xafb954: ldr             x1, [fp, #0x10]
    // 0xafb958: StoreField: r0->field_b = r1
    //     0xafb958: stur            w1, [x0, #0xb]
    // 0xafb95c: LeaveFrame
    //     0xafb95c: mov             SP, fp
    //     0xafb960: ldp             fp, lr, [SP], #0x10
    // 0xafb964: ret
    //     0xafb964: ret             
  }
  [closure] Video <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb974, size: 0x34
    // 0xafb974: EnterFrame
    //     0xafb974: stp             fp, lr, [SP, #-0x10]!
    //     0xafb978: mov             fp, SP
    // 0xafb97c: CheckStackOverflow
    //     0xafb97c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb980: cmp             SP, x16
    //     0xafb984: b.ls            #0xafb9a0
    // 0xafb988: ldr             x2, [fp, #0x10]
    // 0xafb98c: r1 = Null
    //     0xafb98c: mov             x1, NULL
    // 0xafb990: r0 = Video.fromMap()
    //     0xafb990: bl              #0x9066f4  ; [package:nuonline/app/data/models/video.dart] Video::Video.fromMap
    // 0xafb994: LeaveFrame
    //     0xafb994: mov             SP, fp
    //     0xafb998: ldp             fp, lr, [SP], #0x10
    // 0xafb99c: ret
    //     0xafb99c: ret             
    // 0xafb9a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb9a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb9a4: b               #0xafb988
  }
  [closure] HomeTopicsWidget <anonymous closure>(dynamic, List<Topic>) {
    // ** addr: 0xafb9a8, size: 0x20
    // 0xafb9a8: EnterFrame
    //     0xafb9a8: stp             fp, lr, [SP, #-0x10]!
    //     0xafb9ac: mov             fp, SP
    // 0xafb9b0: r0 = HomeTopicsWidget()
    //     0xafb9b0: bl              #0xafb9c8  ; AllocateHomeTopicsWidgetStub -> HomeTopicsWidget (size=0x10)
    // 0xafb9b4: ldr             x1, [fp, #0x10]
    // 0xafb9b8: StoreField: r0->field_b = r1
    //     0xafb9b8: stur            w1, [x0, #0xb]
    // 0xafb9bc: LeaveFrame
    //     0xafb9bc: mov             SP, fp
    //     0xafb9c0: ldp             fp, lr, [SP], #0x10
    // 0xafb9c4: ret
    //     0xafb9c4: ret             
  }
  [closure] Topic <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafba7c, size: 0x34
    // 0xafba7c: EnterFrame
    //     0xafba7c: stp             fp, lr, [SP, #-0x10]!
    //     0xafba80: mov             fp, SP
    // 0xafba84: CheckStackOverflow
    //     0xafba84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafba88: cmp             SP, x16
    //     0xafba8c: b.ls            #0xafbaa8
    // 0xafba90: ldr             x2, [fp, #0x10]
    // 0xafba94: r1 = Null
    //     0xafba94: mov             x1, NULL
    // 0xafba98: r0 = Topic.fromMap()
    //     0xafba98: bl              #0xafbab0  ; [package:nuonline/app/data/models/topic.dart] Topic::Topic.fromMap
    // 0xafba9c: LeaveFrame
    //     0xafba9c: mov             SP, fp
    //     0xafbaa0: ldp             fp, lr, [SP], #0x10
    // 0xafbaa4: ret
    //     0xafbaa4: ret             
    // 0xafbaa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafbaa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafbaac: b               #0xafba90
  }
  [closure] HomeArticlesWidget <anonymous closure>(dynamic, List<Article>) {
    // ** addr: 0xafbc94, size: 0x20
    // 0xafbc94: EnterFrame
    //     0xafbc94: stp             fp, lr, [SP, #-0x10]!
    //     0xafbc98: mov             fp, SP
    // 0xafbc9c: r0 = HomeArticlesWidget()
    //     0xafbc9c: bl              #0xafbcb4  ; AllocateHomeArticlesWidgetStub -> HomeArticlesWidget (size=0x10)
    // 0xafbca0: ldr             x1, [fp, #0x10]
    // 0xafbca4: StoreField: r0->field_b = r1
    //     0xafbca4: stur            w1, [x0, #0xb]
    // 0xafbca8: LeaveFrame
    //     0xafbca8: mov             SP, fp
    //     0xafbcac: ldp             fp, lr, [SP], #0x10
    // 0xafbcb0: ret
    //     0xafbcb0: ret             
  }
  [closure] Article <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafbcc0, size: 0x34
    // 0xafbcc0: EnterFrame
    //     0xafbcc0: stp             fp, lr, [SP, #-0x10]!
    //     0xafbcc4: mov             fp, SP
    // 0xafbcc8: CheckStackOverflow
    //     0xafbcc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafbccc: cmp             SP, x16
    //     0xafbcd0: b.ls            #0xafbcec
    // 0xafbcd4: ldr             x2, [fp, #0x10]
    // 0xafbcd8: r1 = Null
    //     0xafbcd8: mov             x1, NULL
    // 0xafbcdc: r0 = Article.fromMap()
    //     0xafbcdc: bl              #0x8ecaf4  ; [package:nuonline/app/data/models/article.dart] Article::Article.fromMap
    // 0xafbce0: LeaveFrame
    //     0xafbce0: mov             SP, fp
    //     0xafbce4: ldp             fp, lr, [SP], #0x10
    // 0xafbce8: ret
    //     0xafbce8: ret             
    // 0xafbcec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafbcec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafbcf0: b               #0xafbcd4
  }
  [closure] ListView <anonymous closure>(dynamic, List<Article>) {
    // ** addr: 0xafbcf4, size: 0x80
    // 0xafbcf4: EnterFrame
    //     0xafbcf4: stp             fp, lr, [SP, #-0x10]!
    //     0xafbcf8: mov             fp, SP
    // 0xafbcfc: AllocStack(0x28)
    //     0xafbcfc: sub             SP, SP, #0x28
    // 0xafbd00: CheckStackOverflow
    //     0xafbd00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafbd04: cmp             SP, x16
    //     0xafbd08: b.ls            #0xafbd6c
    // 0xafbd0c: r1 = Function '<anonymous closure>':.
    //     0xafbd0c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dde8] AnonymousClosure: (0xacfec4), in [package:nuonline/app/modules/muktamar/views/muktamar_view.dart] MuktamarView::build (0xb09a98)
    //     0xafbd10: ldr             x1, [x1, #0xde8]
    // 0xafbd14: r2 = Null
    //     0xafbd14: mov             x2, NULL
    // 0xafbd18: r0 = AllocateClosure()
    //     0xafbd18: bl              #0xec1630  ; AllocateClosureStub
    // 0xafbd1c: stur            x0, [fp, #-8]
    // 0xafbd20: r0 = ListView()
    //     0xafbd20: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xafbd24: stur            x0, [fp, #-0x10]
    // 0xafbd28: r16 = Instance_EdgeInsets
    //     0xafbd28: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xafbd2c: ldr             x16, [x16, #0x360]
    // 0xafbd30: r30 = Instance_NeverScrollableScrollPhysics
    //     0xafbd30: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xafbd34: ldr             lr, [lr, #0x290]
    // 0xafbd38: stp             lr, x16, [SP, #8]
    // 0xafbd3c: r16 = true
    //     0xafbd3c: add             x16, NULL, #0x20  ; true
    // 0xafbd40: str             x16, [SP]
    // 0xafbd44: mov             x1, x0
    // 0xafbd48: ldur            x2, [fp, #-8]
    // 0xafbd4c: r3 = 3
    //     0xafbd4c: movz            x3, #0x3
    // 0xafbd50: r4 = const [0, 0x6, 0x3, 0x3, padding, 0x3, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xafbd50: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2ddf0] List(11) [0, 0x6, 0x3, 0x3, "padding", 0x3, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xafbd54: ldr             x4, [x4, #0xdf0]
    // 0xafbd58: r0 = ListView.builder()
    //     0xafbd58: bl              #0xa2f6f8  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xafbd5c: ldur            x0, [fp, #-0x10]
    // 0xafbd60: LeaveFrame
    //     0xafbd60: mov             SP, fp
    //     0xafbd64: ldp             fp, lr, [SP], #0x10
    // 0xafbd68: ret
    //     0xafbd68: ret             
    // 0xafbd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafbd6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafbd70: b               #0xafbd0c
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, bool, UpcomingPrayerTime?, (dynamic) => void) {
    // ** addr: 0xafbd74, size: 0x58
    // 0xafbd74: EnterFrame
    //     0xafbd74: stp             fp, lr, [SP, #-0x10]!
    //     0xafbd78: mov             fp, SP
    // 0xafbd7c: ldr             x0, [fp, #0x20]
    // 0xafbd80: tbnz            w0, #4, #0xafbd98
    // 0xafbd84: r0 = Instance_SizedBox
    //     0xafbd84: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafbd88: ldr             x0, [x0, #0xc40]
    // 0xafbd8c: LeaveFrame
    //     0xafbd8c: mov             SP, fp
    //     0xafbd90: ldp             fp, lr, [SP], #0x10
    // 0xafbd94: ret
    //     0xafbd94: ret             
    // 0xafbd98: ldr             x1, [fp, #0x18]
    // 0xafbd9c: ldr             x0, [fp, #0x10]
    // 0xafbda0: cmp             w1, NULL
    // 0xafbda4: b.eq            #0xafbdc8
    // 0xafbda8: r0 = _HomePrayerTimeExpandedWidget()
    //     0xafbda8: bl              #0xafbdcc  ; Allocate_HomePrayerTimeExpandedWidgetStub -> _HomePrayerTimeExpandedWidget (size=0x14)
    // 0xafbdac: ldr             x1, [fp, #0x18]
    // 0xafbdb0: StoreField: r0->field_b = r1
    //     0xafbdb0: stur            w1, [x0, #0xb]
    // 0xafbdb4: ldr             x1, [fp, #0x10]
    // 0xafbdb8: StoreField: r0->field_f = r1
    //     0xafbdb8: stur            w1, [x0, #0xf]
    // 0xafbdbc: LeaveFrame
    //     0xafbdbc: mov             SP, fp
    //     0xafbdc0: ldp             fp, lr, [SP], #0x10
    // 0xafbdc4: ret
    //     0xafbdc4: ret             
    // 0xafbdc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbdc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, bool, UpcomingPrayerTime?, (dynamic) => void) {
    // ** addr: 0xafbdd8, size: 0x58
    // 0xafbdd8: EnterFrame
    //     0xafbdd8: stp             fp, lr, [SP, #-0x10]!
    //     0xafbddc: mov             fp, SP
    // 0xafbde0: ldr             x0, [fp, #0x20]
    // 0xafbde4: tbnz            w0, #4, #0xafbdfc
    // 0xafbde8: r0 = Instance_SizedBox
    //     0xafbde8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xafbdec: ldr             x0, [x0, #0xc40]
    // 0xafbdf0: LeaveFrame
    //     0xafbdf0: mov             SP, fp
    //     0xafbdf4: ldp             fp, lr, [SP], #0x10
    // 0xafbdf8: ret
    //     0xafbdf8: ret             
    // 0xafbdfc: ldr             x1, [fp, #0x18]
    // 0xafbe00: ldr             x0, [fp, #0x10]
    // 0xafbe04: cmp             w1, NULL
    // 0xafbe08: b.eq            #0xafbe2c
    // 0xafbe0c: r0 = HomePrayerTimeWidget()
    //     0xafbe0c: bl              #0xafbe30  ; AllocateHomePrayerTimeWidgetStub -> HomePrayerTimeWidget (size=0x14)
    // 0xafbe10: ldr             x1, [fp, #0x18]
    // 0xafbe14: StoreField: r0->field_b = r1
    //     0xafbe14: stur            w1, [x0, #0xb]
    // 0xafbe18: ldr             x1, [fp, #0x10]
    // 0xafbe1c: StoreField: r0->field_f = r1
    //     0xafbe1c: stur            w1, [x0, #0xf]
    // 0xafbe20: LeaveFrame
    //     0xafbe20: mov             SP, fp
    //     0xafbe24: ldp             fp, lr, [SP], #0x10
    // 0xafbe28: ret
    //     0xafbe28: ret             
    // 0xafbe2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbe2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
