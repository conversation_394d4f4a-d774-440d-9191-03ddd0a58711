// lib: , url: package:nuonline/app/modules/location/widgets/change_location_widget.dart

// class id: 1050364, size: 0x8
class :: {
}

// class id: 4988, size: 0x10, field offset: 0xc
//   const constructor, 
class ChangeLocationWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb9fd68, size: 0x258
    // 0xb9fd68: EnterFrame
    //     0xb9fd68: stp             fp, lr, [SP, #-0x10]!
    //     0xb9fd6c: mov             fp, SP
    // 0xb9fd70: AllocStack(0x28)
    //     0xb9fd70: sub             SP, SP, #0x28
    // 0xb9fd74: CheckStackOverflow
    //     0xb9fd74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9fd78: cmp             SP, x16
    //     0xb9fd7c: b.ls            #0xb9ffb0
    // 0xb9fd80: LoadField: r0 = r1->field_b
    //     0xb9fd80: ldur            w0, [x1, #0xb]
    // 0xb9fd84: DecompressPointer r0
    //     0xb9fd84: add             x0, x0, HEAP, lsl #32
    // 0xb9fd88: stur            x0, [fp, #-8]
    // 0xb9fd8c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb9fd8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9fd90: ldr             x0, [x0, #0x2670]
    //     0xb9fd94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9fd98: cmp             w0, w16
    //     0xb9fd9c: b.ne            #0xb9fda8
    //     0xb9fda0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb9fda4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb9fda8: r0 = GetNavigation.textTheme()
    //     0xb9fda8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb9fdac: LoadField: r1 = r0->field_27
    //     0xb9fdac: ldur            w1, [x0, #0x27]
    // 0xb9fdb0: DecompressPointer r1
    //     0xb9fdb0: add             x1, x1, HEAP, lsl #32
    // 0xb9fdb4: cmp             w1, NULL
    // 0xb9fdb8: b.eq            #0xb9ffb8
    // 0xb9fdbc: r16 = 13.000000
    //     0xb9fdbc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc0] 13
    //     0xb9fdc0: ldr             x16, [x16, #0xfc0]
    // 0xb9fdc4: str             x16, [SP]
    // 0xb9fdc8: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb9fdc8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb9fdcc: ldr             x4, [x4, #0x88]
    // 0xb9fdd0: r0 = copyWith()
    //     0xb9fdd0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9fdd4: stur            x0, [fp, #-0x10]
    // 0xb9fdd8: r0 = Text()
    //     0xb9fdd8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb9fddc: mov             x1, x0
    // 0xb9fde0: ldur            x0, [fp, #-8]
    // 0xb9fde4: stur            x1, [fp, #-0x18]
    // 0xb9fde8: StoreField: r1->field_b = r0
    //     0xb9fde8: stur            w0, [x1, #0xb]
    // 0xb9fdec: ldur            x0, [fp, #-0x10]
    // 0xb9fdf0: StoreField: r1->field_13 = r0
    //     0xb9fdf0: stur            w0, [x1, #0x13]
    // 0xb9fdf4: r0 = Instance_TextOverflow
    //     0xb9fdf4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27888] Obj!TextOverflow@e35cc1
    //     0xb9fdf8: ldr             x0, [x0, #0x888]
    // 0xb9fdfc: StoreField: r1->field_2b = r0
    //     0xb9fdfc: stur            w0, [x1, #0x2b]
    // 0xb9fe00: r0 = Instance__LinearTextScaler
    //     0xb9fe00: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb9fe04: StoreField: r1->field_33 = r0
    //     0xb9fe04: stur            w0, [x1, #0x33]
    // 0xb9fe08: r2 = 2
    //     0xb9fe08: movz            x2, #0x2
    // 0xb9fe0c: StoreField: r1->field_37 = r2
    //     0xb9fe0c: stur            w2, [x1, #0x37]
    // 0xb9fe10: r0 = GetNavigation.textTheme()
    //     0xb9fe10: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb9fe14: LoadField: r1 = r0->field_2f
    //     0xb9fe14: ldur            w1, [x0, #0x2f]
    // 0xb9fe18: DecompressPointer r1
    //     0xb9fe18: add             x1, x1, HEAP, lsl #32
    // 0xb9fe1c: stur            x1, [fp, #-8]
    // 0xb9fe20: cmp             w1, NULL
    // 0xb9fe24: b.eq            #0xb9ffbc
    // 0xb9fe28: r0 = GetNavigation.theme()
    //     0xb9fe28: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb9fe2c: LoadField: r1 = r0->field_3f
    //     0xb9fe2c: ldur            w1, [x0, #0x3f]
    // 0xb9fe30: DecompressPointer r1
    //     0xb9fe30: add             x1, x1, HEAP, lsl #32
    // 0xb9fe34: LoadField: r0 = r1->field_2b
    //     0xb9fe34: ldur            w0, [x1, #0x2b]
    // 0xb9fe38: DecompressPointer r0
    //     0xb9fe38: add             x0, x0, HEAP, lsl #32
    // 0xb9fe3c: r16 = 13.000000
    //     0xb9fe3c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc0] 13
    //     0xb9fe40: ldr             x16, [x16, #0xfc0]
    // 0xb9fe44: stp             x16, x0, [SP]
    // 0xb9fe48: ldur            x1, [fp, #-8]
    // 0xb9fe4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9fe4c: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bad8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9fe50: ldr             x4, [x4, #0xad8]
    // 0xb9fe54: r0 = copyWith()
    //     0xb9fe54: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9fe58: stur            x0, [fp, #-8]
    // 0xb9fe5c: r0 = Text()
    //     0xb9fe5c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb9fe60: mov             x1, x0
    // 0xb9fe64: r0 = "(Ganti)"
    //     0xb9fe64: add             x0, PP, #0x31, lsl #12  ; [pp+0x31518] "(Ganti)"
    //     0xb9fe68: ldr             x0, [x0, #0x518]
    // 0xb9fe6c: stur            x1, [fp, #-0x10]
    // 0xb9fe70: StoreField: r1->field_b = r0
    //     0xb9fe70: stur            w0, [x1, #0xb]
    // 0xb9fe74: ldur            x0, [fp, #-8]
    // 0xb9fe78: StoreField: r1->field_13 = r0
    //     0xb9fe78: stur            w0, [x1, #0x13]
    // 0xb9fe7c: r0 = Instance__LinearTextScaler
    //     0xb9fe7c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb9fe80: StoreField: r1->field_33 = r0
    //     0xb9fe80: stur            w0, [x1, #0x33]
    // 0xb9fe84: r0 = InkWell()
    //     0xb9fe84: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb9fe88: mov             x3, x0
    // 0xb9fe8c: ldur            x0, [fp, #-0x10]
    // 0xb9fe90: stur            x3, [fp, #-8]
    // 0xb9fe94: StoreField: r3->field_b = r0
    //     0xb9fe94: stur            w0, [x3, #0xb]
    // 0xb9fe98: r0 = Closure: () => void from Function 'show': static.
    //     0xb9fe98: add             x0, PP, #0x35, lsl #12  ; [pp+0x35990] Closure: () => void from Function 'show': static. (0x7e54fb51b3b0)
    //     0xb9fe9c: ldr             x0, [x0, #0x990]
    // 0xb9fea0: StoreField: r3->field_f = r0
    //     0xb9fea0: stur            w0, [x3, #0xf]
    // 0xb9fea4: r0 = true
    //     0xb9fea4: add             x0, NULL, #0x20  ; true
    // 0xb9fea8: StoreField: r3->field_43 = r0
    //     0xb9fea8: stur            w0, [x3, #0x43]
    // 0xb9feac: r1 = Instance_BoxShape
    //     0xb9feac: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb9feb0: ldr             x1, [x1, #0xca8]
    // 0xb9feb4: StoreField: r3->field_47 = r1
    //     0xb9feb4: stur            w1, [x3, #0x47]
    // 0xb9feb8: StoreField: r3->field_6f = r0
    //     0xb9feb8: stur            w0, [x3, #0x6f]
    // 0xb9febc: r1 = false
    //     0xb9febc: add             x1, NULL, #0x30  ; false
    // 0xb9fec0: StoreField: r3->field_73 = r1
    //     0xb9fec0: stur            w1, [x3, #0x73]
    // 0xb9fec4: StoreField: r3->field_83 = r0
    //     0xb9fec4: stur            w0, [x3, #0x83]
    // 0xb9fec8: StoreField: r3->field_7b = r1
    //     0xb9fec8: stur            w1, [x3, #0x7b]
    // 0xb9fecc: r1 = Null
    //     0xb9fecc: mov             x1, NULL
    // 0xb9fed0: r2 = 10
    //     0xb9fed0: movz            x2, #0xa
    // 0xb9fed4: r0 = AllocateArray()
    //     0xb9fed4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb9fed8: stur            x0, [fp, #-0x10]
    // 0xb9fedc: r16 = Instance_Icon
    //     0xb9fedc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c8b8] Obj!Icon@e24731
    //     0xb9fee0: ldr             x16, [x16, #0x8b8]
    // 0xb9fee4: StoreField: r0->field_f = r16
    //     0xb9fee4: stur            w16, [x0, #0xf]
    // 0xb9fee8: r16 = Instance_SizedBox
    //     0xb9fee8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xb9feec: ldr             x16, [x16, #0xbd8]
    // 0xb9fef0: StoreField: r0->field_13 = r16
    //     0xb9fef0: stur            w16, [x0, #0x13]
    // 0xb9fef4: ldur            x1, [fp, #-0x18]
    // 0xb9fef8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb9fef8: stur            w1, [x0, #0x17]
    // 0xb9fefc: r16 = Instance_SizedBox
    //     0xb9fefc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xb9ff00: ldr             x16, [x16, #0xbd8]
    // 0xb9ff04: StoreField: r0->field_1b = r16
    //     0xb9ff04: stur            w16, [x0, #0x1b]
    // 0xb9ff08: ldur            x1, [fp, #-8]
    // 0xb9ff0c: StoreField: r0->field_1f = r1
    //     0xb9ff0c: stur            w1, [x0, #0x1f]
    // 0xb9ff10: r1 = <Widget>
    //     0xb9ff10: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb9ff14: r0 = AllocateGrowableArray()
    //     0xb9ff14: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb9ff18: mov             x1, x0
    // 0xb9ff1c: ldur            x0, [fp, #-0x10]
    // 0xb9ff20: stur            x1, [fp, #-8]
    // 0xb9ff24: StoreField: r1->field_f = r0
    //     0xb9ff24: stur            w0, [x1, #0xf]
    // 0xb9ff28: r0 = 10
    //     0xb9ff28: movz            x0, #0xa
    // 0xb9ff2c: StoreField: r1->field_b = r0
    //     0xb9ff2c: stur            w0, [x1, #0xb]
    // 0xb9ff30: r0 = Row()
    //     0xb9ff30: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9ff34: mov             x1, x0
    // 0xb9ff38: r0 = Instance_Axis
    //     0xb9ff38: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb9ff3c: stur            x1, [fp, #-0x10]
    // 0xb9ff40: StoreField: r1->field_f = r0
    //     0xb9ff40: stur            w0, [x1, #0xf]
    // 0xb9ff44: r0 = Instance_MainAxisAlignment
    //     0xb9ff44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xb9ff48: ldr             x0, [x0, #0x290]
    // 0xb9ff4c: StoreField: r1->field_13 = r0
    //     0xb9ff4c: stur            w0, [x1, #0x13]
    // 0xb9ff50: r0 = Instance_MainAxisSize
    //     0xb9ff50: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb9ff54: ldr             x0, [x0, #0xe88]
    // 0xb9ff58: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9ff58: stur            w0, [x1, #0x17]
    // 0xb9ff5c: r0 = Instance_CrossAxisAlignment
    //     0xb9ff5c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb9ff60: ldr             x0, [x0, #0x740]
    // 0xb9ff64: StoreField: r1->field_1b = r0
    //     0xb9ff64: stur            w0, [x1, #0x1b]
    // 0xb9ff68: r0 = Instance_VerticalDirection
    //     0xb9ff68: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb9ff6c: ldr             x0, [x0, #0x748]
    // 0xb9ff70: StoreField: r1->field_23 = r0
    //     0xb9ff70: stur            w0, [x1, #0x23]
    // 0xb9ff74: r0 = Instance_Clip
    //     0xb9ff74: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb9ff78: ldr             x0, [x0, #0x750]
    // 0xb9ff7c: StoreField: r1->field_2b = r0
    //     0xb9ff7c: stur            w0, [x1, #0x2b]
    // 0xb9ff80: StoreField: r1->field_2f = rZR
    //     0xb9ff80: stur            xzr, [x1, #0x2f]
    // 0xb9ff84: ldur            x0, [fp, #-8]
    // 0xb9ff88: StoreField: r1->field_b = r0
    //     0xb9ff88: stur            w0, [x1, #0xb]
    // 0xb9ff8c: r0 = Padding()
    //     0xb9ff8c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9ff90: r1 = Instance_EdgeInsets
    //     0xb9ff90: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d470] Obj!EdgeInsets@e136c1
    //     0xb9ff94: ldr             x1, [x1, #0x470]
    // 0xb9ff98: StoreField: r0->field_f = r1
    //     0xb9ff98: stur            w1, [x0, #0xf]
    // 0xb9ff9c: ldur            x1, [fp, #-0x10]
    // 0xb9ffa0: StoreField: r0->field_b = r1
    //     0xb9ffa0: stur            w1, [x0, #0xb]
    // 0xb9ffa4: LeaveFrame
    //     0xb9ffa4: mov             SP, fp
    //     0xb9ffa8: ldp             fp, lr, [SP], #0x10
    // 0xb9ffac: ret
    //     0xb9ffac: ret             
    // 0xb9ffb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9ffb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9ffb4: b               #0xb9fd80
    // 0xb9ffb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9ffb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9ffbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9ffbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
