// lib: , url: package:nuonline/app/modules/location/widgets/select_location_widget.dart

// class id: 1050369, size: 0x8
class :: {
}

// class id: 4984, size: 0x18, field offset: 0xc
//   const constructor, 
class SelectLocationWidget extends StatelessWidget {

  static void show() {
    // ** addr: 0xb1b354, size: 0x5c
    // 0xb1b354: EnterFrame
    //     0xb1b354: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b358: mov             fp, SP
    // 0xb1b35c: AllocStack(0x10)
    //     0xb1b35c: sub             SP, SP, #0x10
    // 0xb1b360: CheckStackOverflow
    //     0xb1b360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b364: cmp             SP, x16
    //     0xb1b368: b.ls            #0xb1b3a8
    // 0xb1b36c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1b36c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1b370: ldr             x0, [x0, #0x2670]
    //     0xb1b374: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1b378: cmp             w0, w16
    //     0xb1b37c: b.ne            #0xb1b388
    //     0xb1b380: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1b384: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1b388: r0 = _sheet()
    //     0xb1b388: bl              #0xb1b3dc  ; [package:nuonline/app/modules/location/widgets/select_location_widget.dart] SelectLocationWidget::_sheet
    // 0xb1b38c: stp             x0, NULL, [SP]
    // 0xb1b390: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1b390: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1b394: r0 = ExtensionBottomSheet.bottomSheet()
    //     0xb1b394: bl              #0x917724  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionBottomSheet.bottomSheet
    // 0xb1b398: r0 = Null
    //     0xb1b398: mov             x0, NULL
    // 0xb1b39c: LeaveFrame
    //     0xb1b39c: mov             SP, fp
    //     0xb1b3a0: ldp             fp, lr, [SP], #0x10
    // 0xb1b3a4: ret
    //     0xb1b3a4: ret             
    // 0xb1b3a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b3a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b3ac: b               #0xb1b36c
  }
  [closure] static void show(dynamic) {
    // ** addr: 0xb1b3b0, size: 0x2c
    // 0xb1b3b0: EnterFrame
    //     0xb1b3b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b3b4: mov             fp, SP
    // 0xb1b3b8: CheckStackOverflow
    //     0xb1b3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b3bc: cmp             SP, x16
    //     0xb1b3c0: b.ls            #0xb1b3d4
    // 0xb1b3c4: r0 = show()
    //     0xb1b3c4: bl              #0xb1b354  ; [package:nuonline/app/modules/location/widgets/select_location_widget.dart] SelectLocationWidget::show
    // 0xb1b3c8: LeaveFrame
    //     0xb1b3c8: mov             SP, fp
    //     0xb1b3cc: ldp             fp, lr, [SP], #0x10
    // 0xb1b3d0: ret
    //     0xb1b3d0: ret             
    // 0xb1b3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b3d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b3d8: b               #0xb1b3c4
  }
  get _ _sheet(/* No info */) {
    // ** addr: 0xb1b3dc, size: 0x104
    // 0xb1b3dc: EnterFrame
    //     0xb1b3dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b3e0: mov             fp, SP
    // 0xb1b3e4: AllocStack(0x28)
    //     0xb1b3e4: sub             SP, SP, #0x28
    // 0xb1b3e8: CheckStackOverflow
    //     0xb1b3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1b3ec: cmp             SP, x16
    //     0xb1b3f0: b.ls            #0xb1b4d8
    // 0xb1b3f4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1b3f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1b3f8: ldr             x0, [x0, #0x2670]
    //     0xb1b3fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1b400: cmp             w0, w16
    //     0xb1b404: b.ne            #0xb1b410
    //     0xb1b408: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1b40c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1b410: r16 = <LocationRepository>
    //     0xb1b410: add             x16, PP, #0xf, lsl #12  ; [pp+0xff78] TypeArguments: <LocationRepository>
    //     0xb1b414: ldr             x16, [x16, #0xf78]
    // 0xb1b418: r30 = "location_repo"
    //     0xb1b418: add             lr, PP, #0xf, lsl #12  ; [pp+0xff80] "location_repo"
    //     0xb1b41c: ldr             lr, [lr, #0xf80]
    // 0xb1b420: stp             lr, x16, [SP]
    // 0xb1b424: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb1b424: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb1b428: r0 = Inst.find()
    //     0xb1b428: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb1b42c: r1 = false
    //     0xb1b42c: add             x1, NULL, #0x30  ; false
    // 0xb1b430: stur            x0, [fp, #-8]
    // 0xb1b434: r0 = BoolExtension.obs()
    //     0xb1b434: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0xb1b438: stur            x0, [fp, #-0x10]
    // 0xb1b43c: r0 = SelectLocationController()
    //     0xb1b43c: bl              #0xb1b4e0  ; AllocateSelectLocationControllerStub -> SelectLocationController (size=0x28)
    // 0xb1b440: mov             x2, x0
    // 0xb1b444: ldur            x0, [fp, #-0x10]
    // 0xb1b448: stur            x2, [fp, #-0x18]
    // 0xb1b44c: StoreField: r2->field_23 = r0
    //     0xb1b44c: stur            w0, [x2, #0x23]
    // 0xb1b450: ldur            x0, [fp, #-8]
    // 0xb1b454: StoreField: r2->field_1f = r0
    //     0xb1b454: stur            w0, [x2, #0x1f]
    // 0xb1b458: mov             x1, x2
    // 0xb1b45c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb1b45c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb1b460: r1 = <SelectLocationController>
    //     0xb1b460: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c358] TypeArguments: <SelectLocationController>
    //     0xb1b464: ldr             x1, [x1, #0x358]
    // 0xb1b468: r0 = GetBuilder()
    //     0xb1b468: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb1b46c: mov             x3, x0
    // 0xb1b470: ldur            x0, [fp, #-0x18]
    // 0xb1b474: stur            x3, [fp, #-8]
    // 0xb1b478: StoreField: r3->field_3b = r0
    //     0xb1b478: stur            w0, [x3, #0x3b]
    // 0xb1b47c: r0 = true
    //     0xb1b47c: add             x0, NULL, #0x20  ; true
    // 0xb1b480: StoreField: r3->field_13 = r0
    //     0xb1b480: stur            w0, [x3, #0x13]
    // 0xb1b484: r1 = Function '<anonymous closure>': static.
    //     0xb1b484: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c360] AnonymousClosure: static (0xb1b4ec), in [package:nuonline/app/modules/location/widgets/select_location_widget.dart] SelectLocationWidget::_sheet (0xb1b3dc)
    //     0xb1b488: ldr             x1, [x1, #0x360]
    // 0xb1b48c: r2 = Null
    //     0xb1b48c: mov             x2, NULL
    // 0xb1b490: r0 = AllocateClosure()
    //     0xb1b490: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1b494: mov             x1, x0
    // 0xb1b498: ldur            x0, [fp, #-8]
    // 0xb1b49c: StoreField: r0->field_f = r1
    //     0xb1b49c: stur            w1, [x0, #0xf]
    // 0xb1b4a0: r1 = true
    //     0xb1b4a0: add             x1, NULL, #0x20  ; true
    // 0xb1b4a4: StoreField: r0->field_1f = r1
    //     0xb1b4a4: stur            w1, [x0, #0x1f]
    // 0xb1b4a8: r1 = false
    //     0xb1b4a8: add             x1, NULL, #0x30  ; false
    // 0xb1b4ac: StoreField: r0->field_23 = r1
    //     0xb1b4ac: stur            w1, [x0, #0x23]
    // 0xb1b4b0: r1 = Function '<anonymous closure>': static.
    //     0xb1b4b0: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c368] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xb1b4b4: ldr             x1, [x1, #0x368]
    // 0xb1b4b8: r2 = Null
    //     0xb1b4b8: mov             x2, NULL
    // 0xb1b4bc: r0 = AllocateClosure()
    //     0xb1b4bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1b4c0: mov             x1, x0
    // 0xb1b4c4: ldur            x0, [fp, #-8]
    // 0xb1b4c8: StoreField: r0->field_2b = r1
    //     0xb1b4c8: stur            w1, [x0, #0x2b]
    // 0xb1b4cc: LeaveFrame
    //     0xb1b4cc: mov             SP, fp
    //     0xb1b4d0: ldp             fp, lr, [SP], #0x10
    // 0xb1b4d4: ret
    //     0xb1b4d4: ret             
    // 0xb1b4d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1b4d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1b4dc: b               #0xb1b3f4
  }
  [closure] static NLocationBottomSheet <anonymous closure>(dynamic, SelectLocationController) {
    // ** addr: 0xb1b4ec, size: 0x5c
    // 0xb1b4ec: EnterFrame
    //     0xb1b4ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb1b4f0: mov             fp, SP
    // 0xb1b4f4: AllocStack(0x10)
    //     0xb1b4f4: sub             SP, SP, #0x10
    // 0xb1b4f8: ldr             x2, [fp, #0x10]
    // 0xb1b4fc: r1 = Function 'onFindLocation':.
    //     0xb1b4fc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c370] AnonymousClosure: (0xb1bb20), in [package:nuonline/app/modules/location/controllers/select_location_controller.dart] SelectLocationController::onFindLocation (0xb1bb58)
    //     0xb1b500: ldr             x1, [x1, #0x370]
    // 0xb1b504: r0 = AllocateClosure()
    //     0xb1b504: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1b508: stur            x0, [fp, #-8]
    // 0xb1b50c: r0 = NLocationBottomSheet()
    //     0xb1b50c: bl              #0xb1b548  ; AllocateNLocationBottomSheetStub -> NLocationBottomSheet (size=0x14)
    // 0xb1b510: mov             x3, x0
    // 0xb1b514: ldur            x0, [fp, #-8]
    // 0xb1b518: stur            x3, [fp, #-0x10]
    // 0xb1b51c: StoreField: r3->field_b = r0
    //     0xb1b51c: stur            w0, [x3, #0xb]
    // 0xb1b520: ldr             x2, [fp, #0x10]
    // 0xb1b524: r1 = Function 'onFindCurrentLocation':.
    //     0xb1b524: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c378] AnonymousClosure: (0xb1b554), in [package:nuonline/app/modules/location/controllers/select_location_controller.dart] SelectLocationController::onFindCurrentLocation (0xb1b58c)
    //     0xb1b528: ldr             x1, [x1, #0x378]
    // 0xb1b52c: r0 = AllocateClosure()
    //     0xb1b52c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1b530: mov             x1, x0
    // 0xb1b534: ldur            x0, [fp, #-0x10]
    // 0xb1b538: StoreField: r0->field_f = r1
    //     0xb1b538: stur            w1, [x0, #0xf]
    // 0xb1b53c: LeaveFrame
    //     0xb1b53c: mov             SP, fp
    //     0xb1b540: ldp             fp, lr, [SP], #0x10
    // 0xb1b544: ret
    //     0xb1b544: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xba0c30, size: 0x1c0
    // 0xba0c30: EnterFrame
    //     0xba0c30: stp             fp, lr, [SP, #-0x10]!
    //     0xba0c34: mov             fp, SP
    // 0xba0c38: AllocStack(0x20)
    //     0xba0c38: sub             SP, SP, #0x20
    // 0xba0c3c: SetupParameters(SelectLocationWidget this /* r1 => r0, fp-0x8 */)
    //     0xba0c3c: mov             x0, x1
    //     0xba0c40: stur            x1, [fp, #-8]
    // 0xba0c44: CheckStackOverflow
    //     0xba0c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba0c48: cmp             SP, x16
    //     0xba0c4c: b.ls            #0xba0de8
    // 0xba0c50: r1 = Null
    //     0xba0c50: mov             x1, NULL
    // 0xba0c54: r2 = 8
    //     0xba0c54: movz            x2, #0x8
    // 0xba0c58: r0 = AllocateArray()
    //     0xba0c58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba0c5c: stur            x0, [fp, #-0x18]
    // 0xba0c60: r16 = "Koordinat: "
    //     0xba0c60: add             x16, PP, #0x35, lsl #12  ; [pp+0x35968] "Koordinat: "
    //     0xba0c64: ldr             x16, [x16, #0x968]
    // 0xba0c68: StoreField: r0->field_f = r16
    //     0xba0c68: stur            w16, [x0, #0xf]
    // 0xba0c6c: ldur            x3, [fp, #-8]
    // 0xba0c70: LoadField: r1 = r3->field_f
    //     0xba0c70: ldur            w1, [x3, #0xf]
    // 0xba0c74: DecompressPointer r1
    //     0xba0c74: add             x1, x1, HEAP, lsl #32
    // 0xba0c78: StoreField: r0->field_13 = r1
    //     0xba0c78: stur            w1, [x0, #0x13]
    // 0xba0c7c: r16 = "\nKetinggian: "
    //     0xba0c7c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35970] "\nKetinggian: "
    //     0xba0c80: ldr             x16, [x16, #0x970]
    // 0xba0c84: ArrayStore: r0[0] = r16  ; List_4
    //     0xba0c84: stur            w16, [x0, #0x17]
    // 0xba0c88: LoadField: r4 = r3->field_13
    //     0xba0c88: ldur            w4, [x3, #0x13]
    // 0xba0c8c: DecompressPointer r4
    //     0xba0c8c: add             x4, x4, HEAP, lsl #32
    // 0xba0c90: stur            x4, [fp, #-0x10]
    // 0xba0c94: cmp             w4, NULL
    // 0xba0c98: b.ne            #0xba0ca8
    // 0xba0c9c: mov             x2, x3
    // 0xba0ca0: r0 = "-"
    //     0xba0ca0: ldr             x0, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xba0ca4: b               #0xba0cd8
    // 0xba0ca8: r1 = Null
    //     0xba0ca8: mov             x1, NULL
    // 0xba0cac: r2 = 4
    //     0xba0cac: movz            x2, #0x4
    // 0xba0cb0: r0 = AllocateArray()
    //     0xba0cb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba0cb4: mov             x1, x0
    // 0xba0cb8: ldur            x0, [fp, #-0x10]
    // 0xba0cbc: StoreField: r1->field_f = r0
    //     0xba0cbc: stur            w0, [x1, #0xf]
    // 0xba0cc0: r16 = " meter"
    //     0xba0cc0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35978] " meter"
    //     0xba0cc4: ldr             x16, [x16, #0x978]
    // 0xba0cc8: StoreField: r1->field_13 = r16
    //     0xba0cc8: stur            w16, [x1, #0x13]
    // 0xba0ccc: str             x1, [SP]
    // 0xba0cd0: r0 = _interpolate()
    //     0xba0cd0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xba0cd4: ldur            x2, [fp, #-8]
    // 0xba0cd8: ldur            x1, [fp, #-0x18]
    // 0xba0cdc: ArrayStore: r1[3] = r0  ; List_4
    //     0xba0cdc: add             x25, x1, #0x1b
    //     0xba0ce0: str             w0, [x25]
    //     0xba0ce4: tbz             w0, #0, #0xba0d00
    //     0xba0ce8: ldurb           w16, [x1, #-1]
    //     0xba0cec: ldurb           w17, [x0, #-1]
    //     0xba0cf0: and             x16, x17, x16, lsr #2
    //     0xba0cf4: tst             x16, HEAP, lsr #32
    //     0xba0cf8: b.eq            #0xba0d00
    //     0xba0cfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xba0d00: ldur            x16, [fp, #-0x18]
    // 0xba0d04: str             x16, [SP]
    // 0xba0d08: r0 = _interpolate()
    //     0xba0d08: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xba0d0c: mov             x1, x0
    // 0xba0d10: ldur            x0, [fp, #-8]
    // 0xba0d14: stur            x1, [fp, #-0x10]
    // 0xba0d18: LoadField: r2 = r0->field_b
    //     0xba0d18: ldur            w2, [x0, #0xb]
    // 0xba0d1c: DecompressPointer r2
    //     0xba0d1c: add             x2, x2, HEAP, lsl #32
    // 0xba0d20: cmp             w2, NULL
    // 0xba0d24: b.ne            #0xba0d34
    // 0xba0d28: r0 = "Belum dipilih"
    //     0xba0d28: add             x0, PP, #0x35, lsl #12  ; [pp+0x35980] "Belum dipilih"
    //     0xba0d2c: ldr             x0, [x0, #0x980]
    // 0xba0d30: b               #0xba0d38
    // 0xba0d34: mov             x0, x2
    // 0xba0d38: stur            x0, [fp, #-8]
    // 0xba0d3c: r0 = NInlineListTile()
    //     0xba0d3c: bl              #0xa3d3ec  ; AllocateNInlineListTileStub -> NInlineListTile (size=0x1c)
    // 0xba0d40: mov             x3, x0
    // 0xba0d44: r0 = "Pilih Lokasi"
    //     0xba0d44: add             x0, PP, #0x35, lsl #12  ; [pp+0x35988] "Pilih Lokasi"
    //     0xba0d48: ldr             x0, [x0, #0x988]
    // 0xba0d4c: stur            x3, [fp, #-0x18]
    // 0xba0d50: StoreField: r3->field_b = r0
    //     0xba0d50: stur            w0, [x3, #0xb]
    // 0xba0d54: ldur            x0, [fp, #-8]
    // 0xba0d58: StoreField: r3->field_f = r0
    //     0xba0d58: stur            w0, [x3, #0xf]
    // 0xba0d5c: r0 = Closure: () => void from Function 'show': static.
    //     0xba0d5c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35990] Closure: () => void from Function 'show': static. (0x7e54fb51b3b0)
    //     0xba0d60: ldr             x0, [x0, #0x990]
    // 0xba0d64: StoreField: r3->field_13 = r0
    //     0xba0d64: stur            w0, [x3, #0x13]
    // 0xba0d68: r1 = Null
    //     0xba0d68: mov             x1, NULL
    // 0xba0d6c: r2 = 2
    //     0xba0d6c: movz            x2, #0x2
    // 0xba0d70: r0 = AllocateArray()
    //     0xba0d70: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba0d74: mov             x2, x0
    // 0xba0d78: ldur            x0, [fp, #-0x18]
    // 0xba0d7c: stur            x2, [fp, #-8]
    // 0xba0d80: StoreField: r2->field_f = r0
    //     0xba0d80: stur            w0, [x2, #0xf]
    // 0xba0d84: r1 = <Widget>
    //     0xba0d84: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xba0d88: r0 = AllocateGrowableArray()
    //     0xba0d88: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xba0d8c: mov             x1, x0
    // 0xba0d90: ldur            x0, [fp, #-8]
    // 0xba0d94: stur            x1, [fp, #-0x18]
    // 0xba0d98: StoreField: r1->field_f = r0
    //     0xba0d98: stur            w0, [x1, #0xf]
    // 0xba0d9c: r0 = 2
    //     0xba0d9c: movz            x0, #0x2
    // 0xba0da0: StoreField: r1->field_b = r0
    //     0xba0da0: stur            w0, [x1, #0xb]
    // 0xba0da4: r0 = NSection()
    //     0xba0da4: bl              #0xa37548  ; AllocateNSectionStub -> NSection (size=0x38)
    // 0xba0da8: r1 = "Lokasi Waktu Shalat"
    //     0xba0da8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35998] "Lokasi Waktu Shalat"
    //     0xba0dac: ldr             x1, [x1, #0x998]
    // 0xba0db0: StoreField: r0->field_b = r1
    //     0xba0db0: stur            w1, [x0, #0xb]
    // 0xba0db4: ldur            x1, [fp, #-0x18]
    // 0xba0db8: StoreField: r0->field_f = r1
    //     0xba0db8: stur            w1, [x0, #0xf]
    // 0xba0dbc: ldur            x1, [fp, #-0x10]
    // 0xba0dc0: ArrayStore: r0[0] = r1  ; List_4
    //     0xba0dc0: stur            w1, [x0, #0x17]
    // 0xba0dc4: r1 = "Data ketinggian diperoleh otomatis dari koordinat lokasi Anda, sehingga waktu Maghrib ditampilkan lebih akurat."
    //     0xba0dc4: add             x1, PP, #0x35, lsl #12  ; [pp+0x359a0] "Data ketinggian diperoleh otomatis dari koordinat lokasi Anda, sehingga waktu Maghrib ditampilkan lebih akurat."
    //     0xba0dc8: ldr             x1, [x1, #0x9a0]
    // 0xba0dcc: StoreField: r0->field_1b = r1
    //     0xba0dcc: stur            w1, [x0, #0x1b]
    // 0xba0dd0: r1 = false
    //     0xba0dd0: add             x1, NULL, #0x30  ; false
    // 0xba0dd4: StoreField: r0->field_27 = r1
    //     0xba0dd4: stur            w1, [x0, #0x27]
    // 0xba0dd8: StoreField: r0->field_2b = r1
    //     0xba0dd8: stur            w1, [x0, #0x2b]
    // 0xba0ddc: LeaveFrame
    //     0xba0ddc: mov             SP, fp
    //     0xba0de0: ldp             fp, lr, [SP], #0x10
    // 0xba0de4: ret
    //     0xba0de4: ret             
    // 0xba0de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba0de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba0dec: b               #0xba0c50
  }
}
