// lib: , url: package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart

// class id: 1050410, size: 0x8
class :: {
}

// class id: 4977, size: 0x10, field offset: 0xc
//   const constructor, 
class PrayerTimeMothodInformation extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba2750, size: 0x34c
    // 0xba2750: EnterFrame
    //     0xba2750: stp             fp, lr, [SP, #-0x10]!
    //     0xba2754: mov             fp, SP
    // 0xba2758: AllocStack(0x30)
    //     0xba2758: sub             SP, SP, #0x30
    // 0xba275c: CheckStackOverflow
    //     0xba275c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba2760: cmp             SP, x16
    //     0xba2764: b.ls            #0xba2a94
    // 0xba2768: LoadField: r2 = r1->field_b
    //     0xba2768: ldur            w2, [x1, #0xb]
    // 0xba276c: DecompressPointer r2
    //     0xba276c: add             x2, x2, HEAP, lsl #32
    // 0xba2770: stur            x2, [fp, #-8]
    // 0xba2774: r0 = LoadClassIdInstr(r2)
    //     0xba2774: ldur            x0, [x2, #-1]
    //     0xba2778: ubfx            x0, x0, #0xc, #0x14
    // 0xba277c: r16 = "falakiyah_nu"
    //     0xba277c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf300] "falakiyah_nu"
    //     0xba2780: ldr             x16, [x16, #0x300]
    // 0xba2784: stp             x16, x2, [SP]
    // 0xba2788: mov             lr, x0
    // 0xba278c: ldr             lr, [x21, lr, lsl #3]
    // 0xba2790: blr             lr
    // 0xba2794: tbnz            w0, #4, #0xba27a4
    // 0xba2798: r0 = "Waktu Shalat telah ditashih oleh"
    //     0xba2798: add             x0, PP, #0x34, lsl #12  ; [pp+0x34158] "Waktu Shalat telah ditashih oleh"
    //     0xba279c: ldr             x0, [x0, #0x158]
    // 0xba27a0: b               #0xba27ac
    // 0xba27a4: r0 = "Metode perhitungan waktu shalat"
    //     0xba27a4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34160] "Metode perhitungan waktu shalat"
    //     0xba27a8: ldr             x0, [x0, #0x160]
    // 0xba27ac: stur            x0, [fp, #-0x10]
    // 0xba27b0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba27b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba27b4: ldr             x0, [x0, #0x2670]
    //     0xba27b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba27bc: cmp             w0, w16
    //     0xba27c0: b.ne            #0xba27cc
    //     0xba27c4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xba27c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xba27cc: r0 = GetNavigation.textTheme()
    //     0xba27cc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xba27d0: LoadField: r1 = r0->field_23
    //     0xba27d0: ldur            w1, [x0, #0x23]
    // 0xba27d4: DecompressPointer r1
    //     0xba27d4: add             x1, x1, HEAP, lsl #32
    // 0xba27d8: cmp             w1, NULL
    // 0xba27dc: b.ne            #0xba27e8
    // 0xba27e0: r2 = Null
    //     0xba27e0: mov             x2, NULL
    // 0xba27e4: b               #0xba2804
    // 0xba27e8: r16 = 14.000000
    //     0xba27e8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xba27ec: ldr             x16, [x16, #0x9a0]
    // 0xba27f0: str             x16, [SP]
    // 0xba27f4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xba27f4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xba27f8: ldr             x4, [x4, #0x88]
    // 0xba27fc: r0 = copyWith()
    //     0xba27fc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2800: mov             x2, x0
    // 0xba2804: ldur            x1, [fp, #-8]
    // 0xba2808: ldur            x0, [fp, #-0x10]
    // 0xba280c: stur            x2, [fp, #-0x18]
    // 0xba2810: r0 = Text()
    //     0xba2810: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xba2814: mov             x1, x0
    // 0xba2818: ldur            x0, [fp, #-0x10]
    // 0xba281c: stur            x1, [fp, #-0x20]
    // 0xba2820: StoreField: r1->field_b = r0
    //     0xba2820: stur            w0, [x1, #0xb]
    // 0xba2824: ldur            x0, [fp, #-0x18]
    // 0xba2828: StoreField: r1->field_13 = r0
    //     0xba2828: stur            w0, [x1, #0x13]
    // 0xba282c: ldur            x2, [fp, #-8]
    // 0xba2830: r0 = LoadClassIdInstr(r2)
    //     0xba2830: ldur            x0, [x2, #-1]
    //     0xba2834: ubfx            x0, x0, #0xc, #0x14
    // 0xba2838: r16 = "falakiyah_nu"
    //     0xba2838: add             x16, PP, #0xf, lsl #12  ; [pp+0xf300] "falakiyah_nu"
    //     0xba283c: ldr             x16, [x16, #0x300]
    // 0xba2840: stp             x16, x2, [SP]
    // 0xba2844: mov             lr, x0
    // 0xba2848: ldr             lr, [x21, lr, lsl #3]
    // 0xba284c: blr             lr
    // 0xba2850: tbnz            w0, #4, #0xba2860
    // 0xba2854: r0 = "Lembaga Falakiyah Nahdlatul Ulama"
    //     0xba2854: add             x0, PP, #0x34, lsl #12  ; [pp+0x34168] "Lembaga Falakiyah Nahdlatul Ulama"
    //     0xba2858: ldr             x0, [x0, #0x168]
    // 0xba285c: b               #0xba287c
    // 0xba2860: ldur            x1, [fp, #-8]
    // 0xba2864: r0 = getCalculationMethodFromString()
    //     0xba2864: bl              #0x8301fc  ; [package:adhan/src/calculation_method.dart] ::getCalculationMethodFromString
    // 0xba2868: mov             x1, x0
    // 0xba286c: r0 = CalculationMethodExtensions.info()
    //     0xba286c: bl              #0xb44b80  ; [package:adhan/src/calculation_method.dart] ::CalculationMethodExtensions.info
    // 0xba2870: LoadField: r1 = r0->field_7
    //     0xba2870: ldur            w1, [x0, #7]
    // 0xba2874: DecompressPointer r1
    //     0xba2874: add             x1, x1, HEAP, lsl #32
    // 0xba2878: mov             x0, x1
    // 0xba287c: stur            x0, [fp, #-8]
    // 0xba2880: r0 = GetNavigation.textTheme()
    //     0xba2880: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xba2884: LoadField: r1 = r0->field_f
    //     0xba2884: ldur            w1, [x0, #0xf]
    // 0xba2888: DecompressPointer r1
    //     0xba2888: add             x1, x1, HEAP, lsl #32
    // 0xba288c: cmp             w1, NULL
    // 0xba2890: b.ne            #0xba289c
    // 0xba2894: r2 = Null
    //     0xba2894: mov             x2, NULL
    // 0xba2898: b               #0xba28b8
    // 0xba289c: r16 = 14.000000
    //     0xba289c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xba28a0: ldr             x16, [x16, #0x9a0]
    // 0xba28a4: str             x16, [SP]
    // 0xba28a8: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xba28a8: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xba28ac: ldr             x4, [x4, #0x88]
    // 0xba28b0: r0 = copyWith()
    //     0xba28b0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba28b4: mov             x2, x0
    // 0xba28b8: ldur            x1, [fp, #-0x20]
    // 0xba28bc: ldur            x0, [fp, #-8]
    // 0xba28c0: stur            x2, [fp, #-0x10]
    // 0xba28c4: r0 = Text()
    //     0xba28c4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xba28c8: mov             x1, x0
    // 0xba28cc: ldur            x0, [fp, #-8]
    // 0xba28d0: stur            x1, [fp, #-0x18]
    // 0xba28d4: StoreField: r1->field_b = r0
    //     0xba28d4: stur            w0, [x1, #0xb]
    // 0xba28d8: ldur            x0, [fp, #-0x10]
    // 0xba28dc: StoreField: r1->field_13 = r0
    //     0xba28dc: stur            w0, [x1, #0x13]
    // 0xba28e0: r0 = GetNavigation.theme()
    //     0xba28e0: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xba28e4: LoadField: r1 = r0->field_3f
    //     0xba28e4: ldur            w1, [x0, #0x3f]
    // 0xba28e8: DecompressPointer r1
    //     0xba28e8: add             x1, x1, HEAP, lsl #32
    // 0xba28ec: LoadField: r0 = r1->field_2b
    //     0xba28ec: ldur            w0, [x1, #0x2b]
    // 0xba28f0: DecompressPointer r0
    //     0xba28f0: add             x0, x0, HEAP, lsl #32
    // 0xba28f4: stur            x0, [fp, #-8]
    // 0xba28f8: r0 = Icon()
    //     0xba28f8: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xba28fc: mov             x3, x0
    // 0xba2900: r0 = Instance_IconData
    //     0xba2900: add             x0, PP, #0x32, lsl #12  ; [pp+0x32410] Obj!IconData@e10071
    //     0xba2904: ldr             x0, [x0, #0x410]
    // 0xba2908: stur            x3, [fp, #-0x10]
    // 0xba290c: StoreField: r3->field_b = r0
    //     0xba290c: stur            w0, [x3, #0xb]
    // 0xba2910: r0 = 16.000000
    //     0xba2910: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xba2914: ldr             x0, [x0, #0x80]
    // 0xba2918: StoreField: r3->field_f = r0
    //     0xba2918: stur            w0, [x3, #0xf]
    // 0xba291c: ldur            x0, [fp, #-8]
    // 0xba2920: StoreField: r3->field_23 = r0
    //     0xba2920: stur            w0, [x3, #0x23]
    // 0xba2924: r1 = Null
    //     0xba2924: mov             x1, NULL
    // 0xba2928: r2 = 6
    //     0xba2928: movz            x2, #0x6
    // 0xba292c: r0 = AllocateArray()
    //     0xba292c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba2930: mov             x2, x0
    // 0xba2934: ldur            x0, [fp, #-0x18]
    // 0xba2938: stur            x2, [fp, #-8]
    // 0xba293c: StoreField: r2->field_f = r0
    //     0xba293c: stur            w0, [x2, #0xf]
    // 0xba2940: r16 = Instance_SizedBox
    //     0xba2940: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xba2944: ldr             x16, [x16, #0xbd8]
    // 0xba2948: StoreField: r2->field_13 = r16
    //     0xba2948: stur            w16, [x2, #0x13]
    // 0xba294c: ldur            x0, [fp, #-0x10]
    // 0xba2950: ArrayStore: r2[0] = r0  ; List_4
    //     0xba2950: stur            w0, [x2, #0x17]
    // 0xba2954: r1 = <Widget>
    //     0xba2954: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xba2958: r0 = AllocateGrowableArray()
    //     0xba2958: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xba295c: mov             x1, x0
    // 0xba2960: ldur            x0, [fp, #-8]
    // 0xba2964: stur            x1, [fp, #-0x10]
    // 0xba2968: StoreField: r1->field_f = r0
    //     0xba2968: stur            w0, [x1, #0xf]
    // 0xba296c: r0 = 6
    //     0xba296c: movz            x0, #0x6
    // 0xba2970: StoreField: r1->field_b = r0
    //     0xba2970: stur            w0, [x1, #0xb]
    // 0xba2974: r0 = Row()
    //     0xba2974: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba2978: mov             x3, x0
    // 0xba297c: r0 = Instance_Axis
    //     0xba297c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xba2980: stur            x3, [fp, #-8]
    // 0xba2984: StoreField: r3->field_f = r0
    //     0xba2984: stur            w0, [x3, #0xf]
    // 0xba2988: r0 = Instance_MainAxisAlignment
    //     0xba2988: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xba298c: ldr             x0, [x0, #0x730]
    // 0xba2990: StoreField: r3->field_13 = r0
    //     0xba2990: stur            w0, [x3, #0x13]
    // 0xba2994: r1 = Instance_MainAxisSize
    //     0xba2994: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xba2998: ldr             x1, [x1, #0xe88]
    // 0xba299c: ArrayStore: r3[0] = r1  ; List_4
    //     0xba299c: stur            w1, [x3, #0x17]
    // 0xba29a0: r4 = Instance_CrossAxisAlignment
    //     0xba29a0: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xba29a4: ldr             x4, [x4, #0x740]
    // 0xba29a8: StoreField: r3->field_1b = r4
    //     0xba29a8: stur            w4, [x3, #0x1b]
    // 0xba29ac: r5 = Instance_VerticalDirection
    //     0xba29ac: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xba29b0: ldr             x5, [x5, #0x748]
    // 0xba29b4: StoreField: r3->field_23 = r5
    //     0xba29b4: stur            w5, [x3, #0x23]
    // 0xba29b8: r6 = Instance_Clip
    //     0xba29b8: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xba29bc: ldr             x6, [x6, #0x750]
    // 0xba29c0: StoreField: r3->field_2b = r6
    //     0xba29c0: stur            w6, [x3, #0x2b]
    // 0xba29c4: StoreField: r3->field_2f = rZR
    //     0xba29c4: stur            xzr, [x3, #0x2f]
    // 0xba29c8: ldur            x1, [fp, #-0x10]
    // 0xba29cc: StoreField: r3->field_b = r1
    //     0xba29cc: stur            w1, [x3, #0xb]
    // 0xba29d0: r1 = Null
    //     0xba29d0: mov             x1, NULL
    // 0xba29d4: r2 = 4
    //     0xba29d4: movz            x2, #0x4
    // 0xba29d8: r0 = AllocateArray()
    //     0xba29d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba29dc: mov             x2, x0
    // 0xba29e0: ldur            x0, [fp, #-0x20]
    // 0xba29e4: stur            x2, [fp, #-0x10]
    // 0xba29e8: StoreField: r2->field_f = r0
    //     0xba29e8: stur            w0, [x2, #0xf]
    // 0xba29ec: ldur            x0, [fp, #-8]
    // 0xba29f0: StoreField: r2->field_13 = r0
    //     0xba29f0: stur            w0, [x2, #0x13]
    // 0xba29f4: r1 = <Widget>
    //     0xba29f4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xba29f8: r0 = AllocateGrowableArray()
    //     0xba29f8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xba29fc: mov             x1, x0
    // 0xba2a00: ldur            x0, [fp, #-0x10]
    // 0xba2a04: stur            x1, [fp, #-8]
    // 0xba2a08: StoreField: r1->field_f = r0
    //     0xba2a08: stur            w0, [x1, #0xf]
    // 0xba2a0c: r0 = 4
    //     0xba2a0c: movz            x0, #0x4
    // 0xba2a10: StoreField: r1->field_b = r0
    //     0xba2a10: stur            w0, [x1, #0xb]
    // 0xba2a14: r0 = Column()
    //     0xba2a14: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba2a18: mov             x1, x0
    // 0xba2a1c: r0 = Instance_Axis
    //     0xba2a1c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xba2a20: stur            x1, [fp, #-0x10]
    // 0xba2a24: StoreField: r1->field_f = r0
    //     0xba2a24: stur            w0, [x1, #0xf]
    // 0xba2a28: r0 = Instance_MainAxisAlignment
    //     0xba2a28: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xba2a2c: ldr             x0, [x0, #0x730]
    // 0xba2a30: StoreField: r1->field_13 = r0
    //     0xba2a30: stur            w0, [x1, #0x13]
    // 0xba2a34: r0 = Instance_MainAxisSize
    //     0xba2a34: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xba2a38: ldr             x0, [x0, #0x738]
    // 0xba2a3c: ArrayStore: r1[0] = r0  ; List_4
    //     0xba2a3c: stur            w0, [x1, #0x17]
    // 0xba2a40: r0 = Instance_CrossAxisAlignment
    //     0xba2a40: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xba2a44: ldr             x0, [x0, #0x740]
    // 0xba2a48: StoreField: r1->field_1b = r0
    //     0xba2a48: stur            w0, [x1, #0x1b]
    // 0xba2a4c: r0 = Instance_VerticalDirection
    //     0xba2a4c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xba2a50: ldr             x0, [x0, #0x748]
    // 0xba2a54: StoreField: r1->field_23 = r0
    //     0xba2a54: stur            w0, [x1, #0x23]
    // 0xba2a58: r0 = Instance_Clip
    //     0xba2a58: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xba2a5c: ldr             x0, [x0, #0x750]
    // 0xba2a60: StoreField: r1->field_2b = r0
    //     0xba2a60: stur            w0, [x1, #0x2b]
    // 0xba2a64: StoreField: r1->field_2f = rZR
    //     0xba2a64: stur            xzr, [x1, #0x2f]
    // 0xba2a68: ldur            x0, [fp, #-8]
    // 0xba2a6c: StoreField: r1->field_b = r0
    //     0xba2a6c: stur            w0, [x1, #0xb]
    // 0xba2a70: r0 = Padding()
    //     0xba2a70: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba2a74: r1 = Instance_EdgeInsets
    //     0xba2a74: add             x1, PP, #0x34, lsl #12  ; [pp+0x34170] Obj!EdgeInsets@e136f1
    //     0xba2a78: ldr             x1, [x1, #0x170]
    // 0xba2a7c: StoreField: r0->field_f = r1
    //     0xba2a7c: stur            w1, [x0, #0xf]
    // 0xba2a80: ldur            x1, [fp, #-0x10]
    // 0xba2a84: StoreField: r0->field_b = r1
    //     0xba2a84: stur            w1, [x0, #0xb]
    // 0xba2a88: LeaveFrame
    //     0xba2a88: mov             SP, fp
    //     0xba2a8c: ldp             fp, lr, [SP], #0x10
    // 0xba2a90: ret
    //     0xba2a90: ret             
    // 0xba2a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba2a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba2a98: b               #0xba2768
  }
}

// class id: 5239, size: 0x14, field offset: 0x14
class PrayerTimeView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb15c84, size: 0x180
    // 0xb15c84: EnterFrame
    //     0xb15c84: stp             fp, lr, [SP, #-0x10]!
    //     0xb15c88: mov             fp, SP
    // 0xb15c8c: AllocStack(0x18)
    //     0xb15c8c: sub             SP, SP, #0x18
    // 0xb15c90: SetupParameters(PrayerTimeView this /* r1 => r1, fp-0x8 */)
    //     0xb15c90: stur            x1, [fp, #-8]
    // 0xb15c94: r1 = 1
    //     0xb15c94: movz            x1, #0x1
    // 0xb15c98: r0 = AllocateContext()
    //     0xb15c98: bl              #0xec126c  ; AllocateContextStub
    // 0xb15c9c: mov             x1, x0
    // 0xb15ca0: ldur            x0, [fp, #-8]
    // 0xb15ca4: stur            x1, [fp, #-0x10]
    // 0xb15ca8: StoreField: r1->field_f = r0
    //     0xb15ca8: stur            w0, [x1, #0xf]
    // 0xb15cac: r0 = Obx()
    //     0xb15cac: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb15cb0: ldur            x2, [fp, #-0x10]
    // 0xb15cb4: r1 = Function '<anonymous closure>':.
    //     0xb15cb4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2de00] AnonymousClosure: (0xb17bbc), in [package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart] PrayerTimeView::build (0xb15c84)
    //     0xb15cb8: ldr             x1, [x1, #0xe00]
    // 0xb15cbc: stur            x0, [fp, #-8]
    // 0xb15cc0: r0 = AllocateClosure()
    //     0xb15cc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb15cc4: mov             x1, x0
    // 0xb15cc8: ldur            x0, [fp, #-8]
    // 0xb15ccc: StoreField: r0->field_b = r1
    //     0xb15ccc: stur            w1, [x0, #0xb]
    // 0xb15cd0: r0 = Obx()
    //     0xb15cd0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb15cd4: ldur            x2, [fp, #-0x10]
    // 0xb15cd8: r1 = Function '<anonymous closure>':.
    //     0xb15cd8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2de08] AnonymousClosure: (0xb15e04), in [package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart] PrayerTimeView::build (0xb15c84)
    //     0xb15cdc: ldr             x1, [x1, #0xe08]
    // 0xb15ce0: stur            x0, [fp, #-0x10]
    // 0xb15ce4: r0 = AllocateClosure()
    //     0xb15ce4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb15ce8: mov             x1, x0
    // 0xb15cec: ldur            x0, [fp, #-0x10]
    // 0xb15cf0: StoreField: r0->field_b = r1
    //     0xb15cf0: stur            w1, [x0, #0xb]
    // 0xb15cf4: r0 = SliverPadding()
    //     0xb15cf4: bl              #0xa01298  ; AllocateSliverPaddingStub -> SliverPadding (size=0x14)
    // 0xb15cf8: mov             x3, x0
    // 0xb15cfc: r0 = Instance_EdgeInsets
    //     0xb15cfc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2de10] Obj!EdgeInsets@e12b51
    //     0xb15d00: ldr             x0, [x0, #0xe10]
    // 0xb15d04: stur            x3, [fp, #-0x18]
    // 0xb15d08: StoreField: r3->field_f = r0
    //     0xb15d08: stur            w0, [x3, #0xf]
    // 0xb15d0c: ldur            x0, [fp, #-0x10]
    // 0xb15d10: StoreField: r3->field_b = r0
    //     0xb15d10: stur            w0, [x3, #0xb]
    // 0xb15d14: r1 = Null
    //     0xb15d14: mov             x1, NULL
    // 0xb15d18: r2 = 4
    //     0xb15d18: movz            x2, #0x4
    // 0xb15d1c: r0 = AllocateArray()
    //     0xb15d1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb15d20: mov             x2, x0
    // 0xb15d24: ldur            x0, [fp, #-8]
    // 0xb15d28: stur            x2, [fp, #-0x10]
    // 0xb15d2c: StoreField: r2->field_f = r0
    //     0xb15d2c: stur            w0, [x2, #0xf]
    // 0xb15d30: ldur            x0, [fp, #-0x18]
    // 0xb15d34: StoreField: r2->field_13 = r0
    //     0xb15d34: stur            w0, [x2, #0x13]
    // 0xb15d38: r1 = <Widget>
    //     0xb15d38: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb15d3c: r0 = AllocateGrowableArray()
    //     0xb15d3c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb15d40: mov             x1, x0
    // 0xb15d44: ldur            x0, [fp, #-0x10]
    // 0xb15d48: stur            x1, [fp, #-8]
    // 0xb15d4c: StoreField: r1->field_f = r0
    //     0xb15d4c: stur            w0, [x1, #0xf]
    // 0xb15d50: r0 = 4
    //     0xb15d50: movz            x0, #0x4
    // 0xb15d54: StoreField: r1->field_b = r0
    //     0xb15d54: stur            w0, [x1, #0xb]
    // 0xb15d58: r0 = CustomScrollView()
    //     0xb15d58: bl              #0xa0128c  ; AllocateCustomScrollViewStub -> CustomScrollView (size=0x54)
    // 0xb15d5c: mov             x1, x0
    // 0xb15d60: ldur            x0, [fp, #-8]
    // 0xb15d64: stur            x1, [fp, #-0x10]
    // 0xb15d68: StoreField: r1->field_4f = r0
    //     0xb15d68: stur            w0, [x1, #0x4f]
    // 0xb15d6c: r0 = Instance_Axis
    //     0xb15d6c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb15d70: StoreField: r1->field_b = r0
    //     0xb15d70: stur            w0, [x1, #0xb]
    // 0xb15d74: r0 = false
    //     0xb15d74: add             x0, NULL, #0x30  ; false
    // 0xb15d78: StoreField: r1->field_f = r0
    //     0xb15d78: stur            w0, [x1, #0xf]
    // 0xb15d7c: StoreField: r1->field_23 = r0
    //     0xb15d7c: stur            w0, [x1, #0x23]
    // 0xb15d80: StoreField: r1->field_2b = rZR
    //     0xb15d80: stur            xzr, [x1, #0x2b]
    // 0xb15d84: r2 = Instance_DragStartBehavior
    //     0xb15d84: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb15d88: StoreField: r1->field_3b = r2
    //     0xb15d88: stur            w2, [x1, #0x3b]
    // 0xb15d8c: r3 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xb15d8c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xb15d90: ldr             x3, [x3, #0xf00]
    // 0xb15d94: StoreField: r1->field_3f = r3
    //     0xb15d94: stur            w3, [x1, #0x3f]
    // 0xb15d98: r3 = Instance_Clip
    //     0xb15d98: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb15d9c: ldr             x3, [x3, #0x7c0]
    // 0xb15da0: StoreField: r1->field_47 = r3
    //     0xb15da0: stur            w3, [x1, #0x47]
    // 0xb15da4: r3 = Instance_HitTestBehavior
    //     0xb15da4: add             x3, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xb15da8: ldr             x3, [x3, #0x1c8]
    // 0xb15dac: StoreField: r1->field_4b = r3
    //     0xb15dac: stur            w3, [x1, #0x4b]
    // 0xb15db0: r3 = Instance_AlwaysScrollableScrollPhysics
    //     0xb15db0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26f08] Obj!AlwaysScrollableScrollPhysics@e0fd51
    //     0xb15db4: ldr             x3, [x3, #0xf08]
    // 0xb15db8: StoreField: r1->field_1b = r3
    //     0xb15db8: stur            w3, [x1, #0x1b]
    // 0xb15dbc: r0 = Scaffold()
    //     0xb15dbc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb15dc0: ldur            x1, [fp, #-0x10]
    // 0xb15dc4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb15dc4: stur            w1, [x0, #0x17]
    // 0xb15dc8: r1 = Instance_AlignmentDirectional
    //     0xb15dc8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb15dcc: ldr             x1, [x1, #0x758]
    // 0xb15dd0: StoreField: r0->field_2b = r1
    //     0xb15dd0: stur            w1, [x0, #0x2b]
    // 0xb15dd4: r1 = true
    //     0xb15dd4: add             x1, NULL, #0x20  ; true
    // 0xb15dd8: StoreField: r0->field_53 = r1
    //     0xb15dd8: stur            w1, [x0, #0x53]
    // 0xb15ddc: r2 = Instance_DragStartBehavior
    //     0xb15ddc: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb15de0: StoreField: r0->field_57 = r2
    //     0xb15de0: stur            w2, [x0, #0x57]
    // 0xb15de4: r2 = false
    //     0xb15de4: add             x2, NULL, #0x30  ; false
    // 0xb15de8: StoreField: r0->field_b = r2
    //     0xb15de8: stur            w2, [x0, #0xb]
    // 0xb15dec: StoreField: r0->field_f = r2
    //     0xb15dec: stur            w2, [x0, #0xf]
    // 0xb15df0: StoreField: r0->field_5f = r1
    //     0xb15df0: stur            w1, [x0, #0x5f]
    // 0xb15df4: StoreField: r0->field_63 = r1
    //     0xb15df4: stur            w1, [x0, #0x63]
    // 0xb15df8: LeaveFrame
    //     0xb15df8: mov             SP, fp
    //     0xb15dfc: ldp             fp, lr, [SP], #0x10
    // 0xb15e00: ret
    //     0xb15e00: ret             
  }
  [closure] SliverList <anonymous closure>(dynamic) {
    // ** addr: 0xb15e04, size: 0x2c4
    // 0xb15e04: EnterFrame
    //     0xb15e04: stp             fp, lr, [SP, #-0x10]!
    //     0xb15e08: mov             fp, SP
    // 0xb15e0c: AllocStack(0x40)
    //     0xb15e0c: sub             SP, SP, #0x40
    // 0xb15e10: SetupParameters()
    //     0xb15e10: ldr             x0, [fp, #0x10]
    //     0xb15e14: ldur            w3, [x0, #0x17]
    //     0xb15e18: add             x3, x3, HEAP, lsl #32
    //     0xb15e1c: stur            x3, [fp, #-8]
    // 0xb15e20: CheckStackOverflow
    //     0xb15e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15e24: cmp             SP, x16
    //     0xb15e28: b.ls            #0xb160b8
    // 0xb15e2c: r1 = <Widget>
    //     0xb15e2c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb15e30: r2 = 0
    //     0xb15e30: movz            x2, #0
    // 0xb15e34: r0 = _GrowableList()
    //     0xb15e34: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb15e38: ldur            x2, [fp, #-8]
    // 0xb15e3c: stur            x0, [fp, #-0x10]
    // 0xb15e40: LoadField: r1 = r2->field_f
    //     0xb15e40: ldur            w1, [x2, #0xf]
    // 0xb15e44: DecompressPointer r1
    //     0xb15e44: add             x1, x1, HEAP, lsl #32
    // 0xb15e48: r0 = controller()
    //     0xb15e48: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb15e4c: mov             x1, x0
    // 0xb15e50: r0 = prayers()
    //     0xb15e50: bl              #0xb16120  ; [package:nuonline/app/modules/prayer_time/controllers/prayer_time_controller.dart] PrayerTimeController::prayers
    // 0xb15e54: LoadField: r1 = r0->field_b
    //     0xb15e54: ldur            w1, [x0, #0xb]
    // 0xb15e58: cbnz            w1, #0xb15ebc
    // 0xb15e5c: r1 = <Widget>
    //     0xb15e5c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb15e60: r2 = 8
    //     0xb15e60: movz            x2, #0x8
    // 0xb15e64: r0 = _GrowableList()
    //     0xb15e64: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb15e68: LoadField: r1 = r0->field_b
    //     0xb15e68: ldur            w1, [x0, #0xb]
    // 0xb15e6c: r2 = LoadInt32Instr(r1)
    //     0xb15e6c: sbfx            x2, x1, #1, #0x1f
    // 0xb15e70: LoadField: r1 = r0->field_f
    //     0xb15e70: ldur            w1, [x0, #0xf]
    // 0xb15e74: DecompressPointer r1
    //     0xb15e74: add             x1, x1, HEAP, lsl #32
    // 0xb15e78: r3 = 0
    //     0xb15e78: movz            x3, #0
    // 0xb15e7c: CheckStackOverflow
    //     0xb15e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15e80: cmp             SP, x16
    //     0xb15e84: b.ls            #0xb160c0
    // 0xb15e88: cmp             x3, x2
    // 0xb15e8c: b.ge            #0xb15eac
    // 0xb15e90: add             x4, x1, x3, lsl #2
    // 0xb15e94: r16 = Instance_Padding
    //     0xb15e94: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2de18] Obj!Padding@e1e861
    //     0xb15e98: ldr             x16, [x16, #0xe18]
    // 0xb15e9c: StoreField: r4->field_f = r16
    //     0xb15e9c: stur            w16, [x4, #0xf]
    // 0xb15ea0: add             x4, x3, #1
    // 0xb15ea4: mov             x3, x4
    // 0xb15ea8: b               #0xb15e7c
    // 0xb15eac: ldur            x1, [fp, #-0x10]
    // 0xb15eb0: mov             x2, x0
    // 0xb15eb4: r0 = addAll()
    //     0xb15eb4: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb15eb8: b               #0xb15f0c
    // 0xb15ebc: ldur            x2, [fp, #-8]
    // 0xb15ec0: LoadField: r1 = r2->field_f
    //     0xb15ec0: ldur            w1, [x2, #0xf]
    // 0xb15ec4: DecompressPointer r1
    //     0xb15ec4: add             x1, x1, HEAP, lsl #32
    // 0xb15ec8: r0 = controller()
    //     0xb15ec8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb15ecc: mov             x1, x0
    // 0xb15ed0: r0 = prayers()
    //     0xb15ed0: bl              #0xb16120  ; [package:nuonline/app/modules/prayer_time/controllers/prayer_time_controller.dart] PrayerTimeController::prayers
    // 0xb15ed4: ldur            x2, [fp, #-8]
    // 0xb15ed8: r1 = Function '<anonymous closure>':.
    //     0xb15ed8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2de20] AnonymousClosure: (0xb17758), in [package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart] PrayerTimeView::build (0xb15c84)
    //     0xb15edc: ldr             x1, [x1, #0xe20]
    // 0xb15ee0: stur            x0, [fp, #-0x18]
    // 0xb15ee4: r0 = AllocateClosure()
    //     0xb15ee4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb15ee8: r16 = <Widget>
    //     0xb15ee8: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb15eec: ldur            lr, [fp, #-0x18]
    // 0xb15ef0: stp             lr, x16, [SP, #8]
    // 0xb15ef4: str             x0, [SP]
    // 0xb15ef8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb15ef8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb15efc: r0 = map()
    //     0xb15efc: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb15f00: ldur            x1, [fp, #-0x10]
    // 0xb15f04: mov             x2, x0
    // 0xb15f08: r0 = addAll()
    //     0xb15f08: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb15f0c: ldur            x0, [fp, #-8]
    // 0xb15f10: ldur            x2, [fp, #-0x10]
    // 0xb15f14: LoadField: r1 = r0->field_f
    //     0xb15f14: ldur            w1, [x0, #0xf]
    // 0xb15f18: DecompressPointer r1
    //     0xb15f18: add             x1, x1, HEAP, lsl #32
    // 0xb15f1c: r0 = controller()
    //     0xb15f1c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb15f20: LoadField: r1 = r0->field_4b
    //     0xb15f20: ldur            w1, [x0, #0x4b]
    // 0xb15f24: DecompressPointer r1
    //     0xb15f24: add             x1, x1, HEAP, lsl #32
    // 0xb15f28: r0 = value()
    //     0xb15f28: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb15f2c: stur            x0, [fp, #-8]
    // 0xb15f30: r0 = PrayerTimeMothodInformation()
    //     0xb15f30: bl              #0xb16114  ; AllocatePrayerTimeMothodInformationStub -> PrayerTimeMothodInformation (size=0x10)
    // 0xb15f34: mov             x2, x0
    // 0xb15f38: ldur            x0, [fp, #-8]
    // 0xb15f3c: stur            x2, [fp, #-0x18]
    // 0xb15f40: StoreField: r2->field_b = r0
    //     0xb15f40: stur            w0, [x2, #0xb]
    // 0xb15f44: ldur            x0, [fp, #-0x10]
    // 0xb15f48: LoadField: r1 = r0->field_b
    //     0xb15f48: ldur            w1, [x0, #0xb]
    // 0xb15f4c: LoadField: r3 = r0->field_f
    //     0xb15f4c: ldur            w3, [x0, #0xf]
    // 0xb15f50: DecompressPointer r3
    //     0xb15f50: add             x3, x3, HEAP, lsl #32
    // 0xb15f54: LoadField: r4 = r3->field_b
    //     0xb15f54: ldur            w4, [x3, #0xb]
    // 0xb15f58: r3 = LoadInt32Instr(r1)
    //     0xb15f58: sbfx            x3, x1, #1, #0x1f
    // 0xb15f5c: stur            x3, [fp, #-0x20]
    // 0xb15f60: r1 = LoadInt32Instr(r4)
    //     0xb15f60: sbfx            x1, x4, #1, #0x1f
    // 0xb15f64: cmp             x3, x1
    // 0xb15f68: b.ne            #0xb15f74
    // 0xb15f6c: mov             x1, x0
    // 0xb15f70: r0 = _growToNextCapacity()
    //     0xb15f70: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb15f74: ldur            x2, [fp, #-0x10]
    // 0xb15f78: ldur            x3, [fp, #-0x20]
    // 0xb15f7c: add             x0, x3, #1
    // 0xb15f80: lsl             x1, x0, #1
    // 0xb15f84: StoreField: r2->field_b = r1
    //     0xb15f84: stur            w1, [x2, #0xb]
    // 0xb15f88: LoadField: r1 = r2->field_f
    //     0xb15f88: ldur            w1, [x2, #0xf]
    // 0xb15f8c: DecompressPointer r1
    //     0xb15f8c: add             x1, x1, HEAP, lsl #32
    // 0xb15f90: ldur            x0, [fp, #-0x18]
    // 0xb15f94: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb15f94: add             x25, x1, x3, lsl #2
    //     0xb15f98: add             x25, x25, #0xf
    //     0xb15f9c: str             w0, [x25]
    //     0xb15fa0: tbz             w0, #0, #0xb15fbc
    //     0xb15fa4: ldurb           w16, [x1, #-1]
    //     0xb15fa8: ldurb           w17, [x0, #-1]
    //     0xb15fac: and             x16, x17, x16, lsr #2
    //     0xb15fb0: tst             x16, HEAP, lsr #32
    //     0xb15fb4: b.eq            #0xb15fbc
    //     0xb15fb8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb15fbc: r0 = WidgetPaddingX.paddingSymmetric()
    //     0xb15fbc: bl              #0xb160c8  ; [package:get/get_utils/src/extensions/widget_extensions.dart] ::WidgetPaddingX.paddingSymmetric
    // 0xb15fc0: mov             x2, x0
    // 0xb15fc4: ldur            x0, [fp, #-0x10]
    // 0xb15fc8: stur            x2, [fp, #-8]
    // 0xb15fcc: LoadField: r1 = r0->field_b
    //     0xb15fcc: ldur            w1, [x0, #0xb]
    // 0xb15fd0: LoadField: r3 = r0->field_f
    //     0xb15fd0: ldur            w3, [x0, #0xf]
    // 0xb15fd4: DecompressPointer r3
    //     0xb15fd4: add             x3, x3, HEAP, lsl #32
    // 0xb15fd8: LoadField: r4 = r3->field_b
    //     0xb15fd8: ldur            w4, [x3, #0xb]
    // 0xb15fdc: r3 = LoadInt32Instr(r1)
    //     0xb15fdc: sbfx            x3, x1, #1, #0x1f
    // 0xb15fe0: stur            x3, [fp, #-0x20]
    // 0xb15fe4: r1 = LoadInt32Instr(r4)
    //     0xb15fe4: sbfx            x1, x4, #1, #0x1f
    // 0xb15fe8: cmp             x3, x1
    // 0xb15fec: b.ne            #0xb15ff8
    // 0xb15ff0: mov             x1, x0
    // 0xb15ff4: r0 = _growToNextCapacity()
    //     0xb15ff4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb15ff8: ldur            x2, [fp, #-0x10]
    // 0xb15ffc: ldur            x3, [fp, #-0x20]
    // 0xb16000: add             x4, x3, #1
    // 0xb16004: stur            x4, [fp, #-0x28]
    // 0xb16008: lsl             x0, x4, #1
    // 0xb1600c: StoreField: r2->field_b = r0
    //     0xb1600c: stur            w0, [x2, #0xb]
    // 0xb16010: LoadField: r5 = r2->field_f
    //     0xb16010: ldur            w5, [x2, #0xf]
    // 0xb16014: DecompressPointer r5
    //     0xb16014: add             x5, x5, HEAP, lsl #32
    // 0xb16018: mov             x1, x5
    // 0xb1601c: ldur            x0, [fp, #-8]
    // 0xb16020: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb16020: add             x25, x1, x3, lsl #2
    //     0xb16024: add             x25, x25, #0xf
    //     0xb16028: str             w0, [x25]
    //     0xb1602c: tbz             w0, #0, #0xb16048
    //     0xb16030: ldurb           w16, [x1, #-1]
    //     0xb16034: ldurb           w17, [x0, #-1]
    //     0xb16038: and             x16, x17, x16, lsr #2
    //     0xb1603c: tst             x16, HEAP, lsr #32
    //     0xb16040: b.eq            #0xb16048
    //     0xb16044: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb16048: LoadField: r0 = r5->field_b
    //     0xb16048: ldur            w0, [x5, #0xb]
    // 0xb1604c: r1 = LoadInt32Instr(r0)
    //     0xb1604c: sbfx            x1, x0, #1, #0x1f
    // 0xb16050: cmp             x4, x1
    // 0xb16054: b.ne            #0xb16060
    // 0xb16058: mov             x1, x2
    // 0xb1605c: r0 = _growToNextCapacity()
    //     0xb1605c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb16060: ldur            x2, [fp, #-0x10]
    // 0xb16064: ldur            x0, [fp, #-0x28]
    // 0xb16068: add             x1, x0, #1
    // 0xb1606c: lsl             x3, x1, #1
    // 0xb16070: StoreField: r2->field_b = r3
    //     0xb16070: stur            w3, [x2, #0xb]
    // 0xb16074: LoadField: r1 = r2->field_f
    //     0xb16074: ldur            w1, [x2, #0xf]
    // 0xb16078: DecompressPointer r1
    //     0xb16078: add             x1, x1, HEAP, lsl #32
    // 0xb1607c: add             x3, x1, x0, lsl #2
    // 0xb16080: r16 = Instance_SizedBox
    //     0xb16080: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xb16084: ldr             x16, [x16, #0x540]
    // 0xb16088: StoreField: r3->field_f = r16
    //     0xb16088: stur            w16, [x3, #0xf]
    // 0xb1608c: r0 = SliverChildListDelegate()
    //     0xb1608c: bl              #0xa07e78  ; AllocateSliverChildListDelegateStub -> SliverChildListDelegate (size=0x28)
    // 0xb16090: mov             x1, x0
    // 0xb16094: ldur            x2, [fp, #-0x10]
    // 0xb16098: stur            x0, [fp, #-8]
    // 0xb1609c: r0 = SliverChildListDelegate()
    //     0xb1609c: bl              #0xa07d8c  ; [package:flutter/src/widgets/scroll_delegate.dart] SliverChildListDelegate::SliverChildListDelegate
    // 0xb160a0: r0 = SliverList()
    //     0xb160a0: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xb160a4: ldur            x1, [fp, #-8]
    // 0xb160a8: StoreField: r0->field_b = r1
    //     0xb160a8: stur            w1, [x0, #0xb]
    // 0xb160ac: LeaveFrame
    //     0xb160ac: mov             SP, fp
    //     0xb160b0: ldp             fp, lr, [SP], #0x10
    // 0xb160b4: ret
    //     0xb160b4: ret             
    // 0xb160b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb160b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb160bc: b               #0xb15e2c
    // 0xb160c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb160c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb160c4: b               #0xb15e88
  }
  [closure] Widget <anonymous closure>(dynamic, PrayerTime) {
    // ** addr: 0xb17758, size: 0x48
    // 0xb17758: EnterFrame
    //     0xb17758: stp             fp, lr, [SP, #-0x10]!
    //     0xb1775c: mov             fp, SP
    // 0xb17760: ldr             x0, [fp, #0x18]
    // 0xb17764: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb17764: ldur            w1, [x0, #0x17]
    // 0xb17768: DecompressPointer r1
    //     0xb17768: add             x1, x1, HEAP, lsl #32
    // 0xb1776c: CheckStackOverflow
    //     0xb1776c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17770: cmp             SP, x16
    //     0xb17774: b.ls            #0xb17798
    // 0xb17778: LoadField: r0 = r1->field_f
    //     0xb17778: ldur            w0, [x1, #0xf]
    // 0xb1777c: DecompressPointer r0
    //     0xb1777c: add             x0, x0, HEAP, lsl #32
    // 0xb17780: mov             x1, x0
    // 0xb17784: ldr             x2, [fp, #0x10]
    // 0xb17788: r0 = buildPrayerTimeItem()
    //     0xb17788: bl              #0xb177a0  ; [package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart] PrayerTimeView::buildPrayerTimeItem
    // 0xb1778c: LeaveFrame
    //     0xb1778c: mov             SP, fp
    //     0xb17790: ldp             fp, lr, [SP], #0x10
    // 0xb17794: ret
    //     0xb17794: ret             
    // 0xb17798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1779c: b               #0xb17778
  }
  _ buildPrayerTimeItem(/* No info */) {
    // ** addr: 0xb177a0, size: 0x390
    // 0xb177a0: EnterFrame
    //     0xb177a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb177a4: mov             fp, SP
    // 0xb177a8: AllocStack(0x48)
    //     0xb177a8: sub             SP, SP, #0x48
    // 0xb177ac: SetupParameters(PrayerTimeView this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb177ac: mov             x3, x1
    //     0xb177b0: mov             x0, x2
    //     0xb177b4: stur            x1, [fp, #-8]
    //     0xb177b8: stur            x2, [fp, #-0x10]
    // 0xb177bc: CheckStackOverflow
    //     0xb177bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb177c0: cmp             SP, x16
    //     0xb177c4: b.ls            #0xb17b28
    // 0xb177c8: cmp             w0, NULL
    // 0xb177cc: b.ne            #0xb177e4
    // 0xb177d0: r0 = Instance_Padding
    //     0xb177d0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2de18] Obj!Padding@e1e861
    //     0xb177d4: ldr             x0, [x0, #0xe18]
    // 0xb177d8: LeaveFrame
    //     0xb177d8: mov             SP, fp
    //     0xb177dc: ldp             fp, lr, [SP], #0x10
    // 0xb177e0: ret
    //     0xb177e0: ret             
    // 0xb177e4: r1 = Null
    //     0xb177e4: mov             x1, NULL
    // 0xb177e8: r2 = 12
    //     0xb177e8: movz            x2, #0xc
    // 0xb177ec: r0 = AllocateArray()
    //     0xb177ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb177f0: stur            x0, [fp, #-0x20]
    // 0xb177f4: r16 = "prayer_name"
    //     0xb177f4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a250] "prayer_name"
    //     0xb177f8: ldr             x16, [x16, #0x250]
    // 0xb177fc: StoreField: r0->field_f = r16
    //     0xb177fc: stur            w16, [x0, #0xf]
    // 0xb17800: ldur            x2, [fp, #-0x10]
    // 0xb17804: LoadField: r3 = r2->field_7
    //     0xb17804: ldur            w3, [x2, #7]
    // 0xb17808: DecompressPointer r3
    //     0xb17808: add             x3, x3, HEAP, lsl #32
    // 0xb1780c: stur            x3, [fp, #-0x18]
    // 0xb17810: StoreField: r0->field_13 = r3
    //     0xb17810: stur            w3, [x0, #0x13]
    // 0xb17814: r16 = "sound"
    //     0xb17814: add             x16, PP, #8, lsl #12  ; [pp+0x89a8] "sound"
    //     0xb17818: ldr             x16, [x16, #0x9a8]
    // 0xb1781c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb1781c: stur            w16, [x0, #0x17]
    // 0xb17820: LoadField: r1 = r2->field_1b
    //     0xb17820: ldur            w1, [x2, #0x1b]
    // 0xb17824: DecompressPointer r1
    //     0xb17824: add             x1, x1, HEAP, lsl #32
    // 0xb17828: r0 = _enumToString()
    //     0xb17828: bl              #0xc4d2a8  ; [package:nuonline/app/data/models/prayer_time_notification_setting.dart] NotificationSound::_enumToString
    // 0xb1782c: ldur            x1, [fp, #-0x20]
    // 0xb17830: ArrayStore: r1[3] = r0  ; List_4
    //     0xb17830: add             x25, x1, #0x1b
    //     0xb17834: str             w0, [x25]
    //     0xb17838: tbz             w0, #0, #0xb17854
    //     0xb1783c: ldurb           w16, [x1, #-1]
    //     0xb17840: ldurb           w17, [x0, #-1]
    //     0xb17844: and             x16, x17, x16, lsr #2
    //     0xb17848: tst             x16, HEAP, lsr #32
    //     0xb1784c: b.eq            #0xb17854
    //     0xb17850: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb17854: ldur            x2, [fp, #-0x20]
    // 0xb17858: r16 = "delay_time"
    //     0xb17858: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a258] "delay_time"
    //     0xb1785c: ldr             x16, [x16, #0x258]
    // 0xb17860: StoreField: r2->field_1f = r16
    //     0xb17860: stur            w16, [x2, #0x1f]
    // 0xb17864: ldur            x3, [fp, #-0x10]
    // 0xb17868: LoadField: r4 = r3->field_1f
    //     0xb17868: ldur            x4, [x3, #0x1f]
    // 0xb1786c: r0 = BoxInt64Instr(r4)
    //     0xb1786c: sbfiz           x0, x4, #1, #0x1f
    //     0xb17870: cmp             x4, x0, asr #1
    //     0xb17874: b.eq            #0xb17880
    //     0xb17878: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1787c: stur            x4, [x0, #7]
    // 0xb17880: r1 = 60
    //     0xb17880: movz            x1, #0x3c
    // 0xb17884: branchIfSmi(r0, 0xb17890)
    //     0xb17884: tbz             w0, #0, #0xb17890
    // 0xb17888: r1 = LoadClassIdInstr(r0)
    //     0xb17888: ldur            x1, [x0, #-1]
    //     0xb1788c: ubfx            x1, x1, #0xc, #0x14
    // 0xb17890: str             x0, [SP]
    // 0xb17894: mov             x0, x1
    // 0xb17898: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb17898: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb1789c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb1789c: movz            x17, #0x2b03
    //     0xb178a0: add             lr, x0, x17
    //     0xb178a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb178a8: blr             lr
    // 0xb178ac: ldur            x1, [fp, #-0x20]
    // 0xb178b0: ArrayStore: r1[5] = r0  ; List_4
    //     0xb178b0: add             x25, x1, #0x23
    //     0xb178b4: str             w0, [x25]
    //     0xb178b8: tbz             w0, #0, #0xb178d4
    //     0xb178bc: ldurb           w16, [x1, #-1]
    //     0xb178c0: ldurb           w17, [x0, #-1]
    //     0xb178c4: and             x16, x17, x16, lsr #2
    //     0xb178c8: tst             x16, HEAP, lsr #32
    //     0xb178cc: b.eq            #0xb178d4
    //     0xb178d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb178d4: r16 = <String, String>
    //     0xb178d4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb178d8: ldr             x16, [x16, #0x668]
    // 0xb178dc: ldur            lr, [fp, #-0x20]
    // 0xb178e0: stp             lr, x16, [SP]
    // 0xb178e4: r0 = Map._fromLiteral()
    //     0xb178e4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb178e8: stur            x0, [fp, #-0x20]
    // 0xb178ec: r1 = 1
    //     0xb178ec: movz            x1, #0x1
    // 0xb178f0: r0 = AllocateContext()
    //     0xb178f0: bl              #0xec126c  ; AllocateContextStub
    // 0xb178f4: mov             x2, x0
    // 0xb178f8: ldur            x1, [fp, #-0x20]
    // 0xb178fc: stur            x2, [fp, #-0x28]
    // 0xb17900: StoreField: r2->field_f = r1
    //     0xb17900: stur            w1, [x2, #0xf]
    // 0xb17904: ldur            x3, [fp, #-0x18]
    // 0xb17908: r0 = LoadClassIdInstr(r3)
    //     0xb17908: ldur            x0, [x3, #-1]
    //     0xb1790c: ubfx            x0, x0, #0xc, #0x14
    // 0xb17910: r16 = "Imsak"
    //     0xb17910: add             x16, PP, #8, lsl #12  ; [pp+0x86c0] "Imsak"
    //     0xb17914: ldr             x16, [x16, #0x6c0]
    // 0xb17918: stp             x16, x3, [SP]
    // 0xb1791c: mov             lr, x0
    // 0xb17920: ldr             lr, [x21, lr, lsl #3]
    // 0xb17924: blr             lr
    // 0xb17928: tbnz            w0, #4, #0xb179c8
    // 0xb1792c: r1 = Null
    //     0xb1792c: mov             x1, NULL
    // 0xb17930: r2 = 4
    //     0xb17930: movz            x2, #0x4
    // 0xb17934: r0 = AllocateArray()
    //     0xb17934: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb17938: stur            x0, [fp, #-0x30]
    // 0xb1793c: r16 = "imsak_ramadhan_only"
    //     0xb1793c: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a268] "imsak_ramadhan_only"
    //     0xb17940: ldr             x16, [x16, #0x268]
    // 0xb17944: StoreField: r0->field_f = r16
    //     0xb17944: stur            w16, [x0, #0xf]
    // 0xb17948: ldur            x1, [fp, #-8]
    // 0xb1794c: r0 = controller()
    //     0xb1794c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17950: mov             x1, x0
    // 0xb17954: r0 = setting()
    //     0xb17954: bl              #0xb17700  ; [package:nuonline/app/modules/prayer_time/controllers/upcoming_prayer_time_controller.dart.dart] UpcomingPrayerTimeController::setting
    // 0xb17958: LoadField: r1 = r0->field_7b
    //     0xb17958: ldur            w1, [x0, #0x7b]
    // 0xb1795c: DecompressPointer r1
    //     0xb1795c: add             x1, x1, HEAP, lsl #32
    // 0xb17960: r0 = LoadClassIdInstr(r1)
    //     0xb17960: ldur            x0, [x1, #-1]
    //     0xb17964: ubfx            x0, x0, #0xc, #0x14
    // 0xb17968: str             x1, [SP]
    // 0xb1796c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb1796c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb17970: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb17970: movz            x17, #0x2b03
    //     0xb17974: add             lr, x0, x17
    //     0xb17978: ldr             lr, [x21, lr, lsl #3]
    //     0xb1797c: blr             lr
    // 0xb17980: ldur            x1, [fp, #-0x30]
    // 0xb17984: ArrayStore: r1[1] = r0  ; List_4
    //     0xb17984: add             x25, x1, #0x13
    //     0xb17988: str             w0, [x25]
    //     0xb1798c: tbz             w0, #0, #0xb179a8
    //     0xb17990: ldurb           w16, [x1, #-1]
    //     0xb17994: ldurb           w17, [x0, #-1]
    //     0xb17998: and             x16, x17, x16, lsr #2
    //     0xb1799c: tst             x16, HEAP, lsr #32
    //     0xb179a0: b.eq            #0xb179a8
    //     0xb179a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb179a8: r16 = <String, String>
    //     0xb179a8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb179ac: ldr             x16, [x16, #0x668]
    // 0xb179b0: ldur            lr, [fp, #-0x30]
    // 0xb179b4: stp             lr, x16, [SP]
    // 0xb179b8: r0 = Map._fromLiteral()
    //     0xb179b8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb179bc: ldur            x1, [fp, #-0x20]
    // 0xb179c0: mov             x2, x0
    // 0xb179c4: r0 = addAll()
    //     0xb179c4: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xb179c8: ldur            x0, [fp, #-0x10]
    // 0xb179cc: r1 = Null
    //     0xb179cc: mov             x1, NULL
    // 0xb179d0: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb179d0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb179d4: ldr             x2, [x2, #0xad0]
    // 0xb179d8: r0 = verifiedLocale()
    //     0xb179d8: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb179dc: stur            x0, [fp, #-8]
    // 0xb179e0: r0 = DateFormat()
    //     0xb179e0: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb179e4: mov             x3, x0
    // 0xb179e8: ldur            x0, [fp, #-8]
    // 0xb179ec: stur            x3, [fp, #-0x20]
    // 0xb179f0: StoreField: r3->field_7 = r0
    //     0xb179f0: stur            w0, [x3, #7]
    // 0xb179f4: mov             x1, x3
    // 0xb179f8: r2 = "Hm"
    //     0xb179f8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb179fc: ldr             x2, [x2, #0xad8]
    // 0xb17a00: r0 = addPattern()
    //     0xb17a00: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb17a04: ldur            x0, [fp, #-0x10]
    // 0xb17a08: LoadField: r2 = r0->field_b
    //     0xb17a08: ldur            w2, [x0, #0xb]
    // 0xb17a0c: DecompressPointer r2
    //     0xb17a0c: add             x2, x2, HEAP, lsl #32
    // 0xb17a10: ldur            x1, [fp, #-0x20]
    // 0xb17a14: r0 = format()
    //     0xb17a14: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb17a18: mov             x1, x0
    // 0xb17a1c: ldur            x0, [fp, #-0x10]
    // 0xb17a20: stur            x1, [fp, #-0x38]
    // 0xb17a24: LoadField: r2 = r0->field_f
    //     0xb17a24: ldur            w2, [x0, #0xf]
    // 0xb17a28: DecompressPointer r2
    //     0xb17a28: add             x2, x2, HEAP, lsl #32
    // 0xb17a2c: stur            x2, [fp, #-0x30]
    // 0xb17a30: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb17a30: ldur            w3, [x0, #0x17]
    // 0xb17a34: DecompressPointer r3
    //     0xb17a34: add             x3, x3, HEAP, lsl #32
    // 0xb17a38: tbnz            w3, #4, #0xb17a48
    // 0xb17a3c: r4 = Instance_IconData
    //     0xb17a3c: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2de28] Obj!IconData@e10511
    //     0xb17a40: ldr             x4, [x4, #0xe28]
    // 0xb17a44: b               #0xb17a54
    // 0xb17a48: LoadField: r3 = r0->field_13
    //     0xb17a48: ldur            w3, [x0, #0x13]
    // 0xb17a4c: DecompressPointer r3
    //     0xb17a4c: add             x3, x3, HEAP, lsl #32
    // 0xb17a50: mov             x4, x3
    // 0xb17a54: ldur            x3, [fp, #-0x18]
    // 0xb17a58: stur            x4, [fp, #-0x20]
    // 0xb17a5c: LoadField: r5 = r0->field_27
    //     0xb17a5c: ldur            w5, [x0, #0x27]
    // 0xb17a60: DecompressPointer r5
    //     0xb17a60: add             x5, x5, HEAP, lsl #32
    // 0xb17a64: stur            x5, [fp, #-8]
    // 0xb17a68: r0 = NPrayerTimeListTile()
    //     0xb17a68: bl              #0xb17b30  ; AllocateNPrayerTimeListTileStub -> NPrayerTimeListTile (size=0x24)
    // 0xb17a6c: mov             x1, x0
    // 0xb17a70: ldur            x0, [fp, #-8]
    // 0xb17a74: stur            x1, [fp, #-0x10]
    // 0xb17a78: StoreField: r1->field_b = r0
    //     0xb17a78: stur            w0, [x1, #0xb]
    // 0xb17a7c: ldur            x0, [fp, #-0x18]
    // 0xb17a80: StoreField: r1->field_f = r0
    //     0xb17a80: stur            w0, [x1, #0xf]
    // 0xb17a84: ldur            x0, [fp, #-0x38]
    // 0xb17a88: StoreField: r1->field_13 = r0
    //     0xb17a88: stur            w0, [x1, #0x13]
    // 0xb17a8c: ldur            x0, [fp, #-0x30]
    // 0xb17a90: ArrayStore: r1[0] = r0  ; List_4
    //     0xb17a90: stur            w0, [x1, #0x17]
    // 0xb17a94: ldur            x0, [fp, #-0x20]
    // 0xb17a98: StoreField: r1->field_1b = r0
    //     0xb17a98: stur            w0, [x1, #0x1b]
    // 0xb17a9c: r0 = false
    //     0xb17a9c: add             x0, NULL, #0x30  ; false
    // 0xb17aa0: StoreField: r1->field_1f = r0
    //     0xb17aa0: stur            w0, [x1, #0x1f]
    // 0xb17aa4: r0 = Padding()
    //     0xb17aa4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb17aa8: mov             x1, x0
    // 0xb17aac: r0 = Instance_EdgeInsets
    //     0xb17aac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2de30] Obj!EdgeInsets@e12b81
    //     0xb17ab0: ldr             x0, [x0, #0xe30]
    // 0xb17ab4: stur            x1, [fp, #-8]
    // 0xb17ab8: StoreField: r1->field_f = r0
    //     0xb17ab8: stur            w0, [x1, #0xf]
    // 0xb17abc: ldur            x0, [fp, #-0x10]
    // 0xb17ac0: StoreField: r1->field_b = r0
    //     0xb17ac0: stur            w0, [x1, #0xb]
    // 0xb17ac4: r0 = InkWell()
    //     0xb17ac4: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb17ac8: mov             x3, x0
    // 0xb17acc: ldur            x0, [fp, #-8]
    // 0xb17ad0: stur            x3, [fp, #-0x10]
    // 0xb17ad4: StoreField: r3->field_b = r0
    //     0xb17ad4: stur            w0, [x3, #0xb]
    // 0xb17ad8: ldur            x2, [fp, #-0x28]
    // 0xb17adc: r1 = Function '<anonymous closure>':.
    //     0xb17adc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2de38] AnonymousClosure: (0xb17b3c), in [package:nuonline/app/modules/prayer_time/views/prayer_time_view.dart] PrayerTimeView::buildPrayerTimeItem (0xb177a0)
    //     0xb17ae0: ldr             x1, [x1, #0xe38]
    // 0xb17ae4: r0 = AllocateClosure()
    //     0xb17ae4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb17ae8: mov             x1, x0
    // 0xb17aec: ldur            x0, [fp, #-0x10]
    // 0xb17af0: StoreField: r0->field_f = r1
    //     0xb17af0: stur            w1, [x0, #0xf]
    // 0xb17af4: r1 = true
    //     0xb17af4: add             x1, NULL, #0x20  ; true
    // 0xb17af8: StoreField: r0->field_43 = r1
    //     0xb17af8: stur            w1, [x0, #0x43]
    // 0xb17afc: r2 = Instance_BoxShape
    //     0xb17afc: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb17b00: ldr             x2, [x2, #0xca8]
    // 0xb17b04: StoreField: r0->field_47 = r2
    //     0xb17b04: stur            w2, [x0, #0x47]
    // 0xb17b08: StoreField: r0->field_6f = r1
    //     0xb17b08: stur            w1, [x0, #0x6f]
    // 0xb17b0c: r2 = false
    //     0xb17b0c: add             x2, NULL, #0x30  ; false
    // 0xb17b10: StoreField: r0->field_73 = r2
    //     0xb17b10: stur            w2, [x0, #0x73]
    // 0xb17b14: StoreField: r0->field_83 = r1
    //     0xb17b14: stur            w1, [x0, #0x83]
    // 0xb17b18: StoreField: r0->field_7b = r2
    //     0xb17b18: stur            w2, [x0, #0x7b]
    // 0xb17b1c: LeaveFrame
    //     0xb17b1c: mov             SP, fp
    //     0xb17b20: ldp             fp, lr, [SP], #0x10
    // 0xb17b24: ret
    //     0xb17b24: ret             
    // 0xb17b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17b28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17b2c: b               #0xb177c8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb17b3c, size: 0x80
    // 0xb17b3c: EnterFrame
    //     0xb17b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb17b40: mov             fp, SP
    // 0xb17b44: AllocStack(0x20)
    //     0xb17b44: sub             SP, SP, #0x20
    // 0xb17b48: SetupParameters()
    //     0xb17b48: ldr             x0, [fp, #0x10]
    //     0xb17b4c: ldur            w1, [x0, #0x17]
    //     0xb17b50: add             x1, x1, HEAP, lsl #32
    //     0xb17b54: stur            x1, [fp, #-8]
    // 0xb17b58: CheckStackOverflow
    //     0xb17b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17b5c: cmp             SP, x16
    //     0xb17b60: b.ls            #0xb17bb4
    // 0xb17b64: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb17b64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb17b68: ldr             x0, [x0, #0x2670]
    //     0xb17b6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb17b70: cmp             w0, w16
    //     0xb17b74: b.ne            #0xb17b80
    //     0xb17b78: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb17b7c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb17b80: ldur            x0, [fp, #-8]
    // 0xb17b84: LoadField: r1 = r0->field_f
    //     0xb17b84: ldur            w1, [x0, #0xf]
    // 0xb17b88: DecompressPointer r1
    //     0xb17b88: add             x1, x1, HEAP, lsl #32
    // 0xb17b8c: r16 = "/setting/prayer-time-notification/detail"
    //     0xb17b8c: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a260] "/setting/prayer-time-notification/detail"
    //     0xb17b90: ldr             x16, [x16, #0x260]
    // 0xb17b94: stp             x16, NULL, [SP, #8]
    // 0xb17b98: str             x1, [SP]
    // 0xb17b9c: r4 = const [0x1, 0x2, 0x2, 0x1, parameters, 0x1, null]
    //     0xb17b9c: add             x4, PP, #0x27, lsl #12  ; [pp+0x277d0] List(7) [0x1, 0x2, 0x2, 0x1, "parameters", 0x1, Null]
    //     0xb17ba0: ldr             x4, [x4, #0x7d0]
    // 0xb17ba4: r0 = GetNavigation.toNamed()
    //     0xb17ba4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb17ba8: LeaveFrame
    //     0xb17ba8: mov             SP, fp
    //     0xb17bac: ldp             fp, lr, [SP], #0x10
    // 0xb17bb0: ret
    //     0xb17bb0: ret             
    // 0xb17bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17bb8: b               #0xb17b64
  }
  [closure] SliverPersistentHeader <anonymous closure>(dynamic) {
    // ** addr: 0xb17bbc, size: 0x1b8
    // 0xb17bbc: EnterFrame
    //     0xb17bbc: stp             fp, lr, [SP, #-0x10]!
    //     0xb17bc0: mov             fp, SP
    // 0xb17bc4: AllocStack(0x38)
    //     0xb17bc4: sub             SP, SP, #0x38
    // 0xb17bc8: SetupParameters()
    //     0xb17bc8: ldr             x0, [fp, #0x10]
    //     0xb17bcc: ldur            w2, [x0, #0x17]
    //     0xb17bd0: add             x2, x2, HEAP, lsl #32
    //     0xb17bd4: stur            x2, [fp, #-8]
    // 0xb17bd8: CheckStackOverflow
    //     0xb17bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17bdc: cmp             SP, x16
    //     0xb17be0: b.ls            #0xb17d6c
    // 0xb17be4: LoadField: r1 = r2->field_f
    //     0xb17be4: ldur            w1, [x2, #0xf]
    // 0xb17be8: DecompressPointer r1
    //     0xb17be8: add             x1, x1, HEAP, lsl #32
    // 0xb17bec: r0 = controller()
    //     0xb17bec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17bf0: mov             x1, x0
    // 0xb17bf4: r0 = address()
    //     0xb17bf4: bl              #0xb17d80  ; [package:nuonline/app/modules/location/controllers/location_controller.dart] LocationController::address
    // 0xb17bf8: ldur            x0, [fp, #-8]
    // 0xb17bfc: LoadField: r1 = r0->field_f
    //     0xb17bfc: ldur            w1, [x0, #0xf]
    // 0xb17c00: DecompressPointer r1
    //     0xb17c00: add             x1, x1, HEAP, lsl #32
    // 0xb17c04: r0 = controller()
    //     0xb17c04: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17c08: LoadField: r1 = r0->field_43
    //     0xb17c08: ldur            w1, [x0, #0x43]
    // 0xb17c0c: DecompressPointer r1
    //     0xb17c0c: add             x1, x1, HEAP, lsl #32
    // 0xb17c10: r0 = value()
    //     0xb17c10: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb17c14: mov             x2, x0
    // 0xb17c18: ldur            x0, [fp, #-8]
    // 0xb17c1c: stur            x2, [fp, #-0x10]
    // 0xb17c20: LoadField: r1 = r0->field_f
    //     0xb17c20: ldur            w1, [x0, #0xf]
    // 0xb17c24: DecompressPointer r1
    //     0xb17c24: add             x1, x1, HEAP, lsl #32
    // 0xb17c28: r0 = controller()
    //     0xb17c28: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17c2c: r1 = 60
    //     0xb17c2c: movz            x1, #0x3c
    // 0xb17c30: branchIfSmi(r0, 0xb17c3c)
    //     0xb17c30: tbz             w0, #0, #0xb17c3c
    // 0xb17c34: r1 = LoadClassIdInstr(r0)
    //     0xb17c34: ldur            x1, [x0, #-1]
    //     0xb17c38: ubfx            x1, x1, #0xc, #0x14
    // 0xb17c3c: str             x0, [SP]
    // 0xb17c40: mov             x0, x1
    // 0xb17c44: r0 = GDT[cid_x0 + -0xfe0]()
    //     0xb17c44: sub             lr, x0, #0xfe0
    //     0xb17c48: ldr             lr, [x21, lr, lsl #3]
    //     0xb17c4c: blr             lr
    // 0xb17c50: mov             x2, x0
    // 0xb17c54: ldur            x0, [fp, #-8]
    // 0xb17c58: stur            x2, [fp, #-0x18]
    // 0xb17c5c: LoadField: r1 = r0->field_f
    //     0xb17c5c: ldur            w1, [x0, #0xf]
    // 0xb17c60: DecompressPointer r1
    //     0xb17c60: add             x1, x1, HEAP, lsl #32
    // 0xb17c64: r0 = controller()
    //     0xb17c64: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17c68: r1 = 60
    //     0xb17c68: movz            x1, #0x3c
    // 0xb17c6c: branchIfSmi(r0, 0xb17c78)
    //     0xb17c6c: tbz             w0, #0, #0xb17c78
    // 0xb17c70: r1 = LoadClassIdInstr(r0)
    //     0xb17c70: ldur            x1, [x0, #-1]
    //     0xb17c74: ubfx            x1, x1, #0xc, #0x14
    // 0xb17c78: str             x0, [SP]
    // 0xb17c7c: mov             x0, x1
    // 0xb17c80: r0 = GDT[cid_x0 + -0xfe2]()
    //     0xb17c80: sub             lr, x0, #0xfe2
    //     0xb17c84: ldr             lr, [x21, lr, lsl #3]
    //     0xb17c88: blr             lr
    // 0xb17c8c: mov             x2, x0
    // 0xb17c90: ldur            x0, [fp, #-8]
    // 0xb17c94: stur            x2, [fp, #-0x20]
    // 0xb17c98: LoadField: r1 = r0->field_f
    //     0xb17c98: ldur            w1, [x0, #0xf]
    // 0xb17c9c: DecompressPointer r1
    //     0xb17c9c: add             x1, x1, HEAP, lsl #32
    // 0xb17ca0: r0 = controller()
    //     0xb17ca0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17ca4: r1 = 60
    //     0xb17ca4: movz            x1, #0x3c
    // 0xb17ca8: branchIfSmi(r0, 0xb17cb4)
    //     0xb17ca8: tbz             w0, #0, #0xb17cb4
    // 0xb17cac: r1 = LoadClassIdInstr(r0)
    //     0xb17cac: ldur            x1, [x0, #-1]
    //     0xb17cb0: ubfx            x1, x1, #0xc, #0x14
    // 0xb17cb4: str             x0, [SP]
    // 0xb17cb8: mov             x0, x1
    // 0xb17cbc: r0 = GDT[cid_x0 + -0xfca]()
    //     0xb17cbc: sub             lr, x0, #0xfca
    //     0xb17cc0: ldr             lr, [x21, lr, lsl #3]
    //     0xb17cc4: blr             lr
    // 0xb17cc8: mov             x2, x0
    // 0xb17ccc: ldur            x0, [fp, #-8]
    // 0xb17cd0: stur            x2, [fp, #-0x28]
    // 0xb17cd4: LoadField: r1 = r0->field_f
    //     0xb17cd4: ldur            w1, [x0, #0xf]
    // 0xb17cd8: DecompressPointer r1
    //     0xb17cd8: add             x1, x1, HEAP, lsl #32
    // 0xb17cdc: r0 = controller()
    //     0xb17cdc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb17ce0: r1 = 60
    //     0xb17ce0: movz            x1, #0x3c
    // 0xb17ce4: branchIfSmi(r0, 0xb17cf0)
    //     0xb17ce4: tbz             w0, #0, #0xb17cf0
    // 0xb17ce8: r1 = LoadClassIdInstr(r0)
    //     0xb17ce8: ldur            x1, [x0, #-1]
    //     0xb17cec: ubfx            x1, x1, #0xc, #0x14
    // 0xb17cf0: str             x0, [SP]
    // 0xb17cf4: mov             x0, x1
    // 0xb17cf8: r0 = GDT[cid_x0 + -0xfe4]()
    //     0xb17cf8: sub             lr, x0, #0xfe4
    //     0xb17cfc: ldr             lr, [x21, lr, lsl #3]
    //     0xb17d00: blr             lr
    // 0xb17d04: stur            x0, [fp, #-8]
    // 0xb17d08: r0 = PrayerTimeSliverAppBarDelegate()
    //     0xb17d08: bl              #0xb17d74  ; AllocatePrayerTimeSliverAppBarDelegateStub -> PrayerTimeSliverAppBarDelegate (size=0x24)
    // 0xb17d0c: d0 = 364.000000
    //     0xb17d0c: add             x17, PP, #0x2d, lsl #12  ; [pp+0x2ded8] IMM: double(364) from 0x4076c00000000000
    //     0xb17d10: ldr             d0, [x17, #0xed8]
    // 0xb17d14: stur            x0, [fp, #-0x30]
    // 0xb17d18: StoreField: r0->field_7 = d0
    //     0xb17d18: stur            d0, [x0, #7]
    // 0xb17d1c: ldur            x1, [fp, #-0x10]
    // 0xb17d20: StoreField: r0->field_f = r1
    //     0xb17d20: stur            w1, [x0, #0xf]
    // 0xb17d24: ldur            x1, [fp, #-0x18]
    // 0xb17d28: StoreField: r0->field_13 = r1
    //     0xb17d28: stur            w1, [x0, #0x13]
    // 0xb17d2c: ldur            x1, [fp, #-0x20]
    // 0xb17d30: ArrayStore: r0[0] = r1  ; List_4
    //     0xb17d30: stur            w1, [x0, #0x17]
    // 0xb17d34: ldur            x1, [fp, #-0x28]
    // 0xb17d38: StoreField: r0->field_1b = r1
    //     0xb17d38: stur            w1, [x0, #0x1b]
    // 0xb17d3c: ldur            x1, [fp, #-8]
    // 0xb17d40: StoreField: r0->field_1f = r1
    //     0xb17d40: stur            w1, [x0, #0x1f]
    // 0xb17d44: r0 = SliverPersistentHeader()
    //     0xb17d44: bl              #0x9e918c  ; AllocateSliverPersistentHeaderStub -> SliverPersistentHeader (size=0x18)
    // 0xb17d48: ldur            x1, [fp, #-0x30]
    // 0xb17d4c: StoreField: r0->field_b = r1
    //     0xb17d4c: stur            w1, [x0, #0xb]
    // 0xb17d50: r1 = true
    //     0xb17d50: add             x1, NULL, #0x20  ; true
    // 0xb17d54: StoreField: r0->field_f = r1
    //     0xb17d54: stur            w1, [x0, #0xf]
    // 0xb17d58: r1 = false
    //     0xb17d58: add             x1, NULL, #0x30  ; false
    // 0xb17d5c: StoreField: r0->field_13 = r1
    //     0xb17d5c: stur            w1, [x0, #0x13]
    // 0xb17d60: LeaveFrame
    //     0xb17d60: mov             SP, fp
    //     0xb17d64: ldp             fp, lr, [SP], #0x10
    // 0xb17d68: ret
    //     0xb17d68: ret             
    // 0xb17d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17d70: b               #0xb17be4
  }
}
