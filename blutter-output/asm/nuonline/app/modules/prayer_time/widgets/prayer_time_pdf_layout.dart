// lib: , url: package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart

// class id: 1050412, size: 0x8
class :: {
}

// class id: 1054, size: 0x14, field offset: 0x8
class PrayerTimePdfLayout extends Object {

  _ build(/* No info */) async {
    // ** addr: 0xb0dec0, size: 0x1600
    // 0xb0dec0: EnterFrame
    //     0xb0dec0: stp             fp, lr, [SP, #-0x10]!
    //     0xb0dec4: mov             fp, SP
    // 0xb0dec8: AllocStack(0xa8)
    //     0xb0dec8: sub             SP, SP, #0xa8
    // 0xb0decc: SetupParameters(PrayerTimePdfLayout this /* r1 => r1, fp-0x10 */)
    //     0xb0decc: stur            NULL, [fp, #-8]
    //     0xb0ded0: stur            x1, [fp, #-0x10]
    // 0xb0ded4: CheckStackOverflow
    //     0xb0ded4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0ded8: cmp             SP, x16
    //     0xb0dedc: b.ls            #0xb0f454
    // 0xb0dee0: r1 = 6
    //     0xb0dee0: movz            x1, #0x6
    // 0xb0dee4: r0 = AllocateContext()
    //     0xb0dee4: bl              #0xec126c  ; AllocateContextStub
    // 0xb0dee8: mov             x2, x0
    // 0xb0deec: ldur            x1, [fp, #-0x10]
    // 0xb0def0: stur            x2, [fp, #-0x18]
    // 0xb0def4: StoreField: r2->field_f = r1
    //     0xb0def4: stur            w1, [x2, #0xf]
    // 0xb0def8: InitAsync() -> Future<MultiPage>
    //     0xb0def8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e108] TypeArguments: <MultiPage>
    //     0xb0defc: ldr             x0, [x0, #0x108]
    //     0xb0df00: bl              #0x661298  ; InitAsyncStub
    // 0xb0df04: r0 = InitLateStaticField(0x698) // [package:flutter/src/services/asset_bundle.dart] ::rootBundle
    //     0xb0df04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb0df08: ldr             x0, [x0, #0xd30]
    //     0xb0df0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb0df10: cmp             w0, w16
    //     0xb0df14: b.ne            #0xb0df20
    //     0xb0df18: ldr             x2, [PP, #0x3260]  ; [pp+0x3260] Field <::.rootBundle>: static late final (offset: 0x698)
    //     0xb0df1c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb0df20: mov             x1, x0
    // 0xb0df24: r2 = "packages/nuikit/assets/images/logo/logo_download.svg"
    //     0xb0df24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e110] "packages/nuikit/assets/images/logo/logo_download.svg"
    //     0xb0df28: ldr             x2, [x2, #0x110]
    // 0xb0df2c: stur            x0, [fp, #-0x20]
    // 0xb0df30: r0 = loadString()
    //     0xb0df30: bl              #0x72bd88  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadString
    // 0xb0df34: mov             x1, x0
    // 0xb0df38: stur            x1, [fp, #-0x28]
    // 0xb0df3c: r0 = Await()
    //     0xb0df3c: bl              #0x661044  ; AwaitStub
    // 0xb0df40: ldur            x3, [fp, #-0x18]
    // 0xb0df44: StoreField: r3->field_13 = r0
    //     0xb0df44: stur            w0, [x3, #0x13]
    //     0xb0df48: tbz             w0, #0, #0xb0df64
    //     0xb0df4c: ldurb           w16, [x3, #-1]
    //     0xb0df50: ldurb           w17, [x0, #-1]
    //     0xb0df54: and             x16, x17, x16, lsr #2
    //     0xb0df58: tst             x16, HEAP, lsr #32
    //     0xb0df5c: b.eq            #0xb0df64
    //     0xb0df60: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb0df64: ldur            x1, [fp, #-0x20]
    // 0xb0df68: r2 = "assets/fonts/Inter-Regular.ttf"
    //     0xb0df68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e118] "assets/fonts/Inter-Regular.ttf"
    //     0xb0df6c: ldr             x2, [x2, #0x118]
    // 0xb0df70: r0 = load()
    //     0xb0df70: bl              #0x698024  ; [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load
    // 0xb0df74: mov             x1, x0
    // 0xb0df78: stur            x1, [fp, #-0x28]
    // 0xb0df7c: r0 = Await()
    //     0xb0df7c: bl              #0x661044  ; AwaitStub
    // 0xb0df80: mov             x2, x0
    // 0xb0df84: r1 = Null
    //     0xb0df84: mov             x1, NULL
    // 0xb0df88: r0 = Font.ttf()
    //     0xb0df88: bl              #0xb121e0  ; [package:pdf/src/widgets/font.dart] Font::Font.ttf
    // 0xb0df8c: ldur            x1, [fp, #-0x20]
    // 0xb0df90: r2 = "assets/fonts/Inter-Bold.ttf"
    //     0xb0df90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e120] "assets/fonts/Inter-Bold.ttf"
    //     0xb0df94: ldr             x2, [x2, #0x120]
    // 0xb0df98: stur            x0, [fp, #-0x20]
    // 0xb0df9c: r0 = load()
    //     0xb0df9c: bl              #0x698024  ; [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load
    // 0xb0dfa0: mov             x1, x0
    // 0xb0dfa4: stur            x1, [fp, #-0x28]
    // 0xb0dfa8: r0 = Await()
    //     0xb0dfa8: bl              #0x661044  ; AwaitStub
    // 0xb0dfac: mov             x2, x0
    // 0xb0dfb0: r1 = Null
    //     0xb0dfb0: mov             x1, NULL
    // 0xb0dfb4: r0 = Font.ttf()
    //     0xb0dfb4: bl              #0xb121e0  ; [package:pdf/src/widgets/font.dart] Font::Font.ttf
    // 0xb0dfb8: r1 = _ConstMap len:3
    //     0xb0dfb8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb0dfbc: ldr             x1, [x1, #0xbe8]
    // 0xb0dfc0: r2 = 6
    //     0xb0dfc0: movz            x2, #0x6
    // 0xb0dfc4: stur            x0, [fp, #-0x28]
    // 0xb0dfc8: r0 = []()
    //     0xb0dfc8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb0dfcc: cmp             w0, NULL
    // 0xb0dfd0: b.eq            #0xb0f45c
    // 0xb0dfd4: r1 = LoadClassIdInstr(r0)
    //     0xb0dfd4: ldur            x1, [x0, #-1]
    //     0xb0dfd8: ubfx            x1, x1, #0xc, #0x14
    // 0xb0dfdc: mov             x16, x0
    // 0xb0dfe0: mov             x0, x1
    // 0xb0dfe4: mov             x1, x16
    // 0xb0dfe8: r0 = GDT[cid_x0 + -0xd99]()
    //     0xb0dfe8: sub             lr, x0, #0xd99
    //     0xb0dfec: ldr             lr, [x21, lr, lsl #3]
    //     0xb0dff0: blr             lr
    // 0xb0dff4: stur            x0, [fp, #-0x30]
    // 0xb0dff8: asr             x1, x0, #0x10
    // 0xb0dffc: ubfx            x1, x1, #0, #0x20
    // 0xb0e000: r2 = 255
    //     0xb0e000: movz            x2, #0xff
    // 0xb0e004: and             x3, x1, x2
    // 0xb0e008: ubfx            x3, x3, #0, #0x20
    // 0xb0e00c: scvtf           d0, x3
    // 0xb0e010: d1 = 255.000000
    //     0xb0e010: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb0e014: fdiv            d2, d0, d1
    // 0xb0e018: stur            d2, [fp, #-0x90]
    // 0xb0e01c: r0 = PdfColor()
    //     0xb0e01c: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xb0e020: ldur            d0, [fp, #-0x90]
    // 0xb0e024: stur            x0, [fp, #-0x38]
    // 0xb0e028: StoreField: r0->field_f = d0
    //     0xb0e028: stur            d0, [x0, #0xf]
    // 0xb0e02c: ldur            x1, [fp, #-0x30]
    // 0xb0e030: asr             x2, x1, #8
    // 0xb0e034: ubfx            x2, x2, #0, #0x20
    // 0xb0e038: r3 = 255
    //     0xb0e038: movz            x3, #0xff
    // 0xb0e03c: and             x4, x2, x3
    // 0xb0e040: ubfx            x4, x4, #0, #0x20
    // 0xb0e044: scvtf           d0, x4
    // 0xb0e048: d1 = 255.000000
    //     0xb0e048: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb0e04c: fdiv            d2, d0, d1
    // 0xb0e050: ArrayStore: r0[0] = d2  ; List_8
    //     0xb0e050: stur            d2, [x0, #0x17]
    // 0xb0e054: mov             x2, x1
    // 0xb0e058: ubfx            x2, x2, #0, #0x20
    // 0xb0e05c: and             x4, x2, x3
    // 0xb0e060: ubfx            x4, x4, #0, #0x20
    // 0xb0e064: scvtf           d0, x4
    // 0xb0e068: fdiv            d2, d0, d1
    // 0xb0e06c: StoreField: r0->field_1f = d2
    //     0xb0e06c: stur            d2, [x0, #0x1f]
    // 0xb0e070: asr             x2, x1, #0x18
    // 0xb0e074: ubfx            x2, x2, #0, #0x20
    // 0xb0e078: and             x1, x2, x3
    // 0xb0e07c: ubfx            x1, x1, #0, #0x20
    // 0xb0e080: scvtf           d0, x1
    // 0xb0e084: fdiv            d2, d0, d1
    // 0xb0e088: StoreField: r0->field_7 = d2
    //     0xb0e088: stur            d2, [x0, #7]
    // 0xb0e08c: r0 = TextStyle()
    //     0xb0e08c: bl              #0xb121c8  ; AllocateTextStyleStub -> TextStyle (size=0x58)
    // 0xb0e090: mov             x2, x0
    // 0xb0e094: r0 = true
    //     0xb0e094: add             x0, NULL, #0x20  ; true
    // 0xb0e098: stur            x2, [fp, #-0x40]
    // 0xb0e09c: StoreField: r2->field_7 = r0
    //     0xb0e09c: stur            w0, [x2, #7]
    // 0xb0e0a0: ldur            x0, [fp, #-0x38]
    // 0xb0e0a4: StoreField: r2->field_b = r0
    //     0xb0e0a4: stur            w0, [x2, #0xb]
    // 0xb0e0a8: r0 = const []
    //     0xb0e0a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e128] List<Font>(0)
    //     0xb0e0ac: ldr             x0, [x0, #0x128]
    // 0xb0e0b0: StoreField: r2->field_1f = r0
    //     0xb0e0b0: stur            w0, [x2, #0x1f]
    // 0xb0e0b4: ldur            x0, [fp, #-0x20]
    // 0xb0e0b8: StoreField: r2->field_f = r0
    //     0xb0e0b8: stur            w0, [x2, #0xf]
    // 0xb0e0bc: ldur            x0, [fp, #-0x28]
    // 0xb0e0c0: StoreField: r2->field_13 = r0
    //     0xb0e0c0: stur            w0, [x2, #0x13]
    // 0xb0e0c4: ldur            x0, [fp, #-0x10]
    // 0xb0e0c8: LoadField: r3 = r0->field_b
    //     0xb0e0c8: ldur            w3, [x0, #0xb]
    // 0xb0e0cc: DecompressPointer r3
    //     0xb0e0cc: add             x3, x3, HEAP, lsl #32
    // 0xb0e0d0: mov             x1, x3
    // 0xb0e0d4: stur            x3, [fp, #-0x20]
    // 0xb0e0d8: r0 = days()
    //     0xb0e0d8: bl              #0xb11704  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::days
    // 0xb0e0dc: r1 = Null
    //     0xb0e0dc: mov             x1, NULL
    // 0xb0e0e0: r2 = 2
    //     0xb0e0e0: movz            x2, #0x2
    // 0xb0e0e4: stur            x0, [fp, #-0x28]
    // 0xb0e0e8: r0 = AllocateArray()
    //     0xb0e0e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0e0ec: stur            x0, [fp, #-0x38]
    // 0xb0e0f0: r16 = "Jadwal "
    //     0xb0e0f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e130] "Jadwal "
    //     0xb0e0f4: ldr             x16, [x16, #0x130]
    // 0xb0e0f8: StoreField: r0->field_f = r16
    //     0xb0e0f8: stur            w16, [x0, #0xf]
    // 0xb0e0fc: r1 = <String>
    //     0xb0e0fc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e100: r0 = AllocateGrowableArray()
    //     0xb0e100: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0e104: mov             x2, x0
    // 0xb0e108: ldur            x0, [fp, #-0x38]
    // 0xb0e10c: stur            x2, [fp, #-0x48]
    // 0xb0e110: StoreField: r2->field_f = r0
    //     0xb0e110: stur            w0, [x2, #0xf]
    // 0xb0e114: r0 = 2
    //     0xb0e114: movz            x0, #0x2
    // 0xb0e118: StoreField: r2->field_b = r0
    //     0xb0e118: stur            w0, [x2, #0xb]
    // 0xb0e11c: ldur            x1, [fp, #-0x20]
    // 0xb0e120: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb0e120: ldur            w3, [x1, #0x17]
    // 0xb0e124: DecompressPointer r3
    //     0xb0e124: add             x3, x3, HEAP, lsl #32
    // 0xb0e128: stur            x3, [fp, #-0x38]
    // 0xb0e12c: tbnz            w3, #4, #0xb0e174
    // 0xb0e130: LoadField: r4 = r1->field_7
    //     0xb0e130: ldur            x4, [x1, #7]
    // 0xb0e134: cmp             x4, #9
    // 0xb0e138: b.ne            #0xb0e168
    // 0xb0e13c: mov             x1, x2
    // 0xb0e140: r0 = _growToNextCapacity()
    //     0xb0e140: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e144: ldur            x0, [fp, #-0x48]
    // 0xb0e148: r2 = 4
    //     0xb0e148: movz            x2, #0x4
    // 0xb0e14c: StoreField: r0->field_b = r2
    //     0xb0e14c: stur            w2, [x0, #0xb]
    // 0xb0e150: LoadField: r1 = r0->field_f
    //     0xb0e150: ldur            w1, [x0, #0xf]
    // 0xb0e154: DecompressPointer r1
    //     0xb0e154: add             x1, x1, HEAP, lsl #32
    // 0xb0e158: r16 = "Imsakiyah "
    //     0xb0e158: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e138] "Imsakiyah "
    //     0xb0e15c: ldr             x16, [x16, #0x138]
    // 0xb0e160: StoreField: r1->field_13 = r16
    //     0xb0e160: stur            w16, [x1, #0x13]
    // 0xb0e164: b               #0xb0e1a4
    // 0xb0e168: mov             x0, x2
    // 0xb0e16c: r2 = 4
    //     0xb0e16c: movz            x2, #0x4
    // 0xb0e170: b               #0xb0e17c
    // 0xb0e174: mov             x0, x2
    // 0xb0e178: r2 = 4
    //     0xb0e178: movz            x2, #0x4
    // 0xb0e17c: mov             x1, x0
    // 0xb0e180: r0 = _growToNextCapacity()
    //     0xb0e180: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e184: ldur            x0, [fp, #-0x48]
    // 0xb0e188: r2 = 4
    //     0xb0e188: movz            x2, #0x4
    // 0xb0e18c: StoreField: r0->field_b = r2
    //     0xb0e18c: stur            w2, [x0, #0xb]
    // 0xb0e190: LoadField: r1 = r0->field_f
    //     0xb0e190: ldur            w1, [x0, #0xf]
    // 0xb0e194: DecompressPointer r1
    //     0xb0e194: add             x1, x1, HEAP, lsl #32
    // 0xb0e198: r16 = "Shalat "
    //     0xb0e198: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e140] "Shalat "
    //     0xb0e19c: ldr             x16, [x16, #0x140]
    // 0xb0e1a0: StoreField: r1->field_13 = r16
    //     0xb0e1a0: stur            w16, [x1, #0x13]
    // 0xb0e1a4: LoadField: r3 = r1->field_b
    //     0xb0e1a4: ldur            w3, [x1, #0xb]
    // 0xb0e1a8: cmp             w3, #4
    // 0xb0e1ac: b.ne            #0xb0e1b8
    // 0xb0e1b0: mov             x1, x0
    // 0xb0e1b4: r0 = _growToNextCapacity()
    //     0xb0e1b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e1b8: ldur            x2, [fp, #-0x38]
    // 0xb0e1bc: ldur            x0, [fp, #-0x48]
    // 0xb0e1c0: r1 = 6
    //     0xb0e1c0: movz            x1, #0x6
    // 0xb0e1c4: StoreField: r0->field_b = r1
    //     0xb0e1c4: stur            w1, [x0, #0xb]
    // 0xb0e1c8: LoadField: r1 = r0->field_f
    //     0xb0e1c8: ldur            w1, [x0, #0xf]
    // 0xb0e1cc: DecompressPointer r1
    //     0xb0e1cc: add             x1, x1, HEAP, lsl #32
    // 0xb0e1d0: r16 = "Bulan "
    //     0xb0e1d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e148] "Bulan "
    //     0xb0e1d4: ldr             x16, [x16, #0x148]
    // 0xb0e1d8: ArrayStore: r1[0] = r16  ; List_4
    //     0xb0e1d8: stur            w16, [x1, #0x17]
    // 0xb0e1dc: tbnz            w2, #4, #0xb0e288
    // 0xb0e1e0: ldur            x1, [fp, #-0x28]
    // 0xb0e1e4: r0 = first()
    //     0xb0e1e4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xb0e1e8: mov             x1, x0
    // 0xb0e1ec: r0 = toHijri()
    //     0xb0e1ec: bl              #0x815550  ; [package:nuonline/services/hijri_service.dart] HijriService::toHijri
    // 0xb0e1f0: mov             x1, x0
    // 0xb0e1f4: r0 = mY()
    //     0xb0e1f4: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0xb0e1f8: mov             x2, x0
    // 0xb0e1fc: ldur            x0, [fp, #-0x48]
    // 0xb0e200: stur            x2, [fp, #-0x20]
    // 0xb0e204: LoadField: r1 = r0->field_b
    //     0xb0e204: ldur            w1, [x0, #0xb]
    // 0xb0e208: LoadField: r3 = r0->field_f
    //     0xb0e208: ldur            w3, [x0, #0xf]
    // 0xb0e20c: DecompressPointer r3
    //     0xb0e20c: add             x3, x3, HEAP, lsl #32
    // 0xb0e210: LoadField: r4 = r3->field_b
    //     0xb0e210: ldur            w4, [x3, #0xb]
    // 0xb0e214: r3 = LoadInt32Instr(r1)
    //     0xb0e214: sbfx            x3, x1, #1, #0x1f
    // 0xb0e218: stur            x3, [fp, #-0x30]
    // 0xb0e21c: r1 = LoadInt32Instr(r4)
    //     0xb0e21c: sbfx            x1, x4, #1, #0x1f
    // 0xb0e220: cmp             x3, x1
    // 0xb0e224: b.ne            #0xb0e230
    // 0xb0e228: mov             x1, x0
    // 0xb0e22c: r0 = _growToNextCapacity()
    //     0xb0e22c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e230: ldur            x2, [fp, #-0x48]
    // 0xb0e234: ldur            x3, [fp, #-0x30]
    // 0xb0e238: add             x4, x3, #1
    // 0xb0e23c: lsl             x0, x4, #1
    // 0xb0e240: StoreField: r2->field_b = r0
    //     0xb0e240: stur            w0, [x2, #0xb]
    // 0xb0e244: LoadField: r5 = r2->field_f
    //     0xb0e244: ldur            w5, [x2, #0xf]
    // 0xb0e248: DecompressPointer r5
    //     0xb0e248: add             x5, x5, HEAP, lsl #32
    // 0xb0e24c: mov             x1, x5
    // 0xb0e250: ldur            x0, [fp, #-0x20]
    // 0xb0e254: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0e254: add             x25, x1, x3, lsl #2
    //     0xb0e258: add             x25, x25, #0xf
    //     0xb0e25c: str             w0, [x25]
    //     0xb0e260: tbz             w0, #0, #0xb0e27c
    //     0xb0e264: ldurb           w16, [x1, #-1]
    //     0xb0e268: ldurb           w17, [x0, #-1]
    //     0xb0e26c: and             x16, x17, x16, lsr #2
    //     0xb0e270: tst             x16, HEAP, lsr #32
    //     0xb0e274: b.eq            #0xb0e27c
    //     0xb0e278: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0e27c: mov             x3, x4
    // 0xb0e280: mov             x0, x5
    // 0xb0e284: b               #0xb0e328
    // 0xb0e288: mov             x2, x0
    // 0xb0e28c: ldur            x1, [fp, #-0x28]
    // 0xb0e290: r0 = first()
    //     0xb0e290: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xb0e294: mov             x1, x0
    // 0xb0e298: r0 = DateTimeExtensions.mY()
    //     0xb0e298: bl              #0x83a2fc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.mY
    // 0xb0e29c: mov             x2, x0
    // 0xb0e2a0: ldur            x0, [fp, #-0x48]
    // 0xb0e2a4: stur            x2, [fp, #-0x20]
    // 0xb0e2a8: LoadField: r1 = r0->field_b
    //     0xb0e2a8: ldur            w1, [x0, #0xb]
    // 0xb0e2ac: LoadField: r3 = r0->field_f
    //     0xb0e2ac: ldur            w3, [x0, #0xf]
    // 0xb0e2b0: DecompressPointer r3
    //     0xb0e2b0: add             x3, x3, HEAP, lsl #32
    // 0xb0e2b4: LoadField: r4 = r3->field_b
    //     0xb0e2b4: ldur            w4, [x3, #0xb]
    // 0xb0e2b8: r3 = LoadInt32Instr(r1)
    //     0xb0e2b8: sbfx            x3, x1, #1, #0x1f
    // 0xb0e2bc: stur            x3, [fp, #-0x30]
    // 0xb0e2c0: r1 = LoadInt32Instr(r4)
    //     0xb0e2c0: sbfx            x1, x4, #1, #0x1f
    // 0xb0e2c4: cmp             x3, x1
    // 0xb0e2c8: b.ne            #0xb0e2d4
    // 0xb0e2cc: mov             x1, x0
    // 0xb0e2d0: r0 = _growToNextCapacity()
    //     0xb0e2d0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e2d4: ldur            x2, [fp, #-0x48]
    // 0xb0e2d8: ldur            x3, [fp, #-0x30]
    // 0xb0e2dc: add             x4, x3, #1
    // 0xb0e2e0: lsl             x0, x4, #1
    // 0xb0e2e4: StoreField: r2->field_b = r0
    //     0xb0e2e4: stur            w0, [x2, #0xb]
    // 0xb0e2e8: LoadField: r5 = r2->field_f
    //     0xb0e2e8: ldur            w5, [x2, #0xf]
    // 0xb0e2ec: DecompressPointer r5
    //     0xb0e2ec: add             x5, x5, HEAP, lsl #32
    // 0xb0e2f0: mov             x1, x5
    // 0xb0e2f4: ldur            x0, [fp, #-0x20]
    // 0xb0e2f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0e2f8: add             x25, x1, x3, lsl #2
    //     0xb0e2fc: add             x25, x25, #0xf
    //     0xb0e300: str             w0, [x25]
    //     0xb0e304: tbz             w0, #0, #0xb0e320
    //     0xb0e308: ldurb           w16, [x1, #-1]
    //     0xb0e30c: ldurb           w17, [x0, #-1]
    //     0xb0e310: and             x16, x17, x16, lsr #2
    //     0xb0e314: tst             x16, HEAP, lsr #32
    //     0xb0e318: b.eq            #0xb0e320
    //     0xb0e31c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0e320: mov             x3, x4
    // 0xb0e324: mov             x0, x5
    // 0xb0e328: stur            x3, [fp, #-0x30]
    // 0xb0e32c: LoadField: r1 = r0->field_b
    //     0xb0e32c: ldur            w1, [x0, #0xb]
    // 0xb0e330: r0 = LoadInt32Instr(r1)
    //     0xb0e330: sbfx            x0, x1, #1, #0x1f
    // 0xb0e334: cmp             x3, x0
    // 0xb0e338: b.ne            #0xb0e344
    // 0xb0e33c: mov             x1, x2
    // 0xb0e340: r0 = _growToNextCapacity()
    //     0xb0e340: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e344: ldur            x3, [fp, #-0x48]
    // 0xb0e348: ldur            x2, [fp, #-0x30]
    // 0xb0e34c: add             x0, x2, #1
    // 0xb0e350: lsl             x1, x0, #1
    // 0xb0e354: StoreField: r3->field_b = r1
    //     0xb0e354: stur            w1, [x3, #0xb]
    // 0xb0e358: mov             x1, x2
    // 0xb0e35c: cmp             x1, x0
    // 0xb0e360: b.hs            #0xb0f460
    // 0xb0e364: LoadField: r0 = r3->field_f
    //     0xb0e364: ldur            w0, [x3, #0xf]
    // 0xb0e368: DecompressPointer r0
    //     0xb0e368: add             x0, x0, HEAP, lsl #32
    // 0xb0e36c: add             x1, x0, x2, lsl #2
    // 0xb0e370: r16 = " ("
    //     0xb0e370: ldr             x16, [PP, #0x980]  ; [pp+0x980] " ("
    // 0xb0e374: StoreField: r1->field_f = r16
    //     0xb0e374: stur            w16, [x1, #0xf]
    // 0xb0e378: ldur            x2, [fp, #-0x18]
    // 0xb0e37c: r1 = Function '<anonymous closure>':.
    //     0xb0e37c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e150] AnonymousClosure: (0xb15600), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0e380: ldr             x1, [x1, #0x150]
    // 0xb0e384: r0 = AllocateClosure()
    //     0xb0e384: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0e388: r16 = <String>
    //     0xb0e388: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e38c: ldur            lr, [fp, #-0x28]
    // 0xb0e390: stp             lr, x16, [SP, #8]
    // 0xb0e394: str             x0, [SP]
    // 0xb0e398: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0e398: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0e39c: r0 = map()
    //     0xb0e39c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb0e3a0: mov             x1, x0
    // 0xb0e3a4: r0 = toSet()
    //     0xb0e3a4: bl              #0x7aa054  ; [dart:_internal] ListIterable::toSet
    // 0xb0e3a8: r16 = " - "
    //     0xb0e3a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c180] " - "
    //     0xb0e3ac: ldr             x16, [x16, #0x180]
    // 0xb0e3b0: str             x16, [SP]
    // 0xb0e3b4: mov             x1, x0
    // 0xb0e3b8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0e3b8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0e3bc: r0 = join()
    //     0xb0e3bc: bl              #0x86c7d4  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::join
    // 0xb0e3c0: mov             x2, x0
    // 0xb0e3c4: ldur            x0, [fp, #-0x48]
    // 0xb0e3c8: stur            x2, [fp, #-0x20]
    // 0xb0e3cc: LoadField: r1 = r0->field_b
    //     0xb0e3cc: ldur            w1, [x0, #0xb]
    // 0xb0e3d0: LoadField: r3 = r0->field_f
    //     0xb0e3d0: ldur            w3, [x0, #0xf]
    // 0xb0e3d4: DecompressPointer r3
    //     0xb0e3d4: add             x3, x3, HEAP, lsl #32
    // 0xb0e3d8: LoadField: r4 = r3->field_b
    //     0xb0e3d8: ldur            w4, [x3, #0xb]
    // 0xb0e3dc: r3 = LoadInt32Instr(r1)
    //     0xb0e3dc: sbfx            x3, x1, #1, #0x1f
    // 0xb0e3e0: stur            x3, [fp, #-0x30]
    // 0xb0e3e4: r1 = LoadInt32Instr(r4)
    //     0xb0e3e4: sbfx            x1, x4, #1, #0x1f
    // 0xb0e3e8: cmp             x3, x1
    // 0xb0e3ec: b.ne            #0xb0e3f8
    // 0xb0e3f0: mov             x1, x0
    // 0xb0e3f4: r0 = _growToNextCapacity()
    //     0xb0e3f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e3f8: ldur            x2, [fp, #-0x48]
    // 0xb0e3fc: ldur            x3, [fp, #-0x30]
    // 0xb0e400: add             x4, x3, #1
    // 0xb0e404: stur            x4, [fp, #-0x50]
    // 0xb0e408: lsl             x0, x4, #1
    // 0xb0e40c: StoreField: r2->field_b = r0
    //     0xb0e40c: stur            w0, [x2, #0xb]
    // 0xb0e410: LoadField: r5 = r2->field_f
    //     0xb0e410: ldur            w5, [x2, #0xf]
    // 0xb0e414: DecompressPointer r5
    //     0xb0e414: add             x5, x5, HEAP, lsl #32
    // 0xb0e418: mov             x1, x5
    // 0xb0e41c: ldur            x0, [fp, #-0x20]
    // 0xb0e420: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0e420: add             x25, x1, x3, lsl #2
    //     0xb0e424: add             x25, x25, #0xf
    //     0xb0e428: str             w0, [x25]
    //     0xb0e42c: tbz             w0, #0, #0xb0e448
    //     0xb0e430: ldurb           w16, [x1, #-1]
    //     0xb0e434: ldurb           w17, [x0, #-1]
    //     0xb0e438: and             x16, x17, x16, lsr #2
    //     0xb0e43c: tst             x16, HEAP, lsr #32
    //     0xb0e440: b.eq            #0xb0e448
    //     0xb0e444: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0e448: LoadField: r0 = r5->field_b
    //     0xb0e448: ldur            w0, [x5, #0xb]
    // 0xb0e44c: r1 = LoadInt32Instr(r0)
    //     0xb0e44c: sbfx            x1, x0, #1, #0x1f
    // 0xb0e450: cmp             x4, x1
    // 0xb0e454: b.ne            #0xb0e460
    // 0xb0e458: mov             x1, x2
    // 0xb0e45c: r0 = _growToNextCapacity()
    //     0xb0e45c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e460: ldur            x3, [fp, #-0x10]
    // 0xb0e464: ldur            x4, [fp, #-0x18]
    // 0xb0e468: ldur            x2, [fp, #-0x38]
    // 0xb0e46c: ldur            x0, [fp, #-0x50]
    // 0xb0e470: ldur            x1, [fp, #-0x48]
    // 0xb0e474: add             x5, x0, #1
    // 0xb0e478: lsl             x6, x5, #1
    // 0xb0e47c: StoreField: r1->field_b = r6
    //     0xb0e47c: stur            w6, [x1, #0xb]
    // 0xb0e480: LoadField: r5 = r1->field_f
    //     0xb0e480: ldur            w5, [x1, #0xf]
    // 0xb0e484: DecompressPointer r5
    //     0xb0e484: add             x5, x5, HEAP, lsl #32
    // 0xb0e488: add             x6, x5, x0, lsl #2
    // 0xb0e48c: r16 = ")"
    //     0xb0e48c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xb0e490: StoreField: r6->field_f = r16
    //     0xb0e490: stur            w16, [x6, #0xf]
    // 0xb0e494: r16 = ""
    //     0xb0e494: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb0e498: str             x16, [SP]
    // 0xb0e49c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0e49c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0e4a0: r0 = join()
    //     0xb0e4a0: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb0e4a4: ldur            x2, [fp, #-0x18]
    // 0xb0e4a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0e4a8: stur            w0, [x2, #0x17]
    //     0xb0e4ac: ldurb           w16, [x2, #-1]
    //     0xb0e4b0: ldurb           w17, [x0, #-1]
    //     0xb0e4b4: and             x16, x17, x16, lsr #2
    //     0xb0e4b8: tst             x16, HEAP, lsr #32
    //     0xb0e4bc: b.eq            #0xb0e4c4
    //     0xb0e4c0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb0e4c4: ldur            x0, [fp, #-0x10]
    // 0xb0e4c8: LoadField: r1 = r0->field_7
    //     0xb0e4c8: ldur            w1, [x0, #7]
    // 0xb0e4cc: DecompressPointer r1
    //     0xb0e4cc: add             x1, x1, HEAP, lsl #32
    // 0xb0e4d0: r0 = getDMS()
    //     0xb0e4d0: bl              #0xb1144c  ; [package:nuonline/app/data/models/location.dart] Location::getDMS
    // 0xb0e4d4: r1 = Null
    //     0xb0e4d4: mov             x1, NULL
    // 0xb0e4d8: r2 = 4
    //     0xb0e4d8: movz            x2, #0x4
    // 0xb0e4dc: stur            x0, [fp, #-0x20]
    // 0xb0e4e0: r0 = AllocateArray()
    //     0xb0e4e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0e4e4: stur            x0, [fp, #-0x48]
    // 0xb0e4e8: r16 = "Koordinat:"
    //     0xb0e4e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e158] "Koordinat:"
    //     0xb0e4ec: ldr             x16, [x16, #0x158]
    // 0xb0e4f0: StoreField: r0->field_f = r16
    //     0xb0e4f0: stur            w16, [x0, #0xf]
    // 0xb0e4f4: ldur            x1, [fp, #-0x20]
    // 0xb0e4f8: StoreField: r0->field_13 = r1
    //     0xb0e4f8: stur            w1, [x0, #0x13]
    // 0xb0e4fc: r1 = <String>
    //     0xb0e4fc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e500: r0 = AllocateGrowableArray()
    //     0xb0e500: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0e504: mov             x1, x0
    // 0xb0e508: ldur            x0, [fp, #-0x48]
    // 0xb0e50c: StoreField: r1->field_f = r0
    //     0xb0e50c: stur            w0, [x1, #0xf]
    // 0xb0e510: r2 = 4
    //     0xb0e510: movz            x2, #0x4
    // 0xb0e514: StoreField: r1->field_b = r2
    //     0xb0e514: stur            w2, [x1, #0xb]
    // 0xb0e518: r16 = " "
    //     0xb0e518: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb0e51c: str             x16, [SP]
    // 0xb0e520: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0e520: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0e524: r0 = join()
    //     0xb0e524: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb0e528: ldur            x3, [fp, #-0x18]
    // 0xb0e52c: StoreField: r3->field_1b = r0
    //     0xb0e52c: stur            w0, [x3, #0x1b]
    //     0xb0e530: ldurb           w16, [x3, #-1]
    //     0xb0e534: ldurb           w17, [x0, #-1]
    //     0xb0e538: and             x16, x17, x16, lsr #2
    //     0xb0e53c: tst             x16, HEAP, lsr #32
    //     0xb0e540: b.eq            #0xb0e548
    //     0xb0e544: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb0e548: r1 = Function '<anonymous closure>':.
    //     0xb0e548: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e160] AnonymousClosure: (0xb15554), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0e54c: ldr             x1, [x1, #0x160]
    // 0xb0e550: r2 = Null
    //     0xb0e550: mov             x2, NULL
    // 0xb0e554: r0 = AllocateClosure()
    //     0xb0e554: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0e558: r16 = <String>
    //     0xb0e558: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e55c: ldur            lr, [fp, #-0x28]
    // 0xb0e560: stp             lr, x16, [SP, #8]
    // 0xb0e564: str             x0, [SP]
    // 0xb0e568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0e568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0e56c: r0 = map()
    //     0xb0e56c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb0e570: mov             x1, x0
    // 0xb0e574: r0 = toSet()
    //     0xb0e574: bl              #0x7aa054  ; [dart:_internal] ListIterable::toSet
    // 0xb0e578: r16 = "/\n"
    //     0xb0e578: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e168] "/\n"
    //     0xb0e57c: ldr             x16, [x16, #0x168]
    // 0xb0e580: str             x16, [SP]
    // 0xb0e584: mov             x1, x0
    // 0xb0e588: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0e588: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0e58c: r0 = join()
    //     0xb0e58c: bl              #0x86c7d4  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::join
    // 0xb0e590: r1 = Function '<anonymous closure>':.
    //     0xb0e590: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e170] AnonymousClosure: (0xb1551c), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0e594: ldr             x1, [x1, #0x170]
    // 0xb0e598: r2 = Null
    //     0xb0e598: mov             x2, NULL
    // 0xb0e59c: stur            x0, [fp, #-0x20]
    // 0xb0e5a0: r0 = AllocateClosure()
    //     0xb0e5a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0e5a4: r16 = <String>
    //     0xb0e5a4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e5a8: ldur            lr, [fp, #-0x28]
    // 0xb0e5ac: stp             lr, x16, [SP, #8]
    // 0xb0e5b0: str             x0, [SP]
    // 0xb0e5b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0e5b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0e5b8: r0 = map()
    //     0xb0e5b8: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb0e5bc: mov             x1, x0
    // 0xb0e5c0: r0 = toSet()
    //     0xb0e5c0: bl              #0x7aa054  ; [dart:_internal] ListIterable::toSet
    // 0xb0e5c4: r16 = "/\n"
    //     0xb0e5c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e168] "/\n"
    //     0xb0e5c8: ldr             x16, [x16, #0x168]
    // 0xb0e5cc: str             x16, [SP]
    // 0xb0e5d0: mov             x1, x0
    // 0xb0e5d4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0e5d4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0e5d8: r0 = join()
    //     0xb0e5d8: bl              #0x86c7d4  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::join
    // 0xb0e5dc: r1 = Null
    //     0xb0e5dc: mov             x1, NULL
    // 0xb0e5e0: r2 = 4
    //     0xb0e5e0: movz            x2, #0x4
    // 0xb0e5e4: stur            x0, [fp, #-0x48]
    // 0xb0e5e8: r0 = AllocateArray()
    //     0xb0e5e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0e5ec: mov             x2, x0
    // 0xb0e5f0: ldur            x0, [fp, #-0x20]
    // 0xb0e5f4: stur            x2, [fp, #-0x58]
    // 0xb0e5f8: StoreField: r2->field_f = r0
    //     0xb0e5f8: stur            w0, [x2, #0xf]
    // 0xb0e5fc: ldur            x0, [fp, #-0x48]
    // 0xb0e600: StoreField: r2->field_13 = r0
    //     0xb0e600: stur            w0, [x2, #0x13]
    // 0xb0e604: r1 = <String>
    //     0xb0e604: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e608: r0 = AllocateGrowableArray()
    //     0xb0e608: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0e60c: mov             x1, x0
    // 0xb0e610: ldur            x0, [fp, #-0x58]
    // 0xb0e614: stur            x1, [fp, #-0x20]
    // 0xb0e618: StoreField: r1->field_f = r0
    //     0xb0e618: stur            w0, [x1, #0xf]
    // 0xb0e61c: r2 = 4
    //     0xb0e61c: movz            x2, #0x4
    // 0xb0e620: StoreField: r1->field_b = r2
    //     0xb0e620: stur            w2, [x1, #0xb]
    // 0xb0e624: ldur            x0, [fp, #-0x38]
    // 0xb0e628: tbnz            w0, #4, #0xb0e63c
    // 0xb0e62c: r16 = <String>
    //     0xb0e62c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e630: stp             x1, x16, [SP]
    // 0xb0e634: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0e634: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0e638: r0 = ListExtensions.swap()
    //     0xb0e638: bl              #0xb11310  ; [package:collection/src/list_extensions.dart] ::ListExtensions.swap
    // 0xb0e63c: r0 = 2
    //     0xb0e63c: movz            x0, #0x2
    // 0xb0e640: mov             x2, x0
    // 0xb0e644: r1 = Null
    //     0xb0e644: mov             x1, NULL
    // 0xb0e648: r0 = AllocateArray()
    //     0xb0e648: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0e64c: stur            x0, [fp, #-0x48]
    // 0xb0e650: r16 = "Hari"
    //     0xb0e650: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e178] "Hari"
    //     0xb0e654: ldr             x16, [x16, #0x178]
    // 0xb0e658: StoreField: r0->field_f = r16
    //     0xb0e658: stur            w16, [x0, #0xf]
    // 0xb0e65c: r1 = <String>
    //     0xb0e65c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0e660: r0 = AllocateGrowableArray()
    //     0xb0e660: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0e664: mov             x3, x0
    // 0xb0e668: ldur            x0, [fp, #-0x48]
    // 0xb0e66c: stur            x3, [fp, #-0x58]
    // 0xb0e670: StoreField: r3->field_f = r0
    //     0xb0e670: stur            w0, [x3, #0xf]
    // 0xb0e674: r0 = 2
    //     0xb0e674: movz            x0, #0x2
    // 0xb0e678: StoreField: r3->field_b = r0
    //     0xb0e678: stur            w0, [x3, #0xb]
    // 0xb0e67c: mov             x1, x3
    // 0xb0e680: ldur            x2, [fp, #-0x20]
    // 0xb0e684: r0 = addAll()
    //     0xb0e684: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb0e688: ldur            x0, [fp, #-0x58]
    // 0xb0e68c: LoadField: r1 = r0->field_b
    //     0xb0e68c: ldur            w1, [x0, #0xb]
    // 0xb0e690: LoadField: r2 = r0->field_f
    //     0xb0e690: ldur            w2, [x0, #0xf]
    // 0xb0e694: DecompressPointer r2
    //     0xb0e694: add             x2, x2, HEAP, lsl #32
    // 0xb0e698: LoadField: r3 = r2->field_b
    //     0xb0e698: ldur            w3, [x2, #0xb]
    // 0xb0e69c: r2 = LoadInt32Instr(r1)
    //     0xb0e69c: sbfx            x2, x1, #1, #0x1f
    // 0xb0e6a0: stur            x2, [fp, #-0x30]
    // 0xb0e6a4: r1 = LoadInt32Instr(r3)
    //     0xb0e6a4: sbfx            x1, x3, #1, #0x1f
    // 0xb0e6a8: cmp             x2, x1
    // 0xb0e6ac: b.ne            #0xb0e6b8
    // 0xb0e6b0: mov             x1, x0
    // 0xb0e6b4: r0 = _growToNextCapacity()
    //     0xb0e6b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e6b8: ldur            x0, [fp, #-0x58]
    // 0xb0e6bc: ldur            x1, [fp, #-0x30]
    // 0xb0e6c0: add             x2, x1, #1
    // 0xb0e6c4: stur            x2, [fp, #-0x50]
    // 0xb0e6c8: lsl             x3, x2, #1
    // 0xb0e6cc: StoreField: r0->field_b = r3
    //     0xb0e6cc: stur            w3, [x0, #0xb]
    // 0xb0e6d0: LoadField: r3 = r0->field_f
    //     0xb0e6d0: ldur            w3, [x0, #0xf]
    // 0xb0e6d4: DecompressPointer r3
    //     0xb0e6d4: add             x3, x3, HEAP, lsl #32
    // 0xb0e6d8: add             x4, x3, x1, lsl #2
    // 0xb0e6dc: r16 = "Imsak"
    //     0xb0e6dc: add             x16, PP, #8, lsl #12  ; [pp+0x86c0] "Imsak"
    //     0xb0e6e0: ldr             x16, [x16, #0x6c0]
    // 0xb0e6e4: StoreField: r4->field_f = r16
    //     0xb0e6e4: stur            w16, [x4, #0xf]
    // 0xb0e6e8: LoadField: r1 = r3->field_b
    //     0xb0e6e8: ldur            w1, [x3, #0xb]
    // 0xb0e6ec: r3 = LoadInt32Instr(r1)
    //     0xb0e6ec: sbfx            x3, x1, #1, #0x1f
    // 0xb0e6f0: cmp             x2, x3
    // 0xb0e6f4: b.ne            #0xb0e700
    // 0xb0e6f8: mov             x1, x0
    // 0xb0e6fc: r0 = _growToNextCapacity()
    //     0xb0e6fc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e700: ldur            x1, [fp, #-0x50]
    // 0xb0e704: ldur            x0, [fp, #-0x58]
    // 0xb0e708: add             x2, x1, #1
    // 0xb0e70c: stur            x2, [fp, #-0x30]
    // 0xb0e710: lsl             x3, x2, #1
    // 0xb0e714: StoreField: r0->field_b = r3
    //     0xb0e714: stur            w3, [x0, #0xb]
    // 0xb0e718: LoadField: r3 = r0->field_f
    //     0xb0e718: ldur            w3, [x0, #0xf]
    // 0xb0e71c: DecompressPointer r3
    //     0xb0e71c: add             x3, x3, HEAP, lsl #32
    // 0xb0e720: add             x4, x3, x1, lsl #2
    // 0xb0e724: r16 = "Subuh"
    //     0xb0e724: add             x16, PP, #8, lsl #12  ; [pp+0x8688] "Subuh"
    //     0xb0e728: ldr             x16, [x16, #0x688]
    // 0xb0e72c: StoreField: r4->field_f = r16
    //     0xb0e72c: stur            w16, [x4, #0xf]
    // 0xb0e730: LoadField: r1 = r3->field_b
    //     0xb0e730: ldur            w1, [x3, #0xb]
    // 0xb0e734: r3 = LoadInt32Instr(r1)
    //     0xb0e734: sbfx            x3, x1, #1, #0x1f
    // 0xb0e738: cmp             x2, x3
    // 0xb0e73c: b.ne            #0xb0e748
    // 0xb0e740: mov             x1, x0
    // 0xb0e744: r0 = _growToNextCapacity()
    //     0xb0e744: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e748: ldur            x1, [fp, #-0x30]
    // 0xb0e74c: ldur            x0, [fp, #-0x58]
    // 0xb0e750: add             x2, x1, #1
    // 0xb0e754: stur            x2, [fp, #-0x50]
    // 0xb0e758: lsl             x3, x2, #1
    // 0xb0e75c: StoreField: r0->field_b = r3
    //     0xb0e75c: stur            w3, [x0, #0xb]
    // 0xb0e760: LoadField: r3 = r0->field_f
    //     0xb0e760: ldur            w3, [x0, #0xf]
    // 0xb0e764: DecompressPointer r3
    //     0xb0e764: add             x3, x3, HEAP, lsl #32
    // 0xb0e768: add             x4, x3, x1, lsl #2
    // 0xb0e76c: r16 = "Terbit"
    //     0xb0e76c: add             x16, PP, #8, lsl #12  ; [pp+0x8690] "Terbit"
    //     0xb0e770: ldr             x16, [x16, #0x690]
    // 0xb0e774: StoreField: r4->field_f = r16
    //     0xb0e774: stur            w16, [x4, #0xf]
    // 0xb0e778: LoadField: r1 = r3->field_b
    //     0xb0e778: ldur            w1, [x3, #0xb]
    // 0xb0e77c: r3 = LoadInt32Instr(r1)
    //     0xb0e77c: sbfx            x3, x1, #1, #0x1f
    // 0xb0e780: cmp             x2, x3
    // 0xb0e784: b.ne            #0xb0e790
    // 0xb0e788: mov             x1, x0
    // 0xb0e78c: r0 = _growToNextCapacity()
    //     0xb0e78c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e790: ldur            x1, [fp, #-0x50]
    // 0xb0e794: ldur            x0, [fp, #-0x58]
    // 0xb0e798: add             x2, x1, #1
    // 0xb0e79c: stur            x2, [fp, #-0x30]
    // 0xb0e7a0: lsl             x3, x2, #1
    // 0xb0e7a4: StoreField: r0->field_b = r3
    //     0xb0e7a4: stur            w3, [x0, #0xb]
    // 0xb0e7a8: LoadField: r3 = r0->field_f
    //     0xb0e7a8: ldur            w3, [x0, #0xf]
    // 0xb0e7ac: DecompressPointer r3
    //     0xb0e7ac: add             x3, x3, HEAP, lsl #32
    // 0xb0e7b0: add             x4, x3, x1, lsl #2
    // 0xb0e7b4: r16 = "Dhuha"
    //     0xb0e7b4: add             x16, PP, #8, lsl #12  ; [pp+0x8698] "Dhuha"
    //     0xb0e7b8: ldr             x16, [x16, #0x698]
    // 0xb0e7bc: StoreField: r4->field_f = r16
    //     0xb0e7bc: stur            w16, [x4, #0xf]
    // 0xb0e7c0: LoadField: r1 = r3->field_b
    //     0xb0e7c0: ldur            w1, [x3, #0xb]
    // 0xb0e7c4: r3 = LoadInt32Instr(r1)
    //     0xb0e7c4: sbfx            x3, x1, #1, #0x1f
    // 0xb0e7c8: cmp             x2, x3
    // 0xb0e7cc: b.ne            #0xb0e7d8
    // 0xb0e7d0: mov             x1, x0
    // 0xb0e7d4: r0 = _growToNextCapacity()
    //     0xb0e7d4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e7d8: ldur            x1, [fp, #-0x30]
    // 0xb0e7dc: ldur            x0, [fp, #-0x58]
    // 0xb0e7e0: add             x2, x1, #1
    // 0xb0e7e4: stur            x2, [fp, #-0x50]
    // 0xb0e7e8: lsl             x3, x2, #1
    // 0xb0e7ec: StoreField: r0->field_b = r3
    //     0xb0e7ec: stur            w3, [x0, #0xb]
    // 0xb0e7f0: LoadField: r3 = r0->field_f
    //     0xb0e7f0: ldur            w3, [x0, #0xf]
    // 0xb0e7f4: DecompressPointer r3
    //     0xb0e7f4: add             x3, x3, HEAP, lsl #32
    // 0xb0e7f8: add             x4, x3, x1, lsl #2
    // 0xb0e7fc: r16 = "Zuhur"
    //     0xb0e7fc: add             x16, PP, #8, lsl #12  ; [pp+0x86a0] "Zuhur"
    //     0xb0e800: ldr             x16, [x16, #0x6a0]
    // 0xb0e804: StoreField: r4->field_f = r16
    //     0xb0e804: stur            w16, [x4, #0xf]
    // 0xb0e808: LoadField: r1 = r3->field_b
    //     0xb0e808: ldur            w1, [x3, #0xb]
    // 0xb0e80c: r3 = LoadInt32Instr(r1)
    //     0xb0e80c: sbfx            x3, x1, #1, #0x1f
    // 0xb0e810: cmp             x2, x3
    // 0xb0e814: b.ne            #0xb0e820
    // 0xb0e818: mov             x1, x0
    // 0xb0e81c: r0 = _growToNextCapacity()
    //     0xb0e81c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e820: ldur            x1, [fp, #-0x50]
    // 0xb0e824: ldur            x0, [fp, #-0x58]
    // 0xb0e828: add             x2, x1, #1
    // 0xb0e82c: stur            x2, [fp, #-0x30]
    // 0xb0e830: lsl             x3, x2, #1
    // 0xb0e834: StoreField: r0->field_b = r3
    //     0xb0e834: stur            w3, [x0, #0xb]
    // 0xb0e838: LoadField: r3 = r0->field_f
    //     0xb0e838: ldur            w3, [x0, #0xf]
    // 0xb0e83c: DecompressPointer r3
    //     0xb0e83c: add             x3, x3, HEAP, lsl #32
    // 0xb0e840: add             x4, x3, x1, lsl #2
    // 0xb0e844: r16 = "Ashar"
    //     0xb0e844: add             x16, PP, #8, lsl #12  ; [pp+0x86a8] "Ashar"
    //     0xb0e848: ldr             x16, [x16, #0x6a8]
    // 0xb0e84c: StoreField: r4->field_f = r16
    //     0xb0e84c: stur            w16, [x4, #0xf]
    // 0xb0e850: LoadField: r1 = r3->field_b
    //     0xb0e850: ldur            w1, [x3, #0xb]
    // 0xb0e854: r3 = LoadInt32Instr(r1)
    //     0xb0e854: sbfx            x3, x1, #1, #0x1f
    // 0xb0e858: cmp             x2, x3
    // 0xb0e85c: b.ne            #0xb0e868
    // 0xb0e860: mov             x1, x0
    // 0xb0e864: r0 = _growToNextCapacity()
    //     0xb0e864: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e868: ldur            x1, [fp, #-0x30]
    // 0xb0e86c: ldur            x0, [fp, #-0x58]
    // 0xb0e870: add             x2, x1, #1
    // 0xb0e874: stur            x2, [fp, #-0x50]
    // 0xb0e878: lsl             x3, x2, #1
    // 0xb0e87c: StoreField: r0->field_b = r3
    //     0xb0e87c: stur            w3, [x0, #0xb]
    // 0xb0e880: LoadField: r3 = r0->field_f
    //     0xb0e880: ldur            w3, [x0, #0xf]
    // 0xb0e884: DecompressPointer r3
    //     0xb0e884: add             x3, x3, HEAP, lsl #32
    // 0xb0e888: add             x4, x3, x1, lsl #2
    // 0xb0e88c: r16 = "Maghrib"
    //     0xb0e88c: add             x16, PP, #8, lsl #12  ; [pp+0x86b0] "Maghrib"
    //     0xb0e890: ldr             x16, [x16, #0x6b0]
    // 0xb0e894: StoreField: r4->field_f = r16
    //     0xb0e894: stur            w16, [x4, #0xf]
    // 0xb0e898: LoadField: r1 = r3->field_b
    //     0xb0e898: ldur            w1, [x3, #0xb]
    // 0xb0e89c: r3 = LoadInt32Instr(r1)
    //     0xb0e89c: sbfx            x3, x1, #1, #0x1f
    // 0xb0e8a0: cmp             x2, x3
    // 0xb0e8a4: b.ne            #0xb0e8b0
    // 0xb0e8a8: mov             x1, x0
    // 0xb0e8ac: r0 = _growToNextCapacity()
    //     0xb0e8ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0e8b0: ldur            x4, [fp, #-0x10]
    // 0xb0e8b4: ldur            x3, [fp, #-0x18]
    // 0xb0e8b8: ldur            x5, [fp, #-0x28]
    // 0xb0e8bc: ldur            x1, [fp, #-0x50]
    // 0xb0e8c0: ldur            x0, [fp, #-0x58]
    // 0xb0e8c4: add             x2, x1, #1
    // 0xb0e8c8: lsl             x6, x2, #1
    // 0xb0e8cc: StoreField: r0->field_b = r6
    //     0xb0e8cc: stur            w6, [x0, #0xb]
    // 0xb0e8d0: LoadField: r2 = r0->field_f
    //     0xb0e8d0: ldur            w2, [x0, #0xf]
    // 0xb0e8d4: DecompressPointer r2
    //     0xb0e8d4: add             x2, x2, HEAP, lsl #32
    // 0xb0e8d8: add             x6, x2, x1, lsl #2
    // 0xb0e8dc: r16 = "Isya`"
    //     0xb0e8dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e180] "Isya`"
    //     0xb0e8e0: ldr             x16, [x16, #0x180]
    // 0xb0e8e4: StoreField: r6->field_f = r16
    //     0xb0e8e4: stur            w16, [x6, #0xf]
    // 0xb0e8e8: StoreField: r3->field_1f = r0
    //     0xb0e8e8: stur            w0, [x3, #0x1f]
    //     0xb0e8ec: ldurb           w16, [x3, #-1]
    //     0xb0e8f0: ldurb           w17, [x0, #-1]
    //     0xb0e8f4: and             x16, x17, x16, lsr #2
    //     0xb0e8f8: tst             x16, HEAP, lsr #32
    //     0xb0e8fc: b.eq            #0xb0e904
    //     0xb0e900: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb0e904: r1 = <List<String>>
    //     0xb0e904: add             x1, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0xb0e908: ldr             x1, [x1, #0xb40]
    // 0xb0e90c: r2 = 0
    //     0xb0e90c: movz            x2, #0
    // 0xb0e910: r0 = _GrowableList()
    //     0xb0e910: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb0e914: mov             x3, x0
    // 0xb0e918: ldur            x2, [fp, #-0x18]
    // 0xb0e91c: stur            x3, [fp, #-0x60]
    // 0xb0e920: StoreField: r2->field_23 = r0
    //     0xb0e920: stur            w0, [x2, #0x23]
    //     0xb0e924: ldurb           w16, [x2, #-1]
    //     0xb0e928: ldurb           w17, [x0, #-1]
    //     0xb0e92c: and             x16, x17, x16, lsr #2
    //     0xb0e930: tst             x16, HEAP, lsr #32
    //     0xb0e934: b.eq            #0xb0e93c
    //     0xb0e938: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb0e93c: ldur            x4, [fp, #-0x28]
    // 0xb0e940: LoadField: r5 = r4->field_7
    //     0xb0e940: ldur            w5, [x4, #7]
    // 0xb0e944: DecompressPointer r5
    //     0xb0e944: add             x5, x5, HEAP, lsl #32
    // 0xb0e948: stur            x5, [fp, #-0x58]
    // 0xb0e94c: LoadField: r0 = r4->field_b
    //     0xb0e94c: ldur            w0, [x4, #0xb]
    // 0xb0e950: r6 = LoadInt32Instr(r0)
    //     0xb0e950: sbfx            x6, x0, #1, #0x1f
    // 0xb0e954: ldur            x7, [fp, #-0x10]
    // 0xb0e958: stur            x6, [fp, #-0x50]
    // 0xb0e95c: LoadField: r0 = r7->field_f
    //     0xb0e95c: ldur            w0, [x7, #0xf]
    // 0xb0e960: DecompressPointer r0
    //     0xb0e960: add             x0, x0, HEAP, lsl #32
    // 0xb0e964: ArrayLoad: r8 = r0[0]  ; List_4
    //     0xb0e964: ldur            w8, [x0, #0x17]
    // 0xb0e968: DecompressPointer r8
    //     0xb0e968: add             x8, x8, HEAP, lsl #32
    // 0xb0e96c: stur            x8, [fp, #-0x48]
    // 0xb0e970: r0 = 0
    //     0xb0e970: movz            x0, #0
    // 0xb0e974: ldur            x9, [fp, #-0x38]
    // 0xb0e978: CheckStackOverflow
    //     0xb0e978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0e97c: cmp             SP, x16
    //     0xb0e980: b.ls            #0xb0f464
    // 0xb0e984: LoadField: r1 = r4->field_b
    //     0xb0e984: ldur            w1, [x4, #0xb]
    // 0xb0e988: r10 = LoadInt32Instr(r1)
    //     0xb0e988: sbfx            x10, x1, #1, #0x1f
    // 0xb0e98c: cmp             x6, x10
    // 0xb0e990: b.ne            #0xb0f434
    // 0xb0e994: cmp             x0, x10
    // 0xb0e998: b.ge            #0xb0f354
    // 0xb0e99c: LoadField: r1 = r4->field_f
    //     0xb0e99c: ldur            w1, [x4, #0xf]
    // 0xb0e9a0: DecompressPointer r1
    //     0xb0e9a0: add             x1, x1, HEAP, lsl #32
    // 0xb0e9a4: ArrayLoad: r10 = r1[r0]  ; Unknown_4
    //     0xb0e9a4: add             x16, x1, x0, lsl #2
    //     0xb0e9a8: ldur            w10, [x16, #0xf]
    // 0xb0e9ac: DecompressPointer r10
    //     0xb0e9ac: add             x10, x10, HEAP, lsl #32
    // 0xb0e9b0: stur            x10, [fp, #-0x20]
    // 0xb0e9b4: add             x11, x0, #1
    // 0xb0e9b8: stur            x11, [fp, #-0x30]
    // 0xb0e9bc: r0 = LoadClassIdInstr(r10)
    //     0xb0e9bc: ldur            x0, [x10, #-1]
    //     0xb0e9c0: ubfx            x0, x0, #0xc, #0x14
    // 0xb0e9c4: mov             x1, x10
    // 0xb0e9c8: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xb0e9c8: sub             lr, x0, #0xfdf
    //     0xb0e9cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb0e9d0: blr             lr
    // 0xb0e9d4: mov             x2, x0
    // 0xb0e9d8: r0 = BoxInt64Instr(r2)
    //     0xb0e9d8: sbfiz           x0, x2, #1, #0x1f
    //     0xb0e9dc: cmp             x2, x0, asr #1
    //     0xb0e9e0: b.eq            #0xb0e9ec
    //     0xb0e9e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0e9e8: stur            x2, [x0, #7]
    // 0xb0e9ec: r1 = 60
    //     0xb0e9ec: movz            x1, #0x3c
    // 0xb0e9f0: branchIfSmi(r0, 0xb0e9fc)
    //     0xb0e9f0: tbz             w0, #0, #0xb0e9fc
    // 0xb0e9f4: r1 = LoadClassIdInstr(r0)
    //     0xb0e9f4: ldur            x1, [x0, #-1]
    //     0xb0e9f8: ubfx            x1, x1, #0xc, #0x14
    // 0xb0e9fc: str             x0, [SP]
    // 0xb0ea00: mov             x0, x1
    // 0xb0ea04: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb0ea04: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb0ea08: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb0ea08: movz            x17, #0x2b03
    //     0xb0ea0c: add             lr, x0, x17
    //     0xb0ea10: ldr             lr, [x21, lr, lsl #3]
    //     0xb0ea14: blr             lr
    // 0xb0ea18: stur            x0, [fp, #-0x68]
    // 0xb0ea1c: r0 = find()
    //     0xb0ea1c: bl              #0x8179d0  ; [package:nuonline/services/hijri_service.dart] HijriService::find
    // 0xb0ea20: mov             x1, x0
    // 0xb0ea24: ldur            x2, [fp, #-0x20]
    // 0xb0ea28: r0 = from()
    //     0xb0ea28: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0xb0ea2c: LoadField: r2 = r0->field_7
    //     0xb0ea2c: ldur            x2, [x0, #7]
    // 0xb0ea30: r0 = BoxInt64Instr(r2)
    //     0xb0ea30: sbfiz           x0, x2, #1, #0x1f
    //     0xb0ea34: cmp             x2, x0, asr #1
    //     0xb0ea38: b.eq            #0xb0ea44
    //     0xb0ea3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0ea40: stur            x2, [x0, #7]
    // 0xb0ea44: r1 = 60
    //     0xb0ea44: movz            x1, #0x3c
    // 0xb0ea48: branchIfSmi(r0, 0xb0ea54)
    //     0xb0ea48: tbz             w0, #0, #0xb0ea54
    // 0xb0ea4c: r1 = LoadClassIdInstr(r0)
    //     0xb0ea4c: ldur            x1, [x0, #-1]
    //     0xb0ea50: ubfx            x1, x1, #0xc, #0x14
    // 0xb0ea54: str             x0, [SP]
    // 0xb0ea58: mov             x0, x1
    // 0xb0ea5c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb0ea5c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb0ea60: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb0ea60: movz            x17, #0x2b03
    //     0xb0ea64: add             lr, x0, x17
    //     0xb0ea68: ldr             lr, [x21, lr, lsl #3]
    //     0xb0ea6c: blr             lr
    // 0xb0ea70: r1 = Null
    //     0xb0ea70: mov             x1, NULL
    // 0xb0ea74: r2 = 4
    //     0xb0ea74: movz            x2, #0x4
    // 0xb0ea78: stur            x0, [fp, #-0x70]
    // 0xb0ea7c: r0 = AllocateArray()
    //     0xb0ea7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0ea80: mov             x2, x0
    // 0xb0ea84: ldur            x0, [fp, #-0x68]
    // 0xb0ea88: stur            x2, [fp, #-0x78]
    // 0xb0ea8c: StoreField: r2->field_f = r0
    //     0xb0ea8c: stur            w0, [x2, #0xf]
    // 0xb0ea90: ldur            x0, [fp, #-0x70]
    // 0xb0ea94: StoreField: r2->field_13 = r0
    //     0xb0ea94: stur            w0, [x2, #0x13]
    // 0xb0ea98: r1 = <String>
    //     0xb0ea98: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0ea9c: r0 = AllocateGrowableArray()
    //     0xb0ea9c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0eaa0: mov             x1, x0
    // 0xb0eaa4: ldur            x0, [fp, #-0x78]
    // 0xb0eaa8: stur            x1, [fp, #-0x68]
    // 0xb0eaac: StoreField: r1->field_f = r0
    //     0xb0eaac: stur            w0, [x1, #0xf]
    // 0xb0eab0: r0 = 4
    //     0xb0eab0: movz            x0, #0x4
    // 0xb0eab4: StoreField: r1->field_b = r0
    //     0xb0eab4: stur            w0, [x1, #0xb]
    // 0xb0eab8: ldur            x2, [fp, #-0x38]
    // 0xb0eabc: tbnz            w2, #4, #0xb0ead0
    // 0xb0eac0: r16 = <String>
    //     0xb0eac0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0eac4: stp             x1, x16, [SP]
    // 0xb0eac8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0eac8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0eacc: r0 = ListExtensions.swap()
    //     0xb0eacc: bl              #0xb11310  ; [package:collection/src/list_extensions.dart] ::ListExtensions.swap
    // 0xb0ead0: ldur            x1, [fp, #-0x48]
    // 0xb0ead4: ldur            x2, [fp, #-0x20]
    // 0xb0ead8: r0 = calculatePrayerTime()
    //     0xb0ead8: bl              #0xb11258  ; [package:nuonline/app/modules/prayer_time/controllers/prayer_time_download_controller.dart] PrayerTimeDownloadController::calculatePrayerTime
    // 0xb0eadc: mov             x2, x0
    // 0xb0eae0: r1 = <PrayerTimes>
    //     0xb0eae0: add             x1, PP, #0xb, lsl #12  ; [pp+0xb640] TypeArguments: <PrayerTimes>
    //     0xb0eae4: ldr             x1, [x1, #0x640]
    // 0xb0eae8: stur            x2, [fp, #-0x70]
    // 0xb0eaec: r0 = AwaitWithTypeCheck()
    //     0xb0eaec: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xb0eaf0: r1 = "id_ID"
    //     0xb0eaf0: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xb0eaf4: ldr             x1, [x1, #0x200]
    // 0xb0eaf8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0eaf8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0eafc: ldr             x2, [x2, #0xad0]
    // 0xb0eb00: stur            x0, [fp, #-0x70]
    // 0xb0eb04: r0 = verifiedLocale()
    //     0xb0eb04: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0eb08: stur            x0, [fp, #-0x78]
    // 0xb0eb0c: r0 = DateFormat()
    //     0xb0eb0c: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0eb10: mov             x3, x0
    // 0xb0eb14: ldur            x0, [fp, #-0x78]
    // 0xb0eb18: stur            x3, [fp, #-0x80]
    // 0xb0eb1c: StoreField: r3->field_7 = r0
    //     0xb0eb1c: stur            w0, [x3, #7]
    // 0xb0eb20: mov             x1, x3
    // 0xb0eb24: r2 = "EEEE"
    //     0xb0eb24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e188] "EEEE"
    //     0xb0eb28: ldr             x2, [x2, #0x188]
    // 0xb0eb2c: r0 = addPattern()
    //     0xb0eb2c: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0eb30: ldur            x1, [fp, #-0x80]
    // 0xb0eb34: ldur            x2, [fp, #-0x20]
    // 0xb0eb38: r0 = format()
    //     0xb0eb38: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0eb3c: mov             x1, x0
    // 0xb0eb40: r2 = "Minggu"
    //     0xb0eb40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e190] "Minggu"
    //     0xb0eb44: ldr             x2, [x2, #0x190]
    // 0xb0eb48: r3 = "Ahad"
    //     0xb0eb48: add             x3, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xb0eb4c: ldr             x3, [x3, #0x128]
    // 0xb0eb50: r0 = replaceAll()
    //     0xb0eb50: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb0eb54: r1 = Null
    //     0xb0eb54: mov             x1, NULL
    // 0xb0eb58: r2 = 2
    //     0xb0eb58: movz            x2, #0x2
    // 0xb0eb5c: stur            x0, [fp, #-0x20]
    // 0xb0eb60: r0 = AllocateArray()
    //     0xb0eb60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0eb64: mov             x2, x0
    // 0xb0eb68: ldur            x0, [fp, #-0x20]
    // 0xb0eb6c: stur            x2, [fp, #-0x78]
    // 0xb0eb70: StoreField: r2->field_f = r0
    //     0xb0eb70: stur            w0, [x2, #0xf]
    // 0xb0eb74: r1 = <String>
    //     0xb0eb74: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0eb78: r0 = AllocateGrowableArray()
    //     0xb0eb78: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0eb7c: mov             x3, x0
    // 0xb0eb80: ldur            x0, [fp, #-0x78]
    // 0xb0eb84: stur            x3, [fp, #-0x20]
    // 0xb0eb88: StoreField: r3->field_f = r0
    //     0xb0eb88: stur            w0, [x3, #0xf]
    // 0xb0eb8c: r0 = 2
    //     0xb0eb8c: movz            x0, #0x2
    // 0xb0eb90: StoreField: r3->field_b = r0
    //     0xb0eb90: stur            w0, [x3, #0xb]
    // 0xb0eb94: mov             x1, x3
    // 0xb0eb98: ldur            x2, [fp, #-0x68]
    // 0xb0eb9c: r0 = addAll()
    //     0xb0eb9c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb0eba0: ldur            x0, [fp, #-0x70]
    // 0xb0eba4: LoadField: r1 = r0->field_7
    //     0xb0eba4: ldur            w1, [x0, #7]
    // 0xb0eba8: DecompressPointer r1
    //     0xb0eba8: add             x1, x1, HEAP, lsl #32
    // 0xb0ebac: r16 = Sentinel
    //     0xb0ebac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0ebb0: cmp             w1, w16
    // 0xb0ebb4: b.eq            #0xb0f46c
    // 0xb0ebb8: stur            x1, [fp, #-0x68]
    // 0xb0ebbc: r0 = Duration()
    //     0xb0ebbc: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xb0ebc0: mov             x1, x0
    // 0xb0ebc4: r0 = -599982081
    //     0xb0ebc4: movn            x0, #0x23c3, lsl #16
    // 0xb0ebc8: movk            x0, #0xba00
    // 0xb0ebcc: StoreField: r1->field_7 = r0
    //     0xb0ebcc: stur            x0, [x1, #7]
    // 0xb0ebd0: mov             x2, x1
    // 0xb0ebd4: ldur            x1, [fp, #-0x68]
    // 0xb0ebd8: r0 = add()
    //     0xb0ebd8: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0xb0ebdc: r1 = Null
    //     0xb0ebdc: mov             x1, NULL
    // 0xb0ebe0: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0ebe0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0ebe4: ldr             x2, [x2, #0xad0]
    // 0xb0ebe8: stur            x0, [fp, #-0x68]
    // 0xb0ebec: r0 = verifiedLocale()
    //     0xb0ebec: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0ebf0: stur            x0, [fp, #-0x78]
    // 0xb0ebf4: r0 = DateFormat()
    //     0xb0ebf4: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0ebf8: mov             x3, x0
    // 0xb0ebfc: ldur            x0, [fp, #-0x78]
    // 0xb0ec00: stur            x3, [fp, #-0x80]
    // 0xb0ec04: StoreField: r3->field_7 = r0
    //     0xb0ec04: stur            w0, [x3, #7]
    // 0xb0ec08: mov             x1, x3
    // 0xb0ec0c: r2 = "Hm"
    //     0xb0ec0c: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0ec10: ldr             x2, [x2, #0xad8]
    // 0xb0ec14: r0 = addPattern()
    //     0xb0ec14: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0ec18: ldur            x1, [fp, #-0x80]
    // 0xb0ec1c: ldur            x2, [fp, #-0x68]
    // 0xb0ec20: r0 = format()
    //     0xb0ec20: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0ec24: mov             x2, x0
    // 0xb0ec28: ldur            x0, [fp, #-0x20]
    // 0xb0ec2c: stur            x2, [fp, #-0x68]
    // 0xb0ec30: LoadField: r1 = r0->field_b
    //     0xb0ec30: ldur            w1, [x0, #0xb]
    // 0xb0ec34: LoadField: r3 = r0->field_f
    //     0xb0ec34: ldur            w3, [x0, #0xf]
    // 0xb0ec38: DecompressPointer r3
    //     0xb0ec38: add             x3, x3, HEAP, lsl #32
    // 0xb0ec3c: LoadField: r4 = r3->field_b
    //     0xb0ec3c: ldur            w4, [x3, #0xb]
    // 0xb0ec40: r3 = LoadInt32Instr(r1)
    //     0xb0ec40: sbfx            x3, x1, #1, #0x1f
    // 0xb0ec44: stur            x3, [fp, #-0x88]
    // 0xb0ec48: r1 = LoadInt32Instr(r4)
    //     0xb0ec48: sbfx            x1, x4, #1, #0x1f
    // 0xb0ec4c: cmp             x3, x1
    // 0xb0ec50: b.ne            #0xb0ec5c
    // 0xb0ec54: mov             x1, x0
    // 0xb0ec58: r0 = _growToNextCapacity()
    //     0xb0ec58: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0ec5c: ldur            x4, [fp, #-0x70]
    // 0xb0ec60: ldur            x3, [fp, #-0x20]
    // 0xb0ec64: ldur            x2, [fp, #-0x88]
    // 0xb0ec68: add             x0, x2, #1
    // 0xb0ec6c: lsl             x1, x0, #1
    // 0xb0ec70: StoreField: r3->field_b = r1
    //     0xb0ec70: stur            w1, [x3, #0xb]
    // 0xb0ec74: LoadField: r1 = r3->field_f
    //     0xb0ec74: ldur            w1, [x3, #0xf]
    // 0xb0ec78: DecompressPointer r1
    //     0xb0ec78: add             x1, x1, HEAP, lsl #32
    // 0xb0ec7c: ldur            x0, [fp, #-0x68]
    // 0xb0ec80: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0ec80: add             x25, x1, x2, lsl #2
    //     0xb0ec84: add             x25, x25, #0xf
    //     0xb0ec88: str             w0, [x25]
    //     0xb0ec8c: tbz             w0, #0, #0xb0eca8
    //     0xb0ec90: ldurb           w16, [x1, #-1]
    //     0xb0ec94: ldurb           w17, [x0, #-1]
    //     0xb0ec98: and             x16, x17, x16, lsr #2
    //     0xb0ec9c: tst             x16, HEAP, lsr #32
    //     0xb0eca0: b.eq            #0xb0eca8
    //     0xb0eca4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0eca8: LoadField: r0 = r4->field_7
    //     0xb0eca8: ldur            w0, [x4, #7]
    // 0xb0ecac: DecompressPointer r0
    //     0xb0ecac: add             x0, x0, HEAP, lsl #32
    // 0xb0ecb0: stur            x0, [fp, #-0x68]
    // 0xb0ecb4: r1 = Null
    //     0xb0ecb4: mov             x1, NULL
    // 0xb0ecb8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0ecb8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0ecbc: ldr             x2, [x2, #0xad0]
    // 0xb0ecc0: r0 = verifiedLocale()
    //     0xb0ecc0: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0ecc4: stur            x0, [fp, #-0x78]
    // 0xb0ecc8: r0 = DateFormat()
    //     0xb0ecc8: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0eccc: mov             x3, x0
    // 0xb0ecd0: ldur            x0, [fp, #-0x78]
    // 0xb0ecd4: stur            x3, [fp, #-0x80]
    // 0xb0ecd8: StoreField: r3->field_7 = r0
    //     0xb0ecd8: stur            w0, [x3, #7]
    // 0xb0ecdc: mov             x1, x3
    // 0xb0ece0: r2 = "Hm"
    //     0xb0ece0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0ece4: ldr             x2, [x2, #0xad8]
    // 0xb0ece8: r0 = addPattern()
    //     0xb0ece8: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0ecec: ldur            x1, [fp, #-0x80]
    // 0xb0ecf0: ldur            x2, [fp, #-0x68]
    // 0xb0ecf4: r0 = format()
    //     0xb0ecf4: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0ecf8: mov             x2, x0
    // 0xb0ecfc: ldur            x0, [fp, #-0x20]
    // 0xb0ed00: stur            x2, [fp, #-0x68]
    // 0xb0ed04: LoadField: r1 = r0->field_b
    //     0xb0ed04: ldur            w1, [x0, #0xb]
    // 0xb0ed08: LoadField: r3 = r0->field_f
    //     0xb0ed08: ldur            w3, [x0, #0xf]
    // 0xb0ed0c: DecompressPointer r3
    //     0xb0ed0c: add             x3, x3, HEAP, lsl #32
    // 0xb0ed10: LoadField: r4 = r3->field_b
    //     0xb0ed10: ldur            w4, [x3, #0xb]
    // 0xb0ed14: r3 = LoadInt32Instr(r1)
    //     0xb0ed14: sbfx            x3, x1, #1, #0x1f
    // 0xb0ed18: stur            x3, [fp, #-0x88]
    // 0xb0ed1c: r1 = LoadInt32Instr(r4)
    //     0xb0ed1c: sbfx            x1, x4, #1, #0x1f
    // 0xb0ed20: cmp             x3, x1
    // 0xb0ed24: b.ne            #0xb0ed30
    // 0xb0ed28: mov             x1, x0
    // 0xb0ed2c: r0 = _growToNextCapacity()
    //     0xb0ed2c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0ed30: ldur            x4, [fp, #-0x70]
    // 0xb0ed34: ldur            x3, [fp, #-0x20]
    // 0xb0ed38: ldur            x2, [fp, #-0x88]
    // 0xb0ed3c: add             x0, x2, #1
    // 0xb0ed40: lsl             x1, x0, #1
    // 0xb0ed44: StoreField: r3->field_b = r1
    //     0xb0ed44: stur            w1, [x3, #0xb]
    // 0xb0ed48: LoadField: r1 = r3->field_f
    //     0xb0ed48: ldur            w1, [x3, #0xf]
    // 0xb0ed4c: DecompressPointer r1
    //     0xb0ed4c: add             x1, x1, HEAP, lsl #32
    // 0xb0ed50: ldur            x0, [fp, #-0x68]
    // 0xb0ed54: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0ed54: add             x25, x1, x2, lsl #2
    //     0xb0ed58: add             x25, x25, #0xf
    //     0xb0ed5c: str             w0, [x25]
    //     0xb0ed60: tbz             w0, #0, #0xb0ed7c
    //     0xb0ed64: ldurb           w16, [x1, #-1]
    //     0xb0ed68: ldurb           w17, [x0, #-1]
    //     0xb0ed6c: and             x16, x17, x16, lsr #2
    //     0xb0ed70: tst             x16, HEAP, lsr #32
    //     0xb0ed74: b.eq            #0xb0ed7c
    //     0xb0ed78: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0ed7c: LoadField: r0 = r4->field_b
    //     0xb0ed7c: ldur            w0, [x4, #0xb]
    // 0xb0ed80: DecompressPointer r0
    //     0xb0ed80: add             x0, x0, HEAP, lsl #32
    // 0xb0ed84: r16 = Sentinel
    //     0xb0ed84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0ed88: cmp             w0, w16
    // 0xb0ed8c: b.eq            #0xb0f478
    // 0xb0ed90: stur            x0, [fp, #-0x68]
    // 0xb0ed94: r1 = Null
    //     0xb0ed94: mov             x1, NULL
    // 0xb0ed98: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0ed98: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0ed9c: ldr             x2, [x2, #0xad0]
    // 0xb0eda0: r0 = verifiedLocale()
    //     0xb0eda0: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0eda4: stur            x0, [fp, #-0x78]
    // 0xb0eda8: r0 = DateFormat()
    //     0xb0eda8: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0edac: mov             x3, x0
    // 0xb0edb0: ldur            x0, [fp, #-0x78]
    // 0xb0edb4: stur            x3, [fp, #-0x80]
    // 0xb0edb8: StoreField: r3->field_7 = r0
    //     0xb0edb8: stur            w0, [x3, #7]
    // 0xb0edbc: mov             x1, x3
    // 0xb0edc0: r2 = "Hm"
    //     0xb0edc0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0edc4: ldr             x2, [x2, #0xad8]
    // 0xb0edc8: r0 = addPattern()
    //     0xb0edc8: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0edcc: ldur            x1, [fp, #-0x80]
    // 0xb0edd0: ldur            x2, [fp, #-0x68]
    // 0xb0edd4: r0 = format()
    //     0xb0edd4: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0edd8: mov             x2, x0
    // 0xb0eddc: ldur            x0, [fp, #-0x20]
    // 0xb0ede0: stur            x2, [fp, #-0x68]
    // 0xb0ede4: LoadField: r1 = r0->field_b
    //     0xb0ede4: ldur            w1, [x0, #0xb]
    // 0xb0ede8: LoadField: r3 = r0->field_f
    //     0xb0ede8: ldur            w3, [x0, #0xf]
    // 0xb0edec: DecompressPointer r3
    //     0xb0edec: add             x3, x3, HEAP, lsl #32
    // 0xb0edf0: LoadField: r4 = r3->field_b
    //     0xb0edf0: ldur            w4, [x3, #0xb]
    // 0xb0edf4: r3 = LoadInt32Instr(r1)
    //     0xb0edf4: sbfx            x3, x1, #1, #0x1f
    // 0xb0edf8: stur            x3, [fp, #-0x88]
    // 0xb0edfc: r1 = LoadInt32Instr(r4)
    //     0xb0edfc: sbfx            x1, x4, #1, #0x1f
    // 0xb0ee00: cmp             x3, x1
    // 0xb0ee04: b.ne            #0xb0ee10
    // 0xb0ee08: mov             x1, x0
    // 0xb0ee0c: r0 = _growToNextCapacity()
    //     0xb0ee0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0ee10: ldur            x4, [fp, #-0x70]
    // 0xb0ee14: ldur            x3, [fp, #-0x20]
    // 0xb0ee18: ldur            x2, [fp, #-0x88]
    // 0xb0ee1c: add             x0, x2, #1
    // 0xb0ee20: lsl             x1, x0, #1
    // 0xb0ee24: StoreField: r3->field_b = r1
    //     0xb0ee24: stur            w1, [x3, #0xb]
    // 0xb0ee28: LoadField: r1 = r3->field_f
    //     0xb0ee28: ldur            w1, [x3, #0xf]
    // 0xb0ee2c: DecompressPointer r1
    //     0xb0ee2c: add             x1, x1, HEAP, lsl #32
    // 0xb0ee30: ldur            x0, [fp, #-0x68]
    // 0xb0ee34: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0ee34: add             x25, x1, x2, lsl #2
    //     0xb0ee38: add             x25, x25, #0xf
    //     0xb0ee3c: str             w0, [x25]
    //     0xb0ee40: tbz             w0, #0, #0xb0ee5c
    //     0xb0ee44: ldurb           w16, [x1, #-1]
    //     0xb0ee48: ldurb           w17, [x0, #-1]
    //     0xb0ee4c: and             x16, x17, x16, lsr #2
    //     0xb0ee50: tst             x16, HEAP, lsr #32
    //     0xb0ee54: b.eq            #0xb0ee5c
    //     0xb0ee58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0ee5c: LoadField: r0 = r4->field_f
    //     0xb0ee5c: ldur            w0, [x4, #0xf]
    // 0xb0ee60: DecompressPointer r0
    //     0xb0ee60: add             x0, x0, HEAP, lsl #32
    // 0xb0ee64: r16 = Sentinel
    //     0xb0ee64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0ee68: cmp             w0, w16
    // 0xb0ee6c: b.eq            #0xb0f484
    // 0xb0ee70: stur            x0, [fp, #-0x68]
    // 0xb0ee74: r1 = Null
    //     0xb0ee74: mov             x1, NULL
    // 0xb0ee78: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0ee78: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0ee7c: ldr             x2, [x2, #0xad0]
    // 0xb0ee80: r0 = verifiedLocale()
    //     0xb0ee80: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0ee84: stur            x0, [fp, #-0x78]
    // 0xb0ee88: r0 = DateFormat()
    //     0xb0ee88: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0ee8c: mov             x3, x0
    // 0xb0ee90: ldur            x0, [fp, #-0x78]
    // 0xb0ee94: stur            x3, [fp, #-0x80]
    // 0xb0ee98: StoreField: r3->field_7 = r0
    //     0xb0ee98: stur            w0, [x3, #7]
    // 0xb0ee9c: mov             x1, x3
    // 0xb0eea0: r2 = "Hm"
    //     0xb0eea0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0eea4: ldr             x2, [x2, #0xad8]
    // 0xb0eea8: r0 = addPattern()
    //     0xb0eea8: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0eeac: ldur            x1, [fp, #-0x80]
    // 0xb0eeb0: ldur            x2, [fp, #-0x68]
    // 0xb0eeb4: r0 = format()
    //     0xb0eeb4: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0eeb8: mov             x2, x0
    // 0xb0eebc: ldur            x0, [fp, #-0x20]
    // 0xb0eec0: stur            x2, [fp, #-0x68]
    // 0xb0eec4: LoadField: r1 = r0->field_b
    //     0xb0eec4: ldur            w1, [x0, #0xb]
    // 0xb0eec8: LoadField: r3 = r0->field_f
    //     0xb0eec8: ldur            w3, [x0, #0xf]
    // 0xb0eecc: DecompressPointer r3
    //     0xb0eecc: add             x3, x3, HEAP, lsl #32
    // 0xb0eed0: LoadField: r4 = r3->field_b
    //     0xb0eed0: ldur            w4, [x3, #0xb]
    // 0xb0eed4: r3 = LoadInt32Instr(r1)
    //     0xb0eed4: sbfx            x3, x1, #1, #0x1f
    // 0xb0eed8: stur            x3, [fp, #-0x88]
    // 0xb0eedc: r1 = LoadInt32Instr(r4)
    //     0xb0eedc: sbfx            x1, x4, #1, #0x1f
    // 0xb0eee0: cmp             x3, x1
    // 0xb0eee4: b.ne            #0xb0eef0
    // 0xb0eee8: mov             x1, x0
    // 0xb0eeec: r0 = _growToNextCapacity()
    //     0xb0eeec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0eef0: ldur            x4, [fp, #-0x70]
    // 0xb0eef4: ldur            x3, [fp, #-0x20]
    // 0xb0eef8: ldur            x2, [fp, #-0x88]
    // 0xb0eefc: add             x0, x2, #1
    // 0xb0ef00: lsl             x1, x0, #1
    // 0xb0ef04: StoreField: r3->field_b = r1
    //     0xb0ef04: stur            w1, [x3, #0xb]
    // 0xb0ef08: LoadField: r1 = r3->field_f
    //     0xb0ef08: ldur            w1, [x3, #0xf]
    // 0xb0ef0c: DecompressPointer r1
    //     0xb0ef0c: add             x1, x1, HEAP, lsl #32
    // 0xb0ef10: ldur            x0, [fp, #-0x68]
    // 0xb0ef14: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0ef14: add             x25, x1, x2, lsl #2
    //     0xb0ef18: add             x25, x25, #0xf
    //     0xb0ef1c: str             w0, [x25]
    //     0xb0ef20: tbz             w0, #0, #0xb0ef3c
    //     0xb0ef24: ldurb           w16, [x1, #-1]
    //     0xb0ef28: ldurb           w17, [x0, #-1]
    //     0xb0ef2c: and             x16, x17, x16, lsr #2
    //     0xb0ef30: tst             x16, HEAP, lsr #32
    //     0xb0ef34: b.eq            #0xb0ef3c
    //     0xb0ef38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0ef3c: LoadField: r0 = r4->field_13
    //     0xb0ef3c: ldur            w0, [x4, #0x13]
    // 0xb0ef40: DecompressPointer r0
    //     0xb0ef40: add             x0, x0, HEAP, lsl #32
    // 0xb0ef44: r16 = Sentinel
    //     0xb0ef44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0ef48: cmp             w0, w16
    // 0xb0ef4c: b.eq            #0xb0f490
    // 0xb0ef50: stur            x0, [fp, #-0x68]
    // 0xb0ef54: r1 = Null
    //     0xb0ef54: mov             x1, NULL
    // 0xb0ef58: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0ef58: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0ef5c: ldr             x2, [x2, #0xad0]
    // 0xb0ef60: r0 = verifiedLocale()
    //     0xb0ef60: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0ef64: stur            x0, [fp, #-0x78]
    // 0xb0ef68: r0 = DateFormat()
    //     0xb0ef68: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0ef6c: mov             x3, x0
    // 0xb0ef70: ldur            x0, [fp, #-0x78]
    // 0xb0ef74: stur            x3, [fp, #-0x80]
    // 0xb0ef78: StoreField: r3->field_7 = r0
    //     0xb0ef78: stur            w0, [x3, #7]
    // 0xb0ef7c: mov             x1, x3
    // 0xb0ef80: r2 = "Hm"
    //     0xb0ef80: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0ef84: ldr             x2, [x2, #0xad8]
    // 0xb0ef88: r0 = addPattern()
    //     0xb0ef88: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0ef8c: ldur            x1, [fp, #-0x80]
    // 0xb0ef90: ldur            x2, [fp, #-0x68]
    // 0xb0ef94: r0 = format()
    //     0xb0ef94: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0ef98: mov             x2, x0
    // 0xb0ef9c: ldur            x0, [fp, #-0x20]
    // 0xb0efa0: stur            x2, [fp, #-0x68]
    // 0xb0efa4: LoadField: r1 = r0->field_b
    //     0xb0efa4: ldur            w1, [x0, #0xb]
    // 0xb0efa8: LoadField: r3 = r0->field_f
    //     0xb0efa8: ldur            w3, [x0, #0xf]
    // 0xb0efac: DecompressPointer r3
    //     0xb0efac: add             x3, x3, HEAP, lsl #32
    // 0xb0efb0: LoadField: r4 = r3->field_b
    //     0xb0efb0: ldur            w4, [x3, #0xb]
    // 0xb0efb4: r3 = LoadInt32Instr(r1)
    //     0xb0efb4: sbfx            x3, x1, #1, #0x1f
    // 0xb0efb8: stur            x3, [fp, #-0x88]
    // 0xb0efbc: r1 = LoadInt32Instr(r4)
    //     0xb0efbc: sbfx            x1, x4, #1, #0x1f
    // 0xb0efc0: cmp             x3, x1
    // 0xb0efc4: b.ne            #0xb0efd0
    // 0xb0efc8: mov             x1, x0
    // 0xb0efcc: r0 = _growToNextCapacity()
    //     0xb0efcc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0efd0: ldur            x4, [fp, #-0x70]
    // 0xb0efd4: ldur            x3, [fp, #-0x20]
    // 0xb0efd8: ldur            x2, [fp, #-0x88]
    // 0xb0efdc: add             x0, x2, #1
    // 0xb0efe0: lsl             x1, x0, #1
    // 0xb0efe4: StoreField: r3->field_b = r1
    //     0xb0efe4: stur            w1, [x3, #0xb]
    // 0xb0efe8: LoadField: r1 = r3->field_f
    //     0xb0efe8: ldur            w1, [x3, #0xf]
    // 0xb0efec: DecompressPointer r1
    //     0xb0efec: add             x1, x1, HEAP, lsl #32
    // 0xb0eff0: ldur            x0, [fp, #-0x68]
    // 0xb0eff4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0eff4: add             x25, x1, x2, lsl #2
    //     0xb0eff8: add             x25, x25, #0xf
    //     0xb0effc: str             w0, [x25]
    //     0xb0f000: tbz             w0, #0, #0xb0f01c
    //     0xb0f004: ldurb           w16, [x1, #-1]
    //     0xb0f008: ldurb           w17, [x0, #-1]
    //     0xb0f00c: and             x16, x17, x16, lsr #2
    //     0xb0f010: tst             x16, HEAP, lsr #32
    //     0xb0f014: b.eq            #0xb0f01c
    //     0xb0f018: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0f01c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb0f01c: ldur            w0, [x4, #0x17]
    // 0xb0f020: DecompressPointer r0
    //     0xb0f020: add             x0, x0, HEAP, lsl #32
    // 0xb0f024: r16 = Sentinel
    //     0xb0f024: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f028: cmp             w0, w16
    // 0xb0f02c: b.eq            #0xb0f49c
    // 0xb0f030: stur            x0, [fp, #-0x68]
    // 0xb0f034: r1 = Null
    //     0xb0f034: mov             x1, NULL
    // 0xb0f038: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0f038: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0f03c: ldr             x2, [x2, #0xad0]
    // 0xb0f040: r0 = verifiedLocale()
    //     0xb0f040: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0f044: stur            x0, [fp, #-0x78]
    // 0xb0f048: r0 = DateFormat()
    //     0xb0f048: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0f04c: mov             x3, x0
    // 0xb0f050: ldur            x0, [fp, #-0x78]
    // 0xb0f054: stur            x3, [fp, #-0x80]
    // 0xb0f058: StoreField: r3->field_7 = r0
    //     0xb0f058: stur            w0, [x3, #7]
    // 0xb0f05c: mov             x1, x3
    // 0xb0f060: r2 = "Hm"
    //     0xb0f060: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0f064: ldr             x2, [x2, #0xad8]
    // 0xb0f068: r0 = addPattern()
    //     0xb0f068: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0f06c: ldur            x1, [fp, #-0x80]
    // 0xb0f070: ldur            x2, [fp, #-0x68]
    // 0xb0f074: r0 = format()
    //     0xb0f074: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0f078: mov             x2, x0
    // 0xb0f07c: ldur            x0, [fp, #-0x20]
    // 0xb0f080: stur            x2, [fp, #-0x68]
    // 0xb0f084: LoadField: r1 = r0->field_b
    //     0xb0f084: ldur            w1, [x0, #0xb]
    // 0xb0f088: LoadField: r3 = r0->field_f
    //     0xb0f088: ldur            w3, [x0, #0xf]
    // 0xb0f08c: DecompressPointer r3
    //     0xb0f08c: add             x3, x3, HEAP, lsl #32
    // 0xb0f090: LoadField: r4 = r3->field_b
    //     0xb0f090: ldur            w4, [x3, #0xb]
    // 0xb0f094: r3 = LoadInt32Instr(r1)
    //     0xb0f094: sbfx            x3, x1, #1, #0x1f
    // 0xb0f098: stur            x3, [fp, #-0x88]
    // 0xb0f09c: r1 = LoadInt32Instr(r4)
    //     0xb0f09c: sbfx            x1, x4, #1, #0x1f
    // 0xb0f0a0: cmp             x3, x1
    // 0xb0f0a4: b.ne            #0xb0f0b0
    // 0xb0f0a8: mov             x1, x0
    // 0xb0f0ac: r0 = _growToNextCapacity()
    //     0xb0f0ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0f0b0: ldur            x4, [fp, #-0x70]
    // 0xb0f0b4: ldur            x3, [fp, #-0x20]
    // 0xb0f0b8: ldur            x2, [fp, #-0x88]
    // 0xb0f0bc: add             x0, x2, #1
    // 0xb0f0c0: lsl             x1, x0, #1
    // 0xb0f0c4: StoreField: r3->field_b = r1
    //     0xb0f0c4: stur            w1, [x3, #0xb]
    // 0xb0f0c8: LoadField: r1 = r3->field_f
    //     0xb0f0c8: ldur            w1, [x3, #0xf]
    // 0xb0f0cc: DecompressPointer r1
    //     0xb0f0cc: add             x1, x1, HEAP, lsl #32
    // 0xb0f0d0: ldur            x0, [fp, #-0x68]
    // 0xb0f0d4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0f0d4: add             x25, x1, x2, lsl #2
    //     0xb0f0d8: add             x25, x25, #0xf
    //     0xb0f0dc: str             w0, [x25]
    //     0xb0f0e0: tbz             w0, #0, #0xb0f0fc
    //     0xb0f0e4: ldurb           w16, [x1, #-1]
    //     0xb0f0e8: ldurb           w17, [x0, #-1]
    //     0xb0f0ec: and             x16, x17, x16, lsr #2
    //     0xb0f0f0: tst             x16, HEAP, lsr #32
    //     0xb0f0f4: b.eq            #0xb0f0fc
    //     0xb0f0f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0f0fc: LoadField: r0 = r4->field_1b
    //     0xb0f0fc: ldur            w0, [x4, #0x1b]
    // 0xb0f100: DecompressPointer r0
    //     0xb0f100: add             x0, x0, HEAP, lsl #32
    // 0xb0f104: r16 = Sentinel
    //     0xb0f104: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f108: cmp             w0, w16
    // 0xb0f10c: b.eq            #0xb0f4a8
    // 0xb0f110: stur            x0, [fp, #-0x68]
    // 0xb0f114: r1 = Null
    //     0xb0f114: mov             x1, NULL
    // 0xb0f118: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0f118: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0f11c: ldr             x2, [x2, #0xad0]
    // 0xb0f120: r0 = verifiedLocale()
    //     0xb0f120: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0f124: stur            x0, [fp, #-0x78]
    // 0xb0f128: r0 = DateFormat()
    //     0xb0f128: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0f12c: mov             x3, x0
    // 0xb0f130: ldur            x0, [fp, #-0x78]
    // 0xb0f134: stur            x3, [fp, #-0x80]
    // 0xb0f138: StoreField: r3->field_7 = r0
    //     0xb0f138: stur            w0, [x3, #7]
    // 0xb0f13c: mov             x1, x3
    // 0xb0f140: r2 = "Hm"
    //     0xb0f140: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0f144: ldr             x2, [x2, #0xad8]
    // 0xb0f148: r0 = addPattern()
    //     0xb0f148: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0f14c: ldur            x1, [fp, #-0x80]
    // 0xb0f150: ldur            x2, [fp, #-0x68]
    // 0xb0f154: r0 = format()
    //     0xb0f154: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0f158: mov             x2, x0
    // 0xb0f15c: ldur            x0, [fp, #-0x20]
    // 0xb0f160: stur            x2, [fp, #-0x68]
    // 0xb0f164: LoadField: r1 = r0->field_b
    //     0xb0f164: ldur            w1, [x0, #0xb]
    // 0xb0f168: LoadField: r3 = r0->field_f
    //     0xb0f168: ldur            w3, [x0, #0xf]
    // 0xb0f16c: DecompressPointer r3
    //     0xb0f16c: add             x3, x3, HEAP, lsl #32
    // 0xb0f170: LoadField: r4 = r3->field_b
    //     0xb0f170: ldur            w4, [x3, #0xb]
    // 0xb0f174: r3 = LoadInt32Instr(r1)
    //     0xb0f174: sbfx            x3, x1, #1, #0x1f
    // 0xb0f178: stur            x3, [fp, #-0x88]
    // 0xb0f17c: r1 = LoadInt32Instr(r4)
    //     0xb0f17c: sbfx            x1, x4, #1, #0x1f
    // 0xb0f180: cmp             x3, x1
    // 0xb0f184: b.ne            #0xb0f190
    // 0xb0f188: mov             x1, x0
    // 0xb0f18c: r0 = _growToNextCapacity()
    //     0xb0f18c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0f190: ldur            x4, [fp, #-0x70]
    // 0xb0f194: ldur            x3, [fp, #-0x20]
    // 0xb0f198: ldur            x2, [fp, #-0x88]
    // 0xb0f19c: add             x0, x2, #1
    // 0xb0f1a0: lsl             x1, x0, #1
    // 0xb0f1a4: StoreField: r3->field_b = r1
    //     0xb0f1a4: stur            w1, [x3, #0xb]
    // 0xb0f1a8: LoadField: r1 = r3->field_f
    //     0xb0f1a8: ldur            w1, [x3, #0xf]
    // 0xb0f1ac: DecompressPointer r1
    //     0xb0f1ac: add             x1, x1, HEAP, lsl #32
    // 0xb0f1b0: ldur            x0, [fp, #-0x68]
    // 0xb0f1b4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0f1b4: add             x25, x1, x2, lsl #2
    //     0xb0f1b8: add             x25, x25, #0xf
    //     0xb0f1bc: str             w0, [x25]
    //     0xb0f1c0: tbz             w0, #0, #0xb0f1dc
    //     0xb0f1c4: ldurb           w16, [x1, #-1]
    //     0xb0f1c8: ldurb           w17, [x0, #-1]
    //     0xb0f1cc: and             x16, x17, x16, lsr #2
    //     0xb0f1d0: tst             x16, HEAP, lsr #32
    //     0xb0f1d4: b.eq            #0xb0f1dc
    //     0xb0f1d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0f1dc: LoadField: r0 = r4->field_1f
    //     0xb0f1dc: ldur            w0, [x4, #0x1f]
    // 0xb0f1e0: DecompressPointer r0
    //     0xb0f1e0: add             x0, x0, HEAP, lsl #32
    // 0xb0f1e4: r16 = Sentinel
    //     0xb0f1e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0f1e8: cmp             w0, w16
    // 0xb0f1ec: b.eq            #0xb0f4b4
    // 0xb0f1f0: stur            x0, [fp, #-0x68]
    // 0xb0f1f4: r1 = Null
    //     0xb0f1f4: mov             x1, NULL
    // 0xb0f1f8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb0f1f8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb0f1fc: ldr             x2, [x2, #0xad0]
    // 0xb0f200: r0 = verifiedLocale()
    //     0xb0f200: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb0f204: stur            x0, [fp, #-0x70]
    // 0xb0f208: r0 = DateFormat()
    //     0xb0f208: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb0f20c: mov             x3, x0
    // 0xb0f210: ldur            x0, [fp, #-0x70]
    // 0xb0f214: stur            x3, [fp, #-0x78]
    // 0xb0f218: StoreField: r3->field_7 = r0
    //     0xb0f218: stur            w0, [x3, #7]
    // 0xb0f21c: mov             x1, x3
    // 0xb0f220: r2 = "Hm"
    //     0xb0f220: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0xb0f224: ldr             x2, [x2, #0xad8]
    // 0xb0f228: r0 = addPattern()
    //     0xb0f228: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb0f22c: ldur            x1, [fp, #-0x78]
    // 0xb0f230: ldur            x2, [fp, #-0x68]
    // 0xb0f234: r0 = format()
    //     0xb0f234: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb0f238: mov             x2, x0
    // 0xb0f23c: ldur            x0, [fp, #-0x20]
    // 0xb0f240: stur            x2, [fp, #-0x68]
    // 0xb0f244: LoadField: r1 = r0->field_b
    //     0xb0f244: ldur            w1, [x0, #0xb]
    // 0xb0f248: LoadField: r3 = r0->field_f
    //     0xb0f248: ldur            w3, [x0, #0xf]
    // 0xb0f24c: DecompressPointer r3
    //     0xb0f24c: add             x3, x3, HEAP, lsl #32
    // 0xb0f250: LoadField: r4 = r3->field_b
    //     0xb0f250: ldur            w4, [x3, #0xb]
    // 0xb0f254: r3 = LoadInt32Instr(r1)
    //     0xb0f254: sbfx            x3, x1, #1, #0x1f
    // 0xb0f258: stur            x3, [fp, #-0x88]
    // 0xb0f25c: r1 = LoadInt32Instr(r4)
    //     0xb0f25c: sbfx            x1, x4, #1, #0x1f
    // 0xb0f260: cmp             x3, x1
    // 0xb0f264: b.ne            #0xb0f270
    // 0xb0f268: mov             x1, x0
    // 0xb0f26c: r0 = _growToNextCapacity()
    //     0xb0f26c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0f270: ldur            x4, [fp, #-0x60]
    // 0xb0f274: ldur            x2, [fp, #-0x20]
    // 0xb0f278: ldur            x3, [fp, #-0x88]
    // 0xb0f27c: add             x0, x3, #1
    // 0xb0f280: lsl             x1, x0, #1
    // 0xb0f284: StoreField: r2->field_b = r1
    //     0xb0f284: stur            w1, [x2, #0xb]
    // 0xb0f288: LoadField: r1 = r2->field_f
    //     0xb0f288: ldur            w1, [x2, #0xf]
    // 0xb0f28c: DecompressPointer r1
    //     0xb0f28c: add             x1, x1, HEAP, lsl #32
    // 0xb0f290: ldur            x0, [fp, #-0x68]
    // 0xb0f294: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0f294: add             x25, x1, x3, lsl #2
    //     0xb0f298: add             x25, x25, #0xf
    //     0xb0f29c: str             w0, [x25]
    //     0xb0f2a0: tbz             w0, #0, #0xb0f2bc
    //     0xb0f2a4: ldurb           w16, [x1, #-1]
    //     0xb0f2a8: ldurb           w17, [x0, #-1]
    //     0xb0f2ac: and             x16, x17, x16, lsr #2
    //     0xb0f2b0: tst             x16, HEAP, lsr #32
    //     0xb0f2b4: b.eq            #0xb0f2bc
    //     0xb0f2b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0f2bc: LoadField: r0 = r4->field_b
    //     0xb0f2bc: ldur            w0, [x4, #0xb]
    // 0xb0f2c0: LoadField: r1 = r4->field_f
    //     0xb0f2c0: ldur            w1, [x4, #0xf]
    // 0xb0f2c4: DecompressPointer r1
    //     0xb0f2c4: add             x1, x1, HEAP, lsl #32
    // 0xb0f2c8: LoadField: r3 = r1->field_b
    //     0xb0f2c8: ldur            w3, [x1, #0xb]
    // 0xb0f2cc: r5 = LoadInt32Instr(r0)
    //     0xb0f2cc: sbfx            x5, x0, #1, #0x1f
    // 0xb0f2d0: stur            x5, [fp, #-0x88]
    // 0xb0f2d4: r0 = LoadInt32Instr(r3)
    //     0xb0f2d4: sbfx            x0, x3, #1, #0x1f
    // 0xb0f2d8: cmp             x5, x0
    // 0xb0f2dc: b.ne            #0xb0f2e8
    // 0xb0f2e0: mov             x1, x4
    // 0xb0f2e4: r0 = _growToNextCapacity()
    //     0xb0f2e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0f2e8: ldur            x2, [fp, #-0x60]
    // 0xb0f2ec: ldur            x3, [fp, #-0x88]
    // 0xb0f2f0: add             x0, x3, #1
    // 0xb0f2f4: lsl             x1, x0, #1
    // 0xb0f2f8: StoreField: r2->field_b = r1
    //     0xb0f2f8: stur            w1, [x2, #0xb]
    // 0xb0f2fc: LoadField: r1 = r2->field_f
    //     0xb0f2fc: ldur            w1, [x2, #0xf]
    // 0xb0f300: DecompressPointer r1
    //     0xb0f300: add             x1, x1, HEAP, lsl #32
    // 0xb0f304: ldur            x0, [fp, #-0x20]
    // 0xb0f308: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0f308: add             x25, x1, x3, lsl #2
    //     0xb0f30c: add             x25, x25, #0xf
    //     0xb0f310: str             w0, [x25]
    //     0xb0f314: tbz             w0, #0, #0xb0f330
    //     0xb0f318: ldurb           w16, [x1, #-1]
    //     0xb0f31c: ldurb           w17, [x0, #-1]
    //     0xb0f320: and             x16, x17, x16, lsr #2
    //     0xb0f324: tst             x16, HEAP, lsr #32
    //     0xb0f328: b.eq            #0xb0f330
    //     0xb0f32c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0f330: ldur            x0, [fp, #-0x30]
    // 0xb0f334: ldur            x7, [fp, #-0x10]
    // 0xb0f338: mov             x3, x2
    // 0xb0f33c: ldur            x2, [fp, #-0x18]
    // 0xb0f340: ldur            x4, [fp, #-0x28]
    // 0xb0f344: ldur            x5, [fp, #-0x58]
    // 0xb0f348: ldur            x8, [fp, #-0x48]
    // 0xb0f34c: ldur            x6, [fp, #-0x50]
    // 0xb0f350: b               #0xb0e974
    // 0xb0f354: r16 = Instance_FontWeight
    //     0xb0f354: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb0f358: ldr             x16, [x16, #0x198]
    // 0xb0f35c: r30 = 12.000000
    //     0xb0f35c: add             lr, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xb0f360: ldr             lr, [lr, #0xc60]
    // 0xb0f364: stp             lr, x16, [SP]
    // 0xb0f368: ldur            x1, [fp, #-0x40]
    // 0xb0f36c: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb0f36c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e1a0] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb0f370: ldr             x4, [x4, #0x1a0]
    // 0xb0f374: r0 = copyWith()
    //     0xb0f374: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0f378: stur            x0, [fp, #-0x10]
    // 0xb0f37c: r16 = 11.000000
    //     0xb0f37c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1a8] 11
    //     0xb0f380: ldr             x16, [x16, #0x1a8]
    // 0xb0f384: str             x16, [SP]
    // 0xb0f388: ldur            x1, [fp, #-0x40]
    // 0xb0f38c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0f38c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0f390: ldr             x4, [x4, #0x88]
    // 0xb0f394: r0 = copyWith()
    //     0xb0f394: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0f398: stur            x0, [fp, #-0x20]
    // 0xb0f39c: r16 = 9.000000
    //     0xb0f39c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1b0] 9
    //     0xb0f3a0: ldr             x16, [x16, #0x1b0]
    // 0xb0f3a4: str             x16, [SP]
    // 0xb0f3a8: ldur            x1, [fp, #-0x40]
    // 0xb0f3ac: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0f3ac: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0f3b0: ldr             x4, [x4, #0x88]
    // 0xb0f3b4: r0 = copyWith()
    //     0xb0f3b4: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0f3b8: ldur            x2, [fp, #-0x10]
    // 0xb0f3bc: ldur            x3, [fp, #-0x20]
    // 0xb0f3c0: mov             x5, x0
    // 0xb0f3c4: r1 = Null
    //     0xb0f3c4: mov             x1, NULL
    // 0xb0f3c8: r0 = ThemeData()
    //     0xb0f3c8: bl              #0xb0f6c4  ; [package:pdf/src/widgets/theme.dart] ThemeData::ThemeData
    // 0xb0f3cc: ldur            x2, [fp, #-0x18]
    // 0xb0f3d0: r1 = Function '<anonymous closure>':.
    //     0xb0f3d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1b8] AnonymousClosure: (0xb1386c), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0f3d4: ldr             x1, [x1, #0x1b8]
    // 0xb0f3d8: stur            x0, [fp, #-0x10]
    // 0xb0f3dc: r0 = AllocateClosure()
    //     0xb0f3dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0f3e0: ldur            x2, [fp, #-0x18]
    // 0xb0f3e4: r1 = Function '<anonymous closure>':.
    //     0xb0f3e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1c0] AnonymousClosure: (0xb126f0), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0f3e8: ldr             x1, [x1, #0x1c0]
    // 0xb0f3ec: stur            x0, [fp, #-0x18]
    // 0xb0f3f0: r0 = AllocateClosure()
    //     0xb0f3f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0f3f4: r1 = Function '<anonymous closure>':.
    //     0xb0f3f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1c8] AnonymousClosure: (0xb1221c), in [package:nuonline/app/modules/prayer_time/widgets/prayer_time_pdf_layout.dart] PrayerTimePdfLayout::build (0xb0dec0)
    //     0xb0f3f8: ldr             x1, [x1, #0x1c8]
    // 0xb0f3fc: r2 = Null
    //     0xb0f3fc: mov             x2, NULL
    // 0xb0f400: stur            x0, [fp, #-0x20]
    // 0xb0f404: r0 = AllocateClosure()
    //     0xb0f404: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0f408: stur            x0, [fp, #-0x38]
    // 0xb0f40c: r0 = MultiPage()
    //     0xb0f40c: bl              #0xb0f6b8  ; AllocateMultiPageStub -> MultiPage (size=0x28)
    // 0xb0f410: mov             x1, x0
    // 0xb0f414: ldur            x2, [fp, #-0x20]
    // 0xb0f418: ldur            x3, [fp, #-0x38]
    // 0xb0f41c: ldur            x5, [fp, #-0x18]
    // 0xb0f420: ldur            x6, [fp, #-0x10]
    // 0xb0f424: stur            x0, [fp, #-0x10]
    // 0xb0f428: r0 = MultiPage()
    //     0xb0f428: bl              #0xb0f4c0  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::MultiPage
    // 0xb0f42c: ldur            x0, [fp, #-0x10]
    // 0xb0f430: r0 = ReturnAsyncNotFuture()
    //     0xb0f430: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb0f434: mov             x0, x4
    // 0xb0f438: r0 = ConcurrentModificationError()
    //     0xb0f438: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb0f43c: mov             x1, x0
    // 0xb0f440: ldur            x0, [fp, #-0x28]
    // 0xb0f444: StoreField: r1->field_b = r0
    //     0xb0f444: stur            w0, [x1, #0xb]
    // 0xb0f448: mov             x0, x1
    // 0xb0f44c: r0 = Throw()
    //     0xb0f44c: bl              #0xec04b8  ; ThrowStub
    // 0xb0f450: brk             #0
    // 0xb0f454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f454: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f458: b               #0xb0dee0
    // 0xb0f45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0f45c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0f460: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb0f460: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb0f464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f468: b               #0xb0e984
    // 0xb0f46c: r9 = _fajr
    //     0xb0f46c: add             x9, PP, #8, lsl #12  ; [pp+0x8280] Field <PrayerTimes._fajr@750406012>: late (offset: 0x8)
    //     0xb0f470: ldr             x9, [x9, #0x280]
    // 0xb0f474: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f474: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f478: r9 = _sunrise
    //     0xb0f478: add             x9, PP, #8, lsl #12  ; [pp+0x8288] Field <PrayerTimes._sunrise@750406012>: late (offset: 0xc)
    //     0xb0f47c: ldr             x9, [x9, #0x288]
    // 0xb0f480: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f480: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f484: r9 = _dluha
    //     0xb0f484: add             x9, PP, #8, lsl #12  ; [pp+0x8290] Field <PrayerTimes._dluha@750406012>: late (offset: 0x10)
    //     0xb0f488: ldr             x9, [x9, #0x290]
    // 0xb0f48c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f48c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f490: r9 = _dhuhr
    //     0xb0f490: add             x9, PP, #8, lsl #12  ; [pp+0x8298] Field <PrayerTimes._dhuhr@750406012>: late (offset: 0x14)
    //     0xb0f494: ldr             x9, [x9, #0x298]
    // 0xb0f498: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f498: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f49c: r9 = _asr
    //     0xb0f49c: add             x9, PP, #8, lsl #12  ; [pp+0x82a0] Field <PrayerTimes._asr@750406012>: late (offset: 0x18)
    //     0xb0f4a0: ldr             x9, [x9, #0x2a0]
    // 0xb0f4a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f4a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f4a8: r9 = _maghrib
    //     0xb0f4a8: add             x9, PP, #8, lsl #12  ; [pp+0x82a8] Field <PrayerTimes._maghrib@750406012>: late (offset: 0x1c)
    //     0xb0f4ac: ldr             x9, [x9, #0x2a8]
    // 0xb0f4b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f4b0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb0f4b4: r9 = _isha
    //     0xb0f4b4: add             x9, PP, #8, lsl #12  ; [pp+0x82b0] Field <PrayerTimes._isha@750406012>: late (offset: 0x20)
    //     0xb0f4b8: ldr             x9, [x9, #0x2b0]
    // 0xb0f4bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb0f4bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, Context) {
    // ** addr: 0xb1221c, size: 0x134
    // 0xb1221c: EnterFrame
    //     0xb1221c: stp             fp, lr, [SP, #-0x10]!
    //     0xb12220: mov             fp, SP
    // 0xb12224: AllocStack(0x18)
    //     0xb12224: sub             SP, SP, #0x18
    // 0xb12228: CheckStackOverflow
    //     0xb12228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1222c: cmp             SP, x16
    //     0xb12230: b.ls            #0xb12348
    // 0xb12234: ldr             x1, [fp, #0x10]
    // 0xb12238: r0 = of()
    //     0xb12238: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb1223c: LoadField: r1 = r0->field_1b
    //     0xb1223c: ldur            w1, [x0, #0x1b]
    // 0xb12240: DecompressPointer r1
    //     0xb12240: add             x1, x1, HEAP, lsl #32
    // 0xb12244: stur            x1, [fp, #-8]
    // 0xb12248: r0 = TextStyle()
    //     0xb12248: bl              #0xb121c8  ; AllocateTextStyleStub -> TextStyle (size=0x58)
    // 0xb1224c: mov             x1, x0
    // 0xb12250: r0 = true
    //     0xb12250: add             x0, NULL, #0x20  ; true
    // 0xb12254: stur            x1, [fp, #-0x10]
    // 0xb12258: StoreField: r1->field_7 = r0
    //     0xb12258: stur            w0, [x1, #7]
    // 0xb1225c: r0 = const []
    //     0xb1225c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e128] List<Font>(0)
    //     0xb12260: ldr             x0, [x0, #0x128]
    // 0xb12264: StoreField: r1->field_1f = r0
    //     0xb12264: stur            w0, [x1, #0x1f]
    // 0xb12268: r0 = Instance_FontWeight
    //     0xb12268: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb1226c: ldr             x0, [x0, #0x198]
    // 0xb12270: StoreField: r1->field_27 = r0
    //     0xb12270: stur            w0, [x1, #0x27]
    // 0xb12274: r0 = TextSpan()
    //     0xb12274: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb12278: mov             x3, x0
    // 0xb1227c: r0 = " NU Online Super App "
    //     0xb1227c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e1d0] " NU Online Super App "
    //     0xb12280: ldr             x0, [x0, #0x1d0]
    // 0xb12284: stur            x3, [fp, #-0x18]
    // 0xb12288: ArrayStore: r3[0] = r0  ; List_4
    //     0xb12288: stur            w0, [x3, #0x17]
    // 0xb1228c: ldur            x0, [fp, #-0x10]
    // 0xb12290: StoreField: r3->field_7 = r0
    //     0xb12290: stur            w0, [x3, #7]
    // 0xb12294: StoreField: r3->field_b = rZR
    //     0xb12294: stur            xzr, [x3, #0xb]
    // 0xb12298: r1 = Null
    //     0xb12298: mov             x1, NULL
    // 0xb1229c: r2 = 6
    //     0xb1229c: movz            x2, #0x6
    // 0xb122a0: r0 = AllocateArray()
    //     0xb122a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb122a4: stur            x0, [fp, #-0x10]
    // 0xb122a8: r16 = Instance_TextSpan
    //     0xb122a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1d8] Obj!TextSpan@e0c441
    //     0xb122ac: ldr             x16, [x16, #0x1d8]
    // 0xb122b0: StoreField: r0->field_f = r16
    //     0xb122b0: stur            w16, [x0, #0xf]
    // 0xb122b4: ldur            x1, [fp, #-0x18]
    // 0xb122b8: StoreField: r0->field_13 = r1
    //     0xb122b8: stur            w1, [x0, #0x13]
    // 0xb122bc: r16 = Instance_TextSpan
    //     0xb122bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1e0] Obj!TextSpan@e0c421
    //     0xb122c0: ldr             x16, [x16, #0x1e0]
    // 0xb122c4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb122c4: stur            w16, [x0, #0x17]
    // 0xb122c8: r1 = <InlineSpan>
    //     0xb122c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1e8] TypeArguments: <InlineSpan>
    //     0xb122cc: ldr             x1, [x1, #0x1e8]
    // 0xb122d0: r0 = AllocateGrowableArray()
    //     0xb122d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb122d4: mov             x1, x0
    // 0xb122d8: ldur            x0, [fp, #-0x10]
    // 0xb122dc: stur            x1, [fp, #-0x18]
    // 0xb122e0: StoreField: r1->field_f = r0
    //     0xb122e0: stur            w0, [x1, #0xf]
    // 0xb122e4: r0 = 6
    //     0xb122e4: movz            x0, #0x6
    // 0xb122e8: StoreField: r1->field_b = r0
    //     0xb122e8: stur            w0, [x1, #0xb]
    // 0xb122ec: r0 = TextSpan()
    //     0xb122ec: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb122f0: mov             x1, x0
    // 0xb122f4: ldur            x0, [fp, #-0x18]
    // 0xb122f8: stur            x1, [fp, #-0x10]
    // 0xb122fc: StoreField: r1->field_1b = r0
    //     0xb122fc: stur            w0, [x1, #0x1b]
    // 0xb12300: ldur            x0, [fp, #-8]
    // 0xb12304: StoreField: r1->field_7 = r0
    //     0xb12304: stur            w0, [x1, #7]
    // 0xb12308: StoreField: r1->field_b = rZR
    //     0xb12308: stur            xzr, [x1, #0xb]
    // 0xb1230c: r0 = RichText()
    //     0xb1230c: bl              #0xb1259c  ; AllocateRichTextStub -> RichText (size=0x4c)
    // 0xb12310: mov             x1, x0
    // 0xb12314: ldur            x2, [fp, #-0x10]
    // 0xb12318: stur            x0, [fp, #-8]
    // 0xb1231c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1231c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb12320: r0 = RichText()
    //     0xb12320: bl              #0xb1235c  ; [package:pdf/src/widgets/text.dart] RichText::RichText
    // 0xb12324: r0 = Padding()
    //     0xb12324: bl              #0xb12350  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb12328: r1 = Instance_EdgeInsets
    //     0xb12328: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1f0] Obj!EdgeInsets@e0c4d1
    //     0xb1232c: ldr             x1, [x1, #0x1f0]
    // 0xb12330: StoreField: r0->field_f = r1
    //     0xb12330: stur            w1, [x0, #0xf]
    // 0xb12334: ldur            x1, [fp, #-8]
    // 0xb12338: StoreField: r0->field_b = r1
    //     0xb12338: stur            w1, [x0, #0xb]
    // 0xb1233c: LeaveFrame
    //     0xb1233c: mov             SP, fp
    //     0xb12340: ldp             fp, lr, [SP], #0x10
    // 0xb12344: ret
    //     0xb12344: ret             
    // 0xb12348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1234c: b               #0xb12234
  }
  [closure] List<Widget> <anonymous closure>(dynamic, Context) {
    // ** addr: 0xb126f0, size: 0x2c0
    // 0xb126f0: EnterFrame
    //     0xb126f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb126f4: mov             fp, SP
    // 0xb126f8: AllocStack(0x48)
    //     0xb126f8: sub             SP, SP, #0x48
    // 0xb126fc: SetupParameters()
    //     0xb126fc: ldr             x0, [fp, #0x18]
    //     0xb12700: ldur            w2, [x0, #0x17]
    //     0xb12704: add             x2, x2, HEAP, lsl #32
    //     0xb12708: stur            x2, [fp, #-8]
    // 0xb1270c: CheckStackOverflow
    //     0xb1270c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12710: cmp             SP, x16
    //     0xb12714: b.ls            #0xb129a4
    // 0xb12718: r1 = Instance_MaterialColor
    //     0xb12718: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb1271c: ldr             x1, [x1, #0xcf0]
    // 0xb12720: r0 = value()
    //     0xb12720: bl              #0xd69d08  ; [dart:ui] Color::value
    // 0xb12724: stur            x0, [fp, #-0x10]
    // 0xb12728: asr             x1, x0, #0x10
    // 0xb1272c: ubfx            x1, x1, #0, #0x20
    // 0xb12730: r2 = 255
    //     0xb12730: movz            x2, #0xff
    // 0xb12734: and             x3, x1, x2
    // 0xb12738: ubfx            x3, x3, #0, #0x20
    // 0xb1273c: scvtf           d0, x3
    // 0xb12740: d1 = 255.000000
    //     0xb12740: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb12744: fdiv            d2, d0, d1
    // 0xb12748: stur            d2, [fp, #-0x38]
    // 0xb1274c: r0 = PdfColor()
    //     0xb1274c: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xb12750: ldur            d0, [fp, #-0x38]
    // 0xb12754: stur            x0, [fp, #-0x18]
    // 0xb12758: StoreField: r0->field_f = d0
    //     0xb12758: stur            d0, [x0, #0xf]
    // 0xb1275c: ldur            x1, [fp, #-0x10]
    // 0xb12760: asr             x2, x1, #8
    // 0xb12764: ubfx            x2, x2, #0, #0x20
    // 0xb12768: r3 = 255
    //     0xb12768: movz            x3, #0xff
    // 0xb1276c: and             x4, x2, x3
    // 0xb12770: ubfx            x4, x4, #0, #0x20
    // 0xb12774: scvtf           d0, x4
    // 0xb12778: d1 = 255.000000
    //     0xb12778: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb1277c: fdiv            d2, d0, d1
    // 0xb12780: ArrayStore: r0[0] = d2  ; List_8
    //     0xb12780: stur            d2, [x0, #0x17]
    // 0xb12784: mov             x2, x1
    // 0xb12788: ubfx            x2, x2, #0, #0x20
    // 0xb1278c: and             x4, x2, x3
    // 0xb12790: ubfx            x4, x4, #0, #0x20
    // 0xb12794: scvtf           d0, x4
    // 0xb12798: fdiv            d2, d0, d1
    // 0xb1279c: StoreField: r0->field_1f = d2
    //     0xb1279c: stur            d2, [x0, #0x1f]
    // 0xb127a0: asr             x2, x1, #0x18
    // 0xb127a4: ubfx            x2, x2, #0, #0x20
    // 0xb127a8: and             x1, x2, x3
    // 0xb127ac: ubfx            x1, x1, #0, #0x20
    // 0xb127b0: scvtf           d0, x1
    // 0xb127b4: fdiv            d2, d0, d1
    // 0xb127b8: StoreField: r0->field_7 = d2
    //     0xb127b8: stur            d2, [x0, #7]
    // 0xb127bc: r0 = BoxDecoration()
    //     0xb127bc: bl              #0xb13860  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x24)
    // 0xb127c0: mov             x2, x0
    // 0xb127c4: ldur            x0, [fp, #-0x18]
    // 0xb127c8: stur            x2, [fp, #-0x20]
    // 0xb127cc: StoreField: r2->field_7 = r0
    //     0xb127cc: stur            w0, [x2, #7]
    // 0xb127d0: r0 = Instance_BoxShape
    //     0xb127d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e238] Obj!BoxShape@e2e9e1
    //     0xb127d4: ldr             x0, [x0, #0x238]
    // 0xb127d8: StoreField: r2->field_13 = r0
    //     0xb127d8: stur            w0, [x2, #0x13]
    // 0xb127dc: ldr             x1, [fp, #0x10]
    // 0xb127e0: r0 = of()
    //     0xb127e0: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb127e4: LoadField: r1 = r0->field_1b
    //     0xb127e4: ldur            w1, [x0, #0x1b]
    // 0xb127e8: DecompressPointer r1
    //     0xb127e8: add             x1, x1, HEAP, lsl #32
    // 0xb127ec: r16 = Instance_FontWeight
    //     0xb127ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb127f0: ldr             x16, [x16, #0x198]
    // 0xb127f4: r30 = Instance_PdfColor
    //     0xb127f4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e240] Obj!PdfColor@e0ca11
    //     0xb127f8: ldr             lr, [lr, #0x240]
    // 0xb127fc: stp             lr, x16, [SP]
    // 0xb12800: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0xb12800: add             x4, PP, #0x27, lsl #12  ; [pp+0x27ff8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0xb12804: ldr             x4, [x4, #0xff8]
    // 0xb12808: r0 = copyWith()
    //     0xb12808: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb1280c: r1 = _ConstMap len:10
    //     0xb1280c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb12810: ldr             x1, [x1, #0xc08]
    // 0xb12814: r2 = 100
    //     0xb12814: movz            x2, #0x64
    // 0xb12818: stur            x0, [fp, #-0x18]
    // 0xb1281c: r0 = []()
    //     0xb1281c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb12820: cmp             w0, NULL
    // 0xb12824: b.eq            #0xb129ac
    // 0xb12828: r1 = LoadClassIdInstr(r0)
    //     0xb12828: ldur            x1, [x0, #-1]
    //     0xb1282c: ubfx            x1, x1, #0xc, #0x14
    // 0xb12830: mov             x16, x0
    // 0xb12834: mov             x0, x1
    // 0xb12838: mov             x1, x16
    // 0xb1283c: r0 = GDT[cid_x0 + -0xd99]()
    //     0xb1283c: sub             lr, x0, #0xd99
    //     0xb12840: ldr             lr, [x21, lr, lsl #3]
    //     0xb12844: blr             lr
    // 0xb12848: stur            x0, [fp, #-0x10]
    // 0xb1284c: asr             x1, x0, #0x10
    // 0xb12850: ubfx            x1, x1, #0, #0x20
    // 0xb12854: r2 = 255
    //     0xb12854: movz            x2, #0xff
    // 0xb12858: and             x3, x1, x2
    // 0xb1285c: ubfx            x3, x3, #0, #0x20
    // 0xb12860: scvtf           d0, x3
    // 0xb12864: d1 = 255.000000
    //     0xb12864: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb12868: fdiv            d2, d0, d1
    // 0xb1286c: stur            d2, [fp, #-0x38]
    // 0xb12870: r0 = PdfColor()
    //     0xb12870: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xb12874: ldur            d0, [fp, #-0x38]
    // 0xb12878: stur            x0, [fp, #-0x28]
    // 0xb1287c: StoreField: r0->field_f = d0
    //     0xb1287c: stur            d0, [x0, #0xf]
    // 0xb12880: ldur            x1, [fp, #-0x10]
    // 0xb12884: asr             x2, x1, #8
    // 0xb12888: ubfx            x2, x2, #0, #0x20
    // 0xb1288c: r3 = 255
    //     0xb1288c: movz            x3, #0xff
    // 0xb12890: and             x4, x2, x3
    // 0xb12894: ubfx            x4, x4, #0, #0x20
    // 0xb12898: scvtf           d0, x4
    // 0xb1289c: d1 = 255.000000
    //     0xb1289c: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb128a0: fdiv            d2, d0, d1
    // 0xb128a4: ArrayStore: r0[0] = d2  ; List_8
    //     0xb128a4: stur            d2, [x0, #0x17]
    // 0xb128a8: mov             x2, x1
    // 0xb128ac: ubfx            x2, x2, #0, #0x20
    // 0xb128b0: and             x4, x2, x3
    // 0xb128b4: ubfx            x4, x4, #0, #0x20
    // 0xb128b8: scvtf           d0, x4
    // 0xb128bc: fdiv            d2, d0, d1
    // 0xb128c0: StoreField: r0->field_1f = d2
    //     0xb128c0: stur            d2, [x0, #0x1f]
    // 0xb128c4: asr             x2, x1, #0x18
    // 0xb128c8: ubfx            x2, x2, #0, #0x20
    // 0xb128cc: and             x1, x2, x3
    // 0xb128d0: ubfx            x1, x1, #0, #0x20
    // 0xb128d4: scvtf           d0, x1
    // 0xb128d8: fdiv            d2, d0, d1
    // 0xb128dc: StoreField: r0->field_7 = d2
    //     0xb128dc: stur            d2, [x0, #7]
    // 0xb128e0: r0 = BoxDecoration()
    //     0xb128e0: bl              #0xb13860  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x24)
    // 0xb128e4: mov             x2, x0
    // 0xb128e8: ldur            x0, [fp, #-0x28]
    // 0xb128ec: stur            x2, [fp, #-0x30]
    // 0xb128f0: StoreField: r2->field_7 = r0
    //     0xb128f0: stur            w0, [x2, #7]
    // 0xb128f4: r0 = Instance_BoxShape
    //     0xb128f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e238] Obj!BoxShape@e2e9e1
    //     0xb128f8: ldr             x0, [x0, #0x238]
    // 0xb128fc: StoreField: r2->field_13 = r0
    //     0xb128fc: stur            w0, [x2, #0x13]
    // 0xb12900: ldr             x1, [fp, #0x10]
    // 0xb12904: r0 = of()
    //     0xb12904: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb12908: LoadField: r2 = r0->field_1b
    //     0xb12908: ldur            w2, [x0, #0x1b]
    // 0xb1290c: DecompressPointer r2
    //     0xb1290c: add             x2, x2, HEAP, lsl #32
    // 0xb12910: ldr             x1, [fp, #0x10]
    // 0xb12914: stur            x2, [fp, #-0x28]
    // 0xb12918: r0 = of()
    //     0xb12918: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb1291c: LoadField: r1 = r0->field_1b
    //     0xb1291c: ldur            w1, [x0, #0x1b]
    // 0xb12920: DecompressPointer r1
    //     0xb12920: add             x1, x1, HEAP, lsl #32
    // 0xb12924: ldur            x0, [fp, #-8]
    // 0xb12928: LoadField: r3 = r0->field_23
    //     0xb12928: ldur            w3, [x0, #0x23]
    // 0xb1292c: DecompressPointer r3
    //     0xb1292c: add             x3, x3, HEAP, lsl #32
    // 0xb12930: LoadField: r7 = r0->field_1f
    //     0xb12930: ldur            w7, [x0, #0x1f]
    // 0xb12934: DecompressPointer r7
    //     0xb12934: add             x7, x7, HEAP, lsl #32
    // 0xb12938: ldur            x16, [fp, #-0x30]
    // 0xb1293c: stp             x16, x1, [SP]
    // 0xb12940: ldur            x1, [fp, #-0x28]
    // 0xb12944: ldr             x2, [fp, #0x10]
    // 0xb12948: ldur            x5, [fp, #-0x20]
    // 0xb1294c: ldur            x6, [fp, #-0x18]
    // 0xb12950: r4 = const [0, 0x8, 0x2, 0x8, null]
    //     0xb12950: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e248] List(5) [0, 0x8, 0x2, 0x8, Null]
    //     0xb12954: ldr             x4, [x4, #0x248]
    // 0xb12958: r0 = fromTextArray()
    //     0xb12958: bl              #0xb129b0  ; [package:pdf/src/widgets/table_helper.dart] TableHelper::fromTextArray
    // 0xb1295c: r1 = Null
    //     0xb1295c: mov             x1, NULL
    // 0xb12960: r2 = 2
    //     0xb12960: movz            x2, #0x2
    // 0xb12964: stur            x0, [fp, #-8]
    // 0xb12968: r0 = AllocateArray()
    //     0xb12968: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1296c: mov             x2, x0
    // 0xb12970: ldur            x0, [fp, #-8]
    // 0xb12974: stur            x2, [fp, #-0x18]
    // 0xb12978: StoreField: r2->field_f = r0
    //     0xb12978: stur            w0, [x2, #0xf]
    // 0xb1297c: r1 = <Widget>
    //     0xb1297c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e250] TypeArguments: <Widget>
    //     0xb12980: ldr             x1, [x1, #0x250]
    // 0xb12984: r0 = AllocateGrowableArray()
    //     0xb12984: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb12988: ldur            x1, [fp, #-0x18]
    // 0xb1298c: StoreField: r0->field_f = r1
    //     0xb1298c: stur            w1, [x0, #0xf]
    // 0xb12990: r1 = 2
    //     0xb12990: movz            x1, #0x2
    // 0xb12994: StoreField: r0->field_b = r1
    //     0xb12994: stur            w1, [x0, #0xb]
    // 0xb12998: LeaveFrame
    //     0xb12998: mov             SP, fp
    //     0xb1299c: ldp             fp, lr, [SP], #0x10
    // 0xb129a0: ret
    //     0xb129a0: ret             
    // 0xb129a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb129a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb129a8: b               #0xb12718
    // 0xb129ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb129ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, Context) {
    // ** addr: 0xb1386c, size: 0x364
    // 0xb1386c: EnterFrame
    //     0xb1386c: stp             fp, lr, [SP, #-0x10]!
    //     0xb13870: mov             fp, SP
    // 0xb13874: AllocStack(0x60)
    //     0xb13874: sub             SP, SP, #0x60
    // 0xb13878: SetupParameters()
    //     0xb13878: ldr             x0, [fp, #0x18]
    //     0xb1387c: ldur            w3, [x0, #0x17]
    //     0xb13880: add             x3, x3, HEAP, lsl #32
    //     0xb13884: stur            x3, [fp, #-8]
    // 0xb13888: CheckStackOverflow
    //     0xb13888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1388c: cmp             SP, x16
    //     0xb13890: b.ls            #0xb13bc4
    // 0xb13894: LoadField: r2 = r3->field_13
    //     0xb13894: ldur            w2, [x3, #0x13]
    // 0xb13898: DecompressPointer r2
    //     0xb13898: add             x2, x2, HEAP, lsl #32
    // 0xb1389c: r1 = Null
    //     0xb1389c: mov             x1, NULL
    // 0xb138a0: r0 = SvgImage()
    //     0xb138a0: bl              #0xb13e24  ; [package:pdf/src/widgets/svg.dart] SvgImage::SvgImage
    // 0xb138a4: stur            x0, [fp, #-0x10]
    // 0xb138a8: r0 = SizedBox()
    //     0xb138a8: bl              #0xb13e18  ; AllocateSizedBoxStub -> SizedBox (size=0x1c)
    // 0xb138ac: mov             x2, x0
    // 0xb138b0: r0 = 16.000000
    //     0xb138b0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb138b4: ldr             x0, [x0, #0x80]
    // 0xb138b8: stur            x2, [fp, #-0x18]
    // 0xb138bc: StoreField: r2->field_f = r0
    //     0xb138bc: stur            w0, [x2, #0xf]
    // 0xb138c0: ldr             x1, [fp, #0x10]
    // 0xb138c4: r0 = of()
    //     0xb138c4: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb138c8: LoadField: r3 = r0->field_13
    //     0xb138c8: ldur            w3, [x0, #0x13]
    // 0xb138cc: DecompressPointer r3
    //     0xb138cc: add             x3, x3, HEAP, lsl #32
    // 0xb138d0: ldur            x0, [fp, #-8]
    // 0xb138d4: stur            x3, [fp, #-0x28]
    // 0xb138d8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb138d8: ldur            w2, [x0, #0x17]
    // 0xb138dc: DecompressPointer r2
    //     0xb138dc: add             x2, x2, HEAP, lsl #32
    // 0xb138e0: stur            x2, [fp, #-0x20]
    // 0xb138e4: r0 = Text()
    //     0xb138e4: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb138e8: mov             x1, x0
    // 0xb138ec: ldur            x2, [fp, #-0x20]
    // 0xb138f0: ldur            x3, [fp, #-0x28]
    // 0xb138f4: stur            x0, [fp, #-0x20]
    // 0xb138f8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb138f8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb138fc: r0 = Text()
    //     0xb138fc: bl              #0xb135fc  ; [package:pdf/src/widgets/text.dart] Text::Text
    // 0xb13900: r0 = SizedBox()
    //     0xb13900: bl              #0xb13e18  ; AllocateSizedBoxStub -> SizedBox (size=0x1c)
    // 0xb13904: mov             x2, x0
    // 0xb13908: r0 = 4.000000
    //     0xb13908: add             x0, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0xb1390c: ldr             x0, [x0, #0x770]
    // 0xb13910: stur            x2, [fp, #-0x28]
    // 0xb13914: StoreField: r2->field_13 = r0
    //     0xb13914: stur            w0, [x2, #0x13]
    // 0xb13918: ldur            x3, [fp, #-8]
    // 0xb1391c: LoadField: r1 = r3->field_f
    //     0xb1391c: ldur            w1, [x3, #0xf]
    // 0xb13920: DecompressPointer r1
    //     0xb13920: add             x1, x1, HEAP, lsl #32
    // 0xb13924: LoadField: r4 = r1->field_7
    //     0xb13924: ldur            w4, [x1, #7]
    // 0xb13928: DecompressPointer r4
    //     0xb13928: add             x4, x4, HEAP, lsl #32
    // 0xb1392c: mov             x1, x4
    // 0xb13930: r0 = fullAddress()
    //     0xb13930: bl              #0xb13ce0  ; [package:nuonline/app/data/models/location.dart] Location::fullAddress
    // 0xb13934: ldr             x1, [fp, #0x10]
    // 0xb13938: stur            x0, [fp, #-0x30]
    // 0xb1393c: r0 = of()
    //     0xb1393c: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb13940: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb13940: ldur            w3, [x0, #0x17]
    // 0xb13944: DecompressPointer r3
    //     0xb13944: add             x3, x3, HEAP, lsl #32
    // 0xb13948: stur            x3, [fp, #-0x38]
    // 0xb1394c: r0 = Text()
    //     0xb1394c: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb13950: mov             x1, x0
    // 0xb13954: ldur            x2, [fp, #-0x30]
    // 0xb13958: ldur            x3, [fp, #-0x38]
    // 0xb1395c: stur            x0, [fp, #-0x30]
    // 0xb13960: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb13960: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb13964: r0 = Text()
    //     0xb13964: bl              #0xb135fc  ; [package:pdf/src/widgets/text.dart] Text::Text
    // 0xb13968: r0 = SizedBox()
    //     0xb13968: bl              #0xb13e18  ; AllocateSizedBoxStub -> SizedBox (size=0x1c)
    // 0xb1396c: mov             x2, x0
    // 0xb13970: r0 = 4.000000
    //     0xb13970: add             x0, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0xb13974: ldr             x0, [x0, #0x770]
    // 0xb13978: stur            x2, [fp, #-0x38]
    // 0xb1397c: StoreField: r2->field_13 = r0
    //     0xb1397c: stur            w0, [x2, #0x13]
    // 0xb13980: ldr             x1, [fp, #0x10]
    // 0xb13984: r0 = of()
    //     0xb13984: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb13988: LoadField: r3 = r0->field_1b
    //     0xb13988: ldur            w3, [x0, #0x1b]
    // 0xb1398c: DecompressPointer r3
    //     0xb1398c: add             x3, x3, HEAP, lsl #32
    // 0xb13990: stur            x3, [fp, #-0x40]
    // 0xb13994: r1 = _ConstMap len:6
    //     0xb13994: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb13998: ldr             x1, [x1, #0xc20]
    // 0xb1399c: r2 = 8
    //     0xb1399c: movz            x2, #0x8
    // 0xb139a0: r0 = []()
    //     0xb139a0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb139a4: cmp             w0, NULL
    // 0xb139a8: b.eq            #0xb13bcc
    // 0xb139ac: r1 = LoadClassIdInstr(r0)
    //     0xb139ac: ldur            x1, [x0, #-1]
    //     0xb139b0: ubfx            x1, x1, #0xc, #0x14
    // 0xb139b4: mov             x16, x0
    // 0xb139b8: mov             x0, x1
    // 0xb139bc: mov             x1, x16
    // 0xb139c0: r0 = GDT[cid_x0 + -0xd99]()
    //     0xb139c0: sub             lr, x0, #0xd99
    //     0xb139c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb139c8: blr             lr
    // 0xb139cc: stur            x0, [fp, #-0x48]
    // 0xb139d0: asr             x1, x0, #0x10
    // 0xb139d4: ubfx            x1, x1, #0, #0x20
    // 0xb139d8: r2 = 255
    //     0xb139d8: movz            x2, #0xff
    // 0xb139dc: and             x3, x1, x2
    // 0xb139e0: ubfx            x3, x3, #0, #0x20
    // 0xb139e4: scvtf           d0, x3
    // 0xb139e8: d1 = 255.000000
    //     0xb139e8: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb139ec: fdiv            d2, d0, d1
    // 0xb139f0: stur            d2, [fp, #-0x58]
    // 0xb139f4: r0 = PdfColor()
    //     0xb139f4: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xb139f8: ldur            d0, [fp, #-0x58]
    // 0xb139fc: StoreField: r0->field_f = d0
    //     0xb139fc: stur            d0, [x0, #0xf]
    // 0xb13a00: ldur            x1, [fp, #-0x48]
    // 0xb13a04: asr             x2, x1, #8
    // 0xb13a08: ubfx            x2, x2, #0, #0x20
    // 0xb13a0c: r3 = 255
    //     0xb13a0c: movz            x3, #0xff
    // 0xb13a10: and             x4, x2, x3
    // 0xb13a14: ubfx            x4, x4, #0, #0x20
    // 0xb13a18: scvtf           d0, x4
    // 0xb13a1c: d1 = 255.000000
    //     0xb13a1c: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb13a20: fdiv            d2, d0, d1
    // 0xb13a24: ArrayStore: r0[0] = d2  ; List_8
    //     0xb13a24: stur            d2, [x0, #0x17]
    // 0xb13a28: mov             x2, x1
    // 0xb13a2c: ubfx            x2, x2, #0, #0x20
    // 0xb13a30: and             x4, x2, x3
    // 0xb13a34: ubfx            x4, x4, #0, #0x20
    // 0xb13a38: scvtf           d0, x4
    // 0xb13a3c: fdiv            d2, d0, d1
    // 0xb13a40: StoreField: r0->field_1f = d2
    //     0xb13a40: stur            d2, [x0, #0x1f]
    // 0xb13a44: asr             x2, x1, #0x18
    // 0xb13a48: ubfx            x2, x2, #0, #0x20
    // 0xb13a4c: and             x1, x2, x3
    // 0xb13a50: ubfx            x1, x1, #0, #0x20
    // 0xb13a54: scvtf           d0, x1
    // 0xb13a58: fdiv            d2, d0, d1
    // 0xb13a5c: StoreField: r0->field_7 = d2
    //     0xb13a5c: stur            d2, [x0, #7]
    // 0xb13a60: str             x0, [SP]
    // 0xb13a64: ldur            x1, [fp, #-0x40]
    // 0xb13a68: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb13a68: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb13a6c: ldr             x4, [x4, #0x228]
    // 0xb13a70: r0 = copyWith()
    //     0xb13a70: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb13a74: mov             x1, x0
    // 0xb13a78: ldur            x0, [fp, #-8]
    // 0xb13a7c: stur            x1, [fp, #-0x50]
    // 0xb13a80: LoadField: r2 = r0->field_1b
    //     0xb13a80: ldur            w2, [x0, #0x1b]
    // 0xb13a84: DecompressPointer r2
    //     0xb13a84: add             x2, x2, HEAP, lsl #32
    // 0xb13a88: stur            x2, [fp, #-0x40]
    // 0xb13a8c: r0 = Text()
    //     0xb13a8c: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb13a90: mov             x1, x0
    // 0xb13a94: ldur            x2, [fp, #-0x40]
    // 0xb13a98: ldur            x3, [fp, #-0x50]
    // 0xb13a9c: stur            x0, [fp, #-8]
    // 0xb13aa0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb13aa0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb13aa4: r0 = Text()
    //     0xb13aa4: bl              #0xb135fc  ; [package:pdf/src/widgets/text.dart] Text::Text
    // 0xb13aa8: r1 = Null
    //     0xb13aa8: mov             x1, NULL
    // 0xb13aac: r2 = 10
    //     0xb13aac: movz            x2, #0xa
    // 0xb13ab0: r0 = AllocateArray()
    //     0xb13ab0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb13ab4: mov             x2, x0
    // 0xb13ab8: ldur            x0, [fp, #-0x20]
    // 0xb13abc: stur            x2, [fp, #-0x40]
    // 0xb13ac0: StoreField: r2->field_f = r0
    //     0xb13ac0: stur            w0, [x2, #0xf]
    // 0xb13ac4: ldur            x0, [fp, #-0x28]
    // 0xb13ac8: StoreField: r2->field_13 = r0
    //     0xb13ac8: stur            w0, [x2, #0x13]
    // 0xb13acc: ldur            x0, [fp, #-0x30]
    // 0xb13ad0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb13ad0: stur            w0, [x2, #0x17]
    // 0xb13ad4: ldur            x0, [fp, #-0x38]
    // 0xb13ad8: StoreField: r2->field_1b = r0
    //     0xb13ad8: stur            w0, [x2, #0x1b]
    // 0xb13adc: ldur            x0, [fp, #-8]
    // 0xb13ae0: StoreField: r2->field_1f = r0
    //     0xb13ae0: stur            w0, [x2, #0x1f]
    // 0xb13ae4: r1 = <Widget>
    //     0xb13ae4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e250] TypeArguments: <Widget>
    //     0xb13ae8: ldr             x1, [x1, #0x250]
    // 0xb13aec: r0 = AllocateGrowableArray()
    //     0xb13aec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb13af0: mov             x1, x0
    // 0xb13af4: ldur            x0, [fp, #-0x40]
    // 0xb13af8: stur            x1, [fp, #-8]
    // 0xb13afc: StoreField: r1->field_f = r0
    //     0xb13afc: stur            w0, [x1, #0xf]
    // 0xb13b00: r0 = 10
    //     0xb13b00: movz            x0, #0xa
    // 0xb13b04: StoreField: r1->field_b = r0
    //     0xb13b04: stur            w0, [x1, #0xb]
    // 0xb13b08: r0 = Column()
    //     0xb13b08: bl              #0xb13cd4  ; AllocateColumnStub -> Column (size=0x28)
    // 0xb13b0c: mov             x1, x0
    // 0xb13b10: ldur            x2, [fp, #-8]
    // 0xb13b14: r3 = Instance_CrossAxisAlignment
    //     0xb13b14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2e8] Obj!CrossAxisAlignment@e2e8e1
    //     0xb13b18: ldr             x3, [x3, #0x2e8]
    // 0xb13b1c: r5 = Instance_Axis
    //     0xb13b1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e2f0] Obj!Axis@e2e961
    //     0xb13b20: ldr             x5, [x5, #0x2f0]
    // 0xb13b24: stur            x0, [fp, #-8]
    // 0xb13b28: r0 = Flex()
    //     0xb13b28: bl              #0xb13bdc  ; [package:pdf/src/widgets/flex.dart] Flex::Flex
    // 0xb13b2c: r1 = Null
    //     0xb13b2c: mov             x1, NULL
    // 0xb13b30: r2 = 6
    //     0xb13b30: movz            x2, #0x6
    // 0xb13b34: r0 = AllocateArray()
    //     0xb13b34: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb13b38: mov             x2, x0
    // 0xb13b3c: ldur            x0, [fp, #-0x10]
    // 0xb13b40: stur            x2, [fp, #-0x20]
    // 0xb13b44: StoreField: r2->field_f = r0
    //     0xb13b44: stur            w0, [x2, #0xf]
    // 0xb13b48: ldur            x0, [fp, #-0x18]
    // 0xb13b4c: StoreField: r2->field_13 = r0
    //     0xb13b4c: stur            w0, [x2, #0x13]
    // 0xb13b50: ldur            x0, [fp, #-8]
    // 0xb13b54: ArrayStore: r2[0] = r0  ; List_4
    //     0xb13b54: stur            w0, [x2, #0x17]
    // 0xb13b58: r1 = <Widget>
    //     0xb13b58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e250] TypeArguments: <Widget>
    //     0xb13b5c: ldr             x1, [x1, #0x250]
    // 0xb13b60: r0 = AllocateGrowableArray()
    //     0xb13b60: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb13b64: mov             x1, x0
    // 0xb13b68: ldur            x0, [fp, #-0x20]
    // 0xb13b6c: stur            x1, [fp, #-8]
    // 0xb13b70: StoreField: r1->field_f = r0
    //     0xb13b70: stur            w0, [x1, #0xf]
    // 0xb13b74: r0 = 6
    //     0xb13b74: movz            x0, #0x6
    // 0xb13b78: StoreField: r1->field_b = r0
    //     0xb13b78: stur            w0, [x1, #0xb]
    // 0xb13b7c: r0 = Row()
    //     0xb13b7c: bl              #0xb13bd0  ; AllocateRowStub -> Row (size=0x28)
    // 0xb13b80: mov             x1, x0
    // 0xb13b84: ldur            x2, [fp, #-8]
    // 0xb13b88: r3 = Instance_CrossAxisAlignment
    //     0xb13b88: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2f8] Obj!CrossAxisAlignment@e2e8c1
    //     0xb13b8c: ldr             x3, [x3, #0x2f8]
    // 0xb13b90: r5 = Instance_Axis
    //     0xb13b90: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e300] Obj!Axis@e2e941
    //     0xb13b94: ldr             x5, [x5, #0x300]
    // 0xb13b98: stur            x0, [fp, #-8]
    // 0xb13b9c: r0 = Flex()
    //     0xb13b9c: bl              #0xb13bdc  ; [package:pdf/src/widgets/flex.dart] Flex::Flex
    // 0xb13ba0: r0 = Padding()
    //     0xb13ba0: bl              #0xb12350  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb13ba4: r1 = Instance_EdgeInsets
    //     0xb13ba4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e308] Obj!EdgeInsets@e0c531
    //     0xb13ba8: ldr             x1, [x1, #0x308]
    // 0xb13bac: StoreField: r0->field_f = r1
    //     0xb13bac: stur            w1, [x0, #0xf]
    // 0xb13bb0: ldur            x1, [fp, #-8]
    // 0xb13bb4: StoreField: r0->field_b = r1
    //     0xb13bb4: stur            w1, [x0, #0xb]
    // 0xb13bb8: LeaveFrame
    //     0xb13bb8: mov             SP, fp
    //     0xb13bbc: ldp             fp, lr, [SP], #0x10
    // 0xb13bc0: ret
    //     0xb13bc0: ret             
    // 0xb13bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb13bc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb13bc8: b               #0xb13894
    // 0xb13bcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb13bcc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb1551c, size: 0x38
    // 0xb1551c: EnterFrame
    //     0xb1551c: stp             fp, lr, [SP, #-0x10]!
    //     0xb15520: mov             fp, SP
    // 0xb15524: CheckStackOverflow
    //     0xb15524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15528: cmp             SP, x16
    //     0xb1552c: b.ls            #0xb1554c
    // 0xb15530: ldr             x1, [fp, #0x10]
    // 0xb15534: r0 = toHijri()
    //     0xb15534: bl              #0x815550  ; [package:nuonline/services/hijri_service.dart] HijriService::toHijri
    // 0xb15538: mov             x1, x0
    // 0xb1553c: r0 = mMMM()
    //     0xb1553c: bl              #0x8ef284  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mMMM
    // 0xb15540: LeaveFrame
    //     0xb15540: mov             SP, fp
    //     0xb15544: ldp             fp, lr, [SP], #0x10
    // 0xb15548: ret
    //     0xb15548: ret             
    // 0xb1554c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1554c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15550: b               #0xb15530
  }
  [closure] String <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb15554, size: 0x30
    // 0xb15554: EnterFrame
    //     0xb15554: stp             fp, lr, [SP, #-0x10]!
    //     0xb15558: mov             fp, SP
    // 0xb1555c: CheckStackOverflow
    //     0xb1555c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15560: cmp             SP, x16
    //     0xb15564: b.ls            #0xb1557c
    // 0xb15568: ldr             x1, [fp, #0x10]
    // 0xb1556c: r0 = DateTimeExtensions.mMMM()
    //     0xb1556c: bl              #0xb15584  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.mMMM
    // 0xb15570: LeaveFrame
    //     0xb15570: mov             SP, fp
    //     0xb15574: ldp             fp, lr, [SP], #0x10
    // 0xb15578: ret
    //     0xb15578: ret             
    // 0xb1557c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1557c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15580: b               #0xb15568
  }
  [closure] String <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xb15600, size: 0x6c
    // 0xb15600: EnterFrame
    //     0xb15600: stp             fp, lr, [SP, #-0x10]!
    //     0xb15604: mov             fp, SP
    // 0xb15608: ldr             x0, [fp, #0x18]
    // 0xb1560c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1560c: ldur            w1, [x0, #0x17]
    // 0xb15610: DecompressPointer r1
    //     0xb15610: add             x1, x1, HEAP, lsl #32
    // 0xb15614: CheckStackOverflow
    //     0xb15614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15618: cmp             SP, x16
    //     0xb1561c: b.ls            #0xb15664
    // 0xb15620: LoadField: r0 = r1->field_f
    //     0xb15620: ldur            w0, [x1, #0xf]
    // 0xb15624: DecompressPointer r0
    //     0xb15624: add             x0, x0, HEAP, lsl #32
    // 0xb15628: LoadField: r1 = r0->field_b
    //     0xb15628: ldur            w1, [x0, #0xb]
    // 0xb1562c: DecompressPointer r1
    //     0xb1562c: add             x1, x1, HEAP, lsl #32
    // 0xb15630: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb15630: ldur            w0, [x1, #0x17]
    // 0xb15634: DecompressPointer r0
    //     0xb15634: add             x0, x0, HEAP, lsl #32
    // 0xb15638: tbnz            w0, #4, #0xb15648
    // 0xb1563c: ldr             x1, [fp, #0x10]
    // 0xb15640: r0 = DateTimeExtensions.mY()
    //     0xb15640: bl              #0x83a2fc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.mY
    // 0xb15644: b               #0xb15658
    // 0xb15648: ldr             x1, [fp, #0x10]
    // 0xb1564c: r0 = toHijri()
    //     0xb1564c: bl              #0x815550  ; [package:nuonline/services/hijri_service.dart] HijriService::toHijri
    // 0xb15650: mov             x1, x0
    // 0xb15654: r0 = mY()
    //     0xb15654: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0xb15658: LeaveFrame
    //     0xb15658: mov             SP, fp
    //     0xb1565c: ldp             fp, lr, [SP], #0x10
    // 0xb15660: ret
    //     0xb15660: ret             
    // 0xb15664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15664: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15668: b               #0xb15620
  }
}
