// lib: , url: package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart

// class id: 1050435, size: 0x8
class :: {
}

// class id: 4966, size: 0xc, field offset: 0xc
//   const constructor, 
class LoadingIndicator extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba56dc, size: 0xc
    // 0xba56dc: r0 = Instance_Center
    //     0xba56dc: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5c8] Obj!Center@e1e761
    //     0xba56e0: ldr             x0, [x0, #0x5c8]
    // 0xba56e4: ret
    //     0xba56e4: ret             
  }
}

// class id: 4967, size: 0x14, field offset: 0xc
//   const constructor, 
class HorizontalGestureAndDefaultStyle extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba555c, size: 0x180
    // 0xba555c: EnterFrame
    //     0xba555c: stp             fp, lr, [SP, #-0x10]!
    //     0xba5560: mov             fp, SP
    // 0xba5564: AllocStack(0x30)
    //     0xba5564: sub             SP, SP, #0x30
    // 0xba5568: SetupParameters(HorizontalGestureAndDefaultStyle this /* r1 => r1, fp-0x10 */)
    //     0xba5568: stur            x1, [fp, #-0x10]
    // 0xba556c: CheckStackOverflow
    //     0xba556c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba5570: cmp             SP, x16
    //     0xba5574: b.ls            #0xba56d4
    // 0xba5578: LoadField: r0 = r1->field_f
    //     0xba5578: ldur            w0, [x1, #0xf]
    // 0xba557c: DecompressPointer r0
    //     0xba557c: add             x0, x0, HEAP, lsl #32
    // 0xba5580: stur            x0, [fp, #-8]
    // 0xba5584: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba5584: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba5588: ldr             x0, [x0, #0x2670]
    //     0xba558c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba5590: cmp             w0, w16
    //     0xba5594: b.ne            #0xba55a0
    //     0xba5598: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xba559c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xba55a0: r0 = GetNavigation.textTheme()
    //     0xba55a0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xba55a4: LoadField: r1 = r0->field_2f
    //     0xba55a4: ldur            w1, [x0, #0x2f]
    // 0xba55a8: DecompressPointer r1
    //     0xba55a8: add             x1, x1, HEAP, lsl #32
    // 0xba55ac: cmp             w1, NULL
    // 0xba55b0: b.ne            #0xba55bc
    // 0xba55b4: r1 = Null
    //     0xba55b4: mov             x1, NULL
    // 0xba55b8: b               #0xba55c8
    // 0xba55bc: LoadField: r0 = r1->field_b
    //     0xba55bc: ldur            w0, [x1, #0xb]
    // 0xba55c0: DecompressPointer r0
    //     0xba55c0: add             x0, x0, HEAP, lsl #32
    // 0xba55c4: mov             x1, x0
    // 0xba55c8: ldur            x0, [fp, #-0x10]
    // 0xba55cc: stur            x1, [fp, #-0x18]
    // 0xba55d0: r0 = TextStyle()
    //     0xba55d0: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xba55d4: mov             x1, x0
    // 0xba55d8: r0 = true
    //     0xba55d8: add             x0, NULL, #0x20  ; true
    // 0xba55dc: stur            x1, [fp, #-0x20]
    // 0xba55e0: StoreField: r1->field_7 = r0
    //     0xba55e0: stur            w0, [x1, #7]
    // 0xba55e4: ldur            x2, [fp, #-0x18]
    // 0xba55e8: StoreField: r1->field_b = r2
    //     0xba55e8: stur            w2, [x1, #0xb]
    // 0xba55ec: r2 = 24.000000
    //     0xba55ec: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xba55f0: ldr             x2, [x2, #0x368]
    // 0xba55f4: StoreField: r1->field_1f = r2
    //     0xba55f4: stur            w2, [x1, #0x1f]
    // 0xba55f8: r2 = 1.800000
    //     0xba55f8: add             x2, PP, #0x34, lsl #12  ; [pp+0x34da0] 1.8
    //     0xba55fc: ldr             x2, [x2, #0xda0]
    // 0xba5600: StoreField: r1->field_37 = r2
    //     0xba5600: stur            w2, [x1, #0x37]
    // 0xba5604: r2 = "OmarNaskh"
    //     0xba5604: add             x2, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xba5608: ldr             x2, [x2, #0xbc8]
    // 0xba560c: StoreField: r1->field_13 = r2
    //     0xba560c: stur            w2, [x1, #0x13]
    // 0xba5610: ldur            x2, [fp, #-0x10]
    // 0xba5614: LoadField: r3 = r2->field_b
    //     0xba5614: ldur            w3, [x2, #0xb]
    // 0xba5618: DecompressPointer r3
    //     0xba5618: add             x3, x3, HEAP, lsl #32
    // 0xba561c: stur            x3, [fp, #-0x18]
    // 0xba5620: r0 = DefaultTextStyle()
    //     0xba5620: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0xba5624: mov             x1, x0
    // 0xba5628: ldur            x0, [fp, #-0x20]
    // 0xba562c: stur            x1, [fp, #-0x10]
    // 0xba5630: StoreField: r1->field_f = r0
    //     0xba5630: stur            w0, [x1, #0xf]
    // 0xba5634: r0 = true
    //     0xba5634: add             x0, NULL, #0x20  ; true
    // 0xba5638: ArrayStore: r1[0] = r0  ; List_4
    //     0xba5638: stur            w0, [x1, #0x17]
    // 0xba563c: r0 = Instance_TextOverflow
    //     0xba563c: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xba5640: ldr             x0, [x0, #0xc60]
    // 0xba5644: StoreField: r1->field_1b = r0
    //     0xba5644: stur            w0, [x1, #0x1b]
    // 0xba5648: r0 = Instance_TextWidthBasis
    //     0xba5648: add             x0, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xba564c: ldr             x0, [x0, #0x1d8]
    // 0xba5650: StoreField: r1->field_23 = r0
    //     0xba5650: stur            w0, [x1, #0x23]
    // 0xba5654: ldur            x0, [fp, #-0x18]
    // 0xba5658: StoreField: r1->field_b = r0
    //     0xba5658: stur            w0, [x1, #0xb]
    // 0xba565c: r0 = GestureDetector()
    //     0xba565c: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xba5660: stur            x0, [fp, #-0x18]
    // 0xba5664: ldur            x16, [fp, #-8]
    // 0xba5668: ldur            lr, [fp, #-0x10]
    // 0xba566c: stp             lr, x16, [SP]
    // 0xba5670: mov             x1, x0
    // 0xba5674: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onHorizontalDragEnd, 0x1, null]
    //     0xba5674: add             x4, PP, #0x34, lsl #12  ; [pp+0x34da8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onHorizontalDragEnd", 0x1, Null]
    //     0xba5678: ldr             x4, [x4, #0xda8]
    // 0xba567c: r0 = GestureDetector()
    //     0xba567c: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xba5680: r0 = SizedBox()
    //     0xba5680: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba5684: mov             x2, x0
    // 0xba5688: r0 = inf
    //     0xba5688: ldr             x0, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xba568c: stur            x2, [fp, #-8]
    // 0xba5690: StoreField: r2->field_f = r0
    //     0xba5690: stur            w0, [x2, #0xf]
    // 0xba5694: StoreField: r2->field_13 = r0
    //     0xba5694: stur            w0, [x2, #0x13]
    // 0xba5698: ldur            x0, [fp, #-0x18]
    // 0xba569c: StoreField: r2->field_b = r0
    //     0xba569c: stur            w0, [x2, #0xb]
    // 0xba56a0: r1 = <FlexParentData>
    //     0xba56a0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xba56a4: ldr             x1, [x1, #0x720]
    // 0xba56a8: r0 = Expanded()
    //     0xba56a8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba56ac: r1 = 1
    //     0xba56ac: movz            x1, #0x1
    // 0xba56b0: StoreField: r0->field_13 = r1
    //     0xba56b0: stur            x1, [x0, #0x13]
    // 0xba56b4: r1 = Instance_FlexFit
    //     0xba56b4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xba56b8: ldr             x1, [x1, #0x728]
    // 0xba56bc: StoreField: r0->field_1b = r1
    //     0xba56bc: stur            w1, [x0, #0x1b]
    // 0xba56c0: ldur            x1, [fp, #-8]
    // 0xba56c4: StoreField: r0->field_b = r1
    //     0xba56c4: stur            w1, [x0, #0xb]
    // 0xba56c8: LeaveFrame
    //     0xba56c8: mov             SP, fp
    //     0xba56cc: ldp             fp, lr, [SP], #0x10
    // 0xba56d0: ret
    //     0xba56d0: ret             
    // 0xba56d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba56d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba56d8: b               #0xba5578
  }
}

// class id: 4968, size: 0x10, field offset: 0xc
//   const constructor, 
class ErrorMessage extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba5474, size: 0xe8
    // 0xba5474: EnterFrame
    //     0xba5474: stp             fp, lr, [SP, #-0x10]!
    //     0xba5478: mov             fp, SP
    // 0xba547c: AllocStack(0x30)
    //     0xba547c: sub             SP, SP, #0x30
    // 0xba5480: CheckStackOverflow
    //     0xba5480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba5484: cmp             SP, x16
    //     0xba5488: b.ls            #0xba5554
    // 0xba548c: LoadField: r0 = r1->field_b
    //     0xba548c: ldur            w0, [x1, #0xb]
    // 0xba5490: DecompressPointer r0
    //     0xba5490: add             x0, x0, HEAP, lsl #32
    // 0xba5494: stur            x0, [fp, #-8]
    // 0xba5498: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba5498: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba549c: ldr             x0, [x0, #0x2670]
    //     0xba54a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba54a4: cmp             w0, w16
    //     0xba54a8: b.ne            #0xba54b4
    //     0xba54ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xba54b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xba54b4: r0 = GetNavigation.textTheme()
    //     0xba54b4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xba54b8: LoadField: r3 = r0->field_27
    //     0xba54b8: ldur            w3, [x0, #0x27]
    // 0xba54bc: DecompressPointer r3
    //     0xba54bc: add             x3, x3, HEAP, lsl #32
    // 0xba54c0: stur            x3, [fp, #-0x10]
    // 0xba54c4: cmp             w3, NULL
    // 0xba54c8: b.ne            #0xba54d4
    // 0xba54cc: r1 = Null
    //     0xba54cc: mov             x1, NULL
    // 0xba54d0: b               #0xba552c
    // 0xba54d4: r1 = _ConstMap len:3
    //     0xba54d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xba54d8: ldr             x1, [x1, #0xcd0]
    // 0xba54dc: r2 = 6
    //     0xba54dc: movz            x2, #0x6
    // 0xba54e0: r0 = []()
    //     0xba54e0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba54e4: r1 = _ConstMap len:3
    //     0xba54e4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xba54e8: ldr             x1, [x1, #0xcd0]
    // 0xba54ec: r2 = 4
    //     0xba54ec: movz            x2, #0x4
    // 0xba54f0: stur            x0, [fp, #-0x18]
    // 0xba54f4: r0 = []()
    //     0xba54f4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba54f8: r16 = <Color?>
    //     0xba54f8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba54fc: ldr             x16, [x16, #0x98]
    // 0xba5500: stp             x0, x16, [SP, #8]
    // 0xba5504: ldur            x16, [fp, #-0x18]
    // 0xba5508: str             x16, [SP]
    // 0xba550c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba550c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba5510: r0 = mode()
    //     0xba5510: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba5514: str             x0, [SP]
    // 0xba5518: ldur            x1, [fp, #-0x10]
    // 0xba551c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xba551c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xba5520: ldr             x4, [x4, #0x228]
    // 0xba5524: r0 = copyWith()
    //     0xba5524: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba5528: mov             x1, x0
    // 0xba552c: ldur            x0, [fp, #-8]
    // 0xba5530: stur            x1, [fp, #-0x10]
    // 0xba5534: r0 = Text()
    //     0xba5534: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xba5538: ldur            x1, [fp, #-8]
    // 0xba553c: StoreField: r0->field_b = r1
    //     0xba553c: stur            w1, [x0, #0xb]
    // 0xba5540: ldur            x1, [fp, #-0x10]
    // 0xba5544: StoreField: r0->field_13 = r1
    //     0xba5544: stur            w1, [x0, #0x13]
    // 0xba5548: LeaveFrame
    //     0xba5548: mov             SP, fp
    //     0xba554c: ldp             fp, lr, [SP], #0x10
    // 0xba5550: ret
    //     0xba5550: ret             
    // 0xba5554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba5554: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba5558: b               #0xba548c
  }
}

// class id: 4969, size: 0xc, field offset: 0xc
//   const constructor, 
class KhatamButton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba5388, size: 0xec
    // 0xba5388: EnterFrame
    //     0xba5388: stp             fp, lr, [SP, #-0x10]!
    //     0xba538c: mov             fp, SP
    // 0xba5390: AllocStack(0x28)
    //     0xba5390: sub             SP, SP, #0x28
    // 0xba5394: CheckStackOverflow
    //     0xba5394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba5398: cmp             SP, x16
    //     0xba539c: b.ls            #0xba546c
    // 0xba53a0: r1 = _ConstMap len:10
    //     0xba53a0: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xba53a4: ldr             x1, [x1, #0x6c8]
    // 0xba53a8: r2 = 600
    //     0xba53a8: movz            x2, #0x258
    // 0xba53ac: r0 = []()
    //     0xba53ac: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba53b0: r16 = <Color?>
    //     0xba53b0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba53b4: ldr             x16, [x16, #0x98]
    // 0xba53b8: stp             x0, x16, [SP, #8]
    // 0xba53bc: r16 = Instance_MaterialColor
    //     0xba53bc: add             x16, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xba53c0: ldr             x16, [x16, #0x6d0]
    // 0xba53c4: str             x16, [SP]
    // 0xba53c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba53c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba53cc: r0 = mode()
    //     0xba53cc: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba53d0: stur            x0, [fp, #-8]
    // 0xba53d4: r0 = Icon()
    //     0xba53d4: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xba53d8: mov             x1, x0
    // 0xba53dc: r0 = Instance_IconData
    //     0xba53dc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34db0] Obj!IconData@e10a71
    //     0xba53e0: ldr             x0, [x0, #0xdb0]
    // 0xba53e4: stur            x1, [fp, #-0x10]
    // 0xba53e8: StoreField: r1->field_b = r0
    //     0xba53e8: stur            w0, [x1, #0xb]
    // 0xba53ec: r0 = 24.000000
    //     0xba53ec: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xba53f0: ldr             x0, [x0, #0x368]
    // 0xba53f4: StoreField: r1->field_f = r0
    //     0xba53f4: stur            w0, [x1, #0xf]
    // 0xba53f8: ldur            x0, [fp, #-8]
    // 0xba53fc: StoreField: r1->field_23 = r0
    //     0xba53fc: stur            w0, [x1, #0x23]
    // 0xba5400: r0 = NCardListHeader()
    //     0xba5400: bl              #0xadee68  ; AllocateNCardListHeaderStub -> NCardListHeader (size=0x2c)
    // 0xba5404: mov             x3, x0
    // 0xba5408: r0 = "Doa Khataman Al-Qur\'an"
    //     0xba5408: add             x0, PP, #0x34, lsl #12  ; [pp+0x34db8] "Doa Khataman Al-Qur\'an"
    //     0xba540c: ldr             x0, [x0, #0xdb8]
    // 0xba5410: stur            x3, [fp, #-8]
    // 0xba5414: StoreField: r3->field_f = r0
    //     0xba5414: stur            w0, [x3, #0xf]
    // 0xba5418: ldur            x0, [fp, #-0x10]
    // 0xba541c: StoreField: r3->field_b = r0
    //     0xba541c: stur            w0, [x3, #0xb]
    // 0xba5420: r1 = Function '<anonymous closure>':.
    //     0xba5420: add             x1, PP, #0x34, lsl #12  ; [pp+0x34dc0] AnonymousClosure: (0xb2557c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xba5424: ldr             x1, [x1, #0xdc0]
    // 0xba5428: r2 = Null
    //     0xba5428: mov             x2, NULL
    // 0xba542c: r0 = AllocateClosure()
    //     0xba542c: bl              #0xec1630  ; AllocateClosureStub
    // 0xba5430: mov             x1, x0
    // 0xba5434: ldur            x0, [fp, #-8]
    // 0xba5438: StoreField: r0->field_1b = r1
    //     0xba5438: stur            w1, [x0, #0x1b]
    // 0xba543c: r1 = false
    //     0xba543c: add             x1, NULL, #0x30  ; false
    // 0xba5440: StoreField: r0->field_1f = r1
    //     0xba5440: stur            w1, [x0, #0x1f]
    // 0xba5444: StoreField: r0->field_23 = r1
    //     0xba5444: stur            w1, [x0, #0x23]
    // 0xba5448: r0 = Padding()
    //     0xba5448: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba544c: r1 = Instance_EdgeInsets
    //     0xba544c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32e08] Obj!EdgeInsets@e12ca1
    //     0xba5450: ldr             x1, [x1, #0xe08]
    // 0xba5454: StoreField: r0->field_f = r1
    //     0xba5454: stur            w1, [x0, #0xf]
    // 0xba5458: ldur            x1, [fp, #-8]
    // 0xba545c: StoreField: r0->field_b = r1
    //     0xba545c: stur            w1, [x0, #0xb]
    // 0xba5460: LeaveFrame
    //     0xba5460: mov             SP, fp
    //     0xba5464: ldp             fp, lr, [SP], #0x10
    // 0xba5468: ret
    //     0xba5468: ret             
    // 0xba546c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba546c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba5470: b               #0xba53a0
  }
}

// class id: 4970, size: 0x14, field offset: 0xc
//   const constructor, 
class JuzDivider extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba5154, size: 0x234
    // 0xba5154: EnterFrame
    //     0xba5154: stp             fp, lr, [SP, #-0x10]!
    //     0xba5158: mov             fp, SP
    // 0xba515c: AllocStack(0x38)
    //     0xba515c: sub             SP, SP, #0x38
    // 0xba5160: SetupParameters(JuzDivider this /* r1 => r0, fp-0x8 */)
    //     0xba5160: mov             x0, x1
    //     0xba5164: stur            x1, [fp, #-8]
    // 0xba5168: CheckStackOverflow
    //     0xba5168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba516c: cmp             SP, x16
    //     0xba5170: b.ls            #0xba5378
    // 0xba5174: r1 = _ConstMap len:10
    //     0xba5174: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xba5178: ldr             x1, [x1, #0xc08]
    // 0xba517c: r2 = 100
    //     0xba517c: movz            x2, #0x64
    // 0xba5180: r0 = []()
    //     0xba5180: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba5184: r1 = _ConstMap len:10
    //     0xba5184: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xba5188: ldr             x1, [x1, #0xc08]
    // 0xba518c: r2 = 1200
    //     0xba518c: movz            x2, #0x4b0
    // 0xba5190: stur            x0, [fp, #-0x10]
    // 0xba5194: r0 = []()
    //     0xba5194: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba5198: r16 = <Color?>
    //     0xba5198: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba519c: ldr             x16, [x16, #0x98]
    // 0xba51a0: stp             x0, x16, [SP, #8]
    // 0xba51a4: ldur            x16, [fp, #-0x10]
    // 0xba51a8: str             x16, [SP]
    // 0xba51ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba51ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba51b0: r0 = mode()
    //     0xba51b0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba51b4: r1 = _ConstMap len:10
    //     0xba51b4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xba51b8: ldr             x1, [x1, #0xc08]
    // 0xba51bc: r2 = 400
    //     0xba51bc: movz            x2, #0x190
    // 0xba51c0: stur            x0, [fp, #-0x10]
    // 0xba51c4: r0 = []()
    //     0xba51c4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba51c8: stur            x0, [fp, #-0x18]
    // 0xba51cc: cmp             w0, NULL
    // 0xba51d0: b.eq            #0xba5380
    // 0xba51d4: r1 = _ConstMap len:10
    //     0xba51d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xba51d8: ldr             x1, [x1, #0xc08]
    // 0xba51dc: r2 = 1600
    //     0xba51dc: movz            x2, #0x640
    // 0xba51e0: r0 = []()
    //     0xba51e0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba51e4: cmp             w0, NULL
    // 0xba51e8: b.eq            #0xba5384
    // 0xba51ec: r16 = <Color>
    //     0xba51ec: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xba51f0: ldr             x16, [x16, #0x158]
    // 0xba51f4: stp             x0, x16, [SP, #8]
    // 0xba51f8: ldur            x16, [fp, #-0x18]
    // 0xba51fc: str             x16, [SP]
    // 0xba5200: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba5200: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba5204: r0 = mode()
    //     0xba5204: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba5208: str             x0, [SP]
    // 0xba520c: r1 = Null
    //     0xba520c: mov             x1, NULL
    // 0xba5210: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xba5210: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xba5214: ldr             x4, [x4, #0x228]
    // 0xba5218: r0 = Border.all()
    //     0xba5218: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xba521c: stur            x0, [fp, #-0x18]
    // 0xba5220: r0 = BoxDecoration()
    //     0xba5220: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xba5224: mov             x3, x0
    // 0xba5228: ldur            x0, [fp, #-0x10]
    // 0xba522c: stur            x3, [fp, #-0x20]
    // 0xba5230: StoreField: r3->field_7 = r0
    //     0xba5230: stur            w0, [x3, #7]
    // 0xba5234: ldur            x0, [fp, #-0x18]
    // 0xba5238: StoreField: r3->field_f = r0
    //     0xba5238: stur            w0, [x3, #0xf]
    // 0xba523c: r0 = Instance_BoxShape
    //     0xba523c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xba5240: ldr             x0, [x0, #0xca8]
    // 0xba5244: StoreField: r3->field_23 = r0
    //     0xba5244: stur            w0, [x3, #0x23]
    // 0xba5248: r1 = Null
    //     0xba5248: mov             x1, NULL
    // 0xba524c: r2 = 4
    //     0xba524c: movz            x2, #0x4
    // 0xba5250: r0 = AllocateArray()
    //     0xba5250: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba5254: mov             x2, x0
    // 0xba5258: r16 = "Juz "
    //     0xba5258: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f3f0] "Juz "
    //     0xba525c: ldr             x16, [x16, #0x3f0]
    // 0xba5260: StoreField: r2->field_f = r16
    //     0xba5260: stur            w16, [x2, #0xf]
    // 0xba5264: ldur            x0, [fp, #-8]
    // 0xba5268: LoadField: r3 = r0->field_b
    //     0xba5268: ldur            x3, [x0, #0xb]
    // 0xba526c: r0 = BoxInt64Instr(r3)
    //     0xba526c: sbfiz           x0, x3, #1, #0x1f
    //     0xba5270: cmp             x3, x0, asr #1
    //     0xba5274: b.eq            #0xba5280
    //     0xba5278: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba527c: stur            x3, [x0, #7]
    // 0xba5280: StoreField: r2->field_13 = r0
    //     0xba5280: stur            w0, [x2, #0x13]
    // 0xba5284: str             x2, [SP]
    // 0xba5288: r0 = _interpolate()
    //     0xba5288: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xba528c: stur            x0, [fp, #-8]
    // 0xba5290: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba5290: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba5294: ldr             x0, [x0, #0x2670]
    //     0xba5298: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba529c: cmp             w0, w16
    //     0xba52a0: b.ne            #0xba52ac
    //     0xba52a4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xba52a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xba52ac: r0 = GetNavigation.textTheme()
    //     0xba52ac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xba52b0: LoadField: r1 = r0->field_2f
    //     0xba52b0: ldur            w1, [x0, #0x2f]
    // 0xba52b4: DecompressPointer r1
    //     0xba52b4: add             x1, x1, HEAP, lsl #32
    // 0xba52b8: cmp             w1, NULL
    // 0xba52bc: b.ne            #0xba52c8
    // 0xba52c0: r2 = Null
    //     0xba52c0: mov             x2, NULL
    // 0xba52c4: b               #0xba52e4
    // 0xba52c8: r16 = 16.000000
    //     0xba52c8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xba52cc: ldr             x16, [x16, #0x80]
    // 0xba52d0: str             x16, [SP]
    // 0xba52d4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xba52d4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xba52d8: ldr             x4, [x4, #0x88]
    // 0xba52dc: r0 = copyWith()
    //     0xba52dc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba52e0: mov             x2, x0
    // 0xba52e4: ldur            x1, [fp, #-0x20]
    // 0xba52e8: ldur            x0, [fp, #-8]
    // 0xba52ec: stur            x2, [fp, #-0x10]
    // 0xba52f0: r0 = Text()
    //     0xba52f0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xba52f4: mov             x1, x0
    // 0xba52f8: ldur            x0, [fp, #-8]
    // 0xba52fc: stur            x1, [fp, #-0x18]
    // 0xba5300: StoreField: r1->field_b = r0
    //     0xba5300: stur            w0, [x1, #0xb]
    // 0xba5304: ldur            x0, [fp, #-0x10]
    // 0xba5308: StoreField: r1->field_13 = r0
    //     0xba5308: stur            w0, [x1, #0x13]
    // 0xba530c: r0 = Padding()
    //     0xba530c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba5310: mov             x1, x0
    // 0xba5314: r0 = Instance_EdgeInsets
    //     0xba5314: add             x0, PP, #0x34, lsl #12  ; [pp+0x34d98] Obj!EdgeInsets@e12491
    //     0xba5318: ldr             x0, [x0, #0xd98]
    // 0xba531c: stur            x1, [fp, #-8]
    // 0xba5320: StoreField: r1->field_f = r0
    //     0xba5320: stur            w0, [x1, #0xf]
    // 0xba5324: ldur            x0, [fp, #-0x18]
    // 0xba5328: StoreField: r1->field_b = r0
    //     0xba5328: stur            w0, [x1, #0xb]
    // 0xba532c: r0 = Center()
    //     0xba532c: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xba5330: mov             x1, x0
    // 0xba5334: r0 = Instance_Alignment
    //     0xba5334: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xba5338: ldr             x0, [x0, #0x898]
    // 0xba533c: stur            x1, [fp, #-0x10]
    // 0xba5340: StoreField: r1->field_f = r0
    //     0xba5340: stur            w0, [x1, #0xf]
    // 0xba5344: ldur            x0, [fp, #-8]
    // 0xba5348: StoreField: r1->field_b = r0
    //     0xba5348: stur            w0, [x1, #0xb]
    // 0xba534c: r0 = DecoratedBox()
    //     0xba534c: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xba5350: ldur            x1, [fp, #-0x20]
    // 0xba5354: StoreField: r0->field_f = r1
    //     0xba5354: stur            w1, [x0, #0xf]
    // 0xba5358: r1 = Instance_DecorationPosition
    //     0xba5358: add             x1, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xba535c: ldr             x1, [x1, #0xb28]
    // 0xba5360: StoreField: r0->field_13 = r1
    //     0xba5360: stur            w1, [x0, #0x13]
    // 0xba5364: ldur            x1, [fp, #-0x10]
    // 0xba5368: StoreField: r0->field_b = r1
    //     0xba5368: stur            w1, [x0, #0xb]
    // 0xba536c: LeaveFrame
    //     0xba536c: mov             SP, fp
    //     0xba5370: ldp             fp, lr, [SP], #0x10
    // 0xba5374: ret
    //     0xba5374: ret             
    // 0xba5378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba5378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba537c: b               #0xba5174
    // 0xba5380: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5380: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba5384: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba5384: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5236, size: 0x14, field offset: 0x14
class QuranListView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb1bf24, size: 0x59c
    // 0xb1bf24: EnterFrame
    //     0xb1bf24: stp             fp, lr, [SP, #-0x10]!
    //     0xb1bf28: mov             fp, SP
    // 0xb1bf2c: AllocStack(0x58)
    //     0xb1bf2c: sub             SP, SP, #0x58
    // 0xb1bf30: SetupParameters(QuranListView this /* r1 => r1, fp-0x8 */)
    //     0xb1bf30: stur            x1, [fp, #-8]
    // 0xb1bf34: CheckStackOverflow
    //     0xb1bf34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1bf38: cmp             SP, x16
    //     0xb1bf3c: b.ls            #0xb1c4b8
    // 0xb1bf40: r1 = 1
    //     0xb1bf40: movz            x1, #0x1
    // 0xb1bf44: r0 = AllocateContext()
    //     0xb1bf44: bl              #0xec126c  ; AllocateContextStub
    // 0xb1bf48: mov             x3, x0
    // 0xb1bf4c: ldur            x0, [fp, #-8]
    // 0xb1bf50: stur            x3, [fp, #-0x10]
    // 0xb1bf54: StoreField: r3->field_f = r0
    //     0xb1bf54: stur            w0, [x3, #0xf]
    // 0xb1bf58: r1 = _ConstMap len:10
    //     0xb1bf58: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb1bf5c: ldr             x1, [x1, #0xc08]
    // 0xb1bf60: r2 = 1200
    //     0xb1bf60: movz            x2, #0x4b0
    // 0xb1bf64: r0 = []()
    //     0xb1bf64: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb1bf68: r1 = _ConstMap len:3
    //     0xb1bf68: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb1bf6c: ldr             x1, [x1, #0xbe8]
    // 0xb1bf70: r2 = 4
    //     0xb1bf70: movz            x2, #0x4
    // 0xb1bf74: stur            x0, [fp, #-0x18]
    // 0xb1bf78: r0 = []()
    //     0xb1bf78: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb1bf7c: r16 = <Color?>
    //     0xb1bf7c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb1bf80: ldr             x16, [x16, #0x98]
    // 0xb1bf84: stp             x0, x16, [SP, #8]
    // 0xb1bf88: ldur            x16, [fp, #-0x18]
    // 0xb1bf8c: str             x16, [SP]
    // 0xb1bf90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1bf90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1bf94: r0 = mode()
    //     0xb1bf94: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb1bf98: stur            x0, [fp, #-0x18]
    // 0xb1bf9c: r0 = Radius()
    //     0xb1bf9c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1bfa0: d0 = 4.000000
    //     0xb1bfa0: fmov            d0, #4.00000000
    // 0xb1bfa4: stur            x0, [fp, #-0x20]
    // 0xb1bfa8: StoreField: r0->field_7 = d0
    //     0xb1bfa8: stur            d0, [x0, #7]
    // 0xb1bfac: StoreField: r0->field_f = d0
    //     0xb1bfac: stur            d0, [x0, #0xf]
    // 0xb1bfb0: r0 = BorderRadius()
    //     0xb1bfb0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1bfb4: mov             x2, x0
    // 0xb1bfb8: ldur            x0, [fp, #-0x20]
    // 0xb1bfbc: stur            x2, [fp, #-0x28]
    // 0xb1bfc0: StoreField: r2->field_7 = r0
    //     0xb1bfc0: stur            w0, [x2, #7]
    // 0xb1bfc4: StoreField: r2->field_b = r0
    //     0xb1bfc4: stur            w0, [x2, #0xb]
    // 0xb1bfc8: StoreField: r2->field_f = r0
    //     0xb1bfc8: stur            w0, [x2, #0xf]
    // 0xb1bfcc: StoreField: r2->field_13 = r0
    //     0xb1bfcc: stur            w0, [x2, #0x13]
    // 0xb1bfd0: r1 = Instance_Color
    //     0xb1bfd0: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb1bfd4: d0 = 0.500000
    //     0xb1bfd4: fmov            d0, #0.50000000
    // 0xb1bfd8: r0 = withOpacity()
    //     0xb1bfd8: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xb1bfdc: str             x0, [SP]
    // 0xb1bfe0: r1 = Null
    //     0xb1bfe0: mov             x1, NULL
    // 0xb1bfe4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb1bfe4: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb1bfe8: ldr             x4, [x4, #0x228]
    // 0xb1bfec: r0 = Border.all()
    //     0xb1bfec: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb1bff0: stur            x0, [fp, #-0x20]
    // 0xb1bff4: r0 = BoxDecoration()
    //     0xb1bff4: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1bff8: mov             x2, x0
    // 0xb1bffc: ldur            x0, [fp, #-0x18]
    // 0xb1c000: stur            x2, [fp, #-0x30]
    // 0xb1c004: StoreField: r2->field_7 = r0
    //     0xb1c004: stur            w0, [x2, #7]
    // 0xb1c008: ldur            x0, [fp, #-0x20]
    // 0xb1c00c: StoreField: r2->field_f = r0
    //     0xb1c00c: stur            w0, [x2, #0xf]
    // 0xb1c010: ldur            x0, [fp, #-0x28]
    // 0xb1c014: StoreField: r2->field_13 = r0
    //     0xb1c014: stur            w0, [x2, #0x13]
    // 0xb1c018: r0 = Instance_BoxShape
    //     0xb1c018: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb1c01c: ldr             x0, [x0, #0xca8]
    // 0xb1c020: StoreField: r2->field_23 = r0
    //     0xb1c020: stur            w0, [x2, #0x23]
    // 0xb1c024: ldur            x1, [fp, #-8]
    // 0xb1c028: r0 = controller()
    //     0xb1c028: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1c02c: LoadField: r1 = r0->field_67
    //     0xb1c02c: ldur            w1, [x0, #0x67]
    // 0xb1c030: DecompressPointer r1
    //     0xb1c030: add             x1, x1, HEAP, lsl #32
    // 0xb1c034: LoadField: r0 = r1->field_7
    //     0xb1c034: ldur            w0, [x1, #7]
    // 0xb1c038: DecompressPointer r0
    //     0xb1c038: add             x0, x0, HEAP, lsl #32
    // 0xb1c03c: stur            x0, [fp, #-8]
    // 0xb1c040: r1 = <Iterable<ItemPosition>>
    //     0xb1c040: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f280] TypeArguments: <Iterable<ItemPosition>>
    //     0xb1c044: ldr             x1, [x1, #0x280]
    // 0xb1c048: r0 = ValueListenableBuilder()
    //     0xb1c048: bl              #0xa3091c  ; AllocateValueListenableBuilderStub -> ValueListenableBuilder<X0> (size=0x1c)
    // 0xb1c04c: mov             x3, x0
    // 0xb1c050: ldur            x0, [fp, #-8]
    // 0xb1c054: stur            x3, [fp, #-0x18]
    // 0xb1c058: StoreField: r3->field_f = r0
    //     0xb1c058: stur            w0, [x3, #0xf]
    // 0xb1c05c: ldur            x2, [fp, #-0x10]
    // 0xb1c060: r1 = Function '<anonymous closure>':.
    //     0xb1c060: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f288] AnonymousClosure: (0xb209d0), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c064: ldr             x1, [x1, #0x288]
    // 0xb1c068: r0 = AllocateClosure()
    //     0xb1c068: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c06c: mov             x1, x0
    // 0xb1c070: ldur            x0, [fp, #-0x18]
    // 0xb1c074: StoreField: r0->field_13 = r1
    //     0xb1c074: stur            w1, [x0, #0x13]
    // 0xb1c078: r1 = Null
    //     0xb1c078: mov             x1, NULL
    // 0xb1c07c: r2 = 6
    //     0xb1c07c: movz            x2, #0x6
    // 0xb1c080: r0 = AllocateArray()
    //     0xb1c080: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1c084: mov             x2, x0
    // 0xb1c088: ldur            x0, [fp, #-0x18]
    // 0xb1c08c: stur            x2, [fp, #-8]
    // 0xb1c090: StoreField: r2->field_f = r0
    //     0xb1c090: stur            w0, [x2, #0xf]
    // 0xb1c094: r16 = Instance_SizedBox
    //     0xb1c094: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb1c098: ldr             x16, [x16, #0x538]
    // 0xb1c09c: StoreField: r2->field_13 = r16
    //     0xb1c09c: stur            w16, [x2, #0x13]
    // 0xb1c0a0: r16 = Instance_Icon
    //     0xb1c0a0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0d8] Obj!Icon@e24831
    //     0xb1c0a4: ldr             x16, [x16, #0xd8]
    // 0xb1c0a8: ArrayStore: r2[0] = r16  ; List_4
    //     0xb1c0a8: stur            w16, [x2, #0x17]
    // 0xb1c0ac: r1 = <Widget>
    //     0xb1c0ac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1c0b0: r0 = AllocateGrowableArray()
    //     0xb1c0b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1c0b4: mov             x1, x0
    // 0xb1c0b8: ldur            x0, [fp, #-8]
    // 0xb1c0bc: stur            x1, [fp, #-0x18]
    // 0xb1c0c0: StoreField: r1->field_f = r0
    //     0xb1c0c0: stur            w0, [x1, #0xf]
    // 0xb1c0c4: r0 = 6
    //     0xb1c0c4: movz            x0, #0x6
    // 0xb1c0c8: StoreField: r1->field_b = r0
    //     0xb1c0c8: stur            w0, [x1, #0xb]
    // 0xb1c0cc: r0 = Row()
    //     0xb1c0cc: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1c0d0: mov             x1, x0
    // 0xb1c0d4: r0 = Instance_Axis
    //     0xb1c0d4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb1c0d8: stur            x1, [fp, #-8]
    // 0xb1c0dc: StoreField: r1->field_f = r0
    //     0xb1c0dc: stur            w0, [x1, #0xf]
    // 0xb1c0e0: r0 = Instance_MainAxisAlignment
    //     0xb1c0e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb1c0e4: ldr             x0, [x0, #0x730]
    // 0xb1c0e8: StoreField: r1->field_13 = r0
    //     0xb1c0e8: stur            w0, [x1, #0x13]
    // 0xb1c0ec: r0 = Instance_MainAxisSize
    //     0xb1c0ec: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb1c0f0: ldr             x0, [x0, #0xe88]
    // 0xb1c0f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1c0f4: stur            w0, [x1, #0x17]
    // 0xb1c0f8: r0 = Instance_CrossAxisAlignment
    //     0xb1c0f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb1c0fc: ldr             x0, [x0, #0x740]
    // 0xb1c100: StoreField: r1->field_1b = r0
    //     0xb1c100: stur            w0, [x1, #0x1b]
    // 0xb1c104: r0 = Instance_VerticalDirection
    //     0xb1c104: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb1c108: ldr             x0, [x0, #0x748]
    // 0xb1c10c: StoreField: r1->field_23 = r0
    //     0xb1c10c: stur            w0, [x1, #0x23]
    // 0xb1c110: r0 = Instance_Clip
    //     0xb1c110: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb1c114: ldr             x0, [x0, #0x750]
    // 0xb1c118: StoreField: r1->field_2b = r0
    //     0xb1c118: stur            w0, [x1, #0x2b]
    // 0xb1c11c: StoreField: r1->field_2f = rZR
    //     0xb1c11c: stur            xzr, [x1, #0x2f]
    // 0xb1c120: ldur            x0, [fp, #-0x18]
    // 0xb1c124: StoreField: r1->field_b = r0
    //     0xb1c124: stur            w0, [x1, #0xb]
    // 0xb1c128: r0 = Container()
    //     0xb1c128: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1c12c: stur            x0, [fp, #-0x18]
    // 0xb1c130: r16 = Instance_EdgeInsets
    //     0xb1c130: add             x16, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0xb1c134: ldr             x16, [x16, #0xde8]
    // 0xb1c138: ldur            lr, [fp, #-0x30]
    // 0xb1c13c: stp             lr, x16, [SP, #8]
    // 0xb1c140: ldur            x16, [fp, #-8]
    // 0xb1c144: str             x16, [SP]
    // 0xb1c148: mov             x1, x0
    // 0xb1c14c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb1c14c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb1c150: ldr             x4, [x4, #0xe8]
    // 0xb1c154: r0 = Container()
    //     0xb1c154: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1c158: r0 = InkWell()
    //     0xb1c158: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb1c15c: mov             x3, x0
    // 0xb1c160: ldur            x0, [fp, #-0x18]
    // 0xb1c164: stur            x3, [fp, #-8]
    // 0xb1c168: StoreField: r3->field_b = r0
    //     0xb1c168: stur            w0, [x3, #0xb]
    // 0xb1c16c: ldur            x2, [fp, #-0x10]
    // 0xb1c170: r1 = Function '<anonymous closure>':.
    //     0xb1c170: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f290] AnonymousClosure: (0xb1f754), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c174: ldr             x1, [x1, #0x290]
    // 0xb1c178: r0 = AllocateClosure()
    //     0xb1c178: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c17c: mov             x1, x0
    // 0xb1c180: ldur            x0, [fp, #-8]
    // 0xb1c184: StoreField: r0->field_f = r1
    //     0xb1c184: stur            w1, [x0, #0xf]
    // 0xb1c188: r3 = true
    //     0xb1c188: add             x3, NULL, #0x20  ; true
    // 0xb1c18c: StoreField: r0->field_43 = r3
    //     0xb1c18c: stur            w3, [x0, #0x43]
    // 0xb1c190: r1 = Instance_BoxShape
    //     0xb1c190: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb1c194: ldr             x1, [x1, #0xca8]
    // 0xb1c198: StoreField: r0->field_47 = r1
    //     0xb1c198: stur            w1, [x0, #0x47]
    // 0xb1c19c: StoreField: r0->field_6f = r3
    //     0xb1c19c: stur            w3, [x0, #0x6f]
    // 0xb1c1a0: r4 = false
    //     0xb1c1a0: add             x4, NULL, #0x30  ; false
    // 0xb1c1a4: StoreField: r0->field_73 = r4
    //     0xb1c1a4: stur            w4, [x0, #0x73]
    // 0xb1c1a8: StoreField: r0->field_83 = r3
    //     0xb1c1a8: stur            w3, [x0, #0x83]
    // 0xb1c1ac: StoreField: r0->field_7b = r4
    //     0xb1c1ac: stur            w4, [x0, #0x7b]
    // 0xb1c1b0: r1 = Function '<anonymous closure>':.
    //     0xb1c1b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f298] AnonymousClosure: (0xb1f6f8), in [package:nuonline/app/modules/yasin/views/yasin_view.dart] YasinView::build (0xb60fa8)
    //     0xb1c1b4: ldr             x1, [x1, #0x298]
    // 0xb1c1b8: r2 = Null
    //     0xb1c1b8: mov             x2, NULL
    // 0xb1c1bc: r0 = AllocateClosure()
    //     0xb1c1bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c1c0: stur            x0, [fp, #-0x18]
    // 0xb1c1c4: r0 = IconButton()
    //     0xb1c1c4: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1c1c8: mov             x1, x0
    // 0xb1c1cc: ldur            x0, [fp, #-0x18]
    // 0xb1c1d0: stur            x1, [fp, #-0x20]
    // 0xb1c1d4: StoreField: r1->field_3b = r0
    //     0xb1c1d4: stur            w0, [x1, #0x3b]
    // 0xb1c1d8: r0 = false
    //     0xb1c1d8: add             x0, NULL, #0x30  ; false
    // 0xb1c1dc: StoreField: r1->field_47 = r0
    //     0xb1c1dc: stur            w0, [x1, #0x47]
    // 0xb1c1e0: r2 = Instance_Icon
    //     0xb1c1e0: add             x2, PP, #0x27, lsl #12  ; [pp+0x27c78] Obj!Icon@e24631
    //     0xb1c1e4: ldr             x2, [x2, #0xc78]
    // 0xb1c1e8: StoreField: r1->field_1f = r2
    //     0xb1c1e8: stur            w2, [x1, #0x1f]
    // 0xb1c1ec: r2 = Instance__IconButtonVariant
    //     0xb1c1ec: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1c1f0: ldr             x2, [x2, #0xf78]
    // 0xb1c1f4: StoreField: r1->field_63 = r2
    //     0xb1c1f4: stur            w2, [x1, #0x63]
    // 0xb1c1f8: r0 = SizedBox()
    //     0xb1c1f8: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1c1fc: mov             x1, x0
    // 0xb1c200: r0 = 36.000000
    //     0xb1c200: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb1c204: ldr             x0, [x0, #0x478]
    // 0xb1c208: stur            x1, [fp, #-0x18]
    // 0xb1c20c: StoreField: r1->field_f = r0
    //     0xb1c20c: stur            w0, [x1, #0xf]
    // 0xb1c210: ldur            x2, [fp, #-0x20]
    // 0xb1c214: StoreField: r1->field_b = r2
    //     0xb1c214: stur            w2, [x1, #0xb]
    // 0xb1c218: r0 = Obx()
    //     0xb1c218: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb1c21c: ldur            x2, [fp, #-0x10]
    // 0xb1c220: r1 = Function '<anonymous closure>':.
    //     0xb1c220: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2a0] AnonymousClosure: (0xb1f480), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c224: ldr             x1, [x1, #0x2a0]
    // 0xb1c228: stur            x0, [fp, #-0x20]
    // 0xb1c22c: r0 = AllocateClosure()
    //     0xb1c22c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c230: mov             x1, x0
    // 0xb1c234: ldur            x0, [fp, #-0x20]
    // 0xb1c238: StoreField: r0->field_b = r1
    //     0xb1c238: stur            w1, [x0, #0xb]
    // 0xb1c23c: ldur            x2, [fp, #-0x10]
    // 0xb1c240: r1 = Function '<anonymous closure>':.
    //     0xb1c240: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2a8] AnonymousClosure: (0xb1f2f4), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c244: ldr             x1, [x1, #0x2a8]
    // 0xb1c248: r0 = AllocateClosure()
    //     0xb1c248: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c24c: stur            x0, [fp, #-0x28]
    // 0xb1c250: r0 = IconButton()
    //     0xb1c250: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1c254: mov             x1, x0
    // 0xb1c258: ldur            x0, [fp, #-0x28]
    // 0xb1c25c: stur            x1, [fp, #-0x30]
    // 0xb1c260: StoreField: r1->field_3b = r0
    //     0xb1c260: stur            w0, [x1, #0x3b]
    // 0xb1c264: r0 = false
    //     0xb1c264: add             x0, NULL, #0x30  ; false
    // 0xb1c268: StoreField: r1->field_47 = r0
    //     0xb1c268: stur            w0, [x1, #0x47]
    // 0xb1c26c: r2 = Instance_Icon
    //     0xb1c26c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f2b0] Obj!Icon@e24771
    //     0xb1c270: ldr             x2, [x2, #0x2b0]
    // 0xb1c274: StoreField: r1->field_1f = r2
    //     0xb1c274: stur            w2, [x1, #0x1f]
    // 0xb1c278: r2 = Instance__IconButtonVariant
    //     0xb1c278: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1c27c: ldr             x2, [x2, #0xf78]
    // 0xb1c280: StoreField: r1->field_63 = r2
    //     0xb1c280: stur            w2, [x1, #0x63]
    // 0xb1c284: r0 = SizedBox()
    //     0xb1c284: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1c288: mov             x3, x0
    // 0xb1c28c: r0 = 36.000000
    //     0xb1c28c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb1c290: ldr             x0, [x0, #0x478]
    // 0xb1c294: stur            x3, [fp, #-0x28]
    // 0xb1c298: StoreField: r3->field_f = r0
    //     0xb1c298: stur            w0, [x3, #0xf]
    // 0xb1c29c: ldur            x1, [fp, #-0x30]
    // 0xb1c2a0: StoreField: r3->field_b = r1
    //     0xb1c2a0: stur            w1, [x3, #0xb]
    // 0xb1c2a4: r1 = Function '<anonymous closure>':.
    //     0xb1c2a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2b8] AnonymousClosure: (0xb1f298), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb1c2a8: ldr             x1, [x1, #0x2b8]
    // 0xb1c2ac: r2 = Null
    //     0xb1c2ac: mov             x2, NULL
    // 0xb1c2b0: r0 = AllocateClosure()
    //     0xb1c2b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c2b4: stur            x0, [fp, #-0x30]
    // 0xb1c2b8: r0 = IconButton()
    //     0xb1c2b8: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1c2bc: mov             x1, x0
    // 0xb1c2c0: ldur            x0, [fp, #-0x30]
    // 0xb1c2c4: stur            x1, [fp, #-0x38]
    // 0xb1c2c8: StoreField: r1->field_3b = r0
    //     0xb1c2c8: stur            w0, [x1, #0x3b]
    // 0xb1c2cc: r0 = false
    //     0xb1c2cc: add             x0, NULL, #0x30  ; false
    // 0xb1c2d0: StoreField: r1->field_47 = r0
    //     0xb1c2d0: stur            w0, [x1, #0x47]
    // 0xb1c2d4: r2 = Instance_Icon
    //     0xb1c2d4: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be68] Obj!Icon@e247f1
    //     0xb1c2d8: ldr             x2, [x2, #0xe68]
    // 0xb1c2dc: StoreField: r1->field_1f = r2
    //     0xb1c2dc: stur            w2, [x1, #0x1f]
    // 0xb1c2e0: r2 = Instance__IconButtonVariant
    //     0xb1c2e0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1c2e4: ldr             x2, [x2, #0xf78]
    // 0xb1c2e8: StoreField: r1->field_63 = r2
    //     0xb1c2e8: stur            w2, [x1, #0x63]
    // 0xb1c2ec: r0 = SizedBox()
    //     0xb1c2ec: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1c2f0: mov             x3, x0
    // 0xb1c2f4: r0 = 36.000000
    //     0xb1c2f4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb1c2f8: ldr             x0, [x0, #0x478]
    // 0xb1c2fc: stur            x3, [fp, #-0x30]
    // 0xb1c300: StoreField: r3->field_f = r0
    //     0xb1c300: stur            w0, [x3, #0xf]
    // 0xb1c304: ldur            x0, [fp, #-0x38]
    // 0xb1c308: StoreField: r3->field_b = r0
    //     0xb1c308: stur            w0, [x3, #0xb]
    // 0xb1c30c: ldur            x2, [fp, #-0x10]
    // 0xb1c310: r1 = Function '<anonymous closure>':.
    //     0xb1c310: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2c0] AnonymousClosure: (0xb1f0f8), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c314: ldr             x1, [x1, #0x2c0]
    // 0xb1c318: r0 = AllocateClosure()
    //     0xb1c318: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c31c: stur            x0, [fp, #-0x38]
    // 0xb1c320: r0 = IconButton()
    //     0xb1c320: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1c324: mov             x1, x0
    // 0xb1c328: ldur            x0, [fp, #-0x38]
    // 0xb1c32c: stur            x1, [fp, #-0x40]
    // 0xb1c330: StoreField: r1->field_3b = r0
    //     0xb1c330: stur            w0, [x1, #0x3b]
    // 0xb1c334: r0 = false
    //     0xb1c334: add             x0, NULL, #0x30  ; false
    // 0xb1c338: StoreField: r1->field_47 = r0
    //     0xb1c338: stur            w0, [x1, #0x47]
    // 0xb1c33c: r2 = Instance_Icon
    //     0xb1c33c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be78] Obj!Icon@e243f1
    //     0xb1c340: ldr             x2, [x2, #0xe78]
    // 0xb1c344: StoreField: r1->field_1f = r2
    //     0xb1c344: stur            w2, [x1, #0x1f]
    // 0xb1c348: r2 = Instance__IconButtonVariant
    //     0xb1c348: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1c34c: ldr             x2, [x2, #0xf78]
    // 0xb1c350: StoreField: r1->field_63 = r2
    //     0xb1c350: stur            w2, [x1, #0x63]
    // 0xb1c354: r0 = SizedBox()
    //     0xb1c354: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1c358: mov             x3, x0
    // 0xb1c35c: r0 = 42.000000
    //     0xb1c35c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2be80] 42
    //     0xb1c360: ldr             x0, [x0, #0xe80]
    // 0xb1c364: stur            x3, [fp, #-0x38]
    // 0xb1c368: StoreField: r3->field_f = r0
    //     0xb1c368: stur            w0, [x3, #0xf]
    // 0xb1c36c: ldur            x0, [fp, #-0x40]
    // 0xb1c370: StoreField: r3->field_b = r0
    //     0xb1c370: stur            w0, [x3, #0xb]
    // 0xb1c374: r1 = Null
    //     0xb1c374: mov             x1, NULL
    // 0xb1c378: r2 = 10
    //     0xb1c378: movz            x2, #0xa
    // 0xb1c37c: r0 = AllocateArray()
    //     0xb1c37c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1c380: mov             x2, x0
    // 0xb1c384: ldur            x0, [fp, #-0x18]
    // 0xb1c388: stur            x2, [fp, #-0x40]
    // 0xb1c38c: StoreField: r2->field_f = r0
    //     0xb1c38c: stur            w0, [x2, #0xf]
    // 0xb1c390: ldur            x0, [fp, #-0x20]
    // 0xb1c394: StoreField: r2->field_13 = r0
    //     0xb1c394: stur            w0, [x2, #0x13]
    // 0xb1c398: ldur            x0, [fp, #-0x28]
    // 0xb1c39c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1c39c: stur            w0, [x2, #0x17]
    // 0xb1c3a0: ldur            x0, [fp, #-0x30]
    // 0xb1c3a4: StoreField: r2->field_1b = r0
    //     0xb1c3a4: stur            w0, [x2, #0x1b]
    // 0xb1c3a8: ldur            x0, [fp, #-0x38]
    // 0xb1c3ac: StoreField: r2->field_1f = r0
    //     0xb1c3ac: stur            w0, [x2, #0x1f]
    // 0xb1c3b0: r1 = <Widget>
    //     0xb1c3b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1c3b4: r0 = AllocateGrowableArray()
    //     0xb1c3b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1c3b8: mov             x1, x0
    // 0xb1c3bc: ldur            x0, [fp, #-0x40]
    // 0xb1c3c0: stur            x1, [fp, #-0x18]
    // 0xb1c3c4: StoreField: r1->field_f = r0
    //     0xb1c3c4: stur            w0, [x1, #0xf]
    // 0xb1c3c8: r0 = 10
    //     0xb1c3c8: movz            x0, #0xa
    // 0xb1c3cc: StoreField: r1->field_b = r0
    //     0xb1c3cc: stur            w0, [x1, #0xb]
    // 0xb1c3d0: r0 = AppBar()
    //     0xb1c3d0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb1c3d4: stur            x0, [fp, #-0x20]
    // 0xb1c3d8: ldur            x16, [fp, #-8]
    // 0xb1c3dc: ldur            lr, [fp, #-0x18]
    // 0xb1c3e0: stp             lr, x16, [SP]
    // 0xb1c3e4: mov             x1, x0
    // 0xb1c3e8: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xb1c3e8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xb1c3ec: ldr             x4, [x4, #0xf88]
    // 0xb1c3f0: r0 = AppBar()
    //     0xb1c3f0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb1c3f4: ldur            x2, [fp, #-0x10]
    // 0xb1c3f8: r1 = Function '<anonymous closure>':.
    //     0xb1c3f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2c8] AnonymousClosure: (0xb1d114), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c3fc: ldr             x1, [x1, #0x2c8]
    // 0xb1c400: r0 = AllocateClosure()
    //     0xb1c400: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c404: r1 = <ReadingPreferenceController>
    //     0xb1c404: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb1c408: ldr             x1, [x1, #0xe10]
    // 0xb1c40c: stur            x0, [fp, #-8]
    // 0xb1c410: r0 = ReadingPreferenceWidget()
    //     0xb1c410: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb1c414: mov             x1, x0
    // 0xb1c418: ldur            x0, [fp, #-8]
    // 0xb1c41c: stur            x1, [fp, #-0x18]
    // 0xb1c420: StoreField: r1->field_13 = r0
    //     0xb1c420: stur            w0, [x1, #0x13]
    // 0xb1c424: r0 = Scaffold()
    //     0xb1c424: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb1c428: mov             x2, x0
    // 0xb1c42c: ldur            x0, [fp, #-0x20]
    // 0xb1c430: stur            x2, [fp, #-8]
    // 0xb1c434: StoreField: r2->field_13 = r0
    //     0xb1c434: stur            w0, [x2, #0x13]
    // 0xb1c438: ldur            x0, [fp, #-0x18]
    // 0xb1c43c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1c43c: stur            w0, [x2, #0x17]
    // 0xb1c440: r0 = Instance_AlignmentDirectional
    //     0xb1c440: add             x0, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb1c444: ldr             x0, [x0, #0x758]
    // 0xb1c448: StoreField: r2->field_2b = r0
    //     0xb1c448: stur            w0, [x2, #0x2b]
    // 0xb1c44c: r0 = true
    //     0xb1c44c: add             x0, NULL, #0x20  ; true
    // 0xb1c450: StoreField: r2->field_53 = r0
    //     0xb1c450: stur            w0, [x2, #0x53]
    // 0xb1c454: r1 = Instance_DragStartBehavior
    //     0xb1c454: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb1c458: StoreField: r2->field_57 = r1
    //     0xb1c458: stur            w1, [x2, #0x57]
    // 0xb1c45c: r1 = false
    //     0xb1c45c: add             x1, NULL, #0x30  ; false
    // 0xb1c460: StoreField: r2->field_b = r1
    //     0xb1c460: stur            w1, [x2, #0xb]
    // 0xb1c464: StoreField: r2->field_f = r1
    //     0xb1c464: stur            w1, [x2, #0xf]
    // 0xb1c468: StoreField: r2->field_5f = r0
    //     0xb1c468: stur            w0, [x2, #0x5f]
    // 0xb1c46c: StoreField: r2->field_63 = r0
    //     0xb1c46c: stur            w0, [x2, #0x63]
    // 0xb1c470: r1 = <Object>
    //     0xb1c470: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0xb1c474: r0 = PopScope()
    //     0xb1c474: bl              #0xafd0d4  ; AllocatePopScopeStub -> PopScope<X0> (size=0x20)
    // 0xb1c478: mov             x3, x0
    // 0xb1c47c: ldur            x0, [fp, #-8]
    // 0xb1c480: stur            x3, [fp, #-0x18]
    // 0xb1c484: StoreField: r3->field_f = r0
    //     0xb1c484: stur            w0, [x3, #0xf]
    // 0xb1c488: r0 = true
    //     0xb1c488: add             x0, NULL, #0x20  ; true
    // 0xb1c48c: StoreField: r3->field_1b = r0
    //     0xb1c48c: stur            w0, [x3, #0x1b]
    // 0xb1c490: ldur            x2, [fp, #-0x10]
    // 0xb1c494: r1 = Function '<anonymous closure>':.
    //     0xb1c494: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2d0] AnonymousClosure: (0xb1c4c0), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1c498: ldr             x1, [x1, #0x2d0]
    // 0xb1c49c: r0 = AllocateClosure()
    //     0xb1c49c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1c4a0: mov             x1, x0
    // 0xb1c4a4: ldur            x0, [fp, #-0x18]
    // 0xb1c4a8: StoreField: r0->field_13 = r1
    //     0xb1c4a8: stur            w1, [x0, #0x13]
    // 0xb1c4ac: LeaveFrame
    //     0xb1c4ac: mov             SP, fp
    //     0xb1c4b0: ldp             fp, lr, [SP], #0x10
    // 0xb1c4b4: ret
    //     0xb1c4b4: ret             
    // 0xb1c4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1c4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1c4bc: b               #0xb1bf40
  }
  [closure] void <anonymous closure>(dynamic, bool, Object?) {
    // ** addr: 0xb1c4c0, size: 0x50
    // 0xb1c4c0: EnterFrame
    //     0xb1c4c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb1c4c4: mov             fp, SP
    // 0xb1c4c8: ldr             x0, [fp, #0x20]
    // 0xb1c4cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1c4cc: ldur            w1, [x0, #0x17]
    // 0xb1c4d0: DecompressPointer r1
    //     0xb1c4d0: add             x1, x1, HEAP, lsl #32
    // 0xb1c4d4: CheckStackOverflow
    //     0xb1c4d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1c4d8: cmp             SP, x16
    //     0xb1c4dc: b.ls            #0xb1c508
    // 0xb1c4e0: LoadField: r0 = r1->field_f
    //     0xb1c4e0: ldur            w0, [x1, #0xf]
    // 0xb1c4e4: DecompressPointer r0
    //     0xb1c4e4: add             x0, x0, HEAP, lsl #32
    // 0xb1c4e8: mov             x1, x0
    // 0xb1c4ec: r0 = controller()
    //     0xb1c4ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1c4f0: mov             x1, x0
    // 0xb1c4f4: r0 = addLastRead()
    //     0xb1c4f4: bl              #0xb1c510  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::addLastRead
    // 0xb1c4f8: r0 = Null
    //     0xb1c4f8: mov             x0, NULL
    // 0xb1c4fc: LeaveFrame
    //     0xb1c4fc: mov             SP, fp
    //     0xb1c500: ldp             fp, lr, [SP], #0x10
    // 0xb1c504: ret
    //     0xb1c504: ret             
    // 0xb1c508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1c508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1c50c: b               #0xb1c4e0
  }
  [closure] Obx <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb1d114, size: 0x74
    // 0xb1d114: EnterFrame
    //     0xb1d114: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d118: mov             fp, SP
    // 0xb1d11c: AllocStack(0x10)
    //     0xb1d11c: sub             SP, SP, #0x10
    // 0xb1d120: SetupParameters()
    //     0xb1d120: ldr             x0, [fp, #0x20]
    //     0xb1d124: ldur            w1, [x0, #0x17]
    //     0xb1d128: add             x1, x1, HEAP, lsl #32
    //     0xb1d12c: stur            x1, [fp, #-8]
    // 0xb1d130: r1 = 2
    //     0xb1d130: movz            x1, #0x2
    // 0xb1d134: r0 = AllocateContext()
    //     0xb1d134: bl              #0xec126c  ; AllocateContextStub
    // 0xb1d138: mov             x1, x0
    // 0xb1d13c: ldur            x0, [fp, #-8]
    // 0xb1d140: stur            x1, [fp, #-0x10]
    // 0xb1d144: StoreField: r1->field_b = r0
    //     0xb1d144: stur            w0, [x1, #0xb]
    // 0xb1d148: ldr             x0, [fp, #0x18]
    // 0xb1d14c: StoreField: r1->field_f = r0
    //     0xb1d14c: stur            w0, [x1, #0xf]
    // 0xb1d150: ldr             x0, [fp, #0x10]
    // 0xb1d154: StoreField: r1->field_13 = r0
    //     0xb1d154: stur            w0, [x1, #0x13]
    // 0xb1d158: r0 = Obx()
    //     0xb1d158: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb1d15c: ldur            x2, [fp, #-0x10]
    // 0xb1d160: r1 = Function '<anonymous closure>':.
    //     0xb1d160: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f328] AnonymousClosure: (0xb1d188), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1d164: ldr             x1, [x1, #0x328]
    // 0xb1d168: stur            x0, [fp, #-8]
    // 0xb1d16c: r0 = AllocateClosure()
    //     0xb1d16c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1d170: mov             x1, x0
    // 0xb1d174: ldur            x0, [fp, #-8]
    // 0xb1d178: StoreField: r0->field_b = r1
    //     0xb1d178: stur            w1, [x0, #0xb]
    // 0xb1d17c: LeaveFrame
    //     0xb1d17c: mov             SP, fp
    //     0xb1d180: ldp             fp, lr, [SP], #0x10
    // 0xb1d184: ret
    //     0xb1d184: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb1d188, size: 0x178
    // 0xb1d188: EnterFrame
    //     0xb1d188: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d18c: mov             fp, SP
    // 0xb1d190: AllocStack(0x30)
    //     0xb1d190: sub             SP, SP, #0x30
    // 0xb1d194: SetupParameters()
    //     0xb1d194: ldr             x0, [fp, #0x10]
    //     0xb1d198: ldur            w2, [x0, #0x17]
    //     0xb1d19c: add             x2, x2, HEAP, lsl #32
    //     0xb1d1a0: stur            x2, [fp, #-0x10]
    // 0xb1d1a4: CheckStackOverflow
    //     0xb1d1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1d1a8: cmp             SP, x16
    //     0xb1d1ac: b.ls            #0xb1d2f8
    // 0xb1d1b0: LoadField: r0 = r2->field_b
    //     0xb1d1b0: ldur            w0, [x2, #0xb]
    // 0xb1d1b4: DecompressPointer r0
    //     0xb1d1b4: add             x0, x0, HEAP, lsl #32
    // 0xb1d1b8: stur            x0, [fp, #-8]
    // 0xb1d1bc: LoadField: r1 = r0->field_f
    //     0xb1d1bc: ldur            w1, [x0, #0xf]
    // 0xb1d1c0: DecompressPointer r1
    //     0xb1d1c0: add             x1, x1, HEAP, lsl #32
    // 0xb1d1c4: r0 = controller()
    //     0xb1d1c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d1c8: LoadField: r1 = r0->field_3f
    //     0xb1d1c8: ldur            w1, [x0, #0x3f]
    // 0xb1d1cc: DecompressPointer r1
    //     0xb1d1cc: add             x1, x1, HEAP, lsl #32
    // 0xb1d1d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1d1d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1d1d4: r0 = toList()
    //     0xb1d1d4: bl              #0x862c84  ; [dart:collection] ListBase::toList
    // 0xb1d1d8: LoadField: r1 = r0->field_b
    //     0xb1d1d8: ldur            w1, [x0, #0xb]
    // 0xb1d1dc: cbz             w1, #0xb1d2e4
    // 0xb1d1e0: ldur            x0, [fp, #-8]
    // 0xb1d1e4: LoadField: r1 = r0->field_f
    //     0xb1d1e4: ldur            w1, [x0, #0xf]
    // 0xb1d1e8: DecompressPointer r1
    //     0xb1d1e8: add             x1, x1, HEAP, lsl #32
    // 0xb1d1ec: r0 = _buildTabBar()
    //     0xb1d1ec: bl              #0xb1d300  ; [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::_buildTabBar
    // 0xb1d1f0: mov             x2, x0
    // 0xb1d1f4: ldur            x0, [fp, #-8]
    // 0xb1d1f8: stur            x2, [fp, #-0x18]
    // 0xb1d1fc: LoadField: r1 = r0->field_f
    //     0xb1d1fc: ldur            w1, [x0, #0xf]
    // 0xb1d200: DecompressPointer r1
    //     0xb1d200: add             x1, x1, HEAP, lsl #32
    // 0xb1d204: r0 = controller()
    //     0xb1d204: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d208: ldur            x2, [fp, #-0x10]
    // 0xb1d20c: r1 = Function '<anonymous closure>':.
    //     0xb1d20c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f330] AnonymousClosure: (0xb1d6a0), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1d210: ldr             x1, [x1, #0x330]
    // 0xb1d214: stur            x0, [fp, #-8]
    // 0xb1d218: r0 = AllocateClosure()
    //     0xb1d218: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1d21c: r16 = <List<Verse>>
    //     0xb1d21c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b4d0] TypeArguments: <List<Verse>>
    //     0xb1d220: ldr             x16, [x16, #0x4d0]
    // 0xb1d224: ldur            lr, [fp, #-8]
    // 0xb1d228: stp             lr, x16, [SP, #8]
    // 0xb1d22c: str             x0, [SP]
    // 0xb1d230: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1d230: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1d234: r0 = StateExt.obx()
    //     0xb1d234: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xb1d238: r1 = Null
    //     0xb1d238: mov             x1, NULL
    // 0xb1d23c: r2 = 6
    //     0xb1d23c: movz            x2, #0x6
    // 0xb1d240: stur            x0, [fp, #-8]
    // 0xb1d244: r0 = AllocateArray()
    //     0xb1d244: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1d248: mov             x2, x0
    // 0xb1d24c: ldur            x0, [fp, #-0x18]
    // 0xb1d250: stur            x2, [fp, #-0x10]
    // 0xb1d254: StoreField: r2->field_f = r0
    //     0xb1d254: stur            w0, [x2, #0xf]
    // 0xb1d258: ldur            x0, [fp, #-8]
    // 0xb1d25c: StoreField: r2->field_13 = r0
    //     0xb1d25c: stur            w0, [x2, #0x13]
    // 0xb1d260: r16 = Instance_MurottalPlayerView
    //     0xb1d260: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] Obj!MurottalPlayerView@e1f951
    //     0xb1d264: ldr             x16, [x16, #0x338]
    // 0xb1d268: ArrayStore: r2[0] = r16  ; List_4
    //     0xb1d268: stur            w16, [x2, #0x17]
    // 0xb1d26c: r1 = <Widget>
    //     0xb1d26c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1d270: r0 = AllocateGrowableArray()
    //     0xb1d270: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1d274: mov             x1, x0
    // 0xb1d278: ldur            x0, [fp, #-0x10]
    // 0xb1d27c: stur            x1, [fp, #-8]
    // 0xb1d280: StoreField: r1->field_f = r0
    //     0xb1d280: stur            w0, [x1, #0xf]
    // 0xb1d284: r0 = 6
    //     0xb1d284: movz            x0, #0x6
    // 0xb1d288: StoreField: r1->field_b = r0
    //     0xb1d288: stur            w0, [x1, #0xb]
    // 0xb1d28c: r0 = Column()
    //     0xb1d28c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1d290: r1 = Instance_Axis
    //     0xb1d290: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb1d294: StoreField: r0->field_f = r1
    //     0xb1d294: stur            w1, [x0, #0xf]
    // 0xb1d298: r1 = Instance_MainAxisAlignment
    //     0xb1d298: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb1d29c: ldr             x1, [x1, #0x730]
    // 0xb1d2a0: StoreField: r0->field_13 = r1
    //     0xb1d2a0: stur            w1, [x0, #0x13]
    // 0xb1d2a4: r1 = Instance_MainAxisSize
    //     0xb1d2a4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb1d2a8: ldr             x1, [x1, #0x738]
    // 0xb1d2ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1d2ac: stur            w1, [x0, #0x17]
    // 0xb1d2b0: r1 = Instance_CrossAxisAlignment
    //     0xb1d2b0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb1d2b4: ldr             x1, [x1, #0x740]
    // 0xb1d2b8: StoreField: r0->field_1b = r1
    //     0xb1d2b8: stur            w1, [x0, #0x1b]
    // 0xb1d2bc: r1 = Instance_VerticalDirection
    //     0xb1d2bc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb1d2c0: ldr             x1, [x1, #0x748]
    // 0xb1d2c4: StoreField: r0->field_23 = r1
    //     0xb1d2c4: stur            w1, [x0, #0x23]
    // 0xb1d2c8: r1 = Instance_Clip
    //     0xb1d2c8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb1d2cc: ldr             x1, [x1, #0x750]
    // 0xb1d2d0: StoreField: r0->field_2b = r1
    //     0xb1d2d0: stur            w1, [x0, #0x2b]
    // 0xb1d2d4: StoreField: r0->field_2f = rZR
    //     0xb1d2d4: stur            xzr, [x0, #0x2f]
    // 0xb1d2d8: ldur            x1, [fp, #-8]
    // 0xb1d2dc: StoreField: r0->field_b = r1
    //     0xb1d2dc: stur            w1, [x0, #0xb]
    // 0xb1d2e0: b               #0xb1d2ec
    // 0xb1d2e4: r0 = Instance_LoadingIndicator
    //     0xb1d2e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f340] Obj!LoadingIndicator@e1f931
    //     0xb1d2e8: ldr             x0, [x0, #0x340]
    // 0xb1d2ec: LeaveFrame
    //     0xb1d2ec: mov             SP, fp
    //     0xb1d2f0: ldp             fp, lr, [SP], #0x10
    // 0xb1d2f4: ret
    //     0xb1d2f4: ret             
    // 0xb1d2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1d2f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1d2fc: b               #0xb1d1b0
  }
  _ _buildTabBar(/* No info */) {
    // ** addr: 0xb1d300, size: 0x1f0
    // 0xb1d300: EnterFrame
    //     0xb1d300: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d304: mov             fp, SP
    // 0xb1d308: AllocStack(0x38)
    //     0xb1d308: sub             SP, SP, #0x38
    // 0xb1d30c: SetupParameters(QuranListView this /* r1 => r1, fp-0x8 */)
    //     0xb1d30c: stur            x1, [fp, #-8]
    // 0xb1d310: CheckStackOverflow
    //     0xb1d310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1d314: cmp             SP, x16
    //     0xb1d318: b.ls            #0xb1d4dc
    // 0xb1d31c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1d31c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1d320: ldr             x0, [x0, #0x2670]
    //     0xb1d324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1d328: cmp             w0, w16
    //     0xb1d32c: b.ne            #0xb1d338
    //     0xb1d330: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1d334: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1d338: r0 = GetNavigation.theme()
    //     0xb1d338: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb1d33c: LoadField: r1 = r0->field_6f
    //     0xb1d33c: ldur            w1, [x0, #0x6f]
    // 0xb1d340: DecompressPointer r1
    //     0xb1d340: add             x1, x1, HEAP, lsl #32
    // 0xb1d344: stur            x1, [fp, #-0x10]
    // 0xb1d348: r0 = GetNavigation.theme()
    //     0xb1d348: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb1d34c: LoadField: r1 = r0->field_77
    //     0xb1d34c: ldur            w1, [x0, #0x77]
    // 0xb1d350: DecompressPointer r1
    //     0xb1d350: add             x1, x1, HEAP, lsl #32
    // 0xb1d354: stur            x1, [fp, #-0x18]
    // 0xb1d358: r0 = BoxShadow()
    //     0xb1d358: bl              #0x794360  ; AllocateBoxShadowStub -> BoxShadow (size=0x24)
    // 0xb1d35c: stur            x0, [fp, #-0x20]
    // 0xb1d360: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb1d360: stur            xzr, [x0, #0x17]
    // 0xb1d364: r1 = Instance_BlurStyle
    //     0xb1d364: add             x1, PP, #0x29, lsl #12  ; [pp+0x29010] Obj!BlurStyle@e39a01
    //     0xb1d368: ldr             x1, [x1, #0x10]
    // 0xb1d36c: StoreField: r0->field_1f = r1
    //     0xb1d36c: stur            w1, [x0, #0x1f]
    // 0xb1d370: ldur            x1, [fp, #-0x18]
    // 0xb1d374: StoreField: r0->field_7 = r1
    //     0xb1d374: stur            w1, [x0, #7]
    // 0xb1d378: r1 = Instance_Offset
    //     0xb1d378: add             x1, PP, #0x29, lsl #12  ; [pp+0x29ef0] Obj!Offset@e2c8a1
    //     0xb1d37c: ldr             x1, [x1, #0xef0]
    // 0xb1d380: StoreField: r0->field_b = r1
    //     0xb1d380: stur            w1, [x0, #0xb]
    // 0xb1d384: d0 = 8.000000
    //     0xb1d384: fmov            d0, #8.00000000
    // 0xb1d388: StoreField: r0->field_f = d0
    //     0xb1d388: stur            d0, [x0, #0xf]
    // 0xb1d38c: r1 = Null
    //     0xb1d38c: mov             x1, NULL
    // 0xb1d390: r2 = 2
    //     0xb1d390: movz            x2, #0x2
    // 0xb1d394: r0 = AllocateArray()
    //     0xb1d394: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1d398: mov             x2, x0
    // 0xb1d39c: ldur            x0, [fp, #-0x20]
    // 0xb1d3a0: stur            x2, [fp, #-0x18]
    // 0xb1d3a4: StoreField: r2->field_f = r0
    //     0xb1d3a4: stur            w0, [x2, #0xf]
    // 0xb1d3a8: r1 = <BoxShadow>
    //     0xb1d3a8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29020] TypeArguments: <BoxShadow>
    //     0xb1d3ac: ldr             x1, [x1, #0x20]
    // 0xb1d3b0: r0 = AllocateGrowableArray()
    //     0xb1d3b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1d3b4: mov             x1, x0
    // 0xb1d3b8: ldur            x0, [fp, #-0x18]
    // 0xb1d3bc: stur            x1, [fp, #-0x20]
    // 0xb1d3c0: StoreField: r1->field_f = r0
    //     0xb1d3c0: stur            w0, [x1, #0xf]
    // 0xb1d3c4: r0 = 2
    //     0xb1d3c4: movz            x0, #0x2
    // 0xb1d3c8: StoreField: r1->field_b = r0
    //     0xb1d3c8: stur            w0, [x1, #0xb]
    // 0xb1d3cc: r0 = BoxDecoration()
    //     0xb1d3cc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1d3d0: mov             x2, x0
    // 0xb1d3d4: ldur            x0, [fp, #-0x10]
    // 0xb1d3d8: stur            x2, [fp, #-0x18]
    // 0xb1d3dc: StoreField: r2->field_7 = r0
    //     0xb1d3dc: stur            w0, [x2, #7]
    // 0xb1d3e0: ldur            x0, [fp, #-0x20]
    // 0xb1d3e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1d3e4: stur            w0, [x2, #0x17]
    // 0xb1d3e8: r0 = Instance_BoxShape
    //     0xb1d3e8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb1d3ec: ldr             x0, [x0, #0xca8]
    // 0xb1d3f0: StoreField: r2->field_23 = r0
    //     0xb1d3f0: stur            w0, [x2, #0x23]
    // 0xb1d3f4: ldur            x1, [fp, #-8]
    // 0xb1d3f8: r0 = controller()
    //     0xb1d3f8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d3fc: LoadField: r2 = r0->field_57
    //     0xb1d3fc: ldur            w2, [x0, #0x57]
    // 0xb1d400: DecompressPointer r2
    //     0xb1d400: add             x2, x2, HEAP, lsl #32
    // 0xb1d404: r16 = Sentinel
    //     0xb1d404: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1d408: cmp             w2, w16
    // 0xb1d40c: b.eq            #0xb1d4e4
    // 0xb1d410: ldur            x1, [fp, #-8]
    // 0xb1d414: stur            x2, [fp, #-0x10]
    // 0xb1d418: r0 = controller()
    //     0xb1d418: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d41c: mov             x1, x0
    // 0xb1d420: r0 = tabBars()
    //     0xb1d420: bl              #0xb1d4f0  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::tabBars
    // 0xb1d424: r1 = Function '<anonymous closure>':.
    //     0xb1d424: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f3d0] AnonymousClosure: (0xb1d680), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::_buildTabBar (0xb1d300)
    //     0xb1d428: ldr             x1, [x1, #0x3d0]
    // 0xb1d42c: r2 = Null
    //     0xb1d42c: mov             x2, NULL
    // 0xb1d430: stur            x0, [fp, #-8]
    // 0xb1d434: r0 = AllocateClosure()
    //     0xb1d434: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1d438: r16 = <Tab>
    //     0xb1d438: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f028] TypeArguments: <Tab>
    //     0xb1d43c: ldr             x16, [x16, #0x28]
    // 0xb1d440: ldur            lr, [fp, #-8]
    // 0xb1d444: stp             lr, x16, [SP, #8]
    // 0xb1d448: str             x0, [SP]
    // 0xb1d44c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1d44c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1d450: r0 = map()
    //     0xb1d450: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb1d454: LoadField: r1 = r0->field_7
    //     0xb1d454: ldur            w1, [x0, #7]
    // 0xb1d458: DecompressPointer r1
    //     0xb1d458: add             x1, x1, HEAP, lsl #32
    // 0xb1d45c: mov             x2, x0
    // 0xb1d460: r0 = _GrowableList.of()
    //     0xb1d460: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb1d464: stur            x0, [fp, #-8]
    // 0xb1d468: r0 = TabBar()
    //     0xb1d468: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xb1d46c: mov             x1, x0
    // 0xb1d470: ldur            x0, [fp, #-8]
    // 0xb1d474: stur            x1, [fp, #-0x20]
    // 0xb1d478: StoreField: r1->field_b = r0
    //     0xb1d478: stur            w0, [x1, #0xb]
    // 0xb1d47c: ldur            x0, [fp, #-0x10]
    // 0xb1d480: StoreField: r1->field_f = r0
    //     0xb1d480: stur            w0, [x1, #0xf]
    // 0xb1d484: r0 = true
    //     0xb1d484: add             x0, NULL, #0x20  ; true
    // 0xb1d488: StoreField: r1->field_13 = r0
    //     0xb1d488: stur            w0, [x1, #0x13]
    // 0xb1d48c: r2 = false
    //     0xb1d48c: add             x2, NULL, #0x30  ; false
    // 0xb1d490: StoreField: r1->field_2f = r2
    //     0xb1d490: stur            w2, [x1, #0x2f]
    // 0xb1d494: d0 = 2.000000
    //     0xb1d494: fmov            d0, #2.00000000
    // 0xb1d498: StoreField: r1->field_1f = d0
    //     0xb1d498: stur            d0, [x1, #0x1f]
    // 0xb1d49c: r2 = Instance_EdgeInsets
    //     0xb1d49c: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb1d4a0: StoreField: r1->field_27 = r2
    //     0xb1d4a0: stur            w2, [x1, #0x27]
    // 0xb1d4a4: r2 = Instance_DragStartBehavior
    //     0xb1d4a4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb1d4a8: StoreField: r1->field_57 = r2
    //     0xb1d4a8: stur            w2, [x1, #0x57]
    // 0xb1d4ac: StoreField: r1->field_7f = r0
    //     0xb1d4ac: stur            w0, [x1, #0x7f]
    // 0xb1d4b0: r0 = DecoratedBox()
    //     0xb1d4b0: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb1d4b4: ldur            x1, [fp, #-0x18]
    // 0xb1d4b8: StoreField: r0->field_f = r1
    //     0xb1d4b8: stur            w1, [x0, #0xf]
    // 0xb1d4bc: r1 = Instance_DecorationPosition
    //     0xb1d4bc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb1d4c0: ldr             x1, [x1, #0xb28]
    // 0xb1d4c4: StoreField: r0->field_13 = r1
    //     0xb1d4c4: stur            w1, [x0, #0x13]
    // 0xb1d4c8: ldur            x1, [fp, #-0x20]
    // 0xb1d4cc: StoreField: r0->field_b = r1
    //     0xb1d4cc: stur            w1, [x0, #0xb]
    // 0xb1d4d0: LeaveFrame
    //     0xb1d4d0: mov             SP, fp
    //     0xb1d4d4: ldp             fp, lr, [SP], #0x10
    // 0xb1d4d8: ret
    //     0xb1d4d8: ret             
    // 0xb1d4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1d4dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1d4e0: b               #0xb1d31c
    // 0xb1d4e4: r9 = tabController
    //     0xb1d4e4: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f310] Field <QuranListController.tabController>: late (offset: 0x58)
    //     0xb1d4e8: ldr             x9, [x9, #0x310]
    // 0xb1d4ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1d4ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Tab <anonymous closure>(dynamic, String) {
    // ** addr: 0xb1d680, size: 0x20
    // 0xb1d680: EnterFrame
    //     0xb1d680: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d684: mov             fp, SP
    // 0xb1d688: r0 = Tab()
    //     0xb1d688: bl              #0xae37cc  ; AllocateTabStub -> Tab (size=0x20)
    // 0xb1d68c: ldr             x1, [fp, #0x10]
    // 0xb1d690: StoreField: r0->field_b = r1
    //     0xb1d690: stur            w1, [x0, #0xb]
    // 0xb1d694: LeaveFrame
    //     0xb1d694: mov             SP, fp
    //     0xb1d698: ldp             fp, lr, [SP], #0x10
    // 0xb1d69c: ret
    //     0xb1d69c: ret             
  }
  [closure] HorizontalGestureAndDefaultStyle <anonymous closure>(dynamic, List<Verse>?) {
    // ** addr: 0xb1d6a0, size: 0x240
    // 0xb1d6a0: EnterFrame
    //     0xb1d6a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d6a4: mov             fp, SP
    // 0xb1d6a8: AllocStack(0x38)
    //     0xb1d6a8: sub             SP, SP, #0x38
    // 0xb1d6ac: SetupParameters()
    //     0xb1d6ac: ldr             x0, [fp, #0x18]
    //     0xb1d6b0: ldur            w1, [x0, #0x17]
    //     0xb1d6b4: add             x1, x1, HEAP, lsl #32
    //     0xb1d6b8: stur            x1, [fp, #-8]
    // 0xb1d6bc: CheckStackOverflow
    //     0xb1d6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1d6c0: cmp             SP, x16
    //     0xb1d6c4: b.ls            #0xb1d8cc
    // 0xb1d6c8: r1 = 1
    //     0xb1d6c8: movz            x1, #0x1
    // 0xb1d6cc: r0 = AllocateContext()
    //     0xb1d6cc: bl              #0xec126c  ; AllocateContextStub
    // 0xb1d6d0: mov             x2, x0
    // 0xb1d6d4: ldur            x0, [fp, #-8]
    // 0xb1d6d8: stur            x2, [fp, #-0x18]
    // 0xb1d6dc: StoreField: r2->field_b = r0
    //     0xb1d6dc: stur            w0, [x2, #0xb]
    // 0xb1d6e0: ldr             x3, [fp, #0x10]
    // 0xb1d6e4: StoreField: r2->field_f = r3
    //     0xb1d6e4: stur            w3, [x2, #0xf]
    // 0xb1d6e8: LoadField: r4 = r0->field_b
    //     0xb1d6e8: ldur            w4, [x0, #0xb]
    // 0xb1d6ec: DecompressPointer r4
    //     0xb1d6ec: add             x4, x4, HEAP, lsl #32
    // 0xb1d6f0: stur            x4, [fp, #-0x10]
    // 0xb1d6f4: LoadField: r1 = r4->field_f
    //     0xb1d6f4: ldur            w1, [x4, #0xf]
    // 0xb1d6f8: DecompressPointer r1
    //     0xb1d6f8: add             x1, x1, HEAP, lsl #32
    // 0xb1d6fc: r0 = controller()
    //     0xb1d6fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d700: mov             x2, x0
    // 0xb1d704: ldur            x0, [fp, #-0x10]
    // 0xb1d708: stur            x2, [fp, #-8]
    // 0xb1d70c: LoadField: r1 = r0->field_f
    //     0xb1d70c: ldur            w1, [x0, #0xf]
    // 0xb1d710: DecompressPointer r1
    //     0xb1d710: add             x1, x1, HEAP, lsl #32
    // 0xb1d714: r0 = controller()
    //     0xb1d714: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d718: mov             x1, x0
    // 0xb1d71c: LoadField: r0 = r1->field_63
    //     0xb1d71c: ldur            w0, [x1, #0x63]
    // 0xb1d720: DecompressPointer r0
    //     0xb1d720: add             x0, x0, HEAP, lsl #32
    // 0xb1d724: r16 = Sentinel
    //     0xb1d724: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1d728: cmp             w0, w16
    // 0xb1d72c: b.ne            #0xb1d73c
    // 0xb1d730: r2 = itemScrollController
    //     0xb1d730: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f348] Field <QuranListController.itemScrollController>: late (offset: 0x64)
    //     0xb1d734: ldr             x2, [x2, #0x348]
    // 0xb1d738: r0 = InitLateInstanceField()
    //     0xb1d738: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xb1d73c: mov             x2, x0
    // 0xb1d740: ldur            x0, [fp, #-0x10]
    // 0xb1d744: stur            x2, [fp, #-0x20]
    // 0xb1d748: LoadField: r1 = r0->field_f
    //     0xb1d748: ldur            w1, [x0, #0xf]
    // 0xb1d74c: DecompressPointer r1
    //     0xb1d74c: add             x1, x1, HEAP, lsl #32
    // 0xb1d750: r0 = controller()
    //     0xb1d750: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d754: LoadField: r2 = r0->field_67
    //     0xb1d754: ldur            w2, [x0, #0x67]
    // 0xb1d758: DecompressPointer r2
    //     0xb1d758: add             x2, x2, HEAP, lsl #32
    // 0xb1d75c: ldr             x0, [fp, #0x10]
    // 0xb1d760: stur            x2, [fp, #-0x28]
    // 0xb1d764: cmp             w0, NULL
    // 0xb1d768: b.ne            #0xb1d778
    // 0xb1d76c: mov             x0, x2
    // 0xb1d770: r2 = 0
    //     0xb1d770: movz            x2, #0
    // 0xb1d774: b               #0xb1d81c
    // 0xb1d778: ldur            x1, [fp, #-0x10]
    // 0xb1d77c: LoadField: r3 = r1->field_f
    //     0xb1d77c: ldur            w3, [x1, #0xf]
    // 0xb1d780: DecompressPointer r3
    //     0xb1d780: add             x3, x3, HEAP, lsl #32
    // 0xb1d784: mov             x1, x3
    // 0xb1d788: r0 = controller()
    //     0xb1d788: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d78c: LoadField: r1 = r0->field_57
    //     0xb1d78c: ldur            w1, [x0, #0x57]
    // 0xb1d790: DecompressPointer r1
    //     0xb1d790: add             x1, x1, HEAP, lsl #32
    // 0xb1d794: r16 = Sentinel
    //     0xb1d794: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1d798: cmp             w1, w16
    // 0xb1d79c: b.eq            #0xb1d8d4
    // 0xb1d7a0: LoadField: r0 = r1->field_33
    //     0xb1d7a0: ldur            x0, [x1, #0x33]
    // 0xb1d7a4: cbnz            x0, #0xb1d7e0
    // 0xb1d7a8: ldr             x0, [fp, #0x10]
    // 0xb1d7ac: r1 = LoadClassIdInstr(r0)
    //     0xb1d7ac: ldur            x1, [x0, #-1]
    //     0xb1d7b0: ubfx            x1, x1, #0xc, #0x14
    // 0xb1d7b4: str             x0, [SP]
    // 0xb1d7b8: mov             x0, x1
    // 0xb1d7bc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb1d7bc: movz            x17, #0xc834
    //     0xb1d7c0: add             lr, x0, x17
    //     0xb1d7c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb1d7c8: blr             lr
    // 0xb1d7cc: r1 = LoadInt32Instr(r0)
    //     0xb1d7cc: sbfx            x1, x0, #1, #0x1f
    //     0xb1d7d0: tbz             w0, #0, #0xb1d7d8
    //     0xb1d7d4: ldur            x1, [x0, #7]
    // 0xb1d7d8: add             x0, x1, #1
    // 0xb1d7dc: b               #0xb1d814
    // 0xb1d7e0: ldr             x0, [fp, #0x10]
    // 0xb1d7e4: r1 = LoadClassIdInstr(r0)
    //     0xb1d7e4: ldur            x1, [x0, #-1]
    //     0xb1d7e8: ubfx            x1, x1, #0xc, #0x14
    // 0xb1d7ec: str             x0, [SP]
    // 0xb1d7f0: mov             x0, x1
    // 0xb1d7f4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb1d7f4: movz            x17, #0xc834
    //     0xb1d7f8: add             lr, x0, x17
    //     0xb1d7fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb1d800: blr             lr
    // 0xb1d804: r1 = LoadInt32Instr(r0)
    //     0xb1d804: sbfx            x1, x0, #1, #0x1f
    //     0xb1d808: tbz             w0, #0, #0xb1d810
    //     0xb1d80c: ldur            x1, [x0, #7]
    // 0xb1d810: mov             x0, x1
    // 0xb1d814: mov             x2, x0
    // 0xb1d818: ldur            x0, [fp, #-0x28]
    // 0xb1d81c: ldur            x1, [fp, #-0x20]
    // 0xb1d820: stur            x2, [fp, #-0x30]
    // 0xb1d824: r0 = ScrollablePositionedList()
    //     0xb1d824: bl              #0xb1d8ec  ; AllocateScrollablePositionedListStub -> ScrollablePositionedList (size=0x64)
    // 0xb1d828: mov             x3, x0
    // 0xb1d82c: ldur            x0, [fp, #-0x30]
    // 0xb1d830: stur            x3, [fp, #-0x10]
    // 0xb1d834: StoreField: r3->field_b = r0
    //     0xb1d834: stur            x0, [x3, #0xb]
    // 0xb1d838: ldur            x2, [fp, #-0x18]
    // 0xb1d83c: r1 = Function '<anonymous closure>':.
    //     0xb1d83c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f350] AnonymousClosure: (0xb1d8f8), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1d840: ldr             x1, [x1, #0x350]
    // 0xb1d844: r0 = AllocateClosure()
    //     0xb1d844: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1d848: mov             x1, x0
    // 0xb1d84c: ldur            x0, [fp, #-0x10]
    // 0xb1d850: StoreField: r0->field_13 = r1
    //     0xb1d850: stur            w1, [x0, #0x13]
    // 0xb1d854: ldur            x1, [fp, #-0x20]
    // 0xb1d858: StoreField: r0->field_1b = r1
    //     0xb1d858: stur            w1, [x0, #0x1b]
    // 0xb1d85c: r1 = false
    //     0xb1d85c: add             x1, NULL, #0x30  ; false
    // 0xb1d860: StoreField: r0->field_43 = r1
    //     0xb1d860: stur            w1, [x0, #0x43]
    // 0xb1d864: StoreField: r0->field_2b = rZR
    //     0xb1d864: stur            xzr, [x0, #0x2b]
    // 0xb1d868: StoreField: r0->field_33 = rZR
    //     0xb1d868: stur            xzr, [x0, #0x33]
    // 0xb1d86c: r2 = Instance_Axis
    //     0xb1d86c: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb1d870: StoreField: r0->field_3b = r2
    //     0xb1d870: stur            w2, [x0, #0x3b]
    // 0xb1d874: StoreField: r0->field_3f = r1
    //     0xb1d874: stur            w1, [x0, #0x3f]
    // 0xb1d878: r1 = true
    //     0xb1d878: add             x1, NULL, #0x20  ; true
    // 0xb1d87c: StoreField: r0->field_53 = r1
    //     0xb1d87c: stur            w1, [x0, #0x53]
    // 0xb1d880: StoreField: r0->field_57 = r1
    //     0xb1d880: stur            w1, [x0, #0x57]
    // 0xb1d884: StoreField: r0->field_5b = r1
    //     0xb1d884: stur            w1, [x0, #0x5b]
    // 0xb1d888: ldur            x1, [fp, #-0x28]
    // 0xb1d88c: StoreField: r0->field_1f = r1
    //     0xb1d88c: stur            w1, [x0, #0x1f]
    // 0xb1d890: r0 = HorizontalGestureAndDefaultStyle()
    //     0xb1d890: bl              #0xb1d8e0  ; AllocateHorizontalGestureAndDefaultStyleStub -> HorizontalGestureAndDefaultStyle (size=0x14)
    // 0xb1d894: mov             x3, x0
    // 0xb1d898: ldur            x0, [fp, #-0x10]
    // 0xb1d89c: stur            x3, [fp, #-0x18]
    // 0xb1d8a0: StoreField: r3->field_b = r0
    //     0xb1d8a0: stur            w0, [x3, #0xb]
    // 0xb1d8a4: ldur            x2, [fp, #-8]
    // 0xb1d8a8: r1 = Function 'onHorizontalDragEnd':.
    //     0xb1d8a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f358] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xb1d8ac: ldr             x1, [x1, #0x358]
    // 0xb1d8b0: r0 = AllocateClosure()
    //     0xb1d8b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1d8b4: mov             x1, x0
    // 0xb1d8b8: ldur            x0, [fp, #-0x18]
    // 0xb1d8bc: StoreField: r0->field_f = r1
    //     0xb1d8bc: stur            w1, [x0, #0xf]
    // 0xb1d8c0: LeaveFrame
    //     0xb1d8c0: mov             SP, fp
    //     0xb1d8c4: ldp             fp, lr, [SP], #0x10
    // 0xb1d8c8: ret
    //     0xb1d8c8: ret             
    // 0xb1d8cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1d8cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1d8d0: b               #0xb1d6c8
    // 0xb1d8d4: r9 = tabController
    //     0xb1d8d4: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f310] Field <QuranListController.tabController>: late (offset: 0x58)
    //     0xb1d8d8: ldr             x9, [x9, #0x310]
    // 0xb1d8dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1d8dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1d8f8, size: 0x77c
    // 0xb1d8f8: EnterFrame
    //     0xb1d8f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d8fc: mov             fp, SP
    // 0xb1d900: AllocStack(0x48)
    //     0xb1d900: sub             SP, SP, #0x48
    // 0xb1d904: SetupParameters()
    //     0xb1d904: ldr             x0, [fp, #0x20]
    //     0xb1d908: ldur            w1, [x0, #0x17]
    //     0xb1d90c: add             x1, x1, HEAP, lsl #32
    //     0xb1d910: stur            x1, [fp, #-8]
    // 0xb1d914: CheckStackOverflow
    //     0xb1d914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1d918: cmp             SP, x16
    //     0xb1d91c: b.ls            #0xb1e05c
    // 0xb1d920: r1 = 1
    //     0xb1d920: movz            x1, #0x1
    // 0xb1d924: r0 = AllocateContext()
    //     0xb1d924: bl              #0xec126c  ; AllocateContextStub
    // 0xb1d928: mov             x3, x0
    // 0xb1d92c: ldur            x0, [fp, #-8]
    // 0xb1d930: stur            x3, [fp, #-0x10]
    // 0xb1d934: StoreField: r3->field_b = r0
    //     0xb1d934: stur            w0, [x3, #0xb]
    // 0xb1d938: ldr             x1, [fp, #0x10]
    // 0xb1d93c: StoreField: r3->field_f = r1
    //     0xb1d93c: stur            w1, [x3, #0xf]
    // 0xb1d940: r1 = <Widget>
    //     0xb1d940: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1d944: r2 = 0
    //     0xb1d944: movz            x2, #0
    // 0xb1d948: r0 = _GrowableList()
    //     0xb1d948: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb1d94c: mov             x2, x0
    // 0xb1d950: ldur            x0, [fp, #-8]
    // 0xb1d954: stur            x2, [fp, #-0x20]
    // 0xb1d958: LoadField: r1 = r0->field_b
    //     0xb1d958: ldur            w1, [x0, #0xb]
    // 0xb1d95c: DecompressPointer r1
    //     0xb1d95c: add             x1, x1, HEAP, lsl #32
    // 0xb1d960: LoadField: r3 = r1->field_b
    //     0xb1d960: ldur            w3, [x1, #0xb]
    // 0xb1d964: DecompressPointer r3
    //     0xb1d964: add             x3, x3, HEAP, lsl #32
    // 0xb1d968: stur            x3, [fp, #-0x18]
    // 0xb1d96c: LoadField: r1 = r3->field_f
    //     0xb1d96c: ldur            w1, [x3, #0xf]
    // 0xb1d970: DecompressPointer r1
    //     0xb1d970: add             x1, x1, HEAP, lsl #32
    // 0xb1d974: r0 = controller()
    //     0xb1d974: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1d978: LoadField: r1 = r0->field_57
    //     0xb1d978: ldur            w1, [x0, #0x57]
    // 0xb1d97c: DecompressPointer r1
    //     0xb1d97c: add             x1, x1, HEAP, lsl #32
    // 0xb1d980: r16 = Sentinel
    //     0xb1d980: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1d984: cmp             w1, w16
    // 0xb1d988: b.eq            #0xb1e064
    // 0xb1d98c: LoadField: r2 = r1->field_33
    //     0xb1d98c: ldur            x2, [x1, #0x33]
    // 0xb1d990: ldur            x1, [fp, #-8]
    // 0xb1d994: stur            x2, [fp, #-0x28]
    // 0xb1d998: LoadField: r0 = r1->field_f
    //     0xb1d998: ldur            w0, [x1, #0xf]
    // 0xb1d99c: DecompressPointer r0
    //     0xb1d99c: add             x0, x0, HEAP, lsl #32
    // 0xb1d9a0: cmp             w0, NULL
    // 0xb1d9a4: b.ne            #0xb1d9b4
    // 0xb1d9a8: mov             x0, x2
    // 0xb1d9ac: r1 = Null
    //     0xb1d9ac: mov             x1, NULL
    // 0xb1d9b0: b               #0xb1d9dc
    // 0xb1d9b4: r3 = LoadClassIdInstr(r0)
    //     0xb1d9b4: ldur            x3, [x0, #-1]
    //     0xb1d9b8: ubfx            x3, x3, #0xc, #0x14
    // 0xb1d9bc: str             x0, [SP]
    // 0xb1d9c0: mov             x0, x3
    // 0xb1d9c4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb1d9c4: movz            x17, #0xc834
    //     0xb1d9c8: add             lr, x0, x17
    //     0xb1d9cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb1d9d0: blr             lr
    // 0xb1d9d4: mov             x1, x0
    // 0xb1d9d8: ldur            x0, [fp, #-0x28]
    // 0xb1d9dc: ldur            x2, [fp, #-0x10]
    // 0xb1d9e0: LoadField: r3 = r2->field_f
    //     0xb1d9e0: ldur            w3, [x2, #0xf]
    // 0xb1d9e4: DecompressPointer r3
    //     0xb1d9e4: add             x3, x3, HEAP, lsl #32
    // 0xb1d9e8: cbnz            x0, #0xb1da40
    // 0xb1d9ec: cmp             w1, w3
    // 0xb1d9f0: b.eq            #0xb1da2c
    // 0xb1d9f4: and             w16, w1, w3
    // 0xb1d9f8: branchIfSmi(r16, 0xb1da40)
    //     0xb1d9f8: tbz             w16, #0, #0xb1da40
    // 0xb1d9fc: r16 = LoadClassIdInstr(r1)
    //     0xb1d9fc: ldur            x16, [x1, #-1]
    //     0xb1da00: ubfx            x16, x16, #0xc, #0x14
    // 0xb1da04: cmp             x16, #0x3d
    // 0xb1da08: b.ne            #0xb1da40
    // 0xb1da0c: r16 = LoadClassIdInstr(r3)
    //     0xb1da0c: ldur            x16, [x3, #-1]
    //     0xb1da10: ubfx            x16, x16, #0xc, #0x14
    // 0xb1da14: cmp             x16, #0x3d
    // 0xb1da18: b.ne            #0xb1da40
    // 0xb1da1c: LoadField: r16 = r1->field_7
    //     0xb1da1c: ldur            x16, [x1, #7]
    // 0xb1da20: LoadField: r17 = r3->field_7
    //     0xb1da20: ldur            x17, [x3, #7]
    // 0xb1da24: cmp             x16, x17
    // 0xb1da28: b.ne            #0xb1da40
    // 0xb1da2c: r0 = Instance_KhatamButton
    //     0xb1da2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f360] Obj!KhatamButton@e1f941
    //     0xb1da30: ldr             x0, [x0, #0x360]
    // 0xb1da34: LeaveFrame
    //     0xb1da34: mov             SP, fp
    //     0xb1da38: ldp             fp, lr, [SP], #0x10
    // 0xb1da3c: ret
    //     0xb1da3c: ret             
    // 0xb1da40: ldur            x1, [fp, #-8]
    // 0xb1da44: LoadField: r0 = r1->field_f
    //     0xb1da44: ldur            w0, [x1, #0xf]
    // 0xb1da48: DecompressPointer r0
    //     0xb1da48: add             x0, x0, HEAP, lsl #32
    // 0xb1da4c: cmp             w0, NULL
    // 0xb1da50: b.eq            #0xb1e070
    // 0xb1da54: r4 = LoadClassIdInstr(r0)
    //     0xb1da54: ldur            x4, [x0, #-1]
    //     0xb1da58: ubfx            x4, x4, #0xc, #0x14
    // 0xb1da5c: stp             x3, x0, [SP]
    // 0xb1da60: mov             x0, x4
    // 0xb1da64: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1da64: movz            x17, #0x3037
    //     0xb1da68: movk            x17, #0x1, lsl #16
    //     0xb1da6c: add             lr, x0, x17
    //     0xb1da70: ldr             lr, [x21, lr, lsl #3]
    //     0xb1da74: blr             lr
    // 0xb1da78: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xb1da78: ldur            x1, [x0, #0x17]
    // 0xb1da7c: cmp             x1, #1
    // 0xb1da80: b.ne            #0xb1db64
    // 0xb1da84: ldur            x0, [fp, #-0x20]
    // 0xb1da88: ldur            x1, [fp, #-0x18]
    // 0xb1da8c: LoadField: r2 = r1->field_f
    //     0xb1da8c: ldur            w2, [x1, #0xf]
    // 0xb1da90: DecompressPointer r2
    //     0xb1da90: add             x2, x2, HEAP, lsl #32
    // 0xb1da94: mov             x1, x2
    // 0xb1da98: r0 = controller()
    //     0xb1da98: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1da9c: LoadField: r1 = r0->field_3f
    //     0xb1da9c: ldur            w1, [x0, #0x3f]
    // 0xb1daa0: DecompressPointer r1
    //     0xb1daa0: add             x1, x1, HEAP, lsl #32
    // 0xb1daa4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1daa4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1daa8: r0 = toList()
    //     0xb1daa8: bl              #0x862c84  ; [dart:collection] ListBase::toList
    // 0xb1daac: ldur            x2, [fp, #-0x10]
    // 0xb1dab0: r1 = Function '<anonymous closure>':.
    //     0xb1dab0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f368] AnonymousClosure: (0xb1ef60), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1dab4: ldr             x1, [x1, #0x368]
    // 0xb1dab8: stur            x0, [fp, #-0x18]
    // 0xb1dabc: r0 = AllocateClosure()
    //     0xb1dabc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1dac0: ldur            x1, [fp, #-0x18]
    // 0xb1dac4: mov             x2, x0
    // 0xb1dac8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1dac8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1dacc: r0 = firstWhere()
    //     0xb1dacc: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0xb1dad0: stur            x0, [fp, #-0x18]
    // 0xb1dad4: r0 = QuranSurahInfo()
    //     0xb1dad4: bl              #0xb1e080  ; AllocateQuranSurahInfoStub -> QuranSurahInfo (size=0x10)
    // 0xb1dad8: mov             x2, x0
    // 0xb1dadc: ldur            x0, [fp, #-0x18]
    // 0xb1dae0: stur            x2, [fp, #-0x30]
    // 0xb1dae4: StoreField: r2->field_b = r0
    //     0xb1dae4: stur            w0, [x2, #0xb]
    // 0xb1dae8: ldur            x0, [fp, #-0x20]
    // 0xb1daec: LoadField: r1 = r0->field_b
    //     0xb1daec: ldur            w1, [x0, #0xb]
    // 0xb1daf0: LoadField: r3 = r0->field_f
    //     0xb1daf0: ldur            w3, [x0, #0xf]
    // 0xb1daf4: DecompressPointer r3
    //     0xb1daf4: add             x3, x3, HEAP, lsl #32
    // 0xb1daf8: LoadField: r4 = r3->field_b
    //     0xb1daf8: ldur            w4, [x3, #0xb]
    // 0xb1dafc: r3 = LoadInt32Instr(r1)
    //     0xb1dafc: sbfx            x3, x1, #1, #0x1f
    // 0xb1db00: stur            x3, [fp, #-0x28]
    // 0xb1db04: r1 = LoadInt32Instr(r4)
    //     0xb1db04: sbfx            x1, x4, #1, #0x1f
    // 0xb1db08: cmp             x3, x1
    // 0xb1db0c: b.ne            #0xb1db18
    // 0xb1db10: mov             x1, x0
    // 0xb1db14: r0 = _growToNextCapacity()
    //     0xb1db14: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1db18: ldur            x3, [fp, #-0x20]
    // 0xb1db1c: ldur            x2, [fp, #-0x28]
    // 0xb1db20: add             x0, x2, #1
    // 0xb1db24: lsl             x1, x0, #1
    // 0xb1db28: StoreField: r3->field_b = r1
    //     0xb1db28: stur            w1, [x3, #0xb]
    // 0xb1db2c: LoadField: r1 = r3->field_f
    //     0xb1db2c: ldur            w1, [x3, #0xf]
    // 0xb1db30: DecompressPointer r1
    //     0xb1db30: add             x1, x1, HEAP, lsl #32
    // 0xb1db34: ldur            x0, [fp, #-0x30]
    // 0xb1db38: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb1db38: add             x25, x1, x2, lsl #2
    //     0xb1db3c: add             x25, x25, #0xf
    //     0xb1db40: str             w0, [x25]
    //     0xb1db44: tbz             w0, #0, #0xb1db60
    //     0xb1db48: ldurb           w16, [x1, #-1]
    //     0xb1db4c: ldurb           w17, [x0, #-1]
    //     0xb1db50: and             x16, x17, x16, lsr #2
    //     0xb1db54: tst             x16, HEAP, lsr #32
    //     0xb1db58: b.eq            #0xb1db60
    //     0xb1db5c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb1db60: b               #0xb1db68
    // 0xb1db64: ldur            x3, [fp, #-0x20]
    // 0xb1db68: ldur            x4, [fp, #-8]
    // 0xb1db6c: ldur            x0, [fp, #-0x10]
    // 0xb1db70: r5 = 4
    //     0xb1db70: movz            x5, #0x4
    // 0xb1db74: mov             x2, x5
    // 0xb1db78: r1 = Null
    //     0xb1db78: mov             x1, NULL
    // 0xb1db7c: r0 = AllocateArray()
    //     0xb1db7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1db80: stur            x0, [fp, #-0x18]
    // 0xb1db84: r16 = 2
    //     0xb1db84: movz            x16, #0x2
    // 0xb1db88: StoreField: r0->field_f = r16
    //     0xb1db88: stur            w16, [x0, #0xf]
    // 0xb1db8c: r16 = 18
    //     0xb1db8c: movz            x16, #0x12
    // 0xb1db90: StoreField: r0->field_13 = r16
    //     0xb1db90: stur            w16, [x0, #0x13]
    // 0xb1db94: r1 = <int>
    //     0xb1db94: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb1db98: r0 = AllocateGrowableArray()
    //     0xb1db98: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1db9c: mov             x1, x0
    // 0xb1dba0: ldur            x0, [fp, #-0x18]
    // 0xb1dba4: stur            x1, [fp, #-0x30]
    // 0xb1dba8: StoreField: r1->field_f = r0
    //     0xb1dba8: stur            w0, [x1, #0xf]
    // 0xb1dbac: r0 = 4
    //     0xb1dbac: movz            x0, #0x4
    // 0xb1dbb0: StoreField: r1->field_b = r0
    //     0xb1dbb0: stur            w0, [x1, #0xb]
    // 0xb1dbb4: ldur            x2, [fp, #-8]
    // 0xb1dbb8: LoadField: r0 = r2->field_f
    //     0xb1dbb8: ldur            w0, [x2, #0xf]
    // 0xb1dbbc: DecompressPointer r0
    //     0xb1dbbc: add             x0, x0, HEAP, lsl #32
    // 0xb1dbc0: ldur            x3, [fp, #-0x10]
    // 0xb1dbc4: LoadField: r4 = r3->field_f
    //     0xb1dbc4: ldur            w4, [x3, #0xf]
    // 0xb1dbc8: DecompressPointer r4
    //     0xb1dbc8: add             x4, x4, HEAP, lsl #32
    // 0xb1dbcc: r5 = LoadClassIdInstr(r0)
    //     0xb1dbcc: ldur            x5, [x0, #-1]
    //     0xb1dbd0: ubfx            x5, x5, #0xc, #0x14
    // 0xb1dbd4: stp             x4, x0, [SP]
    // 0xb1dbd8: mov             x0, x5
    // 0xb1dbdc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1dbdc: movz            x17, #0x3037
    //     0xb1dbe0: movk            x17, #0x1, lsl #16
    //     0xb1dbe4: add             lr, x0, x17
    //     0xb1dbe8: ldr             lr, [x21, lr, lsl #3]
    //     0xb1dbec: blr             lr
    // 0xb1dbf0: LoadField: r2 = r0->field_33
    //     0xb1dbf0: ldur            x2, [x0, #0x33]
    // 0xb1dbf4: r0 = BoxInt64Instr(r2)
    //     0xb1dbf4: sbfiz           x0, x2, #1, #0x1f
    //     0xb1dbf8: cmp             x2, x0, asr #1
    //     0xb1dbfc: b.eq            #0xb1dc08
    //     0xb1dc00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1dc04: stur            x2, [x0, #7]
    // 0xb1dc08: ldur            x1, [fp, #-0x30]
    // 0xb1dc0c: mov             x2, x0
    // 0xb1dc10: r0 = contains()
    //     0xb1dc10: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xb1dc14: tbz             w0, #4, #0xb1dd58
    // 0xb1dc18: ldur            x1, [fp, #-8]
    // 0xb1dc1c: ldur            x2, [fp, #-0x10]
    // 0xb1dc20: LoadField: r0 = r1->field_f
    //     0xb1dc20: ldur            w0, [x1, #0xf]
    // 0xb1dc24: DecompressPointer r0
    //     0xb1dc24: add             x0, x0, HEAP, lsl #32
    // 0xb1dc28: LoadField: r3 = r2->field_f
    //     0xb1dc28: ldur            w3, [x2, #0xf]
    // 0xb1dc2c: DecompressPointer r3
    //     0xb1dc2c: add             x3, x3, HEAP, lsl #32
    // 0xb1dc30: r4 = LoadClassIdInstr(r0)
    //     0xb1dc30: ldur            x4, [x0, #-1]
    //     0xb1dc34: ubfx            x4, x4, #0xc, #0x14
    // 0xb1dc38: stp             x3, x0, [SP]
    // 0xb1dc3c: mov             x0, x4
    // 0xb1dc40: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1dc40: movz            x17, #0x3037
    //     0xb1dc44: movk            x17, #0x1, lsl #16
    //     0xb1dc48: add             lr, x0, x17
    //     0xb1dc4c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1dc50: blr             lr
    // 0xb1dc54: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xb1dc54: ldur            x1, [x0, #0x17]
    // 0xb1dc58: cmp             x1, #1
    // 0xb1dc5c: b.ne            #0xb1dd50
    // 0xb1dc60: ldur            x0, [fp, #-0x20]
    // 0xb1dc64: LoadField: r1 = r0->field_b
    //     0xb1dc64: ldur            w1, [x0, #0xb]
    // 0xb1dc68: LoadField: r2 = r0->field_f
    //     0xb1dc68: ldur            w2, [x0, #0xf]
    // 0xb1dc6c: DecompressPointer r2
    //     0xb1dc6c: add             x2, x2, HEAP, lsl #32
    // 0xb1dc70: LoadField: r3 = r2->field_b
    //     0xb1dc70: ldur            w3, [x2, #0xb]
    // 0xb1dc74: r2 = LoadInt32Instr(r1)
    //     0xb1dc74: sbfx            x2, x1, #1, #0x1f
    // 0xb1dc78: stur            x2, [fp, #-0x28]
    // 0xb1dc7c: r1 = LoadInt32Instr(r3)
    //     0xb1dc7c: sbfx            x1, x3, #1, #0x1f
    // 0xb1dc80: cmp             x2, x1
    // 0xb1dc84: b.ne            #0xb1dc90
    // 0xb1dc88: mov             x1, x0
    // 0xb1dc8c: r0 = _growToNextCapacity()
    //     0xb1dc8c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1dc90: ldur            x0, [fp, #-0x20]
    // 0xb1dc94: ldur            x1, [fp, #-0x28]
    // 0xb1dc98: add             x2, x1, #1
    // 0xb1dc9c: stur            x2, [fp, #-0x38]
    // 0xb1dca0: lsl             x3, x2, #1
    // 0xb1dca4: StoreField: r0->field_b = r3
    //     0xb1dca4: stur            w3, [x0, #0xb]
    // 0xb1dca8: LoadField: r3 = r0->field_f
    //     0xb1dca8: ldur            w3, [x0, #0xf]
    // 0xb1dcac: DecompressPointer r3
    //     0xb1dcac: add             x3, x3, HEAP, lsl #32
    // 0xb1dcb0: add             x4, x3, x1, lsl #2
    // 0xb1dcb4: r16 = Instance_QuranBasmalah
    //     0xb1dcb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f370] Obj!QuranBasmalah@e1f8d1
    //     0xb1dcb8: ldr             x16, [x16, #0x370]
    // 0xb1dcbc: StoreField: r4->field_f = r16
    //     0xb1dcbc: stur            w16, [x4, #0xf]
    // 0xb1dcc0: LoadField: r1 = r3->field_b
    //     0xb1dcc0: ldur            w1, [x3, #0xb]
    // 0xb1dcc4: r3 = LoadInt32Instr(r1)
    //     0xb1dcc4: sbfx            x3, x1, #1, #0x1f
    // 0xb1dcc8: cmp             x2, x3
    // 0xb1dccc: b.ne            #0xb1dcd8
    // 0xb1dcd0: mov             x1, x0
    // 0xb1dcd4: r0 = _growToNextCapacity()
    //     0xb1dcd4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1dcd8: ldur            x0, [fp, #-0x20]
    // 0xb1dcdc: ldur            x1, [fp, #-0x38]
    // 0xb1dce0: add             x2, x1, #1
    // 0xb1dce4: stur            x2, [fp, #-0x28]
    // 0xb1dce8: lsl             x3, x2, #1
    // 0xb1dcec: StoreField: r0->field_b = r3
    //     0xb1dcec: stur            w3, [x0, #0xb]
    // 0xb1dcf0: LoadField: r3 = r0->field_f
    //     0xb1dcf0: ldur            w3, [x0, #0xf]
    // 0xb1dcf4: DecompressPointer r3
    //     0xb1dcf4: add             x3, x3, HEAP, lsl #32
    // 0xb1dcf8: add             x4, x3, x1, lsl #2
    // 0xb1dcfc: r16 = Instance_SizedBox
    //     0xb1dcfc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb1dd00: ldr             x16, [x16, #0xfb0]
    // 0xb1dd04: StoreField: r4->field_f = r16
    //     0xb1dd04: stur            w16, [x4, #0xf]
    // 0xb1dd08: LoadField: r1 = r3->field_b
    //     0xb1dd08: ldur            w1, [x3, #0xb]
    // 0xb1dd0c: r3 = LoadInt32Instr(r1)
    //     0xb1dd0c: sbfx            x3, x1, #1, #0x1f
    // 0xb1dd10: cmp             x2, x3
    // 0xb1dd14: b.ne            #0xb1dd20
    // 0xb1dd18: mov             x1, x0
    // 0xb1dd1c: r0 = _growToNextCapacity()
    //     0xb1dd1c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1dd20: ldur            x1, [fp, #-0x20]
    // 0xb1dd24: ldur            x0, [fp, #-0x28]
    // 0xb1dd28: add             x2, x0, #1
    // 0xb1dd2c: lsl             x3, x2, #1
    // 0xb1dd30: StoreField: r1->field_b = r3
    //     0xb1dd30: stur            w3, [x1, #0xb]
    // 0xb1dd34: LoadField: r2 = r1->field_f
    //     0xb1dd34: ldur            w2, [x1, #0xf]
    // 0xb1dd38: DecompressPointer r2
    //     0xb1dd38: add             x2, x2, HEAP, lsl #32
    // 0xb1dd3c: add             x3, x2, x0, lsl #2
    // 0xb1dd40: r16 = Instance_Divider
    //     0xb1dd40: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb1dd44: ldr             x16, [x16, #0xc28]
    // 0xb1dd48: StoreField: r3->field_f = r16
    //     0xb1dd48: stur            w16, [x3, #0xf]
    // 0xb1dd4c: b               #0xb1dd5c
    // 0xb1dd50: ldur            x1, [fp, #-0x20]
    // 0xb1dd54: b               #0xb1dd5c
    // 0xb1dd58: ldur            x1, [fp, #-0x20]
    // 0xb1dd5c: ldur            x2, [fp, #-0x10]
    // 0xb1dd60: LoadField: r0 = r2->field_f
    //     0xb1dd60: ldur            w0, [x2, #0xf]
    // 0xb1dd64: DecompressPointer r0
    //     0xb1dd64: add             x0, x0, HEAP, lsl #32
    // 0xb1dd68: r3 = LoadInt32Instr(r0)
    //     0xb1dd68: sbfx            x3, x0, #1, #0x1f
    //     0xb1dd6c: tbz             w0, #0, #0xb1dd74
    //     0xb1dd70: ldur            x3, [x0, #7]
    // 0xb1dd74: cmp             x3, #0
    // 0xb1dd78: b.le            #0xb1df04
    // 0xb1dd7c: ldur            x3, [fp, #-8]
    // 0xb1dd80: LoadField: r4 = r3->field_f
    //     0xb1dd80: ldur            w4, [x3, #0xf]
    // 0xb1dd84: DecompressPointer r4
    //     0xb1dd84: add             x4, x4, HEAP, lsl #32
    // 0xb1dd88: r5 = LoadClassIdInstr(r4)
    //     0xb1dd88: ldur            x5, [x4, #-1]
    //     0xb1dd8c: ubfx            x5, x5, #0xc, #0x14
    // 0xb1dd90: stp             x0, x4, [SP]
    // 0xb1dd94: mov             x0, x5
    // 0xb1dd98: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1dd98: movz            x17, #0x3037
    //     0xb1dd9c: movk            x17, #0x1, lsl #16
    //     0xb1dda0: add             lr, x0, x17
    //     0xb1dda4: ldr             lr, [x21, lr, lsl #3]
    //     0xb1dda8: blr             lr
    // 0xb1ddac: LoadField: r2 = r0->field_2b
    //     0xb1ddac: ldur            x2, [x0, #0x2b]
    // 0xb1ddb0: ldur            x3, [fp, #-8]
    // 0xb1ddb4: stur            x2, [fp, #-0x28]
    // 0xb1ddb8: LoadField: r4 = r3->field_f
    //     0xb1ddb8: ldur            w4, [x3, #0xf]
    // 0xb1ddbc: DecompressPointer r4
    //     0xb1ddbc: add             x4, x4, HEAP, lsl #32
    // 0xb1ddc0: ldur            x5, [fp, #-0x10]
    // 0xb1ddc4: LoadField: r0 = r5->field_f
    //     0xb1ddc4: ldur            w0, [x5, #0xf]
    // 0xb1ddc8: DecompressPointer r0
    //     0xb1ddc8: add             x0, x0, HEAP, lsl #32
    // 0xb1ddcc: r1 = LoadInt32Instr(r0)
    //     0xb1ddcc: sbfx            x1, x0, #1, #0x1f
    //     0xb1ddd0: tbz             w0, #0, #0xb1ddd8
    //     0xb1ddd4: ldur            x1, [x0, #7]
    // 0xb1ddd8: sub             x6, x1, #1
    // 0xb1dddc: r0 = BoxInt64Instr(r6)
    //     0xb1dddc: sbfiz           x0, x6, #1, #0x1f
    //     0xb1dde0: cmp             x6, x0, asr #1
    //     0xb1dde4: b.eq            #0xb1ddf0
    //     0xb1dde8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1ddec: stur            x6, [x0, #7]
    // 0xb1ddf0: r1 = LoadClassIdInstr(r4)
    //     0xb1ddf0: ldur            x1, [x4, #-1]
    //     0xb1ddf4: ubfx            x1, x1, #0xc, #0x14
    // 0xb1ddf8: stp             x0, x4, [SP]
    // 0xb1ddfc: mov             x0, x1
    // 0xb1de00: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1de00: movz            x17, #0x3037
    //     0xb1de04: movk            x17, #0x1, lsl #16
    //     0xb1de08: add             lr, x0, x17
    //     0xb1de0c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1de10: blr             lr
    // 0xb1de14: LoadField: r1 = r0->field_2b
    //     0xb1de14: ldur            x1, [x0, #0x2b]
    // 0xb1de18: ldur            x0, [fp, #-0x28]
    // 0xb1de1c: cmp             x0, x1
    // 0xb1de20: b.eq            #0xb1defc
    // 0xb1de24: ldur            x0, [fp, #-8]
    // 0xb1de28: ldur            x2, [fp, #-0x10]
    // 0xb1de2c: ldur            x1, [fp, #-0x20]
    // 0xb1de30: LoadField: r3 = r0->field_f
    //     0xb1de30: ldur            w3, [x0, #0xf]
    // 0xb1de34: DecompressPointer r3
    //     0xb1de34: add             x3, x3, HEAP, lsl #32
    // 0xb1de38: LoadField: r0 = r2->field_f
    //     0xb1de38: ldur            w0, [x2, #0xf]
    // 0xb1de3c: DecompressPointer r0
    //     0xb1de3c: add             x0, x0, HEAP, lsl #32
    // 0xb1de40: r4 = LoadClassIdInstr(r3)
    //     0xb1de40: ldur            x4, [x3, #-1]
    //     0xb1de44: ubfx            x4, x4, #0xc, #0x14
    // 0xb1de48: stp             x0, x3, [SP]
    // 0xb1de4c: mov             x0, x4
    // 0xb1de50: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1de50: movz            x17, #0x3037
    //     0xb1de54: movk            x17, #0x1, lsl #16
    //     0xb1de58: add             lr, x0, x17
    //     0xb1de5c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1de60: blr             lr
    // 0xb1de64: LoadField: r1 = r0->field_2b
    //     0xb1de64: ldur            x1, [x0, #0x2b]
    // 0xb1de68: stur            x1, [fp, #-0x28]
    // 0xb1de6c: r0 = JuzDivider()
    //     0xb1de6c: bl              #0xb1e074  ; AllocateJuzDividerStub -> JuzDivider (size=0x14)
    // 0xb1de70: mov             x2, x0
    // 0xb1de74: ldur            x0, [fp, #-0x28]
    // 0xb1de78: stur            x2, [fp, #-8]
    // 0xb1de7c: StoreField: r2->field_b = r0
    //     0xb1de7c: stur            x0, [x2, #0xb]
    // 0xb1de80: ldur            x0, [fp, #-0x20]
    // 0xb1de84: LoadField: r1 = r0->field_b
    //     0xb1de84: ldur            w1, [x0, #0xb]
    // 0xb1de88: LoadField: r3 = r0->field_f
    //     0xb1de88: ldur            w3, [x0, #0xf]
    // 0xb1de8c: DecompressPointer r3
    //     0xb1de8c: add             x3, x3, HEAP, lsl #32
    // 0xb1de90: LoadField: r4 = r3->field_b
    //     0xb1de90: ldur            w4, [x3, #0xb]
    // 0xb1de94: r3 = LoadInt32Instr(r1)
    //     0xb1de94: sbfx            x3, x1, #1, #0x1f
    // 0xb1de98: stur            x3, [fp, #-0x28]
    // 0xb1de9c: r1 = LoadInt32Instr(r4)
    //     0xb1de9c: sbfx            x1, x4, #1, #0x1f
    // 0xb1dea0: cmp             x3, x1
    // 0xb1dea4: b.ne            #0xb1deb0
    // 0xb1dea8: mov             x1, x0
    // 0xb1deac: r0 = _growToNextCapacity()
    //     0xb1deac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1deb0: ldur            x2, [fp, #-0x20]
    // 0xb1deb4: ldur            x3, [fp, #-0x28]
    // 0xb1deb8: add             x0, x3, #1
    // 0xb1debc: lsl             x1, x0, #1
    // 0xb1dec0: StoreField: r2->field_b = r1
    //     0xb1dec0: stur            w1, [x2, #0xb]
    // 0xb1dec4: LoadField: r1 = r2->field_f
    //     0xb1dec4: ldur            w1, [x2, #0xf]
    // 0xb1dec8: DecompressPointer r1
    //     0xb1dec8: add             x1, x1, HEAP, lsl #32
    // 0xb1decc: ldur            x0, [fp, #-8]
    // 0xb1ded0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1ded0: add             x25, x1, x3, lsl #2
    //     0xb1ded4: add             x25, x25, #0xf
    //     0xb1ded8: str             w0, [x25]
    //     0xb1dedc: tbz             w0, #0, #0xb1def8
    //     0xb1dee0: ldurb           w16, [x1, #-1]
    //     0xb1dee4: ldurb           w17, [x0, #-1]
    //     0xb1dee8: and             x16, x17, x16, lsr #2
    //     0xb1deec: tst             x16, HEAP, lsr #32
    //     0xb1def0: b.eq            #0xb1def8
    //     0xb1def4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb1def8: b               #0xb1df08
    // 0xb1defc: ldur            x2, [fp, #-0x20]
    // 0xb1df00: b               #0xb1df08
    // 0xb1df04: mov             x2, x1
    // 0xb1df08: r1 = <Widget>
    //     0xb1df08: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1df0c: r0 = _GrowableList._ofGrowableList()
    //     0xb1df0c: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xb1df10: stur            x0, [fp, #-8]
    // 0xb1df14: r0 = Obx()
    //     0xb1df14: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb1df18: ldur            x2, [fp, #-0x10]
    // 0xb1df1c: r1 = Function '<anonymous closure>':.
    //     0xb1df1c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f378] AnonymousClosure: (0xb1e08c), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1df20: ldr             x1, [x1, #0x378]
    // 0xb1df24: stur            x0, [fp, #-0x10]
    // 0xb1df28: r0 = AllocateClosure()
    //     0xb1df28: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1df2c: mov             x1, x0
    // 0xb1df30: ldur            x0, [fp, #-0x10]
    // 0xb1df34: StoreField: r0->field_b = r1
    //     0xb1df34: stur            w1, [x0, #0xb]
    // 0xb1df38: ldur            x2, [fp, #-8]
    // 0xb1df3c: LoadField: r1 = r2->field_b
    //     0xb1df3c: ldur            w1, [x2, #0xb]
    // 0xb1df40: LoadField: r3 = r2->field_f
    //     0xb1df40: ldur            w3, [x2, #0xf]
    // 0xb1df44: DecompressPointer r3
    //     0xb1df44: add             x3, x3, HEAP, lsl #32
    // 0xb1df48: LoadField: r4 = r3->field_b
    //     0xb1df48: ldur            w4, [x3, #0xb]
    // 0xb1df4c: r3 = LoadInt32Instr(r1)
    //     0xb1df4c: sbfx            x3, x1, #1, #0x1f
    // 0xb1df50: stur            x3, [fp, #-0x28]
    // 0xb1df54: r1 = LoadInt32Instr(r4)
    //     0xb1df54: sbfx            x1, x4, #1, #0x1f
    // 0xb1df58: cmp             x3, x1
    // 0xb1df5c: b.ne            #0xb1df68
    // 0xb1df60: mov             x1, x2
    // 0xb1df64: r0 = _growToNextCapacity()
    //     0xb1df64: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1df68: ldur            x2, [fp, #-8]
    // 0xb1df6c: ldur            x3, [fp, #-0x28]
    // 0xb1df70: add             x4, x3, #1
    // 0xb1df74: stur            x4, [fp, #-0x38]
    // 0xb1df78: lsl             x0, x4, #1
    // 0xb1df7c: StoreField: r2->field_b = r0
    //     0xb1df7c: stur            w0, [x2, #0xb]
    // 0xb1df80: LoadField: r5 = r2->field_f
    //     0xb1df80: ldur            w5, [x2, #0xf]
    // 0xb1df84: DecompressPointer r5
    //     0xb1df84: add             x5, x5, HEAP, lsl #32
    // 0xb1df88: mov             x1, x5
    // 0xb1df8c: ldur            x0, [fp, #-0x10]
    // 0xb1df90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1df90: add             x25, x1, x3, lsl #2
    //     0xb1df94: add             x25, x25, #0xf
    //     0xb1df98: str             w0, [x25]
    //     0xb1df9c: tbz             w0, #0, #0xb1dfb8
    //     0xb1dfa0: ldurb           w16, [x1, #-1]
    //     0xb1dfa4: ldurb           w17, [x0, #-1]
    //     0xb1dfa8: and             x16, x17, x16, lsr #2
    //     0xb1dfac: tst             x16, HEAP, lsr #32
    //     0xb1dfb0: b.eq            #0xb1dfb8
    //     0xb1dfb4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb1dfb8: LoadField: r0 = r5->field_b
    //     0xb1dfb8: ldur            w0, [x5, #0xb]
    // 0xb1dfbc: r1 = LoadInt32Instr(r0)
    //     0xb1dfbc: sbfx            x1, x0, #1, #0x1f
    // 0xb1dfc0: cmp             x4, x1
    // 0xb1dfc4: b.ne            #0xb1dfd0
    // 0xb1dfc8: mov             x1, x2
    // 0xb1dfcc: r0 = _growToNextCapacity()
    //     0xb1dfcc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1dfd0: ldur            x1, [fp, #-0x38]
    // 0xb1dfd4: ldur            x0, [fp, #-8]
    // 0xb1dfd8: add             x2, x1, #1
    // 0xb1dfdc: lsl             x3, x2, #1
    // 0xb1dfe0: StoreField: r0->field_b = r3
    //     0xb1dfe0: stur            w3, [x0, #0xb]
    // 0xb1dfe4: LoadField: r2 = r0->field_f
    //     0xb1dfe4: ldur            w2, [x0, #0xf]
    // 0xb1dfe8: DecompressPointer r2
    //     0xb1dfe8: add             x2, x2, HEAP, lsl #32
    // 0xb1dfec: add             x3, x2, x1, lsl #2
    // 0xb1dff0: r16 = Instance_Divider
    //     0xb1dff0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb1dff4: ldr             x16, [x16, #0xc28]
    // 0xb1dff8: StoreField: r3->field_f = r16
    //     0xb1dff8: stur            w16, [x3, #0xf]
    // 0xb1dffc: r0 = Column()
    //     0xb1dffc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1e000: r1 = Instance_Axis
    //     0xb1e000: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb1e004: StoreField: r0->field_f = r1
    //     0xb1e004: stur            w1, [x0, #0xf]
    // 0xb1e008: r1 = Instance_MainAxisAlignment
    //     0xb1e008: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb1e00c: ldr             x1, [x1, #0x730]
    // 0xb1e010: StoreField: r0->field_13 = r1
    //     0xb1e010: stur            w1, [x0, #0x13]
    // 0xb1e014: r1 = Instance_MainAxisSize
    //     0xb1e014: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb1e018: ldr             x1, [x1, #0xe88]
    // 0xb1e01c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1e01c: stur            w1, [x0, #0x17]
    // 0xb1e020: r1 = Instance_CrossAxisAlignment
    //     0xb1e020: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb1e024: ldr             x1, [x1, #0x740]
    // 0xb1e028: StoreField: r0->field_1b = r1
    //     0xb1e028: stur            w1, [x0, #0x1b]
    // 0xb1e02c: r1 = Instance_VerticalDirection
    //     0xb1e02c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb1e030: ldr             x1, [x1, #0x748]
    // 0xb1e034: StoreField: r0->field_23 = r1
    //     0xb1e034: stur            w1, [x0, #0x23]
    // 0xb1e038: r1 = Instance_Clip
    //     0xb1e038: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb1e03c: ldr             x1, [x1, #0x750]
    // 0xb1e040: StoreField: r0->field_2b = r1
    //     0xb1e040: stur            w1, [x0, #0x2b]
    // 0xb1e044: StoreField: r0->field_2f = rZR
    //     0xb1e044: stur            xzr, [x0, #0x2f]
    // 0xb1e048: ldur            x1, [fp, #-8]
    // 0xb1e04c: StoreField: r0->field_b = r1
    //     0xb1e04c: stur            w1, [x0, #0xb]
    // 0xb1e050: LeaveFrame
    //     0xb1e050: mov             SP, fp
    //     0xb1e054: ldp             fp, lr, [SP], #0x10
    // 0xb1e058: ret
    //     0xb1e058: ret             
    // 0xb1e05c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e05c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e060: b               #0xb1d920
    // 0xb1e064: r9 = tabController
    //     0xb1e064: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f310] Field <QuranListController.tabController>: late (offset: 0x58)
    //     0xb1e068: ldr             x9, [x9, #0x310]
    // 0xb1e06c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1e06c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb1e070: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1e070: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] NVerseList <anonymous closure>(dynamic) {
    // ** addr: 0xb1e08c, size: 0x1b4
    // 0xb1e08c: EnterFrame
    //     0xb1e08c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e090: mov             fp, SP
    // 0xb1e094: AllocStack(0x60)
    //     0xb1e094: sub             SP, SP, #0x60
    // 0xb1e098: SetupParameters()
    //     0xb1e098: ldr             x0, [fp, #0x10]
    //     0xb1e09c: ldur            w2, [x0, #0x17]
    //     0xb1e0a0: add             x2, x2, HEAP, lsl #32
    //     0xb1e0a4: stur            x2, [fp, #-0x20]
    // 0xb1e0a8: CheckStackOverflow
    //     0xb1e0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e0ac: cmp             SP, x16
    //     0xb1e0b0: b.ls            #0xb1e238
    // 0xb1e0b4: LoadField: r0 = r2->field_b
    //     0xb1e0b4: ldur            w0, [x2, #0xb]
    // 0xb1e0b8: DecompressPointer r0
    //     0xb1e0b8: add             x0, x0, HEAP, lsl #32
    // 0xb1e0bc: LoadField: r1 = r0->field_b
    //     0xb1e0bc: ldur            w1, [x0, #0xb]
    // 0xb1e0c0: DecompressPointer r1
    //     0xb1e0c0: add             x1, x1, HEAP, lsl #32
    // 0xb1e0c4: stur            x1, [fp, #-0x18]
    // 0xb1e0c8: LoadField: r3 = r1->field_f
    //     0xb1e0c8: ldur            w3, [x1, #0xf]
    // 0xb1e0cc: DecompressPointer r3
    //     0xb1e0cc: add             x3, x3, HEAP, lsl #32
    // 0xb1e0d0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb1e0d0: ldur            w4, [x3, #0x17]
    // 0xb1e0d4: DecompressPointer r4
    //     0xb1e0d4: add             x4, x4, HEAP, lsl #32
    // 0xb1e0d8: stur            x4, [fp, #-0x10]
    // 0xb1e0dc: LoadField: r5 = r3->field_1b
    //     0xb1e0dc: ldur            w5, [x3, #0x1b]
    // 0xb1e0e0: DecompressPointer r5
    //     0xb1e0e0: add             x5, x5, HEAP, lsl #32
    // 0xb1e0e4: stur            x5, [fp, #-8]
    // 0xb1e0e8: LoadField: d0 = r3->field_7
    //     0xb1e0e8: ldur            d0, [x3, #7]
    // 0xb1e0ec: stur            d0, [fp, #-0x50]
    // 0xb1e0f0: LoadField: d1 = r3->field_f
    //     0xb1e0f0: ldur            d1, [x3, #0xf]
    // 0xb1e0f4: stur            d1, [fp, #-0x48]
    // 0xb1e0f8: LoadField: r3 = r0->field_f
    //     0xb1e0f8: ldur            w3, [x0, #0xf]
    // 0xb1e0fc: DecompressPointer r3
    //     0xb1e0fc: add             x3, x3, HEAP, lsl #32
    // 0xb1e100: LoadField: r0 = r2->field_f
    //     0xb1e100: ldur            w0, [x2, #0xf]
    // 0xb1e104: DecompressPointer r0
    //     0xb1e104: add             x0, x0, HEAP, lsl #32
    // 0xb1e108: r6 = LoadClassIdInstr(r3)
    //     0xb1e108: ldur            x6, [x3, #-1]
    //     0xb1e10c: ubfx            x6, x6, #0xc, #0x14
    // 0xb1e110: stp             x0, x3, [SP]
    // 0xb1e114: mov             x0, x6
    // 0xb1e118: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1e118: movz            x17, #0x3037
    //     0xb1e11c: movk            x17, #0x1, lsl #16
    //     0xb1e120: add             lr, x0, x17
    //     0xb1e124: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e128: blr             lr
    // 0xb1e12c: mov             x2, x0
    // 0xb1e130: ldur            x0, [fp, #-0x18]
    // 0xb1e134: stur            x2, [fp, #-0x38]
    // 0xb1e138: LoadField: r3 = r0->field_13
    //     0xb1e138: ldur            w3, [x0, #0x13]
    // 0xb1e13c: DecompressPointer r3
    //     0xb1e13c: add             x3, x3, HEAP, lsl #32
    // 0xb1e140: stur            x3, [fp, #-0x30]
    // 0xb1e144: LoadField: r4 = r0->field_b
    //     0xb1e144: ldur            w4, [x0, #0xb]
    // 0xb1e148: DecompressPointer r4
    //     0xb1e148: add             x4, x4, HEAP, lsl #32
    // 0xb1e14c: stur            x4, [fp, #-0x28]
    // 0xb1e150: LoadField: r1 = r4->field_f
    //     0xb1e150: ldur            w1, [x4, #0xf]
    // 0xb1e154: DecompressPointer r1
    //     0xb1e154: add             x1, x1, HEAP, lsl #32
    // 0xb1e158: r0 = controller()
    //     0xb1e158: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e15c: LoadField: r1 = r0->field_53
    //     0xb1e15c: ldur            w1, [x0, #0x53]
    // 0xb1e160: DecompressPointer r1
    //     0xb1e160: add             x1, x1, HEAP, lsl #32
    // 0xb1e164: r0 = value()
    //     0xb1e164: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1e168: mov             x2, x0
    // 0xb1e16c: ldur            x0, [fp, #-0x28]
    // 0xb1e170: stur            x2, [fp, #-0x18]
    // 0xb1e174: LoadField: r1 = r0->field_f
    //     0xb1e174: ldur            w1, [x0, #0xf]
    // 0xb1e178: DecompressPointer r1
    //     0xb1e178: add             x1, x1, HEAP, lsl #32
    // 0xb1e17c: r0 = controller()
    //     0xb1e17c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e180: mov             x1, x0
    // 0xb1e184: LoadField: r0 = r1->field_7f
    //     0xb1e184: ldur            w0, [x1, #0x7f]
    // 0xb1e188: DecompressPointer r0
    //     0xb1e188: add             x0, x0, HEAP, lsl #32
    // 0xb1e18c: r16 = Sentinel
    //     0xb1e18c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1e190: cmp             w0, w16
    // 0xb1e194: b.ne            #0xb1e1a4
    // 0xb1e198: r2 = showTajweed
    //     0xb1e198: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f380] Field <QuranListController.showTajweed>: late final (offset: 0x80)
    //     0xb1e19c: ldr             x2, [x2, #0x380]
    // 0xb1e1a0: r0 = InitLateFinalInstanceField()
    //     0xb1e1a0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb1e1a4: mov             x1, x0
    // 0xb1e1a8: r0 = value()
    //     0xb1e1a8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1e1ac: stur            x0, [fp, #-0x28]
    // 0xb1e1b0: r0 = NVerseList()
    //     0xb1e1b0: bl              #0xb1e240  ; AllocateNVerseListStub -> NVerseList (size=0x44)
    // 0xb1e1b4: mov             x3, x0
    // 0xb1e1b8: ldur            x0, [fp, #-0x38]
    // 0xb1e1bc: stur            x3, [fp, #-0x40]
    // 0xb1e1c0: StoreField: r3->field_b = r0
    //     0xb1e1c0: stur            w0, [x3, #0xb]
    // 0xb1e1c4: ldur            x2, [fp, #-0x20]
    // 0xb1e1c8: r1 = Function '<anonymous closure>':.
    //     0xb1e1c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f388] AnonymousClosure: (0xb1e24c), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1e1cc: ldr             x1, [x1, #0x388]
    // 0xb1e1d0: r0 = AllocateClosure()
    //     0xb1e1d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1e1d4: mov             x1, x0
    // 0xb1e1d8: ldur            x0, [fp, #-0x40]
    // 0xb1e1dc: StoreField: r0->field_f = r1
    //     0xb1e1dc: stur            w1, [x0, #0xf]
    // 0xb1e1e0: ldur            x1, [fp, #-8]
    // 0xb1e1e4: StoreField: r0->field_13 = r1
    //     0xb1e1e4: stur            w1, [x0, #0x13]
    // 0xb1e1e8: ldur            x1, [fp, #-0x10]
    // 0xb1e1ec: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1e1ec: stur            w1, [x0, #0x17]
    // 0xb1e1f0: ldur            d0, [fp, #-0x50]
    // 0xb1e1f4: StoreField: r0->field_1b = d0
    //     0xb1e1f4: stur            d0, [x0, #0x1b]
    // 0xb1e1f8: ldur            d0, [fp, #-0x48]
    // 0xb1e1fc: StoreField: r0->field_23 = d0
    //     0xb1e1fc: stur            d0, [x0, #0x23]
    // 0xb1e200: ldur            x1, [fp, #-0x30]
    // 0xb1e204: StoreField: r0->field_2b = r1
    //     0xb1e204: stur            w1, [x0, #0x2b]
    // 0xb1e208: ldur            x1, [fp, #-0x18]
    // 0xb1e20c: r2 = LoadInt32Instr(r1)
    //     0xb1e20c: sbfx            x2, x1, #1, #0x1f
    //     0xb1e210: tbz             w1, #0, #0xb1e218
    //     0xb1e214: ldur            x2, [x1, #7]
    // 0xb1e218: StoreField: r0->field_2f = r2
    //     0xb1e218: stur            x2, [x0, #0x2f]
    // 0xb1e21c: ldur            x1, [fp, #-0x28]
    // 0xb1e220: StoreField: r0->field_37 = r1
    //     0xb1e220: stur            w1, [x0, #0x37]
    // 0xb1e224: r1 = true
    //     0xb1e224: add             x1, NULL, #0x20  ; true
    // 0xb1e228: StoreField: r0->field_3b = r1
    //     0xb1e228: stur            w1, [x0, #0x3b]
    // 0xb1e22c: LeaveFrame
    //     0xb1e22c: mov             SP, fp
    //     0xb1e230: ldp             fp, lr, [SP], #0x10
    // 0xb1e234: ret
    //     0xb1e234: ret             
    // 0xb1e238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e23c: b               #0xb1e0b4
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb1e24c, size: 0x384
    // 0xb1e24c: EnterFrame
    //     0xb1e24c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e250: mov             fp, SP
    // 0xb1e254: AllocStack(0x58)
    //     0xb1e254: sub             SP, SP, #0x58
    // 0xb1e258: SetupParameters(QuranListView this /* r1 */)
    //     0xb1e258: stur            NULL, [fp, #-8]
    //     0xb1e25c: movz            x0, #0
    //     0xb1e260: add             x1, fp, w0, sxtw #2
    //     0xb1e264: ldr             x1, [x1, #0x10]
    //     0xb1e268: ldur            w2, [x1, #0x17]
    //     0xb1e26c: add             x2, x2, HEAP, lsl #32
    //     0xb1e270: stur            x2, [fp, #-0x10]
    // 0xb1e274: CheckStackOverflow
    //     0xb1e274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e278: cmp             SP, x16
    //     0xb1e27c: b.ls            #0xb1e5c8
    // 0xb1e280: InitAsync() -> Future<void?>
    //     0xb1e280: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb1e284: bl              #0x661298  ; InitAsyncStub
    // 0xb1e288: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1e288: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1e28c: ldr             x0, [x0, #0x2670]
    //     0xb1e290: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1e294: cmp             w0, w16
    //     0xb1e298: b.ne            #0xb1e2a4
    //     0xb1e29c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1e2a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1e2a4: ldur            x2, [fp, #-0x10]
    // 0xb1e2a8: LoadField: r1 = r2->field_b
    //     0xb1e2a8: ldur            w1, [x2, #0xb]
    // 0xb1e2ac: DecompressPointer r1
    //     0xb1e2ac: add             x1, x1, HEAP, lsl #32
    // 0xb1e2b0: stur            x1, [fp, #-0x18]
    // 0xb1e2b4: LoadField: r0 = r1->field_f
    //     0xb1e2b4: ldur            w0, [x1, #0xf]
    // 0xb1e2b8: DecompressPointer r0
    //     0xb1e2b8: add             x0, x0, HEAP, lsl #32
    // 0xb1e2bc: LoadField: r3 = r2->field_f
    //     0xb1e2bc: ldur            w3, [x2, #0xf]
    // 0xb1e2c0: DecompressPointer r3
    //     0xb1e2c0: add             x3, x3, HEAP, lsl #32
    // 0xb1e2c4: r4 = LoadClassIdInstr(r0)
    //     0xb1e2c4: ldur            x4, [x0, #-1]
    //     0xb1e2c8: ubfx            x4, x4, #0xc, #0x14
    // 0xb1e2cc: stp             x3, x0, [SP]
    // 0xb1e2d0: mov             x0, x4
    // 0xb1e2d4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1e2d4: movz            x17, #0x3037
    //     0xb1e2d8: movk            x17, #0x1, lsl #16
    //     0xb1e2dc: add             lr, x0, x17
    //     0xb1e2e0: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e2e4: blr             lr
    // 0xb1e2e8: mov             x2, x0
    // 0xb1e2ec: ldur            x0, [fp, #-0x18]
    // 0xb1e2f0: stur            x2, [fp, #-0x28]
    // 0xb1e2f4: LoadField: r1 = r0->field_b
    //     0xb1e2f4: ldur            w1, [x0, #0xb]
    // 0xb1e2f8: DecompressPointer r1
    //     0xb1e2f8: add             x1, x1, HEAP, lsl #32
    // 0xb1e2fc: LoadField: r3 = r1->field_b
    //     0xb1e2fc: ldur            w3, [x1, #0xb]
    // 0xb1e300: DecompressPointer r3
    //     0xb1e300: add             x3, x3, HEAP, lsl #32
    // 0xb1e304: stur            x3, [fp, #-0x20]
    // 0xb1e308: LoadField: r1 = r3->field_f
    //     0xb1e308: ldur            w1, [x3, #0xf]
    // 0xb1e30c: DecompressPointer r1
    //     0xb1e30c: add             x1, x1, HEAP, lsl #32
    // 0xb1e310: r0 = controller()
    //     0xb1e310: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e314: LoadField: r1 = r0->field_3f
    //     0xb1e314: ldur            w1, [x0, #0x3f]
    // 0xb1e318: DecompressPointer r1
    //     0xb1e318: add             x1, x1, HEAP, lsl #32
    // 0xb1e31c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1e31c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1e320: r0 = toList()
    //     0xb1e320: bl              #0x862c84  ; [dart:collection] ListBase::toList
    // 0xb1e324: ldur            x2, [fp, #-0x10]
    // 0xb1e328: r1 = Function '<anonymous closure>':.
    //     0xb1e328: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f390] AnonymousClosure: (0xb1ef60), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1e32c: ldr             x1, [x1, #0x390]
    // 0xb1e330: stur            x0, [fp, #-0x30]
    // 0xb1e334: r0 = AllocateClosure()
    //     0xb1e334: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1e338: ldur            x1, [fp, #-0x30]
    // 0xb1e33c: mov             x2, x0
    // 0xb1e340: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1e340: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1e344: r0 = firstWhere()
    //     0xb1e344: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0xb1e348: LoadField: r1 = r0->field_23
    //     0xb1e348: ldur            w1, [x0, #0x23]
    // 0xb1e34c: DecompressPointer r1
    //     0xb1e34c: add             x1, x1, HEAP, lsl #32
    // 0xb1e350: ldur            x2, [fp, #-0x18]
    // 0xb1e354: stur            x1, [fp, #-0x30]
    // 0xb1e358: LoadField: r0 = r2->field_f
    //     0xb1e358: ldur            w0, [x2, #0xf]
    // 0xb1e35c: DecompressPointer r0
    //     0xb1e35c: add             x0, x0, HEAP, lsl #32
    // 0xb1e360: ldur            x3, [fp, #-0x10]
    // 0xb1e364: LoadField: r4 = r3->field_f
    //     0xb1e364: ldur            w4, [x3, #0xf]
    // 0xb1e368: DecompressPointer r4
    //     0xb1e368: add             x4, x4, HEAP, lsl #32
    // 0xb1e36c: r5 = LoadClassIdInstr(r0)
    //     0xb1e36c: ldur            x5, [x0, #-1]
    //     0xb1e370: ubfx            x5, x5, #0xc, #0x14
    // 0xb1e374: stp             x4, x0, [SP]
    // 0xb1e378: mov             x0, x5
    // 0xb1e37c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1e37c: movz            x17, #0x3037
    //     0xb1e380: movk            x17, #0x1, lsl #16
    //     0xb1e384: add             lr, x0, x17
    //     0xb1e388: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e38c: blr             lr
    // 0xb1e390: LoadField: r2 = r0->field_43
    //     0xb1e390: ldur            w2, [x0, #0x43]
    // 0xb1e394: DecompressPointer r2
    //     0xb1e394: add             x2, x2, HEAP, lsl #32
    // 0xb1e398: ldur            x0, [fp, #-0x20]
    // 0xb1e39c: stur            x2, [fp, #-0x38]
    // 0xb1e3a0: LoadField: r1 = r0->field_f
    //     0xb1e3a0: ldur            w1, [x0, #0xf]
    // 0xb1e3a4: DecompressPointer r1
    //     0xb1e3a4: add             x1, x1, HEAP, lsl #32
    // 0xb1e3a8: r0 = controller()
    //     0xb1e3a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e3ac: mov             x2, x0
    // 0xb1e3b0: ldur            x0, [fp, #-0x20]
    // 0xb1e3b4: stur            x2, [fp, #-0x40]
    // 0xb1e3b8: LoadField: r1 = r0->field_f
    //     0xb1e3b8: ldur            w1, [x0, #0xf]
    // 0xb1e3bc: DecompressPointer r1
    //     0xb1e3bc: add             x1, x1, HEAP, lsl #32
    // 0xb1e3c0: r0 = controller()
    //     0xb1e3c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e3c4: LoadField: r1 = r0->field_77
    //     0xb1e3c4: ldur            w1, [x0, #0x77]
    // 0xb1e3c8: DecompressPointer r1
    //     0xb1e3c8: add             x1, x1, HEAP, lsl #32
    // 0xb1e3cc: r0 = value()
    //     0xb1e3cc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1e3d0: cmp             w0, NULL
    // 0xb1e3d4: b.ne            #0xb1e3e0
    // 0xb1e3d8: r3 = Null
    //     0xb1e3d8: mov             x3, NULL
    // 0xb1e3dc: b               #0xb1e3fc
    // 0xb1e3e0: LoadField: r2 = r0->field_7
    //     0xb1e3e0: ldur            x2, [x0, #7]
    // 0xb1e3e4: r0 = BoxInt64Instr(r2)
    //     0xb1e3e4: sbfiz           x0, x2, #1, #0x1f
    //     0xb1e3e8: cmp             x2, x0, asr #1
    //     0xb1e3ec: b.eq            #0xb1e3f8
    //     0xb1e3f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1e3f4: stur            x2, [x0, #7]
    // 0xb1e3f8: mov             x3, x0
    // 0xb1e3fc: ldur            x2, [fp, #-0x10]
    // 0xb1e400: ldur            x0, [fp, #-0x18]
    // 0xb1e404: ldur            x1, [fp, #-0x20]
    // 0xb1e408: stur            x3, [fp, #-0x48]
    // 0xb1e40c: LoadField: r4 = r0->field_f
    //     0xb1e40c: ldur            w4, [x0, #0xf]
    // 0xb1e410: DecompressPointer r4
    //     0xb1e410: add             x4, x4, HEAP, lsl #32
    // 0xb1e414: LoadField: r0 = r2->field_f
    //     0xb1e414: ldur            w0, [x2, #0xf]
    // 0xb1e418: DecompressPointer r0
    //     0xb1e418: add             x0, x0, HEAP, lsl #32
    // 0xb1e41c: r2 = LoadClassIdInstr(r4)
    //     0xb1e41c: ldur            x2, [x4, #-1]
    //     0xb1e420: ubfx            x2, x2, #0xc, #0x14
    // 0xb1e424: stp             x0, x4, [SP]
    // 0xb1e428: mov             x0, x2
    // 0xb1e42c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1e42c: movz            x17, #0x3037
    //     0xb1e430: movk            x17, #0x1, lsl #16
    //     0xb1e434: add             lr, x0, x17
    //     0xb1e438: ldr             lr, [x21, lr, lsl #3]
    //     0xb1e43c: blr             lr
    // 0xb1e440: LoadField: r2 = r0->field_7
    //     0xb1e440: ldur            x2, [x0, #7]
    // 0xb1e444: r0 = BoxInt64Instr(r2)
    //     0xb1e444: sbfiz           x0, x2, #1, #0x1f
    //     0xb1e448: cmp             x2, x0, asr #1
    //     0xb1e44c: b.eq            #0xb1e458
    //     0xb1e450: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1e454: stur            x2, [x0, #7]
    // 0xb1e458: mov             x1, x0
    // 0xb1e45c: ldur            x0, [fp, #-0x48]
    // 0xb1e460: cmp             w0, w1
    // 0xb1e464: b.eq            #0xb1e4a8
    // 0xb1e468: and             w16, w0, w1
    // 0xb1e46c: branchIfSmi(r16, 0xb1e4a0)
    //     0xb1e46c: tbz             w16, #0, #0xb1e4a0
    // 0xb1e470: r16 = LoadClassIdInstr(r0)
    //     0xb1e470: ldur            x16, [x0, #-1]
    //     0xb1e474: ubfx            x16, x16, #0xc, #0x14
    // 0xb1e478: cmp             x16, #0x3d
    // 0xb1e47c: b.ne            #0xb1e4a0
    // 0xb1e480: r16 = LoadClassIdInstr(r1)
    //     0xb1e480: ldur            x16, [x1, #-1]
    //     0xb1e484: ubfx            x16, x16, #0xc, #0x14
    // 0xb1e488: cmp             x16, #0x3d
    // 0xb1e48c: b.ne            #0xb1e4a0
    // 0xb1e490: LoadField: r16 = r0->field_7
    //     0xb1e490: ldur            x16, [x0, #7]
    // 0xb1e494: LoadField: r17 = r1->field_7
    //     0xb1e494: ldur            x17, [x1, #7]
    // 0xb1e498: cmp             x16, x17
    // 0xb1e49c: b.eq            #0xb1e4a8
    // 0xb1e4a0: r2 = false
    //     0xb1e4a0: add             x2, NULL, #0x30  ; false
    // 0xb1e4a4: b               #0xb1e4ac
    // 0xb1e4a8: r2 = true
    //     0xb1e4a8: add             x2, NULL, #0x20  ; true
    // 0xb1e4ac: ldur            x0, [fp, #-0x20]
    // 0xb1e4b0: stur            x2, [fp, #-0x10]
    // 0xb1e4b4: LoadField: r1 = r0->field_f
    //     0xb1e4b4: ldur            w1, [x0, #0xf]
    // 0xb1e4b8: DecompressPointer r1
    //     0xb1e4b8: add             x1, x1, HEAP, lsl #32
    // 0xb1e4bc: r0 = controller()
    //     0xb1e4bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e4c0: LoadField: r1 = r0->field_3b
    //     0xb1e4c0: ldur            w1, [x0, #0x3b]
    // 0xb1e4c4: DecompressPointer r1
    //     0xb1e4c4: add             x1, x1, HEAP, lsl #32
    // 0xb1e4c8: r16 = "surahId"
    //     0xb1e4c8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f8] "surahId"
    //     0xb1e4cc: ldr             x16, [x16, #0xf8]
    // 0xb1e4d0: stp             x16, x1, [SP]
    // 0xb1e4d4: r4 = 0
    //     0xb1e4d4: movz            x4, #0
    // 0xb1e4d8: ldr             x0, [SP, #8]
    // 0xb1e4dc: r16 = UnlinkedCall_0x5f3c08
    //     0xb1e4dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f398] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xb1e4e0: add             x16, x16, #0x398
    // 0xb1e4e4: ldp             x5, lr, [x16]
    // 0xb1e4e8: blr             lr
    // 0xb1e4ec: cmp             w0, NULL
    // 0xb1e4f0: b.eq            #0xb1e500
    // 0xb1e4f4: r5 = "list_surah"
    //     0xb1e4f4: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f2e8] "list_surah"
    //     0xb1e4f8: ldr             x5, [x5, #0x2e8]
    // 0xb1e4fc: b               #0xb1e508
    // 0xb1e500: r5 = "list_juz"
    //     0xb1e500: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f2f0] "list_juz"
    //     0xb1e504: ldr             x5, [x5, #0x2f0]
    // 0xb1e508: ldur            x4, [fp, #-0x28]
    // 0xb1e50c: ldur            x1, [fp, #-0x20]
    // 0xb1e510: ldur            x3, [fp, #-0x30]
    // 0xb1e514: ldur            x2, [fp, #-0x38]
    // 0xb1e518: ldur            x0, [fp, #-0x10]
    // 0xb1e51c: stur            x5, [fp, #-0x18]
    // 0xb1e520: LoadField: r6 = r1->field_f
    //     0xb1e520: ldur            w6, [x1, #0xf]
    // 0xb1e524: DecompressPointer r6
    //     0xb1e524: add             x6, x6, HEAP, lsl #32
    // 0xb1e528: mov             x1, x6
    // 0xb1e52c: r0 = controller()
    //     0xb1e52c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1e530: r1 = <QuranBottomSheetController>
    //     0xb1e530: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b510] TypeArguments: <QuranBottomSheetController>
    //     0xb1e534: ldr             x1, [x1, #0x510]
    // 0xb1e538: stur            x0, [fp, #-0x20]
    // 0xb1e53c: r0 = NQuranBottomSheet()
    //     0xb1e53c: bl              #0xb1e5d0  ; AllocateNQuranBottomSheetStub -> NQuranBottomSheet (size=0x34)
    // 0xb1e540: mov             x3, x0
    // 0xb1e544: ldur            x0, [fp, #-0x28]
    // 0xb1e548: stur            x3, [fp, #-0x48]
    // 0xb1e54c: StoreField: r3->field_13 = r0
    //     0xb1e54c: stur            w0, [x3, #0x13]
    // 0xb1e550: ldur            x0, [fp, #-0x30]
    // 0xb1e554: ArrayStore: r3[0] = r0  ; List_4
    //     0xb1e554: stur            w0, [x3, #0x17]
    // 0xb1e558: ldur            x0, [fp, #-0x38]
    // 0xb1e55c: StoreField: r3->field_1b = r0
    //     0xb1e55c: stur            w0, [x3, #0x1b]
    // 0xb1e560: ldur            x0, [fp, #-0x10]
    // 0xb1e564: StoreField: r3->field_23 = r0
    //     0xb1e564: stur            w0, [x3, #0x23]
    // 0xb1e568: r0 = "/quran/quran-list"
    //     0xb1e568: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c1d8] "/quran/quran-list"
    //     0xb1e56c: ldr             x0, [x0, #0x1d8]
    // 0xb1e570: StoreField: r3->field_27 = r0
    //     0xb1e570: stur            w0, [x3, #0x27]
    // 0xb1e574: ldur            x2, [fp, #-0x40]
    // 0xb1e578: r1 = Function 'onBookmark':.
    //     0xb1e578: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f3a8] AnonymousClosure: (0xb1ec88), in [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::onBookmark (0xb1ecc8)
    //     0xb1e57c: ldr             x1, [x1, #0x3a8]
    // 0xb1e580: r0 = AllocateClosure()
    //     0xb1e580: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1e584: mov             x1, x0
    // 0xb1e588: ldur            x0, [fp, #-0x48]
    // 0xb1e58c: StoreField: r0->field_1f = r1
    //     0xb1e58c: stur            w1, [x0, #0x1f]
    // 0xb1e590: ldur            x1, [fp, #-0x18]
    // 0xb1e594: StoreField: r0->field_2b = r1
    //     0xb1e594: stur            w1, [x0, #0x2b]
    // 0xb1e598: ldur            x2, [fp, #-0x20]
    // 0xb1e59c: r1 = Function 'onPlayPressed':.
    //     0xb1e59c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f3b0] AnonymousClosure: (0xb1e5dc), in [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::onPlayPressed (0xb1e618)
    //     0xb1e5a0: ldr             x1, [x1, #0x3b0]
    // 0xb1e5a4: r0 = AllocateClosure()
    //     0xb1e5a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1e5a8: mov             x1, x0
    // 0xb1e5ac: ldur            x0, [fp, #-0x48]
    // 0xb1e5b0: StoreField: r0->field_2f = r1
    //     0xb1e5b0: stur            w1, [x0, #0x2f]
    // 0xb1e5b4: r16 = <void?>
    //     0xb1e5b4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xb1e5b8: stp             x0, x16, [SP]
    // 0xb1e5bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1e5bc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1e5c0: r0 = ExtensionBottomSheet.bottomSheet()
    //     0xb1e5c0: bl              #0x917724  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionBottomSheet.bottomSheet
    // 0xb1e5c4: r0 = ReturnAsync()
    //     0xb1e5c4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xb1e5c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e5c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e5cc: b               #0xb1e280
  }
  [closure] bool <anonymous closure>(dynamic, Surah) {
    // ** addr: 0xb1ef60, size: 0x98
    // 0xb1ef60: EnterFrame
    //     0xb1ef60: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ef64: mov             fp, SP
    // 0xb1ef68: AllocStack(0x18)
    //     0xb1ef68: sub             SP, SP, #0x18
    // 0xb1ef6c: SetupParameters()
    //     0xb1ef6c: ldr             x0, [fp, #0x18]
    //     0xb1ef70: ldur            w1, [x0, #0x17]
    //     0xb1ef74: add             x1, x1, HEAP, lsl #32
    // 0xb1ef78: CheckStackOverflow
    //     0xb1ef78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ef7c: cmp             SP, x16
    //     0xb1ef80: b.ls            #0xb1eff0
    // 0xb1ef84: ldr             x0, [fp, #0x10]
    // 0xb1ef88: LoadField: r2 = r0->field_13
    //     0xb1ef88: ldur            x2, [x0, #0x13]
    // 0xb1ef8c: stur            x2, [fp, #-8]
    // 0xb1ef90: LoadField: r0 = r1->field_b
    //     0xb1ef90: ldur            w0, [x1, #0xb]
    // 0xb1ef94: DecompressPointer r0
    //     0xb1ef94: add             x0, x0, HEAP, lsl #32
    // 0xb1ef98: LoadField: r3 = r0->field_f
    //     0xb1ef98: ldur            w3, [x0, #0xf]
    // 0xb1ef9c: DecompressPointer r3
    //     0xb1ef9c: add             x3, x3, HEAP, lsl #32
    // 0xb1efa0: LoadField: r0 = r1->field_f
    //     0xb1efa0: ldur            w0, [x1, #0xf]
    // 0xb1efa4: DecompressPointer r0
    //     0xb1efa4: add             x0, x0, HEAP, lsl #32
    // 0xb1efa8: r1 = LoadClassIdInstr(r3)
    //     0xb1efa8: ldur            x1, [x3, #-1]
    //     0xb1efac: ubfx            x1, x1, #0xc, #0x14
    // 0xb1efb0: stp             x0, x3, [SP]
    // 0xb1efb4: mov             x0, x1
    // 0xb1efb8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb1efb8: movz            x17, #0x3037
    //     0xb1efbc: movk            x17, #0x1, lsl #16
    //     0xb1efc0: add             lr, x0, x17
    //     0xb1efc4: ldr             lr, [x21, lr, lsl #3]
    //     0xb1efc8: blr             lr
    // 0xb1efcc: LoadField: r1 = r0->field_33
    //     0xb1efcc: ldur            x1, [x0, #0x33]
    // 0xb1efd0: ldur            x2, [fp, #-8]
    // 0xb1efd4: cmp             x2, x1
    // 0xb1efd8: r16 = true
    //     0xb1efd8: add             x16, NULL, #0x20  ; true
    // 0xb1efdc: r17 = false
    //     0xb1efdc: add             x17, NULL, #0x30  ; false
    // 0xb1efe0: csel            x0, x16, x17, eq
    // 0xb1efe4: LeaveFrame
    //     0xb1efe4: mov             SP, fp
    //     0xb1efe8: ldp             fp, lr, [SP], #0x10
    // 0xb1efec: ret
    //     0xb1efec: ret             
    // 0xb1eff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1eff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1eff4: b               #0xb1ef84
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1f0f8, size: 0xb0
    // 0xb1f0f8: EnterFrame
    //     0xb1f0f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f0fc: mov             fp, SP
    // 0xb1f100: AllocStack(0x20)
    //     0xb1f100: sub             SP, SP, #0x20
    // 0xb1f104: SetupParameters()
    //     0xb1f104: ldr             x0, [fp, #0x10]
    //     0xb1f108: ldur            w1, [x0, #0x17]
    //     0xb1f10c: add             x1, x1, HEAP, lsl #32
    //     0xb1f110: stur            x1, [fp, #-8]
    // 0xb1f114: CheckStackOverflow
    //     0xb1f114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f118: cmp             SP, x16
    //     0xb1f11c: b.ls            #0xb1f1a0
    // 0xb1f120: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1f120: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1f124: ldr             x0, [x0, #0x2670]
    //     0xb1f128: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1f12c: cmp             w0, w16
    //     0xb1f130: b.ne            #0xb1f13c
    //     0xb1f134: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1f138: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1f13c: r16 = "/quran/quran-setting"
    //     0xb1f13c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1b0] "/quran/quran-setting"
    //     0xb1f140: ldr             x16, [x16, #0x1b0]
    // 0xb1f144: stp             x16, NULL, [SP]
    // 0xb1f148: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1f148: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1f14c: r0 = GetNavigation.toNamed()
    //     0xb1f14c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb1f150: stur            x0, [fp, #-0x10]
    // 0xb1f154: cmp             w0, NULL
    // 0xb1f158: b.ne            #0xb1f164
    // 0xb1f15c: r0 = Null
    //     0xb1f15c: mov             x0, NULL
    // 0xb1f160: b               #0xb1f194
    // 0xb1f164: ldur            x1, [fp, #-8]
    // 0xb1f168: LoadField: r2 = r1->field_f
    //     0xb1f168: ldur            w2, [x1, #0xf]
    // 0xb1f16c: DecompressPointer r2
    //     0xb1f16c: add             x2, x2, HEAP, lsl #32
    // 0xb1f170: mov             x1, x2
    // 0xb1f174: r0 = controller()
    //     0xb1f174: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1f178: mov             x2, x0
    // 0xb1f17c: r1 = Function 'updateShowTajweed':.
    //     0xb1f17c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f3f8] AnonymousClosure: (0xb1f1a8), in [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::updateShowTajweed (0xb1f1e0)
    //     0xb1f180: ldr             x1, [x1, #0x3f8]
    // 0xb1f184: r0 = AllocateClosure()
    //     0xb1f184: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f188: ldur            x1, [fp, #-0x10]
    // 0xb1f18c: mov             x2, x0
    // 0xb1f190: r0 = whenComplete()
    //     0xb1f190: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0xb1f194: LeaveFrame
    //     0xb1f194: mov             SP, fp
    //     0xb1f198: ldp             fp, lr, [SP], #0x10
    // 0xb1f19c: ret
    //     0xb1f19c: ret             
    // 0xb1f1a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f1a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f1a4: b               #0xb1f120
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1f2f4, size: 0x8c
    // 0xb1f2f4: EnterFrame
    //     0xb1f2f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f2f8: mov             fp, SP
    // 0xb1f2fc: AllocStack(0x20)
    //     0xb1f2fc: sub             SP, SP, #0x20
    // 0xb1f300: SetupParameters()
    //     0xb1f300: ldr             x0, [fp, #0x10]
    //     0xb1f304: ldur            w1, [x0, #0x17]
    //     0xb1f308: add             x1, x1, HEAP, lsl #32
    //     0xb1f30c: stur            x1, [fp, #-8]
    // 0xb1f310: CheckStackOverflow
    //     0xb1f310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f314: cmp             SP, x16
    //     0xb1f318: b.ls            #0xb1f378
    // 0xb1f31c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1f31c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1f320: ldr             x0, [x0, #0x2670]
    //     0xb1f324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1f328: cmp             w0, w16
    //     0xb1f32c: b.ne            #0xb1f338
    //     0xb1f330: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1f334: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1f338: ldur            x0, [fp, #-8]
    // 0xb1f33c: LoadField: r1 = r0->field_f
    //     0xb1f33c: ldur            w1, [x0, #0xf]
    // 0xb1f340: DecompressPointer r1
    //     0xb1f340: add             x1, x1, HEAP, lsl #32
    // 0xb1f344: r0 = controller()
    //     0xb1f344: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1f348: mov             x1, x0
    // 0xb1f34c: r0 = pageParam()
    //     0xb1f34c: bl              #0xb1f380  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::pageParam
    // 0xb1f350: r16 = "/quran/quran-page"
    //     0xb1f350: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bc80] "/quran/quran-page"
    //     0xb1f354: ldr             x16, [x16, #0xc80]
    // 0xb1f358: stp             x16, NULL, [SP, #8]
    // 0xb1f35c: str             x0, [SP]
    // 0xb1f360: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb1f360: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb1f364: ldr             x4, [x4, #0x478]
    // 0xb1f368: r0 = GetNavigation.offAndToNamed()
    //     0xb1f368: bl              #0xb1bbb8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAndToNamed
    // 0xb1f36c: LeaveFrame
    //     0xb1f36c: mov             SP, fp
    //     0xb1f370: ldp             fp, lr, [SP], #0x10
    // 0xb1f374: ret
    //     0xb1f374: ret             
    // 0xb1f378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f37c: b               #0xb1f31c
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0xb1f480, size: 0xec
    // 0xb1f480: EnterFrame
    //     0xb1f480: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f484: mov             fp, SP
    // 0xb1f488: AllocStack(0x10)
    //     0xb1f488: sub             SP, SP, #0x10
    // 0xb1f48c: SetupParameters()
    //     0xb1f48c: ldr             x0, [fp, #0x10]
    //     0xb1f490: ldur            w1, [x0, #0x17]
    //     0xb1f494: add             x1, x1, HEAP, lsl #32
    // 0xb1f498: CheckStackOverflow
    //     0xb1f498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f49c: cmp             SP, x16
    //     0xb1f4a0: b.ls            #0xb1f564
    // 0xb1f4a4: LoadField: r0 = r1->field_f
    //     0xb1f4a4: ldur            w0, [x1, #0xf]
    // 0xb1f4a8: DecompressPointer r0
    //     0xb1f4a8: add             x0, x0, HEAP, lsl #32
    // 0xb1f4ac: mov             x1, x0
    // 0xb1f4b0: r0 = controller()
    //     0xb1f4b0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1f4b4: mov             x1, x0
    // 0xb1f4b8: LoadField: r0 = r1->field_7f
    //     0xb1f4b8: ldur            w0, [x1, #0x7f]
    // 0xb1f4bc: DecompressPointer r0
    //     0xb1f4bc: add             x0, x0, HEAP, lsl #32
    // 0xb1f4c0: r16 = Sentinel
    //     0xb1f4c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1f4c4: cmp             w0, w16
    // 0xb1f4c8: b.ne            #0xb1f4d8
    // 0xb1f4cc: r2 = showTajweed
    //     0xb1f4cc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f380] Field <QuranListController.showTajweed>: late final (offset: 0x80)
    //     0xb1f4d0: ldr             x2, [x2, #0x380]
    // 0xb1f4d4: r0 = InitLateFinalInstanceField()
    //     0xb1f4d4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb1f4d8: mov             x1, x0
    // 0xb1f4dc: r0 = RxBoolExt.isFalse()
    //     0xb1f4dc: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xb1f4e0: tbnz            w0, #4, #0xb1f4f8
    // 0xb1f4e4: r0 = Instance_SizedBox
    //     0xb1f4e4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb1f4e8: ldr             x0, [x0, #0xc40]
    // 0xb1f4ec: LeaveFrame
    //     0xb1f4ec: mov             SP, fp
    //     0xb1f4f0: ldp             fp, lr, [SP], #0x10
    // 0xb1f4f4: ret
    //     0xb1f4f4: ret             
    // 0xb1f4f8: r1 = Function '<anonymous closure>':.
    //     0xb1f4f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f408] AnonymousClosure: (0xb1f56c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb1f4fc: ldr             x1, [x1, #0x408]
    // 0xb1f500: r2 = Null
    //     0xb1f500: mov             x2, NULL
    // 0xb1f504: r0 = AllocateClosure()
    //     0xb1f504: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f508: stur            x0, [fp, #-8]
    // 0xb1f50c: r0 = IconButton()
    //     0xb1f50c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1f510: mov             x1, x0
    // 0xb1f514: ldur            x0, [fp, #-8]
    // 0xb1f518: stur            x1, [fp, #-0x10]
    // 0xb1f51c: StoreField: r1->field_3b = r0
    //     0xb1f51c: stur            w0, [x1, #0x3b]
    // 0xb1f520: r0 = false
    //     0xb1f520: add             x0, NULL, #0x30  ; false
    // 0xb1f524: StoreField: r1->field_47 = r0
    //     0xb1f524: stur            w0, [x1, #0x47]
    // 0xb1f528: r0 = Instance_Icon
    //     0xb1f528: add             x0, PP, #0x27, lsl #12  ; [pp+0x27fd0] Obj!Icon@e247b1
    //     0xb1f52c: ldr             x0, [x0, #0xfd0]
    // 0xb1f530: StoreField: r1->field_1f = r0
    //     0xb1f530: stur            w0, [x1, #0x1f]
    // 0xb1f534: r0 = Instance__IconButtonVariant
    //     0xb1f534: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1f538: ldr             x0, [x0, #0xf78]
    // 0xb1f53c: StoreField: r1->field_63 = r0
    //     0xb1f53c: stur            w0, [x1, #0x63]
    // 0xb1f540: r0 = SizedBox()
    //     0xb1f540: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1f544: r1 = 36.000000
    //     0xb1f544: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb1f548: ldr             x1, [x1, #0x478]
    // 0xb1f54c: StoreField: r0->field_f = r1
    //     0xb1f54c: stur            w1, [x0, #0xf]
    // 0xb1f550: ldur            x1, [fp, #-0x10]
    // 0xb1f554: StoreField: r0->field_b = r1
    //     0xb1f554: stur            w1, [x0, #0xb]
    // 0xb1f558: LeaveFrame
    //     0xb1f558: mov             SP, fp
    //     0xb1f55c: ldp             fp, lr, [SP], #0x10
    // 0xb1f560: ret
    //     0xb1f560: ret             
    // 0xb1f564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f564: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f568: b               #0xb1f4a4
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb1f754, size: 0x154
    // 0xb1f754: EnterFrame
    //     0xb1f754: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f758: mov             fp, SP
    // 0xb1f75c: AllocStack(0x30)
    //     0xb1f75c: sub             SP, SP, #0x30
    // 0xb1f760: SetupParameters(QuranListView this /* r1 */)
    //     0xb1f760: stur            NULL, [fp, #-8]
    //     0xb1f764: movz            x0, #0
    //     0xb1f768: add             x1, fp, w0, sxtw #2
    //     0xb1f76c: ldr             x1, [x1, #0x10]
    //     0xb1f770: ldur            w2, [x1, #0x17]
    //     0xb1f774: add             x2, x2, HEAP, lsl #32
    //     0xb1f778: stur            x2, [fp, #-0x10]
    // 0xb1f77c: CheckStackOverflow
    //     0xb1f77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f780: cmp             SP, x16
    //     0xb1f784: b.ls            #0xb1f8a0
    // 0xb1f788: InitAsync() -> Future<void?>
    //     0xb1f788: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb1f78c: bl              #0x661298  ; InitAsyncStub
    // 0xb1f790: ldur            x2, [fp, #-0x10]
    // 0xb1f794: LoadField: r1 = r2->field_f
    //     0xb1f794: ldur            w1, [x2, #0xf]
    // 0xb1f798: DecompressPointer r1
    //     0xb1f798: add             x1, x1, HEAP, lsl #32
    // 0xb1f79c: r0 = controller()
    //     0xb1f79c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1f7a0: mov             x1, x0
    // 0xb1f7a4: r0 = resetSelectedSurah()
    //     0xb1f7a4: bl              #0xb1f8a8  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::resetSelectedSurah
    // 0xb1f7a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1f7a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1f7ac: ldr             x0, [x0, #0x2670]
    //     0xb1f7b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1f7b4: cmp             w0, w16
    //     0xb1f7b8: b.ne            #0xb1f7c4
    //     0xb1f7bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1f7c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1f7c4: r0 = Obx()
    //     0xb1f7c4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb1f7c8: ldur            x2, [fp, #-0x10]
    // 0xb1f7cc: r1 = Function '<anonymous closure>':.
    //     0xb1f7cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f410] AnonymousClosure: (0xb20168), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1f7d0: ldr             x1, [x1, #0x410]
    // 0xb1f7d4: stur            x0, [fp, #-0x18]
    // 0xb1f7d8: r0 = AllocateClosure()
    //     0xb1f7d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f7dc: mov             x1, x0
    // 0xb1f7e0: ldur            x0, [fp, #-0x18]
    // 0xb1f7e4: StoreField: r0->field_b = r1
    //     0xb1f7e4: stur            w1, [x0, #0xb]
    // 0xb1f7e8: r1 = Null
    //     0xb1f7e8: mov             x1, NULL
    // 0xb1f7ec: r2 = 2
    //     0xb1f7ec: movz            x2, #0x2
    // 0xb1f7f0: r0 = AllocateArray()
    //     0xb1f7f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1f7f4: mov             x2, x0
    // 0xb1f7f8: ldur            x0, [fp, #-0x18]
    // 0xb1f7fc: stur            x2, [fp, #-0x20]
    // 0xb1f800: StoreField: r2->field_f = r0
    //     0xb1f800: stur            w0, [x2, #0xf]
    // 0xb1f804: r1 = <Widget>
    //     0xb1f804: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb1f808: r0 = AllocateGrowableArray()
    //     0xb1f808: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1f80c: mov             x1, x0
    // 0xb1f810: ldur            x0, [fp, #-0x20]
    // 0xb1f814: stur            x1, [fp, #-0x18]
    // 0xb1f818: StoreField: r1->field_f = r0
    //     0xb1f818: stur            w0, [x1, #0xf]
    // 0xb1f81c: r0 = 2
    //     0xb1f81c: movz            x0, #0x2
    // 0xb1f820: StoreField: r1->field_b = r0
    //     0xb1f820: stur            w0, [x1, #0xb]
    // 0xb1f824: r0 = NDialog()
    //     0xb1f824: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xb1f828: mov             x3, x0
    // 0xb1f82c: r0 = "Pergi ke"
    //     0xb1f82c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b730] "Pergi ke"
    //     0xb1f830: ldr             x0, [x0, #0x730]
    // 0xb1f834: stur            x3, [fp, #-0x20]
    // 0xb1f838: StoreField: r3->field_b = r0
    //     0xb1f838: stur            w0, [x3, #0xb]
    // 0xb1f83c: ldur            x2, [fp, #-0x10]
    // 0xb1f840: r1 = Function '<anonymous closure>':.
    //     0xb1f840: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f418] AnonymousClosure: (0xb1fab0), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb1f844: ldr             x1, [x1, #0x418]
    // 0xb1f848: r0 = AllocateClosure()
    //     0xb1f848: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f84c: mov             x1, x0
    // 0xb1f850: ldur            x0, [fp, #-0x20]
    // 0xb1f854: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1f854: stur            w1, [x0, #0x17]
    // 0xb1f858: ldur            x1, [fp, #-0x18]
    // 0xb1f85c: StoreField: r0->field_f = r1
    //     0xb1f85c: stur            w1, [x0, #0xf]
    // 0xb1f860: r1 = Function '<anonymous closure>':.
    //     0xb1f860: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f420] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xb1f864: ldr             x1, [x1, #0x420]
    // 0xb1f868: r2 = Null
    //     0xb1f868: mov             x2, NULL
    // 0xb1f86c: r0 = AllocateClosure()
    //     0xb1f86c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f870: mov             x1, x0
    // 0xb1f874: ldur            x0, [fp, #-0x20]
    // 0xb1f878: StoreField: r0->field_1b = r1
    //     0xb1f878: stur            w1, [x0, #0x1b]
    // 0xb1f87c: r16 = <int>
    //     0xb1f87c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb1f880: stp             x0, x16, [SP]
    // 0xb1f884: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1f884: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1f888: r0 = ExtensionDialog.dialog()
    //     0xb1f888: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xb1f88c: mov             x1, x0
    // 0xb1f890: stur            x1, [fp, #-0x18]
    // 0xb1f894: r0 = Await()
    //     0xb1f894: bl              #0x661044  ; AwaitStub
    // 0xb1f898: r0 = Null
    //     0xb1f898: mov             x0, NULL
    // 0xb1f89c: r0 = ReturnAsyncNotFuture()
    //     0xb1f89c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb1f8a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f8a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f8a4: b               #0xb1f788
  }
  [closure] Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0xb1fab0, size: 0x4c
    // 0xb1fab0: EnterFrame
    //     0xb1fab0: stp             fp, lr, [SP, #-0x10]!
    //     0xb1fab4: mov             fp, SP
    // 0xb1fab8: ldr             x0, [fp, #0x10]
    // 0xb1fabc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1fabc: ldur            w1, [x0, #0x17]
    // 0xb1fac0: DecompressPointer r1
    //     0xb1fac0: add             x1, x1, HEAP, lsl #32
    // 0xb1fac4: CheckStackOverflow
    //     0xb1fac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1fac8: cmp             SP, x16
    //     0xb1facc: b.ls            #0xb1faf4
    // 0xb1fad0: LoadField: r0 = r1->field_f
    //     0xb1fad0: ldur            w0, [x1, #0xf]
    // 0xb1fad4: DecompressPointer r0
    //     0xb1fad4: add             x0, x0, HEAP, lsl #32
    // 0xb1fad8: mov             x1, x0
    // 0xb1fadc: r0 = controller()
    //     0xb1fadc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1fae0: mov             x1, x0
    // 0xb1fae4: r0 = jumpTo()
    //     0xb1fae4: bl              #0xb1fafc  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::jumpTo
    // 0xb1fae8: LeaveFrame
    //     0xb1fae8: mov             SP, fp
    //     0xb1faec: ldp             fp, lr, [SP], #0x10
    // 0xb1faf0: ret
    //     0xb1faf0: ret             
    // 0xb1faf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1faf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1faf8: b               #0xb1fad0
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb20168, size: 0x754
    // 0xb20168: EnterFrame
    //     0xb20168: stp             fp, lr, [SP, #-0x10]!
    //     0xb2016c: mov             fp, SP
    // 0xb20170: AllocStack(0x58)
    //     0xb20170: sub             SP, SP, #0x58
    // 0xb20174: SetupParameters()
    //     0xb20174: ldr             x0, [fp, #0x10]
    //     0xb20178: ldur            w2, [x0, #0x17]
    //     0xb2017c: add             x2, x2, HEAP, lsl #32
    //     0xb20180: stur            x2, [fp, #-8]
    // 0xb20184: CheckStackOverflow
    //     0xb20184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20188: cmp             SP, x16
    //     0xb2018c: b.ls            #0xb208b4
    // 0xb20190: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb20190: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb20194: ldr             x0, [x0, #0x2670]
    //     0xb20198: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2019c: cmp             w0, w16
    //     0xb201a0: b.ne            #0xb201ac
    //     0xb201a4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb201a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb201ac: r0 = GetNavigation.textTheme()
    //     0xb201ac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb201b0: LoadField: r1 = r0->field_27
    //     0xb201b0: ldur            w1, [x0, #0x27]
    // 0xb201b4: DecompressPointer r1
    //     0xb201b4: add             x1, x1, HEAP, lsl #32
    // 0xb201b8: cmp             w1, NULL
    // 0xb201bc: b.ne            #0xb201c8
    // 0xb201c0: r0 = Null
    //     0xb201c0: mov             x0, NULL
    // 0xb201c4: b               #0xb201e0
    // 0xb201c8: r16 = 14.000000
    //     0xb201c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb201cc: ldr             x16, [x16, #0x9a0]
    // 0xb201d0: str             x16, [SP]
    // 0xb201d4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb201d4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb201d8: ldr             x4, [x4, #0x88]
    // 0xb201dc: r0 = copyWith()
    //     0xb201dc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb201e0: ldur            x2, [fp, #-8]
    // 0xb201e4: stur            x0, [fp, #-0x10]
    // 0xb201e8: r0 = Text()
    //     0xb201e8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb201ec: mov             x2, x0
    // 0xb201f0: r0 = "Pilih Surah"
    //     0xb201f0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b768] "Pilih Surah"
    //     0xb201f4: ldr             x0, [x0, #0x768]
    // 0xb201f8: stur            x2, [fp, #-0x18]
    // 0xb201fc: StoreField: r2->field_b = r0
    //     0xb201fc: stur            w0, [x2, #0xb]
    // 0xb20200: ldur            x0, [fp, #-0x10]
    // 0xb20204: StoreField: r2->field_13 = r0
    //     0xb20204: stur            w0, [x2, #0x13]
    // 0xb20208: ldur            x0, [fp, #-8]
    // 0xb2020c: LoadField: r1 = r0->field_f
    //     0xb2020c: ldur            w1, [x0, #0xf]
    // 0xb20210: DecompressPointer r1
    //     0xb20210: add             x1, x1, HEAP, lsl #32
    // 0xb20214: r0 = controller()
    //     0xb20214: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20218: LoadField: r1 = r0->field_5f
    //     0xb20218: ldur            w1, [x0, #0x5f]
    // 0xb2021c: DecompressPointer r1
    //     0xb2021c: add             x1, x1, HEAP, lsl #32
    // 0xb20220: stur            x1, [fp, #-0x10]
    // 0xb20224: r0 = TextField()
    //     0xb20224: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb20228: mov             x3, x0
    // 0xb2022c: r0 = EditableText
    //     0xb2022c: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb20230: stur            x3, [fp, #-0x20]
    // 0xb20234: StoreField: r3->field_f = r0
    //     0xb20234: stur            w0, [x3, #0xf]
    // 0xb20238: ldur            x1, [fp, #-0x10]
    // 0xb2023c: StoreField: r3->field_13 = r1
    //     0xb2023c: stur            w1, [x3, #0x13]
    // 0xb20240: r1 = Instance_InputDecoration
    //     0xb20240: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c0] Obj!InputDecoration@e14321
    //     0xb20244: ldr             x1, [x1, #0x4c0]
    // 0xb20248: StoreField: r3->field_1b = r1
    //     0xb20248: stur            w1, [x3, #0x1b]
    // 0xb2024c: r4 = Instance_TextCapitalization
    //     0xb2024c: ldr             x4, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb20250: StoreField: r3->field_27 = r4
    //     0xb20250: stur            w4, [x3, #0x27]
    // 0xb20254: r5 = Instance_TextAlign
    //     0xb20254: ldr             x5, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb20258: StoreField: r3->field_33 = r5
    //     0xb20258: stur            w5, [x3, #0x33]
    // 0xb2025c: r6 = true
    //     0xb2025c: add             x6, NULL, #0x20  ; true
    // 0xb20260: StoreField: r3->field_6f = r6
    //     0xb20260: stur            w6, [x3, #0x6f]
    // 0xb20264: r7 = false
    //     0xb20264: add             x7, NULL, #0x30  ; false
    // 0xb20268: StoreField: r3->field_3f = r7
    //     0xb20268: stur            w7, [x3, #0x3f]
    // 0xb2026c: r8 = "•"
    //     0xb2026c: add             x8, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb20270: ldr             x8, [x8, #0x4c8]
    // 0xb20274: StoreField: r3->field_47 = r8
    //     0xb20274: stur            w8, [x3, #0x47]
    // 0xb20278: StoreField: r3->field_4b = r7
    //     0xb20278: stur            w7, [x3, #0x4b]
    // 0xb2027c: StoreField: r3->field_4f = r6
    //     0xb2027c: stur            w6, [x3, #0x4f]
    // 0xb20280: StoreField: r3->field_5b = r6
    //     0xb20280: stur            w6, [x3, #0x5b]
    // 0xb20284: r9 = 1
    //     0xb20284: movz            x9, #0x1
    // 0xb20288: StoreField: r3->field_5f = r9
    //     0xb20288: stur            x9, [x3, #0x5f]
    // 0xb2028c: StoreField: r3->field_6b = r7
    //     0xb2028c: stur            w7, [x3, #0x6b]
    // 0xb20290: d0 = 2.000000
    //     0xb20290: fmov            d0, #2.00000000
    // 0xb20294: StoreField: r3->field_9f = d0
    //     0xb20294: stur            d0, [x3, #0x9f]
    // 0xb20298: r10 = Instance_BoxHeightStyle
    //     0xb20298: ldr             x10, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb2029c: StoreField: r3->field_bb = r10
    //     0xb2029c: stur            w10, [x3, #0xbb]
    // 0xb202a0: r11 = Instance_BoxWidthStyle
    //     0xb202a0: ldr             x11, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb202a4: StoreField: r3->field_bf = r11
    //     0xb202a4: stur            w11, [x3, #0xbf]
    // 0xb202a8: r12 = Instance_EdgeInsets
    //     0xb202a8: ldr             x12, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb202ac: StoreField: r3->field_c7 = r12
    //     0xb202ac: stur            w12, [x3, #0xc7]
    // 0xb202b0: r13 = Instance_DragStartBehavior
    //     0xb202b0: ldr             x13, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb202b4: StoreField: r3->field_d3 = r13
    //     0xb202b4: stur            w13, [x3, #0xd3]
    // 0xb202b8: ldur            x2, [fp, #-8]
    // 0xb202bc: r1 = Function '<anonymous closure>':.
    //     0xb202bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4c0] AnonymousClosure: (0xb208c8), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb202c0: ldr             x1, [x1, #0x4c0]
    // 0xb202c4: r0 = AllocateClosure()
    //     0xb202c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb202c8: mov             x1, x0
    // 0xb202cc: ldur            x0, [fp, #-0x20]
    // 0xb202d0: StoreField: r0->field_d7 = r1
    //     0xb202d0: stur            w1, [x0, #0xd7]
    // 0xb202d4: r1 = false
    //     0xb202d4: add             x1, NULL, #0x30  ; false
    // 0xb202d8: StoreField: r0->field_db = r1
    //     0xb202d8: stur            w1, [x0, #0xdb]
    // 0xb202dc: r2 = const []
    //     0xb202dc: ldr             x2, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb202e0: StoreField: r0->field_f3 = r2
    //     0xb202e0: stur            w2, [x0, #0xf3]
    // 0xb202e4: r3 = Instance_Clip
    //     0xb202e4: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb202e8: ldr             x3, [x3, #0x7c0]
    // 0xb202ec: StoreField: r0->field_f7 = r3
    //     0xb202ec: stur            w3, [x0, #0xf7]
    // 0xb202f0: r4 = true
    //     0xb202f0: add             x4, NULL, #0x20  ; true
    // 0xb202f4: StoreField: r0->field_ff = r4
    //     0xb202f4: stur            w4, [x0, #0xff]
    // 0xb202f8: r17 = 259
    //     0xb202f8: movz            x17, #0x103
    // 0xb202fc: str             w4, [x0, x17]
    // 0xb20300: r5 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb20300: add             x5, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb20304: ldr             x5, [x5, #0x4d8]
    // 0xb20308: r17 = 267
    //     0xb20308: movz            x17, #0x10b
    // 0xb2030c: str             w5, [x0, x17]
    // 0xb20310: r17 = 271
    //     0xb20310: movz            x17, #0x10f
    // 0xb20314: str             w4, [x0, x17]
    // 0xb20318: r6 = Instance_SmartDashesType
    //     0xb20318: ldr             x6, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb2031c: StoreField: r0->field_53 = r6
    //     0xb2031c: stur            w6, [x0, #0x53]
    // 0xb20320: r7 = Instance_SmartQuotesType
    //     0xb20320: add             x7, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb20324: ldr             x7, [x7, #0x4e0]
    // 0xb20328: StoreField: r0->field_57 = r7
    //     0xb20328: stur            w7, [x0, #0x57]
    // 0xb2032c: r8 = Instance_TextInputType
    //     0xb2032c: add             x8, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xb20330: ldr             x8, [x8, #0x4e8]
    // 0xb20334: StoreField: r0->field_1f = r8
    //     0xb20334: stur            w8, [x0, #0x1f]
    // 0xb20338: StoreField: r0->field_cb = r4
    //     0xb20338: stur            w4, [x0, #0xcb]
    // 0xb2033c: r0 = UniqueKey()
    //     0xb2033c: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xb20340: mov             x1, x0
    // 0xb20344: ldur            x0, [fp, #-0x20]
    // 0xb20348: StoreField: r0->field_7 = r1
    //     0xb20348: stur            w1, [x0, #7]
    // 0xb2034c: r1 = Null
    //     0xb2034c: mov             x1, NULL
    // 0xb20350: r2 = 4
    //     0xb20350: movz            x2, #0x4
    // 0xb20354: r0 = AllocateArray()
    //     0xb20354: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb20358: stur            x0, [fp, #-0x10]
    // 0xb2035c: r16 = "Masukkan nomor ayat antara 1 - "
    //     0xb2035c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b778] "Masukkan nomor ayat antara 1 - "
    //     0xb20360: ldr             x16, [x16, #0x778]
    // 0xb20364: StoreField: r0->field_f = r16
    //     0xb20364: stur            w16, [x0, #0xf]
    // 0xb20368: ldur            x2, [fp, #-8]
    // 0xb2036c: LoadField: r1 = r2->field_f
    //     0xb2036c: ldur            w1, [x2, #0xf]
    // 0xb20370: DecompressPointer r1
    //     0xb20370: add             x1, x1, HEAP, lsl #32
    // 0xb20374: r0 = controller()
    //     0xb20374: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20378: LoadField: r1 = r0->field_47
    //     0xb20378: ldur            w1, [x0, #0x47]
    // 0xb2037c: DecompressPointer r1
    //     0xb2037c: add             x1, x1, HEAP, lsl #32
    // 0xb20380: r0 = value()
    //     0xb20380: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb20384: ldur            x1, [fp, #-0x10]
    // 0xb20388: ArrayStore: r1[1] = r0  ; List_4
    //     0xb20388: add             x25, x1, #0x13
    //     0xb2038c: str             w0, [x25]
    //     0xb20390: tbz             w0, #0, #0xb203ac
    //     0xb20394: ldurb           w16, [x1, #-1]
    //     0xb20398: ldurb           w17, [x0, #-1]
    //     0xb2039c: and             x16, x17, x16, lsr #2
    //     0xb203a0: tst             x16, HEAP, lsr #32
    //     0xb203a4: b.eq            #0xb203ac
    //     0xb203a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb203ac: ldur            x16, [fp, #-0x10]
    // 0xb203b0: str             x16, [SP]
    // 0xb203b4: r0 = _interpolate()
    //     0xb203b4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb203b8: stur            x0, [fp, #-0x10]
    // 0xb203bc: r0 = GetNavigation.textTheme()
    //     0xb203bc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb203c0: LoadField: r1 = r0->field_27
    //     0xb203c0: ldur            w1, [x0, #0x27]
    // 0xb203c4: DecompressPointer r1
    //     0xb203c4: add             x1, x1, HEAP, lsl #32
    // 0xb203c8: cmp             w1, NULL
    // 0xb203cc: b.ne            #0xb203d8
    // 0xb203d0: r2 = Null
    //     0xb203d0: mov             x2, NULL
    // 0xb203d4: b               #0xb203f4
    // 0xb203d8: r16 = 14.000000
    //     0xb203d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb203dc: ldr             x16, [x16, #0x9a0]
    // 0xb203e0: str             x16, [SP]
    // 0xb203e4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb203e4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb203e8: ldr             x4, [x4, #0x88]
    // 0xb203ec: r0 = copyWith()
    //     0xb203ec: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb203f0: mov             x2, x0
    // 0xb203f4: ldur            x1, [fp, #-8]
    // 0xb203f8: ldur            x0, [fp, #-0x10]
    // 0xb203fc: stur            x2, [fp, #-0x28]
    // 0xb20400: r0 = Text()
    //     0xb20400: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb20404: mov             x2, x0
    // 0xb20408: ldur            x0, [fp, #-0x10]
    // 0xb2040c: stur            x2, [fp, #-0x30]
    // 0xb20410: StoreField: r2->field_b = r0
    //     0xb20410: stur            w0, [x2, #0xb]
    // 0xb20414: ldur            x0, [fp, #-0x28]
    // 0xb20418: StoreField: r2->field_13 = r0
    //     0xb20418: stur            w0, [x2, #0x13]
    // 0xb2041c: ldur            x0, [fp, #-8]
    // 0xb20420: LoadField: r1 = r0->field_f
    //     0xb20420: ldur            w1, [x0, #0xf]
    // 0xb20424: DecompressPointer r1
    //     0xb20424: add             x1, x1, HEAP, lsl #32
    // 0xb20428: r0 = controller()
    //     0xb20428: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2042c: LoadField: r1 = r0->field_5b
    //     0xb2042c: ldur            w1, [x0, #0x5b]
    // 0xb20430: DecompressPointer r1
    //     0xb20430: add             x1, x1, HEAP, lsl #32
    // 0xb20434: stur            x1, [fp, #-0x10]
    // 0xb20438: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb20438: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2043c: ldr             x0, [x0, #0xdd8]
    //     0xb20440: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb20444: cmp             w0, w16
    //     0xb20448: b.ne            #0xb20458
    //     0xb2044c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb20450: ldr             x2, [x2, #0x780]
    //     0xb20454: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb20458: stur            x0, [fp, #-0x28]
    // 0xb2045c: r0 = LengthLimitingTextInputFormatter()
    //     0xb2045c: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb20460: mov             x3, x0
    // 0xb20464: r0 = 6
    //     0xb20464: movz            x0, #0x6
    // 0xb20468: stur            x3, [fp, #-0x38]
    // 0xb2046c: StoreField: r3->field_7 = r0
    //     0xb2046c: stur            w0, [x3, #7]
    // 0xb20470: r1 = Null
    //     0xb20470: mov             x1, NULL
    // 0xb20474: r2 = 4
    //     0xb20474: movz            x2, #0x4
    // 0xb20478: r0 = AllocateArray()
    //     0xb20478: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb2047c: mov             x2, x0
    // 0xb20480: ldur            x0, [fp, #-0x28]
    // 0xb20484: stur            x2, [fp, #-0x40]
    // 0xb20488: StoreField: r2->field_f = r0
    //     0xb20488: stur            w0, [x2, #0xf]
    // 0xb2048c: ldur            x0, [fp, #-0x38]
    // 0xb20490: StoreField: r2->field_13 = r0
    //     0xb20490: stur            w0, [x2, #0x13]
    // 0xb20494: r1 = <TextInputFormatter>
    //     0xb20494: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb20498: ldr             x1, [x1, #0x788]
    // 0xb2049c: r0 = AllocateGrowableArray()
    //     0xb2049c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb204a0: mov             x3, x0
    // 0xb204a4: ldur            x0, [fp, #-0x40]
    // 0xb204a8: stur            x3, [fp, #-0x28]
    // 0xb204ac: StoreField: r3->field_f = r0
    //     0xb204ac: stur            w0, [x3, #0xf]
    // 0xb204b0: r2 = 4
    //     0xb204b0: movz            x2, #0x4
    // 0xb204b4: StoreField: r3->field_b = r2
    //     0xb204b4: stur            w2, [x3, #0xb]
    // 0xb204b8: r1 = Null
    //     0xb204b8: mov             x1, NULL
    // 0xb204bc: r0 = AllocateArray()
    //     0xb204bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb204c0: stur            x0, [fp, #-0x38]
    // 0xb204c4: r16 = "1 - "
    //     0xb204c4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b790] "1 - "
    //     0xb204c8: ldr             x16, [x16, #0x790]
    // 0xb204cc: StoreField: r0->field_f = r16
    //     0xb204cc: stur            w16, [x0, #0xf]
    // 0xb204d0: ldur            x2, [fp, #-8]
    // 0xb204d4: LoadField: r1 = r2->field_f
    //     0xb204d4: ldur            w1, [x2, #0xf]
    // 0xb204d8: DecompressPointer r1
    //     0xb204d8: add             x1, x1, HEAP, lsl #32
    // 0xb204dc: r0 = controller()
    //     0xb204dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb204e0: LoadField: r1 = r0->field_47
    //     0xb204e0: ldur            w1, [x0, #0x47]
    // 0xb204e4: DecompressPointer r1
    //     0xb204e4: add             x1, x1, HEAP, lsl #32
    // 0xb204e8: r0 = value()
    //     0xb204e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb204ec: ldur            x1, [fp, #-0x38]
    // 0xb204f0: ArrayStore: r1[1] = r0  ; List_4
    //     0xb204f0: add             x25, x1, #0x13
    //     0xb204f4: str             w0, [x25]
    //     0xb204f8: tbz             w0, #0, #0xb20514
    //     0xb204fc: ldurb           w16, [x1, #-1]
    //     0xb20500: ldurb           w17, [x0, #-1]
    //     0xb20504: and             x16, x17, x16, lsr #2
    //     0xb20508: tst             x16, HEAP, lsr #32
    //     0xb2050c: b.eq            #0xb20514
    //     0xb20510: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb20514: ldur            x16, [fp, #-0x38]
    // 0xb20518: str             x16, [SP]
    // 0xb2051c: r0 = _interpolate()
    //     0xb2051c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb20520: mov             x2, x0
    // 0xb20524: ldur            x0, [fp, #-8]
    // 0xb20528: stur            x2, [fp, #-0x38]
    // 0xb2052c: LoadField: r1 = r0->field_f
    //     0xb2052c: ldur            w1, [x0, #0xf]
    // 0xb20530: DecompressPointer r1
    //     0xb20530: add             x1, x1, HEAP, lsl #32
    // 0xb20534: r0 = controller()
    //     0xb20534: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20538: LoadField: r1 = r0->field_43
    //     0xb20538: ldur            w1, [x0, #0x43]
    // 0xb2053c: DecompressPointer r1
    //     0xb2053c: add             x1, x1, HEAP, lsl #32
    // 0xb20540: r0 = value()
    //     0xb20540: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb20544: cmp             w0, NULL
    // 0xb20548: b.eq            #0xb20554
    // 0xb2054c: r7 = ""
    //     0xb2054c: ldr             x7, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb20550: b               #0xb20558
    // 0xb20554: r7 = Null
    //     0xb20554: mov             x7, NULL
    // 0xb20558: ldur            x0, [fp, #-8]
    // 0xb2055c: ldur            x6, [fp, #-0x18]
    // 0xb20560: ldur            x5, [fp, #-0x20]
    // 0xb20564: ldur            x4, [fp, #-0x30]
    // 0xb20568: ldur            x3, [fp, #-0x10]
    // 0xb2056c: ldur            x1, [fp, #-0x38]
    // 0xb20570: ldur            x2, [fp, #-0x28]
    // 0xb20574: stur            x7, [fp, #-0x40]
    // 0xb20578: r0 = InputDecoration()
    //     0xb20578: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb2057c: mov             x1, x0
    // 0xb20580: ldur            x0, [fp, #-0x38]
    // 0xb20584: stur            x1, [fp, #-0x48]
    // 0xb20588: StoreField: r1->field_2f = r0
    //     0xb20588: stur            w0, [x1, #0x2f]
    // 0xb2058c: r0 = true
    //     0xb2058c: add             x0, NULL, #0x20  ; true
    // 0xb20590: StoreField: r1->field_43 = r0
    //     0xb20590: stur            w0, [x1, #0x43]
    // 0xb20594: ldur            x2, [fp, #-0x40]
    // 0xb20598: StoreField: r1->field_4b = r2
    //     0xb20598: stur            w2, [x1, #0x4b]
    // 0xb2059c: r2 = Instance_TextStyle
    //     0xb2059c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb205a0: ldr             x2, [x2, #0x798]
    // 0xb205a4: StoreField: r1->field_4f = r2
    //     0xb205a4: stur            w2, [x1, #0x4f]
    // 0xb205a8: StoreField: r1->field_cf = r0
    //     0xb205a8: stur            w0, [x1, #0xcf]
    // 0xb205ac: r0 = TextField()
    //     0xb205ac: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb205b0: mov             x3, x0
    // 0xb205b4: r0 = EditableText
    //     0xb205b4: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb205b8: stur            x3, [fp, #-0x38]
    // 0xb205bc: StoreField: r3->field_f = r0
    //     0xb205bc: stur            w0, [x3, #0xf]
    // 0xb205c0: ldur            x0, [fp, #-0x10]
    // 0xb205c4: StoreField: r3->field_13 = r0
    //     0xb205c4: stur            w0, [x3, #0x13]
    // 0xb205c8: ldur            x0, [fp, #-0x48]
    // 0xb205cc: StoreField: r3->field_1b = r0
    //     0xb205cc: stur            w0, [x3, #0x1b]
    // 0xb205d0: r0 = Instance_TextCapitalization
    //     0xb205d0: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb205d4: StoreField: r3->field_27 = r0
    //     0xb205d4: stur            w0, [x3, #0x27]
    // 0xb205d8: r0 = Instance_TextAlign
    //     0xb205d8: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb205dc: StoreField: r3->field_33 = r0
    //     0xb205dc: stur            w0, [x3, #0x33]
    // 0xb205e0: r0 = false
    //     0xb205e0: add             x0, NULL, #0x30  ; false
    // 0xb205e4: StoreField: r3->field_6f = r0
    //     0xb205e4: stur            w0, [x3, #0x6f]
    // 0xb205e8: StoreField: r3->field_3f = r0
    //     0xb205e8: stur            w0, [x3, #0x3f]
    // 0xb205ec: r1 = "•"
    //     0xb205ec: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb205f0: ldr             x1, [x1, #0x4c8]
    // 0xb205f4: StoreField: r3->field_47 = r1
    //     0xb205f4: stur            w1, [x3, #0x47]
    // 0xb205f8: StoreField: r3->field_4b = r0
    //     0xb205f8: stur            w0, [x3, #0x4b]
    // 0xb205fc: r1 = true
    //     0xb205fc: add             x1, NULL, #0x20  ; true
    // 0xb20600: StoreField: r3->field_4f = r1
    //     0xb20600: stur            w1, [x3, #0x4f]
    // 0xb20604: StoreField: r3->field_5b = r1
    //     0xb20604: stur            w1, [x3, #0x5b]
    // 0xb20608: r2 = 1
    //     0xb20608: movz            x2, #0x1
    // 0xb2060c: StoreField: r3->field_5f = r2
    //     0xb2060c: stur            x2, [x3, #0x5f]
    // 0xb20610: StoreField: r3->field_6b = r0
    //     0xb20610: stur            w0, [x3, #0x6b]
    // 0xb20614: ldur            x2, [fp, #-0x28]
    // 0xb20618: StoreField: r3->field_93 = r2
    //     0xb20618: stur            w2, [x3, #0x93]
    // 0xb2061c: d0 = 2.000000
    //     0xb2061c: fmov            d0, #2.00000000
    // 0xb20620: StoreField: r3->field_9f = d0
    //     0xb20620: stur            d0, [x3, #0x9f]
    // 0xb20624: r2 = Instance_BoxHeightStyle
    //     0xb20624: ldr             x2, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb20628: StoreField: r3->field_bb = r2
    //     0xb20628: stur            w2, [x3, #0xbb]
    // 0xb2062c: r2 = Instance_BoxWidthStyle
    //     0xb2062c: ldr             x2, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb20630: StoreField: r3->field_bf = r2
    //     0xb20630: stur            w2, [x3, #0xbf]
    // 0xb20634: r2 = Instance_EdgeInsets
    //     0xb20634: ldr             x2, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb20638: StoreField: r3->field_c7 = r2
    //     0xb20638: stur            w2, [x3, #0xc7]
    // 0xb2063c: r2 = Instance_DragStartBehavior
    //     0xb2063c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb20640: StoreField: r3->field_d3 = r2
    //     0xb20640: stur            w2, [x3, #0xd3]
    // 0xb20644: StoreField: r3->field_db = r0
    //     0xb20644: stur            w0, [x3, #0xdb]
    // 0xb20648: r0 = const []
    //     0xb20648: ldr             x0, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb2064c: StoreField: r3->field_f3 = r0
    //     0xb2064c: stur            w0, [x3, #0xf3]
    // 0xb20650: r0 = Instance_Clip
    //     0xb20650: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb20654: ldr             x0, [x0, #0x7c0]
    // 0xb20658: StoreField: r3->field_f7 = r0
    //     0xb20658: stur            w0, [x3, #0xf7]
    // 0xb2065c: StoreField: r3->field_ff = r1
    //     0xb2065c: stur            w1, [x3, #0xff]
    // 0xb20660: r17 = 259
    //     0xb20660: movz            x17, #0x103
    // 0xb20664: str             w1, [x3, x17]
    // 0xb20668: r0 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb20668: add             x0, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb2066c: ldr             x0, [x0, #0x4d8]
    // 0xb20670: r17 = 267
    //     0xb20670: movz            x17, #0x10b
    // 0xb20674: str             w0, [x3, x17]
    // 0xb20678: r17 = 271
    //     0xb20678: movz            x17, #0x10f
    // 0xb2067c: str             w1, [x3, x17]
    // 0xb20680: r0 = Instance_SmartDashesType
    //     0xb20680: ldr             x0, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb20684: StoreField: r3->field_53 = r0
    //     0xb20684: stur            w0, [x3, #0x53]
    // 0xb20688: r0 = Instance_SmartQuotesType
    //     0xb20688: add             x0, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb2068c: ldr             x0, [x0, #0x4e0]
    // 0xb20690: StoreField: r3->field_57 = r0
    //     0xb20690: stur            w0, [x3, #0x57]
    // 0xb20694: r0 = Instance_TextInputType
    //     0xb20694: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb20698: ldr             x0, [x0, #0x7a0]
    // 0xb2069c: StoreField: r3->field_1f = r0
    //     0xb2069c: stur            w0, [x3, #0x1f]
    // 0xb206a0: StoreField: r3->field_cb = r1
    //     0xb206a0: stur            w1, [x3, #0xcb]
    // 0xb206a4: r1 = Null
    //     0xb206a4: mov             x1, NULL
    // 0xb206a8: r2 = 16
    //     0xb206a8: movz            x2, #0x10
    // 0xb206ac: r0 = AllocateArray()
    //     0xb206ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb206b0: mov             x2, x0
    // 0xb206b4: ldur            x0, [fp, #-0x18]
    // 0xb206b8: stur            x2, [fp, #-0x10]
    // 0xb206bc: StoreField: r2->field_f = r0
    //     0xb206bc: stur            w0, [x2, #0xf]
    // 0xb206c0: r16 = Instance_SizedBox
    //     0xb206c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb206c4: ldr             x16, [x16, #0x4a0]
    // 0xb206c8: StoreField: r2->field_13 = r16
    //     0xb206c8: stur            w16, [x2, #0x13]
    // 0xb206cc: ldur            x0, [fp, #-0x20]
    // 0xb206d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb206d0: stur            w0, [x2, #0x17]
    // 0xb206d4: r16 = Instance_SizedBox
    //     0xb206d4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb206d8: ldr             x16, [x16, #0xfe8]
    // 0xb206dc: StoreField: r2->field_1b = r16
    //     0xb206dc: stur            w16, [x2, #0x1b]
    // 0xb206e0: ldur            x0, [fp, #-0x30]
    // 0xb206e4: StoreField: r2->field_1f = r0
    //     0xb206e4: stur            w0, [x2, #0x1f]
    // 0xb206e8: r16 = Instance_SizedBox
    //     0xb206e8: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb206ec: ldr             x16, [x16, #0x4a0]
    // 0xb206f0: StoreField: r2->field_23 = r16
    //     0xb206f0: stur            w16, [x2, #0x23]
    // 0xb206f4: ldur            x0, [fp, #-0x38]
    // 0xb206f8: StoreField: r2->field_27 = r0
    //     0xb206f8: stur            w0, [x2, #0x27]
    // 0xb206fc: r16 = Instance_SizedBox
    //     0xb206fc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb20700: ldr             x16, [x16, #0x7a8]
    // 0xb20704: StoreField: r2->field_2b = r16
    //     0xb20704: stur            w16, [x2, #0x2b]
    // 0xb20708: r1 = <Widget>
    //     0xb20708: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb2070c: r0 = AllocateGrowableArray()
    //     0xb2070c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb20710: mov             x2, x0
    // 0xb20714: ldur            x0, [fp, #-0x10]
    // 0xb20718: stur            x2, [fp, #-0x18]
    // 0xb2071c: StoreField: r2->field_f = r0
    //     0xb2071c: stur            w0, [x2, #0xf]
    // 0xb20720: r0 = 16
    //     0xb20720: movz            x0, #0x10
    // 0xb20724: StoreField: r2->field_b = r0
    //     0xb20724: stur            w0, [x2, #0xb]
    // 0xb20728: ldur            x0, [fp, #-8]
    // 0xb2072c: LoadField: r1 = r0->field_f
    //     0xb2072c: ldur            w1, [x0, #0xf]
    // 0xb20730: DecompressPointer r1
    //     0xb20730: add             x1, x1, HEAP, lsl #32
    // 0xb20734: r0 = controller()
    //     0xb20734: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20738: LoadField: r1 = r0->field_43
    //     0xb20738: ldur            w1, [x0, #0x43]
    // 0xb2073c: DecompressPointer r1
    //     0xb2073c: add             x1, x1, HEAP, lsl #32
    // 0xb20740: r0 = value()
    //     0xb20740: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb20744: cmp             w0, NULL
    // 0xb20748: b.eq            #0xb20830
    // 0xb2074c: ldur            x1, [fp, #-8]
    // 0xb20750: ldur            x0, [fp, #-0x18]
    // 0xb20754: LoadField: r2 = r1->field_f
    //     0xb20754: ldur            w2, [x1, #0xf]
    // 0xb20758: DecompressPointer r2
    //     0xb20758: add             x2, x2, HEAP, lsl #32
    // 0xb2075c: mov             x1, x2
    // 0xb20760: r0 = controller()
    //     0xb20760: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20764: LoadField: r1 = r0->field_43
    //     0xb20764: ldur            w1, [x0, #0x43]
    // 0xb20768: DecompressPointer r1
    //     0xb20768: add             x1, x1, HEAP, lsl #32
    // 0xb2076c: r0 = value()
    //     0xb2076c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb20770: r1 = 60
    //     0xb20770: movz            x1, #0x3c
    // 0xb20774: branchIfSmi(r0, 0xb20780)
    //     0xb20774: tbz             w0, #0, #0xb20780
    // 0xb20778: r1 = LoadClassIdInstr(r0)
    //     0xb20778: ldur            x1, [x0, #-1]
    //     0xb2077c: ubfx            x1, x1, #0xc, #0x14
    // 0xb20780: str             x0, [SP]
    // 0xb20784: mov             x0, x1
    // 0xb20788: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb20788: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2078c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb2078c: movz            x17, #0x2b03
    //     0xb20790: add             lr, x0, x17
    //     0xb20794: ldr             lr, [x21, lr, lsl #3]
    //     0xb20798: blr             lr
    // 0xb2079c: stur            x0, [fp, #-8]
    // 0xb207a0: r0 = ErrorMessage()
    //     0xb207a0: bl              #0xb208bc  ; AllocateErrorMessageStub -> ErrorMessage (size=0x10)
    // 0xb207a4: mov             x2, x0
    // 0xb207a8: ldur            x0, [fp, #-8]
    // 0xb207ac: stur            x2, [fp, #-0x10]
    // 0xb207b0: StoreField: r2->field_b = r0
    //     0xb207b0: stur            w0, [x2, #0xb]
    // 0xb207b4: ldur            x0, [fp, #-0x18]
    // 0xb207b8: LoadField: r1 = r0->field_b
    //     0xb207b8: ldur            w1, [x0, #0xb]
    // 0xb207bc: LoadField: r3 = r0->field_f
    //     0xb207bc: ldur            w3, [x0, #0xf]
    // 0xb207c0: DecompressPointer r3
    //     0xb207c0: add             x3, x3, HEAP, lsl #32
    // 0xb207c4: LoadField: r4 = r3->field_b
    //     0xb207c4: ldur            w4, [x3, #0xb]
    // 0xb207c8: r3 = LoadInt32Instr(r1)
    //     0xb207c8: sbfx            x3, x1, #1, #0x1f
    // 0xb207cc: stur            x3, [fp, #-0x50]
    // 0xb207d0: r1 = LoadInt32Instr(r4)
    //     0xb207d0: sbfx            x1, x4, #1, #0x1f
    // 0xb207d4: cmp             x3, x1
    // 0xb207d8: b.ne            #0xb207e4
    // 0xb207dc: mov             x1, x0
    // 0xb207e0: r0 = _growToNextCapacity()
    //     0xb207e0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb207e4: ldur            x2, [fp, #-0x18]
    // 0xb207e8: ldur            x3, [fp, #-0x50]
    // 0xb207ec: add             x0, x3, #1
    // 0xb207f0: lsl             x1, x0, #1
    // 0xb207f4: StoreField: r2->field_b = r1
    //     0xb207f4: stur            w1, [x2, #0xb]
    // 0xb207f8: LoadField: r1 = r2->field_f
    //     0xb207f8: ldur            w1, [x2, #0xf]
    // 0xb207fc: DecompressPointer r1
    //     0xb207fc: add             x1, x1, HEAP, lsl #32
    // 0xb20800: ldur            x0, [fp, #-0x10]
    // 0xb20804: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb20804: add             x25, x1, x3, lsl #2
    //     0xb20808: add             x25, x25, #0xf
    //     0xb2080c: str             w0, [x25]
    //     0xb20810: tbz             w0, #0, #0xb2082c
    //     0xb20814: ldurb           w16, [x1, #-1]
    //     0xb20818: ldurb           w17, [x0, #-1]
    //     0xb2081c: and             x16, x17, x16, lsr #2
    //     0xb20820: tst             x16, HEAP, lsr #32
    //     0xb20824: b.eq            #0xb2082c
    //     0xb20828: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb2082c: b               #0xb20834
    // 0xb20830: ldur            x2, [fp, #-0x18]
    // 0xb20834: r0 = Column()
    //     0xb20834: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb20838: mov             x1, x0
    // 0xb2083c: r0 = Instance_Axis
    //     0xb2083c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb20840: stur            x1, [fp, #-8]
    // 0xb20844: StoreField: r1->field_f = r0
    //     0xb20844: stur            w0, [x1, #0xf]
    // 0xb20848: r0 = Instance_MainAxisAlignment
    //     0xb20848: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb2084c: ldr             x0, [x0, #0x730]
    // 0xb20850: StoreField: r1->field_13 = r0
    //     0xb20850: stur            w0, [x1, #0x13]
    // 0xb20854: r0 = Instance_MainAxisSize
    //     0xb20854: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb20858: ldr             x0, [x0, #0x738]
    // 0xb2085c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2085c: stur            w0, [x1, #0x17]
    // 0xb20860: r0 = Instance_CrossAxisAlignment
    //     0xb20860: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb20864: ldr             x0, [x0, #0x68]
    // 0xb20868: StoreField: r1->field_1b = r0
    //     0xb20868: stur            w0, [x1, #0x1b]
    // 0xb2086c: r0 = Instance_VerticalDirection
    //     0xb2086c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb20870: ldr             x0, [x0, #0x748]
    // 0xb20874: StoreField: r1->field_23 = r0
    //     0xb20874: stur            w0, [x1, #0x23]
    // 0xb20878: r0 = Instance_Clip
    //     0xb20878: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb2087c: ldr             x0, [x0, #0x750]
    // 0xb20880: StoreField: r1->field_2b = r0
    //     0xb20880: stur            w0, [x1, #0x2b]
    // 0xb20884: StoreField: r1->field_2f = rZR
    //     0xb20884: stur            xzr, [x1, #0x2f]
    // 0xb20888: ldur            x0, [fp, #-0x18]
    // 0xb2088c: StoreField: r1->field_b = r0
    //     0xb2088c: stur            w0, [x1, #0xb]
    // 0xb20890: r0 = Padding()
    //     0xb20890: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb20894: r1 = Instance_EdgeInsets
    //     0xb20894: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb20898: ldr             x1, [x1, #0x7b0]
    // 0xb2089c: StoreField: r0->field_f = r1
    //     0xb2089c: stur            w1, [x0, #0xf]
    // 0xb208a0: ldur            x1, [fp, #-8]
    // 0xb208a4: StoreField: r0->field_b = r1
    //     0xb208a4: stur            w1, [x0, #0xb]
    // 0xb208a8: LeaveFrame
    //     0xb208a8: mov             SP, fp
    //     0xb208ac: ldp             fp, lr, [SP], #0x10
    // 0xb208b0: ret
    //     0xb208b0: ret             
    // 0xb208b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb208b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb208b8: b               #0xb20190
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb208c8, size: 0x108
    // 0xb208c8: EnterFrame
    //     0xb208c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb208cc: mov             fp, SP
    // 0xb208d0: AllocStack(0x30)
    //     0xb208d0: sub             SP, SP, #0x30
    // 0xb208d4: SetupParameters(QuranListView this /* r1 */)
    //     0xb208d4: stur            NULL, [fp, #-8]
    //     0xb208d8: movz            x0, #0
    //     0xb208dc: add             x1, fp, w0, sxtw #2
    //     0xb208e0: ldr             x1, [x1, #0x10]
    //     0xb208e4: ldur            w2, [x1, #0x17]
    //     0xb208e8: add             x2, x2, HEAP, lsl #32
    //     0xb208ec: stur            x2, [fp, #-0x10]
    // 0xb208f0: CheckStackOverflow
    //     0xb208f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb208f4: cmp             SP, x16
    //     0xb208f8: b.ls            #0xb209c8
    // 0xb208fc: InitAsync() -> Future<void?>
    //     0xb208fc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb20900: bl              #0x661298  ; InitAsyncStub
    // 0xb20904: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb20904: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb20908: ldr             x0, [x0, #0x2670]
    //     0xb2090c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb20910: cmp             w0, w16
    //     0xb20914: b.ne            #0xb20920
    //     0xb20918: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb2091c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb20920: r16 = "/quran/quran-surah"
    //     0xb20920: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7b8] "/quran/quran-surah"
    //     0xb20924: ldr             x16, [x16, #0x7b8]
    // 0xb20928: stp             x16, NULL, [SP, #8]
    // 0xb2092c: r16 = false
    //     0xb2092c: add             x16, NULL, #0x30  ; false
    // 0xb20930: str             x16, [SP]
    // 0xb20934: r4 = const [0x1, 0x2, 0x2, 0x1, preventDuplicates, 0x1, null]
    //     0xb20934: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b7c0] List(7) [0x1, 0x2, 0x2, 0x1, "preventDuplicates", 0x1, Null]
    //     0xb20938: ldr             x4, [x4, #0x7c0]
    // 0xb2093c: r0 = GetNavigation.toNamed()
    //     0xb2093c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb20940: mov             x1, x0
    // 0xb20944: stur            x1, [fp, #-0x18]
    // 0xb20948: r0 = Await()
    //     0xb20948: bl              #0x661044  ; AwaitStub
    // 0xb2094c: stur            x0, [fp, #-0x18]
    // 0xb20950: cmp             w0, NULL
    // 0xb20954: b.eq            #0xb209c0
    // 0xb20958: ldur            x1, [fp, #-0x10]
    // 0xb2095c: LoadField: r2 = r1->field_f
    //     0xb2095c: ldur            w2, [x1, #0xf]
    // 0xb20960: DecompressPointer r2
    //     0xb20960: add             x2, x2, HEAP, lsl #32
    // 0xb20964: mov             x1, x2
    // 0xb20968: r0 = controller()
    //     0xb20968: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2096c: mov             x3, x0
    // 0xb20970: ldur            x0, [fp, #-0x18]
    // 0xb20974: r2 = Null
    //     0xb20974: mov             x2, NULL
    // 0xb20978: r1 = Null
    //     0xb20978: mov             x1, NULL
    // 0xb2097c: stur            x3, [fp, #-0x10]
    // 0xb20980: branchIfSmi(r0, 0xb209a8)
    //     0xb20980: tbz             w0, #0, #0xb209a8
    // 0xb20984: r4 = LoadClassIdInstr(r0)
    //     0xb20984: ldur            x4, [x0, #-1]
    //     0xb20988: ubfx            x4, x4, #0xc, #0x14
    // 0xb2098c: sub             x4, x4, #0x3c
    // 0xb20990: cmp             x4, #1
    // 0xb20994: b.ls            #0xb209a8
    // 0xb20998: r8 = int
    //     0xb20998: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xb2099c: r3 = Null
    //     0xb2099c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f4c8] Null
    //     0xb209a0: ldr             x3, [x3, #0x4c8]
    // 0xb209a4: r0 = int()
    //     0xb209a4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xb209a8: ldur            x0, [fp, #-0x18]
    // 0xb209ac: r2 = LoadInt32Instr(r0)
    //     0xb209ac: sbfx            x2, x0, #1, #0x1f
    //     0xb209b0: tbz             w0, #0, #0xb209b8
    //     0xb209b4: ldur            x2, [x0, #7]
    // 0xb209b8: ldur            x1, [fp, #-0x10]
    // 0xb209bc: r0 = setSelectedSurah()
    //     0xb209bc: bl              #0xb1f948  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::setSelectedSurah
    // 0xb209c0: r0 = Null
    //     0xb209c0: mov             x0, NULL
    // 0xb209c4: r0 = ReturnAsyncNotFuture()
    //     0xb209c4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb209c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb209c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb209cc: b               #0xb208fc
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Iterable<ItemPosition>, Widget?) {
    // ** addr: 0xb209d0, size: 0x1bc
    // 0xb209d0: EnterFrame
    //     0xb209d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb209d4: mov             fp, SP
    // 0xb209d8: AllocStack(0x10)
    //     0xb209d8: sub             SP, SP, #0x10
    // 0xb209dc: SetupParameters()
    //     0xb209dc: ldr             x0, [fp, #0x28]
    //     0xb209e0: ldur            w2, [x0, #0x17]
    //     0xb209e4: add             x2, x2, HEAP, lsl #32
    //     0xb209e8: stur            x2, [fp, #-8]
    // 0xb209ec: CheckStackOverflow
    //     0xb209ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb209f0: cmp             SP, x16
    //     0xb209f4: b.ls            #0xb20b84
    // 0xb209f8: ldr             x3, [fp, #0x18]
    // 0xb209fc: r0 = LoadClassIdInstr(r3)
    //     0xb209fc: ldur            x0, [x3, #-1]
    //     0xb20a00: ubfx            x0, x0, #0xc, #0x14
    // 0xb20a04: mov             x1, x3
    // 0xb20a08: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb20a08: movz            x17, #0xd488
    //     0xb20a0c: add             lr, x0, x17
    //     0xb20a10: ldr             lr, [x21, lr, lsl #3]
    //     0xb20a14: blr             lr
    // 0xb20a18: tbnz            w0, #4, #0xb20ab4
    // 0xb20a1c: ldr             x0, [fp, #0x18]
    // 0xb20a20: r1 = Function '<anonymous closure>':.
    //     0xb20a20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4d8] AnonymousClosure: (0xb20e38), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb20a24: ldr             x1, [x1, #0x4d8]
    // 0xb20a28: r2 = Null
    //     0xb20a28: mov             x2, NULL
    // 0xb20a2c: r0 = AllocateClosure()
    //     0xb20a2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20a30: ldr             x1, [fp, #0x18]
    // 0xb20a34: r2 = LoadClassIdInstr(r1)
    //     0xb20a34: ldur            x2, [x1, #-1]
    //     0xb20a38: ubfx            x2, x2, #0xc, #0x14
    // 0xb20a3c: mov             x16, x0
    // 0xb20a40: mov             x0, x2
    // 0xb20a44: mov             x2, x16
    // 0xb20a48: r0 = GDT[cid_x0 + 0xea28]()
    //     0xb20a48: movz            x17, #0xea28
    //     0xb20a4c: add             lr, x0, x17
    //     0xb20a50: ldr             lr, [x21, lr, lsl #3]
    //     0xb20a54: blr             lr
    // 0xb20a58: r1 = Function '<anonymous closure>':.
    //     0xb20a58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4e0] AnonymousClosure: (0xb20e10), in [package:nuonline/app/modules/quran/quran_list/views/quran_list_view.dart] QuranListView::build (0xb1bf24)
    //     0xb20a5c: ldr             x1, [x1, #0x4e0]
    // 0xb20a60: r2 = Null
    //     0xb20a60: mov             x2, NULL
    // 0xb20a64: stur            x0, [fp, #-0x10]
    // 0xb20a68: r0 = AllocateClosure()
    //     0xb20a68: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20a6c: ldur            x1, [fp, #-0x10]
    // 0xb20a70: r2 = LoadClassIdInstr(r1)
    //     0xb20a70: ldur            x2, [x1, #-1]
    //     0xb20a74: ubfx            x2, x2, #0xc, #0x14
    // 0xb20a78: mov             x16, x0
    // 0xb20a7c: mov             x0, x2
    // 0xb20a80: mov             x2, x16
    // 0xb20a84: r0 = GDT[cid_x0 + 0xe7f9]()
    //     0xb20a84: movz            x17, #0xe7f9
    //     0xb20a88: add             lr, x0, x17
    //     0xb20a8c: ldr             lr, [x21, lr, lsl #3]
    //     0xb20a90: blr             lr
    // 0xb20a94: LoadField: r2 = r0->field_7
    //     0xb20a94: ldur            x2, [x0, #7]
    // 0xb20a98: r0 = BoxInt64Instr(r2)
    //     0xb20a98: sbfiz           x0, x2, #1, #0x1f
    //     0xb20a9c: cmp             x2, x0, asr #1
    //     0xb20aa0: b.eq            #0xb20aac
    //     0xb20aa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb20aa8: stur            x2, [x0, #7]
    // 0xb20aac: mov             x2, x0
    // 0xb20ab0: b               #0xb20ab8
    // 0xb20ab4: r2 = Null
    //     0xb20ab4: mov             x2, NULL
    // 0xb20ab8: ldur            x0, [fp, #-8]
    // 0xb20abc: stur            x2, [fp, #-0x10]
    // 0xb20ac0: LoadField: r1 = r0->field_f
    //     0xb20ac0: ldur            w1, [x0, #0xf]
    // 0xb20ac4: DecompressPointer r1
    //     0xb20ac4: add             x1, x1, HEAP, lsl #32
    // 0xb20ac8: r0 = controller()
    //     0xb20ac8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20acc: LoadField: r2 = r0->field_73
    //     0xb20acc: ldur            w2, [x0, #0x73]
    // 0xb20ad0: DecompressPointer r2
    //     0xb20ad0: add             x2, x2, HEAP, lsl #32
    // 0xb20ad4: ldur            x3, [fp, #-0x10]
    // 0xb20ad8: cmp             w3, NULL
    // 0xb20adc: b.ne            #0xb20ae8
    // 0xb20ae0: r4 = 0
    //     0xb20ae0: movz            x4, #0
    // 0xb20ae4: b               #0xb20af8
    // 0xb20ae8: r0 = LoadInt32Instr(r3)
    //     0xb20ae8: sbfx            x0, x3, #1, #0x1f
    //     0xb20aec: tbz             w3, #0, #0xb20af4
    //     0xb20af0: ldur            x0, [x3, #7]
    // 0xb20af4: mov             x4, x0
    // 0xb20af8: r0 = BoxInt64Instr(r4)
    //     0xb20af8: sbfiz           x0, x4, #1, #0x1f
    //     0xb20afc: cmp             x4, x0, asr #1
    //     0xb20b00: b.eq            #0xb20b0c
    //     0xb20b04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb20b08: stur            x4, [x0, #7]
    // 0xb20b0c: mov             x1, x2
    // 0xb20b10: mov             x2, x0
    // 0xb20b14: r0 = value=()
    //     0xb20b14: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb20b18: ldur            x0, [fp, #-0x10]
    // 0xb20b1c: cmp             w0, NULL
    // 0xb20b20: b.ne            #0xb20b30
    // 0xb20b24: r0 = Instance_SizedBox
    //     0xb20b24: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb20b28: ldr             x0, [x0, #0xc40]
    // 0xb20b2c: b               #0xb20b78
    // 0xb20b30: ldur            x1, [fp, #-8]
    // 0xb20b34: LoadField: r2 = r1->field_f
    //     0xb20b34: ldur            w2, [x1, #0xf]
    // 0xb20b38: DecompressPointer r2
    //     0xb20b38: add             x2, x2, HEAP, lsl #32
    // 0xb20b3c: mov             x1, x2
    // 0xb20b40: r0 = controller()
    //     0xb20b40: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb20b44: mov             x1, x0
    // 0xb20b48: ldur            x0, [fp, #-0x10]
    // 0xb20b4c: r2 = LoadInt32Instr(r0)
    //     0xb20b4c: sbfx            x2, x0, #1, #0x1f
    //     0xb20b50: tbz             w0, #0, #0xb20b58
    //     0xb20b54: ldur            x2, [x0, #7]
    // 0xb20b58: r0 = getAppBarTitle()
    //     0xb20b58: bl              #0xb20b8c  ; [package:nuonline/app/modules/quran/quran_list/controllers/quran_list_controller.dart] QuranListController::getAppBarTitle
    // 0xb20b5c: stur            x0, [fp, #-8]
    // 0xb20b60: r0 = Text()
    //     0xb20b60: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb20b64: ldur            x1, [fp, #-8]
    // 0xb20b68: StoreField: r0->field_b = r1
    //     0xb20b68: stur            w1, [x0, #0xb]
    // 0xb20b6c: r1 = Instance_TextStyle
    //     0xb20b6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4e8] Obj!TextStyle@e1bab1
    //     0xb20b70: ldr             x1, [x1, #0x4e8]
    // 0xb20b74: StoreField: r0->field_13 = r1
    //     0xb20b74: stur            w1, [x0, #0x13]
    // 0xb20b78: LeaveFrame
    //     0xb20b78: mov             SP, fp
    //     0xb20b7c: ldp             fp, lr, [SP], #0x10
    // 0xb20b80: ret
    //     0xb20b80: ret             
    // 0xb20b84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20b84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb20b88: b               #0xb209f8
  }
  [closure] ItemPosition <anonymous closure>(dynamic, ItemPosition, ItemPosition) {
    // ** addr: 0xb20e10, size: 0x28
    // 0xb20e10: ldr             x1, [SP]
    // 0xb20e14: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xb20e14: ldur            d0, [x1, #0x17]
    // 0xb20e18: ldr             x2, [SP, #8]
    // 0xb20e1c: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xb20e1c: ldur            d1, [x2, #0x17]
    // 0xb20e20: fcmp            d1, d0
    // 0xb20e24: b.le            #0xb20e30
    // 0xb20e28: mov             x0, x1
    // 0xb20e2c: b               #0xb20e34
    // 0xb20e30: mov             x0, x2
    // 0xb20e34: ret
    //     0xb20e34: ret             
  }
  [closure] bool <anonymous closure>(dynamic, ItemPosition) {
    // ** addr: 0xb20e38, size: 0x20
    // 0xb20e38: d0 = 0.000000
    //     0xb20e38: eor             v0.16b, v0.16b, v0.16b
    // 0xb20e3c: ldr             x1, [SP]
    // 0xb20e40: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xb20e40: ldur            d1, [x1, #0x17]
    // 0xb20e44: fcmp            d1, d0
    // 0xb20e48: r16 = true
    //     0xb20e48: add             x16, NULL, #0x20  ; true
    // 0xb20e4c: r17 = false
    //     0xb20e4c: add             x17, NULL, #0x30  ; false
    // 0xb20e50: csel            x0, x16, x17, gt
    // 0xb20e54: ret
    //     0xb20e54: ret             
  }
}
