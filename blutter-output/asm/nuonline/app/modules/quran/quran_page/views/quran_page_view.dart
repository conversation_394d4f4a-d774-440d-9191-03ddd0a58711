// lib: , url: package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart

// class id: 1050438, size: 0x8
class :: {
}

// class id: 5235, size: 0x14, field offset: 0x14
class QuranPageView extends GetView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1f298, size: 0x5c
    // 0xb1f298: EnterFrame
    //     0xb1f298: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f29c: mov             fp, SP
    // 0xb1f2a0: AllocStack(0x10)
    //     0xb1f2a0: sub             SP, SP, #0x10
    // 0xb1f2a4: CheckStackOverflow
    //     0xb1f2a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f2a8: cmp             SP, x16
    //     0xb1f2ac: b.ls            #0xb1f2ec
    // 0xb1f2b0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1f2b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1f2b4: ldr             x0, [x0, #0x2670]
    //     0xb1f2b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1f2bc: cmp             w0, w16
    //     0xb1f2c0: b.ne            #0xb1f2cc
    //     0xb1f2c4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1f2c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1f2cc: r16 = "/setting/dark-mode"
    //     0xb1f2cc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1c0] "/setting/dark-mode"
    //     0xb1f2d0: ldr             x16, [x16, #0x1c0]
    // 0xb1f2d4: stp             x16, NULL, [SP]
    // 0xb1f2d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1f2d8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1f2dc: r0 = GetNavigation.toNamed()
    //     0xb1f2dc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb1f2e0: LeaveFrame
    //     0xb1f2e0: mov             SP, fp
    //     0xb1f2e4: ldp             fp, lr, [SP], #0x10
    // 0xb1f2e8: ret
    //     0xb1f2e8: ret             
    // 0xb1f2ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f2ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f2f0: b               #0xb1f2b0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1f56c, size: 0x60
    // 0xb1f56c: EnterFrame
    //     0xb1f56c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f570: mov             fp, SP
    // 0xb1f574: AllocStack(0x10)
    //     0xb1f574: sub             SP, SP, #0x10
    // 0xb1f578: CheckStackOverflow
    //     0xb1f578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f57c: cmp             SP, x16
    //     0xb1f580: b.ls            #0xb1f5c4
    // 0xb1f584: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1f584: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1f588: ldr             x0, [x0, #0x2670]
    //     0xb1f58c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1f590: cmp             w0, w16
    //     0xb1f594: b.ne            #0xb1f5a0
    //     0xb1f598: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb1f59c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1f5a0: r16 = "/tajweed-guidance"
    //     0xb1f5a0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24308] "/tajweed-guidance"
    //     0xb1f5a4: ldr             x16, [x16, #0x308]
    // 0xb1f5a8: stp             x16, NULL, [SP]
    // 0xb1f5ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb1f5ac: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb1f5b0: r0 = GetNavigation.toNamed()
    //     0xb1f5b0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb1f5b4: r0 = Null
    //     0xb1f5b4: mov             x0, NULL
    // 0xb1f5b8: LeaveFrame
    //     0xb1f5b8: mov             SP, fp
    //     0xb1f5bc: ldp             fp, lr, [SP], #0x10
    // 0xb1f5c0: ret
    //     0xb1f5c0: ret             
    // 0xb1f5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f5c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f5c8: b               #0xb1f584
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0xb1f5cc, size: 0xec
    // 0xb1f5cc: EnterFrame
    //     0xb1f5cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb1f5d0: mov             fp, SP
    // 0xb1f5d4: AllocStack(0x10)
    //     0xb1f5d4: sub             SP, SP, #0x10
    // 0xb1f5d8: SetupParameters()
    //     0xb1f5d8: ldr             x0, [fp, #0x10]
    //     0xb1f5dc: ldur            w1, [x0, #0x17]
    //     0xb1f5e0: add             x1, x1, HEAP, lsl #32
    // 0xb1f5e4: CheckStackOverflow
    //     0xb1f5e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1f5e8: cmp             SP, x16
    //     0xb1f5ec: b.ls            #0xb1f6b0
    // 0xb1f5f0: LoadField: r0 = r1->field_f
    //     0xb1f5f0: ldur            w0, [x1, #0xf]
    // 0xb1f5f4: DecompressPointer r0
    //     0xb1f5f4: add             x0, x0, HEAP, lsl #32
    // 0xb1f5f8: mov             x1, x0
    // 0xb1f5fc: r0 = controller()
    //     0xb1f5fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb1f600: mov             x1, x0
    // 0xb1f604: LoadField: r0 = r1->field_5f
    //     0xb1f604: ldur            w0, [x1, #0x5f]
    // 0xb1f608: DecompressPointer r0
    //     0xb1f608: add             x0, x0, HEAP, lsl #32
    // 0xb1f60c: r16 = Sentinel
    //     0xb1f60c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1f610: cmp             w0, w16
    // 0xb1f614: b.ne            #0xb1f624
    // 0xb1f618: r2 = showTajweed
    //     0xb1f618: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bf18] Field <QuranPageController.showTajweed>: late final (offset: 0x60)
    //     0xb1f61c: ldr             x2, [x2, #0xf18]
    // 0xb1f620: r0 = InitLateFinalInstanceField()
    //     0xb1f620: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb1f624: mov             x1, x0
    // 0xb1f628: r0 = RxBoolExt.isFalse()
    //     0xb1f628: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xb1f62c: tbnz            w0, #4, #0xb1f644
    // 0xb1f630: r0 = Instance_SizedBox
    //     0xb1f630: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb1f634: ldr             x0, [x0, #0xc40]
    // 0xb1f638: LeaveFrame
    //     0xb1f638: mov             SP, fp
    //     0xb1f63c: ldp             fp, lr, [SP], #0x10
    // 0xb1f640: ret
    //     0xb1f640: ret             
    // 0xb1f644: r1 = Function '<anonymous closure>':.
    //     0xb1f644: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c208] AnonymousClosure: (0xb1f56c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb1f648: ldr             x1, [x1, #0x208]
    // 0xb1f64c: r2 = Null
    //     0xb1f64c: mov             x2, NULL
    // 0xb1f650: r0 = AllocateClosure()
    //     0xb1f650: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1f654: stur            x0, [fp, #-8]
    // 0xb1f658: r0 = IconButton()
    //     0xb1f658: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb1f65c: mov             x1, x0
    // 0xb1f660: ldur            x0, [fp, #-8]
    // 0xb1f664: stur            x1, [fp, #-0x10]
    // 0xb1f668: StoreField: r1->field_3b = r0
    //     0xb1f668: stur            w0, [x1, #0x3b]
    // 0xb1f66c: r0 = false
    //     0xb1f66c: add             x0, NULL, #0x30  ; false
    // 0xb1f670: StoreField: r1->field_47 = r0
    //     0xb1f670: stur            w0, [x1, #0x47]
    // 0xb1f674: r0 = Instance_Icon
    //     0xb1f674: add             x0, PP, #0x27, lsl #12  ; [pp+0x27fd0] Obj!Icon@e247b1
    //     0xb1f678: ldr             x0, [x0, #0xfd0]
    // 0xb1f67c: StoreField: r1->field_1f = r0
    //     0xb1f67c: stur            w0, [x1, #0x1f]
    // 0xb1f680: r0 = Instance__IconButtonVariant
    //     0xb1f680: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb1f684: ldr             x0, [x0, #0xf78]
    // 0xb1f688: StoreField: r1->field_63 = r0
    //     0xb1f688: stur            w0, [x1, #0x63]
    // 0xb1f68c: r0 = SizedBox()
    //     0xb1f68c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1f690: r1 = 36.000000
    //     0xb1f690: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb1f694: ldr             x1, [x1, #0x478]
    // 0xb1f698: StoreField: r0->field_f = r1
    //     0xb1f698: stur            w1, [x0, #0xf]
    // 0xb1f69c: ldur            x1, [fp, #-0x10]
    // 0xb1f6a0: StoreField: r0->field_b = r1
    //     0xb1f6a0: stur            w1, [x0, #0xb]
    // 0xb1f6a4: LeaveFrame
    //     0xb1f6a4: mov             SP, fp
    //     0xb1f6a8: ldp             fp, lr, [SP], #0x10
    // 0xb1f6ac: ret
    //     0xb1f6ac: ret             
    // 0xb1f6b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1f6b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1f6b4: b               #0xb1f5f0
  }
  _ build(/* No info */) {
    // ** addr: 0xb20e58, size: 0x43c
    // 0xb20e58: EnterFrame
    //     0xb20e58: stp             fp, lr, [SP, #-0x10]!
    //     0xb20e5c: mov             fp, SP
    // 0xb20e60: AllocStack(0x48)
    //     0xb20e60: sub             SP, SP, #0x48
    // 0xb20e64: SetupParameters(QuranPageView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb20e64: stur            x1, [fp, #-8]
    //     0xb20e68: stur            x2, [fp, #-0x10]
    // 0xb20e6c: CheckStackOverflow
    //     0xb20e6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20e70: cmp             SP, x16
    //     0xb20e74: b.ls            #0xb2128c
    // 0xb20e78: r1 = 2
    //     0xb20e78: movz            x1, #0x2
    // 0xb20e7c: r0 = AllocateContext()
    //     0xb20e7c: bl              #0xec126c  ; AllocateContextStub
    // 0xb20e80: mov             x3, x0
    // 0xb20e84: ldur            x0, [fp, #-8]
    // 0xb20e88: stur            x3, [fp, #-0x18]
    // 0xb20e8c: StoreField: r3->field_f = r0
    //     0xb20e8c: stur            w0, [x3, #0xf]
    // 0xb20e90: ldur            x0, [fp, #-0x10]
    // 0xb20e94: StoreField: r3->field_13 = r0
    //     0xb20e94: stur            w0, [x3, #0x13]
    // 0xb20e98: r1 = Null
    //     0xb20e98: mov             x1, NULL
    // 0xb20e9c: r2 = 8
    //     0xb20e9c: movz            x2, #0x8
    // 0xb20ea0: r0 = AllocateArray()
    //     0xb20ea0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb20ea4: stur            x0, [fp, #-8]
    // 0xb20ea8: r16 = Instance_DeviceOrientation
    //     0xb20ea8: add             x16, PP, #0xd, lsl #12  ; [pp+0xdac0] Obj!DeviceOrientation@e34da1
    //     0xb20eac: ldr             x16, [x16, #0xac0]
    // 0xb20eb0: StoreField: r0->field_f = r16
    //     0xb20eb0: stur            w16, [x0, #0xf]
    // 0xb20eb4: r16 = Instance_DeviceOrientation
    //     0xb20eb4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdac8] Obj!DeviceOrientation@e34d81
    //     0xb20eb8: ldr             x16, [x16, #0xac8]
    // 0xb20ebc: StoreField: r0->field_13 = r16
    //     0xb20ebc: stur            w16, [x0, #0x13]
    // 0xb20ec0: r16 = Instance_DeviceOrientation
    //     0xb20ec0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2be30] Obj!DeviceOrientation@e34dc1
    //     0xb20ec4: ldr             x16, [x16, #0xe30]
    // 0xb20ec8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb20ec8: stur            w16, [x0, #0x17]
    // 0xb20ecc: r16 = Instance_DeviceOrientation
    //     0xb20ecc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2be38] Obj!DeviceOrientation@e34d61
    //     0xb20ed0: ldr             x16, [x16, #0xe38]
    // 0xb20ed4: StoreField: r0->field_1b = r16
    //     0xb20ed4: stur            w16, [x0, #0x1b]
    // 0xb20ed8: r1 = <DeviceOrientation>
    //     0xb20ed8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad0] TypeArguments: <DeviceOrientation>
    //     0xb20edc: ldr             x1, [x1, #0xad0]
    // 0xb20ee0: r0 = AllocateGrowableArray()
    //     0xb20ee0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb20ee4: mov             x1, x0
    // 0xb20ee8: ldur            x0, [fp, #-8]
    // 0xb20eec: StoreField: r1->field_f = r0
    //     0xb20eec: stur            w0, [x1, #0xf]
    // 0xb20ef0: r0 = 8
    //     0xb20ef0: movz            x0, #0x8
    // 0xb20ef4: StoreField: r1->field_b = r0
    //     0xb20ef4: stur            w0, [x1, #0xb]
    // 0xb20ef8: r0 = setPreferredOrientations()
    //     0xb20ef8: bl              #0x97d8a8  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setPreferredOrientations
    // 0xb20efc: r0 = Obx()
    //     0xb20efc: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb20f00: ldur            x2, [fp, #-0x18]
    // 0xb20f04: r1 = Function '<anonymous closure>':.
    //     0xb20f04: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be40] AnonymousClosure: (0xb254bc), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb20f08: ldr             x1, [x1, #0xe40]
    // 0xb20f0c: stur            x0, [fp, #-8]
    // 0xb20f10: r0 = AllocateClosure()
    //     0xb20f10: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20f14: mov             x1, x0
    // 0xb20f18: ldur            x0, [fp, #-8]
    // 0xb20f1c: StoreField: r0->field_b = r1
    //     0xb20f1c: stur            w1, [x0, #0xb]
    // 0xb20f20: r0 = Obx()
    //     0xb20f20: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb20f24: ldur            x2, [fp, #-0x18]
    // 0xb20f28: r1 = Function '<anonymous closure>':.
    //     0xb20f28: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be48] AnonymousClosure: (0xb1f5cc), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb20f2c: ldr             x1, [x1, #0xe48]
    // 0xb20f30: stur            x0, [fp, #-0x10]
    // 0xb20f34: r0 = AllocateClosure()
    //     0xb20f34: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20f38: mov             x1, x0
    // 0xb20f3c: ldur            x0, [fp, #-0x10]
    // 0xb20f40: StoreField: r0->field_b = r1
    //     0xb20f40: stur            w1, [x0, #0xb]
    // 0xb20f44: ldur            x2, [fp, #-0x18]
    // 0xb20f48: r1 = Function '<anonymous closure>':.
    //     0xb20f48: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be50] AnonymousClosure: (0xb24fec), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb20f4c: ldr             x1, [x1, #0xe50]
    // 0xb20f50: r0 = AllocateClosure()
    //     0xb20f50: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20f54: stur            x0, [fp, #-0x20]
    // 0xb20f58: r0 = IconButton()
    //     0xb20f58: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb20f5c: mov             x1, x0
    // 0xb20f60: ldur            x0, [fp, #-0x20]
    // 0xb20f64: stur            x1, [fp, #-0x28]
    // 0xb20f68: StoreField: r1->field_3b = r0
    //     0xb20f68: stur            w0, [x1, #0x3b]
    // 0xb20f6c: r0 = false
    //     0xb20f6c: add             x0, NULL, #0x30  ; false
    // 0xb20f70: StoreField: r1->field_47 = r0
    //     0xb20f70: stur            w0, [x1, #0x47]
    // 0xb20f74: r2 = Instance_Icon
    //     0xb20f74: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be58] Obj!Icon@e24871
    //     0xb20f78: ldr             x2, [x2, #0xe58]
    // 0xb20f7c: StoreField: r1->field_1f = r2
    //     0xb20f7c: stur            w2, [x1, #0x1f]
    // 0xb20f80: r2 = Instance__IconButtonVariant
    //     0xb20f80: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb20f84: ldr             x2, [x2, #0xf78]
    // 0xb20f88: StoreField: r1->field_63 = r2
    //     0xb20f88: stur            w2, [x1, #0x63]
    // 0xb20f8c: r0 = SizedBox()
    //     0xb20f8c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb20f90: mov             x3, x0
    // 0xb20f94: r0 = 36.000000
    //     0xb20f94: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb20f98: ldr             x0, [x0, #0x478]
    // 0xb20f9c: stur            x3, [fp, #-0x20]
    // 0xb20fa0: StoreField: r3->field_f = r0
    //     0xb20fa0: stur            w0, [x3, #0xf]
    // 0xb20fa4: ldur            x1, [fp, #-0x28]
    // 0xb20fa8: StoreField: r3->field_b = r1
    //     0xb20fa8: stur            w1, [x3, #0xb]
    // 0xb20fac: r1 = Function '<anonymous closure>':.
    //     0xb20fac: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be60] AnonymousClosure: (0xb1f298), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb20fb0: ldr             x1, [x1, #0xe60]
    // 0xb20fb4: r2 = Null
    //     0xb20fb4: mov             x2, NULL
    // 0xb20fb8: r0 = AllocateClosure()
    //     0xb20fb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb20fbc: stur            x0, [fp, #-0x28]
    // 0xb20fc0: r0 = IconButton()
    //     0xb20fc0: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb20fc4: mov             x1, x0
    // 0xb20fc8: ldur            x0, [fp, #-0x28]
    // 0xb20fcc: stur            x1, [fp, #-0x30]
    // 0xb20fd0: StoreField: r1->field_3b = r0
    //     0xb20fd0: stur            w0, [x1, #0x3b]
    // 0xb20fd4: r0 = false
    //     0xb20fd4: add             x0, NULL, #0x30  ; false
    // 0xb20fd8: StoreField: r1->field_47 = r0
    //     0xb20fd8: stur            w0, [x1, #0x47]
    // 0xb20fdc: r2 = Instance_Icon
    //     0xb20fdc: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be68] Obj!Icon@e247f1
    //     0xb20fe0: ldr             x2, [x2, #0xe68]
    // 0xb20fe4: StoreField: r1->field_1f = r2
    //     0xb20fe4: stur            w2, [x1, #0x1f]
    // 0xb20fe8: r2 = Instance__IconButtonVariant
    //     0xb20fe8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb20fec: ldr             x2, [x2, #0xf78]
    // 0xb20ff0: StoreField: r1->field_63 = r2
    //     0xb20ff0: stur            w2, [x1, #0x63]
    // 0xb20ff4: r0 = SizedBox()
    //     0xb20ff4: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb20ff8: mov             x3, x0
    // 0xb20ffc: r0 = 36.000000
    //     0xb20ffc: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d478] 36
    //     0xb21000: ldr             x0, [x0, #0x478]
    // 0xb21004: stur            x3, [fp, #-0x28]
    // 0xb21008: StoreField: r3->field_f = r0
    //     0xb21008: stur            w0, [x3, #0xf]
    // 0xb2100c: ldur            x0, [fp, #-0x30]
    // 0xb21010: StoreField: r3->field_b = r0
    //     0xb21010: stur            w0, [x3, #0xb]
    // 0xb21014: ldur            x2, [fp, #-0x18]
    // 0xb21018: r1 = Function '<anonymous closure>':.
    //     0xb21018: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be70] AnonymousClosure: (0xb24e4c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb2101c: ldr             x1, [x1, #0xe70]
    // 0xb21020: r0 = AllocateClosure()
    //     0xb21020: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21024: stur            x0, [fp, #-0x30]
    // 0xb21028: r0 = IconButton()
    //     0xb21028: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb2102c: mov             x1, x0
    // 0xb21030: ldur            x0, [fp, #-0x30]
    // 0xb21034: stur            x1, [fp, #-0x38]
    // 0xb21038: StoreField: r1->field_3b = r0
    //     0xb21038: stur            w0, [x1, #0x3b]
    // 0xb2103c: r0 = false
    //     0xb2103c: add             x0, NULL, #0x30  ; false
    // 0xb21040: StoreField: r1->field_47 = r0
    //     0xb21040: stur            w0, [x1, #0x47]
    // 0xb21044: r2 = Instance_Icon
    //     0xb21044: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2be78] Obj!Icon@e243f1
    //     0xb21048: ldr             x2, [x2, #0xe78]
    // 0xb2104c: StoreField: r1->field_1f = r2
    //     0xb2104c: stur            w2, [x1, #0x1f]
    // 0xb21050: r2 = Instance__IconButtonVariant
    //     0xb21050: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb21054: ldr             x2, [x2, #0xf78]
    // 0xb21058: StoreField: r1->field_63 = r2
    //     0xb21058: stur            w2, [x1, #0x63]
    // 0xb2105c: r0 = SizedBox()
    //     0xb2105c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb21060: mov             x3, x0
    // 0xb21064: r0 = 42.000000
    //     0xb21064: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2be80] 42
    //     0xb21068: ldr             x0, [x0, #0xe80]
    // 0xb2106c: stur            x3, [fp, #-0x30]
    // 0xb21070: StoreField: r3->field_f = r0
    //     0xb21070: stur            w0, [x3, #0xf]
    // 0xb21074: ldur            x0, [fp, #-0x38]
    // 0xb21078: StoreField: r3->field_b = r0
    //     0xb21078: stur            w0, [x3, #0xb]
    // 0xb2107c: r1 = Null
    //     0xb2107c: mov             x1, NULL
    // 0xb21080: r2 = 10
    //     0xb21080: movz            x2, #0xa
    // 0xb21084: r0 = AllocateArray()
    //     0xb21084: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb21088: mov             x2, x0
    // 0xb2108c: ldur            x0, [fp, #-8]
    // 0xb21090: stur            x2, [fp, #-0x38]
    // 0xb21094: StoreField: r2->field_f = r0
    //     0xb21094: stur            w0, [x2, #0xf]
    // 0xb21098: ldur            x0, [fp, #-0x10]
    // 0xb2109c: StoreField: r2->field_13 = r0
    //     0xb2109c: stur            w0, [x2, #0x13]
    // 0xb210a0: ldur            x0, [fp, #-0x20]
    // 0xb210a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb210a4: stur            w0, [x2, #0x17]
    // 0xb210a8: ldur            x0, [fp, #-0x28]
    // 0xb210ac: StoreField: r2->field_1b = r0
    //     0xb210ac: stur            w0, [x2, #0x1b]
    // 0xb210b0: ldur            x0, [fp, #-0x30]
    // 0xb210b4: StoreField: r2->field_1f = r0
    //     0xb210b4: stur            w0, [x2, #0x1f]
    // 0xb210b8: r1 = <Widget>
    //     0xb210b8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb210bc: r0 = AllocateGrowableArray()
    //     0xb210bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb210c0: mov             x1, x0
    // 0xb210c4: ldur            x0, [fp, #-0x38]
    // 0xb210c8: stur            x1, [fp, #-8]
    // 0xb210cc: StoreField: r1->field_f = r0
    //     0xb210cc: stur            w0, [x1, #0xf]
    // 0xb210d0: r0 = 10
    //     0xb210d0: movz            x0, #0xa
    // 0xb210d4: StoreField: r1->field_b = r0
    //     0xb210d4: stur            w0, [x1, #0xb]
    // 0xb210d8: r0 = Obx()
    //     0xb210d8: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb210dc: ldur            x2, [fp, #-0x18]
    // 0xb210e0: r1 = Function '<anonymous closure>':.
    //     0xb210e0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be88] AnonymousClosure: (0xb22814), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb210e4: ldr             x1, [x1, #0xe88]
    // 0xb210e8: stur            x0, [fp, #-0x10]
    // 0xb210ec: r0 = AllocateClosure()
    //     0xb210ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xb210f0: mov             x1, x0
    // 0xb210f4: ldur            x0, [fp, #-0x10]
    // 0xb210f8: StoreField: r0->field_b = r1
    //     0xb210f8: stur            w1, [x0, #0xb]
    // 0xb210fc: r0 = AppBar()
    //     0xb210fc: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb21100: stur            x0, [fp, #-0x20]
    // 0xb21104: ldur            x16, [fp, #-8]
    // 0xb21108: ldur            lr, [fp, #-0x10]
    // 0xb2110c: stp             lr, x16, [SP]
    // 0xb21110: mov             x1, x0
    // 0xb21114: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x1, title, 0x2, null]
    //     0xb21114: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2be90] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x1, "title", 0x2, Null]
    //     0xb21118: ldr             x4, [x4, #0xe90]
    // 0xb2111c: r0 = AppBar()
    //     0xb2111c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb21120: ldur            x2, [fp, #-0x18]
    // 0xb21124: r1 = Function '<anonymous closure>':.
    //     0xb21124: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2be98] AnonymousClosure: (0xb21608), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb21128: ldr             x1, [x1, #0xe98]
    // 0xb2112c: r0 = AllocateClosure()
    //     0xb2112c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21130: stur            x0, [fp, #-8]
    // 0xb21134: r0 = OrientationBuilder()
    //     0xb21134: bl              #0xa515c4  ; AllocateOrientationBuilderStub -> OrientationBuilder (size=0x10)
    // 0xb21138: mov             x3, x0
    // 0xb2113c: ldur            x0, [fp, #-8]
    // 0xb21140: stur            x3, [fp, #-0x10]
    // 0xb21144: StoreField: r3->field_b = r0
    //     0xb21144: stur            w0, [x3, #0xb]
    // 0xb21148: r1 = Null
    //     0xb21148: mov             x1, NULL
    // 0xb2114c: r2 = 4
    //     0xb2114c: movz            x2, #0x4
    // 0xb21150: r0 = AllocateArray()
    //     0xb21150: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb21154: mov             x2, x0
    // 0xb21158: ldur            x0, [fp, #-0x10]
    // 0xb2115c: stur            x2, [fp, #-8]
    // 0xb21160: StoreField: r2->field_f = r0
    //     0xb21160: stur            w0, [x2, #0xf]
    // 0xb21164: r16 = Instance_Align
    //     0xb21164: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bea0] Obj!Align@e1e6a1
    //     0xb21168: ldr             x16, [x16, #0xea0]
    // 0xb2116c: StoreField: r2->field_13 = r16
    //     0xb2116c: stur            w16, [x2, #0x13]
    // 0xb21170: r1 = <Widget>
    //     0xb21170: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb21174: r0 = AllocateGrowableArray()
    //     0xb21174: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb21178: mov             x1, x0
    // 0xb2117c: ldur            x0, [fp, #-8]
    // 0xb21180: stur            x1, [fp, #-0x10]
    // 0xb21184: StoreField: r1->field_f = r0
    //     0xb21184: stur            w0, [x1, #0xf]
    // 0xb21188: r0 = 4
    //     0xb21188: movz            x0, #0x4
    // 0xb2118c: StoreField: r1->field_b = r0
    //     0xb2118c: stur            w0, [x1, #0xb]
    // 0xb21190: r0 = Stack()
    //     0xb21190: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb21194: mov             x1, x0
    // 0xb21198: r0 = Instance_AlignmentDirectional
    //     0xb21198: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xb2119c: ldr             x0, [x0, #0x7b0]
    // 0xb211a0: stur            x1, [fp, #-8]
    // 0xb211a4: StoreField: r1->field_f = r0
    //     0xb211a4: stur            w0, [x1, #0xf]
    // 0xb211a8: r0 = Instance_StackFit
    //     0xb211a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xb211ac: ldr             x0, [x0, #0x7b8]
    // 0xb211b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb211b0: stur            w0, [x1, #0x17]
    // 0xb211b4: r0 = Instance_Clip
    //     0xb211b4: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb211b8: ldr             x0, [x0, #0x7c0]
    // 0xb211bc: StoreField: r1->field_1b = r0
    //     0xb211bc: stur            w0, [x1, #0x1b]
    // 0xb211c0: ldur            x0, [fp, #-0x10]
    // 0xb211c4: StoreField: r1->field_b = r0
    //     0xb211c4: stur            w0, [x1, #0xb]
    // 0xb211c8: r0 = GestureDetector()
    //     0xb211c8: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb211cc: ldur            x2, [fp, #-0x18]
    // 0xb211d0: r1 = Function '<anonymous closure>':.
    //     0xb211d0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bea8] AnonymousClosure: (0xb215b4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb211d4: ldr             x1, [x1, #0xea8]
    // 0xb211d8: stur            x0, [fp, #-0x10]
    // 0xb211dc: r0 = AllocateClosure()
    //     0xb211dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb211e0: ldur            x16, [fp, #-8]
    // 0xb211e4: stp             x16, x0, [SP]
    // 0xb211e8: ldur            x1, [fp, #-0x10]
    // 0xb211ec: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb211ec: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb211f0: ldr             x4, [x4, #0x7d0]
    // 0xb211f4: r0 = GestureDetector()
    //     0xb211f4: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb211f8: r0 = Scaffold()
    //     0xb211f8: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb211fc: mov             x2, x0
    // 0xb21200: ldur            x0, [fp, #-0x20]
    // 0xb21204: stur            x2, [fp, #-8]
    // 0xb21208: StoreField: r2->field_13 = r0
    //     0xb21208: stur            w0, [x2, #0x13]
    // 0xb2120c: ldur            x0, [fp, #-0x10]
    // 0xb21210: ArrayStore: r2[0] = r0  ; List_4
    //     0xb21210: stur            w0, [x2, #0x17]
    // 0xb21214: r0 = Instance_AlignmentDirectional
    //     0xb21214: add             x0, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb21218: ldr             x0, [x0, #0x758]
    // 0xb2121c: StoreField: r2->field_2b = r0
    //     0xb2121c: stur            w0, [x2, #0x2b]
    // 0xb21220: r0 = true
    //     0xb21220: add             x0, NULL, #0x20  ; true
    // 0xb21224: StoreField: r2->field_53 = r0
    //     0xb21224: stur            w0, [x2, #0x53]
    // 0xb21228: r1 = Instance_DragStartBehavior
    //     0xb21228: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb2122c: StoreField: r2->field_57 = r1
    //     0xb2122c: stur            w1, [x2, #0x57]
    // 0xb21230: r1 = false
    //     0xb21230: add             x1, NULL, #0x30  ; false
    // 0xb21234: StoreField: r2->field_b = r1
    //     0xb21234: stur            w1, [x2, #0xb]
    // 0xb21238: StoreField: r2->field_f = r1
    //     0xb21238: stur            w1, [x2, #0xf]
    // 0xb2123c: StoreField: r2->field_5f = r0
    //     0xb2123c: stur            w0, [x2, #0x5f]
    // 0xb21240: StoreField: r2->field_63 = r0
    //     0xb21240: stur            w0, [x2, #0x63]
    // 0xb21244: r1 = <Object>
    //     0xb21244: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0xb21248: r0 = PopScope()
    //     0xb21248: bl              #0xafd0d4  ; AllocatePopScopeStub -> PopScope<X0> (size=0x20)
    // 0xb2124c: mov             x3, x0
    // 0xb21250: ldur            x0, [fp, #-8]
    // 0xb21254: stur            x3, [fp, #-0x10]
    // 0xb21258: StoreField: r3->field_f = r0
    //     0xb21258: stur            w0, [x3, #0xf]
    // 0xb2125c: r0 = true
    //     0xb2125c: add             x0, NULL, #0x20  ; true
    // 0xb21260: StoreField: r3->field_1b = r0
    //     0xb21260: stur            w0, [x3, #0x1b]
    // 0xb21264: ldur            x2, [fp, #-0x18]
    // 0xb21268: r1 = Function '<anonymous closure>':.
    //     0xb21268: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2beb0] AnonymousClosure: (0xb21294), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb2126c: ldr             x1, [x1, #0xeb0]
    // 0xb21270: r0 = AllocateClosure()
    //     0xb21270: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21274: mov             x1, x0
    // 0xb21278: ldur            x0, [fp, #-0x10]
    // 0xb2127c: StoreField: r0->field_13 = r1
    //     0xb2127c: stur            w1, [x0, #0x13]
    // 0xb21280: LeaveFrame
    //     0xb21280: mov             SP, fp
    //     0xb21284: ldp             fp, lr, [SP], #0x10
    // 0xb21288: ret
    //     0xb21288: ret             
    // 0xb2128c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2128c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21290: b               #0xb20e78
  }
  [closure] void <anonymous closure>(dynamic, bool, Object?) {
    // ** addr: 0xb21294, size: 0xf4
    // 0xb21294: EnterFrame
    //     0xb21294: stp             fp, lr, [SP, #-0x10]!
    //     0xb21298: mov             fp, SP
    // 0xb2129c: AllocStack(0x8)
    //     0xb2129c: sub             SP, SP, #8
    // 0xb212a0: SetupParameters()
    //     0xb212a0: ldr             x0, [fp, #0x20]
    //     0xb212a4: ldur            w2, [x0, #0x17]
    //     0xb212a8: add             x2, x2, HEAP, lsl #32
    //     0xb212ac: stur            x2, [fp, #-8]
    // 0xb212b0: CheckStackOverflow
    //     0xb212b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb212b4: cmp             SP, x16
    //     0xb212b8: b.ls            #0xb21380
    // 0xb212bc: LoadField: r1 = r2->field_f
    //     0xb212bc: ldur            w1, [x2, #0xf]
    // 0xb212c0: DecompressPointer r1
    //     0xb212c0: add             x1, x1, HEAP, lsl #32
    // 0xb212c4: r0 = controller()
    //     0xb212c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb212c8: mov             x1, x0
    // 0xb212cc: r0 = addLastRead()
    //     0xb212cc: bl              #0xb21388  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::addLastRead
    // 0xb212d0: ldur            x0, [fp, #-8]
    // 0xb212d4: LoadField: r1 = r0->field_13
    //     0xb212d4: ldur            w1, [x0, #0x13]
    // 0xb212d8: DecompressPointer r1
    //     0xb212d8: add             x1, x1, HEAP, lsl #32
    // 0xb212dc: r0 = of()
    //     0xb212dc: bl              #0x685314  ; [package:flutter/src/widgets/view.dart] View::of
    // 0xb212e0: stur            x0, [fp, #-8]
    // 0xb212e4: r0 = MediaQueryData()
    //     0xb212e4: bl              #0x6881b8  ; AllocateMediaQueryDataStub -> MediaQueryData (size=0x60)
    // 0xb212e8: mov             x1, x0
    // 0xb212ec: ldur            x2, [fp, #-8]
    // 0xb212f0: stur            x0, [fp, #-8]
    // 0xb212f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb212f4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb212f8: r0 = MediaQueryData.fromView()
    //     0xb212f8: bl              #0x686e10  ; [package:flutter/src/widgets/media_query.dart] MediaQueryData::MediaQueryData.fromView
    // 0xb212fc: ldur            x0, [fp, #-8]
    // 0xb21300: LoadField: r1 = r0->field_7
    //     0xb21300: ldur            w1, [x0, #7]
    // 0xb21304: DecompressPointer r1
    //     0xb21304: add             x1, x1, HEAP, lsl #32
    // 0xb21308: r0 = shortestSide()
    //     0xb21308: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0xb2130c: mov             v1.16b, v0.16b
    // 0xb21310: d0 = 720.000000
    //     0xb21310: add             x17, PP, #0xd, lsl #12  ; [pp+0xdab8] IMM: double(720) from 0x4086800000000000
    //     0xb21314: ldr             d0, [x17, #0xab8]
    // 0xb21318: fcmp            d0, d1
    // 0xb2131c: b.le            #0xb21370
    // 0xb21320: r0 = 4
    //     0xb21320: movz            x0, #0x4
    // 0xb21324: mov             x2, x0
    // 0xb21328: r1 = Null
    //     0xb21328: mov             x1, NULL
    // 0xb2132c: r0 = AllocateArray()
    //     0xb2132c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb21330: stur            x0, [fp, #-8]
    // 0xb21334: r16 = Instance_DeviceOrientation
    //     0xb21334: add             x16, PP, #0xd, lsl #12  ; [pp+0xdac0] Obj!DeviceOrientation@e34da1
    //     0xb21338: ldr             x16, [x16, #0xac0]
    // 0xb2133c: StoreField: r0->field_f = r16
    //     0xb2133c: stur            w16, [x0, #0xf]
    // 0xb21340: r16 = Instance_DeviceOrientation
    //     0xb21340: add             x16, PP, #0xd, lsl #12  ; [pp+0xdac8] Obj!DeviceOrientation@e34d81
    //     0xb21344: ldr             x16, [x16, #0xac8]
    // 0xb21348: StoreField: r0->field_13 = r16
    //     0xb21348: stur            w16, [x0, #0x13]
    // 0xb2134c: r1 = <DeviceOrientation>
    //     0xb2134c: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad0] TypeArguments: <DeviceOrientation>
    //     0xb21350: ldr             x1, [x1, #0xad0]
    // 0xb21354: r0 = AllocateGrowableArray()
    //     0xb21354: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb21358: mov             x1, x0
    // 0xb2135c: ldur            x0, [fp, #-8]
    // 0xb21360: StoreField: r1->field_f = r0
    //     0xb21360: stur            w0, [x1, #0xf]
    // 0xb21364: r0 = 4
    //     0xb21364: movz            x0, #0x4
    // 0xb21368: StoreField: r1->field_b = r0
    //     0xb21368: stur            w0, [x1, #0xb]
    // 0xb2136c: r0 = setPreferredOrientations()
    //     0xb2136c: bl              #0x97d8a8  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setPreferredOrientations
    // 0xb21370: r0 = Null
    //     0xb21370: mov             x0, NULL
    // 0xb21374: LeaveFrame
    //     0xb21374: mov             SP, fp
    //     0xb21378: ldp             fp, lr, [SP], #0x10
    // 0xb2137c: ret
    //     0xb2137c: ret             
    // 0xb21380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21384: b               #0xb212bc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb215b4, size: 0x54
    // 0xb215b4: EnterFrame
    //     0xb215b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb215b8: mov             fp, SP
    // 0xb215bc: ldr             x0, [fp, #0x10]
    // 0xb215c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb215c0: ldur            w1, [x0, #0x17]
    // 0xb215c4: DecompressPointer r1
    //     0xb215c4: add             x1, x1, HEAP, lsl #32
    // 0xb215c8: CheckStackOverflow
    //     0xb215c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb215cc: cmp             SP, x16
    //     0xb215d0: b.ls            #0xb21600
    // 0xb215d4: LoadField: r0 = r1->field_f
    //     0xb215d4: ldur            w0, [x1, #0xf]
    // 0xb215d8: DecompressPointer r0
    //     0xb215d8: add             x0, x0, HEAP, lsl #32
    // 0xb215dc: mov             x1, x0
    // 0xb215e0: r0 = controller()
    //     0xb215e0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb215e4: mov             x1, x0
    // 0xb215e8: r2 = 0
    //     0xb215e8: movz            x2, #0
    // 0xb215ec: r0 = onSelected()
    //     0xb215ec: bl              #0x8ea230  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onSelected
    // 0xb215f0: r0 = Null
    //     0xb215f0: mov             x0, NULL
    // 0xb215f4: LeaveFrame
    //     0xb215f4: mov             SP, fp
    //     0xb215f8: ldp             fp, lr, [SP], #0x10
    // 0xb215fc: ret
    //     0xb215fc: ret             
    // 0xb21600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21604: b               #0xb215d4
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Orientation) {
    // ** addr: 0xb21608, size: 0xc8
    // 0xb21608: EnterFrame
    //     0xb21608: stp             fp, lr, [SP, #-0x10]!
    //     0xb2160c: mov             fp, SP
    // 0xb21610: AllocStack(0x10)
    //     0xb21610: sub             SP, SP, #0x10
    // 0xb21614: SetupParameters()
    //     0xb21614: ldr             x0, [fp, #0x20]
    //     0xb21618: ldur            w2, [x0, #0x17]
    //     0xb2161c: add             x2, x2, HEAP, lsl #32
    // 0xb21620: ldr             x0, [fp, #0x10]
    // 0xb21624: stur            x2, [fp, #-8]
    // 0xb21628: r16 = Instance_Orientation
    //     0xb21628: add             x16, PP, #0x29, lsl #12  ; [pp+0x29a30] Obj!Orientation@e34321
    //     0xb2162c: ldr             x16, [x16, #0xa30]
    // 0xb21630: cmp             w0, w16
    // 0xb21634: b.ne            #0xb216a0
    // 0xb21638: r0 = Obx()
    //     0xb21638: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb2163c: ldur            x2, [fp, #-8]
    // 0xb21640: r1 = Function '<anonymous closure>':.
    //     0xb21640: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bec0] AnonymousClosure: (0xb2240c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb21644: ldr             x1, [x1, #0xec0]
    // 0xb21648: stur            x0, [fp, #-0x10]
    // 0xb2164c: r0 = AllocateClosure()
    //     0xb2164c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21650: mov             x1, x0
    // 0xb21654: ldur            x0, [fp, #-0x10]
    // 0xb21658: StoreField: r0->field_b = r1
    //     0xb21658: stur            w1, [x0, #0xb]
    // 0xb2165c: r0 = SafeArea()
    //     0xb2165c: bl              #0x91a5dc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb21660: mov             x1, x0
    // 0xb21664: r0 = true
    //     0xb21664: add             x0, NULL, #0x20  ; true
    // 0xb21668: StoreField: r1->field_b = r0
    //     0xb21668: stur            w0, [x1, #0xb]
    // 0xb2166c: StoreField: r1->field_f = r0
    //     0xb2166c: stur            w0, [x1, #0xf]
    // 0xb21670: StoreField: r1->field_13 = r0
    //     0xb21670: stur            w0, [x1, #0x13]
    // 0xb21674: ArrayStore: r1[0] = r0  ; List_4
    //     0xb21674: stur            w0, [x1, #0x17]
    // 0xb21678: r0 = Instance_EdgeInsets
    //     0xb21678: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb2167c: StoreField: r1->field_1b = r0
    //     0xb2167c: stur            w0, [x1, #0x1b]
    // 0xb21680: r0 = false
    //     0xb21680: add             x0, NULL, #0x30  ; false
    // 0xb21684: StoreField: r1->field_1f = r0
    //     0xb21684: stur            w0, [x1, #0x1f]
    // 0xb21688: ldur            x0, [fp, #-0x10]
    // 0xb2168c: StoreField: r1->field_23 = r0
    //     0xb2168c: stur            w0, [x1, #0x23]
    // 0xb21690: mov             x0, x1
    // 0xb21694: LeaveFrame
    //     0xb21694: mov             SP, fp
    //     0xb21698: ldp             fp, lr, [SP], #0x10
    // 0xb2169c: ret
    //     0xb2169c: ret             
    // 0xb216a0: r0 = Obx()
    //     0xb216a0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb216a4: ldur            x2, [fp, #-8]
    // 0xb216a8: r1 = Function '<anonymous closure>':.
    //     0xb216a8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bec8] AnonymousClosure: (0xb216d0), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb216ac: ldr             x1, [x1, #0xec8]
    // 0xb216b0: stur            x0, [fp, #-8]
    // 0xb216b4: r0 = AllocateClosure()
    //     0xb216b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb216b8: mov             x1, x0
    // 0xb216bc: ldur            x0, [fp, #-8]
    // 0xb216c0: StoreField: r0->field_b = r1
    //     0xb216c0: stur            w1, [x0, #0xb]
    // 0xb216c4: LeaveFrame
    //     0xb216c4: mov             SP, fp
    //     0xb216c8: ldp             fp, lr, [SP], #0x10
    // 0xb216cc: ret
    //     0xb216cc: ret             
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0xb216d0, size: 0x2e4
    // 0xb216d0: EnterFrame
    //     0xb216d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb216d4: mov             fp, SP
    // 0xb216d8: AllocStack(0x40)
    //     0xb216d8: sub             SP, SP, #0x40
    // 0xb216dc: SetupParameters()
    //     0xb216dc: ldr             x0, [fp, #0x10]
    //     0xb216e0: ldur            w2, [x0, #0x17]
    //     0xb216e4: add             x2, x2, HEAP, lsl #32
    //     0xb216e8: stur            x2, [fp, #-8]
    // 0xb216ec: CheckStackOverflow
    //     0xb216ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb216f0: cmp             SP, x16
    //     0xb216f4: b.ls            #0xb21988
    // 0xb216f8: LoadField: r1 = r2->field_f
    //     0xb216f8: ldur            w1, [x2, #0xf]
    // 0xb216fc: DecompressPointer r1
    //     0xb216fc: add             x1, x1, HEAP, lsl #32
    // 0xb21700: r0 = controller()
    //     0xb21700: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21704: LoadField: r1 = r0->field_57
    //     0xb21704: ldur            w1, [x0, #0x57]
    // 0xb21708: DecompressPointer r1
    //     0xb21708: add             x1, x1, HEAP, lsl #32
    // 0xb2170c: r0 = value()
    //     0xb2170c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21710: tbz             w0, #4, #0xb21974
    // 0xb21714: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb21714: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb21718: ldr             x0, [x0, #0x2670]
    //     0xb2171c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb21720: cmp             w0, w16
    //     0xb21724: b.ne            #0xb21730
    //     0xb21728: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb2172c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb21730: r0 = GetNavigation.size()
    //     0xb21730: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb21734: LoadField: d0 = r0->field_f
    //     0xb21734: ldur            d0, [x0, #0xf]
    // 0xb21738: d1 = 640.000000
    //     0xb21738: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed0] IMM: double(640) from 0x4084000000000000
    //     0xb2173c: ldr             d1, [x17, #0xed0]
    // 0xb21740: fcmp            d0, d1
    // 0xb21744: b.le            #0xb21790
    // 0xb21748: r0 = GetNavigation.size()
    //     0xb21748: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2174c: LoadField: d0 = r0->field_7
    //     0xb2174c: ldur            d0, [x0, #7]
    // 0xb21750: stur            d0, [fp, #-0x28]
    // 0xb21754: r0 = GetNavigation.size()
    //     0xb21754: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb21758: LoadField: d0 = r0->field_f
    //     0xb21758: ldur            d0, [x0, #0xf]
    // 0xb2175c: ldur            d1, [fp, #-0x28]
    // 0xb21760: fdiv            d2, d1, d0
    // 0xb21764: d0 = 0.550000
    //     0xb21764: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb21768: ldr             d0, [x17, #0xed8]
    // 0xb2176c: fcmp            d0, d2
    // 0xb21770: b.le            #0xb21798
    // 0xb21774: r0 = GetNavigation.size()
    //     0xb21774: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb21778: LoadField: d0 = r0->field_f
    //     0xb21778: ldur            d0, [x0, #0xf]
    // 0xb2177c: d1 = 130.000000
    //     0xb2177c: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bee0] IMM: double(130) from 0x4060400000000000
    //     0xb21780: ldr             d1, [x17, #0xee0]
    // 0xb21784: fsub            d2, d0, d1
    // 0xb21788: mov             v0.16b, v2.16b
    // 0xb2178c: b               #0xb21808
    // 0xb21790: d0 = 0.550000
    //     0xb21790: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb21794: ldr             d0, [x17, #0xed8]
    // 0xb21798: r0 = GetNavigation.size()
    //     0xb21798: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2179c: LoadField: d0 = r0->field_f
    //     0xb2179c: ldur            d0, [x0, #0xf]
    // 0xb217a0: d1 = 720.000000
    //     0xb217a0: add             x17, PP, #0xd, lsl #12  ; [pp+0xdab8] IMM: double(720) from 0x4086800000000000
    //     0xb217a4: ldr             d1, [x17, #0xab8]
    // 0xb217a8: fcmp            d0, d1
    // 0xb217ac: b.le            #0xb217f4
    // 0xb217b0: r0 = GetNavigation.size()
    //     0xb217b0: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb217b4: LoadField: d0 = r0->field_7
    //     0xb217b4: ldur            d0, [x0, #7]
    // 0xb217b8: stur            d0, [fp, #-0x28]
    // 0xb217bc: r0 = GetNavigation.size()
    //     0xb217bc: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb217c0: LoadField: d0 = r0->field_f
    //     0xb217c0: ldur            d0, [x0, #0xf]
    // 0xb217c4: ldur            d1, [fp, #-0x28]
    // 0xb217c8: fdiv            d2, d1, d0
    // 0xb217cc: d0 = 0.550000
    //     0xb217cc: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb217d0: ldr             d0, [x17, #0xed8]
    // 0xb217d4: fcmp            d0, d2
    // 0xb217d8: b.le            #0xb217f4
    // 0xb217dc: r0 = GetNavigation.size()
    //     0xb217dc: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb217e0: LoadField: d0 = r0->field_f
    //     0xb217e0: ldur            d0, [x0, #0xf]
    // 0xb217e4: d1 = 180.000000
    //     0xb217e4: ldr             d1, [PP, #0x5a50]  ; [pp+0x5a50] IMM: double(180) from 0x4066800000000000
    // 0xb217e8: fsub            d2, d0, d1
    // 0xb217ec: mov             v0.16b, v2.16b
    // 0xb217f0: b               #0xb21808
    // 0xb217f4: r0 = GetNavigation.size()
    //     0xb217f4: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb217f8: LoadField: d0 = r0->field_f
    //     0xb217f8: ldur            d0, [x0, #0xf]
    // 0xb217fc: d1 = 80.000000
    //     0xb217fc: ldr             d1, [PP, #0x65c0]  ; [pp+0x65c0] IMM: double(80) from 0x4054000000000000
    // 0xb21800: fsub            d2, d0, d1
    // 0xb21804: mov             v0.16b, v2.16b
    // 0xb21808: ldur            x2, [fp, #-8]
    // 0xb2180c: stur            d0, [fp, #-0x28]
    // 0xb21810: LoadField: r1 = r2->field_f
    //     0xb21810: ldur            w1, [x2, #0xf]
    // 0xb21814: DecompressPointer r1
    //     0xb21814: add             x1, x1, HEAP, lsl #32
    // 0xb21818: r0 = controller()
    //     0xb21818: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2181c: ldur            x2, [fp, #-8]
    // 0xb21820: LoadField: r1 = r2->field_f
    //     0xb21820: ldur            w1, [x2, #0xf]
    // 0xb21824: DecompressPointer r1
    //     0xb21824: add             x1, x1, HEAP, lsl #32
    // 0xb21828: r0 = controller()
    //     0xb21828: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2182c: LoadField: r2 = r0->field_6f
    //     0xb2182c: ldur            w2, [x0, #0x6f]
    // 0xb21830: DecompressPointer r2
    //     0xb21830: add             x2, x2, HEAP, lsl #32
    // 0xb21834: r16 = Sentinel
    //     0xb21834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb21838: cmp             w2, w16
    // 0xb2183c: b.eq            #0xb21990
    // 0xb21840: ldur            x0, [fp, #-8]
    // 0xb21844: stur            x2, [fp, #-0x10]
    // 0xb21848: LoadField: r1 = r0->field_f
    //     0xb21848: ldur            w1, [x0, #0xf]
    // 0xb2184c: DecompressPointer r1
    //     0xb2184c: add             x1, x1, HEAP, lsl #32
    // 0xb21850: r0 = controller()
    //     0xb21850: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21854: mov             x2, x0
    // 0xb21858: r1 = Function 'onPageChanged':.
    //     0xb21858: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bee8] AnonymousClosure: (0xb2236c), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onPageChanged (0xb223c0)
    //     0xb2185c: ldr             x1, [x1, #0xee8]
    // 0xb21860: r0 = AllocateClosure()
    //     0xb21860: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21864: ldur            x2, [fp, #-8]
    // 0xb21868: r1 = Function '<anonymous closure>':.
    //     0xb21868: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bef0] AnonymousClosure: (0xb219b4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb2186c: ldr             x1, [x1, #0xef0]
    // 0xb21870: stur            x0, [fp, #-8]
    // 0xb21874: r0 = AllocateClosure()
    //     0xb21874: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21878: stur            x0, [fp, #-0x18]
    // 0xb2187c: r0 = PageView()
    //     0xb2187c: bl              #0x9d332c  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb21880: stur            x0, [fp, #-0x20]
    // 0xb21884: r16 = Instance_PageStorageKey
    //     0xb21884: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bef8] Obj!PageStorageKey<String>@e14be1
    //     0xb21888: ldr             x16, [x16, #0xef8]
    // 0xb2188c: r30 = true
    //     0xb2188c: add             lr, NULL, #0x20  ; true
    // 0xb21890: stp             lr, x16, [SP, #8]
    // 0xb21894: r16 = 1208
    //     0xb21894: movz            x16, #0x4b8
    // 0xb21898: str             x16, [SP]
    // 0xb2189c: mov             x1, x0
    // 0xb218a0: ldur            x2, [fp, #-0x10]
    // 0xb218a4: ldur            x3, [fp, #-0x18]
    // 0xb218a8: ldur            x5, [fp, #-8]
    // 0xb218ac: r4 = const [0, 0x7, 0x3, 0x4, itemCount, 0x6, key, 0x4, reverse, 0x5, null]
    //     0xb218ac: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bf00] List(11) [0, 0x7, 0x3, 0x4, "itemCount", 0x6, "key", 0x4, "reverse", 0x5, Null]
    //     0xb218b0: ldr             x4, [x4, #0xf00]
    // 0xb218b4: r0 = PageView.builder()
    //     0xb218b4: bl              #0x9d3014  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb218b8: r1 = Null
    //     0xb218b8: mov             x1, NULL
    // 0xb218bc: r2 = 2
    //     0xb218bc: movz            x2, #0x2
    // 0xb218c0: r0 = AllocateArray()
    //     0xb218c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb218c4: mov             x2, x0
    // 0xb218c8: ldur            x0, [fp, #-0x20]
    // 0xb218cc: stur            x2, [fp, #-8]
    // 0xb218d0: StoreField: r2->field_f = r0
    //     0xb218d0: stur            w0, [x2, #0xf]
    // 0xb218d4: r1 = <Widget>
    //     0xb218d4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb218d8: r0 = AllocateGrowableArray()
    //     0xb218d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb218dc: mov             x1, x0
    // 0xb218e0: ldur            x0, [fp, #-8]
    // 0xb218e4: stur            x1, [fp, #-0x10]
    // 0xb218e8: StoreField: r1->field_f = r0
    //     0xb218e8: stur            w0, [x1, #0xf]
    // 0xb218ec: r0 = 2
    //     0xb218ec: movz            x0, #0x2
    // 0xb218f0: StoreField: r1->field_b = r0
    //     0xb218f0: stur            w0, [x1, #0xb]
    // 0xb218f4: r0 = Stack()
    //     0xb218f4: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb218f8: mov             x1, x0
    // 0xb218fc: r0 = Instance_AlignmentDirectional
    //     0xb218fc: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xb21900: ldr             x0, [x0, #0x7b0]
    // 0xb21904: stur            x1, [fp, #-0x18]
    // 0xb21908: StoreField: r1->field_f = r0
    //     0xb21908: stur            w0, [x1, #0xf]
    // 0xb2190c: r0 = Instance_StackFit
    //     0xb2190c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xb21910: ldr             x0, [x0, #0x7b8]
    // 0xb21914: ArrayStore: r1[0] = r0  ; List_4
    //     0xb21914: stur            w0, [x1, #0x17]
    // 0xb21918: r0 = Instance_Clip
    //     0xb21918: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb2191c: ldr             x0, [x0, #0x7c0]
    // 0xb21920: StoreField: r1->field_1b = r0
    //     0xb21920: stur            w0, [x1, #0x1b]
    // 0xb21924: ldur            x0, [fp, #-0x10]
    // 0xb21928: StoreField: r1->field_b = r0
    //     0xb21928: stur            w0, [x1, #0xb]
    // 0xb2192c: ldur            d0, [fp, #-0x28]
    // 0xb21930: r0 = inline_Allocate_Double()
    //     0xb21930: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb21934: add             x0, x0, #0x10
    //     0xb21938: cmp             x2, x0
    //     0xb2193c: b.ls            #0xb2199c
    //     0xb21940: str             x0, [THR, #0x50]  ; THR::top
    //     0xb21944: sub             x0, x0, #0xf
    //     0xb21948: movz            x2, #0xe15c
    //     0xb2194c: movk            x2, #0x3, lsl #16
    //     0xb21950: stur            x2, [x0, #-1]
    // 0xb21954: StoreField: r0->field_7 = d0
    //     0xb21954: stur            d0, [x0, #7]
    // 0xb21958: stur            x0, [fp, #-8]
    // 0xb2195c: r0 = SizedBox()
    //     0xb2195c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb21960: ldur            x1, [fp, #-8]
    // 0xb21964: StoreField: r0->field_13 = r1
    //     0xb21964: stur            w1, [x0, #0x13]
    // 0xb21968: ldur            x1, [fp, #-0x18]
    // 0xb2196c: StoreField: r0->field_b = r1
    //     0xb2196c: stur            w1, [x0, #0xb]
    // 0xb21970: b               #0xb2197c
    // 0xb21974: r0 = Instance_Center
    //     0xb21974: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5c8] Obj!Center@e1e761
    //     0xb21978: ldr             x0, [x0, #0x5c8]
    // 0xb2197c: LeaveFrame
    //     0xb2197c: mov             SP, fp
    //     0xb21980: ldp             fp, lr, [SP], #0x10
    // 0xb21984: ret
    //     0xb21984: ret             
    // 0xb21988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2198c: b               #0xb216f8
    // 0xb21990: r9 = pageController
    //     0xb21990: add             x9, PP, #0x2b, lsl #12  ; [pp+0x2bf08] Field <QuranPageController.pageController>: late (offset: 0x70)
    //     0xb21994: ldr             x9, [x9, #0xf08]
    // 0xb21998: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb21998: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb2199c: SaveReg d0
    //     0xb2199c: str             q0, [SP, #-0x10]!
    // 0xb219a0: SaveReg r1
    //     0xb219a0: str             x1, [SP, #-8]!
    // 0xb219a4: r0 = AllocateDouble()
    //     0xb219a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb219a8: RestoreReg r1
    //     0xb219a8: ldr             x1, [SP], #8
    // 0xb219ac: RestoreReg d0
    //     0xb219ac: ldr             q0, [SP], #0x10
    // 0xb219b0: b               #0xb21954
  }
  [closure] QuranPage <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb219b4, size: 0x124
    // 0xb219b4: EnterFrame
    //     0xb219b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb219b8: mov             fp, SP
    // 0xb219bc: AllocStack(0x28)
    //     0xb219bc: sub             SP, SP, #0x28
    // 0xb219c0: SetupParameters()
    //     0xb219c0: ldr             x0, [fp, #0x20]
    //     0xb219c4: ldur            w1, [x0, #0x17]
    //     0xb219c8: add             x1, x1, HEAP, lsl #32
    //     0xb219cc: stur            x1, [fp, #-8]
    // 0xb219d0: CheckStackOverflow
    //     0xb219d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb219d4: cmp             SP, x16
    //     0xb219d8: b.ls            #0xb21ad0
    // 0xb219dc: r1 = 1
    //     0xb219dc: movz            x1, #0x1
    // 0xb219e0: r0 = AllocateContext()
    //     0xb219e0: bl              #0xec126c  ; AllocateContextStub
    // 0xb219e4: mov             x2, x0
    // 0xb219e8: ldur            x0, [fp, #-8]
    // 0xb219ec: stur            x2, [fp, #-0x20]
    // 0xb219f0: StoreField: r2->field_b = r0
    //     0xb219f0: stur            w0, [x2, #0xb]
    // 0xb219f4: ldr             x1, [fp, #0x10]
    // 0xb219f8: StoreField: r2->field_f = r1
    //     0xb219f8: stur            w1, [x2, #0xf]
    // 0xb219fc: r3 = LoadInt32Instr(r1)
    //     0xb219fc: sbfx            x3, x1, #1, #0x1f
    //     0xb21a00: tbz             w1, #0, #0xb21a08
    //     0xb21a04: ldur            x3, [x1, #7]
    // 0xb21a08: stur            x3, [fp, #-0x18]
    // 0xb21a0c: add             x4, x3, #1
    // 0xb21a10: stur            x4, [fp, #-0x10]
    // 0xb21a14: LoadField: r1 = r0->field_f
    //     0xb21a14: ldur            w1, [x0, #0xf]
    // 0xb21a18: DecompressPointer r1
    //     0xb21a18: add             x1, x1, HEAP, lsl #32
    // 0xb21a1c: r0 = controller()
    //     0xb21a1c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21a20: LoadField: r1 = r0->field_5b
    //     0xb21a20: ldur            w1, [x0, #0x5b]
    // 0xb21a24: DecompressPointer r1
    //     0xb21a24: add             x1, x1, HEAP, lsl #32
    // 0xb21a28: r0 = value()
    //     0xb21a28: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21a2c: r1 = LoadInt32Instr(r0)
    //     0xb21a2c: sbfx            x1, x0, #1, #0x1f
    //     0xb21a30: tbz             w0, #0, #0xb21a38
    //     0xb21a34: ldur            x1, [x0, #7]
    // 0xb21a38: ldur            x0, [fp, #-0x18]
    // 0xb21a3c: add             x2, x0, x1
    // 0xb21a40: r0 = BoxInt64Instr(r2)
    //     0xb21a40: sbfiz           x0, x2, #1, #0x1f
    //     0xb21a44: cmp             x2, x0, asr #1
    //     0xb21a48: b.eq            #0xb21a54
    //     0xb21a4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb21a50: stur            x2, [x0, #7]
    // 0xb21a54: r1 = 60
    //     0xb21a54: movz            x1, #0x3c
    // 0xb21a58: branchIfSmi(r0, 0xb21a64)
    //     0xb21a58: tbz             w0, #0, #0xb21a64
    // 0xb21a5c: r1 = LoadClassIdInstr(r0)
    //     0xb21a5c: ldur            x1, [x0, #-1]
    //     0xb21a60: ubfx            x1, x1, #0xc, #0x14
    // 0xb21a64: str             x0, [SP]
    // 0xb21a68: mov             x0, x1
    // 0xb21a6c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb21a6c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb21a70: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb21a70: movz            x17, #0x2b03
    //     0xb21a74: add             lr, x0, x17
    //     0xb21a78: ldr             lr, [x21, lr, lsl #3]
    //     0xb21a7c: blr             lr
    // 0xb21a80: stur            x0, [fp, #-8]
    // 0xb21a84: r0 = Obx()
    //     0xb21a84: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb21a88: ldur            x2, [fp, #-0x20]
    // 0xb21a8c: r1 = Function '<anonymous closure>':.
    //     0xb21a8c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bf10] AnonymousClosure: (0xb21ae4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb21a90: ldr             x1, [x1, #0xf10]
    // 0xb21a94: stur            x0, [fp, #-0x20]
    // 0xb21a98: r0 = AllocateClosure()
    //     0xb21a98: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21a9c: mov             x1, x0
    // 0xb21aa0: ldur            x0, [fp, #-0x20]
    // 0xb21aa4: StoreField: r0->field_b = r1
    //     0xb21aa4: stur            w1, [x0, #0xb]
    // 0xb21aa8: r0 = QuranPage()
    //     0xb21aa8: bl              #0xb21ad8  ; AllocateQuranPageStub -> QuranPage (size=0x20)
    // 0xb21aac: ldur            x1, [fp, #-0x10]
    // 0xb21ab0: StoreField: r0->field_b = r1
    //     0xb21ab0: stur            x1, [x0, #0xb]
    // 0xb21ab4: ldur            x1, [fp, #-0x20]
    // 0xb21ab8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb21ab8: stur            w1, [x0, #0x17]
    // 0xb21abc: ldur            x1, [fp, #-8]
    // 0xb21ac0: StoreField: r0->field_13 = r1
    //     0xb21ac0: stur            w1, [x0, #0x13]
    // 0xb21ac4: LeaveFrame
    //     0xb21ac4: mov             SP, fp
    //     0xb21ac8: ldp             fp, lr, [SP], #0x10
    // 0xb21acc: ret
    //     0xb21acc: ret             
    // 0xb21ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21ad4: b               #0xb219dc
  }
  [closure] QuranVerses <anonymous closure>(dynamic) {
    // ** addr: 0xb21ae4, size: 0x27c
    // 0xb21ae4: EnterFrame
    //     0xb21ae4: stp             fp, lr, [SP, #-0x10]!
    //     0xb21ae8: mov             fp, SP
    // 0xb21aec: AllocStack(0x50)
    //     0xb21aec: sub             SP, SP, #0x50
    // 0xb21af0: SetupParameters()
    //     0xb21af0: ldr             x0, [fp, #0x10]
    //     0xb21af4: ldur            w2, [x0, #0x17]
    //     0xb21af8: add             x2, x2, HEAP, lsl #32
    //     0xb21afc: stur            x2, [fp, #-0x10]
    // 0xb21b00: CheckStackOverflow
    //     0xb21b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb21b04: cmp             SP, x16
    //     0xb21b08: b.ls            #0xb21d58
    // 0xb21b0c: LoadField: r0 = r2->field_b
    //     0xb21b0c: ldur            w0, [x2, #0xb]
    // 0xb21b10: DecompressPointer r0
    //     0xb21b10: add             x0, x0, HEAP, lsl #32
    // 0xb21b14: stur            x0, [fp, #-8]
    // 0xb21b18: LoadField: r1 = r0->field_f
    //     0xb21b18: ldur            w1, [x0, #0xf]
    // 0xb21b1c: DecompressPointer r1
    //     0xb21b1c: add             x1, x1, HEAP, lsl #32
    // 0xb21b20: r0 = controller()
    //     0xb21b20: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21b24: mov             x1, x0
    // 0xb21b28: ldur            x0, [fp, #-0x10]
    // 0xb21b2c: LoadField: r2 = r0->field_f
    //     0xb21b2c: ldur            w2, [x0, #0xf]
    // 0xb21b30: DecompressPointer r2
    //     0xb21b30: add             x2, x2, HEAP, lsl #32
    // 0xb21b34: r0 = LoadInt32Instr(r2)
    //     0xb21b34: sbfx            x0, x2, #1, #0x1f
    //     0xb21b38: tbz             w2, #0, #0xb21b40
    //     0xb21b3c: ldur            x0, [x2, #7]
    // 0xb21b40: add             x2, x0, #1
    // 0xb21b44: r0 = versesByPageId()
    //     0xb21b44: bl              #0xb21d6c  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::versesByPageId
    // 0xb21b48: mov             x2, x0
    // 0xb21b4c: ldur            x0, [fp, #-8]
    // 0xb21b50: stur            x2, [fp, #-0x10]
    // 0xb21b54: LoadField: r1 = r0->field_f
    //     0xb21b54: ldur            w1, [x0, #0xf]
    // 0xb21b58: DecompressPointer r1
    //     0xb21b58: add             x1, x1, HEAP, lsl #32
    // 0xb21b5c: r0 = controller()
    //     0xb21b5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21b60: mov             x2, x0
    // 0xb21b64: ldur            x0, [fp, #-8]
    // 0xb21b68: stur            x2, [fp, #-0x18]
    // 0xb21b6c: LoadField: r1 = r0->field_f
    //     0xb21b6c: ldur            w1, [x0, #0xf]
    // 0xb21b70: DecompressPointer r1
    //     0xb21b70: add             x1, x1, HEAP, lsl #32
    // 0xb21b74: r0 = controller()
    //     0xb21b74: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21b78: mov             x2, x0
    // 0xb21b7c: ldur            x0, [fp, #-8]
    // 0xb21b80: stur            x2, [fp, #-0x20]
    // 0xb21b84: LoadField: r1 = r0->field_f
    //     0xb21b84: ldur            w1, [x0, #0xf]
    // 0xb21b88: DecompressPointer r1
    //     0xb21b88: add             x1, x1, HEAP, lsl #32
    // 0xb21b8c: r0 = controller()
    //     0xb21b8c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21b90: LoadField: r1 = r0->field_4f
    //     0xb21b90: ldur            w1, [x0, #0x4f]
    // 0xb21b94: DecompressPointer r1
    //     0xb21b94: add             x1, x1, HEAP, lsl #32
    // 0xb21b98: r0 = value()
    //     0xb21b98: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21b9c: mov             x2, x0
    // 0xb21ba0: ldur            x0, [fp, #-8]
    // 0xb21ba4: stur            x2, [fp, #-0x28]
    // 0xb21ba8: LoadField: r1 = r0->field_f
    //     0xb21ba8: ldur            w1, [x0, #0xf]
    // 0xb21bac: DecompressPointer r1
    //     0xb21bac: add             x1, x1, HEAP, lsl #32
    // 0xb21bb0: r0 = controller()
    //     0xb21bb0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21bb4: LoadField: r1 = r0->field_53
    //     0xb21bb4: ldur            w1, [x0, #0x53]
    // 0xb21bb8: DecompressPointer r1
    //     0xb21bb8: add             x1, x1, HEAP, lsl #32
    // 0xb21bbc: r0 = value()
    //     0xb21bbc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21bc0: mov             x2, x0
    // 0xb21bc4: ldur            x0, [fp, #-8]
    // 0xb21bc8: stur            x2, [fp, #-0x30]
    // 0xb21bcc: LoadField: r1 = r0->field_f
    //     0xb21bcc: ldur            w1, [x0, #0xf]
    // 0xb21bd0: DecompressPointer r1
    //     0xb21bd0: add             x1, x1, HEAP, lsl #32
    // 0xb21bd4: r0 = controller()
    //     0xb21bd4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21bd8: mov             x2, x0
    // 0xb21bdc: ldur            x0, [fp, #-8]
    // 0xb21be0: stur            x2, [fp, #-0x38]
    // 0xb21be4: LoadField: r1 = r0->field_f
    //     0xb21be4: ldur            w1, [x0, #0xf]
    // 0xb21be8: DecompressPointer r1
    //     0xb21be8: add             x1, x1, HEAP, lsl #32
    // 0xb21bec: r0 = controller()
    //     0xb21bec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21bf0: LoadField: r1 = r0->field_87
    //     0xb21bf0: ldur            w1, [x0, #0x87]
    // 0xb21bf4: DecompressPointer r1
    //     0xb21bf4: add             x1, x1, HEAP, lsl #32
    // 0xb21bf8: r0 = value()
    //     0xb21bf8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21bfc: cmp             w0, NULL
    // 0xb21c00: b.ne            #0xb21c0c
    // 0xb21c04: r5 = Null
    //     0xb21c04: mov             x5, NULL
    // 0xb21c08: b               #0xb21c28
    // 0xb21c0c: LoadField: r2 = r0->field_7
    //     0xb21c0c: ldur            x2, [x0, #7]
    // 0xb21c10: r0 = BoxInt64Instr(r2)
    //     0xb21c10: sbfiz           x0, x2, #1, #0x1f
    //     0xb21c14: cmp             x2, x0, asr #1
    //     0xb21c18: b.eq            #0xb21c24
    //     0xb21c1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb21c20: stur            x2, [x0, #7]
    // 0xb21c24: mov             x5, x0
    // 0xb21c28: ldur            x0, [fp, #-8]
    // 0xb21c2c: ldur            x4, [fp, #-0x10]
    // 0xb21c30: ldur            x3, [fp, #-0x28]
    // 0xb21c34: ldur            x2, [fp, #-0x30]
    // 0xb21c38: stur            x5, [fp, #-0x40]
    // 0xb21c3c: LoadField: r1 = r0->field_f
    //     0xb21c3c: ldur            w1, [x0, #0xf]
    // 0xb21c40: DecompressPointer r1
    //     0xb21c40: add             x1, x1, HEAP, lsl #32
    // 0xb21c44: r0 = controller()
    //     0xb21c44: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21c48: mov             x2, x0
    // 0xb21c4c: ldur            x0, [fp, #-8]
    // 0xb21c50: stur            x2, [fp, #-0x48]
    // 0xb21c54: LoadField: r1 = r0->field_f
    //     0xb21c54: ldur            w1, [x0, #0xf]
    // 0xb21c58: DecompressPointer r1
    //     0xb21c58: add             x1, x1, HEAP, lsl #32
    // 0xb21c5c: r0 = controller()
    //     0xb21c5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb21c60: mov             x1, x0
    // 0xb21c64: LoadField: r0 = r1->field_5f
    //     0xb21c64: ldur            w0, [x1, #0x5f]
    // 0xb21c68: DecompressPointer r0
    //     0xb21c68: add             x0, x0, HEAP, lsl #32
    // 0xb21c6c: r16 = Sentinel
    //     0xb21c6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb21c70: cmp             w0, w16
    // 0xb21c74: b.ne            #0xb21c84
    // 0xb21c78: r2 = showTajweed
    //     0xb21c78: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bf18] Field <QuranPageController.showTajweed>: late final (offset: 0x60)
    //     0xb21c7c: ldr             x2, [x2, #0xf18]
    // 0xb21c80: r0 = InitLateFinalInstanceField()
    //     0xb21c80: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb21c84: mov             x1, x0
    // 0xb21c88: r0 = value()
    //     0xb21c88: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb21c8c: stur            x0, [fp, #-8]
    // 0xb21c90: r0 = QuranVerses()
    //     0xb21c90: bl              #0xb21d60  ; AllocateQuranVersesStub -> QuranVerses (size=0x38)
    // 0xb21c94: mov             x3, x0
    // 0xb21c98: ldur            x0, [fp, #-0x10]
    // 0xb21c9c: stur            x3, [fp, #-0x50]
    // 0xb21ca0: StoreField: r3->field_b = r0
    //     0xb21ca0: stur            w0, [x3, #0xb]
    // 0xb21ca4: ldur            x2, [fp, #-0x18]
    // 0xb21ca8: r1 = Function 'findSurah':.
    //     0xb21ca8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bf20] AnonymousClosure: (0x8c2428), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::findSurah (0x8c23e0)
    //     0xb21cac: ldr             x1, [x1, #0xf20]
    // 0xb21cb0: r0 = AllocateClosure()
    //     0xb21cb0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21cb4: mov             x1, x0
    // 0xb21cb8: ldur            x0, [fp, #-0x50]
    // 0xb21cbc: StoreField: r0->field_f = r1
    //     0xb21cbc: stur            w1, [x0, #0xf]
    // 0xb21cc0: ldur            x1, [fp, #-0x28]
    // 0xb21cc4: r2 = LoadInt32Instr(r1)
    //     0xb21cc4: sbfx            x2, x1, #1, #0x1f
    //     0xb21cc8: tbz             w1, #0, #0xb21cd0
    //     0xb21ccc: ldur            x2, [x1, #7]
    // 0xb21cd0: StoreField: r0->field_13 = r2
    //     0xb21cd0: stur            x2, [x0, #0x13]
    // 0xb21cd4: ldur            x1, [fp, #-0x30]
    // 0xb21cd8: r2 = LoadInt32Instr(r1)
    //     0xb21cd8: sbfx            x2, x1, #1, #0x1f
    //     0xb21cdc: tbz             w1, #0, #0xb21ce4
    //     0xb21ce0: ldur            x2, [x1, #7]
    // 0xb21ce4: StoreField: r0->field_1b = r2
    //     0xb21ce4: stur            x2, [x0, #0x1b]
    // 0xb21ce8: ldur            x2, [fp, #-0x20]
    // 0xb21cec: r1 = Function 'onSelected':.
    //     0xb21cec: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bf28] AnonymousClosure: (0x8ea26c), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onSelected (0x8ea230)
    //     0xb21cf0: ldr             x1, [x1, #0xf28]
    // 0xb21cf4: r0 = AllocateClosure()
    //     0xb21cf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21cf8: mov             x1, x0
    // 0xb21cfc: ldur            x0, [fp, #-0x50]
    // 0xb21d00: StoreField: r0->field_23 = r1
    //     0xb21d00: stur            w1, [x0, #0x23]
    // 0xb21d04: ldur            x2, [fp, #-0x38]
    // 0xb21d08: r1 = Function 'onBookmark':.
    //     0xb21d08: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bf30] AnonymousClosure: (0xb21f90), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onBookmark (0xb21fd0)
    //     0xb21d0c: ldr             x1, [x1, #0xf30]
    // 0xb21d10: r0 = AllocateClosure()
    //     0xb21d10: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21d14: mov             x1, x0
    // 0xb21d18: ldur            x0, [fp, #-0x50]
    // 0xb21d1c: StoreField: r0->field_27 = r1
    //     0xb21d1c: stur            w1, [x0, #0x27]
    // 0xb21d20: ldur            x1, [fp, #-0x40]
    // 0xb21d24: StoreField: r0->field_2b = r1
    //     0xb21d24: stur            w1, [x0, #0x2b]
    // 0xb21d28: ldur            x2, [fp, #-0x48]
    // 0xb21d2c: r1 = Function 'onPlayPressed':.
    //     0xb21d2c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bf38] AnonymousClosure: (0xb21e5c), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onPlayPressed (0xb21e98)
    //     0xb21d30: ldr             x1, [x1, #0xf38]
    // 0xb21d34: r0 = AllocateClosure()
    //     0xb21d34: bl              #0xec1630  ; AllocateClosureStub
    // 0xb21d38: mov             x1, x0
    // 0xb21d3c: ldur            x0, [fp, #-0x50]
    // 0xb21d40: StoreField: r0->field_2f = r1
    //     0xb21d40: stur            w1, [x0, #0x2f]
    // 0xb21d44: ldur            x1, [fp, #-8]
    // 0xb21d48: StoreField: r0->field_33 = r1
    //     0xb21d48: stur            w1, [x0, #0x33]
    // 0xb21d4c: LeaveFrame
    //     0xb21d4c: mov             SP, fp
    //     0xb21d50: ldp             fp, lr, [SP], #0x10
    // 0xb21d54: ret
    //     0xb21d54: ret             
    // 0xb21d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21d58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21d5c: b               #0xb21b0c
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0xb2240c, size: 0x2e4
    // 0xb2240c: EnterFrame
    //     0xb2240c: stp             fp, lr, [SP, #-0x10]!
    //     0xb22410: mov             fp, SP
    // 0xb22414: AllocStack(0x40)
    //     0xb22414: sub             SP, SP, #0x40
    // 0xb22418: SetupParameters()
    //     0xb22418: ldr             x0, [fp, #0x10]
    //     0xb2241c: ldur            w2, [x0, #0x17]
    //     0xb22420: add             x2, x2, HEAP, lsl #32
    //     0xb22424: stur            x2, [fp, #-8]
    // 0xb22428: CheckStackOverflow
    //     0xb22428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2242c: cmp             SP, x16
    //     0xb22430: b.ls            #0xb226c4
    // 0xb22434: LoadField: r1 = r2->field_f
    //     0xb22434: ldur            w1, [x2, #0xf]
    // 0xb22438: DecompressPointer r1
    //     0xb22438: add             x1, x1, HEAP, lsl #32
    // 0xb2243c: r0 = controller()
    //     0xb2243c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22440: LoadField: r1 = r0->field_57
    //     0xb22440: ldur            w1, [x0, #0x57]
    // 0xb22444: DecompressPointer r1
    //     0xb22444: add             x1, x1, HEAP, lsl #32
    // 0xb22448: r0 = value()
    //     0xb22448: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb2244c: tbz             w0, #4, #0xb226b0
    // 0xb22450: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb22450: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb22454: ldr             x0, [x0, #0x2670]
    //     0xb22458: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2245c: cmp             w0, w16
    //     0xb22460: b.ne            #0xb2246c
    //     0xb22464: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb22468: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb2246c: r0 = GetNavigation.size()
    //     0xb2246c: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb22470: LoadField: d0 = r0->field_f
    //     0xb22470: ldur            d0, [x0, #0xf]
    // 0xb22474: d1 = 640.000000
    //     0xb22474: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed0] IMM: double(640) from 0x4084000000000000
    //     0xb22478: ldr             d1, [x17, #0xed0]
    // 0xb2247c: fcmp            d0, d1
    // 0xb22480: b.le            #0xb224cc
    // 0xb22484: r0 = GetNavigation.size()
    //     0xb22484: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb22488: LoadField: d0 = r0->field_7
    //     0xb22488: ldur            d0, [x0, #7]
    // 0xb2248c: stur            d0, [fp, #-0x28]
    // 0xb22490: r0 = GetNavigation.size()
    //     0xb22490: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb22494: LoadField: d0 = r0->field_f
    //     0xb22494: ldur            d0, [x0, #0xf]
    // 0xb22498: ldur            d1, [fp, #-0x28]
    // 0xb2249c: fdiv            d2, d1, d0
    // 0xb224a0: d0 = 0.550000
    //     0xb224a0: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb224a4: ldr             d0, [x17, #0xed8]
    // 0xb224a8: fcmp            d0, d2
    // 0xb224ac: b.le            #0xb224d4
    // 0xb224b0: r0 = GetNavigation.size()
    //     0xb224b0: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb224b4: LoadField: d0 = r0->field_f
    //     0xb224b4: ldur            d0, [x0, #0xf]
    // 0xb224b8: d1 = 130.000000
    //     0xb224b8: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bee0] IMM: double(130) from 0x4060400000000000
    //     0xb224bc: ldr             d1, [x17, #0xee0]
    // 0xb224c0: fsub            d2, d0, d1
    // 0xb224c4: mov             v0.16b, v2.16b
    // 0xb224c8: b               #0xb22544
    // 0xb224cc: d0 = 0.550000
    //     0xb224cc: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb224d0: ldr             d0, [x17, #0xed8]
    // 0xb224d4: r0 = GetNavigation.size()
    //     0xb224d4: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb224d8: LoadField: d0 = r0->field_f
    //     0xb224d8: ldur            d0, [x0, #0xf]
    // 0xb224dc: d1 = 720.000000
    //     0xb224dc: add             x17, PP, #0xd, lsl #12  ; [pp+0xdab8] IMM: double(720) from 0x4086800000000000
    //     0xb224e0: ldr             d1, [x17, #0xab8]
    // 0xb224e4: fcmp            d0, d1
    // 0xb224e8: b.le            #0xb22530
    // 0xb224ec: r0 = GetNavigation.size()
    //     0xb224ec: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb224f0: LoadField: d0 = r0->field_7
    //     0xb224f0: ldur            d0, [x0, #7]
    // 0xb224f4: stur            d0, [fp, #-0x28]
    // 0xb224f8: r0 = GetNavigation.size()
    //     0xb224f8: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb224fc: LoadField: d0 = r0->field_f
    //     0xb224fc: ldur            d0, [x0, #0xf]
    // 0xb22500: ldur            d1, [fp, #-0x28]
    // 0xb22504: fdiv            d2, d1, d0
    // 0xb22508: d0 = 0.550000
    //     0xb22508: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2bed8] IMM: double(0.55) from 0x3fe199999999999a
    //     0xb2250c: ldr             d0, [x17, #0xed8]
    // 0xb22510: fcmp            d0, d2
    // 0xb22514: b.le            #0xb22530
    // 0xb22518: r0 = GetNavigation.size()
    //     0xb22518: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2251c: LoadField: d0 = r0->field_f
    //     0xb2251c: ldur            d0, [x0, #0xf]
    // 0xb22520: d1 = 180.000000
    //     0xb22520: ldr             d1, [PP, #0x5a50]  ; [pp+0x5a50] IMM: double(180) from 0x4066800000000000
    // 0xb22524: fsub            d2, d0, d1
    // 0xb22528: mov             v0.16b, v2.16b
    // 0xb2252c: b               #0xb22544
    // 0xb22530: r0 = GetNavigation.size()
    //     0xb22530: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb22534: LoadField: d0 = r0->field_f
    //     0xb22534: ldur            d0, [x0, #0xf]
    // 0xb22538: d1 = 80.000000
    //     0xb22538: ldr             d1, [PP, #0x65c0]  ; [pp+0x65c0] IMM: double(80) from 0x4054000000000000
    // 0xb2253c: fsub            d2, d0, d1
    // 0xb22540: mov             v0.16b, v2.16b
    // 0xb22544: ldur            x2, [fp, #-8]
    // 0xb22548: stur            d0, [fp, #-0x28]
    // 0xb2254c: LoadField: r1 = r2->field_f
    //     0xb2254c: ldur            w1, [x2, #0xf]
    // 0xb22550: DecompressPointer r1
    //     0xb22550: add             x1, x1, HEAP, lsl #32
    // 0xb22554: r0 = controller()
    //     0xb22554: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22558: ldur            x2, [fp, #-8]
    // 0xb2255c: LoadField: r1 = r2->field_f
    //     0xb2255c: ldur            w1, [x2, #0xf]
    // 0xb22560: DecompressPointer r1
    //     0xb22560: add             x1, x1, HEAP, lsl #32
    // 0xb22564: r0 = controller()
    //     0xb22564: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22568: LoadField: r2 = r0->field_6f
    //     0xb22568: ldur            w2, [x0, #0x6f]
    // 0xb2256c: DecompressPointer r2
    //     0xb2256c: add             x2, x2, HEAP, lsl #32
    // 0xb22570: r16 = Sentinel
    //     0xb22570: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb22574: cmp             w2, w16
    // 0xb22578: b.eq            #0xb226cc
    // 0xb2257c: ldur            x0, [fp, #-8]
    // 0xb22580: stur            x2, [fp, #-0x10]
    // 0xb22584: LoadField: r1 = r0->field_f
    //     0xb22584: ldur            w1, [x0, #0xf]
    // 0xb22588: DecompressPointer r1
    //     0xb22588: add             x1, x1, HEAP, lsl #32
    // 0xb2258c: r0 = controller()
    //     0xb2258c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22590: mov             x2, x0
    // 0xb22594: r1 = Function 'onPageChanged':.
    //     0xb22594: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bee8] AnonymousClosure: (0xb2236c), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onPageChanged (0xb223c0)
    //     0xb22598: ldr             x1, [x1, #0xee8]
    // 0xb2259c: r0 = AllocateClosure()
    //     0xb2259c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb225a0: ldur            x2, [fp, #-8]
    // 0xb225a4: r1 = Function '<anonymous closure>':.
    //     0xb225a4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c0b0] AnonymousClosure: (0xb226f0), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb225a8: ldr             x1, [x1, #0xb0]
    // 0xb225ac: stur            x0, [fp, #-8]
    // 0xb225b0: r0 = AllocateClosure()
    //     0xb225b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb225b4: stur            x0, [fp, #-0x18]
    // 0xb225b8: r0 = PageView()
    //     0xb225b8: bl              #0x9d332c  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb225bc: stur            x0, [fp, #-0x20]
    // 0xb225c0: r16 = Instance_PageStorageKey
    //     0xb225c0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bef8] Obj!PageStorageKey<String>@e14be1
    //     0xb225c4: ldr             x16, [x16, #0xef8]
    // 0xb225c8: r30 = true
    //     0xb225c8: add             lr, NULL, #0x20  ; true
    // 0xb225cc: stp             lr, x16, [SP, #8]
    // 0xb225d0: r16 = 1208
    //     0xb225d0: movz            x16, #0x4b8
    // 0xb225d4: str             x16, [SP]
    // 0xb225d8: mov             x1, x0
    // 0xb225dc: ldur            x2, [fp, #-0x10]
    // 0xb225e0: ldur            x3, [fp, #-0x18]
    // 0xb225e4: ldur            x5, [fp, #-8]
    // 0xb225e8: r4 = const [0, 0x7, 0x3, 0x4, itemCount, 0x6, key, 0x4, reverse, 0x5, null]
    //     0xb225e8: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bf00] List(11) [0, 0x7, 0x3, 0x4, "itemCount", 0x6, "key", 0x4, "reverse", 0x5, Null]
    //     0xb225ec: ldr             x4, [x4, #0xf00]
    // 0xb225f0: r0 = PageView.builder()
    //     0xb225f0: bl              #0x9d3014  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb225f4: r1 = Null
    //     0xb225f4: mov             x1, NULL
    // 0xb225f8: r2 = 2
    //     0xb225f8: movz            x2, #0x2
    // 0xb225fc: r0 = AllocateArray()
    //     0xb225fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb22600: mov             x2, x0
    // 0xb22604: ldur            x0, [fp, #-0x20]
    // 0xb22608: stur            x2, [fp, #-8]
    // 0xb2260c: StoreField: r2->field_f = r0
    //     0xb2260c: stur            w0, [x2, #0xf]
    // 0xb22610: r1 = <Widget>
    //     0xb22610: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb22614: r0 = AllocateGrowableArray()
    //     0xb22614: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb22618: mov             x1, x0
    // 0xb2261c: ldur            x0, [fp, #-8]
    // 0xb22620: stur            x1, [fp, #-0x10]
    // 0xb22624: StoreField: r1->field_f = r0
    //     0xb22624: stur            w0, [x1, #0xf]
    // 0xb22628: r0 = 2
    //     0xb22628: movz            x0, #0x2
    // 0xb2262c: StoreField: r1->field_b = r0
    //     0xb2262c: stur            w0, [x1, #0xb]
    // 0xb22630: r0 = Stack()
    //     0xb22630: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb22634: mov             x1, x0
    // 0xb22638: r0 = Instance_AlignmentDirectional
    //     0xb22638: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xb2263c: ldr             x0, [x0, #0x7b0]
    // 0xb22640: stur            x1, [fp, #-0x18]
    // 0xb22644: StoreField: r1->field_f = r0
    //     0xb22644: stur            w0, [x1, #0xf]
    // 0xb22648: r0 = Instance_StackFit
    //     0xb22648: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xb2264c: ldr             x0, [x0, #0x7b8]
    // 0xb22650: ArrayStore: r1[0] = r0  ; List_4
    //     0xb22650: stur            w0, [x1, #0x17]
    // 0xb22654: r0 = Instance_Clip
    //     0xb22654: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb22658: ldr             x0, [x0, #0x7c0]
    // 0xb2265c: StoreField: r1->field_1b = r0
    //     0xb2265c: stur            w0, [x1, #0x1b]
    // 0xb22660: ldur            x0, [fp, #-0x10]
    // 0xb22664: StoreField: r1->field_b = r0
    //     0xb22664: stur            w0, [x1, #0xb]
    // 0xb22668: ldur            d0, [fp, #-0x28]
    // 0xb2266c: r0 = inline_Allocate_Double()
    //     0xb2266c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb22670: add             x0, x0, #0x10
    //     0xb22674: cmp             x2, x0
    //     0xb22678: b.ls            #0xb226d8
    //     0xb2267c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb22680: sub             x0, x0, #0xf
    //     0xb22684: movz            x2, #0xe15c
    //     0xb22688: movk            x2, #0x3, lsl #16
    //     0xb2268c: stur            x2, [x0, #-1]
    // 0xb22690: StoreField: r0->field_7 = d0
    //     0xb22690: stur            d0, [x0, #7]
    // 0xb22694: stur            x0, [fp, #-8]
    // 0xb22698: r0 = SizedBox()
    //     0xb22698: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb2269c: ldur            x1, [fp, #-8]
    // 0xb226a0: StoreField: r0->field_13 = r1
    //     0xb226a0: stur            w1, [x0, #0x13]
    // 0xb226a4: ldur            x1, [fp, #-0x18]
    // 0xb226a8: StoreField: r0->field_b = r1
    //     0xb226a8: stur            w1, [x0, #0xb]
    // 0xb226ac: b               #0xb226b8
    // 0xb226b0: r0 = Instance_Center
    //     0xb226b0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5c8] Obj!Center@e1e761
    //     0xb226b4: ldr             x0, [x0, #0x5c8]
    // 0xb226b8: LeaveFrame
    //     0xb226b8: mov             SP, fp
    //     0xb226bc: ldp             fp, lr, [SP], #0x10
    // 0xb226c0: ret
    //     0xb226c0: ret             
    // 0xb226c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb226c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb226c8: b               #0xb22434
    // 0xb226cc: r9 = pageController
    //     0xb226cc: add             x9, PP, #0x2b, lsl #12  ; [pp+0x2bf08] Field <QuranPageController.pageController>: late (offset: 0x70)
    //     0xb226d0: ldr             x9, [x9, #0xf08]
    // 0xb226d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb226d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb226d8: SaveReg d0
    //     0xb226d8: str             q0, [SP, #-0x10]!
    // 0xb226dc: SaveReg r1
    //     0xb226dc: str             x1, [SP, #-8]!
    // 0xb226e0: r0 = AllocateDouble()
    //     0xb226e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb226e4: RestoreReg r1
    //     0xb226e4: ldr             x1, [SP], #8
    // 0xb226e8: RestoreReg d0
    //     0xb226e8: ldr             q0, [SP], #0x10
    // 0xb226ec: b               #0xb22690
  }
  [closure] QuranPage <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb226f0, size: 0x124
    // 0xb226f0: EnterFrame
    //     0xb226f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb226f4: mov             fp, SP
    // 0xb226f8: AllocStack(0x28)
    //     0xb226f8: sub             SP, SP, #0x28
    // 0xb226fc: SetupParameters()
    //     0xb226fc: ldr             x0, [fp, #0x20]
    //     0xb22700: ldur            w1, [x0, #0x17]
    //     0xb22704: add             x1, x1, HEAP, lsl #32
    //     0xb22708: stur            x1, [fp, #-8]
    // 0xb2270c: CheckStackOverflow
    //     0xb2270c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb22710: cmp             SP, x16
    //     0xb22714: b.ls            #0xb2280c
    // 0xb22718: r1 = 1
    //     0xb22718: movz            x1, #0x1
    // 0xb2271c: r0 = AllocateContext()
    //     0xb2271c: bl              #0xec126c  ; AllocateContextStub
    // 0xb22720: mov             x2, x0
    // 0xb22724: ldur            x0, [fp, #-8]
    // 0xb22728: stur            x2, [fp, #-0x20]
    // 0xb2272c: StoreField: r2->field_b = r0
    //     0xb2272c: stur            w0, [x2, #0xb]
    // 0xb22730: ldr             x1, [fp, #0x10]
    // 0xb22734: StoreField: r2->field_f = r1
    //     0xb22734: stur            w1, [x2, #0xf]
    // 0xb22738: r3 = LoadInt32Instr(r1)
    //     0xb22738: sbfx            x3, x1, #1, #0x1f
    //     0xb2273c: tbz             w1, #0, #0xb22744
    //     0xb22740: ldur            x3, [x1, #7]
    // 0xb22744: stur            x3, [fp, #-0x18]
    // 0xb22748: add             x4, x3, #1
    // 0xb2274c: stur            x4, [fp, #-0x10]
    // 0xb22750: LoadField: r1 = r0->field_f
    //     0xb22750: ldur            w1, [x0, #0xf]
    // 0xb22754: DecompressPointer r1
    //     0xb22754: add             x1, x1, HEAP, lsl #32
    // 0xb22758: r0 = controller()
    //     0xb22758: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2275c: LoadField: r1 = r0->field_5b
    //     0xb2275c: ldur            w1, [x0, #0x5b]
    // 0xb22760: DecompressPointer r1
    //     0xb22760: add             x1, x1, HEAP, lsl #32
    // 0xb22764: r0 = value()
    //     0xb22764: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb22768: r1 = LoadInt32Instr(r0)
    //     0xb22768: sbfx            x1, x0, #1, #0x1f
    //     0xb2276c: tbz             w0, #0, #0xb22774
    //     0xb22770: ldur            x1, [x0, #7]
    // 0xb22774: ldur            x0, [fp, #-0x18]
    // 0xb22778: add             x2, x0, x1
    // 0xb2277c: r0 = BoxInt64Instr(r2)
    //     0xb2277c: sbfiz           x0, x2, #1, #0x1f
    //     0xb22780: cmp             x2, x0, asr #1
    //     0xb22784: b.eq            #0xb22790
    //     0xb22788: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2278c: stur            x2, [x0, #7]
    // 0xb22790: r1 = 60
    //     0xb22790: movz            x1, #0x3c
    // 0xb22794: branchIfSmi(r0, 0xb227a0)
    //     0xb22794: tbz             w0, #0, #0xb227a0
    // 0xb22798: r1 = LoadClassIdInstr(r0)
    //     0xb22798: ldur            x1, [x0, #-1]
    //     0xb2279c: ubfx            x1, x1, #0xc, #0x14
    // 0xb227a0: str             x0, [SP]
    // 0xb227a4: mov             x0, x1
    // 0xb227a8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb227a8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb227ac: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb227ac: movz            x17, #0x2b03
    //     0xb227b0: add             lr, x0, x17
    //     0xb227b4: ldr             lr, [x21, lr, lsl #3]
    //     0xb227b8: blr             lr
    // 0xb227bc: stur            x0, [fp, #-8]
    // 0xb227c0: r0 = Obx()
    //     0xb227c0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb227c4: ldur            x2, [fp, #-0x20]
    // 0xb227c8: r1 = Function '<anonymous closure>':.
    //     0xb227c8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c0b8] AnonymousClosure: (0xb21ae4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb227cc: ldr             x1, [x1, #0xb8]
    // 0xb227d0: stur            x0, [fp, #-0x20]
    // 0xb227d4: r0 = AllocateClosure()
    //     0xb227d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb227d8: mov             x1, x0
    // 0xb227dc: ldur            x0, [fp, #-0x20]
    // 0xb227e0: StoreField: r0->field_b = r1
    //     0xb227e0: stur            w1, [x0, #0xb]
    // 0xb227e4: r0 = QuranPage()
    //     0xb227e4: bl              #0xb21ad8  ; AllocateQuranPageStub -> QuranPage (size=0x20)
    // 0xb227e8: ldur            x1, [fp, #-0x10]
    // 0xb227ec: StoreField: r0->field_b = r1
    //     0xb227ec: stur            x1, [x0, #0xb]
    // 0xb227f0: ldur            x1, [fp, #-0x20]
    // 0xb227f4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb227f4: stur            w1, [x0, #0x17]
    // 0xb227f8: ldur            x1, [fp, #-8]
    // 0xb227fc: StoreField: r0->field_13 = r1
    //     0xb227fc: stur            w1, [x0, #0x13]
    // 0xb22800: LeaveFrame
    //     0xb22800: mov             SP, fp
    //     0xb22804: ldp             fp, lr, [SP], #0x10
    // 0xb22808: ret
    //     0xb22808: ret             
    // 0xb2280c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2280c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb22810: b               #0xb22718
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0xb22814, size: 0x418
    // 0xb22814: EnterFrame
    //     0xb22814: stp             fp, lr, [SP, #-0x10]!
    //     0xb22818: mov             fp, SP
    // 0xb2281c: AllocStack(0x40)
    //     0xb2281c: sub             SP, SP, #0x40
    // 0xb22820: SetupParameters()
    //     0xb22820: ldr             x0, [fp, #0x10]
    //     0xb22824: ldur            w3, [x0, #0x17]
    //     0xb22828: add             x3, x3, HEAP, lsl #32
    //     0xb2282c: stur            x3, [fp, #-8]
    // 0xb22830: CheckStackOverflow
    //     0xb22830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb22834: cmp             SP, x16
    //     0xb22838: b.ls            #0xb22c24
    // 0xb2283c: r1 = _ConstMap len:10
    //     0xb2283c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb22840: ldr             x1, [x1, #0xc08]
    // 0xb22844: r2 = 1200
    //     0xb22844: movz            x2, #0x4b0
    // 0xb22848: r0 = []()
    //     0xb22848: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb2284c: r1 = _ConstMap len:3
    //     0xb2284c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb22850: ldr             x1, [x1, #0xbe8]
    // 0xb22854: r2 = 4
    //     0xb22854: movz            x2, #0x4
    // 0xb22858: stur            x0, [fp, #-0x10]
    // 0xb2285c: r0 = []()
    //     0xb2285c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb22860: r16 = <Color?>
    //     0xb22860: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb22864: ldr             x16, [x16, #0x98]
    // 0xb22868: stp             x0, x16, [SP, #8]
    // 0xb2286c: ldur            x16, [fp, #-0x10]
    // 0xb22870: str             x16, [SP]
    // 0xb22874: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb22874: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb22878: r0 = mode()
    //     0xb22878: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb2287c: stur            x0, [fp, #-0x10]
    // 0xb22880: r0 = Radius()
    //     0xb22880: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb22884: d0 = 4.000000
    //     0xb22884: fmov            d0, #4.00000000
    // 0xb22888: stur            x0, [fp, #-0x18]
    // 0xb2288c: StoreField: r0->field_7 = d0
    //     0xb2288c: stur            d0, [x0, #7]
    // 0xb22890: StoreField: r0->field_f = d0
    //     0xb22890: stur            d0, [x0, #0xf]
    // 0xb22894: r0 = BorderRadius()
    //     0xb22894: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb22898: mov             x2, x0
    // 0xb2289c: ldur            x0, [fp, #-0x18]
    // 0xb228a0: stur            x2, [fp, #-0x20]
    // 0xb228a4: StoreField: r2->field_7 = r0
    //     0xb228a4: stur            w0, [x2, #7]
    // 0xb228a8: StoreField: r2->field_b = r0
    //     0xb228a8: stur            w0, [x2, #0xb]
    // 0xb228ac: StoreField: r2->field_f = r0
    //     0xb228ac: stur            w0, [x2, #0xf]
    // 0xb228b0: StoreField: r2->field_13 = r0
    //     0xb228b0: stur            w0, [x2, #0x13]
    // 0xb228b4: r1 = Instance_Color
    //     0xb228b4: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xb228b8: d0 = 0.500000
    //     0xb228b8: fmov            d0, #0.50000000
    // 0xb228bc: r0 = withOpacity()
    //     0xb228bc: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xb228c0: str             x0, [SP]
    // 0xb228c4: r1 = Null
    //     0xb228c4: mov             x1, NULL
    // 0xb228c8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb228c8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb228cc: ldr             x4, [x4, #0x228]
    // 0xb228d0: r0 = Border.all()
    //     0xb228d0: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb228d4: stur            x0, [fp, #-0x18]
    // 0xb228d8: r0 = BoxDecoration()
    //     0xb228d8: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb228dc: mov             x3, x0
    // 0xb228e0: ldur            x0, [fp, #-0x10]
    // 0xb228e4: stur            x3, [fp, #-0x28]
    // 0xb228e8: StoreField: r3->field_7 = r0
    //     0xb228e8: stur            w0, [x3, #7]
    // 0xb228ec: ldur            x0, [fp, #-0x18]
    // 0xb228f0: StoreField: r3->field_f = r0
    //     0xb228f0: stur            w0, [x3, #0xf]
    // 0xb228f4: ldur            x0, [fp, #-0x20]
    // 0xb228f8: StoreField: r3->field_13 = r0
    //     0xb228f8: stur            w0, [x3, #0x13]
    // 0xb228fc: r0 = Instance_BoxShape
    //     0xb228fc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb22900: ldr             x0, [x0, #0xca8]
    // 0xb22904: StoreField: r3->field_23 = r0
    //     0xb22904: stur            w0, [x3, #0x23]
    // 0xb22908: r1 = Null
    //     0xb22908: mov             x1, NULL
    // 0xb2290c: r2 = 8
    //     0xb2290c: movz            x2, #0x8
    // 0xb22910: r0 = AllocateArray()
    //     0xb22910: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb22914: stur            x0, [fp, #-0x10]
    // 0xb22918: r16 = "Halaman "
    //     0xb22918: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b8c8] "Halaman "
    //     0xb2291c: ldr             x16, [x16, #0x8c8]
    // 0xb22920: StoreField: r0->field_f = r16
    //     0xb22920: stur            w16, [x0, #0xf]
    // 0xb22924: ldur            x2, [fp, #-8]
    // 0xb22928: LoadField: r1 = r2->field_f
    //     0xb22928: ldur            w1, [x2, #0xf]
    // 0xb2292c: DecompressPointer r1
    //     0xb2292c: add             x1, x1, HEAP, lsl #32
    // 0xb22930: r0 = controller()
    //     0xb22930: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22934: LoadField: r1 = r0->field_3b
    //     0xb22934: ldur            w1, [x0, #0x3b]
    // 0xb22938: DecompressPointer r1
    //     0xb22938: add             x1, x1, HEAP, lsl #32
    // 0xb2293c: r0 = value()
    //     0xb2293c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb22940: ldur            x1, [fp, #-0x10]
    // 0xb22944: ArrayStore: r1[1] = r0  ; List_4
    //     0xb22944: add             x25, x1, #0x13
    //     0xb22948: str             w0, [x25]
    //     0xb2294c: tbz             w0, #0, #0xb22968
    //     0xb22950: ldurb           w16, [x1, #-1]
    //     0xb22954: ldurb           w17, [x0, #-1]
    //     0xb22958: and             x16, x17, x16, lsr #2
    //     0xb2295c: tst             x16, HEAP, lsr #32
    //     0xb22960: b.eq            #0xb22968
    //     0xb22964: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb22968: ldur            x0, [fp, #-0x10]
    // 0xb2296c: r16 = ", Juz "
    //     0xb2296c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0c0] ", Juz "
    //     0xb22970: ldr             x16, [x16, #0xc0]
    // 0xb22974: ArrayStore: r0[0] = r16  ; List_4
    //     0xb22974: stur            w16, [x0, #0x17]
    // 0xb22978: ldur            x2, [fp, #-8]
    // 0xb2297c: LoadField: r1 = r2->field_f
    //     0xb2297c: ldur            w1, [x2, #0xf]
    // 0xb22980: DecompressPointer r1
    //     0xb22980: add             x1, x1, HEAP, lsl #32
    // 0xb22984: r0 = controller()
    //     0xb22984: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22988: mov             x1, x0
    // 0xb2298c: r0 = juz()
    //     0xb2298c: bl              #0xb22c2c  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::juz
    // 0xb22990: mov             x2, x0
    // 0xb22994: r0 = BoxInt64Instr(r2)
    //     0xb22994: sbfiz           x0, x2, #1, #0x1f
    //     0xb22998: cmp             x2, x0, asr #1
    //     0xb2299c: b.eq            #0xb229a8
    //     0xb229a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb229a4: stur            x2, [x0, #7]
    // 0xb229a8: ldur            x1, [fp, #-0x10]
    // 0xb229ac: ArrayStore: r1[3] = r0  ; List_4
    //     0xb229ac: add             x25, x1, #0x1b
    //     0xb229b0: str             w0, [x25]
    //     0xb229b4: tbz             w0, #0, #0xb229d0
    //     0xb229b8: ldurb           w16, [x1, #-1]
    //     0xb229bc: ldurb           w17, [x0, #-1]
    //     0xb229c0: and             x16, x17, x16, lsr #2
    //     0xb229c4: tst             x16, HEAP, lsr #32
    //     0xb229c8: b.eq            #0xb229d0
    //     0xb229cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb229d0: ldur            x16, [fp, #-0x10]
    // 0xb229d4: str             x16, [SP]
    // 0xb229d8: r0 = _interpolate()
    //     0xb229d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb229dc: stur            x0, [fp, #-0x10]
    // 0xb229e0: r0 = Text()
    //     0xb229e0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb229e4: mov             x2, x0
    // 0xb229e8: ldur            x0, [fp, #-0x10]
    // 0xb229ec: stur            x2, [fp, #-0x18]
    // 0xb229f0: StoreField: r2->field_b = r0
    //     0xb229f0: stur            w0, [x2, #0xb]
    // 0xb229f4: r0 = Instance_TextStyle
    //     0xb229f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c0c8] Obj!TextStyle@e1b8f1
    //     0xb229f8: ldr             x0, [x0, #0xc8]
    // 0xb229fc: StoreField: r2->field_13 = r0
    //     0xb229fc: stur            w0, [x2, #0x13]
    // 0xb22a00: ldur            x0, [fp, #-8]
    // 0xb22a04: LoadField: r1 = r0->field_f
    //     0xb22a04: ldur            w1, [x0, #0xf]
    // 0xb22a08: DecompressPointer r1
    //     0xb22a08: add             x1, x1, HEAP, lsl #32
    // 0xb22a0c: r0 = controller()
    //     0xb22a0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22a10: LoadField: r1 = r0->field_3f
    //     0xb22a10: ldur            w1, [x0, #0x3f]
    // 0xb22a14: DecompressPointer r1
    //     0xb22a14: add             x1, x1, HEAP, lsl #32
    // 0xb22a18: r0 = value()
    //     0xb22a18: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb22a1c: stur            x0, [fp, #-0x10]
    // 0xb22a20: r0 = Text()
    //     0xb22a20: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb22a24: mov             x3, x0
    // 0xb22a28: ldur            x0, [fp, #-0x10]
    // 0xb22a2c: stur            x3, [fp, #-0x20]
    // 0xb22a30: StoreField: r3->field_b = r0
    //     0xb22a30: stur            w0, [x3, #0xb]
    // 0xb22a34: r0 = Instance_TextStyle
    //     0xb22a34: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c0d0] Obj!TextStyle@e1bb21
    //     0xb22a38: ldr             x0, [x0, #0xd0]
    // 0xb22a3c: StoreField: r3->field_13 = r0
    //     0xb22a3c: stur            w0, [x3, #0x13]
    // 0xb22a40: r1 = Null
    //     0xb22a40: mov             x1, NULL
    // 0xb22a44: r2 = 4
    //     0xb22a44: movz            x2, #0x4
    // 0xb22a48: r0 = AllocateArray()
    //     0xb22a48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb22a4c: mov             x2, x0
    // 0xb22a50: ldur            x0, [fp, #-0x18]
    // 0xb22a54: stur            x2, [fp, #-0x10]
    // 0xb22a58: StoreField: r2->field_f = r0
    //     0xb22a58: stur            w0, [x2, #0xf]
    // 0xb22a5c: ldur            x0, [fp, #-0x20]
    // 0xb22a60: StoreField: r2->field_13 = r0
    //     0xb22a60: stur            w0, [x2, #0x13]
    // 0xb22a64: r1 = <Widget>
    //     0xb22a64: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb22a68: r0 = AllocateGrowableArray()
    //     0xb22a68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb22a6c: mov             x1, x0
    // 0xb22a70: ldur            x0, [fp, #-0x10]
    // 0xb22a74: stur            x1, [fp, #-0x18]
    // 0xb22a78: StoreField: r1->field_f = r0
    //     0xb22a78: stur            w0, [x1, #0xf]
    // 0xb22a7c: r0 = 4
    //     0xb22a7c: movz            x0, #0x4
    // 0xb22a80: StoreField: r1->field_b = r0
    //     0xb22a80: stur            w0, [x1, #0xb]
    // 0xb22a84: r0 = Column()
    //     0xb22a84: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb22a88: mov             x3, x0
    // 0xb22a8c: r0 = Instance_Axis
    //     0xb22a8c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb22a90: stur            x3, [fp, #-0x10]
    // 0xb22a94: StoreField: r3->field_f = r0
    //     0xb22a94: stur            w0, [x3, #0xf]
    // 0xb22a98: r0 = Instance_MainAxisAlignment
    //     0xb22a98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb22a9c: ldr             x0, [x0, #0x730]
    // 0xb22aa0: StoreField: r3->field_13 = r0
    //     0xb22aa0: stur            w0, [x3, #0x13]
    // 0xb22aa4: r1 = Instance_MainAxisSize
    //     0xb22aa4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb22aa8: ldr             x1, [x1, #0x738]
    // 0xb22aac: ArrayStore: r3[0] = r1  ; List_4
    //     0xb22aac: stur            w1, [x3, #0x17]
    // 0xb22ab0: r1 = Instance_CrossAxisAlignment
    //     0xb22ab0: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb22ab4: ldr             x1, [x1, #0x68]
    // 0xb22ab8: StoreField: r3->field_1b = r1
    //     0xb22ab8: stur            w1, [x3, #0x1b]
    // 0xb22abc: r4 = Instance_VerticalDirection
    //     0xb22abc: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb22ac0: ldr             x4, [x4, #0x748]
    // 0xb22ac4: StoreField: r3->field_23 = r4
    //     0xb22ac4: stur            w4, [x3, #0x23]
    // 0xb22ac8: r5 = Instance_Clip
    //     0xb22ac8: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb22acc: ldr             x5, [x5, #0x750]
    // 0xb22ad0: StoreField: r3->field_2b = r5
    //     0xb22ad0: stur            w5, [x3, #0x2b]
    // 0xb22ad4: StoreField: r3->field_2f = rZR
    //     0xb22ad4: stur            xzr, [x3, #0x2f]
    // 0xb22ad8: ldur            x1, [fp, #-0x18]
    // 0xb22adc: StoreField: r3->field_b = r1
    //     0xb22adc: stur            w1, [x3, #0xb]
    // 0xb22ae0: r1 = Null
    //     0xb22ae0: mov             x1, NULL
    // 0xb22ae4: r2 = 6
    //     0xb22ae4: movz            x2, #0x6
    // 0xb22ae8: r0 = AllocateArray()
    //     0xb22ae8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb22aec: mov             x2, x0
    // 0xb22af0: ldur            x0, [fp, #-0x10]
    // 0xb22af4: stur            x2, [fp, #-0x18]
    // 0xb22af8: StoreField: r2->field_f = r0
    //     0xb22af8: stur            w0, [x2, #0xf]
    // 0xb22afc: r16 = Instance_SizedBox
    //     0xb22afc: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb22b00: ldr             x16, [x16, #0x538]
    // 0xb22b04: StoreField: r2->field_13 = r16
    //     0xb22b04: stur            w16, [x2, #0x13]
    // 0xb22b08: r16 = Instance_Icon
    //     0xb22b08: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0d8] Obj!Icon@e24831
    //     0xb22b0c: ldr             x16, [x16, #0xd8]
    // 0xb22b10: ArrayStore: r2[0] = r16  ; List_4
    //     0xb22b10: stur            w16, [x2, #0x17]
    // 0xb22b14: r1 = <Widget>
    //     0xb22b14: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb22b18: r0 = AllocateGrowableArray()
    //     0xb22b18: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb22b1c: mov             x1, x0
    // 0xb22b20: ldur            x0, [fp, #-0x18]
    // 0xb22b24: stur            x1, [fp, #-0x10]
    // 0xb22b28: StoreField: r1->field_f = r0
    //     0xb22b28: stur            w0, [x1, #0xf]
    // 0xb22b2c: r0 = 6
    //     0xb22b2c: movz            x0, #0x6
    // 0xb22b30: StoreField: r1->field_b = r0
    //     0xb22b30: stur            w0, [x1, #0xb]
    // 0xb22b34: r0 = Row()
    //     0xb22b34: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22b38: mov             x1, x0
    // 0xb22b3c: r0 = Instance_Axis
    //     0xb22b3c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb22b40: stur            x1, [fp, #-0x18]
    // 0xb22b44: StoreField: r1->field_f = r0
    //     0xb22b44: stur            w0, [x1, #0xf]
    // 0xb22b48: r0 = Instance_MainAxisAlignment
    //     0xb22b48: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb22b4c: ldr             x0, [x0, #0x730]
    // 0xb22b50: StoreField: r1->field_13 = r0
    //     0xb22b50: stur            w0, [x1, #0x13]
    // 0xb22b54: r0 = Instance_MainAxisSize
    //     0xb22b54: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb22b58: ldr             x0, [x0, #0xe88]
    // 0xb22b5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb22b5c: stur            w0, [x1, #0x17]
    // 0xb22b60: r0 = Instance_CrossAxisAlignment
    //     0xb22b60: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb22b64: ldr             x0, [x0, #0x740]
    // 0xb22b68: StoreField: r1->field_1b = r0
    //     0xb22b68: stur            w0, [x1, #0x1b]
    // 0xb22b6c: r0 = Instance_VerticalDirection
    //     0xb22b6c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb22b70: ldr             x0, [x0, #0x748]
    // 0xb22b74: StoreField: r1->field_23 = r0
    //     0xb22b74: stur            w0, [x1, #0x23]
    // 0xb22b78: r0 = Instance_Clip
    //     0xb22b78: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb22b7c: ldr             x0, [x0, #0x750]
    // 0xb22b80: StoreField: r1->field_2b = r0
    //     0xb22b80: stur            w0, [x1, #0x2b]
    // 0xb22b84: StoreField: r1->field_2f = rZR
    //     0xb22b84: stur            xzr, [x1, #0x2f]
    // 0xb22b88: ldur            x0, [fp, #-0x10]
    // 0xb22b8c: StoreField: r1->field_b = r0
    //     0xb22b8c: stur            w0, [x1, #0xb]
    // 0xb22b90: r0 = Container()
    //     0xb22b90: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb22b94: stur            x0, [fp, #-0x10]
    // 0xb22b98: r16 = Instance_EdgeInsets
    //     0xb22b98: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0e0] Obj!EdgeInsets@e12c11
    //     0xb22b9c: ldr             x16, [x16, #0xe0]
    // 0xb22ba0: ldur            lr, [fp, #-0x28]
    // 0xb22ba4: stp             lr, x16, [SP, #8]
    // 0xb22ba8: ldur            x16, [fp, #-0x18]
    // 0xb22bac: str             x16, [SP]
    // 0xb22bb0: mov             x1, x0
    // 0xb22bb4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb22bb4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb22bb8: ldr             x4, [x4, #0xe8]
    // 0xb22bbc: r0 = Container()
    //     0xb22bbc: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb22bc0: r0 = InkWell()
    //     0xb22bc0: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb22bc4: mov             x3, x0
    // 0xb22bc8: ldur            x0, [fp, #-0x10]
    // 0xb22bcc: stur            x3, [fp, #-0x18]
    // 0xb22bd0: StoreField: r3->field_b = r0
    //     0xb22bd0: stur            w0, [x3, #0xb]
    // 0xb22bd4: ldur            x2, [fp, #-8]
    // 0xb22bd8: r1 = Function '<anonymous closure>':.
    //     0xb22bd8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c0f0] AnonymousClosure: (0xb22cb4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb22bdc: ldr             x1, [x1, #0xf0]
    // 0xb22be0: r0 = AllocateClosure()
    //     0xb22be0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb22be4: mov             x1, x0
    // 0xb22be8: ldur            x0, [fp, #-0x18]
    // 0xb22bec: StoreField: r0->field_f = r1
    //     0xb22bec: stur            w1, [x0, #0xf]
    // 0xb22bf0: r1 = true
    //     0xb22bf0: add             x1, NULL, #0x20  ; true
    // 0xb22bf4: StoreField: r0->field_43 = r1
    //     0xb22bf4: stur            w1, [x0, #0x43]
    // 0xb22bf8: r2 = Instance_BoxShape
    //     0xb22bf8: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb22bfc: ldr             x2, [x2, #0xca8]
    // 0xb22c00: StoreField: r0->field_47 = r2
    //     0xb22c00: stur            w2, [x0, #0x47]
    // 0xb22c04: StoreField: r0->field_6f = r1
    //     0xb22c04: stur            w1, [x0, #0x6f]
    // 0xb22c08: r2 = false
    //     0xb22c08: add             x2, NULL, #0x30  ; false
    // 0xb22c0c: StoreField: r0->field_73 = r2
    //     0xb22c0c: stur            w2, [x0, #0x73]
    // 0xb22c10: StoreField: r0->field_83 = r1
    //     0xb22c10: stur            w1, [x0, #0x83]
    // 0xb22c14: StoreField: r0->field_7b = r2
    //     0xb22c14: stur            w2, [x0, #0x7b]
    // 0xb22c18: LeaveFrame
    //     0xb22c18: mov             SP, fp
    //     0xb22c1c: ldp             fp, lr, [SP], #0x10
    // 0xb22c20: ret
    //     0xb22c20: ret             
    // 0xb22c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb22c24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb22c28: b               #0xb2283c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb22cb4, size: 0x1f0
    // 0xb22cb4: EnterFrame
    //     0xb22cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xb22cb8: mov             fp, SP
    // 0xb22cbc: AllocStack(0x38)
    //     0xb22cbc: sub             SP, SP, #0x38
    // 0xb22cc0: SetupParameters(QuranPageView this /* r1 */)
    //     0xb22cc0: stur            NULL, [fp, #-8]
    //     0xb22cc4: movz            x0, #0
    //     0xb22cc8: add             x1, fp, w0, sxtw #2
    //     0xb22ccc: ldr             x1, [x1, #0x10]
    //     0xb22cd0: ldur            w2, [x1, #0x17]
    //     0xb22cd4: add             x2, x2, HEAP, lsl #32
    //     0xb22cd8: stur            x2, [fp, #-0x10]
    // 0xb22cdc: CheckStackOverflow
    //     0xb22cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb22ce0: cmp             SP, x16
    //     0xb22ce4: b.ls            #0xb22e90
    // 0xb22ce8: InitAsync() -> Future<void?>
    //     0xb22ce8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb22cec: bl              #0x661298  ; InitAsyncStub
    // 0xb22cf0: ldur            x2, [fp, #-0x10]
    // 0xb22cf4: LoadField: r1 = r2->field_f
    //     0xb22cf4: ldur            w1, [x2, #0xf]
    // 0xb22cf8: DecompressPointer r1
    //     0xb22cf8: add             x1, x1, HEAP, lsl #32
    // 0xb22cfc: r0 = controller()
    //     0xb22cfc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22d00: mov             x1, x0
    // 0xb22d04: r0 = resetSelecteSurah()
    //     0xb22d04: bl              #0x8c261c  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::resetSelecteSurah
    // 0xb22d08: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb22d08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb22d0c: ldr             x0, [x0, #0x2670]
    //     0xb22d10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb22d14: cmp             w0, w16
    //     0xb22d18: b.ne            #0xb22d24
    //     0xb22d1c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb22d20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb22d24: ldur            x2, [fp, #-0x10]
    // 0xb22d28: LoadField: r1 = r2->field_f
    //     0xb22d28: ldur            w1, [x2, #0xf]
    // 0xb22d2c: DecompressPointer r1
    //     0xb22d2c: add             x1, x1, HEAP, lsl #32
    // 0xb22d30: r0 = controller()
    //     0xb22d30: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22d34: LoadField: r1 = r0->field_37
    //     0xb22d34: ldur            w1, [x0, #0x37]
    // 0xb22d38: DecompressPointer r1
    //     0xb22d38: add             x1, x1, HEAP, lsl #32
    // 0xb22d3c: r16 = Sentinel
    //     0xb22d3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb22d40: cmp             w1, w16
    // 0xb22d44: b.eq            #0xb22e98
    // 0xb22d48: stur            x1, [fp, #-0x18]
    // 0xb22d4c: r0 = TabBar()
    //     0xb22d4c: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xb22d50: mov             x1, x0
    // 0xb22d54: r0 = const [Instance of 'Tab', Instance of 'Tab', Instance of 'Tab']
    //     0xb22d54: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c0f8] List<Widget>(3)
    //     0xb22d58: ldr             x0, [x0, #0xf8]
    // 0xb22d5c: stur            x1, [fp, #-0x20]
    // 0xb22d60: StoreField: r1->field_b = r0
    //     0xb22d60: stur            w0, [x1, #0xb]
    // 0xb22d64: ldur            x0, [fp, #-0x18]
    // 0xb22d68: StoreField: r1->field_f = r0
    //     0xb22d68: stur            w0, [x1, #0xf]
    // 0xb22d6c: r0 = false
    //     0xb22d6c: add             x0, NULL, #0x30  ; false
    // 0xb22d70: StoreField: r1->field_13 = r0
    //     0xb22d70: stur            w0, [x1, #0x13]
    // 0xb22d74: r0 = true
    //     0xb22d74: add             x0, NULL, #0x20  ; true
    // 0xb22d78: StoreField: r1->field_2f = r0
    //     0xb22d78: stur            w0, [x1, #0x2f]
    // 0xb22d7c: d0 = 2.000000
    //     0xb22d7c: fmov            d0, #2.00000000
    // 0xb22d80: StoreField: r1->field_1f = d0
    //     0xb22d80: stur            d0, [x1, #0x1f]
    // 0xb22d84: r2 = Instance_EdgeInsets
    //     0xb22d84: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb22d88: StoreField: r1->field_27 = r2
    //     0xb22d88: stur            w2, [x1, #0x27]
    // 0xb22d8c: r2 = Instance_DragStartBehavior
    //     0xb22d8c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb22d90: StoreField: r1->field_57 = r2
    //     0xb22d90: stur            w2, [x1, #0x57]
    // 0xb22d94: StoreField: r1->field_7f = r0
    //     0xb22d94: stur            w0, [x1, #0x7f]
    // 0xb22d98: r0 = Obx()
    //     0xb22d98: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb22d9c: ldur            x2, [fp, #-0x10]
    // 0xb22da0: r1 = Function '<anonymous closure>':.
    //     0xb22da0: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c100] AnonymousClosure: (0xb236e0), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb22da4: ldr             x1, [x1, #0x100]
    // 0xb22da8: stur            x0, [fp, #-0x18]
    // 0xb22dac: r0 = AllocateClosure()
    //     0xb22dac: bl              #0xec1630  ; AllocateClosureStub
    // 0xb22db0: mov             x1, x0
    // 0xb22db4: ldur            x0, [fp, #-0x18]
    // 0xb22db8: StoreField: r0->field_b = r1
    //     0xb22db8: stur            w1, [x0, #0xb]
    // 0xb22dbc: r1 = Null
    //     0xb22dbc: mov             x1, NULL
    // 0xb22dc0: r2 = 4
    //     0xb22dc0: movz            x2, #0x4
    // 0xb22dc4: r0 = AllocateArray()
    //     0xb22dc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb22dc8: mov             x2, x0
    // 0xb22dcc: ldur            x0, [fp, #-0x20]
    // 0xb22dd0: stur            x2, [fp, #-0x28]
    // 0xb22dd4: StoreField: r2->field_f = r0
    //     0xb22dd4: stur            w0, [x2, #0xf]
    // 0xb22dd8: ldur            x0, [fp, #-0x18]
    // 0xb22ddc: StoreField: r2->field_13 = r0
    //     0xb22ddc: stur            w0, [x2, #0x13]
    // 0xb22de0: r1 = <Widget>
    //     0xb22de0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb22de4: r0 = AllocateGrowableArray()
    //     0xb22de4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb22de8: mov             x2, x0
    // 0xb22dec: ldur            x0, [fp, #-0x28]
    // 0xb22df0: stur            x2, [fp, #-0x18]
    // 0xb22df4: StoreField: r2->field_f = r0
    //     0xb22df4: stur            w0, [x2, #0xf]
    // 0xb22df8: r0 = 4
    //     0xb22df8: movz            x0, #0x4
    // 0xb22dfc: StoreField: r2->field_b = r0
    //     0xb22dfc: stur            w0, [x2, #0xb]
    // 0xb22e00: ldur            x0, [fp, #-0x10]
    // 0xb22e04: LoadField: r1 = r0->field_f
    //     0xb22e04: ldur            w1, [x0, #0xf]
    // 0xb22e08: DecompressPointer r1
    //     0xb22e08: add             x1, x1, HEAP, lsl #32
    // 0xb22e0c: r0 = controller()
    //     0xb22e0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22e10: stur            x0, [fp, #-0x20]
    // 0xb22e14: r0 = NDialog()
    //     0xb22e14: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xb22e18: mov             x3, x0
    // 0xb22e1c: r0 = "Pergi ke"
    //     0xb22e1c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b730] "Pergi ke"
    //     0xb22e20: ldr             x0, [x0, #0x730]
    // 0xb22e24: stur            x3, [fp, #-0x28]
    // 0xb22e28: StoreField: r3->field_b = r0
    //     0xb22e28: stur            w0, [x3, #0xb]
    // 0xb22e2c: ldur            x2, [fp, #-0x20]
    // 0xb22e30: r1 = Function 'jumpTo':.
    //     0xb22e30: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c108] AnonymousClosure: (0xb22f90), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::jumpTo (0xb22fc8)
    //     0xb22e34: ldr             x1, [x1, #0x108]
    // 0xb22e38: r0 = AllocateClosure()
    //     0xb22e38: bl              #0xec1630  ; AllocateClosureStub
    // 0xb22e3c: mov             x1, x0
    // 0xb22e40: ldur            x0, [fp, #-0x28]
    // 0xb22e44: ArrayStore: r0[0] = r1  ; List_4
    //     0xb22e44: stur            w1, [x0, #0x17]
    // 0xb22e48: ldur            x1, [fp, #-0x18]
    // 0xb22e4c: StoreField: r0->field_f = r1
    //     0xb22e4c: stur            w1, [x0, #0xf]
    // 0xb22e50: ldur            x2, [fp, #-0x10]
    // 0xb22e54: r1 = Function '<anonymous closure>':.
    //     0xb22e54: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c110] AnonymousClosure: (0xb22ea4), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb22e58: ldr             x1, [x1, #0x110]
    // 0xb22e5c: r0 = AllocateClosure()
    //     0xb22e5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb22e60: mov             x1, x0
    // 0xb22e64: ldur            x0, [fp, #-0x28]
    // 0xb22e68: StoreField: r0->field_1b = r1
    //     0xb22e68: stur            w1, [x0, #0x1b]
    // 0xb22e6c: r16 = <int>
    //     0xb22e6c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb22e70: stp             x0, x16, [SP]
    // 0xb22e74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb22e74: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb22e78: r0 = ExtensionDialog.dialog()
    //     0xb22e78: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xb22e7c: mov             x1, x0
    // 0xb22e80: stur            x1, [fp, #-0x18]
    // 0xb22e84: r0 = Await()
    //     0xb22e84: bl              #0x661044  ; AwaitStub
    // 0xb22e88: r0 = Null
    //     0xb22e88: mov             x0, NULL
    // 0xb22e8c: r0 = ReturnAsyncNotFuture()
    //     0xb22e8c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb22e90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb22e90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb22e94: b               #0xb22ce8
    // 0xb22e98: r9 = tabController
    //     0xb22e98: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2c118] Field <QuranPageController.tabController>: late (offset: 0x38)
    //     0xb22e9c: ldr             x9, [x9, #0x118]
    // 0xb22ea0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb22ea0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xb22ea4, size: 0x7c
    // 0xb22ea4: EnterFrame
    //     0xb22ea4: stp             fp, lr, [SP, #-0x10]!
    //     0xb22ea8: mov             fp, SP
    // 0xb22eac: AllocStack(0x8)
    //     0xb22eac: sub             SP, SP, #8
    // 0xb22eb0: SetupParameters()
    //     0xb22eb0: ldr             x0, [fp, #0x10]
    //     0xb22eb4: ldur            w1, [x0, #0x17]
    //     0xb22eb8: add             x1, x1, HEAP, lsl #32
    // 0xb22ebc: CheckStackOverflow
    //     0xb22ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb22ec0: cmp             SP, x16
    //     0xb22ec4: b.ls            #0xb22f18
    // 0xb22ec8: LoadField: r0 = r1->field_f
    //     0xb22ec8: ldur            w0, [x1, #0xf]
    // 0xb22ecc: DecompressPointer r0
    //     0xb22ecc: add             x0, x0, HEAP, lsl #32
    // 0xb22ed0: mov             x1, x0
    // 0xb22ed4: r0 = controller()
    //     0xb22ed4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb22ed8: mov             x1, x0
    // 0xb22edc: r0 = cleanError()
    //     0xb22edc: bl              #0xb22f20  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::cleanError
    // 0xb22ee0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb22ee0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb22ee4: ldr             x0, [x0, #0x2670]
    //     0xb22ee8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb22eec: cmp             w0, w16
    //     0xb22ef0: b.ne            #0xb22efc
    //     0xb22ef4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb22ef8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb22efc: str             NULL, [SP]
    // 0xb22f00: r4 = const [0x1, 0, 0, 0, null]
    //     0xb22f00: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xb22f04: r0 = GetNavigation.back()
    //     0xb22f04: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb22f08: r0 = Null
    //     0xb22f08: mov             x0, NULL
    // 0xb22f0c: LeaveFrame
    //     0xb22f0c: mov             SP, fp
    //     0xb22f10: ldp             fp, lr, [SP], #0x10
    // 0xb22f14: ret
    //     0xb22f14: ret             
    // 0xb22f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb22f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb22f1c: b               #0xb22ec8
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb236e0, size: 0x1664
    // 0xb236e0: EnterFrame
    //     0xb236e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb236e4: mov             fp, SP
    // 0xb236e8: AllocStack(0x68)
    //     0xb236e8: sub             SP, SP, #0x68
    // 0xb236ec: SetupParameters()
    //     0xb236ec: ldr             x0, [fp, #0x10]
    //     0xb236f0: ldur            w2, [x0, #0x17]
    //     0xb236f4: add             x2, x2, HEAP, lsl #32
    //     0xb236f8: stur            x2, [fp, #-8]
    // 0xb236fc: CheckStackOverflow
    //     0xb236fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23700: cmp             SP, x16
    //     0xb23704: b.ls            #0xb24d3c
    // 0xb23708: LoadField: r1 = r2->field_f
    //     0xb23708: ldur            w1, [x2, #0xf]
    // 0xb2370c: DecompressPointer r1
    //     0xb2370c: add             x1, x1, HEAP, lsl #32
    // 0xb23710: r0 = controller()
    //     0xb23710: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23714: LoadField: r1 = r0->field_43
    //     0xb23714: ldur            w1, [x0, #0x43]
    // 0xb23718: DecompressPointer r1
    //     0xb23718: add             x1, x1, HEAP, lsl #32
    // 0xb2371c: r0 = value()
    //     0xb2371c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb23720: r1 = LoadInt32Instr(r0)
    //     0xb23720: sbfx            x1, x0, #1, #0x1f
    //     0xb23724: tbz             w0, #0, #0xb2372c
    //     0xb23728: ldur            x1, [x0, #7]
    // 0xb2372c: cmp             x1, #0
    // 0xb23730: b.gt            #0xb2402c
    // 0xb23734: cbnz            w0, #0xb23ee8
    // 0xb23738: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb23738: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2373c: ldr             x0, [x0, #0x2670]
    //     0xb23740: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb23744: cmp             w0, w16
    //     0xb23748: b.ne            #0xb23754
    //     0xb2374c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb23750: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb23754: r0 = GetNavigation.textTheme()
    //     0xb23754: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb23758: LoadField: r1 = r0->field_27
    //     0xb23758: ldur            w1, [x0, #0x27]
    // 0xb2375c: DecompressPointer r1
    //     0xb2375c: add             x1, x1, HEAP, lsl #32
    // 0xb23760: cmp             w1, NULL
    // 0xb23764: b.ne            #0xb23770
    // 0xb23768: r0 = Null
    //     0xb23768: mov             x0, NULL
    // 0xb2376c: b               #0xb23788
    // 0xb23770: r16 = 14.000000
    //     0xb23770: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb23774: ldr             x16, [x16, #0x9a0]
    // 0xb23778: str             x16, [SP]
    // 0xb2377c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb2377c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb23780: ldr             x4, [x4, #0x88]
    // 0xb23784: r0 = copyWith()
    //     0xb23784: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb23788: ldur            x2, [fp, #-8]
    // 0xb2378c: stur            x0, [fp, #-0x10]
    // 0xb23790: r0 = Text()
    //     0xb23790: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb23794: mov             x2, x0
    // 0xb23798: r0 = "Pilih Surah"
    //     0xb23798: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b768] "Pilih Surah"
    //     0xb2379c: ldr             x0, [x0, #0x768]
    // 0xb237a0: stur            x2, [fp, #-0x18]
    // 0xb237a4: StoreField: r2->field_b = r0
    //     0xb237a4: stur            w0, [x2, #0xb]
    // 0xb237a8: ldur            x0, [fp, #-0x10]
    // 0xb237ac: StoreField: r2->field_13 = r0
    //     0xb237ac: stur            w0, [x2, #0x13]
    // 0xb237b0: ldur            x0, [fp, #-8]
    // 0xb237b4: LoadField: r1 = r0->field_f
    //     0xb237b4: ldur            w1, [x0, #0xf]
    // 0xb237b8: DecompressPointer r1
    //     0xb237b8: add             x1, x1, HEAP, lsl #32
    // 0xb237bc: r0 = controller()
    //     0xb237bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb237c0: LoadField: r1 = r0->field_7b
    //     0xb237c0: ldur            w1, [x0, #0x7b]
    // 0xb237c4: DecompressPointer r1
    //     0xb237c4: add             x1, x1, HEAP, lsl #32
    // 0xb237c8: stur            x1, [fp, #-0x10]
    // 0xb237cc: r0 = TextField()
    //     0xb237cc: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb237d0: mov             x3, x0
    // 0xb237d4: r0 = EditableText
    //     0xb237d4: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb237d8: stur            x3, [fp, #-0x20]
    // 0xb237dc: StoreField: r3->field_f = r0
    //     0xb237dc: stur            w0, [x3, #0xf]
    // 0xb237e0: ldur            x1, [fp, #-0x10]
    // 0xb237e4: StoreField: r3->field_13 = r1
    //     0xb237e4: stur            w1, [x3, #0x13]
    // 0xb237e8: r1 = Instance_InputDecoration
    //     0xb237e8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c0] Obj!InputDecoration@e14321
    //     0xb237ec: ldr             x1, [x1, #0x4c0]
    // 0xb237f0: StoreField: r3->field_1b = r1
    //     0xb237f0: stur            w1, [x3, #0x1b]
    // 0xb237f4: r4 = Instance_TextCapitalization
    //     0xb237f4: ldr             x4, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb237f8: StoreField: r3->field_27 = r4
    //     0xb237f8: stur            w4, [x3, #0x27]
    // 0xb237fc: r5 = Instance_TextAlign
    //     0xb237fc: ldr             x5, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb23800: StoreField: r3->field_33 = r5
    //     0xb23800: stur            w5, [x3, #0x33]
    // 0xb23804: r6 = true
    //     0xb23804: add             x6, NULL, #0x20  ; true
    // 0xb23808: StoreField: r3->field_6f = r6
    //     0xb23808: stur            w6, [x3, #0x6f]
    // 0xb2380c: r7 = false
    //     0xb2380c: add             x7, NULL, #0x30  ; false
    // 0xb23810: StoreField: r3->field_3f = r7
    //     0xb23810: stur            w7, [x3, #0x3f]
    // 0xb23814: r8 = "•"
    //     0xb23814: add             x8, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb23818: ldr             x8, [x8, #0x4c8]
    // 0xb2381c: StoreField: r3->field_47 = r8
    //     0xb2381c: stur            w8, [x3, #0x47]
    // 0xb23820: StoreField: r3->field_4b = r7
    //     0xb23820: stur            w7, [x3, #0x4b]
    // 0xb23824: StoreField: r3->field_4f = r6
    //     0xb23824: stur            w6, [x3, #0x4f]
    // 0xb23828: StoreField: r3->field_5b = r6
    //     0xb23828: stur            w6, [x3, #0x5b]
    // 0xb2382c: r9 = 1
    //     0xb2382c: movz            x9, #0x1
    // 0xb23830: StoreField: r3->field_5f = r9
    //     0xb23830: stur            x9, [x3, #0x5f]
    // 0xb23834: StoreField: r3->field_6b = r7
    //     0xb23834: stur            w7, [x3, #0x6b]
    // 0xb23838: d0 = 2.000000
    //     0xb23838: fmov            d0, #2.00000000
    // 0xb2383c: StoreField: r3->field_9f = d0
    //     0xb2383c: stur            d0, [x3, #0x9f]
    // 0xb23840: r10 = Instance_BoxHeightStyle
    //     0xb23840: ldr             x10, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb23844: StoreField: r3->field_bb = r10
    //     0xb23844: stur            w10, [x3, #0xbb]
    // 0xb23848: r11 = Instance_BoxWidthStyle
    //     0xb23848: ldr             x11, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb2384c: StoreField: r3->field_bf = r11
    //     0xb2384c: stur            w11, [x3, #0xbf]
    // 0xb23850: r12 = Instance_EdgeInsets
    //     0xb23850: ldr             x12, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb23854: StoreField: r3->field_c7 = r12
    //     0xb23854: stur            w12, [x3, #0xc7]
    // 0xb23858: r13 = Instance_DragStartBehavior
    //     0xb23858: ldr             x13, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb2385c: StoreField: r3->field_d3 = r13
    //     0xb2385c: stur            w13, [x3, #0xd3]
    // 0xb23860: ldur            x2, [fp, #-8]
    // 0xb23864: r1 = Function '<anonymous closure>':.
    //     0xb23864: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c160] AnonymousClosure: (0xb24d44), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb23868: ldr             x1, [x1, #0x160]
    // 0xb2386c: r0 = AllocateClosure()
    //     0xb2386c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb23870: mov             x1, x0
    // 0xb23874: ldur            x0, [fp, #-0x20]
    // 0xb23878: StoreField: r0->field_d7 = r1
    //     0xb23878: stur            w1, [x0, #0xd7]
    // 0xb2387c: r1 = false
    //     0xb2387c: add             x1, NULL, #0x30  ; false
    // 0xb23880: StoreField: r0->field_db = r1
    //     0xb23880: stur            w1, [x0, #0xdb]
    // 0xb23884: r2 = const []
    //     0xb23884: ldr             x2, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb23888: StoreField: r0->field_f3 = r2
    //     0xb23888: stur            w2, [x0, #0xf3]
    // 0xb2388c: r3 = Instance_Clip
    //     0xb2388c: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb23890: ldr             x3, [x3, #0x7c0]
    // 0xb23894: StoreField: r0->field_f7 = r3
    //     0xb23894: stur            w3, [x0, #0xf7]
    // 0xb23898: r4 = true
    //     0xb23898: add             x4, NULL, #0x20  ; true
    // 0xb2389c: StoreField: r0->field_ff = r4
    //     0xb2389c: stur            w4, [x0, #0xff]
    // 0xb238a0: r17 = 259
    //     0xb238a0: movz            x17, #0x103
    // 0xb238a4: str             w4, [x0, x17]
    // 0xb238a8: r5 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb238a8: add             x5, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb238ac: ldr             x5, [x5, #0x4d8]
    // 0xb238b0: r17 = 267
    //     0xb238b0: movz            x17, #0x10b
    // 0xb238b4: str             w5, [x0, x17]
    // 0xb238b8: r17 = 271
    //     0xb238b8: movz            x17, #0x10f
    // 0xb238bc: str             w4, [x0, x17]
    // 0xb238c0: r6 = Instance_SmartDashesType
    //     0xb238c0: ldr             x6, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb238c4: StoreField: r0->field_53 = r6
    //     0xb238c4: stur            w6, [x0, #0x53]
    // 0xb238c8: r7 = Instance_SmartQuotesType
    //     0xb238c8: add             x7, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb238cc: ldr             x7, [x7, #0x4e0]
    // 0xb238d0: StoreField: r0->field_57 = r7
    //     0xb238d0: stur            w7, [x0, #0x57]
    // 0xb238d4: r8 = Instance_TextInputType
    //     0xb238d4: add             x8, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xb238d8: ldr             x8, [x8, #0x4e8]
    // 0xb238dc: StoreField: r0->field_1f = r8
    //     0xb238dc: stur            w8, [x0, #0x1f]
    // 0xb238e0: StoreField: r0->field_cb = r4
    //     0xb238e0: stur            w4, [x0, #0xcb]
    // 0xb238e4: r0 = UniqueKey()
    //     0xb238e4: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xb238e8: mov             x1, x0
    // 0xb238ec: ldur            x0, [fp, #-0x20]
    // 0xb238f0: StoreField: r0->field_7 = r1
    //     0xb238f0: stur            w1, [x0, #7]
    // 0xb238f4: r1 = Null
    //     0xb238f4: mov             x1, NULL
    // 0xb238f8: r2 = 4
    //     0xb238f8: movz            x2, #0x4
    // 0xb238fc: r0 = AllocateArray()
    //     0xb238fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb23900: stur            x0, [fp, #-0x10]
    // 0xb23904: r16 = "Masukkan nomor ayat antara 1 - "
    //     0xb23904: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b778] "Masukkan nomor ayat antara 1 - "
    //     0xb23908: ldr             x16, [x16, #0x778]
    // 0xb2390c: StoreField: r0->field_f = r16
    //     0xb2390c: stur            w16, [x0, #0xf]
    // 0xb23910: ldur            x2, [fp, #-8]
    // 0xb23914: LoadField: r1 = r2->field_f
    //     0xb23914: ldur            w1, [x2, #0xf]
    // 0xb23918: DecompressPointer r1
    //     0xb23918: add             x1, x1, HEAP, lsl #32
    // 0xb2391c: r0 = controller()
    //     0xb2391c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23920: LoadField: r1 = r0->field_47
    //     0xb23920: ldur            w1, [x0, #0x47]
    // 0xb23924: DecompressPointer r1
    //     0xb23924: add             x1, x1, HEAP, lsl #32
    // 0xb23928: r0 = value()
    //     0xb23928: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb2392c: ldur            x1, [fp, #-0x10]
    // 0xb23930: ArrayStore: r1[1] = r0  ; List_4
    //     0xb23930: add             x25, x1, #0x13
    //     0xb23934: str             w0, [x25]
    //     0xb23938: tbz             w0, #0, #0xb23954
    //     0xb2393c: ldurb           w16, [x1, #-1]
    //     0xb23940: ldurb           w17, [x0, #-1]
    //     0xb23944: and             x16, x17, x16, lsr #2
    //     0xb23948: tst             x16, HEAP, lsr #32
    //     0xb2394c: b.eq            #0xb23954
    //     0xb23950: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb23954: ldur            x16, [fp, #-0x10]
    // 0xb23958: str             x16, [SP]
    // 0xb2395c: r0 = _interpolate()
    //     0xb2395c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb23960: stur            x0, [fp, #-0x10]
    // 0xb23964: r0 = GetNavigation.textTheme()
    //     0xb23964: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb23968: LoadField: r1 = r0->field_27
    //     0xb23968: ldur            w1, [x0, #0x27]
    // 0xb2396c: DecompressPointer r1
    //     0xb2396c: add             x1, x1, HEAP, lsl #32
    // 0xb23970: cmp             w1, NULL
    // 0xb23974: b.ne            #0xb23980
    // 0xb23978: r2 = Null
    //     0xb23978: mov             x2, NULL
    // 0xb2397c: b               #0xb2399c
    // 0xb23980: r16 = 14.000000
    //     0xb23980: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb23984: ldr             x16, [x16, #0x9a0]
    // 0xb23988: str             x16, [SP]
    // 0xb2398c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb2398c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb23990: ldr             x4, [x4, #0x88]
    // 0xb23994: r0 = copyWith()
    //     0xb23994: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb23998: mov             x2, x0
    // 0xb2399c: ldur            x1, [fp, #-8]
    // 0xb239a0: ldur            x0, [fp, #-0x10]
    // 0xb239a4: stur            x2, [fp, #-0x28]
    // 0xb239a8: r0 = Text()
    //     0xb239a8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb239ac: mov             x2, x0
    // 0xb239b0: ldur            x0, [fp, #-0x10]
    // 0xb239b4: stur            x2, [fp, #-0x30]
    // 0xb239b8: StoreField: r2->field_b = r0
    //     0xb239b8: stur            w0, [x2, #0xb]
    // 0xb239bc: ldur            x0, [fp, #-0x28]
    // 0xb239c0: StoreField: r2->field_13 = r0
    //     0xb239c0: stur            w0, [x2, #0x13]
    // 0xb239c4: ldur            x0, [fp, #-8]
    // 0xb239c8: LoadField: r1 = r0->field_f
    //     0xb239c8: ldur            w1, [x0, #0xf]
    // 0xb239cc: DecompressPointer r1
    //     0xb239cc: add             x1, x1, HEAP, lsl #32
    // 0xb239d0: r0 = controller()
    //     0xb239d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb239d4: LoadField: r1 = r0->field_7f
    //     0xb239d4: ldur            w1, [x0, #0x7f]
    // 0xb239d8: DecompressPointer r1
    //     0xb239d8: add             x1, x1, HEAP, lsl #32
    // 0xb239dc: stur            x1, [fp, #-0x10]
    // 0xb239e0: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb239e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb239e4: ldr             x0, [x0, #0xdd8]
    //     0xb239e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb239ec: cmp             w0, w16
    //     0xb239f0: b.ne            #0xb23a00
    //     0xb239f4: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb239f8: ldr             x2, [x2, #0x780]
    //     0xb239fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb23a00: stur            x0, [fp, #-0x28]
    // 0xb23a04: r0 = LengthLimitingTextInputFormatter()
    //     0xb23a04: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb23a08: mov             x3, x0
    // 0xb23a0c: r0 = 6
    //     0xb23a0c: movz            x0, #0x6
    // 0xb23a10: stur            x3, [fp, #-0x38]
    // 0xb23a14: StoreField: r3->field_7 = r0
    //     0xb23a14: stur            w0, [x3, #7]
    // 0xb23a18: r1 = Null
    //     0xb23a18: mov             x1, NULL
    // 0xb23a1c: r2 = 4
    //     0xb23a1c: movz            x2, #0x4
    // 0xb23a20: r0 = AllocateArray()
    //     0xb23a20: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb23a24: mov             x2, x0
    // 0xb23a28: ldur            x0, [fp, #-0x28]
    // 0xb23a2c: stur            x2, [fp, #-0x40]
    // 0xb23a30: StoreField: r2->field_f = r0
    //     0xb23a30: stur            w0, [x2, #0xf]
    // 0xb23a34: ldur            x0, [fp, #-0x38]
    // 0xb23a38: StoreField: r2->field_13 = r0
    //     0xb23a38: stur            w0, [x2, #0x13]
    // 0xb23a3c: r1 = <TextInputFormatter>
    //     0xb23a3c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb23a40: ldr             x1, [x1, #0x788]
    // 0xb23a44: r0 = AllocateGrowableArray()
    //     0xb23a44: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb23a48: mov             x3, x0
    // 0xb23a4c: ldur            x0, [fp, #-0x40]
    // 0xb23a50: stur            x3, [fp, #-0x28]
    // 0xb23a54: StoreField: r3->field_f = r0
    //     0xb23a54: stur            w0, [x3, #0xf]
    // 0xb23a58: r0 = 4
    //     0xb23a58: movz            x0, #0x4
    // 0xb23a5c: StoreField: r3->field_b = r0
    //     0xb23a5c: stur            w0, [x3, #0xb]
    // 0xb23a60: mov             x2, x0
    // 0xb23a64: r1 = Null
    //     0xb23a64: mov             x1, NULL
    // 0xb23a68: r0 = AllocateArray()
    //     0xb23a68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb23a6c: stur            x0, [fp, #-0x38]
    // 0xb23a70: r16 = "1 - "
    //     0xb23a70: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b790] "1 - "
    //     0xb23a74: ldr             x16, [x16, #0x790]
    // 0xb23a78: StoreField: r0->field_f = r16
    //     0xb23a78: stur            w16, [x0, #0xf]
    // 0xb23a7c: ldur            x2, [fp, #-8]
    // 0xb23a80: LoadField: r1 = r2->field_f
    //     0xb23a80: ldur            w1, [x2, #0xf]
    // 0xb23a84: DecompressPointer r1
    //     0xb23a84: add             x1, x1, HEAP, lsl #32
    // 0xb23a88: r0 = controller()
    //     0xb23a88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23a8c: LoadField: r1 = r0->field_47
    //     0xb23a8c: ldur            w1, [x0, #0x47]
    // 0xb23a90: DecompressPointer r1
    //     0xb23a90: add             x1, x1, HEAP, lsl #32
    // 0xb23a94: r0 = value()
    //     0xb23a94: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb23a98: ldur            x1, [fp, #-0x38]
    // 0xb23a9c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb23a9c: add             x25, x1, #0x13
    //     0xb23aa0: str             w0, [x25]
    //     0xb23aa4: tbz             w0, #0, #0xb23ac0
    //     0xb23aa8: ldurb           w16, [x1, #-1]
    //     0xb23aac: ldurb           w17, [x0, #-1]
    //     0xb23ab0: and             x16, x17, x16, lsr #2
    //     0xb23ab4: tst             x16, HEAP, lsr #32
    //     0xb23ab8: b.eq            #0xb23ac0
    //     0xb23abc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb23ac0: ldur            x16, [fp, #-0x38]
    // 0xb23ac4: str             x16, [SP]
    // 0xb23ac8: r0 = _interpolate()
    //     0xb23ac8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb23acc: mov             x2, x0
    // 0xb23ad0: ldur            x0, [fp, #-8]
    // 0xb23ad4: stur            x2, [fp, #-0x38]
    // 0xb23ad8: LoadField: r1 = r0->field_f
    //     0xb23ad8: ldur            w1, [x0, #0xf]
    // 0xb23adc: DecompressPointer r1
    //     0xb23adc: add             x1, x1, HEAP, lsl #32
    // 0xb23ae0: r0 = controller()
    //     0xb23ae0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23ae4: LoadField: r1 = r0->field_6b
    //     0xb23ae4: ldur            w1, [x0, #0x6b]
    // 0xb23ae8: DecompressPointer r1
    //     0xb23ae8: add             x1, x1, HEAP, lsl #32
    // 0xb23aec: r0 = value()
    //     0xb23aec: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb23af0: cmp             w0, NULL
    // 0xb23af4: b.eq            #0xb23b00
    // 0xb23af8: r7 = ""
    //     0xb23af8: ldr             x7, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb23afc: b               #0xb23b04
    // 0xb23b00: r7 = Null
    //     0xb23b00: mov             x7, NULL
    // 0xb23b04: ldur            x0, [fp, #-8]
    // 0xb23b08: ldur            x6, [fp, #-0x18]
    // 0xb23b0c: ldur            x5, [fp, #-0x20]
    // 0xb23b10: ldur            x4, [fp, #-0x30]
    // 0xb23b14: ldur            x3, [fp, #-0x10]
    // 0xb23b18: ldur            x1, [fp, #-0x38]
    // 0xb23b1c: ldur            x2, [fp, #-0x28]
    // 0xb23b20: stur            x7, [fp, #-0x40]
    // 0xb23b24: r0 = InputDecoration()
    //     0xb23b24: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb23b28: mov             x1, x0
    // 0xb23b2c: ldur            x0, [fp, #-0x38]
    // 0xb23b30: stur            x1, [fp, #-0x48]
    // 0xb23b34: StoreField: r1->field_2f = r0
    //     0xb23b34: stur            w0, [x1, #0x2f]
    // 0xb23b38: r0 = true
    //     0xb23b38: add             x0, NULL, #0x20  ; true
    // 0xb23b3c: StoreField: r1->field_43 = r0
    //     0xb23b3c: stur            w0, [x1, #0x43]
    // 0xb23b40: ldur            x2, [fp, #-0x40]
    // 0xb23b44: StoreField: r1->field_4b = r2
    //     0xb23b44: stur            w2, [x1, #0x4b]
    // 0xb23b48: r2 = Instance_TextStyle
    //     0xb23b48: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb23b4c: ldr             x2, [x2, #0x798]
    // 0xb23b50: StoreField: r1->field_4f = r2
    //     0xb23b50: stur            w2, [x1, #0x4f]
    // 0xb23b54: StoreField: r1->field_cf = r0
    //     0xb23b54: stur            w0, [x1, #0xcf]
    // 0xb23b58: r0 = TextField()
    //     0xb23b58: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb23b5c: r1 = EditableText
    //     0xb23b5c: ldr             x1, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb23b60: stur            x0, [fp, #-0x38]
    // 0xb23b64: StoreField: r0->field_f = r1
    //     0xb23b64: stur            w1, [x0, #0xf]
    // 0xb23b68: ldur            x1, [fp, #-0x10]
    // 0xb23b6c: StoreField: r0->field_13 = r1
    //     0xb23b6c: stur            w1, [x0, #0x13]
    // 0xb23b70: ldur            x1, [fp, #-0x48]
    // 0xb23b74: StoreField: r0->field_1b = r1
    //     0xb23b74: stur            w1, [x0, #0x1b]
    // 0xb23b78: r3 = Instance_TextCapitalization
    //     0xb23b78: ldr             x3, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb23b7c: StoreField: r0->field_27 = r3
    //     0xb23b7c: stur            w3, [x0, #0x27]
    // 0xb23b80: r4 = Instance_TextAlign
    //     0xb23b80: ldr             x4, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb23b84: StoreField: r0->field_33 = r4
    //     0xb23b84: stur            w4, [x0, #0x33]
    // 0xb23b88: r5 = false
    //     0xb23b88: add             x5, NULL, #0x30  ; false
    // 0xb23b8c: StoreField: r0->field_6f = r5
    //     0xb23b8c: stur            w5, [x0, #0x6f]
    // 0xb23b90: StoreField: r0->field_3f = r5
    //     0xb23b90: stur            w5, [x0, #0x3f]
    // 0xb23b94: r6 = "•"
    //     0xb23b94: add             x6, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb23b98: ldr             x6, [x6, #0x4c8]
    // 0xb23b9c: StoreField: r0->field_47 = r6
    //     0xb23b9c: stur            w6, [x0, #0x47]
    // 0xb23ba0: StoreField: r0->field_4b = r5
    //     0xb23ba0: stur            w5, [x0, #0x4b]
    // 0xb23ba4: r7 = true
    //     0xb23ba4: add             x7, NULL, #0x20  ; true
    // 0xb23ba8: StoreField: r0->field_4f = r7
    //     0xb23ba8: stur            w7, [x0, #0x4f]
    // 0xb23bac: StoreField: r0->field_5b = r7
    //     0xb23bac: stur            w7, [x0, #0x5b]
    // 0xb23bb0: r8 = 1
    //     0xb23bb0: movz            x8, #0x1
    // 0xb23bb4: StoreField: r0->field_5f = r8
    //     0xb23bb4: stur            x8, [x0, #0x5f]
    // 0xb23bb8: StoreField: r0->field_6b = r5
    //     0xb23bb8: stur            w5, [x0, #0x6b]
    // 0xb23bbc: ldur            x1, [fp, #-0x28]
    // 0xb23bc0: StoreField: r0->field_93 = r1
    //     0xb23bc0: stur            w1, [x0, #0x93]
    // 0xb23bc4: d0 = 2.000000
    //     0xb23bc4: fmov            d0, #2.00000000
    // 0xb23bc8: StoreField: r0->field_9f = d0
    //     0xb23bc8: stur            d0, [x0, #0x9f]
    // 0xb23bcc: r9 = Instance_BoxHeightStyle
    //     0xb23bcc: ldr             x9, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb23bd0: StoreField: r0->field_bb = r9
    //     0xb23bd0: stur            w9, [x0, #0xbb]
    // 0xb23bd4: r10 = Instance_BoxWidthStyle
    //     0xb23bd4: ldr             x10, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb23bd8: StoreField: r0->field_bf = r10
    //     0xb23bd8: stur            w10, [x0, #0xbf]
    // 0xb23bdc: r11 = Instance_EdgeInsets
    //     0xb23bdc: ldr             x11, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb23be0: StoreField: r0->field_c7 = r11
    //     0xb23be0: stur            w11, [x0, #0xc7]
    // 0xb23be4: r12 = Instance_DragStartBehavior
    //     0xb23be4: ldr             x12, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb23be8: StoreField: r0->field_d3 = r12
    //     0xb23be8: stur            w12, [x0, #0xd3]
    // 0xb23bec: StoreField: r0->field_db = r5
    //     0xb23bec: stur            w5, [x0, #0xdb]
    // 0xb23bf0: r13 = const []
    //     0xb23bf0: ldr             x13, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb23bf4: StoreField: r0->field_f3 = r13
    //     0xb23bf4: stur            w13, [x0, #0xf3]
    // 0xb23bf8: r14 = Instance_Clip
    //     0xb23bf8: add             x14, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb23bfc: ldr             x14, [x14, #0x7c0]
    // 0xb23c00: StoreField: r0->field_f7 = r14
    //     0xb23c00: stur            w14, [x0, #0xf7]
    // 0xb23c04: StoreField: r0->field_ff = r7
    //     0xb23c04: stur            w7, [x0, #0xff]
    // 0xb23c08: r17 = 259
    //     0xb23c08: movz            x17, #0x103
    // 0xb23c0c: str             w7, [x0, x17]
    // 0xb23c10: r19 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb23c10: add             x19, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb23c14: ldr             x19, [x19, #0x4d8]
    // 0xb23c18: r17 = 267
    //     0xb23c18: movz            x17, #0x10b
    // 0xb23c1c: str             w19, [x0, x17]
    // 0xb23c20: r17 = 271
    //     0xb23c20: movz            x17, #0x10f
    // 0xb23c24: str             w7, [x0, x17]
    // 0xb23c28: r20 = Instance_SmartDashesType
    //     0xb23c28: ldr             x20, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb23c2c: StoreField: r0->field_53 = r20
    //     0xb23c2c: stur            w20, [x0, #0x53]
    // 0xb23c30: r23 = Instance_SmartQuotesType
    //     0xb23c30: add             x23, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb23c34: ldr             x23, [x23, #0x4e0]
    // 0xb23c38: StoreField: r0->field_57 = r23
    //     0xb23c38: stur            w23, [x0, #0x57]
    // 0xb23c3c: r24 = Instance_TextInputType
    //     0xb23c3c: add             x24, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb23c40: ldr             x24, [x24, #0x7a0]
    // 0xb23c44: StoreField: r0->field_1f = r24
    //     0xb23c44: stur            w24, [x0, #0x1f]
    // 0xb23c48: StoreField: r0->field_cb = r7
    //     0xb23c48: stur            w7, [x0, #0xcb]
    // 0xb23c4c: r1 = Null
    //     0xb23c4c: mov             x1, NULL
    // 0xb23c50: r2 = 16
    //     0xb23c50: movz            x2, #0x10
    // 0xb23c54: r0 = AllocateArray()
    //     0xb23c54: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb23c58: mov             x2, x0
    // 0xb23c5c: ldur            x0, [fp, #-0x18]
    // 0xb23c60: stur            x2, [fp, #-0x10]
    // 0xb23c64: StoreField: r2->field_f = r0
    //     0xb23c64: stur            w0, [x2, #0xf]
    // 0xb23c68: r16 = Instance_SizedBox
    //     0xb23c68: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb23c6c: ldr             x16, [x16, #0x4a0]
    // 0xb23c70: StoreField: r2->field_13 = r16
    //     0xb23c70: stur            w16, [x2, #0x13]
    // 0xb23c74: ldur            x0, [fp, #-0x20]
    // 0xb23c78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb23c78: stur            w0, [x2, #0x17]
    // 0xb23c7c: r16 = Instance_SizedBox
    //     0xb23c7c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb23c80: ldr             x16, [x16, #0xfe8]
    // 0xb23c84: StoreField: r2->field_1b = r16
    //     0xb23c84: stur            w16, [x2, #0x1b]
    // 0xb23c88: ldur            x0, [fp, #-0x30]
    // 0xb23c8c: StoreField: r2->field_1f = r0
    //     0xb23c8c: stur            w0, [x2, #0x1f]
    // 0xb23c90: r16 = Instance_SizedBox
    //     0xb23c90: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb23c94: ldr             x16, [x16, #0x4a0]
    // 0xb23c98: StoreField: r2->field_23 = r16
    //     0xb23c98: stur            w16, [x2, #0x23]
    // 0xb23c9c: ldur            x0, [fp, #-0x38]
    // 0xb23ca0: StoreField: r2->field_27 = r0
    //     0xb23ca0: stur            w0, [x2, #0x27]
    // 0xb23ca4: r16 = Instance_SizedBox
    //     0xb23ca4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb23ca8: ldr             x16, [x16, #0x7a8]
    // 0xb23cac: StoreField: r2->field_2b = r16
    //     0xb23cac: stur            w16, [x2, #0x2b]
    // 0xb23cb0: r1 = <Widget>
    //     0xb23cb0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb23cb4: r0 = AllocateGrowableArray()
    //     0xb23cb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb23cb8: mov             x2, x0
    // 0xb23cbc: ldur            x0, [fp, #-0x10]
    // 0xb23cc0: stur            x2, [fp, #-0x18]
    // 0xb23cc4: StoreField: r2->field_f = r0
    //     0xb23cc4: stur            w0, [x2, #0xf]
    // 0xb23cc8: r0 = 16
    //     0xb23cc8: movz            x0, #0x10
    // 0xb23ccc: StoreField: r2->field_b = r0
    //     0xb23ccc: stur            w0, [x2, #0xb]
    // 0xb23cd0: ldur            x0, [fp, #-8]
    // 0xb23cd4: LoadField: r1 = r0->field_f
    //     0xb23cd4: ldur            w1, [x0, #0xf]
    // 0xb23cd8: DecompressPointer r1
    //     0xb23cd8: add             x1, x1, HEAP, lsl #32
    // 0xb23cdc: r0 = controller()
    //     0xb23cdc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23ce0: LoadField: r1 = r0->field_6b
    //     0xb23ce0: ldur            w1, [x0, #0x6b]
    // 0xb23ce4: DecompressPointer r1
    //     0xb23ce4: add             x1, x1, HEAP, lsl #32
    // 0xb23ce8: r0 = value()
    //     0xb23ce8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb23cec: cmp             w0, NULL
    // 0xb23cf0: b.eq            #0xb23e5c
    // 0xb23cf4: ldur            x25, [fp, #-8]
    // 0xb23cf8: LoadField: r1 = r25->field_f
    //     0xb23cf8: ldur            w1, [x25, #0xf]
    // 0xb23cfc: DecompressPointer r1
    //     0xb23cfc: add             x1, x1, HEAP, lsl #32
    // 0xb23d00: r0 = controller()
    //     0xb23d00: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb23d04: LoadField: r1 = r0->field_6b
    //     0xb23d04: ldur            w1, [x0, #0x6b]
    // 0xb23d08: DecompressPointer r1
    //     0xb23d08: add             x1, x1, HEAP, lsl #32
    // 0xb23d0c: r0 = value()
    //     0xb23d0c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb23d10: r1 = 60
    //     0xb23d10: movz            x1, #0x3c
    // 0xb23d14: branchIfSmi(r0, 0xb23d20)
    //     0xb23d14: tbz             w0, #0, #0xb23d20
    // 0xb23d18: r1 = LoadClassIdInstr(r0)
    //     0xb23d18: ldur            x1, [x0, #-1]
    //     0xb23d1c: ubfx            x1, x1, #0xc, #0x14
    // 0xb23d20: str             x0, [SP]
    // 0xb23d24: mov             x0, x1
    // 0xb23d28: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb23d28: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb23d2c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb23d2c: movz            x17, #0x2b03
    //     0xb23d30: add             lr, x0, x17
    //     0xb23d34: ldr             lr, [x21, lr, lsl #3]
    //     0xb23d38: blr             lr
    // 0xb23d3c: stur            x0, [fp, #-0x10]
    // 0xb23d40: r0 = GetNavigation.textTheme()
    //     0xb23d40: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb23d44: LoadField: r3 = r0->field_27
    //     0xb23d44: ldur            w3, [x0, #0x27]
    // 0xb23d48: DecompressPointer r3
    //     0xb23d48: add             x3, x3, HEAP, lsl #32
    // 0xb23d4c: stur            x3, [fp, #-0x20]
    // 0xb23d50: cmp             w3, NULL
    // 0xb23d54: b.ne            #0xb23d60
    // 0xb23d58: r2 = Null
    //     0xb23d58: mov             x2, NULL
    // 0xb23d5c: b               #0xb23db8
    // 0xb23d60: r1 = _ConstMap len:3
    //     0xb23d60: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb23d64: ldr             x1, [x1, #0xcd0]
    // 0xb23d68: r2 = 6
    //     0xb23d68: movz            x2, #0x6
    // 0xb23d6c: r0 = []()
    //     0xb23d6c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb23d70: r1 = _ConstMap len:3
    //     0xb23d70: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb23d74: ldr             x1, [x1, #0xcd0]
    // 0xb23d78: r2 = 4
    //     0xb23d78: movz            x2, #0x4
    // 0xb23d7c: stur            x0, [fp, #-0x28]
    // 0xb23d80: r0 = []()
    //     0xb23d80: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb23d84: r16 = <Color?>
    //     0xb23d84: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb23d88: ldr             x16, [x16, #0x98]
    // 0xb23d8c: stp             x0, x16, [SP, #8]
    // 0xb23d90: ldur            x16, [fp, #-0x28]
    // 0xb23d94: str             x16, [SP]
    // 0xb23d98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb23d98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb23d9c: r0 = mode()
    //     0xb23d9c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb23da0: str             x0, [SP]
    // 0xb23da4: ldur            x1, [fp, #-0x20]
    // 0xb23da8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb23da8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb23dac: ldr             x4, [x4, #0x228]
    // 0xb23db0: r0 = copyWith()
    //     0xb23db0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb23db4: mov             x2, x0
    // 0xb23db8: ldur            x0, [fp, #-0x10]
    // 0xb23dbc: ldur            x1, [fp, #-0x18]
    // 0xb23dc0: stur            x2, [fp, #-0x20]
    // 0xb23dc4: r0 = Text()
    //     0xb23dc4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb23dc8: mov             x2, x0
    // 0xb23dcc: ldur            x0, [fp, #-0x10]
    // 0xb23dd0: stur            x2, [fp, #-0x28]
    // 0xb23dd4: StoreField: r2->field_b = r0
    //     0xb23dd4: stur            w0, [x2, #0xb]
    // 0xb23dd8: ldur            x0, [fp, #-0x20]
    // 0xb23ddc: StoreField: r2->field_13 = r0
    //     0xb23ddc: stur            w0, [x2, #0x13]
    // 0xb23de0: ldur            x0, [fp, #-0x18]
    // 0xb23de4: LoadField: r1 = r0->field_b
    //     0xb23de4: ldur            w1, [x0, #0xb]
    // 0xb23de8: LoadField: r3 = r0->field_f
    //     0xb23de8: ldur            w3, [x0, #0xf]
    // 0xb23dec: DecompressPointer r3
    //     0xb23dec: add             x3, x3, HEAP, lsl #32
    // 0xb23df0: LoadField: r4 = r3->field_b
    //     0xb23df0: ldur            w4, [x3, #0xb]
    // 0xb23df4: r3 = LoadInt32Instr(r1)
    //     0xb23df4: sbfx            x3, x1, #1, #0x1f
    // 0xb23df8: stur            x3, [fp, #-0x50]
    // 0xb23dfc: r1 = LoadInt32Instr(r4)
    //     0xb23dfc: sbfx            x1, x4, #1, #0x1f
    // 0xb23e00: cmp             x3, x1
    // 0xb23e04: b.ne            #0xb23e10
    // 0xb23e08: mov             x1, x0
    // 0xb23e0c: r0 = _growToNextCapacity()
    //     0xb23e0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb23e10: ldur            x2, [fp, #-0x18]
    // 0xb23e14: ldur            x3, [fp, #-0x50]
    // 0xb23e18: add             x0, x3, #1
    // 0xb23e1c: lsl             x1, x0, #1
    // 0xb23e20: StoreField: r2->field_b = r1
    //     0xb23e20: stur            w1, [x2, #0xb]
    // 0xb23e24: LoadField: r1 = r2->field_f
    //     0xb23e24: ldur            w1, [x2, #0xf]
    // 0xb23e28: DecompressPointer r1
    //     0xb23e28: add             x1, x1, HEAP, lsl #32
    // 0xb23e2c: ldur            x0, [fp, #-0x28]
    // 0xb23e30: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb23e30: add             x25, x1, x3, lsl #2
    //     0xb23e34: add             x25, x25, #0xf
    //     0xb23e38: str             w0, [x25]
    //     0xb23e3c: tbz             w0, #0, #0xb23e58
    //     0xb23e40: ldurb           w16, [x1, #-1]
    //     0xb23e44: ldurb           w17, [x0, #-1]
    //     0xb23e48: and             x16, x17, x16, lsr #2
    //     0xb23e4c: tst             x16, HEAP, lsr #32
    //     0xb23e50: b.eq            #0xb23e58
    //     0xb23e54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb23e58: b               #0xb23e60
    // 0xb23e5c: ldur            x2, [fp, #-0x18]
    // 0xb23e60: r0 = Column()
    //     0xb23e60: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb23e64: mov             x1, x0
    // 0xb23e68: r0 = Instance_Axis
    //     0xb23e68: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb23e6c: stur            x1, [fp, #-0x10]
    // 0xb23e70: StoreField: r1->field_f = r0
    //     0xb23e70: stur            w0, [x1, #0xf]
    // 0xb23e74: r0 = Instance_MainAxisAlignment
    //     0xb23e74: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb23e78: ldr             x0, [x0, #0x730]
    // 0xb23e7c: StoreField: r1->field_13 = r0
    //     0xb23e7c: stur            w0, [x1, #0x13]
    // 0xb23e80: r0 = Instance_MainAxisSize
    //     0xb23e80: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb23e84: ldr             x0, [x0, #0x738]
    // 0xb23e88: ArrayStore: r1[0] = r0  ; List_4
    //     0xb23e88: stur            w0, [x1, #0x17]
    // 0xb23e8c: r0 = Instance_CrossAxisAlignment
    //     0xb23e8c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb23e90: ldr             x0, [x0, #0x68]
    // 0xb23e94: StoreField: r1->field_1b = r0
    //     0xb23e94: stur            w0, [x1, #0x1b]
    // 0xb23e98: r0 = Instance_VerticalDirection
    //     0xb23e98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb23e9c: ldr             x0, [x0, #0x748]
    // 0xb23ea0: StoreField: r1->field_23 = r0
    //     0xb23ea0: stur            w0, [x1, #0x23]
    // 0xb23ea4: r0 = Instance_Clip
    //     0xb23ea4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb23ea8: ldr             x0, [x0, #0x750]
    // 0xb23eac: StoreField: r1->field_2b = r0
    //     0xb23eac: stur            w0, [x1, #0x2b]
    // 0xb23eb0: StoreField: r1->field_2f = rZR
    //     0xb23eb0: stur            xzr, [x1, #0x2f]
    // 0xb23eb4: ldur            x0, [fp, #-0x18]
    // 0xb23eb8: StoreField: r1->field_b = r0
    //     0xb23eb8: stur            w0, [x1, #0xb]
    // 0xb23ebc: r0 = Padding()
    //     0xb23ebc: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb23ec0: mov             x1, x0
    // 0xb23ec4: r0 = Instance_EdgeInsets
    //     0xb23ec4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb23ec8: ldr             x0, [x0, #0x7b0]
    // 0xb23ecc: StoreField: r1->field_f = r0
    //     0xb23ecc: stur            w0, [x1, #0xf]
    // 0xb23ed0: ldur            x0, [fp, #-0x10]
    // 0xb23ed4: StoreField: r1->field_b = r0
    //     0xb23ed4: stur            w0, [x1, #0xb]
    // 0xb23ed8: mov             x0, x1
    // 0xb23edc: LeaveFrame
    //     0xb23edc: mov             SP, fp
    //     0xb23ee0: ldp             fp, lr, [SP], #0x10
    // 0xb23ee4: ret
    //     0xb23ee4: ret             
    // 0xb23ee8: ldur            x25, [fp, #-8]
    // 0xb23eec: r7 = true
    //     0xb23eec: add             x7, NULL, #0x20  ; true
    // 0xb23ef0: r2 = Instance_TextStyle
    //     0xb23ef0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb23ef4: ldr             x2, [x2, #0x798]
    // 0xb23ef8: r24 = Instance_TextInputType
    //     0xb23ef8: add             x24, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb23efc: ldr             x24, [x24, #0x7a0]
    // 0xb23f00: r0 = Instance_Axis
    //     0xb23f00: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb23f04: r13 = const []
    //     0xb23f04: ldr             x13, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb23f08: r5 = false
    //     0xb23f08: add             x5, NULL, #0x30  ; false
    // 0xb23f0c: r19 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb23f0c: add             x19, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb23f10: ldr             x19, [x19, #0x4d8]
    // 0xb23f14: r1 = EditableText
    //     0xb23f14: ldr             x1, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb23f18: r4 = Instance_TextAlign
    //     0xb23f18: ldr             x4, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb23f1c: r3 = Instance_TextCapitalization
    //     0xb23f1c: ldr             x3, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb23f20: r6 = "•"
    //     0xb23f20: add             x6, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb23f24: ldr             x6, [x6, #0x4c8]
    // 0xb23f28: r9 = Instance_BoxHeightStyle
    //     0xb23f28: ldr             x9, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb23f2c: r10 = Instance_BoxWidthStyle
    //     0xb23f2c: ldr             x10, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb23f30: r11 = Instance_EdgeInsets
    //     0xb23f30: ldr             x11, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb23f34: r12 = Instance_DragStartBehavior
    //     0xb23f34: ldr             x12, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb23f38: r14 = Instance_Clip
    //     0xb23f38: add             x14, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb23f3c: ldr             x14, [x14, #0x7c0]
    // 0xb23f40: r23 = Instance_SmartQuotesType
    //     0xb23f40: add             x23, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb23f44: ldr             x23, [x23, #0x4e0]
    // 0xb23f48: r20 = Instance_SmartDashesType
    //     0xb23f48: ldr             x20, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb23f4c: r8 = 1
    //     0xb23f4c: movz            x8, #0x1
    // 0xb23f50: d0 = 2.000000
    //     0xb23f50: fmov            d0, #2.00000000
    // 0xb23f54: mov             x16, x24
    // 0xb23f58: mov             x24, x25
    // 0xb23f5c: mov             x25, x16
    // 0xb23f60: mov             x16, x23
    // 0xb23f64: mov             x23, x24
    // 0xb23f68: mov             x24, x16
    // 0xb23f6c: mov             x16, x20
    // 0xb23f70: mov             x20, x23
    // 0xb23f74: mov             x23, x16
    // 0xb23f78: mov             x16, x19
    // 0xb23f7c: mov             x19, x20
    // 0xb23f80: mov             x20, x16
    // 0xb23f84: mov             x16, x14
    // 0xb23f88: mov             x14, x19
    // 0xb23f8c: mov             x19, x16
    // 0xb23f90: mov             x16, x13
    // 0xb23f94: mov             x13, x14
    // 0xb23f98: mov             x14, x16
    // 0xb23f9c: mov             x16, x12
    // 0xb23fa0: mov             x12, x13
    // 0xb23fa4: mov             x13, x16
    // 0xb23fa8: mov             x16, x11
    // 0xb23fac: mov             x11, x12
    // 0xb23fb0: mov             x12, x16
    // 0xb23fb4: mov             x16, x10
    // 0xb23fb8: mov             x10, x11
    // 0xb23fbc: mov             x11, x16
    // 0xb23fc0: mov             x16, x9
    // 0xb23fc4: mov             x9, x10
    // 0xb23fc8: mov             x10, x16
    // 0xb23fcc: mov             x16, x8
    // 0xb23fd0: mov             x8, x9
    // 0xb23fd4: mov             x9, x16
    // 0xb23fd8: mov             x16, x7
    // 0xb23fdc: mov             x7, x8
    // 0xb23fe0: mov             x8, x16
    // 0xb23fe4: mov             x16, x6
    // 0xb23fe8: mov             x6, x7
    // 0xb23fec: mov             x7, x16
    // 0xb23ff0: mov             x16, x5
    // 0xb23ff4: mov             x5, x6
    // 0xb23ff8: mov             x6, x16
    // 0xb23ffc: mov             x16, x4
    // 0xb24000: mov             x4, x5
    // 0xb24004: mov             x5, x16
    // 0xb24008: mov             x16, x3
    // 0xb2400c: mov             x3, x4
    // 0xb24010: mov             x4, x16
    // 0xb24014: mov             x16, x2
    // 0xb24018: mov             x2, x3
    // 0xb2401c: mov             x3, x16
    // 0xb24020: mov             x0, x1
    // 0xb24024: r1 = 8
    //     0xb24024: movz            x1, #0x8
    // 0xb24028: b               #0xb24680
    // 0xb2402c: ldur            x25, [fp, #-8]
    // 0xb24030: r7 = true
    //     0xb24030: add             x7, NULL, #0x20  ; true
    // 0xb24034: r2 = Instance_TextStyle
    //     0xb24034: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb24038: ldr             x2, [x2, #0x798]
    // 0xb2403c: r24 = Instance_TextInputType
    //     0xb2403c: add             x24, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb24040: ldr             x24, [x24, #0x7a0]
    // 0xb24044: r13 = const []
    //     0xb24044: ldr             x13, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb24048: r5 = false
    //     0xb24048: add             x5, NULL, #0x30  ; false
    // 0xb2404c: r19 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb2404c: add             x19, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb24050: ldr             x19, [x19, #0x4d8]
    // 0xb24054: r1 = EditableText
    //     0xb24054: ldr             x1, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb24058: r4 = Instance_TextAlign
    //     0xb24058: ldr             x4, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb2405c: r3 = Instance_TextCapitalization
    //     0xb2405c: ldr             x3, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb24060: r6 = "•"
    //     0xb24060: add             x6, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb24064: ldr             x6, [x6, #0x4c8]
    // 0xb24068: r9 = Instance_BoxHeightStyle
    //     0xb24068: ldr             x9, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb2406c: r10 = Instance_BoxWidthStyle
    //     0xb2406c: ldr             x10, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb24070: r11 = Instance_EdgeInsets
    //     0xb24070: ldr             x11, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb24074: r12 = Instance_DragStartBehavior
    //     0xb24074: ldr             x12, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb24078: r14 = Instance_Clip
    //     0xb24078: add             x14, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb2407c: ldr             x14, [x14, #0x7c0]
    // 0xb24080: r23 = Instance_SmartQuotesType
    //     0xb24080: add             x23, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb24084: ldr             x23, [x23, #0x4e0]
    // 0xb24088: r20 = Instance_SmartDashesType
    //     0xb24088: ldr             x20, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb2408c: r8 = 1
    //     0xb2408c: movz            x8, #0x1
    // 0xb24090: d0 = 2.000000
    //     0xb24090: fmov            d0, #2.00000000
    // 0xb24094: cmp             w0, #2
    // 0xb24098: b.ne            #0xb245ac
    // 0xb2409c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2409c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb240a0: ldr             x0, [x0, #0x2670]
    //     0xb240a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb240a8: cmp             w0, w16
    //     0xb240ac: b.ne            #0xb240b8
    //     0xb240b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb240b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb240b8: r0 = GetNavigation.textTheme()
    //     0xb240b8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb240bc: LoadField: r1 = r0->field_27
    //     0xb240bc: ldur            w1, [x0, #0x27]
    // 0xb240c0: DecompressPointer r1
    //     0xb240c0: add             x1, x1, HEAP, lsl #32
    // 0xb240c4: cmp             w1, NULL
    // 0xb240c8: b.ne            #0xb240d4
    // 0xb240cc: r1 = Null
    //     0xb240cc: mov             x1, NULL
    // 0xb240d0: b               #0xb240f0
    // 0xb240d4: r16 = 14.000000
    //     0xb240d4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb240d8: ldr             x16, [x16, #0x9a0]
    // 0xb240dc: str             x16, [SP]
    // 0xb240e0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb240e0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb240e4: ldr             x4, [x4, #0x88]
    // 0xb240e8: r0 = copyWith()
    //     0xb240e8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb240ec: mov             x1, x0
    // 0xb240f0: ldur            x0, [fp, #-8]
    // 0xb240f4: stur            x1, [fp, #-0x10]
    // 0xb240f8: r0 = Text()
    //     0xb240f8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb240fc: mov             x2, x0
    // 0xb24100: r0 = "Masukkan nomor Juz (1 - 30)"
    //     0xb24100: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c168] "Masukkan nomor Juz (1 - 30)"
    //     0xb24104: ldr             x0, [x0, #0x168]
    // 0xb24108: stur            x2, [fp, #-0x18]
    // 0xb2410c: StoreField: r2->field_b = r0
    //     0xb2410c: stur            w0, [x2, #0xb]
    // 0xb24110: ldur            x0, [fp, #-0x10]
    // 0xb24114: StoreField: r2->field_13 = r0
    //     0xb24114: stur            w0, [x2, #0x13]
    // 0xb24118: ldur            x0, [fp, #-8]
    // 0xb2411c: LoadField: r1 = r0->field_f
    //     0xb2411c: ldur            w1, [x0, #0xf]
    // 0xb24120: DecompressPointer r1
    //     0xb24120: add             x1, x1, HEAP, lsl #32
    // 0xb24124: r0 = controller()
    //     0xb24124: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24128: LoadField: r1 = r0->field_77
    //     0xb24128: ldur            w1, [x0, #0x77]
    // 0xb2412c: DecompressPointer r1
    //     0xb2412c: add             x1, x1, HEAP, lsl #32
    // 0xb24130: stur            x1, [fp, #-0x10]
    // 0xb24134: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb24134: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb24138: ldr             x0, [x0, #0xdd8]
    //     0xb2413c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb24140: cmp             w0, w16
    //     0xb24144: b.ne            #0xb24154
    //     0xb24148: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb2414c: ldr             x2, [x2, #0x780]
    //     0xb24150: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb24154: stur            x0, [fp, #-0x20]
    // 0xb24158: r0 = LengthLimitingTextInputFormatter()
    //     0xb24158: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb2415c: mov             x3, x0
    // 0xb24160: r0 = 4
    //     0xb24160: movz            x0, #0x4
    // 0xb24164: stur            x3, [fp, #-0x28]
    // 0xb24168: StoreField: r3->field_7 = r0
    //     0xb24168: stur            w0, [x3, #7]
    // 0xb2416c: mov             x2, x0
    // 0xb24170: r1 = Null
    //     0xb24170: mov             x1, NULL
    // 0xb24174: r0 = AllocateArray()
    //     0xb24174: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb24178: mov             x2, x0
    // 0xb2417c: ldur            x0, [fp, #-0x20]
    // 0xb24180: stur            x2, [fp, #-0x30]
    // 0xb24184: StoreField: r2->field_f = r0
    //     0xb24184: stur            w0, [x2, #0xf]
    // 0xb24188: ldur            x0, [fp, #-0x28]
    // 0xb2418c: StoreField: r2->field_13 = r0
    //     0xb2418c: stur            w0, [x2, #0x13]
    // 0xb24190: r1 = <TextInputFormatter>
    //     0xb24190: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb24194: ldr             x1, [x1, #0x788]
    // 0xb24198: r0 = AllocateGrowableArray()
    //     0xb24198: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb2419c: mov             x2, x0
    // 0xb241a0: ldur            x0, [fp, #-0x30]
    // 0xb241a4: stur            x2, [fp, #-0x20]
    // 0xb241a8: StoreField: r2->field_f = r0
    //     0xb241a8: stur            w0, [x2, #0xf]
    // 0xb241ac: r0 = 4
    //     0xb241ac: movz            x0, #0x4
    // 0xb241b0: StoreField: r2->field_b = r0
    //     0xb241b0: stur            w0, [x2, #0xb]
    // 0xb241b4: ldur            x3, [fp, #-8]
    // 0xb241b8: LoadField: r1 = r3->field_f
    //     0xb241b8: ldur            w1, [x3, #0xf]
    // 0xb241bc: DecompressPointer r1
    //     0xb241bc: add             x1, x1, HEAP, lsl #32
    // 0xb241c0: r0 = controller()
    //     0xb241c0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb241c4: LoadField: r1 = r0->field_67
    //     0xb241c4: ldur            w1, [x0, #0x67]
    // 0xb241c8: DecompressPointer r1
    //     0xb241c8: add             x1, x1, HEAP, lsl #32
    // 0xb241cc: r0 = value()
    //     0xb241cc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb241d0: cmp             w0, NULL
    // 0xb241d4: b.eq            #0xb241e0
    // 0xb241d8: r4 = ""
    //     0xb241d8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb241dc: b               #0xb241e4
    // 0xb241e0: r4 = Null
    //     0xb241e0: mov             x4, NULL
    // 0xb241e4: ldur            x1, [fp, #-8]
    // 0xb241e8: ldur            x3, [fp, #-0x18]
    // 0xb241ec: ldur            x2, [fp, #-0x10]
    // 0xb241f0: ldur            x0, [fp, #-0x20]
    // 0xb241f4: stur            x4, [fp, #-0x28]
    // 0xb241f8: r0 = InputDecoration()
    //     0xb241f8: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb241fc: mov             x1, x0
    // 0xb24200: r0 = "1 - 30"
    //     0xb24200: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c170] "1 - 30"
    //     0xb24204: ldr             x0, [x0, #0x170]
    // 0xb24208: stur            x1, [fp, #-0x30]
    // 0xb2420c: StoreField: r1->field_2f = r0
    //     0xb2420c: stur            w0, [x1, #0x2f]
    // 0xb24210: r0 = true
    //     0xb24210: add             x0, NULL, #0x20  ; true
    // 0xb24214: StoreField: r1->field_43 = r0
    //     0xb24214: stur            w0, [x1, #0x43]
    // 0xb24218: ldur            x2, [fp, #-0x28]
    // 0xb2421c: StoreField: r1->field_4b = r2
    //     0xb2421c: stur            w2, [x1, #0x4b]
    // 0xb24220: r3 = Instance_TextStyle
    //     0xb24220: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb24224: ldr             x3, [x3, #0x798]
    // 0xb24228: StoreField: r1->field_4f = r3
    //     0xb24228: stur            w3, [x1, #0x4f]
    // 0xb2422c: StoreField: r1->field_cf = r0
    //     0xb2422c: stur            w0, [x1, #0xcf]
    // 0xb24230: r0 = TextField()
    //     0xb24230: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb24234: mov             x1, x0
    // 0xb24238: r0 = EditableText
    //     0xb24238: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb2423c: stur            x1, [fp, #-0x28]
    // 0xb24240: StoreField: r1->field_f = r0
    //     0xb24240: stur            w0, [x1, #0xf]
    // 0xb24244: ldur            x0, [fp, #-0x10]
    // 0xb24248: StoreField: r1->field_13 = r0
    //     0xb24248: stur            w0, [x1, #0x13]
    // 0xb2424c: ldur            x0, [fp, #-0x30]
    // 0xb24250: StoreField: r1->field_1b = r0
    //     0xb24250: stur            w0, [x1, #0x1b]
    // 0xb24254: r4 = Instance_TextCapitalization
    //     0xb24254: ldr             x4, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb24258: StoreField: r1->field_27 = r4
    //     0xb24258: stur            w4, [x1, #0x27]
    // 0xb2425c: r5 = Instance_TextAlign
    //     0xb2425c: ldr             x5, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb24260: StoreField: r1->field_33 = r5
    //     0xb24260: stur            w5, [x1, #0x33]
    // 0xb24264: r6 = false
    //     0xb24264: add             x6, NULL, #0x30  ; false
    // 0xb24268: StoreField: r1->field_6f = r6
    //     0xb24268: stur            w6, [x1, #0x6f]
    // 0xb2426c: StoreField: r1->field_3f = r6
    //     0xb2426c: stur            w6, [x1, #0x3f]
    // 0xb24270: r7 = "•"
    //     0xb24270: add             x7, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb24274: ldr             x7, [x7, #0x4c8]
    // 0xb24278: StoreField: r1->field_47 = r7
    //     0xb24278: stur            w7, [x1, #0x47]
    // 0xb2427c: StoreField: r1->field_4b = r6
    //     0xb2427c: stur            w6, [x1, #0x4b]
    // 0xb24280: r8 = true
    //     0xb24280: add             x8, NULL, #0x20  ; true
    // 0xb24284: StoreField: r1->field_4f = r8
    //     0xb24284: stur            w8, [x1, #0x4f]
    // 0xb24288: StoreField: r1->field_5b = r8
    //     0xb24288: stur            w8, [x1, #0x5b]
    // 0xb2428c: r9 = 1
    //     0xb2428c: movz            x9, #0x1
    // 0xb24290: StoreField: r1->field_5f = r9
    //     0xb24290: stur            x9, [x1, #0x5f]
    // 0xb24294: StoreField: r1->field_6b = r6
    //     0xb24294: stur            w6, [x1, #0x6b]
    // 0xb24298: ldur            x0, [fp, #-0x20]
    // 0xb2429c: StoreField: r1->field_93 = r0
    //     0xb2429c: stur            w0, [x1, #0x93]
    // 0xb242a0: d0 = 2.000000
    //     0xb242a0: fmov            d0, #2.00000000
    // 0xb242a4: StoreField: r1->field_9f = d0
    //     0xb242a4: stur            d0, [x1, #0x9f]
    // 0xb242a8: r10 = Instance_BoxHeightStyle
    //     0xb242a8: ldr             x10, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb242ac: StoreField: r1->field_bb = r10
    //     0xb242ac: stur            w10, [x1, #0xbb]
    // 0xb242b0: r11 = Instance_BoxWidthStyle
    //     0xb242b0: ldr             x11, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb242b4: StoreField: r1->field_bf = r11
    //     0xb242b4: stur            w11, [x1, #0xbf]
    // 0xb242b8: r12 = Instance_EdgeInsets
    //     0xb242b8: ldr             x12, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb242bc: StoreField: r1->field_c7 = r12
    //     0xb242bc: stur            w12, [x1, #0xc7]
    // 0xb242c0: r13 = Instance_DragStartBehavior
    //     0xb242c0: ldr             x13, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb242c4: StoreField: r1->field_d3 = r13
    //     0xb242c4: stur            w13, [x1, #0xd3]
    // 0xb242c8: StoreField: r1->field_db = r6
    //     0xb242c8: stur            w6, [x1, #0xdb]
    // 0xb242cc: r14 = const []
    //     0xb242cc: ldr             x14, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb242d0: StoreField: r1->field_f3 = r14
    //     0xb242d0: stur            w14, [x1, #0xf3]
    // 0xb242d4: r19 = Instance_Clip
    //     0xb242d4: add             x19, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb242d8: ldr             x19, [x19, #0x7c0]
    // 0xb242dc: StoreField: r1->field_f7 = r19
    //     0xb242dc: stur            w19, [x1, #0xf7]
    // 0xb242e0: StoreField: r1->field_ff = r8
    //     0xb242e0: stur            w8, [x1, #0xff]
    // 0xb242e4: r17 = 259
    //     0xb242e4: movz            x17, #0x103
    // 0xb242e8: str             w8, [x1, x17]
    // 0xb242ec: r20 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb242ec: add             x20, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb242f0: ldr             x20, [x20, #0x4d8]
    // 0xb242f4: r17 = 267
    //     0xb242f4: movz            x17, #0x10b
    // 0xb242f8: str             w20, [x1, x17]
    // 0xb242fc: r17 = 271
    //     0xb242fc: movz            x17, #0x10f
    // 0xb24300: str             w8, [x1, x17]
    // 0xb24304: r23 = Instance_SmartDashesType
    //     0xb24304: ldr             x23, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb24308: StoreField: r1->field_53 = r23
    //     0xb24308: stur            w23, [x1, #0x53]
    // 0xb2430c: r24 = Instance_SmartQuotesType
    //     0xb2430c: add             x24, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb24310: ldr             x24, [x24, #0x4e0]
    // 0xb24314: StoreField: r1->field_57 = r24
    //     0xb24314: stur            w24, [x1, #0x57]
    // 0xb24318: r25 = Instance_TextInputType
    //     0xb24318: add             x25, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb2431c: ldr             x25, [x25, #0x7a0]
    // 0xb24320: StoreField: r1->field_1f = r25
    //     0xb24320: stur            w25, [x1, #0x1f]
    // 0xb24324: StoreField: r1->field_cb = r8
    //     0xb24324: stur            w8, [x1, #0xcb]
    // 0xb24328: r0 = UniqueKey()
    //     0xb24328: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xb2432c: mov             x1, x0
    // 0xb24330: ldur            x0, [fp, #-0x28]
    // 0xb24334: StoreField: r0->field_7 = r1
    //     0xb24334: stur            w1, [x0, #7]
    // 0xb24338: r1 = Null
    //     0xb24338: mov             x1, NULL
    // 0xb2433c: r2 = 8
    //     0xb2433c: movz            x2, #0x8
    // 0xb24340: r0 = AllocateArray()
    //     0xb24340: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb24344: mov             x2, x0
    // 0xb24348: ldur            x0, [fp, #-0x18]
    // 0xb2434c: stur            x2, [fp, #-0x10]
    // 0xb24350: StoreField: r2->field_f = r0
    //     0xb24350: stur            w0, [x2, #0xf]
    // 0xb24354: r16 = Instance_SizedBox
    //     0xb24354: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb24358: ldr             x16, [x16, #0x4a0]
    // 0xb2435c: StoreField: r2->field_13 = r16
    //     0xb2435c: stur            w16, [x2, #0x13]
    // 0xb24360: ldur            x0, [fp, #-0x28]
    // 0xb24364: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24364: stur            w0, [x2, #0x17]
    // 0xb24368: r16 = Instance_SizedBox
    //     0xb24368: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb2436c: ldr             x16, [x16, #0x7a8]
    // 0xb24370: StoreField: r2->field_1b = r16
    //     0xb24370: stur            w16, [x2, #0x1b]
    // 0xb24374: r1 = <Widget>
    //     0xb24374: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb24378: r0 = AllocateGrowableArray()
    //     0xb24378: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb2437c: mov             x2, x0
    // 0xb24380: ldur            x0, [fp, #-0x10]
    // 0xb24384: stur            x2, [fp, #-0x18]
    // 0xb24388: StoreField: r2->field_f = r0
    //     0xb24388: stur            w0, [x2, #0xf]
    // 0xb2438c: r1 = 8
    //     0xb2438c: movz            x1, #0x8
    // 0xb24390: StoreField: r2->field_b = r1
    //     0xb24390: stur            w1, [x2, #0xb]
    // 0xb24394: ldur            x0, [fp, #-8]
    // 0xb24398: LoadField: r1 = r0->field_f
    //     0xb24398: ldur            w1, [x0, #0xf]
    // 0xb2439c: DecompressPointer r1
    //     0xb2439c: add             x1, x1, HEAP, lsl #32
    // 0xb243a0: r0 = controller()
    //     0xb243a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb243a4: LoadField: r1 = r0->field_67
    //     0xb243a4: ldur            w1, [x0, #0x67]
    // 0xb243a8: DecompressPointer r1
    //     0xb243a8: add             x1, x1, HEAP, lsl #32
    // 0xb243ac: r0 = value()
    //     0xb243ac: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb243b0: cmp             w0, NULL
    // 0xb243b4: b.eq            #0xb24520
    // 0xb243b8: ldur            x2, [fp, #-8]
    // 0xb243bc: LoadField: r1 = r2->field_f
    //     0xb243bc: ldur            w1, [x2, #0xf]
    // 0xb243c0: DecompressPointer r1
    //     0xb243c0: add             x1, x1, HEAP, lsl #32
    // 0xb243c4: r0 = controller()
    //     0xb243c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb243c8: LoadField: r1 = r0->field_67
    //     0xb243c8: ldur            w1, [x0, #0x67]
    // 0xb243cc: DecompressPointer r1
    //     0xb243cc: add             x1, x1, HEAP, lsl #32
    // 0xb243d0: r0 = value()
    //     0xb243d0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb243d4: r1 = 60
    //     0xb243d4: movz            x1, #0x3c
    // 0xb243d8: branchIfSmi(r0, 0xb243e4)
    //     0xb243d8: tbz             w0, #0, #0xb243e4
    // 0xb243dc: r1 = LoadClassIdInstr(r0)
    //     0xb243dc: ldur            x1, [x0, #-1]
    //     0xb243e0: ubfx            x1, x1, #0xc, #0x14
    // 0xb243e4: str             x0, [SP]
    // 0xb243e8: mov             x0, x1
    // 0xb243ec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb243ec: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb243f0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb243f0: movz            x17, #0x2b03
    //     0xb243f4: add             lr, x0, x17
    //     0xb243f8: ldr             lr, [x21, lr, lsl #3]
    //     0xb243fc: blr             lr
    // 0xb24400: stur            x0, [fp, #-0x10]
    // 0xb24404: r0 = GetNavigation.textTheme()
    //     0xb24404: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb24408: LoadField: r3 = r0->field_27
    //     0xb24408: ldur            w3, [x0, #0x27]
    // 0xb2440c: DecompressPointer r3
    //     0xb2440c: add             x3, x3, HEAP, lsl #32
    // 0xb24410: stur            x3, [fp, #-0x20]
    // 0xb24414: cmp             w3, NULL
    // 0xb24418: b.ne            #0xb24424
    // 0xb2441c: r2 = Null
    //     0xb2441c: mov             x2, NULL
    // 0xb24420: b               #0xb2447c
    // 0xb24424: r1 = _ConstMap len:3
    //     0xb24424: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb24428: ldr             x1, [x1, #0xcd0]
    // 0xb2442c: r2 = 6
    //     0xb2442c: movz            x2, #0x6
    // 0xb24430: r0 = []()
    //     0xb24430: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb24434: r1 = _ConstMap len:3
    //     0xb24434: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb24438: ldr             x1, [x1, #0xcd0]
    // 0xb2443c: r2 = 4
    //     0xb2443c: movz            x2, #0x4
    // 0xb24440: stur            x0, [fp, #-0x28]
    // 0xb24444: r0 = []()
    //     0xb24444: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb24448: r16 = <Color?>
    //     0xb24448: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb2444c: ldr             x16, [x16, #0x98]
    // 0xb24450: stp             x0, x16, [SP, #8]
    // 0xb24454: ldur            x16, [fp, #-0x28]
    // 0xb24458: str             x16, [SP]
    // 0xb2445c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb2445c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb24460: r0 = mode()
    //     0xb24460: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb24464: str             x0, [SP]
    // 0xb24468: ldur            x1, [fp, #-0x20]
    // 0xb2446c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2446c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb24470: ldr             x4, [x4, #0x228]
    // 0xb24474: r0 = copyWith()
    //     0xb24474: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb24478: mov             x2, x0
    // 0xb2447c: ldur            x0, [fp, #-0x10]
    // 0xb24480: ldur            x1, [fp, #-0x18]
    // 0xb24484: stur            x2, [fp, #-0x20]
    // 0xb24488: r0 = Text()
    //     0xb24488: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb2448c: mov             x2, x0
    // 0xb24490: ldur            x0, [fp, #-0x10]
    // 0xb24494: stur            x2, [fp, #-0x28]
    // 0xb24498: StoreField: r2->field_b = r0
    //     0xb24498: stur            w0, [x2, #0xb]
    // 0xb2449c: ldur            x0, [fp, #-0x20]
    // 0xb244a0: StoreField: r2->field_13 = r0
    //     0xb244a0: stur            w0, [x2, #0x13]
    // 0xb244a4: ldur            x0, [fp, #-0x18]
    // 0xb244a8: LoadField: r1 = r0->field_b
    //     0xb244a8: ldur            w1, [x0, #0xb]
    // 0xb244ac: LoadField: r3 = r0->field_f
    //     0xb244ac: ldur            w3, [x0, #0xf]
    // 0xb244b0: DecompressPointer r3
    //     0xb244b0: add             x3, x3, HEAP, lsl #32
    // 0xb244b4: LoadField: r4 = r3->field_b
    //     0xb244b4: ldur            w4, [x3, #0xb]
    // 0xb244b8: r3 = LoadInt32Instr(r1)
    //     0xb244b8: sbfx            x3, x1, #1, #0x1f
    // 0xb244bc: stur            x3, [fp, #-0x50]
    // 0xb244c0: r1 = LoadInt32Instr(r4)
    //     0xb244c0: sbfx            x1, x4, #1, #0x1f
    // 0xb244c4: cmp             x3, x1
    // 0xb244c8: b.ne            #0xb244d4
    // 0xb244cc: mov             x1, x0
    // 0xb244d0: r0 = _growToNextCapacity()
    //     0xb244d0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb244d4: ldur            x2, [fp, #-0x18]
    // 0xb244d8: ldur            x3, [fp, #-0x50]
    // 0xb244dc: add             x0, x3, #1
    // 0xb244e0: lsl             x1, x0, #1
    // 0xb244e4: StoreField: r2->field_b = r1
    //     0xb244e4: stur            w1, [x2, #0xb]
    // 0xb244e8: LoadField: r1 = r2->field_f
    //     0xb244e8: ldur            w1, [x2, #0xf]
    // 0xb244ec: DecompressPointer r1
    //     0xb244ec: add             x1, x1, HEAP, lsl #32
    // 0xb244f0: ldur            x0, [fp, #-0x28]
    // 0xb244f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb244f4: add             x25, x1, x3, lsl #2
    //     0xb244f8: add             x25, x25, #0xf
    //     0xb244fc: str             w0, [x25]
    //     0xb24500: tbz             w0, #0, #0xb2451c
    //     0xb24504: ldurb           w16, [x1, #-1]
    //     0xb24508: ldurb           w17, [x0, #-1]
    //     0xb2450c: and             x16, x17, x16, lsr #2
    //     0xb24510: tst             x16, HEAP, lsr #32
    //     0xb24514: b.eq            #0xb2451c
    //     0xb24518: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb2451c: b               #0xb24524
    // 0xb24520: ldur            x2, [fp, #-0x18]
    // 0xb24524: r0 = Column()
    //     0xb24524: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb24528: mov             x1, x0
    // 0xb2452c: r0 = Instance_Axis
    //     0xb2452c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb24530: stur            x1, [fp, #-0x10]
    // 0xb24534: StoreField: r1->field_f = r0
    //     0xb24534: stur            w0, [x1, #0xf]
    // 0xb24538: r0 = Instance_MainAxisAlignment
    //     0xb24538: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb2453c: ldr             x0, [x0, #0x730]
    // 0xb24540: StoreField: r1->field_13 = r0
    //     0xb24540: stur            w0, [x1, #0x13]
    // 0xb24544: r0 = Instance_MainAxisSize
    //     0xb24544: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb24548: ldr             x0, [x0, #0x738]
    // 0xb2454c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2454c: stur            w0, [x1, #0x17]
    // 0xb24550: r0 = Instance_CrossAxisAlignment
    //     0xb24550: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb24554: ldr             x0, [x0, #0x68]
    // 0xb24558: StoreField: r1->field_1b = r0
    //     0xb24558: stur            w0, [x1, #0x1b]
    // 0xb2455c: r0 = Instance_VerticalDirection
    //     0xb2455c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb24560: ldr             x0, [x0, #0x748]
    // 0xb24564: StoreField: r1->field_23 = r0
    //     0xb24564: stur            w0, [x1, #0x23]
    // 0xb24568: r0 = Instance_Clip
    //     0xb24568: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb2456c: ldr             x0, [x0, #0x750]
    // 0xb24570: StoreField: r1->field_2b = r0
    //     0xb24570: stur            w0, [x1, #0x2b]
    // 0xb24574: StoreField: r1->field_2f = rZR
    //     0xb24574: stur            xzr, [x1, #0x2f]
    // 0xb24578: ldur            x0, [fp, #-0x18]
    // 0xb2457c: StoreField: r1->field_b = r0
    //     0xb2457c: stur            w0, [x1, #0xb]
    // 0xb24580: r0 = Padding()
    //     0xb24580: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb24584: mov             x1, x0
    // 0xb24588: r0 = Instance_EdgeInsets
    //     0xb24588: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb2458c: ldr             x0, [x0, #0x7b0]
    // 0xb24590: StoreField: r1->field_f = r0
    //     0xb24590: stur            w0, [x1, #0xf]
    // 0xb24594: ldur            x0, [fp, #-0x10]
    // 0xb24598: StoreField: r1->field_b = r0
    //     0xb24598: stur            w0, [x1, #0xb]
    // 0xb2459c: mov             x0, x1
    // 0xb245a0: LeaveFrame
    //     0xb245a0: mov             SP, fp
    //     0xb245a4: ldp             fp, lr, [SP], #0x10
    // 0xb245a8: ret
    //     0xb245a8: ret             
    // 0xb245ac: mov             x16, x24
    // 0xb245b0: mov             x24, x25
    // 0xb245b4: mov             x25, x16
    // 0xb245b8: mov             x16, x23
    // 0xb245bc: mov             x23, x24
    // 0xb245c0: mov             x24, x16
    // 0xb245c4: mov             x16, x20
    // 0xb245c8: mov             x20, x23
    // 0xb245cc: mov             x23, x16
    // 0xb245d0: mov             x16, x19
    // 0xb245d4: mov             x19, x20
    // 0xb245d8: mov             x20, x16
    // 0xb245dc: mov             x16, x14
    // 0xb245e0: mov             x14, x19
    // 0xb245e4: mov             x19, x16
    // 0xb245e8: mov             x16, x13
    // 0xb245ec: mov             x13, x14
    // 0xb245f0: mov             x14, x16
    // 0xb245f4: mov             x16, x12
    // 0xb245f8: mov             x12, x13
    // 0xb245fc: mov             x13, x16
    // 0xb24600: mov             x16, x11
    // 0xb24604: mov             x11, x12
    // 0xb24608: mov             x12, x16
    // 0xb2460c: mov             x16, x10
    // 0xb24610: mov             x10, x11
    // 0xb24614: mov             x11, x16
    // 0xb24618: mov             x16, x9
    // 0xb2461c: mov             x9, x10
    // 0xb24620: mov             x10, x16
    // 0xb24624: mov             x16, x8
    // 0xb24628: mov             x8, x9
    // 0xb2462c: mov             x9, x16
    // 0xb24630: mov             x16, x7
    // 0xb24634: mov             x7, x8
    // 0xb24638: mov             x8, x16
    // 0xb2463c: mov             x16, x6
    // 0xb24640: mov             x6, x7
    // 0xb24644: mov             x7, x16
    // 0xb24648: mov             x16, x5
    // 0xb2464c: mov             x5, x6
    // 0xb24650: mov             x6, x16
    // 0xb24654: mov             x16, x4
    // 0xb24658: mov             x4, x5
    // 0xb2465c: mov             x5, x16
    // 0xb24660: mov             x16, x3
    // 0xb24664: mov             x3, x4
    // 0xb24668: mov             x4, x16
    // 0xb2466c: mov             x16, x2
    // 0xb24670: mov             x2, x3
    // 0xb24674: mov             x3, x16
    // 0xb24678: mov             x0, x1
    // 0xb2467c: r1 = 8
    //     0xb2467c: movz            x1, #0x8
    // 0xb24680: r1 = Null
    //     0xb24680: mov             x1, NULL
    // 0xb24684: r2 = 10
    //     0xb24684: movz            x2, #0xa
    // 0xb24688: r0 = AllocateArray()
    //     0xb24688: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb2468c: stur            x0, [fp, #-0x10]
    // 0xb24690: r16 = "Masukkan nomor halaman ("
    //     0xb24690: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c178] "Masukkan nomor halaman ("
    //     0xb24694: ldr             x16, [x16, #0x178]
    // 0xb24698: StoreField: r0->field_f = r16
    //     0xb24698: stur            w16, [x0, #0xf]
    // 0xb2469c: ldur            x2, [fp, #-8]
    // 0xb246a0: LoadField: r1 = r2->field_f
    //     0xb246a0: ldur            w1, [x2, #0xf]
    // 0xb246a4: DecompressPointer r1
    //     0xb246a4: add             x1, x1, HEAP, lsl #32
    // 0xb246a8: r0 = controller()
    //     0xb246a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb246ac: LoadField: r1 = r0->field_5b
    //     0xb246ac: ldur            w1, [x0, #0x5b]
    // 0xb246b0: DecompressPointer r1
    //     0xb246b0: add             x1, x1, HEAP, lsl #32
    // 0xb246b4: r0 = value()
    //     0xb246b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb246b8: ldur            x1, [fp, #-0x10]
    // 0xb246bc: ArrayStore: r1[1] = r0  ; List_4
    //     0xb246bc: add             x25, x1, #0x13
    //     0xb246c0: str             w0, [x25]
    //     0xb246c4: tbz             w0, #0, #0xb246e0
    //     0xb246c8: ldurb           w16, [x1, #-1]
    //     0xb246cc: ldurb           w17, [x0, #-1]
    //     0xb246d0: and             x16, x17, x16, lsr #2
    //     0xb246d4: tst             x16, HEAP, lsr #32
    //     0xb246d8: b.eq            #0xb246e0
    //     0xb246dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb246e0: ldur            x0, [fp, #-0x10]
    // 0xb246e4: r16 = " - "
    //     0xb246e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c180] " - "
    //     0xb246e8: ldr             x16, [x16, #0x180]
    // 0xb246ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xb246ec: stur            w16, [x0, #0x17]
    // 0xb246f0: ldur            x2, [fp, #-8]
    // 0xb246f4: LoadField: r1 = r2->field_f
    //     0xb246f4: ldur            w1, [x2, #0xf]
    // 0xb246f8: DecompressPointer r1
    //     0xb246f8: add             x1, x1, HEAP, lsl #32
    // 0xb246fc: r0 = controller()
    //     0xb246fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24700: LoadField: r1 = r0->field_5b
    //     0xb24700: ldur            w1, [x0, #0x5b]
    // 0xb24704: DecompressPointer r1
    //     0xb24704: add             x1, x1, HEAP, lsl #32
    // 0xb24708: r0 = value()
    //     0xb24708: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb2470c: r1 = LoadInt32Instr(r0)
    //     0xb2470c: sbfx            x1, x0, #1, #0x1f
    //     0xb24710: tbz             w0, #0, #0xb24718
    //     0xb24714: ldur            x1, [x0, #7]
    // 0xb24718: add             x2, x1, #0x25b
    // 0xb2471c: r0 = BoxInt64Instr(r2)
    //     0xb2471c: sbfiz           x0, x2, #1, #0x1f
    //     0xb24720: cmp             x2, x0, asr #1
    //     0xb24724: b.eq            #0xb24730
    //     0xb24728: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2472c: stur            x2, [x0, #7]
    // 0xb24730: ldur            x1, [fp, #-0x10]
    // 0xb24734: ArrayStore: r1[3] = r0  ; List_4
    //     0xb24734: add             x25, x1, #0x1b
    //     0xb24738: str             w0, [x25]
    //     0xb2473c: tbz             w0, #0, #0xb24758
    //     0xb24740: ldurb           w16, [x1, #-1]
    //     0xb24744: ldurb           w17, [x0, #-1]
    //     0xb24748: and             x16, x17, x16, lsr #2
    //     0xb2474c: tst             x16, HEAP, lsr #32
    //     0xb24750: b.eq            #0xb24758
    //     0xb24754: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb24758: ldur            x0, [fp, #-0x10]
    // 0xb2475c: r16 = ")"
    //     0xb2475c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xb24760: StoreField: r0->field_1f = r16
    //     0xb24760: stur            w16, [x0, #0x1f]
    // 0xb24764: str             x0, [SP]
    // 0xb24768: r0 = _interpolate()
    //     0xb24768: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb2476c: stur            x0, [fp, #-0x10]
    // 0xb24770: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb24770: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb24774: ldr             x0, [x0, #0x2670]
    //     0xb24778: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2477c: cmp             w0, w16
    //     0xb24780: b.ne            #0xb2478c
    //     0xb24784: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb24788: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb2478c: r0 = GetNavigation.textTheme()
    //     0xb2478c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb24790: LoadField: r1 = r0->field_27
    //     0xb24790: ldur            w1, [x0, #0x27]
    // 0xb24794: DecompressPointer r1
    //     0xb24794: add             x1, x1, HEAP, lsl #32
    // 0xb24798: cmp             w1, NULL
    // 0xb2479c: b.ne            #0xb247a8
    // 0xb247a0: r2 = Null
    //     0xb247a0: mov             x2, NULL
    // 0xb247a4: b               #0xb247c4
    // 0xb247a8: r16 = 14.000000
    //     0xb247a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb247ac: ldr             x16, [x16, #0x9a0]
    // 0xb247b0: str             x16, [SP]
    // 0xb247b4: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb247b4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb247b8: ldr             x4, [x4, #0x88]
    // 0xb247bc: r0 = copyWith()
    //     0xb247bc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb247c0: mov             x2, x0
    // 0xb247c4: ldur            x1, [fp, #-8]
    // 0xb247c8: ldur            x0, [fp, #-0x10]
    // 0xb247cc: stur            x2, [fp, #-0x18]
    // 0xb247d0: r0 = Text()
    //     0xb247d0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb247d4: mov             x2, x0
    // 0xb247d8: ldur            x0, [fp, #-0x10]
    // 0xb247dc: stur            x2, [fp, #-0x20]
    // 0xb247e0: StoreField: r2->field_b = r0
    //     0xb247e0: stur            w0, [x2, #0xb]
    // 0xb247e4: ldur            x0, [fp, #-0x18]
    // 0xb247e8: StoreField: r2->field_13 = r0
    //     0xb247e8: stur            w0, [x2, #0x13]
    // 0xb247ec: ldur            x0, [fp, #-8]
    // 0xb247f0: LoadField: r1 = r0->field_f
    //     0xb247f0: ldur            w1, [x0, #0xf]
    // 0xb247f4: DecompressPointer r1
    //     0xb247f4: add             x1, x1, HEAP, lsl #32
    // 0xb247f8: r0 = controller()
    //     0xb247f8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb247fc: LoadField: r1 = r0->field_73
    //     0xb247fc: ldur            w1, [x0, #0x73]
    // 0xb24800: DecompressPointer r1
    //     0xb24800: add             x1, x1, HEAP, lsl #32
    // 0xb24804: stur            x1, [fp, #-0x10]
    // 0xb24808: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb24808: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2480c: ldr             x0, [x0, #0xdd8]
    //     0xb24810: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb24814: cmp             w0, w16
    //     0xb24818: b.ne            #0xb24828
    //     0xb2481c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb24820: ldr             x2, [x2, #0x780]
    //     0xb24824: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb24828: stur            x0, [fp, #-0x18]
    // 0xb2482c: r0 = LengthLimitingTextInputFormatter()
    //     0xb2482c: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb24830: mov             x3, x0
    // 0xb24834: r0 = 6
    //     0xb24834: movz            x0, #0x6
    // 0xb24838: stur            x3, [fp, #-0x28]
    // 0xb2483c: StoreField: r3->field_7 = r0
    //     0xb2483c: stur            w0, [x3, #7]
    // 0xb24840: r1 = Null
    //     0xb24840: mov             x1, NULL
    // 0xb24844: r2 = 4
    //     0xb24844: movz            x2, #0x4
    // 0xb24848: r0 = AllocateArray()
    //     0xb24848: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb2484c: mov             x2, x0
    // 0xb24850: ldur            x0, [fp, #-0x18]
    // 0xb24854: stur            x2, [fp, #-0x30]
    // 0xb24858: StoreField: r2->field_f = r0
    //     0xb24858: stur            w0, [x2, #0xf]
    // 0xb2485c: ldur            x0, [fp, #-0x28]
    // 0xb24860: StoreField: r2->field_13 = r0
    //     0xb24860: stur            w0, [x2, #0x13]
    // 0xb24864: r1 = <TextInputFormatter>
    //     0xb24864: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb24868: ldr             x1, [x1, #0x788]
    // 0xb2486c: r0 = AllocateGrowableArray()
    //     0xb2486c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb24870: mov             x2, x0
    // 0xb24874: ldur            x0, [fp, #-0x30]
    // 0xb24878: stur            x2, [fp, #-0x18]
    // 0xb2487c: StoreField: r2->field_f = r0
    //     0xb2487c: stur            w0, [x2, #0xf]
    // 0xb24880: r0 = 4
    //     0xb24880: movz            x0, #0x4
    // 0xb24884: StoreField: r2->field_b = r0
    //     0xb24884: stur            w0, [x2, #0xb]
    // 0xb24888: ldur            x3, [fp, #-8]
    // 0xb2488c: LoadField: r1 = r3->field_f
    //     0xb2488c: ldur            w1, [x3, #0xf]
    // 0xb24890: DecompressPointer r1
    //     0xb24890: add             x1, x1, HEAP, lsl #32
    // 0xb24894: r0 = controller()
    //     0xb24894: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24898: LoadField: r1 = r0->field_5b
    //     0xb24898: ldur            w1, [x0, #0x5b]
    // 0xb2489c: DecompressPointer r1
    //     0xb2489c: add             x1, x1, HEAP, lsl #32
    // 0xb248a0: r0 = value()
    //     0xb248a0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb248a4: r1 = Null
    //     0xb248a4: mov             x1, NULL
    // 0xb248a8: r2 = 6
    //     0xb248a8: movz            x2, #0x6
    // 0xb248ac: stur            x0, [fp, #-0x28]
    // 0xb248b0: r0 = AllocateArray()
    //     0xb248b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb248b4: mov             x2, x0
    // 0xb248b8: ldur            x0, [fp, #-0x28]
    // 0xb248bc: stur            x2, [fp, #-0x30]
    // 0xb248c0: StoreField: r2->field_f = r0
    //     0xb248c0: stur            w0, [x2, #0xf]
    // 0xb248c4: r16 = " - "
    //     0xb248c4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c180] " - "
    //     0xb248c8: ldr             x16, [x16, #0x180]
    // 0xb248cc: StoreField: r2->field_13 = r16
    //     0xb248cc: stur            w16, [x2, #0x13]
    // 0xb248d0: ldur            x0, [fp, #-8]
    // 0xb248d4: LoadField: r1 = r0->field_f
    //     0xb248d4: ldur            w1, [x0, #0xf]
    // 0xb248d8: DecompressPointer r1
    //     0xb248d8: add             x1, x1, HEAP, lsl #32
    // 0xb248dc: r0 = controller()
    //     0xb248dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb248e0: LoadField: r1 = r0->field_5b
    //     0xb248e0: ldur            w1, [x0, #0x5b]
    // 0xb248e4: DecompressPointer r1
    //     0xb248e4: add             x1, x1, HEAP, lsl #32
    // 0xb248e8: r0 = value()
    //     0xb248e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb248ec: r1 = LoadInt32Instr(r0)
    //     0xb248ec: sbfx            x1, x0, #1, #0x1f
    //     0xb248f0: tbz             w0, #0, #0xb248f8
    //     0xb248f4: ldur            x1, [x0, #7]
    // 0xb248f8: add             x2, x1, #0x25b
    // 0xb248fc: r0 = BoxInt64Instr(r2)
    //     0xb248fc: sbfiz           x0, x2, #1, #0x1f
    //     0xb24900: cmp             x2, x0, asr #1
    //     0xb24904: b.eq            #0xb24910
    //     0xb24908: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2490c: stur            x2, [x0, #7]
    // 0xb24910: ldur            x1, [fp, #-0x30]
    // 0xb24914: ArrayStore: r1[2] = r0  ; List_4
    //     0xb24914: add             x25, x1, #0x17
    //     0xb24918: str             w0, [x25]
    //     0xb2491c: tbz             w0, #0, #0xb24938
    //     0xb24920: ldurb           w16, [x1, #-1]
    //     0xb24924: ldurb           w17, [x0, #-1]
    //     0xb24928: and             x16, x17, x16, lsr #2
    //     0xb2492c: tst             x16, HEAP, lsr #32
    //     0xb24930: b.eq            #0xb24938
    //     0xb24934: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb24938: ldur            x16, [fp, #-0x30]
    // 0xb2493c: str             x16, [SP]
    // 0xb24940: r0 = _interpolate()
    //     0xb24940: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb24944: mov             x2, x0
    // 0xb24948: ldur            x0, [fp, #-8]
    // 0xb2494c: stur            x2, [fp, #-0x28]
    // 0xb24950: LoadField: r1 = r0->field_f
    //     0xb24950: ldur            w1, [x0, #0xf]
    // 0xb24954: DecompressPointer r1
    //     0xb24954: add             x1, x1, HEAP, lsl #32
    // 0xb24958: r0 = controller()
    //     0xb24958: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2495c: LoadField: r1 = r0->field_63
    //     0xb2495c: ldur            w1, [x0, #0x63]
    // 0xb24960: DecompressPointer r1
    //     0xb24960: add             x1, x1, HEAP, lsl #32
    // 0xb24964: r0 = value()
    //     0xb24964: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb24968: cmp             w0, NULL
    // 0xb2496c: b.eq            #0xb24978
    // 0xb24970: r5 = ""
    //     0xb24970: ldr             x5, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb24974: b               #0xb2497c
    // 0xb24978: r5 = Null
    //     0xb24978: mov             x5, NULL
    // 0xb2497c: ldur            x0, [fp, #-8]
    // 0xb24980: ldur            x4, [fp, #-0x20]
    // 0xb24984: ldur            x3, [fp, #-0x10]
    // 0xb24988: ldur            x1, [fp, #-0x28]
    // 0xb2498c: ldur            x2, [fp, #-0x18]
    // 0xb24990: stur            x5, [fp, #-0x30]
    // 0xb24994: r0 = InputDecoration()
    //     0xb24994: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb24998: mov             x1, x0
    // 0xb2499c: ldur            x0, [fp, #-0x28]
    // 0xb249a0: stur            x1, [fp, #-0x38]
    // 0xb249a4: StoreField: r1->field_2f = r0
    //     0xb249a4: stur            w0, [x1, #0x2f]
    // 0xb249a8: r0 = true
    //     0xb249a8: add             x0, NULL, #0x20  ; true
    // 0xb249ac: StoreField: r1->field_43 = r0
    //     0xb249ac: stur            w0, [x1, #0x43]
    // 0xb249b0: ldur            x2, [fp, #-0x30]
    // 0xb249b4: StoreField: r1->field_4b = r2
    //     0xb249b4: stur            w2, [x1, #0x4b]
    // 0xb249b8: r2 = Instance_TextStyle
    //     0xb249b8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb249bc: ldr             x2, [x2, #0x798]
    // 0xb249c0: StoreField: r1->field_4f = r2
    //     0xb249c0: stur            w2, [x1, #0x4f]
    // 0xb249c4: StoreField: r1->field_cf = r0
    //     0xb249c4: stur            w0, [x1, #0xcf]
    // 0xb249c8: r0 = TextField()
    //     0xb249c8: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb249cc: mov             x1, x0
    // 0xb249d0: r0 = EditableText
    //     0xb249d0: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb249d4: stur            x1, [fp, #-0x28]
    // 0xb249d8: StoreField: r1->field_f = r0
    //     0xb249d8: stur            w0, [x1, #0xf]
    // 0xb249dc: ldur            x0, [fp, #-0x10]
    // 0xb249e0: StoreField: r1->field_13 = r0
    //     0xb249e0: stur            w0, [x1, #0x13]
    // 0xb249e4: ldur            x0, [fp, #-0x38]
    // 0xb249e8: StoreField: r1->field_1b = r0
    //     0xb249e8: stur            w0, [x1, #0x1b]
    // 0xb249ec: r0 = Instance_TextCapitalization
    //     0xb249ec: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb249f0: StoreField: r1->field_27 = r0
    //     0xb249f0: stur            w0, [x1, #0x27]
    // 0xb249f4: r0 = Instance_TextAlign
    //     0xb249f4: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb249f8: StoreField: r1->field_33 = r0
    //     0xb249f8: stur            w0, [x1, #0x33]
    // 0xb249fc: r0 = false
    //     0xb249fc: add             x0, NULL, #0x30  ; false
    // 0xb24a00: StoreField: r1->field_6f = r0
    //     0xb24a00: stur            w0, [x1, #0x6f]
    // 0xb24a04: StoreField: r1->field_3f = r0
    //     0xb24a04: stur            w0, [x1, #0x3f]
    // 0xb24a08: r2 = "•"
    //     0xb24a08: add             x2, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb24a0c: ldr             x2, [x2, #0x4c8]
    // 0xb24a10: StoreField: r1->field_47 = r2
    //     0xb24a10: stur            w2, [x1, #0x47]
    // 0xb24a14: StoreField: r1->field_4b = r0
    //     0xb24a14: stur            w0, [x1, #0x4b]
    // 0xb24a18: r2 = true
    //     0xb24a18: add             x2, NULL, #0x20  ; true
    // 0xb24a1c: StoreField: r1->field_4f = r2
    //     0xb24a1c: stur            w2, [x1, #0x4f]
    // 0xb24a20: StoreField: r1->field_5b = r2
    //     0xb24a20: stur            w2, [x1, #0x5b]
    // 0xb24a24: r3 = 1
    //     0xb24a24: movz            x3, #0x1
    // 0xb24a28: StoreField: r1->field_5f = r3
    //     0xb24a28: stur            x3, [x1, #0x5f]
    // 0xb24a2c: StoreField: r1->field_6b = r0
    //     0xb24a2c: stur            w0, [x1, #0x6b]
    // 0xb24a30: ldur            x3, [fp, #-0x18]
    // 0xb24a34: StoreField: r1->field_93 = r3
    //     0xb24a34: stur            w3, [x1, #0x93]
    // 0xb24a38: d0 = 2.000000
    //     0xb24a38: fmov            d0, #2.00000000
    // 0xb24a3c: StoreField: r1->field_9f = d0
    //     0xb24a3c: stur            d0, [x1, #0x9f]
    // 0xb24a40: r3 = Instance_BoxHeightStyle
    //     0xb24a40: ldr             x3, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb24a44: StoreField: r1->field_bb = r3
    //     0xb24a44: stur            w3, [x1, #0xbb]
    // 0xb24a48: r3 = Instance_BoxWidthStyle
    //     0xb24a48: ldr             x3, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb24a4c: StoreField: r1->field_bf = r3
    //     0xb24a4c: stur            w3, [x1, #0xbf]
    // 0xb24a50: r3 = Instance_EdgeInsets
    //     0xb24a50: ldr             x3, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb24a54: StoreField: r1->field_c7 = r3
    //     0xb24a54: stur            w3, [x1, #0xc7]
    // 0xb24a58: r3 = Instance_DragStartBehavior
    //     0xb24a58: ldr             x3, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb24a5c: StoreField: r1->field_d3 = r3
    //     0xb24a5c: stur            w3, [x1, #0xd3]
    // 0xb24a60: StoreField: r1->field_db = r0
    //     0xb24a60: stur            w0, [x1, #0xdb]
    // 0xb24a64: r0 = const []
    //     0xb24a64: ldr             x0, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb24a68: StoreField: r1->field_f3 = r0
    //     0xb24a68: stur            w0, [x1, #0xf3]
    // 0xb24a6c: r0 = Instance_Clip
    //     0xb24a6c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb24a70: ldr             x0, [x0, #0x7c0]
    // 0xb24a74: StoreField: r1->field_f7 = r0
    //     0xb24a74: stur            w0, [x1, #0xf7]
    // 0xb24a78: StoreField: r1->field_ff = r2
    //     0xb24a78: stur            w2, [x1, #0xff]
    // 0xb24a7c: r17 = 259
    //     0xb24a7c: movz            x17, #0x103
    // 0xb24a80: str             w2, [x1, x17]
    // 0xb24a84: r0 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb24a84: add             x0, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb24a88: ldr             x0, [x0, #0x4d8]
    // 0xb24a8c: r17 = 267
    //     0xb24a8c: movz            x17, #0x10b
    // 0xb24a90: str             w0, [x1, x17]
    // 0xb24a94: r17 = 271
    //     0xb24a94: movz            x17, #0x10f
    // 0xb24a98: str             w2, [x1, x17]
    // 0xb24a9c: r0 = Instance_SmartDashesType
    //     0xb24a9c: ldr             x0, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb24aa0: StoreField: r1->field_53 = r0
    //     0xb24aa0: stur            w0, [x1, #0x53]
    // 0xb24aa4: r0 = Instance_SmartQuotesType
    //     0xb24aa4: add             x0, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb24aa8: ldr             x0, [x0, #0x4e0]
    // 0xb24aac: StoreField: r1->field_57 = r0
    //     0xb24aac: stur            w0, [x1, #0x57]
    // 0xb24ab0: r0 = Instance_TextInputType
    //     0xb24ab0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb24ab4: ldr             x0, [x0, #0x7a0]
    // 0xb24ab8: StoreField: r1->field_1f = r0
    //     0xb24ab8: stur            w0, [x1, #0x1f]
    // 0xb24abc: StoreField: r1->field_cb = r2
    //     0xb24abc: stur            w2, [x1, #0xcb]
    // 0xb24ac0: r0 = UniqueKey()
    //     0xb24ac0: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xb24ac4: mov             x1, x0
    // 0xb24ac8: ldur            x0, [fp, #-0x28]
    // 0xb24acc: StoreField: r0->field_7 = r1
    //     0xb24acc: stur            w1, [x0, #7]
    // 0xb24ad0: r1 = Null
    //     0xb24ad0: mov             x1, NULL
    // 0xb24ad4: r2 = 8
    //     0xb24ad4: movz            x2, #0x8
    // 0xb24ad8: r0 = AllocateArray()
    //     0xb24ad8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb24adc: mov             x2, x0
    // 0xb24ae0: ldur            x0, [fp, #-0x20]
    // 0xb24ae4: stur            x2, [fp, #-0x10]
    // 0xb24ae8: StoreField: r2->field_f = r0
    //     0xb24ae8: stur            w0, [x2, #0xf]
    // 0xb24aec: r16 = Instance_SizedBox
    //     0xb24aec: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb24af0: ldr             x16, [x16, #0x4a0]
    // 0xb24af4: StoreField: r2->field_13 = r16
    //     0xb24af4: stur            w16, [x2, #0x13]
    // 0xb24af8: ldur            x0, [fp, #-0x28]
    // 0xb24afc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24afc: stur            w0, [x2, #0x17]
    // 0xb24b00: r16 = Instance_SizedBox
    //     0xb24b00: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb24b04: ldr             x16, [x16, #0x7a8]
    // 0xb24b08: StoreField: r2->field_1b = r16
    //     0xb24b08: stur            w16, [x2, #0x1b]
    // 0xb24b0c: r1 = <Widget>
    //     0xb24b0c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb24b10: r0 = AllocateGrowableArray()
    //     0xb24b10: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb24b14: mov             x2, x0
    // 0xb24b18: ldur            x0, [fp, #-0x10]
    // 0xb24b1c: stur            x2, [fp, #-0x18]
    // 0xb24b20: StoreField: r2->field_f = r0
    //     0xb24b20: stur            w0, [x2, #0xf]
    // 0xb24b24: r0 = 8
    //     0xb24b24: movz            x0, #0x8
    // 0xb24b28: StoreField: r2->field_b = r0
    //     0xb24b28: stur            w0, [x2, #0xb]
    // 0xb24b2c: ldur            x0, [fp, #-8]
    // 0xb24b30: LoadField: r1 = r0->field_f
    //     0xb24b30: ldur            w1, [x0, #0xf]
    // 0xb24b34: DecompressPointer r1
    //     0xb24b34: add             x1, x1, HEAP, lsl #32
    // 0xb24b38: r0 = controller()
    //     0xb24b38: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24b3c: LoadField: r1 = r0->field_63
    //     0xb24b3c: ldur            w1, [x0, #0x63]
    // 0xb24b40: DecompressPointer r1
    //     0xb24b40: add             x1, x1, HEAP, lsl #32
    // 0xb24b44: r0 = value()
    //     0xb24b44: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb24b48: cmp             w0, NULL
    // 0xb24b4c: b.eq            #0xb24cb8
    // 0xb24b50: ldur            x0, [fp, #-8]
    // 0xb24b54: LoadField: r1 = r0->field_f
    //     0xb24b54: ldur            w1, [x0, #0xf]
    // 0xb24b58: DecompressPointer r1
    //     0xb24b58: add             x1, x1, HEAP, lsl #32
    // 0xb24b5c: r0 = controller()
    //     0xb24b5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24b60: LoadField: r1 = r0->field_63
    //     0xb24b60: ldur            w1, [x0, #0x63]
    // 0xb24b64: DecompressPointer r1
    //     0xb24b64: add             x1, x1, HEAP, lsl #32
    // 0xb24b68: r0 = value()
    //     0xb24b68: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb24b6c: r1 = 60
    //     0xb24b6c: movz            x1, #0x3c
    // 0xb24b70: branchIfSmi(r0, 0xb24b7c)
    //     0xb24b70: tbz             w0, #0, #0xb24b7c
    // 0xb24b74: r1 = LoadClassIdInstr(r0)
    //     0xb24b74: ldur            x1, [x0, #-1]
    //     0xb24b78: ubfx            x1, x1, #0xc, #0x14
    // 0xb24b7c: str             x0, [SP]
    // 0xb24b80: mov             x0, x1
    // 0xb24b84: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb24b84: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb24b88: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb24b88: movz            x17, #0x2b03
    //     0xb24b8c: add             lr, x0, x17
    //     0xb24b90: ldr             lr, [x21, lr, lsl #3]
    //     0xb24b94: blr             lr
    // 0xb24b98: stur            x0, [fp, #-8]
    // 0xb24b9c: r0 = GetNavigation.textTheme()
    //     0xb24b9c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb24ba0: LoadField: r3 = r0->field_27
    //     0xb24ba0: ldur            w3, [x0, #0x27]
    // 0xb24ba4: DecompressPointer r3
    //     0xb24ba4: add             x3, x3, HEAP, lsl #32
    // 0xb24ba8: stur            x3, [fp, #-0x10]
    // 0xb24bac: cmp             w3, NULL
    // 0xb24bb0: b.ne            #0xb24bbc
    // 0xb24bb4: r2 = Null
    //     0xb24bb4: mov             x2, NULL
    // 0xb24bb8: b               #0xb24c14
    // 0xb24bbc: r1 = _ConstMap len:3
    //     0xb24bbc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb24bc0: ldr             x1, [x1, #0xcd0]
    // 0xb24bc4: r2 = 6
    //     0xb24bc4: movz            x2, #0x6
    // 0xb24bc8: r0 = []()
    //     0xb24bc8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb24bcc: r1 = _ConstMap len:3
    //     0xb24bcc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb24bd0: ldr             x1, [x1, #0xcd0]
    // 0xb24bd4: r2 = 4
    //     0xb24bd4: movz            x2, #0x4
    // 0xb24bd8: stur            x0, [fp, #-0x20]
    // 0xb24bdc: r0 = []()
    //     0xb24bdc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb24be0: r16 = <Color?>
    //     0xb24be0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb24be4: ldr             x16, [x16, #0x98]
    // 0xb24be8: stp             x0, x16, [SP, #8]
    // 0xb24bec: ldur            x16, [fp, #-0x20]
    // 0xb24bf0: str             x16, [SP]
    // 0xb24bf4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb24bf4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb24bf8: r0 = mode()
    //     0xb24bf8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb24bfc: str             x0, [SP]
    // 0xb24c00: ldur            x1, [fp, #-0x10]
    // 0xb24c04: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb24c04: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb24c08: ldr             x4, [x4, #0x228]
    // 0xb24c0c: r0 = copyWith()
    //     0xb24c0c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb24c10: mov             x2, x0
    // 0xb24c14: ldur            x0, [fp, #-8]
    // 0xb24c18: ldur            x1, [fp, #-0x18]
    // 0xb24c1c: stur            x2, [fp, #-0x10]
    // 0xb24c20: r0 = Text()
    //     0xb24c20: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb24c24: mov             x2, x0
    // 0xb24c28: ldur            x0, [fp, #-8]
    // 0xb24c2c: stur            x2, [fp, #-0x20]
    // 0xb24c30: StoreField: r2->field_b = r0
    //     0xb24c30: stur            w0, [x2, #0xb]
    // 0xb24c34: ldur            x0, [fp, #-0x10]
    // 0xb24c38: StoreField: r2->field_13 = r0
    //     0xb24c38: stur            w0, [x2, #0x13]
    // 0xb24c3c: ldur            x0, [fp, #-0x18]
    // 0xb24c40: LoadField: r1 = r0->field_b
    //     0xb24c40: ldur            w1, [x0, #0xb]
    // 0xb24c44: LoadField: r3 = r0->field_f
    //     0xb24c44: ldur            w3, [x0, #0xf]
    // 0xb24c48: DecompressPointer r3
    //     0xb24c48: add             x3, x3, HEAP, lsl #32
    // 0xb24c4c: LoadField: r4 = r3->field_b
    //     0xb24c4c: ldur            w4, [x3, #0xb]
    // 0xb24c50: r3 = LoadInt32Instr(r1)
    //     0xb24c50: sbfx            x3, x1, #1, #0x1f
    // 0xb24c54: stur            x3, [fp, #-0x50]
    // 0xb24c58: r1 = LoadInt32Instr(r4)
    //     0xb24c58: sbfx            x1, x4, #1, #0x1f
    // 0xb24c5c: cmp             x3, x1
    // 0xb24c60: b.ne            #0xb24c6c
    // 0xb24c64: mov             x1, x0
    // 0xb24c68: r0 = _growToNextCapacity()
    //     0xb24c68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb24c6c: ldur            x2, [fp, #-0x18]
    // 0xb24c70: ldur            x3, [fp, #-0x50]
    // 0xb24c74: add             x0, x3, #1
    // 0xb24c78: lsl             x1, x0, #1
    // 0xb24c7c: StoreField: r2->field_b = r1
    //     0xb24c7c: stur            w1, [x2, #0xb]
    // 0xb24c80: LoadField: r1 = r2->field_f
    //     0xb24c80: ldur            w1, [x2, #0xf]
    // 0xb24c84: DecompressPointer r1
    //     0xb24c84: add             x1, x1, HEAP, lsl #32
    // 0xb24c88: ldur            x0, [fp, #-0x20]
    // 0xb24c8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24c8c: add             x25, x1, x3, lsl #2
    //     0xb24c90: add             x25, x25, #0xf
    //     0xb24c94: str             w0, [x25]
    //     0xb24c98: tbz             w0, #0, #0xb24cb4
    //     0xb24c9c: ldurb           w16, [x1, #-1]
    //     0xb24ca0: ldurb           w17, [x0, #-1]
    //     0xb24ca4: and             x16, x17, x16, lsr #2
    //     0xb24ca8: tst             x16, HEAP, lsr #32
    //     0xb24cac: b.eq            #0xb24cb4
    //     0xb24cb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb24cb4: b               #0xb24cbc
    // 0xb24cb8: ldur            x2, [fp, #-0x18]
    // 0xb24cbc: r0 = Column()
    //     0xb24cbc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb24cc0: mov             x1, x0
    // 0xb24cc4: r0 = Instance_Axis
    //     0xb24cc4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb24cc8: stur            x1, [fp, #-8]
    // 0xb24ccc: StoreField: r1->field_f = r0
    //     0xb24ccc: stur            w0, [x1, #0xf]
    // 0xb24cd0: r0 = Instance_MainAxisAlignment
    //     0xb24cd0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb24cd4: ldr             x0, [x0, #0x730]
    // 0xb24cd8: StoreField: r1->field_13 = r0
    //     0xb24cd8: stur            w0, [x1, #0x13]
    // 0xb24cdc: r0 = Instance_MainAxisSize
    //     0xb24cdc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb24ce0: ldr             x0, [x0, #0x738]
    // 0xb24ce4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb24ce4: stur            w0, [x1, #0x17]
    // 0xb24ce8: r0 = Instance_CrossAxisAlignment
    //     0xb24ce8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb24cec: ldr             x0, [x0, #0x68]
    // 0xb24cf0: StoreField: r1->field_1b = r0
    //     0xb24cf0: stur            w0, [x1, #0x1b]
    // 0xb24cf4: r0 = Instance_VerticalDirection
    //     0xb24cf4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb24cf8: ldr             x0, [x0, #0x748]
    // 0xb24cfc: StoreField: r1->field_23 = r0
    //     0xb24cfc: stur            w0, [x1, #0x23]
    // 0xb24d00: r0 = Instance_Clip
    //     0xb24d00: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb24d04: ldr             x0, [x0, #0x750]
    // 0xb24d08: StoreField: r1->field_2b = r0
    //     0xb24d08: stur            w0, [x1, #0x2b]
    // 0xb24d0c: StoreField: r1->field_2f = rZR
    //     0xb24d0c: stur            xzr, [x1, #0x2f]
    // 0xb24d10: ldur            x0, [fp, #-0x18]
    // 0xb24d14: StoreField: r1->field_b = r0
    //     0xb24d14: stur            w0, [x1, #0xb]
    // 0xb24d18: r0 = Padding()
    //     0xb24d18: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb24d1c: r1 = Instance_EdgeInsets
    //     0xb24d1c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb24d20: ldr             x1, [x1, #0x7b0]
    // 0xb24d24: StoreField: r0->field_f = r1
    //     0xb24d24: stur            w1, [x0, #0xf]
    // 0xb24d28: ldur            x1, [fp, #-8]
    // 0xb24d2c: StoreField: r0->field_b = r1
    //     0xb24d2c: stur            w1, [x0, #0xb]
    // 0xb24d30: LeaveFrame
    //     0xb24d30: mov             SP, fp
    //     0xb24d34: ldp             fp, lr, [SP], #0x10
    // 0xb24d38: ret
    //     0xb24d38: ret             
    // 0xb24d3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb24d3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb24d40: b               #0xb23708
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb24d44, size: 0x108
    // 0xb24d44: EnterFrame
    //     0xb24d44: stp             fp, lr, [SP, #-0x10]!
    //     0xb24d48: mov             fp, SP
    // 0xb24d4c: AllocStack(0x30)
    //     0xb24d4c: sub             SP, SP, #0x30
    // 0xb24d50: SetupParameters(QuranPageView this /* r1 */)
    //     0xb24d50: stur            NULL, [fp, #-8]
    //     0xb24d54: movz            x0, #0
    //     0xb24d58: add             x1, fp, w0, sxtw #2
    //     0xb24d5c: ldr             x1, [x1, #0x10]
    //     0xb24d60: ldur            w2, [x1, #0x17]
    //     0xb24d64: add             x2, x2, HEAP, lsl #32
    //     0xb24d68: stur            x2, [fp, #-0x10]
    // 0xb24d6c: CheckStackOverflow
    //     0xb24d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb24d70: cmp             SP, x16
    //     0xb24d74: b.ls            #0xb24e44
    // 0xb24d78: InitAsync() -> Future<void?>
    //     0xb24d78: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb24d7c: bl              #0x661298  ; InitAsyncStub
    // 0xb24d80: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb24d80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb24d84: ldr             x0, [x0, #0x2670]
    //     0xb24d88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb24d8c: cmp             w0, w16
    //     0xb24d90: b.ne            #0xb24d9c
    //     0xb24d94: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb24d98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb24d9c: r16 = "/quran/quran-surah"
    //     0xb24d9c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7b8] "/quran/quran-surah"
    //     0xb24da0: ldr             x16, [x16, #0x7b8]
    // 0xb24da4: stp             x16, NULL, [SP, #8]
    // 0xb24da8: r16 = false
    //     0xb24da8: add             x16, NULL, #0x30  ; false
    // 0xb24dac: str             x16, [SP]
    // 0xb24db0: r4 = const [0x1, 0x2, 0x2, 0x1, preventDuplicates, 0x1, null]
    //     0xb24db0: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b7c0] List(7) [0x1, 0x2, 0x2, 0x1, "preventDuplicates", 0x1, Null]
    //     0xb24db4: ldr             x4, [x4, #0x7c0]
    // 0xb24db8: r0 = GetNavigation.toNamed()
    //     0xb24db8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb24dbc: mov             x1, x0
    // 0xb24dc0: stur            x1, [fp, #-0x18]
    // 0xb24dc4: r0 = Await()
    //     0xb24dc4: bl              #0x661044  ; AwaitStub
    // 0xb24dc8: stur            x0, [fp, #-0x18]
    // 0xb24dcc: cmp             w0, NULL
    // 0xb24dd0: b.eq            #0xb24e3c
    // 0xb24dd4: ldur            x1, [fp, #-0x10]
    // 0xb24dd8: LoadField: r2 = r1->field_f
    //     0xb24dd8: ldur            w2, [x1, #0xf]
    // 0xb24ddc: DecompressPointer r2
    //     0xb24ddc: add             x2, x2, HEAP, lsl #32
    // 0xb24de0: mov             x1, x2
    // 0xb24de4: r0 = controller()
    //     0xb24de4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24de8: mov             x3, x0
    // 0xb24dec: ldur            x0, [fp, #-0x18]
    // 0xb24df0: r2 = Null
    //     0xb24df0: mov             x2, NULL
    // 0xb24df4: r1 = Null
    //     0xb24df4: mov             x1, NULL
    // 0xb24df8: stur            x3, [fp, #-0x10]
    // 0xb24dfc: branchIfSmi(r0, 0xb24e24)
    //     0xb24dfc: tbz             w0, #0, #0xb24e24
    // 0xb24e00: r4 = LoadClassIdInstr(r0)
    //     0xb24e00: ldur            x4, [x0, #-1]
    //     0xb24e04: ubfx            x4, x4, #0xc, #0x14
    // 0xb24e08: sub             x4, x4, #0x3c
    // 0xb24e0c: cmp             x4, #1
    // 0xb24e10: b.ls            #0xb24e24
    // 0xb24e14: r8 = int
    //     0xb24e14: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xb24e18: r3 = Null
    //     0xb24e18: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c188] Null
    //     0xb24e1c: ldr             x3, [x3, #0x188]
    // 0xb24e20: r0 = int()
    //     0xb24e20: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xb24e24: ldur            x0, [fp, #-0x18]
    // 0xb24e28: r2 = LoadInt32Instr(r0)
    //     0xb24e28: sbfx            x2, x0, #1, #0x1f
    //     0xb24e2c: tbz             w0, #0, #0xb24e34
    //     0xb24e30: ldur            x2, [x0, #7]
    // 0xb24e34: ldur            x1, [fp, #-0x10]
    // 0xb24e38: r0 = setSelectedSurah()
    //     0xb24e38: bl              #0x8c26bc  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::setSelectedSurah
    // 0xb24e3c: r0 = Null
    //     0xb24e3c: mov             x0, NULL
    // 0xb24e40: r0 = ReturnAsyncNotFuture()
    //     0xb24e40: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb24e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb24e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb24e48: b               #0xb24d78
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb24e4c, size: 0xb0
    // 0xb24e4c: EnterFrame
    //     0xb24e4c: stp             fp, lr, [SP, #-0x10]!
    //     0xb24e50: mov             fp, SP
    // 0xb24e54: AllocStack(0x20)
    //     0xb24e54: sub             SP, SP, #0x20
    // 0xb24e58: SetupParameters()
    //     0xb24e58: ldr             x0, [fp, #0x10]
    //     0xb24e5c: ldur            w1, [x0, #0x17]
    //     0xb24e60: add             x1, x1, HEAP, lsl #32
    //     0xb24e64: stur            x1, [fp, #-8]
    // 0xb24e68: CheckStackOverflow
    //     0xb24e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb24e6c: cmp             SP, x16
    //     0xb24e70: b.ls            #0xb24ef4
    // 0xb24e74: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb24e74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb24e78: ldr             x0, [x0, #0x2670]
    //     0xb24e7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb24e80: cmp             w0, w16
    //     0xb24e84: b.ne            #0xb24e90
    //     0xb24e88: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb24e8c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb24e90: r16 = "/quran/quran-setting"
    //     0xb24e90: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1b0] "/quran/quran-setting"
    //     0xb24e94: ldr             x16, [x16, #0x1b0]
    // 0xb24e98: stp             x16, NULL, [SP]
    // 0xb24e9c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb24e9c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb24ea0: r0 = GetNavigation.toNamed()
    //     0xb24ea0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb24ea4: stur            x0, [fp, #-0x10]
    // 0xb24ea8: cmp             w0, NULL
    // 0xb24eac: b.ne            #0xb24eb8
    // 0xb24eb0: r0 = Null
    //     0xb24eb0: mov             x0, NULL
    // 0xb24eb4: b               #0xb24ee8
    // 0xb24eb8: ldur            x1, [fp, #-8]
    // 0xb24ebc: LoadField: r2 = r1->field_f
    //     0xb24ebc: ldur            w2, [x1, #0xf]
    // 0xb24ec0: DecompressPointer r2
    //     0xb24ec0: add             x2, x2, HEAP, lsl #32
    // 0xb24ec4: mov             x1, x2
    // 0xb24ec8: r0 = controller()
    //     0xb24ec8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb24ecc: mov             x2, x0
    // 0xb24ed0: r1 = Function 'updateShowTajweed':.
    //     0xb24ed0: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c1b8] AnonymousClosure: (0xb24efc), in [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::updateShowTajweed (0xb24f34)
    //     0xb24ed4: ldr             x1, [x1, #0x1b8]
    // 0xb24ed8: r0 = AllocateClosure()
    //     0xb24ed8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb24edc: ldur            x1, [fp, #-0x10]
    // 0xb24ee0: mov             x2, x0
    // 0xb24ee4: r0 = whenComplete()
    //     0xb24ee4: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0xb24ee8: LeaveFrame
    //     0xb24ee8: mov             SP, fp
    //     0xb24eec: ldp             fp, lr, [SP], #0x10
    // 0xb24ef0: ret
    //     0xb24ef0: ret             
    // 0xb24ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb24ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb24ef8: b               #0xb24e74
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb24fec, size: 0x4c4
    // 0xb24fec: EnterFrame
    //     0xb24fec: stp             fp, lr, [SP, #-0x10]!
    //     0xb24ff0: mov             fp, SP
    // 0xb24ff4: AllocStack(0x40)
    //     0xb24ff4: sub             SP, SP, #0x40
    // 0xb24ff8: SetupParameters()
    //     0xb24ff8: ldr             x0, [fp, #0x10]
    //     0xb24ffc: ldur            w2, [x0, #0x17]
    //     0xb25000: add             x2, x2, HEAP, lsl #32
    //     0xb25004: stur            x2, [fp, #-8]
    // 0xb25008: CheckStackOverflow
    //     0xb25008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2500c: cmp             SP, x16
    //     0xb25010: b.ls            #0xb2548c
    // 0xb25014: LoadField: r1 = r2->field_f
    //     0xb25014: ldur            w1, [x2, #0xf]
    // 0xb25018: DecompressPointer r1
    //     0xb25018: add             x1, x1, HEAP, lsl #32
    // 0xb2501c: r0 = controller()
    //     0xb2501c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb25020: LoadField: r1 = r0->field_2b
    //     0xb25020: ldur            w1, [x0, #0x2b]
    // 0xb25024: DecompressPointer r1
    //     0xb25024: add             x1, x1, HEAP, lsl #32
    // 0xb25028: r0 = quranListView()
    //     0xb25028: bl              #0x9030f0  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::quranListView
    // 0xb2502c: r16 = Instance_QuranListViewType
    //     0xb2502c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1c8] Obj!QuranListViewType@e303a1
    //     0xb25030: ldr             x16, [x16, #0x1c8]
    // 0xb25034: cmp             w0, w16
    // 0xb25038: b.ne            #0xb2517c
    // 0xb2503c: ldur            x0, [fp, #-8]
    // 0xb25040: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb25040: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb25044: ldr             x0, [x0, #0x2670]
    //     0xb25048: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2504c: cmp             w0, w16
    //     0xb25050: b.ne            #0xb2505c
    //     0xb25054: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb25058: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb2505c: ldur            x0, [fp, #-8]
    // 0xb25060: LoadField: r1 = r0->field_f
    //     0xb25060: ldur            w1, [x0, #0xf]
    // 0xb25064: DecompressPointer r1
    //     0xb25064: add             x1, x1, HEAP, lsl #32
    // 0xb25068: r0 = controller()
    //     0xb25068: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2506c: mov             x1, x0
    // 0xb25070: r0 = verses()
    //     0xb25070: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb25074: mov             x2, x0
    // 0xb25078: LoadField: r0 = r2->field_b
    //     0xb25078: ldur            w0, [x2, #0xb]
    // 0xb2507c: r1 = LoadInt32Instr(r0)
    //     0xb2507c: sbfx            x1, x0, #1, #0x1f
    // 0xb25080: mov             x0, x1
    // 0xb25084: r1 = 0
    //     0xb25084: movz            x1, #0
    // 0xb25088: cmp             x1, x0
    // 0xb2508c: b.hs            #0xb25494
    // 0xb25090: LoadField: r0 = r2->field_f
    //     0xb25090: ldur            w0, [x2, #0xf]
    // 0xb25094: DecompressPointer r0
    //     0xb25094: add             x0, x0, HEAP, lsl #32
    // 0xb25098: LoadField: r1 = r0->field_f
    //     0xb25098: ldur            w1, [x0, #0xf]
    // 0xb2509c: DecompressPointer r1
    //     0xb2509c: add             x1, x1, HEAP, lsl #32
    // 0xb250a0: LoadField: r0 = r1->field_33
    //     0xb250a0: ldur            x0, [x1, #0x33]
    // 0xb250a4: ldur            x2, [fp, #-8]
    // 0xb250a8: stur            x0, [fp, #-0x10]
    // 0xb250ac: LoadField: r1 = r2->field_f
    //     0xb250ac: ldur            w1, [x2, #0xf]
    // 0xb250b0: DecompressPointer r1
    //     0xb250b0: add             x1, x1, HEAP, lsl #32
    // 0xb250b4: r0 = controller()
    //     0xb250b4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb250b8: mov             x1, x0
    // 0xb250bc: r0 = verses()
    //     0xb250bc: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb250c0: mov             x2, x0
    // 0xb250c4: LoadField: r0 = r2->field_b
    //     0xb250c4: ldur            w0, [x2, #0xb]
    // 0xb250c8: r1 = LoadInt32Instr(r0)
    //     0xb250c8: sbfx            x1, x0, #1, #0x1f
    // 0xb250cc: mov             x0, x1
    // 0xb250d0: r1 = 0
    //     0xb250d0: movz            x1, #0
    // 0xb250d4: cmp             x1, x0
    // 0xb250d8: b.hs            #0xb25498
    // 0xb250dc: LoadField: r0 = r2->field_f
    //     0xb250dc: ldur            w0, [x2, #0xf]
    // 0xb250e0: DecompressPointer r0
    //     0xb250e0: add             x0, x0, HEAP, lsl #32
    // 0xb250e4: LoadField: r1 = r0->field_f
    //     0xb250e4: ldur            w1, [x0, #0xf]
    // 0xb250e8: DecompressPointer r1
    //     0xb250e8: add             x1, x1, HEAP, lsl #32
    // 0xb250ec: LoadField: r0 = r1->field_2b
    //     0xb250ec: ldur            x0, [x1, #0x2b]
    // 0xb250f0: ldur            x1, [fp, #-8]
    // 0xb250f4: stur            x0, [fp, #-0x18]
    // 0xb250f8: LoadField: r2 = r1->field_f
    //     0xb250f8: ldur            w2, [x1, #0xf]
    // 0xb250fc: DecompressPointer r2
    //     0xb250fc: add             x2, x2, HEAP, lsl #32
    // 0xb25100: mov             x1, x2
    // 0xb25104: r0 = controller()
    //     0xb25104: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb25108: mov             x1, x0
    // 0xb2510c: r0 = verses()
    //     0xb2510c: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb25110: mov             x2, x0
    // 0xb25114: LoadField: r0 = r2->field_b
    //     0xb25114: ldur            w0, [x2, #0xb]
    // 0xb25118: r1 = LoadInt32Instr(r0)
    //     0xb25118: sbfx            x1, x0, #1, #0x1f
    // 0xb2511c: mov             x0, x1
    // 0xb25120: r1 = 0
    //     0xb25120: movz            x1, #0
    // 0xb25124: cmp             x1, x0
    // 0xb25128: b.hs            #0xb2549c
    // 0xb2512c: LoadField: r0 = r2->field_f
    //     0xb2512c: ldur            w0, [x2, #0xf]
    // 0xb25130: DecompressPointer r0
    //     0xb25130: add             x0, x0, HEAP, lsl #32
    // 0xb25134: LoadField: r1 = r0->field_f
    //     0xb25134: ldur            w1, [x0, #0xf]
    // 0xb25138: DecompressPointer r1
    //     0xb25138: add             x1, x1, HEAP, lsl #32
    // 0xb2513c: LoadField: r0 = r1->field_7
    //     0xb2513c: ldur            x0, [x1, #7]
    // 0xb25140: stur            x0, [fp, #-0x20]
    // 0xb25144: r1 = <QuranController>
    //     0xb25144: add             x1, PP, #0x24, lsl #12  ; [pp+0x24c38] TypeArguments: <QuranController>
    //     0xb25148: ldr             x1, [x1, #0xc38]
    // 0xb2514c: r0 = NQuranListTypeDialog()
    //     0xb2514c: bl              #0xb254b0  ; AllocateNQuranListTypeDialogStub -> NQuranListTypeDialog (size=0x2c)
    // 0xb25150: mov             x1, x0
    // 0xb25154: ldur            x0, [fp, #-0x10]
    // 0xb25158: StoreField: r1->field_13 = r0
    //     0xb25158: stur            x0, [x1, #0x13]
    // 0xb2515c: ldur            x0, [fp, #-0x20]
    // 0xb25160: StoreField: r1->field_23 = r0
    //     0xb25160: stur            x0, [x1, #0x23]
    // 0xb25164: ldur            x0, [fp, #-0x18]
    // 0xb25168: StoreField: r1->field_1b = r0
    //     0xb25168: stur            x0, [x1, #0x1b]
    // 0xb2516c: stp             x1, NULL, [SP]
    // 0xb25170: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb25170: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb25174: r0 = ExtensionDialog.dialog()
    //     0xb25174: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xb25178: b               #0xb2547c
    // 0xb2517c: ldur            x1, [fp, #-8]
    // 0xb25180: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb25180: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb25184: ldr             x0, [x0, #0x2670]
    //     0xb25188: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2518c: cmp             w0, w16
    //     0xb25190: b.ne            #0xb2519c
    //     0xb25194: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb25198: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb2519c: ldur            x0, [fp, #-8]
    // 0xb251a0: LoadField: r1 = r0->field_f
    //     0xb251a0: ldur            w1, [x0, #0xf]
    // 0xb251a4: DecompressPointer r1
    //     0xb251a4: add             x1, x1, HEAP, lsl #32
    // 0xb251a8: r0 = controller()
    //     0xb251a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb251ac: LoadField: r1 = r0->field_2b
    //     0xb251ac: ldur            w1, [x0, #0x2b]
    // 0xb251b0: DecompressPointer r1
    //     0xb251b0: add             x1, x1, HEAP, lsl #32
    // 0xb251b4: r0 = quranListView()
    //     0xb251b4: bl              #0x9030f0  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::quranListView
    // 0xb251b8: r16 = Instance_QuranListViewType
    //     0xb251b8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1d0] Obj!QuranListViewType@e303e1
    //     0xb251bc: ldr             x16, [x16, #0x1d0]
    // 0xb251c0: cmp             w0, w16
    // 0xb251c4: b.ne            #0xb25314
    // 0xb251c8: ldur            x0, [fp, #-8]
    // 0xb251cc: r1 = Null
    //     0xb251cc: mov             x1, NULL
    // 0xb251d0: r2 = 8
    //     0xb251d0: movz            x2, #0x8
    // 0xb251d4: r0 = AllocateArray()
    //     0xb251d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb251d8: stur            x0, [fp, #-0x28]
    // 0xb251dc: r16 = "surahId"
    //     0xb251dc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f8] "surahId"
    //     0xb251e0: ldr             x16, [x16, #0xf8]
    // 0xb251e4: StoreField: r0->field_f = r16
    //     0xb251e4: stur            w16, [x0, #0xf]
    // 0xb251e8: ldur            x2, [fp, #-8]
    // 0xb251ec: LoadField: r1 = r2->field_f
    //     0xb251ec: ldur            w1, [x2, #0xf]
    // 0xb251f0: DecompressPointer r1
    //     0xb251f0: add             x1, x1, HEAP, lsl #32
    // 0xb251f4: r0 = controller()
    //     0xb251f4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb251f8: mov             x1, x0
    // 0xb251fc: r0 = verses()
    //     0xb251fc: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb25200: mov             x2, x0
    // 0xb25204: LoadField: r0 = r2->field_b
    //     0xb25204: ldur            w0, [x2, #0xb]
    // 0xb25208: r1 = LoadInt32Instr(r0)
    //     0xb25208: sbfx            x1, x0, #1, #0x1f
    // 0xb2520c: mov             x0, x1
    // 0xb25210: r1 = 0
    //     0xb25210: movz            x1, #0
    // 0xb25214: cmp             x1, x0
    // 0xb25218: b.hs            #0xb254a0
    // 0xb2521c: LoadField: r0 = r2->field_f
    //     0xb2521c: ldur            w0, [x2, #0xf]
    // 0xb25220: DecompressPointer r0
    //     0xb25220: add             x0, x0, HEAP, lsl #32
    // 0xb25224: LoadField: r1 = r0->field_f
    //     0xb25224: ldur            w1, [x0, #0xf]
    // 0xb25228: DecompressPointer r1
    //     0xb25228: add             x1, x1, HEAP, lsl #32
    // 0xb2522c: LoadField: r2 = r1->field_33
    //     0xb2522c: ldur            x2, [x1, #0x33]
    // 0xb25230: r0 = BoxInt64Instr(r2)
    //     0xb25230: sbfiz           x0, x2, #1, #0x1f
    //     0xb25234: cmp             x2, x0, asr #1
    //     0xb25238: b.eq            #0xb25244
    //     0xb2523c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb25240: stur            x2, [x0, #7]
    // 0xb25244: ldur            x1, [fp, #-0x28]
    // 0xb25248: ArrayStore: r1[1] = r0  ; List_4
    //     0xb25248: add             x25, x1, #0x13
    //     0xb2524c: str             w0, [x25]
    //     0xb25250: tbz             w0, #0, #0xb2526c
    //     0xb25254: ldurb           w16, [x1, #-1]
    //     0xb25258: ldurb           w17, [x0, #-1]
    //     0xb2525c: and             x16, x17, x16, lsr #2
    //     0xb25260: tst             x16, HEAP, lsr #32
    //     0xb25264: b.eq            #0xb2526c
    //     0xb25268: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb2526c: ldur            x0, [fp, #-0x28]
    // 0xb25270: r16 = "verseId"
    //     0xb25270: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bc70] "verseId"
    //     0xb25274: ldr             x16, [x16, #0xc70]
    // 0xb25278: ArrayStore: r0[0] = r16  ; List_4
    //     0xb25278: stur            w16, [x0, #0x17]
    // 0xb2527c: ldur            x3, [fp, #-8]
    // 0xb25280: LoadField: r1 = r3->field_f
    //     0xb25280: ldur            w1, [x3, #0xf]
    // 0xb25284: DecompressPointer r1
    //     0xb25284: add             x1, x1, HEAP, lsl #32
    // 0xb25288: r0 = controller()
    //     0xb25288: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb2528c: mov             x1, x0
    // 0xb25290: r0 = verses()
    //     0xb25290: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb25294: mov             x2, x0
    // 0xb25298: LoadField: r0 = r2->field_b
    //     0xb25298: ldur            w0, [x2, #0xb]
    // 0xb2529c: r1 = LoadInt32Instr(r0)
    //     0xb2529c: sbfx            x1, x0, #1, #0x1f
    // 0xb252a0: mov             x0, x1
    // 0xb252a4: r1 = 0
    //     0xb252a4: movz            x1, #0
    // 0xb252a8: cmp             x1, x0
    // 0xb252ac: b.hs            #0xb254a4
    // 0xb252b0: LoadField: r0 = r2->field_f
    //     0xb252b0: ldur            w0, [x2, #0xf]
    // 0xb252b4: DecompressPointer r0
    //     0xb252b4: add             x0, x0, HEAP, lsl #32
    // 0xb252b8: LoadField: r1 = r0->field_f
    //     0xb252b8: ldur            w1, [x0, #0xf]
    // 0xb252bc: DecompressPointer r1
    //     0xb252bc: add             x1, x1, HEAP, lsl #32
    // 0xb252c0: LoadField: r2 = r1->field_7
    //     0xb252c0: ldur            x2, [x1, #7]
    // 0xb252c4: r0 = BoxInt64Instr(r2)
    //     0xb252c4: sbfiz           x0, x2, #1, #0x1f
    //     0xb252c8: cmp             x2, x0, asr #1
    //     0xb252cc: b.eq            #0xb252d8
    //     0xb252d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb252d4: stur            x2, [x0, #7]
    // 0xb252d8: ldur            x1, [fp, #-0x28]
    // 0xb252dc: ArrayStore: r1[3] = r0  ; List_4
    //     0xb252dc: add             x25, x1, #0x1b
    //     0xb252e0: str             w0, [x25]
    //     0xb252e4: tbz             w0, #0, #0xb25300
    //     0xb252e8: ldurb           w16, [x1, #-1]
    //     0xb252ec: ldurb           w17, [x0, #-1]
    //     0xb252f0: and             x16, x17, x16, lsr #2
    //     0xb252f4: tst             x16, HEAP, lsr #32
    //     0xb252f8: b.eq            #0xb25300
    //     0xb252fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb25300: r16 = <String, int>
    //     0xb25300: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb25304: ldur            lr, [fp, #-0x28]
    // 0xb25308: stp             lr, x16, [SP]
    // 0xb2530c: r0 = Map._fromLiteral()
    //     0xb2530c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb25310: b               #0xb25460
    // 0xb25314: ldur            x3, [fp, #-8]
    // 0xb25318: r1 = Null
    //     0xb25318: mov             x1, NULL
    // 0xb2531c: r2 = 8
    //     0xb2531c: movz            x2, #0x8
    // 0xb25320: r0 = AllocateArray()
    //     0xb25320: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb25324: stur            x0, [fp, #-0x28]
    // 0xb25328: r16 = "juzId"
    //     0xb25328: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f0] "juzId"
    //     0xb2532c: ldr             x16, [x16, #0xf0]
    // 0xb25330: StoreField: r0->field_f = r16
    //     0xb25330: stur            w16, [x0, #0xf]
    // 0xb25334: ldur            x2, [fp, #-8]
    // 0xb25338: LoadField: r1 = r2->field_f
    //     0xb25338: ldur            w1, [x2, #0xf]
    // 0xb2533c: DecompressPointer r1
    //     0xb2533c: add             x1, x1, HEAP, lsl #32
    // 0xb25340: r0 = controller()
    //     0xb25340: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb25344: mov             x1, x0
    // 0xb25348: r0 = verses()
    //     0xb25348: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb2534c: mov             x2, x0
    // 0xb25350: LoadField: r0 = r2->field_b
    //     0xb25350: ldur            w0, [x2, #0xb]
    // 0xb25354: r1 = LoadInt32Instr(r0)
    //     0xb25354: sbfx            x1, x0, #1, #0x1f
    // 0xb25358: mov             x0, x1
    // 0xb2535c: r1 = 0
    //     0xb2535c: movz            x1, #0
    // 0xb25360: cmp             x1, x0
    // 0xb25364: b.hs            #0xb254a8
    // 0xb25368: LoadField: r0 = r2->field_f
    //     0xb25368: ldur            w0, [x2, #0xf]
    // 0xb2536c: DecompressPointer r0
    //     0xb2536c: add             x0, x0, HEAP, lsl #32
    // 0xb25370: LoadField: r1 = r0->field_f
    //     0xb25370: ldur            w1, [x0, #0xf]
    // 0xb25374: DecompressPointer r1
    //     0xb25374: add             x1, x1, HEAP, lsl #32
    // 0xb25378: LoadField: r2 = r1->field_2b
    //     0xb25378: ldur            x2, [x1, #0x2b]
    // 0xb2537c: r0 = BoxInt64Instr(r2)
    //     0xb2537c: sbfiz           x0, x2, #1, #0x1f
    //     0xb25380: cmp             x2, x0, asr #1
    //     0xb25384: b.eq            #0xb25390
    //     0xb25388: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2538c: stur            x2, [x0, #7]
    // 0xb25390: ldur            x1, [fp, #-0x28]
    // 0xb25394: ArrayStore: r1[1] = r0  ; List_4
    //     0xb25394: add             x25, x1, #0x13
    //     0xb25398: str             w0, [x25]
    //     0xb2539c: tbz             w0, #0, #0xb253b8
    //     0xb253a0: ldurb           w16, [x1, #-1]
    //     0xb253a4: ldurb           w17, [x0, #-1]
    //     0xb253a8: and             x16, x17, x16, lsr #2
    //     0xb253ac: tst             x16, HEAP, lsr #32
    //     0xb253b0: b.eq            #0xb253b8
    //     0xb253b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb253b8: ldur            x0, [fp, #-0x28]
    // 0xb253bc: r16 = "verseId"
    //     0xb253bc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bc70] "verseId"
    //     0xb253c0: ldr             x16, [x16, #0xc70]
    // 0xb253c4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb253c4: stur            w16, [x0, #0x17]
    // 0xb253c8: ldur            x1, [fp, #-8]
    // 0xb253cc: LoadField: r2 = r1->field_f
    //     0xb253cc: ldur            w2, [x1, #0xf]
    // 0xb253d0: DecompressPointer r2
    //     0xb253d0: add             x2, x2, HEAP, lsl #32
    // 0xb253d4: mov             x1, x2
    // 0xb253d8: r0 = controller()
    //     0xb253d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb253dc: mov             x1, x0
    // 0xb253e0: r0 = verses()
    //     0xb253e0: bl              #0x8c2464  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::verses
    // 0xb253e4: mov             x2, x0
    // 0xb253e8: LoadField: r0 = r2->field_b
    //     0xb253e8: ldur            w0, [x2, #0xb]
    // 0xb253ec: r1 = LoadInt32Instr(r0)
    //     0xb253ec: sbfx            x1, x0, #1, #0x1f
    // 0xb253f0: mov             x0, x1
    // 0xb253f4: r1 = 0
    //     0xb253f4: movz            x1, #0
    // 0xb253f8: cmp             x1, x0
    // 0xb253fc: b.hs            #0xb254ac
    // 0xb25400: LoadField: r0 = r2->field_f
    //     0xb25400: ldur            w0, [x2, #0xf]
    // 0xb25404: DecompressPointer r0
    //     0xb25404: add             x0, x0, HEAP, lsl #32
    // 0xb25408: LoadField: r1 = r0->field_f
    //     0xb25408: ldur            w1, [x0, #0xf]
    // 0xb2540c: DecompressPointer r1
    //     0xb2540c: add             x1, x1, HEAP, lsl #32
    // 0xb25410: LoadField: r2 = r1->field_7
    //     0xb25410: ldur            x2, [x1, #7]
    // 0xb25414: r0 = BoxInt64Instr(r2)
    //     0xb25414: sbfiz           x0, x2, #1, #0x1f
    //     0xb25418: cmp             x2, x0, asr #1
    //     0xb2541c: b.eq            #0xb25428
    //     0xb25420: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb25424: stur            x2, [x0, #7]
    // 0xb25428: ldur            x1, [fp, #-0x28]
    // 0xb2542c: ArrayStore: r1[3] = r0  ; List_4
    //     0xb2542c: add             x25, x1, #0x1b
    //     0xb25430: str             w0, [x25]
    //     0xb25434: tbz             w0, #0, #0xb25450
    //     0xb25438: ldurb           w16, [x1, #-1]
    //     0xb2543c: ldurb           w17, [x0, #-1]
    //     0xb25440: and             x16, x17, x16, lsr #2
    //     0xb25444: tst             x16, HEAP, lsr #32
    //     0xb25448: b.eq            #0xb25450
    //     0xb2544c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb25450: r16 = <String, int>
    //     0xb25450: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb25454: ldur            lr, [fp, #-0x28]
    // 0xb25458: stp             lr, x16, [SP]
    // 0xb2545c: r0 = Map._fromLiteral()
    //     0xb2545c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb25460: r16 = "/quran/quran-list"
    //     0xb25460: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c1d8] "/quran/quran-list"
    //     0xb25464: ldr             x16, [x16, #0x1d8]
    // 0xb25468: stp             x16, NULL, [SP, #8]
    // 0xb2546c: str             x0, [SP]
    // 0xb25470: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb25470: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb25474: ldr             x4, [x4, #0x478]
    // 0xb25478: r0 = GetNavigation.offAndToNamed()
    //     0xb25478: bl              #0xb1bbb8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAndToNamed
    // 0xb2547c: r0 = Null
    //     0xb2547c: mov             x0, NULL
    // 0xb25480: LeaveFrame
    //     0xb25480: mov             SP, fp
    //     0xb25484: ldp             fp, lr, [SP], #0x10
    // 0xb25488: ret
    //     0xb25488: ret             
    // 0xb2548c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2548c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb25490: b               #0xb25014
    // 0xb25494: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb25494: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb25498: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb25498: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2549c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2549c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb254a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb254a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb254a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb254a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb254a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb254a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb254ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb254ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb254bc, size: 0xc0
    // 0xb254bc: EnterFrame
    //     0xb254bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb254c0: mov             fp, SP
    // 0xb254c4: AllocStack(0x10)
    //     0xb254c4: sub             SP, SP, #0x10
    // 0xb254c8: SetupParameters()
    //     0xb254c8: ldr             x0, [fp, #0x10]
    //     0xb254cc: ldur            w1, [x0, #0x17]
    //     0xb254d0: add             x1, x1, HEAP, lsl #32
    // 0xb254d4: CheckStackOverflow
    //     0xb254d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb254d8: cmp             SP, x16
    //     0xb254dc: b.ls            #0xb25574
    // 0xb254e0: LoadField: r0 = r1->field_f
    //     0xb254e0: ldur            w0, [x1, #0xf]
    // 0xb254e4: DecompressPointer r0
    //     0xb254e4: add             x0, x0, HEAP, lsl #32
    // 0xb254e8: mov             x1, x0
    // 0xb254ec: r0 = controller()
    //     0xb254ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb254f0: LoadField: r1 = r0->field_3b
    //     0xb254f0: ldur            w1, [x0, #0x3b]
    // 0xb254f4: DecompressPointer r1
    //     0xb254f4: add             x1, x1, HEAP, lsl #32
    // 0xb254f8: r0 = value()
    //     0xb254f8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb254fc: cmp             w0, #0x4b8
    // 0xb25500: b.ne            #0xb25560
    // 0xb25504: r0 = SvgPicture()
    //     0xb25504: bl              #0xacfad4  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb25508: mov             x1, x0
    // 0xb2550c: r2 = "assets/images/doa_icon.svg"
    //     0xb2550c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c210] "assets/images/doa_icon.svg"
    //     0xb25510: ldr             x2, [x2, #0x210]
    // 0xb25514: stur            x0, [fp, #-8]
    // 0xb25518: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb25518: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2551c: r0 = SvgPicture.asset()
    //     0xb2551c: bl              #0xabab8c  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb25520: r1 = Function '<anonymous closure>':.
    //     0xb25520: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c218] AnonymousClosure: (0xb2557c), in [package:nuonline/app/modules/quran/quran_page/views/quran_page_view.dart] QuranPageView::build (0xb20e58)
    //     0xb25524: ldr             x1, [x1, #0x218]
    // 0xb25528: r2 = Null
    //     0xb25528: mov             x2, NULL
    // 0xb2552c: r0 = AllocateClosure()
    //     0xb2552c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb25530: stur            x0, [fp, #-0x10]
    // 0xb25534: r0 = IconButton()
    //     0xb25534: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb25538: ldur            x1, [fp, #-0x10]
    // 0xb2553c: StoreField: r0->field_3b = r1
    //     0xb2553c: stur            w1, [x0, #0x3b]
    // 0xb25540: r1 = false
    //     0xb25540: add             x1, NULL, #0x30  ; false
    // 0xb25544: StoreField: r0->field_47 = r1
    //     0xb25544: stur            w1, [x0, #0x47]
    // 0xb25548: ldur            x1, [fp, #-8]
    // 0xb2554c: StoreField: r0->field_1f = r1
    //     0xb2554c: stur            w1, [x0, #0x1f]
    // 0xb25550: r1 = Instance__IconButtonVariant
    //     0xb25550: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb25554: ldr             x1, [x1, #0xf78]
    // 0xb25558: StoreField: r0->field_63 = r1
    //     0xb25558: stur            w1, [x0, #0x63]
    // 0xb2555c: b               #0xb25568
    // 0xb25560: r0 = Instance_SizedBox
    //     0xb25560: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb25564: ldr             x0, [x0, #0xc40]
    // 0xb25568: LeaveFrame
    //     0xb25568: mov             SP, fp
    //     0xb2556c: ldp             fp, lr, [SP], #0x10
    // 0xb25570: ret
    //     0xb25570: ret             
    // 0xb25574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb25574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb25578: b               #0xb254e0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2557c, size: 0x90
    // 0xb2557c: EnterFrame
    //     0xb2557c: stp             fp, lr, [SP, #-0x10]!
    //     0xb25580: mov             fp, SP
    // 0xb25584: AllocStack(0x18)
    //     0xb25584: sub             SP, SP, #0x18
    // 0xb25588: CheckStackOverflow
    //     0xb25588: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2558c: cmp             SP, x16
    //     0xb25590: b.ls            #0xb25604
    // 0xb25594: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb25594: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb25598: ldr             x0, [x0, #0x2670]
    //     0xb2559c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb255a0: cmp             w0, w16
    //     0xb255a4: b.ne            #0xb255b0
    //     0xb255a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb255ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb255b0: r1 = Null
    //     0xb255b0: mov             x1, NULL
    // 0xb255b4: r2 = 4
    //     0xb255b4: movz            x2, #0x4
    // 0xb255b8: r0 = AllocateArray()
    //     0xb255b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb255bc: r16 = "id"
    //     0xb255bc: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb255c0: ldr             x16, [x16, #0x740]
    // 0xb255c4: StoreField: r0->field_f = r16
    //     0xb255c4: stur            w16, [x0, #0xf]
    // 0xb255c8: r16 = 94
    //     0xb255c8: movz            x16, #0x5e
    // 0xb255cc: StoreField: r0->field_13 = r16
    //     0xb255cc: stur            w16, [x0, #0x13]
    // 0xb255d0: r16 = <String, int>
    //     0xb255d0: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb255d4: stp             x0, x16, [SP]
    // 0xb255d8: r0 = Map._fromLiteral()
    //     0xb255d8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb255dc: r16 = "/doa/doa-detail"
    //     0xb255dc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c220] "/doa/doa-detail"
    //     0xb255e0: ldr             x16, [x16, #0x220]
    // 0xb255e4: stp             x16, NULL, [SP, #8]
    // 0xb255e8: str             x0, [SP]
    // 0xb255ec: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb255ec: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb255f0: ldr             x4, [x4, #0x478]
    // 0xb255f4: r0 = GetNavigation.toNamed()
    //     0xb255f4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb255f8: LeaveFrame
    //     0xb255f8: mov             SP, fp
    //     0xb255fc: ldp             fp, lr, [SP], #0x10
    // 0xb25600: ret
    //     0xb25600: ret             
    // 0xb25604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb25604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb25608: b               #0xb25594
  }
}
