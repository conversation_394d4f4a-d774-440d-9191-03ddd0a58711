// lib: , url: package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart

// class id: 1050462, size: 0x8
class :: {
}

// class id: 5227, size: 0x14, field offset: 0x14
class Quran<PERSON><PERSON>sir<PERSON>iew extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb319d8, size: 0x1ac
    // 0xb319d8: EnterFrame
    //     0xb319d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb319dc: mov             fp, SP
    // 0xb319e0: AllocStack(0x30)
    //     0xb319e0: sub             SP, SP, #0x30
    // 0xb319e4: SetupParameters(QuranTafsirView this /* r1 => r1, fp-0x8 */)
    //     0xb319e4: stur            x1, [fp, #-8]
    // 0xb319e8: CheckStackOverflow
    //     0xb319e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb319ec: cmp             SP, x16
    //     0xb319f0: b.ls            #0xb31b7c
    // 0xb319f4: r1 = 1
    //     0xb319f4: movz            x1, #0x1
    // 0xb319f8: r0 = AllocateContext()
    //     0xb319f8: bl              #0xec126c  ; AllocateContextStub
    // 0xb319fc: mov             x3, x0
    // 0xb31a00: ldur            x0, [fp, #-8]
    // 0xb31a04: stur            x3, [fp, #-0x10]
    // 0xb31a08: StoreField: r3->field_f = r0
    //     0xb31a08: stur            w0, [x3, #0xf]
    // 0xb31a0c: mov             x2, x3
    // 0xb31a10: r1 = Function '<anonymous closure>':.
    //     0xb31a10: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b580] AnonymousClosure: (0xb3301c), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb31a14: ldr             x1, [x1, #0x580]
    // 0xb31a18: r0 = AllocateClosure()
    //     0xb31a18: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31a1c: stur            x0, [fp, #-8]
    // 0xb31a20: r0 = IconButton()
    //     0xb31a20: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb31a24: mov             x3, x0
    // 0xb31a28: ldur            x0, [fp, #-8]
    // 0xb31a2c: stur            x3, [fp, #-0x18]
    // 0xb31a30: StoreField: r3->field_3b = r0
    //     0xb31a30: stur            w0, [x3, #0x3b]
    // 0xb31a34: r0 = false
    //     0xb31a34: add             x0, NULL, #0x30  ; false
    // 0xb31a38: StoreField: r3->field_47 = r0
    //     0xb31a38: stur            w0, [x3, #0x47]
    // 0xb31a3c: r1 = Instance_Icon
    //     0xb31a3c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b588] Obj!Icon@e248b1
    //     0xb31a40: ldr             x1, [x1, #0x588]
    // 0xb31a44: StoreField: r3->field_1f = r1
    //     0xb31a44: stur            w1, [x3, #0x1f]
    // 0xb31a48: r4 = Instance__IconButtonVariant
    //     0xb31a48: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb31a4c: ldr             x4, [x4, #0xf78]
    // 0xb31a50: StoreField: r3->field_63 = r4
    //     0xb31a50: stur            w4, [x3, #0x63]
    // 0xb31a54: r1 = Function '<anonymous closure>':.
    //     0xb31a54: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b590] AnonymousClosure: (0xb1f6f8), in [package:nuonline/app/modules/yasin/views/yasin_view.dart] YasinView::build (0xb60fa8)
    //     0xb31a58: ldr             x1, [x1, #0x590]
    // 0xb31a5c: r2 = Null
    //     0xb31a5c: mov             x2, NULL
    // 0xb31a60: r0 = AllocateClosure()
    //     0xb31a60: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31a64: stur            x0, [fp, #-8]
    // 0xb31a68: r0 = IconButton()
    //     0xb31a68: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb31a6c: mov             x3, x0
    // 0xb31a70: ldur            x0, [fp, #-8]
    // 0xb31a74: stur            x3, [fp, #-0x20]
    // 0xb31a78: StoreField: r3->field_3b = r0
    //     0xb31a78: stur            w0, [x3, #0x3b]
    // 0xb31a7c: r0 = false
    //     0xb31a7c: add             x0, NULL, #0x30  ; false
    // 0xb31a80: StoreField: r3->field_47 = r0
    //     0xb31a80: stur            w0, [x3, #0x47]
    // 0xb31a84: r1 = Instance_Icon
    //     0xb31a84: add             x1, PP, #0x27, lsl #12  ; [pp+0x27c78] Obj!Icon@e24631
    //     0xb31a88: ldr             x1, [x1, #0xc78]
    // 0xb31a8c: StoreField: r3->field_1f = r1
    //     0xb31a8c: stur            w1, [x3, #0x1f]
    // 0xb31a90: r1 = Instance__IconButtonVariant
    //     0xb31a90: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb31a94: ldr             x1, [x1, #0xf78]
    // 0xb31a98: StoreField: r3->field_63 = r1
    //     0xb31a98: stur            w1, [x3, #0x63]
    // 0xb31a9c: r1 = Null
    //     0xb31a9c: mov             x1, NULL
    // 0xb31aa0: r2 = 4
    //     0xb31aa0: movz            x2, #0x4
    // 0xb31aa4: r0 = AllocateArray()
    //     0xb31aa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb31aa8: mov             x2, x0
    // 0xb31aac: ldur            x0, [fp, #-0x18]
    // 0xb31ab0: stur            x2, [fp, #-8]
    // 0xb31ab4: StoreField: r2->field_f = r0
    //     0xb31ab4: stur            w0, [x2, #0xf]
    // 0xb31ab8: ldur            x0, [fp, #-0x20]
    // 0xb31abc: StoreField: r2->field_13 = r0
    //     0xb31abc: stur            w0, [x2, #0x13]
    // 0xb31ac0: r1 = <Widget>
    //     0xb31ac0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb31ac4: r0 = AllocateGrowableArray()
    //     0xb31ac4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb31ac8: mov             x1, x0
    // 0xb31acc: ldur            x0, [fp, #-8]
    // 0xb31ad0: stur            x1, [fp, #-0x18]
    // 0xb31ad4: StoreField: r1->field_f = r0
    //     0xb31ad4: stur            w0, [x1, #0xf]
    // 0xb31ad8: r0 = 4
    //     0xb31ad8: movz            x0, #0x4
    // 0xb31adc: StoreField: r1->field_b = r0
    //     0xb31adc: stur            w0, [x1, #0xb]
    // 0xb31ae0: r0 = AppBar()
    //     0xb31ae0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb31ae4: stur            x0, [fp, #-8]
    // 0xb31ae8: r16 = Instance_Text
    //     0xb31ae8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b598] Obj!Text@e22721
    //     0xb31aec: ldr             x16, [x16, #0x598]
    // 0xb31af0: ldur            lr, [fp, #-0x18]
    // 0xb31af4: stp             lr, x16, [SP]
    // 0xb31af8: mov             x1, x0
    // 0xb31afc: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xb31afc: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xb31b00: ldr             x4, [x4, #0xf88]
    // 0xb31b04: r0 = AppBar()
    //     0xb31b04: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb31b08: r0 = Obx()
    //     0xb31b08: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb31b0c: ldur            x2, [fp, #-0x10]
    // 0xb31b10: r1 = Function '<anonymous closure>':.
    //     0xb31b10: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5a0] AnonymousClosure: (0xb31b84), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb31b14: ldr             x1, [x1, #0x5a0]
    // 0xb31b18: stur            x0, [fp, #-0x10]
    // 0xb31b1c: r0 = AllocateClosure()
    //     0xb31b1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31b20: mov             x1, x0
    // 0xb31b24: ldur            x0, [fp, #-0x10]
    // 0xb31b28: StoreField: r0->field_b = r1
    //     0xb31b28: stur            w1, [x0, #0xb]
    // 0xb31b2c: r0 = Scaffold()
    //     0xb31b2c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb31b30: ldur            x1, [fp, #-8]
    // 0xb31b34: StoreField: r0->field_13 = r1
    //     0xb31b34: stur            w1, [x0, #0x13]
    // 0xb31b38: ldur            x1, [fp, #-0x10]
    // 0xb31b3c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb31b3c: stur            w1, [x0, #0x17]
    // 0xb31b40: r1 = Instance_AlignmentDirectional
    //     0xb31b40: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb31b44: ldr             x1, [x1, #0x758]
    // 0xb31b48: StoreField: r0->field_2b = r1
    //     0xb31b48: stur            w1, [x0, #0x2b]
    // 0xb31b4c: r1 = true
    //     0xb31b4c: add             x1, NULL, #0x20  ; true
    // 0xb31b50: StoreField: r0->field_53 = r1
    //     0xb31b50: stur            w1, [x0, #0x53]
    // 0xb31b54: r2 = Instance_DragStartBehavior
    //     0xb31b54: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb31b58: StoreField: r0->field_57 = r2
    //     0xb31b58: stur            w2, [x0, #0x57]
    // 0xb31b5c: r2 = false
    //     0xb31b5c: add             x2, NULL, #0x30  ; false
    // 0xb31b60: StoreField: r0->field_b = r2
    //     0xb31b60: stur            w2, [x0, #0xb]
    // 0xb31b64: StoreField: r0->field_f = r2
    //     0xb31b64: stur            w2, [x0, #0xf]
    // 0xb31b68: StoreField: r0->field_5f = r1
    //     0xb31b68: stur            w1, [x0, #0x5f]
    // 0xb31b6c: StoreField: r0->field_63 = r1
    //     0xb31b6c: stur            w1, [x0, #0x63]
    // 0xb31b70: LeaveFrame
    //     0xb31b70: mov             SP, fp
    //     0xb31b74: ldp             fp, lr, [SP], #0x10
    // 0xb31b78: ret
    //     0xb31b78: ret             
    // 0xb31b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb31b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb31b80: b               #0xb319f4
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0xb31b84, size: 0x420
    // 0xb31b84: EnterFrame
    //     0xb31b84: stp             fp, lr, [SP, #-0x10]!
    //     0xb31b88: mov             fp, SP
    // 0xb31b8c: AllocStack(0x38)
    //     0xb31b8c: sub             SP, SP, #0x38
    // 0xb31b90: SetupParameters()
    //     0xb31b90: ldr             x0, [fp, #0x10]
    //     0xb31b94: ldur            w2, [x0, #0x17]
    //     0xb31b98: add             x2, x2, HEAP, lsl #32
    //     0xb31b9c: stur            x2, [fp, #-8]
    // 0xb31ba0: CheckStackOverflow
    //     0xb31ba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb31ba4: cmp             SP, x16
    //     0xb31ba8: b.ls            #0xb31f94
    // 0xb31bac: LoadField: r1 = r2->field_f
    //     0xb31bac: ldur            w1, [x2, #0xf]
    // 0xb31bb0: DecompressPointer r1
    //     0xb31bb0: add             x1, x1, HEAP, lsl #32
    // 0xb31bb4: r0 = controller()
    //     0xb31bb4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31bb8: LoadField: r1 = r0->field_53
    //     0xb31bb8: ldur            w1, [x0, #0x53]
    // 0xb31bbc: DecompressPointer r1
    //     0xb31bbc: add             x1, x1, HEAP, lsl #32
    // 0xb31bc0: r0 = value()
    //     0xb31bc0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb31bc4: cmp             w0, NULL
    // 0xb31bc8: b.eq            #0xb31f80
    // 0xb31bcc: ldur            x2, [fp, #-8]
    // 0xb31bd0: LoadField: r1 = r2->field_f
    //     0xb31bd0: ldur            w1, [x2, #0xf]
    // 0xb31bd4: DecompressPointer r1
    //     0xb31bd4: add             x1, x1, HEAP, lsl #32
    // 0xb31bd8: r0 = controller()
    //     0xb31bd8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31bdc: LoadField: r1 = r0->field_4f
    //     0xb31bdc: ldur            w1, [x0, #0x4f]
    // 0xb31be0: DecompressPointer r1
    //     0xb31be0: add             x1, x1, HEAP, lsl #32
    // 0xb31be4: r0 = value()
    //     0xb31be4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb31be8: cmp             w0, NULL
    // 0xb31bec: b.eq            #0xb31f80
    // 0xb31bf0: ldur            x2, [fp, #-8]
    // 0xb31bf4: LoadField: r1 = r2->field_f
    //     0xb31bf4: ldur            w1, [x2, #0xf]
    // 0xb31bf8: DecompressPointer r1
    //     0xb31bf8: add             x1, x1, HEAP, lsl #32
    // 0xb31bfc: r0 = controller()
    //     0xb31bfc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31c00: ldur            x2, [fp, #-8]
    // 0xb31c04: stur            x0, [fp, #-0x10]
    // 0xb31c08: LoadField: r1 = r2->field_f
    //     0xb31c08: ldur            w1, [x2, #0xf]
    // 0xb31c0c: DecompressPointer r1
    //     0xb31c0c: add             x1, x1, HEAP, lsl #32
    // 0xb31c10: r0 = controller()
    //     0xb31c10: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31c14: ldur            x2, [fp, #-8]
    // 0xb31c18: stur            x0, [fp, #-0x18]
    // 0xb31c1c: LoadField: r1 = r2->field_f
    //     0xb31c1c: ldur            w1, [x2, #0xf]
    // 0xb31c20: DecompressPointer r1
    //     0xb31c20: add             x1, x1, HEAP, lsl #32
    // 0xb31c24: r0 = controller()
    //     0xb31c24: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31c28: mov             x1, x0
    // 0xb31c2c: r0 = currentVerse()
    //     0xb31c2c: bl              #0xb31fa4  ; [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::currentVerse
    // 0xb31c30: r1 = Null
    //     0xb31c30: mov             x1, NULL
    // 0xb31c34: r2 = 14
    //     0xb31c34: movz            x2, #0xe
    // 0xb31c38: stur            x0, [fp, #-0x20]
    // 0xb31c3c: r0 = AllocateArray()
    //     0xb31c3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb31c40: stur            x0, [fp, #-0x28]
    // 0xb31c44: r16 = "QS. "
    //     0xb31c44: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b568] "QS. "
    //     0xb31c48: ldr             x16, [x16, #0x568]
    // 0xb31c4c: StoreField: r0->field_f = r16
    //     0xb31c4c: stur            w16, [x0, #0xf]
    // 0xb31c50: ldur            x2, [fp, #-8]
    // 0xb31c54: LoadField: r1 = r2->field_f
    //     0xb31c54: ldur            w1, [x2, #0xf]
    // 0xb31c58: DecompressPointer r1
    //     0xb31c58: add             x1, x1, HEAP, lsl #32
    // 0xb31c5c: r0 = controller()
    //     0xb31c5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31c60: LoadField: r1 = r0->field_57
    //     0xb31c60: ldur            w1, [x0, #0x57]
    // 0xb31c64: DecompressPointer r1
    //     0xb31c64: add             x1, x1, HEAP, lsl #32
    // 0xb31c68: r0 = value()
    //     0xb31c68: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb31c6c: LoadField: r1 = r0->field_23
    //     0xb31c6c: ldur            w1, [x0, #0x23]
    // 0xb31c70: DecompressPointer r1
    //     0xb31c70: add             x1, x1, HEAP, lsl #32
    // 0xb31c74: mov             x0, x1
    // 0xb31c78: ldur            x1, [fp, #-0x28]
    // 0xb31c7c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb31c7c: add             x25, x1, #0x13
    //     0xb31c80: str             w0, [x25]
    //     0xb31c84: tbz             w0, #0, #0xb31ca0
    //     0xb31c88: ldurb           w16, [x1, #-1]
    //     0xb31c8c: ldurb           w17, [x0, #-1]
    //     0xb31c90: and             x16, x17, x16, lsr #2
    //     0xb31c94: tst             x16, HEAP, lsr #32
    //     0xb31c98: b.eq            #0xb31ca0
    //     0xb31c9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb31ca0: ldur            x0, [fp, #-0x28]
    // 0xb31ca4: r16 = ": Ayat "
    //     0xb31ca4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b570] ": Ayat "
    //     0xb31ca8: ldr             x16, [x16, #0x570]
    // 0xb31cac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb31cac: stur            w16, [x0, #0x17]
    // 0xb31cb0: ldur            x2, [fp, #-8]
    // 0xb31cb4: LoadField: r1 = r2->field_f
    //     0xb31cb4: ldur            w1, [x2, #0xf]
    // 0xb31cb8: DecompressPointer r1
    //     0xb31cb8: add             x1, x1, HEAP, lsl #32
    // 0xb31cbc: r0 = controller()
    //     0xb31cbc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31cc0: LoadField: r1 = r0->field_4f
    //     0xb31cc0: ldur            w1, [x0, #0x4f]
    // 0xb31cc4: DecompressPointer r1
    //     0xb31cc4: add             x1, x1, HEAP, lsl #32
    // 0xb31cc8: r0 = value()
    //     0xb31cc8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb31ccc: cmp             w0, NULL
    // 0xb31cd0: b.eq            #0xb31f9c
    // 0xb31cd4: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xb31cd4: ldur            x2, [x0, #0x17]
    // 0xb31cd8: r0 = BoxInt64Instr(r2)
    //     0xb31cd8: sbfiz           x0, x2, #1, #0x1f
    //     0xb31cdc: cmp             x2, x0, asr #1
    //     0xb31ce0: b.eq            #0xb31cec
    //     0xb31ce4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb31ce8: stur            x2, [x0, #7]
    // 0xb31cec: ldur            x1, [fp, #-0x28]
    // 0xb31cf0: ArrayStore: r1[3] = r0  ; List_4
    //     0xb31cf0: add             x25, x1, #0x1b
    //     0xb31cf4: str             w0, [x25]
    //     0xb31cf8: tbz             w0, #0, #0xb31d14
    //     0xb31cfc: ldurb           w16, [x1, #-1]
    //     0xb31d00: ldurb           w17, [x0, #-1]
    //     0xb31d04: and             x16, x17, x16, lsr #2
    //     0xb31d08: tst             x16, HEAP, lsr #32
    //     0xb31d0c: b.eq            #0xb31d14
    //     0xb31d10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb31d14: ldur            x0, [fp, #-0x28]
    // 0xb31d18: r16 = " (Juz "
    //     0xb31d18: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b578] " (Juz "
    //     0xb31d1c: ldr             x16, [x16, #0x578]
    // 0xb31d20: StoreField: r0->field_1f = r16
    //     0xb31d20: stur            w16, [x0, #0x1f]
    // 0xb31d24: ldur            x2, [fp, #-8]
    // 0xb31d28: LoadField: r1 = r2->field_f
    //     0xb31d28: ldur            w1, [x2, #0xf]
    // 0xb31d2c: DecompressPointer r1
    //     0xb31d2c: add             x1, x1, HEAP, lsl #32
    // 0xb31d30: r0 = controller()
    //     0xb31d30: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb31d34: LoadField: r1 = r0->field_4f
    //     0xb31d34: ldur            w1, [x0, #0x4f]
    // 0xb31d38: DecompressPointer r1
    //     0xb31d38: add             x1, x1, HEAP, lsl #32
    // 0xb31d3c: r0 = value()
    //     0xb31d3c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb31d40: cmp             w0, NULL
    // 0xb31d44: b.eq            #0xb31fa0
    // 0xb31d48: LoadField: r2 = r0->field_2b
    //     0xb31d48: ldur            x2, [x0, #0x2b]
    // 0xb31d4c: r0 = BoxInt64Instr(r2)
    //     0xb31d4c: sbfiz           x0, x2, #1, #0x1f
    //     0xb31d50: cmp             x2, x0, asr #1
    //     0xb31d54: b.eq            #0xb31d60
    //     0xb31d58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb31d5c: stur            x2, [x0, #7]
    // 0xb31d60: ldur            x1, [fp, #-0x28]
    // 0xb31d64: ArrayStore: r1[5] = r0  ; List_4
    //     0xb31d64: add             x25, x1, #0x23
    //     0xb31d68: str             w0, [x25]
    //     0xb31d6c: tbz             w0, #0, #0xb31d88
    //     0xb31d70: ldurb           w16, [x1, #-1]
    //     0xb31d74: ldurb           w17, [x0, #-1]
    //     0xb31d78: and             x16, x17, x16, lsr #2
    //     0xb31d7c: tst             x16, HEAP, lsr #32
    //     0xb31d80: b.eq            #0xb31d88
    //     0xb31d84: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb31d88: ldur            x0, [fp, #-0x28]
    // 0xb31d8c: r16 = ") "
    //     0xb31d8c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b720] ") "
    //     0xb31d90: ldr             x16, [x16, #0x720]
    // 0xb31d94: StoreField: r0->field_27 = r16
    //     0xb31d94: stur            w16, [x0, #0x27]
    // 0xb31d98: str             x0, [SP]
    // 0xb31d9c: r0 = _interpolate()
    //     0xb31d9c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb31da0: stur            x0, [fp, #-0x28]
    // 0xb31da4: r0 = NSwipeNavigation()
    //     0xb31da4: bl              #0xad8e7c  ; AllocateNSwipeNavigationStub -> NSwipeNavigation (size=0x34)
    // 0xb31da8: mov             x3, x0
    // 0xb31dac: ldur            x0, [fp, #-0x20]
    // 0xb31db0: stur            x3, [fp, #-0x30]
    // 0xb31db4: StoreField: r3->field_13 = r0
    //     0xb31db4: stur            x0, [x3, #0x13]
    // 0xb31db8: r0 = 6236
    //     0xb31db8: movz            x0, #0x185c
    // 0xb31dbc: StoreField: r3->field_1b = r0
    //     0xb31dbc: stur            x0, [x3, #0x1b]
    // 0xb31dc0: ldur            x0, [fp, #-0x28]
    // 0xb31dc4: StoreField: r3->field_23 = r0
    //     0xb31dc4: stur            w0, [x3, #0x23]
    // 0xb31dc8: r0 = ""
    //     0xb31dc8: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb31dcc: StoreField: r3->field_27 = r0
    //     0xb31dcc: stur            w0, [x3, #0x27]
    // 0xb31dd0: ldur            x2, [fp, #-0x10]
    // 0xb31dd4: r1 = Function 'onNext':.
    //     0xb31dd4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5a8] AnonymousClosure: (0xb32f74), in [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::onNext (0xb32fac)
    //     0xb31dd8: ldr             x1, [x1, #0x5a8]
    // 0xb31ddc: r0 = AllocateClosure()
    //     0xb31ddc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31de0: mov             x1, x0
    // 0xb31de4: ldur            x0, [fp, #-0x30]
    // 0xb31de8: StoreField: r0->field_b = r1
    //     0xb31de8: stur            w1, [x0, #0xb]
    // 0xb31dec: ldur            x2, [fp, #-0x18]
    // 0xb31df0: r1 = Function 'onPrev':.
    //     0xb31df0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5b0] AnonymousClosure: (0xb32ecc), in [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::onPrev (0xb32f04)
    //     0xb31df4: ldr             x1, [x1, #0x5b0]
    // 0xb31df8: r0 = AllocateClosure()
    //     0xb31df8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31dfc: mov             x1, x0
    // 0xb31e00: ldur            x0, [fp, #-0x30]
    // 0xb31e04: StoreField: r0->field_f = r1
    //     0xb31e04: stur            w1, [x0, #0xf]
    // 0xb31e08: r3 = false
    //     0xb31e08: add             x3, NULL, #0x30  ; false
    // 0xb31e0c: StoreField: r0->field_2b = r3
    //     0xb31e0c: stur            w3, [x0, #0x2b]
    // 0xb31e10: ldur            x2, [fp, #-8]
    // 0xb31e14: r1 = Function '<anonymous closure>':.
    //     0xb31e14: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5b8] AnonymousClosure: (0xb327c0), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb31e18: ldr             x1, [x1, #0x5b8]
    // 0xb31e1c: r0 = AllocateClosure()
    //     0xb31e1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31e20: r1 = <ReadingPreferenceController>
    //     0xb31e20: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb31e24: ldr             x1, [x1, #0xe10]
    // 0xb31e28: stur            x0, [fp, #-0x10]
    // 0xb31e2c: r0 = ReadingPreferenceWidget()
    //     0xb31e2c: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb31e30: mov             x1, x0
    // 0xb31e34: ldur            x0, [fp, #-0x10]
    // 0xb31e38: stur            x1, [fp, #-0x18]
    // 0xb31e3c: StoreField: r1->field_13 = r0
    //     0xb31e3c: stur            w0, [x1, #0x13]
    // 0xb31e40: r0 = NestedScrollView()
    //     0xb31e40: bl              #0xae1f40  ; AllocateNestedScrollViewStub -> NestedScrollView (size=0x3c)
    // 0xb31e44: mov             x3, x0
    // 0xb31e48: r0 = Instance_Axis
    //     0xb31e48: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb31e4c: stur            x3, [fp, #-0x10]
    // 0xb31e50: StoreField: r3->field_f = r0
    //     0xb31e50: stur            w0, [x3, #0xf]
    // 0xb31e54: r4 = false
    //     0xb31e54: add             x4, NULL, #0x30  ; false
    // 0xb31e58: StoreField: r3->field_13 = r4
    //     0xb31e58: stur            w4, [x3, #0x13]
    // 0xb31e5c: ldur            x2, [fp, #-8]
    // 0xb31e60: r1 = Function '<anonymous closure>':.
    //     0xb31e60: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5c0] AnonymousClosure: (0xb32010), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb31e64: ldr             x1, [x1, #0x5c0]
    // 0xb31e68: r0 = AllocateClosure()
    //     0xb31e68: bl              #0xec1630  ; AllocateClosureStub
    // 0xb31e6c: mov             x1, x0
    // 0xb31e70: ldur            x0, [fp, #-0x10]
    // 0xb31e74: StoreField: r0->field_1b = r1
    //     0xb31e74: stur            w1, [x0, #0x1b]
    // 0xb31e78: ldur            x1, [fp, #-0x18]
    // 0xb31e7c: StoreField: r0->field_1f = r1
    //     0xb31e7c: stur            w1, [x0, #0x1f]
    // 0xb31e80: r1 = Instance_DragStartBehavior
    //     0xb31e80: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb31e84: StoreField: r0->field_23 = r1
    //     0xb31e84: stur            w1, [x0, #0x23]
    // 0xb31e88: r1 = false
    //     0xb31e88: add             x1, NULL, #0x30  ; false
    // 0xb31e8c: StoreField: r0->field_27 = r1
    //     0xb31e8c: stur            w1, [x0, #0x27]
    // 0xb31e90: r1 = Instance_Clip
    //     0xb31e90: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb31e94: ldr             x1, [x1, #0x7c0]
    // 0xb31e98: StoreField: r0->field_2b = r1
    //     0xb31e98: stur            w1, [x0, #0x2b]
    // 0xb31e9c: r1 = Instance_HitTestBehavior
    //     0xb31e9c: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xb31ea0: ldr             x1, [x1, #0x1c8]
    // 0xb31ea4: StoreField: r0->field_2f = r1
    //     0xb31ea4: stur            w1, [x0, #0x2f]
    // 0xb31ea8: r1 = <FlexParentData>
    //     0xb31ea8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb31eac: ldr             x1, [x1, #0x720]
    // 0xb31eb0: r0 = Expanded()
    //     0xb31eb0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb31eb4: mov             x3, x0
    // 0xb31eb8: r0 = 1
    //     0xb31eb8: movz            x0, #0x1
    // 0xb31ebc: stur            x3, [fp, #-8]
    // 0xb31ec0: StoreField: r3->field_13 = r0
    //     0xb31ec0: stur            x0, [x3, #0x13]
    // 0xb31ec4: r0 = Instance_FlexFit
    //     0xb31ec4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb31ec8: ldr             x0, [x0, #0x728]
    // 0xb31ecc: StoreField: r3->field_1b = r0
    //     0xb31ecc: stur            w0, [x3, #0x1b]
    // 0xb31ed0: ldur            x0, [fp, #-0x10]
    // 0xb31ed4: StoreField: r3->field_b = r0
    //     0xb31ed4: stur            w0, [x3, #0xb]
    // 0xb31ed8: r1 = Null
    //     0xb31ed8: mov             x1, NULL
    // 0xb31edc: r2 = 6
    //     0xb31edc: movz            x2, #0x6
    // 0xb31ee0: r0 = AllocateArray()
    //     0xb31ee0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb31ee4: mov             x2, x0
    // 0xb31ee8: ldur            x0, [fp, #-0x30]
    // 0xb31eec: stur            x2, [fp, #-0x10]
    // 0xb31ef0: StoreField: r2->field_f = r0
    //     0xb31ef0: stur            w0, [x2, #0xf]
    // 0xb31ef4: r16 = Instance_Divider
    //     0xb31ef4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb31ef8: ldr             x16, [x16, #0xc28]
    // 0xb31efc: StoreField: r2->field_13 = r16
    //     0xb31efc: stur            w16, [x2, #0x13]
    // 0xb31f00: ldur            x0, [fp, #-8]
    // 0xb31f04: ArrayStore: r2[0] = r0  ; List_4
    //     0xb31f04: stur            w0, [x2, #0x17]
    // 0xb31f08: r1 = <Widget>
    //     0xb31f08: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb31f0c: r0 = AllocateGrowableArray()
    //     0xb31f0c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb31f10: mov             x1, x0
    // 0xb31f14: ldur            x0, [fp, #-0x10]
    // 0xb31f18: stur            x1, [fp, #-8]
    // 0xb31f1c: StoreField: r1->field_f = r0
    //     0xb31f1c: stur            w0, [x1, #0xf]
    // 0xb31f20: r0 = 6
    //     0xb31f20: movz            x0, #0x6
    // 0xb31f24: StoreField: r1->field_b = r0
    //     0xb31f24: stur            w0, [x1, #0xb]
    // 0xb31f28: r0 = Column()
    //     0xb31f28: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb31f2c: r1 = Instance_Axis
    //     0xb31f2c: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb31f30: StoreField: r0->field_f = r1
    //     0xb31f30: stur            w1, [x0, #0xf]
    // 0xb31f34: r1 = Instance_MainAxisAlignment
    //     0xb31f34: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb31f38: ldr             x1, [x1, #0x730]
    // 0xb31f3c: StoreField: r0->field_13 = r1
    //     0xb31f3c: stur            w1, [x0, #0x13]
    // 0xb31f40: r1 = Instance_MainAxisSize
    //     0xb31f40: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb31f44: ldr             x1, [x1, #0x738]
    // 0xb31f48: ArrayStore: r0[0] = r1  ; List_4
    //     0xb31f48: stur            w1, [x0, #0x17]
    // 0xb31f4c: r1 = Instance_CrossAxisAlignment
    //     0xb31f4c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb31f50: ldr             x1, [x1, #0x740]
    // 0xb31f54: StoreField: r0->field_1b = r1
    //     0xb31f54: stur            w1, [x0, #0x1b]
    // 0xb31f58: r1 = Instance_VerticalDirection
    //     0xb31f58: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb31f5c: ldr             x1, [x1, #0x748]
    // 0xb31f60: StoreField: r0->field_23 = r1
    //     0xb31f60: stur            w1, [x0, #0x23]
    // 0xb31f64: r1 = Instance_Clip
    //     0xb31f64: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb31f68: ldr             x1, [x1, #0x750]
    // 0xb31f6c: StoreField: r0->field_2b = r1
    //     0xb31f6c: stur            w1, [x0, #0x2b]
    // 0xb31f70: StoreField: r0->field_2f = rZR
    //     0xb31f70: stur            xzr, [x0, #0x2f]
    // 0xb31f74: ldur            x1, [fp, #-8]
    // 0xb31f78: StoreField: r0->field_b = r1
    //     0xb31f78: stur            w1, [x0, #0xb]
    // 0xb31f7c: b               #0xb31f88
    // 0xb31f80: r0 = Instance_Center
    //     0xb31f80: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5c8] Obj!Center@e1e761
    //     0xb31f84: ldr             x0, [x0, #0x5c8]
    // 0xb31f88: LeaveFrame
    //     0xb31f88: mov             SP, fp
    //     0xb31f8c: ldp             fp, lr, [SP], #0x10
    // 0xb31f90: ret
    //     0xb31f90: ret             
    // 0xb31f94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb31f94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb31f98: b               #0xb31bac
    // 0xb31f9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb31f9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb31fa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb31fa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] List<Widget> <anonymous closure>(dynamic, BuildContext, bool) {
    // ** addr: 0xb32010, size: 0x330
    // 0xb32010: EnterFrame
    //     0xb32010: stp             fp, lr, [SP, #-0x10]!
    //     0xb32014: mov             fp, SP
    // 0xb32018: AllocStack(0x28)
    //     0xb32018: sub             SP, SP, #0x28
    // 0xb3201c: SetupParameters()
    //     0xb3201c: ldr             x0, [fp, #0x20]
    //     0xb32020: ldur            w3, [x0, #0x17]
    //     0xb32024: add             x3, x3, HEAP, lsl #32
    //     0xb32028: stur            x3, [fp, #-8]
    // 0xb3202c: CheckStackOverflow
    //     0xb3202c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32030: cmp             SP, x16
    //     0xb32034: b.ls            #0xb3232c
    // 0xb32038: mov             x2, x3
    // 0xb3203c: r1 = Function '<anonymous closure>':.
    //     0xb3203c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5d0] AnonymousClosure: (0xb32340), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb32040: ldr             x1, [x1, #0x5d0]
    // 0xb32044: r0 = AllocateClosure()
    //     0xb32044: bl              #0xec1630  ; AllocateClosureStub
    // 0xb32048: r1 = <ReadingPreferenceController>
    //     0xb32048: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xb3204c: ldr             x1, [x1, #0xe10]
    // 0xb32050: stur            x0, [fp, #-0x10]
    // 0xb32054: r0 = ReadingPreferenceWidget()
    //     0xb32054: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xb32058: mov             x1, x0
    // 0xb3205c: ldur            x0, [fp, #-0x10]
    // 0xb32060: stur            x1, [fp, #-0x18]
    // 0xb32064: StoreField: r1->field_13 = r0
    //     0xb32064: stur            w0, [x1, #0x13]
    // 0xb32068: r0 = Padding()
    //     0xb32068: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3206c: mov             x1, x0
    // 0xb32070: r0 = Instance_EdgeInsets
    //     0xb32070: ldr             x0, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb32074: stur            x1, [fp, #-0x10]
    // 0xb32078: StoreField: r1->field_f = r0
    //     0xb32078: stur            w0, [x1, #0xf]
    // 0xb3207c: ldur            x0, [fp, #-0x18]
    // 0xb32080: StoreField: r1->field_b = r0
    //     0xb32080: stur            w0, [x1, #0xb]
    // 0xb32084: r0 = Align()
    //     0xb32084: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb32088: mov             x3, x0
    // 0xb3208c: r0 = Instance_Alignment
    //     0xb3208c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5d8] Obj!Alignment@e13f11
    //     0xb32090: ldr             x0, [x0, #0x5d8]
    // 0xb32094: stur            x3, [fp, #-0x18]
    // 0xb32098: StoreField: r3->field_f = r0
    //     0xb32098: stur            w0, [x3, #0xf]
    // 0xb3209c: ldur            x0, [fp, #-0x10]
    // 0xb320a0: StoreField: r3->field_b = r0
    //     0xb320a0: stur            w0, [x3, #0xb]
    // 0xb320a4: r1 = Null
    //     0xb320a4: mov             x1, NULL
    // 0xb320a8: r2 = 4
    //     0xb320a8: movz            x2, #0x4
    // 0xb320ac: r0 = AllocateArray()
    //     0xb320ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb320b0: mov             x2, x0
    // 0xb320b4: ldur            x0, [fp, #-0x18]
    // 0xb320b8: stur            x2, [fp, #-0x10]
    // 0xb320bc: StoreField: r2->field_f = r0
    //     0xb320bc: stur            w0, [x2, #0xf]
    // 0xb320c0: r16 = Instance_Divider
    //     0xb320c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb320c4: ldr             x16, [x16, #0xc28]
    // 0xb320c8: StoreField: r2->field_13 = r16
    //     0xb320c8: stur            w16, [x2, #0x13]
    // 0xb320cc: r1 = <Widget>
    //     0xb320cc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb320d0: r0 = AllocateGrowableArray()
    //     0xb320d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb320d4: mov             x1, x0
    // 0xb320d8: ldur            x0, [fp, #-0x10]
    // 0xb320dc: stur            x1, [fp, #-0x18]
    // 0xb320e0: StoreField: r1->field_f = r0
    //     0xb320e0: stur            w0, [x1, #0xf]
    // 0xb320e4: r2 = 4
    //     0xb320e4: movz            x2, #0x4
    // 0xb320e8: StoreField: r1->field_b = r2
    //     0xb320e8: stur            w2, [x1, #0xb]
    // 0xb320ec: r0 = Column()
    //     0xb320ec: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb320f0: mov             x1, x0
    // 0xb320f4: r0 = Instance_Axis
    //     0xb320f4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb320f8: stur            x1, [fp, #-0x10]
    // 0xb320fc: StoreField: r1->field_f = r0
    //     0xb320fc: stur            w0, [x1, #0xf]
    // 0xb32100: r0 = Instance_MainAxisAlignment
    //     0xb32100: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb32104: ldr             x0, [x0, #0x730]
    // 0xb32108: StoreField: r1->field_13 = r0
    //     0xb32108: stur            w0, [x1, #0x13]
    // 0xb3210c: r0 = Instance_MainAxisSize
    //     0xb3210c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xb32110: ldr             x0, [x0, #0xe88]
    // 0xb32114: ArrayStore: r1[0] = r0  ; List_4
    //     0xb32114: stur            w0, [x1, #0x17]
    // 0xb32118: r0 = Instance_CrossAxisAlignment
    //     0xb32118: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb3211c: ldr             x0, [x0, #0x740]
    // 0xb32120: StoreField: r1->field_1b = r0
    //     0xb32120: stur            w0, [x1, #0x1b]
    // 0xb32124: r0 = Instance_VerticalDirection
    //     0xb32124: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb32128: ldr             x0, [x0, #0x748]
    // 0xb3212c: StoreField: r1->field_23 = r0
    //     0xb3212c: stur            w0, [x1, #0x23]
    // 0xb32130: r0 = Instance_Clip
    //     0xb32130: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb32134: ldr             x0, [x0, #0x750]
    // 0xb32138: StoreField: r1->field_2b = r0
    //     0xb32138: stur            w0, [x1, #0x2b]
    // 0xb3213c: StoreField: r1->field_2f = rZR
    //     0xb3213c: stur            xzr, [x1, #0x2f]
    // 0xb32140: ldur            x0, [fp, #-0x18]
    // 0xb32144: StoreField: r1->field_b = r0
    //     0xb32144: stur            w0, [x1, #0xb]
    // 0xb32148: r0 = SliverToBoxAdapter()
    //     0xb32148: bl              #0xae2334  ; AllocateSliverToBoxAdapterStub -> SliverToBoxAdapter (size=0x10)
    // 0xb3214c: mov             x1, x0
    // 0xb32150: ldur            x0, [fp, #-0x10]
    // 0xb32154: stur            x1, [fp, #-0x18]
    // 0xb32158: StoreField: r1->field_b = r0
    //     0xb32158: stur            w0, [x1, #0xb]
    // 0xb3215c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3215c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb32160: ldr             x0, [x0, #0x2670]
    //     0xb32164: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb32168: cmp             w0, w16
    //     0xb3216c: b.ne            #0xb32178
    //     0xb32170: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb32174: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb32178: r0 = GetNavigation.theme()
    //     0xb32178: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb3217c: LoadField: r1 = r0->field_6f
    //     0xb3217c: ldur            w1, [x0, #0x6f]
    // 0xb32180: DecompressPointer r1
    //     0xb32180: add             x1, x1, HEAP, lsl #32
    // 0xb32184: stur            x1, [fp, #-0x10]
    // 0xb32188: r0 = GetNavigation.theme()
    //     0xb32188: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb3218c: LoadField: r1 = r0->field_77
    //     0xb3218c: ldur            w1, [x0, #0x77]
    // 0xb32190: DecompressPointer r1
    //     0xb32190: add             x1, x1, HEAP, lsl #32
    // 0xb32194: stur            x1, [fp, #-0x20]
    // 0xb32198: r0 = BoxShadow()
    //     0xb32198: bl              #0x794360  ; AllocateBoxShadowStub -> BoxShadow (size=0x24)
    // 0xb3219c: stur            x0, [fp, #-0x28]
    // 0xb321a0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb321a0: stur            xzr, [x0, #0x17]
    // 0xb321a4: r1 = Instance_BlurStyle
    //     0xb321a4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29010] Obj!BlurStyle@e39a01
    //     0xb321a8: ldr             x1, [x1, #0x10]
    // 0xb321ac: StoreField: r0->field_1f = r1
    //     0xb321ac: stur            w1, [x0, #0x1f]
    // 0xb321b0: ldur            x1, [fp, #-0x20]
    // 0xb321b4: StoreField: r0->field_7 = r1
    //     0xb321b4: stur            w1, [x0, #7]
    // 0xb321b8: r1 = Instance_Offset
    //     0xb321b8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29ef0] Obj!Offset@e2c8a1
    //     0xb321bc: ldr             x1, [x1, #0xef0]
    // 0xb321c0: StoreField: r0->field_b = r1
    //     0xb321c0: stur            w1, [x0, #0xb]
    // 0xb321c4: d0 = 8.000000
    //     0xb321c4: fmov            d0, #8.00000000
    // 0xb321c8: StoreField: r0->field_f = d0
    //     0xb321c8: stur            d0, [x0, #0xf]
    // 0xb321cc: r1 = Null
    //     0xb321cc: mov             x1, NULL
    // 0xb321d0: r2 = 2
    //     0xb321d0: movz            x2, #0x2
    // 0xb321d4: r0 = AllocateArray()
    //     0xb321d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb321d8: mov             x2, x0
    // 0xb321dc: ldur            x0, [fp, #-0x28]
    // 0xb321e0: stur            x2, [fp, #-0x20]
    // 0xb321e4: StoreField: r2->field_f = r0
    //     0xb321e4: stur            w0, [x2, #0xf]
    // 0xb321e8: r1 = <BoxShadow>
    //     0xb321e8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29020] TypeArguments: <BoxShadow>
    //     0xb321ec: ldr             x1, [x1, #0x20]
    // 0xb321f0: r0 = AllocateGrowableArray()
    //     0xb321f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb321f4: mov             x1, x0
    // 0xb321f8: ldur            x0, [fp, #-0x20]
    // 0xb321fc: stur            x1, [fp, #-0x28]
    // 0xb32200: StoreField: r1->field_f = r0
    //     0xb32200: stur            w0, [x1, #0xf]
    // 0xb32204: r0 = 2
    //     0xb32204: movz            x0, #0x2
    // 0xb32208: StoreField: r1->field_b = r0
    //     0xb32208: stur            w0, [x1, #0xb]
    // 0xb3220c: r0 = BoxDecoration()
    //     0xb3220c: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb32210: mov             x2, x0
    // 0xb32214: ldur            x0, [fp, #-0x10]
    // 0xb32218: stur            x2, [fp, #-0x20]
    // 0xb3221c: StoreField: r2->field_7 = r0
    //     0xb3221c: stur            w0, [x2, #7]
    // 0xb32220: ldur            x0, [fp, #-0x28]
    // 0xb32224: ArrayStore: r2[0] = r0  ; List_4
    //     0xb32224: stur            w0, [x2, #0x17]
    // 0xb32228: r0 = Instance_BoxShape
    //     0xb32228: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb3222c: ldr             x0, [x0, #0xca8]
    // 0xb32230: StoreField: r2->field_23 = r0
    //     0xb32230: stur            w0, [x2, #0x23]
    // 0xb32234: ldur            x0, [fp, #-8]
    // 0xb32238: LoadField: r1 = r0->field_f
    //     0xb32238: ldur            w1, [x0, #0xf]
    // 0xb3223c: DecompressPointer r1
    //     0xb3223c: add             x1, x1, HEAP, lsl #32
    // 0xb32240: r0 = controller()
    //     0xb32240: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb32244: LoadField: r1 = r0->field_2f
    //     0xb32244: ldur            w1, [x0, #0x2f]
    // 0xb32248: DecompressPointer r1
    //     0xb32248: add             x1, x1, HEAP, lsl #32
    // 0xb3224c: r16 = Sentinel
    //     0xb3224c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb32250: cmp             w1, w16
    // 0xb32254: b.eq            #0xb32334
    // 0xb32258: stur            x1, [fp, #-8]
    // 0xb3225c: r0 = TabBar()
    //     0xb3225c: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xb32260: mov             x1, x0
    // 0xb32264: r0 = const [Instance of 'Tab', Instance of 'Tab', Instance of 'Tab']
    //     0xb32264: add             x0, PP, #0x29, lsl #12  ; [pp+0x29ef8] List<Widget>(3)
    //     0xb32268: ldr             x0, [x0, #0xef8]
    // 0xb3226c: stur            x1, [fp, #-0x10]
    // 0xb32270: StoreField: r1->field_b = r0
    //     0xb32270: stur            w0, [x1, #0xb]
    // 0xb32274: ldur            x0, [fp, #-8]
    // 0xb32278: StoreField: r1->field_f = r0
    //     0xb32278: stur            w0, [x1, #0xf]
    // 0xb3227c: r0 = false
    //     0xb3227c: add             x0, NULL, #0x30  ; false
    // 0xb32280: StoreField: r1->field_13 = r0
    //     0xb32280: stur            w0, [x1, #0x13]
    // 0xb32284: r0 = true
    //     0xb32284: add             x0, NULL, #0x20  ; true
    // 0xb32288: StoreField: r1->field_2f = r0
    //     0xb32288: stur            w0, [x1, #0x2f]
    // 0xb3228c: d0 = 2.000000
    //     0xb3228c: fmov            d0, #2.00000000
    // 0xb32290: StoreField: r1->field_1f = d0
    //     0xb32290: stur            d0, [x1, #0x1f]
    // 0xb32294: r2 = Instance_EdgeInsets
    //     0xb32294: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb32298: StoreField: r1->field_27 = r2
    //     0xb32298: stur            w2, [x1, #0x27]
    // 0xb3229c: r2 = Instance_DragStartBehavior
    //     0xb3229c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb322a0: StoreField: r1->field_57 = r2
    //     0xb322a0: stur            w2, [x1, #0x57]
    // 0xb322a4: StoreField: r1->field_7f = r0
    //     0xb322a4: stur            w0, [x1, #0x7f]
    // 0xb322a8: r0 = DecoratedBox()
    //     0xb322a8: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb322ac: mov             x1, x0
    // 0xb322b0: ldur            x0, [fp, #-0x20]
    // 0xb322b4: stur            x1, [fp, #-8]
    // 0xb322b8: StoreField: r1->field_f = r0
    //     0xb322b8: stur            w0, [x1, #0xf]
    // 0xb322bc: r0 = Instance_DecorationPosition
    //     0xb322bc: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb322c0: ldr             x0, [x0, #0xb28]
    // 0xb322c4: StoreField: r1->field_13 = r0
    //     0xb322c4: stur            w0, [x1, #0x13]
    // 0xb322c8: ldur            x0, [fp, #-0x10]
    // 0xb322cc: StoreField: r1->field_b = r0
    //     0xb322cc: stur            w0, [x1, #0xb]
    // 0xb322d0: r0 = SliverPinnedHeader()
    //     0xb322d0: bl              #0xae2328  ; AllocateSliverPinnedHeaderStub -> SliverPinnedHeader (size=0x10)
    // 0xb322d4: mov             x3, x0
    // 0xb322d8: ldur            x0, [fp, #-8]
    // 0xb322dc: stur            x3, [fp, #-0x10]
    // 0xb322e0: StoreField: r3->field_b = r0
    //     0xb322e0: stur            w0, [x3, #0xb]
    // 0xb322e4: r1 = Null
    //     0xb322e4: mov             x1, NULL
    // 0xb322e8: r2 = 4
    //     0xb322e8: movz            x2, #0x4
    // 0xb322ec: r0 = AllocateArray()
    //     0xb322ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb322f0: mov             x2, x0
    // 0xb322f4: ldur            x0, [fp, #-0x18]
    // 0xb322f8: stur            x2, [fp, #-8]
    // 0xb322fc: StoreField: r2->field_f = r0
    //     0xb322fc: stur            w0, [x2, #0xf]
    // 0xb32300: ldur            x0, [fp, #-0x10]
    // 0xb32304: StoreField: r2->field_13 = r0
    //     0xb32304: stur            w0, [x2, #0x13]
    // 0xb32308: r1 = <Widget>
    //     0xb32308: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3230c: r0 = AllocateGrowableArray()
    //     0xb3230c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb32310: ldur            x1, [fp, #-8]
    // 0xb32314: StoreField: r0->field_f = r1
    //     0xb32314: stur            w1, [x0, #0xf]
    // 0xb32318: r1 = 4
    //     0xb32318: movz            x1, #0x4
    // 0xb3231c: StoreField: r0->field_b = r1
    //     0xb3231c: stur            w1, [x0, #0xb]
    // 0xb32320: LeaveFrame
    //     0xb32320: mov             SP, fp
    //     0xb32324: ldp             fp, lr, [SP], #0x10
    // 0xb32328: ret
    //     0xb32328: ret             
    // 0xb3232c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3232c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32330: b               #0xb32038
    // 0xb32334: r9 = tabController
    //     0xb32334: add             x9, PP, #0x2b, lsl #12  ; [pp+0x2b5e0] Field <QuranTafsirController.tabController>: late (offset: 0x30)
    //     0xb32338: ldr             x9, [x9, #0x5e0]
    // 0xb3233c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb3233c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Text <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb32340, size: 0x2b0
    // 0xb32340: EnterFrame
    //     0xb32340: stp             fp, lr, [SP, #-0x10]!
    //     0xb32344: mov             fp, SP
    // 0xb32348: AllocStack(0x48)
    //     0xb32348: sub             SP, SP, #0x48
    // 0xb3234c: SetupParameters()
    //     0xb3234c: ldr             x0, [fp, #0x20]
    //     0xb32350: ldur            w2, [x0, #0x17]
    //     0xb32354: add             x2, x2, HEAP, lsl #32
    //     0xb32358: stur            x2, [fp, #-8]
    // 0xb3235c: CheckStackOverflow
    //     0xb3235c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32360: cmp             SP, x16
    //     0xb32364: b.ls            #0xb325ac
    // 0xb32368: LoadField: r1 = r2->field_f
    //     0xb32368: ldur            w1, [x2, #0xf]
    // 0xb3236c: DecompressPointer r1
    //     0xb3236c: add             x1, x1, HEAP, lsl #32
    // 0xb32370: r0 = controller()
    //     0xb32370: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb32374: LoadField: r1 = r0->field_4f
    //     0xb32374: ldur            w1, [x0, #0x4f]
    // 0xb32378: DecompressPointer r1
    //     0xb32378: add             x1, x1, HEAP, lsl #32
    // 0xb3237c: r0 = value()
    //     0xb3237c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb32380: cmp             w0, NULL
    // 0xb32384: b.eq            #0xb325b4
    // 0xb32388: LoadField: r1 = r0->field_1f
    //     0xb32388: ldur            w1, [x0, #0x1f]
    // 0xb3238c: DecompressPointer r1
    //     0xb3238c: add             x1, x1, HEAP, lsl #32
    // 0xb32390: stur            x1, [fp, #-0x10]
    // 0xb32394: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb32394: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb32398: ldr             x0, [x0, #0x2670]
    //     0xb3239c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb323a0: cmp             w0, w16
    //     0xb323a4: b.ne            #0xb323b0
    //     0xb323a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb323ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb323b0: r0 = GetNavigation.textTheme()
    //     0xb323b0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb323b4: LoadField: r1 = r0->field_23
    //     0xb323b4: ldur            w1, [x0, #0x23]
    // 0xb323b8: DecompressPointer r1
    //     0xb323b8: add             x1, x1, HEAP, lsl #32
    // 0xb323bc: cmp             w1, NULL
    // 0xb323c0: b.ne            #0xb323cc
    // 0xb323c4: r3 = Null
    //     0xb323c4: mov             x3, NULL
    // 0xb323c8: b               #0xb32418
    // 0xb323cc: ldr             x0, [fp, #0x18]
    // 0xb323d0: LoadField: d0 = r0->field_7
    //     0xb323d0: ldur            d0, [x0, #7]
    // 0xb323d4: r2 = inline_Allocate_Double()
    //     0xb323d4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb323d8: add             x2, x2, #0x10
    //     0xb323dc: cmp             x3, x2
    //     0xb323e0: b.ls            #0xb325b8
    //     0xb323e4: str             x2, [THR, #0x50]  ; THR::top
    //     0xb323e8: sub             x2, x2, #0xf
    //     0xb323ec: movz            x3, #0xe15c
    //     0xb323f0: movk            x3, #0x3, lsl #16
    //     0xb323f4: stur            x3, [x2, #-1]
    // 0xb323f8: StoreField: r2->field_7 = d0
    //     0xb323f8: stur            d0, [x2, #7]
    // 0xb323fc: r16 = "OmarNaskh"
    //     0xb323fc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xb32400: ldr             x16, [x16, #0xbc8]
    // 0xb32404: stp             x2, x16, [SP]
    // 0xb32408: r4 = const [0, 0x3, 0x2, 0x1, fontFamily, 0x1, fontSize, 0x2, null]
    //     0xb32408: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b5e8] List(9) [0, 0x3, 0x2, 0x1, "fontFamily", 0x1, "fontSize", 0x2, Null]
    //     0xb3240c: ldr             x4, [x4, #0x5e8]
    // 0xb32410: r0 = copyWith()
    //     0xb32410: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32414: mov             x3, x0
    // 0xb32418: ldr             x0, [fp, #0x18]
    // 0xb3241c: ldur            x1, [fp, #-8]
    // 0xb32420: ldur            x2, [fp, #-0x10]
    // 0xb32424: stur            x3, [fp, #-0x18]
    // 0xb32428: LoadField: r4 = r1->field_f
    //     0xb32428: ldur            w4, [x1, #0xf]
    // 0xb3242c: DecompressPointer r4
    //     0xb3242c: add             x4, x4, HEAP, lsl #32
    // 0xb32430: mov             x1, x4
    // 0xb32434: r0 = controller()
    //     0xb32434: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb32438: LoadField: r1 = r0->field_4f
    //     0xb32438: ldur            w1, [x0, #0x4f]
    // 0xb3243c: DecompressPointer r1
    //     0xb3243c: add             x1, x1, HEAP, lsl #32
    // 0xb32440: r0 = value()
    //     0xb32440: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb32444: cmp             w0, NULL
    // 0xb32448: b.eq            #0xb325d4
    // 0xb3244c: mov             x1, x0
    // 0xb32450: r0 = numberArabic()
    //     0xb32450: bl              #0xb325f0  ; [package:nuonline/app/data/models/verse.dart] Verse::numberArabic
    // 0xb32454: mov             x3, x0
    // 0xb32458: ldr             x0, [fp, #0x18]
    // 0xb3245c: stur            x3, [fp, #-8]
    // 0xb32460: LoadField: d0 = r0->field_7
    //     0xb32460: ldur            d0, [x0, #7]
    // 0xb32464: stur            d0, [fp, #-0x30]
    // 0xb32468: r1 = _ConstMap len:10
    //     0xb32468: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xb3246c: ldr             x1, [x1, #0x6c8]
    // 0xb32470: r2 = 600
    //     0xb32470: movz            x2, #0x258
    // 0xb32474: r0 = []()
    //     0xb32474: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb32478: r16 = <Color?>
    //     0xb32478: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb3247c: ldr             x16, [x16, #0x98]
    // 0xb32480: stp             x0, x16, [SP, #8]
    // 0xb32484: r16 = Instance_MaterialColor
    //     0xb32484: add             x16, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xb32488: ldr             x16, [x16, #0x6d0]
    // 0xb3248c: str             x16, [SP]
    // 0xb32490: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb32490: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb32494: r0 = mode()
    //     0xb32494: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb32498: stur            x0, [fp, #-0x20]
    // 0xb3249c: r0 = TextStyle()
    //     0xb3249c: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb324a0: mov             x1, x0
    // 0xb324a4: r0 = true
    //     0xb324a4: add             x0, NULL, #0x20  ; true
    // 0xb324a8: stur            x1, [fp, #-0x28]
    // 0xb324ac: StoreField: r1->field_7 = r0
    //     0xb324ac: stur            w0, [x1, #7]
    // 0xb324b0: ldur            x0, [fp, #-0x20]
    // 0xb324b4: StoreField: r1->field_b = r0
    //     0xb324b4: stur            w0, [x1, #0xb]
    // 0xb324b8: ldur            d0, [fp, #-0x30]
    // 0xb324bc: r0 = inline_Allocate_Double()
    //     0xb324bc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb324c0: add             x0, x0, #0x10
    //     0xb324c4: cmp             x2, x0
    //     0xb324c8: b.ls            #0xb325d8
    //     0xb324cc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb324d0: sub             x0, x0, #0xf
    //     0xb324d4: movz            x2, #0xe15c
    //     0xb324d8: movk            x2, #0x3, lsl #16
    //     0xb324dc: stur            x2, [x0, #-1]
    // 0xb324e0: StoreField: r0->field_7 = d0
    //     0xb324e0: stur            d0, [x0, #7]
    // 0xb324e4: StoreField: r1->field_1f = r0
    //     0xb324e4: stur            w0, [x1, #0x1f]
    // 0xb324e8: r0 = "OmarNaskh"
    //     0xb324e8: add             x0, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xb324ec: ldr             x0, [x0, #0xbc8]
    // 0xb324f0: StoreField: r1->field_13 = r0
    //     0xb324f0: stur            w0, [x1, #0x13]
    // 0xb324f4: r0 = TextSpan()
    //     0xb324f4: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb324f8: mov             x3, x0
    // 0xb324fc: ldur            x0, [fp, #-8]
    // 0xb32500: stur            x3, [fp, #-0x20]
    // 0xb32504: StoreField: r3->field_b = r0
    //     0xb32504: stur            w0, [x3, #0xb]
    // 0xb32508: r0 = Instance__DeferringMouseCursor
    //     0xb32508: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb3250c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3250c: stur            w0, [x3, #0x17]
    // 0xb32510: ldur            x1, [fp, #-0x28]
    // 0xb32514: StoreField: r3->field_7 = r1
    //     0xb32514: stur            w1, [x3, #7]
    // 0xb32518: r1 = Null
    //     0xb32518: mov             x1, NULL
    // 0xb3251c: r2 = 2
    //     0xb3251c: movz            x2, #0x2
    // 0xb32520: r0 = AllocateArray()
    //     0xb32520: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb32524: mov             x2, x0
    // 0xb32528: ldur            x0, [fp, #-0x20]
    // 0xb3252c: stur            x2, [fp, #-8]
    // 0xb32530: StoreField: r2->field_f = r0
    //     0xb32530: stur            w0, [x2, #0xf]
    // 0xb32534: r1 = <InlineSpan>
    //     0xb32534: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xb32538: ldr             x1, [x1, #0x5f0]
    // 0xb3253c: r0 = AllocateGrowableArray()
    //     0xb3253c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb32540: mov             x1, x0
    // 0xb32544: ldur            x0, [fp, #-8]
    // 0xb32548: stur            x1, [fp, #-0x20]
    // 0xb3254c: StoreField: r1->field_f = r0
    //     0xb3254c: stur            w0, [x1, #0xf]
    // 0xb32550: r0 = 2
    //     0xb32550: movz            x0, #0x2
    // 0xb32554: StoreField: r1->field_b = r0
    //     0xb32554: stur            w0, [x1, #0xb]
    // 0xb32558: r0 = TextSpan()
    //     0xb32558: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb3255c: mov             x1, x0
    // 0xb32560: ldur            x0, [fp, #-0x10]
    // 0xb32564: stur            x1, [fp, #-8]
    // 0xb32568: StoreField: r1->field_b = r0
    //     0xb32568: stur            w0, [x1, #0xb]
    // 0xb3256c: ldur            x0, [fp, #-0x20]
    // 0xb32570: StoreField: r1->field_f = r0
    //     0xb32570: stur            w0, [x1, #0xf]
    // 0xb32574: r0 = Instance__DeferringMouseCursor
    //     0xb32574: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb32578: ArrayStore: r1[0] = r0  ; List_4
    //     0xb32578: stur            w0, [x1, #0x17]
    // 0xb3257c: ldur            x0, [fp, #-0x18]
    // 0xb32580: StoreField: r1->field_7 = r0
    //     0xb32580: stur            w0, [x1, #7]
    // 0xb32584: r0 = Text()
    //     0xb32584: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32588: ldur            x1, [fp, #-8]
    // 0xb3258c: StoreField: r0->field_f = r1
    //     0xb3258c: stur            w1, [x0, #0xf]
    // 0xb32590: r1 = Instance_TextAlign
    //     0xb32590: ldr             x1, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0xb32594: StoreField: r0->field_1b = r1
    //     0xb32594: stur            w1, [x0, #0x1b]
    // 0xb32598: r1 = Instance_TextDirection
    //     0xb32598: ldr             x1, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xb3259c: StoreField: r0->field_1f = r1
    //     0xb3259c: stur            w1, [x0, #0x1f]
    // 0xb325a0: LeaveFrame
    //     0xb325a0: mov             SP, fp
    //     0xb325a4: ldp             fp, lr, [SP], #0x10
    // 0xb325a8: ret
    //     0xb325a8: ret             
    // 0xb325ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb325ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb325b0: b               #0xb32368
    // 0xb325b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb325b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb325b8: SaveReg d0
    //     0xb325b8: str             q0, [SP, #-0x10]!
    // 0xb325bc: stp             x0, x1, [SP, #-0x10]!
    // 0xb325c0: r0 = AllocateDouble()
    //     0xb325c0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb325c4: mov             x2, x0
    // 0xb325c8: ldp             x0, x1, [SP], #0x10
    // 0xb325cc: RestoreReg d0
    //     0xb325cc: ldr             q0, [SP], #0x10
    // 0xb325d0: b               #0xb323f8
    // 0xb325d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb325d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb325d8: SaveReg d0
    //     0xb325d8: str             q0, [SP, #-0x10]!
    // 0xb325dc: SaveReg r1
    //     0xb325dc: str             x1, [SP, #-8]!
    // 0xb325e0: r0 = AllocateDouble()
    //     0xb325e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb325e4: RestoreReg r1
    //     0xb325e4: ldr             x1, [SP], #8
    // 0xb325e8: RestoreReg d0
    //     0xb325e8: ldr             q0, [SP], #0x10
    // 0xb325ec: b               #0xb324e0
  }
  [closure] TabBarView <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xb327c0, size: 0x70c
    // 0xb327c0: EnterFrame
    //     0xb327c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb327c4: mov             fp, SP
    // 0xb327c8: AllocStack(0x48)
    //     0xb327c8: sub             SP, SP, #0x48
    // 0xb327cc: SetupParameters()
    //     0xb327cc: ldr             x0, [fp, #0x20]
    //     0xb327d0: ldur            w2, [x0, #0x17]
    //     0xb327d4: add             x2, x2, HEAP, lsl #32
    //     0xb327d8: stur            x2, [fp, #-8]
    // 0xb327dc: CheckStackOverflow
    //     0xb327dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb327e0: cmp             SP, x16
    //     0xb327e4: b.ls            #0xb32e08
    // 0xb327e8: LoadField: r1 = r2->field_f
    //     0xb327e8: ldur            w1, [x2, #0xf]
    // 0xb327ec: DecompressPointer r1
    //     0xb327ec: add             x1, x1, HEAP, lsl #32
    // 0xb327f0: r0 = controller()
    //     0xb327f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb327f4: LoadField: r1 = r0->field_2f
    //     0xb327f4: ldur            w1, [x0, #0x2f]
    // 0xb327f8: DecompressPointer r1
    //     0xb327f8: add             x1, x1, HEAP, lsl #32
    // 0xb327fc: r16 = Sentinel
    //     0xb327fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb32800: cmp             w1, w16
    // 0xb32804: b.eq            #0xb32e10
    // 0xb32808: stur            x1, [fp, #-0x10]
    // 0xb3280c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3280c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb32810: ldr             x0, [x0, #0x2670]
    //     0xb32814: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb32818: cmp             w0, w16
    //     0xb3281c: b.ne            #0xb32828
    //     0xb32820: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb32824: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb32828: r0 = GetNavigation.textTheme()
    //     0xb32828: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb3282c: LoadField: r1 = r0->field_27
    //     0xb3282c: ldur            w1, [x0, #0x27]
    // 0xb32830: DecompressPointer r1
    //     0xb32830: add             x1, x1, HEAP, lsl #32
    // 0xb32834: cmp             w1, NULL
    // 0xb32838: b.ne            #0xb32844
    // 0xb3283c: r1 = Null
    //     0xb3283c: mov             x1, NULL
    // 0xb32840: b               #0xb32890
    // 0xb32844: ldr             x0, [fp, #0x18]
    // 0xb32848: d0 = 0.750000
    //     0xb32848: fmov            d0, #0.75000000
    // 0xb3284c: LoadField: d1 = r0->field_f
    //     0xb3284c: ldur            d1, [x0, #0xf]
    // 0xb32850: fmul            d2, d1, d0
    // 0xb32854: r2 = inline_Allocate_Double()
    //     0xb32854: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb32858: add             x2, x2, #0x10
    //     0xb3285c: cmp             x3, x2
    //     0xb32860: b.ls            #0xb32e1c
    //     0xb32864: str             x2, [THR, #0x50]  ; THR::top
    //     0xb32868: sub             x2, x2, #0xf
    //     0xb3286c: movz            x3, #0xe15c
    //     0xb32870: movk            x3, #0x3, lsl #16
    //     0xb32874: stur            x3, [x2, #-1]
    // 0xb32878: StoreField: r2->field_7 = d2
    //     0xb32878: stur            d2, [x2, #7]
    // 0xb3287c: str             x2, [SP]
    // 0xb32880: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb32880: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb32884: ldr             x4, [x4, #0x88]
    // 0xb32888: r0 = copyWith()
    //     0xb32888: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3288c: mov             x1, x0
    // 0xb32890: ldur            x0, [fp, #-8]
    // 0xb32894: stur            x1, [fp, #-0x18]
    // 0xb32898: r0 = Text()
    //     0xb32898: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb3289c: mov             x2, x0
    // 0xb328a0: r0 = "Sumber: Kemenag RI/NU Online"
    //     0xb328a0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b600] "Sumber: Kemenag RI/NU Online"
    //     0xb328a4: ldr             x0, [x0, #0x600]
    // 0xb328a8: stur            x2, [fp, #-0x20]
    // 0xb328ac: StoreField: r2->field_b = r0
    //     0xb328ac: stur            w0, [x2, #0xb]
    // 0xb328b0: ldur            x1, [fp, #-0x18]
    // 0xb328b4: StoreField: r2->field_13 = r1
    //     0xb328b4: stur            w1, [x2, #0x13]
    // 0xb328b8: ldur            x3, [fp, #-8]
    // 0xb328bc: LoadField: r1 = r3->field_f
    //     0xb328bc: ldur            w1, [x3, #0xf]
    // 0xb328c0: DecompressPointer r1
    //     0xb328c0: add             x1, x1, HEAP, lsl #32
    // 0xb328c4: r0 = controller()
    //     0xb328c4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb328c8: LoadField: r1 = r0->field_53
    //     0xb328c8: ldur            w1, [x0, #0x53]
    // 0xb328cc: DecompressPointer r1
    //     0xb328cc: add             x1, x1, HEAP, lsl #32
    // 0xb328d0: r0 = value()
    //     0xb328d0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb328d4: cmp             w0, NULL
    // 0xb328d8: b.eq            #0xb32e38
    // 0xb328dc: LoadField: r1 = r0->field_43
    //     0xb328dc: ldur            w1, [x0, #0x43]
    // 0xb328e0: DecompressPointer r1
    //     0xb328e0: add             x1, x1, HEAP, lsl #32
    // 0xb328e4: stur            x1, [fp, #-0x18]
    // 0xb328e8: r0 = GetNavigation.textTheme()
    //     0xb328e8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb328ec: LoadField: r1 = r0->field_2f
    //     0xb328ec: ldur            w1, [x0, #0x2f]
    // 0xb328f0: DecompressPointer r1
    //     0xb328f0: add             x1, x1, HEAP, lsl #32
    // 0xb328f4: cmp             w1, NULL
    // 0xb328f8: b.ne            #0xb32904
    // 0xb328fc: r2 = Null
    //     0xb328fc: mov             x2, NULL
    // 0xb32900: b               #0xb32950
    // 0xb32904: ldr             x0, [fp, #0x18]
    // 0xb32908: LoadField: d0 = r0->field_f
    //     0xb32908: ldur            d0, [x0, #0xf]
    // 0xb3290c: r2 = inline_Allocate_Double()
    //     0xb3290c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb32910: add             x2, x2, #0x10
    //     0xb32914: cmp             x3, x2
    //     0xb32918: b.ls            #0xb32e3c
    //     0xb3291c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb32920: sub             x2, x2, #0xf
    //     0xb32924: movz            x3, #0xe15c
    //     0xb32928: movk            x3, #0x3, lsl #16
    //     0xb3292c: stur            x3, [x2, #-1]
    // 0xb32930: StoreField: r2->field_7 = d0
    //     0xb32930: stur            d0, [x2, #7]
    // 0xb32934: r16 = "Inter"
    //     0xb32934: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb32938: ldr             x16, [x16, #0xcb0]
    // 0xb3293c: stp             x16, x2, [SP]
    // 0xb32940: r4 = const [0, 0x3, 0x2, 0x1, fontFamily, 0x2, fontSize, 0x1, null]
    //     0xb32940: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b608] List(9) [0, 0x3, 0x2, 0x1, "fontFamily", 0x2, "fontSize", 0x1, Null]
    //     0xb32944: ldr             x4, [x4, #0x608]
    // 0xb32948: r0 = copyWith()
    //     0xb32948: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3294c: mov             x2, x0
    // 0xb32950: ldur            x1, [fp, #-0x20]
    // 0xb32954: ldur            x0, [fp, #-0x18]
    // 0xb32958: stur            x2, [fp, #-0x28]
    // 0xb3295c: r0 = Text()
    //     0xb3295c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32960: mov             x3, x0
    // 0xb32964: ldur            x0, [fp, #-0x18]
    // 0xb32968: stur            x3, [fp, #-0x30]
    // 0xb3296c: StoreField: r3->field_b = r0
    //     0xb3296c: stur            w0, [x3, #0xb]
    // 0xb32970: ldur            x0, [fp, #-0x28]
    // 0xb32974: StoreField: r3->field_13 = r0
    //     0xb32974: stur            w0, [x3, #0x13]
    // 0xb32978: r1 = Null
    //     0xb32978: mov             x1, NULL
    // 0xb3297c: r2 = 6
    //     0xb3297c: movz            x2, #0x6
    // 0xb32980: r0 = AllocateArray()
    //     0xb32980: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb32984: mov             x2, x0
    // 0xb32988: ldur            x0, [fp, #-0x20]
    // 0xb3298c: stur            x2, [fp, #-0x18]
    // 0xb32990: StoreField: r2->field_f = r0
    //     0xb32990: stur            w0, [x2, #0xf]
    // 0xb32994: r16 = Instance_SizedBox
    //     0xb32994: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb32998: ldr             x16, [x16, #0xfb0]
    // 0xb3299c: StoreField: r2->field_13 = r16
    //     0xb3299c: stur            w16, [x2, #0x13]
    // 0xb329a0: ldur            x0, [fp, #-0x30]
    // 0xb329a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb329a4: stur            w0, [x2, #0x17]
    // 0xb329a8: r1 = <Widget>
    //     0xb329a8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb329ac: r0 = AllocateGrowableArray()
    //     0xb329ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb329b0: mov             x1, x0
    // 0xb329b4: ldur            x0, [fp, #-0x18]
    // 0xb329b8: stur            x1, [fp, #-0x20]
    // 0xb329bc: StoreField: r1->field_f = r0
    //     0xb329bc: stur            w0, [x1, #0xf]
    // 0xb329c0: r2 = 6
    //     0xb329c0: movz            x2, #0x6
    // 0xb329c4: StoreField: r1->field_b = r2
    //     0xb329c4: stur            w2, [x1, #0xb]
    // 0xb329c8: r0 = ListView()
    //     0xb329c8: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb329cc: stur            x0, [fp, #-0x18]
    // 0xb329d0: r16 = Instance_EdgeInsets
    //     0xb329d0: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb329d4: str             x16, [SP]
    // 0xb329d8: mov             x1, x0
    // 0xb329dc: ldur            x2, [fp, #-0x20]
    // 0xb329e0: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb329e0: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb329e4: ldr             x4, [x4, #0xa0]
    // 0xb329e8: r0 = ListView()
    //     0xb329e8: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb329ec: r0 = GetNavigation.textTheme()
    //     0xb329ec: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb329f0: LoadField: r1 = r0->field_27
    //     0xb329f0: ldur            w1, [x0, #0x27]
    // 0xb329f4: DecompressPointer r1
    //     0xb329f4: add             x1, x1, HEAP, lsl #32
    // 0xb329f8: cmp             w1, NULL
    // 0xb329fc: b.ne            #0xb32a08
    // 0xb32a00: r1 = Null
    //     0xb32a00: mov             x1, NULL
    // 0xb32a04: b               #0xb32a54
    // 0xb32a08: ldr             x0, [fp, #0x18]
    // 0xb32a0c: d0 = 0.750000
    //     0xb32a0c: fmov            d0, #0.75000000
    // 0xb32a10: LoadField: d1 = r0->field_f
    //     0xb32a10: ldur            d1, [x0, #0xf]
    // 0xb32a14: fmul            d2, d1, d0
    // 0xb32a18: r2 = inline_Allocate_Double()
    //     0xb32a18: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb32a1c: add             x2, x2, #0x10
    //     0xb32a20: cmp             x3, x2
    //     0xb32a24: b.ls            #0xb32e58
    //     0xb32a28: str             x2, [THR, #0x50]  ; THR::top
    //     0xb32a2c: sub             x2, x2, #0xf
    //     0xb32a30: movz            x3, #0xe15c
    //     0xb32a34: movk            x3, #0x3, lsl #16
    //     0xb32a38: stur            x3, [x2, #-1]
    // 0xb32a3c: StoreField: r2->field_7 = d2
    //     0xb32a3c: stur            d2, [x2, #7]
    // 0xb32a40: str             x2, [SP]
    // 0xb32a44: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb32a44: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb32a48: ldr             x4, [x4, #0x88]
    // 0xb32a4c: r0 = copyWith()
    //     0xb32a4c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32a50: mov             x1, x0
    // 0xb32a54: ldur            x0, [fp, #-8]
    // 0xb32a58: stur            x1, [fp, #-0x20]
    // 0xb32a5c: r0 = Text()
    //     0xb32a5c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32a60: mov             x2, x0
    // 0xb32a64: r0 = "Sumber: Kemenag RI/NU Online"
    //     0xb32a64: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b600] "Sumber: Kemenag RI/NU Online"
    //     0xb32a68: ldr             x0, [x0, #0x600]
    // 0xb32a6c: stur            x2, [fp, #-0x28]
    // 0xb32a70: StoreField: r2->field_b = r0
    //     0xb32a70: stur            w0, [x2, #0xb]
    // 0xb32a74: ldur            x1, [fp, #-0x20]
    // 0xb32a78: StoreField: r2->field_13 = r1
    //     0xb32a78: stur            w1, [x2, #0x13]
    // 0xb32a7c: ldur            x3, [fp, #-8]
    // 0xb32a80: LoadField: r1 = r3->field_f
    //     0xb32a80: ldur            w1, [x3, #0xf]
    // 0xb32a84: DecompressPointer r1
    //     0xb32a84: add             x1, x1, HEAP, lsl #32
    // 0xb32a88: r0 = controller()
    //     0xb32a88: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb32a8c: LoadField: r1 = r0->field_53
    //     0xb32a8c: ldur            w1, [x0, #0x53]
    // 0xb32a90: DecompressPointer r1
    //     0xb32a90: add             x1, x1, HEAP, lsl #32
    // 0xb32a94: r0 = value()
    //     0xb32a94: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb32a98: cmp             w0, NULL
    // 0xb32a9c: b.eq            #0xb32e74
    // 0xb32aa0: LoadField: r1 = r0->field_3f
    //     0xb32aa0: ldur            w1, [x0, #0x3f]
    // 0xb32aa4: DecompressPointer r1
    //     0xb32aa4: add             x1, x1, HEAP, lsl #32
    // 0xb32aa8: stur            x1, [fp, #-0x20]
    // 0xb32aac: r0 = GetNavigation.textTheme()
    //     0xb32aac: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb32ab0: LoadField: r1 = r0->field_2f
    //     0xb32ab0: ldur            w1, [x0, #0x2f]
    // 0xb32ab4: DecompressPointer r1
    //     0xb32ab4: add             x1, x1, HEAP, lsl #32
    // 0xb32ab8: cmp             w1, NULL
    // 0xb32abc: b.ne            #0xb32ac8
    // 0xb32ac0: r2 = Null
    //     0xb32ac0: mov             x2, NULL
    // 0xb32ac4: b               #0xb32b14
    // 0xb32ac8: ldr             x0, [fp, #0x18]
    // 0xb32acc: LoadField: d0 = r0->field_f
    //     0xb32acc: ldur            d0, [x0, #0xf]
    // 0xb32ad0: r2 = inline_Allocate_Double()
    //     0xb32ad0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb32ad4: add             x2, x2, #0x10
    //     0xb32ad8: cmp             x3, x2
    //     0xb32adc: b.ls            #0xb32e78
    //     0xb32ae0: str             x2, [THR, #0x50]  ; THR::top
    //     0xb32ae4: sub             x2, x2, #0xf
    //     0xb32ae8: movz            x3, #0xe15c
    //     0xb32aec: movk            x3, #0x3, lsl #16
    //     0xb32af0: stur            x3, [x2, #-1]
    // 0xb32af4: StoreField: r2->field_7 = d0
    //     0xb32af4: stur            d0, [x2, #7]
    // 0xb32af8: r16 = "Inter"
    //     0xb32af8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb32afc: ldr             x16, [x16, #0xcb0]
    // 0xb32b00: stp             x16, x2, [SP]
    // 0xb32b04: r4 = const [0, 0x3, 0x2, 0x1, fontFamily, 0x2, fontSize, 0x1, null]
    //     0xb32b04: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b608] List(9) [0, 0x3, 0x2, 0x1, "fontFamily", 0x2, "fontSize", 0x1, Null]
    //     0xb32b08: ldr             x4, [x4, #0x608]
    // 0xb32b0c: r0 = copyWith()
    //     0xb32b0c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32b10: mov             x2, x0
    // 0xb32b14: ldur            x1, [fp, #-0x28]
    // 0xb32b18: ldur            x0, [fp, #-0x20]
    // 0xb32b1c: stur            x2, [fp, #-0x30]
    // 0xb32b20: r0 = Text()
    //     0xb32b20: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32b24: mov             x3, x0
    // 0xb32b28: ldur            x0, [fp, #-0x20]
    // 0xb32b2c: stur            x3, [fp, #-0x38]
    // 0xb32b30: StoreField: r3->field_b = r0
    //     0xb32b30: stur            w0, [x3, #0xb]
    // 0xb32b34: ldur            x0, [fp, #-0x30]
    // 0xb32b38: StoreField: r3->field_13 = r0
    //     0xb32b38: stur            w0, [x3, #0x13]
    // 0xb32b3c: r1 = Null
    //     0xb32b3c: mov             x1, NULL
    // 0xb32b40: r2 = 6
    //     0xb32b40: movz            x2, #0x6
    // 0xb32b44: r0 = AllocateArray()
    //     0xb32b44: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb32b48: mov             x2, x0
    // 0xb32b4c: ldur            x0, [fp, #-0x28]
    // 0xb32b50: stur            x2, [fp, #-0x20]
    // 0xb32b54: StoreField: r2->field_f = r0
    //     0xb32b54: stur            w0, [x2, #0xf]
    // 0xb32b58: r16 = Instance_SizedBox
    //     0xb32b58: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb32b5c: ldr             x16, [x16, #0xfb0]
    // 0xb32b60: StoreField: r2->field_13 = r16
    //     0xb32b60: stur            w16, [x2, #0x13]
    // 0xb32b64: ldur            x0, [fp, #-0x38]
    // 0xb32b68: ArrayStore: r2[0] = r0  ; List_4
    //     0xb32b68: stur            w0, [x2, #0x17]
    // 0xb32b6c: r1 = <Widget>
    //     0xb32b6c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb32b70: r0 = AllocateGrowableArray()
    //     0xb32b70: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb32b74: mov             x1, x0
    // 0xb32b78: ldur            x0, [fp, #-0x20]
    // 0xb32b7c: stur            x1, [fp, #-0x28]
    // 0xb32b80: StoreField: r1->field_f = r0
    //     0xb32b80: stur            w0, [x1, #0xf]
    // 0xb32b84: r2 = 6
    //     0xb32b84: movz            x2, #0x6
    // 0xb32b88: StoreField: r1->field_b = r2
    //     0xb32b88: stur            w2, [x1, #0xb]
    // 0xb32b8c: r0 = ListView()
    //     0xb32b8c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb32b90: stur            x0, [fp, #-0x20]
    // 0xb32b94: r16 = Instance_EdgeInsets
    //     0xb32b94: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb32b98: str             x16, [SP]
    // 0xb32b9c: mov             x1, x0
    // 0xb32ba0: ldur            x2, [fp, #-0x28]
    // 0xb32ba4: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb32ba4: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb32ba8: ldr             x4, [x4, #0xa0]
    // 0xb32bac: r0 = ListView()
    //     0xb32bac: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb32bb0: r0 = GetNavigation.textTheme()
    //     0xb32bb0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb32bb4: LoadField: r1 = r0->field_27
    //     0xb32bb4: ldur            w1, [x0, #0x27]
    // 0xb32bb8: DecompressPointer r1
    //     0xb32bb8: add             x1, x1, HEAP, lsl #32
    // 0xb32bbc: cmp             w1, NULL
    // 0xb32bc0: b.ne            #0xb32bcc
    // 0xb32bc4: r1 = Null
    //     0xb32bc4: mov             x1, NULL
    // 0xb32bc8: b               #0xb32c18
    // 0xb32bcc: ldr             x0, [fp, #0x18]
    // 0xb32bd0: d0 = 0.750000
    //     0xb32bd0: fmov            d0, #0.75000000
    // 0xb32bd4: LoadField: d1 = r0->field_f
    //     0xb32bd4: ldur            d1, [x0, #0xf]
    // 0xb32bd8: fmul            d2, d1, d0
    // 0xb32bdc: r2 = inline_Allocate_Double()
    //     0xb32bdc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb32be0: add             x2, x2, #0x10
    //     0xb32be4: cmp             x3, x2
    //     0xb32be8: b.ls            #0xb32e94
    //     0xb32bec: str             x2, [THR, #0x50]  ; THR::top
    //     0xb32bf0: sub             x2, x2, #0xf
    //     0xb32bf4: movz            x3, #0xe15c
    //     0xb32bf8: movk            x3, #0x3, lsl #16
    //     0xb32bfc: stur            x3, [x2, #-1]
    // 0xb32c00: StoreField: r2->field_7 = d2
    //     0xb32c00: stur            d2, [x2, #7]
    // 0xb32c04: str             x2, [SP]
    // 0xb32c08: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb32c08: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb32c0c: ldr             x4, [x4, #0x88]
    // 0xb32c10: r0 = copyWith()
    //     0xb32c10: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32c14: mov             x1, x0
    // 0xb32c18: ldur            x0, [fp, #-8]
    // 0xb32c1c: stur            x1, [fp, #-0x28]
    // 0xb32c20: r0 = Text()
    //     0xb32c20: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32c24: mov             x2, x0
    // 0xb32c28: r0 = "Sumber: Kemenag RI/NU Online"
    //     0xb32c28: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b600] "Sumber: Kemenag RI/NU Online"
    //     0xb32c2c: ldr             x0, [x0, #0x600]
    // 0xb32c30: stur            x2, [fp, #-0x30]
    // 0xb32c34: StoreField: r2->field_b = r0
    //     0xb32c34: stur            w0, [x2, #0xb]
    // 0xb32c38: ldur            x0, [fp, #-0x28]
    // 0xb32c3c: StoreField: r2->field_13 = r0
    //     0xb32c3c: stur            w0, [x2, #0x13]
    // 0xb32c40: ldur            x0, [fp, #-8]
    // 0xb32c44: LoadField: r1 = r0->field_f
    //     0xb32c44: ldur            w1, [x0, #0xf]
    // 0xb32c48: DecompressPointer r1
    //     0xb32c48: add             x1, x1, HEAP, lsl #32
    // 0xb32c4c: r0 = controller()
    //     0xb32c4c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb32c50: LoadField: r1 = r0->field_53
    //     0xb32c50: ldur            w1, [x0, #0x53]
    // 0xb32c54: DecompressPointer r1
    //     0xb32c54: add             x1, x1, HEAP, lsl #32
    // 0xb32c58: r0 = value()
    //     0xb32c58: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb32c5c: cmp             w0, NULL
    // 0xb32c60: b.eq            #0xb32eb0
    // 0xb32c64: LoadField: r1 = r0->field_3b
    //     0xb32c64: ldur            w1, [x0, #0x3b]
    // 0xb32c68: DecompressPointer r1
    //     0xb32c68: add             x1, x1, HEAP, lsl #32
    // 0xb32c6c: stur            x1, [fp, #-8]
    // 0xb32c70: r0 = GetNavigation.textTheme()
    //     0xb32c70: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb32c74: LoadField: r1 = r0->field_2f
    //     0xb32c74: ldur            w1, [x0, #0x2f]
    // 0xb32c78: DecompressPointer r1
    //     0xb32c78: add             x1, x1, HEAP, lsl #32
    // 0xb32c7c: cmp             w1, NULL
    // 0xb32c80: b.ne            #0xb32c8c
    // 0xb32c84: r5 = Null
    //     0xb32c84: mov             x5, NULL
    // 0xb32c88: b               #0xb32cd8
    // 0xb32c8c: ldr             x0, [fp, #0x18]
    // 0xb32c90: LoadField: d0 = r0->field_f
    //     0xb32c90: ldur            d0, [x0, #0xf]
    // 0xb32c94: r0 = inline_Allocate_Double()
    //     0xb32c94: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb32c98: add             x0, x0, #0x10
    //     0xb32c9c: cmp             x2, x0
    //     0xb32ca0: b.ls            #0xb32eb4
    //     0xb32ca4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb32ca8: sub             x0, x0, #0xf
    //     0xb32cac: movz            x2, #0xe15c
    //     0xb32cb0: movk            x2, #0x3, lsl #16
    //     0xb32cb4: stur            x2, [x0, #-1]
    // 0xb32cb8: StoreField: r0->field_7 = d0
    //     0xb32cb8: stur            d0, [x0, #7]
    // 0xb32cbc: r16 = "Inter"
    //     0xb32cbc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb32cc0: ldr             x16, [x16, #0xcb0]
    // 0xb32cc4: stp             x16, x0, [SP]
    // 0xb32cc8: r4 = const [0, 0x3, 0x2, 0x1, fontFamily, 0x2, fontSize, 0x1, null]
    //     0xb32cc8: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b608] List(9) [0, 0x3, 0x2, 0x1, "fontFamily", 0x2, "fontSize", 0x1, Null]
    //     0xb32ccc: ldr             x4, [x4, #0x608]
    // 0xb32cd0: r0 = copyWith()
    //     0xb32cd0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32cd4: mov             x5, x0
    // 0xb32cd8: ldur            x3, [fp, #-0x18]
    // 0xb32cdc: ldur            x2, [fp, #-0x20]
    // 0xb32ce0: ldur            x1, [fp, #-0x30]
    // 0xb32ce4: ldur            x0, [fp, #-8]
    // 0xb32ce8: ldur            x4, [fp, #-0x10]
    // 0xb32cec: stur            x5, [fp, #-0x28]
    // 0xb32cf0: r0 = Text()
    //     0xb32cf0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb32cf4: mov             x3, x0
    // 0xb32cf8: ldur            x0, [fp, #-8]
    // 0xb32cfc: stur            x3, [fp, #-0x38]
    // 0xb32d00: StoreField: r3->field_b = r0
    //     0xb32d00: stur            w0, [x3, #0xb]
    // 0xb32d04: ldur            x0, [fp, #-0x28]
    // 0xb32d08: StoreField: r3->field_13 = r0
    //     0xb32d08: stur            w0, [x3, #0x13]
    // 0xb32d0c: r1 = Null
    //     0xb32d0c: mov             x1, NULL
    // 0xb32d10: r2 = 6
    //     0xb32d10: movz            x2, #0x6
    // 0xb32d14: r0 = AllocateArray()
    //     0xb32d14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb32d18: mov             x2, x0
    // 0xb32d1c: ldur            x0, [fp, #-0x30]
    // 0xb32d20: stur            x2, [fp, #-8]
    // 0xb32d24: StoreField: r2->field_f = r0
    //     0xb32d24: stur            w0, [x2, #0xf]
    // 0xb32d28: r16 = Instance_SizedBox
    //     0xb32d28: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb32d2c: ldr             x16, [x16, #0xfb0]
    // 0xb32d30: StoreField: r2->field_13 = r16
    //     0xb32d30: stur            w16, [x2, #0x13]
    // 0xb32d34: ldur            x0, [fp, #-0x38]
    // 0xb32d38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb32d38: stur            w0, [x2, #0x17]
    // 0xb32d3c: r1 = <Widget>
    //     0xb32d3c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb32d40: r0 = AllocateGrowableArray()
    //     0xb32d40: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb32d44: mov             x1, x0
    // 0xb32d48: ldur            x0, [fp, #-8]
    // 0xb32d4c: stur            x1, [fp, #-0x28]
    // 0xb32d50: StoreField: r1->field_f = r0
    //     0xb32d50: stur            w0, [x1, #0xf]
    // 0xb32d54: r2 = 6
    //     0xb32d54: movz            x2, #0x6
    // 0xb32d58: StoreField: r1->field_b = r2
    //     0xb32d58: stur            w2, [x1, #0xb]
    // 0xb32d5c: r0 = ListView()
    //     0xb32d5c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb32d60: stur            x0, [fp, #-8]
    // 0xb32d64: r16 = Instance_EdgeInsets
    //     0xb32d64: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb32d68: str             x16, [SP]
    // 0xb32d6c: mov             x1, x0
    // 0xb32d70: ldur            x2, [fp, #-0x28]
    // 0xb32d74: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb32d74: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb32d78: ldr             x4, [x4, #0xa0]
    // 0xb32d7c: r0 = ListView()
    //     0xb32d7c: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb32d80: r1 = Null
    //     0xb32d80: mov             x1, NULL
    // 0xb32d84: r2 = 6
    //     0xb32d84: movz            x2, #0x6
    // 0xb32d88: r0 = AllocateArray()
    //     0xb32d88: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb32d8c: mov             x2, x0
    // 0xb32d90: ldur            x0, [fp, #-0x18]
    // 0xb32d94: stur            x2, [fp, #-0x28]
    // 0xb32d98: StoreField: r2->field_f = r0
    //     0xb32d98: stur            w0, [x2, #0xf]
    // 0xb32d9c: ldur            x0, [fp, #-0x20]
    // 0xb32da0: StoreField: r2->field_13 = r0
    //     0xb32da0: stur            w0, [x2, #0x13]
    // 0xb32da4: ldur            x0, [fp, #-8]
    // 0xb32da8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb32da8: stur            w0, [x2, #0x17]
    // 0xb32dac: r1 = <Widget>
    //     0xb32dac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb32db0: r0 = AllocateGrowableArray()
    //     0xb32db0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb32db4: mov             x1, x0
    // 0xb32db8: ldur            x0, [fp, #-0x28]
    // 0xb32dbc: stur            x1, [fp, #-8]
    // 0xb32dc0: StoreField: r1->field_f = r0
    //     0xb32dc0: stur            w0, [x1, #0xf]
    // 0xb32dc4: r0 = 6
    //     0xb32dc4: movz            x0, #0x6
    // 0xb32dc8: StoreField: r1->field_b = r0
    //     0xb32dc8: stur            w0, [x1, #0xb]
    // 0xb32dcc: r0 = TabBarView()
    //     0xb32dcc: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xb32dd0: ldur            x1, [fp, #-8]
    // 0xb32dd4: StoreField: r0->field_f = r1
    //     0xb32dd4: stur            w1, [x0, #0xf]
    // 0xb32dd8: ldur            x1, [fp, #-0x10]
    // 0xb32ddc: StoreField: r0->field_b = r1
    //     0xb32ddc: stur            w1, [x0, #0xb]
    // 0xb32de0: r1 = Instance_DragStartBehavior
    //     0xb32de0: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb32de4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb32de4: stur            w1, [x0, #0x17]
    // 0xb32de8: d0 = 1.000000
    //     0xb32de8: fmov            d0, #1.00000000
    // 0xb32dec: StoreField: r0->field_1b = d0
    //     0xb32dec: stur            d0, [x0, #0x1b]
    // 0xb32df0: r1 = Instance_Clip
    //     0xb32df0: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb32df4: ldr             x1, [x1, #0x7c0]
    // 0xb32df8: StoreField: r0->field_23 = r1
    //     0xb32df8: stur            w1, [x0, #0x23]
    // 0xb32dfc: LeaveFrame
    //     0xb32dfc: mov             SP, fp
    //     0xb32e00: ldp             fp, lr, [SP], #0x10
    // 0xb32e04: ret
    //     0xb32e04: ret             
    // 0xb32e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32e0c: b               #0xb327e8
    // 0xb32e10: r9 = tabController
    //     0xb32e10: add             x9, PP, #0x2b, lsl #12  ; [pp+0x2b5e0] Field <QuranTafsirController.tabController>: late (offset: 0x30)
    //     0xb32e14: ldr             x9, [x9, #0x5e0]
    // 0xb32e18: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb32e18: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb32e1c: stp             q0, q2, [SP, #-0x20]!
    // 0xb32e20: stp             x0, x1, [SP, #-0x10]!
    // 0xb32e24: r0 = AllocateDouble()
    //     0xb32e24: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32e28: mov             x2, x0
    // 0xb32e2c: ldp             x0, x1, [SP], #0x10
    // 0xb32e30: ldp             q0, q2, [SP], #0x20
    // 0xb32e34: b               #0xb32878
    // 0xb32e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32e38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32e3c: SaveReg d0
    //     0xb32e3c: str             q0, [SP, #-0x10]!
    // 0xb32e40: stp             x0, x1, [SP, #-0x10]!
    // 0xb32e44: r0 = AllocateDouble()
    //     0xb32e44: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32e48: mov             x2, x0
    // 0xb32e4c: ldp             x0, x1, [SP], #0x10
    // 0xb32e50: RestoreReg d0
    //     0xb32e50: ldr             q0, [SP], #0x10
    // 0xb32e54: b               #0xb32930
    // 0xb32e58: stp             q0, q2, [SP, #-0x20]!
    // 0xb32e5c: stp             x0, x1, [SP, #-0x10]!
    // 0xb32e60: r0 = AllocateDouble()
    //     0xb32e60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32e64: mov             x2, x0
    // 0xb32e68: ldp             x0, x1, [SP], #0x10
    // 0xb32e6c: ldp             q0, q2, [SP], #0x20
    // 0xb32e70: b               #0xb32a3c
    // 0xb32e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32e74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32e78: SaveReg d0
    //     0xb32e78: str             q0, [SP, #-0x10]!
    // 0xb32e7c: stp             x0, x1, [SP, #-0x10]!
    // 0xb32e80: r0 = AllocateDouble()
    //     0xb32e80: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32e84: mov             x2, x0
    // 0xb32e88: ldp             x0, x1, [SP], #0x10
    // 0xb32e8c: RestoreReg d0
    //     0xb32e8c: ldr             q0, [SP], #0x10
    // 0xb32e90: b               #0xb32af4
    // 0xb32e94: SaveReg d2
    //     0xb32e94: str             q2, [SP, #-0x10]!
    // 0xb32e98: stp             x0, x1, [SP, #-0x10]!
    // 0xb32e9c: r0 = AllocateDouble()
    //     0xb32e9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32ea0: mov             x2, x0
    // 0xb32ea4: ldp             x0, x1, [SP], #0x10
    // 0xb32ea8: RestoreReg d2
    //     0xb32ea8: ldr             q2, [SP], #0x10
    // 0xb32eac: b               #0xb32c00
    // 0xb32eb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32eb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32eb4: SaveReg d0
    //     0xb32eb4: str             q0, [SP, #-0x10]!
    // 0xb32eb8: SaveReg r1
    //     0xb32eb8: str             x1, [SP, #-8]!
    // 0xb32ebc: r0 = AllocateDouble()
    //     0xb32ebc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb32ec0: RestoreReg r1
    //     0xb32ec0: ldr             x1, [SP], #8
    // 0xb32ec4: RestoreReg d0
    //     0xb32ec4: ldr             q0, [SP], #0x10
    // 0xb32ec8: b               #0xb32cb8
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb3301c, size: 0x154
    // 0xb3301c: EnterFrame
    //     0xb3301c: stp             fp, lr, [SP, #-0x10]!
    //     0xb33020: mov             fp, SP
    // 0xb33024: AllocStack(0x30)
    //     0xb33024: sub             SP, SP, #0x30
    // 0xb33028: SetupParameters(QuranTafsirView this /* r1 */)
    //     0xb33028: stur            NULL, [fp, #-8]
    //     0xb3302c: movz            x0, #0
    //     0xb33030: add             x1, fp, w0, sxtw #2
    //     0xb33034: ldr             x1, [x1, #0x10]
    //     0xb33038: ldur            w2, [x1, #0x17]
    //     0xb3303c: add             x2, x2, HEAP, lsl #32
    //     0xb33040: stur            x2, [fp, #-0x10]
    // 0xb33044: CheckStackOverflow
    //     0xb33044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb33048: cmp             SP, x16
    //     0xb3304c: b.ls            #0xb33168
    // 0xb33050: InitAsync() -> Future<void?>
    //     0xb33050: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb33054: bl              #0x661298  ; InitAsyncStub
    // 0xb33058: ldur            x2, [fp, #-0x10]
    // 0xb3305c: LoadField: r1 = r2->field_f
    //     0xb3305c: ldur            w1, [x2, #0xf]
    // 0xb33060: DecompressPointer r1
    //     0xb33060: add             x1, x1, HEAP, lsl #32
    // 0xb33064: r0 = controller()
    //     0xb33064: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33068: mov             x1, x0
    // 0xb3306c: r0 = resetSelectedSurah()
    //     0xb3306c: bl              #0xb33170  ; [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::resetSelectedSurah
    // 0xb33070: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb33070: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb33074: ldr             x0, [x0, #0x2670]
    //     0xb33078: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3307c: cmp             w0, w16
    //     0xb33080: b.ne            #0xb3308c
    //     0xb33084: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb33088: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb3308c: r0 = Obx()
    //     0xb3308c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb33090: ldur            x2, [fp, #-0x10]
    // 0xb33094: r1 = Function '<anonymous closure>':.
    //     0xb33094: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b728] AnonymousClosure: (0xb33660), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb33098: ldr             x1, [x1, #0x728]
    // 0xb3309c: stur            x0, [fp, #-0x18]
    // 0xb330a0: r0 = AllocateClosure()
    //     0xb330a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb330a4: mov             x1, x0
    // 0xb330a8: ldur            x0, [fp, #-0x18]
    // 0xb330ac: StoreField: r0->field_b = r1
    //     0xb330ac: stur            w1, [x0, #0xb]
    // 0xb330b0: r1 = Null
    //     0xb330b0: mov             x1, NULL
    // 0xb330b4: r2 = 2
    //     0xb330b4: movz            x2, #0x2
    // 0xb330b8: r0 = AllocateArray()
    //     0xb330b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb330bc: mov             x2, x0
    // 0xb330c0: ldur            x0, [fp, #-0x18]
    // 0xb330c4: stur            x2, [fp, #-0x20]
    // 0xb330c8: StoreField: r2->field_f = r0
    //     0xb330c8: stur            w0, [x2, #0xf]
    // 0xb330cc: r1 = <Widget>
    //     0xb330cc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb330d0: r0 = AllocateGrowableArray()
    //     0xb330d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb330d4: mov             x1, x0
    // 0xb330d8: ldur            x0, [fp, #-0x20]
    // 0xb330dc: stur            x1, [fp, #-0x18]
    // 0xb330e0: StoreField: r1->field_f = r0
    //     0xb330e0: stur            w0, [x1, #0xf]
    // 0xb330e4: r0 = 2
    //     0xb330e4: movz            x0, #0x2
    // 0xb330e8: StoreField: r1->field_b = r0
    //     0xb330e8: stur            w0, [x1, #0xb]
    // 0xb330ec: r0 = NDialog()
    //     0xb330ec: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xb330f0: mov             x3, x0
    // 0xb330f4: r0 = "Pergi ke"
    //     0xb330f4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b730] "Pergi ke"
    //     0xb330f8: ldr             x0, [x0, #0x730]
    // 0xb330fc: stur            x3, [fp, #-0x20]
    // 0xb33100: StoreField: r3->field_b = r0
    //     0xb33100: stur            w0, [x3, #0xb]
    // 0xb33104: ldur            x2, [fp, #-0x10]
    // 0xb33108: r1 = Function '<anonymous closure>':.
    //     0xb33108: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b738] AnonymousClosure: (0xb332f4), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb3310c: ldr             x1, [x1, #0x738]
    // 0xb33110: r0 = AllocateClosure()
    //     0xb33110: bl              #0xec1630  ; AllocateClosureStub
    // 0xb33114: mov             x1, x0
    // 0xb33118: ldur            x0, [fp, #-0x20]
    // 0xb3311c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb3311c: stur            w1, [x0, #0x17]
    // 0xb33120: ldur            x1, [fp, #-0x18]
    // 0xb33124: StoreField: r0->field_f = r1
    //     0xb33124: stur            w1, [x0, #0xf]
    // 0xb33128: r1 = Function '<anonymous closure>':.
    //     0xb33128: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b740] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xb3312c: ldr             x1, [x1, #0x740]
    // 0xb33130: r2 = Null
    //     0xb33130: mov             x2, NULL
    // 0xb33134: r0 = AllocateClosure()
    //     0xb33134: bl              #0xec1630  ; AllocateClosureStub
    // 0xb33138: mov             x1, x0
    // 0xb3313c: ldur            x0, [fp, #-0x20]
    // 0xb33140: StoreField: r0->field_1b = r1
    //     0xb33140: stur            w1, [x0, #0x1b]
    // 0xb33144: r16 = <int>
    //     0xb33144: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb33148: stp             x0, x16, [SP]
    // 0xb3314c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3314c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb33150: r0 = ExtensionDialog.dialog()
    //     0xb33150: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xb33154: mov             x1, x0
    // 0xb33158: stur            x1, [fp, #-0x18]
    // 0xb3315c: r0 = Await()
    //     0xb3315c: bl              #0x661044  ; AwaitStub
    // 0xb33160: r0 = Null
    //     0xb33160: mov             x0, NULL
    // 0xb33164: r0 = ReturnAsyncNotFuture()
    //     0xb33164: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb33168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb33168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3316c: b               #0xb33050
  }
  [closure] Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0xb332f4, size: 0x4c
    // 0xb332f4: EnterFrame
    //     0xb332f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb332f8: mov             fp, SP
    // 0xb332fc: ldr             x0, [fp, #0x10]
    // 0xb33300: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb33300: ldur            w1, [x0, #0x17]
    // 0xb33304: DecompressPointer r1
    //     0xb33304: add             x1, x1, HEAP, lsl #32
    // 0xb33308: CheckStackOverflow
    //     0xb33308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3330c: cmp             SP, x16
    //     0xb33310: b.ls            #0xb33338
    // 0xb33314: LoadField: r0 = r1->field_f
    //     0xb33314: ldur            w0, [x1, #0xf]
    // 0xb33318: DecompressPointer r0
    //     0xb33318: add             x0, x0, HEAP, lsl #32
    // 0xb3331c: mov             x1, x0
    // 0xb33320: r0 = controller()
    //     0xb33320: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33324: mov             x1, x0
    // 0xb33328: r0 = jumpTo()
    //     0xb33328: bl              #0xb33340  ; [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::jumpTo
    // 0xb3332c: LeaveFrame
    //     0xb3332c: mov             SP, fp
    //     0xb33330: ldp             fp, lr, [SP], #0x10
    // 0xb33334: ret
    //     0xb33334: ret             
    // 0xb33338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb33338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3333c: b               #0xb33314
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb33660, size: 0x7dc
    // 0xb33660: EnterFrame
    //     0xb33660: stp             fp, lr, [SP, #-0x10]!
    //     0xb33664: mov             fp, SP
    // 0xb33668: AllocStack(0x68)
    //     0xb33668: sub             SP, SP, #0x68
    // 0xb3366c: SetupParameters()
    //     0xb3366c: ldr             x0, [fp, #0x10]
    //     0xb33670: ldur            w2, [x0, #0x17]
    //     0xb33674: add             x2, x2, HEAP, lsl #32
    //     0xb33678: stur            x2, [fp, #-8]
    // 0xb3367c: CheckStackOverflow
    //     0xb3367c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb33680: cmp             SP, x16
    //     0xb33684: b.ls            #0xb33e34
    // 0xb33688: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb33688: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3368c: ldr             x0, [x0, #0x2670]
    //     0xb33690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb33694: cmp             w0, w16
    //     0xb33698: b.ne            #0xb336a4
    //     0xb3369c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb336a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb336a4: r0 = GetNavigation.textTheme()
    //     0xb336a4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb336a8: LoadField: r1 = r0->field_27
    //     0xb336a8: ldur            w1, [x0, #0x27]
    // 0xb336ac: DecompressPointer r1
    //     0xb336ac: add             x1, x1, HEAP, lsl #32
    // 0xb336b0: cmp             w1, NULL
    // 0xb336b4: b.ne            #0xb336c0
    // 0xb336b8: r0 = Null
    //     0xb336b8: mov             x0, NULL
    // 0xb336bc: b               #0xb336d8
    // 0xb336c0: r16 = 14.000000
    //     0xb336c0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb336c4: ldr             x16, [x16, #0x9a0]
    // 0xb336c8: str             x16, [SP]
    // 0xb336cc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb336cc: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb336d0: ldr             x4, [x4, #0x88]
    // 0xb336d4: r0 = copyWith()
    //     0xb336d4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb336d8: ldur            x2, [fp, #-8]
    // 0xb336dc: stur            x0, [fp, #-0x10]
    // 0xb336e0: r0 = Text()
    //     0xb336e0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb336e4: mov             x2, x0
    // 0xb336e8: r0 = "Pilih Surah"
    //     0xb336e8: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b768] "Pilih Surah"
    //     0xb336ec: ldr             x0, [x0, #0x768]
    // 0xb336f0: stur            x2, [fp, #-0x18]
    // 0xb336f4: StoreField: r2->field_b = r0
    //     0xb336f4: stur            w0, [x2, #0xb]
    // 0xb336f8: ldur            x0, [fp, #-0x10]
    // 0xb336fc: StoreField: r2->field_13 = r0
    //     0xb336fc: stur            w0, [x2, #0x13]
    // 0xb33700: ldur            x0, [fp, #-8]
    // 0xb33704: LoadField: r1 = r0->field_f
    //     0xb33704: ldur            w1, [x0, #0xf]
    // 0xb33708: DecompressPointer r1
    //     0xb33708: add             x1, x1, HEAP, lsl #32
    // 0xb3370c: r0 = controller()
    //     0xb3370c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33710: LoadField: r1 = r0->field_4b
    //     0xb33710: ldur            w1, [x0, #0x4b]
    // 0xb33714: DecompressPointer r1
    //     0xb33714: add             x1, x1, HEAP, lsl #32
    // 0xb33718: stur            x1, [fp, #-0x10]
    // 0xb3371c: r0 = TextField()
    //     0xb3371c: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb33720: mov             x3, x0
    // 0xb33724: r0 = EditableText
    //     0xb33724: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb33728: stur            x3, [fp, #-0x20]
    // 0xb3372c: StoreField: r3->field_f = r0
    //     0xb3372c: stur            w0, [x3, #0xf]
    // 0xb33730: ldur            x1, [fp, #-0x10]
    // 0xb33734: StoreField: r3->field_13 = r1
    //     0xb33734: stur            w1, [x3, #0x13]
    // 0xb33738: r1 = Instance_InputDecoration
    //     0xb33738: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c0] Obj!InputDecoration@e14321
    //     0xb3373c: ldr             x1, [x1, #0x4c0]
    // 0xb33740: StoreField: r3->field_1b = r1
    //     0xb33740: stur            w1, [x3, #0x1b]
    // 0xb33744: r4 = Instance_TextCapitalization
    //     0xb33744: ldr             x4, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb33748: StoreField: r3->field_27 = r4
    //     0xb33748: stur            w4, [x3, #0x27]
    // 0xb3374c: r5 = Instance_TextAlign
    //     0xb3374c: ldr             x5, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb33750: StoreField: r3->field_33 = r5
    //     0xb33750: stur            w5, [x3, #0x33]
    // 0xb33754: r6 = true
    //     0xb33754: add             x6, NULL, #0x20  ; true
    // 0xb33758: StoreField: r3->field_6f = r6
    //     0xb33758: stur            w6, [x3, #0x6f]
    // 0xb3375c: r7 = false
    //     0xb3375c: add             x7, NULL, #0x30  ; false
    // 0xb33760: StoreField: r3->field_3f = r7
    //     0xb33760: stur            w7, [x3, #0x3f]
    // 0xb33764: r8 = "•"
    //     0xb33764: add             x8, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb33768: ldr             x8, [x8, #0x4c8]
    // 0xb3376c: StoreField: r3->field_47 = r8
    //     0xb3376c: stur            w8, [x3, #0x47]
    // 0xb33770: StoreField: r3->field_4b = r7
    //     0xb33770: stur            w7, [x3, #0x4b]
    // 0xb33774: StoreField: r3->field_4f = r6
    //     0xb33774: stur            w6, [x3, #0x4f]
    // 0xb33778: StoreField: r3->field_5b = r6
    //     0xb33778: stur            w6, [x3, #0x5b]
    // 0xb3377c: r9 = 1
    //     0xb3377c: movz            x9, #0x1
    // 0xb33780: StoreField: r3->field_5f = r9
    //     0xb33780: stur            x9, [x3, #0x5f]
    // 0xb33784: StoreField: r3->field_6b = r7
    //     0xb33784: stur            w7, [x3, #0x6b]
    // 0xb33788: d0 = 2.000000
    //     0xb33788: fmov            d0, #2.00000000
    // 0xb3378c: StoreField: r3->field_9f = d0
    //     0xb3378c: stur            d0, [x3, #0x9f]
    // 0xb33790: r10 = Instance_BoxHeightStyle
    //     0xb33790: ldr             x10, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb33794: StoreField: r3->field_bb = r10
    //     0xb33794: stur            w10, [x3, #0xbb]
    // 0xb33798: r11 = Instance_BoxWidthStyle
    //     0xb33798: ldr             x11, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb3379c: StoreField: r3->field_bf = r11
    //     0xb3379c: stur            w11, [x3, #0xbf]
    // 0xb337a0: r12 = Instance_EdgeInsets
    //     0xb337a0: ldr             x12, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb337a4: StoreField: r3->field_c7 = r12
    //     0xb337a4: stur            w12, [x3, #0xc7]
    // 0xb337a8: r13 = Instance_DragStartBehavior
    //     0xb337a8: ldr             x13, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb337ac: StoreField: r3->field_d3 = r13
    //     0xb337ac: stur            w13, [x3, #0xd3]
    // 0xb337b0: ldur            x2, [fp, #-8]
    // 0xb337b4: r1 = Function '<anonymous closure>':.
    //     0xb337b4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b770] AnonymousClosure: (0xb33e3c), in [package:nuonline/app/modules/quran/quran_tafsir/views/quran_tafsir_view.dart] QuranTafsirView::build (0xb319d8)
    //     0xb337b8: ldr             x1, [x1, #0x770]
    // 0xb337bc: r0 = AllocateClosure()
    //     0xb337bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb337c0: mov             x1, x0
    // 0xb337c4: ldur            x0, [fp, #-0x20]
    // 0xb337c8: StoreField: r0->field_d7 = r1
    //     0xb337c8: stur            w1, [x0, #0xd7]
    // 0xb337cc: r1 = false
    //     0xb337cc: add             x1, NULL, #0x30  ; false
    // 0xb337d0: StoreField: r0->field_db = r1
    //     0xb337d0: stur            w1, [x0, #0xdb]
    // 0xb337d4: r2 = const []
    //     0xb337d4: ldr             x2, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb337d8: StoreField: r0->field_f3 = r2
    //     0xb337d8: stur            w2, [x0, #0xf3]
    // 0xb337dc: r3 = Instance_Clip
    //     0xb337dc: add             x3, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb337e0: ldr             x3, [x3, #0x7c0]
    // 0xb337e4: StoreField: r0->field_f7 = r3
    //     0xb337e4: stur            w3, [x0, #0xf7]
    // 0xb337e8: r4 = true
    //     0xb337e8: add             x4, NULL, #0x20  ; true
    // 0xb337ec: StoreField: r0->field_ff = r4
    //     0xb337ec: stur            w4, [x0, #0xff]
    // 0xb337f0: r17 = 259
    //     0xb337f0: movz            x17, #0x103
    // 0xb337f4: str             w4, [x0, x17]
    // 0xb337f8: r5 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb337f8: add             x5, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb337fc: ldr             x5, [x5, #0x4d8]
    // 0xb33800: r17 = 267
    //     0xb33800: movz            x17, #0x10b
    // 0xb33804: str             w5, [x0, x17]
    // 0xb33808: r17 = 271
    //     0xb33808: movz            x17, #0x10f
    // 0xb3380c: str             w4, [x0, x17]
    // 0xb33810: r6 = Instance_SmartDashesType
    //     0xb33810: ldr             x6, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb33814: StoreField: r0->field_53 = r6
    //     0xb33814: stur            w6, [x0, #0x53]
    // 0xb33818: r7 = Instance_SmartQuotesType
    //     0xb33818: add             x7, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb3381c: ldr             x7, [x7, #0x4e0]
    // 0xb33820: StoreField: r0->field_57 = r7
    //     0xb33820: stur            w7, [x0, #0x57]
    // 0xb33824: r8 = Instance_TextInputType
    //     0xb33824: add             x8, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xb33828: ldr             x8, [x8, #0x4e8]
    // 0xb3382c: StoreField: r0->field_1f = r8
    //     0xb3382c: stur            w8, [x0, #0x1f]
    // 0xb33830: StoreField: r0->field_cb = r4
    //     0xb33830: stur            w4, [x0, #0xcb]
    // 0xb33834: r0 = UniqueKey()
    //     0xb33834: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xb33838: mov             x1, x0
    // 0xb3383c: ldur            x0, [fp, #-0x20]
    // 0xb33840: StoreField: r0->field_7 = r1
    //     0xb33840: stur            w1, [x0, #7]
    // 0xb33844: r1 = Null
    //     0xb33844: mov             x1, NULL
    // 0xb33848: r2 = 4
    //     0xb33848: movz            x2, #0x4
    // 0xb3384c: r0 = AllocateArray()
    //     0xb3384c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb33850: stur            x0, [fp, #-0x10]
    // 0xb33854: r16 = "Masukkan nomor ayat antara 1 - "
    //     0xb33854: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b778] "Masukkan nomor ayat antara 1 - "
    //     0xb33858: ldr             x16, [x16, #0x778]
    // 0xb3385c: StoreField: r0->field_f = r16
    //     0xb3385c: stur            w16, [x0, #0xf]
    // 0xb33860: ldur            x2, [fp, #-8]
    // 0xb33864: LoadField: r1 = r2->field_f
    //     0xb33864: ldur            w1, [x2, #0xf]
    // 0xb33868: DecompressPointer r1
    //     0xb33868: add             x1, x1, HEAP, lsl #32
    // 0xb3386c: r0 = controller()
    //     0xb3386c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33870: LoadField: r1 = r0->field_3f
    //     0xb33870: ldur            w1, [x0, #0x3f]
    // 0xb33874: DecompressPointer r1
    //     0xb33874: add             x1, x1, HEAP, lsl #32
    // 0xb33878: r0 = value()
    //     0xb33878: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb3387c: ldur            x1, [fp, #-0x10]
    // 0xb33880: ArrayStore: r1[1] = r0  ; List_4
    //     0xb33880: add             x25, x1, #0x13
    //     0xb33884: str             w0, [x25]
    //     0xb33888: tbz             w0, #0, #0xb338a4
    //     0xb3388c: ldurb           w16, [x1, #-1]
    //     0xb33890: ldurb           w17, [x0, #-1]
    //     0xb33894: and             x16, x17, x16, lsr #2
    //     0xb33898: tst             x16, HEAP, lsr #32
    //     0xb3389c: b.eq            #0xb338a4
    //     0xb338a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb338a4: ldur            x16, [fp, #-0x10]
    // 0xb338a8: str             x16, [SP]
    // 0xb338ac: r0 = _interpolate()
    //     0xb338ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb338b0: stur            x0, [fp, #-0x10]
    // 0xb338b4: r0 = GetNavigation.textTheme()
    //     0xb338b4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb338b8: LoadField: r1 = r0->field_27
    //     0xb338b8: ldur            w1, [x0, #0x27]
    // 0xb338bc: DecompressPointer r1
    //     0xb338bc: add             x1, x1, HEAP, lsl #32
    // 0xb338c0: cmp             w1, NULL
    // 0xb338c4: b.ne            #0xb338d0
    // 0xb338c8: r2 = Null
    //     0xb338c8: mov             x2, NULL
    // 0xb338cc: b               #0xb338ec
    // 0xb338d0: r16 = 14.000000
    //     0xb338d0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb338d4: ldr             x16, [x16, #0x9a0]
    // 0xb338d8: str             x16, [SP]
    // 0xb338dc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb338dc: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb338e0: ldr             x4, [x4, #0x88]
    // 0xb338e4: r0 = copyWith()
    //     0xb338e4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb338e8: mov             x2, x0
    // 0xb338ec: ldur            x1, [fp, #-8]
    // 0xb338f0: ldur            x0, [fp, #-0x10]
    // 0xb338f4: stur            x2, [fp, #-0x28]
    // 0xb338f8: r0 = Text()
    //     0xb338f8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb338fc: mov             x2, x0
    // 0xb33900: ldur            x0, [fp, #-0x10]
    // 0xb33904: stur            x2, [fp, #-0x30]
    // 0xb33908: StoreField: r2->field_b = r0
    //     0xb33908: stur            w0, [x2, #0xb]
    // 0xb3390c: ldur            x0, [fp, #-0x28]
    // 0xb33910: StoreField: r2->field_13 = r0
    //     0xb33910: stur            w0, [x2, #0x13]
    // 0xb33914: ldur            x0, [fp, #-8]
    // 0xb33918: LoadField: r1 = r0->field_f
    //     0xb33918: ldur            w1, [x0, #0xf]
    // 0xb3391c: DecompressPointer r1
    //     0xb3391c: add             x1, x1, HEAP, lsl #32
    // 0xb33920: r0 = controller()
    //     0xb33920: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33924: LoadField: r1 = r0->field_47
    //     0xb33924: ldur            w1, [x0, #0x47]
    // 0xb33928: DecompressPointer r1
    //     0xb33928: add             x1, x1, HEAP, lsl #32
    // 0xb3392c: stur            x1, [fp, #-0x10]
    // 0xb33930: r0 = InitLateStaticField(0x6ec) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb33930: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb33934: ldr             x0, [x0, #0xdd8]
    //     0xb33938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3393c: cmp             w0, w16
    //     0xb33940: b.ne            #0xb33950
    //     0xb33944: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b780] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0x6ec)
    //     0xb33948: ldr             x2, [x2, #0x780]
    //     0xb3394c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb33950: stur            x0, [fp, #-0x28]
    // 0xb33954: r0 = LengthLimitingTextInputFormatter()
    //     0xb33954: bl              #0xa0cd4c  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb33958: mov             x3, x0
    // 0xb3395c: r0 = 6
    //     0xb3395c: movz            x0, #0x6
    // 0xb33960: stur            x3, [fp, #-0x38]
    // 0xb33964: StoreField: r3->field_7 = r0
    //     0xb33964: stur            w0, [x3, #7]
    // 0xb33968: r1 = Null
    //     0xb33968: mov             x1, NULL
    // 0xb3396c: r2 = 4
    //     0xb3396c: movz            x2, #0x4
    // 0xb33970: r0 = AllocateArray()
    //     0xb33970: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb33974: mov             x2, x0
    // 0xb33978: ldur            x0, [fp, #-0x28]
    // 0xb3397c: stur            x2, [fp, #-0x40]
    // 0xb33980: StoreField: r2->field_f = r0
    //     0xb33980: stur            w0, [x2, #0xf]
    // 0xb33984: ldur            x0, [fp, #-0x38]
    // 0xb33988: StoreField: r2->field_13 = r0
    //     0xb33988: stur            w0, [x2, #0x13]
    // 0xb3398c: r1 = <TextInputFormatter>
    //     0xb3398c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b788] TypeArguments: <TextInputFormatter>
    //     0xb33990: ldr             x1, [x1, #0x788]
    // 0xb33994: r0 = AllocateGrowableArray()
    //     0xb33994: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb33998: mov             x3, x0
    // 0xb3399c: ldur            x0, [fp, #-0x40]
    // 0xb339a0: stur            x3, [fp, #-0x28]
    // 0xb339a4: StoreField: r3->field_f = r0
    //     0xb339a4: stur            w0, [x3, #0xf]
    // 0xb339a8: r0 = 4
    //     0xb339a8: movz            x0, #0x4
    // 0xb339ac: StoreField: r3->field_b = r0
    //     0xb339ac: stur            w0, [x3, #0xb]
    // 0xb339b0: mov             x2, x0
    // 0xb339b4: r1 = Null
    //     0xb339b4: mov             x1, NULL
    // 0xb339b8: r0 = AllocateArray()
    //     0xb339b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb339bc: stur            x0, [fp, #-0x38]
    // 0xb339c0: r16 = "1 - "
    //     0xb339c0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b790] "1 - "
    //     0xb339c4: ldr             x16, [x16, #0x790]
    // 0xb339c8: StoreField: r0->field_f = r16
    //     0xb339c8: stur            w16, [x0, #0xf]
    // 0xb339cc: ldur            x2, [fp, #-8]
    // 0xb339d0: LoadField: r1 = r2->field_f
    //     0xb339d0: ldur            w1, [x2, #0xf]
    // 0xb339d4: DecompressPointer r1
    //     0xb339d4: add             x1, x1, HEAP, lsl #32
    // 0xb339d8: r0 = controller()
    //     0xb339d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb339dc: LoadField: r1 = r0->field_3f
    //     0xb339dc: ldur            w1, [x0, #0x3f]
    // 0xb339e0: DecompressPointer r1
    //     0xb339e0: add             x1, x1, HEAP, lsl #32
    // 0xb339e4: r0 = value()
    //     0xb339e4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb339e8: ldur            x1, [fp, #-0x38]
    // 0xb339ec: ArrayStore: r1[1] = r0  ; List_4
    //     0xb339ec: add             x25, x1, #0x13
    //     0xb339f0: str             w0, [x25]
    //     0xb339f4: tbz             w0, #0, #0xb33a10
    //     0xb339f8: ldurb           w16, [x1, #-1]
    //     0xb339fc: ldurb           w17, [x0, #-1]
    //     0xb33a00: and             x16, x17, x16, lsr #2
    //     0xb33a04: tst             x16, HEAP, lsr #32
    //     0xb33a08: b.eq            #0xb33a10
    //     0xb33a0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb33a10: ldur            x16, [fp, #-0x38]
    // 0xb33a14: str             x16, [SP]
    // 0xb33a18: r0 = _interpolate()
    //     0xb33a18: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb33a1c: mov             x2, x0
    // 0xb33a20: ldur            x0, [fp, #-8]
    // 0xb33a24: stur            x2, [fp, #-0x38]
    // 0xb33a28: LoadField: r1 = r0->field_f
    //     0xb33a28: ldur            w1, [x0, #0xf]
    // 0xb33a2c: DecompressPointer r1
    //     0xb33a2c: add             x1, x1, HEAP, lsl #32
    // 0xb33a30: r0 = controller()
    //     0xb33a30: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33a34: LoadField: r1 = r0->field_3b
    //     0xb33a34: ldur            w1, [x0, #0x3b]
    // 0xb33a38: DecompressPointer r1
    //     0xb33a38: add             x1, x1, HEAP, lsl #32
    // 0xb33a3c: r0 = value()
    //     0xb33a3c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb33a40: cmp             w0, NULL
    // 0xb33a44: b.eq            #0xb33a50
    // 0xb33a48: r7 = ""
    //     0xb33a48: ldr             x7, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb33a4c: b               #0xb33a54
    // 0xb33a50: r7 = Null
    //     0xb33a50: mov             x7, NULL
    // 0xb33a54: ldur            x0, [fp, #-8]
    // 0xb33a58: ldur            x6, [fp, #-0x18]
    // 0xb33a5c: ldur            x5, [fp, #-0x20]
    // 0xb33a60: ldur            x4, [fp, #-0x30]
    // 0xb33a64: ldur            x3, [fp, #-0x10]
    // 0xb33a68: ldur            x1, [fp, #-0x38]
    // 0xb33a6c: ldur            x2, [fp, #-0x28]
    // 0xb33a70: stur            x7, [fp, #-0x40]
    // 0xb33a74: r0 = InputDecoration()
    //     0xb33a74: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb33a78: mov             x1, x0
    // 0xb33a7c: ldur            x0, [fp, #-0x38]
    // 0xb33a80: stur            x1, [fp, #-0x48]
    // 0xb33a84: StoreField: r1->field_2f = r0
    //     0xb33a84: stur            w0, [x1, #0x2f]
    // 0xb33a88: r0 = true
    //     0xb33a88: add             x0, NULL, #0x20  ; true
    // 0xb33a8c: StoreField: r1->field_43 = r0
    //     0xb33a8c: stur            w0, [x1, #0x43]
    // 0xb33a90: ldur            x2, [fp, #-0x40]
    // 0xb33a94: StoreField: r1->field_4b = r2
    //     0xb33a94: stur            w2, [x1, #0x4b]
    // 0xb33a98: r2 = Instance_TextStyle
    //     0xb33a98: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b798] Obj!TextStyle@e1ba41
    //     0xb33a9c: ldr             x2, [x2, #0x798]
    // 0xb33aa0: StoreField: r1->field_4f = r2
    //     0xb33aa0: stur            w2, [x1, #0x4f]
    // 0xb33aa4: StoreField: r1->field_cf = r0
    //     0xb33aa4: stur            w0, [x1, #0xcf]
    // 0xb33aa8: r0 = TextField()
    //     0xb33aa8: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb33aac: mov             x3, x0
    // 0xb33ab0: r0 = EditableText
    //     0xb33ab0: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb33ab4: stur            x3, [fp, #-0x38]
    // 0xb33ab8: StoreField: r3->field_f = r0
    //     0xb33ab8: stur            w0, [x3, #0xf]
    // 0xb33abc: ldur            x0, [fp, #-0x10]
    // 0xb33ac0: StoreField: r3->field_13 = r0
    //     0xb33ac0: stur            w0, [x3, #0x13]
    // 0xb33ac4: ldur            x0, [fp, #-0x48]
    // 0xb33ac8: StoreField: r3->field_1b = r0
    //     0xb33ac8: stur            w0, [x3, #0x1b]
    // 0xb33acc: r0 = Instance_TextCapitalization
    //     0xb33acc: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb33ad0: StoreField: r3->field_27 = r0
    //     0xb33ad0: stur            w0, [x3, #0x27]
    // 0xb33ad4: r0 = Instance_TextAlign
    //     0xb33ad4: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb33ad8: StoreField: r3->field_33 = r0
    //     0xb33ad8: stur            w0, [x3, #0x33]
    // 0xb33adc: r0 = false
    //     0xb33adc: add             x0, NULL, #0x30  ; false
    // 0xb33ae0: StoreField: r3->field_6f = r0
    //     0xb33ae0: stur            w0, [x3, #0x6f]
    // 0xb33ae4: StoreField: r3->field_3f = r0
    //     0xb33ae4: stur            w0, [x3, #0x3f]
    // 0xb33ae8: r1 = "•"
    //     0xb33ae8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb33aec: ldr             x1, [x1, #0x4c8]
    // 0xb33af0: StoreField: r3->field_47 = r1
    //     0xb33af0: stur            w1, [x3, #0x47]
    // 0xb33af4: StoreField: r3->field_4b = r0
    //     0xb33af4: stur            w0, [x3, #0x4b]
    // 0xb33af8: r1 = true
    //     0xb33af8: add             x1, NULL, #0x20  ; true
    // 0xb33afc: StoreField: r3->field_4f = r1
    //     0xb33afc: stur            w1, [x3, #0x4f]
    // 0xb33b00: StoreField: r3->field_5b = r1
    //     0xb33b00: stur            w1, [x3, #0x5b]
    // 0xb33b04: r2 = 1
    //     0xb33b04: movz            x2, #0x1
    // 0xb33b08: StoreField: r3->field_5f = r2
    //     0xb33b08: stur            x2, [x3, #0x5f]
    // 0xb33b0c: StoreField: r3->field_6b = r0
    //     0xb33b0c: stur            w0, [x3, #0x6b]
    // 0xb33b10: ldur            x2, [fp, #-0x28]
    // 0xb33b14: StoreField: r3->field_93 = r2
    //     0xb33b14: stur            w2, [x3, #0x93]
    // 0xb33b18: d0 = 2.000000
    //     0xb33b18: fmov            d0, #2.00000000
    // 0xb33b1c: StoreField: r3->field_9f = d0
    //     0xb33b1c: stur            d0, [x3, #0x9f]
    // 0xb33b20: r2 = Instance_BoxHeightStyle
    //     0xb33b20: ldr             x2, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb33b24: StoreField: r3->field_bb = r2
    //     0xb33b24: stur            w2, [x3, #0xbb]
    // 0xb33b28: r2 = Instance_BoxWidthStyle
    //     0xb33b28: ldr             x2, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb33b2c: StoreField: r3->field_bf = r2
    //     0xb33b2c: stur            w2, [x3, #0xbf]
    // 0xb33b30: r2 = Instance_EdgeInsets
    //     0xb33b30: ldr             x2, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb33b34: StoreField: r3->field_c7 = r2
    //     0xb33b34: stur            w2, [x3, #0xc7]
    // 0xb33b38: r2 = Instance_DragStartBehavior
    //     0xb33b38: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb33b3c: StoreField: r3->field_d3 = r2
    //     0xb33b3c: stur            w2, [x3, #0xd3]
    // 0xb33b40: StoreField: r3->field_db = r0
    //     0xb33b40: stur            w0, [x3, #0xdb]
    // 0xb33b44: r0 = const []
    //     0xb33b44: ldr             x0, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb33b48: StoreField: r3->field_f3 = r0
    //     0xb33b48: stur            w0, [x3, #0xf3]
    // 0xb33b4c: r0 = Instance_Clip
    //     0xb33b4c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb33b50: ldr             x0, [x0, #0x7c0]
    // 0xb33b54: StoreField: r3->field_f7 = r0
    //     0xb33b54: stur            w0, [x3, #0xf7]
    // 0xb33b58: StoreField: r3->field_ff = r1
    //     0xb33b58: stur            w1, [x3, #0xff]
    // 0xb33b5c: r17 = 259
    //     0xb33b5c: movz            x17, #0x103
    // 0xb33b60: str             w1, [x3, x17]
    // 0xb33b64: r0 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb33b64: add             x0, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb33b68: ldr             x0, [x0, #0x4d8]
    // 0xb33b6c: r17 = 267
    //     0xb33b6c: movz            x17, #0x10b
    // 0xb33b70: str             w0, [x3, x17]
    // 0xb33b74: r17 = 271
    //     0xb33b74: movz            x17, #0x10f
    // 0xb33b78: str             w1, [x3, x17]
    // 0xb33b7c: r0 = Instance_SmartDashesType
    //     0xb33b7c: ldr             x0, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb33b80: StoreField: r3->field_53 = r0
    //     0xb33b80: stur            w0, [x3, #0x53]
    // 0xb33b84: r0 = Instance_SmartQuotesType
    //     0xb33b84: add             x0, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb33b88: ldr             x0, [x0, #0x4e0]
    // 0xb33b8c: StoreField: r3->field_57 = r0
    //     0xb33b8c: stur            w0, [x3, #0x57]
    // 0xb33b90: r0 = Instance_TextInputType
    //     0xb33b90: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b7a0] Obj!TextInputType@e10dd1
    //     0xb33b94: ldr             x0, [x0, #0x7a0]
    // 0xb33b98: StoreField: r3->field_1f = r0
    //     0xb33b98: stur            w0, [x3, #0x1f]
    // 0xb33b9c: StoreField: r3->field_cb = r1
    //     0xb33b9c: stur            w1, [x3, #0xcb]
    // 0xb33ba0: r1 = Null
    //     0xb33ba0: mov             x1, NULL
    // 0xb33ba4: r2 = 16
    //     0xb33ba4: movz            x2, #0x10
    // 0xb33ba8: r0 = AllocateArray()
    //     0xb33ba8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb33bac: mov             x2, x0
    // 0xb33bb0: ldur            x0, [fp, #-0x18]
    // 0xb33bb4: stur            x2, [fp, #-0x10]
    // 0xb33bb8: StoreField: r2->field_f = r0
    //     0xb33bb8: stur            w0, [x2, #0xf]
    // 0xb33bbc: r16 = Instance_SizedBox
    //     0xb33bbc: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb33bc0: ldr             x16, [x16, #0x4a0]
    // 0xb33bc4: StoreField: r2->field_13 = r16
    //     0xb33bc4: stur            w16, [x2, #0x13]
    // 0xb33bc8: ldur            x0, [fp, #-0x20]
    // 0xb33bcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb33bcc: stur            w0, [x2, #0x17]
    // 0xb33bd0: r16 = Instance_SizedBox
    //     0xb33bd0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb33bd4: ldr             x16, [x16, #0xfe8]
    // 0xb33bd8: StoreField: r2->field_1b = r16
    //     0xb33bd8: stur            w16, [x2, #0x1b]
    // 0xb33bdc: ldur            x0, [fp, #-0x30]
    // 0xb33be0: StoreField: r2->field_1f = r0
    //     0xb33be0: stur            w0, [x2, #0x1f]
    // 0xb33be4: r16 = Instance_SizedBox
    //     0xb33be4: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb33be8: ldr             x16, [x16, #0x4a0]
    // 0xb33bec: StoreField: r2->field_23 = r16
    //     0xb33bec: stur            w16, [x2, #0x23]
    // 0xb33bf0: ldur            x0, [fp, #-0x38]
    // 0xb33bf4: StoreField: r2->field_27 = r0
    //     0xb33bf4: stur            w0, [x2, #0x27]
    // 0xb33bf8: r16 = Instance_SizedBox
    //     0xb33bf8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7a8] Obj!SizedBox@e1e2c1
    //     0xb33bfc: ldr             x16, [x16, #0x7a8]
    // 0xb33c00: StoreField: r2->field_2b = r16
    //     0xb33c00: stur            w16, [x2, #0x2b]
    // 0xb33c04: r1 = <Widget>
    //     0xb33c04: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb33c08: r0 = AllocateGrowableArray()
    //     0xb33c08: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb33c0c: mov             x2, x0
    // 0xb33c10: ldur            x0, [fp, #-0x10]
    // 0xb33c14: stur            x2, [fp, #-0x18]
    // 0xb33c18: StoreField: r2->field_f = r0
    //     0xb33c18: stur            w0, [x2, #0xf]
    // 0xb33c1c: r0 = 16
    //     0xb33c1c: movz            x0, #0x10
    // 0xb33c20: StoreField: r2->field_b = r0
    //     0xb33c20: stur            w0, [x2, #0xb]
    // 0xb33c24: ldur            x0, [fp, #-8]
    // 0xb33c28: LoadField: r1 = r0->field_f
    //     0xb33c28: ldur            w1, [x0, #0xf]
    // 0xb33c2c: DecompressPointer r1
    //     0xb33c2c: add             x1, x1, HEAP, lsl #32
    // 0xb33c30: r0 = controller()
    //     0xb33c30: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33c34: LoadField: r1 = r0->field_3b
    //     0xb33c34: ldur            w1, [x0, #0x3b]
    // 0xb33c38: DecompressPointer r1
    //     0xb33c38: add             x1, x1, HEAP, lsl #32
    // 0xb33c3c: r0 = value()
    //     0xb33c3c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb33c40: cmp             w0, NULL
    // 0xb33c44: b.eq            #0xb33db0
    // 0xb33c48: ldur            x0, [fp, #-8]
    // 0xb33c4c: LoadField: r1 = r0->field_f
    //     0xb33c4c: ldur            w1, [x0, #0xf]
    // 0xb33c50: DecompressPointer r1
    //     0xb33c50: add             x1, x1, HEAP, lsl #32
    // 0xb33c54: r0 = controller()
    //     0xb33c54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33c58: LoadField: r1 = r0->field_3b
    //     0xb33c58: ldur            w1, [x0, #0x3b]
    // 0xb33c5c: DecompressPointer r1
    //     0xb33c5c: add             x1, x1, HEAP, lsl #32
    // 0xb33c60: r0 = value()
    //     0xb33c60: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb33c64: r1 = 60
    //     0xb33c64: movz            x1, #0x3c
    // 0xb33c68: branchIfSmi(r0, 0xb33c74)
    //     0xb33c68: tbz             w0, #0, #0xb33c74
    // 0xb33c6c: r1 = LoadClassIdInstr(r0)
    //     0xb33c6c: ldur            x1, [x0, #-1]
    //     0xb33c70: ubfx            x1, x1, #0xc, #0x14
    // 0xb33c74: str             x0, [SP]
    // 0xb33c78: mov             x0, x1
    // 0xb33c7c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb33c7c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb33c80: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb33c80: movz            x17, #0x2b03
    //     0xb33c84: add             lr, x0, x17
    //     0xb33c88: ldr             lr, [x21, lr, lsl #3]
    //     0xb33c8c: blr             lr
    // 0xb33c90: stur            x0, [fp, #-8]
    // 0xb33c94: r0 = GetNavigation.textTheme()
    //     0xb33c94: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb33c98: LoadField: r3 = r0->field_27
    //     0xb33c98: ldur            w3, [x0, #0x27]
    // 0xb33c9c: DecompressPointer r3
    //     0xb33c9c: add             x3, x3, HEAP, lsl #32
    // 0xb33ca0: stur            x3, [fp, #-0x10]
    // 0xb33ca4: cmp             w3, NULL
    // 0xb33ca8: b.ne            #0xb33cb4
    // 0xb33cac: r2 = Null
    //     0xb33cac: mov             x2, NULL
    // 0xb33cb0: b               #0xb33d0c
    // 0xb33cb4: r1 = _ConstMap len:3
    //     0xb33cb4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb33cb8: ldr             x1, [x1, #0xcd0]
    // 0xb33cbc: r2 = 6
    //     0xb33cbc: movz            x2, #0x6
    // 0xb33cc0: r0 = []()
    //     0xb33cc0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb33cc4: r1 = _ConstMap len:3
    //     0xb33cc4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xb33cc8: ldr             x1, [x1, #0xcd0]
    // 0xb33ccc: r2 = 4
    //     0xb33ccc: movz            x2, #0x4
    // 0xb33cd0: stur            x0, [fp, #-0x20]
    // 0xb33cd4: r0 = []()
    //     0xb33cd4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb33cd8: r16 = <Color?>
    //     0xb33cd8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb33cdc: ldr             x16, [x16, #0x98]
    // 0xb33ce0: stp             x0, x16, [SP, #8]
    // 0xb33ce4: ldur            x16, [fp, #-0x20]
    // 0xb33ce8: str             x16, [SP]
    // 0xb33cec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb33cec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb33cf0: r0 = mode()
    //     0xb33cf0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb33cf4: str             x0, [SP]
    // 0xb33cf8: ldur            x1, [fp, #-0x10]
    // 0xb33cfc: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb33cfc: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb33d00: ldr             x4, [x4, #0x228]
    // 0xb33d04: r0 = copyWith()
    //     0xb33d04: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb33d08: mov             x2, x0
    // 0xb33d0c: ldur            x0, [fp, #-8]
    // 0xb33d10: ldur            x1, [fp, #-0x18]
    // 0xb33d14: stur            x2, [fp, #-0x10]
    // 0xb33d18: r0 = Text()
    //     0xb33d18: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb33d1c: mov             x2, x0
    // 0xb33d20: ldur            x0, [fp, #-8]
    // 0xb33d24: stur            x2, [fp, #-0x20]
    // 0xb33d28: StoreField: r2->field_b = r0
    //     0xb33d28: stur            w0, [x2, #0xb]
    // 0xb33d2c: ldur            x0, [fp, #-0x10]
    // 0xb33d30: StoreField: r2->field_13 = r0
    //     0xb33d30: stur            w0, [x2, #0x13]
    // 0xb33d34: ldur            x0, [fp, #-0x18]
    // 0xb33d38: LoadField: r1 = r0->field_b
    //     0xb33d38: ldur            w1, [x0, #0xb]
    // 0xb33d3c: LoadField: r3 = r0->field_f
    //     0xb33d3c: ldur            w3, [x0, #0xf]
    // 0xb33d40: DecompressPointer r3
    //     0xb33d40: add             x3, x3, HEAP, lsl #32
    // 0xb33d44: LoadField: r4 = r3->field_b
    //     0xb33d44: ldur            w4, [x3, #0xb]
    // 0xb33d48: r3 = LoadInt32Instr(r1)
    //     0xb33d48: sbfx            x3, x1, #1, #0x1f
    // 0xb33d4c: stur            x3, [fp, #-0x50]
    // 0xb33d50: r1 = LoadInt32Instr(r4)
    //     0xb33d50: sbfx            x1, x4, #1, #0x1f
    // 0xb33d54: cmp             x3, x1
    // 0xb33d58: b.ne            #0xb33d64
    // 0xb33d5c: mov             x1, x0
    // 0xb33d60: r0 = _growToNextCapacity()
    //     0xb33d60: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb33d64: ldur            x2, [fp, #-0x18]
    // 0xb33d68: ldur            x3, [fp, #-0x50]
    // 0xb33d6c: add             x0, x3, #1
    // 0xb33d70: lsl             x1, x0, #1
    // 0xb33d74: StoreField: r2->field_b = r1
    //     0xb33d74: stur            w1, [x2, #0xb]
    // 0xb33d78: LoadField: r1 = r2->field_f
    //     0xb33d78: ldur            w1, [x2, #0xf]
    // 0xb33d7c: DecompressPointer r1
    //     0xb33d7c: add             x1, x1, HEAP, lsl #32
    // 0xb33d80: ldur            x0, [fp, #-0x20]
    // 0xb33d84: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb33d84: add             x25, x1, x3, lsl #2
    //     0xb33d88: add             x25, x25, #0xf
    //     0xb33d8c: str             w0, [x25]
    //     0xb33d90: tbz             w0, #0, #0xb33dac
    //     0xb33d94: ldurb           w16, [x1, #-1]
    //     0xb33d98: ldurb           w17, [x0, #-1]
    //     0xb33d9c: and             x16, x17, x16, lsr #2
    //     0xb33da0: tst             x16, HEAP, lsr #32
    //     0xb33da4: b.eq            #0xb33dac
    //     0xb33da8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb33dac: b               #0xb33db4
    // 0xb33db0: ldur            x2, [fp, #-0x18]
    // 0xb33db4: r0 = Column()
    //     0xb33db4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb33db8: mov             x1, x0
    // 0xb33dbc: r0 = Instance_Axis
    //     0xb33dbc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb33dc0: stur            x1, [fp, #-8]
    // 0xb33dc4: StoreField: r1->field_f = r0
    //     0xb33dc4: stur            w0, [x1, #0xf]
    // 0xb33dc8: r0 = Instance_MainAxisAlignment
    //     0xb33dc8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb33dcc: ldr             x0, [x0, #0x730]
    // 0xb33dd0: StoreField: r1->field_13 = r0
    //     0xb33dd0: stur            w0, [x1, #0x13]
    // 0xb33dd4: r0 = Instance_MainAxisSize
    //     0xb33dd4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb33dd8: ldr             x0, [x0, #0x738]
    // 0xb33ddc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb33ddc: stur            w0, [x1, #0x17]
    // 0xb33de0: r0 = Instance_CrossAxisAlignment
    //     0xb33de0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb33de4: ldr             x0, [x0, #0x68]
    // 0xb33de8: StoreField: r1->field_1b = r0
    //     0xb33de8: stur            w0, [x1, #0x1b]
    // 0xb33dec: r0 = Instance_VerticalDirection
    //     0xb33dec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb33df0: ldr             x0, [x0, #0x748]
    // 0xb33df4: StoreField: r1->field_23 = r0
    //     0xb33df4: stur            w0, [x1, #0x23]
    // 0xb33df8: r0 = Instance_Clip
    //     0xb33df8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb33dfc: ldr             x0, [x0, #0x750]
    // 0xb33e00: StoreField: r1->field_2b = r0
    //     0xb33e00: stur            w0, [x1, #0x2b]
    // 0xb33e04: StoreField: r1->field_2f = rZR
    //     0xb33e04: stur            xzr, [x1, #0x2f]
    // 0xb33e08: ldur            x0, [fp, #-0x18]
    // 0xb33e0c: StoreField: r1->field_b = r0
    //     0xb33e0c: stur            w0, [x1, #0xb]
    // 0xb33e10: r0 = Padding()
    //     0xb33e10: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33e14: r1 = Instance_EdgeInsets
    //     0xb33e14: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b7b0] Obj!EdgeInsets@e12be1
    //     0xb33e18: ldr             x1, [x1, #0x7b0]
    // 0xb33e1c: StoreField: r0->field_f = r1
    //     0xb33e1c: stur            w1, [x0, #0xf]
    // 0xb33e20: ldur            x1, [fp, #-8]
    // 0xb33e24: StoreField: r0->field_b = r1
    //     0xb33e24: stur            w1, [x0, #0xb]
    // 0xb33e28: LeaveFrame
    //     0xb33e28: mov             SP, fp
    //     0xb33e2c: ldp             fp, lr, [SP], #0x10
    // 0xb33e30: ret
    //     0xb33e30: ret             
    // 0xb33e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb33e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb33e38: b               #0xb33688
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb33e3c, size: 0x108
    // 0xb33e3c: EnterFrame
    //     0xb33e3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb33e40: mov             fp, SP
    // 0xb33e44: AllocStack(0x30)
    //     0xb33e44: sub             SP, SP, #0x30
    // 0xb33e48: SetupParameters(QuranTafsirView this /* r1 */)
    //     0xb33e48: stur            NULL, [fp, #-8]
    //     0xb33e4c: movz            x0, #0
    //     0xb33e50: add             x1, fp, w0, sxtw #2
    //     0xb33e54: ldr             x1, [x1, #0x10]
    //     0xb33e58: ldur            w2, [x1, #0x17]
    //     0xb33e5c: add             x2, x2, HEAP, lsl #32
    //     0xb33e60: stur            x2, [fp, #-0x10]
    // 0xb33e64: CheckStackOverflow
    //     0xb33e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb33e68: cmp             SP, x16
    //     0xb33e6c: b.ls            #0xb33f3c
    // 0xb33e70: InitAsync() -> Future<void?>
    //     0xb33e70: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb33e74: bl              #0x661298  ; InitAsyncStub
    // 0xb33e78: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb33e78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb33e7c: ldr             x0, [x0, #0x2670]
    //     0xb33e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb33e84: cmp             w0, w16
    //     0xb33e88: b.ne            #0xb33e94
    //     0xb33e8c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb33e90: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb33e94: r16 = "/quran/quran-surah"
    //     0xb33e94: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b7b8] "/quran/quran-surah"
    //     0xb33e98: ldr             x16, [x16, #0x7b8]
    // 0xb33e9c: stp             x16, NULL, [SP, #8]
    // 0xb33ea0: r16 = false
    //     0xb33ea0: add             x16, NULL, #0x30  ; false
    // 0xb33ea4: str             x16, [SP]
    // 0xb33ea8: r4 = const [0x1, 0x2, 0x2, 0x1, preventDuplicates, 0x1, null]
    //     0xb33ea8: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b7c0] List(7) [0x1, 0x2, 0x2, 0x1, "preventDuplicates", 0x1, Null]
    //     0xb33eac: ldr             x4, [x4, #0x7c0]
    // 0xb33eb0: r0 = GetNavigation.toNamed()
    //     0xb33eb0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb33eb4: mov             x1, x0
    // 0xb33eb8: stur            x1, [fp, #-0x18]
    // 0xb33ebc: r0 = Await()
    //     0xb33ebc: bl              #0x661044  ; AwaitStub
    // 0xb33ec0: stur            x0, [fp, #-0x18]
    // 0xb33ec4: cmp             w0, NULL
    // 0xb33ec8: b.eq            #0xb33f34
    // 0xb33ecc: ldur            x1, [fp, #-0x10]
    // 0xb33ed0: LoadField: r2 = r1->field_f
    //     0xb33ed0: ldur            w2, [x1, #0xf]
    // 0xb33ed4: DecompressPointer r2
    //     0xb33ed4: add             x2, x2, HEAP, lsl #32
    // 0xb33ed8: mov             x1, x2
    // 0xb33edc: r0 = controller()
    //     0xb33edc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb33ee0: mov             x3, x0
    // 0xb33ee4: ldur            x0, [fp, #-0x18]
    // 0xb33ee8: r2 = Null
    //     0xb33ee8: mov             x2, NULL
    // 0xb33eec: r1 = Null
    //     0xb33eec: mov             x1, NULL
    // 0xb33ef0: stur            x3, [fp, #-0x10]
    // 0xb33ef4: branchIfSmi(r0, 0xb33f1c)
    //     0xb33ef4: tbz             w0, #0, #0xb33f1c
    // 0xb33ef8: r4 = LoadClassIdInstr(r0)
    //     0xb33ef8: ldur            x4, [x0, #-1]
    //     0xb33efc: ubfx            x4, x4, #0xc, #0x14
    // 0xb33f00: sub             x4, x4, #0x3c
    // 0xb33f04: cmp             x4, #1
    // 0xb33f08: b.ls            #0xb33f1c
    // 0xb33f0c: r8 = int
    //     0xb33f0c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xb33f10: r3 = Null
    //     0xb33f10: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b7c8] Null
    //     0xb33f14: ldr             x3, [x3, #0x7c8]
    // 0xb33f18: r0 = int()
    //     0xb33f18: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xb33f1c: ldur            x0, [fp, #-0x18]
    // 0xb33f20: r2 = LoadInt32Instr(r0)
    //     0xb33f20: sbfx            x2, x0, #1, #0x1f
    //     0xb33f24: tbz             w0, #0, #0xb33f2c
    //     0xb33f28: ldur            x2, [x0, #7]
    // 0xb33f2c: ldur            x1, [fp, #-0x10]
    // 0xb33f30: r0 = setSelectedSurah()
    //     0xb33f30: bl              #0xb331c0  ; [package:nuonline/app/modules/quran/quran_tafsir/controllers/quran_tafsir_controller.dart] QuranTafsirController::setSelectedSurah
    // 0xb33f34: r0 = Null
    //     0xb33f34: mov             x0, NULL
    // 0xb33f38: r0 = ReturnAsyncNotFuture()
    //     0xb33f38: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb33f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb33f3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb33f40: b               #0xb33e70
  }
}
