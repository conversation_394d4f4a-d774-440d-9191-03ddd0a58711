// lib: , url: package:nuonline/app/modules/quran/widgets/quran_verses.dart

// class id: 1050478, size: 0x8
class :: {
}

// class id: 4956, size: 0x38, field offset: 0xc
//   const constructor, 
class <PERSON><PERSON><PERSON><PERSON> extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xba7d08, size: 0x158
    // 0xba7d08: EnterFrame
    //     0xba7d08: stp             fp, lr, [SP, #-0x10]!
    //     0xba7d0c: mov             fp, SP
    // 0xba7d10: AllocStack(0x38)
    //     0xba7d10: sub             SP, SP, #0x38
    // 0xba7d14: SetupParameters(QuranVerses this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xba7d14: stur            x1, [fp, #-8]
    //     0xba7d18: stur            x2, [fp, #-0x10]
    // 0xba7d1c: CheckStackOverflow
    //     0xba7d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba7d20: cmp             SP, x16
    //     0xba7d24: b.ls            #0xba7e58
    // 0xba7d28: r1 = 4
    //     0xba7d28: movz            x1, #0x4
    // 0xba7d2c: r0 = AllocateContext()
    //     0xba7d2c: bl              #0xec126c  ; AllocateContextStub
    // 0xba7d30: mov             x3, x0
    // 0xba7d34: ldur            x0, [fp, #-8]
    // 0xba7d38: stur            x3, [fp, #-0x18]
    // 0xba7d3c: StoreField: r3->field_f = r0
    //     0xba7d3c: stur            w0, [x3, #0xf]
    // 0xba7d40: ldur            x1, [fp, #-0x10]
    // 0xba7d44: StoreField: r3->field_13 = r1
    //     0xba7d44: stur            w1, [x3, #0x13]
    // 0xba7d48: r1 = <Widget>
    //     0xba7d48: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xba7d4c: r2 = 0
    //     0xba7d4c: movz            x2, #0
    // 0xba7d50: r0 = _GrowableList()
    //     0xba7d50: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xba7d54: mov             x4, x0
    // 0xba7d58: ldur            x3, [fp, #-0x18]
    // 0xba7d5c: stur            x4, [fp, #-0x20]
    // 0xba7d60: ArrayStore: r3[0] = r0  ; List_4
    //     0xba7d60: stur            w0, [x3, #0x17]
    //     0xba7d64: ldurb           w16, [x3, #-1]
    //     0xba7d68: ldurb           w17, [x0, #-1]
    //     0xba7d6c: and             x16, x17, x16, lsr #2
    //     0xba7d70: tst             x16, HEAP, lsr #32
    //     0xba7d74: b.eq            #0xba7d7c
    //     0xba7d78: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba7d7c: ldur            x0, [fp, #-8]
    // 0xba7d80: LoadField: r5 = r0->field_b
    //     0xba7d80: ldur            w5, [x0, #0xb]
    // 0xba7d84: DecompressPointer r5
    //     0xba7d84: add             x5, x5, HEAP, lsl #32
    // 0xba7d88: stur            x5, [fp, #-0x10]
    // 0xba7d8c: r1 = Function '<anonymous closure>':.
    //     0xba7d8c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33010] AnonymousClosure: (0xba9c1c), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba7d90: ldr             x1, [x1, #0x10]
    // 0xba7d94: r2 = Null
    //     0xba7d94: mov             x2, NULL
    // 0xba7d98: r0 = AllocateClosure()
    //     0xba7d98: bl              #0xec1630  ; AllocateClosureStub
    // 0xba7d9c: r16 = <Verse, int, Verse>
    //     0xba7d9c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33018] TypeArguments: <Verse, int, Verse>
    //     0xba7da0: ldr             x16, [x16, #0x18]
    // 0xba7da4: ldur            lr, [fp, #-0x10]
    // 0xba7da8: stp             lr, x16, [SP, #8]
    // 0xba7dac: str             x0, [SP]
    // 0xba7db0: r4 = const [0x3, 0x2, 0x2, 0x2, null]
    //     0xba7db0: ldr             x4, [PP, #0x1930]  ; [pp+0x1930] List(5) [0x3, 0x2, 0x2, 0x2, Null]
    // 0xba7db4: r0 = IterableSC.groupBy()
    //     0xba7db4: bl              #0xba7e60  ; [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.groupBy
    // 0xba7db8: mov             x3, x0
    // 0xba7dbc: ldur            x2, [fp, #-0x18]
    // 0xba7dc0: stur            x3, [fp, #-8]
    // 0xba7dc4: StoreField: r2->field_1b = r0
    //     0xba7dc4: stur            w0, [x2, #0x1b]
    //     0xba7dc8: ldurb           w16, [x2, #-1]
    //     0xba7dcc: ldurb           w17, [x0, #-1]
    //     0xba7dd0: and             x16, x17, x16, lsr #2
    //     0xba7dd4: tst             x16, HEAP, lsr #32
    //     0xba7dd8: b.eq            #0xba7de0
    //     0xba7ddc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xba7de0: r1 = Function '<anonymous closure>':.
    //     0xba7de0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33020] AnonymousClosure: (0xba8168), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba7de4: ldr             x1, [x1, #0x20]
    // 0xba7de8: r0 = AllocateClosure()
    //     0xba7de8: bl              #0xec1630  ; AllocateClosureStub
    // 0xba7dec: ldur            x1, [fp, #-8]
    // 0xba7df0: mov             x2, x0
    // 0xba7df4: r0 = forEach()
    //     0xba7df4: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xba7df8: r0 = Column()
    //     0xba7df8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba7dfc: r1 = Instance_Axis
    //     0xba7dfc: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xba7e00: StoreField: r0->field_f = r1
    //     0xba7e00: stur            w1, [x0, #0xf]
    // 0xba7e04: r1 = Instance_MainAxisAlignment
    //     0xba7e04: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xba7e08: ldr             x1, [x1, #0x730]
    // 0xba7e0c: StoreField: r0->field_13 = r1
    //     0xba7e0c: stur            w1, [x0, #0x13]
    // 0xba7e10: r1 = Instance_MainAxisSize
    //     0xba7e10: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xba7e14: ldr             x1, [x1, #0xe88]
    // 0xba7e18: ArrayStore: r0[0] = r1  ; List_4
    //     0xba7e18: stur            w1, [x0, #0x17]
    // 0xba7e1c: r1 = Instance_CrossAxisAlignment
    //     0xba7e1c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xba7e20: ldr             x1, [x1, #0x740]
    // 0xba7e24: StoreField: r0->field_1b = r1
    //     0xba7e24: stur            w1, [x0, #0x1b]
    // 0xba7e28: r1 = Instance_VerticalDirection
    //     0xba7e28: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xba7e2c: ldr             x1, [x1, #0x748]
    // 0xba7e30: StoreField: r0->field_23 = r1
    //     0xba7e30: stur            w1, [x0, #0x23]
    // 0xba7e34: r1 = Instance_Clip
    //     0xba7e34: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xba7e38: ldr             x1, [x1, #0x750]
    // 0xba7e3c: StoreField: r0->field_2b = r1
    //     0xba7e3c: stur            w1, [x0, #0x2b]
    // 0xba7e40: StoreField: r0->field_2f = rZR
    //     0xba7e40: stur            xzr, [x0, #0x2f]
    // 0xba7e44: ldur            x1, [fp, #-0x20]
    // 0xba7e48: StoreField: r0->field_b = r1
    //     0xba7e48: stur            w1, [x0, #0xb]
    // 0xba7e4c: LeaveFrame
    //     0xba7e4c: mov             SP, fp
    //     0xba7e50: ldp             fp, lr, [SP], #0x10
    // 0xba7e54: ret
    //     0xba7e54: ret             
    // 0xba7e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba7e58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba7e5c: b               #0xba7d28
  }
  [closure] void <anonymous closure>(dynamic, int, List<Verse>) {
    // ** addr: 0xba8168, size: 0x5f4
    // 0xba8168: EnterFrame
    //     0xba8168: stp             fp, lr, [SP, #-0x10]!
    //     0xba816c: mov             fp, SP
    // 0xba8170: AllocStack(0x48)
    //     0xba8170: sub             SP, SP, #0x48
    // 0xba8174: SetupParameters()
    //     0xba8174: ldr             x0, [fp, #0x20]
    //     0xba8178: ldur            w1, [x0, #0x17]
    //     0xba817c: add             x1, x1, HEAP, lsl #32
    //     0xba8180: stur            x1, [fp, #-8]
    // 0xba8184: CheckStackOverflow
    //     0xba8184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba8188: cmp             SP, x16
    //     0xba818c: b.ls            #0xba8754
    // 0xba8190: r1 = 1
    //     0xba8190: movz            x1, #0x1
    // 0xba8194: r0 = AllocateContext()
    //     0xba8194: bl              #0xec126c  ; AllocateContextStub
    // 0xba8198: mov             x3, x0
    // 0xba819c: ldur            x0, [fp, #-8]
    // 0xba81a0: stur            x3, [fp, #-0x10]
    // 0xba81a4: StoreField: r3->field_b = r0
    //     0xba81a4: stur            w0, [x3, #0xb]
    // 0xba81a8: ldr             x1, [fp, #0x18]
    // 0xba81ac: StoreField: r3->field_f = r1
    //     0xba81ac: stur            w1, [x3, #0xf]
    // 0xba81b0: r1 = Function '<anonymous closure>':.
    //     0xba81b0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33028] AnonymousClosure: (0xafb240), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xba81b4: ldr             x1, [x1, #0x28]
    // 0xba81b8: r2 = Null
    //     0xba81b8: mov             x2, NULL
    // 0xba81bc: r0 = AllocateClosure()
    //     0xba81bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xba81c0: ldr             x2, [fp, #0x10]
    // 0xba81c4: r1 = LoadClassIdInstr(r2)
    //     0xba81c4: ldur            x1, [x2, #-1]
    //     0xba81c8: ubfx            x1, x1, #0xc, #0x14
    // 0xba81cc: str             x0, [SP]
    // 0xba81d0: mov             x0, x1
    // 0xba81d4: mov             x1, x2
    // 0xba81d8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xba81d8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xba81dc: r0 = GDT[cid_x0 + 0x133e4]()
    //     0xba81dc: movz            x17, #0x33e4
    //     0xba81e0: movk            x17, #0x1, lsl #16
    //     0xba81e4: add             lr, x0, x17
    //     0xba81e8: ldr             lr, [x21, lr, lsl #3]
    //     0xba81ec: blr             lr
    // 0xba81f0: ldr             x2, [fp, #0x10]
    // 0xba81f4: r0 = LoadClassIdInstr(r2)
    //     0xba81f4: ldur            x0, [x2, #-1]
    //     0xba81f8: ubfx            x0, x0, #0xc, #0x14
    // 0xba81fc: mov             x1, x2
    // 0xba8200: r0 = GDT[cid_x0 + 0xd20f]()
    //     0xba8200: movz            x17, #0xd20f
    //     0xba8204: add             lr, x0, x17
    //     0xba8208: ldr             lr, [x21, lr, lsl #3]
    //     0xba820c: blr             lr
    // 0xba8210: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xba8210: ldur            x1, [x0, #0x17]
    // 0xba8214: cmp             x1, #1
    // 0xba8218: b.ne            #0xba83dc
    // 0xba821c: ldur            x0, [fp, #-0x10]
    // 0xba8220: LoadField: r2 = r0->field_f
    //     0xba8220: ldur            w2, [x0, #0xf]
    // 0xba8224: DecompressPointer r2
    //     0xba8224: add             x2, x2, HEAP, lsl #32
    // 0xba8228: r1 = const [0x4, 0xa, 0x16, 0x17, 0x18, 0x1a, 0x1b, 0x20, 0x21, 0x25, 0x26, 0x2d, 0x2f, 0x35, 0x3c, 0x40, 0x41, 0x50, 0x52, 0x56, 0x5b]
    //     0xba8228: add             x1, PP, #0x33, lsl #12  ; [pp+0x33030] List<int>(21)
    //     0xba822c: ldr             x1, [x1, #0x30]
    // 0xba8230: r0 = contains()
    //     0xba8230: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xba8234: tbz             w0, #4, #0xba8358
    // 0xba8238: ldur            x3, [fp, #-8]
    // 0xba823c: ldur            x0, [fp, #-0x10]
    // 0xba8240: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xba8240: ldur            w4, [x3, #0x17]
    // 0xba8244: DecompressPointer r4
    //     0xba8244: add             x4, x4, HEAP, lsl #32
    // 0xba8248: stur            x4, [fp, #-0x18]
    // 0xba824c: LoadField: r2 = r0->field_f
    //     0xba824c: ldur            w2, [x0, #0xf]
    // 0xba8250: DecompressPointer r2
    //     0xba8250: add             x2, x2, HEAP, lsl #32
    // 0xba8254: LoadField: r1 = r3->field_f
    //     0xba8254: ldur            w1, [x3, #0xf]
    // 0xba8258: DecompressPointer r1
    //     0xba8258: add             x1, x1, HEAP, lsl #32
    // 0xba825c: LoadField: r5 = r1->field_f
    //     0xba825c: ldur            w5, [x1, #0xf]
    // 0xba8260: DecompressPointer r5
    //     0xba8260: add             x5, x5, HEAP, lsl #32
    // 0xba8264: ArrayLoad: r1 = r5[0]  ; List_4
    //     0xba8264: ldur            w1, [x5, #0x17]
    // 0xba8268: DecompressPointer r1
    //     0xba8268: add             x1, x1, HEAP, lsl #32
    // 0xba826c: r0 = findSurah()
    //     0xba826c: bl              #0x8c23e0  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::findSurah
    // 0xba8270: r1 = <Surah>
    //     0xba8270: add             x1, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0xba8274: ldr             x1, [x1, #0x1e0]
    // 0xba8278: stur            x0, [fp, #-0x20]
    // 0xba827c: r0 = FutureBuilder()
    //     0xba827c: bl              #0xa2edc4  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xba8280: mov             x3, x0
    // 0xba8284: ldur            x0, [fp, #-0x20]
    // 0xba8288: stur            x3, [fp, #-0x28]
    // 0xba828c: StoreField: r3->field_f = r0
    //     0xba828c: stur            w0, [x3, #0xf]
    // 0xba8290: r1 = Function '<anonymous closure>':.
    //     0xba8290: add             x1, PP, #0x33, lsl #12  ; [pp+0x33038] AnonymousClosure: (0xba9be8), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba8294: ldr             x1, [x1, #0x38]
    // 0xba8298: r2 = Null
    //     0xba8298: mov             x2, NULL
    // 0xba829c: r0 = AllocateClosure()
    //     0xba829c: bl              #0xec1630  ; AllocateClosureStub
    // 0xba82a0: ldur            x3, [fp, #-0x28]
    // 0xba82a4: StoreField: r3->field_13 = r0
    //     0xba82a4: stur            w0, [x3, #0x13]
    // 0xba82a8: ldur            x4, [fp, #-0x18]
    // 0xba82ac: LoadField: r2 = r4->field_7
    //     0xba82ac: ldur            w2, [x4, #7]
    // 0xba82b0: DecompressPointer r2
    //     0xba82b0: add             x2, x2, HEAP, lsl #32
    // 0xba82b4: mov             x0, x3
    // 0xba82b8: r1 = Null
    //     0xba82b8: mov             x1, NULL
    // 0xba82bc: cmp             w2, NULL
    // 0xba82c0: b.eq            #0xba82e0
    // 0xba82c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xba82c4: ldur            w4, [x2, #0x17]
    // 0xba82c8: DecompressPointer r4
    //     0xba82c8: add             x4, x4, HEAP, lsl #32
    // 0xba82cc: r8 = X0
    //     0xba82cc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xba82d0: LoadField: r9 = r4->field_7
    //     0xba82d0: ldur            x9, [x4, #7]
    // 0xba82d4: r3 = Null
    //     0xba82d4: add             x3, PP, #0x33, lsl #12  ; [pp+0x33040] Null
    //     0xba82d8: ldr             x3, [x3, #0x40]
    // 0xba82dc: blr             x9
    // 0xba82e0: ldur            x0, [fp, #-0x18]
    // 0xba82e4: LoadField: r1 = r0->field_b
    //     0xba82e4: ldur            w1, [x0, #0xb]
    // 0xba82e8: LoadField: r2 = r0->field_f
    //     0xba82e8: ldur            w2, [x0, #0xf]
    // 0xba82ec: DecompressPointer r2
    //     0xba82ec: add             x2, x2, HEAP, lsl #32
    // 0xba82f0: LoadField: r3 = r2->field_b
    //     0xba82f0: ldur            w3, [x2, #0xb]
    // 0xba82f4: r2 = LoadInt32Instr(r1)
    //     0xba82f4: sbfx            x2, x1, #1, #0x1f
    // 0xba82f8: stur            x2, [fp, #-0x30]
    // 0xba82fc: r1 = LoadInt32Instr(r3)
    //     0xba82fc: sbfx            x1, x3, #1, #0x1f
    // 0xba8300: cmp             x2, x1
    // 0xba8304: b.ne            #0xba8310
    // 0xba8308: mov             x1, x0
    // 0xba830c: r0 = _growToNextCapacity()
    //     0xba830c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xba8310: ldur            x0, [fp, #-0x18]
    // 0xba8314: ldur            x2, [fp, #-0x30]
    // 0xba8318: add             x1, x2, #1
    // 0xba831c: lsl             x3, x1, #1
    // 0xba8320: StoreField: r0->field_b = r3
    //     0xba8320: stur            w3, [x0, #0xb]
    // 0xba8324: LoadField: r1 = r0->field_f
    //     0xba8324: ldur            w1, [x0, #0xf]
    // 0xba8328: DecompressPointer r1
    //     0xba8328: add             x1, x1, HEAP, lsl #32
    // 0xba832c: ldur            x0, [fp, #-0x28]
    // 0xba8330: ArrayStore: r1[r2] = r0  ; List_4
    //     0xba8330: add             x25, x1, x2, lsl #2
    //     0xba8334: add             x25, x25, #0xf
    //     0xba8338: str             w0, [x25]
    //     0xba833c: tbz             w0, #0, #0xba8358
    //     0xba8340: ldurb           w16, [x1, #-1]
    //     0xba8344: ldurb           w17, [x0, #-1]
    //     0xba8348: and             x16, x17, x16, lsr #2
    //     0xba834c: tst             x16, HEAP, lsr #32
    //     0xba8350: b.eq            #0xba8358
    //     0xba8354: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xba8358: ldur            x0, [fp, #-0x10]
    // 0xba835c: LoadField: r2 = r0->field_f
    //     0xba835c: ldur            w2, [x0, #0xf]
    // 0xba8360: DecompressPointer r2
    //     0xba8360: add             x2, x2, HEAP, lsl #32
    // 0xba8364: r1 = const [0x1, 0x9]
    //     0xba8364: add             x1, PP, #0x33, lsl #12  ; [pp+0x33050] List<int>(2)
    //     0xba8368: ldr             x1, [x1, #0x50]
    // 0xba836c: r0 = contains()
    //     0xba836c: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xba8370: tbz             w0, #4, #0xba83dc
    // 0xba8374: ldur            x0, [fp, #-8]
    // 0xba8378: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xba8378: ldur            w2, [x0, #0x17]
    // 0xba837c: DecompressPointer r2
    //     0xba837c: add             x2, x2, HEAP, lsl #32
    // 0xba8380: stur            x2, [fp, #-0x18]
    // 0xba8384: LoadField: r1 = r2->field_b
    //     0xba8384: ldur            w1, [x2, #0xb]
    // 0xba8388: LoadField: r3 = r2->field_f
    //     0xba8388: ldur            w3, [x2, #0xf]
    // 0xba838c: DecompressPointer r3
    //     0xba838c: add             x3, x3, HEAP, lsl #32
    // 0xba8390: LoadField: r4 = r3->field_b
    //     0xba8390: ldur            w4, [x3, #0xb]
    // 0xba8394: r3 = LoadInt32Instr(r1)
    //     0xba8394: sbfx            x3, x1, #1, #0x1f
    // 0xba8398: stur            x3, [fp, #-0x30]
    // 0xba839c: r1 = LoadInt32Instr(r4)
    //     0xba839c: sbfx            x1, x4, #1, #0x1f
    // 0xba83a0: cmp             x3, x1
    // 0xba83a4: b.ne            #0xba83b0
    // 0xba83a8: mov             x1, x2
    // 0xba83ac: r0 = _growToNextCapacity()
    //     0xba83ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xba83b0: ldur            x0, [fp, #-0x18]
    // 0xba83b4: ldur            x1, [fp, #-0x30]
    // 0xba83b8: add             x2, x1, #1
    // 0xba83bc: lsl             x3, x2, #1
    // 0xba83c0: StoreField: r0->field_b = r3
    //     0xba83c0: stur            w3, [x0, #0xb]
    // 0xba83c4: LoadField: r2 = r0->field_f
    //     0xba83c4: ldur            w2, [x0, #0xf]
    // 0xba83c8: DecompressPointer r2
    //     0xba83c8: add             x2, x2, HEAP, lsl #32
    // 0xba83cc: add             x0, x2, x1, lsl #2
    // 0xba83d0: r16 = Instance_QuranBasmalah
    //     0xba83d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f370] Obj!QuranBasmalah@e1f8d1
    //     0xba83d4: ldr             x16, [x16, #0x370]
    // 0xba83d8: StoreField: r0->field_f = r16
    //     0xba83d8: stur            w16, [x0, #0xf]
    // 0xba83dc: ldr             x3, [fp, #0x10]
    // 0xba83e0: ldur            x0, [fp, #-8]
    // 0xba83e4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xba83e4: ldur            w4, [x0, #0x17]
    // 0xba83e8: DecompressPointer r4
    //     0xba83e8: add             x4, x4, HEAP, lsl #32
    // 0xba83ec: ldur            x2, [fp, #-0x10]
    // 0xba83f0: stur            x4, [fp, #-0x18]
    // 0xba83f4: r1 = Function '<anonymous closure>':.
    //     0xba83f4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33058] AnonymousClosure: (0xba87ac), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba83f8: ldr             x1, [x1, #0x58]
    // 0xba83fc: r0 = AllocateClosure()
    //     0xba83fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xba8400: ldr             x1, [fp, #0x10]
    // 0xba8404: r2 = LoadClassIdInstr(r1)
    //     0xba8404: ldur            x2, [x1, #-1]
    //     0xba8408: ubfx            x2, x2, #0xc, #0x14
    // 0xba840c: r16 = <TextSpan>
    //     0xba840c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33060] TypeArguments: <TextSpan>
    //     0xba8410: ldr             x16, [x16, #0x60]
    // 0xba8414: stp             x1, x16, [SP, #8]
    // 0xba8418: str             x0, [SP]
    // 0xba841c: mov             x0, x2
    // 0xba8420: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba8420: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba8424: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xba8424: movz            x17, #0xf28c
    //     0xba8428: add             lr, x0, x17
    //     0xba842c: ldr             lr, [x21, lr, lsl #3]
    //     0xba8430: blr             lr
    // 0xba8434: r1 = LoadClassIdInstr(r0)
    //     0xba8434: ldur            x1, [x0, #-1]
    //     0xba8438: ubfx            x1, x1, #0xc, #0x14
    // 0xba843c: mov             x16, x0
    // 0xba8440: mov             x0, x1
    // 0xba8444: mov             x1, x16
    // 0xba8448: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba8448: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba844c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xba844c: movz            x17, #0xd889
    //     0xba8450: add             lr, x0, x17
    //     0xba8454: ldr             lr, [x21, lr, lsl #3]
    //     0xba8458: blr             lr
    // 0xba845c: stur            x0, [fp, #-0x20]
    // 0xba8460: r0 = TextSpan()
    //     0xba8460: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xba8464: mov             x1, x0
    // 0xba8468: ldur            x0, [fp, #-0x20]
    // 0xba846c: stur            x1, [fp, #-0x28]
    // 0xba8470: StoreField: r1->field_f = r0
    //     0xba8470: stur            w0, [x1, #0xf]
    // 0xba8474: r0 = Instance__DeferringMouseCursor
    //     0xba8474: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xba8478: ArrayStore: r1[0] = r0  ; List_4
    //     0xba8478: stur            w0, [x1, #0x17]
    // 0xba847c: r0 = Text()
    //     0xba847c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xba8480: mov             x2, x0
    // 0xba8484: ldur            x0, [fp, #-0x28]
    // 0xba8488: stur            x2, [fp, #-0x20]
    // 0xba848c: StoreField: r2->field_f = r0
    //     0xba848c: stur            w0, [x2, #0xf]
    // 0xba8490: r0 = Instance_TextDirection
    //     0xba8490: ldr             x0, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xba8494: StoreField: r2->field_1f = r0
    //     0xba8494: stur            w0, [x2, #0x1f]
    // 0xba8498: r0 = Instance__LinearTextScaler
    //     0xba8498: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xba849c: StoreField: r2->field_33 = r0
    //     0xba849c: stur            w0, [x2, #0x33]
    // 0xba84a0: ldur            x0, [fp, #-0x18]
    // 0xba84a4: LoadField: r1 = r0->field_b
    //     0xba84a4: ldur            w1, [x0, #0xb]
    // 0xba84a8: LoadField: r3 = r0->field_f
    //     0xba84a8: ldur            w3, [x0, #0xf]
    // 0xba84ac: DecompressPointer r3
    //     0xba84ac: add             x3, x3, HEAP, lsl #32
    // 0xba84b0: LoadField: r4 = r3->field_b
    //     0xba84b0: ldur            w4, [x3, #0xb]
    // 0xba84b4: r3 = LoadInt32Instr(r1)
    //     0xba84b4: sbfx            x3, x1, #1, #0x1f
    // 0xba84b8: stur            x3, [fp, #-0x30]
    // 0xba84bc: r1 = LoadInt32Instr(r4)
    //     0xba84bc: sbfx            x1, x4, #1, #0x1f
    // 0xba84c0: cmp             x3, x1
    // 0xba84c4: b.ne            #0xba84d0
    // 0xba84c8: mov             x1, x0
    // 0xba84cc: r0 = _growToNextCapacity()
    //     0xba84cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xba84d0: ldur            x4, [fp, #-8]
    // 0xba84d4: ldur            x5, [fp, #-0x10]
    // 0xba84d8: ldur            x2, [fp, #-0x18]
    // 0xba84dc: ldur            x3, [fp, #-0x30]
    // 0xba84e0: add             x0, x3, #1
    // 0xba84e4: lsl             x1, x0, #1
    // 0xba84e8: StoreField: r2->field_b = r1
    //     0xba84e8: stur            w1, [x2, #0xb]
    // 0xba84ec: LoadField: r1 = r2->field_f
    //     0xba84ec: ldur            w1, [x2, #0xf]
    // 0xba84f0: DecompressPointer r1
    //     0xba84f0: add             x1, x1, HEAP, lsl #32
    // 0xba84f4: ldur            x0, [fp, #-0x20]
    // 0xba84f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xba84f8: add             x25, x1, x3, lsl #2
    //     0xba84fc: add             x25, x25, #0xf
    //     0xba8500: str             w0, [x25]
    //     0xba8504: tbz             w0, #0, #0xba8520
    //     0xba8508: ldurb           w16, [x1, #-1]
    //     0xba850c: ldurb           w17, [x0, #-1]
    //     0xba8510: and             x16, x17, x16, lsr #2
    //     0xba8514: tst             x16, HEAP, lsr #32
    //     0xba8518: b.eq            #0xba8520
    //     0xba851c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xba8520: LoadField: r0 = r5->field_f
    //     0xba8520: ldur            w0, [x5, #0xf]
    // 0xba8524: DecompressPointer r0
    //     0xba8524: add             x0, x0, HEAP, lsl #32
    // 0xba8528: stur            x0, [fp, #-0x28]
    // 0xba852c: LoadField: r3 = r4->field_1b
    //     0xba852c: ldur            w3, [x4, #0x1b]
    // 0xba8530: DecompressPointer r3
    //     0xba8530: add             x3, x3, HEAP, lsl #32
    // 0xba8534: stur            x3, [fp, #-0x20]
    // 0xba8538: LoadField: r1 = r3->field_7
    //     0xba8538: ldur            w1, [x3, #7]
    // 0xba853c: DecompressPointer r1
    //     0xba853c: add             x1, x1, HEAP, lsl #32
    // 0xba8540: r0 = _CompactIterable()
    //     0xba8540: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xba8544: mov             x1, x0
    // 0xba8548: ldur            x0, [fp, #-0x20]
    // 0xba854c: StoreField: r1->field_b = r0
    //     0xba854c: stur            w0, [x1, #0xb]
    // 0xba8550: r0 = -2
    //     0xba8550: orr             x0, xzr, #0xfffffffffffffffe
    // 0xba8554: StoreField: r1->field_f = r0
    //     0xba8554: stur            x0, [x1, #0xf]
    // 0xba8558: r0 = 2
    //     0xba8558: movz            x0, #0x2
    // 0xba855c: ArrayStore: r1[0] = r0  ; List_8
    //     0xba855c: stur            x0, [x1, #0x17]
    // 0xba8560: r0 = last()
    //     0xba8560: bl              #0x7a963c  ; [dart:core] Iterable::last
    // 0xba8564: mov             x1, x0
    // 0xba8568: ldur            x0, [fp, #-0x28]
    // 0xba856c: r2 = LoadInt32Instr(r0)
    //     0xba856c: sbfx            x2, x0, #1, #0x1f
    //     0xba8570: tbz             w0, #0, #0xba8578
    //     0xba8574: ldur            x2, [x0, #7]
    // 0xba8578: r0 = LoadInt32Instr(r1)
    //     0xba8578: sbfx            x0, x1, #1, #0x1f
    //     0xba857c: tbz             w1, #0, #0xba8584
    //     0xba8580: ldur            x0, [x1, #7]
    // 0xba8584: cmp             x2, x0
    // 0xba8588: b.ne            #0xba8744
    // 0xba858c: ldr             x1, [fp, #0x10]
    // 0xba8590: r0 = LoadClassIdInstr(r1)
    //     0xba8590: ldur            x0, [x1, #-1]
    //     0xba8594: ubfx            x0, x0, #0xc, #0x14
    // 0xba8598: r0 = GDT[cid_x0 + 0xf5bf]()
    //     0xba8598: movz            x17, #0xf5bf
    //     0xba859c: add             lr, x0, x17
    //     0xba85a0: ldr             lr, [x21, lr, lsl #3]
    //     0xba85a4: blr             lr
    // 0xba85a8: LoadField: r1 = r0->field_3f
    //     0xba85a8: ldur            w1, [x0, #0x3f]
    // 0xba85ac: DecompressPointer r1
    //     0xba85ac: add             x1, x1, HEAP, lsl #32
    // 0xba85b0: cmp             w1, NULL
    // 0xba85b4: b.eq            #0xba8744
    // 0xba85b8: tbnz            w1, #4, #0xba8744
    // 0xba85bc: ldur            x3, [fp, #-0x10]
    // 0xba85c0: LoadField: r0 = r3->field_f
    //     0xba85c0: ldur            w0, [x3, #0xf]
    // 0xba85c4: DecompressPointer r0
    //     0xba85c4: add             x0, x0, HEAP, lsl #32
    // 0xba85c8: r1 = LoadInt32Instr(r0)
    //     0xba85c8: sbfx            x1, x0, #1, #0x1f
    //     0xba85cc: tbz             w0, #0, #0xba85d4
    //     0xba85d0: ldur            x1, [x0, #7]
    // 0xba85d4: add             x2, x1, #1
    // 0xba85d8: r0 = BoxInt64Instr(r2)
    //     0xba85d8: sbfiz           x0, x2, #1, #0x1f
    //     0xba85dc: cmp             x2, x0, asr #1
    //     0xba85e0: b.eq            #0xba85ec
    //     0xba85e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba85e8: stur            x2, [x0, #7]
    // 0xba85ec: mov             x2, x0
    // 0xba85f0: r1 = const [0x4, 0xa, 0x16, 0x17, 0x18, 0x1a, 0x1b, 0x20, 0x21, 0x25, 0x26, 0x2d, 0x2f, 0x35, 0x3c, 0x40, 0x41, 0x50, 0x52, 0x56, 0x5b]
    //     0xba85f0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33030] List<int>(21)
    //     0xba85f4: ldr             x1, [x1, #0x30]
    // 0xba85f8: r0 = contains()
    //     0xba85f8: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xba85fc: tbnz            w0, #4, #0xba8744
    // 0xba8600: ldur            x1, [fp, #-8]
    // 0xba8604: ldur            x0, [fp, #-0x10]
    // 0xba8608: ldur            x3, [fp, #-0x18]
    // 0xba860c: LoadField: r2 = r0->field_f
    //     0xba860c: ldur            w2, [x0, #0xf]
    // 0xba8610: DecompressPointer r2
    //     0xba8610: add             x2, x2, HEAP, lsl #32
    // 0xba8614: r0 = LoadInt32Instr(r2)
    //     0xba8614: sbfx            x0, x2, #1, #0x1f
    //     0xba8618: tbz             w2, #0, #0xba8620
    //     0xba861c: ldur            x0, [x2, #7]
    // 0xba8620: add             x2, x0, #1
    // 0xba8624: LoadField: r0 = r1->field_f
    //     0xba8624: ldur            w0, [x1, #0xf]
    // 0xba8628: DecompressPointer r0
    //     0xba8628: add             x0, x0, HEAP, lsl #32
    // 0xba862c: LoadField: r1 = r0->field_f
    //     0xba862c: ldur            w1, [x0, #0xf]
    // 0xba8630: DecompressPointer r1
    //     0xba8630: add             x1, x1, HEAP, lsl #32
    // 0xba8634: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xba8634: ldur            w4, [x1, #0x17]
    // 0xba8638: DecompressPointer r4
    //     0xba8638: add             x4, x4, HEAP, lsl #32
    // 0xba863c: r0 = BoxInt64Instr(r2)
    //     0xba863c: sbfiz           x0, x2, #1, #0x1f
    //     0xba8640: cmp             x2, x0, asr #1
    //     0xba8644: b.eq            #0xba8650
    //     0xba8648: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba864c: stur            x2, [x0, #7]
    // 0xba8650: mov             x1, x4
    // 0xba8654: mov             x2, x0
    // 0xba8658: r0 = findSurah()
    //     0xba8658: bl              #0x8c23e0  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::findSurah
    // 0xba865c: r1 = <Surah>
    //     0xba865c: add             x1, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0xba8660: ldr             x1, [x1, #0x1e0]
    // 0xba8664: stur            x0, [fp, #-8]
    // 0xba8668: r0 = FutureBuilder()
    //     0xba8668: bl              #0xa2edc4  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xba866c: mov             x3, x0
    // 0xba8670: ldur            x0, [fp, #-8]
    // 0xba8674: stur            x3, [fp, #-0x10]
    // 0xba8678: StoreField: r3->field_f = r0
    //     0xba8678: stur            w0, [x3, #0xf]
    // 0xba867c: r1 = Function '<anonymous closure>':.
    //     0xba867c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33068] AnonymousClosure: (0xba875c), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba8680: ldr             x1, [x1, #0x68]
    // 0xba8684: r2 = Null
    //     0xba8684: mov             x2, NULL
    // 0xba8688: r0 = AllocateClosure()
    //     0xba8688: bl              #0xec1630  ; AllocateClosureStub
    // 0xba868c: ldur            x3, [fp, #-0x10]
    // 0xba8690: StoreField: r3->field_13 = r0
    //     0xba8690: stur            w0, [x3, #0x13]
    // 0xba8694: ldur            x4, [fp, #-0x18]
    // 0xba8698: LoadField: r2 = r4->field_7
    //     0xba8698: ldur            w2, [x4, #7]
    // 0xba869c: DecompressPointer r2
    //     0xba869c: add             x2, x2, HEAP, lsl #32
    // 0xba86a0: mov             x0, x3
    // 0xba86a4: r1 = Null
    //     0xba86a4: mov             x1, NULL
    // 0xba86a8: cmp             w2, NULL
    // 0xba86ac: b.eq            #0xba86cc
    // 0xba86b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xba86b0: ldur            w4, [x2, #0x17]
    // 0xba86b4: DecompressPointer r4
    //     0xba86b4: add             x4, x4, HEAP, lsl #32
    // 0xba86b8: r8 = X0
    //     0xba86b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xba86bc: LoadField: r9 = r4->field_7
    //     0xba86bc: ldur            x9, [x4, #7]
    // 0xba86c0: r3 = Null
    //     0xba86c0: add             x3, PP, #0x33, lsl #12  ; [pp+0x33070] Null
    //     0xba86c4: ldr             x3, [x3, #0x70]
    // 0xba86c8: blr             x9
    // 0xba86cc: ldur            x0, [fp, #-0x18]
    // 0xba86d0: LoadField: r1 = r0->field_b
    //     0xba86d0: ldur            w1, [x0, #0xb]
    // 0xba86d4: LoadField: r2 = r0->field_f
    //     0xba86d4: ldur            w2, [x0, #0xf]
    // 0xba86d8: DecompressPointer r2
    //     0xba86d8: add             x2, x2, HEAP, lsl #32
    // 0xba86dc: LoadField: r3 = r2->field_b
    //     0xba86dc: ldur            w3, [x2, #0xb]
    // 0xba86e0: r2 = LoadInt32Instr(r1)
    //     0xba86e0: sbfx            x2, x1, #1, #0x1f
    // 0xba86e4: stur            x2, [fp, #-0x30]
    // 0xba86e8: r1 = LoadInt32Instr(r3)
    //     0xba86e8: sbfx            x1, x3, #1, #0x1f
    // 0xba86ec: cmp             x2, x1
    // 0xba86f0: b.ne            #0xba86fc
    // 0xba86f4: mov             x1, x0
    // 0xba86f8: r0 = _growToNextCapacity()
    //     0xba86f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xba86fc: ldur            x2, [fp, #-0x18]
    // 0xba8700: ldur            x3, [fp, #-0x30]
    // 0xba8704: add             x4, x3, #1
    // 0xba8708: lsl             x5, x4, #1
    // 0xba870c: StoreField: r2->field_b = r5
    //     0xba870c: stur            w5, [x2, #0xb]
    // 0xba8710: LoadField: r1 = r2->field_f
    //     0xba8710: ldur            w1, [x2, #0xf]
    // 0xba8714: DecompressPointer r1
    //     0xba8714: add             x1, x1, HEAP, lsl #32
    // 0xba8718: ldur            x0, [fp, #-0x10]
    // 0xba871c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xba871c: add             x25, x1, x3, lsl #2
    //     0xba8720: add             x25, x25, #0xf
    //     0xba8724: str             w0, [x25]
    //     0xba8728: tbz             w0, #0, #0xba8744
    //     0xba872c: ldurb           w16, [x1, #-1]
    //     0xba8730: ldurb           w17, [x0, #-1]
    //     0xba8734: and             x16, x17, x16, lsr #2
    //     0xba8738: tst             x16, HEAP, lsr #32
    //     0xba873c: b.eq            #0xba8744
    //     0xba8740: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xba8744: r0 = Null
    //     0xba8744: mov             x0, NULL
    // 0xba8748: LeaveFrame
    //     0xba8748: mov             SP, fp
    //     0xba874c: ldp             fp, lr, [SP], #0x10
    // 0xba8750: ret
    //     0xba8750: ret             
    // 0xba8754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba8754: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba8758: b               #0xba8190
  }
  [closure] QuranSurahInfo <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<Surah>) {
    // ** addr: 0xba875c, size: 0x50
    // 0xba875c: EnterFrame
    //     0xba875c: stp             fp, lr, [SP, #-0x10]!
    //     0xba8760: mov             fp, SP
    // 0xba8764: AllocStack(0x8)
    //     0xba8764: sub             SP, SP, #8
    // 0xba8768: ldr             x0, [fp, #0x10]
    // 0xba876c: LoadField: r1 = r0->field_f
    //     0xba876c: ldur            w1, [x0, #0xf]
    // 0xba8770: DecompressPointer r1
    //     0xba8770: add             x1, x1, HEAP, lsl #32
    // 0xba8774: stur            x1, [fp, #-8]
    // 0xba8778: cmp             w1, NULL
    // 0xba877c: b.eq            #0xba8798
    // 0xba8780: r0 = QuranSurahInfo()
    //     0xba8780: bl              #0xb1e080  ; AllocateQuranSurahInfoStub -> QuranSurahInfo (size=0x10)
    // 0xba8784: ldur            x1, [fp, #-8]
    // 0xba8788: StoreField: r0->field_b = r1
    //     0xba8788: stur            w1, [x0, #0xb]
    // 0xba878c: LeaveFrame
    //     0xba878c: mov             SP, fp
    //     0xba8790: ldp             fp, lr, [SP], #0x10
    // 0xba8794: ret
    //     0xba8794: ret             
    // 0xba8798: r0 = Instance_QuranSurahInfo
    //     0xba8798: add             x0, PP, #0x33, lsl #12  ; [pp+0x33080] Obj!QuranSurahInfo@e1f8c1
    //     0xba879c: ldr             x0, [x0, #0x80]
    // 0xba87a0: LeaveFrame
    //     0xba87a0: mov             SP, fp
    //     0xba87a4: ldp             fp, lr, [SP], #0x10
    // 0xba87a8: ret
    //     0xba87a8: ret             
  }
  [closure] TextSpan <anonymous closure>(dynamic, Verse) {
    // ** addr: 0xba87ac, size: 0x5c4
    // 0xba87ac: EnterFrame
    //     0xba87ac: stp             fp, lr, [SP, #-0x10]!
    //     0xba87b0: mov             fp, SP
    // 0xba87b4: AllocStack(0x60)
    //     0xba87b4: sub             SP, SP, #0x60
    // 0xba87b8: SetupParameters()
    //     0xba87b8: ldr             x0, [fp, #0x18]
    //     0xba87bc: ldur            w1, [x0, #0x17]
    //     0xba87c0: add             x1, x1, HEAP, lsl #32
    //     0xba87c4: stur            x1, [fp, #-8]
    // 0xba87c8: CheckStackOverflow
    //     0xba87c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba87cc: cmp             SP, x16
    //     0xba87d0: b.ls            #0xba8d64
    // 0xba87d4: r1 = 1
    //     0xba87d4: movz            x1, #0x1
    // 0xba87d8: r0 = AllocateContext()
    //     0xba87d8: bl              #0xec126c  ; AllocateContextStub
    // 0xba87dc: mov             x1, x0
    // 0xba87e0: ldur            x0, [fp, #-8]
    // 0xba87e4: stur            x1, [fp, #-0x18]
    // 0xba87e8: StoreField: r1->field_b = r0
    //     0xba87e8: stur            w0, [x1, #0xb]
    // 0xba87ec: ldr             x2, [fp, #0x10]
    // 0xba87f0: StoreField: r1->field_f = r2
    //     0xba87f0: stur            w2, [x1, #0xf]
    // 0xba87f4: LoadField: r3 = r0->field_b
    //     0xba87f4: ldur            w3, [x0, #0xb]
    // 0xba87f8: DecompressPointer r3
    //     0xba87f8: add             x3, x3, HEAP, lsl #32
    // 0xba87fc: stur            x3, [fp, #-0x10]
    // 0xba8800: LoadField: r0 = r3->field_f
    //     0xba8800: ldur            w0, [x3, #0xf]
    // 0xba8804: DecompressPointer r0
    //     0xba8804: add             x0, x0, HEAP, lsl #32
    // 0xba8808: LoadField: r4 = r0->field_1b
    //     0xba8808: ldur            x4, [x0, #0x1b]
    // 0xba880c: LoadField: r0 = r2->field_7
    //     0xba880c: ldur            x0, [x2, #7]
    // 0xba8810: cmp             x4, x0
    // 0xba8814: b.ne            #0xba8864
    // 0xba8818: r16 = <Color?>
    //     0xba8818: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba881c: ldr             x16, [x16, #0x98]
    // 0xba8820: r30 = Instance_Color
    //     0xba8820: add             lr, PP, #0x33, lsl #12  ; [pp+0x33088] Obj!Color@e2b741
    //     0xba8824: ldr             lr, [lr, #0x88]
    // 0xba8828: stp             lr, x16, [SP, #8]
    // 0xba882c: r16 = Instance_Color
    //     0xba882c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33090] Obj!Color@e2b711
    //     0xba8830: ldr             x16, [x16, #0x90]
    // 0xba8834: str             x16, [SP]
    // 0xba8838: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba8838: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba883c: r0 = mode()
    //     0xba883c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba8840: stur            x0, [fp, #-8]
    // 0xba8844: r0 = TextStyle()
    //     0xba8844: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xba8848: mov             x1, x0
    // 0xba884c: r0 = true
    //     0xba884c: add             x0, NULL, #0x20  ; true
    // 0xba8850: StoreField: r1->field_7 = r0
    //     0xba8850: stur            w0, [x1, #7]
    // 0xba8854: ldur            x2, [fp, #-8]
    // 0xba8858: StoreField: r1->field_f = r2
    //     0xba8858: stur            w2, [x1, #0xf]
    // 0xba885c: mov             x3, x1
    // 0xba8860: b               #0xba886c
    // 0xba8864: r0 = true
    //     0xba8864: add             x0, NULL, #0x20  ; true
    // 0xba8868: r3 = Instance_TextStyle
    //     0xba8868: ldr             x3, [PP, #0x47e0]  ; [pp+0x47e0] Obj!TextStyle@e1ad91
    // 0xba886c: ldur            x2, [fp, #-0x18]
    // 0xba8870: ldur            x1, [fp, #-0x10]
    // 0xba8874: LoadField: r4 = r1->field_f
    //     0xba8874: ldur            w4, [x1, #0xf]
    // 0xba8878: DecompressPointer r4
    //     0xba8878: add             x4, x4, HEAP, lsl #32
    // 0xba887c: LoadField: r5 = r4->field_13
    //     0xba887c: ldur            x5, [x4, #0x13]
    // 0xba8880: LoadField: r4 = r2->field_f
    //     0xba8880: ldur            w4, [x2, #0xf]
    // 0xba8884: DecompressPointer r4
    //     0xba8884: add             x4, x4, HEAP, lsl #32
    // 0xba8888: LoadField: r6 = r4->field_7
    //     0xba8888: ldur            x6, [x4, #7]
    // 0xba888c: cmp             x5, x6
    // 0xba8890: b.ne            #0xba88e0
    // 0xba8894: r16 = <Color?>
    //     0xba8894: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba8898: ldr             x16, [x16, #0x98]
    // 0xba889c: r30 = Instance_Color
    //     0xba889c: add             lr, PP, #0x33, lsl #12  ; [pp+0x33088] Obj!Color@e2b741
    //     0xba88a0: ldr             lr, [lr, #0x88]
    // 0xba88a4: stp             lr, x16, [SP, #8]
    // 0xba88a8: r16 = Instance_Color
    //     0xba88a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33090] Obj!Color@e2b711
    //     0xba88ac: ldr             x16, [x16, #0x90]
    // 0xba88b0: str             x16, [SP]
    // 0xba88b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba88b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba88b8: r0 = mode()
    //     0xba88b8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba88bc: stur            x0, [fp, #-8]
    // 0xba88c0: r0 = TextStyle()
    //     0xba88c0: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xba88c4: mov             x1, x0
    // 0xba88c8: r0 = true
    //     0xba88c8: add             x0, NULL, #0x20  ; true
    // 0xba88cc: StoreField: r1->field_7 = r0
    //     0xba88cc: stur            w0, [x1, #7]
    // 0xba88d0: ldur            x2, [fp, #-8]
    // 0xba88d4: StoreField: r1->field_f = r2
    //     0xba88d4: stur            w2, [x1, #0xf]
    // 0xba88d8: mov             x6, x1
    // 0xba88dc: b               #0xba88e4
    // 0xba88e0: mov             x6, x3
    // 0xba88e4: ldur            x4, [fp, #-0x18]
    // 0xba88e8: ldur            x5, [fp, #-0x10]
    // 0xba88ec: stur            x6, [fp, #-0x20]
    // 0xba88f0: LoadField: r7 = r4->field_f
    //     0xba88f0: ldur            w7, [x4, #0xf]
    // 0xba88f4: DecompressPointer r7
    //     0xba88f4: add             x7, x7, HEAP, lsl #32
    // 0xba88f8: stur            x7, [fp, #-8]
    // 0xba88fc: LoadField: r1 = r7->field_1f
    //     0xba88fc: ldur            w1, [x7, #0x1f]
    // 0xba8900: DecompressPointer r1
    //     0xba8900: add             x1, x1, HEAP, lsl #32
    // 0xba8904: r2 = "\n"
    //     0xba8904: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xba8908: r3 = "enter"
    //     0xba8908: add             x3, PP, #0x33, lsl #12  ; [pp+0x33098] "enter"
    //     0xba890c: ldr             x3, [x3, #0x98]
    // 0xba8910: r0 = replaceAll()
    //     0xba8910: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xba8914: mov             x1, x0
    // 0xba8918: r0 = trim()
    //     0xba8918: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xba891c: mov             x1, x0
    // 0xba8920: r2 = "enter"
    //     0xba8920: add             x2, PP, #0x33, lsl #12  ; [pp+0x33098] "enter"
    //     0xba8924: ldr             x2, [x2, #0x98]
    // 0xba8928: r3 = "\n"
    //     0xba8928: ldr             x3, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xba892c: r0 = replaceAll()
    //     0xba892c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xba8930: str             x0, [SP]
    // 0xba8934: ldur            x1, [fp, #-8]
    // 0xba8938: r4 = const [0, 0x2, 0x1, 0x1, value, 0x1, null]
    //     0xba8938: add             x4, PP, #0x33, lsl #12  ; [pp+0x330a0] List(7) [0, 0x2, 0x1, 0x1, "value", 0x1, Null]
    //     0xba893c: ldr             x4, [x4, #0xa0]
    // 0xba8940: r0 = copyWith()
    //     0xba8940: bl              #0xb220fc  ; [package:nuonline/app/data/models/verse.dart] Verse::copyWith
    // 0xba8944: stur            x0, [fp, #-8]
    // 0xba8948: r0 = LongPressGestureRecognizer()
    //     0xba8948: bl              #0x762f70  ; AllocateLongPressGestureRecognizerStub -> LongPressGestureRecognizer (size=0xac)
    // 0xba894c: mov             x4, x0
    // 0xba8950: r0 = false
    //     0xba8950: add             x0, NULL, #0x30  ; false
    // 0xba8954: stur            x4, [fp, #-0x28]
    // 0xba8958: StoreField: r4->field_47 = r0
    //     0xba8958: stur            w0, [x4, #0x47]
    // 0xba895c: str             NULL, [SP]
    // 0xba8960: mov             x1, x4
    // 0xba8964: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static.
    //     0xba8964: add             x2, PP, #0x33, lsl #12  ; [pp+0x330a8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static. (0x7e54fb163834)
    //     0xba8968: ldr             x2, [x2, #0xa8]
    // 0xba896c: r3 = Instance_Duration
    //     0xba896c: ldr             x3, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xba8970: r5 = Null
    //     0xba8970: mov             x5, NULL
    // 0xba8974: r4 = const [0, 0x5, 0x1, 0x4, postAcceptSlopTolerance, 0x4, null]
    //     0xba8974: add             x4, PP, #0x33, lsl #12  ; [pp+0x330b0] List(7) [0, 0x5, 0x1, 0x4, "postAcceptSlopTolerance", 0x4, Null]
    //     0xba8978: ldr             x4, [x4, #0xb0]
    // 0xba897c: r0 = PrimaryPointerGestureRecognizer()
    //     0xba897c: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xba8980: ldur            x2, [fp, #-0x18]
    // 0xba8984: r1 = Function '<anonymous closure>':.
    //     0xba8984: add             x1, PP, #0x33, lsl #12  ; [pp+0x330b8] AnonymousClosure: (0xba9910), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba8988: ldr             x1, [x1, #0xb8]
    // 0xba898c: r0 = AllocateClosure()
    //     0xba898c: bl              #0xec1630  ; AllocateClosureStub
    // 0xba8990: ldur            x3, [fp, #-0x28]
    // 0xba8994: StoreField: r3->field_5b = r0
    //     0xba8994: stur            w0, [x3, #0x5b]
    //     0xba8998: ldurb           w16, [x3, #-1]
    //     0xba899c: ldurb           w17, [x0, #-1]
    //     0xba89a0: and             x16, x17, x16, lsr #2
    //     0xba89a4: tst             x16, HEAP, lsr #32
    //     0xba89a8: b.eq            #0xba89b0
    //     0xba89ac: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba89b0: ldur            x0, [fp, #-0x18]
    // 0xba89b4: LoadField: r1 = r0->field_f
    //     0xba89b4: ldur            w1, [x0, #0xf]
    // 0xba89b8: DecompressPointer r1
    //     0xba89b8: add             x1, x1, HEAP, lsl #32
    // 0xba89bc: LoadField: r4 = r1->field_3b
    //     0xba89bc: ldur            w4, [x1, #0x3b]
    // 0xba89c0: DecompressPointer r4
    //     0xba89c0: add             x4, x4, HEAP, lsl #32
    // 0xba89c4: stur            x4, [fp, #-0x30]
    // 0xba89c8: cmp             w4, NULL
    // 0xba89cc: b.eq            #0xba8d6c
    // 0xba89d0: r1 = Function '<anonymous closure>':.
    //     0xba89d0: add             x1, PP, #0x33, lsl #12  ; [pp+0x330c0] AnonymousClosure: (0xba9b74), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba89d4: ldr             x1, [x1, #0xc0]
    // 0xba89d8: r2 = Null
    //     0xba89d8: mov             x2, NULL
    // 0xba89dc: r0 = AllocateClosure()
    //     0xba89dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xba89e0: mov             x1, x0
    // 0xba89e4: ldur            x0, [fp, #-0x30]
    // 0xba89e8: r2 = LoadClassIdInstr(r0)
    //     0xba89e8: ldur            x2, [x0, #-1]
    //     0xba89ec: ubfx            x2, x2, #0xc, #0x14
    // 0xba89f0: r16 = <FontFeature>
    //     0xba89f0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1caf0] TypeArguments: <FontFeature>
    //     0xba89f4: ldr             x16, [x16, #0xaf0]
    // 0xba89f8: stp             x0, x16, [SP, #8]
    // 0xba89fc: str             x1, [SP]
    // 0xba8a00: mov             x0, x2
    // 0xba8a04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba8a04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba8a08: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xba8a08: movz            x17, #0xf28c
    //     0xba8a0c: add             lr, x0, x17
    //     0xba8a10: ldr             lr, [x21, lr, lsl #3]
    //     0xba8a14: blr             lr
    // 0xba8a18: r1 = LoadClassIdInstr(r0)
    //     0xba8a18: ldur            x1, [x0, #-1]
    //     0xba8a1c: ubfx            x1, x1, #0xc, #0x14
    // 0xba8a20: mov             x16, x0
    // 0xba8a24: mov             x0, x1
    // 0xba8a28: mov             x1, x16
    // 0xba8a2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba8a2c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba8a30: r0 = GDT[cid_x0 + 0xd889]()
    //     0xba8a30: movz            x17, #0xd889
    //     0xba8a34: add             lr, x0, x17
    //     0xba8a38: ldr             lr, [x21, lr, lsl #3]
    //     0xba8a3c: blr             lr
    // 0xba8a40: stur            x0, [fp, #-0x30]
    // 0xba8a44: r0 = TextStyle()
    //     0xba8a44: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xba8a48: mov             x1, x0
    // 0xba8a4c: r0 = true
    //     0xba8a4c: add             x0, NULL, #0x20  ; true
    // 0xba8a50: stur            x1, [fp, #-0x48]
    // 0xba8a54: StoreField: r1->field_7 = r0
    //     0xba8a54: stur            w0, [x1, #7]
    // 0xba8a58: r2 = Instance_FontWeight
    //     0xba8a58: add             x2, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xba8a5c: ldr             x2, [x2, #0xcc0]
    // 0xba8a60: StoreField: r1->field_23 = r2
    //     0xba8a60: stur            w2, [x1, #0x23]
    // 0xba8a64: ldur            x3, [fp, #-0x30]
    // 0xba8a68: StoreField: r1->field_63 = r3
    //     0xba8a68: stur            w3, [x1, #0x63]
    // 0xba8a6c: ldur            x3, [fp, #-0x10]
    // 0xba8a70: LoadField: r4 = r3->field_f
    //     0xba8a70: ldur            w4, [x3, #0xf]
    // 0xba8a74: DecompressPointer r4
    //     0xba8a74: add             x4, x4, HEAP, lsl #32
    // 0xba8a78: LoadField: r5 = r4->field_33
    //     0xba8a78: ldur            w5, [x4, #0x33]
    // 0xba8a7c: DecompressPointer r5
    //     0xba8a7c: add             x5, x5, HEAP, lsl #32
    // 0xba8a80: stur            x5, [fp, #-0x40]
    // 0xba8a84: tbnz            w5, #4, #0xba8a94
    // 0xba8a88: ldur            x4, [fp, #-8]
    // 0xba8a8c: r6 = Null
    //     0xba8a8c: mov             x6, NULL
    // 0xba8a90: b               #0xba8aa0
    // 0xba8a94: ldur            x4, [fp, #-8]
    // 0xba8a98: LoadField: r6 = r4->field_1f
    //     0xba8a98: ldur            w6, [x4, #0x1f]
    // 0xba8a9c: DecompressPointer r6
    //     0xba8a9c: add             x6, x6, HEAP, lsl #32
    // 0xba8aa0: stur            x6, [fp, #-0x38]
    // 0xba8aa4: tbnz            w5, #4, #0xba8ae8
    // 0xba8aa8: LoadField: r7 = r3->field_13
    //     0xba8aa8: ldur            w7, [x3, #0x13]
    // 0xba8aac: DecompressPointer r7
    //     0xba8aac: add             x7, x7, HEAP, lsl #32
    // 0xba8ab0: stur            x7, [fp, #-0x30]
    // 0xba8ab4: r0 = QuranVerseTajweed()
    //     0xba8ab4: bl              #0xba9904  ; AllocateQuranVerseTajweedStub -> QuranVerseTajweed (size=0x18)
    // 0xba8ab8: mov             x1, x0
    // 0xba8abc: ldur            x0, [fp, #-0x30]
    // 0xba8ac0: StoreField: r1->field_7 = r0
    //     0xba8ac0: stur            w0, [x1, #7]
    // 0xba8ac4: ldur            x0, [fp, #-0x40]
    // 0xba8ac8: StoreField: r1->field_b = r0
    //     0xba8ac8: stur            w0, [x1, #0xb]
    // 0xba8acc: ldur            x0, [fp, #-8]
    // 0xba8ad0: StoreField: r1->field_f = r0
    //     0xba8ad0: stur            w0, [x1, #0xf]
    // 0xba8ad4: r0 = true
    //     0xba8ad4: add             x0, NULL, #0x20  ; true
    // 0xba8ad8: StoreField: r1->field_13 = r0
    //     0xba8ad8: stur            w0, [x1, #0x13]
    // 0xba8adc: r0 = build()
    //     0xba8adc: bl              #0xba8dd8  ; [package:nuonline/app/modules/quran/widgets/quran_verse_tajweed.dart] QuranVerseTajweed::build
    // 0xba8ae0: mov             x5, x0
    // 0xba8ae4: b               #0xba8aec
    // 0xba8ae8: r5 = Null
    //     0xba8ae8: mov             x5, NULL
    // 0xba8aec: ldur            x3, [fp, #-0x18]
    // 0xba8af0: ldur            x4, [fp, #-0x20]
    // 0xba8af4: ldur            x2, [fp, #-0x28]
    // 0xba8af8: ldur            x0, [fp, #-0x48]
    // 0xba8afc: ldur            x1, [fp, #-0x38]
    // 0xba8b00: stur            x5, [fp, #-8]
    // 0xba8b04: r0 = TextSpan()
    //     0xba8b04: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xba8b08: mov             x3, x0
    // 0xba8b0c: ldur            x0, [fp, #-0x38]
    // 0xba8b10: stur            x3, [fp, #-0x10]
    // 0xba8b14: StoreField: r3->field_b = r0
    //     0xba8b14: stur            w0, [x3, #0xb]
    // 0xba8b18: ldur            x0, [fp, #-8]
    // 0xba8b1c: StoreField: r3->field_f = r0
    //     0xba8b1c: stur            w0, [x3, #0xf]
    // 0xba8b20: ldur            x0, [fp, #-0x28]
    // 0xba8b24: StoreField: r3->field_13 = r0
    //     0xba8b24: stur            w0, [x3, #0x13]
    // 0xba8b28: r0 = Instance_SystemMouseCursor
    //     0xba8b28: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xba8b2c: ldr             x0, [x0, #0xbf0]
    // 0xba8b30: ArrayStore: r3[0] = r0  ; List_4
    //     0xba8b30: stur            w0, [x3, #0x17]
    // 0xba8b34: ldur            x1, [fp, #-0x48]
    // 0xba8b38: StoreField: r3->field_7 = r1
    //     0xba8b38: stur            w1, [x3, #7]
    // 0xba8b3c: r1 = Null
    //     0xba8b3c: mov             x1, NULL
    // 0xba8b40: r2 = 6
    //     0xba8b40: movz            x2, #0x6
    // 0xba8b44: r0 = AllocateArray()
    //     0xba8b44: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba8b48: stur            x0, [fp, #-8]
    // 0xba8b4c: r16 = "‮ "
    //     0xba8b4c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b5f8] "‮ "
    //     0xba8b50: ldr             x16, [x16, #0x5f8]
    // 0xba8b54: StoreField: r0->field_f = r16
    //     0xba8b54: stur            w16, [x0, #0xf]
    // 0xba8b58: ldur            x2, [fp, #-0x18]
    // 0xba8b5c: LoadField: r1 = r2->field_f
    //     0xba8b5c: ldur            w1, [x2, #0xf]
    // 0xba8b60: DecompressPointer r1
    //     0xba8b60: add             x1, x1, HEAP, lsl #32
    // 0xba8b64: r0 = arabicNumberLeft()
    //     0xba8b64: bl              #0xba8d70  ; [package:nuonline/app/data/models/verse.dart] Verse::arabicNumberLeft
    // 0xba8b68: ldur            x1, [fp, #-8]
    // 0xba8b6c: ArrayStore: r1[1] = r0  ; List_4
    //     0xba8b6c: add             x25, x1, #0x13
    //     0xba8b70: str             w0, [x25]
    //     0xba8b74: tbz             w0, #0, #0xba8b90
    //     0xba8b78: ldurb           w16, [x1, #-1]
    //     0xba8b7c: ldurb           w17, [x0, #-1]
    //     0xba8b80: and             x16, x17, x16, lsr #2
    //     0xba8b84: tst             x16, HEAP, lsr #32
    //     0xba8b88: b.eq            #0xba8b90
    //     0xba8b8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xba8b90: ldur            x0, [fp, #-8]
    // 0xba8b94: r16 = " "
    //     0xba8b94: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xba8b98: ArrayStore: r0[0] = r16  ; List_4
    //     0xba8b98: stur            w16, [x0, #0x17]
    // 0xba8b9c: str             x0, [SP]
    // 0xba8ba0: r0 = _interpolate()
    //     0xba8ba0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xba8ba4: r1 = _ConstMap len:10
    //     0xba8ba4: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xba8ba8: ldr             x1, [x1, #0x6c8]
    // 0xba8bac: r2 = 600
    //     0xba8bac: movz            x2, #0x258
    // 0xba8bb0: stur            x0, [fp, #-8]
    // 0xba8bb4: r0 = []()
    //     0xba8bb4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xba8bb8: r16 = <Color?>
    //     0xba8bb8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xba8bbc: ldr             x16, [x16, #0x98]
    // 0xba8bc0: stp             x0, x16, [SP, #8]
    // 0xba8bc4: r16 = Instance_MaterialColor
    //     0xba8bc4: add             x16, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xba8bc8: ldr             x16, [x16, #0x6d0]
    // 0xba8bcc: str             x16, [SP]
    // 0xba8bd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xba8bd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xba8bd4: r0 = mode()
    //     0xba8bd4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xba8bd8: r1 = Null
    //     0xba8bd8: mov             x1, NULL
    // 0xba8bdc: r2 = 1
    //     0xba8bdc: movz            x2, #0x1
    // 0xba8be0: stur            x0, [fp, #-0x28]
    // 0xba8be4: r0 = FontFeature.stylisticSet()
    //     0xba8be4: bl              #0xba69cc  ; [dart:ui] FontFeature::FontFeature.stylisticSet
    // 0xba8be8: r1 = Null
    //     0xba8be8: mov             x1, NULL
    // 0xba8bec: r2 = 2
    //     0xba8bec: movz            x2, #0x2
    // 0xba8bf0: stur            x0, [fp, #-0x30]
    // 0xba8bf4: r0 = AllocateArray()
    //     0xba8bf4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba8bf8: mov             x2, x0
    // 0xba8bfc: ldur            x0, [fp, #-0x30]
    // 0xba8c00: stur            x2, [fp, #-0x38]
    // 0xba8c04: StoreField: r2->field_f = r0
    //     0xba8c04: stur            w0, [x2, #0xf]
    // 0xba8c08: r1 = <FontFeature>
    //     0xba8c08: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1caf0] TypeArguments: <FontFeature>
    //     0xba8c0c: ldr             x1, [x1, #0xaf0]
    // 0xba8c10: r0 = AllocateGrowableArray()
    //     0xba8c10: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xba8c14: mov             x1, x0
    // 0xba8c18: ldur            x0, [fp, #-0x38]
    // 0xba8c1c: stur            x1, [fp, #-0x30]
    // 0xba8c20: StoreField: r1->field_f = r0
    //     0xba8c20: stur            w0, [x1, #0xf]
    // 0xba8c24: r0 = 2
    //     0xba8c24: movz            x0, #0x2
    // 0xba8c28: StoreField: r1->field_b = r0
    //     0xba8c28: stur            w0, [x1, #0xb]
    // 0xba8c2c: r0 = TextStyle()
    //     0xba8c2c: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xba8c30: mov             x1, x0
    // 0xba8c34: r0 = true
    //     0xba8c34: add             x0, NULL, #0x20  ; true
    // 0xba8c38: stur            x1, [fp, #-0x38]
    // 0xba8c3c: StoreField: r1->field_7 = r0
    //     0xba8c3c: stur            w0, [x1, #7]
    // 0xba8c40: ldur            x0, [fp, #-0x28]
    // 0xba8c44: StoreField: r1->field_b = r0
    //     0xba8c44: stur            w0, [x1, #0xb]
    // 0xba8c48: r0 = Instance_FontWeight
    //     0xba8c48: add             x0, PP, #0x25, lsl #12  ; [pp+0x25cc0] Obj!FontWeight@e26551
    //     0xba8c4c: ldr             x0, [x0, #0xcc0]
    // 0xba8c50: StoreField: r1->field_23 = r0
    //     0xba8c50: stur            w0, [x1, #0x23]
    // 0xba8c54: ldur            x0, [fp, #-0x30]
    // 0xba8c58: StoreField: r1->field_63 = r0
    //     0xba8c58: stur            w0, [x1, #0x63]
    // 0xba8c5c: r0 = LongPressGestureRecognizer()
    //     0xba8c5c: bl              #0x762f70  ; AllocateLongPressGestureRecognizerStub -> LongPressGestureRecognizer (size=0xac)
    // 0xba8c60: mov             x4, x0
    // 0xba8c64: r0 = false
    //     0xba8c64: add             x0, NULL, #0x30  ; false
    // 0xba8c68: stur            x4, [fp, #-0x28]
    // 0xba8c6c: StoreField: r4->field_47 = r0
    //     0xba8c6c: stur            w0, [x4, #0x47]
    // 0xba8c70: str             NULL, [SP]
    // 0xba8c74: mov             x1, x4
    // 0xba8c78: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static.
    //     0xba8c78: add             x2, PP, #0x33, lsl #12  ; [pp+0x330a8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static. (0x7e54fb163834)
    //     0xba8c7c: ldr             x2, [x2, #0xa8]
    // 0xba8c80: r3 = Instance_Duration
    //     0xba8c80: ldr             x3, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xba8c84: r5 = Null
    //     0xba8c84: mov             x5, NULL
    // 0xba8c88: r4 = const [0, 0x5, 0x1, 0x4, postAcceptSlopTolerance, 0x4, null]
    //     0xba8c88: add             x4, PP, #0x33, lsl #12  ; [pp+0x330b0] List(7) [0, 0x5, 0x1, 0x4, "postAcceptSlopTolerance", 0x4, Null]
    //     0xba8c8c: ldr             x4, [x4, #0xb0]
    // 0xba8c90: r0 = PrimaryPointerGestureRecognizer()
    //     0xba8c90: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xba8c94: ldur            x2, [fp, #-0x18]
    // 0xba8c98: r1 = Function '<anonymous closure>':.
    //     0xba8c98: add             x1, PP, #0x33, lsl #12  ; [pp+0x330c8] AnonymousClosure: (0xba9910), in [package:nuonline/app/modules/quran/widgets/quran_verses.dart] QuranVerses::build (0xba7d08)
    //     0xba8c9c: ldr             x1, [x1, #0xc8]
    // 0xba8ca0: r0 = AllocateClosure()
    //     0xba8ca0: bl              #0xec1630  ; AllocateClosureStub
    // 0xba8ca4: ldur            x1, [fp, #-0x28]
    // 0xba8ca8: StoreField: r1->field_5b = r0
    //     0xba8ca8: stur            w0, [x1, #0x5b]
    //     0xba8cac: ldurb           w16, [x1, #-1]
    //     0xba8cb0: ldurb           w17, [x0, #-1]
    //     0xba8cb4: and             x16, x17, x16, lsr #2
    //     0xba8cb8: tst             x16, HEAP, lsr #32
    //     0xba8cbc: b.eq            #0xba8cc4
    //     0xba8cc0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xba8cc4: r0 = TextSpan()
    //     0xba8cc4: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xba8cc8: mov             x3, x0
    // 0xba8ccc: ldur            x0, [fp, #-8]
    // 0xba8cd0: stur            x3, [fp, #-0x18]
    // 0xba8cd4: StoreField: r3->field_b = r0
    //     0xba8cd4: stur            w0, [x3, #0xb]
    // 0xba8cd8: ldur            x0, [fp, #-0x28]
    // 0xba8cdc: StoreField: r3->field_13 = r0
    //     0xba8cdc: stur            w0, [x3, #0x13]
    // 0xba8ce0: r0 = Instance_SystemMouseCursor
    //     0xba8ce0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xba8ce4: ldr             x0, [x0, #0xbf0]
    // 0xba8ce8: ArrayStore: r3[0] = r0  ; List_4
    //     0xba8ce8: stur            w0, [x3, #0x17]
    // 0xba8cec: ldur            x0, [fp, #-0x38]
    // 0xba8cf0: StoreField: r3->field_7 = r0
    //     0xba8cf0: stur            w0, [x3, #7]
    // 0xba8cf4: r1 = Null
    //     0xba8cf4: mov             x1, NULL
    // 0xba8cf8: r2 = 4
    //     0xba8cf8: movz            x2, #0x4
    // 0xba8cfc: r0 = AllocateArray()
    //     0xba8cfc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba8d00: mov             x2, x0
    // 0xba8d04: ldur            x0, [fp, #-0x10]
    // 0xba8d08: stur            x2, [fp, #-8]
    // 0xba8d0c: StoreField: r2->field_f = r0
    //     0xba8d0c: stur            w0, [x2, #0xf]
    // 0xba8d10: ldur            x0, [fp, #-0x18]
    // 0xba8d14: StoreField: r2->field_13 = r0
    //     0xba8d14: stur            w0, [x2, #0x13]
    // 0xba8d18: r1 = <InlineSpan>
    //     0xba8d18: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xba8d1c: ldr             x1, [x1, #0x5f0]
    // 0xba8d20: r0 = AllocateGrowableArray()
    //     0xba8d20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xba8d24: mov             x1, x0
    // 0xba8d28: ldur            x0, [fp, #-8]
    // 0xba8d2c: stur            x1, [fp, #-0x10]
    // 0xba8d30: StoreField: r1->field_f = r0
    //     0xba8d30: stur            w0, [x1, #0xf]
    // 0xba8d34: r0 = 4
    //     0xba8d34: movz            x0, #0x4
    // 0xba8d38: StoreField: r1->field_b = r0
    //     0xba8d38: stur            w0, [x1, #0xb]
    // 0xba8d3c: r0 = TextSpan()
    //     0xba8d3c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xba8d40: ldur            x1, [fp, #-0x10]
    // 0xba8d44: StoreField: r0->field_f = r1
    //     0xba8d44: stur            w1, [x0, #0xf]
    // 0xba8d48: r1 = Instance__DeferringMouseCursor
    //     0xba8d48: ldr             x1, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xba8d4c: ArrayStore: r0[0] = r1  ; List_4
    //     0xba8d4c: stur            w1, [x0, #0x17]
    // 0xba8d50: ldur            x1, [fp, #-0x20]
    // 0xba8d54: StoreField: r0->field_7 = r1
    //     0xba8d54: stur            w1, [x0, #7]
    // 0xba8d58: LeaveFrame
    //     0xba8d58: mov             SP, fp
    //     0xba8d5c: ldp             fp, lr, [SP], #0x10
    // 0xba8d60: ret
    //     0xba8d60: ret             
    // 0xba8d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba8d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba8d68: b               #0xba87d4
    // 0xba8d6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba8d6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xba9910, size: 0x264
    // 0xba9910: EnterFrame
    //     0xba9910: stp             fp, lr, [SP, #-0x10]!
    //     0xba9914: mov             fp, SP
    // 0xba9918: AllocStack(0x58)
    //     0xba9918: sub             SP, SP, #0x58
    // 0xba991c: SetupParameters(QuranVerses this /* r0 */)
    //     0xba991c: stur            NULL, [fp, #-8]
    //     0xba9920: movz            x2, #0
    //     0xba9924: add             x0, fp, w2, sxtw #2
    //     0xba9928: ldr             x0, [x0, #0x10]
    //     0xba992c: ldur            w1, [x0, #0x17]
    //     0xba9930: add             x1, x1, HEAP, lsl #32
    //     0xba9934: stur            x1, [fp, #-0x10]
    // 0xba9938: CheckStackOverflow
    //     0xba9938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba993c: cmp             SP, x16
    //     0xba9940: b.ls            #0xba9b6c
    // 0xba9944: InitAsync() -> Future<void?>
    //     0xba9944: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xba9948: bl              #0x661298  ; InitAsyncStub
    // 0xba994c: ldur            x3, [fp, #-0x10]
    // 0xba9950: LoadField: r0 = r3->field_f
    //     0xba9950: ldur            w0, [x3, #0xf]
    // 0xba9954: DecompressPointer r0
    //     0xba9954: add             x0, x0, HEAP, lsl #32
    // 0xba9958: LoadField: r2 = r0->field_7
    //     0xba9958: ldur            x2, [x0, #7]
    // 0xba995c: LoadField: r4 = r3->field_b
    //     0xba995c: ldur            w4, [x3, #0xb]
    // 0xba9960: DecompressPointer r4
    //     0xba9960: add             x4, x4, HEAP, lsl #32
    // 0xba9964: stur            x4, [fp, #-0x20]
    // 0xba9968: LoadField: r5 = r4->field_b
    //     0xba9968: ldur            w5, [x4, #0xb]
    // 0xba996c: DecompressPointer r5
    //     0xba996c: add             x5, x5, HEAP, lsl #32
    // 0xba9970: stur            x5, [fp, #-0x18]
    // 0xba9974: LoadField: r0 = r5->field_f
    //     0xba9974: ldur            w0, [x5, #0xf]
    // 0xba9978: DecompressPointer r0
    //     0xba9978: add             x0, x0, HEAP, lsl #32
    // 0xba997c: LoadField: r1 = r0->field_23
    //     0xba997c: ldur            w1, [x0, #0x23]
    // 0xba9980: DecompressPointer r1
    //     0xba9980: add             x1, x1, HEAP, lsl #32
    // 0xba9984: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xba9984: ldur            w6, [x1, #0x17]
    // 0xba9988: DecompressPointer r6
    //     0xba9988: add             x6, x6, HEAP, lsl #32
    // 0xba998c: r0 = BoxInt64Instr(r2)
    //     0xba998c: sbfiz           x0, x2, #1, #0x1f
    //     0xba9990: cmp             x2, x0, asr #1
    //     0xba9994: b.eq            #0xba99a0
    //     0xba9998: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba999c: stur            x2, [x0, #7]
    // 0xba99a0: mov             x1, x6
    // 0xba99a4: mov             x2, x0
    // 0xba99a8: r0 = onSelected()
    //     0xba99a8: bl              #0x8ea230  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onSelected
    // 0xba99ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba99ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba99b0: ldr             x0, [x0, #0x2670]
    //     0xba99b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba99b8: cmp             w0, w16
    //     0xba99bc: b.ne            #0xba99c8
    //     0xba99c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xba99c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xba99c8: ldur            x0, [fp, #-0x10]
    // 0xba99cc: LoadField: r3 = r0->field_f
    //     0xba99cc: ldur            w3, [x0, #0xf]
    // 0xba99d0: DecompressPointer r3
    //     0xba99d0: add             x3, x3, HEAP, lsl #32
    // 0xba99d4: ldur            x1, [fp, #-0x20]
    // 0xba99d8: stur            x3, [fp, #-0x30]
    // 0xba99dc: LoadField: r4 = r1->field_f
    //     0xba99dc: ldur            w4, [x1, #0xf]
    // 0xba99e0: DecompressPointer r4
    //     0xba99e0: add             x4, x4, HEAP, lsl #32
    // 0xba99e4: ldur            x5, [fp, #-0x18]
    // 0xba99e8: stur            x4, [fp, #-0x28]
    // 0xba99ec: LoadField: r1 = r5->field_f
    //     0xba99ec: ldur            w1, [x5, #0xf]
    // 0xba99f0: DecompressPointer r1
    //     0xba99f0: add             x1, x1, HEAP, lsl #32
    // 0xba99f4: LoadField: r2 = r1->field_f
    //     0xba99f4: ldur            w2, [x1, #0xf]
    // 0xba99f8: DecompressPointer r2
    //     0xba99f8: add             x2, x2, HEAP, lsl #32
    // 0xba99fc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xba99fc: ldur            w1, [x2, #0x17]
    // 0xba9a00: DecompressPointer r1
    //     0xba9a00: add             x1, x1, HEAP, lsl #32
    // 0xba9a04: mov             x2, x4
    // 0xba9a08: r0 = findSurah()
    //     0xba9a08: bl              #0x8c23e0  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::findSurah
    // 0xba9a0c: mov             x1, x0
    // 0xba9a10: stur            x1, [fp, #-0x20]
    // 0xba9a14: r0 = Await()
    //     0xba9a14: bl              #0x661044  ; AwaitStub
    // 0xba9a18: LoadField: r2 = r0->field_23
    //     0xba9a18: ldur            w2, [x0, #0x23]
    // 0xba9a1c: DecompressPointer r2
    //     0xba9a1c: add             x2, x2, HEAP, lsl #32
    // 0xba9a20: ldur            x3, [fp, #-0x18]
    // 0xba9a24: stur            x2, [fp, #-0x48]
    // 0xba9a28: LoadField: r4 = r3->field_f
    //     0xba9a28: ldur            w4, [x3, #0xf]
    // 0xba9a2c: DecompressPointer r4
    //     0xba9a2c: add             x4, x4, HEAP, lsl #32
    // 0xba9a30: LoadField: r5 = r4->field_27
    //     0xba9a30: ldur            w5, [x4, #0x27]
    // 0xba9a34: DecompressPointer r5
    //     0xba9a34: add             x5, x5, HEAP, lsl #32
    // 0xba9a38: ldur            x6, [fp, #-0x10]
    // 0xba9a3c: stur            x5, [fp, #-0x40]
    // 0xba9a40: LoadField: r0 = r6->field_f
    //     0xba9a40: ldur            w0, [x6, #0xf]
    // 0xba9a44: DecompressPointer r0
    //     0xba9a44: add             x0, x0, HEAP, lsl #32
    // 0xba9a48: LoadField: r7 = r0->field_43
    //     0xba9a48: ldur            w7, [x0, #0x43]
    // 0xba9a4c: DecompressPointer r7
    //     0xba9a4c: add             x7, x7, HEAP, lsl #32
    // 0xba9a50: stur            x7, [fp, #-0x38]
    // 0xba9a54: LoadField: r8 = r4->field_2b
    //     0xba9a54: ldur            w8, [x4, #0x2b]
    // 0xba9a58: DecompressPointer r8
    //     0xba9a58: add             x8, x8, HEAP, lsl #32
    // 0xba9a5c: LoadField: r9 = r0->field_7
    //     0xba9a5c: ldur            x9, [x0, #7]
    // 0xba9a60: r0 = BoxInt64Instr(r9)
    //     0xba9a60: sbfiz           x0, x9, #1, #0x1f
    //     0xba9a64: cmp             x9, x0, asr #1
    //     0xba9a68: b.eq            #0xba9a74
    //     0xba9a6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba9a70: stur            x9, [x0, #7]
    // 0xba9a74: cmp             w8, w0
    // 0xba9a78: b.eq            #0xba9abc
    // 0xba9a7c: and             w16, w8, w0
    // 0xba9a80: branchIfSmi(r16, 0xba9ab4)
    //     0xba9a80: tbz             w16, #0, #0xba9ab4
    // 0xba9a84: r16 = LoadClassIdInstr(r8)
    //     0xba9a84: ldur            x16, [x8, #-1]
    //     0xba9a88: ubfx            x16, x16, #0xc, #0x14
    // 0xba9a8c: cmp             x16, #0x3d
    // 0xba9a90: b.ne            #0xba9ab4
    // 0xba9a94: r16 = LoadClassIdInstr(r0)
    //     0xba9a94: ldur            x16, [x0, #-1]
    //     0xba9a98: ubfx            x16, x16, #0xc, #0x14
    // 0xba9a9c: cmp             x16, #0x3d
    // 0xba9aa0: b.ne            #0xba9ab4
    // 0xba9aa4: LoadField: r16 = r8->field_7
    //     0xba9aa4: ldur            x16, [x8, #7]
    // 0xba9aa8: LoadField: r17 = r0->field_7
    //     0xba9aa8: ldur            x17, [x0, #7]
    // 0xba9aac: cmp             x16, x17
    // 0xba9ab0: b.eq            #0xba9abc
    // 0xba9ab4: r9 = false
    //     0xba9ab4: add             x9, NULL, #0x30  ; false
    // 0xba9ab8: b               #0xba9ac0
    // 0xba9abc: r9 = true
    //     0xba9abc: add             x9, NULL, #0x20  ; true
    // 0xba9ac0: stur            x9, [fp, #-0x28]
    // 0xba9ac4: LoadField: r0 = r4->field_2f
    //     0xba9ac4: ldur            w0, [x4, #0x2f]
    // 0xba9ac8: DecompressPointer r0
    //     0xba9ac8: add             x0, x0, HEAP, lsl #32
    // 0xba9acc: stur            x0, [fp, #-0x20]
    // 0xba9ad0: r1 = <QuranBottomSheetController>
    //     0xba9ad0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b510] TypeArguments: <QuranBottomSheetController>
    //     0xba9ad4: ldr             x1, [x1, #0x510]
    // 0xba9ad8: r0 = NQuranBottomSheet()
    //     0xba9ad8: bl              #0xb1e5d0  ; AllocateNQuranBottomSheetStub -> NQuranBottomSheet (size=0x34)
    // 0xba9adc: mov             x1, x0
    // 0xba9ae0: ldur            x0, [fp, #-0x30]
    // 0xba9ae4: StoreField: r1->field_13 = r0
    //     0xba9ae4: stur            w0, [x1, #0x13]
    // 0xba9ae8: ldur            x0, [fp, #-0x48]
    // 0xba9aec: ArrayStore: r1[0] = r0  ; List_4
    //     0xba9aec: stur            w0, [x1, #0x17]
    // 0xba9af0: ldur            x0, [fp, #-0x38]
    // 0xba9af4: StoreField: r1->field_1b = r0
    //     0xba9af4: stur            w0, [x1, #0x1b]
    // 0xba9af8: ldur            x0, [fp, #-0x28]
    // 0xba9afc: StoreField: r1->field_23 = r0
    //     0xba9afc: stur            w0, [x1, #0x23]
    // 0xba9b00: r0 = "/quran/quran-page"
    //     0xba9b00: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bc80] "/quran/quran-page"
    //     0xba9b04: ldr             x0, [x0, #0xc80]
    // 0xba9b08: StoreField: r1->field_27 = r0
    //     0xba9b08: stur            w0, [x1, #0x27]
    // 0xba9b0c: ldur            x0, [fp, #-0x40]
    // 0xba9b10: StoreField: r1->field_1f = r0
    //     0xba9b10: stur            w0, [x1, #0x1f]
    // 0xba9b14: r0 = "page"
    //     0xba9b14: add             x0, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xba9b18: ldr             x0, [x0, #0x300]
    // 0xba9b1c: StoreField: r1->field_2b = r0
    //     0xba9b1c: stur            w0, [x1, #0x2b]
    // 0xba9b20: ldur            x0, [fp, #-0x20]
    // 0xba9b24: StoreField: r1->field_2f = r0
    //     0xba9b24: stur            w0, [x1, #0x2f]
    // 0xba9b28: stp             x1, NULL, [SP]
    // 0xba9b2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba9b2c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba9b30: r0 = ExtensionBottomSheet.bottomSheet()
    //     0xba9b30: bl              #0x917724  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionBottomSheet.bottomSheet
    // 0xba9b34: mov             x1, x0
    // 0xba9b38: stur            x1, [fp, #-0x20]
    // 0xba9b3c: r0 = Await()
    //     0xba9b3c: bl              #0x661044  ; AwaitStub
    // 0xba9b40: ldur            x0, [fp, #-0x18]
    // 0xba9b44: LoadField: r1 = r0->field_f
    //     0xba9b44: ldur            w1, [x0, #0xf]
    // 0xba9b48: DecompressPointer r1
    //     0xba9b48: add             x1, x1, HEAP, lsl #32
    // 0xba9b4c: LoadField: r0 = r1->field_23
    //     0xba9b4c: ldur            w0, [x1, #0x23]
    // 0xba9b50: DecompressPointer r0
    //     0xba9b50: add             x0, x0, HEAP, lsl #32
    // 0xba9b54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xba9b54: ldur            w1, [x0, #0x17]
    // 0xba9b58: DecompressPointer r1
    //     0xba9b58: add             x1, x1, HEAP, lsl #32
    // 0xba9b5c: r2 = 0
    //     0xba9b5c: movz            x2, #0
    // 0xba9b60: r0 = onSelected()
    //     0xba9b60: bl              #0x8ea230  ; [package:nuonline/app/modules/quran/quran_page/controllers/quran_page_controller.dart] QuranPageController::onSelected
    // 0xba9b64: r0 = Null
    //     0xba9b64: mov             x0, NULL
    // 0xba9b68: r0 = ReturnAsyncNotFuture()
    //     0xba9b68: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xba9b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9b6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba9b70: b               #0xba9944
  }
  [closure] FontFeature <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xba9b74, size: 0x74
    // 0xba9b74: EnterFrame
    //     0xba9b74: stp             fp, lr, [SP, #-0x10]!
    //     0xba9b78: mov             fp, SP
    // 0xba9b7c: CheckStackOverflow
    //     0xba9b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba9b80: cmp             SP, x16
    //     0xba9b84: b.ls            #0xba9be0
    // 0xba9b88: ldr             x0, [fp, #0x10]
    // 0xba9b8c: r2 = Null
    //     0xba9b8c: mov             x2, NULL
    // 0xba9b90: r1 = Null
    //     0xba9b90: mov             x1, NULL
    // 0xba9b94: branchIfSmi(r0, 0xba9bbc)
    //     0xba9b94: tbz             w0, #0, #0xba9bbc
    // 0xba9b98: r4 = LoadClassIdInstr(r0)
    //     0xba9b98: ldur            x4, [x0, #-1]
    //     0xba9b9c: ubfx            x4, x4, #0xc, #0x14
    // 0xba9ba0: sub             x4, x4, #0x3c
    // 0xba9ba4: cmp             x4, #1
    // 0xba9ba8: b.ls            #0xba9bbc
    // 0xba9bac: r8 = int
    //     0xba9bac: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xba9bb0: r3 = Null
    //     0xba9bb0: add             x3, PP, #0x33, lsl #12  ; [pp+0x330d0] Null
    //     0xba9bb4: ldr             x3, [x3, #0xd0]
    // 0xba9bb8: r0 = int()
    //     0xba9bb8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xba9bbc: ldr             x0, [fp, #0x10]
    // 0xba9bc0: r2 = LoadInt32Instr(r0)
    //     0xba9bc0: sbfx            x2, x0, #1, #0x1f
    //     0xba9bc4: tbz             w0, #0, #0xba9bcc
    //     0xba9bc8: ldur            x2, [x0, #7]
    // 0xba9bcc: r1 = Null
    //     0xba9bcc: mov             x1, NULL
    // 0xba9bd0: r0 = FontFeature.stylisticSet()
    //     0xba9bd0: bl              #0xba69cc  ; [dart:ui] FontFeature::FontFeature.stylisticSet
    // 0xba9bd4: LeaveFrame
    //     0xba9bd4: mov             SP, fp
    //     0xba9bd8: ldp             fp, lr, [SP], #0x10
    // 0xba9bdc: ret
    //     0xba9bdc: ret             
    // 0xba9be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba9be4: b               #0xba9b88
  }
  [closure] QuranSurahInfo <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<Surah>) {
    // ** addr: 0xba9be8, size: 0x34
    // 0xba9be8: EnterFrame
    //     0xba9be8: stp             fp, lr, [SP, #-0x10]!
    //     0xba9bec: mov             fp, SP
    // 0xba9bf0: AllocStack(0x8)
    //     0xba9bf0: sub             SP, SP, #8
    // 0xba9bf4: ldr             x0, [fp, #0x10]
    // 0xba9bf8: LoadField: r1 = r0->field_f
    //     0xba9bf8: ldur            w1, [x0, #0xf]
    // 0xba9bfc: DecompressPointer r1
    //     0xba9bfc: add             x1, x1, HEAP, lsl #32
    // 0xba9c00: stur            x1, [fp, #-8]
    // 0xba9c04: r0 = QuranSurahInfo()
    //     0xba9c04: bl              #0xb1e080  ; AllocateQuranSurahInfoStub -> QuranSurahInfo (size=0x10)
    // 0xba9c08: ldur            x1, [fp, #-8]
    // 0xba9c0c: StoreField: r0->field_b = r1
    //     0xba9c0c: stur            w1, [x0, #0xb]
    // 0xba9c10: LeaveFrame
    //     0xba9c10: mov             SP, fp
    //     0xba9c14: ldp             fp, lr, [SP], #0x10
    // 0xba9c18: ret
    //     0xba9c18: ret             
  }
  [closure] int <anonymous closure>(dynamic, Verse) {
    // ** addr: 0xba9c1c, size: 0x30
    // 0xba9c1c: ldr             x2, [SP]
    // 0xba9c20: LoadField: r3 = r2->field_33
    //     0xba9c20: ldur            x3, [x2, #0x33]
    // 0xba9c24: r0 = BoxInt64Instr(r3)
    //     0xba9c24: sbfiz           x0, x3, #1, #0x1f
    //     0xba9c28: cmp             x3, x0, asr #1
    //     0xba9c2c: b.eq            #0xba9c48
    //     0xba9c30: stp             fp, lr, [SP, #-0x10]!
    //     0xba9c34: mov             fp, SP
    //     0xba9c38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xba9c3c: mov             SP, fp
    //     0xba9c40: ldp             fp, lr, [SP], #0x10
    //     0xba9c44: stur            x3, [x0, #7]
    // 0xba9c48: ret
    //     0xba9c48: ret             
  }
}
