// lib: , url: package:nuonline/app/modules/qurban/views/qurban_view.dart

// class id: 1050491, size: 0x8
class :: {
}

// class id: 5213, size: 0x14, field offset: 0x14
//   const constructor, 
class <PERSON><PERSON>View extends GetView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3b864, size: 0x5c
    // 0xb3b864: EnterFrame
    //     0xb3b864: stp             fp, lr, [SP, #-0x10]!
    //     0xb3b868: mov             fp, SP
    // 0xb3b86c: AllocStack(0x10)
    //     0xb3b86c: sub             SP, SP, #0x10
    // 0xb3b870: CheckStackOverflow
    //     0xb3b870: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3b874: cmp             SP, x16
    //     0xb3b878: b.ls            #0xb3b8b8
    // 0xb3b87c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3b87c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3b880: ldr             x0, [x0, #0x2670]
    //     0xb3b884: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3b888: cmp             w0, w16
    //     0xb3b88c: b.ne            #0xb3b898
    //     0xb3b890: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb3b894: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb3b898: r16 = "/qurban/qurban-news"
    //     0xb3b898: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b2d0] "/qurban/qurban-news"
    //     0xb3b89c: ldr             x16, [x16, #0x2d0]
    // 0xb3b8a0: stp             x16, NULL, [SP]
    // 0xb3b8a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3b8a4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3b8a8: r0 = GetNavigation.toNamed()
    //     0xb3b8a8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb3b8ac: LeaveFrame
    //     0xb3b8ac: mov             SP, fp
    //     0xb3b8b0: ldp             fp, lr, [SP], #0x10
    // 0xb3b8b4: ret
    //     0xb3b8b4: ret             
    // 0xb3b8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3b8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3b8bc: b               #0xb3b87c
  }
  _ build(/* No info */) {
    // ** addr: 0xb3deac, size: 0x58c
    // 0xb3deac: EnterFrame
    //     0xb3deac: stp             fp, lr, [SP, #-0x10]!
    //     0xb3deb0: mov             fp, SP
    // 0xb3deb4: AllocStack(0x58)
    //     0xb3deb4: sub             SP, SP, #0x58
    // 0xb3deb8: SetupParameters(QurbanView this /* r1 => r1, fp-0x8 */)
    //     0xb3deb8: stur            x1, [fp, #-8]
    // 0xb3debc: CheckStackOverflow
    //     0xb3debc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3dec0: cmp             SP, x16
    //     0xb3dec4: b.ls            #0xb3e430
    // 0xb3dec8: r1 = 1
    //     0xb3dec8: movz            x1, #0x1
    // 0xb3decc: r0 = AllocateContext()
    //     0xb3decc: bl              #0xec126c  ; AllocateContextStub
    // 0xb3ded0: mov             x3, x0
    // 0xb3ded4: ldur            x0, [fp, #-8]
    // 0xb3ded8: stur            x3, [fp, #-0x10]
    // 0xb3dedc: StoreField: r3->field_f = r0
    //     0xb3dedc: stur            w0, [x3, #0xf]
    // 0xb3dee0: r1 = Function '<anonymous closure>':.
    //     0xb3dee0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aeb0] AnonymousClosure: (0xb3e9a4), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3dee4: ldr             x1, [x1, #0xeb0]
    // 0xb3dee8: r2 = Null
    //     0xb3dee8: mov             x2, NULL
    // 0xb3deec: r0 = AllocateClosure()
    //     0xb3deec: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3def0: stur            x0, [fp, #-0x18]
    // 0xb3def4: r0 = IconButton()
    //     0xb3def4: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb3def8: mov             x3, x0
    // 0xb3defc: ldur            x0, [fp, #-0x18]
    // 0xb3df00: stur            x3, [fp, #-0x20]
    // 0xb3df04: StoreField: r3->field_3b = r0
    //     0xb3df04: stur            w0, [x3, #0x3b]
    // 0xb3df08: r0 = false
    //     0xb3df08: add             x0, NULL, #0x30  ; false
    // 0xb3df0c: StoreField: r3->field_47 = r0
    //     0xb3df0c: stur            w0, [x3, #0x47]
    // 0xb3df10: r1 = Instance_Icon
    //     0xb3df10: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aeb8] Obj!Icon@e24a31
    //     0xb3df14: ldr             x1, [x1, #0xeb8]
    // 0xb3df18: StoreField: r3->field_1f = r1
    //     0xb3df18: stur            w1, [x3, #0x1f]
    // 0xb3df1c: r4 = Instance__IconButtonVariant
    //     0xb3df1c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb3df20: ldr             x4, [x4, #0xf78]
    // 0xb3df24: StoreField: r3->field_63 = r4
    //     0xb3df24: stur            w4, [x3, #0x63]
    // 0xb3df28: r1 = Function '<anonymous closure>':.
    //     0xb3df28: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aec0] AnonymousClosure: (0xb3b864), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3df2c: ldr             x1, [x1, #0xec0]
    // 0xb3df30: r2 = Null
    //     0xb3df30: mov             x2, NULL
    // 0xb3df34: r0 = AllocateClosure()
    //     0xb3df34: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3df38: stur            x0, [fp, #-0x18]
    // 0xb3df3c: r0 = IconButton()
    //     0xb3df3c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb3df40: mov             x3, x0
    // 0xb3df44: ldur            x0, [fp, #-0x18]
    // 0xb3df48: stur            x3, [fp, #-0x28]
    // 0xb3df4c: StoreField: r3->field_3b = r0
    //     0xb3df4c: stur            w0, [x3, #0x3b]
    // 0xb3df50: r0 = false
    //     0xb3df50: add             x0, NULL, #0x30  ; false
    // 0xb3df54: StoreField: r3->field_47 = r0
    //     0xb3df54: stur            w0, [x3, #0x47]
    // 0xb3df58: r1 = Instance_Icon
    //     0xb3df58: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aec8] Obj!Icon@e24531
    //     0xb3df5c: ldr             x1, [x1, #0xec8]
    // 0xb3df60: StoreField: r3->field_1f = r1
    //     0xb3df60: stur            w1, [x3, #0x1f]
    // 0xb3df64: r1 = Instance__IconButtonVariant
    //     0xb3df64: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb3df68: ldr             x1, [x1, #0xf78]
    // 0xb3df6c: StoreField: r3->field_63 = r1
    //     0xb3df6c: stur            w1, [x3, #0x63]
    // 0xb3df70: r1 = Null
    //     0xb3df70: mov             x1, NULL
    // 0xb3df74: r2 = 4
    //     0xb3df74: movz            x2, #0x4
    // 0xb3df78: r0 = AllocateArray()
    //     0xb3df78: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3df7c: mov             x2, x0
    // 0xb3df80: ldur            x0, [fp, #-0x20]
    // 0xb3df84: stur            x2, [fp, #-0x18]
    // 0xb3df88: StoreField: r2->field_f = r0
    //     0xb3df88: stur            w0, [x2, #0xf]
    // 0xb3df8c: ldur            x0, [fp, #-0x28]
    // 0xb3df90: StoreField: r2->field_13 = r0
    //     0xb3df90: stur            w0, [x2, #0x13]
    // 0xb3df94: r1 = <Widget>
    //     0xb3df94: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3df98: r0 = AllocateGrowableArray()
    //     0xb3df98: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3df9c: mov             x1, x0
    // 0xb3dfa0: ldur            x0, [fp, #-0x18]
    // 0xb3dfa4: stur            x1, [fp, #-0x20]
    // 0xb3dfa8: StoreField: r1->field_f = r0
    //     0xb3dfa8: stur            w0, [x1, #0xf]
    // 0xb3dfac: r0 = 4
    //     0xb3dfac: movz            x0, #0x4
    // 0xb3dfb0: StoreField: r1->field_b = r0
    //     0xb3dfb0: stur            w0, [x1, #0xb]
    // 0xb3dfb4: r0 = AppBar()
    //     0xb3dfb4: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb3dfb8: stur            x0, [fp, #-0x18]
    // 0xb3dfbc: r16 = Instance_Text
    //     0xb3dfbc: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2aed0] Obj!Text@e229a1
    //     0xb3dfc0: ldr             x16, [x16, #0xed0]
    // 0xb3dfc4: r30 = false
    //     0xb3dfc4: add             lr, NULL, #0x30  ; false
    // 0xb3dfc8: stp             lr, x16, [SP, #8]
    // 0xb3dfcc: ldur            x16, [fp, #-0x20]
    // 0xb3dfd0: str             x16, [SP]
    // 0xb3dfd4: mov             x1, x0
    // 0xb3dfd8: r4 = const [0, 0x4, 0x3, 0x1, actions, 0x3, centerTitle, 0x2, title, 0x1, null]
    //     0xb3dfd8: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2aed8] List(11) [0, 0x4, 0x3, 0x1, "actions", 0x3, "centerTitle", 0x2, "title", 0x1, Null]
    //     0xb3dfdc: ldr             x4, [x4, #0xed8]
    // 0xb3dfe0: r0 = AppBar()
    //     0xb3dfe0: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb3dfe4: ldur            x1, [fp, #-8]
    // 0xb3dfe8: r0 = controller()
    //     0xb3dfe8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb3dfec: mov             x1, x0
    // 0xb3dff0: LoadField: r0 = r1->field_23
    //     0xb3dff0: ldur            w0, [x1, #0x23]
    // 0xb3dff4: DecompressPointer r0
    //     0xb3dff4: add             x0, x0, HEAP, lsl #32
    // 0xb3dff8: r16 = Sentinel
    //     0xb3dff8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb3dffc: cmp             w0, w16
    // 0xb3e000: b.ne            #0xb3e010
    // 0xb3e004: r2 = tabController
    //     0xb3e004: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2aee0] Field <QurbanController.tabController>: late final (offset: 0x24)
    //     0xb3e008: ldr             x2, [x2, #0xee0]
    // 0xb3e00c: r0 = InitLateFinalInstanceField()
    //     0xb3e00c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb3e010: stur            x0, [fp, #-0x20]
    // 0xb3e014: r0 = TabBar()
    //     0xb3e014: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xb3e018: mov             x2, x0
    // 0xb3e01c: r0 = const [Instance of 'Tab', Instance of 'Tab', Instance of 'Tab']
    //     0xb3e01c: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2aee8] List<Widget>(3)
    //     0xb3e020: ldr             x0, [x0, #0xee8]
    // 0xb3e024: stur            x2, [fp, #-0x28]
    // 0xb3e028: StoreField: r2->field_b = r0
    //     0xb3e028: stur            w0, [x2, #0xb]
    // 0xb3e02c: ldur            x0, [fp, #-0x20]
    // 0xb3e030: StoreField: r2->field_f = r0
    //     0xb3e030: stur            w0, [x2, #0xf]
    // 0xb3e034: r0 = false
    //     0xb3e034: add             x0, NULL, #0x30  ; false
    // 0xb3e038: StoreField: r2->field_13 = r0
    //     0xb3e038: stur            w0, [x2, #0x13]
    // 0xb3e03c: r3 = true
    //     0xb3e03c: add             x3, NULL, #0x20  ; true
    // 0xb3e040: StoreField: r2->field_2f = r3
    //     0xb3e040: stur            w3, [x2, #0x2f]
    // 0xb3e044: d0 = 2.000000
    //     0xb3e044: fmov            d0, #2.00000000
    // 0xb3e048: StoreField: r2->field_1f = d0
    //     0xb3e048: stur            d0, [x2, #0x1f]
    // 0xb3e04c: r1 = Instance_EdgeInsets
    //     0xb3e04c: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb3e050: StoreField: r2->field_27 = r1
    //     0xb3e050: stur            w1, [x2, #0x27]
    // 0xb3e054: r4 = Instance_DragStartBehavior
    //     0xb3e054: ldr             x4, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb3e058: StoreField: r2->field_57 = r4
    //     0xb3e058: stur            w4, [x2, #0x57]
    // 0xb3e05c: StoreField: r2->field_7f = r3
    //     0xb3e05c: stur            w3, [x2, #0x7f]
    // 0xb3e060: ldur            x1, [fp, #-8]
    // 0xb3e064: r0 = controller()
    //     0xb3e064: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb3e068: mov             x1, x0
    // 0xb3e06c: LoadField: r0 = r1->field_23
    //     0xb3e06c: ldur            w0, [x1, #0x23]
    // 0xb3e070: DecompressPointer r0
    //     0xb3e070: add             x0, x0, HEAP, lsl #32
    // 0xb3e074: r16 = Sentinel
    //     0xb3e074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb3e078: cmp             w0, w16
    // 0xb3e07c: b.ne            #0xb3e08c
    // 0xb3e080: r2 = tabController
    //     0xb3e080: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2aee0] Field <QurbanController.tabController>: late final (offset: 0x24)
    //     0xb3e084: ldr             x2, [x2, #0xee0]
    // 0xb3e088: r0 = InitLateFinalInstanceField()
    //     0xb3e088: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb3e08c: stur            x0, [fp, #-8]
    // 0xb3e090: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3e090: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3e094: ldr             x0, [x0, #0x2670]
    //     0xb3e098: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3e09c: cmp             w0, w16
    //     0xb3e0a0: b.ne            #0xb3e0ac
    //     0xb3e0a4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb3e0a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb3e0ac: r16 = <DonationRepository>
    //     0xb3e0ac: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xb3e0b0: ldr             x16, [x16, #0xb0]
    // 0xb3e0b4: r30 = "donation_repo"
    //     0xb3e0b4: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xb3e0b8: ldr             lr, [lr, #0xb8]
    // 0xb3e0bc: stp             lr, x16, [SP]
    // 0xb3e0c0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb3e0c0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb3e0c4: r0 = Inst.find()
    //     0xb3e0c4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb3e0c8: r1 = <List<Campaign>>
    //     0xb3e0c8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aef0] TypeArguments: <List<Campaign>>
    //     0xb3e0cc: ldr             x1, [x1, #0xef0]
    // 0xb3e0d0: stur            x0, [fp, #-0x20]
    // 0xb3e0d4: r0 = QurbanListController()
    //     0xb3e0d4: bl              #0xb3e590  ; AllocateQurbanListControllerStub -> QurbanListController (size=0x30)
    // 0xb3e0d8: mov             x2, x0
    // 0xb3e0dc: ldur            x0, [fp, #-0x20]
    // 0xb3e0e0: stur            x2, [fp, #-0x30]
    // 0xb3e0e4: StoreField: r2->field_2b = r0
    //     0xb3e0e4: stur            w0, [x2, #0x2b]
    // 0xb3e0e8: mov             x1, x2
    // 0xb3e0ec: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0xb3e0ec: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0xb3e0f0: r1 = <QurbanListController>
    //     0xb3e0f0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aef8] TypeArguments: <QurbanListController>
    //     0xb3e0f4: ldr             x1, [x1, #0xef8]
    // 0xb3e0f8: r0 = GetBuilder()
    //     0xb3e0f8: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb3e0fc: mov             x3, x0
    // 0xb3e100: ldur            x0, [fp, #-0x30]
    // 0xb3e104: stur            x3, [fp, #-0x20]
    // 0xb3e108: StoreField: r3->field_3b = r0
    //     0xb3e108: stur            w0, [x3, #0x3b]
    // 0xb3e10c: r0 = true
    //     0xb3e10c: add             x0, NULL, #0x20  ; true
    // 0xb3e110: StoreField: r3->field_13 = r0
    //     0xb3e110: stur            w0, [x3, #0x13]
    // 0xb3e114: r1 = Function '<anonymous closure>':.
    //     0xb3e114: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af00] AnonymousClosure: (0xb3e998), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e118: ldr             x1, [x1, #0xf00]
    // 0xb3e11c: r2 = Null
    //     0xb3e11c: mov             x2, NULL
    // 0xb3e120: r0 = AllocateClosure()
    //     0xb3e120: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e124: mov             x1, x0
    // 0xb3e128: ldur            x0, [fp, #-0x20]
    // 0xb3e12c: StoreField: r0->field_f = r1
    //     0xb3e12c: stur            w1, [x0, #0xf]
    // 0xb3e130: r1 = false
    //     0xb3e130: add             x1, NULL, #0x30  ; false
    // 0xb3e134: StoreField: r0->field_1f = r1
    //     0xb3e134: stur            w1, [x0, #0x1f]
    // 0xb3e138: StoreField: r0->field_23 = r1
    //     0xb3e138: stur            w1, [x0, #0x23]
    // 0xb3e13c: r16 = <DonationRepository>
    //     0xb3e13c: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xb3e140: ldr             x16, [x16, #0xb0]
    // 0xb3e144: r30 = "donation_repo"
    //     0xb3e144: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xb3e148: ldr             lr, [lr, #0xb8]
    // 0xb3e14c: stp             lr, x16, [SP]
    // 0xb3e150: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb3e150: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb3e154: r0 = Inst.find()
    //     0xb3e154: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb3e158: stur            x0, [fp, #-0x30]
    // 0xb3e15c: r0 = TransactionHistoryController()
    //     0xb3e15c: bl              #0x811f60  ; AllocateTransactionHistoryControllerStub -> TransactionHistoryController (size=0x4c)
    // 0xb3e160: mov             x1, x0
    // 0xb3e164: ldur            x2, [fp, #-0x30]
    // 0xb3e168: r3 = Instance_PaymentType
    //     0xb3e168: add             x3, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xb3e16c: ldr             x3, [x3, #0x5f0]
    // 0xb3e170: stur            x0, [fp, #-0x30]
    // 0xb3e174: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb3e174: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb3e178: r0 = TransactionHistoryController()
    //     0xb3e178: bl              #0x811c3c  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] TransactionHistoryController::TransactionHistoryController
    // 0xb3e17c: r1 = <TransactionHistoryController>
    //     0xb3e17c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af08] TypeArguments: <TransactionHistoryController>
    //     0xb3e180: ldr             x1, [x1, #0xf08]
    // 0xb3e184: r0 = GetBuilder()
    //     0xb3e184: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb3e188: mov             x3, x0
    // 0xb3e18c: ldur            x0, [fp, #-0x30]
    // 0xb3e190: stur            x3, [fp, #-0x38]
    // 0xb3e194: StoreField: r3->field_3b = r0
    //     0xb3e194: stur            w0, [x3, #0x3b]
    // 0xb3e198: r0 = true
    //     0xb3e198: add             x0, NULL, #0x20  ; true
    // 0xb3e19c: StoreField: r3->field_13 = r0
    //     0xb3e19c: stur            w0, [x3, #0x13]
    // 0xb3e1a0: r1 = Function '<anonymous closure>':.
    //     0xb3e1a0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af10] AnonymousClosure: (0xb3e70c), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e1a4: ldr             x1, [x1, #0xf10]
    // 0xb3e1a8: r2 = Null
    //     0xb3e1a8: mov             x2, NULL
    // 0xb3e1ac: r0 = AllocateClosure()
    //     0xb3e1ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e1b0: mov             x1, x0
    // 0xb3e1b4: ldur            x0, [fp, #-0x38]
    // 0xb3e1b8: StoreField: r0->field_f = r1
    //     0xb3e1b8: stur            w1, [x0, #0xf]
    // 0xb3e1bc: r1 = false
    //     0xb3e1bc: add             x1, NULL, #0x30  ; false
    // 0xb3e1c0: StoreField: r0->field_1f = r1
    //     0xb3e1c0: stur            w1, [x0, #0x1f]
    // 0xb3e1c4: StoreField: r0->field_23 = r1
    //     0xb3e1c4: stur            w1, [x0, #0x23]
    // 0xb3e1c8: r16 = <DonationRepository>
    //     0xb3e1c8: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0xb3e1cc: ldr             x16, [x16, #0xb0]
    // 0xb3e1d0: r30 = "donation_repo"
    //     0xb3e1d0: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0xb3e1d4: ldr             lr, [lr, #0xb8]
    // 0xb3e1d8: stp             lr, x16, [SP]
    // 0xb3e1dc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xb3e1dc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xb3e1e0: r0 = Inst.find()
    //     0xb3e1e0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb3e1e4: r1 = <List<Transaction>, Transaction>
    //     0xb3e1e4: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af18] TypeArguments: <List<Transaction>, Transaction>
    //     0xb3e1e8: ldr             x1, [x1, #0xf18]
    // 0xb3e1ec: stur            x0, [fp, #-0x30]
    // 0xb3e1f0: r0 = HistoryController()
    //     0xb3e1f0: bl              #0xb3e584  ; AllocateHistoryControllerStub -> HistoryController (size=0x40)
    // 0xb3e1f4: stur            x0, [fp, #-0x40]
    // 0xb3e1f8: r16 = true
    //     0xb3e1f8: add             x16, NULL, #0x20  ; true
    // 0xb3e1fc: str             x16, [SP]
    // 0xb3e200: mov             x1, x0
    // 0xb3e204: ldur            x2, [fp, #-0x30]
    // 0xb3e208: r4 = const [0, 0x3, 0x1, 0x2, isQurban, 0x2, null]
    //     0xb3e208: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2af20] List(7) [0, 0x3, 0x1, 0x2, "isQurban", 0x2, Null]
    //     0xb3e20c: ldr             x4, [x4, #0xf20]
    // 0xb3e210: r0 = HistoryController()
    //     0xb3e210: bl              #0xb3e438  ; [package:nuonline/app/modules/donation/controllers/history_tab_controller.dart] HistoryController::HistoryController
    // 0xb3e214: r1 = <HistoryController>
    //     0xb3e214: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af28] TypeArguments: <HistoryController>
    //     0xb3e218: ldr             x1, [x1, #0xf28]
    // 0xb3e21c: r0 = GetBuilder()
    //     0xb3e21c: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xb3e220: mov             x3, x0
    // 0xb3e224: ldur            x0, [fp, #-0x40]
    // 0xb3e228: stur            x3, [fp, #-0x30]
    // 0xb3e22c: StoreField: r3->field_3b = r0
    //     0xb3e22c: stur            w0, [x3, #0x3b]
    // 0xb3e230: r0 = true
    //     0xb3e230: add             x0, NULL, #0x20  ; true
    // 0xb3e234: StoreField: r3->field_13 = r0
    //     0xb3e234: stur            w0, [x3, #0x13]
    // 0xb3e238: r1 = Function '<anonymous closure>':.
    //     0xb3e238: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af30] AnonymousClosure: (0xb3e60c), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e23c: ldr             x1, [x1, #0xf30]
    // 0xb3e240: r2 = Null
    //     0xb3e240: mov             x2, NULL
    // 0xb3e244: r0 = AllocateClosure()
    //     0xb3e244: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e248: mov             x1, x0
    // 0xb3e24c: ldur            x0, [fp, #-0x30]
    // 0xb3e250: StoreField: r0->field_f = r1
    //     0xb3e250: stur            w1, [x0, #0xf]
    // 0xb3e254: r3 = false
    //     0xb3e254: add             x3, NULL, #0x30  ; false
    // 0xb3e258: StoreField: r0->field_1f = r3
    //     0xb3e258: stur            w3, [x0, #0x1f]
    // 0xb3e25c: StoreField: r0->field_23 = r3
    //     0xb3e25c: stur            w3, [x0, #0x23]
    // 0xb3e260: r1 = Null
    //     0xb3e260: mov             x1, NULL
    // 0xb3e264: r2 = 6
    //     0xb3e264: movz            x2, #0x6
    // 0xb3e268: r0 = AllocateArray()
    //     0xb3e268: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3e26c: mov             x2, x0
    // 0xb3e270: ldur            x0, [fp, #-0x20]
    // 0xb3e274: stur            x2, [fp, #-0x40]
    // 0xb3e278: StoreField: r2->field_f = r0
    //     0xb3e278: stur            w0, [x2, #0xf]
    // 0xb3e27c: ldur            x0, [fp, #-0x38]
    // 0xb3e280: StoreField: r2->field_13 = r0
    //     0xb3e280: stur            w0, [x2, #0x13]
    // 0xb3e284: ldur            x0, [fp, #-0x30]
    // 0xb3e288: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3e288: stur            w0, [x2, #0x17]
    // 0xb3e28c: r1 = <Widget>
    //     0xb3e28c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3e290: r0 = AllocateGrowableArray()
    //     0xb3e290: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3e294: mov             x1, x0
    // 0xb3e298: ldur            x0, [fp, #-0x40]
    // 0xb3e29c: stur            x1, [fp, #-0x20]
    // 0xb3e2a0: StoreField: r1->field_f = r0
    //     0xb3e2a0: stur            w0, [x1, #0xf]
    // 0xb3e2a4: r2 = 6
    //     0xb3e2a4: movz            x2, #0x6
    // 0xb3e2a8: StoreField: r1->field_b = r2
    //     0xb3e2a8: stur            w2, [x1, #0xb]
    // 0xb3e2ac: r0 = TabBarView()
    //     0xb3e2ac: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xb3e2b0: mov             x2, x0
    // 0xb3e2b4: ldur            x0, [fp, #-0x20]
    // 0xb3e2b8: stur            x2, [fp, #-0x30]
    // 0xb3e2bc: StoreField: r2->field_f = r0
    //     0xb3e2bc: stur            w0, [x2, #0xf]
    // 0xb3e2c0: ldur            x0, [fp, #-8]
    // 0xb3e2c4: StoreField: r2->field_b = r0
    //     0xb3e2c4: stur            w0, [x2, #0xb]
    // 0xb3e2c8: r0 = Instance_DragStartBehavior
    //     0xb3e2c8: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb3e2cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3e2cc: stur            w0, [x2, #0x17]
    // 0xb3e2d0: d0 = 1.000000
    //     0xb3e2d0: fmov            d0, #1.00000000
    // 0xb3e2d4: StoreField: r2->field_1b = d0
    //     0xb3e2d4: stur            d0, [x2, #0x1b]
    // 0xb3e2d8: r1 = Instance_Clip
    //     0xb3e2d8: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb3e2dc: ldr             x1, [x1, #0x7c0]
    // 0xb3e2e0: StoreField: r2->field_23 = r1
    //     0xb3e2e0: stur            w1, [x2, #0x23]
    // 0xb3e2e4: r1 = <FlexParentData>
    //     0xb3e2e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb3e2e8: ldr             x1, [x1, #0x720]
    // 0xb3e2ec: r0 = Expanded()
    //     0xb3e2ec: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb3e2f0: mov             x1, x0
    // 0xb3e2f4: r0 = 1
    //     0xb3e2f4: movz            x0, #0x1
    // 0xb3e2f8: stur            x1, [fp, #-8]
    // 0xb3e2fc: StoreField: r1->field_13 = r0
    //     0xb3e2fc: stur            x0, [x1, #0x13]
    // 0xb3e300: r0 = Instance_FlexFit
    //     0xb3e300: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb3e304: ldr             x0, [x0, #0x728]
    // 0xb3e308: StoreField: r1->field_1b = r0
    //     0xb3e308: stur            w0, [x1, #0x1b]
    // 0xb3e30c: ldur            x0, [fp, #-0x30]
    // 0xb3e310: StoreField: r1->field_b = r0
    //     0xb3e310: stur            w0, [x1, #0xb]
    // 0xb3e314: r0 = Obx()
    //     0xb3e314: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb3e318: ldur            x2, [fp, #-0x10]
    // 0xb3e31c: r1 = Function '<anonymous closure>':.
    //     0xb3e31c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af38] AnonymousClosure: (0xb3e59c), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e320: ldr             x1, [x1, #0xf38]
    // 0xb3e324: stur            x0, [fp, #-0x10]
    // 0xb3e328: r0 = AllocateClosure()
    //     0xb3e328: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e32c: mov             x1, x0
    // 0xb3e330: ldur            x0, [fp, #-0x10]
    // 0xb3e334: StoreField: r0->field_b = r1
    //     0xb3e334: stur            w1, [x0, #0xb]
    // 0xb3e338: r1 = Null
    //     0xb3e338: mov             x1, NULL
    // 0xb3e33c: r2 = 6
    //     0xb3e33c: movz            x2, #0x6
    // 0xb3e340: r0 = AllocateArray()
    //     0xb3e340: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3e344: mov             x2, x0
    // 0xb3e348: ldur            x0, [fp, #-0x28]
    // 0xb3e34c: stur            x2, [fp, #-0x20]
    // 0xb3e350: StoreField: r2->field_f = r0
    //     0xb3e350: stur            w0, [x2, #0xf]
    // 0xb3e354: ldur            x0, [fp, #-8]
    // 0xb3e358: StoreField: r2->field_13 = r0
    //     0xb3e358: stur            w0, [x2, #0x13]
    // 0xb3e35c: ldur            x0, [fp, #-0x10]
    // 0xb3e360: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3e360: stur            w0, [x2, #0x17]
    // 0xb3e364: r1 = <Widget>
    //     0xb3e364: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb3e368: r0 = AllocateGrowableArray()
    //     0xb3e368: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb3e36c: mov             x1, x0
    // 0xb3e370: ldur            x0, [fp, #-0x20]
    // 0xb3e374: stur            x1, [fp, #-8]
    // 0xb3e378: StoreField: r1->field_f = r0
    //     0xb3e378: stur            w0, [x1, #0xf]
    // 0xb3e37c: r0 = 6
    //     0xb3e37c: movz            x0, #0x6
    // 0xb3e380: StoreField: r1->field_b = r0
    //     0xb3e380: stur            w0, [x1, #0xb]
    // 0xb3e384: r0 = Column()
    //     0xb3e384: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3e388: mov             x1, x0
    // 0xb3e38c: r0 = Instance_Axis
    //     0xb3e38c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb3e390: stur            x1, [fp, #-0x10]
    // 0xb3e394: StoreField: r1->field_f = r0
    //     0xb3e394: stur            w0, [x1, #0xf]
    // 0xb3e398: r0 = Instance_MainAxisAlignment
    //     0xb3e398: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb3e39c: ldr             x0, [x0, #0x730]
    // 0xb3e3a0: StoreField: r1->field_13 = r0
    //     0xb3e3a0: stur            w0, [x1, #0x13]
    // 0xb3e3a4: r0 = Instance_MainAxisSize
    //     0xb3e3a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb3e3a8: ldr             x0, [x0, #0x738]
    // 0xb3e3ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3e3ac: stur            w0, [x1, #0x17]
    // 0xb3e3b0: r0 = Instance_CrossAxisAlignment
    //     0xb3e3b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb3e3b4: ldr             x0, [x0, #0x740]
    // 0xb3e3b8: StoreField: r1->field_1b = r0
    //     0xb3e3b8: stur            w0, [x1, #0x1b]
    // 0xb3e3bc: r0 = Instance_VerticalDirection
    //     0xb3e3bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb3e3c0: ldr             x0, [x0, #0x748]
    // 0xb3e3c4: StoreField: r1->field_23 = r0
    //     0xb3e3c4: stur            w0, [x1, #0x23]
    // 0xb3e3c8: r0 = Instance_Clip
    //     0xb3e3c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb3e3cc: ldr             x0, [x0, #0x750]
    // 0xb3e3d0: StoreField: r1->field_2b = r0
    //     0xb3e3d0: stur            w0, [x1, #0x2b]
    // 0xb3e3d4: StoreField: r1->field_2f = rZR
    //     0xb3e3d4: stur            xzr, [x1, #0x2f]
    // 0xb3e3d8: ldur            x0, [fp, #-8]
    // 0xb3e3dc: StoreField: r1->field_b = r0
    //     0xb3e3dc: stur            w0, [x1, #0xb]
    // 0xb3e3e0: r0 = Scaffold()
    //     0xb3e3e0: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb3e3e4: ldur            x1, [fp, #-0x18]
    // 0xb3e3e8: StoreField: r0->field_13 = r1
    //     0xb3e3e8: stur            w1, [x0, #0x13]
    // 0xb3e3ec: ldur            x1, [fp, #-0x10]
    // 0xb3e3f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb3e3f0: stur            w1, [x0, #0x17]
    // 0xb3e3f4: r1 = Instance_AlignmentDirectional
    //     0xb3e3f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb3e3f8: ldr             x1, [x1, #0x758]
    // 0xb3e3fc: StoreField: r0->field_2b = r1
    //     0xb3e3fc: stur            w1, [x0, #0x2b]
    // 0xb3e400: r1 = true
    //     0xb3e400: add             x1, NULL, #0x20  ; true
    // 0xb3e404: StoreField: r0->field_53 = r1
    //     0xb3e404: stur            w1, [x0, #0x53]
    // 0xb3e408: r2 = Instance_DragStartBehavior
    //     0xb3e408: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb3e40c: StoreField: r0->field_57 = r2
    //     0xb3e40c: stur            w2, [x0, #0x57]
    // 0xb3e410: r2 = false
    //     0xb3e410: add             x2, NULL, #0x30  ; false
    // 0xb3e414: StoreField: r0->field_b = r2
    //     0xb3e414: stur            w2, [x0, #0xb]
    // 0xb3e418: StoreField: r0->field_f = r2
    //     0xb3e418: stur            w2, [x0, #0xf]
    // 0xb3e41c: StoreField: r0->field_5f = r1
    //     0xb3e41c: stur            w1, [x0, #0x5f]
    // 0xb3e420: StoreField: r0->field_63 = r1
    //     0xb3e420: stur            w1, [x0, #0x63]
    // 0xb3e424: LeaveFrame
    //     0xb3e424: mov             SP, fp
    //     0xb3e428: ldp             fp, lr, [SP], #0x10
    // 0xb3e42c: ret
    //     0xb3e42c: ret             
    // 0xb3e430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e434: b               #0xb3dec8
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb3e59c, size: 0x70
    // 0xb3e59c: EnterFrame
    //     0xb3e59c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e5a0: mov             fp, SP
    // 0xb3e5a4: ldr             x0, [fp, #0x10]
    // 0xb3e5a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb3e5a8: ldur            w1, [x0, #0x17]
    // 0xb3e5ac: DecompressPointer r1
    //     0xb3e5ac: add             x1, x1, HEAP, lsl #32
    // 0xb3e5b0: CheckStackOverflow
    //     0xb3e5b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e5b4: cmp             SP, x16
    //     0xb3e5b8: b.ls            #0xb3e604
    // 0xb3e5bc: LoadField: r0 = r1->field_f
    //     0xb3e5bc: ldur            w0, [x1, #0xf]
    // 0xb3e5c0: DecompressPointer r0
    //     0xb3e5c0: add             x0, x0, HEAP, lsl #32
    // 0xb3e5c4: mov             x1, x0
    // 0xb3e5c8: r0 = controller()
    //     0xb3e5c8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb3e5cc: LoadField: r1 = r0->field_27
    //     0xb3e5cc: ldur            w1, [x0, #0x27]
    // 0xb3e5d0: DecompressPointer r1
    //     0xb3e5d0: add             x1, x1, HEAP, lsl #32
    // 0xb3e5d4: r0 = RxBoolExt.isFalse()
    //     0xb3e5d4: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xb3e5d8: tbnz            w0, #4, #0xb3e5f0
    // 0xb3e5dc: r0 = Instance_SizedBox
    //     0xb3e5dc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xb3e5e0: ldr             x0, [x0, #0xc40]
    // 0xb3e5e4: LeaveFrame
    //     0xb3e5e4: mov             SP, fp
    //     0xb3e5e8: ldp             fp, lr, [SP], #0x10
    // 0xb3e5ec: ret
    //     0xb3e5ec: ret             
    // 0xb3e5f0: r0 = Instance_NPersistentFooterButton
    //     0xb3e5f0: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2af40] Obj!NPersistentFooterButton@e20de1
    //     0xb3e5f4: ldr             x0, [x0, #0xf40]
    // 0xb3e5f8: LeaveFrame
    //     0xb3e5f8: mov             SP, fp
    //     0xb3e5fc: ldp             fp, lr, [SP], #0x10
    // 0xb3e600: ret
    //     0xb3e600: ret             
    // 0xb3e604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e608: b               #0xb3e5bc
  }
  [closure] Widget <anonymous closure>(dynamic, HistoryController) {
    // ** addr: 0xb3e60c, size: 0x78
    // 0xb3e60c: EnterFrame
    //     0xb3e60c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e610: mov             fp, SP
    // 0xb3e614: AllocStack(0x18)
    //     0xb3e614: sub             SP, SP, #0x18
    // 0xb3e618: CheckStackOverflow
    //     0xb3e618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e61c: cmp             SP, x16
    //     0xb3e620: b.ls            #0xb3e67c
    // 0xb3e624: r1 = Function '<anonymous closure>':.
    //     0xb3e624: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af48] AnonymousClosure: (0xb3e6e0), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e628: ldr             x1, [x1, #0xf48]
    // 0xb3e62c: r2 = Null
    //     0xb3e62c: mov             x2, NULL
    // 0xb3e630: r0 = AllocateClosure()
    //     0xb3e630: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e634: r1 = Function '<anonymous closure>':.
    //     0xb3e634: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2af50] AnonymousClosure: (0xb3e684), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e638: ldr             x1, [x1, #0xf50]
    // 0xb3e63c: r2 = Null
    //     0xb3e63c: mov             x2, NULL
    // 0xb3e640: stur            x0, [fp, #-8]
    // 0xb3e644: r0 = AllocateClosure()
    //     0xb3e644: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e648: r16 = Instance_EdgeInsets
    //     0xb3e648: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af58] Obj!EdgeInsets@e125e1
    //     0xb3e64c: ldr             x16, [x16, #0xf58]
    // 0xb3e650: stp             x0, x16, [SP]
    // 0xb3e654: ldr             x1, [fp, #0x10]
    // 0xb3e658: ldur            x2, [fp, #-8]
    // 0xb3e65c: r3 = Instance_Divider
    //     0xb3e65c: add             x3, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb3e660: ldr             x3, [x3, #0xc28]
    // 0xb3e664: r4 = const [0, 0x5, 0x2, 0x3, empty, 0x4, padding, 0x3, null]
    //     0xb3e664: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2af60] List(9) [0, 0x5, 0x2, 0x3, "empty", 0x4, "padding", 0x3, Null]
    //     0xb3e668: ldr             x4, [x4, #0xf60]
    // 0xb3e66c: r0 = paginate()
    //     0xb3e66c: bl              #0xadfa70  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate
    // 0xb3e670: LeaveFrame
    //     0xb3e670: mov             SP, fp
    //     0xb3e674: ldp             fp, lr, [SP], #0x10
    // 0xb3e678: ret
    //     0xb3e678: ret             
    // 0xb3e67c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e67c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e680: b               #0xb3e624
  }
  [closure] NEmptyState <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb3e684, size: 0x5c
    // 0xb3e684: EnterFrame
    //     0xb3e684: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e688: mov             fp, SP
    // 0xb3e68c: AllocStack(0x8)
    //     0xb3e68c: sub             SP, SP, #8
    // 0xb3e690: CheckStackOverflow
    //     0xb3e690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e694: cmp             SP, x16
    //     0xb3e698: b.ls            #0xb3e6d8
    // 0xb3e69c: r0 = NEmptyState()
    //     0xb3e69c: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xb3e6a0: mov             x1, x0
    // 0xb3e6a4: r2 = "Belum berqurban tahun ini\? Yuk, wujudkan aksi nyata untuk membantu sesama."
    //     0xb3e6a4: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af68] "Belum berqurban tahun ini\? Yuk, wujudkan aksi nyata untuk membantu sesama."
    //     0xb3e6a8: ldr             x2, [x2, #0xf68]
    // 0xb3e6ac: r3 = "/assets/images/illustration/no_qurban.svg"
    //     0xb3e6ac: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af70] "/assets/images/illustration/no_qurban.svg"
    //     0xb3e6b0: ldr             x3, [x3, #0xf70]
    // 0xb3e6b4: r5 = "Belum Ada Riwayat Qurban"
    //     0xb3e6b4: add             x5, PP, #0x2a, lsl #12  ; [pp+0x2af78] "Belum Ada Riwayat Qurban"
    //     0xb3e6b8: ldr             x5, [x5, #0xf78]
    // 0xb3e6bc: stur            x0, [fp, #-8]
    // 0xb3e6c0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb3e6c0: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb3e6c4: r0 = NEmptyState.svg()
    //     0xb3e6c4: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xb3e6c8: ldur            x0, [fp, #-8]
    // 0xb3e6cc: LeaveFrame
    //     0xb3e6cc: mov             SP, fp
    //     0xb3e6d0: ldp             fp, lr, [SP], #0x10
    // 0xb3e6d4: ret
    //     0xb3e6d4: ret             
    // 0xb3e6d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e6d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e6dc: b               #0xb3e69c
  }
  [closure] DonationHistoryItem <anonymous closure>(dynamic, BuildContext, Transaction, int) {
    // ** addr: 0xb3e6e0, size: 0x20
    // 0xb3e6e0: EnterFrame
    //     0xb3e6e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e6e4: mov             fp, SP
    // 0xb3e6e8: r0 = DonationHistoryItem()
    //     0xb3e6e8: bl              #0xb3e700  ; AllocateDonationHistoryItemStub -> DonationHistoryItem (size=0x10)
    // 0xb3e6ec: ldr             x1, [fp, #0x18]
    // 0xb3e6f0: StoreField: r0->field_b = r1
    //     0xb3e6f0: stur            w1, [x0, #0xb]
    // 0xb3e6f4: LeaveFrame
    //     0xb3e6f4: mov             SP, fp
    //     0xb3e6f8: ldp             fp, lr, [SP], #0x10
    // 0xb3e6fc: ret
    //     0xb3e6fc: ret             
  }
  [closure] RefreshIndicator <anonymous closure>(dynamic, TransactionHistoryController) {
    // ** addr: 0xb3e70c, size: 0xdc
    // 0xb3e70c: EnterFrame
    //     0xb3e70c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e710: mov             fp, SP
    // 0xb3e714: AllocStack(0x10)
    //     0xb3e714: sub             SP, SP, #0x10
    // 0xb3e718: SetupParameters()
    //     0xb3e718: ldr             x0, [fp, #0x18]
    //     0xb3e71c: ldur            w1, [x0, #0x17]
    //     0xb3e720: add             x1, x1, HEAP, lsl #32
    //     0xb3e724: stur            x1, [fp, #-8]
    // 0xb3e728: r1 = 1
    //     0xb3e728: movz            x1, #0x1
    // 0xb3e72c: r0 = AllocateContext()
    //     0xb3e72c: bl              #0xec126c  ; AllocateContextStub
    // 0xb3e730: mov             x1, x0
    // 0xb3e734: ldur            x0, [fp, #-8]
    // 0xb3e738: stur            x1, [fp, #-0x10]
    // 0xb3e73c: StoreField: r1->field_b = r0
    //     0xb3e73c: stur            w0, [x1, #0xb]
    // 0xb3e740: ldr             x2, [fp, #0x10]
    // 0xb3e744: StoreField: r1->field_f = r2
    //     0xb3e744: stur            w2, [x1, #0xf]
    // 0xb3e748: r0 = Obx()
    //     0xb3e748: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb3e74c: ldur            x2, [fp, #-0x10]
    // 0xb3e750: r1 = Function '<anonymous closure>':.
    //     0xb3e750: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b018] AnonymousClosure: (0xb3e7e8), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e754: ldr             x1, [x1, #0x18]
    // 0xb3e758: stur            x0, [fp, #-8]
    // 0xb3e75c: r0 = AllocateClosure()
    //     0xb3e75c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e760: mov             x1, x0
    // 0xb3e764: ldur            x0, [fp, #-8]
    // 0xb3e768: StoreField: r0->field_b = r1
    //     0xb3e768: stur            w1, [x0, #0xb]
    // 0xb3e76c: r0 = RefreshIndicator()
    //     0xb3e76c: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xb3e770: mov             x3, x0
    // 0xb3e774: ldur            x0, [fp, #-8]
    // 0xb3e778: stur            x3, [fp, #-0x10]
    // 0xb3e77c: StoreField: r3->field_b = r0
    //     0xb3e77c: stur            w0, [x3, #0xb]
    // 0xb3e780: d0 = 40.000000
    //     0xb3e780: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xb3e784: StoreField: r3->field_f = d0
    //     0xb3e784: stur            d0, [x3, #0xf]
    // 0xb3e788: ArrayStore: r3[0] = rZR  ; List_8
    //     0xb3e788: stur            xzr, [x3, #0x17]
    // 0xb3e78c: ldr             x2, [fp, #0x10]
    // 0xb3e790: r1 = Function 'onPageRefresh':.
    //     0xb3e790: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b020] AnonymousClosure: (0xae7024), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageRefresh (0xae6ebc)
    //     0xb3e794: ldr             x1, [x1, #0x20]
    // 0xb3e798: r0 = AllocateClosure()
    //     0xb3e798: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e79c: mov             x1, x0
    // 0xb3e7a0: ldur            x0, [fp, #-0x10]
    // 0xb3e7a4: StoreField: r0->field_1f = r1
    //     0xb3e7a4: stur            w1, [x0, #0x1f]
    // 0xb3e7a8: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xb3e7a8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xb3e7ac: ldr             x1, [x1, #0xf58]
    // 0xb3e7b0: StoreField: r0->field_2f = r1
    //     0xb3e7b0: stur            w1, [x0, #0x2f]
    // 0xb3e7b4: d0 = 2.500000
    //     0xb3e7b4: fmov            d0, #2.50000000
    // 0xb3e7b8: StoreField: r0->field_3b = d0
    //     0xb3e7b8: stur            d0, [x0, #0x3b]
    // 0xb3e7bc: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xb3e7bc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xb3e7c0: ldr             x1, [x1, #0xa68]
    // 0xb3e7c4: StoreField: r0->field_47 = r1
    //     0xb3e7c4: stur            w1, [x0, #0x47]
    // 0xb3e7c8: d0 = 2.000000
    //     0xb3e7c8: fmov            d0, #2.00000000
    // 0xb3e7cc: StoreField: r0->field_4b = d0
    //     0xb3e7cc: stur            d0, [x0, #0x4b]
    // 0xb3e7d0: r1 = Instance__IndicatorType
    //     0xb3e7d0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xb3e7d4: ldr             x1, [x1, #0xa70]
    // 0xb3e7d8: StoreField: r0->field_43 = r1
    //     0xb3e7d8: stur            w1, [x0, #0x43]
    // 0xb3e7dc: LeaveFrame
    //     0xb3e7dc: mov             SP, fp
    //     0xb3e7e0: ldp             fp, lr, [SP], #0x10
    // 0xb3e7e4: ret
    //     0xb3e7e4: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xb3e7e8, size: 0x12c
    // 0xb3e7e8: EnterFrame
    //     0xb3e7e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e7ec: mov             fp, SP
    // 0xb3e7f0: AllocStack(0x30)
    //     0xb3e7f0: sub             SP, SP, #0x30
    // 0xb3e7f4: SetupParameters()
    //     0xb3e7f4: ldr             x0, [fp, #0x10]
    //     0xb3e7f8: ldur            w2, [x0, #0x17]
    //     0xb3e7fc: add             x2, x2, HEAP, lsl #32
    //     0xb3e800: stur            x2, [fp, #-8]
    // 0xb3e804: CheckStackOverflow
    //     0xb3e804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e808: cmp             SP, x16
    //     0xb3e80c: b.ls            #0xb3e90c
    // 0xb3e810: LoadField: r1 = r2->field_f
    //     0xb3e810: ldur            w1, [x2, #0xf]
    // 0xb3e814: DecompressPointer r1
    //     0xb3e814: add             x1, x1, HEAP, lsl #32
    // 0xb3e818: r0 = hasError()
    //     0xb3e818: bl              #0xad1aa0  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError
    // 0xb3e81c: tbnz            w0, #4, #0xb3e85c
    // 0xb3e820: r0 = NEmptyState()
    //     0xb3e820: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xb3e824: mov             x1, x0
    // 0xb3e828: r2 = "Belum berqurban tahun ini\? Yuk, wujudkan aksi nyata untuk membantu sesama."
    //     0xb3e828: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af68] "Belum berqurban tahun ini\? Yuk, wujudkan aksi nyata untuk membantu sesama."
    //     0xb3e82c: ldr             x2, [x2, #0xf68]
    // 0xb3e830: r3 = "/assets/images/illustration/no_qurban.svg"
    //     0xb3e830: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af70] "/assets/images/illustration/no_qurban.svg"
    //     0xb3e834: ldr             x3, [x3, #0xf70]
    // 0xb3e838: r5 = "Belum Ada Riwayat Qurban"
    //     0xb3e838: add             x5, PP, #0x2a, lsl #12  ; [pp+0x2af78] "Belum Ada Riwayat Qurban"
    //     0xb3e83c: ldr             x5, [x5, #0xf78]
    // 0xb3e840: stur            x0, [fp, #-0x10]
    // 0xb3e844: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb3e844: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb3e848: r0 = NEmptyState.svg()
    //     0xb3e848: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xb3e84c: ldur            x0, [fp, #-0x10]
    // 0xb3e850: LeaveFrame
    //     0xb3e850: mov             SP, fp
    //     0xb3e854: ldp             fp, lr, [SP], #0x10
    // 0xb3e858: ret
    //     0xb3e858: ret             
    // 0xb3e85c: ldur            x2, [fp, #-8]
    // 0xb3e860: LoadField: r0 = r2->field_f
    //     0xb3e860: ldur            w0, [x2, #0xf]
    // 0xb3e864: DecompressPointer r0
    //     0xb3e864: add             x0, x0, HEAP, lsl #32
    // 0xb3e868: mov             x1, x0
    // 0xb3e86c: stur            x0, [fp, #-0x10]
    // 0xb3e870: r0 = itemsCount()
    //     0xb3e870: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xb3e874: r1 = Function '<anonymous closure>':.
    //     0xb3e874: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b2a8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb3e878: ldr             x1, [x1, #0x2a8]
    // 0xb3e87c: r2 = Null
    //     0xb3e87c: mov             x2, NULL
    // 0xb3e880: stur            x0, [fp, #-0x18]
    // 0xb3e884: r0 = AllocateClosure()
    //     0xb3e884: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e888: ldur            x2, [fp, #-8]
    // 0xb3e88c: r1 = Function '<anonymous closure>':.
    //     0xb3e88c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b2b0] AnonymousClosure: (0xb3e914), in [package:nuonline/app/modules/qurban/views/qurban_view.dart] QurbanView::build (0xb3deac)
    //     0xb3e890: ldr             x1, [x1, #0x2b0]
    // 0xb3e894: stur            x0, [fp, #-8]
    // 0xb3e898: r0 = AllocateClosure()
    //     0xb3e898: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e89c: stur            x0, [fp, #-0x20]
    // 0xb3e8a0: r0 = ListView()
    //     0xb3e8a0: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb3e8a4: stur            x0, [fp, #-0x28]
    // 0xb3e8a8: r16 = Instance_EdgeInsets
    //     0xb3e8a8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb3e8ac: ldr             x16, [x16, #0x360]
    // 0xb3e8b0: str             x16, [SP]
    // 0xb3e8b4: mov             x1, x0
    // 0xb3e8b8: ldur            x2, [fp, #-0x20]
    // 0xb3e8bc: ldur            x3, [fp, #-0x18]
    // 0xb3e8c0: ldur            x5, [fp, #-8]
    // 0xb3e8c4: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xb3e8c4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xb3e8c8: ldr             x4, [x4, #0x700]
    // 0xb3e8cc: r0 = ListView.separated()
    //     0xb3e8cc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb3e8d0: ldur            x2, [fp, #-0x10]
    // 0xb3e8d4: r1 = Function 'onPageScrolled':.
    //     0xb3e8d4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b2b8] AnonymousClosure: (0xae6984), in [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::onPageScrolled (0xae69c0)
    //     0xb3e8d8: ldr             x1, [x1, #0x2b8]
    // 0xb3e8dc: r0 = AllocateClosure()
    //     0xb3e8dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3e8e0: r1 = <ScrollNotification>
    //     0xb3e8e0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xb3e8e4: ldr             x1, [x1, #0x110]
    // 0xb3e8e8: stur            x0, [fp, #-8]
    // 0xb3e8ec: r0 = NotificationListener()
    //     0xb3e8ec: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xb3e8f0: ldur            x1, [fp, #-8]
    // 0xb3e8f4: StoreField: r0->field_13 = r1
    //     0xb3e8f4: stur            w1, [x0, #0x13]
    // 0xb3e8f8: ldur            x1, [fp, #-0x28]
    // 0xb3e8fc: StoreField: r0->field_b = r1
    //     0xb3e8fc: stur            w1, [x0, #0xb]
    // 0xb3e900: LeaveFrame
    //     0xb3e900: mov             SP, fp
    //     0xb3e904: ldp             fp, lr, [SP], #0x10
    // 0xb3e908: ret
    //     0xb3e908: ret             
    // 0xb3e90c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e90c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e910: b               #0xb3e810
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb3e914, size: 0x84
    // 0xb3e914: EnterFrame
    //     0xb3e914: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e918: mov             fp, SP
    // 0xb3e91c: AllocStack(0x8)
    //     0xb3e91c: sub             SP, SP, #8
    // 0xb3e920: SetupParameters()
    //     0xb3e920: ldr             x0, [fp, #0x20]
    //     0xb3e924: ldur            w1, [x0, #0x17]
    //     0xb3e928: add             x1, x1, HEAP, lsl #32
    // 0xb3e92c: CheckStackOverflow
    //     0xb3e92c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e930: cmp             SP, x16
    //     0xb3e934: b.ls            #0xb3e990
    // 0xb3e938: LoadField: r0 = r1->field_f
    //     0xb3e938: ldur            w0, [x1, #0xf]
    // 0xb3e93c: DecompressPointer r0
    //     0xb3e93c: add             x0, x0, HEAP, lsl #32
    // 0xb3e940: ldr             x1, [fp, #0x10]
    // 0xb3e944: r2 = LoadInt32Instr(r1)
    //     0xb3e944: sbfx            x2, x1, #1, #0x1f
    //     0xb3e948: tbz             w1, #0, #0xb3e950
    //     0xb3e94c: ldur            x2, [x1, #7]
    // 0xb3e950: mov             x1, x0
    // 0xb3e954: r0 = find()
    //     0xb3e954: bl              #0xae6864  ; [package:nuonline/app/modules/donation/controllers/transaction_history_controller.dart] _TransactionHistoryController&GetxController&PagingMixin::find
    // 0xb3e958: stur            x0, [fp, #-8]
    // 0xb3e95c: cmp             w0, NULL
    // 0xb3e960: b.ne            #0xb3e978
    // 0xb3e964: r0 = Instance_NSkeleton
    //     0xb3e964: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b2c0] Obj!NSkeleton@e20941
    //     0xb3e968: ldr             x0, [x0, #0x2c0]
    // 0xb3e96c: LeaveFrame
    //     0xb3e96c: mov             SP, fp
    //     0xb3e970: ldp             fp, lr, [SP], #0x10
    // 0xb3e974: ret
    //     0xb3e974: ret             
    // 0xb3e978: r0 = TransactionHistoryListTile()
    //     0xb3e978: bl              #0xae6858  ; AllocateTransactionHistoryListTileStub -> TransactionHistoryListTile (size=0x10)
    // 0xb3e97c: ldur            x1, [fp, #-8]
    // 0xb3e980: StoreField: r0->field_b = r1
    //     0xb3e980: stur            w1, [x0, #0xb]
    // 0xb3e984: LeaveFrame
    //     0xb3e984: mov             SP, fp
    //     0xb3e988: ldp             fp, lr, [SP], #0x10
    // 0xb3e98c: ret
    //     0xb3e98c: ret             
    // 0xb3e990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e994: b               #0xb3e938
  }
  [closure] QurbanListView <anonymous closure>(dynamic, QurbanListController) {
    // ** addr: 0xb3e998, size: 0xc
    // 0xb3e998: r0 = Instance_QurbanListView
    //     0xb3e998: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b2c8] Obj!QurbanListView@e21121
    //     0xb3e99c: ldr             x0, [x0, #0x2c8]
    // 0xb3e9a0: ret
    //     0xb3e9a0: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3e9a4, size: 0x5c
    // 0xb3e9a4: EnterFrame
    //     0xb3e9a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb3e9a8: mov             fp, SP
    // 0xb3e9ac: AllocStack(0x10)
    //     0xb3e9ac: sub             SP, SP, #0x10
    // 0xb3e9b0: CheckStackOverflow
    //     0xb3e9b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3e9b4: cmp             SP, x16
    //     0xb3e9b8: b.ls            #0xb3e9f8
    // 0xb3e9bc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb3e9bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb3e9c0: ldr             x0, [x0, #0x2670]
    //     0xb3e9c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb3e9c8: cmp             w0, w16
    //     0xb3e9cc: b.ne            #0xb3e9d8
    //     0xb3e9d0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb3e9d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb3e9d8: r16 = "/qurban/qurban-report"
    //     0xb3e9d8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b2d8] "/qurban/qurban-report"
    //     0xb3e9dc: ldr             x16, [x16, #0x2d8]
    // 0xb3e9e0: stp             x16, NULL, [SP]
    // 0xb3e9e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb3e9e4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3e9e8: r0 = GetNavigation.toNamed()
    //     0xb3e9e8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb3e9ec: LeaveFrame
    //     0xb3e9ec: mov             SP, fp
    //     0xb3e9f0: ldp             fp, lr, [SP], #0x10
    // 0xb3e9f4: ret
    //     0xb3e9f4: ret             
    // 0xb3e9f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3e9f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3e9fc: b               #0xb3e9bc
  }
}
