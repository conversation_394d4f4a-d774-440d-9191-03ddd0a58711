// lib: , url: package:nuonline/app/modules/topic/topic_detail/views/topic_detail_view.dart

// class id: 1050582, size: 0x8
class :: {
}

// class id: 5185, size: 0x14, field offset: 0x14
class TopicDetailView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb57e1c, size: 0x4d8
    // 0xb57e1c: EnterFrame
    //     0xb57e1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb57e20: mov             fp, SP
    // 0xb57e24: AllocStack(0x48)
    //     0xb57e24: sub             SP, SP, #0x48
    // 0xb57e28: SetupParameters(TopicDetailView this /* r1 => r1, fp-0x8 */)
    //     0xb57e28: stur            x1, [fp, #-8]
    // 0xb57e2c: CheckStackOverflow
    //     0xb57e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb57e30: cmp             SP, x16
    //     0xb57e34: b.ls            #0xb582ec
    // 0xb57e38: r1 = 1
    //     0xb57e38: movz            x1, #0x1
    // 0xb57e3c: r0 = AllocateContext()
    //     0xb57e3c: bl              #0xec126c  ; AllocateContextStub
    // 0xb57e40: mov             x2, x0
    // 0xb57e44: ldur            x0, [fp, #-8]
    // 0xb57e48: stur            x2, [fp, #-0x10]
    // 0xb57e4c: StoreField: r2->field_f = r0
    //     0xb57e4c: stur            w0, [x2, #0xf]
    // 0xb57e50: mov             x1, x0
    // 0xb57e54: r0 = controller()
    //     0xb57e54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb57e58: mov             x2, x0
    // 0xb57e5c: r1 = Function 'share':.
    //     0xb57e5c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a40] AnonymousClosure: (0xb58784), in [package:nuonline/app/modules/topic/topic_detail/controllers/topic_detail_controller.dart] TopicDetailController::share (0xb587bc)
    //     0xb57e60: ldr             x1, [x1, #0xa40]
    // 0xb57e64: r0 = AllocateClosure()
    //     0xb57e64: bl              #0xec1630  ; AllocateClosureStub
    // 0xb57e68: stur            x0, [fp, #-0x18]
    // 0xb57e6c: r0 = IconButton()
    //     0xb57e6c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb57e70: mov             x3, x0
    // 0xb57e74: ldur            x0, [fp, #-0x18]
    // 0xb57e78: stur            x3, [fp, #-0x20]
    // 0xb57e7c: StoreField: r3->field_3b = r0
    //     0xb57e7c: stur            w0, [x3, #0x3b]
    // 0xb57e80: r0 = false
    //     0xb57e80: add             x0, NULL, #0x30  ; false
    // 0xb57e84: StoreField: r3->field_47 = r0
    //     0xb57e84: stur            w0, [x3, #0x47]
    // 0xb57e88: r1 = Instance_NAdaptiveIcon
    //     0xb57e88: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a48] Obj!NAdaptiveIcon@e20cf1
    //     0xb57e8c: ldr             x1, [x1, #0xa48]
    // 0xb57e90: StoreField: r3->field_1f = r1
    //     0xb57e90: stur            w1, [x3, #0x1f]
    // 0xb57e94: r1 = Instance__IconButtonVariant
    //     0xb57e94: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb57e98: ldr             x1, [x1, #0xf78]
    // 0xb57e9c: StoreField: r3->field_63 = r1
    //     0xb57e9c: stur            w1, [x3, #0x63]
    // 0xb57ea0: r1 = Null
    //     0xb57ea0: mov             x1, NULL
    // 0xb57ea4: r2 = 2
    //     0xb57ea4: movz            x2, #0x2
    // 0xb57ea8: r0 = AllocateArray()
    //     0xb57ea8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb57eac: mov             x2, x0
    // 0xb57eb0: ldur            x0, [fp, #-0x20]
    // 0xb57eb4: stur            x2, [fp, #-0x18]
    // 0xb57eb8: StoreField: r2->field_f = r0
    //     0xb57eb8: stur            w0, [x2, #0xf]
    // 0xb57ebc: r1 = <Widget>
    //     0xb57ebc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb57ec0: r0 = AllocateGrowableArray()
    //     0xb57ec0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb57ec4: mov             x1, x0
    // 0xb57ec8: ldur            x0, [fp, #-0x18]
    // 0xb57ecc: stur            x1, [fp, #-0x20]
    // 0xb57ed0: StoreField: r1->field_f = r0
    //     0xb57ed0: stur            w0, [x1, #0xf]
    // 0xb57ed4: r2 = 2
    //     0xb57ed4: movz            x2, #0x2
    // 0xb57ed8: StoreField: r1->field_b = r2
    //     0xb57ed8: stur            w2, [x1, #0xb]
    // 0xb57edc: r0 = AppBar()
    //     0xb57edc: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb57ee0: stur            x0, [fp, #-0x18]
    // 0xb57ee4: r16 = Instance_Text
    //     0xb57ee4: add             x16, PP, #0x29, lsl #12  ; [pp+0x29a50] Obj!Text@e23171
    //     0xb57ee8: ldr             x16, [x16, #0xa50]
    // 0xb57eec: ldur            lr, [fp, #-0x20]
    // 0xb57ef0: stp             lr, x16, [SP]
    // 0xb57ef4: mov             x1, x0
    // 0xb57ef8: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xb57ef8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xb57efc: ldr             x4, [x4, #0xf88]
    // 0xb57f00: r0 = AppBar()
    //     0xb57f00: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb57f04: ldur            x1, [fp, #-8]
    // 0xb57f08: r0 = controller()
    //     0xb57f08: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb57f0c: ldur            x1, [fp, #-8]
    // 0xb57f10: stur            x0, [fp, #-0x20]
    // 0xb57f14: r0 = controller()
    //     0xb57f14: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb57f18: LoadField: r1 = r0->field_43
    //     0xb57f18: ldur            w1, [x0, #0x43]
    // 0xb57f1c: DecompressPointer r1
    //     0xb57f1c: add             x1, x1, HEAP, lsl #32
    // 0xb57f20: stur            x1, [fp, #-0x28]
    // 0xb57f24: r0 = NArticleHeader()
    //     0xb57f24: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xb57f28: mov             x1, x0
    // 0xb57f2c: ldur            x0, [fp, #-0x28]
    // 0xb57f30: stur            x1, [fp, #-0x30]
    // 0xb57f34: StoreField: r1->field_b = r0
    //     0xb57f34: stur            w0, [x1, #0xb]
    // 0xb57f38: r0 = false
    //     0xb57f38: add             x0, NULL, #0x30  ; false
    // 0xb57f3c: StoreField: r1->field_13 = r0
    //     0xb57f3c: stur            w0, [x1, #0x13]
    // 0xb57f40: r0 = Padding()
    //     0xb57f40: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb57f44: mov             x1, x0
    // 0xb57f48: r0 = Instance_EdgeInsets
    //     0xb57f48: add             x0, PP, #0x29, lsl #12  ; [pp+0x296f0] Obj!EdgeInsets@e12791
    //     0xb57f4c: ldr             x0, [x0, #0x6f0]
    // 0xb57f50: stur            x1, [fp, #-0x28]
    // 0xb57f54: StoreField: r1->field_f = r0
    //     0xb57f54: stur            w0, [x1, #0xf]
    // 0xb57f58: ldur            x0, [fp, #-0x30]
    // 0xb57f5c: StoreField: r1->field_b = r0
    //     0xb57f5c: stur            w0, [x1, #0xb]
    // 0xb57f60: r0 = Obx()
    //     0xb57f60: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb57f64: ldur            x2, [fp, #-0x10]
    // 0xb57f68: r1 = Function '<anonymous closure>':.
    //     0xb57f68: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a58] AnonymousClosure: (0xb58338), in [package:nuonline/app/modules/topic/topic_detail/views/topic_detail_view.dart] TopicDetailView::build (0xb57e1c)
    //     0xb57f6c: ldr             x1, [x1, #0xa58]
    // 0xb57f70: stur            x0, [fp, #-0x10]
    // 0xb57f74: r0 = AllocateClosure()
    //     0xb57f74: bl              #0xec1630  ; AllocateClosureStub
    // 0xb57f78: mov             x1, x0
    // 0xb57f7c: ldur            x0, [fp, #-0x10]
    // 0xb57f80: StoreField: r0->field_b = r1
    //     0xb57f80: stur            w1, [x0, #0xb]
    // 0xb57f84: r1 = <FlexParentData>
    //     0xb57f84: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb57f88: ldr             x1, [x1, #0x720]
    // 0xb57f8c: r0 = Expanded()
    //     0xb57f8c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb57f90: mov             x3, x0
    // 0xb57f94: r0 = 1
    //     0xb57f94: movz            x0, #0x1
    // 0xb57f98: stur            x3, [fp, #-0x30]
    // 0xb57f9c: StoreField: r3->field_13 = r0
    //     0xb57f9c: stur            x0, [x3, #0x13]
    // 0xb57fa0: r4 = Instance_FlexFit
    //     0xb57fa0: add             x4, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb57fa4: ldr             x4, [x4, #0x728]
    // 0xb57fa8: StoreField: r3->field_1b = r4
    //     0xb57fa8: stur            w4, [x3, #0x1b]
    // 0xb57fac: ldur            x1, [fp, #-0x10]
    // 0xb57fb0: StoreField: r3->field_b = r1
    //     0xb57fb0: stur            w1, [x3, #0xb]
    // 0xb57fb4: r1 = Null
    //     0xb57fb4: mov             x1, NULL
    // 0xb57fb8: r2 = 6
    //     0xb57fb8: movz            x2, #0x6
    // 0xb57fbc: r0 = AllocateArray()
    //     0xb57fbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb57fc0: mov             x2, x0
    // 0xb57fc4: ldur            x0, [fp, #-0x28]
    // 0xb57fc8: stur            x2, [fp, #-0x10]
    // 0xb57fcc: StoreField: r2->field_f = r0
    //     0xb57fcc: stur            w0, [x2, #0xf]
    // 0xb57fd0: r16 = Instance_Divider
    //     0xb57fd0: add             x16, PP, #0x29, lsl #12  ; [pp+0x29190] Obj!Divider@e25781
    //     0xb57fd4: ldr             x16, [x16, #0x190]
    // 0xb57fd8: StoreField: r2->field_13 = r16
    //     0xb57fd8: stur            w16, [x2, #0x13]
    // 0xb57fdc: ldur            x0, [fp, #-0x30]
    // 0xb57fe0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb57fe0: stur            w0, [x2, #0x17]
    // 0xb57fe4: r1 = <Widget>
    //     0xb57fe4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb57fe8: r0 = AllocateGrowableArray()
    //     0xb57fe8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb57fec: mov             x1, x0
    // 0xb57ff0: ldur            x0, [fp, #-0x10]
    // 0xb57ff4: stur            x1, [fp, #-0x28]
    // 0xb57ff8: StoreField: r1->field_f = r0
    //     0xb57ff8: stur            w0, [x1, #0xf]
    // 0xb57ffc: r0 = 6
    //     0xb57ffc: movz            x0, #0x6
    // 0xb58000: StoreField: r1->field_b = r0
    //     0xb58000: stur            w0, [x1, #0xb]
    // 0xb58004: r0 = Column()
    //     0xb58004: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb58008: mov             x1, x0
    // 0xb5800c: r0 = Instance_Axis
    //     0xb5800c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb58010: stur            x1, [fp, #-0x10]
    // 0xb58014: StoreField: r1->field_f = r0
    //     0xb58014: stur            w0, [x1, #0xf]
    // 0xb58018: r2 = Instance_MainAxisAlignment
    //     0xb58018: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb5801c: ldr             x2, [x2, #0x730]
    // 0xb58020: StoreField: r1->field_13 = r2
    //     0xb58020: stur            w2, [x1, #0x13]
    // 0xb58024: r3 = Instance_MainAxisSize
    //     0xb58024: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb58028: ldr             x3, [x3, #0x738]
    // 0xb5802c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb5802c: stur            w3, [x1, #0x17]
    // 0xb58030: r4 = Instance_CrossAxisAlignment
    //     0xb58030: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb58034: ldr             x4, [x4, #0x740]
    // 0xb58038: StoreField: r1->field_1b = r4
    //     0xb58038: stur            w4, [x1, #0x1b]
    // 0xb5803c: r5 = Instance_VerticalDirection
    //     0xb5803c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb58040: ldr             x5, [x5, #0x748]
    // 0xb58044: StoreField: r1->field_23 = r5
    //     0xb58044: stur            w5, [x1, #0x23]
    // 0xb58048: r6 = Instance_Clip
    //     0xb58048: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb5804c: ldr             x6, [x6, #0x750]
    // 0xb58050: StoreField: r1->field_2b = r6
    //     0xb58050: stur            w6, [x1, #0x2b]
    // 0xb58054: StoreField: r1->field_2f = rZR
    //     0xb58054: stur            xzr, [x1, #0x2f]
    // 0xb58058: ldur            x7, [fp, #-0x28]
    // 0xb5805c: StoreField: r1->field_b = r7
    //     0xb5805c: stur            w7, [x1, #0xb]
    // 0xb58060: r0 = RefreshIndicator()
    //     0xb58060: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xb58064: mov             x3, x0
    // 0xb58068: ldur            x0, [fp, #-0x10]
    // 0xb5806c: stur            x3, [fp, #-0x28]
    // 0xb58070: StoreField: r3->field_b = r0
    //     0xb58070: stur            w0, [x3, #0xb]
    // 0xb58074: d0 = 40.000000
    //     0xb58074: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xb58078: StoreField: r3->field_f = d0
    //     0xb58078: stur            d0, [x3, #0xf]
    // 0xb5807c: ArrayStore: r3[0] = rZR  ; List_8
    //     0xb5807c: stur            xzr, [x3, #0x17]
    // 0xb58080: ldur            x2, [fp, #-0x20]
    // 0xb58084: r1 = Function 'onPageRefresh':.
    //     0xb58084: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xb58088: ldr             x1, [x1, #0xa60]
    // 0xb5808c: r0 = AllocateClosure()
    //     0xb5808c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb58090: mov             x1, x0
    // 0xb58094: ldur            x0, [fp, #-0x28]
    // 0xb58098: StoreField: r0->field_1f = r1
    //     0xb58098: stur            w1, [x0, #0x1f]
    // 0xb5809c: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xb5809c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xb580a0: ldr             x1, [x1, #0xf58]
    // 0xb580a4: StoreField: r0->field_2f = r1
    //     0xb580a4: stur            w1, [x0, #0x2f]
    // 0xb580a8: d0 = 2.500000
    //     0xb580a8: fmov            d0, #2.50000000
    // 0xb580ac: StoreField: r0->field_3b = d0
    //     0xb580ac: stur            d0, [x0, #0x3b]
    // 0xb580b0: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xb580b0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xb580b4: ldr             x1, [x1, #0xa68]
    // 0xb580b8: StoreField: r0->field_47 = r1
    //     0xb580b8: stur            w1, [x0, #0x47]
    // 0xb580bc: d0 = 2.000000
    //     0xb580bc: fmov            d0, #2.00000000
    // 0xb580c0: StoreField: r0->field_4b = d0
    //     0xb580c0: stur            d0, [x0, #0x4b]
    // 0xb580c4: r1 = Instance__IndicatorType
    //     0xb580c4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xb580c8: ldr             x1, [x1, #0xa70]
    // 0xb580cc: StoreField: r0->field_43 = r1
    //     0xb580cc: stur            w1, [x0, #0x43]
    // 0xb580d0: r1 = <FlexParentData>
    //     0xb580d0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb580d4: ldr             x1, [x1, #0x720]
    // 0xb580d8: r0 = Expanded()
    //     0xb580d8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb580dc: mov             x3, x0
    // 0xb580e0: r0 = 1
    //     0xb580e0: movz            x0, #0x1
    // 0xb580e4: stur            x3, [fp, #-0x10]
    // 0xb580e8: StoreField: r3->field_13 = r0
    //     0xb580e8: stur            x0, [x3, #0x13]
    // 0xb580ec: r0 = Instance_FlexFit
    //     0xb580ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb580f0: ldr             x0, [x0, #0x728]
    // 0xb580f4: StoreField: r3->field_1b = r0
    //     0xb580f4: stur            w0, [x3, #0x1b]
    // 0xb580f8: ldur            x0, [fp, #-0x28]
    // 0xb580fc: StoreField: r3->field_b = r0
    //     0xb580fc: stur            w0, [x3, #0xb]
    // 0xb58100: r1 = Null
    //     0xb58100: mov             x1, NULL
    // 0xb58104: r2 = 2
    //     0xb58104: movz            x2, #0x2
    // 0xb58108: r0 = AllocateArray()
    //     0xb58108: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb5810c: mov             x2, x0
    // 0xb58110: ldur            x0, [fp, #-0x10]
    // 0xb58114: stur            x2, [fp, #-0x20]
    // 0xb58118: StoreField: r2->field_f = r0
    //     0xb58118: stur            w0, [x2, #0xf]
    // 0xb5811c: r1 = <Widget>
    //     0xb5811c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb58120: r0 = AllocateGrowableArray()
    //     0xb58120: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb58124: mov             x2, x0
    // 0xb58128: ldur            x0, [fp, #-0x20]
    // 0xb5812c: stur            x2, [fp, #-0x10]
    // 0xb58130: StoreField: r2->field_f = r0
    //     0xb58130: stur            w0, [x2, #0xf]
    // 0xb58134: r0 = 2
    //     0xb58134: movz            x0, #0x2
    // 0xb58138: StoreField: r2->field_b = r0
    //     0xb58138: stur            w0, [x2, #0xb]
    // 0xb5813c: ldur            x1, [fp, #-8]
    // 0xb58140: r0 = controller()
    //     0xb58140: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb58144: LoadField: r1 = r0->field_37
    //     0xb58144: ldur            w1, [x0, #0x37]
    // 0xb58148: DecompressPointer r1
    //     0xb58148: add             x1, x1, HEAP, lsl #32
    // 0xb5814c: r0 = _adsVisibility()
    //     0xb5814c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xb58150: mov             x2, x0
    // 0xb58154: r1 = Null
    //     0xb58154: mov             x1, NULL
    // 0xb58158: r0 = AdsConfig.fromJson()
    //     0xb58158: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xb5815c: LoadField: r1 = r0->field_23
    //     0xb5815c: ldur            w1, [x0, #0x23]
    // 0xb58160: DecompressPointer r1
    //     0xb58160: add             x1, x1, HEAP, lsl #32
    // 0xb58164: LoadField: r0 = r1->field_7
    //     0xb58164: ldur            w0, [x1, #7]
    // 0xb58168: DecompressPointer r0
    //     0xb58168: add             x0, x0, HEAP, lsl #32
    // 0xb5816c: tbnz            w0, #4, #0xb58238
    // 0xb58170: ldur            x0, [fp, #-0x10]
    // 0xb58174: ldur            x1, [fp, #-8]
    // 0xb58178: r0 = controller()
    //     0xb58178: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb5817c: LoadField: r1 = r0->field_37
    //     0xb5817c: ldur            w1, [x0, #0x37]
    // 0xb58180: DecompressPointer r1
    //     0xb58180: add             x1, x1, HEAP, lsl #32
    // 0xb58184: r0 = _adsVisibility()
    //     0xb58184: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xb58188: mov             x2, x0
    // 0xb5818c: r1 = Null
    //     0xb5818c: mov             x1, NULL
    // 0xb58190: r0 = AdsConfig.fromJson()
    //     0xb58190: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xb58194: LoadField: r1 = r0->field_23
    //     0xb58194: ldur            w1, [x0, #0x23]
    // 0xb58198: DecompressPointer r1
    //     0xb58198: add             x1, x1, HEAP, lsl #32
    // 0xb5819c: LoadField: r0 = r1->field_f
    //     0xb5819c: ldur            w0, [x1, #0xf]
    // 0xb581a0: DecompressPointer r0
    //     0xb581a0: add             x0, x0, HEAP, lsl #32
    // 0xb581a4: stur            x0, [fp, #-8]
    // 0xb581a8: r0 = AdmobBannerWidget()
    //     0xb581a8: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xb581ac: mov             x2, x0
    // 0xb581b0: ldur            x0, [fp, #-8]
    // 0xb581b4: stur            x2, [fp, #-0x20]
    // 0xb581b8: StoreField: r2->field_b = r0
    //     0xb581b8: stur            w0, [x2, #0xb]
    // 0xb581bc: ldur            x0, [fp, #-0x10]
    // 0xb581c0: LoadField: r1 = r0->field_b
    //     0xb581c0: ldur            w1, [x0, #0xb]
    // 0xb581c4: LoadField: r3 = r0->field_f
    //     0xb581c4: ldur            w3, [x0, #0xf]
    // 0xb581c8: DecompressPointer r3
    //     0xb581c8: add             x3, x3, HEAP, lsl #32
    // 0xb581cc: LoadField: r4 = r3->field_b
    //     0xb581cc: ldur            w4, [x3, #0xb]
    // 0xb581d0: r3 = LoadInt32Instr(r1)
    //     0xb581d0: sbfx            x3, x1, #1, #0x1f
    // 0xb581d4: stur            x3, [fp, #-0x38]
    // 0xb581d8: r1 = LoadInt32Instr(r4)
    //     0xb581d8: sbfx            x1, x4, #1, #0x1f
    // 0xb581dc: cmp             x3, x1
    // 0xb581e0: b.ne            #0xb581ec
    // 0xb581e4: mov             x1, x0
    // 0xb581e8: r0 = _growToNextCapacity()
    //     0xb581e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb581ec: ldur            x2, [fp, #-0x10]
    // 0xb581f0: ldur            x3, [fp, #-0x38]
    // 0xb581f4: add             x0, x3, #1
    // 0xb581f8: lsl             x1, x0, #1
    // 0xb581fc: StoreField: r2->field_b = r1
    //     0xb581fc: stur            w1, [x2, #0xb]
    // 0xb58200: LoadField: r1 = r2->field_f
    //     0xb58200: ldur            w1, [x2, #0xf]
    // 0xb58204: DecompressPointer r1
    //     0xb58204: add             x1, x1, HEAP, lsl #32
    // 0xb58208: ldur            x0, [fp, #-0x20]
    // 0xb5820c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5820c: add             x25, x1, x3, lsl #2
    //     0xb58210: add             x25, x25, #0xf
    //     0xb58214: str             w0, [x25]
    //     0xb58218: tbz             w0, #0, #0xb58234
    //     0xb5821c: ldurb           w16, [x1, #-1]
    //     0xb58220: ldurb           w17, [x0, #-1]
    //     0xb58224: and             x16, x17, x16, lsr #2
    //     0xb58228: tst             x16, HEAP, lsr #32
    //     0xb5822c: b.eq            #0xb58234
    //     0xb58230: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb58234: b               #0xb5823c
    // 0xb58238: ldur            x2, [fp, #-0x10]
    // 0xb5823c: ldur            x0, [fp, #-0x18]
    // 0xb58240: r0 = Column()
    //     0xb58240: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb58244: mov             x1, x0
    // 0xb58248: r0 = Instance_Axis
    //     0xb58248: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb5824c: stur            x1, [fp, #-8]
    // 0xb58250: StoreField: r1->field_f = r0
    //     0xb58250: stur            w0, [x1, #0xf]
    // 0xb58254: r0 = Instance_MainAxisAlignment
    //     0xb58254: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb58258: ldr             x0, [x0, #0x730]
    // 0xb5825c: StoreField: r1->field_13 = r0
    //     0xb5825c: stur            w0, [x1, #0x13]
    // 0xb58260: r0 = Instance_MainAxisSize
    //     0xb58260: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb58264: ldr             x0, [x0, #0x738]
    // 0xb58268: ArrayStore: r1[0] = r0  ; List_4
    //     0xb58268: stur            w0, [x1, #0x17]
    // 0xb5826c: r0 = Instance_CrossAxisAlignment
    //     0xb5826c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb58270: ldr             x0, [x0, #0x740]
    // 0xb58274: StoreField: r1->field_1b = r0
    //     0xb58274: stur            w0, [x1, #0x1b]
    // 0xb58278: r0 = Instance_VerticalDirection
    //     0xb58278: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb5827c: ldr             x0, [x0, #0x748]
    // 0xb58280: StoreField: r1->field_23 = r0
    //     0xb58280: stur            w0, [x1, #0x23]
    // 0xb58284: r0 = Instance_Clip
    //     0xb58284: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb58288: ldr             x0, [x0, #0x750]
    // 0xb5828c: StoreField: r1->field_2b = r0
    //     0xb5828c: stur            w0, [x1, #0x2b]
    // 0xb58290: StoreField: r1->field_2f = rZR
    //     0xb58290: stur            xzr, [x1, #0x2f]
    // 0xb58294: ldur            x0, [fp, #-0x10]
    // 0xb58298: StoreField: r1->field_b = r0
    //     0xb58298: stur            w0, [x1, #0xb]
    // 0xb5829c: r0 = Scaffold()
    //     0xb5829c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb582a0: ldur            x1, [fp, #-0x18]
    // 0xb582a4: StoreField: r0->field_13 = r1
    //     0xb582a4: stur            w1, [x0, #0x13]
    // 0xb582a8: ldur            x1, [fp, #-8]
    // 0xb582ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xb582ac: stur            w1, [x0, #0x17]
    // 0xb582b0: r1 = Instance_AlignmentDirectional
    //     0xb582b0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb582b4: ldr             x1, [x1, #0x758]
    // 0xb582b8: StoreField: r0->field_2b = r1
    //     0xb582b8: stur            w1, [x0, #0x2b]
    // 0xb582bc: r1 = true
    //     0xb582bc: add             x1, NULL, #0x20  ; true
    // 0xb582c0: StoreField: r0->field_53 = r1
    //     0xb582c0: stur            w1, [x0, #0x53]
    // 0xb582c4: r2 = Instance_DragStartBehavior
    //     0xb582c4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb582c8: StoreField: r0->field_57 = r2
    //     0xb582c8: stur            w2, [x0, #0x57]
    // 0xb582cc: r2 = false
    //     0xb582cc: add             x2, NULL, #0x30  ; false
    // 0xb582d0: StoreField: r0->field_b = r2
    //     0xb582d0: stur            w2, [x0, #0xb]
    // 0xb582d4: StoreField: r0->field_f = r2
    //     0xb582d4: stur            w2, [x0, #0xf]
    // 0xb582d8: StoreField: r0->field_5f = r1
    //     0xb582d8: stur            w1, [x0, #0x5f]
    // 0xb582dc: StoreField: r0->field_63 = r1
    //     0xb582dc: stur            w1, [x0, #0x63]
    // 0xb582e0: LeaveFrame
    //     0xb582e0: mov             SP, fp
    //     0xb582e4: ldp             fp, lr, [SP], #0x10
    // 0xb582e8: ret
    //     0xb582e8: ret             
    // 0xb582ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb582ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb582f0: b               #0xb57e38
  }
  [closure] NotificationListener<ScrollNotification> <anonymous closure>(dynamic) {
    // ** addr: 0xb58338, size: 0xf4
    // 0xb58338: EnterFrame
    //     0xb58338: stp             fp, lr, [SP, #-0x10]!
    //     0xb5833c: mov             fp, SP
    // 0xb58340: AllocStack(0x38)
    //     0xb58340: sub             SP, SP, #0x38
    // 0xb58344: SetupParameters()
    //     0xb58344: ldr             x0, [fp, #0x10]
    //     0xb58348: ldur            w2, [x0, #0x17]
    //     0xb5834c: add             x2, x2, HEAP, lsl #32
    //     0xb58350: stur            x2, [fp, #-8]
    // 0xb58354: CheckStackOverflow
    //     0xb58354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58358: cmp             SP, x16
    //     0xb5835c: b.ls            #0xb58424
    // 0xb58360: LoadField: r1 = r2->field_f
    //     0xb58360: ldur            w1, [x2, #0xf]
    // 0xb58364: DecompressPointer r1
    //     0xb58364: add             x1, x1, HEAP, lsl #32
    // 0xb58368: r0 = controller()
    //     0xb58368: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb5836c: ldur            x2, [fp, #-8]
    // 0xb58370: stur            x0, [fp, #-0x10]
    // 0xb58374: LoadField: r1 = r2->field_f
    //     0xb58374: ldur            w1, [x2, #0xf]
    // 0xb58378: DecompressPointer r1
    //     0xb58378: add             x1, x1, HEAP, lsl #32
    // 0xb5837c: r0 = controller()
    //     0xb5837c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb58380: mov             x1, x0
    // 0xb58384: r0 = itemsCount()
    //     0xb58384: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xb58388: r1 = Function '<anonymous closure>':.
    //     0xb58388: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a78] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb5838c: ldr             x1, [x1, #0xa78]
    // 0xb58390: r2 = Null
    //     0xb58390: mov             x2, NULL
    // 0xb58394: stur            x0, [fp, #-0x18]
    // 0xb58398: r0 = AllocateClosure()
    //     0xb58398: bl              #0xec1630  ; AllocateClosureStub
    // 0xb5839c: ldur            x2, [fp, #-8]
    // 0xb583a0: r1 = Function '<anonymous closure>':.
    //     0xb583a0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a80] AnonymousClosure: (0xb5842c), in [package:nuonline/app/modules/topic/topic_detail/views/topic_detail_view.dart] TopicDetailView::build (0xb57e1c)
    //     0xb583a4: ldr             x1, [x1, #0xa80]
    // 0xb583a8: stur            x0, [fp, #-8]
    // 0xb583ac: r0 = AllocateClosure()
    //     0xb583ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xb583b0: stur            x0, [fp, #-0x20]
    // 0xb583b4: r0 = ListView()
    //     0xb583b4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb583b8: stur            x0, [fp, #-0x28]
    // 0xb583bc: r16 = true
    //     0xb583bc: add             x16, NULL, #0x20  ; true
    // 0xb583c0: r30 = Instance_EdgeInsets
    //     0xb583c0: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb583c4: ldr             lr, [lr, #0x360]
    // 0xb583c8: stp             lr, x16, [SP]
    // 0xb583cc: mov             x1, x0
    // 0xb583d0: ldur            x2, [fp, #-0x20]
    // 0xb583d4: ldur            x3, [fp, #-0x18]
    // 0xb583d8: ldur            x5, [fp, #-8]
    // 0xb583dc: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xb583dc: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb583e0: ldr             x4, [x4, #0x100]
    // 0xb583e4: r0 = ListView.separated()
    //     0xb583e4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb583e8: ldur            x2, [fp, #-0x10]
    // 0xb583ec: r1 = Function 'onPageScrolled':.
    //     0xb583ec: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xb583f0: ldr             x1, [x1, #0xa88]
    // 0xb583f4: r0 = AllocateClosure()
    //     0xb583f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb583f8: r1 = <ScrollNotification>
    //     0xb583f8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xb583fc: ldr             x1, [x1, #0x110]
    // 0xb58400: stur            x0, [fp, #-8]
    // 0xb58404: r0 = NotificationListener()
    //     0xb58404: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xb58408: ldur            x1, [fp, #-8]
    // 0xb5840c: StoreField: r0->field_13 = r1
    //     0xb5840c: stur            w1, [x0, #0x13]
    // 0xb58410: ldur            x1, [fp, #-0x28]
    // 0xb58414: StoreField: r0->field_b = r1
    //     0xb58414: stur            w1, [x0, #0xb]
    // 0xb58418: LeaveFrame
    //     0xb58418: mov             SP, fp
    //     0xb5841c: ldp             fp, lr, [SP], #0x10
    // 0xb58420: ret
    //     0xb58420: ret             
    // 0xb58424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58428: b               #0xb58360
  }
  [closure] NArticleListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb5842c, size: 0x194
    // 0xb5842c: EnterFrame
    //     0xb5842c: stp             fp, lr, [SP, #-0x10]!
    //     0xb58430: mov             fp, SP
    // 0xb58434: AllocStack(0x38)
    //     0xb58434: sub             SP, SP, #0x38
    // 0xb58438: SetupParameters()
    //     0xb58438: ldr             x0, [fp, #0x20]
    //     0xb5843c: ldur            w1, [x0, #0x17]
    //     0xb58440: add             x1, x1, HEAP, lsl #32
    //     0xb58444: stur            x1, [fp, #-8]
    // 0xb58448: CheckStackOverflow
    //     0xb58448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5844c: cmp             SP, x16
    //     0xb58450: b.ls            #0xb585b8
    // 0xb58454: r1 = 1
    //     0xb58454: movz            x1, #0x1
    // 0xb58458: r0 = AllocateContext()
    //     0xb58458: bl              #0xec126c  ; AllocateContextStub
    // 0xb5845c: mov             x2, x0
    // 0xb58460: ldur            x0, [fp, #-8]
    // 0xb58464: stur            x2, [fp, #-0x10]
    // 0xb58468: StoreField: r2->field_b = r0
    //     0xb58468: stur            w0, [x2, #0xb]
    // 0xb5846c: LoadField: r1 = r0->field_f
    //     0xb5846c: ldur            w1, [x0, #0xf]
    // 0xb58470: DecompressPointer r1
    //     0xb58470: add             x1, x1, HEAP, lsl #32
    // 0xb58474: r0 = controller()
    //     0xb58474: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb58478: mov             x1, x0
    // 0xb5847c: ldr             x0, [fp, #0x10]
    // 0xb58480: r2 = LoadInt32Instr(r0)
    //     0xb58480: sbfx            x2, x0, #1, #0x1f
    //     0xb58484: tbz             w0, #0, #0xb5848c
    //     0xb58488: ldur            x2, [x0, #7]
    // 0xb5848c: r0 = find()
    //     0xb5848c: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xb58490: mov             x3, x0
    // 0xb58494: ldur            x2, [fp, #-0x10]
    // 0xb58498: stur            x3, [fp, #-0x18]
    // 0xb5849c: StoreField: r2->field_f = r0
    //     0xb5849c: stur            w0, [x2, #0xf]
    //     0xb584a0: ldurb           w16, [x2, #-1]
    //     0xb584a4: ldurb           w17, [x0, #-1]
    //     0xb584a8: and             x16, x17, x16, lsr #2
    //     0xb584ac: tst             x16, HEAP, lsr #32
    //     0xb584b0: b.eq            #0xb584b8
    //     0xb584b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb584b8: cmp             w3, NULL
    // 0xb584bc: b.ne            #0xb584d4
    // 0xb584c0: r0 = Instance_NArticleListTile
    //     0xb584c0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xb584c4: ldr             x0, [x0, #0xa90]
    // 0xb584c8: LeaveFrame
    //     0xb584c8: mov             SP, fp
    //     0xb584cc: ldp             fp, lr, [SP], #0x10
    // 0xb584d0: ret
    //     0xb584d0: ret             
    // 0xb584d4: LoadField: r4 = r3->field_7
    //     0xb584d4: ldur            x4, [x3, #7]
    // 0xb584d8: r0 = BoxInt64Instr(r4)
    //     0xb584d8: sbfiz           x0, x4, #1, #0x1f
    //     0xb584dc: cmp             x4, x0, asr #1
    //     0xb584e0: b.eq            #0xb584ec
    //     0xb584e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb584e8: stur            x4, [x0, #7]
    // 0xb584ec: r1 = <int>
    //     0xb584ec: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb584f0: stur            x0, [fp, #-8]
    // 0xb584f4: r0 = ValueKey()
    //     0xb584f4: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xb584f8: mov             x2, x0
    // 0xb584fc: ldur            x0, [fp, #-8]
    // 0xb58500: stur            x2, [fp, #-0x28]
    // 0xb58504: StoreField: r2->field_b = r0
    //     0xb58504: stur            w0, [x2, #0xb]
    // 0xb58508: ldur            x0, [fp, #-0x18]
    // 0xb5850c: LoadField: r1 = r0->field_1b
    //     0xb5850c: ldur            w1, [x0, #0x1b]
    // 0xb58510: DecompressPointer r1
    //     0xb58510: add             x1, x1, HEAP, lsl #32
    // 0xb58514: LoadField: r3 = r1->field_1b
    //     0xb58514: ldur            w3, [x1, #0x1b]
    // 0xb58518: DecompressPointer r3
    //     0xb58518: add             x3, x3, HEAP, lsl #32
    // 0xb5851c: stur            x3, [fp, #-0x20]
    // 0xb58520: LoadField: r4 = r0->field_f
    //     0xb58520: ldur            w4, [x0, #0xf]
    // 0xb58524: DecompressPointer r4
    //     0xb58524: add             x4, x4, HEAP, lsl #32
    // 0xb58528: mov             x1, x0
    // 0xb5852c: stur            x4, [fp, #-8]
    // 0xb58530: r0 = publishedAt()
    //     0xb58530: bl              #0xb585c0  ; [package:nuonline/app/data/models/article.dart] Article::publishedAt
    // 0xb58534: mov             x1, x0
    // 0xb58538: ldur            x0, [fp, #-0x18]
    // 0xb5853c: stur            x1, [fp, #-0x30]
    // 0xb58540: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb58540: ldur            w2, [x0, #0x17]
    // 0xb58544: DecompressPointer r2
    //     0xb58544: add             x2, x2, HEAP, lsl #32
    // 0xb58548: LoadField: r0 = r2->field_7
    //     0xb58548: ldur            w0, [x2, #7]
    // 0xb5854c: DecompressPointer r0
    //     0xb5854c: add             x0, x0, HEAP, lsl #32
    // 0xb58550: stur            x0, [fp, #-0x18]
    // 0xb58554: r0 = NArticleListTile()
    //     0xb58554: bl              #0xb07fe4  ; AllocateNArticleListTileStub -> NArticleListTile (size=0x28)
    // 0xb58558: mov             x3, x0
    // 0xb5855c: ldur            x0, [fp, #-8]
    // 0xb58560: stur            x3, [fp, #-0x38]
    // 0xb58564: StoreField: r3->field_b = r0
    //     0xb58564: stur            w0, [x3, #0xb]
    // 0xb58568: ldur            x0, [fp, #-0x20]
    // 0xb5856c: StoreField: r3->field_f = r0
    //     0xb5856c: stur            w0, [x3, #0xf]
    // 0xb58570: ldur            x0, [fp, #-0x18]
    // 0xb58574: StoreField: r3->field_13 = r0
    //     0xb58574: stur            w0, [x3, #0x13]
    // 0xb58578: ldur            x0, [fp, #-0x30]
    // 0xb5857c: StoreField: r3->field_1b = r0
    //     0xb5857c: stur            w0, [x3, #0x1b]
    // 0xb58580: ldur            x2, [fp, #-0x10]
    // 0xb58584: r1 = Function '<anonymous closure>':.
    //     0xb58584: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a98] AnonymousClosure: (0xb586b4), in [package:nuonline/app/modules/topic/topic_detail/views/topic_detail_view.dart] TopicDetailView::build (0xb57e1c)
    //     0xb58588: ldr             x1, [x1, #0xa98]
    // 0xb5858c: r0 = AllocateClosure()
    //     0xb5858c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb58590: mov             x1, x0
    // 0xb58594: ldur            x0, [fp, #-0x38]
    // 0xb58598: StoreField: r0->field_1f = r1
    //     0xb58598: stur            w1, [x0, #0x1f]
    // 0xb5859c: r1 = false
    //     0xb5859c: add             x1, NULL, #0x30  ; false
    // 0xb585a0: StoreField: r0->field_23 = r1
    //     0xb585a0: stur            w1, [x0, #0x23]
    // 0xb585a4: ldur            x1, [fp, #-0x28]
    // 0xb585a8: StoreField: r0->field_7 = r1
    //     0xb585a8: stur            w1, [x0, #7]
    // 0xb585ac: LeaveFrame
    //     0xb585ac: mov             SP, fp
    //     0xb585b0: ldp             fp, lr, [SP], #0x10
    // 0xb585b4: ret
    //     0xb585b4: ret             
    // 0xb585b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb585b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb585bc: b               #0xb58454
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb586b4, size: 0xd0
    // 0xb586b4: EnterFrame
    //     0xb586b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb586b8: mov             fp, SP
    // 0xb586bc: AllocStack(0x20)
    //     0xb586bc: sub             SP, SP, #0x20
    // 0xb586c0: SetupParameters()
    //     0xb586c0: ldr             x0, [fp, #0x10]
    //     0xb586c4: ldur            w1, [x0, #0x17]
    //     0xb586c8: add             x1, x1, HEAP, lsl #32
    //     0xb586cc: stur            x1, [fp, #-8]
    // 0xb586d0: CheckStackOverflow
    //     0xb586d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb586d4: cmp             SP, x16
    //     0xb586d8: b.ls            #0xb58778
    // 0xb586dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb586dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb586e0: ldr             x0, [x0, #0x2670]
    //     0xb586e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb586e8: cmp             w0, w16
    //     0xb586ec: b.ne            #0xb586f8
    //     0xb586f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb586f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb586f8: r1 = Null
    //     0xb586f8: mov             x1, NULL
    // 0xb586fc: r2 = 4
    //     0xb586fc: movz            x2, #0x4
    // 0xb58700: r0 = AllocateArray()
    //     0xb58700: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb58704: mov             x2, x0
    // 0xb58708: r16 = "id"
    //     0xb58708: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb5870c: ldr             x16, [x16, #0x740]
    // 0xb58710: StoreField: r2->field_f = r16
    //     0xb58710: stur            w16, [x2, #0xf]
    // 0xb58714: ldur            x0, [fp, #-8]
    // 0xb58718: LoadField: r1 = r0->field_f
    //     0xb58718: ldur            w1, [x0, #0xf]
    // 0xb5871c: DecompressPointer r1
    //     0xb5871c: add             x1, x1, HEAP, lsl #32
    // 0xb58720: cmp             w1, NULL
    // 0xb58724: b.eq            #0xb58780
    // 0xb58728: LoadField: r3 = r1->field_7
    //     0xb58728: ldur            x3, [x1, #7]
    // 0xb5872c: r0 = BoxInt64Instr(r3)
    //     0xb5872c: sbfiz           x0, x3, #1, #0x1f
    //     0xb58730: cmp             x3, x0, asr #1
    //     0xb58734: b.eq            #0xb58740
    //     0xb58738: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5873c: stur            x3, [x0, #7]
    // 0xb58740: StoreField: r2->field_13 = r0
    //     0xb58740: stur            w0, [x2, #0x13]
    // 0xb58744: r16 = <String, int>
    //     0xb58744: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb58748: stp             x2, x16, [SP]
    // 0xb5874c: r0 = Map._fromLiteral()
    //     0xb5874c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb58750: r16 = "/article/article-detail"
    //     0xb58750: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xb58754: ldr             x16, [x16, #0x3d0]
    // 0xb58758: stp             x16, NULL, [SP, #8]
    // 0xb5875c: str             x0, [SP]
    // 0xb58760: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb58760: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb58764: ldr             x4, [x4, #0x478]
    // 0xb58768: r0 = GetNavigation.toNamed()
    //     0xb58768: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb5876c: LeaveFrame
    //     0xb5876c: mov             SP, fp
    //     0xb58770: ldp             fp, lr, [SP], #0x10
    // 0xb58774: ret
    //     0xb58774: ret             
    // 0xb58778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5877c: b               #0xb586dc
    // 0xb58780: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb58780: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}
