// lib: , url: package:nuonline/app/modules/waris/controllers/waris_controller.dart

// class id: 1050625, size: 0x8
class :: {
}

// class id: 1045, size: 0x20, field offset: 0x8
class HeirItem extends Object {

  _ copyWith(/* No info */) {
    // ** addr: 0x906fa0, size: 0x190
    // 0x906fa0: EnterFrame
    //     0x906fa0: stp             fp, lr, [SP, #-0x10]!
    //     0x906fa4: mov             fp, SP
    // 0x906fa8: AllocStack(0x28)
    //     0x906fa8: sub             SP, SP, #0x28
    // 0x906fac: SetupParameters({dynamic checked = Null /* r3 */, dynamic count = Null /* r5 */, dynamic disabled = Null /* r0 */})
    //     0x906fac: ldur            w0, [x4, #0x13]
    //     0x906fb0: ldur            w2, [x4, #0x1f]
    //     0x906fb4: add             x2, x2, HEAP, lsl #32
    //     0x906fb8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22228] "checked"
    //     0x906fbc: ldr             x16, [x16, #0x228]
    //     0x906fc0: cmp             w2, w16
    //     0x906fc4: b.ne            #0x906fe8
    //     0x906fc8: ldur            w2, [x4, #0x23]
    //     0x906fcc: add             x2, x2, HEAP, lsl #32
    //     0x906fd0: sub             w3, w0, w2
    //     0x906fd4: add             x2, fp, w3, sxtw #2
    //     0x906fd8: ldr             x2, [x2, #8]
    //     0x906fdc: mov             x3, x2
    //     0x906fe0: movz            x2, #0x1
    //     0x906fe4: b               #0x906ff0
    //     0x906fe8: mov             x3, NULL
    //     0x906fec: movz            x2, #0
    //     0x906ff0: lsl             x5, x2, #1
    //     0x906ff4: lsl             w6, w5, #1
    //     0x906ff8: add             w7, w6, #8
    //     0x906ffc: add             x16, x4, w7, sxtw #1
    //     0x907000: ldur            w8, [x16, #0xf]
    //     0x907004: add             x8, x8, HEAP, lsl #32
    //     0x907008: add             x16, PP, #0xc, lsl #12  ; [pp+0xc640] "count"
    //     0x90700c: ldr             x16, [x16, #0x640]
    //     0x907010: cmp             w8, w16
    //     0x907014: b.ne            #0x907048
    //     0x907018: add             w2, w6, #0xa
    //     0x90701c: add             x16, x4, w2, sxtw #1
    //     0x907020: ldur            w6, [x16, #0xf]
    //     0x907024: add             x6, x6, HEAP, lsl #32
    //     0x907028: sub             w2, w0, w6
    //     0x90702c: add             x6, fp, w2, sxtw #2
    //     0x907030: ldr             x6, [x6, #8]
    //     0x907034: add             w2, w5, #2
    //     0x907038: sbfx            x5, x2, #1, #0x1f
    //     0x90703c: mov             x2, x5
    //     0x907040: mov             x5, x6
    //     0x907044: b               #0x90704c
    //     0x907048: mov             x5, NULL
    //     0x90704c: lsl             x6, x2, #1
    //     0x907050: lsl             w2, w6, #1
    //     0x907054: add             w6, w2, #8
    //     0x907058: add             x16, x4, w6, sxtw #1
    //     0x90705c: ldur            w7, [x16, #0xf]
    //     0x907060: add             x7, x7, HEAP, lsl #32
    //     0x907064: add             x16, PP, #0x28, lsl #12  ; [pp+0x282d8] "disabled"
    //     0x907068: ldr             x16, [x16, #0x2d8]
    //     0x90706c: cmp             w7, w16
    //     0x907070: b.ne            #0x907094
    //     0x907074: add             w6, w2, #0xa
    //     0x907078: add             x16, x4, w6, sxtw #1
    //     0x90707c: ldur            w2, [x16, #0xf]
    //     0x907080: add             x2, x2, HEAP, lsl #32
    //     0x907084: sub             w4, w0, w2
    //     0x907088: add             x0, fp, w4, sxtw #2
    //     0x90708c: ldr             x0, [x0, #8]
    //     0x907090: b               #0x907098
    //     0x907094: mov             x0, NULL
    // 0x907098: LoadField: r2 = r1->field_7
    //     0x907098: ldur            w2, [x1, #7]
    // 0x90709c: DecompressPointer r2
    //     0x90709c: add             x2, x2, HEAP, lsl #32
    // 0x9070a0: stur            x2, [fp, #-0x28]
    // 0x9070a4: cmp             w5, NULL
    // 0x9070a8: b.ne            #0x9070b4
    // 0x9070ac: LoadField: r4 = r1->field_b
    //     0x9070ac: ldur            x4, [x1, #0xb]
    // 0x9070b0: b               #0x9070c0
    // 0x9070b4: r4 = LoadInt32Instr(r5)
    //     0x9070b4: sbfx            x4, x5, #1, #0x1f
    //     0x9070b8: tbz             w5, #0, #0x9070c0
    //     0x9070bc: ldur            x4, [x5, #7]
    // 0x9070c0: stur            x4, [fp, #-0x20]
    // 0x9070c4: cmp             w3, NULL
    // 0x9070c8: b.ne            #0x9070d4
    // 0x9070cc: LoadField: r3 = r1->field_13
    //     0x9070cc: ldur            w3, [x1, #0x13]
    // 0x9070d0: DecompressPointer r3
    //     0x9070d0: add             x3, x3, HEAP, lsl #32
    // 0x9070d4: stur            x3, [fp, #-0x18]
    // 0x9070d8: cmp             w0, NULL
    // 0x9070dc: b.ne            #0x9070e8
    // 0x9070e0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9070e0: ldur            w0, [x1, #0x17]
    // 0x9070e4: DecompressPointer r0
    //     0x9070e4: add             x0, x0, HEAP, lsl #32
    // 0x9070e8: stur            x0, [fp, #-0x10]
    // 0x9070ec: LoadField: r5 = r1->field_1b
    //     0x9070ec: ldur            w5, [x1, #0x1b]
    // 0x9070f0: DecompressPointer r5
    //     0x9070f0: add             x5, x5, HEAP, lsl #32
    // 0x9070f4: stur            x5, [fp, #-8]
    // 0x9070f8: r0 = HeirItem()
    //     0x9070f8: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x9070fc: ldur            x1, [fp, #-0x28]
    // 0x907100: StoreField: r0->field_7 = r1
    //     0x907100: stur            w1, [x0, #7]
    // 0x907104: ldur            x1, [fp, #-0x20]
    // 0x907108: StoreField: r0->field_b = r1
    //     0x907108: stur            x1, [x0, #0xb]
    // 0x90710c: ldur            x1, [fp, #-0x18]
    // 0x907110: StoreField: r0->field_13 = r1
    //     0x907110: stur            w1, [x0, #0x13]
    // 0x907114: ldur            x1, [fp, #-0x10]
    // 0x907118: ArrayStore: r0[0] = r1  ; List_4
    //     0x907118: stur            w1, [x0, #0x17]
    // 0x90711c: ldur            x1, [fp, #-8]
    // 0x907120: StoreField: r0->field_1b = r1
    //     0x907120: stur            w1, [x0, #0x1b]
    // 0x907124: LeaveFrame
    //     0x907124: mov             SP, fp
    //     0x907128: ldp             fp, lr, [SP], #0x10
    // 0x90712c: ret
    //     0x90712c: ret             
  }
}

// class id: 1788, size: 0x30, field offset: 0x20
class WarisController extends GetxController {

  _ WarisController(/* No info */) {
    // ** addr: 0x844f54, size: 0x9e4
    // 0x844f54: EnterFrame
    //     0x844f54: stp             fp, lr, [SP, #-0x10]!
    //     0x844f58: mov             fp, SP
    // 0x844f5c: AllocStack(0x30)
    //     0x844f5c: sub             SP, SP, #0x30
    // 0x844f60: SetupParameters(WarisController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x844f60: mov             x0, x2
    //     0x844f64: stur            x2, [fp, #-0x10]
    //     0x844f68: mov             x2, x1
    //     0x844f6c: stur            x1, [fp, #-8]
    // 0x844f70: CheckStackOverflow
    //     0x844f70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x844f74: cmp             SP, x16
    //     0x844f78: b.ls            #0x845930
    // 0x844f7c: r1 = true
    //     0x844f7c: add             x1, NULL, #0x20  ; true
    // 0x844f80: r0 = BoolExtension.obs()
    //     0x844f80: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x844f84: ldur            x1, [fp, #-8]
    // 0x844f88: StoreField: r1->field_23 = r0
    //     0x844f88: stur            w0, [x1, #0x23]
    //     0x844f8c: ldurb           w16, [x1, #-1]
    //     0x844f90: ldurb           w17, [x0, #-1]
    //     0x844f94: and             x16, x17, x16, lsr #2
    //     0x844f98: tst             x16, HEAP, lsr #32
    //     0x844f9c: b.eq            #0x844fa4
    //     0x844fa0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x844fa4: r0 = HeirItem()
    //     0x844fa4: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x844fa8: mov             x3, x0
    // 0x844fac: r0 = Instance_Heir
    //     0x844fac: add             x0, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x844fb0: ldr             x0, [x0, #0x8c8]
    // 0x844fb4: stur            x3, [fp, #-0x18]
    // 0x844fb8: StoreField: r3->field_7 = r0
    //     0x844fb8: stur            w0, [x3, #7]
    // 0x844fbc: r0 = 1
    //     0x844fbc: movz            x0, #0x1
    // 0x844fc0: StoreField: r3->field_b = r0
    //     0x844fc0: stur            x0, [x3, #0xb]
    // 0x844fc4: r4 = false
    //     0x844fc4: add             x4, NULL, #0x30  ; false
    // 0x844fc8: StoreField: r3->field_13 = r4
    //     0x844fc8: stur            w4, [x3, #0x13]
    // 0x844fcc: ArrayStore: r3[0] = r4  ; List_4
    //     0x844fcc: stur            w4, [x3, #0x17]
    // 0x844fd0: r5 = 2
    //     0x844fd0: movz            x5, #0x2
    // 0x844fd4: StoreField: r3->field_1b = r5
    //     0x844fd4: stur            w5, [x3, #0x1b]
    // 0x844fd8: r1 = <HeirItem>
    //     0x844fd8: add             x1, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x844fdc: ldr             x1, [x1, #0x68]
    // 0x844fe0: r2 = 32
    //     0x844fe0: movz            x2, #0x20
    // 0x844fe4: r0 = AllocateArray()
    //     0x844fe4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x844fe8: mov             x1, x0
    // 0x844fec: ldur            x0, [fp, #-0x18]
    // 0x844ff0: stur            x1, [fp, #-0x20]
    // 0x844ff4: StoreField: r1->field_f = r0
    //     0x844ff4: stur            w0, [x1, #0xf]
    // 0x844ff8: r0 = HeirItem()
    //     0x844ff8: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x844ffc: mov             x1, x0
    // 0x845000: r0 = Instance_Heir
    //     0x845000: add             x0, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x845004: ldr             x0, [x0, #0x7d0]
    // 0x845008: StoreField: r1->field_7 = r0
    //     0x845008: stur            w0, [x1, #7]
    // 0x84500c: r2 = 1
    //     0x84500c: movz            x2, #0x1
    // 0x845010: StoreField: r1->field_b = r2
    //     0x845010: stur            x2, [x1, #0xb]
    // 0x845014: r3 = false
    //     0x845014: add             x3, NULL, #0x30  ; false
    // 0x845018: StoreField: r1->field_13 = r3
    //     0x845018: stur            w3, [x1, #0x13]
    // 0x84501c: ArrayStore: r1[0] = r3  ; List_4
    //     0x84501c: stur            w3, [x1, #0x17]
    // 0x845020: mov             x0, x1
    // 0x845024: ldur            x1, [fp, #-0x20]
    // 0x845028: ArrayStore: r1[1] = r0  ; List_4
    //     0x845028: add             x25, x1, #0x13
    //     0x84502c: str             w0, [x25]
    //     0x845030: tbz             w0, #0, #0x84504c
    //     0x845034: ldurb           w16, [x1, #-1]
    //     0x845038: ldurb           w17, [x0, #-1]
    //     0x84503c: and             x16, x17, x16, lsr #2
    //     0x845040: tst             x16, HEAP, lsr #32
    //     0x845044: b.eq            #0x84504c
    //     0x845048: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x84504c: r0 = HeirItem()
    //     0x84504c: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845050: mov             x1, x0
    // 0x845054: r0 = Instance_Heir
    //     0x845054: add             x0, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x845058: ldr             x0, [x0, #0x7e0]
    // 0x84505c: StoreField: r1->field_7 = r0
    //     0x84505c: stur            w0, [x1, #7]
    // 0x845060: r2 = 1
    //     0x845060: movz            x2, #0x1
    // 0x845064: StoreField: r1->field_b = r2
    //     0x845064: stur            x2, [x1, #0xb]
    // 0x845068: r3 = false
    //     0x845068: add             x3, NULL, #0x30  ; false
    // 0x84506c: StoreField: r1->field_13 = r3
    //     0x84506c: stur            w3, [x1, #0x13]
    // 0x845070: ArrayStore: r1[0] = r3  ; List_4
    //     0x845070: stur            w3, [x1, #0x17]
    // 0x845074: mov             x0, x1
    // 0x845078: ldur            x1, [fp, #-0x20]
    // 0x84507c: ArrayStore: r1[2] = r0  ; List_4
    //     0x84507c: add             x25, x1, #0x17
    //     0x845080: str             w0, [x25]
    //     0x845084: tbz             w0, #0, #0x8450a0
    //     0x845088: ldurb           w16, [x1, #-1]
    //     0x84508c: ldurb           w17, [x0, #-1]
    //     0x845090: and             x16, x17, x16, lsr #2
    //     0x845094: tst             x16, HEAP, lsr #32
    //     0x845098: b.eq            #0x8450a0
    //     0x84509c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8450a0: r0 = HeirItem()
    //     0x8450a0: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8450a4: mov             x1, x0
    // 0x8450a8: r0 = Instance_Heir
    //     0x8450a8: add             x0, PP, #0x31, lsl #12  ; [pp+0x318e8] Obj!Heir@e2dcf1
    //     0x8450ac: ldr             x0, [x0, #0x8e8]
    // 0x8450b0: StoreField: r1->field_7 = r0
    //     0x8450b0: stur            w0, [x1, #7]
    // 0x8450b4: r2 = 1
    //     0x8450b4: movz            x2, #0x1
    // 0x8450b8: StoreField: r1->field_b = r2
    //     0x8450b8: stur            x2, [x1, #0xb]
    // 0x8450bc: r3 = false
    //     0x8450bc: add             x3, NULL, #0x30  ; false
    // 0x8450c0: StoreField: r1->field_13 = r3
    //     0x8450c0: stur            w3, [x1, #0x13]
    // 0x8450c4: ArrayStore: r1[0] = r3  ; List_4
    //     0x8450c4: stur            w3, [x1, #0x17]
    // 0x8450c8: mov             x0, x1
    // 0x8450cc: ldur            x1, [fp, #-0x20]
    // 0x8450d0: ArrayStore: r1[3] = r0  ; List_4
    //     0x8450d0: add             x25, x1, #0x1b
    //     0x8450d4: str             w0, [x25]
    //     0x8450d8: tbz             w0, #0, #0x8450f4
    //     0x8450dc: ldurb           w16, [x1, #-1]
    //     0x8450e0: ldurb           w17, [x0, #-1]
    //     0x8450e4: and             x16, x17, x16, lsr #2
    //     0x8450e8: tst             x16, HEAP, lsr #32
    //     0x8450ec: b.eq            #0x8450f4
    //     0x8450f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8450f4: r0 = HeirItem()
    //     0x8450f4: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8450f8: mov             x1, x0
    // 0x8450fc: r0 = Instance_Heir
    //     0x8450fc: add             x0, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x845100: ldr             x0, [x0, #0x838]
    // 0x845104: StoreField: r1->field_7 = r0
    //     0x845104: stur            w0, [x1, #7]
    // 0x845108: r2 = 1
    //     0x845108: movz            x2, #0x1
    // 0x84510c: StoreField: r1->field_b = r2
    //     0x84510c: stur            x2, [x1, #0xb]
    // 0x845110: r3 = false
    //     0x845110: add             x3, NULL, #0x30  ; false
    // 0x845114: StoreField: r1->field_13 = r3
    //     0x845114: stur            w3, [x1, #0x13]
    // 0x845118: ArrayStore: r1[0] = r3  ; List_4
    //     0x845118: stur            w3, [x1, #0x17]
    // 0x84511c: r4 = 2
    //     0x84511c: movz            x4, #0x2
    // 0x845120: StoreField: r1->field_1b = r4
    //     0x845120: stur            w4, [x1, #0x1b]
    // 0x845124: mov             x0, x1
    // 0x845128: ldur            x1, [fp, #-0x20]
    // 0x84512c: ArrayStore: r1[4] = r0  ; List_4
    //     0x84512c: add             x25, x1, #0x1f
    //     0x845130: str             w0, [x25]
    //     0x845134: tbz             w0, #0, #0x845150
    //     0x845138: ldurb           w16, [x1, #-1]
    //     0x84513c: ldurb           w17, [x0, #-1]
    //     0x845140: and             x16, x17, x16, lsr #2
    //     0x845144: tst             x16, HEAP, lsr #32
    //     0x845148: b.eq            #0x845150
    //     0x84514c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845150: r0 = HeirItem()
    //     0x845150: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845154: mov             x1, x0
    // 0x845158: r0 = Instance_Heir
    //     0x845158: add             x0, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x84515c: ldr             x0, [x0, #0x88]
    // 0x845160: StoreField: r1->field_7 = r0
    //     0x845160: stur            w0, [x1, #7]
    // 0x845164: r2 = 1
    //     0x845164: movz            x2, #0x1
    // 0x845168: StoreField: r1->field_b = r2
    //     0x845168: stur            x2, [x1, #0xb]
    // 0x84516c: r3 = false
    //     0x84516c: add             x3, NULL, #0x30  ; false
    // 0x845170: StoreField: r1->field_13 = r3
    //     0x845170: stur            w3, [x1, #0x13]
    // 0x845174: ArrayStore: r1[0] = r3  ; List_4
    //     0x845174: stur            w3, [x1, #0x17]
    // 0x845178: r4 = 2
    //     0x845178: movz            x4, #0x2
    // 0x84517c: StoreField: r1->field_1b = r4
    //     0x84517c: stur            w4, [x1, #0x1b]
    // 0x845180: mov             x0, x1
    // 0x845184: ldur            x1, [fp, #-0x20]
    // 0x845188: ArrayStore: r1[5] = r0  ; List_4
    //     0x845188: add             x25, x1, #0x23
    //     0x84518c: str             w0, [x25]
    //     0x845190: tbz             w0, #0, #0x8451ac
    //     0x845194: ldurb           w16, [x1, #-1]
    //     0x845198: ldurb           w17, [x0, #-1]
    //     0x84519c: and             x16, x17, x16, lsr #2
    //     0x8451a0: tst             x16, HEAP, lsr #32
    //     0x8451a4: b.eq            #0x8451ac
    //     0x8451a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8451ac: r0 = HeirItem()
    //     0x8451ac: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8451b0: mov             x1, x0
    // 0x8451b4: r0 = Instance_Heir
    //     0x8451b4: add             x0, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x8451b8: ldr             x0, [x0, #0x90]
    // 0x8451bc: StoreField: r1->field_7 = r0
    //     0x8451bc: stur            w0, [x1, #7]
    // 0x8451c0: r2 = 1
    //     0x8451c0: movz            x2, #0x1
    // 0x8451c4: StoreField: r1->field_b = r2
    //     0x8451c4: stur            x2, [x1, #0xb]
    // 0x8451c8: r3 = false
    //     0x8451c8: add             x3, NULL, #0x30  ; false
    // 0x8451cc: StoreField: r1->field_13 = r3
    //     0x8451cc: stur            w3, [x1, #0x13]
    // 0x8451d0: ArrayStore: r1[0] = r3  ; List_4
    //     0x8451d0: stur            w3, [x1, #0x17]
    // 0x8451d4: mov             x0, x1
    // 0x8451d8: ldur            x1, [fp, #-0x20]
    // 0x8451dc: ArrayStore: r1[6] = r0  ; List_4
    //     0x8451dc: add             x25, x1, #0x27
    //     0x8451e0: str             w0, [x25]
    //     0x8451e4: tbz             w0, #0, #0x845200
    //     0x8451e8: ldurb           w16, [x1, #-1]
    //     0x8451ec: ldurb           w17, [x0, #-1]
    //     0x8451f0: and             x16, x17, x16, lsr #2
    //     0x8451f4: tst             x16, HEAP, lsr #32
    //     0x8451f8: b.eq            #0x845200
    //     0x8451fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845200: r0 = HeirItem()
    //     0x845200: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845204: mov             x1, x0
    // 0x845208: r0 = Instance_Heir
    //     0x845208: add             x0, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x84520c: ldr             x0, [x0, #0x98]
    // 0x845210: StoreField: r1->field_7 = r0
    //     0x845210: stur            w0, [x1, #7]
    // 0x845214: r2 = 1
    //     0x845214: movz            x2, #0x1
    // 0x845218: StoreField: r1->field_b = r2
    //     0x845218: stur            x2, [x1, #0xb]
    // 0x84521c: r3 = false
    //     0x84521c: add             x3, NULL, #0x30  ; false
    // 0x845220: StoreField: r1->field_13 = r3
    //     0x845220: stur            w3, [x1, #0x13]
    // 0x845224: ArrayStore: r1[0] = r3  ; List_4
    //     0x845224: stur            w3, [x1, #0x17]
    // 0x845228: mov             x0, x1
    // 0x84522c: ldur            x1, [fp, #-0x20]
    // 0x845230: ArrayStore: r1[7] = r0  ; List_4
    //     0x845230: add             x25, x1, #0x2b
    //     0x845234: str             w0, [x25]
    //     0x845238: tbz             w0, #0, #0x845254
    //     0x84523c: ldurb           w16, [x1, #-1]
    //     0x845240: ldurb           w17, [x0, #-1]
    //     0x845244: and             x16, x17, x16, lsr #2
    //     0x845248: tst             x16, HEAP, lsr #32
    //     0x84524c: b.eq            #0x845254
    //     0x845250: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845254: r0 = HeirItem()
    //     0x845254: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845258: mov             x1, x0
    // 0x84525c: r0 = Instance_Heir
    //     0x84525c: add             x0, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x845260: ldr             x0, [x0, #0xa0]
    // 0x845264: StoreField: r1->field_7 = r0
    //     0x845264: stur            w0, [x1, #7]
    // 0x845268: r2 = 1
    //     0x845268: movz            x2, #0x1
    // 0x84526c: StoreField: r1->field_b = r2
    //     0x84526c: stur            x2, [x1, #0xb]
    // 0x845270: r3 = false
    //     0x845270: add             x3, NULL, #0x30  ; false
    // 0x845274: StoreField: r1->field_13 = r3
    //     0x845274: stur            w3, [x1, #0x13]
    // 0x845278: ArrayStore: r1[0] = r3  ; List_4
    //     0x845278: stur            w3, [x1, #0x17]
    // 0x84527c: mov             x0, x1
    // 0x845280: ldur            x1, [fp, #-0x20]
    // 0x845284: ArrayStore: r1[8] = r0  ; List_4
    //     0x845284: add             x25, x1, #0x2f
    //     0x845288: str             w0, [x25]
    //     0x84528c: tbz             w0, #0, #0x8452a8
    //     0x845290: ldurb           w16, [x1, #-1]
    //     0x845294: ldurb           w17, [x0, #-1]
    //     0x845298: and             x16, x17, x16, lsr #2
    //     0x84529c: tst             x16, HEAP, lsr #32
    //     0x8452a0: b.eq            #0x8452a8
    //     0x8452a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8452a8: r0 = HeirItem()
    //     0x8452a8: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8452ac: mov             x1, x0
    // 0x8452b0: r0 = Instance_Heir
    //     0x8452b0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x8452b4: ldr             x0, [x0, #0x868]
    // 0x8452b8: StoreField: r1->field_7 = r0
    //     0x8452b8: stur            w0, [x1, #7]
    // 0x8452bc: r2 = 1
    //     0x8452bc: movz            x2, #0x1
    // 0x8452c0: StoreField: r1->field_b = r2
    //     0x8452c0: stur            x2, [x1, #0xb]
    // 0x8452c4: r3 = false
    //     0x8452c4: add             x3, NULL, #0x30  ; false
    // 0x8452c8: StoreField: r1->field_13 = r3
    //     0x8452c8: stur            w3, [x1, #0x13]
    // 0x8452cc: ArrayStore: r1[0] = r3  ; List_4
    //     0x8452cc: stur            w3, [x1, #0x17]
    // 0x8452d0: mov             x0, x1
    // 0x8452d4: ldur            x1, [fp, #-0x20]
    // 0x8452d8: ArrayStore: r1[9] = r0  ; List_4
    //     0x8452d8: add             x25, x1, #0x33
    //     0x8452dc: str             w0, [x25]
    //     0x8452e0: tbz             w0, #0, #0x8452fc
    //     0x8452e4: ldurb           w16, [x1, #-1]
    //     0x8452e8: ldurb           w17, [x0, #-1]
    //     0x8452ec: and             x16, x17, x16, lsr #2
    //     0x8452f0: tst             x16, HEAP, lsr #32
    //     0x8452f4: b.eq            #0x8452fc
    //     0x8452f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8452fc: r0 = HeirItem()
    //     0x8452fc: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845300: mov             x1, x0
    // 0x845304: r0 = Instance_Heir
    //     0x845304: add             x0, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x845308: ldr             x0, [x0, #0x878]
    // 0x84530c: StoreField: r1->field_7 = r0
    //     0x84530c: stur            w0, [x1, #7]
    // 0x845310: r2 = 1
    //     0x845310: movz            x2, #0x1
    // 0x845314: StoreField: r1->field_b = r2
    //     0x845314: stur            x2, [x1, #0xb]
    // 0x845318: r3 = false
    //     0x845318: add             x3, NULL, #0x30  ; false
    // 0x84531c: StoreField: r1->field_13 = r3
    //     0x84531c: stur            w3, [x1, #0x13]
    // 0x845320: ArrayStore: r1[0] = r3  ; List_4
    //     0x845320: stur            w3, [x1, #0x17]
    // 0x845324: mov             x0, x1
    // 0x845328: ldur            x1, [fp, #-0x20]
    // 0x84532c: ArrayStore: r1[10] = r0  ; List_4
    //     0x84532c: add             x25, x1, #0x37
    //     0x845330: str             w0, [x25]
    //     0x845334: tbz             w0, #0, #0x845350
    //     0x845338: ldurb           w16, [x1, #-1]
    //     0x84533c: ldurb           w17, [x0, #-1]
    //     0x845340: and             x16, x17, x16, lsr #2
    //     0x845344: tst             x16, HEAP, lsr #32
    //     0x845348: b.eq            #0x845350
    //     0x84534c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845350: r0 = HeirItem()
    //     0x845350: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845354: mov             x1, x0
    // 0x845358: r0 = Instance_Heir
    //     0x845358: add             x0, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x84535c: ldr             x0, [x0, #0x888]
    // 0x845360: StoreField: r1->field_7 = r0
    //     0x845360: stur            w0, [x1, #7]
    // 0x845364: r2 = 1
    //     0x845364: movz            x2, #0x1
    // 0x845368: StoreField: r1->field_b = r2
    //     0x845368: stur            x2, [x1, #0xb]
    // 0x84536c: r3 = false
    //     0x84536c: add             x3, NULL, #0x30  ; false
    // 0x845370: StoreField: r1->field_13 = r3
    //     0x845370: stur            w3, [x1, #0x13]
    // 0x845374: ArrayStore: r1[0] = r3  ; List_4
    //     0x845374: stur            w3, [x1, #0x17]
    // 0x845378: mov             x0, x1
    // 0x84537c: ldur            x1, [fp, #-0x20]
    // 0x845380: ArrayStore: r1[11] = r0  ; List_4
    //     0x845380: add             x25, x1, #0x3b
    //     0x845384: str             w0, [x25]
    //     0x845388: tbz             w0, #0, #0x8453a4
    //     0x84538c: ldurb           w16, [x1, #-1]
    //     0x845390: ldurb           w17, [x0, #-1]
    //     0x845394: and             x16, x17, x16, lsr #2
    //     0x845398: tst             x16, HEAP, lsr #32
    //     0x84539c: b.eq            #0x8453a4
    //     0x8453a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8453a4: r0 = HeirItem()
    //     0x8453a4: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8453a8: mov             x1, x0
    // 0x8453ac: r0 = Instance_Heir
    //     0x8453ac: add             x0, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x8453b0: ldr             x0, [x0, #0x898]
    // 0x8453b4: StoreField: r1->field_7 = r0
    //     0x8453b4: stur            w0, [x1, #7]
    // 0x8453b8: r2 = 1
    //     0x8453b8: movz            x2, #0x1
    // 0x8453bc: StoreField: r1->field_b = r2
    //     0x8453bc: stur            x2, [x1, #0xb]
    // 0x8453c0: r3 = false
    //     0x8453c0: add             x3, NULL, #0x30  ; false
    // 0x8453c4: StoreField: r1->field_13 = r3
    //     0x8453c4: stur            w3, [x1, #0x13]
    // 0x8453c8: ArrayStore: r1[0] = r3  ; List_4
    //     0x8453c8: stur            w3, [x1, #0x17]
    // 0x8453cc: mov             x0, x1
    // 0x8453d0: ldur            x1, [fp, #-0x20]
    // 0x8453d4: ArrayStore: r1[12] = r0  ; List_4
    //     0x8453d4: add             x25, x1, #0x3f
    //     0x8453d8: str             w0, [x25]
    //     0x8453dc: tbz             w0, #0, #0x8453f8
    //     0x8453e0: ldurb           w16, [x1, #-1]
    //     0x8453e4: ldurb           w17, [x0, #-1]
    //     0x8453e8: and             x16, x17, x16, lsr #2
    //     0x8453ec: tst             x16, HEAP, lsr #32
    //     0x8453f0: b.eq            #0x8453f8
    //     0x8453f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8453f8: r0 = HeirItem()
    //     0x8453f8: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8453fc: mov             x1, x0
    // 0x845400: r0 = Instance_Heir
    //     0x845400: add             x0, PP, #0x31, lsl #12  ; [pp+0x318a8] Obj!Heir@e2db11
    //     0x845404: ldr             x0, [x0, #0x8a8]
    // 0x845408: StoreField: r1->field_7 = r0
    //     0x845408: stur            w0, [x1, #7]
    // 0x84540c: r2 = 1
    //     0x84540c: movz            x2, #0x1
    // 0x845410: StoreField: r1->field_b = r2
    //     0x845410: stur            x2, [x1, #0xb]
    // 0x845414: r3 = false
    //     0x845414: add             x3, NULL, #0x30  ; false
    // 0x845418: StoreField: r1->field_13 = r3
    //     0x845418: stur            w3, [x1, #0x13]
    // 0x84541c: ArrayStore: r1[0] = r3  ; List_4
    //     0x84541c: stur            w3, [x1, #0x17]
    // 0x845420: mov             x0, x1
    // 0x845424: ldur            x1, [fp, #-0x20]
    // 0x845428: ArrayStore: r1[13] = r0  ; List_4
    //     0x845428: add             x25, x1, #0x43
    //     0x84542c: str             w0, [x25]
    //     0x845430: tbz             w0, #0, #0x84544c
    //     0x845434: ldurb           w16, [x1, #-1]
    //     0x845438: ldurb           w17, [x0, #-1]
    //     0x84543c: and             x16, x17, x16, lsr #2
    //     0x845440: tst             x16, HEAP, lsr #32
    //     0x845444: b.eq            #0x84544c
    //     0x845448: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x84544c: r0 = HeirItem()
    //     0x84544c: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845450: mov             x1, x0
    // 0x845454: r0 = Instance_Heir
    //     0x845454: add             x0, PP, #0x31, lsl #12  ; [pp+0x318b8] Obj!Heir@e2dae1
    //     0x845458: ldr             x0, [x0, #0x8b8]
    // 0x84545c: StoreField: r1->field_7 = r0
    //     0x84545c: stur            w0, [x1, #7]
    // 0x845460: r2 = 1
    //     0x845460: movz            x2, #0x1
    // 0x845464: StoreField: r1->field_b = r2
    //     0x845464: stur            x2, [x1, #0xb]
    // 0x845468: r3 = false
    //     0x845468: add             x3, NULL, #0x30  ; false
    // 0x84546c: StoreField: r1->field_13 = r3
    //     0x84546c: stur            w3, [x1, #0x13]
    // 0x845470: ArrayStore: r1[0] = r3  ; List_4
    //     0x845470: stur            w3, [x1, #0x17]
    // 0x845474: mov             x0, x1
    // 0x845478: ldur            x1, [fp, #-0x20]
    // 0x84547c: ArrayStore: r1[14] = r0  ; List_4
    //     0x84547c: add             x25, x1, #0x47
    //     0x845480: str             w0, [x25]
    //     0x845484: tbz             w0, #0, #0x8454a0
    //     0x845488: ldurb           w16, [x1, #-1]
    //     0x84548c: ldurb           w17, [x0, #-1]
    //     0x845490: and             x16, x17, x16, lsr #2
    //     0x845494: tst             x16, HEAP, lsr #32
    //     0x845498: b.eq            #0x8454a0
    //     0x84549c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8454a0: r0 = HeirItem()
    //     0x8454a0: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8454a4: mov             x1, x0
    // 0x8454a8: r0 = Instance_Heir
    //     0x8454a8: add             x0, PP, #0x31, lsl #12  ; [pp+0x318d8] Obj!Heir@e2dab1
    //     0x8454ac: ldr             x0, [x0, #0x8d8]
    // 0x8454b0: StoreField: r1->field_7 = r0
    //     0x8454b0: stur            w0, [x1, #7]
    // 0x8454b4: r2 = 1
    //     0x8454b4: movz            x2, #0x1
    // 0x8454b8: StoreField: r1->field_b = r2
    //     0x8454b8: stur            x2, [x1, #0xb]
    // 0x8454bc: r3 = false
    //     0x8454bc: add             x3, NULL, #0x30  ; false
    // 0x8454c0: StoreField: r1->field_13 = r3
    //     0x8454c0: stur            w3, [x1, #0x13]
    // 0x8454c4: ArrayStore: r1[0] = r3  ; List_4
    //     0x8454c4: stur            w3, [x1, #0x17]
    // 0x8454c8: mov             x0, x1
    // 0x8454cc: ldur            x1, [fp, #-0x20]
    // 0x8454d0: ArrayStore: r1[15] = r0  ; List_4
    //     0x8454d0: add             x25, x1, #0x4b
    //     0x8454d4: str             w0, [x25]
    //     0x8454d8: tbz             w0, #0, #0x8454f4
    //     0x8454dc: ldurb           w16, [x1, #-1]
    //     0x8454e0: ldurb           w17, [x0, #-1]
    //     0x8454e4: and             x16, x17, x16, lsr #2
    //     0x8454e8: tst             x16, HEAP, lsr #32
    //     0x8454ec: b.eq            #0x8454f4
    //     0x8454f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8454f4: r1 = <HeirItem>
    //     0x8454f4: add             x1, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x8454f8: ldr             x1, [x1, #0x68]
    // 0x8454fc: r0 = AllocateGrowableArray()
    //     0x8454fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x845500: mov             x1, x0
    // 0x845504: ldur            x0, [fp, #-0x20]
    // 0x845508: StoreField: r1->field_f = r0
    //     0x845508: stur            w0, [x1, #0xf]
    // 0x84550c: r0 = 32
    //     0x84550c: movz            x0, #0x20
    // 0x845510: StoreField: r1->field_b = r0
    //     0x845510: stur            w0, [x1, #0xb]
    // 0x845514: r16 = <HeirItem>
    //     0x845514: add             x16, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x845518: ldr             x16, [x16, #0x68]
    // 0x84551c: stp             x1, x16, [SP]
    // 0x845520: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x845520: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x845524: r0 = ListExtension.obs()
    //     0x845524: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x845528: ldur            x1, [fp, #-8]
    // 0x84552c: StoreField: r1->field_27 = r0
    //     0x84552c: stur            w0, [x1, #0x27]
    //     0x845530: ldurb           w16, [x1, #-1]
    //     0x845534: ldurb           w17, [x0, #-1]
    //     0x845538: and             x16, x17, x16, lsr #2
    //     0x84553c: tst             x16, HEAP, lsr #32
    //     0x845540: b.eq            #0x845548
    //     0x845544: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x845548: r0 = HeirItem()
    //     0x845548: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x84554c: mov             x3, x0
    // 0x845550: r0 = Instance_Heir
    //     0x845550: add             x0, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x845554: ldr             x0, [x0, #0x950]
    // 0x845558: stur            x3, [fp, #-0x18]
    // 0x84555c: StoreField: r3->field_7 = r0
    //     0x84555c: stur            w0, [x3, #7]
    // 0x845560: r0 = 1
    //     0x845560: movz            x0, #0x1
    // 0x845564: StoreField: r3->field_b = r0
    //     0x845564: stur            x0, [x3, #0xb]
    // 0x845568: r4 = false
    //     0x845568: add             x4, NULL, #0x30  ; false
    // 0x84556c: StoreField: r3->field_13 = r4
    //     0x84556c: stur            w4, [x3, #0x13]
    // 0x845570: ArrayStore: r3[0] = r4  ; List_4
    //     0x845570: stur            w4, [x3, #0x17]
    // 0x845574: r1 = 8
    //     0x845574: movz            x1, #0x8
    // 0x845578: StoreField: r3->field_1b = r1
    //     0x845578: stur            w1, [x3, #0x1b]
    // 0x84557c: r1 = <HeirItem>
    //     0x84557c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x845580: ldr             x1, [x1, #0x68]
    // 0x845584: r2 = 20
    //     0x845584: movz            x2, #0x14
    // 0x845588: r0 = AllocateArray()
    //     0x845588: bl              #0xec22fc  ; AllocateArrayStub
    // 0x84558c: mov             x1, x0
    // 0x845590: ldur            x0, [fp, #-0x18]
    // 0x845594: stur            x1, [fp, #-0x20]
    // 0x845598: StoreField: r1->field_f = r0
    //     0x845598: stur            w0, [x1, #0xf]
    // 0x84559c: r0 = HeirItem()
    //     0x84559c: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8455a0: mov             x1, x0
    // 0x8455a4: r0 = Instance_Heir
    //     0x8455a4: add             x0, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x8455a8: ldr             x0, [x0, #0x7d8]
    // 0x8455ac: StoreField: r1->field_7 = r0
    //     0x8455ac: stur            w0, [x1, #7]
    // 0x8455b0: r2 = 1
    //     0x8455b0: movz            x2, #0x1
    // 0x8455b4: StoreField: r1->field_b = r2
    //     0x8455b4: stur            x2, [x1, #0xb]
    // 0x8455b8: r3 = false
    //     0x8455b8: add             x3, NULL, #0x30  ; false
    // 0x8455bc: StoreField: r1->field_13 = r3
    //     0x8455bc: stur            w3, [x1, #0x13]
    // 0x8455c0: ArrayStore: r1[0] = r3  ; List_4
    //     0x8455c0: stur            w3, [x1, #0x17]
    // 0x8455c4: mov             x0, x1
    // 0x8455c8: ldur            x1, [fp, #-0x20]
    // 0x8455cc: ArrayStore: r1[1] = r0  ; List_4
    //     0x8455cc: add             x25, x1, #0x13
    //     0x8455d0: str             w0, [x25]
    //     0x8455d4: tbz             w0, #0, #0x8455f0
    //     0x8455d8: ldurb           w16, [x1, #-1]
    //     0x8455dc: ldurb           w17, [x0, #-1]
    //     0x8455e0: and             x16, x17, x16, lsr #2
    //     0x8455e4: tst             x16, HEAP, lsr #32
    //     0x8455e8: b.eq            #0x8455f0
    //     0x8455ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8455f0: r0 = HeirItem()
    //     0x8455f0: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8455f4: mov             x1, x0
    // 0x8455f8: r0 = Instance_Heir
    //     0x8455f8: add             x0, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x8455fc: ldr             x0, [x0, #0x7e8]
    // 0x845600: StoreField: r1->field_7 = r0
    //     0x845600: stur            w0, [x1, #7]
    // 0x845604: r2 = 1
    //     0x845604: movz            x2, #0x1
    // 0x845608: StoreField: r1->field_b = r2
    //     0x845608: stur            x2, [x1, #0xb]
    // 0x84560c: r3 = false
    //     0x84560c: add             x3, NULL, #0x30  ; false
    // 0x845610: StoreField: r1->field_13 = r3
    //     0x845610: stur            w3, [x1, #0x13]
    // 0x845614: ArrayStore: r1[0] = r3  ; List_4
    //     0x845614: stur            w3, [x1, #0x17]
    // 0x845618: mov             x0, x1
    // 0x84561c: ldur            x1, [fp, #-0x20]
    // 0x845620: ArrayStore: r1[2] = r0  ; List_4
    //     0x845620: add             x25, x1, #0x17
    //     0x845624: str             w0, [x25]
    //     0x845628: tbz             w0, #0, #0x845644
    //     0x84562c: ldurb           w16, [x1, #-1]
    //     0x845630: ldurb           w17, [x0, #-1]
    //     0x845634: and             x16, x17, x16, lsr #2
    //     0x845638: tst             x16, HEAP, lsr #32
    //     0x84563c: b.eq            #0x845644
    //     0x845640: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845644: r0 = HeirItem()
    //     0x845644: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845648: mov             x1, x0
    // 0x84564c: r0 = Instance_Heir
    //     0x84564c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x845650: ldr             x0, [x0, #0x918]
    // 0x845654: StoreField: r1->field_7 = r0
    //     0x845654: stur            w0, [x1, #7]
    // 0x845658: r2 = 1
    //     0x845658: movz            x2, #0x1
    // 0x84565c: StoreField: r1->field_b = r2
    //     0x84565c: stur            x2, [x1, #0xb]
    // 0x845660: r3 = false
    //     0x845660: add             x3, NULL, #0x30  ; false
    // 0x845664: StoreField: r1->field_13 = r3
    //     0x845664: stur            w3, [x1, #0x13]
    // 0x845668: ArrayStore: r1[0] = r3  ; List_4
    //     0x845668: stur            w3, [x1, #0x17]
    // 0x84566c: r4 = 2
    //     0x84566c: movz            x4, #0x2
    // 0x845670: StoreField: r1->field_1b = r4
    //     0x845670: stur            w4, [x1, #0x1b]
    // 0x845674: mov             x0, x1
    // 0x845678: ldur            x1, [fp, #-0x20]
    // 0x84567c: ArrayStore: r1[3] = r0  ; List_4
    //     0x84567c: add             x25, x1, #0x1b
    //     0x845680: str             w0, [x25]
    //     0x845684: tbz             w0, #0, #0x8456a0
    //     0x845688: ldurb           w16, [x1, #-1]
    //     0x84568c: ldurb           w17, [x0, #-1]
    //     0x845690: and             x16, x17, x16, lsr #2
    //     0x845694: tst             x16, HEAP, lsr #32
    //     0x845698: b.eq            #0x8456a0
    //     0x84569c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8456a0: r0 = HeirItem()
    //     0x8456a0: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8456a4: mov             x1, x0
    // 0x8456a8: r0 = Instance_Heir
    //     0x8456a8: add             x0, PP, #0x31, lsl #12  ; [pp+0x318f8] Obj!Heir@e2d9c1
    //     0x8456ac: ldr             x0, [x0, #0x8f8]
    // 0x8456b0: StoreField: r1->field_7 = r0
    //     0x8456b0: stur            w0, [x1, #7]
    // 0x8456b4: r2 = 1
    //     0x8456b4: movz            x2, #0x1
    // 0x8456b8: StoreField: r1->field_b = r2
    //     0x8456b8: stur            x2, [x1, #0xb]
    // 0x8456bc: r3 = false
    //     0x8456bc: add             x3, NULL, #0x30  ; false
    // 0x8456c0: StoreField: r1->field_13 = r3
    //     0x8456c0: stur            w3, [x1, #0x13]
    // 0x8456c4: ArrayStore: r1[0] = r3  ; List_4
    //     0x8456c4: stur            w3, [x1, #0x17]
    // 0x8456c8: r4 = 2
    //     0x8456c8: movz            x4, #0x2
    // 0x8456cc: StoreField: r1->field_1b = r4
    //     0x8456cc: stur            w4, [x1, #0x1b]
    // 0x8456d0: mov             x0, x1
    // 0x8456d4: ldur            x1, [fp, #-0x20]
    // 0x8456d8: ArrayStore: r1[4] = r0  ; List_4
    //     0x8456d8: add             x25, x1, #0x1f
    //     0x8456dc: str             w0, [x25]
    //     0x8456e0: tbz             w0, #0, #0x8456fc
    //     0x8456e4: ldurb           w16, [x1, #-1]
    //     0x8456e8: ldurb           w17, [x0, #-1]
    //     0x8456ec: and             x16, x17, x16, lsr #2
    //     0x8456f0: tst             x16, HEAP, lsr #32
    //     0x8456f4: b.eq            #0x8456fc
    //     0x8456f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8456fc: r0 = HeirItem()
    //     0x8456fc: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845700: mov             x1, x0
    // 0x845704: r0 = Instance_Heir
    //     0x845704: add             x0, PP, #0x31, lsl #12  ; [pp+0x31908] Obj!Heir@e2d991
    //     0x845708: ldr             x0, [x0, #0x908]
    // 0x84570c: StoreField: r1->field_7 = r0
    //     0x84570c: stur            w0, [x1, #7]
    // 0x845710: r2 = 1
    //     0x845710: movz            x2, #0x1
    // 0x845714: StoreField: r1->field_b = r2
    //     0x845714: stur            x2, [x1, #0xb]
    // 0x845718: r3 = false
    //     0x845718: add             x3, NULL, #0x30  ; false
    // 0x84571c: StoreField: r1->field_13 = r3
    //     0x84571c: stur            w3, [x1, #0x13]
    // 0x845720: ArrayStore: r1[0] = r3  ; List_4
    //     0x845720: stur            w3, [x1, #0x17]
    // 0x845724: r0 = 2
    //     0x845724: movz            x0, #0x2
    // 0x845728: StoreField: r1->field_1b = r0
    //     0x845728: stur            w0, [x1, #0x1b]
    // 0x84572c: mov             x0, x1
    // 0x845730: ldur            x1, [fp, #-0x20]
    // 0x845734: ArrayStore: r1[5] = r0  ; List_4
    //     0x845734: add             x25, x1, #0x23
    //     0x845738: str             w0, [x25]
    //     0x84573c: tbz             w0, #0, #0x845758
    //     0x845740: ldurb           w16, [x1, #-1]
    //     0x845744: ldurb           w17, [x0, #-1]
    //     0x845748: and             x16, x17, x16, lsr #2
    //     0x84574c: tst             x16, HEAP, lsr #32
    //     0x845750: b.eq            #0x845758
    //     0x845754: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845758: r0 = HeirItem()
    //     0x845758: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x84575c: mov             x1, x0
    // 0x845760: r0 = Instance_Heir
    //     0x845760: add             x0, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x845764: ldr             x0, [x0, #0xa8]
    // 0x845768: StoreField: r1->field_7 = r0
    //     0x845768: stur            w0, [x1, #7]
    // 0x84576c: r2 = 1
    //     0x84576c: movz            x2, #0x1
    // 0x845770: StoreField: r1->field_b = r2
    //     0x845770: stur            x2, [x1, #0xb]
    // 0x845774: r3 = false
    //     0x845774: add             x3, NULL, #0x30  ; false
    // 0x845778: StoreField: r1->field_13 = r3
    //     0x845778: stur            w3, [x1, #0x13]
    // 0x84577c: ArrayStore: r1[0] = r3  ; List_4
    //     0x84577c: stur            w3, [x1, #0x17]
    // 0x845780: mov             x0, x1
    // 0x845784: ldur            x1, [fp, #-0x20]
    // 0x845788: ArrayStore: r1[6] = r0  ; List_4
    //     0x845788: add             x25, x1, #0x27
    //     0x84578c: str             w0, [x25]
    //     0x845790: tbz             w0, #0, #0x8457ac
    //     0x845794: ldurb           w16, [x1, #-1]
    //     0x845798: ldurb           w17, [x0, #-1]
    //     0x84579c: and             x16, x17, x16, lsr #2
    //     0x8457a0: tst             x16, HEAP, lsr #32
    //     0x8457a4: b.eq            #0x8457ac
    //     0x8457a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8457ac: r0 = HeirItem()
    //     0x8457ac: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x8457b0: mov             x1, x0
    // 0x8457b4: r0 = Instance_Heir
    //     0x8457b4: add             x0, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x8457b8: ldr             x0, [x0, #0xb0]
    // 0x8457bc: StoreField: r1->field_7 = r0
    //     0x8457bc: stur            w0, [x1, #7]
    // 0x8457c0: r2 = 1
    //     0x8457c0: movz            x2, #0x1
    // 0x8457c4: StoreField: r1->field_b = r2
    //     0x8457c4: stur            x2, [x1, #0xb]
    // 0x8457c8: r3 = false
    //     0x8457c8: add             x3, NULL, #0x30  ; false
    // 0x8457cc: StoreField: r1->field_13 = r3
    //     0x8457cc: stur            w3, [x1, #0x13]
    // 0x8457d0: ArrayStore: r1[0] = r3  ; List_4
    //     0x8457d0: stur            w3, [x1, #0x17]
    // 0x8457d4: mov             x0, x1
    // 0x8457d8: ldur            x1, [fp, #-0x20]
    // 0x8457dc: ArrayStore: r1[7] = r0  ; List_4
    //     0x8457dc: add             x25, x1, #0x2b
    //     0x8457e0: str             w0, [x25]
    //     0x8457e4: tbz             w0, #0, #0x845800
    //     0x8457e8: ldurb           w16, [x1, #-1]
    //     0x8457ec: ldurb           w17, [x0, #-1]
    //     0x8457f0: and             x16, x17, x16, lsr #2
    //     0x8457f4: tst             x16, HEAP, lsr #32
    //     0x8457f8: b.eq            #0x845800
    //     0x8457fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845800: r0 = HeirItem()
    //     0x845800: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845804: mov             x1, x0
    // 0x845808: r0 = Instance_Heir
    //     0x845808: add             x0, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x84580c: ldr             x0, [x0, #0xb8]
    // 0x845810: StoreField: r1->field_7 = r0
    //     0x845810: stur            w0, [x1, #7]
    // 0x845814: r2 = 1
    //     0x845814: movz            x2, #0x1
    // 0x845818: StoreField: r1->field_b = r2
    //     0x845818: stur            x2, [x1, #0xb]
    // 0x84581c: r3 = false
    //     0x84581c: add             x3, NULL, #0x30  ; false
    // 0x845820: StoreField: r1->field_13 = r3
    //     0x845820: stur            w3, [x1, #0x13]
    // 0x845824: ArrayStore: r1[0] = r3  ; List_4
    //     0x845824: stur            w3, [x1, #0x17]
    // 0x845828: mov             x0, x1
    // 0x84582c: ldur            x1, [fp, #-0x20]
    // 0x845830: ArrayStore: r1[8] = r0  ; List_4
    //     0x845830: add             x25, x1, #0x2f
    //     0x845834: str             w0, [x25]
    //     0x845838: tbz             w0, #0, #0x845854
    //     0x84583c: ldurb           w16, [x1, #-1]
    //     0x845840: ldurb           w17, [x0, #-1]
    //     0x845844: and             x16, x17, x16, lsr #2
    //     0x845848: tst             x16, HEAP, lsr #32
    //     0x84584c: b.eq            #0x845854
    //     0x845850: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x845854: r0 = HeirItem()
    //     0x845854: bl              #0x845938  ; AllocateHeirItemStub -> HeirItem (size=0x20)
    // 0x845858: mov             x1, x0
    // 0x84585c: r0 = Instance_Heir
    //     0x84585c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31960] Obj!Heir@e2d8d1
    //     0x845860: ldr             x0, [x0, #0x960]
    // 0x845864: StoreField: r1->field_7 = r0
    //     0x845864: stur            w0, [x1, #7]
    // 0x845868: r0 = 1
    //     0x845868: movz            x0, #0x1
    // 0x84586c: StoreField: r1->field_b = r0
    //     0x84586c: stur            x0, [x1, #0xb]
    // 0x845870: r0 = false
    //     0x845870: add             x0, NULL, #0x30  ; false
    // 0x845874: StoreField: r1->field_13 = r0
    //     0x845874: stur            w0, [x1, #0x13]
    // 0x845878: ArrayStore: r1[0] = r0  ; List_4
    //     0x845878: stur            w0, [x1, #0x17]
    // 0x84587c: mov             x0, x1
    // 0x845880: ldur            x1, [fp, #-0x20]
    // 0x845884: ArrayStore: r1[9] = r0  ; List_4
    //     0x845884: add             x25, x1, #0x33
    //     0x845888: str             w0, [x25]
    //     0x84588c: tbz             w0, #0, #0x8458a8
    //     0x845890: ldurb           w16, [x1, #-1]
    //     0x845894: ldurb           w17, [x0, #-1]
    //     0x845898: and             x16, x17, x16, lsr #2
    //     0x84589c: tst             x16, HEAP, lsr #32
    //     0x8458a0: b.eq            #0x8458a8
    //     0x8458a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8458a8: r1 = <HeirItem>
    //     0x8458a8: add             x1, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x8458ac: ldr             x1, [x1, #0x68]
    // 0x8458b0: r0 = AllocateGrowableArray()
    //     0x8458b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8458b4: mov             x1, x0
    // 0x8458b8: ldur            x0, [fp, #-0x20]
    // 0x8458bc: StoreField: r1->field_f = r0
    //     0x8458bc: stur            w0, [x1, #0xf]
    // 0x8458c0: r0 = 20
    //     0x8458c0: movz            x0, #0x14
    // 0x8458c4: StoreField: r1->field_b = r0
    //     0x8458c4: stur            w0, [x1, #0xb]
    // 0x8458c8: r16 = <HeirItem>
    //     0x8458c8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x8458cc: ldr             x16, [x16, #0x68]
    // 0x8458d0: stp             x1, x16, [SP]
    // 0x8458d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8458d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8458d8: r0 = ListExtension.obs()
    //     0x8458d8: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x8458dc: ldur            x1, [fp, #-8]
    // 0x8458e0: StoreField: r1->field_2b = r0
    //     0x8458e0: stur            w0, [x1, #0x2b]
    //     0x8458e4: ldurb           w16, [x1, #-1]
    //     0x8458e8: ldurb           w17, [x0, #-1]
    //     0x8458ec: and             x16, x17, x16, lsr #2
    //     0x8458f0: tst             x16, HEAP, lsr #32
    //     0x8458f4: b.eq            #0x8458fc
    //     0x8458f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8458fc: ldur            x0, [fp, #-0x10]
    // 0x845900: StoreField: r1->field_1f = r0
    //     0x845900: stur            w0, [x1, #0x1f]
    //     0x845904: ldurb           w16, [x1, #-1]
    //     0x845908: ldurb           w17, [x0, #-1]
    //     0x84590c: and             x16, x17, x16, lsr #2
    //     0x845910: tst             x16, HEAP, lsr #32
    //     0x845914: b.eq            #0x84591c
    //     0x845918: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x84591c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x84591c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x845920: r0 = Null
    //     0x845920: mov             x0, NULL
    // 0x845924: LeaveFrame
    //     0x845924: mov             SP, fp
    //     0x845928: ldp             fp, lr, [SP], #0x10
    // 0x84592c: ret
    //     0x84592c: ret             
    // 0x845930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x845930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x845934: b               #0x844f7c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x906d50, size: 0x90
    // 0x906d50: EnterFrame
    //     0x906d50: stp             fp, lr, [SP, #-0x10]!
    //     0x906d54: mov             fp, SP
    // 0x906d58: AllocStack(0x18)
    //     0x906d58: sub             SP, SP, #0x18
    // 0x906d5c: SetupParameters(WarisController this /* r1 => r1, fp-0x8 */)
    //     0x906d5c: stur            x1, [fp, #-8]
    // 0x906d60: CheckStackOverflow
    //     0x906d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x906d64: cmp             SP, x16
    //     0x906d68: b.ls            #0x906dd8
    // 0x906d6c: r1 = 1
    //     0x906d6c: movz            x1, #0x1
    // 0x906d70: r0 = AllocateContext()
    //     0x906d70: bl              #0xec126c  ; AllocateContextStub
    // 0x906d74: mov             x2, x0
    // 0x906d78: ldur            x0, [fp, #-8]
    // 0x906d7c: stur            x2, [fp, #-0x10]
    // 0x906d80: StoreField: r2->field_f = r0
    //     0x906d80: stur            w0, [x2, #0xf]
    // 0x906d84: mov             x1, x0
    // 0x906d88: r0 = onInit()
    //     0x906d88: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x906d8c: ldur            x0, [fp, #-8]
    // 0x906d90: LoadField: r3 = r0->field_23
    //     0x906d90: ldur            w3, [x0, #0x23]
    // 0x906d94: DecompressPointer r3
    //     0x906d94: add             x3, x3, HEAP, lsl #32
    // 0x906d98: ldur            x2, [fp, #-0x10]
    // 0x906d9c: stur            x3, [fp, #-0x18]
    // 0x906da0: r1 = Function '<anonymous closure>':.
    //     0x906da0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6f8] AnonymousClosure: (0x906de0), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::onInit (0x906d50)
    //     0x906da4: ldr             x1, [x1, #0x6f8]
    // 0x906da8: r0 = AllocateClosure()
    //     0x906da8: bl              #0xec1630  ; AllocateClosureStub
    // 0x906dac: ldur            x1, [fp, #-0x18]
    // 0x906db0: mov             x2, x0
    // 0x906db4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x906db4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x906db8: r0 = listen()
    //     0x906db8: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x906dbc: ldur            x1, [fp, #-0x18]
    // 0x906dc0: r2 = true
    //     0x906dc0: add             x2, NULL, #0x20  ; true
    // 0x906dc4: r0 = trigger()
    //     0x906dc4: bl              #0x8f4b10  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::trigger
    // 0x906dc8: r0 = Null
    //     0x906dc8: mov             x0, NULL
    // 0x906dcc: LeaveFrame
    //     0x906dcc: mov             SP, fp
    //     0x906dd0: ldp             fp, lr, [SP], #0x10
    // 0x906dd4: ret
    //     0x906dd4: ret             
    // 0x906dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906dd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906ddc: b               #0x906d6c
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x906de0, size: 0x100
    // 0x906de0: EnterFrame
    //     0x906de0: stp             fp, lr, [SP, #-0x10]!
    //     0x906de4: mov             fp, SP
    // 0x906de8: AllocStack(0x18)
    //     0x906de8: sub             SP, SP, #0x18
    // 0x906dec: SetupParameters()
    //     0x906dec: ldr             x0, [fp, #0x18]
    //     0x906df0: ldur            w2, [x0, #0x17]
    //     0x906df4: add             x2, x2, HEAP, lsl #32
    //     0x906df8: stur            x2, [fp, #-0x10]
    // 0x906dfc: CheckStackOverflow
    //     0x906dfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x906e00: cmp             SP, x16
    //     0x906e04: b.ls            #0x906ed8
    // 0x906e08: LoadField: r0 = r2->field_f
    //     0x906e08: ldur            w0, [x2, #0xf]
    // 0x906e0c: DecompressPointer r0
    //     0x906e0c: add             x0, x0, HEAP, lsl #32
    // 0x906e10: LoadField: r3 = r0->field_27
    //     0x906e10: ldur            w3, [x0, #0x27]
    // 0x906e14: DecompressPointer r3
    //     0x906e14: add             x3, x3, HEAP, lsl #32
    // 0x906e18: mov             x1, x3
    // 0x906e1c: stur            x3, [fp, #-8]
    // 0x906e20: r0 = first()
    //     0x906e20: bl              #0x89303c  ; [dart:collection] ListBase::first
    // 0x906e24: ldr             x16, [fp, #0x10]
    // 0x906e28: str             x16, [SP]
    // 0x906e2c: mov             x1, x0
    // 0x906e30: r4 = const [0, 0x2, 0x1, 0x1, disabled, 0x1, null]
    //     0x906e30: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c700] List(7) [0, 0x2, 0x1, 0x1, "disabled", 0x1, Null]
    //     0x906e34: ldr             x4, [x4, #0x700]
    // 0x906e38: r0 = copyWith()
    //     0x906e38: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0x906e3c: ldur            x1, [fp, #-8]
    // 0x906e40: mov             x2, x0
    // 0x906e44: r0 = first=()
    //     0x906e44: bl              #0x906ee0  ; [dart:collection] ListBase::first=
    // 0x906e48: ldur            x0, [fp, #-0x10]
    // 0x906e4c: LoadField: r1 = r0->field_f
    //     0x906e4c: ldur            w1, [x0, #0xf]
    // 0x906e50: DecompressPointer r1
    //     0x906e50: add             x1, x1, HEAP, lsl #32
    // 0x906e54: LoadField: r2 = r1->field_2b
    //     0x906e54: ldur            w2, [x1, #0x2b]
    // 0x906e58: DecompressPointer r2
    //     0x906e58: add             x2, x2, HEAP, lsl #32
    // 0x906e5c: mov             x1, x2
    // 0x906e60: stur            x2, [fp, #-8]
    // 0x906e64: r0 = first()
    //     0x906e64: bl              #0x89303c  ; [dart:collection] ListBase::first
    // 0x906e68: mov             x1, x0
    // 0x906e6c: ldr             x0, [fp, #0x10]
    // 0x906e70: eor             x2, x0, #0x10
    // 0x906e74: str             x2, [SP]
    // 0x906e78: r4 = const [0, 0x2, 0x1, 0x1, disabled, 0x1, null]
    //     0x906e78: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c700] List(7) [0, 0x2, 0x1, 0x1, "disabled", 0x1, Null]
    //     0x906e7c: ldr             x4, [x4, #0x700]
    // 0x906e80: r0 = copyWith()
    //     0x906e80: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0x906e84: ldur            x1, [fp, #-8]
    // 0x906e88: mov             x2, x0
    // 0x906e8c: r0 = first=()
    //     0x906e8c: bl              #0x906ee0  ; [dart:collection] ListBase::first=
    // 0x906e90: ldur            x0, [fp, #-0x10]
    // 0x906e94: LoadField: r1 = r0->field_f
    //     0x906e94: ldur            w1, [x0, #0xf]
    // 0x906e98: DecompressPointer r1
    //     0x906e98: add             x1, x1, HEAP, lsl #32
    // 0x906e9c: LoadField: r2 = r1->field_27
    //     0x906e9c: ldur            w2, [x1, #0x27]
    // 0x906ea0: DecompressPointer r2
    //     0x906ea0: add             x2, x2, HEAP, lsl #32
    // 0x906ea4: mov             x1, x2
    // 0x906ea8: r0 = refresh()
    //     0x906ea8: bl              #0x6680f4  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::refresh
    // 0x906eac: ldur            x0, [fp, #-0x10]
    // 0x906eb0: LoadField: r1 = r0->field_f
    //     0x906eb0: ldur            w1, [x0, #0xf]
    // 0x906eb4: DecompressPointer r1
    //     0x906eb4: add             x1, x1, HEAP, lsl #32
    // 0x906eb8: LoadField: r0 = r1->field_2b
    //     0x906eb8: ldur            w0, [x1, #0x2b]
    // 0x906ebc: DecompressPointer r0
    //     0x906ebc: add             x0, x0, HEAP, lsl #32
    // 0x906ec0: mov             x1, x0
    // 0x906ec4: r0 = refresh()
    //     0x906ec4: bl              #0x6680f4  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::refresh
    // 0x906ec8: r0 = Null
    //     0x906ec8: mov             x0, NULL
    // 0x906ecc: LeaveFrame
    //     0x906ecc: mov             SP, fp
    //     0x906ed0: ldp             fp, lr, [SP], #0x10
    // 0x906ed4: ret
    //     0x906ed4: ret             
    // 0x906ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906ed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906edc: b               #0x906e08
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9221a8, size: 0x58
    // 0x9221a8: EnterFrame
    //     0x9221a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9221ac: mov             fp, SP
    // 0x9221b0: AllocStack(0x8)
    //     0x9221b0: sub             SP, SP, #8
    // 0x9221b4: CheckStackOverflow
    //     0x9221b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9221b8: cmp             SP, x16
    //     0x9221bc: b.ls            #0x9221f8
    // 0x9221c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x9221c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9221c4: ldr             x0, [x0, #0x2670]
    //     0x9221c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9221cc: cmp             w0, w16
    //     0x9221d0: b.ne            #0x9221dc
    //     0x9221d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x9221d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9221dc: str             NULL, [SP]
    // 0x9221e0: r4 = const [0x1, 0, 0, 0, null]
    //     0x9221e0: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0x9221e4: r0 = GetNavigation.back()
    //     0x9221e4: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x9221e8: r0 = Null
    //     0x9221e8: mov             x0, NULL
    // 0x9221ec: LeaveFrame
    //     0x9221ec: mov             SP, fp
    //     0x9221f0: ldp             fp, lr, [SP], #0x10
    // 0x9221f4: ret
    //     0x9221f4: ret             
    // 0x9221f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9221f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9221fc: b               #0x9221c0
  }
  _ calculate(/* No info */) {
    // ** addr: 0x922200, size: 0x3f8
    // 0x922200: EnterFrame
    //     0x922200: stp             fp, lr, [SP, #-0x10]!
    //     0x922204: mov             fp, SP
    // 0x922208: AllocStack(0x40)
    //     0x922208: sub             SP, SP, #0x40
    // 0x92220c: SetupParameters(WarisController this /* r1 => r1, fp-0x8 */)
    //     0x92220c: stur            x1, [fp, #-8]
    // 0x922210: CheckStackOverflow
    //     0x922210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922214: cmp             SP, x16
    //     0x922218: b.ls            #0x9225e0
    // 0x92221c: r16 = <Heir, int>
    //     0x92221c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28060] TypeArguments: <Heir, int>
    //     0x922220: ldr             x16, [x16, #0x60]
    // 0x922224: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x922228: stp             lr, x16, [SP]
    // 0x92222c: r0 = Map._fromLiteral()
    //     0x92222c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x922230: mov             x3, x0
    // 0x922234: ldur            x0, [fp, #-8]
    // 0x922238: stur            x3, [fp, #-0x10]
    // 0x92223c: LoadField: r2 = r0->field_27
    //     0x92223c: ldur            w2, [x0, #0x27]
    // 0x922240: DecompressPointer r2
    //     0x922240: add             x2, x2, HEAP, lsl #32
    // 0x922244: r1 = <HeirItem>
    //     0x922244: add             x1, PP, #0x28, lsl #12  ; [pp+0x28068] TypeArguments: <HeirItem>
    //     0x922248: ldr             x1, [x1, #0x68]
    // 0x92224c: r0 = LinkedHashSet.of()
    //     0x92224c: bl              #0x64d534  ; [dart:collection] LinkedHashSet::LinkedHashSet.of
    // 0x922250: mov             x3, x0
    // 0x922254: ldur            x0, [fp, #-8]
    // 0x922258: stur            x3, [fp, #-0x18]
    // 0x92225c: LoadField: r2 = r0->field_2b
    //     0x92225c: ldur            w2, [x0, #0x2b]
    // 0x922260: DecompressPointer r2
    //     0x922260: add             x2, x2, HEAP, lsl #32
    // 0x922264: mov             x1, x3
    // 0x922268: r0 = addAll()
    //     0x922268: bl              #0x69b32c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x92226c: r1 = Function '<anonymous closure>':.
    //     0x92226c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28070] AnonymousClosure: (0x9225f8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0x922270: ldr             x1, [x1, #0x70]
    // 0x922274: r2 = Null
    //     0x922274: mov             x2, NULL
    // 0x922278: r0 = AllocateClosure()
    //     0x922278: bl              #0xec1630  ; AllocateClosureStub
    // 0x92227c: ldur            x1, [fp, #-0x18]
    // 0x922280: mov             x2, x0
    // 0x922284: r0 = where()
    //     0x922284: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x922288: mov             x1, x0
    // 0x92228c: r0 = iterator()
    //     0x92228c: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0x922290: LoadField: r2 = r0->field_b
    //     0x922290: ldur            w2, [x0, #0xb]
    // 0x922294: DecompressPointer r2
    //     0x922294: add             x2, x2, HEAP, lsl #32
    // 0x922298: stur            x2, [fp, #-0x18]
    // 0x92229c: LoadField: r3 = r0->field_f
    //     0x92229c: ldur            w3, [x0, #0xf]
    // 0x9222a0: DecompressPointer r3
    //     0x9222a0: add             x3, x3, HEAP, lsl #32
    // 0x9222a4: stur            x3, [fp, #-8]
    // 0x9222a8: CheckStackOverflow
    //     0x9222a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9222ac: cmp             SP, x16
    //     0x9222b0: b.ls            #0x9225e8
    // 0x9222b4: CheckStackOverflow
    //     0x9222b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9222b8: cmp             SP, x16
    //     0x9222bc: b.ls            #0x9225f0
    // 0x9222c0: r0 = LoadClassIdInstr(r2)
    //     0x9222c0: ldur            x0, [x2, #-1]
    //     0x9222c4: ubfx            x0, x0, #0xc, #0x14
    // 0x9222c8: mov             x1, x2
    // 0x9222cc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x9222cc: movz            x17, #0x292d
    //     0x9222d0: movk            x17, #0x1, lsl #16
    //     0x9222d4: add             lr, x0, x17
    //     0x9222d8: ldr             lr, [x21, lr, lsl #3]
    //     0x9222dc: blr             lr
    // 0x9222e0: tbnz            w0, #4, #0x9223b4
    // 0x9222e4: ldur            x2, [fp, #-0x18]
    // 0x9222e8: r0 = LoadClassIdInstr(r2)
    //     0x9222e8: ldur            x0, [x2, #-1]
    //     0x9222ec: ubfx            x0, x0, #0xc, #0x14
    // 0x9222f0: mov             x1, x2
    // 0x9222f4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x9222f4: movz            x17, #0x384d
    //     0x9222f8: movk            x17, #0x1, lsl #16
    //     0x9222fc: add             lr, x0, x17
    //     0x922300: ldr             lr, [x21, lr, lsl #3]
    //     0x922304: blr             lr
    // 0x922308: ldur            x16, [fp, #-8]
    // 0x92230c: stp             x0, x16, [SP]
    // 0x922310: ldur            x0, [fp, #-8]
    // 0x922314: ClosureCall
    //     0x922314: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x922318: ldur            x2, [x0, #0x1f]
    //     0x92231c: blr             x2
    // 0x922320: r16 = true
    //     0x922320: add             x16, NULL, #0x20  ; true
    // 0x922324: cmp             w0, w16
    // 0x922328: b.eq            #0x922338
    // 0x92232c: ldur            x2, [fp, #-0x18]
    // 0x922330: ldur            x3, [fp, #-8]
    // 0x922334: b               #0x9222b4
    // 0x922338: ldur            x2, [fp, #-0x18]
    // 0x92233c: r0 = LoadClassIdInstr(r2)
    //     0x92233c: ldur            x0, [x2, #-1]
    //     0x922340: ubfx            x0, x0, #0xc, #0x14
    // 0x922344: mov             x1, x2
    // 0x922348: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x922348: movz            x17, #0x384d
    //     0x92234c: movk            x17, #0x1, lsl #16
    //     0x922350: add             lr, x0, x17
    //     0x922354: ldr             lr, [x21, lr, lsl #3]
    //     0x922358: blr             lr
    // 0x92235c: LoadField: r2 = r0->field_7
    //     0x92235c: ldur            w2, [x0, #7]
    // 0x922360: DecompressPointer r2
    //     0x922360: add             x2, x2, HEAP, lsl #32
    // 0x922364: stur            x2, [fp, #-0x28]
    // 0x922368: LoadField: r1 = r0->field_b
    //     0x922368: ldur            x1, [x0, #0xb]
    // 0x92236c: stur            x1, [fp, #-0x20]
    // 0x922370: str             x2, [SP]
    // 0x922374: r0 = _getHash()
    //     0x922374: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x922378: mov             x3, x0
    // 0x92237c: ldur            x2, [fp, #-0x20]
    // 0x922380: r0 = BoxInt64Instr(r2)
    //     0x922380: sbfiz           x0, x2, #1, #0x1f
    //     0x922384: cmp             x2, x0, asr #1
    //     0x922388: b.eq            #0x922394
    //     0x92238c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x922390: stur            x2, [x0, #7]
    // 0x922394: r5 = LoadInt32Instr(r3)
    //     0x922394: sbfx            x5, x3, #1, #0x1f
    // 0x922398: ldur            x1, [fp, #-0x10]
    // 0x92239c: ldur            x2, [fp, #-0x28]
    // 0x9223a0: mov             x3, x0
    // 0x9223a4: r0 = _set()
    //     0x9223a4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x9223a8: ldur            x2, [fp, #-0x18]
    // 0x9223ac: ldur            x3, [fp, #-8]
    // 0x9223b0: b               #0x9222a8
    // 0x9223b4: ldur            x0, [fp, #-0x10]
    // 0x9223b8: LoadField: r1 = r0->field_13
    //     0x9223b8: ldur            w1, [x0, #0x13]
    // 0x9223bc: r2 = LoadInt32Instr(r1)
    //     0x9223bc: sbfx            x2, x1, #1, #0x1f
    // 0x9223c0: asr             x1, x2, #1
    // 0x9223c4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9223c4: ldur            w2, [x0, #0x17]
    // 0x9223c8: r3 = LoadInt32Instr(r2)
    //     0x9223c8: sbfx            x3, x2, #1, #0x1f
    // 0x9223cc: sub             x2, x1, x3
    // 0x9223d0: cbnz            x2, #0x9223ec
    // 0x9223d4: r1 = "Ahli waris tidak boleh kosong"
    //     0x9223d4: add             x1, PP, #0x28, lsl #12  ; [pp+0x28078] "Ahli waris tidak boleh kosong"
    //     0x9223d8: ldr             x1, [x1, #0x78]
    // 0x9223dc: r2 = Instance_IconData
    //     0x9223dc: add             x2, PP, #0x28, lsl #12  ; [pp+0x28080] Obj!IconData@e0fe91
    //     0x9223e0: ldr             x2, [x2, #0x80]
    // 0x9223e4: r0 = show()
    //     0x9223e4: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0x9223e8: b               #0x9225d0
    // 0x9223ec: mov             x1, x0
    // 0x9223f0: r2 = Instance_Heir
    //     0x9223f0: add             x2, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x9223f4: ldr             x2, [x2, #0x88]
    // 0x9223f8: r0 = HeirsExt.has()
    //     0x9223f8: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x9223fc: tbnz            w0, #4, #0x922594
    // 0x922400: r0 = 12
    //     0x922400: movz            x0, #0xc
    // 0x922404: mov             x2, x0
    // 0x922408: r1 = Null
    //     0x922408: mov             x1, NULL
    // 0x92240c: r0 = AllocateArray()
    //     0x92240c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x922410: stur            x0, [fp, #-8]
    // 0x922414: r16 = Instance_Heir
    //     0x922414: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x922418: ldr             x16, [x16, #0x90]
    // 0x92241c: StoreField: r0->field_f = r16
    //     0x92241c: stur            w16, [x0, #0xf]
    // 0x922420: r16 = Instance_Heir
    //     0x922420: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x922424: ldr             x16, [x16, #0x98]
    // 0x922428: StoreField: r0->field_13 = r16
    //     0x922428: stur            w16, [x0, #0x13]
    // 0x92242c: r16 = Instance_Heir
    //     0x92242c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x922430: ldr             x16, [x16, #0xa0]
    // 0x922434: ArrayStore: r0[0] = r16  ; List_4
    //     0x922434: stur            w16, [x0, #0x17]
    // 0x922438: r16 = Instance_Heir
    //     0x922438: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x92243c: ldr             x16, [x16, #0xa8]
    // 0x922440: StoreField: r0->field_1b = r16
    //     0x922440: stur            w16, [x0, #0x1b]
    // 0x922444: r16 = Instance_Heir
    //     0x922444: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x922448: ldr             x16, [x16, #0xb0]
    // 0x92244c: StoreField: r0->field_1f = r16
    //     0x92244c: stur            w16, [x0, #0x1f]
    // 0x922450: r16 = Instance_Heir
    //     0x922450: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x922454: ldr             x16, [x16, #0xb8]
    // 0x922458: StoreField: r0->field_23 = r16
    //     0x922458: stur            w16, [x0, #0x23]
    // 0x92245c: r1 = <Heir>
    //     0x92245c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x922460: ldr             x1, [x1, #0xc0]
    // 0x922464: r0 = AllocateGrowableArray()
    //     0x922464: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x922468: mov             x1, x0
    // 0x92246c: ldur            x0, [fp, #-8]
    // 0x922470: StoreField: r1->field_f = r0
    //     0x922470: stur            w0, [x1, #0xf]
    // 0x922474: r0 = 12
    //     0x922474: movz            x0, #0xc
    // 0x922478: StoreField: r1->field_b = r0
    //     0x922478: stur            w0, [x1, #0xb]
    // 0x92247c: mov             x2, x1
    // 0x922480: ldur            x1, [fp, #-0x10]
    // 0x922484: r0 = HeirsExt.hasAnyOf()
    //     0x922484: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x922488: tbnz            w0, #4, #0x922594
    // 0x92248c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x92248c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x922490: ldr             x0, [x0, #0x2670]
    //     0x922494: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x922498: cmp             w0, w16
    //     0x92249c: b.ne            #0x9224a8
    //     0x9224a0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x9224a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9224a8: r0 = GetNavigation.textTheme()
    //     0x9224a8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0x9224ac: LoadField: r1 = r0->field_27
    //     0x9224ac: ldur            w1, [x0, #0x27]
    // 0x9224b0: DecompressPointer r1
    //     0x9224b0: add             x1, x1, HEAP, lsl #32
    // 0x9224b4: cmp             w1, NULL
    // 0x9224b8: b.ne            #0x9224c4
    // 0x9224bc: r0 = Null
    //     0x9224bc: mov             x0, NULL
    // 0x9224c0: b               #0x9224dc
    // 0x9224c4: r16 = 14.000000
    //     0x9224c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0x9224c8: ldr             x16, [x16, #0x9a0]
    // 0x9224cc: str             x16, [SP]
    // 0x9224d0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x9224d0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x9224d4: ldr             x4, [x4, #0x88]
    // 0x9224d8: r0 = copyWith()
    //     0x9224d8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9224dc: stur            x0, [fp, #-8]
    // 0x9224e0: r0 = Text()
    //     0x9224e0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0x9224e4: mov             x3, x0
    // 0x9224e8: r0 = "Masalah waris Jadd wal Ikhwah (kakek dan saudara) masih dalam pengembangan."
    //     0x9224e8: add             x0, PP, #0x28, lsl #12  ; [pp+0x280c8] "Masalah waris Jadd wal Ikhwah (kakek dan saudara) masih dalam pengembangan."
    //     0x9224ec: ldr             x0, [x0, #0xc8]
    // 0x9224f0: stur            x3, [fp, #-0x18]
    // 0x9224f4: StoreField: r3->field_b = r0
    //     0x9224f4: stur            w0, [x3, #0xb]
    // 0x9224f8: ldur            x0, [fp, #-8]
    // 0x9224fc: StoreField: r3->field_13 = r0
    //     0x9224fc: stur            w0, [x3, #0x13]
    // 0x922500: r1 = Null
    //     0x922500: mov             x1, NULL
    // 0x922504: r2 = 2
    //     0x922504: movz            x2, #0x2
    // 0x922508: r0 = AllocateArray()
    //     0x922508: bl              #0xec22fc  ; AllocateArrayStub
    // 0x92250c: mov             x2, x0
    // 0x922510: ldur            x0, [fp, #-0x18]
    // 0x922514: stur            x2, [fp, #-8]
    // 0x922518: StoreField: r2->field_f = r0
    //     0x922518: stur            w0, [x2, #0xf]
    // 0x92251c: r1 = <Widget>
    //     0x92251c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x922520: r0 = AllocateGrowableArray()
    //     0x922520: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x922524: mov             x1, x0
    // 0x922528: ldur            x0, [fp, #-8]
    // 0x92252c: stur            x1, [fp, #-0x18]
    // 0x922530: StoreField: r1->field_f = r0
    //     0x922530: stur            w0, [x1, #0xf]
    // 0x922534: r0 = 2
    //     0x922534: movz            x0, #0x2
    // 0x922538: StoreField: r1->field_b = r0
    //     0x922538: stur            w0, [x1, #0xb]
    // 0x92253c: r0 = NDialog()
    //     0x92253c: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0x922540: mov             x3, x0
    // 0x922544: r0 = "Informasi"
    //     0x922544: add             x0, PP, #0x28, lsl #12  ; [pp+0x280d0] "Informasi"
    //     0x922548: ldr             x0, [x0, #0xd0]
    // 0x92254c: stur            x3, [fp, #-8]
    // 0x922550: StoreField: r3->field_b = r0
    //     0x922550: stur            w0, [x3, #0xb]
    // 0x922554: r1 = Function '<anonymous closure>':.
    //     0x922554: add             x1, PP, #0x28, lsl #12  ; [pp+0x280d8] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0x922558: ldr             x1, [x1, #0xd8]
    // 0x92255c: r2 = Null
    //     0x92255c: mov             x2, NULL
    // 0x922560: r0 = AllocateClosure()
    //     0x922560: bl              #0xec1630  ; AllocateClosureStub
    // 0x922564: mov             x1, x0
    // 0x922568: ldur            x0, [fp, #-8]
    // 0x92256c: ArrayStore: r0[0] = r1  ; List_4
    //     0x92256c: stur            w1, [x0, #0x17]
    // 0x922570: ldur            x1, [fp, #-0x18]
    // 0x922574: StoreField: r0->field_f = r1
    //     0x922574: stur            w1, [x0, #0xf]
    // 0x922578: r1 = "Mengerti"
    //     0x922578: add             x1, PP, #0x28, lsl #12  ; [pp+0x280e0] "Mengerti"
    //     0x92257c: ldr             x1, [x1, #0xe0]
    // 0x922580: StoreField: r0->field_13 = r1
    //     0x922580: stur            w1, [x0, #0x13]
    // 0x922584: stp             x0, NULL, [SP]
    // 0x922588: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x922588: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92258c: r0 = ExtensionDialog.dialog()
    //     0x92258c: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0x922590: b               #0x9225d0
    // 0x922594: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x922594: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x922598: ldr             x0, [x0, #0x2670]
    //     0x92259c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9225a0: cmp             w0, w16
    //     0x9225a4: b.ne            #0x9225b0
    //     0x9225a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x9225ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9225b0: r16 = "/waris/waris-result"
    //     0x9225b0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280e8] "/waris/waris-result"
    //     0x9225b4: ldr             x16, [x16, #0xe8]
    // 0x9225b8: stp             x16, NULL, [SP, #8]
    // 0x9225bc: ldur            x16, [fp, #-0x10]
    // 0x9225c0: str             x16, [SP]
    // 0x9225c4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x9225c4: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x9225c8: ldr             x4, [x4, #0x478]
    // 0x9225cc: r0 = GetNavigation.toNamed()
    //     0x9225cc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x9225d0: r0 = Null
    //     0x9225d0: mov             x0, NULL
    // 0x9225d4: LeaveFrame
    //     0x9225d4: mov             SP, fp
    //     0x9225d8: ldp             fp, lr, [SP], #0x10
    // 0x9225dc: ret
    //     0x9225dc: ret             
    // 0x9225e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9225e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9225e4: b               #0x92221c
    // 0x9225e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9225e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9225ec: b               #0x9222b4
    // 0x9225f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9225f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9225f4: b               #0x9222c0
  }
  [closure] bool <anonymous closure>(dynamic, HeirItem) {
    // ** addr: 0x9225f8, size: 0x28
    // 0x9225f8: ldr             x1, [SP]
    // 0x9225fc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9225fc: ldur            w2, [x1, #0x17]
    // 0x922600: DecompressPointer r2
    //     0x922600: add             x2, x2, HEAP, lsl #32
    // 0x922604: tbz             w2, #4, #0x922618
    // 0x922608: LoadField: r2 = r1->field_13
    //     0x922608: ldur            w2, [x1, #0x13]
    // 0x92260c: DecompressPointer r2
    //     0x92260c: add             x2, x2, HEAP, lsl #32
    // 0x922610: mov             x0, x2
    // 0x922614: b               #0x92261c
    // 0x922618: r0 = false
    //     0x922618: add             x0, NULL, #0x30  ; false
    // 0x92261c: ret
    //     0x92261c: ret             
  }
  [closure] void calculate(dynamic) {
    // ** addr: 0x922620, size: 0x38
    // 0x922620: EnterFrame
    //     0x922620: stp             fp, lr, [SP, #-0x10]!
    //     0x922624: mov             fp, SP
    // 0x922628: ldr             x0, [fp, #0x10]
    // 0x92262c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x92262c: ldur            w1, [x0, #0x17]
    // 0x922630: DecompressPointer r1
    //     0x922630: add             x1, x1, HEAP, lsl #32
    // 0x922634: CheckStackOverflow
    //     0x922634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922638: cmp             SP, x16
    //     0x92263c: b.ls            #0x922650
    // 0x922640: r0 = calculate()
    //     0x922640: bl              #0x922200  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate
    // 0x922644: LeaveFrame
    //     0x922644: mov             SP, fp
    //     0x922648: ldp             fp, lr, [SP], #0x10
    // 0x92264c: ret
    //     0x92264c: ret             
    // 0x922650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922650: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922654: b               #0x922640
  }
  _ onReady(/* No info */) {
    // ** addr: 0x925b38, size: 0x80
    // 0x925b38: EnterFrame
    //     0x925b38: stp             fp, lr, [SP, #-0x10]!
    //     0x925b3c: mov             fp, SP
    // 0x925b40: AllocStack(0x18)
    //     0x925b40: sub             SP, SP, #0x18
    // 0x925b44: SetupParameters(WarisController this /* r1 => r1, fp-0x8 */)
    //     0x925b44: stur            x1, [fp, #-8]
    // 0x925b48: CheckStackOverflow
    //     0x925b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925b4c: cmp             SP, x16
    //     0x925b50: b.ls            #0x925bb0
    // 0x925b54: r1 = 1
    //     0x925b54: movz            x1, #0x1
    // 0x925b58: r0 = AllocateContext()
    //     0x925b58: bl              #0xec126c  ; AllocateContextStub
    // 0x925b5c: mov             x2, x0
    // 0x925b60: ldur            x0, [fp, #-8]
    // 0x925b64: stur            x2, [fp, #-0x10]
    // 0x925b68: StoreField: r2->field_f = r0
    //     0x925b68: stur            w0, [x2, #0xf]
    // 0x925b6c: LoadField: r1 = r0->field_1f
    //     0x925b6c: ldur            w1, [x0, #0x1f]
    // 0x925b70: DecompressPointer r1
    //     0x925b70: add             x1, x1, HEAP, lsl #32
    // 0x925b74: r0 = isFirstOpenWaris()
    //     0x925b74: bl              #0x925bb8  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isFirstOpenWaris
    // 0x925b78: tbnz            w0, #4, #0x925ba0
    // 0x925b7c: ldur            x2, [fp, #-0x10]
    // 0x925b80: r1 = Function '<anonymous closure>':.
    //     0x925b80: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6c8] AnonymousClosure: (0x925c78), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::onReady (0x925b38)
    //     0x925b84: ldr             x1, [x1, #0x6c8]
    // 0x925b88: r0 = AllocateClosure()
    //     0x925b88: bl              #0xec1630  ; AllocateClosureStub
    // 0x925b8c: str             x0, [SP]
    // 0x925b90: r1 = <Null?>
    //     0x925b90: ldr             x1, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x925b94: r2 = Instance_Duration
    //     0x925b94: ldr             x2, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0x925b98: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x925b98: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x925b9c: r0 = Future.delayed()
    //     0x925b9c: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0x925ba0: r0 = Null
    //     0x925ba0: mov             x0, NULL
    // 0x925ba4: LeaveFrame
    //     0x925ba4: mov             SP, fp
    //     0x925ba8: ldp             fp, lr, [SP], #0x10
    // 0x925bac: ret
    //     0x925bac: ret             
    // 0x925bb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925bb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925bb4: b               #0x925b54
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x925c78, size: 0xa0
    // 0x925c78: EnterFrame
    //     0x925c78: stp             fp, lr, [SP, #-0x10]!
    //     0x925c7c: mov             fp, SP
    // 0x925c80: AllocStack(0x28)
    //     0x925c80: sub             SP, SP, #0x28
    // 0x925c84: SetupParameters()
    //     0x925c84: ldr             x0, [fp, #0x10]
    //     0x925c88: ldur            w2, [x0, #0x17]
    //     0x925c8c: add             x2, x2, HEAP, lsl #32
    //     0x925c90: stur            x2, [fp, #-8]
    // 0x925c94: CheckStackOverflow
    //     0x925c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925c98: cmp             SP, x16
    //     0x925c9c: b.ls            #0x925d10
    // 0x925ca0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x925ca0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x925ca4: ldr             x0, [x0, #0x2670]
    //     0x925ca8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x925cac: cmp             w0, w16
    //     0x925cb0: b.ne            #0x925cbc
    //     0x925cb4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x925cb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x925cbc: r1 = Null
    //     0x925cbc: mov             x1, NULL
    // 0x925cc0: r0 = WarisInfoView.bottomSheet()
    //     0x925cc0: bl              #0x925d18  ; [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::WarisInfoView.bottomSheet
    // 0x925cc4: stp             x0, NULL, [SP, #0x10]
    // 0x925cc8: r16 = false
    //     0x925cc8: add             x16, NULL, #0x30  ; false
    // 0x925ccc: r30 = true
    //     0x925ccc: add             lr, NULL, #0x20  ; true
    // 0x925cd0: stp             lr, x16, [SP]
    // 0x925cd4: r4 = const [0x1, 0x3, 0x3, 0x1, isDismissible, 0x1, isScrollControlled, 0x2, null]
    //     0x925cd4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c6d0] List(9) [0x1, 0x3, 0x3, 0x1, "isDismissible", 0x1, "isScrollControlled", 0x2, Null]
    //     0x925cd8: ldr             x4, [x4, #0x6d0]
    // 0x925cdc: r0 = ExtensionBottomSheet.bottomSheet()
    //     0x925cdc: bl              #0x917724  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionBottomSheet.bottomSheet
    // 0x925ce0: ldur            x2, [fp, #-8]
    // 0x925ce4: r1 = Function '<anonymous closure>':.
    //     0x925ce4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6d8] AnonymousClosure: (0x9260ec), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::onReady (0x925b38)
    //     0x925ce8: ldr             x1, [x1, #0x6d8]
    // 0x925cec: stur            x0, [fp, #-8]
    // 0x925cf0: r0 = AllocateClosure()
    //     0x925cf0: bl              #0xec1630  ; AllocateClosureStub
    // 0x925cf4: ldur            x1, [fp, #-8]
    // 0x925cf8: mov             x2, x0
    // 0x925cfc: r0 = whenComplete()
    //     0x925cfc: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0x925d00: r0 = Null
    //     0x925d00: mov             x0, NULL
    // 0x925d04: LeaveFrame
    //     0x925d04: mov             SP, fp
    //     0x925d08: ldp             fp, lr, [SP], #0x10
    // 0x925d0c: ret
    //     0x925d0c: ret             
    // 0x925d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925d10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925d14: b               #0x925ca0
  }
  [closure] bool <anonymous closure>(dynamic) {
    // ** addr: 0x9260ec, size: 0x50
    // 0x9260ec: EnterFrame
    //     0x9260ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9260f0: mov             fp, SP
    // 0x9260f4: ldr             x0, [fp, #0x10]
    // 0x9260f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9260f8: ldur            w1, [x0, #0x17]
    // 0x9260fc: DecompressPointer r1
    //     0x9260fc: add             x1, x1, HEAP, lsl #32
    // 0x926100: CheckStackOverflow
    //     0x926100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926104: cmp             SP, x16
    //     0x926108: b.ls            #0x926134
    // 0x92610c: LoadField: r0 = r1->field_f
    //     0x92610c: ldur            w0, [x1, #0xf]
    // 0x926110: DecompressPointer r0
    //     0x926110: add             x0, x0, HEAP, lsl #32
    // 0x926114: LoadField: r1 = r0->field_1f
    //     0x926114: ldur            w1, [x0, #0x1f]
    // 0x926118: DecompressPointer r1
    //     0x926118: add             x1, x1, HEAP, lsl #32
    // 0x92611c: r2 = false
    //     0x92611c: add             x2, NULL, #0x30  ; false
    // 0x926120: r0 = isFirstOpenWaris=()
    //     0x926120: bl              #0x92613c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isFirstOpenWaris=
    // 0x926124: r0 = false
    //     0x926124: add             x0, NULL, #0x30  ; false
    // 0x926128: LeaveFrame
    //     0x926128: mov             SP, fp
    //     0x92612c: ldp             fp, lr, [SP], #0x10
    // 0x926130: ret
    //     0x926130: ret             
    // 0x926134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926134: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926138: b               #0x92610c
  }
  _ onFemaleCountChanged(/* No info */) {
    // ** addr: 0xb5f5a8, size: 0x168
    // 0xb5f5a8: EnterFrame
    //     0xb5f5a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb5f5ac: mov             fp, SP
    // 0xb5f5b0: AllocStack(0x40)
    //     0xb5f5b0: sub             SP, SP, #0x40
    // 0xb5f5b4: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5f5b4: stur            x2, [fp, #-0x10]
    //     0xb5f5b8: stur            x3, [fp, #-0x18]
    // 0xb5f5bc: CheckStackOverflow
    //     0xb5f5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5f5c0: cmp             SP, x16
    //     0xb5f5c4: b.ls            #0xb5f708
    // 0xb5f5c8: LoadField: r0 = r1->field_2b
    //     0xb5f5c8: ldur            w0, [x1, #0x2b]
    // 0xb5f5cc: DecompressPointer r0
    //     0xb5f5cc: add             x0, x0, HEAP, lsl #32
    // 0xb5f5d0: mov             x1, x0
    // 0xb5f5d4: stur            x0, [fp, #-8]
    // 0xb5f5d8: r0 = value()
    //     0xb5f5d8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5f5dc: mov             x3, x0
    // 0xb5f5e0: ldur            x2, [fp, #-0x10]
    // 0xb5f5e4: r0 = BoxInt64Instr(r2)
    //     0xb5f5e4: sbfiz           x0, x2, #1, #0x1f
    //     0xb5f5e8: cmp             x2, x0, asr #1
    //     0xb5f5ec: b.eq            #0xb5f5f8
    //     0xb5f5f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5f5f4: stur            x2, [x0, #7]
    // 0xb5f5f8: mov             x1, x0
    // 0xb5f5fc: stur            x1, [fp, #-0x20]
    // 0xb5f600: r0 = LoadClassIdInstr(r3)
    //     0xb5f600: ldur            x0, [x3, #-1]
    //     0xb5f604: ubfx            x0, x0, #0xc, #0x14
    // 0xb5f608: stp             x1, x3, [SP]
    // 0xb5f60c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5f60c: movz            x17, #0x3037
    //     0xb5f610: movk            x17, #0x1, lsl #16
    //     0xb5f614: add             lr, x0, x17
    //     0xb5f618: ldr             lr, [x21, lr, lsl #3]
    //     0xb5f61c: blr             lr
    // 0xb5f620: LoadField: r1 = r0->field_b
    //     0xb5f620: ldur            x1, [x0, #0xb]
    // 0xb5f624: cmp             x1, #1
    // 0xb5f628: b.ne            #0xb5f648
    // 0xb5f62c: ldur            x0, [fp, #-0x18]
    // 0xb5f630: cmn             x0, #1
    // 0xb5f634: b.ne            #0xb5f64c
    // 0xb5f638: r0 = Null
    //     0xb5f638: mov             x0, NULL
    // 0xb5f63c: LeaveFrame
    //     0xb5f63c: mov             SP, fp
    //     0xb5f640: ldp             fp, lr, [SP], #0x10
    // 0xb5f644: ret
    //     0xb5f644: ret             
    // 0xb5f648: ldur            x0, [fp, #-0x18]
    // 0xb5f64c: ldur            x1, [fp, #-8]
    // 0xb5f650: r0 = value()
    //     0xb5f650: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5f654: r1 = LoadClassIdInstr(r0)
    //     0xb5f654: ldur            x1, [x0, #-1]
    //     0xb5f658: ubfx            x1, x1, #0xc, #0x14
    // 0xb5f65c: ldur            x16, [fp, #-0x20]
    // 0xb5f660: stp             x16, x0, [SP]
    // 0xb5f664: mov             x0, x1
    // 0xb5f668: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5f668: movz            x17, #0x3037
    //     0xb5f66c: movk            x17, #0x1, lsl #16
    //     0xb5f670: add             lr, x0, x17
    //     0xb5f674: ldr             lr, [x21, lr, lsl #3]
    //     0xb5f678: blr             lr
    // 0xb5f67c: ldur            x1, [fp, #-8]
    // 0xb5f680: stur            x0, [fp, #-0x28]
    // 0xb5f684: r0 = value()
    //     0xb5f684: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5f688: r1 = LoadClassIdInstr(r0)
    //     0xb5f688: ldur            x1, [x0, #-1]
    //     0xb5f68c: ubfx            x1, x1, #0xc, #0x14
    // 0xb5f690: ldur            x16, [fp, #-0x20]
    // 0xb5f694: stp             x16, x0, [SP]
    // 0xb5f698: mov             x0, x1
    // 0xb5f69c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5f69c: movz            x17, #0x3037
    //     0xb5f6a0: movk            x17, #0x1, lsl #16
    //     0xb5f6a4: add             lr, x0, x17
    //     0xb5f6a8: ldr             lr, [x21, lr, lsl #3]
    //     0xb5f6ac: blr             lr
    // 0xb5f6b0: LoadField: r1 = r0->field_b
    //     0xb5f6b0: ldur            x1, [x0, #0xb]
    // 0xb5f6b4: ldur            x0, [fp, #-0x18]
    // 0xb5f6b8: add             x2, x1, x0
    // 0xb5f6bc: r0 = BoxInt64Instr(r2)
    //     0xb5f6bc: sbfiz           x0, x2, #1, #0x1f
    //     0xb5f6c0: cmp             x2, x0, asr #1
    //     0xb5f6c4: b.eq            #0xb5f6d0
    //     0xb5f6c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5f6cc: stur            x2, [x0, #7]
    // 0xb5f6d0: str             x0, [SP]
    // 0xb5f6d4: ldur            x1, [fp, #-0x28]
    // 0xb5f6d8: r4 = const [0, 0x2, 0x1, 0x1, count, 0x1, null]
    //     0xb5f6d8: add             x4, PP, #0x28, lsl #12  ; [pp+0x282b8] List(7) [0, 0x2, 0x1, 0x1, "count", 0x1, Null]
    //     0xb5f6dc: ldr             x4, [x4, #0x2b8]
    // 0xb5f6e0: r0 = copyWith()
    //     0xb5f6e0: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0xb5f6e4: ldur            x16, [fp, #-8]
    // 0xb5f6e8: ldur            lr, [fp, #-0x20]
    // 0xb5f6ec: stp             lr, x16, [SP, #8]
    // 0xb5f6f0: str             x0, [SP]
    // 0xb5f6f4: r0 = []=()
    //     0xb5f6f4: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xb5f6f8: r0 = Null
    //     0xb5f6f8: mov             x0, NULL
    // 0xb5f6fc: LeaveFrame
    //     0xb5f6fc: mov             SP, fp
    //     0xb5f700: ldp             fp, lr, [SP], #0x10
    // 0xb5f704: ret
    //     0xb5f704: ret             
    // 0xb5f708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5f708: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5f70c: b               #0xb5f5c8
  }
  _ onFemaleCheckChanged(/* No info */) {
    // ** addr: 0xb5f808, size: 0xbc
    // 0xb5f808: EnterFrame
    //     0xb5f808: stp             fp, lr, [SP, #-0x10]!
    //     0xb5f80c: mov             fp, SP
    // 0xb5f810: AllocStack(0x38)
    //     0xb5f810: sub             SP, SP, #0x38
    // 0xb5f814: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5f814: stur            x2, [fp, #-0x10]
    //     0xb5f818: stur            x3, [fp, #-0x18]
    // 0xb5f81c: CheckStackOverflow
    //     0xb5f81c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5f820: cmp             SP, x16
    //     0xb5f824: b.ls            #0xb5f8bc
    // 0xb5f828: LoadField: r0 = r1->field_2b
    //     0xb5f828: ldur            w0, [x1, #0x2b]
    // 0xb5f82c: DecompressPointer r0
    //     0xb5f82c: add             x0, x0, HEAP, lsl #32
    // 0xb5f830: mov             x1, x0
    // 0xb5f834: stur            x0, [fp, #-8]
    // 0xb5f838: r0 = value()
    //     0xb5f838: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5f83c: mov             x3, x0
    // 0xb5f840: ldur            x2, [fp, #-0x10]
    // 0xb5f844: r0 = BoxInt64Instr(r2)
    //     0xb5f844: sbfiz           x0, x2, #1, #0x1f
    //     0xb5f848: cmp             x2, x0, asr #1
    //     0xb5f84c: b.eq            #0xb5f858
    //     0xb5f850: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5f854: stur            x2, [x0, #7]
    // 0xb5f858: mov             x1, x0
    // 0xb5f85c: stur            x1, [fp, #-0x20]
    // 0xb5f860: r0 = LoadClassIdInstr(r3)
    //     0xb5f860: ldur            x0, [x3, #-1]
    //     0xb5f864: ubfx            x0, x0, #0xc, #0x14
    // 0xb5f868: stp             x1, x3, [SP]
    // 0xb5f86c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5f86c: movz            x17, #0x3037
    //     0xb5f870: movk            x17, #0x1, lsl #16
    //     0xb5f874: add             lr, x0, x17
    //     0xb5f878: ldr             lr, [x21, lr, lsl #3]
    //     0xb5f87c: blr             lr
    // 0xb5f880: ldur            x16, [fp, #-0x18]
    // 0xb5f884: str             x16, [SP]
    // 0xb5f888: mov             x1, x0
    // 0xb5f88c: r4 = const [0, 0x2, 0x1, 0x1, checked, 0x1, null]
    //     0xb5f88c: add             x4, PP, #0x28, lsl #12  ; [pp+0x282e0] List(7) [0, 0x2, 0x1, 0x1, "checked", 0x1, Null]
    //     0xb5f890: ldr             x4, [x4, #0x2e0]
    // 0xb5f894: r0 = copyWith()
    //     0xb5f894: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0xb5f898: ldur            x16, [fp, #-8]
    // 0xb5f89c: ldur            lr, [fp, #-0x20]
    // 0xb5f8a0: stp             lr, x16, [SP, #8]
    // 0xb5f8a4: str             x0, [SP]
    // 0xb5f8a8: r0 = []=()
    //     0xb5f8a8: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xb5f8ac: r0 = Null
    //     0xb5f8ac: mov             x0, NULL
    // 0xb5f8b0: LeaveFrame
    //     0xb5f8b0: mov             SP, fp
    //     0xb5f8b4: ldp             fp, lr, [SP], #0x10
    // 0xb5f8b8: ret
    //     0xb5f8b8: ret             
    // 0xb5f8bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5f8bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5f8c0: b               #0xb5f828
  }
  _ onMaleCountChanged(/* No info */) {
    // ** addr: 0xb5fbe0, size: 0x168
    // 0xb5fbe0: EnterFrame
    //     0xb5fbe0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fbe4: mov             fp, SP
    // 0xb5fbe8: AllocStack(0x40)
    //     0xb5fbe8: sub             SP, SP, #0x40
    // 0xb5fbec: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5fbec: stur            x2, [fp, #-0x10]
    //     0xb5fbf0: stur            x3, [fp, #-0x18]
    // 0xb5fbf4: CheckStackOverflow
    //     0xb5fbf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fbf8: cmp             SP, x16
    //     0xb5fbfc: b.ls            #0xb5fd40
    // 0xb5fc00: LoadField: r0 = r1->field_27
    //     0xb5fc00: ldur            w0, [x1, #0x27]
    // 0xb5fc04: DecompressPointer r0
    //     0xb5fc04: add             x0, x0, HEAP, lsl #32
    // 0xb5fc08: mov             x1, x0
    // 0xb5fc0c: stur            x0, [fp, #-8]
    // 0xb5fc10: r0 = value()
    //     0xb5fc10: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5fc14: mov             x3, x0
    // 0xb5fc18: ldur            x2, [fp, #-0x10]
    // 0xb5fc1c: r0 = BoxInt64Instr(r2)
    //     0xb5fc1c: sbfiz           x0, x2, #1, #0x1f
    //     0xb5fc20: cmp             x2, x0, asr #1
    //     0xb5fc24: b.eq            #0xb5fc30
    //     0xb5fc28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5fc2c: stur            x2, [x0, #7]
    // 0xb5fc30: mov             x1, x0
    // 0xb5fc34: stur            x1, [fp, #-0x20]
    // 0xb5fc38: r0 = LoadClassIdInstr(r3)
    //     0xb5fc38: ldur            x0, [x3, #-1]
    //     0xb5fc3c: ubfx            x0, x0, #0xc, #0x14
    // 0xb5fc40: stp             x1, x3, [SP]
    // 0xb5fc44: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5fc44: movz            x17, #0x3037
    //     0xb5fc48: movk            x17, #0x1, lsl #16
    //     0xb5fc4c: add             lr, x0, x17
    //     0xb5fc50: ldr             lr, [x21, lr, lsl #3]
    //     0xb5fc54: blr             lr
    // 0xb5fc58: LoadField: r1 = r0->field_b
    //     0xb5fc58: ldur            x1, [x0, #0xb]
    // 0xb5fc5c: cmp             x1, #1
    // 0xb5fc60: b.ne            #0xb5fc80
    // 0xb5fc64: ldur            x0, [fp, #-0x18]
    // 0xb5fc68: cmn             x0, #1
    // 0xb5fc6c: b.ne            #0xb5fc84
    // 0xb5fc70: r0 = Null
    //     0xb5fc70: mov             x0, NULL
    // 0xb5fc74: LeaveFrame
    //     0xb5fc74: mov             SP, fp
    //     0xb5fc78: ldp             fp, lr, [SP], #0x10
    // 0xb5fc7c: ret
    //     0xb5fc7c: ret             
    // 0xb5fc80: ldur            x0, [fp, #-0x18]
    // 0xb5fc84: ldur            x1, [fp, #-8]
    // 0xb5fc88: r0 = value()
    //     0xb5fc88: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5fc8c: r1 = LoadClassIdInstr(r0)
    //     0xb5fc8c: ldur            x1, [x0, #-1]
    //     0xb5fc90: ubfx            x1, x1, #0xc, #0x14
    // 0xb5fc94: ldur            x16, [fp, #-0x20]
    // 0xb5fc98: stp             x16, x0, [SP]
    // 0xb5fc9c: mov             x0, x1
    // 0xb5fca0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5fca0: movz            x17, #0x3037
    //     0xb5fca4: movk            x17, #0x1, lsl #16
    //     0xb5fca8: add             lr, x0, x17
    //     0xb5fcac: ldr             lr, [x21, lr, lsl #3]
    //     0xb5fcb0: blr             lr
    // 0xb5fcb4: ldur            x1, [fp, #-8]
    // 0xb5fcb8: stur            x0, [fp, #-0x28]
    // 0xb5fcbc: r0 = value()
    //     0xb5fcbc: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5fcc0: r1 = LoadClassIdInstr(r0)
    //     0xb5fcc0: ldur            x1, [x0, #-1]
    //     0xb5fcc4: ubfx            x1, x1, #0xc, #0x14
    // 0xb5fcc8: ldur            x16, [fp, #-0x20]
    // 0xb5fccc: stp             x16, x0, [SP]
    // 0xb5fcd0: mov             x0, x1
    // 0xb5fcd4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5fcd4: movz            x17, #0x3037
    //     0xb5fcd8: movk            x17, #0x1, lsl #16
    //     0xb5fcdc: add             lr, x0, x17
    //     0xb5fce0: ldr             lr, [x21, lr, lsl #3]
    //     0xb5fce4: blr             lr
    // 0xb5fce8: LoadField: r1 = r0->field_b
    //     0xb5fce8: ldur            x1, [x0, #0xb]
    // 0xb5fcec: ldur            x0, [fp, #-0x18]
    // 0xb5fcf0: add             x2, x1, x0
    // 0xb5fcf4: r0 = BoxInt64Instr(r2)
    //     0xb5fcf4: sbfiz           x0, x2, #1, #0x1f
    //     0xb5fcf8: cmp             x2, x0, asr #1
    //     0xb5fcfc: b.eq            #0xb5fd08
    //     0xb5fd00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5fd04: stur            x2, [x0, #7]
    // 0xb5fd08: str             x0, [SP]
    // 0xb5fd0c: ldur            x1, [fp, #-0x28]
    // 0xb5fd10: r4 = const [0, 0x2, 0x1, 0x1, count, 0x1, null]
    //     0xb5fd10: add             x4, PP, #0x28, lsl #12  ; [pp+0x282b8] List(7) [0, 0x2, 0x1, 0x1, "count", 0x1, Null]
    //     0xb5fd14: ldr             x4, [x4, #0x2b8]
    // 0xb5fd18: r0 = copyWith()
    //     0xb5fd18: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0xb5fd1c: ldur            x16, [fp, #-8]
    // 0xb5fd20: ldur            lr, [fp, #-0x20]
    // 0xb5fd24: stp             lr, x16, [SP, #8]
    // 0xb5fd28: str             x0, [SP]
    // 0xb5fd2c: r0 = []=()
    //     0xb5fd2c: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xb5fd30: r0 = Null
    //     0xb5fd30: mov             x0, NULL
    // 0xb5fd34: LeaveFrame
    //     0xb5fd34: mov             SP, fp
    //     0xb5fd38: ldp             fp, lr, [SP], #0x10
    // 0xb5fd3c: ret
    //     0xb5fd3c: ret             
    // 0xb5fd40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fd40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fd44: b               #0xb5fc00
  }
  _ onMaleCheckChanged(/* No info */) {
    // ** addr: 0xb5fe40, size: 0xbc
    // 0xb5fe40: EnterFrame
    //     0xb5fe40: stp             fp, lr, [SP, #-0x10]!
    //     0xb5fe44: mov             fp, SP
    // 0xb5fe48: AllocStack(0x38)
    //     0xb5fe48: sub             SP, SP, #0x38
    // 0xb5fe4c: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5fe4c: stur            x2, [fp, #-0x10]
    //     0xb5fe50: stur            x3, [fp, #-0x18]
    // 0xb5fe54: CheckStackOverflow
    //     0xb5fe54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5fe58: cmp             SP, x16
    //     0xb5fe5c: b.ls            #0xb5fef4
    // 0xb5fe60: LoadField: r0 = r1->field_27
    //     0xb5fe60: ldur            w0, [x1, #0x27]
    // 0xb5fe64: DecompressPointer r0
    //     0xb5fe64: add             x0, x0, HEAP, lsl #32
    // 0xb5fe68: mov             x1, x0
    // 0xb5fe6c: stur            x0, [fp, #-8]
    // 0xb5fe70: r0 = value()
    //     0xb5fe70: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xb5fe74: mov             x3, x0
    // 0xb5fe78: ldur            x2, [fp, #-0x10]
    // 0xb5fe7c: r0 = BoxInt64Instr(r2)
    //     0xb5fe7c: sbfiz           x0, x2, #1, #0x1f
    //     0xb5fe80: cmp             x2, x0, asr #1
    //     0xb5fe84: b.eq            #0xb5fe90
    //     0xb5fe88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5fe8c: stur            x2, [x0, #7]
    // 0xb5fe90: mov             x1, x0
    // 0xb5fe94: stur            x1, [fp, #-0x20]
    // 0xb5fe98: r0 = LoadClassIdInstr(r3)
    //     0xb5fe98: ldur            x0, [x3, #-1]
    //     0xb5fe9c: ubfx            x0, x0, #0xc, #0x14
    // 0xb5fea0: stp             x1, x3, [SP]
    // 0xb5fea4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb5fea4: movz            x17, #0x3037
    //     0xb5fea8: movk            x17, #0x1, lsl #16
    //     0xb5feac: add             lr, x0, x17
    //     0xb5feb0: ldr             lr, [x21, lr, lsl #3]
    //     0xb5feb4: blr             lr
    // 0xb5feb8: ldur            x16, [fp, #-0x18]
    // 0xb5febc: str             x16, [SP]
    // 0xb5fec0: mov             x1, x0
    // 0xb5fec4: r4 = const [0, 0x2, 0x1, 0x1, checked, 0x1, null]
    //     0xb5fec4: add             x4, PP, #0x28, lsl #12  ; [pp+0x282e0] List(7) [0, 0x2, 0x1, 0x1, "checked", 0x1, Null]
    //     0xb5fec8: ldr             x4, [x4, #0x2e0]
    // 0xb5fecc: r0 = copyWith()
    //     0xb5fecc: bl              #0x906fa0  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] HeirItem::copyWith
    // 0xb5fed0: ldur            x16, [fp, #-8]
    // 0xb5fed4: ldur            lr, [fp, #-0x20]
    // 0xb5fed8: stp             lr, x16, [SP, #8]
    // 0xb5fedc: str             x0, [SP]
    // 0xb5fee0: r0 = []=()
    //     0xb5fee0: bl              #0x66d1d0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::[]=
    // 0xb5fee4: r0 = Null
    //     0xb5fee4: mov             x0, NULL
    // 0xb5fee8: LeaveFrame
    //     0xb5fee8: mov             SP, fp
    //     0xb5feec: ldp             fp, lr, [SP], #0x10
    // 0xb5fef0: ret
    //     0xb5fef0: ret             
    // 0xb5fef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5fef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5fef8: b               #0xb5fe60
  }
  [closure] void onInfoPressed(dynamic) {
    // ** addr: 0xb60128, size: 0x38
    // 0xb60128: EnterFrame
    //     0xb60128: stp             fp, lr, [SP, #-0x10]!
    //     0xb6012c: mov             fp, SP
    // 0xb60130: ldr             x0, [fp, #0x10]
    // 0xb60134: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb60134: ldur            w1, [x0, #0x17]
    // 0xb60138: DecompressPointer r1
    //     0xb60138: add             x1, x1, HEAP, lsl #32
    // 0xb6013c: CheckStackOverflow
    //     0xb6013c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb60140: cmp             SP, x16
    //     0xb60144: b.ls            #0xb60158
    // 0xb60148: r0 = onInfoPressed()
    //     0xb60148: bl              #0xb60160  ; [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::onInfoPressed
    // 0xb6014c: LeaveFrame
    //     0xb6014c: mov             SP, fp
    //     0xb60150: ldp             fp, lr, [SP], #0x10
    // 0xb60154: ret
    //     0xb60154: ret             
    // 0xb60158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb60158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6015c: b               #0xb60148
  }
  _ onInfoPressed(/* No info */) {
    // ** addr: 0xb60160, size: 0x60
    // 0xb60160: EnterFrame
    //     0xb60160: stp             fp, lr, [SP, #-0x10]!
    //     0xb60164: mov             fp, SP
    // 0xb60168: AllocStack(0x10)
    //     0xb60168: sub             SP, SP, #0x10
    // 0xb6016c: CheckStackOverflow
    //     0xb6016c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb60170: cmp             SP, x16
    //     0xb60174: b.ls            #0xb601b8
    // 0xb60178: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb60178: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6017c: ldr             x0, [x0, #0x2670]
    //     0xb60180: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb60184: cmp             w0, w16
    //     0xb60188: b.ne            #0xb60194
    //     0xb6018c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb60190: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb60194: r16 = Closure: () => WarisInfoView from Function 'WarisInfoView.page': static.
    //     0xb60194: add             x16, PP, #0x28, lsl #12  ; [pp+0x28348] Closure: () => WarisInfoView from Function 'WarisInfoView.page': static. (0x7e54fb5601c0)
    //     0xb60198: ldr             x16, [x16, #0x348]
    // 0xb6019c: stp             x16, NULL, [SP]
    // 0xb601a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb601a0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb601a4: r0 = GetNavigation.to()
    //     0xb601a4: bl              #0xadf3e0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xb601a8: r0 = Null
    //     0xb601a8: mov             x0, NULL
    // 0xb601ac: LeaveFrame
    //     0xb601ac: mov             SP, fp
    //     0xb601b0: ldp             fp, lr, [SP], #0x10
    // 0xb601b4: ret
    //     0xb601b4: ret             
    // 0xb601b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb601b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb601bc: b               #0xb60178
  }
}
