// lib: , url: package:nuonline/app/modules/waris/views/waris_info_view.dart

// class id: 1050628, size: 0x8
class :: {
}

// class id: 4939, size: 0x10, field offset: 0xc
//   const constructor, 
class WarisInfoView extends StatelessWidget {

  factory WarisInfoView WarisInfoView.bottomSheet(dynamic) {
    // ** addr: 0x925d18, size: 0x3c
    // 0x925d18: EnterFrame
    //     0x925d18: stp             fp, lr, [SP, #-0x10]!
    //     0x925d1c: mov             fp, SP
    // 0x925d20: AllocStack(0x8)
    //     0x925d20: sub             SP, SP, #8
    // 0x925d24: SetupParameters(dynamic _ /* r1 => r0 */)
    //     0x925d24: mov             x0, x1
    // 0x925d28: r1 = Function '<anonymous closure>': static.
    //     0x925d28: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6e8] AnonymousClosure: static (0x925d60), in [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::WarisInfoView.bottomSheet (0x925d18)
    //     0x925d2c: ldr             x1, [x1, #0x6e8]
    // 0x925d30: r2 = Null
    //     0x925d30: mov             x2, NULL
    // 0x925d34: r0 = AllocateClosure()
    //     0x925d34: bl              #0xec1630  ; AllocateClosureStub
    // 0x925d38: stur            x0, [fp, #-8]
    // 0x925d3c: r0 = WarisInfoView()
    //     0x925d3c: bl              #0x925d54  ; AllocateWarisInfoViewStub -> WarisInfoView (size=0x10)
    // 0x925d40: ldur            x1, [fp, #-8]
    // 0x925d44: StoreField: r0->field_b = r1
    //     0x925d44: stur            w1, [x0, #0xb]
    // 0x925d48: LeaveFrame
    //     0x925d48: mov             SP, fp
    //     0x925d4c: ldp             fp, lr, [SP], #0x10
    // 0x925d50: ret
    //     0x925d50: ret             
  }
  [closure] static NBottomSheet <anonymous closure>(dynamic, Widget) {
    // ** addr: 0x925d60, size: 0x194
    // 0x925d60: EnterFrame
    //     0x925d60: stp             fp, lr, [SP, #-0x10]!
    //     0x925d64: mov             fp, SP
    // 0x925d68: AllocStack(0x28)
    //     0x925d68: sub             SP, SP, #0x28
    // 0x925d6c: CheckStackOverflow
    //     0x925d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925d70: cmp             SP, x16
    //     0x925d74: b.ls            #0x925eec
    // 0x925d78: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x925d78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x925d7c: ldr             x0, [x0, #0x2670]
    //     0x925d80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x925d84: cmp             w0, w16
    //     0x925d88: b.ne            #0x925d94
    //     0x925d8c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x925d90: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x925d94: r0 = GetNavigation.textTheme()
    //     0x925d94: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0x925d98: LoadField: r1 = r0->field_2f
    //     0x925d98: ldur            w1, [x0, #0x2f]
    // 0x925d9c: DecompressPointer r1
    //     0x925d9c: add             x1, x1, HEAP, lsl #32
    // 0x925da0: cmp             w1, NULL
    // 0x925da4: b.ne            #0x925db0
    // 0x925da8: r1 = Null
    //     0x925da8: mov             x1, NULL
    // 0x925dac: b               #0x925dd4
    // 0x925db0: r16 = 16.000000
    //     0x925db0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0x925db4: ldr             x16, [x16, #0x80]
    // 0x925db8: r30 = Instance_FontWeight
    //     0x925db8: add             lr, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0x925dbc: ldr             lr, [lr, #0xe20]
    // 0x925dc0: stp             lr, x16, [SP]
    // 0x925dc4: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, fontWeight, 0x2, null]
    //     0x925dc4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cdf8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0x925dc8: ldr             x4, [x4, #0xdf8]
    // 0x925dcc: r0 = copyWith()
    //     0x925dcc: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x925dd0: mov             x1, x0
    // 0x925dd4: ldr             x0, [fp, #0x10]
    // 0x925dd8: stur            x1, [fp, #-8]
    // 0x925ddc: r0 = Text()
    //     0x925ddc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0x925de0: mov             x3, x0
    // 0x925de4: r0 = "Penting!"
    //     0x925de4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bd8] "Penting!"
    //     0x925de8: ldr             x0, [x0, #0xbd8]
    // 0x925dec: stur            x3, [fp, #-0x10]
    // 0x925df0: StoreField: r3->field_b = r0
    //     0x925df0: stur            w0, [x3, #0xb]
    // 0x925df4: ldur            x0, [fp, #-8]
    // 0x925df8: StoreField: r3->field_13 = r0
    //     0x925df8: stur            w0, [x3, #0x13]
    // 0x925dfc: r1 = Function '<anonymous closure>': static.
    //     0x925dfc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d5d0] AnonymousClosure: static (0x925f18), of [package:get/get_navigation/src/extension_navigation.dart] 
    //     0x925e00: ldr             x1, [x1, #0x5d0]
    // 0x925e04: r2 = Null
    //     0x925e04: mov             x2, NULL
    // 0x925e08: r0 = AllocateClosureGeneric()
    //     0x925e08: bl              #0xec1550  ; AllocateClosureGenericStub
    // 0x925e0c: stp             NULL, x0, [SP]
    // 0x925e10: r0 = _boundsCheckForPartialInstantiation()
    //     0x925e10: bl              #0x6022c8  ; [dart:_internal] ::_boundsCheckForPartialInstantiation
    // 0x925e14: r1 = Function '<anonymous closure>': static.
    //     0x925e14: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d5d0] AnonymousClosure: static (0x925f18), of [package:get/get_navigation/src/extension_navigation.dart] 
    //     0x925e18: ldr             x1, [x1, #0x5d0]
    // 0x925e1c: r2 = Null
    //     0x925e1c: mov             x2, NULL
    // 0x925e20: r3 = Null
    //     0x925e20: mov             x3, NULL
    // 0x925e24: r0 = AllocateClosureTA()
    //     0x925e24: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x925e28: stur            x0, [fp, #-8]
    // 0x925e2c: r0 = TextButton()
    //     0x925e2c: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x925e30: mov             x1, x0
    // 0x925e34: ldur            x0, [fp, #-8]
    // 0x925e38: stur            x1, [fp, #-0x18]
    // 0x925e3c: StoreField: r1->field_b = r0
    //     0x925e3c: stur            w0, [x1, #0xb]
    // 0x925e40: r0 = false
    //     0x925e40: add             x0, NULL, #0x30  ; false
    // 0x925e44: StoreField: r1->field_27 = r0
    //     0x925e44: stur            w0, [x1, #0x27]
    // 0x925e48: r0 = true
    //     0x925e48: add             x0, NULL, #0x20  ; true
    // 0x925e4c: StoreField: r1->field_2f = r0
    //     0x925e4c: stur            w0, [x1, #0x2f]
    // 0x925e50: r0 = Instance_Text
    //     0x925e50: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c6f0] Obj!Text@e21461
    //     0x925e54: ldr             x0, [x0, #0x6f0]
    // 0x925e58: StoreField: r1->field_37 = r0
    //     0x925e58: stur            w0, [x1, #0x37]
    // 0x925e5c: r0 = SizedBox()
    //     0x925e5c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x925e60: mov             x3, x0
    // 0x925e64: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0x925e64: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0x925e68: ldr             x0, [x0, #0xc58]
    // 0x925e6c: stur            x3, [fp, #-8]
    // 0x925e70: StoreField: r3->field_f = r0
    //     0x925e70: stur            w0, [x3, #0xf]
    // 0x925e74: ldur            x0, [fp, #-0x18]
    // 0x925e78: StoreField: r3->field_b = r0
    //     0x925e78: stur            w0, [x3, #0xb]
    // 0x925e7c: r1 = Null
    //     0x925e7c: mov             x1, NULL
    // 0x925e80: r2 = 8
    //     0x925e80: movz            x2, #0x8
    // 0x925e84: r0 = AllocateArray()
    //     0x925e84: bl              #0xec22fc  ; AllocateArrayStub
    // 0x925e88: mov             x2, x0
    // 0x925e8c: ldr             x0, [fp, #0x10]
    // 0x925e90: stur            x2, [fp, #-0x18]
    // 0x925e94: StoreField: r2->field_f = r0
    //     0x925e94: stur            w0, [x2, #0xf]
    // 0x925e98: r16 = Instance_SizedBox
    //     0x925e98: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0x925e9c: ldr             x16, [x16, #0x540]
    // 0x925ea0: StoreField: r2->field_13 = r16
    //     0x925ea0: stur            w16, [x2, #0x13]
    // 0x925ea4: ldur            x0, [fp, #-0x10]
    // 0x925ea8: ArrayStore: r2[0] = r0  ; List_4
    //     0x925ea8: stur            w0, [x2, #0x17]
    // 0x925eac: ldur            x0, [fp, #-8]
    // 0x925eb0: StoreField: r2->field_1b = r0
    //     0x925eb0: stur            w0, [x2, #0x1b]
    // 0x925eb4: r1 = <Widget>
    //     0x925eb4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x925eb8: r0 = AllocateGrowableArray()
    //     0x925eb8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x925ebc: mov             x1, x0
    // 0x925ec0: ldur            x0, [fp, #-0x18]
    // 0x925ec4: stur            x1, [fp, #-8]
    // 0x925ec8: StoreField: r1->field_f = r0
    //     0x925ec8: stur            w0, [x1, #0xf]
    // 0x925ecc: r0 = 8
    //     0x925ecc: movz            x0, #0x8
    // 0x925ed0: StoreField: r1->field_b = r0
    //     0x925ed0: stur            w0, [x1, #0xb]
    // 0x925ed4: r0 = NBottomSheet()
    //     0x925ed4: bl              #0x925ef4  ; AllocateNBottomSheetStub -> NBottomSheet (size=0x10)
    // 0x925ed8: ldur            x1, [fp, #-8]
    // 0x925edc: StoreField: r0->field_b = r1
    //     0x925edc: stur            w1, [x0, #0xb]
    // 0x925ee0: LeaveFrame
    //     0x925ee0: mov             SP, fp
    //     0x925ee4: ldp             fp, lr, [SP], #0x10
    // 0x925ee8: ret
    //     0x925ee8: ret             
    // 0x925eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925eec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925ef0: b               #0x925d78
  }
  [closure] static WarisInfoView WarisInfoView.page(dynamic) {
    // ** addr: 0xb601c0, size: 0x30
    // 0xb601c0: EnterFrame
    //     0xb601c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb601c4: mov             fp, SP
    // 0xb601c8: CheckStackOverflow
    //     0xb601c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb601cc: cmp             SP, x16
    //     0xb601d0: b.ls            #0xb601e8
    // 0xb601d4: r1 = Null
    //     0xb601d4: mov             x1, NULL
    // 0xb601d8: r0 = WarisInfoView.page()
    //     0xb601d8: bl              #0xb601f0  ; [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::WarisInfoView.page
    // 0xb601dc: LeaveFrame
    //     0xb601dc: mov             SP, fp
    //     0xb601e0: ldp             fp, lr, [SP], #0x10
    // 0xb601e4: ret
    //     0xb601e4: ret             
    // 0xb601e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb601e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb601ec: b               #0xb601d4
  }
  factory WarisInfoView WarisInfoView.page(dynamic) {
    // ** addr: 0xb601f0, size: 0x3c
    // 0xb601f0: EnterFrame
    //     0xb601f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb601f4: mov             fp, SP
    // 0xb601f8: AllocStack(0x8)
    //     0xb601f8: sub             SP, SP, #8
    // 0xb601fc: SetupParameters(dynamic _ /* r1 => r0 */)
    //     0xb601fc: mov             x0, x1
    // 0xb60200: r1 = Function '<anonymous closure>': static.
    //     0xb60200: add             x1, PP, #0x28, lsl #12  ; [pp+0x28350] AnonymousClosure: static (0xb6022c), in [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::WarisInfoView.page (0xb601f0)
    //     0xb60204: ldr             x1, [x1, #0x350]
    // 0xb60208: r2 = Null
    //     0xb60208: mov             x2, NULL
    // 0xb6020c: r0 = AllocateClosure()
    //     0xb6020c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb60210: stur            x0, [fp, #-8]
    // 0xb60214: r0 = WarisInfoView()
    //     0xb60214: bl              #0x925d54  ; AllocateWarisInfoViewStub -> WarisInfoView (size=0x10)
    // 0xb60218: ldur            x1, [fp, #-8]
    // 0xb6021c: StoreField: r0->field_b = r1
    //     0xb6021c: stur            w1, [x0, #0xb]
    // 0xb60220: LeaveFrame
    //     0xb60220: mov             SP, fp
    //     0xb60224: ldp             fp, lr, [SP], #0x10
    // 0xb60228: ret
    //     0xb60228: ret             
  }
  [closure] static Scaffold <anonymous closure>(dynamic, Widget) {
    // ** addr: 0xb6022c, size: 0xb4
    // 0xb6022c: EnterFrame
    //     0xb6022c: stp             fp, lr, [SP, #-0x10]!
    //     0xb60230: mov             fp, SP
    // 0xb60234: AllocStack(0x18)
    //     0xb60234: sub             SP, SP, #0x18
    // 0xb60238: CheckStackOverflow
    //     0xb60238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6023c: cmp             SP, x16
    //     0xb60240: b.ls            #0xb602d8
    // 0xb60244: r0 = AppBar()
    //     0xb60244: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb60248: stur            x0, [fp, #-8]
    // 0xb6024c: r16 = Instance_Text
    //     0xb6024c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28358] Obj!Text@e219b1
    //     0xb60250: ldr             x16, [x16, #0x358]
    // 0xb60254: str             x16, [SP]
    // 0xb60258: mov             x1, x0
    // 0xb6025c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xb6025c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xb60260: ldr             x4, [x4, #0x6e8]
    // 0xb60264: r0 = AppBar()
    //     0xb60264: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb60268: r0 = Padding()
    //     0xb60268: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6026c: mov             x1, x0
    // 0xb60270: r0 = Instance_EdgeInsets
    //     0xb60270: add             x0, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb60274: ldr             x0, [x0, #0x360]
    // 0xb60278: stur            x1, [fp, #-0x10]
    // 0xb6027c: StoreField: r1->field_f = r0
    //     0xb6027c: stur            w0, [x1, #0xf]
    // 0xb60280: ldr             x0, [fp, #0x10]
    // 0xb60284: StoreField: r1->field_b = r0
    //     0xb60284: stur            w0, [x1, #0xb]
    // 0xb60288: r0 = Scaffold()
    //     0xb60288: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb6028c: ldur            x1, [fp, #-8]
    // 0xb60290: StoreField: r0->field_13 = r1
    //     0xb60290: stur            w1, [x0, #0x13]
    // 0xb60294: ldur            x1, [fp, #-0x10]
    // 0xb60298: ArrayStore: r0[0] = r1  ; List_4
    //     0xb60298: stur            w1, [x0, #0x17]
    // 0xb6029c: r1 = Instance_AlignmentDirectional
    //     0xb6029c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb602a0: ldr             x1, [x1, #0x758]
    // 0xb602a4: StoreField: r0->field_2b = r1
    //     0xb602a4: stur            w1, [x0, #0x2b]
    // 0xb602a8: r1 = true
    //     0xb602a8: add             x1, NULL, #0x20  ; true
    // 0xb602ac: StoreField: r0->field_53 = r1
    //     0xb602ac: stur            w1, [x0, #0x53]
    // 0xb602b0: r2 = Instance_DragStartBehavior
    //     0xb602b0: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb602b4: StoreField: r0->field_57 = r2
    //     0xb602b4: stur            w2, [x0, #0x57]
    // 0xb602b8: r2 = false
    //     0xb602b8: add             x2, NULL, #0x30  ; false
    // 0xb602bc: StoreField: r0->field_b = r2
    //     0xb602bc: stur            w2, [x0, #0xb]
    // 0xb602c0: StoreField: r0->field_f = r2
    //     0xb602c0: stur            w2, [x0, #0xf]
    // 0xb602c4: StoreField: r0->field_5f = r1
    //     0xb602c4: stur            w1, [x0, #0x5f]
    // 0xb602c8: StoreField: r0->field_63 = r1
    //     0xb602c8: stur            w1, [x0, #0x63]
    // 0xb602cc: LeaveFrame
    //     0xb602cc: mov             SP, fp
    //     0xb602d0: ldp             fp, lr, [SP], #0x10
    // 0xb602d4: ret
    //     0xb602d4: ret             
    // 0xb602d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb602d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb602dc: b               #0xb60244
  }
  _ build(/* No info */) {
    // ** addr: 0xbaed18, size: 0x45c
    // 0xbaed18: EnterFrame
    //     0xbaed18: stp             fp, lr, [SP, #-0x10]!
    //     0xbaed1c: mov             fp, SP
    // 0xbaed20: AllocStack(0x48)
    //     0xbaed20: sub             SP, SP, #0x48
    // 0xbaed24: SetupParameters(WarisInfoView this /* r1 => r0, fp-0x8 */)
    //     0xbaed24: mov             x0, x1
    //     0xbaed28: stur            x1, [fp, #-8]
    // 0xbaed2c: CheckStackOverflow
    //     0xbaed2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaed30: cmp             SP, x16
    //     0xbaed34: b.ls            #0xbaf16c
    // 0xbaed38: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbaed38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbaed3c: ldr             x0, [x0, #0x2670]
    //     0xbaed40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbaed44: cmp             w0, w16
    //     0xbaed48: b.ne            #0xbaed54
    //     0xbaed4c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbaed50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbaed54: r0 = GetNavigation.textTheme()
    //     0xbaed54: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbaed58: LoadField: r1 = r0->field_2f
    //     0xbaed58: ldur            w1, [x0, #0x2f]
    // 0xbaed5c: DecompressPointer r1
    //     0xbaed5c: add             x1, x1, HEAP, lsl #32
    // 0xbaed60: cmp             w1, NULL
    // 0xbaed64: b.ne            #0xbaed70
    // 0xbaed68: r0 = Null
    //     0xbaed68: mov             x0, NULL
    // 0xbaed6c: b               #0xbaed90
    // 0xbaed70: r16 = 16.000000
    //     0xbaed70: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbaed74: ldr             x16, [x16, #0x80]
    // 0xbaed78: r30 = Instance_FontWeight
    //     0xbaed78: add             lr, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xbaed7c: ldr             lr, [lr, #0xe20]
    // 0xbaed80: stp             lr, x16, [SP]
    // 0xbaed84: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, fontWeight, 0x2, null]
    //     0xbaed84: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cdf8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0xbaed88: ldr             x4, [x4, #0xdf8]
    // 0xbaed8c: r0 = copyWith()
    //     0xbaed8c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaed90: stur            x0, [fp, #-0x10]
    // 0xbaed94: r0 = Text()
    //     0xbaed94: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbaed98: mov             x1, x0
    // 0xbaed9c: r0 = "Penting!"
    //     0xbaed9c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bd8] "Penting!"
    //     0xbaeda0: ldr             x0, [x0, #0xbd8]
    // 0xbaeda4: stur            x1, [fp, #-0x18]
    // 0xbaeda8: StoreField: r1->field_b = r0
    //     0xbaeda8: stur            w0, [x1, #0xb]
    // 0xbaedac: ldur            x0, [fp, #-0x10]
    // 0xbaedb0: StoreField: r1->field_13 = r0
    //     0xbaedb0: stur            w0, [x1, #0x13]
    // 0xbaedb4: r0 = TapGestureRecognizer()
    //     0xbaedb4: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0xbaedb8: mov             x4, x0
    // 0xbaedbc: r0 = false
    //     0xbaedbc: add             x0, NULL, #0x30  ; false
    // 0xbaedc0: stur            x4, [fp, #-0x10]
    // 0xbaedc4: StoreField: r4->field_47 = r0
    //     0xbaedc4: stur            w0, [x4, #0x47]
    // 0xbaedc8: StoreField: r4->field_4b = r0
    //     0xbaedc8: stur            w0, [x4, #0x4b]
    // 0xbaedcc: mov             x1, x4
    // 0xbaedd0: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xbaedd0: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xbaedd4: ldr             x2, [x2, #0x3d8]
    // 0xbaedd8: r3 = Instance_Duration
    //     0xbaedd8: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xbaeddc: r5 = Null
    //     0xbaeddc: mov             x5, NULL
    // 0xbaede0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbaede0: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbaede4: r0 = PrimaryPointerGestureRecognizer()
    //     0xbaede4: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xbaede8: ldur            x2, [fp, #-8]
    // 0xbaedec: r1 = Function 'onMailToTapped':.
    //     0xbaedec: add             x1, PP, #0x31, lsl #12  ; [pp+0x31be0] AnonymousClosure: (0xbaf174), in [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::onMailToTapped (0xbaf1ac)
    //     0xbaedf0: ldr             x1, [x1, #0xbe0]
    // 0xbaedf4: r0 = AllocateClosure()
    //     0xbaedf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xbaedf8: ldur            x3, [fp, #-0x10]
    // 0xbaedfc: StoreField: r3->field_5f = r0
    //     0xbaedfc: stur            w0, [x3, #0x5f]
    //     0xbaee00: ldurb           w16, [x3, #-1]
    //     0xbaee04: ldurb           w17, [x0, #-1]
    //     0xbaee08: and             x16, x17, x16, lsr #2
    //     0xbaee0c: tst             x16, HEAP, lsr #32
    //     0xbaee10: b.eq            #0xbaee18
    //     0xbaee14: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xbaee18: r1 = _ConstMap len:10
    //     0xbaee18: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xbaee1c: ldr             x1, [x1, #0xc08]
    // 0xbaee20: r2 = 600
    //     0xbaee20: movz            x2, #0x258
    // 0xbaee24: r0 = []()
    //     0xbaee24: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbaee28: r16 = <Color?>
    //     0xbaee28: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xbaee2c: ldr             x16, [x16, #0x98]
    // 0xbaee30: stp             x0, x16, [SP, #8]
    // 0xbaee34: r16 = Instance_MaterialColor
    //     0xbaee34: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xbaee38: ldr             x16, [x16, #0xcf0]
    // 0xbaee3c: str             x16, [SP]
    // 0xbaee40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbaee40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbaee44: r0 = mode()
    //     0xbaee44: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xbaee48: stur            x0, [fp, #-0x20]
    // 0xbaee4c: r0 = TextStyle()
    //     0xbaee4c: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xbaee50: mov             x1, x0
    // 0xbaee54: r0 = true
    //     0xbaee54: add             x0, NULL, #0x20  ; true
    // 0xbaee58: stur            x1, [fp, #-0x28]
    // 0xbaee5c: StoreField: r1->field_7 = r0
    //     0xbaee5c: stur            w0, [x1, #7]
    // 0xbaee60: ldur            x0, [fp, #-0x20]
    // 0xbaee64: StoreField: r1->field_b = r0
    //     0xbaee64: stur            w0, [x1, #0xb]
    // 0xbaee68: r0 = Instance_TextDecoration
    //     0xbaee68: add             x0, PP, #0x25, lsl #12  ; [pp+0x25c20] Obj!TextDecoration@e264b1
    //     0xbaee6c: ldr             x0, [x0, #0xc20]
    // 0xbaee70: StoreField: r1->field_4b = r0
    //     0xbaee70: stur            w0, [x1, #0x4b]
    // 0xbaee74: r0 = TextSpan()
    //     0xbaee74: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbaee78: mov             x3, x0
    // 0xbaee7c: r0 = "<EMAIL>"
    //     0xbaee7c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31be8] "<EMAIL>"
    //     0xbaee80: ldr             x0, [x0, #0xbe8]
    // 0xbaee84: stur            x3, [fp, #-0x20]
    // 0xbaee88: StoreField: r3->field_b = r0
    //     0xbaee88: stur            w0, [x3, #0xb]
    // 0xbaee8c: ldur            x0, [fp, #-0x10]
    // 0xbaee90: StoreField: r3->field_13 = r0
    //     0xbaee90: stur            w0, [x3, #0x13]
    // 0xbaee94: r0 = Instance_SystemMouseCursor
    //     0xbaee94: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xbaee98: ldr             x0, [x0, #0xbf0]
    // 0xbaee9c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbaee9c: stur            w0, [x3, #0x17]
    // 0xbaeea0: ldur            x0, [fp, #-0x28]
    // 0xbaeea4: StoreField: r3->field_7 = r0
    //     0xbaeea4: stur            w0, [x3, #7]
    // 0xbaeea8: r1 = Null
    //     0xbaeea8: mov             x1, NULL
    // 0xbaeeac: r2 = 2
    //     0xbaeeac: movz            x2, #0x2
    // 0xbaeeb0: r0 = AllocateArray()
    //     0xbaeeb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbaeeb4: mov             x2, x0
    // 0xbaeeb8: ldur            x0, [fp, #-0x20]
    // 0xbaeebc: stur            x2, [fp, #-0x10]
    // 0xbaeec0: StoreField: r2->field_f = r0
    //     0xbaeec0: stur            w0, [x2, #0xf]
    // 0xbaeec4: r1 = <InlineSpan>
    //     0xbaeec4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xbaeec8: ldr             x1, [x1, #0x5f0]
    // 0xbaeecc: r0 = AllocateGrowableArray()
    //     0xbaeecc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbaeed0: mov             x1, x0
    // 0xbaeed4: ldur            x0, [fp, #-0x10]
    // 0xbaeed8: stur            x1, [fp, #-0x20]
    // 0xbaeedc: StoreField: r1->field_f = r0
    //     0xbaeedc: stur            w0, [x1, #0xf]
    // 0xbaeee0: r0 = 2
    //     0xbaeee0: movz            x0, #0x2
    // 0xbaeee4: StoreField: r1->field_b = r0
    //     0xbaeee4: stur            w0, [x1, #0xb]
    // 0xbaeee8: r0 = TextSpan()
    //     0xbaeee8: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbaeeec: mov             x1, x0
    // 0xbaeef0: r0 = "Fitur kalkulator waris ini masih dalam pengembangan. Jika Anda punya masukan, silakan laporkan ke "
    //     0xbaeef0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf8] "Fitur kalkulator waris ini masih dalam pengembangan. Jika Anda punya masukan, silakan laporkan ke "
    //     0xbaeef4: ldr             x0, [x0, #0xbf8]
    // 0xbaeef8: stur            x1, [fp, #-0x10]
    // 0xbaeefc: StoreField: r1->field_b = r0
    //     0xbaeefc: stur            w0, [x1, #0xb]
    // 0xbaef00: ldur            x0, [fp, #-0x20]
    // 0xbaef04: StoreField: r1->field_f = r0
    //     0xbaef04: stur            w0, [x1, #0xf]
    // 0xbaef08: r0 = Instance__DeferringMouseCursor
    //     0xbaef08: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xbaef0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbaef0c: stur            w0, [x1, #0x17]
    // 0xbaef10: r0 = GetNavigation.textTheme()
    //     0xbaef10: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbaef14: LoadField: r1 = r0->field_2f
    //     0xbaef14: ldur            w1, [x0, #0x2f]
    // 0xbaef18: DecompressPointer r1
    //     0xbaef18: add             x1, x1, HEAP, lsl #32
    // 0xbaef1c: cmp             w1, NULL
    // 0xbaef20: b.ne            #0xbaef2c
    // 0xbaef24: r1 = Null
    //     0xbaef24: mov             x1, NULL
    // 0xbaef28: b               #0xbaef48
    // 0xbaef2c: r16 = 16.000000
    //     0xbaef2c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbaef30: ldr             x16, [x16, #0x80]
    // 0xbaef34: str             x16, [SP]
    // 0xbaef38: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xbaef38: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xbaef3c: ldr             x4, [x4, #0x88]
    // 0xbaef40: r0 = copyWith()
    //     0xbaef40: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaef44: mov             x1, x0
    // 0xbaef48: ldur            x0, [fp, #-0x10]
    // 0xbaef4c: stur            x1, [fp, #-0x20]
    // 0xbaef50: r0 = Text()
    //     0xbaef50: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbaef54: mov             x1, x0
    // 0xbaef58: ldur            x0, [fp, #-0x10]
    // 0xbaef5c: stur            x1, [fp, #-0x28]
    // 0xbaef60: StoreField: r1->field_f = r0
    //     0xbaef60: stur            w0, [x1, #0xf]
    // 0xbaef64: ldur            x0, [fp, #-0x20]
    // 0xbaef68: StoreField: r1->field_13 = r0
    //     0xbaef68: stur            w0, [x1, #0x13]
    // 0xbaef6c: r0 = GetNavigation.textTheme()
    //     0xbaef6c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbaef70: LoadField: r1 = r0->field_2f
    //     0xbaef70: ldur            w1, [x0, #0x2f]
    // 0xbaef74: DecompressPointer r1
    //     0xbaef74: add             x1, x1, HEAP, lsl #32
    // 0xbaef78: cmp             w1, NULL
    // 0xbaef7c: b.ne            #0xbaef88
    // 0xbaef80: r0 = Null
    //     0xbaef80: mov             x0, NULL
    // 0xbaef84: b               #0xbaefa0
    // 0xbaef88: r16 = 16.000000
    //     0xbaef88: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbaef8c: ldr             x16, [x16, #0x80]
    // 0xbaef90: str             x16, [SP]
    // 0xbaef94: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xbaef94: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xbaef98: ldr             x4, [x4, #0x88]
    // 0xbaef9c: r0 = copyWith()
    //     0xbaef9c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaefa0: stur            x0, [fp, #-0x10]
    // 0xbaefa4: r0 = Text()
    //     0xbaefa4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbaefa8: mov             x1, x0
    // 0xbaefac: r0 = "Perlu diketahui juga, beberapa masalah waris yang belum masuk ke dalam sistem dan masih dalam pengembangan:"
    //     0xbaefac: add             x0, PP, #0x31, lsl #12  ; [pp+0x31c00] "Perlu diketahui juga, beberapa masalah waris yang belum masuk ke dalam sistem dan masih dalam pengembangan:"
    //     0xbaefb0: ldr             x0, [x0, #0xc00]
    // 0xbaefb4: stur            x1, [fp, #-0x20]
    // 0xbaefb8: StoreField: r1->field_b = r0
    //     0xbaefb8: stur            w0, [x1, #0xb]
    // 0xbaefbc: ldur            x0, [fp, #-0x10]
    // 0xbaefc0: StoreField: r1->field_13 = r0
    //     0xbaefc0: stur            w0, [x1, #0x13]
    // 0xbaefc4: r0 = GetNavigation.textTheme()
    //     0xbaefc4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbaefc8: LoadField: r1 = r0->field_2f
    //     0xbaefc8: ldur            w1, [x0, #0x2f]
    // 0xbaefcc: DecompressPointer r1
    //     0xbaefcc: add             x1, x1, HEAP, lsl #32
    // 0xbaefd0: cmp             w1, NULL
    // 0xbaefd4: b.ne            #0xbaefe0
    // 0xbaefd8: r4 = Null
    //     0xbaefd8: mov             x4, NULL
    // 0xbaefdc: b               #0xbaeffc
    // 0xbaefe0: r16 = 16.000000
    //     0xbaefe0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbaefe4: ldr             x16, [x16, #0x80]
    // 0xbaefe8: str             x16, [SP]
    // 0xbaefec: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xbaefec: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xbaeff0: ldr             x4, [x4, #0x88]
    // 0xbaeff4: r0 = copyWith()
    //     0xbaeff4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbaeff8: mov             x4, x0
    // 0xbaeffc: ldur            x3, [fp, #-8]
    // 0xbaf000: ldur            x2, [fp, #-0x18]
    // 0xbaf004: ldur            x1, [fp, #-0x28]
    // 0xbaf008: ldur            x0, [fp, #-0x20]
    // 0xbaf00c: stur            x4, [fp, #-0x10]
    // 0xbaf010: r0 = Text()
    //     0xbaf010: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbaf014: mov             x3, x0
    // 0xbaf018: r0 = "  •  Masalah munasakhoh\n  •  Masalah kakek dan saudara\n  •  Masalah akdariyyah\n  •  Masalah khuntsa musykil, orang yang hilang, dan janin\n"
    //     0xbaf018: add             x0, PP, #0x31, lsl #12  ; [pp+0x31c08] "  •  Masalah munasakhoh\n  •  Masalah kakek dan saudara\n  •  Masalah akdariyyah\n  •  Masalah khuntsa musykil, orang yang hilang, dan janin\n"
    //     0xbaf01c: ldr             x0, [x0, #0xc08]
    // 0xbaf020: stur            x3, [fp, #-0x30]
    // 0xbaf024: StoreField: r3->field_b = r0
    //     0xbaf024: stur            w0, [x3, #0xb]
    // 0xbaf028: ldur            x0, [fp, #-0x10]
    // 0xbaf02c: StoreField: r3->field_13 = r0
    //     0xbaf02c: stur            w0, [x3, #0x13]
    // 0xbaf030: r1 = Null
    //     0xbaf030: mov             x1, NULL
    // 0xbaf034: r2 = 12
    //     0xbaf034: movz            x2, #0xc
    // 0xbaf038: r0 = AllocateArray()
    //     0xbaf038: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbaf03c: stur            x0, [fp, #-0x10]
    // 0xbaf040: r16 = Instance_SizedBox
    //     0xbaf040: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xbaf044: ldr             x16, [x16, #0xfe8]
    // 0xbaf048: StoreField: r0->field_f = r16
    //     0xbaf048: stur            w16, [x0, #0xf]
    // 0xbaf04c: ldur            x1, [fp, #-0x18]
    // 0xbaf050: StoreField: r0->field_13 = r1
    //     0xbaf050: stur            w1, [x0, #0x13]
    // 0xbaf054: ldur            x1, [fp, #-0x28]
    // 0xbaf058: ArrayStore: r0[0] = r1  ; List_4
    //     0xbaf058: stur            w1, [x0, #0x17]
    // 0xbaf05c: r16 = Instance_SizedBox
    //     0xbaf05c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xbaf060: ldr             x16, [x16, #0x540]
    // 0xbaf064: StoreField: r0->field_1b = r16
    //     0xbaf064: stur            w16, [x0, #0x1b]
    // 0xbaf068: ldur            x1, [fp, #-0x20]
    // 0xbaf06c: StoreField: r0->field_1f = r1
    //     0xbaf06c: stur            w1, [x0, #0x1f]
    // 0xbaf070: ldur            x1, [fp, #-0x30]
    // 0xbaf074: StoreField: r0->field_23 = r1
    //     0xbaf074: stur            w1, [x0, #0x23]
    // 0xbaf078: r1 = <Widget>
    //     0xbaf078: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbaf07c: r0 = AllocateGrowableArray()
    //     0xbaf07c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbaf080: mov             x1, x0
    // 0xbaf084: ldur            x0, [fp, #-0x10]
    // 0xbaf088: stur            x1, [fp, #-0x18]
    // 0xbaf08c: StoreField: r1->field_f = r0
    //     0xbaf08c: stur            w0, [x1, #0xf]
    // 0xbaf090: r0 = 12
    //     0xbaf090: movz            x0, #0xc
    // 0xbaf094: StoreField: r1->field_b = r0
    //     0xbaf094: stur            w0, [x1, #0xb]
    // 0xbaf098: r0 = Column()
    //     0xbaf098: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbaf09c: mov             x1, x0
    // 0xbaf0a0: r0 = Instance_Axis
    //     0xbaf0a0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbaf0a4: stur            x1, [fp, #-0x10]
    // 0xbaf0a8: StoreField: r1->field_f = r0
    //     0xbaf0a8: stur            w0, [x1, #0xf]
    // 0xbaf0ac: r2 = Instance_MainAxisAlignment
    //     0xbaf0ac: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbaf0b0: ldr             x2, [x2, #0x730]
    // 0xbaf0b4: StoreField: r1->field_13 = r2
    //     0xbaf0b4: stur            w2, [x1, #0x13]
    // 0xbaf0b8: r2 = Instance_MainAxisSize
    //     0xbaf0b8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbaf0bc: ldr             x2, [x2, #0x738]
    // 0xbaf0c0: ArrayStore: r1[0] = r2  ; List_4
    //     0xbaf0c0: stur            w2, [x1, #0x17]
    // 0xbaf0c4: r2 = Instance_CrossAxisAlignment
    //     0xbaf0c4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xbaf0c8: ldr             x2, [x2, #0x68]
    // 0xbaf0cc: StoreField: r1->field_1b = r2
    //     0xbaf0cc: stur            w2, [x1, #0x1b]
    // 0xbaf0d0: r2 = Instance_VerticalDirection
    //     0xbaf0d0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbaf0d4: ldr             x2, [x2, #0x748]
    // 0xbaf0d8: StoreField: r1->field_23 = r2
    //     0xbaf0d8: stur            w2, [x1, #0x23]
    // 0xbaf0dc: r2 = Instance_Clip
    //     0xbaf0dc: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbaf0e0: ldr             x2, [x2, #0x750]
    // 0xbaf0e4: StoreField: r1->field_2b = r2
    //     0xbaf0e4: stur            w2, [x1, #0x2b]
    // 0xbaf0e8: StoreField: r1->field_2f = rZR
    //     0xbaf0e8: stur            xzr, [x1, #0x2f]
    // 0xbaf0ec: ldur            x2, [fp, #-0x18]
    // 0xbaf0f0: StoreField: r1->field_b = r2
    //     0xbaf0f0: stur            w2, [x1, #0xb]
    // 0xbaf0f4: r0 = SingleChildScrollView()
    //     0xbaf0f4: bl              #0xa06cec  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbaf0f8: mov             x1, x0
    // 0xbaf0fc: r0 = Instance_Axis
    //     0xbaf0fc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbaf100: StoreField: r1->field_b = r0
    //     0xbaf100: stur            w0, [x1, #0xb]
    // 0xbaf104: r0 = false
    //     0xbaf104: add             x0, NULL, #0x30  ; false
    // 0xbaf108: StoreField: r1->field_f = r0
    //     0xbaf108: stur            w0, [x1, #0xf]
    // 0xbaf10c: ldur            x0, [fp, #-0x10]
    // 0xbaf110: StoreField: r1->field_23 = r0
    //     0xbaf110: stur            w0, [x1, #0x23]
    // 0xbaf114: r0 = Instance_DragStartBehavior
    //     0xbaf114: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbaf118: StoreField: r1->field_27 = r0
    //     0xbaf118: stur            w0, [x1, #0x27]
    // 0xbaf11c: r0 = Instance_Clip
    //     0xbaf11c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xbaf120: ldr             x0, [x0, #0x7c0]
    // 0xbaf124: StoreField: r1->field_2b = r0
    //     0xbaf124: stur            w0, [x1, #0x2b]
    // 0xbaf128: r0 = Instance_HitTestBehavior
    //     0xbaf128: add             x0, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xbaf12c: ldr             x0, [x0, #0x1c8]
    // 0xbaf130: StoreField: r1->field_2f = r0
    //     0xbaf130: stur            w0, [x1, #0x2f]
    // 0xbaf134: r0 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xbaf134: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xbaf138: ldr             x0, [x0, #0xf00]
    // 0xbaf13c: StoreField: r1->field_37 = r0
    //     0xbaf13c: stur            w0, [x1, #0x37]
    // 0xbaf140: ldur            x0, [fp, #-8]
    // 0xbaf144: LoadField: r2 = r0->field_b
    //     0xbaf144: ldur            w2, [x0, #0xb]
    // 0xbaf148: DecompressPointer r2
    //     0xbaf148: add             x2, x2, HEAP, lsl #32
    // 0xbaf14c: stp             x1, x2, [SP]
    // 0xbaf150: mov             x0, x2
    // 0xbaf154: ClosureCall
    //     0xbaf154: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xbaf158: ldur            x2, [x0, #0x1f]
    //     0xbaf15c: blr             x2
    // 0xbaf160: LeaveFrame
    //     0xbaf160: mov             SP, fp
    //     0xbaf164: ldp             fp, lr, [SP], #0x10
    // 0xbaf168: ret
    //     0xbaf168: ret             
    // 0xbaf16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaf16c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaf170: b               #0xbaed38
  }
  [closure] Future<void> onMailToTapped(dynamic) {
    // ** addr: 0xbaf174, size: 0x38
    // 0xbaf174: EnterFrame
    //     0xbaf174: stp             fp, lr, [SP, #-0x10]!
    //     0xbaf178: mov             fp, SP
    // 0xbaf17c: ldr             x0, [fp, #0x10]
    // 0xbaf180: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbaf180: ldur            w1, [x0, #0x17]
    // 0xbaf184: DecompressPointer r1
    //     0xbaf184: add             x1, x1, HEAP, lsl #32
    // 0xbaf188: CheckStackOverflow
    //     0xbaf188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaf18c: cmp             SP, x16
    //     0xbaf190: b.ls            #0xbaf1a4
    // 0xbaf194: r0 = onMailToTapped()
    //     0xbaf194: bl              #0xbaf1ac  ; [package:nuonline/app/modules/waris/views/waris_info_view.dart] WarisInfoView::onMailToTapped
    // 0xbaf198: LeaveFrame
    //     0xbaf198: mov             SP, fp
    //     0xbaf19c: ldp             fp, lr, [SP], #0x10
    // 0xbaf1a0: ret
    //     0xbaf1a0: ret             
    // 0xbaf1a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaf1a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaf1a8: b               #0xbaf194
  }
  _ onMailToTapped(/* No info */) async {
    // ** addr: 0xbaf1ac, size: 0xc0
    // 0xbaf1ac: EnterFrame
    //     0xbaf1ac: stp             fp, lr, [SP, #-0x10]!
    //     0xbaf1b0: mov             fp, SP
    // 0xbaf1b4: AllocStack(0x30)
    //     0xbaf1b4: sub             SP, SP, #0x30
    // 0xbaf1b8: SetupParameters(WarisInfoView this /* r1 => r1, fp-0x10 */)
    //     0xbaf1b8: stur            NULL, [fp, #-8]
    //     0xbaf1bc: stur            x1, [fp, #-0x10]
    // 0xbaf1c0: CheckStackOverflow
    //     0xbaf1c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaf1c4: cmp             SP, x16
    //     0xbaf1c8: b.ls            #0xbaf264
    // 0xbaf1cc: InitAsync() -> Future<void?>
    //     0xbaf1cc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbaf1d0: bl              #0x661298  ; InitAsyncStub
    // 0xbaf1d4: r1 = "mailto:<EMAIL>\?subject=MASUKAN%20FITUR%20WARIS"
    //     0xbaf1d4: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c10] "mailto:<EMAIL>\?subject=MASUKAN%20FITUR%20WARIS"
    //     0xbaf1d8: ldr             x1, [x1, #0xc10]
    // 0xbaf1dc: r0 = canLaunchUrlString()
    //     0xbaf1dc: bl              #0x7da07c  ; [package:url_launcher/src/url_launcher_string.dart] ::canLaunchUrlString
    // 0xbaf1e0: mov             x1, x0
    // 0xbaf1e4: stur            x1, [fp, #-0x18]
    // 0xbaf1e8: r0 = Await()
    //     0xbaf1e8: bl              #0x661044  ; AwaitStub
    // 0xbaf1ec: r16 = true
    //     0xbaf1ec: add             x16, NULL, #0x20  ; true
    // 0xbaf1f0: cmp             w0, w16
    // 0xbaf1f4: b.ne            #0xbaf224
    // 0xbaf1f8: r16 = Instance_LaunchMode
    //     0xbaf1f8: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a470] Obj!LaunchMode@e2dee1
    //     0xbaf1fc: ldr             x16, [x16, #0x470]
    // 0xbaf200: str             x16, [SP]
    // 0xbaf204: r1 = "mailto:<EMAIL>\?subject=MASUKAN%20FITUR%20WARIS"
    //     0xbaf204: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c10] "mailto:<EMAIL>\?subject=MASUKAN%20FITUR%20WARIS"
    //     0xbaf208: ldr             x1, [x1, #0xc10]
    // 0xbaf20c: r4 = const [0, 0x2, 0x1, 0x1, mode, 0x1, null]
    //     0xbaf20c: ldr             x4, [PP, #0x1cd8]  ; [pp+0x1cd8] List(7) [0, 0x2, 0x1, 0x1, "mode", 0x1, Null]
    // 0xbaf210: r0 = launchUrlString()
    //     0xbaf210: bl              #0x7d9cc8  ; [package:url_launcher/src/url_launcher_string.dart] ::launchUrlString
    // 0xbaf214: mov             x1, x0
    // 0xbaf218: stur            x1, [fp, #-0x10]
    // 0xbaf21c: r0 = Await()
    //     0xbaf21c: bl              #0x661044  ; AwaitStub
    // 0xbaf220: b               #0xbaf25c
    // 0xbaf224: r1 = Instance_ClipboardData
    //     0xbaf224: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbd8] Obj!ClipboardData@e11631
    //     0xbaf228: ldr             x1, [x1, #0xbd8]
    // 0xbaf22c: r0 = setData()
    //     0xbaf22c: bl              #0xa05344  ; [package:flutter/src/services/clipboard.dart] Clipboard::setData
    // 0xbaf230: r1 = Function '<anonymous closure>':.
    //     0xbaf230: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c18] AnonymousClosure: (0xb51da8), in [package:nuonline/app/modules/setting/controllers/setting_controller.dart] SettingController::onMailToTapped (0xb51ce8)
    //     0xbaf234: ldr             x1, [x1, #0xc18]
    // 0xbaf238: r2 = Null
    //     0xbaf238: mov             x2, NULL
    // 0xbaf23c: stur            x0, [fp, #-0x10]
    // 0xbaf240: r0 = AllocateClosure()
    //     0xbaf240: bl              #0xec1630  ; AllocateClosureStub
    // 0xbaf244: r16 = <void?>
    //     0xbaf244: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xbaf248: ldur            lr, [fp, #-0x10]
    // 0xbaf24c: stp             lr, x16, [SP, #8]
    // 0xbaf250: str             x0, [SP]
    // 0xbaf254: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbaf254: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbaf258: r0 = then()
    //     0xbaf258: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xbaf25c: r0 = Null
    //     0xbaf25c: mov             x0, NULL
    // 0xbaf260: r0 = ReturnAsyncNotFuture()
    //     0xbaf260: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbaf264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaf264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaf268: b               #0xbaf1cc
  }
}
