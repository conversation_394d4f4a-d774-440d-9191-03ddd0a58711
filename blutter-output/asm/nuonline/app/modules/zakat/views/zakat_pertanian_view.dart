// lib: , url: package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart

// class id: 1050660, size: 0x8
class :: {
}

// class id: 4929, size: 0x18, field offset: 0xc
//   const constructor, 
class TagButton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb404c, size: 0x2f8
    // 0xbb404c: EnterFrame
    //     0xbb404c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb4050: mov             fp, SP
    // 0xbb4054: AllocStack(0x48)
    //     0xbb4054: sub             SP, SP, #0x48
    // 0xbb4058: SetupParameters(TagButton this /* r1 => r1, fp-0x10 */)
    //     0xbb4058: stur            x1, [fp, #-0x10]
    // 0xbb405c: CheckStackOverflow
    //     0xbb405c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb4060: cmp             SP, x16
    //     0xbb4064: b.ls            #0xbb4338
    // 0xbb4068: LoadField: r0 = r1->field_f
    //     0xbb4068: ldur            w0, [x1, #0xf]
    // 0xbb406c: DecompressPointer r0
    //     0xbb406c: add             x0, x0, HEAP, lsl #32
    // 0xbb4070: stur            x0, [fp, #-8]
    // 0xbb4074: r0 = Radius()
    //     0xbb4074: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xbb4078: d0 = 16.000000
    //     0xbb4078: fmov            d0, #16.00000000
    // 0xbb407c: stur            x0, [fp, #-0x18]
    // 0xbb4080: StoreField: r0->field_7 = d0
    //     0xbb4080: stur            d0, [x0, #7]
    // 0xbb4084: StoreField: r0->field_f = d0
    //     0xbb4084: stur            d0, [x0, #0xf]
    // 0xbb4088: r0 = BorderRadius()
    //     0xbb4088: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xbb408c: mov             x3, x0
    // 0xbb4090: ldur            x0, [fp, #-0x18]
    // 0xbb4094: stur            x3, [fp, #-0x20]
    // 0xbb4098: StoreField: r3->field_7 = r0
    //     0xbb4098: stur            w0, [x3, #7]
    // 0xbb409c: StoreField: r3->field_b = r0
    //     0xbb409c: stur            w0, [x3, #0xb]
    // 0xbb40a0: StoreField: r3->field_f = r0
    //     0xbb40a0: stur            w0, [x3, #0xf]
    // 0xbb40a4: StoreField: r3->field_13 = r0
    //     0xbb40a4: stur            w0, [x3, #0x13]
    // 0xbb40a8: ldur            x0, [fp, #-0x10]
    // 0xbb40ac: LoadField: r4 = r0->field_13
    //     0xbb40ac: ldur            w4, [x0, #0x13]
    // 0xbb40b0: DecompressPointer r4
    //     0xbb40b0: add             x4, x4, HEAP, lsl #32
    // 0xbb40b4: stur            x4, [fp, #-0x18]
    // 0xbb40b8: r1 = _ConstMap len:10
    //     0xbb40b8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xbb40bc: ldr             x1, [x1, #0xc08]
    // 0xbb40c0: r2 = 100
    //     0xbb40c0: movz            x2, #0x64
    // 0xbb40c4: r0 = []()
    //     0xbb40c4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbb40c8: r1 = _ConstMap len:10
    //     0xbb40c8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xbb40cc: ldr             x1, [x1, #0xc08]
    // 0xbb40d0: r2 = 1800
    //     0xbb40d0: movz            x2, #0x708
    // 0xbb40d4: stur            x0, [fp, #-0x28]
    // 0xbb40d8: r0 = []()
    //     0xbb40d8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbb40dc: r16 = <Color?>
    //     0xbb40dc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xbb40e0: ldr             x16, [x16, #0x98]
    // 0xbb40e4: stp             x0, x16, [SP, #8]
    // 0xbb40e8: ldur            x16, [fp, #-0x28]
    // 0xbb40ec: str             x16, [SP]
    // 0xbb40f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbb40f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbb40f4: r0 = mode()
    //     0xbb40f4: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xbb40f8: r1 = _ConstMap len:3
    //     0xbb40f8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xbb40fc: ldr             x1, [x1, #0xbe8]
    // 0xbb4100: r2 = 2
    //     0xbb4100: movz            x2, #0x2
    // 0xbb4104: stur            x0, [fp, #-0x28]
    // 0xbb4108: r0 = []()
    //     0xbb4108: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbb410c: r16 = <Color?>
    //     0xbb410c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xbb4110: ldr             x16, [x16, #0x98]
    // 0xbb4114: stp             x0, x16, [SP, #8]
    // 0xbb4118: r16 = Instance_MaterialColor
    //     0xbb4118: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xbb411c: ldr             x16, [x16, #0xe38]
    // 0xbb4120: str             x16, [SP]
    // 0xbb4124: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbb4124: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbb4128: r0 = mode()
    //     0xbb4128: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xbb412c: mov             x1, x0
    // 0xbb4130: ldur            x0, [fp, #-0x18]
    // 0xbb4134: tbnz            w0, #4, #0xbb4140
    // 0xbb4138: ldur            x3, [fp, #-0x28]
    // 0xbb413c: b               #0xbb4144
    // 0xbb4140: mov             x3, x1
    // 0xbb4144: stur            x3, [fp, #-0x28]
    // 0xbb4148: r1 = _ConstMap len:10
    //     0xbb4148: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xbb414c: ldr             x1, [x1, #0xc08]
    // 0xbb4150: r2 = 600
    //     0xbb4150: movz            x2, #0x258
    // 0xbb4154: r0 = []()
    //     0xbb4154: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbb4158: cmp             w0, NULL
    // 0xbb415c: b.eq            #0xbb4340
    // 0xbb4160: r16 = <Color>
    //     0xbb4160: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xbb4164: ldr             x16, [x16, #0x158]
    // 0xbb4168: stp             x0, x16, [SP, #8]
    // 0xbb416c: r16 = Instance_MaterialColor
    //     0xbb416c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xbb4170: ldr             x16, [x16, #0xcf0]
    // 0xbb4174: str             x16, [SP]
    // 0xbb4178: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbb4178: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbb417c: r0 = mode()
    //     0xbb417c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xbb4180: str             x0, [SP]
    // 0xbb4184: r1 = Null
    //     0xbb4184: mov             x1, NULL
    // 0xbb4188: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xbb4188: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xbb418c: ldr             x4, [x4, #0x228]
    // 0xbb4190: r0 = Border.all()
    //     0xbb4190: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbb4194: mov             x1, x0
    // 0xbb4198: ldur            x0, [fp, #-0x18]
    // 0xbb419c: tbnz            w0, #4, #0xbb41a8
    // 0xbb41a0: mov             x3, x1
    // 0xbb41a4: b               #0xbb41ac
    // 0xbb41a8: r3 = Null
    //     0xbb41a8: mov             x3, NULL
    // 0xbb41ac: ldur            x2, [fp, #-0x10]
    // 0xbb41b0: ldur            x1, [fp, #-0x20]
    // 0xbb41b4: ldur            x0, [fp, #-0x28]
    // 0xbb41b8: stur            x3, [fp, #-0x18]
    // 0xbb41bc: r0 = BoxDecoration()
    //     0xbb41bc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb41c0: mov             x1, x0
    // 0xbb41c4: ldur            x0, [fp, #-0x28]
    // 0xbb41c8: stur            x1, [fp, #-0x30]
    // 0xbb41cc: StoreField: r1->field_7 = r0
    //     0xbb41cc: stur            w0, [x1, #7]
    // 0xbb41d0: ldur            x0, [fp, #-0x18]
    // 0xbb41d4: StoreField: r1->field_f = r0
    //     0xbb41d4: stur            w0, [x1, #0xf]
    // 0xbb41d8: ldur            x0, [fp, #-0x20]
    // 0xbb41dc: StoreField: r1->field_13 = r0
    //     0xbb41dc: stur            w0, [x1, #0x13]
    // 0xbb41e0: r0 = Instance_BoxShape
    //     0xbb41e0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xbb41e4: ldr             x0, [x0, #0xca8]
    // 0xbb41e8: StoreField: r1->field_23 = r0
    //     0xbb41e8: stur            w0, [x1, #0x23]
    // 0xbb41ec: ldur            x0, [fp, #-0x10]
    // 0xbb41f0: LoadField: r2 = r0->field_b
    //     0xbb41f0: ldur            w2, [x0, #0xb]
    // 0xbb41f4: DecompressPointer r2
    //     0xbb41f4: add             x2, x2, HEAP, lsl #32
    // 0xbb41f8: stur            x2, [fp, #-0x18]
    // 0xbb41fc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbb41fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb4200: ldr             x0, [x0, #0x2670]
    //     0xbb4204: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb4208: cmp             w0, w16
    //     0xbb420c: b.ne            #0xbb4218
    //     0xbb4210: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbb4214: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbb4218: r0 = GetNavigation.textTheme()
    //     0xbb4218: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbb421c: LoadField: r1 = r0->field_27
    //     0xbb421c: ldur            w1, [x0, #0x27]
    // 0xbb4220: DecompressPointer r1
    //     0xbb4220: add             x1, x1, HEAP, lsl #32
    // 0xbb4224: stur            x1, [fp, #-0x10]
    // 0xbb4228: cmp             w1, NULL
    // 0xbb422c: b.ne            #0xbb4238
    // 0xbb4230: r1 = Null
    //     0xbb4230: mov             x1, NULL
    // 0xbb4234: b               #0xbb4288
    // 0xbb4238: r16 = <Color?>
    //     0xbb4238: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xbb423c: ldr             x16, [x16, #0x98]
    // 0xbb4240: r30 = Instance_Color
    //     0xbb4240: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xbb4244: stp             lr, x16, [SP, #8]
    // 0xbb4248: r16 = Instance_MaterialColor
    //     0xbb4248: add             x16, PP, #0x23, lsl #12  ; [pp+0x23bf0] Obj!MaterialColor@e2baf1
    //     0xbb424c: ldr             x16, [x16, #0xbf0]
    // 0xbb4250: str             x16, [SP]
    // 0xbb4254: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbb4254: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbb4258: r0 = mode()
    //     0xbb4258: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xbb425c: r16 = 14.000000
    //     0xbb425c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xbb4260: ldr             x16, [x16, #0x9a0]
    // 0xbb4264: r30 = 1.500000
    //     0xbb4264: add             lr, PP, #0x23, lsl #12  ; [pp+0x23c58] 1.5
    //     0xbb4268: ldr             lr, [lr, #0xc58]
    // 0xbb426c: stp             lr, x16, [SP, #8]
    // 0xbb4270: str             x0, [SP]
    // 0xbb4274: ldur            x1, [fp, #-0x10]
    // 0xbb4278: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x1, height, 0x2, null]
    //     0xbb4278: add             x4, PP, #0x31, lsl #12  ; [pp+0x31528] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x1, "height", 0x2, Null]
    //     0xbb427c: ldr             x4, [x4, #0x528]
    // 0xbb4280: r0 = copyWith()
    //     0xbb4280: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb4284: mov             x1, x0
    // 0xbb4288: ldur            x0, [fp, #-0x18]
    // 0xbb428c: stur            x1, [fp, #-0x10]
    // 0xbb4290: r0 = Text()
    //     0xbb4290: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbb4294: mov             x1, x0
    // 0xbb4298: ldur            x0, [fp, #-0x18]
    // 0xbb429c: stur            x1, [fp, #-0x20]
    // 0xbb42a0: StoreField: r1->field_b = r0
    //     0xbb42a0: stur            w0, [x1, #0xb]
    // 0xbb42a4: ldur            x0, [fp, #-0x10]
    // 0xbb42a8: StoreField: r1->field_13 = r0
    //     0xbb42a8: stur            w0, [x1, #0x13]
    // 0xbb42ac: r0 = Instance__LinearTextScaler
    //     0xbb42ac: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xbb42b0: StoreField: r1->field_33 = r0
    //     0xbb42b0: stur            w0, [x1, #0x33]
    // 0xbb42b4: r0 = Padding()
    //     0xbb42b4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb42b8: mov             x1, x0
    // 0xbb42bc: r0 = Instance_EdgeInsets
    //     0xbb42bc: add             x0, PP, #0x31, lsl #12  ; [pp+0x31530] Obj!EdgeInsets@e12251
    //     0xbb42c0: ldr             x0, [x0, #0x530]
    // 0xbb42c4: stur            x1, [fp, #-0x10]
    // 0xbb42c8: StoreField: r1->field_f = r0
    //     0xbb42c8: stur            w0, [x1, #0xf]
    // 0xbb42cc: ldur            x0, [fp, #-0x20]
    // 0xbb42d0: StoreField: r1->field_b = r0
    //     0xbb42d0: stur            w0, [x1, #0xb]
    // 0xbb42d4: r0 = Container()
    //     0xbb42d4: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb42d8: stur            x0, [fp, #-0x18]
    // 0xbb42dc: r16 = 32.000000
    //     0xbb42dc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25d88] 32
    //     0xbb42e0: ldr             x16, [x16, #0xd88]
    // 0xbb42e4: ldur            lr, [fp, #-0x30]
    // 0xbb42e8: stp             lr, x16, [SP, #8]
    // 0xbb42ec: ldur            x16, [fp, #-0x10]
    // 0xbb42f0: str             x16, [SP]
    // 0xbb42f4: mov             x1, x0
    // 0xbb42f8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbb42f8: add             x4, PP, #0x31, lsl #12  ; [pp+0x31538] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbb42fc: ldr             x4, [x4, #0x538]
    // 0xbb4300: r0 = Container()
    //     0xbb4300: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb4304: r0 = GestureDetector()
    //     0xbb4304: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbb4308: stur            x0, [fp, #-0x10]
    // 0xbb430c: ldur            x16, [fp, #-8]
    // 0xbb4310: ldur            lr, [fp, #-0x18]
    // 0xbb4314: stp             lr, x16, [SP]
    // 0xbb4318: mov             x1, x0
    // 0xbb431c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbb431c: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbb4320: ldr             x4, [x4, #0x7d0]
    // 0xbb4324: r0 = GestureDetector()
    //     0xbb4324: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbb4328: ldur            x0, [fp, #-0x10]
    // 0xbb432c: LeaveFrame
    //     0xbb432c: mov             SP, fp
    //     0xbb4330: ldp             fp, lr, [SP], #0x10
    // 0xbb4334: ret
    //     0xbb4334: ret             
    // 0xbb4338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb4338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb433c: b               #0xbb4068
    // 0xbb4340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4340: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5163, size: 0x14, field offset: 0x14
class ZakatPertanianView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb66d54, size: 0xbb0
    // 0xb66d54: EnterFrame
    //     0xb66d54: stp             fp, lr, [SP, #-0x10]!
    //     0xb66d58: mov             fp, SP
    // 0xb66d5c: AllocStack(0x60)
    //     0xb66d5c: sub             SP, SP, #0x60
    // 0xb66d60: SetupParameters(ZakatPertanianView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb66d60: stur            x1, [fp, #-8]
    //     0xb66d64: stur            x2, [fp, #-0x10]
    // 0xb66d68: CheckStackOverflow
    //     0xb66d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66d6c: cmp             SP, x16
    //     0xb66d70: b.ls            #0xb678fc
    // 0xb66d74: r1 = 2
    //     0xb66d74: movz            x1, #0x2
    // 0xb66d78: r0 = AllocateContext()
    //     0xb66d78: bl              #0xec126c  ; AllocateContextStub
    // 0xb66d7c: mov             x3, x0
    // 0xb66d80: ldur            x0, [fp, #-8]
    // 0xb66d84: stur            x3, [fp, #-0x18]
    // 0xb66d88: StoreField: r3->field_f = r0
    //     0xb66d88: stur            w0, [x3, #0xf]
    // 0xb66d8c: ldur            x1, [fp, #-0x10]
    // 0xb66d90: StoreField: r3->field_13 = r1
    //     0xb66d90: stur            w1, [x3, #0x13]
    // 0xb66d94: r1 = Function '<anonymous closure>':.
    //     0xb66d94: add             x1, PP, #0x27, lsl #12  ; [pp+0x27478] AnonymousClosure: (0xb68f50), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb66d98: ldr             x1, [x1, #0x478]
    // 0xb66d9c: r2 = Null
    //     0xb66d9c: mov             x2, NULL
    // 0xb66da0: r0 = AllocateClosure()
    //     0xb66da0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb66da4: stur            x0, [fp, #-0x10]
    // 0xb66da8: r0 = IconButton()
    //     0xb66da8: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb66dac: mov             x3, x0
    // 0xb66db0: ldur            x0, [fp, #-0x10]
    // 0xb66db4: stur            x3, [fp, #-0x20]
    // 0xb66db8: StoreField: r3->field_3b = r0
    //     0xb66db8: stur            w0, [x3, #0x3b]
    // 0xb66dbc: r0 = false
    //     0xb66dbc: add             x0, NULL, #0x30  ; false
    // 0xb66dc0: StoreField: r3->field_47 = r0
    //     0xb66dc0: stur            w0, [x3, #0x47]
    // 0xb66dc4: r1 = Instance_Icon
    //     0xb66dc4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f70] Obj!Icon@e24431
    //     0xb66dc8: ldr             x1, [x1, #0xf70]
    // 0xb66dcc: StoreField: r3->field_1f = r1
    //     0xb66dcc: stur            w1, [x3, #0x1f]
    // 0xb66dd0: r1 = Instance__IconButtonVariant
    //     0xb66dd0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb66dd4: ldr             x1, [x1, #0xf78]
    // 0xb66dd8: StoreField: r3->field_63 = r1
    //     0xb66dd8: stur            w1, [x3, #0x63]
    // 0xb66ddc: r1 = Null
    //     0xb66ddc: mov             x1, NULL
    // 0xb66de0: r2 = 2
    //     0xb66de0: movz            x2, #0x2
    // 0xb66de4: r0 = AllocateArray()
    //     0xb66de4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb66de8: mov             x2, x0
    // 0xb66dec: ldur            x0, [fp, #-0x20]
    // 0xb66df0: stur            x2, [fp, #-0x10]
    // 0xb66df4: StoreField: r2->field_f = r0
    //     0xb66df4: stur            w0, [x2, #0xf]
    // 0xb66df8: r1 = <Widget>
    //     0xb66df8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb66dfc: r0 = AllocateGrowableArray()
    //     0xb66dfc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb66e00: mov             x1, x0
    // 0xb66e04: ldur            x0, [fp, #-0x10]
    // 0xb66e08: stur            x1, [fp, #-0x20]
    // 0xb66e0c: StoreField: r1->field_f = r0
    //     0xb66e0c: stur            w0, [x1, #0xf]
    // 0xb66e10: r0 = 2
    //     0xb66e10: movz            x0, #0x2
    // 0xb66e14: StoreField: r1->field_b = r0
    //     0xb66e14: stur            w0, [x1, #0xb]
    // 0xb66e18: r0 = AppBar()
    //     0xb66e18: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xb66e1c: stur            x0, [fp, #-0x10]
    // 0xb66e20: r16 = Instance_Text
    //     0xb66e20: add             x16, PP, #0x27, lsl #12  ; [pp+0x27480] Obj!Text@e23581
    //     0xb66e24: ldr             x16, [x16, #0x480]
    // 0xb66e28: ldur            lr, [fp, #-0x20]
    // 0xb66e2c: stp             lr, x16, [SP]
    // 0xb66e30: mov             x1, x0
    // 0xb66e34: r4 = const [0, 0x3, 0x2, 0x1, actions, 0x2, title, 0x1, null]
    //     0xb66e34: add             x4, PP, #0x26, lsl #12  ; [pp+0x26f88] List(9) [0, 0x3, 0x2, 0x1, "actions", 0x2, "title", 0x1, Null]
    //     0xb66e38: ldr             x4, [x4, #0xf88]
    // 0xb66e3c: r0 = AppBar()
    //     0xb66e3c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xb66e40: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb66e40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb66e44: ldr             x0, [x0, #0x2670]
    //     0xb66e48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb66e4c: cmp             w0, w16
    //     0xb66e50: b.ne            #0xb66e5c
    //     0xb66e54: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb66e58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb66e5c: r0 = GetNavigation.isDarkMode()
    //     0xb66e5c: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb66e60: tbnz            w0, #4, #0xb66e78
    // 0xb66e64: r1 = _ConstMap len:3
    //     0xb66e64: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb66e68: ldr             x1, [x1, #0xbe8]
    // 0xb66e6c: r2 = 6
    //     0xb66e6c: movz            x2, #0x6
    // 0xb66e70: r0 = []()
    //     0xb66e70: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb66e74: b               #0xb66e80
    // 0xb66e78: r0 = Instance_MaterialColor
    //     0xb66e78: add             x0, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb66e7c: ldr             x0, [x0, #0xe38]
    // 0xb66e80: stur            x0, [fp, #-0x20]
    // 0xb66e84: r0 = GetNavigation.theme()
    //     0xb66e84: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb66e88: LoadField: r1 = r0->field_37
    //     0xb66e88: ldur            w1, [x0, #0x37]
    // 0xb66e8c: DecompressPointer r1
    //     0xb66e8c: add             x1, x1, HEAP, lsl #32
    // 0xb66e90: stur            x1, [fp, #-0x28]
    // 0xb66e94: r0 = Obx()
    //     0xb66e94: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb66e98: ldur            x2, [fp, #-0x18]
    // 0xb66e9c: r1 = Function '<anonymous closure>':.
    //     0xb66e9c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27488] AnonymousClosure: (0xb68968), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb66ea0: ldr             x1, [x1, #0x488]
    // 0xb66ea4: stur            x0, [fp, #-0x30]
    // 0xb66ea8: r0 = AllocateClosure()
    //     0xb66ea8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb66eac: mov             x1, x0
    // 0xb66eb0: ldur            x0, [fp, #-0x30]
    // 0xb66eb4: StoreField: r0->field_b = r1
    //     0xb66eb4: stur            w1, [x0, #0xb]
    // 0xb66eb8: r0 = Padding()
    //     0xb66eb8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb66ebc: mov             x1, x0
    // 0xb66ec0: r0 = Instance_EdgeInsets
    //     0xb66ec0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26fa8] Obj!EdgeInsets@e12a61
    //     0xb66ec4: ldr             x0, [x0, #0xfa8]
    // 0xb66ec8: stur            x1, [fp, #-0x38]
    // 0xb66ecc: StoreField: r1->field_f = r0
    //     0xb66ecc: stur            w0, [x1, #0xf]
    // 0xb66ed0: ldur            x0, [fp, #-0x30]
    // 0xb66ed4: StoreField: r1->field_b = r0
    //     0xb66ed4: stur            w0, [x1, #0xb]
    // 0xb66ed8: r0 = ColoredBox()
    //     0xb66ed8: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb66edc: mov             x1, x0
    // 0xb66ee0: ldur            x0, [fp, #-0x28]
    // 0xb66ee4: stur            x1, [fp, #-0x30]
    // 0xb66ee8: StoreField: r1->field_f = r0
    //     0xb66ee8: stur            w0, [x1, #0xf]
    // 0xb66eec: ldur            x0, [fp, #-0x38]
    // 0xb66ef0: StoreField: r1->field_b = r0
    //     0xb66ef0: stur            w0, [x1, #0xb]
    // 0xb66ef4: r0 = GetNavigation.theme()
    //     0xb66ef4: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb66ef8: LoadField: r1 = r0->field_37
    //     0xb66ef8: ldur            w1, [x0, #0x37]
    // 0xb66efc: DecompressPointer r1
    //     0xb66efc: add             x1, x1, HEAP, lsl #32
    // 0xb66f00: stur            x1, [fp, #-0x28]
    // 0xb66f04: r0 = NArticleHeader()
    //     0xb66f04: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xb66f08: mov             x3, x0
    // 0xb66f0c: r0 = "Komponen yang Dihitung"
    //     0xb66f0c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27490] "Komponen yang Dihitung"
    //     0xb66f10: ldr             x0, [x0, #0x490]
    // 0xb66f14: stur            x3, [fp, #-0x38]
    // 0xb66f18: StoreField: r3->field_b = r0
    //     0xb66f18: stur            w0, [x3, #0xb]
    // 0xb66f1c: r0 = false
    //     0xb66f1c: add             x0, NULL, #0x30  ; false
    // 0xb66f20: StoreField: r3->field_13 = r0
    //     0xb66f20: stur            w0, [x3, #0x13]
    // 0xb66f24: r1 = <Widget>
    //     0xb66f24: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb66f28: r2 = 38
    //     0xb66f28: movz            x2, #0x26
    // 0xb66f2c: r0 = AllocateArray()
    //     0xb66f2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb66f30: mov             x1, x0
    // 0xb66f34: ldur            x0, [fp, #-0x38]
    // 0xb66f38: stur            x1, [fp, #-0x40]
    // 0xb66f3c: StoreField: r1->field_f = r0
    //     0xb66f3c: stur            w0, [x1, #0xf]
    // 0xb66f40: r16 = Instance_SizedBox
    //     0xb66f40: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb66f44: ldr             x16, [x16, #0xfb0]
    // 0xb66f48: StoreField: r1->field_13 = r16
    //     0xb66f48: stur            w16, [x1, #0x13]
    // 0xb66f4c: r0 = GetNavigation.textTheme()
    //     0xb66f4c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb66f50: LoadField: r1 = r0->field_27
    //     0xb66f50: ldur            w1, [x0, #0x27]
    // 0xb66f54: DecompressPointer r1
    //     0xb66f54: add             x1, x1, HEAP, lsl #32
    // 0xb66f58: stur            x1, [fp, #-0x38]
    // 0xb66f5c: r0 = Text()
    //     0xb66f5c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb66f60: mov             x1, x0
    // 0xb66f64: r0 = "Lengkapi seluruh isian berikut untuk menampilkan hasil zakat"
    //     0xb66f64: add             x0, PP, #0x26, lsl #12  ; [pp+0x26fb8] "Lengkapi seluruh isian berikut untuk menampilkan hasil zakat"
    //     0xb66f68: ldr             x0, [x0, #0xfb8]
    // 0xb66f6c: StoreField: r1->field_b = r0
    //     0xb66f6c: stur            w0, [x1, #0xb]
    // 0xb66f70: ldur            x0, [fp, #-0x38]
    // 0xb66f74: StoreField: r1->field_13 = r0
    //     0xb66f74: stur            w0, [x1, #0x13]
    // 0xb66f78: mov             x0, x1
    // 0xb66f7c: ldur            x1, [fp, #-0x40]
    // 0xb66f80: ArrayStore: r1[2] = r0  ; List_4
    //     0xb66f80: add             x25, x1, #0x17
    //     0xb66f84: str             w0, [x25]
    //     0xb66f88: tbz             w0, #0, #0xb66fa4
    //     0xb66f8c: ldurb           w16, [x1, #-1]
    //     0xb66f90: ldurb           w17, [x0, #-1]
    //     0xb66f94: and             x16, x17, x16, lsr #2
    //     0xb66f98: tst             x16, HEAP, lsr #32
    //     0xb66f9c: b.eq            #0xb66fa4
    //     0xb66fa0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb66fa4: ldur            x1, [fp, #-0x40]
    // 0xb66fa8: r16 = Instance_Divider
    //     0xb66fa8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fc0] Obj!Divider@e25801
    //     0xb66fac: ldr             x16, [x16, #0xfc0]
    // 0xb66fb0: StoreField: r1->field_1b = r16
    //     0xb66fb0: stur            w16, [x1, #0x1b]
    // 0xb66fb4: r0 = GetNavigation.textTheme()
    //     0xb66fb4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb66fb8: LoadField: r1 = r0->field_27
    //     0xb66fb8: ldur            w1, [x0, #0x27]
    // 0xb66fbc: DecompressPointer r1
    //     0xb66fbc: add             x1, x1, HEAP, lsl #32
    // 0xb66fc0: cmp             w1, NULL
    // 0xb66fc4: b.ne            #0xb66fd0
    // 0xb66fc8: r0 = Null
    //     0xb66fc8: mov             x0, NULL
    // 0xb66fcc: b               #0xb66fe8
    // 0xb66fd0: r16 = 14.000000
    //     0xb66fd0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb66fd4: ldr             x16, [x16, #0x9a0]
    // 0xb66fd8: str             x16, [SP]
    // 0xb66fdc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb66fdc: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb66fe0: ldr             x4, [x4, #0x88]
    // 0xb66fe4: r0 = copyWith()
    //     0xb66fe4: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb66fe8: ldur            x1, [fp, #-0x40]
    // 0xb66fec: stur            x0, [fp, #-0x38]
    // 0xb66ff0: r0 = Text()
    //     0xb66ff0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb66ff4: mov             x1, x0
    // 0xb66ff8: r0 = "Jenis Hasil Pertanian yang Dihitung"
    //     0xb66ff8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27498] "Jenis Hasil Pertanian yang Dihitung"
    //     0xb66ffc: ldr             x0, [x0, #0x498]
    // 0xb67000: StoreField: r1->field_b = r0
    //     0xb67000: stur            w0, [x1, #0xb]
    // 0xb67004: ldur            x0, [fp, #-0x38]
    // 0xb67008: StoreField: r1->field_13 = r0
    //     0xb67008: stur            w0, [x1, #0x13]
    // 0xb6700c: mov             x0, x1
    // 0xb67010: ldur            x1, [fp, #-0x40]
    // 0xb67014: ArrayStore: r1[4] = r0  ; List_4
    //     0xb67014: add             x25, x1, #0x1f
    //     0xb67018: str             w0, [x25]
    //     0xb6701c: tbz             w0, #0, #0xb67038
    //     0xb67020: ldurb           w16, [x1, #-1]
    //     0xb67024: ldurb           w17, [x0, #-1]
    //     0xb67028: and             x16, x17, x16, lsr #2
    //     0xb6702c: tst             x16, HEAP, lsr #32
    //     0xb67030: b.eq            #0xb67038
    //     0xb67034: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb67038: ldur            x1, [fp, #-0x40]
    // 0xb6703c: r16 = Instance_SizedBox
    //     0xb6703c: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb67040: ldr             x16, [x16, #0x4a0]
    // 0xb67044: StoreField: r1->field_23 = r16
    //     0xb67044: stur            w16, [x1, #0x23]
    // 0xb67048: r0 = GetNavigation.textTheme()
    //     0xb67048: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb6704c: LoadField: r1 = r0->field_2f
    //     0xb6704c: ldur            w1, [x0, #0x2f]
    // 0xb67050: DecompressPointer r1
    //     0xb67050: add             x1, x1, HEAP, lsl #32
    // 0xb67054: cmp             w1, NULL
    // 0xb67058: b.ne            #0xb67064
    // 0xb6705c: r2 = Null
    //     0xb6705c: mov             x2, NULL
    // 0xb67060: b               #0xb67088
    // 0xb67064: r16 = 16.000000
    //     0xb67064: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb67068: ldr             x16, [x16, #0x80]
    // 0xb6706c: r30 = 1.250000
    //     0xb6706c: add             lr, PP, #0x27, lsl #12  ; [pp+0x274a8] 1.25
    //     0xb67070: ldr             lr, [lr, #0x4a8]
    // 0xb67074: stp             lr, x16, [SP]
    // 0xb67078: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, height, 0x2, null]
    //     0xb67078: add             x4, PP, #0x27, lsl #12  ; [pp+0x274b0] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "height", 0x2, Null]
    //     0xb6707c: ldr             x4, [x4, #0x4b0]
    // 0xb67080: r0 = copyWith()
    //     0xb67080: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb67084: mov             x2, x0
    // 0xb67088: ldur            x0, [fp, #-0x40]
    // 0xb6708c: ldur            x1, [fp, #-8]
    // 0xb67090: stur            x2, [fp, #-0x38]
    // 0xb67094: r0 = controller()
    //     0xb67094: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb67098: mov             x1, x0
    // 0xb6709c: LoadField: r0 = r1->field_23
    //     0xb6709c: ldur            w0, [x1, #0x23]
    // 0xb670a0: DecompressPointer r0
    //     0xb670a0: add             x0, x0, HEAP, lsl #32
    // 0xb670a4: r16 = Sentinel
    //     0xb670a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb670a8: cmp             w0, w16
    // 0xb670ac: b.ne            #0xb670bc
    // 0xb670b0: r2 = jenisPertanianController
    //     0xb670b0: add             x2, PP, #0x27, lsl #12  ; [pp+0x274b8] Field <ZakatPertanianController.jenisPertanianController>: late final (offset: 0x24)
    //     0xb670b4: ldr             x2, [x2, #0x4b8]
    // 0xb670b8: r0 = InitLateFinalInstanceField()
    //     0xb670b8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb670bc: stur            x0, [fp, #-0x48]
    // 0xb670c0: r0 = TextField()
    //     0xb670c0: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb670c4: mov             x3, x0
    // 0xb670c8: r0 = EditableText
    //     0xb670c8: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb670cc: stur            x3, [fp, #-0x50]
    // 0xb670d0: StoreField: r3->field_f = r0
    //     0xb670d0: stur            w0, [x3, #0xf]
    // 0xb670d4: ldur            x0, [fp, #-0x48]
    // 0xb670d8: StoreField: r3->field_13 = r0
    //     0xb670d8: stur            w0, [x3, #0x13]
    // 0xb670dc: r0 = Instance_InputDecoration
    //     0xb670dc: add             x0, PP, #0x27, lsl #12  ; [pp+0x274c0] Obj!InputDecoration@e14321
    //     0xb670e0: ldr             x0, [x0, #0x4c0]
    // 0xb670e4: StoreField: r3->field_1b = r0
    //     0xb670e4: stur            w0, [x3, #0x1b]
    // 0xb670e8: r0 = Instance_TextCapitalization
    //     0xb670e8: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb670ec: StoreField: r3->field_27 = r0
    //     0xb670ec: stur            w0, [x3, #0x27]
    // 0xb670f0: ldur            x0, [fp, #-0x38]
    // 0xb670f4: StoreField: r3->field_2b = r0
    //     0xb670f4: stur            w0, [x3, #0x2b]
    // 0xb670f8: r0 = Instance_TextAlign
    //     0xb670f8: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb670fc: StoreField: r3->field_33 = r0
    //     0xb670fc: stur            w0, [x3, #0x33]
    // 0xb67100: r0 = true
    //     0xb67100: add             x0, NULL, #0x20  ; true
    // 0xb67104: StoreField: r3->field_6f = r0
    //     0xb67104: stur            w0, [x3, #0x6f]
    // 0xb67108: r4 = false
    //     0xb67108: add             x4, NULL, #0x30  ; false
    // 0xb6710c: StoreField: r3->field_3f = r4
    //     0xb6710c: stur            w4, [x3, #0x3f]
    // 0xb67110: r1 = "•"
    //     0xb67110: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb67114: ldr             x1, [x1, #0x4c8]
    // 0xb67118: StoreField: r3->field_47 = r1
    //     0xb67118: stur            w1, [x3, #0x47]
    // 0xb6711c: StoreField: r3->field_4b = r4
    //     0xb6711c: stur            w4, [x3, #0x4b]
    // 0xb67120: StoreField: r3->field_4f = r0
    //     0xb67120: stur            w0, [x3, #0x4f]
    // 0xb67124: StoreField: r3->field_5b = r0
    //     0xb67124: stur            w0, [x3, #0x5b]
    // 0xb67128: r5 = 1
    //     0xb67128: movz            x5, #0x1
    // 0xb6712c: StoreField: r3->field_5f = r5
    //     0xb6712c: stur            x5, [x3, #0x5f]
    // 0xb67130: StoreField: r3->field_6b = r4
    //     0xb67130: stur            w4, [x3, #0x6b]
    // 0xb67134: StoreField: r3->field_97 = r0
    //     0xb67134: stur            w0, [x3, #0x97]
    // 0xb67138: d0 = 2.000000
    //     0xb67138: fmov            d0, #2.00000000
    // 0xb6713c: StoreField: r3->field_9f = d0
    //     0xb6713c: stur            d0, [x3, #0x9f]
    // 0xb67140: r1 = Instance_BoxHeightStyle
    //     0xb67140: ldr             x1, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb67144: StoreField: r3->field_bb = r1
    //     0xb67144: stur            w1, [x3, #0xbb]
    // 0xb67148: r1 = Instance_BoxWidthStyle
    //     0xb67148: ldr             x1, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb6714c: StoreField: r3->field_bf = r1
    //     0xb6714c: stur            w1, [x3, #0xbf]
    // 0xb67150: r1 = Instance_EdgeInsets
    //     0xb67150: ldr             x1, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb67154: StoreField: r3->field_c7 = r1
    //     0xb67154: stur            w1, [x3, #0xc7]
    // 0xb67158: r6 = Instance_DragStartBehavior
    //     0xb67158: ldr             x6, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb6715c: StoreField: r3->field_d3 = r6
    //     0xb6715c: stur            w6, [x3, #0xd3]
    // 0xb67160: ldur            x2, [fp, #-0x18]
    // 0xb67164: r1 = Function '<anonymous closure>':.
    //     0xb67164: add             x1, PP, #0x27, lsl #12  ; [pp+0x274d0] AnonymousClosure: (0xb686ec), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb67168: ldr             x1, [x1, #0x4d0]
    // 0xb6716c: r0 = AllocateClosure()
    //     0xb6716c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb67170: mov             x1, x0
    // 0xb67174: ldur            x0, [fp, #-0x50]
    // 0xb67178: StoreField: r0->field_d7 = r1
    //     0xb67178: stur            w1, [x0, #0xd7]
    // 0xb6717c: r2 = false
    //     0xb6717c: add             x2, NULL, #0x30  ; false
    // 0xb67180: StoreField: r0->field_db = r2
    //     0xb67180: stur            w2, [x0, #0xdb]
    // 0xb67184: r1 = const []
    //     0xb67184: ldr             x1, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb67188: StoreField: r0->field_f3 = r1
    //     0xb67188: stur            w1, [x0, #0xf3]
    // 0xb6718c: r1 = Instance_Clip
    //     0xb6718c: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb67190: ldr             x1, [x1, #0x7c0]
    // 0xb67194: StoreField: r0->field_f7 = r1
    //     0xb67194: stur            w1, [x0, #0xf7]
    // 0xb67198: r3 = true
    //     0xb67198: add             x3, NULL, #0x20  ; true
    // 0xb6719c: StoreField: r0->field_ff = r3
    //     0xb6719c: stur            w3, [x0, #0xff]
    // 0xb671a0: r17 = 259
    //     0xb671a0: movz            x17, #0x103
    // 0xb671a4: str             w3, [x0, x17]
    // 0xb671a8: r1 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb671a8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb671ac: ldr             x1, [x1, #0x4d8]
    // 0xb671b0: r17 = 267
    //     0xb671b0: movz            x17, #0x10b
    // 0xb671b4: str             w1, [x0, x17]
    // 0xb671b8: r17 = 271
    //     0xb671b8: movz            x17, #0x10f
    // 0xb671bc: str             w3, [x0, x17]
    // 0xb671c0: r1 = Instance_SmartDashesType
    //     0xb671c0: ldr             x1, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb671c4: StoreField: r0->field_53 = r1
    //     0xb671c4: stur            w1, [x0, #0x53]
    // 0xb671c8: r1 = Instance_SmartQuotesType
    //     0xb671c8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb671cc: ldr             x1, [x1, #0x4e0]
    // 0xb671d0: StoreField: r0->field_57 = r1
    //     0xb671d0: stur            w1, [x0, #0x57]
    // 0xb671d4: r1 = Instance_TextInputType
    //     0xb671d4: add             x1, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xb671d8: ldr             x1, [x1, #0x4e8]
    // 0xb671dc: StoreField: r0->field_1f = r1
    //     0xb671dc: stur            w1, [x0, #0x1f]
    // 0xb671e0: StoreField: r0->field_cb = r3
    //     0xb671e0: stur            w3, [x0, #0xcb]
    // 0xb671e4: ldur            x1, [fp, #-0x40]
    // 0xb671e8: ArrayStore: r1[6] = r0  ; List_4
    //     0xb671e8: add             x25, x1, #0x27
    //     0xb671ec: str             w0, [x25]
    //     0xb671f0: tbz             w0, #0, #0xb6720c
    //     0xb671f4: ldurb           w16, [x1, #-1]
    //     0xb671f8: ldurb           w17, [x0, #-1]
    //     0xb671fc: and             x16, x17, x16, lsr #2
    //     0xb67200: tst             x16, HEAP, lsr #32
    //     0xb67204: b.eq            #0xb6720c
    //     0xb67208: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6720c: ldur            x1, [fp, #-0x40]
    // 0xb67210: r16 = Instance_SizedBox
    //     0xb67210: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb67214: ldr             x16, [x16, #0xfe8]
    // 0xb67218: StoreField: r1->field_2b = r16
    //     0xb67218: stur            w16, [x1, #0x2b]
    // 0xb6721c: r0 = GetNavigation.textTheme()
    //     0xb6721c: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb67220: LoadField: r1 = r0->field_27
    //     0xb67220: ldur            w1, [x0, #0x27]
    // 0xb67224: DecompressPointer r1
    //     0xb67224: add             x1, x1, HEAP, lsl #32
    // 0xb67228: cmp             w1, NULL
    // 0xb6722c: b.ne            #0xb67238
    // 0xb67230: r0 = Null
    //     0xb67230: mov             x0, NULL
    // 0xb67234: b               #0xb67250
    // 0xb67238: r16 = 14.000000
    //     0xb67238: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb6723c: ldr             x16, [x16, #0x9a0]
    // 0xb67240: str             x16, [SP]
    // 0xb67244: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb67244: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb67248: ldr             x4, [x4, #0x88]
    // 0xb6724c: r0 = copyWith()
    //     0xb6724c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb67250: ldur            x1, [fp, #-0x40]
    // 0xb67254: stur            x0, [fp, #-0x38]
    // 0xb67258: r0 = Text()
    //     0xb67258: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb6725c: mov             x1, x0
    // 0xb67260: r0 = "Satuan Berat"
    //     0xb67260: add             x0, PP, #0x27, lsl #12  ; [pp+0x274f0] "Satuan Berat"
    //     0xb67264: ldr             x0, [x0, #0x4f0]
    // 0xb67268: StoreField: r1->field_b = r0
    //     0xb67268: stur            w0, [x1, #0xb]
    // 0xb6726c: ldur            x0, [fp, #-0x38]
    // 0xb67270: StoreField: r1->field_13 = r0
    //     0xb67270: stur            w0, [x1, #0x13]
    // 0xb67274: mov             x0, x1
    // 0xb67278: ldur            x1, [fp, #-0x40]
    // 0xb6727c: ArrayStore: r1[8] = r0  ; List_4
    //     0xb6727c: add             x25, x1, #0x2f
    //     0xb67280: str             w0, [x25]
    //     0xb67284: tbz             w0, #0, #0xb672a0
    //     0xb67288: ldurb           w16, [x1, #-1]
    //     0xb6728c: ldurb           w17, [x0, #-1]
    //     0xb67290: and             x16, x17, x16, lsr #2
    //     0xb67294: tst             x16, HEAP, lsr #32
    //     0xb67298: b.eq            #0xb672a0
    //     0xb6729c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb672a0: ldur            x1, [fp, #-0x40]
    // 0xb672a4: r16 = Instance_SizedBox
    //     0xb672a4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fb0] Obj!SizedBox@e1e041
    //     0xb672a8: ldr             x16, [x16, #0xfb0]
    // 0xb672ac: StoreField: r1->field_33 = r16
    //     0xb672ac: stur            w16, [x1, #0x33]
    // 0xb672b0: r0 = Obx()
    //     0xb672b0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb672b4: ldur            x2, [fp, #-0x18]
    // 0xb672b8: r1 = Function '<anonymous closure>':.
    //     0xb672b8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274f8] AnonymousClosure: (0xb68470), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb672bc: ldr             x1, [x1, #0x4f8]
    // 0xb672c0: stur            x0, [fp, #-0x38]
    // 0xb672c4: r0 = AllocateClosure()
    //     0xb672c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb672c8: mov             x1, x0
    // 0xb672cc: ldur            x0, [fp, #-0x38]
    // 0xb672d0: StoreField: r0->field_b = r1
    //     0xb672d0: stur            w1, [x0, #0xb]
    // 0xb672d4: ldur            x1, [fp, #-0x40]
    // 0xb672d8: ArrayStore: r1[10] = r0  ; List_4
    //     0xb672d8: add             x25, x1, #0x37
    //     0xb672dc: str             w0, [x25]
    //     0xb672e0: tbz             w0, #0, #0xb672fc
    //     0xb672e4: ldurb           w16, [x1, #-1]
    //     0xb672e8: ldurb           w17, [x0, #-1]
    //     0xb672ec: and             x16, x17, x16, lsr #2
    //     0xb672f0: tst             x16, HEAP, lsr #32
    //     0xb672f4: b.eq            #0xb672fc
    //     0xb672f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb672fc: ldur            x1, [fp, #-0x40]
    // 0xb67300: r16 = Instance_SizedBox
    //     0xb67300: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb67304: ldr             x16, [x16, #0xfe8]
    // 0xb67308: StoreField: r1->field_3b = r16
    //     0xb67308: stur            w16, [x1, #0x3b]
    // 0xb6730c: r0 = Obx()
    //     0xb6730c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb67310: ldur            x2, [fp, #-0x18]
    // 0xb67314: r1 = Function '<anonymous closure>':.
    //     0xb67314: add             x1, PP, #0x27, lsl #12  ; [pp+0x27500] AnonymousClosure: (0xb682d0), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb67318: ldr             x1, [x1, #0x500]
    // 0xb6731c: stur            x0, [fp, #-0x38]
    // 0xb67320: r0 = AllocateClosure()
    //     0xb67320: bl              #0xec1630  ; AllocateClosureStub
    // 0xb67324: mov             x1, x0
    // 0xb67328: ldur            x0, [fp, #-0x38]
    // 0xb6732c: StoreField: r0->field_b = r1
    //     0xb6732c: stur            w1, [x0, #0xb]
    // 0xb67330: ldur            x1, [fp, #-0x40]
    // 0xb67334: ArrayStore: r1[12] = r0  ; List_4
    //     0xb67334: add             x25, x1, #0x3f
    //     0xb67338: str             w0, [x25]
    //     0xb6733c: tbz             w0, #0, #0xb67358
    //     0xb67340: ldurb           w16, [x1, #-1]
    //     0xb67344: ldurb           w17, [x0, #-1]
    //     0xb67348: and             x16, x17, x16, lsr #2
    //     0xb6734c: tst             x16, HEAP, lsr #32
    //     0xb67350: b.eq            #0xb67358
    //     0xb67354: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb67358: ldur            x1, [fp, #-0x40]
    // 0xb6735c: r16 = Instance_SizedBox
    //     0xb6735c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb67360: ldr             x16, [x16, #0xfe8]
    // 0xb67364: StoreField: r1->field_43 = r16
    //     0xb67364: stur            w16, [x1, #0x43]
    // 0xb67368: r0 = GetNavigation.textTheme()
    //     0xb67368: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb6736c: LoadField: r1 = r0->field_27
    //     0xb6736c: ldur            w1, [x0, #0x27]
    // 0xb67370: DecompressPointer r1
    //     0xb67370: add             x1, x1, HEAP, lsl #32
    // 0xb67374: cmp             w1, NULL
    // 0xb67378: b.ne            #0xb67384
    // 0xb6737c: r5 = Null
    //     0xb6737c: mov             x5, NULL
    // 0xb67380: b               #0xb673a0
    // 0xb67384: r16 = 14.000000
    //     0xb67384: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xb67388: ldr             x16, [x16, #0x9a0]
    // 0xb6738c: str             x16, [SP]
    // 0xb67390: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb67390: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb67394: ldr             x4, [x4, #0x88]
    // 0xb67398: r0 = copyWith()
    //     0xb67398: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6739c: mov             x5, x0
    // 0xb673a0: ldur            x4, [fp, #-0x10]
    // 0xb673a4: ldur            x3, [fp, #-0x20]
    // 0xb673a8: ldur            x2, [fp, #-0x30]
    // 0xb673ac: ldur            x0, [fp, #-0x28]
    // 0xb673b0: ldur            x1, [fp, #-0x40]
    // 0xb673b4: stur            x5, [fp, #-0x38]
    // 0xb673b8: r0 = Text()
    //     0xb673b8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb673bc: mov             x1, x0
    // 0xb673c0: r0 = "Jenis Pengairan"
    //     0xb673c0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27508] "Jenis Pengairan"
    //     0xb673c4: ldr             x0, [x0, #0x508]
    // 0xb673c8: StoreField: r1->field_b = r0
    //     0xb673c8: stur            w0, [x1, #0xb]
    // 0xb673cc: ldur            x0, [fp, #-0x38]
    // 0xb673d0: StoreField: r1->field_13 = r0
    //     0xb673d0: stur            w0, [x1, #0x13]
    // 0xb673d4: mov             x0, x1
    // 0xb673d8: ldur            x1, [fp, #-0x40]
    // 0xb673dc: ArrayStore: r1[14] = r0  ; List_4
    //     0xb673dc: add             x25, x1, #0x47
    //     0xb673e0: str             w0, [x25]
    //     0xb673e4: tbz             w0, #0, #0xb67400
    //     0xb673e8: ldurb           w16, [x1, #-1]
    //     0xb673ec: ldurb           w17, [x0, #-1]
    //     0xb673f0: and             x16, x17, x16, lsr #2
    //     0xb673f4: tst             x16, HEAP, lsr #32
    //     0xb673f8: b.eq            #0xb67400
    //     0xb673fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb67400: ldur            x1, [fp, #-0x40]
    // 0xb67404: r16 = Instance_SizedBox
    //     0xb67404: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb67408: ldr             x16, [x16, #0x950]
    // 0xb6740c: StoreField: r1->field_4b = r16
    //     0xb6740c: stur            w16, [x1, #0x4b]
    // 0xb67410: r0 = Obx()
    //     0xb67410: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb67414: ldur            x2, [fp, #-0x18]
    // 0xb67418: r1 = Function '<anonymous closure>':.
    //     0xb67418: add             x1, PP, #0x27, lsl #12  ; [pp+0x27510] AnonymousClosure: (0xb68210), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb6741c: ldr             x1, [x1, #0x510]
    // 0xb67420: stur            x0, [fp, #-0x38]
    // 0xb67424: r0 = AllocateClosure()
    //     0xb67424: bl              #0xec1630  ; AllocateClosureStub
    // 0xb67428: mov             x1, x0
    // 0xb6742c: ldur            x0, [fp, #-0x38]
    // 0xb67430: StoreField: r0->field_b = r1
    //     0xb67430: stur            w1, [x0, #0xb]
    // 0xb67434: ldur            x1, [fp, #-0x40]
    // 0xb67438: ArrayStore: r1[16] = r0  ; List_4
    //     0xb67438: add             x25, x1, #0x4f
    //     0xb6743c: str             w0, [x25]
    //     0xb67440: tbz             w0, #0, #0xb6745c
    //     0xb67444: ldurb           w16, [x1, #-1]
    //     0xb67448: ldurb           w17, [x0, #-1]
    //     0xb6744c: and             x16, x17, x16, lsr #2
    //     0xb67450: tst             x16, HEAP, lsr #32
    //     0xb67454: b.eq            #0xb6745c
    //     0xb67458: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6745c: ldur            x1, [fp, #-0x40]
    // 0xb67460: r16 = Instance_SizedBox
    //     0xb67460: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb67464: ldr             x16, [x16, #0x950]
    // 0xb67468: StoreField: r1->field_53 = r16
    //     0xb67468: stur            w16, [x1, #0x53]
    // 0xb6746c: r0 = Obx()
    //     0xb6746c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb67470: ldur            x2, [fp, #-0x18]
    // 0xb67474: r1 = Function '<anonymous closure>':.
    //     0xb67474: add             x1, PP, #0x27, lsl #12  ; [pp+0x27518] AnonymousClosure: (0xb680b0), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb67478: ldr             x1, [x1, #0x518]
    // 0xb6747c: stur            x0, [fp, #-0x38]
    // 0xb67480: r0 = AllocateClosure()
    //     0xb67480: bl              #0xec1630  ; AllocateClosureStub
    // 0xb67484: mov             x1, x0
    // 0xb67488: ldur            x0, [fp, #-0x38]
    // 0xb6748c: StoreField: r0->field_b = r1
    //     0xb6748c: stur            w1, [x0, #0xb]
    // 0xb67490: ldur            x1, [fp, #-0x40]
    // 0xb67494: ArrayStore: r1[18] = r0  ; List_4
    //     0xb67494: add             x25, x1, #0x57
    //     0xb67498: str             w0, [x25]
    //     0xb6749c: tbz             w0, #0, #0xb674b8
    //     0xb674a0: ldurb           w16, [x1, #-1]
    //     0xb674a4: ldurb           w17, [x0, #-1]
    //     0xb674a8: and             x16, x17, x16, lsr #2
    //     0xb674ac: tst             x16, HEAP, lsr #32
    //     0xb674b0: b.eq            #0xb674b8
    //     0xb674b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb674b8: r1 = <Widget>
    //     0xb674b8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb674bc: r0 = AllocateGrowableArray()
    //     0xb674bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb674c0: mov             x1, x0
    // 0xb674c4: ldur            x0, [fp, #-0x40]
    // 0xb674c8: stur            x1, [fp, #-0x38]
    // 0xb674cc: StoreField: r1->field_f = r0
    //     0xb674cc: stur            w0, [x1, #0xf]
    // 0xb674d0: r0 = 38
    //     0xb674d0: movz            x0, #0x26
    // 0xb674d4: StoreField: r1->field_b = r0
    //     0xb674d4: stur            w0, [x1, #0xb]
    // 0xb674d8: r0 = Column()
    //     0xb674d8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb674dc: mov             x1, x0
    // 0xb674e0: r0 = Instance_Axis
    //     0xb674e0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb674e4: stur            x1, [fp, #-0x40]
    // 0xb674e8: StoreField: r1->field_f = r0
    //     0xb674e8: stur            w0, [x1, #0xf]
    // 0xb674ec: r2 = Instance_MainAxisAlignment
    //     0xb674ec: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb674f0: ldr             x2, [x2, #0x730]
    // 0xb674f4: StoreField: r1->field_13 = r2
    //     0xb674f4: stur            w2, [x1, #0x13]
    // 0xb674f8: r3 = Instance_MainAxisSize
    //     0xb674f8: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb674fc: ldr             x3, [x3, #0x738]
    // 0xb67500: ArrayStore: r1[0] = r3  ; List_4
    //     0xb67500: stur            w3, [x1, #0x17]
    // 0xb67504: r4 = Instance_CrossAxisAlignment
    //     0xb67504: add             x4, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb67508: ldr             x4, [x4, #0x68]
    // 0xb6750c: StoreField: r1->field_1b = r4
    //     0xb6750c: stur            w4, [x1, #0x1b]
    // 0xb67510: r5 = Instance_VerticalDirection
    //     0xb67510: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb67514: ldr             x5, [x5, #0x748]
    // 0xb67518: StoreField: r1->field_23 = r5
    //     0xb67518: stur            w5, [x1, #0x23]
    // 0xb6751c: r6 = Instance_Clip
    //     0xb6751c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb67520: ldr             x6, [x6, #0x750]
    // 0xb67524: StoreField: r1->field_2b = r6
    //     0xb67524: stur            w6, [x1, #0x2b]
    // 0xb67528: StoreField: r1->field_2f = rZR
    //     0xb67528: stur            xzr, [x1, #0x2f]
    // 0xb6752c: ldur            x7, [fp, #-0x38]
    // 0xb67530: StoreField: r1->field_b = r7
    //     0xb67530: stur            w7, [x1, #0xb]
    // 0xb67534: r0 = Padding()
    //     0xb67534: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67538: mov             x1, x0
    // 0xb6753c: r0 = Instance_EdgeInsets
    //     0xb6753c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27070] Obj!EdgeInsets@e12ee1
    //     0xb67540: ldr             x0, [x0, #0x70]
    // 0xb67544: stur            x1, [fp, #-0x38]
    // 0xb67548: StoreField: r1->field_f = r0
    //     0xb67548: stur            w0, [x1, #0xf]
    // 0xb6754c: ldur            x2, [fp, #-0x40]
    // 0xb67550: StoreField: r1->field_b = r2
    //     0xb67550: stur            w2, [x1, #0xb]
    // 0xb67554: r0 = ColoredBox()
    //     0xb67554: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb67558: mov             x1, x0
    // 0xb6755c: ldur            x0, [fp, #-0x28]
    // 0xb67560: stur            x1, [fp, #-0x40]
    // 0xb67564: StoreField: r1->field_f = r0
    //     0xb67564: stur            w0, [x1, #0xf]
    // 0xb67568: ldur            x0, [fp, #-0x38]
    // 0xb6756c: StoreField: r1->field_b = r0
    //     0xb6756c: stur            w0, [x1, #0xb]
    // 0xb67570: r0 = GetNavigation.theme()
    //     0xb67570: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb67574: LoadField: r1 = r0->field_37
    //     0xb67574: ldur            w1, [x0, #0x37]
    // 0xb67578: DecompressPointer r1
    //     0xb67578: add             x1, x1, HEAP, lsl #32
    // 0xb6757c: stur            x1, [fp, #-0x28]
    // 0xb67580: r0 = NArticleHeader()
    //     0xb67580: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xb67584: mov             x4, x0
    // 0xb67588: r0 = "Besaran Zakat"
    //     0xb67588: add             x0, PP, #0x27, lsl #12  ; [pp+0x27078] "Besaran Zakat"
    //     0xb6758c: ldr             x0, [x0, #0x78]
    // 0xb67590: stur            x4, [fp, #-0x38]
    // 0xb67594: StoreField: r4->field_b = r0
    //     0xb67594: stur            w0, [x4, #0xb]
    // 0xb67598: r0 = false
    //     0xb67598: add             x0, NULL, #0x30  ; false
    // 0xb6759c: StoreField: r4->field_13 = r0
    //     0xb6759c: stur            w0, [x4, #0x13]
    // 0xb675a0: ldur            x1, [fp, #-8]
    // 0xb675a4: r2 = "Irigasi Berbayar"
    //     0xb675a4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27520] "Irigasi Berbayar"
    //     0xb675a8: ldr             x2, [x2, #0x520]
    // 0xb675ac: r3 = "Hasil Panen x 5%"
    //     0xb675ac: add             x3, PP, #0x27, lsl #12  ; [pp+0x27528] "Hasil Panen x 5%"
    //     0xb675b0: ldr             x3, [x3, #0x528]
    // 0xb675b4: r0 = _buildBesaranZakatInfo()
    //     0xb675b4: bl              #0xb63b8c  ; [package:nuonline/app/modules/zakat/views/zakat_emas_view.dart] ZakatEmasView::_buildBesaranZakatInfo
    // 0xb675b8: ldur            x1, [fp, #-8]
    // 0xb675bc: r2 = "Irigasi Tidak Berbayar"
    //     0xb675bc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27530] "Irigasi Tidak Berbayar"
    //     0xb675c0: ldr             x2, [x2, #0x530]
    // 0xb675c4: r3 = "Hasil Panen x 10%"
    //     0xb675c4: add             x3, PP, #0x27, lsl #12  ; [pp+0x27538] "Hasil Panen x 10%"
    //     0xb675c8: ldr             x3, [x3, #0x538]
    // 0xb675cc: stur            x0, [fp, #-8]
    // 0xb675d0: r0 = _buildBesaranZakatInfo()
    //     0xb675d0: bl              #0xb63b8c  ; [package:nuonline/app/modules/zakat/views/zakat_emas_view.dart] ZakatEmasView::_buildBesaranZakatInfo
    // 0xb675d4: r1 = Null
    //     0xb675d4: mov             x1, NULL
    // 0xb675d8: r2 = 10
    //     0xb675d8: movz            x2, #0xa
    // 0xb675dc: stur            x0, [fp, #-0x48]
    // 0xb675e0: r0 = AllocateArray()
    //     0xb675e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb675e4: mov             x2, x0
    // 0xb675e8: ldur            x0, [fp, #-0x38]
    // 0xb675ec: stur            x2, [fp, #-0x50]
    // 0xb675f0: StoreField: r2->field_f = r0
    //     0xb675f0: stur            w0, [x2, #0xf]
    // 0xb675f4: r16 = Instance_Divider
    //     0xb675f4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fc0] Obj!Divider@e25801
    //     0xb675f8: ldr             x16, [x16, #0xfc0]
    // 0xb675fc: StoreField: r2->field_13 = r16
    //     0xb675fc: stur            w16, [x2, #0x13]
    // 0xb67600: ldur            x0, [fp, #-8]
    // 0xb67604: ArrayStore: r2[0] = r0  ; List_4
    //     0xb67604: stur            w0, [x2, #0x17]
    // 0xb67608: r16 = Instance_SizedBox
    //     0xb67608: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xb6760c: ldr             x16, [x16, #0x540]
    // 0xb67610: StoreField: r2->field_1b = r16
    //     0xb67610: stur            w16, [x2, #0x1b]
    // 0xb67614: ldur            x0, [fp, #-0x48]
    // 0xb67618: StoreField: r2->field_1f = r0
    //     0xb67618: stur            w0, [x2, #0x1f]
    // 0xb6761c: r1 = <Widget>
    //     0xb6761c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb67620: r0 = AllocateGrowableArray()
    //     0xb67620: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb67624: mov             x1, x0
    // 0xb67628: ldur            x0, [fp, #-0x50]
    // 0xb6762c: stur            x1, [fp, #-8]
    // 0xb67630: StoreField: r1->field_f = r0
    //     0xb67630: stur            w0, [x1, #0xf]
    // 0xb67634: r2 = 10
    //     0xb67634: movz            x2, #0xa
    // 0xb67638: StoreField: r1->field_b = r2
    //     0xb67638: stur            w2, [x1, #0xb]
    // 0xb6763c: r0 = Column()
    //     0xb6763c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb67640: mov             x1, x0
    // 0xb67644: r0 = Instance_Axis
    //     0xb67644: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb67648: stur            x1, [fp, #-0x38]
    // 0xb6764c: StoreField: r1->field_f = r0
    //     0xb6764c: stur            w0, [x1, #0xf]
    // 0xb67650: r2 = Instance_MainAxisAlignment
    //     0xb67650: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb67654: ldr             x2, [x2, #0x730]
    // 0xb67658: StoreField: r1->field_13 = r2
    //     0xb67658: stur            w2, [x1, #0x13]
    // 0xb6765c: r3 = Instance_MainAxisSize
    //     0xb6765c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb67660: ldr             x3, [x3, #0x738]
    // 0xb67664: ArrayStore: r1[0] = r3  ; List_4
    //     0xb67664: stur            w3, [x1, #0x17]
    // 0xb67668: r4 = Instance_CrossAxisAlignment
    //     0xb67668: add             x4, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb6766c: ldr             x4, [x4, #0x68]
    // 0xb67670: StoreField: r1->field_1b = r4
    //     0xb67670: stur            w4, [x1, #0x1b]
    // 0xb67674: r4 = Instance_VerticalDirection
    //     0xb67674: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb67678: ldr             x4, [x4, #0x748]
    // 0xb6767c: StoreField: r1->field_23 = r4
    //     0xb6767c: stur            w4, [x1, #0x23]
    // 0xb67680: r5 = Instance_Clip
    //     0xb67680: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb67684: ldr             x5, [x5, #0x750]
    // 0xb67688: StoreField: r1->field_2b = r5
    //     0xb67688: stur            w5, [x1, #0x2b]
    // 0xb6768c: StoreField: r1->field_2f = rZR
    //     0xb6768c: stur            xzr, [x1, #0x2f]
    // 0xb67690: ldur            x6, [fp, #-8]
    // 0xb67694: StoreField: r1->field_b = r6
    //     0xb67694: stur            w6, [x1, #0xb]
    // 0xb67698: r0 = Padding()
    //     0xb67698: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6769c: mov             x1, x0
    // 0xb676a0: r0 = Instance_EdgeInsets
    //     0xb676a0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27070] Obj!EdgeInsets@e12ee1
    //     0xb676a4: ldr             x0, [x0, #0x70]
    // 0xb676a8: stur            x1, [fp, #-8]
    // 0xb676ac: StoreField: r1->field_f = r0
    //     0xb676ac: stur            w0, [x1, #0xf]
    // 0xb676b0: ldur            x0, [fp, #-0x38]
    // 0xb676b4: StoreField: r1->field_b = r0
    //     0xb676b4: stur            w0, [x1, #0xb]
    // 0xb676b8: r0 = ColoredBox()
    //     0xb676b8: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb676bc: mov             x3, x0
    // 0xb676c0: ldur            x0, [fp, #-0x28]
    // 0xb676c4: stur            x3, [fp, #-0x38]
    // 0xb676c8: StoreField: r3->field_f = r0
    //     0xb676c8: stur            w0, [x3, #0xf]
    // 0xb676cc: ldur            x0, [fp, #-8]
    // 0xb676d0: StoreField: r3->field_b = r0
    //     0xb676d0: stur            w0, [x3, #0xb]
    // 0xb676d4: r1 = Null
    //     0xb676d4: mov             x1, NULL
    // 0xb676d8: r2 = 10
    //     0xb676d8: movz            x2, #0xa
    // 0xb676dc: r0 = AllocateArray()
    //     0xb676dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb676e0: mov             x2, x0
    // 0xb676e4: ldur            x0, [fp, #-0x30]
    // 0xb676e8: stur            x2, [fp, #-8]
    // 0xb676ec: StoreField: r2->field_f = r0
    //     0xb676ec: stur            w0, [x2, #0xf]
    // 0xb676f0: r16 = Instance_SizedBox
    //     0xb676f0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb676f4: ldr             x16, [x16, #0x950]
    // 0xb676f8: StoreField: r2->field_13 = r16
    //     0xb676f8: stur            w16, [x2, #0x13]
    // 0xb676fc: ldur            x0, [fp, #-0x40]
    // 0xb67700: ArrayStore: r2[0] = r0  ; List_4
    //     0xb67700: stur            w0, [x2, #0x17]
    // 0xb67704: r16 = Instance_SizedBox
    //     0xb67704: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xb67708: ldr             x16, [x16, #0x950]
    // 0xb6770c: StoreField: r2->field_1b = r16
    //     0xb6770c: stur            w16, [x2, #0x1b]
    // 0xb67710: ldur            x0, [fp, #-0x38]
    // 0xb67714: StoreField: r2->field_1f = r0
    //     0xb67714: stur            w0, [x2, #0x1f]
    // 0xb67718: r1 = <Widget>
    //     0xb67718: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb6771c: r0 = AllocateGrowableArray()
    //     0xb6771c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb67720: mov             x1, x0
    // 0xb67724: ldur            x0, [fp, #-8]
    // 0xb67728: stur            x1, [fp, #-0x28]
    // 0xb6772c: StoreField: r1->field_f = r0
    //     0xb6772c: stur            w0, [x1, #0xf]
    // 0xb67730: r0 = 10
    //     0xb67730: movz            x0, #0xa
    // 0xb67734: StoreField: r1->field_b = r0
    //     0xb67734: stur            w0, [x1, #0xb]
    // 0xb67738: r0 = ListView()
    //     0xb67738: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb6773c: stur            x0, [fp, #-8]
    // 0xb67740: r16 = Instance_EdgeInsets
    //     0xb67740: add             x16, PP, #0x27, lsl #12  ; [pp+0x27098] Obj!EdgeInsets@e11f21
    //     0xb67744: ldr             x16, [x16, #0x98]
    // 0xb67748: str             x16, [SP]
    // 0xb6774c: mov             x1, x0
    // 0xb67750: ldur            x2, [fp, #-0x28]
    // 0xb67754: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xb67754: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xb67758: ldr             x4, [x4, #0xa0]
    // 0xb6775c: r0 = ListView()
    //     0xb6775c: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb67760: r1 = <FlexParentData>
    //     0xb67760: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb67764: ldr             x1, [x1, #0x720]
    // 0xb67768: r0 = Expanded()
    //     0xb67768: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb6776c: mov             x1, x0
    // 0xb67770: r0 = 1
    //     0xb67770: movz            x0, #0x1
    // 0xb67774: stur            x1, [fp, #-0x28]
    // 0xb67778: StoreField: r1->field_13 = r0
    //     0xb67778: stur            x0, [x1, #0x13]
    // 0xb6777c: r0 = Instance_FlexFit
    //     0xb6777c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb67780: ldr             x0, [x0, #0x728]
    // 0xb67784: StoreField: r1->field_1b = r0
    //     0xb67784: stur            w0, [x1, #0x1b]
    // 0xb67788: ldur            x0, [fp, #-8]
    // 0xb6778c: StoreField: r1->field_b = r0
    //     0xb6778c: stur            w0, [x1, #0xb]
    // 0xb67790: r0 = Obx()
    //     0xb67790: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb67794: ldur            x2, [fp, #-0x18]
    // 0xb67798: r1 = Function '<anonymous closure>':.
    //     0xb67798: add             x1, PP, #0x27, lsl #12  ; [pp+0x27548] AnonymousClosure: (0xb67904), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb6779c: ldr             x1, [x1, #0x548]
    // 0xb677a0: stur            x0, [fp, #-8]
    // 0xb677a4: r0 = AllocateClosure()
    //     0xb677a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb677a8: mov             x1, x0
    // 0xb677ac: ldur            x0, [fp, #-8]
    // 0xb677b0: StoreField: r0->field_b = r1
    //     0xb677b0: stur            w1, [x0, #0xb]
    // 0xb677b4: r0 = Align()
    //     0xb677b4: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb677b8: mov             x3, x0
    // 0xb677bc: r0 = Instance_Alignment
    //     0xb677bc: add             x0, PP, #0x27, lsl #12  ; [pp+0x270b0] Obj!Alignment@e13e91
    //     0xb677c0: ldr             x0, [x0, #0xb0]
    // 0xb677c4: stur            x3, [fp, #-0x30]
    // 0xb677c8: StoreField: r3->field_f = r0
    //     0xb677c8: stur            w0, [x3, #0xf]
    // 0xb677cc: ldur            x0, [fp, #-8]
    // 0xb677d0: StoreField: r3->field_b = r0
    //     0xb677d0: stur            w0, [x3, #0xb]
    // 0xb677d4: r1 = Null
    //     0xb677d4: mov             x1, NULL
    // 0xb677d8: r2 = 4
    //     0xb677d8: movz            x2, #0x4
    // 0xb677dc: r0 = AllocateArray()
    //     0xb677dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb677e0: mov             x2, x0
    // 0xb677e4: ldur            x0, [fp, #-0x28]
    // 0xb677e8: stur            x2, [fp, #-8]
    // 0xb677ec: StoreField: r2->field_f = r0
    //     0xb677ec: stur            w0, [x2, #0xf]
    // 0xb677f0: ldur            x0, [fp, #-0x30]
    // 0xb677f4: StoreField: r2->field_13 = r0
    //     0xb677f4: stur            w0, [x2, #0x13]
    // 0xb677f8: r1 = <Widget>
    //     0xb677f8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb677fc: r0 = AllocateGrowableArray()
    //     0xb677fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb67800: mov             x1, x0
    // 0xb67804: ldur            x0, [fp, #-8]
    // 0xb67808: stur            x1, [fp, #-0x28]
    // 0xb6780c: StoreField: r1->field_f = r0
    //     0xb6780c: stur            w0, [x1, #0xf]
    // 0xb67810: r0 = 4
    //     0xb67810: movz            x0, #0x4
    // 0xb67814: StoreField: r1->field_b = r0
    //     0xb67814: stur            w0, [x1, #0xb]
    // 0xb67818: r0 = Column()
    //     0xb67818: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6781c: mov             x1, x0
    // 0xb67820: r0 = Instance_Axis
    //     0xb67820: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb67824: stur            x1, [fp, #-8]
    // 0xb67828: StoreField: r1->field_f = r0
    //     0xb67828: stur            w0, [x1, #0xf]
    // 0xb6782c: r0 = Instance_MainAxisAlignment
    //     0xb6782c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb67830: ldr             x0, [x0, #0x730]
    // 0xb67834: StoreField: r1->field_13 = r0
    //     0xb67834: stur            w0, [x1, #0x13]
    // 0xb67838: r0 = Instance_MainAxisSize
    //     0xb67838: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb6783c: ldr             x0, [x0, #0x738]
    // 0xb67840: ArrayStore: r1[0] = r0  ; List_4
    //     0xb67840: stur            w0, [x1, #0x17]
    // 0xb67844: r0 = Instance_CrossAxisAlignment
    //     0xb67844: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb67848: ldr             x0, [x0, #0x740]
    // 0xb6784c: StoreField: r1->field_1b = r0
    //     0xb6784c: stur            w0, [x1, #0x1b]
    // 0xb67850: r0 = Instance_VerticalDirection
    //     0xb67850: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb67854: ldr             x0, [x0, #0x748]
    // 0xb67858: StoreField: r1->field_23 = r0
    //     0xb67858: stur            w0, [x1, #0x23]
    // 0xb6785c: r0 = Instance_Clip
    //     0xb6785c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb67860: ldr             x0, [x0, #0x750]
    // 0xb67864: StoreField: r1->field_2b = r0
    //     0xb67864: stur            w0, [x1, #0x2b]
    // 0xb67868: StoreField: r1->field_2f = rZR
    //     0xb67868: stur            xzr, [x1, #0x2f]
    // 0xb6786c: ldur            x0, [fp, #-0x28]
    // 0xb67870: StoreField: r1->field_b = r0
    //     0xb67870: stur            w0, [x1, #0xb]
    // 0xb67874: r0 = GestureDetector()
    //     0xb67874: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb67878: ldur            x2, [fp, #-0x18]
    // 0xb6787c: r1 = Function '<anonymous closure>':.
    //     0xb6787c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27550] AnonymousClosure: (0xb6416c), in [package:nuonline/app/modules/zakat/views/zakat_properti_view.dart] ZakatPropertiView::build (0xb69c64)
    //     0xb67880: ldr             x1, [x1, #0x550]
    // 0xb67884: stur            x0, [fp, #-0x18]
    // 0xb67888: r0 = AllocateClosure()
    //     0xb67888: bl              #0xec1630  ; AllocateClosureStub
    // 0xb6788c: ldur            x16, [fp, #-8]
    // 0xb67890: stp             x16, x0, [SP]
    // 0xb67894: ldur            x1, [fp, #-0x18]
    // 0xb67898: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb67898: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb6789c: ldr             x4, [x4, #0x7d0]
    // 0xb678a0: r0 = GestureDetector()
    //     0xb678a0: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb678a4: r0 = Scaffold()
    //     0xb678a4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xb678a8: ldur            x1, [fp, #-0x10]
    // 0xb678ac: StoreField: r0->field_13 = r1
    //     0xb678ac: stur            w1, [x0, #0x13]
    // 0xb678b0: ldur            x1, [fp, #-0x18]
    // 0xb678b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb678b4: stur            w1, [x0, #0x17]
    // 0xb678b8: r1 = Instance_AlignmentDirectional
    //     0xb678b8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xb678bc: ldr             x1, [x1, #0x758]
    // 0xb678c0: StoreField: r0->field_2b = r1
    //     0xb678c0: stur            w1, [x0, #0x2b]
    // 0xb678c4: ldur            x1, [fp, #-0x20]
    // 0xb678c8: StoreField: r0->field_43 = r1
    //     0xb678c8: stur            w1, [x0, #0x43]
    // 0xb678cc: r1 = true
    //     0xb678cc: add             x1, NULL, #0x20  ; true
    // 0xb678d0: StoreField: r0->field_53 = r1
    //     0xb678d0: stur            w1, [x0, #0x53]
    // 0xb678d4: r2 = Instance_DragStartBehavior
    //     0xb678d4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb678d8: StoreField: r0->field_57 = r2
    //     0xb678d8: stur            w2, [x0, #0x57]
    // 0xb678dc: r2 = false
    //     0xb678dc: add             x2, NULL, #0x30  ; false
    // 0xb678e0: StoreField: r0->field_b = r2
    //     0xb678e0: stur            w2, [x0, #0xb]
    // 0xb678e4: StoreField: r0->field_f = r2
    //     0xb678e4: stur            w2, [x0, #0xf]
    // 0xb678e8: StoreField: r0->field_5f = r1
    //     0xb678e8: stur            w1, [x0, #0x5f]
    // 0xb678ec: StoreField: r0->field_63 = r1
    //     0xb678ec: stur            w1, [x0, #0x63]
    // 0xb678f0: LeaveFrame
    //     0xb678f0: mov             SP, fp
    //     0xb678f4: ldp             fp, lr, [SP], #0x10
    // 0xb678f8: ret
    //     0xb678f8: ret             
    // 0xb678fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb678fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb67900: b               #0xb66d74
  }
  [closure] NZakatResult <anonymous closure>(dynamic) {
    // ** addr: 0xb67904, size: 0xa0
    // 0xb67904: EnterFrame
    //     0xb67904: stp             fp, lr, [SP, #-0x10]!
    //     0xb67908: mov             fp, SP
    // 0xb6790c: AllocStack(0x18)
    //     0xb6790c: sub             SP, SP, #0x18
    // 0xb67910: SetupParameters()
    //     0xb67910: ldr             x0, [fp, #0x10]
    //     0xb67914: ldur            w2, [x0, #0x17]
    //     0xb67918: add             x2, x2, HEAP, lsl #32
    //     0xb6791c: stur            x2, [fp, #-8]
    // 0xb67920: CheckStackOverflow
    //     0xb67920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67924: cmp             SP, x16
    //     0xb67928: b.ls            #0xb6799c
    // 0xb6792c: LoadField: r1 = r2->field_f
    //     0xb6792c: ldur            w1, [x2, #0xf]
    // 0xb67930: DecompressPointer r1
    //     0xb67930: add             x1, x1, HEAP, lsl #32
    // 0xb67934: r0 = controller()
    //     0xb67934: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb67938: LoadField: r1 = r0->field_57
    //     0xb67938: ldur            w1, [x0, #0x57]
    // 0xb6793c: DecompressPointer r1
    //     0xb6793c: add             x1, x1, HEAP, lsl #32
    // 0xb67940: r0 = value()
    //     0xb67940: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb67944: mov             x2, x0
    // 0xb67948: ldur            x0, [fp, #-8]
    // 0xb6794c: stur            x2, [fp, #-0x10]
    // 0xb67950: LoadField: r1 = r0->field_f
    //     0xb67950: ldur            w1, [x0, #0xf]
    // 0xb67954: DecompressPointer r1
    //     0xb67954: add             x1, x1, HEAP, lsl #32
    // 0xb67958: r0 = controller()
    //     0xb67958: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb6795c: stur            x0, [fp, #-8]
    // 0xb67960: r0 = NZakatResult()
    //     0xb67960: bl              #0xb64288  ; AllocateNZakatResultStub -> NZakatResult (size=0x18)
    // 0xb67964: mov             x3, x0
    // 0xb67968: ldur            x0, [fp, #-0x10]
    // 0xb6796c: stur            x3, [fp, #-0x18]
    // 0xb67970: StoreField: r3->field_b = r0
    //     0xb67970: stur            w0, [x3, #0xb]
    // 0xb67974: ldur            x2, [fp, #-8]
    // 0xb67978: r1 = Function 'onClear':.
    //     0xb67978: add             x1, PP, #0x27, lsl #12  ; [pp+0x27558] AnonymousClosure: (0xb679a4), in [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::onClear (0xb679dc)
    //     0xb6797c: ldr             x1, [x1, #0x558]
    // 0xb67980: r0 = AllocateClosure()
    //     0xb67980: bl              #0xec1630  ; AllocateClosureStub
    // 0xb67984: mov             x1, x0
    // 0xb67988: ldur            x0, [fp, #-0x18]
    // 0xb6798c: StoreField: r0->field_f = r1
    //     0xb6798c: stur            w1, [x0, #0xf]
    // 0xb67990: LeaveFrame
    //     0xb67990: mov             SP, fp
    //     0xb67994: ldp             fp, lr, [SP], #0x10
    // 0xb67998: ret
    //     0xb67998: ret             
    // 0xb6799c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6799c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb679a0: b               #0xb6792c
  }
  [closure] ZakatRadioListTile<int> <anonymous closure>(dynamic) {
    // ** addr: 0xb680b0, size: 0xc0
    // 0xb680b0: EnterFrame
    //     0xb680b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb680b4: mov             fp, SP
    // 0xb680b8: AllocStack(0x18)
    //     0xb680b8: sub             SP, SP, #0x18
    // 0xb680bc: SetupParameters()
    //     0xb680bc: ldr             x0, [fp, #0x10]
    //     0xb680c0: ldur            w2, [x0, #0x17]
    //     0xb680c4: add             x2, x2, HEAP, lsl #32
    //     0xb680c8: stur            x2, [fp, #-8]
    // 0xb680cc: CheckStackOverflow
    //     0xb680cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb680d0: cmp             SP, x16
    //     0xb680d4: b.ls            #0xb68168
    // 0xb680d8: LoadField: r1 = r2->field_f
    //     0xb680d8: ldur            w1, [x2, #0xf]
    // 0xb680dc: DecompressPointer r1
    //     0xb680dc: add             x1, x1, HEAP, lsl #32
    // 0xb680e0: r0 = controller()
    //     0xb680e0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb680e4: LoadField: r1 = r0->field_67
    //     0xb680e4: ldur            w1, [x0, #0x67]
    // 0xb680e8: DecompressPointer r1
    //     0xb680e8: add             x1, x1, HEAP, lsl #32
    // 0xb680ec: r0 = value()
    //     0xb680ec: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb680f0: mov             x2, x0
    // 0xb680f4: ldur            x0, [fp, #-8]
    // 0xb680f8: stur            x2, [fp, #-0x10]
    // 0xb680fc: LoadField: r1 = r0->field_f
    //     0xb680fc: ldur            w1, [x0, #0xf]
    // 0xb68100: DecompressPointer r1
    //     0xb68100: add             x1, x1, HEAP, lsl #32
    // 0xb68104: r0 = controller()
    //     0xb68104: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68108: r1 = <int>
    //     0xb68108: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb6810c: stur            x0, [fp, #-8]
    // 0xb68110: r0 = ZakatRadioListTile()
    //     0xb68110: bl              #0xb68170  ; AllocateZakatRadioListTileStub -> ZakatRadioListTile<X0> (size=0x34)
    // 0xb68114: mov             x3, x0
    // 0xb68118: r0 = "Irigasi tadah hujan atau berasal dari saluran irigasi tidak berbayar"
    //     0xb68118: add             x0, PP, #0x27, lsl #12  ; [pp+0x275d0] "Irigasi tadah hujan atau berasal dari saluran irigasi tidak berbayar"
    //     0xb6811c: ldr             x0, [x0, #0x5d0]
    // 0xb68120: stur            x3, [fp, #-0x18]
    // 0xb68124: StoreField: r3->field_f = r0
    //     0xb68124: stur            w0, [x3, #0xf]
    // 0xb68128: r0 = 20
    //     0xb68128: movz            x0, #0x14
    // 0xb6812c: StoreField: r3->field_1b = r0
    //     0xb6812c: stur            w0, [x3, #0x1b]
    // 0xb68130: ldur            x0, [fp, #-0x10]
    // 0xb68134: StoreField: r3->field_1f = r0
    //     0xb68134: stur            w0, [x3, #0x1f]
    // 0xb68138: ldur            x2, [fp, #-8]
    // 0xb6813c: r1 = Function 'pengairanOnChanged':.
    //     0xb6813c: add             x1, PP, #0x27, lsl #12  ; [pp+0x275d8] AnonymousClosure: (0xb6817c), in [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::pengairanOnChanged (0xb681b8)
    //     0xb68140: ldr             x1, [x1, #0x5d8]
    // 0xb68144: r0 = AllocateClosure()
    //     0xb68144: bl              #0xec1630  ; AllocateClosureStub
    // 0xb68148: mov             x1, x0
    // 0xb6814c: ldur            x0, [fp, #-0x18]
    // 0xb68150: StoreField: r0->field_23 = r1
    //     0xb68150: stur            w1, [x0, #0x23]
    // 0xb68154: r1 = false
    //     0xb68154: add             x1, NULL, #0x30  ; false
    // 0xb68158: StoreField: r0->field_2b = r1
    //     0xb68158: stur            w1, [x0, #0x2b]
    // 0xb6815c: LeaveFrame
    //     0xb6815c: mov             SP, fp
    //     0xb68160: ldp             fp, lr, [SP], #0x10
    // 0xb68164: ret
    //     0xb68164: ret             
    // 0xb68168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6816c: b               #0xb680d8
  }
  [closure] ZakatRadioListTile<int> <anonymous closure>(dynamic) {
    // ** addr: 0xb68210, size: 0xc0
    // 0xb68210: EnterFrame
    //     0xb68210: stp             fp, lr, [SP, #-0x10]!
    //     0xb68214: mov             fp, SP
    // 0xb68218: AllocStack(0x18)
    //     0xb68218: sub             SP, SP, #0x18
    // 0xb6821c: SetupParameters()
    //     0xb6821c: ldr             x0, [fp, #0x10]
    //     0xb68220: ldur            w2, [x0, #0x17]
    //     0xb68224: add             x2, x2, HEAP, lsl #32
    //     0xb68228: stur            x2, [fp, #-8]
    // 0xb6822c: CheckStackOverflow
    //     0xb6822c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68230: cmp             SP, x16
    //     0xb68234: b.ls            #0xb682c8
    // 0xb68238: LoadField: r1 = r2->field_f
    //     0xb68238: ldur            w1, [x2, #0xf]
    // 0xb6823c: DecompressPointer r1
    //     0xb6823c: add             x1, x1, HEAP, lsl #32
    // 0xb68240: r0 = controller()
    //     0xb68240: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68244: LoadField: r1 = r0->field_67
    //     0xb68244: ldur            w1, [x0, #0x67]
    // 0xb68248: DecompressPointer r1
    //     0xb68248: add             x1, x1, HEAP, lsl #32
    // 0xb6824c: r0 = value()
    //     0xb6824c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb68250: mov             x2, x0
    // 0xb68254: ldur            x0, [fp, #-8]
    // 0xb68258: stur            x2, [fp, #-0x10]
    // 0xb6825c: LoadField: r1 = r0->field_f
    //     0xb6825c: ldur            w1, [x0, #0xf]
    // 0xb68260: DecompressPointer r1
    //     0xb68260: add             x1, x1, HEAP, lsl #32
    // 0xb68264: r0 = controller()
    //     0xb68264: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68268: r1 = <int>
    //     0xb68268: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb6826c: stur            x0, [fp, #-8]
    // 0xb68270: r0 = ZakatRadioListTile()
    //     0xb68270: bl              #0xb68170  ; AllocateZakatRadioListTileStub -> ZakatRadioListTile<X0> (size=0x34)
    // 0xb68274: mov             x3, x0
    // 0xb68278: r0 = "Irigasi berbayar"
    //     0xb68278: add             x0, PP, #0x27, lsl #12  ; [pp+0x275e0] "Irigasi berbayar"
    //     0xb6827c: ldr             x0, [x0, #0x5e0]
    // 0xb68280: stur            x3, [fp, #-0x18]
    // 0xb68284: StoreField: r3->field_f = r0
    //     0xb68284: stur            w0, [x3, #0xf]
    // 0xb68288: r0 = 10
    //     0xb68288: movz            x0, #0xa
    // 0xb6828c: StoreField: r3->field_1b = r0
    //     0xb6828c: stur            w0, [x3, #0x1b]
    // 0xb68290: ldur            x0, [fp, #-0x10]
    // 0xb68294: StoreField: r3->field_1f = r0
    //     0xb68294: stur            w0, [x3, #0x1f]
    // 0xb68298: ldur            x2, [fp, #-8]
    // 0xb6829c: r1 = Function 'pengairanOnChanged':.
    //     0xb6829c: add             x1, PP, #0x27, lsl #12  ; [pp+0x275d8] AnonymousClosure: (0xb6817c), in [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::pengairanOnChanged (0xb681b8)
    //     0xb682a0: ldr             x1, [x1, #0x5d8]
    // 0xb682a4: r0 = AllocateClosure()
    //     0xb682a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb682a8: mov             x1, x0
    // 0xb682ac: ldur            x0, [fp, #-0x18]
    // 0xb682b0: StoreField: r0->field_23 = r1
    //     0xb682b0: stur            w1, [x0, #0x23]
    // 0xb682b4: r1 = false
    //     0xb682b4: add             x1, NULL, #0x30  ; false
    // 0xb682b8: StoreField: r0->field_2b = r1
    //     0xb682b8: stur            w1, [x0, #0x2b]
    // 0xb682bc: LeaveFrame
    //     0xb682bc: mov             SP, fp
    //     0xb682c0: ldp             fp, lr, [SP], #0x10
    // 0xb682c4: ret
    //     0xb682c4: ret             
    // 0xb682c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb682c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb682cc: b               #0xb68238
  }
  [closure] NZakatTextField <anonymous closure>(dynamic) {
    // ** addr: 0xb682d0, size: 0x1a0
    // 0xb682d0: EnterFrame
    //     0xb682d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb682d4: mov             fp, SP
    // 0xb682d8: AllocStack(0x30)
    //     0xb682d8: sub             SP, SP, #0x30
    // 0xb682dc: SetupParameters()
    //     0xb682dc: ldr             x0, [fp, #0x10]
    //     0xb682e0: ldur            w2, [x0, #0x17]
    //     0xb682e4: add             x2, x2, HEAP, lsl #32
    //     0xb682e8: stur            x2, [fp, #-8]
    // 0xb682ec: CheckStackOverflow
    //     0xb682ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb682f0: cmp             SP, x16
    //     0xb682f4: b.ls            #0xb68468
    // 0xb682f8: LoadField: r1 = r2->field_f
    //     0xb682f8: ldur            w1, [x2, #0xf]
    // 0xb682fc: DecompressPointer r1
    //     0xb682fc: add             x1, x1, HEAP, lsl #32
    // 0xb68300: r0 = controller()
    //     0xb68300: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68304: mov             x1, x0
    // 0xb68308: LoadField: r0 = r1->field_1f
    //     0xb68308: ldur            w0, [x1, #0x1f]
    // 0xb6830c: DecompressPointer r0
    //     0xb6830c: add             x0, x0, HEAP, lsl #32
    // 0xb68310: r16 = Sentinel
    //     0xb68310: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb68314: cmp             w0, w16
    // 0xb68318: b.ne            #0xb68328
    // 0xb6831c: r2 = jumlahPanen
    //     0xb6831c: add             x2, PP, #0x27, lsl #12  ; [pp+0x27560] Field <ZakatPertanianController.jumlahPanen>: late final (offset: 0x20)
    //     0xb68320: ldr             x2, [x2, #0x560]
    // 0xb68324: r0 = InitLateFinalInstanceField()
    //     0xb68324: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb68328: mov             x2, x0
    // 0xb6832c: ldur            x0, [fp, #-8]
    // 0xb68330: stur            x2, [fp, #-0x10]
    // 0xb68334: LoadField: r1 = r0->field_f
    //     0xb68334: ldur            w1, [x0, #0xf]
    // 0xb68338: DecompressPointer r1
    //     0xb68338: add             x1, x1, HEAP, lsl #32
    // 0xb6833c: r0 = controller()
    //     0xb6833c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68340: r1 = Null
    //     0xb68340: mov             x1, NULL
    // 0xb68344: r2 = 6
    //     0xb68344: movz            x2, #0x6
    // 0xb68348: stur            x0, [fp, #-0x18]
    // 0xb6834c: r0 = AllocateArray()
    //     0xb6834c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb68350: stur            x0, [fp, #-0x20]
    // 0xb68354: r16 = "Jumlah "
    //     0xb68354: add             x16, PP, #0x27, lsl #12  ; [pp+0x275e8] "Jumlah "
    //     0xb68358: ldr             x16, [x16, #0x5e8]
    // 0xb6835c: StoreField: r0->field_f = r16
    //     0xb6835c: stur            w16, [x0, #0xf]
    // 0xb68360: ldur            x2, [fp, #-8]
    // 0xb68364: LoadField: r1 = r2->field_f
    //     0xb68364: ldur            w1, [x2, #0xf]
    // 0xb68368: DecompressPointer r1
    //     0xb68368: add             x1, x1, HEAP, lsl #32
    // 0xb6836c: r0 = controller()
    //     0xb6836c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68370: mov             x1, x0
    // 0xb68374: LoadField: r0 = r1->field_23
    //     0xb68374: ldur            w0, [x1, #0x23]
    // 0xb68378: DecompressPointer r0
    //     0xb68378: add             x0, x0, HEAP, lsl #32
    // 0xb6837c: r16 = Sentinel
    //     0xb6837c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb68380: cmp             w0, w16
    // 0xb68384: b.ne            #0xb68394
    // 0xb68388: r2 = jenisPertanianController
    //     0xb68388: add             x2, PP, #0x27, lsl #12  ; [pp+0x274b8] Field <ZakatPertanianController.jenisPertanianController>: late final (offset: 0x24)
    //     0xb6838c: ldr             x2, [x2, #0x4b8]
    // 0xb68390: r0 = InitLateFinalInstanceField()
    //     0xb68390: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb68394: LoadField: r1 = r0->field_27
    //     0xb68394: ldur            w1, [x0, #0x27]
    // 0xb68398: DecompressPointer r1
    //     0xb68398: add             x1, x1, HEAP, lsl #32
    // 0xb6839c: LoadField: r0 = r1->field_7
    //     0xb6839c: ldur            w0, [x1, #7]
    // 0xb683a0: DecompressPointer r0
    //     0xb683a0: add             x0, x0, HEAP, lsl #32
    // 0xb683a4: ldur            x1, [fp, #-0x20]
    // 0xb683a8: ArrayStore: r1[1] = r0  ; List_4
    //     0xb683a8: add             x25, x1, #0x13
    //     0xb683ac: str             w0, [x25]
    //     0xb683b0: tbz             w0, #0, #0xb683cc
    //     0xb683b4: ldurb           w16, [x1, #-1]
    //     0xb683b8: ldurb           w17, [x0, #-1]
    //     0xb683bc: and             x16, x17, x16, lsr #2
    //     0xb683c0: tst             x16, HEAP, lsr #32
    //     0xb683c4: b.eq            #0xb683cc
    //     0xb683c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb683cc: ldur            x0, [fp, #-0x20]
    // 0xb683d0: r16 = " yang Dipanen"
    //     0xb683d0: add             x16, PP, #0x27, lsl #12  ; [pp+0x275f0] " yang Dipanen"
    //     0xb683d4: ldr             x16, [x16, #0x5f0]
    // 0xb683d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb683d8: stur            w16, [x0, #0x17]
    // 0xb683dc: str             x0, [SP]
    // 0xb683e0: r0 = _interpolate()
    //     0xb683e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb683e4: mov             x2, x0
    // 0xb683e8: ldur            x0, [fp, #-8]
    // 0xb683ec: stur            x2, [fp, #-0x20]
    // 0xb683f0: LoadField: r1 = r0->field_f
    //     0xb683f0: ldur            w1, [x0, #0xf]
    // 0xb683f4: DecompressPointer r1
    //     0xb683f4: add             x1, x1, HEAP, lsl #32
    // 0xb683f8: r0 = controller()
    //     0xb683f8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb683fc: mov             x1, x0
    // 0xb68400: r0 = suffix()
    //     0xb68400: bl              #0xb67fd4  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::suffix
    // 0xb68404: stur            x0, [fp, #-8]
    // 0xb68408: r0 = NZakatTextField()
    //     0xb68408: bl              #0xae765c  ; AllocateNZakatTextFieldStub -> NZakatTextField (size=0x34)
    // 0xb6840c: mov             x3, x0
    // 0xb68410: ldur            x0, [fp, #-0x10]
    // 0xb68414: stur            x3, [fp, #-0x28]
    // 0xb68418: StoreField: r3->field_b = r0
    //     0xb68418: stur            w0, [x3, #0xb]
    // 0xb6841c: ldur            x0, [fp, #-0x20]
    // 0xb68420: StoreField: r3->field_f = r0
    //     0xb68420: stur            w0, [x3, #0xf]
    // 0xb68424: ldur            x2, [fp, #-0x18]
    // 0xb68428: r1 = Function 'onChange':.
    //     0xb68428: add             x1, PP, #0x27, lsl #12  ; [pp+0x275f8] AnonymousClosure: (0xb67cec), in [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::onChange (0xb67a54)
    //     0xb6842c: ldr             x1, [x1, #0x5f8]
    // 0xb68430: r0 = AllocateClosure()
    //     0xb68430: bl              #0xec1630  ; AllocateClosureStub
    // 0xb68434: mov             x1, x0
    // 0xb68438: ldur            x0, [fp, #-0x28]
    // 0xb6843c: StoreField: r0->field_1b = r1
    //     0xb6843c: stur            w1, [x0, #0x1b]
    // 0xb68440: r1 = true
    //     0xb68440: add             x1, NULL, #0x20  ; true
    // 0xb68444: StoreField: r0->field_1f = r1
    //     0xb68444: stur            w1, [x0, #0x1f]
    // 0xb68448: ldur            x1, [fp, #-8]
    // 0xb6844c: StoreField: r0->field_23 = r1
    //     0xb6844c: stur            w1, [x0, #0x23]
    // 0xb68450: r1 = false
    //     0xb68450: add             x1, NULL, #0x30  ; false
    // 0xb68454: StoreField: r0->field_27 = r1
    //     0xb68454: stur            w1, [x0, #0x27]
    // 0xb68458: StoreField: r0->field_2f = r1
    //     0xb68458: stur            w1, [x0, #0x2f]
    // 0xb6845c: LeaveFrame
    //     0xb6845c: mov             SP, fp
    //     0xb68460: ldp             fp, lr, [SP], #0x10
    // 0xb68464: ret
    //     0xb68464: ret             
    // 0xb68468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68468: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6846c: b               #0xb682f8
  }
  [closure] Wrap <anonymous closure>(dynamic) {
    // ** addr: 0xb68470, size: 0xe8
    // 0xb68470: EnterFrame
    //     0xb68470: stp             fp, lr, [SP, #-0x10]!
    //     0xb68474: mov             fp, SP
    // 0xb68478: AllocStack(0x28)
    //     0xb68478: sub             SP, SP, #0x28
    // 0xb6847c: SetupParameters()
    //     0xb6847c: ldr             x0, [fp, #0x10]
    //     0xb68480: ldur            w2, [x0, #0x17]
    //     0xb68484: add             x2, x2, HEAP, lsl #32
    //     0xb68488: stur            x2, [fp, #-8]
    // 0xb6848c: CheckStackOverflow
    //     0xb6848c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68490: cmp             SP, x16
    //     0xb68494: b.ls            #0xb68550
    // 0xb68498: LoadField: r1 = r2->field_f
    //     0xb68498: ldur            w1, [x2, #0xf]
    // 0xb6849c: DecompressPointer r1
    //     0xb6849c: add             x1, x1, HEAP, lsl #32
    // 0xb684a0: r0 = controller()
    //     0xb684a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb684a4: LoadField: r3 = r0->field_5f
    //     0xb684a4: ldur            w3, [x0, #0x5f]
    // 0xb684a8: DecompressPointer r3
    //     0xb684a8: add             x3, x3, HEAP, lsl #32
    // 0xb684ac: ldur            x2, [fp, #-8]
    // 0xb684b0: stur            x3, [fp, #-0x10]
    // 0xb684b4: r1 = Function '<anonymous closure>':.
    //     0xb684b4: add             x1, PP, #0x27, lsl #12  ; [pp+0x27600] AnonymousClosure: (0xb68558), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb684b8: ldr             x1, [x1, #0x600]
    // 0xb684bc: r0 = AllocateClosure()
    //     0xb684bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb684c0: r16 = <TagButton>
    //     0xb684c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27608] TypeArguments: <TagButton>
    //     0xb684c4: ldr             x16, [x16, #0x608]
    // 0xb684c8: ldur            lr, [fp, #-0x10]
    // 0xb684cc: stp             lr, x16, [SP, #8]
    // 0xb684d0: str             x0, [SP]
    // 0xb684d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb684d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb684d8: r0 = map()
    //     0xb684d8: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb684dc: LoadField: r1 = r0->field_7
    //     0xb684dc: ldur            w1, [x0, #7]
    // 0xb684e0: DecompressPointer r1
    //     0xb684e0: add             x1, x1, HEAP, lsl #32
    // 0xb684e4: mov             x2, x0
    // 0xb684e8: r0 = _GrowableList.of()
    //     0xb684e8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb684ec: stur            x0, [fp, #-8]
    // 0xb684f0: r0 = Wrap()
    //     0xb684f0: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb684f4: r1 = Instance_Axis
    //     0xb684f4: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb684f8: StoreField: r0->field_f = r1
    //     0xb684f8: stur            w1, [x0, #0xf]
    // 0xb684fc: r1 = Instance_WrapAlignment
    //     0xb684fc: add             x1, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xb68500: ldr             x1, [x1, #0x610]
    // 0xb68504: StoreField: r0->field_13 = r1
    //     0xb68504: stur            w1, [x0, #0x13]
    // 0xb68508: d0 = 8.000000
    //     0xb68508: fmov            d0, #8.00000000
    // 0xb6850c: ArrayStore: r0[0] = d0  ; List_8
    //     0xb6850c: stur            d0, [x0, #0x17]
    // 0xb68510: StoreField: r0->field_1f = r1
    //     0xb68510: stur            w1, [x0, #0x1f]
    // 0xb68514: StoreField: r0->field_23 = rZR
    //     0xb68514: stur            xzr, [x0, #0x23]
    // 0xb68518: r1 = Instance_WrapCrossAlignment
    //     0xb68518: add             x1, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xb6851c: ldr             x1, [x1, #0x618]
    // 0xb68520: StoreField: r0->field_2b = r1
    //     0xb68520: stur            w1, [x0, #0x2b]
    // 0xb68524: r1 = Instance_VerticalDirection
    //     0xb68524: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb68528: ldr             x1, [x1, #0x748]
    // 0xb6852c: StoreField: r0->field_33 = r1
    //     0xb6852c: stur            w1, [x0, #0x33]
    // 0xb68530: r1 = Instance_Clip
    //     0xb68530: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb68534: ldr             x1, [x1, #0x750]
    // 0xb68538: StoreField: r0->field_37 = r1
    //     0xb68538: stur            w1, [x0, #0x37]
    // 0xb6853c: ldur            x1, [fp, #-8]
    // 0xb68540: StoreField: r0->field_b = r1
    //     0xb68540: stur            w1, [x0, #0xb]
    // 0xb68544: LeaveFrame
    //     0xb68544: mov             SP, fp
    //     0xb68548: ldp             fp, lr, [SP], #0x10
    // 0xb6854c: ret
    //     0xb6854c: ret             
    // 0xb68550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68554: b               #0xb68498
  }
  [closure] TagButton <anonymous closure>(dynamic, String) {
    // ** addr: 0xb68558, size: 0xd0
    // 0xb68558: EnterFrame
    //     0xb68558: stp             fp, lr, [SP, #-0x10]!
    //     0xb6855c: mov             fp, SP
    // 0xb68560: AllocStack(0x28)
    //     0xb68560: sub             SP, SP, #0x28
    // 0xb68564: SetupParameters()
    //     0xb68564: ldr             x0, [fp, #0x18]
    //     0xb68568: ldur            w1, [x0, #0x17]
    //     0xb6856c: add             x1, x1, HEAP, lsl #32
    //     0xb68570: stur            x1, [fp, #-8]
    // 0xb68574: CheckStackOverflow
    //     0xb68574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68578: cmp             SP, x16
    //     0xb6857c: b.ls            #0xb68620
    // 0xb68580: r1 = 1
    //     0xb68580: movz            x1, #0x1
    // 0xb68584: r0 = AllocateContext()
    //     0xb68584: bl              #0xec126c  ; AllocateContextStub
    // 0xb68588: mov             x2, x0
    // 0xb6858c: ldur            x0, [fp, #-8]
    // 0xb68590: stur            x2, [fp, #-0x10]
    // 0xb68594: StoreField: r2->field_b = r0
    //     0xb68594: stur            w0, [x2, #0xb]
    // 0xb68598: ldr             x3, [fp, #0x10]
    // 0xb6859c: StoreField: r2->field_f = r3
    //     0xb6859c: stur            w3, [x2, #0xf]
    // 0xb685a0: LoadField: r1 = r0->field_f
    //     0xb685a0: ldur            w1, [x0, #0xf]
    // 0xb685a4: DecompressPointer r1
    //     0xb685a4: add             x1, x1, HEAP, lsl #32
    // 0xb685a8: r0 = controller()
    //     0xb685a8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb685ac: LoadField: r1 = r0->field_63
    //     0xb685ac: ldur            w1, [x0, #0x63]
    // 0xb685b0: DecompressPointer r1
    //     0xb685b0: add             x1, x1, HEAP, lsl #32
    // 0xb685b4: r0 = value()
    //     0xb685b4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb685b8: r1 = LoadClassIdInstr(r0)
    //     0xb685b8: ldur            x1, [x0, #-1]
    //     0xb685bc: ubfx            x1, x1, #0xc, #0x14
    // 0xb685c0: ldr             x16, [fp, #0x10]
    // 0xb685c4: stp             x16, x0, [SP]
    // 0xb685c8: mov             x0, x1
    // 0xb685cc: mov             lr, x0
    // 0xb685d0: ldr             lr, [x21, lr, lsl #3]
    // 0xb685d4: blr             lr
    // 0xb685d8: stur            x0, [fp, #-8]
    // 0xb685dc: r0 = TagButton()
    //     0xb685dc: bl              #0xb68628  ; AllocateTagButtonStub -> TagButton (size=0x18)
    // 0xb685e0: mov             x3, x0
    // 0xb685e4: ldr             x0, [fp, #0x10]
    // 0xb685e8: stur            x3, [fp, #-0x18]
    // 0xb685ec: StoreField: r3->field_b = r0
    //     0xb685ec: stur            w0, [x3, #0xb]
    // 0xb685f0: ldur            x2, [fp, #-0x10]
    // 0xb685f4: r1 = Function '<anonymous closure>':.
    //     0xb685f4: add             x1, PP, #0x27, lsl #12  ; [pp+0x27620] AnonymousClosure: (0xb68634), in [package:nuonline/app/modules/zakat/views/zakat_pertanian_view.dart] ZakatPertanianView::build (0xb66d54)
    //     0xb685f8: ldr             x1, [x1, #0x620]
    // 0xb685fc: r0 = AllocateClosure()
    //     0xb685fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb68600: mov             x1, x0
    // 0xb68604: ldur            x0, [fp, #-0x18]
    // 0xb68608: StoreField: r0->field_f = r1
    //     0xb68608: stur            w1, [x0, #0xf]
    // 0xb6860c: ldur            x1, [fp, #-8]
    // 0xb68610: StoreField: r0->field_13 = r1
    //     0xb68610: stur            w1, [x0, #0x13]
    // 0xb68614: LeaveFrame
    //     0xb68614: mov             SP, fp
    //     0xb68618: ldp             fp, lr, [SP], #0x10
    // 0xb6861c: ret
    //     0xb6861c: ret             
    // 0xb68620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68624: b               #0xb68580
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb68634, size: 0x68
    // 0xb68634: EnterFrame
    //     0xb68634: stp             fp, lr, [SP, #-0x10]!
    //     0xb68638: mov             fp, SP
    // 0xb6863c: AllocStack(0x8)
    //     0xb6863c: sub             SP, SP, #8
    // 0xb68640: SetupParameters()
    //     0xb68640: ldr             x0, [fp, #0x10]
    //     0xb68644: ldur            w2, [x0, #0x17]
    //     0xb68648: add             x2, x2, HEAP, lsl #32
    //     0xb6864c: stur            x2, [fp, #-8]
    // 0xb68650: CheckStackOverflow
    //     0xb68650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68654: cmp             SP, x16
    //     0xb68658: b.ls            #0xb68694
    // 0xb6865c: LoadField: r0 = r2->field_b
    //     0xb6865c: ldur            w0, [x2, #0xb]
    // 0xb68660: DecompressPointer r0
    //     0xb68660: add             x0, x0, HEAP, lsl #32
    // 0xb68664: LoadField: r1 = r0->field_f
    //     0xb68664: ldur            w1, [x0, #0xf]
    // 0xb68668: DecompressPointer r1
    //     0xb68668: add             x1, x1, HEAP, lsl #32
    // 0xb6866c: r0 = controller()
    //     0xb6866c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68670: mov             x1, x0
    // 0xb68674: ldur            x0, [fp, #-8]
    // 0xb68678: LoadField: r2 = r0->field_f
    //     0xb68678: ldur            w2, [x0, #0xf]
    // 0xb6867c: DecompressPointer r2
    //     0xb6867c: add             x2, x2, HEAP, lsl #32
    // 0xb68680: r0 = satuanOnTap()
    //     0xb68680: bl              #0xb6869c  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::satuanOnTap
    // 0xb68684: r0 = Null
    //     0xb68684: mov             x0, NULL
    // 0xb68688: LeaveFrame
    //     0xb68688: mov             SP, fp
    //     0xb6868c: ldp             fp, lr, [SP], #0x10
    // 0xb68690: ret
    //     0xb68690: ret             
    // 0xb68694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68698: b               #0xb6865c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb686ec, size: 0x224
    // 0xb686ec: EnterFrame
    //     0xb686ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb686f0: mov             fp, SP
    // 0xb686f4: AllocStack(0x38)
    //     0xb686f4: sub             SP, SP, #0x38
    // 0xb686f8: SetupParameters(ZakatPertanianView this /* r0 */)
    //     0xb686f8: stur            NULL, [fp, #-8]
    //     0xb686fc: movz            x2, #0
    //     0xb68700: add             x0, fp, w2, sxtw #2
    //     0xb68704: ldr             x0, [x0, #0x10]
    //     0xb68708: ldur            w1, [x0, #0x17]
    //     0xb6870c: add             x1, x1, HEAP, lsl #32
    //     0xb68710: stur            x1, [fp, #-0x10]
    // 0xb68714: CheckStackOverflow
    //     0xb68714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68718: cmp             SP, x16
    //     0xb6871c: b.ls            #0xb68908
    // 0xb68720: InitAsync() -> Future<void?>
    //     0xb68720: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb68724: bl              #0x661298  ; InitAsyncStub
    // 0xb68728: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb68728: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6872c: ldr             x0, [x0, #0x2670]
    //     0xb68730: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb68734: cmp             w0, w16
    //     0xb68738: b.ne            #0xb68744
    //     0xb6873c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb68740: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb68744: r1 = Null
    //     0xb68744: mov             x1, NULL
    // 0xb68748: r2 = 4
    //     0xb68748: movz            x2, #0x4
    // 0xb6874c: r0 = AllocateArray()
    //     0xb6874c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb68750: stur            x0, [fp, #-0x18]
    // 0xb68754: r16 = "jenis_pertanian"
    //     0xb68754: add             x16, PP, #0x27, lsl #12  ; [pp+0x277c0] "jenis_pertanian"
    //     0xb68758: ldr             x16, [x16, #0x7c0]
    // 0xb6875c: StoreField: r0->field_f = r16
    //     0xb6875c: stur            w16, [x0, #0xf]
    // 0xb68760: ldur            x2, [fp, #-0x10]
    // 0xb68764: LoadField: r1 = r2->field_f
    //     0xb68764: ldur            w1, [x2, #0xf]
    // 0xb68768: DecompressPointer r1
    //     0xb68768: add             x1, x1, HEAP, lsl #32
    // 0xb6876c: r0 = controller()
    //     0xb6876c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68770: mov             x1, x0
    // 0xb68774: LoadField: r0 = r1->field_23
    //     0xb68774: ldur            w0, [x1, #0x23]
    // 0xb68778: DecompressPointer r0
    //     0xb68778: add             x0, x0, HEAP, lsl #32
    // 0xb6877c: r16 = Sentinel
    //     0xb6877c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb68780: cmp             w0, w16
    // 0xb68784: b.ne            #0xb68794
    // 0xb68788: r2 = jenisPertanianController
    //     0xb68788: add             x2, PP, #0x27, lsl #12  ; [pp+0x274b8] Field <ZakatPertanianController.jenisPertanianController>: late final (offset: 0x24)
    //     0xb6878c: ldr             x2, [x2, #0x4b8]
    // 0xb68790: r0 = InitLateFinalInstanceField()
    //     0xb68790: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb68794: LoadField: r1 = r0->field_27
    //     0xb68794: ldur            w1, [x0, #0x27]
    // 0xb68798: DecompressPointer r1
    //     0xb68798: add             x1, x1, HEAP, lsl #32
    // 0xb6879c: LoadField: r0 = r1->field_7
    //     0xb6879c: ldur            w0, [x1, #7]
    // 0xb687a0: DecompressPointer r0
    //     0xb687a0: add             x0, x0, HEAP, lsl #32
    // 0xb687a4: ldur            x1, [fp, #-0x18]
    // 0xb687a8: ArrayStore: r1[1] = r0  ; List_4
    //     0xb687a8: add             x25, x1, #0x13
    //     0xb687ac: str             w0, [x25]
    //     0xb687b0: tbz             w0, #0, #0xb687cc
    //     0xb687b4: ldurb           w16, [x1, #-1]
    //     0xb687b8: ldurb           w17, [x0, #-1]
    //     0xb687bc: and             x16, x17, x16, lsr #2
    //     0xb687c0: tst             x16, HEAP, lsr #32
    //     0xb687c4: b.eq            #0xb687cc
    //     0xb687c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb687cc: r16 = <String, String>
    //     0xb687cc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb687d0: ldr             x16, [x16, #0x668]
    // 0xb687d4: ldur            lr, [fp, #-0x18]
    // 0xb687d8: stp             lr, x16, [SP]
    // 0xb687dc: r0 = Map._fromLiteral()
    //     0xb687dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb687e0: r16 = "/zakat/select-pertanian"
    //     0xb687e0: add             x16, PP, #0x27, lsl #12  ; [pp+0x277c8] "/zakat/select-pertanian"
    //     0xb687e4: ldr             x16, [x16, #0x7c8]
    // 0xb687e8: stp             x16, NULL, [SP, #8]
    // 0xb687ec: str             x0, [SP]
    // 0xb687f0: r4 = const [0x1, 0x2, 0x2, 0x1, parameters, 0x1, null]
    //     0xb687f0: add             x4, PP, #0x27, lsl #12  ; [pp+0x277d0] List(7) [0x1, 0x2, 0x2, 0x1, "parameters", 0x1, Null]
    //     0xb687f4: ldr             x4, [x4, #0x7d0]
    // 0xb687f8: r0 = GetNavigation.toNamed()
    //     0xb687f8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb687fc: mov             x1, x0
    // 0xb68800: stur            x1, [fp, #-0x18]
    // 0xb68804: r0 = Await()
    //     0xb68804: bl              #0x661044  ; AwaitStub
    // 0xb68808: stur            x0, [fp, #-0x18]
    // 0xb6880c: cmp             w0, NULL
    // 0xb68810: b.eq            #0xb68900
    // 0xb68814: ldur            x2, [fp, #-0x10]
    // 0xb68818: LoadField: r1 = r2->field_f
    //     0xb68818: ldur            w1, [x2, #0xf]
    // 0xb6881c: DecompressPointer r1
    //     0xb6881c: add             x1, x1, HEAP, lsl #32
    // 0xb68820: r0 = controller()
    //     0xb68820: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68824: mov             x1, x0
    // 0xb68828: LoadField: r0 = r1->field_23
    //     0xb68828: ldur            w0, [x1, #0x23]
    // 0xb6882c: DecompressPointer r0
    //     0xb6882c: add             x0, x0, HEAP, lsl #32
    // 0xb68830: r16 = Sentinel
    //     0xb68830: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb68834: cmp             w0, w16
    // 0xb68838: b.ne            #0xb68848
    // 0xb6883c: r2 = jenisPertanianController
    //     0xb6883c: add             x2, PP, #0x27, lsl #12  ; [pp+0x274b8] Field <ZakatPertanianController.jenisPertanianController>: late final (offset: 0x24)
    //     0xb68840: ldr             x2, [x2, #0x4b8]
    // 0xb68844: r0 = InitLateFinalInstanceField()
    //     0xb68844: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb68848: mov             x3, x0
    // 0xb6884c: ldur            x0, [fp, #-0x18]
    // 0xb68850: r2 = Null
    //     0xb68850: mov             x2, NULL
    // 0xb68854: r1 = Null
    //     0xb68854: mov             x1, NULL
    // 0xb68858: stur            x3, [fp, #-0x20]
    // 0xb6885c: r4 = 60
    //     0xb6885c: movz            x4, #0x3c
    // 0xb68860: branchIfSmi(r0, 0xb6886c)
    //     0xb68860: tbz             w0, #0, #0xb6886c
    // 0xb68864: r4 = LoadClassIdInstr(r0)
    //     0xb68864: ldur            x4, [x0, #-1]
    //     0xb68868: ubfx            x4, x4, #0xc, #0x14
    // 0xb6886c: sub             x4, x4, #0x5e
    // 0xb68870: cmp             x4, #1
    // 0xb68874: b.ls            #0xb68888
    // 0xb68878: r8 = String
    //     0xb68878: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xb6887c: r3 = Null
    //     0xb6887c: add             x3, PP, #0x27, lsl #12  ; [pp+0x277d8] Null
    //     0xb68880: ldr             x3, [x3, #0x7d8]
    // 0xb68884: r0 = String()
    //     0xb68884: bl              #0xed43b0  ; IsType_String_Stub
    // 0xb68888: ldur            x1, [fp, #-0x20]
    // 0xb6888c: ldur            x2, [fp, #-0x18]
    // 0xb68890: r0 = text=()
    //     0xb68890: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xb68894: ldur            x0, [fp, #-0x10]
    // 0xb68898: LoadField: r1 = r0->field_f
    //     0xb68898: ldur            w1, [x0, #0xf]
    // 0xb6889c: DecompressPointer r1
    //     0xb6889c: add             x1, x1, HEAP, lsl #32
    // 0xb688a0: r0 = controller()
    //     0xb688a0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb688a4: LoadField: r3 = r0->field_5b
    //     0xb688a4: ldur            w3, [x0, #0x5b]
    // 0xb688a8: DecompressPointer r3
    //     0xb688a8: add             x3, x3, HEAP, lsl #32
    // 0xb688ac: stur            x3, [fp, #-0x20]
    // 0xb688b0: r1 = Null
    //     0xb688b0: mov             x1, NULL
    // 0xb688b4: r2 = 4
    //     0xb688b4: movz            x2, #0x4
    // 0xb688b8: r0 = AllocateArray()
    //     0xb688b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb688bc: r16 = "Nishab "
    //     0xb688bc: add             x16, PP, #0x27, lsl #12  ; [pp+0x277e8] "Nishab "
    //     0xb688c0: ldr             x16, [x16, #0x7e8]
    // 0xb688c4: StoreField: r0->field_f = r16
    //     0xb688c4: stur            w16, [x0, #0xf]
    // 0xb688c8: ldur            x1, [fp, #-0x18]
    // 0xb688cc: StoreField: r0->field_13 = r1
    //     0xb688cc: stur            w1, [x0, #0x13]
    // 0xb688d0: str             x0, [SP]
    // 0xb688d4: r0 = _interpolate()
    //     0xb688d4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb688d8: ldur            x1, [fp, #-0x20]
    // 0xb688dc: mov             x2, x0
    // 0xb688e0: r0 = value=()
    //     0xb688e0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb688e4: ldur            x0, [fp, #-0x10]
    // 0xb688e8: LoadField: r1 = r0->field_f
    //     0xb688e8: ldur            w1, [x0, #0xf]
    // 0xb688ec: DecompressPointer r1
    //     0xb688ec: add             x1, x1, HEAP, lsl #32
    // 0xb688f0: r0 = controller()
    //     0xb688f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb688f4: mov             x1, x0
    // 0xb688f8: r2 = 0
    //     0xb688f8: movz            x2, #0
    // 0xb688fc: r0 = onChange()
    //     0xb688fc: bl              #0xb67a54  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::onChange
    // 0xb68900: r0 = Null
    //     0xb68900: mov             x0, NULL
    // 0xb68904: r0 = ReturnAsyncNotFuture()
    //     0xb68904: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb68908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6890c: b               #0xb68720
  }
  [closure] NNishabCard <anonymous closure>(dynamic) {
    // ** addr: 0xb68968, size: 0xf0
    // 0xb68968: EnterFrame
    //     0xb68968: stp             fp, lr, [SP, #-0x10]!
    //     0xb6896c: mov             fp, SP
    // 0xb68970: AllocStack(0x20)
    //     0xb68970: sub             SP, SP, #0x20
    // 0xb68974: SetupParameters()
    //     0xb68974: ldr             x0, [fp, #0x10]
    //     0xb68978: ldur            w2, [x0, #0x17]
    //     0xb6897c: add             x2, x2, HEAP, lsl #32
    //     0xb68980: stur            x2, [fp, #-8]
    // 0xb68984: CheckStackOverflow
    //     0xb68984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68988: cmp             SP, x16
    //     0xb6898c: b.ls            #0xb68a50
    // 0xb68990: LoadField: r1 = r2->field_f
    //     0xb68990: ldur            w1, [x2, #0xf]
    // 0xb68994: DecompressPointer r1
    //     0xb68994: add             x1, x1, HEAP, lsl #32
    // 0xb68998: r0 = controller()
    //     0xb68998: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb6899c: mov             x1, x0
    // 0xb689a0: r0 = nishabInfo()
    //     0xb689a0: bl              #0xb68e84  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::nishabInfo
    // 0xb689a4: mov             x2, x0
    // 0xb689a8: ldur            x0, [fp, #-8]
    // 0xb689ac: stur            x2, [fp, #-0x10]
    // 0xb689b0: LoadField: r1 = r0->field_f
    //     0xb689b0: ldur            w1, [x0, #0xf]
    // 0xb689b4: DecompressPointer r1
    //     0xb689b4: add             x1, x1, HEAP, lsl #32
    // 0xb689b8: r0 = controller()
    //     0xb689b8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb689bc: LoadField: r1 = r0->field_5b
    //     0xb689bc: ldur            w1, [x0, #0x5b]
    // 0xb689c0: DecompressPointer r1
    //     0xb689c0: add             x1, x1, HEAP, lsl #32
    // 0xb689c4: r0 = value()
    //     0xb689c4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb689c8: mov             x2, x0
    // 0xb689cc: ldur            x0, [fp, #-8]
    // 0xb689d0: stur            x2, [fp, #-0x18]
    // 0xb689d4: LoadField: r1 = r0->field_f
    //     0xb689d4: ldur            w1, [x0, #0xf]
    // 0xb689d8: DecompressPointer r1
    //     0xb689d8: add             x1, x1, HEAP, lsl #32
    // 0xb689dc: r0 = controller()
    //     0xb689dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb689e0: mov             x1, x0
    // 0xb689e4: r0 = nishabValue()
    //     0xb689e4: bl              #0xb68b34  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::nishabValue
    // 0xb689e8: mov             x2, x0
    // 0xb689ec: ldur            x0, [fp, #-8]
    // 0xb689f0: stur            x2, [fp, #-0x20]
    // 0xb689f4: LoadField: r1 = r0->field_f
    //     0xb689f4: ldur            w1, [x0, #0xf]
    // 0xb689f8: DecompressPointer r1
    //     0xb689f8: add             x1, x1, HEAP, lsl #32
    // 0xb689fc: r0 = controller()
    //     0xb689fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb68a00: mov             x1, x0
    // 0xb68a04: r0 = nishabSuffix()
    //     0xb68a04: bl              #0xb68a58  ; [package:nuonline/app/modules/zakat/controllers/zakat_pertanian_controller.dart] ZakatPertanianController::nishabSuffix
    // 0xb68a08: stur            x0, [fp, #-8]
    // 0xb68a0c: r0 = NNishabCard()
    //     0xb68a0c: bl              #0xb63f74  ; AllocateNNishabCardStub -> NNishabCard (size=0x24)
    // 0xb68a10: ldur            x1, [fp, #-0x20]
    // 0xb68a14: StoreField: r0->field_b = r1
    //     0xb68a14: stur            w1, [x0, #0xb]
    // 0xb68a18: ldur            x1, [fp, #-0x18]
    // 0xb68a1c: StoreField: r0->field_f = r1
    //     0xb68a1c: stur            w1, [x0, #0xf]
    // 0xb68a20: ldur            x1, [fp, #-0x10]
    // 0xb68a24: ArrayStore: r0[0] = r1  ; List_4
    //     0xb68a24: stur            w1, [x0, #0x17]
    // 0xb68a28: r1 = "Nishab Zakat Pertanian"
    //     0xb68a28: add             x1, PP, #0x27, lsl #12  ; [pp+0x27800] "Nishab Zakat Pertanian"
    //     0xb68a2c: ldr             x1, [x1, #0x800]
    // 0xb68a30: StoreField: r0->field_13 = r1
    //     0xb68a30: stur            w1, [x0, #0x13]
    // 0xb68a34: r1 = true
    //     0xb68a34: add             x1, NULL, #0x20  ; true
    // 0xb68a38: StoreField: r0->field_1f = r1
    //     0xb68a38: stur            w1, [x0, #0x1f]
    // 0xb68a3c: ldur            x1, [fp, #-8]
    // 0xb68a40: StoreField: r0->field_1b = r1
    //     0xb68a40: stur            w1, [x0, #0x1b]
    // 0xb68a44: LeaveFrame
    //     0xb68a44: mov             SP, fp
    //     0xb68a48: ldp             fp, lr, [SP], #0x10
    // 0xb68a4c: ret
    //     0xb68a4c: ret             
    // 0xb68a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68a54: b               #0xb68990
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb68f50, size: 0x94
    // 0xb68f50: EnterFrame
    //     0xb68f50: stp             fp, lr, [SP, #-0x10]!
    //     0xb68f54: mov             fp, SP
    // 0xb68f58: AllocStack(0x18)
    //     0xb68f58: sub             SP, SP, #0x18
    // 0xb68f5c: CheckStackOverflow
    //     0xb68f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68f60: cmp             SP, x16
    //     0xb68f64: b.ls            #0xb68fdc
    // 0xb68f68: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb68f68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb68f6c: ldr             x0, [x0, #0x2670]
    //     0xb68f70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb68f74: cmp             w0, w16
    //     0xb68f78: b.ne            #0xb68f84
    //     0xb68f7c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb68f80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb68f84: r1 = Null
    //     0xb68f84: mov             x1, NULL
    // 0xb68f88: r2 = 4
    //     0xb68f88: movz            x2, #0x4
    // 0xb68f8c: r0 = AllocateArray()
    //     0xb68f8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb68f90: r16 = "id"
    //     0xb68f90: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb68f94: ldr             x16, [x16, #0x740]
    // 0xb68f98: StoreField: r0->field_f = r16
    //     0xb68f98: stur            w16, [x0, #0xf]
    // 0xb68f9c: r16 = 264154
    //     0xb68f9c: movz            x16, #0x7da
    //     0xb68fa0: movk            x16, #0x4, lsl #16
    // 0xb68fa4: StoreField: r0->field_13 = r16
    //     0xb68fa4: stur            w16, [x0, #0x13]
    // 0xb68fa8: r16 = <String, int>
    //     0xb68fa8: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb68fac: stp             x0, x16, [SP]
    // 0xb68fb0: r0 = Map._fromLiteral()
    //     0xb68fb0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb68fb4: r16 = "/article/article-detail"
    //     0xb68fb4: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xb68fb8: ldr             x16, [x16, #0x3d0]
    // 0xb68fbc: stp             x16, NULL, [SP, #8]
    // 0xb68fc0: str             x0, [SP]
    // 0xb68fc4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb68fc4: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb68fc8: ldr             x4, [x4, #0x478]
    // 0xb68fcc: r0 = GetNavigation.toNamed()
    //     0xb68fcc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb68fd0: LeaveFrame
    //     0xb68fd0: mov             SP, fp
    //     0xb68fd4: ldp             fp, lr, [SP], #0x10
    // 0xb68fd8: ret
    //     0xb68fd8: ret             
    // 0xb68fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68fdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68fe0: b               #0xb68f68
  }
}
