// lib: , url: package:nuonline/app/modules/ziarah/views/ziarah_destinasi_view.dart

// class id: 1050684, size: 0x8
class :: {
}

// class id: 4925, size: 0x18, field offset: 0xc
//   const constructor, 
class ZiarahDestinasiView extends StatelessWidget {

  _Mint field_10;
  bool field_14;

  _ build(/* No info */) {
    // ** addr: 0xbb5a04, size: 0x1c4
    // 0xbb5a04: EnterFrame
    //     0xbb5a04: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5a08: mov             fp, SP
    // 0xbb5a0c: AllocStack(0x40)
    //     0xbb5a0c: sub             SP, SP, #0x40
    // 0xbb5a10: r0 = 4
    //     0xbb5a10: movz            x0, #0x4
    // 0xbb5a14: mov             x3, x1
    // 0xbb5a18: stur            x1, [fp, #-0x18]
    // 0xbb5a1c: CheckStackOverflow
    //     0xbb5a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5a20: cmp             SP, x16
    //     0xbb5a24: b.ls            #0xbb5bc0
    // 0xbb5a28: LoadField: r4 = r3->field_b
    //     0xbb5a28: ldur            w4, [x3, #0xb]
    // 0xbb5a2c: DecompressPointer r4
    //     0xbb5a2c: add             x4, x4, HEAP, lsl #32
    // 0xbb5a30: stur            x4, [fp, #-0x10]
    // 0xbb5a34: LoadField: r5 = r3->field_f
    //     0xbb5a34: ldur            w5, [x3, #0xf]
    // 0xbb5a38: DecompressPointer r5
    //     0xbb5a38: add             x5, x5, HEAP, lsl #32
    // 0xbb5a3c: mov             x2, x0
    // 0xbb5a40: stur            x5, [fp, #-8]
    // 0xbb5a44: r1 = Null
    //     0xbb5a44: mov             x1, NULL
    // 0xbb5a48: r0 = AllocateArray()
    //     0xbb5a48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb5a4c: mov             x2, x0
    // 0xbb5a50: ldur            x0, [fp, #-0x10]
    // 0xbb5a54: stur            x2, [fp, #-0x20]
    // 0xbb5a58: StoreField: r2->field_f = r0
    //     0xbb5a58: stur            w0, [x2, #0xf]
    // 0xbb5a5c: ldur            x3, [fp, #-8]
    // 0xbb5a60: StoreField: r2->field_13 = r3
    //     0xbb5a60: stur            w3, [x2, #0x13]
    // 0xbb5a64: r1 = <int?>
    //     0xbb5a64: ldr             x1, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbb5a68: r0 = AllocateGrowableArray()
    //     0xbb5a68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb5a6c: mov             x1, x0
    // 0xbb5a70: ldur            x0, [fp, #-0x20]
    // 0xbb5a74: StoreField: r1->field_f = r0
    //     0xbb5a74: stur            w0, [x1, #0xf]
    // 0xbb5a78: r2 = 4
    //     0xbb5a78: movz            x2, #0x4
    // 0xbb5a7c: StoreField: r1->field_b = r2
    //     0xbb5a7c: stur            w2, [x1, #0xb]
    // 0xbb5a80: r16 = "-"
    //     0xbb5a80: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xbb5a84: str             x16, [SP]
    // 0xbb5a88: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbb5a88: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbb5a8c: r0 = join()
    //     0xbb5a8c: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xbb5a90: r1 = <String>
    //     0xbb5a90: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbb5a94: stur            x0, [fp, #-0x20]
    // 0xbb5a98: r0 = ValueKey()
    //     0xbb5a98: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xbb5a9c: mov             x3, x0
    // 0xbb5aa0: ldur            x0, [fp, #-0x20]
    // 0xbb5aa4: stur            x3, [fp, #-0x28]
    // 0xbb5aa8: StoreField: r3->field_b = r0
    //     0xbb5aa8: stur            w0, [x3, #0xb]
    // 0xbb5aac: r1 = Null
    //     0xbb5aac: mov             x1, NULL
    // 0xbb5ab0: r2 = 4
    //     0xbb5ab0: movz            x2, #0x4
    // 0xbb5ab4: r0 = AllocateArray()
    //     0xbb5ab4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb5ab8: mov             x2, x0
    // 0xbb5abc: ldur            x0, [fp, #-0x10]
    // 0xbb5ac0: stur            x2, [fp, #-0x20]
    // 0xbb5ac4: StoreField: r2->field_f = r0
    //     0xbb5ac4: stur            w0, [x2, #0xf]
    // 0xbb5ac8: ldur            x3, [fp, #-8]
    // 0xbb5acc: StoreField: r2->field_13 = r3
    //     0xbb5acc: stur            w3, [x2, #0x13]
    // 0xbb5ad0: r1 = <int?>
    //     0xbb5ad0: ldr             x1, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbb5ad4: r0 = AllocateGrowableArray()
    //     0xbb5ad4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb5ad8: mov             x1, x0
    // 0xbb5adc: ldur            x0, [fp, #-0x20]
    // 0xbb5ae0: StoreField: r1->field_f = r0
    //     0xbb5ae0: stur            w0, [x1, #0xf]
    // 0xbb5ae4: r0 = 4
    //     0xbb5ae4: movz            x0, #0x4
    // 0xbb5ae8: StoreField: r1->field_b = r0
    //     0xbb5ae8: stur            w0, [x1, #0xb]
    // 0xbb5aec: r16 = "-"
    //     0xbb5aec: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xbb5af0: str             x16, [SP]
    // 0xbb5af4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbb5af4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbb5af8: r0 = join()
    //     0xbb5af8: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xbb5afc: stur            x0, [fp, #-0x20]
    // 0xbb5b00: r0 = find()
    //     0xbb5b00: bl              #0x851efc  ; [package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart] ZiarahRemoteRepository::find
    // 0xbb5b04: mov             x1, x0
    // 0xbb5b08: ldur            x0, [fp, #-0x18]
    // 0xbb5b0c: stur            x1, [fp, #-0x38]
    // 0xbb5b10: LoadField: r2 = r0->field_13
    //     0xbb5b10: ldur            w2, [x0, #0x13]
    // 0xbb5b14: DecompressPointer r2
    //     0xbb5b14: add             x2, x2, HEAP, lsl #32
    // 0xbb5b18: stur            x2, [fp, #-0x30]
    // 0xbb5b1c: r0 = ZiarahDestinasiController()
    //     0xbb5b1c: bl              #0xbb5d10  ; AllocateZiarahDestinasiControllerStub -> ZiarahDestinasiController (size=0x50)
    // 0xbb5b20: mov             x2, x0
    // 0xbb5b24: ldur            x0, [fp, #-0x30]
    // 0xbb5b28: stur            x2, [fp, #-0x18]
    // 0xbb5b2c: StoreField: r2->field_4b = r0
    //     0xbb5b2c: stur            w0, [x2, #0x4b]
    // 0xbb5b30: r0 = 3
    //     0xbb5b30: movz            x0, #0x3
    // 0xbb5b34: StoreField: r2->field_37 = r0
    //     0xbb5b34: stur            x0, [x2, #0x37]
    // 0xbb5b38: ldur            x0, [fp, #-0x38]
    // 0xbb5b3c: StoreField: r2->field_47 = r0
    //     0xbb5b3c: stur            w0, [x2, #0x47]
    // 0xbb5b40: ldur            x0, [fp, #-0x10]
    // 0xbb5b44: StoreField: r2->field_3f = r0
    //     0xbb5b44: stur            w0, [x2, #0x3f]
    // 0xbb5b48: ldur            x0, [fp, #-8]
    // 0xbb5b4c: StoreField: r2->field_43 = r0
    //     0xbb5b4c: stur            w0, [x2, #0x43]
    // 0xbb5b50: mov             x1, x2
    // 0xbb5b54: r0 = __ZiarahListController&GetxController&PagingMixin()
    //     0xbb5b54: bl              #0xbb5bc8  ; [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::__ZiarahListController&GetxController&PagingMixin
    // 0xbb5b58: r1 = <ZiarahDestinasiController>
    //     0xbb5b58: add             x1, PP, #0x34, lsl #12  ; [pp+0x34470] TypeArguments: <ZiarahDestinasiController>
    //     0xbb5b5c: ldr             x1, [x1, #0x470]
    // 0xbb5b60: r0 = GetBuilder()
    //     0xbb5b60: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xbb5b64: mov             x3, x0
    // 0xbb5b68: ldur            x0, [fp, #-0x18]
    // 0xbb5b6c: stur            x3, [fp, #-8]
    // 0xbb5b70: StoreField: r3->field_3b = r0
    //     0xbb5b70: stur            w0, [x3, #0x3b]
    // 0xbb5b74: r0 = true
    //     0xbb5b74: add             x0, NULL, #0x20  ; true
    // 0xbb5b78: StoreField: r3->field_13 = r0
    //     0xbb5b78: stur            w0, [x3, #0x13]
    // 0xbb5b7c: r1 = Function '<anonymous closure>':.
    //     0xbb5b7c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34478] AnonymousClosure: (0xbb5d1c), in [package:nuonline/app/modules/ziarah/views/ziarah_destinasi_view.dart] ZiarahDestinasiView::build (0xbb5a04)
    //     0xbb5b80: ldr             x1, [x1, #0x478]
    // 0xbb5b84: r2 = Null
    //     0xbb5b84: mov             x2, NULL
    // 0xbb5b88: r0 = AllocateClosure()
    //     0xbb5b88: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5b8c: mov             x1, x0
    // 0xbb5b90: ldur            x0, [fp, #-8]
    // 0xbb5b94: StoreField: r0->field_f = r1
    //     0xbb5b94: stur            w1, [x0, #0xf]
    // 0xbb5b98: r1 = false
    //     0xbb5b98: add             x1, NULL, #0x30  ; false
    // 0xbb5b9c: StoreField: r0->field_1f = r1
    //     0xbb5b9c: stur            w1, [x0, #0x1f]
    // 0xbb5ba0: StoreField: r0->field_23 = r1
    //     0xbb5ba0: stur            w1, [x0, #0x23]
    // 0xbb5ba4: ldur            x1, [fp, #-0x20]
    // 0xbb5ba8: StoreField: r0->field_1b = r1
    //     0xbb5ba8: stur            w1, [x0, #0x1b]
    // 0xbb5bac: ldur            x1, [fp, #-0x28]
    // 0xbb5bb0: StoreField: r0->field_7 = r1
    //     0xbb5bb0: stur            w1, [x0, #7]
    // 0xbb5bb4: LeaveFrame
    //     0xbb5bb4: mov             SP, fp
    //     0xbb5bb8: ldp             fp, lr, [SP], #0x10
    // 0xbb5bbc: ret
    //     0xbb5bbc: ret             
    // 0xbb5bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5bc4: b               #0xbb5a28
  }
  [closure] RefreshIndicator <anonymous closure>(dynamic, ZiarahDestinasiController) {
    // ** addr: 0xbb5d1c, size: 0x148
    // 0xbb5d1c: EnterFrame
    //     0xbb5d1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5d20: mov             fp, SP
    // 0xbb5d24: AllocStack(0x18)
    //     0xbb5d24: sub             SP, SP, #0x18
    // 0xbb5d28: SetupParameters()
    //     0xbb5d28: ldr             x0, [fp, #0x18]
    //     0xbb5d2c: ldur            w1, [x0, #0x17]
    //     0xbb5d30: add             x1, x1, HEAP, lsl #32
    //     0xbb5d34: stur            x1, [fp, #-8]
    // 0xbb5d38: r1 = 1
    //     0xbb5d38: movz            x1, #0x1
    // 0xbb5d3c: r0 = AllocateContext()
    //     0xbb5d3c: bl              #0xec126c  ; AllocateContextStub
    // 0xbb5d40: mov             x1, x0
    // 0xbb5d44: ldur            x0, [fp, #-8]
    // 0xbb5d48: stur            x1, [fp, #-0x10]
    // 0xbb5d4c: StoreField: r1->field_b = r0
    //     0xbb5d4c: stur            w0, [x1, #0xb]
    // 0xbb5d50: ldr             x2, [fp, #0x10]
    // 0xbb5d54: StoreField: r1->field_f = r2
    //     0xbb5d54: stur            w2, [x1, #0xf]
    // 0xbb5d58: r0 = Obx()
    //     0xbb5d58: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbb5d5c: ldur            x2, [fp, #-0x10]
    // 0xbb5d60: r1 = Function '<anonymous closure>':.
    //     0xbb5d60: add             x1, PP, #0x34, lsl #12  ; [pp+0x34480] AnonymousClosure: (0xbb5e64), in [package:nuonline/app/modules/ziarah/views/ziarah_destinasi_view.dart] ZiarahDestinasiView::build (0xbb5a04)
    //     0xbb5d64: ldr             x1, [x1, #0x480]
    // 0xbb5d68: stur            x0, [fp, #-8]
    // 0xbb5d6c: r0 = AllocateClosure()
    //     0xbb5d6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5d70: mov             x1, x0
    // 0xbb5d74: ldur            x0, [fp, #-8]
    // 0xbb5d78: StoreField: r0->field_b = r1
    //     0xbb5d78: stur            w1, [x0, #0xb]
    // 0xbb5d7c: ldr             x2, [fp, #0x10]
    // 0xbb5d80: r1 = Function 'onPageScrolled':.
    //     0xbb5d80: add             x1, PP, #0x34, lsl #12  ; [pp+0x343d0] AnonymousClosure: (0xbb64a0), in [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::onPageScrolled (0xbb64dc)
    //     0xbb5d84: ldr             x1, [x1, #0x3d0]
    // 0xbb5d88: r0 = AllocateClosure()
    //     0xbb5d88: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5d8c: r1 = <ScrollNotification>
    //     0xbb5d8c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbb5d90: ldr             x1, [x1, #0x110]
    // 0xbb5d94: stur            x0, [fp, #-0x10]
    // 0xbb5d98: r0 = NotificationListener()
    //     0xbb5d98: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbb5d9c: mov             x1, x0
    // 0xbb5da0: ldur            x0, [fp, #-0x10]
    // 0xbb5da4: stur            x1, [fp, #-0x18]
    // 0xbb5da8: StoreField: r1->field_13 = r0
    //     0xbb5da8: stur            w0, [x1, #0x13]
    // 0xbb5dac: ldur            x0, [fp, #-8]
    // 0xbb5db0: StoreField: r1->field_b = r0
    //     0xbb5db0: stur            w0, [x1, #0xb]
    // 0xbb5db4: r0 = ListTileTheme()
    //     0xbb5db4: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xbb5db8: mov             x1, x0
    // 0xbb5dbc: r0 = Instance_EdgeInsets
    //     0xbb5dbc: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbb5dc0: stur            x1, [fp, #-8]
    // 0xbb5dc4: StoreField: r1->field_2b = r0
    //     0xbb5dc4: stur            w0, [x1, #0x2b]
    // 0xbb5dc8: r0 = 16.000000
    //     0xbb5dc8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbb5dcc: ldr             x0, [x0, #0x80]
    // 0xbb5dd0: StoreField: r1->field_37 = r0
    //     0xbb5dd0: stur            w0, [x1, #0x37]
    // 0xbb5dd4: r0 = 24.000000
    //     0xbb5dd4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xbb5dd8: ldr             x0, [x0, #0x368]
    // 0xbb5ddc: StoreField: r1->field_3f = r0
    //     0xbb5ddc: stur            w0, [x1, #0x3f]
    // 0xbb5de0: ldur            x0, [fp, #-0x18]
    // 0xbb5de4: StoreField: r1->field_b = r0
    //     0xbb5de4: stur            w0, [x1, #0xb]
    // 0xbb5de8: r0 = RefreshIndicator()
    //     0xbb5de8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbb5dec: mov             x3, x0
    // 0xbb5df0: ldur            x0, [fp, #-8]
    // 0xbb5df4: stur            x3, [fp, #-0x10]
    // 0xbb5df8: StoreField: r3->field_b = r0
    //     0xbb5df8: stur            w0, [x3, #0xb]
    // 0xbb5dfc: d0 = 40.000000
    //     0xbb5dfc: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbb5e00: StoreField: r3->field_f = d0
    //     0xbb5e00: stur            d0, [x3, #0xf]
    // 0xbb5e04: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbb5e04: stur            xzr, [x3, #0x17]
    // 0xbb5e08: ldr             x2, [fp, #0x10]
    // 0xbb5e0c: r1 = Function 'onPageRefresh':.
    //     0xbb5e0c: add             x1, PP, #0x34, lsl #12  ; [pp+0x343d8] AnonymousClosure: (0xbb63b4), in [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::onPageRefresh (0xbb63ec)
    //     0xbb5e10: ldr             x1, [x1, #0x3d8]
    // 0xbb5e14: r0 = AllocateClosure()
    //     0xbb5e14: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5e18: mov             x1, x0
    // 0xbb5e1c: ldur            x0, [fp, #-0x10]
    // 0xbb5e20: StoreField: r0->field_1f = r1
    //     0xbb5e20: stur            w1, [x0, #0x1f]
    // 0xbb5e24: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbb5e24: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbb5e28: ldr             x1, [x1, #0xf58]
    // 0xbb5e2c: StoreField: r0->field_2f = r1
    //     0xbb5e2c: stur            w1, [x0, #0x2f]
    // 0xbb5e30: d0 = 2.500000
    //     0xbb5e30: fmov            d0, #2.50000000
    // 0xbb5e34: StoreField: r0->field_3b = d0
    //     0xbb5e34: stur            d0, [x0, #0x3b]
    // 0xbb5e38: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbb5e38: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbb5e3c: ldr             x1, [x1, #0xa68]
    // 0xbb5e40: StoreField: r0->field_47 = r1
    //     0xbb5e40: stur            w1, [x0, #0x47]
    // 0xbb5e44: d0 = 2.000000
    //     0xbb5e44: fmov            d0, #2.00000000
    // 0xbb5e48: StoreField: r0->field_4b = d0
    //     0xbb5e48: stur            d0, [x0, #0x4b]
    // 0xbb5e4c: r1 = Instance__IndicatorType
    //     0xbb5e4c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbb5e50: ldr             x1, [x1, #0xa70]
    // 0xbb5e54: StoreField: r0->field_43 = r1
    //     0xbb5e54: stur            w1, [x0, #0x43]
    // 0xbb5e58: LeaveFrame
    //     0xbb5e58: mov             SP, fp
    //     0xbb5e5c: ldp             fp, lr, [SP], #0x10
    // 0xbb5e60: ret
    //     0xbb5e60: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xbb5e64, size: 0x1f0
    // 0xbb5e64: EnterFrame
    //     0xbb5e64: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5e68: mov             fp, SP
    // 0xbb5e6c: AllocStack(0x38)
    //     0xbb5e6c: sub             SP, SP, #0x38
    // 0xbb5e70: SetupParameters()
    //     0xbb5e70: ldr             x0, [fp, #0x10]
    //     0xbb5e74: ldur            w2, [x0, #0x17]
    //     0xbb5e78: add             x2, x2, HEAP, lsl #32
    //     0xbb5e7c: stur            x2, [fp, #-8]
    // 0xbb5e80: CheckStackOverflow
    //     0xbb5e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5e84: cmp             SP, x16
    //     0xbb5e88: b.ls            #0xbb604c
    // 0xbb5e8c: LoadField: r0 = r2->field_f
    //     0xbb5e8c: ldur            w0, [x2, #0xf]
    // 0xbb5e90: DecompressPointer r0
    //     0xbb5e90: add             x0, x0, HEAP, lsl #32
    // 0xbb5e94: LoadField: r1 = r0->field_33
    //     0xbb5e94: ldur            w1, [x0, #0x33]
    // 0xbb5e98: DecompressPointer r1
    //     0xbb5e98: add             x1, x1, HEAP, lsl #32
    // 0xbb5e9c: r0 = value()
    //     0xbb5e9c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb5ea0: tbnz            w0, #4, #0xbb5fcc
    // 0xbb5ea4: r0 = NEmptyState()
    //     0xbb5ea4: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xbb5ea8: mov             x1, x0
    // 0xbb5eac: r2 = "Silakan berkontribusi untuk mengirimkan data Ziarah di aplikasi NU Online, baik informasi tokoh, masjid bersejarah, kompleks makam, musem, ataupun petilasan."
    //     0xbb5eac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ecf0] "Silakan berkontribusi untuk mengirimkan data Ziarah di aplikasi NU Online, baik informasi tokoh, masjid bersejarah, kompleks makam, musem, ataupun petilasan."
    //     0xbb5eb0: ldr             x2, [x2, #0xcf0]
    // 0xbb5eb4: r3 = "assets/images/illustration/no_location.svg"
    //     0xbb5eb4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d018] "assets/images/illustration/no_location.svg"
    //     0xbb5eb8: ldr             x3, [x3, #0x18]
    // 0xbb5ebc: r5 = "Hasil Tidak Ditemukan"
    //     0xbb5ebc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ecf8] "Hasil Tidak Ditemukan"
    //     0xbb5ec0: ldr             x5, [x5, #0xcf8]
    // 0xbb5ec4: stur            x0, [fp, #-0x10]
    // 0xbb5ec8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbb5ec8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbb5ecc: r0 = NEmptyState.svg()
    //     0xbb5ecc: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xbb5ed0: r1 = Function '<anonymous closure>':.
    //     0xbb5ed0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34488] AnonymousClosure: (0xb6ef50), in [package:nuonline/app/modules/ziarah/views/ziarah_view.dart] ZiarahView::build (0xb6f104)
    //     0xbb5ed4: ldr             x1, [x1, #0x488]
    // 0xbb5ed8: r2 = Null
    //     0xbb5ed8: mov             x2, NULL
    // 0xbb5edc: r0 = AllocateClosure()
    //     0xbb5edc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5ee0: stur            x0, [fp, #-0x18]
    // 0xbb5ee4: r0 = OutlinedButton()
    //     0xbb5ee4: bl              #0xa3b670  ; AllocateOutlinedButtonStub -> OutlinedButton (size=0x3c)
    // 0xbb5ee8: mov             x3, x0
    // 0xbb5eec: ldur            x0, [fp, #-0x18]
    // 0xbb5ef0: stur            x3, [fp, #-0x20]
    // 0xbb5ef4: StoreField: r3->field_b = r0
    //     0xbb5ef4: stur            w0, [x3, #0xb]
    // 0xbb5ef8: r0 = false
    //     0xbb5ef8: add             x0, NULL, #0x30  ; false
    // 0xbb5efc: StoreField: r3->field_27 = r0
    //     0xbb5efc: stur            w0, [x3, #0x27]
    // 0xbb5f00: r0 = true
    //     0xbb5f00: add             x0, NULL, #0x20  ; true
    // 0xbb5f04: StoreField: r3->field_2f = r0
    //     0xbb5f04: stur            w0, [x3, #0x2f]
    // 0xbb5f08: r0 = Instance_Text
    //     0xbb5f08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ecd0] Obj!Text@e23801
    //     0xbb5f0c: ldr             x0, [x0, #0xcd0]
    // 0xbb5f10: StoreField: r3->field_37 = r0
    //     0xbb5f10: stur            w0, [x3, #0x37]
    // 0xbb5f14: r1 = Null
    //     0xbb5f14: mov             x1, NULL
    // 0xbb5f18: r2 = 6
    //     0xbb5f18: movz            x2, #0x6
    // 0xbb5f1c: r0 = AllocateArray()
    //     0xbb5f1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb5f20: mov             x2, x0
    // 0xbb5f24: ldur            x0, [fp, #-0x10]
    // 0xbb5f28: stur            x2, [fp, #-0x18]
    // 0xbb5f2c: StoreField: r2->field_f = r0
    //     0xbb5f2c: stur            w0, [x2, #0xf]
    // 0xbb5f30: r16 = Instance_SizedBox
    //     0xbb5f30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ecd8] Obj!SizedBox@e1e361
    //     0xbb5f34: ldr             x16, [x16, #0xcd8]
    // 0xbb5f38: StoreField: r2->field_13 = r16
    //     0xbb5f38: stur            w16, [x2, #0x13]
    // 0xbb5f3c: ldur            x0, [fp, #-0x20]
    // 0xbb5f40: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb5f40: stur            w0, [x2, #0x17]
    // 0xbb5f44: r1 = <Widget>
    //     0xbb5f44: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbb5f48: r0 = AllocateGrowableArray()
    //     0xbb5f48: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb5f4c: mov             x1, x0
    // 0xbb5f50: ldur            x0, [fp, #-0x18]
    // 0xbb5f54: stur            x1, [fp, #-0x10]
    // 0xbb5f58: StoreField: r1->field_f = r0
    //     0xbb5f58: stur            w0, [x1, #0xf]
    // 0xbb5f5c: r0 = 6
    //     0xbb5f5c: movz            x0, #0x6
    // 0xbb5f60: StoreField: r1->field_b = r0
    //     0xbb5f60: stur            w0, [x1, #0xb]
    // 0xbb5f64: r0 = Column()
    //     0xbb5f64: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb5f68: mov             x1, x0
    // 0xbb5f6c: r0 = Instance_Axis
    //     0xbb5f6c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbb5f70: StoreField: r1->field_f = r0
    //     0xbb5f70: stur            w0, [x1, #0xf]
    // 0xbb5f74: r0 = Instance_MainAxisAlignment
    //     0xbb5f74: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xbb5f78: ldr             x0, [x0, #0x290]
    // 0xbb5f7c: StoreField: r1->field_13 = r0
    //     0xbb5f7c: stur            w0, [x1, #0x13]
    // 0xbb5f80: r0 = Instance_MainAxisSize
    //     0xbb5f80: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbb5f84: ldr             x0, [x0, #0x738]
    // 0xbb5f88: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb5f88: stur            w0, [x1, #0x17]
    // 0xbb5f8c: r0 = Instance_CrossAxisAlignment
    //     0xbb5f8c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbb5f90: ldr             x0, [x0, #0x740]
    // 0xbb5f94: StoreField: r1->field_1b = r0
    //     0xbb5f94: stur            w0, [x1, #0x1b]
    // 0xbb5f98: r0 = Instance_VerticalDirection
    //     0xbb5f98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbb5f9c: ldr             x0, [x0, #0x748]
    // 0xbb5fa0: StoreField: r1->field_23 = r0
    //     0xbb5fa0: stur            w0, [x1, #0x23]
    // 0xbb5fa4: r0 = Instance_Clip
    //     0xbb5fa4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbb5fa8: ldr             x0, [x0, #0x750]
    // 0xbb5fac: StoreField: r1->field_2b = r0
    //     0xbb5fac: stur            w0, [x1, #0x2b]
    // 0xbb5fb0: StoreField: r1->field_2f = rZR
    //     0xbb5fb0: stur            xzr, [x1, #0x2f]
    // 0xbb5fb4: ldur            x0, [fp, #-0x10]
    // 0xbb5fb8: StoreField: r1->field_b = r0
    //     0xbb5fb8: stur            w0, [x1, #0xb]
    // 0xbb5fbc: mov             x0, x1
    // 0xbb5fc0: LeaveFrame
    //     0xbb5fc0: mov             SP, fp
    //     0xbb5fc4: ldp             fp, lr, [SP], #0x10
    // 0xbb5fc8: ret
    //     0xbb5fc8: ret             
    // 0xbb5fcc: ldur            x2, [fp, #-8]
    // 0xbb5fd0: LoadField: r1 = r2->field_f
    //     0xbb5fd0: ldur            w1, [x2, #0xf]
    // 0xbb5fd4: DecompressPointer r1
    //     0xbb5fd4: add             x1, x1, HEAP, lsl #32
    // 0xbb5fd8: r0 = itemsCount()
    //     0xbb5fd8: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbb5fdc: r1 = Function '<anonymous closure>':.
    //     0xbb5fdc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34490] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbb5fe0: ldr             x1, [x1, #0x490]
    // 0xbb5fe4: r2 = Null
    //     0xbb5fe4: mov             x2, NULL
    // 0xbb5fe8: stur            x0, [fp, #-0x28]
    // 0xbb5fec: r0 = AllocateClosure()
    //     0xbb5fec: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb5ff0: ldur            x2, [fp, #-8]
    // 0xbb5ff4: r1 = Function '<anonymous closure>':.
    //     0xbb5ff4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34498] AnonymousClosure: (0xbb6054), in [package:nuonline/app/modules/ziarah/views/ziarah_destinasi_view.dart] ZiarahDestinasiView::build (0xbb5a04)
    //     0xbb5ff8: ldr             x1, [x1, #0x498]
    // 0xbb5ffc: stur            x0, [fp, #-8]
    // 0xbb6000: r0 = AllocateClosure()
    //     0xbb6000: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6004: stur            x0, [fp, #-0x10]
    // 0xbb6008: r0 = ListView()
    //     0xbb6008: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbb600c: stur            x0, [fp, #-0x18]
    // 0xbb6010: r16 = true
    //     0xbb6010: add             x16, NULL, #0x20  ; true
    // 0xbb6014: r30 = Instance_EdgeInsets
    //     0xbb6014: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xbb6018: ldr             lr, [lr, #0x360]
    // 0xbb601c: stp             lr, x16, [SP]
    // 0xbb6020: mov             x1, x0
    // 0xbb6024: ldur            x2, [fp, #-0x10]
    // 0xbb6028: ldur            x3, [fp, #-0x28]
    // 0xbb602c: ldur            x5, [fp, #-8]
    // 0xbb6030: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xbb6030: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbb6034: ldr             x4, [x4, #0x100]
    // 0xbb6038: r0 = ListView.separated()
    //     0xbb6038: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbb603c: ldur            x0, [fp, #-0x18]
    // 0xbb6040: LeaveFrame
    //     0xbb6040: mov             SP, fp
    //     0xbb6044: ldp             fp, lr, [SP], #0x10
    // 0xbb6048: ret
    //     0xbb6048: ret             
    // 0xbb604c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb604c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6050: b               #0xbb5e8c
  }
  [closure] NZiarahListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbb6054, size: 0x240
    // 0xbb6054: EnterFrame
    //     0xbb6054: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6058: mov             fp, SP
    // 0xbb605c: AllocStack(0x40)
    //     0xbb605c: sub             SP, SP, #0x40
    // 0xbb6060: SetupParameters()
    //     0xbb6060: ldr             x0, [fp, #0x20]
    //     0xbb6064: ldur            w1, [x0, #0x17]
    //     0xbb6068: add             x1, x1, HEAP, lsl #32
    //     0xbb606c: stur            x1, [fp, #-8]
    // 0xbb6070: CheckStackOverflow
    //     0xbb6070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6074: cmp             SP, x16
    //     0xbb6078: b.ls            #0xbb628c
    // 0xbb607c: r1 = 1
    //     0xbb607c: movz            x1, #0x1
    // 0xbb6080: r0 = AllocateContext()
    //     0xbb6080: bl              #0xec126c  ; AllocateContextStub
    // 0xbb6084: mov             x3, x0
    // 0xbb6088: ldur            x0, [fp, #-8]
    // 0xbb608c: stur            x3, [fp, #-0x10]
    // 0xbb6090: StoreField: r3->field_b = r0
    //     0xbb6090: stur            w0, [x3, #0xb]
    // 0xbb6094: LoadField: r1 = r0->field_f
    //     0xbb6094: ldur            w1, [x0, #0xf]
    // 0xbb6098: DecompressPointer r1
    //     0xbb6098: add             x1, x1, HEAP, lsl #32
    // 0xbb609c: ldr             x2, [fp, #0x10]
    // 0xbb60a0: r4 = LoadInt32Instr(r2)
    //     0xbb60a0: sbfx            x4, x2, #1, #0x1f
    //     0xbb60a4: tbz             w2, #0, #0xbb60ac
    //     0xbb60a8: ldur            x4, [x2, #7]
    // 0xbb60ac: mov             x2, x4
    // 0xbb60b0: r0 = find()
    //     0xbb60b0: bl              #0xbb6294  ; [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::find
    // 0xbb60b4: mov             x3, x0
    // 0xbb60b8: ldur            x2, [fp, #-0x10]
    // 0xbb60bc: stur            x3, [fp, #-0x28]
    // 0xbb60c0: StoreField: r2->field_f = r0
    //     0xbb60c0: stur            w0, [x2, #0xf]
    //     0xbb60c4: ldurb           w16, [x2, #-1]
    //     0xbb60c8: ldurb           w17, [x0, #-1]
    //     0xbb60cc: and             x16, x17, x16, lsr #2
    //     0xbb60d0: tst             x16, HEAP, lsr #32
    //     0xbb60d4: b.eq            #0xbb60dc
    //     0xbb60d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb60dc: cmp             w3, NULL
    // 0xbb60e0: b.ne            #0xbb60f8
    // 0xbb60e4: r0 = Instance_NZiarahListTile
    //     0xbb60e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ed20] Obj!NZiarahListTile@e20df1
    //     0xbb60e8: ldr             x0, [x0, #0xd20]
    // 0xbb60ec: LeaveFrame
    //     0xbb60ec: mov             SP, fp
    //     0xbb60f0: ldp             fp, lr, [SP], #0x10
    // 0xbb60f4: ret
    //     0xbb60f4: ret             
    // 0xbb60f8: ldur            x0, [fp, #-8]
    // 0xbb60fc: LoadField: r1 = r3->field_13
    //     0xbb60fc: ldur            w1, [x3, #0x13]
    // 0xbb6100: DecompressPointer r1
    //     0xbb6100: add             x1, x1, HEAP, lsl #32
    // 0xbb6104: LoadField: r4 = r1->field_7
    //     0xbb6104: ldur            w4, [x1, #7]
    // 0xbb6108: DecompressPointer r4
    //     0xbb6108: add             x4, x4, HEAP, lsl #32
    // 0xbb610c: stur            x4, [fp, #-0x20]
    // 0xbb6110: LoadField: r5 = r3->field_f
    //     0xbb6110: ldur            w5, [x3, #0xf]
    // 0xbb6114: DecompressPointer r5
    //     0xbb6114: add             x5, x5, HEAP, lsl #32
    // 0xbb6118: stur            x5, [fp, #-0x18]
    // 0xbb611c: LoadField: r1 = r0->field_f
    //     0xbb611c: ldur            w1, [x0, #0xf]
    // 0xbb6120: DecompressPointer r1
    //     0xbb6120: add             x1, x1, HEAP, lsl #32
    // 0xbb6124: LoadField: r0 = r1->field_4b
    //     0xbb6124: ldur            w0, [x1, #0x4b]
    // 0xbb6128: DecompressPointer r0
    //     0xbb6128: add             x0, x0, HEAP, lsl #32
    // 0xbb612c: stur            x0, [fp, #-8]
    // 0xbb6130: LoadField: r1 = r3->field_23
    //     0xbb6130: ldur            w1, [x3, #0x23]
    // 0xbb6134: DecompressPointer r1
    //     0xbb6134: add             x1, x1, HEAP, lsl #32
    // 0xbb6138: r0 = full()
    //     0xbb6138: bl              #0xb6ce00  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::full
    // 0xbb613c: mov             x1, x0
    // 0xbb6140: ldur            x0, [fp, #-8]
    // 0xbb6144: tbnz            w0, #4, #0xbb6150
    // 0xbb6148: r2 = ""
    //     0xbb6148: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbb614c: b               #0xbb6154
    // 0xbb6150: mov             x2, x1
    // 0xbb6154: ldur            x0, [fp, #-0x28]
    // 0xbb6158: mov             x1, x0
    // 0xbb615c: stur            x2, [fp, #-8]
    // 0xbb6160: r0 = distanceKM()
    //     0xbb6160: bl              #0xb6ec4c  ; [package:nuonline/app/data/models/ziarah.dart] Ziarah::distanceKM
    // 0xbb6164: mov             x2, x0
    // 0xbb6168: ldur            x0, [fp, #-0x28]
    // 0xbb616c: stur            x2, [fp, #-0x38]
    // 0xbb6170: LoadField: r1 = r0->field_1f
    //     0xbb6170: ldur            w1, [x0, #0x1f]
    // 0xbb6174: DecompressPointer r1
    //     0xbb6174: add             x1, x1, HEAP, lsl #32
    // 0xbb6178: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbb6178: ldur            w3, [x1, #0x17]
    // 0xbb617c: DecompressPointer r3
    //     0xbb617c: add             x3, x3, HEAP, lsl #32
    // 0xbb6180: stur            x3, [fp, #-0x30]
    // 0xbb6184: LoadField: r4 = r1->field_7
    //     0xbb6184: ldur            x4, [x1, #7]
    // 0xbb6188: cmp             x4, #5
    // 0xbb618c: b.gt            #0xbb61cc
    // 0xbb6190: cmp             x4, #4
    // 0xbb6194: b.gt            #0xbb61c0
    // 0xbb6198: r0 = BoxInt64Instr(r4)
    //     0xbb6198: sbfiz           x0, x4, #1, #0x1f
    //     0xbb619c: cmp             x4, x0, asr #1
    //     0xbb61a0: b.eq            #0xbb61ac
    //     0xbb61a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb61a8: stur            x4, [x0, #7]
    // 0xbb61ac: cmp             w0, #8
    // 0xbb61b0: b.ne            #0xbb6208
    // 0xbb61b4: r5 = Instance_IconData
    //     0xbb61b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed28] Obj!IconData@e10811
    //     0xbb61b8: ldr             x5, [x5, #0xd28]
    // 0xbb61bc: b               #0xbb6210
    // 0xbb61c0: r5 = Instance_IconData
    //     0xbb61c0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed30] Obj!IconData@e107f1
    //     0xbb61c4: ldr             x5, [x5, #0xd30]
    // 0xbb61c8: b               #0xbb6210
    // 0xbb61cc: cmp             x4, #6
    // 0xbb61d0: b.gt            #0xbb61e0
    // 0xbb61d4: r5 = Instance_IconData
    //     0xbb61d4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed38] Obj!IconData@e107d1
    //     0xbb61d8: ldr             x5, [x5, #0xd38]
    // 0xbb61dc: b               #0xbb6210
    // 0xbb61e0: r0 = BoxInt64Instr(r4)
    //     0xbb61e0: sbfiz           x0, x4, #1, #0x1f
    //     0xbb61e4: cmp             x4, x0, asr #1
    //     0xbb61e8: b.eq            #0xbb61f4
    //     0xbb61ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb61f0: stur            x4, [x0, #7]
    // 0xbb61f4: cmp             w0, #0xe
    // 0xbb61f8: b.ne            #0xbb6208
    // 0xbb61fc: r5 = Instance_IconData
    //     0xbb61fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed40] Obj!IconData@e107b1
    //     0xbb6200: ldr             x5, [x5, #0xd40]
    // 0xbb6204: b               #0xbb6210
    // 0xbb6208: r5 = Instance_IconData
    //     0xbb6208: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed48] Obj!IconData@e10791
    //     0xbb620c: ldr             x5, [x5, #0xd48]
    // 0xbb6210: ldur            x1, [fp, #-0x20]
    // 0xbb6214: ldur            x4, [fp, #-0x18]
    // 0xbb6218: ldur            x0, [fp, #-8]
    // 0xbb621c: stur            x5, [fp, #-0x28]
    // 0xbb6220: r0 = NZiarahListTile()
    //     0xbb6220: bl              #0xb6b414  ; AllocateNZiarahListTileStub -> NZiarahListTile (size=0x2c)
    // 0xbb6224: mov             x3, x0
    // 0xbb6228: ldur            x0, [fp, #-0x20]
    // 0xbb622c: stur            x3, [fp, #-0x40]
    // 0xbb6230: StoreField: r3->field_b = r0
    //     0xbb6230: stur            w0, [x3, #0xb]
    // 0xbb6234: ldur            x2, [fp, #-0x10]
    // 0xbb6238: r1 = Function '<anonymous closure>':.
    //     0xbb6238: add             x1, PP, #0x34, lsl #12  ; [pp+0x344a0] AnonymousClosure: (0xb6ee38), in [package:nuonline/app/modules/ziarah/views/ziarah_search_view.dart] ZiarahSearchView::build (0xb6e354)
    //     0xbb623c: ldr             x1, [x1, #0x4a0]
    // 0xbb6240: r0 = AllocateClosure()
    //     0xbb6240: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6244: mov             x1, x0
    // 0xbb6248: ldur            x0, [fp, #-0x40]
    // 0xbb624c: StoreField: r0->field_f = r1
    //     0xbb624c: stur            w1, [x0, #0xf]
    // 0xbb6250: ldur            x1, [fp, #-0x18]
    // 0xbb6254: StoreField: r0->field_13 = r1
    //     0xbb6254: stur            w1, [x0, #0x13]
    // 0xbb6258: ldur            x1, [fp, #-8]
    // 0xbb625c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb625c: stur            w1, [x0, #0x17]
    // 0xbb6260: ldur            x1, [fp, #-0x38]
    // 0xbb6264: StoreField: r0->field_1b = r1
    //     0xbb6264: stur            w1, [x0, #0x1b]
    // 0xbb6268: ldur            x1, [fp, #-0x30]
    // 0xbb626c: StoreField: r0->field_1f = r1
    //     0xbb626c: stur            w1, [x0, #0x1f]
    // 0xbb6270: r1 = false
    //     0xbb6270: add             x1, NULL, #0x30  ; false
    // 0xbb6274: StoreField: r0->field_23 = r1
    //     0xbb6274: stur            w1, [x0, #0x23]
    // 0xbb6278: ldur            x1, [fp, #-0x28]
    // 0xbb627c: StoreField: r0->field_27 = r1
    //     0xbb627c: stur            w1, [x0, #0x27]
    // 0xbb6280: LeaveFrame
    //     0xbb6280: mov             SP, fp
    //     0xbb6284: ldp             fp, lr, [SP], #0x10
    // 0xbb6288: ret
    //     0xbb6288: ret             
    // 0xbb628c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb628c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6290: b               #0xbb607c
  }
}
