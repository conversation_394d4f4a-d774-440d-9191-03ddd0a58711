// lib: , url: package:nuonline/app/modules/ziarah/views/ziarah_tokoh_view.dart

// class id: 1050690, size: 0x8
class :: {
}

// class id: 4923, size: 0x1c, field offset: 0xc
//   const constructor, 
class ZiarahTokohView extends StatelessWidget {

  _Mint field_14;
  bool field_18;

  _ build(/* No info */) {
    // ** addr: 0xbb6758, size: 0x1b8
    // 0xbb6758: EnterFrame
    //     0xbb6758: stp             fp, lr, [SP, #-0x10]!
    //     0xbb675c: mov             fp, SP
    // 0xbb6760: AllocStack(0x40)
    //     0xbb6760: sub             SP, SP, #0x40
    // 0xbb6764: SetupParameters(ZiarahTokohView this /* r1 => r1, fp-0x8 */)
    //     0xbb6764: stur            x1, [fp, #-8]
    // 0xbb6768: CheckStackOverflow
    //     0xbb6768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb676c: cmp             SP, x16
    //     0xbb6770: b.ls            #0xbb6908
    // 0xbb6774: r1 = 1
    //     0xbb6774: movz            x1, #0x1
    // 0xbb6778: r0 = AllocateContext()
    //     0xbb6778: bl              #0xec126c  ; AllocateContextStub
    // 0xbb677c: mov             x3, x0
    // 0xbb6780: ldur            x0, [fp, #-8]
    // 0xbb6784: stur            x3, [fp, #-0x20]
    // 0xbb6788: StoreField: r3->field_f = r0
    //     0xbb6788: stur            w0, [x3, #0xf]
    // 0xbb678c: LoadField: r4 = r0->field_f
    //     0xbb678c: ldur            w4, [x0, #0xf]
    // 0xbb6790: DecompressPointer r4
    //     0xbb6790: add             x4, x4, HEAP, lsl #32
    // 0xbb6794: stur            x4, [fp, #-0x18]
    // 0xbb6798: LoadField: r5 = r0->field_13
    //     0xbb6798: ldur            w5, [x0, #0x13]
    // 0xbb679c: DecompressPointer r5
    //     0xbb679c: add             x5, x5, HEAP, lsl #32
    // 0xbb67a0: stur            x5, [fp, #-0x10]
    // 0xbb67a4: r1 = Null
    //     0xbb67a4: mov             x1, NULL
    // 0xbb67a8: r2 = 6
    //     0xbb67a8: movz            x2, #0x6
    // 0xbb67ac: r0 = AllocateArray()
    //     0xbb67ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb67b0: stur            x0, [fp, #-8]
    // 0xbb67b4: StoreField: r0->field_f = rNULL
    //     0xbb67b4: stur            NULL, [x0, #0xf]
    // 0xbb67b8: ldur            x2, [fp, #-0x18]
    // 0xbb67bc: StoreField: r0->field_13 = r2
    //     0xbb67bc: stur            w2, [x0, #0x13]
    // 0xbb67c0: ldur            x3, [fp, #-0x10]
    // 0xbb67c4: ArrayStore: r0[0] = r3  ; List_4
    //     0xbb67c4: stur            w3, [x0, #0x17]
    // 0xbb67c8: r1 = <int?>
    //     0xbb67c8: ldr             x1, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbb67cc: r0 = AllocateGrowableArray()
    //     0xbb67cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb67d0: mov             x1, x0
    // 0xbb67d4: ldur            x0, [fp, #-8]
    // 0xbb67d8: StoreField: r1->field_f = r0
    //     0xbb67d8: stur            w0, [x1, #0xf]
    // 0xbb67dc: r0 = 6
    //     0xbb67dc: movz            x0, #0x6
    // 0xbb67e0: StoreField: r1->field_b = r0
    //     0xbb67e0: stur            w0, [x1, #0xb]
    // 0xbb67e4: r16 = "-"
    //     0xbb67e4: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xbb67e8: str             x16, [SP]
    // 0xbb67ec: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbb67ec: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbb67f0: r0 = join()
    //     0xbb67f0: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xbb67f4: r1 = <String>
    //     0xbb67f4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbb67f8: stur            x0, [fp, #-8]
    // 0xbb67fc: r0 = ValueKey()
    //     0xbb67fc: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xbb6800: mov             x3, x0
    // 0xbb6804: ldur            x0, [fp, #-8]
    // 0xbb6808: stur            x3, [fp, #-0x28]
    // 0xbb680c: StoreField: r3->field_b = r0
    //     0xbb680c: stur            w0, [x3, #0xb]
    // 0xbb6810: r1 = Null
    //     0xbb6810: mov             x1, NULL
    // 0xbb6814: r2 = 4
    //     0xbb6814: movz            x2, #0x4
    // 0xbb6818: r0 = AllocateArray()
    //     0xbb6818: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb681c: mov             x2, x0
    // 0xbb6820: ldur            x0, [fp, #-0x18]
    // 0xbb6824: stur            x2, [fp, #-8]
    // 0xbb6828: StoreField: r2->field_f = r0
    //     0xbb6828: stur            w0, [x2, #0xf]
    // 0xbb682c: ldur            x3, [fp, #-0x10]
    // 0xbb6830: StoreField: r2->field_13 = r3
    //     0xbb6830: stur            w3, [x2, #0x13]
    // 0xbb6834: r1 = <int?>
    //     0xbb6834: ldr             x1, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbb6838: r0 = AllocateGrowableArray()
    //     0xbb6838: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb683c: mov             x1, x0
    // 0xbb6840: ldur            x0, [fp, #-8]
    // 0xbb6844: StoreField: r1->field_f = r0
    //     0xbb6844: stur            w0, [x1, #0xf]
    // 0xbb6848: r0 = 4
    //     0xbb6848: movz            x0, #0x4
    // 0xbb684c: StoreField: r1->field_b = r0
    //     0xbb684c: stur            w0, [x1, #0xb]
    // 0xbb6850: r16 = "-"
    //     0xbb6850: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xbb6854: str             x16, [SP]
    // 0xbb6858: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbb6858: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbb685c: r0 = join()
    //     0xbb685c: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xbb6860: stur            x0, [fp, #-8]
    // 0xbb6864: r0 = find()
    //     0xbb6864: bl              #0x851efc  ; [package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart] ZiarahRemoteRepository::find
    // 0xbb6868: stur            x0, [fp, #-0x30]
    // 0xbb686c: r0 = ZiarahTokohController()
    //     0xbb686c: bl              #0xbb6910  ; AllocateZiarahTokohControllerStub -> ZiarahTokohController (size=0x4c)
    // 0xbb6870: mov             x2, x0
    // 0xbb6874: r0 = 1
    //     0xbb6874: movz            x0, #0x1
    // 0xbb6878: stur            x2, [fp, #-0x38]
    // 0xbb687c: StoreField: r2->field_37 = r0
    //     0xbb687c: stur            x0, [x2, #0x37]
    // 0xbb6880: ldur            x0, [fp, #-0x30]
    // 0xbb6884: StoreField: r2->field_47 = r0
    //     0xbb6884: stur            w0, [x2, #0x47]
    // 0xbb6888: ldur            x0, [fp, #-0x18]
    // 0xbb688c: StoreField: r2->field_3f = r0
    //     0xbb688c: stur            w0, [x2, #0x3f]
    // 0xbb6890: ldur            x0, [fp, #-0x10]
    // 0xbb6894: StoreField: r2->field_43 = r0
    //     0xbb6894: stur            w0, [x2, #0x43]
    // 0xbb6898: mov             x1, x2
    // 0xbb689c: r0 = __ZiarahListController&GetxController&PagingMixin()
    //     0xbb689c: bl              #0xbb5bc8  ; [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::__ZiarahListController&GetxController&PagingMixin
    // 0xbb68a0: r1 = <ZiarahTokohController>
    //     0xbb68a0: add             x1, PP, #0x34, lsl #12  ; [pp+0x343b8] TypeArguments: <ZiarahTokohController>
    //     0xbb68a4: ldr             x1, [x1, #0x3b8]
    // 0xbb68a8: r0 = GetBuilder()
    //     0xbb68a8: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xbb68ac: mov             x3, x0
    // 0xbb68b0: ldur            x0, [fp, #-0x38]
    // 0xbb68b4: stur            x3, [fp, #-0x10]
    // 0xbb68b8: StoreField: r3->field_3b = r0
    //     0xbb68b8: stur            w0, [x3, #0x3b]
    // 0xbb68bc: r0 = true
    //     0xbb68bc: add             x0, NULL, #0x20  ; true
    // 0xbb68c0: StoreField: r3->field_13 = r0
    //     0xbb68c0: stur            w0, [x3, #0x13]
    // 0xbb68c4: ldur            x2, [fp, #-0x20]
    // 0xbb68c8: r1 = Function '<anonymous closure>':.
    //     0xbb68c8: add             x1, PP, #0x34, lsl #12  ; [pp+0x343c0] AnonymousClosure: (0xbb691c), in [package:nuonline/app/modules/ziarah/views/ziarah_tokoh_view.dart] ZiarahTokohView::build (0xbb6758)
    //     0xbb68cc: ldr             x1, [x1, #0x3c0]
    // 0xbb68d0: r0 = AllocateClosure()
    //     0xbb68d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb68d4: mov             x1, x0
    // 0xbb68d8: ldur            x0, [fp, #-0x10]
    // 0xbb68dc: StoreField: r0->field_f = r1
    //     0xbb68dc: stur            w1, [x0, #0xf]
    // 0xbb68e0: r1 = false
    //     0xbb68e0: add             x1, NULL, #0x30  ; false
    // 0xbb68e4: StoreField: r0->field_1f = r1
    //     0xbb68e4: stur            w1, [x0, #0x1f]
    // 0xbb68e8: StoreField: r0->field_23 = r1
    //     0xbb68e8: stur            w1, [x0, #0x23]
    // 0xbb68ec: ldur            x1, [fp, #-8]
    // 0xbb68f0: StoreField: r0->field_1b = r1
    //     0xbb68f0: stur            w1, [x0, #0x1b]
    // 0xbb68f4: ldur            x1, [fp, #-0x28]
    // 0xbb68f8: StoreField: r0->field_7 = r1
    //     0xbb68f8: stur            w1, [x0, #7]
    // 0xbb68fc: LeaveFrame
    //     0xbb68fc: mov             SP, fp
    //     0xbb6900: ldp             fp, lr, [SP], #0x10
    // 0xbb6904: ret
    //     0xbb6904: ret             
    // 0xbb6908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb690c: b               #0xbb6774
  }
  [closure] RefreshIndicator <anonymous closure>(dynamic, ZiarahTokohController) {
    // ** addr: 0xbb691c, size: 0x148
    // 0xbb691c: EnterFrame
    //     0xbb691c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6920: mov             fp, SP
    // 0xbb6924: AllocStack(0x18)
    //     0xbb6924: sub             SP, SP, #0x18
    // 0xbb6928: SetupParameters()
    //     0xbb6928: ldr             x0, [fp, #0x18]
    //     0xbb692c: ldur            w1, [x0, #0x17]
    //     0xbb6930: add             x1, x1, HEAP, lsl #32
    //     0xbb6934: stur            x1, [fp, #-8]
    // 0xbb6938: r1 = 1
    //     0xbb6938: movz            x1, #0x1
    // 0xbb693c: r0 = AllocateContext()
    //     0xbb693c: bl              #0xec126c  ; AllocateContextStub
    // 0xbb6940: mov             x1, x0
    // 0xbb6944: ldur            x0, [fp, #-8]
    // 0xbb6948: stur            x1, [fp, #-0x10]
    // 0xbb694c: StoreField: r1->field_b = r0
    //     0xbb694c: stur            w0, [x1, #0xb]
    // 0xbb6950: ldr             x2, [fp, #0x10]
    // 0xbb6954: StoreField: r1->field_f = r2
    //     0xbb6954: stur            w2, [x1, #0xf]
    // 0xbb6958: r0 = Obx()
    //     0xbb6958: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbb695c: ldur            x2, [fp, #-0x10]
    // 0xbb6960: r1 = Function '<anonymous closure>':.
    //     0xbb6960: add             x1, PP, #0x34, lsl #12  ; [pp+0x343c8] AnonymousClosure: (0xbb6a64), in [package:nuonline/app/modules/ziarah/views/ziarah_tokoh_view.dart] ZiarahTokohView::build (0xbb6758)
    //     0xbb6964: ldr             x1, [x1, #0x3c8]
    // 0xbb6968: stur            x0, [fp, #-8]
    // 0xbb696c: r0 = AllocateClosure()
    //     0xbb696c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6970: mov             x1, x0
    // 0xbb6974: ldur            x0, [fp, #-8]
    // 0xbb6978: StoreField: r0->field_b = r1
    //     0xbb6978: stur            w1, [x0, #0xb]
    // 0xbb697c: ldr             x2, [fp, #0x10]
    // 0xbb6980: r1 = Function 'onPageScrolled':.
    //     0xbb6980: add             x1, PP, #0x34, lsl #12  ; [pp+0x343d0] AnonymousClosure: (0xbb64a0), in [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::onPageScrolled (0xbb64dc)
    //     0xbb6984: ldr             x1, [x1, #0x3d0]
    // 0xbb6988: r0 = AllocateClosure()
    //     0xbb6988: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb698c: r1 = <ScrollNotification>
    //     0xbb698c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbb6990: ldr             x1, [x1, #0x110]
    // 0xbb6994: stur            x0, [fp, #-0x10]
    // 0xbb6998: r0 = NotificationListener()
    //     0xbb6998: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbb699c: mov             x1, x0
    // 0xbb69a0: ldur            x0, [fp, #-0x10]
    // 0xbb69a4: stur            x1, [fp, #-0x18]
    // 0xbb69a8: StoreField: r1->field_13 = r0
    //     0xbb69a8: stur            w0, [x1, #0x13]
    // 0xbb69ac: ldur            x0, [fp, #-8]
    // 0xbb69b0: StoreField: r1->field_b = r0
    //     0xbb69b0: stur            w0, [x1, #0xb]
    // 0xbb69b4: r0 = ListTileTheme()
    //     0xbb69b4: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xbb69b8: mov             x1, x0
    // 0xbb69bc: r0 = Instance_EdgeInsets
    //     0xbb69bc: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbb69c0: stur            x1, [fp, #-8]
    // 0xbb69c4: StoreField: r1->field_2b = r0
    //     0xbb69c4: stur            w0, [x1, #0x2b]
    // 0xbb69c8: r0 = 16.000000
    //     0xbb69c8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbb69cc: ldr             x0, [x0, #0x80]
    // 0xbb69d0: StoreField: r1->field_37 = r0
    //     0xbb69d0: stur            w0, [x1, #0x37]
    // 0xbb69d4: r0 = 24.000000
    //     0xbb69d4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xbb69d8: ldr             x0, [x0, #0x368]
    // 0xbb69dc: StoreField: r1->field_3f = r0
    //     0xbb69dc: stur            w0, [x1, #0x3f]
    // 0xbb69e0: ldur            x0, [fp, #-0x18]
    // 0xbb69e4: StoreField: r1->field_b = r0
    //     0xbb69e4: stur            w0, [x1, #0xb]
    // 0xbb69e8: r0 = RefreshIndicator()
    //     0xbb69e8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbb69ec: mov             x3, x0
    // 0xbb69f0: ldur            x0, [fp, #-8]
    // 0xbb69f4: stur            x3, [fp, #-0x10]
    // 0xbb69f8: StoreField: r3->field_b = r0
    //     0xbb69f8: stur            w0, [x3, #0xb]
    // 0xbb69fc: d0 = 40.000000
    //     0xbb69fc: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbb6a00: StoreField: r3->field_f = d0
    //     0xbb6a00: stur            d0, [x3, #0xf]
    // 0xbb6a04: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbb6a04: stur            xzr, [x3, #0x17]
    // 0xbb6a08: ldr             x2, [fp, #0x10]
    // 0xbb6a0c: r1 = Function 'onPageRefresh':.
    //     0xbb6a0c: add             x1, PP, #0x34, lsl #12  ; [pp+0x343d8] AnonymousClosure: (0xbb63b4), in [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::onPageRefresh (0xbb63ec)
    //     0xbb6a10: ldr             x1, [x1, #0x3d8]
    // 0xbb6a14: r0 = AllocateClosure()
    //     0xbb6a14: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6a18: mov             x1, x0
    // 0xbb6a1c: ldur            x0, [fp, #-0x10]
    // 0xbb6a20: StoreField: r0->field_1f = r1
    //     0xbb6a20: stur            w1, [x0, #0x1f]
    // 0xbb6a24: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbb6a24: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbb6a28: ldr             x1, [x1, #0xf58]
    // 0xbb6a2c: StoreField: r0->field_2f = r1
    //     0xbb6a2c: stur            w1, [x0, #0x2f]
    // 0xbb6a30: d0 = 2.500000
    //     0xbb6a30: fmov            d0, #2.50000000
    // 0xbb6a34: StoreField: r0->field_3b = d0
    //     0xbb6a34: stur            d0, [x0, #0x3b]
    // 0xbb6a38: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbb6a38: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbb6a3c: ldr             x1, [x1, #0xa68]
    // 0xbb6a40: StoreField: r0->field_47 = r1
    //     0xbb6a40: stur            w1, [x0, #0x47]
    // 0xbb6a44: d0 = 2.000000
    //     0xbb6a44: fmov            d0, #2.00000000
    // 0xbb6a48: StoreField: r0->field_4b = d0
    //     0xbb6a48: stur            d0, [x0, #0x4b]
    // 0xbb6a4c: r1 = Instance__IndicatorType
    //     0xbb6a4c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbb6a50: ldr             x1, [x1, #0xa70]
    // 0xbb6a54: StoreField: r0->field_43 = r1
    //     0xbb6a54: stur            w1, [x0, #0x43]
    // 0xbb6a58: LeaveFrame
    //     0xbb6a58: mov             SP, fp
    //     0xbb6a5c: ldp             fp, lr, [SP], #0x10
    // 0xbb6a60: ret
    //     0xbb6a60: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xbb6a64, size: 0x1f0
    // 0xbb6a64: EnterFrame
    //     0xbb6a64: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6a68: mov             fp, SP
    // 0xbb6a6c: AllocStack(0x38)
    //     0xbb6a6c: sub             SP, SP, #0x38
    // 0xbb6a70: SetupParameters()
    //     0xbb6a70: ldr             x0, [fp, #0x10]
    //     0xbb6a74: ldur            w2, [x0, #0x17]
    //     0xbb6a78: add             x2, x2, HEAP, lsl #32
    //     0xbb6a7c: stur            x2, [fp, #-8]
    // 0xbb6a80: CheckStackOverflow
    //     0xbb6a80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6a84: cmp             SP, x16
    //     0xbb6a88: b.ls            #0xbb6c4c
    // 0xbb6a8c: LoadField: r0 = r2->field_f
    //     0xbb6a8c: ldur            w0, [x2, #0xf]
    // 0xbb6a90: DecompressPointer r0
    //     0xbb6a90: add             x0, x0, HEAP, lsl #32
    // 0xbb6a94: LoadField: r1 = r0->field_33
    //     0xbb6a94: ldur            w1, [x0, #0x33]
    // 0xbb6a98: DecompressPointer r1
    //     0xbb6a98: add             x1, x1, HEAP, lsl #32
    // 0xbb6a9c: r0 = value()
    //     0xbb6a9c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb6aa0: tbnz            w0, #4, #0xbb6bcc
    // 0xbb6aa4: r0 = NEmptyState()
    //     0xbb6aa4: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xbb6aa8: mov             x1, x0
    // 0xbb6aac: r2 = "Silakan berkontribusi untuk mengirimkan data Ziarah di aplikasi NU Online, baik informasi tokoh, masjid bersejarah, kompleks makam, musem, ataupun petilasan."
    //     0xbb6aac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ecf0] "Silakan berkontribusi untuk mengirimkan data Ziarah di aplikasi NU Online, baik informasi tokoh, masjid bersejarah, kompleks makam, musem, ataupun petilasan."
    //     0xbb6ab0: ldr             x2, [x2, #0xcf0]
    // 0xbb6ab4: r3 = "assets/images/illustration/no_location.svg"
    //     0xbb6ab4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d018] "assets/images/illustration/no_location.svg"
    //     0xbb6ab8: ldr             x3, [x3, #0x18]
    // 0xbb6abc: r5 = "Hasil Tidak Ditemukan"
    //     0xbb6abc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ecf8] "Hasil Tidak Ditemukan"
    //     0xbb6ac0: ldr             x5, [x5, #0xcf8]
    // 0xbb6ac4: stur            x0, [fp, #-0x10]
    // 0xbb6ac8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xbb6ac8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xbb6acc: r0 = NEmptyState.svg()
    //     0xbb6acc: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xbb6ad0: r1 = Function '<anonymous closure>':.
    //     0xbb6ad0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34400] AnonymousClosure: (0xb6ef50), in [package:nuonline/app/modules/ziarah/views/ziarah_view.dart] ZiarahView::build (0xb6f104)
    //     0xbb6ad4: ldr             x1, [x1, #0x400]
    // 0xbb6ad8: r2 = Null
    //     0xbb6ad8: mov             x2, NULL
    // 0xbb6adc: r0 = AllocateClosure()
    //     0xbb6adc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6ae0: stur            x0, [fp, #-0x18]
    // 0xbb6ae4: r0 = OutlinedButton()
    //     0xbb6ae4: bl              #0xa3b670  ; AllocateOutlinedButtonStub -> OutlinedButton (size=0x3c)
    // 0xbb6ae8: mov             x3, x0
    // 0xbb6aec: ldur            x0, [fp, #-0x18]
    // 0xbb6af0: stur            x3, [fp, #-0x20]
    // 0xbb6af4: StoreField: r3->field_b = r0
    //     0xbb6af4: stur            w0, [x3, #0xb]
    // 0xbb6af8: r0 = false
    //     0xbb6af8: add             x0, NULL, #0x30  ; false
    // 0xbb6afc: StoreField: r3->field_27 = r0
    //     0xbb6afc: stur            w0, [x3, #0x27]
    // 0xbb6b00: r0 = true
    //     0xbb6b00: add             x0, NULL, #0x20  ; true
    // 0xbb6b04: StoreField: r3->field_2f = r0
    //     0xbb6b04: stur            w0, [x3, #0x2f]
    // 0xbb6b08: r0 = Instance_Text
    //     0xbb6b08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ecd0] Obj!Text@e23801
    //     0xbb6b0c: ldr             x0, [x0, #0xcd0]
    // 0xbb6b10: StoreField: r3->field_37 = r0
    //     0xbb6b10: stur            w0, [x3, #0x37]
    // 0xbb6b14: r1 = Null
    //     0xbb6b14: mov             x1, NULL
    // 0xbb6b18: r2 = 6
    //     0xbb6b18: movz            x2, #0x6
    // 0xbb6b1c: r0 = AllocateArray()
    //     0xbb6b1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb6b20: mov             x2, x0
    // 0xbb6b24: ldur            x0, [fp, #-0x10]
    // 0xbb6b28: stur            x2, [fp, #-0x18]
    // 0xbb6b2c: StoreField: r2->field_f = r0
    //     0xbb6b2c: stur            w0, [x2, #0xf]
    // 0xbb6b30: r16 = Instance_SizedBox
    //     0xbb6b30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ecd8] Obj!SizedBox@e1e361
    //     0xbb6b34: ldr             x16, [x16, #0xcd8]
    // 0xbb6b38: StoreField: r2->field_13 = r16
    //     0xbb6b38: stur            w16, [x2, #0x13]
    // 0xbb6b3c: ldur            x0, [fp, #-0x20]
    // 0xbb6b40: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb6b40: stur            w0, [x2, #0x17]
    // 0xbb6b44: r1 = <Widget>
    //     0xbb6b44: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbb6b48: r0 = AllocateGrowableArray()
    //     0xbb6b48: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb6b4c: mov             x1, x0
    // 0xbb6b50: ldur            x0, [fp, #-0x18]
    // 0xbb6b54: stur            x1, [fp, #-0x10]
    // 0xbb6b58: StoreField: r1->field_f = r0
    //     0xbb6b58: stur            w0, [x1, #0xf]
    // 0xbb6b5c: r0 = 6
    //     0xbb6b5c: movz            x0, #0x6
    // 0xbb6b60: StoreField: r1->field_b = r0
    //     0xbb6b60: stur            w0, [x1, #0xb]
    // 0xbb6b64: r0 = Column()
    //     0xbb6b64: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb6b68: mov             x1, x0
    // 0xbb6b6c: r0 = Instance_Axis
    //     0xbb6b6c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbb6b70: StoreField: r1->field_f = r0
    //     0xbb6b70: stur            w0, [x1, #0xf]
    // 0xbb6b74: r0 = Instance_MainAxisAlignment
    //     0xbb6b74: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0xbb6b78: ldr             x0, [x0, #0x290]
    // 0xbb6b7c: StoreField: r1->field_13 = r0
    //     0xbb6b7c: stur            w0, [x1, #0x13]
    // 0xbb6b80: r0 = Instance_MainAxisSize
    //     0xbb6b80: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbb6b84: ldr             x0, [x0, #0x738]
    // 0xbb6b88: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb6b88: stur            w0, [x1, #0x17]
    // 0xbb6b8c: r0 = Instance_CrossAxisAlignment
    //     0xbb6b8c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbb6b90: ldr             x0, [x0, #0x740]
    // 0xbb6b94: StoreField: r1->field_1b = r0
    //     0xbb6b94: stur            w0, [x1, #0x1b]
    // 0xbb6b98: r0 = Instance_VerticalDirection
    //     0xbb6b98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbb6b9c: ldr             x0, [x0, #0x748]
    // 0xbb6ba0: StoreField: r1->field_23 = r0
    //     0xbb6ba0: stur            w0, [x1, #0x23]
    // 0xbb6ba4: r0 = Instance_Clip
    //     0xbb6ba4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbb6ba8: ldr             x0, [x0, #0x750]
    // 0xbb6bac: StoreField: r1->field_2b = r0
    //     0xbb6bac: stur            w0, [x1, #0x2b]
    // 0xbb6bb0: StoreField: r1->field_2f = rZR
    //     0xbb6bb0: stur            xzr, [x1, #0x2f]
    // 0xbb6bb4: ldur            x0, [fp, #-0x10]
    // 0xbb6bb8: StoreField: r1->field_b = r0
    //     0xbb6bb8: stur            w0, [x1, #0xb]
    // 0xbb6bbc: mov             x0, x1
    // 0xbb6bc0: LeaveFrame
    //     0xbb6bc0: mov             SP, fp
    //     0xbb6bc4: ldp             fp, lr, [SP], #0x10
    // 0xbb6bc8: ret
    //     0xbb6bc8: ret             
    // 0xbb6bcc: ldur            x2, [fp, #-8]
    // 0xbb6bd0: LoadField: r1 = r2->field_f
    //     0xbb6bd0: ldur            w1, [x2, #0xf]
    // 0xbb6bd4: DecompressPointer r1
    //     0xbb6bd4: add             x1, x1, HEAP, lsl #32
    // 0xbb6bd8: r0 = itemsCount()
    //     0xbb6bd8: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbb6bdc: r1 = Function '<anonymous closure>':.
    //     0xbb6bdc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34408] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbb6be0: ldr             x1, [x1, #0x408]
    // 0xbb6be4: r2 = Null
    //     0xbb6be4: mov             x2, NULL
    // 0xbb6be8: stur            x0, [fp, #-0x28]
    // 0xbb6bec: r0 = AllocateClosure()
    //     0xbb6bec: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6bf0: ldur            x2, [fp, #-8]
    // 0xbb6bf4: r1 = Function '<anonymous closure>':.
    //     0xbb6bf4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34410] AnonymousClosure: (0xbb6c54), in [package:nuonline/app/modules/ziarah/views/ziarah_tokoh_view.dart] ZiarahTokohView::build (0xbb6758)
    //     0xbb6bf8: ldr             x1, [x1, #0x410]
    // 0xbb6bfc: stur            x0, [fp, #-8]
    // 0xbb6c00: r0 = AllocateClosure()
    //     0xbb6c00: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6c04: stur            x0, [fp, #-0x10]
    // 0xbb6c08: r0 = ListView()
    //     0xbb6c08: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbb6c0c: stur            x0, [fp, #-0x18]
    // 0xbb6c10: r16 = true
    //     0xbb6c10: add             x16, NULL, #0x20  ; true
    // 0xbb6c14: r30 = Instance_EdgeInsets
    //     0xbb6c14: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xbb6c18: ldr             lr, [lr, #0x360]
    // 0xbb6c1c: stp             lr, x16, [SP]
    // 0xbb6c20: mov             x1, x0
    // 0xbb6c24: ldur            x2, [fp, #-0x10]
    // 0xbb6c28: ldur            x3, [fp, #-0x28]
    // 0xbb6c2c: ldur            x5, [fp, #-8]
    // 0xbb6c30: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xbb6c30: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbb6c34: ldr             x4, [x4, #0x100]
    // 0xbb6c38: r0 = ListView.separated()
    //     0xbb6c38: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbb6c3c: ldur            x0, [fp, #-0x18]
    // 0xbb6c40: LeaveFrame
    //     0xbb6c40: mov             SP, fp
    //     0xbb6c44: ldp             fp, lr, [SP], #0x10
    // 0xbb6c48: ret
    //     0xbb6c48: ret             
    // 0xbb6c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6c50: b               #0xbb6a8c
  }
  [closure] NZiarahListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbb6c54, size: 0x240
    // 0xbb6c54: EnterFrame
    //     0xbb6c54: stp             fp, lr, [SP, #-0x10]!
    //     0xbb6c58: mov             fp, SP
    // 0xbb6c5c: AllocStack(0x40)
    //     0xbb6c5c: sub             SP, SP, #0x40
    // 0xbb6c60: SetupParameters()
    //     0xbb6c60: ldr             x0, [fp, #0x20]
    //     0xbb6c64: ldur            w1, [x0, #0x17]
    //     0xbb6c68: add             x1, x1, HEAP, lsl #32
    //     0xbb6c6c: stur            x1, [fp, #-8]
    // 0xbb6c70: CheckStackOverflow
    //     0xbb6c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb6c74: cmp             SP, x16
    //     0xbb6c78: b.ls            #0xbb6e8c
    // 0xbb6c7c: r1 = 1
    //     0xbb6c7c: movz            x1, #0x1
    // 0xbb6c80: r0 = AllocateContext()
    //     0xbb6c80: bl              #0xec126c  ; AllocateContextStub
    // 0xbb6c84: mov             x3, x0
    // 0xbb6c88: ldur            x0, [fp, #-8]
    // 0xbb6c8c: stur            x3, [fp, #-0x10]
    // 0xbb6c90: StoreField: r3->field_b = r0
    //     0xbb6c90: stur            w0, [x3, #0xb]
    // 0xbb6c94: LoadField: r1 = r0->field_f
    //     0xbb6c94: ldur            w1, [x0, #0xf]
    // 0xbb6c98: DecompressPointer r1
    //     0xbb6c98: add             x1, x1, HEAP, lsl #32
    // 0xbb6c9c: ldr             x2, [fp, #0x10]
    // 0xbb6ca0: r4 = LoadInt32Instr(r2)
    //     0xbb6ca0: sbfx            x4, x2, #1, #0x1f
    //     0xbb6ca4: tbz             w2, #0, #0xbb6cac
    //     0xbb6ca8: ldur            x4, [x2, #7]
    // 0xbb6cac: mov             x2, x4
    // 0xbb6cb0: r0 = find()
    //     0xbb6cb0: bl              #0xbb6294  ; [package:nuonline/app/modules/ziarah/controllers/ziarah_list_controller.dart] __ZiarahListController&GetxController&PagingMixin::find
    // 0xbb6cb4: mov             x3, x0
    // 0xbb6cb8: ldur            x2, [fp, #-0x10]
    // 0xbb6cbc: stur            x3, [fp, #-0x28]
    // 0xbb6cc0: StoreField: r2->field_f = r0
    //     0xbb6cc0: stur            w0, [x2, #0xf]
    //     0xbb6cc4: ldurb           w16, [x2, #-1]
    //     0xbb6cc8: ldurb           w17, [x0, #-1]
    //     0xbb6ccc: and             x16, x17, x16, lsr #2
    //     0xbb6cd0: tst             x16, HEAP, lsr #32
    //     0xbb6cd4: b.eq            #0xbb6cdc
    //     0xbb6cd8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb6cdc: cmp             w3, NULL
    // 0xbb6ce0: b.ne            #0xbb6cf8
    // 0xbb6ce4: r0 = Instance_NZiarahListTile
    //     0xbb6ce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ed20] Obj!NZiarahListTile@e20df1
    //     0xbb6ce8: ldr             x0, [x0, #0xd20]
    // 0xbb6cec: LeaveFrame
    //     0xbb6cec: mov             SP, fp
    //     0xbb6cf0: ldp             fp, lr, [SP], #0x10
    // 0xbb6cf4: ret
    //     0xbb6cf4: ret             
    // 0xbb6cf8: ldur            x0, [fp, #-8]
    // 0xbb6cfc: LoadField: r1 = r3->field_13
    //     0xbb6cfc: ldur            w1, [x3, #0x13]
    // 0xbb6d00: DecompressPointer r1
    //     0xbb6d00: add             x1, x1, HEAP, lsl #32
    // 0xbb6d04: LoadField: r4 = r1->field_7
    //     0xbb6d04: ldur            w4, [x1, #7]
    // 0xbb6d08: DecompressPointer r4
    //     0xbb6d08: add             x4, x4, HEAP, lsl #32
    // 0xbb6d0c: stur            x4, [fp, #-0x20]
    // 0xbb6d10: LoadField: r5 = r3->field_f
    //     0xbb6d10: ldur            w5, [x3, #0xf]
    // 0xbb6d14: DecompressPointer r5
    //     0xbb6d14: add             x5, x5, HEAP, lsl #32
    // 0xbb6d18: stur            x5, [fp, #-0x18]
    // 0xbb6d1c: LoadField: r1 = r0->field_b
    //     0xbb6d1c: ldur            w1, [x0, #0xb]
    // 0xbb6d20: DecompressPointer r1
    //     0xbb6d20: add             x1, x1, HEAP, lsl #32
    // 0xbb6d24: LoadField: r0 = r1->field_f
    //     0xbb6d24: ldur            w0, [x1, #0xf]
    // 0xbb6d28: DecompressPointer r0
    //     0xbb6d28: add             x0, x0, HEAP, lsl #32
    // 0xbb6d2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb6d2c: ldur            w1, [x0, #0x17]
    // 0xbb6d30: DecompressPointer r1
    //     0xbb6d30: add             x1, x1, HEAP, lsl #32
    // 0xbb6d34: tbnz            w1, #4, #0xbb6d44
    // 0xbb6d38: mov             x0, x3
    // 0xbb6d3c: r2 = ""
    //     0xbb6d3c: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbb6d40: b               #0xbb6d58
    // 0xbb6d44: LoadField: r1 = r3->field_23
    //     0xbb6d44: ldur            w1, [x3, #0x23]
    // 0xbb6d48: DecompressPointer r1
    //     0xbb6d48: add             x1, x1, HEAP, lsl #32
    // 0xbb6d4c: r0 = full()
    //     0xbb6d4c: bl              #0xb6ce00  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::full
    // 0xbb6d50: mov             x2, x0
    // 0xbb6d54: ldur            x0, [fp, #-0x28]
    // 0xbb6d58: mov             x1, x0
    // 0xbb6d5c: stur            x2, [fp, #-8]
    // 0xbb6d60: r0 = distanceKM()
    //     0xbb6d60: bl              #0xb6ec4c  ; [package:nuonline/app/data/models/ziarah.dart] Ziarah::distanceKM
    // 0xbb6d64: mov             x2, x0
    // 0xbb6d68: ldur            x0, [fp, #-0x28]
    // 0xbb6d6c: stur            x2, [fp, #-0x38]
    // 0xbb6d70: LoadField: r1 = r0->field_1f
    //     0xbb6d70: ldur            w1, [x0, #0x1f]
    // 0xbb6d74: DecompressPointer r1
    //     0xbb6d74: add             x1, x1, HEAP, lsl #32
    // 0xbb6d78: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbb6d78: ldur            w3, [x1, #0x17]
    // 0xbb6d7c: DecompressPointer r3
    //     0xbb6d7c: add             x3, x3, HEAP, lsl #32
    // 0xbb6d80: stur            x3, [fp, #-0x30]
    // 0xbb6d84: LoadField: r4 = r1->field_7
    //     0xbb6d84: ldur            x4, [x1, #7]
    // 0xbb6d88: cmp             x4, #5
    // 0xbb6d8c: b.gt            #0xbb6dcc
    // 0xbb6d90: cmp             x4, #4
    // 0xbb6d94: b.gt            #0xbb6dc0
    // 0xbb6d98: r0 = BoxInt64Instr(r4)
    //     0xbb6d98: sbfiz           x0, x4, #1, #0x1f
    //     0xbb6d9c: cmp             x4, x0, asr #1
    //     0xbb6da0: b.eq            #0xbb6dac
    //     0xbb6da4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb6da8: stur            x4, [x0, #7]
    // 0xbb6dac: cmp             w0, #8
    // 0xbb6db0: b.ne            #0xbb6e08
    // 0xbb6db4: r5 = Instance_IconData
    //     0xbb6db4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed28] Obj!IconData@e10811
    //     0xbb6db8: ldr             x5, [x5, #0xd28]
    // 0xbb6dbc: b               #0xbb6e10
    // 0xbb6dc0: r5 = Instance_IconData
    //     0xbb6dc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed30] Obj!IconData@e107f1
    //     0xbb6dc4: ldr             x5, [x5, #0xd30]
    // 0xbb6dc8: b               #0xbb6e10
    // 0xbb6dcc: cmp             x4, #6
    // 0xbb6dd0: b.gt            #0xbb6de0
    // 0xbb6dd4: r5 = Instance_IconData
    //     0xbb6dd4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed38] Obj!IconData@e107d1
    //     0xbb6dd8: ldr             x5, [x5, #0xd38]
    // 0xbb6ddc: b               #0xbb6e10
    // 0xbb6de0: r0 = BoxInt64Instr(r4)
    //     0xbb6de0: sbfiz           x0, x4, #1, #0x1f
    //     0xbb6de4: cmp             x4, x0, asr #1
    //     0xbb6de8: b.eq            #0xbb6df4
    //     0xbb6dec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb6df0: stur            x4, [x0, #7]
    // 0xbb6df4: cmp             w0, #0xe
    // 0xbb6df8: b.ne            #0xbb6e08
    // 0xbb6dfc: r5 = Instance_IconData
    //     0xbb6dfc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed40] Obj!IconData@e107b1
    //     0xbb6e00: ldr             x5, [x5, #0xd40]
    // 0xbb6e04: b               #0xbb6e10
    // 0xbb6e08: r5 = Instance_IconData
    //     0xbb6e08: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed48] Obj!IconData@e10791
    //     0xbb6e0c: ldr             x5, [x5, #0xd48]
    // 0xbb6e10: ldur            x1, [fp, #-0x20]
    // 0xbb6e14: ldur            x4, [fp, #-0x18]
    // 0xbb6e18: ldur            x0, [fp, #-8]
    // 0xbb6e1c: stur            x5, [fp, #-0x28]
    // 0xbb6e20: r0 = NZiarahListTile()
    //     0xbb6e20: bl              #0xb6b414  ; AllocateNZiarahListTileStub -> NZiarahListTile (size=0x2c)
    // 0xbb6e24: mov             x3, x0
    // 0xbb6e28: ldur            x0, [fp, #-0x20]
    // 0xbb6e2c: stur            x3, [fp, #-0x40]
    // 0xbb6e30: StoreField: r3->field_b = r0
    //     0xbb6e30: stur            w0, [x3, #0xb]
    // 0xbb6e34: ldur            x2, [fp, #-0x10]
    // 0xbb6e38: r1 = Function '<anonymous closure>':.
    //     0xbb6e38: add             x1, PP, #0x34, lsl #12  ; [pp+0x34418] AnonymousClosure: (0xb6ee38), in [package:nuonline/app/modules/ziarah/views/ziarah_search_view.dart] ZiarahSearchView::build (0xb6e354)
    //     0xbb6e3c: ldr             x1, [x1, #0x418]
    // 0xbb6e40: r0 = AllocateClosure()
    //     0xbb6e40: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb6e44: mov             x1, x0
    // 0xbb6e48: ldur            x0, [fp, #-0x40]
    // 0xbb6e4c: StoreField: r0->field_f = r1
    //     0xbb6e4c: stur            w1, [x0, #0xf]
    // 0xbb6e50: ldur            x1, [fp, #-0x18]
    // 0xbb6e54: StoreField: r0->field_13 = r1
    //     0xbb6e54: stur            w1, [x0, #0x13]
    // 0xbb6e58: ldur            x1, [fp, #-8]
    // 0xbb6e5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb6e5c: stur            w1, [x0, #0x17]
    // 0xbb6e60: ldur            x1, [fp, #-0x38]
    // 0xbb6e64: StoreField: r0->field_1b = r1
    //     0xbb6e64: stur            w1, [x0, #0x1b]
    // 0xbb6e68: ldur            x1, [fp, #-0x30]
    // 0xbb6e6c: StoreField: r0->field_1f = r1
    //     0xbb6e6c: stur            w1, [x0, #0x1f]
    // 0xbb6e70: r1 = false
    //     0xbb6e70: add             x1, NULL, #0x30  ; false
    // 0xbb6e74: StoreField: r0->field_23 = r1
    //     0xbb6e74: stur            w1, [x0, #0x23]
    // 0xbb6e78: ldur            x1, [fp, #-0x28]
    // 0xbb6e7c: StoreField: r0->field_27 = r1
    //     0xbb6e7c: stur            w1, [x0, #0x27]
    // 0xbb6e80: LeaveFrame
    //     0xbb6e80: mov             SP, fp
    //     0xbb6e84: ldp             fp, lr, [SP], #0x10
    // 0xbb6e88: ret
    //     0xbb6e88: ret             
    // 0xbb6e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb6e8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb6e90: b               #0xbb6c7c
  }
}
