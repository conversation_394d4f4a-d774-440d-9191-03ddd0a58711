// lib: , url: package:nuonline/common/extensions/date_time_extension.dart

// class id: 1050698, size: 0x8
class :: {

  static _ DateTimeExtensions.hm(/* No info */) {
    // ** addr: 0x81c130, size: 0x78
    // 0x81c130: EnterFrame
    //     0x81c130: stp             fp, lr, [SP, #-0x10]!
    //     0x81c134: mov             fp, SP
    // 0x81c138: AllocStack(0x18)
    //     0x81c138: sub             SP, SP, #0x18
    // 0x81c13c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x81c13c: mov             x0, x1
    //     0x81c140: stur            x1, [fp, #-8]
    // 0x81c144: CheckStackOverflow
    //     0x81c144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81c148: cmp             SP, x16
    //     0x81c14c: b.ls            #0x81c1a0
    // 0x81c150: r1 = Null
    //     0x81c150: mov             x1, NULL
    // 0x81c154: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x81c154: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x81c158: ldr             x2, [x2, #0xad0]
    // 0x81c15c: r0 = verifiedLocale()
    //     0x81c15c: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x81c160: stur            x0, [fp, #-0x10]
    // 0x81c164: r0 = DateFormat()
    //     0x81c164: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x81c168: mov             x3, x0
    // 0x81c16c: ldur            x0, [fp, #-0x10]
    // 0x81c170: stur            x3, [fp, #-0x18]
    // 0x81c174: StoreField: r3->field_7 = r0
    //     0x81c174: stur            w0, [x3, #7]
    // 0x81c178: mov             x1, x3
    // 0x81c17c: r2 = "Hm"
    //     0x81c17c: add             x2, PP, #8, lsl #12  ; [pp+0x8ad8] "Hm"
    //     0x81c180: ldr             x2, [x2, #0xad8]
    // 0x81c184: r0 = addPattern()
    //     0x81c184: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x81c188: ldur            x1, [fp, #-0x18]
    // 0x81c18c: ldur            x2, [fp, #-8]
    // 0x81c190: r0 = format()
    //     0x81c190: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x81c194: LeaveFrame
    //     0x81c194: mov             SP, fp
    //     0x81c198: ldp             fp, lr, [SP], #0x10
    // 0x81c19c: ret
    //     0x81c19c: ret             
    // 0x81c1a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81c1a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81c1a4: b               #0x81c150
  }
  static _ DateTimeExtensions.toUniqueID(/* No info */) {
    // ** addr: 0x81da9c, size: 0xcc
    // 0x81da9c: EnterFrame
    //     0x81da9c: stp             fp, lr, [SP, #-0x10]!
    //     0x81daa0: mov             fp, SP
    // 0x81daa4: AllocStack(0x28)
    //     0x81daa4: sub             SP, SP, #0x28
    // 0x81daa8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x81daa8: mov             x3, x1
    //     0x81daac: mov             x0, x2
    //     0x81dab0: stur            x1, [fp, #-8]
    //     0x81dab4: stur            x2, [fp, #-0x10]
    // 0x81dab8: CheckStackOverflow
    //     0x81dab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81dabc: cmp             SP, x16
    //     0x81dac0: b.ls            #0x81db60
    // 0x81dac4: r1 = Null
    //     0x81dac4: mov             x1, NULL
    // 0x81dac8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x81dac8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x81dacc: ldr             x2, [x2, #0xad0]
    // 0x81dad0: r0 = verifiedLocale()
    //     0x81dad0: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x81dad4: stur            x0, [fp, #-0x18]
    // 0x81dad8: r0 = DateFormat()
    //     0x81dad8: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x81dadc: mov             x3, x0
    // 0x81dae0: ldur            x0, [fp, #-0x18]
    // 0x81dae4: stur            x3, [fp, #-0x20]
    // 0x81dae8: StoreField: r3->field_7 = r0
    //     0x81dae8: stur            w0, [x3, #7]
    // 0x81daec: mov             x1, x3
    // 0x81daf0: r2 = "ddM"
    //     0x81daf0: add             x2, PP, #8, lsl #12  ; [pp+0x8ca0] "ddM"
    //     0x81daf4: ldr             x2, [x2, #0xca0]
    // 0x81daf8: r0 = addPattern()
    //     0x81daf8: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x81dafc: ldur            x1, [fp, #-0x20]
    // 0x81db00: ldur            x2, [fp, #-8]
    // 0x81db04: r0 = format()
    //     0x81db04: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x81db08: r1 = Null
    //     0x81db08: mov             x1, NULL
    // 0x81db0c: r2 = 4
    //     0x81db0c: movz            x2, #0x4
    // 0x81db10: stur            x0, [fp, #-8]
    // 0x81db14: r0 = AllocateArray()
    //     0x81db14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81db18: mov             x2, x0
    // 0x81db1c: ldur            x0, [fp, #-8]
    // 0x81db20: StoreField: r2->field_f = r0
    //     0x81db20: stur            w0, [x2, #0xf]
    // 0x81db24: ldur            x3, [fp, #-0x10]
    // 0x81db28: r0 = BoxInt64Instr(r3)
    //     0x81db28: sbfiz           x0, x3, #1, #0x1f
    //     0x81db2c: cmp             x3, x0, asr #1
    //     0x81db30: b.eq            #0x81db3c
    //     0x81db34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81db38: stur            x3, [x0, #7]
    // 0x81db3c: StoreField: r2->field_13 = r0
    //     0x81db3c: stur            w0, [x2, #0x13]
    // 0x81db40: str             x2, [SP]
    // 0x81db44: r0 = _interpolate()
    //     0x81db44: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81db48: mov             x1, x0
    // 0x81db4c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x81db4c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x81db50: r0 = parse()
    //     0x81db50: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x81db54: LeaveFrame
    //     0x81db54: mov             SP, fp
    //     0x81db58: ldp             fp, lr, [SP], #0x10
    // 0x81db5c: ret
    //     0x81db5c: ret             
    // 0x81db60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81db60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81db64: b               #0x81dac4
  }
  static _ DateTimeExtensions.gregorian(/* No info */) {
    // ** addr: 0x81f220, size: 0x7c
    // 0x81f220: EnterFrame
    //     0x81f220: stp             fp, lr, [SP, #-0x10]!
    //     0x81f224: mov             fp, SP
    // 0x81f228: AllocStack(0x18)
    //     0x81f228: sub             SP, SP, #0x18
    // 0x81f22c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x81f22c: mov             x0, x1
    //     0x81f230: stur            x1, [fp, #-8]
    // 0x81f234: CheckStackOverflow
    //     0x81f234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81f238: cmp             SP, x16
    //     0x81f23c: b.ls            #0x81f294
    // 0x81f240: r1 = "id_ID"
    //     0x81f240: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0x81f244: ldr             x1, [x1, #0x200]
    // 0x81f248: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x81f248: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x81f24c: ldr             x2, [x2, #0xad0]
    // 0x81f250: r0 = verifiedLocale()
    //     0x81f250: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x81f254: stur            x0, [fp, #-0x10]
    // 0x81f258: r0 = DateFormat()
    //     0x81f258: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x81f25c: mov             x3, x0
    // 0x81f260: ldur            x0, [fp, #-0x10]
    // 0x81f264: stur            x3, [fp, #-0x18]
    // 0x81f268: StoreField: r3->field_7 = r0
    //     0x81f268: stur            w0, [x3, #7]
    // 0x81f26c: mov             x1, x3
    // 0x81f270: r2 = "d MMMM yyyy"
    //     0x81f270: add             x2, PP, #9, lsl #12  ; [pp+0x9208] "d MMMM yyyy"
    //     0x81f274: ldr             x2, [x2, #0x208]
    // 0x81f278: r0 = addPattern()
    //     0x81f278: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x81f27c: ldur            x1, [fp, #-0x18]
    // 0x81f280: ldur            x2, [fp, #-8]
    // 0x81f284: r0 = format()
    //     0x81f284: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x81f288: LeaveFrame
    //     0x81f288: mov             SP, fp
    //     0x81f28c: ldp             fp, lr, [SP], #0x10
    // 0x81f290: ret
    //     0x81f290: ret             
    // 0x81f294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81f294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81f298: b               #0x81f240
  }
  static _ DateTimeExtensions.isToday(/* No info */) {
    // ** addr: 0x82a788, size: 0x204
    // 0x82a788: EnterFrame
    //     0x82a788: stp             fp, lr, [SP, #-0x10]!
    //     0x82a78c: mov             fp, SP
    // 0x82a790: AllocStack(0x18)
    //     0x82a790: sub             SP, SP, #0x18
    // 0x82a794: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x82a794: stur            x1, [fp, #-8]
    // 0x82a798: CheckStackOverflow
    //     0x82a798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82a79c: cmp             SP, x16
    //     0x82a7a0: b.ls            #0x82a96c
    // 0x82a7a4: r0 = DateTime()
    //     0x82a7a4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x82a7a8: mov             x1, x0
    // 0x82a7ac: r0 = false
    //     0x82a7ac: add             x0, NULL, #0x30  ; false
    // 0x82a7b0: stur            x1, [fp, #-0x10]
    // 0x82a7b4: StoreField: r1->field_13 = r0
    //     0x82a7b4: stur            w0, [x1, #0x13]
    // 0x82a7b8: r0 = _getCurrentMicros()
    //     0x82a7b8: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x82a7bc: r1 = LoadInt32Instr(r0)
    //     0x82a7bc: sbfx            x1, x0, #1, #0x1f
    //     0x82a7c0: tbz             w0, #0, #0x82a7c8
    //     0x82a7c4: ldur            x1, [x0, #7]
    // 0x82a7c8: ldur            x0, [fp, #-0x10]
    // 0x82a7cc: StoreField: r0->field_7 = r1
    //     0x82a7cc: stur            x1, [x0, #7]
    // 0x82a7d0: mov             x1, x0
    // 0x82a7d4: r0 = _parts()
    //     0x82a7d4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a7d8: mov             x2, x0
    // 0x82a7dc: LoadField: r0 = r2->field_b
    //     0x82a7dc: ldur            w0, [x2, #0xb]
    // 0x82a7e0: r1 = LoadInt32Instr(r0)
    //     0x82a7e0: sbfx            x1, x0, #1, #0x1f
    // 0x82a7e4: mov             x0, x1
    // 0x82a7e8: r1 = 5
    //     0x82a7e8: movz            x1, #0x5
    // 0x82a7ec: cmp             x1, x0
    // 0x82a7f0: b.hs            #0x82a974
    // 0x82a7f4: LoadField: r0 = r2->field_23
    //     0x82a7f4: ldur            w0, [x2, #0x23]
    // 0x82a7f8: DecompressPointer r0
    //     0x82a7f8: add             x0, x0, HEAP, lsl #32
    // 0x82a7fc: ldur            x1, [fp, #-8]
    // 0x82a800: stur            x0, [fp, #-0x18]
    // 0x82a804: r0 = _parts()
    //     0x82a804: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a808: mov             x2, x0
    // 0x82a80c: LoadField: r0 = r2->field_b
    //     0x82a80c: ldur            w0, [x2, #0xb]
    // 0x82a810: r1 = LoadInt32Instr(r0)
    //     0x82a810: sbfx            x1, x0, #1, #0x1f
    // 0x82a814: mov             x0, x1
    // 0x82a818: r1 = 5
    //     0x82a818: movz            x1, #0x5
    // 0x82a81c: cmp             x1, x0
    // 0x82a820: b.hs            #0x82a978
    // 0x82a824: LoadField: r0 = r2->field_23
    //     0x82a824: ldur            w0, [x2, #0x23]
    // 0x82a828: DecompressPointer r0
    //     0x82a828: add             x0, x0, HEAP, lsl #32
    // 0x82a82c: ldur            x1, [fp, #-0x18]
    // 0x82a830: r2 = LoadInt32Instr(r1)
    //     0x82a830: sbfx            x2, x1, #1, #0x1f
    //     0x82a834: tbz             w1, #0, #0x82a83c
    //     0x82a838: ldur            x2, [x1, #7]
    // 0x82a83c: r1 = LoadInt32Instr(r0)
    //     0x82a83c: sbfx            x1, x0, #1, #0x1f
    //     0x82a840: tbz             w0, #0, #0x82a848
    //     0x82a844: ldur            x1, [x0, #7]
    // 0x82a848: cmp             x2, x1
    // 0x82a84c: b.ne            #0x82a95c
    // 0x82a850: ldur            x1, [fp, #-0x10]
    // 0x82a854: r0 = _parts()
    //     0x82a854: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a858: mov             x2, x0
    // 0x82a85c: LoadField: r0 = r2->field_b
    //     0x82a85c: ldur            w0, [x2, #0xb]
    // 0x82a860: r1 = LoadInt32Instr(r0)
    //     0x82a860: sbfx            x1, x0, #1, #0x1f
    // 0x82a864: mov             x0, x1
    // 0x82a868: r1 = 7
    //     0x82a868: movz            x1, #0x7
    // 0x82a86c: cmp             x1, x0
    // 0x82a870: b.hs            #0x82a97c
    // 0x82a874: LoadField: r0 = r2->field_2b
    //     0x82a874: ldur            w0, [x2, #0x2b]
    // 0x82a878: DecompressPointer r0
    //     0x82a878: add             x0, x0, HEAP, lsl #32
    // 0x82a87c: ldur            x1, [fp, #-8]
    // 0x82a880: stur            x0, [fp, #-0x18]
    // 0x82a884: r0 = _parts()
    //     0x82a884: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a888: mov             x2, x0
    // 0x82a88c: LoadField: r0 = r2->field_b
    //     0x82a88c: ldur            w0, [x2, #0xb]
    // 0x82a890: r1 = LoadInt32Instr(r0)
    //     0x82a890: sbfx            x1, x0, #1, #0x1f
    // 0x82a894: mov             x0, x1
    // 0x82a898: r1 = 7
    //     0x82a898: movz            x1, #0x7
    // 0x82a89c: cmp             x1, x0
    // 0x82a8a0: b.hs            #0x82a980
    // 0x82a8a4: LoadField: r0 = r2->field_2b
    //     0x82a8a4: ldur            w0, [x2, #0x2b]
    // 0x82a8a8: DecompressPointer r0
    //     0x82a8a8: add             x0, x0, HEAP, lsl #32
    // 0x82a8ac: ldur            x1, [fp, #-0x18]
    // 0x82a8b0: r2 = LoadInt32Instr(r1)
    //     0x82a8b0: sbfx            x2, x1, #1, #0x1f
    //     0x82a8b4: tbz             w1, #0, #0x82a8bc
    //     0x82a8b8: ldur            x2, [x1, #7]
    // 0x82a8bc: r1 = LoadInt32Instr(r0)
    //     0x82a8bc: sbfx            x1, x0, #1, #0x1f
    //     0x82a8c0: tbz             w0, #0, #0x82a8c8
    //     0x82a8c4: ldur            x1, [x0, #7]
    // 0x82a8c8: cmp             x2, x1
    // 0x82a8cc: b.ne            #0x82a95c
    // 0x82a8d0: ldur            x1, [fp, #-0x10]
    // 0x82a8d4: r0 = _parts()
    //     0x82a8d4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a8d8: mov             x2, x0
    // 0x82a8dc: LoadField: r0 = r2->field_b
    //     0x82a8dc: ldur            w0, [x2, #0xb]
    // 0x82a8e0: r1 = LoadInt32Instr(r0)
    //     0x82a8e0: sbfx            x1, x0, #1, #0x1f
    // 0x82a8e4: mov             x0, x1
    // 0x82a8e8: r1 = 8
    //     0x82a8e8: movz            x1, #0x8
    // 0x82a8ec: cmp             x1, x0
    // 0x82a8f0: b.hs            #0x82a984
    // 0x82a8f4: LoadField: r0 = r2->field_2f
    //     0x82a8f4: ldur            w0, [x2, #0x2f]
    // 0x82a8f8: DecompressPointer r0
    //     0x82a8f8: add             x0, x0, HEAP, lsl #32
    // 0x82a8fc: ldur            x1, [fp, #-8]
    // 0x82a900: stur            x0, [fp, #-0x10]
    // 0x82a904: r0 = _parts()
    //     0x82a904: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82a908: mov             x2, x0
    // 0x82a90c: LoadField: r3 = r2->field_b
    //     0x82a90c: ldur            w3, [x2, #0xb]
    // 0x82a910: r0 = LoadInt32Instr(r3)
    //     0x82a910: sbfx            x0, x3, #1, #0x1f
    // 0x82a914: r1 = 8
    //     0x82a914: movz            x1, #0x8
    // 0x82a918: cmp             x1, x0
    // 0x82a91c: b.hs            #0x82a988
    // 0x82a920: LoadField: r1 = r2->field_2f
    //     0x82a920: ldur            w1, [x2, #0x2f]
    // 0x82a924: DecompressPointer r1
    //     0x82a924: add             x1, x1, HEAP, lsl #32
    // 0x82a928: ldur            x2, [fp, #-0x10]
    // 0x82a92c: r3 = LoadInt32Instr(r2)
    //     0x82a92c: sbfx            x3, x2, #1, #0x1f
    //     0x82a930: tbz             w2, #0, #0x82a938
    //     0x82a934: ldur            x3, [x2, #7]
    // 0x82a938: r2 = LoadInt32Instr(r1)
    //     0x82a938: sbfx            x2, x1, #1, #0x1f
    //     0x82a93c: tbz             w1, #0, #0x82a944
    //     0x82a940: ldur            x2, [x1, #7]
    // 0x82a944: cmp             x3, x2
    // 0x82a948: r16 = true
    //     0x82a948: add             x16, NULL, #0x20  ; true
    // 0x82a94c: r17 = false
    //     0x82a94c: add             x17, NULL, #0x30  ; false
    // 0x82a950: csel            x1, x16, x17, eq
    // 0x82a954: mov             x0, x1
    // 0x82a958: b               #0x82a960
    // 0x82a95c: r0 = false
    //     0x82a95c: add             x0, NULL, #0x30  ; false
    // 0x82a960: LeaveFrame
    //     0x82a960: mov             SP, fp
    //     0x82a964: ldp             fp, lr, [SP], #0x10
    // 0x82a968: ret
    //     0x82a968: ret             
    // 0x82a96c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82a96c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82a970: b               #0x82a7a4
    // 0x82a974: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a974: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82a978: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a978: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82a97c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a97c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82a980: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a980: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82a984: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a984: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82a988: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82a988: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ DateTimeExtensions.mY(/* No info */) {
    // ** addr: 0x83a2fc, size: 0x7c
    // 0x83a2fc: EnterFrame
    //     0x83a2fc: stp             fp, lr, [SP, #-0x10]!
    //     0x83a300: mov             fp, SP
    // 0x83a304: AllocStack(0x18)
    //     0x83a304: sub             SP, SP, #0x18
    // 0x83a308: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x83a308: mov             x0, x1
    //     0x83a30c: stur            x1, [fp, #-8]
    // 0x83a310: CheckStackOverflow
    //     0x83a310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a314: cmp             SP, x16
    //     0x83a318: b.ls            #0x83a370
    // 0x83a31c: r1 = "id_ID"
    //     0x83a31c: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0x83a320: ldr             x1, [x1, #0x200]
    // 0x83a324: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x83a324: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x83a328: ldr             x2, [x2, #0xad0]
    // 0x83a32c: r0 = verifiedLocale()
    //     0x83a32c: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x83a330: stur            x0, [fp, #-0x10]
    // 0x83a334: r0 = DateFormat()
    //     0x83a334: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x83a338: mov             x3, x0
    // 0x83a33c: ldur            x0, [fp, #-0x10]
    // 0x83a340: stur            x3, [fp, #-0x18]
    // 0x83a344: StoreField: r3->field_7 = r0
    //     0x83a344: stur            w0, [x3, #7]
    // 0x83a348: mov             x1, x3
    // 0x83a34c: r2 = "MMMM yyyy"
    //     0x83a34c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e100] "MMMM yyyy"
    //     0x83a350: ldr             x2, [x2, #0x100]
    // 0x83a354: r0 = addPattern()
    //     0x83a354: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x83a358: ldur            x1, [fp, #-0x18]
    // 0x83a35c: ldur            x2, [fp, #-8]
    // 0x83a360: r0 = format()
    //     0x83a360: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x83a364: LeaveFrame
    //     0x83a364: mov             SP, fp
    //     0x83a368: ldp             fp, lr, [SP], #0x10
    // 0x83a36c: ret
    //     0x83a36c: ret             
    // 0x83a370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a374: b               #0x83a31c
  }
  static _ DateTimeExtensions.addDay(/* No info */) {
    // ** addr: 0x8aae7c, size: 0x5c
    // 0x8aae7c: EnterFrame
    //     0x8aae7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8aae80: mov             fp, SP
    // 0x8aae84: AllocStack(0x10)
    //     0x8aae84: sub             SP, SP, #0x10
    // 0x8aae88: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x8aae88: stur            x1, [fp, #-0x10]
    // 0x8aae8c: CheckStackOverflow
    //     0x8aae8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aae90: cmp             SP, x16
    //     0x8aae94: b.ls            #0x8aaed0
    // 0x8aae98: r16 = 86400000000
    //     0x8aae98: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x8aae9c: ldr             x16, [x16, #0x268]
    // 0x8aaea0: mul             x0, x2, x16
    // 0x8aaea4: stur            x0, [fp, #-8]
    // 0x8aaea8: r0 = Duration()
    //     0x8aaea8: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x8aaeac: mov             x1, x0
    // 0x8aaeb0: ldur            x0, [fp, #-8]
    // 0x8aaeb4: StoreField: r1->field_7 = r0
    //     0x8aaeb4: stur            x0, [x1, #7]
    // 0x8aaeb8: mov             x2, x1
    // 0x8aaebc: ldur            x1, [fp, #-0x10]
    // 0x8aaec0: r0 = add()
    //     0x8aaec0: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x8aaec4: LeaveFrame
    //     0x8aaec4: mov             SP, fp
    //     0x8aaec8: ldp             fp, lr, [SP], #0x10
    // 0x8aaecc: ret
    //     0x8aaecc: ret             
    // 0x8aaed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aaed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aaed4: b               #0x8aae98
  }
  static _ DateTimeExtensions.toUniqueKey(/* No info */) {
    // ** addr: 0x8ed9bc, size: 0x78
    // 0x8ed9bc: EnterFrame
    //     0x8ed9bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed9c0: mov             fp, SP
    // 0x8ed9c4: AllocStack(0x18)
    //     0x8ed9c4: sub             SP, SP, #0x18
    // 0x8ed9c8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8ed9c8: mov             x0, x1
    //     0x8ed9cc: stur            x1, [fp, #-8]
    // 0x8ed9d0: CheckStackOverflow
    //     0x8ed9d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed9d4: cmp             SP, x16
    //     0x8ed9d8: b.ls            #0x8eda2c
    // 0x8ed9dc: r1 = Null
    //     0x8ed9dc: mov             x1, NULL
    // 0x8ed9e0: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x8ed9e0: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x8ed9e4: ldr             x2, [x2, #0xad0]
    // 0x8ed9e8: r0 = verifiedLocale()
    //     0x8ed9e8: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x8ed9ec: stur            x0, [fp, #-0x10]
    // 0x8ed9f0: r0 = DateFormat()
    //     0x8ed9f0: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x8ed9f4: mov             x3, x0
    // 0x8ed9f8: ldur            x0, [fp, #-0x10]
    // 0x8ed9fc: stur            x3, [fp, #-0x18]
    // 0x8eda00: StoreField: r3->field_7 = r0
    //     0x8eda00: stur            w0, [x3, #7]
    // 0x8eda04: mov             x1, x3
    // 0x8eda08: r2 = "ddMmyyy"
    //     0x8eda08: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb00] "ddMmyyy"
    //     0x8eda0c: ldr             x2, [x2, #0xb00]
    // 0x8eda10: r0 = addPattern()
    //     0x8eda10: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x8eda14: ldur            x1, [fp, #-0x18]
    // 0x8eda18: ldur            x2, [fp, #-8]
    // 0x8eda1c: r0 = format()
    //     0x8eda1c: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x8eda20: LeaveFrame
    //     0x8eda20: mov             SP, fp
    //     0x8eda24: ldp             fp, lr, [SP], #0x10
    // 0x8eda28: ret
    //     0x8eda28: ret             
    // 0x8eda2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eda2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eda30: b               #0x8ed9dc
  }
  static _ DateTimeExtensions.humanizeWithTime(/* No info */) {
    // ** addr: 0xa359b0, size: 0x7c
    // 0xa359b0: EnterFrame
    //     0xa359b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa359b4: mov             fp, SP
    // 0xa359b8: AllocStack(0x18)
    //     0xa359b8: sub             SP, SP, #0x18
    // 0xa359bc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xa359bc: mov             x0, x1
    //     0xa359c0: stur            x1, [fp, #-8]
    // 0xa359c4: CheckStackOverflow
    //     0xa359c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa359c8: cmp             SP, x16
    //     0xa359cc: b.ls            #0xa35a24
    // 0xa359d0: r1 = "id_ID"
    //     0xa359d0: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xa359d4: ldr             x1, [x1, #0x200]
    // 0xa359d8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xa359d8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xa359dc: ldr             x2, [x2, #0xad0]
    // 0xa359e0: r0 = verifiedLocale()
    //     0xa359e0: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xa359e4: stur            x0, [fp, #-0x10]
    // 0xa359e8: r0 = DateFormat()
    //     0xa359e8: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xa359ec: mov             x3, x0
    // 0xa359f0: ldur            x0, [fp, #-0x10]
    // 0xa359f4: stur            x3, [fp, #-0x18]
    // 0xa359f8: StoreField: r3->field_7 = r0
    //     0xa359f8: stur            w0, [x3, #7]
    // 0xa359fc: mov             x1, x3
    // 0xa35a00: r2 = "EEEE, dd MMMM yyyy kk:mm WIB"
    //     0xa35a00: add             x2, PP, #0x40, lsl #12  ; [pp+0x40c70] "EEEE, dd MMMM yyyy kk:mm WIB"
    //     0xa35a04: ldr             x2, [x2, #0xc70]
    // 0xa35a08: r0 = addPattern()
    //     0xa35a08: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xa35a0c: ldur            x1, [fp, #-0x18]
    // 0xa35a10: ldur            x2, [fp, #-8]
    // 0xa35a14: r0 = format()
    //     0xa35a14: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xa35a18: LeaveFrame
    //     0xa35a18: mov             SP, fp
    //     0xa35a1c: ldp             fp, lr, [SP], #0x10
    // 0xa35a20: ret
    //     0xa35a20: ret             
    // 0xa35a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa35a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa35a28: b               #0xa359d0
  }
  static _ DateTimeExtensions.humanizeDateTimeWIB(/* No info */) {
    // ** addr: 0xaf6544, size: 0x7c
    // 0xaf6544: EnterFrame
    //     0xaf6544: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6548: mov             fp, SP
    // 0xaf654c: AllocStack(0x18)
    //     0xaf654c: sub             SP, SP, #0x18
    // 0xaf6550: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xaf6550: mov             x0, x1
    //     0xaf6554: stur            x1, [fp, #-8]
    // 0xaf6558: CheckStackOverflow
    //     0xaf6558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf655c: cmp             SP, x16
    //     0xaf6560: b.ls            #0xaf65b8
    // 0xaf6564: r1 = "id_ID"
    //     0xaf6564: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xaf6568: ldr             x1, [x1, #0x200]
    // 0xaf656c: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xaf656c: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xaf6570: ldr             x2, [x2, #0xad0]
    // 0xaf6574: r0 = verifiedLocale()
    //     0xaf6574: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xaf6578: stur            x0, [fp, #-0x10]
    // 0xaf657c: r0 = DateFormat()
    //     0xaf657c: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xaf6580: mov             x3, x0
    // 0xaf6584: ldur            x0, [fp, #-0x10]
    // 0xaf6588: stur            x3, [fp, #-0x18]
    // 0xaf658c: StoreField: r3->field_7 = r0
    //     0xaf658c: stur            w0, [x3, #7]
    // 0xaf6590: mov             x1, x3
    // 0xaf6594: r2 = "dd MMMM yyyy kk:mm WIB"
    //     0xaf6594: add             x2, PP, #0x32, lsl #12  ; [pp+0x32988] "dd MMMM yyyy kk:mm WIB"
    //     0xaf6598: ldr             x2, [x2, #0x988]
    // 0xaf659c: r0 = addPattern()
    //     0xaf659c: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xaf65a0: ldur            x1, [fp, #-0x18]
    // 0xaf65a4: ldur            x2, [fp, #-8]
    // 0xaf65a8: r0 = format()
    //     0xaf65a8: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xaf65ac: LeaveFrame
    //     0xaf65ac: mov             SP, fp
    //     0xaf65b0: ldp             fp, lr, [SP], #0x10
    // 0xaf65b4: ret
    //     0xaf65b4: ret             
    // 0xaf65b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf65b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf65bc: b               #0xaf6564
  }
  static _ DateTimeExtensions.mMMM(/* No info */) {
    // ** addr: 0xb15584, size: 0x7c
    // 0xb15584: EnterFrame
    //     0xb15584: stp             fp, lr, [SP, #-0x10]!
    //     0xb15588: mov             fp, SP
    // 0xb1558c: AllocStack(0x18)
    //     0xb1558c: sub             SP, SP, #0x18
    // 0xb15590: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb15590: mov             x0, x1
    //     0xb15594: stur            x1, [fp, #-8]
    // 0xb15598: CheckStackOverflow
    //     0xb15598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1559c: cmp             SP, x16
    //     0xb155a0: b.ls            #0xb155f8
    // 0xb155a4: r1 = "id_ID"
    //     0xb155a4: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xb155a8: ldr             x1, [x1, #0x200]
    // 0xb155ac: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb155ac: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb155b0: ldr             x2, [x2, #0xad0]
    // 0xb155b4: r0 = verifiedLocale()
    //     0xb155b4: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb155b8: stur            x0, [fp, #-0x10]
    // 0xb155bc: r0 = DateFormat()
    //     0xb155bc: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb155c0: mov             x3, x0
    // 0xb155c4: ldur            x0, [fp, #-0x10]
    // 0xb155c8: stur            x3, [fp, #-0x18]
    // 0xb155cc: StoreField: r3->field_7 = r0
    //     0xb155cc: stur            w0, [x3, #7]
    // 0xb155d0: mov             x1, x3
    // 0xb155d4: r2 = "MMMM"
    //     0xb155d4: add             x2, PP, #8, lsl #12  ; [pp+0x8ec0] "MMMM"
    //     0xb155d8: ldr             x2, [x2, #0xec0]
    // 0xb155dc: r0 = addPattern()
    //     0xb155dc: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb155e0: ldur            x1, [fp, #-0x18]
    // 0xb155e4: ldur            x2, [fp, #-8]
    // 0xb155e8: r0 = format()
    //     0xb155e8: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb155ec: LeaveFrame
    //     0xb155ec: mov             SP, fp
    //     0xb155f0: ldp             fp, lr, [SP], #0x10
    // 0xb155f4: ret
    //     0xb155f4: ret             
    // 0xb155f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb155f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb155fc: b               #0xb155a4
  }
  static _ DateTimeExtensions.humanize(/* No info */) {
    // ** addr: 0xb58638, size: 0x7c
    // 0xb58638: EnterFrame
    //     0xb58638: stp             fp, lr, [SP, #-0x10]!
    //     0xb5863c: mov             fp, SP
    // 0xb58640: AllocStack(0x18)
    //     0xb58640: sub             SP, SP, #0x18
    // 0xb58644: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb58644: mov             x0, x1
    //     0xb58648: stur            x1, [fp, #-8]
    // 0xb5864c: CheckStackOverflow
    //     0xb5864c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58650: cmp             SP, x16
    //     0xb58654: b.ls            #0xb586ac
    // 0xb58658: r1 = "id_ID"
    //     0xb58658: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xb5865c: ldr             x1, [x1, #0x200]
    // 0xb58660: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb58660: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb58664: ldr             x2, [x2, #0xad0]
    // 0xb58668: r0 = verifiedLocale()
    //     0xb58668: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb5866c: stur            x0, [fp, #-0x10]
    // 0xb58670: r0 = DateFormat()
    //     0xb58670: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb58674: mov             x3, x0
    // 0xb58678: ldur            x0, [fp, #-0x10]
    // 0xb5867c: stur            x3, [fp, #-0x18]
    // 0xb58680: StoreField: r3->field_7 = r0
    //     0xb58680: stur            w0, [x3, #7]
    // 0xb58684: mov             x1, x3
    // 0xb58688: r2 = "EEEE, dd MMMM yyyy"
    //     0xb58688: add             x2, PP, #0x27, lsl #12  ; [pp+0x27400] "EEEE, dd MMMM yyyy"
    //     0xb5868c: ldr             x2, [x2, #0x400]
    // 0xb58690: r0 = addPattern()
    //     0xb58690: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb58694: ldur            x1, [fp, #-0x18]
    // 0xb58698: ldur            x2, [fp, #-8]
    // 0xb5869c: r0 = format()
    //     0xb5869c: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb586a0: LeaveFrame
    //     0xb586a0: mov             SP, fp
    //     0xb586a4: ldp             fp, lr, [SP], #0x10
    // 0xb586a8: ret
    //     0xb586a8: ret             
    // 0xb586ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb586ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb586b0: b               #0xb58658
  }
  static _ DateTimeExtensions.humanizeShortDateTimeWIB(/* No info */) {
    // ** addr: 0xb8e358, size: 0x7c
    // 0xb8e358: EnterFrame
    //     0xb8e358: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e35c: mov             fp, SP
    // 0xb8e360: AllocStack(0x18)
    //     0xb8e360: sub             SP, SP, #0x18
    // 0xb8e364: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb8e364: mov             x0, x1
    //     0xb8e368: stur            x1, [fp, #-8]
    // 0xb8e36c: CheckStackOverflow
    //     0xb8e36c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e370: cmp             SP, x16
    //     0xb8e374: b.ls            #0xb8e3cc
    // 0xb8e378: r1 = "id_ID"
    //     0xb8e378: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xb8e37c: ldr             x1, [x1, #0x200]
    // 0xb8e380: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb8e380: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb8e384: ldr             x2, [x2, #0xad0]
    // 0xb8e388: r0 = verifiedLocale()
    //     0xb8e388: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb8e38c: stur            x0, [fp, #-0x10]
    // 0xb8e390: r0 = DateFormat()
    //     0xb8e390: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb8e394: mov             x3, x0
    // 0xb8e398: ldur            x0, [fp, #-0x10]
    // 0xb8e39c: stur            x3, [fp, #-0x18]
    // 0xb8e3a0: StoreField: r3->field_7 = r0
    //     0xb8e3a0: stur            w0, [x3, #7]
    // 0xb8e3a4: mov             x1, x3
    // 0xb8e3a8: r2 = "dd MMM yyyy kk:mm WIB"
    //     0xb8e3a8: add             x2, PP, #0x35, lsl #12  ; [pp+0x35018] "dd MMM yyyy kk:mm WIB"
    //     0xb8e3ac: ldr             x2, [x2, #0x18]
    // 0xb8e3b0: r0 = addPattern()
    //     0xb8e3b0: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb8e3b4: ldur            x1, [fp, #-0x18]
    // 0xb8e3b8: ldur            x2, [fp, #-8]
    // 0xb8e3bc: r0 = format()
    //     0xb8e3bc: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb8e3c0: LeaveFrame
    //     0xb8e3c0: mov             SP, fp
    //     0xb8e3c4: ldp             fp, lr, [SP], #0x10
    // 0xb8e3c8: ret
    //     0xb8e3c8: ret             
    // 0xb8e3cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e3cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e3d0: b               #0xb8e378
  }
  static _ DateTimeExtensions.humanizeDateTime(/* No info */) {
    // ** addr: 0xb943c0, size: 0x7c
    // 0xb943c0: EnterFrame
    //     0xb943c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb943c4: mov             fp, SP
    // 0xb943c8: AllocStack(0x18)
    //     0xb943c8: sub             SP, SP, #0x18
    // 0xb943cc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb943cc: mov             x0, x1
    //     0xb943d0: stur            x1, [fp, #-8]
    // 0xb943d4: CheckStackOverflow
    //     0xb943d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb943d8: cmp             SP, x16
    //     0xb943dc: b.ls            #0xb94434
    // 0xb943e0: r1 = "id_ID"
    //     0xb943e0: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xb943e4: ldr             x1, [x1, #0x200]
    // 0xb943e8: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xb943e8: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xb943ec: ldr             x2, [x2, #0xad0]
    // 0xb943f0: r0 = verifiedLocale()
    //     0xb943f0: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xb943f4: stur            x0, [fp, #-0x10]
    // 0xb943f8: r0 = DateFormat()
    //     0xb943f8: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xb943fc: mov             x3, x0
    // 0xb94400: ldur            x0, [fp, #-0x10]
    // 0xb94404: stur            x3, [fp, #-0x18]
    // 0xb94408: StoreField: r3->field_7 = r0
    //     0xb94408: stur            w0, [x3, #7]
    // 0xb9440c: mov             x1, x3
    // 0xb94410: r2 = "dd MMM yyyy kk:mm"
    //     0xb94410: add             x2, PP, #0x40, lsl #12  ; [pp+0x402c8] "dd MMM yyyy kk:mm"
    //     0xb94414: ldr             x2, [x2, #0x2c8]
    // 0xb94418: r0 = addPattern()
    //     0xb94418: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xb9441c: ldur            x1, [fp, #-0x18]
    // 0xb94420: ldur            x2, [fp, #-8]
    // 0xb94424: r0 = format()
    //     0xb94424: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xb94428: LeaveFrame
    //     0xb94428: mov             SP, fp
    //     0xb9442c: ldp             fp, lr, [SP], #0x10
    // 0xb94430: ret
    //     0xb94430: ret             
    // 0xb94434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94438: b               #0xb943e0
  }
}
