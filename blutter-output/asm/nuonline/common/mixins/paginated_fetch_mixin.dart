// lib: , url: package:nuonline/common/mixins/paginated_fetch_mixin.dart

// class id: 1050709, size: 0x8
class :: {
}

// class id: 1934, size: 0x34, field offset: 0x24
abstract class PaginatedFetchController<C1X0> extends FetchController<C1X0> {

  late final PagingController<int, C1X0> paging; // offset: 0x2c

  _ onFetchLoaded(/* No info */) async {
    // ** addr: 0x7d8bc4, size: 0x234
    // 0x7d8bc4: EnterFrame
    //     0x7d8bc4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d8bc8: mov             fp, SP
    // 0x7d8bcc: AllocStack(0x30)
    //     0x7d8bcc: sub             SP, SP, #0x30
    // 0x7d8bd0: SetupParameters(PaginatedFetchController<C1X0> this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7d8bd0: stur            NULL, [fp, #-8]
    //     0x7d8bd4: stur            x1, [fp, #-0x10]
    //     0x7d8bd8: mov             x16, x2
    //     0x7d8bdc: mov             x2, x1
    //     0x7d8be0: mov             x1, x16
    //     0x7d8be4: stur            x1, [fp, #-0x18]
    //     0x7d8be8: stur            x3, [fp, #-0x20]
    // 0x7d8bec: CheckStackOverflow
    //     0x7d8bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d8bf0: cmp             SP, x16
    //     0x7d8bf4: b.ls            #0x7d8df0
    // 0x7d8bf8: InitAsync() -> Future<void?>
    //     0x7d8bf8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7d8bfc: bl              #0x661298  ; InitAsyncStub
    // 0x7d8c00: ldur            x2, [fp, #-0x18]
    // 0x7d8c04: r0 = LoadClassIdInstr(r2)
    //     0x7d8c04: ldur            x0, [x2, #-1]
    //     0x7d8c08: ubfx            x0, x0, #0xc, #0x14
    // 0x7d8c0c: mov             x1, x2
    // 0x7d8c10: r0 = GDT[cid_x0 + 0xd488]()
    //     0x7d8c10: movz            x17, #0xd488
    //     0x7d8c14: add             lr, x0, x17
    //     0x7d8c18: ldr             lr, [x21, lr, lsl #3]
    //     0x7d8c1c: blr             lr
    // 0x7d8c20: tbnz            w0, #4, #0x7d8db8
    // 0x7d8c24: ldur            x1, [fp, #-0x10]
    // 0x7d8c28: r2 = LoadClassIdInstr(r1)
    //     0x7d8c28: ldur            x2, [x1, #-1]
    //     0x7d8c2c: ubfx            x2, x2, #0xc, #0x14
    // 0x7d8c30: stur            x2, [fp, #-0x28]
    // 0x7d8c34: sub             x16, x2, #0x78f
    // 0x7d8c38: cmp             x16, #2
    // 0x7d8c3c: b.ls            #0x7d8c88
    // 0x7d8c40: LoadField: r0 = r1->field_3b
    //     0x7d8c40: ldur            w0, [x1, #0x3b]
    // 0x7d8c44: DecompressPointer r0
    //     0x7d8c44: add             x0, x0, HEAP, lsl #32
    // 0x7d8c48: tbnz            w0, #4, #0x7d8c88
    // 0x7d8c4c: LoadField: r0 = r1->field_23
    //     0x7d8c4c: ldur            x0, [x1, #0x23]
    // 0x7d8c50: cmp             x0, #1
    // 0x7d8c54: b.ne            #0x7d8c88
    // 0x7d8c58: LoadField: r0 = r1->field_2b
    //     0x7d8c58: ldur            w0, [x1, #0x2b]
    // 0x7d8c5c: DecompressPointer r0
    //     0x7d8c5c: add             x0, x0, HEAP, lsl #32
    // 0x7d8c60: r16 = Sentinel
    //     0x7d8c60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d8c64: cmp             w0, w16
    // 0x7d8c68: b.ne            #0x7d8c78
    // 0x7d8c6c: r2 = paging
    //     0x7d8c6c: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7d8c70: ldr             x2, [x2, #0xf90]
    // 0x7d8c74: r0 = InitLateFinalInstanceField()
    //     0x7d8c74: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7d8c78: mov             x1, x0
    // 0x7d8c7c: ldur            x2, [fp, #-0x18]
    // 0x7d8c80: r0 = appendLastPage()
    //     0x7d8c80: bl              #0x7d94c4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::appendLastPage
    // 0x7d8c84: b               #0x7d8de8
    // 0x7d8c88: ldur            x3, [fp, #-0x18]
    // 0x7d8c8c: r0 = LoadClassIdInstr(r3)
    //     0x7d8c8c: ldur            x0, [x3, #-1]
    //     0x7d8c90: ubfx            x0, x0, #0xc, #0x14
    // 0x7d8c94: str             x3, [SP]
    // 0x7d8c98: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7d8c98: movz            x17, #0xc834
    //     0x7d8c9c: add             lr, x0, x17
    //     0x7d8ca0: ldr             lr, [x21, lr, lsl #3]
    //     0x7d8ca4: blr             lr
    // 0x7d8ca8: mov             x1, x0
    // 0x7d8cac: ldur            x0, [fp, #-0x28]
    // 0x7d8cb0: cmp             x0, #0x78f
    // 0x7d8cb4: b.ne            #0x7d8cc4
    // 0x7d8cb8: ldur            x0, [fp, #-0x10]
    // 0x7d8cbc: r2 = 10
    //     0x7d8cbc: movz            x2, #0xa
    // 0x7d8cc0: b               #0x7d8d14
    // 0x7d8cc4: cmp             x0, #0x790
    // 0x7d8cc8: b.ne            #0x7d8cd8
    // 0x7d8ccc: ldur            x0, [fp, #-0x10]
    // 0x7d8cd0: r2 = 10
    //     0x7d8cd0: movz            x2, #0xa
    // 0x7d8cd4: b               #0x7d8d14
    // 0x7d8cd8: cmp             x0, #0x791
    // 0x7d8cdc: b.ne            #0x7d8cec
    // 0x7d8ce0: ldur            x0, [fp, #-0x10]
    // 0x7d8ce4: r2 = 10
    //     0x7d8ce4: movz            x2, #0xa
    // 0x7d8ce8: b               #0x7d8d14
    // 0x7d8cec: ldur            x0, [fp, #-0x10]
    // 0x7d8cf0: LoadField: r2 = r0->field_3b
    //     0x7d8cf0: ldur            w2, [x0, #0x3b]
    // 0x7d8cf4: DecompressPointer r2
    //     0x7d8cf4: add             x2, x2, HEAP, lsl #32
    // 0x7d8cf8: tst             x2, #0x10
    // 0x7d8cfc: cset            x3, ne
    // 0x7d8d00: sub             x3, x3, #1
    // 0x7d8d04: r16 = -10
    //     0x7d8d04: movn            x16, #0x9
    // 0x7d8d08: and             x3, x3, x16
    // 0x7d8d0c: add             x3, x3, #0x14
    // 0x7d8d10: r2 = LoadInt32Instr(r3)
    //     0x7d8d10: sbfx            x2, x3, #1, #0x1f
    // 0x7d8d14: r3 = LoadInt32Instr(r1)
    //     0x7d8d14: sbfx            x3, x1, #1, #0x1f
    //     0x7d8d18: tbz             w1, #0, #0x7d8d20
    //     0x7d8d1c: ldur            x3, [x1, #7]
    // 0x7d8d20: cmp             x3, x2
    // 0x7d8d24: b.ge            #0x7d8d5c
    // 0x7d8d28: mov             x1, x0
    // 0x7d8d2c: LoadField: r0 = r1->field_2b
    //     0x7d8d2c: ldur            w0, [x1, #0x2b]
    // 0x7d8d30: DecompressPointer r0
    //     0x7d8d30: add             x0, x0, HEAP, lsl #32
    // 0x7d8d34: r16 = Sentinel
    //     0x7d8d34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d8d38: cmp             w0, w16
    // 0x7d8d3c: b.ne            #0x7d8d4c
    // 0x7d8d40: r2 = paging
    //     0x7d8d40: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7d8d44: ldr             x2, [x2, #0xf90]
    // 0x7d8d48: r0 = InitLateFinalInstanceField()
    //     0x7d8d48: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7d8d4c: mov             x1, x0
    // 0x7d8d50: ldur            x2, [fp, #-0x18]
    // 0x7d8d54: r0 = appendLastPage()
    //     0x7d8d54: bl              #0x7d94c4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::appendLastPage
    // 0x7d8d58: b               #0x7d8de8
    // 0x7d8d5c: mov             x1, x0
    // 0x7d8d60: LoadField: r0 = r1->field_2b
    //     0x7d8d60: ldur            w0, [x1, #0x2b]
    // 0x7d8d64: DecompressPointer r0
    //     0x7d8d64: add             x0, x0, HEAP, lsl #32
    // 0x7d8d68: r16 = Sentinel
    //     0x7d8d68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d8d6c: cmp             w0, w16
    // 0x7d8d70: b.ne            #0x7d8d80
    // 0x7d8d74: r2 = paging
    //     0x7d8d74: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7d8d78: ldr             x2, [x2, #0xf90]
    // 0x7d8d7c: r0 = InitLateFinalInstanceField()
    //     0x7d8d7c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7d8d80: mov             x2, x0
    // 0x7d8d84: ldur            x1, [fp, #-0x10]
    // 0x7d8d88: LoadField: r0 = r1->field_23
    //     0x7d8d88: ldur            x0, [x1, #0x23]
    // 0x7d8d8c: add             x3, x0, #1
    // 0x7d8d90: r0 = BoxInt64Instr(r3)
    //     0x7d8d90: sbfiz           x0, x3, #1, #0x1f
    //     0x7d8d94: cmp             x3, x0, asr #1
    //     0x7d8d98: b.eq            #0x7d8da4
    //     0x7d8d9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d8da0: stur            x3, [x0, #7]
    // 0x7d8da4: mov             x1, x2
    // 0x7d8da8: ldur            x2, [fp, #-0x18]
    // 0x7d8dac: mov             x3, x0
    // 0x7d8db0: r0 = appendPage()
    //     0x7d8db0: bl              #0x7d8df8  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::appendPage
    // 0x7d8db4: b               #0x7d8de8
    // 0x7d8db8: ldur            x1, [fp, #-0x10]
    // 0x7d8dbc: LoadField: r0 = r1->field_2b
    //     0x7d8dbc: ldur            w0, [x1, #0x2b]
    // 0x7d8dc0: DecompressPointer r0
    //     0x7d8dc0: add             x0, x0, HEAP, lsl #32
    // 0x7d8dc4: r16 = Sentinel
    //     0x7d8dc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d8dc8: cmp             w0, w16
    // 0x7d8dcc: b.ne            #0x7d8ddc
    // 0x7d8dd0: r2 = paging
    //     0x7d8dd0: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7d8dd4: ldr             x2, [x2, #0xf90]
    // 0x7d8dd8: r0 = InitLateFinalInstanceField()
    //     0x7d8dd8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7d8ddc: mov             x1, x0
    // 0x7d8de0: ldur            x2, [fp, #-0x18]
    // 0x7d8de4: r0 = appendLastPage()
    //     0x7d8de4: bl              #0x7d94c4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::appendLastPage
    // 0x7d8de8: r0 = Null
    //     0x7d8de8: mov             x0, NULL
    // 0x7d8dec: r0 = ReturnAsyncNotFuture()
    //     0x7d8dec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7d8df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d8df0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d8df4: b               #0x7d8bf8
  }
  PagingController<int, C1X0> paging(PaginatedFetchController<C1X0>) {
    // ** addr: 0x7d953c, size: 0x74
    // 0x7d953c: EnterFrame
    //     0x7d953c: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9540: mov             fp, SP
    // 0x7d9544: AllocStack(0x10)
    //     0x7d9544: sub             SP, SP, #0x10
    // 0x7d9548: CheckStackOverflow
    //     0x7d9548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d954c: cmp             SP, x16
    //     0x7d9550: b.ls            #0x7d95a8
    // 0x7d9554: ldr             x0, [fp, #0x10]
    // 0x7d9558: LoadField: r4 = r0->field_23
    //     0x7d9558: ldur            x4, [x0, #0x23]
    // 0x7d955c: stur            x4, [fp, #-8]
    // 0x7d9560: LoadField: r2 = r0->field_1f
    //     0x7d9560: ldur            w2, [x0, #0x1f]
    // 0x7d9564: DecompressPointer r2
    //     0x7d9564: add             x2, x2, HEAP, lsl #32
    // 0x7d9568: r1 = Null
    //     0x7d9568: mov             x1, NULL
    // 0x7d956c: r3 = <PagingState<int, C1X0>, int, C1X0>
    //     0x7d956c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b008] TypeArguments: <PagingState<int, C1X0>, int, C1X0>
    //     0x7d9570: ldr             x3, [x3, #8]
    // 0x7d9574: r30 = InstantiateTypeArgumentsStub
    //     0x7d9574: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7d9578: LoadField: r30 = r30->field_7
    //     0x7d9578: ldur            lr, [lr, #7]
    // 0x7d957c: blr             lr
    // 0x7d9580: mov             x1, x0
    // 0x7d9584: r0 = PagingController()
    //     0x7d9584: bl              #0x7d97cc  ; AllocatePagingControllerStub -> PagingController<C1X0, C1X1> (size=0x40)
    // 0x7d9588: mov             x1, x0
    // 0x7d958c: ldur            x2, [fp, #-8]
    // 0x7d9590: stur            x0, [fp, #-0x10]
    // 0x7d9594: r0 = PagingController()
    //     0x7d9594: bl              #0x7d95b0  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::PagingController
    // 0x7d9598: ldur            x0, [fp, #-0x10]
    // 0x7d959c: LeaveFrame
    //     0x7d959c: mov             SP, fp
    //     0x7d95a0: ldp             fp, lr, [SP], #0x10
    // 0x7d95a4: ret
    //     0x7d95a4: ret             
    // 0x7d95a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d95a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d95ac: b               #0x7d9554
  }
  _ onFetchFailure(/* No info */) async {
    // ** addr: 0x7e254c, size: 0x118
    // 0x7e254c: EnterFrame
    //     0x7e254c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e2550: mov             fp, SP
    // 0x7e2554: AllocStack(0x20)
    //     0x7e2554: sub             SP, SP, #0x20
    // 0x7e2558: SetupParameters(PaginatedFetchController<C1X0> this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r2, fp-0x20 */)
    //     0x7e2558: stur            NULL, [fp, #-8]
    //     0x7e255c: stur            x1, [fp, #-0x10]
    //     0x7e2560: mov             x16, x2
    //     0x7e2564: mov             x2, x1
    //     0x7e2568: mov             x1, x16
    //     0x7e256c: mov             x16, x3
    //     0x7e2570: mov             x3, x2
    //     0x7e2574: mov             x2, x16
    //     0x7e2578: stur            x1, [fp, #-0x18]
    //     0x7e257c: stur            x2, [fp, #-0x20]
    // 0x7e2580: CheckStackOverflow
    //     0x7e2580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e2584: cmp             SP, x16
    //     0x7e2588: b.ls            #0x7e265c
    // 0x7e258c: InitAsync() -> Future<void?>
    //     0x7e258c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7e2590: bl              #0x661298  ; InitAsyncStub
    // 0x7e2594: ldur            x0, [fp, #-0x10]
    // 0x7e2598: r1 = LoadClassIdInstr(r0)
    //     0x7e2598: ldur            x1, [x0, #-1]
    //     0x7e259c: ubfx            x1, x1, #0xc, #0x14
    // 0x7e25a0: sub             x16, x1, #0x78f
    // 0x7e25a4: cmp             x16, #2
    // 0x7e25a8: b.hi            #0x7e25e0
    // 0x7e25ac: mov             x1, x0
    // 0x7e25b0: LoadField: r0 = r1->field_2b
    //     0x7e25b0: ldur            w0, [x1, #0x2b]
    // 0x7e25b4: DecompressPointer r0
    //     0x7e25b4: add             x0, x0, HEAP, lsl #32
    // 0x7e25b8: r16 = Sentinel
    //     0x7e25b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7e25bc: cmp             w0, w16
    // 0x7e25c0: b.ne            #0x7e25d0
    // 0x7e25c4: r2 = paging
    //     0x7e25c4: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7e25c8: ldr             x2, [x2, #0xf90]
    // 0x7e25cc: r0 = InitLateFinalInstanceField()
    //     0x7e25cc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7e25d0: mov             x1, x0
    // 0x7e25d4: ldur            x2, [fp, #-0x20]
    // 0x7e25d8: r0 = error=()
    //     0x7e25d8: bl              #0x7e2664  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::error=
    // 0x7e25dc: b               #0x7e2654
    // 0x7e25e0: mov             x1, x0
    // 0x7e25e4: LoadField: r0 = r1->field_2b
    //     0x7e25e4: ldur            w0, [x1, #0x2b]
    // 0x7e25e8: DecompressPointer r0
    //     0x7e25e8: add             x0, x0, HEAP, lsl #32
    // 0x7e25ec: r16 = Sentinel
    //     0x7e25ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7e25f0: cmp             w0, w16
    // 0x7e25f4: b.ne            #0x7e2604
    // 0x7e25f8: r2 = paging
    //     0x7e25f8: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x7e25fc: ldr             x2, [x2, #0xf90]
    // 0x7e2600: r0 = InitLateFinalInstanceField()
    //     0x7e2600: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x7e2604: mov             x4, x0
    // 0x7e2608: ldur            x0, [fp, #-0x10]
    // 0x7e260c: stur            x4, [fp, #-0x18]
    // 0x7e2610: LoadField: r2 = r0->field_1f
    //     0x7e2610: ldur            w2, [x0, #0x1f]
    // 0x7e2614: DecompressPointer r2
    //     0x7e2614: add             x2, x2, HEAP, lsl #32
    // 0x7e2618: r1 = Null
    //     0x7e2618: mov             x1, NULL
    // 0x7e261c: r3 = <C1X0>
    //     0x7e261c: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x7e2620: ldr             x3, [x3, #0xf98]
    // 0x7e2624: r0 = Null
    //     0x7e2624: mov             x0, NULL
    // 0x7e2628: cmp             x2, x0
    // 0x7e262c: b.eq            #0x7e263c
    // 0x7e2630: r30 = InstantiateTypeArgumentsStub
    //     0x7e2630: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7e2634: LoadField: r30 = r30->field_7
    //     0x7e2634: ldur            lr, [lr, #7]
    // 0x7e2638: blr             lr
    // 0x7e263c: mov             x1, x0
    // 0x7e2640: r2 = 0
    //     0x7e2640: movz            x2, #0
    // 0x7e2644: r0 = _GrowableList()
    //     0x7e2644: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e2648: ldur            x1, [fp, #-0x18]
    // 0x7e264c: mov             x2, x0
    // 0x7e2650: r0 = appendLastPage()
    //     0x7e2650: bl              #0x7d94c4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::appendLastPage
    // 0x7e2654: r0 = Null
    //     0x7e2654: mov             x0, NULL
    // 0x7e2658: r0 = ReturnAsyncNotFuture()
    //     0x7e2658: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e265c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e265c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e2660: b               #0x7e258c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8f3db0, size: 0xe4
    // 0x8f3db0: EnterFrame
    //     0x8f3db0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f3db4: mov             fp, SP
    // 0x8f3db8: AllocStack(0x30)
    //     0x8f3db8: sub             SP, SP, #0x30
    // 0x8f3dbc: SetupParameters(PaginatedFetchController<C1X0> this /* r1 => r1, fp-0x8 */)
    //     0x8f3dbc: stur            x1, [fp, #-8]
    // 0x8f3dc0: CheckStackOverflow
    //     0x8f3dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f3dc4: cmp             SP, x16
    //     0x8f3dc8: b.ls            #0x8f3e8c
    // 0x8f3dcc: r1 = 1
    //     0x8f3dcc: movz            x1, #0x1
    // 0x8f3dd0: r0 = AllocateContext()
    //     0x8f3dd0: bl              #0xec126c  ; AllocateContextStub
    // 0x8f3dd4: mov             x2, x0
    // 0x8f3dd8: ldur            x0, [fp, #-8]
    // 0x8f3ddc: stur            x2, [fp, #-0x10]
    // 0x8f3de0: StoreField: r2->field_f = r0
    //     0x8f3de0: stur            w0, [x2, #0xf]
    // 0x8f3de4: mov             x1, x0
    // 0x8f3de8: r0 = onInit()
    //     0x8f3de8: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8f3dec: ldur            x1, [fp, #-8]
    // 0x8f3df0: LoadField: r0 = r1->field_2b
    //     0x8f3df0: ldur            w0, [x1, #0x2b]
    // 0x8f3df4: DecompressPointer r0
    //     0x8f3df4: add             x0, x0, HEAP, lsl #32
    // 0x8f3df8: r16 = Sentinel
    //     0x8f3df8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f3dfc: cmp             w0, w16
    // 0x8f3e00: b.ne            #0x8f3e10
    // 0x8f3e04: r2 = paging
    //     0x8f3e04: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x8f3e08: ldr             x2, [x2, #0xf90]
    // 0x8f3e0c: r0 = InitLateFinalInstanceField()
    //     0x8f3e0c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x8f3e10: ldur            x2, [fp, #-0x10]
    // 0x8f3e14: r1 = Function '<anonymous closure>':.
    //     0x8f3e14: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ef0] AnonymousClosure: (0x8f439c), in [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onInit (0x8f3db0)
    //     0x8f3e18: ldr             x1, [x1, #0xef0]
    // 0x8f3e1c: stur            x0, [fp, #-0x18]
    // 0x8f3e20: r0 = AllocateClosure()
    //     0x8f3e20: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f3e24: ldur            x1, [fp, #-0x18]
    // 0x8f3e28: mov             x2, x0
    // 0x8f3e2c: r0 = addPageRequestListener()
    //     0x8f3e2c: bl              #0x8f4114  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::addPageRequestListener
    // 0x8f3e30: ldur            x0, [fp, #-8]
    // 0x8f3e34: r1 = LoadClassIdInstr(r0)
    //     0x8f3e34: ldur            x1, [x0, #-1]
    //     0x8f3e38: ubfx            x1, x1, #0xc, #0x14
    // 0x8f3e3c: sub             x16, x1, #0x791
    // 0x8f3e40: cmp             x16, #1
    // 0x8f3e44: b.ls            #0x8f3e7c
    // 0x8f3e48: LoadField: r3 = r0->field_2f
    //     0x8f3e48: ldur            w3, [x0, #0x2f]
    // 0x8f3e4c: DecompressPointer r3
    //     0x8f3e4c: add             x3, x3, HEAP, lsl #32
    // 0x8f3e50: ldur            x2, [fp, #-0x10]
    // 0x8f3e54: stur            x3, [fp, #-0x18]
    // 0x8f3e58: r1 = Function '<anonymous closure>':.
    //     0x8f3e58: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ef8] AnonymousClosure: (0x8f4288), in [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onInit (0x8f3db0)
    //     0x8f3e5c: ldr             x1, [x1, #0xef8]
    // 0x8f3e60: r0 = AllocateClosure()
    //     0x8f3e60: bl              #0xec1630  ; AllocateClosureStub
    // 0x8f3e64: r16 = <String>
    //     0x8f3e64: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f3e68: ldur            lr, [fp, #-0x18]
    // 0x8f3e6c: stp             lr, x16, [SP, #8]
    // 0x8f3e70: str             x0, [SP]
    // 0x8f3e74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f3e74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f3e78: r0 = debounce()
    //     0x8f3e78: bl              #0x8f3e94  ; [package:get/get_rx/src/rx_workers/rx_workers.dart] ::debounce
    // 0x8f3e7c: r0 = Null
    //     0x8f3e7c: mov             x0, NULL
    // 0x8f3e80: LeaveFrame
    //     0x8f3e80: mov             SP, fp
    //     0x8f3e84: ldp             fp, lr, [SP], #0x10
    // 0x8f3e88: ret
    //     0x8f3e88: ret             
    // 0x8f3e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3e8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3e90: b               #0x8f3dcc
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x8f4288, size: 0x6c
    // 0x8f4288: EnterFrame
    //     0x8f4288: stp             fp, lr, [SP, #-0x10]!
    //     0x8f428c: mov             fp, SP
    // 0x8f4290: ldr             x0, [fp, #0x18]
    // 0x8f4294: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f4294: ldur            w1, [x0, #0x17]
    // 0x8f4298: DecompressPointer r1
    //     0x8f4298: add             x1, x1, HEAP, lsl #32
    // 0x8f429c: CheckStackOverflow
    //     0x8f429c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f42a0: cmp             SP, x16
    //     0x8f42a4: b.ls            #0x8f42ec
    // 0x8f42a8: LoadField: r0 = r1->field_f
    //     0x8f42a8: ldur            w0, [x1, #0xf]
    // 0x8f42ac: DecompressPointer r0
    //     0x8f42ac: add             x0, x0, HEAP, lsl #32
    // 0x8f42b0: mov             x1, x0
    // 0x8f42b4: LoadField: r0 = r1->field_2b
    //     0x8f42b4: ldur            w0, [x1, #0x2b]
    // 0x8f42b8: DecompressPointer r0
    //     0x8f42b8: add             x0, x0, HEAP, lsl #32
    // 0x8f42bc: r16 = Sentinel
    //     0x8f42bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f42c0: cmp             w0, w16
    // 0x8f42c4: b.ne            #0x8f42d4
    // 0x8f42c8: r2 = paging
    //     0x8f42c8: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x8f42cc: ldr             x2, [x2, #0xf90]
    // 0x8f42d0: r0 = InitLateFinalInstanceField()
    //     0x8f42d0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x8f42d4: mov             x1, x0
    // 0x8f42d8: r0 = refresh()
    //     0x8f42d8: bl              #0x8f42f4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::refresh
    // 0x8f42dc: r0 = Null
    //     0x8f42dc: mov             x0, NULL
    // 0x8f42e0: LeaveFrame
    //     0x8f42e0: mov             SP, fp
    //     0x8f42e4: ldp             fp, lr, [SP], #0x10
    // 0x8f42e8: ret
    //     0x8f42e8: ret             
    // 0x8f42ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f42ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f42f0: b               #0x8f42a8
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x8f439c, size: 0x5c
    // 0x8f439c: EnterFrame
    //     0x8f439c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f43a0: mov             fp, SP
    // 0x8f43a4: ldr             x0, [fp, #0x18]
    // 0x8f43a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8f43a8: ldur            w1, [x0, #0x17]
    // 0x8f43ac: DecompressPointer r1
    //     0x8f43ac: add             x1, x1, HEAP, lsl #32
    // 0x8f43b0: CheckStackOverflow
    //     0x8f43b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f43b4: cmp             SP, x16
    //     0x8f43b8: b.ls            #0x8f43f0
    // 0x8f43bc: LoadField: r0 = r1->field_f
    //     0x8f43bc: ldur            w0, [x1, #0xf]
    // 0x8f43c0: DecompressPointer r0
    //     0x8f43c0: add             x0, x0, HEAP, lsl #32
    // 0x8f43c4: ldr             x1, [fp, #0x10]
    // 0x8f43c8: r2 = LoadInt32Instr(r1)
    //     0x8f43c8: sbfx            x2, x1, #1, #0x1f
    //     0x8f43cc: tbz             w1, #0, #0x8f43d4
    //     0x8f43d0: ldur            x2, [x1, #7]
    // 0x8f43d4: StoreField: r0->field_23 = r2
    //     0x8f43d4: stur            x2, [x0, #0x23]
    // 0x8f43d8: mov             x1, x0
    // 0x8f43dc: r0 = fetch()
    //     0x8f43dc: bl              #0x8f43f8  ; [package:nuonline/common/mixins/fetch_mixin.dart] _FetchController&GetxController&FetchMixin::fetch
    // 0x8f43e0: r0 = Null
    //     0x8f43e0: mov             x0, NULL
    // 0x8f43e4: LeaveFrame
    //     0x8f43e4: mov             SP, fp
    //     0x8f43e8: ldp             fp, lr, [SP], #0x10
    // 0x8f43ec: ret
    //     0x8f43ec: ret             
    // 0x8f43f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f43f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f43f4: b               #0x8f43bc
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927850, size: 0x54
    // 0x927850: EnterFrame
    //     0x927850: stp             fp, lr, [SP, #-0x10]!
    //     0x927854: mov             fp, SP
    // 0x927858: CheckStackOverflow
    //     0x927858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92785c: cmp             SP, x16
    //     0x927860: b.ls            #0x92789c
    // 0x927864: LoadField: r0 = r1->field_2b
    //     0x927864: ldur            w0, [x1, #0x2b]
    // 0x927868: DecompressPointer r0
    //     0x927868: add             x0, x0, HEAP, lsl #32
    // 0x92786c: r16 = Sentinel
    //     0x92786c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x927870: cmp             w0, w16
    // 0x927874: b.ne            #0x927884
    // 0x927878: r2 = paging
    //     0x927878: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0x92787c: ldr             x2, [x2, #0xf90]
    // 0x927880: r0 = InitLateFinalInstanceField()
    //     0x927880: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x927884: mov             x1, x0
    // 0x927888: r0 = dispose()
    //     0x927888: bl              #0xa871bc  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::dispose
    // 0x92788c: r0 = Null
    //     0x92788c: mov             x0, NULL
    // 0x927890: LeaveFrame
    //     0x927890: mov             SP, fp
    //     0x927894: ldp             fp, lr, [SP], #0x10
    // 0x927898: ret
    //     0x927898: ret             
    // 0x92789c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92789c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9278a0: b               #0x927864
  }
  _ paginate(/* No info */) {
    // ** addr: 0xadfa70, size: 0x3ec
    // 0xadfa70: EnterFrame
    //     0xadfa70: stp             fp, lr, [SP, #-0x10]!
    //     0xadfa74: mov             fp, SP
    // 0xadfa78: AllocStack(0x58)
    //     0xadfa78: sub             SP, SP, #0x58
    // 0xadfa7c: SetupParameters(PaginatedFetchController<C1X0> this /* r1 => r1, fp-0x30 */, dynamic _ /* r2 => r2, fp-0x38 */, dynamic _ /* r3 => r3, fp-0x40 */, {dynamic empty = Null /* r6, fp-0x28 */, dynamic firstPageLoading = Null /* r7, fp-0x20 */, dynamic padding = Null /* r8, fp-0x18 */, dynamic physics = Null /* r9, fp-0x10 */, dynamic shrinkWrap = false /* r0, fp-0x8 */})
    //     0xadfa7c: stur            x1, [fp, #-0x30]
    //     0xadfa80: stur            x2, [fp, #-0x38]
    //     0xadfa84: stur            x3, [fp, #-0x40]
    //     0xadfa88: ldur            w0, [x4, #0x13]
    //     0xadfa8c: ldur            w5, [x4, #0x1f]
    //     0xadfa90: add             x5, x5, HEAP, lsl #32
    //     0xadfa94: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af80] "empty"
    //     0xadfa98: ldr             x16, [x16, #0xf80]
    //     0xadfa9c: cmp             w5, w16
    //     0xadfaa0: b.ne            #0xadfac4
    //     0xadfaa4: ldur            w5, [x4, #0x23]
    //     0xadfaa8: add             x5, x5, HEAP, lsl #32
    //     0xadfaac: sub             w6, w0, w5
    //     0xadfab0: add             x5, fp, w6, sxtw #2
    //     0xadfab4: ldr             x5, [x5, #8]
    //     0xadfab8: mov             x6, x5
    //     0xadfabc: movz            x5, #0x1
    //     0xadfac0: b               #0xadfacc
    //     0xadfac4: mov             x6, NULL
    //     0xadfac8: movz            x5, #0
    //     0xadfacc: stur            x6, [fp, #-0x28]
    //     0xadfad0: lsl             x7, x5, #1
    //     0xadfad4: lsl             w8, w7, #1
    //     0xadfad8: add             w9, w8, #8
    //     0xadfadc: add             x16, x4, w9, sxtw #1
    //     0xadfae0: ldur            w10, [x16, #0xf]
    //     0xadfae4: add             x10, x10, HEAP, lsl #32
    //     0xadfae8: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2af88] "firstPageLoading"
    //     0xadfaec: ldr             x16, [x16, #0xf88]
    //     0xadfaf0: cmp             w10, w16
    //     0xadfaf4: b.ne            #0xadfb28
    //     0xadfaf8: add             w5, w8, #0xa
    //     0xadfafc: add             x16, x4, w5, sxtw #1
    //     0xadfb00: ldur            w8, [x16, #0xf]
    //     0xadfb04: add             x8, x8, HEAP, lsl #32
    //     0xadfb08: sub             w5, w0, w8
    //     0xadfb0c: add             x8, fp, w5, sxtw #2
    //     0xadfb10: ldr             x8, [x8, #8]
    //     0xadfb14: add             w5, w7, #2
    //     0xadfb18: sbfx            x7, x5, #1, #0x1f
    //     0xadfb1c: mov             x5, x7
    //     0xadfb20: mov             x7, x8
    //     0xadfb24: b               #0xadfb2c
    //     0xadfb28: mov             x7, NULL
    //     0xadfb2c: stur            x7, [fp, #-0x20]
    //     0xadfb30: lsl             x8, x5, #1
    //     0xadfb34: lsl             w9, w8, #1
    //     0xadfb38: add             w10, w9, #8
    //     0xadfb3c: add             x16, x4, w10, sxtw #1
    //     0xadfb40: ldur            w11, [x16, #0xf]
    //     0xadfb44: add             x11, x11, HEAP, lsl #32
    //     0xadfb48: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d88] "padding"
    //     0xadfb4c: ldr             x16, [x16, #0xd88]
    //     0xadfb50: cmp             w11, w16
    //     0xadfb54: b.ne            #0xadfb88
    //     0xadfb58: add             w5, w9, #0xa
    //     0xadfb5c: add             x16, x4, w5, sxtw #1
    //     0xadfb60: ldur            w9, [x16, #0xf]
    //     0xadfb64: add             x9, x9, HEAP, lsl #32
    //     0xadfb68: sub             w5, w0, w9
    //     0xadfb6c: add             x9, fp, w5, sxtw #2
    //     0xadfb70: ldr             x9, [x9, #8]
    //     0xadfb74: add             w5, w8, #2
    //     0xadfb78: sbfx            x8, x5, #1, #0x1f
    //     0xadfb7c: mov             x5, x8
    //     0xadfb80: mov             x8, x9
    //     0xadfb84: b               #0xadfb8c
    //     0xadfb88: mov             x8, NULL
    //     0xadfb8c: stur            x8, [fp, #-0x18]
    //     0xadfb90: lsl             x9, x5, #1
    //     0xadfb94: lsl             w10, w9, #1
    //     0xadfb98: add             w11, w10, #8
    //     0xadfb9c: add             x16, x4, w11, sxtw #1
    //     0xadfba0: ldur            w12, [x16, #0xf]
    //     0xadfba4: add             x12, x12, HEAP, lsl #32
    //     0xadfba8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ee8] "physics"
    //     0xadfbac: ldr             x16, [x16, #0xee8]
    //     0xadfbb0: cmp             w12, w16
    //     0xadfbb4: b.ne            #0xadfbe8
    //     0xadfbb8: add             w5, w10, #0xa
    //     0xadfbbc: add             x16, x4, w5, sxtw #1
    //     0xadfbc0: ldur            w10, [x16, #0xf]
    //     0xadfbc4: add             x10, x10, HEAP, lsl #32
    //     0xadfbc8: sub             w5, w0, w10
    //     0xadfbcc: add             x10, fp, w5, sxtw #2
    //     0xadfbd0: ldr             x10, [x10, #8]
    //     0xadfbd4: add             w5, w9, #2
    //     0xadfbd8: sbfx            x9, x5, #1, #0x1f
    //     0xadfbdc: mov             x5, x9
    //     0xadfbe0: mov             x9, x10
    //     0xadfbe4: b               #0xadfbec
    //     0xadfbe8: mov             x9, NULL
    //     0xadfbec: stur            x9, [fp, #-0x10]
    //     0xadfbf0: lsl             x10, x5, #1
    //     0xadfbf4: lsl             w5, w10, #1
    //     0xadfbf8: add             w10, w5, #8
    //     0xadfbfc: add             x16, x4, w10, sxtw #1
    //     0xadfc00: ldur            w11, [x16, #0xf]
    //     0xadfc04: add             x11, x11, HEAP, lsl #32
    //     0xadfc08: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ef0] "shrinkWrap"
    //     0xadfc0c: ldr             x16, [x16, #0xef0]
    //     0xadfc10: cmp             w11, w16
    //     0xadfc14: b.ne            #0xadfc38
    //     0xadfc18: add             w10, w5, #0xa
    //     0xadfc1c: add             x16, x4, w10, sxtw #1
    //     0xadfc20: ldur            w5, [x16, #0xf]
    //     0xadfc24: add             x5, x5, HEAP, lsl #32
    //     0xadfc28: sub             w4, w0, w5
    //     0xadfc2c: add             x0, fp, w4, sxtw #2
    //     0xadfc30: ldr             x0, [x0, #8]
    //     0xadfc34: b               #0xadfc3c
    //     0xadfc38: add             x0, NULL, #0x30  ; false
    //     0xadfc3c: stur            x0, [fp, #-8]
    // 0xadfc40: CheckStackOverflow
    //     0xadfc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfc44: cmp             SP, x16
    //     0xadfc48: b.ls            #0xadfe54
    // 0xadfc4c: r1 = 1
    //     0xadfc4c: movz            x1, #0x1
    // 0xadfc50: r0 = AllocateContext()
    //     0xadfc50: bl              #0xec126c  ; AllocateContextStub
    // 0xadfc54: mov             x2, x0
    // 0xadfc58: ldur            x0, [fp, #-0x40]
    // 0xadfc5c: stur            x2, [fp, #-0x48]
    // 0xadfc60: StoreField: r2->field_f = r0
    //     0xadfc60: stur            w0, [x2, #0xf]
    // 0xadfc64: ldur            x1, [fp, #-0x30]
    // 0xadfc68: LoadField: r0 = r1->field_2b
    //     0xadfc68: ldur            w0, [x1, #0x2b]
    // 0xadfc6c: DecompressPointer r0
    //     0xadfc6c: add             x0, x0, HEAP, lsl #32
    // 0xadfc70: r16 = Sentinel
    //     0xadfc70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadfc74: cmp             w0, w16
    // 0xadfc78: b.ne            #0xadfc88
    // 0xadfc7c: r2 = paging
    //     0xadfc7c: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0xadfc80: ldr             x2, [x2, #0xf90]
    // 0xadfc84: r0 = InitLateFinalInstanceField()
    //     0xadfc84: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xadfc88: mov             x4, x0
    // 0xadfc8c: ldur            x0, [fp, #-0x30]
    // 0xadfc90: stur            x4, [fp, #-0x50]
    // 0xadfc94: LoadField: r5 = r0->field_1f
    //     0xadfc94: ldur            w5, [x0, #0x1f]
    // 0xadfc98: DecompressPointer r5
    //     0xadfc98: add             x5, x5, HEAP, lsl #32
    // 0xadfc9c: mov             x2, x5
    // 0xadfca0: stur            x5, [fp, #-0x40]
    // 0xadfca4: r1 = Null
    //     0xadfca4: mov             x1, NULL
    // 0xadfca8: r3 = <C1X0>
    //     0xadfca8: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0xadfcac: ldr             x3, [x3, #0xf98]
    // 0xadfcb0: r0 = Null
    //     0xadfcb0: mov             x0, NULL
    // 0xadfcb4: cmp             x2, x0
    // 0xadfcb8: b.eq            #0xadfcc8
    // 0xadfcbc: r30 = InstantiateTypeArgumentsStub
    //     0xadfcbc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xadfcc0: LoadField: r30 = r30->field_7
    //     0xadfcc0: ldur            lr, [lr, #7]
    // 0xadfcc4: blr             lr
    // 0xadfcc8: mov             x1, x0
    // 0xadfccc: r0 = PagedChildBuilderDelegate()
    //     0xadfccc: bl              #0xadfe68  ; AllocatePagedChildBuilderDelegateStub -> PagedChildBuilderDelegate<X0> (size=0x2c)
    // 0xadfcd0: mov             x4, x0
    // 0xadfcd4: ldur            x0, [fp, #-0x38]
    // 0xadfcd8: stur            x4, [fp, #-0x58]
    // 0xadfcdc: StoreField: r4->field_b = r0
    //     0xadfcdc: stur            w0, [x4, #0xb]
    // 0xadfce0: ldur            x0, [fp, #-0x28]
    // 0xadfce4: StoreField: r4->field_f = r0
    //     0xadfce4: stur            w0, [x4, #0xf]
    // 0xadfce8: StoreField: r4->field_13 = r0
    //     0xadfce8: stur            w0, [x4, #0x13]
    // 0xadfcec: ldur            x1, [fp, #-0x20]
    // 0xadfcf0: ArrayStore: r4[0] = r1  ; List_4
    //     0xadfcf0: stur            w1, [x4, #0x17]
    // 0xadfcf4: StoreField: r4->field_1f = r0
    //     0xadfcf4: stur            w0, [x4, #0x1f]
    // 0xadfcf8: r0 = false
    //     0xadfcf8: add             x0, NULL, #0x30  ; false
    // 0xadfcfc: StoreField: r4->field_27 = r0
    //     0xadfcfc: stur            w0, [x4, #0x27]
    // 0xadfd00: ldur            x2, [fp, #-0x40]
    // 0xadfd04: r1 = Null
    //     0xadfd04: mov             x1, NULL
    // 0xadfd08: r3 = <int, C1X0>
    //     0xadfd08: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2afa0] TypeArguments: <int, C1X0>
    //     0xadfd0c: ldr             x3, [x3, #0xfa0]
    // 0xadfd10: r30 = InstantiateTypeArgumentsStub
    //     0xadfd10: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xadfd14: LoadField: r30 = r30->field_7
    //     0xadfd14: ldur            lr, [lr, #7]
    // 0xadfd18: blr             lr
    // 0xadfd1c: mov             x1, x0
    // 0xadfd20: r0 = PagedListView()
    //     0xadfd20: bl              #0xadfe5c  ; AllocatePagedListViewStub -> PagedListView<X0, X1> (size=0x78)
    // 0xadfd24: mov             x3, x0
    // 0xadfd28: ldur            x0, [fp, #-0x50]
    // 0xadfd2c: stur            x3, [fp, #-0x20]
    // 0xadfd30: StoreField: r3->field_57 = r0
    //     0xadfd30: stur            w0, [x3, #0x57]
    // 0xadfd34: ldur            x0, [fp, #-0x58]
    // 0xadfd38: StoreField: r3->field_5b = r0
    //     0xadfd38: stur            w0, [x3, #0x5b]
    // 0xadfd3c: r0 = true
    //     0xadfd3c: add             x0, NULL, #0x20  ; true
    // 0xadfd40: StoreField: r3->field_63 = r0
    //     0xadfd40: stur            w0, [x3, #0x63]
    // 0xadfd44: StoreField: r3->field_67 = r0
    //     0xadfd44: stur            w0, [x3, #0x67]
    // 0xadfd48: StoreField: r3->field_6b = r0
    //     0xadfd48: stur            w0, [x3, #0x6b]
    // 0xadfd4c: ldur            x0, [fp, #-8]
    // 0xadfd50: StoreField: r3->field_73 = r0
    //     0xadfd50: stur            w0, [x3, #0x73]
    // 0xadfd54: ldur            x2, [fp, #-0x48]
    // 0xadfd58: r1 = Function '<anonymous closure>':.
    //     0xadfd58: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2afa8] AnonymousClosure: (0xadff10), in [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::paginate (0xadfa70)
    //     0xadfd5c: ldr             x1, [x1, #0xfa8]
    // 0xadfd60: r0 = AllocateClosure()
    //     0xadfd60: bl              #0xec1630  ; AllocateClosureStub
    // 0xadfd64: mov             x1, x0
    // 0xadfd68: ldur            x0, [fp, #-0x20]
    // 0xadfd6c: StoreField: r0->field_5f = r1
    //     0xadfd6c: stur            w1, [x0, #0x5f]
    // 0xadfd70: ldur            x1, [fp, #-0x18]
    // 0xadfd74: StoreField: r0->field_4f = r1
    //     0xadfd74: stur            w1, [x0, #0x4f]
    // 0xadfd78: r1 = Instance_Axis
    //     0xadfd78: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xadfd7c: StoreField: r0->field_b = r1
    //     0xadfd7c: stur            w1, [x0, #0xb]
    // 0xadfd80: r1 = false
    //     0xadfd80: add             x1, NULL, #0x30  ; false
    // 0xadfd84: StoreField: r0->field_f = r1
    //     0xadfd84: stur            w1, [x0, #0xf]
    // 0xadfd88: ldur            x1, [fp, #-8]
    // 0xadfd8c: StoreField: r0->field_23 = r1
    //     0xadfd8c: stur            w1, [x0, #0x23]
    // 0xadfd90: StoreField: r0->field_2b = rZR
    //     0xadfd90: stur            xzr, [x0, #0x2b]
    // 0xadfd94: r1 = Instance_DragStartBehavior
    //     0xadfd94: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xadfd98: StoreField: r0->field_3b = r1
    //     0xadfd98: stur            w1, [x0, #0x3b]
    // 0xadfd9c: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xadfd9c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xadfda0: ldr             x1, [x1, #0xf00]
    // 0xadfda4: StoreField: r0->field_3f = r1
    //     0xadfda4: stur            w1, [x0, #0x3f]
    // 0xadfda8: r1 = Instance_Clip
    //     0xadfda8: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xadfdac: ldr             x1, [x1, #0x7c0]
    // 0xadfdb0: StoreField: r0->field_47 = r1
    //     0xadfdb0: stur            w1, [x0, #0x47]
    // 0xadfdb4: r1 = Instance_HitTestBehavior
    //     0xadfdb4: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xadfdb8: ldr             x1, [x1, #0x1c8]
    // 0xadfdbc: StoreField: r0->field_4b = r1
    //     0xadfdbc: stur            w1, [x0, #0x4b]
    // 0xadfdc0: ldur            x1, [fp, #-0x10]
    // 0xadfdc4: cmp             w1, NULL
    // 0xadfdc8: b.ne            #0xadfdd4
    // 0xadfdcc: r1 = Instance_AlwaysScrollableScrollPhysics
    //     0xadfdcc: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f08] Obj!AlwaysScrollableScrollPhysics@e0fd51
    //     0xadfdd0: ldr             x1, [x1, #0xf08]
    // 0xadfdd4: StoreField: r0->field_1b = r1
    //     0xadfdd4: stur            w1, [x0, #0x1b]
    // 0xadfdd8: r0 = RefreshIndicator()
    //     0xadfdd8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xadfddc: mov             x3, x0
    // 0xadfde0: ldur            x0, [fp, #-0x20]
    // 0xadfde4: stur            x3, [fp, #-8]
    // 0xadfde8: StoreField: r3->field_b = r0
    //     0xadfde8: stur            w0, [x3, #0xb]
    // 0xadfdec: d0 = 40.000000
    //     0xadfdec: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xadfdf0: StoreField: r3->field_f = d0
    //     0xadfdf0: stur            d0, [x3, #0xf]
    // 0xadfdf4: ArrayStore: r3[0] = rZR  ; List_8
    //     0xadfdf4: stur            xzr, [x3, #0x17]
    // 0xadfdf8: ldur            x2, [fp, #-0x30]
    // 0xadfdfc: r1 = Function 'onRefresh':.
    //     0xadfdfc: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2afb0] AnonymousClosure: (0xadfe74), in [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onRefresh (0xadfeac)
    //     0xadfe00: ldr             x1, [x1, #0xfb0]
    // 0xadfe04: r0 = AllocateClosure()
    //     0xadfe04: bl              #0xec1630  ; AllocateClosureStub
    // 0xadfe08: mov             x1, x0
    // 0xadfe0c: ldur            x0, [fp, #-8]
    // 0xadfe10: StoreField: r0->field_1f = r1
    //     0xadfe10: stur            w1, [x0, #0x1f]
    // 0xadfe14: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xadfe14: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xadfe18: ldr             x1, [x1, #0xf58]
    // 0xadfe1c: StoreField: r0->field_2f = r1
    //     0xadfe1c: stur            w1, [x0, #0x2f]
    // 0xadfe20: d0 = 2.500000
    //     0xadfe20: fmov            d0, #2.50000000
    // 0xadfe24: StoreField: r0->field_3b = d0
    //     0xadfe24: stur            d0, [x0, #0x3b]
    // 0xadfe28: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xadfe28: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xadfe2c: ldr             x1, [x1, #0xa68]
    // 0xadfe30: StoreField: r0->field_47 = r1
    //     0xadfe30: stur            w1, [x0, #0x47]
    // 0xadfe34: d0 = 2.000000
    //     0xadfe34: fmov            d0, #2.00000000
    // 0xadfe38: StoreField: r0->field_4b = d0
    //     0xadfe38: stur            d0, [x0, #0x4b]
    // 0xadfe3c: r1 = Instance__IndicatorType
    //     0xadfe3c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2afb8] Obj!_IndicatorType@e36361
    //     0xadfe40: ldr             x1, [x1, #0xfb8]
    // 0xadfe44: StoreField: r0->field_43 = r1
    //     0xadfe44: stur            w1, [x0, #0x43]
    // 0xadfe48: LeaveFrame
    //     0xadfe48: mov             SP, fp
    //     0xadfe4c: ldp             fp, lr, [SP], #0x10
    // 0xadfe50: ret
    //     0xadfe50: ret             
    // 0xadfe54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfe54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfe58: b               #0xadfc4c
  }
  [closure] Future<void> onRefresh(dynamic) {
    // ** addr: 0xadfe74, size: 0x38
    // 0xadfe74: EnterFrame
    //     0xadfe74: stp             fp, lr, [SP, #-0x10]!
    //     0xadfe78: mov             fp, SP
    // 0xadfe7c: ldr             x0, [fp, #0x10]
    // 0xadfe80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadfe80: ldur            w1, [x0, #0x17]
    // 0xadfe84: DecompressPointer r1
    //     0xadfe84: add             x1, x1, HEAP, lsl #32
    // 0xadfe88: CheckStackOverflow
    //     0xadfe88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfe8c: cmp             SP, x16
    //     0xadfe90: b.ls            #0xadfea4
    // 0xadfe94: r0 = onRefresh()
    //     0xadfe94: bl              #0xadfeac  ; [package:nuonline/common/mixins/paginated_fetch_mixin.dart] PaginatedFetchController::onRefresh
    // 0xadfe98: LeaveFrame
    //     0xadfe98: mov             SP, fp
    //     0xadfe9c: ldp             fp, lr, [SP], #0x10
    // 0xadfea0: ret
    //     0xadfea0: ret             
    // 0xadfea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfea4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfea8: b               #0xadfe94
  }
  _ onRefresh(/* No info */) async {
    // ** addr: 0xadfeac, size: 0x64
    // 0xadfeac: EnterFrame
    //     0xadfeac: stp             fp, lr, [SP, #-0x10]!
    //     0xadfeb0: mov             fp, SP
    // 0xadfeb4: AllocStack(0x10)
    //     0xadfeb4: sub             SP, SP, #0x10
    // 0xadfeb8: SetupParameters(PaginatedFetchController<C1X0> this /* r1 => r1, fp-0x10 */)
    //     0xadfeb8: stur            NULL, [fp, #-8]
    //     0xadfebc: stur            x1, [fp, #-0x10]
    // 0xadfec0: CheckStackOverflow
    //     0xadfec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfec4: cmp             SP, x16
    //     0xadfec8: b.ls            #0xadff08
    // 0xadfecc: InitAsync() -> Future<void?>
    //     0xadfecc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xadfed0: bl              #0x661298  ; InitAsyncStub
    // 0xadfed4: ldur            x1, [fp, #-0x10]
    // 0xadfed8: LoadField: r0 = r1->field_2b
    //     0xadfed8: ldur            w0, [x1, #0x2b]
    // 0xadfedc: DecompressPointer r0
    //     0xadfedc: add             x0, x0, HEAP, lsl #32
    // 0xadfee0: r16 = Sentinel
    //     0xadfee0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xadfee4: cmp             w0, w16
    // 0xadfee8: b.ne            #0xadfef8
    // 0xadfeec: r2 = paging
    //     0xadfeec: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2af90] Field <PaginatedFetchController.paging>: late final (offset: 0x2c)
    //     0xadfef0: ldr             x2, [x2, #0xf90]
    // 0xadfef4: r0 = InitLateFinalInstanceField()
    //     0xadfef4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xadfef8: mov             x1, x0
    // 0xadfefc: r0 = refresh()
    //     0xadfefc: bl              #0x8f42f4  ; [package:infinite_scroll_pagination/src/core/paging_controller.dart] PagingController::refresh
    // 0xadff00: r0 = Null
    //     0xadff00: mov             x0, NULL
    // 0xadff04: r0 = ReturnAsyncNotFuture()
    //     0xadff04: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xadff08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadff08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadff0c: b               #0xadfecc
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadff10, size: 0x18
    // 0xadff10: ldr             x1, [SP, #0x10]
    // 0xadff14: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xadff14: ldur            w2, [x1, #0x17]
    // 0xadff18: DecompressPointer r2
    //     0xadff18: add             x2, x2, HEAP, lsl #32
    // 0xadff1c: LoadField: r0 = r2->field_f
    //     0xadff1c: ldur            w0, [x2, #0xf]
    // 0xadff20: DecompressPointer r0
    //     0xadff20: add             x0, x0, HEAP, lsl #32
    // 0xadff24: ret
    //     0xadff24: ret             
  }
}
