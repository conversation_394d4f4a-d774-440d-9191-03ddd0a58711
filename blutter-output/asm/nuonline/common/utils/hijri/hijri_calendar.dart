// lib: , url: package:nuonline/common/utils/hijri/hijri_calendar.dart

// class id: 1050717, size: 0x8
class :: {
}

// class id: 1030, size: 0x20, field offset: 0x8
class NHijriCalendar extends Object {

  late int hMonth; // offset: 0x10
  late Box<HijriDate> hijriMapping; // offset: 0x18
  late int hYear; // offset: 0x14

  _ isRamadhan(/* No info */) {
    // ** addr: 0x8149f8, size: 0xec
    // 0x8149f8: EnterFrame
    //     0x8149f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8149fc: mov             fp, SP
    // 0x814a00: AllocStack(0x8)
    //     0x814a00: sub             SP, SP, #8
    // 0x814a04: SetupParameters(NHijriCalendar this /* r1 => r0, fp-0x8 */, {dynamic includeOneDayBefore = false /* r1 */})
    //     0x814a04: mov             x0, x1
    //     0x814a08: stur            x1, [fp, #-8]
    //     0x814a0c: ldur            w1, [x4, #0x13]
    //     0x814a10: ldur            w2, [x4, #0x1f]
    //     0x814a14: add             x2, x2, HEAP, lsl #32
    //     0x814a18: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2deb8] "includeOneDayBefore"
    //     0x814a1c: ldr             x16, [x16, #0xeb8]
    //     0x814a20: cmp             w2, w16
    //     0x814a24: b.ne            #0x814a40
    //     0x814a28: ldur            w2, [x4, #0x23]
    //     0x814a2c: add             x2, x2, HEAP, lsl #32
    //     0x814a30: sub             w3, w1, w2
    //     0x814a34: add             x1, fp, w3, sxtw #2
    //     0x814a38: ldr             x1, [x1, #8]
    //     0x814a3c: b               #0x814a44
    //     0x814a40: add             x1, NULL, #0x30  ; false
    // 0x814a44: CheckStackOverflow
    //     0x814a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x814a48: cmp             SP, x16
    //     0x814a4c: b.ls            #0x814ad0
    // 0x814a50: LoadField: r2 = r0->field_f
    //     0x814a50: ldur            w2, [x0, #0xf]
    // 0x814a54: DecompressPointer r2
    //     0x814a54: add             x2, x2, HEAP, lsl #32
    // 0x814a58: r16 = Sentinel
    //     0x814a58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814a5c: cmp             w2, w16
    // 0x814a60: b.eq            #0x814ad8
    // 0x814a64: r3 = LoadInt32Instr(r2)
    //     0x814a64: sbfx            x3, x2, #1, #0x1f
    //     0x814a68: tbz             w2, #0, #0x814a70
    //     0x814a6c: ldur            x3, [x2, #7]
    // 0x814a70: cmp             x3, #8
    // 0x814a74: b.ne            #0x814ab4
    // 0x814a78: tbnz            w1, #4, #0x814ab4
    // 0x814a7c: mov             x1, x0
    // 0x814a80: r0 = daysInMonth()
    //     0x814a80: bl              #0x814b04  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::daysInMonth
    // 0x814a84: mov             x1, x0
    // 0x814a88: r0 = last()
    //     0x814a88: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x814a8c: LoadField: r1 = r0->field_7
    //     0x814a8c: ldur            x1, [x0, #7]
    // 0x814a90: ldur            x2, [fp, #-8]
    // 0x814a94: LoadField: r4 = r2->field_7
    //     0x814a94: ldur            x4, [x2, #7]
    // 0x814a98: cmp             x1, x4
    // 0x814a9c: r16 = true
    //     0x814a9c: add             x16, NULL, #0x20  ; true
    // 0x814aa0: r17 = false
    //     0x814aa0: add             x17, NULL, #0x30  ; false
    // 0x814aa4: csel            x0, x16, x17, eq
    // 0x814aa8: LeaveFrame
    //     0x814aa8: mov             SP, fp
    //     0x814aac: ldp             fp, lr, [SP], #0x10
    // 0x814ab0: ret
    //     0x814ab0: ret             
    // 0x814ab4: cmp             x3, #9
    // 0x814ab8: r16 = true
    //     0x814ab8: add             x16, NULL, #0x20  ; true
    // 0x814abc: r17 = false
    //     0x814abc: add             x17, NULL, #0x30  ; false
    // 0x814ac0: csel            x0, x16, x17, eq
    // 0x814ac4: LeaveFrame
    //     0x814ac4: mov             SP, fp
    //     0x814ac8: ldp             fp, lr, [SP], #0x10
    // 0x814acc: ret
    //     0x814acc: ret             
    // 0x814ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x814ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x814ad4: b               #0x814a50
    // 0x814ad8: r9 = hMonth
    //     0x814ad8: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x814adc: ldr             x9, [x9, #0x278]
    // 0x814ae0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814ae0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ daysInMonth(/* No info */) {
    // ** addr: 0x814b04, size: 0x374
    // 0x814b04: EnterFrame
    //     0x814b04: stp             fp, lr, [SP, #-0x10]!
    //     0x814b08: mov             fp, SP
    // 0x814b0c: AllocStack(0x48)
    //     0x814b0c: sub             SP, SP, #0x48
    // 0x814b10: SetupParameters(NHijriCalendar this /* r1 => r1, fp-0x8 */)
    //     0x814b10: stur            x1, [fp, #-8]
    // 0x814b14: CheckStackOverflow
    //     0x814b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x814b18: cmp             SP, x16
    //     0x814b1c: b.ls            #0x814e30
    // 0x814b20: r1 = 1
    //     0x814b20: movz            x1, #0x1
    // 0x814b24: r0 = AllocateContext()
    //     0x814b24: bl              #0xec126c  ; AllocateContextStub
    // 0x814b28: mov             x2, x0
    // 0x814b2c: ldur            x1, [fp, #-8]
    // 0x814b30: stur            x2, [fp, #-0x10]
    // 0x814b34: StoreField: r2->field_f = r1
    //     0x814b34: stur            w1, [x2, #0xf]
    // 0x814b38: LoadField: r0 = r1->field_1b
    //     0x814b38: ldur            w0, [x1, #0x1b]
    // 0x814b3c: DecompressPointer r0
    //     0x814b3c: add             x0, x0, HEAP, lsl #32
    // 0x814b40: r3 = LoadClassIdInstr(r0)
    //     0x814b40: ldur            x3, [x0, #-1]
    //     0x814b44: ubfx            x3, x3, #0xc, #0x14
    // 0x814b48: r16 = "nu"
    //     0x814b48: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0x814b4c: ldr             x16, [x16, #0xdf8]
    // 0x814b50: stp             x16, x0, [SP]
    // 0x814b54: mov             x0, x3
    // 0x814b58: mov             lr, x0
    // 0x814b5c: ldr             lr, [x21, lr, lsl #3]
    // 0x814b60: blr             lr
    // 0x814b64: tbnz            w0, #4, #0x814c14
    // 0x814b68: ldur            x0, [fp, #-8]
    // 0x814b6c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x814b6c: ldur            w2, [x0, #0x17]
    // 0x814b70: DecompressPointer r2
    //     0x814b70: add             x2, x2, HEAP, lsl #32
    // 0x814b74: r16 = Sentinel
    //     0x814b74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814b78: cmp             w2, w16
    // 0x814b7c: b.eq            #0x814e38
    // 0x814b80: mov             x1, x2
    // 0x814b84: stur            x2, [fp, #-0x18]
    // 0x814b88: r0 = checkOpen()
    //     0x814b88: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x814b8c: ldur            x0, [fp, #-0x18]
    // 0x814b90: LoadField: r1 = r0->field_1b
    //     0x814b90: ldur            w1, [x0, #0x1b]
    // 0x814b94: DecompressPointer r1
    //     0x814b94: add             x1, x1, HEAP, lsl #32
    // 0x814b98: r16 = Sentinel
    //     0x814b98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814b9c: cmp             w1, w16
    // 0x814ba0: b.eq            #0x814e44
    // 0x814ba4: r0 = getValues()
    //     0x814ba4: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x814ba8: ldur            x2, [fp, #-0x10]
    // 0x814bac: r1 = Function '<anonymous closure>':.
    //     0x814bac: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dec0] AnonymousClosure: (0x815440), in [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::daysInMonth (0x814b04)
    //     0x814bb0: ldr             x1, [x1, #0xec0]
    // 0x814bb4: stur            x0, [fp, #-0x18]
    // 0x814bb8: r0 = AllocateClosure()
    //     0x814bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x814bbc: ldur            x1, [fp, #-0x18]
    // 0x814bc0: mov             x2, x0
    // 0x814bc4: r0 = where()
    //     0x814bc4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x814bc8: ldur            x2, [fp, #-0x10]
    // 0x814bcc: r1 = Function '<anonymous closure>':.
    //     0x814bcc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dec8] AnonymousClosure: (0x8150a4), in [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::daysInMonth (0x814b04)
    //     0x814bd0: ldr             x1, [x1, #0xec8]
    // 0x814bd4: stur            x0, [fp, #-0x10]
    // 0x814bd8: r0 = AllocateClosure()
    //     0x814bd8: bl              #0xec1630  ; AllocateClosureStub
    // 0x814bdc: r16 = <NHijriCalendar>
    //     0x814bdc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2ded0] TypeArguments: <NHijriCalendar>
    //     0x814be0: ldr             x16, [x16, #0xed0]
    // 0x814be4: ldur            lr, [fp, #-0x10]
    // 0x814be8: stp             lr, x16, [SP, #8]
    // 0x814bec: str             x0, [SP]
    // 0x814bf0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x814bf0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x814bf4: r0 = map()
    //     0x814bf4: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0x814bf8: LoadField: r1 = r0->field_7
    //     0x814bf8: ldur            w1, [x0, #7]
    // 0x814bfc: DecompressPointer r1
    //     0x814bfc: add             x1, x1, HEAP, lsl #32
    // 0x814c00: mov             x2, x0
    // 0x814c04: r0 = _GrowableList.of()
    //     0x814c04: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x814c08: LeaveFrame
    //     0x814c08: mov             SP, fp
    //     0x814c0c: ldp             fp, lr, [SP], #0x10
    // 0x814c10: ret
    //     0x814c10: ret             
    // 0x814c14: ldur            x0, [fp, #-8]
    // 0x814c18: r1 = <NHijriCalendar>
    //     0x814c18: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2ded0] TypeArguments: <NHijriCalendar>
    //     0x814c1c: ldr             x1, [x1, #0xed0]
    // 0x814c20: r2 = 0
    //     0x814c20: movz            x2, #0
    // 0x814c24: r0 = _GrowableList()
    //     0x814c24: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x814c28: stur            x0, [fp, #-0x10]
    // 0x814c2c: r2 = 1
    //     0x814c2c: movz            x2, #0x1
    // 0x814c30: ldur            x1, [fp, #-8]
    // 0x814c34: stur            x2, [fp, #-0x20]
    // 0x814c38: CheckStackOverflow
    //     0x814c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x814c3c: cmp             SP, x16
    //     0x814c40: b.ls            #0x814e4c
    // 0x814c44: cmp             x2, #0x1f
    // 0x814c48: b.ge            #0x814e1c
    // 0x814c4c: r0 = HijriCalendar()
    //     0x814c4c: bl              #0x815098  ; AllocateHijriCalendarStub -> HijriCalendar (size=0x20)
    // 0x814c50: mov             x1, x0
    // 0x814c54: r0 = 1
    //     0x814c54: movz            x0, #0x1
    // 0x814c58: StoreField: r1->field_7 = r0
    //     0x814c58: stur            x0, [x1, #7]
    // 0x814c5c: r4 = Sentinel
    //     0x814c5c: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814c60: StoreField: r1->field_f = r4
    //     0x814c60: stur            w4, [x1, #0xf]
    // 0x814c64: StoreField: r1->field_13 = r4
    //     0x814c64: stur            w4, [x1, #0x13]
    // 0x814c68: ldur            x5, [fp, #-8]
    // 0x814c6c: LoadField: r2 = r5->field_13
    //     0x814c6c: ldur            w2, [x5, #0x13]
    // 0x814c70: DecompressPointer r2
    //     0x814c70: add             x2, x2, HEAP, lsl #32
    // 0x814c74: r16 = Sentinel
    //     0x814c74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814c78: cmp             w2, w16
    // 0x814c7c: b.eq            #0x814e54
    // 0x814c80: StoreField: r1->field_13 = r2
    //     0x814c80: stur            w2, [x1, #0x13]
    // 0x814c84: LoadField: r3 = r5->field_f
    //     0x814c84: ldur            w3, [x5, #0xf]
    // 0x814c88: DecompressPointer r3
    //     0x814c88: add             x3, x3, HEAP, lsl #32
    // 0x814c8c: r16 = Sentinel
    //     0x814c8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814c90: cmp             w3, w16
    // 0x814c94: b.eq            #0x814e60
    // 0x814c98: StoreField: r1->field_f = r3
    //     0x814c98: stur            w3, [x1, #0xf]
    // 0x814c9c: ldur            x6, [fp, #-0x20]
    // 0x814ca0: StoreField: r1->field_7 = r6
    //     0x814ca0: stur            x6, [x1, #7]
    // 0x814ca4: r7 = LoadInt32Instr(r3)
    //     0x814ca4: sbfx            x7, x3, #1, #0x1f
    //     0x814ca8: tbz             w3, #0, #0x814cb0
    //     0x814cac: ldur            x7, [x3, #7]
    // 0x814cb0: cmp             x7, #1
    // 0x814cb4: b.lt            #0x814e14
    // 0x814cb8: cmp             x7, #0xc
    // 0x814cbc: b.gt            #0x814e0c
    // 0x814cc0: cmp             x6, #1
    // 0x814cc4: b.lt            #0x814e04
    // 0x814cc8: cmp             x6, #0x1e
    // 0x814ccc: b.gt            #0x814dfc
    // 0x814cd0: r3 = LoadInt32Instr(r2)
    //     0x814cd0: sbfx            x3, x2, #1, #0x1f
    //     0x814cd4: tbz             w2, #0, #0x814cdc
    //     0x814cd8: ldur            x3, [x2, #7]
    // 0x814cdc: mov             x2, x3
    // 0x814ce0: mov             x3, x7
    // 0x814ce4: r0 = getDaysInMonth()
    //     0x814ce4: bl              #0x814f90  ; [package:hijri/hijri_calendar.dart] HijriCalendar::getDaysInMonth
    // 0x814ce8: mov             x1, x0
    // 0x814cec: ldur            x0, [fp, #-0x20]
    // 0x814cf0: cmp             x0, x1
    // 0x814cf4: b.gt            #0x814df4
    // 0x814cf8: ldur            x1, [fp, #-8]
    // 0x814cfc: ldur            x2, [fp, #-0x10]
    // 0x814d00: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x814d00: ldur            w3, [x1, #0x17]
    // 0x814d04: DecompressPointer r3
    //     0x814d04: add             x3, x3, HEAP, lsl #32
    // 0x814d08: r16 = Sentinel
    //     0x814d08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814d0c: cmp             w3, w16
    // 0x814d10: b.eq            #0x814e6c
    // 0x814d14: stur            x3, [fp, #-0x18]
    // 0x814d18: r0 = NHijriCalendar()
    //     0x814d18: bl              #0x814f84  ; AllocateNHijriCalendarStub -> NHijriCalendar (size=0x20)
    // 0x814d1c: mov             x2, x0
    // 0x814d20: r0 = Sentinel
    //     0x814d20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x814d24: stur            x2, [fp, #-0x30]
    // 0x814d28: StoreField: r2->field_f = r0
    //     0x814d28: stur            w0, [x2, #0xf]
    // 0x814d2c: StoreField: r2->field_13 = r0
    //     0x814d2c: stur            w0, [x2, #0x13]
    // 0x814d30: r3 = "nu"
    //     0x814d30: add             x3, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0x814d34: ldr             x3, [x3, #0xdf8]
    // 0x814d38: StoreField: r2->field_1b = r3
    //     0x814d38: stur            w3, [x2, #0x1b]
    // 0x814d3c: ldur            x1, [fp, #-0x18]
    // 0x814d40: ArrayStore: r2[0] = r1  ; List_4
    //     0x814d40: stur            w1, [x2, #0x17]
    // 0x814d44: ldur            x4, [fp, #-8]
    // 0x814d48: LoadField: r1 = r4->field_13
    //     0x814d48: ldur            w1, [x4, #0x13]
    // 0x814d4c: DecompressPointer r1
    //     0x814d4c: add             x1, x1, HEAP, lsl #32
    // 0x814d50: LoadField: r5 = r4->field_f
    //     0x814d50: ldur            w5, [x4, #0xf]
    // 0x814d54: DecompressPointer r5
    //     0x814d54: add             x5, x5, HEAP, lsl #32
    // 0x814d58: StoreField: r2->field_13 = r1
    //     0x814d58: stur            w1, [x2, #0x13]
    // 0x814d5c: StoreField: r2->field_f = r5
    //     0x814d5c: stur            w5, [x2, #0xf]
    // 0x814d60: ldur            x5, [fp, #-0x20]
    // 0x814d64: StoreField: r2->field_7 = r5
    //     0x814d64: stur            x5, [x2, #7]
    // 0x814d68: ldur            x6, [fp, #-0x10]
    // 0x814d6c: LoadField: r1 = r6->field_b
    //     0x814d6c: ldur            w1, [x6, #0xb]
    // 0x814d70: LoadField: r7 = r6->field_f
    //     0x814d70: ldur            w7, [x6, #0xf]
    // 0x814d74: DecompressPointer r7
    //     0x814d74: add             x7, x7, HEAP, lsl #32
    // 0x814d78: LoadField: r8 = r7->field_b
    //     0x814d78: ldur            w8, [x7, #0xb]
    // 0x814d7c: r7 = LoadInt32Instr(r1)
    //     0x814d7c: sbfx            x7, x1, #1, #0x1f
    // 0x814d80: stur            x7, [fp, #-0x28]
    // 0x814d84: r1 = LoadInt32Instr(r8)
    //     0x814d84: sbfx            x1, x8, #1, #0x1f
    // 0x814d88: cmp             x7, x1
    // 0x814d8c: b.ne            #0x814d98
    // 0x814d90: mov             x1, x6
    // 0x814d94: r0 = _growToNextCapacity()
    //     0x814d94: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x814d98: ldur            x3, [fp, #-0x10]
    // 0x814d9c: ldur            x2, [fp, #-0x20]
    // 0x814da0: ldur            x4, [fp, #-0x28]
    // 0x814da4: add             x5, x4, #1
    // 0x814da8: lsl             x6, x5, #1
    // 0x814dac: StoreField: r3->field_b = r6
    //     0x814dac: stur            w6, [x3, #0xb]
    // 0x814db0: LoadField: r1 = r3->field_f
    //     0x814db0: ldur            w1, [x3, #0xf]
    // 0x814db4: DecompressPointer r1
    //     0x814db4: add             x1, x1, HEAP, lsl #32
    // 0x814db8: ldur            x0, [fp, #-0x30]
    // 0x814dbc: ArrayStore: r1[r4] = r0  ; List_4
    //     0x814dbc: add             x25, x1, x4, lsl #2
    //     0x814dc0: add             x25, x25, #0xf
    //     0x814dc4: str             w0, [x25]
    //     0x814dc8: tbz             w0, #0, #0x814de4
    //     0x814dcc: ldurb           w16, [x1, #-1]
    //     0x814dd0: ldurb           w17, [x0, #-1]
    //     0x814dd4: and             x16, x17, x16, lsr #2
    //     0x814dd8: tst             x16, HEAP, lsr #32
    //     0x814ddc: b.eq            #0x814de4
    //     0x814de0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x814de4: add             x0, x2, #1
    // 0x814de8: mov             x2, x0
    // 0x814dec: mov             x0, x3
    // 0x814df0: b               #0x814c30
    // 0x814df4: ldur            x3, [fp, #-0x10]
    // 0x814df8: b               #0x814e20
    // 0x814dfc: ldur            x3, [fp, #-0x10]
    // 0x814e00: b               #0x814e20
    // 0x814e04: ldur            x3, [fp, #-0x10]
    // 0x814e08: b               #0x814e20
    // 0x814e0c: ldur            x3, [fp, #-0x10]
    // 0x814e10: b               #0x814e20
    // 0x814e14: ldur            x3, [fp, #-0x10]
    // 0x814e18: b               #0x814e20
    // 0x814e1c: mov             x3, x0
    // 0x814e20: mov             x0, x3
    // 0x814e24: LeaveFrame
    //     0x814e24: mov             SP, fp
    //     0x814e28: ldp             fp, lr, [SP], #0x10
    // 0x814e2c: ret
    //     0x814e2c: ret             
    // 0x814e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x814e30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x814e34: b               #0x814b20
    // 0x814e38: r9 = hijriMapping
    //     0x814e38: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0x814e3c: ldr             x9, [x9, #0xe08]
    // 0x814e40: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814e40: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x814e44: r9 = keystore
    //     0x814e44: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x814e48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814e48: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x814e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x814e4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x814e50: b               #0x814c44
    // 0x814e54: r9 = hYear
    //     0x814e54: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x814e58: ldr             x9, [x9, #0x270]
    // 0x814e5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814e5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x814e60: r9 = hMonth
    //     0x814e60: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x814e64: ldr             x9, [x9, #0x278]
    // 0x814e68: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814e68: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x814e6c: r9 = hijriMapping
    //     0x814e6c: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0x814e70: ldr             x9, [x9, #0xe08]
    // 0x814e74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x814e74: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] NHijriCalendar <anonymous closure>(dynamic, HijriDate) {
    // ** addr: 0x8150a4, size: 0x80
    // 0x8150a4: EnterFrame
    //     0x8150a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8150a8: mov             fp, SP
    // 0x8150ac: AllocStack(0x8)
    //     0x8150ac: sub             SP, SP, #8
    // 0x8150b0: SetupParameters()
    //     0x8150b0: ldr             x0, [fp, #0x18]
    //     0x8150b4: ldur            w2, [x0, #0x17]
    //     0x8150b8: add             x2, x2, HEAP, lsl #32
    //     0x8150bc: stur            x2, [fp, #-8]
    // 0x8150c0: CheckStackOverflow
    //     0x8150c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8150c4: cmp             SP, x16
    //     0x8150c8: b.ls            #0x815110
    // 0x8150cc: ldr             x1, [fp, #0x10]
    // 0x8150d0: r0 = toMap()
    //     0x8150d0: bl              #0x815350  ; [package:nuonline/app/data/models/calendar.dart] HijriDate::toMap
    // 0x8150d4: mov             x1, x0
    // 0x8150d8: ldur            x0, [fp, #-8]
    // 0x8150dc: LoadField: r2 = r0->field_f
    //     0x8150dc: ldur            w2, [x0, #0xf]
    // 0x8150e0: DecompressPointer r2
    //     0x8150e0: add             x2, x2, HEAP, lsl #32
    // 0x8150e4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8150e4: ldur            w3, [x2, #0x17]
    // 0x8150e8: DecompressPointer r3
    //     0x8150e8: add             x3, x3, HEAP, lsl #32
    // 0x8150ec: r16 = Sentinel
    //     0x8150ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8150f0: cmp             w3, w16
    // 0x8150f4: b.eq            #0x815118
    // 0x8150f8: mov             x2, x1
    // 0x8150fc: r1 = Null
    //     0x8150fc: mov             x1, NULL
    // 0x815100: r0 = NHijriCalendar.fromMap()
    //     0x815100: bl              #0x815124  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::NHijriCalendar.fromMap
    // 0x815104: LeaveFrame
    //     0x815104: mov             SP, fp
    //     0x815108: ldp             fp, lr, [SP], #0x10
    // 0x81510c: ret
    //     0x81510c: ret             
    // 0x815110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x815110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x815114: b               #0x8150cc
    // 0x815118: r9 = hijriMapping
    //     0x815118: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0x81511c: ldr             x9, [x9, #0xe08]
    // 0x815120: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x815120: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  factory _ NHijriCalendar.fromMap(/* No info */) {
    // ** addr: 0x815124, size: 0x178
    // 0x815124: EnterFrame
    //     0x815124: stp             fp, lr, [SP, #-0x10]!
    //     0x815128: mov             fp, SP
    // 0x81512c: AllocStack(0x28)
    //     0x81512c: sub             SP, SP, #0x28
    // 0x815130: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x815130: mov             x0, x1
    //     0x815134: mov             x1, x2
    //     0x815138: stur            x2, [fp, #-8]
    //     0x81513c: stur            x3, [fp, #-0x10]
    // 0x815140: CheckStackOverflow
    //     0x815140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x815144: cmp             SP, x16
    //     0x815148: b.ls            #0x815288
    // 0x81514c: r0 = NHijriCalendar()
    //     0x81514c: bl              #0x814f84  ; AllocateNHijriCalendarStub -> NHijriCalendar (size=0x20)
    // 0x815150: mov             x3, x0
    // 0x815154: r0 = 1
    //     0x815154: movz            x0, #0x1
    // 0x815158: stur            x3, [fp, #-0x18]
    // 0x81515c: StoreField: r3->field_7 = r0
    //     0x81515c: stur            x0, [x3, #7]
    // 0x815160: r0 = Sentinel
    //     0x815160: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815164: StoreField: r3->field_f = r0
    //     0x815164: stur            w0, [x3, #0xf]
    // 0x815168: StoreField: r3->field_13 = r0
    //     0x815168: stur            w0, [x3, #0x13]
    // 0x81516c: r0 = "nu"
    //     0x81516c: add             x0, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0x815170: ldr             x0, [x0, #0xdf8]
    // 0x815174: StoreField: r3->field_1b = r0
    //     0x815174: stur            w0, [x3, #0x1b]
    // 0x815178: ldur            x0, [fp, #-0x10]
    // 0x81517c: ArrayStore: r3[0] = r0  ; List_4
    //     0x81517c: stur            w0, [x3, #0x17]
    // 0x815180: ldur            x1, [fp, #-8]
    // 0x815184: r2 = "year"
    //     0x815184: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x815188: ldr             x2, [x2, #0x310]
    // 0x81518c: r0 = _getValueOrData()
    //     0x81518c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x815190: mov             x1, x0
    // 0x815194: ldur            x0, [fp, #-8]
    // 0x815198: LoadField: r2 = r0->field_f
    //     0x815198: ldur            w2, [x0, #0xf]
    // 0x81519c: DecompressPointer r2
    //     0x81519c: add             x2, x2, HEAP, lsl #32
    // 0x8151a0: cmp             w2, w1
    // 0x8151a4: b.ne            #0x8151b0
    // 0x8151a8: r3 = Null
    //     0x8151a8: mov             x3, NULL
    // 0x8151ac: b               #0x8151b4
    // 0x8151b0: mov             x3, x1
    // 0x8151b4: stur            x3, [fp, #-0x10]
    // 0x8151b8: cmp             w3, NULL
    // 0x8151bc: b.eq            #0x815290
    // 0x8151c0: mov             x1, x0
    // 0x8151c4: r2 = "month"
    //     0x8151c4: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x8151c8: ldr             x2, [x2, #0x328]
    // 0x8151cc: r0 = _getValueOrData()
    //     0x8151cc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8151d0: mov             x1, x0
    // 0x8151d4: ldur            x0, [fp, #-8]
    // 0x8151d8: LoadField: r2 = r0->field_f
    //     0x8151d8: ldur            w2, [x0, #0xf]
    // 0x8151dc: DecompressPointer r2
    //     0x8151dc: add             x2, x2, HEAP, lsl #32
    // 0x8151e0: cmp             w2, w1
    // 0x8151e4: b.ne            #0x8151f0
    // 0x8151e8: r3 = Null
    //     0x8151e8: mov             x3, NULL
    // 0x8151ec: b               #0x8151f4
    // 0x8151f0: mov             x3, x1
    // 0x8151f4: stur            x3, [fp, #-0x20]
    // 0x8151f8: cmp             w3, NULL
    // 0x8151fc: b.eq            #0x815294
    // 0x815200: mov             x1, x0
    // 0x815204: r2 = "day"
    //     0x815204: add             x2, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0x815208: ldr             x2, [x2, #0x340]
    // 0x81520c: r0 = _getValueOrData()
    //     0x81520c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x815210: mov             x1, x0
    // 0x815214: ldur            x0, [fp, #-8]
    // 0x815218: LoadField: r2 = r0->field_f
    //     0x815218: ldur            w2, [x0, #0xf]
    // 0x81521c: DecompressPointer r2
    //     0x81521c: add             x2, x2, HEAP, lsl #32
    // 0x815220: cmp             w2, w1
    // 0x815224: b.ne            #0x815230
    // 0x815228: r2 = Null
    //     0x815228: mov             x2, NULL
    // 0x81522c: b               #0x815234
    // 0x815230: mov             x2, x1
    // 0x815234: ldur            x1, [fp, #-0x10]
    // 0x815238: ldur            x0, [fp, #-0x20]
    // 0x81523c: cmp             w2, NULL
    // 0x815240: b.eq            #0x815298
    // 0x815244: r3 = LoadInt32Instr(r1)
    //     0x815244: sbfx            x3, x1, #1, #0x1f
    //     0x815248: tbz             w1, #0, #0x815250
    //     0x81524c: ldur            x3, [x1, #7]
    // 0x815250: r1 = LoadInt32Instr(r0)
    //     0x815250: sbfx            x1, x0, #1, #0x1f
    //     0x815254: tbz             w0, #0, #0x81525c
    //     0x815258: ldur            x1, [x0, #7]
    // 0x81525c: str             x2, [SP]
    // 0x815260: mov             x2, x3
    // 0x815264: mov             x3, x1
    // 0x815268: ldur            x1, [fp, #-0x18]
    // 0x81526c: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0x81526c: add             x4, PP, #0xe, lsl #12  ; [pp+0xe5a8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0x815270: ldr             x4, [x4, #0x5a8]
    // 0x815274: r0 = init()
    //     0x815274: bl              #0x81529c  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::init
    // 0x815278: ldur            x0, [fp, #-0x18]
    // 0x81527c: LeaveFrame
    //     0x81527c: mov             SP, fp
    //     0x815280: ldp             fp, lr, [SP], #0x10
    // 0x815284: ret
    //     0x815284: ret             
    // 0x815288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x815288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81528c: b               #0x81514c
    // 0x815290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815290: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815294: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815298: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0x81529c, size: 0xb4
    // 0x81529c: EnterFrame
    //     0x81529c: stp             fp, lr, [SP, #-0x10]!
    //     0x8152a0: mov             fp, SP
    // 0x8152a4: mov             x5, x1
    // 0x8152a8: LoadField: r6 = r4->field_13
    //     0x8152a8: ldur            w6, [x4, #0x13]
    // 0x8152ac: sub             x4, x6, #6
    // 0x8152b0: cmp             w4, #2
    // 0x8152b4: b.lt            #0x8152d0
    // 0x8152b8: add             x6, fp, w4, sxtw #2
    // 0x8152bc: ldr             x6, [x6, #8]
    // 0x8152c0: r4 = LoadInt32Instr(r6)
    //     0x8152c0: sbfx            x4, x6, #1, #0x1f
    //     0x8152c4: tbz             w6, #0, #0x8152cc
    //     0x8152c8: ldur            x4, [x6, #7]
    // 0x8152cc: b               #0x8152d4
    // 0x8152d0: r4 = 1
    //     0x8152d0: movz            x4, #0x1
    // 0x8152d4: r0 = BoxInt64Instr(r2)
    //     0x8152d4: sbfiz           x0, x2, #1, #0x1f
    //     0x8152d8: cmp             x2, x0, asr #1
    //     0x8152dc: b.eq            #0x8152e8
    //     0x8152e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8152e4: stur            x2, [x0, #7]
    // 0x8152e8: StoreField: r5->field_13 = r0
    //     0x8152e8: stur            w0, [x5, #0x13]
    //     0x8152ec: tbz             w0, #0, #0x815308
    //     0x8152f0: ldurb           w16, [x5, #-1]
    //     0x8152f4: ldurb           w17, [x0, #-1]
    //     0x8152f8: and             x16, x17, x16, lsr #2
    //     0x8152fc: tst             x16, HEAP, lsr #32
    //     0x815300: b.eq            #0x815308
    //     0x815304: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x815308: r0 = BoxInt64Instr(r3)
    //     0x815308: sbfiz           x0, x3, #1, #0x1f
    //     0x81530c: cmp             x3, x0, asr #1
    //     0x815310: b.eq            #0x81531c
    //     0x815314: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815318: stur            x3, [x0, #7]
    // 0x81531c: StoreField: r5->field_f = r0
    //     0x81531c: stur            w0, [x5, #0xf]
    //     0x815320: tbz             w0, #0, #0x81533c
    //     0x815324: ldurb           w16, [x5, #-1]
    //     0x815328: ldurb           w17, [x0, #-1]
    //     0x81532c: and             x16, x17, x16, lsr #2
    //     0x815330: tst             x16, HEAP, lsr #32
    //     0x815334: b.eq            #0x81533c
    //     0x815338: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x81533c: StoreField: r5->field_7 = r4
    //     0x81533c: stur            x4, [x5, #7]
    // 0x815340: r0 = Null
    //     0x815340: mov             x0, NULL
    // 0x815344: LeaveFrame
    //     0x815344: mov             SP, fp
    //     0x815348: ldp             fp, lr, [SP], #0x10
    // 0x81534c: ret
    //     0x81534c: ret             
  }
  [closure] bool <anonymous closure>(dynamic, HijriDate) {
    // ** addr: 0x815440, size: 0xb0
    // 0x815440: EnterFrame
    //     0x815440: stp             fp, lr, [SP, #-0x10]!
    //     0x815444: mov             fp, SP
    // 0x815448: ldr             x1, [fp, #0x18]
    // 0x81544c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x81544c: ldur            w2, [x1, #0x17]
    // 0x815450: DecompressPointer r2
    //     0x815450: add             x2, x2, HEAP, lsl #32
    // 0x815454: ldr             x1, [fp, #0x10]
    // 0x815458: LoadField: r3 = r1->field_f
    //     0x815458: ldur            x3, [x1, #0xf]
    // 0x81545c: LoadField: r4 = r2->field_f
    //     0x81545c: ldur            w4, [x2, #0xf]
    // 0x815460: DecompressPointer r4
    //     0x815460: add             x4, x4, HEAP, lsl #32
    // 0x815464: LoadField: r2 = r4->field_f
    //     0x815464: ldur            w2, [x4, #0xf]
    // 0x815468: DecompressPointer r2
    //     0x815468: add             x2, x2, HEAP, lsl #32
    // 0x81546c: r16 = Sentinel
    //     0x81546c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815470: cmp             w2, w16
    // 0x815474: b.eq            #0x8154d8
    // 0x815478: r5 = LoadInt32Instr(r2)
    //     0x815478: sbfx            x5, x2, #1, #0x1f
    //     0x81547c: tbz             w2, #0, #0x815484
    //     0x815480: ldur            x5, [x2, #7]
    // 0x815484: cmp             x3, x5
    // 0x815488: b.ne            #0x8154c8
    // 0x81548c: ArrayLoad: r2 = r1[0]  ; List_8
    //     0x81548c: ldur            x2, [x1, #0x17]
    // 0x815490: LoadField: r1 = r4->field_13
    //     0x815490: ldur            w1, [x4, #0x13]
    // 0x815494: DecompressPointer r1
    //     0x815494: add             x1, x1, HEAP, lsl #32
    // 0x815498: r16 = Sentinel
    //     0x815498: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81549c: cmp             w1, w16
    // 0x8154a0: b.eq            #0x8154e4
    // 0x8154a4: r3 = LoadInt32Instr(r1)
    //     0x8154a4: sbfx            x3, x1, #1, #0x1f
    //     0x8154a8: tbz             w1, #0, #0x8154b0
    //     0x8154ac: ldur            x3, [x1, #7]
    // 0x8154b0: cmp             x2, x3
    // 0x8154b4: r16 = true
    //     0x8154b4: add             x16, NULL, #0x20  ; true
    // 0x8154b8: r17 = false
    //     0x8154b8: add             x17, NULL, #0x30  ; false
    // 0x8154bc: csel            x1, x16, x17, eq
    // 0x8154c0: mov             x0, x1
    // 0x8154c4: b               #0x8154cc
    // 0x8154c8: r0 = false
    //     0x8154c8: add             x0, NULL, #0x30  ; false
    // 0x8154cc: LeaveFrame
    //     0x8154cc: mov             SP, fp
    //     0x8154d0: ldp             fp, lr, [SP], #0x10
    // 0x8154d4: ret
    //     0x8154d4: ret             
    // 0x8154d8: r9 = hMonth
    //     0x8154d8: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x8154dc: ldr             x9, [x9, #0x278]
    // 0x8154e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8154e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8154e4: r9 = hYear
    //     0x8154e4: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x8154e8: ldr             x9, [x9, #0x270]
    // 0x8154ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8154ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ NHijriCalendar.fromDate(/* No info */) {
    // ** addr: 0x815680, size: 0x114
    // 0x815680: EnterFrame
    //     0x815680: stp             fp, lr, [SP, #-0x10]!
    //     0x815684: mov             fp, SP
    // 0x815688: AllocStack(0x20)
    //     0x815688: sub             SP, SP, #0x20
    // 0x81568c: r4 = Sentinel
    //     0x81568c: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815690: r0 = 1
    //     0x815690: movz            x0, #0x1
    // 0x815694: stur            x1, [fp, #-8]
    // 0x815698: mov             x16, x5
    // 0x81569c: mov             x5, x1
    // 0x8156a0: mov             x1, x16
    // 0x8156a4: stur            x2, [fp, #-0x10]
    // 0x8156a8: mov             x16, x3
    // 0x8156ac: mov             x3, x2
    // 0x8156b0: mov             x2, x16
    // 0x8156b4: CheckStackOverflow
    //     0x8156b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8156b8: cmp             SP, x16
    //     0x8156bc: b.ls            #0x81578c
    // 0x8156c0: StoreField: r5->field_7 = r0
    //     0x8156c0: stur            x0, [x5, #7]
    // 0x8156c4: StoreField: r5->field_f = r4
    //     0x8156c4: stur            w4, [x5, #0xf]
    // 0x8156c8: StoreField: r5->field_13 = r4
    //     0x8156c8: stur            w4, [x5, #0x13]
    // 0x8156cc: mov             x0, x2
    // 0x8156d0: ArrayStore: r5[0] = r0  ; List_4
    //     0x8156d0: stur            w0, [x5, #0x17]
    //     0x8156d4: ldurb           w16, [x5, #-1]
    //     0x8156d8: ldurb           w17, [x0, #-1]
    //     0x8156dc: and             x16, x17, x16, lsr #2
    //     0x8156e0: tst             x16, HEAP, lsr #32
    //     0x8156e4: b.eq            #0x8156ec
    //     0x8156e8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x8156ec: mov             x0, x1
    // 0x8156f0: StoreField: r5->field_1b = r0
    //     0x8156f0: stur            w0, [x5, #0x1b]
    //     0x8156f4: ldurb           w16, [x5, #-1]
    //     0x8156f8: ldurb           w17, [x0, #-1]
    //     0x8156fc: and             x16, x17, x16, lsr #2
    //     0x815700: tst             x16, HEAP, lsr #32
    //     0x815704: b.eq            #0x81570c
    //     0x815708: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x81570c: r0 = LoadClassIdInstr(r3)
    //     0x81570c: ldur            x0, [x3, #-1]
    //     0x815710: ubfx            x0, x0, #0xc, #0x14
    // 0x815714: mov             x1, x3
    // 0x815718: r0 = GDT[cid_x0 + -0xff6]()
    //     0x815718: sub             lr, x0, #0xff6
    //     0x81571c: ldr             lr, [x21, lr, lsl #3]
    //     0x815720: blr             lr
    // 0x815724: mov             x3, x0
    // 0x815728: ldur            x2, [fp, #-0x10]
    // 0x81572c: stur            x3, [fp, #-0x18]
    // 0x815730: r0 = LoadClassIdInstr(r2)
    //     0x815730: ldur            x0, [x2, #-1]
    //     0x815734: ubfx            x0, x0, #0xc, #0x14
    // 0x815738: mov             x1, x2
    // 0x81573c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81573c: sub             lr, x0, #0xfff
    //     0x815740: ldr             lr, [x21, lr, lsl #3]
    //     0x815744: blr             lr
    // 0x815748: mov             x2, x0
    // 0x81574c: ldur            x1, [fp, #-0x10]
    // 0x815750: stur            x2, [fp, #-0x20]
    // 0x815754: r0 = LoadClassIdInstr(r1)
    //     0x815754: ldur            x0, [x1, #-1]
    //     0x815758: ubfx            x0, x0, #0xc, #0x14
    // 0x81575c: r0 = GDT[cid_x0 + -0xfdf]()
    //     0x81575c: sub             lr, x0, #0xfdf
    //     0x815760: ldr             lr, [x21, lr, lsl #3]
    //     0x815764: blr             lr
    // 0x815768: ldur            x1, [fp, #-8]
    // 0x81576c: ldur            x2, [fp, #-0x18]
    // 0x815770: ldur            x3, [fp, #-0x20]
    // 0x815774: mov             x5, x0
    // 0x815778: r0 = gregorianToHijri()
    //     0x815778: bl              #0x815794  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::gregorianToHijri
    // 0x81577c: r0 = Null
    //     0x81577c: mov             x0, NULL
    // 0x815780: LeaveFrame
    //     0x815780: mov             SP, fp
    //     0x815784: ldp             fp, lr, [SP], #0x10
    // 0x815788: ret
    //     0x815788: ret             
    // 0x81578c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81578c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x815790: b               #0x8156c0
  }
  _ gregorianToHijri(/* No info */) {
    // ** addr: 0x815794, size: 0x2d4
    // 0x815794: EnterFrame
    //     0x815794: stp             fp, lr, [SP, #-0x10]!
    //     0x815798: mov             fp, SP
    // 0x81579c: AllocStack(0x48)
    //     0x81579c: sub             SP, SP, #0x48
    // 0x8157a0: SetupParameters(NHijriCalendar this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8157a0: stur            x1, [fp, #-8]
    //     0x8157a4: stur            x2, [fp, #-0x10]
    //     0x8157a8: stur            x3, [fp, #-0x18]
    //     0x8157ac: stur            x5, [fp, #-0x20]
    // 0x8157b0: CheckStackOverflow
    //     0x8157b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8157b4: cmp             SP, x16
    //     0x8157b8: b.ls            #0x815a3c
    // 0x8157bc: LoadField: r0 = r1->field_1b
    //     0x8157bc: ldur            w0, [x1, #0x1b]
    // 0x8157c0: DecompressPointer r0
    //     0x8157c0: add             x0, x0, HEAP, lsl #32
    // 0x8157c4: r4 = LoadClassIdInstr(r0)
    //     0x8157c4: ldur            x4, [x0, #-1]
    //     0x8157c8: ubfx            x4, x4, #0xc, #0x14
    // 0x8157cc: r16 = "nu"
    //     0x8157cc: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0x8157d0: ldr             x16, [x16, #0xdf8]
    // 0x8157d4: stp             x16, x0, [SP]
    // 0x8157d8: mov             x0, x4
    // 0x8157dc: mov             lr, x0
    // 0x8157e0: ldr             lr, [x21, lr, lsl #3]
    // 0x8157e4: blr             lr
    // 0x8157e8: tbnz            w0, #4, #0x815934
    // 0x8157ec: ldur            x5, [fp, #-8]
    // 0x8157f0: ldur            x2, [fp, #-0x10]
    // 0x8157f4: ldur            x4, [fp, #-0x18]
    // 0x8157f8: ldur            x3, [fp, #-0x20]
    // 0x8157fc: r0 = BoxInt64Instr(r2)
    //     0x8157fc: sbfiz           x0, x2, #1, #0x1f
    //     0x815800: cmp             x2, x0, asr #1
    //     0x815804: b.eq            #0x815810
    //     0x815808: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81580c: stur            x2, [x0, #7]
    // 0x815810: r1 = Null
    //     0x815810: mov             x1, NULL
    // 0x815814: r2 = 10
    //     0x815814: movz            x2, #0xa
    // 0x815818: stur            x0, [fp, #-0x28]
    // 0x81581c: r0 = AllocateArray()
    //     0x81581c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x815820: mov             x2, x0
    // 0x815824: ldur            x0, [fp, #-0x28]
    // 0x815828: StoreField: r2->field_f = r0
    //     0x815828: stur            w0, [x2, #0xf]
    // 0x81582c: r16 = "-"
    //     0x81582c: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x815830: StoreField: r2->field_13 = r16
    //     0x815830: stur            w16, [x2, #0x13]
    // 0x815834: ldur            x3, [fp, #-0x18]
    // 0x815838: r0 = BoxInt64Instr(r3)
    //     0x815838: sbfiz           x0, x3, #1, #0x1f
    //     0x81583c: cmp             x3, x0, asr #1
    //     0x815840: b.eq            #0x81584c
    //     0x815844: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815848: stur            x3, [x0, #7]
    // 0x81584c: ArrayStore: r2[0] = r0  ; List_4
    //     0x81584c: stur            w0, [x2, #0x17]
    // 0x815850: r16 = "-"
    //     0x815850: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x815854: StoreField: r2->field_1b = r16
    //     0x815854: stur            w16, [x2, #0x1b]
    // 0x815858: ldur            x4, [fp, #-0x20]
    // 0x81585c: r0 = BoxInt64Instr(r4)
    //     0x81585c: sbfiz           x0, x4, #1, #0x1f
    //     0x815860: cmp             x4, x0, asr #1
    //     0x815864: b.eq            #0x815870
    //     0x815868: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81586c: stur            x4, [x0, #7]
    // 0x815870: StoreField: r2->field_1f = r0
    //     0x815870: stur            w0, [x2, #0x1f]
    // 0x815874: str             x2, [SP]
    // 0x815878: r0 = _interpolate()
    //     0x815878: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81587c: mov             x1, x0
    // 0x815880: ldur            x0, [fp, #-8]
    // 0x815884: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x815884: ldur            w2, [x0, #0x17]
    // 0x815888: DecompressPointer r2
    //     0x815888: add             x2, x2, HEAP, lsl #32
    // 0x81588c: r16 = Sentinel
    //     0x81588c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815890: cmp             w2, w16
    // 0x815894: b.eq            #0x815a44
    // 0x815898: mov             x16, x1
    // 0x81589c: mov             x1, x2
    // 0x8158a0: mov             x2, x16
    // 0x8158a4: r0 = get()
    //     0x8158a4: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8158a8: mov             x2, x0
    // 0x8158ac: cmp             w2, NULL
    // 0x8158b0: b.eq            #0x815a2c
    // 0x8158b4: ldur            x5, [fp, #-8]
    // 0x8158b8: LoadField: r0 = r2->field_7
    //     0x8158b8: ldur            x0, [x2, #7]
    // 0x8158bc: StoreField: r5->field_7 = r0
    //     0x8158bc: stur            x0, [x5, #7]
    // 0x8158c0: LoadField: r3 = r2->field_f
    //     0x8158c0: ldur            x3, [x2, #0xf]
    // 0x8158c4: r0 = BoxInt64Instr(r3)
    //     0x8158c4: sbfiz           x0, x3, #1, #0x1f
    //     0x8158c8: cmp             x3, x0, asr #1
    //     0x8158cc: b.eq            #0x8158d8
    //     0x8158d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8158d4: stur            x3, [x0, #7]
    // 0x8158d8: StoreField: r5->field_f = r0
    //     0x8158d8: stur            w0, [x5, #0xf]
    //     0x8158dc: tbz             w0, #0, #0x8158f8
    //     0x8158e0: ldurb           w16, [x5, #-1]
    //     0x8158e4: ldurb           w17, [x0, #-1]
    //     0x8158e8: and             x16, x17, x16, lsr #2
    //     0x8158ec: tst             x16, HEAP, lsr #32
    //     0x8158f0: b.eq            #0x8158f8
    //     0x8158f4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x8158f8: ArrayLoad: r3 = r2[0]  ; List_8
    //     0x8158f8: ldur            x3, [x2, #0x17]
    // 0x8158fc: r0 = BoxInt64Instr(r3)
    //     0x8158fc: sbfiz           x0, x3, #1, #0x1f
    //     0x815900: cmp             x3, x0, asr #1
    //     0x815904: b.eq            #0x815910
    //     0x815908: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81590c: stur            x3, [x0, #7]
    // 0x815910: StoreField: r5->field_13 = r0
    //     0x815910: stur            w0, [x5, #0x13]
    //     0x815914: tbz             w0, #0, #0x815930
    //     0x815918: ldurb           w16, [x5, #-1]
    //     0x81591c: ldurb           w17, [x0, #-1]
    //     0x815920: and             x16, x17, x16, lsr #2
    //     0x815924: tst             x16, HEAP, lsr #32
    //     0x815928: b.eq            #0x815930
    //     0x81592c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x815930: b               #0x815a2c
    // 0x815934: ldur            x5, [fp, #-8]
    // 0x815938: ldur            x2, [fp, #-0x10]
    // 0x81593c: ldur            x3, [fp, #-0x18]
    // 0x815940: ldur            x4, [fp, #-0x20]
    // 0x815944: r0 = BoxInt64Instr(r3)
    //     0x815944: sbfiz           x0, x3, #1, #0x1f
    //     0x815948: cmp             x3, x0, asr #1
    //     0x81594c: b.eq            #0x815958
    //     0x815950: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815954: stur            x3, [x0, #7]
    // 0x815958: mov             x3, x0
    // 0x81595c: stur            x3, [fp, #-0x30]
    // 0x815960: r0 = BoxInt64Instr(r4)
    //     0x815960: sbfiz           x0, x4, #1, #0x1f
    //     0x815964: cmp             x4, x0, asr #1
    //     0x815968: b.eq            #0x815974
    //     0x81596c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815970: stur            x4, [x0, #7]
    // 0x815974: stur            x0, [fp, #-0x28]
    // 0x815978: r0 = DateTime()
    //     0x815978: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81597c: stur            x0, [fp, #-0x38]
    // 0x815980: ldur            x16, [fp, #-0x30]
    // 0x815984: ldur            lr, [fp, #-0x28]
    // 0x815988: stp             lr, x16, [SP]
    // 0x81598c: mov             x1, x0
    // 0x815990: ldur            x2, [fp, #-0x10]
    // 0x815994: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x815994: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x815998: ldr             x4, [x4, #0xe00]
    // 0x81599c: r0 = DateTime()
    //     0x81599c: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x8159a0: r0 = HijriCalendar()
    //     0x8159a0: bl              #0x815098  ; AllocateHijriCalendarStub -> HijriCalendar (size=0x20)
    // 0x8159a4: mov             x1, x0
    // 0x8159a8: ldur            x2, [fp, #-0x38]
    // 0x8159ac: stur            x0, [fp, #-0x28]
    // 0x8159b0: r0 = HijriCalendar.fromDate()
    //     0x8159b0: bl              #0x815a68  ; [package:hijri/hijri_calendar.dart] HijriCalendar::HijriCalendar.fromDate
    // 0x8159b4: ldur            x1, [fp, #-0x28]
    // 0x8159b8: LoadField: r2 = r1->field_7
    //     0x8159b8: ldur            x2, [x1, #7]
    // 0x8159bc: ldur            x3, [fp, #-8]
    // 0x8159c0: StoreField: r3->field_7 = r2
    //     0x8159c0: stur            x2, [x3, #7]
    // 0x8159c4: LoadField: r0 = r1->field_f
    //     0x8159c4: ldur            w0, [x1, #0xf]
    // 0x8159c8: DecompressPointer r0
    //     0x8159c8: add             x0, x0, HEAP, lsl #32
    // 0x8159cc: r16 = Sentinel
    //     0x8159cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8159d0: cmp             w0, w16
    // 0x8159d4: b.eq            #0x815a50
    // 0x8159d8: StoreField: r3->field_f = r0
    //     0x8159d8: stur            w0, [x3, #0xf]
    //     0x8159dc: tbz             w0, #0, #0x8159f8
    //     0x8159e0: ldurb           w16, [x3, #-1]
    //     0x8159e4: ldurb           w17, [x0, #-1]
    //     0x8159e8: and             x16, x17, x16, lsr #2
    //     0x8159ec: tst             x16, HEAP, lsr #32
    //     0x8159f0: b.eq            #0x8159f8
    //     0x8159f4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8159f8: LoadField: r0 = r1->field_13
    //     0x8159f8: ldur            w0, [x1, #0x13]
    // 0x8159fc: DecompressPointer r0
    //     0x8159fc: add             x0, x0, HEAP, lsl #32
    // 0x815a00: r16 = Sentinel
    //     0x815a00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815a04: cmp             w0, w16
    // 0x815a08: b.eq            #0x815a5c
    // 0x815a0c: StoreField: r3->field_13 = r0
    //     0x815a0c: stur            w0, [x3, #0x13]
    //     0x815a10: tbz             w0, #0, #0x815a2c
    //     0x815a14: ldurb           w16, [x3, #-1]
    //     0x815a18: ldurb           w17, [x0, #-1]
    //     0x815a1c: and             x16, x17, x16, lsr #2
    //     0x815a20: tst             x16, HEAP, lsr #32
    //     0x815a24: b.eq            #0x815a2c
    //     0x815a28: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x815a2c: r0 = Null
    //     0x815a2c: mov             x0, NULL
    // 0x815a30: LeaveFrame
    //     0x815a30: mov             SP, fp
    //     0x815a34: ldp             fp, lr, [SP], #0x10
    // 0x815a38: ret
    //     0x815a38: ret             
    // 0x815a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x815a3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x815a40: b               #0x8157bc
    // 0x815a44: r9 = hijriMapping
    //     0x815a44: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0x815a48: ldr             x9, [x9, #0xe08]
    // 0x815a4c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x815a4c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x815a50: r9 = hMonth
    //     0x815a50: add             x9, PP, #8, lsl #12  ; [pp+0x8e10] Field <HijriCalendar.hMonth>: late (offset: 0x10)
    //     0x815a54: ldr             x9, [x9, #0xe10]
    // 0x815a58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x815a58: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x815a5c: r9 = hYear
    //     0x815a5c: add             x9, PP, #8, lsl #12  ; [pp+0x8e18] Field <HijriCalendar.hYear>: late (offset: 0x14)
    //     0x815a60: ldr             x9, [x9, #0xe18]
    // 0x815a64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x815a64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ dMY(/* No info */) {
    // ** addr: 0x81f29c, size: 0x1f8
    // 0x81f29c: EnterFrame
    //     0x81f29c: stp             fp, lr, [SP, #-0x10]!
    //     0x81f2a0: mov             fp, SP
    // 0x81f2a4: AllocStack(0x28)
    //     0x81f2a4: sub             SP, SP, #0x28
    // 0x81f2a8: CheckStackOverflow
    //     0x81f2a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81f2ac: cmp             SP, x16
    //     0x81f2b0: b.ls            #0x81f474
    // 0x81f2b4: LoadField: r2 = r1->field_7
    //     0x81f2b4: ldur            x2, [x1, #7]
    // 0x81f2b8: LoadField: r0 = r1->field_f
    //     0x81f2b8: ldur            w0, [x1, #0xf]
    // 0x81f2bc: DecompressPointer r0
    //     0x81f2bc: add             x0, x0, HEAP, lsl #32
    // 0x81f2c0: r16 = Sentinel
    //     0x81f2c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81f2c4: cmp             w0, w16
    // 0x81f2c8: b.eq            #0x81f47c
    // 0x81f2cc: r3 = LoadInt32Instr(r0)
    //     0x81f2cc: sbfx            x3, x0, #1, #0x1f
    //     0x81f2d0: tbz             w0, #0, #0x81f2d8
    //     0x81f2d4: ldur            x3, [x0, #7]
    // 0x81f2d8: cmp             x3, #6
    // 0x81f2dc: b.gt            #0x81f358
    // 0x81f2e0: cmp             x3, #3
    // 0x81f2e4: b.gt            #0x81f324
    // 0x81f2e8: cmp             x3, #2
    // 0x81f2ec: b.gt            #0x81f318
    // 0x81f2f0: cmp             x3, #1
    // 0x81f2f4: b.gt            #0x81f30c
    // 0x81f2f8: cmp             w0, #2
    // 0x81f2fc: b.ne            #0x81f3d0
    // 0x81f300: r4 = "Muharam"
    //     0x81f300: add             x4, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x81f304: ldr             x4, [x4, #0x210]
    // 0x81f308: b               #0x81f3d8
    // 0x81f30c: r4 = "Safar"
    //     0x81f30c: add             x4, PP, #9, lsl #12  ; [pp+0x9218] "Safar"
    //     0x81f310: ldr             x4, [x4, #0x218]
    // 0x81f314: b               #0x81f3d8
    // 0x81f318: r4 = "Rabiul Awal"
    //     0x81f318: add             x4, PP, #9, lsl #12  ; [pp+0x9220] "Rabiul Awal"
    //     0x81f31c: ldr             x4, [x4, #0x220]
    // 0x81f320: b               #0x81f3d8
    // 0x81f324: cmp             x3, #5
    // 0x81f328: b.gt            #0x81f34c
    // 0x81f32c: cmp             x3, #4
    // 0x81f330: b.gt            #0x81f340
    // 0x81f334: r4 = "Rabiul Akhir"
    //     0x81f334: add             x4, PP, #9, lsl #12  ; [pp+0x9228] "Rabiul Akhir"
    //     0x81f338: ldr             x4, [x4, #0x228]
    // 0x81f33c: b               #0x81f3d8
    // 0x81f340: r4 = "Jumadal Ula"
    //     0x81f340: add             x4, PP, #9, lsl #12  ; [pp+0x9230] "Jumadal Ula"
    //     0x81f344: ldr             x4, [x4, #0x230]
    // 0x81f348: b               #0x81f3d8
    // 0x81f34c: r4 = "Jumadal Akhirah"
    //     0x81f34c: add             x4, PP, #9, lsl #12  ; [pp+0x9238] "Jumadal Akhirah"
    //     0x81f350: ldr             x4, [x4, #0x238]
    // 0x81f354: b               #0x81f3d8
    // 0x81f358: cmp             x3, #9
    // 0x81f35c: b.gt            #0x81f394
    // 0x81f360: cmp             x3, #8
    // 0x81f364: b.gt            #0x81f388
    // 0x81f368: cmp             x3, #7
    // 0x81f36c: b.gt            #0x81f37c
    // 0x81f370: r4 = "Rajab"
    //     0x81f370: add             x4, PP, #9, lsl #12  ; [pp+0x9240] "Rajab"
    //     0x81f374: ldr             x4, [x4, #0x240]
    // 0x81f378: b               #0x81f3d8
    // 0x81f37c: r4 = "Sya\'ban"
    //     0x81f37c: add             x4, PP, #9, lsl #12  ; [pp+0x9248] "Sya\'ban"
    //     0x81f380: ldr             x4, [x4, #0x248]
    // 0x81f384: b               #0x81f3d8
    // 0x81f388: r4 = "Ramadhan"
    //     0x81f388: add             x4, PP, #9, lsl #12  ; [pp+0x9250] "Ramadhan"
    //     0x81f38c: ldr             x4, [x4, #0x250]
    // 0x81f390: b               #0x81f3d8
    // 0x81f394: cmp             x3, #0xb
    // 0x81f398: b.gt            #0x81f3bc
    // 0x81f39c: cmp             x3, #0xa
    // 0x81f3a0: b.gt            #0x81f3b0
    // 0x81f3a4: r4 = "Syawal"
    //     0x81f3a4: add             x4, PP, #9, lsl #12  ; [pp+0x9258] "Syawal"
    //     0x81f3a8: ldr             x4, [x4, #0x258]
    // 0x81f3ac: b               #0x81f3d8
    // 0x81f3b0: r4 = "Dzulqa\'dah"
    //     0x81f3b0: add             x4, PP, #9, lsl #12  ; [pp+0x9260] "Dzulqa\'dah"
    //     0x81f3b4: ldr             x4, [x4, #0x260]
    // 0x81f3b8: b               #0x81f3d8
    // 0x81f3bc: cmp             w0, #0x18
    // 0x81f3c0: b.ne            #0x81f3d0
    // 0x81f3c4: r4 = "Dzulhijjah"
    //     0x81f3c4: add             x4, PP, #9, lsl #12  ; [pp+0x9268] "Dzulhijjah"
    //     0x81f3c8: ldr             x4, [x4, #0x268]
    // 0x81f3cc: b               #0x81f3d8
    // 0x81f3d0: r4 = "Muharam"
    //     0x81f3d0: add             x4, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x81f3d4: ldr             x4, [x4, #0x210]
    // 0x81f3d8: r3 = 6
    //     0x81f3d8: movz            x3, #0x6
    // 0x81f3dc: stur            x4, [fp, #-0x18]
    // 0x81f3e0: LoadField: r5 = r1->field_13
    //     0x81f3e0: ldur            w5, [x1, #0x13]
    // 0x81f3e4: DecompressPointer r5
    //     0x81f3e4: add             x5, x5, HEAP, lsl #32
    // 0x81f3e8: r16 = Sentinel
    //     0x81f3e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81f3ec: cmp             w5, w16
    // 0x81f3f0: b.eq            #0x81f488
    // 0x81f3f4: stur            x5, [fp, #-0x10]
    // 0x81f3f8: r0 = BoxInt64Instr(r2)
    //     0x81f3f8: sbfiz           x0, x2, #1, #0x1f
    //     0x81f3fc: cmp             x2, x0, asr #1
    //     0x81f400: b.eq            #0x81f40c
    //     0x81f404: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81f408: stur            x2, [x0, #7]
    // 0x81f40c: mov             x2, x3
    // 0x81f410: r1 = Null
    //     0x81f410: mov             x1, NULL
    // 0x81f414: stur            x0, [fp, #-8]
    // 0x81f418: r0 = AllocateArray()
    //     0x81f418: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81f41c: mov             x2, x0
    // 0x81f420: ldur            x0, [fp, #-8]
    // 0x81f424: stur            x2, [fp, #-0x20]
    // 0x81f428: StoreField: r2->field_f = r0
    //     0x81f428: stur            w0, [x2, #0xf]
    // 0x81f42c: ldur            x0, [fp, #-0x18]
    // 0x81f430: StoreField: r2->field_13 = r0
    //     0x81f430: stur            w0, [x2, #0x13]
    // 0x81f434: ldur            x0, [fp, #-0x10]
    // 0x81f438: ArrayStore: r2[0] = r0  ; List_4
    //     0x81f438: stur            w0, [x2, #0x17]
    // 0x81f43c: r1 = <Object>
    //     0x81f43c: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x81f440: r0 = AllocateGrowableArray()
    //     0x81f440: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x81f444: mov             x1, x0
    // 0x81f448: ldur            x0, [fp, #-0x20]
    // 0x81f44c: StoreField: r1->field_f = r0
    //     0x81f44c: stur            w0, [x1, #0xf]
    // 0x81f450: r0 = 6
    //     0x81f450: movz            x0, #0x6
    // 0x81f454: StoreField: r1->field_b = r0
    //     0x81f454: stur            w0, [x1, #0xb]
    // 0x81f458: r16 = " "
    //     0x81f458: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x81f45c: str             x16, [SP]
    // 0x81f460: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x81f460: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x81f464: r0 = join()
    //     0x81f464: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x81f468: LeaveFrame
    //     0x81f468: mov             SP, fp
    //     0x81f46c: ldp             fp, lr, [SP], #0x10
    // 0x81f470: ret
    //     0x81f470: ret             
    // 0x81f474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81f474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81f478: b               #0x81f2b4
    // 0x81f47c: r9 = hMonth
    //     0x81f47c: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x81f480: ldr             x9, [x9, #0x278]
    // 0x81f484: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x81f484: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x81f488: r9 = hYear
    //     0x81f488: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x81f48c: ldr             x9, [x9, #0x270]
    // 0x81f490: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x81f490: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ hijriToGregorian(/* No info */) {
    // ** addr: 0x820b68, size: 0x470
    // 0x820b68: EnterFrame
    //     0x820b68: stp             fp, lr, [SP, #-0x10]!
    //     0x820b6c: mov             fp, SP
    // 0x820b70: AllocStack(0x70)
    //     0x820b70: sub             SP, SP, #0x70
    // 0x820b74: SetupParameters(NHijriCalendar this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x820b74: stur            x1, [fp, #-8]
    //     0x820b78: stur            x2, [fp, #-0x10]
    //     0x820b7c: stur            x3, [fp, #-0x18]
    //     0x820b80: stur            x5, [fp, #-0x20]
    // 0x820b84: CheckStackOverflow
    //     0x820b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x820b88: cmp             SP, x16
    //     0x820b8c: b.ls            #0x820fb0
    // 0x820b90: r0 = LoadClassIdInstr(r6)
    //     0x820b90: ldur            x0, [x6, #-1]
    //     0x820b94: ubfx            x0, x0, #0xc, #0x14
    // 0x820b98: r16 = "nu"
    //     0x820b98: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0x820b9c: ldr             x16, [x16, #0xdf8]
    // 0x820ba0: stp             x16, x6, [SP]
    // 0x820ba4: mov             lr, x0
    // 0x820ba8: ldr             lr, [x21, lr, lsl #3]
    // 0x820bac: blr             lr
    // 0x820bb0: tbnz            w0, #4, #0x820f6c
    // 0x820bb4: ldur            x0, [fp, #-8]
    // 0x820bb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x820bb8: ldur            w1, [x0, #0x17]
    // 0x820bbc: DecompressPointer r1
    //     0x820bbc: add             x1, x1, HEAP, lsl #32
    // 0x820bc0: r16 = Sentinel
    //     0x820bc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x820bc4: cmp             w1, w16
    // 0x820bc8: b.eq            #0x820fb8
    // 0x820bcc: r0 = values()
    //     0x820bcc: bl              #0x7bcc3c  ; [package:hive/src/box/box_impl.dart] BoxImpl::values
    // 0x820bd0: mov             x1, x0
    // 0x820bd4: r0 = iterator()
    //     0x820bd4: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0x820bd8: mov             x2, x0
    // 0x820bdc: stur            x2, [fp, #-0x38]
    // 0x820be0: LoadField: r3 = r2->field_f
    //     0x820be0: ldur            w3, [x2, #0xf]
    // 0x820be4: DecompressPointer r3
    //     0x820be4: add             x3, x3, HEAP, lsl #32
    // 0x820be8: stur            x3, [fp, #-0x30]
    // 0x820bec: LoadField: r4 = r2->field_13
    //     0x820bec: ldur            w4, [x2, #0x13]
    // 0x820bf0: DecompressPointer r4
    //     0x820bf0: add             x4, x4, HEAP, lsl #32
    // 0x820bf4: stur            x4, [fp, #-0x28]
    // 0x820bf8: LoadField: r5 = r2->field_7
    //     0x820bf8: ldur            w5, [x2, #7]
    // 0x820bfc: DecompressPointer r5
    //     0x820bfc: add             x5, x5, HEAP, lsl #32
    // 0x820c00: stur            x5, [fp, #-8]
    // 0x820c04: ldur            x8, [fp, #-0x10]
    // 0x820c08: ldur            x7, [fp, #-0x18]
    // 0x820c0c: ldur            x6, [fp, #-0x20]
    // 0x820c10: CheckStackOverflow
    //     0x820c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x820c14: cmp             SP, x16
    //     0x820c18: b.ls            #0x820fc4
    // 0x820c1c: r0 = LoadClassIdInstr(r3)
    //     0x820c1c: ldur            x0, [x3, #-1]
    //     0x820c20: ubfx            x0, x0, #0xc, #0x14
    // 0x820c24: mov             x1, x3
    // 0x820c28: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x820c28: movz            x17, #0x292d
    //     0x820c2c: movk            x17, #0x1, lsl #16
    //     0x820c30: add             lr, x0, x17
    //     0x820c34: ldr             lr, [x21, lr, lsl #3]
    //     0x820c38: blr             lr
    // 0x820c3c: tbnz            w0, #4, #0x820f2c
    // 0x820c40: ldur            x2, [fp, #-0x38]
    // 0x820c44: ldur            x3, [fp, #-0x30]
    // 0x820c48: r0 = LoadClassIdInstr(r3)
    //     0x820c48: ldur            x0, [x3, #-1]
    //     0x820c4c: ubfx            x0, x0, #0xc, #0x14
    // 0x820c50: mov             x1, x3
    // 0x820c54: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x820c54: movz            x17, #0x384d
    //     0x820c58: movk            x17, #0x1, lsl #16
    //     0x820c5c: add             lr, x0, x17
    //     0x820c60: ldr             lr, [x21, lr, lsl #3]
    //     0x820c64: blr             lr
    // 0x820c68: ldur            x16, [fp, #-0x28]
    // 0x820c6c: stp             x0, x16, [SP]
    // 0x820c70: ldur            x0, [fp, #-0x28]
    // 0x820c74: ClosureCall
    //     0x820c74: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x820c78: ldur            x2, [x0, #0x1f]
    //     0x820c7c: blr             x2
    // 0x820c80: mov             x4, x0
    // 0x820c84: ldur            x3, [fp, #-0x38]
    // 0x820c88: stur            x4, [fp, #-0x40]
    // 0x820c8c: StoreField: r3->field_b = r0
    //     0x820c8c: stur            w0, [x3, #0xb]
    //     0x820c90: tbz             w0, #0, #0x820cac
    //     0x820c94: ldurb           w16, [x3, #-1]
    //     0x820c98: ldurb           w17, [x0, #-1]
    //     0x820c9c: and             x16, x17, x16, lsr #2
    //     0x820ca0: tst             x16, HEAP, lsr #32
    //     0x820ca4: b.eq            #0x820cac
    //     0x820ca8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x820cac: cmp             w4, NULL
    // 0x820cb0: b.ne            #0x820ce4
    // 0x820cb4: mov             x0, x4
    // 0x820cb8: ldur            x2, [fp, #-8]
    // 0x820cbc: r1 = Null
    //     0x820cbc: mov             x1, NULL
    // 0x820cc0: cmp             w2, NULL
    // 0x820cc4: b.eq            #0x820ce4
    // 0x820cc8: LoadField: r4 = r2->field_1b
    //     0x820cc8: ldur            w4, [x2, #0x1b]
    // 0x820ccc: DecompressPointer r4
    //     0x820ccc: add             x4, x4, HEAP, lsl #32
    // 0x820cd0: r8 = X1
    //     0x820cd0: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x820cd4: LoadField: r9 = r4->field_7
    //     0x820cd4: ldur            x9, [x4, #7]
    // 0x820cd8: r3 = Null
    //     0x820cd8: add             x3, PP, #9, lsl #12  ; [pp+0x93e0] Null
    //     0x820cdc: ldr             x3, [x3, #0x3e0]
    // 0x820ce0: blr             x9
    // 0x820ce4: ldur            x4, [fp, #-0x10]
    // 0x820ce8: ldur            x3, [fp, #-0x40]
    // 0x820cec: LoadField: r1 = r3->field_1f
    //     0x820cec: ldur            w1, [x3, #0x1f]
    // 0x820cf0: DecompressPointer r1
    //     0x820cf0: add             x1, x1, HEAP, lsl #32
    // 0x820cf4: r0 = LoadClassIdInstr(r1)
    //     0x820cf4: ldur            x0, [x1, #-1]
    //     0x820cf8: ubfx            x0, x0, #0xc, #0x14
    // 0x820cfc: r2 = "-"
    //     0x820cfc: ldr             x2, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x820d00: r0 = GDT[cid_x0 + -0x1000]()
    //     0x820d00: sub             lr, x0, #1, lsl #12
    //     0x820d04: ldr             lr, [x21, lr, lsl #3]
    //     0x820d08: blr             lr
    // 0x820d0c: mov             x3, x0
    // 0x820d10: ldur            x2, [fp, #-0x40]
    // 0x820d14: stur            x3, [fp, #-0x48]
    // 0x820d18: ArrayLoad: r0 = r2[0]  ; List_8
    //     0x820d18: ldur            x0, [x2, #0x17]
    // 0x820d1c: ldur            x4, [fp, #-0x10]
    // 0x820d20: cmp             x0, x4
    // 0x820d24: b.ne            #0x820f04
    // 0x820d28: ldur            x5, [fp, #-0x20]
    // 0x820d2c: LoadField: r6 = r2->field_7
    //     0x820d2c: ldur            x6, [x2, #7]
    // 0x820d30: r0 = BoxInt64Instr(r6)
    //     0x820d30: sbfiz           x0, x6, #1, #0x1f
    //     0x820d34: cmp             x6, x0, asr #1
    //     0x820d38: b.eq            #0x820d44
    //     0x820d3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x820d40: stur            x6, [x0, #7]
    // 0x820d44: cmp             w0, w5
    // 0x820d48: b.eq            #0x820d84
    // 0x820d4c: and             w16, w0, w5
    // 0x820d50: branchIfSmi(r16, 0x820efc)
    //     0x820d50: tbz             w16, #0, #0x820efc
    // 0x820d54: r16 = LoadClassIdInstr(r0)
    //     0x820d54: ldur            x16, [x0, #-1]
    //     0x820d58: ubfx            x16, x16, #0xc, #0x14
    // 0x820d5c: cmp             x16, #0x3d
    // 0x820d60: b.ne            #0x820efc
    // 0x820d64: r16 = LoadClassIdInstr(r5)
    //     0x820d64: ldur            x16, [x5, #-1]
    //     0x820d68: ubfx            x16, x16, #0xc, #0x14
    // 0x820d6c: cmp             x16, #0x3d
    // 0x820d70: b.ne            #0x820efc
    // 0x820d74: LoadField: r16 = r0->field_7
    //     0x820d74: ldur            x16, [x0, #7]
    // 0x820d78: LoadField: r17 = r5->field_7
    //     0x820d78: ldur            x17, [x5, #7]
    // 0x820d7c: cmp             x16, x17
    // 0x820d80: b.ne            #0x820efc
    // 0x820d84: ldur            x6, [fp, #-0x18]
    // 0x820d88: LoadField: r7 = r2->field_f
    //     0x820d88: ldur            x7, [x2, #0xf]
    // 0x820d8c: r0 = BoxInt64Instr(r7)
    //     0x820d8c: sbfiz           x0, x7, #1, #0x1f
    //     0x820d90: cmp             x7, x0, asr #1
    //     0x820d94: b.eq            #0x820da0
    //     0x820d98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x820d9c: stur            x7, [x0, #7]
    // 0x820da0: cmp             w0, w6
    // 0x820da4: b.eq            #0x820de0
    // 0x820da8: and             w16, w0, w6
    // 0x820dac: branchIfSmi(r16, 0x820f0c)
    //     0x820dac: tbz             w16, #0, #0x820f0c
    // 0x820db0: r16 = LoadClassIdInstr(r0)
    //     0x820db0: ldur            x16, [x0, #-1]
    //     0x820db4: ubfx            x16, x16, #0xc, #0x14
    // 0x820db8: cmp             x16, #0x3d
    // 0x820dbc: b.ne            #0x820f0c
    // 0x820dc0: r16 = LoadClassIdInstr(r6)
    //     0x820dc0: ldur            x16, [x6, #-1]
    //     0x820dc4: ubfx            x16, x16, #0xc, #0x14
    // 0x820dc8: cmp             x16, #0x3d
    // 0x820dcc: b.ne            #0x820f0c
    // 0x820dd0: LoadField: r16 = r0->field_7
    //     0x820dd0: ldur            x16, [x0, #7]
    // 0x820dd4: LoadField: r17 = r6->field_7
    //     0x820dd4: ldur            x17, [x6, #7]
    // 0x820dd8: cmp             x16, x17
    // 0x820ddc: b.ne            #0x820f0c
    // 0x820de0: LoadField: r0 = r3->field_b
    //     0x820de0: ldur            w0, [x3, #0xb]
    // 0x820de4: r1 = LoadInt32Instr(r0)
    //     0x820de4: sbfx            x1, x0, #1, #0x1f
    // 0x820de8: mov             x0, x1
    // 0x820dec: r1 = 0
    //     0x820dec: movz            x1, #0
    // 0x820df0: cmp             x1, x0
    // 0x820df4: b.hs            #0x820fcc
    // 0x820df8: LoadField: r0 = r3->field_f
    //     0x820df8: ldur            w0, [x3, #0xf]
    // 0x820dfc: DecompressPointer r0
    //     0x820dfc: add             x0, x0, HEAP, lsl #32
    // 0x820e00: LoadField: r1 = r0->field_f
    //     0x820e00: ldur            w1, [x0, #0xf]
    // 0x820e04: DecompressPointer r1
    //     0x820e04: add             x1, x1, HEAP, lsl #32
    // 0x820e08: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x820e08: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x820e0c: r0 = parse()
    //     0x820e0c: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x820e10: mov             x3, x0
    // 0x820e14: ldur            x2, [fp, #-0x48]
    // 0x820e18: stur            x3, [fp, #-0x50]
    // 0x820e1c: LoadField: r0 = r2->field_b
    //     0x820e1c: ldur            w0, [x2, #0xb]
    // 0x820e20: r1 = LoadInt32Instr(r0)
    //     0x820e20: sbfx            x1, x0, #1, #0x1f
    // 0x820e24: mov             x0, x1
    // 0x820e28: r1 = 1
    //     0x820e28: movz            x1, #0x1
    // 0x820e2c: cmp             x1, x0
    // 0x820e30: b.hs            #0x820fd0
    // 0x820e34: LoadField: r0 = r2->field_f
    //     0x820e34: ldur            w0, [x2, #0xf]
    // 0x820e38: DecompressPointer r0
    //     0x820e38: add             x0, x0, HEAP, lsl #32
    // 0x820e3c: LoadField: r1 = r0->field_13
    //     0x820e3c: ldur            w1, [x0, #0x13]
    // 0x820e40: DecompressPointer r1
    //     0x820e40: add             x1, x1, HEAP, lsl #32
    // 0x820e44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x820e44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x820e48: r0 = parse()
    //     0x820e48: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x820e4c: mov             x3, x0
    // 0x820e50: ldur            x2, [fp, #-0x48]
    // 0x820e54: stur            x3, [fp, #-0x58]
    // 0x820e58: LoadField: r0 = r2->field_b
    //     0x820e58: ldur            w0, [x2, #0xb]
    // 0x820e5c: r1 = LoadInt32Instr(r0)
    //     0x820e5c: sbfx            x1, x0, #1, #0x1f
    // 0x820e60: mov             x0, x1
    // 0x820e64: r1 = 2
    //     0x820e64: movz            x1, #0x2
    // 0x820e68: cmp             x1, x0
    // 0x820e6c: b.hs            #0x820fd4
    // 0x820e70: LoadField: r0 = r2->field_f
    //     0x820e70: ldur            w0, [x2, #0xf]
    // 0x820e74: DecompressPointer r0
    //     0x820e74: add             x0, x0, HEAP, lsl #32
    // 0x820e78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x820e78: ldur            w1, [x0, #0x17]
    // 0x820e7c: DecompressPointer r1
    //     0x820e7c: add             x1, x1, HEAP, lsl #32
    // 0x820e80: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x820e80: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x820e84: r0 = parse()
    //     0x820e84: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x820e88: mov             x3, x0
    // 0x820e8c: ldur            x2, [fp, #-0x58]
    // 0x820e90: r0 = BoxInt64Instr(r2)
    //     0x820e90: sbfiz           x0, x2, #1, #0x1f
    //     0x820e94: cmp             x2, x0, asr #1
    //     0x820e98: b.eq            #0x820ea4
    //     0x820e9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x820ea0: stur            x2, [x0, #7]
    // 0x820ea4: mov             x2, x0
    // 0x820ea8: stur            x2, [fp, #-0x48]
    // 0x820eac: r0 = BoxInt64Instr(r3)
    //     0x820eac: sbfiz           x0, x3, #1, #0x1f
    //     0x820eb0: cmp             x3, x0, asr #1
    //     0x820eb4: b.eq            #0x820ec0
    //     0x820eb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x820ebc: stur            x3, [x0, #7]
    // 0x820ec0: stur            x0, [fp, #-0x40]
    // 0x820ec4: r0 = DateTime()
    //     0x820ec4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x820ec8: stur            x0, [fp, #-0x60]
    // 0x820ecc: ldur            x16, [fp, #-0x48]
    // 0x820ed0: ldur            lr, [fp, #-0x40]
    // 0x820ed4: stp             lr, x16, [SP]
    // 0x820ed8: mov             x1, x0
    // 0x820edc: ldur            x2, [fp, #-0x50]
    // 0x820ee0: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x820ee0: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x820ee4: ldr             x4, [x4, #0xe00]
    // 0x820ee8: r0 = DateTime()
    //     0x820ee8: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x820eec: ldur            x0, [fp, #-0x60]
    // 0x820ef0: LeaveFrame
    //     0x820ef0: mov             SP, fp
    //     0x820ef4: ldp             fp, lr, [SP], #0x10
    // 0x820ef8: ret
    //     0x820ef8: ret             
    // 0x820efc: ldur            x6, [fp, #-0x18]
    // 0x820f00: b               #0x820f0c
    // 0x820f04: ldur            x6, [fp, #-0x18]
    // 0x820f08: ldur            x5, [fp, #-0x20]
    // 0x820f0c: mov             x8, x4
    // 0x820f10: mov             x7, x6
    // 0x820f14: mov             x6, x5
    // 0x820f18: ldur            x2, [fp, #-0x38]
    // 0x820f1c: ldur            x5, [fp, #-8]
    // 0x820f20: ldur            x3, [fp, #-0x30]
    // 0x820f24: ldur            x4, [fp, #-0x28]
    // 0x820f28: b               #0x820c10
    // 0x820f2c: ldur            x0, [fp, #-0x38]
    // 0x820f30: StoreField: r0->field_b = rNULL
    //     0x820f30: stur            NULL, [x0, #0xb]
    // 0x820f34: r0 = DateTime()
    //     0x820f34: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x820f38: mov             x1, x0
    // 0x820f3c: r0 = false
    //     0x820f3c: add             x0, NULL, #0x30  ; false
    // 0x820f40: stur            x1, [fp, #-8]
    // 0x820f44: StoreField: r1->field_13 = r0
    //     0x820f44: stur            w0, [x1, #0x13]
    // 0x820f48: r0 = _getCurrentMicros()
    //     0x820f48: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x820f4c: r1 = LoadInt32Instr(r0)
    //     0x820f4c: sbfx            x1, x0, #1, #0x1f
    //     0x820f50: tbz             w0, #0, #0x820f58
    //     0x820f54: ldur            x1, [x0, #7]
    // 0x820f58: ldur            x0, [fp, #-8]
    // 0x820f5c: StoreField: r0->field_7 = r1
    //     0x820f5c: stur            x1, [x0, #7]
    // 0x820f60: LeaveFrame
    //     0x820f60: mov             SP, fp
    //     0x820f64: ldp             fp, lr, [SP], #0x10
    // 0x820f68: ret
    //     0x820f68: ret             
    // 0x820f6c: ldur            x4, [fp, #-0x10]
    // 0x820f70: ldur            x6, [fp, #-0x18]
    // 0x820f74: ldur            x5, [fp, #-0x20]
    // 0x820f78: r0 = HijriCalendar()
    //     0x820f78: bl              #0x815098  ; AllocateHijriCalendarStub -> HijriCalendar (size=0x20)
    // 0x820f7c: mov             x1, x0
    // 0x820f80: r0 = 1
    //     0x820f80: movz            x0, #0x1
    // 0x820f84: StoreField: r1->field_7 = r0
    //     0x820f84: stur            x0, [x1, #7]
    // 0x820f88: r0 = Sentinel
    //     0x820f88: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x820f8c: StoreField: r1->field_f = r0
    //     0x820f8c: stur            w0, [x1, #0xf]
    // 0x820f90: StoreField: r1->field_13 = r0
    //     0x820f90: stur            w0, [x1, #0x13]
    // 0x820f94: ldur            x2, [fp, #-0x10]
    // 0x820f98: ldur            x3, [fp, #-0x18]
    // 0x820f9c: ldur            x5, [fp, #-0x20]
    // 0x820fa0: r0 = hijriToGregorian()
    //     0x820fa0: bl              #0x816c10  ; [package:hijri/hijri_calendar.dart] HijriCalendar::hijriToGregorian
    // 0x820fa4: LeaveFrame
    //     0x820fa4: mov             SP, fp
    //     0x820fa8: ldp             fp, lr, [SP], #0x10
    // 0x820fac: ret
    //     0x820fac: ret             
    // 0x820fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820fb4: b               #0x820b90
    // 0x820fb8: r9 = hijriMapping
    //     0x820fb8: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0x820fbc: ldr             x9, [x9, #0xe08]
    // 0x820fc0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x820fc0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x820fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820fc8: b               #0x820c1c
    // 0x820fcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x820fcc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x820fd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x820fd0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x820fd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x820fd4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ isBefore(/* No info */) {
    // ** addr: 0x821420, size: 0x100
    // 0x821420: EnterFrame
    //     0x821420: stp             fp, lr, [SP, #-0x10]!
    //     0x821424: mov             fp, SP
    // 0x821428: LoadField: r4 = r1->field_13
    //     0x821428: ldur            w4, [x1, #0x13]
    // 0x82142c: DecompressPointer r4
    //     0x82142c: add             x4, x4, HEAP, lsl #32
    // 0x821430: r16 = Sentinel
    //     0x821430: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x821434: cmp             w4, w16
    // 0x821438: b.eq            #0x8214fc
    // 0x82143c: r6 = LoadInt32Instr(r4)
    //     0x82143c: sbfx            x6, x4, #1, #0x1f
    //     0x821440: tbz             w4, #0, #0x821448
    //     0x821444: ldur            x6, [x4, #7]
    // 0x821448: cmp             x6, x2
    // 0x82144c: b.ge            #0x821460
    // 0x821450: r0 = true
    //     0x821450: add             x0, NULL, #0x20  ; true
    // 0x821454: LeaveFrame
    //     0x821454: mov             SP, fp
    //     0x821458: ldp             fp, lr, [SP], #0x10
    // 0x82145c: ret
    //     0x82145c: ret             
    // 0x821460: cmp             x6, x2
    // 0x821464: b.ne            #0x8214a0
    // 0x821468: LoadField: r4 = r1->field_f
    //     0x821468: ldur            w4, [x1, #0xf]
    // 0x82146c: DecompressPointer r4
    //     0x82146c: add             x4, x4, HEAP, lsl #32
    // 0x821470: r16 = Sentinel
    //     0x821470: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x821474: cmp             w4, w16
    // 0x821478: b.eq            #0x821508
    // 0x82147c: r7 = LoadInt32Instr(r4)
    //     0x82147c: sbfx            x7, x4, #1, #0x1f
    //     0x821480: tbz             w4, #0, #0x821488
    //     0x821484: ldur            x7, [x4, #7]
    // 0x821488: cmp             x7, x3
    // 0x82148c: b.ge            #0x8214a0
    // 0x821490: r0 = true
    //     0x821490: add             x0, NULL, #0x20  ; true
    // 0x821494: LeaveFrame
    //     0x821494: mov             SP, fp
    //     0x821498: ldp             fp, lr, [SP], #0x10
    // 0x82149c: ret
    //     0x82149c: ret             
    // 0x8214a0: cmp             x6, x2
    // 0x8214a4: b.ne            #0x8214ec
    // 0x8214a8: LoadField: r2 = r1->field_f
    //     0x8214a8: ldur            w2, [x1, #0xf]
    // 0x8214ac: DecompressPointer r2
    //     0x8214ac: add             x2, x2, HEAP, lsl #32
    // 0x8214b0: r16 = Sentinel
    //     0x8214b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8214b4: cmp             w2, w16
    // 0x8214b8: b.eq            #0x821514
    // 0x8214bc: r4 = LoadInt32Instr(r2)
    //     0x8214bc: sbfx            x4, x2, #1, #0x1f
    //     0x8214c0: tbz             w2, #0, #0x8214c8
    //     0x8214c4: ldur            x4, [x2, #7]
    // 0x8214c8: cmp             x4, x3
    // 0x8214cc: b.ne            #0x8214ec
    // 0x8214d0: LoadField: r2 = r1->field_7
    //     0x8214d0: ldur            x2, [x1, #7]
    // 0x8214d4: cmp             x2, x5
    // 0x8214d8: b.ge            #0x8214ec
    // 0x8214dc: r0 = true
    //     0x8214dc: add             x0, NULL, #0x20  ; true
    // 0x8214e0: LeaveFrame
    //     0x8214e0: mov             SP, fp
    //     0x8214e4: ldp             fp, lr, [SP], #0x10
    // 0x8214e8: ret
    //     0x8214e8: ret             
    // 0x8214ec: r0 = false
    //     0x8214ec: add             x0, NULL, #0x30  ; false
    // 0x8214f0: LeaveFrame
    //     0x8214f0: mov             SP, fp
    //     0x8214f4: ldp             fp, lr, [SP], #0x10
    // 0x8214f8: ret
    //     0x8214f8: ret             
    // 0x8214fc: r9 = hYear
    //     0x8214fc: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x821500: ldr             x9, [x9, #0x270]
    // 0x821504: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x821504: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x821508: r9 = hMonth
    //     0x821508: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x82150c: ldr             x9, [x9, #0x278]
    // 0x821510: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x821510: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x821514: r9 = hMonth
    //     0x821514: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x821518: ldr             x9, [x9, #0x278]
    // 0x82151c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x82151c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ isAfter(/* No info */) {
    // ** addr: 0x821520, size: 0x100
    // 0x821520: EnterFrame
    //     0x821520: stp             fp, lr, [SP, #-0x10]!
    //     0x821524: mov             fp, SP
    // 0x821528: LoadField: r4 = r1->field_13
    //     0x821528: ldur            w4, [x1, #0x13]
    // 0x82152c: DecompressPointer r4
    //     0x82152c: add             x4, x4, HEAP, lsl #32
    // 0x821530: r16 = Sentinel
    //     0x821530: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x821534: cmp             w4, w16
    // 0x821538: b.eq            #0x8215fc
    // 0x82153c: r6 = LoadInt32Instr(r4)
    //     0x82153c: sbfx            x6, x4, #1, #0x1f
    //     0x821540: tbz             w4, #0, #0x821548
    //     0x821544: ldur            x6, [x4, #7]
    // 0x821548: cmp             x6, x2
    // 0x82154c: b.le            #0x821560
    // 0x821550: r0 = true
    //     0x821550: add             x0, NULL, #0x20  ; true
    // 0x821554: LeaveFrame
    //     0x821554: mov             SP, fp
    //     0x821558: ldp             fp, lr, [SP], #0x10
    // 0x82155c: ret
    //     0x82155c: ret             
    // 0x821560: cmp             x6, x2
    // 0x821564: b.ne            #0x8215a0
    // 0x821568: LoadField: r4 = r1->field_f
    //     0x821568: ldur            w4, [x1, #0xf]
    // 0x82156c: DecompressPointer r4
    //     0x82156c: add             x4, x4, HEAP, lsl #32
    // 0x821570: r16 = Sentinel
    //     0x821570: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x821574: cmp             w4, w16
    // 0x821578: b.eq            #0x821608
    // 0x82157c: r7 = LoadInt32Instr(r4)
    //     0x82157c: sbfx            x7, x4, #1, #0x1f
    //     0x821580: tbz             w4, #0, #0x821588
    //     0x821584: ldur            x7, [x4, #7]
    // 0x821588: cmp             x7, x3
    // 0x82158c: b.le            #0x8215a0
    // 0x821590: r0 = true
    //     0x821590: add             x0, NULL, #0x20  ; true
    // 0x821594: LeaveFrame
    //     0x821594: mov             SP, fp
    //     0x821598: ldp             fp, lr, [SP], #0x10
    // 0x82159c: ret
    //     0x82159c: ret             
    // 0x8215a0: cmp             x6, x2
    // 0x8215a4: b.ne            #0x8215ec
    // 0x8215a8: LoadField: r2 = r1->field_f
    //     0x8215a8: ldur            w2, [x1, #0xf]
    // 0x8215ac: DecompressPointer r2
    //     0x8215ac: add             x2, x2, HEAP, lsl #32
    // 0x8215b0: r16 = Sentinel
    //     0x8215b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8215b4: cmp             w2, w16
    // 0x8215b8: b.eq            #0x821614
    // 0x8215bc: r4 = LoadInt32Instr(r2)
    //     0x8215bc: sbfx            x4, x2, #1, #0x1f
    //     0x8215c0: tbz             w2, #0, #0x8215c8
    //     0x8215c4: ldur            x4, [x2, #7]
    // 0x8215c8: cmp             x4, x3
    // 0x8215cc: b.ne            #0x8215ec
    // 0x8215d0: LoadField: r2 = r1->field_7
    //     0x8215d0: ldur            x2, [x1, #7]
    // 0x8215d4: cmp             x2, x5
    // 0x8215d8: b.le            #0x8215ec
    // 0x8215dc: r0 = true
    //     0x8215dc: add             x0, NULL, #0x20  ; true
    // 0x8215e0: LeaveFrame
    //     0x8215e0: mov             SP, fp
    //     0x8215e4: ldp             fp, lr, [SP], #0x10
    // 0x8215e8: ret
    //     0x8215e8: ret             
    // 0x8215ec: r0 = false
    //     0x8215ec: add             x0, NULL, #0x30  ; false
    // 0x8215f0: LeaveFrame
    //     0x8215f0: mov             SP, fp
    //     0x8215f4: ldp             fp, lr, [SP], #0x10
    // 0x8215f8: ret
    //     0x8215f8: ret             
    // 0x8215fc: r9 = hYear
    //     0x8215fc: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x821600: ldr             x9, [x9, #0x270]
    // 0x821604: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x821604: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x821608: r9 = hMonth
    //     0x821608: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x82160c: ldr             x9, [x9, #0x278]
    // 0x821610: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x821610: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x821614: r9 = hMonth
    //     0x821614: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x821618: ldr             x9, [x9, #0x278]
    // 0x82161c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x82161c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ mY(/* No info */) {
    // ** addr: 0x83a3ec, size: 0x1d4
    // 0x83a3ec: EnterFrame
    //     0x83a3ec: stp             fp, lr, [SP, #-0x10]!
    //     0x83a3f0: mov             fp, SP
    // 0x83a3f4: AllocStack(0x20)
    //     0x83a3f4: sub             SP, SP, #0x20
    // 0x83a3f8: CheckStackOverflow
    //     0x83a3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a3fc: cmp             SP, x16
    //     0x83a400: b.ls            #0x83a5a0
    // 0x83a404: LoadField: r0 = r1->field_f
    //     0x83a404: ldur            w0, [x1, #0xf]
    // 0x83a408: DecompressPointer r0
    //     0x83a408: add             x0, x0, HEAP, lsl #32
    // 0x83a40c: r16 = Sentinel
    //     0x83a40c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x83a410: cmp             w0, w16
    // 0x83a414: b.eq            #0x83a5a8
    // 0x83a418: r2 = LoadInt32Instr(r0)
    //     0x83a418: sbfx            x2, x0, #1, #0x1f
    //     0x83a41c: tbz             w0, #0, #0x83a424
    //     0x83a420: ldur            x2, [x0, #7]
    // 0x83a424: cmp             x2, #6
    // 0x83a428: b.gt            #0x83a4a4
    // 0x83a42c: cmp             x2, #3
    // 0x83a430: b.gt            #0x83a470
    // 0x83a434: cmp             x2, #2
    // 0x83a438: b.gt            #0x83a464
    // 0x83a43c: cmp             x2, #1
    // 0x83a440: b.gt            #0x83a458
    // 0x83a444: cmp             w0, #2
    // 0x83a448: b.ne            #0x83a51c
    // 0x83a44c: r3 = "Muharam"
    //     0x83a44c: add             x3, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x83a450: ldr             x3, [x3, #0x210]
    // 0x83a454: b               #0x83a524
    // 0x83a458: r3 = "Safar"
    //     0x83a458: add             x3, PP, #9, lsl #12  ; [pp+0x9218] "Safar"
    //     0x83a45c: ldr             x3, [x3, #0x218]
    // 0x83a460: b               #0x83a524
    // 0x83a464: r3 = "Rabiul Awal"
    //     0x83a464: add             x3, PP, #9, lsl #12  ; [pp+0x9220] "Rabiul Awal"
    //     0x83a468: ldr             x3, [x3, #0x220]
    // 0x83a46c: b               #0x83a524
    // 0x83a470: cmp             x2, #5
    // 0x83a474: b.gt            #0x83a498
    // 0x83a478: cmp             x2, #4
    // 0x83a47c: b.gt            #0x83a48c
    // 0x83a480: r3 = "Rabiul Akhir"
    //     0x83a480: add             x3, PP, #9, lsl #12  ; [pp+0x9228] "Rabiul Akhir"
    //     0x83a484: ldr             x3, [x3, #0x228]
    // 0x83a488: b               #0x83a524
    // 0x83a48c: r3 = "Jumadal Ula"
    //     0x83a48c: add             x3, PP, #9, lsl #12  ; [pp+0x9230] "Jumadal Ula"
    //     0x83a490: ldr             x3, [x3, #0x230]
    // 0x83a494: b               #0x83a524
    // 0x83a498: r3 = "Jumadal Akhirah"
    //     0x83a498: add             x3, PP, #9, lsl #12  ; [pp+0x9238] "Jumadal Akhirah"
    //     0x83a49c: ldr             x3, [x3, #0x238]
    // 0x83a4a0: b               #0x83a524
    // 0x83a4a4: cmp             x2, #9
    // 0x83a4a8: b.gt            #0x83a4e0
    // 0x83a4ac: cmp             x2, #8
    // 0x83a4b0: b.gt            #0x83a4d4
    // 0x83a4b4: cmp             x2, #7
    // 0x83a4b8: b.gt            #0x83a4c8
    // 0x83a4bc: r3 = "Rajab"
    //     0x83a4bc: add             x3, PP, #9, lsl #12  ; [pp+0x9240] "Rajab"
    //     0x83a4c0: ldr             x3, [x3, #0x240]
    // 0x83a4c4: b               #0x83a524
    // 0x83a4c8: r3 = "Sya\'ban"
    //     0x83a4c8: add             x3, PP, #9, lsl #12  ; [pp+0x9248] "Sya\'ban"
    //     0x83a4cc: ldr             x3, [x3, #0x248]
    // 0x83a4d0: b               #0x83a524
    // 0x83a4d4: r3 = "Ramadhan"
    //     0x83a4d4: add             x3, PP, #9, lsl #12  ; [pp+0x9250] "Ramadhan"
    //     0x83a4d8: ldr             x3, [x3, #0x250]
    // 0x83a4dc: b               #0x83a524
    // 0x83a4e0: cmp             x2, #0xb
    // 0x83a4e4: b.gt            #0x83a508
    // 0x83a4e8: cmp             x2, #0xa
    // 0x83a4ec: b.gt            #0x83a4fc
    // 0x83a4f0: r3 = "Syawal"
    //     0x83a4f0: add             x3, PP, #9, lsl #12  ; [pp+0x9258] "Syawal"
    //     0x83a4f4: ldr             x3, [x3, #0x258]
    // 0x83a4f8: b               #0x83a524
    // 0x83a4fc: r3 = "Dzulqa\'dah"
    //     0x83a4fc: add             x3, PP, #9, lsl #12  ; [pp+0x9260] "Dzulqa\'dah"
    //     0x83a500: ldr             x3, [x3, #0x260]
    // 0x83a504: b               #0x83a524
    // 0x83a508: cmp             w0, #0x18
    // 0x83a50c: b.ne            #0x83a51c
    // 0x83a510: r3 = "Dzulhijjah"
    //     0x83a510: add             x3, PP, #9, lsl #12  ; [pp+0x9268] "Dzulhijjah"
    //     0x83a514: ldr             x3, [x3, #0x268]
    // 0x83a518: b               #0x83a524
    // 0x83a51c: r3 = "Muharam"
    //     0x83a51c: add             x3, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x83a520: ldr             x3, [x3, #0x210]
    // 0x83a524: r0 = 4
    //     0x83a524: movz            x0, #0x4
    // 0x83a528: stur            x3, [fp, #-0x10]
    // 0x83a52c: LoadField: r4 = r1->field_13
    //     0x83a52c: ldur            w4, [x1, #0x13]
    // 0x83a530: DecompressPointer r4
    //     0x83a530: add             x4, x4, HEAP, lsl #32
    // 0x83a534: r16 = Sentinel
    //     0x83a534: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x83a538: cmp             w4, w16
    // 0x83a53c: b.eq            #0x83a5b4
    // 0x83a540: mov             x2, x0
    // 0x83a544: stur            x4, [fp, #-8]
    // 0x83a548: r1 = Null
    //     0x83a548: mov             x1, NULL
    // 0x83a54c: r0 = AllocateArray()
    //     0x83a54c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83a550: mov             x2, x0
    // 0x83a554: ldur            x0, [fp, #-0x10]
    // 0x83a558: stur            x2, [fp, #-0x18]
    // 0x83a55c: StoreField: r2->field_f = r0
    //     0x83a55c: stur            w0, [x2, #0xf]
    // 0x83a560: ldur            x0, [fp, #-8]
    // 0x83a564: StoreField: r2->field_13 = r0
    //     0x83a564: stur            w0, [x2, #0x13]
    // 0x83a568: r1 = <Object>
    //     0x83a568: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x83a56c: r0 = AllocateGrowableArray()
    //     0x83a56c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x83a570: mov             x1, x0
    // 0x83a574: ldur            x0, [fp, #-0x18]
    // 0x83a578: StoreField: r1->field_f = r0
    //     0x83a578: stur            w0, [x1, #0xf]
    // 0x83a57c: r0 = 4
    //     0x83a57c: movz            x0, #0x4
    // 0x83a580: StoreField: r1->field_b = r0
    //     0x83a580: stur            w0, [x1, #0xb]
    // 0x83a584: r16 = " "
    //     0x83a584: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x83a588: str             x16, [SP]
    // 0x83a58c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x83a58c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x83a590: r0 = join()
    //     0x83a590: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x83a594: LeaveFrame
    //     0x83a594: mov             SP, fp
    //     0x83a598: ldp             fp, lr, [SP], #0x10
    // 0x83a59c: ret
    //     0x83a59c: ret             
    // 0x83a5a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a5a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a5a4: b               #0x83a404
    // 0x83a5a8: r9 = hMonth
    //     0x83a5a8: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x83a5ac: ldr             x9, [x9, #0x278]
    // 0x83a5b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x83a5b0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x83a5b4: r9 = hYear
    //     0x83a5b4: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x83a5b8: ldr             x9, [x9, #0x270]
    // 0x83a5bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x83a5bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ mMMM(/* No info */) {
    // ** addr: 0x8ef284, size: 0x138
    // 0x8ef284: LoadField: r2 = r1->field_f
    //     0x8ef284: ldur            w2, [x1, #0xf]
    // 0x8ef288: DecompressPointer r2
    //     0x8ef288: add             x2, x2, HEAP, lsl #32
    // 0x8ef28c: r16 = Sentinel
    //     0x8ef28c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ef290: cmp             w2, w16
    // 0x8ef294: b.eq            #0x8ef3a8
    // 0x8ef298: r1 = LoadInt32Instr(r2)
    //     0x8ef298: sbfx            x1, x2, #1, #0x1f
    //     0x8ef29c: tbz             w2, #0, #0x8ef2a4
    //     0x8ef2a0: ldur            x1, [x2, #7]
    // 0x8ef2a4: cmp             x1, #6
    // 0x8ef2a8: b.gt            #0x8ef324
    // 0x8ef2ac: cmp             x1, #3
    // 0x8ef2b0: b.gt            #0x8ef2f0
    // 0x8ef2b4: cmp             x1, #2
    // 0x8ef2b8: b.gt            #0x8ef2e4
    // 0x8ef2bc: cmp             x1, #1
    // 0x8ef2c0: b.gt            #0x8ef2d8
    // 0x8ef2c4: cmp             w2, #2
    // 0x8ef2c8: b.ne            #0x8ef39c
    // 0x8ef2cc: r0 = "Muharam"
    //     0x8ef2cc: add             x0, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x8ef2d0: ldr             x0, [x0, #0x210]
    // 0x8ef2d4: b               #0x8ef3a4
    // 0x8ef2d8: r0 = "Safar"
    //     0x8ef2d8: add             x0, PP, #9, lsl #12  ; [pp+0x9218] "Safar"
    //     0x8ef2dc: ldr             x0, [x0, #0x218]
    // 0x8ef2e0: b               #0x8ef3a4
    // 0x8ef2e4: r0 = "Rabiul Awal"
    //     0x8ef2e4: add             x0, PP, #9, lsl #12  ; [pp+0x9220] "Rabiul Awal"
    //     0x8ef2e8: ldr             x0, [x0, #0x220]
    // 0x8ef2ec: b               #0x8ef3a4
    // 0x8ef2f0: cmp             x1, #5
    // 0x8ef2f4: b.gt            #0x8ef318
    // 0x8ef2f8: cmp             x1, #4
    // 0x8ef2fc: b.gt            #0x8ef30c
    // 0x8ef300: r0 = "Rabiul Akhir"
    //     0x8ef300: add             x0, PP, #9, lsl #12  ; [pp+0x9228] "Rabiul Akhir"
    //     0x8ef304: ldr             x0, [x0, #0x228]
    // 0x8ef308: b               #0x8ef3a4
    // 0x8ef30c: r0 = "Jumadal Ula"
    //     0x8ef30c: add             x0, PP, #9, lsl #12  ; [pp+0x9230] "Jumadal Ula"
    //     0x8ef310: ldr             x0, [x0, #0x230]
    // 0x8ef314: b               #0x8ef3a4
    // 0x8ef318: r0 = "Jumadal Akhirah"
    //     0x8ef318: add             x0, PP, #9, lsl #12  ; [pp+0x9238] "Jumadal Akhirah"
    //     0x8ef31c: ldr             x0, [x0, #0x238]
    // 0x8ef320: b               #0x8ef3a4
    // 0x8ef324: cmp             x1, #9
    // 0x8ef328: b.gt            #0x8ef360
    // 0x8ef32c: cmp             x1, #8
    // 0x8ef330: b.gt            #0x8ef354
    // 0x8ef334: cmp             x1, #7
    // 0x8ef338: b.gt            #0x8ef348
    // 0x8ef33c: r0 = "Rajab"
    //     0x8ef33c: add             x0, PP, #9, lsl #12  ; [pp+0x9240] "Rajab"
    //     0x8ef340: ldr             x0, [x0, #0x240]
    // 0x8ef344: b               #0x8ef3a4
    // 0x8ef348: r0 = "Sya\'ban"
    //     0x8ef348: add             x0, PP, #9, lsl #12  ; [pp+0x9248] "Sya\'ban"
    //     0x8ef34c: ldr             x0, [x0, #0x248]
    // 0x8ef350: b               #0x8ef3a4
    // 0x8ef354: r0 = "Ramadhan"
    //     0x8ef354: add             x0, PP, #9, lsl #12  ; [pp+0x9250] "Ramadhan"
    //     0x8ef358: ldr             x0, [x0, #0x250]
    // 0x8ef35c: b               #0x8ef3a4
    // 0x8ef360: cmp             x1, #0xb
    // 0x8ef364: b.gt            #0x8ef388
    // 0x8ef368: cmp             x1, #0xa
    // 0x8ef36c: b.gt            #0x8ef37c
    // 0x8ef370: r0 = "Syawal"
    //     0x8ef370: add             x0, PP, #9, lsl #12  ; [pp+0x9258] "Syawal"
    //     0x8ef374: ldr             x0, [x0, #0x258]
    // 0x8ef378: b               #0x8ef3a4
    // 0x8ef37c: r0 = "Dzulqa\'dah"
    //     0x8ef37c: add             x0, PP, #9, lsl #12  ; [pp+0x9260] "Dzulqa\'dah"
    //     0x8ef380: ldr             x0, [x0, #0x260]
    // 0x8ef384: b               #0x8ef3a4
    // 0x8ef388: cmp             w2, #0x18
    // 0x8ef38c: b.ne            #0x8ef39c
    // 0x8ef390: r0 = "Dzulhijjah"
    //     0x8ef390: add             x0, PP, #9, lsl #12  ; [pp+0x9268] "Dzulhijjah"
    //     0x8ef394: ldr             x0, [x0, #0x268]
    // 0x8ef398: b               #0x8ef3a4
    // 0x8ef39c: r0 = "Muharam"
    //     0x8ef39c: add             x0, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0x8ef3a0: ldr             x0, [x0, #0x210]
    // 0x8ef3a4: ret
    //     0x8ef3a4: ret             
    // 0x8ef3a8: EnterFrame
    //     0x8ef3a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef3ac: mov             fp, SP
    // 0x8ef3b0: r9 = hMonth
    //     0x8ef3b0: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x8ef3b4: ldr             x9, [x9, #0x278]
    // 0x8ef3b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ef3b8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toGregorian(/* No info */) {
    // ** addr: 0xb11d50, size: 0x2c4
    // 0xb11d50: EnterFrame
    //     0xb11d50: stp             fp, lr, [SP, #-0x10]!
    //     0xb11d54: mov             fp, SP
    // 0xb11d58: AllocStack(0x70)
    //     0xb11d58: sub             SP, SP, #0x70
    // 0xb11d5c: SetupParameters(NHijriCalendar this /* r1 => r1, fp-0x48 */)
    //     0xb11d5c: stur            x1, [fp, #-0x48]
    // 0xb11d60: CheckStackOverflow
    //     0xb11d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb11d64: cmp             SP, x16
    //     0xb11d68: b.ls            #0xb11fd4
    // 0xb11d6c: r1 = 1
    //     0xb11d6c: movz            x1, #0x1
    // 0xb11d70: r0 = AllocateContext()
    //     0xb11d70: bl              #0xec126c  ; AllocateContextStub
    // 0xb11d74: mov             x2, x0
    // 0xb11d78: ldur            x1, [fp, #-0x48]
    // 0xb11d7c: stur            x2, [fp, #-0x50]
    // 0xb11d80: StoreField: r2->field_f = r1
    //     0xb11d80: stur            w1, [x2, #0xf]
    // 0xb11d84: LoadField: r0 = r1->field_1b
    //     0xb11d84: ldur            w0, [x1, #0x1b]
    // 0xb11d88: DecompressPointer r0
    //     0xb11d88: add             x0, x0, HEAP, lsl #32
    // 0xb11d8c: r3 = LoadClassIdInstr(r0)
    //     0xb11d8c: ldur            x3, [x0, #-1]
    //     0xb11d90: ubfx            x3, x3, #0xc, #0x14
    // 0xb11d94: r16 = "nu"
    //     0xb11d94: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "nu"
    //     0xb11d98: ldr             x16, [x16, #0xdf8]
    // 0xb11d9c: stp             x16, x0, [SP]
    // 0xb11da0: mov             x0, x3
    // 0xb11da4: mov             lr, x0
    // 0xb11da8: ldr             lr, [x21, lr, lsl #3]
    // 0xb11dac: blr             lr
    // 0xb11db0: tbnz            w0, #4, #0xb11f0c
    // 0xb11db4: ldur            x0, [fp, #-0x48]
    // 0xb11db8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb11db8: ldur            w2, [x0, #0x17]
    // 0xb11dbc: DecompressPointer r2
    //     0xb11dbc: add             x2, x2, HEAP, lsl #32
    // 0xb11dc0: r16 = Sentinel
    //     0xb11dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11dc4: cmp             w2, w16
    // 0xb11dc8: b.eq            #0xb11fdc
    // 0xb11dcc: mov             x1, x2
    // 0xb11dd0: stur            x2, [fp, #-0x58]
    // 0xb11dd4: r0 = checkOpen()
    //     0xb11dd4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb11dd8: ldur            x0, [fp, #-0x58]
    // 0xb11ddc: LoadField: r1 = r0->field_1b
    //     0xb11ddc: ldur            w1, [x0, #0x1b]
    // 0xb11de0: DecompressPointer r1
    //     0xb11de0: add             x1, x1, HEAP, lsl #32
    // 0xb11de4: r16 = Sentinel
    //     0xb11de4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11de8: cmp             w1, w16
    // 0xb11dec: b.eq            #0xb11fe8
    // 0xb11df0: r0 = getValues()
    //     0xb11df0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb11df4: ldur            x2, [fp, #-0x50]
    // 0xb11df8: r1 = Function '<anonymous closure>':.
    //     0xb11df8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e748] AnonymousClosure: (0xb12014), in [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::toGregorian (0xb11d50)
    //     0xb11dfc: ldr             x1, [x1, #0x748]
    // 0xb11e00: stur            x0, [fp, #-0x58]
    // 0xb11e04: r0 = AllocateClosure()
    //     0xb11e04: bl              #0xec1630  ; AllocateClosureStub
    // 0xb11e08: ldur            x1, [fp, #-0x58]
    // 0xb11e0c: mov             x2, x0
    // 0xb11e10: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb11e10: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb11e14: r0 = firstWhere()
    //     0xb11e14: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0xb11e18: LoadField: r1 = r0->field_1f
    //     0xb11e18: ldur            w1, [x0, #0x1f]
    // 0xb11e1c: DecompressPointer r1
    //     0xb11e1c: add             x1, x1, HEAP, lsl #32
    // 0xb11e20: r0 = LoadClassIdInstr(r1)
    //     0xb11e20: ldur            x0, [x1, #-1]
    //     0xb11e24: ubfx            x0, x0, #0xc, #0x14
    // 0xb11e28: r2 = "-"
    //     0xb11e28: ldr             x2, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb11e2c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb11e2c: sub             lr, x0, #1, lsl #12
    //     0xb11e30: ldr             lr, [x21, lr, lsl #3]
    //     0xb11e34: blr             lr
    // 0xb11e38: r16 = <int>
    //     0xb11e38: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb11e3c: stp             x0, x16, [SP, #8]
    // 0xb11e40: r16 = Closure: (String, {int? radix, ((String) => int)? onError}) => int from Function 'parse': static.
    //     0xb11e40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e750] Closure: (String, {int? radix, ((String) => int)? onError}) => int from Function 'parse': static. (0x7e54fb00653c)
    //     0xb11e44: ldr             x16, [x16, #0x750]
    // 0xb11e48: str             x16, [SP]
    // 0xb11e4c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb11e4c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb11e50: r0 = map()
    //     0xb11e50: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb11e54: stur            x0, [fp, #-0x58]
    // 0xb11e58: LoadField: r3 = r0->field_7
    //     0xb11e58: ldur            w3, [x0, #7]
    // 0xb11e5c: DecompressPointer r3
    //     0xb11e5c: add             x3, x3, HEAP, lsl #32
    // 0xb11e60: mov             x1, x3
    // 0xb11e64: mov             x2, x0
    // 0xb11e68: stur            x3, [fp, #-0x50]
    // 0xb11e6c: r0 = _GrowableList.of()
    //     0xb11e6c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb11e70: stur            x0, [fp, #-0x50]
    // 0xb11e74: r0 = DateTime()
    //     0xb11e74: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb11e78: mov             x3, x0
    // 0xb11e7c: ldur            x2, [fp, #-0x50]
    // 0xb11e80: stur            x3, [fp, #-0x58]
    // 0xb11e84: LoadField: r0 = r2->field_b
    //     0xb11e84: ldur            w0, [x2, #0xb]
    // 0xb11e88: r4 = LoadInt32Instr(r0)
    //     0xb11e88: sbfx            x4, x0, #1, #0x1f
    // 0xb11e8c: mov             x0, x4
    // 0xb11e90: r1 = 0
    //     0xb11e90: movz            x1, #0
    // 0xb11e94: cmp             x1, x0
    // 0xb11e98: b.hs            #0xb11ff0
    // 0xb11e9c: LoadField: r5 = r2->field_f
    //     0xb11e9c: ldur            w5, [x2, #0xf]
    // 0xb11ea0: DecompressPointer r5
    //     0xb11ea0: add             x5, x5, HEAP, lsl #32
    // 0xb11ea4: LoadField: r6 = r5->field_f
    //     0xb11ea4: ldur            w6, [x5, #0xf]
    // 0xb11ea8: DecompressPointer r6
    //     0xb11ea8: add             x6, x6, HEAP, lsl #32
    // 0xb11eac: mov             x0, x4
    // 0xb11eb0: r1 = 1
    //     0xb11eb0: movz            x1, #0x1
    // 0xb11eb4: cmp             x1, x0
    // 0xb11eb8: b.hs            #0xb11ff4
    // 0xb11ebc: LoadField: r7 = r5->field_13
    //     0xb11ebc: ldur            w7, [x5, #0x13]
    // 0xb11ec0: DecompressPointer r7
    //     0xb11ec0: add             x7, x7, HEAP, lsl #32
    // 0xb11ec4: mov             x0, x4
    // 0xb11ec8: r1 = 2
    //     0xb11ec8: movz            x1, #0x2
    // 0xb11ecc: cmp             x1, x0
    // 0xb11ed0: b.hs            #0xb11ff8
    // 0xb11ed4: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xb11ed4: ldur            w0, [x5, #0x17]
    // 0xb11ed8: DecompressPointer r0
    //     0xb11ed8: add             x0, x0, HEAP, lsl #32
    // 0xb11edc: r2 = LoadInt32Instr(r6)
    //     0xb11edc: sbfx            x2, x6, #1, #0x1f
    //     0xb11ee0: tbz             w6, #0, #0xb11ee8
    //     0xb11ee4: ldur            x2, [x6, #7]
    // 0xb11ee8: stp             x0, x7, [SP]
    // 0xb11eec: mov             x1, x3
    // 0xb11ef0: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xb11ef0: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xb11ef4: ldr             x4, [x4, #0xe00]
    // 0xb11ef8: r0 = DateTime()
    //     0xb11ef8: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0xb11efc: ldur            x0, [fp, #-0x58]
    // 0xb11f00: LeaveFrame
    //     0xb11f00: mov             SP, fp
    //     0xb11f04: ldp             fp, lr, [SP], #0x10
    // 0xb11f08: ret
    //     0xb11f08: ret             
    // 0xb11f0c: ldur            x0, [fp, #-0x48]
    // 0xb11f10: r0 = HijriCalendar()
    //     0xb11f10: bl              #0x815098  ; AllocateHijriCalendarStub -> HijriCalendar (size=0x20)
    // 0xb11f14: mov             x2, x0
    // 0xb11f18: r0 = 1
    //     0xb11f18: movz            x0, #0x1
    // 0xb11f1c: StoreField: r2->field_7 = r0
    //     0xb11f1c: stur            x0, [x2, #7]
    // 0xb11f20: r0 = Sentinel
    //     0xb11f20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11f24: StoreField: r2->field_f = r0
    //     0xb11f24: stur            w0, [x2, #0xf]
    // 0xb11f28: StoreField: r2->field_13 = r0
    //     0xb11f28: stur            w0, [x2, #0x13]
    // 0xb11f2c: ldur            x0, [fp, #-0x48]
    // 0xb11f30: LoadField: r1 = r0->field_13
    //     0xb11f30: ldur            w1, [x0, #0x13]
    // 0xb11f34: DecompressPointer r1
    //     0xb11f34: add             x1, x1, HEAP, lsl #32
    // 0xb11f38: r16 = Sentinel
    //     0xb11f38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11f3c: cmp             w1, w16
    // 0xb11f40: b.eq            #0xb11ffc
    // 0xb11f44: LoadField: r3 = r0->field_f
    //     0xb11f44: ldur            w3, [x0, #0xf]
    // 0xb11f48: DecompressPointer r3
    //     0xb11f48: add             x3, x3, HEAP, lsl #32
    // 0xb11f4c: r16 = Sentinel
    //     0xb11f4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11f50: cmp             w3, w16
    // 0xb11f54: b.eq            #0xb12008
    // 0xb11f58: LoadField: r4 = r0->field_7
    //     0xb11f58: ldur            x4, [x0, #7]
    // 0xb11f5c: r5 = LoadInt32Instr(r1)
    //     0xb11f5c: sbfx            x5, x1, #1, #0x1f
    //     0xb11f60: tbz             w1, #0, #0xb11f68
    //     0xb11f64: ldur            x5, [x1, #7]
    // 0xb11f68: r0 = BoxInt64Instr(r4)
    //     0xb11f68: sbfiz           x0, x4, #1, #0x1f
    //     0xb11f6c: cmp             x4, x0, asr #1
    //     0xb11f70: b.eq            #0xb11f7c
    //     0xb11f74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb11f78: stur            x4, [x0, #7]
    // 0xb11f7c: mov             x1, x2
    // 0xb11f80: mov             x2, x5
    // 0xb11f84: mov             x5, x0
    // 0xb11f88: r0 = hijriToGregorian()
    //     0xb11f88: bl              #0x816c10  ; [package:hijri/hijri_calendar.dart] HijriCalendar::hijriToGregorian
    // 0xb11f8c: LeaveFrame
    //     0xb11f8c: mov             SP, fp
    //     0xb11f90: ldp             fp, lr, [SP], #0x10
    // 0xb11f94: ret
    //     0xb11f94: ret             
    // 0xb11f98: sub             SP, fp, #0x70
    // 0xb11f9c: r0 = DateTime()
    //     0xb11f9c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb11fa0: mov             x1, x0
    // 0xb11fa4: r0 = false
    //     0xb11fa4: add             x0, NULL, #0x30  ; false
    // 0xb11fa8: stur            x1, [fp, #-0x48]
    // 0xb11fac: StoreField: r1->field_13 = r0
    //     0xb11fac: stur            w0, [x1, #0x13]
    // 0xb11fb0: r0 = _getCurrentMicros()
    //     0xb11fb0: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb11fb4: r1 = LoadInt32Instr(r0)
    //     0xb11fb4: sbfx            x1, x0, #1, #0x1f
    //     0xb11fb8: tbz             w0, #0, #0xb11fc0
    //     0xb11fbc: ldur            x1, [x0, #7]
    // 0xb11fc0: ldur            x0, [fp, #-0x48]
    // 0xb11fc4: StoreField: r0->field_7 = r1
    //     0xb11fc4: stur            x1, [x0, #7]
    // 0xb11fc8: LeaveFrame
    //     0xb11fc8: mov             SP, fp
    //     0xb11fcc: ldp             fp, lr, [SP], #0x10
    // 0xb11fd0: ret
    //     0xb11fd0: ret             
    // 0xb11fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11fd8: b               #0xb11d6c
    // 0xb11fdc: r9 = hijriMapping
    //     0xb11fdc: add             x9, PP, #8, lsl #12  ; [pp+0x8e08] Field <NHijriCalendar.hijriMapping>: late (offset: 0x18)
    //     0xb11fe0: ldr             x9, [x9, #0xe08]
    // 0xb11fe4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb11fe4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb11fe8: r9 = keystore
    //     0xb11fe8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb11fec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb11fec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb11ff0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb11ff0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb11ff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb11ff4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb11ff8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb11ff8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb11ffc: r9 = hYear
    //     0xb11ffc: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0xb12000: ldr             x9, [x9, #0x270]
    // 0xb12004: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb12004: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb12008: r9 = hMonth
    //     0xb12008: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0xb1200c: ldr             x9, [x9, #0x278]
    // 0xb12010: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb12010: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, HijriDate) {
    // ** addr: 0xb12014, size: 0x74
    // 0xb12014: EnterFrame
    //     0xb12014: stp             fp, lr, [SP, #-0x10]!
    //     0xb12018: mov             fp, SP
    // 0xb1201c: AllocStack(0x28)
    //     0xb1201c: sub             SP, SP, #0x28
    // 0xb12020: SetupParameters()
    //     0xb12020: ldr             x0, [fp, #0x18]
    //     0xb12024: ldur            w2, [x0, #0x17]
    //     0xb12028: add             x2, x2, HEAP, lsl #32
    //     0xb1202c: stur            x2, [fp, #-8]
    // 0xb12030: CheckStackOverflow
    //     0xb12030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12034: cmp             SP, x16
    //     0xb12038: b.ls            #0xb12080
    // 0xb1203c: ldr             x1, [fp, #0x10]
    // 0xb12040: r0 = toMap()
    //     0xb12040: bl              #0x815350  ; [package:nuonline/app/data/models/calendar.dart] HijriDate::toMap
    // 0xb12044: mov             x2, x0
    // 0xb12048: ldur            x0, [fp, #-8]
    // 0xb1204c: stur            x2, [fp, #-0x10]
    // 0xb12050: LoadField: r1 = r0->field_f
    //     0xb12050: ldur            w1, [x0, #0xf]
    // 0xb12054: DecompressPointer r1
    //     0xb12054: add             x1, x1, HEAP, lsl #32
    // 0xb12058: r0 = toMap()
    //     0xb12058: bl              #0xb12088  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::toMap
    // 0xb1205c: r16 = <String, int>
    //     0xb1205c: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb12060: ldur            lr, [fp, #-0x10]
    // 0xb12064: stp             lr, x16, [SP, #8]
    // 0xb12068: str             x0, [SP]
    // 0xb1206c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xb1206c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xb12070: r0 = mapEquals()
    //     0xb12070: bl              #0x94bf78  ; [package:flutter/src/foundation/collections.dart] ::mapEquals
    // 0xb12074: LeaveFrame
    //     0xb12074: mov             SP, fp
    //     0xb12078: ldp             fp, lr, [SP], #0x10
    // 0xb1207c: ret
    //     0xb1207c: ret             
    // 0xb12080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb12084: b               #0xb1203c
  }
  _ toMap(/* No info */) {
    // ** addr: 0xb12088, size: 0xdc
    // 0xb12088: EnterFrame
    //     0xb12088: stp             fp, lr, [SP, #-0x10]!
    //     0xb1208c: mov             fp, SP
    // 0xb12090: AllocStack(0x18)
    //     0xb12090: sub             SP, SP, #0x18
    // 0xb12094: SetupParameters(NHijriCalendar this /* r1 => r0, fp-0x8 */)
    //     0xb12094: mov             x0, x1
    //     0xb12098: stur            x1, [fp, #-8]
    // 0xb1209c: CheckStackOverflow
    //     0xb1209c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb120a0: cmp             SP, x16
    //     0xb120a4: b.ls            #0xb12144
    // 0xb120a8: r1 = Null
    //     0xb120a8: mov             x1, NULL
    // 0xb120ac: r2 = 12
    //     0xb120ac: movz            x2, #0xc
    // 0xb120b0: r0 = AllocateArray()
    //     0xb120b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb120b4: mov             x2, x0
    // 0xb120b8: r16 = "day"
    //     0xb120b8: add             x16, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0xb120bc: ldr             x16, [x16, #0x340]
    // 0xb120c0: StoreField: r2->field_f = r16
    //     0xb120c0: stur            w16, [x2, #0xf]
    // 0xb120c4: ldur            x3, [fp, #-8]
    // 0xb120c8: LoadField: r4 = r3->field_7
    //     0xb120c8: ldur            x4, [x3, #7]
    // 0xb120cc: r0 = BoxInt64Instr(r4)
    //     0xb120cc: sbfiz           x0, x4, #1, #0x1f
    //     0xb120d0: cmp             x4, x0, asr #1
    //     0xb120d4: b.eq            #0xb120e0
    //     0xb120d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb120dc: stur            x4, [x0, #7]
    // 0xb120e0: StoreField: r2->field_13 = r0
    //     0xb120e0: stur            w0, [x2, #0x13]
    // 0xb120e4: r16 = "month"
    //     0xb120e4: add             x16, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0xb120e8: ldr             x16, [x16, #0x328]
    // 0xb120ec: ArrayStore: r2[0] = r16  ; List_4
    //     0xb120ec: stur            w16, [x2, #0x17]
    // 0xb120f0: LoadField: r0 = r3->field_f
    //     0xb120f0: ldur            w0, [x3, #0xf]
    // 0xb120f4: DecompressPointer r0
    //     0xb120f4: add             x0, x0, HEAP, lsl #32
    // 0xb120f8: r16 = Sentinel
    //     0xb120f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb120fc: cmp             w0, w16
    // 0xb12100: b.eq            #0xb1214c
    // 0xb12104: StoreField: r2->field_1b = r0
    //     0xb12104: stur            w0, [x2, #0x1b]
    // 0xb12108: r16 = "year"
    //     0xb12108: add             x16, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0xb1210c: ldr             x16, [x16, #0x310]
    // 0xb12110: StoreField: r2->field_1f = r16
    //     0xb12110: stur            w16, [x2, #0x1f]
    // 0xb12114: LoadField: r0 = r3->field_13
    //     0xb12114: ldur            w0, [x3, #0x13]
    // 0xb12118: DecompressPointer r0
    //     0xb12118: add             x0, x0, HEAP, lsl #32
    // 0xb1211c: r16 = Sentinel
    //     0xb1211c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb12120: cmp             w0, w16
    // 0xb12124: b.eq            #0xb12158
    // 0xb12128: StoreField: r2->field_23 = r0
    //     0xb12128: stur            w0, [x2, #0x23]
    // 0xb1212c: r16 = <String, int>
    //     0xb1212c: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb12130: stp             x2, x16, [SP]
    // 0xb12134: r0 = Map._fromLiteral()
    //     0xb12134: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb12138: LeaveFrame
    //     0xb12138: mov             SP, fp
    //     0xb1213c: ldp             fp, lr, [SP], #0x10
    // 0xb12140: ret
    //     0xb12140: ret             
    // 0xb12144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb12148: b               #0xb120a8
    // 0xb1214c: r9 = hMonth
    //     0xb1214c: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0xb12150: ldr             x9, [x9, #0x278]
    // 0xb12154: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb12154: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb12158: r9 = hYear
    //     0xb12158: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0xb1215c: ldr             x9, [x9, #0x270]
    // 0xb12160: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb12160: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ dM(/* No info */) {
    // ** addr: 0xb4fb80, size: 0x1cc
    // 0xb4fb80: EnterFrame
    //     0xb4fb80: stp             fp, lr, [SP, #-0x10]!
    //     0xb4fb84: mov             fp, SP
    // 0xb4fb88: AllocStack(0x20)
    //     0xb4fb88: sub             SP, SP, #0x20
    // 0xb4fb8c: CheckStackOverflow
    //     0xb4fb8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4fb90: cmp             SP, x16
    //     0xb4fb94: b.ls            #0xb4fd38
    // 0xb4fb98: LoadField: r2 = r1->field_7
    //     0xb4fb98: ldur            x2, [x1, #7]
    // 0xb4fb9c: LoadField: r0 = r1->field_f
    //     0xb4fb9c: ldur            w0, [x1, #0xf]
    // 0xb4fba0: DecompressPointer r0
    //     0xb4fba0: add             x0, x0, HEAP, lsl #32
    // 0xb4fba4: r16 = Sentinel
    //     0xb4fba4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb4fba8: cmp             w0, w16
    // 0xb4fbac: b.eq            #0xb4fd40
    // 0xb4fbb0: r1 = LoadInt32Instr(r0)
    //     0xb4fbb0: sbfx            x1, x0, #1, #0x1f
    //     0xb4fbb4: tbz             w0, #0, #0xb4fbbc
    //     0xb4fbb8: ldur            x1, [x0, #7]
    // 0xb4fbbc: cmp             x1, #6
    // 0xb4fbc0: b.gt            #0xb4fc3c
    // 0xb4fbc4: cmp             x1, #3
    // 0xb4fbc8: b.gt            #0xb4fc08
    // 0xb4fbcc: cmp             x1, #2
    // 0xb4fbd0: b.gt            #0xb4fbfc
    // 0xb4fbd4: cmp             x1, #1
    // 0xb4fbd8: b.gt            #0xb4fbf0
    // 0xb4fbdc: cmp             w0, #2
    // 0xb4fbe0: b.ne            #0xb4fcb4
    // 0xb4fbe4: r4 = "Muharam"
    //     0xb4fbe4: add             x4, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0xb4fbe8: ldr             x4, [x4, #0x210]
    // 0xb4fbec: b               #0xb4fcbc
    // 0xb4fbf0: r4 = "Safar"
    //     0xb4fbf0: add             x4, PP, #9, lsl #12  ; [pp+0x9218] "Safar"
    //     0xb4fbf4: ldr             x4, [x4, #0x218]
    // 0xb4fbf8: b               #0xb4fcbc
    // 0xb4fbfc: r4 = "Rabiul Awal"
    //     0xb4fbfc: add             x4, PP, #9, lsl #12  ; [pp+0x9220] "Rabiul Awal"
    //     0xb4fc00: ldr             x4, [x4, #0x220]
    // 0xb4fc04: b               #0xb4fcbc
    // 0xb4fc08: cmp             x1, #5
    // 0xb4fc0c: b.gt            #0xb4fc30
    // 0xb4fc10: cmp             x1, #4
    // 0xb4fc14: b.gt            #0xb4fc24
    // 0xb4fc18: r4 = "Rabiul Akhir"
    //     0xb4fc18: add             x4, PP, #9, lsl #12  ; [pp+0x9228] "Rabiul Akhir"
    //     0xb4fc1c: ldr             x4, [x4, #0x228]
    // 0xb4fc20: b               #0xb4fcbc
    // 0xb4fc24: r4 = "Jumadal Ula"
    //     0xb4fc24: add             x4, PP, #9, lsl #12  ; [pp+0x9230] "Jumadal Ula"
    //     0xb4fc28: ldr             x4, [x4, #0x230]
    // 0xb4fc2c: b               #0xb4fcbc
    // 0xb4fc30: r4 = "Jumadal Akhirah"
    //     0xb4fc30: add             x4, PP, #9, lsl #12  ; [pp+0x9238] "Jumadal Akhirah"
    //     0xb4fc34: ldr             x4, [x4, #0x238]
    // 0xb4fc38: b               #0xb4fcbc
    // 0xb4fc3c: cmp             x1, #9
    // 0xb4fc40: b.gt            #0xb4fc78
    // 0xb4fc44: cmp             x1, #8
    // 0xb4fc48: b.gt            #0xb4fc6c
    // 0xb4fc4c: cmp             x1, #7
    // 0xb4fc50: b.gt            #0xb4fc60
    // 0xb4fc54: r4 = "Rajab"
    //     0xb4fc54: add             x4, PP, #9, lsl #12  ; [pp+0x9240] "Rajab"
    //     0xb4fc58: ldr             x4, [x4, #0x240]
    // 0xb4fc5c: b               #0xb4fcbc
    // 0xb4fc60: r4 = "Sya\'ban"
    //     0xb4fc60: add             x4, PP, #9, lsl #12  ; [pp+0x9248] "Sya\'ban"
    //     0xb4fc64: ldr             x4, [x4, #0x248]
    // 0xb4fc68: b               #0xb4fcbc
    // 0xb4fc6c: r4 = "Ramadhan"
    //     0xb4fc6c: add             x4, PP, #9, lsl #12  ; [pp+0x9250] "Ramadhan"
    //     0xb4fc70: ldr             x4, [x4, #0x250]
    // 0xb4fc74: b               #0xb4fcbc
    // 0xb4fc78: cmp             x1, #0xb
    // 0xb4fc7c: b.gt            #0xb4fca0
    // 0xb4fc80: cmp             x1, #0xa
    // 0xb4fc84: b.gt            #0xb4fc94
    // 0xb4fc88: r4 = "Syawal"
    //     0xb4fc88: add             x4, PP, #9, lsl #12  ; [pp+0x9258] "Syawal"
    //     0xb4fc8c: ldr             x4, [x4, #0x258]
    // 0xb4fc90: b               #0xb4fcbc
    // 0xb4fc94: r4 = "Dzulqa\'dah"
    //     0xb4fc94: add             x4, PP, #9, lsl #12  ; [pp+0x9260] "Dzulqa\'dah"
    //     0xb4fc98: ldr             x4, [x4, #0x260]
    // 0xb4fc9c: b               #0xb4fcbc
    // 0xb4fca0: cmp             w0, #0x18
    // 0xb4fca4: b.ne            #0xb4fcb4
    // 0xb4fca8: r4 = "Dzulhijjah"
    //     0xb4fca8: add             x4, PP, #9, lsl #12  ; [pp+0x9268] "Dzulhijjah"
    //     0xb4fcac: ldr             x4, [x4, #0x268]
    // 0xb4fcb0: b               #0xb4fcbc
    // 0xb4fcb4: r4 = "Muharam"
    //     0xb4fcb4: add             x4, PP, #9, lsl #12  ; [pp+0x9210] "Muharam"
    //     0xb4fcb8: ldr             x4, [x4, #0x210]
    // 0xb4fcbc: r3 = 4
    //     0xb4fcbc: movz            x3, #0x4
    // 0xb4fcc0: stur            x4, [fp, #-0x10]
    // 0xb4fcc4: r0 = BoxInt64Instr(r2)
    //     0xb4fcc4: sbfiz           x0, x2, #1, #0x1f
    //     0xb4fcc8: cmp             x2, x0, asr #1
    //     0xb4fccc: b.eq            #0xb4fcd8
    //     0xb4fcd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb4fcd4: stur            x2, [x0, #7]
    // 0xb4fcd8: mov             x2, x3
    // 0xb4fcdc: r1 = Null
    //     0xb4fcdc: mov             x1, NULL
    // 0xb4fce0: stur            x0, [fp, #-8]
    // 0xb4fce4: r0 = AllocateArray()
    //     0xb4fce4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb4fce8: mov             x2, x0
    // 0xb4fcec: ldur            x0, [fp, #-8]
    // 0xb4fcf0: stur            x2, [fp, #-0x18]
    // 0xb4fcf4: StoreField: r2->field_f = r0
    //     0xb4fcf4: stur            w0, [x2, #0xf]
    // 0xb4fcf8: ldur            x0, [fp, #-0x10]
    // 0xb4fcfc: StoreField: r2->field_13 = r0
    //     0xb4fcfc: stur            w0, [x2, #0x13]
    // 0xb4fd00: r1 = <Object>
    //     0xb4fd00: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0xb4fd04: r0 = AllocateGrowableArray()
    //     0xb4fd04: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb4fd08: mov             x1, x0
    // 0xb4fd0c: ldur            x0, [fp, #-0x18]
    // 0xb4fd10: StoreField: r1->field_f = r0
    //     0xb4fd10: stur            w0, [x1, #0xf]
    // 0xb4fd14: r0 = 4
    //     0xb4fd14: movz            x0, #0x4
    // 0xb4fd18: StoreField: r1->field_b = r0
    //     0xb4fd18: stur            w0, [x1, #0xb]
    // 0xb4fd1c: r16 = " "
    //     0xb4fd1c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb4fd20: str             x16, [SP]
    // 0xb4fd24: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb4fd24: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb4fd28: r0 = join()
    //     0xb4fd28: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb4fd2c: LeaveFrame
    //     0xb4fd2c: mov             SP, fp
    //     0xb4fd30: ldp             fp, lr, [SP], #0x10
    // 0xb4fd34: ret
    //     0xb4fd34: ret             
    // 0xb4fd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4fd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4fd3c: b               #0xb4fb98
    // 0xb4fd40: r9 = hMonth
    //     0xb4fd40: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0xb4fd44: ldr             x9, [x9, #0x278]
    // 0xb4fd48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb4fd48: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
