// lib: , url: package:octo_image/src/image/fade_widget.dart

// class id: 1050745, size: 0x8
class :: {
}

// class id: 4106, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __FadeWidgetState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f9eac, size: 0x98
    // 0x6f9eac: EnterFrame
    //     0x6f9eac: stp             fp, lr, [SP, #-0x10]!
    //     0x6f9eb0: mov             fp, SP
    // 0x6f9eb4: AllocStack(0x10)
    //     0x6f9eb4: sub             SP, SP, #0x10
    // 0x6f9eb8: SetupParameters(__FadeWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f9eb8: stur            x1, [fp, #-8]
    //     0x6f9ebc: stur            x2, [fp, #-0x10]
    // 0x6f9ec0: CheckStackOverflow
    //     0x6f9ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f9ec4: cmp             SP, x16
    //     0x6f9ec8: b.ls            #0x6f9f38
    // 0x6f9ecc: r0 = Ticker()
    //     0x6f9ecc: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6f9ed0: mov             x1, x0
    // 0x6f9ed4: r0 = false
    //     0x6f9ed4: add             x0, NULL, #0x30  ; false
    // 0x6f9ed8: StoreField: r1->field_b = r0
    //     0x6f9ed8: stur            w0, [x1, #0xb]
    // 0x6f9edc: ldur            x0, [fp, #-0x10]
    // 0x6f9ee0: StoreField: r1->field_13 = r0
    //     0x6f9ee0: stur            w0, [x1, #0x13]
    // 0x6f9ee4: mov             x0, x1
    // 0x6f9ee8: ldur            x2, [fp, #-8]
    // 0x6f9eec: StoreField: r2->field_13 = r0
    //     0x6f9eec: stur            w0, [x2, #0x13]
    //     0x6f9ef0: ldurb           w16, [x2, #-1]
    //     0x6f9ef4: ldurb           w17, [x0, #-1]
    //     0x6f9ef8: and             x16, x17, x16, lsr #2
    //     0x6f9efc: tst             x16, HEAP, lsr #32
    //     0x6f9f00: b.eq            #0x6f9f08
    //     0x6f9f04: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6f9f08: mov             x1, x2
    // 0x6f9f0c: r0 = _updateTickerModeNotifier()
    //     0x6f9f0c: bl              #0x6f9f68  ; [package:octo_image/src/image/fade_widget.dart] __FadeWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f9f10: ldur            x1, [fp, #-8]
    // 0x6f9f14: r0 = _updateTicker()
    //     0x6f9f14: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f9f18: ldur            x1, [fp, #-8]
    // 0x6f9f1c: LoadField: r0 = r1->field_13
    //     0x6f9f1c: ldur            w0, [x1, #0x13]
    // 0x6f9f20: DecompressPointer r0
    //     0x6f9f20: add             x0, x0, HEAP, lsl #32
    // 0x6f9f24: cmp             w0, NULL
    // 0x6f9f28: b.eq            #0x6f9f40
    // 0x6f9f2c: LeaveFrame
    //     0x6f9f2c: mov             SP, fp
    //     0x6f9f30: ldp             fp, lr, [SP], #0x10
    // 0x6f9f34: ret
    //     0x6f9f34: ret             
    // 0x6f9f38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f9f38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f9f3c: b               #0x6f9ecc
    // 0x6f9f40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f9f40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f9f68, size: 0x124
    // 0x6f9f68: EnterFrame
    //     0x6f9f68: stp             fp, lr, [SP, #-0x10]!
    //     0x6f9f6c: mov             fp, SP
    // 0x6f9f70: AllocStack(0x18)
    //     0x6f9f70: sub             SP, SP, #0x18
    // 0x6f9f74: SetupParameters(__FadeWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f9f74: mov             x2, x1
    //     0x6f9f78: stur            x1, [fp, #-8]
    // 0x6f9f7c: CheckStackOverflow
    //     0x6f9f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f9f80: cmp             SP, x16
    //     0x6f9f84: b.ls            #0x6fa080
    // 0x6f9f88: LoadField: r1 = r2->field_f
    //     0x6f9f88: ldur            w1, [x2, #0xf]
    // 0x6f9f8c: DecompressPointer r1
    //     0x6f9f8c: add             x1, x1, HEAP, lsl #32
    // 0x6f9f90: cmp             w1, NULL
    // 0x6f9f94: b.eq            #0x6fa088
    // 0x6f9f98: r0 = getNotifier()
    //     0x6f9f98: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f9f9c: mov             x3, x0
    // 0x6f9fa0: ldur            x0, [fp, #-8]
    // 0x6f9fa4: stur            x3, [fp, #-0x18]
    // 0x6f9fa8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f9fa8: ldur            w4, [x0, #0x17]
    // 0x6f9fac: DecompressPointer r4
    //     0x6f9fac: add             x4, x4, HEAP, lsl #32
    // 0x6f9fb0: stur            x4, [fp, #-0x10]
    // 0x6f9fb4: cmp             w3, w4
    // 0x6f9fb8: b.ne            #0x6f9fcc
    // 0x6f9fbc: r0 = Null
    //     0x6f9fbc: mov             x0, NULL
    // 0x6f9fc0: LeaveFrame
    //     0x6f9fc0: mov             SP, fp
    //     0x6f9fc4: ldp             fp, lr, [SP], #0x10
    // 0x6f9fc8: ret
    //     0x6f9fc8: ret             
    // 0x6f9fcc: cmp             w4, NULL
    // 0x6f9fd0: b.eq            #0x6fa014
    // 0x6f9fd4: mov             x2, x0
    // 0x6f9fd8: r1 = Function '_updateTicker@364311458':.
    //     0x6f9fd8: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af78] AnonymousClosure: (0x6fa08c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f9fdc: ldr             x1, [x1, #0xf78]
    // 0x6f9fe0: r0 = AllocateClosure()
    //     0x6f9fe0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f9fe4: ldur            x1, [fp, #-0x10]
    // 0x6f9fe8: r2 = LoadClassIdInstr(r1)
    //     0x6f9fe8: ldur            x2, [x1, #-1]
    //     0x6f9fec: ubfx            x2, x2, #0xc, #0x14
    // 0x6f9ff0: mov             x16, x0
    // 0x6f9ff4: mov             x0, x2
    // 0x6f9ff8: mov             x2, x16
    // 0x6f9ffc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f9ffc: movz            x17, #0xbf5c
    //     0x6fa000: add             lr, x0, x17
    //     0x6fa004: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa008: blr             lr
    // 0x6fa00c: ldur            x0, [fp, #-8]
    // 0x6fa010: ldur            x3, [fp, #-0x18]
    // 0x6fa014: mov             x2, x0
    // 0x6fa018: r1 = Function '_updateTicker@364311458':.
    //     0x6fa018: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af78] AnonymousClosure: (0x6fa08c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6fa01c: ldr             x1, [x1, #0xf78]
    // 0x6fa020: r0 = AllocateClosure()
    //     0x6fa020: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fa024: ldur            x3, [fp, #-0x18]
    // 0x6fa028: r1 = LoadClassIdInstr(r3)
    //     0x6fa028: ldur            x1, [x3, #-1]
    //     0x6fa02c: ubfx            x1, x1, #0xc, #0x14
    // 0x6fa030: mov             x2, x0
    // 0x6fa034: mov             x0, x1
    // 0x6fa038: mov             x1, x3
    // 0x6fa03c: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6fa03c: movz            x17, #0xc407
    //     0x6fa040: add             lr, x0, x17
    //     0x6fa044: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa048: blr             lr
    // 0x6fa04c: ldur            x0, [fp, #-0x18]
    // 0x6fa050: ldur            x1, [fp, #-8]
    // 0x6fa054: ArrayStore: r1[0] = r0  ; List_4
    //     0x6fa054: stur            w0, [x1, #0x17]
    //     0x6fa058: ldurb           w16, [x1, #-1]
    //     0x6fa05c: ldurb           w17, [x0, #-1]
    //     0x6fa060: and             x16, x17, x16, lsr #2
    //     0x6fa064: tst             x16, HEAP, lsr #32
    //     0x6fa068: b.eq            #0x6fa070
    //     0x6fa06c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fa070: r0 = Null
    //     0x6fa070: mov             x0, NULL
    // 0x6fa074: LeaveFrame
    //     0x6fa074: mov             SP, fp
    //     0x6fa078: ldp             fp, lr, [SP], #0x10
    // 0x6fa07c: ret
    //     0x6fa07c: ret             
    // 0x6fa080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa084: b               #0x6f9f88
    // 0x6fa088: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa088: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6fa08c, size: 0x38
    // 0x6fa08c: EnterFrame
    //     0x6fa08c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa090: mov             fp, SP
    // 0x6fa094: ldr             x0, [fp, #0x10]
    // 0x6fa098: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fa098: ldur            w1, [x0, #0x17]
    // 0x6fa09c: DecompressPointer r1
    //     0x6fa09c: add             x1, x1, HEAP, lsl #32
    // 0x6fa0a0: CheckStackOverflow
    //     0x6fa0a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa0a4: cmp             SP, x16
    //     0x6fa0a8: b.ls            #0x6fa0bc
    // 0x6fa0ac: r0 = _updateTicker()
    //     0x6fa0ac: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6fa0b0: LeaveFrame
    //     0x6fa0b0: mov             SP, fp
    //     0x6fa0b4: ldp             fp, lr, [SP], #0x10
    // 0x6fa0b8: ret
    //     0x6fa0b8: ret             
    // 0x6fa0bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa0bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa0c0: b               #0x6fa0ac
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83a38, size: 0x94
    // 0xa83a38: EnterFrame
    //     0xa83a38: stp             fp, lr, [SP, #-0x10]!
    //     0xa83a3c: mov             fp, SP
    // 0xa83a40: AllocStack(0x10)
    //     0xa83a40: sub             SP, SP, #0x10
    // 0xa83a44: SetupParameters(__FadeWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83a44: mov             x0, x1
    //     0xa83a48: stur            x1, [fp, #-0x10]
    // 0xa83a4c: CheckStackOverflow
    //     0xa83a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83a50: cmp             SP, x16
    //     0xa83a54: b.ls            #0xa83ac4
    // 0xa83a58: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83a58: ldur            w3, [x0, #0x17]
    // 0xa83a5c: DecompressPointer r3
    //     0xa83a5c: add             x3, x3, HEAP, lsl #32
    // 0xa83a60: stur            x3, [fp, #-8]
    // 0xa83a64: cmp             w3, NULL
    // 0xa83a68: b.ne            #0xa83a74
    // 0xa83a6c: mov             x1, x0
    // 0xa83a70: b               #0xa83ab0
    // 0xa83a74: mov             x2, x0
    // 0xa83a78: r1 = Function '_updateTicker@364311458':.
    //     0xa83a78: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af78] AnonymousClosure: (0x6fa08c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa83a7c: ldr             x1, [x1, #0xf78]
    // 0xa83a80: r0 = AllocateClosure()
    //     0xa83a80: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83a84: ldur            x1, [fp, #-8]
    // 0xa83a88: r2 = LoadClassIdInstr(r1)
    //     0xa83a88: ldur            x2, [x1, #-1]
    //     0xa83a8c: ubfx            x2, x2, #0xc, #0x14
    // 0xa83a90: mov             x16, x0
    // 0xa83a94: mov             x0, x2
    // 0xa83a98: mov             x2, x16
    // 0xa83a9c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa83a9c: movz            x17, #0xbf5c
    //     0xa83aa0: add             lr, x0, x17
    //     0xa83aa4: ldr             lr, [x21, lr, lsl #3]
    //     0xa83aa8: blr             lr
    // 0xa83aac: ldur            x1, [fp, #-0x10]
    // 0xa83ab0: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa83ab0: stur            NULL, [x1, #0x17]
    // 0xa83ab4: r0 = Null
    //     0xa83ab4: mov             x0, NULL
    // 0xa83ab8: LeaveFrame
    //     0xa83ab8: mov             SP, fp
    //     0xa83abc: ldp             fp, lr, [SP], #0x10
    // 0xa83ac0: ret
    //     0xa83ac0: ret             
    // 0xa83ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83ac8: b               #0xa83a58
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85c60, size: 0x48
    // 0xa85c60: EnterFrame
    //     0xa85c60: stp             fp, lr, [SP, #-0x10]!
    //     0xa85c64: mov             fp, SP
    // 0xa85c68: AllocStack(0x8)
    //     0xa85c68: sub             SP, SP, #8
    // 0xa85c6c: SetupParameters(__FadeWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa85c6c: mov             x0, x1
    //     0xa85c70: stur            x1, [fp, #-8]
    // 0xa85c74: CheckStackOverflow
    //     0xa85c74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85c78: cmp             SP, x16
    //     0xa85c7c: b.ls            #0xa85ca0
    // 0xa85c80: mov             x1, x0
    // 0xa85c84: r0 = _updateTickerModeNotifier()
    //     0xa85c84: bl              #0x6f9f68  ; [package:octo_image/src/image/fade_widget.dart] __FadeWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85c88: ldur            x1, [fp, #-8]
    // 0xa85c8c: r0 = _updateTicker()
    //     0xa85c8c: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa85c90: r0 = Null
    //     0xa85c90: mov             x0, NULL
    // 0xa85c94: LeaveFrame
    //     0xa85c94: mov             SP, fp
    //     0xa85c98: ldp             fp, lr, [SP], #0x10
    // 0xa85c9c: ret
    //     0xa85c9c: ret             
    // 0xa85ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85ca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85ca4: b               #0xa85c80
  }
}

// class id: 4107, size: 0x28, field offset: 0x1c
class _FadeWidgetState extends __FadeWidgetState&State&SingleTickerProviderStateMixin {

  late Animation<double> opacity; // offset: 0x1c
  late AnimationController controller; // offset: 0x20
  late bool hideWidget; // offset: 0x24

  _ initState(/* No info */) {
    // ** addr: 0x97db9c, size: 0x2a0
    // 0x97db9c: EnterFrame
    //     0x97db9c: stp             fp, lr, [SP, #-0x10]!
    //     0x97dba0: mov             fp, SP
    // 0x97dba4: AllocStack(0x30)
    //     0x97dba4: sub             SP, SP, #0x30
    // 0x97dba8: SetupParameters(_FadeWidgetState this /* r1 => r2, fp-0x10 */)
    //     0x97dba8: mov             x2, x1
    //     0x97dbac: stur            x1, [fp, #-0x10]
    // 0x97dbb0: CheckStackOverflow
    //     0x97dbb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97dbb4: cmp             SP, x16
    //     0x97dbb8: b.ls            #0x97ddf4
    // 0x97dbbc: LoadField: r0 = r2->field_b
    //     0x97dbbc: ldur            w0, [x2, #0xb]
    // 0x97dbc0: DecompressPointer r0
    //     0x97dbc0: add             x0, x0, HEAP, lsl #32
    // 0x97dbc4: cmp             w0, NULL
    // 0x97dbc8: b.eq            #0x97ddfc
    // 0x97dbcc: LoadField: r3 = r0->field_f
    //     0x97dbcc: ldur            w3, [x0, #0xf]
    // 0x97dbd0: DecompressPointer r3
    //     0x97dbd0: add             x3, x3, HEAP, lsl #32
    // 0x97dbd4: stur            x3, [fp, #-8]
    // 0x97dbd8: r1 = <double>
    //     0x97dbd8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x97dbdc: r0 = AnimationController()
    //     0x97dbdc: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x97dbe0: stur            x0, [fp, #-0x18]
    // 0x97dbe4: ldur            x16, [fp, #-8]
    // 0x97dbe8: str             x16, [SP]
    // 0x97dbec: mov             x1, x0
    // 0x97dbf0: ldur            x2, [fp, #-0x10]
    // 0x97dbf4: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x97dbf4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x97dbf8: ldr             x4, [x4, #0x408]
    // 0x97dbfc: r0 = AnimationController()
    //     0x97dbfc: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x97dc00: ldur            x0, [fp, #-0x18]
    // 0x97dc04: ldur            x2, [fp, #-0x10]
    // 0x97dc08: StoreField: r2->field_1f = r0
    //     0x97dc08: stur            w0, [x2, #0x1f]
    //     0x97dc0c: ldurb           w16, [x2, #-1]
    //     0x97dc10: ldurb           w17, [x0, #-1]
    //     0x97dc14: and             x16, x17, x16, lsr #2
    //     0x97dc18: tst             x16, HEAP, lsr #32
    //     0x97dc1c: b.eq            #0x97dc24
    //     0x97dc20: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97dc24: LoadField: r0 = r2->field_b
    //     0x97dc24: ldur            w0, [x2, #0xb]
    // 0x97dc28: DecompressPointer r0
    //     0x97dc28: add             x0, x0, HEAP, lsl #32
    // 0x97dc2c: cmp             w0, NULL
    // 0x97dc30: b.eq            #0x97de00
    // 0x97dc34: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x97dc34: ldur            w3, [x0, #0x17]
    // 0x97dc38: DecompressPointer r3
    //     0x97dc38: add             x3, x3, HEAP, lsl #32
    // 0x97dc3c: stur            x3, [fp, #-8]
    // 0x97dc40: r1 = <double>
    //     0x97dc40: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x97dc44: r0 = CurvedAnimation()
    //     0x97dc44: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x97dc48: mov             x1, x0
    // 0x97dc4c: ldur            x2, [fp, #-8]
    // 0x97dc50: ldur            x3, [fp, #-0x18]
    // 0x97dc54: stur            x0, [fp, #-8]
    // 0x97dc58: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x97dc58: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x97dc5c: r0 = CurvedAnimation()
    //     0x97dc5c: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x97dc60: ldur            x2, [fp, #-0x10]
    // 0x97dc64: LoadField: r0 = r2->field_b
    //     0x97dc64: ldur            w0, [x2, #0xb]
    // 0x97dc68: DecompressPointer r0
    //     0x97dc68: add             x0, x0, HEAP, lsl #32
    // 0x97dc6c: cmp             w0, NULL
    // 0x97dc70: b.eq            #0x97de04
    // 0x97dc74: LoadField: r1 = r0->field_13
    //     0x97dc74: ldur            w1, [x0, #0x13]
    // 0x97dc78: DecompressPointer r1
    //     0x97dc78: add             x1, x1, HEAP, lsl #32
    // 0x97dc7c: r16 = Instance_AnimationDirection
    //     0x97dc7c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54738] Obj!AnimationDirection@e30081
    //     0x97dc80: ldr             x16, [x16, #0x738]
    // 0x97dc84: cmp             w1, w16
    // 0x97dc88: b.ne            #0x97dc94
    // 0x97dc8c: d0 = 0.000000
    //     0x97dc8c: eor             v0.16b, v0.16b, v0.16b
    // 0x97dc90: b               #0x97dc98
    // 0x97dc94: d0 = 1.000000
    //     0x97dc94: fmov            d0, #1.00000000
    // 0x97dc98: r16 = Instance_AnimationDirection
    //     0x97dc98: add             x16, PP, #0x54, lsl #12  ; [pp+0x54738] Obj!AnimationDirection@e30081
    //     0x97dc9c: ldr             x16, [x16, #0x738]
    // 0x97dca0: cmp             w1, w16
    // 0x97dca4: b.ne            #0x97dcb0
    // 0x97dca8: d1 = 1.000000
    //     0x97dca8: fmov            d1, #1.00000000
    // 0x97dcac: b               #0x97dcb4
    // 0x97dcb0: d1 = 0.000000
    //     0x97dcb0: eor             v1.16b, v1.16b, v1.16b
    // 0x97dcb4: stur            d1, [fp, #-0x20]
    // 0x97dcb8: r0 = inline_Allocate_Double()
    //     0x97dcb8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x97dcbc: add             x0, x0, #0x10
    //     0x97dcc0: cmp             x1, x0
    //     0x97dcc4: b.ls            #0x97de08
    //     0x97dcc8: str             x0, [THR, #0x50]  ; THR::top
    //     0x97dccc: sub             x0, x0, #0xf
    //     0x97dcd0: movz            x1, #0xe15c
    //     0x97dcd4: movk            x1, #0x3, lsl #16
    //     0x97dcd8: stur            x1, [x0, #-1]
    // 0x97dcdc: StoreField: r0->field_7 = d0
    //     0x97dcdc: stur            d0, [x0, #7]
    // 0x97dce0: stur            x0, [fp, #-0x18]
    // 0x97dce4: r1 = <double>
    //     0x97dce4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x97dce8: r0 = Tween()
    //     0x97dce8: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x97dcec: mov             x1, x0
    // 0x97dcf0: ldur            x0, [fp, #-0x18]
    // 0x97dcf4: StoreField: r1->field_b = r0
    //     0x97dcf4: stur            w0, [x1, #0xb]
    // 0x97dcf8: ldur            d0, [fp, #-0x20]
    // 0x97dcfc: r0 = inline_Allocate_Double()
    //     0x97dcfc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x97dd00: add             x0, x0, #0x10
    //     0x97dd04: cmp             x2, x0
    //     0x97dd08: b.ls            #0x97de20
    //     0x97dd0c: str             x0, [THR, #0x50]  ; THR::top
    //     0x97dd10: sub             x0, x0, #0xf
    //     0x97dd14: movz            x2, #0xe15c
    //     0x97dd18: movk            x2, #0x3, lsl #16
    //     0x97dd1c: stur            x2, [x0, #-1]
    // 0x97dd20: StoreField: r0->field_7 = d0
    //     0x97dd20: stur            d0, [x0, #7]
    // 0x97dd24: StoreField: r1->field_f = r0
    //     0x97dd24: stur            w0, [x1, #0xf]
    // 0x97dd28: ldur            x2, [fp, #-8]
    // 0x97dd2c: r0 = animate()
    //     0x97dd2c: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x97dd30: ldur            x2, [fp, #-0x10]
    // 0x97dd34: StoreField: r2->field_1b = r0
    //     0x97dd34: stur            w0, [x2, #0x1b]
    //     0x97dd38: ldurb           w16, [x2, #-1]
    //     0x97dd3c: ldurb           w17, [x0, #-1]
    //     0x97dd40: and             x16, x17, x16, lsr #2
    //     0x97dd44: tst             x16, HEAP, lsr #32
    //     0x97dd48: b.eq            #0x97dd50
    //     0x97dd4c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97dd50: LoadField: r1 = r2->field_1f
    //     0x97dd50: ldur            w1, [x2, #0x1f]
    // 0x97dd54: DecompressPointer r1
    //     0x97dd54: add             x1, x1, HEAP, lsl #32
    // 0x97dd58: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97dd58: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97dd5c: r0 = forward()
    //     0x97dd5c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x97dd60: ldur            x2, [fp, #-0x10]
    // 0x97dd64: r0 = false
    //     0x97dd64: add             x0, NULL, #0x30  ; false
    // 0x97dd68: StoreField: r2->field_23 = r0
    //     0x97dd68: stur            w0, [x2, #0x23]
    // 0x97dd6c: LoadField: r0 = r2->field_b
    //     0x97dd6c: ldur            w0, [x2, #0xb]
    // 0x97dd70: DecompressPointer r0
    //     0x97dd70: add             x0, x0, HEAP, lsl #32
    // 0x97dd74: cmp             w0, NULL
    // 0x97dd78: b.eq            #0x97de38
    // 0x97dd7c: LoadField: r1 = r0->field_13
    //     0x97dd7c: ldur            w1, [x0, #0x13]
    // 0x97dd80: DecompressPointer r1
    //     0x97dd80: add             x1, x1, HEAP, lsl #32
    // 0x97dd84: r16 = Instance_AnimationDirection
    //     0x97dd84: add             x16, PP, #0x54, lsl #12  ; [pp+0x54740] Obj!AnimationDirection@e30061
    //     0x97dd88: ldr             x16, [x16, #0x740]
    // 0x97dd8c: cmp             w1, w16
    // 0x97dd90: b.ne            #0x97dde4
    // 0x97dd94: LoadField: r1 = r0->field_f
    //     0x97dd94: ldur            w1, [x0, #0xf]
    // 0x97dd98: DecompressPointer r1
    //     0x97dd98: add             x1, x1, HEAP, lsl #32
    // 0x97dd9c: r16 = Instance_Duration
    //     0x97dd9c: ldr             x16, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x97dda0: stp             x16, x1, [SP]
    // 0x97dda4: r0 = ==()
    //     0x97dda4: bl              #0xd2c01c  ; [dart:core] Duration::==
    // 0x97dda8: tbnz            w0, #4, #0x97ddbc
    // 0x97ddac: ldur            x2, [fp, #-0x10]
    // 0x97ddb0: r0 = true
    //     0x97ddb0: add             x0, NULL, #0x20  ; true
    // 0x97ddb4: StoreField: r2->field_23 = r0
    //     0x97ddb4: stur            w0, [x2, #0x23]
    // 0x97ddb8: b               #0x97dde4
    // 0x97ddbc: ldur            x2, [fp, #-0x10]
    // 0x97ddc0: LoadField: r0 = r2->field_1b
    //     0x97ddc0: ldur            w0, [x2, #0x1b]
    // 0x97ddc4: DecompressPointer r0
    //     0x97ddc4: add             x0, x0, HEAP, lsl #32
    // 0x97ddc8: stur            x0, [fp, #-8]
    // 0x97ddcc: r1 = Function 'animationStatusChange':.
    //     0x97ddcc: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af28] AnonymousClosure: (0x97de3c), in [package:octo_image/src/image/fade_widget.dart] _FadeWidgetState::animationStatusChange (0x97de78)
    //     0x97ddd0: ldr             x1, [x1, #0xf28]
    // 0x97ddd4: r0 = AllocateClosure()
    //     0x97ddd4: bl              #0xec1630  ; AllocateClosureStub
    // 0x97ddd8: ldur            x1, [fp, #-8]
    // 0x97dddc: mov             x2, x0
    // 0x97dde0: r0 = addStatusListener()
    //     0x97dde0: bl              #0xd25438  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::addStatusListener
    // 0x97dde4: r0 = Null
    //     0x97dde4: mov             x0, NULL
    // 0x97dde8: LeaveFrame
    //     0x97dde8: mov             SP, fp
    //     0x97ddec: ldp             fp, lr, [SP], #0x10
    // 0x97ddf0: ret
    //     0x97ddf0: ret             
    // 0x97ddf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ddf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ddf8: b               #0x97dbbc
    // 0x97ddfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ddfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97de00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97de00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97de04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97de04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97de08: stp             q0, q1, [SP, #-0x20]!
    // 0x97de0c: SaveReg r2
    //     0x97de0c: str             x2, [SP, #-8]!
    // 0x97de10: r0 = AllocateDouble()
    //     0x97de10: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97de14: RestoreReg r2
    //     0x97de14: ldr             x2, [SP], #8
    // 0x97de18: ldp             q0, q1, [SP], #0x20
    // 0x97de1c: b               #0x97dcdc
    // 0x97de20: SaveReg d0
    //     0x97de20: str             q0, [SP, #-0x10]!
    // 0x97de24: SaveReg r1
    //     0x97de24: str             x1, [SP, #-8]!
    // 0x97de28: r0 = AllocateDouble()
    //     0x97de28: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97de2c: RestoreReg r1
    //     0x97de2c: ldr             x1, [SP], #8
    // 0x97de30: RestoreReg d0
    //     0x97de30: ldr             q0, [SP], #0x10
    // 0x97de34: b               #0x97dd20
    // 0x97de38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97de38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void animationStatusChange(dynamic, AnimationStatus) {
    // ** addr: 0x97de3c, size: 0x3c
    // 0x97de3c: EnterFrame
    //     0x97de3c: stp             fp, lr, [SP, #-0x10]!
    //     0x97de40: mov             fp, SP
    // 0x97de44: ldr             x0, [fp, #0x18]
    // 0x97de48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97de48: ldur            w1, [x0, #0x17]
    // 0x97de4c: DecompressPointer r1
    //     0x97de4c: add             x1, x1, HEAP, lsl #32
    // 0x97de50: CheckStackOverflow
    //     0x97de50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97de54: cmp             SP, x16
    //     0x97de58: b.ls            #0x97de70
    // 0x97de5c: ldr             x2, [fp, #0x10]
    // 0x97de60: r0 = animationStatusChange()
    //     0x97de60: bl              #0x97de78  ; [package:octo_image/src/image/fade_widget.dart] _FadeWidgetState::animationStatusChange
    // 0x97de64: LeaveFrame
    //     0x97de64: mov             SP, fp
    //     0x97de68: ldp             fp, lr, [SP], #0x10
    // 0x97de6c: ret
    //     0x97de6c: ret             
    // 0x97de70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97de70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97de74: b               #0x97de5c
  }
  _ animationStatusChange(/* No info */) {
    // ** addr: 0x97de78, size: 0x70
    // 0x97de78: EnterFrame
    //     0x97de78: stp             fp, lr, [SP, #-0x10]!
    //     0x97de7c: mov             fp, SP
    // 0x97de80: AllocStack(0x10)
    //     0x97de80: sub             SP, SP, #0x10
    // 0x97de84: SetupParameters(_FadeWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x97de84: stur            x1, [fp, #-8]
    //     0x97de88: stur            x2, [fp, #-0x10]
    // 0x97de8c: CheckStackOverflow
    //     0x97de8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97de90: cmp             SP, x16
    //     0x97de94: b.ls            #0x97dee0
    // 0x97de98: r1 = 2
    //     0x97de98: movz            x1, #0x2
    // 0x97de9c: r0 = AllocateContext()
    //     0x97de9c: bl              #0xec126c  ; AllocateContextStub
    // 0x97dea0: mov             x1, x0
    // 0x97dea4: ldur            x0, [fp, #-8]
    // 0x97dea8: StoreField: r1->field_f = r0
    //     0x97dea8: stur            w0, [x1, #0xf]
    // 0x97deac: ldur            x2, [fp, #-0x10]
    // 0x97deb0: StoreField: r1->field_13 = r2
    //     0x97deb0: stur            w2, [x1, #0x13]
    // 0x97deb4: mov             x2, x1
    // 0x97deb8: r1 = Function '<anonymous closure>':.
    //     0x97deb8: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af40] AnonymousClosure: (0x97dee8), in [package:octo_image/src/image/fade_widget.dart] _FadeWidgetState::animationStatusChange (0x97de78)
    //     0x97debc: ldr             x1, [x1, #0xf40]
    // 0x97dec0: r0 = AllocateClosure()
    //     0x97dec0: bl              #0xec1630  ; AllocateClosureStub
    // 0x97dec4: ldur            x1, [fp, #-8]
    // 0x97dec8: mov             x2, x0
    // 0x97decc: r0 = setState()
    //     0x97decc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x97ded0: r0 = Null
    //     0x97ded0: mov             x0, NULL
    // 0x97ded4: LeaveFrame
    //     0x97ded4: mov             SP, fp
    //     0x97ded8: ldp             fp, lr, [SP], #0x10
    // 0x97dedc: ret
    //     0x97dedc: ret             
    // 0x97dee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97dee0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97dee4: b               #0x97de98
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x97dee8, size: 0x78
    // 0x97dee8: ldr             x1, [SP]
    // 0x97deec: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x97deec: ldur            w2, [x1, #0x17]
    // 0x97def0: DecompressPointer r2
    //     0x97def0: add             x2, x2, HEAP, lsl #32
    // 0x97def4: LoadField: r1 = r2->field_f
    //     0x97def4: ldur            w1, [x2, #0xf]
    // 0x97def8: DecompressPointer r1
    //     0x97def8: add             x1, x1, HEAP, lsl #32
    // 0x97defc: LoadField: r3 = r1->field_b
    //     0x97defc: ldur            w3, [x1, #0xb]
    // 0x97df00: DecompressPointer r3
    //     0x97df00: add             x3, x3, HEAP, lsl #32
    // 0x97df04: cmp             w3, NULL
    // 0x97df08: b.eq            #0x97df54
    // 0x97df0c: LoadField: r4 = r3->field_13
    //     0x97df0c: ldur            w4, [x3, #0x13]
    // 0x97df10: DecompressPointer r4
    //     0x97df10: add             x4, x4, HEAP, lsl #32
    // 0x97df14: r16 = Instance_AnimationDirection
    //     0x97df14: add             x16, PP, #0x54, lsl #12  ; [pp+0x54740] Obj!AnimationDirection@e30061
    //     0x97df18: ldr             x16, [x16, #0x740]
    // 0x97df1c: cmp             w4, w16
    // 0x97df20: b.ne            #0x97df44
    // 0x97df24: LoadField: r3 = r2->field_13
    //     0x97df24: ldur            w3, [x2, #0x13]
    // 0x97df28: DecompressPointer r3
    //     0x97df28: add             x3, x3, HEAP, lsl #32
    // 0x97df2c: r16 = Instance_AnimationStatus
    //     0x97df2c: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x97df30: cmp             w3, w16
    // 0x97df34: r16 = true
    //     0x97df34: add             x16, NULL, #0x20  ; true
    // 0x97df38: r17 = false
    //     0x97df38: add             x17, NULL, #0x30  ; false
    // 0x97df3c: csel            x2, x16, x17, eq
    // 0x97df40: b               #0x97df48
    // 0x97df44: r2 = false
    //     0x97df44: add             x2, NULL, #0x30  ; false
    // 0x97df48: StoreField: r1->field_23 = r2
    //     0x97df48: stur            w2, [x1, #0x23]
    // 0x97df4c: r0 = Null
    //     0x97df4c: mov             x0, NULL
    // 0x97df50: ret
    //     0x97df50: ret             
    // 0x97df54: EnterFrame
    //     0x97df54: stp             fp, lr, [SP, #-0x10]!
    //     0x97df58: mov             fp, SP
    // 0x97df5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97df5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a0bb0, size: 0x3ac
    // 0x9a0bb0: EnterFrame
    //     0x9a0bb0: stp             fp, lr, [SP, #-0x10]!
    //     0x9a0bb4: mov             fp, SP
    // 0x9a0bb8: AllocStack(0x38)
    //     0x9a0bb8: sub             SP, SP, #0x38
    // 0x9a0bbc: SetupParameters(_FadeWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a0bbc: mov             x4, x1
    //     0x9a0bc0: mov             x3, x2
    //     0x9a0bc4: stur            x1, [fp, #-8]
    //     0x9a0bc8: stur            x2, [fp, #-0x10]
    // 0x9a0bcc: CheckStackOverflow
    //     0x9a0bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a0bd0: cmp             SP, x16
    //     0x9a0bd4: b.ls            #0x9a0ef4
    // 0x9a0bd8: mov             x0, x3
    // 0x9a0bdc: r2 = Null
    //     0x9a0bdc: mov             x2, NULL
    // 0x9a0be0: r1 = Null
    //     0x9a0be0: mov             x1, NULL
    // 0x9a0be4: r4 = 60
    //     0x9a0be4: movz            x4, #0x3c
    // 0x9a0be8: branchIfSmi(r0, 0x9a0bf4)
    //     0x9a0be8: tbz             w0, #0, #0x9a0bf4
    // 0x9a0bec: r4 = LoadClassIdInstr(r0)
    //     0x9a0bec: ldur            x4, [x0, #-1]
    //     0x9a0bf0: ubfx            x4, x4, #0xc, #0x14
    // 0x9a0bf4: r17 = 4705
    //     0x9a0bf4: movz            x17, #0x1261
    // 0x9a0bf8: cmp             x4, x17
    // 0x9a0bfc: b.eq            #0x9a0c14
    // 0x9a0c00: r8 = FadeWidget
    //     0x9a0c00: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5af48] Type: FadeWidget
    //     0x9a0c04: ldr             x8, [x8, #0xf48]
    // 0x9a0c08: r3 = Null
    //     0x9a0c08: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5af50] Null
    //     0x9a0c0c: ldr             x3, [x3, #0xf50]
    // 0x9a0c10: r0 = FadeWidget()
    //     0x9a0c10: bl              #0x6f9f44  ; IsType_FadeWidget_Stub
    // 0x9a0c14: ldur            x3, [fp, #-8]
    // 0x9a0c18: LoadField: r2 = r3->field_7
    //     0x9a0c18: ldur            w2, [x3, #7]
    // 0x9a0c1c: DecompressPointer r2
    //     0x9a0c1c: add             x2, x2, HEAP, lsl #32
    // 0x9a0c20: ldur            x0, [fp, #-0x10]
    // 0x9a0c24: r1 = Null
    //     0x9a0c24: mov             x1, NULL
    // 0x9a0c28: cmp             w2, NULL
    // 0x9a0c2c: b.eq            #0x9a0c50
    // 0x9a0c30: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a0c30: ldur            w4, [x2, #0x17]
    // 0x9a0c34: DecompressPointer r4
    //     0x9a0c34: add             x4, x4, HEAP, lsl #32
    // 0x9a0c38: r8 = X0 bound StatefulWidget
    //     0x9a0c38: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a0c3c: ldr             x8, [x8, #0x7f8]
    // 0x9a0c40: LoadField: r9 = r4->field_7
    //     0x9a0c40: ldur            x9, [x4, #7]
    // 0x9a0c44: r3 = Null
    //     0x9a0c44: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5af60] Null
    //     0x9a0c48: ldr             x3, [x3, #0xf60]
    // 0x9a0c4c: blr             x9
    // 0x9a0c50: ldur            x0, [fp, #-0x10]
    // 0x9a0c54: LoadField: r1 = r0->field_b
    //     0x9a0c54: ldur            w1, [x0, #0xb]
    // 0x9a0c58: DecompressPointer r1
    //     0x9a0c58: add             x1, x1, HEAP, lsl #32
    // 0x9a0c5c: ldur            x0, [fp, #-8]
    // 0x9a0c60: LoadField: r2 = r0->field_b
    //     0x9a0c60: ldur            w2, [x0, #0xb]
    // 0x9a0c64: DecompressPointer r2
    //     0x9a0c64: add             x2, x2, HEAP, lsl #32
    // 0x9a0c68: cmp             w2, NULL
    // 0x9a0c6c: b.eq            #0x9a0efc
    // 0x9a0c70: LoadField: r3 = r2->field_b
    //     0x9a0c70: ldur            w3, [x2, #0xb]
    // 0x9a0c74: DecompressPointer r3
    //     0x9a0c74: add             x3, x3, HEAP, lsl #32
    // 0x9a0c78: mov             x2, x3
    // 0x9a0c7c: r0 = canUpdate()
    //     0x9a0c7c: bl              #0x86bb0c  ; [package:flutter/src/widgets/framework.dart] Widget::canUpdate
    // 0x9a0c80: tbnz            w0, #4, #0x9a0c94
    // 0x9a0c84: r0 = Null
    //     0x9a0c84: mov             x0, NULL
    // 0x9a0c88: LeaveFrame
    //     0x9a0c88: mov             SP, fp
    //     0x9a0c8c: ldp             fp, lr, [SP], #0x10
    // 0x9a0c90: ret
    //     0x9a0c90: ret             
    // 0x9a0c94: ldur            x0, [fp, #-8]
    // 0x9a0c98: LoadField: r3 = r0->field_1b
    //     0x9a0c98: ldur            w3, [x0, #0x1b]
    // 0x9a0c9c: DecompressPointer r3
    //     0x9a0c9c: add             x3, x3, HEAP, lsl #32
    // 0x9a0ca0: r16 = Sentinel
    //     0x9a0ca0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a0ca4: cmp             w3, w16
    // 0x9a0ca8: b.eq            #0x9a0f00
    // 0x9a0cac: mov             x2, x0
    // 0x9a0cb0: stur            x3, [fp, #-0x10]
    // 0x9a0cb4: r1 = Function 'animationStatusChange':.
    //     0x9a0cb4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af28] AnonymousClosure: (0x97de3c), in [package:octo_image/src/image/fade_widget.dart] _FadeWidgetState::animationStatusChange (0x97de78)
    //     0x9a0cb8: ldr             x1, [x1, #0xf28]
    // 0x9a0cbc: r0 = AllocateClosure()
    //     0x9a0cbc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a0cc0: ldur            x1, [fp, #-0x10]
    // 0x9a0cc4: mov             x2, x0
    // 0x9a0cc8: stur            x0, [fp, #-0x10]
    // 0x9a0ccc: r0 = removeStatusListener()
    //     0x9a0ccc: bl              #0xd368f8  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::removeStatusListener
    // 0x9a0cd0: ldur            x2, [fp, #-8]
    // 0x9a0cd4: LoadField: r1 = r2->field_1f
    //     0x9a0cd4: ldur            w1, [x2, #0x1f]
    // 0x9a0cd8: DecompressPointer r1
    //     0x9a0cd8: add             x1, x1, HEAP, lsl #32
    // 0x9a0cdc: r16 = Sentinel
    //     0x9a0cdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a0ce0: cmp             w1, w16
    // 0x9a0ce4: b.eq            #0x9a0f0c
    // 0x9a0ce8: LoadField: r0 = r2->field_b
    //     0x9a0ce8: ldur            w0, [x2, #0xb]
    // 0x9a0cec: DecompressPointer r0
    //     0x9a0cec: add             x0, x0, HEAP, lsl #32
    // 0x9a0cf0: cmp             w0, NULL
    // 0x9a0cf4: b.eq            #0x9a0f18
    // 0x9a0cf8: LoadField: r3 = r0->field_f
    //     0x9a0cf8: ldur            w3, [x0, #0xf]
    // 0x9a0cfc: DecompressPointer r3
    //     0x9a0cfc: add             x3, x3, HEAP, lsl #32
    // 0x9a0d00: mov             x0, x3
    // 0x9a0d04: StoreField: r1->field_27 = r0
    //     0x9a0d04: stur            w0, [x1, #0x27]
    //     0x9a0d08: ldurb           w16, [x1, #-1]
    //     0x9a0d0c: ldurb           w17, [x0, #-1]
    //     0x9a0d10: and             x16, x17, x16, lsr #2
    //     0x9a0d14: tst             x16, HEAP, lsr #32
    //     0x9a0d18: b.eq            #0x9a0d20
    //     0x9a0d1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a0d20: d0 = 0.000000
    //     0x9a0d20: eor             v0.16b, v0.16b, v0.16b
    // 0x9a0d24: r0 = value=()
    //     0x9a0d24: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x9a0d28: ldur            x0, [fp, #-8]
    // 0x9a0d2c: LoadField: r3 = r0->field_1f
    //     0x9a0d2c: ldur            w3, [x0, #0x1f]
    // 0x9a0d30: DecompressPointer r3
    //     0x9a0d30: add             x3, x3, HEAP, lsl #32
    // 0x9a0d34: stur            x3, [fp, #-0x20]
    // 0x9a0d38: LoadField: r1 = r0->field_b
    //     0x9a0d38: ldur            w1, [x0, #0xb]
    // 0x9a0d3c: DecompressPointer r1
    //     0x9a0d3c: add             x1, x1, HEAP, lsl #32
    // 0x9a0d40: cmp             w1, NULL
    // 0x9a0d44: b.eq            #0x9a0f1c
    // 0x9a0d48: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9a0d48: ldur            w2, [x1, #0x17]
    // 0x9a0d4c: DecompressPointer r2
    //     0x9a0d4c: add             x2, x2, HEAP, lsl #32
    // 0x9a0d50: stur            x2, [fp, #-0x18]
    // 0x9a0d54: r1 = <double>
    //     0x9a0d54: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9a0d58: r0 = CurvedAnimation()
    //     0x9a0d58: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x9a0d5c: mov             x1, x0
    // 0x9a0d60: ldur            x2, [fp, #-0x18]
    // 0x9a0d64: ldur            x3, [fp, #-0x20]
    // 0x9a0d68: stur            x0, [fp, #-0x18]
    // 0x9a0d6c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x9a0d6c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x9a0d70: r0 = CurvedAnimation()
    //     0x9a0d70: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x9a0d74: ldur            x0, [fp, #-8]
    // 0x9a0d78: LoadField: r1 = r0->field_b
    //     0x9a0d78: ldur            w1, [x0, #0xb]
    // 0x9a0d7c: DecompressPointer r1
    //     0x9a0d7c: add             x1, x1, HEAP, lsl #32
    // 0x9a0d80: cmp             w1, NULL
    // 0x9a0d84: b.eq            #0x9a0f20
    // 0x9a0d88: LoadField: r2 = r1->field_13
    //     0x9a0d88: ldur            w2, [x1, #0x13]
    // 0x9a0d8c: DecompressPointer r2
    //     0x9a0d8c: add             x2, x2, HEAP, lsl #32
    // 0x9a0d90: r16 = Instance_AnimationDirection
    //     0x9a0d90: add             x16, PP, #0x54, lsl #12  ; [pp+0x54738] Obj!AnimationDirection@e30081
    //     0x9a0d94: ldr             x16, [x16, #0x738]
    // 0x9a0d98: cmp             w2, w16
    // 0x9a0d9c: b.ne            #0x9a0da8
    // 0x9a0da0: d0 = 0.000000
    //     0x9a0da0: eor             v0.16b, v0.16b, v0.16b
    // 0x9a0da4: b               #0x9a0dac
    // 0x9a0da8: d0 = 1.000000
    //     0x9a0da8: fmov            d0, #1.00000000
    // 0x9a0dac: r16 = Instance_AnimationDirection
    //     0x9a0dac: add             x16, PP, #0x54, lsl #12  ; [pp+0x54738] Obj!AnimationDirection@e30081
    //     0x9a0db0: ldr             x16, [x16, #0x738]
    // 0x9a0db4: cmp             w2, w16
    // 0x9a0db8: b.ne            #0x9a0dc4
    // 0x9a0dbc: d1 = 1.000000
    //     0x9a0dbc: fmov            d1, #1.00000000
    // 0x9a0dc0: b               #0x9a0dc8
    // 0x9a0dc4: d1 = 0.000000
    //     0x9a0dc4: eor             v1.16b, v1.16b, v1.16b
    // 0x9a0dc8: stur            d1, [fp, #-0x28]
    // 0x9a0dcc: r2 = inline_Allocate_Double()
    //     0x9a0dcc: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0x9a0dd0: add             x2, x2, #0x10
    //     0x9a0dd4: cmp             x1, x2
    //     0x9a0dd8: b.ls            #0x9a0f24
    //     0x9a0ddc: str             x2, [THR, #0x50]  ; THR::top
    //     0x9a0de0: sub             x2, x2, #0xf
    //     0x9a0de4: movz            x1, #0xe15c
    //     0x9a0de8: movk            x1, #0x3, lsl #16
    //     0x9a0dec: stur            x1, [x2, #-1]
    // 0x9a0df0: StoreField: r2->field_7 = d0
    //     0x9a0df0: stur            d0, [x2, #7]
    // 0x9a0df4: stur            x2, [fp, #-0x20]
    // 0x9a0df8: r1 = <double>
    //     0x9a0df8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9a0dfc: r0 = Tween()
    //     0x9a0dfc: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x9a0e00: mov             x1, x0
    // 0x9a0e04: ldur            x0, [fp, #-0x20]
    // 0x9a0e08: StoreField: r1->field_b = r0
    //     0x9a0e08: stur            w0, [x1, #0xb]
    // 0x9a0e0c: ldur            d0, [fp, #-0x28]
    // 0x9a0e10: r0 = inline_Allocate_Double()
    //     0x9a0e10: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x9a0e14: add             x0, x0, #0x10
    //     0x9a0e18: cmp             x2, x0
    //     0x9a0e1c: b.ls            #0x9a0f40
    //     0x9a0e20: str             x0, [THR, #0x50]  ; THR::top
    //     0x9a0e24: sub             x0, x0, #0xf
    //     0x9a0e28: movz            x2, #0xe15c
    //     0x9a0e2c: movk            x2, #0x3, lsl #16
    //     0x9a0e30: stur            x2, [x0, #-1]
    // 0x9a0e34: StoreField: r0->field_7 = d0
    //     0x9a0e34: stur            d0, [x0, #7]
    // 0x9a0e38: StoreField: r1->field_f = r0
    //     0x9a0e38: stur            w0, [x1, #0xf]
    // 0x9a0e3c: ldur            x2, [fp, #-0x18]
    // 0x9a0e40: r0 = animate()
    //     0x9a0e40: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9a0e44: ldur            x2, [fp, #-8]
    // 0x9a0e48: StoreField: r2->field_1b = r0
    //     0x9a0e48: stur            w0, [x2, #0x1b]
    //     0x9a0e4c: ldurb           w16, [x2, #-1]
    //     0x9a0e50: ldurb           w17, [x0, #-1]
    //     0x9a0e54: and             x16, x17, x16, lsr #2
    //     0x9a0e58: tst             x16, HEAP, lsr #32
    //     0x9a0e5c: b.eq            #0x9a0e64
    //     0x9a0e60: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a0e64: LoadField: r1 = r2->field_1f
    //     0x9a0e64: ldur            w1, [x2, #0x1f]
    // 0x9a0e68: DecompressPointer r1
    //     0x9a0e68: add             x1, x1, HEAP, lsl #32
    // 0x9a0e6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9a0e6c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9a0e70: r0 = forward()
    //     0x9a0e70: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x9a0e74: ldur            x0, [fp, #-8]
    // 0x9a0e78: r1 = false
    //     0x9a0e78: add             x1, NULL, #0x30  ; false
    // 0x9a0e7c: StoreField: r0->field_23 = r1
    //     0x9a0e7c: stur            w1, [x0, #0x23]
    // 0x9a0e80: LoadField: r1 = r0->field_b
    //     0x9a0e80: ldur            w1, [x0, #0xb]
    // 0x9a0e84: DecompressPointer r1
    //     0x9a0e84: add             x1, x1, HEAP, lsl #32
    // 0x9a0e88: cmp             w1, NULL
    // 0x9a0e8c: b.eq            #0x9a0f58
    // 0x9a0e90: LoadField: r2 = r1->field_13
    //     0x9a0e90: ldur            w2, [x1, #0x13]
    // 0x9a0e94: DecompressPointer r2
    //     0x9a0e94: add             x2, x2, HEAP, lsl #32
    // 0x9a0e98: r16 = Instance_AnimationDirection
    //     0x9a0e98: add             x16, PP, #0x54, lsl #12  ; [pp+0x54740] Obj!AnimationDirection@e30061
    //     0x9a0e9c: ldr             x16, [x16, #0x740]
    // 0x9a0ea0: cmp             w2, w16
    // 0x9a0ea4: b.ne            #0x9a0ee4
    // 0x9a0ea8: LoadField: r2 = r1->field_f
    //     0x9a0ea8: ldur            w2, [x1, #0xf]
    // 0x9a0eac: DecompressPointer r2
    //     0x9a0eac: add             x2, x2, HEAP, lsl #32
    // 0x9a0eb0: r16 = Instance_Duration
    //     0x9a0eb0: ldr             x16, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x9a0eb4: stp             x16, x2, [SP]
    // 0x9a0eb8: r0 = ==()
    //     0x9a0eb8: bl              #0xd2c01c  ; [dart:core] Duration::==
    // 0x9a0ebc: tbnz            w0, #4, #0x9a0ed0
    // 0x9a0ec0: ldur            x0, [fp, #-8]
    // 0x9a0ec4: r1 = true
    //     0x9a0ec4: add             x1, NULL, #0x20  ; true
    // 0x9a0ec8: StoreField: r0->field_23 = r1
    //     0x9a0ec8: stur            w1, [x0, #0x23]
    // 0x9a0ecc: b               #0x9a0ee4
    // 0x9a0ed0: ldur            x0, [fp, #-8]
    // 0x9a0ed4: LoadField: r1 = r0->field_1b
    //     0x9a0ed4: ldur            w1, [x0, #0x1b]
    // 0x9a0ed8: DecompressPointer r1
    //     0x9a0ed8: add             x1, x1, HEAP, lsl #32
    // 0x9a0edc: ldur            x2, [fp, #-0x10]
    // 0x9a0ee0: r0 = addStatusListener()
    //     0x9a0ee0: bl              #0xd25438  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::addStatusListener
    // 0x9a0ee4: r0 = Null
    //     0x9a0ee4: mov             x0, NULL
    // 0x9a0ee8: LeaveFrame
    //     0x9a0ee8: mov             SP, fp
    //     0x9a0eec: ldp             fp, lr, [SP], #0x10
    // 0x9a0ef0: ret
    //     0x9a0ef0: ret             
    // 0x9a0ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a0ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a0ef8: b               #0x9a0bd8
    // 0x9a0efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0efc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a0f00: r9 = opacity
    //     0x9a0f00: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af30] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x9a0f04: ldr             x9, [x9, #0xf30]
    // 0x9a0f08: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a0f08: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a0f0c: r9 = controller
    //     0x9a0f0c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af38] Field <<EMAIL>>: late (offset: 0x20)
    //     0x9a0f10: ldr             x9, [x9, #0xf38]
    // 0x9a0f14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a0f14: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a0f18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0f18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a0f1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0f1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a0f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0f20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a0f24: stp             q0, q1, [SP, #-0x20]!
    // 0x9a0f28: SaveReg r0
    //     0x9a0f28: str             x0, [SP, #-8]!
    // 0x9a0f2c: r0 = AllocateDouble()
    //     0x9a0f2c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9a0f30: mov             x2, x0
    // 0x9a0f34: RestoreReg r0
    //     0x9a0f34: ldr             x0, [SP], #8
    // 0x9a0f38: ldp             q0, q1, [SP], #0x20
    // 0x9a0f3c: b               #0x9a0df0
    // 0x9a0f40: SaveReg d0
    //     0x9a0f40: str             q0, [SP, #-0x10]!
    // 0x9a0f44: SaveReg r1
    //     0x9a0f44: str             x1, [SP, #-8]!
    // 0x9a0f48: r0 = AllocateDouble()
    //     0x9a0f48: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9a0f4c: RestoreReg r1
    //     0x9a0f4c: ldr             x1, [SP], #8
    // 0x9a0f50: RestoreReg d0
    //     0x9a0f50: ldr             q0, [SP], #0x10
    // 0x9a0f54: b               #0x9a0e34
    // 0x9a0f58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0f58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa43724, size: 0xac
    // 0xa43724: EnterFrame
    //     0xa43724: stp             fp, lr, [SP, #-0x10]!
    //     0xa43728: mov             fp, SP
    // 0xa4372c: AllocStack(0x10)
    //     0xa4372c: sub             SP, SP, #0x10
    // 0xa43730: LoadField: r0 = r1->field_23
    //     0xa43730: ldur            w0, [x1, #0x23]
    // 0xa43734: DecompressPointer r0
    //     0xa43734: add             x0, x0, HEAP, lsl #32
    // 0xa43738: r16 = Sentinel
    //     0xa43738: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4373c: cmp             w0, w16
    // 0xa43740: b.eq            #0xa437b4
    // 0xa43744: tbnz            w0, #4, #0xa43758
    // 0xa43748: r0 = Instance_SizedBox
    //     0xa43748: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa4374c: LeaveFrame
    //     0xa4374c: mov             SP, fp
    //     0xa43750: ldp             fp, lr, [SP], #0x10
    // 0xa43754: ret
    //     0xa43754: ret             
    // 0xa43758: LoadField: r0 = r1->field_1b
    //     0xa43758: ldur            w0, [x1, #0x1b]
    // 0xa4375c: DecompressPointer r0
    //     0xa4375c: add             x0, x0, HEAP, lsl #32
    // 0xa43760: r16 = Sentinel
    //     0xa43760: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa43764: cmp             w0, w16
    // 0xa43768: b.eq            #0xa437c0
    // 0xa4376c: stur            x0, [fp, #-0x10]
    // 0xa43770: LoadField: r2 = r1->field_b
    //     0xa43770: ldur            w2, [x1, #0xb]
    // 0xa43774: DecompressPointer r2
    //     0xa43774: add             x2, x2, HEAP, lsl #32
    // 0xa43778: cmp             w2, NULL
    // 0xa4377c: b.eq            #0xa437cc
    // 0xa43780: LoadField: r1 = r2->field_b
    //     0xa43780: ldur            w1, [x2, #0xb]
    // 0xa43784: DecompressPointer r1
    //     0xa43784: add             x1, x1, HEAP, lsl #32
    // 0xa43788: stur            x1, [fp, #-8]
    // 0xa4378c: r0 = FadeTransition()
    //     0xa4378c: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0xa43790: ldur            x1, [fp, #-0x10]
    // 0xa43794: StoreField: r0->field_f = r1
    //     0xa43794: stur            w1, [x0, #0xf]
    // 0xa43798: r1 = false
    //     0xa43798: add             x1, NULL, #0x30  ; false
    // 0xa4379c: StoreField: r0->field_13 = r1
    //     0xa4379c: stur            w1, [x0, #0x13]
    // 0xa437a0: ldur            x1, [fp, #-8]
    // 0xa437a4: StoreField: r0->field_b = r1
    //     0xa437a4: stur            w1, [x0, #0xb]
    // 0xa437a8: LeaveFrame
    //     0xa437a8: mov             SP, fp
    //     0xa437ac: ldp             fp, lr, [SP], #0x10
    // 0xa437b0: ret
    //     0xa437b0: ret             
    // 0xa437b4: r9 = hideWidget
    //     0xa437b4: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af70] Field <<EMAIL>>: late (offset: 0x24)
    //     0xa437b8: ldr             x9, [x9, #0xf70]
    // 0xa437bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa437bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa437c0: r9 = opacity
    //     0xa437c0: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af30] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xa437c4: ldr             x9, [x9, #0xf30]
    // 0xa437c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa437c8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa437cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa437cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83990, size: 0xa8
    // 0xa83990: EnterFrame
    //     0xa83990: stp             fp, lr, [SP, #-0x10]!
    //     0xa83994: mov             fp, SP
    // 0xa83998: AllocStack(0x10)
    //     0xa83998: sub             SP, SP, #0x10
    // 0xa8399c: SetupParameters(_FadeWidgetState this /* r1 => r0, fp-0x10 */)
    //     0xa8399c: mov             x0, x1
    //     0xa839a0: stur            x1, [fp, #-0x10]
    // 0xa839a4: CheckStackOverflow
    //     0xa839a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa839a8: cmp             SP, x16
    //     0xa839ac: b.ls            #0xa83a18
    // 0xa839b0: LoadField: r3 = r0->field_1b
    //     0xa839b0: ldur            w3, [x0, #0x1b]
    // 0xa839b4: DecompressPointer r3
    //     0xa839b4: add             x3, x3, HEAP, lsl #32
    // 0xa839b8: r16 = Sentinel
    //     0xa839b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa839bc: cmp             w3, w16
    // 0xa839c0: b.eq            #0xa83a20
    // 0xa839c4: mov             x2, x0
    // 0xa839c8: stur            x3, [fp, #-8]
    // 0xa839cc: r1 = Function 'animationStatusChange':.
    //     0xa839cc: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5af28] AnonymousClosure: (0x97de3c), in [package:octo_image/src/image/fade_widget.dart] _FadeWidgetState::animationStatusChange (0x97de78)
    //     0xa839d0: ldr             x1, [x1, #0xf28]
    // 0xa839d4: r0 = AllocateClosure()
    //     0xa839d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa839d8: ldur            x1, [fp, #-8]
    // 0xa839dc: mov             x2, x0
    // 0xa839e0: r0 = removeStatusListener()
    //     0xa839e0: bl              #0xd368f8  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::removeStatusListener
    // 0xa839e4: ldur            x0, [fp, #-0x10]
    // 0xa839e8: LoadField: r1 = r0->field_1f
    //     0xa839e8: ldur            w1, [x0, #0x1f]
    // 0xa839ec: DecompressPointer r1
    //     0xa839ec: add             x1, x1, HEAP, lsl #32
    // 0xa839f0: r16 = Sentinel
    //     0xa839f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa839f4: cmp             w1, w16
    // 0xa839f8: b.eq            #0xa83a2c
    // 0xa839fc: r0 = dispose()
    //     0xa839fc: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa83a00: ldur            x1, [fp, #-0x10]
    // 0xa83a04: r0 = dispose()
    //     0xa83a04: bl              #0xa83a38  ; [package:octo_image/src/image/fade_widget.dart] __FadeWidgetState&State&SingleTickerProviderStateMixin::dispose
    // 0xa83a08: r0 = Null
    //     0xa83a08: mov             x0, NULL
    // 0xa83a0c: LeaveFrame
    //     0xa83a0c: mov             SP, fp
    //     0xa83a10: ldp             fp, lr, [SP], #0x10
    // 0xa83a14: ret
    //     0xa83a14: ret             
    // 0xa83a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83a18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83a1c: b               #0xa839b0
    // 0xa83a20: r9 = opacity
    //     0xa83a20: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af30] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xa83a24: ldr             x9, [x9, #0xf30]
    // 0xa83a28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83a28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa83a2c: r9 = controller
    //     0xa83a2c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af38] Field <<EMAIL>>: late (offset: 0x20)
    //     0xa83a30: ldr             x9, [x9, #0xf38]
    // 0xa83a34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83a34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4705, size: 0x1c, field offset: 0xc
//   const constructor, 
class FadeWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94a68, size: 0x34
    // 0xa94a68: EnterFrame
    //     0xa94a68: stp             fp, lr, [SP, #-0x10]!
    //     0xa94a6c: mov             fp, SP
    // 0xa94a70: mov             x0, x1
    // 0xa94a74: r1 = <FadeWidget>
    //     0xa94a74: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a80] TypeArguments: <FadeWidget>
    //     0xa94a78: ldr             x1, [x1, #0xa80]
    // 0xa94a7c: r0 = _FadeWidgetState()
    //     0xa94a7c: bl              #0xa94a9c  ; Allocate_FadeWidgetStateStub -> _FadeWidgetState (size=0x28)
    // 0xa94a80: r1 = Sentinel
    //     0xa94a80: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94a84: StoreField: r0->field_1b = r1
    //     0xa94a84: stur            w1, [x0, #0x1b]
    // 0xa94a88: StoreField: r0->field_1f = r1
    //     0xa94a88: stur            w1, [x0, #0x1f]
    // 0xa94a8c: StoreField: r0->field_23 = r1
    //     0xa94a8c: stur            w1, [x0, #0x23]
    // 0xa94a90: LeaveFrame
    //     0xa94a90: mov             SP, fp
    //     0xa94a94: ldp             fp, lr, [SP], #0x10
    // 0xa94a98: ret
    //     0xa94a98: ret             
  }
}

// class id: 6826, size: 0x14, field offset: 0x14
enum AnimationDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d5c8, size: 0x64
    // 0xc4d5c8: EnterFrame
    //     0xc4d5c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d5cc: mov             fp, SP
    // 0xc4d5d0: AllocStack(0x10)
    //     0xc4d5d0: sub             SP, SP, #0x10
    // 0xc4d5d4: SetupParameters(AnimationDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4d5d4: mov             x0, x1
    //     0xc4d5d8: stur            x1, [fp, #-8]
    // 0xc4d5dc: CheckStackOverflow
    //     0xc4d5dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d5e0: cmp             SP, x16
    //     0xc4d5e4: b.ls            #0xc4d624
    // 0xc4d5e8: r1 = Null
    //     0xc4d5e8: mov             x1, NULL
    // 0xc4d5ec: r2 = 4
    //     0xc4d5ec: movz            x2, #0x4
    // 0xc4d5f0: r0 = AllocateArray()
    //     0xc4d5f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d5f4: r16 = "AnimationDirection."
    //     0xc4d5f4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a78] "AnimationDirection."
    //     0xc4d5f8: ldr             x16, [x16, #0xa78]
    // 0xc4d5fc: StoreField: r0->field_f = r16
    //     0xc4d5fc: stur            w16, [x0, #0xf]
    // 0xc4d600: ldur            x1, [fp, #-8]
    // 0xc4d604: LoadField: r2 = r1->field_f
    //     0xc4d604: ldur            w2, [x1, #0xf]
    // 0xc4d608: DecompressPointer r2
    //     0xc4d608: add             x2, x2, HEAP, lsl #32
    // 0xc4d60c: StoreField: r0->field_13 = r2
    //     0xc4d60c: stur            w2, [x0, #0x13]
    // 0xc4d610: str             x0, [SP]
    // 0xc4d614: r0 = _interpolate()
    //     0xc4d614: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d618: LeaveFrame
    //     0xc4d618: mov             SP, fp
    //     0xc4d61c: ldp             fp, lr, [SP], #0x10
    // 0xc4d620: ret
    //     0xc4d620: ret             
    // 0xc4d624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d628: b               #0xc4d5e8
  }
}
