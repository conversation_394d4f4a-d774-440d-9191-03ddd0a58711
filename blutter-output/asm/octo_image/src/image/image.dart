// lib: , url: package:octo_image/src/image/image.dart

// class id: 1050746, size: 0x8
class :: {
}

// class id: 4105, size: 0x1c, field offset: 0x14
class _OctoImageState extends State<dynamic> {

  late ImageHand<PERSON> _imageHandler; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x97df60, size: 0xe0
    // 0x97df60: EnterFrame
    //     0x97df60: stp             fp, lr, [SP, #-0x10]!
    //     0x97df64: mov             fp, SP
    // 0x97df68: AllocStack(0x48)
    //     0x97df68: sub             SP, SP, #0x48
    // 0x97df6c: SetupParameters(_OctoImageState this /* r1 => r1, fp-0x38 */)
    //     0x97df6c: stur            x1, [fp, #-0x38]
    // 0x97df70: CheckStackOverflow
    //     0x97df70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97df74: cmp             SP, x16
    //     0x97df78: b.ls            #0x97e034
    // 0x97df7c: LoadField: r0 = r1->field_b
    //     0x97df7c: ldur            w0, [x1, #0xb]
    // 0x97df80: DecompressPointer r0
    //     0x97df80: add             x0, x0, HEAP, lsl #32
    // 0x97df84: cmp             w0, NULL
    // 0x97df88: b.eq            #0x97e03c
    // 0x97df8c: LoadField: r6 = r0->field_b
    //     0x97df8c: ldur            w6, [x0, #0xb]
    // 0x97df90: DecompressPointer r6
    //     0x97df90: add             x6, x6, HEAP, lsl #32
    // 0x97df94: stur            x6, [fp, #-0x30]
    // 0x97df98: LoadField: r7 = r0->field_13
    //     0x97df98: ldur            w7, [x0, #0x13]
    // 0x97df9c: DecompressPointer r7
    //     0x97df9c: add             x7, x7, HEAP, lsl #32
    // 0x97dfa0: stur            x7, [fp, #-0x28]
    // 0x97dfa4: LoadField: r2 = r0->field_1b
    //     0x97dfa4: ldur            w2, [x0, #0x1b]
    // 0x97dfa8: DecompressPointer r2
    //     0x97dfa8: add             x2, x2, HEAP, lsl #32
    // 0x97dfac: stur            x2, [fp, #-0x20]
    // 0x97dfb0: LoadField: r3 = r0->field_3b
    //     0x97dfb0: ldur            w3, [x0, #0x3b]
    // 0x97dfb4: DecompressPointer r3
    //     0x97dfb4: add             x3, x3, HEAP, lsl #32
    // 0x97dfb8: stur            x3, [fp, #-0x18]
    // 0x97dfbc: LoadField: r4 = r0->field_33
    //     0x97dfbc: ldur            w4, [x0, #0x33]
    // 0x97dfc0: DecompressPointer r4
    //     0x97dfc0: add             x4, x4, HEAP, lsl #32
    // 0x97dfc4: stur            x4, [fp, #-0x10]
    // 0x97dfc8: LoadField: r5 = r0->field_37
    //     0x97dfc8: ldur            w5, [x0, #0x37]
    // 0x97dfcc: DecompressPointer r5
    //     0x97dfcc: add             x5, x5, HEAP, lsl #32
    // 0x97dfd0: stur            x5, [fp, #-8]
    // 0x97dfd4: r0 = ImageHandler()
    //     0x97dfd4: bl              #0x97e1f8  ; AllocateImageHandlerStub -> ImageHandler (size=0x64)
    // 0x97dfd8: stur            x0, [fp, #-0x40]
    // 0x97dfdc: ldur            x16, [fp, #-0x10]
    // 0x97dfe0: str             x16, [SP]
    // 0x97dfe4: mov             x1, x0
    // 0x97dfe8: ldur            x2, [fp, #-0x20]
    // 0x97dfec: ldur            x3, [fp, #-0x18]
    // 0x97dff0: ldur            x5, [fp, #-8]
    // 0x97dff4: ldur            x6, [fp, #-0x30]
    // 0x97dff8: ldur            x7, [fp, #-0x28]
    // 0x97dffc: r0 = ImageHandler()
    //     0x97dffc: bl              #0x97e064  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::ImageHandler
    // 0x97e000: ldur            x0, [fp, #-0x40]
    // 0x97e004: ldur            x1, [fp, #-0x38]
    // 0x97e008: ArrayStore: r1[0] = r0  ; List_4
    //     0x97e008: stur            w0, [x1, #0x17]
    //     0x97e00c: ldurb           w16, [x1, #-1]
    //     0x97e010: ldurb           w17, [x0, #-1]
    //     0x97e014: and             x16, x17, x16, lsr #2
    //     0x97e018: tst             x16, HEAP, lsr #32
    //     0x97e01c: b.eq            #0x97e024
    //     0x97e020: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97e024: r0 = Null
    //     0x97e024: mov             x0, NULL
    // 0x97e028: LeaveFrame
    //     0x97e028: mov             SP, fp
    //     0x97e02c: ldp             fp, lr, [SP], #0x10
    // 0x97e030: ret
    //     0x97e030: ret             
    // 0x97e034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e038: b               #0x97df7c
    // 0x97e03c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e03c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a0f5c, size: 0x1c0
    // 0x9a0f5c: EnterFrame
    //     0x9a0f5c: stp             fp, lr, [SP, #-0x10]!
    //     0x9a0f60: mov             fp, SP
    // 0x9a0f64: AllocStack(0x50)
    //     0x9a0f64: sub             SP, SP, #0x50
    // 0x9a0f68: SetupParameters(_OctoImageState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a0f68: mov             x4, x1
    //     0x9a0f6c: mov             x3, x2
    //     0x9a0f70: stur            x1, [fp, #-8]
    //     0x9a0f74: stur            x2, [fp, #-0x10]
    // 0x9a0f78: CheckStackOverflow
    //     0x9a0f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a0f7c: cmp             SP, x16
    //     0x9a0f80: b.ls            #0x9a1108
    // 0x9a0f84: mov             x0, x3
    // 0x9a0f88: r2 = Null
    //     0x9a0f88: mov             x2, NULL
    // 0x9a0f8c: r1 = Null
    //     0x9a0f8c: mov             x1, NULL
    // 0x9a0f90: r4 = 60
    //     0x9a0f90: movz            x4, #0x3c
    // 0x9a0f94: branchIfSmi(r0, 0x9a0fa0)
    //     0x9a0f94: tbz             w0, #0, #0x9a0fa0
    // 0x9a0f98: r4 = LoadClassIdInstr(r0)
    //     0x9a0f98: ldur            x4, [x0, #-1]
    //     0x9a0f9c: ubfx            x4, x4, #0xc, #0x14
    // 0x9a0fa0: r17 = 4704
    //     0x9a0fa0: movz            x17, #0x1260
    // 0x9a0fa4: cmp             x4, x17
    // 0x9a0fa8: b.eq            #0x9a0fc0
    // 0x9a0fac: r8 = OctoImage
    //     0x9a0fac: add             x8, PP, #0x54, lsl #12  ; [pp+0x54758] Type: OctoImage
    //     0x9a0fb0: ldr             x8, [x8, #0x758]
    // 0x9a0fb4: r3 = Null
    //     0x9a0fb4: add             x3, PP, #0x54, lsl #12  ; [pp+0x54760] Null
    //     0x9a0fb8: ldr             x3, [x3, #0x760]
    // 0x9a0fbc: r0 = OctoImage()
    //     0x9a0fbc: bl              #0x97e040  ; IsType_OctoImage_Stub
    // 0x9a0fc0: ldur            x3, [fp, #-8]
    // 0x9a0fc4: LoadField: r2 = r3->field_7
    //     0x9a0fc4: ldur            w2, [x3, #7]
    // 0x9a0fc8: DecompressPointer r2
    //     0x9a0fc8: add             x2, x2, HEAP, lsl #32
    // 0x9a0fcc: ldur            x0, [fp, #-0x10]
    // 0x9a0fd0: r1 = Null
    //     0x9a0fd0: mov             x1, NULL
    // 0x9a0fd4: cmp             w2, NULL
    // 0x9a0fd8: b.eq            #0x9a0ffc
    // 0x9a0fdc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a0fdc: ldur            w4, [x2, #0x17]
    // 0x9a0fe0: DecompressPointer r4
    //     0x9a0fe0: add             x4, x4, HEAP, lsl #32
    // 0x9a0fe4: r8 = X0 bound StatefulWidget
    //     0x9a0fe4: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a0fe8: ldr             x8, [x8, #0x7f8]
    // 0x9a0fec: LoadField: r9 = r4->field_7
    //     0x9a0fec: ldur            x9, [x4, #7]
    // 0x9a0ff0: r3 = Null
    //     0x9a0ff0: add             x3, PP, #0x54, lsl #12  ; [pp+0x54770] Null
    //     0x9a0ff4: ldr             x3, [x3, #0x770]
    // 0x9a0ff8: blr             x9
    // 0x9a0ffc: ldur            x0, [fp, #-0x10]
    // 0x9a1000: LoadField: r1 = r0->field_b
    //     0x9a1000: ldur            w1, [x0, #0xb]
    // 0x9a1004: DecompressPointer r1
    //     0x9a1004: add             x1, x1, HEAP, lsl #32
    // 0x9a1008: ldur            x0, [fp, #-8]
    // 0x9a100c: LoadField: r2 = r0->field_b
    //     0x9a100c: ldur            w2, [x0, #0xb]
    // 0x9a1010: DecompressPointer r2
    //     0x9a1010: add             x2, x2, HEAP, lsl #32
    // 0x9a1014: cmp             w2, NULL
    // 0x9a1018: b.eq            #0x9a1110
    // 0x9a101c: LoadField: r3 = r2->field_b
    //     0x9a101c: ldur            w3, [x2, #0xb]
    // 0x9a1020: DecompressPointer r3
    //     0x9a1020: add             x3, x3, HEAP, lsl #32
    // 0x9a1024: stp             x3, x1, [SP]
    // 0x9a1028: r0 = ==()
    //     0x9a1028: bl              #0xd3ce3c  ; [package:cached_network_image/src/image_provider/cached_network_image_provider.dart] CachedNetworkImageProvider::==
    // 0x9a102c: tbz             w0, #4, #0x9a104c
    // 0x9a1030: ldur            x0, [fp, #-8]
    // 0x9a1034: LoadField: r1 = r0->field_b
    //     0x9a1034: ldur            w1, [x0, #0xb]
    // 0x9a1038: DecompressPointer r1
    //     0x9a1038: add             x1, x1, HEAP, lsl #32
    // 0x9a103c: cmp             w1, NULL
    // 0x9a1040: b.eq            #0x9a1114
    // 0x9a1044: StoreField: r0->field_13 = rNULL
    //     0x9a1044: stur            NULL, [x0, #0x13]
    // 0x9a1048: b               #0x9a1050
    // 0x9a104c: ldur            x0, [fp, #-8]
    // 0x9a1050: LoadField: r1 = r0->field_b
    //     0x9a1050: ldur            w1, [x0, #0xb]
    // 0x9a1054: DecompressPointer r1
    //     0x9a1054: add             x1, x1, HEAP, lsl #32
    // 0x9a1058: cmp             w1, NULL
    // 0x9a105c: b.eq            #0x9a1118
    // 0x9a1060: LoadField: r6 = r1->field_b
    //     0x9a1060: ldur            w6, [x1, #0xb]
    // 0x9a1064: DecompressPointer r6
    //     0x9a1064: add             x6, x6, HEAP, lsl #32
    // 0x9a1068: stur            x6, [fp, #-0x38]
    // 0x9a106c: LoadField: r7 = r1->field_13
    //     0x9a106c: ldur            w7, [x1, #0x13]
    // 0x9a1070: DecompressPointer r7
    //     0x9a1070: add             x7, x7, HEAP, lsl #32
    // 0x9a1074: stur            x7, [fp, #-0x30]
    // 0x9a1078: LoadField: r2 = r1->field_1b
    //     0x9a1078: ldur            w2, [x1, #0x1b]
    // 0x9a107c: DecompressPointer r2
    //     0x9a107c: add             x2, x2, HEAP, lsl #32
    // 0x9a1080: stur            x2, [fp, #-0x28]
    // 0x9a1084: LoadField: r3 = r1->field_3b
    //     0x9a1084: ldur            w3, [x1, #0x3b]
    // 0x9a1088: DecompressPointer r3
    //     0x9a1088: add             x3, x3, HEAP, lsl #32
    // 0x9a108c: stur            x3, [fp, #-0x20]
    // 0x9a1090: LoadField: r4 = r1->field_33
    //     0x9a1090: ldur            w4, [x1, #0x33]
    // 0x9a1094: DecompressPointer r4
    //     0x9a1094: add             x4, x4, HEAP, lsl #32
    // 0x9a1098: stur            x4, [fp, #-0x18]
    // 0x9a109c: LoadField: r5 = r1->field_37
    //     0x9a109c: ldur            w5, [x1, #0x37]
    // 0x9a10a0: DecompressPointer r5
    //     0x9a10a0: add             x5, x5, HEAP, lsl #32
    // 0x9a10a4: stur            x5, [fp, #-0x10]
    // 0x9a10a8: r0 = ImageHandler()
    //     0x9a10a8: bl              #0x97e1f8  ; AllocateImageHandlerStub -> ImageHandler (size=0x64)
    // 0x9a10ac: stur            x0, [fp, #-0x40]
    // 0x9a10b0: ldur            x16, [fp, #-0x18]
    // 0x9a10b4: str             x16, [SP]
    // 0x9a10b8: mov             x1, x0
    // 0x9a10bc: ldur            x2, [fp, #-0x28]
    // 0x9a10c0: ldur            x3, [fp, #-0x20]
    // 0x9a10c4: ldur            x5, [fp, #-0x10]
    // 0x9a10c8: ldur            x6, [fp, #-0x38]
    // 0x9a10cc: ldur            x7, [fp, #-0x30]
    // 0x9a10d0: r0 = ImageHandler()
    //     0x9a10d0: bl              #0x97e064  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::ImageHandler
    // 0x9a10d4: ldur            x0, [fp, #-0x40]
    // 0x9a10d8: ldur            x1, [fp, #-8]
    // 0x9a10dc: ArrayStore: r1[0] = r0  ; List_4
    //     0x9a10dc: stur            w0, [x1, #0x17]
    //     0x9a10e0: ldurb           w16, [x1, #-1]
    //     0x9a10e4: ldurb           w17, [x0, #-1]
    //     0x9a10e8: and             x16, x17, x16, lsr #2
    //     0x9a10ec: tst             x16, HEAP, lsr #32
    //     0x9a10f0: b.eq            #0x9a10f8
    //     0x9a10f4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a10f8: r0 = Null
    //     0x9a10f8: mov             x0, NULL
    // 0x9a10fc: LeaveFrame
    //     0x9a10fc: mov             SP, fp
    //     0x9a1100: ldp             fp, lr, [SP], #0x10
    // 0x9a1104: ret
    //     0x9a1104: ret             
    // 0x9a1108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a110c: b               #0x9a0f84
    // 0x9a1110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1114: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1114: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1118: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1118: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa437d0, size: 0xa0
    // 0xa437d0: EnterFrame
    //     0xa437d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa437d4: mov             fp, SP
    // 0xa437d8: AllocStack(0x18)
    //     0xa437d8: sub             SP, SP, #0x18
    // 0xa437dc: CheckStackOverflow
    //     0xa437dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa437e0: cmp             SP, x16
    //     0xa437e4: b.ls            #0xa43858
    // 0xa437e8: LoadField: r0 = r1->field_b
    //     0xa437e8: ldur            w0, [x1, #0xb]
    // 0xa437ec: DecompressPointer r0
    //     0xa437ec: add             x0, x0, HEAP, lsl #32
    // 0xa437f0: cmp             w0, NULL
    // 0xa437f4: b.eq            #0xa43860
    // 0xa437f8: LoadField: r2 = r0->field_33
    //     0xa437f8: ldur            w2, [x0, #0x33]
    // 0xa437fc: DecompressPointer r2
    //     0xa437fc: add             x2, x2, HEAP, lsl #32
    // 0xa43800: stur            x2, [fp, #-0x10]
    // 0xa43804: LoadField: r3 = r0->field_37
    //     0xa43804: ldur            w3, [x0, #0x37]
    // 0xa43808: DecompressPointer r3
    //     0xa43808: add             x3, x3, HEAP, lsl #32
    // 0xa4380c: stur            x3, [fp, #-8]
    // 0xa43810: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa43810: ldur            w0, [x1, #0x17]
    // 0xa43814: DecompressPointer r0
    //     0xa43814: add             x0, x0, HEAP, lsl #32
    // 0xa43818: r16 = Sentinel
    //     0xa43818: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4381c: cmp             w0, w16
    // 0xa43820: b.eq            #0xa43864
    // 0xa43824: mov             x1, x0
    // 0xa43828: r0 = build()
    //     0xa43828: bl              #0xa43870  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::build
    // 0xa4382c: stur            x0, [fp, #-0x18]
    // 0xa43830: r0 = SizedBox()
    //     0xa43830: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa43834: ldur            x1, [fp, #-0x10]
    // 0xa43838: StoreField: r0->field_f = r1
    //     0xa43838: stur            w1, [x0, #0xf]
    // 0xa4383c: ldur            x1, [fp, #-8]
    // 0xa43840: StoreField: r0->field_13 = r1
    //     0xa43840: stur            w1, [x0, #0x13]
    // 0xa43844: ldur            x1, [fp, #-0x18]
    // 0xa43848: StoreField: r0->field_b = r1
    //     0xa43848: stur            w1, [x0, #0xb]
    // 0xa4384c: LeaveFrame
    //     0xa4384c: mov             SP, fp
    //     0xa43850: ldp             fp, lr, [SP], #0x10
    // 0xa43854: ret
    //     0xa43854: ret             
    // 0xa43858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4385c: b               #0xa437e8
    // 0xa43860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa43860: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa43864: r9 = _imageHandler
    //     0xa43864: add             x9, PP, #0x54, lsl #12  ; [pp+0x54700] Field <_OctoImageState@828416564._imageHandler@828416564>: late (offset: 0x18)
    //     0xa43868: ldr             x9, [x9, #0x700]
    // 0xa4386c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4386c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4704, size: 0x5c, field offset: 0xc
class OctoImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94aa8, size: 0x2c
    // 0xa94aa8: EnterFrame
    //     0xa94aa8: stp             fp, lr, [SP, #-0x10]!
    //     0xa94aac: mov             fp, SP
    // 0xa94ab0: mov             x0, x1
    // 0xa94ab4: r1 = <OctoImage>
    //     0xa94ab4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d098] TypeArguments: <OctoImage>
    //     0xa94ab8: ldr             x1, [x1, #0x98]
    // 0xa94abc: r0 = _OctoImageState()
    //     0xa94abc: bl              #0xa94ad4  ; Allocate_OctoImageStateStub -> _OctoImageState (size=0x1c)
    // 0xa94ac0: r1 = Sentinel
    //     0xa94ac0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94ac4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa94ac4: stur            w1, [x0, #0x17]
    // 0xa94ac8: LeaveFrame
    //     0xa94ac8: mov             SP, fp
    //     0xa94acc: ldp             fp, lr, [SP], #0x10
    // 0xa94ad0: ret
    //     0xa94ad0: ret             
  }
  _ OctoImage(/* No info */) {
    // ** addr: 0xa965cc, size: 0x150
    // 0xa965cc: EnterFrame
    //     0xa965cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa965d0: mov             fp, SP
    // 0xa965d4: r19 = Instance_Duration
    //     0xa965d4: ldr             x19, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0xa965d8: r14 = Instance_Cubic
    //     0xa965d8: add             x14, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xa965dc: ldr             x14, [x14, #0xb28]
    // 0xa965e0: r13 = Instance_Duration
    //     0xa965e0: ldr             x13, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xa965e4: r12 = Instance_Cubic
    //     0xa965e4: add             x12, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0xa965e8: ldr             x12, [x12, #0x408]
    // 0xa965ec: r11 = Instance_Alignment
    //     0xa965ec: add             x11, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa965f0: ldr             x11, [x11, #0x898]
    // 0xa965f4: r10 = Instance_ImageRepeat
    //     0xa965f4: add             x10, PP, #0x2b, lsl #12  ; [pp+0x2bc08] Obj!ImageRepeat@e35d21
    //     0xa965f8: ldr             x10, [x10, #0xc08]
    // 0xa965fc: r9 = false
    //     0xa965fc: add             x9, NULL, #0x30  ; false
    // 0xa96600: r8 = Instance_FilterQuality
    //     0xa96600: add             x8, PP, #0x38, lsl #12  ; [pp+0x38410] Obj!FilterQuality@e39c81
    //     0xa96604: ldr             x8, [x8, #0x410]
    // 0xa96608: r4 = Instance_Duration
    //     0xa96608: ldr             x4, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0xa9660c: mov             x0, x7
    // 0xa96610: mov             x16, x6
    // 0xa96614: mov             x6, x1
    // 0xa96618: mov             x1, x16
    // 0xa9661c: mov             x16, x5
    // 0xa96620: mov             x5, x2
    // 0xa96624: mov             x2, x16
    // 0xa96628: StoreField: r6->field_13 = r0
    //     0xa96628: stur            w0, [x6, #0x13]
    //     0xa9662c: ldurb           w16, [x6, #-1]
    //     0xa96630: ldurb           w17, [x0, #-1]
    //     0xa96634: and             x16, x17, x16, lsr #2
    //     0xa96638: tst             x16, HEAP, lsr #32
    //     0xa9663c: b.eq            #0xa96644
    //     0xa96640: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa96644: mov             x0, x5
    // 0xa96648: StoreField: r6->field_1b = r0
    //     0xa96648: stur            w0, [x6, #0x1b]
    //     0xa9664c: ldurb           w16, [x6, #-1]
    //     0xa96650: ldurb           w17, [x0, #-1]
    //     0xa96654: and             x16, x17, x16, lsr #2
    //     0xa96658: tst             x16, HEAP, lsr #32
    //     0xa9665c: b.eq            #0xa96664
    //     0xa96660: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa96664: ldr             x0, [fp, #0x10]
    // 0xa96668: StoreField: r6->field_33 = r0
    //     0xa96668: stur            w0, [x6, #0x33]
    //     0xa9666c: ldurb           w16, [x6, #-1]
    //     0xa96670: ldurb           w17, [x0, #-1]
    //     0xa96674: and             x16, x17, x16, lsr #2
    //     0xa96678: tst             x16, HEAP, lsr #32
    //     0xa9667c: b.eq            #0xa96684
    //     0xa96680: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa96684: mov             x0, x2
    // 0xa96688: StoreField: r6->field_37 = r0
    //     0xa96688: stur            w0, [x6, #0x37]
    //     0xa9668c: ldurb           w16, [x6, #-1]
    //     0xa96690: ldurb           w17, [x0, #-1]
    //     0xa96694: and             x16, x17, x16, lsr #2
    //     0xa96698: tst             x16, HEAP, lsr #32
    //     0xa9669c: b.eq            #0xa966a4
    //     0xa966a0: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa966a4: mov             x0, x3
    // 0xa966a8: StoreField: r6->field_3b = r0
    //     0xa966a8: stur            w0, [x6, #0x3b]
    //     0xa966ac: ldurb           w16, [x6, #-1]
    //     0xa966b0: ldurb           w17, [x0, #-1]
    //     0xa966b4: and             x16, x17, x16, lsr #2
    //     0xa966b8: tst             x16, HEAP, lsr #32
    //     0xa966bc: b.eq            #0xa966c4
    //     0xa966c0: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa966c4: mov             x0, x1
    // 0xa966c8: StoreField: r6->field_b = r0
    //     0xa966c8: stur            w0, [x6, #0xb]
    //     0xa966cc: ldurb           w16, [x6, #-1]
    //     0xa966d0: ldurb           w17, [x0, #-1]
    //     0xa966d4: and             x16, x17, x16, lsr #2
    //     0xa966d8: tst             x16, HEAP, lsr #32
    //     0xa966dc: b.eq            #0xa966e4
    //     0xa966e0: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xa966e4: StoreField: r6->field_23 = r19
    //     0xa966e4: stur            w19, [x6, #0x23]
    // 0xa966e8: StoreField: r6->field_27 = r14
    //     0xa966e8: stur            w14, [x6, #0x27]
    // 0xa966ec: StoreField: r6->field_2b = r13
    //     0xa966ec: stur            w13, [x6, #0x2b]
    // 0xa966f0: StoreField: r6->field_2f = r12
    //     0xa966f0: stur            w12, [x6, #0x2f]
    // 0xa966f4: StoreField: r6->field_3f = r11
    //     0xa966f4: stur            w11, [x6, #0x3f]
    // 0xa966f8: StoreField: r6->field_43 = r10
    //     0xa966f8: stur            w10, [x6, #0x43]
    // 0xa966fc: StoreField: r6->field_47 = r9
    //     0xa966fc: stur            w9, [x6, #0x47]
    // 0xa96700: StoreField: r6->field_53 = r8
    //     0xa96700: stur            w8, [x6, #0x53]
    // 0xa96704: StoreField: r6->field_1f = r4
    //     0xa96704: stur            w4, [x6, #0x1f]
    // 0xa96708: StoreField: r6->field_57 = r9
    //     0xa96708: stur            w9, [x6, #0x57]
    // 0xa9670c: r0 = Null
    //     0xa9670c: mov             x0, NULL
    // 0xa96710: LeaveFrame
    //     0xa96710: mov             SP, fp
    //     0xa96714: ldp             fp, lr, [SP], #0x10
    // 0xa96718: ret
    //     0xa96718: ret             
  }
}
