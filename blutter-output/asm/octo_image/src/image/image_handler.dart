// lib: , url: package:octo_image/src/image/image_handler.dart

// class id: 1050747, size: 0x8
class :: {
}

// class id: 949, size: 0x64, field offset: 0x8
class Image<PERSON><PERSON><PERSON> extends Object {

  late _PlaceholderType _placeholderType; // offset: 0x30

  _ ImageHandler(/* No info */) {
    // ** addr: 0x97e064, size: 0x194
    // 0x97e064: EnterFrame
    //     0x97e064: stp             fp, lr, [SP, #-0x10]!
    //     0x97e068: mov             fp, SP
    // 0x97e06c: r20 = Sentinel
    //     0x97e06c: ldr             x20, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97e070: r19 = false
    //     0x97e070: add             x19, NULL, #0x30  ; false
    // 0x97e074: r14 = Instance_Alignment
    //     0x97e074: add             x14, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x97e078: ldr             x14, [x14, #0x898]
    // 0x97e07c: r13 = Instance_ImageRepeat
    //     0x97e07c: add             x13, PP, #0x2b, lsl #12  ; [pp+0x2bc08] Obj!ImageRepeat@e35d21
    //     0x97e080: ldr             x13, [x13, #0xc08]
    // 0x97e084: r12 = Instance_FilterQuality
    //     0x97e084: add             x12, PP, #0x38, lsl #12  ; [pp+0x38410] Obj!FilterQuality@e39c81
    //     0x97e088: ldr             x12, [x12, #0x410]
    // 0x97e08c: r11 = Instance_Duration
    //     0x97e08c: ldr             x11, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x97e090: r10 = Instance_Duration
    //     0x97e090: ldr             x10, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0x97e094: r9 = Instance_Cubic
    //     0x97e094: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0x97e098: ldr             x9, [x9, #0xb28]
    // 0x97e09c: r8 = Instance_Duration
    //     0x97e09c: ldr             x8, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0x97e0a0: r4 = Instance_Cubic
    //     0x97e0a0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0x97e0a4: ldr             x4, [x4, #0x408]
    // 0x97e0a8: mov             x0, x6
    // 0x97e0ac: mov             x6, x1
    // 0x97e0b0: mov             x16, x5
    // 0x97e0b4: mov             x5, x2
    // 0x97e0b8: mov             x2, x16
    // 0x97e0bc: mov             x1, x7
    // 0x97e0c0: StoreField: r6->field_2f = r20
    //     0x97e0c0: stur            w20, [x6, #0x2f]
    // 0x97e0c4: StoreField: r6->field_5b = r19
    //     0x97e0c4: stur            w19, [x6, #0x5b]
    // 0x97e0c8: StoreField: r6->field_5f = r19
    //     0x97e0c8: stur            w19, [x6, #0x5f]
    // 0x97e0cc: StoreField: r6->field_7 = r0
    //     0x97e0cc: stur            w0, [x6, #7]
    //     0x97e0d0: ldurb           w16, [x6, #-1]
    //     0x97e0d4: ldurb           w17, [x0, #-1]
    //     0x97e0d8: and             x16, x17, x16, lsr #2
    //     0x97e0dc: tst             x16, HEAP, lsr #32
    //     0x97e0e0: b.eq            #0x97e0e8
    //     0x97e0e4: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e0e8: ldr             x0, [fp, #0x10]
    // 0x97e0ec: StoreField: r6->field_b = r0
    //     0x97e0ec: stur            w0, [x6, #0xb]
    //     0x97e0f0: ldurb           w16, [x6, #-1]
    //     0x97e0f4: ldurb           w17, [x0, #-1]
    //     0x97e0f8: and             x16, x17, x16, lsr #2
    //     0x97e0fc: tst             x16, HEAP, lsr #32
    //     0x97e100: b.eq            #0x97e108
    //     0x97e104: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e108: mov             x0, x2
    // 0x97e10c: StoreField: r6->field_f = r0
    //     0x97e10c: stur            w0, [x6, #0xf]
    //     0x97e110: ldurb           w16, [x6, #-1]
    //     0x97e114: ldurb           w17, [x0, #-1]
    //     0x97e118: and             x16, x17, x16, lsr #2
    //     0x97e11c: tst             x16, HEAP, lsr #32
    //     0x97e120: b.eq            #0x97e128
    //     0x97e124: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e128: mov             x0, x3
    // 0x97e12c: StoreField: r6->field_13 = r0
    //     0x97e12c: stur            w0, [x6, #0x13]
    //     0x97e130: ldurb           w16, [x6, #-1]
    //     0x97e134: ldurb           w17, [x0, #-1]
    //     0x97e138: and             x16, x17, x16, lsr #2
    //     0x97e13c: tst             x16, HEAP, lsr #32
    //     0x97e140: b.eq            #0x97e148
    //     0x97e144: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e148: ArrayStore: r6[0] = r14  ; List_4
    //     0x97e148: stur            w14, [x6, #0x17]
    // 0x97e14c: StoreField: r6->field_1b = r13
    //     0x97e14c: stur            w13, [x6, #0x1b]
    // 0x97e150: StoreField: r6->field_1f = r19
    //     0x97e150: stur            w19, [x6, #0x1f]
    // 0x97e154: StoreField: r6->field_2b = r12
    //     0x97e154: stur            w12, [x6, #0x2b]
    // 0x97e158: mov             x0, x1
    // 0x97e15c: StoreField: r6->field_37 = r0
    //     0x97e15c: stur            w0, [x6, #0x37]
    //     0x97e160: ldurb           w16, [x6, #-1]
    //     0x97e164: ldurb           w17, [x0, #-1]
    //     0x97e168: and             x16, x17, x16, lsr #2
    //     0x97e16c: tst             x16, HEAP, lsr #32
    //     0x97e170: b.eq            #0x97e178
    //     0x97e174: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e178: mov             x0, x5
    // 0x97e17c: StoreField: r6->field_3f = r0
    //     0x97e17c: stur            w0, [x6, #0x3f]
    //     0x97e180: ldurb           w16, [x6, #-1]
    //     0x97e184: ldurb           w17, [x0, #-1]
    //     0x97e188: and             x16, x17, x16, lsr #2
    //     0x97e18c: tst             x16, HEAP, lsr #32
    //     0x97e190: b.eq            #0x97e198
    //     0x97e194: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e198: StoreField: r6->field_43 = r11
    //     0x97e198: stur            w11, [x6, #0x43]
    // 0x97e19c: StoreField: r6->field_47 = r10
    //     0x97e19c: stur            w10, [x6, #0x47]
    // 0x97e1a0: StoreField: r6->field_4b = r9
    //     0x97e1a0: stur            w9, [x6, #0x4b]
    // 0x97e1a4: StoreField: r6->field_4f = r8
    //     0x97e1a4: stur            w8, [x6, #0x4f]
    // 0x97e1a8: StoreField: r6->field_53 = r4
    //     0x97e1a8: stur            w4, [x6, #0x53]
    // 0x97e1ac: StoreField: r6->field_57 = r19
    //     0x97e1ac: stur            w19, [x6, #0x57]
    // 0x97e1b0: cmp             w1, NULL
    // 0x97e1b4: b.eq            #0x97e1c4
    // 0x97e1b8: r0 = Instance__PlaceholderType
    //     0x97e1b8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54780] Obj!_PlaceholderType@e30021
    //     0x97e1bc: ldr             x0, [x0, #0x780]
    // 0x97e1c0: b               #0x97e1cc
    // 0x97e1c4: r0 = Instance__PlaceholderType
    //     0x97e1c4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54788] Obj!_PlaceholderType@e30001
    //     0x97e1c8: ldr             x0, [x0, #0x788]
    // 0x97e1cc: StoreField: r6->field_2f = r0
    //     0x97e1cc: stur            w0, [x6, #0x2f]
    //     0x97e1d0: ldurb           w16, [x6, #-1]
    //     0x97e1d4: ldurb           w17, [x0, #-1]
    //     0x97e1d8: and             x16, x17, x16, lsr #2
    //     0x97e1dc: tst             x16, HEAP, lsr #32
    //     0x97e1e0: b.eq            #0x97e1e8
    //     0x97e1e4: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x97e1e8: r0 = Null
    //     0x97e1e8: mov             x0, NULL
    // 0x97e1ec: LeaveFrame
    //     0x97e1ec: mov             SP, fp
    //     0x97e1f0: ldp             fp, lr, [SP], #0x10
    // 0x97e1f4: ret
    //     0x97e1f4: ret             
  }
  [closure] Widget _loadingBuilder(dynamic, BuildContext, Widget, ImageChunkEvent?) {
    // ** addr: 0xa17124, size: 0x44
    // 0xa17124: EnterFrame
    //     0xa17124: stp             fp, lr, [SP, #-0x10]!
    //     0xa17128: mov             fp, SP
    // 0xa1712c: ldr             x0, [fp, #0x28]
    // 0xa17130: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa17130: ldur            w1, [x0, #0x17]
    // 0xa17134: DecompressPointer r1
    //     0xa17134: add             x1, x1, HEAP, lsl #32
    // 0xa17138: CheckStackOverflow
    //     0xa17138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1713c: cmp             SP, x16
    //     0xa17140: b.ls            #0xa17160
    // 0xa17144: ldr             x2, [fp, #0x20]
    // 0xa17148: ldr             x3, [fp, #0x18]
    // 0xa1714c: ldr             x5, [fp, #0x10]
    // 0xa17150: r0 = _loadingBuilder()
    //     0xa17150: bl              #0xa17168  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_loadingBuilder
    // 0xa17154: LeaveFrame
    //     0xa17154: mov             SP, fp
    //     0xa17158: ldp             fp, lr, [SP], #0x10
    // 0xa1715c: ret
    //     0xa1715c: ret             
    // 0xa17160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa17160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa17164: b               #0xa17144
  }
  _ _loadingBuilder(/* No info */) {
    // ** addr: 0xa17168, size: 0x64
    // 0xa17168: EnterFrame
    //     0xa17168: stp             fp, lr, [SP, #-0x10]!
    //     0xa1716c: mov             fp, SP
    // 0xa17170: mov             x0, x3
    // 0xa17174: CheckStackOverflow
    //     0xa17174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa17178: cmp             SP, x16
    //     0xa1717c: b.ls            #0xa171c4
    // 0xa17180: LoadField: r2 = r1->field_5f
    //     0xa17180: ldur            w2, [x1, #0x5f]
    // 0xa17184: DecompressPointer r2
    //     0xa17184: add             x2, x2, HEAP, lsl #32
    // 0xa17188: tbnz            w2, #4, #0xa171a4
    // 0xa1718c: LoadField: r2 = r1->field_5b
    //     0xa1718c: ldur            w2, [x1, #0x5b]
    // 0xa17190: DecompressPointer r2
    //     0xa17190: add             x2, x2, HEAP, lsl #32
    // 0xa17194: tbnz            w2, #4, #0xa171b4
    // 0xa17198: LeaveFrame
    //     0xa17198: mov             SP, fp
    //     0xa1719c: ldp             fp, lr, [SP], #0x10
    // 0xa171a0: ret
    //     0xa171a0: ret             
    // 0xa171a4: r0 = _progressIndicator()
    //     0xa171a4: bl              #0xa171cc  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_progressIndicator
    // 0xa171a8: LeaveFrame
    //     0xa171a8: mov             SP, fp
    //     0xa171ac: ldp             fp, lr, [SP], #0x10
    // 0xa171b0: ret
    //     0xa171b0: ret             
    // 0xa171b4: r0 = _progressIndicator()
    //     0xa171b4: bl              #0xa171cc  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_progressIndicator
    // 0xa171b8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xa171b8: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xa171bc: r0 = Throw()
    //     0xa171bc: bl              #0xec04b8  ; ThrowStub
    // 0xa171c0: brk             #0
    // 0xa171c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa171c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa171c8: b               #0xa17180
  }
  _ _progressIndicator(/* No info */) {
    // ** addr: 0xa171cc, size: 0x28
    // 0xa171cc: EnterFrame
    //     0xa171cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa171d0: mov             fp, SP
    // 0xa171d4: r0 = StateError()
    //     0xa171d4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xa171d8: mov             x1, x0
    // 0xa171dc: r0 = "Try to build progressIndicatorBuilder with progressIndicatorBuilder null"
    //     0xa171dc: add             x0, PP, #0x46, lsl #12  ; [pp+0x46148] "Try to build progressIndicatorBuilder with progressIndicatorBuilder null"
    //     0xa171e0: ldr             x0, [x0, #0x148]
    // 0xa171e4: StoreField: r1->field_b = r0
    //     0xa171e4: stur            w0, [x1, #0xb]
    // 0xa171e8: mov             x0, x1
    // 0xa171ec: r0 = Throw()
    //     0xa171ec: bl              #0xec04b8  ; ThrowStub
    // 0xa171f0: brk             #0
  }
  _ build(/* No info */) {
    // ** addr: 0xa43870, size: 0x128
    // 0xa43870: EnterFrame
    //     0xa43870: stp             fp, lr, [SP, #-0x10]!
    //     0xa43874: mov             fp, SP
    // 0xa43878: AllocStack(0x48)
    //     0xa43878: sub             SP, SP, #0x48
    // 0xa4387c: SetupParameters(ImageHandler this /* r1 => r0, fp-0x10 */)
    //     0xa4387c: mov             x0, x1
    //     0xa43880: stur            x1, [fp, #-0x10]
    // 0xa43884: CheckStackOverflow
    //     0xa43884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43888: cmp             SP, x16
    //     0xa4388c: b.ls            #0xa43990
    // 0xa43890: LoadField: r2 = r0->field_7
    //     0xa43890: ldur            w2, [x0, #7]
    // 0xa43894: DecompressPointer r2
    //     0xa43894: add             x2, x2, HEAP, lsl #32
    // 0xa43898: stur            x2, [fp, #-8]
    // 0xa4389c: r1 = <ImageProvider<Object>>
    //     0xa4389c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43488] TypeArguments: <ImageProvider<Object>>
    //     0xa438a0: ldr             x1, [x1, #0x488]
    // 0xa438a4: r0 = ValueKey()
    //     0xa438a4: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa438a8: mov             x2, x0
    // 0xa438ac: ldur            x0, [fp, #-8]
    // 0xa438b0: stur            x2, [fp, #-0x18]
    // 0xa438b4: StoreField: r2->field_b = r0
    //     0xa438b4: stur            w0, [x2, #0xb]
    // 0xa438b8: ldur            x1, [fp, #-0x10]
    // 0xa438bc: r0 = imageLoadingBuilder()
    //     0xa438bc: bl              #0xa43e54  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::imageLoadingBuilder
    // 0xa438c0: ldur            x1, [fp, #-0x10]
    // 0xa438c4: stur            x0, [fp, #-0x20]
    // 0xa438c8: r0 = imageFrameBuilder()
    //     0xa438c8: bl              #0xa43b1c  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::imageFrameBuilder
    // 0xa438cc: ldur            x1, [fp, #-0x10]
    // 0xa438d0: stur            x0, [fp, #-0x28]
    // 0xa438d4: r0 = errorWidgetBuilder()
    //     0xa438d4: bl              #0xa43998  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::errorWidgetBuilder
    // 0xa438d8: mov             x1, x0
    // 0xa438dc: ldur            x0, [fp, #-0x10]
    // 0xa438e0: stur            x1, [fp, #-0x48]
    // 0xa438e4: LoadField: r2 = r0->field_13
    //     0xa438e4: ldur            w2, [x0, #0x13]
    // 0xa438e8: DecompressPointer r2
    //     0xa438e8: add             x2, x2, HEAP, lsl #32
    // 0xa438ec: stur            x2, [fp, #-0x40]
    // 0xa438f0: LoadField: r3 = r0->field_b
    //     0xa438f0: ldur            w3, [x0, #0xb]
    // 0xa438f4: DecompressPointer r3
    //     0xa438f4: add             x3, x3, HEAP, lsl #32
    // 0xa438f8: stur            x3, [fp, #-0x38]
    // 0xa438fc: LoadField: r4 = r0->field_f
    //     0xa438fc: ldur            w4, [x0, #0xf]
    // 0xa43900: DecompressPointer r4
    //     0xa43900: add             x4, x4, HEAP, lsl #32
    // 0xa43904: stur            x4, [fp, #-0x30]
    // 0xa43908: r0 = Image()
    //     0xa43908: bl              #0x92219c  ; AllocateImageStub -> Image (size=0x58)
    // 0xa4390c: ldur            x1, [fp, #-8]
    // 0xa43910: StoreField: r0->field_b = r1
    //     0xa43910: stur            w1, [x0, #0xb]
    // 0xa43914: ldur            x1, [fp, #-0x28]
    // 0xa43918: StoreField: r0->field_f = r1
    //     0xa43918: stur            w1, [x0, #0xf]
    // 0xa4391c: ldur            x1, [fp, #-0x20]
    // 0xa43920: StoreField: r0->field_13 = r1
    //     0xa43920: stur            w1, [x0, #0x13]
    // 0xa43924: ldur            x1, [fp, #-0x48]
    // 0xa43928: ArrayStore: r0[0] = r1  ; List_4
    //     0xa43928: stur            w1, [x0, #0x17]
    // 0xa4392c: r1 = false
    //     0xa4392c: add             x1, NULL, #0x30  ; false
    // 0xa43930: StoreField: r0->field_4f = r1
    //     0xa43930: stur            w1, [x0, #0x4f]
    // 0xa43934: ldur            x2, [fp, #-0x38]
    // 0xa43938: StoreField: r0->field_1b = r2
    //     0xa43938: stur            w2, [x0, #0x1b]
    // 0xa4393c: ldur            x2, [fp, #-0x30]
    // 0xa43940: StoreField: r0->field_1f = r2
    //     0xa43940: stur            w2, [x0, #0x1f]
    // 0xa43944: ldur            x2, [fp, #-0x40]
    // 0xa43948: StoreField: r0->field_33 = r2
    //     0xa43948: stur            w2, [x0, #0x33]
    // 0xa4394c: r2 = Instance_Alignment
    //     0xa4394c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa43950: ldr             x2, [x2, #0x898]
    // 0xa43954: StoreField: r0->field_37 = r2
    //     0xa43954: stur            w2, [x0, #0x37]
    // 0xa43958: r2 = Instance_ImageRepeat
    //     0xa43958: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bc08] Obj!ImageRepeat@e35d21
    //     0xa4395c: ldr             x2, [x2, #0xc08]
    // 0xa43960: StoreField: r0->field_3b = r2
    //     0xa43960: stur            w2, [x0, #0x3b]
    // 0xa43964: StoreField: r0->field_43 = r1
    //     0xa43964: stur            w1, [x0, #0x43]
    // 0xa43968: StoreField: r0->field_47 = r1
    //     0xa43968: stur            w1, [x0, #0x47]
    // 0xa4396c: StoreField: r0->field_53 = r1
    //     0xa4396c: stur            w1, [x0, #0x53]
    // 0xa43970: r1 = Instance_FilterQuality
    //     0xa43970: add             x1, PP, #0x38, lsl #12  ; [pp+0x38410] Obj!FilterQuality@e39c81
    //     0xa43974: ldr             x1, [x1, #0x410]
    // 0xa43978: StoreField: r0->field_2b = r1
    //     0xa43978: stur            w1, [x0, #0x2b]
    // 0xa4397c: ldur            x1, [fp, #-0x18]
    // 0xa43980: StoreField: r0->field_7 = r1
    //     0xa43980: stur            w1, [x0, #7]
    // 0xa43984: LeaveFrame
    //     0xa43984: mov             SP, fp
    //     0xa43988: ldp             fp, lr, [SP], #0x10
    // 0xa4398c: ret
    //     0xa4398c: ret             
    // 0xa43990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43994: b               #0xa43890
  }
  _ errorWidgetBuilder(/* No info */) {
    // ** addr: 0xa43998, size: 0x3c
    // 0xa43998: EnterFrame
    //     0xa43998: stp             fp, lr, [SP, #-0x10]!
    //     0xa4399c: mov             fp, SP
    // 0xa439a0: mov             x2, x1
    // 0xa439a4: LoadField: r0 = r2->field_3f
    //     0xa439a4: ldur            w0, [x2, #0x3f]
    // 0xa439a8: DecompressPointer r0
    //     0xa439a8: add             x0, x0, HEAP, lsl #32
    // 0xa439ac: cmp             w0, NULL
    // 0xa439b0: b.eq            #0xa439c4
    // 0xa439b4: r1 = Function '_errorBuilder@2550215462':.
    //     0xa439b4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54708] AnonymousClosure: (0xa439d4), in [package:octo_image/src/image/image_handler.dart] ImageHandler::_errorBuilder (0xa43a18)
    //     0xa439b8: ldr             x1, [x1, #0x708]
    // 0xa439bc: r0 = AllocateClosure()
    //     0xa439bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa439c0: b               #0xa439c8
    // 0xa439c4: r0 = Null
    //     0xa439c4: mov             x0, NULL
    // 0xa439c8: LeaveFrame
    //     0xa439c8: mov             SP, fp
    //     0xa439cc: ldp             fp, lr, [SP], #0x10
    // 0xa439d0: ret
    //     0xa439d0: ret             
  }
  [closure] Widget _errorBuilder(dynamic, BuildContext, Object, StackTrace?) {
    // ** addr: 0xa439d4, size: 0x44
    // 0xa439d4: EnterFrame
    //     0xa439d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa439d8: mov             fp, SP
    // 0xa439dc: ldr             x0, [fp, #0x28]
    // 0xa439e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa439e0: ldur            w1, [x0, #0x17]
    // 0xa439e4: DecompressPointer r1
    //     0xa439e4: add             x1, x1, HEAP, lsl #32
    // 0xa439e8: CheckStackOverflow
    //     0xa439e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa439ec: cmp             SP, x16
    //     0xa439f0: b.ls            #0xa43a10
    // 0xa439f4: ldr             x2, [fp, #0x20]
    // 0xa439f8: ldr             x3, [fp, #0x18]
    // 0xa439fc: ldr             x5, [fp, #0x10]
    // 0xa43a00: r0 = _errorBuilder()
    //     0xa43a00: bl              #0xa43a18  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_errorBuilder
    // 0xa43a04: LeaveFrame
    //     0xa43a04: mov             SP, fp
    //     0xa43a08: ldp             fp, lr, [SP], #0x10
    // 0xa43a0c: ret
    //     0xa43a0c: ret             
    // 0xa43a10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43a10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43a14: b               #0xa439f4
  }
  _ _errorBuilder(/* No info */) {
    // ** addr: 0xa43a18, size: 0x64
    // 0xa43a18: EnterFrame
    //     0xa43a18: stp             fp, lr, [SP, #-0x10]!
    //     0xa43a1c: mov             fp, SP
    // 0xa43a20: CheckStackOverflow
    //     0xa43a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43a24: cmp             SP, x16
    //     0xa43a28: b.ls            #0xa43a74
    // 0xa43a2c: LoadField: r0 = r1->field_3f
    //     0xa43a2c: ldur            w0, [x1, #0x3f]
    // 0xa43a30: DecompressPointer r0
    //     0xa43a30: add             x0, x0, HEAP, lsl #32
    // 0xa43a34: cmp             w0, NULL
    // 0xa43a38: b.eq            #0xa43a54
    // 0xa43a3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa43a3c: ldur            w1, [x0, #0x17]
    // 0xa43a40: DecompressPointer r1
    //     0xa43a40: add             x1, x1, HEAP, lsl #32
    // 0xa43a44: r0 = _octoErrorBuilder()
    //     0xa43a44: bl              #0xa43ac0  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoErrorBuilder
    // 0xa43a48: LeaveFrame
    //     0xa43a48: mov             SP, fp
    //     0xa43a4c: ldp             fp, lr, [SP], #0x10
    // 0xa43a50: ret
    //     0xa43a50: ret             
    // 0xa43a54: r0 = StateError()
    //     0xa43a54: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xa43a58: mov             x1, x0
    // 0xa43a5c: r0 = "Try to build errorBuilder with errorBuilder null"
    //     0xa43a5c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54710] "Try to build errorBuilder with errorBuilder null"
    //     0xa43a60: ldr             x0, [x0, #0x710]
    // 0xa43a64: StoreField: r1->field_b = r0
    //     0xa43a64: stur            w0, [x1, #0xb]
    // 0xa43a68: mov             x0, x1
    // 0xa43a6c: r0 = Throw()
    //     0xa43a6c: bl              #0xec04b8  ; ThrowStub
    // 0xa43a70: brk             #0
    // 0xa43a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43a74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43a78: b               #0xa43a2c
  }
  _ imageFrameBuilder(/* No info */) {
    // ** addr: 0xa43b1c, size: 0x88
    // 0xa43b1c: EnterFrame
    //     0xa43b1c: stp             fp, lr, [SP, #-0x10]!
    //     0xa43b20: mov             fp, SP
    // 0xa43b24: mov             x2, x1
    // 0xa43b28: LoadField: r0 = r2->field_2f
    //     0xa43b28: ldur            w0, [x2, #0x2f]
    // 0xa43b2c: DecompressPointer r0
    //     0xa43b2c: add             x0, x0, HEAP, lsl #32
    // 0xa43b30: r16 = Sentinel
    //     0xa43b30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa43b34: cmp             w0, w16
    // 0xa43b38: b.eq            #0xa43b98
    // 0xa43b3c: LoadField: r1 = r0->field_7
    //     0xa43b3c: ldur            x1, [x0, #7]
    // 0xa43b40: cmp             x1, #1
    // 0xa43b44: b.gt            #0xa43b80
    // 0xa43b48: cmp             x1, #0
    // 0xa43b4c: b.gt            #0xa43b68
    // 0xa43b50: r1 = Function '_imageBuilder@2550215462':.
    //     0xa43b50: add             x1, PP, #0x54, lsl #12  ; [pp+0x54718] AnonymousClosure: (0xa43e4c), of [package:octo_image/src/image/image_handler.dart] ImageHandler
    //     0xa43b54: ldr             x1, [x1, #0x718]
    // 0xa43b58: r0 = AllocateClosure()
    //     0xa43b58: bl              #0xec1630  ; AllocateClosureStub
    // 0xa43b5c: LeaveFrame
    //     0xa43b5c: mov             SP, fp
    //     0xa43b60: ldp             fp, lr, [SP], #0x10
    // 0xa43b64: ret
    //     0xa43b64: ret             
    // 0xa43b68: r1 = Function '_placeholderBuilder@2550215462':.
    //     0xa43b68: add             x1, PP, #0x54, lsl #12  ; [pp+0x54720] AnonymousClosure: (0xa43c0c), in [package:octo_image/src/image/image_handler.dart] ImageHandler::_placeholderBuilder (0xa43c54)
    //     0xa43b6c: ldr             x1, [x1, #0x720]
    // 0xa43b70: r0 = AllocateClosure()
    //     0xa43b70: bl              #0xec1630  ; AllocateClosureStub
    // 0xa43b74: LeaveFrame
    //     0xa43b74: mov             SP, fp
    //     0xa43b78: ldp             fp, lr, [SP], #0x10
    // 0xa43b7c: ret
    //     0xa43b7c: ret             
    // 0xa43b80: r1 = Function '_preLoadingBuilder@2550215462':.
    //     0xa43b80: add             x1, PP, #0x54, lsl #12  ; [pp+0x54728] AnonymousClosure: (0xa43ba4), in [package:octo_image/src/image/image_handler.dart] ImageHandler::_preLoadingBuilder (0xa43bec)
    //     0xa43b84: ldr             x1, [x1, #0x728]
    // 0xa43b88: r0 = AllocateClosure()
    //     0xa43b88: bl              #0xec1630  ; AllocateClosureStub
    // 0xa43b8c: LeaveFrame
    //     0xa43b8c: mov             SP, fp
    //     0xa43b90: ldp             fp, lr, [SP], #0x10
    // 0xa43b94: ret
    //     0xa43b94: ret             
    // 0xa43b98: r9 = _placeholderType
    //     0xa43b98: add             x9, PP, #0x54, lsl #12  ; [pp+0x54730] Field <ImageHandler._placeholderType@2550215462>: late (offset: 0x30)
    //     0xa43b9c: ldr             x9, [x9, #0x730]
    // 0xa43ba0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa43ba0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _preLoadingBuilder(dynamic, BuildContext, Widget, int?, bool) {
    // ** addr: 0xa43ba4, size: 0x48
    // 0xa43ba4: EnterFrame
    //     0xa43ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xa43ba8: mov             fp, SP
    // 0xa43bac: ldr             x0, [fp, #0x30]
    // 0xa43bb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa43bb0: ldur            w1, [x0, #0x17]
    // 0xa43bb4: DecompressPointer r1
    //     0xa43bb4: add             x1, x1, HEAP, lsl #32
    // 0xa43bb8: CheckStackOverflow
    //     0xa43bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43bbc: cmp             SP, x16
    //     0xa43bc0: b.ls            #0xa43be4
    // 0xa43bc4: ldr             x2, [fp, #0x28]
    // 0xa43bc8: ldr             x3, [fp, #0x20]
    // 0xa43bcc: ldr             x5, [fp, #0x18]
    // 0xa43bd0: ldr             x6, [fp, #0x10]
    // 0xa43bd4: r0 = _preLoadingBuilder()
    //     0xa43bd4: bl              #0xa43bec  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_preLoadingBuilder
    // 0xa43bd8: LeaveFrame
    //     0xa43bd8: mov             SP, fp
    //     0xa43bdc: ldp             fp, lr, [SP], #0x10
    // 0xa43be0: ret
    //     0xa43be0: ret             
    // 0xa43be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43be4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43be8: b               #0xa43bc4
  }
  _ _preLoadingBuilder(/* No info */) {
    // ** addr: 0xa43bec, size: 0x20
    // 0xa43bec: mov             x0, x3
    // 0xa43bf0: StoreField: r1->field_5b = r6
    //     0xa43bf0: stur            w6, [x1, #0x5b]
    // 0xa43bf4: cmp             w5, NULL
    // 0xa43bf8: r16 = true
    //     0xa43bf8: add             x16, NULL, #0x20  ; true
    // 0xa43bfc: r17 = false
    //     0xa43bfc: add             x17, NULL, #0x30  ; false
    // 0xa43c00: csel            x2, x16, x17, ne
    // 0xa43c04: StoreField: r1->field_5f = r2
    //     0xa43c04: stur            w2, [x1, #0x5f]
    // 0xa43c08: ret
    //     0xa43c08: ret             
  }
  [closure] Widget _placeholderBuilder(dynamic, BuildContext, Widget, int?, bool) {
    // ** addr: 0xa43c0c, size: 0x48
    // 0xa43c0c: EnterFrame
    //     0xa43c0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa43c10: mov             fp, SP
    // 0xa43c14: ldr             x0, [fp, #0x30]
    // 0xa43c18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa43c18: ldur            w1, [x0, #0x17]
    // 0xa43c1c: DecompressPointer r1
    //     0xa43c1c: add             x1, x1, HEAP, lsl #32
    // 0xa43c20: CheckStackOverflow
    //     0xa43c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43c24: cmp             SP, x16
    //     0xa43c28: b.ls            #0xa43c4c
    // 0xa43c2c: ldr             x2, [fp, #0x28]
    // 0xa43c30: ldr             x3, [fp, #0x20]
    // 0xa43c34: ldr             x5, [fp, #0x18]
    // 0xa43c38: ldr             x6, [fp, #0x10]
    // 0xa43c3c: r0 = _placeholderBuilder()
    //     0xa43c3c: bl              #0xa43c54  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_placeholderBuilder
    // 0xa43c40: LeaveFrame
    //     0xa43c40: mov             SP, fp
    //     0xa43c44: ldp             fp, lr, [SP], #0x10
    // 0xa43c48: ret
    //     0xa43c48: ret             
    // 0xa43c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43c50: b               #0xa43c2c
  }
  _ _placeholderBuilder(/* No info */) {
    // ** addr: 0xa43c54, size: 0x80
    // 0xa43c54: EnterFrame
    //     0xa43c54: stp             fp, lr, [SP, #-0x10]!
    //     0xa43c58: mov             fp, SP
    // 0xa43c5c: AllocStack(0x10)
    //     0xa43c5c: sub             SP, SP, #0x10
    // 0xa43c60: SetupParameters(ImageHandler this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xa43c60: mov             x0, x3
    //     0xa43c64: stur            x3, [fp, #-0x10]
    //     0xa43c68: mov             x3, x1
    //     0xa43c6c: stur            x1, [fp, #-8]
    // 0xa43c70: CheckStackOverflow
    //     0xa43c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43c74: cmp             SP, x16
    //     0xa43c78: b.ls            #0xa43ccc
    // 0xa43c7c: cmp             w5, NULL
    // 0xa43c80: b.ne            #0xa43c98
    // 0xa43c84: mov             x1, x3
    // 0xa43c88: r0 = _placeholder()
    //     0xa43c88: bl              #0xa43ddc  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_placeholder
    // 0xa43c8c: LeaveFrame
    //     0xa43c8c: mov             SP, fp
    //     0xa43c90: ldp             fp, lr, [SP], #0x10
    // 0xa43c94: ret
    //     0xa43c94: ret             
    // 0xa43c98: tbnz            w6, #4, #0xa43ca8
    // 0xa43c9c: LeaveFrame
    //     0xa43c9c: mov             SP, fp
    //     0xa43ca0: ldp             fp, lr, [SP], #0x10
    // 0xa43ca4: ret
    //     0xa43ca4: ret             
    // 0xa43ca8: mov             x1, x3
    // 0xa43cac: r0 = _placeholder()
    //     0xa43cac: bl              #0xa43ddc  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_placeholder
    // 0xa43cb0: ldur            x1, [fp, #-8]
    // 0xa43cb4: ldur            x2, [fp, #-0x10]
    // 0xa43cb8: mov             x3, x0
    // 0xa43cbc: r0 = _stack()
    //     0xa43cbc: bl              #0xa43cd4  ; [package:octo_image/src/image/image_handler.dart] ImageHandler::_stack
    // 0xa43cc0: LeaveFrame
    //     0xa43cc0: mov             SP, fp
    //     0xa43cc4: ldp             fp, lr, [SP], #0x10
    // 0xa43cc8: ret
    //     0xa43cc8: ret             
    // 0xa43ccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43ccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43cd0: b               #0xa43c7c
  }
  _ _stack(/* No info */) {
    // ** addr: 0xa43cd4, size: 0xfc
    // 0xa43cd4: EnterFrame
    //     0xa43cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xa43cd8: mov             fp, SP
    // 0xa43cdc: AllocStack(0x18)
    //     0xa43cdc: sub             SP, SP, #0x18
    // 0xa43ce0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa43ce0: stur            x2, [fp, #-8]
    //     0xa43ce4: stur            x3, [fp, #-0x10]
    // 0xa43ce8: r0 = FadeWidget()
    //     0xa43ce8: bl              #0xa43dd0  ; AllocateFadeWidgetStub -> FadeWidget (size=0x1c)
    // 0xa43cec: mov             x1, x0
    // 0xa43cf0: ldur            x0, [fp, #-8]
    // 0xa43cf4: stur            x1, [fp, #-0x18]
    // 0xa43cf8: StoreField: r1->field_b = r0
    //     0xa43cf8: stur            w0, [x1, #0xb]
    // 0xa43cfc: r0 = Instance_Duration
    //     0xa43cfc: ldr             x0, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xa43d00: StoreField: r1->field_f = r0
    //     0xa43d00: stur            w0, [x1, #0xf]
    // 0xa43d04: r0 = Instance_AnimationDirection
    //     0xa43d04: add             x0, PP, #0x54, lsl #12  ; [pp+0x54738] Obj!AnimationDirection@e30081
    //     0xa43d08: ldr             x0, [x0, #0x738]
    // 0xa43d0c: StoreField: r1->field_13 = r0
    //     0xa43d0c: stur            w0, [x1, #0x13]
    // 0xa43d10: r0 = Instance_Cubic
    //     0xa43d10: add             x0, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0xa43d14: ldr             x0, [x0, #0x408]
    // 0xa43d18: ArrayStore: r1[0] = r0  ; List_4
    //     0xa43d18: stur            w0, [x1, #0x17]
    // 0xa43d1c: r0 = FadeWidget()
    //     0xa43d1c: bl              #0xa43dd0  ; AllocateFadeWidgetStub -> FadeWidget (size=0x1c)
    // 0xa43d20: mov             x3, x0
    // 0xa43d24: ldur            x0, [fp, #-0x10]
    // 0xa43d28: stur            x3, [fp, #-8]
    // 0xa43d2c: StoreField: r3->field_b = r0
    //     0xa43d2c: stur            w0, [x3, #0xb]
    // 0xa43d30: r0 = Instance_Duration
    //     0xa43d30: ldr             x0, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0xa43d34: StoreField: r3->field_f = r0
    //     0xa43d34: stur            w0, [x3, #0xf]
    // 0xa43d38: r0 = Instance_AnimationDirection
    //     0xa43d38: add             x0, PP, #0x54, lsl #12  ; [pp+0x54740] Obj!AnimationDirection@e30061
    //     0xa43d3c: ldr             x0, [x0, #0x740]
    // 0xa43d40: StoreField: r3->field_13 = r0
    //     0xa43d40: stur            w0, [x3, #0x13]
    // 0xa43d44: r0 = Instance_Cubic
    //     0xa43d44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xa43d48: ldr             x0, [x0, #0xb28]
    // 0xa43d4c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa43d4c: stur            w0, [x3, #0x17]
    // 0xa43d50: r1 = Null
    //     0xa43d50: mov             x1, NULL
    // 0xa43d54: r2 = 4
    //     0xa43d54: movz            x2, #0x4
    // 0xa43d58: r0 = AllocateArray()
    //     0xa43d58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa43d5c: mov             x2, x0
    // 0xa43d60: ldur            x0, [fp, #-0x18]
    // 0xa43d64: stur            x2, [fp, #-0x10]
    // 0xa43d68: StoreField: r2->field_f = r0
    //     0xa43d68: stur            w0, [x2, #0xf]
    // 0xa43d6c: ldur            x0, [fp, #-8]
    // 0xa43d70: StoreField: r2->field_13 = r0
    //     0xa43d70: stur            w0, [x2, #0x13]
    // 0xa43d74: r1 = <Widget>
    //     0xa43d74: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa43d78: r0 = AllocateGrowableArray()
    //     0xa43d78: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa43d7c: mov             x1, x0
    // 0xa43d80: ldur            x0, [fp, #-0x10]
    // 0xa43d84: stur            x1, [fp, #-8]
    // 0xa43d88: StoreField: r1->field_f = r0
    //     0xa43d88: stur            w0, [x1, #0xf]
    // 0xa43d8c: r0 = 4
    //     0xa43d8c: movz            x0, #0x4
    // 0xa43d90: StoreField: r1->field_b = r0
    //     0xa43d90: stur            w0, [x1, #0xb]
    // 0xa43d94: r0 = Stack()
    //     0xa43d94: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa43d98: r1 = Instance_Alignment
    //     0xa43d98: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa43d9c: ldr             x1, [x1, #0x898]
    // 0xa43da0: StoreField: r0->field_f = r1
    //     0xa43da0: stur            w1, [x0, #0xf]
    // 0xa43da4: r1 = Instance_StackFit
    //     0xa43da4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41458] Obj!StackFit@e35441
    //     0xa43da8: ldr             x1, [x1, #0x458]
    // 0xa43dac: ArrayStore: r0[0] = r1  ; List_4
    //     0xa43dac: stur            w1, [x0, #0x17]
    // 0xa43db0: r1 = Instance_Clip
    //     0xa43db0: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa43db4: ldr             x1, [x1, #0x7c0]
    // 0xa43db8: StoreField: r0->field_1b = r1
    //     0xa43db8: stur            w1, [x0, #0x1b]
    // 0xa43dbc: ldur            x1, [fp, #-8]
    // 0xa43dc0: StoreField: r0->field_b = r1
    //     0xa43dc0: stur            w1, [x0, #0xb]
    // 0xa43dc4: LeaveFrame
    //     0xa43dc4: mov             SP, fp
    //     0xa43dc8: ldp             fp, lr, [SP], #0x10
    // 0xa43dcc: ret
    //     0xa43dcc: ret             
  }
  _ _placeholder(/* No info */) {
    // ** addr: 0xa43ddc, size: 0x70
    // 0xa43ddc: EnterFrame
    //     0xa43ddc: stp             fp, lr, [SP, #-0x10]!
    //     0xa43de0: mov             fp, SP
    // 0xa43de4: AllocStack(0x18)
    //     0xa43de4: sub             SP, SP, #0x18
    // 0xa43de8: CheckStackOverflow
    //     0xa43de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43dec: cmp             SP, x16
    //     0xa43df0: b.ls            #0xa43e44
    // 0xa43df4: LoadField: r0 = r1->field_37
    //     0xa43df4: ldur            w0, [x1, #0x37]
    // 0xa43df8: DecompressPointer r0
    //     0xa43df8: add             x0, x0, HEAP, lsl #32
    // 0xa43dfc: cmp             w0, NULL
    // 0xa43e00: b.eq            #0xa43e20
    // 0xa43e04: stp             x2, x0, [SP]
    // 0xa43e08: ClosureCall
    //     0xa43e08: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa43e0c: ldur            x2, [x0, #0x1f]
    //     0xa43e10: blr             x2
    // 0xa43e14: LeaveFrame
    //     0xa43e14: mov             SP, fp
    //     0xa43e18: ldp             fp, lr, [SP], #0x10
    // 0xa43e1c: ret
    //     0xa43e1c: ret             
    // 0xa43e20: r0 = Container()
    //     0xa43e20: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa43e24: mov             x1, x0
    // 0xa43e28: stur            x0, [fp, #-8]
    // 0xa43e2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa43e2c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa43e30: r0 = Container()
    //     0xa43e30: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa43e34: ldur            x0, [fp, #-8]
    // 0xa43e38: LeaveFrame
    //     0xa43e38: mov             SP, fp
    //     0xa43e3c: ldp             fp, lr, [SP], #0x10
    // 0xa43e40: ret
    //     0xa43e40: ret             
    // 0xa43e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa43e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa43e48: b               #0xa43df4
  }
  [closure] Widget _imageBuilder(dynamic, BuildContext, Widget, int?, bool) {
    // ** addr: 0xa43e4c, size: 0x8
    // 0xa43e4c: ldr             x0, [SP, #0x10]
    // 0xa43e50: ret
    //     0xa43e50: ret             
  }
  _ imageLoadingBuilder(/* No info */) {
    // ** addr: 0xa43e54, size: 0x5c
    // 0xa43e54: EnterFrame
    //     0xa43e54: stp             fp, lr, [SP, #-0x10]!
    //     0xa43e58: mov             fp, SP
    // 0xa43e5c: mov             x2, x1
    // 0xa43e60: LoadField: r0 = r2->field_2f
    //     0xa43e60: ldur            w0, [x2, #0x2f]
    // 0xa43e64: DecompressPointer r0
    //     0xa43e64: add             x0, x0, HEAP, lsl #32
    // 0xa43e68: r16 = Sentinel
    //     0xa43e68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa43e6c: cmp             w0, w16
    // 0xa43e70: b.eq            #0xa43ea4
    // 0xa43e74: r16 = Instance__PlaceholderType
    //     0xa43e74: add             x16, PP, #0x54, lsl #12  ; [pp+0x54748] Obj!_PlaceholderType@e30041
    //     0xa43e78: ldr             x16, [x16, #0x748]
    // 0xa43e7c: cmp             w0, w16
    // 0xa43e80: b.ne            #0xa43e94
    // 0xa43e84: r1 = Function '_loadingBuilder@2550215462':.
    //     0xa43e84: add             x1, PP, #0x54, lsl #12  ; [pp+0x54750] AnonymousClosure: (0xa17124), in [package:octo_image/src/image/image_handler.dart] ImageHandler::_loadingBuilder (0xa17168)
    //     0xa43e88: ldr             x1, [x1, #0x750]
    // 0xa43e8c: r0 = AllocateClosure()
    //     0xa43e8c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa43e90: b               #0xa43e98
    // 0xa43e94: r0 = Null
    //     0xa43e94: mov             x0, NULL
    // 0xa43e98: LeaveFrame
    //     0xa43e98: mov             SP, fp
    //     0xa43e9c: ldp             fp, lr, [SP], #0x10
    // 0xa43ea0: ret
    //     0xa43ea0: ret             
    // 0xa43ea4: r9 = _placeholderType
    //     0xa43ea4: add             x9, PP, #0x54, lsl #12  ; [pp+0x54730] Field <ImageHandler._placeholderType@2550215462>: late (offset: 0x30)
    //     0xa43ea8: ldr             x9, [x9, #0x730]
    // 0xa43eac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa43eac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 6825, size: 0x14, field offset: 0x14
enum _PlaceholderType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d62c, size: 0x64
    // 0xc4d62c: EnterFrame
    //     0xc4d62c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d630: mov             fp, SP
    // 0xc4d634: AllocStack(0x10)
    //     0xc4d634: sub             SP, SP, #0x10
    // 0xc4d638: SetupParameters(_PlaceholderType this /* r1 => r0, fp-0x8 */)
    //     0xc4d638: mov             x0, x1
    //     0xc4d63c: stur            x1, [fp, #-8]
    // 0xc4d640: CheckStackOverflow
    //     0xc4d640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d644: cmp             SP, x16
    //     0xc4d648: b.ls            #0xc4d688
    // 0xc4d64c: r1 = Null
    //     0xc4d64c: mov             x1, NULL
    // 0xc4d650: r2 = 4
    //     0xc4d650: movz            x2, #0x4
    // 0xc4d654: r0 = AllocateArray()
    //     0xc4d654: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d658: r16 = "_PlaceholderType."
    //     0xc4d658: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a70] "_PlaceholderType."
    //     0xc4d65c: ldr             x16, [x16, #0xa70]
    // 0xc4d660: StoreField: r0->field_f = r16
    //     0xc4d660: stur            w16, [x0, #0xf]
    // 0xc4d664: ldur            x1, [fp, #-8]
    // 0xc4d668: LoadField: r2 = r1->field_f
    //     0xc4d668: ldur            w2, [x1, #0xf]
    // 0xc4d66c: DecompressPointer r2
    //     0xc4d66c: add             x2, x2, HEAP, lsl #32
    // 0xc4d670: StoreField: r0->field_13 = r2
    //     0xc4d670: stur            w2, [x0, #0x13]
    // 0xc4d674: str             x0, [SP]
    // 0xc4d678: r0 = _interpolate()
    //     0xc4d678: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d67c: LeaveFrame
    //     0xc4d67c: mov             SP, fp
    //     0xc4d680: ldp             fp, lr, [SP], #0x10
    // 0xc4d684: ret
    //     0xc4d684: ret             
    // 0xc4d688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d68c: b               #0xc4d64c
  }
}
