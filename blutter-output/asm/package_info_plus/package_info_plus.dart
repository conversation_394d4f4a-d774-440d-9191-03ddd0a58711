// lib: , url: package:package_info_plus/package_info_plus.dart

// class id: 1050748, size: 0x8
class :: {
}

// class id: 948, size: 0x28, field offset: 0x8
class PackageInfo extends Object {

  static _ fromPlatform(/* No info */) async {
    // ** addr: 0x91ed90, size: 0x128
    // 0x91ed90: EnterFrame
    //     0x91ed90: stp             fp, lr, [SP, #-0x10]!
    //     0x91ed94: mov             fp, SP
    // 0x91ed98: AllocStack(0x48)
    //     0x91ed98: sub             SP, SP, #0x48
    // 0x91ed9c: SetupParameters()
    //     0x91ed9c: stur            NULL, [fp, #-8]
    // 0x91eda0: CheckStackOverflow
    //     0x91eda0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91eda4: cmp             SP, x16
    //     0x91eda8: b.ls            #0x91eeb0
    // 0x91edac: InitAsync() -> Future<PackageInfo>
    //     0x91edac: add             x0, PP, #0xf, lsl #12  ; [pp+0xfe40] TypeArguments: <PackageInfo>
    //     0x91edb0: ldr             x0, [x0, #0xe40]
    //     0x91edb4: bl              #0x661298  ; InitAsyncStub
    // 0x91edb8: r0 = LoadStaticField(0x618)
    //     0x91edb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91edbc: ldr             x0, [x0, #0xc30]
    // 0x91edc0: cmp             w0, NULL
    // 0x91edc4: b.eq            #0x91edcc
    // 0x91edc8: r0 = ReturnAsyncNotFuture()
    //     0x91edc8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91edcc: r0 = InitLateStaticField(0x16c0) // [package:package_info_plus_platform_interface/package_info_platform_interface.dart] PackageInfoPlatform::_instance
    //     0x91edcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91edd0: ldr             x0, [x0, #0x2d80]
    //     0x91edd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91edd8: cmp             w0, w16
    //     0x91eddc: b.ne            #0x91edec
    //     0x91ede0: add             x2, PP, #0xf, lsl #12  ; [pp+0xfe48] Field <PackageInfoPlatform._instance@2551110083>: static late (offset: 0x16c0)
    //     0x91ede4: ldr             x2, [x2, #0xe48]
    //     0x91ede8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x91edec: mov             x1, x0
    // 0x91edf0: r0 = getAll()
    //     0x91edf0: bl              #0x91eec4  ; [package:package_info_plus_platform_interface/method_channel_package_info.dart] MethodChannelPackageInfo::getAll
    // 0x91edf4: mov             x1, x0
    // 0x91edf8: stur            x1, [fp, #-0x10]
    // 0x91edfc: r0 = Await()
    //     0x91edfc: bl              #0x661044  ; AwaitStub
    // 0x91ee00: LoadField: r1 = r0->field_7
    //     0x91ee00: ldur            w1, [x0, #7]
    // 0x91ee04: DecompressPointer r1
    //     0x91ee04: add             x1, x1, HEAP, lsl #32
    // 0x91ee08: stur            x1, [fp, #-0x48]
    // 0x91ee0c: LoadField: r2 = r0->field_b
    //     0x91ee0c: ldur            w2, [x0, #0xb]
    // 0x91ee10: DecompressPointer r2
    //     0x91ee10: add             x2, x2, HEAP, lsl #32
    // 0x91ee14: stur            x2, [fp, #-0x40]
    // 0x91ee18: LoadField: r3 = r0->field_f
    //     0x91ee18: ldur            w3, [x0, #0xf]
    // 0x91ee1c: DecompressPointer r3
    //     0x91ee1c: add             x3, x3, HEAP, lsl #32
    // 0x91ee20: stur            x3, [fp, #-0x38]
    // 0x91ee24: LoadField: r4 = r0->field_13
    //     0x91ee24: ldur            w4, [x0, #0x13]
    // 0x91ee28: DecompressPointer r4
    //     0x91ee28: add             x4, x4, HEAP, lsl #32
    // 0x91ee2c: stur            x4, [fp, #-0x30]
    // 0x91ee30: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x91ee30: ldur            w5, [x0, #0x17]
    // 0x91ee34: DecompressPointer r5
    //     0x91ee34: add             x5, x5, HEAP, lsl #32
    // 0x91ee38: stur            x5, [fp, #-0x28]
    // 0x91ee3c: LoadField: r6 = r0->field_1b
    //     0x91ee3c: ldur            w6, [x0, #0x1b]
    // 0x91ee40: DecompressPointer r6
    //     0x91ee40: add             x6, x6, HEAP, lsl #32
    // 0x91ee44: stur            x6, [fp, #-0x20]
    // 0x91ee48: LoadField: r7 = r0->field_1f
    //     0x91ee48: ldur            w7, [x0, #0x1f]
    // 0x91ee4c: DecompressPointer r7
    //     0x91ee4c: add             x7, x7, HEAP, lsl #32
    // 0x91ee50: stur            x7, [fp, #-0x18]
    // 0x91ee54: LoadField: r8 = r0->field_23
    //     0x91ee54: ldur            w8, [x0, #0x23]
    // 0x91ee58: DecompressPointer r8
    //     0x91ee58: add             x8, x8, HEAP, lsl #32
    // 0x91ee5c: stur            x8, [fp, #-0x10]
    // 0x91ee60: r0 = PackageInfo()
    //     0x91ee60: bl              #0x91eeb8  ; AllocatePackageInfoStub -> PackageInfo (size=0x28)
    // 0x91ee64: ldur            x1, [fp, #-0x48]
    // 0x91ee68: StoreField: r0->field_7 = r1
    //     0x91ee68: stur            w1, [x0, #7]
    // 0x91ee6c: ldur            x1, [fp, #-0x40]
    // 0x91ee70: StoreField: r0->field_b = r1
    //     0x91ee70: stur            w1, [x0, #0xb]
    // 0x91ee74: ldur            x1, [fp, #-0x38]
    // 0x91ee78: StoreField: r0->field_f = r1
    //     0x91ee78: stur            w1, [x0, #0xf]
    // 0x91ee7c: ldur            x1, [fp, #-0x30]
    // 0x91ee80: StoreField: r0->field_13 = r1
    //     0x91ee80: stur            w1, [x0, #0x13]
    // 0x91ee84: ldur            x1, [fp, #-0x28]
    // 0x91ee88: ArrayStore: r0[0] = r1  ; List_4
    //     0x91ee88: stur            w1, [x0, #0x17]
    // 0x91ee8c: ldur            x1, [fp, #-0x20]
    // 0x91ee90: StoreField: r0->field_1b = r1
    //     0x91ee90: stur            w1, [x0, #0x1b]
    // 0x91ee94: ldur            x1, [fp, #-0x18]
    // 0x91ee98: StoreField: r0->field_1f = r1
    //     0x91ee98: stur            w1, [x0, #0x1f]
    // 0x91ee9c: ldur            x1, [fp, #-0x10]
    // 0x91eea0: StoreField: r0->field_23 = r1
    //     0x91eea0: stur            w1, [x0, #0x23]
    // 0x91eea4: StoreStaticField(0x618, r0)
    //     0x91eea4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x91eea8: str             x0, [x1, #0xc30]
    // 0x91eeac: r0 = ReturnAsyncNotFuture()
    //     0x91eeac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91eeb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91eeb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91eeb4: b               #0x91edac
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf14b0, size: 0x228
    // 0xbf14b0: EnterFrame
    //     0xbf14b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf14b4: mov             fp, SP
    // 0xbf14b8: AllocStack(0x20)
    //     0xbf14b8: sub             SP, SP, #0x20
    // 0xbf14bc: CheckStackOverflow
    //     0xbf14bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf14c0: cmp             SP, x16
    //     0xbf14c4: b.ls            #0xbf16d0
    // 0xbf14c8: ldr             x1, [fp, #0x10]
    // 0xbf14cc: LoadField: r0 = r1->field_7
    //     0xbf14cc: ldur            w0, [x1, #7]
    // 0xbf14d0: DecompressPointer r0
    //     0xbf14d0: add             x0, x0, HEAP, lsl #32
    // 0xbf14d4: r2 = LoadClassIdInstr(r0)
    //     0xbf14d4: ldur            x2, [x0, #-1]
    //     0xbf14d8: ubfx            x2, x2, #0xc, #0x14
    // 0xbf14dc: str             x0, [SP]
    // 0xbf14e0: mov             x0, x2
    // 0xbf14e4: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf14e4: movz            x17, #0x64af
    //     0xbf14e8: add             lr, x0, x17
    //     0xbf14ec: ldr             lr, [x21, lr, lsl #3]
    //     0xbf14f0: blr             lr
    // 0xbf14f4: mov             x2, x0
    // 0xbf14f8: ldr             x1, [fp, #0x10]
    // 0xbf14fc: stur            x2, [fp, #-8]
    // 0xbf1500: LoadField: r0 = r1->field_b
    //     0xbf1500: ldur            w0, [x1, #0xb]
    // 0xbf1504: DecompressPointer r0
    //     0xbf1504: add             x0, x0, HEAP, lsl #32
    // 0xbf1508: r3 = LoadClassIdInstr(r0)
    //     0xbf1508: ldur            x3, [x0, #-1]
    //     0xbf150c: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1510: str             x0, [SP]
    // 0xbf1514: mov             x0, x3
    // 0xbf1518: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1518: movz            x17, #0x64af
    //     0xbf151c: add             lr, x0, x17
    //     0xbf1520: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1524: blr             lr
    // 0xbf1528: mov             x1, x0
    // 0xbf152c: ldur            x0, [fp, #-8]
    // 0xbf1530: r2 = LoadInt32Instr(r0)
    //     0xbf1530: sbfx            x2, x0, #1, #0x1f
    // 0xbf1534: r0 = LoadInt32Instr(r1)
    //     0xbf1534: sbfx            x0, x1, #1, #0x1f
    // 0xbf1538: eor             x1, x2, x0
    // 0xbf153c: ldr             x2, [fp, #0x10]
    // 0xbf1540: stur            x1, [fp, #-0x10]
    // 0xbf1544: LoadField: r0 = r2->field_f
    //     0xbf1544: ldur            w0, [x2, #0xf]
    // 0xbf1548: DecompressPointer r0
    //     0xbf1548: add             x0, x0, HEAP, lsl #32
    // 0xbf154c: r3 = LoadClassIdInstr(r0)
    //     0xbf154c: ldur            x3, [x0, #-1]
    //     0xbf1550: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1554: str             x0, [SP]
    // 0xbf1558: mov             x0, x3
    // 0xbf155c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf155c: movz            x17, #0x64af
    //     0xbf1560: add             lr, x0, x17
    //     0xbf1564: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1568: blr             lr
    // 0xbf156c: r1 = LoadInt32Instr(r0)
    //     0xbf156c: sbfx            x1, x0, #1, #0x1f
    // 0xbf1570: ldur            x0, [fp, #-0x10]
    // 0xbf1574: eor             x2, x0, x1
    // 0xbf1578: ldr             x1, [fp, #0x10]
    // 0xbf157c: stur            x2, [fp, #-0x18]
    // 0xbf1580: LoadField: r0 = r1->field_13
    //     0xbf1580: ldur            w0, [x1, #0x13]
    // 0xbf1584: DecompressPointer r0
    //     0xbf1584: add             x0, x0, HEAP, lsl #32
    // 0xbf1588: r3 = LoadClassIdInstr(r0)
    //     0xbf1588: ldur            x3, [x0, #-1]
    //     0xbf158c: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1590: str             x0, [SP]
    // 0xbf1594: mov             x0, x3
    // 0xbf1598: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1598: movz            x17, #0x64af
    //     0xbf159c: add             lr, x0, x17
    //     0xbf15a0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf15a4: blr             lr
    // 0xbf15a8: r1 = LoadInt32Instr(r0)
    //     0xbf15a8: sbfx            x1, x0, #1, #0x1f
    // 0xbf15ac: ldur            x0, [fp, #-0x18]
    // 0xbf15b0: eor             x2, x0, x1
    // 0xbf15b4: ldr             x1, [fp, #0x10]
    // 0xbf15b8: stur            x2, [fp, #-0x10]
    // 0xbf15bc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbf15bc: ldur            w0, [x1, #0x17]
    // 0xbf15c0: DecompressPointer r0
    //     0xbf15c0: add             x0, x0, HEAP, lsl #32
    // 0xbf15c4: r3 = LoadClassIdInstr(r0)
    //     0xbf15c4: ldur            x3, [x0, #-1]
    //     0xbf15c8: ubfx            x3, x3, #0xc, #0x14
    // 0xbf15cc: str             x0, [SP]
    // 0xbf15d0: mov             x0, x3
    // 0xbf15d4: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf15d4: movz            x17, #0x64af
    //     0xbf15d8: add             lr, x0, x17
    //     0xbf15dc: ldr             lr, [x21, lr, lsl #3]
    //     0xbf15e0: blr             lr
    // 0xbf15e4: r1 = LoadInt32Instr(r0)
    //     0xbf15e4: sbfx            x1, x0, #1, #0x1f
    // 0xbf15e8: ldur            x0, [fp, #-0x10]
    // 0xbf15ec: eor             x2, x0, x1
    // 0xbf15f0: ldr             x1, [fp, #0x10]
    // 0xbf15f4: stur            x2, [fp, #-0x18]
    // 0xbf15f8: LoadField: r0 = r1->field_1b
    //     0xbf15f8: ldur            w0, [x1, #0x1b]
    // 0xbf15fc: DecompressPointer r0
    //     0xbf15fc: add             x0, x0, HEAP, lsl #32
    // 0xbf1600: r3 = LoadClassIdInstr(r0)
    //     0xbf1600: ldur            x3, [x0, #-1]
    //     0xbf1604: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1608: str             x0, [SP]
    // 0xbf160c: mov             x0, x3
    // 0xbf1610: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1610: movz            x17, #0x64af
    //     0xbf1614: add             lr, x0, x17
    //     0xbf1618: ldr             lr, [x21, lr, lsl #3]
    //     0xbf161c: blr             lr
    // 0xbf1620: r1 = LoadInt32Instr(r0)
    //     0xbf1620: sbfx            x1, x0, #1, #0x1f
    // 0xbf1624: ldur            x0, [fp, #-0x18]
    // 0xbf1628: eor             x2, x0, x1
    // 0xbf162c: ldr             x1, [fp, #0x10]
    // 0xbf1630: stur            x2, [fp, #-0x10]
    // 0xbf1634: LoadField: r0 = r1->field_1f
    //     0xbf1634: ldur            w0, [x1, #0x1f]
    // 0xbf1638: DecompressPointer r0
    //     0xbf1638: add             x0, x0, HEAP, lsl #32
    // 0xbf163c: r3 = LoadClassIdInstr(r0)
    //     0xbf163c: ldur            x3, [x0, #-1]
    //     0xbf1640: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1644: str             x0, [SP]
    // 0xbf1648: mov             x0, x3
    // 0xbf164c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf164c: movz            x17, #0x64af
    //     0xbf1650: add             lr, x0, x17
    //     0xbf1654: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1658: blr             lr
    // 0xbf165c: r1 = LoadInt32Instr(r0)
    //     0xbf165c: sbfx            x1, x0, #1, #0x1f
    //     0xbf1660: tbz             w0, #0, #0xbf1668
    //     0xbf1664: ldur            x1, [x0, #7]
    // 0xbf1668: ldur            x0, [fp, #-0x10]
    // 0xbf166c: eor             x2, x0, x1
    // 0xbf1670: ldr             x0, [fp, #0x10]
    // 0xbf1674: stur            x2, [fp, #-0x18]
    // 0xbf1678: LoadField: r1 = r0->field_23
    //     0xbf1678: ldur            w1, [x0, #0x23]
    // 0xbf167c: DecompressPointer r1
    //     0xbf167c: add             x1, x1, HEAP, lsl #32
    // 0xbf1680: r0 = LoadClassIdInstr(r1)
    //     0xbf1680: ldur            x0, [x1, #-1]
    //     0xbf1684: ubfx            x0, x0, #0xc, #0x14
    // 0xbf1688: str             x1, [SP]
    // 0xbf168c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf168c: movz            x17, #0x64af
    //     0xbf1690: add             lr, x0, x17
    //     0xbf1694: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1698: blr             lr
    // 0xbf169c: r2 = LoadInt32Instr(r0)
    //     0xbf169c: sbfx            x2, x0, #1, #0x1f
    //     0xbf16a0: tbz             w0, #0, #0xbf16a8
    //     0xbf16a4: ldur            x2, [x0, #7]
    // 0xbf16a8: ldur            x3, [fp, #-0x18]
    // 0xbf16ac: eor             x4, x3, x2
    // 0xbf16b0: r0 = BoxInt64Instr(r4)
    //     0xbf16b0: sbfiz           x0, x4, #1, #0x1f
    //     0xbf16b4: cmp             x4, x0, asr #1
    //     0xbf16b8: b.eq            #0xbf16c4
    //     0xbf16bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf16c0: stur            x4, [x0, #7]
    // 0xbf16c4: LeaveFrame
    //     0xbf16c4: mov             SP, fp
    //     0xbf16c8: ldp             fp, lr, [SP], #0x10
    // 0xbf16cc: ret
    //     0xbf16cc: ret             
    // 0xbf16d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf16d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf16d4: b               #0xbf14c8
  }
  _ toString(/* No info */) {
    // ** addr: 0xc33574, size: 0x10c
    // 0xc33574: EnterFrame
    //     0xc33574: stp             fp, lr, [SP, #-0x10]!
    //     0xc33578: mov             fp, SP
    // 0xc3357c: AllocStack(0x8)
    //     0xc3357c: sub             SP, SP, #8
    // 0xc33580: CheckStackOverflow
    //     0xc33580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33584: cmp             SP, x16
    //     0xc33588: b.ls            #0xc33678
    // 0xc3358c: r1 = Null
    //     0xc3358c: mov             x1, NULL
    // 0xc33590: r2 = 34
    //     0xc33590: movz            x2, #0x22
    // 0xc33594: r0 = AllocateArray()
    //     0xc33594: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33598: r16 = "PackageInfo(appName: "
    //     0xc33598: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db78] "PackageInfo(appName: "
    //     0xc3359c: ldr             x16, [x16, #0xb78]
    // 0xc335a0: StoreField: r0->field_f = r16
    //     0xc335a0: stur            w16, [x0, #0xf]
    // 0xc335a4: ldr             x1, [fp, #0x10]
    // 0xc335a8: LoadField: r2 = r1->field_7
    //     0xc335a8: ldur            w2, [x1, #7]
    // 0xc335ac: DecompressPointer r2
    //     0xc335ac: add             x2, x2, HEAP, lsl #32
    // 0xc335b0: StoreField: r0->field_13 = r2
    //     0xc335b0: stur            w2, [x0, #0x13]
    // 0xc335b4: r16 = ", buildNumber: "
    //     0xc335b4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db80] ", buildNumber: "
    //     0xc335b8: ldr             x16, [x16, #0xb80]
    // 0xc335bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xc335bc: stur            w16, [x0, #0x17]
    // 0xc335c0: LoadField: r2 = r1->field_13
    //     0xc335c0: ldur            w2, [x1, #0x13]
    // 0xc335c4: DecompressPointer r2
    //     0xc335c4: add             x2, x2, HEAP, lsl #32
    // 0xc335c8: StoreField: r0->field_1b = r2
    //     0xc335c8: stur            w2, [x0, #0x1b]
    // 0xc335cc: r16 = ", packageName: "
    //     0xc335cc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db88] ", packageName: "
    //     0xc335d0: ldr             x16, [x16, #0xb88]
    // 0xc335d4: StoreField: r0->field_1f = r16
    //     0xc335d4: stur            w16, [x0, #0x1f]
    // 0xc335d8: LoadField: r2 = r1->field_b
    //     0xc335d8: ldur            w2, [x1, #0xb]
    // 0xc335dc: DecompressPointer r2
    //     0xc335dc: add             x2, x2, HEAP, lsl #32
    // 0xc335e0: StoreField: r0->field_23 = r2
    //     0xc335e0: stur            w2, [x0, #0x23]
    // 0xc335e4: r16 = ", version: "
    //     0xc335e4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db90] ", version: "
    //     0xc335e8: ldr             x16, [x16, #0xb90]
    // 0xc335ec: StoreField: r0->field_27 = r16
    //     0xc335ec: stur            w16, [x0, #0x27]
    // 0xc335f0: LoadField: r2 = r1->field_f
    //     0xc335f0: ldur            w2, [x1, #0xf]
    // 0xc335f4: DecompressPointer r2
    //     0xc335f4: add             x2, x2, HEAP, lsl #32
    // 0xc335f8: StoreField: r0->field_2b = r2
    //     0xc335f8: stur            w2, [x0, #0x2b]
    // 0xc335fc: r16 = ", buildSignature: "
    //     0xc335fc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db98] ", buildSignature: "
    //     0xc33600: ldr             x16, [x16, #0xb98]
    // 0xc33604: StoreField: r0->field_2f = r16
    //     0xc33604: stur            w16, [x0, #0x2f]
    // 0xc33608: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc33608: ldur            w2, [x1, #0x17]
    // 0xc3360c: DecompressPointer r2
    //     0xc3360c: add             x2, x2, HEAP, lsl #32
    // 0xc33610: StoreField: r0->field_33 = r2
    //     0xc33610: stur            w2, [x0, #0x33]
    // 0xc33614: r16 = ", installerStore: "
    //     0xc33614: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1dba0] ", installerStore: "
    //     0xc33618: ldr             x16, [x16, #0xba0]
    // 0xc3361c: StoreField: r0->field_37 = r16
    //     0xc3361c: stur            w16, [x0, #0x37]
    // 0xc33620: LoadField: r2 = r1->field_1b
    //     0xc33620: ldur            w2, [x1, #0x1b]
    // 0xc33624: DecompressPointer r2
    //     0xc33624: add             x2, x2, HEAP, lsl #32
    // 0xc33628: StoreField: r0->field_3b = r2
    //     0xc33628: stur            w2, [x0, #0x3b]
    // 0xc3362c: r16 = ", installTime: "
    //     0xc3362c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1dba8] ", installTime: "
    //     0xc33630: ldr             x16, [x16, #0xba8]
    // 0xc33634: StoreField: r0->field_3f = r16
    //     0xc33634: stur            w16, [x0, #0x3f]
    // 0xc33638: LoadField: r2 = r1->field_1f
    //     0xc33638: ldur            w2, [x1, #0x1f]
    // 0xc3363c: DecompressPointer r2
    //     0xc3363c: add             x2, x2, HEAP, lsl #32
    // 0xc33640: StoreField: r0->field_43 = r2
    //     0xc33640: stur            w2, [x0, #0x43]
    // 0xc33644: r16 = ", updateTime: "
    //     0xc33644: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1dbb0] ", updateTime: "
    //     0xc33648: ldr             x16, [x16, #0xbb0]
    // 0xc3364c: StoreField: r0->field_47 = r16
    //     0xc3364c: stur            w16, [x0, #0x47]
    // 0xc33650: LoadField: r2 = r1->field_23
    //     0xc33650: ldur            w2, [x1, #0x23]
    // 0xc33654: DecompressPointer r2
    //     0xc33654: add             x2, x2, HEAP, lsl #32
    // 0xc33658: StoreField: r0->field_4b = r2
    //     0xc33658: stur            w2, [x0, #0x4b]
    // 0xc3365c: r16 = ")"
    //     0xc3365c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc33660: StoreField: r0->field_4f = r16
    //     0xc33660: stur            w16, [x0, #0x4f]
    // 0xc33664: str             x0, [SP]
    // 0xc33668: r0 = _interpolate()
    //     0xc33668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3366c: LeaveFrame
    //     0xc3366c: mov             SP, fp
    //     0xc33670: ldp             fp, lr, [SP], #0x10
    // 0xc33674: ret
    //     0xc33674: ret             
    // 0xc33678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3367c: b               #0xc3358c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c0fc, size: 0x250
    // 0xd7c0fc: EnterFrame
    //     0xd7c0fc: stp             fp, lr, [SP, #-0x10]!
    //     0xd7c100: mov             fp, SP
    // 0xd7c104: AllocStack(0x10)
    //     0xd7c104: sub             SP, SP, #0x10
    // 0xd7c108: CheckStackOverflow
    //     0xd7c108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7c10c: cmp             SP, x16
    //     0xd7c110: b.ls            #0xd7c344
    // 0xd7c114: ldr             x0, [fp, #0x10]
    // 0xd7c118: cmp             w0, NULL
    // 0xd7c11c: b.ne            #0xd7c130
    // 0xd7c120: r0 = false
    //     0xd7c120: add             x0, NULL, #0x30  ; false
    // 0xd7c124: LeaveFrame
    //     0xd7c124: mov             SP, fp
    //     0xd7c128: ldp             fp, lr, [SP], #0x10
    // 0xd7c12c: ret
    //     0xd7c12c: ret             
    // 0xd7c130: ldr             x1, [fp, #0x18]
    // 0xd7c134: cmp             w1, w0
    // 0xd7c138: b.ne            #0xd7c144
    // 0xd7c13c: r0 = true
    //     0xd7c13c: add             x0, NULL, #0x20  ; true
    // 0xd7c140: b               #0xd7c338
    // 0xd7c144: r2 = 60
    //     0xd7c144: movz            x2, #0x3c
    // 0xd7c148: branchIfSmi(r0, 0xd7c154)
    //     0xd7c148: tbz             w0, #0, #0xd7c154
    // 0xd7c14c: r2 = LoadClassIdInstr(r0)
    //     0xd7c14c: ldur            x2, [x0, #-1]
    //     0xd7c150: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c154: cmp             x2, #0x3b4
    // 0xd7c158: b.ne            #0xd7c334
    // 0xd7c15c: r16 = PackageInfo
    //     0xd7c15c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1dbb8] Type: PackageInfo
    //     0xd7c160: ldr             x16, [x16, #0xbb8]
    // 0xd7c164: r30 = PackageInfo
    //     0xd7c164: add             lr, PP, #0x1d, lsl #12  ; [pp+0x1dbb8] Type: PackageInfo
    //     0xd7c168: ldr             lr, [lr, #0xbb8]
    // 0xd7c16c: stp             lr, x16, [SP]
    // 0xd7c170: r0 = ==()
    //     0xd7c170: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7c174: tbnz            w0, #4, #0xd7c334
    // 0xd7c178: ldr             x2, [fp, #0x18]
    // 0xd7c17c: ldr             x1, [fp, #0x10]
    // 0xd7c180: LoadField: r0 = r2->field_7
    //     0xd7c180: ldur            w0, [x2, #7]
    // 0xd7c184: DecompressPointer r0
    //     0xd7c184: add             x0, x0, HEAP, lsl #32
    // 0xd7c188: LoadField: r3 = r1->field_7
    //     0xd7c188: ldur            w3, [x1, #7]
    // 0xd7c18c: DecompressPointer r3
    //     0xd7c18c: add             x3, x3, HEAP, lsl #32
    // 0xd7c190: r4 = LoadClassIdInstr(r0)
    //     0xd7c190: ldur            x4, [x0, #-1]
    //     0xd7c194: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c198: stp             x3, x0, [SP]
    // 0xd7c19c: mov             x0, x4
    // 0xd7c1a0: mov             lr, x0
    // 0xd7c1a4: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c1a8: blr             lr
    // 0xd7c1ac: tbnz            w0, #4, #0xd7c334
    // 0xd7c1b0: ldr             x2, [fp, #0x18]
    // 0xd7c1b4: ldr             x1, [fp, #0x10]
    // 0xd7c1b8: LoadField: r0 = r2->field_b
    //     0xd7c1b8: ldur            w0, [x2, #0xb]
    // 0xd7c1bc: DecompressPointer r0
    //     0xd7c1bc: add             x0, x0, HEAP, lsl #32
    // 0xd7c1c0: LoadField: r3 = r1->field_b
    //     0xd7c1c0: ldur            w3, [x1, #0xb]
    // 0xd7c1c4: DecompressPointer r3
    //     0xd7c1c4: add             x3, x3, HEAP, lsl #32
    // 0xd7c1c8: r4 = LoadClassIdInstr(r0)
    //     0xd7c1c8: ldur            x4, [x0, #-1]
    //     0xd7c1cc: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c1d0: stp             x3, x0, [SP]
    // 0xd7c1d4: mov             x0, x4
    // 0xd7c1d8: mov             lr, x0
    // 0xd7c1dc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c1e0: blr             lr
    // 0xd7c1e4: tbnz            w0, #4, #0xd7c334
    // 0xd7c1e8: ldr             x2, [fp, #0x18]
    // 0xd7c1ec: ldr             x1, [fp, #0x10]
    // 0xd7c1f0: LoadField: r0 = r2->field_f
    //     0xd7c1f0: ldur            w0, [x2, #0xf]
    // 0xd7c1f4: DecompressPointer r0
    //     0xd7c1f4: add             x0, x0, HEAP, lsl #32
    // 0xd7c1f8: LoadField: r3 = r1->field_f
    //     0xd7c1f8: ldur            w3, [x1, #0xf]
    // 0xd7c1fc: DecompressPointer r3
    //     0xd7c1fc: add             x3, x3, HEAP, lsl #32
    // 0xd7c200: r4 = LoadClassIdInstr(r0)
    //     0xd7c200: ldur            x4, [x0, #-1]
    //     0xd7c204: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c208: stp             x3, x0, [SP]
    // 0xd7c20c: mov             x0, x4
    // 0xd7c210: mov             lr, x0
    // 0xd7c214: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c218: blr             lr
    // 0xd7c21c: tbnz            w0, #4, #0xd7c334
    // 0xd7c220: ldr             x2, [fp, #0x18]
    // 0xd7c224: ldr             x1, [fp, #0x10]
    // 0xd7c228: LoadField: r0 = r2->field_13
    //     0xd7c228: ldur            w0, [x2, #0x13]
    // 0xd7c22c: DecompressPointer r0
    //     0xd7c22c: add             x0, x0, HEAP, lsl #32
    // 0xd7c230: LoadField: r3 = r1->field_13
    //     0xd7c230: ldur            w3, [x1, #0x13]
    // 0xd7c234: DecompressPointer r3
    //     0xd7c234: add             x3, x3, HEAP, lsl #32
    // 0xd7c238: r4 = LoadClassIdInstr(r0)
    //     0xd7c238: ldur            x4, [x0, #-1]
    //     0xd7c23c: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c240: stp             x3, x0, [SP]
    // 0xd7c244: mov             x0, x4
    // 0xd7c248: mov             lr, x0
    // 0xd7c24c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c250: blr             lr
    // 0xd7c254: tbnz            w0, #4, #0xd7c334
    // 0xd7c258: ldr             x2, [fp, #0x18]
    // 0xd7c25c: ldr             x1, [fp, #0x10]
    // 0xd7c260: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xd7c260: ldur            w0, [x2, #0x17]
    // 0xd7c264: DecompressPointer r0
    //     0xd7c264: add             x0, x0, HEAP, lsl #32
    // 0xd7c268: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xd7c268: ldur            w3, [x1, #0x17]
    // 0xd7c26c: DecompressPointer r3
    //     0xd7c26c: add             x3, x3, HEAP, lsl #32
    // 0xd7c270: r4 = LoadClassIdInstr(r0)
    //     0xd7c270: ldur            x4, [x0, #-1]
    //     0xd7c274: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c278: stp             x3, x0, [SP]
    // 0xd7c27c: mov             x0, x4
    // 0xd7c280: mov             lr, x0
    // 0xd7c284: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c288: blr             lr
    // 0xd7c28c: tbnz            w0, #4, #0xd7c334
    // 0xd7c290: ldr             x2, [fp, #0x18]
    // 0xd7c294: ldr             x1, [fp, #0x10]
    // 0xd7c298: LoadField: r0 = r2->field_1b
    //     0xd7c298: ldur            w0, [x2, #0x1b]
    // 0xd7c29c: DecompressPointer r0
    //     0xd7c29c: add             x0, x0, HEAP, lsl #32
    // 0xd7c2a0: LoadField: r3 = r1->field_1b
    //     0xd7c2a0: ldur            w3, [x1, #0x1b]
    // 0xd7c2a4: DecompressPointer r3
    //     0xd7c2a4: add             x3, x3, HEAP, lsl #32
    // 0xd7c2a8: r4 = LoadClassIdInstr(r0)
    //     0xd7c2a8: ldur            x4, [x0, #-1]
    //     0xd7c2ac: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c2b0: stp             x3, x0, [SP]
    // 0xd7c2b4: mov             x0, x4
    // 0xd7c2b8: mov             lr, x0
    // 0xd7c2bc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c2c0: blr             lr
    // 0xd7c2c4: tbnz            w0, #4, #0xd7c334
    // 0xd7c2c8: ldr             x2, [fp, #0x18]
    // 0xd7c2cc: ldr             x1, [fp, #0x10]
    // 0xd7c2d0: LoadField: r0 = r2->field_1f
    //     0xd7c2d0: ldur            w0, [x2, #0x1f]
    // 0xd7c2d4: DecompressPointer r0
    //     0xd7c2d4: add             x0, x0, HEAP, lsl #32
    // 0xd7c2d8: LoadField: r3 = r1->field_1f
    //     0xd7c2d8: ldur            w3, [x1, #0x1f]
    // 0xd7c2dc: DecompressPointer r3
    //     0xd7c2dc: add             x3, x3, HEAP, lsl #32
    // 0xd7c2e0: r4 = LoadClassIdInstr(r0)
    //     0xd7c2e0: ldur            x4, [x0, #-1]
    //     0xd7c2e4: ubfx            x4, x4, #0xc, #0x14
    // 0xd7c2e8: stp             x3, x0, [SP]
    // 0xd7c2ec: mov             x0, x4
    // 0xd7c2f0: mov             lr, x0
    // 0xd7c2f4: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c2f8: blr             lr
    // 0xd7c2fc: tbnz            w0, #4, #0xd7c334
    // 0xd7c300: ldr             x1, [fp, #0x18]
    // 0xd7c304: ldr             x0, [fp, #0x10]
    // 0xd7c308: LoadField: r2 = r1->field_23
    //     0xd7c308: ldur            w2, [x1, #0x23]
    // 0xd7c30c: DecompressPointer r2
    //     0xd7c30c: add             x2, x2, HEAP, lsl #32
    // 0xd7c310: LoadField: r1 = r0->field_23
    //     0xd7c310: ldur            w1, [x0, #0x23]
    // 0xd7c314: DecompressPointer r1
    //     0xd7c314: add             x1, x1, HEAP, lsl #32
    // 0xd7c318: r0 = LoadClassIdInstr(r2)
    //     0xd7c318: ldur            x0, [x2, #-1]
    //     0xd7c31c: ubfx            x0, x0, #0xc, #0x14
    // 0xd7c320: stp             x1, x2, [SP]
    // 0xd7c324: mov             lr, x0
    // 0xd7c328: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c32c: blr             lr
    // 0xd7c330: b               #0xd7c338
    // 0xd7c334: r0 = false
    //     0xd7c334: add             x0, NULL, #0x30  ; false
    // 0xd7c338: LeaveFrame
    //     0xd7c338: mov             SP, fp
    //     0xd7c33c: ldp             fp, lr, [SP], #0x10
    // 0xd7c340: ret
    //     0xd7c340: ret             
    // 0xd7c344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7c344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7c348: b               #0xd7c114
  }
}
