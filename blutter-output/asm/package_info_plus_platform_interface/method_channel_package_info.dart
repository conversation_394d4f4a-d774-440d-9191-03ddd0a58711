// lib: , url: package:package_info_plus_platform_interface/method_channel_package_info.dart

// class id: 1050749, size: 0x8
class :: {
}

// class id: 5877, size: 0x8, field offset: 0x8
class MethodChannelPackageInfo extends PackageInfoPlatform {

  _ getAll(/* No info */) async {
    // ** addr: 0x91eec4, size: 0x4a0
    // 0x91eec4: EnterFrame
    //     0x91eec4: stp             fp, lr, [SP, #-0x10]!
    //     0x91eec8: mov             fp, SP
    // 0x91eecc: AllocStack(0x60)
    //     0x91eecc: sub             SP, SP, #0x60
    // 0x91eed0: SetupParameters(MethodChannelPackageInfo this /* r1 => r1, fp-0x10 */)
    //     0x91eed0: stur            NULL, [fp, #-8]
    //     0x91eed4: stur            x1, [fp, #-0x10]
    // 0x91eed8: CheckStackOverflow
    //     0x91eed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91eedc: cmp             SP, x16
    //     0x91eee0: b.ls            #0x91f358
    // 0x91eee4: InitAsync() -> Future<PackageInfoData>
    //     0x91eee4: add             x0, PP, #0xf, lsl #12  ; [pp+0xfe50] TypeArguments: <PackageInfoData>
    //     0x91eee8: ldr             x0, [x0, #0xe50]
    //     0x91eeec: bl              #0x661298  ; InitAsyncStub
    // 0x91eef0: r16 = <String, dynamic>
    //     0x91eef0: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x91eef4: r30 = Instance_MethodChannel
    //     0x91eef4: add             lr, PP, #0xf, lsl #12  ; [pp+0xfe58] Obj!MethodChannel@e11271
    //     0x91eef8: ldr             lr, [lr, #0xe58]
    // 0x91eefc: stp             lr, x16, [SP, #8]
    // 0x91ef00: r16 = "getAll"
    //     0x91ef00: add             x16, PP, #0xf, lsl #12  ; [pp+0xfe60] "getAll"
    //     0x91ef04: ldr             x16, [x16, #0xe60]
    // 0x91ef08: str             x16, [SP]
    // 0x91ef0c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x91ef0c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x91ef10: r0 = invokeMapMethod()
    //     0x91ef10: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0x91ef14: mov             x1, x0
    // 0x91ef18: stur            x1, [fp, #-0x18]
    // 0x91ef1c: r0 = Await()
    //     0x91ef1c: bl              #0x661044  ; AwaitStub
    // 0x91ef20: mov             x3, x0
    // 0x91ef24: stur            x3, [fp, #-0x18]
    // 0x91ef28: cmp             w3, NULL
    // 0x91ef2c: b.ne            #0x91ef38
    // 0x91ef30: r4 = Null
    //     0x91ef30: mov             x4, NULL
    // 0x91ef34: b               #0x91ef60
    // 0x91ef38: r0 = LoadClassIdInstr(r3)
    //     0x91ef38: ldur            x0, [x3, #-1]
    //     0x91ef3c: ubfx            x0, x0, #0xc, #0x14
    // 0x91ef40: mov             x1, x3
    // 0x91ef44: r2 = "installTime"
    //     0x91ef44: add             x2, PP, #0xf, lsl #12  ; [pp+0xfe68] "installTime"
    //     0x91ef48: ldr             x2, [x2, #0xe68]
    // 0x91ef4c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91ef4c: sub             lr, x0, #0x114
    //     0x91ef50: ldr             lr, [x21, lr, lsl #3]
    //     0x91ef54: blr             lr
    // 0x91ef58: mov             x4, x0
    // 0x91ef5c: ldur            x3, [fp, #-0x18]
    // 0x91ef60: mov             x0, x4
    // 0x91ef64: stur            x4, [fp, #-0x20]
    // 0x91ef68: r2 = Null
    //     0x91ef68: mov             x2, NULL
    // 0x91ef6c: r1 = Null
    //     0x91ef6c: mov             x1, NULL
    // 0x91ef70: r4 = 60
    //     0x91ef70: movz            x4, #0x3c
    // 0x91ef74: branchIfSmi(r0, 0x91ef80)
    //     0x91ef74: tbz             w0, #0, #0x91ef80
    // 0x91ef78: r4 = LoadClassIdInstr(r0)
    //     0x91ef78: ldur            x4, [x0, #-1]
    //     0x91ef7c: ubfx            x4, x4, #0xc, #0x14
    // 0x91ef80: sub             x4, x4, #0x5e
    // 0x91ef84: cmp             x4, #1
    // 0x91ef88: b.ls            #0x91ef9c
    // 0x91ef8c: r8 = String?
    //     0x91ef8c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91ef90: r3 = Null
    //     0x91ef90: add             x3, PP, #0xf, lsl #12  ; [pp+0xfe70] Null
    //     0x91ef94: ldr             x3, [x3, #0xe70]
    // 0x91ef98: r0 = String?()
    //     0x91ef98: bl              #0x600324  ; IsType_String?_Stub
    // 0x91ef9c: ldur            x1, [fp, #-0x10]
    // 0x91efa0: ldur            x2, [fp, #-0x20]
    // 0x91efa4: r0 = _parseNullableStringMillis()
    //     0x91efa4: bl              #0x91f370  ; [package:package_info_plus_platform_interface/method_channel_package_info.dart] MethodChannelPackageInfo::_parseNullableStringMillis
    // 0x91efa8: mov             x4, x0
    // 0x91efac: ldur            x3, [fp, #-0x18]
    // 0x91efb0: stur            x4, [fp, #-0x20]
    // 0x91efb4: cmp             w3, NULL
    // 0x91efb8: b.ne            #0x91efc4
    // 0x91efbc: r4 = Null
    //     0x91efbc: mov             x4, NULL
    // 0x91efc0: b               #0x91efec
    // 0x91efc4: r0 = LoadClassIdInstr(r3)
    //     0x91efc4: ldur            x0, [x3, #-1]
    //     0x91efc8: ubfx            x0, x0, #0xc, #0x14
    // 0x91efcc: mov             x1, x3
    // 0x91efd0: r2 = "updateTime"
    //     0x91efd0: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf48] "updateTime"
    //     0x91efd4: ldr             x2, [x2, #0xf48]
    // 0x91efd8: r0 = GDT[cid_x0 + -0x114]()
    //     0x91efd8: sub             lr, x0, #0x114
    //     0x91efdc: ldr             lr, [x21, lr, lsl #3]
    //     0x91efe0: blr             lr
    // 0x91efe4: mov             x4, x0
    // 0x91efe8: ldur            x3, [fp, #-0x18]
    // 0x91efec: mov             x0, x4
    // 0x91eff0: stur            x4, [fp, #-0x28]
    // 0x91eff4: r2 = Null
    //     0x91eff4: mov             x2, NULL
    // 0x91eff8: r1 = Null
    //     0x91eff8: mov             x1, NULL
    // 0x91effc: r4 = 60
    //     0x91effc: movz            x4, #0x3c
    // 0x91f000: branchIfSmi(r0, 0x91f00c)
    //     0x91f000: tbz             w0, #0, #0x91f00c
    // 0x91f004: r4 = LoadClassIdInstr(r0)
    //     0x91f004: ldur            x4, [x0, #-1]
    //     0x91f008: ubfx            x4, x4, #0xc, #0x14
    // 0x91f00c: sub             x4, x4, #0x5e
    // 0x91f010: cmp             x4, #1
    // 0x91f014: b.ls            #0x91f028
    // 0x91f018: r8 = String?
    //     0x91f018: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91f01c: r3 = Null
    //     0x91f01c: add             x3, PP, #0xf, lsl #12  ; [pp+0xfe80] Null
    //     0x91f020: ldr             x3, [x3, #0xe80]
    // 0x91f024: r0 = String?()
    //     0x91f024: bl              #0x600324  ; IsType_String?_Stub
    // 0x91f028: ldur            x1, [fp, #-0x10]
    // 0x91f02c: ldur            x2, [fp, #-0x28]
    // 0x91f030: r0 = _parseNullableStringMillis()
    //     0x91f030: bl              #0x91f370  ; [package:package_info_plus_platform_interface/method_channel_package_info.dart] MethodChannelPackageInfo::_parseNullableStringMillis
    // 0x91f034: mov             x4, x0
    // 0x91f038: ldur            x3, [fp, #-0x18]
    // 0x91f03c: stur            x4, [fp, #-0x10]
    // 0x91f040: cmp             w3, NULL
    // 0x91f044: b.eq            #0x91f360
    // 0x91f048: r0 = LoadClassIdInstr(r3)
    //     0x91f048: ldur            x0, [x3, #-1]
    //     0x91f04c: ubfx            x0, x0, #0xc, #0x14
    // 0x91f050: mov             x1, x3
    // 0x91f054: r2 = "appName"
    //     0x91f054: add             x2, PP, #0xf, lsl #12  ; [pp+0xf9c0] "appName"
    //     0x91f058: ldr             x2, [x2, #0x9c0]
    // 0x91f05c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f05c: sub             lr, x0, #0x114
    //     0x91f060: ldr             lr, [x21, lr, lsl #3]
    //     0x91f064: blr             lr
    // 0x91f068: cmp             w0, NULL
    // 0x91f06c: b.ne            #0x91f078
    // 0x91f070: r4 = ""
    //     0x91f070: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91f074: b               #0x91f07c
    // 0x91f078: mov             x4, x0
    // 0x91f07c: ldur            x3, [fp, #-0x18]
    // 0x91f080: mov             x0, x4
    // 0x91f084: stur            x4, [fp, #-0x28]
    // 0x91f088: r2 = Null
    //     0x91f088: mov             x2, NULL
    // 0x91f08c: r1 = Null
    //     0x91f08c: mov             x1, NULL
    // 0x91f090: r4 = 60
    //     0x91f090: movz            x4, #0x3c
    // 0x91f094: branchIfSmi(r0, 0x91f0a0)
    //     0x91f094: tbz             w0, #0, #0x91f0a0
    // 0x91f098: r4 = LoadClassIdInstr(r0)
    //     0x91f098: ldur            x4, [x0, #-1]
    //     0x91f09c: ubfx            x4, x4, #0xc, #0x14
    // 0x91f0a0: sub             x4, x4, #0x5e
    // 0x91f0a4: cmp             x4, #1
    // 0x91f0a8: b.ls            #0x91f0bc
    // 0x91f0ac: r8 = String
    //     0x91f0ac: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f0b0: r3 = Null
    //     0x91f0b0: add             x3, PP, #0xf, lsl #12  ; [pp+0xfe90] Null
    //     0x91f0b4: ldr             x3, [x3, #0xe90]
    // 0x91f0b8: r0 = String()
    //     0x91f0b8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f0bc: ldur            x3, [fp, #-0x18]
    // 0x91f0c0: r0 = LoadClassIdInstr(r3)
    //     0x91f0c0: ldur            x0, [x3, #-1]
    //     0x91f0c4: ubfx            x0, x0, #0xc, #0x14
    // 0x91f0c8: mov             x1, x3
    // 0x91f0cc: r2 = "packageName"
    //     0x91f0cc: add             x2, PP, #0xf, lsl #12  ; [pp+0xfea0] "packageName"
    //     0x91f0d0: ldr             x2, [x2, #0xea0]
    // 0x91f0d4: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f0d4: sub             lr, x0, #0x114
    //     0x91f0d8: ldr             lr, [x21, lr, lsl #3]
    //     0x91f0dc: blr             lr
    // 0x91f0e0: cmp             w0, NULL
    // 0x91f0e4: b.ne            #0x91f0f0
    // 0x91f0e8: r4 = ""
    //     0x91f0e8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91f0ec: b               #0x91f0f4
    // 0x91f0f0: mov             x4, x0
    // 0x91f0f4: ldur            x3, [fp, #-0x18]
    // 0x91f0f8: mov             x0, x4
    // 0x91f0fc: stur            x4, [fp, #-0x30]
    // 0x91f100: r2 = Null
    //     0x91f100: mov             x2, NULL
    // 0x91f104: r1 = Null
    //     0x91f104: mov             x1, NULL
    // 0x91f108: r4 = 60
    //     0x91f108: movz            x4, #0x3c
    // 0x91f10c: branchIfSmi(r0, 0x91f118)
    //     0x91f10c: tbz             w0, #0, #0x91f118
    // 0x91f110: r4 = LoadClassIdInstr(r0)
    //     0x91f110: ldur            x4, [x0, #-1]
    //     0x91f114: ubfx            x4, x4, #0xc, #0x14
    // 0x91f118: sub             x4, x4, #0x5e
    // 0x91f11c: cmp             x4, #1
    // 0x91f120: b.ls            #0x91f134
    // 0x91f124: r8 = String
    //     0x91f124: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f128: r3 = Null
    //     0x91f128: add             x3, PP, #0xf, lsl #12  ; [pp+0xfea8] Null
    //     0x91f12c: ldr             x3, [x3, #0xea8]
    // 0x91f130: r0 = String()
    //     0x91f130: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f134: ldur            x3, [fp, #-0x18]
    // 0x91f138: r0 = LoadClassIdInstr(r3)
    //     0x91f138: ldur            x0, [x3, #-1]
    //     0x91f13c: ubfx            x0, x0, #0xc, #0x14
    // 0x91f140: mov             x1, x3
    // 0x91f144: r2 = "version"
    //     0x91f144: add             x2, PP, #0xf, lsl #12  ; [pp+0xfeb8] "version"
    //     0x91f148: ldr             x2, [x2, #0xeb8]
    // 0x91f14c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f14c: sub             lr, x0, #0x114
    //     0x91f150: ldr             lr, [x21, lr, lsl #3]
    //     0x91f154: blr             lr
    // 0x91f158: cmp             w0, NULL
    // 0x91f15c: b.ne            #0x91f168
    // 0x91f160: r4 = ""
    //     0x91f160: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91f164: b               #0x91f16c
    // 0x91f168: mov             x4, x0
    // 0x91f16c: ldur            x3, [fp, #-0x18]
    // 0x91f170: mov             x0, x4
    // 0x91f174: stur            x4, [fp, #-0x38]
    // 0x91f178: r2 = Null
    //     0x91f178: mov             x2, NULL
    // 0x91f17c: r1 = Null
    //     0x91f17c: mov             x1, NULL
    // 0x91f180: r4 = 60
    //     0x91f180: movz            x4, #0x3c
    // 0x91f184: branchIfSmi(r0, 0x91f190)
    //     0x91f184: tbz             w0, #0, #0x91f190
    // 0x91f188: r4 = LoadClassIdInstr(r0)
    //     0x91f188: ldur            x4, [x0, #-1]
    //     0x91f18c: ubfx            x4, x4, #0xc, #0x14
    // 0x91f190: sub             x4, x4, #0x5e
    // 0x91f194: cmp             x4, #1
    // 0x91f198: b.ls            #0x91f1ac
    // 0x91f19c: r8 = String
    //     0x91f19c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f1a0: r3 = Null
    //     0x91f1a0: add             x3, PP, #0xf, lsl #12  ; [pp+0xfec0] Null
    //     0x91f1a4: ldr             x3, [x3, #0xec0]
    // 0x91f1a8: r0 = String()
    //     0x91f1a8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f1ac: ldur            x3, [fp, #-0x18]
    // 0x91f1b0: r0 = LoadClassIdInstr(r3)
    //     0x91f1b0: ldur            x0, [x3, #-1]
    //     0x91f1b4: ubfx            x0, x0, #0xc, #0x14
    // 0x91f1b8: mov             x1, x3
    // 0x91f1bc: r2 = "buildNumber"
    //     0x91f1bc: add             x2, PP, #0xf, lsl #12  ; [pp+0xfed0] "buildNumber"
    //     0x91f1c0: ldr             x2, [x2, #0xed0]
    // 0x91f1c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f1c4: sub             lr, x0, #0x114
    //     0x91f1c8: ldr             lr, [x21, lr, lsl #3]
    //     0x91f1cc: blr             lr
    // 0x91f1d0: cmp             w0, NULL
    // 0x91f1d4: b.ne            #0x91f1e0
    // 0x91f1d8: r4 = ""
    //     0x91f1d8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91f1dc: b               #0x91f1e4
    // 0x91f1e0: mov             x4, x0
    // 0x91f1e4: ldur            x3, [fp, #-0x18]
    // 0x91f1e8: mov             x0, x4
    // 0x91f1ec: stur            x4, [fp, #-0x40]
    // 0x91f1f0: r2 = Null
    //     0x91f1f0: mov             x2, NULL
    // 0x91f1f4: r1 = Null
    //     0x91f1f4: mov             x1, NULL
    // 0x91f1f8: r4 = 60
    //     0x91f1f8: movz            x4, #0x3c
    // 0x91f1fc: branchIfSmi(r0, 0x91f208)
    //     0x91f1fc: tbz             w0, #0, #0x91f208
    // 0x91f200: r4 = LoadClassIdInstr(r0)
    //     0x91f200: ldur            x4, [x0, #-1]
    //     0x91f204: ubfx            x4, x4, #0xc, #0x14
    // 0x91f208: sub             x4, x4, #0x5e
    // 0x91f20c: cmp             x4, #1
    // 0x91f210: b.ls            #0x91f224
    // 0x91f214: r8 = String
    //     0x91f214: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f218: r3 = Null
    //     0x91f218: add             x3, PP, #0xf, lsl #12  ; [pp+0xfed8] Null
    //     0x91f21c: ldr             x3, [x3, #0xed8]
    // 0x91f220: r0 = String()
    //     0x91f220: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f224: ldur            x3, [fp, #-0x18]
    // 0x91f228: r0 = LoadClassIdInstr(r3)
    //     0x91f228: ldur            x0, [x3, #-1]
    //     0x91f22c: ubfx            x0, x0, #0xc, #0x14
    // 0x91f230: mov             x1, x3
    // 0x91f234: r2 = "buildSignature"
    //     0x91f234: add             x2, PP, #0xf, lsl #12  ; [pp+0xfee8] "buildSignature"
    //     0x91f238: ldr             x2, [x2, #0xee8]
    // 0x91f23c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f23c: sub             lr, x0, #0x114
    //     0x91f240: ldr             lr, [x21, lr, lsl #3]
    //     0x91f244: blr             lr
    // 0x91f248: cmp             w0, NULL
    // 0x91f24c: b.ne            #0x91f258
    // 0x91f250: r10 = ""
    //     0x91f250: ldr             x10, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91f254: b               #0x91f25c
    // 0x91f258: mov             x10, x0
    // 0x91f25c: ldur            x3, [fp, #-0x18]
    // 0x91f260: ldur            x9, [fp, #-0x20]
    // 0x91f264: ldur            x8, [fp, #-0x10]
    // 0x91f268: ldur            x7, [fp, #-0x28]
    // 0x91f26c: ldur            x6, [fp, #-0x30]
    // 0x91f270: ldur            x5, [fp, #-0x38]
    // 0x91f274: ldur            x4, [fp, #-0x40]
    // 0x91f278: mov             x0, x10
    // 0x91f27c: stur            x10, [fp, #-0x48]
    // 0x91f280: r2 = Null
    //     0x91f280: mov             x2, NULL
    // 0x91f284: r1 = Null
    //     0x91f284: mov             x1, NULL
    // 0x91f288: r4 = 60
    //     0x91f288: movz            x4, #0x3c
    // 0x91f28c: branchIfSmi(r0, 0x91f298)
    //     0x91f28c: tbz             w0, #0, #0x91f298
    // 0x91f290: r4 = LoadClassIdInstr(r0)
    //     0x91f290: ldur            x4, [x0, #-1]
    //     0x91f294: ubfx            x4, x4, #0xc, #0x14
    // 0x91f298: sub             x4, x4, #0x5e
    // 0x91f29c: cmp             x4, #1
    // 0x91f2a0: b.ls            #0x91f2b4
    // 0x91f2a4: r8 = String
    //     0x91f2a4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f2a8: r3 = Null
    //     0x91f2a8: add             x3, PP, #0xf, lsl #12  ; [pp+0xfef0] Null
    //     0x91f2ac: ldr             x3, [x3, #0xef0]
    // 0x91f2b0: r0 = String()
    //     0x91f2b0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f2b4: ldur            x1, [fp, #-0x18]
    // 0x91f2b8: r0 = LoadClassIdInstr(r1)
    //     0x91f2b8: ldur            x0, [x1, #-1]
    //     0x91f2bc: ubfx            x0, x0, #0xc, #0x14
    // 0x91f2c0: r2 = "installerStore"
    //     0x91f2c0: add             x2, PP, #0xf, lsl #12  ; [pp+0xff00] "installerStore"
    //     0x91f2c4: ldr             x2, [x2, #0xf00]
    // 0x91f2c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x91f2c8: sub             lr, x0, #0x114
    //     0x91f2cc: ldr             lr, [x21, lr, lsl #3]
    //     0x91f2d0: blr             lr
    // 0x91f2d4: mov             x3, x0
    // 0x91f2d8: r2 = Null
    //     0x91f2d8: mov             x2, NULL
    // 0x91f2dc: r1 = Null
    //     0x91f2dc: mov             x1, NULL
    // 0x91f2e0: stur            x3, [fp, #-0x18]
    // 0x91f2e4: r4 = 60
    //     0x91f2e4: movz            x4, #0x3c
    // 0x91f2e8: branchIfSmi(r0, 0x91f2f4)
    //     0x91f2e8: tbz             w0, #0, #0x91f2f4
    // 0x91f2ec: r4 = LoadClassIdInstr(r0)
    //     0x91f2ec: ldur            x4, [x0, #-1]
    //     0x91f2f0: ubfx            x4, x4, #0xc, #0x14
    // 0x91f2f4: sub             x4, x4, #0x5e
    // 0x91f2f8: cmp             x4, #1
    // 0x91f2fc: b.ls            #0x91f310
    // 0x91f300: r8 = String?
    //     0x91f300: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91f304: r3 = Null
    //     0x91f304: add             x3, PP, #0xf, lsl #12  ; [pp+0xff08] Null
    //     0x91f308: ldr             x3, [x3, #0xf08]
    // 0x91f30c: r0 = String?()
    //     0x91f30c: bl              #0x600324  ; IsType_String?_Stub
    // 0x91f310: r0 = PackageInfoData()
    //     0x91f310: bl              #0x91f364  ; AllocatePackageInfoDataStub -> PackageInfoData (size=0x28)
    // 0x91f314: ldur            x1, [fp, #-0x28]
    // 0x91f318: StoreField: r0->field_7 = r1
    //     0x91f318: stur            w1, [x0, #7]
    // 0x91f31c: ldur            x1, [fp, #-0x30]
    // 0x91f320: StoreField: r0->field_b = r1
    //     0x91f320: stur            w1, [x0, #0xb]
    // 0x91f324: ldur            x1, [fp, #-0x38]
    // 0x91f328: StoreField: r0->field_f = r1
    //     0x91f328: stur            w1, [x0, #0xf]
    // 0x91f32c: ldur            x1, [fp, #-0x40]
    // 0x91f330: StoreField: r0->field_13 = r1
    //     0x91f330: stur            w1, [x0, #0x13]
    // 0x91f334: ldur            x1, [fp, #-0x48]
    // 0x91f338: ArrayStore: r0[0] = r1  ; List_4
    //     0x91f338: stur            w1, [x0, #0x17]
    // 0x91f33c: ldur            x1, [fp, #-0x18]
    // 0x91f340: StoreField: r0->field_1b = r1
    //     0x91f340: stur            w1, [x0, #0x1b]
    // 0x91f344: ldur            x1, [fp, #-0x20]
    // 0x91f348: StoreField: r0->field_1f = r1
    //     0x91f348: stur            w1, [x0, #0x1f]
    // 0x91f34c: ldur            x1, [fp, #-0x10]
    // 0x91f350: StoreField: r0->field_23 = r1
    //     0x91f350: stur            w1, [x0, #0x23]
    // 0x91f354: r0 = ReturnAsyncNotFuture()
    //     0x91f354: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91f358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91f358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91f35c: b               #0x91eee4
    // 0x91f360: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91f360: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _parseNullableStringMillis(/* No info */) {
    // ** addr: 0x91f370, size: 0x94
    // 0x91f370: EnterFrame
    //     0x91f370: stp             fp, lr, [SP, #-0x10]!
    //     0x91f374: mov             fp, SP
    // 0x91f378: AllocStack(0x10)
    //     0x91f378: sub             SP, SP, #0x10
    // 0x91f37c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x91f37c: mov             x0, x2
    //     0x91f380: stur            x2, [fp, #-8]
    // 0x91f384: CheckStackOverflow
    //     0x91f384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91f388: cmp             SP, x16
    //     0x91f38c: b.ls            #0x91f3fc
    // 0x91f390: cmp             w0, NULL
    // 0x91f394: b.eq            #0x91f3ec
    // 0x91f398: mov             x1, x0
    // 0x91f39c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91f39c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91f3a0: r0 = tryParse()
    //     0x91f3a0: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0x91f3a4: cmp             w0, NULL
    // 0x91f3a8: b.eq            #0x91f3ec
    // 0x91f3ac: ldur            x1, [fp, #-8]
    // 0x91f3b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91f3b0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91f3b4: r0 = parse()
    //     0x91f3b4: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x91f3b8: mov             x1, x0
    // 0x91f3bc: r0 = _validateMilliseconds()
    //     0x91f3bc: bl              #0x7ef2d0  ; [dart:core] DateTime::_validateMilliseconds
    // 0x91f3c0: r16 = 1000
    //     0x91f3c0: movz            x16, #0x3e8
    // 0x91f3c4: mul             x2, x0, x16
    // 0x91f3c8: stur            x2, [fp, #-0x10]
    // 0x91f3cc: r0 = DateTime()
    //     0x91f3cc: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x91f3d0: mov             x1, x0
    // 0x91f3d4: ldur            x2, [fp, #-0x10]
    // 0x91f3d8: r3 = false
    //     0x91f3d8: add             x3, NULL, #0x30  ; false
    // 0x91f3dc: stur            x0, [fp, #-8]
    // 0x91f3e0: r0 = DateTime._withValue()
    //     0x91f3e0: bl              #0x6fe64c  ; [dart:core] DateTime::DateTime._withValue
    // 0x91f3e4: ldur            x0, [fp, #-8]
    // 0x91f3e8: b               #0x91f3f0
    // 0x91f3ec: r0 = Null
    //     0x91f3ec: mov             x0, NULL
    // 0x91f3f0: LeaveFrame
    //     0x91f3f0: mov             SP, fp
    //     0x91f3f4: ldp             fp, lr, [SP], #0x10
    // 0x91f3f8: ret
    //     0x91f3f8: ret             
    // 0x91f3fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91f3fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91f400: b               #0x91f390
  }
}
