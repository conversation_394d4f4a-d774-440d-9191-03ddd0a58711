// lib: , url: package:package_info_plus_platform_interface/package_info_platform_interface.dart

// class id: 1050751, size: 0x8
class :: {
}

// class id: 5876, size: 0x8, field offset: 0x8
abstract class PackageInfoPlatform extends PlatformInterface {

  static late PackageInfoPlatform _instance; // offset: 0x16c0
  static late final Object _token; // offset: 0x16bc

  static PackageInfoPlatform _instance() {
    // ** addr: 0x91f404, size: 0x8c
    // 0x91f404: EnterFrame
    //     0x91f404: stp             fp, lr, [SP, #-0x10]!
    //     0x91f408: mov             fp, SP
    // 0x91f40c: AllocStack(0x10)
    //     0x91f40c: sub             SP, SP, #0x10
    // 0x91f410: CheckStackOverflow
    //     0x91f410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91f414: cmp             SP, x16
    //     0x91f418: b.ls            #0x91f488
    // 0x91f41c: r0 = InitLateStaticField(0x16bc) // [package:package_info_plus_platform_interface/package_info_platform_interface.dart] PackageInfoPlatform::_token
    //     0x91f41c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91f420: ldr             x0, [x0, #0x2d78]
    //     0x91f424: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91f428: cmp             w0, w16
    //     0x91f42c: b.ne            #0x91f43c
    //     0x91f430: add             x2, PP, #0xf, lsl #12  ; [pp+0xff18] Field <PackageInfoPlatform._token@2551110083>: static late final (offset: 0x16bc)
    //     0x91f434: ldr             x2, [x2, #0xf18]
    //     0x91f438: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91f43c: stur            x0, [fp, #-8]
    // 0x91f440: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x91f440: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91f444: ldr             x0, [x0, #0xc08]
    //     0x91f448: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91f44c: cmp             w0, w16
    //     0x91f450: b.ne            #0x91f45c
    //     0x91f454: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x91f458: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91f45c: stur            x0, [fp, #-0x10]
    // 0x91f460: r0 = MethodChannelPackageInfo()
    //     0x91f460: bl              #0x91f490  ; AllocateMethodChannelPackageInfoStub -> MethodChannelPackageInfo (size=0x8)
    // 0x91f464: ldur            x1, [fp, #-0x10]
    // 0x91f468: mov             x2, x0
    // 0x91f46c: ldur            x3, [fp, #-8]
    // 0x91f470: stur            x0, [fp, #-8]
    // 0x91f474: r0 = []=()
    //     0x91f474: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x91f478: ldur            x0, [fp, #-8]
    // 0x91f47c: LeaveFrame
    //     0x91f47c: mov             SP, fp
    //     0x91f480: ldp             fp, lr, [SP], #0x10
    // 0x91f484: ret
    //     0x91f484: ret             
    // 0x91f488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91f488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91f48c: b               #0x91f41c
  }
}
