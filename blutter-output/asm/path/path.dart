// lib: , url: package:path/path.dart

// class id: 1050752, size: 0x8
class :: {

  static late final Context context; // offset: 0xc24

  static _ join(/* No info */) {
    // ** addr: 0x831be8, size: 0x6c
    // 0x831be8: EnterFrame
    //     0x831be8: stp             fp, lr, [SP, #-0x10]!
    //     0x831bec: mov             fp, SP
    // 0x831bf0: AllocStack(0x10)
    //     0x831bf0: sub             SP, SP, #0x10
    // 0x831bf4: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x831bf4: mov             x3, x2
    //     0x831bf8: stur            x2, [fp, #-0x10]
    //     0x831bfc: mov             x2, x1
    //     0x831c00: stur            x1, [fp, #-8]
    // 0x831c04: CheckStackOverflow
    //     0x831c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x831c08: cmp             SP, x16
    //     0x831c0c: b.ls            #0x831c4c
    // 0x831c10: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0x831c10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x831c14: ldr             x0, [x0, #0x1848]
    //     0x831c18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x831c1c: cmp             w0, w16
    //     0x831c20: b.ne            #0x831c30
    //     0x831c24: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0x831c28: ldr             x2, [x2, #0xbf0]
    //     0x831c2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x831c30: mov             x1, x0
    // 0x831c34: ldur            x2, [fp, #-8]
    // 0x831c38: ldur            x3, [fp, #-0x10]
    // 0x831c3c: r0 = join()
    //     0x831c3c: bl              #0x831c54  ; [package:path/src/context.dart] Context::join
    // 0x831c40: LeaveFrame
    //     0x831c40: mov             SP, fp
    //     0x831c44: ldp             fp, lr, [SP], #0x10
    // 0x831c48: ret
    //     0x831c48: ret             
    // 0x831c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x831c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x831c50: b               #0x831c10
  }
  static Context context() {
    // ** addr: 0x833460, size: 0x2c
    // 0x833460: EnterFrame
    //     0x833460: stp             fp, lr, [SP, #-0x10]!
    //     0x833464: mov             fp, SP
    // 0x833468: CheckStackOverflow
    //     0x833468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83346c: cmp             SP, x16
    //     0x833470: b.ls            #0x833484
    // 0x833474: r0 = createInternal()
    //     0x833474: bl              #0x83348c  ; [package:path/src/context.dart] ::createInternal
    // 0x833478: LeaveFrame
    //     0x833478: mov             SP, fp
    //     0x83347c: ldp             fp, lr, [SP], #0x10
    // 0x833480: ret
    //     0x833480: ret             
    // 0x833484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833488: b               #0x833474
  }
  String current() {
    // ** addr: 0xaaa248, size: 0x2a8
    // 0xaaa248: EnterFrame
    //     0xaaa248: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa24c: mov             fp, SP
    // 0xaaa250: AllocStack(0x60)
    //     0xaaa250: sub             SP, SP, #0x60
    // 0xaaa254: CheckStackOverflow
    //     0xaaa254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa258: cmp             SP, x16
    //     0xaaa25c: b.ls            #0xaaa4e4
    // 0xaaa260: r0 = InitLateStaticField(0x178) // [dart:core] ::_uriBaseClosure
    //     0xaaa260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa264: ldr             x0, [x0, #0x2f0]
    //     0xaaa268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaaa26c: cmp             w0, w16
    //     0xaaa270: b.ne            #0xaaa280
    //     0xaaa274: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc38] Field <::._uriBaseClosure@0150898>: static late (offset: 0x178)
    //     0xaaa278: ldr             x2, [x2, #0xc38]
    //     0xaaa27c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xaaa280: mov             x1, x0
    // 0xaaa284: stur            x1, [fp, #-0x48]
    // 0xaaa288: str             x1, [SP]
    // 0xaaa28c: mov             x0, x1
    // 0xaaa290: ClosureCall
    //     0xaaa290: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xaaa294: ldur            x2, [x0, #0x1f]
    //     0xaaa298: blr             x2
    // 0xaaa29c: mov             x1, x0
    // 0xaaa2a0: stur            x1, [fp, #-0x48]
    // 0xaaa2a4: r0 = LoadStaticField(0xc28)
    //     0xaaa2a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa2a8: ldr             x0, [x0, #0x1850]
    // 0xaaa2ac: r2 = LoadClassIdInstr(r1)
    //     0xaaa2ac: ldur            x2, [x1, #-1]
    //     0xaaa2b0: ubfx            x2, x2, #0xc, #0x14
    // 0xaaa2b4: stp             x0, x1, [SP]
    // 0xaaa2b8: mov             x0, x2
    // 0xaaa2bc: mov             lr, x0
    // 0xaaa2c0: ldr             lr, [x21, lr, lsl #3]
    // 0xaaa2c4: blr             lr
    // 0xaaa2c8: tbnz            w0, #4, #0xaaa2e8
    // 0xaaa2cc: r0 = LoadStaticField(0xc2c)
    //     0xaaa2cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa2d0: ldr             x0, [x0, #0x1858]
    // 0xaaa2d4: cmp             w0, NULL
    // 0xaaa2d8: b.eq            #0xaaa4ec
    // 0xaaa2dc: LeaveFrame
    //     0xaaa2dc: mov             SP, fp
    //     0xaaa2e0: ldp             fp, lr, [SP], #0x10
    // 0xaaa2e4: ret
    //     0xaaa2e4: ret             
    // 0xaaa2e8: ldur            x1, [fp, #-0x48]
    // 0xaaa2ec: StoreStaticField(0xc28, r1)
    //     0xaaa2ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa2f0: str             x1, [x0, #0x1850]
    // 0xaaa2f4: r0 = InitLateStaticField(0x16d0) // [package:path/src/style.dart] Style::platform
    //     0xaaa2f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa2f8: ldr             x0, [x0, #0x2da0]
    //     0xaaa2fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaaa300: cmp             w0, w16
    //     0xaaa304: b.ne            #0xaaa314
    //     0xaaa308: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc30] Field <Style.platform>: static late final (offset: 0x16d0)
    //     0xaaa30c: ldr             x2, [x2, #0xc30]
    //     0xaaa310: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaaa314: stur            x0, [fp, #-0x50]
    // 0xaaa318: r0 = InitLateStaticField(0x16cc) // [package:path/src/style.dart] Style::url
    //     0xaaa318: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa31c: ldr             x0, [x0, #0x2d98]
    //     0xaaa320: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaaa324: cmp             w0, w16
    //     0xaaa328: b.ne            #0xaaa338
    //     0xaaa32c: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc40] Field <Style.url>: static late final (offset: 0x16cc)
    //     0xaaa330: ldr             x2, [x2, #0xc40]
    //     0xaaa334: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaaa338: mov             x1, x0
    // 0xaaa33c: ldur            x0, [fp, #-0x50]
    // 0xaaa340: cmp             w0, w1
    // 0xaaa344: b.ne            #0xaaa394
    // 0xaaa348: ldur            x1, [fp, #-0x48]
    // 0xaaa34c: r0 = LoadClassIdInstr(r1)
    //     0xaaa34c: ldur            x0, [x1, #-1]
    //     0xaaa350: ubfx            x0, x0, #0xc, #0x14
    // 0xaaa354: r2 = "."
    //     0xaaa354: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xaaa358: r0 = GDT[cid_x0 + -0xe4f]()
    //     0xaaa358: sub             lr, x0, #0xe4f
    //     0xaaa35c: ldr             lr, [x21, lr, lsl #3]
    //     0xaaa360: blr             lr
    // 0xaaa364: r1 = LoadClassIdInstr(r0)
    //     0xaaa364: ldur            x1, [x0, #-1]
    //     0xaaa368: ubfx            x1, x1, #0xc, #0x14
    // 0xaaa36c: str             x0, [SP]
    // 0xaaa370: mov             x0, x1
    // 0xaaa374: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xaaa374: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xaaa378: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xaaa378: movz            x17, #0x2b03
    //     0xaaa37c: add             lr, x0, x17
    //     0xaaa380: ldr             lr, [x21, lr, lsl #3]
    //     0xaaa384: blr             lr
    // 0xaaa388: StoreStaticField(0xc2c, r0)
    //     0xaaa388: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa38c: str             x0, [x1, #0x1858]
    // 0xaaa390: b               #0xaaa3e0
    // 0xaaa394: ldur            x1, [fp, #-0x48]
    // 0xaaa398: r0 = LoadClassIdInstr(r1)
    //     0xaaa398: ldur            x0, [x1, #-1]
    //     0xaaa39c: ubfx            x0, x0, #0xc, #0x14
    // 0xaaa3a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaa3a0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaa3a4: r0 = GDT[cid_x0 + 0x11]()
    //     0xaaa3a4: add             lr, x0, #0x11
    //     0xaaa3a8: ldr             lr, [x21, lr, lsl #3]
    //     0xaaa3ac: blr             lr
    // 0xaaa3b0: LoadField: r1 = r0->field_7
    //     0xaaa3b0: ldur            w1, [x0, #7]
    // 0xaaa3b4: r2 = LoadInt32Instr(r1)
    //     0xaaa3b4: sbfx            x2, x1, #1, #0x1f
    // 0xaaa3b8: sub             x1, x2, #1
    // 0xaaa3bc: cbz             x1, #0xaaa3d8
    // 0xaaa3c0: lsl             x2, x1, #1
    // 0xaaa3c4: str             x2, [SP]
    // 0xaaa3c8: mov             x1, x0
    // 0xaaa3cc: r2 = 0
    //     0xaaa3cc: movz            x2, #0
    // 0xaaa3d0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xaaa3d0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xaaa3d4: r0 = substring()
    //     0xaaa3d4: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xaaa3d8: StoreStaticField(0xc2c, r0)
    //     0xaaa3d8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa3dc: str             x0, [x1, #0x1858]
    // 0xaaa3e0: LeaveFrame
    //     0xaaa3e0: mov             SP, fp
    //     0xaaa3e4: ldp             fp, lr, [SP], #0x10
    // 0xaaa3e8: ret
    //     0xaaa3e8: ret             
    // 0xaaa3ec: sub             SP, fp, #0x60
    // 0xaaa3f0: mov             x4, x0
    // 0xaaa3f4: mov             x3, x1
    // 0xaaa3f8: stur            x0, [fp, #-0x48]
    // 0xaaa3fc: stur            x1, [fp, #-0x50]
    // 0xaaa400: r2 = Null
    //     0xaaa400: mov             x2, NULL
    // 0xaaa404: r1 = Null
    //     0xaaa404: mov             x1, NULL
    // 0xaaa408: cmp             w0, NULL
    // 0xaaa40c: b.eq            #0xaaa498
    // 0xaaa410: branchIfSmi(r0, 0xaaa498)
    //     0xaaa410: tbz             w0, #0, #0xaaa498
    // 0xaaa414: r3 = LoadClassIdInstr(r0)
    //     0xaaa414: ldur            x3, [x0, #-1]
    //     0xaaa418: ubfx            x3, x3, #0xc, #0x14
    // 0xaaa41c: r4 = LoadClassIdInstr(r0)
    //     0xaaa41c: ldur            x4, [x0, #-1]
    //     0xaaa420: ubfx            x4, x4, #0xc, #0x14
    // 0xaaa424: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xaaa428: ldr             x3, [x3, #0x18]
    // 0xaaa42c: ldr             x3, [x3, x4, lsl #3]
    // 0xaaa430: LoadField: r3 = r3->field_2b
    //     0xaaa430: ldur            w3, [x3, #0x2b]
    // 0xaaa434: DecompressPointer r3
    //     0xaaa434: add             x3, x3, HEAP, lsl #32
    // 0xaaa438: cmp             w3, NULL
    // 0xaaa43c: b.eq            #0xaaa498
    // 0xaaa440: LoadField: r3 = r3->field_f
    //     0xaaa440: ldur            w3, [x3, #0xf]
    // 0xaaa444: lsr             x3, x3, #3
    // 0xaaa448: r17 = 6724
    //     0xaaa448: movz            x17, #0x1a44
    // 0xaaa44c: cmp             x3, x17
    // 0xaaa450: b.eq            #0xaaa4a0
    // 0xaaa454: r3 = SubtypeTestCache
    //     0xaaa454: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb80] SubtypeTestCache
    //     0xaaa458: ldr             x3, [x3, #0xb80]
    // 0xaaa45c: r30 = Subtype1TestCacheStub
    //     0xaaa45c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xaaa460: LoadField: r30 = r30->field_7
    //     0xaaa460: ldur            lr, [lr, #7]
    // 0xaaa464: blr             lr
    // 0xaaa468: cmp             w7, NULL
    // 0xaaa46c: b.eq            #0xaaa478
    // 0xaaa470: tbnz            w7, #4, #0xaaa498
    // 0xaaa474: b               #0xaaa4a0
    // 0xaaa478: r8 = Exception
    //     0xaaa478: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bb88] Type: Exception
    //     0xaaa47c: ldr             x8, [x8, #0xb88]
    // 0xaaa480: r3 = SubtypeTestCache
    //     0xaaa480: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb90] SubtypeTestCache
    //     0xaaa484: ldr             x3, [x3, #0xb90]
    // 0xaaa488: r30 = InstanceOfStub
    //     0xaaa488: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xaaa48c: LoadField: r30 = r30->field_7
    //     0xaaa48c: ldur            lr, [lr, #7]
    // 0xaaa490: blr             lr
    // 0xaaa494: b               #0xaaa4a4
    // 0xaaa498: r0 = false
    //     0xaaa498: add             x0, NULL, #0x30  ; false
    // 0xaaa49c: b               #0xaaa4a4
    // 0xaaa4a0: r0 = true
    //     0xaaa4a0: add             x0, NULL, #0x20  ; true
    // 0xaaa4a4: tbnz            w0, #4, #0xaaa4d4
    // 0xaaa4a8: r0 = LoadStaticField(0xc2c)
    //     0xaaa4a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa4ac: ldr             x0, [x0, #0x1858]
    // 0xaaa4b0: cmp             w0, NULL
    // 0xaaa4b4: b.eq            #0xaaa4c4
    // 0xaaa4b8: LeaveFrame
    //     0xaaa4b8: mov             SP, fp
    //     0xaaa4bc: ldp             fp, lr, [SP], #0x10
    // 0xaaa4c0: ret
    //     0xaaa4c0: ret             
    // 0xaaa4c4: ldur            x0, [fp, #-0x48]
    // 0xaaa4c8: ldur            x1, [fp, #-0x50]
    // 0xaaa4cc: r0 = ReThrow()
    //     0xaaa4cc: bl              #0xec048c  ; ReThrowStub
    // 0xaaa4d0: brk             #0
    // 0xaaa4d4: ldur            x0, [fp, #-0x48]
    // 0xaaa4d8: ldur            x1, [fp, #-0x50]
    // 0xaaa4dc: r0 = ReThrow()
    //     0xaaa4dc: bl              #0xec048c  ; ReThrowStub
    // 0xaaa4e0: brk             #0
    // 0xaaa4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa4e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa4e8: b               #0xaaa260
    // 0xaaa4ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaaa4ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ absolute(/* No info */) {
    // ** addr: 0xab2484, size: 0x60
    // 0xab2484: EnterFrame
    //     0xab2484: stp             fp, lr, [SP, #-0x10]!
    //     0xab2488: mov             fp, SP
    // 0xab248c: AllocStack(0x8)
    //     0xab248c: sub             SP, SP, #8
    // 0xab2490: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xab2490: mov             x2, x1
    //     0xab2494: stur            x1, [fp, #-8]
    // 0xab2498: CheckStackOverflow
    //     0xab2498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab249c: cmp             SP, x16
    //     0xab24a0: b.ls            #0xab24dc
    // 0xab24a4: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0xab24a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab24a8: ldr             x0, [x0, #0x1848]
    //     0xab24ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab24b0: cmp             w0, w16
    //     0xab24b4: b.ne            #0xab24c4
    //     0xab24b8: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0xab24bc: ldr             x2, [x2, #0xbf0]
    //     0xab24c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab24c4: mov             x1, x0
    // 0xab24c8: ldur            x2, [fp, #-8]
    // 0xab24cc: r0 = absolute()
    //     0xab24cc: bl              #0xab24e4  ; [package:path/src/context.dart] Context::absolute
    // 0xab24d0: LeaveFrame
    //     0xab24d0: mov             SP, fp
    //     0xab24d4: ldp             fp, lr, [SP], #0x10
    // 0xab24d8: ret
    //     0xab24d8: ret             
    // 0xab24dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab24dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab24e0: b               #0xab24a4
  }
  static _ normalize(/* No info */) {
    // ** addr: 0xab263c, size: 0x60
    // 0xab263c: EnterFrame
    //     0xab263c: stp             fp, lr, [SP, #-0x10]!
    //     0xab2640: mov             fp, SP
    // 0xab2644: AllocStack(0x8)
    //     0xab2644: sub             SP, SP, #8
    // 0xab2648: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xab2648: mov             x2, x1
    //     0xab264c: stur            x1, [fp, #-8]
    // 0xab2650: CheckStackOverflow
    //     0xab2650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2654: cmp             SP, x16
    //     0xab2658: b.ls            #0xab2694
    // 0xab265c: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0xab265c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab2660: ldr             x0, [x0, #0x1848]
    //     0xab2664: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab2668: cmp             w0, w16
    //     0xab266c: b.ne            #0xab267c
    //     0xab2670: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0xab2674: ldr             x2, [x2, #0xbf0]
    //     0xab2678: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab267c: mov             x1, x0
    // 0xab2680: ldur            x2, [fp, #-8]
    // 0xab2684: r0 = normalize()
    //     0xab2684: bl              #0xab269c  ; [package:path/src/context.dart] Context::normalize
    // 0xab2688: LeaveFrame
    //     0xab2688: mov             SP, fp
    //     0xab268c: ldp             fp, lr, [SP], #0x10
    // 0xab2690: ret
    //     0xab2690: ret             
    // 0xab2694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2698: b               #0xab265c
  }
  static _ isRelative(/* No info */) {
    // ** addr: 0xab37ac, size: 0x60
    // 0xab37ac: EnterFrame
    //     0xab37ac: stp             fp, lr, [SP, #-0x10]!
    //     0xab37b0: mov             fp, SP
    // 0xab37b4: AllocStack(0x8)
    //     0xab37b4: sub             SP, SP, #8
    // 0xab37b8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xab37b8: mov             x2, x1
    //     0xab37bc: stur            x1, [fp, #-8]
    // 0xab37c0: CheckStackOverflow
    //     0xab37c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab37c4: cmp             SP, x16
    //     0xab37c8: b.ls            #0xab3804
    // 0xab37cc: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0xab37cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab37d0: ldr             x0, [x0, #0x1848]
    //     0xab37d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab37d8: cmp             w0, w16
    //     0xab37dc: b.ne            #0xab37ec
    //     0xab37e0: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0xab37e4: ldr             x2, [x2, #0xbf0]
    //     0xab37e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab37ec: mov             x1, x0
    // 0xab37f0: ldur            x2, [fp, #-8]
    // 0xab37f4: r0 = isRelative()
    //     0xab37f4: bl              #0xab380c  ; [package:path/src/context.dart] Context::isRelative
    // 0xab37f8: LeaveFrame
    //     0xab37f8: mov             SP, fp
    //     0xab37fc: ldp             fp, lr, [SP], #0x10
    // 0xab3800: ret
    //     0xab3800: ret             
    // 0xab3804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3808: b               #0xab37cc
  }
  static String prettyUri(Object?) {
    // ** addr: 0xc1738c, size: 0x60
    // 0xc1738c: EnterFrame
    //     0xc1738c: stp             fp, lr, [SP, #-0x10]!
    //     0xc17390: mov             fp, SP
    // 0xc17394: AllocStack(0x8)
    //     0xc17394: sub             SP, SP, #8
    // 0xc17398: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc17398: mov             x2, x1
    //     0xc1739c: stur            x1, [fp, #-8]
    // 0xc173a0: CheckStackOverflow
    //     0xc173a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc173a4: cmp             SP, x16
    //     0xc173a8: b.ls            #0xc173e4
    // 0xc173ac: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0xc173ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc173b0: ldr             x0, [x0, #0x1848]
    //     0xc173b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc173b8: cmp             w0, w16
    //     0xc173bc: b.ne            #0xc173cc
    //     0xc173c0: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0xc173c4: ldr             x2, [x2, #0xbf0]
    //     0xc173c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc173cc: mov             x1, x0
    // 0xc173d0: ldur            x2, [fp, #-8]
    // 0xc173d4: r0 = prettyUri()
    //     0xc173d4: bl              #0xc173ec  ; [package:path/src/context.dart] Context::prettyUri
    // 0xc173d8: LeaveFrame
    //     0xc173d8: mov             SP, fp
    //     0xc173dc: ldp             fp, lr, [SP], #0x10
    // 0xc173e0: ret
    //     0xc173e0: ret             
    // 0xc173e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc173e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc173e8: b               #0xc173ac
  }
  static _ joinAll(/* No info */) {
    // ** addr: 0xe5b144, size: 0x60
    // 0xe5b144: EnterFrame
    //     0xe5b144: stp             fp, lr, [SP, #-0x10]!
    //     0xe5b148: mov             fp, SP
    // 0xe5b14c: AllocStack(0x8)
    //     0xe5b14c: sub             SP, SP, #8
    // 0xe5b150: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xe5b150: mov             x2, x1
    //     0xe5b154: stur            x1, [fp, #-8]
    // 0xe5b158: CheckStackOverflow
    //     0xe5b158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5b15c: cmp             SP, x16
    //     0xe5b160: b.ls            #0xe5b19c
    // 0xe5b164: r0 = InitLateStaticField(0xc24) // [package:path/path.dart] ::context
    //     0xe5b164: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe5b168: ldr             x0, [x0, #0x1848]
    //     0xe5b16c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe5b170: cmp             w0, w16
    //     0xe5b174: b.ne            #0xe5b184
    //     0xe5b178: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf0] Field <::.context>: static late final (offset: 0xc24)
    //     0xe5b17c: ldr             x2, [x2, #0xbf0]
    //     0xe5b180: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe5b184: mov             x1, x0
    // 0xe5b188: ldur            x2, [fp, #-8]
    // 0xe5b18c: r0 = joinAll()
    //     0xe5b18c: bl              #0x831d44  ; [package:path/src/context.dart] Context::joinAll
    // 0xe5b190: LeaveFrame
    //     0xe5b190: mov             SP, fp
    //     0xe5b194: ldp             fp, lr, [SP], #0x10
    // 0xe5b198: ret
    //     0xe5b198: ret             
    // 0xe5b19c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5b19c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5b1a0: b               #0xe5b164
  }
}
