// lib: , url: package:path/src/context.dart

// class id: 1050753, size: 0x8
class :: {

  static _ _validateArgList(/* No info */) {
    // ** addr: 0x8331d0, size: 0x224
    // 0x8331d0: EnterFrame
    //     0x8331d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8331d4: mov             fp, SP
    // 0x8331d8: AllocStack(0x48)
    //     0x8331d8: sub             SP, SP, #0x48
    // 0x8331dc: SetupParameters(dynamic _ /* r1 => r0, fp-0x20 */, dynamic _ /* r2 => r1, fp-0x28 */)
    //     0x8331dc: mov             x0, x1
    //     0x8331e0: stur            x1, [fp, #-0x20]
    //     0x8331e4: mov             x1, x2
    //     0x8331e8: stur            x2, [fp, #-0x28]
    // 0x8331ec: CheckStackOverflow
    //     0x8331ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8331f0: cmp             SP, x16
    //     0x8331f4: b.ls            #0x8333dc
    // 0x8331f8: LoadField: r2 = r1->field_b
    //     0x8331f8: ldur            w2, [x1, #0xb]
    // 0x8331fc: r3 = LoadInt32Instr(r2)
    //     0x8331fc: sbfx            x3, x2, #1, #0x1f
    // 0x833200: LoadField: r2 = r1->field_f
    //     0x833200: ldur            w2, [x1, #0xf]
    // 0x833204: DecompressPointer r2
    //     0x833204: add             x2, x2, HEAP, lsl #32
    // 0x833208: r4 = 1
    //     0x833208: movz            x4, #0x1
    // 0x83320c: CheckStackOverflow
    //     0x83320c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833210: cmp             SP, x16
    //     0x833214: b.ls            #0x8333e4
    // 0x833218: cmp             x4, x3
    // 0x83321c: b.ge            #0x8332a0
    // 0x833220: lsl             x5, x4, #1
    // 0x833224: stur            x5, [fp, #-0x18]
    // 0x833228: ArrayLoad: r6 = r2[r4]  ; Unknown_4
    //     0x833228: add             x16, x2, x4, lsl #2
    //     0x83322c: ldur            w6, [x16, #0xf]
    // 0x833230: DecompressPointer r6
    //     0x833230: add             x6, x6, HEAP, lsl #32
    // 0x833234: cmp             w6, NULL
    // 0x833238: b.eq            #0x83325c
    // 0x83323c: sub             x6, x4, #1
    // 0x833240: lsl             x7, x6, #1
    // 0x833244: stur            x7, [fp, #-0x10]
    // 0x833248: ArrayLoad: r8 = r2[r6]  ; Unknown_4
    //     0x833248: add             x16, x2, x6, lsl #2
    //     0x83324c: ldur            w8, [x16, #0xf]
    // 0x833250: DecompressPointer r8
    //     0x833250: add             x8, x8, HEAP, lsl #32
    // 0x833254: cmp             w8, NULL
    // 0x833258: b.eq            #0x833268
    // 0x83325c: add             x5, x4, #1
    // 0x833260: mov             x4, x5
    // 0x833264: b               #0x83320c
    // 0x833268: stur            x3, [fp, #-8]
    // 0x83326c: CheckStackOverflow
    //     0x83326c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833270: cmp             SP, x16
    //     0x833274: b.ls            #0x8333ec
    // 0x833278: cmp             x3, #1
    // 0x83327c: b.lt            #0x8332b0
    // 0x833280: sub             x4, x3, #1
    // 0x833284: ArrayLoad: r6 = r2[r4]  ; Unknown_4
    //     0x833284: add             x16, x2, x4, lsl #2
    //     0x833288: ldur            w6, [x16, #0xf]
    // 0x83328c: DecompressPointer r6
    //     0x83328c: add             x6, x6, HEAP, lsl #32
    // 0x833290: cmp             w6, NULL
    // 0x833294: b.ne            #0x8332b0
    // 0x833298: mov             x3, x4
    // 0x83329c: b               #0x833268
    // 0x8332a0: r0 = Null
    //     0x8332a0: mov             x0, NULL
    // 0x8332a4: LeaveFrame
    //     0x8332a4: mov             SP, fp
    //     0x8332a8: ldp             fp, lr, [SP], #0x10
    // 0x8332ac: ret
    //     0x8332ac: ret             
    // 0x8332b0: r0 = StringBuffer()
    //     0x8332b0: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x8332b4: mov             x1, x0
    // 0x8332b8: stur            x0, [fp, #-0x30]
    // 0x8332bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8332bc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8332c0: r0 = StringBuffer()
    //     0x8332c0: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x8332c4: r1 = Null
    //     0x8332c4: mov             x1, NULL
    // 0x8332c8: r2 = 4
    //     0x8332c8: movz            x2, #0x4
    // 0x8332cc: r0 = AllocateArray()
    //     0x8332cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8332d0: mov             x1, x0
    // 0x8332d4: ldur            x0, [fp, #-0x20]
    // 0x8332d8: StoreField: r1->field_f = r0
    //     0x8332d8: stur            w0, [x1, #0xf]
    // 0x8332dc: r16 = "("
    //     0x8332dc: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0x8332e0: ldr             x16, [x16, #0xf08]
    // 0x8332e4: StoreField: r1->field_13 = r16
    //     0x8332e4: stur            w16, [x1, #0x13]
    // 0x8332e8: str             x1, [SP]
    // 0x8332ec: r0 = _interpolate()
    //     0x8332ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8332f0: ldur            x1, [fp, #-0x30]
    // 0x8332f4: mov             x2, x0
    // 0x8332f8: r0 = write()
    //     0x8332f8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8332fc: ldur            x1, [fp, #-0x28]
    // 0x833300: ldur            x2, [fp, #-8]
    // 0x833304: r0 = take()
    //     0x833304: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0x833308: r1 = Function '<anonymous closure>': static.
    //     0x833308: add             x1, PP, #0xb, lsl #12  ; [pp+0xbc10] AnonymousClosure: static (0x8333f4), in [package:path/src/context.dart] ::_validateArgList (0x8331d0)
    //     0x83330c: ldr             x1, [x1, #0xc10]
    // 0x833310: r2 = Null
    //     0x833310: mov             x2, NULL
    // 0x833314: stur            x0, [fp, #-0x20]
    // 0x833318: r0 = AllocateClosure()
    //     0x833318: bl              #0xec1630  ; AllocateClosureStub
    // 0x83331c: r16 = <String>
    //     0x83331c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x833320: ldur            lr, [fp, #-0x20]
    // 0x833324: stp             lr, x16, [SP, #8]
    // 0x833328: str             x0, [SP]
    // 0x83332c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x83332c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x833330: r0 = map()
    //     0x833330: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0x833334: r16 = ", "
    //     0x833334: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x833338: str             x16, [SP]
    // 0x83333c: mov             x1, x0
    // 0x833340: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x833340: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x833344: r0 = join()
    //     0x833344: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0x833348: ldur            x1, [fp, #-0x30]
    // 0x83334c: mov             x2, x0
    // 0x833350: r0 = write()
    //     0x833350: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x833354: r1 = Null
    //     0x833354: mov             x1, NULL
    // 0x833358: r2 = 10
    //     0x833358: movz            x2, #0xa
    // 0x83335c: r0 = AllocateArray()
    //     0x83335c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x833360: r16 = "): part "
    //     0x833360: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc18] "): part "
    //     0x833364: ldr             x16, [x16, #0xc18]
    // 0x833368: StoreField: r0->field_f = r16
    //     0x833368: stur            w16, [x0, #0xf]
    // 0x83336c: ldur            x1, [fp, #-0x10]
    // 0x833370: StoreField: r0->field_13 = r1
    //     0x833370: stur            w1, [x0, #0x13]
    // 0x833374: r16 = " was null, but part "
    //     0x833374: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc20] " was null, but part "
    //     0x833378: ldr             x16, [x16, #0xc20]
    // 0x83337c: ArrayStore: r0[0] = r16  ; List_4
    //     0x83337c: stur            w16, [x0, #0x17]
    // 0x833380: ldur            x1, [fp, #-0x18]
    // 0x833384: StoreField: r0->field_1b = r1
    //     0x833384: stur            w1, [x0, #0x1b]
    // 0x833388: r16 = " was not."
    //     0x833388: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] " was not."
    //     0x83338c: ldr             x16, [x16, #0xc28]
    // 0x833390: StoreField: r0->field_1f = r16
    //     0x833390: stur            w16, [x0, #0x1f]
    // 0x833394: str             x0, [SP]
    // 0x833398: r0 = _interpolate()
    //     0x833398: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x83339c: ldur            x1, [fp, #-0x30]
    // 0x8333a0: mov             x2, x0
    // 0x8333a4: r0 = write()
    //     0x8333a4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8333a8: ldur            x16, [fp, #-0x30]
    // 0x8333ac: str             x16, [SP]
    // 0x8333b0: r0 = toString()
    //     0x8333b0: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x8333b4: stur            x0, [fp, #-0x10]
    // 0x8333b8: r0 = ArgumentError()
    //     0x8333b8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8333bc: mov             x1, x0
    // 0x8333c0: ldur            x0, [fp, #-0x10]
    // 0x8333c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8333c4: stur            w0, [x1, #0x17]
    // 0x8333c8: r0 = false
    //     0x8333c8: add             x0, NULL, #0x30  ; false
    // 0x8333cc: StoreField: r1->field_b = r0
    //     0x8333cc: stur            w0, [x1, #0xb]
    // 0x8333d0: mov             x0, x1
    // 0x8333d4: r0 = Throw()
    //     0x8333d4: bl              #0xec04b8  ; ThrowStub
    // 0x8333d8: brk             #0
    // 0x8333dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8333dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8333e0: b               #0x8331f8
    // 0x8333e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8333e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8333e8: b               #0x833218
    // 0x8333ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8333ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8333f0: b               #0x833278
  }
  [closure] static String <anonymous closure>(dynamic, String?) {
    // ** addr: 0x8333f4, size: 0x6c
    // 0x8333f4: EnterFrame
    //     0x8333f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8333f8: mov             fp, SP
    // 0x8333fc: AllocStack(0x8)
    //     0x8333fc: sub             SP, SP, #8
    // 0x833400: CheckStackOverflow
    //     0x833400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833404: cmp             SP, x16
    //     0x833408: b.ls            #0x833458
    // 0x83340c: ldr             x0, [fp, #0x10]
    // 0x833410: cmp             w0, NULL
    // 0x833414: b.ne            #0x833420
    // 0x833418: r0 = "null"
    //     0x833418: ldr             x0, [PP, #0x4b8]  ; [pp+0x4b8] "null"
    // 0x83341c: b               #0x83344c
    // 0x833420: r1 = Null
    //     0x833420: mov             x1, NULL
    // 0x833424: r2 = 6
    //     0x833424: movz            x2, #0x6
    // 0x833428: r0 = AllocateArray()
    //     0x833428: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83342c: r16 = "\""
    //     0x83342c: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x833430: StoreField: r0->field_f = r16
    //     0x833430: stur            w16, [x0, #0xf]
    // 0x833434: ldr             x1, [fp, #0x10]
    // 0x833438: StoreField: r0->field_13 = r1
    //     0x833438: stur            w1, [x0, #0x13]
    // 0x83343c: r16 = "\""
    //     0x83343c: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x833440: ArrayStore: r0[0] = r16  ; List_4
    //     0x833440: stur            w16, [x0, #0x17]
    // 0x833444: str             x0, [SP]
    // 0x833448: r0 = _interpolate()
    //     0x833448: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x83344c: LeaveFrame
    //     0x83344c: mov             SP, fp
    //     0x833450: ldp             fp, lr, [SP], #0x10
    // 0x833454: ret
    //     0x833454: ret             
    // 0x833458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83345c: b               #0x83340c
  }
  static Context createInternal() {
    // ** addr: 0x83348c, size: 0x5c
    // 0x83348c: EnterFrame
    //     0x83348c: stp             fp, lr, [SP, #-0x10]!
    //     0x833490: mov             fp, SP
    // 0x833494: AllocStack(0x8)
    //     0x833494: sub             SP, SP, #8
    // 0x833498: CheckStackOverflow
    //     0x833498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83349c: cmp             SP, x16
    //     0x8334a0: b.ls            #0x8334e0
    // 0x8334a4: r0 = InitLateStaticField(0x16d0) // [package:path/src/style.dart] Style::platform
    //     0x8334a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8334a8: ldr             x0, [x0, #0x2da0]
    //     0x8334ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8334b0: cmp             w0, w16
    //     0x8334b4: b.ne            #0x8334c4
    //     0x8334b8: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc30] Field <Style.platform>: static late final (offset: 0x16d0)
    //     0x8334bc: ldr             x2, [x2, #0xc30]
    //     0x8334c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8334c4: stur            x0, [fp, #-8]
    // 0x8334c8: r0 = Context()
    //     0x8334c8: bl              #0x8334e8  ; AllocateContextStub -> Context (size=0x10)
    // 0x8334cc: ldur            x1, [fp, #-8]
    // 0x8334d0: StoreField: r0->field_7 = r1
    //     0x8334d0: stur            w1, [x0, #7]
    // 0x8334d4: LeaveFrame
    //     0x8334d4: mov             SP, fp
    //     0x8334d8: ldp             fp, lr, [SP], #0x10
    // 0x8334dc: ret
    //     0x8334dc: ret             
    // 0x8334e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8334e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8334e4: b               #0x8334a4
  }
}

// class id: 946, size: 0x10, field offset: 0x8
class Context extends Object {

  _ join(/* No info */) {
    // ** addr: 0x831c54, size: 0xf0
    // 0x831c54: EnterFrame
    //     0x831c54: stp             fp, lr, [SP, #-0x10]!
    //     0x831c58: mov             fp, SP
    // 0x831c5c: AllocStack(0x30)
    //     0x831c5c: sub             SP, SP, #0x30
    // 0x831c60: r0 = 32
    //     0x831c60: movz            x0, #0x20
    // 0x831c64: mov             x5, x1
    // 0x831c68: mov             x4, x2
    // 0x831c6c: stur            x1, [fp, #-8]
    // 0x831c70: stur            x2, [fp, #-0x10]
    // 0x831c74: stur            x3, [fp, #-0x18]
    // 0x831c78: CheckStackOverflow
    //     0x831c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x831c7c: cmp             SP, x16
    //     0x831c80: b.ls            #0x831d3c
    // 0x831c84: mov             x2, x0
    // 0x831c88: r1 = <String?>
    //     0x831c88: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x831c8c: r0 = AllocateArray()
    //     0x831c8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x831c90: mov             x2, x0
    // 0x831c94: ldur            x0, [fp, #-0x10]
    // 0x831c98: stur            x2, [fp, #-0x20]
    // 0x831c9c: StoreField: r2->field_f = r0
    //     0x831c9c: stur            w0, [x2, #0xf]
    // 0x831ca0: ldur            x0, [fp, #-0x18]
    // 0x831ca4: StoreField: r2->field_13 = r0
    //     0x831ca4: stur            w0, [x2, #0x13]
    // 0x831ca8: ArrayStore: r2[0] = rNULL  ; List_4
    //     0x831ca8: stur            NULL, [x2, #0x17]
    // 0x831cac: StoreField: r2->field_1b = rNULL
    //     0x831cac: stur            NULL, [x2, #0x1b]
    // 0x831cb0: StoreField: r2->field_1f = rNULL
    //     0x831cb0: stur            NULL, [x2, #0x1f]
    // 0x831cb4: StoreField: r2->field_23 = rNULL
    //     0x831cb4: stur            NULL, [x2, #0x23]
    // 0x831cb8: StoreField: r2->field_27 = rNULL
    //     0x831cb8: stur            NULL, [x2, #0x27]
    // 0x831cbc: StoreField: r2->field_2b = rNULL
    //     0x831cbc: stur            NULL, [x2, #0x2b]
    // 0x831cc0: StoreField: r2->field_2f = rNULL
    //     0x831cc0: stur            NULL, [x2, #0x2f]
    // 0x831cc4: StoreField: r2->field_33 = rNULL
    //     0x831cc4: stur            NULL, [x2, #0x33]
    // 0x831cc8: StoreField: r2->field_37 = rNULL
    //     0x831cc8: stur            NULL, [x2, #0x37]
    // 0x831ccc: StoreField: r2->field_3b = rNULL
    //     0x831ccc: stur            NULL, [x2, #0x3b]
    // 0x831cd0: StoreField: r2->field_3f = rNULL
    //     0x831cd0: stur            NULL, [x2, #0x3f]
    // 0x831cd4: StoreField: r2->field_43 = rNULL
    //     0x831cd4: stur            NULL, [x2, #0x43]
    // 0x831cd8: StoreField: r2->field_47 = rNULL
    //     0x831cd8: stur            NULL, [x2, #0x47]
    // 0x831cdc: StoreField: r2->field_4b = rNULL
    //     0x831cdc: stur            NULL, [x2, #0x4b]
    // 0x831ce0: r1 = <String?>
    //     0x831ce0: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x831ce4: r0 = AllocateGrowableArray()
    //     0x831ce4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x831ce8: mov             x3, x0
    // 0x831cec: ldur            x0, [fp, #-0x20]
    // 0x831cf0: stur            x3, [fp, #-0x10]
    // 0x831cf4: StoreField: r3->field_f = r0
    //     0x831cf4: stur            w0, [x3, #0xf]
    // 0x831cf8: r0 = 32
    //     0x831cf8: movz            x0, #0x20
    // 0x831cfc: StoreField: r3->field_b = r0
    //     0x831cfc: stur            w0, [x3, #0xb]
    // 0x831d00: mov             x2, x3
    // 0x831d04: r1 = "join"
    //     0x831d04: add             x1, PP, #0xb, lsl #12  ; [pp+0xbbf8] "join"
    //     0x831d08: ldr             x1, [x1, #0xbf8]
    // 0x831d0c: r0 = _validateArgList()
    //     0x831d0c: bl              #0x8331d0  ; [package:path/src/context.dart] ::_validateArgList
    // 0x831d10: r16 = <String>
    //     0x831d10: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x831d14: ldur            lr, [fp, #-0x10]
    // 0x831d18: stp             lr, x16, [SP]
    // 0x831d1c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x831d1c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x831d20: r0 = whereType()
    //     0x831d20: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x831d24: ldur            x1, [fp, #-8]
    // 0x831d28: mov             x2, x0
    // 0x831d2c: r0 = joinAll()
    //     0x831d2c: bl              #0x831d44  ; [package:path/src/context.dart] Context::joinAll
    // 0x831d30: LeaveFrame
    //     0x831d30: mov             SP, fp
    //     0x831d34: ldp             fp, lr, [SP], #0x10
    // 0x831d38: ret
    //     0x831d38: ret             
    // 0x831d3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x831d3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x831d40: b               #0x831c84
  }
  _ joinAll(/* No info */) {
    // ** addr: 0x831d44, size: 0xab0
    // 0x831d44: EnterFrame
    //     0x831d44: stp             fp, lr, [SP, #-0x10]!
    //     0x831d48: mov             fp, SP
    // 0x831d4c: AllocStack(0x60)
    //     0x831d4c: sub             SP, SP, #0x60
    // 0x831d50: SetupParameters(Context this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x831d50: mov             x0, x1
    //     0x831d54: stur            x1, [fp, #-8]
    //     0x831d58: mov             x1, x2
    //     0x831d5c: stur            x2, [fp, #-0x10]
    // 0x831d60: CheckStackOverflow
    //     0x831d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x831d64: cmp             SP, x16
    //     0x831d68: b.ls            #0x8327b8
    // 0x831d6c: r0 = StringBuffer()
    //     0x831d6c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x831d70: mov             x1, x0
    // 0x831d74: stur            x0, [fp, #-0x18]
    // 0x831d78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x831d78: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x831d7c: r0 = StringBuffer()
    //     0x831d7c: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x831d80: r1 = Function '<anonymous closure>':.
    //     0x831d80: add             x1, PP, #0xb, lsl #12  ; [pp+0xbc00] AnonymousClosure: (0x833178), in [package:path/src/context.dart] Context::joinAll (0x831d44)
    //     0x831d84: ldr             x1, [x1, #0xc00]
    // 0x831d88: r2 = Null
    //     0x831d88: mov             x2, NULL
    // 0x831d8c: r0 = AllocateClosure()
    //     0x831d8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x831d90: ldur            x1, [fp, #-0x10]
    // 0x831d94: r2 = LoadClassIdInstr(r1)
    //     0x831d94: ldur            x2, [x1, #-1]
    //     0x831d98: ubfx            x2, x2, #0xc, #0x14
    // 0x831d9c: mov             x16, x0
    // 0x831da0: mov             x0, x2
    // 0x831da4: mov             x2, x16
    // 0x831da8: r0 = GDT[cid_x0 + 0xea28]()
    //     0x831da8: movz            x17, #0xea28
    //     0x831dac: add             lr, x0, x17
    //     0x831db0: ldr             lr, [x21, lr, lsl #3]
    //     0x831db4: blr             lr
    // 0x831db8: mov             x1, x0
    // 0x831dbc: r0 = iterator()
    //     0x831dbc: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0x831dc0: LoadField: r2 = r0->field_b
    //     0x831dc0: ldur            w2, [x0, #0xb]
    // 0x831dc4: DecompressPointer r2
    //     0x831dc4: add             x2, x2, HEAP, lsl #32
    // 0x831dc8: stur            x2, [fp, #-0x38]
    // 0x831dcc: LoadField: r3 = r0->field_f
    //     0x831dcc: ldur            w3, [x0, #0xf]
    // 0x831dd0: DecompressPointer r3
    //     0x831dd0: add             x3, x3, HEAP, lsl #32
    // 0x831dd4: ldur            x0, [fp, #-8]
    // 0x831dd8: stur            x3, [fp, #-0x30]
    // 0x831ddc: LoadField: r4 = r0->field_7
    //     0x831ddc: ldur            w4, [x0, #7]
    // 0x831de0: DecompressPointer r4
    //     0x831de0: add             x4, x4, HEAP, lsl #32
    // 0x831de4: stur            x4, [fp, #-0x28]
    // 0x831de8: r5 = LoadClassIdInstr(r4)
    //     0x831de8: ldur            x5, [x4, #-1]
    //     0x831dec: ubfx            x5, x5, #0xc, #0x14
    // 0x831df0: stur            x5, [fp, #-0x20]
    // 0x831df4: r8 = false
    //     0x831df4: add             x8, NULL, #0x30  ; false
    // 0x831df8: r7 = false
    //     0x831df8: add             x7, NULL, #0x30  ; false
    // 0x831dfc: ldur            x6, [fp, #-0x18]
    // 0x831e00: stur            x8, [fp, #-8]
    // 0x831e04: stur            x7, [fp, #-0x10]
    // 0x831e08: CheckStackOverflow
    //     0x831e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x831e0c: cmp             SP, x16
    //     0x831e10: b.ls            #0x8327c0
    // 0x831e14: CheckStackOverflow
    //     0x831e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x831e18: cmp             SP, x16
    //     0x831e1c: b.ls            #0x8327c8
    // 0x831e20: r0 = LoadClassIdInstr(r2)
    //     0x831e20: ldur            x0, [x2, #-1]
    //     0x831e24: ubfx            x0, x0, #0xc, #0x14
    // 0x831e28: mov             x1, x2
    // 0x831e2c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x831e2c: movz            x17, #0x292d
    //     0x831e30: movk            x17, #0x1, lsl #16
    //     0x831e34: add             lr, x0, x17
    //     0x831e38: ldr             lr, [x21, lr, lsl #3]
    //     0x831e3c: blr             lr
    // 0x831e40: tbnz            w0, #4, #0x8327a0
    // 0x831e44: ldur            x2, [fp, #-0x38]
    // 0x831e48: r0 = LoadClassIdInstr(r2)
    //     0x831e48: ldur            x0, [x2, #-1]
    //     0x831e4c: ubfx            x0, x0, #0xc, #0x14
    // 0x831e50: mov             x1, x2
    // 0x831e54: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x831e54: movz            x17, #0x384d
    //     0x831e58: movk            x17, #0x1, lsl #16
    //     0x831e5c: add             lr, x0, x17
    //     0x831e60: ldr             lr, [x21, lr, lsl #3]
    //     0x831e64: blr             lr
    // 0x831e68: ldur            x16, [fp, #-0x30]
    // 0x831e6c: stp             x0, x16, [SP]
    // 0x831e70: ldur            x0, [fp, #-0x30]
    // 0x831e74: ClosureCall
    //     0x831e74: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x831e78: ldur            x2, [x0, #0x1f]
    //     0x831e7c: blr             x2
    // 0x831e80: r16 = true
    //     0x831e80: add             x16, NULL, #0x20  ; true
    // 0x831e84: cmp             w0, w16
    // 0x831e88: b.eq            #0x831eac
    // 0x831e8c: ldur            x6, [fp, #-0x18]
    // 0x831e90: ldur            x8, [fp, #-8]
    // 0x831e94: ldur            x7, [fp, #-0x10]
    // 0x831e98: ldur            x4, [fp, #-0x28]
    // 0x831e9c: ldur            x2, [fp, #-0x38]
    // 0x831ea0: ldur            x3, [fp, #-0x30]
    // 0x831ea4: ldur            x5, [fp, #-0x20]
    // 0x831ea8: b               #0x831e14
    // 0x831eac: ldur            x2, [fp, #-0x38]
    // 0x831eb0: ldur            x3, [fp, #-0x20]
    // 0x831eb4: r0 = LoadClassIdInstr(r2)
    //     0x831eb4: ldur            x0, [x2, #-1]
    //     0x831eb8: ubfx            x0, x0, #0xc, #0x14
    // 0x831ebc: mov             x1, x2
    // 0x831ec0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x831ec0: movz            x17, #0x384d
    //     0x831ec4: movk            x17, #0x1, lsl #16
    //     0x831ec8: add             lr, x0, x17
    //     0x831ecc: ldr             lr, [x21, lr, lsl #3]
    //     0x831ed0: blr             lr
    // 0x831ed4: mov             x3, x0
    // 0x831ed8: ldur            x0, [fp, #-0x20]
    // 0x831edc: stur            x3, [fp, #-0x40]
    // 0x831ee0: cmp             x0, #0x3af
    // 0x831ee4: b.ne            #0x831f0c
    // 0x831ee8: ldur            x1, [fp, #-0x28]
    // 0x831eec: mov             x2, x3
    // 0x831ef0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x831ef0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x831ef4: r0 = rootLength()
    //     0x831ef4: bl              #0xe8d7a4  ; [package:path/src/style/windows.dart] WindowsStyle::rootLength
    // 0x831ef8: cmp             x0, #1
    // 0x831efc: b.ne            #0x832330
    // 0x831f00: ldur            x5, [fp, #-0x40]
    // 0x831f04: ldur            x4, [fp, #-0x20]
    // 0x831f08: b               #0x831f68
    // 0x831f0c: mov             x4, x0
    // 0x831f10: cmp             x4, #0x3b0
    // 0x831f14: b.ne            #0x832330
    // 0x831f18: ldur            x5, [fp, #-0x40]
    // 0x831f1c: LoadField: r0 = r5->field_7
    //     0x831f1c: ldur            w0, [x5, #7]
    // 0x831f20: cbz             w0, #0x832330
    // 0x831f24: r1 = LoadInt32Instr(r0)
    //     0x831f24: sbfx            x1, x0, #1, #0x1f
    // 0x831f28: mov             x0, x1
    // 0x831f2c: r1 = 0
    //     0x831f2c: movz            x1, #0
    // 0x831f30: cmp             x1, x0
    // 0x831f34: b.hs            #0x8327d0
    // 0x831f38: r0 = LoadTaggedClassIdMayBeSmiInstr(r5)
    //     0x831f38: movz            x0, #0x78
    //     0x831f3c: tbz             w5, #0, #0x831f4c
    //     0x831f40: ldur            x0, [x5, #-1]
    //     0x831f44: ubfx            x0, x0, #0xc, #0x14
    //     0x831f48: lsl             x0, x0, #1
    // 0x831f4c: cmp             w0, #0xbc
    // 0x831f50: b.ne            #0x831f5c
    // 0x831f54: ArrayLoad: r0 = r5[-8]  ; TypedUnsigned_1
    //     0x831f54: ldrb            w0, [x5, #0xf]
    // 0x831f58: b               #0x831f60
    // 0x831f5c: ldurh           w0, [x5, #0xf]
    // 0x831f60: cmp             x0, #0x2f
    // 0x831f64: b.ne            #0x832330
    // 0x831f68: ldur            x0, [fp, #-0x10]
    // 0x831f6c: tbnz            w0, #4, #0x832330
    // 0x831f70: ldur            x6, [fp, #-0x18]
    // 0x831f74: mov             x2, x5
    // 0x831f78: ldur            x3, [fp, #-0x28]
    // 0x831f7c: r1 = Null
    //     0x831f7c: mov             x1, NULL
    // 0x831f80: r0 = ParsedPath.parse()
    //     0x831f80: bl              #0x8329d4  ; [package:path/src/parsed_path.dart] ParsedPath::ParsedPath.parse
    // 0x831f84: ldur            x1, [fp, #-0x18]
    // 0x831f88: stur            x0, [fp, #-0x48]
    // 0x831f8c: r0 = _consumeBuffer()
    //     0x831f8c: bl              #0x601270  ; [dart:core] StringBuffer::_consumeBuffer
    // 0x831f90: ldur            x0, [fp, #-0x18]
    // 0x831f94: LoadField: r1 = r0->field_7
    //     0x831f94: ldur            w1, [x0, #7]
    // 0x831f98: DecompressPointer r1
    //     0x831f98: add             x1, x1, HEAP, lsl #32
    // 0x831f9c: LoadField: r2 = r0->field_b
    //     0x831f9c: ldur            x2, [x0, #0xb]
    // 0x831fa0: cbz             x2, #0x831fac
    // 0x831fa4: cmp             w1, NULL
    // 0x831fa8: b.ne            #0x831fb4
    // 0x831fac: r4 = ""
    //     0x831fac: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x831fb0: b               #0x831fc8
    // 0x831fb4: LoadField: r2 = r1->field_b
    //     0x831fb4: ldur            w2, [x1, #0xb]
    // 0x831fb8: r3 = LoadInt32Instr(r2)
    //     0x831fb8: sbfx            x3, x2, #1, #0x1f
    // 0x831fbc: r2 = 0
    //     0x831fbc: movz            x2, #0
    // 0x831fc0: r0 = _concatRange()
    //     0x831fc0: bl              #0x601060  ; [dart:core] _StringBase::_concatRange
    // 0x831fc4: mov             x4, x0
    // 0x831fc8: ldur            x3, [fp, #-0x20]
    // 0x831fcc: stur            x4, [fp, #-0x50]
    // 0x831fd0: cmp             x3, #0x3b1
    // 0x831fd4: b.ne            #0x832044
    // 0x831fd8: LoadField: r0 = r4->field_7
    //     0x831fd8: ldur            w0, [x4, #7]
    // 0x831fdc: cbz             w0, #0x832034
    // 0x831fe0: r1 = LoadInt32Instr(r0)
    //     0x831fe0: sbfx            x1, x0, #1, #0x1f
    // 0x831fe4: mov             x0, x1
    // 0x831fe8: r1 = 0
    //     0x831fe8: movz            x1, #0
    // 0x831fec: cmp             x1, x0
    // 0x831ff0: b.hs            #0x8327d4
    // 0x831ff4: r0 = LoadClassIdInstr(r4)
    //     0x831ff4: ldur            x0, [x4, #-1]
    //     0x831ff8: ubfx            x0, x0, #0xc, #0x14
    // 0x831ffc: lsl             x0, x0, #1
    // 0x832000: cmp             w0, #0xbc
    // 0x832004: b.ne            #0x832018
    // 0x832008: ArrayLoad: r0 = r4[-8]  ; TypedUnsigned_1
    //     0x832008: ldrb            w0, [x4, #0xf]
    // 0x83200c: cmp             x0, #0x2f
    // 0x832010: b.ne            #0x832034
    // 0x832014: b               #0x832024
    // 0x832018: ldurh           w0, [x4, #0xf]
    // 0x83201c: cmp             x0, #0x2f
    // 0x832020: b.ne            #0x832034
    // 0x832024: mov             x5, x4
    // 0x832028: mov             x4, x3
    // 0x83202c: r2 = 1
    //     0x83202c: movz            x2, #0x1
    // 0x832030: b               #0x832080
    // 0x832034: mov             x5, x4
    // 0x832038: mov             x4, x3
    // 0x83203c: r2 = 0
    //     0x83203c: movz            x2, #0
    // 0x832040: b               #0x832080
    // 0x832044: ldur            x5, [fp, #-0x28]
    // 0x832048: r0 = LoadClassIdInstr(r5)
    //     0x832048: ldur            x0, [x5, #-1]
    //     0x83204c: ubfx            x0, x0, #0xc, #0x14
    // 0x832050: r16 = true
    //     0x832050: add             x16, NULL, #0x20  ; true
    // 0x832054: str             x16, [SP]
    // 0x832058: mov             x1, x5
    // 0x83205c: mov             x2, x4
    // 0x832060: r4 = const [0, 0x3, 0x1, 0x2, withDrive, 0x2, null]
    //     0x832060: add             x4, PP, #0xb, lsl #12  ; [pp+0xbc08] List(7) [0, 0x3, 0x1, 0x2, "withDrive", 0x2, Null]
    //     0x832064: ldr             x4, [x4, #0xc08]
    // 0x832068: r0 = GDT[cid_x0 + -0xffd]()
    //     0x832068: sub             lr, x0, #0xffd
    //     0x83206c: ldr             lr, [x21, lr, lsl #3]
    //     0x832070: blr             lr
    // 0x832074: mov             x2, x0
    // 0x832078: ldur            x5, [fp, #-0x50]
    // 0x83207c: ldur            x4, [fp, #-0x20]
    // 0x832080: ldur            x6, [fp, #-0x48]
    // 0x832084: LoadField: r3 = r5->field_7
    //     0x832084: ldur            w3, [x5, #7]
    // 0x832088: r0 = BoxInt64Instr(r2)
    //     0x832088: sbfiz           x0, x2, #1, #0x1f
    //     0x83208c: cmp             x2, x0, asr #1
    //     0x832090: b.eq            #0x83209c
    //     0x832094: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x832098: stur            x2, [x0, #7]
    // 0x83209c: r1 = LoadInt32Instr(r3)
    //     0x83209c: sbfx            x1, x3, #1, #0x1f
    // 0x8320a0: mov             x2, x0
    // 0x8320a4: mov             x3, x1
    // 0x8320a8: r1 = 0
    //     0x8320a8: movz            x1, #0
    // 0x8320ac: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8320ac: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8320b0: r0 = checkValidRange()
    //     0x8320b0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x8320b4: ldur            x1, [fp, #-0x50]
    // 0x8320b8: mov             x3, x0
    // 0x8320bc: r2 = 0
    //     0x8320bc: movz            x2, #0
    // 0x8320c0: r0 = _substringUnchecked()
    //     0x8320c0: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x8320c4: mov             x2, x0
    // 0x8320c8: ldur            x3, [fp, #-0x48]
    // 0x8320cc: StoreField: r3->field_b = r0
    //     0x8320cc: stur            w0, [x3, #0xb]
    //     0x8320d0: ldurb           w16, [x3, #-1]
    //     0x8320d4: ldurb           w17, [x0, #-1]
    //     0x8320d8: and             x16, x17, x16, lsr #2
    //     0x8320dc: tst             x16, HEAP, lsr #32
    //     0x8320e0: b.eq            #0x8320e8
    //     0x8320e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8320e8: ldur            x4, [fp, #-0x20]
    // 0x8320ec: cmp             x4, #0x3af
    // 0x8320f0: b.ne            #0x832194
    // 0x8320f4: LoadField: r0 = r2->field_7
    //     0x8320f4: ldur            w0, [x2, #7]
    // 0x8320f8: cbnz            w0, #0x83210c
    // 0x8320fc: mov             x2, x3
    // 0x832100: ldur            x5, [fp, #-0x28]
    // 0x832104: mov             x3, x4
    // 0x832108: b               #0x8322d4
    // 0x83210c: r1 = LoadInt32Instr(r0)
    //     0x83210c: sbfx            x1, x0, #1, #0x1f
    // 0x832110: sub             x5, x1, #1
    // 0x832114: mov             x0, x1
    // 0x832118: mov             x1, x5
    // 0x83211c: cmp             x1, x0
    // 0x832120: b.hs            #0x8327d8
    // 0x832124: r0 = LoadClassIdInstr(r2)
    //     0x832124: ldur            x0, [x2, #-1]
    //     0x832128: ubfx            x0, x0, #0xc, #0x14
    // 0x83212c: lsl             x0, x0, #1
    // 0x832130: cmp             w0, #0xbc
    // 0x832134: b.ne            #0x832144
    // 0x832138: ArrayLoad: r0 = r2[r5]  ; TypedUnsigned_1
    //     0x832138: add             x16, x2, x5
    //     0x83213c: ldrb            w0, [x16, #0xf]
    // 0x832140: b               #0x83214c
    // 0x832144: add             x16, x2, x5, lsl #1
    // 0x832148: ldurh           w0, [x16, #0xf]
    // 0x83214c: cmp             x0, #0x2f
    // 0x832150: b.ne            #0x83215c
    // 0x832154: r0 = true
    //     0x832154: add             x0, NULL, #0x20  ; true
    // 0x832158: b               #0x832170
    // 0x83215c: cmp             x0, #0x5c
    // 0x832160: r16 = true
    //     0x832160: add             x16, NULL, #0x20  ; true
    // 0x832164: r17 = false
    //     0x832164: add             x17, NULL, #0x30  ; false
    // 0x832168: csel            x1, x16, x17, eq
    // 0x83216c: mov             x0, x1
    // 0x832170: eor             x1, x0, #0x10
    // 0x832174: tbnz            w1, #4, #0x832184
    // 0x832178: mov             x2, x3
    // 0x83217c: mov             x3, x4
    // 0x832180: b               #0x832230
    // 0x832184: mov             x2, x3
    // 0x832188: ldur            x5, [fp, #-0x28]
    // 0x83218c: mov             x3, x4
    // 0x832190: b               #0x8322d4
    // 0x832194: cmp             x4, #0x3b1
    // 0x832198: b.ne            #0x832208
    // 0x83219c: LoadField: r0 = r2->field_7
    //     0x83219c: ldur            w0, [x2, #7]
    // 0x8321a0: cbz             w0, #0x8321f8
    // 0x8321a4: r1 = LoadInt32Instr(r0)
    //     0x8321a4: sbfx            x1, x0, #1, #0x1f
    // 0x8321a8: sub             x5, x1, #1
    // 0x8321ac: mov             x0, x1
    // 0x8321b0: mov             x1, x5
    // 0x8321b4: cmp             x1, x0
    // 0x8321b8: b.hs            #0x8327dc
    // 0x8321bc: r0 = LoadClassIdInstr(r2)
    //     0x8321bc: ldur            x0, [x2, #-1]
    //     0x8321c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8321c4: lsl             x0, x0, #1
    // 0x8321c8: cmp             w0, #0xbc
    // 0x8321cc: b.ne            #0x8321dc
    // 0x8321d0: ArrayLoad: r0 = r2[r5]  ; TypedUnsigned_1
    //     0x8321d0: add             x16, x2, x5
    //     0x8321d4: ldrb            w0, [x16, #0xf]
    // 0x8321d8: b               #0x8321e4
    // 0x8321dc: add             x16, x2, x5, lsl #1
    // 0x8321e0: ldurh           w0, [x16, #0xf]
    // 0x8321e4: cmp             x0, #0x2f
    // 0x8321e8: b.eq            #0x8321f8
    // 0x8321ec: mov             x2, x3
    // 0x8321f0: mov             x3, x4
    // 0x8321f4: b               #0x832230
    // 0x8321f8: mov             x2, x3
    // 0x8321fc: ldur            x5, [fp, #-0x28]
    // 0x832200: mov             x3, x4
    // 0x832204: b               #0x8322d4
    // 0x832208: ldur            x5, [fp, #-0x28]
    // 0x83220c: r0 = LoadClassIdInstr(r5)
    //     0x83220c: ldur            x0, [x5, #-1]
    //     0x832210: ubfx            x0, x0, #0xc, #0x14
    // 0x832214: mov             x1, x5
    // 0x832218: r0 = GDT[cid_x0 + -0xff1]()
    //     0x832218: sub             lr, x0, #0xff1
    //     0x83221c: ldr             lr, [x21, lr, lsl #3]
    //     0x832220: blr             lr
    // 0x832224: tbnz            w0, #4, #0x8322c8
    // 0x832228: ldur            x2, [fp, #-0x48]
    // 0x83222c: ldur            x3, [fp, #-0x20]
    // 0x832230: LoadField: r4 = r2->field_13
    //     0x832230: ldur            w4, [x2, #0x13]
    // 0x832234: DecompressPointer r4
    //     0x832234: add             x4, x4, HEAP, lsl #32
    // 0x832238: cmp             x3, #0x3af
    // 0x83223c: b.ne            #0x832254
    // 0x832240: ldur            x5, [fp, #-0x28]
    // 0x832244: LoadField: r0 = r5->field_b
    //     0x832244: ldur            w0, [x5, #0xb]
    // 0x832248: DecompressPointer r0
    //     0x832248: add             x0, x0, HEAP, lsl #32
    // 0x83224c: mov             x6, x0
    // 0x832250: b               #0x83227c
    // 0x832254: ldur            x5, [fp, #-0x28]
    // 0x832258: cmp             x3, #0x3b0
    // 0x83225c: b.ne            #0x832270
    // 0x832260: LoadField: r0 = r5->field_b
    //     0x832260: ldur            w0, [x5, #0xb]
    // 0x832264: DecompressPointer r0
    //     0x832264: add             x0, x0, HEAP, lsl #32
    // 0x832268: mov             x6, x0
    // 0x83226c: b               #0x83227c
    // 0x832270: LoadField: r0 = r5->field_b
    //     0x832270: ldur            w0, [x5, #0xb]
    // 0x832274: DecompressPointer r0
    //     0x832274: add             x0, x0, HEAP, lsl #32
    // 0x832278: mov             x6, x0
    // 0x83227c: LoadField: r0 = r4->field_b
    //     0x83227c: ldur            w0, [x4, #0xb]
    // 0x832280: r1 = LoadInt32Instr(r0)
    //     0x832280: sbfx            x1, x0, #1, #0x1f
    // 0x832284: mov             x0, x1
    // 0x832288: r1 = 0
    //     0x832288: movz            x1, #0
    // 0x83228c: cmp             x1, x0
    // 0x832290: b.hs            #0x8327e0
    // 0x832294: LoadField: r1 = r4->field_f
    //     0x832294: ldur            w1, [x4, #0xf]
    // 0x832298: DecompressPointer r1
    //     0x832298: add             x1, x1, HEAP, lsl #32
    // 0x83229c: mov             x0, x6
    // 0x8322a0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8322a0: add             x25, x1, #0xf
    //     0x8322a4: str             w0, [x25]
    //     0x8322a8: tbz             w0, #0, #0x8322c4
    //     0x8322ac: ldurb           w16, [x1, #-1]
    //     0x8322b0: ldurb           w17, [x0, #-1]
    //     0x8322b4: and             x16, x17, x16, lsr #2
    //     0x8322b8: tst             x16, HEAP, lsr #32
    //     0x8322bc: b.eq            #0x8322c4
    //     0x8322c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8322c4: b               #0x8322d4
    // 0x8322c8: ldur            x2, [fp, #-0x48]
    // 0x8322cc: ldur            x5, [fp, #-0x28]
    // 0x8322d0: ldur            x3, [fp, #-0x20]
    // 0x8322d4: ldur            x1, [fp, #-0x18]
    // 0x8322d8: StoreField: r1->field_7 = rNULL
    //     0x8322d8: stur            NULL, [x1, #7]
    // 0x8322dc: StoreField: r1->field_2f = rZR
    //     0x8322dc: stur            xzr, [x1, #0x2f]
    // 0x8322e0: StoreField: r1->field_27 = rZR
    //     0x8322e0: stur            xzr, [x1, #0x27]
    // 0x8322e4: StoreField: r1->field_b = rZR
    //     0x8322e4: stur            xzr, [x1, #0xb]
    // 0x8322e8: str             x2, [SP]
    // 0x8322ec: r0 = toString()
    //     0x8322ec: bl              #0xc336cc  ; [package:path/src/parsed_path.dart] ParsedPath::toString
    // 0x8322f0: r1 = LoadClassIdInstr(r0)
    //     0x8322f0: ldur            x1, [x0, #-1]
    //     0x8322f4: ubfx            x1, x1, #0xc, #0x14
    // 0x8322f8: str             x0, [SP]
    // 0x8322fc: mov             x0, x1
    // 0x832300: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x832300: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x832304: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x832304: movz            x17, #0x2b03
    //     0x832308: add             lr, x0, x17
    //     0x83230c: ldr             lr, [x21, lr, lsl #3]
    //     0x832310: blr             lr
    // 0x832314: LoadField: r1 = r0->field_7
    //     0x832314: ldur            w1, [x0, #7]
    // 0x832318: cbz             w1, #0x832328
    // 0x83231c: ldur            x1, [fp, #-0x18]
    // 0x832320: mov             x2, x0
    // 0x832324: r0 = _writeString()
    //     0x832324: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x832328: ldur            x7, [fp, #-0x10]
    // 0x83232c: b               #0x83264c
    // 0x832330: ldur            x3, [fp, #-0x20]
    // 0x832334: cmp             x3, #0x3b1
    // 0x832338: b.ne            #0x8323a4
    // 0x83233c: ldur            x4, [fp, #-0x40]
    // 0x832340: LoadField: r0 = r4->field_7
    //     0x832340: ldur            w0, [x4, #7]
    // 0x832344: cbz             w0, #0x83239c
    // 0x832348: r1 = LoadInt32Instr(r0)
    //     0x832348: sbfx            x1, x0, #1, #0x1f
    // 0x83234c: mov             x0, x1
    // 0x832350: r1 = 0
    //     0x832350: movz            x1, #0
    // 0x832354: cmp             x1, x0
    // 0x832358: b.hs            #0x8327e4
    // 0x83235c: r0 = LoadTaggedClassIdMayBeSmiInstr(r4)
    //     0x83235c: movz            x0, #0x78
    //     0x832360: tbz             w4, #0, #0x832370
    //     0x832364: ldur            x0, [x4, #-1]
    //     0x832368: ubfx            x0, x0, #0xc, #0x14
    //     0x83236c: lsl             x0, x0, #1
    // 0x832370: cmp             w0, #0xbc
    // 0x832374: b.ne            #0x832388
    // 0x832378: ArrayLoad: r0 = r4[-8]  ; TypedUnsigned_1
    //     0x832378: ldrb            w0, [x4, #0xf]
    // 0x83237c: cmp             x0, #0x2f
    // 0x832380: b.ne            #0x83239c
    // 0x832384: b               #0x832394
    // 0x832388: ldurh           w0, [x4, #0xf]
    // 0x83238c: cmp             x0, #0x2f
    // 0x832390: b.ne            #0x83239c
    // 0x832394: mov             x0, x3
    // 0x832398: b               #0x8323d8
    // 0x83239c: mov             x2, x4
    // 0x8323a0: b               #0x8324f0
    // 0x8323a4: ldur            x4, [fp, #-0x40]
    // 0x8323a8: ldur            x5, [fp, #-0x28]
    // 0x8323ac: r0 = LoadClassIdInstr(r5)
    //     0x8323ac: ldur            x0, [x5, #-1]
    //     0x8323b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8323b4: mov             x1, x5
    // 0x8323b8: mov             x2, x4
    // 0x8323bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8323bc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8323c0: r0 = GDT[cid_x0 + -0xffd]()
    //     0x8323c0: sub             lr, x0, #0xffd
    //     0x8323c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8323c8: blr             lr
    // 0x8323cc: cmp             x0, #0
    // 0x8323d0: b.le            #0x8324ec
    // 0x8323d4: ldur            x0, [fp, #-0x20]
    // 0x8323d8: cmp             x0, #0x3af
    // 0x8323dc: b.ne            #0x832410
    // 0x8323e0: ldur            x1, [fp, #-0x28]
    // 0x8323e4: ldur            x2, [fp, #-0x40]
    // 0x8323e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8323e8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8323ec: r0 = rootLength()
    //     0x8323ec: bl              #0xe8d7a4  ; [package:path/src/style/windows.dart] WindowsStyle::rootLength
    // 0x8323f0: cmp             x0, #1
    // 0x8323f4: r16 = true
    //     0x8323f4: add             x16, NULL, #0x20  ; true
    // 0x8323f8: r17 = false
    //     0x8323f8: add             x17, NULL, #0x30  ; false
    // 0x8323fc: csel            x1, x16, x17, eq
    // 0x832400: mov             x0, x1
    // 0x832404: ldur            x3, [fp, #-0x40]
    // 0x832408: ldur            x2, [fp, #-0x20]
    // 0x83240c: b               #0x83248c
    // 0x832410: mov             x2, x0
    // 0x832414: cmp             x2, #0x3b0
    // 0x832418: b.ne            #0x832484
    // 0x83241c: ldur            x3, [fp, #-0x40]
    // 0x832420: LoadField: r0 = r3->field_7
    //     0x832420: ldur            w0, [x3, #7]
    // 0x832424: cbz             w0, #0x83247c
    // 0x832428: r1 = LoadInt32Instr(r0)
    //     0x832428: sbfx            x1, x0, #1, #0x1f
    // 0x83242c: mov             x0, x1
    // 0x832430: r1 = 0
    //     0x832430: movz            x1, #0
    // 0x832434: cmp             x1, x0
    // 0x832438: b.hs            #0x8327e8
    // 0x83243c: r0 = LoadTaggedClassIdMayBeSmiInstr(r3)
    //     0x83243c: movz            x0, #0x78
    //     0x832440: tbz             w3, #0, #0x832450
    //     0x832444: ldur            x0, [x3, #-1]
    //     0x832448: ubfx            x0, x0, #0xc, #0x14
    //     0x83244c: lsl             x0, x0, #1
    // 0x832450: cmp             w0, #0xbc
    // 0x832454: b.ne            #0x832460
    // 0x832458: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0x832458: ldrb            w0, [x3, #0xf]
    // 0x83245c: b               #0x832464
    // 0x832460: ldurh           w0, [x3, #0xf]
    // 0x832464: cmp             x0, #0x2f
    // 0x832468: r16 = true
    //     0x832468: add             x16, NULL, #0x20  ; true
    // 0x83246c: r17 = false
    //     0x83246c: add             x17, NULL, #0x30  ; false
    // 0x832470: csel            x1, x16, x17, eq
    // 0x832474: mov             x0, x1
    // 0x832478: b               #0x83248c
    // 0x83247c: r0 = false
    //     0x83247c: add             x0, NULL, #0x30  ; false
    // 0x832480: b               #0x83248c
    // 0x832484: ldur            x3, [fp, #-0x40]
    // 0x832488: r0 = false
    //     0x832488: add             x0, NULL, #0x30  ; false
    // 0x83248c: ldur            x1, [fp, #-0x18]
    // 0x832490: eor             x4, x0, #0x10
    // 0x832494: stur            x4, [fp, #-0x48]
    // 0x832498: StoreField: r1->field_7 = rNULL
    //     0x832498: stur            NULL, [x1, #7]
    // 0x83249c: StoreField: r1->field_2f = rZR
    //     0x83249c: stur            xzr, [x1, #0x2f]
    // 0x8324a0: StoreField: r1->field_27 = rZR
    //     0x8324a0: stur            xzr, [x1, #0x27]
    // 0x8324a4: StoreField: r1->field_b = rZR
    //     0x8324a4: stur            xzr, [x1, #0xb]
    // 0x8324a8: r0 = 60
    //     0x8324a8: movz            x0, #0x3c
    // 0x8324ac: branchIfSmi(r3, 0x8324b8)
    //     0x8324ac: tbz             w3, #0, #0x8324b8
    // 0x8324b0: r0 = LoadClassIdInstr(r3)
    //     0x8324b0: ldur            x0, [x3, #-1]
    //     0x8324b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8324b8: str             x3, [SP]
    // 0x8324bc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8324bc: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8324c0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8324c0: movz            x17, #0x2b03
    //     0x8324c4: add             lr, x0, x17
    //     0x8324c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8324cc: blr             lr
    // 0x8324d0: LoadField: r1 = r0->field_7
    //     0x8324d0: ldur            w1, [x0, #7]
    // 0x8324d4: cbz             w1, #0x8324e4
    // 0x8324d8: ldur            x1, [fp, #-0x18]
    // 0x8324dc: mov             x2, x0
    // 0x8324e0: r0 = _writeString()
    //     0x8324e0: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x8324e4: ldur            x0, [fp, #-0x48]
    // 0x8324e8: b               #0x832648
    // 0x8324ec: ldur            x2, [fp, #-0x40]
    // 0x8324f0: LoadField: r0 = r2->field_7
    //     0x8324f0: ldur            w0, [x2, #7]
    // 0x8324f4: cbz             w0, #0x8325a4
    // 0x8324f8: ldur            x0, [fp, #-0x20]
    // 0x8324fc: stp             xzr, x2, [SP]
    // 0x832500: r0 = []()
    //     0x832500: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x832504: ldur            x3, [fp, #-0x20]
    // 0x832508: cmp             x3, #0x3af
    // 0x83250c: b.ne            #0x832540
    // 0x832510: r1 = LoadClassIdInstr(r0)
    //     0x832510: ldur            x1, [x0, #-1]
    //     0x832514: ubfx            x1, x1, #0xc, #0x14
    // 0x832518: mov             x16, x0
    // 0x83251c: mov             x0, x1
    // 0x832520: mov             x1, x16
    // 0x832524: r2 = "/"
    //     0x832524: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x832528: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832528: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83252c: r0 = GDT[cid_x0 + -0xffc]()
    //     0x83252c: sub             lr, x0, #0xffc
    //     0x832530: ldr             lr, [x21, lr, lsl #3]
    //     0x832534: blr             lr
    // 0x832538: tbnz            w0, #4, #0x8325a4
    // 0x83253c: b               #0x832604
    // 0x832540: cmp             x3, #0x3b0
    // 0x832544: b.ne            #0x832578
    // 0x832548: r1 = LoadClassIdInstr(r0)
    //     0x832548: ldur            x1, [x0, #-1]
    //     0x83254c: ubfx            x1, x1, #0xc, #0x14
    // 0x832550: mov             x16, x0
    // 0x832554: mov             x0, x1
    // 0x832558: mov             x1, x16
    // 0x83255c: r2 = "/"
    //     0x83255c: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x832560: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832560: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832564: r0 = GDT[cid_x0 + -0xffc]()
    //     0x832564: sub             lr, x0, #0xffc
    //     0x832568: ldr             lr, [x21, lr, lsl #3]
    //     0x83256c: blr             lr
    // 0x832570: tbnz            w0, #4, #0x8325a4
    // 0x832574: b               #0x832604
    // 0x832578: r1 = LoadClassIdInstr(r0)
    //     0x832578: ldur            x1, [x0, #-1]
    //     0x83257c: ubfx            x1, x1, #0xc, #0x14
    // 0x832580: mov             x16, x0
    // 0x832584: mov             x0, x1
    // 0x832588: mov             x1, x16
    // 0x83258c: r2 = "/"
    //     0x83258c: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x832590: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832590: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832594: r0 = GDT[cid_x0 + -0xffc]()
    //     0x832594: sub             lr, x0, #0xffc
    //     0x832598: ldr             lr, [x21, lr, lsl #3]
    //     0x83259c: blr             lr
    // 0x8325a0: tbz             w0, #4, #0x832604
    // 0x8325a4: ldur            x0, [fp, #-8]
    // 0x8325a8: tbnz            w0, #4, #0x832604
    // 0x8325ac: ldur            x0, [fp, #-0x20]
    // 0x8325b0: cmp             x0, #0x3af
    // 0x8325b4: b.ne            #0x8325cc
    // 0x8325b8: ldur            x3, [fp, #-0x28]
    // 0x8325bc: LoadField: r1 = r3->field_b
    //     0x8325bc: ldur            w1, [x3, #0xb]
    // 0x8325c0: DecompressPointer r1
    //     0x8325c0: add             x1, x1, HEAP, lsl #32
    // 0x8325c4: mov             x2, x1
    // 0x8325c8: b               #0x8325f4
    // 0x8325cc: ldur            x3, [fp, #-0x28]
    // 0x8325d0: cmp             x0, #0x3b0
    // 0x8325d4: b.ne            #0x8325e8
    // 0x8325d8: LoadField: r1 = r3->field_b
    //     0x8325d8: ldur            w1, [x3, #0xb]
    // 0x8325dc: DecompressPointer r1
    //     0x8325dc: add             x1, x1, HEAP, lsl #32
    // 0x8325e0: mov             x2, x1
    // 0x8325e4: b               #0x8325f4
    // 0x8325e8: LoadField: r1 = r3->field_b
    //     0x8325e8: ldur            w1, [x3, #0xb]
    // 0x8325ec: DecompressPointer r1
    //     0x8325ec: add             x1, x1, HEAP, lsl #32
    // 0x8325f0: mov             x2, x1
    // 0x8325f4: LoadField: r1 = r2->field_7
    //     0x8325f4: ldur            w1, [x2, #7]
    // 0x8325f8: cbz             w1, #0x832604
    // 0x8325fc: ldur            x1, [fp, #-0x18]
    // 0x832600: r0 = _writeString()
    //     0x832600: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x832604: ldur            x2, [fp, #-0x40]
    // 0x832608: r0 = 60
    //     0x832608: movz            x0, #0x3c
    // 0x83260c: branchIfSmi(r2, 0x832618)
    //     0x83260c: tbz             w2, #0, #0x832618
    // 0x832610: r0 = LoadClassIdInstr(r2)
    //     0x832610: ldur            x0, [x2, #-1]
    //     0x832614: ubfx            x0, x0, #0xc, #0x14
    // 0x832618: str             x2, [SP]
    // 0x83261c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x83261c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x832620: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x832620: movz            x17, #0x2b03
    //     0x832624: add             lr, x0, x17
    //     0x832628: ldr             lr, [x21, lr, lsl #3]
    //     0x83262c: blr             lr
    // 0x832630: LoadField: r1 = r0->field_7
    //     0x832630: ldur            w1, [x0, #7]
    // 0x832634: cbz             w1, #0x832644
    // 0x832638: ldur            x1, [fp, #-0x18]
    // 0x83263c: mov             x2, x0
    // 0x832640: r0 = _writeString()
    //     0x832640: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x832644: ldur            x0, [fp, #-0x10]
    // 0x832648: mov             x7, x0
    // 0x83264c: ldur            x3, [fp, #-0x20]
    // 0x832650: stur            x7, [fp, #-8]
    // 0x832654: cmp             x3, #0x3af
    // 0x832658: b.ne            #0x8326e8
    // 0x83265c: ldur            x2, [fp, #-0x40]
    // 0x832660: LoadField: r0 = r2->field_7
    //     0x832660: ldur            w0, [x2, #7]
    // 0x832664: cbnz            w0, #0x832670
    // 0x832668: r8 = false
    //     0x832668: add             x8, NULL, #0x30  ; false
    // 0x83266c: b               #0x832788
    // 0x832670: r1 = LoadInt32Instr(r0)
    //     0x832670: sbfx            x1, x0, #1, #0x1f
    // 0x832674: sub             x4, x1, #1
    // 0x832678: mov             x0, x1
    // 0x83267c: mov             x1, x4
    // 0x832680: cmp             x1, x0
    // 0x832684: b.hs            #0x8327ec
    // 0x832688: r0 = LoadTaggedClassIdMayBeSmiInstr(r2)
    //     0x832688: movz            x0, #0x78
    //     0x83268c: tbz             w2, #0, #0x83269c
    //     0x832690: ldur            x0, [x2, #-1]
    //     0x832694: ubfx            x0, x0, #0xc, #0x14
    //     0x832698: lsl             x0, x0, #1
    // 0x83269c: cmp             w0, #0xbc
    // 0x8326a0: b.ne            #0x8326b0
    // 0x8326a4: ArrayLoad: r0 = r2[r4]  ; TypedUnsigned_1
    //     0x8326a4: add             x16, x2, x4
    //     0x8326a8: ldrb            w0, [x16, #0xf]
    // 0x8326ac: b               #0x8326b8
    // 0x8326b0: add             x16, x2, x4, lsl #1
    // 0x8326b4: ldurh           w0, [x16, #0xf]
    // 0x8326b8: cmp             x0, #0x2f
    // 0x8326bc: b.ne            #0x8326c8
    // 0x8326c0: r0 = true
    //     0x8326c0: add             x0, NULL, #0x20  ; true
    // 0x8326c4: b               #0x8326dc
    // 0x8326c8: cmp             x0, #0x5c
    // 0x8326cc: r16 = true
    //     0x8326cc: add             x16, NULL, #0x20  ; true
    // 0x8326d0: r17 = false
    //     0x8326d0: add             x17, NULL, #0x30  ; false
    // 0x8326d4: csel            x1, x16, x17, eq
    // 0x8326d8: mov             x0, x1
    // 0x8326dc: eor             x1, x0, #0x10
    // 0x8326e0: mov             x8, x1
    // 0x8326e4: b               #0x832788
    // 0x8326e8: ldur            x2, [fp, #-0x40]
    // 0x8326ec: cmp             x3, #0x3b1
    // 0x8326f0: b.ne            #0x832768
    // 0x8326f4: LoadField: r0 = r2->field_7
    //     0x8326f4: ldur            w0, [x2, #7]
    // 0x8326f8: cbz             w0, #0x83275c
    // 0x8326fc: r1 = LoadInt32Instr(r0)
    //     0x8326fc: sbfx            x1, x0, #1, #0x1f
    // 0x832700: sub             x4, x1, #1
    // 0x832704: mov             x0, x1
    // 0x832708: mov             x1, x4
    // 0x83270c: cmp             x1, x0
    // 0x832710: b.hs            #0x8327f0
    // 0x832714: r0 = LoadTaggedClassIdMayBeSmiInstr(r2)
    //     0x832714: movz            x0, #0x78
    //     0x832718: tbz             w2, #0, #0x832728
    //     0x83271c: ldur            x0, [x2, #-1]
    //     0x832720: ubfx            x0, x0, #0xc, #0x14
    //     0x832724: lsl             x0, x0, #1
    // 0x832728: cmp             w0, #0xbc
    // 0x83272c: b.ne            #0x83273c
    // 0x832730: ArrayLoad: r0 = r2[r4]  ; TypedUnsigned_1
    //     0x832730: add             x16, x2, x4
    //     0x832734: ldrb            w0, [x16, #0xf]
    // 0x832738: b               #0x832744
    // 0x83273c: add             x16, x2, x4, lsl #1
    // 0x832740: ldurh           w0, [x16, #0xf]
    // 0x832744: cmp             x0, #0x2f
    // 0x832748: r16 = true
    //     0x832748: add             x16, NULL, #0x20  ; true
    // 0x83274c: r17 = false
    //     0x83274c: add             x17, NULL, #0x30  ; false
    // 0x832750: csel            x1, x16, x17, ne
    // 0x832754: mov             x0, x1
    // 0x832758: b               #0x832760
    // 0x83275c: r0 = false
    //     0x83275c: add             x0, NULL, #0x30  ; false
    // 0x832760: mov             x8, x0
    // 0x832764: b               #0x832788
    // 0x832768: ldur            x4, [fp, #-0x28]
    // 0x83276c: r0 = LoadClassIdInstr(r4)
    //     0x83276c: ldur            x0, [x4, #-1]
    //     0x832770: ubfx            x0, x0, #0xc, #0x14
    // 0x832774: mov             x1, x4
    // 0x832778: r0 = GDT[cid_x0 + -0xff1]()
    //     0x832778: sub             lr, x0, #0xff1
    //     0x83277c: ldr             lr, [x21, lr, lsl #3]
    //     0x832780: blr             lr
    // 0x832784: mov             x8, x0
    // 0x832788: ldur            x7, [fp, #-8]
    // 0x83278c: ldur            x4, [fp, #-0x28]
    // 0x832790: ldur            x2, [fp, #-0x38]
    // 0x832794: ldur            x3, [fp, #-0x30]
    // 0x832798: ldur            x5, [fp, #-0x20]
    // 0x83279c: b               #0x831dfc
    // 0x8327a0: ldur            x16, [fp, #-0x18]
    // 0x8327a4: str             x16, [SP]
    // 0x8327a8: r0 = toString()
    //     0x8327a8: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x8327ac: LeaveFrame
    //     0x8327ac: mov             SP, fp
    //     0x8327b0: ldp             fp, lr, [SP], #0x10
    // 0x8327b4: ret
    //     0x8327b4: ret             
    // 0x8327b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8327b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8327bc: b               #0x831d6c
    // 0x8327c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8327c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8327c4: b               #0x831e14
    // 0x8327c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8327c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8327cc: b               #0x831e20
    // 0x8327d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8327f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8327f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ isAbsolute(/* No info */) {
    // ** addr: 0x8327f4, size: 0xd8
    // 0x8327f4: EnterFrame
    //     0x8327f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8327f8: mov             fp, SP
    // 0x8327fc: CheckStackOverflow
    //     0x8327fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x832800: cmp             SP, x16
    //     0x832804: b.ls            #0x8328c0
    // 0x832808: LoadField: r0 = r1->field_7
    //     0x832808: ldur            w0, [x1, #7]
    // 0x83280c: DecompressPointer r0
    //     0x83280c: add             x0, x0, HEAP, lsl #32
    // 0x832810: r1 = LoadClassIdInstr(r0)
    //     0x832810: ldur            x1, [x0, #-1]
    //     0x832814: ubfx            x1, x1, #0xc, #0x14
    // 0x832818: cmp             x1, #0x3b1
    // 0x83281c: b.ne            #0x83287c
    // 0x832820: LoadField: r0 = r2->field_7
    //     0x832820: ldur            w0, [x2, #7]
    // 0x832824: cbz             w0, #0x832874
    // 0x832828: r1 = LoadInt32Instr(r0)
    //     0x832828: sbfx            x1, x0, #1, #0x1f
    // 0x83282c: mov             x0, x1
    // 0x832830: r1 = 0
    //     0x832830: movz            x1, #0
    // 0x832834: cmp             x1, x0
    // 0x832838: b.hs            #0x8328c8
    // 0x83283c: r0 = LoadClassIdInstr(r2)
    //     0x83283c: ldur            x0, [x2, #-1]
    //     0x832840: ubfx            x0, x0, #0xc, #0x14
    // 0x832844: lsl             x0, x0, #1
    // 0x832848: cmp             w0, #0xbc
    // 0x83284c: b.ne            #0x832860
    // 0x832850: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x832850: ldrb            w0, [x2, #0xf]
    // 0x832854: cmp             x0, #0x2f
    // 0x832858: b.ne            #0x832874
    // 0x83285c: b               #0x83286c
    // 0x832860: ldurh           w0, [x2, #0xf]
    // 0x832864: cmp             x0, #0x2f
    // 0x832868: b.ne            #0x832874
    // 0x83286c: r1 = 1
    //     0x83286c: movz            x1, #0x1
    // 0x832870: b               #0x8328a4
    // 0x832874: r1 = 0
    //     0x832874: movz            x1, #0
    // 0x832878: b               #0x8328a4
    // 0x83287c: r1 = LoadClassIdInstr(r0)
    //     0x83287c: ldur            x1, [x0, #-1]
    //     0x832880: ubfx            x1, x1, #0xc, #0x14
    // 0x832884: mov             x16, x0
    // 0x832888: mov             x0, x1
    // 0x83288c: mov             x1, x16
    // 0x832890: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832890: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832894: r0 = GDT[cid_x0 + -0xffd]()
    //     0x832894: sub             lr, x0, #0xffd
    //     0x832898: ldr             lr, [x21, lr, lsl #3]
    //     0x83289c: blr             lr
    // 0x8328a0: mov             x1, x0
    // 0x8328a4: cmp             x1, #0
    // 0x8328a8: r16 = true
    //     0x8328a8: add             x16, NULL, #0x20  ; true
    // 0x8328ac: r17 = false
    //     0x8328ac: add             x17, NULL, #0x30  ; false
    // 0x8328b0: csel            x0, x16, x17, gt
    // 0x8328b4: LeaveFrame
    //     0x8328b4: mov             SP, fp
    //     0x8328b8: ldp             fp, lr, [SP], #0x10
    // 0x8328bc: ret
    //     0x8328bc: ret             
    // 0x8328c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8328c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8328c4: b               #0x832808
    // 0x8328c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8328c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parse(/* No info */) {
    // ** addr: 0x8328cc, size: 0x38
    // 0x8328cc: EnterFrame
    //     0x8328cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8328d0: mov             fp, SP
    // 0x8328d4: CheckStackOverflow
    //     0x8328d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8328d8: cmp             SP, x16
    //     0x8328dc: b.ls            #0x8328fc
    // 0x8328e0: LoadField: r3 = r1->field_7
    //     0x8328e0: ldur            w3, [x1, #7]
    // 0x8328e4: DecompressPointer r3
    //     0x8328e4: add             x3, x3, HEAP, lsl #32
    // 0x8328e8: r1 = Null
    //     0x8328e8: mov             x1, NULL
    // 0x8328ec: r0 = ParsedPath.parse()
    //     0x8328ec: bl              #0x8329d4  ; [package:path/src/parsed_path.dart] ParsedPath::ParsedPath.parse
    // 0x8328f0: LeaveFrame
    //     0x8328f0: mov             SP, fp
    //     0x8328f4: ldp             fp, lr, [SP], #0x10
    // 0x8328f8: ret
    //     0x8328f8: ret             
    // 0x8328fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8328fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x832900: b               #0x8328e0
  }
  _ isRootRelative(/* No info */) {
    // ** addr: 0x832904, size: 0xd0
    // 0x832904: EnterFrame
    //     0x832904: stp             fp, lr, [SP, #-0x10]!
    //     0x832908: mov             fp, SP
    // 0x83290c: CheckStackOverflow
    //     0x83290c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x832910: cmp             SP, x16
    //     0x832914: b.ls            #0x8329c8
    // 0x832918: LoadField: r0 = r1->field_7
    //     0x832918: ldur            w0, [x1, #7]
    // 0x83291c: DecompressPointer r0
    //     0x83291c: add             x0, x0, HEAP, lsl #32
    // 0x832920: r1 = LoadClassIdInstr(r0)
    //     0x832920: ldur            x1, [x0, #-1]
    //     0x832924: ubfx            x1, x1, #0xc, #0x14
    // 0x832928: cmp             x1, #0x3af
    // 0x83292c: b.ne            #0x832954
    // 0x832930: mov             x1, x0
    // 0x832934: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832934: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832938: r0 = rootLength()
    //     0x832938: bl              #0xe8d7a4  ; [package:path/src/style/windows.dart] WindowsStyle::rootLength
    // 0x83293c: cmp             x0, #1
    // 0x832940: r16 = true
    //     0x832940: add             x16, NULL, #0x20  ; true
    // 0x832944: r17 = false
    //     0x832944: add             x17, NULL, #0x30  ; false
    // 0x832948: csel            x3, x16, x17, eq
    // 0x83294c: mov             x0, x3
    // 0x832950: b               #0x8329bc
    // 0x832954: cmp             x1, #0x3b0
    // 0x832958: b.ne            #0x8329b8
    // 0x83295c: LoadField: r3 = r2->field_7
    //     0x83295c: ldur            w3, [x2, #7]
    // 0x832960: cbz             w3, #0x8329ac
    // 0x832964: r0 = LoadInt32Instr(r3)
    //     0x832964: sbfx            x0, x3, #1, #0x1f
    // 0x832968: r1 = 0
    //     0x832968: movz            x1, #0
    // 0x83296c: cmp             x1, x0
    // 0x832970: b.hs            #0x8329d0
    // 0x832974: r1 = LoadClassIdInstr(r2)
    //     0x832974: ldur            x1, [x2, #-1]
    //     0x832978: ubfx            x1, x1, #0xc, #0x14
    // 0x83297c: lsl             x1, x1, #1
    // 0x832980: cmp             w1, #0xbc
    // 0x832984: b.ne            #0x832990
    // 0x832988: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x832988: ldrb            w1, [x2, #0xf]
    // 0x83298c: b               #0x832994
    // 0x832990: ldurh           w1, [x2, #0xf]
    // 0x832994: cmp             x1, #0x2f
    // 0x832998: r16 = true
    //     0x832998: add             x16, NULL, #0x20  ; true
    // 0x83299c: r17 = false
    //     0x83299c: add             x17, NULL, #0x30  ; false
    // 0x8329a0: csel            x2, x16, x17, eq
    // 0x8329a4: mov             x1, x2
    // 0x8329a8: b               #0x8329b0
    // 0x8329ac: r1 = false
    //     0x8329ac: add             x1, NULL, #0x30  ; false
    // 0x8329b0: mov             x0, x1
    // 0x8329b4: b               #0x8329bc
    // 0x8329b8: r0 = false
    //     0x8329b8: add             x0, NULL, #0x30  ; false
    // 0x8329bc: LeaveFrame
    //     0x8329bc: mov             SP, fp
    //     0x8329c0: ldp             fp, lr, [SP], #0x10
    // 0x8329c4: ret
    //     0x8329c4: ret             
    // 0x8329c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8329c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8329cc: b               #0x832918
    // 0x8329d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8329d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x833178, size: 0x58
    // 0x833178: EnterFrame
    //     0x833178: stp             fp, lr, [SP, #-0x10]!
    //     0x83317c: mov             fp, SP
    // 0x833180: AllocStack(0x10)
    //     0x833180: sub             SP, SP, #0x10
    // 0x833184: CheckStackOverflow
    //     0x833184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833188: cmp             SP, x16
    //     0x83318c: b.ls            #0x8331c8
    // 0x833190: ldr             x0, [fp, #0x10]
    // 0x833194: r1 = LoadClassIdInstr(r0)
    //     0x833194: ldur            x1, [x0, #-1]
    //     0x833198: ubfx            x1, x1, #0xc, #0x14
    // 0x83319c: r16 = ""
    //     0x83319c: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8331a0: stp             x16, x0, [SP]
    // 0x8331a4: mov             x0, x1
    // 0x8331a8: mov             lr, x0
    // 0x8331ac: ldr             lr, [x21, lr, lsl #3]
    // 0x8331b0: blr             lr
    // 0x8331b4: eor             x1, x0, #0x10
    // 0x8331b8: mov             x0, x1
    // 0x8331bc: LeaveFrame
    //     0x8331bc: mov             SP, fp
    //     0x8331c0: ldp             fp, lr, [SP], #0x10
    // 0x8331c4: ret
    //     0x8331c4: ret             
    // 0x8331c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8331c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8331cc: b               #0x833190
  }
  factory _ Context(/* No info */) {
    // ** addr: 0xaaa1c4, size: 0x84
    // 0xaaa1c4: EnterFrame
    //     0xaaa1c4: stp             fp, lr, [SP, #-0x10]!
    //     0xaaa1c8: mov             fp, SP
    // 0xaaa1cc: AllocStack(0x10)
    //     0xaaa1cc: sub             SP, SP, #0x10
    // 0xaaa1d0: SetupParameters({dynamic current})
    //     0xaaa1d0: ldur            w0, [x4, #0x1f]
    //     0xaaa1d4: add             x0, x0, HEAP, lsl #32
    //     0xaaa1d8: add             x16, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0xaaa1dc: ldr             x16, [x16, #0x110]
    //     0xaaa1e0: cmp             w0, w16
    //     0xaaa1e4: b.eq            #0xaaa1e8
    // 0xaaa1e8: CheckStackOverflow
    //     0xaaa1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaa1ec: cmp             SP, x16
    //     0xaaa1f0: b.ls            #0xaaa240
    // 0xaaa1f4: r0 = current()
    //     0xaaa1f4: bl              #0xaaa248  ; [package:path/path.dart] ::current
    // 0xaaa1f8: stur            x0, [fp, #-8]
    // 0xaaa1fc: r0 = InitLateStaticField(0x16d0) // [package:path/src/style.dart] Style::platform
    //     0xaaa1fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaa200: ldr             x0, [x0, #0x2da0]
    //     0xaaa204: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaaa208: cmp             w0, w16
    //     0xaaa20c: b.ne            #0xaaa21c
    //     0xaaa210: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc30] Field <Style.platform>: static late final (offset: 0x16d0)
    //     0xaaa214: ldr             x2, [x2, #0xc30]
    //     0xaaa218: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xaaa21c: stur            x0, [fp, #-0x10]
    // 0xaaa220: r0 = Context()
    //     0xaaa220: bl              #0x8334e8  ; AllocateContextStub -> Context (size=0x10)
    // 0xaaa224: ldur            x1, [fp, #-0x10]
    // 0xaaa228: StoreField: r0->field_7 = r1
    //     0xaaa228: stur            w1, [x0, #7]
    // 0xaaa22c: ldur            x1, [fp, #-8]
    // 0xaaa230: StoreField: r0->field_b = r1
    //     0xaaa230: stur            w1, [x0, #0xb]
    // 0xaaa234: LeaveFrame
    //     0xaaa234: mov             SP, fp
    //     0xaaa238: ldp             fp, lr, [SP], #0x10
    // 0xaaa23c: ret
    //     0xaaa23c: ret             
    // 0xaaa240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaa240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaa244: b               #0xaaa1f4
  }
  _ absolute(/* No info */) {
    // ** addr: 0xab24e4, size: 0x11c
    // 0xab24e4: EnterFrame
    //     0xab24e4: stp             fp, lr, [SP, #-0x10]!
    //     0xab24e8: mov             fp, SP
    // 0xab24ec: AllocStack(0x18)
    //     0xab24ec: sub             SP, SP, #0x18
    // 0xab24f0: r0 = 30
    //     0xab24f0: movz            x0, #0x1e
    // 0xab24f4: mov             x4, x1
    // 0xab24f8: mov             x3, x2
    // 0xab24fc: stur            x1, [fp, #-8]
    // 0xab2500: stur            x2, [fp, #-0x10]
    // 0xab2504: CheckStackOverflow
    //     0xab2504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2508: cmp             SP, x16
    //     0xab250c: b.ls            #0xab25f8
    // 0xab2510: mov             x2, x0
    // 0xab2514: r1 = <String?>
    //     0xab2514: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xab2518: r0 = AllocateArray()
    //     0xab2518: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab251c: ldur            x2, [fp, #-0x10]
    // 0xab2520: stur            x0, [fp, #-0x18]
    // 0xab2524: StoreField: r0->field_f = r2
    //     0xab2524: stur            w2, [x0, #0xf]
    // 0xab2528: StoreField: r0->field_13 = rNULL
    //     0xab2528: stur            NULL, [x0, #0x13]
    // 0xab252c: ArrayStore: r0[0] = rNULL  ; List_4
    //     0xab252c: stur            NULL, [x0, #0x17]
    // 0xab2530: StoreField: r0->field_1b = rNULL
    //     0xab2530: stur            NULL, [x0, #0x1b]
    // 0xab2534: StoreField: r0->field_1f = rNULL
    //     0xab2534: stur            NULL, [x0, #0x1f]
    // 0xab2538: StoreField: r0->field_23 = rNULL
    //     0xab2538: stur            NULL, [x0, #0x23]
    // 0xab253c: StoreField: r0->field_27 = rNULL
    //     0xab253c: stur            NULL, [x0, #0x27]
    // 0xab2540: StoreField: r0->field_2b = rNULL
    //     0xab2540: stur            NULL, [x0, #0x2b]
    // 0xab2544: StoreField: r0->field_2f = rNULL
    //     0xab2544: stur            NULL, [x0, #0x2f]
    // 0xab2548: StoreField: r0->field_33 = rNULL
    //     0xab2548: stur            NULL, [x0, #0x33]
    // 0xab254c: StoreField: r0->field_37 = rNULL
    //     0xab254c: stur            NULL, [x0, #0x37]
    // 0xab2550: StoreField: r0->field_3b = rNULL
    //     0xab2550: stur            NULL, [x0, #0x3b]
    // 0xab2554: StoreField: r0->field_3f = rNULL
    //     0xab2554: stur            NULL, [x0, #0x3f]
    // 0xab2558: StoreField: r0->field_43 = rNULL
    //     0xab2558: stur            NULL, [x0, #0x43]
    // 0xab255c: StoreField: r0->field_47 = rNULL
    //     0xab255c: stur            NULL, [x0, #0x47]
    // 0xab2560: r1 = <String?>
    //     0xab2560: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xab2564: r0 = AllocateGrowableArray()
    //     0xab2564: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xab2568: mov             x1, x0
    // 0xab256c: ldur            x0, [fp, #-0x18]
    // 0xab2570: StoreField: r1->field_f = r0
    //     0xab2570: stur            w0, [x1, #0xf]
    // 0xab2574: r0 = 30
    //     0xab2574: movz            x0, #0x1e
    // 0xab2578: StoreField: r1->field_b = r0
    //     0xab2578: stur            w0, [x1, #0xb]
    // 0xab257c: mov             x2, x1
    // 0xab2580: r1 = "absolute"
    //     0xab2580: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bb78] "absolute"
    //     0xab2584: ldr             x1, [x1, #0xb78]
    // 0xab2588: r0 = _validateArgList()
    //     0xab2588: bl              #0x8331d0  ; [package:path/src/context.dart] ::_validateArgList
    // 0xab258c: ldur            x1, [fp, #-8]
    // 0xab2590: ldur            x2, [fp, #-0x10]
    // 0xab2594: r0 = isAbsolute()
    //     0xab2594: bl              #0x8327f4  ; [package:path/src/context.dart] Context::isAbsolute
    // 0xab2598: tbnz            w0, #4, #0xab25bc
    // 0xab259c: ldur            x1, [fp, #-8]
    // 0xab25a0: ldur            x2, [fp, #-0x10]
    // 0xab25a4: r0 = isRootRelative()
    //     0xab25a4: bl              #0x832904  ; [package:path/src/context.dart] Context::isRootRelative
    // 0xab25a8: tbz             w0, #4, #0xab25bc
    // 0xab25ac: ldur            x0, [fp, #-0x10]
    // 0xab25b0: LeaveFrame
    //     0xab25b0: mov             SP, fp
    //     0xab25b4: ldp             fp, lr, [SP], #0x10
    // 0xab25b8: ret
    //     0xab25b8: ret             
    // 0xab25bc: ldur            x1, [fp, #-8]
    // 0xab25c0: LoadField: r0 = r1->field_b
    //     0xab25c0: ldur            w0, [x1, #0xb]
    // 0xab25c4: DecompressPointer r0
    //     0xab25c4: add             x0, x0, HEAP, lsl #32
    // 0xab25c8: cmp             w0, NULL
    // 0xab25cc: b.ne            #0xab25dc
    // 0xab25d0: r0 = current()
    //     0xab25d0: bl              #0xaaa248  ; [package:path/path.dart] ::current
    // 0xab25d4: mov             x2, x0
    // 0xab25d8: b               #0xab25e0
    // 0xab25dc: mov             x2, x0
    // 0xab25e0: ldur            x1, [fp, #-8]
    // 0xab25e4: ldur            x3, [fp, #-0x10]
    // 0xab25e8: r0 = join()
    //     0xab25e8: bl              #0x831c54  ; [package:path/src/context.dart] Context::join
    // 0xab25ec: LeaveFrame
    //     0xab25ec: mov             SP, fp
    //     0xab25f0: ldp             fp, lr, [SP], #0x10
    // 0xab25f4: ret
    //     0xab25f4: ret             
    // 0xab25f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab25f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab25fc: b               #0xab2510
  }
  get _ current(/* No info */) {
    // ** addr: 0xab2600, size: 0x3c
    // 0xab2600: EnterFrame
    //     0xab2600: stp             fp, lr, [SP, #-0x10]!
    //     0xab2604: mov             fp, SP
    // 0xab2608: CheckStackOverflow
    //     0xab2608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab260c: cmp             SP, x16
    //     0xab2610: b.ls            #0xab2634
    // 0xab2614: LoadField: r0 = r1->field_b
    //     0xab2614: ldur            w0, [x1, #0xb]
    // 0xab2618: DecompressPointer r0
    //     0xab2618: add             x0, x0, HEAP, lsl #32
    // 0xab261c: cmp             w0, NULL
    // 0xab2620: b.ne            #0xab2628
    // 0xab2624: r0 = current()
    //     0xab2624: bl              #0xaaa248  ; [package:path/path.dart] ::current
    // 0xab2628: LeaveFrame
    //     0xab2628: mov             SP, fp
    //     0xab262c: ldp             fp, lr, [SP], #0x10
    // 0xab2630: ret
    //     0xab2630: ret             
    // 0xab2634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2638: b               #0xab2614
  }
  _ normalize(/* No info */) {
    // ** addr: 0xab269c, size: 0x80
    // 0xab269c: EnterFrame
    //     0xab269c: stp             fp, lr, [SP, #-0x10]!
    //     0xab26a0: mov             fp, SP
    // 0xab26a4: AllocStack(0x18)
    //     0xab26a4: sub             SP, SP, #0x18
    // 0xab26a8: SetupParameters(Context this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xab26a8: mov             x3, x1
    //     0xab26ac: mov             x0, x2
    //     0xab26b0: stur            x1, [fp, #-8]
    //     0xab26b4: stur            x2, [fp, #-0x10]
    // 0xab26b8: CheckStackOverflow
    //     0xab26b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab26bc: cmp             SP, x16
    //     0xab26c0: b.ls            #0xab2714
    // 0xab26c4: mov             x1, x3
    // 0xab26c8: mov             x2, x0
    // 0xab26cc: r0 = _needsNormalization()
    //     0xab26cc: bl              #0xab2ea0  ; [package:path/src/context.dart] Context::_needsNormalization
    // 0xab26d0: tbz             w0, #4, #0xab26e4
    // 0xab26d4: ldur            x0, [fp, #-0x10]
    // 0xab26d8: LeaveFrame
    //     0xab26d8: mov             SP, fp
    //     0xab26dc: ldp             fp, lr, [SP], #0x10
    // 0xab26e0: ret
    //     0xab26e0: ret             
    // 0xab26e4: ldur            x1, [fp, #-8]
    // 0xab26e8: ldur            x2, [fp, #-0x10]
    // 0xab26ec: r0 = _parse()
    //     0xab26ec: bl              #0x8328cc  ; [package:path/src/context.dart] Context::_parse
    // 0xab26f0: mov             x1, x0
    // 0xab26f4: stur            x0, [fp, #-8]
    // 0xab26f8: r0 = normalize()
    //     0xab26f8: bl              #0xab271c  ; [package:path/src/parsed_path.dart] ParsedPath::normalize
    // 0xab26fc: ldur            x16, [fp, #-8]
    // 0xab2700: str             x16, [SP]
    // 0xab2704: r0 = toString()
    //     0xab2704: bl              #0xc336cc  ; [package:path/src/parsed_path.dart] ParsedPath::toString
    // 0xab2708: LeaveFrame
    //     0xab2708: mov             SP, fp
    //     0xab270c: ldp             fp, lr, [SP], #0x10
    // 0xab2710: ret
    //     0xab2710: ret             
    // 0xab2714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2718: b               #0xab26c4
  }
  _ _needsNormalization(/* No info */) {
    // ** addr: 0xab2ea0, size: 0x4c8
    // 0xab2ea0: EnterFrame
    //     0xab2ea0: stp             fp, lr, [SP, #-0x10]!
    //     0xab2ea4: mov             fp, SP
    // 0xab2ea8: AllocStack(0x48)
    //     0xab2ea8: sub             SP, SP, #0x48
    // 0xab2eac: SetupParameters(dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xab2eac: mov             x3, x2
    //     0xab2eb0: stur            x2, [fp, #-0x18]
    // 0xab2eb4: CheckStackOverflow
    //     0xab2eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2eb8: cmp             SP, x16
    //     0xab2ebc: b.ls            #0xab3344
    // 0xab2ec0: LoadField: r4 = r1->field_7
    //     0xab2ec0: ldur            w4, [x1, #7]
    // 0xab2ec4: DecompressPointer r4
    //     0xab2ec4: add             x4, x4, HEAP, lsl #32
    // 0xab2ec8: stur            x4, [fp, #-0x10]
    // 0xab2ecc: r5 = LoadClassIdInstr(r4)
    //     0xab2ecc: ldur            x5, [x4, #-1]
    //     0xab2ed0: ubfx            x5, x5, #0xc, #0x14
    // 0xab2ed4: stur            x5, [fp, #-8]
    // 0xab2ed8: cmp             x5, #0x3b1
    // 0xab2edc: b.ne            #0xab2f3c
    // 0xab2ee0: LoadField: r0 = r3->field_7
    //     0xab2ee0: ldur            w0, [x3, #7]
    // 0xab2ee4: cbz             w0, #0xab2f34
    // 0xab2ee8: r1 = LoadInt32Instr(r0)
    //     0xab2ee8: sbfx            x1, x0, #1, #0x1f
    // 0xab2eec: mov             x0, x1
    // 0xab2ef0: r1 = 0
    //     0xab2ef0: movz            x1, #0
    // 0xab2ef4: cmp             x1, x0
    // 0xab2ef8: b.hs            #0xab334c
    // 0xab2efc: r0 = LoadClassIdInstr(r3)
    //     0xab2efc: ldur            x0, [x3, #-1]
    //     0xab2f00: ubfx            x0, x0, #0xc, #0x14
    // 0xab2f04: lsl             x0, x0, #1
    // 0xab2f08: cmp             w0, #0xbc
    // 0xab2f0c: b.ne            #0xab2f20
    // 0xab2f10: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0xab2f10: ldrb            w0, [x3, #0xf]
    // 0xab2f14: cmp             x0, #0x2f
    // 0xab2f18: b.ne            #0xab2f34
    // 0xab2f1c: b               #0xab2f2c
    // 0xab2f20: ldurh           w0, [x3, #0xf]
    // 0xab2f24: cmp             x0, #0x2f
    // 0xab2f28: b.ne            #0xab2f34
    // 0xab2f2c: r0 = 1
    //     0xab2f2c: movz            x0, #0x1
    // 0xab2f30: b               #0xab2f5c
    // 0xab2f34: r0 = 0
    //     0xab2f34: movz            x0, #0
    // 0xab2f38: b               #0xab2f5c
    // 0xab2f3c: r0 = LoadClassIdInstr(r4)
    //     0xab2f3c: ldur            x0, [x4, #-1]
    //     0xab2f40: ubfx            x0, x0, #0xc, #0x14
    // 0xab2f44: mov             x1, x4
    // 0xab2f48: mov             x2, x3
    // 0xab2f4c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab2f4c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab2f50: r0 = GDT[cid_x0 + -0xffd]()
    //     0xab2f50: sub             lr, x0, #0xffd
    //     0xab2f54: ldr             lr, [x21, lr, lsl #3]
    //     0xab2f58: blr             lr
    // 0xab2f5c: stur            x0, [fp, #-0x20]
    // 0xab2f60: cbz             x0, #0xab3034
    // 0xab2f64: ldur            x1, [fp, #-0x10]
    // 0xab2f68: r0 = InitLateStaticField(0x16c8) // [package:path/src/style.dart] Style::windows
    //     0xab2f68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab2f6c: ldr             x0, [x0, #0x2d90]
    //     0xab2f70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab2f74: cmp             w0, w16
    //     0xab2f78: b.ne            #0xab2f88
    //     0xab2f7c: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc58] Field <Style.windows>: static late final (offset: 0x16c8)
    //     0xab2f80: ldr             x2, [x2, #0xc58]
    //     0xab2f84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab2f88: ldur            x2, [fp, #-0x10]
    // 0xab2f8c: cmp             w2, w0
    // 0xab2f90: b.ne            #0xab3020
    // 0xab2f94: ldur            x3, [fp, #-0x18]
    // 0xab2f98: LoadField: r0 = r3->field_7
    //     0xab2f98: ldur            w0, [x3, #7]
    // 0xab2f9c: r4 = LoadInt32Instr(r0)
    //     0xab2f9c: sbfx            x4, x0, #1, #0x1f
    // 0xab2fa0: r5 = LoadClassIdInstr(r3)
    //     0xab2fa0: ldur            x5, [x3, #-1]
    //     0xab2fa4: ubfx            x5, x5, #0xc, #0x14
    // 0xab2fa8: lsl             x5, x5, #1
    // 0xab2fac: ldur            x6, [fp, #-0x20]
    // 0xab2fb0: r7 = 0
    //     0xab2fb0: movz            x7, #0
    // 0xab2fb4: CheckStackOverflow
    //     0xab2fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2fb8: cmp             SP, x16
    //     0xab2fbc: b.ls            #0xab3350
    // 0xab2fc0: cmp             x7, x6
    // 0xab2fc4: b.ge            #0xab3028
    // 0xab2fc8: mov             x0, x4
    // 0xab2fcc: mov             x1, x7
    // 0xab2fd0: cmp             x1, x0
    // 0xab2fd4: b.hs            #0xab3358
    // 0xab2fd8: cmp             w5, #0xbc
    // 0xab2fdc: b.ne            #0xab2ff4
    // 0xab2fe0: ArrayLoad: r0 = r3[r7]  ; TypedUnsigned_1
    //     0xab2fe0: add             x16, x3, x7
    //     0xab2fe4: ldrb            w0, [x16, #0xf]
    // 0xab2fe8: cmp             x0, #0x2f
    // 0xab2fec: b.ne            #0xab3004
    // 0xab2ff0: b               #0xab3010
    // 0xab2ff4: add             x16, x3, x7, lsl #1
    // 0xab2ff8: ldurh           w0, [x16, #0xf]
    // 0xab2ffc: cmp             x0, #0x2f
    // 0xab3000: b.eq            #0xab3010
    // 0xab3004: add             x0, x7, #1
    // 0xab3008: mov             x7, x0
    // 0xab300c: b               #0xab2fb4
    // 0xab3010: r0 = true
    //     0xab3010: add             x0, NULL, #0x20  ; true
    // 0xab3014: LeaveFrame
    //     0xab3014: mov             SP, fp
    //     0xab3018: ldp             fp, lr, [SP], #0x10
    // 0xab301c: ret
    //     0xab301c: ret             
    // 0xab3020: ldur            x3, [fp, #-0x18]
    // 0xab3024: ldur            x6, [fp, #-0x20]
    // 0xab3028: mov             x1, x6
    // 0xab302c: r0 = 94
    //     0xab302c: movz            x0, #0x5e
    // 0xab3030: b               #0xab3044
    // 0xab3034: ldur            x3, [fp, #-0x18]
    // 0xab3038: ldur            x2, [fp, #-0x10]
    // 0xab303c: r1 = 0
    //     0xab303c: movz            x1, #0
    // 0xab3040: r0 = Null
    //     0xab3040: mov             x0, NULL
    // 0xab3044: LoadField: r4 = r3->field_7
    //     0xab3044: ldur            w4, [x3, #7]
    // 0xab3048: r5 = LoadInt32Instr(r4)
    //     0xab3048: sbfx            x5, x4, #1, #0x1f
    // 0xab304c: stur            x5, [fp, #-0x48]
    // 0xab3050: r4 = LoadClassIdInstr(r3)
    //     0xab3050: ldur            x4, [x3, #-1]
    //     0xab3054: ubfx            x4, x4, #0xc, #0x14
    // 0xab3058: lsl             x4, x4, #1
    // 0xab305c: stur            x4, [fp, #-0x40]
    // 0xab3060: mov             x8, x0
    // 0xab3064: mov             x7, x1
    // 0xab3068: ldur            x6, [fp, #-8]
    // 0xab306c: r9 = Null
    //     0xab306c: mov             x9, NULL
    // 0xab3070: stur            x9, [fp, #-0x28]
    // 0xab3074: stur            x8, [fp, #-0x30]
    // 0xab3078: stur            x7, [fp, #-0x38]
    // 0xab307c: CheckStackOverflow
    //     0xab307c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3080: cmp             SP, x16
    //     0xab3084: b.ls            #0xab335c
    // 0xab3088: cmp             x7, x5
    // 0xab308c: b.ge            #0xab3260
    // 0xab3090: mov             x0, x5
    // 0xab3094: mov             x1, x7
    // 0xab3098: cmp             x1, x0
    // 0xab309c: b.hs            #0xab3364
    // 0xab30a0: cmp             w4, #0xbc
    // 0xab30a4: b.ne            #0xab30b4
    // 0xab30a8: ArrayLoad: r0 = r3[r7]  ; TypedUnsigned_1
    //     0xab30a8: add             x16, x3, x7
    //     0xab30ac: ldrb            w0, [x16, #0xf]
    // 0xab30b0: b               #0xab30bc
    // 0xab30b4: add             x16, x3, x7, lsl #1
    // 0xab30b8: ldurh           w0, [x16, #0xf]
    // 0xab30bc: stur            x0, [fp, #-0x20]
    // 0xab30c0: cmp             x6, #0x3af
    // 0xab30c4: b.ne            #0xab30ec
    // 0xab30c8: cmp             x0, #0x2f
    // 0xab30cc: b.eq            #0xab3118
    // 0xab30d0: cmp             x0, #0x5c
    // 0xab30d4: b.eq            #0xab3118
    // 0xab30d8: mov             x1, x2
    // 0xab30dc: mov             x9, x8
    // 0xab30e0: mov             x3, x6
    // 0xab30e4: mov             x2, x0
    // 0xab30e8: b               #0xab323c
    // 0xab30ec: cmp             x6, #0x3b0
    // 0xab30f0: b.ne            #0xab3110
    // 0xab30f4: cmp             x0, #0x2f
    // 0xab30f8: b.eq            #0xab3118
    // 0xab30fc: mov             x1, x2
    // 0xab3100: mov             x9, x8
    // 0xab3104: mov             x3, x6
    // 0xab3108: mov             x2, x0
    // 0xab310c: b               #0xab323c
    // 0xab3110: cmp             x0, #0x2f
    // 0xab3114: b.ne            #0xab322c
    // 0xab3118: r0 = InitLateStaticField(0x16c8) // [package:path/src/style.dart] Style::windows
    //     0xab3118: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab311c: ldr             x0, [x0, #0x2d90]
    //     0xab3120: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab3124: cmp             w0, w16
    //     0xab3128: b.ne            #0xab3138
    //     0xab312c: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc58] Field <Style.windows>: static late final (offset: 0x16c8)
    //     0xab3130: ldr             x2, [x2, #0xc58]
    //     0xab3134: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab3138: ldur            x1, [fp, #-0x10]
    // 0xab313c: cmp             w1, w0
    // 0xab3140: b.ne            #0xab3160
    // 0xab3144: ldur            x2, [fp, #-0x20]
    // 0xab3148: cmp             x2, #0x2f
    // 0xab314c: b.ne            #0xab3164
    // 0xab3150: r0 = true
    //     0xab3150: add             x0, NULL, #0x20  ; true
    // 0xab3154: LeaveFrame
    //     0xab3154: mov             SP, fp
    //     0xab3158: ldp             fp, lr, [SP], #0x10
    // 0xab315c: ret
    //     0xab315c: ret             
    // 0xab3160: ldur            x2, [fp, #-0x20]
    // 0xab3164: ldur            x9, [fp, #-0x30]
    // 0xab3168: cmp             w9, NULL
    // 0xab316c: b.eq            #0xab31c0
    // 0xab3170: ldur            x3, [fp, #-8]
    // 0xab3174: cmp             x3, #0x3af
    // 0xab3178: b.ne            #0xab3194
    // 0xab317c: r4 = LoadInt32Instr(r9)
    //     0xab317c: sbfx            x4, x9, #1, #0x1f
    // 0xab3180: cmp             x4, #0x2f
    // 0xab3184: b.eq            #0xab31b0
    // 0xab3188: cmp             x4, #0x5c
    // 0xab318c: b.ne            #0xab31c4
    // 0xab3190: b               #0xab31b0
    // 0xab3194: cmp             x3, #0x3b0
    // 0xab3198: b.ne            #0xab31a8
    // 0xab319c: cmp             w9, #0x5e
    // 0xab31a0: b.ne            #0xab31c4
    // 0xab31a4: b               #0xab31b0
    // 0xab31a8: cmp             w9, #0x5e
    // 0xab31ac: b.ne            #0xab31c4
    // 0xab31b0: r0 = true
    //     0xab31b0: add             x0, NULL, #0x20  ; true
    // 0xab31b4: LeaveFrame
    //     0xab31b4: mov             SP, fp
    //     0xab31b8: ldp             fp, lr, [SP], #0x10
    // 0xab31bc: ret
    //     0xab31bc: ret             
    // 0xab31c0: ldur            x3, [fp, #-8]
    // 0xab31c4: cmp             w9, #0x5c
    // 0xab31c8: b.ne            #0xab323c
    // 0xab31cc: ldur            x4, [fp, #-0x28]
    // 0xab31d0: cmp             w4, NULL
    // 0xab31d4: b.eq            #0xab321c
    // 0xab31d8: cmp             w4, #0x5c
    // 0xab31dc: b.eq            #0xab321c
    // 0xab31e0: cmp             x3, #0x3af
    // 0xab31e4: b.ne            #0xab3200
    // 0xab31e8: r5 = LoadInt32Instr(r4)
    //     0xab31e8: sbfx            x5, x4, #1, #0x1f
    // 0xab31ec: cmp             x5, #0x2f
    // 0xab31f0: b.eq            #0xab321c
    // 0xab31f4: cmp             x5, #0x5c
    // 0xab31f8: b.ne            #0xab323c
    // 0xab31fc: b               #0xab321c
    // 0xab3200: cmp             x3, #0x3b0
    // 0xab3204: b.ne            #0xab3214
    // 0xab3208: cmp             w4, #0x5e
    // 0xab320c: b.ne            #0xab323c
    // 0xab3210: b               #0xab321c
    // 0xab3214: cmp             w4, #0x5e
    // 0xab3218: b.ne            #0xab323c
    // 0xab321c: r0 = true
    //     0xab321c: add             x0, NULL, #0x20  ; true
    // 0xab3220: LeaveFrame
    //     0xab3220: mov             SP, fp
    //     0xab3224: ldp             fp, lr, [SP], #0x10
    // 0xab3228: ret
    //     0xab3228: ret             
    // 0xab322c: mov             x1, x2
    // 0xab3230: mov             x9, x8
    // 0xab3234: mov             x3, x6
    // 0xab3238: mov             x2, x0
    // 0xab323c: ldur            x5, [fp, #-0x38]
    // 0xab3240: add             x7, x5, #1
    // 0xab3244: lsl             x8, x2, #1
    // 0xab3248: mov             x6, x3
    // 0xab324c: ldur            x3, [fp, #-0x18]
    // 0xab3250: mov             x2, x1
    // 0xab3254: ldur            x4, [fp, #-0x40]
    // 0xab3258: ldur            x5, [fp, #-0x48]
    // 0xab325c: b               #0xab3070
    // 0xab3260: mov             x4, x9
    // 0xab3264: mov             x9, x8
    // 0xab3268: mov             x3, x6
    // 0xab326c: cmp             w9, NULL
    // 0xab3270: b.ne            #0xab3284
    // 0xab3274: r0 = true
    //     0xab3274: add             x0, NULL, #0x20  ; true
    // 0xab3278: LeaveFrame
    //     0xab3278: mov             SP, fp
    //     0xab327c: ldp             fp, lr, [SP], #0x10
    // 0xab3280: ret
    //     0xab3280: ret             
    // 0xab3284: cmp             x3, #0x3af
    // 0xab3288: b.ne            #0xab32a4
    // 0xab328c: r1 = LoadInt32Instr(r9)
    //     0xab328c: sbfx            x1, x9, #1, #0x1f
    // 0xab3290: cmp             x1, #0x2f
    // 0xab3294: b.eq            #0xab32c0
    // 0xab3298: cmp             x1, #0x5c
    // 0xab329c: b.ne            #0xab32d0
    // 0xab32a0: b               #0xab32c0
    // 0xab32a4: cmp             x3, #0x3b0
    // 0xab32a8: b.ne            #0xab32b8
    // 0xab32ac: cmp             w9, #0x5e
    // 0xab32b0: b.ne            #0xab32d0
    // 0xab32b4: b               #0xab32c0
    // 0xab32b8: cmp             w9, #0x5e
    // 0xab32bc: b.ne            #0xab32d0
    // 0xab32c0: r0 = true
    //     0xab32c0: add             x0, NULL, #0x20  ; true
    // 0xab32c4: LeaveFrame
    //     0xab32c4: mov             SP, fp
    //     0xab32c8: ldp             fp, lr, [SP], #0x10
    // 0xab32cc: ret
    //     0xab32cc: ret             
    // 0xab32d0: cmp             w9, #0x5c
    // 0xab32d4: b.ne            #0xab3334
    // 0xab32d8: cmp             w4, NULL
    // 0xab32dc: b.eq            #0xab3324
    // 0xab32e0: cmp             x3, #0x3af
    // 0xab32e4: b.ne            #0xab3300
    // 0xab32e8: r1 = LoadInt32Instr(r4)
    //     0xab32e8: sbfx            x1, x4, #1, #0x1f
    // 0xab32ec: cmp             x1, #0x2f
    // 0xab32f0: b.eq            #0xab3324
    // 0xab32f4: cmp             x1, #0x5c
    // 0xab32f8: b.ne            #0xab331c
    // 0xab32fc: b               #0xab3324
    // 0xab3300: cmp             x3, #0x3b0
    // 0xab3304: b.ne            #0xab3314
    // 0xab3308: cmp             w4, #0x5e
    // 0xab330c: b.ne            #0xab331c
    // 0xab3310: b               #0xab3324
    // 0xab3314: cmp             w4, #0x5e
    // 0xab3318: b.eq            #0xab3324
    // 0xab331c: cmp             w4, #0x5c
    // 0xab3320: b.ne            #0xab3334
    // 0xab3324: r0 = true
    //     0xab3324: add             x0, NULL, #0x20  ; true
    // 0xab3328: LeaveFrame
    //     0xab3328: mov             SP, fp
    //     0xab332c: ldp             fp, lr, [SP], #0x10
    // 0xab3330: ret
    //     0xab3330: ret             
    // 0xab3334: r0 = false
    //     0xab3334: add             x0, NULL, #0x30  ; false
    // 0xab3338: LeaveFrame
    //     0xab3338: mov             SP, fp
    //     0xab333c: ldp             fp, lr, [SP], #0x10
    // 0xab3340: ret
    //     0xab3340: ret             
    // 0xab3344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3348: b               #0xab2ec0
    // 0xab334c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab334c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab3350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3354: b               #0xab2fc0
    // 0xab3358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab3358: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab335c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab335c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3360: b               #0xab3088
    // 0xab3364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab3364: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ isRelative(/* No info */) {
    // ** addr: 0xab380c, size: 0x34
    // 0xab380c: EnterFrame
    //     0xab380c: stp             fp, lr, [SP, #-0x10]!
    //     0xab3810: mov             fp, SP
    // 0xab3814: CheckStackOverflow
    //     0xab3814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3818: cmp             SP, x16
    //     0xab381c: b.ls            #0xab3838
    // 0xab3820: r0 = isAbsolute()
    //     0xab3820: bl              #0x8327f4  ; [package:path/src/context.dart] Context::isAbsolute
    // 0xab3824: eor             x1, x0, #0x10
    // 0xab3828: mov             x0, x1
    // 0xab382c: LeaveFrame
    //     0xab382c: mov             SP, fp
    //     0xab3830: ldp             fp, lr, [SP], #0x10
    // 0xab3834: ret
    //     0xab3834: ret             
    // 0xab3838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab383c: b               #0xab3820
  }
  _ prettyUri(/* No info */) {
    // ** addr: 0xc173ec, size: 0x260
    // 0xc173ec: EnterFrame
    //     0xc173ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc173f0: mov             fp, SP
    // 0xc173f4: AllocStack(0x30)
    //     0xc173f4: sub             SP, SP, #0x30
    // 0xc173f8: SetupParameters(Context this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc173f8: mov             x3, x1
    //     0xc173fc: stur            x1, [fp, #-8]
    //     0xc17400: stur            x2, [fp, #-0x10]
    // 0xc17404: CheckStackOverflow
    //     0xc17404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17408: cmp             SP, x16
    //     0xc1740c: b.ls            #0xc17644
    // 0xc17410: r0 = LoadClassIdInstr(r2)
    //     0xc17410: ldur            x0, [x2, #-1]
    //     0xc17414: ubfx            x0, x0, #0xc, #0x14
    // 0xc17418: mov             x1, x2
    // 0xc1741c: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xc1741c: sub             lr, x0, #0xfcb
    //     0xc17420: ldr             lr, [x21, lr, lsl #3]
    //     0xc17424: blr             lr
    // 0xc17428: r1 = LoadClassIdInstr(r0)
    //     0xc17428: ldur            x1, [x0, #-1]
    //     0xc1742c: ubfx            x1, x1, #0xc, #0x14
    // 0xc17430: r16 = "file"
    //     0xc17430: ldr             x16, [PP, #0xc40]  ; [pp+0xc40] "file"
    // 0xc17434: stp             x16, x0, [SP]
    // 0xc17438: mov             x0, x1
    // 0xc1743c: mov             lr, x0
    // 0xc17440: ldr             lr, [x21, lr, lsl #3]
    // 0xc17444: blr             lr
    // 0xc17448: tbnz            w0, #4, #0xc174c4
    // 0xc1744c: ldur            x1, [fp, #-8]
    // 0xc17450: LoadField: r0 = r1->field_7
    //     0xc17450: ldur            w0, [x1, #7]
    // 0xc17454: DecompressPointer r0
    //     0xc17454: add             x0, x0, HEAP, lsl #32
    // 0xc17458: stur            x0, [fp, #-0x18]
    // 0xc1745c: r0 = InitLateStaticField(0x16cc) // [package:path/src/style.dart] Style::url
    //     0xc1745c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc17460: ldr             x0, [x0, #0x2d98]
    //     0xc17464: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc17468: cmp             w0, w16
    //     0xc1746c: b.ne            #0xc1747c
    //     0xc17470: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc40] Field <Style.url>: static late final (offset: 0x16cc)
    //     0xc17474: ldr             x2, [x2, #0xc40]
    //     0xc17478: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc1747c: mov             x1, x0
    // 0xc17480: ldur            x0, [fp, #-0x18]
    // 0xc17484: cmp             w0, w1
    // 0xc17488: b.ne            #0xc174bc
    // 0xc1748c: ldur            x2, [fp, #-0x10]
    // 0xc17490: r0 = LoadClassIdInstr(r2)
    //     0xc17490: ldur            x0, [x2, #-1]
    //     0xc17494: ubfx            x0, x0, #0xc, #0x14
    // 0xc17498: str             x2, [SP]
    // 0xc1749c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc1749c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc174a0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc174a0: movz            x17, #0x2b03
    //     0xc174a4: add             lr, x0, x17
    //     0xc174a8: ldr             lr, [x21, lr, lsl #3]
    //     0xc174ac: blr             lr
    // 0xc174b0: LeaveFrame
    //     0xc174b0: mov             SP, fp
    //     0xc174b4: ldp             fp, lr, [SP], #0x10
    // 0xc174b8: ret
    //     0xc174b8: ret             
    // 0xc174bc: ldur            x2, [fp, #-0x10]
    // 0xc174c0: b               #0xc174c8
    // 0xc174c4: ldur            x2, [fp, #-0x10]
    // 0xc174c8: r0 = LoadClassIdInstr(r2)
    //     0xc174c8: ldur            x0, [x2, #-1]
    //     0xc174cc: ubfx            x0, x0, #0xc, #0x14
    // 0xc174d0: mov             x1, x2
    // 0xc174d4: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xc174d4: sub             lr, x0, #0xfcb
    //     0xc174d8: ldr             lr, [x21, lr, lsl #3]
    //     0xc174dc: blr             lr
    // 0xc174e0: r1 = LoadClassIdInstr(r0)
    //     0xc174e0: ldur            x1, [x0, #-1]
    //     0xc174e4: ubfx            x1, x1, #0xc, #0x14
    // 0xc174e8: r16 = "file"
    //     0xc174e8: ldr             x16, [PP, #0xc40]  ; [pp+0xc40] "file"
    // 0xc174ec: stp             x16, x0, [SP]
    // 0xc174f0: mov             x0, x1
    // 0xc174f4: mov             lr, x0
    // 0xc174f8: ldr             lr, [x21, lr, lsl #3]
    // 0xc174fc: blr             lr
    // 0xc17500: tbz             w0, #4, #0xc175c4
    // 0xc17504: ldur            x2, [fp, #-0x10]
    // 0xc17508: r0 = LoadClassIdInstr(r2)
    //     0xc17508: ldur            x0, [x2, #-1]
    //     0xc1750c: ubfx            x0, x0, #0xc, #0x14
    // 0xc17510: mov             x1, x2
    // 0xc17514: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xc17514: sub             lr, x0, #0xfcb
    //     0xc17518: ldr             lr, [x21, lr, lsl #3]
    //     0xc1751c: blr             lr
    // 0xc17520: r1 = LoadClassIdInstr(r0)
    //     0xc17520: ldur            x1, [x0, #-1]
    //     0xc17524: ubfx            x1, x1, #0xc, #0x14
    // 0xc17528: r16 = ""
    //     0xc17528: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc1752c: stp             x16, x0, [SP]
    // 0xc17530: mov             x0, x1
    // 0xc17534: mov             lr, x0
    // 0xc17538: ldr             lr, [x21, lr, lsl #3]
    // 0xc1753c: blr             lr
    // 0xc17540: tbz             w0, #4, #0xc175bc
    // 0xc17544: ldur            x1, [fp, #-8]
    // 0xc17548: LoadField: r0 = r1->field_7
    //     0xc17548: ldur            w0, [x1, #7]
    // 0xc1754c: DecompressPointer r0
    //     0xc1754c: add             x0, x0, HEAP, lsl #32
    // 0xc17550: stur            x0, [fp, #-0x18]
    // 0xc17554: r0 = InitLateStaticField(0x16cc) // [package:path/src/style.dart] Style::url
    //     0xc17554: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc17558: ldr             x0, [x0, #0x2d98]
    //     0xc1755c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc17560: cmp             w0, w16
    //     0xc17564: b.ne            #0xc17574
    //     0xc17568: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc40] Field <Style.url>: static late final (offset: 0x16cc)
    //     0xc1756c: ldr             x2, [x2, #0xc40]
    //     0xc17570: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc17574: mov             x1, x0
    // 0xc17578: ldur            x0, [fp, #-0x18]
    // 0xc1757c: cmp             w0, w1
    // 0xc17580: b.eq            #0xc175b4
    // 0xc17584: ldur            x2, [fp, #-0x10]
    // 0xc17588: r0 = LoadClassIdInstr(r2)
    //     0xc17588: ldur            x0, [x2, #-1]
    //     0xc1758c: ubfx            x0, x0, #0xc, #0x14
    // 0xc17590: str             x2, [SP]
    // 0xc17594: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc17594: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc17598: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc17598: movz            x17, #0x2b03
    //     0xc1759c: add             lr, x0, x17
    //     0xc175a0: ldr             lr, [x21, lr, lsl #3]
    //     0xc175a4: blr             lr
    // 0xc175a8: LeaveFrame
    //     0xc175a8: mov             SP, fp
    //     0xc175ac: ldp             fp, lr, [SP], #0x10
    // 0xc175b0: ret
    //     0xc175b0: ret             
    // 0xc175b4: ldur            x2, [fp, #-0x10]
    // 0xc175b8: b               #0xc175c8
    // 0xc175bc: ldur            x2, [fp, #-0x10]
    // 0xc175c0: b               #0xc175c8
    // 0xc175c4: ldur            x2, [fp, #-0x10]
    // 0xc175c8: ldur            x1, [fp, #-8]
    // 0xc175cc: r0 = fromUri()
    //     0xc175cc: bl              #0xc183c0  ; [package:path/src/context.dart] Context::fromUri
    // 0xc175d0: ldur            x1, [fp, #-8]
    // 0xc175d4: mov             x2, x0
    // 0xc175d8: r0 = normalize()
    //     0xc175d8: bl              #0xab269c  ; [package:path/src/context.dart] Context::normalize
    // 0xc175dc: ldur            x1, [fp, #-8]
    // 0xc175e0: mov             x2, x0
    // 0xc175e4: stur            x0, [fp, #-0x10]
    // 0xc175e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc175e8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc175ec: r0 = relative()
    //     0xc175ec: bl              #0xc17700  ; [package:path/src/context.dart] Context::relative
    // 0xc175f0: ldur            x1, [fp, #-8]
    // 0xc175f4: mov             x2, x0
    // 0xc175f8: stur            x0, [fp, #-0x18]
    // 0xc175fc: r0 = split()
    //     0xc175fc: bl              #0xc1764c  ; [package:path/src/context.dart] Context::split
    // 0xc17600: LoadField: r3 = r0->field_b
    //     0xc17600: ldur            w3, [x0, #0xb]
    // 0xc17604: ldur            x1, [fp, #-8]
    // 0xc17608: ldur            x2, [fp, #-0x10]
    // 0xc1760c: stur            x3, [fp, #-0x20]
    // 0xc17610: r0 = split()
    //     0xc17610: bl              #0xc1764c  ; [package:path/src/context.dart] Context::split
    // 0xc17614: LoadField: r1 = r0->field_b
    //     0xc17614: ldur            w1, [x0, #0xb]
    // 0xc17618: ldur            x2, [fp, #-0x20]
    // 0xc1761c: r3 = LoadInt32Instr(r2)
    //     0xc1761c: sbfx            x3, x2, #1, #0x1f
    // 0xc17620: r2 = LoadInt32Instr(r1)
    //     0xc17620: sbfx            x2, x1, #1, #0x1f
    // 0xc17624: cmp             x3, x2
    // 0xc17628: b.le            #0xc17634
    // 0xc1762c: ldur            x0, [fp, #-0x10]
    // 0xc17630: b               #0xc17638
    // 0xc17634: ldur            x0, [fp, #-0x18]
    // 0xc17638: LeaveFrame
    //     0xc17638: mov             SP, fp
    //     0xc1763c: ldp             fp, lr, [SP], #0x10
    // 0xc17640: ret
    //     0xc17640: ret             
    // 0xc17644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17648: b               #0xc17410
  }
  _ split(/* No info */) {
    // ** addr: 0xc1764c, size: 0xb4
    // 0xc1764c: EnterFrame
    //     0xc1764c: stp             fp, lr, [SP, #-0x10]!
    //     0xc17650: mov             fp, SP
    // 0xc17654: AllocStack(0x10)
    //     0xc17654: sub             SP, SP, #0x10
    // 0xc17658: CheckStackOverflow
    //     0xc17658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1765c: cmp             SP, x16
    //     0xc17660: b.ls            #0xc176f8
    // 0xc17664: r0 = _parse()
    //     0xc17664: bl              #0x8328cc  ; [package:path/src/context.dart] Context::_parse
    // 0xc17668: stur            x0, [fp, #-0x10]
    // 0xc1766c: LoadField: r3 = r0->field_f
    //     0xc1766c: ldur            w3, [x0, #0xf]
    // 0xc17670: DecompressPointer r3
    //     0xc17670: add             x3, x3, HEAP, lsl #32
    // 0xc17674: stur            x3, [fp, #-8]
    // 0xc17678: r1 = Function '<anonymous closure>':.
    //     0xc17678: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bb20] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    //     0xc1767c: ldr             x1, [x1, #0xb20]
    // 0xc17680: r2 = Null
    //     0xc17680: mov             x2, NULL
    // 0xc17684: r0 = AllocateClosure()
    //     0xc17684: bl              #0xec1630  ; AllocateClosureStub
    // 0xc17688: ldur            x1, [fp, #-8]
    // 0xc1768c: mov             x2, x0
    // 0xc17690: r0 = where()
    //     0xc17690: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xc17694: LoadField: r1 = r0->field_7
    //     0xc17694: ldur            w1, [x0, #7]
    // 0xc17698: DecompressPointer r1
    //     0xc17698: add             x1, x1, HEAP, lsl #32
    // 0xc1769c: mov             x2, x0
    // 0xc176a0: r0 = _GrowableList.of()
    //     0xc176a0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xc176a4: mov             x1, x0
    // 0xc176a8: ldur            x4, [fp, #-0x10]
    // 0xc176ac: StoreField: r4->field_f = r0
    //     0xc176ac: stur            w0, [x4, #0xf]
    //     0xc176b0: ldurb           w16, [x4, #-1]
    //     0xc176b4: ldurb           w17, [x0, #-1]
    //     0xc176b8: and             x16, x17, x16, lsr #2
    //     0xc176bc: tst             x16, HEAP, lsr #32
    //     0xc176c0: b.eq            #0xc176c8
    //     0xc176c4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xc176c8: LoadField: r3 = r4->field_b
    //     0xc176c8: ldur            w3, [x4, #0xb]
    // 0xc176cc: DecompressPointer r3
    //     0xc176cc: add             x3, x3, HEAP, lsl #32
    // 0xc176d0: cmp             w3, NULL
    // 0xc176d4: b.eq            #0xc176e0
    // 0xc176d8: r2 = 0
    //     0xc176d8: movz            x2, #0
    // 0xc176dc: r0 = insert()
    //     0xc176dc: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xc176e0: ldur            x1, [fp, #-0x10]
    // 0xc176e4: LoadField: r0 = r1->field_f
    //     0xc176e4: ldur            w0, [x1, #0xf]
    // 0xc176e8: DecompressPointer r0
    //     0xc176e8: add             x0, x0, HEAP, lsl #32
    // 0xc176ec: LeaveFrame
    //     0xc176ec: mov             SP, fp
    //     0xc176f0: ldp             fp, lr, [SP], #0x10
    // 0xc176f4: ret
    //     0xc176f4: ret             
    // 0xc176f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc176f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc176fc: b               #0xc17664
  }
  _ relative(/* No info */) {
    // ** addr: 0xc17700, size: 0xcb4
    // 0xc17700: EnterFrame
    //     0xc17700: stp             fp, lr, [SP, #-0x10]!
    //     0xc17704: mov             fp, SP
    // 0xc17708: AllocStack(0x80)
    //     0xc17708: sub             SP, SP, #0x80
    // 0xc1770c: SetupParameters(Context this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc1770c: mov             x3, x1
    //     0xc17710: mov             x0, x2
    //     0xc17714: stur            x1, [fp, #-8]
    //     0xc17718: stur            x2, [fp, #-0x10]
    // 0xc1771c: CheckStackOverflow
    //     0xc1771c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17720: cmp             SP, x16
    //     0xc17724: b.ls            #0xc18344
    // 0xc17728: mov             x1, x3
    // 0xc1772c: mov             x2, x0
    // 0xc17730: r0 = isRelative()
    //     0xc17730: bl              #0xab380c  ; [package:path/src/context.dart] Context::isRelative
    // 0xc17734: tbnz            w0, #4, #0xc17750
    // 0xc17738: ldur            x1, [fp, #-8]
    // 0xc1773c: ldur            x2, [fp, #-0x10]
    // 0xc17740: r0 = normalize()
    //     0xc17740: bl              #0xab269c  ; [package:path/src/context.dart] Context::normalize
    // 0xc17744: LeaveFrame
    //     0xc17744: mov             SP, fp
    //     0xc17748: ldp             fp, lr, [SP], #0x10
    // 0xc1774c: ret
    //     0xc1774c: ret             
    // 0xc17750: ldur            x1, [fp, #-8]
    // 0xc17754: r0 = current()
    //     0xc17754: bl              #0xab2600  ; [package:path/src/context.dart] Context::current
    // 0xc17758: ldur            x1, [fp, #-8]
    // 0xc1775c: mov             x2, x0
    // 0xc17760: stur            x0, [fp, #-0x18]
    // 0xc17764: r0 = isRelative()
    //     0xc17764: bl              #0xab380c  ; [package:path/src/context.dart] Context::isRelative
    // 0xc17768: tbnz            w0, #4, #0xc17794
    // 0xc1776c: ldur            x1, [fp, #-8]
    // 0xc17770: ldur            x2, [fp, #-0x10]
    // 0xc17774: r0 = isAbsolute()
    //     0xc17774: bl              #0x8327f4  ; [package:path/src/context.dart] Context::isAbsolute
    // 0xc17778: tbnz            w0, #4, #0xc17794
    // 0xc1777c: ldur            x1, [fp, #-8]
    // 0xc17780: ldur            x2, [fp, #-0x10]
    // 0xc17784: r0 = normalize()
    //     0xc17784: bl              #0xab269c  ; [package:path/src/context.dart] Context::normalize
    // 0xc17788: LeaveFrame
    //     0xc17788: mov             SP, fp
    //     0xc1778c: ldp             fp, lr, [SP], #0x10
    // 0xc17790: ret
    //     0xc17790: ret             
    // 0xc17794: ldur            x1, [fp, #-8]
    // 0xc17798: ldur            x2, [fp, #-0x10]
    // 0xc1779c: r0 = isRelative()
    //     0xc1779c: bl              #0xab380c  ; [package:path/src/context.dart] Context::isRelative
    // 0xc177a0: tbz             w0, #4, #0xc177b4
    // 0xc177a4: ldur            x1, [fp, #-8]
    // 0xc177a8: ldur            x2, [fp, #-0x10]
    // 0xc177ac: r0 = isRootRelative()
    //     0xc177ac: bl              #0x832904  ; [package:path/src/context.dart] Context::isRootRelative
    // 0xc177b0: tbnz            w0, #4, #0xc177c4
    // 0xc177b4: ldur            x1, [fp, #-8]
    // 0xc177b8: ldur            x2, [fp, #-0x10]
    // 0xc177bc: r0 = absolute()
    //     0xc177bc: bl              #0xab24e4  ; [package:path/src/context.dart] Context::absolute
    // 0xc177c0: b               #0xc177c8
    // 0xc177c4: ldur            x0, [fp, #-0x10]
    // 0xc177c8: ldur            x1, [fp, #-8]
    // 0xc177cc: mov             x2, x0
    // 0xc177d0: stur            x0, [fp, #-0x10]
    // 0xc177d4: r0 = isRelative()
    //     0xc177d4: bl              #0xab380c  ; [package:path/src/context.dart] Context::isRelative
    // 0xc177d8: tbnz            w0, #4, #0xc177ec
    // 0xc177dc: ldur            x1, [fp, #-8]
    // 0xc177e0: ldur            x2, [fp, #-0x18]
    // 0xc177e4: r0 = isAbsolute()
    //     0xc177e4: bl              #0x8327f4  ; [package:path/src/context.dart] Context::isAbsolute
    // 0xc177e8: tbz             w0, #4, #0xc1826c
    // 0xc177ec: ldur            x1, [fp, #-8]
    // 0xc177f0: ldur            x2, [fp, #-0x18]
    // 0xc177f4: r0 = _parse()
    //     0xc177f4: bl              #0x8328cc  ; [package:path/src/context.dart] Context::_parse
    // 0xc177f8: mov             x1, x0
    // 0xc177fc: stur            x0, [fp, #-0x20]
    // 0xc17800: r0 = normalize()
    //     0xc17800: bl              #0xab271c  ; [package:path/src/parsed_path.dart] ParsedPath::normalize
    // 0xc17804: ldur            x1, [fp, #-8]
    // 0xc17808: ldur            x2, [fp, #-0x10]
    // 0xc1780c: r0 = _parse()
    //     0xc1780c: bl              #0x8328cc  ; [package:path/src/context.dart] Context::_parse
    // 0xc17810: mov             x1, x0
    // 0xc17814: stur            x0, [fp, #-0x28]
    // 0xc17818: r0 = normalize()
    //     0xc17818: bl              #0xab271c  ; [package:path/src/parsed_path.dart] ParsedPath::normalize
    // 0xc1781c: ldur            x2, [fp, #-0x20]
    // 0xc17820: LoadField: r3 = r2->field_f
    //     0xc17820: ldur            w3, [x2, #0xf]
    // 0xc17824: DecompressPointer r3
    //     0xc17824: add             x3, x3, HEAP, lsl #32
    // 0xc17828: LoadField: r0 = r3->field_b
    //     0xc17828: ldur            w0, [x3, #0xb]
    // 0xc1782c: r1 = LoadInt32Instr(r0)
    //     0xc1782c: sbfx            x1, x0, #1, #0x1f
    // 0xc17830: cbz             w0, #0xc1788c
    // 0xc17834: mov             x0, x1
    // 0xc17838: r1 = 0
    //     0xc17838: movz            x1, #0
    // 0xc1783c: cmp             x1, x0
    // 0xc17840: b.hs            #0xc1834c
    // 0xc17844: LoadField: r0 = r3->field_f
    //     0xc17844: ldur            w0, [x3, #0xf]
    // 0xc17848: DecompressPointer r0
    //     0xc17848: add             x0, x0, HEAP, lsl #32
    // 0xc1784c: LoadField: r1 = r0->field_f
    //     0xc1784c: ldur            w1, [x0, #0xf]
    // 0xc17850: DecompressPointer r1
    //     0xc17850: add             x1, x1, HEAP, lsl #32
    // 0xc17854: r0 = LoadClassIdInstr(r1)
    //     0xc17854: ldur            x0, [x1, #-1]
    //     0xc17858: ubfx            x0, x0, #0xc, #0x14
    // 0xc1785c: r16 = "."
    //     0xc1785c: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc17860: stp             x16, x1, [SP]
    // 0xc17864: mov             lr, x0
    // 0xc17868: ldr             lr, [x21, lr, lsl #3]
    // 0xc1786c: blr             lr
    // 0xc17870: tbnz            w0, #4, #0xc1788c
    // 0xc17874: ldur            x16, [fp, #-0x28]
    // 0xc17878: str             x16, [SP]
    // 0xc1787c: r0 = toString()
    //     0xc1787c: bl              #0xc336cc  ; [package:path/src/parsed_path.dart] ParsedPath::toString
    // 0xc17880: LeaveFrame
    //     0xc17880: mov             SP, fp
    //     0xc17884: ldp             fp, lr, [SP], #0x10
    // 0xc17888: ret
    //     0xc17888: ret             
    // 0xc1788c: ldur            x1, [fp, #-0x20]
    // 0xc17890: ldur            x2, [fp, #-0x28]
    // 0xc17894: LoadField: r0 = r1->field_b
    //     0xc17894: ldur            w0, [x1, #0xb]
    // 0xc17898: DecompressPointer r0
    //     0xc17898: add             x0, x0, HEAP, lsl #32
    // 0xc1789c: LoadField: r3 = r2->field_b
    //     0xc1789c: ldur            w3, [x2, #0xb]
    // 0xc178a0: DecompressPointer r3
    //     0xc178a0: add             x3, x3, HEAP, lsl #32
    // 0xc178a4: r4 = LoadClassIdInstr(r0)
    //     0xc178a4: ldur            x4, [x0, #-1]
    //     0xc178a8: ubfx            x4, x4, #0xc, #0x14
    // 0xc178ac: stp             x3, x0, [SP]
    // 0xc178b0: mov             x0, x4
    // 0xc178b4: mov             lr, x0
    // 0xc178b8: ldr             lr, [x21, lr, lsl #3]
    // 0xc178bc: blr             lr
    // 0xc178c0: tbz             w0, #4, #0xc1796c
    // 0xc178c4: ldur            x1, [fp, #-0x20]
    // 0xc178c8: LoadField: r2 = r1->field_b
    //     0xc178c8: ldur            w2, [x1, #0xb]
    // 0xc178cc: DecompressPointer r2
    //     0xc178cc: add             x2, x2, HEAP, lsl #32
    // 0xc178d0: cmp             w2, NULL
    // 0xc178d4: b.eq            #0xc17954
    // 0xc178d8: ldur            x3, [fp, #-0x28]
    // 0xc178dc: LoadField: r0 = r3->field_b
    //     0xc178dc: ldur            w0, [x3, #0xb]
    // 0xc178e0: DecompressPointer r0
    //     0xc178e0: add             x0, x0, HEAP, lsl #32
    // 0xc178e4: cmp             w0, NULL
    // 0xc178e8: b.eq            #0xc17954
    // 0xc178ec: ldur            x4, [fp, #-8]
    // 0xc178f0: LoadField: r5 = r4->field_7
    //     0xc178f0: ldur            w5, [x4, #7]
    // 0xc178f4: DecompressPointer r5
    //     0xc178f4: add             x5, x5, HEAP, lsl #32
    // 0xc178f8: r6 = LoadClassIdInstr(r5)
    //     0xc178f8: ldur            x6, [x5, #-1]
    //     0xc178fc: ubfx            x6, x6, #0xc, #0x14
    // 0xc17900: sub             x16, x6, #0x3b0
    // 0xc17904: cmp             x16, #1
    // 0xc17908: b.hi            #0xc17930
    // 0xc1790c: r5 = LoadClassIdInstr(r2)
    //     0xc1790c: ldur            x5, [x2, #-1]
    //     0xc17910: ubfx            x5, x5, #0xc, #0x14
    // 0xc17914: stp             x0, x2, [SP]
    // 0xc17918: mov             x0, x5
    // 0xc1791c: mov             lr, x0
    // 0xc17920: ldr             lr, [x21, lr, lsl #3]
    // 0xc17924: blr             lr
    // 0xc17928: tbz             w0, #4, #0xc1796c
    // 0xc1792c: b               #0xc17954
    // 0xc17930: r1 = LoadClassIdInstr(r5)
    //     0xc17930: ldur            x1, [x5, #-1]
    //     0xc17934: ubfx            x1, x1, #0xc, #0x14
    // 0xc17938: mov             x3, x0
    // 0xc1793c: mov             x0, x1
    // 0xc17940: mov             x1, x5
    // 0xc17944: r0 = GDT[cid_x0 + -0xfe7]()
    //     0xc17944: sub             lr, x0, #0xfe7
    //     0xc17948: ldr             lr, [x21, lr, lsl #3]
    //     0xc1794c: blr             lr
    // 0xc17950: tbz             w0, #4, #0xc1796c
    // 0xc17954: ldur            x16, [fp, #-0x28]
    // 0xc17958: str             x16, [SP]
    // 0xc1795c: r0 = toString()
    //     0xc1795c: bl              #0xc336cc  ; [package:path/src/parsed_path.dart] ParsedPath::toString
    // 0xc17960: LeaveFrame
    //     0xc17960: mov             SP, fp
    //     0xc17964: ldp             fp, lr, [SP], #0x10
    // 0xc17968: ret
    //     0xc17968: ret             
    // 0xc1796c: ldur            x0, [fp, #-8]
    // 0xc17970: LoadField: r2 = r0->field_7
    //     0xc17970: ldur            w2, [x0, #7]
    // 0xc17974: DecompressPointer r2
    //     0xc17974: add             x2, x2, HEAP, lsl #32
    // 0xc17978: stur            x2, [fp, #-0x38]
    // 0xc1797c: r3 = LoadClassIdInstr(r2)
    //     0xc1797c: ldur            x3, [x2, #-1]
    //     0xc17980: ubfx            x3, x3, #0xc, #0x14
    // 0xc17984: stur            x3, [fp, #-0x30]
    // 0xc17988: ldur            x4, [fp, #-0x20]
    // 0xc1798c: ldur            x5, [fp, #-0x28]
    // 0xc17990: CheckStackOverflow
    //     0xc17990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17994: cmp             SP, x16
    //     0xc17998: b.ls            #0xc18350
    // 0xc1799c: LoadField: r6 = r4->field_f
    //     0xc1799c: ldur            w6, [x4, #0xf]
    // 0xc179a0: DecompressPointer r6
    //     0xc179a0: add             x6, x6, HEAP, lsl #32
    // 0xc179a4: LoadField: r0 = r6->field_b
    //     0xc179a4: ldur            w0, [x6, #0xb]
    // 0xc179a8: r1 = LoadInt32Instr(r0)
    //     0xc179a8: sbfx            x1, x0, #1, #0x1f
    // 0xc179ac: cbz             w0, #0xc17f08
    // 0xc179b0: LoadField: r7 = r5->field_f
    //     0xc179b0: ldur            w7, [x5, #0xf]
    // 0xc179b4: DecompressPointer r7
    //     0xc179b4: add             x7, x7, HEAP, lsl #32
    // 0xc179b8: LoadField: r0 = r7->field_b
    //     0xc179b8: ldur            w0, [x7, #0xb]
    // 0xc179bc: r8 = LoadInt32Instr(r0)
    //     0xc179bc: sbfx            x8, x0, #1, #0x1f
    // 0xc179c0: cbz             w0, #0xc17f08
    // 0xc179c4: mov             x0, x1
    // 0xc179c8: r1 = 0
    //     0xc179c8: movz            x1, #0
    // 0xc179cc: cmp             x1, x0
    // 0xc179d0: b.hs            #0xc18358
    // 0xc179d4: LoadField: r0 = r6->field_f
    //     0xc179d4: ldur            w0, [x6, #0xf]
    // 0xc179d8: DecompressPointer r0
    //     0xc179d8: add             x0, x0, HEAP, lsl #32
    // 0xc179dc: LoadField: r6 = r0->field_f
    //     0xc179dc: ldur            w6, [x0, #0xf]
    // 0xc179e0: DecompressPointer r6
    //     0xc179e0: add             x6, x6, HEAP, lsl #32
    // 0xc179e4: mov             x0, x8
    // 0xc179e8: r1 = 0
    //     0xc179e8: movz            x1, #0
    // 0xc179ec: cmp             x1, x0
    // 0xc179f0: b.hs            #0xc1835c
    // 0xc179f4: LoadField: r0 = r7->field_f
    //     0xc179f4: ldur            w0, [x7, #0xf]
    // 0xc179f8: DecompressPointer r0
    //     0xc179f8: add             x0, x0, HEAP, lsl #32
    // 0xc179fc: LoadField: r1 = r0->field_f
    //     0xc179fc: ldur            w1, [x0, #0xf]
    // 0xc17a00: DecompressPointer r1
    //     0xc17a00: add             x1, x1, HEAP, lsl #32
    // 0xc17a04: sub             x16, x3, #0x3b0
    // 0xc17a08: cmp             x16, #1
    // 0xc17a0c: b.hi            #0xc17a30
    // 0xc17a10: r0 = LoadClassIdInstr(r6)
    //     0xc17a10: ldur            x0, [x6, #-1]
    //     0xc17a14: ubfx            x0, x0, #0xc, #0x14
    // 0xc17a18: stp             x1, x6, [SP]
    // 0xc17a1c: mov             lr, x0
    // 0xc17a20: ldr             lr, [x21, lr, lsl #3]
    // 0xc17a24: blr             lr
    // 0xc17a28: tbnz            w0, #4, #0xc17f08
    // 0xc17a2c: b               #0xc17a58
    // 0xc17a30: mov             x4, x2
    // 0xc17a34: r0 = LoadClassIdInstr(r4)
    //     0xc17a34: ldur            x0, [x4, #-1]
    //     0xc17a38: ubfx            x0, x0, #0xc, #0x14
    // 0xc17a3c: mov             x3, x1
    // 0xc17a40: mov             x1, x4
    // 0xc17a44: mov             x2, x6
    // 0xc17a48: r0 = GDT[cid_x0 + -0xfe7]()
    //     0xc17a48: sub             lr, x0, #0xfe7
    //     0xc17a4c: ldr             lr, [x21, lr, lsl #3]
    //     0xc17a50: blr             lr
    // 0xc17a54: tbnz            w0, #4, #0xc17f08
    // 0xc17a58: ldur            x3, [fp, #-0x20]
    // 0xc17a5c: LoadField: r4 = r3->field_f
    //     0xc17a5c: ldur            w4, [x3, #0xf]
    // 0xc17a60: DecompressPointer r4
    //     0xc17a60: add             x4, x4, HEAP, lsl #32
    // 0xc17a64: stur            x4, [fp, #-0x70]
    // 0xc17a68: LoadField: r0 = r4->field_b
    //     0xc17a68: ldur            w0, [x4, #0xb]
    // 0xc17a6c: r2 = LoadInt32Instr(r0)
    //     0xc17a6c: sbfx            x2, x0, #1, #0x1f
    // 0xc17a70: mov             x0, x2
    // 0xc17a74: r1 = 0
    //     0xc17a74: movz            x1, #0
    // 0xc17a78: cmp             x1, x0
    // 0xc17a7c: b.hs            #0xc18360
    // 0xc17a80: LoadField: r5 = r4->field_f
    //     0xc17a80: ldur            w5, [x4, #0xf]
    // 0xc17a84: DecompressPointer r5
    //     0xc17a84: add             x5, x5, HEAP, lsl #32
    // 0xc17a88: stur            x5, [fp, #-0x68]
    // 0xc17a8c: sub             x6, x2, #1
    // 0xc17a90: stur            x6, [fp, #-0x60]
    // 0xc17a94: cmp             x6, #0
    // 0xc17a98: b.le            #0xc17b70
    // 0xc17a9c: add             x7, x6, #1
    // 0xc17aa0: stur            x7, [fp, #-0x58]
    // 0xc17aa4: LoadField: r8 = r4->field_7
    //     0xc17aa4: ldur            w8, [x4, #7]
    // 0xc17aa8: DecompressPointer r8
    //     0xc17aa8: add             x8, x8, HEAP, lsl #32
    // 0xc17aac: stur            x8, [fp, #-0x50]
    // 0xc17ab0: r10 = 1
    //     0xc17ab0: movz            x10, #0x1
    // 0xc17ab4: r9 = 0
    //     0xc17ab4: movz            x9, #0
    // 0xc17ab8: stur            x10, [fp, #-0x40]
    // 0xc17abc: stur            x9, [fp, #-0x48]
    // 0xc17ac0: CheckStackOverflow
    //     0xc17ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17ac4: cmp             SP, x16
    //     0xc17ac8: b.ls            #0xc18364
    // 0xc17acc: cmp             x10, x7
    // 0xc17ad0: b.ge            #0xc17b70
    // 0xc17ad4: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0xc17ad4: add             x16, x5, x10, lsl #2
    //     0xc17ad8: ldur            w11, [x16, #0xf]
    // 0xc17adc: DecompressPointer r11
    //     0xc17adc: add             x11, x11, HEAP, lsl #32
    // 0xc17ae0: mov             x0, x11
    // 0xc17ae4: mov             x2, x8
    // 0xc17ae8: stur            x11, [fp, #-8]
    // 0xc17aec: r1 = Null
    //     0xc17aec: mov             x1, NULL
    // 0xc17af0: cmp             w2, NULL
    // 0xc17af4: b.eq            #0xc17b14
    // 0xc17af8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc17af8: ldur            w4, [x2, #0x17]
    // 0xc17afc: DecompressPointer r4
    //     0xc17afc: add             x4, x4, HEAP, lsl #32
    // 0xc17b00: r8 = X0
    //     0xc17b00: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc17b04: LoadField: r9 = r4->field_7
    //     0xc17b04: ldur            x9, [x4, #7]
    // 0xc17b08: r3 = Null
    //     0xc17b08: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb28] Null
    //     0xc17b0c: ldr             x3, [x3, #0xb28]
    // 0xc17b10: blr             x9
    // 0xc17b14: ldur            x1, [fp, #-0x68]
    // 0xc17b18: ldur            x0, [fp, #-8]
    // 0xc17b1c: ldur            x2, [fp, #-0x48]
    // 0xc17b20: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc17b20: add             x25, x1, x2, lsl #2
    //     0xc17b24: add             x25, x25, #0xf
    //     0xc17b28: str             w0, [x25]
    //     0xc17b2c: tbz             w0, #0, #0xc17b48
    //     0xc17b30: ldurb           w16, [x1, #-1]
    //     0xc17b34: ldurb           w17, [x0, #-1]
    //     0xc17b38: and             x16, x17, x16, lsr #2
    //     0xc17b3c: tst             x16, HEAP, lsr #32
    //     0xc17b40: b.eq            #0xc17b48
    //     0xc17b44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc17b48: ldur            x0, [fp, #-0x40]
    // 0xc17b4c: add             x10, x0, #1
    // 0xc17b50: add             x9, x2, #1
    // 0xc17b54: ldur            x3, [fp, #-0x20]
    // 0xc17b58: ldur            x4, [fp, #-0x70]
    // 0xc17b5c: ldur            x6, [fp, #-0x60]
    // 0xc17b60: ldur            x5, [fp, #-0x68]
    // 0xc17b64: ldur            x7, [fp, #-0x58]
    // 0xc17b68: ldur            x8, [fp, #-0x50]
    // 0xc17b6c: b               #0xc17ab8
    // 0xc17b70: ldur            x0, [fp, #-0x20]
    // 0xc17b74: ldur            x1, [fp, #-0x70]
    // 0xc17b78: ldur            x2, [fp, #-0x60]
    // 0xc17b7c: r0 = length=()
    //     0xc17b7c: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc17b80: ldur            x3, [fp, #-0x20]
    // 0xc17b84: LoadField: r4 = r3->field_13
    //     0xc17b84: ldur            w4, [x3, #0x13]
    // 0xc17b88: DecompressPointer r4
    //     0xc17b88: add             x4, x4, HEAP, lsl #32
    // 0xc17b8c: stur            x4, [fp, #-0x70]
    // 0xc17b90: LoadField: r0 = r4->field_b
    //     0xc17b90: ldur            w0, [x4, #0xb]
    // 0xc17b94: r2 = LoadInt32Instr(r0)
    //     0xc17b94: sbfx            x2, x0, #1, #0x1f
    // 0xc17b98: mov             x0, x2
    // 0xc17b9c: r1 = 1
    //     0xc17b9c: movz            x1, #0x1
    // 0xc17ba0: cmp             x1, x0
    // 0xc17ba4: b.hs            #0xc1836c
    // 0xc17ba8: LoadField: r5 = r4->field_f
    //     0xc17ba8: ldur            w5, [x4, #0xf]
    // 0xc17bac: DecompressPointer r5
    //     0xc17bac: add             x5, x5, HEAP, lsl #32
    // 0xc17bb0: stur            x5, [fp, #-0x68]
    // 0xc17bb4: sub             x6, x2, #1
    // 0xc17bb8: stur            x6, [fp, #-0x60]
    // 0xc17bbc: cmp             x6, #1
    // 0xc17bc0: b.le            #0xc17c9c
    // 0xc17bc4: sub             x0, x6, #1
    // 0xc17bc8: add             x7, x0, #2
    // 0xc17bcc: stur            x7, [fp, #-0x58]
    // 0xc17bd0: LoadField: r8 = r4->field_7
    //     0xc17bd0: ldur            w8, [x4, #7]
    // 0xc17bd4: DecompressPointer r8
    //     0xc17bd4: add             x8, x8, HEAP, lsl #32
    // 0xc17bd8: stur            x8, [fp, #-0x50]
    // 0xc17bdc: r10 = 2
    //     0xc17bdc: movz            x10, #0x2
    // 0xc17be0: r9 = 1
    //     0xc17be0: movz            x9, #0x1
    // 0xc17be4: stur            x10, [fp, #-0x40]
    // 0xc17be8: stur            x9, [fp, #-0x48]
    // 0xc17bec: CheckStackOverflow
    //     0xc17bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17bf0: cmp             SP, x16
    //     0xc17bf4: b.ls            #0xc18370
    // 0xc17bf8: cmp             x10, x7
    // 0xc17bfc: b.ge            #0xc17c9c
    // 0xc17c00: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0xc17c00: add             x16, x5, x10, lsl #2
    //     0xc17c04: ldur            w11, [x16, #0xf]
    // 0xc17c08: DecompressPointer r11
    //     0xc17c08: add             x11, x11, HEAP, lsl #32
    // 0xc17c0c: mov             x0, x11
    // 0xc17c10: mov             x2, x8
    // 0xc17c14: stur            x11, [fp, #-8]
    // 0xc17c18: r1 = Null
    //     0xc17c18: mov             x1, NULL
    // 0xc17c1c: cmp             w2, NULL
    // 0xc17c20: b.eq            #0xc17c40
    // 0xc17c24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc17c24: ldur            w4, [x2, #0x17]
    // 0xc17c28: DecompressPointer r4
    //     0xc17c28: add             x4, x4, HEAP, lsl #32
    // 0xc17c2c: r8 = X0
    //     0xc17c2c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc17c30: LoadField: r9 = r4->field_7
    //     0xc17c30: ldur            x9, [x4, #7]
    // 0xc17c34: r3 = Null
    //     0xc17c34: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb38] Null
    //     0xc17c38: ldr             x3, [x3, #0xb38]
    // 0xc17c3c: blr             x9
    // 0xc17c40: ldur            x1, [fp, #-0x68]
    // 0xc17c44: ldur            x0, [fp, #-8]
    // 0xc17c48: ldur            x2, [fp, #-0x48]
    // 0xc17c4c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc17c4c: add             x25, x1, x2, lsl #2
    //     0xc17c50: add             x25, x25, #0xf
    //     0xc17c54: str             w0, [x25]
    //     0xc17c58: tbz             w0, #0, #0xc17c74
    //     0xc17c5c: ldurb           w16, [x1, #-1]
    //     0xc17c60: ldurb           w17, [x0, #-1]
    //     0xc17c64: and             x16, x17, x16, lsr #2
    //     0xc17c68: tst             x16, HEAP, lsr #32
    //     0xc17c6c: b.eq            #0xc17c74
    //     0xc17c70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc17c74: ldur            x0, [fp, #-0x40]
    // 0xc17c78: add             x10, x0, #1
    // 0xc17c7c: add             x9, x2, #1
    // 0xc17c80: ldur            x3, [fp, #-0x20]
    // 0xc17c84: ldur            x4, [fp, #-0x70]
    // 0xc17c88: ldur            x6, [fp, #-0x60]
    // 0xc17c8c: ldur            x5, [fp, #-0x68]
    // 0xc17c90: ldur            x7, [fp, #-0x58]
    // 0xc17c94: ldur            x8, [fp, #-0x50]
    // 0xc17c98: b               #0xc17be4
    // 0xc17c9c: ldur            x0, [fp, #-0x28]
    // 0xc17ca0: ldur            x1, [fp, #-0x70]
    // 0xc17ca4: ldur            x2, [fp, #-0x60]
    // 0xc17ca8: r0 = length=()
    //     0xc17ca8: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc17cac: ldur            x3, [fp, #-0x28]
    // 0xc17cb0: LoadField: r4 = r3->field_f
    //     0xc17cb0: ldur            w4, [x3, #0xf]
    // 0xc17cb4: DecompressPointer r4
    //     0xc17cb4: add             x4, x4, HEAP, lsl #32
    // 0xc17cb8: stur            x4, [fp, #-0x70]
    // 0xc17cbc: LoadField: r0 = r4->field_b
    //     0xc17cbc: ldur            w0, [x4, #0xb]
    // 0xc17cc0: r2 = LoadInt32Instr(r0)
    //     0xc17cc0: sbfx            x2, x0, #1, #0x1f
    // 0xc17cc4: mov             x0, x2
    // 0xc17cc8: r1 = 0
    //     0xc17cc8: movz            x1, #0
    // 0xc17ccc: cmp             x1, x0
    // 0xc17cd0: b.hs            #0xc18378
    // 0xc17cd4: LoadField: r5 = r4->field_f
    //     0xc17cd4: ldur            w5, [x4, #0xf]
    // 0xc17cd8: DecompressPointer r5
    //     0xc17cd8: add             x5, x5, HEAP, lsl #32
    // 0xc17cdc: stur            x5, [fp, #-0x68]
    // 0xc17ce0: sub             x6, x2, #1
    // 0xc17ce4: stur            x6, [fp, #-0x60]
    // 0xc17ce8: cmp             x6, #0
    // 0xc17cec: b.le            #0xc17dc4
    // 0xc17cf0: add             x7, x6, #1
    // 0xc17cf4: stur            x7, [fp, #-0x58]
    // 0xc17cf8: LoadField: r8 = r4->field_7
    //     0xc17cf8: ldur            w8, [x4, #7]
    // 0xc17cfc: DecompressPointer r8
    //     0xc17cfc: add             x8, x8, HEAP, lsl #32
    // 0xc17d00: stur            x8, [fp, #-0x50]
    // 0xc17d04: r10 = 1
    //     0xc17d04: movz            x10, #0x1
    // 0xc17d08: r9 = 0
    //     0xc17d08: movz            x9, #0
    // 0xc17d0c: stur            x10, [fp, #-0x40]
    // 0xc17d10: stur            x9, [fp, #-0x48]
    // 0xc17d14: CheckStackOverflow
    //     0xc17d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17d18: cmp             SP, x16
    //     0xc17d1c: b.ls            #0xc1837c
    // 0xc17d20: cmp             x10, x7
    // 0xc17d24: b.ge            #0xc17dc4
    // 0xc17d28: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0xc17d28: add             x16, x5, x10, lsl #2
    //     0xc17d2c: ldur            w11, [x16, #0xf]
    // 0xc17d30: DecompressPointer r11
    //     0xc17d30: add             x11, x11, HEAP, lsl #32
    // 0xc17d34: mov             x0, x11
    // 0xc17d38: mov             x2, x8
    // 0xc17d3c: stur            x11, [fp, #-8]
    // 0xc17d40: r1 = Null
    //     0xc17d40: mov             x1, NULL
    // 0xc17d44: cmp             w2, NULL
    // 0xc17d48: b.eq            #0xc17d68
    // 0xc17d4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc17d4c: ldur            w4, [x2, #0x17]
    // 0xc17d50: DecompressPointer r4
    //     0xc17d50: add             x4, x4, HEAP, lsl #32
    // 0xc17d54: r8 = X0
    //     0xc17d54: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc17d58: LoadField: r9 = r4->field_7
    //     0xc17d58: ldur            x9, [x4, #7]
    // 0xc17d5c: r3 = Null
    //     0xc17d5c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb48] Null
    //     0xc17d60: ldr             x3, [x3, #0xb48]
    // 0xc17d64: blr             x9
    // 0xc17d68: ldur            x1, [fp, #-0x68]
    // 0xc17d6c: ldur            x0, [fp, #-8]
    // 0xc17d70: ldur            x2, [fp, #-0x48]
    // 0xc17d74: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc17d74: add             x25, x1, x2, lsl #2
    //     0xc17d78: add             x25, x25, #0xf
    //     0xc17d7c: str             w0, [x25]
    //     0xc17d80: tbz             w0, #0, #0xc17d9c
    //     0xc17d84: ldurb           w16, [x1, #-1]
    //     0xc17d88: ldurb           w17, [x0, #-1]
    //     0xc17d8c: and             x16, x17, x16, lsr #2
    //     0xc17d90: tst             x16, HEAP, lsr #32
    //     0xc17d94: b.eq            #0xc17d9c
    //     0xc17d98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc17d9c: ldur            x0, [fp, #-0x40]
    // 0xc17da0: add             x10, x0, #1
    // 0xc17da4: add             x9, x2, #1
    // 0xc17da8: ldur            x3, [fp, #-0x28]
    // 0xc17dac: ldur            x4, [fp, #-0x70]
    // 0xc17db0: ldur            x6, [fp, #-0x60]
    // 0xc17db4: ldur            x5, [fp, #-0x68]
    // 0xc17db8: ldur            x7, [fp, #-0x58]
    // 0xc17dbc: ldur            x8, [fp, #-0x50]
    // 0xc17dc0: b               #0xc17d0c
    // 0xc17dc4: ldur            x0, [fp, #-0x28]
    // 0xc17dc8: ldur            x1, [fp, #-0x70]
    // 0xc17dcc: ldur            x2, [fp, #-0x60]
    // 0xc17dd0: r0 = length=()
    //     0xc17dd0: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc17dd4: ldur            x3, [fp, #-0x28]
    // 0xc17dd8: LoadField: r4 = r3->field_13
    //     0xc17dd8: ldur            w4, [x3, #0x13]
    // 0xc17ddc: DecompressPointer r4
    //     0xc17ddc: add             x4, x4, HEAP, lsl #32
    // 0xc17de0: stur            x4, [fp, #-0x70]
    // 0xc17de4: LoadField: r0 = r4->field_b
    //     0xc17de4: ldur            w0, [x4, #0xb]
    // 0xc17de8: r2 = LoadInt32Instr(r0)
    //     0xc17de8: sbfx            x2, x0, #1, #0x1f
    // 0xc17dec: mov             x0, x2
    // 0xc17df0: r1 = 1
    //     0xc17df0: movz            x1, #0x1
    // 0xc17df4: cmp             x1, x0
    // 0xc17df8: b.hs            #0xc18384
    // 0xc17dfc: LoadField: r5 = r4->field_f
    //     0xc17dfc: ldur            w5, [x4, #0xf]
    // 0xc17e00: DecompressPointer r5
    //     0xc17e00: add             x5, x5, HEAP, lsl #32
    // 0xc17e04: stur            x5, [fp, #-0x68]
    // 0xc17e08: sub             x6, x2, #1
    // 0xc17e0c: stur            x6, [fp, #-0x60]
    // 0xc17e10: cmp             x6, #1
    // 0xc17e14: b.le            #0xc17ef0
    // 0xc17e18: sub             x0, x6, #1
    // 0xc17e1c: add             x7, x0, #2
    // 0xc17e20: stur            x7, [fp, #-0x58]
    // 0xc17e24: LoadField: r8 = r4->field_7
    //     0xc17e24: ldur            w8, [x4, #7]
    // 0xc17e28: DecompressPointer r8
    //     0xc17e28: add             x8, x8, HEAP, lsl #32
    // 0xc17e2c: stur            x8, [fp, #-0x50]
    // 0xc17e30: r10 = 2
    //     0xc17e30: movz            x10, #0x2
    // 0xc17e34: r9 = 1
    //     0xc17e34: movz            x9, #0x1
    // 0xc17e38: stur            x10, [fp, #-0x40]
    // 0xc17e3c: stur            x9, [fp, #-0x48]
    // 0xc17e40: CheckStackOverflow
    //     0xc17e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17e44: cmp             SP, x16
    //     0xc17e48: b.ls            #0xc18388
    // 0xc17e4c: cmp             x10, x7
    // 0xc17e50: b.ge            #0xc17ef0
    // 0xc17e54: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0xc17e54: add             x16, x5, x10, lsl #2
    //     0xc17e58: ldur            w11, [x16, #0xf]
    // 0xc17e5c: DecompressPointer r11
    //     0xc17e5c: add             x11, x11, HEAP, lsl #32
    // 0xc17e60: mov             x0, x11
    // 0xc17e64: mov             x2, x8
    // 0xc17e68: stur            x11, [fp, #-8]
    // 0xc17e6c: r1 = Null
    //     0xc17e6c: mov             x1, NULL
    // 0xc17e70: cmp             w2, NULL
    // 0xc17e74: b.eq            #0xc17e94
    // 0xc17e78: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc17e78: ldur            w4, [x2, #0x17]
    // 0xc17e7c: DecompressPointer r4
    //     0xc17e7c: add             x4, x4, HEAP, lsl #32
    // 0xc17e80: r8 = X0
    //     0xc17e80: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc17e84: LoadField: r9 = r4->field_7
    //     0xc17e84: ldur            x9, [x4, #7]
    // 0xc17e88: r3 = Null
    //     0xc17e88: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bb58] Null
    //     0xc17e8c: ldr             x3, [x3, #0xb58]
    // 0xc17e90: blr             x9
    // 0xc17e94: ldur            x1, [fp, #-0x68]
    // 0xc17e98: ldur            x0, [fp, #-8]
    // 0xc17e9c: ldur            x2, [fp, #-0x48]
    // 0xc17ea0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc17ea0: add             x25, x1, x2, lsl #2
    //     0xc17ea4: add             x25, x25, #0xf
    //     0xc17ea8: str             w0, [x25]
    //     0xc17eac: tbz             w0, #0, #0xc17ec8
    //     0xc17eb0: ldurb           w16, [x1, #-1]
    //     0xc17eb4: ldurb           w17, [x0, #-1]
    //     0xc17eb8: and             x16, x17, x16, lsr #2
    //     0xc17ebc: tst             x16, HEAP, lsr #32
    //     0xc17ec0: b.eq            #0xc17ec8
    //     0xc17ec4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc17ec8: ldur            x0, [fp, #-0x40]
    // 0xc17ecc: add             x10, x0, #1
    // 0xc17ed0: add             x9, x2, #1
    // 0xc17ed4: ldur            x3, [fp, #-0x28]
    // 0xc17ed8: ldur            x4, [fp, #-0x70]
    // 0xc17edc: ldur            x6, [fp, #-0x60]
    // 0xc17ee0: ldur            x5, [fp, #-0x68]
    // 0xc17ee4: ldur            x7, [fp, #-0x58]
    // 0xc17ee8: ldur            x8, [fp, #-0x50]
    // 0xc17eec: b               #0xc17e38
    // 0xc17ef0: ldur            x1, [fp, #-0x70]
    // 0xc17ef4: ldur            x2, [fp, #-0x60]
    // 0xc17ef8: r0 = length=()
    //     0xc17ef8: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc17efc: ldur            x2, [fp, #-0x38]
    // 0xc17f00: ldur            x3, [fp, #-0x30]
    // 0xc17f04: b               #0xc17988
    // 0xc17f08: ldur            x2, [fp, #-0x20]
    // 0xc17f0c: LoadField: r3 = r2->field_f
    //     0xc17f0c: ldur            w3, [x2, #0xf]
    // 0xc17f10: DecompressPointer r3
    //     0xc17f10: add             x3, x3, HEAP, lsl #32
    // 0xc17f14: LoadField: r0 = r3->field_b
    //     0xc17f14: ldur            w0, [x3, #0xb]
    // 0xc17f18: r1 = LoadInt32Instr(r0)
    //     0xc17f18: sbfx            x1, x0, #1, #0x1f
    // 0xc17f1c: cbz             w0, #0xc17f60
    // 0xc17f20: mov             x0, x1
    // 0xc17f24: r1 = 0
    //     0xc17f24: movz            x1, #0
    // 0xc17f28: cmp             x1, x0
    // 0xc17f2c: b.hs            #0xc18390
    // 0xc17f30: LoadField: r0 = r3->field_f
    //     0xc17f30: ldur            w0, [x3, #0xf]
    // 0xc17f34: DecompressPointer r0
    //     0xc17f34: add             x0, x0, HEAP, lsl #32
    // 0xc17f38: LoadField: r1 = r0->field_f
    //     0xc17f38: ldur            w1, [x0, #0xf]
    // 0xc17f3c: DecompressPointer r1
    //     0xc17f3c: add             x1, x1, HEAP, lsl #32
    // 0xc17f40: r0 = LoadClassIdInstr(r1)
    //     0xc17f40: ldur            x0, [x1, #-1]
    //     0xc17f44: ubfx            x0, x0, #0xc, #0x14
    // 0xc17f48: r16 = ".."
    //     0xc17f48: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xc17f4c: stp             x16, x1, [SP]
    // 0xc17f50: mov             lr, x0
    // 0xc17f54: ldr             lr, [x21, lr, lsl #3]
    // 0xc17f58: blr             lr
    // 0xc17f5c: tbz             w0, #4, #0xc182d8
    // 0xc17f60: ldur            x0, [fp, #-0x20]
    // 0xc17f64: ldur            x3, [fp, #-0x28]
    // 0xc17f68: LoadField: r4 = r3->field_f
    //     0xc17f68: ldur            w4, [x3, #0xf]
    // 0xc17f6c: DecompressPointer r4
    //     0xc17f6c: add             x4, x4, HEAP, lsl #32
    // 0xc17f70: stur            x4, [fp, #-0x50]
    // 0xc17f74: LoadField: r1 = r0->field_f
    //     0xc17f74: ldur            w1, [x0, #0xf]
    // 0xc17f78: DecompressPointer r1
    //     0xc17f78: add             x1, x1, HEAP, lsl #32
    // 0xc17f7c: LoadField: r5 = r1->field_b
    //     0xc17f7c: ldur            w5, [x1, #0xb]
    // 0xc17f80: mov             x2, x5
    // 0xc17f84: stur            x5, [fp, #-8]
    // 0xc17f88: r1 = <String>
    //     0xc17f88: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc17f8c: r0 = AllocateArray()
    //     0xc17f8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc17f90: mov             x1, x0
    // 0xc17f94: ldur            x0, [fp, #-8]
    // 0xc17f98: r2 = LoadInt32Instr(r0)
    //     0xc17f98: sbfx            x2, x0, #1, #0x1f
    // 0xc17f9c: r0 = 0
    //     0xc17f9c: movz            x0, #0
    // 0xc17fa0: CheckStackOverflow
    //     0xc17fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17fa4: cmp             SP, x16
    //     0xc17fa8: b.ls            #0xc18394
    // 0xc17fac: cmp             x0, x2
    // 0xc17fb0: b.ge            #0xc17fcc
    // 0xc17fb4: add             x3, x1, x0, lsl #2
    // 0xc17fb8: r16 = ".."
    //     0xc17fb8: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xc17fbc: StoreField: r3->field_f = r16
    //     0xc17fbc: stur            w16, [x3, #0xf]
    // 0xc17fc0: add             x3, x0, #1
    // 0xc17fc4: mov             x0, x3
    // 0xc17fc8: b               #0xc17fa0
    // 0xc17fcc: ldur            x0, [fp, #-0x20]
    // 0xc17fd0: ldur            x4, [fp, #-0x28]
    // 0xc17fd4: ldur            x5, [fp, #-0x38]
    // 0xc17fd8: mov             x3, x1
    // 0xc17fdc: ldur            x1, [fp, #-0x50]
    // 0xc17fe0: r2 = 0
    //     0xc17fe0: movz            x2, #0
    // 0xc17fe4: r0 = insertAll()
    //     0xc17fe4: bl              #0x64d8c4  ; [dart:core] _GrowableList::insertAll
    // 0xc17fe8: ldur            x3, [fp, #-0x28]
    // 0xc17fec: LoadField: r4 = r3->field_13
    //     0xc17fec: ldur            w4, [x3, #0x13]
    // 0xc17ff0: DecompressPointer r4
    //     0xc17ff0: add             x4, x4, HEAP, lsl #32
    // 0xc17ff4: stur            x4, [fp, #-0x50]
    // 0xc17ff8: LoadField: r0 = r4->field_b
    //     0xc17ff8: ldur            w0, [x4, #0xb]
    // 0xc17ffc: r1 = LoadInt32Instr(r0)
    //     0xc17ffc: sbfx            x1, x0, #1, #0x1f
    // 0xc18000: mov             x0, x1
    // 0xc18004: r1 = 0
    //     0xc18004: movz            x1, #0
    // 0xc18008: cmp             x1, x0
    // 0xc1800c: b.hs            #0xc1839c
    // 0xc18010: LoadField: r0 = r4->field_f
    //     0xc18010: ldur            w0, [x4, #0xf]
    // 0xc18014: DecompressPointer r0
    //     0xc18014: add             x0, x0, HEAP, lsl #32
    // 0xc18018: r16 = ""
    //     0xc18018: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc1801c: StoreField: r0->field_f = r16
    //     0xc1801c: stur            w16, [x0, #0xf]
    // 0xc18020: ldur            x0, [fp, #-0x20]
    // 0xc18024: LoadField: r1 = r0->field_f
    //     0xc18024: ldur            w1, [x0, #0xf]
    // 0xc18028: DecompressPointer r1
    //     0xc18028: add             x1, x1, HEAP, lsl #32
    // 0xc1802c: LoadField: r0 = r1->field_b
    //     0xc1802c: ldur            w0, [x1, #0xb]
    // 0xc18030: ldur            x1, [fp, #-0x38]
    // 0xc18034: stur            x0, [fp, #-0x20]
    // 0xc18038: r2 = LoadClassIdInstr(r1)
    //     0xc18038: ldur            x2, [x1, #-1]
    //     0xc1803c: ubfx            x2, x2, #0xc, #0x14
    // 0xc18040: cmp             x2, #0x3af
    // 0xc18044: b.ne            #0xc18058
    // 0xc18048: LoadField: r2 = r1->field_b
    //     0xc18048: ldur            w2, [x1, #0xb]
    // 0xc1804c: DecompressPointer r2
    //     0xc1804c: add             x2, x2, HEAP, lsl #32
    // 0xc18050: mov             x5, x2
    // 0xc18054: b               #0xc1807c
    // 0xc18058: cmp             x2, #0x3b0
    // 0xc1805c: b.ne            #0xc18070
    // 0xc18060: LoadField: r2 = r1->field_b
    //     0xc18060: ldur            w2, [x1, #0xb]
    // 0xc18064: DecompressPointer r2
    //     0xc18064: add             x2, x2, HEAP, lsl #32
    // 0xc18068: mov             x5, x2
    // 0xc1806c: b               #0xc1807c
    // 0xc18070: LoadField: r2 = r1->field_b
    //     0xc18070: ldur            w2, [x1, #0xb]
    // 0xc18074: DecompressPointer r2
    //     0xc18074: add             x2, x2, HEAP, lsl #32
    // 0xc18078: mov             x5, x2
    // 0xc1807c: mov             x2, x0
    // 0xc18080: stur            x5, [fp, #-8]
    // 0xc18084: r1 = <String>
    //     0xc18084: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc18088: r0 = AllocateArray()
    //     0xc18088: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1808c: mov             x2, x0
    // 0xc18090: ldur            x0, [fp, #-0x20]
    // 0xc18094: r3 = LoadInt32Instr(r0)
    //     0xc18094: sbfx            x3, x0, #1, #0x1f
    // 0xc18098: r4 = 0
    //     0xc18098: movz            x4, #0
    // 0xc1809c: CheckStackOverflow
    //     0xc1809c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc180a0: cmp             SP, x16
    //     0xc180a4: b.ls            #0xc183a0
    // 0xc180a8: cmp             x4, x3
    // 0xc180ac: b.ge            #0xc180ec
    // 0xc180b0: mov             x1, x2
    // 0xc180b4: ldur            x0, [fp, #-8]
    // 0xc180b8: ArrayStore: r1[r4] = r0  ; List_4
    //     0xc180b8: add             x25, x1, x4, lsl #2
    //     0xc180bc: add             x25, x25, #0xf
    //     0xc180c0: str             w0, [x25]
    //     0xc180c4: tbz             w0, #0, #0xc180e0
    //     0xc180c8: ldurb           w16, [x1, #-1]
    //     0xc180cc: ldurb           w17, [x0, #-1]
    //     0xc180d0: and             x16, x17, x16, lsr #2
    //     0xc180d4: tst             x16, HEAP, lsr #32
    //     0xc180d8: b.eq            #0xc180e0
    //     0xc180dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc180e0: add             x0, x4, #1
    // 0xc180e4: mov             x4, x0
    // 0xc180e8: b               #0xc1809c
    // 0xc180ec: ldur            x0, [fp, #-0x28]
    // 0xc180f0: ldur            x1, [fp, #-0x50]
    // 0xc180f4: mov             x3, x2
    // 0xc180f8: r2 = 1
    //     0xc180f8: movz            x2, #0x1
    // 0xc180fc: r0 = insertAll()
    //     0xc180fc: bl              #0x64d8c4  ; [dart:core] _GrowableList::insertAll
    // 0xc18100: ldur            x0, [fp, #-0x28]
    // 0xc18104: LoadField: r1 = r0->field_f
    //     0xc18104: ldur            w1, [x0, #0xf]
    // 0xc18108: DecompressPointer r1
    //     0xc18108: add             x1, x1, HEAP, lsl #32
    // 0xc1810c: LoadField: r2 = r1->field_b
    //     0xc1810c: ldur            w2, [x1, #0xb]
    // 0xc18110: r3 = LoadInt32Instr(r2)
    //     0xc18110: sbfx            x3, x2, #1, #0x1f
    // 0xc18114: cbnz            w2, #0xc18128
    // 0xc18118: r0 = "."
    //     0xc18118: ldr             x0, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1811c: LeaveFrame
    //     0xc1811c: mov             SP, fp
    //     0xc18120: ldp             fp, lr, [SP], #0x10
    // 0xc18124: ret
    //     0xc18124: ret             
    // 0xc18128: cmp             x3, #1
    // 0xc1812c: b.le            #0xc18240
    // 0xc18130: r0 = last()
    //     0xc18130: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xc18134: r1 = LoadClassIdInstr(r0)
    //     0xc18134: ldur            x1, [x0, #-1]
    //     0xc18138: ubfx            x1, x1, #0xc, #0x14
    // 0xc1813c: r16 = "."
    //     0xc1813c: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc18140: stp             x16, x0, [SP]
    // 0xc18144: mov             x0, x1
    // 0xc18148: mov             lr, x0
    // 0xc1814c: ldr             lr, [x21, lr, lsl #3]
    // 0xc18150: blr             lr
    // 0xc18154: tbnz            w0, #4, #0xc18240
    // 0xc18158: ldur            x3, [fp, #-0x28]
    // 0xc1815c: LoadField: r2 = r3->field_f
    //     0xc1815c: ldur            w2, [x3, #0xf]
    // 0xc18160: DecompressPointer r2
    //     0xc18160: add             x2, x2, HEAP, lsl #32
    // 0xc18164: LoadField: r0 = r2->field_b
    //     0xc18164: ldur            w0, [x2, #0xb]
    // 0xc18168: r1 = LoadInt32Instr(r0)
    //     0xc18168: sbfx            x1, x0, #1, #0x1f
    // 0xc1816c: sub             x4, x1, #1
    // 0xc18170: mov             x0, x1
    // 0xc18174: mov             x1, x4
    // 0xc18178: cmp             x1, x0
    // 0xc1817c: b.hs            #0xc183a8
    // 0xc18180: mov             x1, x2
    // 0xc18184: mov             x2, x4
    // 0xc18188: r0 = length=()
    //     0xc18188: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc1818c: ldur            x3, [fp, #-0x28]
    // 0xc18190: LoadField: r4 = r3->field_13
    //     0xc18190: ldur            w4, [x3, #0x13]
    // 0xc18194: DecompressPointer r4
    //     0xc18194: add             x4, x4, HEAP, lsl #32
    // 0xc18198: stur            x4, [fp, #-8]
    // 0xc1819c: LoadField: r0 = r4->field_b
    //     0xc1819c: ldur            w0, [x4, #0xb]
    // 0xc181a0: r1 = LoadInt32Instr(r0)
    //     0xc181a0: sbfx            x1, x0, #1, #0x1f
    // 0xc181a4: sub             x2, x1, #1
    // 0xc181a8: mov             x0, x1
    // 0xc181ac: mov             x1, x2
    // 0xc181b0: cmp             x1, x0
    // 0xc181b4: b.hs            #0xc183ac
    // 0xc181b8: mov             x1, x4
    // 0xc181bc: r0 = length=()
    //     0xc181bc: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc181c0: ldur            x3, [fp, #-8]
    // 0xc181c4: LoadField: r0 = r3->field_b
    //     0xc181c4: ldur            w0, [x3, #0xb]
    // 0xc181c8: r1 = LoadInt32Instr(r0)
    //     0xc181c8: sbfx            x1, x0, #1, #0x1f
    // 0xc181cc: sub             x2, x1, #1
    // 0xc181d0: mov             x0, x1
    // 0xc181d4: mov             x1, x2
    // 0xc181d8: cmp             x1, x0
    // 0xc181dc: b.hs            #0xc183b0
    // 0xc181e0: mov             x1, x3
    // 0xc181e4: r0 = length=()
    //     0xc181e4: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xc181e8: ldur            x0, [fp, #-8]
    // 0xc181ec: LoadField: r1 = r0->field_b
    //     0xc181ec: ldur            w1, [x0, #0xb]
    // 0xc181f0: LoadField: r2 = r0->field_f
    //     0xc181f0: ldur            w2, [x0, #0xf]
    // 0xc181f4: DecompressPointer r2
    //     0xc181f4: add             x2, x2, HEAP, lsl #32
    // 0xc181f8: LoadField: r3 = r2->field_b
    //     0xc181f8: ldur            w3, [x2, #0xb]
    // 0xc181fc: r2 = LoadInt32Instr(r1)
    //     0xc181fc: sbfx            x2, x1, #1, #0x1f
    // 0xc18200: stur            x2, [fp, #-0x30]
    // 0xc18204: r1 = LoadInt32Instr(r3)
    //     0xc18204: sbfx            x1, x3, #1, #0x1f
    // 0xc18208: cmp             x2, x1
    // 0xc1820c: b.ne            #0xc18218
    // 0xc18210: mov             x1, x0
    // 0xc18214: r0 = _growToNextCapacity()
    //     0xc18214: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc18218: ldur            x0, [fp, #-8]
    // 0xc1821c: ldur            x1, [fp, #-0x30]
    // 0xc18220: add             x2, x1, #1
    // 0xc18224: lsl             x3, x2, #1
    // 0xc18228: StoreField: r0->field_b = r3
    //     0xc18228: stur            w3, [x0, #0xb]
    // 0xc1822c: LoadField: r2 = r0->field_f
    //     0xc1822c: ldur            w2, [x0, #0xf]
    // 0xc18230: DecompressPointer r2
    //     0xc18230: add             x2, x2, HEAP, lsl #32
    // 0xc18234: add             x0, x2, x1, lsl #2
    // 0xc18238: r16 = ""
    //     0xc18238: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc1823c: StoreField: r0->field_f = r16
    //     0xc1823c: stur            w16, [x0, #0xf]
    // 0xc18240: ldur            x0, [fp, #-0x28]
    // 0xc18244: r1 = ""
    //     0xc18244: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc18248: StoreField: r0->field_b = r1
    //     0xc18248: stur            w1, [x0, #0xb]
    // 0xc1824c: mov             x1, x0
    // 0xc18250: r0 = removeTrailingSeparators()
    //     0xc18250: bl              #0xab2d24  ; [package:path/src/parsed_path.dart] ParsedPath::removeTrailingSeparators
    // 0xc18254: ldur            x16, [fp, #-0x28]
    // 0xc18258: str             x16, [SP]
    // 0xc1825c: r0 = toString()
    //     0xc1825c: bl              #0xc336cc  ; [package:path/src/parsed_path.dart] ParsedPath::toString
    // 0xc18260: LeaveFrame
    //     0xc18260: mov             SP, fp
    //     0xc18264: ldp             fp, lr, [SP], #0x10
    // 0xc18268: ret
    //     0xc18268: ret             
    // 0xc1826c: ldur            x3, [fp, #-0x18]
    // 0xc18270: ldur            x0, [fp, #-0x10]
    // 0xc18274: r1 = Null
    //     0xc18274: mov             x1, NULL
    // 0xc18278: r2 = 10
    //     0xc18278: movz            x2, #0xa
    // 0xc1827c: r0 = AllocateArray()
    //     0xc1827c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc18280: r16 = "Unable to find a path to \""
    //     0xc18280: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bb68] "Unable to find a path to \""
    //     0xc18284: ldr             x16, [x16, #0xb68]
    // 0xc18288: StoreField: r0->field_f = r16
    //     0xc18288: stur            w16, [x0, #0xf]
    // 0xc1828c: ldur            x3, [fp, #-0x10]
    // 0xc18290: StoreField: r0->field_13 = r3
    //     0xc18290: stur            w3, [x0, #0x13]
    // 0xc18294: r16 = "\" from \""
    //     0xc18294: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bb70] "\" from \""
    //     0xc18298: ldr             x16, [x16, #0xb70]
    // 0xc1829c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc1829c: stur            w16, [x0, #0x17]
    // 0xc182a0: ldur            x4, [fp, #-0x18]
    // 0xc182a4: StoreField: r0->field_1b = r4
    //     0xc182a4: stur            w4, [x0, #0x1b]
    // 0xc182a8: r16 = "\"."
    //     0xc182a8: ldr             x16, [PP, #0x378]  ; [pp+0x378] "\"."
    // 0xc182ac: StoreField: r0->field_1f = r16
    //     0xc182ac: stur            w16, [x0, #0x1f]
    // 0xc182b0: str             x0, [SP]
    // 0xc182b4: r0 = _interpolate()
    //     0xc182b4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc182b8: stur            x0, [fp, #-8]
    // 0xc182bc: r0 = PathException()
    //     0xc182bc: bl              #0xc183b4  ; AllocatePathExceptionStub -> PathException (size=0xc)
    // 0xc182c0: mov             x1, x0
    // 0xc182c4: ldur            x0, [fp, #-8]
    // 0xc182c8: StoreField: r1->field_7 = r0
    //     0xc182c8: stur            w0, [x1, #7]
    // 0xc182cc: mov             x0, x1
    // 0xc182d0: r0 = Throw()
    //     0xc182d0: bl              #0xec04b8  ; ThrowStub
    // 0xc182d4: brk             #0
    // 0xc182d8: ldur            x4, [fp, #-0x18]
    // 0xc182dc: ldur            x3, [fp, #-0x10]
    // 0xc182e0: r1 = Null
    //     0xc182e0: mov             x1, NULL
    // 0xc182e4: r2 = 10
    //     0xc182e4: movz            x2, #0xa
    // 0xc182e8: r0 = AllocateArray()
    //     0xc182e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc182ec: r16 = "Unable to find a path to \""
    //     0xc182ec: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bb68] "Unable to find a path to \""
    //     0xc182f0: ldr             x16, [x16, #0xb68]
    // 0xc182f4: StoreField: r0->field_f = r16
    //     0xc182f4: stur            w16, [x0, #0xf]
    // 0xc182f8: ldur            x1, [fp, #-0x10]
    // 0xc182fc: StoreField: r0->field_13 = r1
    //     0xc182fc: stur            w1, [x0, #0x13]
    // 0xc18300: r16 = "\" from \""
    //     0xc18300: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bb70] "\" from \""
    //     0xc18304: ldr             x16, [x16, #0xb70]
    // 0xc18308: ArrayStore: r0[0] = r16  ; List_4
    //     0xc18308: stur            w16, [x0, #0x17]
    // 0xc1830c: ldur            x1, [fp, #-0x18]
    // 0xc18310: StoreField: r0->field_1b = r1
    //     0xc18310: stur            w1, [x0, #0x1b]
    // 0xc18314: r16 = "\"."
    //     0xc18314: ldr             x16, [PP, #0x378]  ; [pp+0x378] "\"."
    // 0xc18318: StoreField: r0->field_1f = r16
    //     0xc18318: stur            w16, [x0, #0x1f]
    // 0xc1831c: str             x0, [SP]
    // 0xc18320: r0 = _interpolate()
    //     0xc18320: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc18324: stur            x0, [fp, #-8]
    // 0xc18328: r0 = PathException()
    //     0xc18328: bl              #0xc183b4  ; AllocatePathExceptionStub -> PathException (size=0xc)
    // 0xc1832c: mov             x1, x0
    // 0xc18330: ldur            x0, [fp, #-8]
    // 0xc18334: StoreField: r1->field_7 = r0
    //     0xc18334: stur            w0, [x1, #7]
    // 0xc18338: mov             x0, x1
    // 0xc1833c: r0 = Throw()
    //     0xc1833c: bl              #0xec04b8  ; ThrowStub
    // 0xc18340: brk             #0
    // 0xc18344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18348: b               #0xc17728
    // 0xc1834c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1834c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18354: b               #0xc1799c
    // 0xc18358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18358: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc1835c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1835c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18360: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18368: b               #0xc17acc
    // 0xc1836c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1836c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18374: b               #0xc17bf8
    // 0xc18378: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18378: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc1837c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1837c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18380: b               #0xc17d20
    // 0xc18384: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18384: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1838c: b               #0xc17e4c
    // 0xc18390: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18390: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18398: b               #0xc17fac
    // 0xc1839c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1839c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc183a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc183a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc183a4: b               #0xc180a8
    // 0xc183a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc183a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc183ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc183ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc183b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc183b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ fromUri(/* No info */) {
    // ** addr: 0xc183c0, size: 0x88
    // 0xc183c0: EnterFrame
    //     0xc183c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc183c4: mov             fp, SP
    // 0xc183c8: AllocStack(0x8)
    //     0xc183c8: sub             SP, SP, #8
    // 0xc183cc: CheckStackOverflow
    //     0xc183cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc183d0: cmp             SP, x16
    //     0xc183d4: b.ls            #0xc18440
    // 0xc183d8: LoadField: r0 = r1->field_7
    //     0xc183d8: ldur            w0, [x1, #7]
    // 0xc183dc: DecompressPointer r0
    //     0xc183dc: add             x0, x0, HEAP, lsl #32
    // 0xc183e0: r1 = LoadClassIdInstr(r0)
    //     0xc183e0: ldur            x1, [x0, #-1]
    //     0xc183e4: ubfx            x1, x1, #0xc, #0x14
    // 0xc183e8: cmp             x1, #0x3b0
    // 0xc183ec: b.ne            #0xc18414
    // 0xc183f0: r0 = LoadClassIdInstr(r2)
    //     0xc183f0: ldur            x0, [x2, #-1]
    //     0xc183f4: ubfx            x0, x0, #0xc, #0x14
    // 0xc183f8: str             x2, [SP]
    // 0xc183fc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc183fc: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc18400: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc18400: movz            x17, #0x2b03
    //     0xc18404: add             lr, x0, x17
    //     0xc18408: ldr             lr, [x21, lr, lsl #3]
    //     0xc1840c: blr             lr
    // 0xc18410: b               #0xc18434
    // 0xc18414: r1 = LoadClassIdInstr(r0)
    //     0xc18414: ldur            x1, [x0, #-1]
    //     0xc18418: ubfx            x1, x1, #0xc, #0x14
    // 0xc1841c: mov             x16, x0
    // 0xc18420: mov             x0, x1
    // 0xc18424: mov             x1, x16
    // 0xc18428: r0 = GDT[cid_x0 + -0xf6e]()
    //     0xc18428: sub             lr, x0, #0xf6e
    //     0xc1842c: ldr             lr, [x21, lr, lsl #3]
    //     0xc18430: blr             lr
    // 0xc18434: LeaveFrame
    //     0xc18434: mov             SP, fp
    //     0xc18438: ldp             fp, lr, [SP], #0x10
    // 0xc1843c: ret
    //     0xc1843c: ret             
    // 0xc18440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18444: b               #0xc183d8
  }
}
