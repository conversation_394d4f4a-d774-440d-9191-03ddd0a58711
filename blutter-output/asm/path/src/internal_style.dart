// lib: , url: package:path/src/internal_style.dart

// class id: 1050754, size: 0x8
class :: {
}

// class id: 942, size: 0x8, field offset: 0x8
abstract class InternalStyle extends Style {

  _ getRoot(/* No info */) {
    // ** addr: 0x832fd8, size: 0x1a0
    // 0x832fd8: EnterFrame
    //     0x832fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x832fdc: mov             fp, SP
    // 0x832fe0: AllocStack(0x28)
    //     0x832fe0: sub             SP, SP, #0x28
    // 0x832fe4: SetupParameters(InternalStyle this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x832fe4: mov             x4, x1
    //     0x832fe8: mov             x3, x2
    //     0x832fec: stur            x1, [fp, #-0x10]
    //     0x832ff0: stur            x2, [fp, #-0x18]
    // 0x832ff4: CheckStackOverflow
    //     0x832ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x832ff8: cmp             SP, x16
    //     0x832ffc: b.ls            #0x833168
    // 0x833000: r5 = LoadClassIdInstr(r4)
    //     0x833000: ldur            x5, [x4, #-1]
    //     0x833004: ubfx            x5, x5, #0xc, #0x14
    // 0x833008: stur            x5, [fp, #-8]
    // 0x83300c: cmp             x5, #0x3b1
    // 0x833010: b.ne            #0x833070
    // 0x833014: LoadField: r0 = r3->field_7
    //     0x833014: ldur            w0, [x3, #7]
    // 0x833018: cbz             w0, #0x833068
    // 0x83301c: r1 = LoadInt32Instr(r0)
    //     0x83301c: sbfx            x1, x0, #1, #0x1f
    // 0x833020: mov             x0, x1
    // 0x833024: r1 = 0
    //     0x833024: movz            x1, #0
    // 0x833028: cmp             x1, x0
    // 0x83302c: b.hs            #0x833170
    // 0x833030: r0 = LoadClassIdInstr(r3)
    //     0x833030: ldur            x0, [x3, #-1]
    //     0x833034: ubfx            x0, x0, #0xc, #0x14
    // 0x833038: lsl             x0, x0, #1
    // 0x83303c: cmp             w0, #0xbc
    // 0x833040: b.ne            #0x833054
    // 0x833044: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0x833044: ldrb            w0, [x3, #0xf]
    // 0x833048: cmp             x0, #0x2f
    // 0x83304c: b.ne            #0x833068
    // 0x833050: b               #0x833060
    // 0x833054: ldurh           w0, [x3, #0xf]
    // 0x833058: cmp             x0, #0x2f
    // 0x83305c: b.ne            #0x833068
    // 0x833060: r2 = 1
    //     0x833060: movz            x2, #0x1
    // 0x833064: b               #0x833094
    // 0x833068: r2 = 0
    //     0x833068: movz            x2, #0
    // 0x83306c: b               #0x833094
    // 0x833070: r0 = LoadClassIdInstr(r4)
    //     0x833070: ldur            x0, [x4, #-1]
    //     0x833074: ubfx            x0, x0, #0xc, #0x14
    // 0x833078: mov             x1, x4
    // 0x83307c: mov             x2, x3
    // 0x833080: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x833080: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x833084: r0 = GDT[cid_x0 + -0xffd]()
    //     0x833084: sub             lr, x0, #0xffd
    //     0x833088: ldr             lr, [x21, lr, lsl #3]
    //     0x83308c: blr             lr
    // 0x833090: mov             x2, x0
    // 0x833094: cmp             x2, #0
    // 0x833098: b.le            #0x8330d0
    // 0x83309c: r0 = BoxInt64Instr(r2)
    //     0x83309c: sbfiz           x0, x2, #1, #0x1f
    //     0x8330a0: cmp             x2, x0, asr #1
    //     0x8330a4: b.eq            #0x8330b0
    //     0x8330a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8330ac: stur            x2, [x0, #7]
    // 0x8330b0: str             x0, [SP]
    // 0x8330b4: ldur            x1, [fp, #-0x18]
    // 0x8330b8: r2 = 0
    //     0x8330b8: movz            x2, #0
    // 0x8330bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8330bc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8330c0: r0 = substring()
    //     0x8330c0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x8330c4: LeaveFrame
    //     0x8330c4: mov             SP, fp
    //     0x8330c8: ldp             fp, lr, [SP], #0x10
    // 0x8330cc: ret
    //     0x8330cc: ret             
    // 0x8330d0: ldur            x0, [fp, #-8]
    // 0x8330d4: cmp             x0, #0x3af
    // 0x8330d8: b.ne            #0x8330fc
    // 0x8330dc: ldur            x1, [fp, #-0x10]
    // 0x8330e0: ldur            x2, [fp, #-0x18]
    // 0x8330e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8330e4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8330e8: r0 = rootLength()
    //     0x8330e8: bl              #0xe8d7a4  ; [package:path/src/style/windows.dart] WindowsStyle::rootLength
    // 0x8330ec: cmp             x0, #1
    // 0x8330f0: b.ne            #0x833158
    // 0x8330f4: ldur            x2, [fp, #-0x18]
    // 0x8330f8: b               #0x83314c
    // 0x8330fc: cmp             x0, #0x3b0
    // 0x833100: b.ne            #0x833158
    // 0x833104: ldur            x2, [fp, #-0x18]
    // 0x833108: LoadField: r0 = r2->field_7
    //     0x833108: ldur            w0, [x2, #7]
    // 0x83310c: cbz             w0, #0x833158
    // 0x833110: r1 = LoadInt32Instr(r0)
    //     0x833110: sbfx            x1, x0, #1, #0x1f
    // 0x833114: mov             x0, x1
    // 0x833118: r1 = 0
    //     0x833118: movz            x1, #0
    // 0x83311c: cmp             x1, x0
    // 0x833120: b.hs            #0x833174
    // 0x833124: r0 = LoadClassIdInstr(r2)
    //     0x833124: ldur            x0, [x2, #-1]
    //     0x833128: ubfx            x0, x0, #0xc, #0x14
    // 0x83312c: lsl             x0, x0, #1
    // 0x833130: cmp             w0, #0xbc
    // 0x833134: b.ne            #0x833140
    // 0x833138: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x833138: ldrb            w0, [x2, #0xf]
    // 0x83313c: b               #0x833144
    // 0x833140: ldurh           w0, [x2, #0xf]
    // 0x833144: cmp             x0, #0x2f
    // 0x833148: b.ne            #0x833158
    // 0x83314c: stp             xzr, x2, [SP]
    // 0x833150: r0 = []()
    //     0x833150: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x833154: b               #0x83315c
    // 0x833158: r0 = Null
    //     0x833158: mov             x0, NULL
    // 0x83315c: LeaveFrame
    //     0x83315c: mov             SP, fp
    //     0x833160: ldp             fp, lr, [SP], #0x10
    // 0x833164: ret
    //     0x833164: ret             
    // 0x833168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83316c: b               #0x833000
    // 0x833170: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x833170: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x833174: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x833174: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ pathsEqual(/* No info */) {
    // ** addr: 0xe8b574, size: 0x44
    // 0xe8b574: EnterFrame
    //     0xe8b574: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b578: mov             fp, SP
    // 0xe8b57c: AllocStack(0x10)
    //     0xe8b57c: sub             SP, SP, #0x10
    // 0xe8b580: CheckStackOverflow
    //     0xe8b580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b584: cmp             SP, x16
    //     0xe8b588: b.ls            #0xe8b5b0
    // 0xe8b58c: r0 = LoadClassIdInstr(r2)
    //     0xe8b58c: ldur            x0, [x2, #-1]
    //     0xe8b590: ubfx            x0, x0, #0xc, #0x14
    // 0xe8b594: stp             x3, x2, [SP]
    // 0xe8b598: mov             lr, x0
    // 0xe8b59c: ldr             lr, [x21, lr, lsl #3]
    // 0xe8b5a0: blr             lr
    // 0xe8b5a4: LeaveFrame
    //     0xe8b5a4: mov             SP, fp
    //     0xe8b5a8: ldp             fp, lr, [SP], #0x10
    // 0xe8b5ac: ret
    //     0xe8b5ac: ret             
    // 0xe8b5b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b5b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b5b4: b               #0xe8b58c
  }
}
