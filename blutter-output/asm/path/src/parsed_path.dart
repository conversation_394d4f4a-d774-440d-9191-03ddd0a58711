// lib: , url: package:path/src/parsed_path.dart

// class id: 1050755, size: 0x8
class :: {
}

// class id: 940, size: 0x18, field offset: 0x8
class ParsedPath extends Object {

  factory _ ParsedPath.parse(/* No info */) {
    // ** addr: 0x8329d4, size: 0x5f8
    // 0x8329d4: EnterFrame
    //     0x8329d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8329d8: mov             fp, SP
    // 0x8329dc: AllocStack(0x78)
    //     0x8329dc: sub             SP, SP, #0x78
    // 0x8329e0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x8329e0: mov             x0, x3
    //     0x8329e4: stur            x3, [fp, #-0x10]
    //     0x8329e8: mov             x3, x2
    //     0x8329ec: stur            x2, [fp, #-8]
    // 0x8329f0: CheckStackOverflow
    //     0x8329f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8329f4: cmp             SP, x16
    //     0x8329f8: b.ls            #0x832fb4
    // 0x8329fc: mov             x1, x0
    // 0x832a00: mov             x2, x3
    // 0x832a04: r0 = getRoot()
    //     0x832a04: bl              #0x832fd8  ; [package:path/src/internal_style.dart] InternalStyle::getRoot
    // 0x832a08: mov             x3, x0
    // 0x832a0c: ldur            x0, [fp, #-0x10]
    // 0x832a10: stur            x3, [fp, #-0x20]
    // 0x832a14: r4 = LoadClassIdInstr(r0)
    //     0x832a14: ldur            x4, [x0, #-1]
    //     0x832a18: ubfx            x4, x4, #0xc, #0x14
    // 0x832a1c: stur            x4, [fp, #-0x18]
    // 0x832a20: cmp             x4, #0x3af
    // 0x832a24: b.ne            #0x832a44
    // 0x832a28: mov             x1, x0
    // 0x832a2c: ldur            x2, [fp, #-8]
    // 0x832a30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832a30: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832a34: r0 = rootLength()
    //     0x832a34: bl              #0xe8d7a4  ; [package:path/src/style/windows.dart] WindowsStyle::rootLength
    // 0x832a38: ldur            x2, [fp, #-8]
    // 0x832a3c: ldur            x3, [fp, #-0x18]
    // 0x832a40: b               #0x832a78
    // 0x832a44: mov             x3, x4
    // 0x832a48: cmp             x3, #0x3b0
    // 0x832a4c: b.ne            #0x832a74
    // 0x832a50: ldur            x2, [fp, #-8]
    // 0x832a54: LoadField: r0 = r2->field_7
    //     0x832a54: ldur            w0, [x2, #7]
    // 0x832a58: cbz             w0, #0x832a78
    // 0x832a5c: r1 = LoadInt32Instr(r0)
    //     0x832a5c: sbfx            x1, x0, #1, #0x1f
    // 0x832a60: mov             x0, x1
    // 0x832a64: r1 = 0
    //     0x832a64: movz            x1, #0
    // 0x832a68: cmp             x1, x0
    // 0x832a6c: b.hs            #0x832fbc
    // 0x832a70: b               #0x832a78
    // 0x832a74: ldur            x2, [fp, #-8]
    // 0x832a78: ldur            x0, [fp, #-0x20]
    // 0x832a7c: cmp             w0, NULL
    // 0x832a80: b.eq            #0x832aa0
    // 0x832a84: LoadField: r1 = r0->field_7
    //     0x832a84: ldur            w1, [x0, #7]
    // 0x832a88: r4 = LoadInt32Instr(r1)
    //     0x832a88: sbfx            x4, x1, #1, #0x1f
    // 0x832a8c: mov             x1, x2
    // 0x832a90: mov             x2, x4
    // 0x832a94: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832a94: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832a98: r0 = substring()
    //     0x832a98: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x832a9c: b               #0x832aa4
    // 0x832aa0: mov             x0, x2
    // 0x832aa4: stur            x0, [fp, #-8]
    // 0x832aa8: r1 = <String>
    //     0x832aa8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x832aac: r2 = 0
    //     0x832aac: movz            x2, #0
    // 0x832ab0: r0 = _GrowableList()
    //     0x832ab0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x832ab4: r1 = <String>
    //     0x832ab4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x832ab8: r2 = 0
    //     0x832ab8: movz            x2, #0
    // 0x832abc: stur            x0, [fp, #-0x28]
    // 0x832ac0: r0 = _GrowableList()
    //     0x832ac0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x832ac4: mov             x3, x0
    // 0x832ac8: ldur            x2, [fp, #-8]
    // 0x832acc: stur            x3, [fp, #-0x38]
    // 0x832ad0: LoadField: r4 = r2->field_7
    //     0x832ad0: ldur            w4, [x2, #7]
    // 0x832ad4: stur            x4, [fp, #-0x30]
    // 0x832ad8: cbz             w4, #0x832bf4
    // 0x832adc: r0 = LoadInt32Instr(r4)
    //     0x832adc: sbfx            x0, x4, #1, #0x1f
    // 0x832ae0: r1 = 0
    //     0x832ae0: movz            x1, #0
    // 0x832ae4: cmp             x1, x0
    // 0x832ae8: b.hs            #0x832fc0
    // 0x832aec: r0 = LoadClassIdInstr(r2)
    //     0x832aec: ldur            x0, [x2, #-1]
    //     0x832af0: ubfx            x0, x0, #0xc, #0x14
    // 0x832af4: lsl             x0, x0, #1
    // 0x832af8: cmp             w0, #0xbc
    // 0x832afc: b.ne            #0x832b0c
    // 0x832b00: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x832b00: ldrb            w0, [x2, #0xf]
    // 0x832b04: mov             x1, x0
    // 0x832b08: b               #0x832b14
    // 0x832b0c: ldurh           w0, [x2, #0xf]
    // 0x832b10: mov             x1, x0
    // 0x832b14: ldur            x0, [fp, #-0x18]
    // 0x832b18: cmp             x0, #0x3af
    // 0x832b1c: b.ne            #0x832b38
    // 0x832b20: cmp             x1, #0x2f
    // 0x832b24: b.eq            #0x832b58
    // 0x832b28: cmp             x1, #0x5c
    // 0x832b2c: b.eq            #0x832b58
    // 0x832b30: mov             x2, x3
    // 0x832b34: b               #0x832bf8
    // 0x832b38: cmp             x0, #0x3b0
    // 0x832b3c: b.ne            #0x832b50
    // 0x832b40: cmp             x1, #0x2f
    // 0x832b44: b.eq            #0x832b58
    // 0x832b48: mov             x2, x3
    // 0x832b4c: b               #0x832bf8
    // 0x832b50: cmp             x1, #0x2f
    // 0x832b54: b.ne            #0x832bec
    // 0x832b58: stp             xzr, x2, [SP]
    // 0x832b5c: r0 = []()
    //     0x832b5c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x832b60: mov             x2, x0
    // 0x832b64: ldur            x0, [fp, #-0x38]
    // 0x832b68: stur            x2, [fp, #-0x48]
    // 0x832b6c: LoadField: r1 = r0->field_b
    //     0x832b6c: ldur            w1, [x0, #0xb]
    // 0x832b70: LoadField: r3 = r0->field_f
    //     0x832b70: ldur            w3, [x0, #0xf]
    // 0x832b74: DecompressPointer r3
    //     0x832b74: add             x3, x3, HEAP, lsl #32
    // 0x832b78: LoadField: r4 = r3->field_b
    //     0x832b78: ldur            w4, [x3, #0xb]
    // 0x832b7c: r3 = LoadInt32Instr(r1)
    //     0x832b7c: sbfx            x3, x1, #1, #0x1f
    // 0x832b80: stur            x3, [fp, #-0x40]
    // 0x832b84: r1 = LoadInt32Instr(r4)
    //     0x832b84: sbfx            x1, x4, #1, #0x1f
    // 0x832b88: cmp             x3, x1
    // 0x832b8c: b.ne            #0x832b98
    // 0x832b90: mov             x1, x0
    // 0x832b94: r0 = _growToNextCapacity()
    //     0x832b94: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832b98: ldur            x2, [fp, #-0x38]
    // 0x832b9c: ldur            x3, [fp, #-0x40]
    // 0x832ba0: add             x0, x3, #1
    // 0x832ba4: lsl             x1, x0, #1
    // 0x832ba8: StoreField: r2->field_b = r1
    //     0x832ba8: stur            w1, [x2, #0xb]
    // 0x832bac: LoadField: r1 = r2->field_f
    //     0x832bac: ldur            w1, [x2, #0xf]
    // 0x832bb0: DecompressPointer r1
    //     0x832bb0: add             x1, x1, HEAP, lsl #32
    // 0x832bb4: ldur            x0, [fp, #-0x48]
    // 0x832bb8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x832bb8: add             x25, x1, x3, lsl #2
    //     0x832bbc: add             x25, x25, #0xf
    //     0x832bc0: str             w0, [x25]
    //     0x832bc4: tbz             w0, #0, #0x832be0
    //     0x832bc8: ldurb           w16, [x1, #-1]
    //     0x832bcc: ldurb           w17, [x0, #-1]
    //     0x832bd0: and             x16, x17, x16, lsr #2
    //     0x832bd4: tst             x16, HEAP, lsr #32
    //     0x832bd8: b.eq            #0x832be0
    //     0x832bdc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x832be0: mov             x0, x2
    // 0x832be4: r2 = 1
    //     0x832be4: movz            x2, #0x1
    // 0x832be8: b               #0x832c50
    // 0x832bec: mov             x2, x3
    // 0x832bf0: b               #0x832bf8
    // 0x832bf4: mov             x2, x3
    // 0x832bf8: LoadField: r0 = r2->field_b
    //     0x832bf8: ldur            w0, [x2, #0xb]
    // 0x832bfc: LoadField: r1 = r2->field_f
    //     0x832bfc: ldur            w1, [x2, #0xf]
    // 0x832c00: DecompressPointer r1
    //     0x832c00: add             x1, x1, HEAP, lsl #32
    // 0x832c04: LoadField: r3 = r1->field_b
    //     0x832c04: ldur            w3, [x1, #0xb]
    // 0x832c08: r4 = LoadInt32Instr(r0)
    //     0x832c08: sbfx            x4, x0, #1, #0x1f
    // 0x832c0c: stur            x4, [fp, #-0x40]
    // 0x832c10: r0 = LoadInt32Instr(r3)
    //     0x832c10: sbfx            x0, x3, #1, #0x1f
    // 0x832c14: cmp             x4, x0
    // 0x832c18: b.ne            #0x832c24
    // 0x832c1c: mov             x1, x2
    // 0x832c20: r0 = _growToNextCapacity()
    //     0x832c20: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832c24: ldur            x0, [fp, #-0x38]
    // 0x832c28: ldur            x1, [fp, #-0x40]
    // 0x832c2c: add             x2, x1, #1
    // 0x832c30: lsl             x3, x2, #1
    // 0x832c34: StoreField: r0->field_b = r3
    //     0x832c34: stur            w3, [x0, #0xb]
    // 0x832c38: LoadField: r2 = r0->field_f
    //     0x832c38: ldur            w2, [x0, #0xf]
    // 0x832c3c: DecompressPointer r2
    //     0x832c3c: add             x2, x2, HEAP, lsl #32
    // 0x832c40: add             x3, x2, x1, lsl #2
    // 0x832c44: r16 = ""
    //     0x832c44: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x832c48: StoreField: r3->field_f = r16
    //     0x832c48: stur            w16, [x3, #0xf]
    // 0x832c4c: r2 = 0
    //     0x832c4c: movz            x2, #0
    // 0x832c50: ldur            x4, [fp, #-8]
    // 0x832c54: ldur            x1, [fp, #-0x30]
    // 0x832c58: r5 = LoadInt32Instr(r1)
    //     0x832c58: sbfx            x5, x1, #1, #0x1f
    // 0x832c5c: stur            x5, [fp, #-0x58]
    // 0x832c60: r6 = LoadClassIdInstr(r4)
    //     0x832c60: ldur            x6, [x4, #-1]
    //     0x832c64: ubfx            x6, x6, #0xc, #0x14
    // 0x832c68: lsl             x6, x6, #1
    // 0x832c6c: stur            x6, [fp, #-0x48]
    // 0x832c70: mov             x10, x2
    // 0x832c74: mov             x9, x2
    // 0x832c78: ldur            x8, [fp, #-0x28]
    // 0x832c7c: ldur            x7, [fp, #-0x18]
    // 0x832c80: stur            x10, [fp, #-0x40]
    // 0x832c84: stur            x9, [fp, #-0x50]
    // 0x832c88: CheckStackOverflow
    //     0x832c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x832c8c: cmp             SP, x16
    //     0x832c90: b.ls            #0x832fc4
    // 0x832c94: cmp             x9, x5
    // 0x832c98: b.ge            #0x832e74
    // 0x832c9c: cmp             w6, #0xbc
    // 0x832ca0: b.ne            #0x832cb0
    // 0x832ca4: ArrayLoad: r1 = r4[r9]  ; TypedUnsigned_1
    //     0x832ca4: add             x16, x4, x9
    //     0x832ca8: ldrb            w1, [x16, #0xf]
    // 0x832cac: b               #0x832cb8
    // 0x832cb0: add             x16, x4, x9, lsl #1
    // 0x832cb4: ldurh           w1, [x16, #0xf]
    // 0x832cb8: cmp             x7, #0x3af
    // 0x832cbc: b.ne            #0x832cdc
    // 0x832cc0: cmp             x1, #0x2f
    // 0x832cc4: b.eq            #0x832d00
    // 0x832cc8: cmp             x1, #0x5c
    // 0x832ccc: b.eq            #0x832d00
    // 0x832cd0: mov             x3, x0
    // 0x832cd4: mov             x4, x9
    // 0x832cd8: b               #0x832e58
    // 0x832cdc: cmp             x7, #0x3b0
    // 0x832ce0: b.ne            #0x832cf8
    // 0x832ce4: cmp             x1, #0x2f
    // 0x832ce8: b.eq            #0x832d00
    // 0x832cec: mov             x3, x0
    // 0x832cf0: mov             x4, x9
    // 0x832cf4: b               #0x832e58
    // 0x832cf8: cmp             x1, #0x2f
    // 0x832cfc: b.ne            #0x832e50
    // 0x832d00: lsl             x11, x9, #1
    // 0x832d04: mov             x1, x10
    // 0x832d08: mov             x2, x11
    // 0x832d0c: mov             x3, x5
    // 0x832d10: stur            x11, [fp, #-0x30]
    // 0x832d14: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x832d14: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x832d18: r0 = checkValidRange()
    //     0x832d18: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x832d1c: ldur            x1, [fp, #-8]
    // 0x832d20: ldur            x2, [fp, #-0x40]
    // 0x832d24: mov             x3, x0
    // 0x832d28: r0 = _substringUnchecked()
    //     0x832d28: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x832d2c: mov             x2, x0
    // 0x832d30: ldur            x0, [fp, #-0x28]
    // 0x832d34: stur            x2, [fp, #-0x68]
    // 0x832d38: LoadField: r1 = r0->field_b
    //     0x832d38: ldur            w1, [x0, #0xb]
    // 0x832d3c: LoadField: r3 = r0->field_f
    //     0x832d3c: ldur            w3, [x0, #0xf]
    // 0x832d40: DecompressPointer r3
    //     0x832d40: add             x3, x3, HEAP, lsl #32
    // 0x832d44: LoadField: r4 = r3->field_b
    //     0x832d44: ldur            w4, [x3, #0xb]
    // 0x832d48: r3 = LoadInt32Instr(r1)
    //     0x832d48: sbfx            x3, x1, #1, #0x1f
    // 0x832d4c: stur            x3, [fp, #-0x60]
    // 0x832d50: r1 = LoadInt32Instr(r4)
    //     0x832d50: sbfx            x1, x4, #1, #0x1f
    // 0x832d54: cmp             x3, x1
    // 0x832d58: b.ne            #0x832d64
    // 0x832d5c: mov             x1, x0
    // 0x832d60: r0 = _growToNextCapacity()
    //     0x832d60: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832d64: ldur            x2, [fp, #-0x28]
    // 0x832d68: ldur            x4, [fp, #-0x38]
    // 0x832d6c: ldur            x3, [fp, #-0x60]
    // 0x832d70: add             x0, x3, #1
    // 0x832d74: lsl             x1, x0, #1
    // 0x832d78: StoreField: r2->field_b = r1
    //     0x832d78: stur            w1, [x2, #0xb]
    // 0x832d7c: LoadField: r1 = r2->field_f
    //     0x832d7c: ldur            w1, [x2, #0xf]
    // 0x832d80: DecompressPointer r1
    //     0x832d80: add             x1, x1, HEAP, lsl #32
    // 0x832d84: ldur            x0, [fp, #-0x68]
    // 0x832d88: ArrayStore: r1[r3] = r0  ; List_4
    //     0x832d88: add             x25, x1, x3, lsl #2
    //     0x832d8c: add             x25, x25, #0xf
    //     0x832d90: str             w0, [x25]
    //     0x832d94: tbz             w0, #0, #0x832db0
    //     0x832d98: ldurb           w16, [x1, #-1]
    //     0x832d9c: ldurb           w17, [x0, #-1]
    //     0x832da0: and             x16, x17, x16, lsr #2
    //     0x832da4: tst             x16, HEAP, lsr #32
    //     0x832da8: b.eq            #0x832db0
    //     0x832dac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x832db0: ldur            x16, [fp, #-8]
    // 0x832db4: ldur            lr, [fp, #-0x30]
    // 0x832db8: stp             lr, x16, [SP]
    // 0x832dbc: r0 = []()
    //     0x832dbc: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x832dc0: mov             x2, x0
    // 0x832dc4: ldur            x0, [fp, #-0x38]
    // 0x832dc8: stur            x2, [fp, #-0x30]
    // 0x832dcc: LoadField: r1 = r0->field_b
    //     0x832dcc: ldur            w1, [x0, #0xb]
    // 0x832dd0: LoadField: r3 = r0->field_f
    //     0x832dd0: ldur            w3, [x0, #0xf]
    // 0x832dd4: DecompressPointer r3
    //     0x832dd4: add             x3, x3, HEAP, lsl #32
    // 0x832dd8: LoadField: r4 = r3->field_b
    //     0x832dd8: ldur            w4, [x3, #0xb]
    // 0x832ddc: r3 = LoadInt32Instr(r1)
    //     0x832ddc: sbfx            x3, x1, #1, #0x1f
    // 0x832de0: stur            x3, [fp, #-0x60]
    // 0x832de4: r1 = LoadInt32Instr(r4)
    //     0x832de4: sbfx            x1, x4, #1, #0x1f
    // 0x832de8: cmp             x3, x1
    // 0x832dec: b.ne            #0x832df8
    // 0x832df0: mov             x1, x0
    // 0x832df4: r0 = _growToNextCapacity()
    //     0x832df4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832df8: ldur            x3, [fp, #-0x38]
    // 0x832dfc: ldur            x4, [fp, #-0x50]
    // 0x832e00: ldur            x2, [fp, #-0x60]
    // 0x832e04: add             x0, x2, #1
    // 0x832e08: lsl             x1, x0, #1
    // 0x832e0c: StoreField: r3->field_b = r1
    //     0x832e0c: stur            w1, [x3, #0xb]
    // 0x832e10: LoadField: r1 = r3->field_f
    //     0x832e10: ldur            w1, [x3, #0xf]
    // 0x832e14: DecompressPointer r1
    //     0x832e14: add             x1, x1, HEAP, lsl #32
    // 0x832e18: ldur            x0, [fp, #-0x30]
    // 0x832e1c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x832e1c: add             x25, x1, x2, lsl #2
    //     0x832e20: add             x25, x25, #0xf
    //     0x832e24: str             w0, [x25]
    //     0x832e28: tbz             w0, #0, #0x832e44
    //     0x832e2c: ldurb           w16, [x1, #-1]
    //     0x832e30: ldurb           w17, [x0, #-1]
    //     0x832e34: and             x16, x17, x16, lsr #2
    //     0x832e38: tst             x16, HEAP, lsr #32
    //     0x832e3c: b.eq            #0x832e44
    //     0x832e40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x832e44: add             x0, x4, #1
    // 0x832e48: mov             x10, x0
    // 0x832e4c: b               #0x832e5c
    // 0x832e50: mov             x3, x0
    // 0x832e54: mov             x4, x9
    // 0x832e58: ldur            x10, [fp, #-0x40]
    // 0x832e5c: add             x9, x4, #1
    // 0x832e60: ldur            x4, [fp, #-8]
    // 0x832e64: mov             x0, x3
    // 0x832e68: ldur            x6, [fp, #-0x48]
    // 0x832e6c: ldur            x5, [fp, #-0x58]
    // 0x832e70: b               #0x832c78
    // 0x832e74: mov             x3, x0
    // 0x832e78: mov             x2, x10
    // 0x832e7c: mov             x0, x5
    // 0x832e80: cmp             x2, x0
    // 0x832e84: b.ge            #0x832f74
    // 0x832e88: ldur            x0, [fp, #-0x28]
    // 0x832e8c: ldur            x1, [fp, #-8]
    // 0x832e90: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x832e90: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x832e94: r0 = substring()
    //     0x832e94: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x832e98: mov             x2, x0
    // 0x832e9c: ldur            x0, [fp, #-0x28]
    // 0x832ea0: stur            x2, [fp, #-8]
    // 0x832ea4: LoadField: r1 = r0->field_b
    //     0x832ea4: ldur            w1, [x0, #0xb]
    // 0x832ea8: LoadField: r3 = r0->field_f
    //     0x832ea8: ldur            w3, [x0, #0xf]
    // 0x832eac: DecompressPointer r3
    //     0x832eac: add             x3, x3, HEAP, lsl #32
    // 0x832eb0: LoadField: r4 = r3->field_b
    //     0x832eb0: ldur            w4, [x3, #0xb]
    // 0x832eb4: r3 = LoadInt32Instr(r1)
    //     0x832eb4: sbfx            x3, x1, #1, #0x1f
    // 0x832eb8: stur            x3, [fp, #-0x18]
    // 0x832ebc: r1 = LoadInt32Instr(r4)
    //     0x832ebc: sbfx            x1, x4, #1, #0x1f
    // 0x832ec0: cmp             x3, x1
    // 0x832ec4: b.ne            #0x832ed0
    // 0x832ec8: mov             x1, x0
    // 0x832ecc: r0 = _growToNextCapacity()
    //     0x832ecc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832ed0: ldur            x2, [fp, #-0x28]
    // 0x832ed4: ldur            x4, [fp, #-0x38]
    // 0x832ed8: ldur            x3, [fp, #-0x18]
    // 0x832edc: add             x0, x3, #1
    // 0x832ee0: lsl             x1, x0, #1
    // 0x832ee4: StoreField: r2->field_b = r1
    //     0x832ee4: stur            w1, [x2, #0xb]
    // 0x832ee8: LoadField: r1 = r2->field_f
    //     0x832ee8: ldur            w1, [x2, #0xf]
    // 0x832eec: DecompressPointer r1
    //     0x832eec: add             x1, x1, HEAP, lsl #32
    // 0x832ef0: ldur            x0, [fp, #-8]
    // 0x832ef4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x832ef4: add             x25, x1, x3, lsl #2
    //     0x832ef8: add             x25, x25, #0xf
    //     0x832efc: str             w0, [x25]
    //     0x832f00: tbz             w0, #0, #0x832f1c
    //     0x832f04: ldurb           w16, [x1, #-1]
    //     0x832f08: ldurb           w17, [x0, #-1]
    //     0x832f0c: and             x16, x17, x16, lsr #2
    //     0x832f10: tst             x16, HEAP, lsr #32
    //     0x832f14: b.eq            #0x832f1c
    //     0x832f18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x832f1c: LoadField: r0 = r4->field_b
    //     0x832f1c: ldur            w0, [x4, #0xb]
    // 0x832f20: LoadField: r1 = r4->field_f
    //     0x832f20: ldur            w1, [x4, #0xf]
    // 0x832f24: DecompressPointer r1
    //     0x832f24: add             x1, x1, HEAP, lsl #32
    // 0x832f28: LoadField: r3 = r1->field_b
    //     0x832f28: ldur            w3, [x1, #0xb]
    // 0x832f2c: r5 = LoadInt32Instr(r0)
    //     0x832f2c: sbfx            x5, x0, #1, #0x1f
    // 0x832f30: stur            x5, [fp, #-0x18]
    // 0x832f34: r0 = LoadInt32Instr(r3)
    //     0x832f34: sbfx            x0, x3, #1, #0x1f
    // 0x832f38: cmp             x5, x0
    // 0x832f3c: b.ne            #0x832f48
    // 0x832f40: mov             x1, x4
    // 0x832f44: r0 = _growToNextCapacity()
    //     0x832f44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x832f48: ldur            x0, [fp, #-0x38]
    // 0x832f4c: ldur            x1, [fp, #-0x18]
    // 0x832f50: add             x2, x1, #1
    // 0x832f54: lsl             x3, x2, #1
    // 0x832f58: StoreField: r0->field_b = r3
    //     0x832f58: stur            w3, [x0, #0xb]
    // 0x832f5c: LoadField: r2 = r0->field_f
    //     0x832f5c: ldur            w2, [x0, #0xf]
    // 0x832f60: DecompressPointer r2
    //     0x832f60: add             x2, x2, HEAP, lsl #32
    // 0x832f64: add             x3, x2, x1, lsl #2
    // 0x832f68: r16 = ""
    //     0x832f68: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x832f6c: StoreField: r3->field_f = r16
    //     0x832f6c: stur            w16, [x3, #0xf]
    // 0x832f70: b               #0x832f78
    // 0x832f74: mov             x0, x3
    // 0x832f78: ldur            x3, [fp, #-0x10]
    // 0x832f7c: ldur            x2, [fp, #-0x20]
    // 0x832f80: ldur            x1, [fp, #-0x28]
    // 0x832f84: r0 = ParsedPath()
    //     0x832f84: bl              #0x832fcc  ; AllocateParsedPathStub -> ParsedPath (size=0x18)
    // 0x832f88: ldur            x1, [fp, #-0x10]
    // 0x832f8c: StoreField: r0->field_7 = r1
    //     0x832f8c: stur            w1, [x0, #7]
    // 0x832f90: ldur            x1, [fp, #-0x20]
    // 0x832f94: StoreField: r0->field_b = r1
    //     0x832f94: stur            w1, [x0, #0xb]
    // 0x832f98: ldur            x1, [fp, #-0x28]
    // 0x832f9c: StoreField: r0->field_f = r1
    //     0x832f9c: stur            w1, [x0, #0xf]
    // 0x832fa0: ldur            x1, [fp, #-0x38]
    // 0x832fa4: StoreField: r0->field_13 = r1
    //     0x832fa4: stur            w1, [x0, #0x13]
    // 0x832fa8: LeaveFrame
    //     0x832fa8: mov             SP, fp
    //     0x832fac: ldp             fp, lr, [SP], #0x10
    // 0x832fb0: ret
    //     0x832fb0: ret             
    // 0x832fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x832fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x832fb8: b               #0x8329fc
    // 0x832fbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x832fbc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x832fc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x832fc0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x832fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x832fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x832fc8: b               #0x832c94
  }
  _ normalize(/* No info */) {
    // ** addr: 0xab271c, size: 0x608
    // 0xab271c: EnterFrame
    //     0xab271c: stp             fp, lr, [SP, #-0x10]!
    //     0xab2720: mov             fp, SP
    // 0xab2724: AllocStack(0x50)
    //     0xab2724: sub             SP, SP, #0x50
    // 0xab2728: SetupParameters(ParsedPath this /* r1 => r0, fp-0x8 */)
    //     0xab2728: mov             x0, x1
    //     0xab272c: stur            x1, [fp, #-8]
    // 0xab2730: CheckStackOverflow
    //     0xab2730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2734: cmp             SP, x16
    //     0xab2738: b.ls            #0xab2cf8
    // 0xab273c: r1 = <String>
    //     0xab273c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xab2740: r2 = 0
    //     0xab2740: movz            x2, #0
    // 0xab2744: r0 = _GrowableList()
    //     0xab2744: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xab2748: mov             x2, x0
    // 0xab274c: ldur            x1, [fp, #-8]
    // 0xab2750: stur            x2, [fp, #-0x38]
    // 0xab2754: LoadField: r3 = r1->field_f
    //     0xab2754: ldur            w3, [x1, #0xf]
    // 0xab2758: DecompressPointer r3
    //     0xab2758: add             x3, x3, HEAP, lsl #32
    // 0xab275c: stur            x3, [fp, #-0x30]
    // 0xab2760: LoadField: r0 = r3->field_b
    //     0xab2760: ldur            w0, [x3, #0xb]
    // 0xab2764: r4 = LoadInt32Instr(r0)
    //     0xab2764: sbfx            x4, x0, #1, #0x1f
    // 0xab2768: stur            x4, [fp, #-0x28]
    // 0xab276c: r5 = 0
    //     0xab276c: movz            x5, #0
    // 0xab2770: r0 = 0
    //     0xab2770: movz            x0, #0
    // 0xab2774: stur            x5, [fp, #-0x20]
    // 0xab2778: CheckStackOverflow
    //     0xab2778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab277c: cmp             SP, x16
    //     0xab2780: b.ls            #0xab2d00
    // 0xab2784: LoadField: r6 = r3->field_b
    //     0xab2784: ldur            w6, [x3, #0xb]
    // 0xab2788: r7 = LoadInt32Instr(r6)
    //     0xab2788: sbfx            x7, x6, #1, #0x1f
    // 0xab278c: cmp             x4, x7
    // 0xab2790: b.ne            #0xab2cd8
    // 0xab2794: cmp             x0, x7
    // 0xab2798: b.ge            #0xab2910
    // 0xab279c: LoadField: r6 = r3->field_f
    //     0xab279c: ldur            w6, [x3, #0xf]
    // 0xab27a0: DecompressPointer r6
    //     0xab27a0: add             x6, x6, HEAP, lsl #32
    // 0xab27a4: ArrayLoad: r7 = r6[r0]  ; Unknown_4
    //     0xab27a4: add             x16, x6, x0, lsl #2
    //     0xab27a8: ldur            w7, [x16, #0xf]
    // 0xab27ac: DecompressPointer r7
    //     0xab27ac: add             x7, x7, HEAP, lsl #32
    // 0xab27b0: stur            x7, [fp, #-0x18]
    // 0xab27b4: add             x6, x0, #1
    // 0xab27b8: stur            x6, [fp, #-0x10]
    // 0xab27bc: r0 = LoadClassIdInstr(r7)
    //     0xab27bc: ldur            x0, [x7, #-1]
    //     0xab27c0: ubfx            x0, x0, #0xc, #0x14
    // 0xab27c4: r16 = "."
    //     0xab27c4: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xab27c8: stp             x16, x7, [SP]
    // 0xab27cc: mov             lr, x0
    // 0xab27d0: ldr             lr, [x21, lr, lsl #3]
    // 0xab27d4: blr             lr
    // 0xab27d8: tbz             w0, #4, #0xab2800
    // 0xab27dc: ldur            x1, [fp, #-0x18]
    // 0xab27e0: r0 = LoadClassIdInstr(r1)
    //     0xab27e0: ldur            x0, [x1, #-1]
    //     0xab27e4: ubfx            x0, x0, #0xc, #0x14
    // 0xab27e8: r16 = ""
    //     0xab27e8: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xab27ec: stp             x16, x1, [SP]
    // 0xab27f0: mov             lr, x0
    // 0xab27f4: ldr             lr, [x21, lr, lsl #3]
    // 0xab27f8: blr             lr
    // 0xab27fc: tbnz            w0, #4, #0xab280c
    // 0xab2800: ldur            x5, [fp, #-0x20]
    // 0xab2804: ldur            x3, [fp, #-0x38]
    // 0xab2808: b               #0xab28f8
    // 0xab280c: ldur            x1, [fp, #-0x18]
    // 0xab2810: r0 = LoadClassIdInstr(r1)
    //     0xab2810: ldur            x0, [x1, #-1]
    //     0xab2814: ubfx            x0, x0, #0xc, #0x14
    // 0xab2818: r16 = ".."
    //     0xab2818: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xab281c: stp             x16, x1, [SP]
    // 0xab2820: mov             lr, x0
    // 0xab2824: ldr             lr, [x21, lr, lsl #3]
    // 0xab2828: blr             lr
    // 0xab282c: tbnz            w0, #4, #0xab2874
    // 0xab2830: ldur            x3, [fp, #-0x38]
    // 0xab2834: LoadField: r0 = r3->field_b
    //     0xab2834: ldur            w0, [x3, #0xb]
    // 0xab2838: r1 = LoadInt32Instr(r0)
    //     0xab2838: sbfx            x1, x0, #1, #0x1f
    // 0xab283c: cbz             x1, #0xab2864
    // 0xab2840: sub             x2, x1, #1
    // 0xab2844: mov             x0, x1
    // 0xab2848: mov             x1, x2
    // 0xab284c: cmp             x1, x0
    // 0xab2850: b.hs            #0xab2d08
    // 0xab2854: mov             x1, x3
    // 0xab2858: r0 = length=()
    //     0xab2858: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xab285c: ldur            x1, [fp, #-0x20]
    // 0xab2860: b               #0xab286c
    // 0xab2864: ldur            x0, [fp, #-0x20]
    // 0xab2868: add             x1, x0, #1
    // 0xab286c: ldur            x3, [fp, #-0x38]
    // 0xab2870: b               #0xab28f4
    // 0xab2874: ldur            x2, [fp, #-0x38]
    // 0xab2878: ldur            x0, [fp, #-0x20]
    // 0xab287c: LoadField: r1 = r2->field_b
    //     0xab287c: ldur            w1, [x2, #0xb]
    // 0xab2880: LoadField: r3 = r2->field_f
    //     0xab2880: ldur            w3, [x2, #0xf]
    // 0xab2884: DecompressPointer r3
    //     0xab2884: add             x3, x3, HEAP, lsl #32
    // 0xab2888: LoadField: r4 = r3->field_b
    //     0xab2888: ldur            w4, [x3, #0xb]
    // 0xab288c: r3 = LoadInt32Instr(r1)
    //     0xab288c: sbfx            x3, x1, #1, #0x1f
    // 0xab2890: stur            x3, [fp, #-0x40]
    // 0xab2894: r1 = LoadInt32Instr(r4)
    //     0xab2894: sbfx            x1, x4, #1, #0x1f
    // 0xab2898: cmp             x3, x1
    // 0xab289c: b.ne            #0xab28a8
    // 0xab28a0: mov             x1, x2
    // 0xab28a4: r0 = _growToNextCapacity()
    //     0xab28a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xab28a8: ldur            x3, [fp, #-0x38]
    // 0xab28ac: ldur            x2, [fp, #-0x40]
    // 0xab28b0: add             x0, x2, #1
    // 0xab28b4: lsl             x1, x0, #1
    // 0xab28b8: StoreField: r3->field_b = r1
    //     0xab28b8: stur            w1, [x3, #0xb]
    // 0xab28bc: LoadField: r1 = r3->field_f
    //     0xab28bc: ldur            w1, [x3, #0xf]
    // 0xab28c0: DecompressPointer r1
    //     0xab28c0: add             x1, x1, HEAP, lsl #32
    // 0xab28c4: ldur            x0, [fp, #-0x18]
    // 0xab28c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xab28c8: add             x25, x1, x2, lsl #2
    //     0xab28cc: add             x25, x25, #0xf
    //     0xab28d0: str             w0, [x25]
    //     0xab28d4: tbz             w0, #0, #0xab28f0
    //     0xab28d8: ldurb           w16, [x1, #-1]
    //     0xab28dc: ldurb           w17, [x0, #-1]
    //     0xab28e0: and             x16, x17, x16, lsr #2
    //     0xab28e4: tst             x16, HEAP, lsr #32
    //     0xab28e8: b.eq            #0xab28f0
    //     0xab28ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xab28f0: ldur            x1, [fp, #-0x20]
    // 0xab28f4: mov             x5, x1
    // 0xab28f8: ldur            x0, [fp, #-0x10]
    // 0xab28fc: ldur            x1, [fp, #-8]
    // 0xab2900: mov             x2, x3
    // 0xab2904: ldur            x3, [fp, #-0x30]
    // 0xab2908: ldur            x4, [fp, #-0x28]
    // 0xab290c: b               #0xab2774
    // 0xab2910: mov             x4, x1
    // 0xab2914: mov             x3, x2
    // 0xab2918: LoadField: r0 = r4->field_b
    //     0xab2918: ldur            w0, [x4, #0xb]
    // 0xab291c: DecompressPointer r0
    //     0xab291c: add             x0, x0, HEAP, lsl #32
    // 0xab2920: cmp             w0, NULL
    // 0xab2924: b.ne            #0xab2990
    // 0xab2928: ldur            x5, [fp, #-0x20]
    // 0xab292c: r0 = BoxInt64Instr(r5)
    //     0xab292c: sbfiz           x0, x5, #1, #0x1f
    //     0xab2930: cmp             x5, x0, asr #1
    //     0xab2934: b.eq            #0xab2940
    //     0xab2938: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab293c: stur            x5, [x0, #7]
    // 0xab2940: mov             x2, x0
    // 0xab2944: r1 = <String>
    //     0xab2944: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xab2948: r0 = AllocateArray()
    //     0xab2948: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab294c: ldur            x1, [fp, #-0x20]
    // 0xab2950: r2 = 0
    //     0xab2950: movz            x2, #0
    // 0xab2954: CheckStackOverflow
    //     0xab2954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2958: cmp             SP, x16
    //     0xab295c: b.ls            #0xab2d0c
    // 0xab2960: cmp             x2, x1
    // 0xab2964: b.ge            #0xab2980
    // 0xab2968: add             x3, x0, x2, lsl #2
    // 0xab296c: r16 = ".."
    //     0xab296c: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xab2970: StoreField: r3->field_f = r16
    //     0xab2970: stur            w16, [x3, #0xf]
    // 0xab2974: add             x3, x2, #1
    // 0xab2978: mov             x2, x3
    // 0xab297c: b               #0xab2954
    // 0xab2980: ldur            x1, [fp, #-0x38]
    // 0xab2984: mov             x3, x0
    // 0xab2988: r2 = 0
    //     0xab2988: movz            x2, #0
    // 0xab298c: r0 = insertAll()
    //     0xab298c: bl              #0x64d8c4  ; [dart:core] _GrowableList::insertAll
    // 0xab2990: ldur            x0, [fp, #-0x38]
    // 0xab2994: LoadField: r1 = r0->field_b
    //     0xab2994: ldur            w1, [x0, #0xb]
    // 0xab2998: r2 = LoadInt32Instr(r1)
    //     0xab2998: sbfx            x2, x1, #1, #0x1f
    // 0xab299c: stur            x2, [fp, #-0x10]
    // 0xab29a0: cbnz            x2, #0xab2a0c
    // 0xab29a4: ldur            x3, [fp, #-8]
    // 0xab29a8: LoadField: r4 = r3->field_b
    //     0xab29a8: ldur            w4, [x3, #0xb]
    // 0xab29ac: DecompressPointer r4
    //     0xab29ac: add             x4, x4, HEAP, lsl #32
    // 0xab29b0: cmp             w4, NULL
    // 0xab29b4: b.ne            #0xab2a04
    // 0xab29b8: LoadField: r1 = r0->field_f
    //     0xab29b8: ldur            w1, [x0, #0xf]
    // 0xab29bc: DecompressPointer r1
    //     0xab29bc: add             x1, x1, HEAP, lsl #32
    // 0xab29c0: LoadField: r4 = r1->field_b
    //     0xab29c0: ldur            w4, [x1, #0xb]
    // 0xab29c4: r1 = LoadInt32Instr(r4)
    //     0xab29c4: sbfx            x1, x4, #1, #0x1f
    // 0xab29c8: cmp             x2, x1
    // 0xab29cc: b.ne            #0xab29d8
    // 0xab29d0: mov             x1, x0
    // 0xab29d4: r0 = _growToNextCapacity()
    //     0xab29d4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xab29d8: ldur            x4, [fp, #-0x38]
    // 0xab29dc: ldur            x0, [fp, #-0x10]
    // 0xab29e0: r1 = 2
    //     0xab29e0: movz            x1, #0x2
    // 0xab29e4: StoreField: r4->field_b = r1
    //     0xab29e4: stur            w1, [x4, #0xb]
    // 0xab29e8: LoadField: r1 = r4->field_f
    //     0xab29e8: ldur            w1, [x4, #0xf]
    // 0xab29ec: DecompressPointer r1
    //     0xab29ec: add             x1, x1, HEAP, lsl #32
    // 0xab29f0: add             x2, x1, x0, lsl #2
    // 0xab29f4: r16 = "."
    //     0xab29f4: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xab29f8: StoreField: r2->field_f = r16
    //     0xab29f8: stur            w16, [x2, #0xf]
    // 0xab29fc: r1 = 1
    //     0xab29fc: movz            x1, #0x1
    // 0xab2a00: b               #0xab2a18
    // 0xab2a04: mov             x4, x0
    // 0xab2a08: b               #0xab2a10
    // 0xab2a0c: mov             x4, x0
    // 0xab2a10: r0 = LoadInt32Instr(r1)
    //     0xab2a10: sbfx            x0, x1, #1, #0x1f
    // 0xab2a14: mov             x1, x0
    // 0xab2a18: ldur            x5, [fp, #-8]
    // 0xab2a1c: mov             x0, x4
    // 0xab2a20: StoreField: r5->field_f = r0
    //     0xab2a20: stur            w0, [x5, #0xf]
    //     0xab2a24: ldurb           w16, [x5, #-1]
    //     0xab2a28: ldurb           w17, [x0, #-1]
    //     0xab2a2c: and             x16, x17, x16, lsr #2
    //     0xab2a30: tst             x16, HEAP, lsr #32
    //     0xab2a34: b.eq            #0xab2a3c
    //     0xab2a38: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xab2a3c: add             x2, x1, #1
    // 0xab2a40: LoadField: r0 = r5->field_7
    //     0xab2a40: ldur            w0, [x5, #7]
    // 0xab2a44: DecompressPointer r0
    //     0xab2a44: add             x0, x0, HEAP, lsl #32
    // 0xab2a48: r1 = LoadClassIdInstr(r0)
    //     0xab2a48: ldur            x1, [x0, #-1]
    //     0xab2a4c: ubfx            x1, x1, #0xc, #0x14
    // 0xab2a50: cmp             x1, #0x3af
    // 0xab2a54: b.ne            #0xab2a68
    // 0xab2a58: LoadField: r1 = r0->field_b
    //     0xab2a58: ldur            w1, [x0, #0xb]
    // 0xab2a5c: DecompressPointer r1
    //     0xab2a5c: add             x1, x1, HEAP, lsl #32
    // 0xab2a60: mov             x3, x1
    // 0xab2a64: b               #0xab2a8c
    // 0xab2a68: cmp             x1, #0x3b0
    // 0xab2a6c: b.ne            #0xab2a80
    // 0xab2a70: LoadField: r1 = r0->field_b
    //     0xab2a70: ldur            w1, [x0, #0xb]
    // 0xab2a74: DecompressPointer r1
    //     0xab2a74: add             x1, x1, HEAP, lsl #32
    // 0xab2a78: mov             x3, x1
    // 0xab2a7c: b               #0xab2a8c
    // 0xab2a80: LoadField: r1 = r0->field_b
    //     0xab2a80: ldur            w1, [x0, #0xb]
    // 0xab2a84: DecompressPointer r1
    //     0xab2a84: add             x1, x1, HEAP, lsl #32
    // 0xab2a88: mov             x3, x1
    // 0xab2a8c: r1 = <String>
    //     0xab2a8c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xab2a90: r0 = _GrowableList.filled()
    //     0xab2a90: bl              #0x862f8c  ; [dart:core] _GrowableList::_GrowableList.filled
    // 0xab2a94: ldur            x3, [fp, #-8]
    // 0xab2a98: StoreField: r3->field_13 = r0
    //     0xab2a98: stur            w0, [x3, #0x13]
    //     0xab2a9c: ldurb           w16, [x3, #-1]
    //     0xab2aa0: ldurb           w17, [x0, #-1]
    //     0xab2aa4: and             x16, x17, x16, lsr #2
    //     0xab2aa8: tst             x16, HEAP, lsr #32
    //     0xab2aac: b.eq            #0xab2ab4
    //     0xab2ab0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab2ab4: LoadField: r2 = r3->field_b
    //     0xab2ab4: ldur            w2, [x3, #0xb]
    // 0xab2ab8: DecompressPointer r2
    //     0xab2ab8: add             x2, x2, HEAP, lsl #32
    // 0xab2abc: cmp             w2, NULL
    // 0xab2ac0: b.ne            #0xab2acc
    // 0xab2ac4: mov             x2, x3
    // 0xab2ac8: b               #0xab2bf0
    // 0xab2acc: ldur            x0, [fp, #-0x38]
    // 0xab2ad0: LoadField: r1 = r0->field_b
    //     0xab2ad0: ldur            w1, [x0, #0xb]
    // 0xab2ad4: cbnz            w1, #0xab2ae0
    // 0xab2ad8: mov             x2, x3
    // 0xab2adc: b               #0xab2bf0
    // 0xab2ae0: LoadField: r1 = r3->field_7
    //     0xab2ae0: ldur            w1, [x3, #7]
    // 0xab2ae4: DecompressPointer r1
    //     0xab2ae4: add             x1, x1, HEAP, lsl #32
    // 0xab2ae8: r0 = LoadClassIdInstr(r1)
    //     0xab2ae8: ldur            x0, [x1, #-1]
    //     0xab2aec: ubfx            x0, x0, #0xc, #0x14
    // 0xab2af0: cmp             x0, #0x3af
    // 0xab2af4: b.ne            #0xab2b74
    // 0xab2af8: LoadField: r0 = r2->field_7
    //     0xab2af8: ldur            w0, [x2, #7]
    // 0xab2afc: cbz             w0, #0xab2bec
    // 0xab2b00: r1 = LoadInt32Instr(r0)
    //     0xab2b00: sbfx            x1, x0, #1, #0x1f
    // 0xab2b04: sub             x4, x1, #1
    // 0xab2b08: mov             x0, x1
    // 0xab2b0c: mov             x1, x4
    // 0xab2b10: cmp             x1, x0
    // 0xab2b14: b.hs            #0xab2d14
    // 0xab2b18: r0 = LoadClassIdInstr(r2)
    //     0xab2b18: ldur            x0, [x2, #-1]
    //     0xab2b1c: ubfx            x0, x0, #0xc, #0x14
    // 0xab2b20: lsl             x0, x0, #1
    // 0xab2b24: cmp             w0, #0xbc
    // 0xab2b28: b.ne            #0xab2b38
    // 0xab2b2c: ArrayLoad: r0 = r2[r4]  ; TypedUnsigned_1
    //     0xab2b2c: add             x16, x2, x4
    //     0xab2b30: ldrb            w0, [x16, #0xf]
    // 0xab2b34: b               #0xab2b40
    // 0xab2b38: add             x16, x2, x4, lsl #1
    // 0xab2b3c: ldurh           w0, [x16, #0xf]
    // 0xab2b40: cmp             x0, #0x2f
    // 0xab2b44: b.ne            #0xab2b50
    // 0xab2b48: r0 = true
    //     0xab2b48: add             x0, NULL, #0x20  ; true
    // 0xab2b4c: b               #0xab2b64
    // 0xab2b50: cmp             x0, #0x5c
    // 0xab2b54: r16 = true
    //     0xab2b54: add             x16, NULL, #0x20  ; true
    // 0xab2b58: r17 = false
    //     0xab2b58: add             x17, NULL, #0x30  ; false
    // 0xab2b5c: csel            x1, x16, x17, eq
    // 0xab2b60: mov             x0, x1
    // 0xab2b64: eor             x1, x0, #0x10
    // 0xab2b68: tbnz            w1, #4, #0xab2bec
    // 0xab2b6c: mov             x2, x3
    // 0xab2b70: b               #0xab2c28
    // 0xab2b74: cmp             x0, #0x3b1
    // 0xab2b78: b.ne            #0xab2bd4
    // 0xab2b7c: LoadField: r0 = r2->field_7
    //     0xab2b7c: ldur            w0, [x2, #7]
    // 0xab2b80: cbz             w0, #0xab2bec
    // 0xab2b84: r1 = LoadInt32Instr(r0)
    //     0xab2b84: sbfx            x1, x0, #1, #0x1f
    // 0xab2b88: sub             x4, x1, #1
    // 0xab2b8c: mov             x0, x1
    // 0xab2b90: mov             x1, x4
    // 0xab2b94: cmp             x1, x0
    // 0xab2b98: b.hs            #0xab2d18
    // 0xab2b9c: r0 = LoadClassIdInstr(r2)
    //     0xab2b9c: ldur            x0, [x2, #-1]
    //     0xab2ba0: ubfx            x0, x0, #0xc, #0x14
    // 0xab2ba4: lsl             x0, x0, #1
    // 0xab2ba8: cmp             w0, #0xbc
    // 0xab2bac: b.ne            #0xab2bbc
    // 0xab2bb0: ArrayLoad: r0 = r2[r4]  ; TypedUnsigned_1
    //     0xab2bb0: add             x16, x2, x4
    //     0xab2bb4: ldrb            w0, [x16, #0xf]
    // 0xab2bb8: b               #0xab2bc4
    // 0xab2bbc: add             x16, x2, x4, lsl #1
    // 0xab2bc0: ldurh           w0, [x16, #0xf]
    // 0xab2bc4: cmp             x0, #0x2f
    // 0xab2bc8: b.eq            #0xab2bec
    // 0xab2bcc: mov             x2, x3
    // 0xab2bd0: b               #0xab2c28
    // 0xab2bd4: r0 = LoadClassIdInstr(r1)
    //     0xab2bd4: ldur            x0, [x1, #-1]
    //     0xab2bd8: ubfx            x0, x0, #0xc, #0x14
    // 0xab2bdc: r0 = GDT[cid_x0 + -0xff1]()
    //     0xab2bdc: sub             lr, x0, #0xff1
    //     0xab2be0: ldr             lr, [x21, lr, lsl #3]
    //     0xab2be4: blr             lr
    // 0xab2be8: tbz             w0, #4, #0xab2c24
    // 0xab2bec: ldur            x2, [fp, #-8]
    // 0xab2bf0: LoadField: r3 = r2->field_13
    //     0xab2bf0: ldur            w3, [x2, #0x13]
    // 0xab2bf4: DecompressPointer r3
    //     0xab2bf4: add             x3, x3, HEAP, lsl #32
    // 0xab2bf8: LoadField: r0 = r3->field_b
    //     0xab2bf8: ldur            w0, [x3, #0xb]
    // 0xab2bfc: r1 = LoadInt32Instr(r0)
    //     0xab2bfc: sbfx            x1, x0, #1, #0x1f
    // 0xab2c00: mov             x0, x1
    // 0xab2c04: r1 = 0
    //     0xab2c04: movz            x1, #0
    // 0xab2c08: cmp             x1, x0
    // 0xab2c0c: b.hs            #0xab2d1c
    // 0xab2c10: LoadField: r0 = r3->field_f
    //     0xab2c10: ldur            w0, [x3, #0xf]
    // 0xab2c14: DecompressPointer r0
    //     0xab2c14: add             x0, x0, HEAP, lsl #32
    // 0xab2c18: r16 = ""
    //     0xab2c18: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xab2c1c: StoreField: r0->field_f = r16
    //     0xab2c1c: stur            w16, [x0, #0xf]
    // 0xab2c20: b               #0xab2c28
    // 0xab2c24: ldur            x2, [fp, #-8]
    // 0xab2c28: LoadField: r0 = r2->field_b
    //     0xab2c28: ldur            w0, [x2, #0xb]
    // 0xab2c2c: DecompressPointer r0
    //     0xab2c2c: add             x0, x0, HEAP, lsl #32
    // 0xab2c30: cmp             w0, NULL
    // 0xab2c34: b.eq            #0xab2cc0
    // 0xab2c38: LoadField: r0 = r2->field_7
    //     0xab2c38: ldur            w0, [x2, #7]
    // 0xab2c3c: DecompressPointer r0
    //     0xab2c3c: add             x0, x0, HEAP, lsl #32
    // 0xab2c40: stur            x0, [fp, #-0x18]
    // 0xab2c44: r0 = InitLateStaticField(0x16c8) // [package:path/src/style.dart] Style::windows
    //     0xab2c44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab2c48: ldr             x0, [x0, #0x2d90]
    //     0xab2c4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab2c50: cmp             w0, w16
    //     0xab2c54: b.ne            #0xab2c64
    //     0xab2c58: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc58] Field <Style.windows>: static late final (offset: 0x16c8)
    //     0xab2c5c: ldr             x2, [x2, #0xc58]
    //     0xab2c60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab2c64: mov             x1, x0
    // 0xab2c68: ldur            x0, [fp, #-0x18]
    // 0xab2c6c: cmp             w0, w1
    // 0xab2c70: b.ne            #0xab2cb8
    // 0xab2c74: ldur            x0, [fp, #-8]
    // 0xab2c78: LoadField: r1 = r0->field_b
    //     0xab2c78: ldur            w1, [x0, #0xb]
    // 0xab2c7c: DecompressPointer r1
    //     0xab2c7c: add             x1, x1, HEAP, lsl #32
    // 0xab2c80: cmp             w1, NULL
    // 0xab2c84: b.eq            #0xab2d20
    // 0xab2c88: r2 = "/"
    //     0xab2c88: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xab2c8c: r3 = "\\"
    //     0xab2c8c: ldr             x3, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0xab2c90: r0 = replaceAll()
    //     0xab2c90: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xab2c94: ldur            x1, [fp, #-8]
    // 0xab2c98: StoreField: r1->field_b = r0
    //     0xab2c98: stur            w0, [x1, #0xb]
    //     0xab2c9c: ldurb           w16, [x1, #-1]
    //     0xab2ca0: ldurb           w17, [x0, #-1]
    //     0xab2ca4: and             x16, x17, x16, lsr #2
    //     0xab2ca8: tst             x16, HEAP, lsr #32
    //     0xab2cac: b.eq            #0xab2cb4
    //     0xab2cb0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab2cb4: b               #0xab2cc4
    // 0xab2cb8: ldur            x1, [fp, #-8]
    // 0xab2cbc: b               #0xab2cc4
    // 0xab2cc0: mov             x1, x2
    // 0xab2cc4: r0 = removeTrailingSeparators()
    //     0xab2cc4: bl              #0xab2d24  ; [package:path/src/parsed_path.dart] ParsedPath::removeTrailingSeparators
    // 0xab2cc8: r0 = Null
    //     0xab2cc8: mov             x0, NULL
    // 0xab2ccc: LeaveFrame
    //     0xab2ccc: mov             SP, fp
    //     0xab2cd0: ldp             fp, lr, [SP], #0x10
    // 0xab2cd4: ret
    //     0xab2cd4: ret             
    // 0xab2cd8: mov             x0, x3
    // 0xab2cdc: r0 = ConcurrentModificationError()
    //     0xab2cdc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xab2ce0: mov             x1, x0
    // 0xab2ce4: ldur            x0, [fp, #-0x30]
    // 0xab2ce8: StoreField: r1->field_b = r0
    //     0xab2ce8: stur            w0, [x1, #0xb]
    // 0xab2cec: mov             x0, x1
    // 0xab2cf0: r0 = Throw()
    //     0xab2cf0: bl              #0xec04b8  ; ThrowStub
    // 0xab2cf4: brk             #0
    // 0xab2cf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2cf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2cfc: b               #0xab273c
    // 0xab2d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2d00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2d04: b               #0xab2784
    // 0xab2d08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2d08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2d0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2d10: b               #0xab2960
    // 0xab2d14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2d14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2d18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2d18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2d1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2d1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab2d20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ removeTrailingSeparators(/* No info */) {
    // ** addr: 0xab2d24, size: 0x17c
    // 0xab2d24: EnterFrame
    //     0xab2d24: stp             fp, lr, [SP, #-0x10]!
    //     0xab2d28: mov             fp, SP
    // 0xab2d2c: AllocStack(0x18)
    //     0xab2d2c: sub             SP, SP, #0x18
    // 0xab2d30: SetupParameters(ParsedPath this /* r1 => r2, fp-0x8 */)
    //     0xab2d30: mov             x2, x1
    //     0xab2d34: stur            x1, [fp, #-8]
    // 0xab2d38: CheckStackOverflow
    //     0xab2d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2d3c: cmp             SP, x16
    //     0xab2d40: b.ls            #0xab2e80
    // 0xab2d44: CheckStackOverflow
    //     0xab2d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2d48: cmp             SP, x16
    //     0xab2d4c: b.ls            #0xab2e88
    // 0xab2d50: LoadField: r3 = r2->field_f
    //     0xab2d50: ldur            w3, [x2, #0xf]
    // 0xab2d54: DecompressPointer r3
    //     0xab2d54: add             x3, x3, HEAP, lsl #32
    // 0xab2d58: LoadField: r0 = r3->field_b
    //     0xab2d58: ldur            w0, [x3, #0xb]
    // 0xab2d5c: r1 = LoadInt32Instr(r0)
    //     0xab2d5c: sbfx            x1, x0, #1, #0x1f
    // 0xab2d60: cbz             w0, #0xab2e24
    // 0xab2d64: cmp             x1, #0
    // 0xab2d68: b.le            #0xab2e74
    // 0xab2d6c: sub             x4, x1, #1
    // 0xab2d70: mov             x0, x1
    // 0xab2d74: mov             x1, x4
    // 0xab2d78: cmp             x1, x0
    // 0xab2d7c: b.hs            #0xab2e90
    // 0xab2d80: LoadField: r0 = r3->field_f
    //     0xab2d80: ldur            w0, [x3, #0xf]
    // 0xab2d84: DecompressPointer r0
    //     0xab2d84: add             x0, x0, HEAP, lsl #32
    // 0xab2d88: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xab2d88: add             x16, x0, x4, lsl #2
    //     0xab2d8c: ldur            w1, [x16, #0xf]
    // 0xab2d90: DecompressPointer r1
    //     0xab2d90: add             x1, x1, HEAP, lsl #32
    // 0xab2d94: r0 = LoadClassIdInstr(r1)
    //     0xab2d94: ldur            x0, [x1, #-1]
    //     0xab2d98: ubfx            x0, x0, #0xc, #0x14
    // 0xab2d9c: r16 = ""
    //     0xab2d9c: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xab2da0: stp             x16, x1, [SP]
    // 0xab2da4: mov             lr, x0
    // 0xab2da8: ldr             lr, [x21, lr, lsl #3]
    // 0xab2dac: blr             lr
    // 0xab2db0: tbnz            w0, #4, #0xab2e24
    // 0xab2db4: ldur            x3, [fp, #-8]
    // 0xab2db8: LoadField: r2 = r3->field_f
    //     0xab2db8: ldur            w2, [x3, #0xf]
    // 0xab2dbc: DecompressPointer r2
    //     0xab2dbc: add             x2, x2, HEAP, lsl #32
    // 0xab2dc0: LoadField: r0 = r2->field_b
    //     0xab2dc0: ldur            w0, [x2, #0xb]
    // 0xab2dc4: r1 = LoadInt32Instr(r0)
    //     0xab2dc4: sbfx            x1, x0, #1, #0x1f
    // 0xab2dc8: sub             x4, x1, #1
    // 0xab2dcc: mov             x0, x1
    // 0xab2dd0: mov             x1, x4
    // 0xab2dd4: cmp             x1, x0
    // 0xab2dd8: b.hs            #0xab2e94
    // 0xab2ddc: mov             x1, x2
    // 0xab2de0: mov             x2, x4
    // 0xab2de4: r0 = length=()
    //     0xab2de4: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xab2de8: ldur            x3, [fp, #-8]
    // 0xab2dec: LoadField: r2 = r3->field_13
    //     0xab2dec: ldur            w2, [x3, #0x13]
    // 0xab2df0: DecompressPointer r2
    //     0xab2df0: add             x2, x2, HEAP, lsl #32
    // 0xab2df4: LoadField: r0 = r2->field_b
    //     0xab2df4: ldur            w0, [x2, #0xb]
    // 0xab2df8: r1 = LoadInt32Instr(r0)
    //     0xab2df8: sbfx            x1, x0, #1, #0x1f
    // 0xab2dfc: sub             x4, x1, #1
    // 0xab2e00: mov             x0, x1
    // 0xab2e04: mov             x1, x4
    // 0xab2e08: cmp             x1, x0
    // 0xab2e0c: b.hs            #0xab2e98
    // 0xab2e10: mov             x1, x2
    // 0xab2e14: mov             x2, x4
    // 0xab2e18: r0 = length=()
    //     0xab2e18: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xab2e1c: ldur            x2, [fp, #-8]
    // 0xab2e20: b               #0xab2d44
    // 0xab2e24: ldur            x0, [fp, #-8]
    // 0xab2e28: LoadField: r2 = r0->field_13
    //     0xab2e28: ldur            w2, [x0, #0x13]
    // 0xab2e2c: DecompressPointer r2
    //     0xab2e2c: add             x2, x2, HEAP, lsl #32
    // 0xab2e30: LoadField: r0 = r2->field_b
    //     0xab2e30: ldur            w0, [x2, #0xb]
    // 0xab2e34: r1 = LoadInt32Instr(r0)
    //     0xab2e34: sbfx            x1, x0, #1, #0x1f
    // 0xab2e38: cbz             w0, #0xab2e64
    // 0xab2e3c: sub             x3, x1, #1
    // 0xab2e40: mov             x0, x1
    // 0xab2e44: mov             x1, x3
    // 0xab2e48: cmp             x1, x0
    // 0xab2e4c: b.hs            #0xab2e9c
    // 0xab2e50: LoadField: r0 = r2->field_f
    //     0xab2e50: ldur            w0, [x2, #0xf]
    // 0xab2e54: DecompressPointer r0
    //     0xab2e54: add             x0, x0, HEAP, lsl #32
    // 0xab2e58: add             x1, x0, x3, lsl #2
    // 0xab2e5c: r16 = ""
    //     0xab2e5c: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xab2e60: StoreField: r1->field_f = r16
    //     0xab2e60: stur            w16, [x1, #0xf]
    // 0xab2e64: r0 = Null
    //     0xab2e64: mov             x0, NULL
    // 0xab2e68: LeaveFrame
    //     0xab2e68: mov             SP, fp
    //     0xab2e6c: ldp             fp, lr, [SP], #0x10
    // 0xab2e70: ret
    //     0xab2e70: ret             
    // 0xab2e74: r0 = noElement()
    //     0xab2e74: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xab2e78: r0 = Throw()
    //     0xab2e78: bl              #0xec04b8  ; ThrowStub
    // 0xab2e7c: brk             #0
    // 0xab2e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2e84: b               #0xab2d44
    // 0xab2e88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2e88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2e8c: b               #0xab2d50
    // 0xab2e90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2e90: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2e94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2e94: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2e98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2e98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab2e9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab2e9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc336cc, size: 0x1a8
    // 0xc336cc: EnterFrame
    //     0xc336cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc336d0: mov             fp, SP
    // 0xc336d4: AllocStack(0x18)
    //     0xc336d4: sub             SP, SP, #0x18
    // 0xc336d8: CheckStackOverflow
    //     0xc336d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc336dc: cmp             SP, x16
    //     0xc336e0: b.ls            #0xc3385c
    // 0xc336e4: r0 = StringBuffer()
    //     0xc336e4: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc336e8: mov             x1, x0
    // 0xc336ec: stur            x0, [fp, #-8]
    // 0xc336f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc336f0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc336f4: r0 = StringBuffer()
    //     0xc336f4: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc336f8: ldr             x0, [fp, #0x10]
    // 0xc336fc: LoadField: r2 = r0->field_b
    //     0xc336fc: ldur            w2, [x0, #0xb]
    // 0xc33700: DecompressPointer r2
    //     0xc33700: add             x2, x2, HEAP, lsl #32
    // 0xc33704: cmp             w2, NULL
    // 0xc33708: b.eq            #0xc33714
    // 0xc3370c: ldur            x1, [fp, #-8]
    // 0xc33710: r0 = write()
    //     0xc33710: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc33714: r3 = 0
    //     0xc33714: movz            x3, #0
    // 0xc33718: ldr             x2, [fp, #0x10]
    // 0xc3371c: stur            x3, [fp, #-0x10]
    // 0xc33720: CheckStackOverflow
    //     0xc33720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33724: cmp             SP, x16
    //     0xc33728: b.ls            #0xc33864
    // 0xc3372c: LoadField: r0 = r2->field_f
    //     0xc3372c: ldur            w0, [x2, #0xf]
    // 0xc33730: DecompressPointer r0
    //     0xc33730: add             x0, x0, HEAP, lsl #32
    // 0xc33734: LoadField: r1 = r0->field_b
    //     0xc33734: ldur            w1, [x0, #0xb]
    // 0xc33738: r0 = LoadInt32Instr(r1)
    //     0xc33738: sbfx            x0, x1, #1, #0x1f
    // 0xc3373c: cmp             x3, x0
    // 0xc33740: b.ge            #0xc33828
    // 0xc33744: LoadField: r4 = r2->field_13
    //     0xc33744: ldur            w4, [x2, #0x13]
    // 0xc33748: DecompressPointer r4
    //     0xc33748: add             x4, x4, HEAP, lsl #32
    // 0xc3374c: LoadField: r0 = r4->field_b
    //     0xc3374c: ldur            w0, [x4, #0xb]
    // 0xc33750: r1 = LoadInt32Instr(r0)
    //     0xc33750: sbfx            x1, x0, #1, #0x1f
    // 0xc33754: mov             x0, x1
    // 0xc33758: mov             x1, x3
    // 0xc3375c: cmp             x1, x0
    // 0xc33760: b.hs            #0xc3386c
    // 0xc33764: LoadField: r0 = r4->field_f
    //     0xc33764: ldur            w0, [x4, #0xf]
    // 0xc33768: DecompressPointer r0
    //     0xc33768: add             x0, x0, HEAP, lsl #32
    // 0xc3376c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc3376c: add             x16, x0, x3, lsl #2
    //     0xc33770: ldur            w1, [x16, #0xf]
    // 0xc33774: DecompressPointer r1
    //     0xc33774: add             x1, x1, HEAP, lsl #32
    // 0xc33778: r0 = LoadClassIdInstr(r1)
    //     0xc33778: ldur            x0, [x1, #-1]
    //     0xc3377c: ubfx            x0, x0, #0xc, #0x14
    // 0xc33780: str             x1, [SP]
    // 0xc33784: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc33784: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc33788: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc33788: movz            x17, #0x2b03
    //     0xc3378c: add             lr, x0, x17
    //     0xc33790: ldr             lr, [x21, lr, lsl #3]
    //     0xc33794: blr             lr
    // 0xc33798: LoadField: r1 = r0->field_7
    //     0xc33798: ldur            w1, [x0, #7]
    // 0xc3379c: cbz             w1, #0xc337ac
    // 0xc337a0: ldur            x1, [fp, #-8]
    // 0xc337a4: mov             x2, x0
    // 0xc337a8: r0 = _writeString()
    //     0xc337a8: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc337ac: ldr             x2, [fp, #0x10]
    // 0xc337b0: ldur            x3, [fp, #-0x10]
    // 0xc337b4: LoadField: r4 = r2->field_f
    //     0xc337b4: ldur            w4, [x2, #0xf]
    // 0xc337b8: DecompressPointer r4
    //     0xc337b8: add             x4, x4, HEAP, lsl #32
    // 0xc337bc: LoadField: r0 = r4->field_b
    //     0xc337bc: ldur            w0, [x4, #0xb]
    // 0xc337c0: r1 = LoadInt32Instr(r0)
    //     0xc337c0: sbfx            x1, x0, #1, #0x1f
    // 0xc337c4: mov             x0, x1
    // 0xc337c8: mov             x1, x3
    // 0xc337cc: cmp             x1, x0
    // 0xc337d0: b.hs            #0xc33870
    // 0xc337d4: LoadField: r0 = r4->field_f
    //     0xc337d4: ldur            w0, [x4, #0xf]
    // 0xc337d8: DecompressPointer r0
    //     0xc337d8: add             x0, x0, HEAP, lsl #32
    // 0xc337dc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc337dc: add             x16, x0, x3, lsl #2
    //     0xc337e0: ldur            w1, [x16, #0xf]
    // 0xc337e4: DecompressPointer r1
    //     0xc337e4: add             x1, x1, HEAP, lsl #32
    // 0xc337e8: r0 = LoadClassIdInstr(r1)
    //     0xc337e8: ldur            x0, [x1, #-1]
    //     0xc337ec: ubfx            x0, x0, #0xc, #0x14
    // 0xc337f0: str             x1, [SP]
    // 0xc337f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc337f4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc337f8: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc337f8: movz            x17, #0x2b03
    //     0xc337fc: add             lr, x0, x17
    //     0xc33800: ldr             lr, [x21, lr, lsl #3]
    //     0xc33804: blr             lr
    // 0xc33808: LoadField: r1 = r0->field_7
    //     0xc33808: ldur            w1, [x0, #7]
    // 0xc3380c: cbz             w1, #0xc3381c
    // 0xc33810: ldur            x1, [fp, #-8]
    // 0xc33814: mov             x2, x0
    // 0xc33818: r0 = _writeString()
    //     0xc33818: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc3381c: ldur            x0, [fp, #-0x10]
    // 0xc33820: add             x3, x0, #1
    // 0xc33824: b               #0xc33718
    // 0xc33828: mov             x0, x2
    // 0xc3382c: LoadField: r1 = r0->field_13
    //     0xc3382c: ldur            w1, [x0, #0x13]
    // 0xc33830: DecompressPointer r1
    //     0xc33830: add             x1, x1, HEAP, lsl #32
    // 0xc33834: r0 = last()
    //     0xc33834: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xc33838: ldur            x1, [fp, #-8]
    // 0xc3383c: mov             x2, x0
    // 0xc33840: r0 = write()
    //     0xc33840: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc33844: ldur            x16, [fp, #-8]
    // 0xc33848: str             x16, [SP]
    // 0xc3384c: r0 = toString()
    //     0xc3384c: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc33850: LeaveFrame
    //     0xc33850: mov             SP, fp
    //     0xc33854: ldp             fp, lr, [SP], #0x10
    // 0xc33858: ret
    //     0xc33858: ret             
    // 0xc3385c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3385c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33860: b               #0xc336e4
    // 0xc33864: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33864: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33868: b               #0xc3372c
    // 0xc3386c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3386c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc33870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc33870: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
