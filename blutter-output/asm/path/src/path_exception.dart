// lib: , url: package:path/src/path_exception.dart

// class id: 1050756, size: 0x8
class :: {
}

// class id: 939, size: 0xc, field offset: 0x8
class PathException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xc33874, size: 0x5c
    // 0xc33874: EnterFrame
    //     0xc33874: stp             fp, lr, [SP, #-0x10]!
    //     0xc33878: mov             fp, SP
    // 0xc3387c: AllocStack(0x8)
    //     0xc3387c: sub             SP, SP, #8
    // 0xc33880: CheckStackOverflow
    //     0xc33880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33884: cmp             SP, x16
    //     0xc33888: b.ls            #0xc338c8
    // 0xc3388c: r1 = Null
    //     0xc3388c: mov             x1, NULL
    // 0xc33890: r2 = 4
    //     0xc33890: movz            x2, #0x4
    // 0xc33894: r0 = AllocateArray()
    //     0xc33894: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33898: r16 = "PathException: "
    //     0xc33898: add             x16, PP, #0x20, lsl #12  ; [pp+0x20aa0] "PathException: "
    //     0xc3389c: ldr             x16, [x16, #0xaa0]
    // 0xc338a0: StoreField: r0->field_f = r16
    //     0xc338a0: stur            w16, [x0, #0xf]
    // 0xc338a4: ldr             x1, [fp, #0x10]
    // 0xc338a8: LoadField: r2 = r1->field_7
    //     0xc338a8: ldur            w2, [x1, #7]
    // 0xc338ac: DecompressPointer r2
    //     0xc338ac: add             x2, x2, HEAP, lsl #32
    // 0xc338b0: StoreField: r0->field_13 = r2
    //     0xc338b0: stur            w2, [x0, #0x13]
    // 0xc338b4: str             x0, [SP]
    // 0xc338b8: r0 = _interpolate()
    //     0xc338b8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc338bc: LeaveFrame
    //     0xc338bc: mov             SP, fp
    //     0xc338c0: ldp             fp, lr, [SP], #0x10
    // 0xc338c4: ret
    //     0xc338c4: ret             
    // 0xc338c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc338c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc338cc: b               #0xc3388c
  }
}
