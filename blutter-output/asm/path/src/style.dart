// lib: , url: package:path/src/style.dart

// class id: 1050757, size: 0x8
class :: {
}

// class id: 941, size: 0x8, field offset: 0x8
abstract class Style extends Object {

  static late final Style platform; // offset: 0x16d0
  static late final Style url; // offset: 0x16cc
  static late final Style windows; // offset: 0x16c8
  static late final Style posix; // offset: 0x16c4

  static Style platform() {
    // ** addr: 0x8334f4, size: 0x2c
    // 0x8334f4: EnterFrame
    //     0x8334f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8334f8: mov             fp, SP
    // 0x8334fc: CheckStackOverflow
    //     0x8334fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833500: cmp             SP, x16
    //     0x833504: b.ls            #0x833518
    // 0x833508: r0 = _getPlatformStyle()
    //     0x833508: bl              #0x833520  ; [package:path/src/style.dart] Style::_getPlatformStyle
    // 0x83350c: LeaveFrame
    //     0x83350c: mov             SP, fp
    //     0x833510: ldp             fp, lr, [SP], #0x10
    // 0x833514: ret
    //     0x833514: ret             
    // 0x833518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833518: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83351c: b               #0x833508
  }
  static Style _getPlatformStyle() {
    // ** addr: 0x833520, size: 0x1d8
    // 0x833520: EnterFrame
    //     0x833520: stp             fp, lr, [SP, #-0x10]!
    //     0x833524: mov             fp, SP
    // 0x833528: AllocStack(0x18)
    //     0x833528: sub             SP, SP, #0x18
    // 0x83352c: CheckStackOverflow
    //     0x83352c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833530: cmp             SP, x16
    //     0x833534: b.ls            #0x8336f0
    // 0x833538: r0 = InitLateStaticField(0x178) // [dart:core] ::_uriBaseClosure
    //     0x833538: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x83353c: ldr             x0, [x0, #0x2f0]
    //     0x833540: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833544: cmp             w0, w16
    //     0x833548: b.ne            #0x833558
    //     0x83354c: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc38] Field <::._uriBaseClosure@0150898>: static late (offset: 0x178)
    //     0x833550: ldr             x2, [x2, #0xc38]
    //     0x833554: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x833558: str             x0, [SP]
    // 0x83355c: ClosureCall
    //     0x83355c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x833560: ldur            x2, [x0, #0x1f]
    //     0x833564: blr             x2
    // 0x833568: r1 = LoadClassIdInstr(r0)
    //     0x833568: ldur            x1, [x0, #-1]
    //     0x83356c: ubfx            x1, x1, #0xc, #0x14
    // 0x833570: mov             x16, x0
    // 0x833574: mov             x0, x1
    // 0x833578: mov             x1, x16
    // 0x83357c: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x83357c: sub             lr, x0, #0xfcb
    //     0x833580: ldr             lr, [x21, lr, lsl #3]
    //     0x833584: blr             lr
    // 0x833588: r1 = LoadClassIdInstr(r0)
    //     0x833588: ldur            x1, [x0, #-1]
    //     0x83358c: ubfx            x1, x1, #0xc, #0x14
    // 0x833590: r16 = "file"
    //     0x833590: ldr             x16, [PP, #0xc40]  ; [pp+0xc40] "file"
    // 0x833594: stp             x16, x0, [SP]
    // 0x833598: mov             x0, x1
    // 0x83359c: mov             lr, x0
    // 0x8335a0: ldr             lr, [x21, lr, lsl #3]
    // 0x8335a4: blr             lr
    // 0x8335a8: tbz             w0, #4, #0x8335d8
    // 0x8335ac: r0 = InitLateStaticField(0x16cc) // [package:path/src/style.dart] Style::url
    //     0x8335ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8335b0: ldr             x0, [x0, #0x2d98]
    //     0x8335b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8335b8: cmp             w0, w16
    //     0x8335bc: b.ne            #0x8335cc
    //     0x8335c0: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc40] Field <Style.url>: static late final (offset: 0x16cc)
    //     0x8335c4: ldr             x2, [x2, #0xc40]
    //     0x8335c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8335cc: LeaveFrame
    //     0x8335cc: mov             SP, fp
    //     0x8335d0: ldp             fp, lr, [SP], #0x10
    // 0x8335d4: ret
    //     0x8335d4: ret             
    // 0x8335d8: r0 = base()
    //     0x8335d8: bl              #0x60d09c  ; [dart:core] Uri::base
    // 0x8335dc: r1 = LoadClassIdInstr(r0)
    //     0x8335dc: ldur            x1, [x0, #-1]
    //     0x8335e0: ubfx            x1, x1, #0xc, #0x14
    // 0x8335e4: mov             x16, x0
    // 0x8335e8: mov             x0, x1
    // 0x8335ec: mov             x1, x16
    // 0x8335f0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8335f0: sub             lr, x0, #0xffa
    //     0x8335f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8335f8: blr             lr
    // 0x8335fc: LoadField: r1 = r0->field_7
    //     0x8335fc: ldur            w1, [x0, #7]
    // 0x833600: r2 = LoadInt32Instr(r1)
    //     0x833600: sbfx            x2, x1, #1, #0x1f
    // 0x833604: sub             x1, x2, #1
    // 0x833608: lsl             x2, x1, #1
    // 0x83360c: stp             x2, x0, [SP, #8]
    // 0x833610: r16 = "/"
    //     0x833610: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x833614: str             x16, [SP]
    // 0x833618: r0 = _substringMatches()
    //     0x833618: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0x83361c: tbz             w0, #4, #0x83364c
    // 0x833620: r0 = InitLateStaticField(0x16cc) // [package:path/src/style.dart] Style::url
    //     0x833620: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833624: ldr             x0, [x0, #0x2d98]
    //     0x833628: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x83362c: cmp             w0, w16
    //     0x833630: b.ne            #0x833640
    //     0x833634: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc40] Field <Style.url>: static late final (offset: 0x16cc)
    //     0x833638: ldr             x2, [x2, #0xc40]
    //     0x83363c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833640: LeaveFrame
    //     0x833640: mov             SP, fp
    //     0x833644: ldp             fp, lr, [SP], #0x10
    // 0x833648: ret
    //     0x833648: ret             
    // 0x83364c: r16 = "a/b"
    //     0x83364c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc48] "a/b"
    //     0x833650: ldr             x16, [x16, #0xc48]
    // 0x833654: str             x16, [SP]
    // 0x833658: r1 = Null
    //     0x833658: mov             x1, NULL
    // 0x83365c: r4 = const [0, 0x2, 0x1, 0x1, path, 0x1, null]
    //     0x83365c: ldr             x4, [PP, #0x35f8]  ; [pp+0x35f8] List(7) [0, 0x2, 0x1, 0x1, "path", 0x1, Null]
    // 0x833660: r0 = _Uri()
    //     0x833660: bl              #0x5ff47c  ; [dart:core] _Uri::_Uri
    // 0x833664: mov             x1, x0
    // 0x833668: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x833668: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x83366c: r0 = toFilePath()
    //     0x83366c: bl              #0xd2c5c4  ; [dart:core] _Uri::toFilePath
    // 0x833670: r1 = LoadClassIdInstr(r0)
    //     0x833670: ldur            x1, [x0, #-1]
    //     0x833674: ubfx            x1, x1, #0xc, #0x14
    // 0x833678: r16 = "a\\b"
    //     0x833678: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc50] "a\\b"
    //     0x83367c: ldr             x16, [x16, #0xc50]
    // 0x833680: stp             x16, x0, [SP]
    // 0x833684: mov             x0, x1
    // 0x833688: mov             lr, x0
    // 0x83368c: ldr             lr, [x21, lr, lsl #3]
    // 0x833690: blr             lr
    // 0x833694: tbnz            w0, #4, #0x8336c4
    // 0x833698: r0 = InitLateStaticField(0x16c8) // [package:path/src/style.dart] Style::windows
    //     0x833698: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x83369c: ldr             x0, [x0, #0x2d90]
    //     0x8336a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8336a4: cmp             w0, w16
    //     0x8336a8: b.ne            #0x8336b8
    //     0x8336ac: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc58] Field <Style.windows>: static late final (offset: 0x16c8)
    //     0x8336b0: ldr             x2, [x2, #0xc58]
    //     0x8336b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8336b8: LeaveFrame
    //     0x8336b8: mov             SP, fp
    //     0x8336bc: ldp             fp, lr, [SP], #0x10
    // 0x8336c0: ret
    //     0x8336c0: ret             
    // 0x8336c4: r0 = InitLateStaticField(0x16c4) // [package:path/src/style.dart] Style::posix
    //     0x8336c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8336c8: ldr             x0, [x0, #0x2d88]
    //     0x8336cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8336d0: cmp             w0, w16
    //     0x8336d4: b.ne            #0x8336e4
    //     0x8336d8: add             x2, PP, #0xb, lsl #12  ; [pp+0xbc60] Field <Style.posix>: static late final (offset: 0x16c4)
    //     0x8336dc: ldr             x2, [x2, #0xc60]
    //     0x8336e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8336e4: LeaveFrame
    //     0x8336e4: mov             SP, fp
    //     0x8336e8: ldp             fp, lr, [SP], #0x10
    // 0x8336ec: ret
    //     0x8336ec: ret             
    // 0x8336f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8336f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8336f4: b               #0x833538
  }
  static Style posix() {
    // ** addr: 0x8336f8, size: 0x40
    // 0x8336f8: EnterFrame
    //     0x8336f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8336fc: mov             fp, SP
    // 0x833700: AllocStack(0x8)
    //     0x833700: sub             SP, SP, #8
    // 0x833704: CheckStackOverflow
    //     0x833704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833708: cmp             SP, x16
    //     0x83370c: b.ls            #0x833730
    // 0x833710: r0 = PosixStyle()
    //     0x833710: bl              #0x8337fc  ; AllocatePosixStyleStub -> PosixStyle (size=0x10)
    // 0x833714: mov             x1, x0
    // 0x833718: stur            x0, [fp, #-8]
    // 0x83371c: r0 = PosixStyle()
    //     0x83371c: bl              #0x833738  ; [package:path/src/style/posix.dart] PosixStyle::PosixStyle
    // 0x833720: ldur            x0, [fp, #-8]
    // 0x833724: LeaveFrame
    //     0x833724: mov             SP, fp
    //     0x833728: ldp             fp, lr, [SP], #0x10
    // 0x83372c: ret
    //     0x83372c: ret             
    // 0x833730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833734: b               #0x833710
  }
  static Style windows() {
    // ** addr: 0x833808, size: 0x40
    // 0x833808: EnterFrame
    //     0x833808: stp             fp, lr, [SP, #-0x10]!
    //     0x83380c: mov             fp, SP
    // 0x833810: AllocStack(0x8)
    //     0x833810: sub             SP, SP, #8
    // 0x833814: CheckStackOverflow
    //     0x833814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833818: cmp             SP, x16
    //     0x83381c: b.ls            #0x833840
    // 0x833820: r0 = WindowsStyle()
    //     0x833820: bl              #0x833938  ; AllocateWindowsStyleStub -> WindowsStyle (size=0x10)
    // 0x833824: mov             x1, x0
    // 0x833828: stur            x0, [fp, #-8]
    // 0x83382c: r0 = WindowsStyle()
    //     0x83382c: bl              #0x833848  ; [package:path/src/style/windows.dart] WindowsStyle::WindowsStyle
    // 0x833830: ldur            x0, [fp, #-8]
    // 0x833834: LeaveFrame
    //     0x833834: mov             SP, fp
    //     0x833838: ldp             fp, lr, [SP], #0x10
    // 0x83383c: ret
    //     0x83383c: ret             
    // 0x833840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833844: b               #0x833820
  }
  static Style url() {
    // ** addr: 0x833944, size: 0x40
    // 0x833944: EnterFrame
    //     0x833944: stp             fp, lr, [SP, #-0x10]!
    //     0x833948: mov             fp, SP
    // 0x83394c: AllocStack(0x8)
    //     0x83394c: sub             SP, SP, #8
    // 0x833950: CheckStackOverflow
    //     0x833950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833954: cmp             SP, x16
    //     0x833958: b.ls            #0x83397c
    // 0x83395c: r0 = UrlStyle()
    //     0x83395c: bl              #0x833a74  ; AllocateUrlStyleStub -> UrlStyle (size=0x10)
    // 0x833960: mov             x1, x0
    // 0x833964: stur            x0, [fp, #-8]
    // 0x833968: r0 = UrlStyle()
    //     0x833968: bl              #0x833984  ; [package:path/src/style/url.dart] UrlStyle::UrlStyle
    // 0x83396c: ldur            x0, [fp, #-8]
    // 0x833970: LeaveFrame
    //     0x833970: mov             SP, fp
    //     0x833974: ldp             fp, lr, [SP], #0x10
    // 0x833978: ret
    //     0x833978: ret             
    // 0x83397c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83397c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833980: b               #0x83395c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc33680, size: 0x4c
    // 0xc33680: ldr             x1, [SP]
    // 0xc33684: r2 = LoadClassIdInstr(r1)
    //     0xc33684: ldur            x2, [x1, #-1]
    //     0xc33688: ubfx            x2, x2, #0xc, #0x14
    // 0xc3368c: cmp             x2, #0x3af
    // 0xc33690: b.ne            #0xc336a4
    // 0xc33694: LoadField: r3 = r1->field_7
    //     0xc33694: ldur            w3, [x1, #7]
    // 0xc33698: DecompressPointer r3
    //     0xc33698: add             x3, x3, HEAP, lsl #32
    // 0xc3369c: mov             x0, x3
    // 0xc336a0: b               #0xc336c8
    // 0xc336a4: cmp             x2, #0x3b0
    // 0xc336a8: b.ne            #0xc336bc
    // 0xc336ac: LoadField: r2 = r1->field_7
    //     0xc336ac: ldur            w2, [x1, #7]
    // 0xc336b0: DecompressPointer r2
    //     0xc336b0: add             x2, x2, HEAP, lsl #32
    // 0xc336b4: mov             x0, x2
    // 0xc336b8: b               #0xc336c8
    // 0xc336bc: LoadField: r2 = r1->field_7
    //     0xc336bc: ldur            w2, [x1, #7]
    // 0xc336c0: DecompressPointer r2
    //     0xc336c0: add             x2, x2, HEAP, lsl #32
    // 0xc336c4: mov             x0, x2
    // 0xc336c8: ret
    //     0xc336c8: ret             
  }
}
