// lib: , url: package:path/src/style/posix.dart

// class id: 1050758, size: 0x8
class :: {
}

// class id: 945, size: 0x10, field offset: 0x8
class PosixStyle extends InternalStyle {

  _ PosixStyle(/* No info */) {
    // ** addr: 0x833738, size: 0xc4
    // 0x833738: EnterFrame
    //     0x833738: stp             fp, lr, [SP, #-0x10]!
    //     0x83373c: mov             fp, SP
    // 0x833740: AllocStack(0x30)
    //     0x833740: sub             SP, SP, #0x30
    // 0x833744: r2 = "posix"
    //     0x833744: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd40] "posix"
    //     0x833748: ldr             x2, [x2, #0xd40]
    // 0x83374c: r0 = "/"
    //     0x83374c: ldr             x0, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x833750: CheckStackOverflow
    //     0x833750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833754: cmp             SP, x16
    //     0x833758: b.ls            #0x8337f4
    // 0x83375c: StoreField: r1->field_7 = r2
    //     0x83375c: stur            w2, [x1, #7]
    // 0x833760: StoreField: r1->field_b = r0
    //     0x833760: stur            w0, [x1, #0xb]
    // 0x833764: r16 = "/"
    //     0x833764: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x833768: stp             x16, NULL, [SP, #0x20]
    // 0x83376c: r16 = false
    //     0x83376c: add             x16, NULL, #0x30  ; false
    // 0x833770: r30 = true
    //     0x833770: add             lr, NULL, #0x20  ; true
    // 0x833774: stp             lr, x16, [SP, #0x10]
    // 0x833778: r16 = false
    //     0x833778: add             x16, NULL, #0x30  ; false
    // 0x83377c: r30 = false
    //     0x83377c: add             lr, NULL, #0x30  ; false
    // 0x833780: stp             lr, x16, [SP]
    // 0x833784: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x833784: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x833788: r0 = _RegExp()
    //     0x833788: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x83378c: r16 = "[^/]$"
    //     0x83378c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd48] "[^/]$"
    //     0x833790: ldr             x16, [x16, #0xd48]
    // 0x833794: stp             x16, NULL, [SP, #0x20]
    // 0x833798: r16 = false
    //     0x833798: add             x16, NULL, #0x30  ; false
    // 0x83379c: r30 = true
    //     0x83379c: add             lr, NULL, #0x20  ; true
    // 0x8337a0: stp             lr, x16, [SP, #0x10]
    // 0x8337a4: r16 = false
    //     0x8337a4: add             x16, NULL, #0x30  ; false
    // 0x8337a8: r30 = false
    //     0x8337a8: add             lr, NULL, #0x30  ; false
    // 0x8337ac: stp             lr, x16, [SP]
    // 0x8337b0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8337b0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8337b4: r0 = _RegExp()
    //     0x8337b4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8337b8: r16 = "^/"
    //     0x8337b8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd50] "^/"
    //     0x8337bc: ldr             x16, [x16, #0xd50]
    // 0x8337c0: stp             x16, NULL, [SP, #0x20]
    // 0x8337c4: r16 = false
    //     0x8337c4: add             x16, NULL, #0x30  ; false
    // 0x8337c8: r30 = true
    //     0x8337c8: add             lr, NULL, #0x20  ; true
    // 0x8337cc: stp             lr, x16, [SP, #0x10]
    // 0x8337d0: r16 = false
    //     0x8337d0: add             x16, NULL, #0x30  ; false
    // 0x8337d4: r30 = false
    //     0x8337d4: add             lr, NULL, #0x30  ; false
    // 0x8337d8: stp             lr, x16, [SP]
    // 0x8337dc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8337dc: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8337e0: r0 = _RegExp()
    //     0x8337e0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8337e4: r0 = Null
    //     0x8337e4: mov             x0, NULL
    // 0x8337e8: LeaveFrame
    //     0x8337e8: mov             SP, fp
    //     0x8337ec: ldp             fp, lr, [SP], #0x10
    // 0x8337f0: ret
    //     0x8337f0: ret             
    // 0x8337f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8337f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8337f8: b               #0x83375c
  }
  _ pathFromUri(/* No info */) {
    // ** addr: 0xe7aad0, size: 0x138
    // 0xe7aad0: EnterFrame
    //     0xe7aad0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7aad4: mov             fp, SP
    // 0xe7aad8: AllocStack(0x18)
    //     0xe7aad8: sub             SP, SP, #0x18
    // 0xe7aadc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe7aadc: stur            x2, [fp, #-8]
    // 0xe7aae0: CheckStackOverflow
    //     0xe7aae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7aae4: cmp             SP, x16
    //     0xe7aae8: b.ls            #0xe7ac00
    // 0xe7aaec: r0 = LoadClassIdInstr(r2)
    //     0xe7aaec: ldur            x0, [x2, #-1]
    //     0xe7aaf0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7aaf4: mov             x1, x2
    // 0xe7aaf8: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xe7aaf8: sub             lr, x0, #0xfcb
    //     0xe7aafc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ab00: blr             lr
    // 0xe7ab04: r1 = LoadClassIdInstr(r0)
    //     0xe7ab04: ldur            x1, [x0, #-1]
    //     0xe7ab08: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ab0c: r16 = ""
    //     0xe7ab0c: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7ab10: stp             x16, x0, [SP]
    // 0xe7ab14: mov             x0, x1
    // 0xe7ab18: mov             lr, x0
    // 0xe7ab1c: ldr             lr, [x21, lr, lsl #3]
    // 0xe7ab20: blr             lr
    // 0xe7ab24: tbz             w0, #4, #0xe7ab68
    // 0xe7ab28: ldur            x2, [fp, #-8]
    // 0xe7ab2c: r0 = LoadClassIdInstr(r2)
    //     0xe7ab2c: ldur            x0, [x2, #-1]
    //     0xe7ab30: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ab34: mov             x1, x2
    // 0xe7ab38: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xe7ab38: sub             lr, x0, #0xfcb
    //     0xe7ab3c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ab40: blr             lr
    // 0xe7ab44: r1 = LoadClassIdInstr(r0)
    //     0xe7ab44: ldur            x1, [x0, #-1]
    //     0xe7ab48: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ab4c: r16 = "file"
    //     0xe7ab4c: ldr             x16, [PP, #0xc40]  ; [pp+0xc40] "file"
    // 0xe7ab50: stp             x16, x0, [SP]
    // 0xe7ab54: mov             x0, x1
    // 0xe7ab58: mov             lr, x0
    // 0xe7ab5c: ldr             lr, [x21, lr, lsl #3]
    // 0xe7ab60: blr             lr
    // 0xe7ab64: tbnz            w0, #4, #0xe7aba0
    // 0xe7ab68: ldur            x0, [fp, #-8]
    // 0xe7ab6c: r1 = LoadClassIdInstr(r0)
    //     0xe7ab6c: ldur            x1, [x0, #-1]
    //     0xe7ab70: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ab74: mov             x16, x0
    // 0xe7ab78: mov             x0, x1
    // 0xe7ab7c: mov             x1, x16
    // 0xe7ab80: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe7ab80: sub             lr, x0, #0xffa
    //     0xe7ab84: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ab88: blr             lr
    // 0xe7ab8c: mov             x1, x0
    // 0xe7ab90: r0 = decodeComponent()
    //     0xe7ab90: bl              #0x6763cc  ; [dart:core] Uri::decodeComponent
    // 0xe7ab94: LeaveFrame
    //     0xe7ab94: mov             SP, fp
    //     0xe7ab98: ldp             fp, lr, [SP], #0x10
    // 0xe7ab9c: ret
    //     0xe7ab9c: ret             
    // 0xe7aba0: ldur            x0, [fp, #-8]
    // 0xe7aba4: r1 = Null
    //     0xe7aba4: mov             x1, NULL
    // 0xe7aba8: r2 = 6
    //     0xe7aba8: movz            x2, #0x6
    // 0xe7abac: r0 = AllocateArray()
    //     0xe7abac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7abb0: r16 = "Uri "
    //     0xe7abb0: add             x16, PP, #0x20, lsl #12  ; [pp+0x20a90] "Uri "
    //     0xe7abb4: ldr             x16, [x16, #0xa90]
    // 0xe7abb8: StoreField: r0->field_f = r16
    //     0xe7abb8: stur            w16, [x0, #0xf]
    // 0xe7abbc: ldur            x1, [fp, #-8]
    // 0xe7abc0: StoreField: r0->field_13 = r1
    //     0xe7abc0: stur            w1, [x0, #0x13]
    // 0xe7abc4: r16 = " must have scheme \'file:\'."
    //     0xe7abc4: add             x16, PP, #0x20, lsl #12  ; [pp+0x20a98] " must have scheme \'file:\'."
    //     0xe7abc8: ldr             x16, [x16, #0xa98]
    // 0xe7abcc: ArrayStore: r0[0] = r16  ; List_4
    //     0xe7abcc: stur            w16, [x0, #0x17]
    // 0xe7abd0: str             x0, [SP]
    // 0xe7abd4: r0 = _interpolate()
    //     0xe7abd4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7abd8: stur            x0, [fp, #-8]
    // 0xe7abdc: r0 = ArgumentError()
    //     0xe7abdc: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe7abe0: mov             x1, x0
    // 0xe7abe4: ldur            x0, [fp, #-8]
    // 0xe7abe8: ArrayStore: r1[0] = r0  ; List_4
    //     0xe7abe8: stur            w0, [x1, #0x17]
    // 0xe7abec: r0 = false
    //     0xe7abec: add             x0, NULL, #0x30  ; false
    // 0xe7abf0: StoreField: r1->field_b = r0
    //     0xe7abf0: stur            w0, [x1, #0xb]
    // 0xe7abf4: mov             x0, x1
    // 0xe7abf8: r0 = Throw()
    //     0xe7abf8: bl              #0xec04b8  ; ThrowStub
    // 0xe7abfc: brk             #0
    // 0xe7ac00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ac00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ac04: b               #0xe7aaec
  }
  _ needsSeparator(/* No info */) {
    // ** addr: 0xe8d2c4, size: 0x70
    // 0xe8d2c4: LoadField: r3 = r2->field_7
    //     0xe8d2c4: ldur            w3, [x2, #7]
    // 0xe8d2c8: cbz             w3, #0xe8d320
    // 0xe8d2cc: r0 = LoadInt32Instr(r3)
    //     0xe8d2cc: sbfx            x0, x3, #1, #0x1f
    // 0xe8d2d0: sub             x3, x0, #1
    // 0xe8d2d4: mov             x1, x3
    // 0xe8d2d8: cmp             x1, x0
    // 0xe8d2dc: b.hs            #0xe8d328
    // 0xe8d2e0: r1 = LoadClassIdInstr(r2)
    //     0xe8d2e0: ldur            x1, [x2, #-1]
    //     0xe8d2e4: ubfx            x1, x1, #0xc, #0x14
    // 0xe8d2e8: lsl             x1, x1, #1
    // 0xe8d2ec: cmp             w1, #0xbc
    // 0xe8d2f0: b.ne            #0xe8d300
    // 0xe8d2f4: ArrayLoad: r1 = r2[r3]  ; TypedUnsigned_1
    //     0xe8d2f4: add             x16, x2, x3
    //     0xe8d2f8: ldrb            w1, [x16, #0xf]
    // 0xe8d2fc: b               #0xe8d308
    // 0xe8d300: add             x16, x2, x3, lsl #1
    // 0xe8d304: ldurh           w1, [x16, #0xf]
    // 0xe8d308: cmp             x1, #0x2f
    // 0xe8d30c: r16 = true
    //     0xe8d30c: add             x16, NULL, #0x20  ; true
    // 0xe8d310: r17 = false
    //     0xe8d310: add             x17, NULL, #0x30  ; false
    // 0xe8d314: csel            x2, x16, x17, ne
    // 0xe8d318: mov             x0, x2
    // 0xe8d31c: b               #0xe8d324
    // 0xe8d320: r0 = false
    //     0xe8d320: add             x0, NULL, #0x30  ; false
    // 0xe8d324: ret
    //     0xe8d324: ret             
    // 0xe8d328: EnterFrame
    //     0xe8d328: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d32c: mov             fp, SP
    // 0xe8d330: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8d330: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ rootLength(/* No info */) {
    // ** addr: 0xe8d4c4, size: 0x74
    // 0xe8d4c4: EnterFrame
    //     0xe8d4c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d4c8: mov             fp, SP
    // 0xe8d4cc: LoadField: r3 = r2->field_7
    //     0xe8d4cc: ldur            w3, [x2, #7]
    // 0xe8d4d0: cbz             w3, #0xe8d524
    // 0xe8d4d4: r0 = LoadInt32Instr(r3)
    //     0xe8d4d4: sbfx            x0, x3, #1, #0x1f
    // 0xe8d4d8: r1 = 0
    //     0xe8d4d8: movz            x1, #0
    // 0xe8d4dc: cmp             x1, x0
    // 0xe8d4e0: b.hs            #0xe8d534
    // 0xe8d4e4: r1 = LoadClassIdInstr(r2)
    //     0xe8d4e4: ldur            x1, [x2, #-1]
    //     0xe8d4e8: ubfx            x1, x1, #0xc, #0x14
    // 0xe8d4ec: lsl             x1, x1, #1
    // 0xe8d4f0: cmp             w1, #0xbc
    // 0xe8d4f4: b.ne            #0xe8d508
    // 0xe8d4f8: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0xe8d4f8: ldrb            w1, [x2, #0xf]
    // 0xe8d4fc: cmp             x1, #0x2f
    // 0xe8d500: b.ne            #0xe8d524
    // 0xe8d504: b               #0xe8d514
    // 0xe8d508: ldurh           w1, [x2, #0xf]
    // 0xe8d50c: cmp             x1, #0x2f
    // 0xe8d510: b.ne            #0xe8d524
    // 0xe8d514: r0 = 1
    //     0xe8d514: movz            x0, #0x1
    // 0xe8d518: LeaveFrame
    //     0xe8d518: mov             SP, fp
    //     0xe8d51c: ldp             fp, lr, [SP], #0x10
    // 0xe8d520: ret
    //     0xe8d520: ret             
    // 0xe8d524: r0 = 0
    //     0xe8d524: movz            x0, #0
    // 0xe8d528: LeaveFrame
    //     0xe8d528: mov             SP, fp
    //     0xe8d52c: ldp             fp, lr, [SP], #0x10
    // 0xe8d530: ret
    //     0xe8d530: ret             
    // 0xe8d534: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8d534: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
