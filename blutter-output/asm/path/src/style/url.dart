// lib: , url: package:path/src/style/url.dart

// class id: 1050759, size: 0x8
class :: {
}

// class id: 944, size: 0x10, field offset: 0x8
class UrlStyle extends InternalStyle {

  _ UrlStyle(/* No info */) {
    // ** addr: 0x833984, size: 0xf0
    // 0x833984: EnterFrame
    //     0x833984: stp             fp, lr, [SP, #-0x10]!
    //     0x833988: mov             fp, SP
    // 0x83398c: AllocStack(0x30)
    //     0x83398c: sub             SP, SP, #0x30
    // 0x833990: r2 = "url"
    //     0x833990: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x833994: ldr             x2, [x2, #0xd78]
    // 0x833998: r0 = "/"
    //     0x833998: ldr             x0, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x83399c: CheckStackOverflow
    //     0x83399c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8339a0: cmp             SP, x16
    //     0x8339a4: b.ls            #0x833a6c
    // 0x8339a8: StoreField: r1->field_7 = r2
    //     0x8339a8: stur            w2, [x1, #7]
    // 0x8339ac: StoreField: r1->field_b = r0
    //     0x8339ac: stur            w0, [x1, #0xb]
    // 0x8339b0: r16 = "/"
    //     0x8339b0: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x8339b4: stp             x16, NULL, [SP, #0x20]
    // 0x8339b8: r16 = false
    //     0x8339b8: add             x16, NULL, #0x30  ; false
    // 0x8339bc: r30 = true
    //     0x8339bc: add             lr, NULL, #0x20  ; true
    // 0x8339c0: stp             lr, x16, [SP, #0x10]
    // 0x8339c4: r16 = false
    //     0x8339c4: add             x16, NULL, #0x30  ; false
    // 0x8339c8: r30 = false
    //     0x8339c8: add             lr, NULL, #0x30  ; false
    // 0x8339cc: stp             lr, x16, [SP]
    // 0x8339d0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8339d0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8339d4: r0 = _RegExp()
    //     0x8339d4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8339d8: r16 = "(^[a-zA-Z][-+.a-zA-Z\\d]*://|[^/])$"
    //     0x8339d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd80] "(^[a-zA-Z][-+.a-zA-Z\\d]*://|[^/])$"
    //     0x8339dc: ldr             x16, [x16, #0xd80]
    // 0x8339e0: stp             x16, NULL, [SP, #0x20]
    // 0x8339e4: r16 = false
    //     0x8339e4: add             x16, NULL, #0x30  ; false
    // 0x8339e8: r30 = true
    //     0x8339e8: add             lr, NULL, #0x20  ; true
    // 0x8339ec: stp             lr, x16, [SP, #0x10]
    // 0x8339f0: r16 = false
    //     0x8339f0: add             x16, NULL, #0x30  ; false
    // 0x8339f4: r30 = false
    //     0x8339f4: add             lr, NULL, #0x30  ; false
    // 0x8339f8: stp             lr, x16, [SP]
    // 0x8339fc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8339fc: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x833a00: r0 = _RegExp()
    //     0x833a00: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x833a04: r16 = "[a-zA-Z][-+.a-zA-Z\\d]*://[^/]*"
    //     0x833a04: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd88] "[a-zA-Z][-+.a-zA-Z\\d]*://[^/]*"
    //     0x833a08: ldr             x16, [x16, #0xd88]
    // 0x833a0c: stp             x16, NULL, [SP, #0x20]
    // 0x833a10: r16 = false
    //     0x833a10: add             x16, NULL, #0x30  ; false
    // 0x833a14: r30 = true
    //     0x833a14: add             lr, NULL, #0x20  ; true
    // 0x833a18: stp             lr, x16, [SP, #0x10]
    // 0x833a1c: r16 = false
    //     0x833a1c: add             x16, NULL, #0x30  ; false
    // 0x833a20: r30 = false
    //     0x833a20: add             lr, NULL, #0x30  ; false
    // 0x833a24: stp             lr, x16, [SP]
    // 0x833a28: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x833a28: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x833a2c: r0 = _RegExp()
    //     0x833a2c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x833a30: r16 = "^/"
    //     0x833a30: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd50] "^/"
    //     0x833a34: ldr             x16, [x16, #0xd50]
    // 0x833a38: stp             x16, NULL, [SP, #0x20]
    // 0x833a3c: r16 = false
    //     0x833a3c: add             x16, NULL, #0x30  ; false
    // 0x833a40: r30 = true
    //     0x833a40: add             lr, NULL, #0x20  ; true
    // 0x833a44: stp             lr, x16, [SP, #0x10]
    // 0x833a48: r16 = false
    //     0x833a48: add             x16, NULL, #0x30  ; false
    // 0x833a4c: r30 = false
    //     0x833a4c: add             lr, NULL, #0x30  ; false
    // 0x833a50: stp             lr, x16, [SP]
    // 0x833a54: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x833a54: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x833a58: r0 = _RegExp()
    //     0x833a58: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x833a5c: r0 = Null
    //     0x833a5c: mov             x0, NULL
    // 0x833a60: LeaveFrame
    //     0x833a60: mov             SP, fp
    //     0x833a64: ldp             fp, lr, [SP], #0x10
    // 0x833a68: ret
    //     0x833a68: ret             
    // 0x833a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833a70: b               #0x8339a8
  }
  _ pathFromUri(/* No info */) {
    // ** addr: 0xe7ac08, size: 0x4c
    // 0xe7ac08: EnterFrame
    //     0xe7ac08: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ac0c: mov             fp, SP
    // 0xe7ac10: AllocStack(0x8)
    //     0xe7ac10: sub             SP, SP, #8
    // 0xe7ac14: CheckStackOverflow
    //     0xe7ac14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ac18: cmp             SP, x16
    //     0xe7ac1c: b.ls            #0xe7ac4c
    // 0xe7ac20: r0 = LoadClassIdInstr(r2)
    //     0xe7ac20: ldur            x0, [x2, #-1]
    //     0xe7ac24: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ac28: str             x2, [SP]
    // 0xe7ac2c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7ac2c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7ac30: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7ac30: movz            x17, #0x2b03
    //     0xe7ac34: add             lr, x0, x17
    //     0xe7ac38: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ac3c: blr             lr
    // 0xe7ac40: LeaveFrame
    //     0xe7ac40: mov             SP, fp
    //     0xe7ac44: ldp             fp, lr, [SP], #0x10
    // 0xe7ac48: ret
    //     0xe7ac48: ret             
    // 0xe7ac4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ac4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ac50: b               #0xe7ac20
  }
  _ needsSeparator(/* No info */) {
    // ** addr: 0xe8d334, size: 0x10c
    // 0xe8d334: EnterFrame
    //     0xe8d334: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d338: mov             fp, SP
    // 0xe8d33c: AllocStack(0x30)
    //     0xe8d33c: sub             SP, SP, #0x30
    // 0xe8d340: SetupParameters(UrlStyle this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe8d340: mov             x3, x1
    //     0xe8d344: stur            x1, [fp, #-0x10]
    //     0xe8d348: stur            x2, [fp, #-0x18]
    // 0xe8d34c: CheckStackOverflow
    //     0xe8d34c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8d350: cmp             SP, x16
    //     0xe8d354: b.ls            #0xe8d434
    // 0xe8d358: LoadField: r0 = r2->field_7
    //     0xe8d358: ldur            w0, [x2, #7]
    // 0xe8d35c: cbnz            w0, #0xe8d370
    // 0xe8d360: r0 = false
    //     0xe8d360: add             x0, NULL, #0x30  ; false
    // 0xe8d364: LeaveFrame
    //     0xe8d364: mov             SP, fp
    //     0xe8d368: ldp             fp, lr, [SP], #0x10
    // 0xe8d36c: ret
    //     0xe8d36c: ret             
    // 0xe8d370: r4 = LoadInt32Instr(r0)
    //     0xe8d370: sbfx            x4, x0, #1, #0x1f
    // 0xe8d374: stur            x4, [fp, #-8]
    // 0xe8d378: sub             x5, x4, #1
    // 0xe8d37c: mov             x0, x4
    // 0xe8d380: mov             x1, x5
    // 0xe8d384: cmp             x1, x0
    // 0xe8d388: b.hs            #0xe8d43c
    // 0xe8d38c: r0 = LoadClassIdInstr(r2)
    //     0xe8d38c: ldur            x0, [x2, #-1]
    //     0xe8d390: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d394: lsl             x0, x0, #1
    // 0xe8d398: cmp             w0, #0xbc
    // 0xe8d39c: b.ne            #0xe8d3b4
    // 0xe8d3a0: ArrayLoad: r0 = r2[r5]  ; TypedUnsigned_1
    //     0xe8d3a0: add             x16, x2, x5
    //     0xe8d3a4: ldrb            w0, [x16, #0xf]
    // 0xe8d3a8: cmp             x0, #0x2f
    // 0xe8d3ac: b.eq            #0xe8d3d4
    // 0xe8d3b0: b               #0xe8d3c4
    // 0xe8d3b4: add             x16, x2, x5, lsl #1
    // 0xe8d3b8: ldurh           w0, [x16, #0xf]
    // 0xe8d3bc: cmp             x0, #0x2f
    // 0xe8d3c0: b.eq            #0xe8d3d4
    // 0xe8d3c4: r0 = true
    //     0xe8d3c4: add             x0, NULL, #0x20  ; true
    // 0xe8d3c8: LeaveFrame
    //     0xe8d3c8: mov             SP, fp
    //     0xe8d3cc: ldp             fp, lr, [SP], #0x10
    // 0xe8d3d0: ret
    //     0xe8d3d0: ret             
    // 0xe8d3d4: sub             x0, x4, #3
    // 0xe8d3d8: lsl             x1, x0, #1
    // 0xe8d3dc: stp             x1, x2, [SP, #8]
    // 0xe8d3e0: r16 = "://"
    //     0xe8d3e0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11f90] "://"
    //     0xe8d3e4: ldr             x16, [x16, #0xf90]
    // 0xe8d3e8: str             x16, [SP]
    // 0xe8d3ec: r0 = _substringMatches()
    //     0xe8d3ec: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xe8d3f0: tbnz            w0, #4, #0xe8d424
    // 0xe8d3f4: ldur            x0, [fp, #-8]
    // 0xe8d3f8: ldur            x1, [fp, #-0x10]
    // 0xe8d3fc: ldur            x2, [fp, #-0x18]
    // 0xe8d400: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe8d400: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe8d404: r0 = rootLength()
    //     0xe8d404: bl              #0xe8d538  ; [package:path/src/style/url.dart] UrlStyle::rootLength
    // 0xe8d408: ldur            x1, [fp, #-8]
    // 0xe8d40c: cmp             x0, x1
    // 0xe8d410: r16 = true
    //     0xe8d410: add             x16, NULL, #0x20  ; true
    // 0xe8d414: r17 = false
    //     0xe8d414: add             x17, NULL, #0x30  ; false
    // 0xe8d418: csel            x2, x16, x17, eq
    // 0xe8d41c: mov             x0, x2
    // 0xe8d420: b               #0xe8d428
    // 0xe8d424: r0 = false
    //     0xe8d424: add             x0, NULL, #0x30  ; false
    // 0xe8d428: LeaveFrame
    //     0xe8d428: mov             SP, fp
    //     0xe8d42c: ldp             fp, lr, [SP], #0x10
    // 0xe8d430: ret
    //     0xe8d430: ret             
    // 0xe8d434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8d434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8d438: b               #0xe8d358
    // 0xe8d43c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8d43c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ rootLength(/* No info */) {
    // ** addr: 0xe8d538, size: 0x26c
    // 0xe8d538: EnterFrame
    //     0xe8d538: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d53c: mov             fp, SP
    // 0xe8d540: AllocStack(0x28)
    //     0xe8d540: sub             SP, SP, #0x28
    // 0xe8d544: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */, {dynamic withDrive = false /* r4, fp-0x18 */})
    //     0xe8d544: mov             x3, x2
    //     0xe8d548: stur            x2, [fp, #-0x20]
    //     0xe8d54c: ldur            w0, [x4, #0x13]
    //     0xe8d550: ldur            w1, [x4, #0x1f]
    //     0xe8d554: add             x1, x1, HEAP, lsl #32
    //     0xe8d558: add             x16, PP, #0x12, lsl #12  ; [pp+0x127c0] "withDrive"
    //     0xe8d55c: ldr             x16, [x16, #0x7c0]
    //     0xe8d560: cmp             w1, w16
    //     0xe8d564: b.ne            #0xe8d584
    //     0xe8d568: ldur            w1, [x4, #0x23]
    //     0xe8d56c: add             x1, x1, HEAP, lsl #32
    //     0xe8d570: sub             w2, w0, w1
    //     0xe8d574: add             x0, fp, w2, sxtw #2
    //     0xe8d578: ldr             x0, [x0, #8]
    //     0xe8d57c: mov             x4, x0
    //     0xe8d580: b               #0xe8d588
    //     0xe8d584: add             x4, NULL, #0x30  ; false
    //     0xe8d588: stur            x4, [fp, #-0x18]
    // 0xe8d58c: CheckStackOverflow
    //     0xe8d58c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8d590: cmp             SP, x16
    //     0xe8d594: b.ls            #0xe8d790
    // 0xe8d598: LoadField: r0 = r3->field_7
    //     0xe8d598: ldur            w0, [x3, #7]
    // 0xe8d59c: cbnz            w0, #0xe8d5b0
    // 0xe8d5a0: r0 = 0
    //     0xe8d5a0: movz            x0, #0
    // 0xe8d5a4: LeaveFrame
    //     0xe8d5a4: mov             SP, fp
    //     0xe8d5a8: ldp             fp, lr, [SP], #0x10
    // 0xe8d5ac: ret
    //     0xe8d5ac: ret             
    // 0xe8d5b0: r5 = LoadInt32Instr(r0)
    //     0xe8d5b0: sbfx            x5, x0, #1, #0x1f
    // 0xe8d5b4: mov             x0, x5
    // 0xe8d5b8: stur            x5, [fp, #-0x10]
    // 0xe8d5bc: r1 = 0
    //     0xe8d5bc: movz            x1, #0
    // 0xe8d5c0: cmp             x1, x0
    // 0xe8d5c4: b.hs            #0xe8d798
    // 0xe8d5c8: r0 = LoadClassIdInstr(r3)
    //     0xe8d5c8: ldur            x0, [x3, #-1]
    //     0xe8d5cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d5d0: lsl             x0, x0, #1
    // 0xe8d5d4: cmp             w0, #0xbc
    // 0xe8d5d8: b.ne            #0xe8d5ec
    // 0xe8d5dc: ArrayLoad: r1 = r3[-8]  ; TypedUnsigned_1
    //     0xe8d5dc: ldrb            w1, [x3, #0xf]
    // 0xe8d5e0: cmp             x1, #0x2f
    // 0xe8d5e4: b.ne            #0xe8d608
    // 0xe8d5e8: b               #0xe8d5f8
    // 0xe8d5ec: ldurh           w1, [x3, #0xf]
    // 0xe8d5f0: cmp             x1, #0x2f
    // 0xe8d5f4: b.ne            #0xe8d608
    // 0xe8d5f8: r0 = 1
    //     0xe8d5f8: movz            x0, #0x1
    // 0xe8d5fc: LeaveFrame
    //     0xe8d5fc: mov             SP, fp
    //     0xe8d600: ldp             fp, lr, [SP], #0x10
    // 0xe8d604: ret
    //     0xe8d604: ret             
    // 0xe8d608: r6 = 0
    //     0xe8d608: movz            x6, #0
    // 0xe8d60c: stur            x6, [fp, #-8]
    // 0xe8d610: CheckStackOverflow
    //     0xe8d610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8d614: cmp             SP, x16
    //     0xe8d618: b.ls            #0xe8d79c
    // 0xe8d61c: cmp             x6, x5
    // 0xe8d620: b.ge            #0xe8d780
    // 0xe8d624: cmp             w0, #0xbc
    // 0xe8d628: b.ne            #0xe8d638
    // 0xe8d62c: ArrayLoad: r1 = r3[r6]  ; TypedUnsigned_1
    //     0xe8d62c: add             x16, x3, x6
    //     0xe8d630: ldrb            w1, [x16, #0xf]
    // 0xe8d634: b               #0xe8d640
    // 0xe8d638: add             x16, x3, x6, lsl #1
    // 0xe8d63c: ldurh           w1, [x16, #0xf]
    // 0xe8d640: cmp             x1, #0x2f
    // 0xe8d644: b.eq            #0xe8d770
    // 0xe8d648: cmp             x1, #0x3a
    // 0xe8d64c: b.eq            #0xe8d65c
    // 0xe8d650: add             x1, x6, #1
    // 0xe8d654: mov             x6, x1
    // 0xe8d658: b               #0xe8d60c
    // 0xe8d65c: cbnz            x6, #0xe8d670
    // 0xe8d660: r0 = 0
    //     0xe8d660: movz            x0, #0
    // 0xe8d664: LeaveFrame
    //     0xe8d664: mov             SP, fp
    //     0xe8d668: ldp             fp, lr, [SP], #0x10
    // 0xe8d66c: ret
    //     0xe8d66c: ret             
    // 0xe8d670: add             x0, x6, #1
    // 0xe8d674: lsl             x1, x0, #1
    // 0xe8d678: str             x1, [SP]
    // 0xe8d67c: mov             x1, x3
    // 0xe8d680: r2 = "//"
    //     0xe8d680: ldr             x2, [PP, #0x3670]  ; [pp+0x3670] "//"
    // 0xe8d684: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe8d684: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe8d688: r0 = startsWith()
    //     0xe8d688: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe8d68c: tbnz            w0, #4, #0xe8d6a0
    // 0xe8d690: ldur            x0, [fp, #-8]
    // 0xe8d694: add             x1, x0, #3
    // 0xe8d698: mov             x0, x1
    // 0xe8d69c: b               #0xe8d6a4
    // 0xe8d6a0: ldur            x0, [fp, #-8]
    // 0xe8d6a4: ldur            x3, [fp, #-0x20]
    // 0xe8d6a8: lsl             x1, x0, #1
    // 0xe8d6ac: r0 = LoadClassIdInstr(r3)
    //     0xe8d6ac: ldur            x0, [x3, #-1]
    //     0xe8d6b0: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d6b4: str             x1, [SP]
    // 0xe8d6b8: mov             x1, x3
    // 0xe8d6bc: r2 = "/"
    //     0xe8d6bc: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe8d6c0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe8d6c0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe8d6c4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe8d6c4: sub             lr, x0, #0xffa
    //     0xe8d6c8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8d6cc: blr             lr
    // 0xe8d6d0: stur            x0, [fp, #-8]
    // 0xe8d6d4: cmp             x0, #0
    // 0xe8d6d8: b.gt            #0xe8d6ec
    // 0xe8d6dc: ldur            x0, [fp, #-0x10]
    // 0xe8d6e0: LeaveFrame
    //     0xe8d6e0: mov             SP, fp
    //     0xe8d6e4: ldp             fp, lr, [SP], #0x10
    // 0xe8d6e8: ret
    //     0xe8d6e8: ret             
    // 0xe8d6ec: ldur            x1, [fp, #-0x18]
    // 0xe8d6f0: tbnz            w1, #4, #0xe8d704
    // 0xe8d6f4: ldur            x1, [fp, #-0x10]
    // 0xe8d6f8: add             x2, x0, #3
    // 0xe8d6fc: cmp             x1, x2
    // 0xe8d700: b.ge            #0xe8d710
    // 0xe8d704: LeaveFrame
    //     0xe8d704: mov             SP, fp
    //     0xe8d708: ldp             fp, lr, [SP], #0x10
    // 0xe8d70c: ret
    //     0xe8d70c: ret             
    // 0xe8d710: ldur            x1, [fp, #-0x20]
    // 0xe8d714: r2 = "file://"
    //     0xe8d714: ldr             x2, [PP, #0xc50]  ; [pp+0xc50] "file://"
    // 0xe8d718: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe8d718: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe8d71c: r0 = startsWith()
    //     0xe8d71c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe8d720: tbz             w0, #4, #0xe8d734
    // 0xe8d724: ldur            x0, [fp, #-8]
    // 0xe8d728: LeaveFrame
    //     0xe8d728: mov             SP, fp
    //     0xe8d72c: ldp             fp, lr, [SP], #0x10
    // 0xe8d730: ret
    //     0xe8d730: ret             
    // 0xe8d734: ldur            x0, [fp, #-8]
    // 0xe8d738: add             x2, x0, #1
    // 0xe8d73c: ldur            x1, [fp, #-0x20]
    // 0xe8d740: r0 = driveLetterEnd()
    //     0xe8d740: bl              #0xe7af1c  ; [package:path/src/utils.dart] ::driveLetterEnd
    // 0xe8d744: cmp             w0, NULL
    // 0xe8d748: b.ne            #0xe8d754
    // 0xe8d74c: ldur            x0, [fp, #-8]
    // 0xe8d750: b               #0xe8d764
    // 0xe8d754: r1 = LoadInt32Instr(r0)
    //     0xe8d754: sbfx            x1, x0, #1, #0x1f
    //     0xe8d758: tbz             w0, #0, #0xe8d760
    //     0xe8d75c: ldur            x1, [x0, #7]
    // 0xe8d760: mov             x0, x1
    // 0xe8d764: LeaveFrame
    //     0xe8d764: mov             SP, fp
    //     0xe8d768: ldp             fp, lr, [SP], #0x10
    // 0xe8d76c: ret
    //     0xe8d76c: ret             
    // 0xe8d770: r0 = 0
    //     0xe8d770: movz            x0, #0
    // 0xe8d774: LeaveFrame
    //     0xe8d774: mov             SP, fp
    //     0xe8d778: ldp             fp, lr, [SP], #0x10
    // 0xe8d77c: ret
    //     0xe8d77c: ret             
    // 0xe8d780: r0 = 0
    //     0xe8d780: movz            x0, #0
    // 0xe8d784: LeaveFrame
    //     0xe8d784: mov             SP, fp
    //     0xe8d788: ldp             fp, lr, [SP], #0x10
    // 0xe8d78c: ret
    //     0xe8d78c: ret             
    // 0xe8d790: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8d790: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8d794: b               #0xe8d598
    // 0xe8d798: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8d798: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8d79c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8d79c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8d7a0: b               #0xe8d61c
  }
}
