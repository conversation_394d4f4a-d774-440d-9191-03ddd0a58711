// lib: , url: package:path/src/style/windows.dart

// class id: 1050760, size: 0x8
class :: {
}

// class id: 943, size: 0x10, field offset: 0x8
class WindowsStyle extends InternalStyle {

  _ WindowsStyle(/* No info */) {
    // ** addr: 0x833848, size: 0xf0
    // 0x833848: EnterFrame
    //     0x833848: stp             fp, lr, [SP, #-0x10]!
    //     0x83384c: mov             fp, SP
    // 0x833850: AllocStack(0x30)
    //     0x833850: sub             SP, SP, #0x30
    // 0x833854: r2 = "windows"
    //     0x833854: ldr             x2, [PP, #0x3c88]  ; [pp+0x3c88] "windows"
    // 0x833858: r0 = "\\"
    //     0x833858: ldr             x0, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0x83385c: CheckStackOverflow
    //     0x83385c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833860: cmp             SP, x16
    //     0x833864: b.ls            #0x833930
    // 0x833868: StoreField: r1->field_7 = r2
    //     0x833868: stur            w2, [x1, #7]
    // 0x83386c: StoreField: r1->field_b = r0
    //     0x83386c: stur            w0, [x1, #0xb]
    // 0x833870: r16 = "[/\\\\]"
    //     0x833870: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd58] "[/\\\\]"
    //     0x833874: ldr             x16, [x16, #0xd58]
    // 0x833878: stp             x16, NULL, [SP, #0x20]
    // 0x83387c: r16 = false
    //     0x83387c: add             x16, NULL, #0x30  ; false
    // 0x833880: r30 = true
    //     0x833880: add             lr, NULL, #0x20  ; true
    // 0x833884: stp             lr, x16, [SP, #0x10]
    // 0x833888: r16 = false
    //     0x833888: add             x16, NULL, #0x30  ; false
    // 0x83388c: r30 = false
    //     0x83388c: add             lr, NULL, #0x30  ; false
    // 0x833890: stp             lr, x16, [SP]
    // 0x833894: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x833894: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x833898: r0 = _RegExp()
    //     0x833898: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x83389c: r16 = "[^/\\\\]$"
    //     0x83389c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd60] "[^/\\\\]$"
    //     0x8338a0: ldr             x16, [x16, #0xd60]
    // 0x8338a4: stp             x16, NULL, [SP, #0x20]
    // 0x8338a8: r16 = false
    //     0x8338a8: add             x16, NULL, #0x30  ; false
    // 0x8338ac: r30 = true
    //     0x8338ac: add             lr, NULL, #0x20  ; true
    // 0x8338b0: stp             lr, x16, [SP, #0x10]
    // 0x8338b4: r16 = false
    //     0x8338b4: add             x16, NULL, #0x30  ; false
    // 0x8338b8: r30 = false
    //     0x8338b8: add             lr, NULL, #0x30  ; false
    // 0x8338bc: stp             lr, x16, [SP]
    // 0x8338c0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8338c0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8338c4: r0 = _RegExp()
    //     0x8338c4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8338c8: r16 = "^(\\\\\\\\[^\\\\]+\\\\[^\\\\/]+|[a-zA-Z]:[/\\\\])"
    //     0x8338c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd68] "^(\\\\\\\\[^\\\\]+\\\\[^\\\\/]+|[a-zA-Z]:[/\\\\])"
    //     0x8338cc: ldr             x16, [x16, #0xd68]
    // 0x8338d0: stp             x16, NULL, [SP, #0x20]
    // 0x8338d4: r16 = false
    //     0x8338d4: add             x16, NULL, #0x30  ; false
    // 0x8338d8: r30 = true
    //     0x8338d8: add             lr, NULL, #0x20  ; true
    // 0x8338dc: stp             lr, x16, [SP, #0x10]
    // 0x8338e0: r16 = false
    //     0x8338e0: add             x16, NULL, #0x30  ; false
    // 0x8338e4: r30 = false
    //     0x8338e4: add             lr, NULL, #0x30  ; false
    // 0x8338e8: stp             lr, x16, [SP]
    // 0x8338ec: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8338ec: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8338f0: r0 = _RegExp()
    //     0x8338f0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8338f4: r16 = "^[/\\\\](\?![/\\\\])"
    //     0x8338f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd70] "^[/\\\\](\?![/\\\\])"
    //     0x8338f8: ldr             x16, [x16, #0xd70]
    // 0x8338fc: stp             x16, NULL, [SP, #0x20]
    // 0x833900: r16 = false
    //     0x833900: add             x16, NULL, #0x30  ; false
    // 0x833904: r30 = true
    //     0x833904: add             lr, NULL, #0x20  ; true
    // 0x833908: stp             lr, x16, [SP, #0x10]
    // 0x83390c: r16 = false
    //     0x83390c: add             x16, NULL, #0x30  ; false
    // 0x833910: r30 = false
    //     0x833910: add             lr, NULL, #0x30  ; false
    // 0x833914: stp             lr, x16, [SP]
    // 0x833918: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x833918: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x83391c: r0 = _RegExp()
    //     0x83391c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x833920: r0 = Null
    //     0x833920: mov             x0, NULL
    // 0x833924: LeaveFrame
    //     0x833924: mov             SP, fp
    //     0x833928: ldp             fp, lr, [SP], #0x10
    // 0x83392c: ret
    //     0x83392c: ret             
    // 0x833930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833934: b               #0x833868
  }
  _ pathFromUri(/* No info */) {
    // ** addr: 0xe7ac54, size: 0x284
    // 0xe7ac54: EnterFrame
    //     0xe7ac54: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ac58: mov             fp, SP
    // 0xe7ac5c: AllocStack(0x28)
    //     0xe7ac5c: sub             SP, SP, #0x28
    // 0xe7ac60: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe7ac60: stur            x2, [fp, #-8]
    // 0xe7ac64: CheckStackOverflow
    //     0xe7ac64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ac68: cmp             SP, x16
    //     0xe7ac6c: b.ls            #0xe7aed0
    // 0xe7ac70: r0 = LoadClassIdInstr(r2)
    //     0xe7ac70: ldur            x0, [x2, #-1]
    //     0xe7ac74: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ac78: mov             x1, x2
    // 0xe7ac7c: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xe7ac7c: sub             lr, x0, #0xfcb
    //     0xe7ac80: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ac84: blr             lr
    // 0xe7ac88: r1 = LoadClassIdInstr(r0)
    //     0xe7ac88: ldur            x1, [x0, #-1]
    //     0xe7ac8c: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ac90: r16 = ""
    //     0xe7ac90: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7ac94: stp             x16, x0, [SP]
    // 0xe7ac98: mov             x0, x1
    // 0xe7ac9c: mov             lr, x0
    // 0xe7aca0: ldr             lr, [x21, lr, lsl #3]
    // 0xe7aca4: blr             lr
    // 0xe7aca8: tbz             w0, #4, #0xe7acec
    // 0xe7acac: ldur            x2, [fp, #-8]
    // 0xe7acb0: r0 = LoadClassIdInstr(r2)
    //     0xe7acb0: ldur            x0, [x2, #-1]
    //     0xe7acb4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7acb8: mov             x1, x2
    // 0xe7acbc: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xe7acbc: sub             lr, x0, #0xfcb
    //     0xe7acc0: ldr             lr, [x21, lr, lsl #3]
    //     0xe7acc4: blr             lr
    // 0xe7acc8: r1 = LoadClassIdInstr(r0)
    //     0xe7acc8: ldur            x1, [x0, #-1]
    //     0xe7accc: ubfx            x1, x1, #0xc, #0x14
    // 0xe7acd0: r16 = "file"
    //     0xe7acd0: ldr             x16, [PP, #0xc40]  ; [pp+0xc40] "file"
    // 0xe7acd4: stp             x16, x0, [SP]
    // 0xe7acd8: mov             x0, x1
    // 0xe7acdc: mov             lr, x0
    // 0xe7ace0: ldr             lr, [x21, lr, lsl #3]
    // 0xe7ace4: blr             lr
    // 0xe7ace8: tbnz            w0, #4, #0xe7ae70
    // 0xe7acec: ldur            x2, [fp, #-8]
    // 0xe7acf0: r0 = LoadClassIdInstr(r2)
    //     0xe7acf0: ldur            x0, [x2, #-1]
    //     0xe7acf4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7acf8: mov             x1, x2
    // 0xe7acfc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe7acfc: sub             lr, x0, #0xffa
    //     0xe7ad00: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ad04: blr             lr
    // 0xe7ad08: mov             x3, x0
    // 0xe7ad0c: ldur            x2, [fp, #-8]
    // 0xe7ad10: stur            x3, [fp, #-0x10]
    // 0xe7ad14: r0 = LoadClassIdInstr(r2)
    //     0xe7ad14: ldur            x0, [x2, #-1]
    //     0xe7ad18: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ad1c: mov             x1, x2
    // 0xe7ad20: r0 = GDT[cid_x0 + -0xf97]()
    //     0xe7ad20: sub             lr, x0, #0xf97
    //     0xe7ad24: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ad28: blr             lr
    // 0xe7ad2c: r1 = LoadClassIdInstr(r0)
    //     0xe7ad2c: ldur            x1, [x0, #-1]
    //     0xe7ad30: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ad34: r16 = ""
    //     0xe7ad34: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7ad38: stp             x16, x0, [SP]
    // 0xe7ad3c: mov             x0, x1
    // 0xe7ad40: mov             lr, x0
    // 0xe7ad44: ldr             lr, [x21, lr, lsl #3]
    // 0xe7ad48: blr             lr
    // 0xe7ad4c: tbnz            w0, #4, #0xe7ada4
    // 0xe7ad50: ldur            x0, [fp, #-0x10]
    // 0xe7ad54: LoadField: r1 = r0->field_7
    //     0xe7ad54: ldur            w1, [x0, #7]
    // 0xe7ad58: r2 = LoadInt32Instr(r1)
    //     0xe7ad58: sbfx            x2, x1, #1, #0x1f
    // 0xe7ad5c: cmp             x2, #3
    // 0xe7ad60: b.lt            #0xe7ad98
    // 0xe7ad64: mov             x1, x0
    // 0xe7ad68: r2 = "/"
    //     0xe7ad68: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe7ad6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7ad6c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe7ad70: r0 = startsWith()
    //     0xe7ad70: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe7ad74: tbnz            w0, #4, #0xe7ad98
    // 0xe7ad78: ldur            x1, [fp, #-0x10]
    // 0xe7ad7c: r0 = isDriveLetter()
    //     0xe7ad7c: bl              #0xe7aed8  ; [package:path/src/utils.dart] ::isDriveLetter
    // 0xe7ad80: tbnz            w0, #4, #0xe7ad98
    // 0xe7ad84: ldur            x1, [fp, #-0x10]
    // 0xe7ad88: r2 = "/"
    //     0xe7ad88: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe7ad8c: r3 = ""
    //     0xe7ad8c: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7ad90: r0 = replaceFirst()
    //     0xe7ad90: bl              #0x6440c0  ; [dart:core] _StringBase::replaceFirst
    // 0xe7ad94: b               #0xe7ad9c
    // 0xe7ad98: ldur            x0, [fp, #-0x10]
    // 0xe7ad9c: mov             x1, x0
    // 0xe7ada0: b               #0xe7ae50
    // 0xe7ada4: ldur            x0, [fp, #-8]
    // 0xe7ada8: r1 = Null
    //     0xe7ada8: mov             x1, NULL
    // 0xe7adac: r2 = 6
    //     0xe7adac: movz            x2, #0x6
    // 0xe7adb0: r0 = AllocateArray()
    //     0xe7adb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7adb4: mov             x2, x0
    // 0xe7adb8: stur            x2, [fp, #-0x18]
    // 0xe7adbc: r16 = "\\\\"
    //     0xe7adbc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10800] "\\\\"
    //     0xe7adc0: ldr             x16, [x16, #0x800]
    // 0xe7adc4: StoreField: r2->field_f = r16
    //     0xe7adc4: stur            w16, [x2, #0xf]
    // 0xe7adc8: ldur            x0, [fp, #-8]
    // 0xe7adcc: r1 = LoadClassIdInstr(r0)
    //     0xe7adcc: ldur            x1, [x0, #-1]
    //     0xe7add0: ubfx            x1, x1, #0xc, #0x14
    // 0xe7add4: mov             x16, x0
    // 0xe7add8: mov             x0, x1
    // 0xe7addc: mov             x1, x16
    // 0xe7ade0: r0 = GDT[cid_x0 + -0xf97]()
    //     0xe7ade0: sub             lr, x0, #0xf97
    //     0xe7ade4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ade8: blr             lr
    // 0xe7adec: ldur            x1, [fp, #-0x18]
    // 0xe7adf0: ArrayStore: r1[1] = r0  ; List_4
    //     0xe7adf0: add             x25, x1, #0x13
    //     0xe7adf4: str             w0, [x25]
    //     0xe7adf8: tbz             w0, #0, #0xe7ae14
    //     0xe7adfc: ldurb           w16, [x1, #-1]
    //     0xe7ae00: ldurb           w17, [x0, #-1]
    //     0xe7ae04: and             x16, x17, x16, lsr #2
    //     0xe7ae08: tst             x16, HEAP, lsr #32
    //     0xe7ae0c: b.eq            #0xe7ae14
    //     0xe7ae10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7ae14: ldur            x1, [fp, #-0x18]
    // 0xe7ae18: ldur            x0, [fp, #-0x10]
    // 0xe7ae1c: ArrayStore: r1[2] = r0  ; List_4
    //     0xe7ae1c: add             x25, x1, #0x17
    //     0xe7ae20: str             w0, [x25]
    //     0xe7ae24: tbz             w0, #0, #0xe7ae40
    //     0xe7ae28: ldurb           w16, [x1, #-1]
    //     0xe7ae2c: ldurb           w17, [x0, #-1]
    //     0xe7ae30: and             x16, x17, x16, lsr #2
    //     0xe7ae34: tst             x16, HEAP, lsr #32
    //     0xe7ae38: b.eq            #0xe7ae40
    //     0xe7ae3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7ae40: ldur            x16, [fp, #-0x18]
    // 0xe7ae44: str             x16, [SP]
    // 0xe7ae48: r0 = _interpolate()
    //     0xe7ae48: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7ae4c: mov             x1, x0
    // 0xe7ae50: r2 = "/"
    //     0xe7ae50: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe7ae54: r3 = "\\"
    //     0xe7ae54: ldr             x3, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0xe7ae58: r0 = replaceAll()
    //     0xe7ae58: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xe7ae5c: mov             x1, x0
    // 0xe7ae60: r0 = decodeComponent()
    //     0xe7ae60: bl              #0x6763cc  ; [dart:core] Uri::decodeComponent
    // 0xe7ae64: LeaveFrame
    //     0xe7ae64: mov             SP, fp
    //     0xe7ae68: ldp             fp, lr, [SP], #0x10
    // 0xe7ae6c: ret
    //     0xe7ae6c: ret             
    // 0xe7ae70: ldur            x0, [fp, #-8]
    // 0xe7ae74: r1 = Null
    //     0xe7ae74: mov             x1, NULL
    // 0xe7ae78: r2 = 6
    //     0xe7ae78: movz            x2, #0x6
    // 0xe7ae7c: r0 = AllocateArray()
    //     0xe7ae7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7ae80: r16 = "Uri "
    //     0xe7ae80: add             x16, PP, #0x20, lsl #12  ; [pp+0x20a90] "Uri "
    //     0xe7ae84: ldr             x16, [x16, #0xa90]
    // 0xe7ae88: StoreField: r0->field_f = r16
    //     0xe7ae88: stur            w16, [x0, #0xf]
    // 0xe7ae8c: ldur            x1, [fp, #-8]
    // 0xe7ae90: StoreField: r0->field_13 = r1
    //     0xe7ae90: stur            w1, [x0, #0x13]
    // 0xe7ae94: r16 = " must have scheme \'file:\'."
    //     0xe7ae94: add             x16, PP, #0x20, lsl #12  ; [pp+0x20a98] " must have scheme \'file:\'."
    //     0xe7ae98: ldr             x16, [x16, #0xa98]
    // 0xe7ae9c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe7ae9c: stur            w16, [x0, #0x17]
    // 0xe7aea0: str             x0, [SP]
    // 0xe7aea4: r0 = _interpolate()
    //     0xe7aea4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7aea8: stur            x0, [fp, #-8]
    // 0xe7aeac: r0 = ArgumentError()
    //     0xe7aeac: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe7aeb0: mov             x1, x0
    // 0xe7aeb4: ldur            x0, [fp, #-8]
    // 0xe7aeb8: ArrayStore: r1[0] = r0  ; List_4
    //     0xe7aeb8: stur            w0, [x1, #0x17]
    // 0xe7aebc: r0 = false
    //     0xe7aebc: add             x0, NULL, #0x30  ; false
    // 0xe7aec0: StoreField: r1->field_b = r0
    //     0xe7aec0: stur            w0, [x1, #0xb]
    // 0xe7aec4: mov             x0, x1
    // 0xe7aec8: r0 = Throw()
    //     0xe7aec8: bl              #0xec04b8  ; ThrowStub
    // 0xe7aecc: brk             #0
    // 0xe7aed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7aed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7aed4: b               #0xe7ac70
  }
  _ pathsEqual(/* No info */) {
    // ** addr: 0xe8b5b8, size: 0x148
    // 0xe8b5b8: EnterFrame
    //     0xe8b5b8: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b5bc: mov             fp, SP
    // 0xe8b5c0: cmp             w2, w3
    // 0xe8b5c4: b.ne            #0xe8b5d8
    // 0xe8b5c8: r0 = true
    //     0xe8b5c8: add             x0, NULL, #0x20  ; true
    // 0xe8b5cc: LeaveFrame
    //     0xe8b5cc: mov             SP, fp
    //     0xe8b5d0: ldp             fp, lr, [SP], #0x10
    // 0xe8b5d4: ret
    //     0xe8b5d4: ret             
    // 0xe8b5d8: LoadField: r4 = r2->field_7
    //     0xe8b5d8: ldur            w4, [x2, #7]
    // 0xe8b5dc: LoadField: r5 = r3->field_7
    //     0xe8b5dc: ldur            w5, [x3, #7]
    // 0xe8b5e0: r6 = LoadInt32Instr(r4)
    //     0xe8b5e0: sbfx            x6, x4, #1, #0x1f
    // 0xe8b5e4: r4 = LoadInt32Instr(r5)
    //     0xe8b5e4: sbfx            x4, x5, #1, #0x1f
    // 0xe8b5e8: cmp             x6, x4
    // 0xe8b5ec: b.eq            #0xe8b600
    // 0xe8b5f0: r0 = false
    //     0xe8b5f0: add             x0, NULL, #0x30  ; false
    // 0xe8b5f4: LeaveFrame
    //     0xe8b5f4: mov             SP, fp
    //     0xe8b5f8: ldp             fp, lr, [SP], #0x10
    // 0xe8b5fc: ret
    //     0xe8b5fc: ret             
    // 0xe8b600: r5 = LoadClassIdInstr(r2)
    //     0xe8b600: ldur            x5, [x2, #-1]
    //     0xe8b604: ubfx            x5, x5, #0xc, #0x14
    // 0xe8b608: lsl             x5, x5, #1
    // 0xe8b60c: r7 = LoadClassIdInstr(r3)
    //     0xe8b60c: ldur            x7, [x3, #-1]
    //     0xe8b610: ubfx            x7, x7, #0xc, #0x14
    // 0xe8b614: lsl             x7, x7, #1
    // 0xe8b618: r8 = 0
    //     0xe8b618: movz            x8, #0
    // 0xe8b61c: CheckStackOverflow
    //     0xe8b61c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b620: cmp             SP, x16
    //     0xe8b624: b.ls            #0xe8b6f4
    // 0xe8b628: cmp             x8, x6
    // 0xe8b62c: b.ge            #0xe8b6e4
    // 0xe8b630: cmp             w5, #0xbc
    // 0xe8b634: b.ne            #0xe8b644
    // 0xe8b638: ArrayLoad: r9 = r2[r8]  ; TypedUnsigned_1
    //     0xe8b638: add             x16, x2, x8
    //     0xe8b63c: ldrb            w9, [x16, #0xf]
    // 0xe8b640: b               #0xe8b64c
    // 0xe8b644: add             x16, x2, x8, lsl #1
    // 0xe8b648: ldurh           w9, [x16, #0xf]
    // 0xe8b64c: mov             x0, x4
    // 0xe8b650: mov             x1, x8
    // 0xe8b654: cmp             x1, x0
    // 0xe8b658: b.hs            #0xe8b6fc
    // 0xe8b65c: cmp             w7, #0xbc
    // 0xe8b660: b.ne            #0xe8b670
    // 0xe8b664: ArrayLoad: r1 = r3[r8]  ; TypedUnsigned_1
    //     0xe8b664: add             x16, x3, x8
    //     0xe8b668: ldrb            w1, [x16, #0xf]
    // 0xe8b66c: b               #0xe8b678
    // 0xe8b670: add             x16, x3, x8, lsl #1
    // 0xe8b674: ldurh           w1, [x16, #0xf]
    // 0xe8b678: cmp             x9, x1
    // 0xe8b67c: b.eq            #0xe8b6c8
    // 0xe8b680: cmp             x9, #0x2f
    // 0xe8b684: b.ne            #0xe8b694
    // 0xe8b688: cmp             x1, #0x5c
    // 0xe8b68c: b.eq            #0xe8b6c8
    // 0xe8b690: b               #0xe8b6d4
    // 0xe8b694: cmp             x9, #0x5c
    // 0xe8b698: b.ne            #0xe8b6a8
    // 0xe8b69c: cmp             x1, #0x2f
    // 0xe8b6a0: b.eq            #0xe8b6c8
    // 0xe8b6a4: b               #0xe8b6d4
    // 0xe8b6a8: eor             x10, x9, x1
    // 0xe8b6ac: cmp             x10, #0x20
    // 0xe8b6b0: b.ne            #0xe8b6d4
    // 0xe8b6b4: orr             x1, x9, #0x20
    // 0xe8b6b8: cmp             x1, #0x61
    // 0xe8b6bc: b.lt            #0xe8b6d4
    // 0xe8b6c0: cmp             x1, #0x7a
    // 0xe8b6c4: b.gt            #0xe8b6d4
    // 0xe8b6c8: add             x0, x8, #1
    // 0xe8b6cc: mov             x8, x0
    // 0xe8b6d0: b               #0xe8b61c
    // 0xe8b6d4: r0 = false
    //     0xe8b6d4: add             x0, NULL, #0x30  ; false
    // 0xe8b6d8: LeaveFrame
    //     0xe8b6d8: mov             SP, fp
    //     0xe8b6dc: ldp             fp, lr, [SP], #0x10
    // 0xe8b6e0: ret
    //     0xe8b6e0: ret             
    // 0xe8b6e4: r0 = true
    //     0xe8b6e4: add             x0, NULL, #0x20  ; true
    // 0xe8b6e8: LeaveFrame
    //     0xe8b6e8: mov             SP, fp
    //     0xe8b6ec: ldp             fp, lr, [SP], #0x10
    // 0xe8b6f0: ret
    //     0xe8b6f0: ret             
    // 0xe8b6f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b6f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b6f8: b               #0xe8b628
    // 0xe8b6fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8b6fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ needsSeparator(/* No info */) {
    // ** addr: 0xe8d440, size: 0x84
    // 0xe8d440: LoadField: r3 = r2->field_7
    //     0xe8d440: ldur            w3, [x2, #7]
    // 0xe8d444: cbnz            w3, #0xe8d450
    // 0xe8d448: r0 = false
    //     0xe8d448: add             x0, NULL, #0x30  ; false
    // 0xe8d44c: ret
    //     0xe8d44c: ret             
    // 0xe8d450: r0 = LoadInt32Instr(r3)
    //     0xe8d450: sbfx            x0, x3, #1, #0x1f
    // 0xe8d454: sub             x3, x0, #1
    // 0xe8d458: mov             x1, x3
    // 0xe8d45c: cmp             x1, x0
    // 0xe8d460: b.hs            #0xe8d4b8
    // 0xe8d464: r1 = LoadClassIdInstr(r2)
    //     0xe8d464: ldur            x1, [x2, #-1]
    //     0xe8d468: ubfx            x1, x1, #0xc, #0x14
    // 0xe8d46c: lsl             x1, x1, #1
    // 0xe8d470: cmp             w1, #0xbc
    // 0xe8d474: b.ne            #0xe8d484
    // 0xe8d478: ArrayLoad: r1 = r2[r3]  ; TypedUnsigned_1
    //     0xe8d478: add             x16, x2, x3
    //     0xe8d47c: ldrb            w1, [x16, #0xf]
    // 0xe8d480: b               #0xe8d48c
    // 0xe8d484: add             x16, x2, x3, lsl #1
    // 0xe8d488: ldurh           w1, [x16, #0xf]
    // 0xe8d48c: cmp             x1, #0x2f
    // 0xe8d490: b.ne            #0xe8d49c
    // 0xe8d494: r1 = true
    //     0xe8d494: add             x1, NULL, #0x20  ; true
    // 0xe8d498: b               #0xe8d4b0
    // 0xe8d49c: cmp             x1, #0x5c
    // 0xe8d4a0: r16 = true
    //     0xe8d4a0: add             x16, NULL, #0x20  ; true
    // 0xe8d4a4: r17 = false
    //     0xe8d4a4: add             x17, NULL, #0x30  ; false
    // 0xe8d4a8: csel            x2, x16, x17, eq
    // 0xe8d4ac: mov             x1, x2
    // 0xe8d4b0: eor             x0, x1, #0x10
    // 0xe8d4b4: ret
    //     0xe8d4b4: ret             
    // 0xe8d4b8: EnterFrame
    //     0xe8d4b8: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d4bc: mov             fp, SP
    // 0xe8d4c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8d4c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ rootLength(/* No info */) {
    // ** addr: 0xe8d7a4, size: 0x2a8
    // 0xe8d7a4: EnterFrame
    //     0xe8d7a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d7a8: mov             fp, SP
    // 0xe8d7ac: AllocStack(0x18)
    //     0xe8d7ac: sub             SP, SP, #0x18
    // 0xe8d7b0: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe8d7b0: mov             x3, x2
    //     0xe8d7b4: stur            x2, [fp, #-0x10]
    // 0xe8d7b8: CheckStackOverflow
    //     0xe8d7b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8d7bc: cmp             SP, x16
    //     0xe8d7c0: b.ls            #0xe8da34
    // 0xe8d7c4: LoadField: r0 = r3->field_7
    //     0xe8d7c4: ldur            w0, [x3, #7]
    // 0xe8d7c8: cbnz            w0, #0xe8d7dc
    // 0xe8d7cc: r0 = 0
    //     0xe8d7cc: movz            x0, #0
    // 0xe8d7d0: LeaveFrame
    //     0xe8d7d0: mov             SP, fp
    //     0xe8d7d4: ldp             fp, lr, [SP], #0x10
    // 0xe8d7d8: ret
    //     0xe8d7d8: ret             
    // 0xe8d7dc: r4 = LoadInt32Instr(r0)
    //     0xe8d7dc: sbfx            x4, x0, #1, #0x1f
    // 0xe8d7e0: mov             x0, x4
    // 0xe8d7e4: stur            x4, [fp, #-8]
    // 0xe8d7e8: r1 = 0
    //     0xe8d7e8: movz            x1, #0
    // 0xe8d7ec: cmp             x1, x0
    // 0xe8d7f0: b.hs            #0xe8da3c
    // 0xe8d7f4: r2 = LoadClassIdInstr(r3)
    //     0xe8d7f4: ldur            x2, [x3, #-1]
    //     0xe8d7f8: ubfx            x2, x2, #0xc, #0x14
    // 0xe8d7fc: lsl             x2, x2, #1
    // 0xe8d800: cmp             w2, #0xbc
    // 0xe8d804: b.ne            #0xe8d818
    // 0xe8d808: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0xe8d808: ldrb            w0, [x3, #0xf]
    // 0xe8d80c: cmp             x0, #0x2f
    // 0xe8d810: b.ne            #0xe8d834
    // 0xe8d814: b               #0xe8d824
    // 0xe8d818: ldurh           w0, [x3, #0xf]
    // 0xe8d81c: cmp             x0, #0x2f
    // 0xe8d820: b.ne            #0xe8d834
    // 0xe8d824: r0 = 1
    //     0xe8d824: movz            x0, #0x1
    // 0xe8d828: LeaveFrame
    //     0xe8d828: mov             SP, fp
    //     0xe8d82c: ldp             fp, lr, [SP], #0x10
    // 0xe8d830: ret
    //     0xe8d830: ret             
    // 0xe8d834: cmp             w2, #0xbc
    // 0xe8d838: b.ne            #0xe8d84c
    // 0xe8d83c: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0xe8d83c: ldrb            w0, [x3, #0xf]
    // 0xe8d840: cmp             x0, #0x5c
    // 0xe8d844: b.ne            #0xe8d940
    // 0xe8d848: b               #0xe8d858
    // 0xe8d84c: ldurh           w0, [x3, #0xf]
    // 0xe8d850: cmp             x0, #0x5c
    // 0xe8d854: b.ne            #0xe8d93c
    // 0xe8d858: cmp             x4, #2
    // 0xe8d85c: b.lt            #0xe8d894
    // 0xe8d860: mov             x0, x4
    // 0xe8d864: r1 = 1
    //     0xe8d864: movz            x1, #0x1
    // 0xe8d868: cmp             x1, x0
    // 0xe8d86c: b.hs            #0xe8da40
    // 0xe8d870: cmp             w2, #0xbc
    // 0xe8d874: b.ne            #0xe8d888
    // 0xe8d878: ArrayLoad: r0 = r3[-7]  ; TypedUnsigned_1
    //     0xe8d878: ldrb            w0, [x3, #0x10]
    // 0xe8d87c: cmp             x0, #0x5c
    // 0xe8d880: b.eq            #0xe8d8a4
    // 0xe8d884: b               #0xe8d894
    // 0xe8d888: ldurh           w0, [x3, #0x11]
    // 0xe8d88c: cmp             x0, #0x5c
    // 0xe8d890: b.eq            #0xe8d8a4
    // 0xe8d894: r0 = 1
    //     0xe8d894: movz            x0, #0x1
    // 0xe8d898: LeaveFrame
    //     0xe8d898: mov             SP, fp
    //     0xe8d89c: ldp             fp, lr, [SP], #0x10
    // 0xe8d8a0: ret
    //     0xe8d8a0: ret             
    // 0xe8d8a4: r0 = LoadClassIdInstr(r3)
    //     0xe8d8a4: ldur            x0, [x3, #-1]
    //     0xe8d8a8: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d8ac: r16 = 4
    //     0xe8d8ac: movz            x16, #0x4
    // 0xe8d8b0: str             x16, [SP]
    // 0xe8d8b4: mov             x1, x3
    // 0xe8d8b8: r2 = "\\"
    //     0xe8d8b8: ldr             x2, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0xe8d8bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe8d8bc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe8d8c0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe8d8c0: sub             lr, x0, #0xffa
    //     0xe8d8c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe8d8c8: blr             lr
    // 0xe8d8cc: cmp             x0, #0
    // 0xe8d8d0: b.le            #0xe8d92c
    // 0xe8d8d4: ldur            x3, [fp, #-0x10]
    // 0xe8d8d8: add             x2, x0, #1
    // 0xe8d8dc: r0 = BoxInt64Instr(r2)
    //     0xe8d8dc: sbfiz           x0, x2, #1, #0x1f
    //     0xe8d8e0: cmp             x2, x0, asr #1
    //     0xe8d8e4: b.eq            #0xe8d8f0
    //     0xe8d8e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8d8ec: stur            x2, [x0, #7]
    // 0xe8d8f0: r1 = LoadClassIdInstr(r3)
    //     0xe8d8f0: ldur            x1, [x3, #-1]
    //     0xe8d8f4: ubfx            x1, x1, #0xc, #0x14
    // 0xe8d8f8: str             x0, [SP]
    // 0xe8d8fc: mov             x0, x1
    // 0xe8d900: mov             x1, x3
    // 0xe8d904: r2 = "\\"
    //     0xe8d904: ldr             x2, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0xe8d908: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe8d908: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe8d90c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe8d90c: sub             lr, x0, #0xffa
    //     0xe8d910: ldr             lr, [x21, lr, lsl #3]
    //     0xe8d914: blr             lr
    // 0xe8d918: cmp             x0, #0
    // 0xe8d91c: b.le            #0xe8d92c
    // 0xe8d920: LeaveFrame
    //     0xe8d920: mov             SP, fp
    //     0xe8d924: ldp             fp, lr, [SP], #0x10
    // 0xe8d928: ret
    //     0xe8d928: ret             
    // 0xe8d92c: ldur            x0, [fp, #-8]
    // 0xe8d930: LeaveFrame
    //     0xe8d930: mov             SP, fp
    //     0xe8d934: ldp             fp, lr, [SP], #0x10
    // 0xe8d938: ret
    //     0xe8d938: ret             
    // 0xe8d93c: ldur            x4, [fp, #-8]
    // 0xe8d940: cmp             x4, #3
    // 0xe8d944: b.ge            #0xe8d958
    // 0xe8d948: r0 = 0
    //     0xe8d948: movz            x0, #0
    // 0xe8d94c: LeaveFrame
    //     0xe8d94c: mov             SP, fp
    //     0xe8d950: ldp             fp, lr, [SP], #0x10
    // 0xe8d954: ret
    //     0xe8d954: ret             
    // 0xe8d958: cmp             w2, #0xbc
    // 0xe8d95c: b.ne            #0xe8d968
    // 0xe8d960: ArrayLoad: r5 = r3[-8]  ; TypedUnsigned_1
    //     0xe8d960: ldrb            w5, [x3, #0xf]
    // 0xe8d964: b               #0xe8d96c
    // 0xe8d968: ldurh           w5, [x3, #0xf]
    // 0xe8d96c: cmp             x5, #0x41
    // 0xe8d970: b.lt            #0xe8d97c
    // 0xe8d974: cmp             x5, #0x5a
    // 0xe8d978: b.le            #0xe8d98c
    // 0xe8d97c: cmp             x5, #0x61
    // 0xe8d980: b.lt            #0xe8da24
    // 0xe8d984: cmp             x5, #0x7a
    // 0xe8d988: b.gt            #0xe8da24
    // 0xe8d98c: mov             x0, x4
    // 0xe8d990: r1 = 1
    //     0xe8d990: movz            x1, #0x1
    // 0xe8d994: cmp             x1, x0
    // 0xe8d998: b.hs            #0xe8da44
    // 0xe8d99c: cmp             w2, #0xbc
    // 0xe8d9a0: b.ne            #0xe8d9b4
    // 0xe8d9a4: ArrayLoad: r5 = r3[-7]  ; TypedUnsigned_1
    //     0xe8d9a4: ldrb            w5, [x3, #0x10]
    // 0xe8d9a8: cmp             x5, #0x3a
    // 0xe8d9ac: b.eq            #0xe8d9d0
    // 0xe8d9b0: b               #0xe8d9c0
    // 0xe8d9b4: ldurh           w5, [x3, #0x11]
    // 0xe8d9b8: cmp             x5, #0x3a
    // 0xe8d9bc: b.eq            #0xe8d9d0
    // 0xe8d9c0: r0 = 0
    //     0xe8d9c0: movz            x0, #0
    // 0xe8d9c4: LeaveFrame
    //     0xe8d9c4: mov             SP, fp
    //     0xe8d9c8: ldp             fp, lr, [SP], #0x10
    // 0xe8d9cc: ret
    //     0xe8d9cc: ret             
    // 0xe8d9d0: mov             x0, x4
    // 0xe8d9d4: r1 = 2
    //     0xe8d9d4: movz            x1, #0x2
    // 0xe8d9d8: cmp             x1, x0
    // 0xe8d9dc: b.hs            #0xe8da48
    // 0xe8d9e0: cmp             w2, #0xbc
    // 0xe8d9e4: b.ne            #0xe8d9f0
    // 0xe8d9e8: ArrayLoad: r1 = r3[-6]  ; TypedUnsigned_1
    //     0xe8d9e8: ldrb            w1, [x3, #0x11]
    // 0xe8d9ec: b               #0xe8d9f4
    // 0xe8d9f0: ldurh           w1, [x3, #0x13]
    // 0xe8d9f4: cmp             x1, #0x2f
    // 0xe8d9f8: b.eq            #0xe8da14
    // 0xe8d9fc: cmp             x1, #0x5c
    // 0xe8da00: b.eq            #0xe8da14
    // 0xe8da04: r0 = 0
    //     0xe8da04: movz            x0, #0
    // 0xe8da08: LeaveFrame
    //     0xe8da08: mov             SP, fp
    //     0xe8da0c: ldp             fp, lr, [SP], #0x10
    // 0xe8da10: ret
    //     0xe8da10: ret             
    // 0xe8da14: r0 = 3
    //     0xe8da14: movz            x0, #0x3
    // 0xe8da18: LeaveFrame
    //     0xe8da18: mov             SP, fp
    //     0xe8da1c: ldp             fp, lr, [SP], #0x10
    // 0xe8da20: ret
    //     0xe8da20: ret             
    // 0xe8da24: r0 = 0
    //     0xe8da24: movz            x0, #0
    // 0xe8da28: LeaveFrame
    //     0xe8da28: mov             SP, fp
    //     0xe8da2c: ldp             fp, lr, [SP], #0x10
    // 0xe8da30: ret
    //     0xe8da30: ret             
    // 0xe8da34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8da34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8da38: b               #0xe8d7c4
    // 0xe8da3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8da3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8da40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8da40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8da44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8da44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8da48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8da48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
