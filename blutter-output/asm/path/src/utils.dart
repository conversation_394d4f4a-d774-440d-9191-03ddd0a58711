// lib: , url: package:path/src/utils.dart

// class id: 1050761, size: 0x8
class :: {

  static _ isDriveLetter(/* No info */) {
    // ** addr: 0xe7aed8, size: 0x44
    // 0xe7aed8: EnterFrame
    //     0xe7aed8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7aedc: mov             fp, SP
    // 0xe7aee0: CheckStackOverflow
    //     0xe7aee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7aee4: cmp             SP, x16
    //     0xe7aee8: b.ls            #0xe7af14
    // 0xe7aeec: r2 = 1
    //     0xe7aeec: movz            x2, #0x1
    // 0xe7aef0: r0 = driveLetterEnd()
    //     0xe7aef0: bl              #0xe7af1c  ; [package:path/src/utils.dart] ::driveLetterEnd
    // 0xe7aef4: cmp             w0, NULL
    // 0xe7aef8: r16 = true
    //     0xe7aef8: add             x16, NULL, #0x20  ; true
    // 0xe7aefc: r17 = false
    //     0xe7aefc: add             x17, NULL, #0x30  ; false
    // 0xe7af00: csel            x1, x16, x17, ne
    // 0xe7af04: mov             x0, x1
    // 0xe7af08: LeaveFrame
    //     0xe7af08: mov             SP, fp
    //     0xe7af0c: ldp             fp, lr, [SP], #0x10
    // 0xe7af10: ret
    //     0xe7af10: ret             
    // 0xe7af14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7af14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7af18: b               #0xe7aeec
  }
  static _ driveLetterEnd(/* No info */) {
    // ** addr: 0xe7af1c, size: 0x240
    // 0xe7af1c: EnterFrame
    //     0xe7af1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7af20: mov             fp, SP
    // 0xe7af24: AllocStack(0x30)
    //     0xe7af24: sub             SP, SP, #0x30
    // 0xe7af28: SetupParameters(dynamic _ /* r1 => r3, fp-0x20 */)
    //     0xe7af28: mov             x3, x1
    //     0xe7af2c: stur            x1, [fp, #-0x20]
    // 0xe7af30: CheckStackOverflow
    //     0xe7af30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7af34: cmp             SP, x16
    //     0xe7af38: b.ls            #0xe7b148
    // 0xe7af3c: LoadField: r0 = r3->field_7
    //     0xe7af3c: ldur            w0, [x3, #7]
    // 0xe7af40: add             x4, x2, #2
    // 0xe7af44: stur            x4, [fp, #-0x18]
    // 0xe7af48: r5 = LoadInt32Instr(r0)
    //     0xe7af48: sbfx            x5, x0, #1, #0x1f
    // 0xe7af4c: stur            x5, [fp, #-0x10]
    // 0xe7af50: cmp             x5, x4
    // 0xe7af54: b.ge            #0xe7af68
    // 0xe7af58: r0 = Null
    //     0xe7af58: mov             x0, NULL
    // 0xe7af5c: LeaveFrame
    //     0xe7af5c: mov             SP, fp
    //     0xe7af60: ldp             fp, lr, [SP], #0x10
    // 0xe7af64: ret
    //     0xe7af64: ret             
    // 0xe7af68: mov             x0, x5
    // 0xe7af6c: mov             x1, x2
    // 0xe7af70: cmp             x1, x0
    // 0xe7af74: b.hs            #0xe7b150
    // 0xe7af78: r6 = LoadClassIdInstr(r3)
    //     0xe7af78: ldur            x6, [x3, #-1]
    //     0xe7af7c: ubfx            x6, x6, #0xc, #0x14
    // 0xe7af80: lsl             x6, x6, #1
    // 0xe7af84: stur            x6, [fp, #-8]
    // 0xe7af88: cmp             w6, #0xbc
    // 0xe7af8c: b.ne            #0xe7af9c
    // 0xe7af90: ArrayLoad: r0 = r3[r2]  ; TypedUnsigned_1
    //     0xe7af90: add             x16, x3, x2
    //     0xe7af94: ldrb            w0, [x16, #0xf]
    // 0xe7af98: b               #0xe7afa4
    // 0xe7af9c: add             x16, x3, x2, lsl #1
    // 0xe7afa0: ldurh           w0, [x16, #0xf]
    // 0xe7afa4: cmp             x0, #0x41
    // 0xe7afa8: b.lt            #0xe7afb4
    // 0xe7afac: cmp             x0, #0x5a
    // 0xe7afb0: b.le            #0xe7afc4
    // 0xe7afb4: cmp             x0, #0x61
    // 0xe7afb8: b.lt            #0xe7b138
    // 0xe7afbc: cmp             x0, #0x7a
    // 0xe7afc0: b.gt            #0xe7b138
    // 0xe7afc4: add             x7, x2, #1
    // 0xe7afc8: mov             x0, x5
    // 0xe7afcc: mov             x1, x7
    // 0xe7afd0: cmp             x1, x0
    // 0xe7afd4: b.hs            #0xe7b154
    // 0xe7afd8: cmp             w6, #0xbc
    // 0xe7afdc: b.ne            #0xe7aff4
    // 0xe7afe0: ArrayLoad: r0 = r3[r7]  ; TypedUnsigned_1
    //     0xe7afe0: add             x16, x3, x7
    //     0xe7afe4: ldrb            w0, [x16, #0xf]
    // 0xe7afe8: cmp             x0, #0x3a
    // 0xe7afec: b.eq            #0xe7b090
    // 0xe7aff0: b               #0xe7b004
    // 0xe7aff4: add             x16, x3, x7, lsl #1
    // 0xe7aff8: ldurh           w0, [x16, #0xf]
    // 0xe7affc: cmp             x0, #0x3a
    // 0xe7b000: b.eq            #0xe7b090
    // 0xe7b004: add             x0, x2, #4
    // 0xe7b008: cmp             x5, x0
    // 0xe7b00c: b.ge            #0xe7b020
    // 0xe7b010: r0 = Null
    //     0xe7b010: mov             x0, NULL
    // 0xe7b014: LeaveFrame
    //     0xe7b014: mov             SP, fp
    //     0xe7b018: ldp             fp, lr, [SP], #0x10
    // 0xe7b01c: ret
    //     0xe7b01c: ret             
    // 0xe7b020: lsl             x1, x0, #1
    // 0xe7b024: str             x1, [SP]
    // 0xe7b028: mov             x1, x3
    // 0xe7b02c: mov             x2, x7
    // 0xe7b030: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe7b030: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe7b034: r0 = substring()
    //     0xe7b034: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe7b038: r1 = LoadClassIdInstr(r0)
    //     0xe7b038: ldur            x1, [x0, #-1]
    //     0xe7b03c: ubfx            x1, x1, #0xc, #0x14
    // 0xe7b040: str             x0, [SP]
    // 0xe7b044: mov             x0, x1
    // 0xe7b048: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe7b048: sub             lr, x0, #0xffe
    //     0xe7b04c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b050: blr             lr
    // 0xe7b054: r1 = LoadClassIdInstr(r0)
    //     0xe7b054: ldur            x1, [x0, #-1]
    //     0xe7b058: ubfx            x1, x1, #0xc, #0x14
    // 0xe7b05c: r16 = "%3a"
    //     0xe7b05c: add             x16, PP, #0x12, lsl #12  ; [pp+0x127c8] "%3a"
    //     0xe7b060: ldr             x16, [x16, #0x7c8]
    // 0xe7b064: stp             x16, x0, [SP]
    // 0xe7b068: mov             x0, x1
    // 0xe7b06c: mov             lr, x0
    // 0xe7b070: ldr             lr, [x21, lr, lsl #3]
    // 0xe7b074: blr             lr
    // 0xe7b078: tbz             w0, #4, #0xe7b08c
    // 0xe7b07c: r0 = Null
    //     0xe7b07c: mov             x0, NULL
    // 0xe7b080: LeaveFrame
    //     0xe7b080: mov             SP, fp
    //     0xe7b084: ldp             fp, lr, [SP], #0x10
    // 0xe7b088: ret
    //     0xe7b088: ret             
    // 0xe7b08c: ldur            x2, [fp, #-0x18]
    // 0xe7b090: ldur            x0, [fp, #-0x10]
    // 0xe7b094: add             x3, x2, #2
    // 0xe7b098: cmp             x0, x3
    // 0xe7b09c: b.ne            #0xe7b0c0
    // 0xe7b0a0: r0 = BoxInt64Instr(r3)
    //     0xe7b0a0: sbfiz           x0, x3, #1, #0x1f
    //     0xe7b0a4: cmp             x3, x0, asr #1
    //     0xe7b0a8: b.eq            #0xe7b0b4
    //     0xe7b0ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7b0b0: stur            x3, [x0, #7]
    // 0xe7b0b4: LeaveFrame
    //     0xe7b0b4: mov             SP, fp
    //     0xe7b0b8: ldp             fp, lr, [SP], #0x10
    // 0xe7b0bc: ret
    //     0xe7b0bc: ret             
    // 0xe7b0c0: ldur            x4, [fp, #-8]
    // 0xe7b0c4: mov             x1, x3
    // 0xe7b0c8: cmp             x1, x0
    // 0xe7b0cc: b.hs            #0xe7b158
    // 0xe7b0d0: cmp             w4, #0xbc
    // 0xe7b0d4: b.ne            #0xe7b0f0
    // 0xe7b0d8: ldur            x4, [fp, #-0x20]
    // 0xe7b0dc: ArrayLoad: r5 = r4[r3]  ; TypedUnsigned_1
    //     0xe7b0dc: add             x16, x4, x3
    //     0xe7b0e0: ldrb            w5, [x16, #0xf]
    // 0xe7b0e4: cmp             x5, #0x2f
    // 0xe7b0e8: b.eq            #0xe7b114
    // 0xe7b0ec: b               #0xe7b104
    // 0xe7b0f0: ldur            x4, [fp, #-0x20]
    // 0xe7b0f4: add             x16, x4, x3, lsl #1
    // 0xe7b0f8: ldurh           w5, [x16, #0xf]
    // 0xe7b0fc: cmp             x5, #0x2f
    // 0xe7b100: b.eq            #0xe7b114
    // 0xe7b104: r0 = Null
    //     0xe7b104: mov             x0, NULL
    // 0xe7b108: LeaveFrame
    //     0xe7b108: mov             SP, fp
    //     0xe7b10c: ldp             fp, lr, [SP], #0x10
    // 0xe7b110: ret
    //     0xe7b110: ret             
    // 0xe7b114: add             x3, x2, #3
    // 0xe7b118: r0 = BoxInt64Instr(r3)
    //     0xe7b118: sbfiz           x0, x3, #1, #0x1f
    //     0xe7b11c: cmp             x3, x0, asr #1
    //     0xe7b120: b.eq            #0xe7b12c
    //     0xe7b124: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7b128: stur            x3, [x0, #7]
    // 0xe7b12c: LeaveFrame
    //     0xe7b12c: mov             SP, fp
    //     0xe7b130: ldp             fp, lr, [SP], #0x10
    // 0xe7b134: ret
    //     0xe7b134: ret             
    // 0xe7b138: r0 = Null
    //     0xe7b138: mov             x0, NULL
    // 0xe7b13c: LeaveFrame
    //     0xe7b13c: mov             SP, fp
    //     0xe7b140: ldp             fp, lr, [SP], #0x10
    // 0xe7b144: ret
    //     0xe7b144: ret             
    // 0xe7b148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7b148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7b14c: b               #0xe7af3c
    // 0xe7b150: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7b150: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7b154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7b154: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7b158: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7b158: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
