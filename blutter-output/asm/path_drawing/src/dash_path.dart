// lib: , url: package:path_drawing/src/dash_path.dart

// class id: 1050762, size: 0x8
class :: {

  static _ dashPath(/* No info */) {
    // ** addr: 0xe3d494, size: 0x3d4
    // 0xe3d494: EnterFrame
    //     0xe3d494: stp             fp, lr, [SP, #-0x10]!
    //     0xe3d498: mov             fp, SP
    // 0xe3d49c: AllocStack(0xa8)
    //     0xe3d49c: sub             SP, SP, #0xa8
    // 0xe3d4a0: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe3d4a0: stur            x1, [fp, #-0x10]
    //     0xe3d4a4: stur            x2, [fp, #-0x18]
    // 0xe3d4a8: CheckStackOverflow
    //     0xe3d4a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3d4ac: cmp             SP, x16
    //     0xe3d4b0: b.ls            #0xe3d844
    // 0xe3d4b4: cmp             w3, NULL
    // 0xe3d4b8: b.ne            #0xe3d4c8
    // 0xe3d4bc: r0 = Instance_DashOffset
    //     0xe3d4bc: add             x0, PP, #0x39, lsl #12  ; [pp+0x391f0] Obj!DashOffset@e0e411
    //     0xe3d4c0: ldr             x0, [x0, #0x1f0]
    // 0xe3d4c4: b               #0xe3d4cc
    // 0xe3d4c8: mov             x0, x3
    // 0xe3d4cc: stur            x0, [fp, #-8]
    // 0xe3d4d0: r0 = _NativePath()
    //     0xe3d4d0: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0xe3d4d4: mov             x1, x0
    // 0xe3d4d8: stur            x0, [fp, #-0x20]
    // 0xe3d4dc: r0 = __constructor$Method$FfiNative()
    //     0xe3d4dc: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0xe3d4e0: ldur            x1, [fp, #-0x10]
    // 0xe3d4e4: r0 = computeMetrics()
    //     0xe3d4e4: bl              #0xe3da44  ; [dart:ui] _NativePath::computeMetrics
    // 0xe3d4e8: mov             x1, x0
    // 0xe3d4ec: r0 = name()
    //     0xe3d4ec: bl              #0xebac00  ; [package:xml/src/xml/nodes/attribute.dart] XmlAttribute::name
    // 0xe3d4f0: stur            x0, [fp, #-0x30]
    // 0xe3d4f4: LoadField: r2 = r0->field_b
    //     0xe3d4f4: ldur            w2, [x0, #0xb]
    // 0xe3d4f8: DecompressPointer r2
    //     0xe3d4f8: add             x2, x2, HEAP, lsl #32
    // 0xe3d4fc: ldur            x1, [fp, #-8]
    // 0xe3d500: stur            x2, [fp, #-0x28]
    // 0xe3d504: LoadField: r3 = r1->field_f
    //     0xe3d504: ldur            w3, [x1, #0xf]
    // 0xe3d508: DecompressPointer r3
    //     0xe3d508: add             x3, x3, HEAP, lsl #32
    // 0xe3d50c: stur            x3, [fp, #-0x10]
    // 0xe3d510: LoadField: d0 = r1->field_7
    //     0xe3d510: ldur            d0, [x1, #7]
    // 0xe3d514: ldur            x4, [fp, #-0x18]
    // 0xe3d518: stur            d0, [fp, #-0x80]
    // 0xe3d51c: LoadField: r5 = r4->field_b
    //     0xe3d51c: ldur            w5, [x4, #0xb]
    // 0xe3d520: DecompressPointer r5
    //     0xe3d520: add             x5, x5, HEAP, lsl #32
    // 0xe3d524: stur            x5, [fp, #-8]
    // 0xe3d528: r1 = Instance_Offset
    //     0xe3d528: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xe3d52c: LoadField: d1 = r1->field_7
    //     0xe3d52c: ldur            d1, [x1, #7]
    // 0xe3d530: stur            d1, [fp, #-0x78]
    // 0xe3d534: LoadField: d2 = r1->field_f
    //     0xe3d534: ldur            d2, [x1, #0xf]
    // 0xe3d538: stur            d2, [fp, #-0x70]
    // 0xe3d53c: ldur            x6, [fp, #-0x20]
    // 0xe3d540: CheckStackOverflow
    //     0xe3d540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3d544: cmp             SP, x16
    //     0xe3d548: b.ls            #0xe3d84c
    // 0xe3d54c: mov             x1, x2
    // 0xe3d550: r0 = _nextContour()
    //     0xe3d550: bl              #0x68be4c  ; [dart:ui] _PathMeasure::_nextContour
    // 0xe3d554: tbnz            w0, #4, #0xe3d82c
    // 0xe3d558: ldur            x0, [fp, #-0x30]
    // 0xe3d55c: ldur            x1, [fp, #-0x10]
    // 0xe3d560: r0 = PathMetric()
    //     0xe3d560: bl              #0x68be40  ; AllocatePathMetricStub -> PathMetric (size=0x1c)
    // 0xe3d564: mov             x1, x0
    // 0xe3d568: ldur            x2, [fp, #-0x28]
    // 0xe3d56c: stur            x0, [fp, #-0x38]
    // 0xe3d570: r0 = PathMetric._()
    //     0xe3d570: bl              #0x68bb58  ; [dart:ui] PathMetric::PathMetric._
    // 0xe3d574: ldur            x0, [fp, #-0x38]
    // 0xe3d578: ldur            x1, [fp, #-0x30]
    // 0xe3d57c: StoreField: r1->field_7 = r0
    //     0xe3d57c: stur            w0, [x1, #7]
    //     0xe3d580: ldurb           w16, [x1, #-1]
    //     0xe3d584: ldurb           w17, [x0, #-1]
    //     0xe3d588: and             x16, x17, x16, lsr #2
    //     0xe3d58c: tst             x16, HEAP, lsr #32
    //     0xe3d590: b.eq            #0xe3d598
    //     0xe3d594: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe3d598: ldur            x0, [fp, #-0x38]
    // 0xe3d59c: LoadField: d0 = r0->field_7
    //     0xe3d59c: ldur            d0, [x0, #7]
    // 0xe3d5a0: ldur            x2, [fp, #-0x10]
    // 0xe3d5a4: stur            d0, [fp, #-0x90]
    // 0xe3d5a8: r16 = Instance__DashOffsetType
    //     0xe3d5a8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e20] Obj!_DashOffsetType@e2ffe1
    //     0xe3d5ac: ldr             x16, [x16, #0xe20]
    // 0xe3d5b0: cmp             w2, w16
    // 0xe3d5b4: b.ne            #0xe3d5c4
    // 0xe3d5b8: ldur            d2, [fp, #-0x80]
    // 0xe3d5bc: ldur            d1, [fp, #-0x80]
    // 0xe3d5c0: b               #0xe3d5cc
    // 0xe3d5c4: ldur            d1, [fp, #-0x80]
    // 0xe3d5c8: fmul            d2, d0, d1
    // 0xe3d5cc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xe3d5cc: ldur            w3, [x0, #0x17]
    // 0xe3d5d0: DecompressPointer r3
    //     0xe3d5d0: add             x3, x3, HEAP, lsl #32
    // 0xe3d5d4: stur            x3, [fp, #-0x50]
    // 0xe3d5d8: LoadField: r4 = r0->field_f
    //     0xe3d5d8: ldur            x4, [x0, #0xf]
    // 0xe3d5dc: stur            x4, [fp, #-0x48]
    // 0xe3d5e0: r8 = true
    //     0xe3d5e0: add             x8, NULL, #0x20  ; true
    // 0xe3d5e4: ldur            x5, [fp, #-0x18]
    // 0xe3d5e8: ldur            x7, [fp, #-0x20]
    // 0xe3d5ec: ldur            x6, [fp, #-8]
    // 0xe3d5f0: stur            x8, [fp, #-0x38]
    // 0xe3d5f4: stur            d2, [fp, #-0x88]
    // 0xe3d5f8: CheckStackOverflow
    //     0xe3d5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3d5fc: cmp             SP, x16
    //     0xe3d600: b.ls            #0xe3d854
    // 0xe3d604: fcmp            d0, d2
    // 0xe3d608: b.le            #0xe3d808
    // 0xe3d60c: LoadField: r9 = r5->field_f
    //     0xe3d60c: ldur            x9, [x5, #0xf]
    // 0xe3d610: stur            x9, [fp, #-0x40]
    // 0xe3d614: r0 = LoadClassIdInstr(r6)
    //     0xe3d614: ldur            x0, [x6, #-1]
    //     0xe3d618: ubfx            x0, x0, #0xc, #0x14
    // 0xe3d61c: str             x6, [SP]
    // 0xe3d620: r0 = GDT[cid_x0 + 0xc834]()
    //     0xe3d620: movz            x17, #0xc834
    //     0xe3d624: add             lr, x0, x17
    //     0xe3d628: ldr             lr, [x21, lr, lsl #3]
    //     0xe3d62c: blr             lr
    // 0xe3d630: r1 = LoadInt32Instr(r0)
    //     0xe3d630: sbfx            x1, x0, #1, #0x1f
    // 0xe3d634: ldur            x0, [fp, #-0x40]
    // 0xe3d638: cmp             x0, x1
    // 0xe3d63c: b.lt            #0xe3d64c
    // 0xe3d640: ldur            x2, [fp, #-0x18]
    // 0xe3d644: StoreField: r2->field_f = rZR
    //     0xe3d644: stur            xzr, [x2, #0xf]
    // 0xe3d648: b               #0xe3d650
    // 0xe3d64c: ldur            x2, [fp, #-0x18]
    // 0xe3d650: ldur            x4, [fp, #-0x38]
    // 0xe3d654: ldur            x3, [fp, #-8]
    // 0xe3d658: LoadField: r5 = r2->field_f
    //     0xe3d658: ldur            x5, [x2, #0xf]
    // 0xe3d65c: add             x0, x5, #1
    // 0xe3d660: StoreField: r2->field_f = r0
    //     0xe3d660: stur            x0, [x2, #0xf]
    // 0xe3d664: r0 = BoxInt64Instr(r5)
    //     0xe3d664: sbfiz           x0, x5, #1, #0x1f
    //     0xe3d668: cmp             x5, x0, asr #1
    //     0xe3d66c: b.eq            #0xe3d678
    //     0xe3d670: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe3d674: stur            x5, [x0, #7]
    // 0xe3d678: r1 = LoadClassIdInstr(r3)
    //     0xe3d678: ldur            x1, [x3, #-1]
    //     0xe3d67c: ubfx            x1, x1, #0xc, #0x14
    // 0xe3d680: stp             x0, x3, [SP]
    // 0xe3d684: mov             x0, x1
    // 0xe3d688: r0 = GDT[cid_x0 + 0x13037]()
    //     0xe3d688: movz            x17, #0x3037
    //     0xe3d68c: movk            x17, #0x1, lsl #16
    //     0xe3d690: add             lr, x0, x17
    //     0xe3d694: ldr             lr, [x21, lr, lsl #3]
    //     0xe3d698: blr             lr
    // 0xe3d69c: mov             x1, x0
    // 0xe3d6a0: ldur            x0, [fp, #-0x38]
    // 0xe3d6a4: stur            x1, [fp, #-0x58]
    // 0xe3d6a8: tbnz            w0, #4, #0xe3d7d4
    // 0xe3d6ac: ldur            d0, [fp, #-0x88]
    // 0xe3d6b0: ldur            x2, [fp, #-0x50]
    // 0xe3d6b4: LoadField: d1 = r1->field_7
    //     0xe3d6b4: ldur            d1, [x1, #7]
    // 0xe3d6b8: fadd            d2, d0, d1
    // 0xe3d6bc: stur            d2, [fp, #-0x98]
    // 0xe3d6c0: LoadField: r3 = r2->field_7
    //     0xe3d6c0: ldur            w3, [x2, #7]
    // 0xe3d6c4: DecompressPointer r3
    //     0xe3d6c4: add             x3, x3, HEAP, lsl #32
    // 0xe3d6c8: cmp             w3, NULL
    // 0xe3d6cc: b.eq            #0xe3d85c
    // 0xe3d6d0: LoadField: r4 = r3->field_7
    //     0xe3d6d0: ldur            x4, [x3, #7]
    // 0xe3d6d4: ldr             x3, [x4]
    // 0xe3d6d8: stur            x3, [fp, #-0x40]
    // 0xe3d6dc: cbnz            x3, #0xe3d6ec
    // 0xe3d6e0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xe3d6e0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xe3d6e4: str             x16, [SP]
    // 0xe3d6e8: r0 = _throwNew()
    //     0xe3d6e8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xe3d6ec: ldur            x0, [fp, #-0x20]
    // 0xe3d6f0: ldur            x2, [fp, #-0x40]
    // 0xe3d6f4: stur            x2, [fp, #-0x40]
    // 0xe3d6f8: r1 = <Never>
    //     0xe3d6f8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xe3d6fc: r0 = Pointer()
    //     0xe3d6fc: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xe3d700: mov             x1, x0
    // 0xe3d704: ldur            x0, [fp, #-0x40]
    // 0xe3d708: stur            x1, [fp, #-0x60]
    // 0xe3d70c: StoreField: r1->field_7 = r0
    //     0xe3d70c: stur            x0, [x1, #7]
    // 0xe3d710: r0 = _NativePath()
    //     0xe3d710: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0xe3d714: ldur            x1, [fp, #-0x60]
    // 0xe3d718: mov             x2, x0
    // 0xe3d71c: ldur            x3, [fp, #-0x48]
    // 0xe3d720: ldur            d0, [fp, #-0x88]
    // 0xe3d724: ldur            d1, [fp, #-0x98]
    // 0xe3d728: r5 = true
    //     0xe3d728: add             x5, NULL, #0x20  ; true
    // 0xe3d72c: stur            x0, [fp, #-0x60]
    // 0xe3d730: r0 = __extractPath$Method$FfiNative()
    //     0xe3d730: bl              #0xe3d868  ; [dart:ui] _PathMeasure::__extractPath$Method$FfiNative
    // 0xe3d734: ldur            x0, [fp, #-0x20]
    // 0xe3d738: LoadField: r1 = r0->field_7
    //     0xe3d738: ldur            w1, [x0, #7]
    // 0xe3d73c: DecompressPointer r1
    //     0xe3d73c: add             x1, x1, HEAP, lsl #32
    // 0xe3d740: cmp             w1, NULL
    // 0xe3d744: b.eq            #0xe3d860
    // 0xe3d748: LoadField: r2 = r1->field_7
    //     0xe3d748: ldur            x2, [x1, #7]
    // 0xe3d74c: ldr             x1, [x2]
    // 0xe3d750: stur            x1, [fp, #-0x40]
    // 0xe3d754: cbnz            x1, #0xe3d764
    // 0xe3d758: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xe3d758: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xe3d75c: str             x16, [SP]
    // 0xe3d760: r0 = _throwNew()
    //     0xe3d760: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xe3d764: ldur            x0, [fp, #-0x60]
    // 0xe3d768: ldur            x2, [fp, #-0x40]
    // 0xe3d76c: stur            x2, [fp, #-0x40]
    // 0xe3d770: r1 = <Never>
    //     0xe3d770: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xe3d774: r0 = Pointer()
    //     0xe3d774: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xe3d778: mov             x2, x0
    // 0xe3d77c: ldur            x0, [fp, #-0x40]
    // 0xe3d780: stur            x2, [fp, #-0x68]
    // 0xe3d784: StoreField: r2->field_7 = r0
    //     0xe3d784: stur            x0, [x2, #7]
    // 0xe3d788: ldur            x0, [fp, #-0x60]
    // 0xe3d78c: LoadField: r1 = r0->field_7
    //     0xe3d78c: ldur            w1, [x0, #7]
    // 0xe3d790: DecompressPointer r1
    //     0xe3d790: add             x1, x1, HEAP, lsl #32
    // 0xe3d794: cmp             w1, NULL
    // 0xe3d798: b.eq            #0xe3d864
    // 0xe3d79c: LoadField: r3 = r1->field_7
    //     0xe3d79c: ldur            x3, [x1, #7]
    // 0xe3d7a0: ldr             x1, [x3]
    // 0xe3d7a4: mov             x3, x1
    // 0xe3d7a8: stur            x3, [fp, #-0x40]
    // 0xe3d7ac: r1 = <Never>
    //     0xe3d7ac: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xe3d7b0: r0 = Pointer()
    //     0xe3d7b0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xe3d7b4: mov             x1, x0
    // 0xe3d7b8: ldur            x0, [fp, #-0x40]
    // 0xe3d7bc: StoreField: r1->field_7 = r0
    //     0xe3d7bc: stur            x0, [x1, #7]
    // 0xe3d7c0: mov             x2, x1
    // 0xe3d7c4: ldur            x1, [fp, #-0x68]
    // 0xe3d7c8: ldur            d0, [fp, #-0x78]
    // 0xe3d7cc: ldur            d1, [fp, #-0x70]
    // 0xe3d7d0: r0 = __addPath$Method$FfiNative()
    //     0xe3d7d0: bl              #0xacc12c  ; [dart:ui] _NativePath::__addPath$Method$FfiNative
    // 0xe3d7d4: ldur            d0, [fp, #-0x88]
    // 0xe3d7d8: ldur            x1, [fp, #-0x38]
    // 0xe3d7dc: ldur            x2, [fp, #-0x58]
    // 0xe3d7e0: LoadField: d1 = r2->field_7
    //     0xe3d7e0: ldur            d1, [x2, #7]
    // 0xe3d7e4: fadd            d2, d0, d1
    // 0xe3d7e8: eor             x8, x1, #0x10
    // 0xe3d7ec: ldur            x1, [fp, #-0x30]
    // 0xe3d7f0: ldur            d0, [fp, #-0x90]
    // 0xe3d7f4: ldur            x3, [fp, #-0x50]
    // 0xe3d7f8: ldur            x4, [fp, #-0x48]
    // 0xe3d7fc: ldur            x2, [fp, #-0x10]
    // 0xe3d800: ldur            d1, [fp, #-0x80]
    // 0xe3d804: b               #0xe3d5e4
    // 0xe3d808: ldur            x4, [fp, #-0x18]
    // 0xe3d80c: ldur            x0, [fp, #-0x30]
    // 0xe3d810: ldur            x5, [fp, #-8]
    // 0xe3d814: ldur            x3, [fp, #-0x10]
    // 0xe3d818: ldur            d0, [fp, #-0x80]
    // 0xe3d81c: ldur            x2, [fp, #-0x28]
    // 0xe3d820: ldur            d1, [fp, #-0x78]
    // 0xe3d824: ldur            d2, [fp, #-0x70]
    // 0xe3d828: b               #0xe3d53c
    // 0xe3d82c: ldur            x1, [fp, #-0x30]
    // 0xe3d830: StoreField: r1->field_7 = rNULL
    //     0xe3d830: stur            NULL, [x1, #7]
    // 0xe3d834: ldur            x0, [fp, #-0x20]
    // 0xe3d838: LeaveFrame
    //     0xe3d838: mov             SP, fp
    //     0xe3d83c: ldp             fp, lr, [SP], #0x10
    // 0xe3d840: ret
    //     0xe3d840: ret             
    // 0xe3d844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3d844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3d848: b               #0xe3d4b4
    // 0xe3d84c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe3d84c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe3d850: b               #0xe3d54c
    // 0xe3d854: r0 = StackOverflowSharedWithFPURegs()
    //     0xe3d854: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe3d858: b               #0xe3d604
    // 0xe3d85c: r0 = NullErrorSharedWithFPURegs()
    //     0xe3d85c: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xe3d860: r0 = NullErrorSharedWithoutFPURegs()
    //     0xe3d860: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xe3d864: r0 = NullErrorSharedWithoutFPURegs()
    //     0xe3d864: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 937, size: 0x18, field offset: 0x8
class CircularIntervalList<X0> extends Object {
}

// class id: 938, size: 0x14, field offset: 0x8
//   const constructor, 
class DashOffset extends Object {

  _Mint field_8;
  _DashOffsetType field_10;

  _ DashOffset.percentage(/* No info */) {
    // ** addr: 0xac1b90, size: 0xa0
    // 0xac1b90: EnterFrame
    //     0xac1b90: stp             fp, lr, [SP, #-0x10]!
    //     0xac1b94: mov             fp, SP
    // 0xac1b98: AllocStack(0x8)
    //     0xac1b98: sub             SP, SP, #8
    // 0xac1b9c: SetupParameters(DashOffset this /* r1 => r0, fp-0x8 */)
    //     0xac1b9c: mov             x0, x1
    //     0xac1ba0: stur            x1, [fp, #-8]
    // 0xac1ba4: CheckStackOverflow
    //     0xac1ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac1ba8: cmp             SP, x16
    //     0xac1bac: b.ls            #0xac1c0c
    // 0xac1bb0: r1 = inline_Allocate_Double()
    //     0xac1bb0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xac1bb4: add             x1, x1, #0x10
    //     0xac1bb8: cmp             x2, x1
    //     0xac1bbc: b.ls            #0xac1c14
    //     0xac1bc0: str             x1, [THR, #0x50]  ; THR::top
    //     0xac1bc4: sub             x1, x1, #0xf
    //     0xac1bc8: movz            x2, #0xe15c
    //     0xac1bcc: movk            x2, #0x3, lsl #16
    //     0xac1bd0: stur            x2, [x1, #-1]
    // 0xac1bd4: StoreField: r1->field_7 = d0
    //     0xac1bd4: stur            d0, [x1, #7]
    // 0xac1bd8: r2 = 0.000000
    //     0xac1bd8: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xac1bdc: r3 = 1.000000
    //     0xac1bdc: ldr             x3, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xac1be0: r0 = clamp()
    //     0xac1be0: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xac1be4: LoadField: d0 = r0->field_7
    //     0xac1be4: ldur            d0, [x0, #7]
    // 0xac1be8: ldur            x1, [fp, #-8]
    // 0xac1bec: StoreField: r1->field_7 = d0
    //     0xac1bec: stur            d0, [x1, #7]
    // 0xac1bf0: r2 = Instance__DashOffsetType
    //     0xac1bf0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e28] Obj!_DashOffsetType@e2ffc1
    //     0xac1bf4: ldr             x2, [x2, #0xe28]
    // 0xac1bf8: StoreField: r1->field_f = r2
    //     0xac1bf8: stur            w2, [x1, #0xf]
    // 0xac1bfc: r0 = Null
    //     0xac1bfc: mov             x0, NULL
    // 0xac1c00: LeaveFrame
    //     0xac1c00: mov             SP, fp
    //     0xac1c04: ldp             fp, lr, [SP], #0x10
    // 0xac1c08: ret
    //     0xac1c08: ret             
    // 0xac1c0c: r0 = StackOverflowSharedWithFPURegs()
    //     0xac1c0c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac1c10: b               #0xac1bb0
    // 0xac1c14: SaveReg d0
    //     0xac1c14: str             q0, [SP, #-0x10]!
    // 0xac1c18: SaveReg r0
    //     0xac1c18: str             x0, [SP, #-8]!
    // 0xac1c1c: r0 = AllocateDouble()
    //     0xac1c1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac1c20: mov             x1, x0
    // 0xac1c24: RestoreReg r0
    //     0xac1c24: ldr             x0, [SP], #8
    // 0xac1c28: RestoreReg d0
    //     0xac1c28: ldr             q0, [SP], #0x10
    // 0xac1c2c: b               #0xac1bd4
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf16d8, size: 0x9c
    // 0xbf16d8: EnterFrame
    //     0xbf16d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf16dc: mov             fp, SP
    // 0xbf16e0: CheckStackOverflow
    //     0xbf16e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf16e4: cmp             SP, x16
    //     0xbf16e8: b.ls            #0xbf1750
    // 0xbf16ec: ldr             x0, [fp, #0x10]
    // 0xbf16f0: LoadField: d0 = r0->field_7
    //     0xbf16f0: ldur            d0, [x0, #7]
    // 0xbf16f4: LoadField: r2 = r0->field_f
    //     0xbf16f4: ldur            w2, [x0, #0xf]
    // 0xbf16f8: DecompressPointer r2
    //     0xbf16f8: add             x2, x2, HEAP, lsl #32
    // 0xbf16fc: r1 = inline_Allocate_Double()
    //     0xbf16fc: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xbf1700: add             x1, x1, #0x10
    //     0xbf1704: cmp             x0, x1
    //     0xbf1708: b.ls            #0xbf1758
    //     0xbf170c: str             x1, [THR, #0x50]  ; THR::top
    //     0xbf1710: sub             x1, x1, #0xf
    //     0xbf1714: movz            x0, #0xe15c
    //     0xbf1718: movk            x0, #0x3, lsl #16
    //     0xbf171c: stur            x0, [x1, #-1]
    // 0xbf1720: StoreField: r1->field_7 = d0
    //     0xbf1720: stur            d0, [x1, #7]
    // 0xbf1724: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf1724: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf1728: r0 = hash()
    //     0xbf1728: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf172c: mov             x2, x0
    // 0xbf1730: r0 = BoxInt64Instr(r2)
    //     0xbf1730: sbfiz           x0, x2, #1, #0x1f
    //     0xbf1734: cmp             x2, x0, asr #1
    //     0xbf1738: b.eq            #0xbf1744
    //     0xbf173c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1740: stur            x2, [x0, #7]
    // 0xbf1744: LeaveFrame
    //     0xbf1744: mov             SP, fp
    //     0xbf1748: ldp             fp, lr, [SP], #0x10
    // 0xbf174c: ret
    //     0xbf174c: ret             
    // 0xbf1750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1754: b               #0xbf16ec
    // 0xbf1758: SaveReg d0
    //     0xbf1758: str             q0, [SP, #-0x10]!
    // 0xbf175c: SaveReg r2
    //     0xbf175c: str             x2, [SP, #-8]!
    // 0xbf1760: r0 = AllocateDouble()
    //     0xbf1760: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbf1764: mov             x1, x0
    // 0xbf1768: RestoreReg r2
    //     0xbf1768: ldr             x2, [SP], #8
    // 0xbf176c: RestoreReg d0
    //     0xbf176c: ldr             q0, [SP], #0x10
    // 0xbf1770: b               #0xbf1720
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c34c, size: 0x80
    // 0xd7c34c: ldr             x1, [SP]
    // 0xd7c350: cmp             w1, NULL
    // 0xd7c354: b.ne            #0xd7c360
    // 0xd7c358: r0 = false
    //     0xd7c358: add             x0, NULL, #0x30  ; false
    // 0xd7c35c: ret
    //     0xd7c35c: ret             
    // 0xd7c360: ldr             x2, [SP, #8]
    // 0xd7c364: cmp             w2, w1
    // 0xd7c368: b.ne            #0xd7c374
    // 0xd7c36c: r0 = true
    //     0xd7c36c: add             x0, NULL, #0x20  ; true
    // 0xd7c370: ret
    //     0xd7c370: ret             
    // 0xd7c374: r3 = 60
    //     0xd7c374: movz            x3, #0x3c
    // 0xd7c378: branchIfSmi(r1, 0xd7c384)
    //     0xd7c378: tbz             w1, #0, #0xd7c384
    // 0xd7c37c: r3 = LoadClassIdInstr(r1)
    //     0xd7c37c: ldur            x3, [x1, #-1]
    //     0xd7c380: ubfx            x3, x3, #0xc, #0x14
    // 0xd7c384: cmp             x3, #0x3aa
    // 0xd7c388: b.ne            #0xd7c3c4
    // 0xd7c38c: LoadField: d0 = r1->field_7
    //     0xd7c38c: ldur            d0, [x1, #7]
    // 0xd7c390: LoadField: d1 = r2->field_7
    //     0xd7c390: ldur            d1, [x2, #7]
    // 0xd7c394: fcmp            d0, d1
    // 0xd7c398: b.ne            #0xd7c3c4
    // 0xd7c39c: LoadField: r3 = r1->field_f
    //     0xd7c39c: ldur            w3, [x1, #0xf]
    // 0xd7c3a0: DecompressPointer r3
    //     0xd7c3a0: add             x3, x3, HEAP, lsl #32
    // 0xd7c3a4: LoadField: r1 = r2->field_f
    //     0xd7c3a4: ldur            w1, [x2, #0xf]
    // 0xd7c3a8: DecompressPointer r1
    //     0xd7c3a8: add             x1, x1, HEAP, lsl #32
    // 0xd7c3ac: cmp             w3, w1
    // 0xd7c3b0: r16 = true
    //     0xd7c3b0: add             x16, NULL, #0x20  ; true
    // 0xd7c3b4: r17 = false
    //     0xd7c3b4: add             x17, NULL, #0x30  ; false
    // 0xd7c3b8: csel            x2, x16, x17, eq
    // 0xd7c3bc: mov             x0, x2
    // 0xd7c3c0: b               #0xd7c3c8
    // 0xd7c3c4: r0 = false
    //     0xd7c3c4: add             x0, NULL, #0x30  ; false
    // 0xd7c3c8: ret
    //     0xd7c3c8: ret             
  }
}

// class id: 6824, size: 0x14, field offset: 0x14
enum _DashOffsetType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d690, size: 0x64
    // 0xc4d690: EnterFrame
    //     0xc4d690: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d694: mov             fp, SP
    // 0xc4d698: AllocStack(0x10)
    //     0xc4d698: sub             SP, SP, #0x10
    // 0xc4d69c: SetupParameters(_DashOffsetType this /* r1 => r0, fp-0x8 */)
    //     0xc4d69c: mov             x0, x1
    //     0xc4d6a0: stur            x1, [fp, #-8]
    // 0xc4d6a4: CheckStackOverflow
    //     0xc4d6a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d6a8: cmp             SP, x16
    //     0xc4d6ac: b.ls            #0xc4d6ec
    // 0xc4d6b0: r1 = Null
    //     0xc4d6b0: mov             x1, NULL
    // 0xc4d6b4: r2 = 4
    //     0xc4d6b4: movz            x2, #0x4
    // 0xc4d6b8: r0 = AllocateArray()
    //     0xc4d6b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d6bc: r16 = "_DashOffsetType."
    //     0xc4d6bc: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ff0] "_DashOffsetType."
    //     0xc4d6c0: ldr             x16, [x16, #0xff0]
    // 0xc4d6c4: StoreField: r0->field_f = r16
    //     0xc4d6c4: stur            w16, [x0, #0xf]
    // 0xc4d6c8: ldur            x1, [fp, #-8]
    // 0xc4d6cc: LoadField: r2 = r1->field_f
    //     0xc4d6cc: ldur            w2, [x1, #0xf]
    // 0xc4d6d0: DecompressPointer r2
    //     0xc4d6d0: add             x2, x2, HEAP, lsl #32
    // 0xc4d6d4: StoreField: r0->field_13 = r2
    //     0xc4d6d4: stur            w2, [x0, #0x13]
    // 0xc4d6d8: str             x0, [SP]
    // 0xc4d6dc: r0 = _interpolate()
    //     0xc4d6dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d6e0: LeaveFrame
    //     0xc4d6e0: mov             SP, fp
    //     0xc4d6e4: ldp             fp, lr, [SP], #0x10
    // 0xc4d6e8: ret
    //     0xc4d6e8: ret             
    // 0xc4d6ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d6ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d6f0: b               #0xc4d6b0
  }
}
