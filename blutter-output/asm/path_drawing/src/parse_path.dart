// lib: , url: package:path_drawing/src/parse_path.dart

// class id: 1050763, size: 0x8
class :: {

  static _ parseSvgPathData(/* No info */) {
    // ** addr: 0xac3458, size: 0x180
    // 0xac3458: EnterFrame
    //     0xac3458: stp             fp, lr, [SP, #-0x10]!
    //     0xac345c: mov             fp, SP
    // 0xac3460: AllocStack(0x40)
    //     0xac3460: sub             SP, SP, #0x40
    // 0xac3464: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xac3464: mov             x2, x1
    //     0xac3468: stur            x1, [fp, #-8]
    // 0xac346c: CheckStackOverflow
    //     0xac346c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac3470: cmp             SP, x16
    //     0xac3474: b.ls            #0xac35c8
    // 0xac3478: r0 = LoadClassIdInstr(r2)
    //     0xac3478: ldur            x0, [x2, #-1]
    //     0xac347c: ubfx            x0, x0, #0xc, #0x14
    // 0xac3480: r16 = ""
    //     0xac3480: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xac3484: stp             x16, x2, [SP]
    // 0xac3488: mov             lr, x0
    // 0xac348c: ldr             lr, [x21, lr, lsl #3]
    // 0xac3490: blr             lr
    // 0xac3494: tbnz            w0, #4, #0xac34b8
    // 0xac3498: r0 = _NativePath()
    //     0xac3498: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0xac349c: mov             x1, x0
    // 0xac34a0: stur            x0, [fp, #-0x10]
    // 0xac34a4: r0 = __constructor$Method$FfiNative()
    //     0xac34a4: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0xac34a8: ldur            x0, [fp, #-0x10]
    // 0xac34ac: LeaveFrame
    //     0xac34ac: mov             SP, fp
    //     0xac34b0: ldp             fp, lr, [SP], #0x10
    // 0xac34b4: ret
    //     0xac34b4: ret             
    // 0xac34b8: r0 = SvgPathStringSource()
    //     0xac34b8: bl              #0xac80dc  ; AllocateSvgPathStringSourceStub -> SvgPathStringSource (size=0x20)
    // 0xac34bc: mov             x1, x0
    // 0xac34c0: ldur            x2, [fp, #-8]
    // 0xac34c4: stur            x0, [fp, #-8]
    // 0xac34c8: r0 = SvgPathStringSource()
    //     0xac34c8: bl              #0xac8060  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::SvgPathStringSource
    // 0xac34cc: r0 = _NativePath()
    //     0xac34cc: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0xac34d0: mov             x1, x0
    // 0xac34d4: stur            x0, [fp, #-0x10]
    // 0xac34d8: r0 = __constructor$Method$FfiNative()
    //     0xac34d8: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0xac34dc: r0 = FlutterPathProxy()
    //     0xac34dc: bl              #0xac8054  ; AllocateFlutterPathProxyStub -> FlutterPathProxy (size=0xc)
    // 0xac34e0: mov             x1, x0
    // 0xac34e4: ldur            x0, [fp, #-0x10]
    // 0xac34e8: stur            x1, [fp, #-0x18]
    // 0xac34ec: StoreField: r1->field_7 = r0
    //     0xac34ec: stur            w0, [x1, #7]
    // 0xac34f0: r0 = SvgPathNormalizer()
    //     0xac34f0: bl              #0xac8048  ; AllocateSvgPathNormalizerStub -> SvgPathNormalizer (size=0x18)
    // 0xac34f4: mov             x2, x0
    // 0xac34f8: r0 = Instance_SvgPathSegType
    //     0xac34f8: add             x0, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac34fc: ldr             x0, [x0, #0x150]
    // 0xac3500: stur            x2, [fp, #-0x20]
    // 0xac3504: StoreField: r2->field_13 = r0
    //     0xac3504: stur            w0, [x2, #0x13]
    // 0xac3508: r0 = Instance__PathOffset
    //     0xac3508: add             x0, PP, #0x26, lsl #12  ; [pp+0x26158] Obj!_PathOffset@e0e3f1
    //     0xac350c: ldr             x0, [x0, #0x158]
    // 0xac3510: StoreField: r2->field_7 = r0
    //     0xac3510: stur            w0, [x2, #7]
    // 0xac3514: StoreField: r2->field_b = r0
    //     0xac3514: stur            w0, [x2, #0xb]
    // 0xac3518: StoreField: r2->field_f = r0
    //     0xac3518: stur            w0, [x2, #0xf]
    // 0xac351c: ldur            x1, [fp, #-8]
    // 0xac3520: r0 = parseSegments()
    //     0xac3520: bl              #0xac6c90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::parseSegments
    // 0xac3524: mov             x1, x0
    // 0xac3528: r0 = iterator()
    //     0xac3528: bl              #0x887928  ; [dart:async] _SyncStarIterable::iterator
    // 0xac352c: stur            x0, [fp, #-0x28]
    // 0xac3530: LoadField: r2 = r0->field_7
    //     0xac3530: ldur            w2, [x0, #7]
    // 0xac3534: DecompressPointer r2
    //     0xac3534: add             x2, x2, HEAP, lsl #32
    // 0xac3538: stur            x2, [fp, #-8]
    // 0xac353c: CheckStackOverflow
    //     0xac353c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac3540: cmp             SP, x16
    //     0xac3544: b.ls            #0xac35d0
    // 0xac3548: mov             x1, x0
    // 0xac354c: r0 = moveNext()
    //     0xac354c: bl              #0x6769a4  ; [dart:async] _SyncStarIterator::moveNext
    // 0xac3550: tbnz            w0, #4, #0xac35b8
    // 0xac3554: ldur            x3, [fp, #-0x28]
    // 0xac3558: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xac3558: ldur            w4, [x3, #0x17]
    // 0xac355c: DecompressPointer r4
    //     0xac355c: add             x4, x4, HEAP, lsl #32
    // 0xac3560: stur            x4, [fp, #-0x30]
    // 0xac3564: cmp             w4, NULL
    // 0xac3568: b.ne            #0xac359c
    // 0xac356c: mov             x0, x4
    // 0xac3570: ldur            x2, [fp, #-8]
    // 0xac3574: r1 = Null
    //     0xac3574: mov             x1, NULL
    // 0xac3578: cmp             w2, NULL
    // 0xac357c: b.eq            #0xac359c
    // 0xac3580: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xac3580: ldur            w4, [x2, #0x17]
    // 0xac3584: DecompressPointer r4
    //     0xac3584: add             x4, x4, HEAP, lsl #32
    // 0xac3588: r8 = X0
    //     0xac3588: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xac358c: LoadField: r9 = r4->field_7
    //     0xac358c: ldur            x9, [x4, #7]
    // 0xac3590: r3 = Null
    //     0xac3590: add             x3, PP, #0x26, lsl #12  ; [pp+0x26160] Null
    //     0xac3594: ldr             x3, [x3, #0x160]
    // 0xac3598: blr             x9
    // 0xac359c: ldur            x1, [fp, #-0x20]
    // 0xac35a0: ldur            x2, [fp, #-0x30]
    // 0xac35a4: ldur            x3, [fp, #-0x18]
    // 0xac35a8: r0 = emitSegment()
    //     0xac35a8: bl              #0xac35d8  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::emitSegment
    // 0xac35ac: ldur            x0, [fp, #-0x28]
    // 0xac35b0: ldur            x2, [fp, #-8]
    // 0xac35b4: b               #0xac353c
    // 0xac35b8: ldur            x0, [fp, #-0x10]
    // 0xac35bc: LeaveFrame
    //     0xac35bc: mov             SP, fp
    //     0xac35c0: ldp             fp, lr, [SP], #0x10
    // 0xac35c4: ret
    //     0xac35c4: ret             
    // 0xac35c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac35c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac35cc: b               #0xac3478
    // 0xac35d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac35d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac35d4: b               #0xac3548
  }
}

// class id: 936, size: 0xc, field offset: 0x8
class FlutterPathProxy extends PathProxy {

  _ cubicTo(/* No info */) {
    // ** addr: 0xe8da4c, size: 0xbc
    // 0xe8da4c: EnterFrame
    //     0xe8da4c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8da50: mov             fp, SP
    // 0xe8da54: AllocStack(0x48)
    //     0xe8da54: sub             SP, SP, #0x48
    // 0xe8da58: SetupParameters(dynamic _ /* d0 => d0, fp-0x18 */, dynamic _ /* d1 => d1, fp-0x20 */, dynamic _ /* d2 => d2, fp-0x28 */, dynamic _ /* d3 => d3, fp-0x30 */, dynamic _ /* d4 => d4, fp-0x38 */, dynamic _ /* d5 => d5, fp-0x40 */)
    //     0xe8da58: stur            d0, [fp, #-0x18]
    //     0xe8da5c: stur            d1, [fp, #-0x20]
    //     0xe8da60: stur            d2, [fp, #-0x28]
    //     0xe8da64: stur            d3, [fp, #-0x30]
    //     0xe8da68: stur            d4, [fp, #-0x38]
    //     0xe8da6c: stur            d5, [fp, #-0x40]
    // 0xe8da70: CheckStackOverflow
    //     0xe8da70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8da74: cmp             SP, x16
    //     0xe8da78: b.ls            #0xe8dafc
    // 0xe8da7c: LoadField: r0 = r1->field_7
    //     0xe8da7c: ldur            w0, [x1, #7]
    // 0xe8da80: DecompressPointer r0
    //     0xe8da80: add             x0, x0, HEAP, lsl #32
    // 0xe8da84: stur            x0, [fp, #-0x10]
    // 0xe8da88: LoadField: r1 = r0->field_7
    //     0xe8da88: ldur            w1, [x0, #7]
    // 0xe8da8c: DecompressPointer r1
    //     0xe8da8c: add             x1, x1, HEAP, lsl #32
    // 0xe8da90: cmp             w1, NULL
    // 0xe8da94: b.eq            #0xe8db04
    // 0xe8da98: LoadField: r2 = r1->field_7
    //     0xe8da98: ldur            x2, [x1, #7]
    // 0xe8da9c: ldr             x1, [x2]
    // 0xe8daa0: stur            x1, [fp, #-8]
    // 0xe8daa4: cbnz            x1, #0xe8dab4
    // 0xe8daa8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xe8daa8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xe8daac: str             x16, [SP]
    // 0xe8dab0: r0 = _throwNew()
    //     0xe8dab0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xe8dab4: ldur            x0, [fp, #-8]
    // 0xe8dab8: stur            x0, [fp, #-8]
    // 0xe8dabc: r1 = <Never>
    //     0xe8dabc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xe8dac0: r0 = Pointer()
    //     0xe8dac0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xe8dac4: mov             x1, x0
    // 0xe8dac8: ldur            x0, [fp, #-8]
    // 0xe8dacc: StoreField: r1->field_7 = r0
    //     0xe8dacc: stur            x0, [x1, #7]
    // 0xe8dad0: ldur            d0, [fp, #-0x18]
    // 0xe8dad4: ldur            d1, [fp, #-0x20]
    // 0xe8dad8: ldur            d2, [fp, #-0x28]
    // 0xe8dadc: ldur            d3, [fp, #-0x30]
    // 0xe8dae0: ldur            d4, [fp, #-0x38]
    // 0xe8dae4: ldur            d5, [fp, #-0x40]
    // 0xe8dae8: r0 = _cubicTo$Method$FfiNative()
    //     0xe8dae8: bl              #0xac4334  ; [dart:ui] _NativePath::_cubicTo$Method$FfiNative
    // 0xe8daec: r0 = Null
    //     0xe8daec: mov             x0, NULL
    // 0xe8daf0: LeaveFrame
    //     0xe8daf0: mov             SP, fp
    //     0xe8daf4: ldp             fp, lr, [SP], #0x10
    // 0xe8daf8: ret
    //     0xe8daf8: ret             
    // 0xe8dafc: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8dafc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8db00: b               #0xe8da7c
    // 0xe8db04: r0 = NullErrorSharedWithFPURegs()
    //     0xe8db04: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
}
