// lib: , url: package:path_parsing/src/path_parsing.dart

// class id: 1050764, size: 0x8
class :: {

  static _ blendPoints(/* No info */) {
    // ** addr: 0xac42d0, size: 0x64
    // 0xac42d0: EnterFrame
    //     0xac42d0: stp             fp, lr, [SP, #-0x10]!
    //     0xac42d4: mov             fp, SP
    // 0xac42d8: AllocStack(0x10)
    //     0xac42d8: sub             SP, SP, #0x10
    // 0xac42dc: d1 = 2.000000
    //     0xac42dc: fmov            d1, #2.00000000
    // 0xac42e0: d0 = 0.333333
    //     0xac42e0: ldr             d0, [PP, #0x5948]  ; [pp+0x5948] IMM: double(0.3333333333333333) from 0x3fd5555555555555
    // 0xac42e4: LoadField: d2 = r1->field_7
    //     0xac42e4: ldur            d2, [x1, #7]
    // 0xac42e8: LoadField: d3 = r2->field_7
    //     0xac42e8: ldur            d3, [x2, #7]
    // 0xac42ec: fmul            d4, d3, d1
    // 0xac42f0: fadd            d3, d2, d4
    // 0xac42f4: fmul            d2, d3, d0
    // 0xac42f8: stur            d2, [fp, #-0x10]
    // 0xac42fc: LoadField: d3 = r1->field_f
    //     0xac42fc: ldur            d3, [x1, #0xf]
    // 0xac4300: LoadField: d4 = r2->field_f
    //     0xac4300: ldur            d4, [x2, #0xf]
    // 0xac4304: fmul            d5, d4, d1
    // 0xac4308: fadd            d1, d3, d5
    // 0xac430c: fmul            d3, d1, d0
    // 0xac4310: stur            d3, [fp, #-8]
    // 0xac4314: r0 = _PathOffset()
    //     0xac4314: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac4318: ldur            d0, [fp, #-0x10]
    // 0xac431c: StoreField: r0->field_7 = d0
    //     0xac431c: stur            d0, [x0, #7]
    // 0xac4320: ldur            d0, [fp, #-8]
    // 0xac4324: StoreField: r0->field_f = d0
    //     0xac4324: stur            d0, [x0, #0xf]
    // 0xac4328: LeaveFrame
    //     0xac4328: mov             SP, fp
    //     0xac432c: ldp             fp, lr, [SP], #0x10
    // 0xac4330: ret
    //     0xac4330: ret             
  }
  static _ reflectedPoint(/* No info */) {
    // ** addr: 0xac4690, size: 0x58
    // 0xac4690: EnterFrame
    //     0xac4690: stp             fp, lr, [SP, #-0x10]!
    //     0xac4694: mov             fp, SP
    // 0xac4698: AllocStack(0x10)
    //     0xac4698: sub             SP, SP, #0x10
    // 0xac469c: d0 = 2.000000
    //     0xac469c: fmov            d0, #2.00000000
    // 0xac46a0: LoadField: d1 = r1->field_7
    //     0xac46a0: ldur            d1, [x1, #7]
    // 0xac46a4: fmul            d2, d1, d0
    // 0xac46a8: LoadField: d1 = r2->field_7
    //     0xac46a8: ldur            d1, [x2, #7]
    // 0xac46ac: fsub            d3, d2, d1
    // 0xac46b0: stur            d3, [fp, #-0x10]
    // 0xac46b4: LoadField: d1 = r1->field_f
    //     0xac46b4: ldur            d1, [x1, #0xf]
    // 0xac46b8: fmul            d2, d1, d0
    // 0xac46bc: LoadField: d0 = r2->field_f
    //     0xac46bc: ldur            d0, [x2, #0xf]
    // 0xac46c0: fsub            d1, d2, d0
    // 0xac46c4: stur            d1, [fp, #-8]
    // 0xac46c8: r0 = _PathOffset()
    //     0xac46c8: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac46cc: ldur            d0, [fp, #-0x10]
    // 0xac46d0: StoreField: r0->field_7 = d0
    //     0xac46d0: stur            d0, [x0, #7]
    // 0xac46d4: ldur            d0, [fp, #-8]
    // 0xac46d8: StoreField: r0->field_f = d0
    //     0xac46d8: stur            d0, [x0, #0xf]
    // 0xac46dc: LeaveFrame
    //     0xac46dc: mov             SP, fp
    //     0xac46e0: ldp             fp, lr, [SP], #0x10
    // 0xac46e4: ret
    //     0xac46e4: ret             
  }
  static _ writeSvgPathDataToPath(/* No info */) {
    // ** addr: 0xe4982c, size: 0x154
    // 0xe4982c: EnterFrame
    //     0xe4982c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49830: mov             fp, SP
    // 0xe49834: AllocStack(0x38)
    //     0xe49834: sub             SP, SP, #0x38
    // 0xe49838: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe49838: mov             x3, x2
    //     0xe4983c: stur            x2, [fp, #-0x10]
    //     0xe49840: mov             x2, x1
    //     0xe49844: stur            x1, [fp, #-8]
    // 0xe49848: CheckStackOverflow
    //     0xe49848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4984c: cmp             SP, x16
    //     0xe49850: b.ls            #0xe49970
    // 0xe49854: r0 = LoadClassIdInstr(r2)
    //     0xe49854: ldur            x0, [x2, #-1]
    //     0xe49858: ubfx            x0, x0, #0xc, #0x14
    // 0xe4985c: r16 = ""
    //     0xe4985c: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe49860: stp             x16, x2, [SP]
    // 0xe49864: mov             lr, x0
    // 0xe49868: ldr             lr, [x21, lr, lsl #3]
    // 0xe4986c: blr             lr
    // 0xe49870: tbnz            w0, #4, #0xe49884
    // 0xe49874: r0 = Null
    //     0xe49874: mov             x0, NULL
    // 0xe49878: LeaveFrame
    //     0xe49878: mov             SP, fp
    //     0xe4987c: ldp             fp, lr, [SP], #0x10
    // 0xe49880: ret
    //     0xe49880: ret             
    // 0xe49884: r0 = SvgPathStringSource()
    //     0xe49884: bl              #0xac80dc  ; AllocateSvgPathStringSourceStub -> SvgPathStringSource (size=0x20)
    // 0xe49888: mov             x1, x0
    // 0xe4988c: ldur            x2, [fp, #-8]
    // 0xe49890: stur            x0, [fp, #-8]
    // 0xe49894: r0 = SvgPathStringSource()
    //     0xe49894: bl              #0xac8060  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::SvgPathStringSource
    // 0xe49898: r0 = SvgPathNormalizer()
    //     0xe49898: bl              #0xac8048  ; AllocateSvgPathNormalizerStub -> SvgPathNormalizer (size=0x18)
    // 0xe4989c: mov             x2, x0
    // 0xe498a0: r0 = Instance_SvgPathSegType
    //     0xe498a0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xe498a4: ldr             x0, [x0, #0x150]
    // 0xe498a8: stur            x2, [fp, #-0x18]
    // 0xe498ac: StoreField: r2->field_13 = r0
    //     0xe498ac: stur            w0, [x2, #0x13]
    // 0xe498b0: r0 = Instance__PathOffset
    //     0xe498b0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26158] Obj!_PathOffset@e0e3f1
    //     0xe498b4: ldr             x0, [x0, #0x158]
    // 0xe498b8: StoreField: r2->field_7 = r0
    //     0xe498b8: stur            w0, [x2, #7]
    // 0xe498bc: StoreField: r2->field_b = r0
    //     0xe498bc: stur            w0, [x2, #0xb]
    // 0xe498c0: StoreField: r2->field_f = r0
    //     0xe498c0: stur            w0, [x2, #0xf]
    // 0xe498c4: ldur            x1, [fp, #-8]
    // 0xe498c8: r0 = parseSegments()
    //     0xe498c8: bl              #0xac6c90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::parseSegments
    // 0xe498cc: mov             x1, x0
    // 0xe498d0: r0 = iterator()
    //     0xe498d0: bl              #0x887928  ; [dart:async] _SyncStarIterable::iterator
    // 0xe498d4: stur            x0, [fp, #-0x20]
    // 0xe498d8: LoadField: r2 = r0->field_7
    //     0xe498d8: ldur            w2, [x0, #7]
    // 0xe498dc: DecompressPointer r2
    //     0xe498dc: add             x2, x2, HEAP, lsl #32
    // 0xe498e0: stur            x2, [fp, #-8]
    // 0xe498e4: CheckStackOverflow
    //     0xe498e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe498e8: cmp             SP, x16
    //     0xe498ec: b.ls            #0xe49978
    // 0xe498f0: mov             x1, x0
    // 0xe498f4: r0 = moveNext()
    //     0xe498f4: bl              #0x6769a4  ; [dart:async] _SyncStarIterator::moveNext
    // 0xe498f8: tbnz            w0, #4, #0xe49960
    // 0xe498fc: ldur            x3, [fp, #-0x20]
    // 0xe49900: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xe49900: ldur            w4, [x3, #0x17]
    // 0xe49904: DecompressPointer r4
    //     0xe49904: add             x4, x4, HEAP, lsl #32
    // 0xe49908: stur            x4, [fp, #-0x28]
    // 0xe4990c: cmp             w4, NULL
    // 0xe49910: b.ne            #0xe49944
    // 0xe49914: mov             x0, x4
    // 0xe49918: ldur            x2, [fp, #-8]
    // 0xe4991c: r1 = Null
    //     0xe4991c: mov             x1, NULL
    // 0xe49920: cmp             w2, NULL
    // 0xe49924: b.eq            #0xe49944
    // 0xe49928: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe49928: ldur            w4, [x2, #0x17]
    // 0xe4992c: DecompressPointer r4
    //     0xe4992c: add             x4, x4, HEAP, lsl #32
    // 0xe49930: r8 = X0
    //     0xe49930: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe49934: LoadField: r9 = r4->field_7
    //     0xe49934: ldur            x9, [x4, #7]
    // 0xe49938: r3 = Null
    //     0xe49938: add             x3, PP, #0x46, lsl #12  ; [pp+0x46df0] Null
    //     0xe4993c: ldr             x3, [x3, #0xdf0]
    // 0xe49940: blr             x9
    // 0xe49944: ldur            x1, [fp, #-0x18]
    // 0xe49948: ldur            x2, [fp, #-0x28]
    // 0xe4994c: ldur            x3, [fp, #-0x10]
    // 0xe49950: r0 = emitSegment()
    //     0xe49950: bl              #0xac35d8  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::emitSegment
    // 0xe49954: ldur            x0, [fp, #-0x20]
    // 0xe49958: ldur            x2, [fp, #-8]
    // 0xe4995c: b               #0xe498e4
    // 0xe49960: r0 = Null
    //     0xe49960: mov             x0, NULL
    // 0xe49964: LeaveFrame
    //     0xe49964: mov             SP, fp
    //     0xe49968: ldp             fp, lr, [SP], #0x10
    // 0xe4996c: ret
    //     0xe4996c: ret             
    // 0xe49970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49970: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49974: b               #0xe49854
    // 0xe49978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4997c: b               #0xe498f0
  }
}

// class id: 929, size: 0x18, field offset: 0x8
class SvgPathNormalizer extends Object {

  _ emitSegment(/* No info */) {
    // ** addr: 0xac35d8, size: 0xcf8
    // 0xac35d8: EnterFrame
    //     0xac35d8: stp             fp, lr, [SP, #-0x10]!
    //     0xac35dc: mov             fp, SP
    // 0xac35e0: AllocStack(0x60)
    //     0xac35e0: sub             SP, SP, #0x60
    // 0xac35e4: SetupParameters(SvgPathNormalizer this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xac35e4: mov             x5, x1
    //     0xac35e8: mov             x4, x2
    //     0xac35ec: stur            x1, [fp, #-8]
    //     0xac35f0: stur            x2, [fp, #-0x10]
    //     0xac35f4: stur            x3, [fp, #-0x18]
    // 0xac35f8: CheckStackOverflow
    //     0xac35f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac35fc: cmp             SP, x16
    //     0xac3600: b.ls            #0xac42b0
    // 0xac3604: LoadField: r0 = r4->field_7
    //     0xac3604: ldur            w0, [x4, #7]
    // 0xac3608: DecompressPointer r0
    //     0xac3608: add             x0, x0, HEAP, lsl #32
    // 0xac360c: LoadField: r2 = r0->field_7
    //     0xac360c: ldur            x2, [x0, #7]
    // 0xac3610: cmp             x2, #0xb
    // 0xac3614: b.gt            #0xac37a4
    // 0xac3618: cmp             x2, #5
    // 0xac361c: b.gt            #0xac36b8
    // 0xac3620: cmp             x2, #3
    // 0xac3624: b.gt            #0xac369c
    // 0xac3628: cmp             x2, #1
    // 0xac362c: b.gt            #0xac3680
    // 0xac3630: r0 = BoxInt64Instr(r2)
    //     0xac3630: sbfiz           x0, x2, #1, #0x1f
    //     0xac3634: cmp             x2, x0, asr #1
    //     0xac3638: b.eq            #0xac3644
    //     0xac363c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xac3640: stur            x2, [x0, #7]
    // 0xac3644: cmp             w0, #2
    // 0xac3648: b.ne            #0xac3678
    // 0xac364c: LoadField: r0 = r5->field_b
    //     0xac364c: ldur            w0, [x5, #0xb]
    // 0xac3650: DecompressPointer r0
    //     0xac3650: add             x0, x0, HEAP, lsl #32
    // 0xac3654: StoreField: r4->field_b = r0
    //     0xac3654: stur            w0, [x4, #0xb]
    //     0xac3658: ldurb           w16, [x4, #-1]
    //     0xac365c: ldurb           w17, [x0, #-1]
    //     0xac3660: and             x16, x17, x16, lsr #2
    //     0xac3664: tst             x16, HEAP, lsr #32
    //     0xac3668: b.eq            #0xac3670
    //     0xac366c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xac3670: mov             x3, x4
    // 0xac3674: b               #0xac393c
    // 0xac3678: mov             x3, x4
    // 0xac367c: b               #0xac393c
    // 0xac3680: cmp             x2, #3
    // 0xac3684: b.lt            #0xac3694
    // 0xac3688: mov             x0, x5
    // 0xac368c: mov             x3, x4
    // 0xac3690: b               #0xac3908
    // 0xac3694: mov             x3, x4
    // 0xac3698: b               #0xac393c
    // 0xac369c: cmp             x2, #5
    // 0xac36a0: b.lt            #0xac36b0
    // 0xac36a4: mov             x0, x5
    // 0xac36a8: mov             x3, x4
    // 0xac36ac: b               #0xac3908
    // 0xac36b0: mov             x3, x4
    // 0xac36b4: b               #0xac393c
    // 0xac36b8: cmp             x2, #7
    // 0xac36bc: b.lt            #0xac379c
    // 0xac36c0: cmp             x2, #9
    // 0xac36c4: b.gt            #0xac3788
    // 0xac36c8: cmp             x2, #7
    // 0xac36cc: b.gt            #0xac3708
    // 0xac36d0: LoadField: r1 = r4->field_f
    //     0xac36d0: ldur            w1, [x4, #0xf]
    // 0xac36d4: DecompressPointer r1
    //     0xac36d4: add             x1, x1, HEAP, lsl #32
    // 0xac36d8: LoadField: r2 = r5->field_7
    //     0xac36d8: ldur            w2, [x5, #7]
    // 0xac36dc: DecompressPointer r2
    //     0xac36dc: add             x2, x2, HEAP, lsl #32
    // 0xac36e0: r0 = +()
    //     0xac36e0: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac36e4: ldur            x3, [fp, #-0x10]
    // 0xac36e8: StoreField: r3->field_f = r0
    //     0xac36e8: stur            w0, [x3, #0xf]
    //     0xac36ec: ldurb           w16, [x3, #-1]
    //     0xac36f0: ldurb           w17, [x0, #-1]
    //     0xac36f4: and             x16, x17, x16, lsr #2
    //     0xac36f8: tst             x16, HEAP, lsr #32
    //     0xac36fc: b.eq            #0xac3704
    //     0xac3700: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac3704: b               #0xac38a0
    // 0xac3708: mov             x3, x4
    // 0xac370c: cmp             x2, #9
    // 0xac3710: b.lt            #0xac393c
    // 0xac3714: ldur            x0, [fp, #-8]
    // 0xac3718: LoadField: r1 = r3->field_f
    //     0xac3718: ldur            w1, [x3, #0xf]
    // 0xac371c: DecompressPointer r1
    //     0xac371c: add             x1, x1, HEAP, lsl #32
    // 0xac3720: LoadField: r2 = r0->field_7
    //     0xac3720: ldur            w2, [x0, #7]
    // 0xac3724: DecompressPointer r2
    //     0xac3724: add             x2, x2, HEAP, lsl #32
    // 0xac3728: r0 = +()
    //     0xac3728: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac372c: ldur            x3, [fp, #-0x10]
    // 0xac3730: StoreField: r3->field_f = r0
    //     0xac3730: stur            w0, [x3, #0xf]
    //     0xac3734: ldurb           w16, [x3, #-1]
    //     0xac3738: ldurb           w17, [x0, #-1]
    //     0xac373c: and             x16, x17, x16, lsr #2
    //     0xac3740: tst             x16, HEAP, lsr #32
    //     0xac3744: b.eq            #0xac374c
    //     0xac3748: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac374c: LoadField: r1 = r3->field_b
    //     0xac374c: ldur            w1, [x3, #0xb]
    // 0xac3750: DecompressPointer r1
    //     0xac3750: add             x1, x1, HEAP, lsl #32
    // 0xac3754: ldur            x0, [fp, #-8]
    // 0xac3758: LoadField: r2 = r0->field_7
    //     0xac3758: ldur            w2, [x0, #7]
    // 0xac375c: DecompressPointer r2
    //     0xac375c: add             x2, x2, HEAP, lsl #32
    // 0xac3760: r0 = +()
    //     0xac3760: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac3764: ldur            x3, [fp, #-0x10]
    // 0xac3768: StoreField: r3->field_b = r0
    //     0xac3768: stur            w0, [x3, #0xb]
    //     0xac376c: ldurb           w16, [x3, #-1]
    //     0xac3770: ldurb           w17, [x0, #-1]
    //     0xac3774: and             x16, x17, x16, lsr #2
    //     0xac3778: tst             x16, HEAP, lsr #32
    //     0xac377c: b.eq            #0xac3784
    //     0xac3780: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac3784: b               #0xac393c
    // 0xac3788: mov             x3, x4
    // 0xac378c: cmp             x2, #0xb
    // 0xac3790: b.lt            #0xac393c
    // 0xac3794: ldur            x0, [fp, #-8]
    // 0xac3798: b               #0xac3908
    // 0xac379c: mov             x3, x4
    // 0xac37a0: b               #0xac393c
    // 0xac37a4: mov             x3, x4
    // 0xac37a8: cmp             x2, #0xe
    // 0xac37ac: b.gt            #0xac3880
    // 0xac37b0: cmp             x2, #0xd
    // 0xac37b4: b.gt            #0xac3824
    // 0xac37b8: cmp             x2, #0xc
    // 0xac37bc: b.gt            #0xac381c
    // 0xac37c0: ldur            x1, [fp, #-8]
    // 0xac37c4: LoadField: r0 = r3->field_b
    //     0xac37c4: ldur            w0, [x3, #0xb]
    // 0xac37c8: DecompressPointer r0
    //     0xac37c8: add             x0, x0, HEAP, lsl #32
    // 0xac37cc: LoadField: d0 = r0->field_7
    //     0xac37cc: ldur            d0, [x0, #7]
    // 0xac37d0: stur            d0, [fp, #-0x38]
    // 0xac37d4: LoadField: r0 = r1->field_7
    //     0xac37d4: ldur            w0, [x1, #7]
    // 0xac37d8: DecompressPointer r0
    //     0xac37d8: add             x0, x0, HEAP, lsl #32
    // 0xac37dc: LoadField: d1 = r0->field_f
    //     0xac37dc: ldur            d1, [x0, #0xf]
    // 0xac37e0: stur            d1, [fp, #-0x30]
    // 0xac37e4: r0 = _PathOffset()
    //     0xac37e4: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac37e8: ldur            d0, [fp, #-0x38]
    // 0xac37ec: StoreField: r0->field_7 = d0
    //     0xac37ec: stur            d0, [x0, #7]
    // 0xac37f0: ldur            d0, [fp, #-0x30]
    // 0xac37f4: StoreField: r0->field_f = d0
    //     0xac37f4: stur            d0, [x0, #0xf]
    // 0xac37f8: ldur            x3, [fp, #-0x10]
    // 0xac37fc: StoreField: r3->field_b = r0
    //     0xac37fc: stur            w0, [x3, #0xb]
    //     0xac3800: ldurb           w16, [x3, #-1]
    //     0xac3804: ldurb           w17, [x0, #-1]
    //     0xac3808: and             x16, x17, x16, lsr #2
    //     0xac380c: tst             x16, HEAP, lsr #32
    //     0xac3810: b.eq            #0xac3818
    //     0xac3814: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac3818: b               #0xac393c
    // 0xac381c: ldur            x0, [fp, #-8]
    // 0xac3820: b               #0xac3908
    // 0xac3824: ldur            x1, [fp, #-8]
    // 0xac3828: LoadField: r0 = r1->field_7
    //     0xac3828: ldur            w0, [x1, #7]
    // 0xac382c: DecompressPointer r0
    //     0xac382c: add             x0, x0, HEAP, lsl #32
    // 0xac3830: LoadField: d0 = r0->field_7
    //     0xac3830: ldur            d0, [x0, #7]
    // 0xac3834: stur            d0, [fp, #-0x38]
    // 0xac3838: LoadField: r0 = r3->field_b
    //     0xac3838: ldur            w0, [x3, #0xb]
    // 0xac383c: DecompressPointer r0
    //     0xac383c: add             x0, x0, HEAP, lsl #32
    // 0xac3840: LoadField: d1 = r0->field_f
    //     0xac3840: ldur            d1, [x0, #0xf]
    // 0xac3844: stur            d1, [fp, #-0x30]
    // 0xac3848: r0 = _PathOffset()
    //     0xac3848: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac384c: ldur            d0, [fp, #-0x38]
    // 0xac3850: StoreField: r0->field_7 = d0
    //     0xac3850: stur            d0, [x0, #7]
    // 0xac3854: ldur            d0, [fp, #-0x30]
    // 0xac3858: StoreField: r0->field_f = d0
    //     0xac3858: stur            d0, [x0, #0xf]
    // 0xac385c: ldur            x3, [fp, #-0x10]
    // 0xac3860: StoreField: r3->field_b = r0
    //     0xac3860: stur            w0, [x3, #0xb]
    //     0xac3864: ldurb           w16, [x3, #-1]
    //     0xac3868: ldurb           w17, [x0, #-1]
    //     0xac386c: and             x16, x17, x16, lsr #2
    //     0xac3870: tst             x16, HEAP, lsr #32
    //     0xac3874: b.eq            #0xac387c
    //     0xac3878: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac387c: b               #0xac393c
    // 0xac3880: cmp             x2, #0x11
    // 0xac3884: b.gt            #0xac38e0
    // 0xac3888: cmp             x2, #0xf
    // 0xac388c: b.gt            #0xac3898
    // 0xac3890: ldur            x0, [fp, #-8]
    // 0xac3894: b               #0xac3908
    // 0xac3898: cmp             x2, #0x11
    // 0xac389c: b.lt            #0xac393c
    // 0xac38a0: ldur            x0, [fp, #-8]
    // 0xac38a4: LoadField: r1 = r3->field_13
    //     0xac38a4: ldur            w1, [x3, #0x13]
    // 0xac38a8: DecompressPointer r1
    //     0xac38a8: add             x1, x1, HEAP, lsl #32
    // 0xac38ac: LoadField: r2 = r0->field_7
    //     0xac38ac: ldur            w2, [x0, #7]
    // 0xac38b0: DecompressPointer r2
    //     0xac38b0: add             x2, x2, HEAP, lsl #32
    // 0xac38b4: r0 = +()
    //     0xac38b4: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac38b8: ldur            x3, [fp, #-0x10]
    // 0xac38bc: StoreField: r3->field_13 = r0
    //     0xac38bc: stur            w0, [x3, #0x13]
    //     0xac38c0: ldurb           w16, [x3, #-1]
    //     0xac38c4: ldurb           w17, [x0, #-1]
    //     0xac38c8: and             x16, x17, x16, lsr #2
    //     0xac38cc: tst             x16, HEAP, lsr #32
    //     0xac38d0: b.eq            #0xac38d8
    //     0xac38d4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac38d8: ldur            x0, [fp, #-8]
    // 0xac38dc: b               #0xac3908
    // 0xac38e0: cmp             x2, #0x13
    // 0xac38e4: b.lt            #0xac393c
    // 0xac38e8: r0 = BoxInt64Instr(r2)
    //     0xac38e8: sbfiz           x0, x2, #1, #0x1f
    //     0xac38ec: cmp             x2, x0, asr #1
    //     0xac38f0: b.eq            #0xac38fc
    //     0xac38f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xac38f8: stur            x2, [x0, #7]
    // 0xac38fc: cmp             w0, #0x26
    // 0xac3900: b.ne            #0xac393c
    // 0xac3904: ldur            x0, [fp, #-8]
    // 0xac3908: LoadField: r1 = r3->field_b
    //     0xac3908: ldur            w1, [x3, #0xb]
    // 0xac390c: DecompressPointer r1
    //     0xac390c: add             x1, x1, HEAP, lsl #32
    // 0xac3910: LoadField: r2 = r0->field_7
    //     0xac3910: ldur            w2, [x0, #7]
    // 0xac3914: DecompressPointer r2
    //     0xac3914: add             x2, x2, HEAP, lsl #32
    // 0xac3918: r0 = +()
    //     0xac3918: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac391c: ldur            x3, [fp, #-0x10]
    // 0xac3920: StoreField: r3->field_b = r0
    //     0xac3920: stur            w0, [x3, #0xb]
    //     0xac3924: ldurb           w16, [x3, #-1]
    //     0xac3928: ldurb           w17, [x0, #-1]
    //     0xac392c: and             x16, x17, x16, lsr #2
    //     0xac3930: tst             x16, HEAP, lsr #32
    //     0xac3934: b.eq            #0xac393c
    //     0xac3938: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac393c: LoadField: r0 = r3->field_7
    //     0xac393c: ldur            w0, [x3, #7]
    // 0xac3940: DecompressPointer r0
    //     0xac3940: add             x0, x0, HEAP, lsl #32
    // 0xac3944: LoadField: r2 = r0->field_7
    //     0xac3944: ldur            x2, [x0, #7]
    // 0xac3948: cmp             x2, #0x13
    // 0xac394c: b.gt            #0xac4290
    // 0xac3950: r0 = BoxInt64Instr(r2)
    //     0xac3950: sbfiz           x0, x2, #1, #0x1f
    //     0xac3954: cmp             x2, x0, asr #1
    //     0xac3958: b.eq            #0xac3964
    //     0xac395c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xac3960: stur            x2, [x0, #7]
    // 0xac3964: r1 = _Int32List
    //     0xac3964: add             x1, PP, #0x26, lsl #12  ; [pp+0x26170] _Int32List(20) [0xcb8, 0x3a8, 0x430, 0x438, 0x51c, 0x528, 0x534, 0x544, 0x554, 0x560, 0x56c, 0x574, 0x658, 0x660, 0x668, 0x670, 0x730, 0x738, 0x938, 0x940]
    //     0xac3968: ldr             x1, [x1, #0x170]
    // 0xac396c: ArrayLoad: r1 = r1[r0]  ; TypedSigned_4
    //     0xac396c: add             x16, x1, w0, sxtw #1
    //     0xac3970: ldursw          x1, [x16, #0x17]
    // 0xac3974: adr             x2, #0xac35d8
    // 0xac3978: add             x2, x2, x1
    // 0xac397c: br              x2
    // 0xac3980: ldur            x4, [fp, #-0x18]
    // 0xac3984: r0 = LoadClassIdInstr(r4)
    //     0xac3984: ldur            x0, [x4, #-1]
    //     0xac3988: ubfx            x0, x0, #0xc, #0x14
    // 0xac398c: cmp             x0, #0x3a6
    // 0xac3990: b.eq            #0xac4188
    // 0xac3994: cmp             x0, #0x3a7
    // 0xac3998: b.ne            #0xac39ac
    // 0xac399c: LoadField: r1 = r4->field_7
    //     0xac399c: ldur            w1, [x4, #7]
    // 0xac39a0: DecompressPointer r1
    //     0xac39a0: add             x1, x1, HEAP, lsl #32
    // 0xac39a4: r0 = closePath()
    //     0xac39a4: bl              #0xac6bc8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::closePath
    // 0xac39a8: b               #0xac4188
    // 0xac39ac: LoadField: r0 = r4->field_7
    //     0xac39ac: ldur            w0, [x4, #7]
    // 0xac39b0: DecompressPointer r0
    //     0xac39b0: add             x0, x0, HEAP, lsl #32
    // 0xac39b4: stur            x0, [fp, #-0x28]
    // 0xac39b8: LoadField: r1 = r0->field_7
    //     0xac39b8: ldur            w1, [x0, #7]
    // 0xac39bc: DecompressPointer r1
    //     0xac39bc: add             x1, x1, HEAP, lsl #32
    // 0xac39c0: cmp             w1, NULL
    // 0xac39c4: b.eq            #0xac42b8
    // 0xac39c8: LoadField: r2 = r1->field_7
    //     0xac39c8: ldur            x2, [x1, #7]
    // 0xac39cc: ldr             x1, [x2]
    // 0xac39d0: stur            x1, [fp, #-0x20]
    // 0xac39d4: cbnz            x1, #0xac39e4
    // 0xac39d8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac39d8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac39dc: str             x16, [SP]
    // 0xac39e0: r0 = _throwNew()
    //     0xac39e0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac39e4: ldur            x0, [fp, #-0x20]
    // 0xac39e8: stur            x0, [fp, #-0x20]
    // 0xac39ec: r1 = <Never>
    //     0xac39ec: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac39f0: r0 = Pointer()
    //     0xac39f0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac39f4: mov             x1, x0
    // 0xac39f8: ldur            x0, [fp, #-0x20]
    // 0xac39fc: StoreField: r1->field_7 = r0
    //     0xac39fc: stur            x0, [x1, #7]
    // 0xac3a00: r0 = _close$Method$FfiNative()
    //     0xac3a00: bl              #0x7948f4  ; [dart:ui] _NativePath::_close$Method$FfiNative
    // 0xac3a04: b               #0xac4188
    // 0xac3a08: ldur            x4, [fp, #-0x18]
    // 0xac3a0c: b               #0xac3a14
    // 0xac3a10: ldur            x4, [fp, #-0x18]
    // 0xac3a14: ldur            x2, [fp, #-8]
    // 0xac3a18: ldur            x3, [fp, #-0x10]
    // 0xac3a1c: LoadField: r1 = r3->field_b
    //     0xac3a1c: ldur            w1, [x3, #0xb]
    // 0xac3a20: DecompressPointer r1
    //     0xac3a20: add             x1, x1, HEAP, lsl #32
    // 0xac3a24: mov             x0, x1
    // 0xac3a28: StoreField: r2->field_b = r0
    //     0xac3a28: stur            w0, [x2, #0xb]
    //     0xac3a2c: ldurb           w16, [x2, #-1]
    //     0xac3a30: ldurb           w17, [x0, #-1]
    //     0xac3a34: and             x16, x17, x16, lsr #2
    //     0xac3a38: tst             x16, HEAP, lsr #32
    //     0xac3a3c: b.eq            #0xac3a44
    //     0xac3a40: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac3a44: LoadField: d0 = r1->field_7
    //     0xac3a44: ldur            d0, [x1, #7]
    // 0xac3a48: stur            d0, [fp, #-0x38]
    // 0xac3a4c: LoadField: d1 = r1->field_f
    //     0xac3a4c: ldur            d1, [x1, #0xf]
    // 0xac3a50: stur            d1, [fp, #-0x30]
    // 0xac3a54: r0 = LoadClassIdInstr(r4)
    //     0xac3a54: ldur            x0, [x4, #-1]
    //     0xac3a58: ubfx            x0, x0, #0xc, #0x14
    // 0xac3a5c: cmp             x0, #0x3a6
    // 0xac3a60: b.ne            #0xac3a78
    // 0xac3a64: StoreField: r4->field_27 = d0
    //     0xac3a64: stur            d0, [x4, #0x27]
    // 0xac3a68: StoreField: r4->field_2f = d1
    //     0xac3a68: stur            d1, [x4, #0x2f]
    // 0xac3a6c: mov             x1, x4
    // 0xac3a70: r0 = _updateMinMax()
    //     0xac3a70: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xac3a74: b               #0xac4188
    // 0xac3a78: cmp             x0, #0x3a7
    // 0xac3a7c: b.ne            #0xac3a90
    // 0xac3a80: LoadField: r1 = r4->field_7
    //     0xac3a80: ldur            w1, [x4, #7]
    // 0xac3a84: DecompressPointer r1
    //     0xac3a84: add             x1, x1, HEAP, lsl #32
    // 0xac3a88: r0 = moveTo()
    //     0xac3a88: bl              #0xac6914  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::moveTo
    // 0xac3a8c: b               #0xac4188
    // 0xac3a90: LoadField: r0 = r4->field_7
    //     0xac3a90: ldur            w0, [x4, #7]
    // 0xac3a94: DecompressPointer r0
    //     0xac3a94: add             x0, x0, HEAP, lsl #32
    // 0xac3a98: stur            x0, [fp, #-0x28]
    // 0xac3a9c: LoadField: r1 = r0->field_7
    //     0xac3a9c: ldur            w1, [x0, #7]
    // 0xac3aa0: DecompressPointer r1
    //     0xac3aa0: add             x1, x1, HEAP, lsl #32
    // 0xac3aa4: cmp             w1, NULL
    // 0xac3aa8: b.eq            #0xac42bc
    // 0xac3aac: LoadField: r2 = r1->field_7
    //     0xac3aac: ldur            x2, [x1, #7]
    // 0xac3ab0: ldr             x1, [x2]
    // 0xac3ab4: stur            x1, [fp, #-0x20]
    // 0xac3ab8: cbnz            x1, #0xac3ac8
    // 0xac3abc: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac3abc: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac3ac0: str             x16, [SP]
    // 0xac3ac4: r0 = _throwNew()
    //     0xac3ac4: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac3ac8: ldur            x0, [fp, #-0x20]
    // 0xac3acc: stur            x0, [fp, #-0x20]
    // 0xac3ad0: r1 = <Never>
    //     0xac3ad0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac3ad4: r0 = Pointer()
    //     0xac3ad4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac3ad8: mov             x1, x0
    // 0xac3adc: ldur            x0, [fp, #-0x20]
    // 0xac3ae0: StoreField: r1->field_7 = r0
    //     0xac3ae0: stur            x0, [x1, #7]
    // 0xac3ae4: ldur            d0, [fp, #-0x38]
    // 0xac3ae8: ldur            d1, [fp, #-0x30]
    // 0xac3aec: r0 = _moveTo$Method$FfiNative()
    //     0xac3aec: bl              #0x795144  ; [dart:ui] _NativePath::_moveTo$Method$FfiNative
    // 0xac3af0: b               #0xac4188
    // 0xac3af4: ldur            x4, [fp, #-0x18]
    // 0xac3af8: mov             x3, x4
    // 0xac3afc: b               #0xac3c4c
    // 0xac3b00: ldur            x4, [fp, #-0x18]
    // 0xac3b04: mov             x3, x4
    // 0xac3b08: b               #0xac3c4c
    // 0xac3b0c: ldur            x4, [fp, #-0x18]
    // 0xac3b10: ldur            x2, [fp, #-0x10]
    // 0xac3b14: mov             x3, x4
    // 0xac3b18: b               #0xac3dd4
    // 0xac3b1c: ldur            x4, [fp, #-0x18]
    // 0xac3b20: ldur            x2, [fp, #-0x10]
    // 0xac3b24: mov             x3, x4
    // 0xac3b28: b               #0xac3dd4
    // 0xac3b2c: ldur            x4, [fp, #-0x18]
    // 0xac3b30: ldur            x3, [fp, #-0x10]
    // 0xac3b34: b               #0xac3fdc
    // 0xac3b38: ldur            x4, [fp, #-0x18]
    // 0xac3b3c: ldur            x3, [fp, #-0x10]
    // 0xac3b40: b               #0xac3fdc
    // 0xac3b44: ldur            x4, [fp, #-0x18]
    // 0xac3b48: b               #0xac3b50
    // 0xac3b4c: ldur            x4, [fp, #-0x18]
    // 0xac3b50: ldur            x0, [fp, #-8]
    // 0xac3b54: LoadField: r2 = r0->field_7
    //     0xac3b54: ldur            w2, [x0, #7]
    // 0xac3b58: DecompressPointer r2
    //     0xac3b58: add             x2, x2, HEAP, lsl #32
    // 0xac3b5c: mov             x1, x0
    // 0xac3b60: ldur            x3, [fp, #-0x10]
    // 0xac3b64: mov             x5, x4
    // 0xac3b68: r0 = _decomposeArcToCubic()
    //     0xac3b68: bl              #0xac4834  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_decomposeArcToCubic
    // 0xac3b6c: tbz             w0, #4, #0xac4188
    // 0xac3b70: ldur            x0, [fp, #-0x10]
    // 0xac3b74: ldur            x3, [fp, #-0x18]
    // 0xac3b78: LoadField: r1 = r0->field_b
    //     0xac3b78: ldur            w1, [x0, #0xb]
    // 0xac3b7c: DecompressPointer r1
    //     0xac3b7c: add             x1, x1, HEAP, lsl #32
    // 0xac3b80: LoadField: d0 = r1->field_7
    //     0xac3b80: ldur            d0, [x1, #7]
    // 0xac3b84: stur            d0, [fp, #-0x38]
    // 0xac3b88: LoadField: d1 = r1->field_f
    //     0xac3b88: ldur            d1, [x1, #0xf]
    // 0xac3b8c: stur            d1, [fp, #-0x30]
    // 0xac3b90: r1 = LoadClassIdInstr(r3)
    //     0xac3b90: ldur            x1, [x3, #-1]
    //     0xac3b94: ubfx            x1, x1, #0xc, #0x14
    // 0xac3b98: cmp             x1, #0x3a6
    // 0xac3b9c: b.ne            #0xac3bb4
    // 0xac3ba0: StoreField: r3->field_27 = d0
    //     0xac3ba0: stur            d0, [x3, #0x27]
    // 0xac3ba4: StoreField: r3->field_2f = d1
    //     0xac3ba4: stur            d1, [x3, #0x2f]
    // 0xac3ba8: mov             x1, x3
    // 0xac3bac: r0 = _updateMinMax()
    //     0xac3bac: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xac3bb0: b               #0xac4188
    // 0xac3bb4: cmp             x1, #0x3a7
    // 0xac3bb8: b.ne            #0xac3bcc
    // 0xac3bbc: LoadField: r1 = r3->field_7
    //     0xac3bbc: ldur            w1, [x3, #7]
    // 0xac3bc0: DecompressPointer r1
    //     0xac3bc0: add             x1, x1, HEAP, lsl #32
    // 0xac3bc4: r0 = lineTo()
    //     0xac3bc4: bl              #0xac46e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::lineTo
    // 0xac3bc8: b               #0xac4188
    // 0xac3bcc: LoadField: r0 = r3->field_7
    //     0xac3bcc: ldur            w0, [x3, #7]
    // 0xac3bd0: DecompressPointer r0
    //     0xac3bd0: add             x0, x0, HEAP, lsl #32
    // 0xac3bd4: stur            x0, [fp, #-0x28]
    // 0xac3bd8: LoadField: r1 = r0->field_7
    //     0xac3bd8: ldur            w1, [x0, #7]
    // 0xac3bdc: DecompressPointer r1
    //     0xac3bdc: add             x1, x1, HEAP, lsl #32
    // 0xac3be0: cmp             w1, NULL
    // 0xac3be4: b.eq            #0xac42c0
    // 0xac3be8: LoadField: r2 = r1->field_7
    //     0xac3be8: ldur            x2, [x1, #7]
    // 0xac3bec: ldr             x1, [x2]
    // 0xac3bf0: stur            x1, [fp, #-0x20]
    // 0xac3bf4: cbnz            x1, #0xac3c04
    // 0xac3bf8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac3bf8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac3bfc: str             x16, [SP]
    // 0xac3c00: r0 = _throwNew()
    //     0xac3c00: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac3c04: ldur            x0, [fp, #-0x20]
    // 0xac3c08: stur            x0, [fp, #-0x20]
    // 0xac3c0c: r1 = <Never>
    //     0xac3c0c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac3c10: r0 = Pointer()
    //     0xac3c10: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac3c14: mov             x1, x0
    // 0xac3c18: ldur            x0, [fp, #-0x20]
    // 0xac3c1c: StoreField: r1->field_7 = r0
    //     0xac3c1c: stur            x0, [x1, #7]
    // 0xac3c20: ldur            d0, [fp, #-0x38]
    // 0xac3c24: ldur            d1, [fp, #-0x30]
    // 0xac3c28: r0 = _lineTo$Method$FfiNative()
    //     0xac3c28: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0xac3c2c: b               #0xac4188
    // 0xac3c30: ldur            x3, [fp, #-0x18]
    // 0xac3c34: b               #0xac3c4c
    // 0xac3c38: ldur            x3, [fp, #-0x18]
    // 0xac3c3c: b               #0xac3c4c
    // 0xac3c40: ldur            x3, [fp, #-0x18]
    // 0xac3c44: b               #0xac3c4c
    // 0xac3c48: ldur            x3, [fp, #-0x18]
    // 0xac3c4c: ldur            x0, [fp, #-0x10]
    // 0xac3c50: LoadField: r1 = r0->field_b
    //     0xac3c50: ldur            w1, [x0, #0xb]
    // 0xac3c54: DecompressPointer r1
    //     0xac3c54: add             x1, x1, HEAP, lsl #32
    // 0xac3c58: LoadField: d0 = r1->field_7
    //     0xac3c58: ldur            d0, [x1, #7]
    // 0xac3c5c: stur            d0, [fp, #-0x38]
    // 0xac3c60: LoadField: d1 = r1->field_f
    //     0xac3c60: ldur            d1, [x1, #0xf]
    // 0xac3c64: stur            d1, [fp, #-0x30]
    // 0xac3c68: r1 = LoadClassIdInstr(r3)
    //     0xac3c68: ldur            x1, [x3, #-1]
    //     0xac3c6c: ubfx            x1, x1, #0xc, #0x14
    // 0xac3c70: cmp             x1, #0x3a6
    // 0xac3c74: b.ne            #0xac3c8c
    // 0xac3c78: StoreField: r3->field_27 = d0
    //     0xac3c78: stur            d0, [x3, #0x27]
    // 0xac3c7c: StoreField: r3->field_2f = d1
    //     0xac3c7c: stur            d1, [x3, #0x2f]
    // 0xac3c80: mov             x1, x3
    // 0xac3c84: r0 = _updateMinMax()
    //     0xac3c84: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xac3c88: b               #0xac4188
    // 0xac3c8c: cmp             x1, #0x3a7
    // 0xac3c90: b.ne            #0xac3ca4
    // 0xac3c94: LoadField: r1 = r3->field_7
    //     0xac3c94: ldur            w1, [x3, #7]
    // 0xac3c98: DecompressPointer r1
    //     0xac3c98: add             x1, x1, HEAP, lsl #32
    // 0xac3c9c: r0 = lineTo()
    //     0xac3c9c: bl              #0xac46e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::lineTo
    // 0xac3ca0: b               #0xac4188
    // 0xac3ca4: LoadField: r0 = r3->field_7
    //     0xac3ca4: ldur            w0, [x3, #7]
    // 0xac3ca8: DecompressPointer r0
    //     0xac3ca8: add             x0, x0, HEAP, lsl #32
    // 0xac3cac: stur            x0, [fp, #-0x28]
    // 0xac3cb0: LoadField: r1 = r0->field_7
    //     0xac3cb0: ldur            w1, [x0, #7]
    // 0xac3cb4: DecompressPointer r1
    //     0xac3cb4: add             x1, x1, HEAP, lsl #32
    // 0xac3cb8: cmp             w1, NULL
    // 0xac3cbc: b.eq            #0xac42c4
    // 0xac3cc0: LoadField: r2 = r1->field_7
    //     0xac3cc0: ldur            x2, [x1, #7]
    // 0xac3cc4: ldr             x1, [x2]
    // 0xac3cc8: stur            x1, [fp, #-0x20]
    // 0xac3ccc: cbnz            x1, #0xac3cdc
    // 0xac3cd0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac3cd0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac3cd4: str             x16, [SP]
    // 0xac3cd8: r0 = _throwNew()
    //     0xac3cd8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac3cdc: ldur            x0, [fp, #-0x20]
    // 0xac3ce0: stur            x0, [fp, #-0x20]
    // 0xac3ce4: r1 = <Never>
    //     0xac3ce4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac3ce8: r0 = Pointer()
    //     0xac3ce8: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac3cec: mov             x1, x0
    // 0xac3cf0: ldur            x0, [fp, #-0x20]
    // 0xac3cf4: StoreField: r1->field_7 = r0
    //     0xac3cf4: stur            x0, [x1, #7]
    // 0xac3cf8: ldur            d0, [fp, #-0x38]
    // 0xac3cfc: ldur            d1, [fp, #-0x30]
    // 0xac3d00: r0 = _lineTo$Method$FfiNative()
    //     0xac3d00: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0xac3d04: b               #0xac4188
    // 0xac3d08: ldur            x3, [fp, #-0x18]
    // 0xac3d0c: b               #0xac3d14
    // 0xac3d10: ldur            x3, [fp, #-0x18]
    // 0xac3d14: ldur            x4, [fp, #-8]
    // 0xac3d18: LoadField: r0 = r4->field_13
    //     0xac3d18: ldur            w0, [x4, #0x13]
    // 0xac3d1c: DecompressPointer r0
    //     0xac3d1c: add             x0, x0, HEAP, lsl #32
    // 0xac3d20: r16 = Instance_SvgPathSegType
    //     0xac3d20: add             x16, PP, #0x26, lsl #12  ; [pp+0x26178] Obj!SvgPathSegType@e2fe21
    //     0xac3d24: ldr             x16, [x16, #0x178]
    // 0xac3d28: cmp             w0, w16
    // 0xac3d2c: b.eq            #0xac3d50
    // 0xac3d30: r16 = Instance_SvgPathSegType
    //     0xac3d30: add             x16, PP, #0x26, lsl #12  ; [pp+0x26180] Obj!SvgPathSegType@e2fe01
    //     0xac3d34: ldr             x16, [x16, #0x180]
    // 0xac3d38: cmp             w0, w16
    // 0xac3d3c: b.eq            #0xac3d50
    // 0xac3d40: r16 = Instance_SvgPathSegType
    //     0xac3d40: add             x16, PP, #0x26, lsl #12  ; [pp+0x26188] Obj!SvgPathSegType@e2fde1
    //     0xac3d44: ldr             x16, [x16, #0x188]
    // 0xac3d48: cmp             w0, w16
    // 0xac3d4c: b.ne            #0xac3d58
    // 0xac3d50: ldur            x5, [fp, #-0x10]
    // 0xac3d54: b               #0xac3d9c
    // 0xac3d58: r16 = Instance_SvgPathSegType
    //     0xac3d58: add             x16, PP, #0x26, lsl #12  ; [pp+0x26190] Obj!SvgPathSegType@e2fdc1
    //     0xac3d5c: ldr             x16, [x16, #0x190]
    // 0xac3d60: cmp             w0, w16
    // 0xac3d64: b.eq            #0xac3d98
    // 0xac3d68: ldur            x5, [fp, #-0x10]
    // 0xac3d6c: LoadField: r0 = r4->field_7
    //     0xac3d6c: ldur            w0, [x4, #7]
    // 0xac3d70: DecompressPointer r0
    //     0xac3d70: add             x0, x0, HEAP, lsl #32
    // 0xac3d74: StoreField: r5->field_f = r0
    //     0xac3d74: stur            w0, [x5, #0xf]
    //     0xac3d78: ldurb           w16, [x5, #-1]
    //     0xac3d7c: ldurb           w17, [x0, #-1]
    //     0xac3d80: and             x16, x17, x16, lsr #2
    //     0xac3d84: tst             x16, HEAP, lsr #32
    //     0xac3d88: b.eq            #0xac3d90
    //     0xac3d8c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xac3d90: mov             x2, x5
    // 0xac3d94: b               #0xac3dd0
    // 0xac3d98: ldur            x5, [fp, #-0x10]
    // 0xac3d9c: LoadField: r1 = r4->field_7
    //     0xac3d9c: ldur            w1, [x4, #7]
    // 0xac3da0: DecompressPointer r1
    //     0xac3da0: add             x1, x1, HEAP, lsl #32
    // 0xac3da4: LoadField: r2 = r4->field_f
    //     0xac3da4: ldur            w2, [x4, #0xf]
    // 0xac3da8: DecompressPointer r2
    //     0xac3da8: add             x2, x2, HEAP, lsl #32
    // 0xac3dac: r0 = reflectedPoint()
    //     0xac3dac: bl              #0xac4690  ; [package:path_parsing/src/path_parsing.dart] ::reflectedPoint
    // 0xac3db0: ldur            x2, [fp, #-0x10]
    // 0xac3db4: StoreField: r2->field_f = r0
    //     0xac3db4: stur            w0, [x2, #0xf]
    //     0xac3db8: ldurb           w16, [x2, #-1]
    //     0xac3dbc: ldurb           w17, [x0, #-1]
    //     0xac3dc0: and             x16, x17, x16, lsr #2
    //     0xac3dc4: tst             x16, HEAP, lsr #32
    //     0xac3dc8: b.eq            #0xac3dd0
    //     0xac3dcc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac3dd0: ldur            x3, [fp, #-0x18]
    // 0xac3dd4: ldur            x4, [fp, #-8]
    // 0xac3dd8: LoadField: r1 = r2->field_13
    //     0xac3dd8: ldur            w1, [x2, #0x13]
    // 0xac3ddc: DecompressPointer r1
    //     0xac3ddc: add             x1, x1, HEAP, lsl #32
    // 0xac3de0: mov             x0, x1
    // 0xac3de4: StoreField: r4->field_f = r0
    //     0xac3de4: stur            w0, [x4, #0xf]
    //     0xac3de8: ldurb           w16, [x4, #-1]
    //     0xac3dec: ldurb           w17, [x0, #-1]
    //     0xac3df0: and             x16, x17, x16, lsr #2
    //     0xac3df4: tst             x16, HEAP, lsr #32
    //     0xac3df8: b.eq            #0xac3e00
    //     0xac3dfc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xac3e00: LoadField: r0 = r2->field_f
    //     0xac3e00: ldur            w0, [x2, #0xf]
    // 0xac3e04: DecompressPointer r0
    //     0xac3e04: add             x0, x0, HEAP, lsl #32
    // 0xac3e08: LoadField: d0 = r0->field_7
    //     0xac3e08: ldur            d0, [x0, #7]
    // 0xac3e0c: stur            d0, [fp, #-0x58]
    // 0xac3e10: LoadField: d1 = r0->field_f
    //     0xac3e10: ldur            d1, [x0, #0xf]
    // 0xac3e14: stur            d1, [fp, #-0x50]
    // 0xac3e18: LoadField: d2 = r1->field_7
    //     0xac3e18: ldur            d2, [x1, #7]
    // 0xac3e1c: stur            d2, [fp, #-0x48]
    // 0xac3e20: LoadField: d3 = r1->field_f
    //     0xac3e20: ldur            d3, [x1, #0xf]
    // 0xac3e24: stur            d3, [fp, #-0x40]
    // 0xac3e28: LoadField: r0 = r2->field_b
    //     0xac3e28: ldur            w0, [x2, #0xb]
    // 0xac3e2c: DecompressPointer r0
    //     0xac3e2c: add             x0, x0, HEAP, lsl #32
    // 0xac3e30: LoadField: d4 = r0->field_7
    //     0xac3e30: ldur            d4, [x0, #7]
    // 0xac3e34: stur            d4, [fp, #-0x38]
    // 0xac3e38: LoadField: d5 = r0->field_f
    //     0xac3e38: ldur            d5, [x0, #0xf]
    // 0xac3e3c: stur            d5, [fp, #-0x30]
    // 0xac3e40: r0 = LoadClassIdInstr(r3)
    //     0xac3e40: ldur            x0, [x3, #-1]
    //     0xac3e44: ubfx            x0, x0, #0xc, #0x14
    // 0xac3e48: cmp             x0, #0x3a7
    // 0xac3e4c: b.ne            #0xac3e60
    // 0xac3e50: LoadField: r1 = r3->field_7
    //     0xac3e50: ldur            w1, [x3, #7]
    // 0xac3e54: DecompressPointer r1
    //     0xac3e54: add             x1, x1, HEAP, lsl #32
    // 0xac3e58: r0 = curveTo()
    //     0xac3e58: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xac3e5c: b               #0xac4188
    // 0xac3e60: cmp             x0, #0x3a8
    // 0xac3e64: b.ne            #0xac3edc
    // 0xac3e68: LoadField: r0 = r3->field_7
    //     0xac3e68: ldur            w0, [x3, #7]
    // 0xac3e6c: DecompressPointer r0
    //     0xac3e6c: add             x0, x0, HEAP, lsl #32
    // 0xac3e70: stur            x0, [fp, #-0x28]
    // 0xac3e74: LoadField: r1 = r0->field_7
    //     0xac3e74: ldur            w1, [x0, #7]
    // 0xac3e78: DecompressPointer r1
    //     0xac3e78: add             x1, x1, HEAP, lsl #32
    // 0xac3e7c: cmp             w1, NULL
    // 0xac3e80: b.eq            #0xac42c8
    // 0xac3e84: LoadField: r2 = r1->field_7
    //     0xac3e84: ldur            x2, [x1, #7]
    // 0xac3e88: ldr             x1, [x2]
    // 0xac3e8c: stur            x1, [fp, #-0x20]
    // 0xac3e90: cbnz            x1, #0xac3ea0
    // 0xac3e94: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac3e94: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac3e98: str             x16, [SP]
    // 0xac3e9c: r0 = _throwNew()
    //     0xac3e9c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac3ea0: ldur            x0, [fp, #-0x20]
    // 0xac3ea4: stur            x0, [fp, #-0x20]
    // 0xac3ea8: r1 = <Never>
    //     0xac3ea8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac3eac: r0 = Pointer()
    //     0xac3eac: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac3eb0: mov             x1, x0
    // 0xac3eb4: ldur            x0, [fp, #-0x20]
    // 0xac3eb8: StoreField: r1->field_7 = r0
    //     0xac3eb8: stur            x0, [x1, #7]
    // 0xac3ebc: ldur            d0, [fp, #-0x58]
    // 0xac3ec0: ldur            d1, [fp, #-0x50]
    // 0xac3ec4: ldur            d2, [fp, #-0x48]
    // 0xac3ec8: ldur            d3, [fp, #-0x40]
    // 0xac3ecc: ldur            d4, [fp, #-0x38]
    // 0xac3ed0: ldur            d5, [fp, #-0x30]
    // 0xac3ed4: r0 = _cubicTo$Method$FfiNative()
    //     0xac3ed4: bl              #0xac4334  ; [dart:ui] _NativePath::_cubicTo$Method$FfiNative
    // 0xac3ed8: b               #0xac4188
    // 0xac3edc: r0 = LoadClassIdInstr(r3)
    //     0xac3edc: ldur            x0, [x3, #-1]
    //     0xac3ee0: ubfx            x0, x0, #0xc, #0x14
    // 0xac3ee4: mov             x1, x3
    // 0xac3ee8: ldur            d0, [fp, #-0x58]
    // 0xac3eec: ldur            d1, [fp, #-0x50]
    // 0xac3ef0: ldur            d2, [fp, #-0x48]
    // 0xac3ef4: ldur            d3, [fp, #-0x40]
    // 0xac3ef8: ldur            d4, [fp, #-0x38]
    // 0xac3efc: ldur            d5, [fp, #-0x30]
    // 0xac3f00: r0 = GDT[cid_x0 + -0x1000]()
    //     0xac3f00: sub             lr, x0, #1, lsl #12
    //     0xac3f04: ldr             lr, [x21, lr, lsl #3]
    //     0xac3f08: blr             lr
    // 0xac3f0c: b               #0xac4188
    // 0xac3f10: ldur            x3, [fp, #-0x18]
    // 0xac3f14: b               #0xac3f1c
    // 0xac3f18: ldur            x3, [fp, #-0x18]
    // 0xac3f1c: ldur            x4, [fp, #-8]
    // 0xac3f20: LoadField: r0 = r4->field_13
    //     0xac3f20: ldur            w0, [x4, #0x13]
    // 0xac3f24: DecompressPointer r0
    //     0xac3f24: add             x0, x0, HEAP, lsl #32
    // 0xac3f28: r16 = Instance_SvgPathSegType
    //     0xac3f28: add             x16, PP, #0x26, lsl #12  ; [pp+0x26198] Obj!SvgPathSegType@e2fda1
    //     0xac3f2c: ldr             x16, [x16, #0x198]
    // 0xac3f30: cmp             w0, w16
    // 0xac3f34: b.eq            #0xac3f58
    // 0xac3f38: r16 = Instance_SvgPathSegType
    //     0xac3f38: add             x16, PP, #0x26, lsl #12  ; [pp+0x261a0] Obj!SvgPathSegType@e2fd81
    //     0xac3f3c: ldr             x16, [x16, #0x1a0]
    // 0xac3f40: cmp             w0, w16
    // 0xac3f44: b.eq            #0xac3f58
    // 0xac3f48: r16 = Instance_SvgPathSegType
    //     0xac3f48: add             x16, PP, #0x26, lsl #12  ; [pp+0x261a8] Obj!SvgPathSegType@e2fd61
    //     0xac3f4c: ldr             x16, [x16, #0x1a8]
    // 0xac3f50: cmp             w0, w16
    // 0xac3f54: b.ne            #0xac3f60
    // 0xac3f58: ldur            x5, [fp, #-0x10]
    // 0xac3f5c: b               #0xac3fa4
    // 0xac3f60: r16 = Instance_SvgPathSegType
    //     0xac3f60: add             x16, PP, #0x26, lsl #12  ; [pp+0x261b0] Obj!SvgPathSegType@e2fd41
    //     0xac3f64: ldr             x16, [x16, #0x1b0]
    // 0xac3f68: cmp             w0, w16
    // 0xac3f6c: b.eq            #0xac3fa0
    // 0xac3f70: ldur            x5, [fp, #-0x10]
    // 0xac3f74: LoadField: r0 = r4->field_7
    //     0xac3f74: ldur            w0, [x4, #7]
    // 0xac3f78: DecompressPointer r0
    //     0xac3f78: add             x0, x0, HEAP, lsl #32
    // 0xac3f7c: StoreField: r5->field_f = r0
    //     0xac3f7c: stur            w0, [x5, #0xf]
    //     0xac3f80: ldurb           w16, [x5, #-1]
    //     0xac3f84: ldurb           w17, [x0, #-1]
    //     0xac3f88: and             x16, x17, x16, lsr #2
    //     0xac3f8c: tst             x16, HEAP, lsr #32
    //     0xac3f90: b.eq            #0xac3f98
    //     0xac3f94: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xac3f98: mov             x3, x5
    // 0xac3f9c: b               #0xac3fd8
    // 0xac3fa0: ldur            x5, [fp, #-0x10]
    // 0xac3fa4: LoadField: r1 = r4->field_7
    //     0xac3fa4: ldur            w1, [x4, #7]
    // 0xac3fa8: DecompressPointer r1
    //     0xac3fa8: add             x1, x1, HEAP, lsl #32
    // 0xac3fac: LoadField: r2 = r4->field_f
    //     0xac3fac: ldur            w2, [x4, #0xf]
    // 0xac3fb0: DecompressPointer r2
    //     0xac3fb0: add             x2, x2, HEAP, lsl #32
    // 0xac3fb4: r0 = reflectedPoint()
    //     0xac3fb4: bl              #0xac4690  ; [package:path_parsing/src/path_parsing.dart] ::reflectedPoint
    // 0xac3fb8: ldur            x3, [fp, #-0x10]
    // 0xac3fbc: StoreField: r3->field_f = r0
    //     0xac3fbc: stur            w0, [x3, #0xf]
    //     0xac3fc0: ldurb           w16, [x3, #-1]
    //     0xac3fc4: ldurb           w17, [x0, #-1]
    //     0xac3fc8: and             x16, x17, x16, lsr #2
    //     0xac3fcc: tst             x16, HEAP, lsr #32
    //     0xac3fd0: b.eq            #0xac3fd8
    //     0xac3fd4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac3fd8: ldur            x4, [fp, #-0x18]
    // 0xac3fdc: ldur            x5, [fp, #-8]
    // 0xac3fe0: LoadField: r1 = r3->field_f
    //     0xac3fe0: ldur            w1, [x3, #0xf]
    // 0xac3fe4: DecompressPointer r1
    //     0xac3fe4: add             x1, x1, HEAP, lsl #32
    // 0xac3fe8: mov             x0, x1
    // 0xac3fec: StoreField: r5->field_f = r0
    //     0xac3fec: stur            w0, [x5, #0xf]
    //     0xac3ff0: ldurb           w16, [x5, #-1]
    //     0xac3ff4: ldurb           w17, [x0, #-1]
    //     0xac3ff8: and             x16, x17, x16, lsr #2
    //     0xac3ffc: tst             x16, HEAP, lsr #32
    //     0xac4000: b.eq            #0xac4008
    //     0xac4004: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xac4008: LoadField: r0 = r5->field_7
    //     0xac4008: ldur            w0, [x5, #7]
    // 0xac400c: DecompressPointer r0
    //     0xac400c: add             x0, x0, HEAP, lsl #32
    // 0xac4010: mov             x2, x1
    // 0xac4014: mov             x1, x0
    // 0xac4018: r0 = blendPoints()
    //     0xac4018: bl              #0xac42d0  ; [package:path_parsing/src/path_parsing.dart] ::blendPoints
    // 0xac401c: ldur            x3, [fp, #-0x10]
    // 0xac4020: StoreField: r3->field_f = r0
    //     0xac4020: stur            w0, [x3, #0xf]
    //     0xac4024: ldurb           w16, [x3, #-1]
    //     0xac4028: ldurb           w17, [x0, #-1]
    //     0xac402c: and             x16, x17, x16, lsr #2
    //     0xac4030: tst             x16, HEAP, lsr #32
    //     0xac4034: b.eq            #0xac403c
    //     0xac4038: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac403c: LoadField: r1 = r3->field_b
    //     0xac403c: ldur            w1, [x3, #0xb]
    // 0xac4040: DecompressPointer r1
    //     0xac4040: add             x1, x1, HEAP, lsl #32
    // 0xac4044: ldur            x0, [fp, #-8]
    // 0xac4048: LoadField: r2 = r0->field_f
    //     0xac4048: ldur            w2, [x0, #0xf]
    // 0xac404c: DecompressPointer r2
    //     0xac404c: add             x2, x2, HEAP, lsl #32
    // 0xac4050: r0 = blendPoints()
    //     0xac4050: bl              #0xac42d0  ; [package:path_parsing/src/path_parsing.dart] ::blendPoints
    // 0xac4054: mov             x1, x0
    // 0xac4058: ldur            x2, [fp, #-0x10]
    // 0xac405c: StoreField: r2->field_13 = r0
    //     0xac405c: stur            w0, [x2, #0x13]
    //     0xac4060: ldurb           w16, [x2, #-1]
    //     0xac4064: ldurb           w17, [x0, #-1]
    //     0xac4068: and             x16, x17, x16, lsr #2
    //     0xac406c: tst             x16, HEAP, lsr #32
    //     0xac4070: b.eq            #0xac4078
    //     0xac4074: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac4078: LoadField: r0 = r2->field_f
    //     0xac4078: ldur            w0, [x2, #0xf]
    // 0xac407c: DecompressPointer r0
    //     0xac407c: add             x0, x0, HEAP, lsl #32
    // 0xac4080: LoadField: d0 = r0->field_7
    //     0xac4080: ldur            d0, [x0, #7]
    // 0xac4084: stur            d0, [fp, #-0x58]
    // 0xac4088: LoadField: d1 = r0->field_f
    //     0xac4088: ldur            d1, [x0, #0xf]
    // 0xac408c: stur            d1, [fp, #-0x50]
    // 0xac4090: LoadField: d2 = r1->field_7
    //     0xac4090: ldur            d2, [x1, #7]
    // 0xac4094: stur            d2, [fp, #-0x48]
    // 0xac4098: LoadField: d3 = r1->field_f
    //     0xac4098: ldur            d3, [x1, #0xf]
    // 0xac409c: stur            d3, [fp, #-0x40]
    // 0xac40a0: LoadField: r0 = r2->field_b
    //     0xac40a0: ldur            w0, [x2, #0xb]
    // 0xac40a4: DecompressPointer r0
    //     0xac40a4: add             x0, x0, HEAP, lsl #32
    // 0xac40a8: LoadField: d4 = r0->field_7
    //     0xac40a8: ldur            d4, [x0, #7]
    // 0xac40ac: stur            d4, [fp, #-0x38]
    // 0xac40b0: LoadField: d5 = r0->field_f
    //     0xac40b0: ldur            d5, [x0, #0xf]
    // 0xac40b4: ldur            x1, [fp, #-0x18]
    // 0xac40b8: stur            d5, [fp, #-0x30]
    // 0xac40bc: r0 = LoadClassIdInstr(r1)
    //     0xac40bc: ldur            x0, [x1, #-1]
    //     0xac40c0: ubfx            x0, x0, #0xc, #0x14
    // 0xac40c4: cmp             x0, #0x3a7
    // 0xac40c8: b.ne            #0xac40e0
    // 0xac40cc: LoadField: r0 = r1->field_7
    //     0xac40cc: ldur            w0, [x1, #7]
    // 0xac40d0: DecompressPointer r0
    //     0xac40d0: add             x0, x0, HEAP, lsl #32
    // 0xac40d4: mov             x1, x0
    // 0xac40d8: r0 = curveTo()
    //     0xac40d8: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xac40dc: b               #0xac4188
    // 0xac40e0: cmp             x0, #0x3a8
    // 0xac40e4: b.ne            #0xac415c
    // 0xac40e8: LoadField: r0 = r1->field_7
    //     0xac40e8: ldur            w0, [x1, #7]
    // 0xac40ec: DecompressPointer r0
    //     0xac40ec: add             x0, x0, HEAP, lsl #32
    // 0xac40f0: stur            x0, [fp, #-0x28]
    // 0xac40f4: LoadField: r1 = r0->field_7
    //     0xac40f4: ldur            w1, [x0, #7]
    // 0xac40f8: DecompressPointer r1
    //     0xac40f8: add             x1, x1, HEAP, lsl #32
    // 0xac40fc: cmp             w1, NULL
    // 0xac4100: b.eq            #0xac42cc
    // 0xac4104: LoadField: r2 = r1->field_7
    //     0xac4104: ldur            x2, [x1, #7]
    // 0xac4108: ldr             x1, [x2]
    // 0xac410c: stur            x1, [fp, #-0x20]
    // 0xac4110: cbnz            x1, #0xac4120
    // 0xac4114: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac4114: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac4118: str             x16, [SP]
    // 0xac411c: r0 = _throwNew()
    //     0xac411c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac4120: ldur            x0, [fp, #-0x20]
    // 0xac4124: stur            x0, [fp, #-0x20]
    // 0xac4128: r1 = <Never>
    //     0xac4128: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac412c: r0 = Pointer()
    //     0xac412c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac4130: mov             x1, x0
    // 0xac4134: ldur            x0, [fp, #-0x20]
    // 0xac4138: StoreField: r1->field_7 = r0
    //     0xac4138: stur            x0, [x1, #7]
    // 0xac413c: ldur            d0, [fp, #-0x58]
    // 0xac4140: ldur            d1, [fp, #-0x50]
    // 0xac4144: ldur            d2, [fp, #-0x48]
    // 0xac4148: ldur            d3, [fp, #-0x40]
    // 0xac414c: ldur            d4, [fp, #-0x38]
    // 0xac4150: ldur            d5, [fp, #-0x30]
    // 0xac4154: r0 = _cubicTo$Method$FfiNative()
    //     0xac4154: bl              #0xac4334  ; [dart:ui] _NativePath::_cubicTo$Method$FfiNative
    // 0xac4158: b               #0xac4188
    // 0xac415c: r0 = LoadClassIdInstr(r1)
    //     0xac415c: ldur            x0, [x1, #-1]
    //     0xac4160: ubfx            x0, x0, #0xc, #0x14
    // 0xac4164: ldur            d0, [fp, #-0x58]
    // 0xac4168: ldur            d1, [fp, #-0x50]
    // 0xac416c: ldur            d2, [fp, #-0x48]
    // 0xac4170: ldur            d3, [fp, #-0x40]
    // 0xac4174: ldur            d4, [fp, #-0x38]
    // 0xac4178: ldur            d5, [fp, #-0x30]
    // 0xac417c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xac417c: sub             lr, x0, #1, lsl #12
    //     0xac4180: ldr             lr, [x21, lr, lsl #3]
    //     0xac4184: blr             lr
    // 0xac4188: ldur            x2, [fp, #-8]
    // 0xac418c: ldur            x1, [fp, #-0x10]
    // 0xac4190: LoadField: r3 = r1->field_b
    //     0xac4190: ldur            w3, [x1, #0xb]
    // 0xac4194: DecompressPointer r3
    //     0xac4194: add             x3, x3, HEAP, lsl #32
    // 0xac4198: mov             x0, x3
    // 0xac419c: StoreField: r2->field_7 = r0
    //     0xac419c: stur            w0, [x2, #7]
    //     0xac41a0: ldurb           w16, [x2, #-1]
    //     0xac41a4: ldurb           w17, [x0, #-1]
    //     0xac41a8: and             x16, x17, x16, lsr #2
    //     0xac41ac: tst             x16, HEAP, lsr #32
    //     0xac41b0: b.eq            #0xac41b8
    //     0xac41b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac41b8: LoadField: r4 = r1->field_7
    //     0xac41b8: ldur            w4, [x1, #7]
    // 0xac41bc: DecompressPointer r4
    //     0xac41bc: add             x4, x4, HEAP, lsl #32
    // 0xac41c0: r16 = Instance_SvgPathSegType
    //     0xac41c0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26178] Obj!SvgPathSegType@e2fe21
    //     0xac41c4: ldr             x16, [x16, #0x178]
    // 0xac41c8: cmp             w4, w16
    // 0xac41cc: b.eq            #0xac4260
    // 0xac41d0: r16 = Instance_SvgPathSegType
    //     0xac41d0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26180] Obj!SvgPathSegType@e2fe01
    //     0xac41d4: ldr             x16, [x16, #0x180]
    // 0xac41d8: cmp             w4, w16
    // 0xac41dc: b.eq            #0xac4260
    // 0xac41e0: r16 = Instance_SvgPathSegType
    //     0xac41e0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26188] Obj!SvgPathSegType@e2fde1
    //     0xac41e4: ldr             x16, [x16, #0x188]
    // 0xac41e8: cmp             w4, w16
    // 0xac41ec: b.eq            #0xac4260
    // 0xac41f0: r16 = Instance_SvgPathSegType
    //     0xac41f0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26190] Obj!SvgPathSegType@e2fdc1
    //     0xac41f4: ldr             x16, [x16, #0x190]
    // 0xac41f8: cmp             w4, w16
    // 0xac41fc: b.eq            #0xac4260
    // 0xac4200: r16 = Instance_SvgPathSegType
    //     0xac4200: add             x16, PP, #0x26, lsl #12  ; [pp+0x26198] Obj!SvgPathSegType@e2fda1
    //     0xac4204: ldr             x16, [x16, #0x198]
    // 0xac4208: cmp             w4, w16
    // 0xac420c: b.eq            #0xac4260
    // 0xac4210: r16 = Instance_SvgPathSegType
    //     0xac4210: add             x16, PP, #0x26, lsl #12  ; [pp+0x261a0] Obj!SvgPathSegType@e2fd81
    //     0xac4214: ldr             x16, [x16, #0x1a0]
    // 0xac4218: cmp             w4, w16
    // 0xac421c: b.eq            #0xac4260
    // 0xac4220: r16 = Instance_SvgPathSegType
    //     0xac4220: add             x16, PP, #0x26, lsl #12  ; [pp+0x261a8] Obj!SvgPathSegType@e2fd61
    //     0xac4224: ldr             x16, [x16, #0x1a8]
    // 0xac4228: cmp             w4, w16
    // 0xac422c: b.eq            #0xac4260
    // 0xac4230: r16 = Instance_SvgPathSegType
    //     0xac4230: add             x16, PP, #0x26, lsl #12  ; [pp+0x261b0] Obj!SvgPathSegType@e2fd41
    //     0xac4234: ldr             x16, [x16, #0x1b0]
    // 0xac4238: cmp             w4, w16
    // 0xac423c: b.eq            #0xac4260
    // 0xac4240: mov             x0, x3
    // 0xac4244: StoreField: r2->field_f = r0
    //     0xac4244: stur            w0, [x2, #0xf]
    //     0xac4248: ldurb           w16, [x2, #-1]
    //     0xac424c: ldurb           w17, [x0, #-1]
    //     0xac4250: and             x16, x17, x16, lsr #2
    //     0xac4254: tst             x16, HEAP, lsr #32
    //     0xac4258: b.eq            #0xac4260
    //     0xac425c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac4260: mov             x0, x4
    // 0xac4264: StoreField: r2->field_13 = r0
    //     0xac4264: stur            w0, [x2, #0x13]
    //     0xac4268: ldurb           w16, [x2, #-1]
    //     0xac426c: ldurb           w17, [x0, #-1]
    //     0xac4270: and             x16, x17, x16, lsr #2
    //     0xac4274: tst             x16, HEAP, lsr #32
    //     0xac4278: b.eq            #0xac4280
    //     0xac427c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac4280: r0 = Null
    //     0xac4280: mov             x0, NULL
    // 0xac4284: LeaveFrame
    //     0xac4284: mov             SP, fp
    //     0xac4288: ldp             fp, lr, [SP], #0x10
    // 0xac428c: ret
    //     0xac428c: ret             
    // 0xac4290: r0 = StateError()
    //     0xac4290: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac4294: mov             x1, x0
    // 0xac4298: r0 = "Invalid command type in path"
    //     0xac4298: add             x0, PP, #0x26, lsl #12  ; [pp+0x261b8] "Invalid command type in path"
    //     0xac429c: ldr             x0, [x0, #0x1b8]
    // 0xac42a0: StoreField: r1->field_b = r0
    //     0xac42a0: stur            w0, [x1, #0xb]
    // 0xac42a4: mov             x0, x1
    // 0xac42a8: r0 = Throw()
    //     0xac42a8: bl              #0xec04b8  ; ThrowStub
    // 0xac42ac: brk             #0
    // 0xac42b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac42b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac42b4: b               #0xac3604
    // 0xac42b8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xac42b8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xac42bc: r0 = NullErrorSharedWithFPURegs()
    //     0xac42bc: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xac42c0: r0 = NullErrorSharedWithFPURegs()
    //     0xac42c0: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xac42c4: r0 = NullErrorSharedWithFPURegs()
    //     0xac42c4: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xac42c8: r0 = NullErrorSharedWithFPURegs()
    //     0xac42c8: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xac42cc: r0 = NullErrorSharedWithFPURegs()
    //     0xac42cc: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _decomposeArcToCubic(/* No info */) {
    // ** addr: 0xac4834, size: 0x9cc
    // 0xac4834: EnterFrame
    //     0xac4834: stp             fp, lr, [SP, #-0x10]!
    //     0xac4838: mov             fp, SP
    // 0xac483c: AllocStack(0xb8)
    //     0xac483c: sub             SP, SP, #0xb8
    // 0xac4840: d0 = 0.000000
    //     0xac4840: eor             v0.16b, v0.16b, v0.16b
    // 0xac4844: mov             x0, x5
    // 0xac4848: stur            x5, [fp, #-0x20]
    // 0xac484c: mov             x5, x1
    // 0xac4850: mov             x4, x2
    // 0xac4854: stur            x1, [fp, #-8]
    // 0xac4858: stur            x2, [fp, #-0x10]
    // 0xac485c: stur            x3, [fp, #-0x18]
    // 0xac4860: CheckStackOverflow
    //     0xac4860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac4864: cmp             SP, x16
    //     0xac4868: b.ls            #0xac518c
    // 0xac486c: LoadField: r1 = r3->field_f
    //     0xac486c: ldur            w1, [x3, #0xf]
    // 0xac4870: DecompressPointer r1
    //     0xac4870: add             x1, x1, HEAP, lsl #32
    // 0xac4874: LoadField: d1 = r1->field_7
    //     0xac4874: ldur            d1, [x1, #7]
    // 0xac4878: fcmp            d1, d0
    // 0xac487c: b.ne            #0xac4888
    // 0xac4880: d1 = 0.000000
    //     0xac4880: eor             v1.16b, v1.16b, v1.16b
    // 0xac4884: b               #0xac4898
    // 0xac4888: fcmp            d0, d1
    // 0xac488c: b.le            #0xac4898
    // 0xac4890: fneg            d2, d1
    // 0xac4894: mov             v1.16b, v2.16b
    // 0xac4898: stur            d1, [fp, #-0x70]
    // 0xac489c: LoadField: d2 = r1->field_f
    //     0xac489c: ldur            d2, [x1, #0xf]
    // 0xac48a0: fcmp            d2, d0
    // 0xac48a4: b.ne            #0xac48b0
    // 0xac48a8: d2 = 0.000000
    //     0xac48a8: eor             v2.16b, v2.16b, v2.16b
    // 0xac48ac: b               #0xac48c0
    // 0xac48b0: fcmp            d0, d2
    // 0xac48b4: b.le            #0xac48c0
    // 0xac48b8: fneg            d3, d2
    // 0xac48bc: mov             v2.16b, v3.16b
    // 0xac48c0: stur            d2, [fp, #-0x68]
    // 0xac48c4: fcmp            d1, d0
    // 0xac48c8: b.eq            #0xac48d4
    // 0xac48cc: fcmp            d2, d0
    // 0xac48d0: b.ne            #0xac48e4
    // 0xac48d4: r0 = false
    //     0xac48d4: add             x0, NULL, #0x30  ; false
    // 0xac48d8: LeaveFrame
    //     0xac48d8: mov             SP, fp
    //     0xac48dc: ldp             fp, lr, [SP], #0x10
    // 0xac48e0: ret
    //     0xac48e0: ret             
    // 0xac48e4: LoadField: r2 = r3->field_b
    //     0xac48e4: ldur            w2, [x3, #0xb]
    // 0xac48e8: DecompressPointer r2
    //     0xac48e8: add             x2, x2, HEAP, lsl #32
    // 0xac48ec: LoadField: d3 = r4->field_7
    //     0xac48ec: ldur            d3, [x4, #7]
    // 0xac48f0: LoadField: d4 = r2->field_7
    //     0xac48f0: ldur            d4, [x2, #7]
    // 0xac48f4: fcmp            d3, d4
    // 0xac48f8: b.ne            #0xac491c
    // 0xac48fc: LoadField: d3 = r4->field_f
    //     0xac48fc: ldur            d3, [x4, #0xf]
    // 0xac4900: LoadField: d4 = r2->field_f
    //     0xac4900: ldur            d4, [x2, #0xf]
    // 0xac4904: fcmp            d3, d4
    // 0xac4908: b.ne            #0xac491c
    // 0xac490c: r0 = false
    //     0xac490c: add             x0, NULL, #0x30  ; false
    // 0xac4910: LeaveFrame
    //     0xac4910: mov             SP, fp
    //     0xac4914: ldp             fp, lr, [SP], #0x10
    // 0xac4918: ret
    //     0xac4918: ret             
    // 0xac491c: d3 = 0.017453
    //     0xac491c: add             x17, PP, #0xb, lsl #12  ; [pp+0xb6e8] IMM: double(0.017453292519943295) from 0x3f91df46a2529d39
    //     0xac4920: ldr             d3, [x17, #0x6e8]
    // 0xac4924: LoadField: r1 = r3->field_13
    //     0xac4924: ldur            w1, [x3, #0x13]
    // 0xac4928: DecompressPointer r1
    //     0xac4928: add             x1, x1, HEAP, lsl #32
    // 0xac492c: LoadField: d4 = r1->field_7
    //     0xac492c: ldur            d4, [x1, #7]
    // 0xac4930: fmul            d5, d4, d3
    // 0xac4934: mov             x1, x4
    // 0xac4938: stur            d5, [fp, #-0x60]
    // 0xac493c: r0 = -()
    //     0xac493c: bl              #0xac68c8  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::-
    // 0xac4940: mov             x1, x0
    // 0xac4944: d0 = 0.500000
    //     0xac4944: fmov            d0, #0.50000000
    // 0xac4948: r0 = *()
    //     0xac4948: bl              #0xac6884  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::*
    // 0xac494c: r1 = Null
    //     0xac494c: mov             x1, NULL
    // 0xac4950: stur            x0, [fp, #-0x28]
    // 0xac4954: r0 = Matrix4.identity()
    //     0xac4954: bl              #0xac6830  ; [package:vector_math/vector_math.dart] Matrix4::Matrix4.identity
    // 0xac4958: ldur            d1, [fp, #-0x60]
    // 0xac495c: stur            x0, [fp, #-0x30]
    // 0xac4960: fneg            d2, d1
    // 0xac4964: mov             x1, x0
    // 0xac4968: mov             v0.16b, v2.16b
    // 0xac496c: stur            d2, [fp, #-0x78]
    // 0xac4970: r0 = rotateZ()
    //     0xac4970: bl              #0xac6634  ; [package:vector_math/vector_math.dart] Matrix4::rotateZ
    // 0xac4974: ldur            x0, [fp, #-0x28]
    // 0xac4978: LoadField: d0 = r0->field_7
    //     0xac4978: ldur            d0, [x0, #7]
    // 0xac497c: stur            d0, [fp, #-0x88]
    // 0xac4980: LoadField: d1 = r0->field_f
    //     0xac4980: ldur            d1, [x0, #0xf]
    // 0xac4984: stur            d1, [fp, #-0x80]
    // 0xac4988: r0 = _PathOffset()
    //     0xac4988: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac498c: ldur            d0, [fp, #-0x88]
    // 0xac4990: StoreField: r0->field_7 = d0
    //     0xac4990: stur            d0, [x0, #7]
    // 0xac4994: ldur            d0, [fp, #-0x80]
    // 0xac4998: StoreField: r0->field_f = d0
    //     0xac4998: stur            d0, [x0, #0xf]
    // 0xac499c: ldur            x1, [fp, #-8]
    // 0xac49a0: ldur            x2, [fp, #-0x30]
    // 0xac49a4: mov             x3, x0
    // 0xac49a8: r0 = _mapPoint()
    //     0xac49a8: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac49ac: ldur            d0, [fp, #-0x70]
    // 0xac49b0: fmul            d1, d0, d0
    // 0xac49b4: ldur            d2, [fp, #-0x68]
    // 0xac49b8: fmul            d3, d2, d2
    // 0xac49bc: LoadField: d4 = r0->field_7
    //     0xac49bc: ldur            d4, [x0, #7]
    // 0xac49c0: fmul            d5, d4, d4
    // 0xac49c4: LoadField: d4 = r0->field_f
    //     0xac49c4: ldur            d4, [x0, #0xf]
    // 0xac49c8: fmul            d6, d4, d4
    // 0xac49cc: fdiv            d4, d5, d1
    // 0xac49d0: fdiv            d1, d6, d3
    // 0xac49d4: fadd            d3, d4, d1
    // 0xac49d8: d1 = 1.000000
    //     0xac49d8: fmov            d1, #1.00000000
    // 0xac49dc: fcmp            d3, d1
    // 0xac49e0: b.le            #0xac49f8
    // 0xac49e4: fsqrt           d4, d3
    // 0xac49e8: fmul            d3, d0, d4
    // 0xac49ec: fmul            d0, d2, d4
    // 0xac49f0: mov             v2.16b, v3.16b
    // 0xac49f4: b               #0xac4a04
    // 0xac49f8: mov             v31.16b, v2.16b
    // 0xac49fc: mov             v2.16b, v0.16b
    // 0xac4a00: mov             v0.16b, v31.16b
    // 0xac4a04: ldur            x0, [fp, #-0x18]
    // 0xac4a08: ldur            x1, [fp, #-0x30]
    // 0xac4a0c: stur            d2, [fp, #-0x68]
    // 0xac4a10: stur            d0, [fp, #-0x70]
    // 0xac4a14: r0 = setIdentity()
    //     0xac4a14: bl              #0xac6398  ; [package:vector_math/vector_math.dart] Matrix4::setIdentity
    // 0xac4a18: ldur            d2, [fp, #-0x68]
    // 0xac4a1c: d1 = 1.000000
    //     0xac4a1c: fmov            d1, #1.00000000
    // 0xac4a20: fdiv            d0, d1, d2
    // 0xac4a24: ldur            d3, [fp, #-0x70]
    // 0xac4a28: fdiv            d4, d1, d3
    // 0xac4a2c: r2 = inline_Allocate_Double()
    //     0xac4a2c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xac4a30: add             x2, x2, #0x10
    //     0xac4a34: cmp             x0, x2
    //     0xac4a38: b.ls            #0xac5194
    //     0xac4a3c: str             x2, [THR, #0x50]  ; THR::top
    //     0xac4a40: sub             x2, x2, #0xf
    //     0xac4a44: movz            x0, #0xe15c
    //     0xac4a48: movk            x0, #0x3, lsl #16
    //     0xac4a4c: stur            x0, [x2, #-1]
    // 0xac4a50: StoreField: r2->field_7 = d4
    //     0xac4a50: stur            d4, [x2, #7]
    // 0xac4a54: ldur            x1, [fp, #-0x30]
    // 0xac4a58: r0 = scale()
    //     0xac4a58: bl              #0xac539c  ; [package:vector_math/vector_math.dart] Matrix4::scale
    // 0xac4a5c: ldur            x1, [fp, #-0x30]
    // 0xac4a60: ldur            d0, [fp, #-0x78]
    // 0xac4a64: r0 = rotateZ()
    //     0xac4a64: bl              #0xac6634  ; [package:vector_math/vector_math.dart] Matrix4::rotateZ
    // 0xac4a68: ldur            x1, [fp, #-8]
    // 0xac4a6c: ldur            x2, [fp, #-0x30]
    // 0xac4a70: ldur            x3, [fp, #-0x10]
    // 0xac4a74: r0 = _mapPoint()
    //     0xac4a74: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac4a78: mov             x4, x0
    // 0xac4a7c: ldur            x0, [fp, #-0x18]
    // 0xac4a80: stur            x4, [fp, #-0x10]
    // 0xac4a84: LoadField: r3 = r0->field_b
    //     0xac4a84: ldur            w3, [x0, #0xb]
    // 0xac4a88: DecompressPointer r3
    //     0xac4a88: add             x3, x3, HEAP, lsl #32
    // 0xac4a8c: ldur            x1, [fp, #-8]
    // 0xac4a90: ldur            x2, [fp, #-0x30]
    // 0xac4a94: r0 = _mapPoint()
    //     0xac4a94: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac4a98: mov             x1, x0
    // 0xac4a9c: ldur            x2, [fp, #-0x10]
    // 0xac4aa0: stur            x0, [fp, #-0x28]
    // 0xac4aa4: r0 = -()
    //     0xac4aa4: bl              #0xac68c8  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::-
    // 0xac4aa8: LoadField: d0 = r0->field_7
    //     0xac4aa8: ldur            d0, [x0, #7]
    // 0xac4aac: fmul            d1, d0, d0
    // 0xac4ab0: LoadField: d0 = r0->field_f
    //     0xac4ab0: ldur            d0, [x0, #0xf]
    // 0xac4ab4: fmul            d2, d0, d0
    // 0xac4ab8: fadd            d0, d1, d2
    // 0xac4abc: d1 = 1.000000
    //     0xac4abc: fmov            d1, #1.00000000
    // 0xac4ac0: fdiv            d2, d1, d0
    // 0xac4ac4: d1 = 0.250000
    //     0xac4ac4: fmov            d1, #0.25000000
    // 0xac4ac8: fsub            d0, d2, d1
    // 0xac4acc: d2 = 0.000000
    //     0xac4acc: eor             v2.16b, v2.16b, v2.16b
    // 0xac4ad0: fcmp            d0, d2
    // 0xac4ad4: b.gt            #0xac4af8
    // 0xac4ad8: fcmp            d2, d0
    // 0xac4adc: b.le            #0xac4ae8
    // 0xac4ae0: d0 = 0.000000
    //     0xac4ae0: eor             v0.16b, v0.16b, v0.16b
    // 0xac4ae4: b               #0xac4af8
    // 0xac4ae8: fcmp            d0, d2
    // 0xac4aec: b.ne            #0xac4af8
    // 0xac4af0: fadd            d3, d0, d2
    // 0xac4af4: mov             v0.16b, v3.16b
    // 0xac4af8: fsqrt           d3, d0
    // 0xac4afc: mov             x1, v3.d[0]
    // 0xac4b00: and             x1, x1, #0x7fffffffffffffff
    // 0xac4b04: r17 = 9218868437227405312
    //     0xac4b04: orr             x17, xzr, #0x7ff0000000000000
    // 0xac4b08: cmp             x1, x17
    // 0xac4b0c: b.eq            #0xac4b20
    // 0xac4b10: fcmp            d3, d3
    // 0xac4b14: b.vs            #0xac4b20
    // 0xac4b18: mov             v0.16b, v3.16b
    // 0xac4b1c: b               #0xac4b24
    // 0xac4b20: d0 = 0.000000
    //     0xac4b20: eor             v0.16b, v0.16b, v0.16b
    // 0xac4b24: ldur            x2, [fp, #-0x18]
    // 0xac4b28: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xac4b28: ldur            w1, [x2, #0x17]
    // 0xac4b2c: DecompressPointer r1
    //     0xac4b2c: add             x1, x1, HEAP, lsl #32
    // 0xac4b30: LoadField: r3 = r2->field_1b
    //     0xac4b30: ldur            w3, [x2, #0x1b]
    // 0xac4b34: DecompressPointer r3
    //     0xac4b34: add             x3, x3, HEAP, lsl #32
    // 0xac4b38: cmp             w1, w3
    // 0xac4b3c: b.ne            #0xac4b48
    // 0xac4b40: fneg            d3, d0
    // 0xac4b44: mov             v0.16b, v3.16b
    // 0xac4b48: mov             x1, x0
    // 0xac4b4c: r0 = *()
    //     0xac4b4c: bl              #0xac6884  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::*
    // 0xac4b50: ldur            x1, [fp, #-0x10]
    // 0xac4b54: ldur            x2, [fp, #-0x28]
    // 0xac4b58: stur            x0, [fp, #-0x38]
    // 0xac4b5c: r0 = +()
    //     0xac4b5c: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac4b60: mov             x1, x0
    // 0xac4b64: d0 = 0.500000
    //     0xac4b64: fmov            d0, #0.50000000
    // 0xac4b68: r0 = *()
    //     0xac4b68: bl              #0xac6884  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::*
    // 0xac4b6c: mov             x1, x0
    // 0xac4b70: ldur            x0, [fp, #-0x38]
    // 0xac4b74: LoadField: d0 = r0->field_f
    //     0xac4b74: ldur            d0, [x0, #0xf]
    // 0xac4b78: fneg            d1, d0
    // 0xac4b7c: LoadField: d0 = r0->field_7
    //     0xac4b7c: ldur            d0, [x0, #7]
    // 0xac4b80: mov             v31.16b, v0.16b
    // 0xac4b84: mov             v0.16b, v1.16b
    // 0xac4b88: mov             v1.16b, v31.16b
    // 0xac4b8c: r0 = translate()
    //     0xac4b8c: bl              #0xac5358  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::translate
    // 0xac4b90: ldur            x1, [fp, #-0x10]
    // 0xac4b94: mov             x2, x0
    // 0xac4b98: stur            x0, [fp, #-0x10]
    // 0xac4b9c: r0 = -()
    //     0xac4b9c: bl              #0xac68c8  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::-
    // 0xac4ba0: LoadField: d0 = r0->field_f
    //     0xac4ba0: ldur            d0, [x0, #0xf]
    // 0xac4ba4: LoadField: d1 = r0->field_7
    //     0xac4ba4: ldur            d1, [x0, #7]
    // 0xac4ba8: stp             fp, lr, [SP, #-0x10]!
    // 0xac4bac: mov             fp, SP
    // 0xac4bb0: CallRuntime_LibcAtan2(double, double) -> double
    //     0xac4bb0: and             SP, SP, #0xfffffffffffffff0
    //     0xac4bb4: mov             sp, SP
    //     0xac4bb8: ldr             x16, [THR, #0x5d0]  ; THR::LibcAtan2
    //     0xac4bbc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4bc0: blr             x16
    //     0xac4bc4: movz            x16, #0x8
    //     0xac4bc8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4bcc: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4bd0: sub             sp, x16, #1, lsl #12
    //     0xac4bd4: mov             SP, fp
    //     0xac4bd8: ldp             fp, lr, [SP], #0x10
    // 0xac4bdc: ldur            x1, [fp, #-0x28]
    // 0xac4be0: ldur            x2, [fp, #-0x10]
    // 0xac4be4: stur            d0, [fp, #-0x78]
    // 0xac4be8: r0 = -()
    //     0xac4be8: bl              #0xac68c8  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::-
    // 0xac4bec: LoadField: d0 = r0->field_f
    //     0xac4bec: ldur            d0, [x0, #0xf]
    // 0xac4bf0: LoadField: d1 = r0->field_7
    //     0xac4bf0: ldur            d1, [x0, #7]
    // 0xac4bf4: stp             fp, lr, [SP, #-0x10]!
    // 0xac4bf8: mov             fp, SP
    // 0xac4bfc: CallRuntime_LibcAtan2(double, double) -> double
    //     0xac4bfc: and             SP, SP, #0xfffffffffffffff0
    //     0xac4c00: mov             sp, SP
    //     0xac4c04: ldr             x16, [THR, #0x5d0]  ; THR::LibcAtan2
    //     0xac4c08: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4c0c: blr             x16
    //     0xac4c10: movz            x16, #0x8
    //     0xac4c14: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4c18: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4c1c: sub             sp, x16, #1, lsl #12
    //     0xac4c20: mov             SP, fp
    //     0xac4c24: ldp             fp, lr, [SP], #0x10
    // 0xac4c28: mov             v1.16b, v0.16b
    // 0xac4c2c: ldur            d0, [fp, #-0x78]
    // 0xac4c30: fsub            d2, d1, d0
    // 0xac4c34: d1 = 0.000000
    //     0xac4c34: eor             v1.16b, v1.16b, v1.16b
    // 0xac4c38: fcmp            d1, d2
    // 0xac4c3c: b.le            #0xac4c68
    // 0xac4c40: ldur            x0, [fp, #-0x18]
    // 0xac4c44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xac4c44: ldur            w1, [x0, #0x17]
    // 0xac4c48: DecompressPointer r1
    //     0xac4c48: add             x1, x1, HEAP, lsl #32
    // 0xac4c4c: tbnz            w1, #4, #0xac4c60
    // 0xac4c50: d3 = 6.283185
    //     0xac4c50: ldr             d3, [PP, #0x5b80]  ; [pp+0x5b80] IMM: double(6.283185307179586) from 0x401921fb54442d18
    // 0xac4c54: fadd            d4, d2, d3
    // 0xac4c58: mov             v3.16b, v4.16b
    // 0xac4c5c: b               #0xac4c90
    // 0xac4c60: d3 = 6.283185
    //     0xac4c60: ldr             d3, [PP, #0x5b80]  ; [pp+0x5b80] IMM: double(6.283185307179586) from 0x401921fb54442d18
    // 0xac4c64: b               #0xac4c70
    // 0xac4c68: ldur            x0, [fp, #-0x18]
    // 0xac4c6c: d3 = 6.283185
    //     0xac4c6c: ldr             d3, [PP, #0x5b80]  ; [pp+0x5b80] IMM: double(6.283185307179586) from 0x401921fb54442d18
    // 0xac4c70: fcmp            d2, d1
    // 0xac4c74: b.le            #0xac4c8c
    // 0xac4c78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xac4c78: ldur            w1, [x0, #0x17]
    // 0xac4c7c: DecompressPointer r1
    //     0xac4c7c: add             x1, x1, HEAP, lsl #32
    // 0xac4c80: tbz             w1, #4, #0xac4c8c
    // 0xac4c84: fsub            d4, d2, d3
    // 0xac4c88: mov             v2.16b, v4.16b
    // 0xac4c8c: mov             v3.16b, v2.16b
    // 0xac4c90: ldur            d2, [fp, #-0x70]
    // 0xac4c94: ldur            x1, [fp, #-0x30]
    // 0xac4c98: stur            d3, [fp, #-0x80]
    // 0xac4c9c: r0 = setIdentity()
    //     0xac4c9c: bl              #0xac6398  ; [package:vector_math/vector_math.dart] Matrix4::setIdentity
    // 0xac4ca0: ldur            x1, [fp, #-0x30]
    // 0xac4ca4: ldur            d0, [fp, #-0x60]
    // 0xac4ca8: r0 = rotateZ()
    //     0xac4ca8: bl              #0xac6634  ; [package:vector_math/vector_math.dart] Matrix4::rotateZ
    // 0xac4cac: ldur            d0, [fp, #-0x70]
    // 0xac4cb0: r2 = inline_Allocate_Double()
    //     0xac4cb0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xac4cb4: add             x2, x2, #0x10
    //     0xac4cb8: cmp             x0, x2
    //     0xac4cbc: b.ls            #0xac51b8
    //     0xac4cc0: str             x2, [THR, #0x50]  ; THR::top
    //     0xac4cc4: sub             x2, x2, #0xf
    //     0xac4cc8: movz            x0, #0xe15c
    //     0xac4ccc: movk            x0, #0x3, lsl #16
    //     0xac4cd0: stur            x0, [x2, #-1]
    // 0xac4cd4: StoreField: r2->field_7 = d0
    //     0xac4cd4: stur            d0, [x2, #7]
    // 0xac4cd8: ldur            x1, [fp, #-0x30]
    // 0xac4cdc: ldur            d0, [fp, #-0x68]
    // 0xac4ce0: r0 = scale()
    //     0xac4ce0: bl              #0xac539c  ; [package:vector_math/vector_math.dart] Matrix4::scale
    // 0xac4ce4: ldur            d1, [fp, #-0x80]
    // 0xac4ce8: d0 = 1.571796
    //     0xac4ce8: add             x17, PP, #0x26, lsl #12  ; [pp+0x261f0] IMM: double(1.5717963267948964) from 0x3ff92613e7b8e982
    //     0xac4cec: ldr             d0, [x17, #0x1f0]
    // 0xac4cf0: fdiv            d2, d1, d0
    // 0xac4cf4: d0 = 0.000000
    //     0xac4cf4: eor             v0.16b, v0.16b, v0.16b
    // 0xac4cf8: fcmp            d2, d0
    // 0xac4cfc: b.ne            #0xac4d08
    // 0xac4d00: d0 = 0.000000
    //     0xac4d00: eor             v0.16b, v0.16b, v0.16b
    // 0xac4d04: b               #0xac4d1c
    // 0xac4d08: fcmp            d0, d2
    // 0xac4d0c: b.le            #0xac4d18
    // 0xac4d10: fneg            d0, d2
    // 0xac4d14: b               #0xac4d1c
    // 0xac4d18: mov             v0.16b, v2.16b
    // 0xac4d1c: ldur            x1, [fp, #-0x20]
    // 0xac4d20: ldur            x0, [fp, #-0x10]
    // 0xac4d24: fcmp            d0, d0
    // 0xac4d28: b.vs            #0xac51cc
    // 0xac4d2c: fcvtps          x2, d0
    // 0xac4d30: asr             x16, x2, #0x1e
    // 0xac4d34: cmp             x16, x2, asr #63
    // 0xac4d38: b.ne            #0xac51cc
    // 0xac4d3c: lsl             x2, x2, #1
    // 0xac4d40: r3 = LoadInt32Instr(r2)
    //     0xac4d40: sbfx            x3, x2, #1, #0x1f
    //     0xac4d44: tbz             w2, #0, #0xac4d4c
    //     0xac4d48: ldur            x3, [x2, #7]
    // 0xac4d4c: stur            x3, [fp, #-0x50]
    // 0xac4d50: scvtf           d2, x3
    // 0xac4d54: stur            d2, [fp, #-0x90]
    // 0xac4d58: LoadField: d3 = r0->field_7
    //     0xac4d58: ldur            d3, [x0, #7]
    // 0xac4d5c: stur            d3, [fp, #-0x88]
    // 0xac4d60: LoadField: d4 = r0->field_f
    //     0xac4d60: ldur            d4, [x0, #0xf]
    // 0xac4d64: stur            d4, [fp, #-0x70]
    // 0xac4d68: r0 = LoadClassIdInstr(r1)
    //     0xac4d68: ldur            x0, [x1, #-1]
    //     0xac4d6c: ubfx            x0, x0, #0xc, #0x14
    // 0xac4d70: stur            x0, [fp, #-0x48]
    // 0xac4d74: r2 = 0
    //     0xac4d74: movz            x2, #0
    // 0xac4d78: ldur            d5, [fp, #-0x78]
    // 0xac4d7c: d6 = 0.250000
    //     0xac4d7c: fmov            d6, #0.25000000
    // 0xac4d80: CheckStackOverflow
    //     0xac4d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac4d84: cmp             SP, x16
    //     0xac4d88: b.ls            #0xac51f4
    // 0xac4d8c: cmp             x2, x3
    // 0xac4d90: b.ge            #0xac517c
    // 0xac4d94: scvtf           d0, x2
    // 0xac4d98: fmul            d7, d0, d1
    // 0xac4d9c: fdiv            d0, d7, d2
    // 0xac4da0: fadd            d7, d5, d0
    // 0xac4da4: stur            d7, [fp, #-0x68]
    // 0xac4da8: add             x4, x2, #1
    // 0xac4dac: stur            x4, [fp, #-0x40]
    // 0xac4db0: scvtf           d0, x4
    // 0xac4db4: fmul            d8, d0, d1
    // 0xac4db8: fdiv            d0, d8, d2
    // 0xac4dbc: fadd            d8, d5, d0
    // 0xac4dc0: stur            d8, [fp, #-0x60]
    // 0xac4dc4: fsub            d0, d8, d7
    // 0xac4dc8: fmul            d9, d0, d6
    // 0xac4dcc: mov             v0.16b, v9.16b
    // 0xac4dd0: stp             fp, lr, [SP, #-0x10]!
    // 0xac4dd4: mov             fp, SP
    // 0xac4dd8: CallRuntime_LibcTan(double) -> double
    //     0xac4dd8: and             SP, SP, #0xfffffffffffffff0
    //     0xac4ddc: mov             sp, SP
    //     0xac4de0: ldr             x16, [THR, #0x5b0]  ; THR::LibcTan
    //     0xac4de4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4de8: blr             x16
    //     0xac4dec: movz            x16, #0x8
    //     0xac4df0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4df4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4df8: sub             sp, x16, #1, lsl #12
    //     0xac4dfc: mov             SP, fp
    //     0xac4e00: ldp             fp, lr, [SP], #0x10
    // 0xac4e04: d1 = 1.333333
    //     0xac4e04: add             x17, PP, #0x25, lsl #12  ; [pp+0x25d48] IMM: double(1.3333333333333333) from 0x3ff5555555555555
    //     0xac4e08: ldr             d1, [x17, #0xd48]
    // 0xac4e0c: fmul            d2, d0, d1
    // 0xac4e10: stur            d2, [fp, #-0x98]
    // 0xac4e14: mov             x0, v2.d[0]
    // 0xac4e18: and             x0, x0, #0x7fffffffffffffff
    // 0xac4e1c: r17 = 9218868437227405312
    //     0xac4e1c: orr             x17, xzr, #0x7ff0000000000000
    // 0xac4e20: cmp             x0, x17
    // 0xac4e24: b.eq            #0xac516c
    // 0xac4e28: fcmp            d2, d2
    // 0xac4e2c: b.vs            #0xac516c
    // 0xac4e30: ldur            d3, [fp, #-0x88]
    // 0xac4e34: ldur            d4, [fp, #-0x70]
    // 0xac4e38: ldur            x0, [fp, #-0x48]
    // 0xac4e3c: ldur            d0, [fp, #-0x68]
    // 0xac4e40: stp             fp, lr, [SP, #-0x10]!
    // 0xac4e44: mov             fp, SP
    // 0xac4e48: CallRuntime_LibcSin(double) -> double
    //     0xac4e48: and             SP, SP, #0xfffffffffffffff0
    //     0xac4e4c: mov             sp, SP
    //     0xac4e50: ldr             x16, [THR, #0x5a8]  ; THR::LibcSin
    //     0xac4e54: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4e58: blr             x16
    //     0xac4e5c: movz            x16, #0x8
    //     0xac4e60: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4e64: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4e68: sub             sp, x16, #1, lsl #12
    //     0xac4e6c: mov             SP, fp
    //     0xac4e70: ldp             fp, lr, [SP], #0x10
    // 0xac4e74: mov             v1.16b, v0.16b
    // 0xac4e78: ldur            d0, [fp, #-0x68]
    // 0xac4e7c: stur            d1, [fp, #-0x68]
    // 0xac4e80: stp             fp, lr, [SP, #-0x10]!
    // 0xac4e84: mov             fp, SP
    // 0xac4e88: CallRuntime_LibcCos(double) -> double
    //     0xac4e88: and             SP, SP, #0xfffffffffffffff0
    //     0xac4e8c: mov             sp, SP
    //     0xac4e90: ldr             x16, [THR, #0x5a0]  ; THR::LibcCos
    //     0xac4e94: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4e98: blr             x16
    //     0xac4e9c: movz            x16, #0x8
    //     0xac4ea0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4ea4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4ea8: sub             sp, x16, #1, lsl #12
    //     0xac4eac: mov             SP, fp
    //     0xac4eb0: ldp             fp, lr, [SP], #0x10
    // 0xac4eb4: mov             v1.16b, v0.16b
    // 0xac4eb8: ldur            d0, [fp, #-0x60]
    // 0xac4ebc: stur            d1, [fp, #-0xa0]
    // 0xac4ec0: stp             fp, lr, [SP, #-0x10]!
    // 0xac4ec4: mov             fp, SP
    // 0xac4ec8: CallRuntime_LibcSin(double) -> double
    //     0xac4ec8: and             SP, SP, #0xfffffffffffffff0
    //     0xac4ecc: mov             sp, SP
    //     0xac4ed0: ldr             x16, [THR, #0x5a8]  ; THR::LibcSin
    //     0xac4ed4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4ed8: blr             x16
    //     0xac4edc: movz            x16, #0x8
    //     0xac4ee0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4ee4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4ee8: sub             sp, x16, #1, lsl #12
    //     0xac4eec: mov             SP, fp
    //     0xac4ef0: ldp             fp, lr, [SP], #0x10
    // 0xac4ef4: mov             v1.16b, v0.16b
    // 0xac4ef8: ldur            d0, [fp, #-0x60]
    // 0xac4efc: stur            d1, [fp, #-0x60]
    // 0xac4f00: stp             fp, lr, [SP, #-0x10]!
    // 0xac4f04: mov             fp, SP
    // 0xac4f08: CallRuntime_LibcCos(double) -> double
    //     0xac4f08: and             SP, SP, #0xfffffffffffffff0
    //     0xac4f0c: mov             sp, SP
    //     0xac4f10: ldr             x16, [THR, #0x5a0]  ; THR::LibcCos
    //     0xac4f14: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4f18: blr             x16
    //     0xac4f1c: movz            x16, #0x8
    //     0xac4f20: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac4f24: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac4f28: sub             sp, x16, #1, lsl #12
    //     0xac4f2c: mov             SP, fp
    //     0xac4f30: ldp             fp, lr, [SP], #0x10
    // 0xac4f34: mov             v2.16b, v0.16b
    // 0xac4f38: ldur            d1, [fp, #-0x98]
    // 0xac4f3c: ldur            d0, [fp, #-0x68]
    // 0xac4f40: stur            d2, [fp, #-0xa8]
    // 0xac4f44: fmul            d3, d1, d0
    // 0xac4f48: ldur            d4, [fp, #-0xa0]
    // 0xac4f4c: fsub            d5, d4, d3
    // 0xac4f50: fmul            d3, d1, d4
    // 0xac4f54: fadd            d4, d0, d3
    // 0xac4f58: ldur            d0, [fp, #-0x88]
    // 0xac4f5c: fadd            d3, d5, d0
    // 0xac4f60: ldur            d5, [fp, #-0x70]
    // 0xac4f64: stur            d3, [fp, #-0xa0]
    // 0xac4f68: fadd            d6, d4, d5
    // 0xac4f6c: stur            d6, [fp, #-0x68]
    // 0xac4f70: r0 = _PathOffset()
    //     0xac4f70: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac4f74: ldur            d0, [fp, #-0xa0]
    // 0xac4f78: stur            x0, [fp, #-0x10]
    // 0xac4f7c: StoreField: r0->field_7 = d0
    //     0xac4f7c: stur            d0, [x0, #7]
    // 0xac4f80: ldur            d0, [fp, #-0x68]
    // 0xac4f84: StoreField: r0->field_f = d0
    //     0xac4f84: stur            d0, [x0, #0xf]
    // 0xac4f88: ldur            d1, [fp, #-0x88]
    // 0xac4f8c: ldur            d0, [fp, #-0xa8]
    // 0xac4f90: fadd            d2, d0, d1
    // 0xac4f94: ldur            d3, [fp, #-0x70]
    // 0xac4f98: ldur            d4, [fp, #-0x60]
    // 0xac4f9c: stur            d2, [fp, #-0xa0]
    // 0xac4fa0: fadd            d5, d4, d3
    // 0xac4fa4: stur            d5, [fp, #-0x68]
    // 0xac4fa8: r0 = _PathOffset()
    //     0xac4fa8: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac4fac: ldur            d0, [fp, #-0xa0]
    // 0xac4fb0: stur            x0, [fp, #-0x18]
    // 0xac4fb4: StoreField: r0->field_7 = d0
    //     0xac4fb4: stur            d0, [x0, #7]
    // 0xac4fb8: ldur            d1, [fp, #-0x68]
    // 0xac4fbc: StoreField: r0->field_f = d1
    //     0xac4fbc: stur            d1, [x0, #0xf]
    // 0xac4fc0: ldur            d3, [fp, #-0x98]
    // 0xac4fc4: ldur            d2, [fp, #-0x60]
    // 0xac4fc8: fmul            d4, d3, d2
    // 0xac4fcc: fneg            d2, d3
    // 0xac4fd0: ldur            d3, [fp, #-0xa8]
    // 0xac4fd4: fmul            d5, d2, d3
    // 0xac4fd8: fadd            d2, d0, d4
    // 0xac4fdc: stur            d2, [fp, #-0x98]
    // 0xac4fe0: fadd            d0, d1, d5
    // 0xac4fe4: stur            d0, [fp, #-0x60]
    // 0xac4fe8: r0 = _PathOffset()
    //     0xac4fe8: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac4fec: ldur            d0, [fp, #-0x98]
    // 0xac4ff0: stur            x0, [fp, #-0x28]
    // 0xac4ff4: StoreField: r0->field_7 = d0
    //     0xac4ff4: stur            d0, [x0, #7]
    // 0xac4ff8: ldur            d0, [fp, #-0x60]
    // 0xac4ffc: StoreField: r0->field_f = d0
    //     0xac4ffc: stur            d0, [x0, #0xf]
    // 0xac5000: ldur            x1, [fp, #-8]
    // 0xac5004: ldur            x2, [fp, #-0x30]
    // 0xac5008: ldur            x3, [fp, #-0x10]
    // 0xac500c: r0 = _mapPoint()
    //     0xac500c: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac5010: ldur            x1, [fp, #-8]
    // 0xac5014: ldur            x2, [fp, #-0x30]
    // 0xac5018: ldur            x3, [fp, #-0x28]
    // 0xac501c: stur            x0, [fp, #-0x10]
    // 0xac5020: r0 = _mapPoint()
    //     0xac5020: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac5024: ldur            x1, [fp, #-8]
    // 0xac5028: ldur            x2, [fp, #-0x30]
    // 0xac502c: ldur            x3, [fp, #-0x18]
    // 0xac5030: stur            x0, [fp, #-0x18]
    // 0xac5034: r0 = _mapPoint()
    //     0xac5034: bl              #0xac6548  ; [package:path_parsing/src/path_parsing.dart] SvgPathNormalizer::_mapPoint
    // 0xac5038: mov             x1, x0
    // 0xac503c: ldur            x0, [fp, #-0x10]
    // 0xac5040: LoadField: d0 = r0->field_7
    //     0xac5040: ldur            d0, [x0, #7]
    // 0xac5044: stur            d0, [fp, #-0xb0]
    // 0xac5048: LoadField: d1 = r0->field_f
    //     0xac5048: ldur            d1, [x0, #0xf]
    // 0xac504c: ldur            x0, [fp, #-0x18]
    // 0xac5050: stur            d1, [fp, #-0xa8]
    // 0xac5054: LoadField: d2 = r0->field_7
    //     0xac5054: ldur            d2, [x0, #7]
    // 0xac5058: stur            d2, [fp, #-0xa0]
    // 0xac505c: LoadField: d3 = r0->field_f
    //     0xac505c: ldur            d3, [x0, #0xf]
    // 0xac5060: stur            d3, [fp, #-0x98]
    // 0xac5064: LoadField: d4 = r1->field_7
    //     0xac5064: ldur            d4, [x1, #7]
    // 0xac5068: stur            d4, [fp, #-0x68]
    // 0xac506c: LoadField: d5 = r1->field_f
    //     0xac506c: ldur            d5, [x1, #0xf]
    // 0xac5070: ldur            x0, [fp, #-0x48]
    // 0xac5074: stur            d5, [fp, #-0x60]
    // 0xac5078: cmp             x0, #0x3a7
    // 0xac507c: b.ne            #0xac5094
    // 0xac5080: ldur            x2, [fp, #-0x20]
    // 0xac5084: LoadField: r1 = r2->field_7
    //     0xac5084: ldur            w1, [x2, #7]
    // 0xac5088: DecompressPointer r1
    //     0xac5088: add             x1, x1, HEAP, lsl #32
    // 0xac508c: r0 = curveTo()
    //     0xac508c: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xac5090: b               #0xac5148
    // 0xac5094: cmp             x0, #0x3a8
    // 0xac5098: b.ne            #0xac5114
    // 0xac509c: ldur            x1, [fp, #-0x20]
    // 0xac50a0: LoadField: r2 = r1->field_7
    //     0xac50a0: ldur            w2, [x1, #7]
    // 0xac50a4: DecompressPointer r2
    //     0xac50a4: add             x2, x2, HEAP, lsl #32
    // 0xac50a8: stur            x2, [fp, #-0x10]
    // 0xac50ac: LoadField: r3 = r2->field_7
    //     0xac50ac: ldur            w3, [x2, #7]
    // 0xac50b0: DecompressPointer r3
    //     0xac50b0: add             x3, x3, HEAP, lsl #32
    // 0xac50b4: cmp             w3, NULL
    // 0xac50b8: b.eq            #0xac51fc
    // 0xac50bc: LoadField: r4 = r3->field_7
    //     0xac50bc: ldur            x4, [x3, #7]
    // 0xac50c0: ldr             x3, [x4]
    // 0xac50c4: stur            x3, [fp, #-0x58]
    // 0xac50c8: cbnz            x3, #0xac50d8
    // 0xac50cc: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xac50cc: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xac50d0: str             x16, [SP]
    // 0xac50d4: r0 = _throwNew()
    //     0xac50d4: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xac50d8: ldur            x0, [fp, #-0x58]
    // 0xac50dc: stur            x0, [fp, #-0x58]
    // 0xac50e0: r1 = <Never>
    //     0xac50e0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xac50e4: r0 = Pointer()
    //     0xac50e4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xac50e8: mov             x1, x0
    // 0xac50ec: ldur            x0, [fp, #-0x58]
    // 0xac50f0: StoreField: r1->field_7 = r0
    //     0xac50f0: stur            x0, [x1, #7]
    // 0xac50f4: ldur            d0, [fp, #-0xb0]
    // 0xac50f8: ldur            d1, [fp, #-0xa8]
    // 0xac50fc: ldur            d2, [fp, #-0xa0]
    // 0xac5100: ldur            d3, [fp, #-0x98]
    // 0xac5104: ldur            d4, [fp, #-0x68]
    // 0xac5108: ldur            d5, [fp, #-0x60]
    // 0xac510c: r0 = _cubicTo$Method$FfiNative()
    //     0xac510c: bl              #0xac4334  ; [dart:ui] _NativePath::_cubicTo$Method$FfiNative
    // 0xac5110: b               #0xac5148
    // 0xac5114: ldur            x2, [fp, #-0x20]
    // 0xac5118: r0 = LoadClassIdInstr(r2)
    //     0xac5118: ldur            x0, [x2, #-1]
    //     0xac511c: ubfx            x0, x0, #0xc, #0x14
    // 0xac5120: mov             x1, x2
    // 0xac5124: ldur            d0, [fp, #-0xb0]
    // 0xac5128: ldur            d1, [fp, #-0xa8]
    // 0xac512c: ldur            d2, [fp, #-0xa0]
    // 0xac5130: ldur            d3, [fp, #-0x98]
    // 0xac5134: ldur            d4, [fp, #-0x68]
    // 0xac5138: ldur            d5, [fp, #-0x60]
    // 0xac513c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xac513c: sub             lr, x0, #1, lsl #12
    //     0xac5140: ldr             lr, [x21, lr, lsl #3]
    //     0xac5144: blr             lr
    // 0xac5148: ldur            x2, [fp, #-0x40]
    // 0xac514c: ldur            x1, [fp, #-0x20]
    // 0xac5150: ldur            d1, [fp, #-0x80]
    // 0xac5154: ldur            d3, [fp, #-0x88]
    // 0xac5158: ldur            d4, [fp, #-0x70]
    // 0xac515c: ldur            d2, [fp, #-0x90]
    // 0xac5160: ldur            x0, [fp, #-0x48]
    // 0xac5164: ldur            x3, [fp, #-0x50]
    // 0xac5168: b               #0xac4d78
    // 0xac516c: r0 = false
    //     0xac516c: add             x0, NULL, #0x30  ; false
    // 0xac5170: LeaveFrame
    //     0xac5170: mov             SP, fp
    //     0xac5174: ldp             fp, lr, [SP], #0x10
    // 0xac5178: ret
    //     0xac5178: ret             
    // 0xac517c: r0 = true
    //     0xac517c: add             x0, NULL, #0x20  ; true
    // 0xac5180: LeaveFrame
    //     0xac5180: mov             SP, fp
    //     0xac5184: ldp             fp, lr, [SP], #0x10
    // 0xac5188: ret
    //     0xac5188: ret             
    // 0xac518c: r0 = StackOverflowSharedWithFPURegs()
    //     0xac518c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac5190: b               #0xac486c
    // 0xac5194: stp             q3, q4, [SP, #-0x20]!
    // 0xac5198: stp             q1, q2, [SP, #-0x20]!
    // 0xac519c: SaveReg d0
    //     0xac519c: str             q0, [SP, #-0x10]!
    // 0xac51a0: r0 = AllocateDouble()
    //     0xac51a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac51a4: mov             x2, x0
    // 0xac51a8: RestoreReg d0
    //     0xac51a8: ldr             q0, [SP], #0x10
    // 0xac51ac: ldp             q1, q2, [SP], #0x20
    // 0xac51b0: ldp             q3, q4, [SP], #0x20
    // 0xac51b4: b               #0xac4a50
    // 0xac51b8: SaveReg d0
    //     0xac51b8: str             q0, [SP, #-0x10]!
    // 0xac51bc: r0 = AllocateDouble()
    //     0xac51bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac51c0: mov             x2, x0
    // 0xac51c4: RestoreReg d0
    //     0xac51c4: ldr             q0, [SP], #0x10
    // 0xac51c8: b               #0xac4cd4
    // 0xac51cc: stp             q0, q1, [SP, #-0x20]!
    // 0xac51d0: stp             x0, x1, [SP, #-0x10]!
    // 0xac51d4: r0 = 64
    //     0xac51d4: movz            x0, #0x40
    // 0xac51d8: r30 = DoubleToIntegerStub
    //     0xac51d8: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xac51dc: LoadField: r30 = r30->field_7
    //     0xac51dc: ldur            lr, [lr, #7]
    // 0xac51e0: blr             lr
    // 0xac51e4: mov             x2, x0
    // 0xac51e8: ldp             x0, x1, [SP], #0x10
    // 0xac51ec: ldp             q0, q1, [SP], #0x20
    // 0xac51f0: b               #0xac4d40
    // 0xac51f4: r0 = StackOverflowSharedWithFPURegs()
    //     0xac51f4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac51f8: b               #0xac4d8c
    // 0xac51fc: r0 = NullErrorSharedWithFPURegs()
    //     0xac51fc: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _mapPoint(/* No info */) {
    // ** addr: 0xac6548, size: 0xec
    // 0xac6548: EnterFrame
    //     0xac6548: stp             fp, lr, [SP, #-0x10]!
    //     0xac654c: mov             fp, SP
    // 0xac6550: AllocStack(0x10)
    //     0xac6550: sub             SP, SP, #0x10
    // 0xac6554: LoadField: r4 = r2->field_7
    //     0xac6554: ldur            w4, [x2, #7]
    // 0xac6558: DecompressPointer r4
    //     0xac6558: add             x4, x4, HEAP, lsl #32
    // 0xac655c: LoadField: r0 = r4->field_13
    //     0xac655c: ldur            w0, [x4, #0x13]
    // 0xac6560: r2 = LoadInt32Instr(r0)
    //     0xac6560: sbfx            x2, x0, #1, #0x1f
    // 0xac6564: mov             x0, x2
    // 0xac6568: r1 = 0
    //     0xac6568: movz            x1, #0
    // 0xac656c: cmp             x1, x0
    // 0xac6570: b.hs            #0xac6624
    // 0xac6574: ArrayLoad: d0 = r4[0]  ; List_8
    //     0xac6574: ldur            s0, [x4, #0x17]
    // 0xac6578: fcvt            d1, s0
    // 0xac657c: LoadField: d0 = r3->field_7
    //     0xac657c: ldur            d0, [x3, #7]
    // 0xac6580: fmul            d2, d1, d0
    // 0xac6584: mov             x0, x2
    // 0xac6588: r1 = 4
    //     0xac6588: movz            x1, #0x4
    // 0xac658c: cmp             x1, x0
    // 0xac6590: b.hs            #0xac6628
    // 0xac6594: LoadField: d1 = r4->field_27
    //     0xac6594: ldur            s1, [x4, #0x27]
    // 0xac6598: fcvt            d3, s1
    // 0xac659c: LoadField: d1 = r3->field_f
    //     0xac659c: ldur            d1, [x3, #0xf]
    // 0xac65a0: fmul            d4, d3, d1
    // 0xac65a4: fadd            d3, d2, d4
    // 0xac65a8: mov             x0, x2
    // 0xac65ac: r1 = 12
    //     0xac65ac: movz            x1, #0xc
    // 0xac65b0: cmp             x1, x0
    // 0xac65b4: b.hs            #0xac662c
    // 0xac65b8: LoadField: d2 = r4->field_47
    //     0xac65b8: ldur            s2, [x4, #0x47]
    // 0xac65bc: fcvt            d4, s2
    // 0xac65c0: fadd            d2, d3, d4
    // 0xac65c4: stur            d2, [fp, #-0x10]
    // 0xac65c8: LoadField: d3 = r4->field_1b
    //     0xac65c8: ldur            s3, [x4, #0x1b]
    // 0xac65cc: fcvt            d4, s3
    // 0xac65d0: fmul            d3, d4, d0
    // 0xac65d4: LoadField: d0 = r4->field_2b
    //     0xac65d4: ldur            s0, [x4, #0x2b]
    // 0xac65d8: fcvt            d4, s0
    // 0xac65dc: fmul            d0, d4, d1
    // 0xac65e0: fadd            d1, d3, d0
    // 0xac65e4: mov             x0, x2
    // 0xac65e8: r1 = 13
    //     0xac65e8: movz            x1, #0xd
    // 0xac65ec: cmp             x1, x0
    // 0xac65f0: b.hs            #0xac6630
    // 0xac65f4: LoadField: d0 = r4->field_4b
    //     0xac65f4: ldur            s0, [x4, #0x4b]
    // 0xac65f8: fcvt            d3, s0
    // 0xac65fc: fadd            d0, d1, d3
    // 0xac6600: stur            d0, [fp, #-8]
    // 0xac6604: r0 = _PathOffset()
    //     0xac6604: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac6608: ldur            d0, [fp, #-0x10]
    // 0xac660c: StoreField: r0->field_7 = d0
    //     0xac660c: stur            d0, [x0, #7]
    // 0xac6610: ldur            d0, [fp, #-8]
    // 0xac6614: StoreField: r0->field_f = d0
    //     0xac6614: stur            d0, [x0, #0xf]
    // 0xac6618: LeaveFrame
    //     0xac6618: mov             SP, fp
    //     0xac661c: ldp             fp, lr, [SP], #0x10
    // 0xac6620: ret
    //     0xac6620: ret             
    // 0xac6624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6624: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6628: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6628: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac662c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac662c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6630: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6630: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
}

// class id: 930, size: 0x20, field offset: 0x8
class PathSegmentData extends Object {

  set _ arcAngle=(/* No info */) {
    // ** addr: 0xac7498, size: 0x70
    // 0xac7498: EnterFrame
    //     0xac7498: stp             fp, lr, [SP, #-0x10]!
    //     0xac749c: mov             fp, SP
    // 0xac74a0: AllocStack(0x18)
    //     0xac74a0: sub             SP, SP, #0x18
    // 0xac74a4: SetupParameters(PathSegmentData this /* r1 => r1, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0xac74a4: stur            x1, [fp, #-8]
    //     0xac74a8: stur            d0, [fp, #-0x18]
    // 0xac74ac: LoadField: r0 = r1->field_13
    //     0xac74ac: ldur            w0, [x1, #0x13]
    // 0xac74b0: DecompressPointer r0
    //     0xac74b0: add             x0, x0, HEAP, lsl #32
    // 0xac74b4: LoadField: d1 = r0->field_f
    //     0xac74b4: ldur            d1, [x0, #0xf]
    // 0xac74b8: stur            d1, [fp, #-0x10]
    // 0xac74bc: r0 = _PathOffset()
    //     0xac74bc: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac74c0: mov             x1, x0
    // 0xac74c4: ldur            d0, [fp, #-0x18]
    // 0xac74c8: StoreField: r1->field_7 = d0
    //     0xac74c8: stur            d0, [x1, #7]
    // 0xac74cc: ldur            d0, [fp, #-0x10]
    // 0xac74d0: StoreField: r1->field_f = d0
    //     0xac74d0: stur            d0, [x1, #0xf]
    // 0xac74d4: mov             x0, x1
    // 0xac74d8: ldur            x2, [fp, #-8]
    // 0xac74dc: StoreField: r2->field_13 = r0
    //     0xac74dc: stur            w0, [x2, #0x13]
    //     0xac74e0: ldurb           w16, [x2, #-1]
    //     0xac74e4: ldurb           w17, [x0, #-1]
    //     0xac74e8: and             x16, x17, x16, lsr #2
    //     0xac74ec: tst             x16, HEAP, lsr #32
    //     0xac74f0: b.eq            #0xac74f8
    //     0xac74f4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac74f8: mov             x0, x1
    // 0xac74fc: LeaveFrame
    //     0xac74fc: mov             SP, fp
    //     0xac7500: ldp             fp, lr, [SP], #0x10
    // 0xac7504: ret
    //     0xac7504: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc339d0, size: 0xcc
    // 0xc339d0: EnterFrame
    //     0xc339d0: stp             fp, lr, [SP, #-0x10]!
    //     0xc339d4: mov             fp, SP
    // 0xc339d8: AllocStack(0x8)
    //     0xc339d8: sub             SP, SP, #8
    // 0xc339dc: CheckStackOverflow
    //     0xc339dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc339e0: cmp             SP, x16
    //     0xc339e4: b.ls            #0xc33a94
    // 0xc339e8: r1 = Null
    //     0xc339e8: mov             x1, NULL
    // 0xc339ec: r2 = 26
    //     0xc339ec: movz            x2, #0x1a
    // 0xc339f0: r0 = AllocateArray()
    //     0xc339f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc339f4: r16 = "PathSegmentData{"
    //     0xc339f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31510] "PathSegmentData{"
    //     0xc339f8: ldr             x16, [x16, #0x510]
    // 0xc339fc: StoreField: r0->field_f = r16
    //     0xc339fc: stur            w16, [x0, #0xf]
    // 0xc33a00: ldr             x1, [fp, #0x10]
    // 0xc33a04: LoadField: r2 = r1->field_7
    //     0xc33a04: ldur            w2, [x1, #7]
    // 0xc33a08: DecompressPointer r2
    //     0xc33a08: add             x2, x2, HEAP, lsl #32
    // 0xc33a0c: StoreField: r0->field_13 = r2
    //     0xc33a0c: stur            w2, [x0, #0x13]
    // 0xc33a10: r16 = " "
    //     0xc33a10: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc33a14: ArrayStore: r0[0] = r16  ; List_4
    //     0xc33a14: stur            w16, [x0, #0x17]
    // 0xc33a18: LoadField: r2 = r1->field_b
    //     0xc33a18: ldur            w2, [x1, #0xb]
    // 0xc33a1c: DecompressPointer r2
    //     0xc33a1c: add             x2, x2, HEAP, lsl #32
    // 0xc33a20: StoreField: r0->field_1b = r2
    //     0xc33a20: stur            w2, [x0, #0x1b]
    // 0xc33a24: r16 = " "
    //     0xc33a24: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc33a28: StoreField: r0->field_1f = r16
    //     0xc33a28: stur            w16, [x0, #0x1f]
    // 0xc33a2c: LoadField: r2 = r1->field_f
    //     0xc33a2c: ldur            w2, [x1, #0xf]
    // 0xc33a30: DecompressPointer r2
    //     0xc33a30: add             x2, x2, HEAP, lsl #32
    // 0xc33a34: StoreField: r0->field_23 = r2
    //     0xc33a34: stur            w2, [x0, #0x23]
    // 0xc33a38: r16 = " "
    //     0xc33a38: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc33a3c: StoreField: r0->field_27 = r16
    //     0xc33a3c: stur            w16, [x0, #0x27]
    // 0xc33a40: LoadField: r2 = r1->field_13
    //     0xc33a40: ldur            w2, [x1, #0x13]
    // 0xc33a44: DecompressPointer r2
    //     0xc33a44: add             x2, x2, HEAP, lsl #32
    // 0xc33a48: StoreField: r0->field_2b = r2
    //     0xc33a48: stur            w2, [x0, #0x2b]
    // 0xc33a4c: r16 = " "
    //     0xc33a4c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc33a50: StoreField: r0->field_2f = r16
    //     0xc33a50: stur            w16, [x0, #0x2f]
    // 0xc33a54: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc33a54: ldur            w2, [x1, #0x17]
    // 0xc33a58: DecompressPointer r2
    //     0xc33a58: add             x2, x2, HEAP, lsl #32
    // 0xc33a5c: StoreField: r0->field_33 = r2
    //     0xc33a5c: stur            w2, [x0, #0x33]
    // 0xc33a60: r16 = " "
    //     0xc33a60: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc33a64: StoreField: r0->field_37 = r16
    //     0xc33a64: stur            w16, [x0, #0x37]
    // 0xc33a68: LoadField: r2 = r1->field_1b
    //     0xc33a68: ldur            w2, [x1, #0x1b]
    // 0xc33a6c: DecompressPointer r2
    //     0xc33a6c: add             x2, x2, HEAP, lsl #32
    // 0xc33a70: StoreField: r0->field_3b = r2
    //     0xc33a70: stur            w2, [x0, #0x3b]
    // 0xc33a74: r16 = "}"
    //     0xc33a74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12240] "}"
    //     0xc33a78: ldr             x16, [x16, #0x240]
    // 0xc33a7c: StoreField: r0->field_3f = r16
    //     0xc33a7c: stur            w16, [x0, #0x3f]
    // 0xc33a80: str             x0, [SP]
    // 0xc33a84: r0 = _interpolate()
    //     0xc33a84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc33a88: LeaveFrame
    //     0xc33a88: mov             SP, fp
    //     0xc33a8c: ldp             fp, lr, [SP], #0x10
    // 0xc33a90: ret
    //     0xc33a90: ret             
    // 0xc33a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33a98: b               #0xc339e8
  }
}

// class id: 931, size: 0x20, field offset: 0x8
class SvgPathStringSource extends Object {

  _ parseSegments(/* No info */) {
    // ** addr: 0xac6c90, size: 0xcc
    // 0xac6c90: EnterFrame
    //     0xac6c90: stp             fp, lr, [SP, #-0x10]!
    //     0xac6c94: mov             fp, SP
    // 0xac6c98: AllocStack(0x20)
    //     0xac6c98: sub             SP, SP, #0x20
    // 0xac6c9c: SetupParameters(SvgPathStringSource this /* r1 => r1, fp-0x10 */)
    //     0xac6c9c: stur            NULL, [fp, #-8]
    //     0xac6ca0: stur            x1, [fp, #-0x10]
    // 0xac6ca4: CheckStackOverflow
    //     0xac6ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6ca8: cmp             SP, x16
    //     0xac6cac: b.ls            #0xac6d4c
    // 0xac6cb0: InitAsync() -> Future<PathSegmentData>
    //     0xac6cb0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26220] TypeArguments: <PathSegmentData>
    //     0xac6cb4: ldr             x0, [x0, #0x220]
    //     0xac6cb8: bl              #0x7348c0  ; InitAsyncStub
    // 0xac6cbc: r0 = Null
    //     0xac6cbc: mov             x0, NULL
    // 0xac6cc0: r0 = SuspendSyncStarAtStart()
    //     0xac6cc0: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0xac6cc4: ldur            x0, [fp, #-0x10]
    // 0xac6cc8: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xac6cc8: ldur            x2, [x0, #0x17]
    // 0xac6ccc: stur            x2, [fp, #-0x20]
    // 0xac6cd0: r3 = 0
    //     0xac6cd0: movz            x3, #0
    // 0xac6cd4: CheckStackOverflow
    //     0xac6cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6cd8: cmp             SP, x16
    //     0xac6cdc: b.ls            #0xac6d54
    // 0xac6ce0: LoadField: r1 = r0->field_f
    //     0xac6ce0: ldur            x1, [x0, #0xf]
    // 0xac6ce4: cmp             x1, x2
    // 0xac6ce8: b.ge            #0xac6d3c
    // 0xac6cec: add             x1, fp, w3, sxtw #2
    // 0xac6cf0: LoadField: r1 = r1->field_fffffff8
    //     0xac6cf0: ldur            x1, [x1, #-8]
    // 0xac6cf4: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xac6cf4: ldur            w4, [x1, #0x17]
    // 0xac6cf8: DecompressPointer r4
    //     0xac6cf8: add             x4, x4, HEAP, lsl #32
    // 0xac6cfc: mov             x1, x0
    // 0xac6d00: stur            x4, [fp, #-0x18]
    // 0xac6d04: r0 = parseSegment()
    //     0xac6d04: bl              #0xac6d5c  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::parseSegment
    // 0xac6d08: ldur            x1, [fp, #-0x18]
    // 0xac6d0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xac6d0c: stur            w0, [x1, #0x17]
    //     0xac6d10: ldurb           w16, [x1, #-1]
    //     0xac6d14: ldurb           w17, [x0, #-1]
    //     0xac6d18: and             x16, x17, x16, lsr #2
    //     0xac6d1c: tst             x16, HEAP, lsr #32
    //     0xac6d20: b.eq            #0xac6d28
    //     0xac6d24: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xac6d28: r0 = true
    //     0xac6d28: add             x0, NULL, #0x20  ; true
    // 0xac6d2c: r0 = SuspendSyncStarAtYield()
    //     0xac6d2c: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0xac6d30: ldur            x0, [fp, #-0x10]
    // 0xac6d34: ldur            x2, [fp, #-0x20]
    // 0xac6d38: b               #0xac6cd0
    // 0xac6d3c: r0 = false
    //     0xac6d3c: add             x0, NULL, #0x30  ; false
    // 0xac6d40: LeaveFrame
    //     0xac6d40: mov             SP, fp
    //     0xac6d44: ldp             fp, lr, [SP], #0x10
    // 0xac6d48: ret
    //     0xac6d48: ret             
    // 0xac6d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac6d4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac6d50: b               #0xac6cb0
    // 0xac6d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac6d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac6d58: b               #0xac6ce0
  }
  _ parseSegment(/* No info */) {
    // ** addr: 0xac6d5c, size: 0x5d8
    // 0xac6d5c: EnterFrame
    //     0xac6d5c: stp             fp, lr, [SP, #-0x10]!
    //     0xac6d60: mov             fp, SP
    // 0xac6d64: AllocStack(0x28)
    //     0xac6d64: sub             SP, SP, #0x28
    // 0xac6d68: SetupParameters(SvgPathStringSource this /* r1 => r1, fp-0x8 */)
    //     0xac6d68: stur            x1, [fp, #-8]
    // 0xac6d6c: CheckStackOverflow
    //     0xac6d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6d70: cmp             SP, x16
    //     0xac6d74: b.ls            #0xac7328
    // 0xac6d78: r0 = PathSegmentData()
    //     0xac6d78: bl              #0xac803c  ; AllocatePathSegmentDataStub -> PathSegmentData (size=0x20)
    // 0xac6d7c: mov             x2, x0
    // 0xac6d80: r0 = Instance__PathOffset
    //     0xac6d80: add             x0, PP, #0x26, lsl #12  ; [pp+0x26158] Obj!_PathOffset@e0e3f1
    //     0xac6d84: ldr             x0, [x0, #0x158]
    // 0xac6d88: stur            x2, [fp, #-0x18]
    // 0xac6d8c: StoreField: r2->field_b = r0
    //     0xac6d8c: stur            w0, [x2, #0xb]
    // 0xac6d90: StoreField: r2->field_f = r0
    //     0xac6d90: stur            w0, [x2, #0xf]
    // 0xac6d94: StoreField: r2->field_13 = r0
    //     0xac6d94: stur            w0, [x2, #0x13]
    // 0xac6d98: r0 = Instance_SvgPathSegType
    //     0xac6d98: add             x0, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac6d9c: ldr             x0, [x0, #0x150]
    // 0xac6da0: StoreField: r2->field_7 = r0
    //     0xac6da0: stur            w0, [x2, #7]
    // 0xac6da4: r0 = false
    //     0xac6da4: add             x0, NULL, #0x30  ; false
    // 0xac6da8: ArrayStore: r2[0] = r0  ; List_4
    //     0xac6da8: stur            w0, [x2, #0x17]
    // 0xac6dac: StoreField: r2->field_1b = r0
    //     0xac6dac: stur            w0, [x2, #0x1b]
    // 0xac6db0: ldur            x3, [fp, #-8]
    // 0xac6db4: LoadField: r4 = r3->field_7
    //     0xac6db4: ldur            w4, [x3, #7]
    // 0xac6db8: DecompressPointer r4
    //     0xac6db8: add             x4, x4, HEAP, lsl #32
    // 0xac6dbc: LoadField: r5 = r3->field_f
    //     0xac6dbc: ldur            x5, [x3, #0xf]
    // 0xac6dc0: LoadField: r0 = r4->field_7
    //     0xac6dc0: ldur            w0, [x4, #7]
    // 0xac6dc4: r1 = LoadInt32Instr(r0)
    //     0xac6dc4: sbfx            x1, x0, #1, #0x1f
    // 0xac6dc8: mov             x0, x1
    // 0xac6dcc: mov             x1, x5
    // 0xac6dd0: cmp             x1, x0
    // 0xac6dd4: b.hs            #0xac7330
    // 0xac6dd8: r0 = LoadClassIdInstr(r4)
    //     0xac6dd8: ldur            x0, [x4, #-1]
    //     0xac6ddc: ubfx            x0, x0, #0xc, #0x14
    // 0xac6de0: lsl             x0, x0, #1
    // 0xac6de4: cmp             w0, #0xbc
    // 0xac6de8: b.ne            #0xac6df8
    // 0xac6dec: ArrayLoad: r0 = r4[r5]  ; TypedUnsigned_1
    //     0xac6dec: add             x16, x4, x5
    //     0xac6df0: ldrb            w0, [x16, #0xf]
    // 0xac6df4: b               #0xac6e00
    // 0xac6df8: add             x16, x4, x5, lsl #1
    // 0xac6dfc: ldurh           w0, [x16, #0xf]
    // 0xac6e00: mov             x1, x0
    // 0xac6e04: stur            x0, [fp, #-0x10]
    // 0xac6e08: r0 = mapLetterToSegmentType()
    //     0xac6e08: bl              #0xac7ff4  ; [package:path_parsing/src/path_segment_type.dart] AsciiConstants::mapLetterToSegmentType
    // 0xac6e0c: mov             x1, x0
    // 0xac6e10: ldur            x0, [fp, #-8]
    // 0xac6e14: LoadField: r2 = r0->field_b
    //     0xac6e14: ldur            w2, [x0, #0xb]
    // 0xac6e18: DecompressPointer r2
    //     0xac6e18: add             x2, x2, HEAP, lsl #32
    // 0xac6e1c: r16 = Instance_SvgPathSegType
    //     0xac6e1c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac6e20: ldr             x16, [x16, #0x150]
    // 0xac6e24: cmp             w2, w16
    // 0xac6e28: b.ne            #0xac6e60
    // 0xac6e2c: r16 = Instance_SvgPathSegType
    //     0xac6e2c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26228] Obj!SvgPathSegType@e2ff81
    //     0xac6e30: ldr             x16, [x16, #0x228]
    // 0xac6e34: cmp             w1, w16
    // 0xac6e38: b.eq            #0xac6e4c
    // 0xac6e3c: r16 = Instance_SvgPathSegType
    //     0xac6e3c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26230] Obj!SvgPathSegType@e2ff61
    //     0xac6e40: ldr             x16, [x16, #0x230]
    // 0xac6e44: cmp             w1, w16
    // 0xac6e48: b.ne            #0xac72c8
    // 0xac6e4c: LoadField: r2 = r0->field_f
    //     0xac6e4c: ldur            x2, [x0, #0xf]
    // 0xac6e50: add             x3, x2, #1
    // 0xac6e54: StoreField: r0->field_f = r3
    //     0xac6e54: stur            x3, [x0, #0xf]
    // 0xac6e58: mov             x2, x0
    // 0xac6e5c: b               #0xac6eac
    // 0xac6e60: r16 = Instance_SvgPathSegType
    //     0xac6e60: add             x16, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac6e64: ldr             x16, [x16, #0x150]
    // 0xac6e68: cmp             w1, w16
    // 0xac6e6c: b.ne            #0xac6e94
    // 0xac6e70: mov             x1, x0
    // 0xac6e74: ldur            x2, [fp, #-0x10]
    // 0xac6e78: r0 = _maybeImplicitCommand()
    //     0xac6e78: bl              #0xac7f6c  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_maybeImplicitCommand
    // 0xac6e7c: r16 = Instance_SvgPathSegType
    //     0xac6e7c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac6e80: ldr             x16, [x16, #0x150]
    // 0xac6e84: cmp             w0, w16
    // 0xac6e88: b.eq            #0xac72e8
    // 0xac6e8c: ldur            x2, [fp, #-8]
    // 0xac6e90: b               #0xac6ea8
    // 0xac6e94: mov             x2, x0
    // 0xac6e98: LoadField: r0 = r2->field_f
    //     0xac6e98: ldur            x0, [x2, #0xf]
    // 0xac6e9c: add             x3, x0, #1
    // 0xac6ea0: StoreField: r2->field_f = r3
    //     0xac6ea0: stur            x3, [x2, #0xf]
    // 0xac6ea4: mov             x0, x1
    // 0xac6ea8: mov             x1, x0
    // 0xac6eac: ldur            x3, [fp, #-0x18]
    // 0xac6eb0: mov             x0, x1
    // 0xac6eb4: StoreField: r2->field_b = r0
    //     0xac6eb4: stur            w0, [x2, #0xb]
    //     0xac6eb8: ldurb           w16, [x2, #-1]
    //     0xac6ebc: ldurb           w17, [x0, #-1]
    //     0xac6ec0: and             x16, x17, x16, lsr #2
    //     0xac6ec4: tst             x16, HEAP, lsr #32
    //     0xac6ec8: b.eq            #0xac6ed0
    //     0xac6ecc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac6ed0: mov             x0, x1
    // 0xac6ed4: StoreField: r3->field_7 = r0
    //     0xac6ed4: stur            w0, [x3, #7]
    //     0xac6ed8: ldurb           w16, [x3, #-1]
    //     0xac6edc: ldurb           w17, [x0, #-1]
    //     0xac6ee0: and             x16, x17, x16, lsr #2
    //     0xac6ee4: tst             x16, HEAP, lsr #32
    //     0xac6ee8: b.eq            #0xac6ef0
    //     0xac6eec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xac6ef0: LoadField: r4 = r1->field_7
    //     0xac6ef0: ldur            x4, [x1, #7]
    // 0xac6ef4: r0 = BoxInt64Instr(r4)
    //     0xac6ef4: sbfiz           x0, x4, #1, #0x1f
    //     0xac6ef8: cmp             x4, x0, asr #1
    //     0xac6efc: b.eq            #0xac6f08
    //     0xac6f00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xac6f04: stur            x4, [x0, #7]
    // 0xac6f08: r1 = _Int32List
    //     0xac6f08: add             x1, PP, #0x26, lsl #12  ; [pp+0x26238] _Int32List(20) [0x5ac, 0x1c8, 0x1d8, 0x1e0, 0x1e8, 0x1f0, 0x1f8, 0x1f8, 0x24c, 0x254, 0x2f8, 0x300, 0x3dc, 0x3e4, 0x444, 0x44c, 0x4a8, 0x4b0, 0x504, 0x50c]
    //     0xac6f0c: ldr             x1, [x1, #0x238]
    // 0xac6f10: ArrayLoad: r1 = r1[r0]  ; TypedSigned_4
    //     0xac6f10: add             x16, x1, w0, sxtw #1
    //     0xac6f14: ldursw          x1, [x16, #0x17]
    // 0xac6f18: adr             x4, #0xac6d5c
    // 0xac6f1c: add             x4, x4, x1
    // 0xac6f20: br              x4
    // 0xac6f24: mov             x1, x2
    // 0xac6f28: r0 = _skipOptionalSvgSpaces()
    //     0xac6f28: bl              #0xac7e90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpaces
    // 0xac6f2c: ldur            x1, [fp, #-0x18]
    // 0xac6f30: b               #0xac72b8
    // 0xac6f34: ldur            x2, [fp, #-0x18]
    // 0xac6f38: b               #0xac726c
    // 0xac6f3c: ldur            x2, [fp, #-0x18]
    // 0xac6f40: b               #0xac726c
    // 0xac6f44: ldur            x2, [fp, #-0x18]
    // 0xac6f48: b               #0xac726c
    // 0xac6f4c: ldur            x2, [fp, #-0x18]
    // 0xac6f50: b               #0xac726c
    // 0xac6f54: ldur            x0, [fp, #-0x18]
    // 0xac6f58: mov             x1, x2
    // 0xac6f5c: r0 = _parseNumber()
    //     0xac6f5c: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac6f60: ldur            x1, [fp, #-8]
    // 0xac6f64: stur            d0, [fp, #-0x20]
    // 0xac6f68: r0 = _parseNumber()
    //     0xac6f68: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac6f6c: stur            d0, [fp, #-0x28]
    // 0xac6f70: r0 = _PathOffset()
    //     0xac6f70: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac6f74: ldur            d0, [fp, #-0x20]
    // 0xac6f78: StoreField: r0->field_7 = d0
    //     0xac6f78: stur            d0, [x0, #7]
    // 0xac6f7c: ldur            d0, [fp, #-0x28]
    // 0xac6f80: StoreField: r0->field_f = d0
    //     0xac6f80: stur            d0, [x0, #0xf]
    // 0xac6f84: ldur            x2, [fp, #-0x18]
    // 0xac6f88: StoreField: r2->field_f = r0
    //     0xac6f88: stur            w0, [x2, #0xf]
    //     0xac6f8c: ldurb           w16, [x2, #-1]
    //     0xac6f90: ldurb           w17, [x0, #-1]
    //     0xac6f94: and             x16, x17, x16, lsr #2
    //     0xac6f98: tst             x16, HEAP, lsr #32
    //     0xac6f9c: b.eq            #0xac6fa4
    //     0xac6fa0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac6fa4: b               #0xac7210
    // 0xac6fa8: mov             x2, x3
    // 0xac6fac: b               #0xac6fb4
    // 0xac6fb0: mov             x2, x3
    // 0xac6fb4: ldur            x1, [fp, #-8]
    // 0xac6fb8: r0 = _parseNumber()
    //     0xac6fb8: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac6fbc: ldur            x1, [fp, #-8]
    // 0xac6fc0: stur            d0, [fp, #-0x20]
    // 0xac6fc4: r0 = _parseNumber()
    //     0xac6fc4: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac6fc8: stur            d0, [fp, #-0x28]
    // 0xac6fcc: r0 = _PathOffset()
    //     0xac6fcc: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac6fd0: ldur            d0, [fp, #-0x20]
    // 0xac6fd4: StoreField: r0->field_7 = d0
    //     0xac6fd4: stur            d0, [x0, #7]
    // 0xac6fd8: ldur            d0, [fp, #-0x28]
    // 0xac6fdc: StoreField: r0->field_f = d0
    //     0xac6fdc: stur            d0, [x0, #0xf]
    // 0xac6fe0: ldur            x2, [fp, #-0x18]
    // 0xac6fe4: StoreField: r2->field_f = r0
    //     0xac6fe4: stur            w0, [x2, #0xf]
    //     0xac6fe8: ldurb           w16, [x2, #-1]
    //     0xac6fec: ldurb           w17, [x0, #-1]
    //     0xac6ff0: and             x16, x17, x16, lsr #2
    //     0xac6ff4: tst             x16, HEAP, lsr #32
    //     0xac6ff8: b.eq            #0xac7000
    //     0xac6ffc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac7000: ldur            x1, [fp, #-8]
    // 0xac7004: r0 = _parseNumber()
    //     0xac7004: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7008: ldur            x1, [fp, #-8]
    // 0xac700c: stur            d0, [fp, #-0x20]
    // 0xac7010: r0 = _parseNumber()
    //     0xac7010: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7014: stur            d0, [fp, #-0x28]
    // 0xac7018: r0 = _PathOffset()
    //     0xac7018: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac701c: ldur            d0, [fp, #-0x20]
    // 0xac7020: StoreField: r0->field_7 = d0
    //     0xac7020: stur            d0, [x0, #7]
    // 0xac7024: ldur            d0, [fp, #-0x28]
    // 0xac7028: StoreField: r0->field_f = d0
    //     0xac7028: stur            d0, [x0, #0xf]
    // 0xac702c: ldur            x2, [fp, #-0x18]
    // 0xac7030: StoreField: r2->field_b = r0
    //     0xac7030: stur            w0, [x2, #0xb]
    //     0xac7034: ldurb           w16, [x2, #-1]
    //     0xac7038: ldurb           w17, [x0, #-1]
    //     0xac703c: and             x16, x17, x16, lsr #2
    //     0xac7040: tst             x16, HEAP, lsr #32
    //     0xac7044: b.eq            #0xac704c
    //     0xac7048: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac704c: mov             x1, x2
    // 0xac7050: b               #0xac72b8
    // 0xac7054: mov             x2, x3
    // 0xac7058: b               #0xac7060
    // 0xac705c: mov             x2, x3
    // 0xac7060: ldur            x1, [fp, #-8]
    // 0xac7064: r0 = _parseNumber()
    //     0xac7064: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7068: ldur            x1, [fp, #-8]
    // 0xac706c: stur            d0, [fp, #-0x20]
    // 0xac7070: r0 = _parseNumber()
    //     0xac7070: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7074: stur            d0, [fp, #-0x28]
    // 0xac7078: r0 = _PathOffset()
    //     0xac7078: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac707c: ldur            d0, [fp, #-0x20]
    // 0xac7080: StoreField: r0->field_7 = d0
    //     0xac7080: stur            d0, [x0, #7]
    // 0xac7084: ldur            d0, [fp, #-0x28]
    // 0xac7088: StoreField: r0->field_f = d0
    //     0xac7088: stur            d0, [x0, #0xf]
    // 0xac708c: ldur            x2, [fp, #-0x18]
    // 0xac7090: StoreField: r2->field_f = r0
    //     0xac7090: stur            w0, [x2, #0xf]
    //     0xac7094: ldurb           w16, [x2, #-1]
    //     0xac7098: ldurb           w17, [x0, #-1]
    //     0xac709c: and             x16, x17, x16, lsr #2
    //     0xac70a0: tst             x16, HEAP, lsr #32
    //     0xac70a4: b.eq            #0xac70ac
    //     0xac70a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac70ac: ldur            x1, [fp, #-8]
    // 0xac70b0: r0 = _parseNumber()
    //     0xac70b0: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac70b4: ldur            x1, [fp, #-0x18]
    // 0xac70b8: r0 = arcAngle=()
    //     0xac70b8: bl              #0xac7498  ; [package:path_parsing/src/path_parsing.dart] PathSegmentData::arcAngle=
    // 0xac70bc: ldur            x1, [fp, #-8]
    // 0xac70c0: r0 = _parseArcFlag()
    //     0xac70c0: bl              #0xac7334  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseArcFlag
    // 0xac70c4: mov             x1, x0
    // 0xac70c8: ldur            x0, [fp, #-0x18]
    // 0xac70cc: StoreField: r0->field_1b = r1
    //     0xac70cc: stur            w1, [x0, #0x1b]
    // 0xac70d0: ldur            x1, [fp, #-8]
    // 0xac70d4: r0 = _parseArcFlag()
    //     0xac70d4: bl              #0xac7334  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseArcFlag
    // 0xac70d8: mov             x1, x0
    // 0xac70dc: ldur            x0, [fp, #-0x18]
    // 0xac70e0: ArrayStore: r0[0] = r1  ; List_4
    //     0xac70e0: stur            w1, [x0, #0x17]
    // 0xac70e4: ldur            x1, [fp, #-8]
    // 0xac70e8: r0 = _parseNumber()
    //     0xac70e8: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac70ec: ldur            x1, [fp, #-8]
    // 0xac70f0: stur            d0, [fp, #-0x20]
    // 0xac70f4: r0 = _parseNumber()
    //     0xac70f4: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac70f8: stur            d0, [fp, #-0x28]
    // 0xac70fc: r0 = _PathOffset()
    //     0xac70fc: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac7100: ldur            d0, [fp, #-0x20]
    // 0xac7104: StoreField: r0->field_7 = d0
    //     0xac7104: stur            d0, [x0, #7]
    // 0xac7108: ldur            d0, [fp, #-0x28]
    // 0xac710c: StoreField: r0->field_f = d0
    //     0xac710c: stur            d0, [x0, #0xf]
    // 0xac7110: ldur            x2, [fp, #-0x18]
    // 0xac7114: StoreField: r2->field_b = r0
    //     0xac7114: stur            w0, [x2, #0xb]
    //     0xac7118: ldurb           w16, [x2, #-1]
    //     0xac711c: ldurb           w17, [x0, #-1]
    //     0xac7120: and             x16, x17, x16, lsr #2
    //     0xac7124: tst             x16, HEAP, lsr #32
    //     0xac7128: b.eq            #0xac7130
    //     0xac712c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac7130: mov             x1, x2
    // 0xac7134: b               #0xac72b8
    // 0xac7138: mov             x2, x3
    // 0xac713c: b               #0xac7144
    // 0xac7140: mov             x2, x3
    // 0xac7144: ldur            x1, [fp, #-8]
    // 0xac7148: r0 = _parseNumber()
    //     0xac7148: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac714c: ldur            x0, [fp, #-0x18]
    // 0xac7150: stur            d0, [fp, #-0x28]
    // 0xac7154: LoadField: r1 = r0->field_b
    //     0xac7154: ldur            w1, [x0, #0xb]
    // 0xac7158: DecompressPointer r1
    //     0xac7158: add             x1, x1, HEAP, lsl #32
    // 0xac715c: LoadField: d1 = r1->field_f
    //     0xac715c: ldur            d1, [x1, #0xf]
    // 0xac7160: stur            d1, [fp, #-0x20]
    // 0xac7164: r0 = _PathOffset()
    //     0xac7164: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac7168: ldur            d0, [fp, #-0x28]
    // 0xac716c: StoreField: r0->field_7 = d0
    //     0xac716c: stur            d0, [x0, #7]
    // 0xac7170: ldur            d0, [fp, #-0x20]
    // 0xac7174: StoreField: r0->field_f = d0
    //     0xac7174: stur            d0, [x0, #0xf]
    // 0xac7178: ldur            x2, [fp, #-0x18]
    // 0xac717c: StoreField: r2->field_b = r0
    //     0xac717c: stur            w0, [x2, #0xb]
    //     0xac7180: ldurb           w16, [x2, #-1]
    //     0xac7184: ldurb           w17, [x0, #-1]
    //     0xac7188: and             x16, x17, x16, lsr #2
    //     0xac718c: tst             x16, HEAP, lsr #32
    //     0xac7190: b.eq            #0xac7198
    //     0xac7194: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac7198: mov             x1, x2
    // 0xac719c: b               #0xac72b8
    // 0xac71a0: mov             x2, x3
    // 0xac71a4: b               #0xac71ac
    // 0xac71a8: mov             x2, x3
    // 0xac71ac: LoadField: r0 = r2->field_b
    //     0xac71ac: ldur            w0, [x2, #0xb]
    // 0xac71b0: DecompressPointer r0
    //     0xac71b0: add             x0, x0, HEAP, lsl #32
    // 0xac71b4: LoadField: d0 = r0->field_7
    //     0xac71b4: ldur            d0, [x0, #7]
    // 0xac71b8: ldur            x1, [fp, #-8]
    // 0xac71bc: stur            d0, [fp, #-0x20]
    // 0xac71c0: r0 = _parseNumber()
    //     0xac71c0: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac71c4: stur            d0, [fp, #-0x28]
    // 0xac71c8: r0 = _PathOffset()
    //     0xac71c8: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac71cc: ldur            d0, [fp, #-0x20]
    // 0xac71d0: StoreField: r0->field_7 = d0
    //     0xac71d0: stur            d0, [x0, #7]
    // 0xac71d4: ldur            d0, [fp, #-0x28]
    // 0xac71d8: StoreField: r0->field_f = d0
    //     0xac71d8: stur            d0, [x0, #0xf]
    // 0xac71dc: ldur            x2, [fp, #-0x18]
    // 0xac71e0: StoreField: r2->field_b = r0
    //     0xac71e0: stur            w0, [x2, #0xb]
    //     0xac71e4: ldurb           w16, [x2, #-1]
    //     0xac71e8: ldurb           w17, [x0, #-1]
    //     0xac71ec: and             x16, x17, x16, lsr #2
    //     0xac71f0: tst             x16, HEAP, lsr #32
    //     0xac71f4: b.eq            #0xac71fc
    //     0xac71f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac71fc: mov             x1, x2
    // 0xac7200: b               #0xac72b8
    // 0xac7204: mov             x2, x3
    // 0xac7208: b               #0xac7210
    // 0xac720c: mov             x2, x3
    // 0xac7210: ldur            x1, [fp, #-8]
    // 0xac7214: r0 = _parseNumber()
    //     0xac7214: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7218: ldur            x1, [fp, #-8]
    // 0xac721c: stur            d0, [fp, #-0x20]
    // 0xac7220: r0 = _parseNumber()
    //     0xac7220: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7224: stur            d0, [fp, #-0x28]
    // 0xac7228: r0 = _PathOffset()
    //     0xac7228: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac722c: ldur            d0, [fp, #-0x20]
    // 0xac7230: StoreField: r0->field_7 = d0
    //     0xac7230: stur            d0, [x0, #7]
    // 0xac7234: ldur            d0, [fp, #-0x28]
    // 0xac7238: StoreField: r0->field_f = d0
    //     0xac7238: stur            d0, [x0, #0xf]
    // 0xac723c: ldur            x2, [fp, #-0x18]
    // 0xac7240: StoreField: r2->field_13 = r0
    //     0xac7240: stur            w0, [x2, #0x13]
    //     0xac7244: ldurb           w16, [x2, #-1]
    //     0xac7248: ldurb           w17, [x0, #-1]
    //     0xac724c: and             x16, x17, x16, lsr #2
    //     0xac7250: tst             x16, HEAP, lsr #32
    //     0xac7254: b.eq            #0xac725c
    //     0xac7258: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac725c: b               #0xac726c
    // 0xac7260: mov             x2, x3
    // 0xac7264: b               #0xac726c
    // 0xac7268: mov             x2, x3
    // 0xac726c: ldur            x1, [fp, #-8]
    // 0xac7270: r0 = _parseNumber()
    //     0xac7270: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7274: ldur            x1, [fp, #-8]
    // 0xac7278: stur            d0, [fp, #-0x20]
    // 0xac727c: r0 = _parseNumber()
    //     0xac727c: bl              #0xac7508  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_parseNumber
    // 0xac7280: stur            d0, [fp, #-0x28]
    // 0xac7284: r0 = _PathOffset()
    //     0xac7284: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac7288: ldur            d0, [fp, #-0x20]
    // 0xac728c: StoreField: r0->field_7 = d0
    //     0xac728c: stur            d0, [x0, #7]
    // 0xac7290: ldur            d0, [fp, #-0x28]
    // 0xac7294: StoreField: r0->field_f = d0
    //     0xac7294: stur            d0, [x0, #0xf]
    // 0xac7298: ldur            x1, [fp, #-0x18]
    // 0xac729c: StoreField: r1->field_b = r0
    //     0xac729c: stur            w0, [x1, #0xb]
    //     0xac72a0: ldurb           w16, [x1, #-1]
    //     0xac72a4: ldurb           w17, [x0, #-1]
    //     0xac72a8: and             x16, x17, x16, lsr #2
    //     0xac72ac: tst             x16, HEAP, lsr #32
    //     0xac72b0: b.eq            #0xac72b8
    //     0xac72b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xac72b8: mov             x0, x1
    // 0xac72bc: LeaveFrame
    //     0xac72bc: mov             SP, fp
    //     0xac72c0: ldp             fp, lr, [SP], #0x10
    // 0xac72c4: ret
    //     0xac72c4: ret             
    // 0xac72c8: r0 = StateError()
    //     0xac72c8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac72cc: mov             x1, x0
    // 0xac72d0: r0 = "Expected to find moveTo command"
    //     0xac72d0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26240] "Expected to find moveTo command"
    //     0xac72d4: ldr             x0, [x0, #0x240]
    // 0xac72d8: StoreField: r1->field_b = r0
    //     0xac72d8: stur            w0, [x1, #0xb]
    // 0xac72dc: mov             x0, x1
    // 0xac72e0: r0 = Throw()
    //     0xac72e0: bl              #0xec04b8  ; ThrowStub
    // 0xac72e4: brk             #0
    // 0xac72e8: r0 = StateError()
    //     0xac72e8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac72ec: mov             x1, x0
    // 0xac72f0: r0 = "Expected a path command"
    //     0xac72f0: add             x0, PP, #0x26, lsl #12  ; [pp+0x26248] "Expected a path command"
    //     0xac72f4: ldr             x0, [x0, #0x248]
    // 0xac72f8: StoreField: r1->field_b = r0
    //     0xac72f8: stur            w0, [x1, #0xb]
    // 0xac72fc: mov             x0, x1
    // 0xac7300: r0 = Throw()
    //     0xac7300: bl              #0xec04b8  ; ThrowStub
    // 0xac7304: brk             #0
    // 0xac7308: r0 = StateError()
    //     0xac7308: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac730c: mov             x1, x0
    // 0xac7310: r0 = "Unknown segment command"
    //     0xac7310: add             x0, PP, #0x26, lsl #12  ; [pp+0x26250] "Unknown segment command"
    //     0xac7314: ldr             x0, [x0, #0x250]
    // 0xac7318: StoreField: r1->field_b = r0
    //     0xac7318: stur            w0, [x1, #0xb]
    // 0xac731c: mov             x0, x1
    // 0xac7320: r0 = Throw()
    //     0xac7320: bl              #0xec04b8  ; ThrowStub
    // 0xac7324: brk             #0
    // 0xac7328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac7328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac732c: b               #0xac6d78
    // 0xac7330: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7330: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseArcFlag(/* No info */) {
    // ** addr: 0xac7334, size: 0x108
    // 0xac7334: EnterFrame
    //     0xac7334: stp             fp, lr, [SP, #-0x10]!
    //     0xac7338: mov             fp, SP
    // 0xac733c: AllocStack(0x8)
    //     0xac733c: sub             SP, SP, #8
    // 0xac7340: SetupParameters(SvgPathStringSource this /* r1 => r2 */)
    //     0xac7340: mov             x2, x1
    // 0xac7344: CheckStackOverflow
    //     0xac7344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7348: cmp             SP, x16
    //     0xac734c: b.ls            #0xac7430
    // 0xac7350: LoadField: r3 = r2->field_f
    //     0xac7350: ldur            x3, [x2, #0xf]
    // 0xac7354: ArrayLoad: r0 = r2[0]  ; List_8
    //     0xac7354: ldur            x0, [x2, #0x17]
    // 0xac7358: cmp             x3, x0
    // 0xac735c: b.ge            #0xac73f0
    // 0xac7360: LoadField: r4 = r2->field_7
    //     0xac7360: ldur            w4, [x2, #7]
    // 0xac7364: DecompressPointer r4
    //     0xac7364: add             x4, x4, HEAP, lsl #32
    // 0xac7368: add             x0, x3, #1
    // 0xac736c: StoreField: r2->field_f = r0
    //     0xac736c: stur            x0, [x2, #0xf]
    // 0xac7370: LoadField: r0 = r4->field_7
    //     0xac7370: ldur            w0, [x4, #7]
    // 0xac7374: r1 = LoadInt32Instr(r0)
    //     0xac7374: sbfx            x1, x0, #1, #0x1f
    // 0xac7378: mov             x0, x1
    // 0xac737c: mov             x1, x3
    // 0xac7380: cmp             x1, x0
    // 0xac7384: b.hs            #0xac7438
    // 0xac7388: r0 = LoadClassIdInstr(r4)
    //     0xac7388: ldur            x0, [x4, #-1]
    //     0xac738c: ubfx            x0, x0, #0xc, #0x14
    // 0xac7390: lsl             x0, x0, #1
    // 0xac7394: cmp             w0, #0xbc
    // 0xac7398: b.ne            #0xac73a8
    // 0xac739c: ArrayLoad: r0 = r4[r3]  ; TypedUnsigned_1
    //     0xac739c: add             x16, x4, x3
    //     0xac73a0: ldrb            w0, [x16, #0xf]
    // 0xac73a4: b               #0xac73b0
    // 0xac73a8: add             x16, x4, x3, lsl #1
    // 0xac73ac: ldurh           w0, [x16, #0xf]
    // 0xac73b0: mov             x1, x2
    // 0xac73b4: stur            x0, [fp, #-8]
    // 0xac73b8: r0 = _skipOptionalSvgSpacesOrDelimiter()
    //     0xac73b8: bl              #0xac743c  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpacesOrDelimiter
    // 0xac73bc: ldur            x0, [fp, #-8]
    // 0xac73c0: cmp             x0, #0x30
    // 0xac73c4: b.ne            #0xac73d8
    // 0xac73c8: r0 = false
    //     0xac73c8: add             x0, NULL, #0x30  ; false
    // 0xac73cc: LeaveFrame
    //     0xac73cc: mov             SP, fp
    //     0xac73d0: ldp             fp, lr, [SP], #0x10
    // 0xac73d4: ret
    //     0xac73d4: ret             
    // 0xac73d8: cmp             x0, #0x31
    // 0xac73dc: b.ne            #0xac7410
    // 0xac73e0: r0 = true
    //     0xac73e0: add             x0, NULL, #0x20  ; true
    // 0xac73e4: LeaveFrame
    //     0xac73e4: mov             SP, fp
    //     0xac73e8: ldp             fp, lr, [SP], #0x10
    // 0xac73ec: ret
    //     0xac73ec: ret             
    // 0xac73f0: r0 = StateError()
    //     0xac73f0: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac73f4: mov             x1, x0
    // 0xac73f8: r0 = "Expected more data"
    //     0xac73f8: add             x0, PP, #0x26, lsl #12  ; [pp+0x26258] "Expected more data"
    //     0xac73fc: ldr             x0, [x0, #0x258]
    // 0xac7400: StoreField: r1->field_b = r0
    //     0xac7400: stur            w0, [x1, #0xb]
    // 0xac7404: mov             x0, x1
    // 0xac7408: r0 = Throw()
    //     0xac7408: bl              #0xec04b8  ; ThrowStub
    // 0xac740c: brk             #0
    // 0xac7410: r0 = StateError()
    //     0xac7410: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7414: mov             x1, x0
    // 0xac7418: r0 = "Invalid flag value"
    //     0xac7418: add             x0, PP, #0x26, lsl #12  ; [pp+0x26260] "Invalid flag value"
    //     0xac741c: ldr             x0, [x0, #0x260]
    // 0xac7420: StoreField: r1->field_b = r0
    //     0xac7420: stur            w0, [x1, #0xb]
    // 0xac7424: mov             x0, x1
    // 0xac7428: r0 = Throw()
    //     0xac7428: bl              #0xec04b8  ; ThrowStub
    // 0xac742c: brk             #0
    // 0xac7430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac7430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac7434: b               #0xac7350
    // 0xac7438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7438: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _skipOptionalSvgSpacesOrDelimiter(/* No info */) {
    // ** addr: 0xac743c, size: 0x5c
    // 0xac743c: EnterFrame
    //     0xac743c: stp             fp, lr, [SP, #-0x10]!
    //     0xac7440: mov             fp, SP
    // 0xac7444: AllocStack(0x8)
    //     0xac7444: sub             SP, SP, #8
    // 0xac7448: SetupParameters(SvgPathStringSource this /* r1 => r0, fp-0x8 */)
    //     0xac7448: mov             x0, x1
    //     0xac744c: stur            x1, [fp, #-8]
    // 0xac7450: CheckStackOverflow
    //     0xac7450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7454: cmp             SP, x16
    //     0xac7458: b.ls            #0xac7490
    // 0xac745c: mov             x1, x0
    // 0xac7460: r0 = _skipOptionalSvgSpaces()
    //     0xac7460: bl              #0xac7e90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpaces
    // 0xac7464: cmp             x0, #0x2c
    // 0xac7468: b.ne            #0xac7480
    // 0xac746c: ldur            x1, [fp, #-8]
    // 0xac7470: LoadField: r0 = r1->field_f
    //     0xac7470: ldur            x0, [x1, #0xf]
    // 0xac7474: add             x2, x0, #1
    // 0xac7478: StoreField: r1->field_f = r2
    //     0xac7478: stur            x2, [x1, #0xf]
    // 0xac747c: r0 = _skipOptionalSvgSpaces()
    //     0xac747c: bl              #0xac7e90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpaces
    // 0xac7480: r0 = Null
    //     0xac7480: mov             x0, NULL
    // 0xac7484: LeaveFrame
    //     0xac7484: mov             SP, fp
    //     0xac7488: ldp             fp, lr, [SP], #0x10
    // 0xac748c: ret
    //     0xac748c: ret             
    // 0xac7490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac7490: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac7494: b               #0xac745c
  }
  _ _parseNumber(/* No info */) {
    // ** addr: 0xac7508, size: 0x988
    // 0xac7508: EnterFrame
    //     0xac7508: stp             fp, lr, [SP, #-0x10]!
    //     0xac750c: mov             fp, SP
    // 0xac7510: AllocStack(0x30)
    //     0xac7510: sub             SP, SP, #0x30
    // 0xac7514: SetupParameters(SvgPathStringSource this /* r1 => r0, fp-0x8 */)
    //     0xac7514: mov             x0, x1
    //     0xac7518: stur            x1, [fp, #-8]
    // 0xac751c: CheckStackOverflow
    //     0xac751c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7520: cmp             SP, x16
    //     0xac7524: b.ls            #0xac7e2c
    // 0xac7528: mov             x1, x0
    // 0xac752c: r0 = _skipOptionalSvgSpaces()
    //     0xac752c: bl              #0xac7e90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpaces
    // 0xac7530: ldur            x2, [fp, #-8]
    // 0xac7534: LoadField: r3 = r2->field_f
    //     0xac7534: ldur            x3, [x2, #0xf]
    // 0xac7538: ArrayLoad: r4 = r2[0]  ; List_8
    //     0xac7538: ldur            x4, [x2, #0x17]
    // 0xac753c: cmp             x3, x4
    // 0xac7540: b.lt            #0xac754c
    // 0xac7544: r0 = -1
    //     0xac7544: movn            x0, #0
    // 0xac7548: b               #0xac75a0
    // 0xac754c: LoadField: r5 = r2->field_7
    //     0xac754c: ldur            w5, [x2, #7]
    // 0xac7550: DecompressPointer r5
    //     0xac7550: add             x5, x5, HEAP, lsl #32
    // 0xac7554: add             x6, x3, #1
    // 0xac7558: StoreField: r2->field_f = r6
    //     0xac7558: stur            x6, [x2, #0xf]
    // 0xac755c: LoadField: r0 = r5->field_7
    //     0xac755c: ldur            w0, [x5, #7]
    // 0xac7560: r1 = LoadInt32Instr(r0)
    //     0xac7560: sbfx            x1, x0, #1, #0x1f
    // 0xac7564: mov             x0, x1
    // 0xac7568: mov             x1, x3
    // 0xac756c: cmp             x1, x0
    // 0xac7570: b.hs            #0xac7e34
    // 0xac7574: r0 = LoadClassIdInstr(r5)
    //     0xac7574: ldur            x0, [x5, #-1]
    //     0xac7578: ubfx            x0, x0, #0xc, #0x14
    // 0xac757c: lsl             x0, x0, #1
    // 0xac7580: cmp             w0, #0xbc
    // 0xac7584: b.ne            #0xac7594
    // 0xac7588: ArrayLoad: r0 = r5[r3]  ; TypedUnsigned_1
    //     0xac7588: add             x16, x5, x3
    //     0xac758c: ldrb            w0, [x16, #0xf]
    // 0xac7590: b               #0xac759c
    // 0xac7594: add             x16, x5, x3, lsl #1
    // 0xac7598: ldurh           w0, [x16, #0xf]
    // 0xac759c: mov             x3, x6
    // 0xac75a0: cmp             x0, #0x2b
    // 0xac75a4: b.ne            #0xac761c
    // 0xac75a8: cmp             x3, x4
    // 0xac75ac: b.lt            #0xac75bc
    // 0xac75b0: mov             x0, x3
    // 0xac75b4: r1 = -1
    //     0xac75b4: movn            x1, #0
    // 0xac75b8: b               #0xac7614
    // 0xac75bc: LoadField: r5 = r2->field_7
    //     0xac75bc: ldur            w5, [x2, #7]
    // 0xac75c0: DecompressPointer r5
    //     0xac75c0: add             x5, x5, HEAP, lsl #32
    // 0xac75c4: add             x6, x3, #1
    // 0xac75c8: StoreField: r2->field_f = r6
    //     0xac75c8: stur            x6, [x2, #0xf]
    // 0xac75cc: LoadField: r0 = r5->field_7
    //     0xac75cc: ldur            w0, [x5, #7]
    // 0xac75d0: r1 = LoadInt32Instr(r0)
    //     0xac75d0: sbfx            x1, x0, #1, #0x1f
    // 0xac75d4: mov             x0, x1
    // 0xac75d8: mov             x1, x3
    // 0xac75dc: cmp             x1, x0
    // 0xac75e0: b.hs            #0xac7e38
    // 0xac75e4: r0 = LoadClassIdInstr(r5)
    //     0xac75e4: ldur            x0, [x5, #-1]
    //     0xac75e8: ubfx            x0, x0, #0xc, #0x14
    // 0xac75ec: lsl             x0, x0, #1
    // 0xac75f0: cmp             w0, #0xbc
    // 0xac75f4: b.ne            #0xac7604
    // 0xac75f8: ArrayLoad: r0 = r5[r3]  ; TypedUnsigned_1
    //     0xac75f8: add             x16, x5, x3
    //     0xac75fc: ldrb            w0, [x16, #0xf]
    // 0xac7600: b               #0xac760c
    // 0xac7604: add             x16, x5, x3, lsl #1
    // 0xac7608: ldurh           w0, [x16, #0xf]
    // 0xac760c: mov             x1, x0
    // 0xac7610: mov             x0, x6
    // 0xac7614: r3 = 1
    //     0xac7614: movz            x3, #0x1
    // 0xac7618: b               #0xac76a4
    // 0xac761c: cmp             x0, #0x2d
    // 0xac7620: b.ne            #0xac7698
    // 0xac7624: cmp             x3, x4
    // 0xac7628: b.lt            #0xac7638
    // 0xac762c: mov             x0, x3
    // 0xac7630: r1 = -1
    //     0xac7630: movn            x1, #0
    // 0xac7634: b               #0xac7690
    // 0xac7638: LoadField: r5 = r2->field_7
    //     0xac7638: ldur            w5, [x2, #7]
    // 0xac763c: DecompressPointer r5
    //     0xac763c: add             x5, x5, HEAP, lsl #32
    // 0xac7640: add             x6, x3, #1
    // 0xac7644: StoreField: r2->field_f = r6
    //     0xac7644: stur            x6, [x2, #0xf]
    // 0xac7648: LoadField: r0 = r5->field_7
    //     0xac7648: ldur            w0, [x5, #7]
    // 0xac764c: r1 = LoadInt32Instr(r0)
    //     0xac764c: sbfx            x1, x0, #1, #0x1f
    // 0xac7650: mov             x0, x1
    // 0xac7654: mov             x1, x3
    // 0xac7658: cmp             x1, x0
    // 0xac765c: b.hs            #0xac7e3c
    // 0xac7660: r0 = LoadClassIdInstr(r5)
    //     0xac7660: ldur            x0, [x5, #-1]
    //     0xac7664: ubfx            x0, x0, #0xc, #0x14
    // 0xac7668: lsl             x0, x0, #1
    // 0xac766c: cmp             w0, #0xbc
    // 0xac7670: b.ne            #0xac7680
    // 0xac7674: ArrayLoad: r0 = r5[r3]  ; TypedUnsigned_1
    //     0xac7674: add             x16, x5, x3
    //     0xac7678: ldrb            w0, [x16, #0xf]
    // 0xac767c: b               #0xac7688
    // 0xac7680: add             x16, x5, x3, lsl #1
    // 0xac7684: ldurh           w0, [x16, #0xf]
    // 0xac7688: mov             x1, x0
    // 0xac768c: mov             x0, x6
    // 0xac7690: r3 = -1
    //     0xac7690: movn            x3, #0
    // 0xac7694: b               #0xac76a4
    // 0xac7698: mov             x1, x0
    // 0xac769c: mov             x0, x3
    // 0xac76a0: r3 = 1
    //     0xac76a0: movz            x3, #0x1
    // 0xac76a4: cmp             x1, #0x30
    // 0xac76a8: b.lt            #0xac76b4
    // 0xac76ac: cmp             x1, #0x39
    // 0xac76b0: b.le            #0xac76bc
    // 0xac76b4: cmp             x1, #0x2e
    // 0xac76b8: b.ne            #0xac7d08
    // 0xac76bc: LoadField: r5 = r2->field_7
    //     0xac76bc: ldur            w5, [x2, #7]
    // 0xac76c0: DecompressPointer r5
    //     0xac76c0: add             x5, x5, HEAP, lsl #32
    // 0xac76c4: LoadField: r6 = r5->field_7
    //     0xac76c4: ldur            w6, [x5, #7]
    // 0xac76c8: r7 = LoadInt32Instr(r6)
    //     0xac76c8: sbfx            x7, x6, #1, #0x1f
    // 0xac76cc: r6 = LoadClassIdInstr(r5)
    //     0xac76cc: ldur            x6, [x5, #-1]
    //     0xac76d0: ubfx            x6, x6, #0xc, #0x14
    // 0xac76d4: lsl             x6, x6, #1
    // 0xac76d8: mov             x8, x0
    // 0xac76dc: mov             x0, x1
    // 0xac76e0: d1 = 0.000000
    //     0xac76e0: eor             v1.16b, v1.16b, v1.16b
    // 0xac76e4: d0 = 10.000000
    //     0xac76e4: fmov            d0, #10.00000000
    // 0xac76e8: CheckStackOverflow
    //     0xac76e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac76ec: cmp             SP, x16
    //     0xac76f0: b.ls            #0xac7e40
    // 0xac76f4: cmp             x0, #0x30
    // 0xac76f8: b.lt            #0xac7764
    // 0xac76fc: cmp             x0, #0x39
    // 0xac7700: b.gt            #0xac7764
    // 0xac7704: fmul            d2, d1, d0
    // 0xac7708: sub             x1, x0, #0x30
    // 0xac770c: scvtf           d1, x1
    // 0xac7710: fadd            d3, d2, d1
    // 0xac7714: cmp             x8, x4
    // 0xac7718: b.lt            #0xac7724
    // 0xac771c: r0 = -1
    //     0xac771c: movn            x0, #0
    // 0xac7720: b               #0xac775c
    // 0xac7724: add             x9, x8, #1
    // 0xac7728: StoreField: r2->field_f = r9
    //     0xac7728: stur            x9, [x2, #0xf]
    // 0xac772c: mov             x0, x7
    // 0xac7730: mov             x1, x8
    // 0xac7734: cmp             x1, x0
    // 0xac7738: b.hs            #0xac7e48
    // 0xac773c: cmp             w6, #0xbc
    // 0xac7740: b.ne            #0xac7750
    // 0xac7744: ArrayLoad: r0 = r5[r8]  ; TypedUnsigned_1
    //     0xac7744: add             x16, x5, x8
    //     0xac7748: ldrb            w0, [x16, #0xf]
    // 0xac774c: b               #0xac7758
    // 0xac7750: add             x16, x5, x8, lsl #1
    // 0xac7754: ldurh           w0, [x16, #0xf]
    // 0xac7758: mov             x8, x9
    // 0xac775c: mov             v1.16b, v3.16b
    // 0xac7760: b               #0xac76e8
    // 0xac7764: d2 = -179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xac7764: add             x17, PP, #0x26, lsl #12  ; [pp+0x26268] IMM: double(-1.7976931348623157e+308) from 0xffefffffffffffff
    //     0xac7768: ldr             d2, [x17, #0x268]
    // 0xac776c: fcmp            d1, d2
    // 0xac7770: b.lt            #0xac7e04
    // 0xac7774: d3 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xac7774: add             x17, PP, #0x26, lsl #12  ; [pp+0x26270] IMM: double(1.7976931348623157e+308) from 0x7fefffffffffffff
    //     0xac7778: ldr             d3, [x17, #0x270]
    // 0xac777c: fcmp            d3, d1
    // 0xac7780: r16 = true
    //     0xac7780: add             x16, NULL, #0x20  ; true
    // 0xac7784: r17 = false
    //     0xac7784: add             x17, NULL, #0x30  ; false
    // 0xac7788: csel            x1, x16, x17, ge
    // 0xac778c: tbnz            w1, #4, #0xac7d28
    // 0xac7790: cmp             x0, #0x2e
    // 0xac7794: b.ne            #0xac78c8
    // 0xac7798: cmp             x8, x4
    // 0xac779c: b.lt            #0xac77ac
    // 0xac77a0: mov             x0, x8
    // 0xac77a4: r1 = -1
    //     0xac77a4: movn            x1, #0
    // 0xac77a8: b               #0xac77fc
    // 0xac77ac: add             x6, x8, #1
    // 0xac77b0: StoreField: r2->field_f = r6
    //     0xac77b0: stur            x6, [x2, #0xf]
    // 0xac77b4: LoadField: r0 = r5->field_7
    //     0xac77b4: ldur            w0, [x5, #7]
    // 0xac77b8: r1 = LoadInt32Instr(r0)
    //     0xac77b8: sbfx            x1, x0, #1, #0x1f
    // 0xac77bc: mov             x0, x1
    // 0xac77c0: mov             x1, x8
    // 0xac77c4: cmp             x1, x0
    // 0xac77c8: b.hs            #0xac7e4c
    // 0xac77cc: r0 = LoadClassIdInstr(r5)
    //     0xac77cc: ldur            x0, [x5, #-1]
    //     0xac77d0: ubfx            x0, x0, #0xc, #0x14
    // 0xac77d4: lsl             x0, x0, #1
    // 0xac77d8: cmp             w0, #0xbc
    // 0xac77dc: b.ne            #0xac77ec
    // 0xac77e0: ArrayLoad: r0 = r5[r8]  ; TypedUnsigned_1
    //     0xac77e0: add             x16, x5, x8
    //     0xac77e4: ldrb            w0, [x16, #0xf]
    // 0xac77e8: b               #0xac77f4
    // 0xac77ec: add             x16, x5, x8, lsl #1
    // 0xac77f0: ldurh           w0, [x16, #0xf]
    // 0xac77f4: mov             x1, x0
    // 0xac77f8: mov             x0, x6
    // 0xac77fc: cmp             x1, #0x30
    // 0xac7800: b.lt            #0xac7d34
    // 0xac7804: cmp             x1, #0x39
    // 0xac7808: b.gt            #0xac7d34
    // 0xac780c: LoadField: r6 = r5->field_7
    //     0xac780c: ldur            w6, [x5, #7]
    // 0xac7810: r7 = LoadInt32Instr(r6)
    //     0xac7810: sbfx            x7, x6, #1, #0x1f
    // 0xac7814: r6 = LoadClassIdInstr(r5)
    //     0xac7814: ldur            x6, [x5, #-1]
    //     0xac7818: ubfx            x6, x6, #0xc, #0x14
    // 0xac781c: lsl             x6, x6, #1
    // 0xac7820: mov             x8, x0
    // 0xac7824: mov             x0, x1
    // 0xac7828: d6 = 0.000000
    //     0xac7828: eor             v6.16b, v6.16b, v6.16b
    // 0xac782c: d5 = 1.000000
    //     0xac782c: fmov            d5, #1.00000000
    // 0xac7830: d4 = 0.100000
    //     0xac7830: ldr             d4, [PP, #0x5ad0]  ; [pp+0x5ad0] IMM: double(0.1) from 0x3fb999999999999a
    // 0xac7834: CheckStackOverflow
    //     0xac7834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7838: cmp             SP, x16
    //     0xac783c: b.ls            #0xac7e50
    // 0xac7840: cmp             x0, #0x30
    // 0xac7844: b.lt            #0xac78b8
    // 0xac7848: cmp             x0, #0x39
    // 0xac784c: b.gt            #0xac78b8
    // 0xac7850: fmul            d7, d5, d4
    // 0xac7854: sub             x1, x0, #0x30
    // 0xac7858: scvtf           d5, x1
    // 0xac785c: fmul            d8, d5, d7
    // 0xac7860: fadd            d9, d6, d8
    // 0xac7864: cmp             x8, x4
    // 0xac7868: b.lt            #0xac7874
    // 0xac786c: r0 = -1
    //     0xac786c: movn            x0, #0
    // 0xac7870: b               #0xac78ac
    // 0xac7874: add             x9, x8, #1
    // 0xac7878: StoreField: r2->field_f = r9
    //     0xac7878: stur            x9, [x2, #0xf]
    // 0xac787c: mov             x0, x7
    // 0xac7880: mov             x1, x8
    // 0xac7884: cmp             x1, x0
    // 0xac7888: b.hs            #0xac7e58
    // 0xac788c: cmp             w6, #0xbc
    // 0xac7890: b.ne            #0xac78a0
    // 0xac7894: ArrayLoad: r0 = r5[r8]  ; TypedUnsigned_1
    //     0xac7894: add             x16, x5, x8
    //     0xac7898: ldrb            w0, [x16, #0xf]
    // 0xac789c: b               #0xac78a8
    // 0xac78a0: add             x16, x5, x8, lsl #1
    // 0xac78a4: ldurh           w0, [x16, #0xf]
    // 0xac78a8: mov             x8, x9
    // 0xac78ac: mov             v6.16b, v9.16b
    // 0xac78b0: mov             v5.16b, v7.16b
    // 0xac78b4: b               #0xac7834
    // 0xac78b8: mov             x7, x0
    // 0xac78bc: mov             v4.16b, v6.16b
    // 0xac78c0: mov             x6, x8
    // 0xac78c4: b               #0xac78d4
    // 0xac78c8: mov             x7, x0
    // 0xac78cc: mov             x6, x8
    // 0xac78d0: d4 = 0.000000
    //     0xac78d0: eor             v4.16b, v4.16b, v4.16b
    // 0xac78d4: fadd            d5, d1, d4
    // 0xac78d8: scvtf           d1, x3
    // 0xac78dc: fmul            d4, d5, d1
    // 0xac78e0: stur            d4, [fp, #-0x20]
    // 0xac78e4: cmp             x6, x4
    // 0xac78e8: b.ge            #0xac7ca0
    // 0xac78ec: cmp             x7, #0x65
    // 0xac78f0: b.eq            #0xac78fc
    // 0xac78f4: cmp             x7, #0x45
    // 0xac78f8: b.ne            #0xac7c98
    // 0xac78fc: LoadField: r0 = r5->field_7
    //     0xac78fc: ldur            w0, [x5, #7]
    // 0xac7900: r3 = LoadInt32Instr(r0)
    //     0xac7900: sbfx            x3, x0, #1, #0x1f
    // 0xac7904: mov             x0, x3
    // 0xac7908: mov             x1, x6
    // 0xac790c: cmp             x1, x0
    // 0xac7910: b.hs            #0xac7e5c
    // 0xac7914: r8 = LoadClassIdInstr(r5)
    //     0xac7914: ldur            x8, [x5, #-1]
    //     0xac7918: ubfx            x8, x8, #0xc, #0x14
    // 0xac791c: lsl             x8, x8, #1
    // 0xac7920: cmp             w8, #0xbc
    // 0xac7924: b.ne            #0xac7940
    // 0xac7928: ArrayLoad: r0 = r5[r6]  ; TypedUnsigned_1
    //     0xac7928: add             x16, x5, x6
    //     0xac792c: ldrb            w0, [x16, #0xf]
    // 0xac7930: cmp             x0, #0x78
    // 0xac7934: b.ne            #0xac7950
    // 0xac7938: mov             v0.16b, v4.16b
    // 0xac793c: b               #0xac7ca4
    // 0xac7940: add             x16, x5, x6, lsl #1
    // 0xac7944: ldurh           w0, [x16, #0xf]
    // 0xac7948: cmp             x0, #0x78
    // 0xac794c: b.eq            #0xac7c90
    // 0xac7950: cmp             w8, #0xbc
    // 0xac7954: b.ne            #0xac7970
    // 0xac7958: ArrayLoad: r0 = r5[r6]  ; TypedUnsigned_1
    //     0xac7958: add             x16, x5, x6
    //     0xac795c: ldrb            w0, [x16, #0xf]
    // 0xac7960: cmp             x0, #0x6d
    // 0xac7964: b.ne            #0xac7980
    // 0xac7968: mov             v0.16b, v4.16b
    // 0xac796c: b               #0xac7ca4
    // 0xac7970: add             x16, x5, x6, lsl #1
    // 0xac7974: ldurh           w0, [x16, #0xf]
    // 0xac7978: cmp             x0, #0x6d
    // 0xac797c: b.eq            #0xac7c88
    // 0xac7980: cmp             x6, x4
    // 0xac7984: b.lt            #0xac7990
    // 0xac7988: r0 = -1
    //     0xac7988: movn            x0, #0
    // 0xac798c: b               #0xac79bc
    // 0xac7990: add             x0, x6, #1
    // 0xac7994: StoreField: r2->field_f = r0
    //     0xac7994: stur            x0, [x2, #0xf]
    // 0xac7998: cmp             w8, #0xbc
    // 0xac799c: b.ne            #0xac79ac
    // 0xac79a0: ArrayLoad: r1 = r5[r6]  ; TypedUnsigned_1
    //     0xac79a0: add             x16, x5, x6
    //     0xac79a4: ldrb            w1, [x16, #0xf]
    // 0xac79a8: b               #0xac79b4
    // 0xac79ac: add             x16, x5, x6, lsl #1
    // 0xac79b0: ldurh           w1, [x16, #0xf]
    // 0xac79b4: mov             x6, x0
    // 0xac79b8: mov             x0, x1
    // 0xac79bc: cmp             x0, #0x2b
    // 0xac79c0: b.ne            #0xac7a1c
    // 0xac79c4: cmp             x6, x4
    // 0xac79c8: b.lt            #0xac79d8
    // 0xac79cc: mov             x0, x6
    // 0xac79d0: r1 = -1
    //     0xac79d0: movn            x1, #0
    // 0xac79d4: b               #0xac7a14
    // 0xac79d8: add             x7, x6, #1
    // 0xac79dc: StoreField: r2->field_f = r7
    //     0xac79dc: stur            x7, [x2, #0xf]
    // 0xac79e0: mov             x0, x3
    // 0xac79e4: mov             x1, x6
    // 0xac79e8: cmp             x1, x0
    // 0xac79ec: b.hs            #0xac7e60
    // 0xac79f0: cmp             w8, #0xbc
    // 0xac79f4: b.ne            #0xac7a04
    // 0xac79f8: ArrayLoad: r0 = r5[r6]  ; TypedUnsigned_1
    //     0xac79f8: add             x16, x5, x6
    //     0xac79fc: ldrb            w0, [x16, #0xf]
    // 0xac7a00: b               #0xac7a0c
    // 0xac7a04: add             x16, x5, x6, lsl #1
    // 0xac7a08: ldurh           w0, [x16, #0xf]
    // 0xac7a0c: mov             x1, x0
    // 0xac7a10: mov             x0, x7
    // 0xac7a14: r6 = false
    //     0xac7a14: add             x6, NULL, #0x30  ; false
    // 0xac7a18: b               #0xac7a9c
    // 0xac7a1c: cmp             x0, #0x2d
    // 0xac7a20: b.ne            #0xac7a80
    // 0xac7a24: cmp             x6, x4
    // 0xac7a28: b.lt            #0xac7a38
    // 0xac7a2c: mov             x0, x6
    // 0xac7a30: r1 = -1
    //     0xac7a30: movn            x1, #0
    // 0xac7a34: b               #0xac7a74
    // 0xac7a38: add             x7, x6, #1
    // 0xac7a3c: StoreField: r2->field_f = r7
    //     0xac7a3c: stur            x7, [x2, #0xf]
    // 0xac7a40: mov             x0, x3
    // 0xac7a44: mov             x1, x6
    // 0xac7a48: cmp             x1, x0
    // 0xac7a4c: b.hs            #0xac7e64
    // 0xac7a50: cmp             w8, #0xbc
    // 0xac7a54: b.ne            #0xac7a64
    // 0xac7a58: ArrayLoad: r0 = r5[r6]  ; TypedUnsigned_1
    //     0xac7a58: add             x16, x5, x6
    //     0xac7a5c: ldrb            w0, [x16, #0xf]
    // 0xac7a60: b               #0xac7a6c
    // 0xac7a64: add             x16, x5, x6, lsl #1
    // 0xac7a68: ldurh           w0, [x16, #0xf]
    // 0xac7a6c: mov             x1, x0
    // 0xac7a70: mov             x0, x7
    // 0xac7a74: mov             x6, x1
    // 0xac7a78: r1 = true
    //     0xac7a78: add             x1, NULL, #0x20  ; true
    // 0xac7a7c: b               #0xac7a90
    // 0xac7a80: mov             x16, x6
    // 0xac7a84: mov             x6, x0
    // 0xac7a88: mov             x0, x16
    // 0xac7a8c: r1 = false
    //     0xac7a8c: add             x1, NULL, #0x30  ; false
    // 0xac7a90: mov             x16, x1
    // 0xac7a94: mov             x1, x6
    // 0xac7a98: mov             x6, x16
    // 0xac7a9c: cmp             x1, #0x30
    // 0xac7aa0: b.lt            #0xac7d54
    // 0xac7aa4: cmp             x1, #0x39
    // 0xac7aa8: b.gt            #0xac7d54
    // 0xac7aac: mov             x7, x0
    // 0xac7ab0: mov             x0, x1
    // 0xac7ab4: d1 = 0.000000
    //     0xac7ab4: eor             v1.16b, v1.16b, v1.16b
    // 0xac7ab8: stur            x0, [fp, #-0x10]
    // 0xac7abc: stur            x7, [fp, #-0x18]
    // 0xac7ac0: CheckStackOverflow
    //     0xac7ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7ac4: cmp             SP, x16
    //     0xac7ac8: b.ls            #0xac7e68
    // 0xac7acc: cmp             x0, #0x30
    // 0xac7ad0: b.lt            #0xac7b3c
    // 0xac7ad4: cmp             x0, #0x39
    // 0xac7ad8: b.gt            #0xac7b3c
    // 0xac7adc: fmul            d5, d1, d0
    // 0xac7ae0: sub             x1, x0, #0x30
    // 0xac7ae4: scvtf           d1, x1
    // 0xac7ae8: fadd            d6, d5, d1
    // 0xac7aec: cmp             x7, x4
    // 0xac7af0: b.lt            #0xac7afc
    // 0xac7af4: r0 = -1
    //     0xac7af4: movn            x0, #0
    // 0xac7af8: b               #0xac7b34
    // 0xac7afc: add             x9, x7, #1
    // 0xac7b00: StoreField: r2->field_f = r9
    //     0xac7b00: stur            x9, [x2, #0xf]
    // 0xac7b04: mov             x0, x3
    // 0xac7b08: mov             x1, x7
    // 0xac7b0c: cmp             x1, x0
    // 0xac7b10: b.hs            #0xac7e70
    // 0xac7b14: cmp             w8, #0xbc
    // 0xac7b18: b.ne            #0xac7b28
    // 0xac7b1c: ArrayLoad: r0 = r5[r7]  ; TypedUnsigned_1
    //     0xac7b1c: add             x16, x5, x7
    //     0xac7b20: ldrb            w0, [x16, #0xf]
    // 0xac7b24: b               #0xac7b30
    // 0xac7b28: add             x16, x5, x7, lsl #1
    // 0xac7b2c: ldurh           w0, [x16, #0xf]
    // 0xac7b30: mov             x7, x9
    // 0xac7b34: mov             v1.16b, v6.16b
    // 0xac7b38: b               #0xac7ab8
    // 0xac7b3c: tbnz            w6, #4, #0xac7b48
    // 0xac7b40: fneg            d5, d1
    // 0xac7b44: b               #0xac7b4c
    // 0xac7b48: mov             v5.16b, v1.16b
    // 0xac7b4c: d1 = -37.000000
    //     0xac7b4c: add             x17, PP, #0x26, lsl #12  ; [pp+0x26278] IMM: double(-37) from 0xc042800000000000
    //     0xac7b50: ldr             d1, [x17, #0x278]
    // 0xac7b54: stur            d5, [fp, #-0x28]
    // 0xac7b58: fcmp            d5, d1
    // 0xac7b5c: b.lt            #0xac7d74
    // 0xac7b60: d1 = 38.000000
    //     0xac7b60: add             x17, PP, #0x26, lsl #12  ; [pp+0x26280] IMM: double(38) from 0x4043000000000000
    //     0xac7b64: ldr             d1, [x17, #0x280]
    // 0xac7b68: fcmp            d1, d5
    // 0xac7b6c: r16 = true
    //     0xac7b6c: add             x16, NULL, #0x20  ; true
    // 0xac7b70: r17 = false
    //     0xac7b70: add             x17, NULL, #0x30  ; false
    // 0xac7b74: csel            x1, x16, x17, ge
    // 0xac7b78: tbnz            w1, #4, #0xac7d74
    // 0xac7b7c: d1 = 0.000000
    //     0xac7b7c: eor             v1.16b, v1.16b, v1.16b
    // 0xac7b80: fcmp            d5, d1
    // 0xac7b84: b.eq            #0xac7c74
    // 0xac7b88: mov             v1.16b, v5.16b
    // 0xac7b8c: d30 = 0.000000
    //     0xac7b8c: fmov            d30, d0
    // 0xac7b90: d0 = 1.000000
    //     0xac7b90: fmov            d0, #1.00000000
    // 0xac7b94: fcmp            d1, #0.0
    // 0xac7b98: b.vs            #0xac7bdc
    // 0xac7b9c: b.eq            #0xac7c60
    // 0xac7ba0: fcmp            d1, d0
    // 0xac7ba4: b.eq            #0xac7bcc
    // 0xac7ba8: d31 = 2.000000
    //     0xac7ba8: fmov            d31, #2.00000000
    // 0xac7bac: fcmp            d1, d31
    // 0xac7bb0: b.eq            #0xac7bd4
    // 0xac7bb4: d31 = 3.000000
    //     0xac7bb4: fmov            d31, #3.00000000
    // 0xac7bb8: fcmp            d1, d31
    // 0xac7bbc: b.ne            #0xac7bdc
    // 0xac7bc0: fmul            d0, d30, d30
    // 0xac7bc4: fmul            d0, d0, d30
    // 0xac7bc8: b               #0xac7c60
    // 0xac7bcc: d0 = 0.000000
    //     0xac7bcc: fmov            d0, d30
    // 0xac7bd0: b               #0xac7c60
    // 0xac7bd4: fmul            d0, d30, d30
    // 0xac7bd8: b               #0xac7c60
    // 0xac7bdc: fcmp            d30, d0
    // 0xac7be0: b.vs            #0xac7bf0
    // 0xac7be4: b.eq            #0xac7c60
    // 0xac7be8: fcmp            d30, d1
    // 0xac7bec: b.vc            #0xac7bf8
    // 0xac7bf0: d0 = nan
    //     0xac7bf0: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0xac7bf4: b               #0xac7c60
    // 0xac7bf8: d0 = -inf
    //     0xac7bf8: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xac7bfc: fcmp            d30, d0
    // 0xac7c00: b.eq            #0xac7c28
    // 0xac7c04: d0 = 0.500000
    //     0xac7c04: fmov            d0, #0.50000000
    // 0xac7c08: fcmp            d1, d0
    // 0xac7c0c: b.ne            #0xac7c28
    // 0xac7c10: fcmp            d30, #0.0
    // 0xac7c14: b.eq            #0xac7c20
    // 0xac7c18: fsqrt           d0, d30
    // 0xac7c1c: b               #0xac7c60
    // 0xac7c20: d0 = 0.000000
    //     0xac7c20: eor             v0.16b, v0.16b, v0.16b
    // 0xac7c24: b               #0xac7c60
    // 0xac7c28: d0 = 0.000000
    //     0xac7c28: fmov            d0, d30
    // 0xac7c2c: stp             fp, lr, [SP, #-0x10]!
    // 0xac7c30: mov             fp, SP
    // 0xac7c34: CallRuntime_LibcPow(double, double) -> double
    //     0xac7c34: and             SP, SP, #0xfffffffffffffff0
    //     0xac7c38: mov             sp, SP
    //     0xac7c3c: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0xac7c40: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac7c44: blr             x16
    //     0xac7c48: movz            x16, #0x8
    //     0xac7c4c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac7c50: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac7c54: sub             sp, x16, #1, lsl #12
    //     0xac7c58: mov             SP, fp
    //     0xac7c5c: ldp             fp, lr, [SP], #0x10
    // 0xac7c60: mov             v1.16b, v0.16b
    // 0xac7c64: ldur            d0, [fp, #-0x20]
    // 0xac7c68: fmul            d2, d0, d1
    // 0xac7c6c: mov             v0.16b, v2.16b
    // 0xac7c70: b               #0xac7c78
    // 0xac7c74: mov             v0.16b, v4.16b
    // 0xac7c78: ldur            x1, [fp, #-0x10]
    // 0xac7c7c: mov             v1.16b, v0.16b
    // 0xac7c80: ldur            x0, [fp, #-0x18]
    // 0xac7c84: b               #0xac7cb0
    // 0xac7c88: mov             v0.16b, v4.16b
    // 0xac7c8c: b               #0xac7ca4
    // 0xac7c90: mov             v0.16b, v4.16b
    // 0xac7c94: b               #0xac7ca4
    // 0xac7c98: mov             v0.16b, v4.16b
    // 0xac7c9c: b               #0xac7ca4
    // 0xac7ca0: mov             v0.16b, v4.16b
    // 0xac7ca4: mov             x1, x7
    // 0xac7ca8: mov             v1.16b, v0.16b
    // 0xac7cac: mov             x0, x6
    // 0xac7cb0: d0 = -179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xac7cb0: add             x17, PP, #0x26, lsl #12  ; [pp+0x26268] IMM: double(-1.7976931348623157e+308) from 0xffefffffffffffff
    //     0xac7cb4: ldr             d0, [x17, #0x268]
    // 0xac7cb8: stur            d1, [fp, #-0x20]
    // 0xac7cbc: fcmp            d1, d0
    // 0xac7cc0: b.lt            #0xac7de4
    // 0xac7cc4: d0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xac7cc4: add             x17, PP, #0x26, lsl #12  ; [pp+0x26270] IMM: double(1.7976931348623157e+308) from 0x7fefffffffffffff
    //     0xac7cc8: ldr             d0, [x17, #0x270]
    // 0xac7ccc: fcmp            d0, d1
    // 0xac7cd0: r16 = true
    //     0xac7cd0: add             x16, NULL, #0x20  ; true
    // 0xac7cd4: r17 = false
    //     0xac7cd4: add             x17, NULL, #0x30  ; false
    // 0xac7cd8: csel            x2, x16, x17, ge
    // 0xac7cdc: tbnz            w2, #4, #0xac7de4
    // 0xac7ce0: cmn             x1, #1
    // 0xac7ce4: b.eq            #0xac7cf8
    // 0xac7ce8: ldur            x1, [fp, #-8]
    // 0xac7cec: sub             x2, x0, #1
    // 0xac7cf0: StoreField: r1->field_f = r2
    //     0xac7cf0: stur            x2, [x1, #0xf]
    // 0xac7cf4: r0 = _skipOptionalSvgSpacesOrDelimiter()
    //     0xac7cf4: bl              #0xac743c  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpacesOrDelimiter
    // 0xac7cf8: ldur            d0, [fp, #-0x20]
    // 0xac7cfc: LeaveFrame
    //     0xac7cfc: mov             SP, fp
    //     0xac7d00: ldp             fp, lr, [SP], #0x10
    // 0xac7d04: ret
    //     0xac7d04: ret             
    // 0xac7d08: r0 = StateError()
    //     0xac7d08: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7d0c: mov             x1, x0
    // 0xac7d10: r0 = "First character of a number must be one of [0-9+-.]."
    //     0xac7d10: add             x0, PP, #0x26, lsl #12  ; [pp+0x26288] "First character of a number must be one of [0-9+-.]."
    //     0xac7d14: ldr             x0, [x0, #0x288]
    // 0xac7d18: StoreField: r1->field_b = r0
    //     0xac7d18: stur            w0, [x1, #0xb]
    // 0xac7d1c: mov             x0, x1
    // 0xac7d20: r0 = Throw()
    //     0xac7d20: bl              #0xec04b8  ; ThrowStub
    // 0xac7d24: brk             #0
    // 0xac7d28: r0 = "Numeric overflow"
    //     0xac7d28: add             x0, PP, #0x26, lsl #12  ; [pp+0x26290] "Numeric overflow"
    //     0xac7d2c: ldr             x0, [x0, #0x290]
    // 0xac7d30: b               #0xac7e0c
    // 0xac7d34: r0 = StateError()
    //     0xac7d34: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7d38: mov             x1, x0
    // 0xac7d3c: r0 = "There must be at least one digit following the ."
    //     0xac7d3c: add             x0, PP, #0x26, lsl #12  ; [pp+0x26298] "There must be at least one digit following the ."
    //     0xac7d40: ldr             x0, [x0, #0x298]
    // 0xac7d44: StoreField: r1->field_b = r0
    //     0xac7d44: stur            w0, [x1, #0xb]
    // 0xac7d48: mov             x0, x1
    // 0xac7d4c: r0 = Throw()
    //     0xac7d4c: bl              #0xec04b8  ; ThrowStub
    // 0xac7d50: brk             #0
    // 0xac7d54: r0 = StateError()
    //     0xac7d54: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7d58: mov             x1, x0
    // 0xac7d5c: r0 = "Missing exponent"
    //     0xac7d5c: add             x0, PP, #0x26, lsl #12  ; [pp+0x262a0] "Missing exponent"
    //     0xac7d60: ldr             x0, [x0, #0x2a0]
    // 0xac7d64: StoreField: r1->field_b = r0
    //     0xac7d64: stur            w0, [x1, #0xb]
    // 0xac7d68: mov             x0, x1
    // 0xac7d6c: r0 = Throw()
    //     0xac7d6c: bl              #0xec04b8  ; ThrowStub
    // 0xac7d70: brk             #0
    // 0xac7d74: r1 = Null
    //     0xac7d74: mov             x1, NULL
    // 0xac7d78: r2 = 4
    //     0xac7d78: movz            x2, #0x4
    // 0xac7d7c: r0 = AllocateArray()
    //     0xac7d7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xac7d80: r16 = "Invalid exponent "
    //     0xac7d80: add             x16, PP, #0x26, lsl #12  ; [pp+0x262a8] "Invalid exponent "
    //     0xac7d84: ldr             x16, [x16, #0x2a8]
    // 0xac7d88: StoreField: r0->field_f = r16
    //     0xac7d88: stur            w16, [x0, #0xf]
    // 0xac7d8c: ldur            d0, [fp, #-0x28]
    // 0xac7d90: r1 = inline_Allocate_Double()
    //     0xac7d90: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xac7d94: add             x1, x1, #0x10
    //     0xac7d98: cmp             x2, x1
    //     0xac7d9c: b.ls            #0xac7e74
    //     0xac7da0: str             x1, [THR, #0x50]  ; THR::top
    //     0xac7da4: sub             x1, x1, #0xf
    //     0xac7da8: movz            x2, #0xe15c
    //     0xac7dac: movk            x2, #0x3, lsl #16
    //     0xac7db0: stur            x2, [x1, #-1]
    // 0xac7db4: StoreField: r1->field_7 = d0
    //     0xac7db4: stur            d0, [x1, #7]
    // 0xac7db8: StoreField: r0->field_13 = r1
    //     0xac7db8: stur            w1, [x0, #0x13]
    // 0xac7dbc: str             x0, [SP]
    // 0xac7dc0: r0 = _interpolate()
    //     0xac7dc0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xac7dc4: stur            x0, [fp, #-8]
    // 0xac7dc8: r0 = StateError()
    //     0xac7dc8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7dcc: mov             x1, x0
    // 0xac7dd0: ldur            x0, [fp, #-8]
    // 0xac7dd4: StoreField: r1->field_b = r0
    //     0xac7dd4: stur            w0, [x1, #0xb]
    // 0xac7dd8: mov             x0, x1
    // 0xac7ddc: r0 = Throw()
    //     0xac7ddc: bl              #0xec04b8  ; ThrowStub
    // 0xac7de0: brk             #0
    // 0xac7de4: r0 = StateError()
    //     0xac7de4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7de8: mov             x1, x0
    // 0xac7dec: r0 = "Numeric overflow"
    //     0xac7dec: add             x0, PP, #0x26, lsl #12  ; [pp+0x26290] "Numeric overflow"
    //     0xac7df0: ldr             x0, [x0, #0x290]
    // 0xac7df4: StoreField: r1->field_b = r0
    //     0xac7df4: stur            w0, [x1, #0xb]
    // 0xac7df8: mov             x0, x1
    // 0xac7dfc: r0 = Throw()
    //     0xac7dfc: bl              #0xec04b8  ; ThrowStub
    // 0xac7e00: brk             #0
    // 0xac7e04: r0 = "Numeric overflow"
    //     0xac7e04: add             x0, PP, #0x26, lsl #12  ; [pp+0x26290] "Numeric overflow"
    //     0xac7e08: ldr             x0, [x0, #0x290]
    // 0xac7e0c: r0 = StateError()
    //     0xac7e0c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xac7e10: mov             x1, x0
    // 0xac7e14: r0 = "Numeric overflow"
    //     0xac7e14: add             x0, PP, #0x26, lsl #12  ; [pp+0x26290] "Numeric overflow"
    //     0xac7e18: ldr             x0, [x0, #0x290]
    // 0xac7e1c: StoreField: r1->field_b = r0
    //     0xac7e1c: stur            w0, [x1, #0xb]
    // 0xac7e20: mov             x0, x1
    // 0xac7e24: r0 = Throw()
    //     0xac7e24: bl              #0xec04b8  ; ThrowStub
    // 0xac7e28: brk             #0
    // 0xac7e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac7e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac7e30: b               #0xac7528
    // 0xac7e34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7e34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac7e38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7e38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac7e3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7e3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac7e40: r0 = StackOverflowSharedWithFPURegs()
    //     0xac7e40: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac7e44: b               #0xac76f4
    // 0xac7e48: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e48: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e4c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e4c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e50: r0 = StackOverflowSharedWithFPURegs()
    //     0xac7e50: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac7e54: b               #0xac7840
    // 0xac7e58: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e58: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e5c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e5c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e60: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e60: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e64: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e64: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e68: r0 = StackOverflowSharedWithFPURegs()
    //     0xac7e68: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac7e6c: b               #0xac7acc
    // 0xac7e70: r0 = RangeErrorSharedWithFPURegs()
    //     0xac7e70: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac7e74: SaveReg d0
    //     0xac7e74: str             q0, [SP, #-0x10]!
    // 0xac7e78: SaveReg r0
    //     0xac7e78: str             x0, [SP, #-8]!
    // 0xac7e7c: r0 = AllocateDouble()
    //     0xac7e7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac7e80: mov             x1, x0
    // 0xac7e84: RestoreReg r0
    //     0xac7e84: ldr             x0, [SP], #8
    // 0xac7e88: RestoreReg d0
    //     0xac7e88: ldr             q0, [SP], #0x10
    // 0xac7e8c: b               #0xac7db4
  }
  _ _skipOptionalSvgSpaces(/* No info */) {
    // ** addr: 0xac7e90, size: 0xdc
    // 0xac7e90: EnterFrame
    //     0xac7e90: stp             fp, lr, [SP, #-0x10]!
    //     0xac7e94: mov             fp, SP
    // 0xac7e98: mov             x2, x1
    // 0xac7e9c: ArrayLoad: r3 = r2[0]  ; List_8
    //     0xac7e9c: ldur            x3, [x2, #0x17]
    // 0xac7ea0: LoadField: r4 = r2->field_7
    //     0xac7ea0: ldur            w4, [x2, #7]
    // 0xac7ea4: DecompressPointer r4
    //     0xac7ea4: add             x4, x4, HEAP, lsl #32
    // 0xac7ea8: LoadField: r5 = r4->field_7
    //     0xac7ea8: ldur            w5, [x4, #7]
    // 0xac7eac: r6 = LoadInt32Instr(r5)
    //     0xac7eac: sbfx            x6, x5, #1, #0x1f
    // 0xac7eb0: r5 = LoadClassIdInstr(r4)
    //     0xac7eb0: ldur            x5, [x4, #-1]
    //     0xac7eb4: ubfx            x5, x5, #0xc, #0x14
    // 0xac7eb8: lsl             x5, x5, #1
    // 0xac7ebc: CheckStackOverflow
    //     0xac7ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac7ec0: cmp             SP, x16
    //     0xac7ec4: b.ls            #0xac7f60
    // 0xac7ec8: LoadField: r7 = r2->field_f
    //     0xac7ec8: ldur            x7, [x2, #0xf]
    // 0xac7ecc: cmp             x7, x3
    // 0xac7ed0: b.ge            #0xac7f50
    // 0xac7ed4: mov             x0, x6
    // 0xac7ed8: mov             x1, x7
    // 0xac7edc: cmp             x1, x0
    // 0xac7ee0: b.hs            #0xac7f68
    // 0xac7ee4: cmp             w5, #0xbc
    // 0xac7ee8: b.ne            #0xac7efc
    // 0xac7eec: ArrayLoad: r1 = r4[r7]  ; TypedUnsigned_1
    //     0xac7eec: add             x16, x4, x7
    //     0xac7ef0: ldrb            w1, [x16, #0xf]
    // 0xac7ef4: mov             x0, x1
    // 0xac7ef8: b               #0xac7f08
    // 0xac7efc: add             x16, x4, x7, lsl #1
    // 0xac7f00: ldurh           w1, [x16, #0xf]
    // 0xac7f04: mov             x0, x1
    // 0xac7f08: cmp             x0, #0x20
    // 0xac7f0c: b.gt            #0xac7f44
    // 0xac7f10: cmp             x0, #0x20
    // 0xac7f14: b.eq            #0xac7f38
    // 0xac7f18: cmp             x0, #0xa
    // 0xac7f1c: b.eq            #0xac7f38
    // 0xac7f20: cmp             x0, #9
    // 0xac7f24: b.eq            #0xac7f38
    // 0xac7f28: cmp             x0, #0xd
    // 0xac7f2c: b.eq            #0xac7f38
    // 0xac7f30: cmp             x0, #0xc
    // 0xac7f34: b.ne            #0xac7f44
    // 0xac7f38: add             x1, x7, #1
    // 0xac7f3c: StoreField: r2->field_f = r1
    //     0xac7f3c: stur            x1, [x2, #0xf]
    // 0xac7f40: b               #0xac7ebc
    // 0xac7f44: LeaveFrame
    //     0xac7f44: mov             SP, fp
    //     0xac7f48: ldp             fp, lr, [SP], #0x10
    // 0xac7f4c: ret
    //     0xac7f4c: ret             
    // 0xac7f50: r0 = -1
    //     0xac7f50: movn            x0, #0
    // 0xac7f54: LeaveFrame
    //     0xac7f54: mov             SP, fp
    //     0xac7f58: ldp             fp, lr, [SP], #0x10
    // 0xac7f5c: ret
    //     0xac7f5c: ret             
    // 0xac7f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac7f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac7f64: b               #0xac7ec8
    // 0xac7f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac7f68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _maybeImplicitCommand(/* No info */) {
    // ** addr: 0xac7f6c, size: 0x88
    // 0xac7f6c: cmp             x2, #0x30
    // 0xac7f70: b.lt            #0xac7f7c
    // 0xac7f74: cmp             x2, #0x39
    // 0xac7f78: b.le            #0xac7f94
    // 0xac7f7c: cmp             x2, #0x2b
    // 0xac7f80: b.eq            #0xac7f94
    // 0xac7f84: cmp             x2, #0x2d
    // 0xac7f88: b.eq            #0xac7f94
    // 0xac7f8c: cmp             x2, #0x2e
    // 0xac7f90: b.ne            #0xac7fac
    // 0xac7f94: LoadField: r0 = r1->field_b
    //     0xac7f94: ldur            w0, [x1, #0xb]
    // 0xac7f98: DecompressPointer r0
    //     0xac7f98: add             x0, x0, HEAP, lsl #32
    // 0xac7f9c: r16 = Instance_SvgPathSegType
    //     0xac7f9c: add             x16, PP, #0x26, lsl #12  ; [pp+0x262b0] Obj!SvgPathSegType@e2fe81
    //     0xac7fa0: ldr             x16, [x16, #0x2b0]
    // 0xac7fa4: cmp             w0, w16
    // 0xac7fa8: b.ne            #0xac7fb8
    // 0xac7fac: r0 = Instance_SvgPathSegType
    //     0xac7fac: add             x0, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac7fb0: ldr             x0, [x0, #0x150]
    // 0xac7fb4: ret
    //     0xac7fb4: ret             
    // 0xac7fb8: r16 = Instance_SvgPathSegType
    //     0xac7fb8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26230] Obj!SvgPathSegType@e2ff61
    //     0xac7fbc: ldr             x16, [x16, #0x230]
    // 0xac7fc0: cmp             w0, w16
    // 0xac7fc4: b.ne            #0xac7fd4
    // 0xac7fc8: r0 = Instance_SvgPathSegType
    //     0xac7fc8: add             x0, PP, #0x26, lsl #12  ; [pp+0x262b8] Obj!SvgPathSegType@e2fe61
    //     0xac7fcc: ldr             x0, [x0, #0x2b8]
    // 0xac7fd0: ret
    //     0xac7fd0: ret             
    // 0xac7fd4: r16 = Instance_SvgPathSegType
    //     0xac7fd4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26228] Obj!SvgPathSegType@e2ff81
    //     0xac7fd8: ldr             x16, [x16, #0x228]
    // 0xac7fdc: cmp             w0, w16
    // 0xac7fe0: b.ne            #0xac7ff0
    // 0xac7fe4: r0 = Instance_SvgPathSegType
    //     0xac7fe4: add             x0, PP, #0x26, lsl #12  ; [pp+0x262c0] Obj!SvgPathSegType@e2fe41
    //     0xac7fe8: ldr             x0, [x0, #0x2c0]
    // 0xac7fec: ret
    //     0xac7fec: ret             
    // 0xac7ff0: ret
    //     0xac7ff0: ret             
  }
  _ SvgPathStringSource(/* No info */) {
    // ** addr: 0xac8060, size: 0x7c
    // 0xac8060: EnterFrame
    //     0xac8060: stp             fp, lr, [SP, #-0x10]!
    //     0xac8064: mov             fp, SP
    // 0xac8068: r3 = Instance_SvgPathSegType
    //     0xac8068: add             x3, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac806c: ldr             x3, [x3, #0x150]
    // 0xac8070: mov             x16, x2
    // 0xac8074: mov             x2, x1
    // 0xac8078: mov             x1, x16
    // 0xac807c: CheckStackOverflow
    //     0xac807c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac8080: cmp             SP, x16
    //     0xac8084: b.ls            #0xac80d4
    // 0xac8088: mov             x0, x1
    // 0xac808c: StoreField: r2->field_7 = r0
    //     0xac808c: stur            w0, [x2, #7]
    //     0xac8090: ldurb           w16, [x2, #-1]
    //     0xac8094: ldurb           w17, [x0, #-1]
    //     0xac8098: and             x16, x17, x16, lsr #2
    //     0xac809c: tst             x16, HEAP, lsr #32
    //     0xac80a0: b.eq            #0xac80a8
    //     0xac80a4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xac80a8: StoreField: r2->field_b = r3
    //     0xac80a8: stur            w3, [x2, #0xb]
    // 0xac80ac: StoreField: r2->field_f = rZR
    //     0xac80ac: stur            xzr, [x2, #0xf]
    // 0xac80b0: LoadField: r0 = r1->field_7
    //     0xac80b0: ldur            w0, [x1, #7]
    // 0xac80b4: r1 = LoadInt32Instr(r0)
    //     0xac80b4: sbfx            x1, x0, #1, #0x1f
    // 0xac80b8: ArrayStore: r2[0] = r1  ; List_8
    //     0xac80b8: stur            x1, [x2, #0x17]
    // 0xac80bc: mov             x1, x2
    // 0xac80c0: r0 = _skipOptionalSvgSpaces()
    //     0xac80c0: bl              #0xac7e90  ; [package:path_parsing/src/path_parsing.dart] SvgPathStringSource::_skipOptionalSvgSpaces
    // 0xac80c4: r0 = Null
    //     0xac80c4: mov             x0, NULL
    // 0xac80c8: LeaveFrame
    //     0xac80c8: mov             SP, fp
    //     0xac80cc: ldp             fp, lr, [SP], #0x10
    // 0xac80d0: ret
    //     0xac80d0: ret             
    // 0xac80d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac80d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac80d8: b               #0xac8088
  }
}

// class id: 932, size: 0x18, field offset: 0x8
//   const constructor, 
class _PathOffset extends Object {

  _Mint field_8;
  _Mint field_10;

  _PathOffset -(_PathOffset, _PathOffset) {
    // ** addr: 0xac5218, size: 0x84
    // 0xac5218: EnterFrame
    //     0xac5218: stp             fp, lr, [SP, #-0x10]!
    //     0xac521c: mov             fp, SP
    // 0xac5220: CheckStackOverflow
    //     0xac5220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5224: cmp             SP, x16
    //     0xac5228: b.ls            #0xac527c
    // 0xac522c: ldr             x0, [fp, #0x10]
    // 0xac5230: r2 = Null
    //     0xac5230: mov             x2, NULL
    // 0xac5234: r1 = Null
    //     0xac5234: mov             x1, NULL
    // 0xac5238: r4 = 60
    //     0xac5238: movz            x4, #0x3c
    // 0xac523c: branchIfSmi(r0, 0xac5248)
    //     0xac523c: tbz             w0, #0, #0xac5248
    // 0xac5240: r4 = LoadClassIdInstr(r0)
    //     0xac5240: ldur            x4, [x0, #-1]
    //     0xac5244: ubfx            x4, x4, #0xc, #0x14
    // 0xac5248: cmp             x4, #0x3a4
    // 0xac524c: b.eq            #0xac5264
    // 0xac5250: r8 = _PathOffset
    //     0xac5250: add             x8, PP, #0x31, lsl #12  ; [pp+0x314e8] Type: _PathOffset
    //     0xac5254: ldr             x8, [x8, #0x4e8]
    // 0xac5258: r3 = Null
    //     0xac5258: add             x3, PP, #0x31, lsl #12  ; [pp+0x314f0] Null
    //     0xac525c: ldr             x3, [x3, #0x4f0]
    // 0xac5260: r0 = DefaultTypeTest()
    //     0xac5260: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xac5264: ldr             x1, [fp, #0x18]
    // 0xac5268: ldr             x2, [fp, #0x10]
    // 0xac526c: r0 = -()
    //     0xac526c: bl              #0xac68c8  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::-
    // 0xac5270: LeaveFrame
    //     0xac5270: mov             SP, fp
    //     0xac5274: ldp             fp, lr, [SP], #0x10
    // 0xac5278: ret
    //     0xac5278: ret             
    // 0xac527c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac527c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5280: b               #0xac522c
  }
  _PathOffset +(_PathOffset, _PathOffset) {
    // ** addr: 0xac529c, size: 0x84
    // 0xac529c: EnterFrame
    //     0xac529c: stp             fp, lr, [SP, #-0x10]!
    //     0xac52a0: mov             fp, SP
    // 0xac52a4: CheckStackOverflow
    //     0xac52a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac52a8: cmp             SP, x16
    //     0xac52ac: b.ls            #0xac5300
    // 0xac52b0: ldr             x0, [fp, #0x10]
    // 0xac52b4: r2 = Null
    //     0xac52b4: mov             x2, NULL
    // 0xac52b8: r1 = Null
    //     0xac52b8: mov             x1, NULL
    // 0xac52bc: r4 = 60
    //     0xac52bc: movz            x4, #0x3c
    // 0xac52c0: branchIfSmi(r0, 0xac52cc)
    //     0xac52c0: tbz             w0, #0, #0xac52cc
    // 0xac52c4: r4 = LoadClassIdInstr(r0)
    //     0xac52c4: ldur            x4, [x0, #-1]
    //     0xac52c8: ubfx            x4, x4, #0xc, #0x14
    // 0xac52cc: cmp             x4, #0x3a4
    // 0xac52d0: b.eq            #0xac52e8
    // 0xac52d4: r8 = _PathOffset
    //     0xac52d4: add             x8, PP, #0x31, lsl #12  ; [pp+0x314e8] Type: _PathOffset
    //     0xac52d8: ldr             x8, [x8, #0x4e8]
    // 0xac52dc: r3 = Null
    //     0xac52dc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31500] Null
    //     0xac52e0: ldr             x3, [x3, #0x500]
    // 0xac52e4: r0 = DefaultTypeTest()
    //     0xac52e4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xac52e8: ldr             x1, [fp, #0x18]
    // 0xac52ec: ldr             x2, [fp, #0x10]
    // 0xac52f0: r0 = +()
    //     0xac52f0: bl              #0xac6c44  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::+
    // 0xac52f4: LeaveFrame
    //     0xac52f4: mov             SP, fp
    //     0xac52f8: ldp             fp, lr, [SP], #0x10
    // 0xac52fc: ret
    //     0xac52fc: ret             
    // 0xac5300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5304: b               #0xac52b0
  }
  _PathOffset *(_PathOffset, double) {
    // ** addr: 0xac5320, size: 0x50
    // 0xac5320: EnterFrame
    //     0xac5320: stp             fp, lr, [SP, #-0x10]!
    //     0xac5324: mov             fp, SP
    // 0xac5328: CheckStackOverflow
    //     0xac5328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac532c: cmp             SP, x16
    //     0xac5330: b.ls            #0xac5350
    // 0xac5334: ldr             x0, [fp, #0x10]
    // 0xac5338: LoadField: d0 = r0->field_7
    //     0xac5338: ldur            d0, [x0, #7]
    // 0xac533c: ldr             x1, [fp, #0x18]
    // 0xac5340: r0 = *()
    //     0xac5340: bl              #0xac6884  ; [package:path_parsing/src/path_parsing.dart] _PathOffset::*
    // 0xac5344: LeaveFrame
    //     0xac5344: mov             SP, fp
    //     0xac5348: ldp             fp, lr, [SP], #0x10
    // 0xac534c: ret
    //     0xac534c: ret             
    // 0xac5350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5354: b               #0xac5334
  }
  _ translate(/* No info */) {
    // ** addr: 0xac5358, size: 0x44
    // 0xac5358: EnterFrame
    //     0xac5358: stp             fp, lr, [SP, #-0x10]!
    //     0xac535c: mov             fp, SP
    // 0xac5360: AllocStack(0x10)
    //     0xac5360: sub             SP, SP, #0x10
    // 0xac5364: LoadField: d2 = r1->field_7
    //     0xac5364: ldur            d2, [x1, #7]
    // 0xac5368: fadd            d3, d2, d0
    // 0xac536c: stur            d3, [fp, #-0x10]
    // 0xac5370: LoadField: d0 = r1->field_f
    //     0xac5370: ldur            d0, [x1, #0xf]
    // 0xac5374: fadd            d2, d0, d1
    // 0xac5378: stur            d2, [fp, #-8]
    // 0xac537c: r0 = _PathOffset()
    //     0xac537c: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac5380: ldur            d0, [fp, #-0x10]
    // 0xac5384: StoreField: r0->field_7 = d0
    //     0xac5384: stur            d0, [x0, #7]
    // 0xac5388: ldur            d0, [fp, #-8]
    // 0xac538c: StoreField: r0->field_f = d0
    //     0xac538c: stur            d0, [x0, #0xf]
    // 0xac5390: LeaveFrame
    //     0xac5390: mov             SP, fp
    //     0xac5394: ldp             fp, lr, [SP], #0x10
    // 0xac5398: ret
    //     0xac5398: ret             
  }
  _PathOffset *(_PathOffset, double) {
    // ** addr: 0xac6884, size: 0x44
    // 0xac6884: EnterFrame
    //     0xac6884: stp             fp, lr, [SP, #-0x10]!
    //     0xac6888: mov             fp, SP
    // 0xac688c: AllocStack(0x10)
    //     0xac688c: sub             SP, SP, #0x10
    // 0xac6890: LoadField: d1 = r1->field_7
    //     0xac6890: ldur            d1, [x1, #7]
    // 0xac6894: fmul            d2, d1, d0
    // 0xac6898: stur            d2, [fp, #-0x10]
    // 0xac689c: LoadField: d1 = r1->field_f
    //     0xac689c: ldur            d1, [x1, #0xf]
    // 0xac68a0: fmul            d3, d1, d0
    // 0xac68a4: stur            d3, [fp, #-8]
    // 0xac68a8: r0 = _PathOffset()
    //     0xac68a8: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac68ac: ldur            d0, [fp, #-0x10]
    // 0xac68b0: StoreField: r0->field_7 = d0
    //     0xac68b0: stur            d0, [x0, #7]
    // 0xac68b4: ldur            d0, [fp, #-8]
    // 0xac68b8: StoreField: r0->field_f = d0
    //     0xac68b8: stur            d0, [x0, #0xf]
    // 0xac68bc: LeaveFrame
    //     0xac68bc: mov             SP, fp
    //     0xac68c0: ldp             fp, lr, [SP], #0x10
    // 0xac68c4: ret
    //     0xac68c4: ret             
  }
  _PathOffset -(_PathOffset, _PathOffset) {
    // ** addr: 0xac68c8, size: 0x4c
    // 0xac68c8: EnterFrame
    //     0xac68c8: stp             fp, lr, [SP, #-0x10]!
    //     0xac68cc: mov             fp, SP
    // 0xac68d0: AllocStack(0x10)
    //     0xac68d0: sub             SP, SP, #0x10
    // 0xac68d4: LoadField: d0 = r1->field_7
    //     0xac68d4: ldur            d0, [x1, #7]
    // 0xac68d8: LoadField: d1 = r2->field_7
    //     0xac68d8: ldur            d1, [x2, #7]
    // 0xac68dc: fsub            d2, d0, d1
    // 0xac68e0: stur            d2, [fp, #-0x10]
    // 0xac68e4: LoadField: d0 = r1->field_f
    //     0xac68e4: ldur            d0, [x1, #0xf]
    // 0xac68e8: LoadField: d1 = r2->field_f
    //     0xac68e8: ldur            d1, [x2, #0xf]
    // 0xac68ec: fsub            d3, d0, d1
    // 0xac68f0: stur            d3, [fp, #-8]
    // 0xac68f4: r0 = _PathOffset()
    //     0xac68f4: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac68f8: ldur            d0, [fp, #-0x10]
    // 0xac68fc: StoreField: r0->field_7 = d0
    //     0xac68fc: stur            d0, [x0, #7]
    // 0xac6900: ldur            d0, [fp, #-8]
    // 0xac6904: StoreField: r0->field_f = d0
    //     0xac6904: stur            d0, [x0, #0xf]
    // 0xac6908: LeaveFrame
    //     0xac6908: mov             SP, fp
    //     0xac690c: ldp             fp, lr, [SP], #0x10
    // 0xac6910: ret
    //     0xac6910: ret             
  }
  _PathOffset +(_PathOffset, _PathOffset) {
    // ** addr: 0xac6c44, size: 0x4c
    // 0xac6c44: EnterFrame
    //     0xac6c44: stp             fp, lr, [SP, #-0x10]!
    //     0xac6c48: mov             fp, SP
    // 0xac6c4c: AllocStack(0x10)
    //     0xac6c4c: sub             SP, SP, #0x10
    // 0xac6c50: LoadField: d0 = r1->field_7
    //     0xac6c50: ldur            d0, [x1, #7]
    // 0xac6c54: LoadField: d1 = r2->field_7
    //     0xac6c54: ldur            d1, [x2, #7]
    // 0xac6c58: fadd            d2, d0, d1
    // 0xac6c5c: stur            d2, [fp, #-0x10]
    // 0xac6c60: LoadField: d0 = r1->field_f
    //     0xac6c60: ldur            d0, [x1, #0xf]
    // 0xac6c64: LoadField: d1 = r2->field_f
    //     0xac6c64: ldur            d1, [x2, #0xf]
    // 0xac6c68: fadd            d3, d0, d1
    // 0xac6c6c: stur            d3, [fp, #-8]
    // 0xac6c70: r0 = _PathOffset()
    //     0xac6c70: bl              #0xac6c38  ; Allocate_PathOffsetStub -> _PathOffset (size=0x18)
    // 0xac6c74: ldur            d0, [fp, #-0x10]
    // 0xac6c78: StoreField: r0->field_7 = d0
    //     0xac6c78: stur            d0, [x0, #7]
    // 0xac6c7c: ldur            d0, [fp, #-8]
    // 0xac6c80: StoreField: r0->field_f = d0
    //     0xac6c80: stur            d0, [x0, #0xf]
    // 0xac6c84: LeaveFrame
    //     0xac6c84: mov             SP, fp
    //     0xac6c88: ldp             fp, lr, [SP], #0x10
    // 0xac6c8c: ret
    //     0xac6c8c: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1774, size: 0xe0
    // 0xbf1774: ldr             x2, [SP]
    // 0xbf1778: LoadField: d0 = r2->field_7
    //     0xbf1778: ldur            d0, [x2, #7]
    // 0xbf177c: mov             x16, v0.d[0]
    // 0xbf1780: and             x16, x16, #0x7ff0000000000000
    // 0xbf1784: r17 = 9218868437227405312
    //     0xbf1784: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf1788: cmp             x16, x17
    // 0xbf178c: b.eq            #0xbf17bc
    // 0xbf1790: fcvtzs          x16, d0
    // 0xbf1794: scvtf           d1, x16
    // 0xbf1798: fcmp            d1, d0
    // 0xbf179c: b.ne            #0xbf17bc
    // 0xbf17a0: r17 = 11601
    //     0xbf17a0: movz            x17, #0x2d51
    // 0xbf17a4: mul             x3, x16, x17
    // 0xbf17a8: umulh           x16, x16, x17
    // 0xbf17ac: eor             x3, x3, x16
    // 0xbf17b0: r3 = 0
    //     0xbf17b0: eor             x3, x3, x3, lsr #32
    // 0xbf17b4: and             x3, x3, #0x3fffffff
    // 0xbf17b8: b               #0xbf17c8
    // 0xbf17bc: r3 = 0.000000
    //     0xbf17bc: fmov            x3, d0
    // 0xbf17c0: r3 = 0
    //     0xbf17c0: eor             x3, x3, x3, lsr #32
    // 0xbf17c4: and             x3, x3, #0x3fffffff
    // 0xbf17c8: r16 = 391
    //     0xbf17c8: movz            x16, #0x187
    // 0xbf17cc: eor             x4, x3, x16
    // 0xbf17d0: r16 = 23
    //     0xbf17d0: movz            x16, #0x17
    // 0xbf17d4: mul             x3, x4, x16
    // 0xbf17d8: LoadField: d0 = r2->field_f
    //     0xbf17d8: ldur            d0, [x2, #0xf]
    // 0xbf17dc: mov             x16, v0.d[0]
    // 0xbf17e0: and             x16, x16, #0x7ff0000000000000
    // 0xbf17e4: r17 = 9218868437227405312
    //     0xbf17e4: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf17e8: cmp             x16, x17
    // 0xbf17ec: b.eq            #0xbf181c
    // 0xbf17f0: fcvtzs          x16, d0
    // 0xbf17f4: scvtf           d1, x16
    // 0xbf17f8: fcmp            d1, d0
    // 0xbf17fc: b.ne            #0xbf181c
    // 0xbf1800: r17 = 11601
    //     0xbf1800: movz            x17, #0x2d51
    // 0xbf1804: mul             x2, x16, x17
    // 0xbf1808: umulh           x16, x16, x17
    // 0xbf180c: eor             x2, x2, x16
    // 0xbf1810: r2 = 0
    //     0xbf1810: eor             x2, x2, x2, lsr #32
    // 0xbf1814: and             x2, x2, #0x3fffffff
    // 0xbf1818: b               #0xbf1828
    // 0xbf181c: r2 = 0.000000
    //     0xbf181c: fmov            x2, d0
    // 0xbf1820: r2 = 0
    //     0xbf1820: eor             x2, x2, x2, lsr #32
    // 0xbf1824: and             x2, x2, #0x3fffffff
    // 0xbf1828: eor             x4, x3, x2
    // 0xbf182c: r0 = BoxInt64Instr(r4)
    //     0xbf182c: sbfiz           x0, x4, #1, #0x1f
    //     0xbf1830: cmp             x4, x0, asr #1
    //     0xbf1834: b.eq            #0xbf1850
    //     0xbf1838: stp             fp, lr, [SP, #-0x10]!
    //     0xbf183c: mov             fp, SP
    //     0xbf1840: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1844: mov             SP, fp
    //     0xbf1848: ldp             fp, lr, [SP], #0x10
    //     0xbf184c: stur            x4, [x0, #7]
    // 0xbf1850: ret
    //     0xbf1850: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc338d0, size: 0x100
    // 0xc338d0: EnterFrame
    //     0xc338d0: stp             fp, lr, [SP, #-0x10]!
    //     0xc338d4: mov             fp, SP
    // 0xc338d8: AllocStack(0x8)
    //     0xc338d8: sub             SP, SP, #8
    // 0xc338dc: CheckStackOverflow
    //     0xc338dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc338e0: cmp             SP, x16
    //     0xc338e4: b.ls            #0xc33990
    // 0xc338e8: r1 = Null
    //     0xc338e8: mov             x1, NULL
    // 0xc338ec: r2 = 10
    //     0xc338ec: movz            x2, #0xa
    // 0xc338f0: r0 = AllocateArray()
    //     0xc338f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc338f4: r16 = "PathOffset{"
    //     0xc338f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x314e0] "PathOffset{"
    //     0xc338f8: ldr             x16, [x16, #0x4e0]
    // 0xc338fc: StoreField: r0->field_f = r16
    //     0xc338fc: stur            w16, [x0, #0xf]
    // 0xc33900: ldr             x1, [fp, #0x10]
    // 0xc33904: LoadField: d0 = r1->field_7
    //     0xc33904: ldur            d0, [x1, #7]
    // 0xc33908: r2 = inline_Allocate_Double()
    //     0xc33908: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc3390c: add             x2, x2, #0x10
    //     0xc33910: cmp             x3, x2
    //     0xc33914: b.ls            #0xc33998
    //     0xc33918: str             x2, [THR, #0x50]  ; THR::top
    //     0xc3391c: sub             x2, x2, #0xf
    //     0xc33920: movz            x3, #0xe15c
    //     0xc33924: movk            x3, #0x3, lsl #16
    //     0xc33928: stur            x3, [x2, #-1]
    // 0xc3392c: StoreField: r2->field_7 = d0
    //     0xc3392c: stur            d0, [x2, #7]
    // 0xc33930: StoreField: r0->field_13 = r2
    //     0xc33930: stur            w2, [x0, #0x13]
    // 0xc33934: r16 = ","
    //     0xc33934: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xc33938: ldr             x16, [x16, #0x5f8]
    // 0xc3393c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3393c: stur            w16, [x0, #0x17]
    // 0xc33940: LoadField: d0 = r1->field_f
    //     0xc33940: ldur            d0, [x1, #0xf]
    // 0xc33944: r1 = inline_Allocate_Double()
    //     0xc33944: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc33948: add             x1, x1, #0x10
    //     0xc3394c: cmp             x2, x1
    //     0xc33950: b.ls            #0xc339b4
    //     0xc33954: str             x1, [THR, #0x50]  ; THR::top
    //     0xc33958: sub             x1, x1, #0xf
    //     0xc3395c: movz            x2, #0xe15c
    //     0xc33960: movk            x2, #0x3, lsl #16
    //     0xc33964: stur            x2, [x1, #-1]
    // 0xc33968: StoreField: r1->field_7 = d0
    //     0xc33968: stur            d0, [x1, #7]
    // 0xc3396c: StoreField: r0->field_1b = r1
    //     0xc3396c: stur            w1, [x0, #0x1b]
    // 0xc33970: r16 = "}"
    //     0xc33970: add             x16, PP, #0x12, lsl #12  ; [pp+0x12240] "}"
    //     0xc33974: ldr             x16, [x16, #0x240]
    // 0xc33978: StoreField: r0->field_1f = r16
    //     0xc33978: stur            w16, [x0, #0x1f]
    // 0xc3397c: str             x0, [SP]
    // 0xc33980: r0 = _interpolate()
    //     0xc33980: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc33984: LeaveFrame
    //     0xc33984: mov             SP, fp
    //     0xc33988: ldp             fp, lr, [SP], #0x10
    // 0xc3398c: ret
    //     0xc3398c: ret             
    // 0xc33990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33994: b               #0xc338e8
    // 0xc33998: SaveReg d0
    //     0xc33998: str             q0, [SP, #-0x10]!
    // 0xc3399c: stp             x0, x1, [SP, #-0x10]!
    // 0xc339a0: r0 = AllocateDouble()
    //     0xc339a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc339a4: mov             x2, x0
    // 0xc339a8: ldp             x0, x1, [SP], #0x10
    // 0xc339ac: RestoreReg d0
    //     0xc339ac: ldr             q0, [SP], #0x10
    // 0xc339b0: b               #0xc3392c
    // 0xc339b4: SaveReg d0
    //     0xc339b4: str             q0, [SP, #-0x10]!
    // 0xc339b8: SaveReg r0
    //     0xc339b8: str             x0, [SP, #-8]!
    // 0xc339bc: r0 = AllocateDouble()
    //     0xc339bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc339c0: mov             x1, x0
    // 0xc339c4: RestoreReg r0
    //     0xc339c4: ldr             x0, [SP], #8
    // 0xc339c8: RestoreReg d0
    //     0xc339c8: ldr             q0, [SP], #0x10
    // 0xc339cc: b               #0xc33968
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c3f0, size: 0x68
    // 0xd7c3f0: ldr             x1, [SP]
    // 0xd7c3f4: cmp             w1, NULL
    // 0xd7c3f8: b.ne            #0xd7c404
    // 0xd7c3fc: r0 = false
    //     0xd7c3fc: add             x0, NULL, #0x30  ; false
    // 0xd7c400: ret
    //     0xd7c400: ret             
    // 0xd7c404: r2 = 60
    //     0xd7c404: movz            x2, #0x3c
    // 0xd7c408: branchIfSmi(r1, 0xd7c414)
    //     0xd7c408: tbz             w1, #0, #0xd7c414
    // 0xd7c40c: r2 = LoadClassIdInstr(r1)
    //     0xd7c40c: ldur            x2, [x1, #-1]
    //     0xd7c410: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c414: cmp             x2, #0x3a4
    // 0xd7c418: b.ne            #0xd7c450
    // 0xd7c41c: ldr             x2, [SP, #8]
    // 0xd7c420: LoadField: d0 = r1->field_7
    //     0xd7c420: ldur            d0, [x1, #7]
    // 0xd7c424: LoadField: d1 = r2->field_7
    //     0xd7c424: ldur            d1, [x2, #7]
    // 0xd7c428: fcmp            d0, d1
    // 0xd7c42c: b.ne            #0xd7c450
    // 0xd7c430: LoadField: d0 = r1->field_f
    //     0xd7c430: ldur            d0, [x1, #0xf]
    // 0xd7c434: LoadField: d1 = r2->field_f
    //     0xd7c434: ldur            d1, [x2, #0xf]
    // 0xd7c438: fcmp            d0, d1
    // 0xd7c43c: r16 = true
    //     0xd7c43c: add             x16, NULL, #0x20  ; true
    // 0xd7c440: r17 = false
    //     0xd7c440: add             x17, NULL, #0x30  ; false
    // 0xd7c444: csel            x1, x16, x17, eq
    // 0xd7c448: mov             x0, x1
    // 0xd7c44c: b               #0xd7c454
    // 0xd7c450: r0 = false
    //     0xd7c450: add             x0, NULL, #0x30  ; false
    // 0xd7c454: ret
    //     0xd7c454: ret             
  }
}

// class id: 933, size: 0x8, field offset: 0x8
abstract class PathProxy extends Object {
}
