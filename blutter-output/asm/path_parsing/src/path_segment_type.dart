// lib: , url: package:path_parsing/src/path_segment_type.dart

// class id: 1050765, size: 0x8
class :: {
}

// class id: 928, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class AsciiConstants extends Object {

  static _ mapLetterToSegmentType(/* No info */) {
    // ** addr: 0xac7ff4, size: 0x48
    // 0xac7ff4: EnterFrame
    //     0xac7ff4: stp             fp, lr, [SP, #-0x10]!
    //     0xac7ff8: mov             fp, SP
    // 0xac7ffc: CheckStackOverflow
    //     0xac7ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac8000: cmp             SP, x16
    //     0xac8004: b.ls            #0xac8034
    // 0xac8008: lsl             x2, x1, #1
    // 0xac800c: r1 = _ConstMap len:20
    //     0xac800c: add             x1, PP, #0x26, lsl #12  ; [pp+0x262c8] Map<int, SvgPathSegType>(20)
    //     0xac8010: ldr             x1, [x1, #0x2c8]
    // 0xac8014: r0 = []()
    //     0xac8014: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xac8018: cmp             w0, NULL
    // 0xac801c: b.ne            #0xac8028
    // 0xac8020: r0 = Instance_SvgPathSegType
    //     0xac8020: add             x0, PP, #0x26, lsl #12  ; [pp+0x26150] Obj!SvgPathSegType@e2ffa1
    //     0xac8024: ldr             x0, [x0, #0x150]
    // 0xac8028: LeaveFrame
    //     0xac8028: mov             SP, fp
    //     0xac802c: ldp             fp, lr, [SP], #0x10
    // 0xac8030: ret
    //     0xac8030: ret             
    // 0xac8034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac8034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac8038: b               #0xac8008
  }
}

// class id: 6823, size: 0x14, field offset: 0x14
enum SvgPathSegType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d6f4, size: 0x64
    // 0xc4d6f4: EnterFrame
    //     0xc4d6f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d6f8: mov             fp, SP
    // 0xc4d6fc: AllocStack(0x10)
    //     0xc4d6fc: sub             SP, SP, #0x10
    // 0xc4d700: SetupParameters(SvgPathSegType this /* r1 => r0, fp-0x8 */)
    //     0xc4d700: mov             x0, x1
    //     0xc4d704: stur            x1, [fp, #-8]
    // 0xc4d708: CheckStackOverflow
    //     0xc4d708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d70c: cmp             SP, x16
    //     0xc4d710: b.ls            #0xc4d750
    // 0xc4d714: r1 = Null
    //     0xc4d714: mov             x1, NULL
    // 0xc4d718: r2 = 4
    //     0xc4d718: movz            x2, #0x4
    // 0xc4d71c: r0 = AllocateArray()
    //     0xc4d71c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d720: r16 = "SvgPathSegType."
    //     0xc4d720: add             x16, PP, #0x31, lsl #12  ; [pp+0x314d8] "SvgPathSegType."
    //     0xc4d724: ldr             x16, [x16, #0x4d8]
    // 0xc4d728: StoreField: r0->field_f = r16
    //     0xc4d728: stur            w16, [x0, #0xf]
    // 0xc4d72c: ldur            x1, [fp, #-8]
    // 0xc4d730: LoadField: r2 = r1->field_f
    //     0xc4d730: ldur            w2, [x1, #0xf]
    // 0xc4d734: DecompressPointer r2
    //     0xc4d734: add             x2, x2, HEAP, lsl #32
    // 0xc4d738: StoreField: r0->field_13 = r2
    //     0xc4d738: stur            w2, [x0, #0x13]
    // 0xc4d73c: str             x0, [SP]
    // 0xc4d740: r0 = _interpolate()
    //     0xc4d740: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d744: LeaveFrame
    //     0xc4d744: mov             SP, fp
    //     0xc4d748: ldp             fp, lr, [SP], #0x10
    // 0xc4d74c: ret
    //     0xc4d74c: ret             
    // 0xc4d750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d754: b               #0xc4d714
  }
}
