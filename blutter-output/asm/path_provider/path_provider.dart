// lib: , url: package:path_provider/path_provider.dart

// class id: 1050766, size: 0x8
class :: {

  static _ getApplicationDocumentsDirectory(/* No info */) async {
    // ** addr: 0x7ed868, size: 0xcc
    // 0x7ed868: EnterFrame
    //     0x7ed868: stp             fp, lr, [SP, #-0x10]!
    //     0x7ed86c: mov             fp, SP
    // 0x7ed870: AllocStack(0x28)
    //     0x7ed870: sub             SP, SP, #0x28
    // 0x7ed874: SetupParameters()
    //     0x7ed874: stur            NULL, [fp, #-8]
    // 0x7ed878: CheckStackOverflow
    //     0x7ed878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ed87c: cmp             SP, x16
    //     0x7ed880: b.ls            #0x7ed92c
    // 0x7ed884: InitAsync() -> Future<Directory>
    //     0x7ed884: ldr             x0, [PP, #0x2e0]  ; [pp+0x2e0] TypeArguments: <Directory>
    //     0x7ed888: bl              #0x661298  ; InitAsyncStub
    // 0x7ed88c: r0 = InitLateStaticField(0x60c) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_instance
    //     0x7ed88c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7ed890: ldr             x0, [x0, #0xc18]
    //     0x7ed894: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7ed898: cmp             w0, w16
    //     0x7ed89c: b.ne            #0x7ed8a8
    //     0x7ed8a0: ldr             x2, [PP, #0x2e8]  ; [pp+0x2e8] Field <PathProviderPlatform._instance@678436587>: static late (offset: 0x60c)
    //     0x7ed8a4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7ed8a8: r1 = LoadClassIdInstr(r0)
    //     0x7ed8a8: ldur            x1, [x0, #-1]
    //     0x7ed8ac: ubfx            x1, x1, #0xc, #0x14
    // 0x7ed8b0: r17 = 5874
    //     0x7ed8b0: movz            x17, #0x16f2
    // 0x7ed8b4: cmp             x1, x17
    // 0x7ed8b8: b.ne            #0x7ed8e0
    // 0x7ed8bc: r16 = <String>
    //     0x7ed8bc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7ed8c0: r30 = Instance_MethodChannel
    //     0x7ed8c0: ldr             lr, [PP, #0x2f0]  ; [pp+0x2f0] Obj!MethodChannel@e11071
    // 0x7ed8c4: stp             lr, x16, [SP, #8]
    // 0x7ed8c8: r16 = "getApplicationDocumentsDirectory"
    //     0x7ed8c8: ldr             x16, [PP, #0x2f8]  ; [pp+0x2f8] "getApplicationDocumentsDirectory"
    // 0x7ed8cc: str             x16, [SP]
    // 0x7ed8d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ed8d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ed8d4: r0 = invokeMethod()
    //     0x7ed8d4: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x7ed8d8: mov             x1, x0
    // 0x7ed8dc: b               #0x7ed8f0
    // 0x7ed8e0: LoadField: r1 = r0->field_7
    //     0x7ed8e0: ldur            w1, [x0, #7]
    // 0x7ed8e4: DecompressPointer r1
    //     0x7ed8e4: add             x1, x1, HEAP, lsl #32
    // 0x7ed8e8: r0 = getApplicationDocumentsPath()
    //     0x7ed8e8: bl              #0x7ed940  ; [package:path_provider_android/messages.g.dart] PathProviderApi::getApplicationDocumentsPath
    // 0x7ed8ec: mov             x1, x0
    // 0x7ed8f0: mov             x0, x1
    // 0x7ed8f4: stur            x1, [fp, #-0x10]
    // 0x7ed8f8: r0 = Await()
    //     0x7ed8f8: bl              #0x661044  ; AwaitStub
    // 0x7ed8fc: cmp             w0, NULL
    // 0x7ed900: b.eq            #0x7ed910
    // 0x7ed904: stp             x0, NULL, [SP]
    // 0x7ed908: r0 = Directory()
    //     0x7ed908: bl              #0x60ca18  ; [dart:io] Directory::Directory
    // 0x7ed90c: r0 = ReturnAsyncNotFuture()
    //     0x7ed90c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7ed910: r0 = MissingPlatformDirectoryException()
    //     0x7ed910: bl              #0x7ed934  ; AllocateMissingPlatformDirectoryExceptionStub -> MissingPlatformDirectoryException (size=0x10)
    // 0x7ed914: mov             x1, x0
    // 0x7ed918: r0 = "Unable to get application documents directory"
    //     0x7ed918: ldr             x0, [PP, #0x300]  ; [pp+0x300] "Unable to get application documents directory"
    // 0x7ed91c: StoreField: r1->field_7 = r0
    //     0x7ed91c: stur            w0, [x1, #7]
    // 0x7ed920: mov             x0, x1
    // 0x7ed924: r0 = Throw()
    //     0x7ed924: bl              #0xec04b8  ; ThrowStub
    // 0x7ed928: brk             #0
    // 0x7ed92c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ed92c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ed930: b               #0x7ed884
  }
  static _ getTemporaryDirectory(/* No info */) async {
    // ** addr: 0x8b6a10, size: 0xec
    // 0x8b6a10: EnterFrame
    //     0x8b6a10: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6a14: mov             fp, SP
    // 0x8b6a18: AllocStack(0x28)
    //     0x8b6a18: sub             SP, SP, #0x28
    // 0x8b6a1c: SetupParameters()
    //     0x8b6a1c: stur            NULL, [fp, #-8]
    // 0x8b6a20: CheckStackOverflow
    //     0x8b6a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6a24: cmp             SP, x16
    //     0x8b6a28: b.ls            #0x8b6af4
    // 0x8b6a2c: InitAsync() -> Future<Directory>
    //     0x8b6a2c: ldr             x0, [PP, #0x2e0]  ; [pp+0x2e0] TypeArguments: <Directory>
    //     0x8b6a30: bl              #0x661298  ; InitAsyncStub
    // 0x8b6a34: r0 = InitLateStaticField(0x60c) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_instance
    //     0x8b6a34: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b6a38: ldr             x0, [x0, #0xc18]
    //     0x8b6a3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6a40: cmp             w0, w16
    //     0x8b6a44: b.ne            #0x8b6a50
    //     0x8b6a48: ldr             x2, [PP, #0x2e8]  ; [pp+0x2e8] Field <PathProviderPlatform._instance@678436587>: static late (offset: 0x60c)
    //     0x8b6a4c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8b6a50: r1 = LoadClassIdInstr(r0)
    //     0x8b6a50: ldur            x1, [x0, #-1]
    //     0x8b6a54: ubfx            x1, x1, #0xc, #0x14
    // 0x8b6a58: r17 = 5874
    //     0x8b6a58: movz            x17, #0x16f2
    // 0x8b6a5c: cmp             x1, x17
    // 0x8b6a60: b.ne            #0x8b6a8c
    // 0x8b6a64: r16 = <String>
    //     0x8b6a64: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8b6a68: r30 = Instance_MethodChannel
    //     0x8b6a68: ldr             lr, [PP, #0x2f0]  ; [pp+0x2f0] Obj!MethodChannel@e11071
    // 0x8b6a6c: stp             lr, x16, [SP, #8]
    // 0x8b6a70: r16 = "getTemporaryDirectory"
    //     0x8b6a70: add             x16, PP, #0xd, lsl #12  ; [pp+0xdca8] "getTemporaryDirectory"
    //     0x8b6a74: ldr             x16, [x16, #0xca8]
    // 0x8b6a78: str             x16, [SP]
    // 0x8b6a7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b6a7c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b6a80: r0 = invokeMethod()
    //     0x8b6a80: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x8b6a84: mov             x1, x0
    // 0x8b6a88: b               #0x8b6a9c
    // 0x8b6a8c: LoadField: r1 = r0->field_7
    //     0x8b6a8c: ldur            w1, [x0, #7]
    // 0x8b6a90: DecompressPointer r1
    //     0x8b6a90: add             x1, x1, HEAP, lsl #32
    // 0x8b6a94: r0 = getTemporaryPath()
    //     0x8b6a94: bl              #0x8b6afc  ; [package:path_provider_android/messages.g.dart] PathProviderApi::getTemporaryPath
    // 0x8b6a98: mov             x1, x0
    // 0x8b6a9c: mov             x0, x1
    // 0x8b6aa0: stur            x1, [fp, #-0x10]
    // 0x8b6aa4: r0 = Await()
    //     0x8b6aa4: bl              #0x661044  ; AwaitStub
    // 0x8b6aa8: stur            x0, [fp, #-0x10]
    // 0x8b6aac: cmp             w0, NULL
    // 0x8b6ab0: b.eq            #0x8b6ad4
    // 0x8b6ab4: r0 = current()
    //     0x8b6ab4: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0x8b6ab8: r0 = _Directory()
    //     0x8b6ab8: bl              #0x60ca0c  ; Allocate_DirectoryStub -> _Directory (size=0x10)
    // 0x8b6abc: mov             x1, x0
    // 0x8b6ac0: ldur            x2, [fp, #-0x10]
    // 0x8b6ac4: stur            x0, [fp, #-0x10]
    // 0x8b6ac8: r0 = _File()
    //     0x8b6ac8: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0x8b6acc: ldur            x0, [fp, #-0x10]
    // 0x8b6ad0: r0 = ReturnAsyncNotFuture()
    //     0x8b6ad0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b6ad4: r0 = MissingPlatformDirectoryException()
    //     0x8b6ad4: bl              #0x7ed934  ; AllocateMissingPlatformDirectoryExceptionStub -> MissingPlatformDirectoryException (size=0x10)
    // 0x8b6ad8: mov             x1, x0
    // 0x8b6adc: r0 = "Unable to get temporary directory"
    //     0x8b6adc: add             x0, PP, #0xd, lsl #12  ; [pp+0xdcb0] "Unable to get temporary directory"
    //     0x8b6ae0: ldr             x0, [x0, #0xcb0]
    // 0x8b6ae4: StoreField: r1->field_7 = r0
    //     0x8b6ae4: stur            w0, [x1, #7]
    // 0x8b6ae8: mov             x0, x1
    // 0x8b6aec: r0 = Throw()
    //     0x8b6aec: bl              #0xec04b8  ; ThrowStub
    // 0x8b6af0: brk             #0
    // 0x8b6af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6af4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6af8: b               #0x8b6a2c
  }
  static _ getApplicationSupportDirectory(/* No info */) async {
    // ** addr: 0xab72f4, size: 0xec
    // 0xab72f4: EnterFrame
    //     0xab72f4: stp             fp, lr, [SP, #-0x10]!
    //     0xab72f8: mov             fp, SP
    // 0xab72fc: AllocStack(0x28)
    //     0xab72fc: sub             SP, SP, #0x28
    // 0xab7300: SetupParameters()
    //     0xab7300: stur            NULL, [fp, #-8]
    // 0xab7304: CheckStackOverflow
    //     0xab7304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab7308: cmp             SP, x16
    //     0xab730c: b.ls            #0xab73d8
    // 0xab7310: InitAsync() -> Future<Directory>
    //     0xab7310: ldr             x0, [PP, #0x2e0]  ; [pp+0x2e0] TypeArguments: <Directory>
    //     0xab7314: bl              #0x661298  ; InitAsyncStub
    // 0xab7318: r0 = InitLateStaticField(0x60c) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_instance
    //     0xab7318: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab731c: ldr             x0, [x0, #0xc18]
    //     0xab7320: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab7324: cmp             w0, w16
    //     0xab7328: b.ne            #0xab7334
    //     0xab732c: ldr             x2, [PP, #0x2e8]  ; [pp+0x2e8] Field <PathProviderPlatform._instance@678436587>: static late (offset: 0x60c)
    //     0xab7330: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab7334: r1 = LoadClassIdInstr(r0)
    //     0xab7334: ldur            x1, [x0, #-1]
    //     0xab7338: ubfx            x1, x1, #0xc, #0x14
    // 0xab733c: r17 = 5874
    //     0xab733c: movz            x17, #0x16f2
    // 0xab7340: cmp             x1, x17
    // 0xab7344: b.ne            #0xab7370
    // 0xab7348: r16 = <String>
    //     0xab7348: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xab734c: r30 = Instance_MethodChannel
    //     0xab734c: ldr             lr, [PP, #0x2f0]  ; [pp+0x2f0] Obj!MethodChannel@e11071
    // 0xab7350: stp             lr, x16, [SP, #8]
    // 0xab7354: r16 = "getApplicationSupportDirectory"
    //     0xab7354: add             x16, PP, #0x43, lsl #12  ; [pp+0x433e8] "getApplicationSupportDirectory"
    //     0xab7358: ldr             x16, [x16, #0x3e8]
    // 0xab735c: str             x16, [SP]
    // 0xab7360: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab7360: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab7364: r0 = invokeMethod()
    //     0xab7364: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xab7368: mov             x1, x0
    // 0xab736c: b               #0xab7380
    // 0xab7370: LoadField: r1 = r0->field_7
    //     0xab7370: ldur            w1, [x0, #7]
    // 0xab7374: DecompressPointer r1
    //     0xab7374: add             x1, x1, HEAP, lsl #32
    // 0xab7378: r0 = getApplicationSupportPath()
    //     0xab7378: bl              #0xab73e0  ; [package:path_provider_android/messages.g.dart] PathProviderApi::getApplicationSupportPath
    // 0xab737c: mov             x1, x0
    // 0xab7380: mov             x0, x1
    // 0xab7384: stur            x1, [fp, #-0x10]
    // 0xab7388: r0 = Await()
    //     0xab7388: bl              #0x661044  ; AwaitStub
    // 0xab738c: stur            x0, [fp, #-0x10]
    // 0xab7390: cmp             w0, NULL
    // 0xab7394: b.eq            #0xab73b8
    // 0xab7398: r0 = current()
    //     0xab7398: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0xab739c: r0 = _Directory()
    //     0xab739c: bl              #0x60ca0c  ; Allocate_DirectoryStub -> _Directory (size=0x10)
    // 0xab73a0: mov             x1, x0
    // 0xab73a4: ldur            x2, [fp, #-0x10]
    // 0xab73a8: stur            x0, [fp, #-0x10]
    // 0xab73ac: r0 = _File()
    //     0xab73ac: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0xab73b0: ldur            x0, [fp, #-0x10]
    // 0xab73b4: r0 = ReturnAsyncNotFuture()
    //     0xab73b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab73b8: r0 = MissingPlatformDirectoryException()
    //     0xab73b8: bl              #0x7ed934  ; AllocateMissingPlatformDirectoryExceptionStub -> MissingPlatformDirectoryException (size=0x10)
    // 0xab73bc: mov             x1, x0
    // 0xab73c0: r0 = "Unable to get application support directory"
    //     0xab73c0: add             x0, PP, #0x43, lsl #12  ; [pp+0x433f0] "Unable to get application support directory"
    //     0xab73c4: ldr             x0, [x0, #0x3f0]
    // 0xab73c8: StoreField: r1->field_7 = r0
    //     0xab73c8: stur            w0, [x1, #7]
    // 0xab73cc: mov             x0, x1
    // 0xab73d0: r0 = Throw()
    //     0xab73d0: bl              #0xec04b8  ; ThrowStub
    // 0xab73d4: brk             #0
    // 0xab73d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab73d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab73dc: b               #0xab7310
  }
}

// class id: 927, size: 0x10, field offset: 0x8
class MissingPlatformDirectoryException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xc33a9c, size: 0x6c
    // 0xc33a9c: EnterFrame
    //     0xc33a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc33aa0: mov             fp, SP
    // 0xc33aa4: AllocStack(0x8)
    //     0xc33aa4: sub             SP, SP, #8
    // 0xc33aa8: CheckStackOverflow
    //     0xc33aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33aac: cmp             SP, x16
    //     0xc33ab0: b.ls            #0xc33b00
    // 0xc33ab4: r1 = Null
    //     0xc33ab4: mov             x1, NULL
    // 0xc33ab8: r2 = 8
    //     0xc33ab8: movz            x2, #0x8
    // 0xc33abc: r0 = AllocateArray()
    //     0xc33abc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33ac0: r16 = "MissingPlatformDirectoryException("
    //     0xc33ac0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c880] "MissingPlatformDirectoryException("
    //     0xc33ac4: ldr             x16, [x16, #0x880]
    // 0xc33ac8: StoreField: r0->field_f = r16
    //     0xc33ac8: stur            w16, [x0, #0xf]
    // 0xc33acc: ldr             x1, [fp, #0x10]
    // 0xc33ad0: LoadField: r2 = r1->field_7
    //     0xc33ad0: ldur            w2, [x1, #7]
    // 0xc33ad4: DecompressPointer r2
    //     0xc33ad4: add             x2, x2, HEAP, lsl #32
    // 0xc33ad8: StoreField: r0->field_13 = r2
    //     0xc33ad8: stur            w2, [x0, #0x13]
    // 0xc33adc: r16 = ")"
    //     0xc33adc: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc33ae0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc33ae0: stur            w16, [x0, #0x17]
    // 0xc33ae4: r16 = ""
    //     0xc33ae4: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc33ae8: StoreField: r0->field_1b = r16
    //     0xc33ae8: stur            w16, [x0, #0x1b]
    // 0xc33aec: str             x0, [SP]
    // 0xc33af0: r0 = _interpolate()
    //     0xc33af0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc33af4: LeaveFrame
    //     0xc33af4: mov             SP, fp
    //     0xc33af8: ldp             fp, lr, [SP], #0x10
    // 0xc33afc: ret
    //     0xc33afc: ret             
    // 0xc33b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33b00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33b04: b               #0xc33ab4
  }
}
