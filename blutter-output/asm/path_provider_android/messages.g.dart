// lib: , url: package:path_provider_android/messages.g.dart

// class id: 1050767, size: 0x8
class :: {
}

// class id: 926, size: 0x10, field offset: 0x8
class PathProviderApi extends Object {

  _ getApplicationDocumentsPath(/* No info */) async {
    // ** addr: 0x7ed940, size: 0x2a0
    // 0x7ed940: EnterFrame
    //     0x7ed940: stp             fp, lr, [SP, #-0x10]!
    //     0x7ed944: mov             fp, SP
    // 0x7ed948: AllocStack(0x30)
    //     0x7ed948: sub             SP, SP, #0x30
    // 0x7ed94c: SetupParameters(PathProviderApi this /* r1 => r1, fp-0x10 */)
    //     0x7ed94c: stur            NULL, [fp, #-8]
    //     0x7ed950: stur            x1, [fp, #-0x10]
    // 0x7ed954: CheckStackOverflow
    //     0x7ed954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ed958: cmp             SP, x16
    //     0x7ed95c: b.ls            #0x7edbd4
    // 0x7ed960: InitAsync() -> Future<String?>
    //     0x7ed960: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0x7ed964: bl              #0x661298  ; InitAsyncStub
    // 0x7ed968: r1 = Null
    //     0x7ed968: mov             x1, NULL
    // 0x7ed96c: r2 = 4
    //     0x7ed96c: movz            x2, #0x4
    // 0x7ed970: r0 = AllocateArray()
    //     0x7ed970: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ed974: r16 = "dev.flutter.pigeon.path_provider_android.PathProviderApi.getApplicationDocumentsPath"
    //     0x7ed974: ldr             x16, [PP, #0x310]  ; [pp+0x310] "dev.flutter.pigeon.path_provider_android.PathProviderApi.getApplicationDocumentsPath"
    // 0x7ed978: StoreField: r0->field_f = r16
    //     0x7ed978: stur            w16, [x0, #0xf]
    // 0x7ed97c: ldur            x1, [fp, #-0x10]
    // 0x7ed980: LoadField: r2 = r1->field_b
    //     0x7ed980: ldur            w2, [x1, #0xb]
    // 0x7ed984: DecompressPointer r2
    //     0x7ed984: add             x2, x2, HEAP, lsl #32
    // 0x7ed988: StoreField: r0->field_13 = r2
    //     0x7ed988: stur            w2, [x0, #0x13]
    // 0x7ed98c: str             x0, [SP]
    // 0x7ed990: r0 = _interpolate()
    //     0x7ed990: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7ed994: r1 = <Object?>
    //     0x7ed994: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x7ed998: stur            x0, [fp, #-0x10]
    // 0x7ed99c: r0 = BasicMessageChannel()
    //     0x7ed99c: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x7ed9a0: mov             x1, x0
    // 0x7ed9a4: ldur            x0, [fp, #-0x10]
    // 0x7ed9a8: StoreField: r1->field_b = r0
    //     0x7ed9a8: stur            w0, [x1, #0xb]
    // 0x7ed9ac: r2 = Instance__PigeonCodec
    //     0x7ed9ac: ldr             x2, [PP, #0x318]  ; [pp+0x318] Obj!_PigeonCodec@e259b1
    // 0x7ed9b0: StoreField: r1->field_f = r2
    //     0x7ed9b0: stur            w2, [x1, #0xf]
    // 0x7ed9b4: r2 = Null
    //     0x7ed9b4: mov             x2, NULL
    // 0x7ed9b8: r0 = send()
    //     0x7ed9b8: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0x7ed9bc: mov             x1, x0
    // 0x7ed9c0: stur            x1, [fp, #-0x18]
    // 0x7ed9c4: r0 = Await()
    //     0x7ed9c4: bl              #0x661044  ; AwaitStub
    // 0x7ed9c8: mov             x3, x0
    // 0x7ed9cc: r2 = Null
    //     0x7ed9cc: mov             x2, NULL
    // 0x7ed9d0: r1 = Null
    //     0x7ed9d0: mov             x1, NULL
    // 0x7ed9d4: stur            x3, [fp, #-0x18]
    // 0x7ed9d8: r4 = 60
    //     0x7ed9d8: movz            x4, #0x3c
    // 0x7ed9dc: branchIfSmi(r0, 0x7ed9e8)
    //     0x7ed9dc: tbz             w0, #0, #0x7ed9e8
    // 0x7ed9e0: r4 = LoadClassIdInstr(r0)
    //     0x7ed9e0: ldur            x4, [x0, #-1]
    //     0x7ed9e4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ed9e8: sub             x4, x4, #0x5a
    // 0x7ed9ec: cmp             x4, #2
    // 0x7ed9f0: b.ls            #0x7eda00
    // 0x7ed9f4: r8 = List<Object?>?
    //     0x7ed9f4: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0x7ed9f8: r3 = Null
    //     0x7ed9f8: ldr             x3, [PP, #0x328]  ; [pp+0x328] Null
    // 0x7ed9fc: r0 = List<Object?>?()
    //     0x7ed9fc: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0x7eda00: ldur            x1, [fp, #-0x18]
    // 0x7eda04: cmp             w1, NULL
    // 0x7eda08: b.eq            #0x7edaa0
    // 0x7eda0c: r0 = LoadClassIdInstr(r1)
    //     0x7eda0c: ldur            x0, [x1, #-1]
    //     0x7eda10: ubfx            x0, x0, #0xc, #0x14
    // 0x7eda14: str             x1, [SP]
    // 0x7eda18: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7eda18: movz            x17, #0xc834
    //     0x7eda1c: add             lr, x0, x17
    //     0x7eda20: ldr             lr, [x21, lr, lsl #3]
    //     0x7eda24: blr             lr
    // 0x7eda28: r1 = LoadInt32Instr(r0)
    //     0x7eda28: sbfx            x1, x0, #1, #0x1f
    //     0x7eda2c: tbz             w0, #0, #0x7eda34
    //     0x7eda30: ldur            x1, [x0, #7]
    // 0x7eda34: cmp             x1, #1
    // 0x7eda38: b.gt            #0x7edab0
    // 0x7eda3c: ldur            x1, [fp, #-0x18]
    // 0x7eda40: r0 = LoadClassIdInstr(r1)
    //     0x7eda40: ldur            x0, [x1, #-1]
    //     0x7eda44: ubfx            x0, x0, #0xc, #0x14
    // 0x7eda48: stp             xzr, x1, [SP]
    // 0x7eda4c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7eda4c: movz            x17, #0x3037
    //     0x7eda50: movk            x17, #0x1, lsl #16
    //     0x7eda54: add             lr, x0, x17
    //     0x7eda58: ldr             lr, [x21, lr, lsl #3]
    //     0x7eda5c: blr             lr
    // 0x7eda60: mov             x3, x0
    // 0x7eda64: r2 = Null
    //     0x7eda64: mov             x2, NULL
    // 0x7eda68: r1 = Null
    //     0x7eda68: mov             x1, NULL
    // 0x7eda6c: stur            x3, [fp, #-0x20]
    // 0x7eda70: r4 = 60
    //     0x7eda70: movz            x4, #0x3c
    // 0x7eda74: branchIfSmi(r0, 0x7eda80)
    //     0x7eda74: tbz             w0, #0, #0x7eda80
    // 0x7eda78: r4 = LoadClassIdInstr(r0)
    //     0x7eda78: ldur            x4, [x0, #-1]
    //     0x7eda7c: ubfx            x4, x4, #0xc, #0x14
    // 0x7eda80: sub             x4, x4, #0x5e
    // 0x7eda84: cmp             x4, #1
    // 0x7eda88: b.ls            #0x7eda98
    // 0x7eda8c: r8 = String?
    //     0x7eda8c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7eda90: r3 = Null
    //     0x7eda90: ldr             x3, [PP, #0x340]  ; [pp+0x340] Null
    // 0x7eda94: r0 = String?()
    //     0x7eda94: bl              #0x600324  ; IsType_String?_Stub
    // 0x7eda98: ldur            x0, [fp, #-0x20]
    // 0x7eda9c: r0 = ReturnAsyncNotFuture()
    //     0x7eda9c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7edaa0: ldur            x1, [fp, #-0x10]
    // 0x7edaa4: r0 = _createConnectionError()
    //     0x7edaa4: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0x7edaa8: r0 = Throw()
    //     0x7edaa8: bl              #0xec04b8  ; ThrowStub
    // 0x7edaac: brk             #0
    // 0x7edab0: ldur            x1, [fp, #-0x18]
    // 0x7edab4: r0 = LoadClassIdInstr(r1)
    //     0x7edab4: ldur            x0, [x1, #-1]
    //     0x7edab8: ubfx            x0, x0, #0xc, #0x14
    // 0x7edabc: stp             xzr, x1, [SP]
    // 0x7edac0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7edac0: movz            x17, #0x3037
    //     0x7edac4: movk            x17, #0x1, lsl #16
    //     0x7edac8: add             lr, x0, x17
    //     0x7edacc: ldr             lr, [x21, lr, lsl #3]
    //     0x7edad0: blr             lr
    // 0x7edad4: mov             x3, x0
    // 0x7edad8: stur            x3, [fp, #-0x10]
    // 0x7edadc: cmp             w3, NULL
    // 0x7edae0: b.eq            #0x7edbdc
    // 0x7edae4: mov             x0, x3
    // 0x7edae8: r2 = Null
    //     0x7edae8: mov             x2, NULL
    // 0x7edaec: r1 = Null
    //     0x7edaec: mov             x1, NULL
    // 0x7edaf0: r4 = 60
    //     0x7edaf0: movz            x4, #0x3c
    // 0x7edaf4: branchIfSmi(r0, 0x7edb00)
    //     0x7edaf4: tbz             w0, #0, #0x7edb00
    // 0x7edaf8: r4 = LoadClassIdInstr(r0)
    //     0x7edaf8: ldur            x4, [x0, #-1]
    //     0x7edafc: ubfx            x4, x4, #0xc, #0x14
    // 0x7edb00: sub             x4, x4, #0x5e
    // 0x7edb04: cmp             x4, #1
    // 0x7edb08: b.ls            #0x7edb18
    // 0x7edb0c: r8 = String
    //     0x7edb0c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7edb10: r3 = Null
    //     0x7edb10: ldr             x3, [PP, #0x350]  ; [pp+0x350] Null
    // 0x7edb14: r0 = String()
    //     0x7edb14: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7edb18: ldur            x1, [fp, #-0x18]
    // 0x7edb1c: r0 = LoadClassIdInstr(r1)
    //     0x7edb1c: ldur            x0, [x1, #-1]
    //     0x7edb20: ubfx            x0, x0, #0xc, #0x14
    // 0x7edb24: r16 = 2
    //     0x7edb24: movz            x16, #0x2
    // 0x7edb28: stp             x16, x1, [SP]
    // 0x7edb2c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7edb2c: movz            x17, #0x3037
    //     0x7edb30: movk            x17, #0x1, lsl #16
    //     0x7edb34: add             lr, x0, x17
    //     0x7edb38: ldr             lr, [x21, lr, lsl #3]
    //     0x7edb3c: blr             lr
    // 0x7edb40: mov             x3, x0
    // 0x7edb44: r2 = Null
    //     0x7edb44: mov             x2, NULL
    // 0x7edb48: r1 = Null
    //     0x7edb48: mov             x1, NULL
    // 0x7edb4c: stur            x3, [fp, #-0x20]
    // 0x7edb50: r4 = 60
    //     0x7edb50: movz            x4, #0x3c
    // 0x7edb54: branchIfSmi(r0, 0x7edb60)
    //     0x7edb54: tbz             w0, #0, #0x7edb60
    // 0x7edb58: r4 = LoadClassIdInstr(r0)
    //     0x7edb58: ldur            x4, [x0, #-1]
    //     0x7edb5c: ubfx            x4, x4, #0xc, #0x14
    // 0x7edb60: sub             x4, x4, #0x5e
    // 0x7edb64: cmp             x4, #1
    // 0x7edb68: b.ls            #0x7edb78
    // 0x7edb6c: r8 = String?
    //     0x7edb6c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7edb70: r3 = Null
    //     0x7edb70: ldr             x3, [PP, #0x360]  ; [pp+0x360] Null
    // 0x7edb74: r0 = String?()
    //     0x7edb74: bl              #0x600324  ; IsType_String?_Stub
    // 0x7edb78: ldur            x0, [fp, #-0x18]
    // 0x7edb7c: r1 = LoadClassIdInstr(r0)
    //     0x7edb7c: ldur            x1, [x0, #-1]
    //     0x7edb80: ubfx            x1, x1, #0xc, #0x14
    // 0x7edb84: r16 = 4
    //     0x7edb84: movz            x16, #0x4
    // 0x7edb88: stp             x16, x0, [SP]
    // 0x7edb8c: mov             x0, x1
    // 0x7edb90: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7edb90: movz            x17, #0x3037
    //     0x7edb94: movk            x17, #0x1, lsl #16
    //     0x7edb98: add             lr, x0, x17
    //     0x7edb9c: ldr             lr, [x21, lr, lsl #3]
    //     0x7edba0: blr             lr
    // 0x7edba4: stur            x0, [fp, #-0x18]
    // 0x7edba8: r0 = PlatformException()
    //     0x7edba8: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x7edbac: mov             x1, x0
    // 0x7edbb0: ldur            x0, [fp, #-0x10]
    // 0x7edbb4: StoreField: r1->field_7 = r0
    //     0x7edbb4: stur            w0, [x1, #7]
    // 0x7edbb8: ldur            x0, [fp, #-0x20]
    // 0x7edbbc: StoreField: r1->field_b = r0
    //     0x7edbbc: stur            w0, [x1, #0xb]
    // 0x7edbc0: ldur            x0, [fp, #-0x18]
    // 0x7edbc4: StoreField: r1->field_f = r0
    //     0x7edbc4: stur            w0, [x1, #0xf]
    // 0x7edbc8: mov             x0, x1
    // 0x7edbcc: r0 = Throw()
    //     0x7edbcc: bl              #0xec04b8  ; ThrowStub
    // 0x7edbd0: brk             #0
    // 0x7edbd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7edbd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7edbd8: b               #0x7ed960
    // 0x7edbdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7edbdc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getTemporaryPath(/* No info */) async {
    // ** addr: 0x8b6afc, size: 0x2b4
    // 0x8b6afc: EnterFrame
    //     0x8b6afc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6b00: mov             fp, SP
    // 0x8b6b04: AllocStack(0x30)
    //     0x8b6b04: sub             SP, SP, #0x30
    // 0x8b6b08: SetupParameters(PathProviderApi this /* r1 => r1, fp-0x10 */)
    //     0x8b6b08: stur            NULL, [fp, #-8]
    //     0x8b6b0c: stur            x1, [fp, #-0x10]
    // 0x8b6b10: CheckStackOverflow
    //     0x8b6b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6b14: cmp             SP, x16
    //     0x8b6b18: b.ls            #0x8b6da4
    // 0x8b6b1c: InitAsync() -> Future<String?>
    //     0x8b6b1c: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0x8b6b20: bl              #0x661298  ; InitAsyncStub
    // 0x8b6b24: r1 = Null
    //     0x8b6b24: mov             x1, NULL
    // 0x8b6b28: r2 = 4
    //     0x8b6b28: movz            x2, #0x4
    // 0x8b6b2c: r0 = AllocateArray()
    //     0x8b6b2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b6b30: r16 = "dev.flutter.pigeon.path_provider_android.PathProviderApi.getTemporaryPath"
    //     0x8b6b30: add             x16, PP, #0xd, lsl #12  ; [pp+0xdcb8] "dev.flutter.pigeon.path_provider_android.PathProviderApi.getTemporaryPath"
    //     0x8b6b34: ldr             x16, [x16, #0xcb8]
    // 0x8b6b38: StoreField: r0->field_f = r16
    //     0x8b6b38: stur            w16, [x0, #0xf]
    // 0x8b6b3c: ldur            x1, [fp, #-0x10]
    // 0x8b6b40: LoadField: r2 = r1->field_b
    //     0x8b6b40: ldur            w2, [x1, #0xb]
    // 0x8b6b44: DecompressPointer r2
    //     0x8b6b44: add             x2, x2, HEAP, lsl #32
    // 0x8b6b48: StoreField: r0->field_13 = r2
    //     0x8b6b48: stur            w2, [x0, #0x13]
    // 0x8b6b4c: str             x0, [SP]
    // 0x8b6b50: r0 = _interpolate()
    //     0x8b6b50: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8b6b54: r1 = <Object?>
    //     0x8b6b54: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8b6b58: stur            x0, [fp, #-0x10]
    // 0x8b6b5c: r0 = BasicMessageChannel()
    //     0x8b6b5c: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x8b6b60: mov             x1, x0
    // 0x8b6b64: ldur            x0, [fp, #-0x10]
    // 0x8b6b68: StoreField: r1->field_b = r0
    //     0x8b6b68: stur            w0, [x1, #0xb]
    // 0x8b6b6c: r2 = Instance__PigeonCodec
    //     0x8b6b6c: ldr             x2, [PP, #0x318]  ; [pp+0x318] Obj!_PigeonCodec@e259b1
    // 0x8b6b70: StoreField: r1->field_f = r2
    //     0x8b6b70: stur            w2, [x1, #0xf]
    // 0x8b6b74: r2 = Null
    //     0x8b6b74: mov             x2, NULL
    // 0x8b6b78: r0 = send()
    //     0x8b6b78: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0x8b6b7c: mov             x1, x0
    // 0x8b6b80: stur            x1, [fp, #-0x18]
    // 0x8b6b84: r0 = Await()
    //     0x8b6b84: bl              #0x661044  ; AwaitStub
    // 0x8b6b88: mov             x3, x0
    // 0x8b6b8c: r2 = Null
    //     0x8b6b8c: mov             x2, NULL
    // 0x8b6b90: r1 = Null
    //     0x8b6b90: mov             x1, NULL
    // 0x8b6b94: stur            x3, [fp, #-0x18]
    // 0x8b6b98: r4 = 60
    //     0x8b6b98: movz            x4, #0x3c
    // 0x8b6b9c: branchIfSmi(r0, 0x8b6ba8)
    //     0x8b6b9c: tbz             w0, #0, #0x8b6ba8
    // 0x8b6ba0: r4 = LoadClassIdInstr(r0)
    //     0x8b6ba0: ldur            x4, [x0, #-1]
    //     0x8b6ba4: ubfx            x4, x4, #0xc, #0x14
    // 0x8b6ba8: sub             x4, x4, #0x5a
    // 0x8b6bac: cmp             x4, #2
    // 0x8b6bb0: b.ls            #0x8b6bc4
    // 0x8b6bb4: r8 = List<Object?>?
    //     0x8b6bb4: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0x8b6bb8: r3 = Null
    //     0x8b6bb8: add             x3, PP, #0xd, lsl #12  ; [pp+0xdcc0] Null
    //     0x8b6bbc: ldr             x3, [x3, #0xcc0]
    // 0x8b6bc0: r0 = List<Object?>?()
    //     0x8b6bc0: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0x8b6bc4: ldur            x1, [fp, #-0x18]
    // 0x8b6bc8: cmp             w1, NULL
    // 0x8b6bcc: b.eq            #0x8b6c68
    // 0x8b6bd0: r0 = LoadClassIdInstr(r1)
    //     0x8b6bd0: ldur            x0, [x1, #-1]
    //     0x8b6bd4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b6bd8: str             x1, [SP]
    // 0x8b6bdc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8b6bdc: movz            x17, #0xc834
    //     0x8b6be0: add             lr, x0, x17
    //     0x8b6be4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b6be8: blr             lr
    // 0x8b6bec: r1 = LoadInt32Instr(r0)
    //     0x8b6bec: sbfx            x1, x0, #1, #0x1f
    //     0x8b6bf0: tbz             w0, #0, #0x8b6bf8
    //     0x8b6bf4: ldur            x1, [x0, #7]
    // 0x8b6bf8: cmp             x1, #1
    // 0x8b6bfc: b.gt            #0x8b6c78
    // 0x8b6c00: ldur            x1, [fp, #-0x18]
    // 0x8b6c04: r0 = LoadClassIdInstr(r1)
    //     0x8b6c04: ldur            x0, [x1, #-1]
    //     0x8b6c08: ubfx            x0, x0, #0xc, #0x14
    // 0x8b6c0c: stp             xzr, x1, [SP]
    // 0x8b6c10: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b6c10: movz            x17, #0x3037
    //     0x8b6c14: movk            x17, #0x1, lsl #16
    //     0x8b6c18: add             lr, x0, x17
    //     0x8b6c1c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b6c20: blr             lr
    // 0x8b6c24: mov             x3, x0
    // 0x8b6c28: r2 = Null
    //     0x8b6c28: mov             x2, NULL
    // 0x8b6c2c: r1 = Null
    //     0x8b6c2c: mov             x1, NULL
    // 0x8b6c30: stur            x3, [fp, #-0x20]
    // 0x8b6c34: r4 = 60
    //     0x8b6c34: movz            x4, #0x3c
    // 0x8b6c38: branchIfSmi(r0, 0x8b6c44)
    //     0x8b6c38: tbz             w0, #0, #0x8b6c44
    // 0x8b6c3c: r4 = LoadClassIdInstr(r0)
    //     0x8b6c3c: ldur            x4, [x0, #-1]
    //     0x8b6c40: ubfx            x4, x4, #0xc, #0x14
    // 0x8b6c44: sub             x4, x4, #0x5e
    // 0x8b6c48: cmp             x4, #1
    // 0x8b6c4c: b.ls            #0x8b6c60
    // 0x8b6c50: r8 = String?
    //     0x8b6c50: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8b6c54: r3 = Null
    //     0x8b6c54: add             x3, PP, #0xd, lsl #12  ; [pp+0xdcd0] Null
    //     0x8b6c58: ldr             x3, [x3, #0xcd0]
    // 0x8b6c5c: r0 = String?()
    //     0x8b6c5c: bl              #0x600324  ; IsType_String?_Stub
    // 0x8b6c60: ldur            x0, [fp, #-0x20]
    // 0x8b6c64: r0 = ReturnAsyncNotFuture()
    //     0x8b6c64: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b6c68: ldur            x1, [fp, #-0x10]
    // 0x8b6c6c: r0 = _createConnectionError()
    //     0x8b6c6c: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0x8b6c70: r0 = Throw()
    //     0x8b6c70: bl              #0xec04b8  ; ThrowStub
    // 0x8b6c74: brk             #0
    // 0x8b6c78: ldur            x1, [fp, #-0x18]
    // 0x8b6c7c: r0 = LoadClassIdInstr(r1)
    //     0x8b6c7c: ldur            x0, [x1, #-1]
    //     0x8b6c80: ubfx            x0, x0, #0xc, #0x14
    // 0x8b6c84: stp             xzr, x1, [SP]
    // 0x8b6c88: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b6c88: movz            x17, #0x3037
    //     0x8b6c8c: movk            x17, #0x1, lsl #16
    //     0x8b6c90: add             lr, x0, x17
    //     0x8b6c94: ldr             lr, [x21, lr, lsl #3]
    //     0x8b6c98: blr             lr
    // 0x8b6c9c: mov             x3, x0
    // 0x8b6ca0: stur            x3, [fp, #-0x10]
    // 0x8b6ca4: cmp             w3, NULL
    // 0x8b6ca8: b.eq            #0x8b6dac
    // 0x8b6cac: mov             x0, x3
    // 0x8b6cb0: r2 = Null
    //     0x8b6cb0: mov             x2, NULL
    // 0x8b6cb4: r1 = Null
    //     0x8b6cb4: mov             x1, NULL
    // 0x8b6cb8: r4 = 60
    //     0x8b6cb8: movz            x4, #0x3c
    // 0x8b6cbc: branchIfSmi(r0, 0x8b6cc8)
    //     0x8b6cbc: tbz             w0, #0, #0x8b6cc8
    // 0x8b6cc0: r4 = LoadClassIdInstr(r0)
    //     0x8b6cc0: ldur            x4, [x0, #-1]
    //     0x8b6cc4: ubfx            x4, x4, #0xc, #0x14
    // 0x8b6cc8: sub             x4, x4, #0x5e
    // 0x8b6ccc: cmp             x4, #1
    // 0x8b6cd0: b.ls            #0x8b6ce4
    // 0x8b6cd4: r8 = String
    //     0x8b6cd4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8b6cd8: r3 = Null
    //     0x8b6cd8: add             x3, PP, #0xd, lsl #12  ; [pp+0xdce0] Null
    //     0x8b6cdc: ldr             x3, [x3, #0xce0]
    // 0x8b6ce0: r0 = String()
    //     0x8b6ce0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8b6ce4: ldur            x1, [fp, #-0x18]
    // 0x8b6ce8: r0 = LoadClassIdInstr(r1)
    //     0x8b6ce8: ldur            x0, [x1, #-1]
    //     0x8b6cec: ubfx            x0, x0, #0xc, #0x14
    // 0x8b6cf0: r16 = 2
    //     0x8b6cf0: movz            x16, #0x2
    // 0x8b6cf4: stp             x16, x1, [SP]
    // 0x8b6cf8: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b6cf8: movz            x17, #0x3037
    //     0x8b6cfc: movk            x17, #0x1, lsl #16
    //     0x8b6d00: add             lr, x0, x17
    //     0x8b6d04: ldr             lr, [x21, lr, lsl #3]
    //     0x8b6d08: blr             lr
    // 0x8b6d0c: mov             x3, x0
    // 0x8b6d10: r2 = Null
    //     0x8b6d10: mov             x2, NULL
    // 0x8b6d14: r1 = Null
    //     0x8b6d14: mov             x1, NULL
    // 0x8b6d18: stur            x3, [fp, #-0x20]
    // 0x8b6d1c: r4 = 60
    //     0x8b6d1c: movz            x4, #0x3c
    // 0x8b6d20: branchIfSmi(r0, 0x8b6d2c)
    //     0x8b6d20: tbz             w0, #0, #0x8b6d2c
    // 0x8b6d24: r4 = LoadClassIdInstr(r0)
    //     0x8b6d24: ldur            x4, [x0, #-1]
    //     0x8b6d28: ubfx            x4, x4, #0xc, #0x14
    // 0x8b6d2c: sub             x4, x4, #0x5e
    // 0x8b6d30: cmp             x4, #1
    // 0x8b6d34: b.ls            #0x8b6d48
    // 0x8b6d38: r8 = String?
    //     0x8b6d38: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8b6d3c: r3 = Null
    //     0x8b6d3c: add             x3, PP, #0xd, lsl #12  ; [pp+0xdcf0] Null
    //     0x8b6d40: ldr             x3, [x3, #0xcf0]
    // 0x8b6d44: r0 = String?()
    //     0x8b6d44: bl              #0x600324  ; IsType_String?_Stub
    // 0x8b6d48: ldur            x0, [fp, #-0x18]
    // 0x8b6d4c: r1 = LoadClassIdInstr(r0)
    //     0x8b6d4c: ldur            x1, [x0, #-1]
    //     0x8b6d50: ubfx            x1, x1, #0xc, #0x14
    // 0x8b6d54: r16 = 4
    //     0x8b6d54: movz            x16, #0x4
    // 0x8b6d58: stp             x16, x0, [SP]
    // 0x8b6d5c: mov             x0, x1
    // 0x8b6d60: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b6d60: movz            x17, #0x3037
    //     0x8b6d64: movk            x17, #0x1, lsl #16
    //     0x8b6d68: add             lr, x0, x17
    //     0x8b6d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b6d70: blr             lr
    // 0x8b6d74: stur            x0, [fp, #-0x18]
    // 0x8b6d78: r0 = PlatformException()
    //     0x8b6d78: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x8b6d7c: mov             x1, x0
    // 0x8b6d80: ldur            x0, [fp, #-0x10]
    // 0x8b6d84: StoreField: r1->field_7 = r0
    //     0x8b6d84: stur            w0, [x1, #7]
    // 0x8b6d88: ldur            x0, [fp, #-0x20]
    // 0x8b6d8c: StoreField: r1->field_b = r0
    //     0x8b6d8c: stur            w0, [x1, #0xb]
    // 0x8b6d90: ldur            x0, [fp, #-0x18]
    // 0x8b6d94: StoreField: r1->field_f = r0
    //     0x8b6d94: stur            w0, [x1, #0xf]
    // 0x8b6d98: mov             x0, x1
    // 0x8b6d9c: r0 = Throw()
    //     0x8b6d9c: bl              #0xec04b8  ; ThrowStub
    // 0x8b6da0: brk             #0
    // 0x8b6da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6da4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6da8: b               #0x8b6b1c
    // 0x8b6dac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b6dac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getApplicationSupportPath(/* No info */) async {
    // ** addr: 0xab73e0, size: 0x2b4
    // 0xab73e0: EnterFrame
    //     0xab73e0: stp             fp, lr, [SP, #-0x10]!
    //     0xab73e4: mov             fp, SP
    // 0xab73e8: AllocStack(0x30)
    //     0xab73e8: sub             SP, SP, #0x30
    // 0xab73ec: SetupParameters(PathProviderApi this /* r1 => r1, fp-0x10 */)
    //     0xab73ec: stur            NULL, [fp, #-8]
    //     0xab73f0: stur            x1, [fp, #-0x10]
    // 0xab73f4: CheckStackOverflow
    //     0xab73f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab73f8: cmp             SP, x16
    //     0xab73fc: b.ls            #0xab7688
    // 0xab7400: InitAsync() -> Future<String?>
    //     0xab7400: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0xab7404: bl              #0x661298  ; InitAsyncStub
    // 0xab7408: r1 = Null
    //     0xab7408: mov             x1, NULL
    // 0xab740c: r2 = 4
    //     0xab740c: movz            x2, #0x4
    // 0xab7410: r0 = AllocateArray()
    //     0xab7410: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab7414: r16 = "dev.flutter.pigeon.path_provider_android.PathProviderApi.getApplicationSupportPath"
    //     0xab7414: add             x16, PP, #0x43, lsl #12  ; [pp+0x433f8] "dev.flutter.pigeon.path_provider_android.PathProviderApi.getApplicationSupportPath"
    //     0xab7418: ldr             x16, [x16, #0x3f8]
    // 0xab741c: StoreField: r0->field_f = r16
    //     0xab741c: stur            w16, [x0, #0xf]
    // 0xab7420: ldur            x1, [fp, #-0x10]
    // 0xab7424: LoadField: r2 = r1->field_b
    //     0xab7424: ldur            w2, [x1, #0xb]
    // 0xab7428: DecompressPointer r2
    //     0xab7428: add             x2, x2, HEAP, lsl #32
    // 0xab742c: StoreField: r0->field_13 = r2
    //     0xab742c: stur            w2, [x0, #0x13]
    // 0xab7430: str             x0, [SP]
    // 0xab7434: r0 = _interpolate()
    //     0xab7434: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab7438: r1 = <Object?>
    //     0xab7438: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xab743c: stur            x0, [fp, #-0x10]
    // 0xab7440: r0 = BasicMessageChannel()
    //     0xab7440: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xab7444: mov             x1, x0
    // 0xab7448: ldur            x0, [fp, #-0x10]
    // 0xab744c: StoreField: r1->field_b = r0
    //     0xab744c: stur            w0, [x1, #0xb]
    // 0xab7450: r2 = Instance__PigeonCodec
    //     0xab7450: ldr             x2, [PP, #0x318]  ; [pp+0x318] Obj!_PigeonCodec@e259b1
    // 0xab7454: StoreField: r1->field_f = r2
    //     0xab7454: stur            w2, [x1, #0xf]
    // 0xab7458: r2 = Null
    //     0xab7458: mov             x2, NULL
    // 0xab745c: r0 = send()
    //     0xab745c: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xab7460: mov             x1, x0
    // 0xab7464: stur            x1, [fp, #-0x18]
    // 0xab7468: r0 = Await()
    //     0xab7468: bl              #0x661044  ; AwaitStub
    // 0xab746c: mov             x3, x0
    // 0xab7470: r2 = Null
    //     0xab7470: mov             x2, NULL
    // 0xab7474: r1 = Null
    //     0xab7474: mov             x1, NULL
    // 0xab7478: stur            x3, [fp, #-0x18]
    // 0xab747c: r4 = 60
    //     0xab747c: movz            x4, #0x3c
    // 0xab7480: branchIfSmi(r0, 0xab748c)
    //     0xab7480: tbz             w0, #0, #0xab748c
    // 0xab7484: r4 = LoadClassIdInstr(r0)
    //     0xab7484: ldur            x4, [x0, #-1]
    //     0xab7488: ubfx            x4, x4, #0xc, #0x14
    // 0xab748c: sub             x4, x4, #0x5a
    // 0xab7490: cmp             x4, #2
    // 0xab7494: b.ls            #0xab74a8
    // 0xab7498: r8 = List<Object?>?
    //     0xab7498: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0xab749c: r3 = Null
    //     0xab749c: add             x3, PP, #0x43, lsl #12  ; [pp+0x43400] Null
    //     0xab74a0: ldr             x3, [x3, #0x400]
    // 0xab74a4: r0 = List<Object?>?()
    //     0xab74a4: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0xab74a8: ldur            x1, [fp, #-0x18]
    // 0xab74ac: cmp             w1, NULL
    // 0xab74b0: b.eq            #0xab754c
    // 0xab74b4: r0 = LoadClassIdInstr(r1)
    //     0xab74b4: ldur            x0, [x1, #-1]
    //     0xab74b8: ubfx            x0, x0, #0xc, #0x14
    // 0xab74bc: str             x1, [SP]
    // 0xab74c0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xab74c0: movz            x17, #0xc834
    //     0xab74c4: add             lr, x0, x17
    //     0xab74c8: ldr             lr, [x21, lr, lsl #3]
    //     0xab74cc: blr             lr
    // 0xab74d0: r1 = LoadInt32Instr(r0)
    //     0xab74d0: sbfx            x1, x0, #1, #0x1f
    //     0xab74d4: tbz             w0, #0, #0xab74dc
    //     0xab74d8: ldur            x1, [x0, #7]
    // 0xab74dc: cmp             x1, #1
    // 0xab74e0: b.gt            #0xab755c
    // 0xab74e4: ldur            x1, [fp, #-0x18]
    // 0xab74e8: r0 = LoadClassIdInstr(r1)
    //     0xab74e8: ldur            x0, [x1, #-1]
    //     0xab74ec: ubfx            x0, x0, #0xc, #0x14
    // 0xab74f0: stp             xzr, x1, [SP]
    // 0xab74f4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xab74f4: movz            x17, #0x3037
    //     0xab74f8: movk            x17, #0x1, lsl #16
    //     0xab74fc: add             lr, x0, x17
    //     0xab7500: ldr             lr, [x21, lr, lsl #3]
    //     0xab7504: blr             lr
    // 0xab7508: mov             x3, x0
    // 0xab750c: r2 = Null
    //     0xab750c: mov             x2, NULL
    // 0xab7510: r1 = Null
    //     0xab7510: mov             x1, NULL
    // 0xab7514: stur            x3, [fp, #-0x20]
    // 0xab7518: r4 = 60
    //     0xab7518: movz            x4, #0x3c
    // 0xab751c: branchIfSmi(r0, 0xab7528)
    //     0xab751c: tbz             w0, #0, #0xab7528
    // 0xab7520: r4 = LoadClassIdInstr(r0)
    //     0xab7520: ldur            x4, [x0, #-1]
    //     0xab7524: ubfx            x4, x4, #0xc, #0x14
    // 0xab7528: sub             x4, x4, #0x5e
    // 0xab752c: cmp             x4, #1
    // 0xab7530: b.ls            #0xab7544
    // 0xab7534: r8 = String?
    //     0xab7534: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xab7538: r3 = Null
    //     0xab7538: add             x3, PP, #0x43, lsl #12  ; [pp+0x43410] Null
    //     0xab753c: ldr             x3, [x3, #0x410]
    // 0xab7540: r0 = String?()
    //     0xab7540: bl              #0x600324  ; IsType_String?_Stub
    // 0xab7544: ldur            x0, [fp, #-0x20]
    // 0xab7548: r0 = ReturnAsyncNotFuture()
    //     0xab7548: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab754c: ldur            x1, [fp, #-0x10]
    // 0xab7550: r0 = _createConnectionError()
    //     0xab7550: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0xab7554: r0 = Throw()
    //     0xab7554: bl              #0xec04b8  ; ThrowStub
    // 0xab7558: brk             #0
    // 0xab755c: ldur            x1, [fp, #-0x18]
    // 0xab7560: r0 = LoadClassIdInstr(r1)
    //     0xab7560: ldur            x0, [x1, #-1]
    //     0xab7564: ubfx            x0, x0, #0xc, #0x14
    // 0xab7568: stp             xzr, x1, [SP]
    // 0xab756c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xab756c: movz            x17, #0x3037
    //     0xab7570: movk            x17, #0x1, lsl #16
    //     0xab7574: add             lr, x0, x17
    //     0xab7578: ldr             lr, [x21, lr, lsl #3]
    //     0xab757c: blr             lr
    // 0xab7580: mov             x3, x0
    // 0xab7584: stur            x3, [fp, #-0x10]
    // 0xab7588: cmp             w3, NULL
    // 0xab758c: b.eq            #0xab7690
    // 0xab7590: mov             x0, x3
    // 0xab7594: r2 = Null
    //     0xab7594: mov             x2, NULL
    // 0xab7598: r1 = Null
    //     0xab7598: mov             x1, NULL
    // 0xab759c: r4 = 60
    //     0xab759c: movz            x4, #0x3c
    // 0xab75a0: branchIfSmi(r0, 0xab75ac)
    //     0xab75a0: tbz             w0, #0, #0xab75ac
    // 0xab75a4: r4 = LoadClassIdInstr(r0)
    //     0xab75a4: ldur            x4, [x0, #-1]
    //     0xab75a8: ubfx            x4, x4, #0xc, #0x14
    // 0xab75ac: sub             x4, x4, #0x5e
    // 0xab75b0: cmp             x4, #1
    // 0xab75b4: b.ls            #0xab75c8
    // 0xab75b8: r8 = String
    //     0xab75b8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xab75bc: r3 = Null
    //     0xab75bc: add             x3, PP, #0x43, lsl #12  ; [pp+0x43420] Null
    //     0xab75c0: ldr             x3, [x3, #0x420]
    // 0xab75c4: r0 = String()
    //     0xab75c4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xab75c8: ldur            x1, [fp, #-0x18]
    // 0xab75cc: r0 = LoadClassIdInstr(r1)
    //     0xab75cc: ldur            x0, [x1, #-1]
    //     0xab75d0: ubfx            x0, x0, #0xc, #0x14
    // 0xab75d4: r16 = 2
    //     0xab75d4: movz            x16, #0x2
    // 0xab75d8: stp             x16, x1, [SP]
    // 0xab75dc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xab75dc: movz            x17, #0x3037
    //     0xab75e0: movk            x17, #0x1, lsl #16
    //     0xab75e4: add             lr, x0, x17
    //     0xab75e8: ldr             lr, [x21, lr, lsl #3]
    //     0xab75ec: blr             lr
    // 0xab75f0: mov             x3, x0
    // 0xab75f4: r2 = Null
    //     0xab75f4: mov             x2, NULL
    // 0xab75f8: r1 = Null
    //     0xab75f8: mov             x1, NULL
    // 0xab75fc: stur            x3, [fp, #-0x20]
    // 0xab7600: r4 = 60
    //     0xab7600: movz            x4, #0x3c
    // 0xab7604: branchIfSmi(r0, 0xab7610)
    //     0xab7604: tbz             w0, #0, #0xab7610
    // 0xab7608: r4 = LoadClassIdInstr(r0)
    //     0xab7608: ldur            x4, [x0, #-1]
    //     0xab760c: ubfx            x4, x4, #0xc, #0x14
    // 0xab7610: sub             x4, x4, #0x5e
    // 0xab7614: cmp             x4, #1
    // 0xab7618: b.ls            #0xab762c
    // 0xab761c: r8 = String?
    //     0xab761c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xab7620: r3 = Null
    //     0xab7620: add             x3, PP, #0x43, lsl #12  ; [pp+0x43430] Null
    //     0xab7624: ldr             x3, [x3, #0x430]
    // 0xab7628: r0 = String?()
    //     0xab7628: bl              #0x600324  ; IsType_String?_Stub
    // 0xab762c: ldur            x0, [fp, #-0x18]
    // 0xab7630: r1 = LoadClassIdInstr(r0)
    //     0xab7630: ldur            x1, [x0, #-1]
    //     0xab7634: ubfx            x1, x1, #0xc, #0x14
    // 0xab7638: r16 = 4
    //     0xab7638: movz            x16, #0x4
    // 0xab763c: stp             x16, x0, [SP]
    // 0xab7640: mov             x0, x1
    // 0xab7644: r0 = GDT[cid_x0 + 0x13037]()
    //     0xab7644: movz            x17, #0x3037
    //     0xab7648: movk            x17, #0x1, lsl #16
    //     0xab764c: add             lr, x0, x17
    //     0xab7650: ldr             lr, [x21, lr, lsl #3]
    //     0xab7654: blr             lr
    // 0xab7658: stur            x0, [fp, #-0x18]
    // 0xab765c: r0 = PlatformException()
    //     0xab765c: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xab7660: mov             x1, x0
    // 0xab7664: ldur            x0, [fp, #-0x10]
    // 0xab7668: StoreField: r1->field_7 = r0
    //     0xab7668: stur            w0, [x1, #7]
    // 0xab766c: ldur            x0, [fp, #-0x20]
    // 0xab7670: StoreField: r1->field_b = r0
    //     0xab7670: stur            w0, [x1, #0xb]
    // 0xab7674: ldur            x0, [fp, #-0x18]
    // 0xab7678: StoreField: r1->field_f = r0
    //     0xab7678: stur            w0, [x1, #0xf]
    // 0xab767c: mov             x0, x1
    // 0xab7680: r0 = Throw()
    //     0xab7680: bl              #0xec04b8  ; ThrowStub
    // 0xab7684: brk             #0
    // 0xab7688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab7688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab768c: b               #0xab7400
    // 0xab7690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7690: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5528, size: 0x8, field offset: 0x8
//   const constructor, 
class _PigeonCodec extends StandardMessageCodec {

  _ readValueOfType(/* No info */) {
    // ** addr: 0xce7284, size: 0xdc
    // 0xce7284: EnterFrame
    //     0xce7284: stp             fp, lr, [SP, #-0x10]!
    //     0xce7288: mov             fp, SP
    // 0xce728c: AllocStack(0x8)
    //     0xce728c: sub             SP, SP, #8
    // 0xce7290: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */)
    //     0xce7290: mov             x0, x2
    //     0xce7294: mov             x2, x3
    // 0xce7298: CheckStackOverflow
    //     0xce7298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce729c: cmp             SP, x16
    //     0xce72a0: b.ls            #0xce7354
    // 0xce72a4: lsl             x3, x0, #1
    // 0xce72a8: cmp             w3, #0x102
    // 0xce72ac: b.ne            #0xce733c
    // 0xce72b0: r0 = readValue()
    //     0xce72b0: bl              #0xce719c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xce72b4: mov             x3, x0
    // 0xce72b8: r2 = Null
    //     0xce72b8: mov             x2, NULL
    // 0xce72bc: r1 = Null
    //     0xce72bc: mov             x1, NULL
    // 0xce72c0: stur            x3, [fp, #-8]
    // 0xce72c4: branchIfSmi(r0, 0xce72ec)
    //     0xce72c4: tbz             w0, #0, #0xce72ec
    // 0xce72c8: r4 = LoadClassIdInstr(r0)
    //     0xce72c8: ldur            x4, [x0, #-1]
    //     0xce72cc: ubfx            x4, x4, #0xc, #0x14
    // 0xce72d0: sub             x4, x4, #0x3c
    // 0xce72d4: cmp             x4, #1
    // 0xce72d8: b.ls            #0xce72ec
    // 0xce72dc: r8 = int?
    //     0xce72dc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xce72e0: r3 = Null
    //     0xce72e0: add             x3, PP, #0x12, lsl #12  ; [pp+0x127a8] Null
    //     0xce72e4: ldr             x3, [x3, #0x7a8]
    // 0xce72e8: r0 = int?()
    //     0xce72e8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xce72ec: ldur            x0, [fp, #-8]
    // 0xce72f0: cmp             w0, NULL
    // 0xce72f4: b.ne            #0xce7300
    // 0xce72f8: r0 = Null
    //     0xce72f8: mov             x0, NULL
    // 0xce72fc: b               #0xce7330
    // 0xce7300: r2 = const [Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory', Instance of 'StorageDirectory']
    //     0xce7300: add             x2, PP, #0x12, lsl #12  ; [pp+0x127b8] List<StorageDirectory>(11)
    //     0xce7304: ldr             x2, [x2, #0x7b8]
    // 0xce7308: r3 = LoadInt32Instr(r0)
    //     0xce7308: sbfx            x3, x0, #1, #0x1f
    //     0xce730c: tbz             w0, #0, #0xce7314
    //     0xce7310: ldur            x3, [x0, #7]
    // 0xce7314: mov             x1, x3
    // 0xce7318: r0 = 11
    //     0xce7318: movz            x0, #0xb
    // 0xce731c: cmp             x1, x0
    // 0xce7320: b.hs            #0xce735c
    // 0xce7324: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xce7324: add             x16, x2, x3, lsl #2
    //     0xce7328: ldur            w0, [x16, #0xf]
    // 0xce732c: DecompressPointer r0
    //     0xce732c: add             x0, x0, HEAP, lsl #32
    // 0xce7330: LeaveFrame
    //     0xce7330: mov             SP, fp
    //     0xce7334: ldp             fp, lr, [SP], #0x10
    // 0xce7338: ret
    //     0xce7338: ret             
    // 0xce733c: mov             x3, x2
    // 0xce7340: mov             x2, x0
    // 0xce7344: r0 = readValueOfType()
    //     0xce7344: bl              #0xce7e48  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValueOfType
    // 0xce7348: LeaveFrame
    //     0xce7348: mov             SP, fp
    //     0xce734c: ldp             fp, lr, [SP], #0x10
    // 0xce7350: ret
    //     0xce7350: ret             
    // 0xce7354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce7354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce7358: b               #0xce72a4
    // 0xce735c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xce735c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ writeValue(/* No info */) {
    // ** addr: 0xd49b20, size: 0xdc
    // 0xd49b20: EnterFrame
    //     0xd49b20: stp             fp, lr, [SP, #-0x10]!
    //     0xd49b24: mov             fp, SP
    // 0xd49b28: AllocStack(0x18)
    //     0xd49b28: sub             SP, SP, #0x18
    // 0xd49b2c: SetupParameters(_PigeonCodec this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xd49b2c: mov             x4, x1
    //     0xd49b30: mov             x0, x2
    //     0xd49b34: stur            x2, [fp, #-8]
    //     0xd49b38: stur            x3, [fp, #-0x10]
    //     0xd49b3c: stur            x1, [fp, #-0x18]
    // 0xd49b40: CheckStackOverflow
    //     0xd49b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd49b44: cmp             SP, x16
    //     0xd49b48: b.ls            #0xd49bf4
    // 0xd49b4c: r1 = 60
    //     0xd49b4c: movz            x1, #0x3c
    // 0xd49b50: branchIfSmi(r3, 0xd49b5c)
    //     0xd49b50: tbz             w3, #0, #0xd49b5c
    // 0xd49b54: r1 = LoadClassIdInstr(r3)
    //     0xd49b54: ldur            x1, [x3, #-1]
    //     0xd49b58: ubfx            x1, x1, #0xc, #0x14
    // 0xd49b5c: sub             x16, x1, #0x3c
    // 0xd49b60: cmp             x16, #1
    // 0xd49b64: b.hi            #0xd49b90
    // 0xd49b68: mov             x1, x0
    // 0xd49b6c: r2 = 4
    //     0xd49b6c: movz            x2, #0x4
    // 0xd49b70: r0 = _add()
    //     0xd49b70: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd49b74: ldur            x3, [fp, #-0x10]
    // 0xd49b78: r2 = LoadInt32Instr(r3)
    //     0xd49b78: sbfx            x2, x3, #1, #0x1f
    //     0xd49b7c: tbz             w3, #0, #0xd49b84
    //     0xd49b80: ldur            x2, [x3, #7]
    // 0xd49b84: ldur            x1, [fp, #-8]
    // 0xd49b88: r0 = putInt64()
    //     0xd49b88: bl              #0xd49bfc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::putInt64
    // 0xd49b8c: b               #0xd49be4
    // 0xd49b90: r17 = 6822
    //     0xd49b90: movz            x17, #0x1aa6
    // 0xd49b94: cmp             x1, x17
    // 0xd49b98: b.ne            #0xd49bd8
    // 0xd49b9c: ldur            x1, [fp, #-8]
    // 0xd49ba0: r2 = 129
    //     0xd49ba0: movz            x2, #0x81
    // 0xd49ba4: r0 = _add()
    //     0xd49ba4: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd49ba8: ldur            x3, [fp, #-0x10]
    // 0xd49bac: LoadField: r2 = r3->field_7
    //     0xd49bac: ldur            x2, [x3, #7]
    // 0xd49bb0: r0 = BoxInt64Instr(r2)
    //     0xd49bb0: sbfiz           x0, x2, #1, #0x1f
    //     0xd49bb4: cmp             x2, x0, asr #1
    //     0xd49bb8: b.eq            #0xd49bc4
    //     0xd49bbc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd49bc0: stur            x2, [x0, #7]
    // 0xd49bc4: ldur            x1, [fp, #-0x18]
    // 0xd49bc8: ldur            x2, [fp, #-8]
    // 0xd49bcc: mov             x3, x0
    // 0xd49bd0: r0 = writeValue()
    //     0xd49bd0: bl              #0xd49b20  ; [package:path_provider_android/messages.g.dart] _PigeonCodec::writeValue
    // 0xd49bd4: b               #0xd49be4
    // 0xd49bd8: ldur            x1, [fp, #-0x18]
    // 0xd49bdc: ldur            x2, [fp, #-8]
    // 0xd49be0: r0 = writeValue()
    //     0xd49be0: bl              #0xd4a458  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::writeValue
    // 0xd49be4: r0 = Null
    //     0xd49be4: mov             x0, NULL
    // 0xd49be8: LeaveFrame
    //     0xd49be8: mov             SP, fp
    //     0xd49bec: ldp             fp, lr, [SP], #0x10
    // 0xd49bf0: ret
    //     0xd49bf0: ret             
    // 0xd49bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd49bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd49bf8: b               #0xd49b4c
  }
}

// class id: 6822, size: 0x14, field offset: 0x14
enum StorageDirectory extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d758, size: 0x64
    // 0xc4d758: EnterFrame
    //     0xc4d758: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d75c: mov             fp, SP
    // 0xc4d760: AllocStack(0x10)
    //     0xc4d760: sub             SP, SP, #0x10
    // 0xc4d764: SetupParameters(StorageDirectory this /* r1 => r0, fp-0x8 */)
    //     0xc4d764: mov             x0, x1
    //     0xc4d768: stur            x1, [fp, #-8]
    // 0xc4d76c: CheckStackOverflow
    //     0xc4d76c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d770: cmp             SP, x16
    //     0xc4d774: b.ls            #0xc4d7b4
    // 0xc4d778: r1 = Null
    //     0xc4d778: mov             x1, NULL
    // 0xc4d77c: r2 = 4
    //     0xc4d77c: movz            x2, #0x4
    // 0xc4d780: r0 = AllocateArray()
    //     0xc4d780: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d784: r16 = "StorageDirectory."
    //     0xc4d784: add             x16, PP, #0x20, lsl #12  ; [pp+0x20a88] "StorageDirectory."
    //     0xc4d788: ldr             x16, [x16, #0xa88]
    // 0xc4d78c: StoreField: r0->field_f = r16
    //     0xc4d78c: stur            w16, [x0, #0xf]
    // 0xc4d790: ldur            x1, [fp, #-8]
    // 0xc4d794: LoadField: r2 = r1->field_f
    //     0xc4d794: ldur            w2, [x1, #0xf]
    // 0xc4d798: DecompressPointer r2
    //     0xc4d798: add             x2, x2, HEAP, lsl #32
    // 0xc4d79c: StoreField: r0->field_13 = r2
    //     0xc4d79c: stur            w2, [x0, #0x13]
    // 0xc4d7a0: str             x0, [SP]
    // 0xc4d7a4: r0 = _interpolate()
    //     0xc4d7a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d7a8: LeaveFrame
    //     0xc4d7a8: mov             SP, fp
    //     0xc4d7ac: ldp             fp, lr, [SP], #0x10
    // 0xc4d7b0: ret
    //     0xc4d7b0: ret             
    // 0xc4d7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d7b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d7b8: b               #0xc4d778
  }
}
