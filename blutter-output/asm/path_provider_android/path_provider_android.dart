// lib: , url: package:path_provider_android/path_provider_android.dart

// class id: 1050768, size: 0x8
class :: {
}

// class id: 5875, size: 0xc, field offset: 0x8
class PathProviderAndroid extends PathProviderPlatform {

  static void registerWith() {
    // ** addr: 0x833a80, size: 0xac
    // 0x833a80: EnterFrame
    //     0x833a80: stp             fp, lr, [SP, #-0x10]!
    //     0x833a84: mov             fp, SP
    // 0x833a88: AllocStack(0x10)
    //     0x833a88: sub             SP, SP, #0x10
    // 0x833a8c: CheckStackOverflow
    //     0x833a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833a90: cmp             SP, x16
    //     0x833a94: b.ls            #0x833b24
    // 0x833a98: r0 = PathProviderApi()
    //     0x833a98: bl              #0x833cc4  ; AllocatePathProviderApiStub -> PathProviderApi (size=0x10)
    // 0x833a9c: mov             x1, x0
    // 0x833aa0: r0 = ""
    //     0x833aa0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x833aa4: stur            x1, [fp, #-8]
    // 0x833aa8: StoreField: r1->field_b = r0
    //     0x833aa8: stur            w0, [x1, #0xb]
    // 0x833aac: r0 = PathProviderAndroid()
    //     0x833aac: bl              #0x833cb8  ; AllocatePathProviderAndroidStub -> PathProviderAndroid (size=0xc)
    // 0x833ab0: mov             x1, x0
    // 0x833ab4: ldur            x0, [fp, #-8]
    // 0x833ab8: stur            x1, [fp, #-0x10]
    // 0x833abc: StoreField: r1->field_7 = r0
    //     0x833abc: stur            w0, [x1, #7]
    // 0x833ac0: r0 = InitLateStaticField(0x608) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_token
    //     0x833ac0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833ac4: ldr             x0, [x0, #0xc10]
    //     0x833ac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833acc: cmp             w0, w16
    //     0x833ad0: b.ne            #0x833adc
    //     0x833ad4: ldr             x2, [PP, #0x11d0]  ; [pp+0x11d0] Field <PathProviderPlatform._token@678436587>: static late final (offset: 0x608)
    //     0x833ad8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833adc: stur            x0, [fp, #-8]
    // 0x833ae0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x833ae0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833ae4: ldr             x0, [x0, #0xc08]
    //     0x833ae8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833aec: cmp             w0, w16
    //     0x833af0: b.ne            #0x833afc
    //     0x833af4: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x833af8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833afc: mov             x1, x0
    // 0x833b00: ldur            x2, [fp, #-0x10]
    // 0x833b04: ldur            x3, [fp, #-8]
    // 0x833b08: r0 = []=()
    //     0x833b08: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x833b0c: ldur            x1, [fp, #-0x10]
    // 0x833b10: r0 = instance=()
    //     0x833b10: bl              #0x833b2c  ; [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::instance=
    // 0x833b14: r0 = Null
    //     0x833b14: mov             x0, NULL
    // 0x833b18: LeaveFrame
    //     0x833b18: mov             SP, fp
    //     0x833b1c: ldp             fp, lr, [SP], #0x10
    // 0x833b20: ret
    //     0x833b20: ret             
    // 0x833b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833b24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833b28: b               #0x833a98
  }
}
