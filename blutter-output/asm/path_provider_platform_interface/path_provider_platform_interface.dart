// lib: , url: package:path_provider_platform_interface/path_provider_platform_interface.dart

// class id: 1050769, size: 0x8
class :: {
}

// class id: 5873, size: 0x8, field offset: 0x8
abstract class PathProviderPlatform extends PlatformInterface {

  static late PathProviderPlatform _instance; // offset: 0x60c
  static late final Object _token; // offset: 0x608

  static PathProviderPlatform _instance() {
    // ** addr: 0x7edc70, size: 0x90
    // 0x7edc70: EnterFrame
    //     0x7edc70: stp             fp, lr, [SP, #-0x10]!
    //     0x7edc74: mov             fp, SP
    // 0x7edc78: AllocStack(0x10)
    //     0x7edc78: sub             SP, SP, #0x10
    // 0x7edc7c: CheckStackOverflow
    //     0x7edc7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7edc80: cmp             SP, x16
    //     0x7edc84: b.ls            #0x7edcf8
    // 0x7edc88: r0 = MethodChannelPathProvider()
    //     0x7edc88: bl              #0x7edd00  ; AllocateMethodChannelPathProviderStub -> MethodChannelPathProvider (size=0xc)
    // 0x7edc8c: mov             x1, x0
    // 0x7edc90: r0 = Instance_MethodChannel
    //     0x7edc90: ldr             x0, [PP, #0x2f0]  ; [pp+0x2f0] Obj!MethodChannel@e11071
    // 0x7edc94: stur            x1, [fp, #-8]
    // 0x7edc98: StoreField: r1->field_7 = r0
    //     0x7edc98: stur            w0, [x1, #7]
    // 0x7edc9c: r0 = InitLateStaticField(0x608) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_token
    //     0x7edc9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7edca0: ldr             x0, [x0, #0xc10]
    //     0x7edca4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7edca8: cmp             w0, w16
    //     0x7edcac: b.ne            #0x7edcb8
    //     0x7edcb0: ldr             x2, [PP, #0x11d0]  ; [pp+0x11d0] Field <PathProviderPlatform._token@678436587>: static late final (offset: 0x608)
    //     0x7edcb4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7edcb8: stur            x0, [fp, #-0x10]
    // 0x7edcbc: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x7edcbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7edcc0: ldr             x0, [x0, #0xc08]
    //     0x7edcc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7edcc8: cmp             w0, w16
    //     0x7edccc: b.ne            #0x7edcd8
    //     0x7edcd0: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x7edcd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7edcd8: mov             x1, x0
    // 0x7edcdc: ldur            x2, [fp, #-8]
    // 0x7edce0: ldur            x3, [fp, #-0x10]
    // 0x7edce4: r0 = []=()
    //     0x7edce4: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x7edce8: ldur            x0, [fp, #-8]
    // 0x7edcec: LeaveFrame
    //     0x7edcec: mov             SP, fp
    //     0x7edcf0: ldp             fp, lr, [SP], #0x10
    // 0x7edcf4: ret
    //     0x7edcf4: ret             
    // 0x7edcf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7edcf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7edcfc: b               #0x7edc88
  }
  set _ instance=(/* No info */) {
    // ** addr: 0x833b2c, size: 0x68
    // 0x833b2c: EnterFrame
    //     0x833b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x833b30: mov             fp, SP
    // 0x833b34: AllocStack(0x8)
    //     0x833b34: sub             SP, SP, #8
    // 0x833b38: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x833b38: stur            x1, [fp, #-8]
    // 0x833b3c: CheckStackOverflow
    //     0x833b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833b40: cmp             SP, x16
    //     0x833b44: b.ls            #0x833b8c
    // 0x833b48: r0 = InitLateStaticField(0x608) // [package:path_provider_platform_interface/path_provider_platform_interface.dart] PathProviderPlatform::_token
    //     0x833b48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833b4c: ldr             x0, [x0, #0xc10]
    //     0x833b50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833b54: cmp             w0, w16
    //     0x833b58: b.ne            #0x833b64
    //     0x833b5c: ldr             x2, [PP, #0x11d0]  ; [pp+0x11d0] Field <PathProviderPlatform._token@678436587>: static late final (offset: 0x608)
    //     0x833b60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833b64: ldur            x1, [fp, #-8]
    // 0x833b68: mov             x2, x0
    // 0x833b6c: r0 = verify()
    //     0x833b6c: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0x833b70: ldur            x1, [fp, #-8]
    // 0x833b74: StoreStaticField(0x60c, r1)
    //     0x833b74: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x833b78: str             x1, [x2, #0xc18]
    // 0x833b7c: r0 = Null
    //     0x833b7c: mov             x0, NULL
    // 0x833b80: LeaveFrame
    //     0x833b80: mov             SP, fp
    //     0x833b84: ldp             fp, lr, [SP], #0x10
    // 0x833b88: ret
    //     0x833b88: ret             
    // 0x833b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833b90: b               #0x833b48
  }
}
