// lib: , url: package:pdf/src/pdf/color.dart

// class id: 1050771, size: 0x8
class :: {
}

// class id: 924, size: 0x28, field offset: 0x8
//   const constructor, 
class PdfColor extends Object {

  _Double field_8;
  _Double field_10;
  _Double field_18;
  _Double field_20;

  int toInt(PdfColor) {
    // ** addr: 0xb10490, size: 0x60
    // 0xb10490: EnterFrame
    //     0xb10490: stp             fp, lr, [SP, #-0x10]!
    //     0xb10494: mov             fp, SP
    // 0xb10498: CheckStackOverflow
    //     0xb10498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1049c: cmp             SP, x16
    //     0xb104a0: b.ls            #0xb104d0
    // 0xb104a4: ldr             x1, [fp, #0x10]
    // 0xb104a8: r0 = toInt()
    //     0xb104a8: bl              #0xb104d8  ; [package:pdf/src/pdf/color.dart] PdfColor::toInt
    // 0xb104ac: mov             x2, x0
    // 0xb104b0: r0 = BoxInt64Instr(r2)
    //     0xb104b0: sbfiz           x0, x2, #1, #0x1f
    //     0xb104b4: cmp             x2, x0, asr #1
    //     0xb104b8: b.eq            #0xb104c4
    //     0xb104bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb104c0: stur            x2, [x0, #7]
    // 0xb104c4: LeaveFrame
    //     0xb104c4: mov             SP, fp
    //     0xb104c8: ldp             fp, lr, [SP], #0x10
    // 0xb104cc: ret
    //     0xb104cc: ret             
    // 0xb104d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb104d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb104d4: b               #0xb104a4
  }
  int toInt(PdfColor) {
    // ** addr: 0xb104d8, size: 0x2b4
    // 0xb104d8: EnterFrame
    //     0xb104d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb104dc: mov             fp, SP
    // 0xb104e0: AllocStack(0x18)
    //     0xb104e0: sub             SP, SP, #0x18
    // 0xb104e4: d1 = 255.000000
    //     0xb104e4: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb104e8: stur            x1, [fp, #-8]
    // 0xb104ec: LoadField: d0 = r1->field_7
    //     0xb104ec: ldur            d0, [x1, #7]
    // 0xb104f0: fmul            d2, d0, d1
    // 0xb104f4: mov             v0.16b, v2.16b
    // 0xb104f8: stp             fp, lr, [SP, #-0x10]!
    // 0xb104fc: mov             fp, SP
    // 0xb10500: CallRuntime_LibcRound(double) -> double
    //     0xb10500: and             SP, SP, #0xfffffffffffffff0
    //     0xb10504: mov             sp, SP
    //     0xb10508: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb1050c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb10510: blr             x16
    //     0xb10514: movz            x16, #0x8
    //     0xb10518: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb1051c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb10520: sub             sp, x16, #1, lsl #12
    //     0xb10524: mov             SP, fp
    //     0xb10528: ldp             fp, lr, [SP], #0x10
    // 0xb1052c: fcmp            d0, d0
    // 0xb10530: b.vs            #0xb10718
    // 0xb10534: fcvtzs          x0, d0
    // 0xb10538: asr             x16, x0, #0x1e
    // 0xb1053c: cmp             x16, x0, asr #63
    // 0xb10540: b.ne            #0xb10718
    // 0xb10544: lsl             x0, x0, #1
    // 0xb10548: r1 = LoadInt32Instr(r0)
    //     0xb10548: sbfx            x1, x0, #1, #0x1f
    //     0xb1054c: tbz             w0, #0, #0xb10554
    //     0xb10550: ldur            x1, [x0, #7]
    // 0xb10554: r0 = 255
    //     0xb10554: movz            x0, #0xff
    // 0xb10558: and             x2, x1, x0
    // 0xb1055c: ubfx            x2, x2, #0, #0x20
    // 0xb10560: lsl             x1, x2, #0x18
    // 0xb10564: ldur            x2, [fp, #-8]
    // 0xb10568: stur            x1, [fp, #-0x10]
    // 0xb1056c: LoadField: d0 = r2->field_f
    //     0xb1056c: ldur            d0, [x2, #0xf]
    // 0xb10570: d1 = 255.000000
    //     0xb10570: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb10574: fmul            d2, d0, d1
    // 0xb10578: mov             v0.16b, v2.16b
    // 0xb1057c: stp             fp, lr, [SP, #-0x10]!
    // 0xb10580: mov             fp, SP
    // 0xb10584: CallRuntime_LibcRound(double) -> double
    //     0xb10584: and             SP, SP, #0xfffffffffffffff0
    //     0xb10588: mov             sp, SP
    //     0xb1058c: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb10590: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb10594: blr             x16
    //     0xb10598: movz            x16, #0x8
    //     0xb1059c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb105a0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb105a4: sub             sp, x16, #1, lsl #12
    //     0xb105a8: mov             SP, fp
    //     0xb105ac: ldp             fp, lr, [SP], #0x10
    // 0xb105b0: fcmp            d0, d0
    // 0xb105b4: b.vs            #0xb10734
    // 0xb105b8: fcvtzs          x0, d0
    // 0xb105bc: asr             x16, x0, #0x1e
    // 0xb105c0: cmp             x16, x0, asr #63
    // 0xb105c4: b.ne            #0xb10734
    // 0xb105c8: lsl             x0, x0, #1
    // 0xb105cc: r1 = LoadInt32Instr(r0)
    //     0xb105cc: sbfx            x1, x0, #1, #0x1f
    //     0xb105d0: tbz             w0, #0, #0xb105d8
    //     0xb105d4: ldur            x1, [x0, #7]
    // 0xb105d8: r0 = 255
    //     0xb105d8: movz            x0, #0xff
    // 0xb105dc: and             x2, x1, x0
    // 0xb105e0: ubfx            x2, x2, #0, #0x20
    // 0xb105e4: lsl             x1, x2, #0x10
    // 0xb105e8: ldur            x2, [fp, #-0x10]
    // 0xb105ec: orr             x3, x2, x1
    // 0xb105f0: ldur            x1, [fp, #-8]
    // 0xb105f4: stur            x3, [fp, #-0x18]
    // 0xb105f8: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xb105f8: ldur            d0, [x1, #0x17]
    // 0xb105fc: d1 = 255.000000
    //     0xb105fc: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb10600: fmul            d2, d0, d1
    // 0xb10604: mov             v0.16b, v2.16b
    // 0xb10608: stp             fp, lr, [SP, #-0x10]!
    // 0xb1060c: mov             fp, SP
    // 0xb10610: CallRuntime_LibcRound(double) -> double
    //     0xb10610: and             SP, SP, #0xfffffffffffffff0
    //     0xb10614: mov             sp, SP
    //     0xb10618: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb1061c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb10620: blr             x16
    //     0xb10624: movz            x16, #0x8
    //     0xb10628: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb1062c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb10630: sub             sp, x16, #1, lsl #12
    //     0xb10634: mov             SP, fp
    //     0xb10638: ldp             fp, lr, [SP], #0x10
    // 0xb1063c: fcmp            d0, d0
    // 0xb10640: b.vs            #0xb10750
    // 0xb10644: fcvtzs          x0, d0
    // 0xb10648: asr             x16, x0, #0x1e
    // 0xb1064c: cmp             x16, x0, asr #63
    // 0xb10650: b.ne            #0xb10750
    // 0xb10654: lsl             x0, x0, #1
    // 0xb10658: r1 = LoadInt32Instr(r0)
    //     0xb10658: sbfx            x1, x0, #1, #0x1f
    //     0xb1065c: tbz             w0, #0, #0xb10664
    //     0xb10660: ldur            x1, [x0, #7]
    // 0xb10664: r0 = 255
    //     0xb10664: movz            x0, #0xff
    // 0xb10668: and             x2, x1, x0
    // 0xb1066c: ubfx            x2, x2, #0, #0x20
    // 0xb10670: lsl             x1, x2, #8
    // 0xb10674: ldur            x2, [fp, #-0x18]
    // 0xb10678: orr             x3, x2, x1
    // 0xb1067c: ldur            x1, [fp, #-8]
    // 0xb10680: stur            x3, [fp, #-0x10]
    // 0xb10684: LoadField: d0 = r1->field_1f
    //     0xb10684: ldur            d0, [x1, #0x1f]
    // 0xb10688: d1 = 255.000000
    //     0xb10688: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xb1068c: fmul            d2, d0, d1
    // 0xb10690: mov             v0.16b, v2.16b
    // 0xb10694: stp             fp, lr, [SP, #-0x10]!
    // 0xb10698: mov             fp, SP
    // 0xb1069c: CallRuntime_LibcRound(double) -> double
    //     0xb1069c: and             SP, SP, #0xfffffffffffffff0
    //     0xb106a0: mov             sp, SP
    //     0xb106a4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb106a8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb106ac: blr             x16
    //     0xb106b0: movz            x16, #0x8
    //     0xb106b4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb106b8: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb106bc: sub             sp, x16, #1, lsl #12
    //     0xb106c0: mov             SP, fp
    //     0xb106c4: ldp             fp, lr, [SP], #0x10
    // 0xb106c8: fcmp            d0, d0
    // 0xb106cc: b.vs            #0xb1076c
    // 0xb106d0: fcvtzs          x1, d0
    // 0xb106d4: asr             x16, x1, #0x1e
    // 0xb106d8: cmp             x16, x1, asr #63
    // 0xb106dc: b.ne            #0xb1076c
    // 0xb106e0: lsl             x1, x1, #1
    // 0xb106e4: r2 = LoadInt32Instr(r1)
    //     0xb106e4: sbfx            x2, x1, #1, #0x1f
    //     0xb106e8: tbz             w1, #0, #0xb106f0
    //     0xb106ec: ldur            x2, [x1, #7]
    // 0xb106f0: r1 = 255
    //     0xb106f0: movz            x1, #0xff
    // 0xb106f4: and             x3, x2, x1
    // 0xb106f8: ldur            x1, [fp, #-0x10]
    // 0xb106fc: ubfx            x1, x1, #0, #0x20
    // 0xb10700: orr             x2, x1, x3
    // 0xb10704: ubfx            x2, x2, #0, #0x20
    // 0xb10708: mov             x0, x2
    // 0xb1070c: LeaveFrame
    //     0xb1070c: mov             SP, fp
    //     0xb10710: ldp             fp, lr, [SP], #0x10
    // 0xb10714: ret
    //     0xb10714: ret             
    // 0xb10718: SaveReg d0
    //     0xb10718: str             q0, [SP, #-0x10]!
    // 0xb1071c: r0 = 74
    //     0xb1071c: movz            x0, #0x4a
    // 0xb10720: r30 = DoubleToIntegerStub
    //     0xb10720: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb10724: LoadField: r30 = r30->field_7
    //     0xb10724: ldur            lr, [lr, #7]
    // 0xb10728: blr             lr
    // 0xb1072c: RestoreReg d0
    //     0xb1072c: ldr             q0, [SP], #0x10
    // 0xb10730: b               #0xb10548
    // 0xb10734: SaveReg d0
    //     0xb10734: str             q0, [SP, #-0x10]!
    // 0xb10738: r0 = 74
    //     0xb10738: movz            x0, #0x4a
    // 0xb1073c: r30 = DoubleToIntegerStub
    //     0xb1073c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb10740: LoadField: r30 = r30->field_7
    //     0xb10740: ldur            lr, [lr, #7]
    // 0xb10744: blr             lr
    // 0xb10748: RestoreReg d0
    //     0xb10748: ldr             q0, [SP], #0x10
    // 0xb1074c: b               #0xb105cc
    // 0xb10750: SaveReg d0
    //     0xb10750: str             q0, [SP, #-0x10]!
    // 0xb10754: r0 = 74
    //     0xb10754: movz            x0, #0x4a
    // 0xb10758: r30 = DoubleToIntegerStub
    //     0xb10758: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb1075c: LoadField: r30 = r30->field_7
    //     0xb1075c: ldur            lr, [lr, #7]
    // 0xb10760: blr             lr
    // 0xb10764: RestoreReg d0
    //     0xb10764: ldr             q0, [SP], #0x10
    // 0xb10768: b               #0xb10658
    // 0xb1076c: SaveReg d0
    //     0xb1076c: str             q0, [SP, #-0x10]!
    // 0xb10770: r0 = 74
    //     0xb10770: movz            x0, #0x4a
    // 0xb10774: r30 = DoubleToIntegerStub
    //     0xb10774: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb10778: LoadField: r30 = r30->field_7
    //     0xb10778: ldur            lr, [lr, #7]
    // 0xb1077c: blr             lr
    // 0xb10780: mov             x1, x0
    // 0xb10784: RestoreReg d0
    //     0xb10784: ldr             q0, [SP], #0x10
    // 0xb10788: b               #0xb106e4
  }
  int hashCode(PdfColor) {
    // ** addr: 0xbf1854, size: 0x48
    // 0xbf1854: EnterFrame
    //     0xbf1854: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1858: mov             fp, SP
    // 0xbf185c: CheckStackOverflow
    //     0xbf185c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1860: cmp             SP, x16
    //     0xbf1864: b.ls            #0xbf1894
    // 0xbf1868: ldr             x1, [fp, #0x10]
    // 0xbf186c: r0 = toInt()
    //     0xbf186c: bl              #0xb104d8  ; [package:pdf/src/pdf/color.dart] PdfColor::toInt
    // 0xbf1870: mov             x2, x0
    // 0xbf1874: r0 = BoxInt64Instr(r2)
    //     0xbf1874: sbfiz           x0, x2, #1, #0x1f
    //     0xbf1878: cmp             x2, x0, asr #1
    //     0xbf187c: b.eq            #0xbf1888
    //     0xbf1880: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1884: stur            x2, [x0, #7]
    // 0xbf1888: LeaveFrame
    //     0xbf1888: mov             SP, fp
    //     0xbf188c: ldp             fp, lr, [SP], #0x10
    // 0xbf1890: ret
    //     0xbf1890: ret             
    // 0xbf1894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1894: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1898: b               #0xbf1868
  }
  _ toString(/* No info */) {
    // ** addr: 0xc33d38, size: 0x23c
    // 0xc33d38: EnterFrame
    //     0xc33d38: stp             fp, lr, [SP, #-0x10]!
    //     0xc33d3c: mov             fp, SP
    // 0xc33d40: AllocStack(0x10)
    //     0xc33d40: sub             SP, SP, #0x10
    // 0xc33d44: CheckStackOverflow
    //     0xc33d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33d48: cmp             SP, x16
    //     0xc33d4c: b.ls            #0xc33f0c
    // 0xc33d50: ldr             x16, [fp, #0x10]
    // 0xc33d54: str             x16, [SP]
    // 0xc33d58: r0 = runtimeType()
    //     0xc33d58: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xc33d5c: r1 = Null
    //     0xc33d5c: mov             x1, NULL
    // 0xc33d60: r2 = 20
    //     0xc33d60: movz            x2, #0x14
    // 0xc33d64: stur            x0, [fp, #-8]
    // 0xc33d68: r0 = AllocateArray()
    //     0xc33d68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33d6c: mov             x2, x0
    // 0xc33d70: ldur            x0, [fp, #-8]
    // 0xc33d74: StoreField: r2->field_f = r0
    //     0xc33d74: stur            w0, [x2, #0xf]
    // 0xc33d78: r16 = "("
    //     0xc33d78: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xc33d7c: ldr             x16, [x16, #0xf08]
    // 0xc33d80: StoreField: r2->field_13 = r16
    //     0xc33d80: stur            w16, [x2, #0x13]
    // 0xc33d84: ldr             x3, [fp, #0x10]
    // 0xc33d88: LoadField: d0 = r3->field_f
    //     0xc33d88: ldur            d0, [x3, #0xf]
    // 0xc33d8c: r0 = inline_Allocate_Double()
    //     0xc33d8c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33d90: add             x0, x0, #0x10
    //     0xc33d94: cmp             x1, x0
    //     0xc33d98: b.ls            #0xc33f14
    //     0xc33d9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33da0: sub             x0, x0, #0xf
    //     0xc33da4: movz            x1, #0xe15c
    //     0xc33da8: movk            x1, #0x3, lsl #16
    //     0xc33dac: stur            x1, [x0, #-1]
    // 0xc33db0: StoreField: r0->field_7 = d0
    //     0xc33db0: stur            d0, [x0, #7]
    // 0xc33db4: mov             x1, x2
    // 0xc33db8: ArrayStore: r1[2] = r0  ; List_4
    //     0xc33db8: add             x25, x1, #0x17
    //     0xc33dbc: str             w0, [x25]
    //     0xc33dc0: tbz             w0, #0, #0xc33ddc
    //     0xc33dc4: ldurb           w16, [x1, #-1]
    //     0xc33dc8: ldurb           w17, [x0, #-1]
    //     0xc33dcc: and             x16, x17, x16, lsr #2
    //     0xc33dd0: tst             x16, HEAP, lsr #32
    //     0xc33dd4: b.eq            #0xc33ddc
    //     0xc33dd8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33ddc: r16 = ", "
    //     0xc33ddc: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33de0: StoreField: r2->field_1b = r16
    //     0xc33de0: stur            w16, [x2, #0x1b]
    // 0xc33de4: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc33de4: ldur            d0, [x3, #0x17]
    // 0xc33de8: r0 = inline_Allocate_Double()
    //     0xc33de8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33dec: add             x0, x0, #0x10
    //     0xc33df0: cmp             x1, x0
    //     0xc33df4: b.ls            #0xc33f2c
    //     0xc33df8: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33dfc: sub             x0, x0, #0xf
    //     0xc33e00: movz            x1, #0xe15c
    //     0xc33e04: movk            x1, #0x3, lsl #16
    //     0xc33e08: stur            x1, [x0, #-1]
    // 0xc33e0c: StoreField: r0->field_7 = d0
    //     0xc33e0c: stur            d0, [x0, #7]
    // 0xc33e10: mov             x1, x2
    // 0xc33e14: ArrayStore: r1[4] = r0  ; List_4
    //     0xc33e14: add             x25, x1, #0x1f
    //     0xc33e18: str             w0, [x25]
    //     0xc33e1c: tbz             w0, #0, #0xc33e38
    //     0xc33e20: ldurb           w16, [x1, #-1]
    //     0xc33e24: ldurb           w17, [x0, #-1]
    //     0xc33e28: and             x16, x17, x16, lsr #2
    //     0xc33e2c: tst             x16, HEAP, lsr #32
    //     0xc33e30: b.eq            #0xc33e38
    //     0xc33e34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33e38: r16 = ", "
    //     0xc33e38: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33e3c: StoreField: r2->field_23 = r16
    //     0xc33e3c: stur            w16, [x2, #0x23]
    // 0xc33e40: LoadField: d0 = r3->field_1f
    //     0xc33e40: ldur            d0, [x3, #0x1f]
    // 0xc33e44: r0 = inline_Allocate_Double()
    //     0xc33e44: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33e48: add             x0, x0, #0x10
    //     0xc33e4c: cmp             x1, x0
    //     0xc33e50: b.ls            #0xc33f44
    //     0xc33e54: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33e58: sub             x0, x0, #0xf
    //     0xc33e5c: movz            x1, #0xe15c
    //     0xc33e60: movk            x1, #0x3, lsl #16
    //     0xc33e64: stur            x1, [x0, #-1]
    // 0xc33e68: StoreField: r0->field_7 = d0
    //     0xc33e68: stur            d0, [x0, #7]
    // 0xc33e6c: mov             x1, x2
    // 0xc33e70: ArrayStore: r1[6] = r0  ; List_4
    //     0xc33e70: add             x25, x1, #0x27
    //     0xc33e74: str             w0, [x25]
    //     0xc33e78: tbz             w0, #0, #0xc33e94
    //     0xc33e7c: ldurb           w16, [x1, #-1]
    //     0xc33e80: ldurb           w17, [x0, #-1]
    //     0xc33e84: and             x16, x17, x16, lsr #2
    //     0xc33e88: tst             x16, HEAP, lsr #32
    //     0xc33e8c: b.eq            #0xc33e94
    //     0xc33e90: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33e94: r16 = ", "
    //     0xc33e94: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33e98: StoreField: r2->field_2b = r16
    //     0xc33e98: stur            w16, [x2, #0x2b]
    // 0xc33e9c: LoadField: d0 = r3->field_7
    //     0xc33e9c: ldur            d0, [x3, #7]
    // 0xc33ea0: r0 = inline_Allocate_Double()
    //     0xc33ea0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33ea4: add             x0, x0, #0x10
    //     0xc33ea8: cmp             x1, x0
    //     0xc33eac: b.ls            #0xc33f5c
    //     0xc33eb0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33eb4: sub             x0, x0, #0xf
    //     0xc33eb8: movz            x1, #0xe15c
    //     0xc33ebc: movk            x1, #0x3, lsl #16
    //     0xc33ec0: stur            x1, [x0, #-1]
    // 0xc33ec4: StoreField: r0->field_7 = d0
    //     0xc33ec4: stur            d0, [x0, #7]
    // 0xc33ec8: mov             x1, x2
    // 0xc33ecc: ArrayStore: r1[8] = r0  ; List_4
    //     0xc33ecc: add             x25, x1, #0x2f
    //     0xc33ed0: str             w0, [x25]
    //     0xc33ed4: tbz             w0, #0, #0xc33ef0
    //     0xc33ed8: ldurb           w16, [x1, #-1]
    //     0xc33edc: ldurb           w17, [x0, #-1]
    //     0xc33ee0: and             x16, x17, x16, lsr #2
    //     0xc33ee4: tst             x16, HEAP, lsr #32
    //     0xc33ee8: b.eq            #0xc33ef0
    //     0xc33eec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33ef0: r16 = ")"
    //     0xc33ef0: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc33ef4: StoreField: r2->field_33 = r16
    //     0xc33ef4: stur            w16, [x2, #0x33]
    // 0xc33ef8: str             x2, [SP]
    // 0xc33efc: r0 = _interpolate()
    //     0xc33efc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc33f00: LeaveFrame
    //     0xc33f00: mov             SP, fp
    //     0xc33f04: ldp             fp, lr, [SP], #0x10
    // 0xc33f08: ret
    //     0xc33f08: ret             
    // 0xc33f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33f10: b               #0xc33d50
    // 0xc33f14: SaveReg d0
    //     0xc33f14: str             q0, [SP, #-0x10]!
    // 0xc33f18: stp             x2, x3, [SP, #-0x10]!
    // 0xc33f1c: r0 = AllocateDouble()
    //     0xc33f1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33f20: ldp             x2, x3, [SP], #0x10
    // 0xc33f24: RestoreReg d0
    //     0xc33f24: ldr             q0, [SP], #0x10
    // 0xc33f28: b               #0xc33db0
    // 0xc33f2c: SaveReg d0
    //     0xc33f2c: str             q0, [SP, #-0x10]!
    // 0xc33f30: stp             x2, x3, [SP, #-0x10]!
    // 0xc33f34: r0 = AllocateDouble()
    //     0xc33f34: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33f38: ldp             x2, x3, [SP], #0x10
    // 0xc33f3c: RestoreReg d0
    //     0xc33f3c: ldr             q0, [SP], #0x10
    // 0xc33f40: b               #0xc33e0c
    // 0xc33f44: SaveReg d0
    //     0xc33f44: str             q0, [SP, #-0x10]!
    // 0xc33f48: stp             x2, x3, [SP, #-0x10]!
    // 0xc33f4c: r0 = AllocateDouble()
    //     0xc33f4c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33f50: ldp             x2, x3, [SP], #0x10
    // 0xc33f54: RestoreReg d0
    //     0xc33f54: ldr             q0, [SP], #0x10
    // 0xc33f58: b               #0xc33e68
    // 0xc33f5c: SaveReg d0
    //     0xc33f5c: str             q0, [SP, #-0x10]!
    // 0xc33f60: SaveReg r2
    //     0xc33f60: str             x2, [SP, #-8]!
    // 0xc33f64: r0 = AllocateDouble()
    //     0xc33f64: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33f68: RestoreReg r2
    //     0xc33f68: ldr             x2, [SP], #8
    // 0xc33f6c: RestoreReg d0
    //     0xc33f6c: ldr             q0, [SP], #0x10
    // 0xc33f70: b               #0xc33ec4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c458, size: 0xf8
    // 0xd7c458: EnterFrame
    //     0xd7c458: stp             fp, lr, [SP, #-0x10]!
    //     0xd7c45c: mov             fp, SP
    // 0xd7c460: AllocStack(0x10)
    //     0xd7c460: sub             SP, SP, #0x10
    // 0xd7c464: CheckStackOverflow
    //     0xd7c464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7c468: cmp             SP, x16
    //     0xd7c46c: b.ls            #0xd7c548
    // 0xd7c470: ldr             x0, [fp, #0x10]
    // 0xd7c474: cmp             w0, NULL
    // 0xd7c478: b.ne            #0xd7c48c
    // 0xd7c47c: r0 = false
    //     0xd7c47c: add             x0, NULL, #0x30  ; false
    // 0xd7c480: LeaveFrame
    //     0xd7c480: mov             SP, fp
    //     0xd7c484: ldp             fp, lr, [SP], #0x10
    // 0xd7c488: ret
    //     0xd7c488: ret             
    // 0xd7c48c: ldr             x1, [fp, #0x18]
    // 0xd7c490: cmp             w1, w0
    // 0xd7c494: b.ne            #0xd7c4a8
    // 0xd7c498: r0 = true
    //     0xd7c498: add             x0, NULL, #0x20  ; true
    // 0xd7c49c: LeaveFrame
    //     0xd7c49c: mov             SP, fp
    //     0xd7c4a0: ldp             fp, lr, [SP], #0x10
    // 0xd7c4a4: ret
    //     0xd7c4a4: ret             
    // 0xd7c4a8: stp             x1, x0, [SP]
    // 0xd7c4ac: r0 = _haveSameRuntimeType()
    //     0xd7c4ac: bl              #0x6c18d0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xd7c4b0: tbz             w0, #4, #0xd7c4c4
    // 0xd7c4b4: r0 = false
    //     0xd7c4b4: add             x0, NULL, #0x30  ; false
    // 0xd7c4b8: LeaveFrame
    //     0xd7c4b8: mov             SP, fp
    //     0xd7c4bc: ldp             fp, lr, [SP], #0x10
    // 0xd7c4c0: ret
    //     0xd7c4c0: ret             
    // 0xd7c4c4: ldr             x1, [fp, #0x10]
    // 0xd7c4c8: r2 = 60
    //     0xd7c4c8: movz            x2, #0x3c
    // 0xd7c4cc: branchIfSmi(r1, 0xd7c4d8)
    //     0xd7c4cc: tbz             w1, #0, #0xd7c4d8
    // 0xd7c4d0: r2 = LoadClassIdInstr(r1)
    //     0xd7c4d0: ldur            x2, [x1, #-1]
    //     0xd7c4d4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c4d8: sub             x16, x2, #0x39c
    // 0xd7c4dc: cmp             x16, #1
    // 0xd7c4e0: b.hi            #0xd7c538
    // 0xd7c4e4: ldr             x2, [fp, #0x18]
    // 0xd7c4e8: LoadField: d0 = r1->field_f
    //     0xd7c4e8: ldur            d0, [x1, #0xf]
    // 0xd7c4ec: LoadField: d1 = r2->field_f
    //     0xd7c4ec: ldur            d1, [x2, #0xf]
    // 0xd7c4f0: fcmp            d0, d1
    // 0xd7c4f4: b.ne            #0xd7c538
    // 0xd7c4f8: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xd7c4f8: ldur            d0, [x1, #0x17]
    // 0xd7c4fc: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xd7c4fc: ldur            d1, [x2, #0x17]
    // 0xd7c500: fcmp            d0, d1
    // 0xd7c504: b.ne            #0xd7c538
    // 0xd7c508: LoadField: d0 = r1->field_1f
    //     0xd7c508: ldur            d0, [x1, #0x1f]
    // 0xd7c50c: LoadField: d1 = r2->field_1f
    //     0xd7c50c: ldur            d1, [x2, #0x1f]
    // 0xd7c510: fcmp            d0, d1
    // 0xd7c514: b.ne            #0xd7c538
    // 0xd7c518: LoadField: d0 = r1->field_7
    //     0xd7c518: ldur            d0, [x1, #7]
    // 0xd7c51c: LoadField: d1 = r2->field_7
    //     0xd7c51c: ldur            d1, [x2, #7]
    // 0xd7c520: fcmp            d0, d1
    // 0xd7c524: r16 = true
    //     0xd7c524: add             x16, NULL, #0x20  ; true
    // 0xd7c528: r17 = false
    //     0xd7c528: add             x17, NULL, #0x30  ; false
    // 0xd7c52c: csel            x1, x16, x17, eq
    // 0xd7c530: mov             x0, x1
    // 0xd7c534: b               #0xd7c53c
    // 0xd7c538: r0 = false
    //     0xd7c538: add             x0, NULL, #0x30  ; false
    // 0xd7c53c: LeaveFrame
    //     0xd7c53c: mov             SP, fp
    //     0xd7c540: ldp             fp, lr, [SP], #0x10
    // 0xd7c544: ret
    //     0xd7c544: ret             
    // 0xd7c548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7c548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7c54c: b               #0xd7c470
  }
  factory _ PdfColor.fromHex(/* No info */) {
    // ** addr: 0xe74944, size: 0x304
    // 0xe74944: EnterFrame
    //     0xe74944: stp             fp, lr, [SP, #-0x10]!
    //     0xe74948: mov             fp, SP
    // 0xe7494c: AllocStack(0x38)
    //     0xe7494c: sub             SP, SP, #0x38
    // 0xe74950: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe74950: mov             x0, x2
    //     0xe74954: stur            x2, [fp, #-8]
    // 0xe74958: CheckStackOverflow
    //     0xe74958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7495c: cmp             SP, x16
    //     0xe74960: b.ls            #0xe74c40
    // 0xe74964: mov             x1, x0
    // 0xe74968: r2 = "#"
    //     0xe74968: ldr             x2, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0xe7496c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7496c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74970: r0 = startsWith()
    //     0xe74970: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe74974: tbnz            w0, #4, #0xe7498c
    // 0xe74978: ldur            x1, [fp, #-8]
    // 0xe7497c: r2 = 1
    //     0xe7497c: movz            x2, #0x1
    // 0xe74980: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74980: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74984: r0 = substring()
    //     0xe74984: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74988: b               #0xe74990
    // 0xe7498c: ldur            x0, [fp, #-8]
    // 0xe74990: stur            x0, [fp, #-8]
    // 0xe74994: LoadField: r1 = r0->field_7
    //     0xe74994: ldur            w1, [x0, #7]
    // 0xe74998: r3 = LoadInt32Instr(r1)
    //     0xe74998: sbfx            x3, x1, #1, #0x1f
    // 0xe7499c: stur            x3, [fp, #-0x10]
    // 0xe749a0: cmp             x3, #3
    // 0xe749a4: b.ne            #0xe74af8
    // 0xe749a8: r16 = 2
    //     0xe749a8: movz            x16, #0x2
    // 0xe749ac: str             x16, [SP]
    // 0xe749b0: mov             x1, x0
    // 0xe749b4: r2 = 0
    //     0xe749b4: movz            x2, #0
    // 0xe749b8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe749b8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe749bc: r0 = substring()
    //     0xe749bc: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe749c0: r1 = LoadClassIdInstr(r0)
    //     0xe749c0: ldur            x1, [x0, #-1]
    //     0xe749c4: ubfx            x1, x1, #0xc, #0x14
    // 0xe749c8: mov             x16, x0
    // 0xe749cc: mov             x0, x1
    // 0xe749d0: mov             x1, x16
    // 0xe749d4: r2 = 2
    //     0xe749d4: movz            x2, #0x2
    // 0xe749d8: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xe749d8: sub             lr, x0, #0xfe6
    //     0xe749dc: ldr             lr, [x21, lr, lsl #3]
    //     0xe749e0: blr             lr
    // 0xe749e4: r16 = 32
    //     0xe749e4: movz            x16, #0x20
    // 0xe749e8: str             x16, [SP]
    // 0xe749ec: mov             x1, x0
    // 0xe749f0: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe749f0: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe749f4: r0 = parse()
    //     0xe749f4: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe749f8: scvtf           d0, x0
    // 0xe749fc: d1 = 255.000000
    //     0xe749fc: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74a00: fdiv            d2, d0, d1
    // 0xe74a04: stur            d2, [fp, #-0x18]
    // 0xe74a08: r16 = 4
    //     0xe74a08: movz            x16, #0x4
    // 0xe74a0c: str             x16, [SP]
    // 0xe74a10: ldur            x1, [fp, #-8]
    // 0xe74a14: r2 = 1
    //     0xe74a14: movz            x2, #0x1
    // 0xe74a18: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74a18: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74a1c: r0 = substring()
    //     0xe74a1c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74a20: r1 = LoadClassIdInstr(r0)
    //     0xe74a20: ldur            x1, [x0, #-1]
    //     0xe74a24: ubfx            x1, x1, #0xc, #0x14
    // 0xe74a28: mov             x16, x0
    // 0xe74a2c: mov             x0, x1
    // 0xe74a30: mov             x1, x16
    // 0xe74a34: r2 = 2
    //     0xe74a34: movz            x2, #0x2
    // 0xe74a38: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xe74a38: sub             lr, x0, #0xfe6
    //     0xe74a3c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74a40: blr             lr
    // 0xe74a44: r16 = 32
    //     0xe74a44: movz            x16, #0x20
    // 0xe74a48: str             x16, [SP]
    // 0xe74a4c: mov             x1, x0
    // 0xe74a50: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74a50: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74a54: r0 = parse()
    //     0xe74a54: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74a58: scvtf           d0, x0
    // 0xe74a5c: d1 = 255.000000
    //     0xe74a5c: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74a60: fdiv            d2, d0, d1
    // 0xe74a64: stur            d2, [fp, #-0x20]
    // 0xe74a68: r16 = 6
    //     0xe74a68: movz            x16, #0x6
    // 0xe74a6c: str             x16, [SP]
    // 0xe74a70: ldur            x1, [fp, #-8]
    // 0xe74a74: r2 = 2
    //     0xe74a74: movz            x2, #0x2
    // 0xe74a78: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74a78: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74a7c: r0 = substring()
    //     0xe74a7c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74a80: r1 = LoadClassIdInstr(r0)
    //     0xe74a80: ldur            x1, [x0, #-1]
    //     0xe74a84: ubfx            x1, x1, #0xc, #0x14
    // 0xe74a88: mov             x16, x0
    // 0xe74a8c: mov             x0, x1
    // 0xe74a90: mov             x1, x16
    // 0xe74a94: r2 = 2
    //     0xe74a94: movz            x2, #0x2
    // 0xe74a98: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xe74a98: sub             lr, x0, #0xfe6
    //     0xe74a9c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74aa0: blr             lr
    // 0xe74aa4: r16 = 32
    //     0xe74aa4: movz            x16, #0x20
    // 0xe74aa8: str             x16, [SP]
    // 0xe74aac: mov             x1, x0
    // 0xe74ab0: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74ab0: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74ab4: r0 = parse()
    //     0xe74ab4: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74ab8: scvtf           d0, x0
    // 0xe74abc: d1 = 255.000000
    //     0xe74abc: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74ac0: fdiv            d2, d0, d1
    // 0xe74ac4: stur            d2, [fp, #-0x28]
    // 0xe74ac8: r0 = PdfColor()
    //     0xe74ac8: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xe74acc: ldur            d0, [fp, #-0x18]
    // 0xe74ad0: StoreField: r0->field_f = d0
    //     0xe74ad0: stur            d0, [x0, #0xf]
    // 0xe74ad4: ldur            d0, [fp, #-0x20]
    // 0xe74ad8: ArrayStore: r0[0] = d0  ; List_8
    //     0xe74ad8: stur            d0, [x0, #0x17]
    // 0xe74adc: ldur            d0, [fp, #-0x28]
    // 0xe74ae0: StoreField: r0->field_1f = d0
    //     0xe74ae0: stur            d0, [x0, #0x1f]
    // 0xe74ae4: d0 = 1.000000
    //     0xe74ae4: fmov            d0, #1.00000000
    // 0xe74ae8: StoreField: r0->field_7 = d0
    //     0xe74ae8: stur            d0, [x0, #7]
    // 0xe74aec: LeaveFrame
    //     0xe74aec: mov             SP, fp
    //     0xe74af0: ldp             fp, lr, [SP], #0x10
    // 0xe74af4: ret
    //     0xe74af4: ret             
    // 0xe74af8: d1 = 255.000000
    //     0xe74af8: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74afc: r16 = 4
    //     0xe74afc: movz            x16, #0x4
    // 0xe74b00: str             x16, [SP]
    // 0xe74b04: ldur            x1, [fp, #-8]
    // 0xe74b08: r2 = 0
    //     0xe74b08: movz            x2, #0
    // 0xe74b0c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74b0c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74b10: r0 = substring()
    //     0xe74b10: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74b14: r16 = 32
    //     0xe74b14: movz            x16, #0x20
    // 0xe74b18: str             x16, [SP]
    // 0xe74b1c: mov             x1, x0
    // 0xe74b20: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74b20: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74b24: r0 = parse()
    //     0xe74b24: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74b28: scvtf           d0, x0
    // 0xe74b2c: d1 = 255.000000
    //     0xe74b2c: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74b30: fdiv            d2, d0, d1
    // 0xe74b34: stur            d2, [fp, #-0x18]
    // 0xe74b38: r16 = 8
    //     0xe74b38: movz            x16, #0x8
    // 0xe74b3c: str             x16, [SP]
    // 0xe74b40: ldur            x1, [fp, #-8]
    // 0xe74b44: r2 = 2
    //     0xe74b44: movz            x2, #0x2
    // 0xe74b48: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74b48: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74b4c: r0 = substring()
    //     0xe74b4c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74b50: r16 = 32
    //     0xe74b50: movz            x16, #0x20
    // 0xe74b54: str             x16, [SP]
    // 0xe74b58: mov             x1, x0
    // 0xe74b5c: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74b5c: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74b60: r0 = parse()
    //     0xe74b60: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74b64: scvtf           d0, x0
    // 0xe74b68: d1 = 255.000000
    //     0xe74b68: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74b6c: fdiv            d2, d0, d1
    // 0xe74b70: stur            d2, [fp, #-0x20]
    // 0xe74b74: r16 = 12
    //     0xe74b74: movz            x16, #0xc
    // 0xe74b78: str             x16, [SP]
    // 0xe74b7c: ldur            x1, [fp, #-8]
    // 0xe74b80: r2 = 4
    //     0xe74b80: movz            x2, #0x4
    // 0xe74b84: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74b84: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74b88: r0 = substring()
    //     0xe74b88: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74b8c: r16 = 32
    //     0xe74b8c: movz            x16, #0x20
    // 0xe74b90: str             x16, [SP]
    // 0xe74b94: mov             x1, x0
    // 0xe74b98: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74b98: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74b9c: r0 = parse()
    //     0xe74b9c: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74ba0: scvtf           d0, x0
    // 0xe74ba4: d1 = 255.000000
    //     0xe74ba4: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74ba8: fdiv            d2, d0, d1
    // 0xe74bac: ldur            x0, [fp, #-0x10]
    // 0xe74bb0: stur            d2, [fp, #-0x28]
    // 0xe74bb4: cmp             x0, #8
    // 0xe74bb8: b.ne            #0xe74bfc
    // 0xe74bbc: r16 = 16
    //     0xe74bbc: movz            x16, #0x10
    // 0xe74bc0: str             x16, [SP]
    // 0xe74bc4: ldur            x1, [fp, #-8]
    // 0xe74bc8: r2 = 6
    //     0xe74bc8: movz            x2, #0x6
    // 0xe74bcc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74bcc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74bd0: r0 = substring()
    //     0xe74bd0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74bd4: r16 = 32
    //     0xe74bd4: movz            x16, #0x20
    // 0xe74bd8: str             x16, [SP]
    // 0xe74bdc: mov             x1, x0
    // 0xe74be0: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0xe74be0: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0xe74be4: r0 = parse()
    //     0xe74be4: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xe74be8: scvtf           d0, x0
    // 0xe74bec: d1 = 255.000000
    //     0xe74bec: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe74bf0: fdiv            d2, d0, d1
    // 0xe74bf4: mov             v3.16b, v2.16b
    // 0xe74bf8: b               #0xe74c00
    // 0xe74bfc: d3 = 1.000000
    //     0xe74bfc: fmov            d3, #1.00000000
    // 0xe74c00: ldur            d2, [fp, #-0x18]
    // 0xe74c04: ldur            d1, [fp, #-0x20]
    // 0xe74c08: ldur            d0, [fp, #-0x28]
    // 0xe74c0c: stur            d3, [fp, #-0x30]
    // 0xe74c10: r0 = PdfColor()
    //     0xe74c10: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xe74c14: ldur            d0, [fp, #-0x18]
    // 0xe74c18: StoreField: r0->field_f = d0
    //     0xe74c18: stur            d0, [x0, #0xf]
    // 0xe74c1c: ldur            d0, [fp, #-0x20]
    // 0xe74c20: ArrayStore: r0[0] = d0  ; List_8
    //     0xe74c20: stur            d0, [x0, #0x17]
    // 0xe74c24: ldur            d0, [fp, #-0x28]
    // 0xe74c28: StoreField: r0->field_1f = d0
    //     0xe74c28: stur            d0, [x0, #0x1f]
    // 0xe74c2c: ldur            d0, [fp, #-0x30]
    // 0xe74c30: StoreField: r0->field_7 = d0
    //     0xe74c30: stur            d0, [x0, #7]
    // 0xe74c34: LeaveFrame
    //     0xe74c34: mov             SP, fp
    //     0xe74c38: ldp             fp, lr, [SP], #0x10
    // 0xe74c3c: ret
    //     0xe74c3c: ret             
    // 0xe74c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe74c40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe74c44: b               #0xe74964
  }
}

// class id: 925, size: 0x40, field offset: 0x28
//   const constructor, 
class PdfColorHsl extends PdfColor {

  _ toString(/* No info */) {
    // ** addr: 0xc33b08, size: 0x230
    // 0xc33b08: EnterFrame
    //     0xc33b08: stp             fp, lr, [SP, #-0x10]!
    //     0xc33b0c: mov             fp, SP
    // 0xc33b10: AllocStack(0x8)
    //     0xc33b10: sub             SP, SP, #8
    // 0xc33b14: CheckStackOverflow
    //     0xc33b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33b18: cmp             SP, x16
    //     0xc33b1c: b.ls            #0xc33cd0
    // 0xc33b20: r1 = Null
    //     0xc33b20: mov             x1, NULL
    // 0xc33b24: r2 = 20
    //     0xc33b24: movz            x2, #0x14
    // 0xc33b28: r0 = AllocateArray()
    //     0xc33b28: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33b2c: mov             x2, x0
    // 0xc33b30: r16 = PdfColorHsl
    //     0xc33b30: add             x16, PP, #0x47, lsl #12  ; [pp+0x47998] Type: PdfColorHsl
    //     0xc33b34: ldr             x16, [x16, #0x998]
    // 0xc33b38: StoreField: r2->field_f = r16
    //     0xc33b38: stur            w16, [x2, #0xf]
    // 0xc33b3c: r16 = "("
    //     0xc33b3c: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xc33b40: ldr             x16, [x16, #0xf08]
    // 0xc33b44: StoreField: r2->field_13 = r16
    //     0xc33b44: stur            w16, [x2, #0x13]
    // 0xc33b48: ldr             x3, [fp, #0x10]
    // 0xc33b4c: LoadField: d0 = r3->field_27
    //     0xc33b4c: ldur            d0, [x3, #0x27]
    // 0xc33b50: r0 = inline_Allocate_Double()
    //     0xc33b50: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33b54: add             x0, x0, #0x10
    //     0xc33b58: cmp             x1, x0
    //     0xc33b5c: b.ls            #0xc33cd8
    //     0xc33b60: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33b64: sub             x0, x0, #0xf
    //     0xc33b68: movz            x1, #0xe15c
    //     0xc33b6c: movk            x1, #0x3, lsl #16
    //     0xc33b70: stur            x1, [x0, #-1]
    // 0xc33b74: StoreField: r0->field_7 = d0
    //     0xc33b74: stur            d0, [x0, #7]
    // 0xc33b78: mov             x1, x2
    // 0xc33b7c: ArrayStore: r1[2] = r0  ; List_4
    //     0xc33b7c: add             x25, x1, #0x17
    //     0xc33b80: str             w0, [x25]
    //     0xc33b84: tbz             w0, #0, #0xc33ba0
    //     0xc33b88: ldurb           w16, [x1, #-1]
    //     0xc33b8c: ldurb           w17, [x0, #-1]
    //     0xc33b90: and             x16, x17, x16, lsr #2
    //     0xc33b94: tst             x16, HEAP, lsr #32
    //     0xc33b98: b.eq            #0xc33ba0
    //     0xc33b9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33ba0: r16 = ", "
    //     0xc33ba0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33ba4: StoreField: r2->field_1b = r16
    //     0xc33ba4: stur            w16, [x2, #0x1b]
    // 0xc33ba8: LoadField: d0 = r3->field_2f
    //     0xc33ba8: ldur            d0, [x3, #0x2f]
    // 0xc33bac: r0 = inline_Allocate_Double()
    //     0xc33bac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33bb0: add             x0, x0, #0x10
    //     0xc33bb4: cmp             x1, x0
    //     0xc33bb8: b.ls            #0xc33cf0
    //     0xc33bbc: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33bc0: sub             x0, x0, #0xf
    //     0xc33bc4: movz            x1, #0xe15c
    //     0xc33bc8: movk            x1, #0x3, lsl #16
    //     0xc33bcc: stur            x1, [x0, #-1]
    // 0xc33bd0: StoreField: r0->field_7 = d0
    //     0xc33bd0: stur            d0, [x0, #7]
    // 0xc33bd4: mov             x1, x2
    // 0xc33bd8: ArrayStore: r1[4] = r0  ; List_4
    //     0xc33bd8: add             x25, x1, #0x1f
    //     0xc33bdc: str             w0, [x25]
    //     0xc33be0: tbz             w0, #0, #0xc33bfc
    //     0xc33be4: ldurb           w16, [x1, #-1]
    //     0xc33be8: ldurb           w17, [x0, #-1]
    //     0xc33bec: and             x16, x17, x16, lsr #2
    //     0xc33bf0: tst             x16, HEAP, lsr #32
    //     0xc33bf4: b.eq            #0xc33bfc
    //     0xc33bf8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33bfc: r16 = ", "
    //     0xc33bfc: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33c00: StoreField: r2->field_23 = r16
    //     0xc33c00: stur            w16, [x2, #0x23]
    // 0xc33c04: LoadField: d0 = r3->field_37
    //     0xc33c04: ldur            d0, [x3, #0x37]
    // 0xc33c08: r0 = inline_Allocate_Double()
    //     0xc33c08: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33c0c: add             x0, x0, #0x10
    //     0xc33c10: cmp             x1, x0
    //     0xc33c14: b.ls            #0xc33d08
    //     0xc33c18: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33c1c: sub             x0, x0, #0xf
    //     0xc33c20: movz            x1, #0xe15c
    //     0xc33c24: movk            x1, #0x3, lsl #16
    //     0xc33c28: stur            x1, [x0, #-1]
    // 0xc33c2c: StoreField: r0->field_7 = d0
    //     0xc33c2c: stur            d0, [x0, #7]
    // 0xc33c30: mov             x1, x2
    // 0xc33c34: ArrayStore: r1[6] = r0  ; List_4
    //     0xc33c34: add             x25, x1, #0x27
    //     0xc33c38: str             w0, [x25]
    //     0xc33c3c: tbz             w0, #0, #0xc33c58
    //     0xc33c40: ldurb           w16, [x1, #-1]
    //     0xc33c44: ldurb           w17, [x0, #-1]
    //     0xc33c48: and             x16, x17, x16, lsr #2
    //     0xc33c4c: tst             x16, HEAP, lsr #32
    //     0xc33c50: b.eq            #0xc33c58
    //     0xc33c54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33c58: r16 = ", "
    //     0xc33c58: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc33c5c: StoreField: r2->field_2b = r16
    //     0xc33c5c: stur            w16, [x2, #0x2b]
    // 0xc33c60: LoadField: d0 = r3->field_7
    //     0xc33c60: ldur            d0, [x3, #7]
    // 0xc33c64: r0 = inline_Allocate_Double()
    //     0xc33c64: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc33c68: add             x0, x0, #0x10
    //     0xc33c6c: cmp             x1, x0
    //     0xc33c70: b.ls            #0xc33d20
    //     0xc33c74: str             x0, [THR, #0x50]  ; THR::top
    //     0xc33c78: sub             x0, x0, #0xf
    //     0xc33c7c: movz            x1, #0xe15c
    //     0xc33c80: movk            x1, #0x3, lsl #16
    //     0xc33c84: stur            x1, [x0, #-1]
    // 0xc33c88: StoreField: r0->field_7 = d0
    //     0xc33c88: stur            d0, [x0, #7]
    // 0xc33c8c: mov             x1, x2
    // 0xc33c90: ArrayStore: r1[8] = r0  ; List_4
    //     0xc33c90: add             x25, x1, #0x2f
    //     0xc33c94: str             w0, [x25]
    //     0xc33c98: tbz             w0, #0, #0xc33cb4
    //     0xc33c9c: ldurb           w16, [x1, #-1]
    //     0xc33ca0: ldurb           w17, [x0, #-1]
    //     0xc33ca4: and             x16, x17, x16, lsr #2
    //     0xc33ca8: tst             x16, HEAP, lsr #32
    //     0xc33cac: b.eq            #0xc33cb4
    //     0xc33cb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc33cb4: r16 = ")"
    //     0xc33cb4: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc33cb8: StoreField: r2->field_33 = r16
    //     0xc33cb8: stur            w16, [x2, #0x33]
    // 0xc33cbc: str             x2, [SP]
    // 0xc33cc0: r0 = _interpolate()
    //     0xc33cc0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc33cc4: LeaveFrame
    //     0xc33cc4: mov             SP, fp
    //     0xc33cc8: ldp             fp, lr, [SP], #0x10
    // 0xc33ccc: ret
    //     0xc33ccc: ret             
    // 0xc33cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc33cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc33cd4: b               #0xc33b20
    // 0xc33cd8: SaveReg d0
    //     0xc33cd8: str             q0, [SP, #-0x10]!
    // 0xc33cdc: stp             x2, x3, [SP, #-0x10]!
    // 0xc33ce0: r0 = AllocateDouble()
    //     0xc33ce0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33ce4: ldp             x2, x3, [SP], #0x10
    // 0xc33ce8: RestoreReg d0
    //     0xc33ce8: ldr             q0, [SP], #0x10
    // 0xc33cec: b               #0xc33b74
    // 0xc33cf0: SaveReg d0
    //     0xc33cf0: str             q0, [SP, #-0x10]!
    // 0xc33cf4: stp             x2, x3, [SP, #-0x10]!
    // 0xc33cf8: r0 = AllocateDouble()
    //     0xc33cf8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33cfc: ldp             x2, x3, [SP], #0x10
    // 0xc33d00: RestoreReg d0
    //     0xc33d00: ldr             q0, [SP], #0x10
    // 0xc33d04: b               #0xc33bd0
    // 0xc33d08: SaveReg d0
    //     0xc33d08: str             q0, [SP, #-0x10]!
    // 0xc33d0c: stp             x2, x3, [SP, #-0x10]!
    // 0xc33d10: r0 = AllocateDouble()
    //     0xc33d10: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33d14: ldp             x2, x3, [SP], #0x10
    // 0xc33d18: RestoreReg d0
    //     0xc33d18: ldr             q0, [SP], #0x10
    // 0xc33d1c: b               #0xc33c2c
    // 0xc33d20: SaveReg d0
    //     0xc33d20: str             q0, [SP, #-0x10]!
    // 0xc33d24: SaveReg r2
    //     0xc33d24: str             x2, [SP, #-8]!
    // 0xc33d28: r0 = AllocateDouble()
    //     0xc33d28: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc33d2c: RestoreReg r2
    //     0xc33d2c: ldr             x2, [SP], #8
    // 0xc33d30: RestoreReg d0
    //     0xc33d30: ldr             q0, [SP], #0x10
    // 0xc33d34: b               #0xc33c88
  }
  factory _ PdfColorHsl(/* No info */) {
    // ** addr: 0xe7626c, size: 0x338
    // 0xe7626c: EnterFrame
    //     0xe7626c: stp             fp, lr, [SP, #-0x10]!
    //     0xe76270: mov             fp, SP
    // 0xe76274: AllocStack(0x48)
    //     0xe76274: sub             SP, SP, #0x48
    // 0xe76278: d5 = 2.000000
    //     0xe76278: fmov            d5, #2.00000000
    // 0xe7627c: d4 = 1.000000
    //     0xe7627c: fmov            d4, #1.00000000
    // 0xe76280: d3 = 0.000000
    //     0xe76280: eor             v3.16b, v3.16b, v3.16b
    // 0xe76284: mov             v7.16b, v0.16b
    // 0xe76288: mov             v6.16b, v1.16b
    // 0xe7628c: stur            d0, [fp, #-0x28]
    // 0xe76290: stur            d1, [fp, #-0x30]
    // 0xe76294: stur            d2, [fp, #-0x38]
    // 0xe76298: CheckStackOverflow
    //     0xe76298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7629c: cmp             SP, x16
    //     0xe762a0: b.ls            #0xe76538
    // 0xe762a4: fmul            d0, d2, d5
    // 0xe762a8: fsub            d1, d0, d4
    // 0xe762ac: fcmp            d1, d3
    // 0xe762b0: b.ne            #0xe762bc
    // 0xe762b4: d0 = 0.000000
    //     0xe762b4: eor             v0.16b, v0.16b, v0.16b
    // 0xe762b8: b               #0xe762d0
    // 0xe762bc: fcmp            d3, d1
    // 0xe762c0: b.le            #0xe762cc
    // 0xe762c4: fneg            d0, d1
    // 0xe762c8: b               #0xe762d0
    // 0xe762cc: mov             v0.16b, v1.16b
    // 0xe762d0: d8 = 60.000000
    //     0xe762d0: ldr             d8, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0xe762d4: fsub            d1, d4, d0
    // 0xe762d8: fmul            d9, d1, d6
    // 0xe762dc: stur            d9, [fp, #-0x20]
    // 0xe762e0: fdiv            d0, d7, d8
    // 0xe762e4: mov             v1.16b, v5.16b
    // 0xe762e8: stp             fp, lr, [SP, #-0x10]!
    // 0xe762ec: mov             fp, SP
    // 0xe762f0: CallRuntime_DartModulo(double, double) -> double
    //     0xe762f0: and             SP, SP, #0xfffffffffffffff0
    //     0xe762f4: mov             sp, SP
    //     0xe762f8: ldr             x16, [THR, #0x570]  ; THR::DartModulo
    //     0xe762fc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe76300: blr             x16
    //     0xe76304: movz            x16, #0x8
    //     0xe76308: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7630c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xe76310: sub             sp, x16, #1, lsl #12
    //     0xe76314: mov             SP, fp
    //     0xe76318: ldp             fp, lr, [SP], #0x10
    // 0xe7631c: mov             v1.16b, v0.16b
    // 0xe76320: d0 = 1.000000
    //     0xe76320: fmov            d0, #1.00000000
    // 0xe76324: fsub            d2, d1, d0
    // 0xe76328: d1 = 0.000000
    //     0xe76328: eor             v1.16b, v1.16b, v1.16b
    // 0xe7632c: fcmp            d2, d1
    // 0xe76330: b.ne            #0xe7633c
    // 0xe76334: d6 = 0.000000
    //     0xe76334: eor             v6.16b, v6.16b, v6.16b
    // 0xe76338: b               #0xe76354
    // 0xe7633c: fcmp            d1, d2
    // 0xe76340: b.le            #0xe7634c
    // 0xe76344: fneg            d1, d2
    // 0xe76348: b               #0xe76350
    // 0xe7634c: mov             v1.16b, v2.16b
    // 0xe76350: mov             v6.16b, v1.16b
    // 0xe76354: ldur            d3, [fp, #-0x28]
    // 0xe76358: ldur            d2, [fp, #-0x38]
    // 0xe7635c: ldur            d5, [fp, #-0x20]
    // 0xe76360: d1 = 2.000000
    //     0xe76360: fmov            d1, #2.00000000
    // 0xe76364: d4 = 60.000000
    //     0xe76364: ldr             d4, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0xe76368: fsub            d7, d0, d6
    // 0xe7636c: fmul            d6, d5, d7
    // 0xe76370: fdiv            d7, d5, d1
    // 0xe76374: fsub            d1, d2, d7
    // 0xe76378: stur            d1, [fp, #-0x48]
    // 0xe7637c: fcmp            d4, d3
    // 0xe76380: b.le            #0xe76390
    // 0xe76384: mov             v7.16b, v5.16b
    // 0xe76388: d5 = 0.000000
    //     0xe76388: eor             v5.16b, v5.16b, v5.16b
    // 0xe7638c: b               #0xe76414
    // 0xe76390: d4 = 120.000000
    //     0xe76390: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3ed58] IMM: double(120) from 0x405e000000000000
    //     0xe76394: ldr             d4, [x17, #0xd58]
    // 0xe76398: fcmp            d4, d3
    // 0xe7639c: b.le            #0xe763a8
    // 0xe763a0: d4 = 0.000000
    //     0xe763a0: eor             v4.16b, v4.16b, v4.16b
    // 0xe763a4: b               #0xe76408
    // 0xe763a8: d4 = 180.000000
    //     0xe763a8: ldr             d4, [PP, #0x5a50]  ; [pp+0x5a50] IMM: double(180) from 0x4066800000000000
    // 0xe763ac: fcmp            d4, d3
    // 0xe763b0: b.le            #0xe763c0
    // 0xe763b4: mov             v4.16b, v6.16b
    // 0xe763b8: d6 = 0.000000
    //     0xe763b8: eor             v6.16b, v6.16b, v6.16b
    // 0xe763bc: b               #0xe76408
    // 0xe763c0: d4 = 240.000000
    //     0xe763c0: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3ed60] IMM: double(240) from 0x406e000000000000
    //     0xe763c4: ldr             d4, [x17, #0xd60]
    // 0xe763c8: fcmp            d4, d3
    // 0xe763cc: b.le            #0xe763e0
    // 0xe763d0: mov             v4.16b, v5.16b
    // 0xe763d4: mov             v5.16b, v6.16b
    // 0xe763d8: d6 = 0.000000
    //     0xe763d8: eor             v6.16b, v6.16b, v6.16b
    // 0xe763dc: b               #0xe76408
    // 0xe763e0: d4 = 300.000000
    //     0xe763e0: add             x17, PP, #0x29, lsl #12  ; [pp+0x29d58] IMM: double(300) from 0x4072c00000000000
    //     0xe763e4: ldr             d4, [x17, #0xd58]
    // 0xe763e8: fcmp            d4, d3
    // 0xe763ec: b.le            #0xe763fc
    // 0xe763f0: mov             v4.16b, v5.16b
    // 0xe763f4: mov             v5.16b, v6.16b
    // 0xe763f8: b               #0xe76400
    // 0xe763fc: mov             v4.16b, v6.16b
    // 0xe76400: mov             v6.16b, v5.16b
    // 0xe76404: d5 = 0.000000
    //     0xe76404: eor             v5.16b, v5.16b, v5.16b
    // 0xe76408: mov             v7.16b, v6.16b
    // 0xe7640c: mov             v6.16b, v5.16b
    // 0xe76410: mov             v5.16b, v4.16b
    // 0xe76414: ldur            d4, [fp, #-0x30]
    // 0xe76418: stur            d6, [fp, #-0x20]
    // 0xe7641c: stur            d5, [fp, #-0x40]
    // 0xe76420: fadd            d8, d7, d1
    // 0xe76424: r1 = inline_Allocate_Double()
    //     0xe76424: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe76428: add             x1, x1, #0x10
    //     0xe7642c: cmp             x0, x1
    //     0xe76430: b.ls            #0xe76540
    //     0xe76434: str             x1, [THR, #0x50]  ; THR::top
    //     0xe76438: sub             x1, x1, #0xf
    //     0xe7643c: movz            x0, #0xe15c
    //     0xe76440: movk            x0, #0x3, lsl #16
    //     0xe76444: stur            x0, [x1, #-1]
    // 0xe76448: StoreField: r1->field_7 = d8
    //     0xe76448: stur            d8, [x1, #7]
    // 0xe7644c: r2 = 0.000000
    //     0xe7644c: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe76450: r3 = 1.000000
    //     0xe76450: ldr             x3, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xe76454: r0 = clamp()
    //     0xe76454: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe76458: ldur            d0, [fp, #-0x48]
    // 0xe7645c: ldur            d1, [fp, #-0x20]
    // 0xe76460: stur            x0, [fp, #-8]
    // 0xe76464: fadd            d2, d1, d0
    // 0xe76468: r1 = inline_Allocate_Double()
    //     0xe76468: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe7646c: add             x1, x1, #0x10
    //     0xe76470: cmp             x2, x1
    //     0xe76474: b.ls            #0xe7656c
    //     0xe76478: str             x1, [THR, #0x50]  ; THR::top
    //     0xe7647c: sub             x1, x1, #0xf
    //     0xe76480: movz            x2, #0xe15c
    //     0xe76484: movk            x2, #0x3, lsl #16
    //     0xe76488: stur            x2, [x1, #-1]
    // 0xe7648c: StoreField: r1->field_7 = d2
    //     0xe7648c: stur            d2, [x1, #7]
    // 0xe76490: r2 = 0.000000
    //     0xe76490: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe76494: r3 = 1.000000
    //     0xe76494: ldr             x3, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xe76498: r0 = clamp()
    //     0xe76498: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe7649c: ldur            d0, [fp, #-0x48]
    // 0xe764a0: ldur            d1, [fp, #-0x40]
    // 0xe764a4: stur            x0, [fp, #-0x10]
    // 0xe764a8: fadd            d2, d1, d0
    // 0xe764ac: r1 = inline_Allocate_Double()
    //     0xe764ac: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe764b0: add             x1, x1, #0x10
    //     0xe764b4: cmp             x2, x1
    //     0xe764b8: b.ls            #0xe76588
    //     0xe764bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xe764c0: sub             x1, x1, #0xf
    //     0xe764c4: movz            x2, #0xe15c
    //     0xe764c8: movk            x2, #0x3, lsl #16
    //     0xe764cc: stur            x2, [x1, #-1]
    // 0xe764d0: StoreField: r1->field_7 = d2
    //     0xe764d0: stur            d2, [x1, #7]
    // 0xe764d4: r2 = 0.000000
    //     0xe764d4: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe764d8: r3 = 1.000000
    //     0xe764d8: ldr             x3, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xe764dc: r0 = clamp()
    //     0xe764dc: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe764e0: stur            x0, [fp, #-0x18]
    // 0xe764e4: r0 = PdfColorHsl()
    //     0xe764e4: bl              #0xe765a4  ; AllocatePdfColorHslStub -> PdfColorHsl (size=0x40)
    // 0xe764e8: ldur            d0, [fp, #-0x28]
    // 0xe764ec: StoreField: r0->field_27 = d0
    //     0xe764ec: stur            d0, [x0, #0x27]
    // 0xe764f0: ldur            d0, [fp, #-0x30]
    // 0xe764f4: StoreField: r0->field_2f = d0
    //     0xe764f4: stur            d0, [x0, #0x2f]
    // 0xe764f8: ldur            d0, [fp, #-0x38]
    // 0xe764fc: StoreField: r0->field_37 = d0
    //     0xe764fc: stur            d0, [x0, #0x37]
    // 0xe76500: ldur            x1, [fp, #-8]
    // 0xe76504: LoadField: d0 = r1->field_7
    //     0xe76504: ldur            d0, [x1, #7]
    // 0xe76508: StoreField: r0->field_f = d0
    //     0xe76508: stur            d0, [x0, #0xf]
    // 0xe7650c: ldur            x1, [fp, #-0x10]
    // 0xe76510: LoadField: d0 = r1->field_7
    //     0xe76510: ldur            d0, [x1, #7]
    // 0xe76514: ArrayStore: r0[0] = d0  ; List_8
    //     0xe76514: stur            d0, [x0, #0x17]
    // 0xe76518: ldur            x1, [fp, #-0x18]
    // 0xe7651c: LoadField: d0 = r1->field_7
    //     0xe7651c: ldur            d0, [x1, #7]
    // 0xe76520: StoreField: r0->field_1f = d0
    //     0xe76520: stur            d0, [x0, #0x1f]
    // 0xe76524: d0 = 1.000000
    //     0xe76524: fmov            d0, #1.00000000
    // 0xe76528: StoreField: r0->field_7 = d0
    //     0xe76528: stur            d0, [x0, #7]
    // 0xe7652c: LeaveFrame
    //     0xe7652c: mov             SP, fp
    //     0xe76530: ldp             fp, lr, [SP], #0x10
    // 0xe76534: ret
    //     0xe76534: ret             
    // 0xe76538: r0 = StackOverflowSharedWithFPURegs()
    //     0xe76538: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe7653c: b               #0xe762a4
    // 0xe76540: stp             q6, q8, [SP, #-0x20]!
    // 0xe76544: stp             q4, q5, [SP, #-0x20]!
    // 0xe76548: stp             q2, q3, [SP, #-0x20]!
    // 0xe7654c: stp             q0, q1, [SP, #-0x20]!
    // 0xe76550: r0 = AllocateDouble()
    //     0xe76550: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76554: mov             x1, x0
    // 0xe76558: ldp             q0, q1, [SP], #0x20
    // 0xe7655c: ldp             q2, q3, [SP], #0x20
    // 0xe76560: ldp             q4, q5, [SP], #0x20
    // 0xe76564: ldp             q6, q8, [SP], #0x20
    // 0xe76568: b               #0xe76448
    // 0xe7656c: stp             q0, q2, [SP, #-0x20]!
    // 0xe76570: SaveReg r0
    //     0xe76570: str             x0, [SP, #-8]!
    // 0xe76574: r0 = AllocateDouble()
    //     0xe76574: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76578: mov             x1, x0
    // 0xe7657c: RestoreReg r0
    //     0xe7657c: ldr             x0, [SP], #8
    // 0xe76580: ldp             q0, q2, [SP], #0x20
    // 0xe76584: b               #0xe7648c
    // 0xe76588: SaveReg d2
    //     0xe76588: str             q2, [SP, #-0x10]!
    // 0xe7658c: SaveReg r0
    //     0xe7658c: str             x0, [SP, #-8]!
    // 0xe76590: r0 = AllocateDouble()
    //     0xe76590: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76594: mov             x1, x0
    // 0xe76598: RestoreReg r0
    //     0xe76598: ldr             x0, [SP], #8
    // 0xe7659c: RestoreReg d2
    //     0xe7659c: ldr             q2, [SP], #0x10
    // 0xe765a0: b               #0xe764d0
  }
}
