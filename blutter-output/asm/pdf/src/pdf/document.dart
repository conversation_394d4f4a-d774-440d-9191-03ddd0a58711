// lib: , url: package:pdf/src/pdf/document.dart

// class id: 1050772, size: 0x8
class :: {
}

// class id: 923, size: 0x38, field offset: 0x8
class PdfDocument extends Object {

  late final PdfCatalog catalog; // offset: 0x18
  late final PdfSettings settings; // offset: 0x1c

  get _ graphicStates(/* No info */) {
    // ** addr: 0x7cb36c, size: 0x88
    // 0x7cb36c: EnterFrame
    //     0x7cb36c: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb370: mov             fp, SP
    // 0x7cb374: AllocStack(0x10)
    //     0x7cb374: sub             SP, SP, #0x10
    // 0x7cb378: SetupParameters(PdfDocument this /* r1 => r2, fp-0x8 */)
    //     0x7cb378: mov             x2, x1
    //     0x7cb37c: stur            x1, [fp, #-8]
    // 0x7cb380: CheckStackOverflow
    //     0x7cb380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb384: cmp             SP, x16
    //     0x7cb388: b.ls            #0x7cb3ec
    // 0x7cb38c: LoadField: r0 = r2->field_27
    //     0x7cb38c: ldur            w0, [x2, #0x27]
    // 0x7cb390: DecompressPointer r0
    //     0x7cb390: add             x0, x0, HEAP, lsl #32
    // 0x7cb394: cmp             w0, NULL
    // 0x7cb398: b.ne            #0x7cb3e0
    // 0x7cb39c: r1 = <PdfDict<PdfDataType>>
    //     0x7cb39c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0x7cb3a0: ldr             x1, [x1, #0x758]
    // 0x7cb3a4: r0 = PdfGraphicStates()
    //     0x7cb3a4: bl              #0x7cb5ac  ; AllocatePdfGraphicStatesStub -> PdfGraphicStates (size=0x30)
    // 0x7cb3a8: mov             x1, x0
    // 0x7cb3ac: ldur            x2, [fp, #-8]
    // 0x7cb3b0: stur            x0, [fp, #-0x10]
    // 0x7cb3b4: r0 = PdfGraphicStates()
    //     0x7cb3b4: bl              #0x7cb3f4  ; [package:pdf/src/pdf/graphic_state.dart] PdfGraphicStates::PdfGraphicStates
    // 0x7cb3b8: ldur            x0, [fp, #-0x10]
    // 0x7cb3bc: ldur            x1, [fp, #-8]
    // 0x7cb3c0: StoreField: r1->field_27 = r0
    //     0x7cb3c0: stur            w0, [x1, #0x27]
    //     0x7cb3c4: ldurb           w16, [x1, #-1]
    //     0x7cb3c8: ldurb           w17, [x0, #-1]
    //     0x7cb3cc: and             x16, x17, x16, lsr #2
    //     0x7cb3d0: tst             x16, HEAP, lsr #32
    //     0x7cb3d4: b.eq            #0x7cb3dc
    //     0x7cb3d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7cb3dc: ldur            x0, [fp, #-0x10]
    // 0x7cb3e0: LeaveFrame
    //     0x7cb3e0: mov             SP, fp
    //     0x7cb3e4: ldp             fp, lr, [SP], #0x10
    // 0x7cb3e8: ret
    //     0x7cb3e8: ret             
    // 0x7cb3ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb3ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb3f0: b               #0x7cb38c
  }
  _ genSerial(/* No info */) {
    // ** addr: 0x7cb59c, size: 0x10
    // 0x7cb59c: LoadField: r0 = r1->field_b
    //     0x7cb59c: ldur            x0, [x1, #0xb]
    // 0x7cb5a0: add             x2, x0, #1
    // 0x7cb5a4: StoreField: r1->field_b = r2
    //     0x7cb5a4: stur            x2, [x1, #0xb]
    // 0x7cb5a8: ret
    //     0x7cb5a8: ret             
  }
  _ save(/* No info */) async {
    // ** addr: 0xe88758, size: 0x6c
    // 0xe88758: EnterFrame
    //     0xe88758: stp             fp, lr, [SP, #-0x10]!
    //     0xe8875c: mov             fp, SP
    // 0xe88760: AllocStack(0x28)
    //     0xe88760: sub             SP, SP, #0x28
    // 0xe88764: SetupParameters(PdfDocument this /* r1 => r1, fp-0x10 */)
    //     0xe88764: stur            NULL, [fp, #-8]
    //     0xe88768: stur            x1, [fp, #-0x10]
    // 0xe8876c: CheckStackOverflow
    //     0xe8876c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88770: cmp             SP, x16
    //     0xe88774: b.ls            #0xe887bc
    // 0xe88778: r1 = 1
    //     0xe88778: movz            x1, #0x1
    // 0xe8877c: r0 = AllocateContext()
    //     0xe8877c: bl              #0xec126c  ; AllocateContextStub
    // 0xe88780: mov             x1, x0
    // 0xe88784: ldur            x0, [fp, #-0x10]
    // 0xe88788: stur            x1, [fp, #-0x18]
    // 0xe8878c: StoreField: r1->field_f = r0
    //     0xe8878c: stur            w0, [x1, #0xf]
    // 0xe88790: InitAsync() -> Future<Uint8List>
    //     0xe88790: ldr             x0, [PP, #0xf80]  ; [pp+0xf80] TypeArguments: <Uint8List>
    //     0xe88794: bl              #0x661298  ; InitAsyncStub
    // 0xe88798: ldur            x2, [fp, #-0x18]
    // 0xe8879c: r1 = Function '<anonymous closure>':.
    //     0xe8879c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36578] AnonymousClosure: (0xe88890), in [package:pdf/src/pdf/document.dart] PdfDocument::save (0xe88758)
    //     0xe887a0: ldr             x1, [x1, #0x578]
    // 0xe887a4: r0 = AllocateClosure()
    //     0xe887a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe887a8: r16 = <Uint8List>
    //     0xe887a8: ldr             x16, [PP, #0xf80]  ; [pp+0xf80] TypeArguments: <Uint8List>
    // 0xe887ac: stp             x0, x16, [SP]
    // 0xe887b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe887b0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe887b4: r0 = pdfCompute()
    //     0xe887b4: bl              #0xe887c4  ; [package:pdf/src/pdf/io/vm.dart] ::pdfCompute
    // 0xe887b8: r0 = ReturnAsync()
    //     0xe887b8: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe887bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe887bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe887c0: b               #0xe88778
  }
  [closure] Future<Uint8List> <anonymous closure>(dynamic) async {
    // ** addr: 0xe88890, size: 0x90
    // 0xe88890: EnterFrame
    //     0xe88890: stp             fp, lr, [SP, #-0x10]!
    //     0xe88894: mov             fp, SP
    // 0xe88898: AllocStack(0x20)
    //     0xe88898: sub             SP, SP, #0x20
    // 0xe8889c: SetupParameters(PdfDocument this /* r1 */)
    //     0xe8889c: stur            NULL, [fp, #-8]
    //     0xe888a0: movz            x0, #0
    //     0xe888a4: add             x1, fp, w0, sxtw #2
    //     0xe888a8: ldr             x1, [x1, #0x10]
    //     0xe888ac: ldur            w2, [x1, #0x17]
    //     0xe888b0: add             x2, x2, HEAP, lsl #32
    //     0xe888b4: stur            x2, [fp, #-0x10]
    // 0xe888b8: CheckStackOverflow
    //     0xe888b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe888bc: cmp             SP, x16
    //     0xe888c0: b.ls            #0xe88918
    // 0xe888c4: InitAsync() -> Future<Uint8List>
    //     0xe888c4: ldr             x0, [PP, #0xf80]  ; [pp+0xf80] TypeArguments: <Uint8List>
    //     0xe888c8: bl              #0x661298  ; InitAsyncStub
    // 0xe888cc: r0 = PdfStream()
    //     0xe888cc: bl              #0x7b58f8  ; AllocatePdfStreamStub -> PdfStream (size=0x14)
    // 0xe888d0: stur            x0, [fp, #-0x18]
    // 0xe888d4: StoreField: r0->field_b = rZR
    //     0xe888d4: stur            xzr, [x0, #0xb]
    // 0xe888d8: r4 = 2
    //     0xe888d8: movz            x4, #0x2, lsl #16
    // 0xe888dc: r0 = AllocateUint8Array()
    //     0xe888dc: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe888e0: mov             x1, x0
    // 0xe888e4: ldur            x0, [fp, #-0x18]
    // 0xe888e8: StoreField: r0->field_7 = r1
    //     0xe888e8: stur            w1, [x0, #7]
    // 0xe888ec: ldur            x3, [fp, #-0x10]
    // 0xe888f0: LoadField: r1 = r3->field_f
    //     0xe888f0: ldur            w1, [x3, #0xf]
    // 0xe888f4: DecompressPointer r1
    //     0xe888f4: add             x1, x1, HEAP, lsl #32
    // 0xe888f8: mov             x2, x0
    // 0xe888fc: r0 = _write()
    //     0xe888fc: bl              #0xe88920  ; [package:pdf/src/pdf/document.dart] PdfDocument::_write
    // 0xe88900: mov             x1, x0
    // 0xe88904: stur            x1, [fp, #-0x20]
    // 0xe88908: r0 = Await()
    //     0xe88908: bl              #0x661044  ; AwaitStub
    // 0xe8890c: ldur            x1, [fp, #-0x18]
    // 0xe88910: r0 = output()
    //     0xe88910: bl              #0x7b57e4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::output
    // 0xe88914: r0 = ReturnAsyncNotFuture()
    //     0xe88914: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe88918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88918: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8891c: b               #0xe888c4
  }
  _ _write(/* No info */) async {
    // ** addr: 0xe88920, size: 0x37c
    // 0xe88920: EnterFrame
    //     0xe88920: stp             fp, lr, [SP, #-0x10]!
    //     0xe88924: mov             fp, SP
    // 0xe88928: AllocStack(0x70)
    //     0xe88928: sub             SP, SP, #0x70
    // 0xe8892c: SetupParameters(PdfDocument this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xe8892c: stur            NULL, [fp, #-8]
    //     0xe88930: mov             x3, x2
    //     0xe88934: stur            x1, [fp, #-0x10]
    //     0xe88938: stur            x2, [fp, #-0x18]
    // 0xe8893c: CheckStackOverflow
    //     0xe8893c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88940: cmp             SP, x16
    //     0xe88944: b.ls            #0xe88c78
    // 0xe88948: InitAsync() -> Future<void?>
    //     0xe88948: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe8894c: bl              #0x661298  ; InitAsyncStub
    // 0xe88950: ldur            x1, [fp, #-0x10]
    // 0xe88954: LoadField: r2 = r1->field_b
    //     0xe88954: ldur            x2, [x1, #0xb]
    // 0xe88958: stur            x2, [fp, #-0x20]
    // 0xe8895c: r0 = PdfXrefTable()
    //     0xe8895c: bl              #0xe8901c  ; AllocatePdfXrefTableStub -> PdfXrefTable (size=0x18)
    // 0xe88960: mov             x1, x0
    // 0xe88964: ldur            x2, [fp, #-0x20]
    // 0xe88968: stur            x0, [fp, #-0x28]
    // 0xe8896c: r0 = PdfXrefTable()
    //     0xe8896c: bl              #0xe88efc  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::PdfXrefTable
    // 0xe88970: ldur            x0, [fp, #-0x10]
    // 0xe88974: LoadField: r3 = r0->field_13
    //     0xe88974: ldur            w3, [x0, #0x13]
    // 0xe88978: DecompressPointer r3
    //     0xe88978: add             x3, x3, HEAP, lsl #32
    // 0xe8897c: stur            x3, [fp, #-0x30]
    // 0xe88980: r1 = Function '<anonymous closure>':.
    //     0xe88980: add             x1, PP, #0x36, lsl #12  ; [pp+0x36580] AnonymousClosure: static (0x5fb618), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onBackground (0x5fb628)
    //     0xe88984: ldr             x1, [x1, #0x580]
    // 0xe88988: r2 = Null
    //     0xe88988: mov             x2, NULL
    // 0xe8898c: r0 = AllocateClosure()
    //     0xe8898c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe88990: ldur            x1, [fp, #-0x30]
    // 0xe88994: mov             x2, x0
    // 0xe88998: r0 = where()
    //     0xe88998: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xe8899c: mov             x1, x0
    // 0xe889a0: r0 = iterator()
    //     0xe889a0: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xe889a4: LoadField: r2 = r0->field_b
    //     0xe889a4: ldur            w2, [x0, #0xb]
    // 0xe889a8: DecompressPointer r2
    //     0xe889a8: add             x2, x2, HEAP, lsl #32
    // 0xe889ac: stur            x2, [fp, #-0x58]
    // 0xe889b0: LoadField: r3 = r0->field_f
    //     0xe889b0: ldur            w3, [x0, #0xf]
    // 0xe889b4: DecompressPointer r3
    //     0xe889b4: add             x3, x3, HEAP, lsl #32
    // 0xe889b8: ldur            x4, [fp, #-0x28]
    // 0xe889bc: stur            x3, [fp, #-0x50]
    // 0xe889c0: LoadField: r5 = r4->field_b
    //     0xe889c0: ldur            w5, [x4, #0xb]
    // 0xe889c4: DecompressPointer r5
    //     0xe889c4: add             x5, x5, HEAP, lsl #32
    // 0xe889c8: stur            x5, [fp, #-0x48]
    // 0xe889cc: LoadField: r6 = r5->field_7
    //     0xe889cc: ldur            w6, [x5, #7]
    // 0xe889d0: DecompressPointer r6
    //     0xe889d0: add             x6, x6, HEAP, lsl #32
    // 0xe889d4: stur            x6, [fp, #-0x40]
    // 0xe889d8: LoadField: r7 = r4->field_7
    //     0xe889d8: ldur            w7, [x4, #7]
    // 0xe889dc: DecompressPointer r7
    //     0xe889dc: add             x7, x7, HEAP, lsl #32
    // 0xe889e0: stur            x7, [fp, #-0x38]
    // 0xe889e4: LoadField: r8 = r7->field_b
    //     0xe889e4: ldur            w8, [x7, #0xb]
    // 0xe889e8: DecompressPointer r8
    //     0xe889e8: add             x8, x8, HEAP, lsl #32
    // 0xe889ec: stur            x8, [fp, #-0x30]
    // 0xe889f0: CheckStackOverflow
    //     0xe889f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe889f4: cmp             SP, x16
    //     0xe889f8: b.ls            #0xe88c80
    // 0xe889fc: CheckStackOverflow
    //     0xe889fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88a00: cmp             SP, x16
    //     0xe88a04: b.ls            #0xe88c88
    // 0xe88a08: r0 = LoadClassIdInstr(r2)
    //     0xe88a08: ldur            x0, [x2, #-1]
    //     0xe88a0c: ubfx            x0, x0, #0xc, #0x14
    // 0xe88a10: mov             x1, x2
    // 0xe88a14: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe88a14: movz            x17, #0x292d
    //     0xe88a18: movk            x17, #0x1, lsl #16
    //     0xe88a1c: add             lr, x0, x17
    //     0xe88a20: ldr             lr, [x21, lr, lsl #3]
    //     0xe88a24: blr             lr
    // 0xe88a28: tbnz            w0, #4, #0xe88b98
    // 0xe88a2c: ldur            x2, [fp, #-0x58]
    // 0xe88a30: r0 = LoadClassIdInstr(r2)
    //     0xe88a30: ldur            x0, [x2, #-1]
    //     0xe88a34: ubfx            x0, x0, #0xc, #0x14
    // 0xe88a38: mov             x1, x2
    // 0xe88a3c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe88a3c: movz            x17, #0x384d
    //     0xe88a40: movk            x17, #0x1, lsl #16
    //     0xe88a44: add             lr, x0, x17
    //     0xe88a48: ldr             lr, [x21, lr, lsl #3]
    //     0xe88a4c: blr             lr
    // 0xe88a50: ldur            x16, [fp, #-0x50]
    // 0xe88a54: stp             x0, x16, [SP]
    // 0xe88a58: ldur            x0, [fp, #-0x50]
    // 0xe88a5c: ClosureCall
    //     0xe88a5c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe88a60: ldur            x2, [x0, #0x1f]
    //     0xe88a64: blr             x2
    // 0xe88a68: r16 = true
    //     0xe88a68: add             x16, NULL, #0x20  ; true
    // 0xe88a6c: cmp             w0, w16
    // 0xe88a70: b.eq            #0xe88a94
    // 0xe88a74: ldur            x4, [fp, #-0x28]
    // 0xe88a78: ldur            x5, [fp, #-0x48]
    // 0xe88a7c: ldur            x7, [fp, #-0x38]
    // 0xe88a80: ldur            x6, [fp, #-0x40]
    // 0xe88a84: ldur            x8, [fp, #-0x30]
    // 0xe88a88: ldur            x2, [fp, #-0x58]
    // 0xe88a8c: ldur            x3, [fp, #-0x50]
    // 0xe88a90: b               #0xe889fc
    // 0xe88a94: ldur            x2, [fp, #-0x58]
    // 0xe88a98: r0 = LoadClassIdInstr(r2)
    //     0xe88a98: ldur            x0, [x2, #-1]
    //     0xe88a9c: ubfx            x0, x0, #0xc, #0x14
    // 0xe88aa0: mov             x1, x2
    // 0xe88aa4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe88aa4: movz            x17, #0x384d
    //     0xe88aa8: movk            x17, #0x1, lsl #16
    //     0xe88aac: add             lr, x0, x17
    //     0xe88ab0: ldr             lr, [x21, lr, lsl #3]
    //     0xe88ab4: blr             lr
    // 0xe88ab8: mov             x2, x0
    // 0xe88abc: stur            x2, [fp, #-0x60]
    // 0xe88ac0: r0 = LoadClassIdInstr(r2)
    //     0xe88ac0: ldur            x0, [x2, #-1]
    //     0xe88ac4: ubfx            x0, x0, #0xc, #0x14
    // 0xe88ac8: mov             x1, x2
    // 0xe88acc: r0 = GDT[cid_x0 + 0x10859]()
    //     0xe88acc: movz            x17, #0x859
    //     0xe88ad0: movk            x17, #0x1, lsl #16
    //     0xe88ad4: add             lr, x0, x17
    //     0xe88ad8: ldr             lr, [x21, lr, lsl #3]
    //     0xe88adc: blr             lr
    // 0xe88ae0: ldur            x0, [fp, #-0x60]
    // 0xe88ae4: r1 = 60
    //     0xe88ae4: movz            x1, #0x3c
    // 0xe88ae8: branchIfSmi(r0, 0xe88af4)
    //     0xe88ae8: tbz             w0, #0, #0xe88af4
    // 0xe88aec: r1 = LoadClassIdInstr(r0)
    //     0xe88aec: ldur            x1, [x0, #-1]
    //     0xe88af0: ubfx            x1, x1, #0xc, #0x14
    // 0xe88af4: cmp             x1, #0x372
    // 0xe88af8: b.ne            #0xe88b2c
    // 0xe88afc: LoadField: r1 = r0->field_b
    //     0xe88afc: ldur            x1, [x0, #0xb]
    // 0xe88b00: stur            x1, [fp, #-0x20]
    // 0xe88b04: r0 = PdfIndirect()
    //     0xe88b04: bl              #0x7b5cc0  ; AllocatePdfIndirectStub -> PdfIndirect (size=0x18)
    // 0xe88b08: mov             x1, x0
    // 0xe88b0c: ldur            x0, [fp, #-0x20]
    // 0xe88b10: StoreField: r1->field_7 = r0
    //     0xe88b10: stur            x0, [x1, #7]
    // 0xe88b14: StoreField: r1->field_f = rZR
    //     0xe88b14: stur            xzr, [x1, #0xf]
    // 0xe88b18: mov             x3, x1
    // 0xe88b1c: ldur            x1, [fp, #-0x30]
    // 0xe88b20: r2 = "/Info"
    //     0xe88b20: add             x2, PP, #0x36, lsl #12  ; [pp+0x36588] "/Info"
    //     0xe88b24: ldr             x2, [x2, #0x588]
    // 0xe88b28: r0 = []=()
    //     0xe88b28: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe88b2c: ldur            x0, [fp, #-0x60]
    // 0xe88b30: ldur            x2, [fp, #-0x40]
    // 0xe88b34: r1 = Null
    //     0xe88b34: mov             x1, NULL
    // 0xe88b38: cmp             w2, NULL
    // 0xe88b3c: b.eq            #0xe88b5c
    // 0xe88b40: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe88b40: ldur            w4, [x2, #0x17]
    // 0xe88b44: DecompressPointer r4
    //     0xe88b44: add             x4, x4, HEAP, lsl #32
    // 0xe88b48: r8 = X0
    //     0xe88b48: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe88b4c: LoadField: r9 = r4->field_7
    //     0xe88b4c: ldur            x9, [x4, #7]
    // 0xe88b50: r3 = Null
    //     0xe88b50: add             x3, PP, #0x36, lsl #12  ; [pp+0x36590] Null
    //     0xe88b54: ldr             x3, [x3, #0x590]
    // 0xe88b58: blr             x9
    // 0xe88b5c: ldur            x1, [fp, #-0x48]
    // 0xe88b60: ldur            x2, [fp, #-0x60]
    // 0xe88b64: r0 = _hashCode()
    //     0xe88b64: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xe88b68: ldur            x1, [fp, #-0x48]
    // 0xe88b6c: ldur            x2, [fp, #-0x60]
    // 0xe88b70: mov             x3, x0
    // 0xe88b74: r0 = _add()
    //     0xe88b74: bl              #0x69b44c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0xe88b78: ldur            x4, [fp, #-0x28]
    // 0xe88b7c: ldur            x5, [fp, #-0x48]
    // 0xe88b80: ldur            x7, [fp, #-0x38]
    // 0xe88b84: ldur            x6, [fp, #-0x40]
    // 0xe88b88: ldur            x8, [fp, #-0x30]
    // 0xe88b8c: ldur            x2, [fp, #-0x58]
    // 0xe88b90: ldur            x3, [fp, #-0x50]
    // 0xe88b94: b               #0xe889f0
    // 0xe88b98: ldur            x0, [fp, #-0x10]
    // 0xe88b9c: mov             x1, x0
    // 0xe88ba0: r0 = documentID()
    //     0xe88ba0: bl              #0xe88c9c  ; [package:pdf/src/pdf/document.dart] PdfDocument::documentID
    // 0xe88ba4: stur            x0, [fp, #-0x30]
    // 0xe88ba8: r0 = PdfString()
    //     0xe88ba8: bl              #0x7b8c74  ; AllocatePdfStringStub -> PdfString (size=0x14)
    // 0xe88bac: mov             x3, x0
    // 0xe88bb0: ldur            x0, [fp, #-0x30]
    // 0xe88bb4: stur            x3, [fp, #-0x40]
    // 0xe88bb8: StoreField: r3->field_7 = r0
    //     0xe88bb8: stur            w0, [x3, #7]
    // 0xe88bbc: r0 = Instance_PdfStringFormat
    //     0xe88bbc: add             x0, PP, #0x36, lsl #12  ; [pp+0x365a0] Obj!PdfStringFormat@e2f061
    //     0xe88bc0: ldr             x0, [x0, #0x5a0]
    // 0xe88bc4: StoreField: r3->field_b = r0
    //     0xe88bc4: stur            w0, [x3, #0xb]
    // 0xe88bc8: r0 = false
    //     0xe88bc8: add             x0, NULL, #0x30  ; false
    // 0xe88bcc: StoreField: r3->field_f = r0
    //     0xe88bcc: stur            w0, [x3, #0xf]
    // 0xe88bd0: r1 = Null
    //     0xe88bd0: mov             x1, NULL
    // 0xe88bd4: r2 = 4
    //     0xe88bd4: movz            x2, #0x4
    // 0xe88bd8: r0 = AllocateArray()
    //     0xe88bd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe88bdc: mov             x2, x0
    // 0xe88be0: ldur            x0, [fp, #-0x40]
    // 0xe88be4: stur            x2, [fp, #-0x30]
    // 0xe88be8: StoreField: r2->field_f = r0
    //     0xe88be8: stur            w0, [x2, #0xf]
    // 0xe88bec: StoreField: r2->field_13 = r0
    //     0xe88bec: stur            w0, [x2, #0x13]
    // 0xe88bf0: r1 = <PdfString>
    //     0xe88bf0: add             x1, PP, #0x36, lsl #12  ; [pp+0x365a8] TypeArguments: <PdfString>
    //     0xe88bf4: ldr             x1, [x1, #0x5a8]
    // 0xe88bf8: r0 = AllocateGrowableArray()
    //     0xe88bf8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe88bfc: mov             x2, x0
    // 0xe88c00: ldur            x0, [fp, #-0x30]
    // 0xe88c04: stur            x2, [fp, #-0x40]
    // 0xe88c08: StoreField: r2->field_f = r0
    //     0xe88c08: stur            w0, [x2, #0xf]
    // 0xe88c0c: r0 = 4
    //     0xe88c0c: movz            x0, #0x4
    // 0xe88c10: StoreField: r2->field_b = r0
    //     0xe88c10: stur            w0, [x2, #0xb]
    // 0xe88c14: r1 = <PdfString>
    //     0xe88c14: add             x1, PP, #0x36, lsl #12  ; [pp+0x365a8] TypeArguments: <PdfString>
    //     0xe88c18: ldr             x1, [x1, #0x5a8]
    // 0xe88c1c: r0 = PdfArray()
    //     0xe88c1c: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0xe88c20: stur            x0, [fp, #-0x30]
    // 0xe88c24: ldur            x16, [fp, #-0x40]
    // 0xe88c28: str             x16, [SP]
    // 0xe88c2c: mov             x1, x0
    // 0xe88c30: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe88c30: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe88c34: r0 = PdfArray()
    //     0xe88c34: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0xe88c38: ldur            x1, [fp, #-0x38]
    // 0xe88c3c: ldur            x3, [fp, #-0x30]
    // 0xe88c40: r2 = "/ID"
    //     0xe88c40: add             x2, PP, #0x36, lsl #12  ; [pp+0x365b0] "/ID"
    //     0xe88c44: ldr             x2, [x2, #0x5b0]
    // 0xe88c48: r0 = []=()
    //     0xe88c48: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe88c4c: ldur            x0, [fp, #-0x10]
    // 0xe88c50: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe88c50: ldur            w2, [x0, #0x17]
    // 0xe88c54: DecompressPointer r2
    //     0xe88c54: add             x2, x2, HEAP, lsl #32
    // 0xe88c58: r16 = Sentinel
    //     0xe88c58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe88c5c: cmp             w2, w16
    // 0xe88c60: b.eq            #0xe88c90
    // 0xe88c64: ldur            x1, [fp, #-0x28]
    // 0xe88c68: ldur            x3, [fp, #-0x18]
    // 0xe88c6c: r0 = output()
    //     0xe88c6c: bl              #0xe7fe94  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::output
    // 0xe88c70: r0 = Null
    //     0xe88c70: mov             x0, NULL
    // 0xe88c74: r0 = ReturnAsyncNotFuture()
    //     0xe88c74: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe88c78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88c78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88c7c: b               #0xe88948
    // 0xe88c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88c84: b               #0xe889fc
    // 0xe88c88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88c88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88c8c: b               #0xe88a08
    // 0xe88c90: r9 = catalog
    //     0xe88c90: add             x9, PP, #0x36, lsl #12  ; [pp+0x365b8] Field <PdfDocument.catalog>: late final (offset: 0x18)
    //     0xe88c94: ldr             x9, [x9, #0x5b8]
    // 0xe88c98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe88c98: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ documentID(/* No info */) {
    // ** addr: 0xe88c9c, size: 0x1d8
    // 0xe88c9c: EnterFrame
    //     0xe88c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xe88ca0: mov             fp, SP
    // 0xe88ca4: AllocStack(0x28)
    //     0xe88ca4: sub             SP, SP, #0x28
    // 0xe88ca8: SetupParameters(PdfDocument this /* r1 => r1, fp-0x8 */)
    //     0xe88ca8: stur            x1, [fp, #-8]
    // 0xe88cac: CheckStackOverflow
    //     0xe88cac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88cb0: cmp             SP, x16
    //     0xe88cb4: b.ls            #0xe88e60
    // 0xe88cb8: LoadField: r0 = r1->field_33
    //     0xe88cb8: ldur            w0, [x1, #0x33]
    // 0xe88cbc: DecompressPointer r0
    //     0xe88cbc: add             x0, x0, HEAP, lsl #32
    // 0xe88cc0: cmp             w0, NULL
    // 0xe88cc4: b.ne            #0xe88e54
    // 0xe88cc8: r0 = InitLateStaticField(0x42c) // [dart:math] Random::_secureRandom
    //     0xe88cc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe88ccc: ldr             x0, [x0, #0x858]
    //     0xe88cd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe88cd4: cmp             w0, w16
    //     0xe88cd8: b.ne            #0xe88ce8
    //     0xe88cdc: add             x2, PP, #0xb, lsl #12  ; [pp+0xbdc8] Field <Random._secureRandom@12383281>: static late final (offset: 0x42c)
    //     0xe88ce0: ldr             x2, [x2, #0xdc8]
    //     0xe88ce4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe88ce8: stur            x0, [fp, #-0x10]
    // 0xe88cec: r0 = DateTime()
    //     0xe88cec: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xe88cf0: mov             x1, x0
    // 0xe88cf4: r0 = false
    //     0xe88cf4: add             x0, NULL, #0x30  ; false
    // 0xe88cf8: stur            x1, [fp, #-0x18]
    // 0xe88cfc: StoreField: r1->field_13 = r0
    //     0xe88cfc: stur            w0, [x1, #0x13]
    // 0xe88d00: r0 = _getCurrentMicros()
    //     0xe88d00: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xe88d04: r1 = LoadInt32Instr(r0)
    //     0xe88d04: sbfx            x1, x0, #1, #0x1f
    //     0xe88d08: tbz             w0, #0, #0xe88d10
    //     0xe88d0c: ldur            x1, [x0, #7]
    // 0xe88d10: ldur            x0, [fp, #-0x18]
    // 0xe88d14: StoreField: r0->field_7 = r1
    //     0xe88d14: stur            x1, [x0, #7]
    // 0xe88d18: mov             x1, x0
    // 0xe88d1c: r0 = toIso8601String()
    //     0xe88d1c: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0xe88d20: r1 = <int>
    //     0xe88d20: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe88d24: stur            x0, [fp, #-0x18]
    // 0xe88d28: r0 = CodeUnits()
    //     0xe88d28: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0xe88d2c: mov             x3, x0
    // 0xe88d30: ldur            x0, [fp, #-0x18]
    // 0xe88d34: stur            x3, [fp, #-0x20]
    // 0xe88d38: StoreField: r3->field_b = r0
    //     0xe88d38: stur            w0, [x3, #0xb]
    // 0xe88d3c: r1 = <int>
    //     0xe88d3c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe88d40: r2 = 32
    //     0xe88d40: movz            x2, #0x20
    // 0xe88d44: r0 = _GrowableList()
    //     0xe88d44: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe88d48: stur            x0, [fp, #-0x18]
    // 0xe88d4c: r3 = 0
    //     0xe88d4c: movz            x3, #0
    // 0xe88d50: stur            x3, [fp, #-0x28]
    // 0xe88d54: CheckStackOverflow
    //     0xe88d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88d58: cmp             SP, x16
    //     0xe88d5c: b.ls            #0xe88e68
    // 0xe88d60: LoadField: r1 = r0->field_b
    //     0xe88d60: ldur            w1, [x0, #0xb]
    // 0xe88d64: r2 = LoadInt32Instr(r1)
    //     0xe88d64: sbfx            x2, x1, #1, #0x1f
    // 0xe88d68: cmp             x3, x2
    // 0xe88d6c: b.ge            #0xe88dfc
    // 0xe88d70: ldur            x1, [fp, #-0x10]
    // 0xe88d74: r2 = 256
    //     0xe88d74: movz            x2, #0x100
    // 0xe88d78: r0 = nextInt()
    //     0xe88d78: bl              #0xe88e74  ; [dart:math] _SecureRandom::nextInt
    // 0xe88d7c: mov             x2, x0
    // 0xe88d80: r0 = BoxInt64Instr(r2)
    //     0xe88d80: sbfiz           x0, x2, #1, #0x1f
    //     0xe88d84: cmp             x2, x0, asr #1
    //     0xe88d88: b.eq            #0xe88d94
    //     0xe88d8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe88d90: stur            x2, [x0, #7]
    // 0xe88d94: mov             x3, x0
    // 0xe88d98: ldur            x2, [fp, #-0x18]
    // 0xe88d9c: LoadField: r0 = r2->field_b
    //     0xe88d9c: ldur            w0, [x2, #0xb]
    // 0xe88da0: r1 = LoadInt32Instr(r0)
    //     0xe88da0: sbfx            x1, x0, #1, #0x1f
    // 0xe88da4: mov             x0, x1
    // 0xe88da8: ldur            x1, [fp, #-0x28]
    // 0xe88dac: cmp             x1, x0
    // 0xe88db0: b.hs            #0xe88e70
    // 0xe88db4: LoadField: r1 = r2->field_f
    //     0xe88db4: ldur            w1, [x2, #0xf]
    // 0xe88db8: DecompressPointer r1
    //     0xe88db8: add             x1, x1, HEAP, lsl #32
    // 0xe88dbc: mov             x0, x3
    // 0xe88dc0: ldur            x3, [fp, #-0x28]
    // 0xe88dc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe88dc4: add             x25, x1, x3, lsl #2
    //     0xe88dc8: add             x25, x25, #0xf
    //     0xe88dcc: str             w0, [x25]
    //     0xe88dd0: tbz             w0, #0, #0xe88dec
    //     0xe88dd4: ldurb           w16, [x1, #-1]
    //     0xe88dd8: ldurb           w17, [x0, #-1]
    //     0xe88ddc: and             x16, x17, x16, lsr #2
    //     0xe88de0: tst             x16, HEAP, lsr #32
    //     0xe88de4: b.eq            #0xe88dec
    //     0xe88de8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe88dec: add             x0, x3, #1
    // 0xe88df0: mov             x3, x0
    // 0xe88df4: mov             x0, x2
    // 0xe88df8: b               #0xe88d50
    // 0xe88dfc: mov             x2, x0
    // 0xe88e00: ldur            x0, [fp, #-8]
    // 0xe88e04: ldur            x1, [fp, #-0x20]
    // 0xe88e08: r0 = +()
    //     0xe88e08: bl              #0x640a74  ; [dart:collection] ListBase::+
    // 0xe88e0c: mov             x2, x0
    // 0xe88e10: r1 = Instance__Sha256
    //     0xe88e10: add             x1, PP, #0x12, lsl #12  ; [pp+0x12310] Obj!_Sha256@e2cd31
    //     0xe88e14: ldr             x1, [x1, #0x310]
    // 0xe88e18: r0 = convert()
    //     0xe88e18: bl              #0xcf9ca8  ; [package:crypto/src/hash.dart] Hash::convert
    // 0xe88e1c: LoadField: r2 = r0->field_7
    //     0xe88e1c: ldur            w2, [x0, #7]
    // 0xe88e20: DecompressPointer r2
    //     0xe88e20: add             x2, x2, HEAP, lsl #32
    // 0xe88e24: r1 = Null
    //     0xe88e24: mov             x1, NULL
    // 0xe88e28: r0 = Uint8List.fromList()
    //     0xe88e28: bl              #0x6b9248  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0xe88e2c: mov             x2, x0
    // 0xe88e30: ldur            x1, [fp, #-8]
    // 0xe88e34: StoreField: r1->field_33 = r0
    //     0xe88e34: stur            w0, [x1, #0x33]
    //     0xe88e38: ldurb           w16, [x1, #-1]
    //     0xe88e3c: ldurb           w17, [x0, #-1]
    //     0xe88e40: and             x16, x17, x16, lsr #2
    //     0xe88e44: tst             x16, HEAP, lsr #32
    //     0xe88e48: b.eq            #0xe88e50
    //     0xe88e4c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe88e50: mov             x0, x2
    // 0xe88e54: LeaveFrame
    //     0xe88e54: mov             SP, fp
    //     0xe88e58: ldp             fp, lr, [SP], #0x10
    // 0xe88e5c: ret
    //     0xe88e5c: ret             
    // 0xe88e60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88e60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88e64: b               #0xe88cb8
    // 0xe88e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88e68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88e6c: b               #0xe88d60
    // 0xe88e70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe88e70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ PdfDocument(/* No info */) {
    // ** addr: 0xe8b0b0, size: 0x294
    // 0xe8b0b0: EnterFrame
    //     0xe8b0b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b0b4: mov             fp, SP
    // 0xe8b0b8: AllocStack(0x30)
    //     0xe8b0b8: sub             SP, SP, #0x30
    // 0xe8b0bc: SetupParameters(PdfDocument this /* r1 => r2, fp-0x8 */)
    //     0xe8b0bc: mov             x2, x1
    //     0xe8b0c0: stur            x1, [fp, #-8]
    // 0xe8b0c4: CheckStackOverflow
    //     0xe8b0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b0c8: cmp             SP, x16
    //     0xe8b0cc: b.ls            #0xe8b33c
    // 0xe8b0d0: r1 = 1
    //     0xe8b0d0: movz            x1, #0x1
    // 0xe8b0d4: r0 = AllocateContext()
    //     0xe8b0d4: bl              #0xec126c  ; AllocateContextStub
    // 0xe8b0d8: ldur            x2, [fp, #-8]
    // 0xe8b0dc: stur            x0, [fp, #-0x10]
    // 0xe8b0e0: StoreField: r0->field_f = r2
    //     0xe8b0e0: stur            w2, [x0, #0xf]
    // 0xe8b0e4: r1 = Sentinel
    //     0xe8b0e4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8b0e8: ArrayStore: r2[0] = r1  ; List_4
    //     0xe8b0e8: stur            w1, [x2, #0x17]
    // 0xe8b0ec: StoreField: r2->field_1b = r1
    //     0xe8b0ec: stur            w1, [x2, #0x1b]
    // 0xe8b0f0: r1 = "1.7"
    //     0xe8b0f0: add             x1, PP, #0x36, lsl #12  ; [pp+0x368c0] "1.7"
    //     0xe8b0f4: ldr             x1, [x1, #0x8c0]
    // 0xe8b0f8: StoreField: r2->field_2b = r1
    //     0xe8b0f8: stur            w1, [x2, #0x2b]
    // 0xe8b0fc: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xe8b0fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8b100: ldr             x0, [x0, #0x778]
    //     0xe8b104: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8b108: cmp             w0, w16
    //     0xe8b10c: b.ne            #0xe8b118
    //     0xe8b110: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xe8b114: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8b118: r1 = <PdfObject<PdfDataType>>
    //     0xe8b118: add             x1, PP, #0x36, lsl #12  ; [pp+0x36830] TypeArguments: <PdfObject<PdfDataType>>
    //     0xe8b11c: ldr             x1, [x1, #0x830]
    // 0xe8b120: stur            x0, [fp, #-0x18]
    // 0xe8b124: r0 = _Set()
    //     0xe8b124: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xe8b128: mov             x1, x0
    // 0xe8b12c: ldur            x0, [fp, #-0x18]
    // 0xe8b130: stur            x1, [fp, #-0x20]
    // 0xe8b134: StoreField: r1->field_1b = r0
    //     0xe8b134: stur            w0, [x1, #0x1b]
    // 0xe8b138: StoreField: r1->field_b = rZR
    //     0xe8b138: stur            wzr, [x1, #0xb]
    // 0xe8b13c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xe8b13c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8b140: ldr             x0, [x0, #0x780]
    //     0xe8b144: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8b148: cmp             w0, w16
    //     0xe8b14c: b.ne            #0xe8b158
    //     0xe8b150: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xe8b154: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8b158: mov             x2, x0
    // 0xe8b15c: ldur            x0, [fp, #-0x20]
    // 0xe8b160: stur            x2, [fp, #-0x28]
    // 0xe8b164: StoreField: r0->field_f = r2
    //     0xe8b164: stur            w2, [x0, #0xf]
    // 0xe8b168: StoreField: r0->field_13 = rZR
    //     0xe8b168: stur            wzr, [x0, #0x13]
    // 0xe8b16c: ArrayStore: r0[0] = rZR  ; List_4
    //     0xe8b16c: stur            wzr, [x0, #0x17]
    // 0xe8b170: ldur            x3, [fp, #-8]
    // 0xe8b174: StoreField: r3->field_13 = r0
    //     0xe8b174: stur            w0, [x3, #0x13]
    //     0xe8b178: ldurb           w16, [x3, #-1]
    //     0xe8b17c: ldurb           w17, [x0, #-1]
    //     0xe8b180: and             x16, x17, x16, lsr #2
    //     0xe8b184: tst             x16, HEAP, lsr #32
    //     0xe8b188: b.eq            #0xe8b190
    //     0xe8b18c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8b190: r1 = <PdfFont>
    //     0xe8b190: add             x1, PP, #0x36, lsl #12  ; [pp+0x368c8] TypeArguments: <PdfFont>
    //     0xe8b194: ldr             x1, [x1, #0x8c8]
    // 0xe8b198: r0 = _Set()
    //     0xe8b198: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xe8b19c: mov             x1, x0
    // 0xe8b1a0: ldur            x0, [fp, #-0x18]
    // 0xe8b1a4: StoreField: r1->field_1b = r0
    //     0xe8b1a4: stur            w0, [x1, #0x1b]
    // 0xe8b1a8: StoreField: r1->field_b = rZR
    //     0xe8b1a8: stur            wzr, [x1, #0xb]
    // 0xe8b1ac: ldur            x0, [fp, #-0x28]
    // 0xe8b1b0: StoreField: r1->field_f = r0
    //     0xe8b1b0: stur            w0, [x1, #0xf]
    // 0xe8b1b4: StoreField: r1->field_13 = rZR
    //     0xe8b1b4: stur            wzr, [x1, #0x13]
    // 0xe8b1b8: ArrayStore: r1[0] = rZR  ; List_4
    //     0xe8b1b8: stur            wzr, [x1, #0x17]
    // 0xe8b1bc: mov             x0, x1
    // 0xe8b1c0: ldur            x2, [fp, #-8]
    // 0xe8b1c4: StoreField: r2->field_2f = r0
    //     0xe8b1c4: stur            w0, [x2, #0x2f]
    //     0xe8b1c8: ldurb           w16, [x2, #-1]
    //     0xe8b1cc: ldurb           w17, [x0, #-1]
    //     0xe8b1d0: and             x16, x17, x16, lsr #2
    //     0xe8b1d4: tst             x16, HEAP, lsr #32
    //     0xe8b1d8: b.eq            #0xe8b1e0
    //     0xe8b1dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8b1e0: r0 = 1
    //     0xe8b1e0: movz            x0, #0x1
    // 0xe8b1e4: StoreField: r2->field_b = r0
    //     0xe8b1e4: stur            x0, [x2, #0xb]
    // 0xe8b1e8: r0 = InitLateStaticField(0x16e4) // [package:pdf/src/pdf/io/vm.dart] ::defaultDeflate
    //     0xe8b1e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8b1ec: ldr             x0, [x0, #0x2dc8]
    //     0xe8b1f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8b1f4: cmp             w0, w16
    //     0xe8b1f8: b.ne            #0xe8b208
    //     0xe8b1fc: add             x2, PP, #0x36, lsl #12  ; [pp+0x368d0] Field <::.defaultDeflate>: static late (offset: 0x16e4)
    //     0xe8b200: ldr             x2, [x2, #0x8d0]
    //     0xe8b204: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xe8b208: stur            x0, [fp, #-0x18]
    // 0xe8b20c: r0 = PdfSettings()
    //     0xe8b20c: bl              #0xe8b4f8  ; AllocatePdfSettingsStub -> PdfSettings (size=0x18)
    // 0xe8b210: mov             x3, x0
    // 0xe8b214: ldur            x0, [fp, #-0x18]
    // 0xe8b218: stur            x3, [fp, #-0x20]
    // 0xe8b21c: StoreField: r3->field_7 = r0
    //     0xe8b21c: stur            w0, [x3, #7]
    // 0xe8b220: ldur            x2, [fp, #-0x10]
    // 0xe8b224: r1 = Function '<anonymous closure>':.
    //     0xe8b224: add             x1, PP, #0x36, lsl #12  ; [pp+0x368d8] AnonymousClosure: (0x88bacc), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeIntSubset (0x88b538)
    //     0xe8b228: ldr             x1, [x1, #0x8d8]
    // 0xe8b22c: r0 = AllocateClosure()
    //     0xe8b22c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe8b230: mov             x1, x0
    // 0xe8b234: ldur            x0, [fp, #-0x20]
    // 0xe8b238: StoreField: r0->field_b = r1
    //     0xe8b238: stur            w1, [x0, #0xb]
    // 0xe8b23c: r1 = false
    //     0xe8b23c: add             x1, NULL, #0x30  ; false
    // 0xe8b240: StoreField: r0->field_f = r1
    //     0xe8b240: stur            w1, [x0, #0xf]
    // 0xe8b244: r1 = Instance_PdfVersion
    //     0xe8b244: add             x1, PP, #0x36, lsl #12  ; [pp+0x368e0] Obj!PdfVersion@e2f081
    //     0xe8b248: ldr             x1, [x1, #0x8e0]
    // 0xe8b24c: StoreField: r0->field_13 = r1
    //     0xe8b24c: stur            w1, [x0, #0x13]
    // 0xe8b250: ldur            x2, [fp, #-8]
    // 0xe8b254: LoadField: r1 = r2->field_1b
    //     0xe8b254: ldur            w1, [x2, #0x1b]
    // 0xe8b258: DecompressPointer r1
    //     0xe8b258: add             x1, x1, HEAP, lsl #32
    // 0xe8b25c: r16 = Sentinel
    //     0xe8b25c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8b260: cmp             w1, w16
    // 0xe8b264: b.eq            #0xe8b27c
    // 0xe8b268: r16 = "settings"
    //     0xe8b268: add             x16, PP, #0x36, lsl #12  ; [pp+0x368e8] "settings"
    //     0xe8b26c: ldr             x16, [x16, #0x8e8]
    // 0xe8b270: str             x16, [SP]
    // 0xe8b274: r0 = _throwFieldAlreadyInitialized()
    //     0xe8b274: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xe8b278: ldur            x2, [fp, #-8]
    // 0xe8b27c: ldur            x0, [fp, #-0x20]
    // 0xe8b280: StoreField: r2->field_1b = r0
    //     0xe8b280: stur            w0, [x2, #0x1b]
    //     0xe8b284: ldurb           w16, [x2, #-1]
    //     0xe8b288: ldurb           w17, [x0, #-1]
    //     0xe8b28c: and             x16, x17, x16, lsr #2
    //     0xe8b290: tst             x16, HEAP, lsr #32
    //     0xe8b294: b.eq            #0xe8b29c
    //     0xe8b298: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8b29c: r1 = <PdfDict<PdfDataType>>
    //     0xe8b29c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe8b2a0: ldr             x1, [x1, #0x758]
    // 0xe8b2a4: r0 = PdfPageList()
    //     0xe8b2a4: bl              #0xe8b4ec  ; AllocatePdfPageListStub -> PdfPageList (size=0x30)
    // 0xe8b2a8: mov             x1, x0
    // 0xe8b2ac: ldur            x2, [fp, #-8]
    // 0xe8b2b0: stur            x0, [fp, #-0x10]
    // 0xe8b2b4: r0 = PdfPageList()
    //     0xe8b2b4: bl              #0xe8b41c  ; [package:pdf/src/pdf/obj/page_list.dart] PdfPageList::PdfPageList
    // 0xe8b2b8: r1 = <PdfDict<PdfDataType>>
    //     0xe8b2b8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe8b2bc: ldr             x1, [x1, #0x758]
    // 0xe8b2c0: r0 = PdfCatalog()
    //     0xe8b2c0: bl              #0xe8b410  ; AllocatePdfCatalogStub -> PdfCatalog (size=0x4c)
    // 0xe8b2c4: mov             x1, x0
    // 0xe8b2c8: ldur            x2, [fp, #-8]
    // 0xe8b2cc: ldur            x3, [fp, #-0x10]
    // 0xe8b2d0: stur            x0, [fp, #-0x10]
    // 0xe8b2d4: r0 = PdfCatalog()
    //     0xe8b2d4: bl              #0xe8b344  ; [package:pdf/src/pdf/obj/catalog.dart] PdfCatalog::PdfCatalog
    // 0xe8b2d8: ldur            x0, [fp, #-8]
    // 0xe8b2dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe8b2dc: ldur            w1, [x0, #0x17]
    // 0xe8b2e0: DecompressPointer r1
    //     0xe8b2e0: add             x1, x1, HEAP, lsl #32
    // 0xe8b2e4: r16 = Sentinel
    //     0xe8b2e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8b2e8: cmp             w1, w16
    // 0xe8b2ec: b.ne            #0xe8b2f8
    // 0xe8b2f0: mov             x1, x0
    // 0xe8b2f4: b               #0xe8b30c
    // 0xe8b2f8: r16 = "catalog"
    //     0xe8b2f8: add             x16, PP, #0x36, lsl #12  ; [pp+0x368f0] "catalog"
    //     0xe8b2fc: ldr             x16, [x16, #0x8f0]
    // 0xe8b300: str             x16, [SP]
    // 0xe8b304: r0 = _throwFieldAlreadyInitialized()
    //     0xe8b304: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xe8b308: ldur            x1, [fp, #-8]
    // 0xe8b30c: ldur            x0, [fp, #-0x10]
    // 0xe8b310: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8b310: stur            w0, [x1, #0x17]
    //     0xe8b314: ldurb           w16, [x1, #-1]
    //     0xe8b318: ldurb           w17, [x0, #-1]
    //     0xe8b31c: and             x16, x17, x16, lsr #2
    //     0xe8b320: tst             x16, HEAP, lsr #32
    //     0xe8b324: b.eq            #0xe8b32c
    //     0xe8b328: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b32c: r0 = Null
    //     0xe8b32c: mov             x0, NULL
    // 0xe8b330: LeaveFrame
    //     0xe8b330: mov             SP, fp
    //     0xe8b334: ldp             fp, lr, [SP], #0x10
    // 0xe8b338: ret
    //     0xe8b338: ret             
    // 0xe8b33c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b33c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b340: b               #0xe8b0d0
  }
}

// class id: 6821, size: 0x14, field offset: 0x14
enum PdfPageMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d7bc, size: 0x64
    // 0xc4d7bc: EnterFrame
    //     0xc4d7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d7c0: mov             fp, SP
    // 0xc4d7c4: AllocStack(0x10)
    //     0xc4d7c4: sub             SP, SP, #0x10
    // 0xc4d7c8: SetupParameters(PdfPageMode this /* r1 => r0, fp-0x8 */)
    //     0xc4d7c8: mov             x0, x1
    //     0xc4d7cc: stur            x1, [fp, #-8]
    // 0xc4d7d0: CheckStackOverflow
    //     0xc4d7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d7d4: cmp             SP, x16
    //     0xc4d7d8: b.ls            #0xc4d818
    // 0xc4d7dc: r1 = Null
    //     0xc4d7dc: mov             x1, NULL
    // 0xc4d7e0: r2 = 4
    //     0xc4d7e0: movz            x2, #0x4
    // 0xc4d7e4: r0 = AllocateArray()
    //     0xc4d7e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d7e8: r16 = "PdfPageMode."
    //     0xc4d7e8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f048] "PdfPageMode."
    //     0xc4d7ec: ldr             x16, [x16, #0x48]
    // 0xc4d7f0: StoreField: r0->field_f = r16
    //     0xc4d7f0: stur            w16, [x0, #0xf]
    // 0xc4d7f4: ldur            x1, [fp, #-8]
    // 0xc4d7f8: LoadField: r2 = r1->field_f
    //     0xc4d7f8: ldur            w2, [x1, #0xf]
    // 0xc4d7fc: DecompressPointer r2
    //     0xc4d7fc: add             x2, x2, HEAP, lsl #32
    // 0xc4d800: StoreField: r0->field_13 = r2
    //     0xc4d800: stur            w2, [x0, #0x13]
    // 0xc4d804: str             x0, [SP]
    // 0xc4d808: r0 = _interpolate()
    //     0xc4d808: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d80c: LeaveFrame
    //     0xc4d80c: mov             SP, fp
    //     0xc4d810: ldp             fp, lr, [SP], #0x10
    // 0xc4d814: ret
    //     0xc4d814: ret             
    // 0xc4d818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d81c: b               #0xc4d7dc
  }
}
