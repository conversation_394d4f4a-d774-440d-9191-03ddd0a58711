// lib: , url: package:pdf/src/pdf/exif.dart

// class id: 1050774, size: 0x8
class :: {
}

// class id: 921, size: 0x1c, field offset: 0x8
class PdfJpegInfo extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc33f74, size: 0x270
    // 0xc33f74: EnterFrame
    //     0xc33f74: stp             fp, lr, [SP, #-0x10]!
    //     0xc33f78: mov             fp, SP
    // 0xc33f7c: AllocStack(0x10)
    //     0xc33f7c: sub             SP, SP, #0x10
    // 0xc33f80: CheckStackOverflow
    //     0xc33f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc33f84: cmp             SP, x16
    //     0xc33f88: b.ls            #0xc341dc
    // 0xc33f8c: r1 = Null
    //     0xc33f8c: mov             x1, NULL
    // 0xc33f90: r2 = 36
    //     0xc33f90: movz            x2, #0x24
    // 0xc33f94: r0 = AllocateArray()
    //     0xc33f94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc33f98: mov             x2, x0
    // 0xc33f9c: stur            x2, [fp, #-8]
    // 0xc33fa0: r16 = "width: "
    //     0xc33fa0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47860] "width: "
    //     0xc33fa4: ldr             x16, [x16, #0x860]
    // 0xc33fa8: StoreField: r2->field_f = r16
    //     0xc33fa8: stur            w16, [x2, #0xf]
    // 0xc33fac: ldr             x3, [fp, #0x10]
    // 0xc33fb0: LoadField: r0 = r3->field_7
    //     0xc33fb0: ldur            w0, [x3, #7]
    // 0xc33fb4: DecompressPointer r0
    //     0xc33fb4: add             x0, x0, HEAP, lsl #32
    // 0xc33fb8: StoreField: r2->field_13 = r0
    //     0xc33fb8: stur            w0, [x2, #0x13]
    // 0xc33fbc: r16 = " height: "
    //     0xc33fbc: add             x16, PP, #0x47, lsl #12  ; [pp+0x47868] " height: "
    //     0xc33fc0: ldr             x16, [x16, #0x868]
    // 0xc33fc4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc33fc4: stur            w16, [x2, #0x17]
    // 0xc33fc8: LoadField: r4 = r3->field_b
    //     0xc33fc8: ldur            x4, [x3, #0xb]
    // 0xc33fcc: r0 = BoxInt64Instr(r4)
    //     0xc33fcc: sbfiz           x0, x4, #1, #0x1f
    //     0xc33fd0: cmp             x4, x0, asr #1
    //     0xc33fd4: b.eq            #0xc33fe0
    //     0xc33fd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc33fdc: stur            x4, [x0, #7]
    // 0xc33fe0: mov             x1, x2
    // 0xc33fe4: ArrayStore: r1[3] = r0  ; List_4
    //     0xc33fe4: add             x25, x1, #0x1b
    //     0xc33fe8: str             w0, [x25]
    //     0xc33fec: tbz             w0, #0, #0xc34008
    //     0xc33ff0: ldurb           w16, [x1, #-1]
    //     0xc33ff4: ldurb           w17, [x0, #-1]
    //     0xc33ff8: and             x16, x17, x16, lsr #2
    //     0xc33ffc: tst             x16, HEAP, lsr #32
    //     0xc34000: b.eq            #0xc34008
    //     0xc34004: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34008: r16 = "\nexifVersion: "
    //     0xc34008: add             x16, PP, #0x47, lsl #12  ; [pp+0x47870] "\nexifVersion: "
    //     0xc3400c: ldr             x16, [x16, #0x870]
    // 0xc34010: StoreField: r2->field_1f = r16
    //     0xc34010: stur            w16, [x2, #0x1f]
    // 0xc34014: mov             x1, x3
    // 0xc34018: r0 = exifVersion()
    //     0xc34018: bl              #0xc349cc  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::exifVersion
    // 0xc3401c: ldur            x1, [fp, #-8]
    // 0xc34020: ArrayStore: r1[5] = r0  ; List_4
    //     0xc34020: add             x25, x1, #0x23
    //     0xc34024: str             w0, [x25]
    //     0xc34028: tbz             w0, #0, #0xc34044
    //     0xc3402c: ldurb           w16, [x1, #-1]
    //     0xc34030: ldurb           w17, [x0, #-1]
    //     0xc34034: and             x16, x17, x16, lsr #2
    //     0xc34038: tst             x16, HEAP, lsr #32
    //     0xc3403c: b.eq            #0xc34044
    //     0xc34040: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34044: ldur            x0, [fp, #-8]
    // 0xc34048: r16 = " flashpixVersion: "
    //     0xc34048: add             x16, PP, #0x47, lsl #12  ; [pp+0x47878] " flashpixVersion: "
    //     0xc3404c: ldr             x16, [x16, #0x878]
    // 0xc34050: StoreField: r0->field_27 = r16
    //     0xc34050: stur            w16, [x0, #0x27]
    // 0xc34054: ldr             x1, [fp, #0x10]
    // 0xc34058: r0 = flashpixVersion()
    //     0xc34058: bl              #0xc348f0  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::flashpixVersion
    // 0xc3405c: ldur            x1, [fp, #-8]
    // 0xc34060: ArrayStore: r1[7] = r0  ; List_4
    //     0xc34060: add             x25, x1, #0x2b
    //     0xc34064: str             w0, [x25]
    //     0xc34068: tbz             w0, #0, #0xc34084
    //     0xc3406c: ldurb           w16, [x1, #-1]
    //     0xc34070: ldurb           w17, [x0, #-1]
    //     0xc34074: and             x16, x17, x16, lsr #2
    //     0xc34078: tst             x16, HEAP, lsr #32
    //     0xc3407c: b.eq            #0xc34084
    //     0xc34080: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34084: ldur            x0, [fp, #-8]
    // 0xc34088: r16 = "\nxResolution: "
    //     0xc34088: add             x16, PP, #0x47, lsl #12  ; [pp+0x47880] "\nxResolution: "
    //     0xc3408c: ldr             x16, [x16, #0x880]
    // 0xc34090: StoreField: r0->field_2f = r16
    //     0xc34090: stur            w16, [x0, #0x2f]
    // 0xc34094: ldr             x1, [fp, #0x10]
    // 0xc34098: r0 = xResolution()
    //     0xc34098: bl              #0xc34758  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::xResolution
    // 0xc3409c: ldur            x1, [fp, #-8]
    // 0xc340a0: ArrayStore: r1[9] = r0  ; List_4
    //     0xc340a0: add             x25, x1, #0x33
    //     0xc340a4: str             w0, [x25]
    //     0xc340a8: tbz             w0, #0, #0xc340c4
    //     0xc340ac: ldurb           w16, [x1, #-1]
    //     0xc340b0: ldurb           w17, [x0, #-1]
    //     0xc340b4: and             x16, x17, x16, lsr #2
    //     0xc340b8: tst             x16, HEAP, lsr #32
    //     0xc340bc: b.eq            #0xc340c4
    //     0xc340c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc340c4: ldur            x0, [fp, #-8]
    // 0xc340c8: r16 = " yResolution: "
    //     0xc340c8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47888] " yResolution: "
    //     0xc340cc: ldr             x16, [x16, #0x888]
    // 0xc340d0: StoreField: r0->field_37 = r16
    //     0xc340d0: stur            w16, [x0, #0x37]
    // 0xc340d4: ldr             x1, [fp, #0x10]
    // 0xc340d8: r0 = yResolution()
    //     0xc340d8: bl              #0xc345c0  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::yResolution
    // 0xc340dc: ldur            x1, [fp, #-8]
    // 0xc340e0: ArrayStore: r1[11] = r0  ; List_4
    //     0xc340e0: add             x25, x1, #0x3b
    //     0xc340e4: str             w0, [x25]
    //     0xc340e8: tbz             w0, #0, #0xc34104
    //     0xc340ec: ldurb           w16, [x1, #-1]
    //     0xc340f0: ldurb           w17, [x0, #-1]
    //     0xc340f4: and             x16, x17, x16, lsr #2
    //     0xc340f8: tst             x16, HEAP, lsr #32
    //     0xc340fc: b.eq            #0xc34104
    //     0xc34100: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34104: ldur            x0, [fp, #-8]
    // 0xc34108: r16 = "\npixelXDimension: "
    //     0xc34108: add             x16, PP, #0x47, lsl #12  ; [pp+0x47890] "\npixelXDimension: "
    //     0xc3410c: ldr             x16, [x16, #0x890]
    // 0xc34110: StoreField: r0->field_3f = r16
    //     0xc34110: stur            w16, [x0, #0x3f]
    // 0xc34114: ldr             x1, [fp, #0x10]
    // 0xc34118: r0 = pixelXDimension()
    //     0xc34118: bl              #0xc344c0  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::pixelXDimension
    // 0xc3411c: ldur            x1, [fp, #-8]
    // 0xc34120: ArrayStore: r1[13] = r0  ; List_4
    //     0xc34120: add             x25, x1, #0x43
    //     0xc34124: str             w0, [x25]
    //     0xc34128: tbz             w0, #0, #0xc34144
    //     0xc3412c: ldurb           w16, [x1, #-1]
    //     0xc34130: ldurb           w17, [x0, #-1]
    //     0xc34134: and             x16, x17, x16, lsr #2
    //     0xc34138: tst             x16, HEAP, lsr #32
    //     0xc3413c: b.eq            #0xc34144
    //     0xc34140: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34144: ldur            x0, [fp, #-8]
    // 0xc34148: r16 = " pixelYDimension: "
    //     0xc34148: add             x16, PP, #0x47, lsl #12  ; [pp+0x47898] " pixelYDimension: "
    //     0xc3414c: ldr             x16, [x16, #0x898]
    // 0xc34150: StoreField: r0->field_47 = r16
    //     0xc34150: stur            w16, [x0, #0x47]
    // 0xc34154: ldr             x1, [fp, #0x10]
    // 0xc34158: r0 = pixelYDimension()
    //     0xc34158: bl              #0xc343b0  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::pixelYDimension
    // 0xc3415c: ldur            x1, [fp, #-8]
    // 0xc34160: ArrayStore: r1[15] = r0  ; List_4
    //     0xc34160: add             x25, x1, #0x4b
    //     0xc34164: str             w0, [x25]
    //     0xc34168: tbz             w0, #0, #0xc34184
    //     0xc3416c: ldurb           w16, [x1, #-1]
    //     0xc34170: ldurb           w17, [x0, #-1]
    //     0xc34174: and             x16, x17, x16, lsr #2
    //     0xc34178: tst             x16, HEAP, lsr #32
    //     0xc3417c: b.eq            #0xc34184
    //     0xc34180: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34184: ldur            x0, [fp, #-8]
    // 0xc34188: r16 = "\norientation: "
    //     0xc34188: add             x16, PP, #0x47, lsl #12  ; [pp+0x478a0] "\norientation: "
    //     0xc3418c: ldr             x16, [x16, #0x8a0]
    // 0xc34190: StoreField: r0->field_4f = r16
    //     0xc34190: stur            w16, [x0, #0x4f]
    // 0xc34194: ldr             x1, [fp, #0x10]
    // 0xc34198: r0 = orientation()
    //     0xc34198: bl              #0xc341e4  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::orientation
    // 0xc3419c: ldur            x1, [fp, #-8]
    // 0xc341a0: ArrayStore: r1[17] = r0  ; List_4
    //     0xc341a0: add             x25, x1, #0x53
    //     0xc341a4: str             w0, [x25]
    //     0xc341a8: tbz             w0, #0, #0xc341c4
    //     0xc341ac: ldurb           w16, [x1, #-1]
    //     0xc341b0: ldurb           w17, [x0, #-1]
    //     0xc341b4: and             x16, x17, x16, lsr #2
    //     0xc341b8: tst             x16, HEAP, lsr #32
    //     0xc341bc: b.eq            #0xc341c4
    //     0xc341c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc341c4: ldur            x16, [fp, #-8]
    // 0xc341c8: str             x16, [SP]
    // 0xc341cc: r0 = _interpolate()
    //     0xc341cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc341d0: LeaveFrame
    //     0xc341d0: mov             SP, fp
    //     0xc341d4: ldp             fp, lr, [SP], #0x10
    // 0xc341d8: ret
    //     0xc341d8: ret             
    // 0xc341dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc341dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc341e0: b               #0xc33f8c
  }
  get _ orientation(/* No info */) {
    // ** addr: 0xc341e4, size: 0x1a8
    // 0xc341e4: EnterFrame
    //     0xc341e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc341e8: mov             fp, SP
    // 0xc341ec: AllocStack(0x58)
    //     0xc341ec: sub             SP, SP, #0x58
    // 0xc341f0: CheckStackOverflow
    //     0xc341f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc341f4: cmp             SP, x16
    //     0xc341f8: b.ls            #0xc34380
    // 0xc341fc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc341fc: ldur            w0, [x1, #0x17]
    // 0xc34200: DecompressPointer r0
    //     0xc34200: add             x0, x0, HEAP, lsl #32
    // 0xc34204: stur            x0, [fp, #-0x48]
    // 0xc34208: cmp             w0, NULL
    // 0xc3420c: b.eq            #0xc34240
    // 0xc34210: mov             x1, x0
    // 0xc34214: r2 = Instance_PdfExifTag
    //     0xc34214: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e5a8] Obj!PdfExifTag@e2f0a1
    //     0xc34218: ldr             x2, [x2, #0x5a8]
    // 0xc3421c: r0 = _getValueOrData()
    //     0xc3421c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34220: mov             x1, x0
    // 0xc34224: ldur            x0, [fp, #-0x48]
    // 0xc34228: LoadField: r2 = r0->field_f
    //     0xc34228: ldur            w2, [x0, #0xf]
    // 0xc3422c: DecompressPointer r2
    //     0xc3422c: add             x2, x2, HEAP, lsl #32
    // 0xc34230: cmp             w2, w1
    // 0xc34234: b.eq            #0xc34240
    // 0xc34238: cmp             w1, NULL
    // 0xc3423c: b.ne            #0xc34254
    // 0xc34240: r0 = Instance_PdfImageOrientation
    //     0xc34240: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xc34244: ldr             x0, [x0, #0x588]
    // 0xc34248: LeaveFrame
    //     0xc34248: mov             SP, fp
    //     0xc3424c: ldp             fp, lr, [SP], #0x10
    // 0xc34250: ret
    //     0xc34250: ret             
    // 0xc34254: mov             x1, x0
    // 0xc34258: r2 = Instance_PdfExifTag
    //     0xc34258: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e5a8] Obj!PdfExifTag@e2f0a1
    //     0xc3425c: ldr             x2, [x2, #0x5a8]
    // 0xc34260: r0 = _getValueOrData()
    //     0xc34260: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34264: mov             x1, x0
    // 0xc34268: ldur            x0, [fp, #-0x48]
    // 0xc3426c: LoadField: r2 = r0->field_f
    //     0xc3426c: ldur            w2, [x0, #0xf]
    // 0xc34270: DecompressPointer r2
    //     0xc34270: add             x2, x2, HEAP, lsl #32
    // 0xc34274: cmp             w2, w1
    // 0xc34278: b.ne            #0xc34284
    // 0xc3427c: r0 = Null
    //     0xc3427c: mov             x0, NULL
    // 0xc34280: b               #0xc34288
    // 0xc34284: mov             x0, x1
    // 0xc34288: r16 = 2
    //     0xc34288: movz            x16, #0x2
    // 0xc3428c: stp             x16, x0, [SP]
    // 0xc34290: r4 = 0
    //     0xc34290: movz            x4, #0
    // 0xc34294: ldr             x0, [SP, #8]
    // 0xc34298: r16 = UnlinkedCall_0x5f3c08
    //     0xc34298: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5b0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc3429c: add             x16, x16, #0x5b0
    // 0xc342a0: ldp             x5, lr, [x16]
    // 0xc342a4: blr             lr
    // 0xc342a8: mov             x3, x0
    // 0xc342ac: r2 = Null
    //     0xc342ac: mov             x2, NULL
    // 0xc342b0: r1 = Null
    //     0xc342b0: mov             x1, NULL
    // 0xc342b4: stur            x3, [fp, #-0x48]
    // 0xc342b8: branchIfSmi(r0, 0xc342e0)
    //     0xc342b8: tbz             w0, #0, #0xc342e0
    // 0xc342bc: r4 = LoadClassIdInstr(r0)
    //     0xc342bc: ldur            x4, [x0, #-1]
    //     0xc342c0: ubfx            x4, x4, #0xc, #0x14
    // 0xc342c4: sub             x4, x4, #0x3c
    // 0xc342c8: cmp             x4, #1
    // 0xc342cc: b.ls            #0xc342e0
    // 0xc342d0: r8 = int
    //     0xc342d0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xc342d4: r3 = Null
    //     0xc342d4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e5c0] Null
    //     0xc342d8: ldr             x3, [x3, #0x5c0]
    // 0xc342dc: r0 = int()
    //     0xc342dc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xc342e0: ldur            x2, [fp, #-0x48]
    // 0xc342e4: r3 = LoadInt32Instr(r2)
    //     0xc342e4: sbfx            x3, x2, #1, #0x1f
    //     0xc342e8: tbz             w2, #0, #0xc342f0
    //     0xc342ec: ldur            x3, [x2, #7]
    // 0xc342f0: tbnz            x3, #0x3f, #0xc3432c
    // 0xc342f4: cmp             x3, #8
    // 0xc342f8: b.ge            #0xc3432c
    // 0xc342fc: r4 = const [Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation', Instance of 'PdfImageOrientation']
    //     0xc342fc: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e5d0] List<PdfImageOrientation>(8)
    //     0xc34300: ldr             x4, [x4, #0x5d0]
    // 0xc34304: mov             x1, x3
    // 0xc34308: r0 = 8
    //     0xc34308: movz            x0, #0x8
    // 0xc3430c: cmp             x1, x0
    // 0xc34310: b.hs            #0xc34388
    // 0xc34314: ArrayLoad: r0 = r4[r3]  ; Unknown_4
    //     0xc34314: add             x16, x4, x3, lsl #2
    //     0xc34318: ldur            w0, [x16, #0xf]
    // 0xc3431c: DecompressPointer r0
    //     0xc3431c: add             x0, x0, HEAP, lsl #32
    // 0xc34320: LeaveFrame
    //     0xc34320: mov             SP, fp
    //     0xc34324: ldp             fp, lr, [SP], #0x10
    // 0xc34328: ret
    //     0xc34328: ret             
    // 0xc3432c: r0 = Instance_PdfImageOrientation
    //     0xc3432c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xc34330: ldr             x0, [x0, #0x588]
    // 0xc34334: LeaveFrame
    //     0xc34334: mov             SP, fp
    //     0xc34338: ldp             fp, lr, [SP], #0x10
    // 0xc3433c: ret
    //     0xc3433c: ret             
    // 0xc34340: sub             SP, fp, #0x58
    // 0xc34344: r2 = 60
    //     0xc34344: movz            x2, #0x3c
    // 0xc34348: branchIfSmi(r0, 0xc34354)
    //     0xc34348: tbz             w0, #0, #0xc34354
    // 0xc3434c: r2 = LoadClassIdInstr(r0)
    //     0xc3434c: ldur            x2, [x0, #-1]
    //     0xc34350: ubfx            x2, x2, #0xc, #0x14
    // 0xc34354: r17 = -7360
    //     0xc34354: movn            x17, #0x1cbf
    // 0xc34358: add             x16, x2, x17
    // 0xc3435c: cmp             x16, #1
    // 0xc34360: b.hi            #0xc34378
    // 0xc34364: r0 = Instance_PdfImageOrientation
    //     0xc34364: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xc34368: ldr             x0, [x0, #0x588]
    // 0xc3436c: LeaveFrame
    //     0xc3436c: mov             SP, fp
    //     0xc34370: ldp             fp, lr, [SP], #0x10
    // 0xc34374: ret
    //     0xc34374: ret             
    // 0xc34378: r0 = ReThrow()
    //     0xc34378: bl              #0xec048c  ; ReThrowStub
    // 0xc3437c: brk             #0
    // 0xc34380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc34380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc34384: b               #0xc341fc
    // 0xc34388: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc34388: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ pixelYDimension(/* No info */) {
    // ** addr: 0xc343b0, size: 0x110
    // 0xc343b0: EnterFrame
    //     0xc343b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc343b4: mov             fp, SP
    // 0xc343b8: AllocStack(0x10)
    //     0xc343b8: sub             SP, SP, #0x10
    // 0xc343bc: SetupParameters(PdfJpegInfo this /* r1 => r0, fp-0x10 */)
    //     0xc343bc: mov             x0, x1
    //     0xc343c0: stur            x1, [fp, #-0x10]
    // 0xc343c4: CheckStackOverflow
    //     0xc343c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc343c8: cmp             SP, x16
    //     0xc343cc: b.ls            #0xc344b8
    // 0xc343d0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc343d0: ldur            w3, [x0, #0x17]
    // 0xc343d4: DecompressPointer r3
    //     0xc343d4: add             x3, x3, HEAP, lsl #32
    // 0xc343d8: stur            x3, [fp, #-8]
    // 0xc343dc: cmp             w3, NULL
    // 0xc343e0: b.eq            #0xc34418
    // 0xc343e4: mov             x1, x3
    // 0xc343e8: r2 = Instance_PdfExifTag
    //     0xc343e8: add             x2, PP, #0x47, lsl #12  ; [pp+0x478a8] Obj!PdfExifTag@e2f0c1
    //     0xc343ec: ldr             x2, [x2, #0x8a8]
    // 0xc343f0: r0 = _getValueOrData()
    //     0xc343f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc343f4: mov             x1, x0
    // 0xc343f8: ldur            x0, [fp, #-8]
    // 0xc343fc: LoadField: r2 = r0->field_f
    //     0xc343fc: ldur            w2, [x0, #0xf]
    // 0xc34400: DecompressPointer r2
    //     0xc34400: add             x2, x2, HEAP, lsl #32
    // 0xc34404: cmp             w2, w1
    // 0xc34408: b.eq            #0xc34414
    // 0xc3440c: cmp             w1, NULL
    // 0xc34410: b.ne            #0xc34438
    // 0xc34414: ldur            x0, [fp, #-0x10]
    // 0xc34418: LoadField: r2 = r0->field_b
    //     0xc34418: ldur            x2, [x0, #0xb]
    // 0xc3441c: r0 = BoxInt64Instr(r2)
    //     0xc3441c: sbfiz           x0, x2, #1, #0x1f
    //     0xc34420: cmp             x2, x0, asr #1
    //     0xc34424: b.eq            #0xc34430
    //     0xc34428: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3442c: stur            x2, [x0, #7]
    // 0xc34430: mov             x3, x0
    // 0xc34434: b               #0xc34470
    // 0xc34438: mov             x1, x0
    // 0xc3443c: r2 = Instance_PdfExifTag
    //     0xc3443c: add             x2, PP, #0x47, lsl #12  ; [pp+0x478a8] Obj!PdfExifTag@e2f0c1
    //     0xc34440: ldr             x2, [x2, #0x8a8]
    // 0xc34444: r0 = _getValueOrData()
    //     0xc34444: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34448: mov             x1, x0
    // 0xc3444c: ldur            x0, [fp, #-8]
    // 0xc34450: LoadField: r2 = r0->field_f
    //     0xc34450: ldur            w2, [x0, #0xf]
    // 0xc34454: DecompressPointer r2
    //     0xc34454: add             x2, x2, HEAP, lsl #32
    // 0xc34458: cmp             w2, w1
    // 0xc3445c: b.ne            #0xc34468
    // 0xc34460: r0 = Null
    //     0xc34460: mov             x0, NULL
    // 0xc34464: b               #0xc3446c
    // 0xc34468: mov             x0, x1
    // 0xc3446c: mov             x3, x0
    // 0xc34470: mov             x0, x3
    // 0xc34474: stur            x3, [fp, #-8]
    // 0xc34478: r2 = Null
    //     0xc34478: mov             x2, NULL
    // 0xc3447c: r1 = Null
    //     0xc3447c: mov             x1, NULL
    // 0xc34480: branchIfSmi(r0, 0xc344a8)
    //     0xc34480: tbz             w0, #0, #0xc344a8
    // 0xc34484: r4 = LoadClassIdInstr(r0)
    //     0xc34484: ldur            x4, [x0, #-1]
    //     0xc34488: ubfx            x4, x4, #0xc, #0x14
    // 0xc3448c: sub             x4, x4, #0x3c
    // 0xc34490: cmp             x4, #1
    // 0xc34494: b.ls            #0xc344a8
    // 0xc34498: r8 = int?
    //     0xc34498: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xc3449c: r3 = Null
    //     0xc3449c: add             x3, PP, #0x47, lsl #12  ; [pp+0x478b0] Null
    //     0xc344a0: ldr             x3, [x3, #0x8b0]
    // 0xc344a4: r0 = int?()
    //     0xc344a4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xc344a8: ldur            x0, [fp, #-8]
    // 0xc344ac: LeaveFrame
    //     0xc344ac: mov             SP, fp
    //     0xc344b0: ldp             fp, lr, [SP], #0x10
    // 0xc344b4: ret
    //     0xc344b4: ret             
    // 0xc344b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc344b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc344bc: b               #0xc343d0
  }
  get _ pixelXDimension(/* No info */) {
    // ** addr: 0xc344c0, size: 0x100
    // 0xc344c0: EnterFrame
    //     0xc344c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc344c4: mov             fp, SP
    // 0xc344c8: AllocStack(0x10)
    //     0xc344c8: sub             SP, SP, #0x10
    // 0xc344cc: SetupParameters(PdfJpegInfo this /* r1 => r0, fp-0x10 */)
    //     0xc344cc: mov             x0, x1
    //     0xc344d0: stur            x1, [fp, #-0x10]
    // 0xc344d4: CheckStackOverflow
    //     0xc344d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc344d8: cmp             SP, x16
    //     0xc344dc: b.ls            #0xc345b8
    // 0xc344e0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc344e0: ldur            w3, [x0, #0x17]
    // 0xc344e4: DecompressPointer r3
    //     0xc344e4: add             x3, x3, HEAP, lsl #32
    // 0xc344e8: stur            x3, [fp, #-8]
    // 0xc344ec: cmp             w3, NULL
    // 0xc344f0: b.eq            #0xc34528
    // 0xc344f4: mov             x1, x3
    // 0xc344f8: r2 = Instance_PdfExifTag
    //     0xc344f8: add             x2, PP, #0x47, lsl #12  ; [pp+0x478c0] Obj!PdfExifTag@e2f0e1
    //     0xc344fc: ldr             x2, [x2, #0x8c0]
    // 0xc34500: r0 = _getValueOrData()
    //     0xc34500: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34504: mov             x1, x0
    // 0xc34508: ldur            x0, [fp, #-8]
    // 0xc3450c: LoadField: r2 = r0->field_f
    //     0xc3450c: ldur            w2, [x0, #0xf]
    // 0xc34510: DecompressPointer r2
    //     0xc34510: add             x2, x2, HEAP, lsl #32
    // 0xc34514: cmp             w2, w1
    // 0xc34518: b.eq            #0xc34524
    // 0xc3451c: cmp             w1, NULL
    // 0xc34520: b.ne            #0xc34538
    // 0xc34524: ldur            x0, [fp, #-0x10]
    // 0xc34528: LoadField: r1 = r0->field_7
    //     0xc34528: ldur            w1, [x0, #7]
    // 0xc3452c: DecompressPointer r1
    //     0xc3452c: add             x1, x1, HEAP, lsl #32
    // 0xc34530: mov             x3, x1
    // 0xc34534: b               #0xc34570
    // 0xc34538: mov             x1, x0
    // 0xc3453c: r2 = Instance_PdfExifTag
    //     0xc3453c: add             x2, PP, #0x47, lsl #12  ; [pp+0x478c0] Obj!PdfExifTag@e2f0e1
    //     0xc34540: ldr             x2, [x2, #0x8c0]
    // 0xc34544: r0 = _getValueOrData()
    //     0xc34544: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34548: mov             x1, x0
    // 0xc3454c: ldur            x0, [fp, #-8]
    // 0xc34550: LoadField: r2 = r0->field_f
    //     0xc34550: ldur            w2, [x0, #0xf]
    // 0xc34554: DecompressPointer r2
    //     0xc34554: add             x2, x2, HEAP, lsl #32
    // 0xc34558: cmp             w2, w1
    // 0xc3455c: b.ne            #0xc34568
    // 0xc34560: r0 = Null
    //     0xc34560: mov             x0, NULL
    // 0xc34564: b               #0xc3456c
    // 0xc34568: mov             x0, x1
    // 0xc3456c: mov             x3, x0
    // 0xc34570: mov             x0, x3
    // 0xc34574: stur            x3, [fp, #-8]
    // 0xc34578: r2 = Null
    //     0xc34578: mov             x2, NULL
    // 0xc3457c: r1 = Null
    //     0xc3457c: mov             x1, NULL
    // 0xc34580: branchIfSmi(r0, 0xc345a8)
    //     0xc34580: tbz             w0, #0, #0xc345a8
    // 0xc34584: r4 = LoadClassIdInstr(r0)
    //     0xc34584: ldur            x4, [x0, #-1]
    //     0xc34588: ubfx            x4, x4, #0xc, #0x14
    // 0xc3458c: sub             x4, x4, #0x3c
    // 0xc34590: cmp             x4, #1
    // 0xc34594: b.ls            #0xc345a8
    // 0xc34598: r8 = int?
    //     0xc34598: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xc3459c: r3 = Null
    //     0xc3459c: add             x3, PP, #0x47, lsl #12  ; [pp+0x478c8] Null
    //     0xc345a0: ldr             x3, [x3, #0x8c8]
    // 0xc345a4: r0 = int?()
    //     0xc345a4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xc345a8: ldur            x0, [fp, #-8]
    // 0xc345ac: LeaveFrame
    //     0xc345ac: mov             SP, fp
    //     0xc345b0: ldp             fp, lr, [SP], #0x10
    // 0xc345b4: ret
    //     0xc345b4: ret             
    // 0xc345b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc345b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc345bc: b               #0xc344e0
  }
  get _ yResolution(/* No info */) {
    // ** addr: 0xc345c0, size: 0x198
    // 0xc345c0: EnterFrame
    //     0xc345c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc345c4: mov             fp, SP
    // 0xc345c8: AllocStack(0x20)
    //     0xc345c8: sub             SP, SP, #0x20
    // 0xc345cc: CheckStackOverflow
    //     0xc345cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc345d0: cmp             SP, x16
    //     0xc345d4: b.ls            #0xc3473c
    // 0xc345d8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc345d8: ldur            w0, [x1, #0x17]
    // 0xc345dc: DecompressPointer r0
    //     0xc345dc: add             x0, x0, HEAP, lsl #32
    // 0xc345e0: stur            x0, [fp, #-8]
    // 0xc345e4: cmp             w0, NULL
    // 0xc345e8: b.eq            #0xc3461c
    // 0xc345ec: mov             x1, x0
    // 0xc345f0: r2 = Instance_PdfExifTag
    //     0xc345f0: add             x2, PP, #0x47, lsl #12  ; [pp+0x478d8] Obj!PdfExifTag@e2f101
    //     0xc345f4: ldr             x2, [x2, #0x8d8]
    // 0xc345f8: r0 = _getValueOrData()
    //     0xc345f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc345fc: mov             x1, x0
    // 0xc34600: ldur            x0, [fp, #-8]
    // 0xc34604: LoadField: r2 = r0->field_f
    //     0xc34604: ldur            w2, [x0, #0xf]
    // 0xc34608: DecompressPointer r2
    //     0xc34608: add             x2, x2, HEAP, lsl #32
    // 0xc3460c: cmp             w2, w1
    // 0xc34610: b.eq            #0xc3461c
    // 0xc34614: cmp             w1, NULL
    // 0xc34618: b.ne            #0xc34624
    // 0xc3461c: r0 = Null
    //     0xc3461c: mov             x0, NULL
    // 0xc34620: b               #0xc34730
    // 0xc34624: mov             x1, x0
    // 0xc34628: r2 = Instance_PdfExifTag
    //     0xc34628: add             x2, PP, #0x47, lsl #12  ; [pp+0x478d8] Obj!PdfExifTag@e2f101
    //     0xc3462c: ldr             x2, [x2, #0x8d8]
    // 0xc34630: r0 = _getValueOrData()
    //     0xc34630: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34634: ldur            x1, [fp, #-8]
    // 0xc34638: LoadField: r2 = r1->field_f
    //     0xc34638: ldur            w2, [x1, #0xf]
    // 0xc3463c: DecompressPointer r2
    //     0xc3463c: add             x2, x2, HEAP, lsl #32
    // 0xc34640: cmp             w2, w0
    // 0xc34644: b.ne            #0xc3464c
    // 0xc34648: r0 = Null
    //     0xc34648: mov             x0, NULL
    // 0xc3464c: stp             xzr, x0, [SP]
    // 0xc34650: r4 = 0
    //     0xc34650: movz            x4, #0
    // 0xc34654: ldr             x0, [SP, #8]
    // 0xc34658: r16 = UnlinkedCall_0x5f3c08
    //     0xc34658: add             x16, PP, #0x47, lsl #12  ; [pp+0x478e0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc3465c: add             x16, x16, #0x8e0
    // 0xc34660: ldp             x5, lr, [x16]
    // 0xc34664: blr             lr
    // 0xc34668: str             x0, [SP]
    // 0xc3466c: r4 = 0
    //     0xc3466c: movz            x4, #0
    // 0xc34670: ldr             x0, [SP]
    // 0xc34674: r16 = UnlinkedCall_0x5f3c08
    //     0xc34674: add             x16, PP, #0x47, lsl #12  ; [pp+0x478f0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc34678: add             x16, x16, #0x8f0
    // 0xc3467c: ldp             x5, lr, [x16]
    // 0xc34680: blr             lr
    // 0xc34684: ldur            x1, [fp, #-8]
    // 0xc34688: r2 = Instance_PdfExifTag
    //     0xc34688: add             x2, PP, #0x47, lsl #12  ; [pp+0x478d8] Obj!PdfExifTag@e2f101
    //     0xc3468c: ldr             x2, [x2, #0x8d8]
    // 0xc34690: stur            x0, [fp, #-0x10]
    // 0xc34694: r0 = _getValueOrData()
    //     0xc34694: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34698: mov             x1, x0
    // 0xc3469c: ldur            x0, [fp, #-8]
    // 0xc346a0: LoadField: r2 = r0->field_f
    //     0xc346a0: ldur            w2, [x0, #0xf]
    // 0xc346a4: DecompressPointer r2
    //     0xc346a4: add             x2, x2, HEAP, lsl #32
    // 0xc346a8: cmp             w2, w1
    // 0xc346ac: b.ne            #0xc346b4
    // 0xc346b0: r1 = Null
    //     0xc346b0: mov             x1, NULL
    // 0xc346b4: ldur            x0, [fp, #-0x10]
    // 0xc346b8: r16 = 2
    //     0xc346b8: movz            x16, #0x2
    // 0xc346bc: stp             x16, x1, [SP]
    // 0xc346c0: r4 = 0
    //     0xc346c0: movz            x4, #0
    // 0xc346c4: ldr             x0, [SP, #8]
    // 0xc346c8: r16 = UnlinkedCall_0x5f3c08
    //     0xc346c8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47900] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc346cc: add             x16, x16, #0x900
    // 0xc346d0: ldp             x5, lr, [x16]
    // 0xc346d4: blr             lr
    // 0xc346d8: str             x0, [SP]
    // 0xc346dc: r4 = 0
    //     0xc346dc: movz            x4, #0
    // 0xc346e0: ldr             x0, [SP]
    // 0xc346e4: r16 = UnlinkedCall_0x5f3c08
    //     0xc346e4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47910] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc346e8: add             x16, x16, #0x910
    // 0xc346ec: ldp             x5, lr, [x16]
    // 0xc346f0: blr             lr
    // 0xc346f4: ldur            x1, [fp, #-0x10]
    // 0xc346f8: LoadField: d0 = r1->field_7
    //     0xc346f8: ldur            d0, [x1, #7]
    // 0xc346fc: LoadField: d1 = r0->field_7
    //     0xc346fc: ldur            d1, [x0, #7]
    // 0xc34700: fdiv            d2, d0, d1
    // 0xc34704: r1 = inline_Allocate_Double()
    //     0xc34704: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc34708: add             x1, x1, #0x10
    //     0xc3470c: cmp             x2, x1
    //     0xc34710: b.ls            #0xc34744
    //     0xc34714: str             x1, [THR, #0x50]  ; THR::top
    //     0xc34718: sub             x1, x1, #0xf
    //     0xc3471c: movz            x2, #0xe15c
    //     0xc34720: movk            x2, #0x3, lsl #16
    //     0xc34724: stur            x2, [x1, #-1]
    // 0xc34728: StoreField: r1->field_7 = d2
    //     0xc34728: stur            d2, [x1, #7]
    // 0xc3472c: mov             x0, x1
    // 0xc34730: LeaveFrame
    //     0xc34730: mov             SP, fp
    //     0xc34734: ldp             fp, lr, [SP], #0x10
    // 0xc34738: ret
    //     0xc34738: ret             
    // 0xc3473c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3473c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc34740: b               #0xc345d8
    // 0xc34744: SaveReg d2
    //     0xc34744: str             q2, [SP, #-0x10]!
    // 0xc34748: r0 = AllocateDouble()
    //     0xc34748: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3474c: mov             x1, x0
    // 0xc34750: RestoreReg d2
    //     0xc34750: ldr             q2, [SP], #0x10
    // 0xc34754: b               #0xc34728
  }
  get _ xResolution(/* No info */) {
    // ** addr: 0xc34758, size: 0x198
    // 0xc34758: EnterFrame
    //     0xc34758: stp             fp, lr, [SP, #-0x10]!
    //     0xc3475c: mov             fp, SP
    // 0xc34760: AllocStack(0x20)
    //     0xc34760: sub             SP, SP, #0x20
    // 0xc34764: CheckStackOverflow
    //     0xc34764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc34768: cmp             SP, x16
    //     0xc3476c: b.ls            #0xc348d4
    // 0xc34770: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc34770: ldur            w0, [x1, #0x17]
    // 0xc34774: DecompressPointer r0
    //     0xc34774: add             x0, x0, HEAP, lsl #32
    // 0xc34778: stur            x0, [fp, #-8]
    // 0xc3477c: cmp             w0, NULL
    // 0xc34780: b.eq            #0xc347b4
    // 0xc34784: mov             x1, x0
    // 0xc34788: r2 = Instance_PdfExifTag
    //     0xc34788: add             x2, PP, #0x47, lsl #12  ; [pp+0x47920] Obj!PdfExifTag@e2f121
    //     0xc3478c: ldr             x2, [x2, #0x920]
    // 0xc34790: r0 = _getValueOrData()
    //     0xc34790: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34794: mov             x1, x0
    // 0xc34798: ldur            x0, [fp, #-8]
    // 0xc3479c: LoadField: r2 = r0->field_f
    //     0xc3479c: ldur            w2, [x0, #0xf]
    // 0xc347a0: DecompressPointer r2
    //     0xc347a0: add             x2, x2, HEAP, lsl #32
    // 0xc347a4: cmp             w2, w1
    // 0xc347a8: b.eq            #0xc347b4
    // 0xc347ac: cmp             w1, NULL
    // 0xc347b0: b.ne            #0xc347bc
    // 0xc347b4: r0 = Null
    //     0xc347b4: mov             x0, NULL
    // 0xc347b8: b               #0xc348c8
    // 0xc347bc: mov             x1, x0
    // 0xc347c0: r2 = Instance_PdfExifTag
    //     0xc347c0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47920] Obj!PdfExifTag@e2f121
    //     0xc347c4: ldr             x2, [x2, #0x920]
    // 0xc347c8: r0 = _getValueOrData()
    //     0xc347c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc347cc: ldur            x1, [fp, #-8]
    // 0xc347d0: LoadField: r2 = r1->field_f
    //     0xc347d0: ldur            w2, [x1, #0xf]
    // 0xc347d4: DecompressPointer r2
    //     0xc347d4: add             x2, x2, HEAP, lsl #32
    // 0xc347d8: cmp             w2, w0
    // 0xc347dc: b.ne            #0xc347e4
    // 0xc347e0: r0 = Null
    //     0xc347e0: mov             x0, NULL
    // 0xc347e4: stp             xzr, x0, [SP]
    // 0xc347e8: r4 = 0
    //     0xc347e8: movz            x4, #0
    // 0xc347ec: ldr             x0, [SP, #8]
    // 0xc347f0: r16 = UnlinkedCall_0x5f3c08
    //     0xc347f0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47928] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc347f4: add             x16, x16, #0x928
    // 0xc347f8: ldp             x5, lr, [x16]
    // 0xc347fc: blr             lr
    // 0xc34800: str             x0, [SP]
    // 0xc34804: r4 = 0
    //     0xc34804: movz            x4, #0
    // 0xc34808: ldr             x0, [SP]
    // 0xc3480c: r16 = UnlinkedCall_0x5f3c08
    //     0xc3480c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47938] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc34810: add             x16, x16, #0x938
    // 0xc34814: ldp             x5, lr, [x16]
    // 0xc34818: blr             lr
    // 0xc3481c: ldur            x1, [fp, #-8]
    // 0xc34820: r2 = Instance_PdfExifTag
    //     0xc34820: add             x2, PP, #0x47, lsl #12  ; [pp+0x47920] Obj!PdfExifTag@e2f121
    //     0xc34824: ldr             x2, [x2, #0x920]
    // 0xc34828: stur            x0, [fp, #-0x10]
    // 0xc3482c: r0 = _getValueOrData()
    //     0xc3482c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34830: mov             x1, x0
    // 0xc34834: ldur            x0, [fp, #-8]
    // 0xc34838: LoadField: r2 = r0->field_f
    //     0xc34838: ldur            w2, [x0, #0xf]
    // 0xc3483c: DecompressPointer r2
    //     0xc3483c: add             x2, x2, HEAP, lsl #32
    // 0xc34840: cmp             w2, w1
    // 0xc34844: b.ne            #0xc3484c
    // 0xc34848: r1 = Null
    //     0xc34848: mov             x1, NULL
    // 0xc3484c: ldur            x0, [fp, #-0x10]
    // 0xc34850: r16 = 2
    //     0xc34850: movz            x16, #0x2
    // 0xc34854: stp             x16, x1, [SP]
    // 0xc34858: r4 = 0
    //     0xc34858: movz            x4, #0
    // 0xc3485c: ldr             x0, [SP, #8]
    // 0xc34860: r16 = UnlinkedCall_0x5f3c08
    //     0xc34860: add             x16, PP, #0x47, lsl #12  ; [pp+0x47948] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc34864: add             x16, x16, #0x948
    // 0xc34868: ldp             x5, lr, [x16]
    // 0xc3486c: blr             lr
    // 0xc34870: str             x0, [SP]
    // 0xc34874: r4 = 0
    //     0xc34874: movz            x4, #0
    // 0xc34878: ldr             x0, [SP]
    // 0xc3487c: r16 = UnlinkedCall_0x5f3c08
    //     0xc3487c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47958] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xc34880: add             x16, x16, #0x958
    // 0xc34884: ldp             x5, lr, [x16]
    // 0xc34888: blr             lr
    // 0xc3488c: ldur            x1, [fp, #-0x10]
    // 0xc34890: LoadField: d0 = r1->field_7
    //     0xc34890: ldur            d0, [x1, #7]
    // 0xc34894: LoadField: d1 = r0->field_7
    //     0xc34894: ldur            d1, [x0, #7]
    // 0xc34898: fdiv            d2, d0, d1
    // 0xc3489c: r1 = inline_Allocate_Double()
    //     0xc3489c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc348a0: add             x1, x1, #0x10
    //     0xc348a4: cmp             x2, x1
    //     0xc348a8: b.ls            #0xc348dc
    //     0xc348ac: str             x1, [THR, #0x50]  ; THR::top
    //     0xc348b0: sub             x1, x1, #0xf
    //     0xc348b4: movz            x2, #0xe15c
    //     0xc348b8: movk            x2, #0x3, lsl #16
    //     0xc348bc: stur            x2, [x1, #-1]
    // 0xc348c0: StoreField: r1->field_7 = d2
    //     0xc348c0: stur            d2, [x1, #7]
    // 0xc348c4: mov             x0, x1
    // 0xc348c8: LeaveFrame
    //     0xc348c8: mov             SP, fp
    //     0xc348cc: ldp             fp, lr, [SP], #0x10
    // 0xc348d0: ret
    //     0xc348d0: ret             
    // 0xc348d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc348d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc348d8: b               #0xc34770
    // 0xc348dc: SaveReg d2
    //     0xc348dc: str             q2, [SP, #-0x10]!
    // 0xc348e0: r0 = AllocateDouble()
    //     0xc348e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc348e4: mov             x1, x0
    // 0xc348e8: RestoreReg d2
    //     0xc348e8: ldr             q2, [SP], #0x10
    // 0xc348ec: b               #0xc348c0
  }
  get _ flashpixVersion(/* No info */) {
    // ** addr: 0xc348f0, size: 0xdc
    // 0xc348f0: EnterFrame
    //     0xc348f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc348f4: mov             fp, SP
    // 0xc348f8: AllocStack(0x8)
    //     0xc348f8: sub             SP, SP, #8
    // 0xc348fc: CheckStackOverflow
    //     0xc348fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc34900: cmp             SP, x16
    //     0xc34904: b.ls            #0xc349c4
    // 0xc34908: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc34908: ldur            w0, [x1, #0x17]
    // 0xc3490c: DecompressPointer r0
    //     0xc3490c: add             x0, x0, HEAP, lsl #32
    // 0xc34910: stur            x0, [fp, #-8]
    // 0xc34914: cmp             w0, NULL
    // 0xc34918: b.eq            #0xc3494c
    // 0xc3491c: mov             x1, x0
    // 0xc34920: r2 = Instance_PdfExifTag
    //     0xc34920: add             x2, PP, #0x47, lsl #12  ; [pp+0x47968] Obj!PdfExifTag@e2f141
    //     0xc34924: ldr             x2, [x2, #0x968]
    // 0xc34928: r0 = _getValueOrData()
    //     0xc34928: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc3492c: mov             x1, x0
    // 0xc34930: ldur            x0, [fp, #-8]
    // 0xc34934: LoadField: r2 = r0->field_f
    //     0xc34934: ldur            w2, [x0, #0xf]
    // 0xc34938: DecompressPointer r2
    //     0xc34938: add             x2, x2, HEAP, lsl #32
    // 0xc3493c: cmp             w2, w1
    // 0xc34940: b.eq            #0xc3494c
    // 0xc34944: cmp             w1, NULL
    // 0xc34948: b.ne            #0xc34954
    // 0xc3494c: r0 = Null
    //     0xc3494c: mov             x0, NULL
    // 0xc34950: b               #0xc349b8
    // 0xc34954: mov             x1, x0
    // 0xc34958: r2 = Instance_PdfExifTag
    //     0xc34958: add             x2, PP, #0x47, lsl #12  ; [pp+0x47968] Obj!PdfExifTag@e2f141
    //     0xc3495c: ldr             x2, [x2, #0x968]
    // 0xc34960: r0 = _getValueOrData()
    //     0xc34960: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34964: mov             x1, x0
    // 0xc34968: ldur            x0, [fp, #-8]
    // 0xc3496c: LoadField: r2 = r0->field_f
    //     0xc3496c: ldur            w2, [x0, #0xf]
    // 0xc34970: DecompressPointer r2
    //     0xc34970: add             x2, x2, HEAP, lsl #32
    // 0xc34974: cmp             w2, w1
    // 0xc34978: b.ne            #0xc34984
    // 0xc3497c: r3 = Null
    //     0xc3497c: mov             x3, NULL
    // 0xc34980: b               #0xc34988
    // 0xc34984: mov             x3, x1
    // 0xc34988: mov             x0, x3
    // 0xc3498c: stur            x3, [fp, #-8]
    // 0xc34990: r2 = Null
    //     0xc34990: mov             x2, NULL
    // 0xc34994: r1 = Null
    //     0xc34994: mov             x1, NULL
    // 0xc34998: r8 = List<int>
    //     0xc34998: ldr             x8, [PP, #0x1020]  ; [pp+0x1020] Type: List<int>
    // 0xc3499c: r3 = Null
    //     0xc3499c: add             x3, PP, #0x47, lsl #12  ; [pp+0x47970] Null
    //     0xc349a0: ldr             x3, [x3, #0x970]
    // 0xc349a4: r0 = List<int>()
    //     0xc349a4: bl              #0x601a14  ; IsType_List<int>_Stub
    // 0xc349a8: ldur            x2, [fp, #-8]
    // 0xc349ac: r1 = Instance_Utf8Codec
    //     0xc349ac: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0xc349b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc349b0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc349b4: r0 = decode()
    //     0xc349b4: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0xc349b8: LeaveFrame
    //     0xc349b8: mov             SP, fp
    //     0xc349bc: ldp             fp, lr, [SP], #0x10
    // 0xc349c0: ret
    //     0xc349c0: ret             
    // 0xc349c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc349c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc349c8: b               #0xc34908
  }
  get _ exifVersion(/* No info */) {
    // ** addr: 0xc349cc, size: 0xdc
    // 0xc349cc: EnterFrame
    //     0xc349cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc349d0: mov             fp, SP
    // 0xc349d4: AllocStack(0x8)
    //     0xc349d4: sub             SP, SP, #8
    // 0xc349d8: CheckStackOverflow
    //     0xc349d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc349dc: cmp             SP, x16
    //     0xc349e0: b.ls            #0xc34aa0
    // 0xc349e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc349e4: ldur            w0, [x1, #0x17]
    // 0xc349e8: DecompressPointer r0
    //     0xc349e8: add             x0, x0, HEAP, lsl #32
    // 0xc349ec: stur            x0, [fp, #-8]
    // 0xc349f0: cmp             w0, NULL
    // 0xc349f4: b.eq            #0xc34a28
    // 0xc349f8: mov             x1, x0
    // 0xc349fc: r2 = Instance_PdfExifTag
    //     0xc349fc: add             x2, PP, #0x47, lsl #12  ; [pp+0x47980] Obj!PdfExifTag@e2f161
    //     0xc34a00: ldr             x2, [x2, #0x980]
    // 0xc34a04: r0 = _getValueOrData()
    //     0xc34a04: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34a08: mov             x1, x0
    // 0xc34a0c: ldur            x0, [fp, #-8]
    // 0xc34a10: LoadField: r2 = r0->field_f
    //     0xc34a10: ldur            w2, [x0, #0xf]
    // 0xc34a14: DecompressPointer r2
    //     0xc34a14: add             x2, x2, HEAP, lsl #32
    // 0xc34a18: cmp             w2, w1
    // 0xc34a1c: b.eq            #0xc34a28
    // 0xc34a20: cmp             w1, NULL
    // 0xc34a24: b.ne            #0xc34a30
    // 0xc34a28: r0 = Null
    //     0xc34a28: mov             x0, NULL
    // 0xc34a2c: b               #0xc34a94
    // 0xc34a30: mov             x1, x0
    // 0xc34a34: r2 = Instance_PdfExifTag
    //     0xc34a34: add             x2, PP, #0x47, lsl #12  ; [pp+0x47980] Obj!PdfExifTag@e2f161
    //     0xc34a38: ldr             x2, [x2, #0x980]
    // 0xc34a3c: r0 = _getValueOrData()
    //     0xc34a3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc34a40: mov             x1, x0
    // 0xc34a44: ldur            x0, [fp, #-8]
    // 0xc34a48: LoadField: r2 = r0->field_f
    //     0xc34a48: ldur            w2, [x0, #0xf]
    // 0xc34a4c: DecompressPointer r2
    //     0xc34a4c: add             x2, x2, HEAP, lsl #32
    // 0xc34a50: cmp             w2, w1
    // 0xc34a54: b.ne            #0xc34a60
    // 0xc34a58: r3 = Null
    //     0xc34a58: mov             x3, NULL
    // 0xc34a5c: b               #0xc34a64
    // 0xc34a60: mov             x3, x1
    // 0xc34a64: mov             x0, x3
    // 0xc34a68: stur            x3, [fp, #-8]
    // 0xc34a6c: r2 = Null
    //     0xc34a6c: mov             x2, NULL
    // 0xc34a70: r1 = Null
    //     0xc34a70: mov             x1, NULL
    // 0xc34a74: r8 = List<int>
    //     0xc34a74: ldr             x8, [PP, #0x1020]  ; [pp+0x1020] Type: List<int>
    // 0xc34a78: r3 = Null
    //     0xc34a78: add             x3, PP, #0x47, lsl #12  ; [pp+0x47988] Null
    //     0xc34a7c: ldr             x3, [x3, #0x988]
    // 0xc34a80: r0 = List<int>()
    //     0xc34a80: bl              #0x601a14  ; IsType_List<int>_Stub
    // 0xc34a84: ldur            x2, [fp, #-8]
    // 0xc34a88: r1 = Instance_Utf8Codec
    //     0xc34a88: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0xc34a8c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc34a8c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc34a90: r0 = decode()
    //     0xc34a90: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0xc34a94: LeaveFrame
    //     0xc34a94: mov             SP, fp
    //     0xc34a98: ldp             fp, lr, [SP], #0x10
    // 0xc34a9c: ret
    //     0xc34a9c: ret             
    // 0xc34aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc34aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc34aa4: b               #0xc349e4
  }
  factory _ PdfJpegInfo(/* No info */) {
    // ** addr: 0xe6ab28, size: 0x308
    // 0xe6ab28: EnterFrame
    //     0xe6ab28: stp             fp, lr, [SP, #-0x10]!
    //     0xe6ab2c: mov             fp, SP
    // 0xe6ab30: AllocStack(0x30)
    //     0xe6ab30: sub             SP, SP, #0x30
    // 0xe6ab34: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe6ab34: stur            x2, [fp, #-8]
    // 0xe6ab38: CheckStackOverflow
    //     0xe6ab38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6ab3c: cmp             SP, x16
    //     0xe6ab40: b.ls            #0xe6ae04
    // 0xe6ab44: r0 = LoadClassIdInstr(r2)
    //     0xe6ab44: ldur            x0, [x2, #-1]
    //     0xe6ab48: ubfx            x0, x0, #0xc, #0x14
    // 0xe6ab4c: mov             x1, x2
    // 0xe6ab50: r0 = GDT[cid_x0 + -0xf60]()
    //     0xe6ab50: sub             lr, x0, #0xf60
    //     0xe6ab54: ldr             lr, [x21, lr, lsl #3]
    //     0xe6ab58: blr             lr
    // 0xe6ab5c: mov             x1, x0
    // 0xe6ab60: ldur            x0, [fp, #-8]
    // 0xe6ab64: LoadField: r2 = r0->field_1b
    //     0xe6ab64: ldur            w2, [x0, #0x1b]
    // 0xe6ab68: LoadField: r3 = r0->field_13
    //     0xe6ab68: ldur            w3, [x0, #0x13]
    // 0xe6ab6c: r0 = LoadClassIdInstr(r1)
    //     0xe6ab6c: ldur            x0, [x1, #-1]
    //     0xe6ab70: ubfx            x0, x0, #0xc, #0x14
    // 0xe6ab74: stp             x3, x2, [SP]
    // 0xe6ab78: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xe6ab78: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xe6ab7c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xe6ab7c: sub             lr, x0, #0xfff
    //     0xe6ab80: ldr             lr, [x21, lr, lsl #3]
    //     0xe6ab84: blr             lr
    // 0xe6ab88: mov             x2, x0
    // 0xe6ab8c: LoadField: r0 = r2->field_13
    //     0xe6ab8c: ldur            w0, [x2, #0x13]
    // 0xe6ab90: r3 = LoadInt32Instr(r0)
    //     0xe6ab90: sbfx            x3, x0, #1, #0x1f
    // 0xe6ab94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6ab94: ldur            w4, [x2, #0x17]
    // 0xe6ab98: DecompressPointer r4
    //     0xe6ab98: add             x4, x4, HEAP, lsl #32
    // 0xe6ab9c: LoadField: r0 = r2->field_1b
    //     0xe6ab9c: ldur            w0, [x2, #0x1b]
    // 0xe6aba0: r5 = LoadInt32Instr(r0)
    //     0xe6aba0: sbfx            x5, x0, #1, #0x1f
    // 0xe6aba4: sub             x6, x3, #1
    // 0xe6aba8: r0 = 0
    //     0xe6aba8: movz            x0, #0
    // 0xe6abac: r8 = 65280
    //     0xe6abac: orr             x8, xzr, #0xff00
    // 0xe6abb0: r7 = 255
    //     0xe6abb0: movz            x7, #0xff
    // 0xe6abb4: CheckStackOverflow
    //     0xe6abb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6abb8: cmp             SP, x16
    //     0xe6abbc: b.ls            #0xe6ae0c
    // 0xe6abc0: cmp             x0, x3
    // 0xe6abc4: b.ge            #0xe6ad8c
    // 0xe6abc8: mov             x9, x0
    // 0xe6abcc: CheckStackOverflow
    //     0xe6abcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6abd0: cmp             SP, x16
    //     0xe6abd4: b.ls            #0xe6ae14
    // 0xe6abd8: mov             x0, x3
    // 0xe6abdc: mov             x1, x9
    // 0xe6abe0: cmp             x1, x0
    // 0xe6abe4: b.hs            #0xe6ae1c
    // 0xe6abe8: add             x0, x5, x9
    // 0xe6abec: LoadField: r1 = r4->field_7
    //     0xe6abec: ldur            x1, [x4, #7]
    // 0xe6abf0: ldrb            w10, [x1, x0]
    // 0xe6abf4: cmp             x10, #0xff
    // 0xe6abf8: b.ne            #0xe6ac08
    // 0xe6abfc: add             x0, x9, #1
    // 0xe6ac00: mov             x9, x0
    // 0xe6ac04: b               #0xe6abcc
    // 0xe6ac08: add             x11, x9, #1
    // 0xe6ac0c: cmp             x10, #0xd8
    // 0xe6ac10: b.ne            #0xe6ac1c
    // 0xe6ac14: mov             x0, x11
    // 0xe6ac18: b               #0xe6abb4
    // 0xe6ac1c: cmp             x10, #0xd9
    // 0xe6ac20: b.eq            #0xe6ad7c
    // 0xe6ac24: cmp             x10, #0xd0
    // 0xe6ac28: b.lt            #0xe6ac3c
    // 0xe6ac2c: cmp             x10, #0xd7
    // 0xe6ac30: b.gt            #0xe6ac3c
    // 0xe6ac34: mov             x0, x11
    // 0xe6ac38: b               #0xe6abb4
    // 0xe6ac3c: cmp             x10, #1
    // 0xe6ac40: b.ne            #0xe6ac4c
    // 0xe6ac44: mov             x0, x11
    // 0xe6ac48: b               #0xe6abb4
    // 0xe6ac4c: mov             x0, x6
    // 0xe6ac50: mov             x1, x11
    // 0xe6ac54: cmp             x1, x0
    // 0xe6ac58: b.hs            #0xe6ae20
    // 0xe6ac5c: add             x0, x5, x11
    // 0xe6ac60: LoadField: r1 = r4->field_7
    //     0xe6ac60: ldur            x1, [x4, #7]
    // 0xe6ac64: ldrh            w9, [x1, x0]
    // 0xe6ac68: mov             x0, x9
    // 0xe6ac6c: ubfx            x0, x0, #0, #0x20
    // 0xe6ac70: and             x1, x0, x8
    // 0xe6ac74: ubfx            x1, x1, #0, #0x20
    // 0xe6ac78: asr             x0, x1, #8
    // 0xe6ac7c: ubfx            x9, x9, #0, #0x20
    // 0xe6ac80: and             x1, x9, x7
    // 0xe6ac84: ubfx            x1, x1, #0, #0x20
    // 0xe6ac88: lsl             x9, x1, #8
    // 0xe6ac8c: orr             x1, x0, x9
    // 0xe6ac90: add             x9, x11, #2
    // 0xe6ac94: cmp             x10, #0xc0
    // 0xe6ac98: b.lt            #0xe6ad6c
    // 0xe6ac9c: cmp             x10, #0xc2
    // 0xe6aca0: b.gt            #0xe6ad6c
    // 0xe6aca4: add             x10, x9, #1
    // 0xe6aca8: mov             x0, x6
    // 0xe6acac: mov             x1, x10
    // 0xe6acb0: cmp             x1, x0
    // 0xe6acb4: b.hs            #0xe6ae24
    // 0xe6acb8: add             x0, x5, x10
    // 0xe6acbc: LoadField: r1 = r4->field_7
    //     0xe6acbc: ldur            x1, [x4, #7]
    // 0xe6acc0: ldrh            w10, [x1, x0]
    // 0xe6acc4: mov             x0, x10
    // 0xe6acc8: ubfx            x0, x0, #0, #0x20
    // 0xe6accc: and             x1, x0, x8
    // 0xe6acd0: ubfx            x1, x1, #0, #0x20
    // 0xe6acd4: asr             x0, x1, #8
    // 0xe6acd8: ubfx            x10, x10, #0, #0x20
    // 0xe6acdc: and             x1, x10, x7
    // 0xe6ace0: ubfx            x1, x1, #0, #0x20
    // 0xe6ace4: lsl             x10, x1, #8
    // 0xe6ace8: orr             x11, x0, x10
    // 0xe6acec: add             x10, x9, #3
    // 0xe6acf0: mov             x0, x6
    // 0xe6acf4: mov             x1, x10
    // 0xe6acf8: cmp             x1, x0
    // 0xe6acfc: b.hs            #0xe6ae28
    // 0xe6ad00: add             x0, x5, x10
    // 0xe6ad04: LoadField: r1 = r4->field_7
    //     0xe6ad04: ldur            x1, [x4, #7]
    // 0xe6ad08: ldrh            w6, [x1, x0]
    // 0xe6ad0c: mov             x0, x6
    // 0xe6ad10: ubfx            x0, x0, #0, #0x20
    // 0xe6ad14: and             x1, x0, x8
    // 0xe6ad18: ubfx            x1, x1, #0, #0x20
    // 0xe6ad1c: asr             x0, x1, #8
    // 0xe6ad20: ubfx            x6, x6, #0, #0x20
    // 0xe6ad24: and             x1, x6, x7
    // 0xe6ad28: ubfx            x1, x1, #0, #0x20
    // 0xe6ad2c: lsl             x6, x1, #8
    // 0xe6ad30: orr             x7, x0, x6
    // 0xe6ad34: add             x6, x9, #5
    // 0xe6ad38: mov             x0, x3
    // 0xe6ad3c: mov             x1, x6
    // 0xe6ad40: cmp             x1, x0
    // 0xe6ad44: b.hs            #0xe6ae2c
    // 0xe6ad48: add             x0, x5, x6
    // 0xe6ad4c: LoadField: r1 = r4->field_7
    //     0xe6ad4c: ldur            x1, [x4, #7]
    // 0xe6ad50: ldrb            w3, [x1, x0]
    // 0xe6ad54: lsl             x0, x3, #1
    // 0xe6ad58: lsl             x1, x11, #1
    // 0xe6ad5c: lsl             x3, x7, #1
    // 0xe6ad60: mov             x4, x3
    // 0xe6ad64: mov             x3, x1
    // 0xe6ad68: b               #0xe6ad98
    // 0xe6ad6c: sub             x0, x1, #2
    // 0xe6ad70: add             x1, x9, x0
    // 0xe6ad74: mov             x0, x1
    // 0xe6ad78: b               #0xe6abb4
    // 0xe6ad7c: r4 = Null
    //     0xe6ad7c: mov             x4, NULL
    // 0xe6ad80: r3 = Null
    //     0xe6ad80: mov             x3, NULL
    // 0xe6ad84: r0 = Null
    //     0xe6ad84: mov             x0, NULL
    // 0xe6ad88: b               #0xe6ad98
    // 0xe6ad8c: r4 = Null
    //     0xe6ad8c: mov             x4, NULL
    // 0xe6ad90: r3 = Null
    //     0xe6ad90: mov             x3, NULL
    // 0xe6ad94: r0 = Null
    //     0xe6ad94: mov             x0, NULL
    // 0xe6ad98: stur            x4, [fp, #-8]
    // 0xe6ad9c: stur            x3, [fp, #-0x10]
    // 0xe6ada0: stur            x0, [fp, #-0x18]
    // 0xe6ada4: cmp             w3, NULL
    // 0xe6ada8: b.eq            #0xe6adf4
    // 0xe6adac: mov             x1, x2
    // 0xe6adb0: r0 = _findExifInJpeg()
    //     0xe6adb0: bl              #0xe6ae3c  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_findExifInJpeg
    // 0xe6adb4: stur            x0, [fp, #-0x20]
    // 0xe6adb8: r0 = PdfJpegInfo()
    //     0xe6adb8: bl              #0xe6ae30  ; AllocatePdfJpegInfoStub -> PdfJpegInfo (size=0x1c)
    // 0xe6adbc: mov             x1, x0
    // 0xe6adc0: ldur            x0, [fp, #-8]
    // 0xe6adc4: StoreField: r1->field_7 = r0
    //     0xe6adc4: stur            w0, [x1, #7]
    // 0xe6adc8: ldur            x0, [fp, #-0x10]
    // 0xe6adcc: r2 = LoadInt32Instr(r0)
    //     0xe6adcc: sbfx            x2, x0, #1, #0x1f
    // 0xe6add0: StoreField: r1->field_b = r2
    //     0xe6add0: stur            x2, [x1, #0xb]
    // 0xe6add4: ldur            x0, [fp, #-0x18]
    // 0xe6add8: StoreField: r1->field_13 = r0
    //     0xe6add8: stur            w0, [x1, #0x13]
    // 0xe6addc: ldur            x0, [fp, #-0x20]
    // 0xe6ade0: ArrayStore: r1[0] = r0  ; List_4
    //     0xe6ade0: stur            w0, [x1, #0x17]
    // 0xe6ade4: mov             x0, x1
    // 0xe6ade8: LeaveFrame
    //     0xe6ade8: mov             SP, fp
    //     0xe6adec: ldp             fp, lr, [SP], #0x10
    // 0xe6adf0: ret
    //     0xe6adf0: ret             
    // 0xe6adf4: r0 = "Unable to find a Jpeg image in the file"
    //     0xe6adf4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e5d8] "Unable to find a Jpeg image in the file"
    //     0xe6adf8: ldr             x0, [x0, #0x5d8]
    // 0xe6adfc: r0 = Throw()
    //     0xe6adfc: bl              #0xec04b8  ; ThrowStub
    // 0xe6ae00: brk             #0
    // 0xe6ae04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6ae04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6ae08: b               #0xe6ab44
    // 0xe6ae0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6ae0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6ae10: b               #0xe6abc0
    // 0xe6ae14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6ae14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6ae18: b               #0xe6abd8
    // 0xe6ae1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ae1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ae20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ae20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ae24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ae24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ae28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ae28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ae2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ae2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _findExifInJpeg(/* No info */) {
    // ** addr: 0xe6ae3c, size: 0x1d4
    // 0xe6ae3c: EnterFrame
    //     0xe6ae3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe6ae40: mov             fp, SP
    // 0xe6ae44: AllocStack(0x10)
    //     0xe6ae44: sub             SP, SP, #0x10
    // 0xe6ae48: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0xe6ae48: mov             x2, x1
    // 0xe6ae4c: CheckStackOverflow
    //     0xe6ae4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6ae50: cmp             SP, x16
    //     0xe6ae54: b.ls            #0xe6aff0
    // 0xe6ae58: LoadField: r0 = r2->field_13
    //     0xe6ae58: ldur            w0, [x2, #0x13]
    // 0xe6ae5c: r3 = LoadInt32Instr(r0)
    //     0xe6ae5c: sbfx            x3, x0, #1, #0x1f
    // 0xe6ae60: mov             x0, x3
    // 0xe6ae64: r1 = 0
    //     0xe6ae64: movz            x1, #0
    // 0xe6ae68: cmp             x1, x0
    // 0xe6ae6c: b.hs            #0xe6aff8
    // 0xe6ae70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6ae70: ldur            w4, [x2, #0x17]
    // 0xe6ae74: DecompressPointer r4
    //     0xe6ae74: add             x4, x4, HEAP, lsl #32
    // 0xe6ae78: LoadField: r5 = r2->field_1b
    //     0xe6ae78: ldur            w5, [x2, #0x1b]
    // 0xe6ae7c: LoadField: r0 = r4->field_7
    //     0xe6ae7c: ldur            x0, [x4, #7]
    // 0xe6ae80: asr             w16, w5, #1
    // 0xe6ae84: add             x16, x0, w16, sxtw
    // 0xe6ae88: ldrb            w1, [x16]
    // 0xe6ae8c: cmp             x1, #0xff
    // 0xe6ae90: b.ne            #0xe6aebc
    // 0xe6ae94: mov             x0, x3
    // 0xe6ae98: r1 = 1
    //     0xe6ae98: movz            x1, #0x1
    // 0xe6ae9c: cmp             x1, x0
    // 0xe6aea0: b.hs            #0xe6affc
    // 0xe6aea4: r6 = LoadInt32Instr(r5)
    //     0xe6aea4: sbfx            x6, x5, #1, #0x1f
    // 0xe6aea8: add             x0, x6, #1
    // 0xe6aeac: LoadField: r1 = r4->field_7
    //     0xe6aeac: ldur            x1, [x4, #7]
    // 0xe6aeb0: ldrb            w5, [x1, x0]
    // 0xe6aeb4: cmp             x5, #0xd8
    // 0xe6aeb8: b.eq            #0xe6aedc
    // 0xe6aebc: r16 = <PdfExifTag, dynamic>
    //     0xe6aebc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5e0] TypeArguments: <PdfExifTag, dynamic>
    //     0xe6aec0: ldr             x16, [x16, #0x5e0]
    // 0xe6aec4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe6aec8: stp             lr, x16, [SP]
    // 0xe6aecc: r0 = Map._fromLiteral()
    //     0xe6aecc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe6aed0: LeaveFrame
    //     0xe6aed0: mov             SP, fp
    //     0xe6aed4: ldp             fp, lr, [SP], #0x10
    // 0xe6aed8: ret
    //     0xe6aed8: ret             
    // 0xe6aedc: sub             x5, x3, #1
    // 0xe6aee0: r9 = 2
    //     0xe6aee0: movz            x9, #0x2
    // 0xe6aee4: r8 = 65280
    //     0xe6aee4: orr             x8, xzr, #0xff00
    // 0xe6aee8: r7 = 255
    //     0xe6aee8: movz            x7, #0xff
    // 0xe6aeec: CheckStackOverflow
    //     0xe6aeec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6aef0: cmp             SP, x16
    //     0xe6aef4: b.ls            #0xe6b000
    // 0xe6aef8: cmp             x9, x3
    // 0xe6aefc: b.ge            #0xe6afd0
    // 0xe6af00: add             x0, x6, x9
    // 0xe6af04: LoadField: r1 = r4->field_7
    //     0xe6af04: ldur            x1, [x4, #7]
    // 0xe6af08: ldrb            w10, [x1, x0]
    // 0xe6af0c: cmp             x10, #0xff
    // 0xe6af10: b.ne            #0xe6afb0
    // 0xe6af14: add             x10, x9, #1
    // 0xe6af18: mov             x0, x3
    // 0xe6af1c: mov             x1, x10
    // 0xe6af20: cmp             x1, x0
    // 0xe6af24: b.hs            #0xe6b008
    // 0xe6af28: add             x0, x6, x10
    // 0xe6af2c: LoadField: r1 = r4->field_7
    //     0xe6af2c: ldur            x1, [x4, #7]
    // 0xe6af30: ldrb            w10, [x1, x0]
    // 0xe6af34: cmp             x10, #0xe1
    // 0xe6af38: b.eq            #0xe6af94
    // 0xe6af3c: add             x10, x9, #2
    // 0xe6af40: mov             x0, x5
    // 0xe6af44: mov             x1, x10
    // 0xe6af48: cmp             x1, x0
    // 0xe6af4c: b.hs            #0xe6b00c
    // 0xe6af50: add             x0, x6, x10
    // 0xe6af54: LoadField: r1 = r4->field_7
    //     0xe6af54: ldur            x1, [x4, #7]
    // 0xe6af58: ldrh            w10, [x1, x0]
    // 0xe6af5c: mov             x0, x10
    // 0xe6af60: ubfx            x0, x0, #0, #0x20
    // 0xe6af64: and             x1, x0, x8
    // 0xe6af68: ubfx            x1, x1, #0, #0x20
    // 0xe6af6c: asr             x0, x1, #8
    // 0xe6af70: ubfx            x10, x10, #0, #0x20
    // 0xe6af74: and             x1, x10, x7
    // 0xe6af78: ubfx            x1, x1, #0, #0x20
    // 0xe6af7c: lsl             x10, x1, #8
    // 0xe6af80: orr             x1, x0, x10
    // 0xe6af84: add             x0, x1, #2
    // 0xe6af88: add             x1, x9, x0
    // 0xe6af8c: mov             x9, x1
    // 0xe6af90: b               #0xe6aeec
    // 0xe6af94: add             x0, x9, #4
    // 0xe6af98: mov             x1, x2
    // 0xe6af9c: mov             x2, x0
    // 0xe6afa0: r0 = _readEXIFData()
    //     0xe6afa0: bl              #0xe6b010  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_readEXIFData
    // 0xe6afa4: LeaveFrame
    //     0xe6afa4: mov             SP, fp
    //     0xe6afa8: ldp             fp, lr, [SP], #0x10
    // 0xe6afac: ret
    //     0xe6afac: ret             
    // 0xe6afb0: r16 = <PdfExifTag, dynamic>
    //     0xe6afb0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5e0] TypeArguments: <PdfExifTag, dynamic>
    //     0xe6afb4: ldr             x16, [x16, #0x5e0]
    // 0xe6afb8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe6afbc: stp             lr, x16, [SP]
    // 0xe6afc0: r0 = Map._fromLiteral()
    //     0xe6afc0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe6afc4: LeaveFrame
    //     0xe6afc4: mov             SP, fp
    //     0xe6afc8: ldp             fp, lr, [SP], #0x10
    // 0xe6afcc: ret
    //     0xe6afcc: ret             
    // 0xe6afd0: r16 = <PdfExifTag, dynamic>
    //     0xe6afd0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5e0] TypeArguments: <PdfExifTag, dynamic>
    //     0xe6afd4: ldr             x16, [x16, #0x5e0]
    // 0xe6afd8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe6afdc: stp             lr, x16, [SP]
    // 0xe6afe0: r0 = Map._fromLiteral()
    //     0xe6afe0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe6afe4: LeaveFrame
    //     0xe6afe4: mov             SP, fp
    //     0xe6afe8: ldp             fp, lr, [SP], #0x10
    // 0xe6afec: ret
    //     0xe6afec: ret             
    // 0xe6aff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6aff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6aff4: b               #0xe6ae58
    // 0xe6aff8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6aff8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6affc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6affc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6b000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6b000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6b004: b               #0xe6aef8
    // 0xe6b008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b008: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6b00c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b00c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _readEXIFData(/* No info */) {
    // ** addr: 0xe6b010, size: 0x3a4
    // 0xe6b010: EnterFrame
    //     0xe6b010: stp             fp, lr, [SP, #-0x10]!
    //     0xe6b014: mov             fp, SP
    // 0xe6b018: AllocStack(0x48)
    //     0xe6b018: sub             SP, SP, #0x48
    // 0xe6b01c: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe6b01c: mov             x4, x1
    //     0xe6b020: mov             x0, x2
    //     0xe6b024: stur            x1, [fp, #-8]
    //     0xe6b028: stur            x2, [fp, #-0x10]
    // 0xe6b02c: CheckStackOverflow
    //     0xe6b02c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6b030: cmp             SP, x16
    //     0xe6b034: b.ls            #0xe6b3a0
    // 0xe6b038: mov             x1, x4
    // 0xe6b03c: mov             x2, x0
    // 0xe6b040: r3 = 4
    //     0xe6b040: movz            x3, #0x4
    // 0xe6b044: r0 = _getStringFromDB()
    //     0xe6b044: bl              #0xe6d088  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_getStringFromDB
    // 0xe6b048: r1 = LoadClassIdInstr(r0)
    //     0xe6b048: ldur            x1, [x0, #-1]
    //     0xe6b04c: ubfx            x1, x1, #0xc, #0x14
    // 0xe6b050: r16 = "Exif"
    //     0xe6b050: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5e8] "Exif"
    //     0xe6b054: ldr             x16, [x16, #0x5e8]
    // 0xe6b058: stp             x16, x0, [SP]
    // 0xe6b05c: mov             x0, x1
    // 0xe6b060: mov             lr, x0
    // 0xe6b064: ldr             lr, [x21, lr, lsl #3]
    // 0xe6b068: blr             lr
    // 0xe6b06c: tbz             w0, #4, #0xe6b080
    // 0xe6b070: r0 = Null
    //     0xe6b070: mov             x0, NULL
    // 0xe6b074: LeaveFrame
    //     0xe6b074: mov             SP, fp
    //     0xe6b078: ldp             fp, lr, [SP], #0x10
    // 0xe6b07c: ret
    //     0xe6b07c: ret             
    // 0xe6b080: ldur            x4, [fp, #-8]
    // 0xe6b084: ldur            x0, [fp, #-0x10]
    // 0xe6b088: r3 = 65280
    //     0xe6b088: orr             x3, xzr, #0xff00
    // 0xe6b08c: r2 = 255
    //     0xe6b08c: movz            x2, #0xff
    // 0xe6b090: add             x6, x0, #6
    // 0xe6b094: stur            x6, [fp, #-0x28]
    // 0xe6b098: LoadField: r0 = r4->field_13
    //     0xe6b098: ldur            w0, [x4, #0x13]
    // 0xe6b09c: r5 = LoadInt32Instr(r0)
    //     0xe6b09c: sbfx            x5, x0, #1, #0x1f
    // 0xe6b0a0: sub             x7, x5, #1
    // 0xe6b0a4: r0 = BoxInt64Instr(r6)
    //     0xe6b0a4: sbfiz           x0, x6, #1, #0x1f
    //     0xe6b0a8: cmp             x6, x0, asr #1
    //     0xe6b0ac: b.eq            #0xe6b0b8
    //     0xe6b0b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6b0b4: stur            x6, [x0, #7]
    // 0xe6b0b8: mov             x8, x0
    // 0xe6b0bc: mov             x0, x7
    // 0xe6b0c0: mov             x1, x6
    // 0xe6b0c4: stur            x8, [fp, #-0x20]
    // 0xe6b0c8: cmp             x1, x0
    // 0xe6b0cc: b.hs            #0xe6b3a8
    // 0xe6b0d0: ArrayLoad: r9 = r4[0]  ; List_4
    //     0xe6b0d0: ldur            w9, [x4, #0x17]
    // 0xe6b0d4: DecompressPointer r9
    //     0xe6b0d4: add             x9, x9, HEAP, lsl #32
    // 0xe6b0d8: LoadField: r0 = r4->field_1b
    //     0xe6b0d8: ldur            w0, [x4, #0x1b]
    // 0xe6b0dc: r10 = LoadInt32Instr(r0)
    //     0xe6b0dc: sbfx            x10, x0, #1, #0x1f
    // 0xe6b0e0: add             x0, x10, x6
    // 0xe6b0e4: LoadField: r1 = r9->field_7
    //     0xe6b0e4: ldur            x1, [x9, #7]
    // 0xe6b0e8: ldrh            w11, [x1, x0]
    // 0xe6b0ec: mov             x0, x11
    // 0xe6b0f0: ubfx            x0, x0, #0, #0x20
    // 0xe6b0f4: and             x1, x0, x3
    // 0xe6b0f8: ubfx            x1, x1, #0, #0x20
    // 0xe6b0fc: asr             x0, x1, #8
    // 0xe6b100: ubfx            x11, x11, #0, #0x20
    // 0xe6b104: and             x1, x11, x2
    // 0xe6b108: ubfx            x1, x1, #0, #0x20
    // 0xe6b10c: lsl             x11, x1, #8
    // 0xe6b110: orr             x1, x0, x11
    // 0xe6b114: r17 = 18761
    //     0xe6b114: movz            x17, #0x4949
    // 0xe6b118: cmp             x1, x17
    // 0xe6b11c: b.ne            #0xe6b12c
    // 0xe6b120: r11 = Instance_Endian
    //     0xe6b120: add             x11, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b124: ldr             x11, [x11, #0x8b8]
    // 0xe6b128: b               #0xe6b140
    // 0xe6b12c: r17 = 19789
    //     0xe6b12c: movz            x17, #0x4d4d
    // 0xe6b130: cmp             x1, x17
    // 0xe6b134: b.ne            #0xe6b390
    // 0xe6b138: r11 = Instance_Endian
    //     0xe6b138: add             x11, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0xe6b13c: ldr             x11, [x11, #0x390]
    // 0xe6b140: stur            x11, [fp, #-0x18]
    // 0xe6b144: add             x12, x6, #2
    // 0xe6b148: mov             x0, x7
    // 0xe6b14c: mov             x1, x12
    // 0xe6b150: cmp             x1, x0
    // 0xe6b154: b.hs            #0xe6b3ac
    // 0xe6b158: add             x0, x10, x12
    // 0xe6b15c: LoadField: r1 = r9->field_7
    //     0xe6b15c: ldur            x1, [x9, #7]
    // 0xe6b160: ldrh            w7, [x1, x0]
    // 0xe6b164: r16 = Instance_Endian
    //     0xe6b164: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b168: ldr             x16, [x16, #0x8b8]
    // 0xe6b16c: cmp             w11, w16
    // 0xe6b170: b.ne            #0xe6b180
    // 0xe6b174: cmp             x7, #0x2a
    // 0xe6b178: b.eq            #0xe6b1c0
    // 0xe6b17c: b               #0xe6b1b0
    // 0xe6b180: mov             x0, x7
    // 0xe6b184: ubfx            x0, x0, #0, #0x20
    // 0xe6b188: and             x1, x0, x3
    // 0xe6b18c: ubfx            x1, x1, #0, #0x20
    // 0xe6b190: asr             x0, x1, #8
    // 0xe6b194: ubfx            x7, x7, #0, #0x20
    // 0xe6b198: and             x1, x7, x2
    // 0xe6b19c: ubfx            x1, x1, #0, #0x20
    // 0xe6b1a0: lsl             x2, x1, #8
    // 0xe6b1a4: orr             x1, x0, x2
    // 0xe6b1a8: cmp             x1, #0x2a
    // 0xe6b1ac: b.eq            #0xe6b1c0
    // 0xe6b1b0: r0 = Null
    //     0xe6b1b0: mov             x0, NULL
    // 0xe6b1b4: LeaveFrame
    //     0xe6b1b4: mov             SP, fp
    //     0xe6b1b8: ldp             fp, lr, [SP], #0x10
    // 0xe6b1bc: ret
    //     0xe6b1bc: ret             
    // 0xe6b1c0: add             x2, x6, #4
    // 0xe6b1c4: sub             x0, x5, #3
    // 0xe6b1c8: mov             x1, x2
    // 0xe6b1cc: cmp             x1, x0
    // 0xe6b1d0: b.hs            #0xe6b3b0
    // 0xe6b1d4: add             x0, x10, x2
    // 0xe6b1d8: LoadField: r1 = r9->field_7
    //     0xe6b1d8: ldur            x1, [x9, #7]
    // 0xe6b1dc: ldr             w2, [x1, x0]
    // 0xe6b1e0: r16 = Instance_Endian
    //     0xe6b1e0: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b1e4: ldr             x16, [x16, #0x8b8]
    // 0xe6b1e8: cmp             w11, w16
    // 0xe6b1ec: b.ne            #0xe6b1fc
    // 0xe6b1f0: mov             x0, x2
    // 0xe6b1f4: ubfx            x0, x0, #0, #0x20
    // 0xe6b1f8: b               #0xe6b25c
    // 0xe6b1fc: r5 = 4278255360
    //     0xe6b1fc: movz            x5, #0xff00
    //     0xe6b200: movk            x5, #0xff00, lsl #16
    // 0xe6b204: r3 = 16711935
    //     0xe6b204: movz            x3, #0xff
    //     0xe6b208: movk            x3, #0xff, lsl #16
    // 0xe6b20c: r1 = 4294901760
    //     0xe6b20c: orr             x1, xzr, #0xffff0000
    // 0xe6b210: r0 = 65535
    //     0xe6b210: orr             x0, xzr, #0xffff
    // 0xe6b214: and             x7, x2, x5
    // 0xe6b218: ubfx            x7, x7, #0, #0x20
    // 0xe6b21c: asr             x5, x7, #8
    // 0xe6b220: and             x7, x2, x3
    // 0xe6b224: ubfx            x7, x7, #0, #0x20
    // 0xe6b228: lsl             x2, x7, #8
    // 0xe6b22c: orr             x3, x5, x2
    // 0xe6b230: mov             x2, x3
    // 0xe6b234: ubfx            x2, x2, #0, #0x20
    // 0xe6b238: and             x5, x2, x1
    // 0xe6b23c: ubfx            x5, x5, #0, #0x20
    // 0xe6b240: asr             x1, x5, #0x10
    // 0xe6b244: ubfx            x3, x3, #0, #0x20
    // 0xe6b248: and             x2, x3, x0
    // 0xe6b24c: ubfx            x2, x2, #0, #0x20
    // 0xe6b250: lsl             x0, x2, #0x10
    // 0xe6b254: orr             x2, x1, x0
    // 0xe6b258: mov             x0, x2
    // 0xe6b25c: cmp             x0, #8
    // 0xe6b260: b.ge            #0xe6b274
    // 0xe6b264: r0 = Null
    //     0xe6b264: mov             x0, NULL
    // 0xe6b268: LeaveFrame
    //     0xe6b268: mov             SP, fp
    //     0xe6b26c: ldp             fp, lr, [SP], #0x10
    // 0xe6b270: ret
    //     0xe6b270: ret             
    // 0xe6b274: add             x3, x6, x0
    // 0xe6b278: mov             x1, x4
    // 0xe6b27c: mov             x2, x6
    // 0xe6b280: mov             x5, x11
    // 0xe6b284: r0 = _readTags()
    //     0xe6b284: bl              #0xe6b3b4  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_readTags
    // 0xe6b288: mov             x1, x0
    // 0xe6b28c: r2 = Instance_PdfExifTag
    //     0xe6b28c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e5f0] Obj!PdfExifTag@e2fba1
    //     0xe6b290: ldr             x2, [x2, #0x5f0]
    // 0xe6b294: stur            x0, [fp, #-0x30]
    // 0xe6b298: r0 = containsKey()
    //     0xe6b298: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xe6b29c: tbnz            w0, #4, #0xe6b380
    // 0xe6b2a0: ldur            x0, [fp, #-0x30]
    // 0xe6b2a4: mov             x1, x0
    // 0xe6b2a8: r2 = Instance_PdfExifTag
    //     0xe6b2a8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e5f0] Obj!PdfExifTag@e2fba1
    //     0xe6b2ac: ldr             x2, [x2, #0x5f0]
    // 0xe6b2b0: r0 = _getValueOrData()
    //     0xe6b2b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe6b2b4: ldur            x3, [fp, #-0x30]
    // 0xe6b2b8: LoadField: r1 = r3->field_f
    //     0xe6b2b8: ldur            w1, [x3, #0xf]
    // 0xe6b2bc: DecompressPointer r1
    //     0xe6b2bc: add             x1, x1, HEAP, lsl #32
    // 0xe6b2c0: cmp             w1, w0
    // 0xe6b2c4: b.ne            #0xe6b2d0
    // 0xe6b2c8: r4 = Null
    //     0xe6b2c8: mov             x4, NULL
    // 0xe6b2cc: b               #0xe6b2d4
    // 0xe6b2d0: mov             x4, x0
    // 0xe6b2d4: mov             x0, x4
    // 0xe6b2d8: stur            x4, [fp, #-0x38]
    // 0xe6b2dc: r2 = Null
    //     0xe6b2dc: mov             x2, NULL
    // 0xe6b2e0: r1 = Null
    //     0xe6b2e0: mov             x1, NULL
    // 0xe6b2e4: branchIfSmi(r0, 0xe6b30c)
    //     0xe6b2e4: tbz             w0, #0, #0xe6b30c
    // 0xe6b2e8: r4 = LoadClassIdInstr(r0)
    //     0xe6b2e8: ldur            x4, [x0, #-1]
    //     0xe6b2ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe6b2f0: sub             x4, x4, #0x3c
    // 0xe6b2f4: cmp             x4, #2
    // 0xe6b2f8: b.ls            #0xe6b30c
    // 0xe6b2fc: r8 = num
    //     0xe6b2fc: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xe6b300: r3 = Null
    //     0xe6b300: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e5f8] Null
    //     0xe6b304: ldr             x3, [x3, #0x5f8]
    // 0xe6b308: r0 = num()
    //     0xe6b308: bl              #0xed4df4  ; IsType_num_Stub
    // 0xe6b30c: ldur            x16, [fp, #-0x20]
    // 0xe6b310: ldur            lr, [fp, #-0x38]
    // 0xe6b314: stp             lr, x16, [SP]
    // 0xe6b318: r0 = +()
    //     0xe6b318: bl              #0xebf9fc  ; [dart:core] _IntegerImplementation::+
    // 0xe6b31c: mov             x3, x0
    // 0xe6b320: r2 = Null
    //     0xe6b320: mov             x2, NULL
    // 0xe6b324: r1 = Null
    //     0xe6b324: mov             x1, NULL
    // 0xe6b328: stur            x3, [fp, #-0x20]
    // 0xe6b32c: branchIfSmi(r0, 0xe6b354)
    //     0xe6b32c: tbz             w0, #0, #0xe6b354
    // 0xe6b330: r4 = LoadClassIdInstr(r0)
    //     0xe6b330: ldur            x4, [x0, #-1]
    //     0xe6b334: ubfx            x4, x4, #0xc, #0x14
    // 0xe6b338: sub             x4, x4, #0x3c
    // 0xe6b33c: cmp             x4, #1
    // 0xe6b340: b.ls            #0xe6b354
    // 0xe6b344: r8 = int
    //     0xe6b344: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe6b348: r3 = Null
    //     0xe6b348: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e608] Null
    //     0xe6b34c: ldr             x3, [x3, #0x608]
    // 0xe6b350: r0 = int()
    //     0xe6b350: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe6b354: ldur            x0, [fp, #-0x20]
    // 0xe6b358: r3 = LoadInt32Instr(r0)
    //     0xe6b358: sbfx            x3, x0, #1, #0x1f
    //     0xe6b35c: tbz             w0, #0, #0xe6b364
    //     0xe6b360: ldur            x3, [x0, #7]
    // 0xe6b364: ldur            x1, [fp, #-8]
    // 0xe6b368: ldur            x2, [fp, #-0x28]
    // 0xe6b36c: ldur            x5, [fp, #-0x18]
    // 0xe6b370: r0 = _readTags()
    //     0xe6b370: bl              #0xe6b3b4  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_readTags
    // 0xe6b374: ldur            x1, [fp, #-0x30]
    // 0xe6b378: mov             x2, x0
    // 0xe6b37c: r0 = addAll()
    //     0xe6b37c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xe6b380: ldur            x0, [fp, #-0x30]
    // 0xe6b384: LeaveFrame
    //     0xe6b384: mov             SP, fp
    //     0xe6b388: ldp             fp, lr, [SP], #0x10
    // 0xe6b38c: ret
    //     0xe6b38c: ret             
    // 0xe6b390: r0 = Null
    //     0xe6b390: mov             x0, NULL
    // 0xe6b394: LeaveFrame
    //     0xe6b394: mov             SP, fp
    //     0xe6b398: ldp             fp, lr, [SP], #0x10
    // 0xe6b39c: ret
    //     0xe6b39c: ret             
    // 0xe6b3a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6b3a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6b3a4: b               #0xe6b038
    // 0xe6b3a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b3a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6b3ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b3ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6b3b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b3b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _readTags(/* No info */) {
    // ** addr: 0xe6b3b4, size: 0x28c
    // 0xe6b3b4: EnterFrame
    //     0xe6b3b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe6b3b8: mov             fp, SP
    // 0xe6b3bc: AllocStack(0x80)
    //     0xe6b3bc: sub             SP, SP, #0x80
    // 0xe6b3c0: SetupParameters(dynamic _ /* r1 => r4, fp-0x28 */, dynamic _ /* r2 => r3, fp-0x30 */, dynamic _ /* r3 => r2, fp-0x38 */, dynamic _ /* r5 => r5, fp-0x40 */)
    //     0xe6b3c0: mov             x4, x1
    //     0xe6b3c4: stur            x2, [fp, #-0x30]
    //     0xe6b3c8: mov             x16, x3
    //     0xe6b3cc: mov             x3, x2
    //     0xe6b3d0: mov             x2, x16
    //     0xe6b3d4: stur            x1, [fp, #-0x28]
    //     0xe6b3d8: stur            x2, [fp, #-0x38]
    //     0xe6b3dc: stur            x5, [fp, #-0x40]
    // 0xe6b3e0: CheckStackOverflow
    //     0xe6b3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6b3e4: cmp             SP, x16
    //     0xe6b3e8: b.ls            #0xe6b628
    // 0xe6b3ec: LoadField: r0 = r4->field_13
    //     0xe6b3ec: ldur            w0, [x4, #0x13]
    // 0xe6b3f0: r1 = LoadInt32Instr(r0)
    //     0xe6b3f0: sbfx            x1, x0, #1, #0x1f
    // 0xe6b3f4: sub             x6, x1, #1
    // 0xe6b3f8: mov             x0, x6
    // 0xe6b3fc: mov             x1, x2
    // 0xe6b400: stur            x6, [fp, #-0x20]
    // 0xe6b404: cmp             x1, x0
    // 0xe6b408: b.hs            #0xe6b630
    // 0xe6b40c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xe6b40c: ldur            w0, [x4, #0x17]
    // 0xe6b410: DecompressPointer r0
    //     0xe6b410: add             x0, x0, HEAP, lsl #32
    // 0xe6b414: stur            x0, [fp, #-0x18]
    // 0xe6b418: LoadField: r1 = r4->field_1b
    //     0xe6b418: ldur            w1, [x4, #0x1b]
    // 0xe6b41c: r7 = LoadInt32Instr(r1)
    //     0xe6b41c: sbfx            x7, x1, #1, #0x1f
    // 0xe6b420: stur            x7, [fp, #-0x10]
    // 0xe6b424: add             x1, x7, x2
    // 0xe6b428: LoadField: r8 = r0->field_7
    //     0xe6b428: ldur            x8, [x0, #7]
    // 0xe6b42c: ldrh            w9, [x8, x1]
    // 0xe6b430: r16 = Instance_Endian
    //     0xe6b430: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b434: ldr             x16, [x16, #0x8b8]
    // 0xe6b438: cmp             w5, w16
    // 0xe6b43c: b.ne            #0xe6b44c
    // 0xe6b440: r8 = 65280
    //     0xe6b440: orr             x8, xzr, #0xff00
    // 0xe6b444: r1 = 255
    //     0xe6b444: movz            x1, #0xff
    // 0xe6b448: b               #0xe6b480
    // 0xe6b44c: r8 = 65280
    //     0xe6b44c: orr             x8, xzr, #0xff00
    // 0xe6b450: r1 = 255
    //     0xe6b450: movz            x1, #0xff
    // 0xe6b454: mov             x10, x9
    // 0xe6b458: ubfx            x10, x10, #0, #0x20
    // 0xe6b45c: and             x11, x10, x8
    // 0xe6b460: ubfx            x11, x11, #0, #0x20
    // 0xe6b464: asr             x10, x11, #8
    // 0xe6b468: ubfx            x9, x9, #0, #0x20
    // 0xe6b46c: and             x11, x9, x1
    // 0xe6b470: ubfx            x11, x11, #0, #0x20
    // 0xe6b474: lsl             x9, x11, #8
    // 0xe6b478: orr             x11, x10, x9
    // 0xe6b47c: mov             x9, x11
    // 0xe6b480: stur            x9, [fp, #-8]
    // 0xe6b484: r16 = <PdfExifTag, dynamic>
    //     0xe6b484: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e5e0] TypeArguments: <PdfExifTag, dynamic>
    //     0xe6b488: ldr             x16, [x16, #0x5e0]
    // 0xe6b48c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe6b490: stp             lr, x16, [SP]
    // 0xe6b494: r0 = Map._fromLiteral()
    //     0xe6b494: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe6b498: mov             x3, x0
    // 0xe6b49c: r2 = _ConstMap len:89
    //     0xe6b49c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e618] Map<int, PdfExifTag>(89)
    //     0xe6b4a0: ldr             x2, [x2, #0x618]
    // 0xe6b4a4: stur            x3, [fp, #-0x68]
    // 0xe6b4a8: LoadField: r4 = r2->field_f
    //     0xe6b4a8: ldur            w4, [x2, #0xf]
    // 0xe6b4ac: DecompressPointer r4
    //     0xe6b4ac: add             x4, x4, HEAP, lsl #32
    // 0xe6b4b0: stur            x4, [fp, #-0x60]
    // 0xe6b4b4: r12 = 0
    //     0xe6b4b4: movz            x12, #0
    // 0xe6b4b8: ldur            x6, [fp, #-0x38]
    // 0xe6b4bc: ldur            x5, [fp, #-0x40]
    // 0xe6b4c0: ldur            x11, [fp, #-8]
    // 0xe6b4c4: ldur            x7, [fp, #-0x18]
    // 0xe6b4c8: ldur            x8, [fp, #-0x10]
    // 0xe6b4cc: r10 = 65280
    //     0xe6b4cc: orr             x10, xzr, #0xff00
    // 0xe6b4d0: r9 = 255
    //     0xe6b4d0: movz            x9, #0xff
    // 0xe6b4d4: stur            x12, [fp, #-0x58]
    // 0xe6b4d8: CheckStackOverflow
    //     0xe6b4d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6b4dc: cmp             SP, x16
    //     0xe6b4e0: b.ls            #0xe6b634
    // 0xe6b4e4: cmp             x12, x11
    // 0xe6b4e8: b.ge            #0xe6b618
    // 0xe6b4ec: r16 = 12
    //     0xe6b4ec: movz            x16, #0xc
    // 0xe6b4f0: mul             x0, x12, x16
    // 0xe6b4f4: add             x1, x6, x0
    // 0xe6b4f8: add             x13, x1, #2
    // 0xe6b4fc: ldur            x0, [fp, #-0x20]
    // 0xe6b500: mov             x1, x13
    // 0xe6b504: stur            x13, [fp, #-0x50]
    // 0xe6b508: cmp             x1, x0
    // 0xe6b50c: b.hs            #0xe6b63c
    // 0xe6b510: add             x0, x8, x13
    // 0xe6b514: LoadField: r1 = r7->field_7
    //     0xe6b514: ldur            x1, [x7, #7]
    // 0xe6b518: ldrh            w14, [x1, x0]
    // 0xe6b51c: r16 = Instance_Endian
    //     0xe6b51c: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b520: ldr             x16, [x16, #0x8b8]
    // 0xe6b524: cmp             w5, w16
    // 0xe6b528: b.ne            #0xe6b534
    // 0xe6b52c: mov             x0, x14
    // 0xe6b530: b               #0xe6b560
    // 0xe6b534: mov             x0, x14
    // 0xe6b538: ubfx            x0, x0, #0, #0x20
    // 0xe6b53c: and             x1, x0, x10
    // 0xe6b540: ubfx            x1, x1, #0, #0x20
    // 0xe6b544: asr             x0, x1, #8
    // 0xe6b548: ubfx            x14, x14, #0, #0x20
    // 0xe6b54c: and             x1, x14, x9
    // 0xe6b550: ubfx            x1, x1, #0, #0x20
    // 0xe6b554: lsl             x14, x1, #8
    // 0xe6b558: orr             x1, x0, x14
    // 0xe6b55c: mov             x0, x1
    // 0xe6b560: stur            x0, [fp, #-0x48]
    // 0xe6b564: LoadField: r1 = r2->field_1b
    //     0xe6b564: ldur            w1, [x2, #0x1b]
    // 0xe6b568: DecompressPointer r1
    //     0xe6b568: add             x1, x1, HEAP, lsl #32
    // 0xe6b56c: cmp             w1, NULL
    // 0xe6b570: b.ne            #0xe6b57c
    // 0xe6b574: mov             x1, x2
    // 0xe6b578: r0 = _createIndex()
    //     0xe6b578: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xe6b57c: ldur            x1, [fp, #-0x48]
    // 0xe6b580: ldur            x0, [fp, #-0x60]
    // 0xe6b584: lsl             x2, x1, #1
    // 0xe6b588: r1 = _ConstMap len:89
    //     0xe6b588: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e618] Map<int, PdfExifTag>(89)
    //     0xe6b58c: ldr             x1, [x1, #0x618]
    // 0xe6b590: r0 = _getValueOrData()
    //     0xe6b590: bl              #0xeb9fb0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe6b594: mov             x1, x0
    // 0xe6b598: ldur            x0, [fp, #-0x60]
    // 0xe6b59c: cmp             w0, w1
    // 0xe6b5a0: b.ne            #0xe6b5ac
    // 0xe6b5a4: r4 = Null
    //     0xe6b5a4: mov             x4, NULL
    // 0xe6b5a8: b               #0xe6b5b0
    // 0xe6b5ac: mov             x4, x1
    // 0xe6b5b0: stur            x4, [fp, #-0x70]
    // 0xe6b5b4: cmp             w4, NULL
    // 0xe6b5b8: b.eq            #0xe6b5fc
    // 0xe6b5bc: ldur            x6, [fp, #-0x68]
    // 0xe6b5c0: ldur            x1, [fp, #-0x28]
    // 0xe6b5c4: ldur            x2, [fp, #-0x50]
    // 0xe6b5c8: ldur            x3, [fp, #-0x30]
    // 0xe6b5cc: ldur            x5, [fp, #-0x40]
    // 0xe6b5d0: r0 = _readTagValue()
    //     0xe6b5d0: bl              #0xe6b640  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_readTagValue
    // 0xe6b5d4: ldur            x4, [fp, #-0x68]
    // 0xe6b5d8: r1 = LoadClassIdInstr(r4)
    //     0xe6b5d8: ldur            x1, [x4, #-1]
    //     0xe6b5dc: ubfx            x1, x1, #0xc, #0x14
    // 0xe6b5e0: mov             x3, x0
    // 0xe6b5e4: mov             x0, x1
    // 0xe6b5e8: mov             x1, x4
    // 0xe6b5ec: ldur            x2, [fp, #-0x70]
    // 0xe6b5f0: r0 = GDT[cid_x0 + -0x10d]()
    //     0xe6b5f0: sub             lr, x0, #0x10d
    //     0xe6b5f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe6b5f8: blr             lr
    // 0xe6b5fc: ldur            x1, [fp, #-0x58]
    // 0xe6b600: add             x12, x1, #1
    // 0xe6b604: ldur            x3, [fp, #-0x68]
    // 0xe6b608: ldur            x4, [fp, #-0x60]
    // 0xe6b60c: r2 = _ConstMap len:89
    //     0xe6b60c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e618] Map<int, PdfExifTag>(89)
    //     0xe6b610: ldr             x2, [x2, #0x618]
    // 0xe6b614: b               #0xe6b4b8
    // 0xe6b618: ldur            x0, [fp, #-0x68]
    // 0xe6b61c: LeaveFrame
    //     0xe6b61c: mov             SP, fp
    //     0xe6b620: ldp             fp, lr, [SP], #0x10
    // 0xe6b624: ret
    //     0xe6b624: ret             
    // 0xe6b628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6b628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6b62c: b               #0xe6b3ec
    // 0xe6b630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b630: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6b634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6b634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6b638: b               #0xe6b4e4
    // 0xe6b63c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6b63c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _readTagValue(/* No info */) {
    // ** addr: 0xe6b640, size: 0x19dc
    // 0xe6b640: EnterFrame
    //     0xe6b640: stp             fp, lr, [SP, #-0x10]!
    //     0xe6b644: mov             fp, SP
    // 0xe6b648: AllocStack(0x70)
    //     0xe6b648: sub             SP, SP, #0x70
    // 0xe6b64c: SetupParameters(dynamic _ /* r1 => r4 */, dynamic _ /* r5 => r5, fp-0x40 */)
    //     0xe6b64c: mov             x4, x1
    //     0xe6b650: stur            x5, [fp, #-0x40]
    // 0xe6b654: CheckStackOverflow
    //     0xe6b654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6b658: cmp             SP, x16
    //     0xe6b65c: b.ls            #0xe6cf44
    // 0xe6b660: add             x6, x2, #2
    // 0xe6b664: LoadField: r0 = r4->field_13
    //     0xe6b664: ldur            w0, [x4, #0x13]
    // 0xe6b668: r7 = LoadInt32Instr(r0)
    //     0xe6b668: sbfx            x7, x0, #1, #0x1f
    // 0xe6b66c: stur            x7, [fp, #-0x10]
    // 0xe6b670: sub             x8, x7, #1
    // 0xe6b674: mov             x0, x8
    // 0xe6b678: mov             x1, x6
    // 0xe6b67c: stur            x8, [fp, #-0x38]
    // 0xe6b680: cmp             x1, x0
    // 0xe6b684: b.hs            #0xe6cf4c
    // 0xe6b688: ArrayLoad: r9 = r4[0]  ; List_4
    //     0xe6b688: ldur            w9, [x4, #0x17]
    // 0xe6b68c: DecompressPointer r9
    //     0xe6b68c: add             x9, x9, HEAP, lsl #32
    // 0xe6b690: stur            x9, [fp, #-0x30]
    // 0xe6b694: LoadField: r0 = r4->field_1b
    //     0xe6b694: ldur            w0, [x4, #0x1b]
    // 0xe6b698: r10 = LoadInt32Instr(r0)
    //     0xe6b698: sbfx            x10, x0, #1, #0x1f
    // 0xe6b69c: stur            x10, [fp, #-0x28]
    // 0xe6b6a0: add             x0, x10, x6
    // 0xe6b6a4: LoadField: r1 = r9->field_7
    //     0xe6b6a4: ldur            x1, [x9, #7]
    // 0xe6b6a8: ldrh            w6, [x1, x0]
    // 0xe6b6ac: r16 = Instance_Endian
    //     0xe6b6ac: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b6b0: ldr             x16, [x16, #0x8b8]
    // 0xe6b6b4: cmp             w5, w16
    // 0xe6b6b8: b.ne            #0xe6b6c8
    // 0xe6b6bc: r12 = 65280
    //     0xe6b6bc: orr             x12, xzr, #0xff00
    // 0xe6b6c0: r11 = 255
    //     0xe6b6c0: movz            x11, #0xff
    // 0xe6b6c4: b               #0xe6b6fc
    // 0xe6b6c8: r12 = 65280
    //     0xe6b6c8: orr             x12, xzr, #0xff00
    // 0xe6b6cc: r11 = 255
    //     0xe6b6cc: movz            x11, #0xff
    // 0xe6b6d0: mov             x0, x6
    // 0xe6b6d4: ubfx            x0, x0, #0, #0x20
    // 0xe6b6d8: and             x1, x0, x12
    // 0xe6b6dc: ubfx            x1, x1, #0, #0x20
    // 0xe6b6e0: asr             x0, x1, #8
    // 0xe6b6e4: ubfx            x6, x6, #0, #0x20
    // 0xe6b6e8: and             x1, x6, x11
    // 0xe6b6ec: ubfx            x1, x1, #0, #0x20
    // 0xe6b6f0: lsl             x6, x1, #8
    // 0xe6b6f4: orr             x1, x0, x6
    // 0xe6b6f8: mov             x6, x1
    // 0xe6b6fc: add             x13, x2, #4
    // 0xe6b700: sub             x14, x7, #3
    // 0xe6b704: mov             x0, x14
    // 0xe6b708: mov             x1, x13
    // 0xe6b70c: stur            x14, [fp, #-0x50]
    // 0xe6b710: cmp             x1, x0
    // 0xe6b714: b.hs            #0xe6cf50
    // 0xe6b718: add             x0, x10, x13
    // 0xe6b71c: LoadField: r1 = r9->field_7
    //     0xe6b71c: ldur            x1, [x9, #7]
    // 0xe6b720: ldr             w13, [x1, x0]
    // 0xe6b724: r16 = Instance_Endian
    //     0xe6b724: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b728: ldr             x16, [x16, #0x8b8]
    // 0xe6b72c: cmp             w5, w16
    // 0xe6b730: b.ne            #0xe6b75c
    // 0xe6b734: mov             x0, x13
    // 0xe6b738: ubfx            x0, x0, #0, #0x20
    // 0xe6b73c: mov             x13, x0
    // 0xe6b740: r24 = 4278255360
    //     0xe6b740: movz            x24, #0xff00
    //     0xe6b744: movk            x24, #0xff00, lsl #16
    // 0xe6b748: r23 = 16711935
    //     0xe6b748: movz            x23, #0xff
    //     0xe6b74c: movk            x23, #0xff, lsl #16
    // 0xe6b750: r20 = 4294901760
    //     0xe6b750: orr             x20, xzr, #0xffff0000
    // 0xe6b754: r19 = 65535
    //     0xe6b754: orr             x19, xzr, #0xffff
    // 0xe6b758: b               #0xe6b7b8
    // 0xe6b75c: r24 = 4278255360
    //     0xe6b75c: movz            x24, #0xff00
    //     0xe6b760: movk            x24, #0xff00, lsl #16
    // 0xe6b764: r23 = 16711935
    //     0xe6b764: movz            x23, #0xff
    //     0xe6b768: movk            x23, #0xff, lsl #16
    // 0xe6b76c: r20 = 4294901760
    //     0xe6b76c: orr             x20, xzr, #0xffff0000
    // 0xe6b770: r19 = 65535
    //     0xe6b770: orr             x19, xzr, #0xffff
    // 0xe6b774: and             x0, x13, x24
    // 0xe6b778: ubfx            x0, x0, #0, #0x20
    // 0xe6b77c: asr             x1, x0, #8
    // 0xe6b780: and             x0, x13, x23
    // 0xe6b784: ubfx            x0, x0, #0, #0x20
    // 0xe6b788: lsl             x13, x0, #8
    // 0xe6b78c: orr             x0, x1, x13
    // 0xe6b790: mov             x1, x0
    // 0xe6b794: ubfx            x1, x1, #0, #0x20
    // 0xe6b798: and             x13, x1, x20
    // 0xe6b79c: ubfx            x13, x13, #0, #0x20
    // 0xe6b7a0: asr             x1, x13, #0x10
    // 0xe6b7a4: ubfx            x0, x0, #0, #0x20
    // 0xe6b7a8: and             x13, x0, x19
    // 0xe6b7ac: ubfx            x13, x13, #0, #0x20
    // 0xe6b7b0: lsl             x0, x13, #0x10
    // 0xe6b7b4: orr             x13, x1, x0
    // 0xe6b7b8: stur            x13, [fp, #-0x20]
    // 0xe6b7bc: add             x25, x2, #8
    // 0xe6b7c0: mov             x0, x14
    // 0xe6b7c4: mov             x1, x25
    // 0xe6b7c8: stur            x25, [fp, #-8]
    // 0xe6b7cc: cmp             x1, x0
    // 0xe6b7d0: b.hs            #0xe6cf54
    // 0xe6b7d4: add             x2, x10, x25
    // 0xe6b7d8: stur            x2, [fp, #-0x70]
    // 0xe6b7dc: LoadField: r0 = r9->field_7
    //     0xe6b7dc: ldur            x0, [x9, #7]
    // 0xe6b7e0: ldr             w1, [x0, x2]
    // 0xe6b7e4: r16 = Instance_Endian
    //     0xe6b7e4: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b7e8: ldr             x16, [x16, #0x8b8]
    // 0xe6b7ec: cmp             w5, w16
    // 0xe6b7f0: b.ne            #0xe6b800
    // 0xe6b7f4: mov             x0, x1
    // 0xe6b7f8: ubfx            x0, x0, #0, #0x20
    // 0xe6b7fc: b               #0xe6b848
    // 0xe6b800: and             x0, x1, x24
    // 0xe6b804: ubfx            x0, x0, #0, #0x20
    // 0xe6b808: asr             x25, x0, #8
    // 0xe6b80c: and             x0, x1, x23
    // 0xe6b810: ubfx            x0, x0, #0, #0x20
    // 0xe6b814: lsl             x7, x0, #8
    // 0xe6b818: orr             x0, x25, x7
    // 0xe6b81c: mov             x7, x0
    // 0xe6b820: ubfx            x7, x7, #0, #0x20
    // 0xe6b824: and             x25, x7, x20
    // 0xe6b828: ubfx            x25, x25, #0, #0x20
    // 0xe6b82c: asr             x7, x25, #0x10
    // 0xe6b830: ubfx            x0, x0, #0, #0x20
    // 0xe6b834: and             x25, x0, x19
    // 0xe6b838: ubfx            x25, x25, #0, #0x20
    // 0xe6b83c: lsl             x0, x25, #0x10
    // 0xe6b840: orr             x25, x7, x0
    // 0xe6b844: mov             x0, x25
    // 0xe6b848: add             x7, x0, x3
    // 0xe6b84c: stur            x7, [fp, #-0x48]
    // 0xe6b850: cmp             x6, #5
    // 0xe6b854: b.gt            #0xe6c010
    // 0xe6b858: cmp             x6, #3
    // 0xe6b85c: b.gt            #0xe6ba20
    // 0xe6b860: cmp             x6, #2
    // 0xe6b864: b.gt            #0xe6b8b0
    // 0xe6b868: cmp             x6, #1
    // 0xe6b86c: b.gt            #0xe6b884
    // 0xe6b870: lsl             x0, x6, #1
    // 0xe6b874: cmp             w0, #2
    // 0xe6b878: b.ne            #0xe6cf34
    // 0xe6b87c: mov             x3, x13
    // 0xe6b880: b               #0xe6c034
    // 0xe6b884: cmp             x13, #4
    // 0xe6b888: b.le            #0xe6b894
    // 0xe6b88c: mov             x2, x7
    // 0xe6b890: b               #0xe6b898
    // 0xe6b894: ldur            x2, [fp, #-8]
    // 0xe6b898: sub             x3, x13, #1
    // 0xe6b89c: mov             x1, x4
    // 0xe6b8a0: r0 = _getStringFromDB()
    //     0xe6b8a0: bl              #0xe6d088  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::_getStringFromDB
    // 0xe6b8a4: LeaveFrame
    //     0xe6b8a4: mov             SP, fp
    //     0xe6b8a8: ldp             fp, lr, [SP], #0x10
    // 0xe6b8ac: ret
    //     0xe6b8ac: ret             
    // 0xe6b8b0: cmp             x13, #1
    // 0xe6b8b4: b.ne            #0xe6b928
    // 0xe6b8b8: mov             x0, x8
    // 0xe6b8bc: ldur            x1, [fp, #-8]
    // 0xe6b8c0: cmp             x1, x0
    // 0xe6b8c4: b.hs            #0xe6cf58
    // 0xe6b8c8: LoadField: r0 = r9->field_7
    //     0xe6b8c8: ldur            x0, [x9, #7]
    // 0xe6b8cc: ldrh            w1, [x0, x2]
    // 0xe6b8d0: r16 = Instance_Endian
    //     0xe6b8d0: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b8d4: ldr             x16, [x16, #0x8b8]
    // 0xe6b8d8: cmp             w5, w16
    // 0xe6b8dc: b.ne            #0xe6b8e8
    // 0xe6b8e0: mov             x0, x1
    // 0xe6b8e4: b               #0xe6b914
    // 0xe6b8e8: mov             x0, x1
    // 0xe6b8ec: ubfx            x0, x0, #0, #0x20
    // 0xe6b8f0: and             x2, x0, x12
    // 0xe6b8f4: ubfx            x2, x2, #0, #0x20
    // 0xe6b8f8: asr             x0, x2, #8
    // 0xe6b8fc: ubfx            x1, x1, #0, #0x20
    // 0xe6b900: and             x2, x1, x11
    // 0xe6b904: ubfx            x2, x2, #0, #0x20
    // 0xe6b908: lsl             x1, x2, #8
    // 0xe6b90c: orr             x2, x0, x1
    // 0xe6b910: mov             x0, x2
    // 0xe6b914: lsl             x1, x0, #1
    // 0xe6b918: mov             x0, x1
    // 0xe6b91c: LeaveFrame
    //     0xe6b91c: mov             SP, fp
    //     0xe6b920: ldp             fp, lr, [SP], #0x10
    // 0xe6b924: ret
    //     0xe6b924: ret             
    // 0xe6b928: cmp             x13, #2
    // 0xe6b92c: b.le            #0xe6b938
    // 0xe6b930: mov             x2, x7
    // 0xe6b934: b               #0xe6b93c
    // 0xe6b938: ldur            x2, [fp, #-8]
    // 0xe6b93c: stur            x2, [fp, #-0x18]
    // 0xe6b940: r0 = BoxInt64Instr(r13)
    //     0xe6b940: sbfiz           x0, x13, #1, #0x1f
    //     0xe6b944: cmp             x13, x0, asr #1
    //     0xe6b948: b.eq            #0xe6b954
    //     0xe6b94c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6b950: stur            x13, [x0, #7]
    // 0xe6b954: mov             x4, x0
    // 0xe6b958: r0 = AllocateUint16Array()
    //     0xe6b958: bl              #0xec1da4  ; AllocateUint16ArrayStub
    // 0xe6b95c: mov             x2, x0
    // 0xe6b960: ldur            x3, [fp, #-0x40]
    // 0xe6b964: ldur            x7, [fp, #-0x18]
    // 0xe6b968: ldur            x9, [fp, #-0x20]
    // 0xe6b96c: ldur            x5, [fp, #-0x30]
    // 0xe6b970: ldur            x8, [fp, #-0x28]
    // 0xe6b974: r10 = 0
    //     0xe6b974: movz            x10, #0
    // 0xe6b978: r6 = 65280
    //     0xe6b978: orr             x6, xzr, #0xff00
    // 0xe6b97c: r4 = 255
    //     0xe6b97c: movz            x4, #0xff
    // 0xe6b980: CheckStackOverflow
    //     0xe6b980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6b984: cmp             SP, x16
    //     0xe6b988: b.ls            #0xe6cf5c
    // 0xe6b98c: cmp             x10, x9
    // 0xe6b990: b.ge            #0xe6ba10
    // 0xe6b994: lsl             x0, x10, #1
    // 0xe6b998: add             x11, x7, x0
    // 0xe6b99c: ldur            x0, [fp, #-0x38]
    // 0xe6b9a0: mov             x1, x11
    // 0xe6b9a4: cmp             x1, x0
    // 0xe6b9a8: b.hs            #0xe6cf64
    // 0xe6b9ac: add             x0, x8, x11
    // 0xe6b9b0: LoadField: r1 = r5->field_7
    //     0xe6b9b0: ldur            x1, [x5, #7]
    // 0xe6b9b4: ldrh            w11, [x1, x0]
    // 0xe6b9b8: r16 = Instance_Endian
    //     0xe6b9b8: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6b9bc: ldr             x16, [x16, #0x8b8]
    // 0xe6b9c0: cmp             w3, w16
    // 0xe6b9c4: b.ne            #0xe6b9d0
    // 0xe6b9c8: mov             x0, x11
    // 0xe6b9cc: b               #0xe6b9fc
    // 0xe6b9d0: mov             x0, x11
    // 0xe6b9d4: ubfx            x0, x0, #0, #0x20
    // 0xe6b9d8: and             x1, x0, x6
    // 0xe6b9dc: ubfx            x1, x1, #0, #0x20
    // 0xe6b9e0: asr             x0, x1, #8
    // 0xe6b9e4: ubfx            x11, x11, #0, #0x20
    // 0xe6b9e8: and             x1, x11, x4
    // 0xe6b9ec: ubfx            x1, x1, #0, #0x20
    // 0xe6b9f0: lsl             x11, x1, #8
    // 0xe6b9f4: orr             x1, x0, x11
    // 0xe6b9f8: mov             x0, x1
    // 0xe6b9fc: ArrayStore: r2[r10] = r0  ; TypeUnknown_2
    //     0xe6b9fc: add             x1, x2, x10, lsl #1
    //     0xe6ba00: sturh           w0, [x1, #0x17]
    // 0xe6ba04: add             x0, x10, #1
    // 0xe6ba08: mov             x10, x0
    // 0xe6ba0c: b               #0xe6b980
    // 0xe6ba10: mov             x0, x2
    // 0xe6ba14: LeaveFrame
    //     0xe6ba14: mov             SP, fp
    //     0xe6ba18: ldp             fp, lr, [SP], #0x10
    // 0xe6ba1c: ret
    //     0xe6ba1c: ret             
    // 0xe6ba20: mov             x3, x5
    // 0xe6ba24: mov             x5, x9
    // 0xe6ba28: mov             x9, x13
    // 0xe6ba2c: mov             x8, x10
    // 0xe6ba30: cmp             x6, #4
    // 0xe6ba34: b.gt            #0xe6bbd8
    // 0xe6ba38: cmp             x9, #1
    // 0xe6ba3c: b.ne            #0xe6bac4
    // 0xe6ba40: r16 = Instance_Endian
    //     0xe6ba40: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6ba44: ldr             x16, [x16, #0x8b8]
    // 0xe6ba48: cmp             w3, w16
    // 0xe6ba4c: b.ne            #0xe6ba60
    // 0xe6ba50: mov             x0, x1
    // 0xe6ba54: ubfx            x0, x0, #0, #0x20
    // 0xe6ba58: mov             x2, x0
    // 0xe6ba5c: b               #0xe6baa4
    // 0xe6ba60: and             x0, x1, x24
    // 0xe6ba64: ubfx            x0, x0, #0, #0x20
    // 0xe6ba68: asr             x2, x0, #8
    // 0xe6ba6c: and             x0, x1, x23
    // 0xe6ba70: ubfx            x0, x0, #0, #0x20
    // 0xe6ba74: lsl             x1, x0, #8
    // 0xe6ba78: orr             x0, x2, x1
    // 0xe6ba7c: mov             x1, x0
    // 0xe6ba80: ubfx            x1, x1, #0, #0x20
    // 0xe6ba84: and             x2, x1, x20
    // 0xe6ba88: ubfx            x2, x2, #0, #0x20
    // 0xe6ba8c: asr             x1, x2, #0x10
    // 0xe6ba90: ubfx            x0, x0, #0, #0x20
    // 0xe6ba94: and             x2, x0, x19
    // 0xe6ba98: ubfx            x2, x2, #0, #0x20
    // 0xe6ba9c: lsl             x0, x2, #0x10
    // 0xe6baa0: orr             x2, x1, x0
    // 0xe6baa4: r0 = BoxInt64Instr(r2)
    //     0xe6baa4: sbfiz           x0, x2, #1, #0x1f
    //     0xe6baa8: cmp             x2, x0, asr #1
    //     0xe6baac: b.eq            #0xe6bab8
    //     0xe6bab0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bab4: stur            x2, [x0, #7]
    // 0xe6bab8: LeaveFrame
    //     0xe6bab8: mov             SP, fp
    //     0xe6babc: ldp             fp, lr, [SP], #0x10
    // 0xe6bac0: ret
    //     0xe6bac0: ret             
    // 0xe6bac4: r0 = BoxInt64Instr(r9)
    //     0xe6bac4: sbfiz           x0, x9, #1, #0x1f
    //     0xe6bac8: cmp             x9, x0, asr #1
    //     0xe6bacc: b.eq            #0xe6bad8
    //     0xe6bad0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bad4: stur            x9, [x0, #7]
    // 0xe6bad8: mov             x4, x0
    // 0xe6badc: r0 = AllocateUint32Array()
    //     0xe6badc: bl              #0xec1c2c  ; AllocateUint32ArrayStub
    // 0xe6bae0: mov             x2, x0
    // 0xe6bae4: ldur            x8, [fp, #-0x40]
    // 0xe6bae8: ldur            x7, [fp, #-0x48]
    // 0xe6baec: ldur            x11, [fp, #-0x20]
    // 0xe6baf0: ldur            x9, [fp, #-0x30]
    // 0xe6baf4: ldur            x10, [fp, #-0x28]
    // 0xe6baf8: r12 = 0
    //     0xe6baf8: movz            x12, #0
    // 0xe6bafc: r6 = 4278255360
    //     0xe6bafc: movz            x6, #0xff00
    //     0xe6bb00: movk            x6, #0xff00, lsl #16
    // 0xe6bb04: r5 = 16711935
    //     0xe6bb04: movz            x5, #0xff
    //     0xe6bb08: movk            x5, #0xff, lsl #16
    // 0xe6bb0c: r4 = 4294901760
    //     0xe6bb0c: orr             x4, xzr, #0xffff0000
    // 0xe6bb10: r3 = 65535
    //     0xe6bb10: orr             x3, xzr, #0xffff
    // 0xe6bb14: CheckStackOverflow
    //     0xe6bb14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6bb18: cmp             SP, x16
    //     0xe6bb1c: b.ls            #0xe6cf68
    // 0xe6bb20: cmp             x12, x11
    // 0xe6bb24: b.ge            #0xe6bbc8
    // 0xe6bb28: lsl             x0, x12, #2
    // 0xe6bb2c: add             x13, x7, x0
    // 0xe6bb30: ldur            x0, [fp, #-0x50]
    // 0xe6bb34: mov             x1, x13
    // 0xe6bb38: cmp             x1, x0
    // 0xe6bb3c: b.hs            #0xe6cf70
    // 0xe6bb40: add             x0, x10, x13
    // 0xe6bb44: LoadField: r1 = r9->field_7
    //     0xe6bb44: ldur            x1, [x9, #7]
    // 0xe6bb48: ldr             w13, [x1, x0]
    // 0xe6bb4c: r16 = Instance_Endian
    //     0xe6bb4c: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6bb50: ldr             x16, [x16, #0x8b8]
    // 0xe6bb54: cmp             w8, w16
    // 0xe6bb58: b.ne            #0xe6bb68
    // 0xe6bb5c: mov             x0, x13
    // 0xe6bb60: ubfx            x0, x0, #0, #0x20
    // 0xe6bb64: b               #0xe6bbb0
    // 0xe6bb68: and             x0, x13, x6
    // 0xe6bb6c: ubfx            x0, x0, #0, #0x20
    // 0xe6bb70: asr             x1, x0, #8
    // 0xe6bb74: and             x0, x13, x5
    // 0xe6bb78: ubfx            x0, x0, #0, #0x20
    // 0xe6bb7c: lsl             x13, x0, #8
    // 0xe6bb80: orr             x0, x1, x13
    // 0xe6bb84: mov             x1, x0
    // 0xe6bb88: ubfx            x1, x1, #0, #0x20
    // 0xe6bb8c: and             x13, x1, x4
    // 0xe6bb90: ubfx            x13, x13, #0, #0x20
    // 0xe6bb94: asr             x1, x13, #0x10
    // 0xe6bb98: ubfx            x0, x0, #0, #0x20
    // 0xe6bb9c: and             x13, x0, x3
    // 0xe6bba0: ubfx            x13, x13, #0, #0x20
    // 0xe6bba4: lsl             x0, x13, #0x10
    // 0xe6bba8: orr             x13, x1, x0
    // 0xe6bbac: mov             x0, x13
    // 0xe6bbb0: ubfx            x0, x0, #0, #0x20
    // 0xe6bbb4: ArrayStore: r2[r12] = r0  ; List_4
    //     0xe6bbb4: add             x1, x2, x12, lsl #2
    //     0xe6bbb8: stur            w0, [x1, #0x17]
    // 0xe6bbbc: add             x0, x12, #1
    // 0xe6bbc0: mov             x12, x0
    // 0xe6bbc4: b               #0xe6bb14
    // 0xe6bbc8: mov             x0, x2
    // 0xe6bbcc: LeaveFrame
    //     0xe6bbcc: mov             SP, fp
    //     0xe6bbd0: ldp             fp, lr, [SP], #0x10
    // 0xe6bbd4: ret
    //     0xe6bbd4: ret             
    // 0xe6bbd8: mov             x10, x8
    // 0xe6bbdc: mov             x8, x3
    // 0xe6bbe0: mov             x11, x9
    // 0xe6bbe4: mov             x9, x5
    // 0xe6bbe8: mov             x6, x24
    // 0xe6bbec: mov             x5, x23
    // 0xe6bbf0: mov             x4, x20
    // 0xe6bbf4: mov             x3, x19
    // 0xe6bbf8: cmp             x11, #1
    // 0xe6bbfc: b.ne            #0xe6bd8c
    // 0xe6bc00: ldur            x0, [fp, #-0x50]
    // 0xe6bc04: mov             x1, x7
    // 0xe6bc08: cmp             x1, x0
    // 0xe6bc0c: b.hs            #0xe6cf74
    // 0xe6bc10: add             x0, x10, x7
    // 0xe6bc14: LoadField: r1 = r9->field_7
    //     0xe6bc14: ldur            x1, [x9, #7]
    // 0xe6bc18: ldr             w2, [x1, x0]
    // 0xe6bc1c: r16 = Instance_Endian
    //     0xe6bc1c: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6bc20: ldr             x16, [x16, #0x8b8]
    // 0xe6bc24: cmp             w8, w16
    // 0xe6bc28: b.ne            #0xe6bc3c
    // 0xe6bc2c: mov             x0, x2
    // 0xe6bc30: ubfx            x0, x0, #0, #0x20
    // 0xe6bc34: mov             x2, x0
    // 0xe6bc38: b               #0xe6bc80
    // 0xe6bc3c: and             x0, x2, x6
    // 0xe6bc40: ubfx            x0, x0, #0, #0x20
    // 0xe6bc44: asr             x1, x0, #8
    // 0xe6bc48: and             x0, x2, x5
    // 0xe6bc4c: ubfx            x0, x0, #0, #0x20
    // 0xe6bc50: lsl             x2, x0, #8
    // 0xe6bc54: orr             x0, x1, x2
    // 0xe6bc58: mov             x1, x0
    // 0xe6bc5c: ubfx            x1, x1, #0, #0x20
    // 0xe6bc60: and             x2, x1, x4
    // 0xe6bc64: ubfx            x2, x2, #0, #0x20
    // 0xe6bc68: asr             x1, x2, #0x10
    // 0xe6bc6c: ubfx            x0, x0, #0, #0x20
    // 0xe6bc70: and             x2, x0, x3
    // 0xe6bc74: ubfx            x2, x2, #0, #0x20
    // 0xe6bc78: lsl             x0, x2, #0x10
    // 0xe6bc7c: orr             x2, x1, x0
    // 0xe6bc80: add             x11, x7, #4
    // 0xe6bc84: ldur            x0, [fp, #-0x50]
    // 0xe6bc88: mov             x1, x11
    // 0xe6bc8c: cmp             x1, x0
    // 0xe6bc90: b.hs            #0xe6cf78
    // 0xe6bc94: add             x0, x10, x11
    // 0xe6bc98: LoadField: r1 = r9->field_7
    //     0xe6bc98: ldur            x1, [x9, #7]
    // 0xe6bc9c: ldr             w7, [x1, x0]
    // 0xe6bca0: r16 = Instance_Endian
    //     0xe6bca0: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6bca4: ldr             x16, [x16, #0x8b8]
    // 0xe6bca8: cmp             w8, w16
    // 0xe6bcac: b.ne            #0xe6bcc0
    // 0xe6bcb0: mov             x0, x7
    // 0xe6bcb4: ubfx            x0, x0, #0, #0x20
    // 0xe6bcb8: mov             x4, x0
    // 0xe6bcbc: b               #0xe6bd08
    // 0xe6bcc0: and             x0, x7, x6
    // 0xe6bcc4: ubfx            x0, x0, #0, #0x20
    // 0xe6bcc8: asr             x1, x0, #8
    // 0xe6bccc: and             x0, x7, x5
    // 0xe6bcd0: ubfx            x0, x0, #0, #0x20
    // 0xe6bcd4: lsl             x5, x0, #8
    // 0xe6bcd8: orr             x0, x1, x5
    // 0xe6bcdc: mov             x1, x0
    // 0xe6bce0: ubfx            x1, x1, #0, #0x20
    // 0xe6bce4: and             x5, x1, x4
    // 0xe6bce8: ubfx            x5, x5, #0, #0x20
    // 0xe6bcec: asr             x1, x5, #0x10
    // 0xe6bcf0: ubfx            x0, x0, #0, #0x20
    // 0xe6bcf4: and             x4, x0, x3
    // 0xe6bcf8: ubfx            x4, x4, #0, #0x20
    // 0xe6bcfc: lsl             x0, x4, #0x10
    // 0xe6bd00: orr             x3, x1, x0
    // 0xe6bd04: mov             x4, x3
    // 0xe6bd08: r3 = 4
    //     0xe6bd08: movz            x3, #0x4
    // 0xe6bd0c: stur            x4, [fp, #-0x18]
    // 0xe6bd10: r0 = BoxInt64Instr(r2)
    //     0xe6bd10: sbfiz           x0, x2, #1, #0x1f
    //     0xe6bd14: cmp             x2, x0, asr #1
    //     0xe6bd18: b.eq            #0xe6bd24
    //     0xe6bd1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bd20: stur            x2, [x0, #7]
    // 0xe6bd24: mov             x2, x3
    // 0xe6bd28: r1 = Null
    //     0xe6bd28: mov             x1, NULL
    // 0xe6bd2c: stur            x0, [fp, #-0x58]
    // 0xe6bd30: r0 = AllocateArray()
    //     0xe6bd30: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6bd34: mov             x2, x0
    // 0xe6bd38: ldur            x0, [fp, #-0x58]
    // 0xe6bd3c: stur            x2, [fp, #-0x60]
    // 0xe6bd40: StoreField: r2->field_f = r0
    //     0xe6bd40: stur            w0, [x2, #0xf]
    // 0xe6bd44: ldur            x3, [fp, #-0x18]
    // 0xe6bd48: r0 = BoxInt64Instr(r3)
    //     0xe6bd48: sbfiz           x0, x3, #1, #0x1f
    //     0xe6bd4c: cmp             x3, x0, asr #1
    //     0xe6bd50: b.eq            #0xe6bd5c
    //     0xe6bd54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bd58: stur            x3, [x0, #7]
    // 0xe6bd5c: StoreField: r2->field_13 = r0
    //     0xe6bd5c: stur            w0, [x2, #0x13]
    // 0xe6bd60: r1 = <int>
    //     0xe6bd60: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6bd64: r0 = AllocateGrowableArray()
    //     0xe6bd64: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6bd68: mov             x1, x0
    // 0xe6bd6c: ldur            x0, [fp, #-0x60]
    // 0xe6bd70: StoreField: r1->field_f = r0
    //     0xe6bd70: stur            w0, [x1, #0xf]
    // 0xe6bd74: r0 = 4
    //     0xe6bd74: movz            x0, #0x4
    // 0xe6bd78: StoreField: r1->field_b = r0
    //     0xe6bd78: stur            w0, [x1, #0xb]
    // 0xe6bd7c: mov             x0, x1
    // 0xe6bd80: LeaveFrame
    //     0xe6bd80: mov             SP, fp
    //     0xe6bd84: ldp             fp, lr, [SP], #0x10
    // 0xe6bd88: ret
    //     0xe6bd88: ret             
    // 0xe6bd8c: r0 = 4
    //     0xe6bd8c: movz            x0, #0x4
    // 0xe6bd90: r1 = <List<int>>
    //     0xe6bd90: ldr             x1, [PP, #0x14c0]  ; [pp+0x14c0] TypeArguments: <List<int>>
    // 0xe6bd94: r2 = 0
    //     0xe6bd94: movz            x2, #0
    // 0xe6bd98: r0 = _GrowableList()
    //     0xe6bd98: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe6bd9c: mov             x3, x0
    // 0xe6bda0: stur            x3, [fp, #-0x60]
    // 0xe6bda4: r14 = 0
    //     0xe6bda4: movz            x14, #0
    // 0xe6bda8: ldur            x9, [fp, #-0x40]
    // 0xe6bdac: ldur            x8, [fp, #-0x48]
    // 0xe6bdb0: ldur            x12, [fp, #-0x20]
    // 0xe6bdb4: ldur            x10, [fp, #-0x30]
    // 0xe6bdb8: ldur            x11, [fp, #-0x28]
    // 0xe6bdbc: r13 = 4
    //     0xe6bdbc: movz            x13, #0x4
    // 0xe6bdc0: r7 = 4278255360
    //     0xe6bdc0: movz            x7, #0xff00
    //     0xe6bdc4: movk            x7, #0xff00, lsl #16
    // 0xe6bdc8: r6 = 16711935
    //     0xe6bdc8: movz            x6, #0xff
    //     0xe6bdcc: movk            x6, #0xff, lsl #16
    // 0xe6bdd0: r5 = 4294901760
    //     0xe6bdd0: orr             x5, xzr, #0xffff0000
    // 0xe6bdd4: r4 = 65535
    //     0xe6bdd4: orr             x4, xzr, #0xffff
    // 0xe6bdd8: stur            x14, [fp, #-0x38]
    // 0xe6bddc: CheckStackOverflow
    //     0xe6bddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6bde0: cmp             SP, x16
    //     0xe6bde4: b.ls            #0xe6cf7c
    // 0xe6bde8: cmp             x14, x12
    // 0xe6bdec: b.ge            #0xe6bffc
    // 0xe6bdf0: lsl             x0, x14, #3
    // 0xe6bdf4: add             x2, x8, x0
    // 0xe6bdf8: ldur            x0, [fp, #-0x50]
    // 0xe6bdfc: mov             x1, x2
    // 0xe6be00: cmp             x1, x0
    // 0xe6be04: b.hs            #0xe6cf84
    // 0xe6be08: add             x0, x11, x2
    // 0xe6be0c: LoadField: r1 = r10->field_7
    //     0xe6be0c: ldur            x1, [x10, #7]
    // 0xe6be10: ldr             w19, [x1, x0]
    // 0xe6be14: r16 = Instance_Endian
    //     0xe6be14: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6be18: ldr             x16, [x16, #0x8b8]
    // 0xe6be1c: cmp             w9, w16
    // 0xe6be20: b.ne            #0xe6be34
    // 0xe6be24: mov             x0, x19
    // 0xe6be28: ubfx            x0, x0, #0, #0x20
    // 0xe6be2c: mov             x19, x0
    // 0xe6be30: b               #0xe6be78
    // 0xe6be34: and             x0, x19, x7
    // 0xe6be38: ubfx            x0, x0, #0, #0x20
    // 0xe6be3c: asr             x1, x0, #8
    // 0xe6be40: and             x0, x19, x6
    // 0xe6be44: ubfx            x0, x0, #0, #0x20
    // 0xe6be48: lsl             x19, x0, #8
    // 0xe6be4c: orr             x0, x1, x19
    // 0xe6be50: mov             x1, x0
    // 0xe6be54: ubfx            x1, x1, #0, #0x20
    // 0xe6be58: and             x19, x1, x5
    // 0xe6be5c: ubfx            x19, x19, #0, #0x20
    // 0xe6be60: asr             x1, x19, #0x10
    // 0xe6be64: ubfx            x0, x0, #0, #0x20
    // 0xe6be68: and             x19, x0, x4
    // 0xe6be6c: ubfx            x19, x19, #0, #0x20
    // 0xe6be70: lsl             x0, x19, #0x10
    // 0xe6be74: orr             x19, x1, x0
    // 0xe6be78: add             x20, x2, #4
    // 0xe6be7c: ldur            x0, [fp, #-0x50]
    // 0xe6be80: mov             x1, x20
    // 0xe6be84: cmp             x1, x0
    // 0xe6be88: b.hs            #0xe6cf88
    // 0xe6be8c: add             x0, x11, x20
    // 0xe6be90: LoadField: r1 = r10->field_7
    //     0xe6be90: ldur            x1, [x10, #7]
    // 0xe6be94: ldr             w2, [x1, x0]
    // 0xe6be98: r16 = Instance_Endian
    //     0xe6be98: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6be9c: ldr             x16, [x16, #0x8b8]
    // 0xe6bea0: cmp             w9, w16
    // 0xe6bea4: b.ne            #0xe6beb8
    // 0xe6bea8: mov             x0, x2
    // 0xe6beac: ubfx            x0, x0, #0, #0x20
    // 0xe6beb0: mov             x20, x0
    // 0xe6beb4: b               #0xe6bf00
    // 0xe6beb8: and             x0, x2, x7
    // 0xe6bebc: ubfx            x0, x0, #0, #0x20
    // 0xe6bec0: asr             x1, x0, #8
    // 0xe6bec4: and             x0, x2, x6
    // 0xe6bec8: ubfx            x0, x0, #0, #0x20
    // 0xe6becc: lsl             x2, x0, #8
    // 0xe6bed0: orr             x0, x1, x2
    // 0xe6bed4: mov             x1, x0
    // 0xe6bed8: ubfx            x1, x1, #0, #0x20
    // 0xe6bedc: and             x2, x1, x5
    // 0xe6bee0: ubfx            x2, x2, #0, #0x20
    // 0xe6bee4: asr             x1, x2, #0x10
    // 0xe6bee8: ubfx            x0, x0, #0, #0x20
    // 0xe6beec: and             x2, x0, x4
    // 0xe6bef0: ubfx            x2, x2, #0, #0x20
    // 0xe6bef4: lsl             x0, x2, #0x10
    // 0xe6bef8: orr             x2, x1, x0
    // 0xe6befc: mov             x20, x2
    // 0xe6bf00: stur            x20, [fp, #-0x18]
    // 0xe6bf04: r0 = BoxInt64Instr(r19)
    //     0xe6bf04: sbfiz           x0, x19, #1, #0x1f
    //     0xe6bf08: cmp             x19, x0, asr #1
    //     0xe6bf0c: b.eq            #0xe6bf18
    //     0xe6bf10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bf14: stur            x19, [x0, #7]
    // 0xe6bf18: mov             x2, x13
    // 0xe6bf1c: r1 = Null
    //     0xe6bf1c: mov             x1, NULL
    // 0xe6bf20: stur            x0, [fp, #-0x58]
    // 0xe6bf24: r0 = AllocateArray()
    //     0xe6bf24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6bf28: mov             x2, x0
    // 0xe6bf2c: ldur            x0, [fp, #-0x58]
    // 0xe6bf30: stur            x2, [fp, #-0x68]
    // 0xe6bf34: StoreField: r2->field_f = r0
    //     0xe6bf34: stur            w0, [x2, #0xf]
    // 0xe6bf38: ldur            x3, [fp, #-0x18]
    // 0xe6bf3c: r0 = BoxInt64Instr(r3)
    //     0xe6bf3c: sbfiz           x0, x3, #1, #0x1f
    //     0xe6bf40: cmp             x3, x0, asr #1
    //     0xe6bf44: b.eq            #0xe6bf50
    //     0xe6bf48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6bf4c: stur            x3, [x0, #7]
    // 0xe6bf50: StoreField: r2->field_13 = r0
    //     0xe6bf50: stur            w0, [x2, #0x13]
    // 0xe6bf54: r1 = <int>
    //     0xe6bf54: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6bf58: r0 = AllocateGrowableArray()
    //     0xe6bf58: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6bf5c: mov             x2, x0
    // 0xe6bf60: ldur            x0, [fp, #-0x68]
    // 0xe6bf64: stur            x2, [fp, #-0x58]
    // 0xe6bf68: StoreField: r2->field_f = r0
    //     0xe6bf68: stur            w0, [x2, #0xf]
    // 0xe6bf6c: r0 = 4
    //     0xe6bf6c: movz            x0, #0x4
    // 0xe6bf70: StoreField: r2->field_b = r0
    //     0xe6bf70: stur            w0, [x2, #0xb]
    // 0xe6bf74: ldur            x3, [fp, #-0x60]
    // 0xe6bf78: LoadField: r1 = r3->field_b
    //     0xe6bf78: ldur            w1, [x3, #0xb]
    // 0xe6bf7c: LoadField: r4 = r3->field_f
    //     0xe6bf7c: ldur            w4, [x3, #0xf]
    // 0xe6bf80: DecompressPointer r4
    //     0xe6bf80: add             x4, x4, HEAP, lsl #32
    // 0xe6bf84: LoadField: r5 = r4->field_b
    //     0xe6bf84: ldur            w5, [x4, #0xb]
    // 0xe6bf88: r4 = LoadInt32Instr(r1)
    //     0xe6bf88: sbfx            x4, x1, #1, #0x1f
    // 0xe6bf8c: stur            x4, [fp, #-0x18]
    // 0xe6bf90: r1 = LoadInt32Instr(r5)
    //     0xe6bf90: sbfx            x1, x5, #1, #0x1f
    // 0xe6bf94: cmp             x4, x1
    // 0xe6bf98: b.ne            #0xe6bfa4
    // 0xe6bf9c: mov             x1, x3
    // 0xe6bfa0: r0 = _growToNextCapacity()
    //     0xe6bfa0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe6bfa4: ldur            x2, [fp, #-0x60]
    // 0xe6bfa8: ldur            x4, [fp, #-0x38]
    // 0xe6bfac: ldur            x3, [fp, #-0x18]
    // 0xe6bfb0: add             x0, x3, #1
    // 0xe6bfb4: lsl             x1, x0, #1
    // 0xe6bfb8: StoreField: r2->field_b = r1
    //     0xe6bfb8: stur            w1, [x2, #0xb]
    // 0xe6bfbc: LoadField: r1 = r2->field_f
    //     0xe6bfbc: ldur            w1, [x2, #0xf]
    // 0xe6bfc0: DecompressPointer r1
    //     0xe6bfc0: add             x1, x1, HEAP, lsl #32
    // 0xe6bfc4: ldur            x0, [fp, #-0x58]
    // 0xe6bfc8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe6bfc8: add             x25, x1, x3, lsl #2
    //     0xe6bfcc: add             x25, x25, #0xf
    //     0xe6bfd0: str             w0, [x25]
    //     0xe6bfd4: tbz             w0, #0, #0xe6bff0
    //     0xe6bfd8: ldurb           w16, [x1, #-1]
    //     0xe6bfdc: ldurb           w17, [x0, #-1]
    //     0xe6bfe0: and             x16, x17, x16, lsr #2
    //     0xe6bfe4: tst             x16, HEAP, lsr #32
    //     0xe6bfe8: b.eq            #0xe6bff0
    //     0xe6bfec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe6bff0: add             x14, x4, #1
    // 0xe6bff4: mov             x3, x2
    // 0xe6bff8: b               #0xe6bda8
    // 0xe6bffc: mov             x2, x3
    // 0xe6c000: mov             x0, x2
    // 0xe6c004: LeaveFrame
    //     0xe6c004: mov             SP, fp
    //     0xe6c008: ldp             fp, lr, [SP], #0x10
    // 0xe6c00c: ret
    //     0xe6c00c: ret             
    // 0xe6c010: cmp             x6, #7
    // 0xe6c014: b.lt            #0xe6cf34
    // 0xe6c018: cmp             x6, #0xa
    // 0xe6c01c: b.gt            #0xe6c760
    // 0xe6c020: cmp             x6, #9
    // 0xe6c024: b.gt            #0xe6c314
    // 0xe6c028: cmp             x6, #7
    // 0xe6c02c: b.gt            #0xe6c110
    // 0xe6c030: ldur            x3, [fp, #-0x20]
    // 0xe6c034: cmp             x3, #1
    // 0xe6c038: b.ne            #0xe6c068
    // 0xe6c03c: ldur            x5, [fp, #-0x30]
    // 0xe6c040: ldur            x0, [fp, #-0x10]
    // 0xe6c044: ldur            x1, [fp, #-8]
    // 0xe6c048: cmp             x1, x0
    // 0xe6c04c: b.hs            #0xe6cf8c
    // 0xe6c050: LoadField: r0 = r5->field_7
    //     0xe6c050: ldur            x0, [x5, #7]
    // 0xe6c054: ldrb            w1, [x0, x2]
    // 0xe6c058: lsl             x0, x1, #1
    // 0xe6c05c: LeaveFrame
    //     0xe6c05c: mov             SP, fp
    //     0xe6c060: ldp             fp, lr, [SP], #0x10
    // 0xe6c064: ret
    //     0xe6c064: ret             
    // 0xe6c068: ldur            x5, [fp, #-0x30]
    // 0xe6c06c: cmp             x3, #4
    // 0xe6c070: b.le            #0xe6c07c
    // 0xe6c074: ldur            x2, [fp, #-0x48]
    // 0xe6c078: b               #0xe6c080
    // 0xe6c07c: ldur            x2, [fp, #-8]
    // 0xe6c080: stur            x2, [fp, #-0x18]
    // 0xe6c084: r0 = BoxInt64Instr(r3)
    //     0xe6c084: sbfiz           x0, x3, #1, #0x1f
    //     0xe6c088: cmp             x3, x0, asr #1
    //     0xe6c08c: b.eq            #0xe6c098
    //     0xe6c090: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c094: stur            x3, [x0, #7]
    // 0xe6c098: mov             x4, x0
    // 0xe6c09c: r0 = AllocateUint8Array()
    //     0xe6c09c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe6c0a0: mov             x2, x0
    // 0xe6c0a4: ldur            x4, [fp, #-0x18]
    // 0xe6c0a8: ldur            x3, [fp, #-0x20]
    // 0xe6c0ac: ldur            x5, [fp, #-0x30]
    // 0xe6c0b0: ldur            x7, [fp, #-0x28]
    // 0xe6c0b4: r6 = 0
    //     0xe6c0b4: movz            x6, #0
    // 0xe6c0b8: CheckStackOverflow
    //     0xe6c0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6c0bc: cmp             SP, x16
    //     0xe6c0c0: b.ls            #0xe6cf90
    // 0xe6c0c4: cmp             x6, x3
    // 0xe6c0c8: b.ge            #0xe6c100
    // 0xe6c0cc: add             x8, x4, x6
    // 0xe6c0d0: ldur            x0, [fp, #-0x10]
    // 0xe6c0d4: mov             x1, x8
    // 0xe6c0d8: cmp             x1, x0
    // 0xe6c0dc: b.hs            #0xe6cf98
    // 0xe6c0e0: add             x0, x7, x8
    // 0xe6c0e4: LoadField: r1 = r5->field_7
    //     0xe6c0e4: ldur            x1, [x5, #7]
    // 0xe6c0e8: ldrb            w8, [x1, x0]
    // 0xe6c0ec: ArrayStore: r2[r6] = r8  ; TypeUnknown_1
    //     0xe6c0ec: add             x0, x2, x6
    //     0xe6c0f0: strb            w8, [x0, #0x17]
    // 0xe6c0f4: add             x0, x6, #1
    // 0xe6c0f8: mov             x6, x0
    // 0xe6c0fc: b               #0xe6c0b8
    // 0xe6c100: mov             x0, x2
    // 0xe6c104: LeaveFrame
    //     0xe6c104: mov             SP, fp
    //     0xe6c108: ldp             fp, lr, [SP], #0x10
    // 0xe6c10c: ret
    //     0xe6c10c: ret             
    // 0xe6c110: ldur            x3, [fp, #-0x20]
    // 0xe6c114: ldur            x5, [fp, #-0x30]
    // 0xe6c118: ldur            x7, [fp, #-0x28]
    // 0xe6c11c: cmp             x6, #9
    // 0xe6c120: b.lt            #0xe6cf34
    // 0xe6c124: cmp             x3, #1
    // 0xe6c128: b.ne            #0xe6c1dc
    // 0xe6c12c: ldur            x6, [fp, #-0x40]
    // 0xe6c130: LoadField: r0 = r5->field_7
    //     0xe6c130: ldur            x0, [x5, #7]
    // 0xe6c134: ldrsw           x1, [x0, x2]
    // 0xe6c138: r16 = Instance_Endian
    //     0xe6c138: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c13c: ldr             x16, [x16, #0x8b8]
    // 0xe6c140: cmp             w6, w16
    // 0xe6c144: b.ne            #0xe6c158
    // 0xe6c148: mov             x0, x1
    // 0xe6c14c: sxtw            x0, w0
    // 0xe6c150: mov             x2, x0
    // 0xe6c154: b               #0xe6c1bc
    // 0xe6c158: r10 = 4278255360
    //     0xe6c158: movz            x10, #0xff00
    //     0xe6c15c: movk            x10, #0xff00, lsl #16
    // 0xe6c160: r9 = 16711935
    //     0xe6c160: movz            x9, #0xff
    //     0xe6c164: movk            x9, #0xff, lsl #16
    // 0xe6c168: r8 = 4294901760
    //     0xe6c168: orr             x8, xzr, #0xffff0000
    // 0xe6c16c: r2 = 65535
    //     0xe6c16c: orr             x2, xzr, #0xffff
    // 0xe6c170: r12 = 2147483647
    //     0xe6c170: orr             x12, xzr, #0x7fffffff
    // 0xe6c174: r11 = 2147483648
    //     0xe6c174: orr             x11, xzr, #0x80000000
    // 0xe6c178: mov             x0, x1
    // 0xe6c17c: and             x3, x0, x10
    // 0xe6c180: lsr             w0, w3, #8
    // 0xe6c184: and             x3, x1, x9
    // 0xe6c188: lsl             w1, w3, #8
    // 0xe6c18c: orr             x3, x0, x1
    // 0xe6c190: and             x0, x3, x8
    // 0xe6c194: lsr             w1, w0, #0x10
    // 0xe6c198: and             x0, x3, x2
    // 0xe6c19c: lsl             w2, w0, #0x10
    // 0xe6c1a0: orr             x0, x1, x2
    // 0xe6c1a4: and             x1, x0, x12
    // 0xe6c1a8: and             x2, x0, x11
    // 0xe6c1ac: ubfx            x1, x1, #0, #0x20
    // 0xe6c1b0: ubfx            x2, x2, #0, #0x20
    // 0xe6c1b4: sub             x0, x1, x2
    // 0xe6c1b8: mov             x2, x0
    // 0xe6c1bc: r0 = BoxInt64Instr(r2)
    //     0xe6c1bc: sbfiz           x0, x2, #1, #0x1f
    //     0xe6c1c0: cmp             x2, x0, asr #1
    //     0xe6c1c4: b.eq            #0xe6c1d0
    //     0xe6c1c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c1cc: stur            x2, [x0, #7]
    // 0xe6c1d0: LeaveFrame
    //     0xe6c1d0: mov             SP, fp
    //     0xe6c1d4: ldp             fp, lr, [SP], #0x10
    // 0xe6c1d8: ret
    //     0xe6c1d8: ret             
    // 0xe6c1dc: ldur            x6, [fp, #-0x40]
    // 0xe6c1e0: r10 = 4278255360
    //     0xe6c1e0: movz            x10, #0xff00
    //     0xe6c1e4: movk            x10, #0xff00, lsl #16
    // 0xe6c1e8: r9 = 16711935
    //     0xe6c1e8: movz            x9, #0xff
    //     0xe6c1ec: movk            x9, #0xff, lsl #16
    // 0xe6c1f0: r8 = 4294901760
    //     0xe6c1f0: orr             x8, xzr, #0xffff0000
    // 0xe6c1f4: r2 = 65535
    //     0xe6c1f4: orr             x2, xzr, #0xffff
    // 0xe6c1f8: r12 = 2147483647
    //     0xe6c1f8: orr             x12, xzr, #0x7fffffff
    // 0xe6c1fc: r11 = 2147483648
    //     0xe6c1fc: orr             x11, xzr, #0x80000000
    // 0xe6c200: r0 = BoxInt64Instr(r3)
    //     0xe6c200: sbfiz           x0, x3, #1, #0x1f
    //     0xe6c204: cmp             x3, x0, asr #1
    //     0xe6c208: b.eq            #0xe6c214
    //     0xe6c20c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c210: stur            x3, [x0, #7]
    // 0xe6c214: mov             x4, x0
    // 0xe6c218: r0 = AllocateInt32Array()
    //     0xe6c218: bl              #0xec1ce8  ; AllocateInt32ArrayStub
    // 0xe6c21c: mov             x2, x0
    // 0xe6c220: ldur            x6, [fp, #-0x40]
    // 0xe6c224: ldur            x13, [fp, #-0x48]
    // 0xe6c228: ldur            x3, [fp, #-0x20]
    // 0xe6c22c: ldur            x4, [fp, #-0x30]
    // 0xe6c230: ldur            x5, [fp, #-0x28]
    // 0xe6c234: r14 = 0
    //     0xe6c234: movz            x14, #0
    // 0xe6c238: r10 = 4278255360
    //     0xe6c238: movz            x10, #0xff00
    //     0xe6c23c: movk            x10, #0xff00, lsl #16
    // 0xe6c240: r9 = 16711935
    //     0xe6c240: movz            x9, #0xff
    //     0xe6c244: movk            x9, #0xff, lsl #16
    // 0xe6c248: r8 = 4294901760
    //     0xe6c248: orr             x8, xzr, #0xffff0000
    // 0xe6c24c: r7 = 65535
    //     0xe6c24c: orr             x7, xzr, #0xffff
    // 0xe6c250: r12 = 2147483647
    //     0xe6c250: orr             x12, xzr, #0x7fffffff
    // 0xe6c254: r11 = 2147483648
    //     0xe6c254: orr             x11, xzr, #0x80000000
    // 0xe6c258: CheckStackOverflow
    //     0xe6c258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6c25c: cmp             SP, x16
    //     0xe6c260: b.ls            #0xe6cf9c
    // 0xe6c264: cmp             x14, x3
    // 0xe6c268: b.ge            #0xe6c304
    // 0xe6c26c: lsl             x0, x14, #2
    // 0xe6c270: add             x19, x13, x0
    // 0xe6c274: ldur            x0, [fp, #-0x50]
    // 0xe6c278: mov             x1, x19
    // 0xe6c27c: cmp             x1, x0
    // 0xe6c280: b.hs            #0xe6cfa4
    // 0xe6c284: add             x0, x5, x19
    // 0xe6c288: LoadField: r1 = r4->field_7
    //     0xe6c288: ldur            x1, [x4, #7]
    // 0xe6c28c: ldrsw           x19, [x1, x0]
    // 0xe6c290: r16 = Instance_Endian
    //     0xe6c290: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c294: ldr             x16, [x16, #0x8b8]
    // 0xe6c298: cmp             w6, w16
    // 0xe6c29c: b.ne            #0xe6c2ac
    // 0xe6c2a0: mov             x0, x19
    // 0xe6c2a4: sxtw            x0, w0
    // 0xe6c2a8: b               #0xe6c2ec
    // 0xe6c2ac: mov             x0, x19
    // 0xe6c2b0: and             x1, x0, x10
    // 0xe6c2b4: lsr             w0, w1, #8
    // 0xe6c2b8: and             x1, x19, x9
    // 0xe6c2bc: lsl             w19, w1, #8
    // 0xe6c2c0: orr             x1, x0, x19
    // 0xe6c2c4: and             x0, x1, x8
    // 0xe6c2c8: lsr             w19, w0, #0x10
    // 0xe6c2cc: and             x0, x1, x7
    // 0xe6c2d0: lsl             w1, w0, #0x10
    // 0xe6c2d4: orr             x0, x19, x1
    // 0xe6c2d8: and             x1, x0, x12
    // 0xe6c2dc: and             x19, x0, x11
    // 0xe6c2e0: ubfx            x1, x1, #0, #0x20
    // 0xe6c2e4: ubfx            x19, x19, #0, #0x20
    // 0xe6c2e8: sub             x0, x1, x19
    // 0xe6c2ec: sxtw            x0, w0
    // 0xe6c2f0: ArrayStore: r2[r14] = r0  ; List_4
    //     0xe6c2f0: add             x1, x2, x14, lsl #2
    //     0xe6c2f4: stur            w0, [x1, #0x17]
    // 0xe6c2f8: add             x0, x14, #1
    // 0xe6c2fc: mov             x14, x0
    // 0xe6c300: b               #0xe6c258
    // 0xe6c304: mov             x0, x2
    // 0xe6c308: LeaveFrame
    //     0xe6c308: mov             SP, fp
    //     0xe6c30c: ldp             fp, lr, [SP], #0x10
    // 0xe6c310: ret
    //     0xe6c310: ret             
    // 0xe6c314: ldur            x6, [fp, #-0x40]
    // 0xe6c318: ldur            x13, [fp, #-0x48]
    // 0xe6c31c: ldur            x3, [fp, #-0x20]
    // 0xe6c320: ldur            x4, [fp, #-0x30]
    // 0xe6c324: ldur            x5, [fp, #-0x28]
    // 0xe6c328: r10 = 4278255360
    //     0xe6c328: movz            x10, #0xff00
    //     0xe6c32c: movk            x10, #0xff00, lsl #16
    // 0xe6c330: r9 = 16711935
    //     0xe6c330: movz            x9, #0xff
    //     0xe6c334: movk            x9, #0xff, lsl #16
    // 0xe6c338: r8 = 4294901760
    //     0xe6c338: orr             x8, xzr, #0xffff0000
    // 0xe6c33c: r7 = 65535
    //     0xe6c33c: orr             x7, xzr, #0xffff
    // 0xe6c340: r12 = 2147483647
    //     0xe6c340: orr             x12, xzr, #0x7fffffff
    // 0xe6c344: r11 = 2147483648
    //     0xe6c344: orr             x11, xzr, #0x80000000
    // 0xe6c348: cmp             x3, #1
    // 0xe6c34c: b.ne            #0xe6c4d8
    // 0xe6c350: ldur            x0, [fp, #-0x50]
    // 0xe6c354: mov             x1, x13
    // 0xe6c358: cmp             x1, x0
    // 0xe6c35c: b.hs            #0xe6cfa8
    // 0xe6c360: add             x0, x5, x13
    // 0xe6c364: LoadField: r1 = r4->field_7
    //     0xe6c364: ldur            x1, [x4, #7]
    // 0xe6c368: ldrsw           x2, [x1, x0]
    // 0xe6c36c: r16 = Instance_Endian
    //     0xe6c36c: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c370: ldr             x16, [x16, #0x8b8]
    // 0xe6c374: cmp             w6, w16
    // 0xe6c378: b.ne            #0xe6c38c
    // 0xe6c37c: mov             x0, x2
    // 0xe6c380: sxtw            x0, w0
    // 0xe6c384: mov             x2, x0
    // 0xe6c388: b               #0xe6c3d0
    // 0xe6c38c: mov             x0, x2
    // 0xe6c390: and             x1, x0, x10
    // 0xe6c394: lsr             w0, w1, #8
    // 0xe6c398: and             x1, x2, x9
    // 0xe6c39c: lsl             w2, w1, #8
    // 0xe6c3a0: orr             x1, x0, x2
    // 0xe6c3a4: and             x0, x1, x8
    // 0xe6c3a8: lsr             w2, w0, #0x10
    // 0xe6c3ac: and             x0, x1, x7
    // 0xe6c3b0: lsl             w1, w0, #0x10
    // 0xe6c3b4: orr             x0, x2, x1
    // 0xe6c3b8: and             x1, x0, x12
    // 0xe6c3bc: and             x2, x0, x11
    // 0xe6c3c0: ubfx            x1, x1, #0, #0x20
    // 0xe6c3c4: ubfx            x2, x2, #0, #0x20
    // 0xe6c3c8: sub             x0, x1, x2
    // 0xe6c3cc: mov             x2, x0
    // 0xe6c3d0: add             x3, x13, #4
    // 0xe6c3d4: ldur            x0, [fp, #-0x50]
    // 0xe6c3d8: mov             x1, x3
    // 0xe6c3dc: cmp             x1, x0
    // 0xe6c3e0: b.hs            #0xe6cfac
    // 0xe6c3e4: add             x0, x5, x3
    // 0xe6c3e8: LoadField: r1 = r4->field_7
    //     0xe6c3e8: ldur            x1, [x4, #7]
    // 0xe6c3ec: ldrsw           x3, [x1, x0]
    // 0xe6c3f0: r16 = Instance_Endian
    //     0xe6c3f0: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c3f4: ldr             x16, [x16, #0x8b8]
    // 0xe6c3f8: cmp             w6, w16
    // 0xe6c3fc: b.ne            #0xe6c410
    // 0xe6c400: mov             x0, x3
    // 0xe6c404: sxtw            x0, w0
    // 0xe6c408: mov             x4, x0
    // 0xe6c40c: b               #0xe6c454
    // 0xe6c410: mov             x0, x3
    // 0xe6c414: and             x1, x0, x10
    // 0xe6c418: lsr             w0, w1, #8
    // 0xe6c41c: and             x1, x3, x9
    // 0xe6c420: lsl             w3, w1, #8
    // 0xe6c424: orr             x1, x0, x3
    // 0xe6c428: and             x0, x1, x8
    // 0xe6c42c: lsr             w3, w0, #0x10
    // 0xe6c430: and             x0, x1, x7
    // 0xe6c434: lsl             w1, w0, #0x10
    // 0xe6c438: orr             x0, x3, x1
    // 0xe6c43c: and             x1, x0, x12
    // 0xe6c440: and             x3, x0, x11
    // 0xe6c444: ubfx            x1, x1, #0, #0x20
    // 0xe6c448: ubfx            x3, x3, #0, #0x20
    // 0xe6c44c: sub             x0, x1, x3
    // 0xe6c450: mov             x4, x0
    // 0xe6c454: r3 = 4
    //     0xe6c454: movz            x3, #0x4
    // 0xe6c458: stur            x4, [fp, #-0x18]
    // 0xe6c45c: r0 = BoxInt64Instr(r2)
    //     0xe6c45c: sbfiz           x0, x2, #1, #0x1f
    //     0xe6c460: cmp             x2, x0, asr #1
    //     0xe6c464: b.eq            #0xe6c470
    //     0xe6c468: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c46c: stur            x2, [x0, #7]
    // 0xe6c470: mov             x2, x3
    // 0xe6c474: r1 = Null
    //     0xe6c474: mov             x1, NULL
    // 0xe6c478: stur            x0, [fp, #-0x58]
    // 0xe6c47c: r0 = AllocateArray()
    //     0xe6c47c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6c480: mov             x2, x0
    // 0xe6c484: ldur            x0, [fp, #-0x58]
    // 0xe6c488: stur            x2, [fp, #-0x60]
    // 0xe6c48c: StoreField: r2->field_f = r0
    //     0xe6c48c: stur            w0, [x2, #0xf]
    // 0xe6c490: ldur            x3, [fp, #-0x18]
    // 0xe6c494: r0 = BoxInt64Instr(r3)
    //     0xe6c494: sbfiz           x0, x3, #1, #0x1f
    //     0xe6c498: cmp             x3, x0, asr #1
    //     0xe6c49c: b.eq            #0xe6c4a8
    //     0xe6c4a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c4a4: stur            x3, [x0, #7]
    // 0xe6c4a8: StoreField: r2->field_13 = r0
    //     0xe6c4a8: stur            w0, [x2, #0x13]
    // 0xe6c4ac: r1 = <int>
    //     0xe6c4ac: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6c4b0: r0 = AllocateGrowableArray()
    //     0xe6c4b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6c4b4: mov             x1, x0
    // 0xe6c4b8: ldur            x0, [fp, #-0x60]
    // 0xe6c4bc: StoreField: r1->field_f = r0
    //     0xe6c4bc: stur            w0, [x1, #0xf]
    // 0xe6c4c0: r0 = 4
    //     0xe6c4c0: movz            x0, #0x4
    // 0xe6c4c4: StoreField: r1->field_b = r0
    //     0xe6c4c4: stur            w0, [x1, #0xb]
    // 0xe6c4c8: mov             x0, x1
    // 0xe6c4cc: LeaveFrame
    //     0xe6c4cc: mov             SP, fp
    //     0xe6c4d0: ldp             fp, lr, [SP], #0x10
    // 0xe6c4d4: ret
    //     0xe6c4d4: ret             
    // 0xe6c4d8: r0 = 4
    //     0xe6c4d8: movz            x0, #0x4
    // 0xe6c4dc: r1 = <List<int>>
    //     0xe6c4dc: ldr             x1, [PP, #0x14c0]  ; [pp+0x14c0] TypeArguments: <List<int>>
    // 0xe6c4e0: r2 = 0
    //     0xe6c4e0: movz            x2, #0
    // 0xe6c4e4: r0 = _GrowableList()
    //     0xe6c4e4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe6c4e8: mov             x3, x0
    // 0xe6c4ec: stur            x3, [fp, #-0x60]
    // 0xe6c4f0: r20 = 0
    //     0xe6c4f0: movz            x20, #0
    // 0xe6c4f4: ldur            x7, [fp, #-0x40]
    // 0xe6c4f8: ldur            x14, [fp, #-0x48]
    // 0xe6c4fc: ldur            x4, [fp, #-0x20]
    // 0xe6c500: ldur            x5, [fp, #-0x30]
    // 0xe6c504: ldur            x6, [fp, #-0x28]
    // 0xe6c508: r19 = 4
    //     0xe6c508: movz            x19, #0x4
    // 0xe6c50c: r11 = 4278255360
    //     0xe6c50c: movz            x11, #0xff00
    //     0xe6c510: movk            x11, #0xff00, lsl #16
    // 0xe6c514: r10 = 16711935
    //     0xe6c514: movz            x10, #0xff
    //     0xe6c518: movk            x10, #0xff, lsl #16
    // 0xe6c51c: r9 = 4294901760
    //     0xe6c51c: orr             x9, xzr, #0xffff0000
    // 0xe6c520: r8 = 65535
    //     0xe6c520: orr             x8, xzr, #0xffff
    // 0xe6c524: r13 = 2147483647
    //     0xe6c524: orr             x13, xzr, #0x7fffffff
    // 0xe6c528: r12 = 2147483648
    //     0xe6c528: orr             x12, xzr, #0x80000000
    // 0xe6c52c: stur            x20, [fp, #-0x38]
    // 0xe6c530: CheckStackOverflow
    //     0xe6c530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6c534: cmp             SP, x16
    //     0xe6c538: b.ls            #0xe6cfb0
    // 0xe6c53c: cmp             x20, x4
    // 0xe6c540: b.ge            #0xe6c74c
    // 0xe6c544: lsl             x0, x20, #3
    // 0xe6c548: add             x2, x14, x0
    // 0xe6c54c: ldur            x0, [fp, #-0x50]
    // 0xe6c550: mov             x1, x2
    // 0xe6c554: cmp             x1, x0
    // 0xe6c558: b.hs            #0xe6cfb8
    // 0xe6c55c: add             x0, x6, x2
    // 0xe6c560: LoadField: r1 = r5->field_7
    //     0xe6c560: ldur            x1, [x5, #7]
    // 0xe6c564: ldrsw           x23, [x1, x0]
    // 0xe6c568: r16 = Instance_Endian
    //     0xe6c568: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c56c: ldr             x16, [x16, #0x8b8]
    // 0xe6c570: cmp             w7, w16
    // 0xe6c574: b.ne            #0xe6c588
    // 0xe6c578: mov             x0, x23
    // 0xe6c57c: sxtw            x0, w0
    // 0xe6c580: mov             x23, x0
    // 0xe6c584: b               #0xe6c5cc
    // 0xe6c588: mov             x0, x23
    // 0xe6c58c: and             x1, x0, x11
    // 0xe6c590: lsr             w0, w1, #8
    // 0xe6c594: and             x1, x23, x10
    // 0xe6c598: lsl             w23, w1, #8
    // 0xe6c59c: orr             x1, x0, x23
    // 0xe6c5a0: and             x0, x1, x9
    // 0xe6c5a4: lsr             w23, w0, #0x10
    // 0xe6c5a8: and             x0, x1, x8
    // 0xe6c5ac: lsl             w1, w0, #0x10
    // 0xe6c5b0: orr             x0, x23, x1
    // 0xe6c5b4: and             x1, x0, x13
    // 0xe6c5b8: and             x23, x0, x12
    // 0xe6c5bc: ubfx            x1, x1, #0, #0x20
    // 0xe6c5c0: ubfx            x23, x23, #0, #0x20
    // 0xe6c5c4: sub             x0, x1, x23
    // 0xe6c5c8: mov             x23, x0
    // 0xe6c5cc: add             x24, x2, #4
    // 0xe6c5d0: ldur            x0, [fp, #-0x50]
    // 0xe6c5d4: mov             x1, x24
    // 0xe6c5d8: cmp             x1, x0
    // 0xe6c5dc: b.hs            #0xe6cfbc
    // 0xe6c5e0: add             x0, x6, x24
    // 0xe6c5e4: LoadField: r1 = r5->field_7
    //     0xe6c5e4: ldur            x1, [x5, #7]
    // 0xe6c5e8: ldrsw           x2, [x1, x0]
    // 0xe6c5ec: r16 = Instance_Endian
    //     0xe6c5ec: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c5f0: ldr             x16, [x16, #0x8b8]
    // 0xe6c5f4: cmp             w7, w16
    // 0xe6c5f8: b.ne            #0xe6c60c
    // 0xe6c5fc: mov             x0, x2
    // 0xe6c600: sxtw            x0, w0
    // 0xe6c604: mov             x24, x0
    // 0xe6c608: b               #0xe6c650
    // 0xe6c60c: mov             x0, x2
    // 0xe6c610: and             x1, x0, x11
    // 0xe6c614: lsr             w0, w1, #8
    // 0xe6c618: and             x1, x2, x10
    // 0xe6c61c: lsl             w2, w1, #8
    // 0xe6c620: orr             x1, x0, x2
    // 0xe6c624: and             x0, x1, x9
    // 0xe6c628: lsr             w2, w0, #0x10
    // 0xe6c62c: and             x0, x1, x8
    // 0xe6c630: lsl             w1, w0, #0x10
    // 0xe6c634: orr             x0, x2, x1
    // 0xe6c638: and             x1, x0, x13
    // 0xe6c63c: and             x2, x0, x12
    // 0xe6c640: ubfx            x1, x1, #0, #0x20
    // 0xe6c644: ubfx            x2, x2, #0, #0x20
    // 0xe6c648: sub             x0, x1, x2
    // 0xe6c64c: mov             x24, x0
    // 0xe6c650: stur            x24, [fp, #-0x18]
    // 0xe6c654: r0 = BoxInt64Instr(r23)
    //     0xe6c654: sbfiz           x0, x23, #1, #0x1f
    //     0xe6c658: cmp             x23, x0, asr #1
    //     0xe6c65c: b.eq            #0xe6c668
    //     0xe6c660: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c664: stur            x23, [x0, #7]
    // 0xe6c668: mov             x2, x19
    // 0xe6c66c: r1 = Null
    //     0xe6c66c: mov             x1, NULL
    // 0xe6c670: stur            x0, [fp, #-0x58]
    // 0xe6c674: r0 = AllocateArray()
    //     0xe6c674: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6c678: mov             x2, x0
    // 0xe6c67c: ldur            x0, [fp, #-0x58]
    // 0xe6c680: stur            x2, [fp, #-0x68]
    // 0xe6c684: StoreField: r2->field_f = r0
    //     0xe6c684: stur            w0, [x2, #0xf]
    // 0xe6c688: ldur            x3, [fp, #-0x18]
    // 0xe6c68c: r0 = BoxInt64Instr(r3)
    //     0xe6c68c: sbfiz           x0, x3, #1, #0x1f
    //     0xe6c690: cmp             x3, x0, asr #1
    //     0xe6c694: b.eq            #0xe6c6a0
    //     0xe6c698: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c69c: stur            x3, [x0, #7]
    // 0xe6c6a0: StoreField: r2->field_13 = r0
    //     0xe6c6a0: stur            w0, [x2, #0x13]
    // 0xe6c6a4: r1 = <int>
    //     0xe6c6a4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6c6a8: r0 = AllocateGrowableArray()
    //     0xe6c6a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6c6ac: mov             x2, x0
    // 0xe6c6b0: ldur            x0, [fp, #-0x68]
    // 0xe6c6b4: stur            x2, [fp, #-0x58]
    // 0xe6c6b8: StoreField: r2->field_f = r0
    //     0xe6c6b8: stur            w0, [x2, #0xf]
    // 0xe6c6bc: r0 = 4
    //     0xe6c6bc: movz            x0, #0x4
    // 0xe6c6c0: StoreField: r2->field_b = r0
    //     0xe6c6c0: stur            w0, [x2, #0xb]
    // 0xe6c6c4: ldur            x3, [fp, #-0x60]
    // 0xe6c6c8: LoadField: r1 = r3->field_b
    //     0xe6c6c8: ldur            w1, [x3, #0xb]
    // 0xe6c6cc: LoadField: r4 = r3->field_f
    //     0xe6c6cc: ldur            w4, [x3, #0xf]
    // 0xe6c6d0: DecompressPointer r4
    //     0xe6c6d0: add             x4, x4, HEAP, lsl #32
    // 0xe6c6d4: LoadField: r5 = r4->field_b
    //     0xe6c6d4: ldur            w5, [x4, #0xb]
    // 0xe6c6d8: r4 = LoadInt32Instr(r1)
    //     0xe6c6d8: sbfx            x4, x1, #1, #0x1f
    // 0xe6c6dc: stur            x4, [fp, #-0x18]
    // 0xe6c6e0: r1 = LoadInt32Instr(r5)
    //     0xe6c6e0: sbfx            x1, x5, #1, #0x1f
    // 0xe6c6e4: cmp             x4, x1
    // 0xe6c6e8: b.ne            #0xe6c6f4
    // 0xe6c6ec: mov             x1, x3
    // 0xe6c6f0: r0 = _growToNextCapacity()
    //     0xe6c6f0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe6c6f4: ldur            x2, [fp, #-0x60]
    // 0xe6c6f8: ldur            x4, [fp, #-0x38]
    // 0xe6c6fc: ldur            x3, [fp, #-0x18]
    // 0xe6c700: add             x0, x3, #1
    // 0xe6c704: lsl             x1, x0, #1
    // 0xe6c708: StoreField: r2->field_b = r1
    //     0xe6c708: stur            w1, [x2, #0xb]
    // 0xe6c70c: LoadField: r1 = r2->field_f
    //     0xe6c70c: ldur            w1, [x2, #0xf]
    // 0xe6c710: DecompressPointer r1
    //     0xe6c710: add             x1, x1, HEAP, lsl #32
    // 0xe6c714: ldur            x0, [fp, #-0x58]
    // 0xe6c718: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe6c718: add             x25, x1, x3, lsl #2
    //     0xe6c71c: add             x25, x25, #0xf
    //     0xe6c720: str             w0, [x25]
    //     0xe6c724: tbz             w0, #0, #0xe6c740
    //     0xe6c728: ldurb           w16, [x1, #-1]
    //     0xe6c72c: ldurb           w17, [x0, #-1]
    //     0xe6c730: and             x16, x17, x16, lsr #2
    //     0xe6c734: tst             x16, HEAP, lsr #32
    //     0xe6c738: b.eq            #0xe6c740
    //     0xe6c73c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe6c740: add             x20, x4, #1
    // 0xe6c744: mov             x3, x2
    // 0xe6c748: b               #0xe6c4f4
    // 0xe6c74c: mov             x2, x3
    // 0xe6c750: mov             x0, x2
    // 0xe6c754: LeaveFrame
    //     0xe6c754: mov             SP, fp
    //     0xe6c758: ldp             fp, lr, [SP], #0x10
    // 0xe6c75c: ret
    //     0xe6c75c: ret             
    // 0xe6c760: cmp             x6, #0xb
    // 0xe6c764: b.gt            #0xe6cac8
    // 0xe6c768: ldur            x3, [fp, #-0x20]
    // 0xe6c76c: cmp             x3, #1
    // 0xe6c770: b.ne            #0xe6c8d4
    // 0xe6c774: ldur            x5, [fp, #-0x40]
    // 0xe6c778: r16 = Instance_Endian
    //     0xe6c778: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c77c: ldr             x16, [x16, #0x8b8]
    // 0xe6c780: cmp             w5, w16
    // 0xe6c784: b.ne            #0xe6c7a0
    // 0xe6c788: ldur            x0, [fp, #-0x30]
    // 0xe6c78c: LoadField: r1 = r0->field_7
    //     0xe6c78c: ldur            x1, [x0, #7]
    // 0xe6c790: ldr             s0, [x1, x2]
    // 0xe6c794: fcvt            d1, s0
    // 0xe6c798: mov             v0.16b, v1.16b
    // 0xe6c79c: b               #0xe6c8a0
    // 0xe6c7a0: ldur            x0, [fp, #-0x30]
    // 0xe6c7a4: r0 = InitLateStaticField(0x320) // [dart:typed_data] ::_convU32
    //     0xe6c7a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6c7a8: ldr             x0, [x0, #0x640]
    //     0xe6c7ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6c7b0: cmp             w0, w16
    //     0xe6c7b4: b.ne            #0xe6c7c4
    //     0xe6c7b8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae28] Field <::._convU32@8027147>: static late final (offset: 0x320)
    //     0xe6c7bc: ldr             x2, [x2, #0xe28]
    //     0xe6c7c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6c7c4: mov             x3, x0
    // 0xe6c7c8: ldur            x2, [fp, #-0x30]
    // 0xe6c7cc: LoadField: r0 = r2->field_7
    //     0xe6c7cc: ldur            x0, [x2, #7]
    // 0xe6c7d0: ldur            x2, [fp, #-0x70]
    // 0xe6c7d4: ldr             w1, [x0, x2]
    // 0xe6c7d8: r6 = 4278255360
    //     0xe6c7d8: movz            x6, #0xff00
    //     0xe6c7dc: movk            x6, #0xff00, lsl #16
    // 0xe6c7e0: and             x0, x1, x6
    // 0xe6c7e4: ubfx            x0, x0, #0, #0x20
    // 0xe6c7e8: asr             x2, x0, #8
    // 0xe6c7ec: r7 = 16711935
    //     0xe6c7ec: movz            x7, #0xff
    //     0xe6c7f0: movk            x7, #0xff, lsl #16
    // 0xe6c7f4: and             x0, x1, x7
    // 0xe6c7f8: ubfx            x0, x0, #0, #0x20
    // 0xe6c7fc: lsl             x1, x0, #8
    // 0xe6c800: orr             x0, x2, x1
    // 0xe6c804: mov             x1, x0
    // 0xe6c808: ubfx            x1, x1, #0, #0x20
    // 0xe6c80c: r8 = 4294901760
    //     0xe6c80c: orr             x8, xzr, #0xffff0000
    // 0xe6c810: and             x2, x1, x8
    // 0xe6c814: ubfx            x2, x2, #0, #0x20
    // 0xe6c818: asr             x1, x2, #0x10
    // 0xe6c81c: ubfx            x0, x0, #0, #0x20
    // 0xe6c820: r9 = 65535
    //     0xe6c820: orr             x9, xzr, #0xffff
    // 0xe6c824: and             x2, x0, x9
    // 0xe6c828: ubfx            x2, x2, #0, #0x20
    // 0xe6c82c: lsl             x0, x2, #0x10
    // 0xe6c830: orr             x2, x1, x0
    // 0xe6c834: LoadField: r0 = r3->field_13
    //     0xe6c834: ldur            w0, [x3, #0x13]
    // 0xe6c838: r1 = LoadInt32Instr(r0)
    //     0xe6c838: sbfx            x1, x0, #1, #0x1f
    // 0xe6c83c: mov             x0, x1
    // 0xe6c840: r1 = 0
    //     0xe6c840: movz            x1, #0
    // 0xe6c844: cmp             x1, x0
    // 0xe6c848: b.hs            #0xe6cfc0
    // 0xe6c84c: ubfx            x2, x2, #0, #0x20
    // 0xe6c850: ArrayStore: r3[0] = r2  ; List_4
    //     0xe6c850: stur            w2, [x3, #0x17]
    // 0xe6c854: r0 = InitLateStaticField(0x328) // [dart:typed_data] ::_convF32
    //     0xe6c854: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6c858: ldr             x0, [x0, #0x650]
    //     0xe6c85c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6c860: cmp             w0, w16
    //     0xe6c864: b.ne            #0xe6c874
    //     0xe6c868: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e620] Field <::._convF32@8027147>: static late final (offset: 0x328)
    //     0xe6c86c: ldr             x2, [x2, #0x620]
    //     0xe6c870: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6c874: mov             x2, x0
    // 0xe6c878: LoadField: r0 = r2->field_13
    //     0xe6c878: ldur            w0, [x2, #0x13]
    // 0xe6c87c: r1 = LoadInt32Instr(r0)
    //     0xe6c87c: sbfx            x1, x0, #1, #0x1f
    // 0xe6c880: mov             x0, x1
    // 0xe6c884: r1 = 0
    //     0xe6c884: movz            x1, #0
    // 0xe6c888: cmp             x1, x0
    // 0xe6c88c: b.hs            #0xe6cfc4
    // 0xe6c890: LoadField: r0 = r2->field_7
    //     0xe6c890: ldur            x0, [x2, #7]
    // 0xe6c894: ldr             s0, [x0]
    // 0xe6c898: fcvt            d1, s0
    // 0xe6c89c: mov             v0.16b, v1.16b
    // 0xe6c8a0: r0 = inline_Allocate_Double()
    //     0xe6c8a0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6c8a4: add             x0, x0, #0x10
    //     0xe6c8a8: cmp             x1, x0
    //     0xe6c8ac: b.ls            #0xe6cfc8
    //     0xe6c8b0: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6c8b4: sub             x0, x0, #0xf
    //     0xe6c8b8: movz            x1, #0xe15c
    //     0xe6c8bc: movk            x1, #0x3, lsl #16
    //     0xe6c8c0: stur            x1, [x0, #-1]
    // 0xe6c8c4: StoreField: r0->field_7 = d0
    //     0xe6c8c4: stur            d0, [x0, #7]
    // 0xe6c8c8: LeaveFrame
    //     0xe6c8c8: mov             SP, fp
    //     0xe6c8cc: ldp             fp, lr, [SP], #0x10
    // 0xe6c8d0: ret
    //     0xe6c8d0: ret             
    // 0xe6c8d4: ldur            x5, [fp, #-0x40]
    // 0xe6c8d8: ldur            x2, [fp, #-0x30]
    // 0xe6c8dc: r6 = 4278255360
    //     0xe6c8dc: movz            x6, #0xff00
    //     0xe6c8e0: movk            x6, #0xff00, lsl #16
    // 0xe6c8e4: r7 = 16711935
    //     0xe6c8e4: movz            x7, #0xff
    //     0xe6c8e8: movk            x7, #0xff, lsl #16
    // 0xe6c8ec: r8 = 4294901760
    //     0xe6c8ec: orr             x8, xzr, #0xffff0000
    // 0xe6c8f0: r9 = 65535
    //     0xe6c8f0: orr             x9, xzr, #0xffff
    // 0xe6c8f4: r0 = BoxInt64Instr(r3)
    //     0xe6c8f4: sbfiz           x0, x3, #1, #0x1f
    //     0xe6c8f8: cmp             x3, x0, asr #1
    //     0xe6c8fc: b.eq            #0xe6c908
    //     0xe6c900: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6c904: stur            x3, [x0, #7]
    // 0xe6c908: mov             x4, x0
    // 0xe6c90c: r0 = AllocateFloat32Array()
    //     0xe6c90c: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xe6c910: mov             x2, x0
    // 0xe6c914: stur            x2, [fp, #-0x58]
    // 0xe6c918: r8 = 0
    //     0xe6c918: movz            x8, #0
    // 0xe6c91c: ldur            x4, [fp, #-0x40]
    // 0xe6c920: ldur            x7, [fp, #-0x48]
    // 0xe6c924: ldur            x3, [fp, #-0x20]
    // 0xe6c928: ldur            x5, [fp, #-0x30]
    // 0xe6c92c: ldur            x6, [fp, #-0x28]
    // 0xe6c930: stur            x8, [fp, #-0x38]
    // 0xe6c934: CheckStackOverflow
    //     0xe6c934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6c938: cmp             SP, x16
    //     0xe6c93c: b.ls            #0xe6cfd8
    // 0xe6c940: cmp             x8, x3
    // 0xe6c944: b.ge            #0xe6cab8
    // 0xe6c948: lsl             x0, x8, #2
    // 0xe6c94c: add             x9, x7, x0
    // 0xe6c950: ldur            x0, [fp, #-0x50]
    // 0xe6c954: mov             x1, x9
    // 0xe6c958: stur            x9, [fp, #-0x18]
    // 0xe6c95c: cmp             x1, x0
    // 0xe6c960: b.hs            #0xe6cfe0
    // 0xe6c964: r16 = Instance_Endian
    //     0xe6c964: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6c968: ldr             x16, [x16, #0x8b8]
    // 0xe6c96c: cmp             w4, w16
    // 0xe6c970: b.ne            #0xe6c994
    // 0xe6c974: add             x0, x6, x9
    // 0xe6c978: LoadField: r1 = r5->field_7
    //     0xe6c978: ldur            x1, [x5, #7]
    // 0xe6c97c: ldr             s0, [x1, x0]
    // 0xe6c980: fcvt            d1, s0
    // 0xe6c984: mov             v0.16b, v1.16b
    // 0xe6c988: mov             x1, x8
    // 0xe6c98c: mov             x0, x2
    // 0xe6c990: b               #0xe6caa0
    // 0xe6c994: r0 = InitLateStaticField(0x320) // [dart:typed_data] ::_convU32
    //     0xe6c994: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6c998: ldr             x0, [x0, #0x640]
    //     0xe6c99c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6c9a0: cmp             w0, w16
    //     0xe6c9a4: b.ne            #0xe6c9b4
    //     0xe6c9a8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae28] Field <::._convU32@8027147>: static late final (offset: 0x320)
    //     0xe6c9ac: ldr             x2, [x2, #0xe28]
    //     0xe6c9b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6c9b4: mov             x3, x0
    // 0xe6c9b8: ldur            x0, [fp, #-0x18]
    // 0xe6c9bc: ldur            x2, [fp, #-0x28]
    // 0xe6c9c0: add             x1, x2, x0
    // 0xe6c9c4: ldur            x4, [fp, #-0x30]
    // 0xe6c9c8: LoadField: r0 = r4->field_7
    //     0xe6c9c8: ldur            x0, [x4, #7]
    // 0xe6c9cc: ldr             w5, [x0, x1]
    // 0xe6c9d0: r6 = 4278255360
    //     0xe6c9d0: movz            x6, #0xff00
    //     0xe6c9d4: movk            x6, #0xff00, lsl #16
    // 0xe6c9d8: and             x0, x5, x6
    // 0xe6c9dc: ubfx            x0, x0, #0, #0x20
    // 0xe6c9e0: asr             x1, x0, #8
    // 0xe6c9e4: r7 = 16711935
    //     0xe6c9e4: movz            x7, #0xff
    //     0xe6c9e8: movk            x7, #0xff, lsl #16
    // 0xe6c9ec: and             x0, x5, x7
    // 0xe6c9f0: ubfx            x0, x0, #0, #0x20
    // 0xe6c9f4: lsl             x5, x0, #8
    // 0xe6c9f8: orr             x0, x1, x5
    // 0xe6c9fc: mov             x1, x0
    // 0xe6ca00: ubfx            x1, x1, #0, #0x20
    // 0xe6ca04: r5 = 4294901760
    //     0xe6ca04: orr             x5, xzr, #0xffff0000
    // 0xe6ca08: and             x8, x1, x5
    // 0xe6ca0c: ubfx            x8, x8, #0, #0x20
    // 0xe6ca10: asr             x1, x8, #0x10
    // 0xe6ca14: ubfx            x0, x0, #0, #0x20
    // 0xe6ca18: r8 = 65535
    //     0xe6ca18: orr             x8, xzr, #0xffff
    // 0xe6ca1c: and             x9, x0, x8
    // 0xe6ca20: ubfx            x9, x9, #0, #0x20
    // 0xe6ca24: lsl             x0, x9, #0x10
    // 0xe6ca28: orr             x9, x1, x0
    // 0xe6ca2c: LoadField: r0 = r3->field_13
    //     0xe6ca2c: ldur            w0, [x3, #0x13]
    // 0xe6ca30: r1 = LoadInt32Instr(r0)
    //     0xe6ca30: sbfx            x1, x0, #1, #0x1f
    // 0xe6ca34: mov             x0, x1
    // 0xe6ca38: r1 = 0
    //     0xe6ca38: movz            x1, #0
    // 0xe6ca3c: cmp             x1, x0
    // 0xe6ca40: b.hs            #0xe6cfe4
    // 0xe6ca44: ubfx            x9, x9, #0, #0x20
    // 0xe6ca48: ArrayStore: r3[0] = r9  ; List_4
    //     0xe6ca48: stur            w9, [x3, #0x17]
    // 0xe6ca4c: r0 = InitLateStaticField(0x328) // [dart:typed_data] ::_convF32
    //     0xe6ca4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6ca50: ldr             x0, [x0, #0x650]
    //     0xe6ca54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6ca58: cmp             w0, w16
    //     0xe6ca5c: b.ne            #0xe6ca6c
    //     0xe6ca60: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e620] Field <::._convF32@8027147>: static late final (offset: 0x328)
    //     0xe6ca64: ldr             x2, [x2, #0x620]
    //     0xe6ca68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6ca6c: mov             x2, x0
    // 0xe6ca70: LoadField: r0 = r2->field_13
    //     0xe6ca70: ldur            w0, [x2, #0x13]
    // 0xe6ca74: r1 = LoadInt32Instr(r0)
    //     0xe6ca74: sbfx            x1, x0, #1, #0x1f
    // 0xe6ca78: mov             x0, x1
    // 0xe6ca7c: r1 = 0
    //     0xe6ca7c: movz            x1, #0
    // 0xe6ca80: cmp             x1, x0
    // 0xe6ca84: b.hs            #0xe6cfe8
    // 0xe6ca88: LoadField: r0 = r2->field_7
    //     0xe6ca88: ldur            x0, [x2, #7]
    // 0xe6ca8c: ldr             s0, [x0]
    // 0xe6ca90: fcvt            d1, s0
    // 0xe6ca94: mov             v0.16b, v1.16b
    // 0xe6ca98: ldur            x1, [fp, #-0x38]
    // 0xe6ca9c: ldur            x0, [fp, #-0x58]
    // 0xe6caa0: fcvt            s1, d0
    // 0xe6caa4: ArrayStore: r0[r1] = d1  ; List_8
    //     0xe6caa4: add             x2, x0, x1, lsl #2
    //     0xe6caa8: stur            s1, [x2, #0x17]
    // 0xe6caac: add             x8, x1, #1
    // 0xe6cab0: mov             x2, x0
    // 0xe6cab4: b               #0xe6c91c
    // 0xe6cab8: mov             x0, x2
    // 0xe6cabc: LeaveFrame
    //     0xe6cabc: mov             SP, fp
    //     0xe6cac0: ldp             fp, lr, [SP], #0x10
    // 0xe6cac4: ret
    //     0xe6cac4: ret             
    // 0xe6cac8: lsl             x0, x6, #1
    // 0xe6cacc: cmp             w0, #0x18
    // 0xe6cad0: b.ne            #0xe6cf34
    // 0xe6cad4: ldur            x3, [fp, #-0x20]
    // 0xe6cad8: cmp             x3, #1
    // 0xe6cadc: b.ne            #0xe6ccc4
    // 0xe6cae0: ldur            x5, [fp, #-0x40]
    // 0xe6cae4: ldur            x6, [fp, #-0x10]
    // 0xe6cae8: sub             x0, x6, #7
    // 0xe6caec: ldur            x1, [fp, #-8]
    // 0xe6caf0: cmp             x1, x0
    // 0xe6caf4: b.hs            #0xe6cfec
    // 0xe6caf8: r16 = Instance_Endian
    //     0xe6caf8: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6cafc: ldr             x16, [x16, #0x8b8]
    // 0xe6cb00: cmp             w5, w16
    // 0xe6cb04: b.ne            #0xe6cb18
    // 0xe6cb08: ldur            x0, [fp, #-0x30]
    // 0xe6cb0c: LoadField: r1 = r0->field_7
    //     0xe6cb0c: ldur            x1, [x0, #7]
    // 0xe6cb10: ldr             d0, [x1, x2]
    // 0xe6cb14: b               #0xe6cc90
    // 0xe6cb18: ldur            x0, [fp, #-0x30]
    // 0xe6cb1c: r0 = InitLateStaticField(0x324) // [dart:typed_data] ::_convU64
    //     0xe6cb1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6cb20: ldr             x0, [x0, #0x648]
    //     0xe6cb24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6cb28: cmp             w0, w16
    //     0xe6cb2c: b.ne            #0xe6cb3c
    //     0xe6cb30: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae00] Field <::._convU64@8027147>: static late final (offset: 0x324)
    //     0xe6cb34: ldr             x2, [x2, #0xe00]
    //     0xe6cb38: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6cb3c: mov             x3, x0
    // 0xe6cb40: ldur            x2, [fp, #-0x30]
    // 0xe6cb44: LoadField: r0 = r2->field_7
    //     0xe6cb44: ldur            x0, [x2, #7]
    // 0xe6cb48: ldur            x1, [fp, #-0x70]
    // 0xe6cb4c: ldr             x2, [x0, x1]
    // 0xe6cb50: mov             x0, x2
    // 0xe6cb54: ubfx            x0, x0, #0, #0x20
    // 0xe6cb58: r7 = 4278255360
    //     0xe6cb58: movz            x7, #0xff00
    //     0xe6cb5c: movk            x7, #0xff00, lsl #16
    // 0xe6cb60: and             x1, x0, x7
    // 0xe6cb64: ubfx            x1, x1, #0, #0x20
    // 0xe6cb68: asr             x0, x1, #8
    // 0xe6cb6c: mov             x1, x2
    // 0xe6cb70: ubfx            x1, x1, #0, #0x20
    // 0xe6cb74: r8 = 16711935
    //     0xe6cb74: movz            x8, #0xff
    //     0xe6cb78: movk            x8, #0xff, lsl #16
    // 0xe6cb7c: and             x4, x1, x8
    // 0xe6cb80: ubfx            x4, x4, #0, #0x20
    // 0xe6cb84: lsl             x1, x4, #8
    // 0xe6cb88: orr             x4, x0, x1
    // 0xe6cb8c: mov             x0, x4
    // 0xe6cb90: ubfx            x0, x0, #0, #0x20
    // 0xe6cb94: r9 = 4294901760
    //     0xe6cb94: orr             x9, xzr, #0xffff0000
    // 0xe6cb98: and             x1, x0, x9
    // 0xe6cb9c: ubfx            x1, x1, #0, #0x20
    // 0xe6cba0: asr             x0, x1, #0x10
    // 0xe6cba4: ubfx            x4, x4, #0, #0x20
    // 0xe6cba8: r10 = 65535
    //     0xe6cba8: orr             x10, xzr, #0xffff
    // 0xe6cbac: and             x1, x4, x10
    // 0xe6cbb0: ubfx            x1, x1, #0, #0x20
    // 0xe6cbb4: lsl             x4, x1, #0x10
    // 0xe6cbb8: orr             x1, x0, x4
    // 0xe6cbbc: lsl             x0, x1, #0x20
    // 0xe6cbc0: asr             x1, x2, #0x20
    // 0xe6cbc4: mov             x2, x1
    // 0xe6cbc8: ubfx            x2, x2, #0, #0x20
    // 0xe6cbcc: and             x4, x2, x7
    // 0xe6cbd0: ubfx            x4, x4, #0, #0x20
    // 0xe6cbd4: asr             x2, x4, #8
    // 0xe6cbd8: ubfx            x1, x1, #0, #0x20
    // 0xe6cbdc: and             x4, x1, x8
    // 0xe6cbe0: ubfx            x4, x4, #0, #0x20
    // 0xe6cbe4: lsl             x1, x4, #8
    // 0xe6cbe8: orr             x4, x2, x1
    // 0xe6cbec: mov             x1, x4
    // 0xe6cbf0: ubfx            x1, x1, #0, #0x20
    // 0xe6cbf4: and             x2, x1, x9
    // 0xe6cbf8: ubfx            x2, x2, #0, #0x20
    // 0xe6cbfc: asr             x1, x2, #0x10
    // 0xe6cc00: ubfx            x4, x4, #0, #0x20
    // 0xe6cc04: and             x2, x4, x10
    // 0xe6cc08: ubfx            x2, x2, #0, #0x20
    // 0xe6cc0c: lsl             x4, x2, #0x10
    // 0xe6cc10: orr             x2, x1, x4
    // 0xe6cc14: orr             x4, x0, x2
    // 0xe6cc18: LoadField: r0 = r3->field_13
    //     0xe6cc18: ldur            w0, [x3, #0x13]
    // 0xe6cc1c: r1 = LoadInt32Instr(r0)
    //     0xe6cc1c: sbfx            x1, x0, #1, #0x1f
    // 0xe6cc20: mov             x0, x1
    // 0xe6cc24: r1 = 0
    //     0xe6cc24: movz            x1, #0
    // 0xe6cc28: cmp             x1, x0
    // 0xe6cc2c: b.hs            #0xe6cff0
    // 0xe6cc30: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xe6cc30: ldur            w0, [x3, #0x17]
    // 0xe6cc34: DecompressPointer r0
    //     0xe6cc34: add             x0, x0, HEAP, lsl #32
    // 0xe6cc38: LoadField: r1 = r3->field_1b
    //     0xe6cc38: ldur            w1, [x3, #0x1b]
    // 0xe6cc3c: LoadField: r2 = r0->field_7
    //     0xe6cc3c: ldur            x2, [x0, #7]
    // 0xe6cc40: asr             w0, w1, #1
    // 0xe6cc44: add             x0, x2, w0, sxtw
    // 0xe6cc48: str             x4, [x0]
    // 0xe6cc4c: r0 = InitLateStaticField(0x32c) // [dart:typed_data] ::_convF64
    //     0xe6cc4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6cc50: ldr             x0, [x0, #0x658]
    //     0xe6cc54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6cc58: cmp             w0, w16
    //     0xe6cc5c: b.ne            #0xe6cc6c
    //     0xe6cc60: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae08] Field <::._convF64@8027147>: static late final (offset: 0x32c)
    //     0xe6cc64: ldr             x2, [x2, #0xe08]
    //     0xe6cc68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6cc6c: mov             x2, x0
    // 0xe6cc70: LoadField: r0 = r2->field_13
    //     0xe6cc70: ldur            w0, [x2, #0x13]
    // 0xe6cc74: r1 = LoadInt32Instr(r0)
    //     0xe6cc74: sbfx            x1, x0, #1, #0x1f
    // 0xe6cc78: mov             x0, x1
    // 0xe6cc7c: r1 = 0
    //     0xe6cc7c: movz            x1, #0
    // 0xe6cc80: cmp             x1, x0
    // 0xe6cc84: b.hs            #0xe6cff4
    // 0xe6cc88: LoadField: r0 = r2->field_7
    //     0xe6cc88: ldur            x0, [x2, #7]
    // 0xe6cc8c: ldr             d0, [x0]
    // 0xe6cc90: r0 = inline_Allocate_Double()
    //     0xe6cc90: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6cc94: add             x0, x0, #0x10
    //     0xe6cc98: cmp             x1, x0
    //     0xe6cc9c: b.ls            #0xe6cff8
    //     0xe6cca0: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6cca4: sub             x0, x0, #0xf
    //     0xe6cca8: movz            x1, #0xe15c
    //     0xe6ccac: movk            x1, #0x3, lsl #16
    //     0xe6ccb0: stur            x1, [x0, #-1]
    // 0xe6ccb4: StoreField: r0->field_7 = d0
    //     0xe6ccb4: stur            d0, [x0, #7]
    // 0xe6ccb8: LeaveFrame
    //     0xe6ccb8: mov             SP, fp
    //     0xe6ccbc: ldp             fp, lr, [SP], #0x10
    // 0xe6ccc0: ret
    //     0xe6ccc0: ret             
    // 0xe6ccc4: ldur            x5, [fp, #-0x40]
    // 0xe6ccc8: ldur            x2, [fp, #-0x30]
    // 0xe6cccc: ldur            x6, [fp, #-0x10]
    // 0xe6ccd0: r7 = 4278255360
    //     0xe6ccd0: movz            x7, #0xff00
    //     0xe6ccd4: movk            x7, #0xff00, lsl #16
    // 0xe6ccd8: r8 = 16711935
    //     0xe6ccd8: movz            x8, #0xff
    //     0xe6ccdc: movk            x8, #0xff, lsl #16
    // 0xe6cce0: r9 = 4294901760
    //     0xe6cce0: orr             x9, xzr, #0xffff0000
    // 0xe6cce4: r10 = 65535
    //     0xe6cce4: orr             x10, xzr, #0xffff
    // 0xe6cce8: r0 = BoxInt64Instr(r3)
    //     0xe6cce8: sbfiz           x0, x3, #1, #0x1f
    //     0xe6ccec: cmp             x3, x0, asr #1
    //     0xe6ccf0: b.eq            #0xe6ccfc
    //     0xe6ccf4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6ccf8: stur            x3, [x0, #7]
    // 0xe6ccfc: mov             x4, x0
    // 0xe6cd00: r0 = AllocateFloat64Array()
    //     0xe6cd00: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6cd04: mov             x2, x0
    // 0xe6cd08: ldur            x0, [fp, #-0x10]
    // 0xe6cd0c: stur            x2, [fp, #-0x58]
    // 0xe6cd10: sub             x3, x0, #7
    // 0xe6cd14: stur            x3, [fp, #-0x18]
    // 0xe6cd18: r9 = 0
    //     0xe6cd18: movz            x9, #0
    // 0xe6cd1c: ldur            x5, [fp, #-0x40]
    // 0xe6cd20: ldur            x8, [fp, #-0x48]
    // 0xe6cd24: ldur            x4, [fp, #-0x20]
    // 0xe6cd28: ldur            x6, [fp, #-0x30]
    // 0xe6cd2c: ldur            x7, [fp, #-0x28]
    // 0xe6cd30: stur            x9, [fp, #-0x10]
    // 0xe6cd34: CheckStackOverflow
    //     0xe6cd34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6cd38: cmp             SP, x16
    //     0xe6cd3c: b.ls            #0xe6d008
    // 0xe6cd40: cmp             x9, x4
    // 0xe6cd44: b.ge            #0xe6cf24
    // 0xe6cd48: lsl             x0, x9, #3
    // 0xe6cd4c: add             x10, x8, x0
    // 0xe6cd50: mov             x0, x3
    // 0xe6cd54: mov             x1, x10
    // 0xe6cd58: stur            x10, [fp, #-8]
    // 0xe6cd5c: cmp             x1, x0
    // 0xe6cd60: b.hs            #0xe6d010
    // 0xe6cd64: r16 = Instance_Endian
    //     0xe6cd64: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xe6cd68: ldr             x16, [x16, #0x8b8]
    // 0xe6cd6c: cmp             w5, w16
    // 0xe6cd70: b.ne            #0xe6cd8c
    // 0xe6cd74: add             x0, x7, x10
    // 0xe6cd78: LoadField: r1 = r6->field_7
    //     0xe6cd78: ldur            x1, [x6, #7]
    // 0xe6cd7c: ldr             d0, [x1, x0]
    // 0xe6cd80: mov             x1, x9
    // 0xe6cd84: mov             x0, x2
    // 0xe6cd88: b               #0xe6cf0c
    // 0xe6cd8c: r0 = InitLateStaticField(0x324) // [dart:typed_data] ::_convU64
    //     0xe6cd8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6cd90: ldr             x0, [x0, #0x648]
    //     0xe6cd94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6cd98: cmp             w0, w16
    //     0xe6cd9c: b.ne            #0xe6cdac
    //     0xe6cda0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae00] Field <::._convU64@8027147>: static late final (offset: 0x324)
    //     0xe6cda4: ldr             x2, [x2, #0xe00]
    //     0xe6cda8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6cdac: mov             x3, x0
    // 0xe6cdb0: ldur            x0, [fp, #-8]
    // 0xe6cdb4: ldur            x2, [fp, #-0x28]
    // 0xe6cdb8: add             x1, x2, x0
    // 0xe6cdbc: ldur            x4, [fp, #-0x30]
    // 0xe6cdc0: LoadField: r0 = r4->field_7
    //     0xe6cdc0: ldur            x0, [x4, #7]
    // 0xe6cdc4: ldr             x5, [x0, x1]
    // 0xe6cdc8: mov             x0, x5
    // 0xe6cdcc: ubfx            x0, x0, #0, #0x20
    // 0xe6cdd0: r6 = 4278255360
    //     0xe6cdd0: movz            x6, #0xff00
    //     0xe6cdd4: movk            x6, #0xff00, lsl #16
    // 0xe6cdd8: and             x1, x0, x6
    // 0xe6cddc: ubfx            x1, x1, #0, #0x20
    // 0xe6cde0: asr             x0, x1, #8
    // 0xe6cde4: mov             x1, x5
    // 0xe6cde8: ubfx            x1, x1, #0, #0x20
    // 0xe6cdec: r7 = 16711935
    //     0xe6cdec: movz            x7, #0xff
    //     0xe6cdf0: movk            x7, #0xff, lsl #16
    // 0xe6cdf4: and             x8, x1, x7
    // 0xe6cdf8: ubfx            x8, x8, #0, #0x20
    // 0xe6cdfc: lsl             x1, x8, #8
    // 0xe6ce00: orr             x8, x0, x1
    // 0xe6ce04: mov             x0, x8
    // 0xe6ce08: ubfx            x0, x0, #0, #0x20
    // 0xe6ce0c: r9 = 4294901760
    //     0xe6ce0c: orr             x9, xzr, #0xffff0000
    // 0xe6ce10: and             x1, x0, x9
    // 0xe6ce14: ubfx            x1, x1, #0, #0x20
    // 0xe6ce18: asr             x0, x1, #0x10
    // 0xe6ce1c: ubfx            x8, x8, #0, #0x20
    // 0xe6ce20: r10 = 65535
    //     0xe6ce20: orr             x10, xzr, #0xffff
    // 0xe6ce24: and             x1, x8, x10
    // 0xe6ce28: ubfx            x1, x1, #0, #0x20
    // 0xe6ce2c: lsl             x8, x1, #0x10
    // 0xe6ce30: orr             x1, x0, x8
    // 0xe6ce34: lsl             x0, x1, #0x20
    // 0xe6ce38: asr             x1, x5, #0x20
    // 0xe6ce3c: mov             x5, x1
    // 0xe6ce40: ubfx            x5, x5, #0, #0x20
    // 0xe6ce44: and             x8, x5, x6
    // 0xe6ce48: ubfx            x8, x8, #0, #0x20
    // 0xe6ce4c: asr             x5, x8, #8
    // 0xe6ce50: ubfx            x1, x1, #0, #0x20
    // 0xe6ce54: and             x8, x1, x7
    // 0xe6ce58: ubfx            x8, x8, #0, #0x20
    // 0xe6ce5c: lsl             x1, x8, #8
    // 0xe6ce60: orr             x8, x5, x1
    // 0xe6ce64: mov             x1, x8
    // 0xe6ce68: ubfx            x1, x1, #0, #0x20
    // 0xe6ce6c: and             x5, x1, x9
    // 0xe6ce70: ubfx            x5, x5, #0, #0x20
    // 0xe6ce74: asr             x1, x5, #0x10
    // 0xe6ce78: ubfx            x8, x8, #0, #0x20
    // 0xe6ce7c: and             x5, x8, x10
    // 0xe6ce80: ubfx            x5, x5, #0, #0x20
    // 0xe6ce84: lsl             x8, x5, #0x10
    // 0xe6ce88: orr             x5, x1, x8
    // 0xe6ce8c: orr             x8, x0, x5
    // 0xe6ce90: LoadField: r0 = r3->field_13
    //     0xe6ce90: ldur            w0, [x3, #0x13]
    // 0xe6ce94: r1 = LoadInt32Instr(r0)
    //     0xe6ce94: sbfx            x1, x0, #1, #0x1f
    // 0xe6ce98: mov             x0, x1
    // 0xe6ce9c: r1 = 0
    //     0xe6ce9c: movz            x1, #0
    // 0xe6cea0: cmp             x1, x0
    // 0xe6cea4: b.hs            #0xe6d014
    // 0xe6cea8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xe6cea8: ldur            w0, [x3, #0x17]
    // 0xe6ceac: DecompressPointer r0
    //     0xe6ceac: add             x0, x0, HEAP, lsl #32
    // 0xe6ceb0: LoadField: r1 = r3->field_1b
    //     0xe6ceb0: ldur            w1, [x3, #0x1b]
    // 0xe6ceb4: LoadField: r3 = r0->field_7
    //     0xe6ceb4: ldur            x3, [x0, #7]
    // 0xe6ceb8: asr             w0, w1, #1
    // 0xe6cebc: add             x0, x3, w0, sxtw
    // 0xe6cec0: str             x8, [x0]
    // 0xe6cec4: r0 = InitLateStaticField(0x32c) // [dart:typed_data] ::_convF64
    //     0xe6cec4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6cec8: ldr             x0, [x0, #0x658]
    //     0xe6cecc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6ced0: cmp             w0, w16
    //     0xe6ced4: b.ne            #0xe6cee4
    //     0xe6ced8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae08] Field <::._convF64@8027147>: static late final (offset: 0x32c)
    //     0xe6cedc: ldr             x2, [x2, #0xe08]
    //     0xe6cee0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6cee4: mov             x2, x0
    // 0xe6cee8: LoadField: r3 = r2->field_13
    //     0xe6cee8: ldur            w3, [x2, #0x13]
    // 0xe6ceec: r0 = LoadInt32Instr(r3)
    //     0xe6ceec: sbfx            x0, x3, #1, #0x1f
    // 0xe6cef0: r1 = 0
    //     0xe6cef0: movz            x1, #0
    // 0xe6cef4: cmp             x1, x0
    // 0xe6cef8: b.hs            #0xe6d018
    // 0xe6cefc: LoadField: r1 = r2->field_7
    //     0xe6cefc: ldur            x1, [x2, #7]
    // 0xe6cf00: ldr             d0, [x1]
    // 0xe6cf04: ldur            x1, [fp, #-0x10]
    // 0xe6cf08: ldur            x0, [fp, #-0x58]
    // 0xe6cf0c: ArrayStore: r0[r1] = d0  ; List_8
    //     0xe6cf0c: add             x2, x0, x1, lsl #3
    //     0xe6cf10: stur            d0, [x2, #0x17]
    // 0xe6cf14: add             x9, x1, #1
    // 0xe6cf18: ldur            x3, [fp, #-0x18]
    // 0xe6cf1c: mov             x2, x0
    // 0xe6cf20: b               #0xe6cd1c
    // 0xe6cf24: mov             x0, x2
    // 0xe6cf28: LeaveFrame
    //     0xe6cf28: mov             SP, fp
    //     0xe6cf2c: ldp             fp, lr, [SP], #0x10
    // 0xe6cf30: ret
    //     0xe6cf30: ret             
    // 0xe6cf34: r0 = Null
    //     0xe6cf34: mov             x0, NULL
    // 0xe6cf38: LeaveFrame
    //     0xe6cf38: mov             SP, fp
    //     0xe6cf3c: ldp             fp, lr, [SP], #0x10
    // 0xe6cf40: ret
    //     0xe6cf40: ret             
    // 0xe6cf44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cf48: b               #0xe6b660
    // 0xe6cf4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf58: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cf60: b               #0xe6b98c
    // 0xe6cf64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cf6c: b               #0xe6bb20
    // 0xe6cf70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cf80: b               #0xe6bde8
    // 0xe6cf84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf8c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cf94: b               #0xe6c0c4
    // 0xe6cf98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cf98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cf9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cf9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cfa0: b               #0xe6c264
    // 0xe6cfa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfa4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfa8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cfb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cfb4: b               #0xe6c53c
    // 0xe6cfb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfb8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfbc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfc0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfc4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfc8: SaveReg d0
    //     0xe6cfc8: str             q0, [SP, #-0x10]!
    // 0xe6cfcc: r0 = AllocateDouble()
    //     0xe6cfcc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6cfd0: RestoreReg d0
    //     0xe6cfd0: ldr             q0, [SP], #0x10
    // 0xe6cfd4: b               #0xe6c8c4
    // 0xe6cfd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6cfd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6cfdc: b               #0xe6c940
    // 0xe6cfe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfe0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfe4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfe4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfe8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cfec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cfec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cff0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cff0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6cff4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6cff8: SaveReg d0
    //     0xe6cff8: str             q0, [SP, #-0x10]!
    // 0xe6cffc: r0 = AllocateDouble()
    //     0xe6cffc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6d000: RestoreReg d0
    //     0xe6d000: ldr             q0, [SP], #0x10
    // 0xe6d004: b               #0xe6ccb4
    // 0xe6d008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6d008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6d00c: b               #0xe6cd40
    // 0xe6d010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6d010: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6d014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6d014: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6d018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6d018: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _getStringFromDB(/* No info */) {
    // ** addr: 0xe6d088, size: 0xec
    // 0xe6d088: EnterFrame
    //     0xe6d088: stp             fp, lr, [SP, #-0x10]!
    //     0xe6d08c: mov             fp, SP
    // 0xe6d090: AllocStack(0x18)
    //     0xe6d090: sub             SP, SP, #0x18
    // 0xe6d094: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2 */)
    //     0xe6d094: mov             x0, x2
    //     0xe6d098: stur            x2, [fp, #-0x10]
    //     0xe6d09c: mov             x2, x3
    //     0xe6d0a0: mov             x3, x1
    //     0xe6d0a4: stur            x1, [fp, #-8]
    // 0xe6d0a8: CheckStackOverflow
    //     0xe6d0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6d0ac: cmp             SP, x16
    //     0xe6d0b0: b.ls            #0xe6d160
    // 0xe6d0b4: r1 = <int>
    //     0xe6d0b4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6d0b8: r0 = _GrowableList()
    //     0xe6d0b8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe6d0bc: mov             x2, x0
    // 0xe6d0c0: LoadField: r0 = r2->field_b
    //     0xe6d0c0: ldur            w0, [x2, #0xb]
    // 0xe6d0c4: r3 = LoadInt32Instr(r0)
    //     0xe6d0c4: sbfx            x3, x0, #1, #0x1f
    // 0xe6d0c8: ldur            x0, [fp, #-8]
    // 0xe6d0cc: LoadField: r1 = r0->field_13
    //     0xe6d0cc: ldur            w1, [x0, #0x13]
    // 0xe6d0d0: r4 = LoadInt32Instr(r1)
    //     0xe6d0d0: sbfx            x4, x1, #1, #0x1f
    // 0xe6d0d4: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xe6d0d4: ldur            w5, [x0, #0x17]
    // 0xe6d0d8: DecompressPointer r5
    //     0xe6d0d8: add             x5, x5, HEAP, lsl #32
    // 0xe6d0dc: LoadField: r1 = r0->field_1b
    //     0xe6d0dc: ldur            w1, [x0, #0x1b]
    // 0xe6d0e0: r6 = LoadInt32Instr(r1)
    //     0xe6d0e0: sbfx            x6, x1, #1, #0x1f
    // 0xe6d0e4: LoadField: r7 = r2->field_f
    //     0xe6d0e4: ldur            w7, [x2, #0xf]
    // 0xe6d0e8: DecompressPointer r7
    //     0xe6d0e8: add             x7, x7, HEAP, lsl #32
    // 0xe6d0ec: ldur            x8, [fp, #-0x10]
    // 0xe6d0f0: r9 = 0
    //     0xe6d0f0: movz            x9, #0
    // 0xe6d0f4: CheckStackOverflow
    //     0xe6d0f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6d0f8: cmp             SP, x16
    //     0xe6d0fc: b.ls            #0xe6d168
    // 0xe6d100: cmp             x9, x3
    // 0xe6d104: b.ge            #0xe6d140
    // 0xe6d108: add             x10, x8, x9
    // 0xe6d10c: mov             x0, x4
    // 0xe6d110: mov             x1, x10
    // 0xe6d114: cmp             x1, x0
    // 0xe6d118: b.hs            #0xe6d170
    // 0xe6d11c: add             x0, x6, x10
    // 0xe6d120: LoadField: r1 = r5->field_7
    //     0xe6d120: ldur            x1, [x5, #7]
    // 0xe6d124: ldrb            w10, [x1, x0]
    // 0xe6d128: lsl             x0, x10, #1
    // 0xe6d12c: ArrayStore: r7[r9] = r0  ; Unknown_4
    //     0xe6d12c: add             x1, x7, x9, lsl #2
    //     0xe6d130: stur            w0, [x1, #0xf]
    // 0xe6d134: add             x0, x9, #1
    // 0xe6d138: mov             x9, x0
    // 0xe6d13c: b               #0xe6d0f4
    // 0xe6d140: r16 = true
    //     0xe6d140: add             x16, NULL, #0x20  ; true
    // 0xe6d144: str             x16, [SP]
    // 0xe6d148: r1 = Instance_Utf8Codec
    //     0xe6d148: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0xe6d14c: r4 = const [0, 0x3, 0x1, 0x2, allowMalformed, 0x2, null]
    //     0xe6d14c: ldr             x4, [PP, #0x3368]  ; [pp+0x3368] List(7) [0, 0x3, 0x1, 0x2, "allowMalformed", 0x2, Null]
    // 0xe6d150: r0 = decode()
    //     0xe6d150: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0xe6d154: LeaveFrame
    //     0xe6d154: mov             SP, fp
    //     0xe6d158: ldp             fp, lr, [SP], #0x10
    // 0xe6d15c: ret
    //     0xe6d15c: ret             
    // 0xe6d160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6d160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6d164: b               #0xe6d0b4
    // 0xe6d168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6d168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6d16c: b               #0xe6d100
    // 0xe6d170: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6d170: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 6820, size: 0x14, field offset: 0x14
enum PdfExifTag extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d820, size: 0x64
    // 0xc4d820: EnterFrame
    //     0xc4d820: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d824: mov             fp, SP
    // 0xc4d828: AllocStack(0x10)
    //     0xc4d828: sub             SP, SP, #0x10
    // 0xc4d82c: SetupParameters(PdfExifTag this /* r1 => r0, fp-0x8 */)
    //     0xc4d82c: mov             x0, x1
    //     0xc4d830: stur            x1, [fp, #-8]
    // 0xc4d834: CheckStackOverflow
    //     0xc4d834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d838: cmp             SP, x16
    //     0xc4d83c: b.ls            #0xc4d87c
    // 0xc4d840: r1 = Null
    //     0xc4d840: mov             x1, NULL
    // 0xc4d844: r2 = 4
    //     0xc4d844: movz            x2, #0x4
    // 0xc4d848: r0 = AllocateArray()
    //     0xc4d848: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d84c: r16 = "PdfExifTag."
    //     0xc4d84c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47858] "PdfExifTag."
    //     0xc4d850: ldr             x16, [x16, #0x858]
    // 0xc4d854: StoreField: r0->field_f = r16
    //     0xc4d854: stur            w16, [x0, #0xf]
    // 0xc4d858: ldur            x1, [fp, #-8]
    // 0xc4d85c: LoadField: r2 = r1->field_f
    //     0xc4d85c: ldur            w2, [x1, #0xf]
    // 0xc4d860: DecompressPointer r2
    //     0xc4d860: add             x2, x2, HEAP, lsl #32
    // 0xc4d864: StoreField: r0->field_13 = r2
    //     0xc4d864: stur            w2, [x0, #0x13]
    // 0xc4d868: str             x0, [SP]
    // 0xc4d86c: r0 = _interpolate()
    //     0xc4d86c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d870: LeaveFrame
    //     0xc4d870: mov             SP, fp
    //     0xc4d874: ldp             fp, lr, [SP], #0x10
    // 0xc4d878: ret
    //     0xc4d878: ret             
    // 0xc4d87c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d87c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d880: b               #0xc4d840
  }
}
