// lib: , url: package:pdf/src/pdf/font/bidi_utils.dart

// class id: 1050775, size: 0x8
class :: {

  static _ isArabicDiacriticValue(/* No info */) {
    // ** addr: 0x7b7204, size: 0x50
    // 0x7b7204: EnterFrame
    //     0x7b7204: stp             fp, lr, [SP, #-0x10]!
    //     0x7b7208: mov             fp, SP
    // 0x7b720c: mov             x2, x1
    // 0x7b7210: CheckStackOverflow
    //     0x7b7210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b7214: cmp             SP, x16
    //     0x7b7218: b.ls            #0x7b724c
    // 0x7b721c: r0 = BoxInt64Instr(r2)
    //     0x7b721c: sbfiz           x0, x2, #1, #0x1f
    //     0x7b7220: cmp             x2, x0, asr #1
    //     0x7b7224: b.eq            #0x7b7230
    //     0x7b7228: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b722c: stur            x2, [x0, #7]
    // 0x7b7230: mov             x2, x0
    // 0x7b7234: r1 = _ConstMap len:15
    //     0x7b7234: add             x1, PP, #0x47, lsl #12  ; [pp+0x47530] Map<int, int>(15)
    //     0x7b7238: ldr             x1, [x1, #0x530]
    // 0x7b723c: r0 = containsValue()
    //     0x7b723c: bl              #0x7b7254  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::containsValue
    // 0x7b7240: LeaveFrame
    //     0x7b7240: mov             SP, fp
    //     0x7b7244: ldp             fp, lr, [SP], #0x10
    // 0x7b7248: ret
    //     0x7b7248: ret             
    // 0x7b724c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b724c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7250: b               #0x7b721c
  }
  static _ logicalToVisual(/* No info */) {
    // ** addr: 0xe95aec, size: 0x378
    // 0xe95aec: EnterFrame
    //     0xe95aec: stp             fp, lr, [SP, #-0x10]!
    //     0xe95af0: mov             fp, SP
    // 0xe95af4: AllocStack(0x58)
    //     0xe95af4: sub             SP, SP, #0x58
    // 0xe95af8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xe95af8: mov             x2, x1
    //     0xe95afc: stur            x1, [fp, #-8]
    // 0xe95b00: CheckStackOverflow
    //     0xe95b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe95b04: cmp             SP, x16
    //     0xe95b08: b.ls            #0xe95e54
    // 0xe95b0c: r0 = StringBuffer()
    //     0xe95b0c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xe95b10: mov             x1, x0
    // 0xe95b14: stur            x0, [fp, #-0x10]
    // 0xe95b18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe95b18: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe95b1c: r0 = StringBuffer()
    //     0xe95b1c: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xe95b20: ldur            x2, [fp, #-8]
    // 0xe95b24: r1 = Null
    //     0xe95b24: mov             x1, NULL
    // 0xe95b28: r0 = BidiString.fromLogical()
    //     0xe95b28: bl              #0xe95e88  ; [package:bidi/bidi.dart] BidiString::BidiString.fromLogical
    // 0xe95b2c: LoadField: r2 = r0->field_7
    //     0xe95b2c: ldur            w2, [x0, #7]
    // 0xe95b30: DecompressPointer r2
    //     0xe95b30: add             x2, x2, HEAP, lsl #32
    // 0xe95b34: stur            x2, [fp, #-0x38]
    // 0xe95b38: LoadField: r0 = r2->field_b
    //     0xe95b38: ldur            w0, [x2, #0xb]
    // 0xe95b3c: r3 = LoadInt32Instr(r0)
    //     0xe95b3c: sbfx            x3, x0, #1, #0x1f
    // 0xe95b40: stur            x3, [fp, #-0x30]
    // 0xe95b44: r0 = 0
    //     0xe95b44: movz            x0, #0
    // 0xe95b48: CheckStackOverflow
    //     0xe95b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe95b4c: cmp             SP, x16
    //     0xe95b50: b.ls            #0xe95e5c
    // 0xe95b54: LoadField: r1 = r2->field_b
    //     0xe95b54: ldur            w1, [x2, #0xb]
    // 0xe95b58: r4 = LoadInt32Instr(r1)
    //     0xe95b58: sbfx            x4, x1, #1, #0x1f
    // 0xe95b5c: cmp             x3, x4
    // 0xe95b60: b.ne            #0xe95e34
    // 0xe95b64: cmp             x0, x4
    // 0xe95b68: b.ge            #0xe95e1c
    // 0xe95b6c: LoadField: r1 = r2->field_f
    //     0xe95b6c: ldur            w1, [x2, #0xf]
    // 0xe95b70: DecompressPointer r1
    //     0xe95b70: add             x1, x1, HEAP, lsl #32
    // 0xe95b74: ArrayLoad: r4 = r1[r0]  ; Unknown_4
    //     0xe95b74: add             x16, x1, x0, lsl #2
    //     0xe95b78: ldur            w4, [x16, #0xf]
    // 0xe95b7c: DecompressPointer r4
    //     0xe95b7c: add             x4, x4, HEAP, lsl #32
    // 0xe95b80: add             x5, x0, #1
    // 0xe95b84: stur            x5, [fp, #-0x28]
    // 0xe95b88: LoadField: r0 = r4->field_7
    //     0xe95b88: ldur            x0, [x4, #7]
    // 0xe95b8c: stur            x0, [fp, #-0x20]
    // 0xe95b90: cmp             x0, #0xa
    // 0xe95b94: r16 = true
    //     0xe95b94: add             x16, NULL, #0x20  ; true
    // 0xe95b98: r17 = false
    //     0xe95b98: add             x17, NULL, #0x30  ; false
    // 0xe95b9c: csel            x6, x16, x17, eq
    // 0xe95ba0: stur            x6, [fp, #-0x18]
    // 0xe95ba4: LoadField: r7 = r4->field_13
    //     0xe95ba4: ldur            w7, [x4, #0x13]
    // 0xe95ba8: DecompressPointer r7
    //     0xe95ba8: add             x7, x7, HEAP, lsl #32
    // 0xe95bac: mov             x1, x7
    // 0xe95bb0: stur            x7, [fp, #-8]
    // 0xe95bb4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe95bb4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe95bb8: r0 = toList()
    //     0xe95bb8: bl              #0xa52dc8  ; [dart:core] _GrowableList::toList
    // 0xe95bbc: mov             x2, x0
    // 0xe95bc0: ldur            x0, [fp, #-0x20]
    // 0xe95bc4: stur            x2, [fp, #-0x48]
    // 0xe95bc8: r17 = 65535
    //     0xe95bc8: orr             x17, xzr, #0xffff
    // 0xe95bcc: cmp             x0, x17
    // 0xe95bd0: b.eq            #0xe95c64
    // 0xe95bd4: LoadField: r1 = r2->field_b
    //     0xe95bd4: ldur            w1, [x2, #0xb]
    // 0xe95bd8: LoadField: r3 = r2->field_f
    //     0xe95bd8: ldur            w3, [x2, #0xf]
    // 0xe95bdc: DecompressPointer r3
    //     0xe95bdc: add             x3, x3, HEAP, lsl #32
    // 0xe95be0: LoadField: r4 = r3->field_b
    //     0xe95be0: ldur            w4, [x3, #0xb]
    // 0xe95be4: r3 = LoadInt32Instr(r1)
    //     0xe95be4: sbfx            x3, x1, #1, #0x1f
    // 0xe95be8: stur            x3, [fp, #-0x40]
    // 0xe95bec: r1 = LoadInt32Instr(r4)
    //     0xe95bec: sbfx            x1, x4, #1, #0x1f
    // 0xe95bf0: cmp             x3, x1
    // 0xe95bf4: b.ne            #0xe95c00
    // 0xe95bf8: mov             x1, x2
    // 0xe95bfc: r0 = _growToNextCapacity()
    //     0xe95bfc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe95c00: ldur            x2, [fp, #-0x20]
    // 0xe95c04: ldur            x3, [fp, #-0x48]
    // 0xe95c08: ldur            x4, [fp, #-0x40]
    // 0xe95c0c: add             x0, x4, #1
    // 0xe95c10: lsl             x1, x0, #1
    // 0xe95c14: StoreField: r3->field_b = r1
    //     0xe95c14: stur            w1, [x3, #0xb]
    // 0xe95c18: LoadField: r5 = r3->field_f
    //     0xe95c18: ldur            w5, [x3, #0xf]
    // 0xe95c1c: DecompressPointer r5
    //     0xe95c1c: add             x5, x5, HEAP, lsl #32
    // 0xe95c20: r0 = BoxInt64Instr(r2)
    //     0xe95c20: sbfiz           x0, x2, #1, #0x1f
    //     0xe95c24: cmp             x2, x0, asr #1
    //     0xe95c28: b.eq            #0xe95c34
    //     0xe95c2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe95c30: stur            x2, [x0, #7]
    // 0xe95c34: mov             x1, x5
    // 0xe95c38: ArrayStore: r1[r4] = r0  ; List_4
    //     0xe95c38: add             x25, x1, x4, lsl #2
    //     0xe95c3c: add             x25, x25, #0xf
    //     0xe95c40: str             w0, [x25]
    //     0xe95c44: tbz             w0, #0, #0xe95c60
    //     0xe95c48: ldurb           w16, [x1, #-1]
    //     0xe95c4c: ldurb           w17, [x0, #-1]
    //     0xe95c50: and             x16, x17, x16, lsr #2
    //     0xe95c54: tst             x16, HEAP, lsr #32
    //     0xe95c58: b.eq            #0xe95c60
    //     0xe95c5c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe95c60: b               #0xe95c6c
    // 0xe95c64: mov             x3, x2
    // 0xe95c68: mov             x2, x0
    // 0xe95c6c: ldur            x0, [fp, #-0x18]
    // 0xe95c70: LoadField: r1 = r3->field_b
    //     0xe95c70: ldur            w1, [x3, #0xb]
    // 0xe95c74: tst             x0, #0x10
    // 0xe95c78: cset            x3, eq
    // 0xe95c7c: lsl             x3, x3, #1
    // 0xe95c80: r4 = LoadInt32Instr(r1)
    //     0xe95c80: sbfx            x4, x1, #1, #0x1f
    // 0xe95c84: r1 = LoadInt32Instr(r3)
    //     0xe95c84: sbfx            x1, x3, #1, #0x1f
    // 0xe95c88: sub             x3, x4, x1
    // 0xe95c8c: ldur            x1, [fp, #-8]
    // 0xe95c90: stur            x3, [fp, #-0x40]
    // 0xe95c94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe95c94: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe95c98: r0 = toList()
    //     0xe95c98: bl              #0xa52dc8  ; [dart:core] _GrowableList::toList
    // 0xe95c9c: mov             x2, x0
    // 0xe95ca0: ldur            x0, [fp, #-0x20]
    // 0xe95ca4: stur            x2, [fp, #-8]
    // 0xe95ca8: r17 = 65535
    //     0xe95ca8: orr             x17, xzr, #0xffff
    // 0xe95cac: cmp             x0, x17
    // 0xe95cb0: b.eq            #0xe95d44
    // 0xe95cb4: LoadField: r1 = r2->field_b
    //     0xe95cb4: ldur            w1, [x2, #0xb]
    // 0xe95cb8: LoadField: r3 = r2->field_f
    //     0xe95cb8: ldur            w3, [x2, #0xf]
    // 0xe95cbc: DecompressPointer r3
    //     0xe95cbc: add             x3, x3, HEAP, lsl #32
    // 0xe95cc0: LoadField: r4 = r3->field_b
    //     0xe95cc0: ldur            w4, [x3, #0xb]
    // 0xe95cc4: r3 = LoadInt32Instr(r1)
    //     0xe95cc4: sbfx            x3, x1, #1, #0x1f
    // 0xe95cc8: stur            x3, [fp, #-0x50]
    // 0xe95ccc: r1 = LoadInt32Instr(r4)
    //     0xe95ccc: sbfx            x1, x4, #1, #0x1f
    // 0xe95cd0: cmp             x3, x1
    // 0xe95cd4: b.ne            #0xe95ce0
    // 0xe95cd8: mov             x1, x2
    // 0xe95cdc: r0 = _growToNextCapacity()
    //     0xe95cdc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe95ce0: ldur            x3, [fp, #-8]
    // 0xe95ce4: ldur            x2, [fp, #-0x20]
    // 0xe95ce8: ldur            x4, [fp, #-0x50]
    // 0xe95cec: add             x0, x4, #1
    // 0xe95cf0: lsl             x1, x0, #1
    // 0xe95cf4: StoreField: r3->field_b = r1
    //     0xe95cf4: stur            w1, [x3, #0xb]
    // 0xe95cf8: LoadField: r5 = r3->field_f
    //     0xe95cf8: ldur            w5, [x3, #0xf]
    // 0xe95cfc: DecompressPointer r5
    //     0xe95cfc: add             x5, x5, HEAP, lsl #32
    // 0xe95d00: r0 = BoxInt64Instr(r2)
    //     0xe95d00: sbfiz           x0, x2, #1, #0x1f
    //     0xe95d04: cmp             x2, x0, asr #1
    //     0xe95d08: b.eq            #0xe95d14
    //     0xe95d0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe95d10: stur            x2, [x0, #7]
    // 0xe95d14: mov             x1, x5
    // 0xe95d18: ArrayStore: r1[r4] = r0  ; List_4
    //     0xe95d18: add             x25, x1, x4, lsl #2
    //     0xe95d1c: add             x25, x25, #0xf
    //     0xe95d20: str             w0, [x25]
    //     0xe95d24: tbz             w0, #0, #0xe95d40
    //     0xe95d28: ldurb           w16, [x1, #-1]
    //     0xe95d2c: ldurb           w17, [x0, #-1]
    //     0xe95d30: and             x16, x17, x16, lsr #2
    //     0xe95d34: tst             x16, HEAP, lsr #32
    //     0xe95d38: b.eq            #0xe95d40
    //     0xe95d3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe95d40: b               #0xe95d48
    // 0xe95d44: mov             x3, x2
    // 0xe95d48: ldur            x0, [fp, #-0x40]
    // 0xe95d4c: lsl             x1, x0, #1
    // 0xe95d50: mov             x16, x1
    // 0xe95d54: mov             x1, x3
    // 0xe95d58: mov             x3, x16
    // 0xe95d5c: r2 = 0
    //     0xe95d5c: movz            x2, #0
    // 0xe95d60: r0 = createFromCharCodes()
    //     0xe95d60: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xe95d64: r1 = LoadClassIdInstr(r0)
    //     0xe95d64: ldur            x1, [x0, #-1]
    //     0xe95d68: ubfx            x1, x1, #0xc, #0x14
    // 0xe95d6c: mov             x16, x0
    // 0xe95d70: mov             x0, x1
    // 0xe95d74: mov             x1, x16
    // 0xe95d78: r2 = " "
    //     0xe95d78: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe95d7c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe95d7c: sub             lr, x0, #1, lsl #12
    //     0xe95d80: ldr             lr, [x21, lr, lsl #3]
    //     0xe95d84: blr             lr
    // 0xe95d88: stur            x0, [fp, #-8]
    // 0xe95d8c: LoadField: r1 = r0->field_7
    //     0xe95d8c: ldur            w1, [x0, #7]
    // 0xe95d90: DecompressPointer r1
    //     0xe95d90: add             x1, x1, HEAP, lsl #32
    // 0xe95d94: r0 = ReversedListIterable()
    //     0xe95d94: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xe95d98: mov             x1, x0
    // 0xe95d9c: ldur            x0, [fp, #-8]
    // 0xe95da0: StoreField: r1->field_b = r0
    //     0xe95da0: stur            w0, [x1, #0xb]
    // 0xe95da4: r16 = " "
    //     0xe95da4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe95da8: str             x16, [SP]
    // 0xe95dac: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe95dac: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe95db0: r0 = join()
    //     0xe95db0: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xe95db4: r1 = LoadClassIdInstr(r0)
    //     0xe95db4: ldur            x1, [x0, #-1]
    //     0xe95db8: ubfx            x1, x1, #0xc, #0x14
    // 0xe95dbc: str             x0, [SP]
    // 0xe95dc0: mov             x0, x1
    // 0xe95dc4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe95dc4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe95dc8: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe95dc8: movz            x17, #0x2b03
    //     0xe95dcc: add             lr, x0, x17
    //     0xe95dd0: ldr             lr, [x21, lr, lsl #3]
    //     0xe95dd4: blr             lr
    // 0xe95dd8: LoadField: r1 = r0->field_7
    //     0xe95dd8: ldur            w1, [x0, #7]
    // 0xe95ddc: cbz             w1, #0xe95dec
    // 0xe95de0: ldur            x1, [fp, #-0x10]
    // 0xe95de4: mov             x2, x0
    // 0xe95de8: r0 = _writeString()
    //     0xe95de8: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xe95dec: ldur            x0, [fp, #-0x18]
    // 0xe95df0: tbnz            w0, #4, #0xe95e0c
    // 0xe95df4: ldur            x1, [fp, #-0x10]
    // 0xe95df8: r2 = ""
    //     0xe95df8: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe95dfc: r0 = write()
    //     0xe95dfc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xe95e00: ldur            x1, [fp, #-0x10]
    // 0xe95e04: r2 = "\n"
    //     0xe95e04: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xe95e08: r0 = _writeString()
    //     0xe95e08: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xe95e0c: ldur            x0, [fp, #-0x28]
    // 0xe95e10: ldur            x2, [fp, #-0x38]
    // 0xe95e14: ldur            x3, [fp, #-0x30]
    // 0xe95e18: b               #0xe95b48
    // 0xe95e1c: ldur            x16, [fp, #-0x10]
    // 0xe95e20: str             x16, [SP]
    // 0xe95e24: r0 = toString()
    //     0xe95e24: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xe95e28: LeaveFrame
    //     0xe95e28: mov             SP, fp
    //     0xe95e2c: ldp             fp, lr, [SP], #0x10
    // 0xe95e30: ret
    //     0xe95e30: ret             
    // 0xe95e34: mov             x0, x2
    // 0xe95e38: r0 = ConcurrentModificationError()
    //     0xe95e38: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe95e3c: mov             x1, x0
    // 0xe95e40: ldur            x0, [fp, #-0x38]
    // 0xe95e44: StoreField: r1->field_b = r0
    //     0xe95e44: stur            w0, [x1, #0xb]
    // 0xe95e48: mov             x0, x1
    // 0xe95e4c: r0 = Throw()
    //     0xe95e4c: bl              #0xec04b8  ; ThrowStub
    // 0xe95e50: brk             #0
    // 0xe95e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe95e54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe95e58: b               #0xe95b0c
    // 0xe95e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe95e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe95e60: b               #0xe95b54
  }
}
