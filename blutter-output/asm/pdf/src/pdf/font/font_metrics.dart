// lib: , url: package:pdf/src/pdf/font/font_metrics.dart

// class id: 1050776, size: 0x8
class :: {
}

// class id: 920, size: 0x48, field offset: 0x8
//   const constructor, 
class PdfFontMetrics extends Object {

  _Mint field_8;
  _Mint field_10;
  _Mint field_18;
  _Mint field_20;
  _Mint field_28;
  _Mint field_30;
  _Mint field_38;
  _Mint field_40;

  _ copyWith(/* No info */) {
    // ** addr: 0x7b6ea4, size: 0x354
    // 0x7b6ea4: EnterFrame
    //     0x7b6ea4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b6ea8: mov             fp, SP
    // 0x7b6eac: AllocStack(0x40)
    //     0x7b6eac: sub             SP, SP, #0x40
    // 0x7b6eb0: SetupParameters(dynamic _ /* d0 => d0, fp-0x40 */, {dynamic ascent = Null /* r3 */, dynamic bottom = Null /* r5 */, dynamic descent = Null /* r6 */, dynamic left = Null /* r7 */, dynamic leftBearing = Null /* r8 */, dynamic right = Null /* r9 */, dynamic top = Null /* r0 */})
    //     0x7b6eb0: stur            d0, [fp, #-0x40]
    //     0x7b6eb4: ldur            w0, [x4, #0x13]
    //     0x7b6eb8: ldur            w2, [x4, #0x1f]
    //     0x7b6ebc: add             x2, x2, HEAP, lsl #32
    //     0x7b6ec0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e1b8] "ascent"
    //     0x7b6ec4: ldr             x16, [x16, #0x1b8]
    //     0x7b6ec8: cmp             w2, w16
    //     0x7b6ecc: b.ne            #0x7b6ef0
    //     0x7b6ed0: ldur            w2, [x4, #0x23]
    //     0x7b6ed4: add             x2, x2, HEAP, lsl #32
    //     0x7b6ed8: sub             w3, w0, w2
    //     0x7b6edc: add             x2, fp, w3, sxtw #2
    //     0x7b6ee0: ldr             x2, [x2, #8]
    //     0x7b6ee4: mov             x3, x2
    //     0x7b6ee8: movz            x2, #0x1
    //     0x7b6eec: b               #0x7b6ef8
    //     0x7b6ef0: mov             x3, NULL
    //     0x7b6ef4: movz            x2, #0
    //     0x7b6ef8: lsl             x5, x2, #1
    //     0x7b6efc: lsl             w6, w5, #1
    //     0x7b6f00: add             w7, w6, #8
    //     0x7b6f04: add             x16, x4, w7, sxtw #1
    //     0x7b6f08: ldur            w8, [x16, #0xf]
    //     0x7b6f0c: add             x8, x8, HEAP, lsl #32
    //     0x7b6f10: ldr             x16, [PP, #0x7030]  ; [pp+0x7030] "bottom"
    //     0x7b6f14: cmp             w8, w16
    //     0x7b6f18: b.ne            #0x7b6f4c
    //     0x7b6f1c: add             w2, w6, #0xa
    //     0x7b6f20: add             x16, x4, w2, sxtw #1
    //     0x7b6f24: ldur            w6, [x16, #0xf]
    //     0x7b6f28: add             x6, x6, HEAP, lsl #32
    //     0x7b6f2c: sub             w2, w0, w6
    //     0x7b6f30: add             x6, fp, w2, sxtw #2
    //     0x7b6f34: ldr             x6, [x6, #8]
    //     0x7b6f38: add             w2, w5, #2
    //     0x7b6f3c: sbfx            x5, x2, #1, #0x1f
    //     0x7b6f40: mov             x2, x5
    //     0x7b6f44: mov             x5, x6
    //     0x7b6f48: b               #0x7b6f50
    //     0x7b6f4c: mov             x5, NULL
    //     0x7b6f50: lsl             x6, x2, #1
    //     0x7b6f54: lsl             w7, w6, #1
    //     0x7b6f58: add             w8, w7, #8
    //     0x7b6f5c: add             x16, x4, w8, sxtw #1
    //     0x7b6f60: ldur            w9, [x16, #0xf]
    //     0x7b6f64: add             x9, x9, HEAP, lsl #32
    //     0x7b6f68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e1c0] "descent"
    //     0x7b6f6c: ldr             x16, [x16, #0x1c0]
    //     0x7b6f70: cmp             w9, w16
    //     0x7b6f74: b.ne            #0x7b6fa8
    //     0x7b6f78: add             w2, w7, #0xa
    //     0x7b6f7c: add             x16, x4, w2, sxtw #1
    //     0x7b6f80: ldur            w7, [x16, #0xf]
    //     0x7b6f84: add             x7, x7, HEAP, lsl #32
    //     0x7b6f88: sub             w2, w0, w7
    //     0x7b6f8c: add             x7, fp, w2, sxtw #2
    //     0x7b6f90: ldr             x7, [x7, #8]
    //     0x7b6f94: add             w2, w6, #2
    //     0x7b6f98: sbfx            x6, x2, #1, #0x1f
    //     0x7b6f9c: mov             x2, x6
    //     0x7b6fa0: mov             x6, x7
    //     0x7b6fa4: b               #0x7b6fac
    //     0x7b6fa8: mov             x6, NULL
    //     0x7b6fac: lsl             x7, x2, #1
    //     0x7b6fb0: lsl             w8, w7, #1
    //     0x7b6fb4: add             w9, w8, #8
    //     0x7b6fb8: add             x16, x4, w9, sxtw #1
    //     0x7b6fbc: ldur            w10, [x16, #0xf]
    //     0x7b6fc0: add             x10, x10, HEAP, lsl #32
    //     0x7b6fc4: ldr             x16, [PP, #0x7038]  ; [pp+0x7038] "left"
    //     0x7b6fc8: cmp             w10, w16
    //     0x7b6fcc: b.ne            #0x7b7000
    //     0x7b6fd0: add             w2, w8, #0xa
    //     0x7b6fd4: add             x16, x4, w2, sxtw #1
    //     0x7b6fd8: ldur            w8, [x16, #0xf]
    //     0x7b6fdc: add             x8, x8, HEAP, lsl #32
    //     0x7b6fe0: sub             w2, w0, w8
    //     0x7b6fe4: add             x8, fp, w2, sxtw #2
    //     0x7b6fe8: ldr             x8, [x8, #8]
    //     0x7b6fec: add             w2, w7, #2
    //     0x7b6ff0: sbfx            x7, x2, #1, #0x1f
    //     0x7b6ff4: mov             x2, x7
    //     0x7b6ff8: mov             x7, x8
    //     0x7b6ffc: b               #0x7b7004
    //     0x7b7000: mov             x7, NULL
    //     0x7b7004: lsl             x8, x2, #1
    //     0x7b7008: lsl             w9, w8, #1
    //     0x7b700c: add             w10, w9, #8
    //     0x7b7010: add             x16, x4, w10, sxtw #1
    //     0x7b7014: ldur            w11, [x16, #0xf]
    //     0x7b7018: add             x11, x11, HEAP, lsl #32
    //     0x7b701c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e1c8] "leftBearing"
    //     0x7b7020: ldr             x16, [x16, #0x1c8]
    //     0x7b7024: cmp             w11, w16
    //     0x7b7028: b.ne            #0x7b705c
    //     0x7b702c: add             w2, w9, #0xa
    //     0x7b7030: add             x16, x4, w2, sxtw #1
    //     0x7b7034: ldur            w9, [x16, #0xf]
    //     0x7b7038: add             x9, x9, HEAP, lsl #32
    //     0x7b703c: sub             w2, w0, w9
    //     0x7b7040: add             x9, fp, w2, sxtw #2
    //     0x7b7044: ldr             x9, [x9, #8]
    //     0x7b7048: add             w2, w8, #2
    //     0x7b704c: sbfx            x8, x2, #1, #0x1f
    //     0x7b7050: mov             x2, x8
    //     0x7b7054: mov             x8, x9
    //     0x7b7058: b               #0x7b7060
    //     0x7b705c: mov             x8, NULL
    //     0x7b7060: lsl             x9, x2, #1
    //     0x7b7064: lsl             w10, w9, #1
    //     0x7b7068: add             w11, w10, #8
    //     0x7b706c: add             x16, x4, w11, sxtw #1
    //     0x7b7070: ldur            w12, [x16, #0xf]
    //     0x7b7074: add             x12, x12, HEAP, lsl #32
    //     0x7b7078: ldr             x16, [PP, #0x7040]  ; [pp+0x7040] "right"
    //     0x7b707c: cmp             w12, w16
    //     0x7b7080: b.ne            #0x7b70b4
    //     0x7b7084: add             w2, w10, #0xa
    //     0x7b7088: add             x16, x4, w2, sxtw #1
    //     0x7b708c: ldur            w10, [x16, #0xf]
    //     0x7b7090: add             x10, x10, HEAP, lsl #32
    //     0x7b7094: sub             w2, w0, w10
    //     0x7b7098: add             x10, fp, w2, sxtw #2
    //     0x7b709c: ldr             x10, [x10, #8]
    //     0x7b70a0: add             w2, w9, #2
    //     0x7b70a4: sbfx            x9, x2, #1, #0x1f
    //     0x7b70a8: mov             x2, x9
    //     0x7b70ac: mov             x9, x10
    //     0x7b70b0: b               #0x7b70b8
    //     0x7b70b4: mov             x9, NULL
    //     0x7b70b8: lsl             x10, x2, #1
    //     0x7b70bc: lsl             w2, w10, #1
    //     0x7b70c0: add             w10, w2, #8
    //     0x7b70c4: add             x16, x4, w10, sxtw #1
    //     0x7b70c8: ldur            w11, [x16, #0xf]
    //     0x7b70cc: add             x11, x11, HEAP, lsl #32
    //     0x7b70d0: ldr             x16, [PP, #0x7048]  ; [pp+0x7048] "top"
    //     0x7b70d4: cmp             w11, w16
    //     0x7b70d8: b.ne            #0x7b70fc
    //     0x7b70dc: add             w10, w2, #0xa
    //     0x7b70e0: add             x16, x4, w10, sxtw #1
    //     0x7b70e4: ldur            w2, [x16, #0xf]
    //     0x7b70e8: add             x2, x2, HEAP, lsl #32
    //     0x7b70ec: sub             w4, w0, w2
    //     0x7b70f0: add             x0, fp, w4, sxtw #2
    //     0x7b70f4: ldr             x0, [x0, #8]
    //     0x7b70f8: b               #0x7b7100
    //     0x7b70fc: mov             x0, NULL
    // 0x7b7100: cmp             w7, NULL
    // 0x7b7104: b.ne            #0x7b7110
    // 0x7b7108: LoadField: d1 = r1->field_7
    //     0x7b7108: ldur            d1, [x1, #7]
    // 0x7b710c: b               #0x7b7114
    // 0x7b7110: LoadField: d1 = r7->field_7
    //     0x7b7110: ldur            d1, [x7, #7]
    // 0x7b7114: stur            d1, [fp, #-0x38]
    // 0x7b7118: cmp             w0, NULL
    // 0x7b711c: b.ne            #0x7b7128
    // 0x7b7120: LoadField: d2 = r1->field_f
    //     0x7b7120: ldur            d2, [x1, #0xf]
    // 0x7b7124: b               #0x7b712c
    // 0x7b7128: LoadField: d2 = r0->field_7
    //     0x7b7128: ldur            d2, [x0, #7]
    // 0x7b712c: stur            d2, [fp, #-0x30]
    // 0x7b7130: cmp             w9, NULL
    // 0x7b7134: b.ne            #0x7b7140
    // 0x7b7138: LoadField: d3 = r1->field_1f
    //     0x7b7138: ldur            d3, [x1, #0x1f]
    // 0x7b713c: b               #0x7b7144
    // 0x7b7140: LoadField: d3 = r9->field_7
    //     0x7b7140: ldur            d3, [x9, #7]
    // 0x7b7144: stur            d3, [fp, #-0x28]
    // 0x7b7148: cmp             w5, NULL
    // 0x7b714c: b.ne            #0x7b7158
    // 0x7b7150: ArrayLoad: d4 = r1[0]  ; List_8
    //     0x7b7150: ldur            d4, [x1, #0x17]
    // 0x7b7154: b               #0x7b715c
    // 0x7b7158: LoadField: d4 = r5->field_7
    //     0x7b7158: ldur            d4, [x5, #7]
    // 0x7b715c: stur            d4, [fp, #-0x20]
    // 0x7b7160: cmp             w3, NULL
    // 0x7b7164: b.ne            #0x7b7170
    // 0x7b7168: LoadField: d5 = r1->field_27
    //     0x7b7168: ldur            d5, [x1, #0x27]
    // 0x7b716c: b               #0x7b7174
    // 0x7b7170: LoadField: d5 = r3->field_7
    //     0x7b7170: ldur            d5, [x3, #7]
    // 0x7b7174: stur            d5, [fp, #-0x18]
    // 0x7b7178: cmp             w6, NULL
    // 0x7b717c: b.ne            #0x7b7188
    // 0x7b7180: LoadField: d6 = r1->field_2f
    //     0x7b7180: ldur            d6, [x1, #0x2f]
    // 0x7b7184: b               #0x7b718c
    // 0x7b7188: LoadField: d6 = r6->field_7
    //     0x7b7188: ldur            d6, [x6, #7]
    // 0x7b718c: stur            d6, [fp, #-0x10]
    // 0x7b7190: cmp             w8, NULL
    // 0x7b7194: b.ne            #0x7b71a0
    // 0x7b7198: LoadField: d7 = r1->field_3f
    //     0x7b7198: ldur            d7, [x1, #0x3f]
    // 0x7b719c: b               #0x7b71a4
    // 0x7b71a0: LoadField: d7 = r8->field_7
    //     0x7b71a0: ldur            d7, [x8, #7]
    // 0x7b71a4: stur            d7, [fp, #-8]
    // 0x7b71a8: r0 = PdfFontMetrics()
    //     0x7b71a8: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0x7b71ac: ldur            d0, [fp, #-0x38]
    // 0x7b71b0: StoreField: r0->field_7 = d0
    //     0x7b71b0: stur            d0, [x0, #7]
    // 0x7b71b4: ldur            d0, [fp, #-0x30]
    // 0x7b71b8: StoreField: r0->field_f = d0
    //     0x7b71b8: stur            d0, [x0, #0xf]
    // 0x7b71bc: ldur            d0, [fp, #-0x28]
    // 0x7b71c0: StoreField: r0->field_1f = d0
    //     0x7b71c0: stur            d0, [x0, #0x1f]
    // 0x7b71c4: ldur            d0, [fp, #-0x20]
    // 0x7b71c8: ArrayStore: r0[0] = d0  ; List_8
    //     0x7b71c8: stur            d0, [x0, #0x17]
    // 0x7b71cc: ldur            d0, [fp, #-0x18]
    // 0x7b71d0: StoreField: r0->field_27 = d0
    //     0x7b71d0: stur            d0, [x0, #0x27]
    // 0x7b71d4: ldur            d0, [fp, #-0x10]
    // 0x7b71d8: StoreField: r0->field_2f = d0
    //     0x7b71d8: stur            d0, [x0, #0x2f]
    // 0x7b71dc: ldur            d0, [fp, #-0x40]
    // 0x7b71e0: StoreField: r0->field_37 = d0
    //     0x7b71e0: stur            d0, [x0, #0x37]
    // 0x7b71e4: ldur            d0, [fp, #-8]
    // 0x7b71e8: StoreField: r0->field_3f = d0
    //     0x7b71e8: stur            d0, [x0, #0x3f]
    // 0x7b71ec: LeaveFrame
    //     0x7b71ec: mov             SP, fp
    //     0x7b71f0: ldp             fp, lr, [SP], #0x10
    // 0x7b71f4: ret
    //     0x7b71f4: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc34aa8, size: 0x490
    // 0xc34aa8: EnterFrame
    //     0xc34aa8: stp             fp, lr, [SP, #-0x10]!
    //     0xc34aac: mov             fp, SP
    // 0xc34ab0: AllocStack(0x8)
    //     0xc34ab0: sub             SP, SP, #8
    // 0xc34ab4: CheckStackOverflow
    //     0xc34ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc34ab8: cmp             SP, x16
    //     0xc34abc: b.ls            #0xc34e50
    // 0xc34ac0: r1 = Null
    //     0xc34ac0: mov             x1, NULL
    // 0xc34ac4: r2 = 38
    //     0xc34ac4: movz            x2, #0x26
    // 0xc34ac8: r0 = AllocateArray()
    //     0xc34ac8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc34acc: mov             x2, x0
    // 0xc34ad0: r16 = "PdfFontMetrics(left:"
    //     0xc34ad0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f000] "PdfFontMetrics(left:"
    //     0xc34ad4: ldr             x16, [x16]
    // 0xc34ad8: StoreField: r2->field_f = r16
    //     0xc34ad8: stur            w16, [x2, #0xf]
    // 0xc34adc: ldr             x3, [fp, #0x10]
    // 0xc34ae0: LoadField: d0 = r3->field_7
    //     0xc34ae0: ldur            d0, [x3, #7]
    // 0xc34ae4: r0 = inline_Allocate_Double()
    //     0xc34ae4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34ae8: add             x0, x0, #0x10
    //     0xc34aec: cmp             x1, x0
    //     0xc34af0: b.ls            #0xc34e58
    //     0xc34af4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34af8: sub             x0, x0, #0xf
    //     0xc34afc: movz            x1, #0xe15c
    //     0xc34b00: movk            x1, #0x3, lsl #16
    //     0xc34b04: stur            x1, [x0, #-1]
    // 0xc34b08: StoreField: r0->field_7 = d0
    //     0xc34b08: stur            d0, [x0, #7]
    // 0xc34b0c: mov             x1, x2
    // 0xc34b10: ArrayStore: r1[1] = r0  ; List_4
    //     0xc34b10: add             x25, x1, #0x13
    //     0xc34b14: str             w0, [x25]
    //     0xc34b18: tbz             w0, #0, #0xc34b34
    //     0xc34b1c: ldurb           w16, [x1, #-1]
    //     0xc34b20: ldurb           w17, [x0, #-1]
    //     0xc34b24: and             x16, x17, x16, lsr #2
    //     0xc34b28: tst             x16, HEAP, lsr #32
    //     0xc34b2c: b.eq            #0xc34b34
    //     0xc34b30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34b34: r16 = ", top:"
    //     0xc34b34: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f008] ", top:"
    //     0xc34b38: ldr             x16, [x16, #8]
    // 0xc34b3c: ArrayStore: r2[0] = r16  ; List_4
    //     0xc34b3c: stur            w16, [x2, #0x17]
    // 0xc34b40: LoadField: d0 = r3->field_f
    //     0xc34b40: ldur            d0, [x3, #0xf]
    // 0xc34b44: r0 = inline_Allocate_Double()
    //     0xc34b44: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34b48: add             x0, x0, #0x10
    //     0xc34b4c: cmp             x1, x0
    //     0xc34b50: b.ls            #0xc34e70
    //     0xc34b54: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34b58: sub             x0, x0, #0xf
    //     0xc34b5c: movz            x1, #0xe15c
    //     0xc34b60: movk            x1, #0x3, lsl #16
    //     0xc34b64: stur            x1, [x0, #-1]
    // 0xc34b68: StoreField: r0->field_7 = d0
    //     0xc34b68: stur            d0, [x0, #7]
    // 0xc34b6c: mov             x1, x2
    // 0xc34b70: ArrayStore: r1[3] = r0  ; List_4
    //     0xc34b70: add             x25, x1, #0x1b
    //     0xc34b74: str             w0, [x25]
    //     0xc34b78: tbz             w0, #0, #0xc34b94
    //     0xc34b7c: ldurb           w16, [x1, #-1]
    //     0xc34b80: ldurb           w17, [x0, #-1]
    //     0xc34b84: and             x16, x17, x16, lsr #2
    //     0xc34b88: tst             x16, HEAP, lsr #32
    //     0xc34b8c: b.eq            #0xc34b94
    //     0xc34b90: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34b94: r16 = ", right:"
    //     0xc34b94: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f010] ", right:"
    //     0xc34b98: ldr             x16, [x16, #0x10]
    // 0xc34b9c: StoreField: r2->field_1f = r16
    //     0xc34b9c: stur            w16, [x2, #0x1f]
    // 0xc34ba0: LoadField: d0 = r3->field_1f
    //     0xc34ba0: ldur            d0, [x3, #0x1f]
    // 0xc34ba4: r0 = inline_Allocate_Double()
    //     0xc34ba4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34ba8: add             x0, x0, #0x10
    //     0xc34bac: cmp             x1, x0
    //     0xc34bb0: b.ls            #0xc34e88
    //     0xc34bb4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34bb8: sub             x0, x0, #0xf
    //     0xc34bbc: movz            x1, #0xe15c
    //     0xc34bc0: movk            x1, #0x3, lsl #16
    //     0xc34bc4: stur            x1, [x0, #-1]
    // 0xc34bc8: StoreField: r0->field_7 = d0
    //     0xc34bc8: stur            d0, [x0, #7]
    // 0xc34bcc: mov             x1, x2
    // 0xc34bd0: ArrayStore: r1[5] = r0  ; List_4
    //     0xc34bd0: add             x25, x1, #0x23
    //     0xc34bd4: str             w0, [x25]
    //     0xc34bd8: tbz             w0, #0, #0xc34bf4
    //     0xc34bdc: ldurb           w16, [x1, #-1]
    //     0xc34be0: ldurb           w17, [x0, #-1]
    //     0xc34be4: and             x16, x17, x16, lsr #2
    //     0xc34be8: tst             x16, HEAP, lsr #32
    //     0xc34bec: b.eq            #0xc34bf4
    //     0xc34bf0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34bf4: r16 = ", bottom:"
    //     0xc34bf4: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f018] ", bottom:"
    //     0xc34bf8: ldr             x16, [x16, #0x18]
    // 0xc34bfc: StoreField: r2->field_27 = r16
    //     0xc34bfc: stur            w16, [x2, #0x27]
    // 0xc34c00: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xc34c00: ldur            d1, [x3, #0x17]
    // 0xc34c04: r0 = inline_Allocate_Double()
    //     0xc34c04: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34c08: add             x0, x0, #0x10
    //     0xc34c0c: cmp             x1, x0
    //     0xc34c10: b.ls            #0xc34ea0
    //     0xc34c14: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34c18: sub             x0, x0, #0xf
    //     0xc34c1c: movz            x1, #0xe15c
    //     0xc34c20: movk            x1, #0x3, lsl #16
    //     0xc34c24: stur            x1, [x0, #-1]
    // 0xc34c28: StoreField: r0->field_7 = d1
    //     0xc34c28: stur            d1, [x0, #7]
    // 0xc34c2c: mov             x1, x2
    // 0xc34c30: ArrayStore: r1[7] = r0  ; List_4
    //     0xc34c30: add             x25, x1, #0x2b
    //     0xc34c34: str             w0, [x25]
    //     0xc34c38: tbz             w0, #0, #0xc34c54
    //     0xc34c3c: ldurb           w16, [x1, #-1]
    //     0xc34c40: ldurb           w17, [x0, #-1]
    //     0xc34c44: and             x16, x17, x16, lsr #2
    //     0xc34c48: tst             x16, HEAP, lsr #32
    //     0xc34c4c: b.eq            #0xc34c54
    //     0xc34c50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34c54: r16 = ", ascent:"
    //     0xc34c54: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f020] ", ascent:"
    //     0xc34c58: ldr             x16, [x16, #0x20]
    // 0xc34c5c: StoreField: r2->field_2f = r16
    //     0xc34c5c: stur            w16, [x2, #0x2f]
    // 0xc34c60: LoadField: d1 = r3->field_27
    //     0xc34c60: ldur            d1, [x3, #0x27]
    // 0xc34c64: r0 = inline_Allocate_Double()
    //     0xc34c64: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34c68: add             x0, x0, #0x10
    //     0xc34c6c: cmp             x1, x0
    //     0xc34c70: b.ls            #0xc34eb8
    //     0xc34c74: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34c78: sub             x0, x0, #0xf
    //     0xc34c7c: movz            x1, #0xe15c
    //     0xc34c80: movk            x1, #0x3, lsl #16
    //     0xc34c84: stur            x1, [x0, #-1]
    // 0xc34c88: StoreField: r0->field_7 = d1
    //     0xc34c88: stur            d1, [x0, #7]
    // 0xc34c8c: mov             x1, x2
    // 0xc34c90: ArrayStore: r1[9] = r0  ; List_4
    //     0xc34c90: add             x25, x1, #0x33
    //     0xc34c94: str             w0, [x25]
    //     0xc34c98: tbz             w0, #0, #0xc34cb4
    //     0xc34c9c: ldurb           w16, [x1, #-1]
    //     0xc34ca0: ldurb           w17, [x0, #-1]
    //     0xc34ca4: and             x16, x17, x16, lsr #2
    //     0xc34ca8: tst             x16, HEAP, lsr #32
    //     0xc34cac: b.eq            #0xc34cb4
    //     0xc34cb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34cb4: r16 = ", descent:"
    //     0xc34cb4: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f028] ", descent:"
    //     0xc34cb8: ldr             x16, [x16, #0x28]
    // 0xc34cbc: StoreField: r2->field_37 = r16
    //     0xc34cbc: stur            w16, [x2, #0x37]
    // 0xc34cc0: LoadField: d1 = r3->field_2f
    //     0xc34cc0: ldur            d1, [x3, #0x2f]
    // 0xc34cc4: r0 = inline_Allocate_Double()
    //     0xc34cc4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34cc8: add             x0, x0, #0x10
    //     0xc34ccc: cmp             x1, x0
    //     0xc34cd0: b.ls            #0xc34ed0
    //     0xc34cd4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34cd8: sub             x0, x0, #0xf
    //     0xc34cdc: movz            x1, #0xe15c
    //     0xc34ce0: movk            x1, #0x3, lsl #16
    //     0xc34ce4: stur            x1, [x0, #-1]
    // 0xc34ce8: StoreField: r0->field_7 = d1
    //     0xc34ce8: stur            d1, [x0, #7]
    // 0xc34cec: mov             x1, x2
    // 0xc34cf0: ArrayStore: r1[11] = r0  ; List_4
    //     0xc34cf0: add             x25, x1, #0x3b
    //     0xc34cf4: str             w0, [x25]
    //     0xc34cf8: tbz             w0, #0, #0xc34d14
    //     0xc34cfc: ldurb           w16, [x1, #-1]
    //     0xc34d00: ldurb           w17, [x0, #-1]
    //     0xc34d04: and             x16, x17, x16, lsr #2
    //     0xc34d08: tst             x16, HEAP, lsr #32
    //     0xc34d0c: b.eq            #0xc34d14
    //     0xc34d10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34d14: r16 = ", advanceWidth:"
    //     0xc34d14: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f030] ", advanceWidth:"
    //     0xc34d18: ldr             x16, [x16, #0x30]
    // 0xc34d1c: StoreField: r2->field_3f = r16
    //     0xc34d1c: stur            w16, [x2, #0x3f]
    // 0xc34d20: LoadField: d1 = r3->field_37
    //     0xc34d20: ldur            d1, [x3, #0x37]
    // 0xc34d24: r0 = inline_Allocate_Double()
    //     0xc34d24: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34d28: add             x0, x0, #0x10
    //     0xc34d2c: cmp             x1, x0
    //     0xc34d30: b.ls            #0xc34ee8
    //     0xc34d34: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34d38: sub             x0, x0, #0xf
    //     0xc34d3c: movz            x1, #0xe15c
    //     0xc34d40: movk            x1, #0x3, lsl #16
    //     0xc34d44: stur            x1, [x0, #-1]
    // 0xc34d48: StoreField: r0->field_7 = d1
    //     0xc34d48: stur            d1, [x0, #7]
    // 0xc34d4c: mov             x1, x2
    // 0xc34d50: ArrayStore: r1[13] = r0  ; List_4
    //     0xc34d50: add             x25, x1, #0x43
    //     0xc34d54: str             w0, [x25]
    //     0xc34d58: tbz             w0, #0, #0xc34d74
    //     0xc34d5c: ldurb           w16, [x1, #-1]
    //     0xc34d60: ldurb           w17, [x0, #-1]
    //     0xc34d64: and             x16, x17, x16, lsr #2
    //     0xc34d68: tst             x16, HEAP, lsr #32
    //     0xc34d6c: b.eq            #0xc34d74
    //     0xc34d70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34d74: r16 = ", leftBearing:"
    //     0xc34d74: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f038] ", leftBearing:"
    //     0xc34d78: ldr             x16, [x16, #0x38]
    // 0xc34d7c: StoreField: r2->field_47 = r16
    //     0xc34d7c: stur            w16, [x2, #0x47]
    // 0xc34d80: LoadField: d2 = r3->field_3f
    //     0xc34d80: ldur            d2, [x3, #0x3f]
    // 0xc34d84: r0 = inline_Allocate_Double()
    //     0xc34d84: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34d88: add             x0, x0, #0x10
    //     0xc34d8c: cmp             x1, x0
    //     0xc34d90: b.ls            #0xc34f00
    //     0xc34d94: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34d98: sub             x0, x0, #0xf
    //     0xc34d9c: movz            x1, #0xe15c
    //     0xc34da0: movk            x1, #0x3, lsl #16
    //     0xc34da4: stur            x1, [x0, #-1]
    // 0xc34da8: StoreField: r0->field_7 = d2
    //     0xc34da8: stur            d2, [x0, #7]
    // 0xc34dac: mov             x1, x2
    // 0xc34db0: ArrayStore: r1[15] = r0  ; List_4
    //     0xc34db0: add             x25, x1, #0x4b
    //     0xc34db4: str             w0, [x25]
    //     0xc34db8: tbz             w0, #0, #0xc34dd4
    //     0xc34dbc: ldurb           w16, [x1, #-1]
    //     0xc34dc0: ldurb           w17, [x0, #-1]
    //     0xc34dc4: and             x16, x17, x16, lsr #2
    //     0xc34dc8: tst             x16, HEAP, lsr #32
    //     0xc34dcc: b.eq            #0xc34dd4
    //     0xc34dd0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34dd4: r16 = ", rightBearing:"
    //     0xc34dd4: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f040] ", rightBearing:"
    //     0xc34dd8: ldr             x16, [x16, #0x40]
    // 0xc34ddc: StoreField: r2->field_4f = r16
    //     0xc34ddc: stur            w16, [x2, #0x4f]
    // 0xc34de0: fsub            d2, d1, d0
    // 0xc34de4: r0 = inline_Allocate_Double()
    //     0xc34de4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc34de8: add             x0, x0, #0x10
    //     0xc34dec: cmp             x1, x0
    //     0xc34df0: b.ls            #0xc34f20
    //     0xc34df4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc34df8: sub             x0, x0, #0xf
    //     0xc34dfc: movz            x1, #0xe15c
    //     0xc34e00: movk            x1, #0x3, lsl #16
    //     0xc34e04: stur            x1, [x0, #-1]
    // 0xc34e08: StoreField: r0->field_7 = d2
    //     0xc34e08: stur            d2, [x0, #7]
    // 0xc34e0c: mov             x1, x2
    // 0xc34e10: ArrayStore: r1[17] = r0  ; List_4
    //     0xc34e10: add             x25, x1, #0x53
    //     0xc34e14: str             w0, [x25]
    //     0xc34e18: tbz             w0, #0, #0xc34e34
    //     0xc34e1c: ldurb           w16, [x1, #-1]
    //     0xc34e20: ldurb           w17, [x0, #-1]
    //     0xc34e24: and             x16, x17, x16, lsr #2
    //     0xc34e28: tst             x16, HEAP, lsr #32
    //     0xc34e2c: b.eq            #0xc34e34
    //     0xc34e30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc34e34: r16 = ")"
    //     0xc34e34: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc34e38: StoreField: r2->field_57 = r16
    //     0xc34e38: stur            w16, [x2, #0x57]
    // 0xc34e3c: str             x2, [SP]
    // 0xc34e40: r0 = _interpolate()
    //     0xc34e40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc34e44: LeaveFrame
    //     0xc34e44: mov             SP, fp
    //     0xc34e48: ldp             fp, lr, [SP], #0x10
    // 0xc34e4c: ret
    //     0xc34e4c: ret             
    // 0xc34e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc34e50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc34e54: b               #0xc34ac0
    // 0xc34e58: SaveReg d0
    //     0xc34e58: str             q0, [SP, #-0x10]!
    // 0xc34e5c: stp             x2, x3, [SP, #-0x10]!
    // 0xc34e60: r0 = AllocateDouble()
    //     0xc34e60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34e64: ldp             x2, x3, [SP], #0x10
    // 0xc34e68: RestoreReg d0
    //     0xc34e68: ldr             q0, [SP], #0x10
    // 0xc34e6c: b               #0xc34b08
    // 0xc34e70: SaveReg d0
    //     0xc34e70: str             q0, [SP, #-0x10]!
    // 0xc34e74: stp             x2, x3, [SP, #-0x10]!
    // 0xc34e78: r0 = AllocateDouble()
    //     0xc34e78: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34e7c: ldp             x2, x3, [SP], #0x10
    // 0xc34e80: RestoreReg d0
    //     0xc34e80: ldr             q0, [SP], #0x10
    // 0xc34e84: b               #0xc34b68
    // 0xc34e88: SaveReg d0
    //     0xc34e88: str             q0, [SP, #-0x10]!
    // 0xc34e8c: stp             x2, x3, [SP, #-0x10]!
    // 0xc34e90: r0 = AllocateDouble()
    //     0xc34e90: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34e94: ldp             x2, x3, [SP], #0x10
    // 0xc34e98: RestoreReg d0
    //     0xc34e98: ldr             q0, [SP], #0x10
    // 0xc34e9c: b               #0xc34bc8
    // 0xc34ea0: stp             q0, q1, [SP, #-0x20]!
    // 0xc34ea4: stp             x2, x3, [SP, #-0x10]!
    // 0xc34ea8: r0 = AllocateDouble()
    //     0xc34ea8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34eac: ldp             x2, x3, [SP], #0x10
    // 0xc34eb0: ldp             q0, q1, [SP], #0x20
    // 0xc34eb4: b               #0xc34c28
    // 0xc34eb8: stp             q0, q1, [SP, #-0x20]!
    // 0xc34ebc: stp             x2, x3, [SP, #-0x10]!
    // 0xc34ec0: r0 = AllocateDouble()
    //     0xc34ec0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34ec4: ldp             x2, x3, [SP], #0x10
    // 0xc34ec8: ldp             q0, q1, [SP], #0x20
    // 0xc34ecc: b               #0xc34c88
    // 0xc34ed0: stp             q0, q1, [SP, #-0x20]!
    // 0xc34ed4: stp             x2, x3, [SP, #-0x10]!
    // 0xc34ed8: r0 = AllocateDouble()
    //     0xc34ed8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34edc: ldp             x2, x3, [SP], #0x10
    // 0xc34ee0: ldp             q0, q1, [SP], #0x20
    // 0xc34ee4: b               #0xc34ce8
    // 0xc34ee8: stp             q0, q1, [SP, #-0x20]!
    // 0xc34eec: stp             x2, x3, [SP, #-0x10]!
    // 0xc34ef0: r0 = AllocateDouble()
    //     0xc34ef0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34ef4: ldp             x2, x3, [SP], #0x10
    // 0xc34ef8: ldp             q0, q1, [SP], #0x20
    // 0xc34efc: b               #0xc34d48
    // 0xc34f00: stp             q1, q2, [SP, #-0x20]!
    // 0xc34f04: SaveReg d0
    //     0xc34f04: str             q0, [SP, #-0x10]!
    // 0xc34f08: SaveReg r2
    //     0xc34f08: str             x2, [SP, #-8]!
    // 0xc34f0c: r0 = AllocateDouble()
    //     0xc34f0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34f10: RestoreReg r2
    //     0xc34f10: ldr             x2, [SP], #8
    // 0xc34f14: RestoreReg d0
    //     0xc34f14: ldr             q0, [SP], #0x10
    // 0xc34f18: ldp             q1, q2, [SP], #0x20
    // 0xc34f1c: b               #0xc34da8
    // 0xc34f20: SaveReg d2
    //     0xc34f20: str             q2, [SP, #-0x10]!
    // 0xc34f24: SaveReg r2
    //     0xc34f24: str             x2, [SP, #-8]!
    // 0xc34f28: r0 = AllocateDouble()
    //     0xc34f28: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc34f2c: RestoreReg r2
    //     0xc34f2c: ldr             x2, [SP], #8
    // 0xc34f30: RestoreReg d2
    //     0xc34f30: ldr             q2, [SP], #0x10
    // 0xc34f34: b               #0xc34e08
  }
  _ *(/* No info */) {
    // ** addr: 0xe6feb0, size: 0x30c
    // 0xe6feb0: EnterFrame
    //     0xe6feb0: stp             fp, lr, [SP, #-0x10]!
    //     0xe6feb4: mov             fp, SP
    // 0xe6feb8: AllocStack(0x38)
    //     0xe6feb8: sub             SP, SP, #0x38
    // 0xe6febc: CheckStackOverflow
    //     0xe6febc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6fec0: cmp             SP, x16
    //     0xe6fec4: b.ls            #0xe7004c
    // 0xe6fec8: LoadField: d1 = r1->field_7
    //     0xe6fec8: ldur            d1, [x1, #7]
    // 0xe6fecc: fmul            d2, d1, d0
    // 0xe6fed0: LoadField: d1 = r1->field_f
    //     0xe6fed0: ldur            d1, [x1, #0xf]
    // 0xe6fed4: fmul            d3, d1, d0
    // 0xe6fed8: LoadField: d1 = r1->field_1f
    //     0xe6fed8: ldur            d1, [x1, #0x1f]
    // 0xe6fedc: fmul            d4, d1, d0
    // 0xe6fee0: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xe6fee0: ldur            d1, [x1, #0x17]
    // 0xe6fee4: fmul            d5, d1, d0
    // 0xe6fee8: LoadField: d1 = r1->field_27
    //     0xe6fee8: ldur            d1, [x1, #0x27]
    // 0xe6feec: fmul            d6, d1, d0
    // 0xe6fef0: LoadField: d1 = r1->field_2f
    //     0xe6fef0: ldur            d1, [x1, #0x2f]
    // 0xe6fef4: fmul            d7, d1, d0
    // 0xe6fef8: LoadField: d1 = r1->field_37
    //     0xe6fef8: ldur            d1, [x1, #0x37]
    // 0xe6fefc: fmul            d8, d1, d0
    // 0xe6ff00: LoadField: d1 = r1->field_3f
    //     0xe6ff00: ldur            d1, [x1, #0x3f]
    // 0xe6ff04: fmul            d9, d1, d0
    // 0xe6ff08: r0 = inline_Allocate_Double()
    //     0xe6ff08: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe6ff0c: add             x0, x0, #0x10
    //     0xe6ff10: cmp             x2, x0
    //     0xe6ff14: b.ls            #0xe70054
    //     0xe6ff18: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6ff1c: sub             x0, x0, #0xf
    //     0xe6ff20: movz            x2, #0xe15c
    //     0xe6ff24: movk            x2, #0x3, lsl #16
    //     0xe6ff28: stur            x2, [x0, #-1]
    // 0xe6ff2c: StoreField: r0->field_7 = d2
    //     0xe6ff2c: stur            d2, [x0, #7]
    // 0xe6ff30: r2 = inline_Allocate_Double()
    //     0xe6ff30: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe6ff34: add             x2, x2, #0x10
    //     0xe6ff38: cmp             x3, x2
    //     0xe6ff3c: b.ls            #0xe70084
    //     0xe6ff40: str             x2, [THR, #0x50]  ; THR::top
    //     0xe6ff44: sub             x2, x2, #0xf
    //     0xe6ff48: movz            x3, #0xe15c
    //     0xe6ff4c: movk            x3, #0x3, lsl #16
    //     0xe6ff50: stur            x3, [x2, #-1]
    // 0xe6ff54: StoreField: r2->field_7 = d3
    //     0xe6ff54: stur            d3, [x2, #7]
    // 0xe6ff58: r3 = inline_Allocate_Double()
    //     0xe6ff58: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xe6ff5c: add             x3, x3, #0x10
    //     0xe6ff60: cmp             x4, x3
    //     0xe6ff64: b.ls            #0xe700b8
    //     0xe6ff68: str             x3, [THR, #0x50]  ; THR::top
    //     0xe6ff6c: sub             x3, x3, #0xf
    //     0xe6ff70: movz            x4, #0xe15c
    //     0xe6ff74: movk            x4, #0x3, lsl #16
    //     0xe6ff78: stur            x4, [x3, #-1]
    // 0xe6ff7c: StoreField: r3->field_7 = d4
    //     0xe6ff7c: stur            d4, [x3, #7]
    // 0xe6ff80: r4 = inline_Allocate_Double()
    //     0xe6ff80: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0xe6ff84: add             x4, x4, #0x10
    //     0xe6ff88: cmp             x5, x4
    //     0xe6ff8c: b.ls            #0xe700ec
    //     0xe6ff90: str             x4, [THR, #0x50]  ; THR::top
    //     0xe6ff94: sub             x4, x4, #0xf
    //     0xe6ff98: movz            x5, #0xe15c
    //     0xe6ff9c: movk            x5, #0x3, lsl #16
    //     0xe6ffa0: stur            x5, [x4, #-1]
    // 0xe6ffa4: StoreField: r4->field_7 = d5
    //     0xe6ffa4: stur            d5, [x4, #7]
    // 0xe6ffa8: r5 = inline_Allocate_Double()
    //     0xe6ffa8: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xe6ffac: add             x5, x5, #0x10
    //     0xe6ffb0: cmp             x6, x5
    //     0xe6ffb4: b.ls            #0xe70120
    //     0xe6ffb8: str             x5, [THR, #0x50]  ; THR::top
    //     0xe6ffbc: sub             x5, x5, #0xf
    //     0xe6ffc0: movz            x6, #0xe15c
    //     0xe6ffc4: movk            x6, #0x3, lsl #16
    //     0xe6ffc8: stur            x6, [x5, #-1]
    // 0xe6ffcc: StoreField: r5->field_7 = d6
    //     0xe6ffcc: stur            d6, [x5, #7]
    // 0xe6ffd0: r6 = inline_Allocate_Double()
    //     0xe6ffd0: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xe6ffd4: add             x6, x6, #0x10
    //     0xe6ffd8: cmp             x7, x6
    //     0xe6ffdc: b.ls            #0xe70154
    //     0xe6ffe0: str             x6, [THR, #0x50]  ; THR::top
    //     0xe6ffe4: sub             x6, x6, #0xf
    //     0xe6ffe8: movz            x7, #0xe15c
    //     0xe6ffec: movk            x7, #0x3, lsl #16
    //     0xe6fff0: stur            x7, [x6, #-1]
    // 0xe6fff4: StoreField: r6->field_7 = d7
    //     0xe6fff4: stur            d7, [x6, #7]
    // 0xe6fff8: r7 = inline_Allocate_Double()
    //     0xe6fff8: ldp             x7, x8, [THR, #0x50]  ; THR::top
    //     0xe6fffc: add             x7, x7, #0x10
    //     0xe70000: cmp             x8, x7
    //     0xe70004: b.ls            #0xe70188
    //     0xe70008: str             x7, [THR, #0x50]  ; THR::top
    //     0xe7000c: sub             x7, x7, #0xf
    //     0xe70010: movz            x8, #0xe15c
    //     0xe70014: movk            x8, #0x3, lsl #16
    //     0xe70018: stur            x8, [x7, #-1]
    // 0xe7001c: StoreField: r7->field_7 = d9
    //     0xe7001c: stur            d9, [x7, #7]
    // 0xe70020: stp             x2, x0, [SP, #0x28]
    // 0xe70024: stp             x4, x3, [SP, #0x18]
    // 0xe70028: stp             x6, x5, [SP, #8]
    // 0xe7002c: str             x7, [SP]
    // 0xe70030: mov             v0.16b, v8.16b
    // 0xe70034: r4 = const [0, 0x9, 0x7, 0x2, ascent, 0x6, bottom, 0x5, descent, 0x7, left, 0x2, leftBearing, 0x8, right, 0x4, top, 0x3, null]
    //     0xe70034: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e130] List(19) [0, 0x9, 0x7, 0x2, "ascent", 0x6, "bottom", 0x5, "descent", 0x7, "left", 0x2, "leftBearing", 0x8, "right", 0x4, "top", 0x3, Null]
    //     0xe70038: ldr             x4, [x4, #0x130]
    // 0xe7003c: r0 = copyWith()
    //     0xe7003c: bl              #0x7b6ea4  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::copyWith
    // 0xe70040: LeaveFrame
    //     0xe70040: mov             SP, fp
    //     0xe70044: ldp             fp, lr, [SP], #0x10
    // 0xe70048: ret
    //     0xe70048: ret             
    // 0xe7004c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe7004c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe70050: b               #0xe6fec8
    // 0xe70054: stp             q8, q9, [SP, #-0x20]!
    // 0xe70058: stp             q6, q7, [SP, #-0x20]!
    // 0xe7005c: stp             q4, q5, [SP, #-0x20]!
    // 0xe70060: stp             q2, q3, [SP, #-0x20]!
    // 0xe70064: SaveReg r1
    //     0xe70064: str             x1, [SP, #-8]!
    // 0xe70068: r0 = AllocateDouble()
    //     0xe70068: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7006c: RestoreReg r1
    //     0xe7006c: ldr             x1, [SP], #8
    // 0xe70070: ldp             q2, q3, [SP], #0x20
    // 0xe70074: ldp             q4, q5, [SP], #0x20
    // 0xe70078: ldp             q6, q7, [SP], #0x20
    // 0xe7007c: ldp             q8, q9, [SP], #0x20
    // 0xe70080: b               #0xe6ff2c
    // 0xe70084: stp             q8, q9, [SP, #-0x20]!
    // 0xe70088: stp             q6, q7, [SP, #-0x20]!
    // 0xe7008c: stp             q4, q5, [SP, #-0x20]!
    // 0xe70090: SaveReg d3
    //     0xe70090: str             q3, [SP, #-0x10]!
    // 0xe70094: stp             x0, x1, [SP, #-0x10]!
    // 0xe70098: r0 = AllocateDouble()
    //     0xe70098: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7009c: mov             x2, x0
    // 0xe700a0: ldp             x0, x1, [SP], #0x10
    // 0xe700a4: RestoreReg d3
    //     0xe700a4: ldr             q3, [SP], #0x10
    // 0xe700a8: ldp             q4, q5, [SP], #0x20
    // 0xe700ac: ldp             q6, q7, [SP], #0x20
    // 0xe700b0: ldp             q8, q9, [SP], #0x20
    // 0xe700b4: b               #0xe6ff54
    // 0xe700b8: stp             q8, q9, [SP, #-0x20]!
    // 0xe700bc: stp             q6, q7, [SP, #-0x20]!
    // 0xe700c0: stp             q4, q5, [SP, #-0x20]!
    // 0xe700c4: stp             x1, x2, [SP, #-0x10]!
    // 0xe700c8: SaveReg r0
    //     0xe700c8: str             x0, [SP, #-8]!
    // 0xe700cc: r0 = AllocateDouble()
    //     0xe700cc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe700d0: mov             x3, x0
    // 0xe700d4: RestoreReg r0
    //     0xe700d4: ldr             x0, [SP], #8
    // 0xe700d8: ldp             x1, x2, [SP], #0x10
    // 0xe700dc: ldp             q4, q5, [SP], #0x20
    // 0xe700e0: ldp             q6, q7, [SP], #0x20
    // 0xe700e4: ldp             q8, q9, [SP], #0x20
    // 0xe700e8: b               #0xe6ff7c
    // 0xe700ec: stp             q8, q9, [SP, #-0x20]!
    // 0xe700f0: stp             q6, q7, [SP, #-0x20]!
    // 0xe700f4: SaveReg d5
    //     0xe700f4: str             q5, [SP, #-0x10]!
    // 0xe700f8: stp             x2, x3, [SP, #-0x10]!
    // 0xe700fc: stp             x0, x1, [SP, #-0x10]!
    // 0xe70100: r0 = AllocateDouble()
    //     0xe70100: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70104: mov             x4, x0
    // 0xe70108: ldp             x0, x1, [SP], #0x10
    // 0xe7010c: ldp             x2, x3, [SP], #0x10
    // 0xe70110: RestoreReg d5
    //     0xe70110: ldr             q5, [SP], #0x10
    // 0xe70114: ldp             q6, q7, [SP], #0x20
    // 0xe70118: ldp             q8, q9, [SP], #0x20
    // 0xe7011c: b               #0xe6ffa4
    // 0xe70120: stp             q8, q9, [SP, #-0x20]!
    // 0xe70124: stp             q6, q7, [SP, #-0x20]!
    // 0xe70128: stp             x3, x4, [SP, #-0x10]!
    // 0xe7012c: stp             x1, x2, [SP, #-0x10]!
    // 0xe70130: SaveReg r0
    //     0xe70130: str             x0, [SP, #-8]!
    // 0xe70134: r0 = AllocateDouble()
    //     0xe70134: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70138: mov             x5, x0
    // 0xe7013c: RestoreReg r0
    //     0xe7013c: ldr             x0, [SP], #8
    // 0xe70140: ldp             x1, x2, [SP], #0x10
    // 0xe70144: ldp             x3, x4, [SP], #0x10
    // 0xe70148: ldp             q6, q7, [SP], #0x20
    // 0xe7014c: ldp             q8, q9, [SP], #0x20
    // 0xe70150: b               #0xe6ffcc
    // 0xe70154: stp             q8, q9, [SP, #-0x20]!
    // 0xe70158: SaveReg d7
    //     0xe70158: str             q7, [SP, #-0x10]!
    // 0xe7015c: stp             x4, x5, [SP, #-0x10]!
    // 0xe70160: stp             x2, x3, [SP, #-0x10]!
    // 0xe70164: stp             x0, x1, [SP, #-0x10]!
    // 0xe70168: r0 = AllocateDouble()
    //     0xe70168: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7016c: mov             x6, x0
    // 0xe70170: ldp             x0, x1, [SP], #0x10
    // 0xe70174: ldp             x2, x3, [SP], #0x10
    // 0xe70178: ldp             x4, x5, [SP], #0x10
    // 0xe7017c: RestoreReg d7
    //     0xe7017c: ldr             q7, [SP], #0x10
    // 0xe70180: ldp             q8, q9, [SP], #0x20
    // 0xe70184: b               #0xe6fff4
    // 0xe70188: stp             q8, q9, [SP, #-0x20]!
    // 0xe7018c: stp             x5, x6, [SP, #-0x10]!
    // 0xe70190: stp             x3, x4, [SP, #-0x10]!
    // 0xe70194: stp             x1, x2, [SP, #-0x10]!
    // 0xe70198: SaveReg r0
    //     0xe70198: str             x0, [SP, #-8]!
    // 0xe7019c: r0 = AllocateDouble()
    //     0xe7019c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe701a0: mov             x7, x0
    // 0xe701a4: RestoreReg r0
    //     0xe701a4: ldr             x0, [SP], #8
    // 0xe701a8: ldp             x1, x2, [SP], #0x10
    // 0xe701ac: ldp             x3, x4, [SP], #0x10
    // 0xe701b0: ldp             x5, x6, [SP], #0x10
    // 0xe701b4: ldp             q8, q9, [SP], #0x20
    // 0xe701b8: b               #0xe7001c
  }
  factory _ PdfFontMetrics.append(/* No info */) {
    // ** addr: 0xe701bc, size: 0x7c0
    // 0xe701bc: EnterFrame
    //     0xe701bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe701c0: mov             fp, SP
    // 0xe701c4: AllocStack(0x78)
    //     0xe701c4: sub             SP, SP, #0x78
    // 0xe701c8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x50 */)
    //     0xe701c8: stur            x2, [fp, #-8]
    //     0xe701cc: stur            d0, [fp, #-0x50]
    // 0xe701d0: CheckStackOverflow
    //     0xe701d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe701d4: cmp             SP, x16
    //     0xe701d8: b.ls            #0xe707a0
    // 0xe701dc: r0 = LoadClassIdInstr(r2)
    //     0xe701dc: ldur            x0, [x2, #-1]
    //     0xe701e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe701e4: mov             x1, x2
    // 0xe701e8: r0 = GDT[cid_x0 + 0xe879]()
    //     0xe701e8: movz            x17, #0xe879
    //     0xe701ec: add             lr, x0, x17
    //     0xe701f0: ldr             lr, [x21, lr, lsl #3]
    //     0xe701f4: blr             lr
    // 0xe701f8: tbnz            w0, #4, #0xe70210
    // 0xe701fc: r0 = Instance_PdfFontMetrics
    //     0xe701fc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0xe70200: ldr             x0, [x0, #0x120]
    // 0xe70204: LeaveFrame
    //     0xe70204: mov             SP, fp
    //     0xe70208: ldp             fp, lr, [SP], #0x10
    // 0xe7020c: ret
    //     0xe7020c: ret             
    // 0xe70210: ldur            x1, [fp, #-8]
    // 0xe70214: r0 = LoadClassIdInstr(r1)
    //     0xe70214: ldur            x0, [x1, #-1]
    //     0xe70218: ubfx            x0, x0, #0xc, #0x14
    // 0xe7021c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xe7021c: movz            x17, #0xd35d
    //     0xe70220: add             lr, x0, x17
    //     0xe70224: ldr             lr, [x21, lr, lsl #3]
    //     0xe70228: blr             lr
    // 0xe7022c: mov             x2, x0
    // 0xe70230: stur            x2, [fp, #-0x48]
    // 0xe70234: r10 = Null
    //     0xe70234: mov             x10, NULL
    // 0xe70238: r9 = Null
    //     0xe70238: mov             x9, NULL
    // 0xe7023c: d0 = 0.000000
    //     0xe7023c: eor             v0.16b, v0.16b, v0.16b
    // 0xe70240: r8 = Null
    //     0xe70240: mov             x8, NULL
    // 0xe70244: r7 = Null
    //     0xe70244: mov             x7, NULL
    // 0xe70248: r6 = Null
    //     0xe70248: mov             x6, NULL
    // 0xe7024c: r5 = Sentinel
    //     0xe7024c: ldr             x5, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe70250: r4 = Null
    //     0xe70250: mov             x4, NULL
    // 0xe70254: r3 = Sentinel
    //     0xe70254: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe70258: stur            x10, [fp, #-8]
    // 0xe7025c: stur            x9, [fp, #-0x10]
    // 0xe70260: stur            x8, [fp, #-0x18]
    // 0xe70264: stur            x7, [fp, #-0x20]
    // 0xe70268: stur            x6, [fp, #-0x28]
    // 0xe7026c: stur            x5, [fp, #-0x30]
    // 0xe70270: stur            x4, [fp, #-0x38]
    // 0xe70274: stur            x3, [fp, #-0x40]
    // 0xe70278: stur            d0, [fp, #-0x58]
    // 0xe7027c: CheckStackOverflow
    //     0xe7027c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe70280: cmp             SP, x16
    //     0xe70284: b.ls            #0xe707a8
    // 0xe70288: r0 = LoadClassIdInstr(r2)
    //     0xe70288: ldur            x0, [x2, #-1]
    //     0xe7028c: ubfx            x0, x0, #0xc, #0x14
    // 0xe70290: mov             x1, x2
    // 0xe70294: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe70294: movz            x17, #0x292d
    //     0xe70298: movk            x17, #0x1, lsl #16
    //     0xe7029c: add             lr, x0, x17
    //     0xe702a0: ldr             lr, [x21, lr, lsl #3]
    //     0xe702a4: blr             lr
    // 0xe702a8: tbnz            w0, #4, #0xe7062c
    // 0xe702ac: ldur            x2, [fp, #-0x48]
    // 0xe702b0: ldur            x3, [fp, #-0x38]
    // 0xe702b4: r0 = LoadClassIdInstr(r2)
    //     0xe702b4: ldur            x0, [x2, #-1]
    //     0xe702b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe702bc: mov             x1, x2
    // 0xe702c0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe702c0: movz            x17, #0x384d
    //     0xe702c4: movk            x17, #0x1, lsl #16
    //     0xe702c8: add             lr, x0, x17
    //     0xe702cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe702d0: blr             lr
    // 0xe702d4: mov             x1, x0
    // 0xe702d8: ldur            x0, [fp, #-0x38]
    // 0xe702dc: cmp             w0, NULL
    // 0xe702e0: b.ne            #0xe702f0
    // 0xe702e4: LoadField: d1 = r1->field_3f
    //     0xe702e4: ldur            d1, [x1, #0x3f]
    // 0xe702e8: mov             v0.16b, v1.16b
    // 0xe702ec: b               #0xe702f8
    // 0xe702f0: LoadField: d1 = r0->field_7
    //     0xe702f0: ldur            d1, [x0, #7]
    // 0xe702f4: mov             v0.16b, v1.16b
    // 0xe702f8: ldur            x2, [fp, #-8]
    // 0xe702fc: cmp             w2, NULL
    // 0xe70300: b.ne            #0xe70310
    // 0xe70304: LoadField: d1 = r1->field_7
    //     0xe70304: ldur            d1, [x1, #7]
    // 0xe70308: mov             v2.16b, v1.16b
    // 0xe7030c: b               #0xe70318
    // 0xe70310: LoadField: d1 = r2->field_7
    //     0xe70310: ldur            d1, [x2, #7]
    // 0xe70314: mov             v2.16b, v1.16b
    // 0xe70318: d1 = 0.000000
    //     0xe70318: eor             v1.16b, v1.16b, v1.16b
    // 0xe7031c: LoadField: d3 = r1->field_37
    //     0xe7031c: ldur            d3, [x1, #0x37]
    // 0xe70320: fcmp            d3, d1
    // 0xe70324: b.le            #0xe70330
    // 0xe70328: ldur            d5, [fp, #-0x50]
    // 0xe7032c: b               #0xe70334
    // 0xe70330: d5 = 0.000000
    //     0xe70330: eor             v5.16b, v5.16b, v5.16b
    // 0xe70334: ldur            x3, [fp, #-0x10]
    // 0xe70338: ldur            d4, [fp, #-0x58]
    // 0xe7033c: fadd            d6, d3, d5
    // 0xe70340: fadd            d7, d4, d6
    // 0xe70344: LoadField: d4 = r1->field_1f
    //     0xe70344: ldur            d4, [x1, #0x1f]
    // 0xe70348: fsub            d6, d3, d4
    // 0xe7034c: cmp             w3, NULL
    // 0xe70350: b.ne            #0xe7035c
    // 0xe70354: LoadField: d3 = r1->field_f
    //     0xe70354: ldur            d3, [x1, #0xf]
    // 0xe70358: b               #0xe70360
    // 0xe7035c: LoadField: d3 = r3->field_7
    //     0xe7035c: ldur            d3, [x3, #7]
    // 0xe70360: LoadField: d4 = r1->field_f
    //     0xe70360: ldur            d4, [x1, #0xf]
    // 0xe70364: fcmp            d3, d4
    // 0xe70368: b.le            #0xe70374
    // 0xe7036c: mov             v3.16b, v4.16b
    // 0xe70370: b               #0xe703c0
    // 0xe70374: fcmp            d4, d3
    // 0xe70378: b.gt            #0xe703c0
    // 0xe7037c: fcmp            d3, d1
    // 0xe70380: b.ne            #0xe70394
    // 0xe70384: fadd            d8, d3, d4
    // 0xe70388: fmul            d9, d8, d3
    // 0xe7038c: fmul            d3, d9, d4
    // 0xe70390: b               #0xe703c0
    // 0xe70394: fcmp            d3, d1
    // 0xe70398: b.ne            #0xe703b4
    // 0xe7039c: fcmp            d4, #0.0
    // 0xe703a0: b.vs            #0xe703b4
    // 0xe703a4: b.ne            #0xe703b0
    // 0xe703a8: r0 = 0.000000
    //     0xe703a8: fmov            x0, d4
    // 0xe703ac: cmp             x0, #0
    // 0xe703b0: b.lt            #0xe703bc
    // 0xe703b4: fcmp            d4, d4
    // 0xe703b8: b.vc            #0xe703c0
    // 0xe703bc: mov             v3.16b, v4.16b
    // 0xe703c0: ldur            x4, [fp, #-0x18]
    // 0xe703c4: cmp             w4, NULL
    // 0xe703c8: b.ne            #0xe703d4
    // 0xe703cc: ArrayLoad: d4 = r1[0]  ; List_8
    //     0xe703cc: ldur            d4, [x1, #0x17]
    // 0xe703d0: b               #0xe703d8
    // 0xe703d4: LoadField: d4 = r4->field_7
    //     0xe703d4: ldur            d4, [x4, #7]
    // 0xe703d8: ArrayLoad: d8 = r1[0]  ; List_8
    //     0xe703d8: ldur            d8, [x1, #0x17]
    // 0xe703dc: fcmp            d4, d8
    // 0xe703e0: b.gt            #0xe70414
    // 0xe703e4: fcmp            d8, d4
    // 0xe703e8: b.le            #0xe703f4
    // 0xe703ec: mov             v4.16b, v8.16b
    // 0xe703f0: b               #0xe70414
    // 0xe703f4: fcmp            d4, d1
    // 0xe703f8: b.ne            #0xe70408
    // 0xe703fc: fadd            d9, d4, d8
    // 0xe70400: mov             v4.16b, v9.16b
    // 0xe70404: b               #0xe70414
    // 0xe70408: fcmp            d8, d8
    // 0xe7040c: b.vc            #0xe70414
    // 0xe70410: mov             v4.16b, v8.16b
    // 0xe70414: ldur            x5, [fp, #-0x28]
    // 0xe70418: cmp             w5, NULL
    // 0xe7041c: b.ne            #0xe70428
    // 0xe70420: LoadField: d8 = r1->field_2f
    //     0xe70420: ldur            d8, [x1, #0x2f]
    // 0xe70424: b               #0xe7042c
    // 0xe70428: LoadField: d8 = r5->field_7
    //     0xe70428: ldur            d8, [x5, #7]
    // 0xe7042c: LoadField: d9 = r1->field_2f
    //     0xe7042c: ldur            d9, [x1, #0x2f]
    // 0xe70430: fcmp            d8, d9
    // 0xe70434: b.le            #0xe70440
    // 0xe70438: mov             v8.16b, v9.16b
    // 0xe7043c: b               #0xe7048c
    // 0xe70440: fcmp            d9, d8
    // 0xe70444: b.gt            #0xe7048c
    // 0xe70448: fcmp            d8, d1
    // 0xe7044c: b.ne            #0xe70460
    // 0xe70450: fadd            d10, d8, d9
    // 0xe70454: fmul            d11, d10, d8
    // 0xe70458: fmul            d8, d11, d9
    // 0xe7045c: b               #0xe7048c
    // 0xe70460: fcmp            d8, d1
    // 0xe70464: b.ne            #0xe70480
    // 0xe70468: fcmp            d9, #0.0
    // 0xe7046c: b.vs            #0xe70480
    // 0xe70470: b.ne            #0xe7047c
    // 0xe70474: r0 = 0.000000
    //     0xe70474: fmov            x0, d9
    // 0xe70478: cmp             x0, #0
    // 0xe7047c: b.lt            #0xe70488
    // 0xe70480: fcmp            d9, d9
    // 0xe70484: b.vc            #0xe7048c
    // 0xe70488: mov             v8.16b, v9.16b
    // 0xe7048c: ldur            x6, [fp, #-0x20]
    // 0xe70490: cmp             w6, NULL
    // 0xe70494: b.ne            #0xe704a0
    // 0xe70498: LoadField: d9 = r1->field_27
    //     0xe70498: ldur            d9, [x1, #0x27]
    // 0xe7049c: b               #0xe704a4
    // 0xe704a0: LoadField: d9 = r6->field_7
    //     0xe704a0: ldur            d9, [x6, #7]
    // 0xe704a4: LoadField: d10 = r1->field_27
    //     0xe704a4: ldur            d10, [x1, #0x27]
    // 0xe704a8: fcmp            d9, d10
    // 0xe704ac: b.gt            #0xe704e0
    // 0xe704b0: fcmp            d10, d9
    // 0xe704b4: b.le            #0xe704c0
    // 0xe704b8: mov             v9.16b, v10.16b
    // 0xe704bc: b               #0xe704e0
    // 0xe704c0: fcmp            d9, d1
    // 0xe704c4: b.ne            #0xe704d4
    // 0xe704c8: fadd            d11, d9, d10
    // 0xe704cc: mov             v9.16b, v11.16b
    // 0xe704d0: b               #0xe704e0
    // 0xe704d4: fcmp            d10, d10
    // 0xe704d8: b.vc            #0xe704e0
    // 0xe704dc: mov             v9.16b, v10.16b
    // 0xe704e0: r4 = inline_Allocate_Double()
    //     0xe704e0: ldp             x4, x0, [THR, #0x50]  ; THR::top
    //     0xe704e4: add             x4, x4, #0x10
    //     0xe704e8: cmp             x0, x4
    //     0xe704ec: b.ls            #0xe707b0
    //     0xe704f0: str             x4, [THR, #0x50]  ; THR::top
    //     0xe704f4: sub             x4, x4, #0xf
    //     0xe704f8: movz            x0, #0xe15c
    //     0xe704fc: movk            x0, #0x3, lsl #16
    //     0xe70500: stur            x0, [x4, #-1]
    // 0xe70504: StoreField: r4->field_7 = d0
    //     0xe70504: stur            d0, [x4, #7]
    // 0xe70508: r10 = inline_Allocate_Double()
    //     0xe70508: ldp             x10, x0, [THR, #0x50]  ; THR::top
    //     0xe7050c: add             x10, x10, #0x10
    //     0xe70510: cmp             x0, x10
    //     0xe70514: b.ls            #0xe707e4
    //     0xe70518: str             x10, [THR, #0x50]  ; THR::top
    //     0xe7051c: sub             x10, x10, #0xf
    //     0xe70520: movz            x0, #0xe15c
    //     0xe70524: movk            x0, #0x3, lsl #16
    //     0xe70528: stur            x0, [x10, #-1]
    // 0xe7052c: StoreField: r10->field_7 = d2
    //     0xe7052c: stur            d2, [x10, #7]
    // 0xe70530: r3 = inline_Allocate_Double()
    //     0xe70530: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe70534: add             x3, x3, #0x10
    //     0xe70538: cmp             x0, x3
    //     0xe7053c: b.ls            #0xe70820
    //     0xe70540: str             x3, [THR, #0x50]  ; THR::top
    //     0xe70544: sub             x3, x3, #0xf
    //     0xe70548: movz            x0, #0xe15c
    //     0xe7054c: movk            x0, #0x3, lsl #16
    //     0xe70550: stur            x0, [x3, #-1]
    // 0xe70554: StoreField: r3->field_7 = d5
    //     0xe70554: stur            d5, [x3, #7]
    // 0xe70558: r5 = inline_Allocate_Double()
    //     0xe70558: ldp             x5, x0, [THR, #0x50]  ; THR::top
    //     0xe7055c: add             x5, x5, #0x10
    //     0xe70560: cmp             x0, x5
    //     0xe70564: b.ls            #0xe70854
    //     0xe70568: str             x5, [THR, #0x50]  ; THR::top
    //     0xe7056c: sub             x5, x5, #0xf
    //     0xe70570: movz            x0, #0xe15c
    //     0xe70574: movk            x0, #0x3, lsl #16
    //     0xe70578: stur            x0, [x5, #-1]
    // 0xe7057c: StoreField: r5->field_7 = d6
    //     0xe7057c: stur            d6, [x5, #7]
    // 0xe70580: r9 = inline_Allocate_Double()
    //     0xe70580: ldp             x9, x0, [THR, #0x50]  ; THR::top
    //     0xe70584: add             x9, x9, #0x10
    //     0xe70588: cmp             x0, x9
    //     0xe7058c: b.ls            #0xe70890
    //     0xe70590: str             x9, [THR, #0x50]  ; THR::top
    //     0xe70594: sub             x9, x9, #0xf
    //     0xe70598: movz            x0, #0xe15c
    //     0xe7059c: movk            x0, #0x3, lsl #16
    //     0xe705a0: stur            x0, [x9, #-1]
    // 0xe705a4: StoreField: r9->field_7 = d3
    //     0xe705a4: stur            d3, [x9, #7]
    // 0xe705a8: r8 = inline_Allocate_Double()
    //     0xe705a8: ldp             x8, x0, [THR, #0x50]  ; THR::top
    //     0xe705ac: add             x8, x8, #0x10
    //     0xe705b0: cmp             x0, x8
    //     0xe705b4: b.ls            #0xe708c4
    //     0xe705b8: str             x8, [THR, #0x50]  ; THR::top
    //     0xe705bc: sub             x8, x8, #0xf
    //     0xe705c0: movz            x0, #0xe15c
    //     0xe705c4: movk            x0, #0x3, lsl #16
    //     0xe705c8: stur            x0, [x8, #-1]
    // 0xe705cc: StoreField: r8->field_7 = d4
    //     0xe705cc: stur            d4, [x8, #7]
    // 0xe705d0: r6 = inline_Allocate_Double()
    //     0xe705d0: ldp             x6, x0, [THR, #0x50]  ; THR::top
    //     0xe705d4: add             x6, x6, #0x10
    //     0xe705d8: cmp             x0, x6
    //     0xe705dc: b.ls            #0xe70900
    //     0xe705e0: str             x6, [THR, #0x50]  ; THR::top
    //     0xe705e4: sub             x6, x6, #0xf
    //     0xe705e8: movz            x0, #0xe15c
    //     0xe705ec: movk            x0, #0x3, lsl #16
    //     0xe705f0: stur            x0, [x6, #-1]
    // 0xe705f4: StoreField: r6->field_7 = d8
    //     0xe705f4: stur            d8, [x6, #7]
    // 0xe705f8: r7 = inline_Allocate_Double()
    //     0xe705f8: ldp             x7, x0, [THR, #0x50]  ; THR::top
    //     0xe705fc: add             x7, x7, #0x10
    //     0xe70600: cmp             x0, x7
    //     0xe70604: b.ls            #0xe70934
    //     0xe70608: str             x7, [THR, #0x50]  ; THR::top
    //     0xe7060c: sub             x7, x7, #0xf
    //     0xe70610: movz            x0, #0xe15c
    //     0xe70614: movk            x0, #0x3, lsl #16
    //     0xe70618: stur            x0, [x7, #-1]
    // 0xe7061c: StoreField: r7->field_7 = d9
    //     0xe7061c: stur            d9, [x7, #7]
    // 0xe70620: mov             v0.16b, v7.16b
    // 0xe70624: ldur            x2, [fp, #-0x48]
    // 0xe70628: b               #0xe70258
    // 0xe7062c: ldur            x2, [fp, #-8]
    // 0xe70630: ldur            x3, [fp, #-0x10]
    // 0xe70634: ldur            d4, [fp, #-0x58]
    // 0xe70638: ldur            x4, [fp, #-0x18]
    // 0xe7063c: ldur            x6, [fp, #-0x20]
    // 0xe70640: ldur            x5, [fp, #-0x28]
    // 0xe70644: ldur            x1, [fp, #-0x30]
    // 0xe70648: ldur            x0, [fp, #-0x38]
    // 0xe7064c: cmp             w2, NULL
    // 0xe70650: b.eq            #0xe70970
    // 0xe70654: cmp             w3, NULL
    // 0xe70658: b.eq            #0xe70974
    // 0xe7065c: r16 = Sentinel
    //     0xe7065c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe70660: cmp             w1, w16
    // 0xe70664: b.ne            #0xe70678
    // 0xe70668: r16 = "lastBearing"
    //     0xe70668: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e4c8] "lastBearing"
    //     0xe7066c: ldr             x16, [x16, #0x4c8]
    // 0xe70670: str             x16, [SP]
    // 0xe70674: r0 = _throwLocalNotInitialized()
    //     0xe70674: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe70678: ldur            d0, [fp, #-0x58]
    // 0xe7067c: ldur            x0, [fp, #-0x30]
    // 0xe70680: ldur            x1, [fp, #-0x40]
    // 0xe70684: LoadField: d1 = r0->field_7
    //     0xe70684: ldur            d1, [x0, #7]
    // 0xe70688: fsub            d2, d0, d1
    // 0xe7068c: stur            d2, [fp, #-0x50]
    // 0xe70690: r16 = Sentinel
    //     0xe70690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe70694: cmp             w1, w16
    // 0xe70698: b.ne            #0xe706ac
    // 0xe7069c: r16 = "spacing"
    //     0xe7069c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e4d0] "spacing"
    //     0xe706a0: ldr             x16, [x16, #0x4d0]
    // 0xe706a4: str             x16, [SP]
    // 0xe706a8: r0 = _throwLocalNotInitialized()
    //     0xe706a8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe706ac: ldur            x1, [fp, #-0x18]
    // 0xe706b0: ldur            x0, [fp, #-0x40]
    // 0xe706b4: ldur            d0, [fp, #-0x50]
    // 0xe706b8: LoadField: d1 = r0->field_7
    //     0xe706b8: ldur            d1, [x0, #7]
    // 0xe706bc: stur            d1, [fp, #-0x68]
    // 0xe706c0: fsub            d2, d0, d1
    // 0xe706c4: stur            d2, [fp, #-0x60]
    // 0xe706c8: cmp             w1, NULL
    // 0xe706cc: b.eq            #0xe70978
    // 0xe706d0: r16 = Sentinel
    //     0xe706d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe706d4: cmp             w0, w16
    // 0xe706d8: b.ne            #0xe706ec
    // 0xe706dc: r16 = "spacing"
    //     0xe706dc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e4d0] "spacing"
    //     0xe706e0: ldr             x16, [x16, #0x4d0]
    // 0xe706e4: str             x16, [SP]
    // 0xe706e8: r0 = _throwLocalNotInitialized()
    //     0xe706e8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe706ec: ldur            x1, [fp, #-8]
    // 0xe706f0: ldur            x2, [fp, #-0x10]
    // 0xe706f4: ldur            d2, [fp, #-0x58]
    // 0xe706f8: ldur            x0, [fp, #-0x18]
    // 0xe706fc: ldur            x3, [fp, #-0x20]
    // 0xe70700: ldur            d1, [fp, #-0x60]
    // 0xe70704: ldur            d0, [fp, #-0x68]
    // 0xe70708: fsub            d3, d2, d0
    // 0xe7070c: stur            d3, [fp, #-0x70]
    // 0xe70710: LoadField: d0 = r1->field_7
    //     0xe70710: ldur            d0, [x1, #7]
    // 0xe70714: stur            d0, [fp, #-0x50]
    // 0xe70718: r0 = PdfFontMetrics()
    //     0xe70718: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0xe7071c: ldur            d0, [fp, #-0x50]
    // 0xe70720: StoreField: r0->field_7 = d0
    //     0xe70720: stur            d0, [x0, #7]
    // 0xe70724: ldur            x1, [fp, #-0x10]
    // 0xe70728: LoadField: d1 = r1->field_7
    //     0xe70728: ldur            d1, [x1, #7]
    // 0xe7072c: StoreField: r0->field_f = d1
    //     0xe7072c: stur            d1, [x0, #0xf]
    // 0xe70730: ldur            d2, [fp, #-0x60]
    // 0xe70734: StoreField: r0->field_1f = d2
    //     0xe70734: stur            d2, [x0, #0x1f]
    // 0xe70738: ldur            x1, [fp, #-0x18]
    // 0xe7073c: LoadField: d2 = r1->field_7
    //     0xe7073c: ldur            d2, [x1, #7]
    // 0xe70740: ArrayStore: r0[0] = d2  ; List_8
    //     0xe70740: stur            d2, [x0, #0x17]
    // 0xe70744: ldur            x1, [fp, #-0x20]
    // 0xe70748: cmp             w1, NULL
    // 0xe7074c: b.eq            #0xe70754
    // 0xe70750: LoadField: d2 = r1->field_7
    //     0xe70750: ldur            d2, [x1, #7]
    // 0xe70754: ldur            x1, [fp, #-0x28]
    // 0xe70758: StoreField: r0->field_27 = d2
    //     0xe70758: stur            d2, [x0, #0x27]
    // 0xe7075c: cmp             w1, NULL
    // 0xe70760: b.ne            #0xe7076c
    // 0xe70764: mov             v2.16b, v1.16b
    // 0xe70768: b               #0xe70774
    // 0xe7076c: LoadField: d1 = r1->field_7
    //     0xe7076c: ldur            d1, [x1, #7]
    // 0xe70770: mov             v2.16b, v1.16b
    // 0xe70774: ldur            x1, [fp, #-0x38]
    // 0xe70778: ldur            d1, [fp, #-0x70]
    // 0xe7077c: StoreField: r0->field_2f = d2
    //     0xe7077c: stur            d2, [x0, #0x2f]
    // 0xe70780: StoreField: r0->field_37 = d1
    //     0xe70780: stur            d1, [x0, #0x37]
    // 0xe70784: cmp             w1, NULL
    // 0xe70788: b.eq            #0xe70790
    // 0xe7078c: LoadField: d0 = r1->field_7
    //     0xe7078c: ldur            d0, [x1, #7]
    // 0xe70790: StoreField: r0->field_3f = d0
    //     0xe70790: stur            d0, [x0, #0x3f]
    // 0xe70794: LeaveFrame
    //     0xe70794: mov             SP, fp
    //     0xe70798: ldp             fp, lr, [SP], #0x10
    // 0xe7079c: ret
    //     0xe7079c: ret             
    // 0xe707a0: r0 = StackOverflowSharedWithFPURegs()
    //     0xe707a0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe707a4: b               #0xe701dc
    // 0xe707a8: r0 = StackOverflowSharedWithFPURegs()
    //     0xe707a8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe707ac: b               #0xe70288
    // 0xe707b0: stp             q8, q9, [SP, #-0x20]!
    // 0xe707b4: stp             q6, q7, [SP, #-0x20]!
    // 0xe707b8: stp             q4, q5, [SP, #-0x20]!
    // 0xe707bc: stp             q2, q3, [SP, #-0x20]!
    // 0xe707c0: stp             q0, q1, [SP, #-0x20]!
    // 0xe707c4: r0 = AllocateDouble()
    //     0xe707c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe707c8: mov             x4, x0
    // 0xe707cc: ldp             q0, q1, [SP], #0x20
    // 0xe707d0: ldp             q2, q3, [SP], #0x20
    // 0xe707d4: ldp             q4, q5, [SP], #0x20
    // 0xe707d8: ldp             q6, q7, [SP], #0x20
    // 0xe707dc: ldp             q8, q9, [SP], #0x20
    // 0xe707e0: b               #0xe70504
    // 0xe707e4: stp             q8, q9, [SP, #-0x20]!
    // 0xe707e8: stp             q6, q7, [SP, #-0x20]!
    // 0xe707ec: stp             q4, q5, [SP, #-0x20]!
    // 0xe707f0: stp             q2, q3, [SP, #-0x20]!
    // 0xe707f4: SaveReg d1
    //     0xe707f4: str             q1, [SP, #-0x10]!
    // 0xe707f8: SaveReg r4
    //     0xe707f8: str             x4, [SP, #-8]!
    // 0xe707fc: r0 = AllocateDouble()
    //     0xe707fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70800: mov             x10, x0
    // 0xe70804: RestoreReg r4
    //     0xe70804: ldr             x4, [SP], #8
    // 0xe70808: RestoreReg d1
    //     0xe70808: ldr             q1, [SP], #0x10
    // 0xe7080c: ldp             q2, q3, [SP], #0x20
    // 0xe70810: ldp             q4, q5, [SP], #0x20
    // 0xe70814: ldp             q6, q7, [SP], #0x20
    // 0xe70818: ldp             q8, q9, [SP], #0x20
    // 0xe7081c: b               #0xe7052c
    // 0xe70820: stp             q8, q9, [SP, #-0x20]!
    // 0xe70824: stp             q6, q7, [SP, #-0x20]!
    // 0xe70828: stp             q4, q5, [SP, #-0x20]!
    // 0xe7082c: stp             q1, q3, [SP, #-0x20]!
    // 0xe70830: stp             x4, x10, [SP, #-0x10]!
    // 0xe70834: r0 = AllocateDouble()
    //     0xe70834: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70838: mov             x3, x0
    // 0xe7083c: ldp             x4, x10, [SP], #0x10
    // 0xe70840: ldp             q1, q3, [SP], #0x20
    // 0xe70844: ldp             q4, q5, [SP], #0x20
    // 0xe70848: ldp             q6, q7, [SP], #0x20
    // 0xe7084c: ldp             q8, q9, [SP], #0x20
    // 0xe70850: b               #0xe70554
    // 0xe70854: stp             q8, q9, [SP, #-0x20]!
    // 0xe70858: stp             q6, q7, [SP, #-0x20]!
    // 0xe7085c: stp             q3, q4, [SP, #-0x20]!
    // 0xe70860: SaveReg d1
    //     0xe70860: str             q1, [SP, #-0x10]!
    // 0xe70864: stp             x4, x10, [SP, #-0x10]!
    // 0xe70868: SaveReg r3
    //     0xe70868: str             x3, [SP, #-8]!
    // 0xe7086c: r0 = AllocateDouble()
    //     0xe7086c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70870: mov             x5, x0
    // 0xe70874: RestoreReg r3
    //     0xe70874: ldr             x3, [SP], #8
    // 0xe70878: ldp             x4, x10, [SP], #0x10
    // 0xe7087c: RestoreReg d1
    //     0xe7087c: ldr             q1, [SP], #0x10
    // 0xe70880: ldp             q3, q4, [SP], #0x20
    // 0xe70884: ldp             q6, q7, [SP], #0x20
    // 0xe70888: ldp             q8, q9, [SP], #0x20
    // 0xe7088c: b               #0xe7057c
    // 0xe70890: stp             q8, q9, [SP, #-0x20]!
    // 0xe70894: stp             q4, q7, [SP, #-0x20]!
    // 0xe70898: stp             q1, q3, [SP, #-0x20]!
    // 0xe7089c: stp             x5, x10, [SP, #-0x10]!
    // 0xe708a0: stp             x3, x4, [SP, #-0x10]!
    // 0xe708a4: r0 = AllocateDouble()
    //     0xe708a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe708a8: mov             x9, x0
    // 0xe708ac: ldp             x3, x4, [SP], #0x10
    // 0xe708b0: ldp             x5, x10, [SP], #0x10
    // 0xe708b4: ldp             q1, q3, [SP], #0x20
    // 0xe708b8: ldp             q4, q7, [SP], #0x20
    // 0xe708bc: ldp             q8, q9, [SP], #0x20
    // 0xe708c0: b               #0xe705a4
    // 0xe708c4: stp             q8, q9, [SP, #-0x20]!
    // 0xe708c8: stp             q4, q7, [SP, #-0x20]!
    // 0xe708cc: SaveReg d1
    //     0xe708cc: str             q1, [SP, #-0x10]!
    // 0xe708d0: stp             x9, x10, [SP, #-0x10]!
    // 0xe708d4: stp             x4, x5, [SP, #-0x10]!
    // 0xe708d8: SaveReg r3
    //     0xe708d8: str             x3, [SP, #-8]!
    // 0xe708dc: r0 = AllocateDouble()
    //     0xe708dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe708e0: mov             x8, x0
    // 0xe708e4: RestoreReg r3
    //     0xe708e4: ldr             x3, [SP], #8
    // 0xe708e8: ldp             x4, x5, [SP], #0x10
    // 0xe708ec: ldp             x9, x10, [SP], #0x10
    // 0xe708f0: RestoreReg d1
    //     0xe708f0: ldr             q1, [SP], #0x10
    // 0xe708f4: ldp             q4, q7, [SP], #0x20
    // 0xe708f8: ldp             q8, q9, [SP], #0x20
    // 0xe708fc: b               #0xe705cc
    // 0xe70900: stp             q8, q9, [SP, #-0x20]!
    // 0xe70904: stp             q1, q7, [SP, #-0x20]!
    // 0xe70908: stp             x9, x10, [SP, #-0x10]!
    // 0xe7090c: stp             x5, x8, [SP, #-0x10]!
    // 0xe70910: stp             x3, x4, [SP, #-0x10]!
    // 0xe70914: r0 = AllocateDouble()
    //     0xe70914: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70918: mov             x6, x0
    // 0xe7091c: ldp             x3, x4, [SP], #0x10
    // 0xe70920: ldp             x5, x8, [SP], #0x10
    // 0xe70924: ldp             x9, x10, [SP], #0x10
    // 0xe70928: ldp             q1, q7, [SP], #0x20
    // 0xe7092c: ldp             q8, q9, [SP], #0x20
    // 0xe70930: b               #0xe705f4
    // 0xe70934: stp             q7, q9, [SP, #-0x20]!
    // 0xe70938: SaveReg d1
    //     0xe70938: str             q1, [SP, #-0x10]!
    // 0xe7093c: stp             x9, x10, [SP, #-0x10]!
    // 0xe70940: stp             x6, x8, [SP, #-0x10]!
    // 0xe70944: stp             x4, x5, [SP, #-0x10]!
    // 0xe70948: SaveReg r3
    //     0xe70948: str             x3, [SP, #-8]!
    // 0xe7094c: r0 = AllocateDouble()
    //     0xe7094c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe70950: mov             x7, x0
    // 0xe70954: RestoreReg r3
    //     0xe70954: ldr             x3, [SP], #8
    // 0xe70958: ldp             x4, x5, [SP], #0x10
    // 0xe7095c: ldp             x6, x8, [SP], #0x10
    // 0xe70960: ldp             x9, x10, [SP], #0x10
    // 0xe70964: RestoreReg d1
    //     0xe70964: ldr             q1, [SP], #0x10
    // 0xe70968: ldp             q7, q9, [SP], #0x20
    // 0xe7096c: b               #0xe7061c
    // 0xe70970: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe70970: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe70974: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe70974: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe70978: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe70978: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ toPdfRect(/* No info */) {
    // ** addr: 0xeabd78, size: 0x64
    // 0xeabd78: EnterFrame
    //     0xeabd78: stp             fp, lr, [SP, #-0x10]!
    //     0xeabd7c: mov             fp, SP
    // 0xeabd80: AllocStack(0x20)
    //     0xeabd80: sub             SP, SP, #0x20
    // 0xeabd84: LoadField: d0 = r1->field_7
    //     0xeabd84: ldur            d0, [x1, #7]
    // 0xeabd88: stur            d0, [fp, #-0x20]
    // 0xeabd8c: LoadField: d1 = r1->field_f
    //     0xeabd8c: ldur            d1, [x1, #0xf]
    // 0xeabd90: stur            d1, [fp, #-0x18]
    // 0xeabd94: LoadField: d2 = r1->field_1f
    //     0xeabd94: ldur            d2, [x1, #0x1f]
    // 0xeabd98: ArrayLoad: d3 = r1[0]  ; List_8
    //     0xeabd98: ldur            d3, [x1, #0x17]
    // 0xeabd9c: fsub            d4, d2, d0
    // 0xeabda0: stur            d4, [fp, #-0x10]
    // 0xeabda4: fsub            d2, d3, d1
    // 0xeabda8: stur            d2, [fp, #-8]
    // 0xeabdac: r0 = PdfRect()
    //     0xeabdac: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xeabdb0: ldur            d0, [fp, #-0x20]
    // 0xeabdb4: StoreField: r0->field_7 = d0
    //     0xeabdb4: stur            d0, [x0, #7]
    // 0xeabdb8: ldur            d0, [fp, #-0x18]
    // 0xeabdbc: StoreField: r0->field_f = d0
    //     0xeabdbc: stur            d0, [x0, #0xf]
    // 0xeabdc0: ldur            d0, [fp, #-0x10]
    // 0xeabdc4: ArrayStore: r0[0] = d0  ; List_8
    //     0xeabdc4: stur            d0, [x0, #0x17]
    // 0xeabdc8: ldur            d0, [fp, #-8]
    // 0xeabdcc: StoreField: r0->field_1f = d0
    //     0xeabdcc: stur            d0, [x0, #0x1f]
    // 0xeabdd0: LeaveFrame
    //     0xeabdd0: mov             SP, fp
    //     0xeabdd4: ldp             fp, lr, [SP], #0x10
    // 0xeabdd8: ret
    //     0xeabdd8: ret             
  }
}
