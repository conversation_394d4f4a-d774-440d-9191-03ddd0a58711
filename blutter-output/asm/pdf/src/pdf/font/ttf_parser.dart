// lib: , url: package:pdf/src/pdf/font/ttf_parser.dart

// class id: 1050777, size: 0x8
class :: {
}

// class id: 917, size: 0x28, field offset: 0x8
class TtfParser extends Object {

  get _ fontName(/* No info */) {
    // ** addr: 0x7b73dc, size: 0x58
    // 0x7b73dc: EnterFrame
    //     0x7b73dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7b73e0: mov             fp, SP
    // 0x7b73e4: AllocStack(0x10)
    //     0x7b73e4: sub             SP, SP, #0x10
    // 0x7b73e8: SetupParameters(TtfParser this /* r1 => r0, fp-0x8 */)
    //     0x7b73e8: mov             x0, x1
    //     0x7b73ec: stur            x1, [fp, #-8]
    // 0x7b73f0: CheckStackOverflow
    //     0x7b73f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b73f4: cmp             SP, x16
    //     0x7b73f8: b.ls            #0x7b742c
    // 0x7b73fc: mov             x1, x0
    // 0x7b7400: r0 = getNameID()
    //     0x7b7400: bl              #0x7b7434  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::getNameID
    // 0x7b7404: cmp             w0, NULL
    // 0x7b7408: b.ne            #0x7b7420
    // 0x7b740c: ldur            x16, [fp, #-8]
    // 0x7b7410: str             x16, [SP]
    // 0x7b7414: r0 = _getHash()
    //     0x7b7414: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x7b7418: str             x0, [SP]
    // 0x7b741c: r0 = toString()
    //     0x7b741c: bl              #0xc460ec  ; [dart:core] _Smi::toString
    // 0x7b7420: LeaveFrame
    //     0x7b7420: mov             SP, fp
    //     0x7b7424: ldp             fp, lr, [SP], #0x10
    // 0x7b7428: ret
    //     0x7b7428: ret             
    // 0x7b742c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b742c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7430: b               #0x7b73fc
  }
  _ getNameID(/* No info */) {
    // ** addr: 0x7b7434, size: 0x80c
    // 0x7b7434: EnterFrame
    //     0x7b7434: stp             fp, lr, [SP, #-0x10]!
    //     0x7b7438: mov             fp, SP
    // 0x7b743c: AllocStack(0x138)
    //     0x7b743c: sub             SP, SP, #0x138
    // 0x7b7440: SetupParameters(TtfParser this /* r1 => r0, fp-0xa0 */)
    //     0x7b7440: mov             x0, x1
    //     0x7b7444: stur            x1, [fp, #-0xa0]
    // 0x7b7448: CheckStackOverflow
    //     0x7b7448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b744c: cmp             SP, x16
    //     0x7b7450: b.ls            #0x7b7c10
    // 0x7b7454: LoadField: r3 = r0->field_b
    //     0x7b7454: ldur            w3, [x0, #0xb]
    // 0x7b7458: DecompressPointer r3
    //     0x7b7458: add             x3, x3, HEAP, lsl #32
    // 0x7b745c: mov             x1, x3
    // 0x7b7460: stur            x3, [fp, #-0x98]
    // 0x7b7464: r2 = "name"
    //     0x7b7464: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7b7468: r0 = _getValueOrData()
    //     0x7b7468: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b746c: mov             x1, x0
    // 0x7b7470: ldur            x0, [fp, #-0x98]
    // 0x7b7474: LoadField: r2 = r0->field_f
    //     0x7b7474: ldur            w2, [x0, #0xf]
    // 0x7b7478: DecompressPointer r2
    //     0x7b7478: add             x2, x2, HEAP, lsl #32
    // 0x7b747c: cmp             w2, w1
    // 0x7b7480: b.ne            #0x7b748c
    // 0x7b7484: r2 = Null
    //     0x7b7484: mov             x2, NULL
    // 0x7b7488: b               #0x7b7490
    // 0x7b748c: mov             x2, x1
    // 0x7b7490: cmp             w2, NULL
    // 0x7b7494: b.ne            #0x7b74a8
    // 0x7b7498: r0 = Null
    //     0x7b7498: mov             x0, NULL
    // 0x7b749c: LeaveFrame
    //     0x7b749c: mov             SP, fp
    //     0x7b74a0: ldp             fp, lr, [SP], #0x10
    // 0x7b74a4: ret
    //     0x7b74a4: ret             
    // 0x7b74a8: ldur            x3, [fp, #-0xa0]
    // 0x7b74ac: r5 = 65280
    //     0x7b74ac: orr             x5, xzr, #0xff00
    // 0x7b74b0: r4 = 255
    //     0x7b74b0: movz            x4, #0xff
    // 0x7b74b4: LoadField: r6 = r3->field_7
    //     0x7b74b4: ldur            w6, [x3, #7]
    // 0x7b74b8: DecompressPointer r6
    //     0x7b74b8: add             x6, x6, HEAP, lsl #32
    // 0x7b74bc: r7 = LoadInt32Instr(r2)
    //     0x7b74bc: sbfx            x7, x2, #1, #0x1f
    //     0x7b74c0: tbz             w2, #0, #0x7b74c8
    //     0x7b74c4: ldur            x7, [x2, #7]
    // 0x7b74c8: add             x8, x7, #2
    // 0x7b74cc: LoadField: r0 = r6->field_13
    //     0x7b74cc: ldur            w0, [x6, #0x13]
    // 0x7b74d0: r1 = LoadInt32Instr(r0)
    //     0x7b74d0: sbfx            x1, x0, #1, #0x1f
    // 0x7b74d4: sub             x9, x1, #1
    // 0x7b74d8: mov             x0, x9
    // 0x7b74dc: mov             x1, x8
    // 0x7b74e0: cmp             x1, x0
    // 0x7b74e4: b.hs            #0x7b7c18
    // 0x7b74e8: ArrayLoad: r10 = r6[0]  ; List_4
    //     0x7b74e8: ldur            w10, [x6, #0x17]
    // 0x7b74ec: DecompressPointer r10
    //     0x7b74ec: add             x10, x10, HEAP, lsl #32
    // 0x7b74f0: LoadField: r0 = r6->field_1b
    //     0x7b74f0: ldur            w0, [x6, #0x1b]
    // 0x7b74f4: r6 = LoadInt32Instr(r0)
    //     0x7b74f4: sbfx            x6, x0, #1, #0x1f
    // 0x7b74f8: add             x0, x6, x8
    // 0x7b74fc: LoadField: r1 = r10->field_7
    //     0x7b74fc: ldur            x1, [x10, #7]
    // 0x7b7500: ldrh            w8, [x1, x0]
    // 0x7b7504: mov             x0, x8
    // 0x7b7508: ubfx            x0, x0, #0, #0x20
    // 0x7b750c: and             x1, x0, x5
    // 0x7b7510: ubfx            x1, x1, #0, #0x20
    // 0x7b7514: asr             x0, x1, #8
    // 0x7b7518: ubfx            x8, x8, #0, #0x20
    // 0x7b751c: and             x1, x8, x4
    // 0x7b7520: ubfx            x1, x1, #0, #0x20
    // 0x7b7524: lsl             x8, x1, #8
    // 0x7b7528: orr             x11, x0, x8
    // 0x7b752c: add             x8, x7, #4
    // 0x7b7530: mov             x0, x9
    // 0x7b7534: mov             x1, x8
    // 0x7b7538: cmp             x1, x0
    // 0x7b753c: b.hs            #0x7b7c1c
    // 0x7b7540: add             x0, x6, x8
    // 0x7b7544: LoadField: r1 = r10->field_7
    //     0x7b7544: ldur            x1, [x10, #7]
    // 0x7b7548: ldrh            w6, [x1, x0]
    // 0x7b754c: mov             x0, x6
    // 0x7b7550: ubfx            x0, x0, #0, #0x20
    // 0x7b7554: and             x1, x0, x5
    // 0x7b7558: ubfx            x1, x1, #0, #0x20
    // 0x7b755c: asr             x0, x1, #8
    // 0x7b7560: ubfx            x6, x6, #0, #0x20
    // 0x7b7564: and             x1, x6, x4
    // 0x7b7568: ubfx            x1, x1, #0, #0x20
    // 0x7b756c: lsl             x6, x1, #8
    // 0x7b7570: orr             x1, x0, x6
    // 0x7b7574: add             x6, x7, #6
    // 0x7b7578: lsl             x7, x11, #1
    // 0x7b757c: lsl             x8, x1, #1
    // 0x7b7580: r0 = BoxInt64Instr(r6)
    //     0x7b7580: sbfiz           x0, x6, #1, #0x1f
    //     0x7b7584: cmp             x6, x0, asr #1
    //     0x7b7588: b.eq            #0x7b7594
    //     0x7b758c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7590: stur            x6, [x0, #7]
    // 0x7b7594: mov             x4, x8
    // 0x7b7598: mov             x8, x3
    // 0x7b759c: mov             x6, x2
    // 0x7b75a0: mov             x5, x7
    // 0x7b75a4: r7 = Null
    //     0x7b75a4: mov             x7, NULL
    // 0x7b75a8: r3 = Null
    //     0x7b75a8: mov             x3, NULL
    // 0x7b75ac: r2 = 0
    //     0x7b75ac: movz            x2, #0
    // 0x7b75b0: b               #0x7b7714
    // 0x7b75b4: r5 = 65280
    //     0x7b75b4: orr             x5, xzr, #0xff00
    // 0x7b75b8: r4 = 255
    //     0x7b75b8: movz            x4, #0xff
    // 0x7b75bc: sub             SP, fp, #0x138
    // 0x7b75c0: stur            x0, [fp, #-0x98]
    // 0x7b75c4: r1 = Null
    //     0x7b75c4: mov             x1, NULL
    // 0x7b75c8: r2 = 12
    //     0x7b75c8: movz            x2, #0xc
    // 0x7b75cc: r0 = AllocateArray()
    //     0x7b75cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b75d0: r16 = "Error: "
    //     0x7b75d0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e8] "Error: "
    //     0x7b75d4: ldr             x16, [x16, #0x4e8]
    // 0x7b75d8: StoreField: r0->field_f = r16
    //     0x7b75d8: stur            w16, [x0, #0xf]
    // 0x7b75dc: ldur            x1, [fp, #-0x68]
    // 0x7b75e0: StoreField: r0->field_13 = r1
    //     0x7b75e0: stur            w1, [x0, #0x13]
    // 0x7b75e4: r16 = " "
    //     0x7b75e4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x7b75e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7b75e8: stur            w16, [x0, #0x17]
    // 0x7b75ec: ldur            x2, [fp, #-0x70]
    // 0x7b75f0: StoreField: r0->field_1b = r2
    //     0x7b75f0: stur            w2, [x0, #0x1b]
    // 0x7b75f4: r16 = " "
    //     0x7b75f4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x7b75f8: StoreField: r0->field_1f = r16
    //     0x7b75f8: stur            w16, [x0, #0x1f]
    // 0x7b75fc: ldur            x3, [fp, #-0x98]
    // 0x7b7600: StoreField: r0->field_23 = r3
    //     0x7b7600: stur            w3, [x0, #0x23]
    // 0x7b7604: str             x0, [SP]
    // 0x7b7608: r0 = _interpolate()
    //     0x7b7608: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b760c: mov             x1, x0
    // 0x7b7610: r0 = print()
    //     0x7b7610: bl              #0x63fe38  ; [dart:core] ::print
    // 0x7b7614: ldur            x8, [fp, #-0x90]
    // 0x7b7618: ldur            x7, [fp, #-0x38]
    // 0x7b761c: ldur            x6, [fp, #-0x40]
    // 0x7b7620: ldur            x5, [fp, #-0x48]
    // 0x7b7624: ldur            x4, [fp, #-0x50]
    // 0x7b7628: ldur            x3, [fp, #-0x58]
    // 0x7b762c: ldur            x2, [fp, #-0x60]
    // 0x7b7630: ldur            x1, [fp, #-0x78]
    // 0x7b7634: ldur            x0, [fp, #-0x80]
    // 0x7b7638: mov             x11, x8
    // 0x7b763c: ldur            x10, [fp, #-0x98]
    // 0x7b7640: mov             x9, x7
    // 0x7b7644: mov             x8, x6
    // 0x7b7648: mov             x7, x5
    // 0x7b764c: mov             x6, x4
    // 0x7b7650: mov             x5, x3
    // 0x7b7654: mov             x4, x2
    // 0x7b7658: ldur            x3, [fp, #-0x68]
    // 0x7b765c: ldur            x2, [fp, #-0x70]
    // 0x7b7660: b               #0x7b79fc
    // 0x7b7664: sub             SP, fp, #0x138
    // 0x7b7668: stur            x0, [fp, #-0x98]
    // 0x7b766c: r1 = Null
    //     0x7b766c: mov             x1, NULL
    // 0x7b7670: r2 = 12
    //     0x7b7670: movz            x2, #0xc
    // 0x7b7674: r0 = AllocateArray()
    //     0x7b7674: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b7678: r16 = "Error: "
    //     0x7b7678: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e8] "Error: "
    //     0x7b767c: ldr             x16, [x16, #0x4e8]
    // 0x7b7680: StoreField: r0->field_f = r16
    //     0x7b7680: stur            w16, [x0, #0xf]
    // 0x7b7684: ldur            x1, [fp, #-0x68]
    // 0x7b7688: StoreField: r0->field_13 = r1
    //     0x7b7688: stur            w1, [x0, #0x13]
    // 0x7b768c: r16 = " "
    //     0x7b768c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x7b7690: ArrayStore: r0[0] = r16  ; List_4
    //     0x7b7690: stur            w16, [x0, #0x17]
    // 0x7b7694: ldur            x1, [fp, #-0x70]
    // 0x7b7698: StoreField: r0->field_1b = r1
    //     0x7b7698: stur            w1, [x0, #0x1b]
    // 0x7b769c: r16 = " "
    //     0x7b769c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x7b76a0: StoreField: r0->field_1f = r16
    //     0x7b76a0: stur            w16, [x0, #0x1f]
    // 0x7b76a4: ldur            x1, [fp, #-0x98]
    // 0x7b76a8: StoreField: r0->field_23 = r1
    //     0x7b76a8: stur            w1, [x0, #0x23]
    // 0x7b76ac: str             x0, [SP]
    // 0x7b76b0: r0 = _interpolate()
    //     0x7b76b0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b76b4: mov             x1, x0
    // 0x7b76b8: r0 = print()
    //     0x7b76b8: bl              #0x63fe38  ; [dart:core] ::print
    // 0x7b76bc: ldur            x6, [fp, #-0x90]
    // 0x7b76c0: ldur            x5, [fp, #-0x38]
    // 0x7b76c4: ldur            x4, [fp, #-0x40]
    // 0x7b76c8: ldur            x3, [fp, #-0x48]
    // 0x7b76cc: ldur            x2, [fp, #-0x50]
    // 0x7b76d0: ldur            x1, [fp, #-0x58]
    // 0x7b76d4: ldur            x0, [fp, #-0x60]
    // 0x7b76d8: mov             x7, x6
    // 0x7b76dc: ldur            x6, [fp, #-0x98]
    // 0x7b76e0: r8 = LoadInt32Instr(r0)
    //     0x7b76e0: sbfx            x8, x0, #1, #0x1f
    //     0x7b76e4: tbz             w0, #0, #0x7b76ec
    //     0x7b76e8: ldur            x8, [x0, #7]
    // 0x7b76ec: add             x0, x8, #1
    // 0x7b76f0: mov             x8, x7
    // 0x7b76f4: mov             x7, x6
    // 0x7b76f8: mov             x6, x5
    // 0x7b76fc: mov             x5, x4
    // 0x7b7700: mov             x4, x3
    // 0x7b7704: mov             x16, x0
    // 0x7b7708: mov             x0, x2
    // 0x7b770c: mov             x2, x16
    // 0x7b7710: mov             x3, x1
    // 0x7b7714: stur            x8, [fp, #-0x98]
    // 0x7b7718: stur            x7, [fp, #-0xa0]
    // 0x7b771c: stur            x6, [fp, #-0xd0]
    // 0x7b7720: stur            x5, [fp, #-0xd8]
    // 0x7b7724: stur            x4, [fp, #-0xe0]
    // 0x7b7728: stur            x3, [fp, #-0xe8]
    // 0x7b772c: stur            x2, [fp, #-0xf0]
    // 0x7b7730: CheckStackOverflow
    //     0x7b7730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b7734: cmp             SP, x16
    //     0x7b7738: b.ls            #0x7b7c20
    // 0x7b773c: r1 = LoadInt32Instr(r5)
    //     0x7b773c: sbfx            x1, x5, #1, #0x1f
    //     0x7b7740: tbz             w5, #0, #0x7b7748
    //     0x7b7744: ldur            x1, [x5, #7]
    // 0x7b7748: cmp             x2, x1
    // 0x7b774c: b.ge            #0x7b7c00
    // 0x7b7750: r10 = 65280
    //     0x7b7750: orr             x10, xzr, #0xff00
    // 0x7b7754: r9 = 255
    //     0x7b7754: movz            x9, #0xff
    // 0x7b7758: LoadField: r11 = r8->field_7
    //     0x7b7758: ldur            w11, [x8, #7]
    // 0x7b775c: DecompressPointer r11
    //     0x7b775c: add             x11, x11, HEAP, lsl #32
    // 0x7b7760: LoadField: r1 = r11->field_13
    //     0x7b7760: ldur            w1, [x11, #0x13]
    // 0x7b7764: r12 = LoadInt32Instr(r1)
    //     0x7b7764: sbfx            x12, x1, #1, #0x1f
    // 0x7b7768: sub             x13, x12, #1
    // 0x7b776c: r12 = LoadInt32Instr(r0)
    //     0x7b776c: sbfx            x12, x0, #1, #0x1f
    //     0x7b7770: tbz             w0, #0, #0x7b7778
    //     0x7b7774: ldur            x12, [x0, #7]
    // 0x7b7778: mov             x0, x13
    // 0x7b777c: mov             x1, x12
    // 0x7b7780: cmp             x1, x0
    // 0x7b7784: b.hs            #0x7b7c28
    // 0x7b7788: ArrayLoad: r14 = r11[0]  ; List_4
    //     0x7b7788: ldur            w14, [x11, #0x17]
    // 0x7b778c: DecompressPointer r14
    //     0x7b778c: add             x14, x14, HEAP, lsl #32
    // 0x7b7790: LoadField: r0 = r11->field_1b
    //     0x7b7790: ldur            w0, [x11, #0x1b]
    // 0x7b7794: r19 = LoadInt32Instr(r0)
    //     0x7b7794: sbfx            x19, x0, #1, #0x1f
    // 0x7b7798: add             x0, x19, x12
    // 0x7b779c: LoadField: r1 = r14->field_7
    //     0x7b779c: ldur            x1, [x14, #7]
    // 0x7b77a0: ldrh            w20, [x1, x0]
    // 0x7b77a4: mov             x0, x20
    // 0x7b77a8: ubfx            x0, x0, #0, #0x20
    // 0x7b77ac: and             x1, x0, x10
    // 0x7b77b0: ubfx            x1, x1, #0, #0x20
    // 0x7b77b4: asr             x0, x1, #8
    // 0x7b77b8: ubfx            x20, x20, #0, #0x20
    // 0x7b77bc: and             x1, x20, x9
    // 0x7b77c0: ubfx            x1, x1, #0, #0x20
    // 0x7b77c4: lsl             x20, x1, #8
    // 0x7b77c8: orr             x23, x0, x20
    // 0x7b77cc: stur            x23, [fp, #-0xc8]
    // 0x7b77d0: add             x20, x12, #6
    // 0x7b77d4: mov             x0, x13
    // 0x7b77d8: mov             x1, x20
    // 0x7b77dc: cmp             x1, x0
    // 0x7b77e0: b.hs            #0x7b7c2c
    // 0x7b77e4: add             x0, x19, x20
    // 0x7b77e8: LoadField: r1 = r14->field_7
    //     0x7b77e8: ldur            x1, [x14, #7]
    // 0x7b77ec: ldrh            w20, [x1, x0]
    // 0x7b77f0: mov             x0, x20
    // 0x7b77f4: ubfx            x0, x0, #0, #0x20
    // 0x7b77f8: and             x1, x0, x10
    // 0x7b77fc: ubfx            x1, x1, #0, #0x20
    // 0x7b7800: asr             x0, x1, #8
    // 0x7b7804: ubfx            x20, x20, #0, #0x20
    // 0x7b7808: and             x1, x20, x9
    // 0x7b780c: ubfx            x1, x1, #0, #0x20
    // 0x7b7810: lsl             x20, x1, #8
    // 0x7b7814: orr             x24, x0, x20
    // 0x7b7818: stur            x24, [fp, #-0xc0]
    // 0x7b781c: add             x20, x12, #8
    // 0x7b7820: mov             x0, x13
    // 0x7b7824: mov             x1, x20
    // 0x7b7828: cmp             x1, x0
    // 0x7b782c: b.hs            #0x7b7c30
    // 0x7b7830: add             x0, x19, x20
    // 0x7b7834: LoadField: r1 = r14->field_7
    //     0x7b7834: ldur            x1, [x14, #7]
    // 0x7b7838: ldrh            w20, [x1, x0]
    // 0x7b783c: mov             x0, x20
    // 0x7b7840: ubfx            x0, x0, #0, #0x20
    // 0x7b7844: and             x1, x0, x10
    // 0x7b7848: ubfx            x1, x1, #0, #0x20
    // 0x7b784c: asr             x0, x1, #8
    // 0x7b7850: ubfx            x20, x20, #0, #0x20
    // 0x7b7854: and             x1, x20, x9
    // 0x7b7858: ubfx            x1, x1, #0, #0x20
    // 0x7b785c: lsl             x20, x1, #8
    // 0x7b7860: orr             x25, x0, x20
    // 0x7b7864: stur            x25, [fp, #-0xb8]
    // 0x7b7868: add             x20, x12, #0xa
    // 0x7b786c: mov             x0, x13
    // 0x7b7870: mov             x1, x20
    // 0x7b7874: cmp             x1, x0
    // 0x7b7878: b.hs            #0x7b7c34
    // 0x7b787c: add             x0, x19, x20
    // 0x7b7880: LoadField: r1 = r14->field_7
    //     0x7b7880: ldur            x1, [x14, #7]
    // 0x7b7884: ldrh            w13, [x1, x0]
    // 0x7b7888: mov             x0, x13
    // 0x7b788c: ubfx            x0, x0, #0, #0x20
    // 0x7b7890: and             x1, x0, x10
    // 0x7b7894: ubfx            x1, x1, #0, #0x20
    // 0x7b7898: asr             x0, x1, #8
    // 0x7b789c: ubfx            x13, x13, #0, #0x20
    // 0x7b78a0: and             x1, x13, x9
    // 0x7b78a4: ubfx            x1, x1, #0, #0x20
    // 0x7b78a8: lsl             x13, x1, #8
    // 0x7b78ac: orr             x14, x0, x13
    // 0x7b78b0: stur            x14, [fp, #-0xb0]
    // 0x7b78b4: add             x13, x12, #0xc
    // 0x7b78b8: stur            x13, [fp, #-0xa8]
    // 0x7b78bc: cmp             x23, #1
    // 0x7b78c0: b.ne            #0x7b7a44
    // 0x7b78c4: cmp             x24, #6
    // 0x7b78c8: b.ne            #0x7b7a30
    // 0x7b78cc: r0 = LoadClassIdInstr(r11)
    //     0x7b78cc: ldur            x0, [x11, #-1]
    //     0x7b78d0: ubfx            x0, x0, #0xc, #0x14
    // 0x7b78d4: mov             x1, x11
    // 0x7b78d8: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7b78d8: sub             lr, x0, #0xf60
    //     0x7b78dc: ldr             lr, [x21, lr, lsl #3]
    //     0x7b78e0: blr             lr
    // 0x7b78e4: mov             x3, x0
    // 0x7b78e8: ldur            x2, [fp, #-0xd0]
    // 0x7b78ec: cmp             w2, NULL
    // 0x7b78f0: b.eq            #0x7b7c38
    // 0x7b78f4: ldur            x4, [fp, #-0xe0]
    // 0x7b78f8: r0 = LoadInt32Instr(r4)
    //     0x7b78f8: sbfx            x0, x4, #1, #0x1f
    //     0x7b78fc: tbz             w4, #0, #0x7b7904
    //     0x7b7900: ldur            x0, [x4, #7]
    // 0x7b7904: r1 = LoadInt32Instr(r2)
    //     0x7b7904: sbfx            x1, x2, #1, #0x1f
    //     0x7b7908: tbz             w2, #0, #0x7b7910
    //     0x7b790c: ldur            x1, [x2, #7]
    // 0x7b7910: add             x5, x1, x0
    // 0x7b7914: ldur            x6, [fp, #-0xb0]
    // 0x7b7918: add             x7, x5, x6
    // 0x7b791c: ldur            x5, [fp, #-0xb8]
    // 0x7b7920: lsl             x8, x5, #1
    // 0x7b7924: stur            x8, [fp, #-0xf8]
    // 0x7b7928: r0 = BoxInt64Instr(r7)
    //     0x7b7928: sbfiz           x0, x7, #1, #0x1f
    //     0x7b792c: cmp             x7, x0, asr #1
    //     0x7b7930: b.eq            #0x7b793c
    //     0x7b7934: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7938: stur            x7, [x0, #7]
    // 0x7b793c: r1 = LoadClassIdInstr(r3)
    //     0x7b793c: ldur            x1, [x3, #-1]
    //     0x7b7940: ubfx            x1, x1, #0xc, #0x14
    // 0x7b7944: stp             x8, x0, [SP]
    // 0x7b7948: mov             x0, x1
    // 0x7b794c: mov             x1, x3
    // 0x7b7950: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7b7950: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7b7954: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7b7954: sub             lr, x0, #1, lsl #12
    //     0x7b7958: ldr             lr, [x21, lr, lsl #3]
    //     0x7b795c: blr             lr
    // 0x7b7960: mov             x2, x0
    // 0x7b7964: r1 = Instance_Utf8Codec
    //     0x7b7964: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0x7b7968: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7b7968: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7b796c: r0 = decode()
    //     0x7b796c: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0x7b7970: mov             x3, x0
    // 0x7b7974: ldur            x2, [fp, #-0xf0]
    // 0x7b7978: r0 = BoxInt64Instr(r2)
    //     0x7b7978: sbfiz           x0, x2, #1, #0x1f
    //     0x7b797c: cmp             x2, x0, asr #1
    //     0x7b7980: b.eq            #0x7b798c
    //     0x7b7984: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7988: stur            x2, [x0, #7]
    // 0x7b798c: mov             x2, x0
    // 0x7b7990: ldur            x4, [fp, #-0xc8]
    // 0x7b7994: lsl             x5, x4, #1
    // 0x7b7998: ldur            x6, [fp, #-0xc0]
    // 0x7b799c: lsl             x4, x6, #1
    // 0x7b79a0: ldur            x7, [fp, #-0xb0]
    // 0x7b79a4: lsl             x6, x7, #1
    // 0x7b79a8: ldur            x8, [fp, #-0xa8]
    // 0x7b79ac: r0 = BoxInt64Instr(r8)
    //     0x7b79ac: sbfiz           x0, x8, #1, #0x1f
    //     0x7b79b0: cmp             x8, x0, asr #1
    //     0x7b79b4: b.eq            #0x7b79c0
    //     0x7b79b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b79bc: stur            x8, [x0, #7]
    // 0x7b79c0: ldur            x11, [fp, #-0x98]
    // 0x7b79c4: ldur            x10, [fp, #-0xa0]
    // 0x7b79c8: ldur            x9, [fp, #-0xd0]
    // 0x7b79cc: ldur            x8, [fp, #-0xd8]
    // 0x7b79d0: ldur            x7, [fp, #-0xe0]
    // 0x7b79d4: mov             x16, x6
    // 0x7b79d8: mov             x6, x0
    // 0x7b79dc: mov             x0, x16
    // 0x7b79e0: mov             x16, x5
    // 0x7b79e4: mov             x5, x3
    // 0x7b79e8: mov             x3, x16
    // 0x7b79ec: mov             x16, x4
    // 0x7b79f0: mov             x4, x2
    // 0x7b79f4: mov             x2, x16
    // 0x7b79f8: ldur            x1, [fp, #-0xf8]
    // 0x7b79fc: mov             x13, x11
    // 0x7b7a00: mov             x12, x10
    // 0x7b7a04: mov             x11, x9
    // 0x7b7a08: mov             x10, x8
    // 0x7b7a0c: mov             x9, x7
    // 0x7b7a10: mov             x8, x6
    // 0x7b7a14: mov             x7, x5
    // 0x7b7a18: mov             x6, x4
    // 0x7b7a1c: mov             x5, x3
    // 0x7b7a20: mov             x4, x2
    // 0x7b7a24: mov             x3, x1
    // 0x7b7a28: mov             x2, x0
    // 0x7b7a2c: b               #0x7b7ac8
    // 0x7b7a30: mov             x8, x13
    // 0x7b7a34: mov             x4, x23
    // 0x7b7a38: mov             x6, x24
    // 0x7b7a3c: mov             x7, x14
    // 0x7b7a40: b               #0x7b7a54
    // 0x7b7a44: mov             x8, x13
    // 0x7b7a48: mov             x4, x23
    // 0x7b7a4c: mov             x6, x24
    // 0x7b7a50: mov             x7, x14
    // 0x7b7a54: ldur            x3, [fp, #-0xb8]
    // 0x7b7a58: r0 = BoxInt64Instr(r2)
    //     0x7b7a58: sbfiz           x0, x2, #1, #0x1f
    //     0x7b7a5c: cmp             x2, x0, asr #1
    //     0x7b7a60: b.eq            #0x7b7a6c
    //     0x7b7a64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7a68: stur            x2, [x0, #7]
    // 0x7b7a6c: mov             x2, x0
    // 0x7b7a70: lsl             x5, x4, #1
    // 0x7b7a74: lsl             x4, x6, #1
    // 0x7b7a78: lsl             x6, x3, #1
    // 0x7b7a7c: lsl             x3, x7, #1
    // 0x7b7a80: r0 = BoxInt64Instr(r8)
    //     0x7b7a80: sbfiz           x0, x8, #1, #0x1f
    //     0x7b7a84: cmp             x8, x0, asr #1
    //     0x7b7a88: b.eq            #0x7b7a94
    //     0x7b7a8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7a90: stur            x8, [x0, #7]
    // 0x7b7a94: ldur            x13, [fp, #-0x98]
    // 0x7b7a98: ldur            x12, [fp, #-0xa0]
    // 0x7b7a9c: ldur            x11, [fp, #-0xd0]
    // 0x7b7aa0: ldur            x10, [fp, #-0xd8]
    // 0x7b7aa4: ldur            x9, [fp, #-0xe0]
    // 0x7b7aa8: mov             x8, x0
    // 0x7b7aac: ldur            x7, [fp, #-0xe8]
    // 0x7b7ab0: mov             x16, x3
    // 0x7b7ab4: mov             x3, x2
    // 0x7b7ab8: mov             x2, x16
    // 0x7b7abc: mov             x16, x6
    // 0x7b7ac0: mov             x6, x3
    // 0x7b7ac4: mov             x3, x16
    // 0x7b7ac8: stur            x13, [fp, #-0x98]
    // 0x7b7acc: stur            x12, [fp, #-0xa0]
    // 0x7b7ad0: stur            x11, [fp, #-0xd0]
    // 0x7b7ad4: stur            x10, [fp, #-0xd8]
    // 0x7b7ad8: stur            x9, [fp, #-0xe0]
    // 0x7b7adc: stur            x8, [fp, #-0xf8]
    // 0x7b7ae0: stur            x7, [fp, #-0x100]
    // 0x7b7ae4: r17 = -264
    //     0x7b7ae4: movn            x17, #0x107
    // 0x7b7ae8: str             x6, [fp, x17]
    // 0x7b7aec: r17 = -272
    //     0x7b7aec: movn            x17, #0x10f
    // 0x7b7af0: str             x5, [fp, x17]
    // 0x7b7af4: r17 = -280
    //     0x7b7af4: movn            x17, #0x117
    // 0x7b7af8: str             x4, [fp, x17]
    // 0x7b7afc: r17 = -288
    //     0x7b7afc: movn            x17, #0x11f
    // 0x7b7b00: str             x3, [fp, x17]
    // 0x7b7b04: r17 = -296
    //     0x7b7b04: movn            x17, #0x127
    // 0x7b7b08: str             x2, [fp, x17]
    // 0x7b7b0c: cmp             w5, #6
    // 0x7b7b10: b.ne            #0x7b7bd8
    // 0x7b7b14: cmp             w4, #0xc
    // 0x7b7b18: b.ne            #0x7b7bd8
    // 0x7b7b1c: LoadField: r1 = r13->field_7
    //     0x7b7b1c: ldur            w1, [x13, #7]
    // 0x7b7b20: DecompressPointer r1
    //     0x7b7b20: add             x1, x1, HEAP, lsl #32
    // 0x7b7b24: r0 = LoadClassIdInstr(r1)
    //     0x7b7b24: ldur            x0, [x1, #-1]
    //     0x7b7b28: ubfx            x0, x0, #0xc, #0x14
    // 0x7b7b2c: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7b7b2c: sub             lr, x0, #0xf60
    //     0x7b7b30: ldr             lr, [x21, lr, lsl #3]
    //     0x7b7b34: blr             lr
    // 0x7b7b38: mov             x2, x0
    // 0x7b7b3c: ldur            x5, [fp, #-0xd0]
    // 0x7b7b40: cmp             w5, NULL
    // 0x7b7b44: b.eq            #0x7b7c3c
    // 0x7b7b48: ldur            x3, [fp, #-0xe0]
    // 0x7b7b4c: r0 = LoadInt32Instr(r3)
    //     0x7b7b4c: sbfx            x0, x3, #1, #0x1f
    //     0x7b7b50: tbz             w3, #0, #0x7b7b58
    //     0x7b7b54: ldur            x0, [x3, #7]
    // 0x7b7b58: r1 = LoadInt32Instr(r5)
    //     0x7b7b58: sbfx            x1, x5, #1, #0x1f
    //     0x7b7b5c: tbz             w5, #0, #0x7b7b64
    //     0x7b7b60: ldur            x1, [x5, #7]
    // 0x7b7b64: add             x4, x1, x0
    // 0x7b7b68: r17 = -296
    //     0x7b7b68: movn            x17, #0x127
    // 0x7b7b6c: ldr             x0, [fp, x17]
    // 0x7b7b70: r1 = LoadInt32Instr(r0)
    //     0x7b7b70: sbfx            x1, x0, #1, #0x1f
    //     0x7b7b74: tbz             w0, #0, #0x7b7b7c
    //     0x7b7b78: ldur            x1, [x0, #7]
    // 0x7b7b7c: add             x6, x4, x1
    // 0x7b7b80: r0 = BoxInt64Instr(r6)
    //     0x7b7b80: sbfiz           x0, x6, #1, #0x1f
    //     0x7b7b84: cmp             x6, x0, asr #1
    //     0x7b7b88: b.eq            #0x7b7b94
    //     0x7b7b8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b7b90: stur            x6, [x0, #7]
    // 0x7b7b94: r1 = LoadClassIdInstr(r2)
    //     0x7b7b94: ldur            x1, [x2, #-1]
    //     0x7b7b98: ubfx            x1, x1, #0xc, #0x14
    // 0x7b7b9c: r17 = -288
    //     0x7b7b9c: movn            x17, #0x11f
    // 0x7b7ba0: ldr             x16, [fp, x17]
    // 0x7b7ba4: stp             x16, x0, [SP]
    // 0x7b7ba8: mov             x0, x1
    // 0x7b7bac: mov             x1, x2
    // 0x7b7bb0: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7b7bb0: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7b7bb4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7b7bb4: sub             lr, x0, #1, lsl #12
    //     0x7b7bb8: ldr             lr, [x21, lr, lsl #3]
    //     0x7b7bbc: blr             lr
    // 0x7b7bc0: ldur            x1, [fp, #-0x98]
    // 0x7b7bc4: mov             x2, x0
    // 0x7b7bc8: r0 = _decodeUtf16()
    //     0x7b7bc8: bl              #0x7b7c40  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_decodeUtf16
    // 0x7b7bcc: LeaveFrame
    //     0x7b7bcc: mov             SP, fp
    //     0x7b7bd0: ldp             fp, lr, [SP], #0x10
    // 0x7b7bd4: ret
    //     0x7b7bd4: ret             
    // 0x7b7bd8: ldur            x7, [fp, #-0x98]
    // 0x7b7bdc: ldur            x6, [fp, #-0xa0]
    // 0x7b7be0: ldur            x5, [fp, #-0xd0]
    // 0x7b7be4: ldur            x4, [fp, #-0xd8]
    // 0x7b7be8: ldur            x3, [fp, #-0xe0]
    // 0x7b7bec: ldur            x2, [fp, #-0xf8]
    // 0x7b7bf0: ldur            x1, [fp, #-0x100]
    // 0x7b7bf4: r17 = -264
    //     0x7b7bf4: movn            x17, #0x107
    // 0x7b7bf8: ldr             x0, [fp, x17]
    // 0x7b7bfc: b               #0x7b76e0
    // 0x7b7c00: ldur            x0, [fp, #-0xe8]
    // 0x7b7c04: LeaveFrame
    //     0x7b7c04: mov             SP, fp
    //     0x7b7c08: ldp             fp, lr, [SP], #0x10
    // 0x7b7c0c: ret
    //     0x7b7c0c: ret             
    // 0x7b7c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b7c10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7c14: b               #0x7b7454
    // 0x7b7c18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b7c20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7c24: b               #0x7b773c
    // 0x7b7c28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7c34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b7c38: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7b7c38: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7b7c3c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7b7c3c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _decodeUtf16(/* No info */) {
    // ** addr: 0x7b7c40, size: 0x130
    // 0x7b7c40: EnterFrame
    //     0x7b7c40: stp             fp, lr, [SP, #-0x10]!
    //     0x7b7c44: mov             fp, SP
    // 0x7b7c48: AllocStack(0x30)
    //     0x7b7c48: sub             SP, SP, #0x30
    // 0x7b7c4c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7b7c4c: mov             x0, x2
    //     0x7b7c50: stur            x2, [fp, #-8]
    // 0x7b7c54: CheckStackOverflow
    //     0x7b7c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b7c58: cmp             SP, x16
    //     0x7b7c5c: b.ls            #0x7b7d5c
    // 0x7b7c60: r1 = <int>
    //     0x7b7c60: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7b7c64: r2 = 0
    //     0x7b7c64: movz            x2, #0
    // 0x7b7c68: r0 = _GrowableList()
    //     0x7b7c68: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b7c6c: mov             x3, x0
    // 0x7b7c70: ldur            x2, [fp, #-8]
    // 0x7b7c74: stur            x3, [fp, #-0x30]
    // 0x7b7c78: LoadField: r0 = r2->field_13
    //     0x7b7c78: ldur            w0, [x2, #0x13]
    // 0x7b7c7c: r4 = LoadInt32Instr(r0)
    //     0x7b7c7c: sbfx            x4, x0, #1, #0x1f
    // 0x7b7c80: stur            x4, [fp, #-0x28]
    // 0x7b7c84: r5 = 0
    //     0x7b7c84: movz            x5, #0
    // 0x7b7c88: stur            x5, [fp, #-0x20]
    // 0x7b7c8c: CheckStackOverflow
    //     0x7b7c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b7c90: cmp             SP, x16
    //     0x7b7c94: b.ls            #0x7b7d64
    // 0x7b7c98: cmp             x5, x4
    // 0x7b7c9c: b.ge            #0x7b7d40
    // 0x7b7ca0: LoadField: r0 = r2->field_7
    //     0x7b7ca0: ldur            x0, [x2, #7]
    // 0x7b7ca4: ldrb            w1, [x0, x5]
    // 0x7b7ca8: lsl             x6, x1, #8
    // 0x7b7cac: add             x7, x5, #1
    // 0x7b7cb0: mov             x0, x4
    // 0x7b7cb4: mov             x1, x7
    // 0x7b7cb8: cmp             x1, x0
    // 0x7b7cbc: b.hs            #0x7b7d6c
    // 0x7b7cc0: LoadField: r0 = r2->field_7
    //     0x7b7cc0: ldur            x0, [x2, #7]
    // 0x7b7cc4: ldrb            w1, [x0, x7]
    // 0x7b7cc8: orr             x0, x6, x1
    // 0x7b7ccc: stur            x0, [fp, #-0x18]
    // 0x7b7cd0: LoadField: r1 = r3->field_b
    //     0x7b7cd0: ldur            w1, [x3, #0xb]
    // 0x7b7cd4: LoadField: r6 = r3->field_f
    //     0x7b7cd4: ldur            w6, [x3, #0xf]
    // 0x7b7cd8: DecompressPointer r6
    //     0x7b7cd8: add             x6, x6, HEAP, lsl #32
    // 0x7b7cdc: LoadField: r7 = r6->field_b
    //     0x7b7cdc: ldur            w7, [x6, #0xb]
    // 0x7b7ce0: r6 = LoadInt32Instr(r1)
    //     0x7b7ce0: sbfx            x6, x1, #1, #0x1f
    // 0x7b7ce4: stur            x6, [fp, #-0x10]
    // 0x7b7ce8: r1 = LoadInt32Instr(r7)
    //     0x7b7ce8: sbfx            x1, x7, #1, #0x1f
    // 0x7b7cec: cmp             x6, x1
    // 0x7b7cf0: b.ne            #0x7b7cfc
    // 0x7b7cf4: mov             x1, x3
    // 0x7b7cf8: r0 = _growToNextCapacity()
    //     0x7b7cf8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b7cfc: ldur            x1, [fp, #-0x30]
    // 0x7b7d00: ldur            x0, [fp, #-0x20]
    // 0x7b7d04: ldur            x2, [fp, #-0x18]
    // 0x7b7d08: ldur            x3, [fp, #-0x10]
    // 0x7b7d0c: add             x4, x3, #1
    // 0x7b7d10: lsl             x5, x4, #1
    // 0x7b7d14: StoreField: r1->field_b = r5
    //     0x7b7d14: stur            w5, [x1, #0xb]
    // 0x7b7d18: LoadField: r4 = r1->field_f
    //     0x7b7d18: ldur            w4, [x1, #0xf]
    // 0x7b7d1c: DecompressPointer r4
    //     0x7b7d1c: add             x4, x4, HEAP, lsl #32
    // 0x7b7d20: lsl             x5, x2, #1
    // 0x7b7d24: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0x7b7d24: add             x2, x4, x3, lsl #2
    //     0x7b7d28: stur            w5, [x2, #0xf]
    // 0x7b7d2c: add             x5, x0, #2
    // 0x7b7d30: ldur            x2, [fp, #-8]
    // 0x7b7d34: mov             x3, x1
    // 0x7b7d38: ldur            x4, [fp, #-0x28]
    // 0x7b7d3c: b               #0x7b7c88
    // 0x7b7d40: mov             x1, x3
    // 0x7b7d44: r2 = 0
    //     0x7b7d44: movz            x2, #0
    // 0x7b7d48: r3 = Null
    //     0x7b7d48: mov             x3, NULL
    // 0x7b7d4c: r0 = createFromCharCodes()
    //     0x7b7d4c: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0x7b7d50: LeaveFrame
    //     0x7b7d50: mov             SP, fp
    //     0x7b7d54: ldp             fp, lr, [SP], #0x10
    // 0x7b7d58: ret
    //     0x7b7d58: ret             
    // 0x7b7d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b7d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7d60: b               #0x7b7c60
    // 0x7b7d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b7d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7d68: b               #0x7b7c98
    // 0x7b7d6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b7d6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ numOfLongHorMetrics(/* No info */) {
    // ** addr: 0x7bc650, size: 0xfc
    // 0x7bc650: EnterFrame
    //     0x7bc650: stp             fp, lr, [SP, #-0x10]!
    //     0x7bc654: mov             fp, SP
    // 0x7bc658: AllocStack(0x10)
    //     0x7bc658: sub             SP, SP, #0x10
    // 0x7bc65c: CheckStackOverflow
    //     0x7bc65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bc660: cmp             SP, x16
    //     0x7bc664: b.ls            #0x7bc73c
    // 0x7bc668: LoadField: r0 = r1->field_7
    //     0x7bc668: ldur            w0, [x1, #7]
    // 0x7bc66c: DecompressPointer r0
    //     0x7bc66c: add             x0, x0, HEAP, lsl #32
    // 0x7bc670: stur            x0, [fp, #-0x10]
    // 0x7bc674: LoadField: r3 = r1->field_b
    //     0x7bc674: ldur            w3, [x1, #0xb]
    // 0x7bc678: DecompressPointer r3
    //     0x7bc678: add             x3, x3, HEAP, lsl #32
    // 0x7bc67c: mov             x1, x3
    // 0x7bc680: stur            x3, [fp, #-8]
    // 0x7bc684: r2 = "hhea"
    //     0x7bc684: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0x7bc688: ldr             x2, [x2, #0x888]
    // 0x7bc68c: r0 = _getValueOrData()
    //     0x7bc68c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7bc690: ldur            x2, [fp, #-8]
    // 0x7bc694: LoadField: r3 = r2->field_f
    //     0x7bc694: ldur            w3, [x2, #0xf]
    // 0x7bc698: DecompressPointer r3
    //     0x7bc698: add             x3, x3, HEAP, lsl #32
    // 0x7bc69c: cmp             w3, w0
    // 0x7bc6a0: b.ne            #0x7bc6ac
    // 0x7bc6a4: r5 = Null
    //     0x7bc6a4: mov             x5, NULL
    // 0x7bc6a8: b               #0x7bc6b0
    // 0x7bc6ac: mov             x5, x0
    // 0x7bc6b0: ldur            x2, [fp, #-0x10]
    // 0x7bc6b4: r4 = 65280
    //     0x7bc6b4: orr             x4, xzr, #0xff00
    // 0x7bc6b8: r3 = 255
    //     0x7bc6b8: movz            x3, #0xff
    // 0x7bc6bc: cmp             w5, NULL
    // 0x7bc6c0: b.eq            #0x7bc744
    // 0x7bc6c4: r6 = LoadInt32Instr(r5)
    //     0x7bc6c4: sbfx            x6, x5, #1, #0x1f
    //     0x7bc6c8: tbz             w5, #0, #0x7bc6d0
    //     0x7bc6cc: ldur            x6, [x5, #7]
    // 0x7bc6d0: add             x5, x6, #0x22
    // 0x7bc6d4: LoadField: r6 = r2->field_13
    //     0x7bc6d4: ldur            w6, [x2, #0x13]
    // 0x7bc6d8: r7 = LoadInt32Instr(r6)
    //     0x7bc6d8: sbfx            x7, x6, #1, #0x1f
    // 0x7bc6dc: sub             x0, x7, #1
    // 0x7bc6e0: mov             x1, x5
    // 0x7bc6e4: cmp             x1, x0
    // 0x7bc6e8: b.hs            #0x7bc748
    // 0x7bc6ec: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7bc6ec: ldur            w1, [x2, #0x17]
    // 0x7bc6f0: DecompressPointer r1
    //     0x7bc6f0: add             x1, x1, HEAP, lsl #32
    // 0x7bc6f4: LoadField: r6 = r2->field_1b
    //     0x7bc6f4: ldur            w6, [x2, #0x1b]
    // 0x7bc6f8: r2 = LoadInt32Instr(r6)
    //     0x7bc6f8: sbfx            x2, x6, #1, #0x1f
    // 0x7bc6fc: add             x6, x2, x5
    // 0x7bc700: LoadField: r2 = r1->field_7
    //     0x7bc700: ldur            x2, [x1, #7]
    // 0x7bc704: ldrh            w1, [x2, x6]
    // 0x7bc708: mov             x2, x1
    // 0x7bc70c: ubfx            x2, x2, #0, #0x20
    // 0x7bc710: and             x5, x2, x4
    // 0x7bc714: ubfx            x5, x5, #0, #0x20
    // 0x7bc718: asr             x2, x5, #8
    // 0x7bc71c: ubfx            x1, x1, #0, #0x20
    // 0x7bc720: and             x4, x1, x3
    // 0x7bc724: ubfx            x4, x4, #0, #0x20
    // 0x7bc728: lsl             x1, x4, #8
    // 0x7bc72c: orr             x0, x2, x1
    // 0x7bc730: LeaveFrame
    //     0x7bc730: mov             SP, fp
    //     0x7bc734: ldp             fp, lr, [SP], #0x10
    // 0x7bc738: ret
    //     0x7bc738: ret             
    // 0x7bc73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bc73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bc740: b               #0x7bc668
    // 0x7bc744: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bc744: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bc748: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bc748: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ indexToLocFormat(/* No info */) {
    // ** addr: 0x7bc74c, size: 0x110
    // 0x7bc74c: EnterFrame
    //     0x7bc74c: stp             fp, lr, [SP, #-0x10]!
    //     0x7bc750: mov             fp, SP
    // 0x7bc754: AllocStack(0x10)
    //     0x7bc754: sub             SP, SP, #0x10
    // 0x7bc758: CheckStackOverflow
    //     0x7bc758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bc75c: cmp             SP, x16
    //     0x7bc760: b.ls            #0x7bc84c
    // 0x7bc764: LoadField: r0 = r1->field_7
    //     0x7bc764: ldur            w0, [x1, #7]
    // 0x7bc768: DecompressPointer r0
    //     0x7bc768: add             x0, x0, HEAP, lsl #32
    // 0x7bc76c: stur            x0, [fp, #-0x10]
    // 0x7bc770: LoadField: r3 = r1->field_b
    //     0x7bc770: ldur            w3, [x1, #0xb]
    // 0x7bc774: DecompressPointer r3
    //     0x7bc774: add             x3, x3, HEAP, lsl #32
    // 0x7bc778: mov             x1, x3
    // 0x7bc77c: stur            x3, [fp, #-8]
    // 0x7bc780: r2 = "head"
    //     0x7bc780: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7bc784: ldr             x2, [x2, #0x890]
    // 0x7bc788: r0 = _getValueOrData()
    //     0x7bc788: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7bc78c: ldur            x2, [fp, #-8]
    // 0x7bc790: LoadField: r3 = r2->field_f
    //     0x7bc790: ldur            w3, [x2, #0xf]
    // 0x7bc794: DecompressPointer r3
    //     0x7bc794: add             x3, x3, HEAP, lsl #32
    // 0x7bc798: cmp             w3, w0
    // 0x7bc79c: b.ne            #0x7bc7a8
    // 0x7bc7a0: r7 = Null
    //     0x7bc7a0: mov             x7, NULL
    // 0x7bc7a4: b               #0x7bc7ac
    // 0x7bc7a8: mov             x7, x0
    // 0x7bc7ac: ldur            x2, [fp, #-0x10]
    // 0x7bc7b0: r6 = 65280
    //     0x7bc7b0: orr             x6, xzr, #0xff00
    // 0x7bc7b4: r5 = 255
    //     0x7bc7b4: movz            x5, #0xff
    // 0x7bc7b8: r4 = 32767
    //     0x7bc7b8: orr             x4, xzr, #0x7fff
    // 0x7bc7bc: r3 = 32768
    //     0x7bc7bc: movz            x3, #0x8000
    // 0x7bc7c0: cmp             w7, NULL
    // 0x7bc7c4: b.eq            #0x7bc854
    // 0x7bc7c8: r8 = LoadInt32Instr(r7)
    //     0x7bc7c8: sbfx            x8, x7, #1, #0x1f
    //     0x7bc7cc: tbz             w7, #0, #0x7bc7d4
    //     0x7bc7d0: ldur            x8, [x7, #7]
    // 0x7bc7d4: add             x7, x8, #0x32
    // 0x7bc7d8: LoadField: r8 = r2->field_13
    //     0x7bc7d8: ldur            w8, [x2, #0x13]
    // 0x7bc7dc: r9 = LoadInt32Instr(r8)
    //     0x7bc7dc: sbfx            x9, x8, #1, #0x1f
    // 0x7bc7e0: sub             x0, x9, #1
    // 0x7bc7e4: mov             x1, x7
    // 0x7bc7e8: cmp             x1, x0
    // 0x7bc7ec: b.hs            #0x7bc858
    // 0x7bc7f0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7bc7f0: ldur            w1, [x2, #0x17]
    // 0x7bc7f4: DecompressPointer r1
    //     0x7bc7f4: add             x1, x1, HEAP, lsl #32
    // 0x7bc7f8: LoadField: r8 = r2->field_1b
    //     0x7bc7f8: ldur            w8, [x2, #0x1b]
    // 0x7bc7fc: r2 = LoadInt32Instr(r8)
    //     0x7bc7fc: sbfx            x2, x8, #1, #0x1f
    // 0x7bc800: add             x8, x2, x7
    // 0x7bc804: LoadField: r2 = r1->field_7
    //     0x7bc804: ldur            x2, [x1, #7]
    // 0x7bc808: ldrsh           x1, [x2, x8]
    // 0x7bc80c: mov             x2, x1
    // 0x7bc810: ubfx            x2, x2, #0, #0x20
    // 0x7bc814: and             x7, x2, x6
    // 0x7bc818: lsr             w2, w7, #8
    // 0x7bc81c: ubfx            x1, x1, #0, #0x20
    // 0x7bc820: and             x6, x1, x5
    // 0x7bc824: lsl             w1, w6, #8
    // 0x7bc828: orr             x5, x2, x1
    // 0x7bc82c: and             x1, x5, x4
    // 0x7bc830: and             x2, x5, x3
    // 0x7bc834: ubfx            x1, x1, #0, #0x20
    // 0x7bc838: ubfx            x2, x2, #0, #0x20
    // 0x7bc83c: sub             x0, x1, x2
    // 0x7bc840: LeaveFrame
    //     0x7bc840: mov             SP, fp
    //     0x7bc844: ldp             fp, lr, [SP], #0x10
    // 0x7bc848: ret
    //     0x7bc848: ret             
    // 0x7bc84c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bc84c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bc850: b               #0x7bc764
    // 0x7bc854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bc854: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bc858: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bc858: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ readGlyph(/* No info */) {
    // ** addr: 0x7c85d4, size: 0x19c
    // 0x7c85d4: EnterFrame
    //     0x7c85d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7c85d8: mov             fp, SP
    // 0x7c85dc: AllocStack(0x18)
    //     0x7c85dc: sub             SP, SP, #0x18
    // 0x7c85e0: SetupParameters(TtfParser this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x7c85e0: mov             x3, x1
    //     0x7c85e4: mov             x0, x2
    //     0x7c85e8: stur            x1, [fp, #-0x10]
    //     0x7c85ec: stur            x2, [fp, #-0x18]
    // 0x7c85f0: CheckStackOverflow
    //     0x7c85f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c85f4: cmp             SP, x16
    //     0x7c85f8: b.ls            #0x7c875c
    // 0x7c85fc: LoadField: r4 = r3->field_b
    //     0x7c85fc: ldur            w4, [x3, #0xb]
    // 0x7c8600: DecompressPointer r4
    //     0x7c8600: add             x4, x4, HEAP, lsl #32
    // 0x7c8604: mov             x1, x4
    // 0x7c8608: stur            x4, [fp, #-8]
    // 0x7c860c: r2 = "glyf"
    //     0x7c860c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0x7c8610: ldr             x2, [x2, #0x860]
    // 0x7c8614: r0 = _getValueOrData()
    //     0x7c8614: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7c8618: mov             x1, x0
    // 0x7c861c: ldur            x0, [fp, #-8]
    // 0x7c8620: LoadField: r2 = r0->field_f
    //     0x7c8620: ldur            w2, [x0, #0xf]
    // 0x7c8624: DecompressPointer r2
    //     0x7c8624: add             x2, x2, HEAP, lsl #32
    // 0x7c8628: cmp             w2, w1
    // 0x7c862c: b.ne            #0x7c8638
    // 0x7c8630: r8 = Null
    //     0x7c8630: mov             x8, NULL
    // 0x7c8634: b               #0x7c863c
    // 0x7c8638: mov             x8, x1
    // 0x7c863c: ldur            x3, [fp, #-0x10]
    // 0x7c8640: ldur            x2, [fp, #-0x18]
    // 0x7c8644: r7 = 65280
    //     0x7c8644: orr             x7, xzr, #0xff00
    // 0x7c8648: r6 = 255
    //     0x7c8648: movz            x6, #0xff
    // 0x7c864c: r5 = 32767
    //     0x7c864c: orr             x5, xzr, #0x7fff
    // 0x7c8650: r4 = 32768
    //     0x7c8650: movz            x4, #0x8000
    // 0x7c8654: cmp             w8, NULL
    // 0x7c8658: b.eq            #0x7c8764
    // 0x7c865c: ArrayLoad: r9 = r3[0]  ; List_4
    //     0x7c865c: ldur            w9, [x3, #0x17]
    // 0x7c8660: DecompressPointer r9
    //     0x7c8660: add             x9, x9, HEAP, lsl #32
    // 0x7c8664: LoadField: r0 = r9->field_b
    //     0x7c8664: ldur            w0, [x9, #0xb]
    // 0x7c8668: r1 = LoadInt32Instr(r0)
    //     0x7c8668: sbfx            x1, x0, #1, #0x1f
    // 0x7c866c: mov             x0, x1
    // 0x7c8670: mov             x1, x2
    // 0x7c8674: cmp             x1, x0
    // 0x7c8678: b.hs            #0x7c8768
    // 0x7c867c: LoadField: r0 = r9->field_f
    //     0x7c867c: ldur            w0, [x9, #0xf]
    // 0x7c8680: DecompressPointer r0
    //     0x7c8680: add             x0, x0, HEAP, lsl #32
    // 0x7c8684: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x7c8684: add             x16, x0, x2, lsl #2
    //     0x7c8688: ldur            w1, [x16, #0xf]
    // 0x7c868c: DecompressPointer r1
    //     0x7c868c: add             x1, x1, HEAP, lsl #32
    // 0x7c8690: r0 = LoadInt32Instr(r8)
    //     0x7c8690: sbfx            x0, x8, #1, #0x1f
    //     0x7c8694: tbz             w8, #0, #0x7c869c
    //     0x7c8698: ldur            x0, [x8, #7]
    // 0x7c869c: r8 = LoadInt32Instr(r1)
    //     0x7c869c: sbfx            x8, x1, #1, #0x1f
    //     0x7c86a0: tbz             w1, #0, #0x7c86a8
    //     0x7c86a4: ldur            x8, [x1, #7]
    // 0x7c86a8: add             x9, x0, x8
    // 0x7c86ac: LoadField: r8 = r3->field_7
    //     0x7c86ac: ldur            w8, [x3, #7]
    // 0x7c86b0: DecompressPointer r8
    //     0x7c86b0: add             x8, x8, HEAP, lsl #32
    // 0x7c86b4: LoadField: r0 = r8->field_13
    //     0x7c86b4: ldur            w0, [x8, #0x13]
    // 0x7c86b8: r1 = LoadInt32Instr(r0)
    //     0x7c86b8: sbfx            x1, x0, #1, #0x1f
    // 0x7c86bc: sub             x0, x1, #1
    // 0x7c86c0: mov             x1, x9
    // 0x7c86c4: cmp             x1, x0
    // 0x7c86c8: b.hs            #0x7c876c
    // 0x7c86cc: ArrayLoad: r0 = r8[0]  ; List_4
    //     0x7c86cc: ldur            w0, [x8, #0x17]
    // 0x7c86d0: DecompressPointer r0
    //     0x7c86d0: add             x0, x0, HEAP, lsl #32
    // 0x7c86d4: LoadField: r1 = r8->field_1b
    //     0x7c86d4: ldur            w1, [x8, #0x1b]
    // 0x7c86d8: r8 = LoadInt32Instr(r1)
    //     0x7c86d8: sbfx            x8, x1, #1, #0x1f
    // 0x7c86dc: add             x1, x8, x9
    // 0x7c86e0: LoadField: r8 = r0->field_7
    //     0x7c86e0: ldur            x8, [x0, #7]
    // 0x7c86e4: ldrsh           x0, [x8, x1]
    // 0x7c86e8: mov             x1, x0
    // 0x7c86ec: ubfx            x1, x1, #0, #0x20
    // 0x7c86f0: and             x8, x1, x7
    // 0x7c86f4: lsr             w1, w8, #8
    // 0x7c86f8: ubfx            x0, x0, #0, #0x20
    // 0x7c86fc: and             x7, x0, x6
    // 0x7c8700: lsl             w0, w7, #8
    // 0x7c8704: orr             x6, x1, x0
    // 0x7c8708: and             x0, x6, x5
    // 0x7c870c: and             x1, x6, x4
    // 0x7c8710: ubfx            x0, x0, #0, #0x20
    // 0x7c8714: ubfx            x1, x1, #0, #0x20
    // 0x7c8718: sub             x6, x0, x1
    // 0x7c871c: cmn             x6, #1
    // 0x7c8720: b.ne            #0x7c8740
    // 0x7c8724: add             x5, x9, #0xa
    // 0x7c8728: mov             x1, x3
    // 0x7c872c: mov             x3, x9
    // 0x7c8730: r0 = _readCompoundGlyph()
    //     0x7c8730: bl              #0x7c8ddc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_readCompoundGlyph
    // 0x7c8734: LeaveFrame
    //     0x7c8734: mov             SP, fp
    //     0x7c8738: ldp             fp, lr, [SP], #0x10
    // 0x7c873c: ret
    //     0x7c873c: ret             
    // 0x7c8740: add             x5, x9, #0xa
    // 0x7c8744: mov             x1, x3
    // 0x7c8748: mov             x3, x9
    // 0x7c874c: r0 = _readSimpleGlyph()
    //     0x7c874c: bl              #0x7c8770  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_readSimpleGlyph
    // 0x7c8750: LeaveFrame
    //     0x7c8750: mov             SP, fp
    //     0x7c8754: ldp             fp, lr, [SP], #0x10
    // 0x7c8758: ret
    //     0x7c8758: ret             
    // 0x7c875c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c875c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8760: b               #0x7c85fc
    // 0x7c8764: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c8764: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7c8768: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8768: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c876c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c876c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _readSimpleGlyph(/* No info */) {
    // ** addr: 0x7c8770, size: 0x66c
    // 0x7c8770: EnterFrame
    //     0x7c8770: stp             fp, lr, [SP, #-0x10]!
    //     0x7c8774: mov             fp, SP
    // 0x7c8778: AllocStack(0x88)
    //     0x7c8778: sub             SP, SP, #0x88
    // 0x7c877c: SetupParameters(dynamic _ /* r2 => r2, fp-0x48 */, dynamic _ /* r3 => r3, fp-0x50 */, dynamic _ /* r6 => r6, fp-0x58 */)
    //     0x7c877c: stur            x2, [fp, #-0x48]
    //     0x7c8780: stur            x3, [fp, #-0x50]
    //     0x7c8784: stur            x6, [fp, #-0x58]
    // 0x7c8788: CheckStackOverflow
    //     0x7c8788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c878c: cmp             SP, x16
    //     0x7c8790: b.ls            #0x7c8d94
    // 0x7c8794: LoadField: r4 = r1->field_7
    //     0x7c8794: ldur            w4, [x1, #7]
    // 0x7c8798: DecompressPointer r4
    //     0x7c8798: add             x4, x4, HEAP, lsl #32
    // 0x7c879c: stur            x4, [fp, #-0x40]
    // 0x7c87a0: LoadField: r0 = r4->field_13
    //     0x7c87a0: ldur            w0, [x4, #0x13]
    // 0x7c87a4: r1 = LoadInt32Instr(r0)
    //     0x7c87a4: sbfx            x1, x0, #1, #0x1f
    // 0x7c87a8: sub             x7, x1, #1
    // 0x7c87ac: stur            x7, [fp, #-0x38]
    // 0x7c87b0: ArrayLoad: r8 = r4[0]  ; List_4
    //     0x7c87b0: ldur            w8, [x4, #0x17]
    // 0x7c87b4: DecompressPointer r8
    //     0x7c87b4: add             x8, x8, HEAP, lsl #32
    // 0x7c87b8: stur            x8, [fp, #-0x30]
    // 0x7c87bc: LoadField: r0 = r4->field_1b
    //     0x7c87bc: ldur            w0, [x4, #0x1b]
    // 0x7c87c0: r9 = LoadInt32Instr(r0)
    //     0x7c87c0: sbfx            x9, x0, #1, #0x1f
    // 0x7c87c4: stur            x9, [fp, #-0x28]
    // 0x7c87c8: mov             x13, x5
    // 0x7c87cc: r12 = 1
    //     0x7c87cc: movz            x12, #0x1
    // 0x7c87d0: r11 = 0
    //     0x7c87d0: movz            x11, #0
    // 0x7c87d4: r10 = 65280
    //     0x7c87d4: orr             x10, xzr, #0xff00
    // 0x7c87d8: r5 = 255
    //     0x7c87d8: movz            x5, #0xff
    // 0x7c87dc: stur            x13, [fp, #-0x10]
    // 0x7c87e0: stur            x12, [fp, #-0x18]
    // 0x7c87e4: stur            x11, [fp, #-0x20]
    // 0x7c87e8: CheckStackOverflow
    //     0x7c87e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c87ec: cmp             SP, x16
    //     0x7c87f0: b.ls            #0x7c8d9c
    // 0x7c87f4: cmp             x11, x6
    // 0x7c87f8: b.ge            #0x7c88f8
    // 0x7c87fc: mov             x0, x7
    // 0x7c8800: mov             x1, x13
    // 0x7c8804: cmp             x1, x0
    // 0x7c8808: b.hs            #0x7c8da4
    // 0x7c880c: add             x0, x9, x13
    // 0x7c8810: LoadField: r1 = r8->field_7
    //     0x7c8810: ldur            x1, [x8, #7]
    // 0x7c8814: ldrh            w14, [x1, x0]
    // 0x7c8818: mov             x0, x14
    // 0x7c881c: ubfx            x0, x0, #0, #0x20
    // 0x7c8820: and             x1, x0, x10
    // 0x7c8824: ubfx            x1, x1, #0, #0x20
    // 0x7c8828: asr             x0, x1, #8
    // 0x7c882c: ubfx            x14, x14, #0, #0x20
    // 0x7c8830: and             x1, x14, x5
    // 0x7c8834: ubfx            x1, x1, #0, #0x20
    // 0x7c8838: lsl             x14, x1, #8
    // 0x7c883c: orr             x1, x0, x14
    // 0x7c8840: add             x14, x1, #1
    // 0x7c8844: stur            x14, [fp, #-8]
    // 0x7c8848: cmp             x12, x14
    // 0x7c884c: b.le            #0x7c885c
    // 0x7c8850: mov             x2, x13
    // 0x7c8854: mov             x0, x11
    // 0x7c8858: b               #0x7c88d0
    // 0x7c885c: cmp             x12, x14
    // 0x7c8860: b.ge            #0x7c8874
    // 0x7c8864: mov             x12, x14
    // 0x7c8868: mov             x2, x13
    // 0x7c886c: mov             x0, x11
    // 0x7c8870: b               #0x7c88d0
    // 0x7c8874: cbnz            x14, #0x7c88c4
    // 0x7c8878: r0 = BoxInt64Instr(r12)
    //     0x7c8878: sbfiz           x0, x12, #1, #0x1f
    //     0x7c887c: cmp             x12, x0, asr #1
    //     0x7c8880: b.eq            #0x7c888c
    //     0x7c8884: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c8888: stur            x12, [x0, #7]
    // 0x7c888c: r1 = 60
    //     0x7c888c: movz            x1, #0x3c
    // 0x7c8890: branchIfSmi(r0, 0x7c889c)
    //     0x7c8890: tbz             w0, #0, #0x7c889c
    // 0x7c8894: r1 = LoadClassIdInstr(r0)
    //     0x7c8894: ldur            x1, [x0, #-1]
    //     0x7c8898: ubfx            x1, x1, #0xc, #0x14
    // 0x7c889c: str             x0, [SP]
    // 0x7c88a0: mov             x0, x1
    // 0x7c88a4: r0 = GDT[cid_x0 + -0xfb8]()
    //     0x7c88a4: sub             lr, x0, #0xfb8
    //     0x7c88a8: ldr             lr, [x21, lr, lsl #3]
    //     0x7c88ac: blr             lr
    // 0x7c88b0: tbnz            w0, #4, #0x7c88c4
    // 0x7c88b4: ldur            x12, [fp, #-8]
    // 0x7c88b8: ldur            x2, [fp, #-0x10]
    // 0x7c88bc: ldur            x0, [fp, #-0x20]
    // 0x7c88c0: b               #0x7c88d0
    // 0x7c88c4: ldur            x12, [fp, #-0x18]
    // 0x7c88c8: ldur            x2, [fp, #-0x10]
    // 0x7c88cc: ldur            x0, [fp, #-0x20]
    // 0x7c88d0: add             x13, x2, #2
    // 0x7c88d4: add             x11, x0, #1
    // 0x7c88d8: ldur            x2, [fp, #-0x48]
    // 0x7c88dc: ldur            x3, [fp, #-0x50]
    // 0x7c88e0: ldur            x6, [fp, #-0x58]
    // 0x7c88e4: ldur            x4, [fp, #-0x40]
    // 0x7c88e8: ldur            x7, [fp, #-0x38]
    // 0x7c88ec: ldur            x8, [fp, #-0x30]
    // 0x7c88f0: ldur            x9, [fp, #-0x28]
    // 0x7c88f4: b               #0x7c87d4
    // 0x7c88f8: mov             x3, x6
    // 0x7c88fc: mov             x2, x13
    // 0x7c8900: mov             x6, x10
    // 0x7c8904: LoadField: r0 = r4->field_13
    //     0x7c8904: ldur            w0, [x4, #0x13]
    // 0x7c8908: r7 = LoadInt32Instr(r0)
    //     0x7c8908: sbfx            x7, x0, #1, #0x1f
    // 0x7c890c: stur            x7, [fp, #-0x28]
    // 0x7c8910: sub             x0, x7, #1
    // 0x7c8914: mov             x1, x2
    // 0x7c8918: cmp             x1, x0
    // 0x7c891c: b.hs            #0x7c8da8
    // 0x7c8920: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x7c8920: ldur            w0, [x4, #0x17]
    // 0x7c8924: DecompressPointer r0
    //     0x7c8924: add             x0, x0, HEAP, lsl #32
    // 0x7c8928: stur            x0, [fp, #-0x60]
    // 0x7c892c: LoadField: r1 = r4->field_1b
    //     0x7c892c: ldur            w1, [x4, #0x1b]
    // 0x7c8930: r8 = LoadInt32Instr(r1)
    //     0x7c8930: sbfx            x8, x1, #1, #0x1f
    // 0x7c8934: stur            x8, [fp, #-0x20]
    // 0x7c8938: add             x1, x8, x2
    // 0x7c893c: LoadField: r9 = r0->field_7
    //     0x7c893c: ldur            x9, [x0, #7]
    // 0x7c8940: ldrh            w10, [x9, x1]
    // 0x7c8944: mov             x1, x10
    // 0x7c8948: ubfx            x1, x1, #0, #0x20
    // 0x7c894c: and             x9, x1, x6
    // 0x7c8950: ubfx            x9, x9, #0, #0x20
    // 0x7c8954: asr             x1, x9, #8
    // 0x7c8958: ubfx            x10, x10, #0, #0x20
    // 0x7c895c: and             x6, x10, x5
    // 0x7c8960: ubfx            x6, x6, #0, #0x20
    // 0x7c8964: lsl             x5, x6, #8
    // 0x7c8968: orr             x6, x1, x5
    // 0x7c896c: add             x1, x6, #2
    // 0x7c8970: add             x5, x2, x1
    // 0x7c8974: stur            x5, [fp, #-8]
    // 0x7c8978: cbnz            x3, #0x7c8a2c
    // 0x7c897c: ldur            x3, [fp, #-0x48]
    // 0x7c8980: ldur            x2, [fp, #-0x50]
    // 0x7c8984: r0 = LoadClassIdInstr(r4)
    //     0x7c8984: ldur            x0, [x4, #-1]
    //     0x7c8988: ubfx            x0, x0, #0xc, #0x14
    // 0x7c898c: mov             x1, x4
    // 0x7c8990: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7c8990: sub             lr, x0, #0xf60
    //     0x7c8994: ldr             lr, [x21, lr, lsl #3]
    //     0x7c8998: blr             lr
    // 0x7c899c: mov             x2, x0
    // 0x7c89a0: ldur            x5, [fp, #-0x50]
    // 0x7c89a4: ldur            x3, [fp, #-8]
    // 0x7c89a8: sub             x4, x3, x5
    // 0x7c89ac: r0 = BoxInt64Instr(r5)
    //     0x7c89ac: sbfiz           x0, x5, #1, #0x1f
    //     0x7c89b0: cmp             x5, x0, asr #1
    //     0x7c89b4: b.eq            #0x7c89c0
    //     0x7c89b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c89bc: stur            x5, [x0, #7]
    // 0x7c89c0: mov             x3, x0
    // 0x7c89c4: r0 = BoxInt64Instr(r4)
    //     0x7c89c4: sbfiz           x0, x4, #1, #0x1f
    //     0x7c89c8: cmp             x4, x0, asr #1
    //     0x7c89cc: b.eq            #0x7c89d8
    //     0x7c89d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c89d4: stur            x4, [x0, #7]
    // 0x7c89d8: r1 = LoadClassIdInstr(r2)
    //     0x7c89d8: ldur            x1, [x2, #-1]
    //     0x7c89dc: ubfx            x1, x1, #0xc, #0x14
    // 0x7c89e0: stp             x0, x3, [SP]
    // 0x7c89e4: mov             x0, x1
    // 0x7c89e8: mov             x1, x2
    // 0x7c89ec: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7c89ec: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7c89f0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7c89f0: sub             lr, x0, #1, lsl #12
    //     0x7c89f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c89f8: blr             lr
    // 0x7c89fc: stur            x0, [fp, #-0x30]
    // 0x7c8a00: r0 = TtfGlyphInfo()
    //     0x7c8a00: bl              #0x7bcae4  ; AllocateTtfGlyphInfoStub -> TtfGlyphInfo (size=0x18)
    // 0x7c8a04: ldur            x6, [fp, #-0x48]
    // 0x7c8a08: StoreField: r0->field_7 = r6
    //     0x7c8a08: stur            x6, [x0, #7]
    // 0x7c8a0c: ldur            x1, [fp, #-0x30]
    // 0x7c8a10: StoreField: r0->field_f = r1
    //     0x7c8a10: stur            w1, [x0, #0xf]
    // 0x7c8a14: r9 = const []
    //     0x7c8a14: add             x9, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0x7c8a18: ldr             x9, [x9, #0x8c0]
    // 0x7c8a1c: StoreField: r0->field_13 = r9
    //     0x7c8a1c: stur            w9, [x0, #0x13]
    // 0x7c8a20: LeaveFrame
    //     0x7c8a20: mov             SP, fp
    //     0x7c8a24: ldp             fp, lr, [SP], #0x10
    // 0x7c8a28: ret
    //     0x7c8a28: ret             
    // 0x7c8a2c: ldur            x6, [fp, #-0x48]
    // 0x7c8a30: mov             x3, x5
    // 0x7c8a34: ldur            x5, [fp, #-0x50]
    // 0x7c8a38: r9 = const []
    //     0x7c8a38: add             x9, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0x7c8a3c: ldr             x9, [x9, #0x8c0]
    // 0x7c8a40: r1 = <int>
    //     0x7c8a40: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7c8a44: r2 = 0
    //     0x7c8a44: movz            x2, #0
    // 0x7c8a48: r0 = _GrowableList()
    //     0x7c8a48: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7c8a4c: mov             x2, x0
    // 0x7c8a50: stur            x2, [fp, #-0x68]
    // 0x7c8a54: ldur            x7, [fp, #-8]
    // 0x7c8a58: r6 = 0
    //     0x7c8a58: movz            x6, #0
    // 0x7c8a5c: ldur            x5, [fp, #-0x18]
    // 0x7c8a60: ldur            x3, [fp, #-0x60]
    // 0x7c8a64: ldur            x4, [fp, #-0x20]
    // 0x7c8a68: stur            x6, [fp, #-0x58]
    // 0x7c8a6c: CheckStackOverflow
    //     0x7c8a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8a70: cmp             SP, x16
    //     0x7c8a74: b.ls            #0x7c8dac
    // 0x7c8a78: cmp             x6, x5
    // 0x7c8a7c: b.ge            #0x7c8c10
    // 0x7c8a80: add             x8, x7, #1
    // 0x7c8a84: ldur            x0, [fp, #-0x28]
    // 0x7c8a88: mov             x1, x7
    // 0x7c8a8c: stur            x8, [fp, #-0x38]
    // 0x7c8a90: cmp             x1, x0
    // 0x7c8a94: b.hs            #0x7c8db4
    // 0x7c8a98: add             x0, x4, x7
    // 0x7c8a9c: LoadField: r1 = r3->field_7
    //     0x7c8a9c: ldur            x1, [x3, #7]
    // 0x7c8aa0: ldrb            w7, [x1, x0]
    // 0x7c8aa4: stur            x7, [fp, #-0x10]
    // 0x7c8aa8: lsl             x0, x7, #1
    // 0x7c8aac: stur            x0, [fp, #-0x30]
    // 0x7c8ab0: LoadField: r1 = r2->field_b
    //     0x7c8ab0: ldur            w1, [x2, #0xb]
    // 0x7c8ab4: LoadField: r9 = r2->field_f
    //     0x7c8ab4: ldur            w9, [x2, #0xf]
    // 0x7c8ab8: DecompressPointer r9
    //     0x7c8ab8: add             x9, x9, HEAP, lsl #32
    // 0x7c8abc: LoadField: r10 = r9->field_b
    //     0x7c8abc: ldur            w10, [x9, #0xb]
    // 0x7c8ac0: r9 = LoadInt32Instr(r1)
    //     0x7c8ac0: sbfx            x9, x1, #1, #0x1f
    // 0x7c8ac4: stur            x9, [fp, #-8]
    // 0x7c8ac8: r1 = LoadInt32Instr(r10)
    //     0x7c8ac8: sbfx            x1, x10, #1, #0x1f
    // 0x7c8acc: cmp             x9, x1
    // 0x7c8ad0: b.ne            #0x7c8adc
    // 0x7c8ad4: mov             x1, x2
    // 0x7c8ad8: r0 = _growToNextCapacity()
    //     0x7c8ad8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7c8adc: ldur            x2, [fp, #-0x68]
    // 0x7c8ae0: ldur            x3, [fp, #-0x30]
    // 0x7c8ae4: ldur            x1, [fp, #-8]
    // 0x7c8ae8: ldur            x0, [fp, #-0x10]
    // 0x7c8aec: add             x4, x1, #1
    // 0x7c8af0: lsl             x5, x4, #1
    // 0x7c8af4: StoreField: r2->field_b = r5
    //     0x7c8af4: stur            w5, [x2, #0xb]
    // 0x7c8af8: LoadField: r5 = r2->field_f
    //     0x7c8af8: ldur            w5, [x2, #0xf]
    // 0x7c8afc: DecompressPointer r5
    //     0x7c8afc: add             x5, x5, HEAP, lsl #32
    // 0x7c8b00: ArrayStore: r5[r1] = r3  ; Unknown_4
    //     0x7c8b00: add             x6, x5, x1, lsl #2
    //     0x7c8b04: stur            w3, [x6, #0xf]
    // 0x7c8b08: tbz             w0, #3, #0x7c8bf8
    // 0x7c8b0c: ldur            x8, [fp, #-0x58]
    // 0x7c8b10: ldur            x9, [fp, #-0x38]
    // 0x7c8b14: ldur            x6, [fp, #-0x60]
    // 0x7c8b18: ldur            x7, [fp, #-0x20]
    // 0x7c8b1c: add             x10, x9, #1
    // 0x7c8b20: ldur            x0, [fp, #-0x28]
    // 0x7c8b24: mov             x1, x9
    // 0x7c8b28: stur            x10, [fp, #-0x78]
    // 0x7c8b2c: cmp             x1, x0
    // 0x7c8b30: b.hs            #0x7c8db8
    // 0x7c8b34: add             x0, x7, x9
    // 0x7c8b38: LoadField: r1 = r6->field_7
    //     0x7c8b38: ldur            x1, [x6, #7]
    // 0x7c8b3c: ldrb            w9, [x1, x0]
    // 0x7c8b40: add             x0, x8, x9
    // 0x7c8b44: stur            x0, [fp, #-0x70]
    // 0x7c8b48: mov             x1, x5
    // 0x7c8b4c: mov             x5, x9
    // 0x7c8b50: stur            x4, [fp, #-0x10]
    // 0x7c8b54: CheckStackOverflow
    //     0x7c8b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8b58: cmp             SP, x16
    //     0x7c8b5c: b.ls            #0x7c8dbc
    // 0x7c8b60: sub             x8, x5, #1
    // 0x7c8b64: stur            x8, [fp, #-8]
    // 0x7c8b68: cmp             x5, #0
    // 0x7c8b6c: b.le            #0x7c8bec
    // 0x7c8b70: LoadField: r5 = r1->field_b
    //     0x7c8b70: ldur            w5, [x1, #0xb]
    // 0x7c8b74: r1 = LoadInt32Instr(r5)
    //     0x7c8b74: sbfx            x1, x5, #1, #0x1f
    // 0x7c8b78: cmp             x4, x1
    // 0x7c8b7c: b.ne            #0x7c8b88
    // 0x7c8b80: mov             x1, x2
    // 0x7c8b84: r0 = _growToNextCapacity()
    //     0x7c8b84: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7c8b88: ldur            x2, [fp, #-0x68]
    // 0x7c8b8c: ldur            x3, [fp, #-0x30]
    // 0x7c8b90: ldur            x4, [fp, #-0x10]
    // 0x7c8b94: add             x6, x4, #1
    // 0x7c8b98: r0 = BoxInt64Instr(r6)
    //     0x7c8b98: sbfiz           x0, x6, #1, #0x1f
    //     0x7c8b9c: cmp             x6, x0, asr #1
    //     0x7c8ba0: b.eq            #0x7c8bac
    //     0x7c8ba4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c8ba8: stur            x6, [x0, #7]
    // 0x7c8bac: StoreField: r2->field_b = r0
    //     0x7c8bac: stur            w0, [x2, #0xb]
    // 0x7c8bb0: mov             x0, x6
    // 0x7c8bb4: mov             x1, x4
    // 0x7c8bb8: cmp             x1, x0
    // 0x7c8bbc: b.hs            #0x7c8dc4
    // 0x7c8bc0: LoadField: r1 = r2->field_f
    //     0x7c8bc0: ldur            w1, [x2, #0xf]
    // 0x7c8bc4: DecompressPointer r1
    //     0x7c8bc4: add             x1, x1, HEAP, lsl #32
    // 0x7c8bc8: ArrayStore: r1[r4] = r3  ; Unknown_4
    //     0x7c8bc8: add             x0, x1, x4, lsl #2
    //     0x7c8bcc: stur            w3, [x0, #0xf]
    // 0x7c8bd0: ldur            x5, [fp, #-8]
    // 0x7c8bd4: mov             x4, x6
    // 0x7c8bd8: ldur            x10, [fp, #-0x78]
    // 0x7c8bdc: ldur            x0, [fp, #-0x70]
    // 0x7c8be0: ldur            x6, [fp, #-0x60]
    // 0x7c8be4: ldur            x7, [fp, #-0x20]
    // 0x7c8be8: b               #0x7c8b50
    // 0x7c8bec: ldur            x7, [fp, #-0x78]
    // 0x7c8bf0: ldur            x0, [fp, #-0x70]
    // 0x7c8bf4: b               #0x7c8c08
    // 0x7c8bf8: ldur            x8, [fp, #-0x58]
    // 0x7c8bfc: ldur            x9, [fp, #-0x38]
    // 0x7c8c00: mov             x7, x9
    // 0x7c8c04: mov             x0, x8
    // 0x7c8c08: add             x6, x0, #1
    // 0x7c8c0c: b               #0x7c8a5c
    // 0x7c8c10: LoadField: r0 = r2->field_b
    //     0x7c8c10: ldur            w0, [x2, #0xb]
    // 0x7c8c14: r3 = LoadInt32Instr(r0)
    //     0x7c8c14: sbfx            x3, x0, #1, #0x1f
    // 0x7c8c18: LoadField: r4 = r2->field_f
    //     0x7c8c18: ldur            w4, [x2, #0xf]
    // 0x7c8c1c: DecompressPointer r4
    //     0x7c8c1c: add             x4, x4, HEAP, lsl #32
    // 0x7c8c20: mov             x8, x7
    // 0x7c8c24: ldur            x2, [fp, #-0x18]
    // 0x7c8c28: r7 = 2
    //     0x7c8c28: movz            x7, #0x2
    // 0x7c8c2c: r6 = 16
    //     0x7c8c2c: movz            x6, #0x10
    // 0x7c8c30: r5 = 0
    //     0x7c8c30: movz            x5, #0
    // 0x7c8c34: stur            x8, [fp, #-8]
    // 0x7c8c38: CheckStackOverflow
    //     0x7c8c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8c3c: cmp             SP, x16
    //     0x7c8c40: b.ls            #0x7c8dc8
    // 0x7c8c44: cmp             x5, #2
    // 0x7c8c48: b.ge            #0x7c8ce4
    // 0x7c8c4c: mov             x9, x8
    // 0x7c8c50: r8 = 0
    //     0x7c8c50: movz            x8, #0
    // 0x7c8c54: CheckStackOverflow
    //     0x7c8c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8c58: cmp             SP, x16
    //     0x7c8c5c: b.ls            #0x7c8dd0
    // 0x7c8c60: cmp             x8, x2
    // 0x7c8c64: b.ge            #0x7c8ccc
    // 0x7c8c68: mov             x0, x3
    // 0x7c8c6c: mov             x1, x8
    // 0x7c8c70: cmp             x1, x0
    // 0x7c8c74: b.hs            #0x7c8dd8
    // 0x7c8c78: ArrayLoad: r0 = r4[r8]  ; Unknown_4
    //     0x7c8c78: add             x16, x4, x8, lsl #2
    //     0x7c8c7c: ldur            w0, [x16, #0xf]
    // 0x7c8c80: DecompressPointer r0
    //     0x7c8c80: add             x0, x0, HEAP, lsl #32
    // 0x7c8c84: r1 = LoadInt32Instr(r0)
    //     0x7c8c84: sbfx            x1, x0, #1, #0x1f
    //     0x7c8c88: tbz             w0, #0, #0x7c8c90
    //     0x7c8c8c: ldur            x1, [x0, #7]
    // 0x7c8c90: tst             x1, x7
    // 0x7c8c94: b.eq            #0x7c8ca4
    // 0x7c8c98: add             x0, x9, #1
    // 0x7c8c9c: mov             x9, x0
    // 0x7c8ca0: b               #0x7c8cc0
    // 0x7c8ca4: mvn             x0, x1
    // 0x7c8ca8: tst             x0, x6
    // 0x7c8cac: b.eq            #0x7c8cb8
    // 0x7c8cb0: add             x0, x9, #2
    // 0x7c8cb4: b               #0x7c8cbc
    // 0x7c8cb8: mov             x0, x9
    // 0x7c8cbc: mov             x9, x0
    // 0x7c8cc0: add             x0, x8, #1
    // 0x7c8cc4: mov             x8, x0
    // 0x7c8cc8: b               #0x7c8c54
    // 0x7c8ccc: add             x0, x5, #1
    // 0x7c8cd0: mov             x8, x9
    // 0x7c8cd4: mov             x5, x0
    // 0x7c8cd8: r7 = 4
    //     0x7c8cd8: movz            x7, #0x4
    // 0x7c8cdc: r6 = 32
    //     0x7c8cdc: movz            x6, #0x20
    // 0x7c8ce0: b               #0x7c8c34
    // 0x7c8ce4: ldur            x3, [fp, #-0x48]
    // 0x7c8ce8: ldur            x2, [fp, #-0x50]
    // 0x7c8cec: ldur            x1, [fp, #-0x40]
    // 0x7c8cf0: r0 = LoadClassIdInstr(r1)
    //     0x7c8cf0: ldur            x0, [x1, #-1]
    //     0x7c8cf4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c8cf8: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7c8cf8: sub             lr, x0, #0xf60
    //     0x7c8cfc: ldr             lr, [x21, lr, lsl #3]
    //     0x7c8d00: blr             lr
    // 0x7c8d04: mov             x3, x0
    // 0x7c8d08: ldur            x2, [fp, #-0x50]
    // 0x7c8d0c: ldur            x0, [fp, #-8]
    // 0x7c8d10: sub             x4, x0, x2
    // 0x7c8d14: r0 = BoxInt64Instr(r2)
    //     0x7c8d14: sbfiz           x0, x2, #1, #0x1f
    //     0x7c8d18: cmp             x2, x0, asr #1
    //     0x7c8d1c: b.eq            #0x7c8d28
    //     0x7c8d20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c8d24: stur            x2, [x0, #7]
    // 0x7c8d28: mov             x2, x0
    // 0x7c8d2c: r0 = BoxInt64Instr(r4)
    //     0x7c8d2c: sbfiz           x0, x4, #1, #0x1f
    //     0x7c8d30: cmp             x4, x0, asr #1
    //     0x7c8d34: b.eq            #0x7c8d40
    //     0x7c8d38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c8d3c: stur            x4, [x0, #7]
    // 0x7c8d40: r1 = LoadClassIdInstr(r3)
    //     0x7c8d40: ldur            x1, [x3, #-1]
    //     0x7c8d44: ubfx            x1, x1, #0xc, #0x14
    // 0x7c8d48: stp             x0, x2, [SP]
    // 0x7c8d4c: mov             x0, x1
    // 0x7c8d50: mov             x1, x3
    // 0x7c8d54: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7c8d54: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7c8d58: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7c8d58: sub             lr, x0, #1, lsl #12
    //     0x7c8d5c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c8d60: blr             lr
    // 0x7c8d64: stur            x0, [fp, #-0x30]
    // 0x7c8d68: r0 = TtfGlyphInfo()
    //     0x7c8d68: bl              #0x7bcae4  ; AllocateTtfGlyphInfoStub -> TtfGlyphInfo (size=0x18)
    // 0x7c8d6c: ldur            x1, [fp, #-0x48]
    // 0x7c8d70: StoreField: r0->field_7 = r1
    //     0x7c8d70: stur            x1, [x0, #7]
    // 0x7c8d74: ldur            x1, [fp, #-0x30]
    // 0x7c8d78: StoreField: r0->field_f = r1
    //     0x7c8d78: stur            w1, [x0, #0xf]
    // 0x7c8d7c: r1 = const []
    //     0x7c8d7c: add             x1, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0x7c8d80: ldr             x1, [x1, #0x8c0]
    // 0x7c8d84: StoreField: r0->field_13 = r1
    //     0x7c8d84: stur            w1, [x0, #0x13]
    // 0x7c8d88: LeaveFrame
    //     0x7c8d88: mov             SP, fp
    //     0x7c8d8c: ldp             fp, lr, [SP], #0x10
    // 0x7c8d90: ret
    //     0x7c8d90: ret             
    // 0x7c8d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8d94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8d98: b               #0x7c8794
    // 0x7c8d9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8d9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8da0: b               #0x7c87f4
    // 0x7c8da4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8da4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c8da8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8da8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c8dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8dac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8db0: b               #0x7c8a78
    // 0x7c8db4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8db4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c8db8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8db8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c8dbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8dbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8dc0: b               #0x7c8b60
    // 0x7c8dc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8dc4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c8dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8dc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8dcc: b               #0x7c8c44
    // 0x7c8dd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8dd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8dd4: b               #0x7c8c60
    // 0x7c8dd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c8dd8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _readCompoundGlyph(/* No info */) {
    // ** addr: 0x7c8ddc, size: 0x354
    // 0x7c8ddc: EnterFrame
    //     0x7c8ddc: stp             fp, lr, [SP, #-0x10]!
    //     0x7c8de0: mov             fp, SP
    // 0x7c8de4: AllocStack(0x70)
    //     0x7c8de4: sub             SP, SP, #0x70
    // 0x7c8de8: SetupParameters(TtfParser this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x7c8de8: mov             x4, x1
    //     0x7c8dec: mov             x0, x2
    //     0x7c8df0: stur            x1, [fp, #-8]
    //     0x7c8df4: stur            x2, [fp, #-0x10]
    //     0x7c8df8: stur            x3, [fp, #-0x18]
    //     0x7c8dfc: stur            x5, [fp, #-0x20]
    // 0x7c8e00: CheckStackOverflow
    //     0x7c8e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8e04: cmp             SP, x16
    //     0x7c8e08: b.ls            #0x7c9114
    // 0x7c8e0c: r1 = <int>
    //     0x7c8e0c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7c8e10: r2 = 0
    //     0x7c8e10: movz            x2, #0
    // 0x7c8e14: r0 = _GrowableList()
    //     0x7c8e14: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7c8e18: mov             x2, x0
    // 0x7c8e1c: ldur            x0, [fp, #-8]
    // 0x7c8e20: stur            x2, [fp, #-0x60]
    // 0x7c8e24: LoadField: r3 = r0->field_7
    //     0x7c8e24: ldur            w3, [x0, #7]
    // 0x7c8e28: DecompressPointer r3
    //     0x7c8e28: add             x3, x3, HEAP, lsl #32
    // 0x7c8e2c: stur            x3, [fp, #-0x58]
    // 0x7c8e30: LoadField: r0 = r3->field_13
    //     0x7c8e30: ldur            w0, [x3, #0x13]
    // 0x7c8e34: r1 = LoadInt32Instr(r0)
    //     0x7c8e34: sbfx            x1, x0, #1, #0x1f
    // 0x7c8e38: sub             x4, x1, #1
    // 0x7c8e3c: stur            x4, [fp, #-0x50]
    // 0x7c8e40: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x7c8e40: ldur            w5, [x3, #0x17]
    // 0x7c8e44: DecompressPointer r5
    //     0x7c8e44: add             x5, x5, HEAP, lsl #32
    // 0x7c8e48: stur            x5, [fp, #-0x48]
    // 0x7c8e4c: LoadField: r0 = r3->field_1b
    //     0x7c8e4c: ldur            w0, [x3, #0x1b]
    // 0x7c8e50: r6 = LoadInt32Instr(r0)
    //     0x7c8e50: sbfx            x6, x0, #1, #0x1f
    // 0x7c8e54: stur            x6, [fp, #-0x40]
    // 0x7c8e58: ldur            x10, [fp, #-0x20]
    // 0x7c8e5c: r9 = false
    //     0x7c8e5c: add             x9, NULL, #0x30  ; false
    // 0x7c8e60: r0 = 32
    //     0x7c8e60: movz            x0, #0x20
    // 0x7c8e64: r8 = 65280
    //     0x7c8e64: orr             x8, xzr, #0xff00
    // 0x7c8e68: r7 = 255
    //     0x7c8e68: movz            x7, #0xff
    // 0x7c8e6c: stur            x9, [fp, #-8]
    // 0x7c8e70: CheckStackOverflow
    //     0x7c8e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8e74: cmp             SP, x16
    //     0x7c8e78: b.ls            #0x7c911c
    // 0x7c8e7c: tbz             w0, #5, #0x7c8fdc
    // 0x7c8e80: mov             x0, x4
    // 0x7c8e84: mov             x1, x10
    // 0x7c8e88: cmp             x1, x0
    // 0x7c8e8c: b.hs            #0x7c9124
    // 0x7c8e90: add             x0, x6, x10
    // 0x7c8e94: LoadField: r1 = r5->field_7
    //     0x7c8e94: ldur            x1, [x5, #7]
    // 0x7c8e98: ldrh            w11, [x1, x0]
    // 0x7c8e9c: mov             x0, x11
    // 0x7c8ea0: ubfx            x0, x0, #0, #0x20
    // 0x7c8ea4: and             x1, x0, x8
    // 0x7c8ea8: ubfx            x1, x1, #0, #0x20
    // 0x7c8eac: asr             x0, x1, #8
    // 0x7c8eb0: ubfx            x11, x11, #0, #0x20
    // 0x7c8eb4: and             x1, x11, x7
    // 0x7c8eb8: ubfx            x1, x1, #0, #0x20
    // 0x7c8ebc: lsl             x11, x1, #8
    // 0x7c8ec0: orr             x12, x0, x11
    // 0x7c8ec4: stur            x12, [fp, #-0x38]
    // 0x7c8ec8: add             x11, x10, #2
    // 0x7c8ecc: mov             x0, x4
    // 0x7c8ed0: mov             x1, x11
    // 0x7c8ed4: cmp             x1, x0
    // 0x7c8ed8: b.hs            #0x7c9128
    // 0x7c8edc: add             x0, x6, x11
    // 0x7c8ee0: LoadField: r1 = r5->field_7
    //     0x7c8ee0: ldur            x1, [x5, #7]
    // 0x7c8ee4: ldrh            w11, [x1, x0]
    // 0x7c8ee8: mov             x0, x11
    // 0x7c8eec: ubfx            x0, x0, #0, #0x20
    // 0x7c8ef0: and             x1, x0, x8
    // 0x7c8ef4: ubfx            x1, x1, #0, #0x20
    // 0x7c8ef8: asr             x0, x1, #8
    // 0x7c8efc: ubfx            x11, x11, #0, #0x20
    // 0x7c8f00: and             x1, x11, x7
    // 0x7c8f04: ubfx            x1, x1, #0, #0x20
    // 0x7c8f08: lsl             x11, x1, #8
    // 0x7c8f0c: orr             x13, x0, x11
    // 0x7c8f10: stur            x13, [fp, #-0x30]
    // 0x7c8f14: branchIfSmi(r12, 0x7c8f20)
    //     0x7c8f14: tbz             w12, #0, #0x7c8f20
    // 0x7c8f18: r0 = 8
    //     0x7c8f18: movz            x0, #0x8
    // 0x7c8f1c: b               #0x7c8f24
    // 0x7c8f20: r0 = 6
    //     0x7c8f20: movz            x0, #0x6
    // 0x7c8f24: add             x1, x10, x0
    // 0x7c8f28: tbz             w12, #3, #0x7c8f34
    // 0x7c8f2c: add             x10, x1, #2
    // 0x7c8f30: b               #0x7c8f54
    // 0x7c8f34: tbz             w12, #6, #0x7c8f44
    // 0x7c8f38: add             x10, x1, #4
    // 0x7c8f3c: mov             x1, x10
    // 0x7c8f40: b               #0x7c8f50
    // 0x7c8f44: tbz             w12, #7, #0x7c8f50
    // 0x7c8f48: add             x10, x1, #8
    // 0x7c8f4c: mov             x1, x10
    // 0x7c8f50: mov             x10, x1
    // 0x7c8f54: stur            x10, [fp, #-0x28]
    // 0x7c8f58: LoadField: r0 = r2->field_b
    //     0x7c8f58: ldur            w0, [x2, #0xb]
    // 0x7c8f5c: LoadField: r1 = r2->field_f
    //     0x7c8f5c: ldur            w1, [x2, #0xf]
    // 0x7c8f60: DecompressPointer r1
    //     0x7c8f60: add             x1, x1, HEAP, lsl #32
    // 0x7c8f64: LoadField: r11 = r1->field_b
    //     0x7c8f64: ldur            w11, [x1, #0xb]
    // 0x7c8f68: r14 = LoadInt32Instr(r0)
    //     0x7c8f68: sbfx            x14, x0, #1, #0x1f
    // 0x7c8f6c: stur            x14, [fp, #-0x20]
    // 0x7c8f70: r0 = LoadInt32Instr(r11)
    //     0x7c8f70: sbfx            x0, x11, #1, #0x1f
    // 0x7c8f74: cmp             x14, x0
    // 0x7c8f78: b.ne            #0x7c8f84
    // 0x7c8f7c: mov             x1, x2
    // 0x7c8f80: r0 = _growToNextCapacity()
    //     0x7c8f80: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7c8f84: ldur            x2, [fp, #-0x60]
    // 0x7c8f88: ldur            x0, [fp, #-0x38]
    // 0x7c8f8c: ldur            x1, [fp, #-0x30]
    // 0x7c8f90: ldur            x3, [fp, #-0x20]
    // 0x7c8f94: add             x4, x3, #1
    // 0x7c8f98: lsl             x5, x4, #1
    // 0x7c8f9c: StoreField: r2->field_b = r5
    //     0x7c8f9c: stur            w5, [x2, #0xb]
    // 0x7c8fa0: LoadField: r4 = r2->field_f
    //     0x7c8fa0: ldur            w4, [x2, #0xf]
    // 0x7c8fa4: DecompressPointer r4
    //     0x7c8fa4: add             x4, x4, HEAP, lsl #32
    // 0x7c8fa8: lsl             x5, x1, #1
    // 0x7c8fac: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0x7c8fac: add             x1, x4, x3, lsl #2
    //     0x7c8fb0: stur            w5, [x1, #0xf]
    // 0x7c8fb4: tbz             w0, #8, #0x7c8fc0
    // 0x7c8fb8: r9 = true
    //     0x7c8fb8: add             x9, NULL, #0x20  ; true
    // 0x7c8fbc: b               #0x7c8fc4
    // 0x7c8fc0: ldur            x9, [fp, #-8]
    // 0x7c8fc4: ldur            x10, [fp, #-0x28]
    // 0x7c8fc8: ldur            x3, [fp, #-0x58]
    // 0x7c8fcc: ldur            x4, [fp, #-0x50]
    // 0x7c8fd0: ldur            x5, [fp, #-0x48]
    // 0x7c8fd4: ldur            x6, [fp, #-0x40]
    // 0x7c8fd8: b               #0x7c8e64
    // 0x7c8fdc: mov             x0, x9
    // 0x7c8fe0: tbnz            w0, #4, #0x7c905c
    // 0x7c8fe4: ldur            x3, [fp, #-0x58]
    // 0x7c8fe8: r5 = 65280
    //     0x7c8fe8: orr             x5, xzr, #0xff00
    // 0x7c8fec: r4 = 255
    //     0x7c8fec: movz            x4, #0xff
    // 0x7c8ff0: LoadField: r0 = r3->field_13
    //     0x7c8ff0: ldur            w0, [x3, #0x13]
    // 0x7c8ff4: r1 = LoadInt32Instr(r0)
    //     0x7c8ff4: sbfx            x1, x0, #1, #0x1f
    // 0x7c8ff8: sub             x0, x1, #1
    // 0x7c8ffc: mov             x1, x10
    // 0x7c9000: cmp             x1, x0
    // 0x7c9004: b.hs            #0x7c912c
    // 0x7c9008: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x7c9008: ldur            w0, [x3, #0x17]
    // 0x7c900c: DecompressPointer r0
    //     0x7c900c: add             x0, x0, HEAP, lsl #32
    // 0x7c9010: LoadField: r1 = r3->field_1b
    //     0x7c9010: ldur            w1, [x3, #0x1b]
    // 0x7c9014: r6 = LoadInt32Instr(r1)
    //     0x7c9014: sbfx            x6, x1, #1, #0x1f
    // 0x7c9018: add             x1, x6, x10
    // 0x7c901c: LoadField: r6 = r0->field_7
    //     0x7c901c: ldur            x6, [x0, #7]
    // 0x7c9020: ldrh            w0, [x6, x1]
    // 0x7c9024: mov             x1, x0
    // 0x7c9028: ubfx            x1, x1, #0, #0x20
    // 0x7c902c: and             x6, x1, x5
    // 0x7c9030: ubfx            x6, x6, #0, #0x20
    // 0x7c9034: asr             x1, x6, #8
    // 0x7c9038: ubfx            x0, x0, #0, #0x20
    // 0x7c903c: and             x5, x0, x4
    // 0x7c9040: ubfx            x5, x5, #0, #0x20
    // 0x7c9044: lsl             x0, x5, #8
    // 0x7c9048: orr             x4, x1, x0
    // 0x7c904c: add             x0, x4, #2
    // 0x7c9050: add             x1, x10, x0
    // 0x7c9054: mov             x6, x1
    // 0x7c9058: b               #0x7c9064
    // 0x7c905c: ldur            x3, [fp, #-0x58]
    // 0x7c9060: mov             x6, x10
    // 0x7c9064: ldur            x5, [fp, #-0x10]
    // 0x7c9068: ldur            x4, [fp, #-0x18]
    // 0x7c906c: stur            x6, [fp, #-0x20]
    // 0x7c9070: r0 = LoadClassIdInstr(r3)
    //     0x7c9070: ldur            x0, [x3, #-1]
    //     0x7c9074: ubfx            x0, x0, #0xc, #0x14
    // 0x7c9078: mov             x1, x3
    // 0x7c907c: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7c907c: sub             lr, x0, #0xf60
    //     0x7c9080: ldr             lr, [x21, lr, lsl #3]
    //     0x7c9084: blr             lr
    // 0x7c9088: mov             x3, x0
    // 0x7c908c: ldur            x2, [fp, #-0x18]
    // 0x7c9090: ldur            x0, [fp, #-0x20]
    // 0x7c9094: sub             x4, x0, x2
    // 0x7c9098: r0 = BoxInt64Instr(r2)
    //     0x7c9098: sbfiz           x0, x2, #1, #0x1f
    //     0x7c909c: cmp             x2, x0, asr #1
    //     0x7c90a0: b.eq            #0x7c90ac
    //     0x7c90a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c90a8: stur            x2, [x0, #7]
    // 0x7c90ac: mov             x2, x0
    // 0x7c90b0: r0 = BoxInt64Instr(r4)
    //     0x7c90b0: sbfiz           x0, x4, #1, #0x1f
    //     0x7c90b4: cmp             x4, x0, asr #1
    //     0x7c90b8: b.eq            #0x7c90c4
    //     0x7c90bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c90c0: stur            x4, [x0, #7]
    // 0x7c90c4: r1 = LoadClassIdInstr(r3)
    //     0x7c90c4: ldur            x1, [x3, #-1]
    //     0x7c90c8: ubfx            x1, x1, #0xc, #0x14
    // 0x7c90cc: stp             x0, x2, [SP]
    // 0x7c90d0: mov             x0, x1
    // 0x7c90d4: mov             x1, x3
    // 0x7c90d8: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7c90d8: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7c90dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7c90dc: sub             lr, x0, #1, lsl #12
    //     0x7c90e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7c90e4: blr             lr
    // 0x7c90e8: stur            x0, [fp, #-8]
    // 0x7c90ec: r0 = TtfGlyphInfo()
    //     0x7c90ec: bl              #0x7bcae4  ; AllocateTtfGlyphInfoStub -> TtfGlyphInfo (size=0x18)
    // 0x7c90f0: ldur            x1, [fp, #-0x10]
    // 0x7c90f4: StoreField: r0->field_7 = r1
    //     0x7c90f4: stur            x1, [x0, #7]
    // 0x7c90f8: ldur            x1, [fp, #-8]
    // 0x7c90fc: StoreField: r0->field_f = r1
    //     0x7c90fc: stur            w1, [x0, #0xf]
    // 0x7c9100: ldur            x1, [fp, #-0x60]
    // 0x7c9104: StoreField: r0->field_13 = r1
    //     0x7c9104: stur            w1, [x0, #0x13]
    // 0x7c9108: LeaveFrame
    //     0x7c9108: mov             SP, fp
    //     0x7c910c: ldp             fp, lr, [SP], #0x10
    // 0x7c9110: ret
    //     0x7c9110: ret             
    // 0x7c9114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9118: b               #0x7c8e0c
    // 0x7c911c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c911c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9120: b               #0x7c8e7c
    // 0x7c9124: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9124: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c9128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9128: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c912c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c912c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ yMax(/* No info */) {
    // ** addr: 0x7c9c90, size: 0x110
    // 0x7c9c90: EnterFrame
    //     0x7c9c90: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9c94: mov             fp, SP
    // 0x7c9c98: AllocStack(0x10)
    //     0x7c9c98: sub             SP, SP, #0x10
    // 0x7c9c9c: CheckStackOverflow
    //     0x7c9c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9ca0: cmp             SP, x16
    //     0x7c9ca4: b.ls            #0x7c9d90
    // 0x7c9ca8: LoadField: r0 = r1->field_7
    //     0x7c9ca8: ldur            w0, [x1, #7]
    // 0x7c9cac: DecompressPointer r0
    //     0x7c9cac: add             x0, x0, HEAP, lsl #32
    // 0x7c9cb0: stur            x0, [fp, #-0x10]
    // 0x7c9cb4: LoadField: r3 = r1->field_b
    //     0x7c9cb4: ldur            w3, [x1, #0xb]
    // 0x7c9cb8: DecompressPointer r3
    //     0x7c9cb8: add             x3, x3, HEAP, lsl #32
    // 0x7c9cbc: mov             x1, x3
    // 0x7c9cc0: stur            x3, [fp, #-8]
    // 0x7c9cc4: r2 = "head"
    //     0x7c9cc4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7c9cc8: ldr             x2, [x2, #0x890]
    // 0x7c9ccc: r0 = _getValueOrData()
    //     0x7c9ccc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7c9cd0: ldur            x2, [fp, #-8]
    // 0x7c9cd4: LoadField: r3 = r2->field_f
    //     0x7c9cd4: ldur            w3, [x2, #0xf]
    // 0x7c9cd8: DecompressPointer r3
    //     0x7c9cd8: add             x3, x3, HEAP, lsl #32
    // 0x7c9cdc: cmp             w3, w0
    // 0x7c9ce0: b.ne            #0x7c9cec
    // 0x7c9ce4: r7 = Null
    //     0x7c9ce4: mov             x7, NULL
    // 0x7c9ce8: b               #0x7c9cf0
    // 0x7c9cec: mov             x7, x0
    // 0x7c9cf0: ldur            x2, [fp, #-0x10]
    // 0x7c9cf4: r6 = 65280
    //     0x7c9cf4: orr             x6, xzr, #0xff00
    // 0x7c9cf8: r5 = 255
    //     0x7c9cf8: movz            x5, #0xff
    // 0x7c9cfc: r4 = 32767
    //     0x7c9cfc: orr             x4, xzr, #0x7fff
    // 0x7c9d00: r3 = 32768
    //     0x7c9d00: movz            x3, #0x8000
    // 0x7c9d04: cmp             w7, NULL
    // 0x7c9d08: b.eq            #0x7c9d98
    // 0x7c9d0c: r8 = LoadInt32Instr(r7)
    //     0x7c9d0c: sbfx            x8, x7, #1, #0x1f
    //     0x7c9d10: tbz             w7, #0, #0x7c9d18
    //     0x7c9d14: ldur            x8, [x7, #7]
    // 0x7c9d18: add             x7, x8, #0x2a
    // 0x7c9d1c: LoadField: r8 = r2->field_13
    //     0x7c9d1c: ldur            w8, [x2, #0x13]
    // 0x7c9d20: r9 = LoadInt32Instr(r8)
    //     0x7c9d20: sbfx            x9, x8, #1, #0x1f
    // 0x7c9d24: sub             x0, x9, #1
    // 0x7c9d28: mov             x1, x7
    // 0x7c9d2c: cmp             x1, x0
    // 0x7c9d30: b.hs            #0x7c9d9c
    // 0x7c9d34: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7c9d34: ldur            w1, [x2, #0x17]
    // 0x7c9d38: DecompressPointer r1
    //     0x7c9d38: add             x1, x1, HEAP, lsl #32
    // 0x7c9d3c: LoadField: r8 = r2->field_1b
    //     0x7c9d3c: ldur            w8, [x2, #0x1b]
    // 0x7c9d40: r2 = LoadInt32Instr(r8)
    //     0x7c9d40: sbfx            x2, x8, #1, #0x1f
    // 0x7c9d44: add             x8, x2, x7
    // 0x7c9d48: LoadField: r2 = r1->field_7
    //     0x7c9d48: ldur            x2, [x1, #7]
    // 0x7c9d4c: ldrsh           x1, [x2, x8]
    // 0x7c9d50: mov             x2, x1
    // 0x7c9d54: ubfx            x2, x2, #0, #0x20
    // 0x7c9d58: and             x7, x2, x6
    // 0x7c9d5c: lsr             w2, w7, #8
    // 0x7c9d60: ubfx            x1, x1, #0, #0x20
    // 0x7c9d64: and             x6, x1, x5
    // 0x7c9d68: lsl             w1, w6, #8
    // 0x7c9d6c: orr             x5, x2, x1
    // 0x7c9d70: and             x1, x5, x4
    // 0x7c9d74: and             x2, x5, x3
    // 0x7c9d78: ubfx            x1, x1, #0, #0x20
    // 0x7c9d7c: ubfx            x2, x2, #0, #0x20
    // 0x7c9d80: sub             x0, x1, x2
    // 0x7c9d84: LeaveFrame
    //     0x7c9d84: mov             SP, fp
    //     0x7c9d88: ldp             fp, lr, [SP], #0x10
    // 0x7c9d8c: ret
    //     0x7c9d8c: ret             
    // 0x7c9d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9d94: b               #0x7c9ca8
    // 0x7c9d98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c9d98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7c9d9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9d9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ xMax(/* No info */) {
    // ** addr: 0x7c9da0, size: 0x110
    // 0x7c9da0: EnterFrame
    //     0x7c9da0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9da4: mov             fp, SP
    // 0x7c9da8: AllocStack(0x10)
    //     0x7c9da8: sub             SP, SP, #0x10
    // 0x7c9dac: CheckStackOverflow
    //     0x7c9dac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9db0: cmp             SP, x16
    //     0x7c9db4: b.ls            #0x7c9ea0
    // 0x7c9db8: LoadField: r0 = r1->field_7
    //     0x7c9db8: ldur            w0, [x1, #7]
    // 0x7c9dbc: DecompressPointer r0
    //     0x7c9dbc: add             x0, x0, HEAP, lsl #32
    // 0x7c9dc0: stur            x0, [fp, #-0x10]
    // 0x7c9dc4: LoadField: r3 = r1->field_b
    //     0x7c9dc4: ldur            w3, [x1, #0xb]
    // 0x7c9dc8: DecompressPointer r3
    //     0x7c9dc8: add             x3, x3, HEAP, lsl #32
    // 0x7c9dcc: mov             x1, x3
    // 0x7c9dd0: stur            x3, [fp, #-8]
    // 0x7c9dd4: r2 = "head"
    //     0x7c9dd4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7c9dd8: ldr             x2, [x2, #0x890]
    // 0x7c9ddc: r0 = _getValueOrData()
    //     0x7c9ddc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7c9de0: ldur            x2, [fp, #-8]
    // 0x7c9de4: LoadField: r3 = r2->field_f
    //     0x7c9de4: ldur            w3, [x2, #0xf]
    // 0x7c9de8: DecompressPointer r3
    //     0x7c9de8: add             x3, x3, HEAP, lsl #32
    // 0x7c9dec: cmp             w3, w0
    // 0x7c9df0: b.ne            #0x7c9dfc
    // 0x7c9df4: r7 = Null
    //     0x7c9df4: mov             x7, NULL
    // 0x7c9df8: b               #0x7c9e00
    // 0x7c9dfc: mov             x7, x0
    // 0x7c9e00: ldur            x2, [fp, #-0x10]
    // 0x7c9e04: r6 = 65280
    //     0x7c9e04: orr             x6, xzr, #0xff00
    // 0x7c9e08: r5 = 255
    //     0x7c9e08: movz            x5, #0xff
    // 0x7c9e0c: r4 = 32767
    //     0x7c9e0c: orr             x4, xzr, #0x7fff
    // 0x7c9e10: r3 = 32768
    //     0x7c9e10: movz            x3, #0x8000
    // 0x7c9e14: cmp             w7, NULL
    // 0x7c9e18: b.eq            #0x7c9ea8
    // 0x7c9e1c: r8 = LoadInt32Instr(r7)
    //     0x7c9e1c: sbfx            x8, x7, #1, #0x1f
    //     0x7c9e20: tbz             w7, #0, #0x7c9e28
    //     0x7c9e24: ldur            x8, [x7, #7]
    // 0x7c9e28: add             x7, x8, #0x28
    // 0x7c9e2c: LoadField: r8 = r2->field_13
    //     0x7c9e2c: ldur            w8, [x2, #0x13]
    // 0x7c9e30: r9 = LoadInt32Instr(r8)
    //     0x7c9e30: sbfx            x9, x8, #1, #0x1f
    // 0x7c9e34: sub             x0, x9, #1
    // 0x7c9e38: mov             x1, x7
    // 0x7c9e3c: cmp             x1, x0
    // 0x7c9e40: b.hs            #0x7c9eac
    // 0x7c9e44: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7c9e44: ldur            w1, [x2, #0x17]
    // 0x7c9e48: DecompressPointer r1
    //     0x7c9e48: add             x1, x1, HEAP, lsl #32
    // 0x7c9e4c: LoadField: r8 = r2->field_1b
    //     0x7c9e4c: ldur            w8, [x2, #0x1b]
    // 0x7c9e50: r2 = LoadInt32Instr(r8)
    //     0x7c9e50: sbfx            x2, x8, #1, #0x1f
    // 0x7c9e54: add             x8, x2, x7
    // 0x7c9e58: LoadField: r2 = r1->field_7
    //     0x7c9e58: ldur            x2, [x1, #7]
    // 0x7c9e5c: ldrsh           x1, [x2, x8]
    // 0x7c9e60: mov             x2, x1
    // 0x7c9e64: ubfx            x2, x2, #0, #0x20
    // 0x7c9e68: and             x7, x2, x6
    // 0x7c9e6c: lsr             w2, w7, #8
    // 0x7c9e70: ubfx            x1, x1, #0, #0x20
    // 0x7c9e74: and             x6, x1, x5
    // 0x7c9e78: lsl             w1, w6, #8
    // 0x7c9e7c: orr             x5, x2, x1
    // 0x7c9e80: and             x1, x5, x4
    // 0x7c9e84: and             x2, x5, x3
    // 0x7c9e88: ubfx            x1, x1, #0, #0x20
    // 0x7c9e8c: ubfx            x2, x2, #0, #0x20
    // 0x7c9e90: sub             x0, x1, x2
    // 0x7c9e94: LeaveFrame
    //     0x7c9e94: mov             SP, fp
    //     0x7c9e98: ldp             fp, lr, [SP], #0x10
    // 0x7c9e9c: ret
    //     0x7c9e9c: ret             
    // 0x7c9ea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9ea0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9ea4: b               #0x7c9db8
    // 0x7c9ea8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c9ea8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7c9eac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9eac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ yMin(/* No info */) {
    // ** addr: 0x7c9eb0, size: 0x110
    // 0x7c9eb0: EnterFrame
    //     0x7c9eb0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9eb4: mov             fp, SP
    // 0x7c9eb8: AllocStack(0x10)
    //     0x7c9eb8: sub             SP, SP, #0x10
    // 0x7c9ebc: CheckStackOverflow
    //     0x7c9ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9ec0: cmp             SP, x16
    //     0x7c9ec4: b.ls            #0x7c9fb0
    // 0x7c9ec8: LoadField: r0 = r1->field_7
    //     0x7c9ec8: ldur            w0, [x1, #7]
    // 0x7c9ecc: DecompressPointer r0
    //     0x7c9ecc: add             x0, x0, HEAP, lsl #32
    // 0x7c9ed0: stur            x0, [fp, #-0x10]
    // 0x7c9ed4: LoadField: r3 = r1->field_b
    //     0x7c9ed4: ldur            w3, [x1, #0xb]
    // 0x7c9ed8: DecompressPointer r3
    //     0x7c9ed8: add             x3, x3, HEAP, lsl #32
    // 0x7c9edc: mov             x1, x3
    // 0x7c9ee0: stur            x3, [fp, #-8]
    // 0x7c9ee4: r2 = "head"
    //     0x7c9ee4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7c9ee8: ldr             x2, [x2, #0x890]
    // 0x7c9eec: r0 = _getValueOrData()
    //     0x7c9eec: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7c9ef0: ldur            x2, [fp, #-8]
    // 0x7c9ef4: LoadField: r3 = r2->field_f
    //     0x7c9ef4: ldur            w3, [x2, #0xf]
    // 0x7c9ef8: DecompressPointer r3
    //     0x7c9ef8: add             x3, x3, HEAP, lsl #32
    // 0x7c9efc: cmp             w3, w0
    // 0x7c9f00: b.ne            #0x7c9f0c
    // 0x7c9f04: r7 = Null
    //     0x7c9f04: mov             x7, NULL
    // 0x7c9f08: b               #0x7c9f10
    // 0x7c9f0c: mov             x7, x0
    // 0x7c9f10: ldur            x2, [fp, #-0x10]
    // 0x7c9f14: r6 = 65280
    //     0x7c9f14: orr             x6, xzr, #0xff00
    // 0x7c9f18: r5 = 255
    //     0x7c9f18: movz            x5, #0xff
    // 0x7c9f1c: r4 = 32767
    //     0x7c9f1c: orr             x4, xzr, #0x7fff
    // 0x7c9f20: r3 = 32768
    //     0x7c9f20: movz            x3, #0x8000
    // 0x7c9f24: cmp             w7, NULL
    // 0x7c9f28: b.eq            #0x7c9fb8
    // 0x7c9f2c: r8 = LoadInt32Instr(r7)
    //     0x7c9f2c: sbfx            x8, x7, #1, #0x1f
    //     0x7c9f30: tbz             w7, #0, #0x7c9f38
    //     0x7c9f34: ldur            x8, [x7, #7]
    // 0x7c9f38: add             x7, x8, #0x26
    // 0x7c9f3c: LoadField: r8 = r2->field_13
    //     0x7c9f3c: ldur            w8, [x2, #0x13]
    // 0x7c9f40: r9 = LoadInt32Instr(r8)
    //     0x7c9f40: sbfx            x9, x8, #1, #0x1f
    // 0x7c9f44: sub             x0, x9, #1
    // 0x7c9f48: mov             x1, x7
    // 0x7c9f4c: cmp             x1, x0
    // 0x7c9f50: b.hs            #0x7c9fbc
    // 0x7c9f54: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7c9f54: ldur            w1, [x2, #0x17]
    // 0x7c9f58: DecompressPointer r1
    //     0x7c9f58: add             x1, x1, HEAP, lsl #32
    // 0x7c9f5c: LoadField: r8 = r2->field_1b
    //     0x7c9f5c: ldur            w8, [x2, #0x1b]
    // 0x7c9f60: r2 = LoadInt32Instr(r8)
    //     0x7c9f60: sbfx            x2, x8, #1, #0x1f
    // 0x7c9f64: add             x8, x2, x7
    // 0x7c9f68: LoadField: r2 = r1->field_7
    //     0x7c9f68: ldur            x2, [x1, #7]
    // 0x7c9f6c: ldrsh           x1, [x2, x8]
    // 0x7c9f70: mov             x2, x1
    // 0x7c9f74: ubfx            x2, x2, #0, #0x20
    // 0x7c9f78: and             x7, x2, x6
    // 0x7c9f7c: lsr             w2, w7, #8
    // 0x7c9f80: ubfx            x1, x1, #0, #0x20
    // 0x7c9f84: and             x6, x1, x5
    // 0x7c9f88: lsl             w1, w6, #8
    // 0x7c9f8c: orr             x5, x2, x1
    // 0x7c9f90: and             x1, x5, x4
    // 0x7c9f94: and             x2, x5, x3
    // 0x7c9f98: ubfx            x1, x1, #0, #0x20
    // 0x7c9f9c: ubfx            x2, x2, #0, #0x20
    // 0x7c9fa0: sub             x0, x1, x2
    // 0x7c9fa4: LeaveFrame
    //     0x7c9fa4: mov             SP, fp
    //     0x7c9fa8: ldp             fp, lr, [SP], #0x10
    // 0x7c9fac: ret
    //     0x7c9fac: ret             
    // 0x7c9fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9fb4: b               #0x7c9ec8
    // 0x7c9fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c9fb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7c9fbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9fbc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ unitsPerEm(/* No info */) {
    // ** addr: 0x7c9fc0, size: 0xfc
    // 0x7c9fc0: EnterFrame
    //     0x7c9fc0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9fc4: mov             fp, SP
    // 0x7c9fc8: AllocStack(0x10)
    //     0x7c9fc8: sub             SP, SP, #0x10
    // 0x7c9fcc: CheckStackOverflow
    //     0x7c9fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9fd0: cmp             SP, x16
    //     0x7c9fd4: b.ls            #0x7ca0ac
    // 0x7c9fd8: LoadField: r0 = r1->field_7
    //     0x7c9fd8: ldur            w0, [x1, #7]
    // 0x7c9fdc: DecompressPointer r0
    //     0x7c9fdc: add             x0, x0, HEAP, lsl #32
    // 0x7c9fe0: stur            x0, [fp, #-0x10]
    // 0x7c9fe4: LoadField: r3 = r1->field_b
    //     0x7c9fe4: ldur            w3, [x1, #0xb]
    // 0x7c9fe8: DecompressPointer r3
    //     0x7c9fe8: add             x3, x3, HEAP, lsl #32
    // 0x7c9fec: mov             x1, x3
    // 0x7c9ff0: stur            x3, [fp, #-8]
    // 0x7c9ff4: r2 = "head"
    //     0x7c9ff4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7c9ff8: ldr             x2, [x2, #0x890]
    // 0x7c9ffc: r0 = _getValueOrData()
    //     0x7c9ffc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ca000: ldur            x2, [fp, #-8]
    // 0x7ca004: LoadField: r3 = r2->field_f
    //     0x7ca004: ldur            w3, [x2, #0xf]
    // 0x7ca008: DecompressPointer r3
    //     0x7ca008: add             x3, x3, HEAP, lsl #32
    // 0x7ca00c: cmp             w3, w0
    // 0x7ca010: b.ne            #0x7ca01c
    // 0x7ca014: r5 = Null
    //     0x7ca014: mov             x5, NULL
    // 0x7ca018: b               #0x7ca020
    // 0x7ca01c: mov             x5, x0
    // 0x7ca020: ldur            x2, [fp, #-0x10]
    // 0x7ca024: r4 = 65280
    //     0x7ca024: orr             x4, xzr, #0xff00
    // 0x7ca028: r3 = 255
    //     0x7ca028: movz            x3, #0xff
    // 0x7ca02c: cmp             w5, NULL
    // 0x7ca030: b.eq            #0x7ca0b4
    // 0x7ca034: r6 = LoadInt32Instr(r5)
    //     0x7ca034: sbfx            x6, x5, #1, #0x1f
    //     0x7ca038: tbz             w5, #0, #0x7ca040
    //     0x7ca03c: ldur            x6, [x5, #7]
    // 0x7ca040: add             x5, x6, #0x12
    // 0x7ca044: LoadField: r6 = r2->field_13
    //     0x7ca044: ldur            w6, [x2, #0x13]
    // 0x7ca048: r7 = LoadInt32Instr(r6)
    //     0x7ca048: sbfx            x7, x6, #1, #0x1f
    // 0x7ca04c: sub             x0, x7, #1
    // 0x7ca050: mov             x1, x5
    // 0x7ca054: cmp             x1, x0
    // 0x7ca058: b.hs            #0x7ca0b8
    // 0x7ca05c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7ca05c: ldur            w1, [x2, #0x17]
    // 0x7ca060: DecompressPointer r1
    //     0x7ca060: add             x1, x1, HEAP, lsl #32
    // 0x7ca064: LoadField: r6 = r2->field_1b
    //     0x7ca064: ldur            w6, [x2, #0x1b]
    // 0x7ca068: r2 = LoadInt32Instr(r6)
    //     0x7ca068: sbfx            x2, x6, #1, #0x1f
    // 0x7ca06c: add             x6, x2, x5
    // 0x7ca070: LoadField: r2 = r1->field_7
    //     0x7ca070: ldur            x2, [x1, #7]
    // 0x7ca074: ldrh            w1, [x2, x6]
    // 0x7ca078: mov             x2, x1
    // 0x7ca07c: ubfx            x2, x2, #0, #0x20
    // 0x7ca080: and             x5, x2, x4
    // 0x7ca084: ubfx            x5, x5, #0, #0x20
    // 0x7ca088: asr             x2, x5, #8
    // 0x7ca08c: ubfx            x1, x1, #0, #0x20
    // 0x7ca090: and             x4, x1, x3
    // 0x7ca094: ubfx            x4, x4, #0, #0x20
    // 0x7ca098: lsl             x1, x4, #8
    // 0x7ca09c: orr             x0, x2, x1
    // 0x7ca0a0: LeaveFrame
    //     0x7ca0a0: mov             SP, fp
    //     0x7ca0a4: ldp             fp, lr, [SP], #0x10
    // 0x7ca0a8: ret
    //     0x7ca0a8: ret             
    // 0x7ca0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca0b0: b               #0x7c9fd8
    // 0x7ca0b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ca0b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7ca0b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7ca0b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ xMin(/* No info */) {
    // ** addr: 0x7ca0bc, size: 0x110
    // 0x7ca0bc: EnterFrame
    //     0x7ca0bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca0c0: mov             fp, SP
    // 0x7ca0c4: AllocStack(0x10)
    //     0x7ca0c4: sub             SP, SP, #0x10
    // 0x7ca0c8: CheckStackOverflow
    //     0x7ca0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca0cc: cmp             SP, x16
    //     0x7ca0d0: b.ls            #0x7ca1bc
    // 0x7ca0d4: LoadField: r0 = r1->field_7
    //     0x7ca0d4: ldur            w0, [x1, #7]
    // 0x7ca0d8: DecompressPointer r0
    //     0x7ca0d8: add             x0, x0, HEAP, lsl #32
    // 0x7ca0dc: stur            x0, [fp, #-0x10]
    // 0x7ca0e0: LoadField: r3 = r1->field_b
    //     0x7ca0e0: ldur            w3, [x1, #0xb]
    // 0x7ca0e4: DecompressPointer r3
    //     0x7ca0e4: add             x3, x3, HEAP, lsl #32
    // 0x7ca0e8: mov             x1, x3
    // 0x7ca0ec: stur            x3, [fp, #-8]
    // 0x7ca0f0: r2 = "head"
    //     0x7ca0f0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7ca0f4: ldr             x2, [x2, #0x890]
    // 0x7ca0f8: r0 = _getValueOrData()
    //     0x7ca0f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ca0fc: ldur            x2, [fp, #-8]
    // 0x7ca100: LoadField: r3 = r2->field_f
    //     0x7ca100: ldur            w3, [x2, #0xf]
    // 0x7ca104: DecompressPointer r3
    //     0x7ca104: add             x3, x3, HEAP, lsl #32
    // 0x7ca108: cmp             w3, w0
    // 0x7ca10c: b.ne            #0x7ca118
    // 0x7ca110: r7 = Null
    //     0x7ca110: mov             x7, NULL
    // 0x7ca114: b               #0x7ca11c
    // 0x7ca118: mov             x7, x0
    // 0x7ca11c: ldur            x2, [fp, #-0x10]
    // 0x7ca120: r6 = 65280
    //     0x7ca120: orr             x6, xzr, #0xff00
    // 0x7ca124: r5 = 255
    //     0x7ca124: movz            x5, #0xff
    // 0x7ca128: r4 = 32767
    //     0x7ca128: orr             x4, xzr, #0x7fff
    // 0x7ca12c: r3 = 32768
    //     0x7ca12c: movz            x3, #0x8000
    // 0x7ca130: cmp             w7, NULL
    // 0x7ca134: b.eq            #0x7ca1c4
    // 0x7ca138: r8 = LoadInt32Instr(r7)
    //     0x7ca138: sbfx            x8, x7, #1, #0x1f
    //     0x7ca13c: tbz             w7, #0, #0x7ca144
    //     0x7ca140: ldur            x8, [x7, #7]
    // 0x7ca144: add             x7, x8, #0x24
    // 0x7ca148: LoadField: r8 = r2->field_13
    //     0x7ca148: ldur            w8, [x2, #0x13]
    // 0x7ca14c: r9 = LoadInt32Instr(r8)
    //     0x7ca14c: sbfx            x9, x8, #1, #0x1f
    // 0x7ca150: sub             x0, x9, #1
    // 0x7ca154: mov             x1, x7
    // 0x7ca158: cmp             x1, x0
    // 0x7ca15c: b.hs            #0x7ca1c8
    // 0x7ca160: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7ca160: ldur            w1, [x2, #0x17]
    // 0x7ca164: DecompressPointer r1
    //     0x7ca164: add             x1, x1, HEAP, lsl #32
    // 0x7ca168: LoadField: r8 = r2->field_1b
    //     0x7ca168: ldur            w8, [x2, #0x1b]
    // 0x7ca16c: r2 = LoadInt32Instr(r8)
    //     0x7ca16c: sbfx            x2, x8, #1, #0x1f
    // 0x7ca170: add             x8, x2, x7
    // 0x7ca174: LoadField: r2 = r1->field_7
    //     0x7ca174: ldur            x2, [x1, #7]
    // 0x7ca178: ldrsh           x1, [x2, x8]
    // 0x7ca17c: mov             x2, x1
    // 0x7ca180: ubfx            x2, x2, #0, #0x20
    // 0x7ca184: and             x7, x2, x6
    // 0x7ca188: lsr             w2, w7, #8
    // 0x7ca18c: ubfx            x1, x1, #0, #0x20
    // 0x7ca190: and             x6, x1, x5
    // 0x7ca194: lsl             w1, w6, #8
    // 0x7ca198: orr             x5, x2, x1
    // 0x7ca19c: and             x1, x5, x4
    // 0x7ca1a0: and             x2, x5, x3
    // 0x7ca1a4: ubfx            x1, x1, #0, #0x20
    // 0x7ca1a8: ubfx            x2, x2, #0, #0x20
    // 0x7ca1ac: sub             x0, x1, x2
    // 0x7ca1b0: LeaveFrame
    //     0x7ca1b0: mov             SP, fp
    //     0x7ca1b4: ldp             fp, lr, [SP], #0x10
    // 0x7ca1b8: ret
    //     0x7ca1b8: ret             
    // 0x7ca1bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca1bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca1c0: b               #0x7ca0d4
    // 0x7ca1c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ca1c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7ca1c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7ca1c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ TtfParser(/* No info */) {
    // ** addr: 0xc369f4, size: 0x4e8
    // 0xc369f4: EnterFrame
    //     0xc369f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc369f8: mov             fp, SP
    // 0xc369fc: AllocStack(0x70)
    //     0xc369fc: sub             SP, SP, #0x70
    // 0xc36a00: SetupParameters(TtfParser this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc36a00: mov             x0, x2
    //     0xc36a04: stur            x1, [fp, #-8]
    //     0xc36a08: stur            x2, [fp, #-0x10]
    // 0xc36a0c: CheckStackOverflow
    //     0xc36a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36a10: cmp             SP, x16
    //     0xc36a14: b.ls            #0xc36ec0
    // 0xc36a18: r16 = <String, int>
    //     0xc36a18: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xc36a1c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc36a20: stp             lr, x16, [SP]
    // 0xc36a24: r0 = Map._fromLiteral()
    //     0xc36a24: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc36a28: mov             x2, x0
    // 0xc36a2c: ldur            x1, [fp, #-8]
    // 0xc36a30: stur            x2, [fp, #-0x18]
    // 0xc36a34: StoreField: r1->field_b = r0
    //     0xc36a34: stur            w0, [x1, #0xb]
    //     0xc36a38: ldurb           w16, [x1, #-1]
    //     0xc36a3c: ldurb           w17, [x0, #-1]
    //     0xc36a40: and             x16, x17, x16, lsr #2
    //     0xc36a44: tst             x16, HEAP, lsr #32
    //     0xc36a48: b.eq            #0xc36a50
    //     0xc36a4c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc36a50: r16 = <String, int>
    //     0xc36a50: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xc36a54: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc36a58: stp             lr, x16, [SP]
    // 0xc36a5c: r0 = Map._fromLiteral()
    //     0xc36a5c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc36a60: mov             x2, x0
    // 0xc36a64: ldur            x1, [fp, #-8]
    // 0xc36a68: stur            x2, [fp, #-0x20]
    // 0xc36a6c: StoreField: r1->field_f = r0
    //     0xc36a6c: stur            w0, [x1, #0xf]
    //     0xc36a70: ldurb           w16, [x1, #-1]
    //     0xc36a74: ldurb           w17, [x0, #-1]
    //     0xc36a78: and             x16, x17, x16, lsr #2
    //     0xc36a7c: tst             x16, HEAP, lsr #32
    //     0xc36a80: b.eq            #0xc36a88
    //     0xc36a84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc36a88: r16 = <int, int>
    //     0xc36a88: ldr             x16, [PP, #0x28b0]  ; [pp+0x28b0] TypeArguments: <int, int>
    // 0xc36a8c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc36a90: stp             lr, x16, [SP]
    // 0xc36a94: r0 = Map._fromLiteral()
    //     0xc36a94: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc36a98: ldur            x3, [fp, #-8]
    // 0xc36a9c: StoreField: r3->field_13 = r0
    //     0xc36a9c: stur            w0, [x3, #0x13]
    //     0xc36aa0: ldurb           w16, [x3, #-1]
    //     0xc36aa4: ldurb           w17, [x0, #-1]
    //     0xc36aa8: and             x16, x17, x16, lsr #2
    //     0xc36aac: tst             x16, HEAP, lsr #32
    //     0xc36ab0: b.eq            #0xc36ab8
    //     0xc36ab4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xc36ab8: r1 = <int>
    //     0xc36ab8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc36abc: r2 = 0
    //     0xc36abc: movz            x2, #0
    // 0xc36ac0: r0 = _GrowableList()
    //     0xc36ac0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc36ac4: ldur            x3, [fp, #-8]
    // 0xc36ac8: ArrayStore: r3[0] = r0  ; List_4
    //     0xc36ac8: stur            w0, [x3, #0x17]
    //     0xc36acc: ldurb           w16, [x3, #-1]
    //     0xc36ad0: ldurb           w17, [x0, #-1]
    //     0xc36ad4: and             x16, x17, x16, lsr #2
    //     0xc36ad8: tst             x16, HEAP, lsr #32
    //     0xc36adc: b.eq            #0xc36ae4
    //     0xc36ae0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xc36ae4: r1 = <int>
    //     0xc36ae4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc36ae8: r2 = 0
    //     0xc36ae8: movz            x2, #0
    // 0xc36aec: r0 = _GrowableList()
    //     0xc36aec: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc36af0: ldur            x1, [fp, #-8]
    // 0xc36af4: StoreField: r1->field_1b = r0
    //     0xc36af4: stur            w0, [x1, #0x1b]
    //     0xc36af8: ldurb           w16, [x1, #-1]
    //     0xc36afc: ldurb           w17, [x0, #-1]
    //     0xc36b00: and             x16, x17, x16, lsr #2
    //     0xc36b04: tst             x16, HEAP, lsr #32
    //     0xc36b08: b.eq            #0xc36b10
    //     0xc36b0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc36b10: r16 = <int, PdfFontMetrics>
    //     0xc36b10: add             x16, PP, #0x33, lsl #12  ; [pp+0x33848] TypeArguments: <int, PdfFontMetrics>
    //     0xc36b14: ldr             x16, [x16, #0x848]
    // 0xc36b18: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc36b1c: stp             lr, x16, [SP]
    // 0xc36b20: r0 = Map._fromLiteral()
    //     0xc36b20: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc36b24: ldur            x1, [fp, #-8]
    // 0xc36b28: StoreField: r1->field_1f = r0
    //     0xc36b28: stur            w0, [x1, #0x1f]
    //     0xc36b2c: ldurb           w16, [x1, #-1]
    //     0xc36b30: ldurb           w17, [x0, #-1]
    //     0xc36b34: and             x16, x17, x16, lsr #2
    //     0xc36b38: tst             x16, HEAP, lsr #32
    //     0xc36b3c: b.eq            #0xc36b44
    //     0xc36b40: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc36b44: r16 = <int, TtfBitmapInfo>
    //     0xc36b44: add             x16, PP, #0x33, lsl #12  ; [pp+0x33850] TypeArguments: <int, TtfBitmapInfo>
    //     0xc36b48: ldr             x16, [x16, #0x850]
    // 0xc36b4c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc36b50: stp             lr, x16, [SP]
    // 0xc36b54: r0 = Map._fromLiteral()
    //     0xc36b54: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc36b58: ldur            x2, [fp, #-8]
    // 0xc36b5c: StoreField: r2->field_23 = r0
    //     0xc36b5c: stur            w0, [x2, #0x23]
    //     0xc36b60: ldurb           w16, [x2, #-1]
    //     0xc36b64: ldurb           w17, [x0, #-1]
    //     0xc36b68: and             x16, x17, x16, lsr #2
    //     0xc36b6c: tst             x16, HEAP, lsr #32
    //     0xc36b70: b.eq            #0xc36b78
    //     0xc36b74: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc36b78: ldur            x0, [fp, #-0x10]
    // 0xc36b7c: StoreField: r2->field_7 = r0
    //     0xc36b7c: stur            w0, [x2, #7]
    //     0xc36b80: ldurb           w16, [x2, #-1]
    //     0xc36b84: ldurb           w17, [x0, #-1]
    //     0xc36b88: and             x16, x17, x16, lsr #2
    //     0xc36b8c: tst             x16, HEAP, lsr #32
    //     0xc36b90: b.eq            #0xc36b98
    //     0xc36b94: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc36b98: ldur            x3, [fp, #-0x10]
    // 0xc36b9c: LoadField: r0 = r3->field_13
    //     0xc36b9c: ldur            w0, [x3, #0x13]
    // 0xc36ba0: r4 = LoadInt32Instr(r0)
    //     0xc36ba0: sbfx            x4, x0, #1, #0x1f
    // 0xc36ba4: sub             x0, x4, #1
    // 0xc36ba8: r1 = 4
    //     0xc36ba8: movz            x1, #0x4
    // 0xc36bac: cmp             x1, x0
    // 0xc36bb0: b.hs            #0xc36ec8
    // 0xc36bb4: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xc36bb4: ldur            w5, [x3, #0x17]
    // 0xc36bb8: DecompressPointer r5
    //     0xc36bb8: add             x5, x5, HEAP, lsl #32
    // 0xc36bbc: stur            x5, [fp, #-0x48]
    // 0xc36bc0: LoadField: r0 = r3->field_1b
    //     0xc36bc0: ldur            w0, [x3, #0x1b]
    // 0xc36bc4: r6 = LoadInt32Instr(r0)
    //     0xc36bc4: sbfx            x6, x0, #1, #0x1f
    // 0xc36bc8: stur            x6, [fp, #-0x40]
    // 0xc36bcc: add             x0, x6, #4
    // 0xc36bd0: LoadField: r1 = r5->field_7
    //     0xc36bd0: ldur            x1, [x5, #7]
    // 0xc36bd4: ldrh            w7, [x1, x0]
    // 0xc36bd8: mov             x0, x7
    // 0xc36bdc: ubfx            x0, x0, #0, #0x20
    // 0xc36be0: r1 = 65280
    //     0xc36be0: orr             x1, xzr, #0xff00
    // 0xc36be4: and             x8, x0, x1
    // 0xc36be8: ubfx            x8, x8, #0, #0x20
    // 0xc36bec: asr             x0, x8, #8
    // 0xc36bf0: ubfx            x7, x7, #0, #0x20
    // 0xc36bf4: r1 = 255
    //     0xc36bf4: movz            x1, #0xff
    // 0xc36bf8: and             x8, x7, x1
    // 0xc36bfc: ubfx            x8, x8, #0, #0x20
    // 0xc36c00: lsl             x1, x8, #8
    // 0xc36c04: orr             x7, x0, x1
    // 0xc36c08: stur            x7, [fp, #-0x38]
    // 0xc36c0c: sub             x8, x4, #3
    // 0xc36c10: stur            x8, [fp, #-0x30]
    // 0xc36c14: r4 = 0
    //     0xc36c14: movz            x4, #0
    // 0xc36c18: stur            x4, [fp, #-0x28]
    // 0xc36c1c: CheckStackOverflow
    //     0xc36c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36c20: cmp             SP, x16
    //     0xc36c24: b.ls            #0xc36ecc
    // 0xc36c28: cmp             x4, x7
    // 0xc36c2c: b.ge            #0xc36e40
    // 0xc36c30: r0 = LoadClassIdInstr(r3)
    //     0xc36c30: ldur            x0, [x3, #-1]
    //     0xc36c34: ubfx            x0, x0, #0xc, #0x14
    // 0xc36c38: mov             x1, x3
    // 0xc36c3c: r0 = GDT[cid_x0 + -0xf60]()
    //     0xc36c3c: sub             lr, x0, #0xf60
    //     0xc36c40: ldr             lr, [x21, lr, lsl #3]
    //     0xc36c44: blr             lr
    // 0xc36c48: mov             x3, x0
    // 0xc36c4c: ldur            x2, [fp, #-0x28]
    // 0xc36c50: lsl             x4, x2, #4
    // 0xc36c54: stur            x4, [fp, #-0x50]
    // 0xc36c58: add             x5, x4, #0xc
    // 0xc36c5c: r0 = BoxInt64Instr(r5)
    //     0xc36c5c: sbfiz           x0, x5, #1, #0x1f
    //     0xc36c60: cmp             x5, x0, asr #1
    //     0xc36c64: b.eq            #0xc36c70
    //     0xc36c68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36c6c: stur            x5, [x0, #7]
    // 0xc36c70: r1 = LoadClassIdInstr(r3)
    //     0xc36c70: ldur            x1, [x3, #-1]
    //     0xc36c74: ubfx            x1, x1, #0xc, #0x14
    // 0xc36c78: r16 = 8
    //     0xc36c78: movz            x16, #0x8
    // 0xc36c7c: stp             x16, x0, [SP]
    // 0xc36c80: mov             x0, x1
    // 0xc36c84: mov             x1, x3
    // 0xc36c88: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xc36c88: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xc36c8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc36c8c: sub             lr, x0, #1, lsl #12
    //     0xc36c90: ldr             lr, [x21, lr, lsl #3]
    //     0xc36c94: blr             lr
    // 0xc36c98: mov             x2, x0
    // 0xc36c9c: r1 = Instance_Utf8Decoder
    //     0xc36c9c: ldr             x1, [PP, #0x1458]  ; [pp+0x1458] Obj!Utf8Decoder@e2cd61
    // 0xc36ca0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc36ca0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc36ca4: r0 = convert()
    //     0xc36ca4: bl              #0xcf95bc  ; [dart:convert] Utf8Decoder::convert
    // 0xc36ca8: mov             x3, x0
    // 0xc36cac: ldur            x2, [fp, #-0x50]
    // 0xc36cb0: stur            x3, [fp, #-0x60]
    // 0xc36cb4: add             x4, x2, #0x14
    // 0xc36cb8: ldur            x0, [fp, #-0x30]
    // 0xc36cbc: mov             x1, x4
    // 0xc36cc0: cmp             x1, x0
    // 0xc36cc4: b.hs            #0xc36ed4
    // 0xc36cc8: ldur            x5, [fp, #-0x40]
    // 0xc36ccc: add             x0, x5, x4
    // 0xc36cd0: ldur            x4, [fp, #-0x48]
    // 0xc36cd4: LoadField: r1 = r4->field_7
    //     0xc36cd4: ldur            x1, [x4, #7]
    // 0xc36cd8: ldr             w6, [x1, x0]
    // 0xc36cdc: r7 = 4278255360
    //     0xc36cdc: movz            x7, #0xff00
    //     0xc36ce0: movk            x7, #0xff00, lsl #16
    // 0xc36ce4: and             x0, x6, x7
    // 0xc36ce8: ubfx            x0, x0, #0, #0x20
    // 0xc36cec: asr             x1, x0, #8
    // 0xc36cf0: r8 = 16711935
    //     0xc36cf0: movz            x8, #0xff
    //     0xc36cf4: movk            x8, #0xff, lsl #16
    // 0xc36cf8: and             x0, x6, x8
    // 0xc36cfc: ubfx            x0, x0, #0, #0x20
    // 0xc36d00: lsl             x6, x0, #8
    // 0xc36d04: orr             x0, x1, x6
    // 0xc36d08: mov             x1, x0
    // 0xc36d0c: ubfx            x1, x1, #0, #0x20
    // 0xc36d10: r6 = 4294901760
    //     0xc36d10: orr             x6, xzr, #0xffff0000
    // 0xc36d14: and             x9, x1, x6
    // 0xc36d18: ubfx            x9, x9, #0, #0x20
    // 0xc36d1c: asr             x1, x9, #0x10
    // 0xc36d20: ubfx            x0, x0, #0, #0x20
    // 0xc36d24: r9 = 65535
    //     0xc36d24: orr             x9, xzr, #0xffff
    // 0xc36d28: and             x10, x0, x9
    // 0xc36d2c: ubfx            x10, x10, #0, #0x20
    // 0xc36d30: lsl             x0, x10, #0x10
    // 0xc36d34: orr             x10, x1, x0
    // 0xc36d38: stur            x10, [fp, #-0x58]
    // 0xc36d3c: add             x11, x2, #0x18
    // 0xc36d40: ldur            x0, [fp, #-0x30]
    // 0xc36d44: mov             x1, x11
    // 0xc36d48: cmp             x1, x0
    // 0xc36d4c: b.hs            #0xc36ed8
    // 0xc36d50: add             x0, x5, x11
    // 0xc36d54: LoadField: r1 = r4->field_7
    //     0xc36d54: ldur            x1, [x4, #7]
    // 0xc36d58: ldr             w2, [x1, x0]
    // 0xc36d5c: and             x0, x2, x7
    // 0xc36d60: ubfx            x0, x0, #0, #0x20
    // 0xc36d64: asr             x1, x0, #8
    // 0xc36d68: and             x0, x2, x8
    // 0xc36d6c: ubfx            x0, x0, #0, #0x20
    // 0xc36d70: lsl             x2, x0, #8
    // 0xc36d74: orr             x0, x1, x2
    // 0xc36d78: mov             x1, x0
    // 0xc36d7c: ubfx            x1, x1, #0, #0x20
    // 0xc36d80: and             x2, x1, x6
    // 0xc36d84: ubfx            x2, x2, #0, #0x20
    // 0xc36d88: asr             x1, x2, #0x10
    // 0xc36d8c: ubfx            x0, x0, #0, #0x20
    // 0xc36d90: and             x2, x0, x9
    // 0xc36d94: ubfx            x2, x2, #0, #0x20
    // 0xc36d98: lsl             x0, x2, #0x10
    // 0xc36d9c: orr             x11, x1, x0
    // 0xc36da0: ldur            x1, [fp, #-0x18]
    // 0xc36da4: mov             x2, x3
    // 0xc36da8: stur            x11, [fp, #-0x50]
    // 0xc36dac: r0 = _hashCode()
    //     0xc36dac: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc36db0: mov             x3, x0
    // 0xc36db4: ldur            x2, [fp, #-0x58]
    // 0xc36db8: r0 = BoxInt64Instr(r2)
    //     0xc36db8: sbfiz           x0, x2, #1, #0x1f
    //     0xc36dbc: cmp             x2, x0, asr #1
    //     0xc36dc0: b.eq            #0xc36dcc
    //     0xc36dc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36dc8: stur            x2, [x0, #7]
    // 0xc36dcc: ldur            x1, [fp, #-0x18]
    // 0xc36dd0: ldur            x2, [fp, #-0x60]
    // 0xc36dd4: mov             x5, x3
    // 0xc36dd8: mov             x3, x0
    // 0xc36ddc: r0 = _set()
    //     0xc36ddc: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc36de0: ldur            x1, [fp, #-0x20]
    // 0xc36de4: ldur            x2, [fp, #-0x60]
    // 0xc36de8: r0 = _hashCode()
    //     0xc36de8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc36dec: mov             x3, x0
    // 0xc36df0: ldur            x2, [fp, #-0x50]
    // 0xc36df4: r0 = BoxInt64Instr(r2)
    //     0xc36df4: sbfiz           x0, x2, #1, #0x1f
    //     0xc36df8: cmp             x2, x0, asr #1
    //     0xc36dfc: b.eq            #0xc36e08
    //     0xc36e00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36e04: stur            x2, [x0, #7]
    // 0xc36e08: ldur            x1, [fp, #-0x20]
    // 0xc36e0c: ldur            x2, [fp, #-0x60]
    // 0xc36e10: mov             x5, x3
    // 0xc36e14: mov             x3, x0
    // 0xc36e18: r0 = _set()
    //     0xc36e18: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc36e1c: ldur            x0, [fp, #-0x28]
    // 0xc36e20: add             x4, x0, #1
    // 0xc36e24: ldur            x2, [fp, #-8]
    // 0xc36e28: ldur            x3, [fp, #-0x10]
    // 0xc36e2c: ldur            x8, [fp, #-0x30]
    // 0xc36e30: ldur            x5, [fp, #-0x48]
    // 0xc36e34: ldur            x7, [fp, #-0x38]
    // 0xc36e38: ldur            x6, [fp, #-0x40]
    // 0xc36e3c: b               #0xc36c18
    // 0xc36e40: ldur            x1, [fp, #-8]
    // 0xc36e44: r0 = _parseCMap()
    //     0xc36e44: bl              #0xc38b4c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseCMap
    // 0xc36e48: ldur            x1, [fp, #-0x18]
    // 0xc36e4c: r2 = "loca"
    //     0xc36e4c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0xc36e50: ldr             x2, [x2, #0x858]
    // 0xc36e54: r0 = containsKey()
    //     0xc36e54: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xc36e58: tbnz            w0, #4, #0xc36e80
    // 0xc36e5c: ldur            x1, [fp, #-0x18]
    // 0xc36e60: r2 = "glyf"
    //     0xc36e60: add             x2, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0xc36e64: ldr             x2, [x2, #0x860]
    // 0xc36e68: r0 = containsKey()
    //     0xc36e68: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xc36e6c: tbnz            w0, #4, #0xc36e80
    // 0xc36e70: ldur            x1, [fp, #-8]
    // 0xc36e74: r0 = _parseIndexes()
    //     0xc36e74: bl              #0xc3842c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseIndexes
    // 0xc36e78: ldur            x1, [fp, #-8]
    // 0xc36e7c: r0 = _parseGlyphs()
    //     0xc36e7c: bl              #0xc37858  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseGlyphs
    // 0xc36e80: ldur            x1, [fp, #-0x18]
    // 0xc36e84: r2 = "CBLC"
    //     0xc36e84: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "CBLC"
    //     0xc36e88: ldr             x2, [x2, #0x868]
    // 0xc36e8c: r0 = containsKey()
    //     0xc36e8c: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xc36e90: tbnz            w0, #4, #0xc36eb0
    // 0xc36e94: ldur            x1, [fp, #-0x18]
    // 0xc36e98: r2 = "CBDT"
    //     0xc36e98: add             x2, PP, #0x33, lsl #12  ; [pp+0x33870] "CBDT"
    //     0xc36e9c: ldr             x2, [x2, #0x870]
    // 0xc36ea0: r0 = containsKey()
    //     0xc36ea0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xc36ea4: tbnz            w0, #4, #0xc36eb0
    // 0xc36ea8: ldur            x1, [fp, #-8]
    // 0xc36eac: r0 = _parseBitmaps()
    //     0xc36eac: bl              #0xc36edc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseBitmaps
    // 0xc36eb0: r0 = Null
    //     0xc36eb0: mov             x0, NULL
    // 0xc36eb4: LeaveFrame
    //     0xc36eb4: mov             SP, fp
    //     0xc36eb8: ldp             fp, lr, [SP], #0x10
    // 0xc36ebc: ret
    //     0xc36ebc: ret             
    // 0xc36ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc36ec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36ec4: b               #0xc36a18
    // 0xc36ec8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc36ec8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc36ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc36ecc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36ed0: b               #0xc36c28
    // 0xc36ed4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc36ed4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc36ed8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc36ed8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseBitmaps(/* No info */) {
    // ** addr: 0xc36edc, size: 0x970
    // 0xc36edc: EnterFrame
    //     0xc36edc: stp             fp, lr, [SP, #-0x10]!
    //     0xc36ee0: mov             fp, SP
    // 0xc36ee4: AllocStack(0x118)
    //     0xc36ee4: sub             SP, SP, #0x118
    // 0xc36ee8: SetupParameters(TtfParser this /* r1 => r0, fp-0x10 */)
    //     0xc36ee8: mov             x0, x1
    //     0xc36eec: stur            x1, [fp, #-0x10]
    // 0xc36ef0: CheckStackOverflow
    //     0xc36ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36ef4: cmp             SP, x16
    //     0xc36ef8: b.ls            #0xc377dc
    // 0xc36efc: LoadField: r3 = r0->field_b
    //     0xc36efc: ldur            w3, [x0, #0xb]
    // 0xc36f00: DecompressPointer r3
    //     0xc36f00: add             x3, x3, HEAP, lsl #32
    // 0xc36f04: mov             x1, x3
    // 0xc36f08: stur            x3, [fp, #-8]
    // 0xc36f0c: r2 = "CBLC"
    //     0xc36f0c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "CBLC"
    //     0xc36f10: ldr             x2, [x2, #0x868]
    // 0xc36f14: r0 = _getValueOrData()
    //     0xc36f14: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc36f18: mov             x1, x0
    // 0xc36f1c: ldur            x0, [fp, #-8]
    // 0xc36f20: LoadField: r2 = r0->field_f
    //     0xc36f20: ldur            w2, [x0, #0xf]
    // 0xc36f24: DecompressPointer r2
    //     0xc36f24: add             x2, x2, HEAP, lsl #32
    // 0xc36f28: cmp             w2, w1
    // 0xc36f2c: b.ne            #0xc36f38
    // 0xc36f30: r3 = Null
    //     0xc36f30: mov             x3, NULL
    // 0xc36f34: b               #0xc36f3c
    // 0xc36f38: mov             x3, x1
    // 0xc36f3c: stur            x3, [fp, #-0x18]
    // 0xc36f40: cmp             w3, NULL
    // 0xc36f44: b.eq            #0xc377e4
    // 0xc36f48: mov             x1, x0
    // 0xc36f4c: r2 = "CBDT"
    //     0xc36f4c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33870] "CBDT"
    //     0xc36f50: ldr             x2, [x2, #0x870]
    // 0xc36f54: r0 = _getValueOrData()
    //     0xc36f54: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc36f58: mov             x1, x0
    // 0xc36f5c: ldur            x0, [fp, #-8]
    // 0xc36f60: LoadField: r2 = r0->field_f
    //     0xc36f60: ldur            w2, [x0, #0xf]
    // 0xc36f64: DecompressPointer r2
    //     0xc36f64: add             x2, x2, HEAP, lsl #32
    // 0xc36f68: cmp             w2, w1
    // 0xc36f6c: b.ne            #0xc36f78
    // 0xc36f70: r7 = Null
    //     0xc36f70: mov             x7, NULL
    // 0xc36f74: b               #0xc36f7c
    // 0xc36f78: mov             x7, x1
    // 0xc36f7c: ldur            x2, [fp, #-0x10]
    // 0xc36f80: ldur            x0, [fp, #-0x18]
    // 0xc36f84: r6 = 4278255360
    //     0xc36f84: movz            x6, #0xff00
    //     0xc36f88: movk            x6, #0xff00, lsl #16
    // 0xc36f8c: r5 = 16711935
    //     0xc36f8c: movz            x5, #0xff
    //     0xc36f90: movk            x5, #0xff, lsl #16
    // 0xc36f94: r4 = 4294901760
    //     0xc36f94: orr             x4, xzr, #0xffff0000
    // 0xc36f98: r3 = 65535
    //     0xc36f98: orr             x3, xzr, #0xffff
    // 0xc36f9c: cmp             w7, NULL
    // 0xc36fa0: b.eq            #0xc377e8
    // 0xc36fa4: LoadField: r8 = r2->field_7
    //     0xc36fa4: ldur            w8, [x2, #7]
    // 0xc36fa8: DecompressPointer r8
    //     0xc36fa8: add             x8, x8, HEAP, lsl #32
    // 0xc36fac: stur            x8, [fp, #-0x100]
    // 0xc36fb0: r9 = LoadInt32Instr(r0)
    //     0xc36fb0: sbfx            x9, x0, #1, #0x1f
    //     0xc36fb4: tbz             w0, #0, #0xc36fbc
    //     0xc36fb8: ldur            x9, [x0, #7]
    // 0xc36fbc: stur            x9, [fp, #-0x20]
    // 0xc36fc0: add             x10, x9, #4
    // 0xc36fc4: LoadField: r0 = r8->field_13
    //     0xc36fc4: ldur            w0, [x8, #0x13]
    // 0xc36fc8: r11 = LoadInt32Instr(r0)
    //     0xc36fc8: sbfx            x11, x0, #1, #0x1f
    // 0xc36fcc: stur            x11, [fp, #-0x50]
    // 0xc36fd0: sub             x12, x11, #3
    // 0xc36fd4: mov             x0, x12
    // 0xc36fd8: mov             x1, x10
    // 0xc36fdc: stur            x12, [fp, #-0x68]
    // 0xc36fe0: cmp             x1, x0
    // 0xc36fe4: b.hs            #0xc377ec
    // 0xc36fe8: ArrayLoad: r13 = r8[0]  ; List_4
    //     0xc36fe8: ldur            w13, [x8, #0x17]
    // 0xc36fec: DecompressPointer r13
    //     0xc36fec: add             x13, x13, HEAP, lsl #32
    // 0xc36ff0: stur            x13, [fp, #-0x18]
    // 0xc36ff4: LoadField: r0 = r8->field_1b
    //     0xc36ff4: ldur            w0, [x8, #0x1b]
    // 0xc36ff8: r14 = LoadInt32Instr(r0)
    //     0xc36ff8: sbfx            x14, x0, #1, #0x1f
    // 0xc36ffc: stur            x14, [fp, #-0xf8]
    // 0xc37000: add             x0, x14, x10
    // 0xc37004: LoadField: r1 = r13->field_7
    //     0xc37004: ldur            x1, [x13, #7]
    // 0xc37008: ldr             w10, [x1, x0]
    // 0xc3700c: and             x0, x10, x6
    // 0xc37010: ubfx            x0, x0, #0, #0x20
    // 0xc37014: asr             x1, x0, #8
    // 0xc37018: and             x0, x10, x5
    // 0xc3701c: ubfx            x0, x0, #0, #0x20
    // 0xc37020: lsl             x10, x0, #8
    // 0xc37024: orr             x0, x1, x10
    // 0xc37028: mov             x1, x0
    // 0xc3702c: ubfx            x1, x1, #0, #0x20
    // 0xc37030: and             x10, x1, x4
    // 0xc37034: ubfx            x10, x10, #0, #0x20
    // 0xc37038: asr             x1, x10, #0x10
    // 0xc3703c: ubfx            x0, x0, #0, #0x20
    // 0xc37040: and             x10, x0, x3
    // 0xc37044: ubfx            x10, x10, #0, #0x20
    // 0xc37048: lsl             x0, x10, #0x10
    // 0xc3704c: orr             x10, x1, x0
    // 0xc37050: stur            x10, [fp, #-0x28]
    // 0xc37054: add             x0, x9, #8
    // 0xc37058: sub             x19, x11, #1
    // 0xc3705c: stur            x19, [fp, #-0xf0]
    // 0xc37060: r20 = LoadInt32Instr(r7)
    //     0xc37060: sbfx            x20, x7, #1, #0x1f
    //     0xc37064: tbz             w7, #0, #0xc3706c
    //     0xc37068: ldur            x20, [x7, #7]
    // 0xc3706c: stur            x20, [fp, #-0xe8]
    // 0xc37070: LoadField: r7 = r2->field_23
    //     0xc37070: ldur            w7, [x2, #0x23]
    // 0xc37074: DecompressPointer r7
    //     0xc37074: add             x7, x7, HEAP, lsl #32
    // 0xc37078: stur            x7, [fp, #-8]
    // 0xc3707c: mov             x25, x0
    // 0xc37080: r24 = 0
    //     0xc37080: movz            x24, #0
    // 0xc37084: r23 = 65280
    //     0xc37084: orr             x23, xzr, #0xff00
    // 0xc37088: r2 = 255
    //     0xc37088: movz            x2, #0xff
    // 0xc3708c: stur            x25, [fp, #-0xd8]
    // 0xc37090: stur            x24, [fp, #-0xe0]
    // 0xc37094: CheckStackOverflow
    //     0xc37094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc37098: cmp             SP, x16
    //     0xc3709c: b.ls            #0xc377f0
    // 0xc370a0: cmp             x24, x10
    // 0xc370a4: b.ge            #0xc377cc
    // 0xc370a8: mov             x0, x12
    // 0xc370ac: mov             x1, x25
    // 0xc370b0: cmp             x1, x0
    // 0xc370b4: b.hs            #0xc377f8
    // 0xc370b8: add             x0, x14, x25
    // 0xc370bc: LoadField: r1 = r13->field_7
    //     0xc370bc: ldur            x1, [x13, #7]
    // 0xc370c0: ldr             w7, [x1, x0]
    // 0xc370c4: and             x0, x7, x6
    // 0xc370c8: ubfx            x0, x0, #0, #0x20
    // 0xc370cc: asr             x1, x0, #8
    // 0xc370d0: and             x0, x7, x5
    // 0xc370d4: ubfx            x0, x0, #0, #0x20
    // 0xc370d8: lsl             x7, x0, #8
    // 0xc370dc: orr             x0, x1, x7
    // 0xc370e0: mov             x1, x0
    // 0xc370e4: ubfx            x1, x1, #0, #0x20
    // 0xc370e8: and             x7, x1, x4
    // 0xc370ec: ubfx            x7, x7, #0, #0x20
    // 0xc370f0: asr             x1, x7, #0x10
    // 0xc370f4: ubfx            x0, x0, #0, #0x20
    // 0xc370f8: and             x7, x0, x3
    // 0xc370fc: ubfx            x7, x7, #0, #0x20
    // 0xc37100: lsl             x0, x7, #0x10
    // 0xc37104: orr             x7, x1, x0
    // 0xc37108: add             x0, x9, x7
    // 0xc3710c: stur            x0, [fp, #-0x60]
    // 0xc37110: add             x7, x25, #8
    // 0xc37114: mov             x9, x0
    // 0xc37118: mov             x0, x12
    // 0xc3711c: mov             x1, x7
    // 0xc37120: cmp             x1, x0
    // 0xc37124: b.hs            #0xc377fc
    // 0xc37128: add             x0, x14, x7
    // 0xc3712c: LoadField: r1 = r13->field_7
    //     0xc3712c: ldur            x1, [x13, #7]
    // 0xc37130: ldr             w7, [x1, x0]
    // 0xc37134: and             x0, x7, x6
    // 0xc37138: ubfx            x0, x0, #0, #0x20
    // 0xc3713c: asr             x1, x0, #8
    // 0xc37140: and             x0, x7, x5
    // 0xc37144: ubfx            x0, x0, #0, #0x20
    // 0xc37148: lsl             x7, x0, #8
    // 0xc3714c: orr             x0, x1, x7
    // 0xc37150: mov             x1, x0
    // 0xc37154: ubfx            x1, x1, #0, #0x20
    // 0xc37158: and             x7, x1, x4
    // 0xc3715c: ubfx            x7, x7, #0, #0x20
    // 0xc37160: asr             x1, x7, #0x10
    // 0xc37164: ubfx            x0, x0, #0, #0x20
    // 0xc37168: and             x7, x0, x3
    // 0xc3716c: ubfx            x7, x7, #0, #0x20
    // 0xc37170: lsl             x0, x7, #0x10
    // 0xc37174: orr             x7, x1, x0
    // 0xc37178: stur            x7, [fp, #-0x48]
    // 0xc3717c: add             x1, x25, #0xc
    // 0xc37180: mov             x0, x11
    // 0xc37184: mov             x10, x1
    // 0xc37188: cmp             x1, x0
    // 0xc3718c: b.hs            #0xc37800
    // 0xc37190: add             x0, x14, x10
    // 0xc37194: LoadField: r1 = r13->field_7
    //     0xc37194: ldur            x1, [x13, #7]
    // 0xc37198: ldrsb           x10, [x1, x0]
    // 0xc3719c: stur            x10, [fp, #-0x30]
    // 0xc371a0: add             x1, x25, #0xd
    // 0xc371a4: mov             x0, x11
    // 0xc371a8: mov             x10, x1
    // 0xc371ac: cmp             x1, x0
    // 0xc371b0: b.hs            #0xc37804
    // 0xc371b4: add             x0, x14, x10
    // 0xc371b8: LoadField: r1 = r13->field_7
    //     0xc371b8: ldur            x1, [x13, #7]
    // 0xc371bc: ldrsb           x10, [x1, x0]
    // 0xc371c0: stur            x10, [fp, #-0x38]
    // 0xc371c4: mov             x1, x9
    // 0xc371c8: r0 = 0
    //     0xc371c8: movz            x0, #0
    // 0xc371cc: stur            x0, [fp, #-0x40]
    // 0xc371d0: stur            x1, [fp, #-0x58]
    // 0xc371d4: CheckStackOverflow
    //     0xc371d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc371d8: cmp             SP, x16
    //     0xc371dc: b.ls            #0xc37808
    // 0xc371e0: cmp             x0, x7
    // 0xc371e4: b.ge            #0xc3777c
    // 0xc371e8: mov             x0, x19
    // 0xc371ec: mov             x10, x1
    // 0xc371f0: cmp             x1, x0
    // 0xc371f4: b.hs            #0xc37810
    // 0xc371f8: add             x0, x14, x10
    // 0xc371fc: LoadField: r1 = r13->field_7
    //     0xc371fc: ldur            x1, [x13, #7]
    // 0xc37200: ldrh            w7, [x1, x0]
    // 0xc37204: mov             x0, x7
    // 0xc37208: ubfx            x0, x0, #0, #0x20
    // 0xc3720c: and             x1, x0, x23
    // 0xc37210: ubfx            x1, x1, #0, #0x20
    // 0xc37214: asr             x0, x1, #8
    // 0xc37218: ubfx            x7, x7, #0, #0x20
    // 0xc3721c: and             x1, x7, x2
    // 0xc37220: ubfx            x1, x1, #0, #0x20
    // 0xc37224: lsl             x7, x1, #8
    // 0xc37228: orr             x1, x0, x7
    // 0xc3722c: stur            x1, [fp, #-0xc8]
    // 0xc37230: add             x7, x10, #2
    // 0xc37234: mov             x0, x19
    // 0xc37238: mov             x11, x1
    // 0xc3723c: mov             x1, x7
    // 0xc37240: cmp             x1, x0
    // 0xc37244: b.hs            #0xc37814
    // 0xc37248: add             x0, x14, x7
    // 0xc3724c: LoadField: r1 = r13->field_7
    //     0xc3724c: ldur            x1, [x13, #7]
    // 0xc37250: ldrh            w7, [x1, x0]
    // 0xc37254: mov             x0, x7
    // 0xc37258: ubfx            x0, x0, #0, #0x20
    // 0xc3725c: and             x1, x0, x23
    // 0xc37260: ubfx            x1, x1, #0, #0x20
    // 0xc37264: asr             x0, x1, #8
    // 0xc37268: ubfx            x7, x7, #0, #0x20
    // 0xc3726c: and             x1, x7, x2
    // 0xc37270: ubfx            x1, x1, #0, #0x20
    // 0xc37274: lsl             x7, x1, #8
    // 0xc37278: orr             x1, x0, x7
    // 0xc3727c: stur            x1, [fp, #-0xd0]
    // 0xc37280: add             x7, x10, #4
    // 0xc37284: ldur            x0, [fp, #-0x68]
    // 0xc37288: mov             x10, x1
    // 0xc3728c: mov             x1, x7
    // 0xc37290: cmp             x1, x0
    // 0xc37294: b.hs            #0xc37818
    // 0xc37298: add             x0, x14, x7
    // 0xc3729c: LoadField: r1 = r13->field_7
    //     0xc3729c: ldur            x1, [x13, #7]
    // 0xc372a0: ldr             w7, [x1, x0]
    // 0xc372a4: and             x0, x7, x6
    // 0xc372a8: ubfx            x0, x0, #0, #0x20
    // 0xc372ac: asr             x1, x0, #8
    // 0xc372b0: and             x0, x7, x5
    // 0xc372b4: ubfx            x0, x0, #0, #0x20
    // 0xc372b8: lsl             x7, x0, #8
    // 0xc372bc: orr             x0, x1, x7
    // 0xc372c0: mov             x1, x0
    // 0xc372c4: ubfx            x1, x1, #0, #0x20
    // 0xc372c8: and             x7, x1, x4
    // 0xc372cc: ubfx            x7, x7, #0, #0x20
    // 0xc372d0: asr             x1, x7, #0x10
    // 0xc372d4: ubfx            x0, x0, #0, #0x20
    // 0xc372d8: and             x7, x0, x3
    // 0xc372dc: ubfx            x7, x7, #0, #0x20
    // 0xc372e0: lsl             x0, x7, #0x10
    // 0xc372e4: orr             x7, x1, x0
    // 0xc372e8: add             x1, x9, x7
    // 0xc372ec: mov             x0, x19
    // 0xc372f0: mov             x7, x1
    // 0xc372f4: stur            x1, [fp, #-0x70]
    // 0xc372f8: cmp             x1, x0
    // 0xc372fc: b.hs            #0xc3781c
    // 0xc37300: add             x0, x14, x7
    // 0xc37304: LoadField: r1 = r13->field_7
    //     0xc37304: ldur            x1, [x13, #7]
    // 0xc37308: ldrh            w9, [x1, x0]
    // 0xc3730c: mov             x0, x9
    // 0xc37310: ubfx            x0, x0, #0, #0x20
    // 0xc37314: and             x1, x0, x23
    // 0xc37318: ubfx            x1, x1, #0, #0x20
    // 0xc3731c: asr             x0, x1, #8
    // 0xc37320: ubfx            x9, x9, #0, #0x20
    // 0xc37324: and             x1, x9, x2
    // 0xc37328: ubfx            x1, x1, #0, #0x20
    // 0xc3732c: lsl             x9, x1, #8
    // 0xc37330: orr             x1, x0, x9
    // 0xc37334: add             x9, x7, #2
    // 0xc37338: mov             x0, x19
    // 0xc3733c: mov             x12, x1
    // 0xc37340: mov             x1, x9
    // 0xc37344: cmp             x1, x0
    // 0xc37348: b.hs            #0xc37820
    // 0xc3734c: add             x0, x14, x9
    // 0xc37350: LoadField: r1 = r13->field_7
    //     0xc37350: ldur            x1, [x13, #7]
    // 0xc37354: ldrh            w9, [x1, x0]
    // 0xc37358: mov             x0, x9
    // 0xc3735c: ubfx            x0, x0, #0, #0x20
    // 0xc37360: and             x1, x0, x23
    // 0xc37364: ubfx            x1, x1, #0, #0x20
    // 0xc37368: asr             x0, x1, #8
    // 0xc3736c: ubfx            x9, x9, #0, #0x20
    // 0xc37370: and             x1, x9, x2
    // 0xc37374: ubfx            x1, x1, #0, #0x20
    // 0xc37378: lsl             x9, x1, #8
    // 0xc3737c: orr             x1, x0, x9
    // 0xc37380: stur            x1, [fp, #-0x78]
    // 0xc37384: add             x9, x7, #4
    // 0xc37388: ldur            x0, [fp, #-0x68]
    // 0xc3738c: mov             x2, x1
    // 0xc37390: mov             x1, x9
    // 0xc37394: cmp             x1, x0
    // 0xc37398: b.hs            #0xc37824
    // 0xc3739c: add             x0, x14, x9
    // 0xc373a0: LoadField: r1 = r13->field_7
    //     0xc373a0: ldur            x1, [x13, #7]
    // 0xc373a4: ldr             w9, [x1, x0]
    // 0xc373a8: and             x0, x9, x6
    // 0xc373ac: ubfx            x0, x0, #0, #0x20
    // 0xc373b0: asr             x1, x0, #8
    // 0xc373b4: and             x0, x9, x5
    // 0xc373b8: ubfx            x0, x0, #0, #0x20
    // 0xc373bc: lsl             x9, x0, #8
    // 0xc373c0: orr             x0, x1, x9
    // 0xc373c4: mov             x1, x0
    // 0xc373c8: ubfx            x1, x1, #0, #0x20
    // 0xc373cc: and             x9, x1, x4
    // 0xc373d0: ubfx            x9, x9, #0, #0x20
    // 0xc373d4: asr             x1, x9, #0x10
    // 0xc373d8: ubfx            x0, x0, #0, #0x20
    // 0xc373dc: and             x9, x0, x3
    // 0xc373e0: ubfx            x9, x9, #0, #0x20
    // 0xc373e4: lsl             x0, x9, #0x10
    // 0xc373e8: orr             x9, x1, x0
    // 0xc373ec: add             x0, x20, x9
    // 0xc373f0: stur            x0, [fp, #-0x80]
    // 0xc373f4: cmp             x12, #1
    // 0xc373f8: b.ne            #0xc3771c
    // 0xc373fc: mov             x9, x10
    // 0xc37400: mov             x10, x11
    // 0xc37404: stur            x11, [fp, #-0xc0]
    // 0xc37408: CheckStackOverflow
    //     0xc37408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3740c: cmp             SP, x16
    //     0xc37410: b.ls            #0xc37828
    // 0xc37414: cmp             x11, x9
    // 0xc37418: b.gt            #0xc3771c
    // 0xc3741c: sub             x1, x11, x10
    // 0xc37420: add             x12, x1, #2
    // 0xc37424: lsl             x1, x12, #2
    // 0xc37428: add             x12, x7, x1
    // 0xc3742c: mov             x7, x0
    // 0xc37430: ldur            x0, [fp, #-0x68]
    // 0xc37434: mov             x1, x12
    // 0xc37438: cmp             x1, x0
    // 0xc3743c: b.hs            #0xc37830
    // 0xc37440: add             x0, x14, x12
    // 0xc37444: LoadField: r1 = r13->field_7
    //     0xc37444: ldur            x1, [x13, #7]
    // 0xc37448: ldr             w12, [x1, x0]
    // 0xc3744c: and             x0, x12, x6
    // 0xc37450: ubfx            x0, x0, #0, #0x20
    // 0xc37454: asr             x1, x0, #8
    // 0xc37458: and             x0, x12, x5
    // 0xc3745c: ubfx            x0, x0, #0, #0x20
    // 0xc37460: lsl             x12, x0, #8
    // 0xc37464: orr             x0, x1, x12
    // 0xc37468: mov             x1, x0
    // 0xc3746c: ubfx            x1, x1, #0, #0x20
    // 0xc37470: and             x12, x1, x4
    // 0xc37474: ubfx            x12, x12, #0, #0x20
    // 0xc37478: asr             x1, x12, #0x10
    // 0xc3747c: ubfx            x0, x0, #0, #0x20
    // 0xc37480: and             x12, x0, x3
    // 0xc37484: ubfx            x12, x12, #0, #0x20
    // 0xc37488: lsl             x0, x12, #0x10
    // 0xc3748c: orr             x12, x1, x0
    // 0xc37490: add             x1, x7, x12
    // 0xc37494: cmp             x2, #0x11
    // 0xc37498: b.ne            #0xc376c4
    // 0xc3749c: ldur            x0, [fp, #-0x50]
    // 0xc374a0: mov             x12, x1
    // 0xc374a4: cmp             x1, x0
    // 0xc374a8: b.hs            #0xc37834
    // 0xc374ac: add             x0, x14, x12
    // 0xc374b0: stur            x0, [fp, #-0x88]
    // 0xc374b4: LoadField: r1 = r13->field_7
    //     0xc374b4: ldur            x1, [x13, #7]
    // 0xc374b8: ldrb            w2, [x1, x0]
    // 0xc374bc: stur            x2, [fp, #-0xa8]
    // 0xc374c0: add             x1, x12, #1
    // 0xc374c4: ldur            x0, [fp, #-0x50]
    // 0xc374c8: mov             x7, x1
    // 0xc374cc: cmp             x1, x0
    // 0xc374d0: b.hs            #0xc37838
    // 0xc374d4: add             x0, x14, x7
    // 0xc374d8: LoadField: r1 = r13->field_7
    //     0xc374d8: ldur            x1, [x13, #7]
    // 0xc374dc: ldrb            w7, [x1, x0]
    // 0xc374e0: stur            x7, [fp, #-0x90]
    // 0xc374e4: add             x1, x12, #2
    // 0xc374e8: ldur            x0, [fp, #-0x50]
    // 0xc374ec: mov             x7, x1
    // 0xc374f0: cmp             x1, x0
    // 0xc374f4: b.hs            #0xc3783c
    // 0xc374f8: add             x0, x14, x7
    // 0xc374fc: LoadField: r1 = r13->field_7
    //     0xc374fc: ldur            x1, [x13, #7]
    // 0xc37500: ldrsb           x7, [x1, x0]
    // 0xc37504: stur            x7, [fp, #-0x98]
    // 0xc37508: add             x1, x12, #3
    // 0xc3750c: ldur            x0, [fp, #-0x50]
    // 0xc37510: mov             x7, x1
    // 0xc37514: cmp             x1, x0
    // 0xc37518: b.hs            #0xc37840
    // 0xc3751c: add             x0, x14, x7
    // 0xc37520: LoadField: r1 = r13->field_7
    //     0xc37520: ldur            x1, [x13, #7]
    // 0xc37524: ldrsb           x7, [x1, x0]
    // 0xc37528: stur            x7, [fp, #-0xa0]
    // 0xc3752c: add             x1, x12, #4
    // 0xc37530: ldur            x0, [fp, #-0x50]
    // 0xc37534: mov             x7, x1
    // 0xc37538: cmp             x1, x0
    // 0xc3753c: b.hs            #0xc37844
    // 0xc37540: add             x0, x14, x7
    // 0xc37544: LoadField: r1 = r13->field_7
    //     0xc37544: ldur            x1, [x13, #7]
    // 0xc37548: ldrb            w7, [x1, x0]
    // 0xc3754c: stur            x7, [fp, #-0xb8]
    // 0xc37550: add             x1, x12, #5
    // 0xc37554: ldur            x0, [fp, #-0x68]
    // 0xc37558: mov             x12, x1
    // 0xc3755c: cmp             x1, x0
    // 0xc37560: b.hs            #0xc37848
    // 0xc37564: add             x0, x14, x12
    // 0xc37568: LoadField: r1 = r13->field_7
    //     0xc37568: ldur            x1, [x13, #7]
    // 0xc3756c: ldr             w12, [x1, x0]
    // 0xc37570: and             x0, x12, x6
    // 0xc37574: ubfx            x0, x0, #0, #0x20
    // 0xc37578: asr             x1, x0, #8
    // 0xc3757c: and             x0, x12, x5
    // 0xc37580: ubfx            x0, x0, #0, #0x20
    // 0xc37584: lsl             x12, x0, #8
    // 0xc37588: orr             x0, x1, x12
    // 0xc3758c: mov             x1, x0
    // 0xc37590: ubfx            x1, x1, #0, #0x20
    // 0xc37594: and             x12, x1, x4
    // 0xc37598: ubfx            x12, x12, #0, #0x20
    // 0xc3759c: asr             x1, x12, #0x10
    // 0xc375a0: ubfx            x0, x0, #0, #0x20
    // 0xc375a4: and             x12, x0, x3
    // 0xc375a8: ubfx            x12, x12, #0, #0x20
    // 0xc375ac: lsl             x0, x12, #0x10
    // 0xc375b0: orr             x12, x1, x0
    // 0xc375b4: stur            x12, [fp, #-0xb0]
    // 0xc375b8: r0 = LoadClassIdInstr(r8)
    //     0xc375b8: ldur            x0, [x8, #-1]
    //     0xc375bc: ubfx            x0, x0, #0xc, #0x14
    // 0xc375c0: mov             x1, x8
    // 0xc375c4: r0 = GDT[cid_x0 + -0xf60]()
    //     0xc375c4: sub             lr, x0, #0xf60
    //     0xc375c8: ldr             lr, [x21, lr, lsl #3]
    //     0xc375cc: blr             lr
    // 0xc375d0: mov             x2, x0
    // 0xc375d4: ldur            x0, [fp, #-0x88]
    // 0xc375d8: add             x3, x0, #9
    // 0xc375dc: ldur            x4, [fp, #-0xb0]
    // 0xc375e0: r0 = BoxInt64Instr(r4)
    //     0xc375e0: sbfiz           x0, x4, #1, #0x1f
    //     0xc375e4: cmp             x4, x0, asr #1
    //     0xc375e8: b.eq            #0xc375f4
    //     0xc375ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc375f0: stur            x4, [x0, #7]
    // 0xc375f4: mov             x4, x0
    // 0xc375f8: r0 = BoxInt64Instr(r3)
    //     0xc375f8: sbfiz           x0, x3, #1, #0x1f
    //     0xc375fc: cmp             x3, x0, asr #1
    //     0xc37600: b.eq            #0xc3760c
    //     0xc37604: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc37608: stur            x3, [x0, #7]
    // 0xc3760c: r1 = LoadClassIdInstr(r2)
    //     0xc3760c: ldur            x1, [x2, #-1]
    //     0xc37610: ubfx            x1, x1, #0xc, #0x14
    // 0xc37614: stp             x4, x0, [SP]
    // 0xc37618: mov             x0, x1
    // 0xc3761c: mov             x1, x2
    // 0xc37620: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xc37620: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xc37624: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc37624: sub             lr, x0, #1, lsl #12
    //     0xc37628: ldr             lr, [x21, lr, lsl #3]
    //     0xc3762c: blr             lr
    // 0xc37630: stur            x0, [fp, #-0x10]
    // 0xc37634: r0 = TtfBitmapInfo()
    //     0xc37634: bl              #0xc3784c  ; AllocateTtfBitmapInfoStub -> TtfBitmapInfo (size=0x44)
    // 0xc37638: mov             x3, x0
    // 0xc3763c: ldur            x0, [fp, #-0x10]
    // 0xc37640: r17 = -264
    //     0xc37640: movn            x17, #0x107
    // 0xc37644: str             x3, [fp, x17]
    // 0xc37648: StoreField: r3->field_7 = r0
    //     0xc37648: stur            w0, [x3, #7]
    // 0xc3764c: ldur            x0, [fp, #-0xa8]
    // 0xc37650: StoreField: r3->field_b = r0
    //     0xc37650: stur            x0, [x3, #0xb]
    // 0xc37654: ldur            x0, [fp, #-0x90]
    // 0xc37658: StoreField: r3->field_13 = r0
    //     0xc37658: stur            x0, [x3, #0x13]
    // 0xc3765c: ldur            x0, [fp, #-0x98]
    // 0xc37660: StoreField: r3->field_1b = r0
    //     0xc37660: stur            x0, [x3, #0x1b]
    // 0xc37664: ldur            x0, [fp, #-0xa0]
    // 0xc37668: StoreField: r3->field_23 = r0
    //     0xc37668: stur            x0, [x3, #0x23]
    // 0xc3766c: ldur            x0, [fp, #-0xb8]
    // 0xc37670: StoreField: r3->field_2b = r0
    //     0xc37670: stur            x0, [x3, #0x2b]
    // 0xc37674: ldur            x4, [fp, #-0x30]
    // 0xc37678: StoreField: r3->field_33 = r4
    //     0xc37678: stur            x4, [x3, #0x33]
    // 0xc3767c: ldur            x5, [fp, #-0x38]
    // 0xc37680: StoreField: r3->field_3b = r5
    //     0xc37680: stur            x5, [x3, #0x3b]
    // 0xc37684: ldur            x6, [fp, #-0xc0]
    // 0xc37688: r0 = BoxInt64Instr(r6)
    //     0xc37688: sbfiz           x0, x6, #1, #0x1f
    //     0xc3768c: cmp             x6, x0, asr #1
    //     0xc37690: b.eq            #0xc3769c
    //     0xc37694: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc37698: stur            x6, [x0, #7]
    // 0xc3769c: ldur            x1, [fp, #-8]
    // 0xc376a0: mov             x2, x0
    // 0xc376a4: stur            x0, [fp, #-0x10]
    // 0xc376a8: r0 = _hashCode()
    //     0xc376a8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc376ac: ldur            x1, [fp, #-8]
    // 0xc376b0: ldur            x2, [fp, #-0x10]
    // 0xc376b4: r17 = -264
    //     0xc376b4: movn            x17, #0x107
    // 0xc376b8: ldr             x3, [fp, x17]
    // 0xc376bc: mov             x5, x0
    // 0xc376c0: r0 = _set()
    //     0xc376c0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc376c4: ldur            x1, [fp, #-0xc0]
    // 0xc376c8: add             x11, x1, #1
    // 0xc376cc: ldur            x8, [fp, #-0x100]
    // 0xc376d0: ldur            x25, [fp, #-0xd8]
    // 0xc376d4: ldur            x24, [fp, #-0xe0]
    // 0xc376d8: ldur            x7, [fp, #-0x70]
    // 0xc376dc: ldur            x0, [fp, #-0x80]
    // 0xc376e0: ldur            x19, [fp, #-0xf0]
    // 0xc376e4: ldur            x10, [fp, #-0xc8]
    // 0xc376e8: ldur            x9, [fp, #-0xd0]
    // 0xc376ec: ldur            x2, [fp, #-0x78]
    // 0xc376f0: ldur            x13, [fp, #-0x18]
    // 0xc376f4: ldur            x20, [fp, #-0xe8]
    // 0xc376f8: ldur            x14, [fp, #-0xf8]
    // 0xc376fc: r6 = 4278255360
    //     0xc376fc: movz            x6, #0xff00
    //     0xc37700: movk            x6, #0xff00, lsl #16
    // 0xc37704: r5 = 16711935
    //     0xc37704: movz            x5, #0xff
    //     0xc37708: movk            x5, #0xff, lsl #16
    // 0xc3770c: r4 = 4294901760
    //     0xc3770c: orr             x4, xzr, #0xffff0000
    // 0xc37710: r3 = 65535
    //     0xc37710: orr             x3, xzr, #0xffff
    // 0xc37714: r23 = 65280
    //     0xc37714: orr             x23, xzr, #0xff00
    // 0xc37718: b               #0xc37404
    // 0xc3771c: ldur            x1, [fp, #-0x58]
    // 0xc37720: ldur            x2, [fp, #-0x40]
    // 0xc37724: add             x3, x1, #8
    // 0xc37728: add             x0, x2, #1
    // 0xc3772c: mov             x1, x3
    // 0xc37730: ldur            x8, [fp, #-0x100]
    // 0xc37734: ldur            x25, [fp, #-0xd8]
    // 0xc37738: ldur            x24, [fp, #-0xe0]
    // 0xc3773c: ldur            x9, [fp, #-0x60]
    // 0xc37740: ldur            x19, [fp, #-0xf0]
    // 0xc37744: ldur            x7, [fp, #-0x48]
    // 0xc37748: ldur            x13, [fp, #-0x18]
    // 0xc3774c: ldur            x20, [fp, #-0xe8]
    // 0xc37750: ldur            x14, [fp, #-0xf8]
    // 0xc37754: ldur            x10, [fp, #-0x38]
    // 0xc37758: r6 = 4278255360
    //     0xc37758: movz            x6, #0xff00
    //     0xc3775c: movk            x6, #0xff00, lsl #16
    // 0xc37760: r5 = 16711935
    //     0xc37760: movz            x5, #0xff
    //     0xc37764: movk            x5, #0xff, lsl #16
    // 0xc37768: r4 = 4294901760
    //     0xc37768: orr             x4, xzr, #0xffff0000
    // 0xc3776c: r3 = 65535
    //     0xc3776c: orr             x3, xzr, #0xffff
    // 0xc37770: r23 = 65280
    //     0xc37770: orr             x23, xzr, #0xff00
    // 0xc37774: r2 = 255
    //     0xc37774: movz            x2, #0xff
    // 0xc37778: b               #0xc371cc
    // 0xc3777c: mov             x2, x25
    // 0xc37780: mov             x1, x24
    // 0xc37784: add             x25, x2, #0x30
    // 0xc37788: add             x24, x1, #1
    // 0xc3778c: ldur            x8, [fp, #-0x100]
    // 0xc37790: ldur            x19, [fp, #-0xf0]
    // 0xc37794: ldur            x12, [fp, #-0x68]
    // 0xc37798: ldur            x13, [fp, #-0x18]
    // 0xc3779c: ldur            x10, [fp, #-0x28]
    // 0xc377a0: ldur            x9, [fp, #-0x20]
    // 0xc377a4: ldur            x20, [fp, #-0xe8]
    // 0xc377a8: ldur            x11, [fp, #-0x50]
    // 0xc377ac: ldur            x14, [fp, #-0xf8]
    // 0xc377b0: r6 = 4278255360
    //     0xc377b0: movz            x6, #0xff00
    //     0xc377b4: movk            x6, #0xff00, lsl #16
    // 0xc377b8: r5 = 16711935
    //     0xc377b8: movz            x5, #0xff
    //     0xc377bc: movk            x5, #0xff, lsl #16
    // 0xc377c0: r4 = 4294901760
    //     0xc377c0: orr             x4, xzr, #0xffff0000
    // 0xc377c4: r3 = 65535
    //     0xc377c4: orr             x3, xzr, #0xffff
    // 0xc377c8: b               #0xc37084
    // 0xc377cc: r0 = Null
    //     0xc377cc: mov             x0, NULL
    // 0xc377d0: LeaveFrame
    //     0xc377d0: mov             SP, fp
    //     0xc377d4: ldp             fp, lr, [SP], #0x10
    // 0xc377d8: ret
    //     0xc377d8: ret             
    // 0xc377dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc377dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc377e0: b               #0xc36efc
    // 0xc377e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc377e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc377e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc377e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc377ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc377ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc377f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc377f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc377f4: b               #0xc370a0
    // 0xc377f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc377f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc377fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc377fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37800: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37800: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37804: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc37808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3780c: b               #0xc371e0
    // 0xc37810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37810: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37814: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37818: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37818: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc3781c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3781c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37820: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37820: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37824: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc37828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3782c: b               #0xc37414
    // 0xc37830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37830: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37834: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37834: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37838: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37838: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc3783c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3783c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37840: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37840: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37844: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37844: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc37848: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc37848: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseGlyphs(/* No info */) {
    // ** addr: 0xc37858, size: 0x9b4
    // 0xc37858: EnterFrame
    //     0xc37858: stp             fp, lr, [SP, #-0x10]!
    //     0xc3785c: mov             fp, SP
    // 0xc37860: AllocStack(0x100)
    //     0xc37860: sub             SP, SP, #0x100
    // 0xc37864: SetupParameters(TtfParser this /* r1 => r0, fp-0x10 */)
    //     0xc37864: mov             x0, x1
    //     0xc37868: stur            x1, [fp, #-0x10]
    // 0xc3786c: CheckStackOverflow
    //     0xc3786c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc37870: cmp             SP, x16
    //     0xc37874: b.ls            #0xc381b4
    // 0xc37878: LoadField: r3 = r0->field_b
    //     0xc37878: ldur            w3, [x0, #0xb]
    // 0xc3787c: DecompressPointer r3
    //     0xc3787c: add             x3, x3, HEAP, lsl #32
    // 0xc37880: mov             x1, x3
    // 0xc37884: stur            x3, [fp, #-8]
    // 0xc37888: r2 = "glyf"
    //     0xc37888: add             x2, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0xc3788c: ldr             x2, [x2, #0x860]
    // 0xc37890: r0 = _getValueOrData()
    //     0xc37890: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc37894: mov             x1, x0
    // 0xc37898: ldur            x0, [fp, #-8]
    // 0xc3789c: LoadField: r2 = r0->field_f
    //     0xc3789c: ldur            w2, [x0, #0xf]
    // 0xc378a0: DecompressPointer r2
    //     0xc378a0: add             x2, x2, HEAP, lsl #32
    // 0xc378a4: cmp             w2, w1
    // 0xc378a8: b.ne            #0xc378b4
    // 0xc378ac: r3 = Null
    //     0xc378ac: mov             x3, NULL
    // 0xc378b0: b               #0xc378b8
    // 0xc378b4: mov             x3, x1
    // 0xc378b8: stur            x3, [fp, #-0x18]
    // 0xc378bc: cmp             w3, NULL
    // 0xc378c0: b.eq            #0xc381bc
    // 0xc378c4: mov             x1, x0
    // 0xc378c8: r2 = "hmtx"
    //     0xc378c8: add             x2, PP, #0x33, lsl #12  ; [pp+0x33878] "hmtx"
    //     0xc378cc: ldr             x2, [x2, #0x878]
    // 0xc378d0: r0 = _getValueOrData()
    //     0xc378d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc378d4: mov             x1, x0
    // 0xc378d8: ldur            x0, [fp, #-8]
    // 0xc378dc: LoadField: r2 = r0->field_f
    //     0xc378dc: ldur            w2, [x0, #0xf]
    // 0xc378e0: DecompressPointer r2
    //     0xc378e0: add             x2, x2, HEAP, lsl #32
    // 0xc378e4: cmp             w2, w1
    // 0xc378e8: b.ne            #0xc378f4
    // 0xc378ec: r4 = Null
    //     0xc378ec: mov             x4, NULL
    // 0xc378f0: b               #0xc378f8
    // 0xc378f4: mov             x4, x1
    // 0xc378f8: ldur            x3, [fp, #-0x10]
    // 0xc378fc: ldur            x2, [fp, #-0x18]
    // 0xc37900: stur            x4, [fp, #-0x20]
    // 0xc37904: cmp             w4, NULL
    // 0xc37908: b.eq            #0xc381c0
    // 0xc3790c: mov             x1, x3
    // 0xc37910: r0 = unitsPerEm()
    //     0xc37910: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xc37914: ldur            x1, [fp, #-0x10]
    // 0xc37918: stur            x0, [fp, #-0x28]
    // 0xc3791c: r0 = numOfLongHorMetrics()
    //     0xc3791c: bl              #0x7bc650  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::numOfLongHorMetrics
    // 0xc37920: mov             x3, x0
    // 0xc37924: ldur            x2, [fp, #-0x10]
    // 0xc37928: stur            x3, [fp, #-0x70]
    // 0xc3792c: LoadField: r4 = r2->field_7
    //     0xc3792c: ldur            w4, [x2, #7]
    // 0xc37930: DecompressPointer r4
    //     0xc37930: add             x4, x4, HEAP, lsl #32
    // 0xc37934: sub             x0, x3, #1
    // 0xc37938: lsl             x1, x0, #2
    // 0xc3793c: ldur            x0, [fp, #-0x20]
    // 0xc37940: r5 = LoadInt32Instr(r0)
    //     0xc37940: sbfx            x5, x0, #1, #0x1f
    //     0xc37944: tbz             w0, #0, #0xc3794c
    //     0xc37948: ldur            x5, [x0, #7]
    // 0xc3794c: stur            x5, [fp, #-0x68]
    // 0xc37950: add             x6, x5, x1
    // 0xc37954: LoadField: r0 = r4->field_13
    //     0xc37954: ldur            w0, [x4, #0x13]
    // 0xc37958: r1 = LoadInt32Instr(r0)
    //     0xc37958: sbfx            x1, x0, #1, #0x1f
    // 0xc3795c: sub             x7, x1, #1
    // 0xc37960: mov             x0, x7
    // 0xc37964: mov             x1, x6
    // 0xc37968: stur            x7, [fp, #-0x60]
    // 0xc3796c: cmp             x1, x0
    // 0xc37970: b.hs            #0xc381c4
    // 0xc37974: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xc37974: ldur            w0, [x4, #0x17]
    // 0xc37978: DecompressPointer r0
    //     0xc37978: add             x0, x0, HEAP, lsl #32
    // 0xc3797c: stur            x0, [fp, #-0x58]
    // 0xc37980: LoadField: r1 = r4->field_1b
    //     0xc37980: ldur            w1, [x4, #0x1b]
    // 0xc37984: r4 = LoadInt32Instr(r1)
    //     0xc37984: sbfx            x4, x1, #1, #0x1f
    // 0xc37988: stur            x4, [fp, #-0x50]
    // 0xc3798c: add             x1, x4, x6
    // 0xc37990: LoadField: r6 = r0->field_7
    //     0xc37990: ldur            x6, [x0, #7]
    // 0xc37994: ldrh            w8, [x6, x1]
    // 0xc37998: mov             x1, x8
    // 0xc3799c: ubfx            x1, x1, #0, #0x20
    // 0xc379a0: r6 = 65280
    //     0xc379a0: orr             x6, xzr, #0xff00
    // 0xc379a4: and             x9, x1, x6
    // 0xc379a8: ubfx            x9, x9, #0, #0x20
    // 0xc379ac: asr             x1, x9, #8
    // 0xc379b0: ubfx            x8, x8, #0, #0x20
    // 0xc379b4: r9 = 255
    //     0xc379b4: movz            x9, #0xff
    // 0xc379b8: and             x10, x8, x9
    // 0xc379bc: ubfx            x10, x10, #0, #0x20
    // 0xc379c0: lsl             x8, x10, #8
    // 0xc379c4: orr             x10, x1, x8
    // 0xc379c8: stur            x10, [fp, #-0x48]
    // 0xc379cc: lsl             x1, x3, #2
    // 0xc379d0: add             x8, x5, x1
    // 0xc379d4: stur            x8, [fp, #-0x40]
    // 0xc379d8: LoadField: r11 = r2->field_1b
    //     0xc379d8: ldur            w11, [x2, #0x1b]
    // 0xc379dc: DecompressPointer r11
    //     0xc379dc: add             x11, x11, HEAP, lsl #32
    // 0xc379e0: stur            x11, [fp, #-0x38]
    // 0xc379e4: ArrayLoad: r12 = r2[0]  ; List_4
    //     0xc379e4: ldur            w12, [x2, #0x17]
    // 0xc379e8: DecompressPointer r12
    //     0xc379e8: add             x12, x12, HEAP, lsl #32
    // 0xc379ec: ldur            x1, [fp, #-0x18]
    // 0xc379f0: stur            x12, [fp, #-0x20]
    // 0xc379f4: r13 = LoadInt32Instr(r1)
    //     0xc379f4: sbfx            x13, x1, #1, #0x1f
    //     0xc379f8: tbz             w1, #0, #0xc37a00
    //     0xc379fc: ldur            x13, [x1, #7]
    // 0xc37a00: stur            x13, [fp, #-0x30]
    // 0xc37a04: LoadField: r14 = r2->field_1f
    //     0xc37a04: ldur            w14, [x2, #0x1f]
    // 0xc37a08: DecompressPointer r14
    //     0xc37a08: add             x14, x14, HEAP, lsl #32
    // 0xc37a0c: ldur            x1, [fp, #-0x28]
    // 0xc37a10: stur            x14, [fp, #-0x18]
    // 0xc37a14: scvtf           d0, x1
    // 0xc37a18: stur            d0, [fp, #-0xb0]
    // 0xc37a1c: scvtf           d1, x1
    // 0xc37a20: stur            d1, [fp, #-0xa8]
    // 0xc37a24: r20 = 0
    //     0xc37a24: movz            x20, #0
    // 0xc37a28: ldur            x19, [fp, #-8]
    // 0xc37a2c: stur            x20, [fp, #-0x28]
    // 0xc37a30: CheckStackOverflow
    //     0xc37a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc37a34: cmp             SP, x16
    //     0xc37a38: b.ls            #0xc381c8
    // 0xc37a3c: mov             x1, x19
    // 0xc37a40: r2 = "maxp"
    //     0xc37a40: add             x2, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0xc37a44: ldr             x2, [x2, #0x880]
    // 0xc37a48: r0 = _getValueOrData()
    //     0xc37a48: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc37a4c: ldur            x2, [fp, #-8]
    // 0xc37a50: LoadField: r1 = r2->field_f
    //     0xc37a50: ldur            w1, [x2, #0xf]
    // 0xc37a54: DecompressPointer r1
    //     0xc37a54: add             x1, x1, HEAP, lsl #32
    // 0xc37a58: cmp             w1, w0
    // 0xc37a5c: b.ne            #0xc37a64
    // 0xc37a60: r0 = Null
    //     0xc37a60: mov             x0, NULL
    // 0xc37a64: ldur            x7, [fp, #-0x28]
    // 0xc37a68: ldur            x3, [fp, #-0x58]
    // 0xc37a6c: ldur            x4, [fp, #-0x50]
    // 0xc37a70: r5 = 65280
    //     0xc37a70: orr             x5, xzr, #0xff00
    // 0xc37a74: r6 = 255
    //     0xc37a74: movz            x6, #0xff
    // 0xc37a78: cmp             w0, NULL
    // 0xc37a7c: b.eq            #0xc381d0
    // 0xc37a80: r1 = LoadInt32Instr(r0)
    //     0xc37a80: sbfx            x1, x0, #1, #0x1f
    //     0xc37a84: tbz             w0, #0, #0xc37a8c
    //     0xc37a88: ldur            x1, [x0, #7]
    // 0xc37a8c: add             x8, x1, #4
    // 0xc37a90: ldur            x0, [fp, #-0x60]
    // 0xc37a94: mov             x1, x8
    // 0xc37a98: cmp             x1, x0
    // 0xc37a9c: b.hs            #0xc381d4
    // 0xc37aa0: add             x0, x4, x8
    // 0xc37aa4: LoadField: r1 = r3->field_7
    //     0xc37aa4: ldur            x1, [x3, #7]
    // 0xc37aa8: ldrh            w8, [x1, x0]
    // 0xc37aac: mov             x0, x8
    // 0xc37ab0: ubfx            x0, x0, #0, #0x20
    // 0xc37ab4: and             x1, x0, x5
    // 0xc37ab8: ubfx            x1, x1, #0, #0x20
    // 0xc37abc: asr             x0, x1, #8
    // 0xc37ac0: ubfx            x8, x8, #0, #0x20
    // 0xc37ac4: and             x1, x8, x6
    // 0xc37ac8: ubfx            x1, x1, #0, #0x20
    // 0xc37acc: lsl             x8, x1, #8
    // 0xc37ad0: orr             x1, x0, x8
    // 0xc37ad4: cmp             x7, x1
    // 0xc37ad8: b.ge            #0xc381a4
    // 0xc37adc: ldur            x8, [fp, #-0x70]
    // 0xc37ae0: cmp             x7, x8
    // 0xc37ae4: b.ge            #0xc37b40
    // 0xc37ae8: ldur            x9, [fp, #-0x68]
    // 0xc37aec: lsl             x0, x7, #2
    // 0xc37af0: add             x10, x9, x0
    // 0xc37af4: ldur            x0, [fp, #-0x60]
    // 0xc37af8: mov             x1, x10
    // 0xc37afc: cmp             x1, x0
    // 0xc37b00: b.hs            #0xc381d8
    // 0xc37b04: add             x0, x4, x10
    // 0xc37b08: LoadField: r1 = r3->field_7
    //     0xc37b08: ldur            x1, [x3, #7]
    // 0xc37b0c: ldrh            w10, [x1, x0]
    // 0xc37b10: mov             x0, x10
    // 0xc37b14: ubfx            x0, x0, #0, #0x20
    // 0xc37b18: and             x1, x0, x5
    // 0xc37b1c: ubfx            x1, x1, #0, #0x20
    // 0xc37b20: asr             x0, x1, #8
    // 0xc37b24: ubfx            x10, x10, #0, #0x20
    // 0xc37b28: and             x1, x10, x6
    // 0xc37b2c: ubfx            x1, x1, #0, #0x20
    // 0xc37b30: lsl             x10, x1, #8
    // 0xc37b34: orr             x1, x0, x10
    // 0xc37b38: mov             x10, x1
    // 0xc37b3c: b               #0xc37b48
    // 0xc37b40: ldur            x9, [fp, #-0x68]
    // 0xc37b44: ldur            x10, [fp, #-0x48]
    // 0xc37b48: stur            x10, [fp, #-0xa0]
    // 0xc37b4c: cmp             x7, x8
    // 0xc37b50: b.ge            #0xc37bc4
    // 0xc37b54: r12 = 32767
    //     0xc37b54: orr             x12, xzr, #0x7fff
    // 0xc37b58: r11 = 32768
    //     0xc37b58: movz            x11, #0x8000
    // 0xc37b5c: lsl             x0, x7, #2
    // 0xc37b60: add             x1, x9, x0
    // 0xc37b64: add             x13, x1, #2
    // 0xc37b68: ldur            x0, [fp, #-0x60]
    // 0xc37b6c: mov             x1, x13
    // 0xc37b70: cmp             x1, x0
    // 0xc37b74: b.hs            #0xc381dc
    // 0xc37b78: add             x0, x4, x13
    // 0xc37b7c: LoadField: r1 = r3->field_7
    //     0xc37b7c: ldur            x1, [x3, #7]
    // 0xc37b80: ldrsh           x13, [x1, x0]
    // 0xc37b84: mov             x0, x13
    // 0xc37b88: ubfx            x0, x0, #0, #0x20
    // 0xc37b8c: and             x1, x0, x5
    // 0xc37b90: lsr             w0, w1, #8
    // 0xc37b94: ubfx            x13, x13, #0, #0x20
    // 0xc37b98: and             x1, x13, x6
    // 0xc37b9c: lsl             w13, w1, #8
    // 0xc37ba0: orr             x1, x0, x13
    // 0xc37ba4: and             x0, x1, x12
    // 0xc37ba8: and             x13, x1, x11
    // 0xc37bac: ubfx            x0, x0, #0, #0x20
    // 0xc37bb0: ubfx            x13, x13, #0, #0x20
    // 0xc37bb4: sub             x1, x0, x13
    // 0xc37bb8: mov             x19, x1
    // 0xc37bbc: ldur            x13, [fp, #-0x40]
    // 0xc37bc0: b               #0xc37c30
    // 0xc37bc4: ldur            x13, [fp, #-0x40]
    // 0xc37bc8: r12 = 32767
    //     0xc37bc8: orr             x12, xzr, #0x7fff
    // 0xc37bcc: r11 = 32768
    //     0xc37bcc: movz            x11, #0x8000
    // 0xc37bd0: sub             x0, x7, x8
    // 0xc37bd4: lsl             x1, x0, #1
    // 0xc37bd8: add             x14, x13, x1
    // 0xc37bdc: ldur            x0, [fp, #-0x60]
    // 0xc37be0: mov             x1, x14
    // 0xc37be4: cmp             x1, x0
    // 0xc37be8: b.hs            #0xc381e0
    // 0xc37bec: add             x0, x4, x14
    // 0xc37bf0: LoadField: r1 = r3->field_7
    //     0xc37bf0: ldur            x1, [x3, #7]
    // 0xc37bf4: ldrsh           x14, [x1, x0]
    // 0xc37bf8: mov             x0, x14
    // 0xc37bfc: ubfx            x0, x0, #0, #0x20
    // 0xc37c00: and             x1, x0, x5
    // 0xc37c04: lsr             w0, w1, #8
    // 0xc37c08: ubfx            x14, x14, #0, #0x20
    // 0xc37c0c: and             x1, x14, x6
    // 0xc37c10: lsl             w14, w1, #8
    // 0xc37c14: orr             x1, x0, x14
    // 0xc37c18: and             x0, x1, x12
    // 0xc37c1c: and             x14, x1, x11
    // 0xc37c20: ubfx            x0, x0, #0, #0x20
    // 0xc37c24: ubfx            x14, x14, #0, #0x20
    // 0xc37c28: sub             x1, x0, x14
    // 0xc37c2c: mov             x19, x1
    // 0xc37c30: ldur            x14, [fp, #-0x38]
    // 0xc37c34: stur            x19, [fp, #-0x98]
    // 0xc37c38: LoadField: r0 = r14->field_b
    //     0xc37c38: ldur            w0, [x14, #0xb]
    // 0xc37c3c: r1 = LoadInt32Instr(r0)
    //     0xc37c3c: sbfx            x1, x0, #1, #0x1f
    // 0xc37c40: mov             x0, x1
    // 0xc37c44: mov             x1, x7
    // 0xc37c48: cmp             x1, x0
    // 0xc37c4c: b.hs            #0xc381e4
    // 0xc37c50: LoadField: r0 = r14->field_f
    //     0xc37c50: ldur            w0, [x14, #0xf]
    // 0xc37c54: DecompressPointer r0
    //     0xc37c54: add             x0, x0, HEAP, lsl #32
    // 0xc37c58: lsl             x1, x7, #1
    // 0xc37c5c: stur            x1, [fp, #-0x10]
    // 0xc37c60: ArrayLoad: r20 = r0[r7]  ; Unknown_4
    //     0xc37c60: add             x16, x0, x7, lsl #2
    //     0xc37c64: ldur            w20, [x16, #0xf]
    // 0xc37c68: DecompressPointer r20
    //     0xc37c68: add             x20, x20, HEAP, lsl #32
    // 0xc37c6c: cbnz            w20, #0xc37ce0
    // 0xc37c70: ldur            d0, [fp, #-0xa8]
    // 0xc37c74: scvtf           d1, x10
    // 0xc37c78: fdiv            d2, d1, d0
    // 0xc37c7c: stur            d2, [fp, #-0xc0]
    // 0xc37c80: scvtf           d1, x19
    // 0xc37c84: fdiv            d3, d1, d0
    // 0xc37c88: stur            d3, [fp, #-0xb8]
    // 0xc37c8c: r0 = PdfFontMetrics()
    //     0xc37c8c: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0xc37c90: stur            x0, [fp, #-0x78]
    // 0xc37c94: StoreField: r0->field_7 = rZR
    //     0xc37c94: stur            xzr, [x0, #7]
    // 0xc37c98: StoreField: r0->field_f = rZR
    //     0xc37c98: stur            xzr, [x0, #0xf]
    // 0xc37c9c: StoreField: r0->field_1f = rZR
    //     0xc37c9c: stur            xzr, [x0, #0x1f]
    // 0xc37ca0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc37ca0: stur            xzr, [x0, #0x17]
    // 0xc37ca4: StoreField: r0->field_27 = rZR
    //     0xc37ca4: stur            xzr, [x0, #0x27]
    // 0xc37ca8: StoreField: r0->field_2f = rZR
    //     0xc37ca8: stur            xzr, [x0, #0x2f]
    // 0xc37cac: ldur            d0, [fp, #-0xc0]
    // 0xc37cb0: StoreField: r0->field_37 = d0
    //     0xc37cb0: stur            d0, [x0, #0x37]
    // 0xc37cb4: ldur            d0, [fp, #-0xb8]
    // 0xc37cb8: StoreField: r0->field_3f = d0
    //     0xc37cb8: stur            d0, [x0, #0x3f]
    // 0xc37cbc: ldur            x1, [fp, #-0x18]
    // 0xc37cc0: ldur            x2, [fp, #-0x10]
    // 0xc37cc4: r0 = _hashCode()
    //     0xc37cc4: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc37cc8: ldur            x1, [fp, #-0x18]
    // 0xc37ccc: ldur            x2, [fp, #-0x10]
    // 0xc37cd0: ldur            x3, [fp, #-0x78]
    // 0xc37cd4: mov             x5, x0
    // 0xc37cd8: r0 = _set()
    //     0xc37cd8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc37cdc: b               #0xc3815c
    // 0xc37ce0: mov             x8, x11
    // 0xc37ce4: ldur            x11, [fp, #-0x20]
    // 0xc37ce8: ldur            d0, [fp, #-0xb0]
    // 0xc37cec: mov             x9, x12
    // 0xc37cf0: ldur            x12, [fp, #-0x30]
    // 0xc37cf4: LoadField: r0 = r11->field_b
    //     0xc37cf4: ldur            w0, [x11, #0xb]
    // 0xc37cf8: r1 = LoadInt32Instr(r0)
    //     0xc37cf8: sbfx            x1, x0, #1, #0x1f
    // 0xc37cfc: mov             x0, x1
    // 0xc37d00: mov             x1, x7
    // 0xc37d04: cmp             x1, x0
    // 0xc37d08: b.hs            #0xc381e8
    // 0xc37d0c: LoadField: r0 = r11->field_f
    //     0xc37d0c: ldur            w0, [x11, #0xf]
    // 0xc37d10: DecompressPointer r0
    //     0xc37d10: add             x0, x0, HEAP, lsl #32
    // 0xc37d14: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xc37d14: add             x16, x0, x7, lsl #2
    //     0xc37d18: ldur            w1, [x16, #0xf]
    // 0xc37d1c: DecompressPointer r1
    //     0xc37d1c: add             x1, x1, HEAP, lsl #32
    // 0xc37d20: r0 = LoadInt32Instr(r1)
    //     0xc37d20: sbfx            x0, x1, #1, #0x1f
    //     0xc37d24: tbz             w1, #0, #0xc37d2c
    //     0xc37d28: ldur            x0, [x1, #7]
    // 0xc37d2c: add             x13, x12, x0
    // 0xc37d30: add             x14, x13, #2
    // 0xc37d34: ldur            x0, [fp, #-0x60]
    // 0xc37d38: mov             x1, x14
    // 0xc37d3c: cmp             x1, x0
    // 0xc37d40: b.hs            #0xc381ec
    // 0xc37d44: add             x0, x4, x14
    // 0xc37d48: LoadField: r1 = r3->field_7
    //     0xc37d48: ldur            x1, [x3, #7]
    // 0xc37d4c: ldrsh           x14, [x1, x0]
    // 0xc37d50: mov             x0, x14
    // 0xc37d54: ubfx            x0, x0, #0, #0x20
    // 0xc37d58: and             x1, x0, x5
    // 0xc37d5c: lsr             w0, w1, #8
    // 0xc37d60: ubfx            x14, x14, #0, #0x20
    // 0xc37d64: and             x1, x14, x6
    // 0xc37d68: lsl             w14, w1, #8
    // 0xc37d6c: orr             x1, x0, x14
    // 0xc37d70: and             x0, x1, x9
    // 0xc37d74: and             x14, x1, x8
    // 0xc37d78: ubfx            x0, x0, #0, #0x20
    // 0xc37d7c: ubfx            x14, x14, #0, #0x20
    // 0xc37d80: sub             x20, x0, x14
    // 0xc37d84: add             x14, x13, #4
    // 0xc37d88: ldur            x0, [fp, #-0x60]
    // 0xc37d8c: mov             x1, x14
    // 0xc37d90: cmp             x1, x0
    // 0xc37d94: b.hs            #0xc381f0
    // 0xc37d98: add             x0, x4, x14
    // 0xc37d9c: LoadField: r1 = r3->field_7
    //     0xc37d9c: ldur            x1, [x3, #7]
    // 0xc37da0: ldrsh           x14, [x1, x0]
    // 0xc37da4: mov             x0, x14
    // 0xc37da8: ubfx            x0, x0, #0, #0x20
    // 0xc37dac: and             x1, x0, x5
    // 0xc37db0: lsr             w0, w1, #8
    // 0xc37db4: ubfx            x14, x14, #0, #0x20
    // 0xc37db8: and             x1, x14, x6
    // 0xc37dbc: lsl             w14, w1, #8
    // 0xc37dc0: orr             x1, x0, x14
    // 0xc37dc4: and             x0, x1, x9
    // 0xc37dc8: and             x14, x1, x8
    // 0xc37dcc: ubfx            x0, x0, #0, #0x20
    // 0xc37dd0: ubfx            x14, x14, #0, #0x20
    // 0xc37dd4: sub             x23, x0, x14
    // 0xc37dd8: stur            x23, [fp, #-0x90]
    // 0xc37ddc: add             x14, x13, #6
    // 0xc37de0: ldur            x0, [fp, #-0x60]
    // 0xc37de4: mov             x1, x14
    // 0xc37de8: cmp             x1, x0
    // 0xc37dec: b.hs            #0xc381f4
    // 0xc37df0: add             x0, x4, x14
    // 0xc37df4: LoadField: r1 = r3->field_7
    //     0xc37df4: ldur            x1, [x3, #7]
    // 0xc37df8: ldrsh           x14, [x1, x0]
    // 0xc37dfc: mov             x0, x14
    // 0xc37e00: ubfx            x0, x0, #0, #0x20
    // 0xc37e04: and             x1, x0, x5
    // 0xc37e08: lsr             w0, w1, #8
    // 0xc37e0c: ubfx            x14, x14, #0, #0x20
    // 0xc37e10: and             x1, x14, x6
    // 0xc37e14: lsl             w14, w1, #8
    // 0xc37e18: orr             x1, x0, x14
    // 0xc37e1c: and             x0, x1, x9
    // 0xc37e20: and             x14, x1, x8
    // 0xc37e24: ubfx            x0, x0, #0, #0x20
    // 0xc37e28: ubfx            x14, x14, #0, #0x20
    // 0xc37e2c: sub             x24, x0, x14
    // 0xc37e30: stur            x24, [fp, #-0x88]
    // 0xc37e34: add             x14, x13, #8
    // 0xc37e38: ldur            x0, [fp, #-0x60]
    // 0xc37e3c: mov             x1, x14
    // 0xc37e40: cmp             x1, x0
    // 0xc37e44: b.hs            #0xc381f8
    // 0xc37e48: add             x0, x4, x14
    // 0xc37e4c: LoadField: r1 = r3->field_7
    //     0xc37e4c: ldur            x1, [x3, #7]
    // 0xc37e50: ldrsh           x13, [x1, x0]
    // 0xc37e54: mov             x0, x13
    // 0xc37e58: ubfx            x0, x0, #0, #0x20
    // 0xc37e5c: and             x1, x0, x5
    // 0xc37e60: lsr             w0, w1, #8
    // 0xc37e64: ubfx            x13, x13, #0, #0x20
    // 0xc37e68: and             x1, x13, x6
    // 0xc37e6c: lsl             w13, w1, #8
    // 0xc37e70: orr             x1, x0, x13
    // 0xc37e74: and             x0, x1, x9
    // 0xc37e78: and             x13, x1, x8
    // 0xc37e7c: ubfx            x0, x0, #0, #0x20
    // 0xc37e80: ubfx            x13, x13, #0, #0x20
    // 0xc37e84: sub             x1, x0, x13
    // 0xc37e88: stur            x1, [fp, #-0x80]
    // 0xc37e8c: lsl             x0, x20, #1
    // 0xc37e90: stp             x0, NULL, [SP]
    // 0xc37e94: r0 = _Double.fromInteger()
    //     0xc37e94: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc37e98: LoadField: d0 = r0->field_7
    //     0xc37e98: ldur            d0, [x0, #7]
    // 0xc37e9c: ldur            d1, [fp, #-0xb0]
    // 0xc37ea0: fdiv            d2, d0, d1
    // 0xc37ea4: ldur            x0, [fp, #-0x90]
    // 0xc37ea8: stur            d2, [fp, #-0xb8]
    // 0xc37eac: lsl             x1, x0, #1
    // 0xc37eb0: stp             x1, NULL, [SP]
    // 0xc37eb4: r0 = _Double.fromInteger()
    //     0xc37eb4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc37eb8: LoadField: d0 = r0->field_7
    //     0xc37eb8: ldur            d0, [x0, #7]
    // 0xc37ebc: ldur            d1, [fp, #-0xb0]
    // 0xc37ec0: fdiv            d2, d0, d1
    // 0xc37ec4: ldur            x0, [fp, #-0x88]
    // 0xc37ec8: stur            d2, [fp, #-0xc0]
    // 0xc37ecc: lsl             x1, x0, #1
    // 0xc37ed0: stp             x1, NULL, [SP]
    // 0xc37ed4: r0 = _Double.fromInteger()
    //     0xc37ed4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc37ed8: LoadField: d0 = r0->field_7
    //     0xc37ed8: ldur            d0, [x0, #7]
    // 0xc37edc: ldur            d1, [fp, #-0xb0]
    // 0xc37ee0: fdiv            d2, d0, d1
    // 0xc37ee4: ldur            x0, [fp, #-0x80]
    // 0xc37ee8: stur            d2, [fp, #-0xc8]
    // 0xc37eec: lsl             x1, x0, #1
    // 0xc37ef0: stp             x1, NULL, [SP]
    // 0xc37ef4: r0 = _Double.fromInteger()
    //     0xc37ef4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc37ef8: LoadField: d0 = r0->field_7
    //     0xc37ef8: ldur            d0, [x0, #7]
    // 0xc37efc: ldur            d1, [fp, #-0xb0]
    // 0xc37f00: fdiv            d2, d0, d1
    // 0xc37f04: ldur            x1, [fp, #-8]
    // 0xc37f08: stur            d2, [fp, #-0xd0]
    // 0xc37f0c: r2 = "hhea"
    //     0xc37f0c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0xc37f10: ldr             x2, [x2, #0x888]
    // 0xc37f14: r0 = _getValueOrData()
    //     0xc37f14: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc37f18: ldur            x2, [fp, #-8]
    // 0xc37f1c: LoadField: r1 = r2->field_f
    //     0xc37f1c: ldur            w1, [x2, #0xf]
    // 0xc37f20: DecompressPointer r1
    //     0xc37f20: add             x1, x1, HEAP, lsl #32
    // 0xc37f24: cmp             w1, w0
    // 0xc37f28: b.ne            #0xc37f30
    // 0xc37f2c: r0 = Null
    //     0xc37f2c: mov             x0, NULL
    // 0xc37f30: ldur            d0, [fp, #-0xb0]
    // 0xc37f34: ldur            x3, [fp, #-0x58]
    // 0xc37f38: ldur            x4, [fp, #-0x50]
    // 0xc37f3c: r5 = 65280
    //     0xc37f3c: orr             x5, xzr, #0xff00
    // 0xc37f40: r6 = 255
    //     0xc37f40: movz            x6, #0xff
    // 0xc37f44: r8 = 32767
    //     0xc37f44: orr             x8, xzr, #0x7fff
    // 0xc37f48: r7 = 32768
    //     0xc37f48: movz            x7, #0x8000
    // 0xc37f4c: cmp             w0, NULL
    // 0xc37f50: b.eq            #0xc381fc
    // 0xc37f54: r1 = LoadInt32Instr(r0)
    //     0xc37f54: sbfx            x1, x0, #1, #0x1f
    //     0xc37f58: tbz             w0, #0, #0xc37f60
    //     0xc37f5c: ldur            x1, [x0, #7]
    // 0xc37f60: add             x9, x1, #4
    // 0xc37f64: ldur            x0, [fp, #-0x60]
    // 0xc37f68: mov             x1, x9
    // 0xc37f6c: cmp             x1, x0
    // 0xc37f70: b.hs            #0xc38200
    // 0xc37f74: add             x0, x4, x9
    // 0xc37f78: LoadField: r1 = r3->field_7
    //     0xc37f78: ldur            x1, [x3, #7]
    // 0xc37f7c: ldrsh           x9, [x1, x0]
    // 0xc37f80: mov             x0, x9
    // 0xc37f84: ubfx            x0, x0, #0, #0x20
    // 0xc37f88: and             x1, x0, x5
    // 0xc37f8c: lsr             w0, w1, #8
    // 0xc37f90: ubfx            x9, x9, #0, #0x20
    // 0xc37f94: and             x1, x9, x6
    // 0xc37f98: lsl             w9, w1, #8
    // 0xc37f9c: orr             x1, x0, x9
    // 0xc37fa0: and             x0, x1, x8
    // 0xc37fa4: and             x9, x1, x7
    // 0xc37fa8: ubfx            x0, x0, #0, #0x20
    // 0xc37fac: ubfx            x9, x9, #0, #0x20
    // 0xc37fb0: sub             x1, x0, x9
    // 0xc37fb4: lsl             x0, x1, #1
    // 0xc37fb8: stp             x0, NULL, [SP]
    // 0xc37fbc: r0 = _Double.fromInteger()
    //     0xc37fbc: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc37fc0: LoadField: d0 = r0->field_7
    //     0xc37fc0: ldur            d0, [x0, #7]
    // 0xc37fc4: ldur            d1, [fp, #-0xb0]
    // 0xc37fc8: fdiv            d2, d0, d1
    // 0xc37fcc: ldur            x1, [fp, #-8]
    // 0xc37fd0: stur            d2, [fp, #-0xd8]
    // 0xc37fd4: r2 = "hhea"
    //     0xc37fd4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0xc37fd8: ldr             x2, [x2, #0x888]
    // 0xc37fdc: r0 = _getValueOrData()
    //     0xc37fdc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc37fe0: ldur            x2, [fp, #-8]
    // 0xc37fe4: LoadField: r1 = r2->field_f
    //     0xc37fe4: ldur            w1, [x2, #0xf]
    // 0xc37fe8: DecompressPointer r1
    //     0xc37fe8: add             x1, x1, HEAP, lsl #32
    // 0xc37fec: cmp             w1, w0
    // 0xc37ff0: b.ne            #0xc37ff8
    // 0xc37ff4: r0 = Null
    //     0xc37ff4: mov             x0, NULL
    // 0xc37ff8: ldur            x9, [fp, #-0xa0]
    // 0xc37ffc: ldur            x10, [fp, #-0x98]
    // 0xc38000: ldur            d5, [fp, #-0xb8]
    // 0xc38004: ldur            d4, [fp, #-0xc0]
    // 0xc38008: ldur            d3, [fp, #-0xc8]
    // 0xc3800c: ldur            d2, [fp, #-0xd0]
    // 0xc38010: ldur            d1, [fp, #-0xd8]
    // 0xc38014: ldur            d0, [fp, #-0xb0]
    // 0xc38018: ldur            x3, [fp, #-0x58]
    // 0xc3801c: ldur            x4, [fp, #-0x50]
    // 0xc38020: r5 = 65280
    //     0xc38020: orr             x5, xzr, #0xff00
    // 0xc38024: r6 = 255
    //     0xc38024: movz            x6, #0xff
    // 0xc38028: r8 = 32767
    //     0xc38028: orr             x8, xzr, #0x7fff
    // 0xc3802c: r7 = 32768
    //     0xc3802c: movz            x7, #0x8000
    // 0xc38030: cmp             w0, NULL
    // 0xc38034: b.eq            #0xc38204
    // 0xc38038: r1 = LoadInt32Instr(r0)
    //     0xc38038: sbfx            x1, x0, #1, #0x1f
    //     0xc3803c: tbz             w0, #0, #0xc38044
    //     0xc38040: ldur            x1, [x0, #7]
    // 0xc38044: add             x11, x1, #6
    // 0xc38048: ldur            x0, [fp, #-0x60]
    // 0xc3804c: mov             x1, x11
    // 0xc38050: cmp             x1, x0
    // 0xc38054: b.hs            #0xc38208
    // 0xc38058: add             x0, x4, x11
    // 0xc3805c: LoadField: r1 = r3->field_7
    //     0xc3805c: ldur            x1, [x3, #7]
    // 0xc38060: ldrsh           x11, [x1, x0]
    // 0xc38064: mov             x0, x11
    // 0xc38068: ubfx            x0, x0, #0, #0x20
    // 0xc3806c: and             x1, x0, x5
    // 0xc38070: lsr             w0, w1, #8
    // 0xc38074: ubfx            x11, x11, #0, #0x20
    // 0xc38078: and             x1, x11, x6
    // 0xc3807c: lsl             w11, w1, #8
    // 0xc38080: orr             x1, x0, x11
    // 0xc38084: and             x0, x1, x8
    // 0xc38088: and             x11, x1, x7
    // 0xc3808c: ubfx            x0, x0, #0, #0x20
    // 0xc38090: ubfx            x11, x11, #0, #0x20
    // 0xc38094: sub             x1, x0, x11
    // 0xc38098: lsl             x0, x1, #1
    // 0xc3809c: stp             x0, NULL, [SP]
    // 0xc380a0: r0 = _Double.fromInteger()
    //     0xc380a0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc380a4: LoadField: d0 = r0->field_7
    //     0xc380a4: ldur            d0, [x0, #7]
    // 0xc380a8: ldur            d1, [fp, #-0xb0]
    // 0xc380ac: fdiv            d2, d0, d1
    // 0xc380b0: ldur            x0, [fp, #-0xa0]
    // 0xc380b4: stur            d2, [fp, #-0xe0]
    // 0xc380b8: lsl             x1, x0, #1
    // 0xc380bc: stp             x1, NULL, [SP]
    // 0xc380c0: r0 = _Double.fromInteger()
    //     0xc380c0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc380c4: LoadField: d0 = r0->field_7
    //     0xc380c4: ldur            d0, [x0, #7]
    // 0xc380c8: ldur            d1, [fp, #-0xb0]
    // 0xc380cc: fdiv            d2, d0, d1
    // 0xc380d0: ldur            x0, [fp, #-0x98]
    // 0xc380d4: stur            d2, [fp, #-0xe8]
    // 0xc380d8: lsl             x1, x0, #1
    // 0xc380dc: stp             x1, NULL, [SP]
    // 0xc380e0: r0 = _Double.fromInteger()
    //     0xc380e0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xc380e4: LoadField: d0 = r0->field_7
    //     0xc380e4: ldur            d0, [x0, #7]
    // 0xc380e8: ldur            d1, [fp, #-0xb0]
    // 0xc380ec: fdiv            d2, d0, d1
    // 0xc380f0: stur            d2, [fp, #-0xf0]
    // 0xc380f4: r0 = PdfFontMetrics()
    //     0xc380f4: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0xc380f8: ldur            d0, [fp, #-0xb8]
    // 0xc380fc: stur            x0, [fp, #-0x78]
    // 0xc38100: StoreField: r0->field_7 = d0
    //     0xc38100: stur            d0, [x0, #7]
    // 0xc38104: ldur            d0, [fp, #-0xc0]
    // 0xc38108: StoreField: r0->field_f = d0
    //     0xc38108: stur            d0, [x0, #0xf]
    // 0xc3810c: ldur            d0, [fp, #-0xc8]
    // 0xc38110: StoreField: r0->field_1f = d0
    //     0xc38110: stur            d0, [x0, #0x1f]
    // 0xc38114: ldur            d0, [fp, #-0xd0]
    // 0xc38118: ArrayStore: r0[0] = d0  ; List_8
    //     0xc38118: stur            d0, [x0, #0x17]
    // 0xc3811c: ldur            d0, [fp, #-0xd8]
    // 0xc38120: StoreField: r0->field_27 = d0
    //     0xc38120: stur            d0, [x0, #0x27]
    // 0xc38124: ldur            d0, [fp, #-0xe0]
    // 0xc38128: StoreField: r0->field_2f = d0
    //     0xc38128: stur            d0, [x0, #0x2f]
    // 0xc3812c: ldur            d0, [fp, #-0xe8]
    // 0xc38130: StoreField: r0->field_37 = d0
    //     0xc38130: stur            d0, [x0, #0x37]
    // 0xc38134: ldur            d0, [fp, #-0xf0]
    // 0xc38138: StoreField: r0->field_3f = d0
    //     0xc38138: stur            d0, [x0, #0x3f]
    // 0xc3813c: ldur            x1, [fp, #-0x18]
    // 0xc38140: ldur            x2, [fp, #-0x10]
    // 0xc38144: r0 = _hashCode()
    //     0xc38144: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc38148: ldur            x1, [fp, #-0x18]
    // 0xc3814c: ldur            x2, [fp, #-0x10]
    // 0xc38150: ldur            x3, [fp, #-0x78]
    // 0xc38154: mov             x5, x0
    // 0xc38158: r0 = _set()
    //     0xc38158: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc3815c: ldur            x1, [fp, #-0x28]
    // 0xc38160: add             x20, x1, #1
    // 0xc38164: ldur            x3, [fp, #-0x70]
    // 0xc38168: ldur            x8, [fp, #-0x40]
    // 0xc3816c: ldur            x11, [fp, #-0x38]
    // 0xc38170: ldur            x12, [fp, #-0x20]
    // 0xc38174: ldur            x14, [fp, #-0x18]
    // 0xc38178: ldur            d1, [fp, #-0xa8]
    // 0xc3817c: ldur            d0, [fp, #-0xb0]
    // 0xc38180: ldur            x7, [fp, #-0x60]
    // 0xc38184: ldur            x0, [fp, #-0x58]
    // 0xc38188: ldur            x10, [fp, #-0x48]
    // 0xc3818c: ldur            x13, [fp, #-0x30]
    // 0xc38190: ldur            x5, [fp, #-0x68]
    // 0xc38194: ldur            x4, [fp, #-0x50]
    // 0xc38198: r6 = 65280
    //     0xc38198: orr             x6, xzr, #0xff00
    // 0xc3819c: r9 = 255
    //     0xc3819c: movz            x9, #0xff
    // 0xc381a0: b               #0xc37a28
    // 0xc381a4: r0 = Null
    //     0xc381a4: mov             x0, NULL
    // 0xc381a8: LeaveFrame
    //     0xc381a8: mov             SP, fp
    //     0xc381ac: ldp             fp, lr, [SP], #0x10
    // 0xc381b0: ret
    //     0xc381b0: ret             
    // 0xc381b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc381b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc381b8: b               #0xc37878
    // 0xc381bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc381bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc381c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc381c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc381c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381c8: r0 = StackOverflowSharedWithFPURegs()
    //     0xc381c8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc381cc: b               #0xc37a3c
    // 0xc381d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc381d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc381d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc381e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc381e8: r0 = RangeErrorSharedWithFPURegs()
    //     0xc381e8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc381ec: r0 = RangeErrorSharedWithFPURegs()
    //     0xc381ec: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc381f0: r0 = RangeErrorSharedWithFPURegs()
    //     0xc381f0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc381f4: r0 = RangeErrorSharedWithFPURegs()
    //     0xc381f4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc381f8: r0 = RangeErrorSharedWithFPURegs()
    //     0xc381f8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc381fc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc381fc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xc38200: r0 = RangeErrorSharedWithFPURegs()
    //     0xc38200: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc38204: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc38204: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xc38208: r0 = RangeErrorSharedWithFPURegs()
    //     0xc38208: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  get _ descent(/* No info */) {
    // ** addr: 0xc3820c, size: 0x110
    // 0xc3820c: EnterFrame
    //     0xc3820c: stp             fp, lr, [SP, #-0x10]!
    //     0xc38210: mov             fp, SP
    // 0xc38214: AllocStack(0x10)
    //     0xc38214: sub             SP, SP, #0x10
    // 0xc38218: CheckStackOverflow
    //     0xc38218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3821c: cmp             SP, x16
    //     0xc38220: b.ls            #0xc3830c
    // 0xc38224: LoadField: r0 = r1->field_7
    //     0xc38224: ldur            w0, [x1, #7]
    // 0xc38228: DecompressPointer r0
    //     0xc38228: add             x0, x0, HEAP, lsl #32
    // 0xc3822c: stur            x0, [fp, #-0x10]
    // 0xc38230: LoadField: r3 = r1->field_b
    //     0xc38230: ldur            w3, [x1, #0xb]
    // 0xc38234: DecompressPointer r3
    //     0xc38234: add             x3, x3, HEAP, lsl #32
    // 0xc38238: mov             x1, x3
    // 0xc3823c: stur            x3, [fp, #-8]
    // 0xc38240: r2 = "hhea"
    //     0xc38240: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0xc38244: ldr             x2, [x2, #0x888]
    // 0xc38248: r0 = _getValueOrData()
    //     0xc38248: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc3824c: ldur            x2, [fp, #-8]
    // 0xc38250: LoadField: r3 = r2->field_f
    //     0xc38250: ldur            w3, [x2, #0xf]
    // 0xc38254: DecompressPointer r3
    //     0xc38254: add             x3, x3, HEAP, lsl #32
    // 0xc38258: cmp             w3, w0
    // 0xc3825c: b.ne            #0xc38268
    // 0xc38260: r7 = Null
    //     0xc38260: mov             x7, NULL
    // 0xc38264: b               #0xc3826c
    // 0xc38268: mov             x7, x0
    // 0xc3826c: ldur            x2, [fp, #-0x10]
    // 0xc38270: r6 = 65280
    //     0xc38270: orr             x6, xzr, #0xff00
    // 0xc38274: r5 = 255
    //     0xc38274: movz            x5, #0xff
    // 0xc38278: r4 = 32767
    //     0xc38278: orr             x4, xzr, #0x7fff
    // 0xc3827c: r3 = 32768
    //     0xc3827c: movz            x3, #0x8000
    // 0xc38280: cmp             w7, NULL
    // 0xc38284: b.eq            #0xc38314
    // 0xc38288: r8 = LoadInt32Instr(r7)
    //     0xc38288: sbfx            x8, x7, #1, #0x1f
    //     0xc3828c: tbz             w7, #0, #0xc38294
    //     0xc38290: ldur            x8, [x7, #7]
    // 0xc38294: add             x7, x8, #6
    // 0xc38298: LoadField: r8 = r2->field_13
    //     0xc38298: ldur            w8, [x2, #0x13]
    // 0xc3829c: r9 = LoadInt32Instr(r8)
    //     0xc3829c: sbfx            x9, x8, #1, #0x1f
    // 0xc382a0: sub             x0, x9, #1
    // 0xc382a4: mov             x1, x7
    // 0xc382a8: cmp             x1, x0
    // 0xc382ac: b.hs            #0xc38318
    // 0xc382b0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xc382b0: ldur            w1, [x2, #0x17]
    // 0xc382b4: DecompressPointer r1
    //     0xc382b4: add             x1, x1, HEAP, lsl #32
    // 0xc382b8: LoadField: r8 = r2->field_1b
    //     0xc382b8: ldur            w8, [x2, #0x1b]
    // 0xc382bc: r2 = LoadInt32Instr(r8)
    //     0xc382bc: sbfx            x2, x8, #1, #0x1f
    // 0xc382c0: add             x8, x2, x7
    // 0xc382c4: LoadField: r2 = r1->field_7
    //     0xc382c4: ldur            x2, [x1, #7]
    // 0xc382c8: ldrsh           x1, [x2, x8]
    // 0xc382cc: mov             x2, x1
    // 0xc382d0: ubfx            x2, x2, #0, #0x20
    // 0xc382d4: and             x7, x2, x6
    // 0xc382d8: lsr             w2, w7, #8
    // 0xc382dc: ubfx            x1, x1, #0, #0x20
    // 0xc382e0: and             x6, x1, x5
    // 0xc382e4: lsl             w1, w6, #8
    // 0xc382e8: orr             x5, x2, x1
    // 0xc382ec: and             x1, x5, x4
    // 0xc382f0: and             x2, x5, x3
    // 0xc382f4: ubfx            x1, x1, #0, #0x20
    // 0xc382f8: ubfx            x2, x2, #0, #0x20
    // 0xc382fc: sub             x0, x1, x2
    // 0xc38300: LeaveFrame
    //     0xc38300: mov             SP, fp
    //     0xc38304: ldp             fp, lr, [SP], #0x10
    // 0xc38308: ret
    //     0xc38308: ret             
    // 0xc3830c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3830c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38310: b               #0xc38224
    // 0xc38314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38318: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38318: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ ascent(/* No info */) {
    // ** addr: 0xc3831c, size: 0x110
    // 0xc3831c: EnterFrame
    //     0xc3831c: stp             fp, lr, [SP, #-0x10]!
    //     0xc38320: mov             fp, SP
    // 0xc38324: AllocStack(0x10)
    //     0xc38324: sub             SP, SP, #0x10
    // 0xc38328: CheckStackOverflow
    //     0xc38328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3832c: cmp             SP, x16
    //     0xc38330: b.ls            #0xc3841c
    // 0xc38334: LoadField: r0 = r1->field_7
    //     0xc38334: ldur            w0, [x1, #7]
    // 0xc38338: DecompressPointer r0
    //     0xc38338: add             x0, x0, HEAP, lsl #32
    // 0xc3833c: stur            x0, [fp, #-0x10]
    // 0xc38340: LoadField: r3 = r1->field_b
    //     0xc38340: ldur            w3, [x1, #0xb]
    // 0xc38344: DecompressPointer r3
    //     0xc38344: add             x3, x3, HEAP, lsl #32
    // 0xc38348: mov             x1, x3
    // 0xc3834c: stur            x3, [fp, #-8]
    // 0xc38350: r2 = "hhea"
    //     0xc38350: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0xc38354: ldr             x2, [x2, #0x888]
    // 0xc38358: r0 = _getValueOrData()
    //     0xc38358: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc3835c: ldur            x2, [fp, #-8]
    // 0xc38360: LoadField: r3 = r2->field_f
    //     0xc38360: ldur            w3, [x2, #0xf]
    // 0xc38364: DecompressPointer r3
    //     0xc38364: add             x3, x3, HEAP, lsl #32
    // 0xc38368: cmp             w3, w0
    // 0xc3836c: b.ne            #0xc38378
    // 0xc38370: r7 = Null
    //     0xc38370: mov             x7, NULL
    // 0xc38374: b               #0xc3837c
    // 0xc38378: mov             x7, x0
    // 0xc3837c: ldur            x2, [fp, #-0x10]
    // 0xc38380: r6 = 65280
    //     0xc38380: orr             x6, xzr, #0xff00
    // 0xc38384: r5 = 255
    //     0xc38384: movz            x5, #0xff
    // 0xc38388: r4 = 32767
    //     0xc38388: orr             x4, xzr, #0x7fff
    // 0xc3838c: r3 = 32768
    //     0xc3838c: movz            x3, #0x8000
    // 0xc38390: cmp             w7, NULL
    // 0xc38394: b.eq            #0xc38424
    // 0xc38398: r8 = LoadInt32Instr(r7)
    //     0xc38398: sbfx            x8, x7, #1, #0x1f
    //     0xc3839c: tbz             w7, #0, #0xc383a4
    //     0xc383a0: ldur            x8, [x7, #7]
    // 0xc383a4: add             x7, x8, #4
    // 0xc383a8: LoadField: r8 = r2->field_13
    //     0xc383a8: ldur            w8, [x2, #0x13]
    // 0xc383ac: r9 = LoadInt32Instr(r8)
    //     0xc383ac: sbfx            x9, x8, #1, #0x1f
    // 0xc383b0: sub             x0, x9, #1
    // 0xc383b4: mov             x1, x7
    // 0xc383b8: cmp             x1, x0
    // 0xc383bc: b.hs            #0xc38428
    // 0xc383c0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xc383c0: ldur            w1, [x2, #0x17]
    // 0xc383c4: DecompressPointer r1
    //     0xc383c4: add             x1, x1, HEAP, lsl #32
    // 0xc383c8: LoadField: r8 = r2->field_1b
    //     0xc383c8: ldur            w8, [x2, #0x1b]
    // 0xc383cc: r2 = LoadInt32Instr(r8)
    //     0xc383cc: sbfx            x2, x8, #1, #0x1f
    // 0xc383d0: add             x8, x2, x7
    // 0xc383d4: LoadField: r2 = r1->field_7
    //     0xc383d4: ldur            x2, [x1, #7]
    // 0xc383d8: ldrsh           x1, [x2, x8]
    // 0xc383dc: mov             x2, x1
    // 0xc383e0: ubfx            x2, x2, #0, #0x20
    // 0xc383e4: and             x7, x2, x6
    // 0xc383e8: lsr             w2, w7, #8
    // 0xc383ec: ubfx            x1, x1, #0, #0x20
    // 0xc383f0: and             x6, x1, x5
    // 0xc383f4: lsl             w1, w6, #8
    // 0xc383f8: orr             x5, x2, x1
    // 0xc383fc: and             x1, x5, x4
    // 0xc38400: and             x2, x5, x3
    // 0xc38404: ubfx            x1, x1, #0, #0x20
    // 0xc38408: ubfx            x2, x2, #0, #0x20
    // 0xc3840c: sub             x0, x1, x2
    // 0xc38410: LeaveFrame
    //     0xc38410: mov             SP, fp
    //     0xc38414: ldp             fp, lr, [SP], #0x10
    // 0xc38418: ret
    //     0xc38418: ret             
    // 0xc3841c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3841c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38420: b               #0xc38334
    // 0xc38424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38424: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38428: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseIndexes(/* No info */) {
    // ** addr: 0xc3842c, size: 0x720
    // 0xc3842c: EnterFrame
    //     0xc3842c: stp             fp, lr, [SP, #-0x10]!
    //     0xc38430: mov             fp, SP
    // 0xc38434: AllocStack(0x70)
    //     0xc38434: sub             SP, SP, #0x70
    // 0xc38438: SetupParameters(TtfParser this /* r1 => r0, fp-0x10 */)
    //     0xc38438: mov             x0, x1
    //     0xc3843c: stur            x1, [fp, #-0x10]
    // 0xc38440: CheckStackOverflow
    //     0xc38440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc38444: cmp             SP, x16
    //     0xc38448: b.ls            #0xc38b10
    // 0xc3844c: LoadField: r3 = r0->field_b
    //     0xc3844c: ldur            w3, [x0, #0xb]
    // 0xc38450: DecompressPointer r3
    //     0xc38450: add             x3, x3, HEAP, lsl #32
    // 0xc38454: mov             x1, x3
    // 0xc38458: stur            x3, [fp, #-8]
    // 0xc3845c: r2 = "loca"
    //     0xc3845c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0xc38460: ldr             x2, [x2, #0x858]
    // 0xc38464: r0 = _getValueOrData()
    //     0xc38464: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc38468: mov             x1, x0
    // 0xc3846c: ldur            x0, [fp, #-8]
    // 0xc38470: LoadField: r2 = r0->field_f
    //     0xc38470: ldur            w2, [x0, #0xf]
    // 0xc38474: DecompressPointer r2
    //     0xc38474: add             x2, x2, HEAP, lsl #32
    // 0xc38478: cmp             w2, w1
    // 0xc3847c: b.ne            #0xc38488
    // 0xc38480: r2 = Null
    //     0xc38480: mov             x2, NULL
    // 0xc38484: b               #0xc3848c
    // 0xc38488: mov             x2, x1
    // 0xc3848c: stur            x2, [fp, #-0x18]
    // 0xc38490: cmp             w2, NULL
    // 0xc38494: b.eq            #0xc38b18
    // 0xc38498: ldur            x1, [fp, #-0x10]
    // 0xc3849c: r0 = indexToLocFormat()
    //     0xc3849c: bl              #0x7bc74c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::indexToLocFormat
    // 0xc384a0: cbnz            x0, #0xc38764
    // 0xc384a4: ldur            x2, [fp, #-0x10]
    // 0xc384a8: ldur            x0, [fp, #-0x18]
    // 0xc384ac: r4 = 65280
    //     0xc384ac: orr             x4, xzr, #0xff00
    // 0xc384b0: r3 = 255
    //     0xc384b0: movz            x3, #0xff
    // 0xc384b4: LoadField: r5 = r2->field_7
    //     0xc384b4: ldur            w5, [x2, #7]
    // 0xc384b8: DecompressPointer r5
    //     0xc384b8: add             x5, x5, HEAP, lsl #32
    // 0xc384bc: LoadField: r1 = r5->field_13
    //     0xc384bc: ldur            w1, [x5, #0x13]
    // 0xc384c0: r6 = LoadInt32Instr(r1)
    //     0xc384c0: sbfx            x6, x1, #1, #0x1f
    // 0xc384c4: sub             x7, x6, #1
    // 0xc384c8: stur            x7, [fp, #-0x58]
    // 0xc384cc: r6 = LoadInt32Instr(r0)
    //     0xc384cc: sbfx            x6, x0, #1, #0x1f
    //     0xc384d0: tbz             w0, #0, #0xc384d8
    //     0xc384d4: ldur            x6, [x0, #7]
    // 0xc384d8: mov             x0, x7
    // 0xc384dc: mov             x1, x6
    // 0xc384e0: stur            x6, [fp, #-0x50]
    // 0xc384e4: cmp             x1, x0
    // 0xc384e8: b.hs            #0xc38b1c
    // 0xc384ec: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xc384ec: ldur            w0, [x5, #0x17]
    // 0xc384f0: DecompressPointer r0
    //     0xc384f0: add             x0, x0, HEAP, lsl #32
    // 0xc384f4: stur            x0, [fp, #-0x48]
    // 0xc384f8: LoadField: r1 = r5->field_1b
    //     0xc384f8: ldur            w1, [x5, #0x1b]
    // 0xc384fc: r5 = LoadInt32Instr(r1)
    //     0xc384fc: sbfx            x5, x1, #1, #0x1f
    // 0xc38500: stur            x5, [fp, #-0x40]
    // 0xc38504: add             x1, x5, x6
    // 0xc38508: LoadField: r8 = r0->field_7
    //     0xc38508: ldur            x8, [x0, #7]
    // 0xc3850c: ldrh            w9, [x8, x1]
    // 0xc38510: mov             x1, x9
    // 0xc38514: ubfx            x1, x1, #0, #0x20
    // 0xc38518: and             x8, x1, x4
    // 0xc3851c: ubfx            x8, x8, #0, #0x20
    // 0xc38520: asr             x1, x8, #8
    // 0xc38524: ubfx            x9, x9, #0, #0x20
    // 0xc38528: and             x8, x9, x3
    // 0xc3852c: ubfx            x8, x8, #0, #0x20
    // 0xc38530: lsl             x9, x8, #8
    // 0xc38534: orr             x8, x1, x9
    // 0xc38538: lsl             x1, x8, #1
    // 0xc3853c: ArrayLoad: r8 = r2[0]  ; List_4
    //     0xc3853c: ldur            w8, [x2, #0x17]
    // 0xc38540: DecompressPointer r8
    //     0xc38540: add             x8, x8, HEAP, lsl #32
    // 0xc38544: stur            x8, [fp, #-0x38]
    // 0xc38548: LoadField: r9 = r2->field_1b
    //     0xc38548: ldur            w9, [x2, #0x1b]
    // 0xc3854c: DecompressPointer r9
    //     0xc3854c: add             x9, x9, HEAP, lsl #32
    // 0xc38550: stur            x9, [fp, #-0x30]
    // 0xc38554: mov             x12, x1
    // 0xc38558: r11 = 1
    //     0xc38558: movz            x11, #0x1
    // 0xc3855c: ldur            x10, [fp, #-8]
    // 0xc38560: stur            x12, [fp, #-0x20]
    // 0xc38564: stur            x11, [fp, #-0x28]
    // 0xc38568: CheckStackOverflow
    //     0xc38568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3856c: cmp             SP, x16
    //     0xc38570: b.ls            #0xc38b20
    // 0xc38574: mov             x1, x10
    // 0xc38578: r2 = "maxp"
    //     0xc38578: add             x2, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0xc3857c: ldr             x2, [x2, #0x880]
    // 0xc38580: r0 = _getValueOrData()
    //     0xc38580: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc38584: ldur            x2, [fp, #-8]
    // 0xc38588: LoadField: r1 = r2->field_f
    //     0xc38588: ldur            w1, [x2, #0xf]
    // 0xc3858c: DecompressPointer r1
    //     0xc3858c: add             x1, x1, HEAP, lsl #32
    // 0xc38590: cmp             w1, w0
    // 0xc38594: b.ne            #0xc3859c
    // 0xc38598: r0 = Null
    //     0xc38598: mov             x0, NULL
    // 0xc3859c: ldur            x7, [fp, #-0x28]
    // 0xc385a0: ldur            x5, [fp, #-0x48]
    // 0xc385a4: ldur            x6, [fp, #-0x40]
    // 0xc385a8: r4 = 65280
    //     0xc385a8: orr             x4, xzr, #0xff00
    // 0xc385ac: r3 = 255
    //     0xc385ac: movz            x3, #0xff
    // 0xc385b0: cmp             w0, NULL
    // 0xc385b4: b.eq            #0xc38b28
    // 0xc385b8: r1 = LoadInt32Instr(r0)
    //     0xc385b8: sbfx            x1, x0, #1, #0x1f
    //     0xc385bc: tbz             w0, #0, #0xc385c4
    //     0xc385c0: ldur            x1, [x0, #7]
    // 0xc385c4: add             x8, x1, #4
    // 0xc385c8: ldur            x0, [fp, #-0x58]
    // 0xc385cc: mov             x1, x8
    // 0xc385d0: cmp             x1, x0
    // 0xc385d4: b.hs            #0xc38b2c
    // 0xc385d8: add             x0, x6, x8
    // 0xc385dc: LoadField: r1 = r5->field_7
    //     0xc385dc: ldur            x1, [x5, #7]
    // 0xc385e0: ldrh            w8, [x1, x0]
    // 0xc385e4: mov             x0, x8
    // 0xc385e8: ubfx            x0, x0, #0, #0x20
    // 0xc385ec: and             x1, x0, x4
    // 0xc385f0: ubfx            x1, x1, #0, #0x20
    // 0xc385f4: asr             x0, x1, #8
    // 0xc385f8: ubfx            x8, x8, #0, #0x20
    // 0xc385fc: and             x1, x8, x3
    // 0xc38600: ubfx            x1, x1, #0, #0x20
    // 0xc38604: lsl             x8, x1, #8
    // 0xc38608: orr             x1, x0, x8
    // 0xc3860c: add             x0, x1, #1
    // 0xc38610: cmp             x7, x0
    // 0xc38614: b.ge            #0xc38b00
    // 0xc38618: ldur            x9, [fp, #-0x38]
    // 0xc3861c: ldur            x8, [fp, #-0x50]
    // 0xc38620: lsl             x0, x7, #1
    // 0xc38624: add             x10, x8, x0
    // 0xc38628: ldur            x0, [fp, #-0x58]
    // 0xc3862c: mov             x1, x10
    // 0xc38630: cmp             x1, x0
    // 0xc38634: b.hs            #0xc38b30
    // 0xc38638: add             x0, x6, x10
    // 0xc3863c: LoadField: r1 = r5->field_7
    //     0xc3863c: ldur            x1, [x5, #7]
    // 0xc38640: ldrh            w10, [x1, x0]
    // 0xc38644: mov             x0, x10
    // 0xc38648: ubfx            x0, x0, #0, #0x20
    // 0xc3864c: and             x1, x0, x4
    // 0xc38650: ubfx            x1, x1, #0, #0x20
    // 0xc38654: asr             x0, x1, #8
    // 0xc38658: ubfx            x10, x10, #0, #0x20
    // 0xc3865c: and             x1, x10, x3
    // 0xc38660: ubfx            x1, x1, #0, #0x20
    // 0xc38664: lsl             x10, x1, #8
    // 0xc38668: orr             x1, x0, x10
    // 0xc3866c: lsl             x12, x1, #1
    // 0xc38670: stur            x12, [fp, #-0x68]
    // 0xc38674: LoadField: r0 = r9->field_b
    //     0xc38674: ldur            w0, [x9, #0xb]
    // 0xc38678: LoadField: r1 = r9->field_f
    //     0xc38678: ldur            w1, [x9, #0xf]
    // 0xc3867c: DecompressPointer r1
    //     0xc3867c: add             x1, x1, HEAP, lsl #32
    // 0xc38680: LoadField: r10 = r1->field_b
    //     0xc38680: ldur            w10, [x1, #0xb]
    // 0xc38684: r11 = LoadInt32Instr(r0)
    //     0xc38684: sbfx            x11, x0, #1, #0x1f
    // 0xc38688: stur            x11, [fp, #-0x60]
    // 0xc3868c: r0 = LoadInt32Instr(r10)
    //     0xc3868c: sbfx            x0, x10, #1, #0x1f
    // 0xc38690: cmp             x11, x0
    // 0xc38694: b.ne            #0xc386a0
    // 0xc38698: mov             x1, x9
    // 0xc3869c: r0 = _growToNextCapacity()
    //     0xc3869c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc386a0: ldur            x3, [fp, #-0x20]
    // 0xc386a4: ldur            x0, [fp, #-0x38]
    // 0xc386a8: ldur            x2, [fp, #-0x30]
    // 0xc386ac: ldur            x12, [fp, #-0x68]
    // 0xc386b0: ldur            x1, [fp, #-0x60]
    // 0xc386b4: add             x4, x1, #1
    // 0xc386b8: lsl             x5, x4, #1
    // 0xc386bc: StoreField: r0->field_b = r5
    //     0xc386bc: stur            w5, [x0, #0xb]
    // 0xc386c0: LoadField: r4 = r0->field_f
    //     0xc386c0: ldur            w4, [x0, #0xf]
    // 0xc386c4: DecompressPointer r4
    //     0xc386c4: add             x4, x4, HEAP, lsl #32
    // 0xc386c8: lsl             x5, x3, #1
    // 0xc386cc: ArrayStore: r4[r1] = r5  ; Unknown_4
    //     0xc386cc: add             x6, x4, x1, lsl #2
    //     0xc386d0: stur            w5, [x6, #0xf]
    // 0xc386d4: sub             x4, x12, x3
    // 0xc386d8: stur            x4, [fp, #-0x60]
    // 0xc386dc: LoadField: r1 = r2->field_b
    //     0xc386dc: ldur            w1, [x2, #0xb]
    // 0xc386e0: LoadField: r3 = r2->field_f
    //     0xc386e0: ldur            w3, [x2, #0xf]
    // 0xc386e4: DecompressPointer r3
    //     0xc386e4: add             x3, x3, HEAP, lsl #32
    // 0xc386e8: LoadField: r5 = r3->field_b
    //     0xc386e8: ldur            w5, [x3, #0xb]
    // 0xc386ec: r3 = LoadInt32Instr(r1)
    //     0xc386ec: sbfx            x3, x1, #1, #0x1f
    // 0xc386f0: stur            x3, [fp, #-0x20]
    // 0xc386f4: r1 = LoadInt32Instr(r5)
    //     0xc386f4: sbfx            x1, x5, #1, #0x1f
    // 0xc386f8: cmp             x3, x1
    // 0xc386fc: b.ne            #0xc38708
    // 0xc38700: mov             x1, x2
    // 0xc38704: r0 = _growToNextCapacity()
    //     0xc38704: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc38708: ldur            x3, [fp, #-0x28]
    // 0xc3870c: ldur            x0, [fp, #-0x30]
    // 0xc38710: ldur            x1, [fp, #-0x60]
    // 0xc38714: ldur            x2, [fp, #-0x20]
    // 0xc38718: add             x4, x2, #1
    // 0xc3871c: lsl             x5, x4, #1
    // 0xc38720: StoreField: r0->field_b = r5
    //     0xc38720: stur            w5, [x0, #0xb]
    // 0xc38724: LoadField: r4 = r0->field_f
    //     0xc38724: ldur            w4, [x0, #0xf]
    // 0xc38728: DecompressPointer r4
    //     0xc38728: add             x4, x4, HEAP, lsl #32
    // 0xc3872c: lsl             x5, x1, #1
    // 0xc38730: ArrayStore: r4[r2] = r5  ; Unknown_4
    //     0xc38730: add             x1, x4, x2, lsl #2
    //     0xc38734: stur            w5, [x1, #0xf]
    // 0xc38738: add             x11, x3, #1
    // 0xc3873c: ldur            x12, [fp, #-0x68]
    // 0xc38740: ldur            x8, [fp, #-0x38]
    // 0xc38744: mov             x9, x0
    // 0xc38748: ldur            x7, [fp, #-0x58]
    // 0xc3874c: ldur            x0, [fp, #-0x48]
    // 0xc38750: ldur            x6, [fp, #-0x50]
    // 0xc38754: ldur            x5, [fp, #-0x40]
    // 0xc38758: r4 = 65280
    //     0xc38758: orr             x4, xzr, #0xff00
    // 0xc3875c: r3 = 255
    //     0xc3875c: movz            x3, #0xff
    // 0xc38760: b               #0xc3855c
    // 0xc38764: ldur            x2, [fp, #-0x10]
    // 0xc38768: ldur            x0, [fp, #-0x18]
    // 0xc3876c: r6 = 4278255360
    //     0xc3876c: movz            x6, #0xff00
    //     0xc38770: movk            x6, #0xff00, lsl #16
    // 0xc38774: r5 = 16711935
    //     0xc38774: movz            x5, #0xff
    //     0xc38778: movk            x5, #0xff, lsl #16
    // 0xc3877c: r4 = 4294901760
    //     0xc3877c: orr             x4, xzr, #0xffff0000
    // 0xc38780: r3 = 65535
    //     0xc38780: orr             x3, xzr, #0xffff
    // 0xc38784: LoadField: r7 = r2->field_7
    //     0xc38784: ldur            w7, [x2, #7]
    // 0xc38788: DecompressPointer r7
    //     0xc38788: add             x7, x7, HEAP, lsl #32
    // 0xc3878c: LoadField: r1 = r7->field_13
    //     0xc3878c: ldur            w1, [x7, #0x13]
    // 0xc38790: r8 = LoadInt32Instr(r1)
    //     0xc38790: sbfx            x8, x1, #1, #0x1f
    // 0xc38794: sub             x9, x8, #3
    // 0xc38798: stur            x9, [fp, #-0x60]
    // 0xc3879c: r10 = LoadInt32Instr(r0)
    //     0xc3879c: sbfx            x10, x0, #1, #0x1f
    //     0xc387a0: tbz             w0, #0, #0xc387a8
    //     0xc387a4: ldur            x10, [x0, #7]
    // 0xc387a8: mov             x0, x9
    // 0xc387ac: mov             x1, x10
    // 0xc387b0: stur            x10, [fp, #-0x58]
    // 0xc387b4: cmp             x1, x0
    // 0xc387b8: b.hs            #0xc38b34
    // 0xc387bc: ArrayLoad: r0 = r7[0]  ; List_4
    //     0xc387bc: ldur            w0, [x7, #0x17]
    // 0xc387c0: DecompressPointer r0
    //     0xc387c0: add             x0, x0, HEAP, lsl #32
    // 0xc387c4: stur            x0, [fp, #-0x38]
    // 0xc387c8: LoadField: r1 = r7->field_1b
    //     0xc387c8: ldur            w1, [x7, #0x1b]
    // 0xc387cc: r7 = LoadInt32Instr(r1)
    //     0xc387cc: sbfx            x7, x1, #1, #0x1f
    // 0xc387d0: stur            x7, [fp, #-0x50]
    // 0xc387d4: add             x1, x7, x10
    // 0xc387d8: LoadField: r11 = r0->field_7
    //     0xc387d8: ldur            x11, [x0, #7]
    // 0xc387dc: ldr             w12, [x11, x1]
    // 0xc387e0: and             x1, x12, x6
    // 0xc387e4: ubfx            x1, x1, #0, #0x20
    // 0xc387e8: asr             x11, x1, #8
    // 0xc387ec: and             x1, x12, x5
    // 0xc387f0: ubfx            x1, x1, #0, #0x20
    // 0xc387f4: lsl             x12, x1, #8
    // 0xc387f8: orr             x1, x11, x12
    // 0xc387fc: mov             x11, x1
    // 0xc38800: ubfx            x11, x11, #0, #0x20
    // 0xc38804: and             x12, x11, x4
    // 0xc38808: ubfx            x12, x12, #0, #0x20
    // 0xc3880c: asr             x11, x12, #0x10
    // 0xc38810: ubfx            x1, x1, #0, #0x20
    // 0xc38814: and             x12, x1, x3
    // 0xc38818: ubfx            x12, x12, #0, #0x20
    // 0xc3881c: lsl             x1, x12, #0x10
    // 0xc38820: orr             x12, x11, x1
    // 0xc38824: sub             x11, x8, #1
    // 0xc38828: stur            x11, [fp, #-0x40]
    // 0xc3882c: ArrayLoad: r8 = r2[0]  ; List_4
    //     0xc3882c: ldur            w8, [x2, #0x17]
    // 0xc38830: DecompressPointer r8
    //     0xc38830: add             x8, x8, HEAP, lsl #32
    // 0xc38834: stur            x8, [fp, #-0x30]
    // 0xc38838: LoadField: r13 = r2->field_1b
    //     0xc38838: ldur            w13, [x2, #0x1b]
    // 0xc3883c: DecompressPointer r13
    //     0xc3883c: add             x13, x13, HEAP, lsl #32
    // 0xc38840: stur            x13, [fp, #-0x18]
    // 0xc38844: mov             x19, x12
    // 0xc38848: r14 = 1
    //     0xc38848: movz            x14, #0x1
    // 0xc3884c: ldur            x12, [fp, #-8]
    // 0xc38850: stur            x19, [fp, #-0x20]
    // 0xc38854: stur            x14, [fp, #-0x28]
    // 0xc38858: CheckStackOverflow
    //     0xc38858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3885c: cmp             SP, x16
    //     0xc38860: b.ls            #0xc38b38
    // 0xc38864: mov             x1, x12
    // 0xc38868: r2 = "maxp"
    //     0xc38868: add             x2, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0xc3886c: ldr             x2, [x2, #0x880]
    // 0xc38870: r0 = _getValueOrData()
    //     0xc38870: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc38874: ldur            x2, [fp, #-8]
    // 0xc38878: LoadField: r1 = r2->field_f
    //     0xc38878: ldur            w1, [x2, #0xf]
    // 0xc3887c: DecompressPointer r1
    //     0xc3887c: add             x1, x1, HEAP, lsl #32
    // 0xc38880: cmp             w1, w0
    // 0xc38884: b.ne            #0xc3888c
    // 0xc38888: r0 = Null
    //     0xc38888: mov             x0, NULL
    // 0xc3888c: ldur            x7, [fp, #-0x28]
    // 0xc38890: ldur            x3, [fp, #-0x38]
    // 0xc38894: ldur            x4, [fp, #-0x50]
    // 0xc38898: r6 = 65280
    //     0xc38898: orr             x6, xzr, #0xff00
    // 0xc3889c: r5 = 255
    //     0xc3889c: movz            x5, #0xff
    // 0xc388a0: cmp             w0, NULL
    // 0xc388a4: b.eq            #0xc38b40
    // 0xc388a8: r1 = LoadInt32Instr(r0)
    //     0xc388a8: sbfx            x1, x0, #1, #0x1f
    //     0xc388ac: tbz             w0, #0, #0xc388b4
    //     0xc388b0: ldur            x1, [x0, #7]
    // 0xc388b4: add             x8, x1, #4
    // 0xc388b8: ldur            x0, [fp, #-0x40]
    // 0xc388bc: mov             x1, x8
    // 0xc388c0: cmp             x1, x0
    // 0xc388c4: b.hs            #0xc38b44
    // 0xc388c8: add             x0, x4, x8
    // 0xc388cc: LoadField: r1 = r3->field_7
    //     0xc388cc: ldur            x1, [x3, #7]
    // 0xc388d0: ldrh            w8, [x1, x0]
    // 0xc388d4: mov             x0, x8
    // 0xc388d8: ubfx            x0, x0, #0, #0x20
    // 0xc388dc: and             x1, x0, x6
    // 0xc388e0: ubfx            x1, x1, #0, #0x20
    // 0xc388e4: asr             x0, x1, #8
    // 0xc388e8: ubfx            x8, x8, #0, #0x20
    // 0xc388ec: and             x1, x8, x5
    // 0xc388f0: ubfx            x1, x1, #0, #0x20
    // 0xc388f4: lsl             x8, x1, #8
    // 0xc388f8: orr             x1, x0, x8
    // 0xc388fc: add             x0, x1, #1
    // 0xc38900: cmp             x7, x0
    // 0xc38904: b.ge            #0xc38b00
    // 0xc38908: ldur            x13, [fp, #-0x30]
    // 0xc3890c: ldur            x12, [fp, #-0x58]
    // 0xc38910: r11 = 4278255360
    //     0xc38910: movz            x11, #0xff00
    //     0xc38914: movk            x11, #0xff00, lsl #16
    // 0xc38918: r10 = 16711935
    //     0xc38918: movz            x10, #0xff
    //     0xc3891c: movk            x10, #0xff, lsl #16
    // 0xc38920: r9 = 4294901760
    //     0xc38920: orr             x9, xzr, #0xffff0000
    // 0xc38924: r8 = 65535
    //     0xc38924: orr             x8, xzr, #0xffff
    // 0xc38928: lsl             x0, x7, #2
    // 0xc3892c: add             x14, x12, x0
    // 0xc38930: ldur            x0, [fp, #-0x60]
    // 0xc38934: mov             x1, x14
    // 0xc38938: cmp             x1, x0
    // 0xc3893c: b.hs            #0xc38b48
    // 0xc38940: add             x0, x4, x14
    // 0xc38944: LoadField: r1 = r3->field_7
    //     0xc38944: ldur            x1, [x3, #7]
    // 0xc38948: ldr             w14, [x1, x0]
    // 0xc3894c: and             x0, x14, x11
    // 0xc38950: ubfx            x0, x0, #0, #0x20
    // 0xc38954: asr             x1, x0, #8
    // 0xc38958: and             x0, x14, x10
    // 0xc3895c: ubfx            x0, x0, #0, #0x20
    // 0xc38960: lsl             x14, x0, #8
    // 0xc38964: orr             x0, x1, x14
    // 0xc38968: mov             x1, x0
    // 0xc3896c: ubfx            x1, x1, #0, #0x20
    // 0xc38970: and             x14, x1, x9
    // 0xc38974: ubfx            x14, x14, #0, #0x20
    // 0xc38978: asr             x1, x14, #0x10
    // 0xc3897c: ubfx            x0, x0, #0, #0x20
    // 0xc38980: and             x14, x0, x8
    // 0xc38984: ubfx            x14, x14, #0, #0x20
    // 0xc38988: lsl             x0, x14, #0x10
    // 0xc3898c: orr             x19, x1, x0
    // 0xc38990: stur            x19, [fp, #-0x70]
    // 0xc38994: LoadField: r0 = r13->field_b
    //     0xc38994: ldur            w0, [x13, #0xb]
    // 0xc38998: LoadField: r1 = r13->field_f
    //     0xc38998: ldur            w1, [x13, #0xf]
    // 0xc3899c: DecompressPointer r1
    //     0xc3899c: add             x1, x1, HEAP, lsl #32
    // 0xc389a0: LoadField: r14 = r1->field_b
    //     0xc389a0: ldur            w14, [x1, #0xb]
    // 0xc389a4: r20 = LoadInt32Instr(r0)
    //     0xc389a4: sbfx            x20, x0, #1, #0x1f
    // 0xc389a8: stur            x20, [fp, #-0x68]
    // 0xc389ac: r0 = LoadInt32Instr(r14)
    //     0xc389ac: sbfx            x0, x14, #1, #0x1f
    // 0xc389b0: cmp             x20, x0
    // 0xc389b4: b.ne            #0xc389c0
    // 0xc389b8: mov             x1, x13
    // 0xc389bc: r0 = _growToNextCapacity()
    //     0xc389bc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc389c0: ldur            x5, [fp, #-0x20]
    // 0xc389c4: ldur            x2, [fp, #-0x30]
    // 0xc389c8: ldur            x4, [fp, #-0x18]
    // 0xc389cc: ldur            x19, [fp, #-0x70]
    // 0xc389d0: ldur            x3, [fp, #-0x68]
    // 0xc389d4: add             x0, x3, #1
    // 0xc389d8: lsl             x1, x0, #1
    // 0xc389dc: StoreField: r2->field_b = r1
    //     0xc389dc: stur            w1, [x2, #0xb]
    // 0xc389e0: LoadField: r6 = r2->field_f
    //     0xc389e0: ldur            w6, [x2, #0xf]
    // 0xc389e4: DecompressPointer r6
    //     0xc389e4: add             x6, x6, HEAP, lsl #32
    // 0xc389e8: r0 = BoxInt64Instr(r5)
    //     0xc389e8: sbfiz           x0, x5, #1, #0x1f
    //     0xc389ec: cmp             x5, x0, asr #1
    //     0xc389f0: b.eq            #0xc389fc
    //     0xc389f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc389f8: stur            x5, [x0, #7]
    // 0xc389fc: mov             x1, x6
    // 0xc38a00: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc38a00: add             x25, x1, x3, lsl #2
    //     0xc38a04: add             x25, x25, #0xf
    //     0xc38a08: str             w0, [x25]
    //     0xc38a0c: tbz             w0, #0, #0xc38a28
    //     0xc38a10: ldurb           w16, [x1, #-1]
    //     0xc38a14: ldurb           w17, [x0, #-1]
    //     0xc38a18: and             x16, x17, x16, lsr #2
    //     0xc38a1c: tst             x16, HEAP, lsr #32
    //     0xc38a20: b.eq            #0xc38a28
    //     0xc38a24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc38a28: sub             x0, x19, x5
    // 0xc38a2c: stur            x0, [fp, #-0x68]
    // 0xc38a30: LoadField: r1 = r4->field_b
    //     0xc38a30: ldur            w1, [x4, #0xb]
    // 0xc38a34: LoadField: r3 = r4->field_f
    //     0xc38a34: ldur            w3, [x4, #0xf]
    // 0xc38a38: DecompressPointer r3
    //     0xc38a38: add             x3, x3, HEAP, lsl #32
    // 0xc38a3c: LoadField: r5 = r3->field_b
    //     0xc38a3c: ldur            w5, [x3, #0xb]
    // 0xc38a40: r3 = LoadInt32Instr(r1)
    //     0xc38a40: sbfx            x3, x1, #1, #0x1f
    // 0xc38a44: stur            x3, [fp, #-0x20]
    // 0xc38a48: r1 = LoadInt32Instr(r5)
    //     0xc38a48: sbfx            x1, x5, #1, #0x1f
    // 0xc38a4c: cmp             x3, x1
    // 0xc38a50: b.ne            #0xc38a5c
    // 0xc38a54: mov             x1, x4
    // 0xc38a58: r0 = _growToNextCapacity()
    //     0xc38a58: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc38a5c: ldur            x5, [fp, #-0x28]
    // 0xc38a60: ldur            x2, [fp, #-0x18]
    // 0xc38a64: ldur            x3, [fp, #-0x68]
    // 0xc38a68: ldur            x4, [fp, #-0x20]
    // 0xc38a6c: add             x6, x4, #1
    // 0xc38a70: lsl             x7, x6, #1
    // 0xc38a74: StoreField: r2->field_b = r7
    //     0xc38a74: stur            w7, [x2, #0xb]
    // 0xc38a78: LoadField: r6 = r2->field_f
    //     0xc38a78: ldur            w6, [x2, #0xf]
    // 0xc38a7c: DecompressPointer r6
    //     0xc38a7c: add             x6, x6, HEAP, lsl #32
    // 0xc38a80: r0 = BoxInt64Instr(r3)
    //     0xc38a80: sbfiz           x0, x3, #1, #0x1f
    //     0xc38a84: cmp             x3, x0, asr #1
    //     0xc38a88: b.eq            #0xc38a94
    //     0xc38a8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc38a90: stur            x3, [x0, #7]
    // 0xc38a94: mov             x1, x6
    // 0xc38a98: ArrayStore: r1[r4] = r0  ; List_4
    //     0xc38a98: add             x25, x1, x4, lsl #2
    //     0xc38a9c: add             x25, x25, #0xf
    //     0xc38aa0: str             w0, [x25]
    //     0xc38aa4: tbz             w0, #0, #0xc38ac0
    //     0xc38aa8: ldurb           w16, [x1, #-1]
    //     0xc38aac: ldurb           w17, [x0, #-1]
    //     0xc38ab0: and             x16, x17, x16, lsr #2
    //     0xc38ab4: tst             x16, HEAP, lsr #32
    //     0xc38ab8: b.eq            #0xc38ac0
    //     0xc38abc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc38ac0: add             x14, x5, #1
    // 0xc38ac4: ldur            x19, [fp, #-0x70]
    // 0xc38ac8: ldur            x8, [fp, #-0x30]
    // 0xc38acc: mov             x13, x2
    // 0xc38ad0: ldur            x9, [fp, #-0x60]
    // 0xc38ad4: ldur            x11, [fp, #-0x40]
    // 0xc38ad8: ldur            x0, [fp, #-0x38]
    // 0xc38adc: ldur            x10, [fp, #-0x58]
    // 0xc38ae0: ldur            x7, [fp, #-0x50]
    // 0xc38ae4: r6 = 4278255360
    //     0xc38ae4: movz            x6, #0xff00
    //     0xc38ae8: movk            x6, #0xff00, lsl #16
    // 0xc38aec: r5 = 16711935
    //     0xc38aec: movz            x5, #0xff
    //     0xc38af0: movk            x5, #0xff, lsl #16
    // 0xc38af4: r4 = 4294901760
    //     0xc38af4: orr             x4, xzr, #0xffff0000
    // 0xc38af8: r3 = 65535
    //     0xc38af8: orr             x3, xzr, #0xffff
    // 0xc38afc: b               #0xc3884c
    // 0xc38b00: r0 = Null
    //     0xc38b00: mov             x0, NULL
    // 0xc38b04: LeaveFrame
    //     0xc38b04: mov             SP, fp
    //     0xc38b08: ldp             fp, lr, [SP], #0x10
    // 0xc38b0c: ret
    //     0xc38b0c: ret             
    // 0xc38b10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38b10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38b14: b               #0xc3844c
    // 0xc38b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38b18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38b1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38b20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38b20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38b24: b               #0xc38574
    // 0xc38b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38b28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38b2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38b30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38b34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38b38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38b38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38b3c: b               #0xc38864
    // 0xc38b40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38b40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38b44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38b48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38b48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseCMap(/* No info */) {
    // ** addr: 0xc38b4c, size: 0x3d4
    // 0xc38b4c: EnterFrame
    //     0xc38b4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc38b50: mov             fp, SP
    // 0xc38b54: AllocStack(0x70)
    //     0xc38b54: sub             SP, SP, #0x70
    // 0xc38b58: SetupParameters(TtfParser this /* r1 => r0, fp-0x10 */)
    //     0xc38b58: mov             x0, x1
    //     0xc38b5c: stur            x1, [fp, #-0x10]
    // 0xc38b60: CheckStackOverflow
    //     0xc38b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc38b64: cmp             SP, x16
    //     0xc38b68: b.ls            #0xc38ef4
    // 0xc38b6c: LoadField: r3 = r0->field_b
    //     0xc38b6c: ldur            w3, [x0, #0xb]
    // 0xc38b70: DecompressPointer r3
    //     0xc38b70: add             x3, x3, HEAP, lsl #32
    // 0xc38b74: mov             x1, x3
    // 0xc38b78: stur            x3, [fp, #-8]
    // 0xc38b7c: r2 = "cmap"
    //     0xc38b7c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33898] "cmap"
    //     0xc38b80: ldr             x2, [x2, #0x898]
    // 0xc38b84: r0 = _getValueOrData()
    //     0xc38b84: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc38b88: mov             x1, x0
    // 0xc38b8c: ldur            x0, [fp, #-8]
    // 0xc38b90: LoadField: r2 = r0->field_f
    //     0xc38b90: ldur            w2, [x0, #0xf]
    // 0xc38b94: DecompressPointer r2
    //     0xc38b94: add             x2, x2, HEAP, lsl #32
    // 0xc38b98: cmp             w2, w1
    // 0xc38b9c: b.ne            #0xc38ba8
    // 0xc38ba0: r0 = Null
    //     0xc38ba0: mov             x0, NULL
    // 0xc38ba4: b               #0xc38bac
    // 0xc38ba8: mov             x0, x1
    // 0xc38bac: ldur            x3, [fp, #-0x10]
    // 0xc38bb0: r5 = 65280
    //     0xc38bb0: orr             x5, xzr, #0xff00
    // 0xc38bb4: r4 = 255
    //     0xc38bb4: movz            x4, #0xff
    // 0xc38bb8: cmp             w0, NULL
    // 0xc38bbc: b.eq            #0xc38efc
    // 0xc38bc0: LoadField: r2 = r3->field_7
    //     0xc38bc0: ldur            w2, [x3, #7]
    // 0xc38bc4: DecompressPointer r2
    //     0xc38bc4: add             x2, x2, HEAP, lsl #32
    // 0xc38bc8: r6 = LoadInt32Instr(r0)
    //     0xc38bc8: sbfx            x6, x0, #1, #0x1f
    //     0xc38bcc: tbz             w0, #0, #0xc38bd4
    //     0xc38bd0: ldur            x6, [x0, #7]
    // 0xc38bd4: stur            x6, [fp, #-0x70]
    // 0xc38bd8: add             x7, x6, #2
    // 0xc38bdc: LoadField: r0 = r2->field_13
    //     0xc38bdc: ldur            w0, [x2, #0x13]
    // 0xc38be0: r8 = LoadInt32Instr(r0)
    //     0xc38be0: sbfx            x8, x0, #1, #0x1f
    // 0xc38be4: stur            x8, [fp, #-0x68]
    // 0xc38be8: sub             x9, x8, #1
    // 0xc38bec: mov             x0, x9
    // 0xc38bf0: mov             x1, x7
    // 0xc38bf4: stur            x9, [fp, #-0x60]
    // 0xc38bf8: cmp             x1, x0
    // 0xc38bfc: b.hs            #0xc38f00
    // 0xc38c00: ArrayLoad: r10 = r2[0]  ; List_4
    //     0xc38c00: ldur            w10, [x2, #0x17]
    // 0xc38c04: DecompressPointer r10
    //     0xc38c04: add             x10, x10, HEAP, lsl #32
    // 0xc38c08: stur            x10, [fp, #-0x58]
    // 0xc38c0c: LoadField: r0 = r2->field_1b
    //     0xc38c0c: ldur            w0, [x2, #0x1b]
    // 0xc38c10: r11 = LoadInt32Instr(r0)
    //     0xc38c10: sbfx            x11, x0, #1, #0x1f
    // 0xc38c14: stur            x11, [fp, #-0x50]
    // 0xc38c18: add             x0, x11, x7
    // 0xc38c1c: LoadField: r1 = r10->field_7
    //     0xc38c1c: ldur            x1, [x10, #7]
    // 0xc38c20: ldrh            w2, [x1, x0]
    // 0xc38c24: mov             x0, x2
    // 0xc38c28: ubfx            x0, x0, #0, #0x20
    // 0xc38c2c: and             x1, x0, x5
    // 0xc38c30: ubfx            x1, x1, #0, #0x20
    // 0xc38c34: asr             x0, x1, #8
    // 0xc38c38: ubfx            x2, x2, #0, #0x20
    // 0xc38c3c: and             x1, x2, x4
    // 0xc38c40: ubfx            x1, x1, #0, #0x20
    // 0xc38c44: lsl             x2, x1, #8
    // 0xc38c48: orr             x7, x0, x2
    // 0xc38c4c: stur            x7, [fp, #-0x48]
    // 0xc38c50: sub             x12, x8, #3
    // 0xc38c54: stur            x12, [fp, #-0x40]
    // 0xc38c58: LoadField: r13 = r3->field_13
    //     0xc38c58: ldur            w13, [x3, #0x13]
    // 0xc38c5c: DecompressPointer r13
    //     0xc38c5c: add             x13, x13, HEAP, lsl #32
    // 0xc38c60: stur            x13, [fp, #-0x38]
    // 0xc38c64: r24 = 0
    //     0xc38c64: movz            x24, #0
    // 0xc38c68: r23 = 4278255360
    //     0xc38c68: movz            x23, #0xff00
    //     0xc38c6c: movk            x23, #0xff00, lsl #16
    // 0xc38c70: r20 = 16711935
    //     0xc38c70: movz            x20, #0xff
    //     0xc38c74: movk            x20, #0xff, lsl #16
    // 0xc38c78: r19 = 4294901760
    //     0xc38c78: orr             x19, xzr, #0xffff0000
    // 0xc38c7c: r14 = 65535
    //     0xc38c7c: orr             x14, xzr, #0xffff
    // 0xc38c80: stur            x24, [fp, #-0x30]
    // 0xc38c84: CheckStackOverflow
    //     0xc38c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc38c88: cmp             SP, x16
    //     0xc38c8c: b.ls            #0xc38f04
    // 0xc38c90: cmp             x24, x7
    // 0xc38c94: b.ge            #0xc38ee4
    // 0xc38c98: lsl             x0, x24, #3
    // 0xc38c9c: add             x1, x6, x0
    // 0xc38ca0: add             x2, x1, #8
    // 0xc38ca4: mov             x0, x12
    // 0xc38ca8: mov             x1, x2
    // 0xc38cac: cmp             x1, x0
    // 0xc38cb0: b.hs            #0xc38f0c
    // 0xc38cb4: add             x0, x11, x2
    // 0xc38cb8: LoadField: r1 = r10->field_7
    //     0xc38cb8: ldur            x1, [x10, #7]
    // 0xc38cbc: ldr             w2, [x1, x0]
    // 0xc38cc0: and             x0, x2, x23
    // 0xc38cc4: ubfx            x0, x0, #0, #0x20
    // 0xc38cc8: asr             x1, x0, #8
    // 0xc38ccc: and             x0, x2, x20
    // 0xc38cd0: ubfx            x0, x0, #0, #0x20
    // 0xc38cd4: lsl             x2, x0, #8
    // 0xc38cd8: orr             x0, x1, x2
    // 0xc38cdc: mov             x1, x0
    // 0xc38ce0: ubfx            x1, x1, #0, #0x20
    // 0xc38ce4: and             x2, x1, x19
    // 0xc38ce8: ubfx            x2, x2, #0, #0x20
    // 0xc38cec: asr             x1, x2, #0x10
    // 0xc38cf0: ubfx            x0, x0, #0, #0x20
    // 0xc38cf4: and             x2, x0, x14
    // 0xc38cf8: ubfx            x2, x2, #0, #0x20
    // 0xc38cfc: lsl             x0, x2, #0x10
    // 0xc38d00: orr             x2, x1, x0
    // 0xc38d04: add             x25, x6, x2
    // 0xc38d08: mov             x0, x9
    // 0xc38d0c: mov             x1, x25
    // 0xc38d10: cmp             x1, x0
    // 0xc38d14: b.hs            #0xc38f10
    // 0xc38d18: add             x0, x11, x25
    // 0xc38d1c: LoadField: r1 = r10->field_7
    //     0xc38d1c: ldur            x1, [x10, #7]
    // 0xc38d20: ldrh            w2, [x1, x0]
    // 0xc38d24: mov             x0, x2
    // 0xc38d28: ubfx            x0, x0, #0, #0x20
    // 0xc38d2c: and             x1, x0, x5
    // 0xc38d30: ubfx            x1, x1, #0, #0x20
    // 0xc38d34: asr             x0, x1, #8
    // 0xc38d38: ubfx            x2, x2, #0, #0x20
    // 0xc38d3c: and             x1, x2, x4
    // 0xc38d40: ubfx            x1, x1, #0, #0x20
    // 0xc38d44: lsl             x2, x1, #8
    // 0xc38d48: orr             x1, x0, x2
    // 0xc38d4c: cmp             x1, #4
    // 0xc38d50: b.gt            #0xc38e6c
    // 0xc38d54: cmp             x1, #0
    // 0xc38d58: b.gt            #0xc38e54
    // 0xc38d5c: lsl             x0, x1, #1
    // 0xc38d60: cbnz            w0, #0xc38eac
    // 0xc38d64: add             x2, x25, #2
    // 0xc38d68: stur            x2, [fp, #-0x18]
    // 0xc38d6c: r25 = 0
    //     0xc38d6c: movz            x25, #0
    // 0xc38d70: stur            x25, [fp, #-0x28]
    // 0xc38d74: CheckStackOverflow
    //     0xc38d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc38d78: cmp             SP, x16
    //     0xc38d7c: b.ls            #0xc38f14
    // 0xc38d80: cmp             x25, #0x100
    // 0xc38d84: b.ge            #0xc38eac
    // 0xc38d88: add             x0, x2, x25
    // 0xc38d8c: add             x1, x0, #2
    // 0xc38d90: mov             x0, x8
    // 0xc38d94: mov             x3, x1
    // 0xc38d98: cmp             x1, x0
    // 0xc38d9c: b.hs            #0xc38f1c
    // 0xc38da0: add             x0, x11, x3
    // 0xc38da4: LoadField: r1 = r10->field_7
    //     0xc38da4: ldur            x1, [x10, #7]
    // 0xc38da8: ldrb            w3, [x1, x0]
    // 0xc38dac: lsl             x0, x3, #1
    // 0xc38db0: stur            x0, [fp, #-0x20]
    // 0xc38db4: cmp             x3, #0
    // 0xc38db8: b.le            #0xc38e00
    // 0xc38dbc: mov             x3, x0
    // 0xc38dc0: r0 = BoxInt64Instr(r25)
    //     0xc38dc0: sbfiz           x0, x25, #1, #0x1f
    //     0xc38dc4: cmp             x25, x0, asr #1
    //     0xc38dc8: b.eq            #0xc38dd4
    //     0xc38dcc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc38dd0: stur            x25, [x0, #7]
    // 0xc38dd4: mov             x1, x13
    // 0xc38dd8: stur            x0, [fp, #-8]
    // 0xc38ddc: mov             x16, x2
    // 0xc38de0: mov             x2, x0
    // 0xc38de4: mov             x0, x16
    // 0xc38de8: r0 = _hashCode()
    //     0xc38de8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc38dec: ldur            x1, [fp, #-0x38]
    // 0xc38df0: ldur            x2, [fp, #-8]
    // 0xc38df4: ldur            x3, [fp, #-0x20]
    // 0xc38df8: mov             x5, x0
    // 0xc38dfc: r0 = _set()
    //     0xc38dfc: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc38e00: ldur            x0, [fp, #-0x28]
    // 0xc38e04: add             x25, x0, #1
    // 0xc38e08: ldur            x24, [fp, #-0x30]
    // 0xc38e0c: ldur            x2, [fp, #-0x18]
    // 0xc38e10: ldur            x13, [fp, #-0x38]
    // 0xc38e14: ldur            x12, [fp, #-0x40]
    // 0xc38e18: ldur            x9, [fp, #-0x60]
    // 0xc38e1c: ldur            x10, [fp, #-0x58]
    // 0xc38e20: ldur            x7, [fp, #-0x48]
    // 0xc38e24: ldur            x6, [fp, #-0x70]
    // 0xc38e28: ldur            x8, [fp, #-0x68]
    // 0xc38e2c: ldur            x11, [fp, #-0x50]
    // 0xc38e30: r5 = 65280
    //     0xc38e30: orr             x5, xzr, #0xff00
    // 0xc38e34: r4 = 255
    //     0xc38e34: movz            x4, #0xff
    // 0xc38e38: r23 = 4278255360
    //     0xc38e38: movz            x23, #0xff00
    //     0xc38e3c: movk            x23, #0xff00, lsl #16
    // 0xc38e40: r20 = 16711935
    //     0xc38e40: movz            x20, #0xff
    //     0xc38e44: movk            x20, #0xff, lsl #16
    // 0xc38e48: r19 = 4294901760
    //     0xc38e48: orr             x19, xzr, #0xffff0000
    // 0xc38e4c: r14 = 65535
    //     0xc38e4c: orr             x14, xzr, #0xffff
    // 0xc38e50: b               #0xc38d70
    // 0xc38e54: cmp             x1, #4
    // 0xc38e58: b.lt            #0xc38eac
    // 0xc38e5c: add             x2, x25, #2
    // 0xc38e60: ldur            x1, [fp, #-0x10]
    // 0xc38e64: r0 = _parseCMapFormat4()
    //     0xc38e64: bl              #0xc394b4  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseCMapFormat4
    // 0xc38e68: b               #0xc38eac
    // 0xc38e6c: cmp             x1, #6
    // 0xc38e70: b.lt            #0xc38eac
    // 0xc38e74: cmp             x1, #6
    // 0xc38e78: b.gt            #0xc38e8c
    // 0xc38e7c: add             x2, x25, #2
    // 0xc38e80: ldur            x1, [fp, #-0x10]
    // 0xc38e84: r0 = _parseCMapFormat6()
    //     0xc38e84: bl              #0xc3929c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseCMapFormat6
    // 0xc38e88: b               #0xc38eac
    // 0xc38e8c: cmp             x1, #0xc
    // 0xc38e90: b.lt            #0xc38eac
    // 0xc38e94: lsl             x0, x1, #1
    // 0xc38e98: cmp             w0, #0x18
    // 0xc38e9c: b.ne            #0xc38eac
    // 0xc38ea0: add             x2, x25, #2
    // 0xc38ea4: ldur            x1, [fp, #-0x10]
    // 0xc38ea8: r0 = _parseCMapFormat12()
    //     0xc38ea8: bl              #0xc38f20  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::_parseCMapFormat12
    // 0xc38eac: ldur            x1, [fp, #-0x30]
    // 0xc38eb0: add             x24, x1, #1
    // 0xc38eb4: ldur            x3, [fp, #-0x10]
    // 0xc38eb8: ldur            x13, [fp, #-0x38]
    // 0xc38ebc: ldur            x12, [fp, #-0x40]
    // 0xc38ec0: ldur            x9, [fp, #-0x60]
    // 0xc38ec4: ldur            x10, [fp, #-0x58]
    // 0xc38ec8: ldur            x7, [fp, #-0x48]
    // 0xc38ecc: ldur            x6, [fp, #-0x70]
    // 0xc38ed0: ldur            x8, [fp, #-0x68]
    // 0xc38ed4: ldur            x11, [fp, #-0x50]
    // 0xc38ed8: r5 = 65280
    //     0xc38ed8: orr             x5, xzr, #0xff00
    // 0xc38edc: r4 = 255
    //     0xc38edc: movz            x4, #0xff
    // 0xc38ee0: b               #0xc38c68
    // 0xc38ee4: r0 = Null
    //     0xc38ee4: mov             x0, NULL
    // 0xc38ee8: LeaveFrame
    //     0xc38ee8: mov             SP, fp
    //     0xc38eec: ldp             fp, lr, [SP], #0x10
    // 0xc38ef0: ret
    //     0xc38ef0: ret             
    // 0xc38ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38ef8: b               #0xc38b6c
    // 0xc38efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc38efc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc38f00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38f00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38f04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38f08: b               #0xc38c90
    // 0xc38f0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38f0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38f10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38f10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc38f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc38f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc38f18: b               #0xc38d80
    // 0xc38f1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc38f1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseCMapFormat12(/* No info */) {
    // ** addr: 0xc38f20, size: 0x37c
    // 0xc38f20: EnterFrame
    //     0xc38f20: stp             fp, lr, [SP, #-0x10]!
    //     0xc38f24: mov             fp, SP
    // 0xc38f28: AllocStack(0x68)
    //     0xc38f28: sub             SP, SP, #0x68
    // 0xc38f2c: r6 = 4278255360
    //     0xc38f2c: movz            x6, #0xff00
    //     0xc38f30: movk            x6, #0xff00, lsl #16
    // 0xc38f34: r5 = 16711935
    //     0xc38f34: movz            x5, #0xff
    //     0xc38f38: movk            x5, #0xff, lsl #16
    // 0xc38f3c: r4 = 4294901760
    //     0xc38f3c: orr             x4, xzr, #0xffff0000
    // 0xc38f40: r3 = 65535
    //     0xc38f40: orr             x3, xzr, #0xffff
    // 0xc38f44: mov             x7, x2
    // 0xc38f48: stur            x2, [fp, #-0x68]
    // 0xc38f4c: mov             x2, x1
    // 0xc38f50: CheckStackOverflow
    //     0xc38f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc38f54: cmp             SP, x16
    //     0xc38f58: b.ls            #0xc39274
    // 0xc38f5c: LoadField: r8 = r2->field_7
    //     0xc38f5c: ldur            w8, [x2, #7]
    // 0xc38f60: DecompressPointer r8
    //     0xc38f60: add             x8, x8, HEAP, lsl #32
    // 0xc38f64: add             x9, x7, #0xa
    // 0xc38f68: LoadField: r0 = r8->field_13
    //     0xc38f68: ldur            w0, [x8, #0x13]
    // 0xc38f6c: r1 = LoadInt32Instr(r0)
    //     0xc38f6c: sbfx            x1, x0, #1, #0x1f
    // 0xc38f70: sub             x10, x1, #3
    // 0xc38f74: mov             x0, x10
    // 0xc38f78: mov             x1, x9
    // 0xc38f7c: stur            x10, [fp, #-0x60]
    // 0xc38f80: cmp             x1, x0
    // 0xc38f84: b.hs            #0xc3927c
    // 0xc38f88: ArrayLoad: r11 = r8[0]  ; List_4
    //     0xc38f88: ldur            w11, [x8, #0x17]
    // 0xc38f8c: DecompressPointer r11
    //     0xc38f8c: add             x11, x11, HEAP, lsl #32
    // 0xc38f90: stur            x11, [fp, #-0x58]
    // 0xc38f94: LoadField: r0 = r8->field_1b
    //     0xc38f94: ldur            w0, [x8, #0x1b]
    // 0xc38f98: r8 = LoadInt32Instr(r0)
    //     0xc38f98: sbfx            x8, x0, #1, #0x1f
    // 0xc38f9c: stur            x8, [fp, #-0x50]
    // 0xc38fa0: add             x0, x8, x9
    // 0xc38fa4: LoadField: r1 = r11->field_7
    //     0xc38fa4: ldur            x1, [x11, #7]
    // 0xc38fa8: ldr             w9, [x1, x0]
    // 0xc38fac: and             x0, x9, x6
    // 0xc38fb0: ubfx            x0, x0, #0, #0x20
    // 0xc38fb4: asr             x1, x0, #8
    // 0xc38fb8: and             x0, x9, x5
    // 0xc38fbc: ubfx            x0, x0, #0, #0x20
    // 0xc38fc0: lsl             x9, x0, #8
    // 0xc38fc4: orr             x0, x1, x9
    // 0xc38fc8: mov             x1, x0
    // 0xc38fcc: ubfx            x1, x1, #0, #0x20
    // 0xc38fd0: and             x9, x1, x4
    // 0xc38fd4: ubfx            x9, x9, #0, #0x20
    // 0xc38fd8: asr             x1, x9, #0x10
    // 0xc38fdc: ubfx            x0, x0, #0, #0x20
    // 0xc38fe0: and             x9, x0, x3
    // 0xc38fe4: ubfx            x9, x9, #0, #0x20
    // 0xc38fe8: lsl             x0, x9, #0x10
    // 0xc38fec: orr             x9, x1, x0
    // 0xc38ff0: stur            x9, [fp, #-0x48]
    // 0xc38ff4: LoadField: r12 = r2->field_13
    //     0xc38ff4: ldur            w12, [x2, #0x13]
    // 0xc38ff8: DecompressPointer r12
    //     0xc38ff8: add             x12, x12, HEAP, lsl #32
    // 0xc38ffc: stur            x12, [fp, #-0x40]
    // 0xc39000: r13 = 0
    //     0xc39000: movz            x13, #0
    // 0xc39004: stur            x13, [fp, #-0x38]
    // 0xc39008: CheckStackOverflow
    //     0xc39008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3900c: cmp             SP, x16
    //     0xc39010: b.ls            #0xc39280
    // 0xc39014: cmp             x13, x9
    // 0xc39018: b.ge            #0xc39264
    // 0xc3901c: r16 = 12
    //     0xc3901c: movz            x16, #0xc
    // 0xc39020: mul             x0, x13, x16
    // 0xc39024: add             x2, x7, x0
    // 0xc39028: add             x14, x2, #0xe
    // 0xc3902c: mov             x0, x10
    // 0xc39030: mov             x1, x14
    // 0xc39034: cmp             x1, x0
    // 0xc39038: b.hs            #0xc39288
    // 0xc3903c: add             x0, x8, x14
    // 0xc39040: LoadField: r1 = r11->field_7
    //     0xc39040: ldur            x1, [x11, #7]
    // 0xc39044: ldr             w14, [x1, x0]
    // 0xc39048: and             x0, x14, x6
    // 0xc3904c: ubfx            x0, x0, #0, #0x20
    // 0xc39050: asr             x1, x0, #8
    // 0xc39054: and             x0, x14, x5
    // 0xc39058: ubfx            x0, x0, #0, #0x20
    // 0xc3905c: lsl             x14, x0, #8
    // 0xc39060: orr             x0, x1, x14
    // 0xc39064: mov             x1, x0
    // 0xc39068: ubfx            x1, x1, #0, #0x20
    // 0xc3906c: and             x14, x1, x4
    // 0xc39070: ubfx            x14, x14, #0, #0x20
    // 0xc39074: asr             x1, x14, #0x10
    // 0xc39078: ubfx            x0, x0, #0, #0x20
    // 0xc3907c: and             x14, x0, x3
    // 0xc39080: ubfx            x14, x14, #0, #0x20
    // 0xc39084: lsl             x0, x14, #0x10
    // 0xc39088: orr             x14, x1, x0
    // 0xc3908c: stur            x14, [fp, #-0x30]
    // 0xc39090: add             x19, x2, #0x12
    // 0xc39094: mov             x0, x10
    // 0xc39098: mov             x1, x19
    // 0xc3909c: cmp             x1, x0
    // 0xc390a0: b.hs            #0xc3928c
    // 0xc390a4: add             x0, x8, x19
    // 0xc390a8: LoadField: r1 = r11->field_7
    //     0xc390a8: ldur            x1, [x11, #7]
    // 0xc390ac: ldr             w19, [x1, x0]
    // 0xc390b0: and             x0, x19, x6
    // 0xc390b4: ubfx            x0, x0, #0, #0x20
    // 0xc390b8: asr             x1, x0, #8
    // 0xc390bc: and             x0, x19, x5
    // 0xc390c0: ubfx            x0, x0, #0, #0x20
    // 0xc390c4: lsl             x19, x0, #8
    // 0xc390c8: orr             x0, x1, x19
    // 0xc390cc: mov             x1, x0
    // 0xc390d0: ubfx            x1, x1, #0, #0x20
    // 0xc390d4: and             x19, x1, x4
    // 0xc390d8: ubfx            x19, x19, #0, #0x20
    // 0xc390dc: asr             x1, x19, #0x10
    // 0xc390e0: ubfx            x0, x0, #0, #0x20
    // 0xc390e4: and             x19, x0, x3
    // 0xc390e8: ubfx            x19, x19, #0, #0x20
    // 0xc390ec: lsl             x0, x19, #0x10
    // 0xc390f0: orr             x19, x1, x0
    // 0xc390f4: stur            x19, [fp, #-0x28]
    // 0xc390f8: add             x20, x2, #0x16
    // 0xc390fc: mov             x0, x10
    // 0xc39100: mov             x1, x20
    // 0xc39104: cmp             x1, x0
    // 0xc39108: b.hs            #0xc39290
    // 0xc3910c: add             x0, x8, x20
    // 0xc39110: LoadField: r1 = r11->field_7
    //     0xc39110: ldur            x1, [x11, #7]
    // 0xc39114: ldr             w2, [x1, x0]
    // 0xc39118: and             x0, x2, x6
    // 0xc3911c: ubfx            x0, x0, #0, #0x20
    // 0xc39120: asr             x1, x0, #8
    // 0xc39124: and             x0, x2, x5
    // 0xc39128: ubfx            x0, x0, #0, #0x20
    // 0xc3912c: lsl             x2, x0, #8
    // 0xc39130: orr             x0, x1, x2
    // 0xc39134: mov             x1, x0
    // 0xc39138: ubfx            x1, x1, #0, #0x20
    // 0xc3913c: and             x2, x1, x4
    // 0xc39140: ubfx            x2, x2, #0, #0x20
    // 0xc39144: asr             x1, x2, #0x10
    // 0xc39148: ubfx            x0, x0, #0, #0x20
    // 0xc3914c: and             x2, x0, x3
    // 0xc39150: ubfx            x2, x2, #0, #0x20
    // 0xc39154: lsl             x0, x2, #0x10
    // 0xc39158: orr             x20, x1, x0
    // 0xc3915c: stur            x20, [fp, #-0x20]
    // 0xc39160: mov             x23, x14
    // 0xc39164: stur            x23, [fp, #-0x18]
    // 0xc39168: CheckStackOverflow
    //     0xc39168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3916c: cmp             SP, x16
    //     0xc39170: b.ls            #0xc39294
    // 0xc39174: cmp             x23, x19
    // 0xc39178: b.gt            #0xc39228
    // 0xc3917c: add             x0, x20, x23
    // 0xc39180: sub             x24, x0, x14
    // 0xc39184: stur            x24, [fp, #-0x10]
    // 0xc39188: r0 = BoxInt64Instr(r23)
    //     0xc39188: sbfiz           x0, x23, #1, #0x1f
    //     0xc3918c: cmp             x23, x0, asr #1
    //     0xc39190: b.eq            #0xc3919c
    //     0xc39194: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc39198: stur            x23, [x0, #7]
    // 0xc3919c: mov             x1, x12
    // 0xc391a0: mov             x2, x0
    // 0xc391a4: stur            x0, [fp, #-8]
    // 0xc391a8: r0 = _hashCode()
    //     0xc391a8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc391ac: mov             x3, x0
    // 0xc391b0: ldur            x2, [fp, #-0x10]
    // 0xc391b4: r0 = BoxInt64Instr(r2)
    //     0xc391b4: sbfiz           x0, x2, #1, #0x1f
    //     0xc391b8: cmp             x2, x0, asr #1
    //     0xc391bc: b.eq            #0xc391c8
    //     0xc391c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc391c4: stur            x2, [x0, #7]
    // 0xc391c8: ldur            x1, [fp, #-0x40]
    // 0xc391cc: ldur            x2, [fp, #-8]
    // 0xc391d0: mov             x5, x3
    // 0xc391d4: mov             x3, x0
    // 0xc391d8: r0 = _set()
    //     0xc391d8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc391dc: ldur            x1, [fp, #-0x18]
    // 0xc391e0: add             x23, x1, #1
    // 0xc391e4: ldur            x7, [fp, #-0x68]
    // 0xc391e8: ldur            x13, [fp, #-0x38]
    // 0xc391ec: ldur            x12, [fp, #-0x40]
    // 0xc391f0: ldur            x10, [fp, #-0x60]
    // 0xc391f4: ldur            x14, [fp, #-0x30]
    // 0xc391f8: ldur            x19, [fp, #-0x28]
    // 0xc391fc: ldur            x20, [fp, #-0x20]
    // 0xc39200: ldur            x11, [fp, #-0x58]
    // 0xc39204: ldur            x9, [fp, #-0x48]
    // 0xc39208: ldur            x8, [fp, #-0x50]
    // 0xc3920c: r6 = 4278255360
    //     0xc3920c: movz            x6, #0xff00
    //     0xc39210: movk            x6, #0xff00, lsl #16
    // 0xc39214: r5 = 16711935
    //     0xc39214: movz            x5, #0xff
    //     0xc39218: movk            x5, #0xff, lsl #16
    // 0xc3921c: r4 = 4294901760
    //     0xc3921c: orr             x4, xzr, #0xffff0000
    // 0xc39220: r3 = 65535
    //     0xc39220: orr             x3, xzr, #0xffff
    // 0xc39224: b               #0xc39164
    // 0xc39228: mov             x1, x13
    // 0xc3922c: add             x13, x1, #1
    // 0xc39230: ldur            x7, [fp, #-0x68]
    // 0xc39234: ldur            x12, [fp, #-0x40]
    // 0xc39238: ldur            x10, [fp, #-0x60]
    // 0xc3923c: ldur            x11, [fp, #-0x58]
    // 0xc39240: ldur            x9, [fp, #-0x48]
    // 0xc39244: ldur            x8, [fp, #-0x50]
    // 0xc39248: r6 = 4278255360
    //     0xc39248: movz            x6, #0xff00
    //     0xc3924c: movk            x6, #0xff00, lsl #16
    // 0xc39250: r5 = 16711935
    //     0xc39250: movz            x5, #0xff
    //     0xc39254: movk            x5, #0xff, lsl #16
    // 0xc39258: r4 = 4294901760
    //     0xc39258: orr             x4, xzr, #0xffff0000
    // 0xc3925c: r3 = 65535
    //     0xc3925c: orr             x3, xzr, #0xffff
    // 0xc39260: b               #0xc39004
    // 0xc39264: r0 = Null
    //     0xc39264: mov             x0, NULL
    // 0xc39268: LeaveFrame
    //     0xc39268: mov             SP, fp
    //     0xc3926c: ldp             fp, lr, [SP], #0x10
    // 0xc39270: ret
    //     0xc39270: ret             
    // 0xc39274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39278: b               #0xc38f5c
    // 0xc3927c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3927c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39280: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39280: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39284: b               #0xc39014
    // 0xc39288: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39288: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc3928c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3928c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39290: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39290: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39298: b               #0xc39174
  }
  _ _parseCMapFormat6(/* No info */) {
    // ** addr: 0xc3929c, size: 0x218
    // 0xc3929c: EnterFrame
    //     0xc3929c: stp             fp, lr, [SP, #-0x10]!
    //     0xc392a0: mov             fp, SP
    // 0xc392a4: AllocStack(0x50)
    //     0xc392a4: sub             SP, SP, #0x50
    // 0xc392a8: r4 = 65280
    //     0xc392a8: orr             x4, xzr, #0xff00
    // 0xc392ac: r3 = 255
    //     0xc392ac: movz            x3, #0xff
    // 0xc392b0: mov             x5, x2
    // 0xc392b4: stur            x2, [fp, #-0x50]
    // 0xc392b8: mov             x2, x1
    // 0xc392bc: CheckStackOverflow
    //     0xc392bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc392c0: cmp             SP, x16
    //     0xc392c4: b.ls            #0xc39498
    // 0xc392c8: LoadField: r6 = r2->field_7
    //     0xc392c8: ldur            w6, [x2, #7]
    // 0xc392cc: DecompressPointer r6
    //     0xc392cc: add             x6, x6, HEAP, lsl #32
    // 0xc392d0: add             x7, x5, #4
    // 0xc392d4: LoadField: r0 = r6->field_13
    //     0xc392d4: ldur            w0, [x6, #0x13]
    // 0xc392d8: r1 = LoadInt32Instr(r0)
    //     0xc392d8: sbfx            x1, x0, #1, #0x1f
    // 0xc392dc: sub             x8, x1, #1
    // 0xc392e0: mov             x0, x8
    // 0xc392e4: mov             x1, x7
    // 0xc392e8: stur            x8, [fp, #-0x48]
    // 0xc392ec: cmp             x1, x0
    // 0xc392f0: b.hs            #0xc394a0
    // 0xc392f4: ArrayLoad: r9 = r6[0]  ; List_4
    //     0xc392f4: ldur            w9, [x6, #0x17]
    // 0xc392f8: DecompressPointer r9
    //     0xc392f8: add             x9, x9, HEAP, lsl #32
    // 0xc392fc: stur            x9, [fp, #-0x40]
    // 0xc39300: LoadField: r0 = r6->field_1b
    //     0xc39300: ldur            w0, [x6, #0x1b]
    // 0xc39304: r6 = LoadInt32Instr(r0)
    //     0xc39304: sbfx            x6, x0, #1, #0x1f
    // 0xc39308: stur            x6, [fp, #-0x38]
    // 0xc3930c: add             x0, x6, x7
    // 0xc39310: LoadField: r1 = r9->field_7
    //     0xc39310: ldur            x1, [x9, #7]
    // 0xc39314: ldrh            w7, [x1, x0]
    // 0xc39318: mov             x0, x7
    // 0xc3931c: ubfx            x0, x0, #0, #0x20
    // 0xc39320: and             x1, x0, x4
    // 0xc39324: ubfx            x1, x1, #0, #0x20
    // 0xc39328: asr             x0, x1, #8
    // 0xc3932c: ubfx            x7, x7, #0, #0x20
    // 0xc39330: and             x1, x7, x3
    // 0xc39334: ubfx            x1, x1, #0, #0x20
    // 0xc39338: lsl             x7, x1, #8
    // 0xc3933c: orr             x10, x0, x7
    // 0xc39340: stur            x10, [fp, #-0x30]
    // 0xc39344: add             x7, x5, #6
    // 0xc39348: mov             x0, x8
    // 0xc3934c: mov             x1, x7
    // 0xc39350: cmp             x1, x0
    // 0xc39354: b.hs            #0xc394a4
    // 0xc39358: add             x0, x6, x7
    // 0xc3935c: LoadField: r1 = r9->field_7
    //     0xc3935c: ldur            x1, [x9, #7]
    // 0xc39360: ldrh            w7, [x1, x0]
    // 0xc39364: mov             x0, x7
    // 0xc39368: ubfx            x0, x0, #0, #0x20
    // 0xc3936c: and             x1, x0, x4
    // 0xc39370: ubfx            x1, x1, #0, #0x20
    // 0xc39374: asr             x0, x1, #8
    // 0xc39378: ubfx            x7, x7, #0, #0x20
    // 0xc3937c: and             x1, x7, x3
    // 0xc39380: ubfx            x1, x1, #0, #0x20
    // 0xc39384: lsl             x7, x1, #8
    // 0xc39388: orr             x11, x0, x7
    // 0xc3938c: stur            x11, [fp, #-0x28]
    // 0xc39390: LoadField: r7 = r2->field_13
    //     0xc39390: ldur            w7, [x2, #0x13]
    // 0xc39394: DecompressPointer r7
    //     0xc39394: add             x7, x7, HEAP, lsl #32
    // 0xc39398: stur            x7, [fp, #-0x20]
    // 0xc3939c: r12 = 0
    //     0xc3939c: movz            x12, #0
    // 0xc393a0: stur            x12, [fp, #-0x18]
    // 0xc393a4: CheckStackOverflow
    //     0xc393a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc393a8: cmp             SP, x16
    //     0xc393ac: b.ls            #0xc394a8
    // 0xc393b0: cmp             x12, x11
    // 0xc393b4: b.ge            #0xc39488
    // 0xc393b8: add             x2, x10, x12
    // 0xc393bc: lsl             x0, x12, #1
    // 0xc393c0: add             x1, x5, x0
    // 0xc393c4: add             x13, x1, #8
    // 0xc393c8: mov             x0, x8
    // 0xc393cc: mov             x1, x13
    // 0xc393d0: cmp             x1, x0
    // 0xc393d4: b.hs            #0xc394b0
    // 0xc393d8: add             x0, x6, x13
    // 0xc393dc: LoadField: r1 = r9->field_7
    //     0xc393dc: ldur            x1, [x9, #7]
    // 0xc393e0: ldrh            w13, [x1, x0]
    // 0xc393e4: mov             x0, x13
    // 0xc393e8: ubfx            x0, x0, #0, #0x20
    // 0xc393ec: and             x1, x0, x4
    // 0xc393f0: ubfx            x1, x1, #0, #0x20
    // 0xc393f4: asr             x0, x1, #8
    // 0xc393f8: ubfx            x13, x13, #0, #0x20
    // 0xc393fc: and             x1, x13, x3
    // 0xc39400: ubfx            x1, x1, #0, #0x20
    // 0xc39404: lsl             x13, x1, #8
    // 0xc39408: orr             x14, x0, x13
    // 0xc3940c: stur            x14, [fp, #-0x10]
    // 0xc39410: cmp             x14, #0
    // 0xc39414: b.le            #0xc39458
    // 0xc39418: r0 = BoxInt64Instr(r2)
    //     0xc39418: sbfiz           x0, x2, #1, #0x1f
    //     0xc3941c: cmp             x2, x0, asr #1
    //     0xc39420: b.eq            #0xc3942c
    //     0xc39424: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc39428: stur            x2, [x0, #7]
    // 0xc3942c: mov             x1, x7
    // 0xc39430: mov             x2, x0
    // 0xc39434: stur            x0, [fp, #-8]
    // 0xc39438: r0 = _hashCode()
    //     0xc39438: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc3943c: mov             x1, x0
    // 0xc39440: ldur            x0, [fp, #-0x10]
    // 0xc39444: lsl             x3, x0, #1
    // 0xc39448: mov             x5, x1
    // 0xc3944c: ldur            x1, [fp, #-0x20]
    // 0xc39450: ldur            x2, [fp, #-8]
    // 0xc39454: r0 = _set()
    //     0xc39454: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc39458: ldur            x1, [fp, #-0x18]
    // 0xc3945c: add             x12, x1, #1
    // 0xc39460: ldur            x5, [fp, #-0x50]
    // 0xc39464: ldur            x7, [fp, #-0x20]
    // 0xc39468: ldur            x8, [fp, #-0x48]
    // 0xc3946c: ldur            x9, [fp, #-0x40]
    // 0xc39470: ldur            x10, [fp, #-0x30]
    // 0xc39474: ldur            x11, [fp, #-0x28]
    // 0xc39478: ldur            x6, [fp, #-0x38]
    // 0xc3947c: r4 = 65280
    //     0xc3947c: orr             x4, xzr, #0xff00
    // 0xc39480: r3 = 255
    //     0xc39480: movz            x3, #0xff
    // 0xc39484: b               #0xc393a0
    // 0xc39488: r0 = Null
    //     0xc39488: mov             x0, NULL
    // 0xc3948c: LeaveFrame
    //     0xc3948c: mov             SP, fp
    //     0xc39490: ldp             fp, lr, [SP], #0x10
    // 0xc39494: ret
    //     0xc39494: ret             
    // 0xc39498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3949c: b               #0xc392c8
    // 0xc394a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc394a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc394a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc394a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc394a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc394a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc394ac: b               #0xc393b0
    // 0xc394b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc394b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _parseCMapFormat4(/* No info */) {
    // ** addr: 0xc394b4, size: 0x904
    // 0xc394b4: EnterFrame
    //     0xc394b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc394b8: mov             fp, SP
    // 0xc394bc: AllocStack(0xa8)
    //     0xc394bc: sub             SP, SP, #0xa8
    // 0xc394c0: r5 = 2
    //     0xc394c0: movz            x5, #0x2
    // 0xc394c4: r4 = 65280
    //     0xc394c4: orr             x4, xzr, #0xff00
    // 0xc394c8: r3 = 255
    //     0xc394c8: movz            x3, #0xff
    // 0xc394cc: mov             x7, x1
    // 0xc394d0: mov             x6, x2
    // 0xc394d4: stur            x1, [fp, #-0x28]
    // 0xc394d8: stur            x2, [fp, #-0x30]
    // 0xc394dc: CheckStackOverflow
    //     0xc394dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc394e0: cmp             SP, x16
    //     0xc394e4: b.ls            #0xc39d54
    // 0xc394e8: LoadField: r2 = r7->field_7
    //     0xc394e8: ldur            w2, [x7, #7]
    // 0xc394ec: DecompressPointer r2
    //     0xc394ec: add             x2, x2, HEAP, lsl #32
    // 0xc394f0: add             x8, x6, #4
    // 0xc394f4: LoadField: r0 = r2->field_13
    //     0xc394f4: ldur            w0, [x2, #0x13]
    // 0xc394f8: r1 = LoadInt32Instr(r0)
    //     0xc394f8: sbfx            x1, x0, #1, #0x1f
    // 0xc394fc: sub             x9, x1, #1
    // 0xc39500: mov             x0, x9
    // 0xc39504: mov             x1, x8
    // 0xc39508: stur            x9, [fp, #-0x20]
    // 0xc3950c: cmp             x1, x0
    // 0xc39510: b.hs            #0xc39d5c
    // 0xc39514: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc39514: ldur            w0, [x2, #0x17]
    // 0xc39518: DecompressPointer r0
    //     0xc39518: add             x0, x0, HEAP, lsl #32
    // 0xc3951c: stur            x0, [fp, #-0x18]
    // 0xc39520: LoadField: r1 = r2->field_1b
    //     0xc39520: ldur            w1, [x2, #0x1b]
    // 0xc39524: r10 = LoadInt32Instr(r1)
    //     0xc39524: sbfx            x10, x1, #1, #0x1f
    // 0xc39528: stur            x10, [fp, #-0x10]
    // 0xc3952c: add             x1, x10, x8
    // 0xc39530: LoadField: r2 = r0->field_7
    //     0xc39530: ldur            x2, [x0, #7]
    // 0xc39534: ldrh            w8, [x2, x1]
    // 0xc39538: mov             x1, x8
    // 0xc3953c: ubfx            x1, x1, #0, #0x20
    // 0xc39540: and             x2, x1, x4
    // 0xc39544: ubfx            x2, x2, #0, #0x20
    // 0xc39548: asr             x1, x2, #8
    // 0xc3954c: ubfx            x8, x8, #0, #0x20
    // 0xc39550: and             x2, x8, x3
    // 0xc39554: ubfx            x2, x2, #0, #0x20
    // 0xc39558: lsl             x8, x2, #8
    // 0xc3955c: orr             x2, x1, x8
    // 0xc39560: sdiv            x8, x2, x5
    // 0xc39564: stur            x8, [fp, #-8]
    // 0xc39568: r1 = <int>
    //     0xc39568: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc3956c: r2 = 0
    //     0xc3956c: movz            x2, #0
    // 0xc39570: r0 = _GrowableList()
    //     0xc39570: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc39574: mov             x2, x0
    // 0xc39578: stur            x2, [fp, #-0x50]
    // 0xc3957c: r9 = 0
    //     0xc3957c: movz            x9, #0
    // 0xc39580: ldur            x5, [fp, #-0x30]
    // 0xc39584: ldur            x8, [fp, #-8]
    // 0xc39588: ldur            x6, [fp, #-0x18]
    // 0xc3958c: ldur            x7, [fp, #-0x10]
    // 0xc39590: r4 = 65280
    //     0xc39590: orr             x4, xzr, #0xff00
    // 0xc39594: r3 = 255
    //     0xc39594: movz            x3, #0xff
    // 0xc39598: stur            x9, [fp, #-0x48]
    // 0xc3959c: CheckStackOverflow
    //     0xc3959c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc395a0: cmp             SP, x16
    //     0xc395a4: b.ls            #0xc39d60
    // 0xc395a8: cmp             x9, x8
    // 0xc395ac: b.ge            #0xc3966c
    // 0xc395b0: lsl             x0, x9, #1
    // 0xc395b4: add             x1, x5, x0
    // 0xc395b8: add             x10, x1, #0xc
    // 0xc395bc: ldur            x0, [fp, #-0x20]
    // 0xc395c0: mov             x1, x10
    // 0xc395c4: cmp             x1, x0
    // 0xc395c8: b.hs            #0xc39d68
    // 0xc395cc: add             x0, x7, x10
    // 0xc395d0: LoadField: r1 = r6->field_7
    //     0xc395d0: ldur            x1, [x6, #7]
    // 0xc395d4: ldrh            w10, [x1, x0]
    // 0xc395d8: mov             x0, x10
    // 0xc395dc: ubfx            x0, x0, #0, #0x20
    // 0xc395e0: and             x1, x0, x4
    // 0xc395e4: ubfx            x1, x1, #0, #0x20
    // 0xc395e8: asr             x0, x1, #8
    // 0xc395ec: ubfx            x10, x10, #0, #0x20
    // 0xc395f0: and             x1, x10, x3
    // 0xc395f4: ubfx            x1, x1, #0, #0x20
    // 0xc395f8: lsl             x10, x1, #8
    // 0xc395fc: orr             x11, x0, x10
    // 0xc39600: stur            x11, [fp, #-0x40]
    // 0xc39604: LoadField: r0 = r2->field_b
    //     0xc39604: ldur            w0, [x2, #0xb]
    // 0xc39608: LoadField: r1 = r2->field_f
    //     0xc39608: ldur            w1, [x2, #0xf]
    // 0xc3960c: DecompressPointer r1
    //     0xc3960c: add             x1, x1, HEAP, lsl #32
    // 0xc39610: LoadField: r10 = r1->field_b
    //     0xc39610: ldur            w10, [x1, #0xb]
    // 0xc39614: r12 = LoadInt32Instr(r0)
    //     0xc39614: sbfx            x12, x0, #1, #0x1f
    // 0xc39618: stur            x12, [fp, #-0x38]
    // 0xc3961c: r0 = LoadInt32Instr(r10)
    //     0xc3961c: sbfx            x0, x10, #1, #0x1f
    // 0xc39620: cmp             x12, x0
    // 0xc39624: b.ne            #0xc39630
    // 0xc39628: mov             x1, x2
    // 0xc3962c: r0 = _growToNextCapacity()
    //     0xc3962c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc39630: ldur            x0, [fp, #-0x50]
    // 0xc39634: ldur            x1, [fp, #-0x48]
    // 0xc39638: ldur            x2, [fp, #-0x40]
    // 0xc3963c: ldur            x3, [fp, #-0x38]
    // 0xc39640: add             x4, x3, #1
    // 0xc39644: lsl             x5, x4, #1
    // 0xc39648: StoreField: r0->field_b = r5
    //     0xc39648: stur            w5, [x0, #0xb]
    // 0xc3964c: LoadField: r4 = r0->field_f
    //     0xc3964c: ldur            w4, [x0, #0xf]
    // 0xc39650: DecompressPointer r4
    //     0xc39650: add             x4, x4, HEAP, lsl #32
    // 0xc39654: lsl             x5, x2, #1
    // 0xc39658: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0xc39658: add             x2, x4, x3, lsl #2
    //     0xc3965c: stur            w5, [x2, #0xf]
    // 0xc39660: add             x9, x1, #1
    // 0xc39664: mov             x2, x0
    // 0xc39668: b               #0xc39580
    // 0xc3966c: mov             x0, x2
    // 0xc39670: r1 = <int>
    //     0xc39670: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc39674: r2 = 0
    //     0xc39674: movz            x2, #0
    // 0xc39678: r0 = _GrowableList()
    //     0xc39678: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc3967c: mov             x2, x0
    // 0xc39680: stur            x2, [fp, #-0x58]
    // 0xc39684: r9 = 0
    //     0xc39684: movz            x9, #0
    // 0xc39688: ldur            x5, [fp, #-0x30]
    // 0xc3968c: ldur            x8, [fp, #-8]
    // 0xc39690: ldur            x6, [fp, #-0x18]
    // 0xc39694: ldur            x7, [fp, #-0x10]
    // 0xc39698: r4 = 65280
    //     0xc39698: orr             x4, xzr, #0xff00
    // 0xc3969c: r3 = 255
    //     0xc3969c: movz            x3, #0xff
    // 0xc396a0: stur            x9, [fp, #-0x48]
    // 0xc396a4: CheckStackOverflow
    //     0xc396a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc396a8: cmp             SP, x16
    //     0xc396ac: b.ls            #0xc39d6c
    // 0xc396b0: cmp             x9, x8
    // 0xc396b4: b.ge            #0xc39778
    // 0xc396b8: add             x0, x8, x9
    // 0xc396bc: lsl             x1, x0, #1
    // 0xc396c0: add             x0, x5, x1
    // 0xc396c4: add             x10, x0, #0xe
    // 0xc396c8: ldur            x0, [fp, #-0x20]
    // 0xc396cc: mov             x1, x10
    // 0xc396d0: cmp             x1, x0
    // 0xc396d4: b.hs            #0xc39d74
    // 0xc396d8: add             x0, x7, x10
    // 0xc396dc: LoadField: r1 = r6->field_7
    //     0xc396dc: ldur            x1, [x6, #7]
    // 0xc396e0: ldrh            w10, [x1, x0]
    // 0xc396e4: mov             x0, x10
    // 0xc396e8: ubfx            x0, x0, #0, #0x20
    // 0xc396ec: and             x1, x0, x4
    // 0xc396f0: ubfx            x1, x1, #0, #0x20
    // 0xc396f4: asr             x0, x1, #8
    // 0xc396f8: ubfx            x10, x10, #0, #0x20
    // 0xc396fc: and             x1, x10, x3
    // 0xc39700: ubfx            x1, x1, #0, #0x20
    // 0xc39704: lsl             x10, x1, #8
    // 0xc39708: orr             x11, x0, x10
    // 0xc3970c: stur            x11, [fp, #-0x40]
    // 0xc39710: LoadField: r0 = r2->field_b
    //     0xc39710: ldur            w0, [x2, #0xb]
    // 0xc39714: LoadField: r1 = r2->field_f
    //     0xc39714: ldur            w1, [x2, #0xf]
    // 0xc39718: DecompressPointer r1
    //     0xc39718: add             x1, x1, HEAP, lsl #32
    // 0xc3971c: LoadField: r10 = r1->field_b
    //     0xc3971c: ldur            w10, [x1, #0xb]
    // 0xc39720: r12 = LoadInt32Instr(r0)
    //     0xc39720: sbfx            x12, x0, #1, #0x1f
    // 0xc39724: stur            x12, [fp, #-0x38]
    // 0xc39728: r0 = LoadInt32Instr(r10)
    //     0xc39728: sbfx            x0, x10, #1, #0x1f
    // 0xc3972c: cmp             x12, x0
    // 0xc39730: b.ne            #0xc3973c
    // 0xc39734: mov             x1, x2
    // 0xc39738: r0 = _growToNextCapacity()
    //     0xc39738: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc3973c: ldur            x0, [fp, #-0x58]
    // 0xc39740: ldur            x1, [fp, #-0x48]
    // 0xc39744: ldur            x2, [fp, #-0x40]
    // 0xc39748: ldur            x3, [fp, #-0x38]
    // 0xc3974c: add             x4, x3, #1
    // 0xc39750: lsl             x5, x4, #1
    // 0xc39754: StoreField: r0->field_b = r5
    //     0xc39754: stur            w5, [x0, #0xb]
    // 0xc39758: LoadField: r4 = r0->field_f
    //     0xc39758: ldur            w4, [x0, #0xf]
    // 0xc3975c: DecompressPointer r4
    //     0xc3975c: add             x4, x4, HEAP, lsl #32
    // 0xc39760: lsl             x5, x2, #1
    // 0xc39764: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0xc39764: add             x2, x4, x3, lsl #2
    //     0xc39768: stur            w5, [x2, #0xf]
    // 0xc3976c: add             x9, x1, #1
    // 0xc39770: mov             x2, x0
    // 0xc39774: b               #0xc39688
    // 0xc39778: mov             x3, x8
    // 0xc3977c: mov             x0, x2
    // 0xc39780: r1 = <int>
    //     0xc39780: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc39784: r2 = 0
    //     0xc39784: movz            x2, #0
    // 0xc39788: r0 = _GrowableList()
    //     0xc39788: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc3978c: mov             x3, x0
    // 0xc39790: ldur            x2, [fp, #-8]
    // 0xc39794: stur            x3, [fp, #-0x68]
    // 0xc39798: lsl             x4, x2, #1
    // 0xc3979c: stur            x4, [fp, #-0x60]
    // 0xc397a0: r10 = 0
    //     0xc397a0: movz            x10, #0
    // 0xc397a4: ldur            x7, [fp, #-0x30]
    // 0xc397a8: ldur            x8, [fp, #-0x18]
    // 0xc397ac: ldur            x9, [fp, #-0x10]
    // 0xc397b0: r6 = 65280
    //     0xc397b0: orr             x6, xzr, #0xff00
    // 0xc397b4: r5 = 255
    //     0xc397b4: movz            x5, #0xff
    // 0xc397b8: stur            x10, [fp, #-0x48]
    // 0xc397bc: CheckStackOverflow
    //     0xc397bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc397c0: cmp             SP, x16
    //     0xc397c4: b.ls            #0xc39d78
    // 0xc397c8: cmp             x10, x2
    // 0xc397cc: b.ge            #0xc39898
    // 0xc397d0: add             x0, x4, x10
    // 0xc397d4: lsl             x1, x0, #1
    // 0xc397d8: add             x0, x7, x1
    // 0xc397dc: add             x11, x0, #0xe
    // 0xc397e0: ldur            x0, [fp, #-0x20]
    // 0xc397e4: mov             x1, x11
    // 0xc397e8: cmp             x1, x0
    // 0xc397ec: b.hs            #0xc39d80
    // 0xc397f0: add             x0, x9, x11
    // 0xc397f4: LoadField: r1 = r8->field_7
    //     0xc397f4: ldur            x1, [x8, #7]
    // 0xc397f8: ldrh            w11, [x1, x0]
    // 0xc397fc: mov             x0, x11
    // 0xc39800: ubfx            x0, x0, #0, #0x20
    // 0xc39804: and             x1, x0, x6
    // 0xc39808: ubfx            x1, x1, #0, #0x20
    // 0xc3980c: asr             x0, x1, #8
    // 0xc39810: ubfx            x11, x11, #0, #0x20
    // 0xc39814: and             x1, x11, x5
    // 0xc39818: ubfx            x1, x1, #0, #0x20
    // 0xc3981c: lsl             x11, x1, #8
    // 0xc39820: orr             x12, x0, x11
    // 0xc39824: stur            x12, [fp, #-0x40]
    // 0xc39828: LoadField: r0 = r3->field_b
    //     0xc39828: ldur            w0, [x3, #0xb]
    // 0xc3982c: LoadField: r1 = r3->field_f
    //     0xc3982c: ldur            w1, [x3, #0xf]
    // 0xc39830: DecompressPointer r1
    //     0xc39830: add             x1, x1, HEAP, lsl #32
    // 0xc39834: LoadField: r11 = r1->field_b
    //     0xc39834: ldur            w11, [x1, #0xb]
    // 0xc39838: r13 = LoadInt32Instr(r0)
    //     0xc39838: sbfx            x13, x0, #1, #0x1f
    // 0xc3983c: stur            x13, [fp, #-0x38]
    // 0xc39840: r0 = LoadInt32Instr(r11)
    //     0xc39840: sbfx            x0, x11, #1, #0x1f
    // 0xc39844: cmp             x13, x0
    // 0xc39848: b.ne            #0xc39854
    // 0xc3984c: mov             x1, x3
    // 0xc39850: r0 = _growToNextCapacity()
    //     0xc39850: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc39854: ldur            x0, [fp, #-0x68]
    // 0xc39858: ldur            x1, [fp, #-0x48]
    // 0xc3985c: ldur            x2, [fp, #-0x40]
    // 0xc39860: ldur            x3, [fp, #-0x38]
    // 0xc39864: add             x4, x3, #1
    // 0xc39868: lsl             x5, x4, #1
    // 0xc3986c: StoreField: r0->field_b = r5
    //     0xc3986c: stur            w5, [x0, #0xb]
    // 0xc39870: LoadField: r4 = r0->field_f
    //     0xc39870: ldur            w4, [x0, #0xf]
    // 0xc39874: DecompressPointer r4
    //     0xc39874: add             x4, x4, HEAP, lsl #32
    // 0xc39878: lsl             x5, x2, #1
    // 0xc3987c: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0xc3987c: add             x2, x4, x3, lsl #2
    //     0xc39880: stur            w5, [x2, #0xf]
    // 0xc39884: add             x10, x1, #1
    // 0xc39888: ldur            x2, [fp, #-8]
    // 0xc3988c: mov             x3, x0
    // 0xc39890: ldur            x4, [fp, #-0x60]
    // 0xc39894: b               #0xc397a4
    // 0xc39898: mov             x1, x7
    // 0xc3989c: mov             x0, x3
    // 0xc398a0: mov             x3, x2
    // 0xc398a4: r16 = 6
    //     0xc398a4: movz            x16, #0x6
    // 0xc398a8: mul             x2, x3, x16
    // 0xc398ac: add             x4, x1, x2
    // 0xc398b0: add             x5, x4, #0xe
    // 0xc398b4: stur            x5, [fp, #-0x30]
    // 0xc398b8: r1 = <int>
    //     0xc398b8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc398bc: r2 = 0
    //     0xc398bc: movz            x2, #0
    // 0xc398c0: r0 = _GrowableList()
    //     0xc398c0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc398c4: mov             x2, x0
    // 0xc398c8: stur            x2, [fp, #-0x70]
    // 0xc398cc: r9 = 0
    //     0xc398cc: movz            x9, #0
    // 0xc398d0: ldur            x3, [fp, #-8]
    // 0xc398d4: ldur            x4, [fp, #-0x30]
    // 0xc398d8: ldur            x7, [fp, #-0x18]
    // 0xc398dc: ldur            x8, [fp, #-0x10]
    // 0xc398e0: r6 = 65280
    //     0xc398e0: orr             x6, xzr, #0xff00
    // 0xc398e4: r5 = 255
    //     0xc398e4: movz            x5, #0xff
    // 0xc398e8: stur            x9, [fp, #-0x48]
    // 0xc398ec: CheckStackOverflow
    //     0xc398ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc398f0: cmp             SP, x16
    //     0xc398f4: b.ls            #0xc39d84
    // 0xc398f8: cmp             x9, x3
    // 0xc398fc: b.ge            #0xc399b8
    // 0xc39900: lsl             x0, x9, #1
    // 0xc39904: add             x10, x4, x0
    // 0xc39908: ldur            x0, [fp, #-0x20]
    // 0xc3990c: mov             x1, x10
    // 0xc39910: cmp             x1, x0
    // 0xc39914: b.hs            #0xc39d8c
    // 0xc39918: add             x0, x8, x10
    // 0xc3991c: LoadField: r1 = r7->field_7
    //     0xc3991c: ldur            x1, [x7, #7]
    // 0xc39920: ldrh            w10, [x1, x0]
    // 0xc39924: mov             x0, x10
    // 0xc39928: ubfx            x0, x0, #0, #0x20
    // 0xc3992c: and             x1, x0, x6
    // 0xc39930: ubfx            x1, x1, #0, #0x20
    // 0xc39934: asr             x0, x1, #8
    // 0xc39938: ubfx            x10, x10, #0, #0x20
    // 0xc3993c: and             x1, x10, x5
    // 0xc39940: ubfx            x1, x1, #0, #0x20
    // 0xc39944: lsl             x10, x1, #8
    // 0xc39948: orr             x11, x0, x10
    // 0xc3994c: stur            x11, [fp, #-0x40]
    // 0xc39950: LoadField: r0 = r2->field_b
    //     0xc39950: ldur            w0, [x2, #0xb]
    // 0xc39954: LoadField: r1 = r2->field_f
    //     0xc39954: ldur            w1, [x2, #0xf]
    // 0xc39958: DecompressPointer r1
    //     0xc39958: add             x1, x1, HEAP, lsl #32
    // 0xc3995c: LoadField: r10 = r1->field_b
    //     0xc3995c: ldur            w10, [x1, #0xb]
    // 0xc39960: r12 = LoadInt32Instr(r0)
    //     0xc39960: sbfx            x12, x0, #1, #0x1f
    // 0xc39964: stur            x12, [fp, #-0x38]
    // 0xc39968: r0 = LoadInt32Instr(r10)
    //     0xc39968: sbfx            x0, x10, #1, #0x1f
    // 0xc3996c: cmp             x12, x0
    // 0xc39970: b.ne            #0xc3997c
    // 0xc39974: mov             x1, x2
    // 0xc39978: r0 = _growToNextCapacity()
    //     0xc39978: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc3997c: ldur            x3, [fp, #-0x70]
    // 0xc39980: ldur            x0, [fp, #-0x48]
    // 0xc39984: ldur            x1, [fp, #-0x40]
    // 0xc39988: ldur            x2, [fp, #-0x38]
    // 0xc3998c: add             x4, x2, #1
    // 0xc39990: lsl             x5, x4, #1
    // 0xc39994: StoreField: r3->field_b = r5
    //     0xc39994: stur            w5, [x3, #0xb]
    // 0xc39998: LoadField: r4 = r3->field_f
    //     0xc39998: ldur            w4, [x3, #0xf]
    // 0xc3999c: DecompressPointer r4
    //     0xc3999c: add             x4, x4, HEAP, lsl #32
    // 0xc399a0: lsl             x5, x1, #1
    // 0xc399a4: ArrayStore: r4[r2] = r5  ; Unknown_4
    //     0xc399a4: add             x1, x4, x2, lsl #2
    //     0xc399a8: stur            w5, [x1, #0xf]
    // 0xc399ac: add             x9, x0, #1
    // 0xc399b0: mov             x2, x3
    // 0xc399b4: b               #0xc398d0
    // 0xc399b8: ldur            x1, [fp, #-0x28]
    // 0xc399bc: mov             x0, x3
    // 0xc399c0: mov             x3, x2
    // 0xc399c4: r4 = _ConstMap len:35
    //     0xc399c4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc399c8: ldr             x4, [x4, #0x8a0]
    // 0xc399cc: sub             x5, x0, #1
    // 0xc399d0: stur            x5, [fp, #-0x38]
    // 0xc399d4: LoadField: r6 = r1->field_13
    //     0xc399d4: ldur            w6, [x1, #0x13]
    // 0xc399d8: DecompressPointer r6
    //     0xc399d8: add             x6, x6, HEAP, lsl #32
    // 0xc399dc: stur            x6, [fp, #-0xa0]
    // 0xc399e0: LoadField: r7 = r4->field_f
    //     0xc399e0: ldur            w7, [x4, #0xf]
    // 0xc399e4: DecompressPointer r7
    //     0xc399e4: add             x7, x7, HEAP, lsl #32
    // 0xc399e8: stur            x7, [fp, #-0x98]
    // 0xc399ec: r23 = 0
    //     0xc399ec: movz            x23, #0
    // 0xc399f0: ldur            x19, [fp, #-0x50]
    // 0xc399f4: ldur            x14, [fp, #-0x58]
    // 0xc399f8: ldur            x13, [fp, #-0x68]
    // 0xc399fc: ldur            x8, [fp, #-0x30]
    // 0xc39a00: ldur            x11, [fp, #-0x18]
    // 0xc39a04: ldur            x12, [fp, #-0x10]
    // 0xc39a08: r10 = 65280
    //     0xc39a08: orr             x10, xzr, #0xff00
    // 0xc39a0c: r9 = 255
    //     0xc39a0c: movz            x9, #0xff
    // 0xc39a10: r20 = 65535
    //     0xc39a10: orr             x20, xzr, #0xffff
    // 0xc39a14: stur            x23, [fp, #-0x90]
    // 0xc39a18: CheckStackOverflow
    //     0xc39a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc39a1c: cmp             SP, x16
    //     0xc39a20: b.ls            #0xc39d90
    // 0xc39a24: cmp             x23, x5
    // 0xc39a28: b.ge            #0xc39d44
    // 0xc39a2c: LoadField: r0 = r14->field_b
    //     0xc39a2c: ldur            w0, [x14, #0xb]
    // 0xc39a30: r1 = LoadInt32Instr(r0)
    //     0xc39a30: sbfx            x1, x0, #1, #0x1f
    // 0xc39a34: mov             x0, x1
    // 0xc39a38: mov             x1, x23
    // 0xc39a3c: cmp             x1, x0
    // 0xc39a40: b.hs            #0xc39d98
    // 0xc39a44: LoadField: r0 = r14->field_f
    //     0xc39a44: ldur            w0, [x14, #0xf]
    // 0xc39a48: DecompressPointer r0
    //     0xc39a48: add             x0, x0, HEAP, lsl #32
    // 0xc39a4c: ArrayLoad: r2 = r0[r23]  ; Unknown_4
    //     0xc39a4c: add             x16, x0, x23, lsl #2
    //     0xc39a50: ldur            w2, [x16, #0xf]
    // 0xc39a54: DecompressPointer r2
    //     0xc39a54: add             x2, x2, HEAP, lsl #32
    // 0xc39a58: LoadField: r0 = r19->field_b
    //     0xc39a58: ldur            w0, [x19, #0xb]
    // 0xc39a5c: r1 = LoadInt32Instr(r0)
    //     0xc39a5c: sbfx            x1, x0, #1, #0x1f
    // 0xc39a60: mov             x0, x1
    // 0xc39a64: mov             x1, x23
    // 0xc39a68: cmp             x1, x0
    // 0xc39a6c: b.hs            #0xc39d9c
    // 0xc39a70: LoadField: r0 = r19->field_f
    //     0xc39a70: ldur            w0, [x19, #0xf]
    // 0xc39a74: DecompressPointer r0
    //     0xc39a74: add             x0, x0, HEAP, lsl #32
    // 0xc39a78: ArrayLoad: r24 = r0[r23]  ; Unknown_4
    //     0xc39a78: add             x16, x0, x23, lsl #2
    //     0xc39a7c: ldur            w24, [x16, #0xf]
    // 0xc39a80: DecompressPointer r24
    //     0xc39a80: add             x24, x24, HEAP, lsl #32
    // 0xc39a84: LoadField: r0 = r13->field_b
    //     0xc39a84: ldur            w0, [x13, #0xb]
    // 0xc39a88: r1 = LoadInt32Instr(r0)
    //     0xc39a88: sbfx            x1, x0, #1, #0x1f
    // 0xc39a8c: mov             x0, x1
    // 0xc39a90: mov             x1, x23
    // 0xc39a94: cmp             x1, x0
    // 0xc39a98: b.hs            #0xc39da0
    // 0xc39a9c: LoadField: r0 = r13->field_f
    //     0xc39a9c: ldur            w0, [x13, #0xf]
    // 0xc39aa0: DecompressPointer r0
    //     0xc39aa0: add             x0, x0, HEAP, lsl #32
    // 0xc39aa4: ArrayLoad: r25 = r0[r23]  ; Unknown_4
    //     0xc39aa4: add             x16, x0, x23, lsl #2
    //     0xc39aa8: ldur            w25, [x16, #0xf]
    // 0xc39aac: DecompressPointer r25
    //     0xc39aac: add             x25, x25, HEAP, lsl #32
    // 0xc39ab0: LoadField: r0 = r3->field_b
    //     0xc39ab0: ldur            w0, [x3, #0xb]
    // 0xc39ab4: r1 = LoadInt32Instr(r0)
    //     0xc39ab4: sbfx            x1, x0, #1, #0x1f
    // 0xc39ab8: mov             x0, x1
    // 0xc39abc: mov             x1, x23
    // 0xc39ac0: cmp             x1, x0
    // 0xc39ac4: b.hs            #0xc39da4
    // 0xc39ac8: LoadField: r0 = r3->field_f
    //     0xc39ac8: ldur            w0, [x3, #0xf]
    // 0xc39acc: DecompressPointer r0
    //     0xc39acc: add             x0, x0, HEAP, lsl #32
    // 0xc39ad0: ArrayLoad: r1 = r0[r23]  ; Unknown_4
    //     0xc39ad0: add             x16, x0, x23, lsl #2
    //     0xc39ad4: ldur            w1, [x16, #0xf]
    // 0xc39ad8: DecompressPointer r1
    //     0xc39ad8: add             x1, x1, HEAP, lsl #32
    // 0xc39adc: lsl             x0, x23, #1
    // 0xc39ae0: add             x3, x8, x0
    // 0xc39ae4: stur            x3, [fp, #-0x80]
    // 0xc39ae8: r0 = LoadInt32Instr(r2)
    //     0xc39ae8: sbfx            x0, x2, #1, #0x1f
    //     0xc39aec: tbz             w2, #0, #0xc39af4
    //     0xc39af0: ldur            x0, [x2, #7]
    // 0xc39af4: stur            x0, [fp, #-8]
    // 0xc39af8: r2 = LoadInt32Instr(r24)
    //     0xc39af8: sbfx            x2, x24, #1, #0x1f
    //     0xc39afc: tbz             w24, #0, #0xc39b04
    //     0xc39b00: ldur            x2, [x24, #7]
    // 0xc39b04: stur            x2, [fp, #-0x40]
    // 0xc39b08: r24 = LoadInt32Instr(r1)
    //     0xc39b08: sbfx            x24, x1, #1, #0x1f
    //     0xc39b0c: tbz             w1, #0, #0xc39b14
    //     0xc39b10: ldur            x24, [x1, #7]
    // 0xc39b14: stur            x24, [fp, #-0x88]
    // 0xc39b18: r1 = LoadInt32Instr(r25)
    //     0xc39b18: sbfx            x1, x25, #1, #0x1f
    //     0xc39b1c: tbz             w25, #0, #0xc39b24
    //     0xc39b20: ldur            x1, [x25, #7]
    // 0xc39b24: stur            x1, [fp, #-0x60]
    // 0xc39b28: mov             x25, x0
    // 0xc39b2c: stur            x25, [fp, #-0x78]
    // 0xc39b30: CheckStackOverflow
    //     0xc39b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc39b34: cmp             SP, x16
    //     0xc39b38: b.ls            #0xc39da8
    // 0xc39b3c: cmp             x25, x2
    // 0xc39b40: b.gt            #0xc39d20
    // 0xc39b44: cbnz            x24, #0xc39b68
    // 0xc39b48: mov             x4, x25
    // 0xc39b4c: ubfx            x4, x4, #0, #0x20
    // 0xc39b50: add             w5, w1, w4
    // 0xc39b54: and             x4, x5, x20
    // 0xc39b58: ubfx            x4, x4, #0, #0x20
    // 0xc39b5c: mov             x5, x4
    // 0xc39b60: mov             x4, x1
    // 0xc39b64: b               #0xc39bc4
    // 0xc39b68: sub             x4, x25, x0
    // 0xc39b6c: lsl             x5, x4, #1
    // 0xc39b70: add             x4, x24, x5
    // 0xc39b74: add             x5, x4, x3
    // 0xc39b78: ldur            x0, [fp, #-0x20]
    // 0xc39b7c: mov             x4, x1
    // 0xc39b80: mov             x1, x5
    // 0xc39b84: cmp             x1, x0
    // 0xc39b88: b.hs            #0xc39db0
    // 0xc39b8c: add             x0, x12, x5
    // 0xc39b90: LoadField: r1 = r11->field_7
    //     0xc39b90: ldur            x1, [x11, #7]
    // 0xc39b94: ldrh            w5, [x1, x0]
    // 0xc39b98: mov             x0, x5
    // 0xc39b9c: ubfx            x0, x0, #0, #0x20
    // 0xc39ba0: and             x1, x0, x10
    // 0xc39ba4: ubfx            x1, x1, #0, #0x20
    // 0xc39ba8: asr             x0, x1, #8
    // 0xc39bac: ubfx            x5, x5, #0, #0x20
    // 0xc39bb0: and             x1, x5, x9
    // 0xc39bb4: ubfx            x1, x1, #0, #0x20
    // 0xc39bb8: lsl             x5, x1, #8
    // 0xc39bbc: orr             x1, x0, x5
    // 0xc39bc0: mov             x5, x1
    // 0xc39bc4: stur            x5, [fp, #-0x48]
    // 0xc39bc8: r0 = BoxInt64Instr(r25)
    //     0xc39bc8: sbfiz           x0, x25, #1, #0x1f
    //     0xc39bcc: cmp             x25, x0, asr #1
    //     0xc39bd0: b.eq            #0xc39bdc
    //     0xc39bd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc39bd8: stur            x25, [x0, #7]
    // 0xc39bdc: mov             x1, x6
    // 0xc39be0: stur            x0, [fp, #-0x28]
    // 0xc39be4: mov             x16, x2
    // 0xc39be8: mov             x2, x0
    // 0xc39bec: mov             x0, x16
    // 0xc39bf0: r0 = _hashCode()
    //     0xc39bf0: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc39bf4: mov             x1, x0
    // 0xc39bf8: ldur            x0, [fp, #-0x48]
    // 0xc39bfc: lsl             x4, x0, #1
    // 0xc39c00: mov             x5, x1
    // 0xc39c04: ldur            x1, [fp, #-0xa0]
    // 0xc39c08: ldur            x2, [fp, #-0x28]
    // 0xc39c0c: mov             x3, x4
    // 0xc39c10: stur            x4, [fp, #-0xa8]
    // 0xc39c14: r0 = _set()
    //     0xc39c14: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc39c18: r0 = _ConstMap len:35
    //     0xc39c18: add             x0, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39c1c: ldr             x0, [x0, #0x8a0]
    // 0xc39c20: LoadField: r1 = r0->field_1b
    //     0xc39c20: ldur            w1, [x0, #0x1b]
    // 0xc39c24: DecompressPointer r1
    //     0xc39c24: add             x1, x1, HEAP, lsl #32
    // 0xc39c28: cmp             w1, NULL
    // 0xc39c2c: b.ne            #0xc39c38
    // 0xc39c30: mov             x1, x0
    // 0xc39c34: r0 = _createIndex()
    //     0xc39c34: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xc39c38: ldur            x2, [fp, #-0x28]
    // 0xc39c3c: r1 = _ConstMap len:35
    //     0xc39c3c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39c40: ldr             x1, [x1, #0x8a0]
    // 0xc39c44: r0 = containsKey()
    //     0xc39c44: bl              #0x95b098  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::containsKey
    // 0xc39c48: tbnz            w0, #4, #0xc39cc8
    // 0xc39c4c: r0 = _ConstMap len:35
    //     0xc39c4c: add             x0, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39c50: ldr             x0, [x0, #0x8a0]
    // 0xc39c54: LoadField: r1 = r0->field_1b
    //     0xc39c54: ldur            w1, [x0, #0x1b]
    // 0xc39c58: DecompressPointer r1
    //     0xc39c58: add             x1, x1, HEAP, lsl #32
    // 0xc39c5c: cmp             w1, NULL
    // 0xc39c60: b.ne            #0xc39c6c
    // 0xc39c64: mov             x1, x0
    // 0xc39c68: r0 = _createIndex()
    //     0xc39c68: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xc39c6c: ldur            x0, [fp, #-0x98]
    // 0xc39c70: ldur            x2, [fp, #-0x28]
    // 0xc39c74: r1 = _ConstMap len:35
    //     0xc39c74: add             x1, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39c78: ldr             x1, [x1, #0x8a0]
    // 0xc39c7c: r0 = _getValueOrData()
    //     0xc39c7c: bl              #0xeb9fb0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc39c80: mov             x1, x0
    // 0xc39c84: ldur            x0, [fp, #-0x98]
    // 0xc39c88: cmp             w0, w1
    // 0xc39c8c: b.ne            #0xc39c98
    // 0xc39c90: r3 = Null
    //     0xc39c90: mov             x3, NULL
    // 0xc39c94: b               #0xc39c9c
    // 0xc39c98: mov             x3, x1
    // 0xc39c9c: stur            x3, [fp, #-0x28]
    // 0xc39ca0: cmp             w3, NULL
    // 0xc39ca4: b.eq            #0xc39db4
    // 0xc39ca8: ldur            x1, [fp, #-0xa0]
    // 0xc39cac: mov             x2, x3
    // 0xc39cb0: r0 = _hashCode()
    //     0xc39cb0: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xc39cb4: ldur            x1, [fp, #-0xa0]
    // 0xc39cb8: ldur            x2, [fp, #-0x28]
    // 0xc39cbc: ldur            x3, [fp, #-0xa8]
    // 0xc39cc0: mov             x5, x0
    // 0xc39cc4: r0 = _set()
    //     0xc39cc4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xc39cc8: ldur            x1, [fp, #-0x78]
    // 0xc39ccc: add             x25, x1, #1
    // 0xc39cd0: ldur            x19, [fp, #-0x50]
    // 0xc39cd4: ldur            x14, [fp, #-0x58]
    // 0xc39cd8: ldur            x13, [fp, #-0x68]
    // 0xc39cdc: ldur            x8, [fp, #-0x30]
    // 0xc39ce0: ldur            x23, [fp, #-0x90]
    // 0xc39ce4: ldur            x3, [fp, #-0x80]
    // 0xc39ce8: ldur            x6, [fp, #-0xa0]
    // 0xc39cec: ldur            x11, [fp, #-0x18]
    // 0xc39cf0: ldur            x7, [fp, #-0x98]
    // 0xc39cf4: ldur            x12, [fp, #-0x10]
    // 0xc39cf8: ldur            x0, [fp, #-8]
    // 0xc39cfc: ldur            x2, [fp, #-0x40]
    // 0xc39d00: ldur            x1, [fp, #-0x60]
    // 0xc39d04: ldur            x24, [fp, #-0x88]
    // 0xc39d08: r4 = _ConstMap len:35
    //     0xc39d08: add             x4, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39d0c: ldr             x4, [x4, #0x8a0]
    // 0xc39d10: r10 = 65280
    //     0xc39d10: orr             x10, xzr, #0xff00
    // 0xc39d14: r9 = 255
    //     0xc39d14: movz            x9, #0xff
    // 0xc39d18: r20 = 65535
    //     0xc39d18: orr             x20, xzr, #0xffff
    // 0xc39d1c: b               #0xc39b2c
    // 0xc39d20: mov             x1, x23
    // 0xc39d24: add             x23, x1, #1
    // 0xc39d28: ldur            x3, [fp, #-0x70]
    // 0xc39d2c: ldur            x5, [fp, #-0x38]
    // 0xc39d30: ldur            x6, [fp, #-0xa0]
    // 0xc39d34: ldur            x7, [fp, #-0x98]
    // 0xc39d38: r4 = _ConstMap len:35
    //     0xc39d38: add             x4, PP, #0x33, lsl #12  ; [pp+0x338a0] Map<int, int>(35)
    //     0xc39d3c: ldr             x4, [x4, #0x8a0]
    // 0xc39d40: b               #0xc399f0
    // 0xc39d44: r0 = Null
    //     0xc39d44: mov             x0, NULL
    // 0xc39d48: LeaveFrame
    //     0xc39d48: mov             SP, fp
    //     0xc39d4c: ldp             fp, lr, [SP], #0x10
    // 0xc39d50: ret
    //     0xc39d50: ret             
    // 0xc39d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d58: b               #0xc394e8
    // 0xc39d5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d64: b               #0xc395a8
    // 0xc39d68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d70: b               #0xc396b0
    // 0xc39d74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d7c: b               #0xc397c8
    // 0xc39d80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d88: b               #0xc398f8
    // 0xc39d8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d8c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39d94: b               #0xc39a24
    // 0xc39d98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39d9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39d9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39da0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39da0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39da4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39da4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39dac: b               #0xc39b3c
    // 0xc39db0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc39db0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc39db4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc39db4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 918, size: 0x44, field offset: 0x8
//   const constructor, 
class TtfBitmapInfo extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc34fbc, size: 0x258
    // 0xc34fbc: EnterFrame
    //     0xc34fbc: stp             fp, lr, [SP, #-0x10]!
    //     0xc34fc0: mov             fp, SP
    // 0xc34fc4: AllocStack(0x8)
    //     0xc34fc4: sub             SP, SP, #8
    // 0xc34fc8: CheckStackOverflow
    //     0xc34fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc34fcc: cmp             SP, x16
    //     0xc34fd0: b.ls            #0xc3520c
    // 0xc34fd4: r1 = Null
    //     0xc34fd4: mov             x1, NULL
    // 0xc34fd8: r2 = 28
    //     0xc34fd8: movz            x2, #0x1c
    // 0xc34fdc: r0 = AllocateArray()
    //     0xc34fdc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc34fe0: mov             x2, x0
    // 0xc34fe4: r16 = "Bitmap Glyph "
    //     0xc34fe4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efd0] "Bitmap Glyph "
    //     0xc34fe8: ldr             x16, [x16, #0xfd0]
    // 0xc34fec: StoreField: r2->field_f = r16
    //     0xc34fec: stur            w16, [x2, #0xf]
    // 0xc34ff0: ldr             x3, [fp, #0x10]
    // 0xc34ff4: LoadField: r4 = r3->field_13
    //     0xc34ff4: ldur            x4, [x3, #0x13]
    // 0xc34ff8: r0 = BoxInt64Instr(r4)
    //     0xc34ff8: sbfiz           x0, x4, #1, #0x1f
    //     0xc34ffc: cmp             x4, x0, asr #1
    //     0xc35000: b.eq            #0xc3500c
    //     0xc35004: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35008: stur            x4, [x0, #7]
    // 0xc3500c: mov             x1, x2
    // 0xc35010: ArrayStore: r1[1] = r0  ; List_4
    //     0xc35010: add             x25, x1, #0x13
    //     0xc35014: str             w0, [x25]
    //     0xc35018: tbz             w0, #0, #0xc35034
    //     0xc3501c: ldurb           w16, [x1, #-1]
    //     0xc35020: ldurb           w17, [x0, #-1]
    //     0xc35024: and             x16, x17, x16, lsr #2
    //     0xc35028: tst             x16, HEAP, lsr #32
    //     0xc3502c: b.eq            #0xc35034
    //     0xc35030: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35034: r16 = "x"
    //     0xc35034: ldr             x16, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xc35038: ArrayStore: r2[0] = r16  ; List_4
    //     0xc35038: stur            w16, [x2, #0x17]
    // 0xc3503c: LoadField: r4 = r3->field_b
    //     0xc3503c: ldur            x4, [x3, #0xb]
    // 0xc35040: r0 = BoxInt64Instr(r4)
    //     0xc35040: sbfiz           x0, x4, #1, #0x1f
    //     0xc35044: cmp             x4, x0, asr #1
    //     0xc35048: b.eq            #0xc35054
    //     0xc3504c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35050: stur            x4, [x0, #7]
    // 0xc35054: mov             x1, x2
    // 0xc35058: ArrayStore: r1[3] = r0  ; List_4
    //     0xc35058: add             x25, x1, #0x1b
    //     0xc3505c: str             w0, [x25]
    //     0xc35060: tbz             w0, #0, #0xc3507c
    //     0xc35064: ldurb           w16, [x1, #-1]
    //     0xc35068: ldurb           w17, [x0, #-1]
    //     0xc3506c: and             x16, x17, x16, lsr #2
    //     0xc35070: tst             x16, HEAP, lsr #32
    //     0xc35074: b.eq            #0xc3507c
    //     0xc35078: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3507c: r16 = " horiBearingX:"
    //     0xc3507c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efd8] " horiBearingX:"
    //     0xc35080: ldr             x16, [x16, #0xfd8]
    // 0xc35084: StoreField: r2->field_1f = r16
    //     0xc35084: stur            w16, [x2, #0x1f]
    // 0xc35088: LoadField: r4 = r3->field_1b
    //     0xc35088: ldur            x4, [x3, #0x1b]
    // 0xc3508c: r0 = BoxInt64Instr(r4)
    //     0xc3508c: sbfiz           x0, x4, #1, #0x1f
    //     0xc35090: cmp             x4, x0, asr #1
    //     0xc35094: b.eq            #0xc350a0
    //     0xc35098: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3509c: stur            x4, [x0, #7]
    // 0xc350a0: mov             x1, x2
    // 0xc350a4: ArrayStore: r1[5] = r0  ; List_4
    //     0xc350a4: add             x25, x1, #0x23
    //     0xc350a8: str             w0, [x25]
    //     0xc350ac: tbz             w0, #0, #0xc350c8
    //     0xc350b0: ldurb           w16, [x1, #-1]
    //     0xc350b4: ldurb           w17, [x0, #-1]
    //     0xc350b8: and             x16, x17, x16, lsr #2
    //     0xc350bc: tst             x16, HEAP, lsr #32
    //     0xc350c0: b.eq            #0xc350c8
    //     0xc350c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc350c8: r16 = " horiBearingY:"
    //     0xc350c8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efe0] " horiBearingY:"
    //     0xc350cc: ldr             x16, [x16, #0xfe0]
    // 0xc350d0: StoreField: r2->field_27 = r16
    //     0xc350d0: stur            w16, [x2, #0x27]
    // 0xc350d4: LoadField: r4 = r3->field_23
    //     0xc350d4: ldur            x4, [x3, #0x23]
    // 0xc350d8: r0 = BoxInt64Instr(r4)
    //     0xc350d8: sbfiz           x0, x4, #1, #0x1f
    //     0xc350dc: cmp             x4, x0, asr #1
    //     0xc350e0: b.eq            #0xc350ec
    //     0xc350e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc350e8: stur            x4, [x0, #7]
    // 0xc350ec: mov             x1, x2
    // 0xc350f0: ArrayStore: r1[7] = r0  ; List_4
    //     0xc350f0: add             x25, x1, #0x2b
    //     0xc350f4: str             w0, [x25]
    //     0xc350f8: tbz             w0, #0, #0xc35114
    //     0xc350fc: ldurb           w16, [x1, #-1]
    //     0xc35100: ldurb           w17, [x0, #-1]
    //     0xc35104: and             x16, x17, x16, lsr #2
    //     0xc35108: tst             x16, HEAP, lsr #32
    //     0xc3510c: b.eq            #0xc35114
    //     0xc35110: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35114: r16 = " horiAdvance:"
    //     0xc35114: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efe8] " horiAdvance:"
    //     0xc35118: ldr             x16, [x16, #0xfe8]
    // 0xc3511c: StoreField: r2->field_2f = r16
    //     0xc3511c: stur            w16, [x2, #0x2f]
    // 0xc35120: LoadField: r4 = r3->field_2b
    //     0xc35120: ldur            x4, [x3, #0x2b]
    // 0xc35124: r0 = BoxInt64Instr(r4)
    //     0xc35124: sbfiz           x0, x4, #1, #0x1f
    //     0xc35128: cmp             x4, x0, asr #1
    //     0xc3512c: b.eq            #0xc35138
    //     0xc35130: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35134: stur            x4, [x0, #7]
    // 0xc35138: mov             x1, x2
    // 0xc3513c: ArrayStore: r1[9] = r0  ; List_4
    //     0xc3513c: add             x25, x1, #0x33
    //     0xc35140: str             w0, [x25]
    //     0xc35144: tbz             w0, #0, #0xc35160
    //     0xc35148: ldurb           w16, [x1, #-1]
    //     0xc3514c: ldurb           w17, [x0, #-1]
    //     0xc35150: and             x16, x17, x16, lsr #2
    //     0xc35154: tst             x16, HEAP, lsr #32
    //     0xc35158: b.eq            #0xc35160
    //     0xc3515c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35160: r16 = " ascender:"
    //     0xc35160: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eff0] " ascender:"
    //     0xc35164: ldr             x16, [x16, #0xff0]
    // 0xc35168: StoreField: r2->field_37 = r16
    //     0xc35168: stur            w16, [x2, #0x37]
    // 0xc3516c: LoadField: r4 = r3->field_33
    //     0xc3516c: ldur            x4, [x3, #0x33]
    // 0xc35170: r0 = BoxInt64Instr(r4)
    //     0xc35170: sbfiz           x0, x4, #1, #0x1f
    //     0xc35174: cmp             x4, x0, asr #1
    //     0xc35178: b.eq            #0xc35184
    //     0xc3517c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35180: stur            x4, [x0, #7]
    // 0xc35184: mov             x1, x2
    // 0xc35188: ArrayStore: r1[11] = r0  ; List_4
    //     0xc35188: add             x25, x1, #0x3b
    //     0xc3518c: str             w0, [x25]
    //     0xc35190: tbz             w0, #0, #0xc351ac
    //     0xc35194: ldurb           w16, [x1, #-1]
    //     0xc35198: ldurb           w17, [x0, #-1]
    //     0xc3519c: and             x16, x17, x16, lsr #2
    //     0xc351a0: tst             x16, HEAP, lsr #32
    //     0xc351a4: b.eq            #0xc351ac
    //     0xc351a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc351ac: r16 = " descender:"
    //     0xc351ac: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eff8] " descender:"
    //     0xc351b0: ldr             x16, [x16, #0xff8]
    // 0xc351b4: StoreField: r2->field_3f = r16
    //     0xc351b4: stur            w16, [x2, #0x3f]
    // 0xc351b8: LoadField: r4 = r3->field_3b
    //     0xc351b8: ldur            x4, [x3, #0x3b]
    // 0xc351bc: r0 = BoxInt64Instr(r4)
    //     0xc351bc: sbfiz           x0, x4, #1, #0x1f
    //     0xc351c0: cmp             x4, x0, asr #1
    //     0xc351c4: b.eq            #0xc351d0
    //     0xc351c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc351cc: stur            x4, [x0, #7]
    // 0xc351d0: mov             x1, x2
    // 0xc351d4: ArrayStore: r1[13] = r0  ; List_4
    //     0xc351d4: add             x25, x1, #0x43
    //     0xc351d8: str             w0, [x25]
    //     0xc351dc: tbz             w0, #0, #0xc351f8
    //     0xc351e0: ldurb           w16, [x1, #-1]
    //     0xc351e4: ldurb           w17, [x0, #-1]
    //     0xc351e8: and             x16, x17, x16, lsr #2
    //     0xc351ec: tst             x16, HEAP, lsr #32
    //     0xc351f0: b.eq            #0xc351f8
    //     0xc351f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc351f8: str             x2, [SP]
    // 0xc351fc: r0 = _interpolate()
    //     0xc351fc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35200: LeaveFrame
    //     0xc35200: mov             SP, fp
    //     0xc35204: ldp             fp, lr, [SP], #0x10
    // 0xc35208: ret
    //     0xc35208: ret             
    // 0xc3520c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3520c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35210: b               #0xc34fd4
  }
  get _ metrics(/* No info */) {
    // ** addr: 0xe92f7c, size: 0xac
    // 0xe92f7c: EnterFrame
    //     0xe92f7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe92f80: mov             fp, SP
    // 0xe92f84: AllocStack(0x28)
    //     0xe92f84: sub             SP, SP, #0x28
    // 0xe92f88: d0 = 1.000000
    //     0xe92f88: fmov            d0, #1.00000000
    // 0xe92f8c: LoadField: r0 = r1->field_b
    //     0xe92f8c: ldur            x0, [x1, #0xb]
    // 0xe92f90: scvtf           d1, x0
    // 0xe92f94: fdiv            d2, d0, d1
    // 0xe92f98: LoadField: r0 = r1->field_23
    //     0xe92f98: ldur            x0, [x1, #0x23]
    // 0xe92f9c: scvtf           d0, x0
    // 0xe92fa0: fmul            d3, d0, d2
    // 0xe92fa4: stur            d3, [fp, #-0x28]
    // 0xe92fa8: LoadField: r0 = r1->field_1b
    //     0xe92fa8: ldur            x0, [x1, #0x1b]
    // 0xe92fac: scvtf           d0, x0
    // 0xe92fb0: fmul            d4, d0, d2
    // 0xe92fb4: stur            d4, [fp, #-0x20]
    // 0xe92fb8: fmul            d0, d1, d2
    // 0xe92fbc: fsub            d1, d3, d0
    // 0xe92fc0: stur            d1, [fp, #-0x18]
    // 0xe92fc4: LoadField: r0 = r1->field_2b
    //     0xe92fc4: ldur            x0, [x1, #0x2b]
    // 0xe92fc8: scvtf           d0, x0
    // 0xe92fcc: fmul            d5, d0, d2
    // 0xe92fd0: stur            d5, [fp, #-0x10]
    // 0xe92fd4: LoadField: r0 = r1->field_33
    //     0xe92fd4: ldur            x0, [x1, #0x33]
    // 0xe92fd8: scvtf           d0, x0
    // 0xe92fdc: fmul            d6, d0, d2
    // 0xe92fe0: stur            d6, [fp, #-8]
    // 0xe92fe4: r0 = PdfFontMetrics()
    //     0xe92fe4: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0xe92fe8: ldur            d0, [fp, #-0x20]
    // 0xe92fec: StoreField: r0->field_7 = d0
    //     0xe92fec: stur            d0, [x0, #7]
    // 0xe92ff0: ldur            d1, [fp, #-0x18]
    // 0xe92ff4: StoreField: r0->field_f = d1
    //     0xe92ff4: stur            d1, [x0, #0xf]
    // 0xe92ff8: ldur            d1, [fp, #-0x10]
    // 0xe92ffc: StoreField: r0->field_1f = d1
    //     0xe92ffc: stur            d1, [x0, #0x1f]
    // 0xe93000: ldur            d2, [fp, #-0x28]
    // 0xe93004: ArrayStore: r0[0] = d2  ; List_8
    //     0xe93004: stur            d2, [x0, #0x17]
    // 0xe93008: ldur            d3, [fp, #-8]
    // 0xe9300c: StoreField: r0->field_27 = d3
    //     0xe9300c: stur            d3, [x0, #0x27]
    // 0xe93010: StoreField: r0->field_2f = d2
    //     0xe93010: stur            d2, [x0, #0x2f]
    // 0xe93014: StoreField: r0->field_37 = d1
    //     0xe93014: stur            d1, [x0, #0x37]
    // 0xe93018: StoreField: r0->field_3f = d0
    //     0xe93018: stur            d0, [x0, #0x3f]
    // 0xe9301c: LeaveFrame
    //     0xe9301c: mov             SP, fp
    //     0xe93020: ldp             fp, lr, [SP], #0x10
    // 0xe93024: ret
    //     0xe93024: ret             
  }
}

// class id: 919, size: 0x18, field offset: 0x8
//   const constructor, 
class TtfGlyphInfo extends Object {

  _ copy(/* No info */) {
    // ** addr: 0x7c8548, size: 0x8c
    // 0x7c8548: EnterFrame
    //     0x7c8548: stp             fp, lr, [SP, #-0x10]!
    //     0x7c854c: mov             fp, SP
    // 0x7c8550: AllocStack(0x18)
    //     0x7c8550: sub             SP, SP, #0x18
    // 0x7c8554: SetupParameters(TtfGlyphInfo this /* r1 => r0, fp-0x10 */)
    //     0x7c8554: mov             x0, x1
    //     0x7c8558: stur            x1, [fp, #-0x10]
    // 0x7c855c: CheckStackOverflow
    //     0x7c855c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8560: cmp             SP, x16
    //     0x7c8564: b.ls            #0x7c85cc
    // 0x7c8568: LoadField: r3 = r0->field_7
    //     0x7c8568: ldur            x3, [x0, #7]
    // 0x7c856c: stur            x3, [fp, #-8]
    // 0x7c8570: LoadField: r2 = r0->field_f
    //     0x7c8570: ldur            w2, [x0, #0xf]
    // 0x7c8574: DecompressPointer r2
    //     0x7c8574: add             x2, x2, HEAP, lsl #32
    // 0x7c8578: r1 = Null
    //     0x7c8578: mov             x1, NULL
    // 0x7c857c: r0 = Uint8List.fromList()
    //     0x7c857c: bl              #0x6b9248  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x7c8580: mov             x3, x0
    // 0x7c8584: ldur            x0, [fp, #-0x10]
    // 0x7c8588: stur            x3, [fp, #-0x18]
    // 0x7c858c: LoadField: r2 = r0->field_13
    //     0x7c858c: ldur            w2, [x0, #0x13]
    // 0x7c8590: DecompressPointer r2
    //     0x7c8590: add             x2, x2, HEAP, lsl #32
    // 0x7c8594: r1 = <int>
    //     0x7c8594: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7c8598: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7c8598: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7c859c: r0 = List.from()
    //     0x7c859c: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0x7c85a0: stur            x0, [fp, #-0x10]
    // 0x7c85a4: r0 = TtfGlyphInfo()
    //     0x7c85a4: bl              #0x7bcae4  ; AllocateTtfGlyphInfoStub -> TtfGlyphInfo (size=0x18)
    // 0x7c85a8: ldur            x1, [fp, #-8]
    // 0x7c85ac: StoreField: r0->field_7 = r1
    //     0x7c85ac: stur            x1, [x0, #7]
    // 0x7c85b0: ldur            x1, [fp, #-0x18]
    // 0x7c85b4: StoreField: r0->field_f = r1
    //     0x7c85b4: stur            w1, [x0, #0xf]
    // 0x7c85b8: ldur            x1, [fp, #-0x10]
    // 0x7c85bc: StoreField: r0->field_13 = r1
    //     0x7c85bc: stur            w1, [x0, #0x13]
    // 0x7c85c0: LeaveFrame
    //     0x7c85c0: mov             SP, fp
    //     0x7c85c4: ldp             fp, lr, [SP], #0x10
    // 0x7c85c8: ret
    //     0x7c85c8: ret             
    // 0x7c85cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c85cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c85d0: b               #0x7c8568
  }
  _ toString(/* No info */) {
    // ** addr: 0xc34f38, size: 0x84
    // 0xc34f38: EnterFrame
    //     0xc34f38: stp             fp, lr, [SP, #-0x10]!
    //     0xc34f3c: mov             fp, SP
    // 0xc34f40: AllocStack(0x8)
    //     0xc34f40: sub             SP, SP, #8
    // 0xc34f44: CheckStackOverflow
    //     0xc34f44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc34f48: cmp             SP, x16
    //     0xc34f4c: b.ls            #0xc34fb4
    // 0xc34f50: r1 = Null
    //     0xc34f50: mov             x1, NULL
    // 0xc34f54: r2 = 8
    //     0xc34f54: movz            x2, #0x8
    // 0xc34f58: r0 = AllocateArray()
    //     0xc34f58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc34f5c: mov             x2, x0
    // 0xc34f60: r16 = "Glyph "
    //     0xc34f60: add             x16, PP, #0x51, lsl #12  ; [pp+0x51378] "Glyph "
    //     0xc34f64: ldr             x16, [x16, #0x378]
    // 0xc34f68: StoreField: r2->field_f = r16
    //     0xc34f68: stur            w16, [x2, #0xf]
    // 0xc34f6c: ldr             x3, [fp, #0x10]
    // 0xc34f70: LoadField: r4 = r3->field_7
    //     0xc34f70: ldur            x4, [x3, #7]
    // 0xc34f74: r0 = BoxInt64Instr(r4)
    //     0xc34f74: sbfiz           x0, x4, #1, #0x1f
    //     0xc34f78: cmp             x4, x0, asr #1
    //     0xc34f7c: b.eq            #0xc34f88
    //     0xc34f80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc34f84: stur            x4, [x0, #7]
    // 0xc34f88: StoreField: r2->field_13 = r0
    //     0xc34f88: stur            w0, [x2, #0x13]
    // 0xc34f8c: r16 = " "
    //     0xc34f8c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc34f90: ArrayStore: r2[0] = r16  ; List_4
    //     0xc34f90: stur            w16, [x2, #0x17]
    // 0xc34f94: LoadField: r0 = r3->field_13
    //     0xc34f94: ldur            w0, [x3, #0x13]
    // 0xc34f98: DecompressPointer r0
    //     0xc34f98: add             x0, x0, HEAP, lsl #32
    // 0xc34f9c: StoreField: r2->field_1b = r0
    //     0xc34f9c: stur            w0, [x2, #0x1b]
    // 0xc34fa0: str             x2, [SP]
    // 0xc34fa4: r0 = _interpolate()
    //     0xc34fa4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc34fa8: LeaveFrame
    //     0xc34fa8: mov             SP, fp
    //     0xc34fac: ldp             fp, lr, [SP], #0x10
    // 0xc34fb0: ret
    //     0xc34fb0: ret             
    // 0xc34fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc34fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc34fb8: b               #0xc34f50
  }
}
