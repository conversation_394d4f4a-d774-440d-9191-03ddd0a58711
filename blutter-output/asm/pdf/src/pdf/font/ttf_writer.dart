// lib: , url: package:pdf/src/pdf/font/ttf_writer.dart

// class id: 1050778, size: 0x8
class :: {
}

// class id: 916, size: 0xc, field offset: 0x8
class TtfWriter extends Object {

  _ withChars(/* No info */) {
    // ** addr: 0x7b91c0, size: 0x2bc8
    // 0x7b91c0: EnterFrame
    //     0x7b91c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7b91c4: mov             fp, SP
    // 0x7b91c8: AllocStack(0xd8)
    //     0x7b91c8: sub             SP, SP, #0xd8
    // 0x7b91cc: SetupParameters(TtfWriter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7b91cc: stur            x1, [fp, #-8]
    //     0x7b91d0: stur            x2, [fp, #-0x10]
    // 0x7b91d4: CheckStackOverflow
    //     0x7b91d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b91d8: cmp             SP, x16
    //     0x7b91dc: b.ls            #0x7bbb54
    // 0x7b91e0: r1 = 4
    //     0x7b91e0: movz            x1, #0x4
    // 0x7b91e4: r0 = AllocateContext()
    //     0x7b91e4: bl              #0xec126c  ; AllocateContextStub
    // 0x7b91e8: ldur            x1, [fp, #-8]
    // 0x7b91ec: stur            x0, [fp, #-0x18]
    // 0x7b91f0: StoreField: r0->field_f = r1
    //     0x7b91f0: stur            w1, [x0, #0xf]
    // 0x7b91f4: r16 = <String, Uint8List>
    //     0x7b91f4: add             x16, PP, #0x47, lsl #12  ; [pp+0x476e0] TypeArguments: <String, Uint8List>
    //     0x7b91f8: ldr             x16, [x16, #0x6e0]
    // 0x7b91fc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7b9200: stp             lr, x16, [SP]
    // 0x7b9204: r0 = Map._fromLiteral()
    //     0x7b9204: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b9208: stur            x0, [fp, #-0x20]
    // 0x7b920c: r16 = <String, int>
    //     0x7b920c: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0x7b9210: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7b9214: stp             lr, x16, [SP]
    // 0x7b9218: r0 = Map._fromLiteral()
    //     0x7b9218: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b921c: stur            x0, [fp, #-0x28]
    // 0x7b9220: r16 = <int, TtfGlyphInfo>
    //     0x7b9220: add             x16, PP, #0x47, lsl #12  ; [pp+0x476e8] TypeArguments: <int, TtfGlyphInfo>
    //     0x7b9224: ldr             x16, [x16, #0x6e8]
    // 0x7b9228: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7b922c: stp             lr, x16, [SP]
    // 0x7b9230: r0 = Map._fromLiteral()
    //     0x7b9230: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b9234: mov             x2, x0
    // 0x7b9238: ldur            x1, [fp, #-0x18]
    // 0x7b923c: stur            x2, [fp, #-0x30]
    // 0x7b9240: StoreField: r1->field_13 = r0
    //     0x7b9240: stur            w0, [x1, #0x13]
    //     0x7b9244: ldurb           w16, [x1, #-1]
    //     0x7b9248: ldurb           w17, [x0, #-1]
    //     0x7b924c: and             x16, x17, x16, lsr #2
    //     0x7b9250: tst             x16, HEAP, lsr #32
    //     0x7b9254: b.eq            #0x7b925c
    //     0x7b9258: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b925c: r16 = <int, int>
    //     0x7b925c: ldr             x16, [PP, #0x28b0]  ; [pp+0x28b0] TypeArguments: <int, int>
    // 0x7b9260: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7b9264: stp             lr, x16, [SP]
    // 0x7b9268: r0 = Map._fromLiteral()
    //     0x7b9268: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b926c: stur            x0, [fp, #-0x38]
    // 0x7b9270: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x7b9270: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7b9274: ldr             x0, [x0, #0x778]
    //     0x7b9278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7b927c: cmp             w0, w16
    //     0x7b9280: b.ne            #0x7b928c
    //     0x7b9284: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x7b9288: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7b928c: r1 = <int>
    //     0x7b928c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7b9290: stur            x0, [fp, #-0x40]
    // 0x7b9294: r0 = _Set()
    //     0x7b9294: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x7b9298: mov             x1, x0
    // 0x7b929c: ldur            x0, [fp, #-0x40]
    // 0x7b92a0: stur            x1, [fp, #-0x48]
    // 0x7b92a4: StoreField: r1->field_1b = r0
    //     0x7b92a4: stur            w0, [x1, #0x1b]
    // 0x7b92a8: StoreField: r1->field_b = rZR
    //     0x7b92a8: stur            wzr, [x1, #0xb]
    // 0x7b92ac: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x7b92ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7b92b0: ldr             x0, [x0, #0x780]
    //     0x7b92b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7b92b8: cmp             w0, w16
    //     0x7b92bc: b.ne            #0x7b92c8
    //     0x7b92c0: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x7b92c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7b92c8: mov             x1, x0
    // 0x7b92cc: ldur            x0, [fp, #-0x48]
    // 0x7b92d0: stur            x1, [fp, #-0x50]
    // 0x7b92d4: StoreField: r0->field_f = r1
    //     0x7b92d4: stur            w1, [x0, #0xf]
    // 0x7b92d8: StoreField: r0->field_13 = rZR
    //     0x7b92d8: stur            wzr, [x0, #0x13]
    // 0x7b92dc: ArrayStore: r0[0] = rZR  ; List_4
    //     0x7b92dc: stur            wzr, [x0, #0x17]
    // 0x7b92e0: ldur            x2, [fp, #-0x18]
    // 0x7b92e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x7b92e4: stur            w0, [x2, #0x17]
    //     0x7b92e8: ldurb           w16, [x2, #-1]
    //     0x7b92ec: ldurb           w17, [x0, #-1]
    //     0x7b92f0: and             x16, x17, x16, lsr #2
    //     0x7b92f4: tst             x16, HEAP, lsr #32
    //     0x7b92f8: b.eq            #0x7b9300
    //     0x7b92fc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7b9300: r16 = <int, int>
    //     0x7b9300: ldr             x16, [PP, #0x28b0]  ; [pp+0x28b0] TypeArguments: <int, int>
    // 0x7b9304: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7b9308: stp             lr, x16, [SP]
    // 0x7b930c: r0 = Map._fromLiteral()
    //     0x7b930c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b9310: mov             x2, x0
    // 0x7b9314: ldur            x1, [fp, #-0x18]
    // 0x7b9318: stur            x2, [fp, #-0x88]
    // 0x7b931c: StoreField: r1->field_1b = r0
    //     0x7b931c: stur            w0, [x1, #0x1b]
    //     0x7b9320: ldurb           w16, [x1, #-1]
    //     0x7b9324: ldurb           w17, [x0, #-1]
    //     0x7b9328: and             x16, x17, x16, lsr #2
    //     0x7b932c: tst             x16, HEAP, lsr #32
    //     0x7b9330: b.eq            #0x7b9338
    //     0x7b9334: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b9338: ldur            x0, [fp, #-0x10]
    // 0x7b933c: LoadField: r3 = r0->field_b
    //     0x7b933c: ldur            w3, [x0, #0xb]
    // 0x7b9340: r4 = LoadInt32Instr(r3)
    //     0x7b9340: sbfx            x4, x3, #1, #0x1f
    // 0x7b9344: ldur            x3, [fp, #-8]
    // 0x7b9348: stur            x4, [fp, #-0x80]
    // 0x7b934c: LoadField: r5 = r3->field_7
    //     0x7b934c: ldur            w5, [x3, #7]
    // 0x7b9350: DecompressPointer r5
    //     0x7b9350: add             x5, x5, HEAP, lsl #32
    // 0x7b9354: stur            x5, [fp, #-0x78]
    // 0x7b9358: LoadField: r6 = r5->field_13
    //     0x7b9358: ldur            w6, [x5, #0x13]
    // 0x7b935c: DecompressPointer r6
    //     0x7b935c: add             x6, x6, HEAP, lsl #32
    // 0x7b9360: stur            x6, [fp, #-0x70]
    // 0x7b9364: ArrayLoad: r7 = r5[0]  ; List_4
    //     0x7b9364: ldur            w7, [x5, #0x17]
    // 0x7b9368: DecompressPointer r7
    //     0x7b9368: add             x7, x7, HEAP, lsl #32
    // 0x7b936c: ldur            x8, [fp, #-0x30]
    // 0x7b9370: stur            x7, [fp, #-0x68]
    // 0x7b9374: LoadField: r9 = r8->field_7
    //     0x7b9374: ldur            w9, [x8, #7]
    // 0x7b9378: DecompressPointer r9
    //     0x7b9378: add             x9, x9, HEAP, lsl #32
    // 0x7b937c: stur            x9, [fp, #-0x60]
    // 0x7b9380: r11 = 0
    //     0x7b9380: movz            x11, #0
    // 0x7b9384: ldur            x10, [fp, #-0x38]
    // 0x7b9388: CheckStackOverflow
    //     0x7b9388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b938c: cmp             SP, x16
    //     0x7b9390: b.ls            #0x7bbb5c
    // 0x7b9394: LoadField: r12 = r0->field_b
    //     0x7b9394: ldur            w12, [x0, #0xb]
    // 0x7b9398: r13 = LoadInt32Instr(r12)
    //     0x7b9398: sbfx            x13, x12, #1, #0x1f
    // 0x7b939c: cmp             x4, x13
    // 0x7b93a0: b.ne            #0x7bbb38
    // 0x7b93a4: cmp             x11, x13
    // 0x7b93a8: b.ge            #0x7b9634
    // 0x7b93ac: LoadField: r12 = r0->field_f
    //     0x7b93ac: ldur            w12, [x0, #0xf]
    // 0x7b93b0: DecompressPointer r12
    //     0x7b93b0: add             x12, x12, HEAP, lsl #32
    // 0x7b93b4: ArrayLoad: r13 = r12[r11]  ; Unknown_4
    //     0x7b93b4: add             x16, x12, x11, lsl #2
    //     0x7b93b8: ldur            w13, [x16, #0xf]
    // 0x7b93bc: DecompressPointer r13
    //     0x7b93bc: add             x13, x13, HEAP, lsl #32
    // 0x7b93c0: stur            x13, [fp, #-0x48]
    // 0x7b93c4: add             x12, x11, #1
    // 0x7b93c8: stur            x12, [fp, #-0x58]
    // 0x7b93cc: r1 = 1
    //     0x7b93cc: movz            x1, #0x1
    // 0x7b93d0: r0 = AllocateContext()
    //     0x7b93d0: bl              #0xec126c  ; AllocateContextStub
    // 0x7b93d4: mov             x3, x0
    // 0x7b93d8: ldur            x0, [fp, #-0x18]
    // 0x7b93dc: stur            x3, [fp, #-0xa8]
    // 0x7b93e0: StoreField: r3->field_b = r0
    //     0x7b93e0: stur            w0, [x3, #0xb]
    // 0x7b93e4: ldur            x4, [fp, #-0x48]
    // 0x7b93e8: cmp             w4, #0x40
    // 0x7b93ec: b.ne            #0x7b9518
    // 0x7b93f0: ldur            x3, [fp, #-0x70]
    // 0x7b93f4: mov             x1, x3
    // 0x7b93f8: mov             x2, x4
    // 0x7b93fc: r0 = _getValueOrData()
    //     0x7b93fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b9400: ldur            x1, [fp, #-0x70]
    // 0x7b9404: LoadField: r2 = r1->field_f
    //     0x7b9404: ldur            w2, [x1, #0xf]
    // 0x7b9408: DecompressPointer r2
    //     0x7b9408: add             x2, x2, HEAP, lsl #32
    // 0x7b940c: cmp             w2, w0
    // 0x7b9410: b.ne            #0x7b941c
    // 0x7b9414: r2 = Null
    //     0x7b9414: mov             x2, NULL
    // 0x7b9418: b               #0x7b9420
    // 0x7b941c: mov             x2, x0
    // 0x7b9420: ldur            x0, [fp, #-0x38]
    // 0x7b9424: stur            x2, [fp, #-0x98]
    // 0x7b9428: cmp             w2, NULL
    // 0x7b942c: b.eq            #0x7bbb64
    // 0x7b9430: r3 = LoadInt32Instr(r2)
    //     0x7b9430: sbfx            x3, x2, #1, #0x1f
    //     0x7b9434: tbz             w2, #0, #0x7b943c
    //     0x7b9438: ldur            x3, [x2, #7]
    // 0x7b943c: stur            x3, [fp, #-0x90]
    // 0x7b9440: r0 = TtfGlyphInfo()
    //     0x7b9440: bl              #0x7bcae4  ; AllocateTtfGlyphInfoStub -> TtfGlyphInfo (size=0x18)
    // 0x7b9444: mov             x1, x0
    // 0x7b9448: ldur            x0, [fp, #-0x90]
    // 0x7b944c: stur            x1, [fp, #-0xa0]
    // 0x7b9450: StoreField: r1->field_7 = r0
    //     0x7b9450: stur            x0, [x1, #7]
    // 0x7b9454: r4 = 0
    //     0x7b9454: movz            x4, #0
    // 0x7b9458: r0 = AllocateUint8Array()
    //     0x7b9458: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b945c: ldur            x3, [fp, #-0xa0]
    // 0x7b9460: StoreField: r3->field_f = r0
    //     0x7b9460: stur            w0, [x3, #0xf]
    // 0x7b9464: r4 = const []
    //     0x7b9464: add             x4, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0x7b9468: ldr             x4, [x4, #0x8c0]
    // 0x7b946c: StoreField: r3->field_13 = r4
    //     0x7b946c: stur            w4, [x3, #0x13]
    // 0x7b9470: ldur            x0, [fp, #-0x98]
    // 0x7b9474: ldur            x2, [fp, #-0x60]
    // 0x7b9478: r1 = Null
    //     0x7b9478: mov             x1, NULL
    // 0x7b947c: cmp             w2, NULL
    // 0x7b9480: b.eq            #0x7b94a0
    // 0x7b9484: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b9484: ldur            w4, [x2, #0x17]
    // 0x7b9488: DecompressPointer r4
    //     0x7b9488: add             x4, x4, HEAP, lsl #32
    // 0x7b948c: r8 = X0
    //     0x7b948c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7b9490: LoadField: r9 = r4->field_7
    //     0x7b9490: ldur            x9, [x4, #7]
    // 0x7b9494: r3 = Null
    //     0x7b9494: add             x3, PP, #0x47, lsl #12  ; [pp+0x476f0] Null
    //     0x7b9498: ldr             x3, [x3, #0x6f0]
    // 0x7b949c: blr             x9
    // 0x7b94a0: ldur            x0, [fp, #-0xa0]
    // 0x7b94a4: ldur            x2, [fp, #-0x60]
    // 0x7b94a8: r1 = Null
    //     0x7b94a8: mov             x1, NULL
    // 0x7b94ac: cmp             w2, NULL
    // 0x7b94b0: b.eq            #0x7b94d0
    // 0x7b94b4: LoadField: r4 = r2->field_1b
    //     0x7b94b4: ldur            w4, [x2, #0x1b]
    // 0x7b94b8: DecompressPointer r4
    //     0x7b94b8: add             x4, x4, HEAP, lsl #32
    // 0x7b94bc: r8 = X1
    //     0x7b94bc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x7b94c0: LoadField: r9 = r4->field_7
    //     0x7b94c0: ldur            x9, [x4, #7]
    // 0x7b94c4: r3 = Null
    //     0x7b94c4: add             x3, PP, #0x47, lsl #12  ; [pp+0x47700] Null
    //     0x7b94c8: ldr             x3, [x3, #0x700]
    // 0x7b94cc: blr             x9
    // 0x7b94d0: ldur            x1, [fp, #-0x30]
    // 0x7b94d4: ldur            x2, [fp, #-0x98]
    // 0x7b94d8: r0 = _hashCode()
    //     0x7b94d8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7b94dc: ldur            x1, [fp, #-0x30]
    // 0x7b94e0: ldur            x2, [fp, #-0x98]
    // 0x7b94e4: ldur            x3, [fp, #-0xa0]
    // 0x7b94e8: mov             x5, x0
    // 0x7b94ec: r0 = _set()
    //     0x7b94ec: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7b94f0: ldur            x4, [fp, #-0x38]
    // 0x7b94f4: r0 = LoadClassIdInstr(r4)
    //     0x7b94f4: ldur            x0, [x4, #-1]
    //     0x7b94f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7b94fc: mov             x1, x4
    // 0x7b9500: ldur            x2, [fp, #-0x48]
    // 0x7b9504: ldur            x3, [fp, #-0x98]
    // 0x7b9508: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9508: sub             lr, x0, #0x10d
    //     0x7b950c: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9510: blr             lr
    // 0x7b9514: b               #0x7b9604
    // 0x7b9518: ldur            x0, [fp, #-0x70]
    // 0x7b951c: mov             x1, x0
    // 0x7b9520: ldur            x2, [fp, #-0x48]
    // 0x7b9524: r0 = _getValueOrData()
    //     0x7b9524: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b9528: ldur            x3, [fp, #-0x70]
    // 0x7b952c: LoadField: r1 = r3->field_f
    //     0x7b952c: ldur            w1, [x3, #0xf]
    // 0x7b9530: DecompressPointer r1
    //     0x7b9530: add             x1, x1, HEAP, lsl #32
    // 0x7b9534: cmp             w1, w0
    // 0x7b9538: b.ne            #0x7b9540
    // 0x7b953c: r0 = Null
    //     0x7b953c: mov             x0, NULL
    // 0x7b9540: cmp             w0, NULL
    // 0x7b9544: b.ne            #0x7b9550
    // 0x7b9548: r2 = 0
    //     0x7b9548: movz            x2, #0
    // 0x7b954c: b               #0x7b9560
    // 0x7b9550: r1 = LoadInt32Instr(r0)
    //     0x7b9550: sbfx            x1, x0, #1, #0x1f
    //     0x7b9554: tbz             w0, #0, #0x7b955c
    //     0x7b9558: ldur            x1, [x0, #7]
    // 0x7b955c: mov             x2, x1
    // 0x7b9560: ldur            x4, [fp, #-0x68]
    // 0x7b9564: LoadField: r5 = r4->field_b
    //     0x7b9564: ldur            w5, [x4, #0xb]
    // 0x7b9568: r0 = BoxInt64Instr(r2)
    //     0x7b9568: sbfiz           x0, x2, #1, #0x1f
    //     0x7b956c: cmp             x2, x0, asr #1
    //     0x7b9570: b.eq            #0x7b957c
    //     0x7b9574: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b9578: stur            x2, [x0, #7]
    // 0x7b957c: stur            x0, [fp, #-0x98]
    // 0x7b9580: r1 = LoadInt32Instr(r5)
    //     0x7b9580: sbfx            x1, x5, #1, #0x1f
    // 0x7b9584: cmp             x2, x1
    // 0x7b9588: b.ge            #0x7b9604
    // 0x7b958c: ldur            x5, [fp, #-0xa8]
    // 0x7b9590: mov             x2, x5
    // 0x7b9594: r1 = Function 'addGlyph':.
    //     0x7b9594: add             x1, PP, #0x47, lsl #12  ; [pp+0x47710] AnonymousClosure: (0x7c8334), in [package:pdf/src/pdf/font/ttf_writer.dart] TtfWriter::withChars (0x7b91c0)
    //     0x7b9598: ldr             x1, [x1, #0x710]
    // 0x7b959c: r0 = AllocateClosure()
    //     0x7b959c: bl              #0xec1630  ; AllocateClosureStub
    // 0x7b95a0: mov             x3, x0
    // 0x7b95a4: ldur            x1, [fp, #-0xa8]
    // 0x7b95a8: stur            x3, [fp, #-0xa0]
    // 0x7b95ac: StoreField: r1->field_f = r0
    //     0x7b95ac: stur            w0, [x1, #0xf]
    //     0x7b95b0: ldurb           w16, [x1, #-1]
    //     0x7b95b4: ldurb           w17, [x0, #-1]
    //     0x7b95b8: and             x16, x17, x16, lsr #2
    //     0x7b95bc: tst             x16, HEAP, lsr #32
    //     0x7b95c0: b.eq            #0x7b95c8
    //     0x7b95c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b95c8: ldur            x1, [fp, #-0x38]
    // 0x7b95cc: ldur            x2, [fp, #-0x48]
    // 0x7b95d0: r0 = _hashCode()
    //     0x7b95d0: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7b95d4: ldur            x1, [fp, #-0x38]
    // 0x7b95d8: ldur            x2, [fp, #-0x48]
    // 0x7b95dc: ldur            x3, [fp, #-0x98]
    // 0x7b95e0: mov             x5, x0
    // 0x7b95e4: r0 = _set()
    //     0x7b95e4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7b95e8: ldur            x16, [fp, #-0xa0]
    // 0x7b95ec: ldur            lr, [fp, #-0x98]
    // 0x7b95f0: stp             lr, x16, [SP]
    // 0x7b95f4: ldur            x0, [fp, #-0xa0]
    // 0x7b95f8: ClosureCall
    //     0x7b95f8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7b95fc: ldur            x2, [x0, #0x1f]
    //     0x7b9600: blr             x2
    // 0x7b9604: ldur            x11, [fp, #-0x58]
    // 0x7b9608: ldur            x3, [fp, #-8]
    // 0x7b960c: ldur            x0, [fp, #-0x10]
    // 0x7b9610: ldur            x1, [fp, #-0x18]
    // 0x7b9614: ldur            x8, [fp, #-0x30]
    // 0x7b9618: ldur            x2, [fp, #-0x88]
    // 0x7b961c: ldur            x5, [fp, #-0x78]
    // 0x7b9620: ldur            x6, [fp, #-0x70]
    // 0x7b9624: ldur            x7, [fp, #-0x68]
    // 0x7b9628: ldur            x9, [fp, #-0x60]
    // 0x7b962c: ldur            x4, [fp, #-0x80]
    // 0x7b9630: b               #0x7b9384
    // 0x7b9634: r1 = <TtfGlyphInfo>
    //     0x7b9634: add             x1, PP, #0x47, lsl #12  ; [pp+0x47718] TypeArguments: <TtfGlyphInfo>
    //     0x7b9638: ldr             x1, [x1, #0x718]
    // 0x7b963c: r2 = 0
    //     0x7b963c: movz            x2, #0
    // 0x7b9640: r0 = _GrowableList()
    //     0x7b9640: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b9644: mov             x4, x0
    // 0x7b9648: ldur            x3, [fp, #-0x10]
    // 0x7b964c: stur            x4, [fp, #-0x48]
    // 0x7b9650: LoadField: r0 = r3->field_b
    //     0x7b9650: ldur            w0, [x3, #0xb]
    // 0x7b9654: r5 = LoadInt32Instr(r0)
    //     0x7b9654: sbfx            x5, x0, #1, #0x1f
    // 0x7b9658: stur            x5, [fp, #-0x80]
    // 0x7b965c: r0 = 0
    //     0x7b965c: movz            x0, #0
    // 0x7b9660: ldur            x7, [fp, #-0x30]
    // 0x7b9664: ldur            x6, [fp, #-0x38]
    // 0x7b9668: CheckStackOverflow
    //     0x7b9668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b966c: cmp             SP, x16
    //     0x7b9670: b.ls            #0x7bbb68
    // 0x7b9674: LoadField: r1 = r3->field_b
    //     0x7b9674: ldur            w1, [x3, #0xb]
    // 0x7b9678: r2 = LoadInt32Instr(r1)
    //     0x7b9678: sbfx            x2, x1, #1, #0x1f
    // 0x7b967c: cmp             x5, x2
    // 0x7b9680: b.ne            #0x7bbb18
    // 0x7b9684: cmp             x0, x2
    // 0x7b9688: b.ge            #0x7b9878
    // 0x7b968c: LoadField: r1 = r3->field_f
    //     0x7b968c: ldur            w1, [x3, #0xf]
    // 0x7b9690: DecompressPointer r1
    //     0x7b9690: add             x1, x1, HEAP, lsl #32
    // 0x7b9694: ArrayLoad: r2 = r1[r0]  ; Unknown_4
    //     0x7b9694: add             x16, x1, x0, lsl #2
    //     0x7b9698: ldur            w2, [x16, #0xf]
    // 0x7b969c: DecompressPointer r2
    //     0x7b969c: add             x2, x2, HEAP, lsl #32
    // 0x7b96a0: add             x8, x0, #1
    // 0x7b96a4: stur            x8, [fp, #-0x58]
    // 0x7b96a8: r0 = LoadClassIdInstr(r6)
    //     0x7b96a8: ldur            x0, [x6, #-1]
    //     0x7b96ac: ubfx            x0, x0, #0xc, #0x14
    // 0x7b96b0: mov             x1, x6
    // 0x7b96b4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7b96b4: sub             lr, x0, #0x114
    //     0x7b96b8: ldr             lr, [x21, lr, lsl #3]
    //     0x7b96bc: blr             lr
    // 0x7b96c0: stur            x0, [fp, #-0x68]
    // 0x7b96c4: cmp             w0, NULL
    // 0x7b96c8: b.eq            #0x7b9864
    // 0x7b96cc: ldur            x3, [fp, #-0x30]
    // 0x7b96d0: mov             x1, x3
    // 0x7b96d4: mov             x2, x0
    // 0x7b96d8: r0 = _getValueOrData()
    //     0x7b96d8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b96dc: mov             x1, x0
    // 0x7b96e0: ldur            x0, [fp, #-0x30]
    // 0x7b96e4: LoadField: r2 = r0->field_f
    //     0x7b96e4: ldur            w2, [x0, #0xf]
    // 0x7b96e8: DecompressPointer r2
    //     0x7b96e8: add             x2, x2, HEAP, lsl #32
    // 0x7b96ec: cmp             w2, w1
    // 0x7b96f0: b.ne            #0x7b96f8
    // 0x7b96f4: r1 = Null
    //     0x7b96f4: mov             x1, NULL
    // 0x7b96f8: cmp             w1, NULL
    // 0x7b96fc: b.ne            #0x7b97a4
    // 0x7b9700: ldur            x2, [fp, #-0x60]
    // 0x7b9704: r1 = Null
    //     0x7b9704: mov             x1, NULL
    // 0x7b9708: r3 = <X1>
    //     0x7b9708: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x7b970c: r0 = Null
    //     0x7b970c: mov             x0, NULL
    // 0x7b9710: cmp             x2, x0
    // 0x7b9714: b.eq            #0x7b9724
    // 0x7b9718: r30 = InstantiateTypeArgumentsStub
    //     0x7b9718: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7b971c: LoadField: r30 = r30->field_7
    //     0x7b971c: ldur            lr, [lr, #7]
    // 0x7b9720: blr             lr
    // 0x7b9724: mov             x1, x0
    // 0x7b9728: r0 = _CompactIterable()
    //     0x7b9728: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7b972c: mov             x1, x0
    // 0x7b9730: ldur            x0, [fp, #-0x30]
    // 0x7b9734: StoreField: r1->field_b = r0
    //     0x7b9734: stur            w0, [x1, #0xb]
    // 0x7b9738: r2 = -1
    //     0x7b9738: movn            x2, #0
    // 0x7b973c: StoreField: r1->field_f = r2
    //     0x7b973c: stur            x2, [x1, #0xf]
    // 0x7b9740: r3 = 2
    //     0x7b9740: movz            x3, #0x2
    // 0x7b9744: ArrayStore: r1[0] = r3  ; List_8
    //     0x7b9744: stur            x3, [x1, #0x17]
    // 0x7b9748: r0 = iterator()
    //     0x7b9748: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x7b974c: mov             x2, x0
    // 0x7b9750: stur            x2, [fp, #-0x70]
    // 0x7b9754: r0 = LoadClassIdInstr(r2)
    //     0x7b9754: ldur            x0, [x2, #-1]
    //     0x7b9758: ubfx            x0, x0, #0xc, #0x14
    // 0x7b975c: mov             x1, x2
    // 0x7b9760: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x7b9760: movz            x17, #0x292d
    //     0x7b9764: movk            x17, #0x1, lsl #16
    //     0x7b9768: add             lr, x0, x17
    //     0x7b976c: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9770: blr             lr
    // 0x7b9774: tbnz            w0, #4, #0x7bbaa4
    // 0x7b9778: ldur            x1, [fp, #-0x70]
    // 0x7b977c: r0 = LoadClassIdInstr(r1)
    //     0x7b977c: ldur            x0, [x1, #-1]
    //     0x7b9780: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9784: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x7b9784: movz            x17, #0x384d
    //     0x7b9788: movk            x17, #0x1, lsl #16
    //     0x7b978c: add             lr, x0, x17
    //     0x7b9790: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9794: blr             lr
    // 0x7b9798: mov             x1, x0
    // 0x7b979c: mov             x4, x1
    // 0x7b97a0: b               #0x7b97a8
    // 0x7b97a4: mov             x4, x1
    // 0x7b97a8: ldur            x3, [fp, #-0x48]
    // 0x7b97ac: mov             x0, x4
    // 0x7b97b0: stur            x4, [fp, #-0x70]
    // 0x7b97b4: r2 = Null
    //     0x7b97b4: mov             x2, NULL
    // 0x7b97b8: r1 = Null
    //     0x7b97b8: mov             x1, NULL
    // 0x7b97bc: r4 = LoadClassIdInstr(r0)
    //     0x7b97bc: ldur            x4, [x0, #-1]
    //     0x7b97c0: ubfx            x4, x4, #0xc, #0x14
    // 0x7b97c4: cmp             x4, #0x397
    // 0x7b97c8: b.eq            #0x7b97e0
    // 0x7b97cc: r8 = TtfGlyphInfo
    //     0x7b97cc: add             x8, PP, #0x47, lsl #12  ; [pp+0x47720] Type: TtfGlyphInfo
    //     0x7b97d0: ldr             x8, [x8, #0x720]
    // 0x7b97d4: r3 = Null
    //     0x7b97d4: add             x3, PP, #0x47, lsl #12  ; [pp+0x47728] Null
    //     0x7b97d8: ldr             x3, [x3, #0x728]
    // 0x7b97dc: r0 = TtfGlyphInfo()
    //     0x7b97dc: bl              #0x7c8314  ; IsType_TtfGlyphInfo_Stub
    // 0x7b97e0: ldur            x0, [fp, #-0x48]
    // 0x7b97e4: LoadField: r1 = r0->field_b
    //     0x7b97e4: ldur            w1, [x0, #0xb]
    // 0x7b97e8: LoadField: r2 = r0->field_f
    //     0x7b97e8: ldur            w2, [x0, #0xf]
    // 0x7b97ec: DecompressPointer r2
    //     0x7b97ec: add             x2, x2, HEAP, lsl #32
    // 0x7b97f0: LoadField: r3 = r2->field_b
    //     0x7b97f0: ldur            w3, [x2, #0xb]
    // 0x7b97f4: r2 = LoadInt32Instr(r1)
    //     0x7b97f4: sbfx            x2, x1, #1, #0x1f
    // 0x7b97f8: stur            x2, [fp, #-0x90]
    // 0x7b97fc: r1 = LoadInt32Instr(r3)
    //     0x7b97fc: sbfx            x1, x3, #1, #0x1f
    // 0x7b9800: cmp             x2, x1
    // 0x7b9804: b.ne            #0x7b9810
    // 0x7b9808: mov             x1, x0
    // 0x7b980c: r0 = _growToNextCapacity()
    //     0x7b980c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b9810: ldur            x3, [fp, #-0x48]
    // 0x7b9814: ldur            x2, [fp, #-0x90]
    // 0x7b9818: add             x0, x2, #1
    // 0x7b981c: lsl             x1, x0, #1
    // 0x7b9820: StoreField: r3->field_b = r1
    //     0x7b9820: stur            w1, [x3, #0xb]
    // 0x7b9824: LoadField: r1 = r3->field_f
    //     0x7b9824: ldur            w1, [x3, #0xf]
    // 0x7b9828: DecompressPointer r1
    //     0x7b9828: add             x1, x1, HEAP, lsl #32
    // 0x7b982c: ldur            x0, [fp, #-0x70]
    // 0x7b9830: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7b9830: add             x25, x1, x2, lsl #2
    //     0x7b9834: add             x25, x25, #0xf
    //     0x7b9838: str             w0, [x25]
    //     0x7b983c: tbz             w0, #0, #0x7b9858
    //     0x7b9840: ldurb           w16, [x1, #-1]
    //     0x7b9844: ldurb           w17, [x0, #-1]
    //     0x7b9848: and             x16, x17, x16, lsr #2
    //     0x7b984c: tst             x16, HEAP, lsr #32
    //     0x7b9850: b.eq            #0x7b9858
    //     0x7b9854: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b9858: ldur            x1, [fp, #-0x30]
    // 0x7b985c: ldur            x2, [fp, #-0x68]
    // 0x7b9860: r0 = remove()
    //     0x7b9860: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x7b9864: ldur            x0, [fp, #-0x58]
    // 0x7b9868: ldur            x3, [fp, #-0x10]
    // 0x7b986c: ldur            x4, [fp, #-0x48]
    // 0x7b9870: ldur            x5, [fp, #-0x80]
    // 0x7b9874: b               #0x7b9660
    // 0x7b9878: mov             x0, x7
    // 0x7b987c: ldur            x4, [fp, #-0x88]
    // 0x7b9880: ldur            x2, [fp, #-0x60]
    // 0x7b9884: r1 = Null
    //     0x7b9884: mov             x1, NULL
    // 0x7b9888: r3 = <X1>
    //     0x7b9888: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x7b988c: r0 = Null
    //     0x7b988c: mov             x0, NULL
    // 0x7b9890: cmp             x2, x0
    // 0x7b9894: b.eq            #0x7b98a4
    // 0x7b9898: r30 = InstantiateTypeArgumentsStub
    //     0x7b9898: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7b989c: LoadField: r30 = r30->field_7
    //     0x7b989c: ldur            lr, [lr, #7]
    // 0x7b98a0: blr             lr
    // 0x7b98a4: mov             x1, x0
    // 0x7b98a8: r0 = _CompactIterable()
    //     0x7b98a8: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7b98ac: mov             x1, x0
    // 0x7b98b0: ldur            x0, [fp, #-0x30]
    // 0x7b98b4: StoreField: r1->field_b = r0
    //     0x7b98b4: stur            w0, [x1, #0xb]
    // 0x7b98b8: r0 = -1
    //     0x7b98b8: movn            x0, #0
    // 0x7b98bc: StoreField: r1->field_f = r0
    //     0x7b98bc: stur            x0, [x1, #0xf]
    // 0x7b98c0: r0 = 2
    //     0x7b98c0: movz            x0, #0x2
    // 0x7b98c4: ArrayStore: r1[0] = r0  ; List_8
    //     0x7b98c4: stur            x0, [x1, #0x17]
    // 0x7b98c8: mov             x2, x1
    // 0x7b98cc: ldur            x1, [fp, #-0x48]
    // 0x7b98d0: r0 = addAll()
    //     0x7b98d0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7b98d4: ldur            x0, [fp, #-0x88]
    // 0x7b98d8: LoadField: r1 = r0->field_7
    //     0x7b98d8: ldur            w1, [x0, #7]
    // 0x7b98dc: DecompressPointer r1
    //     0x7b98dc: add             x1, x1, HEAP, lsl #32
    // 0x7b98e0: r0 = _CompactIterable()
    //     0x7b98e0: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7b98e4: mov             x1, x0
    // 0x7b98e8: ldur            x0, [fp, #-0x88]
    // 0x7b98ec: StoreField: r1->field_b = r0
    //     0x7b98ec: stur            w0, [x1, #0xb]
    // 0x7b98f0: r2 = -2
    //     0x7b98f0: orr             x2, xzr, #0xfffffffffffffffe
    // 0x7b98f4: StoreField: r1->field_f = r2
    //     0x7b98f4: stur            x2, [x1, #0xf]
    // 0x7b98f8: r2 = 2
    //     0x7b98f8: movz            x2, #0x2
    // 0x7b98fc: ArrayStore: r1[0] = r2  ; List_8
    //     0x7b98fc: stur            x2, [x1, #0x17]
    // 0x7b9900: r0 = iterator()
    //     0x7b9900: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x7b9904: stur            x0, [fp, #-0x38]
    // 0x7b9908: LoadField: r2 = r0->field_7
    //     0x7b9908: ldur            w2, [x0, #7]
    // 0x7b990c: DecompressPointer r2
    //     0x7b990c: add             x2, x2, HEAP, lsl #32
    // 0x7b9910: stur            x2, [fp, #-0x30]
    // 0x7b9914: ldur            x4, [fp, #-0x18]
    // 0x7b9918: ldur            x3, [fp, #-0x48]
    // 0x7b991c: CheckStackOverflow
    //     0x7b991c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b9920: cmp             SP, x16
    //     0x7b9924: b.ls            #0x7bbb70
    // 0x7b9928: mov             x1, x0
    // 0x7b992c: r0 = moveNext()
    //     0x7b992c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x7b9930: tbnz            w0, #4, #0x7b9a5c
    // 0x7b9934: ldur            x1, [fp, #-0x18]
    // 0x7b9938: ldur            x0, [fp, #-0x38]
    // 0x7b993c: r1 = 1
    //     0x7b993c: movz            x1, #0x1
    // 0x7b9940: r0 = AllocateContext()
    //     0x7b9940: bl              #0xec126c  ; AllocateContextStub
    // 0x7b9944: mov             x4, x0
    // 0x7b9948: ldur            x3, [fp, #-0x18]
    // 0x7b994c: stur            x4, [fp, #-0x68]
    // 0x7b9950: StoreField: r4->field_b = r3
    //     0x7b9950: stur            w3, [x4, #0xb]
    // 0x7b9954: ldur            x5, [fp, #-0x38]
    // 0x7b9958: LoadField: r6 = r5->field_33
    //     0x7b9958: ldur            w6, [x5, #0x33]
    // 0x7b995c: DecompressPointer r6
    //     0x7b995c: add             x6, x6, HEAP, lsl #32
    // 0x7b9960: stur            x6, [fp, #-0x60]
    // 0x7b9964: cmp             w6, NULL
    // 0x7b9968: b.ne            #0x7b999c
    // 0x7b996c: mov             x0, x6
    // 0x7b9970: ldur            x2, [fp, #-0x30]
    // 0x7b9974: r1 = Null
    //     0x7b9974: mov             x1, NULL
    // 0x7b9978: cmp             w2, NULL
    // 0x7b997c: b.eq            #0x7b999c
    // 0x7b9980: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b9980: ldur            w4, [x2, #0x17]
    // 0x7b9984: DecompressPointer r4
    //     0x7b9984: add             x4, x4, HEAP, lsl #32
    // 0x7b9988: r8 = X0
    //     0x7b9988: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7b998c: LoadField: r9 = r4->field_7
    //     0x7b998c: ldur            x9, [x4, #7]
    // 0x7b9990: r3 = Null
    //     0x7b9990: add             x3, PP, #0x47, lsl #12  ; [pp+0x47738] Null
    //     0x7b9994: ldr             x3, [x3, #0x738]
    // 0x7b9998: blr             x9
    // 0x7b999c: ldur            x3, [fp, #-0x48]
    // 0x7b99a0: ldur            x2, [fp, #-0x68]
    // 0x7b99a4: ldur            x0, [fp, #-0x60]
    // 0x7b99a8: StoreField: r2->field_f = r0
    //     0x7b99a8: stur            w0, [x2, #0xf]
    // 0x7b99ac: r1 = Function '<anonymous closure>':.
    //     0x7b99ac: add             x1, PP, #0x47, lsl #12  ; [pp+0x47748] AnonymousClosure: (0x7bcaf0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByVerseId (0x7bcb2c)
    //     0x7b99b0: ldr             x1, [x1, #0x748]
    // 0x7b99b4: r0 = AllocateClosure()
    //     0x7b99b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7b99b8: ldur            x1, [fp, #-0x48]
    // 0x7b99bc: mov             x2, x0
    // 0x7b99c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7b99c0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7b99c4: r0 = firstWhere()
    //     0x7b99c4: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x7b99c8: mov             x1, x0
    // 0x7b99cc: ldur            x0, [fp, #-0x48]
    // 0x7b99d0: LoadField: r2 = r0->field_b
    //     0x7b99d0: ldur            w2, [x0, #0xb]
    // 0x7b99d4: r3 = LoadInt32Instr(r2)
    //     0x7b99d4: sbfx            x3, x2, #1, #0x1f
    // 0x7b99d8: LoadField: r2 = r0->field_f
    //     0x7b99d8: ldur            w2, [x0, #0xf]
    // 0x7b99dc: DecompressPointer r2
    //     0x7b99dc: add             x2, x2, HEAP, lsl #32
    // 0x7b99e0: r4 = 0
    //     0x7b99e0: movz            x4, #0
    // 0x7b99e4: CheckStackOverflow
    //     0x7b99e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b99e8: cmp             SP, x16
    //     0x7b99ec: b.ls            #0x7bbb78
    // 0x7b99f0: cmp             x4, x3
    // 0x7b99f4: b.ge            #0x7b9a20
    // 0x7b99f8: ArrayLoad: r5 = r2[r4]  ; Unknown_4
    //     0x7b99f8: add             x16, x2, x4, lsl #2
    //     0x7b99fc: ldur            w5, [x16, #0xf]
    // 0x7b9a00: DecompressPointer r5
    //     0x7b9a00: add             x5, x5, HEAP, lsl #32
    // 0x7b9a04: cmp             w5, w1
    // 0x7b9a08: b.eq            #0x7b9a18
    // 0x7b9a0c: add             x5, x4, #1
    // 0x7b9a10: mov             x4, x5
    // 0x7b9a14: b               #0x7b99e4
    // 0x7b9a18: mov             x3, x4
    // 0x7b9a1c: b               #0x7b9a24
    // 0x7b9a20: r3 = -1
    //     0x7b9a20: movn            x3, #0
    // 0x7b9a24: ldur            x1, [fp, #-0x88]
    // 0x7b9a28: ldur            x2, [fp, #-0x60]
    // 0x7b9a2c: stur            x3, [fp, #-0x58]
    // 0x7b9a30: r0 = _hashCode()
    //     0x7b9a30: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7b9a34: mov             x1, x0
    // 0x7b9a38: ldur            x0, [fp, #-0x58]
    // 0x7b9a3c: lsl             x3, x0, #1
    // 0x7b9a40: mov             x5, x1
    // 0x7b9a44: ldur            x1, [fp, #-0x88]
    // 0x7b9a48: ldur            x2, [fp, #-0x60]
    // 0x7b9a4c: r0 = _set()
    //     0x7b9a4c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7b9a50: ldur            x0, [fp, #-0x38]
    // 0x7b9a54: ldur            x2, [fp, #-0x30]
    // 0x7b9a58: b               #0x7b9914
    // 0x7b9a5c: ldur            x2, [fp, #-0x48]
    // 0x7b9a60: LoadField: r0 = r2->field_b
    //     0x7b9a60: ldur            w0, [x2, #0xb]
    // 0x7b9a64: r3 = LoadInt32Instr(r0)
    //     0x7b9a64: sbfx            x3, x0, #1, #0x1f
    // 0x7b9a68: stur            x3, [fp, #-0x80]
    // 0x7b9a6c: r0 = 0
    //     0x7b9a6c: movz            x0, #0
    // 0x7b9a70: CheckStackOverflow
    //     0x7b9a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b9a74: cmp             SP, x16
    //     0x7b9a78: b.ls            #0x7bbb80
    // 0x7b9a7c: LoadField: r1 = r2->field_b
    //     0x7b9a7c: ldur            w1, [x2, #0xb]
    // 0x7b9a80: r4 = LoadInt32Instr(r1)
    //     0x7b9a80: sbfx            x4, x1, #1, #0x1f
    // 0x7b9a84: stur            x4, [fp, #-0x90]
    // 0x7b9a88: cmp             x3, x4
    // 0x7b9a8c: b.ne            #0x7bbaf8
    // 0x7b9a90: cmp             x0, x4
    // 0x7b9a94: b.ge            #0x7b9afc
    // 0x7b9a98: LoadField: r1 = r2->field_f
    //     0x7b9a98: ldur            w1, [x2, #0xf]
    // 0x7b9a9c: DecompressPointer r1
    //     0x7b9a9c: add             x1, x1, HEAP, lsl #32
    // 0x7b9aa0: ArrayLoad: r4 = r1[r0]  ; Unknown_4
    //     0x7b9aa0: add             x16, x1, x0, lsl #2
    //     0x7b9aa4: ldur            w4, [x16, #0xf]
    // 0x7b9aa8: DecompressPointer r4
    //     0x7b9aa8: add             x4, x4, HEAP, lsl #32
    // 0x7b9aac: stur            x4, [fp, #-0x18]
    // 0x7b9ab0: add             x5, x0, #1
    // 0x7b9ab4: stur            x5, [fp, #-0x58]
    // 0x7b9ab8: LoadField: r1 = r4->field_13
    //     0x7b9ab8: ldur            w1, [x4, #0x13]
    // 0x7b9abc: DecompressPointer r1
    //     0x7b9abc: add             x1, x1, HEAP, lsl #32
    // 0x7b9ac0: r0 = LoadClassIdInstr(r1)
    //     0x7b9ac0: ldur            x0, [x1, #-1]
    //     0x7b9ac4: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9ac8: r0 = GDT[cid_x0 + 0xd488]()
    //     0x7b9ac8: movz            x17, #0xd488
    //     0x7b9acc: add             lr, x0, x17
    //     0x7b9ad0: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9ad4: blr             lr
    // 0x7b9ad8: tbnz            w0, #4, #0x7b9aec
    // 0x7b9adc: ldur            x1, [fp, #-8]
    // 0x7b9ae0: ldur            x2, [fp, #-0x18]
    // 0x7b9ae4: ldur            x3, [fp, #-0x88]
    // 0x7b9ae8: r0 = _updateCompoundGlyph()
    //     0x7b9ae8: bl              #0x7bc85c  ; [package:pdf/src/pdf/font/ttf_writer.dart] TtfWriter::_updateCompoundGlyph
    // 0x7b9aec: ldur            x0, [fp, #-0x58]
    // 0x7b9af0: ldur            x2, [fp, #-0x48]
    // 0x7b9af4: ldur            x3, [fp, #-0x80]
    // 0x7b9af8: b               #0x7b9a70
    // 0x7b9afc: r3 = 0
    //     0x7b9afc: movz            x3, #0
    // 0x7b9b00: r0 = 0
    //     0x7b9b00: movz            x0, #0
    // 0x7b9b04: ldur            x2, [fp, #-0x48]
    // 0x7b9b08: stur            x3, [fp, #-0x80]
    // 0x7b9b0c: CheckStackOverflow
    //     0x7b9b0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b9b10: cmp             SP, x16
    //     0x7b9b14: b.ls            #0x7bbb88
    // 0x7b9b18: LoadField: r1 = r2->field_b
    //     0x7b9b18: ldur            w1, [x2, #0xb]
    // 0x7b9b1c: r5 = LoadInt32Instr(r1)
    //     0x7b9b1c: sbfx            x5, x1, #1, #0x1f
    // 0x7b9b20: cmp             x4, x5
    // 0x7b9b24: b.ne            #0x7bbad8
    // 0x7b9b28: cmp             x0, x5
    // 0x7b9b2c: b.ge            #0x7b9ba4
    // 0x7b9b30: LoadField: r1 = r2->field_f
    //     0x7b9b30: ldur            w1, [x2, #0xf]
    // 0x7b9b34: DecompressPointer r1
    //     0x7b9b34: add             x1, x1, HEAP, lsl #32
    // 0x7b9b38: ArrayLoad: r5 = r1[r0]  ; Unknown_4
    //     0x7b9b38: add             x16, x1, x0, lsl #2
    //     0x7b9b3c: ldur            w5, [x16, #0xf]
    // 0x7b9b40: DecompressPointer r5
    //     0x7b9b40: add             x5, x5, HEAP, lsl #32
    // 0x7b9b44: add             x6, x0, #1
    // 0x7b9b48: stur            x6, [fp, #-0x58]
    // 0x7b9b4c: LoadField: r1 = r5->field_f
    //     0x7b9b4c: ldur            w1, [x5, #0xf]
    // 0x7b9b50: DecompressPointer r1
    //     0x7b9b50: add             x1, x1, HEAP, lsl #32
    // 0x7b9b54: r0 = LoadClassIdInstr(r1)
    //     0x7b9b54: ldur            x0, [x1, #-1]
    //     0x7b9b58: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9b5c: r0 = GDT[cid_x0 + 0xcd4c]()
    //     0x7b9b5c: movz            x17, #0xcd4c
    //     0x7b9b60: add             lr, x0, x17
    //     0x7b9b64: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9b68: blr             lr
    // 0x7b9b6c: ldur            x2, [fp, #-0x80]
    // 0x7b9b70: add             x1, x2, x0
    // 0x7b9b74: mov             x0, x1
    // 0x7b9b78: ubfx            x0, x0, #0, #0x20
    // 0x7b9b7c: r5 = 3
    //     0x7b9b7c: movz            x5, #0x3
    // 0x7b9b80: and             x2, x0, x5
    // 0x7b9b84: r6 = 4
    //     0x7b9b84: movz            x6, #0x4
    // 0x7b9b88: sub             w0, w6, w2
    // 0x7b9b8c: and             x2, x0, x5
    // 0x7b9b90: ubfx            x2, x2, #0, #0x20
    // 0x7b9b94: add             x3, x1, x2
    // 0x7b9b98: ldur            x0, [fp, #-0x58]
    // 0x7b9b9c: ldur            x4, [fp, #-0x90]
    // 0x7b9ba0: b               #0x7b9b04
    // 0x7b9ba4: ldur            x7, [fp, #-0x20]
    // 0x7b9ba8: mov             x2, x3
    // 0x7b9bac: ldur            x3, [fp, #-0x28]
    // 0x7b9bb0: r5 = 3
    //     0x7b9bb0: movz            x5, #0x3
    // 0x7b9bb4: r6 = 4
    //     0x7b9bb4: movz            x6, #0x4
    // 0x7b9bb8: mov             x0, x2
    // 0x7b9bbc: ubfx            x0, x0, #0, #0x20
    // 0x7b9bc0: and             x1, x0, x5
    // 0x7b9bc4: sub             w0, w6, w1
    // 0x7b9bc8: and             x1, x0, x5
    // 0x7b9bcc: ubfx            x1, x1, #0, #0x20
    // 0x7b9bd0: add             x4, x2, x1
    // 0x7b9bd4: r0 = BoxInt64Instr(r4)
    //     0x7b9bd4: sbfiz           x0, x4, #1, #0x1f
    //     0x7b9bd8: cmp             x4, x0, asr #1
    //     0x7b9bdc: b.eq            #0x7b9be8
    //     0x7b9be0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b9be4: stur            x4, [x0, #7]
    // 0x7b9be8: mov             x4, x0
    // 0x7b9bec: r0 = AllocateUint8Array()
    //     0x7b9bec: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b9bf0: mov             x5, x0
    // 0x7b9bf4: ldur            x4, [fp, #-0x20]
    // 0x7b9bf8: stur            x5, [fp, #-0x18]
    // 0x7b9bfc: r0 = LoadClassIdInstr(r4)
    //     0x7b9bfc: ldur            x0, [x4, #-1]
    //     0x7b9c00: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9c04: mov             x1, x4
    // 0x7b9c08: mov             x3, x5
    // 0x7b9c0c: r2 = "glyf"
    //     0x7b9c0c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0x7b9c10: ldr             x2, [x2, #0x860]
    // 0x7b9c14: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9c14: sub             lr, x0, #0x10d
    //     0x7b9c18: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9c1c: blr             lr
    // 0x7b9c20: ldur            x2, [fp, #-0x80]
    // 0x7b9c24: r0 = BoxInt64Instr(r2)
    //     0x7b9c24: sbfiz           x0, x2, #1, #0x1f
    //     0x7b9c28: cmp             x2, x0, asr #1
    //     0x7b9c2c: b.eq            #0x7b9c38
    //     0x7b9c30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b9c34: stur            x2, [x0, #7]
    // 0x7b9c38: ldur            x4, [fp, #-0x28]
    // 0x7b9c3c: r1 = LoadClassIdInstr(r4)
    //     0x7b9c3c: ldur            x1, [x4, #-1]
    //     0x7b9c40: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9c44: mov             x3, x0
    // 0x7b9c48: mov             x0, x1
    // 0x7b9c4c: mov             x1, x4
    // 0x7b9c50: r2 = "glyf"
    //     0x7b9c50: add             x2, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0x7b9c54: ldr             x2, [x2, #0x860]
    // 0x7b9c58: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9c58: sub             lr, x0, #0x10d
    //     0x7b9c5c: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9c60: blr             lr
    // 0x7b9c64: ldur            x1, [fp, #-0x78]
    // 0x7b9c68: r0 = indexToLocFormat()
    //     0x7b9c68: bl              #0x7bc74c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::indexToLocFormat
    // 0x7b9c6c: cbnz            x0, #0x7b9d28
    // 0x7b9c70: ldur            x0, [fp, #-0x20]
    // 0x7b9c74: ldur            x1, [fp, #-0x28]
    // 0x7b9c78: ldur            x5, [fp, #-0x48]
    // 0x7b9c7c: r2 = 3
    //     0x7b9c7c: movz            x2, #0x3
    // 0x7b9c80: r3 = 4
    //     0x7b9c80: movz            x3, #0x4
    // 0x7b9c84: LoadField: r4 = r5->field_b
    //     0x7b9c84: ldur            w4, [x5, #0xb]
    // 0x7b9c88: r6 = LoadInt32Instr(r4)
    //     0x7b9c88: sbfx            x6, x4, #1, #0x1f
    // 0x7b9c8c: add             x4, x6, #1
    // 0x7b9c90: lsl             x6, x4, #1
    // 0x7b9c94: mov             x4, x6
    // 0x7b9c98: ubfx            x4, x4, #0, #0x20
    // 0x7b9c9c: and             x7, x4, x2
    // 0x7b9ca0: sub             w4, w3, w7
    // 0x7b9ca4: and             x7, x4, x2
    // 0x7b9ca8: ubfx            x7, x7, #0, #0x20
    // 0x7b9cac: add             x4, x6, x7
    // 0x7b9cb0: lsl             x6, x4, #1
    // 0x7b9cb4: mov             x4, x6
    // 0x7b9cb8: r0 = AllocateUint8Array()
    //     0x7b9cb8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b9cbc: ldur            x4, [fp, #-0x20]
    // 0x7b9cc0: r1 = LoadClassIdInstr(r4)
    //     0x7b9cc0: ldur            x1, [x4, #-1]
    //     0x7b9cc4: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9cc8: mov             x3, x0
    // 0x7b9ccc: mov             x0, x1
    // 0x7b9cd0: mov             x1, x4
    // 0x7b9cd4: r2 = "loca"
    //     0x7b9cd4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7b9cd8: ldr             x2, [x2, #0x858]
    // 0x7b9cdc: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9cdc: sub             lr, x0, #0x10d
    //     0x7b9ce0: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9ce4: blr             lr
    // 0x7b9ce8: ldur            x4, [fp, #-0x48]
    // 0x7b9cec: LoadField: r0 = r4->field_b
    //     0x7b9cec: ldur            w0, [x4, #0xb]
    // 0x7b9cf0: r1 = LoadInt32Instr(r0)
    //     0x7b9cf0: sbfx            x1, x0, #1, #0x1f
    // 0x7b9cf4: add             x0, x1, #1
    // 0x7b9cf8: lsl             x1, x0, #1
    // 0x7b9cfc: lsl             x3, x1, #1
    // 0x7b9d00: ldur            x5, [fp, #-0x28]
    // 0x7b9d04: r0 = LoadClassIdInstr(r5)
    //     0x7b9d04: ldur            x0, [x5, #-1]
    //     0x7b9d08: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9d0c: mov             x1, x5
    // 0x7b9d10: r2 = "loca"
    //     0x7b9d10: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7b9d14: ldr             x2, [x2, #0x858]
    // 0x7b9d18: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9d18: sub             lr, x0, #0x10d
    //     0x7b9d1c: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9d20: blr             lr
    // 0x7b9d24: b               #0x7b9e04
    // 0x7b9d28: ldur            x5, [fp, #-0x20]
    // 0x7b9d2c: ldur            x3, [fp, #-0x28]
    // 0x7b9d30: ldur            x2, [fp, #-0x48]
    // 0x7b9d34: r6 = 3
    //     0x7b9d34: movz            x6, #0x3
    // 0x7b9d38: r7 = 4
    //     0x7b9d38: movz            x7, #0x4
    // 0x7b9d3c: LoadField: r0 = r2->field_b
    //     0x7b9d3c: ldur            w0, [x2, #0xb]
    // 0x7b9d40: r1 = LoadInt32Instr(r0)
    //     0x7b9d40: sbfx            x1, x0, #1, #0x1f
    // 0x7b9d44: add             x0, x1, #1
    // 0x7b9d48: lsl             x1, x0, #2
    // 0x7b9d4c: mov             x0, x1
    // 0x7b9d50: ubfx            x0, x0, #0, #0x20
    // 0x7b9d54: and             x4, x0, x6
    // 0x7b9d58: sub             w0, w7, w4
    // 0x7b9d5c: and             x4, x0, x6
    // 0x7b9d60: ubfx            x4, x4, #0, #0x20
    // 0x7b9d64: add             x8, x1, x4
    // 0x7b9d68: r0 = BoxInt64Instr(r8)
    //     0x7b9d68: sbfiz           x0, x8, #1, #0x1f
    //     0x7b9d6c: cmp             x8, x0, asr #1
    //     0x7b9d70: b.eq            #0x7b9d7c
    //     0x7b9d74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b9d78: stur            x8, [x0, #7]
    // 0x7b9d7c: mov             x4, x0
    // 0x7b9d80: r0 = AllocateUint8Array()
    //     0x7b9d80: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b9d84: ldur            x4, [fp, #-0x20]
    // 0x7b9d88: r1 = LoadClassIdInstr(r4)
    //     0x7b9d88: ldur            x1, [x4, #-1]
    //     0x7b9d8c: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9d90: mov             x3, x0
    // 0x7b9d94: mov             x0, x1
    // 0x7b9d98: mov             x1, x4
    // 0x7b9d9c: r2 = "loca"
    //     0x7b9d9c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7b9da0: ldr             x2, [x2, #0x858]
    // 0x7b9da4: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9da4: sub             lr, x0, #0x10d
    //     0x7b9da8: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9dac: blr             lr
    // 0x7b9db0: ldur            x4, [fp, #-0x48]
    // 0x7b9db4: LoadField: r0 = r4->field_b
    //     0x7b9db4: ldur            w0, [x4, #0xb]
    // 0x7b9db8: r1 = LoadInt32Instr(r0)
    //     0x7b9db8: sbfx            x1, x0, #1, #0x1f
    // 0x7b9dbc: add             x0, x1, #1
    // 0x7b9dc0: lsl             x2, x0, #2
    // 0x7b9dc4: r0 = BoxInt64Instr(r2)
    //     0x7b9dc4: sbfiz           x0, x2, #1, #0x1f
    //     0x7b9dc8: cmp             x2, x0, asr #1
    //     0x7b9dcc: b.eq            #0x7b9dd8
    //     0x7b9dd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b9dd4: stur            x2, [x0, #7]
    // 0x7b9dd8: ldur            x5, [fp, #-0x28]
    // 0x7b9ddc: r1 = LoadClassIdInstr(r5)
    //     0x7b9ddc: ldur            x1, [x5, #-1]
    //     0x7b9de0: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9de4: mov             x3, x0
    // 0x7b9de8: mov             x0, x1
    // 0x7b9dec: mov             x1, x5
    // 0x7b9df0: r2 = "loca"
    //     0x7b9df0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7b9df4: ldr             x2, [x2, #0x858]
    // 0x7b9df8: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7b9df8: sub             lr, x0, #0x10d
    //     0x7b9dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9e00: blr             lr
    // 0x7b9e04: ldur            x4, [fp, #-0x20]
    // 0x7b9e08: ldur            x3, [fp, #-0x48]
    // 0x7b9e0c: ldur            x5, [fp, #-0x78]
    // 0x7b9e10: r0 = LoadClassIdInstr(r4)
    //     0x7b9e10: ldur            x0, [x4, #-1]
    //     0x7b9e14: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9e18: mov             x1, x4
    // 0x7b9e1c: r2 = "loca"
    //     0x7b9e1c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7b9e20: ldr             x2, [x2, #0x858]
    // 0x7b9e24: r0 = GDT[cid_x0 + -0x114]()
    //     0x7b9e24: sub             lr, x0, #0x114
    //     0x7b9e28: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9e2c: blr             lr
    // 0x7b9e30: cmp             w0, NULL
    // 0x7b9e34: b.eq            #0x7bbb90
    // 0x7b9e38: r1 = LoadClassIdInstr(r0)
    //     0x7b9e38: ldur            x1, [x0, #-1]
    //     0x7b9e3c: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9e40: mov             x16, x0
    // 0x7b9e44: mov             x0, x1
    // 0x7b9e48: mov             x1, x16
    // 0x7b9e4c: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7b9e4c: sub             lr, x0, #0xf60
    //     0x7b9e50: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9e54: blr             lr
    // 0x7b9e58: r1 = LoadClassIdInstr(r0)
    //     0x7b9e58: ldur            x1, [x0, #-1]
    //     0x7b9e5c: ubfx            x1, x1, #0xc, #0x14
    // 0x7b9e60: mov             x16, x0
    // 0x7b9e64: mov             x0, x1
    // 0x7b9e68: mov             x1, x16
    // 0x7b9e6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b9e6c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b9e70: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7b9e70: sub             lr, x0, #0xfff
    //     0x7b9e74: ldr             lr, [x21, lr, lsl #3]
    //     0x7b9e78: blr             lr
    // 0x7b9e7c: mov             x3, x0
    // 0x7b9e80: ldur            x0, [fp, #-0x48]
    // 0x7b9e84: stur            x3, [fp, #-0x70]
    // 0x7b9e88: LoadField: r1 = r0->field_b
    //     0x7b9e88: ldur            w1, [x0, #0xb]
    // 0x7b9e8c: r4 = LoadInt32Instr(r1)
    //     0x7b9e8c: sbfx            x4, x1, #1, #0x1f
    // 0x7b9e90: ldur            x5, [fp, #-0x78]
    // 0x7b9e94: stur            x4, [fp, #-0xc0]
    // 0x7b9e98: LoadField: r6 = r5->field_7
    //     0x7b9e98: ldur            w6, [x5, #7]
    // 0x7b9e9c: DecompressPointer r6
    //     0x7b9e9c: add             x6, x6, HEAP, lsl #32
    // 0x7b9ea0: stur            x6, [fp, #-0x68]
    // 0x7b9ea4: LoadField: r7 = r5->field_b
    //     0x7b9ea4: ldur            w7, [x5, #0xb]
    // 0x7b9ea8: DecompressPointer r7
    //     0x7b9ea8: add             x7, x7, HEAP, lsl #32
    // 0x7b9eac: stur            x7, [fp, #-0x60]
    // 0x7b9eb0: LoadField: r1 = r6->field_13
    //     0x7b9eb0: ldur            w1, [x6, #0x13]
    // 0x7b9eb4: r2 = LoadInt32Instr(r1)
    //     0x7b9eb4: sbfx            x2, x1, #1, #0x1f
    // 0x7b9eb8: sub             x8, x2, #1
    // 0x7b9ebc: stur            x8, [fp, #-0xb8]
    // 0x7b9ec0: ArrayLoad: r9 = r6[0]  ; List_4
    //     0x7b9ec0: ldur            w9, [x6, #0x17]
    // 0x7b9ec4: DecompressPointer r9
    //     0x7b9ec4: add             x9, x9, HEAP, lsl #32
    // 0x7b9ec8: stur            x9, [fp, #-0x38]
    // 0x7b9ecc: LoadField: r1 = r6->field_1b
    //     0x7b9ecc: ldur            w1, [x6, #0x1b]
    // 0x7b9ed0: r10 = LoadInt32Instr(r1)
    //     0x7b9ed0: sbfx            x10, x1, #1, #0x1f
    // 0x7b9ed4: stur            x10, [fp, #-0xb0]
    // 0x7b9ed8: r12 = 0
    //     0x7b9ed8: movz            x12, #0
    // 0x7b9edc: r11 = 0
    //     0x7b9edc: movz            x11, #0
    // 0x7b9ee0: r1 = 0
    //     0x7b9ee0: movz            x1, #0
    // 0x7b9ee4: stur            x12, [fp, #-0x80]
    // 0x7b9ee8: stur            x11, [fp, #-0x90]
    // 0x7b9eec: CheckStackOverflow
    //     0x7b9eec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b9ef0: cmp             SP, x16
    //     0x7b9ef4: b.ls            #0x7bbb94
    // 0x7b9ef8: LoadField: r2 = r0->field_b
    //     0x7b9ef8: ldur            w2, [x0, #0xb]
    // 0x7b9efc: r13 = LoadInt32Instr(r2)
    //     0x7b9efc: sbfx            x13, x2, #1, #0x1f
    // 0x7b9f00: cmp             x4, x13
    // 0x7b9f04: b.ne            #0x7bbabc
    // 0x7b9f08: cmp             x1, x13
    // 0x7b9f0c: b.ge            #0x7ba0f8
    // 0x7b9f10: LoadField: r2 = r0->field_f
    //     0x7b9f10: ldur            w2, [x0, #0xf]
    // 0x7b9f14: DecompressPointer r2
    //     0x7b9f14: add             x2, x2, HEAP, lsl #32
    // 0x7b9f18: ArrayLoad: r13 = r2[r1]  ; Unknown_4
    //     0x7b9f18: add             x16, x2, x1, lsl #2
    //     0x7b9f1c: ldur            w13, [x16, #0xf]
    // 0x7b9f20: DecompressPointer r13
    //     0x7b9f20: add             x13, x13, HEAP, lsl #32
    // 0x7b9f24: stur            x13, [fp, #-0x30]
    // 0x7b9f28: add             x14, x1, #1
    // 0x7b9f2c: mov             x1, x7
    // 0x7b9f30: stur            x14, [fp, #-0x58]
    // 0x7b9f34: r2 = "head"
    //     0x7b9f34: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7b9f38: ldr             x2, [x2, #0x890]
    // 0x7b9f3c: r0 = _getValueOrData()
    //     0x7b9f3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b9f40: ldur            x4, [fp, #-0x60]
    // 0x7b9f44: LoadField: r1 = r4->field_f
    //     0x7b9f44: ldur            w1, [x4, #0xf]
    // 0x7b9f48: DecompressPointer r1
    //     0x7b9f48: add             x1, x1, HEAP, lsl #32
    // 0x7b9f4c: cmp             w1, w0
    // 0x7b9f50: b.ne            #0x7b9f58
    // 0x7b9f54: r0 = Null
    //     0x7b9f54: mov             x0, NULL
    // 0x7b9f58: ldur            x5, [fp, #-0x38]
    // 0x7b9f5c: ldur            x6, [fp, #-0xb0]
    // 0x7b9f60: r10 = 65280
    //     0x7b9f60: orr             x10, xzr, #0xff00
    // 0x7b9f64: r9 = 255
    //     0x7b9f64: movz            x9, #0xff
    // 0x7b9f68: r8 = 32767
    //     0x7b9f68: orr             x8, xzr, #0x7fff
    // 0x7b9f6c: r7 = 32768
    //     0x7b9f6c: movz            x7, #0x8000
    // 0x7b9f70: cmp             w0, NULL
    // 0x7b9f74: b.eq            #0x7bbb9c
    // 0x7b9f78: r1 = LoadInt32Instr(r0)
    //     0x7b9f78: sbfx            x1, x0, #1, #0x1f
    //     0x7b9f7c: tbz             w0, #0, #0x7b9f84
    //     0x7b9f80: ldur            x1, [x0, #7]
    // 0x7b9f84: add             x2, x1, #0x32
    // 0x7b9f88: ldur            x0, [fp, #-0xb8]
    // 0x7b9f8c: mov             x1, x2
    // 0x7b9f90: cmp             x1, x0
    // 0x7b9f94: b.hs            #0x7bbba0
    // 0x7b9f98: add             x0, x6, x2
    // 0x7b9f9c: LoadField: r1 = r5->field_7
    //     0x7b9f9c: ldur            x1, [x5, #7]
    // 0x7b9fa0: ldrsh           x2, [x1, x0]
    // 0x7b9fa4: mov             x0, x2
    // 0x7b9fa8: ubfx            x0, x0, #0, #0x20
    // 0x7b9fac: and             x1, x0, x10
    // 0x7b9fb0: lsr             w0, w1, #8
    // 0x7b9fb4: ubfx            x2, x2, #0, #0x20
    // 0x7b9fb8: and             x1, x2, x9
    // 0x7b9fbc: lsl             w2, w1, #8
    // 0x7b9fc0: orr             x1, x0, x2
    // 0x7b9fc4: and             x0, x1, x8
    // 0x7b9fc8: and             x2, x1, x7
    // 0x7b9fcc: ubfx            x0, x0, #0, #0x20
    // 0x7b9fd0: ubfx            x2, x2, #0, #0x20
    // 0x7b9fd4: sub             x1, x0, x2
    // 0x7b9fd8: cbnz            x1, #0x7ba020
    // 0x7b9fdc: ldur            x11, [fp, #-0x70]
    // 0x7b9fe0: ldur            x14, [fp, #-0x80]
    // 0x7b9fe4: ldur            x13, [fp, #-0x90]
    // 0x7b9fe8: r12 = 2
    //     0x7b9fe8: movz            x12, #0x2
    // 0x7b9fec: sdiv            x3, x14, x12
    // 0x7b9ff0: r0 = LoadClassIdInstr(r11)
    //     0x7b9ff0: ldur            x0, [x11, #-1]
    //     0x7b9ff4: ubfx            x0, x0, #0xc, #0x14
    // 0x7b9ff8: mov             x1, x11
    // 0x7b9ffc: mov             x2, x13
    // 0x7ba000: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba000: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba004: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba004: sub             lr, x0, #1, lsl #12
    //     0x7ba008: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba00c: blr             lr
    // 0x7ba010: ldur            x4, [fp, #-0x90]
    // 0x7ba014: add             x0, x4, #2
    // 0x7ba018: mov             x11, x0
    // 0x7ba01c: b               #0x7ba058
    // 0x7ba020: ldur            x5, [fp, #-0x70]
    // 0x7ba024: ldur            x4, [fp, #-0x90]
    // 0x7ba028: r0 = LoadClassIdInstr(r5)
    //     0x7ba028: ldur            x0, [x5, #-1]
    //     0x7ba02c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba030: mov             x1, x5
    // 0x7ba034: mov             x2, x4
    // 0x7ba038: ldur            x3, [fp, #-0x80]
    // 0x7ba03c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba03c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba040: r0 = GDT[cid_x0 + -0xffa]()
    //     0x7ba040: sub             lr, x0, #0xffa
    //     0x7ba044: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba048: blr             lr
    // 0x7ba04c: ldur            x2, [fp, #-0x90]
    // 0x7ba050: add             x0, x2, #4
    // 0x7ba054: mov             x11, x0
    // 0x7ba058: ldur            x0, [fp, #-0x80]
    // 0x7ba05c: ldur            x1, [fp, #-0x30]
    // 0x7ba060: stur            x11, [fp, #-0xc8]
    // 0x7ba064: LoadField: r4 = r1->field_f
    //     0x7ba064: ldur            w4, [x1, #0xf]
    // 0x7ba068: DecompressPointer r4
    //     0x7ba068: add             x4, x4, HEAP, lsl #32
    // 0x7ba06c: ldur            x1, [fp, #-0x18]
    // 0x7ba070: mov             x2, x0
    // 0x7ba074: mov             x3, x4
    // 0x7ba078: stur            x4, [fp, #-0x88]
    // 0x7ba07c: r0 = setAll()
    //     0x7ba07c: bl              #0x7b7e30  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::setAll
    // 0x7ba080: ldur            x1, [fp, #-0x88]
    // 0x7ba084: r0 = LoadClassIdInstr(r1)
    //     0x7ba084: ldur            x0, [x1, #-1]
    //     0x7ba088: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba08c: r0 = GDT[cid_x0 + 0xcd4c]()
    //     0x7ba08c: movz            x17, #0xcd4c
    //     0x7ba090: add             lr, x0, x17
    //     0x7ba094: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba098: blr             lr
    // 0x7ba09c: ldur            x3, [fp, #-0x80]
    // 0x7ba0a0: add             x1, x3, x0
    // 0x7ba0a4: mov             x0, x1
    // 0x7ba0a8: ubfx            x0, x0, #0, #0x20
    // 0x7ba0ac: r4 = 3
    //     0x7ba0ac: movz            x4, #0x3
    // 0x7ba0b0: and             x2, x0, x4
    // 0x7ba0b4: r0 = 4
    //     0x7ba0b4: movz            x0, #0x4
    // 0x7ba0b8: sub             w3, w0, w2
    // 0x7ba0bc: and             x2, x3, x4
    // 0x7ba0c0: ubfx            x2, x2, #0, #0x20
    // 0x7ba0c4: add             x12, x1, x2
    // 0x7ba0c8: ldur            x11, [fp, #-0xc8]
    // 0x7ba0cc: ldur            x1, [fp, #-0x58]
    // 0x7ba0d0: ldur            x0, [fp, #-0x48]
    // 0x7ba0d4: ldur            x3, [fp, #-0x70]
    // 0x7ba0d8: ldur            x5, [fp, #-0x78]
    // 0x7ba0dc: ldur            x6, [fp, #-0x68]
    // 0x7ba0e0: ldur            x7, [fp, #-0x60]
    // 0x7ba0e4: ldur            x8, [fp, #-0xb8]
    // 0x7ba0e8: ldur            x9, [fp, #-0x38]
    // 0x7ba0ec: ldur            x4, [fp, #-0xc0]
    // 0x7ba0f0: ldur            x10, [fp, #-0xb0]
    // 0x7ba0f4: b               #0x7b9ee4
    // 0x7ba0f8: mov             x3, x12
    // 0x7ba0fc: mov             x2, x11
    // 0x7ba100: r4 = 3
    //     0x7ba100: movz            x4, #0x3
    // 0x7ba104: r0 = 4
    //     0x7ba104: movz            x0, #0x4
    // 0x7ba108: ldur            x1, [fp, #-0x78]
    // 0x7ba10c: r0 = indexToLocFormat()
    //     0x7ba10c: bl              #0x7bc74c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::indexToLocFormat
    // 0x7ba110: cbnz            x0, #0x7ba14c
    // 0x7ba114: ldur            x1, [fp, #-0x70]
    // 0x7ba118: ldur            x3, [fp, #-0x80]
    // 0x7ba11c: r4 = 2
    //     0x7ba11c: movz            x4, #0x2
    // 0x7ba120: sdiv            x0, x3, x4
    // 0x7ba124: r2 = LoadClassIdInstr(r1)
    //     0x7ba124: ldur            x2, [x1, #-1]
    //     0x7ba128: ubfx            x2, x2, #0xc, #0x14
    // 0x7ba12c: mov             x3, x0
    // 0x7ba130: mov             x0, x2
    // 0x7ba134: ldur            x2, [fp, #-0x90]
    // 0x7ba138: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba138: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba13c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba13c: sub             lr, x0, #1, lsl #12
    //     0x7ba140: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba144: blr             lr
    // 0x7ba148: b               #0x7ba170
    // 0x7ba14c: ldur            x1, [fp, #-0x70]
    // 0x7ba150: ldur            x3, [fp, #-0x80]
    // 0x7ba154: r0 = LoadClassIdInstr(r1)
    //     0x7ba154: ldur            x0, [x1, #-1]
    //     0x7ba158: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba15c: ldur            x2, [fp, #-0x90]
    // 0x7ba160: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba160: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba164: r0 = GDT[cid_x0 + -0xffa]()
    //     0x7ba164: sub             lr, x0, #0xffa
    //     0x7ba168: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba16c: blr             lr
    // 0x7ba170: ldur            x0, [fp, #-0x78]
    // 0x7ba174: ldur            x3, [fp, #-0x40]
    // 0x7ba178: ldur            x2, [fp, #-0x50]
    // 0x7ba17c: r1 = <String>
    //     0x7ba17c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7ba180: r0 = _Set()
    //     0x7ba180: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x7ba184: mov             x3, x0
    // 0x7ba188: ldur            x0, [fp, #-0x40]
    // 0x7ba18c: stur            x3, [fp, #-0x18]
    // 0x7ba190: StoreField: r3->field_1b = r0
    //     0x7ba190: stur            w0, [x3, #0x1b]
    // 0x7ba194: StoreField: r3->field_b = rZR
    //     0x7ba194: stur            wzr, [x3, #0xb]
    // 0x7ba198: ldur            x0, [fp, #-0x50]
    // 0x7ba19c: StoreField: r3->field_f = r0
    //     0x7ba19c: stur            w0, [x3, #0xf]
    // 0x7ba1a0: StoreField: r3->field_13 = rZR
    //     0x7ba1a0: stur            wzr, [x3, #0x13]
    // 0x7ba1a4: ArrayStore: r3[0] = rZR  ; List_4
    //     0x7ba1a4: stur            wzr, [x3, #0x17]
    // 0x7ba1a8: mov             x1, x3
    // 0x7ba1ac: r2 = "head"
    //     0x7ba1ac: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7ba1b0: ldr             x2, [x2, #0x890]
    // 0x7ba1b4: r0 = add()
    //     0x7ba1b4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x7ba1b8: ldur            x1, [fp, #-0x18]
    // 0x7ba1bc: r2 = "maxp"
    //     0x7ba1bc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0x7ba1c0: ldr             x2, [x2, #0x880]
    // 0x7ba1c4: r0 = add()
    //     0x7ba1c4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x7ba1c8: ldur            x1, [fp, #-0x18]
    // 0x7ba1cc: r2 = "hhea"
    //     0x7ba1cc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0x7ba1d0: ldr             x2, [x2, #0x888]
    // 0x7ba1d4: r0 = add()
    //     0x7ba1d4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x7ba1d8: ldur            x1, [fp, #-0x18]
    // 0x7ba1dc: r2 = "OS/2"
    //     0x7ba1dc: add             x2, PP, #0x47, lsl #12  ; [pp+0x47750] "OS/2"
    //     0x7ba1e0: ldr             x2, [x2, #0x750]
    // 0x7ba1e4: r0 = add()
    //     0x7ba1e4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x7ba1e8: ldur            x1, [fp, #-0x18]
    // 0x7ba1ec: r0 = iterator()
    //     0x7ba1ec: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x7ba1f0: mov             x2, x0
    // 0x7ba1f4: ldur            x0, [fp, #-0x78]
    // 0x7ba1f8: stur            x2, [fp, #-0x38]
    // 0x7ba1fc: LoadField: r3 = r0->field_f
    //     0x7ba1fc: ldur            w3, [x0, #0xf]
    // 0x7ba200: DecompressPointer r3
    //     0x7ba200: add             x3, x3, HEAP, lsl #32
    // 0x7ba204: stur            x3, [fp, #-0x30]
    // 0x7ba208: mov             x4, THR
    // 0x7ba20c: stur            x4, [fp, #-0x58]
    // 0x7ba210: LoadField: r5 = r2->field_7
    //     0x7ba210: ldur            w5, [x2, #7]
    // 0x7ba214: DecompressPointer r5
    //     0x7ba214: add             x5, x5, HEAP, lsl #32
    // 0x7ba218: stur            x5, [fp, #-0x18]
    // 0x7ba21c: ldur            x8, [fp, #-0x20]
    // 0x7ba220: ldur            x7, [fp, #-0x68]
    // 0x7ba224: ldur            x6, [fp, #-0x60]
    // 0x7ba228: CheckStackOverflow
    //     0x7ba228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ba22c: cmp             SP, x16
    //     0x7ba230: b.ls            #0x7bbba4
    // 0x7ba234: mov             x1, x2
    // 0x7ba238: r0 = moveNext()
    //     0x7ba238: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x7ba23c: tbnz            w0, #4, #0x7ba5f0
    // 0x7ba240: ldur            x3, [fp, #-0x38]
    // 0x7ba244: LoadField: r4 = r3->field_33
    //     0x7ba244: ldur            w4, [x3, #0x33]
    // 0x7ba248: DecompressPointer r4
    //     0x7ba248: add             x4, x4, HEAP, lsl #32
    // 0x7ba24c: stur            x4, [fp, #-0x40]
    // 0x7ba250: cmp             w4, NULL
    // 0x7ba254: b.ne            #0x7ba288
    // 0x7ba258: mov             x0, x4
    // 0x7ba25c: ldur            x2, [fp, #-0x18]
    // 0x7ba260: r1 = Null
    //     0x7ba260: mov             x1, NULL
    // 0x7ba264: cmp             w2, NULL
    // 0x7ba268: b.eq            #0x7ba288
    // 0x7ba26c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ba26c: ldur            w4, [x2, #0x17]
    // 0x7ba270: DecompressPointer r4
    //     0x7ba270: add             x4, x4, HEAP, lsl #32
    // 0x7ba274: r8 = X0
    //     0x7ba274: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7ba278: LoadField: r9 = r4->field_7
    //     0x7ba278: ldur            x9, [x4, #7]
    // 0x7ba27c: r3 = Null
    //     0x7ba27c: add             x3, PP, #0x47, lsl #12  ; [pp+0x47758] Null
    //     0x7ba280: ldr             x3, [x3, #0x758]
    // 0x7ba284: blr             x9
    // 0x7ba288: ldur            x0, [fp, #-0x60]
    // 0x7ba28c: mov             x1, x0
    // 0x7ba290: ldur            x2, [fp, #-0x40]
    // 0x7ba294: r0 = _getValueOrData()
    //     0x7ba294: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ba298: mov             x1, x0
    // 0x7ba29c: ldur            x0, [fp, #-0x60]
    // 0x7ba2a0: LoadField: r2 = r0->field_f
    //     0x7ba2a0: ldur            w2, [x0, #0xf]
    // 0x7ba2a4: DecompressPointer r2
    //     0x7ba2a4: add             x2, x2, HEAP, lsl #32
    // 0x7ba2a8: cmp             w2, w1
    // 0x7ba2ac: b.ne            #0x7ba2b8
    // 0x7ba2b0: r3 = Null
    //     0x7ba2b0: mov             x3, NULL
    // 0x7ba2b4: b               #0x7ba2bc
    // 0x7ba2b8: mov             x3, x1
    // 0x7ba2bc: stur            x3, [fp, #-0x50]
    // 0x7ba2c0: cmp             w3, NULL
    // 0x7ba2c4: b.eq            #0x7ba5d8
    // 0x7ba2c8: ldur            x4, [fp, #-0x30]
    // 0x7ba2cc: mov             x1, x4
    // 0x7ba2d0: ldur            x2, [fp, #-0x40]
    // 0x7ba2d4: r0 = _getValueOrData()
    //     0x7ba2d4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ba2d8: ldur            x2, [fp, #-0x30]
    // 0x7ba2dc: LoadField: r1 = r2->field_f
    //     0x7ba2dc: ldur            w1, [x2, #0xf]
    // 0x7ba2e0: DecompressPointer r1
    //     0x7ba2e0: add             x1, x1, HEAP, lsl #32
    // 0x7ba2e4: cmp             w1, w0
    // 0x7ba2e8: b.ne            #0x7ba2f4
    // 0x7ba2ec: r4 = Null
    //     0x7ba2ec: mov             x4, NULL
    // 0x7ba2f0: b               #0x7ba2f8
    // 0x7ba2f4: mov             x4, x0
    // 0x7ba2f8: ldur            x3, [fp, #-0x68]
    // 0x7ba2fc: stur            x4, [fp, #-0x70]
    // 0x7ba300: cmp             w4, NULL
    // 0x7ba304: b.eq            #0x7bbbac
    // 0x7ba308: r0 = LoadClassIdInstr(r3)
    //     0x7ba308: ldur            x0, [x3, #-1]
    //     0x7ba30c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba310: mov             x1, x3
    // 0x7ba314: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7ba314: sub             lr, x0, #0xf60
    //     0x7ba318: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba31c: blr             lr
    // 0x7ba320: mov             x2, x0
    // 0x7ba324: ldur            x3, [fp, #-0x70]
    // 0x7ba328: r0 = LoadInt32Instr(r3)
    //     0x7ba328: sbfx            x0, x3, #1, #0x1f
    //     0x7ba32c: tbz             w3, #0, #0x7ba334
    //     0x7ba330: ldur            x0, [x3, #7]
    // 0x7ba334: mov             x1, x0
    // 0x7ba338: ubfx            x1, x1, #0, #0x20
    // 0x7ba33c: r4 = 3
    //     0x7ba33c: movz            x4, #0x3
    // 0x7ba340: and             x5, x1, x4
    // 0x7ba344: r6 = 4
    //     0x7ba344: movz            x6, #0x4
    // 0x7ba348: sub             w1, w6, w5
    // 0x7ba34c: and             x5, x1, x4
    // 0x7ba350: ubfx            x5, x5, #0, #0x20
    // 0x7ba354: add             x7, x0, x5
    // 0x7ba358: r0 = BoxInt64Instr(r7)
    //     0x7ba358: sbfiz           x0, x7, #1, #0x1f
    //     0x7ba35c: cmp             x7, x0, asr #1
    //     0x7ba360: b.eq            #0x7ba36c
    //     0x7ba364: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7ba368: stur            x7, [x0, #7]
    // 0x7ba36c: r1 = LoadClassIdInstr(r2)
    //     0x7ba36c: ldur            x1, [x2, #-1]
    //     0x7ba370: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba374: ldur            x16, [fp, #-0x50]
    // 0x7ba378: stp             x0, x16, [SP]
    // 0x7ba37c: mov             x0, x1
    // 0x7ba380: mov             x1, x2
    // 0x7ba384: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7ba384: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7ba388: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba388: sub             lr, x0, #1, lsl #12
    //     0x7ba38c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba390: blr             lr
    // 0x7ba394: stur            x0, [fp, #-0x88]
    // 0x7ba398: LoadField: r1 = r0->field_13
    //     0x7ba398: ldur            w1, [x0, #0x13]
    // 0x7ba39c: mov             x4, x1
    // 0x7ba3a0: stur            x1, [fp, #-0x50]
    // 0x7ba3a4: r0 = AllocateUint8Array()
    //     0x7ba3a4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7ba3a8: mov             x4, x0
    // 0x7ba3ac: ldur            x0, [fp, #-0x50]
    // 0x7ba3b0: stur            x4, [fp, #-0x98]
    // 0x7ba3b4: r5 = LoadInt32Instr(r0)
    //     0x7ba3b4: sbfx            x5, x0, #1, #0x1f
    // 0x7ba3b8: stur            x5, [fp, #-0x80]
    // 0x7ba3bc: tbz             x5, #0x3f, #0x7ba3d4
    // 0x7ba3c0: mov             x2, x0
    // 0x7ba3c4: mov             x3, x5
    // 0x7ba3c8: r1 = 0
    //     0x7ba3c8: movz            x1, #0
    // 0x7ba3cc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba3cc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba3d0: r0 = checkValidRange()
    //     0x7ba3d0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x7ba3d4: ldur            x2, [fp, #-0x88]
    // 0x7ba3d8: r0 = LoadClassIdInstr(r2)
    //     0x7ba3d8: ldur            x0, [x2, #-1]
    //     0x7ba3dc: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba3e0: mov             x1, x2
    // 0x7ba3e4: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x7ba3e4: movz            x17, #0xd16b
    //     0x7ba3e8: add             lr, x0, x17
    //     0x7ba3ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba3f0: blr             lr
    // 0x7ba3f4: cmp             x0, #1
    // 0x7ba3f8: b.ne            #0x7ba570
    // 0x7ba3fc: ldur            x5, [fp, #-0x88]
    // 0x7ba400: ldur            x2, [fp, #-0x80]
    // 0x7ba404: r0 = LoadClassIdInstr(r5)
    //     0x7ba404: ldur            x0, [x5, #-1]
    //     0x7ba408: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba40c: str             x5, [SP]
    // 0x7ba410: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7ba410: movz            x17, #0xc834
    //     0x7ba414: add             lr, x0, x17
    //     0x7ba418: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba41c: blr             lr
    // 0x7ba420: r1 = LoadInt32Instr(r0)
    //     0x7ba420: sbfx            x1, x0, #1, #0x1f
    //     0x7ba424: tbz             w0, #0, #0x7ba42c
    //     0x7ba428: ldur            x1, [x0, #7]
    // 0x7ba42c: ldur            x2, [fp, #-0x80]
    // 0x7ba430: cmp             x1, x2
    // 0x7ba434: b.lt            #0x7bbab0
    // 0x7ba438: cbz             x2, #0x7ba594
    // 0x7ba43c: ldur            x0, [fp, #-0x50]
    // 0x7ba440: cmp             w0, #0x800
    // 0x7ba444: b.ge            #0x7ba520
    // 0x7ba448: ldur            x5, [fp, #-0x88]
    // 0x7ba44c: ldur            x20, [fp, #-0x98]
    // 0x7ba450: LoadField: r1 = r5->field_7
    //     0x7ba450: ldur            x1, [x5, #7]
    // 0x7ba454: mov             x3, x0
    // 0x7ba458: mov             x2, x1
    // 0x7ba45c: add             x0, x20, #0x17
    // 0x7ba460: cbz             x3, #0x7ba518
    // 0x7ba464: cmp             x0, x2
    // 0x7ba468: b.ls            #0x7ba4d0
    // 0x7ba46c: sxtw            x3, w3
    // 0x7ba470: add             x16, x2, x3, asr #1
    // 0x7ba474: cmp             x0, x16
    // 0x7ba478: b.hs            #0x7ba4d0
    // 0x7ba47c: mov             x2, x16
    // 0x7ba480: add             x0, x0, x3, asr #1
    // 0x7ba484: tbz             w3, #4, #0x7ba490
    // 0x7ba488: ldr             x16, [x2, #-8]!
    // 0x7ba48c: str             x16, [x0, #-8]!
    // 0x7ba490: tbz             w3, #3, #0x7ba49c
    // 0x7ba494: ldr             w16, [x2, #-4]!
    // 0x7ba498: str             w16, [x0, #-4]!
    // 0x7ba49c: tbz             w3, #2, #0x7ba4a8
    // 0x7ba4a0: ldrh            w16, [x2, #-2]!
    // 0x7ba4a4: strh            w16, [x0, #-2]!
    // 0x7ba4a8: tbz             w3, #1, #0x7ba4b4
    // 0x7ba4ac: ldrb            w16, [x2, #-1]!
    // 0x7ba4b0: strb            w16, [x0, #-1]!
    // 0x7ba4b4: ands            w3, w3, #0xffffffe1
    // 0x7ba4b8: b.eq            #0x7ba518
    // 0x7ba4bc: ldp             x16, x17, [x2, #-0x10]!
    // 0x7ba4c0: stp             x16, x17, [x0, #-0x10]!
    // 0x7ba4c4: subs            w3, w3, #0x20
    // 0x7ba4c8: b.ne            #0x7ba4bc
    // 0x7ba4cc: b               #0x7ba518
    // 0x7ba4d0: tbz             w3, #4, #0x7ba4dc
    // 0x7ba4d4: ldr             x16, [x2], #8
    // 0x7ba4d8: str             x16, [x0], #8
    // 0x7ba4dc: tbz             w3, #3, #0x7ba4e8
    // 0x7ba4e0: ldr             w16, [x2], #4
    // 0x7ba4e4: str             w16, [x0], #4
    // 0x7ba4e8: tbz             w3, #2, #0x7ba4f4
    // 0x7ba4ec: ldrh            w16, [x2], #2
    // 0x7ba4f0: strh            w16, [x0], #2
    // 0x7ba4f4: tbz             w3, #1, #0x7ba500
    // 0x7ba4f8: ldrb            w16, [x2], #1
    // 0x7ba4fc: strb            w16, [x0], #1
    // 0x7ba500: ands            w3, w3, #0xffffffe1
    // 0x7ba504: b.eq            #0x7ba518
    // 0x7ba508: ldp             x16, x17, [x2], #0x10
    // 0x7ba50c: stp             x16, x17, [x0], #0x10
    // 0x7ba510: subs            w3, w3, #0x20
    // 0x7ba514: b.ne            #0x7ba508
    // 0x7ba518: ldur            x23, [fp, #-0x58]
    // 0x7ba51c: b               #0x7ba594
    // 0x7ba520: ldur            x5, [fp, #-0x88]
    // 0x7ba524: ldur            x20, [fp, #-0x98]
    // 0x7ba528: ldur            x23, [fp, #-0x58]
    // 0x7ba52c: LoadField: r0 = r20->field_7
    //     0x7ba52c: ldur            x0, [x20, #7]
    // 0x7ba530: LoadField: r1 = r5->field_7
    //     0x7ba530: ldur            x1, [x5, #7]
    // 0x7ba534: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x7ba534: ldr             x9, [x23, #0x658]
    //     0x7ba538: mov             x17, fp
    //     0x7ba53c: str             fp, [SP, #-8]!
    //     0x7ba540: mov             fp, SP
    //     0x7ba544: and             SP, SP, #0xfffffffffffffff0
    //     0x7ba548: mov             x19, sp
    //     0x7ba54c: mov             sp, SP
    //     0x7ba550: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7ba554: blr             x9
    //     0x7ba558: movz            x16, #0x8
    //     0x7ba55c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7ba560: mov             sp, x19
    //     0x7ba564: mov             SP, fp
    //     0x7ba568: ldr             fp, [SP], #8
    // 0x7ba56c: b               #0x7ba594
    // 0x7ba570: ldur            x5, [fp, #-0x88]
    // 0x7ba574: ldur            x20, [fp, #-0x98]
    // 0x7ba578: ldur            x23, [fp, #-0x58]
    // 0x7ba57c: ldur            x2, [fp, #-0x80]
    // 0x7ba580: mov             x1, x20
    // 0x7ba584: mov             x3, x2
    // 0x7ba588: r2 = 0
    //     0x7ba588: movz            x2, #0
    // 0x7ba58c: r6 = 0
    //     0x7ba58c: movz            x6, #0
    // 0x7ba590: r0 = _slowSetRange()
    //     0x7ba590: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x7ba594: ldur            x4, [fp, #-0x20]
    // 0x7ba598: r0 = LoadClassIdInstr(r4)
    //     0x7ba598: ldur            x0, [x4, #-1]
    //     0x7ba59c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba5a0: mov             x1, x4
    // 0x7ba5a4: ldur            x2, [fp, #-0x40]
    // 0x7ba5a8: ldur            x3, [fp, #-0x98]
    // 0x7ba5ac: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7ba5ac: sub             lr, x0, #0x10d
    //     0x7ba5b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba5b4: blr             lr
    // 0x7ba5b8: ldur            x1, [fp, #-0x28]
    // 0x7ba5bc: ldur            x2, [fp, #-0x40]
    // 0x7ba5c0: r0 = _hashCode()
    //     0x7ba5c0: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7ba5c4: ldur            x1, [fp, #-0x28]
    // 0x7ba5c8: ldur            x2, [fp, #-0x40]
    // 0x7ba5cc: ldur            x3, [fp, #-0x70]
    // 0x7ba5d0: mov             x5, x0
    // 0x7ba5d4: r0 = _set()
    //     0x7ba5d4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7ba5d8: ldur            x2, [fp, #-0x38]
    // 0x7ba5dc: ldur            x3, [fp, #-0x30]
    // 0x7ba5e0: ldur            x0, [fp, #-0x78]
    // 0x7ba5e4: ldur            x5, [fp, #-0x18]
    // 0x7ba5e8: ldur            x4, [fp, #-0x58]
    // 0x7ba5ec: b               #0x7ba21c
    // 0x7ba5f0: ldur            x3, [fp, #-0x20]
    // 0x7ba5f4: ldur            x5, [fp, #-0x48]
    // 0x7ba5f8: ldur            x4, [fp, #-0x60]
    // 0x7ba5fc: r0 = LoadClassIdInstr(r3)
    //     0x7ba5fc: ldur            x0, [x3, #-1]
    //     0x7ba600: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba604: mov             x1, x3
    // 0x7ba608: r2 = "head"
    //     0x7ba608: add             x2, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7ba60c: ldr             x2, [x2, #0x890]
    // 0x7ba610: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ba610: sub             lr, x0, #0x114
    //     0x7ba614: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba618: blr             lr
    // 0x7ba61c: cmp             w0, NULL
    // 0x7ba620: b.eq            #0x7bbbb0
    // 0x7ba624: r1 = LoadClassIdInstr(r0)
    //     0x7ba624: ldur            x1, [x0, #-1]
    //     0x7ba628: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba62c: mov             x16, x0
    // 0x7ba630: mov             x0, x1
    // 0x7ba634: mov             x1, x16
    // 0x7ba638: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7ba638: sub             lr, x0, #0xf60
    //     0x7ba63c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba640: blr             lr
    // 0x7ba644: r1 = LoadClassIdInstr(r0)
    //     0x7ba644: ldur            x1, [x0, #-1]
    //     0x7ba648: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba64c: mov             x16, x0
    // 0x7ba650: mov             x0, x1
    // 0x7ba654: mov             x1, x16
    // 0x7ba658: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ba658: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ba65c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7ba65c: sub             lr, x0, #0xfff
    //     0x7ba660: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba664: blr             lr
    // 0x7ba668: r1 = LoadClassIdInstr(r0)
    //     0x7ba668: ldur            x1, [x0, #-1]
    //     0x7ba66c: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba670: mov             x16, x0
    // 0x7ba674: mov             x0, x1
    // 0x7ba678: mov             x1, x16
    // 0x7ba67c: r2 = 8
    //     0x7ba67c: movz            x2, #0x8
    // 0x7ba680: r3 = 0
    //     0x7ba680: movz            x3, #0
    // 0x7ba684: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba684: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba688: r0 = GDT[cid_x0 + -0xffa]()
    //     0x7ba688: sub             lr, x0, #0xffa
    //     0x7ba68c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba690: blr             lr
    // 0x7ba694: ldur            x3, [fp, #-0x20]
    // 0x7ba698: r0 = LoadClassIdInstr(r3)
    //     0x7ba698: ldur            x0, [x3, #-1]
    //     0x7ba69c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba6a0: mov             x1, x3
    // 0x7ba6a4: r2 = "maxp"
    //     0x7ba6a4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0x7ba6a8: ldr             x2, [x2, #0x880]
    // 0x7ba6ac: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ba6ac: sub             lr, x0, #0x114
    //     0x7ba6b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba6b4: blr             lr
    // 0x7ba6b8: cmp             w0, NULL
    // 0x7ba6bc: b.eq            #0x7bbbb4
    // 0x7ba6c0: r1 = LoadClassIdInstr(r0)
    //     0x7ba6c0: ldur            x1, [x0, #-1]
    //     0x7ba6c4: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba6c8: mov             x16, x0
    // 0x7ba6cc: mov             x0, x1
    // 0x7ba6d0: mov             x1, x16
    // 0x7ba6d4: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7ba6d4: sub             lr, x0, #0xf60
    //     0x7ba6d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba6dc: blr             lr
    // 0x7ba6e0: r1 = LoadClassIdInstr(r0)
    //     0x7ba6e0: ldur            x1, [x0, #-1]
    //     0x7ba6e4: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba6e8: mov             x16, x0
    // 0x7ba6ec: mov             x0, x1
    // 0x7ba6f0: mov             x1, x16
    // 0x7ba6f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ba6f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ba6f8: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7ba6f8: sub             lr, x0, #0xfff
    //     0x7ba6fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba700: blr             lr
    // 0x7ba704: ldur            x4, [fp, #-0x48]
    // 0x7ba708: LoadField: r1 = r4->field_b
    //     0x7ba708: ldur            w1, [x4, #0xb]
    // 0x7ba70c: r3 = LoadInt32Instr(r1)
    //     0x7ba70c: sbfx            x3, x1, #1, #0x1f
    // 0x7ba710: r1 = LoadClassIdInstr(r0)
    //     0x7ba710: ldur            x1, [x0, #-1]
    //     0x7ba714: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba718: mov             x16, x0
    // 0x7ba71c: mov             x0, x1
    // 0x7ba720: mov             x1, x16
    // 0x7ba724: r2 = 4
    //     0x7ba724: movz            x2, #0x4
    // 0x7ba728: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba728: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba72c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba72c: sub             lr, x0, #1, lsl #12
    //     0x7ba730: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba734: blr             lr
    // 0x7ba738: ldur            x3, [fp, #-0x20]
    // 0x7ba73c: r0 = LoadClassIdInstr(r3)
    //     0x7ba73c: ldur            x0, [x3, #-1]
    //     0x7ba740: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba744: mov             x1, x3
    // 0x7ba748: r2 = "hhea"
    //     0x7ba748: add             x2, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0x7ba74c: ldr             x2, [x2, #0x888]
    // 0x7ba750: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ba750: sub             lr, x0, #0x114
    //     0x7ba754: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba758: blr             lr
    // 0x7ba75c: cmp             w0, NULL
    // 0x7ba760: b.eq            #0x7bbbb8
    // 0x7ba764: r1 = LoadClassIdInstr(r0)
    //     0x7ba764: ldur            x1, [x0, #-1]
    //     0x7ba768: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba76c: mov             x16, x0
    // 0x7ba770: mov             x0, x1
    // 0x7ba774: mov             x1, x16
    // 0x7ba778: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7ba778: sub             lr, x0, #0xf60
    //     0x7ba77c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba780: blr             lr
    // 0x7ba784: r1 = LoadClassIdInstr(r0)
    //     0x7ba784: ldur            x1, [x0, #-1]
    //     0x7ba788: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba78c: mov             x16, x0
    // 0x7ba790: mov             x0, x1
    // 0x7ba794: mov             x1, x16
    // 0x7ba798: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ba798: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ba79c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7ba79c: sub             lr, x0, #0xfff
    //     0x7ba7a0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba7a4: blr             lr
    // 0x7ba7a8: ldur            x4, [fp, #-0x48]
    // 0x7ba7ac: LoadField: r1 = r4->field_b
    //     0x7ba7ac: ldur            w1, [x4, #0xb]
    // 0x7ba7b0: r3 = LoadInt32Instr(r1)
    //     0x7ba7b0: sbfx            x3, x1, #1, #0x1f
    // 0x7ba7b4: r1 = LoadClassIdInstr(r0)
    //     0x7ba7b4: ldur            x1, [x0, #-1]
    //     0x7ba7b8: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba7bc: mov             x16, x0
    // 0x7ba7c0: mov             x0, x1
    // 0x7ba7c4: mov             x1, x16
    // 0x7ba7c8: r2 = 34
    //     0x7ba7c8: movz            x2, #0x22
    // 0x7ba7cc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ba7cc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ba7d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba7d0: sub             lr, x0, #1, lsl #12
    //     0x7ba7d4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba7d8: blr             lr
    // 0x7ba7dc: ldur            x1, [fp, #-0x60]
    // 0x7ba7e0: r2 = "post"
    //     0x7ba7e0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47768] "post"
    //     0x7ba7e4: ldr             x2, [x2, #0x768]
    // 0x7ba7e8: r0 = _getValueOrData()
    //     0x7ba7e8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ba7ec: ldur            x2, [fp, #-0x60]
    // 0x7ba7f0: LoadField: r1 = r2->field_f
    //     0x7ba7f0: ldur            w1, [x2, #0xf]
    // 0x7ba7f4: DecompressPointer r1
    //     0x7ba7f4: add             x1, x1, HEAP, lsl #32
    // 0x7ba7f8: cmp             w1, w0
    // 0x7ba7fc: b.ne            #0x7ba808
    // 0x7ba800: r7 = Null
    //     0x7ba800: mov             x7, NULL
    // 0x7ba804: b               #0x7ba80c
    // 0x7ba808: mov             x7, x0
    // 0x7ba80c: ldur            x4, [fp, #-0x20]
    // 0x7ba810: ldur            x6, [fp, #-0x28]
    // 0x7ba814: ldur            x3, [fp, #-0x48]
    // 0x7ba818: ldur            x5, [fp, #-0x68]
    // 0x7ba81c: stur            x7, [fp, #-0x18]
    // 0x7ba820: cmp             w7, NULL
    // 0x7ba824: b.eq            #0x7bbbbc
    // 0x7ba828: r0 = LoadClassIdInstr(r5)
    //     0x7ba828: ldur            x0, [x5, #-1]
    //     0x7ba82c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba830: mov             x1, x5
    // 0x7ba834: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7ba834: sub             lr, x0, #0xf60
    //     0x7ba838: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba83c: blr             lr
    // 0x7ba840: r1 = LoadClassIdInstr(r0)
    //     0x7ba840: ldur            x1, [x0, #-1]
    //     0x7ba844: ubfx            x1, x1, #0xc, #0x14
    // 0x7ba848: ldur            x16, [fp, #-0x18]
    // 0x7ba84c: r30 = 64
    //     0x7ba84c: movz            lr, #0x40
    // 0x7ba850: stp             lr, x16, [SP]
    // 0x7ba854: mov             x16, x0
    // 0x7ba858: mov             x0, x1
    // 0x7ba85c: mov             x1, x16
    // 0x7ba860: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7ba860: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7ba864: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ba864: sub             lr, x0, #1, lsl #12
    //     0x7ba868: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba86c: blr             lr
    // 0x7ba870: mov             x2, x0
    // 0x7ba874: r1 = Null
    //     0x7ba874: mov             x1, NULL
    // 0x7ba878: r0 = Uint8List.fromList()
    //     0x7ba878: bl              #0x6b9248  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x7ba87c: stur            x0, [fp, #-0x18]
    // 0x7ba880: r0 = _ByteBuffer()
    //     0x7ba880: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7ba884: ldur            x3, [fp, #-0x18]
    // 0x7ba888: StoreField: r0->field_7 = r3
    //     0x7ba888: stur            w3, [x0, #7]
    // 0x7ba88c: mov             x1, x0
    // 0x7ba890: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ba890: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ba894: r0 = asByteData()
    //     0x7ba894: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7ba898: mov             x2, x0
    // 0x7ba89c: LoadField: r0 = r2->field_13
    //     0x7ba89c: ldur            w0, [x2, #0x13]
    // 0x7ba8a0: r1 = LoadInt32Instr(r0)
    //     0x7ba8a0: sbfx            x1, x0, #1, #0x1f
    // 0x7ba8a4: sub             x0, x1, #3
    // 0x7ba8a8: r1 = 0
    //     0x7ba8a8: movz            x1, #0
    // 0x7ba8ac: cmp             x1, x0
    // 0x7ba8b0: b.hs            #0x7bbbc0
    // 0x7ba8b4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x7ba8b4: ldur            w0, [x2, #0x17]
    // 0x7ba8b8: DecompressPointer r0
    //     0x7ba8b8: add             x0, x0, HEAP, lsl #32
    // 0x7ba8bc: LoadField: r1 = r2->field_1b
    //     0x7ba8bc: ldur            w1, [x2, #0x1b]
    // 0x7ba8c0: LoadField: r2 = r0->field_7
    //     0x7ba8c0: ldur            x2, [x0, #7]
    // 0x7ba8c4: r0 = 768
    //     0x7ba8c4: movz            x0, #0x300
    // 0x7ba8c8: asr             w3, w1, #1
    // 0x7ba8cc: add             x3, x2, w3, sxtw
    // 0x7ba8d0: str             w0, [x3]
    // 0x7ba8d4: ldur            x4, [fp, #-0x20]
    // 0x7ba8d8: r0 = LoadClassIdInstr(r4)
    //     0x7ba8d8: ldur            x0, [x4, #-1]
    //     0x7ba8dc: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba8e0: mov             x1, x4
    // 0x7ba8e4: ldur            x3, [fp, #-0x18]
    // 0x7ba8e8: r2 = "post"
    //     0x7ba8e8: add             x2, PP, #0x47, lsl #12  ; [pp+0x47768] "post"
    //     0x7ba8ec: ldr             x2, [x2, #0x768]
    // 0x7ba8f0: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7ba8f0: sub             lr, x0, #0x10d
    //     0x7ba8f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba8f8: blr             lr
    // 0x7ba8fc: ldur            x4, [fp, #-0x28]
    // 0x7ba900: r0 = LoadClassIdInstr(r4)
    //     0x7ba900: ldur            x0, [x4, #-1]
    //     0x7ba904: ubfx            x0, x0, #0xc, #0x14
    // 0x7ba908: mov             x1, x4
    // 0x7ba90c: r2 = "post"
    //     0x7ba90c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47768] "post"
    //     0x7ba910: ldr             x2, [x2, #0x768]
    // 0x7ba914: r3 = 64
    //     0x7ba914: movz            x3, #0x40
    // 0x7ba918: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7ba918: sub             lr, x0, #0x10d
    //     0x7ba91c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ba920: blr             lr
    // 0x7ba924: ldur            x0, [fp, #-0x48]
    // 0x7ba928: LoadField: r1 = r0->field_b
    //     0x7ba928: ldur            w1, [x0, #0xb]
    // 0x7ba92c: r2 = LoadInt32Instr(r1)
    //     0x7ba92c: sbfx            x2, x1, #1, #0x1f
    // 0x7ba930: lsl             x3, x2, #2
    // 0x7ba934: stur            x3, [fp, #-0x58]
    // 0x7ba938: mov             x1, x3
    // 0x7ba93c: ubfx            x1, x1, #0, #0x20
    // 0x7ba940: r2 = 3
    //     0x7ba940: movz            x2, #0x3
    // 0x7ba944: and             x4, x1, x2
    // 0x7ba948: r1 = 4
    //     0x7ba948: movz            x1, #0x4
    // 0x7ba94c: sub             w5, w1, w4
    // 0x7ba950: and             x1, x5, x2
    // 0x7ba954: ubfx            x1, x1, #0, #0x20
    // 0x7ba958: add             x2, x3, x1
    // 0x7ba95c: lsl             x4, x2, #1
    // 0x7ba960: ldur            x1, [fp, #-0x60]
    // 0x7ba964: stur            x4, [fp, #-0x18]
    // 0x7ba968: r2 = "hmtx"
    //     0x7ba968: add             x2, PP, #0x33, lsl #12  ; [pp+0x33878] "hmtx"
    //     0x7ba96c: ldr             x2, [x2, #0x878]
    // 0x7ba970: r0 = _getValueOrData()
    //     0x7ba970: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7ba974: mov             x1, x0
    // 0x7ba978: ldur            x0, [fp, #-0x60]
    // 0x7ba97c: LoadField: r2 = r0->field_f
    //     0x7ba97c: ldur            w2, [x0, #0xf]
    // 0x7ba980: DecompressPointer r2
    //     0x7ba980: add             x2, x2, HEAP, lsl #32
    // 0x7ba984: cmp             w2, w1
    // 0x7ba988: b.ne            #0x7ba994
    // 0x7ba98c: r2 = Null
    //     0x7ba98c: mov             x2, NULL
    // 0x7ba990: b               #0x7ba998
    // 0x7ba994: mov             x2, x1
    // 0x7ba998: ldur            x0, [fp, #-0x48]
    // 0x7ba99c: ldur            x1, [fp, #-0x68]
    // 0x7ba9a0: stur            x2, [fp, #-0x30]
    // 0x7ba9a4: cmp             w2, NULL
    // 0x7ba9a8: b.eq            #0x7bbbc4
    // 0x7ba9ac: ldur            x4, [fp, #-0x18]
    // 0x7ba9b0: r0 = AllocateUint8Array()
    //     0x7ba9b0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7ba9b4: stur            x0, [fp, #-0x18]
    // 0x7ba9b8: r0 = _ByteBuffer()
    //     0x7ba9b8: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7ba9bc: ldur            x3, [fp, #-0x18]
    // 0x7ba9c0: StoreField: r0->field_7 = r3
    //     0x7ba9c0: stur            w3, [x0, #7]
    // 0x7ba9c4: mov             x1, x0
    // 0x7ba9c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ba9c8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ba9cc: r0 = asByteData()
    //     0x7ba9cc: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7ba9d0: ldur            x1, [fp, #-0x78]
    // 0x7ba9d4: stur            x0, [fp, #-0x38]
    // 0x7ba9d8: r0 = numOfLongHorMetrics()
    //     0x7ba9d8: bl              #0x7bc650  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::numOfLongHorMetrics
    // 0x7ba9dc: mov             x2, x0
    // 0x7ba9e0: stur            x2, [fp, #-0xb0]
    // 0x7ba9e4: sub             x0, x2, #1
    // 0x7ba9e8: lsl             x1, x0, #2
    // 0x7ba9ec: ldur            x0, [fp, #-0x30]
    // 0x7ba9f0: r3 = LoadInt32Instr(r0)
    //     0x7ba9f0: sbfx            x3, x0, #1, #0x1f
    //     0x7ba9f4: tbz             w0, #0, #0x7ba9fc
    //     0x7ba9f8: ldur            x3, [x0, #7]
    // 0x7ba9fc: add             x4, x3, x1
    // 0x7baa00: ldur            x5, [fp, #-0x68]
    // 0x7baa04: LoadField: r0 = r5->field_13
    //     0x7baa04: ldur            w0, [x5, #0x13]
    // 0x7baa08: r1 = LoadInt32Instr(r0)
    //     0x7baa08: sbfx            x1, x0, #1, #0x1f
    // 0x7baa0c: sub             x6, x1, #1
    // 0x7baa10: mov             x0, x6
    // 0x7baa14: mov             x1, x4
    // 0x7baa18: stur            x6, [fp, #-0x90]
    // 0x7baa1c: cmp             x1, x0
    // 0x7baa20: b.hs            #0x7bbbc8
    // 0x7baa24: ArrayLoad: r7 = r5[0]  ; List_4
    //     0x7baa24: ldur            w7, [x5, #0x17]
    // 0x7baa28: DecompressPointer r7
    //     0x7baa28: add             x7, x7, HEAP, lsl #32
    // 0x7baa2c: LoadField: r0 = r5->field_1b
    //     0x7baa2c: ldur            w0, [x5, #0x1b]
    // 0x7baa30: r5 = LoadInt32Instr(r0)
    //     0x7baa30: sbfx            x5, x0, #1, #0x1f
    // 0x7baa34: add             x0, x5, x4
    // 0x7baa38: LoadField: r1 = r7->field_7
    //     0x7baa38: ldur            x1, [x7, #7]
    // 0x7baa3c: ldrh            w4, [x1, x0]
    // 0x7baa40: mov             x0, x4
    // 0x7baa44: ubfx            x0, x0, #0, #0x20
    // 0x7baa48: r8 = 65280
    //     0x7baa48: orr             x8, xzr, #0xff00
    // 0x7baa4c: and             x1, x0, x8
    // 0x7baa50: ubfx            x1, x1, #0, #0x20
    // 0x7baa54: asr             x0, x1, #8
    // 0x7baa58: ubfx            x4, x4, #0, #0x20
    // 0x7baa5c: r9 = 255
    //     0x7baa5c: movz            x9, #0xff
    // 0x7baa60: and             x1, x4, x9
    // 0x7baa64: ubfx            x1, x1, #0, #0x20
    // 0x7baa68: lsl             x4, x1, #8
    // 0x7baa6c: orr             x10, x0, x4
    // 0x7baa70: ldur            x0, [fp, #-0x48]
    // 0x7baa74: LoadField: r1 = r0->field_b
    //     0x7baa74: ldur            w1, [x0, #0xb]
    // 0x7baa78: r4 = LoadInt32Instr(r1)
    //     0x7baa78: sbfx            x4, x1, #1, #0x1f
    // 0x7baa7c: stur            x4, [fp, #-0x80]
    // 0x7baa80: LoadField: r11 = r0->field_f
    //     0x7baa80: ldur            w11, [x0, #0xf]
    // 0x7baa84: DecompressPointer r11
    //     0x7baa84: add             x11, x11, HEAP, lsl #32
    // 0x7baa88: lsl             x0, x2, #2
    // 0x7baa8c: add             x12, x3, x0
    // 0x7baa90: ldur            x0, [fp, #-0x38]
    // 0x7baa94: LoadField: r1 = r0->field_13
    //     0x7baa94: ldur            w1, [x0, #0x13]
    // 0x7baa98: r13 = LoadInt32Instr(r1)
    //     0x7baa98: sbfx            x13, x1, #1, #0x1f
    // 0x7baa9c: sub             x14, x13, #1
    // 0x7baaa0: ArrayLoad: r13 = r0[0]  ; List_4
    //     0x7baaa0: ldur            w13, [x0, #0x17]
    // 0x7baaa4: DecompressPointer r13
    //     0x7baaa4: add             x13, x13, HEAP, lsl #32
    // 0x7baaa8: LoadField: r1 = r0->field_1b
    //     0x7baaa8: ldur            w1, [x0, #0x1b]
    // 0x7baaac: r19 = LoadInt32Instr(r1)
    //     0x7baaac: sbfx            x19, x1, #1, #0x1f
    // 0x7baab0: r24 = 0
    //     0x7baab0: movz            x24, #0
    // 0x7baab4: r0 = 0
    //     0x7baab4: movz            x0, #0
    // 0x7baab8: r23 = 32767
    //     0x7baab8: orr             x23, xzr, #0x7fff
    // 0x7baabc: r20 = 32768
    //     0x7baabc: movz            x20, #0x8000
    // 0x7baac0: CheckStackOverflow
    //     0x7baac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7baac4: cmp             SP, x16
    //     0x7baac8: b.ls            #0x7bbbcc
    // 0x7baacc: cmp             x0, x4
    // 0x7baad0: b.ge            #0x7bacc0
    // 0x7baad4: ArrayLoad: r1 = r11[r0]  ; Unknown_4
    //     0x7baad4: add             x16, x11, x0, lsl #2
    //     0x7baad8: ldur            w1, [x16, #0xf]
    // 0x7baadc: DecompressPointer r1
    //     0x7baadc: add             x1, x1, HEAP, lsl #32
    // 0x7baae0: add             x25, x0, #1
    // 0x7baae4: LoadField: r0 = r1->field_7
    //     0x7baae4: ldur            x0, [x1, #7]
    // 0x7baae8: cmp             x0, x2
    // 0x7baaec: b.ge            #0x7bab48
    // 0x7baaf0: lsl             x1, x0, #2
    // 0x7baaf4: add             x4, x3, x1
    // 0x7baaf8: mov             x6, x0
    // 0x7baafc: ldur            x0, [fp, #-0x90]
    // 0x7bab00: mov             x1, x4
    // 0x7bab04: cmp             x1, x0
    // 0x7bab08: b.hs            #0x7bbbd4
    // 0x7bab0c: add             x0, x5, x4
    // 0x7bab10: LoadField: r1 = r7->field_7
    //     0x7bab10: ldur            x1, [x7, #7]
    // 0x7bab14: ldrh            w4, [x1, x0]
    // 0x7bab18: mov             x0, x4
    // 0x7bab1c: ubfx            x0, x0, #0, #0x20
    // 0x7bab20: and             x1, x0, x8
    // 0x7bab24: ubfx            x1, x1, #0, #0x20
    // 0x7bab28: asr             x0, x1, #8
    // 0x7bab2c: ubfx            x4, x4, #0, #0x20
    // 0x7bab30: and             x1, x4, x9
    // 0x7bab34: ubfx            x1, x1, #0, #0x20
    // 0x7bab38: lsl             x4, x1, #8
    // 0x7bab3c: orr             x1, x0, x4
    // 0x7bab40: mov             x4, x1
    // 0x7bab44: b               #0x7bab50
    // 0x7bab48: mov             x6, x0
    // 0x7bab4c: mov             x4, x10
    // 0x7bab50: cmp             x6, x2
    // 0x7bab54: b.ge            #0x7babbc
    // 0x7bab58: lsl             x0, x6, #2
    // 0x7bab5c: add             x1, x3, x0
    // 0x7bab60: add             x6, x1, #2
    // 0x7bab64: ldur            x0, [fp, #-0x90]
    // 0x7bab68: mov             x1, x6
    // 0x7bab6c: cmp             x1, x0
    // 0x7bab70: b.hs            #0x7bbbd8
    // 0x7bab74: add             x0, x5, x6
    // 0x7bab78: LoadField: r1 = r7->field_7
    //     0x7bab78: ldur            x1, [x7, #7]
    // 0x7bab7c: ldrsh           x6, [x1, x0]
    // 0x7bab80: mov             x0, x6
    // 0x7bab84: ubfx            x0, x0, #0, #0x20
    // 0x7bab88: and             x1, x0, x8
    // 0x7bab8c: lsr             w0, w1, #8
    // 0x7bab90: ubfx            x6, x6, #0, #0x20
    // 0x7bab94: and             x1, x6, x9
    // 0x7bab98: lsl             w6, w1, #8
    // 0x7bab9c: orr             x1, x0, x6
    // 0x7baba0: and             x0, x1, x23
    // 0x7baba4: and             x6, x1, x20
    // 0x7baba8: ubfx            x0, x0, #0, #0x20
    // 0x7babac: ubfx            x6, x6, #0, #0x20
    // 0x7babb0: sub             x1, x0, x6
    // 0x7babb4: mov             x6, x1
    // 0x7babb8: b               #0x7bac1c
    // 0x7babbc: sub             x0, x6, x2
    // 0x7babc0: lsl             x1, x0, #1
    // 0x7babc4: add             x6, x12, x1
    // 0x7babc8: ldur            x0, [fp, #-0x90]
    // 0x7babcc: mov             x1, x6
    // 0x7babd0: cmp             x1, x0
    // 0x7babd4: b.hs            #0x7bbbdc
    // 0x7babd8: add             x0, x5, x6
    // 0x7babdc: LoadField: r1 = r7->field_7
    //     0x7babdc: ldur            x1, [x7, #7]
    // 0x7babe0: ldrsh           x6, [x1, x0]
    // 0x7babe4: mov             x0, x6
    // 0x7babe8: ubfx            x0, x0, #0, #0x20
    // 0x7babec: and             x1, x0, x8
    // 0x7babf0: lsr             w0, w1, #8
    // 0x7babf4: ubfx            x6, x6, #0, #0x20
    // 0x7babf8: and             x1, x6, x9
    // 0x7babfc: lsl             w6, w1, #8
    // 0x7bac00: orr             x1, x0, x6
    // 0x7bac04: and             x0, x1, x23
    // 0x7bac08: and             x6, x1, x20
    // 0x7bac0c: ubfx            x0, x0, #0, #0x20
    // 0x7bac10: ubfx            x6, x6, #0, #0x20
    // 0x7bac14: sub             x1, x0, x6
    // 0x7bac18: mov             x6, x1
    // 0x7bac1c: mov             x0, x14
    // 0x7bac20: mov             x1, x24
    // 0x7bac24: cmp             x1, x0
    // 0x7bac28: b.hs            #0x7bbbe0
    // 0x7bac2c: add             x0, x19, x24
    // 0x7bac30: mov             x1, x4
    // 0x7bac34: ubfx            x1, x1, #0, #0x20
    // 0x7bac38: and             x2, x1, x8
    // 0x7bac3c: ubfx            x2, x2, #0, #0x20
    // 0x7bac40: asr             x1, x2, #8
    // 0x7bac44: ubfx            x4, x4, #0, #0x20
    // 0x7bac48: and             x2, x4, x9
    // 0x7bac4c: ubfx            x2, x2, #0, #0x20
    // 0x7bac50: lsl             x4, x2, #8
    // 0x7bac54: orr             x2, x1, x4
    // 0x7bac58: LoadField: r1 = r13->field_7
    //     0x7bac58: ldur            x1, [x13, #7]
    // 0x7bac5c: strh            w2, [x1, x0]
    // 0x7bac60: add             x2, x24, #2
    // 0x7bac64: mov             x0, x14
    // 0x7bac68: mov             x1, x2
    // 0x7bac6c: cmp             x1, x0
    // 0x7bac70: b.hs            #0x7bbbe4
    // 0x7bac74: add             x0, x19, x2
    // 0x7bac78: mov             x1, x6
    // 0x7bac7c: ubfx            x1, x1, #0, #0x20
    // 0x7bac80: and             x2, x1, x8
    // 0x7bac84: ubfx            x2, x2, #0, #0x20
    // 0x7bac88: asr             x1, x2, #8
    // 0x7bac8c: ubfx            x6, x6, #0, #0x20
    // 0x7bac90: and             x2, x6, x9
    // 0x7bac94: ubfx            x2, x2, #0, #0x20
    // 0x7bac98: lsl             x4, x2, #8
    // 0x7bac9c: orr             x2, x1, x4
    // 0x7baca0: LoadField: r1 = r13->field_7
    //     0x7baca0: ldur            x1, [x13, #7]
    // 0x7baca4: strh            w2, [x1, x0]
    // 0x7baca8: add             x1, x24, #4
    // 0x7bacac: mov             x24, x1
    // 0x7bacb0: mov             x0, x25
    // 0x7bacb4: ldur            x2, [fp, #-0xb0]
    // 0x7bacb8: ldur            x4, [fp, #-0x80]
    // 0x7bacbc: b               #0x7baac0
    // 0x7bacc0: ldur            x7, [fp, #-0x10]
    // 0x7bacc4: ldur            x6, [fp, #-0x20]
    // 0x7bacc8: ldur            x5, [fp, #-0x28]
    // 0x7baccc: ldur            x4, [fp, #-0x58]
    // 0x7bacd0: r0 = LoadClassIdInstr(r6)
    //     0x7bacd0: ldur            x0, [x6, #-1]
    //     0x7bacd4: ubfx            x0, x0, #0xc, #0x14
    // 0x7bacd8: mov             x1, x6
    // 0x7bacdc: ldur            x3, [fp, #-0x18]
    // 0x7bace0: r2 = "hmtx"
    //     0x7bace0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33878] "hmtx"
    //     0x7bace4: ldr             x2, [x2, #0x878]
    // 0x7bace8: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7bace8: sub             lr, x0, #0x10d
    //     0x7bacec: ldr             lr, [x21, lr, lsl #3]
    //     0x7bacf0: blr             lr
    // 0x7bacf4: ldur            x0, [fp, #-0x58]
    // 0x7bacf8: lsl             x3, x0, #1
    // 0x7bacfc: ldur            x4, [fp, #-0x28]
    // 0x7bad00: r0 = LoadClassIdInstr(r4)
    //     0x7bad00: ldur            x0, [x4, #-1]
    //     0x7bad04: ubfx            x0, x0, #0xc, #0x14
    // 0x7bad08: mov             x1, x4
    // 0x7bad0c: r2 = "hmtx"
    //     0x7bad0c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33878] "hmtx"
    //     0x7bad10: ldr             x2, [x2, #0x878]
    // 0x7bad14: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7bad14: sub             lr, x0, #0x10d
    //     0x7bad18: ldr             lr, [x21, lr, lsl #3]
    //     0x7bad1c: blr             lr
    // 0x7bad20: r4 = 80
    //     0x7bad20: movz            x4, #0x50
    // 0x7bad24: r0 = AllocateUint8Array()
    //     0x7bad24: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7bad28: stur            x0, [fp, #-0x18]
    // 0x7bad2c: r0 = _ByteBuffer()
    //     0x7bad2c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7bad30: ldur            x3, [fp, #-0x18]
    // 0x7bad34: StoreField: r0->field_7 = r3
    //     0x7bad34: stur            w3, [x0, #7]
    // 0x7bad38: mov             x1, x0
    // 0x7bad3c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bad3c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bad40: r0 = asByteData()
    //     0x7bad40: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7bad44: mov             x2, x0
    // 0x7bad48: LoadField: r0 = r2->field_13
    //     0x7bad48: ldur            w0, [x2, #0x13]
    // 0x7bad4c: r3 = LoadInt32Instr(r0)
    //     0x7bad4c: sbfx            x3, x0, #1, #0x1f
    // 0x7bad50: sub             x4, x3, #1
    // 0x7bad54: mov             x0, x4
    // 0x7bad58: r1 = 0
    //     0x7bad58: movz            x1, #0
    // 0x7bad5c: cmp             x1, x0
    // 0x7bad60: b.hs            #0x7bbbe8
    // 0x7bad64: ArrayLoad: r5 = r2[0]  ; List_4
    //     0x7bad64: ldur            w5, [x2, #0x17]
    // 0x7bad68: DecompressPointer r5
    //     0x7bad68: add             x5, x5, HEAP, lsl #32
    // 0x7bad6c: LoadField: r6 = r2->field_1b
    //     0x7bad6c: ldur            w6, [x2, #0x1b]
    // 0x7bad70: LoadField: r0 = r5->field_7
    //     0x7bad70: ldur            x0, [x5, #7]
    // 0x7bad74: asr             w1, w6, #1
    // 0x7bad78: add             x1, x0, w1, sxtw
    // 0x7bad7c: strh            wzr, [x1]
    // 0x7bad80: mov             x0, x4
    // 0x7bad84: r1 = 2
    //     0x7bad84: movz            x1, #0x2
    // 0x7bad88: cmp             x1, x0
    // 0x7bad8c: b.hs            #0x7bbbec
    // 0x7bad90: r2 = LoadInt32Instr(r6)
    //     0x7bad90: sbfx            x2, x6, #1, #0x1f
    // 0x7bad94: add             x0, x2, #2
    // 0x7bad98: LoadField: r1 = r5->field_7
    //     0x7bad98: ldur            x1, [x5, #7]
    // 0x7bad9c: r6 = 256
    //     0x7bad9c: movz            x6, #0x100
    // 0x7bada0: strh            w6, [x1, x0]
    // 0x7bada4: mov             x0, x4
    // 0x7bada8: r1 = 4
    //     0x7bada8: movz            x1, #0x4
    // 0x7badac: cmp             x1, x0
    // 0x7badb0: b.hs            #0x7bbbf0
    // 0x7badb4: add             x0, x2, #4
    // 0x7badb8: LoadField: r1 = r5->field_7
    //     0x7badb8: ldur            x1, [x5, #7]
    // 0x7badbc: r6 = 768
    //     0x7badbc: movz            x6, #0x300
    // 0x7badc0: strh            w6, [x1, x0]
    // 0x7badc4: mov             x0, x4
    // 0x7badc8: r1 = 6
    //     0x7badc8: movz            x1, #0x6
    // 0x7badcc: cmp             x1, x0
    // 0x7badd0: b.hs            #0x7bbbf4
    // 0x7badd4: add             x0, x2, #6
    // 0x7badd8: LoadField: r1 = r5->field_7
    //     0x7badd8: ldur            x1, [x5, #7]
    // 0x7baddc: r6 = 2560
    //     0x7baddc: movz            x6, #0xa00
    // 0x7bade0: strh            w6, [x1, x0]
    // 0x7bade4: sub             x6, x3, #3
    // 0x7bade8: mov             x0, x6
    // 0x7badec: r1 = 8
    //     0x7badec: movz            x1, #0x8
    // 0x7badf0: cmp             x1, x0
    // 0x7badf4: b.hs            #0x7bbbf8
    // 0x7badf8: add             x0, x2, #8
    // 0x7badfc: LoadField: r1 = r5->field_7
    //     0x7badfc: ldur            x1, [x5, #7]
    // 0x7bae00: r3 = 201326592
    //     0x7bae00: orr             x3, xzr, #0xc000000
    // 0x7bae04: str             w3, [x1, x0]
    // 0x7bae08: mov             x0, x4
    // 0x7bae0c: r1 = 12
    //     0x7bae0c: movz            x1, #0xc
    // 0x7bae10: cmp             x1, x0
    // 0x7bae14: b.hs            #0x7bbbfc
    // 0x7bae18: add             x0, x2, #0xc
    // 0x7bae1c: LoadField: r1 = r5->field_7
    //     0x7bae1c: ldur            x1, [x5, #7]
    // 0x7bae20: r3 = 3072
    //     0x7bae20: movz            x3, #0xc00
    // 0x7bae24: strh            w3, [x1, x0]
    // 0x7bae28: mov             x0, x6
    // 0x7bae2c: r1 = 16
    //     0x7bae2c: movz            x1, #0x10
    // 0x7bae30: cmp             x1, x0
    // 0x7bae34: b.hs            #0x7bbc00
    // 0x7bae38: add             x0, x2, #0x10
    // 0x7bae3c: LoadField: r1 = r5->field_7
    //     0x7bae3c: ldur            x1, [x5, #7]
    // 0x7bae40: r3 = 469762048
    //     0x7bae40: orr             x3, xzr, #0x1c000000
    // 0x7bae44: str             w3, [x1, x0]
    // 0x7bae48: mov             x0, x6
    // 0x7bae4c: r1 = 20
    //     0x7bae4c: movz            x1, #0x14
    // 0x7bae50: cmp             x1, x0
    // 0x7bae54: b.hs            #0x7bbc04
    // 0x7bae58: add             x0, x2, #0x14
    // 0x7bae5c: LoadField: r1 = r5->field_7
    //     0x7bae5c: ldur            x1, [x5, #7]
    // 0x7bae60: r3 = 16777216
    //     0x7bae60: orr             x3, xzr, #0x1000000
    // 0x7bae64: str             w3, [x1, x0]
    // 0x7bae68: mov             x0, x6
    // 0x7bae6c: r1 = 24
    //     0x7bae6c: movz            x1, #0x18
    // 0x7bae70: cmp             x1, x0
    // 0x7bae74: b.hs            #0x7bbc08
    // 0x7bae78: add             x0, x2, #0x18
    // 0x7bae7c: LoadField: r1 = r5->field_7
    //     0x7bae7c: ldur            x1, [x5, #7]
    // 0x7bae80: str             w3, [x1, x0]
    // 0x7bae84: mov             x0, x6
    // 0x7bae88: r1 = 28
    //     0x7bae88: movz            x1, #0x1c
    // 0x7bae8c: cmp             x1, x0
    // 0x7bae90: b.hs            #0x7bbc0c
    // 0x7bae94: add             x0, x2, #0x1c
    // 0x7bae98: LoadField: r1 = r5->field_7
    //     0x7bae98: ldur            x1, [x5, #7]
    // 0x7bae9c: r3 = 536870912
    //     0x7bae9c: orr             x3, xzr, #0x20000000
    // 0x7baea0: str             w3, [x1, x0]
    // 0x7baea4: ldur            x0, [fp, #-0x10]
    // 0x7baea8: LoadField: r1 = r0->field_b
    //     0x7baea8: ldur            w1, [x0, #0xb]
    // 0x7baeac: r0 = LoadInt32Instr(r1)
    //     0x7baeac: sbfx            x0, x1, #1, #0x1f
    // 0x7baeb0: add             x3, x0, #0x1f
    // 0x7baeb4: mov             x0, x6
    // 0x7baeb8: r1 = 32
    //     0x7baeb8: movz            x1, #0x20
    // 0x7baebc: cmp             x1, x0
    // 0x7baec0: b.hs            #0x7bbc10
    // 0x7baec4: add             x0, x2, #0x20
    // 0x7baec8: mov             x1, x3
    // 0x7baecc: ubfx            x1, x1, #0, #0x20
    // 0x7baed0: r4 = 4278255360
    //     0x7baed0: movz            x4, #0xff00
    //     0x7baed4: movk            x4, #0xff00, lsl #16
    // 0x7baed8: and             x7, x1, x4
    // 0x7baedc: ubfx            x7, x7, #0, #0x20
    // 0x7baee0: asr             x1, x7, #8
    // 0x7baee4: ubfx            x3, x3, #0, #0x20
    // 0x7baee8: r7 = 16711935
    //     0x7baee8: movz            x7, #0xff
    //     0x7baeec: movk            x7, #0xff, lsl #16
    // 0x7baef0: and             x8, x3, x7
    // 0x7baef4: ubfx            x8, x8, #0, #0x20
    // 0x7baef8: lsl             x3, x8, #8
    // 0x7baefc: orr             x8, x1, x3
    // 0x7baf00: mov             x1, x8
    // 0x7baf04: ubfx            x1, x1, #0, #0x20
    // 0x7baf08: r9 = 4294901760
    //     0x7baf08: orr             x9, xzr, #0xffff0000
    // 0x7baf0c: and             x3, x1, x9
    // 0x7baf10: ubfx            x3, x3, #0, #0x20
    // 0x7baf14: asr             x1, x3, #0x10
    // 0x7baf18: ubfx            x8, x8, #0, #0x20
    // 0x7baf1c: r10 = 65535
    //     0x7baf1c: orr             x10, xzr, #0xffff
    // 0x7baf20: and             x3, x8, x10
    // 0x7baf24: ubfx            x3, x3, #0, #0x20
    // 0x7baf28: lsl             x8, x3, #0x10
    // 0x7baf2c: orr             x3, x1, x8
    // 0x7baf30: ubfx            x3, x3, #0, #0x20
    // 0x7baf34: LoadField: r1 = r5->field_7
    //     0x7baf34: ldur            x1, [x5, #7]
    // 0x7baf38: str             w3, [x1, x0]
    // 0x7baf3c: mov             x0, x6
    // 0x7baf40: r1 = 36
    //     0x7baf40: movz            x1, #0x24
    // 0x7baf44: cmp             x1, x0
    // 0x7baf48: b.hs            #0x7bbc14
    // 0x7baf4c: add             x0, x2, #0x24
    // 0x7baf50: LoadField: r1 = r5->field_7
    //     0x7baf50: ldur            x1, [x5, #7]
    // 0x7baf54: str             wzr, [x1, x0]
    // 0x7baf58: ldur            x5, [fp, #-0x20]
    // 0x7baf5c: r0 = LoadClassIdInstr(r5)
    //     0x7baf5c: ldur            x0, [x5, #-1]
    //     0x7baf60: ubfx            x0, x0, #0xc, #0x14
    // 0x7baf64: mov             x1, x5
    // 0x7baf68: ldur            x3, [fp, #-0x18]
    // 0x7baf6c: r2 = "cmap"
    //     0x7baf6c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33898] "cmap"
    //     0x7baf70: ldr             x2, [x2, #0x898]
    // 0x7baf74: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7baf74: sub             lr, x0, #0x10d
    //     0x7baf78: ldr             lr, [x21, lr, lsl #3]
    //     0x7baf7c: blr             lr
    // 0x7baf80: ldur            x4, [fp, #-0x28]
    // 0x7baf84: r0 = LoadClassIdInstr(r4)
    //     0x7baf84: ldur            x0, [x4, #-1]
    //     0x7baf88: ubfx            x0, x0, #0xc, #0x14
    // 0x7baf8c: mov             x1, x4
    // 0x7baf90: r2 = "cmap"
    //     0x7baf90: add             x2, PP, #0x33, lsl #12  ; [pp+0x33898] "cmap"
    //     0x7baf94: ldr             x2, [x2, #0x898]
    // 0x7baf98: r3 = 80
    //     0x7baf98: movz            x3, #0x50
    // 0x7baf9c: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7baf9c: sub             lr, x0, #0x10d
    //     0x7bafa0: ldr             lr, [x21, lr, lsl #3]
    //     0x7bafa4: blr             lr
    // 0x7bafa8: r4 = 40
    //     0x7bafa8: movz            x4, #0x28
    // 0x7bafac: r0 = AllocateUint8Array()
    //     0x7bafac: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7bafb0: stur            x0, [fp, #-0x18]
    // 0x7bafb4: r0 = _ByteBuffer()
    //     0x7bafb4: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7bafb8: ldur            x3, [fp, #-0x18]
    // 0x7bafbc: StoreField: r0->field_7 = r3
    //     0x7bafbc: stur            w3, [x0, #7]
    // 0x7bafc0: mov             x1, x0
    // 0x7bafc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bafc4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bafc8: r0 = asByteData()
    //     0x7bafc8: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7bafcc: mov             x2, x0
    // 0x7bafd0: LoadField: r0 = r2->field_13
    //     0x7bafd0: ldur            w0, [x2, #0x13]
    // 0x7bafd4: r1 = LoadInt32Instr(r0)
    //     0x7bafd4: sbfx            x1, x0, #1, #0x1f
    // 0x7bafd8: sub             x3, x1, #1
    // 0x7bafdc: mov             x0, x3
    // 0x7bafe0: r1 = 0
    //     0x7bafe0: movz            x1, #0
    // 0x7bafe4: cmp             x1, x0
    // 0x7bafe8: b.hs            #0x7bbc18
    // 0x7bafec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7bafec: ldur            w4, [x2, #0x17]
    // 0x7baff0: DecompressPointer r4
    //     0x7baff0: add             x4, x4, HEAP, lsl #32
    // 0x7baff4: LoadField: r5 = r2->field_1b
    //     0x7baff4: ldur            w5, [x2, #0x1b]
    // 0x7baff8: LoadField: r0 = r4->field_7
    //     0x7baff8: ldur            x0, [x4, #7]
    // 0x7baffc: asr             w1, w5, #1
    // 0x7bb000: add             x1, x0, w1, sxtw
    // 0x7bb004: strh            wzr, [x1]
    // 0x7bb008: mov             x0, x3
    // 0x7bb00c: r1 = 2
    //     0x7bb00c: movz            x1, #0x2
    // 0x7bb010: cmp             x1, x0
    // 0x7bb014: b.hs            #0x7bbc1c
    // 0x7bb018: r2 = LoadInt32Instr(r5)
    //     0x7bb018: sbfx            x2, x5, #1, #0x1f
    // 0x7bb01c: add             x0, x2, #2
    // 0x7bb020: LoadField: r1 = r4->field_7
    //     0x7bb020: ldur            x1, [x4, #7]
    // 0x7bb024: strh            wzr, [x1, x0]
    // 0x7bb028: mov             x0, x3
    // 0x7bb02c: r1 = 4
    //     0x7bb02c: movz            x1, #0x4
    // 0x7bb030: cmp             x1, x0
    // 0x7bb034: b.hs            #0x7bbc20
    // 0x7bb038: add             x0, x2, #4
    // 0x7bb03c: LoadField: r1 = r4->field_7
    //     0x7bb03c: ldur            x1, [x4, #7]
    // 0x7bb040: r2 = 1536
    //     0x7bb040: movz            x2, #0x600
    // 0x7bb044: strh            w2, [x1, x0]
    // 0x7bb048: ldur            x4, [fp, #-0x20]
    // 0x7bb04c: r0 = LoadClassIdInstr(r4)
    //     0x7bb04c: ldur            x0, [x4, #-1]
    //     0x7bb050: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb054: mov             x1, x4
    // 0x7bb058: ldur            x3, [fp, #-0x18]
    // 0x7bb05c: r2 = "name"
    //     0x7bb05c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7bb060: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7bb060: sub             lr, x0, #0x10d
    //     0x7bb064: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb068: blr             lr
    // 0x7bb06c: ldur            x4, [fp, #-0x28]
    // 0x7bb070: r0 = LoadClassIdInstr(r4)
    //     0x7bb070: ldur            x0, [x4, #-1]
    //     0x7bb074: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb078: mov             x1, x4
    // 0x7bb07c: r2 = "name"
    //     0x7bb07c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7bb080: r3 = 36
    //     0x7bb080: movz            x3, #0x24
    // 0x7bb084: r0 = GDT[cid_x0 + -0x10d]()
    //     0x7bb084: sub             lr, x0, #0x10d
    //     0x7bb088: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb08c: blr             lr
    // 0x7bb090: r1 = Null
    //     0x7bb090: mov             x1, NULL
    // 0x7bb094: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb094: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb098: r0 = BytesBuilder()
    //     0x7bb098: bl              #0x706a5c  ; [dart:_internal] BytesBuilder::BytesBuilder
    // 0x7bb09c: mov             x3, x0
    // 0x7bb0a0: ldur            x2, [fp, #-0x20]
    // 0x7bb0a4: stur            x3, [fp, #-0x18]
    // 0x7bb0a8: LoadField: r0 = r2->field_13
    //     0x7bb0a8: ldur            w0, [x2, #0x13]
    // 0x7bb0ac: r1 = LoadInt32Instr(r0)
    //     0x7bb0ac: sbfx            x1, x0, #1, #0x1f
    // 0x7bb0b0: asr             x0, x1, #1
    // 0x7bb0b4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7bb0b4: ldur            w1, [x2, #0x17]
    // 0x7bb0b8: r4 = LoadInt32Instr(r1)
    //     0x7bb0b8: sbfx            x4, x1, #1, #0x1f
    // 0x7bb0bc: sub             x5, x0, x4
    // 0x7bb0c0: stur            x5, [fp, #-0x90]
    // 0x7bb0c4: lsl             x4, x5, #4
    // 0x7bb0c8: stur            x4, [fp, #-0x80]
    // 0x7bb0cc: add             x6, x4, #0xc
    // 0x7bb0d0: stur            x6, [fp, #-0x58]
    // 0x7bb0d4: r0 = BoxInt64Instr(r6)
    //     0x7bb0d4: sbfiz           x0, x6, #1, #0x1f
    //     0x7bb0d8: cmp             x6, x0, asr #1
    //     0x7bb0dc: b.eq            #0x7bb0e8
    //     0x7bb0e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bb0e4: stur            x6, [x0, #7]
    // 0x7bb0e8: stp             x0, NULL, [SP]
    // 0x7bb0ec: r0 = ByteData()
    //     0x7bb0ec: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0x7bb0f0: mov             x3, x0
    // 0x7bb0f4: ldur            x2, [fp, #-0x58]
    // 0x7bb0f8: sub             x4, x2, #3
    // 0x7bb0fc: mov             x0, x4
    // 0x7bb100: stur            x4, [fp, #-0xb8]
    // 0x7bb104: r1 = 0
    //     0x7bb104: movz            x1, #0
    // 0x7bb108: cmp             x1, x0
    // 0x7bb10c: b.hs            #0x7bbc24
    // 0x7bb110: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x7bb110: ldur            w5, [x3, #0x17]
    // 0x7bb114: DecompressPointer r5
    //     0x7bb114: add             x5, x5, HEAP, lsl #32
    // 0x7bb118: stur            x5, [fp, #-0x30]
    // 0x7bb11c: LoadField: r0 = r5->field_7
    //     0x7bb11c: ldur            x0, [x5, #7]
    // 0x7bb120: r1 = 256
    //     0x7bb120: movz            x1, #0x100
    // 0x7bb124: str             w1, [x0]
    // 0x7bb128: sub             x3, x2, #1
    // 0x7bb12c: mov             x0, x3
    // 0x7bb130: stur            x3, [fp, #-0xb0]
    // 0x7bb134: r1 = 4
    //     0x7bb134: movz            x1, #0x4
    // 0x7bb138: cmp             x1, x0
    // 0x7bb13c: b.hs            #0x7bbc28
    // 0x7bb140: ldur            x0, [fp, #-0x90]
    // 0x7bb144: ubfx            x0, x0, #0, #0x20
    // 0x7bb148: r6 = 65280
    //     0x7bb148: orr             x6, xzr, #0xff00
    // 0x7bb14c: and             x1, x0, x6
    // 0x7bb150: ubfx            x1, x1, #0, #0x20
    // 0x7bb154: asr             x0, x1, #8
    // 0x7bb158: ldur            x1, [fp, #-0x90]
    // 0x7bb15c: ubfx            x1, x1, #0, #0x20
    // 0x7bb160: r7 = 255
    //     0x7bb160: movz            x7, #0xff
    // 0x7bb164: and             x8, x1, x7
    // 0x7bb168: ubfx            x8, x8, #0, #0x20
    // 0x7bb16c: lsl             x1, x8, #8
    // 0x7bb170: orr             x8, x0, x1
    // 0x7bb174: LoadField: r0 = r5->field_7
    //     0x7bb174: ldur            x0, [x5, #7]
    // 0x7bb178: strh            w8, [x0, #4]
    // 0x7bb17c: ldur            x8, [fp, #-0x90]
    // 0x7bb180: CheckStackOverflow
    //     0x7bb180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bb184: cmp             SP, x16
    //     0x7bb188: b.ls            #0x7bbc2c
    // 0x7bb18c: sub             x0, x8, #1
    // 0x7bb190: tst             x8, x0
    // 0x7bb194: b.eq            #0x7bb1a4
    // 0x7bb198: add             x0, x8, #1
    // 0x7bb19c: mov             x8, x0
    // 0x7bb1a0: b               #0x7bb180
    // 0x7bb1a4: ldur            x9, [fp, #-0x80]
    // 0x7bb1a8: lsl             x10, x8, #4
    // 0x7bb1ac: mov             x0, x3
    // 0x7bb1b0: stur            x10, [fp, #-0x90]
    // 0x7bb1b4: r1 = 6
    //     0x7bb1b4: movz            x1, #0x6
    // 0x7bb1b8: cmp             x1, x0
    // 0x7bb1bc: b.hs            #0x7bbc34
    // 0x7bb1c0: mov             x0, x10
    // 0x7bb1c4: ubfx            x0, x0, #0, #0x20
    // 0x7bb1c8: and             x1, x0, x6
    // 0x7bb1cc: ubfx            x1, x1, #0, #0x20
    // 0x7bb1d0: asr             x0, x1, #8
    // 0x7bb1d4: mov             x1, x10
    // 0x7bb1d8: ubfx            x1, x1, #0, #0x20
    // 0x7bb1dc: and             x11, x1, x7
    // 0x7bb1e0: ubfx            x11, x11, #0, #0x20
    // 0x7bb1e4: lsl             x1, x11, #8
    // 0x7bb1e8: orr             x11, x0, x1
    // 0x7bb1ec: LoadField: r0 = r5->field_7
    //     0x7bb1ec: ldur            x0, [x5, #7]
    // 0x7bb1f0: strh            w11, [x0, #6]
    // 0x7bb1f4: r0 = BoxInt64Instr(r8)
    //     0x7bb1f4: sbfiz           x0, x8, #1, #0x1f
    //     0x7bb1f8: cmp             x8, x0, asr #1
    //     0x7bb1fc: b.eq            #0x7bb208
    //     0x7bb200: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bb204: stur            x8, [x0, #7]
    // 0x7bb208: r1 = 60
    //     0x7bb208: movz            x1, #0x3c
    // 0x7bb20c: branchIfSmi(r0, 0x7bb218)
    //     0x7bb20c: tbz             w0, #0, #0x7bb218
    // 0x7bb210: r1 = LoadClassIdInstr(r0)
    //     0x7bb210: ldur            x1, [x0, #-1]
    //     0x7bb214: ubfx            x1, x1, #0xc, #0x14
    // 0x7bb218: str             x0, [SP]
    // 0x7bb21c: mov             x0, x1
    // 0x7bb220: r0 = GDT[cid_x0 + -0xffa]()
    //     0x7bb220: sub             lr, x0, #0xffa
    //     0x7bb224: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb228: blr             lr
    // 0x7bb22c: LoadField: d0 = r0->field_7
    //     0x7bb22c: ldur            d0, [x0, #7]
    // 0x7bb230: stp             fp, lr, [SP, #-0x10]!
    // 0x7bb234: mov             fp, SP
    // 0x7bb238: CallRuntime_LibcLog(double) -> double
    //     0x7bb238: and             SP, SP, #0xfffffffffffffff0
    //     0x7bb23c: mov             sp, SP
    //     0x7bb240: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x7bb244: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7bb248: blr             x16
    //     0x7bb24c: movz            x16, #0x8
    //     0x7bb250: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7bb254: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x7bb258: sub             sp, x16, #1, lsl #12
    //     0x7bb25c: mov             SP, fp
    //     0x7bb260: ldp             fp, lr, [SP], #0x10
    // 0x7bb264: fcmp            d0, d0
    // 0x7bb268: b.vs            #0x7bbc38
    // 0x7bb26c: fcvtzs          x2, d0
    // 0x7bb270: asr             x16, x2, #0x1e
    // 0x7bb274: cmp             x16, x2, asr #63
    // 0x7bb278: b.ne            #0x7bbc38
    // 0x7bb27c: lsl             x2, x2, #1
    // 0x7bb280: ldur            x0, [fp, #-0xb0]
    // 0x7bb284: r1 = 8
    //     0x7bb284: movz            x1, #0x8
    // 0x7bb288: cmp             x1, x0
    // 0x7bb28c: b.hs            #0x7bbc58
    // 0x7bb290: r0 = LoadInt32Instr(r2)
    //     0x7bb290: sbfx            x0, x2, #1, #0x1f
    //     0x7bb294: tbz             w2, #0, #0x7bb29c
    //     0x7bb298: ldur            x0, [x2, #7]
    // 0x7bb29c: r2 = 65280
    //     0x7bb29c: orr             x2, xzr, #0xff00
    // 0x7bb2a0: and             x1, x0, x2
    // 0x7bb2a4: ubfx            x1, x1, #0, #0x20
    // 0x7bb2a8: asr             x3, x1, #8
    // 0x7bb2ac: r4 = 255
    //     0x7bb2ac: movz            x4, #0xff
    // 0x7bb2b0: and             x1, x0, x4
    // 0x7bb2b4: ubfx            x1, x1, #0, #0x20
    // 0x7bb2b8: lsl             x0, x1, #8
    // 0x7bb2bc: orr             x1, x3, x0
    // 0x7bb2c0: ldur            x3, [fp, #-0x30]
    // 0x7bb2c4: LoadField: r0 = r3->field_7
    //     0x7bb2c4: ldur            x0, [x3, #7]
    // 0x7bb2c8: strh            w1, [x0, #8]
    // 0x7bb2cc: ldur            x0, [fp, #-0x80]
    // 0x7bb2d0: ldur            x1, [fp, #-0x90]
    // 0x7bb2d4: sub             x5, x1, x0
    // 0x7bb2d8: ldur            x0, [fp, #-0xb0]
    // 0x7bb2dc: r1 = 10
    //     0x7bb2dc: movz            x1, #0xa
    // 0x7bb2e0: cmp             x1, x0
    // 0x7bb2e4: b.hs            #0x7bbc5c
    // 0x7bb2e8: mov             x0, x5
    // 0x7bb2ec: ubfx            x0, x0, #0, #0x20
    // 0x7bb2f0: and             x1, x0, x2
    // 0x7bb2f4: ubfx            x1, x1, #0, #0x20
    // 0x7bb2f8: asr             x0, x1, #8
    // 0x7bb2fc: ubfx            x5, x5, #0, #0x20
    // 0x7bb300: and             x1, x5, x4
    // 0x7bb304: ubfx            x1, x1, #0, #0x20
    // 0x7bb308: lsl             x2, x1, #8
    // 0x7bb30c: orr             x1, x0, x2
    // 0x7bb310: LoadField: r0 = r3->field_7
    //     0x7bb310: ldur            x0, [x3, #7]
    // 0x7bb314: strh            w1, [x0, #0xa]
    // 0x7bb318: r1 = <String>
    //     0x7bb318: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7bb31c: r2 = 20
    //     0x7bb31c: movz            x2, #0x14
    // 0x7bb320: r0 = AllocateArray()
    //     0x7bb320: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7bb324: mov             x3, x0
    // 0x7bb328: stur            x3, [fp, #-0x40]
    // 0x7bb32c: r16 = "head"
    //     0x7bb32c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7bb330: ldr             x16, [x16, #0x890]
    // 0x7bb334: StoreField: r3->field_f = r16
    //     0x7bb334: stur            w16, [x3, #0xf]
    // 0x7bb338: r16 = "hhea"
    //     0x7bb338: add             x16, PP, #0x33, lsl #12  ; [pp+0x33888] "hhea"
    //     0x7bb33c: ldr             x16, [x16, #0x888]
    // 0x7bb340: StoreField: r3->field_13 = r16
    //     0x7bb340: stur            w16, [x3, #0x13]
    // 0x7bb344: r16 = "maxp"
    //     0x7bb344: add             x16, PP, #0x33, lsl #12  ; [pp+0x33880] "maxp"
    //     0x7bb348: ldr             x16, [x16, #0x880]
    // 0x7bb34c: ArrayStore: r3[0] = r16  ; List_4
    //     0x7bb34c: stur            w16, [x3, #0x17]
    // 0x7bb350: r16 = "OS/2"
    //     0x7bb350: add             x16, PP, #0x47, lsl #12  ; [pp+0x47750] "OS/2"
    //     0x7bb354: ldr             x16, [x16, #0x750]
    // 0x7bb358: StoreField: r3->field_1b = r16
    //     0x7bb358: stur            w16, [x3, #0x1b]
    // 0x7bb35c: r16 = "hmtx"
    //     0x7bb35c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33878] "hmtx"
    //     0x7bb360: ldr             x16, [x16, #0x878]
    // 0x7bb364: StoreField: r3->field_1f = r16
    //     0x7bb364: stur            w16, [x3, #0x1f]
    // 0x7bb368: r16 = "cmap"
    //     0x7bb368: add             x16, PP, #0x33, lsl #12  ; [pp+0x33898] "cmap"
    //     0x7bb36c: ldr             x16, [x16, #0x898]
    // 0x7bb370: StoreField: r3->field_23 = r16
    //     0x7bb370: stur            w16, [x3, #0x23]
    // 0x7bb374: r16 = "loca"
    //     0x7bb374: add             x16, PP, #0x33, lsl #12  ; [pp+0x33858] "loca"
    //     0x7bb378: ldr             x16, [x16, #0x858]
    // 0x7bb37c: StoreField: r3->field_27 = r16
    //     0x7bb37c: stur            w16, [x3, #0x27]
    // 0x7bb380: r16 = "glyf"
    //     0x7bb380: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "glyf"
    //     0x7bb384: ldr             x16, [x16, #0x860]
    // 0x7bb388: StoreField: r3->field_2b = r16
    //     0x7bb388: stur            w16, [x3, #0x2b]
    // 0x7bb38c: r16 = "name"
    //     0x7bb38c: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7bb390: StoreField: r3->field_2f = r16
    //     0x7bb390: stur            w16, [x3, #0x2f]
    // 0x7bb394: r16 = "post"
    //     0x7bb394: add             x16, PP, #0x47, lsl #12  ; [pp+0x47768] "post"
    //     0x7bb398: ldr             x16, [x16, #0x768]
    // 0x7bb39c: StoreField: r3->field_33 = r16
    //     0x7bb39c: stur            w16, [x3, #0x33]
    // 0x7bb3a0: ldur            x8, [fp, #-0x58]
    // 0x7bb3a4: r9 = 0
    //     0x7bb3a4: movz            x9, #0
    // 0x7bb3a8: r7 = 0
    //     0x7bb3a8: movz            x7, #0
    // 0x7bb3ac: ldur            x5, [fp, #-0x20]
    // 0x7bb3b0: ldur            x6, [fp, #-0x28]
    // 0x7bb3b4: ldur            x4, [fp, #-0x30]
    // 0x7bb3b8: stur            x9, [fp, #-0x90]
    // 0x7bb3bc: stur            x8, [fp, #-0xb0]
    // 0x7bb3c0: stur            x7, [fp, #-0xc0]
    // 0x7bb3c4: CheckStackOverflow
    //     0x7bb3c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bb3c8: cmp             SP, x16
    //     0x7bb3cc: b.ls            #0x7bbc60
    // 0x7bb3d0: cmp             x9, #0xa
    // 0x7bb3d4: b.ge            #0x7bb8b8
    // 0x7bb3d8: ArrayLoad: r10 = r3[r9]  ; Unknown_4
    //     0x7bb3d8: add             x16, x3, x9, lsl #2
    //     0x7bb3dc: ldur            w10, [x16, #0xf]
    // 0x7bb3e0: DecompressPointer r10
    //     0x7bb3e0: add             x10, x10, HEAP, lsl #32
    // 0x7bb3e4: stur            x10, [fp, #-0x38]
    // 0x7bb3e8: add             x11, x9, #1
    // 0x7bb3ec: stur            x11, [fp, #-0x80]
    // 0x7bb3f0: r0 = LoadClassIdInstr(r5)
    //     0x7bb3f0: ldur            x0, [x5, #-1]
    //     0x7bb3f4: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb3f8: mov             x1, x5
    // 0x7bb3fc: mov             x2, x10
    // 0x7bb400: r0 = GDT[cid_x0 + -0x114]()
    //     0x7bb400: sub             lr, x0, #0x114
    //     0x7bb404: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb408: blr             lr
    // 0x7bb40c: stur            x0, [fp, #-0x50]
    // 0x7bb410: cmp             w0, NULL
    // 0x7bb414: b.eq            #0x7bbc68
    // 0x7bb418: r1 = <int>
    //     0x7bb418: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7bb41c: r0 = Runes()
    //     0x7bb41c: bl              #0x7bc644  ; AllocateRunesStub -> Runes (size=0x10)
    // 0x7bb420: mov             x1, x0
    // 0x7bb424: ldur            x0, [fp, #-0x38]
    // 0x7bb428: StoreField: r1->field_b = r0
    //     0x7bb428: stur            w0, [x1, #0xb]
    // 0x7bb42c: mov             x2, x1
    // 0x7bb430: r1 = <int>
    //     0x7bb430: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7bb434: r0 = _GrowableList.of()
    //     0x7bb434: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7bb438: mov             x2, x0
    // 0x7bb43c: ldur            x0, [fp, #-0x90]
    // 0x7bb440: lsl             x1, x0, #4
    // 0x7bb444: add             x3, x1, #0xc
    // 0x7bb448: stur            x3, [fp, #-0xc8]
    // 0x7bb44c: LoadField: r0 = r2->field_b
    //     0x7bb44c: ldur            w0, [x2, #0xb]
    // 0x7bb450: r4 = LoadInt32Instr(r0)
    //     0x7bb450: sbfx            x4, x0, #1, #0x1f
    // 0x7bb454: mov             x0, x4
    // 0x7bb458: r1 = 0
    //     0x7bb458: movz            x1, #0
    // 0x7bb45c: cmp             x1, x0
    // 0x7bb460: b.hs            #0x7bbc6c
    // 0x7bb464: LoadField: r5 = r2->field_f
    //     0x7bb464: ldur            w5, [x2, #0xf]
    // 0x7bb468: DecompressPointer r5
    //     0x7bb468: add             x5, x5, HEAP, lsl #32
    // 0x7bb46c: LoadField: r2 = r5->field_f
    //     0x7bb46c: ldur            w2, [x5, #0xf]
    // 0x7bb470: DecompressPointer r2
    //     0x7bb470: add             x2, x2, HEAP, lsl #32
    // 0x7bb474: ldur            x0, [fp, #-0x58]
    // 0x7bb478: mov             x1, x3
    // 0x7bb47c: cmp             x1, x0
    // 0x7bb480: b.hs            #0x7bbc70
    // 0x7bb484: r0 = LoadInt32Instr(r2)
    //     0x7bb484: sbfx            x0, x2, #1, #0x1f
    //     0x7bb488: tbz             w2, #0, #0x7bb490
    //     0x7bb48c: ldur            x0, [x2, #7]
    // 0x7bb490: ldur            x2, [fp, #-0x30]
    // 0x7bb494: LoadField: r1 = r2->field_7
    //     0x7bb494: ldur            x1, [x2, #7]
    // 0x7bb498: strb            w0, [x1, x3]
    // 0x7bb49c: add             x6, x3, #1
    // 0x7bb4a0: mov             x0, x4
    // 0x7bb4a4: r1 = 1
    //     0x7bb4a4: movz            x1, #0x1
    // 0x7bb4a8: cmp             x1, x0
    // 0x7bb4ac: b.hs            #0x7bbc74
    // 0x7bb4b0: LoadField: r7 = r5->field_13
    //     0x7bb4b0: ldur            w7, [x5, #0x13]
    // 0x7bb4b4: DecompressPointer r7
    //     0x7bb4b4: add             x7, x7, HEAP, lsl #32
    // 0x7bb4b8: ldur            x0, [fp, #-0x58]
    // 0x7bb4bc: mov             x1, x6
    // 0x7bb4c0: cmp             x1, x0
    // 0x7bb4c4: b.hs            #0x7bbc78
    // 0x7bb4c8: r0 = LoadInt32Instr(r7)
    //     0x7bb4c8: sbfx            x0, x7, #1, #0x1f
    //     0x7bb4cc: tbz             w7, #0, #0x7bb4d4
    //     0x7bb4d0: ldur            x0, [x7, #7]
    // 0x7bb4d4: LoadField: r1 = r2->field_7
    //     0x7bb4d4: ldur            x1, [x2, #7]
    // 0x7bb4d8: strb            w0, [x1, x6]
    // 0x7bb4dc: add             x6, x3, #2
    // 0x7bb4e0: mov             x0, x4
    // 0x7bb4e4: r1 = 2
    //     0x7bb4e4: movz            x1, #0x2
    // 0x7bb4e8: cmp             x1, x0
    // 0x7bb4ec: b.hs            #0x7bbc7c
    // 0x7bb4f0: ArrayLoad: r7 = r5[0]  ; List_4
    //     0x7bb4f0: ldur            w7, [x5, #0x17]
    // 0x7bb4f4: DecompressPointer r7
    //     0x7bb4f4: add             x7, x7, HEAP, lsl #32
    // 0x7bb4f8: ldur            x0, [fp, #-0x58]
    // 0x7bb4fc: mov             x1, x6
    // 0x7bb500: cmp             x1, x0
    // 0x7bb504: b.hs            #0x7bbc80
    // 0x7bb508: r0 = LoadInt32Instr(r7)
    //     0x7bb508: sbfx            x0, x7, #1, #0x1f
    //     0x7bb50c: tbz             w7, #0, #0x7bb514
    //     0x7bb510: ldur            x0, [x7, #7]
    // 0x7bb514: LoadField: r1 = r2->field_7
    //     0x7bb514: ldur            x1, [x2, #7]
    // 0x7bb518: strb            w0, [x1, x6]
    // 0x7bb51c: add             x6, x3, #3
    // 0x7bb520: mov             x0, x4
    // 0x7bb524: r1 = 3
    //     0x7bb524: movz            x1, #0x3
    // 0x7bb528: cmp             x1, x0
    // 0x7bb52c: b.hs            #0x7bbc84
    // 0x7bb530: LoadField: r4 = r5->field_1b
    //     0x7bb530: ldur            w4, [x5, #0x1b]
    // 0x7bb534: DecompressPointer r4
    //     0x7bb534: add             x4, x4, HEAP, lsl #32
    // 0x7bb538: ldur            x0, [fp, #-0x58]
    // 0x7bb53c: mov             x1, x6
    // 0x7bb540: cmp             x1, x0
    // 0x7bb544: b.hs            #0x7bbc88
    // 0x7bb548: r0 = LoadInt32Instr(r4)
    //     0x7bb548: sbfx            x0, x4, #1, #0x1f
    //     0x7bb54c: tbz             w4, #0, #0x7bb554
    //     0x7bb550: ldur            x0, [x4, #7]
    // 0x7bb554: LoadField: r1 = r2->field_7
    //     0x7bb554: ldur            x1, [x2, #7]
    // 0x7bb558: strb            w0, [x1, x6]
    // 0x7bb55c: add             x4, x3, #4
    // 0x7bb560: ldur            x5, [fp, #-0x50]
    // 0x7bb564: stur            x4, [fp, #-0x90]
    // 0x7bb568: r0 = LoadClassIdInstr(r5)
    //     0x7bb568: ldur            x0, [x5, #-1]
    //     0x7bb56c: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb570: mov             x1, x5
    // 0x7bb574: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7bb574: sub             lr, x0, #0xf60
    //     0x7bb578: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb57c: blr             lr
    // 0x7bb580: r1 = LoadClassIdInstr(r0)
    //     0x7bb580: ldur            x1, [x0, #-1]
    //     0x7bb584: ubfx            x1, x1, #0xc, #0x14
    // 0x7bb588: mov             x16, x0
    // 0x7bb58c: mov             x0, x1
    // 0x7bb590: mov             x1, x16
    // 0x7bb594: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb594: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb598: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7bb598: sub             lr, x0, #0xfff
    //     0x7bb59c: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb5a0: blr             lr
    // 0x7bb5a4: LoadField: r1 = r0->field_13
    //     0x7bb5a4: ldur            w1, [x0, #0x13]
    // 0x7bb5a8: r2 = LoadInt32Instr(r1)
    //     0x7bb5a8: sbfx            x2, x1, #1, #0x1f
    // 0x7bb5ac: sub             x3, x2, #3
    // 0x7bb5b0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7bb5b0: ldur            w2, [x0, #0x17]
    // 0x7bb5b4: DecompressPointer r2
    //     0x7bb5b4: add             x2, x2, HEAP, lsl #32
    // 0x7bb5b8: LoadField: r1 = r0->field_1b
    //     0x7bb5b8: ldur            w1, [x0, #0x1b]
    // 0x7bb5bc: r4 = LoadInt32Instr(r1)
    //     0x7bb5bc: sbfx            x4, x1, #1, #0x1f
    // 0x7bb5c0: r12 = 0
    //     0x7bb5c0: movz            x12, #0
    // 0x7bb5c4: r11 = 0
    //     0x7bb5c4: movz            x11, #0
    // 0x7bb5c8: r10 = 8
    //     0x7bb5c8: movz            x10, #0x8
    // 0x7bb5cc: r9 = 16
    //     0x7bb5cc: movz            x9, #0x10
    // 0x7bb5d0: r5 = 4278255360
    //     0x7bb5d0: movz            x5, #0xff00
    //     0x7bb5d4: movk            x5, #0xff00, lsl #16
    // 0x7bb5d8: r6 = 16711935
    //     0x7bb5d8: movz            x6, #0xff
    //     0x7bb5dc: movk            x6, #0xff, lsl #16
    // 0x7bb5e0: r7 = 4294901760
    //     0x7bb5e0: orr             x7, xzr, #0xffff0000
    // 0x7bb5e4: r8 = 65535
    //     0x7bb5e4: orr             x8, xzr, #0xffff
    // 0x7bb5e8: CheckStackOverflow
    //     0x7bb5e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bb5ec: cmp             SP, x16
    //     0x7bb5f0: b.ls            #0x7bbc8c
    // 0x7bb5f4: cmp             x11, x3
    // 0x7bb5f8: b.ge            #0x7bb690
    // 0x7bb5fc: mov             x0, x3
    // 0x7bb600: mov             x1, x11
    // 0x7bb604: cmp             x1, x0
    // 0x7bb608: b.hs            #0x7bbc94
    // 0x7bb60c: add             x0, x4, x11
    // 0x7bb610: LoadField: r1 = r2->field_7
    //     0x7bb610: ldur            x1, [x2, #7]
    // 0x7bb614: ldr             w13, [x1, x0]
    // 0x7bb618: and             x0, x13, x5
    // 0x7bb61c: tbnz            x10, #0x3f, #0x7bbc98
    // 0x7bb620: lsr             w1, w0, w10
    // 0x7bb624: cmp             x10, #0x1f
    // 0x7bb628: csel            x1, x1, xzr, le
    // 0x7bb62c: and             x0, x13, x6
    // 0x7bb630: tbnz            x10, #0x3f, #0x7bbccc
    // 0x7bb634: lsl             w13, w0, w10
    // 0x7bb638: cmp             x10, #0x1f
    // 0x7bb63c: csel            x13, x13, xzr, le
    // 0x7bb640: orr             x0, x1, x13
    // 0x7bb644: and             x1, x0, x7
    // 0x7bb648: tbnz            x9, #0x3f, #0x7bbd00
    // 0x7bb64c: lsr             w13, w1, w9
    // 0x7bb650: cmp             x9, #0x1f
    // 0x7bb654: csel            x13, x13, xzr, le
    // 0x7bb658: and             x1, x0, x8
    // 0x7bb65c: tbnz            x9, #0x3f, #0x7bbd34
    // 0x7bb660: lsl             w0, w1, w9
    // 0x7bb664: cmp             x9, #0x1f
    // 0x7bb668: csel            x0, x0, xzr, le
    // 0x7bb66c: orr             x1, x13, x0
    // 0x7bb670: mov             x0, x12
    // 0x7bb674: ubfx            x0, x0, #0, #0x20
    // 0x7bb678: add             w13, w0, w1
    // 0x7bb67c: add             x0, x11, #4
    // 0x7bb680: ubfx            x13, x13, #0, #0x20
    // 0x7bb684: mov             x12, x13
    // 0x7bb688: mov             x11, x0
    // 0x7bb68c: b               #0x7bb5e8
    // 0x7bb690: ldur            x13, [fp, #-0x28]
    // 0x7bb694: ldur            x2, [fp, #-0xc8]
    // 0x7bb698: ldur            x4, [fp, #-0x90]
    // 0x7bb69c: ldur            x3, [fp, #-0x30]
    // 0x7bb6a0: ldur            x11, [fp, #-0x38]
    // 0x7bb6a4: ldur            x0, [fp, #-0xb8]
    // 0x7bb6a8: mov             x1, x4
    // 0x7bb6ac: cmp             x1, x0
    // 0x7bb6b0: b.hs            #0x7bbd68
    // 0x7bb6b4: mov             x0, x12
    // 0x7bb6b8: ubfx            x0, x0, #0, #0x20
    // 0x7bb6bc: and             x1, x0, x5
    // 0x7bb6c0: ubfx            x1, x1, #0, #0x20
    // 0x7bb6c4: asr             x0, x1, #8
    // 0x7bb6c8: ubfx            x12, x12, #0, #0x20
    // 0x7bb6cc: and             x1, x12, x6
    // 0x7bb6d0: ubfx            x1, x1, #0, #0x20
    // 0x7bb6d4: lsl             x12, x1, #8
    // 0x7bb6d8: orr             x1, x0, x12
    // 0x7bb6dc: mov             x0, x1
    // 0x7bb6e0: ubfx            x0, x0, #0, #0x20
    // 0x7bb6e4: and             x12, x0, x7
    // 0x7bb6e8: ubfx            x12, x12, #0, #0x20
    // 0x7bb6ec: asr             x0, x12, #0x10
    // 0x7bb6f0: ubfx            x1, x1, #0, #0x20
    // 0x7bb6f4: and             x12, x1, x8
    // 0x7bb6f8: ubfx            x12, x12, #0, #0x20
    // 0x7bb6fc: lsl             x1, x12, #0x10
    // 0x7bb700: orr             x12, x0, x1
    // 0x7bb704: ubfx            x12, x12, #0, #0x20
    // 0x7bb708: LoadField: r0 = r3->field_7
    //     0x7bb708: ldur            x0, [x3, #7]
    // 0x7bb70c: str             w12, [x0, x4]
    // 0x7bb710: add             x4, x2, #8
    // 0x7bb714: ldur            x0, [fp, #-0xb8]
    // 0x7bb718: mov             x1, x4
    // 0x7bb71c: cmp             x1, x0
    // 0x7bb720: b.hs            #0x7bbd6c
    // 0x7bb724: ldur            x0, [fp, #-0xb0]
    // 0x7bb728: ubfx            x0, x0, #0, #0x20
    // 0x7bb72c: and             x1, x0, x5
    // 0x7bb730: ubfx            x1, x1, #0, #0x20
    // 0x7bb734: asr             x0, x1, #8
    // 0x7bb738: ldur            x1, [fp, #-0xb0]
    // 0x7bb73c: ubfx            x1, x1, #0, #0x20
    // 0x7bb740: and             x12, x1, x6
    // 0x7bb744: ubfx            x12, x12, #0, #0x20
    // 0x7bb748: lsl             x1, x12, #8
    // 0x7bb74c: orr             x12, x0, x1
    // 0x7bb750: mov             x0, x12
    // 0x7bb754: ubfx            x0, x0, #0, #0x20
    // 0x7bb758: and             x1, x0, x7
    // 0x7bb75c: ubfx            x1, x1, #0, #0x20
    // 0x7bb760: asr             x0, x1, #0x10
    // 0x7bb764: ubfx            x12, x12, #0, #0x20
    // 0x7bb768: and             x1, x12, x8
    // 0x7bb76c: ubfx            x1, x1, #0, #0x20
    // 0x7bb770: lsl             x12, x1, #0x10
    // 0x7bb774: orr             x1, x0, x12
    // 0x7bb778: ubfx            x1, x1, #0, #0x20
    // 0x7bb77c: LoadField: r0 = r3->field_7
    //     0x7bb77c: ldur            x0, [x3, #7]
    // 0x7bb780: str             w1, [x0, x4]
    // 0x7bb784: add             x4, x2, #0xc
    // 0x7bb788: stur            x4, [fp, #-0x90]
    // 0x7bb78c: r0 = LoadClassIdInstr(r13)
    //     0x7bb78c: ldur            x0, [x13, #-1]
    //     0x7bb790: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb794: mov             x1, x13
    // 0x7bb798: mov             x2, x11
    // 0x7bb79c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7bb79c: sub             lr, x0, #0x114
    //     0x7bb7a0: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb7a4: blr             lr
    // 0x7bb7a8: mov             x2, x0
    // 0x7bb7ac: cmp             w2, NULL
    // 0x7bb7b0: b.eq            #0x7bbd70
    // 0x7bb7b4: ldur            x0, [fp, #-0xb8]
    // 0x7bb7b8: ldur            x1, [fp, #-0x90]
    // 0x7bb7bc: cmp             x1, x0
    // 0x7bb7c0: b.hs            #0x7bbd74
    // 0x7bb7c4: r0 = LoadInt32Instr(r2)
    //     0x7bb7c4: sbfx            x0, x2, #1, #0x1f
    //     0x7bb7c8: tbz             w2, #0, #0x7bb7d0
    //     0x7bb7cc: ldur            x0, [x2, #7]
    // 0x7bb7d0: r1 = 4278255360
    //     0x7bb7d0: movz            x1, #0xff00
    //     0x7bb7d4: movk            x1, #0xff00, lsl #16
    // 0x7bb7d8: and             x2, x0, x1
    // 0x7bb7dc: ubfx            x2, x2, #0, #0x20
    // 0x7bb7e0: asr             x3, x2, #8
    // 0x7bb7e4: r2 = 16711935
    //     0x7bb7e4: movz            x2, #0xff
    //     0x7bb7e8: movk            x2, #0xff, lsl #16
    // 0x7bb7ec: and             x4, x0, x2
    // 0x7bb7f0: ubfx            x4, x4, #0, #0x20
    // 0x7bb7f4: lsl             x0, x4, #8
    // 0x7bb7f8: orr             x4, x3, x0
    // 0x7bb7fc: mov             x0, x4
    // 0x7bb800: ubfx            x0, x0, #0, #0x20
    // 0x7bb804: r3 = 4294901760
    //     0x7bb804: orr             x3, xzr, #0xffff0000
    // 0x7bb808: and             x5, x0, x3
    // 0x7bb80c: ubfx            x5, x5, #0, #0x20
    // 0x7bb810: asr             x0, x5, #0x10
    // 0x7bb814: ubfx            x4, x4, #0, #0x20
    // 0x7bb818: r5 = 65535
    //     0x7bb818: orr             x5, xzr, #0xffff
    // 0x7bb81c: and             x6, x4, x5
    // 0x7bb820: ubfx            x6, x6, #0, #0x20
    // 0x7bb824: lsl             x4, x6, #0x10
    // 0x7bb828: orr             x6, x0, x4
    // 0x7bb82c: ubfx            x6, x6, #0, #0x20
    // 0x7bb830: ldur            x4, [fp, #-0x30]
    // 0x7bb834: LoadField: r0 = r4->field_7
    //     0x7bb834: ldur            x0, [x4, #7]
    // 0x7bb838: ldur            x7, [fp, #-0x90]
    // 0x7bb83c: str             w6, [x0, x7]
    // 0x7bb840: ldur            x0, [fp, #-0x38]
    // 0x7bb844: r6 = LoadClassIdInstr(r0)
    //     0x7bb844: ldur            x6, [x0, #-1]
    //     0x7bb848: ubfx            x6, x6, #0xc, #0x14
    // 0x7bb84c: r16 = "head"
    //     0x7bb84c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] "head"
    //     0x7bb850: ldr             x16, [x16, #0x890]
    // 0x7bb854: stp             x16, x0, [SP]
    // 0x7bb858: mov             x0, x6
    // 0x7bb85c: mov             lr, x0
    // 0x7bb860: ldr             lr, [x21, lr, lsl #3]
    // 0x7bb864: blr             lr
    // 0x7bb868: tbnz            w0, #4, #0x7bb874
    // 0x7bb86c: ldur            x7, [fp, #-0xb0]
    // 0x7bb870: b               #0x7bb878
    // 0x7bb874: ldur            x7, [fp, #-0xc0]
    // 0x7bb878: ldur            x2, [fp, #-0xb0]
    // 0x7bb87c: ldur            x1, [fp, #-0x50]
    // 0x7bb880: stur            x7, [fp, #-0x90]
    // 0x7bb884: r0 = LoadClassIdInstr(r1)
    //     0x7bb884: ldur            x0, [x1, #-1]
    //     0x7bb888: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb88c: r0 = GDT[cid_x0 + 0xcd4c]()
    //     0x7bb88c: movz            x17, #0xcd4c
    //     0x7bb890: add             lr, x0, x17
    //     0x7bb894: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb898: blr             lr
    // 0x7bb89c: mov             x1, x0
    // 0x7bb8a0: ldur            x0, [fp, #-0xb0]
    // 0x7bb8a4: add             x8, x0, x1
    // 0x7bb8a8: ldur            x9, [fp, #-0x80]
    // 0x7bb8ac: ldur            x7, [fp, #-0x90]
    // 0x7bb8b0: ldur            x3, [fp, #-0x40]
    // 0x7bb8b4: b               #0x7bb3ac
    // 0x7bb8b8: mov             x0, x4
    // 0x7bb8bc: r0 = _ByteBuffer()
    //     0x7bb8bc: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7bb8c0: mov             x1, x0
    // 0x7bb8c4: ldur            x0, [fp, #-0x30]
    // 0x7bb8c8: StoreField: r1->field_7 = r0
    //     0x7bb8c8: stur            w0, [x1, #7]
    // 0x7bb8cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb8cc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb8d0: r0 = asUint8List()
    //     0x7bb8d0: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0x7bb8d4: ldur            x1, [fp, #-0x18]
    // 0x7bb8d8: mov             x2, x0
    // 0x7bb8dc: r0 = add()
    //     0x7bb8dc: bl              #0x7bc0b8  ; [dart:_internal] _CopyingBytesBuilder::add
    // 0x7bb8e0: r0 = 0
    //     0x7bb8e0: movz            x0, #0
    // 0x7bb8e4: ldur            x4, [fp, #-0x20]
    // 0x7bb8e8: ldur            x3, [fp, #-0x40]
    // 0x7bb8ec: CheckStackOverflow
    //     0x7bb8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bb8f0: cmp             SP, x16
    //     0x7bb8f4: b.ls            #0x7bbd78
    // 0x7bb8f8: cmp             x0, #0xa
    // 0x7bb8fc: b.ge            #0x7bb98c
    // 0x7bb900: ArrayLoad: r2 = r3[r0]  ; Unknown_4
    //     0x7bb900: add             x16, x3, x0, lsl #2
    //     0x7bb904: ldur            w2, [x16, #0xf]
    // 0x7bb908: DecompressPointer r2
    //     0x7bb908: add             x2, x2, HEAP, lsl #32
    // 0x7bb90c: add             x5, x0, #1
    // 0x7bb910: stur            x5, [fp, #-0x58]
    // 0x7bb914: r0 = LoadClassIdInstr(r4)
    //     0x7bb914: ldur            x0, [x4, #-1]
    //     0x7bb918: ubfx            x0, x0, #0xc, #0x14
    // 0x7bb91c: mov             x1, x4
    // 0x7bb920: r0 = GDT[cid_x0 + -0x114]()
    //     0x7bb920: sub             lr, x0, #0x114
    //     0x7bb924: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb928: blr             lr
    // 0x7bb92c: cmp             w0, NULL
    // 0x7bb930: b.eq            #0x7bbd80
    // 0x7bb934: r1 = LoadClassIdInstr(r0)
    //     0x7bb934: ldur            x1, [x0, #-1]
    //     0x7bb938: ubfx            x1, x1, #0xc, #0x14
    // 0x7bb93c: mov             x16, x0
    // 0x7bb940: mov             x0, x1
    // 0x7bb944: mov             x1, x16
    // 0x7bb948: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7bb948: sub             lr, x0, #0xf60
    //     0x7bb94c: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb950: blr             lr
    // 0x7bb954: r1 = LoadClassIdInstr(r0)
    //     0x7bb954: ldur            x1, [x0, #-1]
    //     0x7bb958: ubfx            x1, x1, #0xc, #0x14
    // 0x7bb95c: mov             x16, x0
    // 0x7bb960: mov             x0, x1
    // 0x7bb964: mov             x1, x16
    // 0x7bb968: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb968: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb96c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7bb96c: sub             lr, x0, #1, lsl #12
    //     0x7bb970: ldr             lr, [x21, lr, lsl #3]
    //     0x7bb974: blr             lr
    // 0x7bb978: ldur            x1, [fp, #-0x18]
    // 0x7bb97c: mov             x2, x0
    // 0x7bb980: r0 = add()
    //     0x7bb980: bl              #0x7bc0b8  ; [dart:_internal] _CopyingBytesBuilder::add
    // 0x7bb984: ldur            x0, [fp, #-0x58]
    // 0x7bb988: b               #0x7bb8e4
    // 0x7bb98c: ldur            x0, [fp, #-0xc0]
    // 0x7bb990: ldur            x1, [fp, #-0x18]
    // 0x7bb994: r0 = toBytes()
    //     0x7bb994: bl              #0x7bbe90  ; [dart:_internal] _CopyingBytesBuilder::toBytes
    // 0x7bb998: stur            x0, [fp, #-0x18]
    // 0x7bb99c: r0 = _ByteBuffer()
    //     0x7bb99c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7bb9a0: mov             x1, x0
    // 0x7bb9a4: ldur            x0, [fp, #-0x18]
    // 0x7bb9a8: StoreField: r1->field_7 = r0
    //     0x7bb9a8: stur            w0, [x1, #7]
    // 0x7bb9ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb9ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb9b0: r0 = asByteData()
    //     0x7bb9b0: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7bb9b4: ldur            x1, [fp, #-8]
    // 0x7bb9b8: mov             x2, x0
    // 0x7bb9bc: r0 = _calcTableChecksum()
    //     0x7bb9bc: bl              #0x7bbdb8  ; [package:pdf/src/pdf/font/ttf_writer.dart] TtfWriter::_calcTableChecksum
    // 0x7bb9c0: mov             x1, x0
    // 0x7bb9c4: r0 = 2981146554
    //     0x7bb9c4: movz            x0, #0xafba
    //     0x7bb9c8: movk            x0, #0xb1b0, lsl #16
    // 0x7bb9cc: sub             x2, x0, x1
    // 0x7bb9d0: stur            x2, [fp, #-0x58]
    // 0x7bb9d4: r0 = _ByteBuffer()
    //     0x7bb9d4: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x7bb9d8: mov             x1, x0
    // 0x7bb9dc: ldur            x0, [fp, #-0x18]
    // 0x7bb9e0: StoreField: r1->field_7 = r0
    //     0x7bb9e0: stur            w0, [x1, #7]
    // 0x7bb9e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7bb9e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7bb9e8: r0 = asByteData()
    //     0x7bb9e8: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x7bb9ec: mov             x2, x0
    // 0x7bb9f0: ldur            x0, [fp, #-0xc0]
    // 0x7bb9f4: add             x3, x0, #8
    // 0x7bb9f8: ldur            x4, [fp, #-0x58]
    // 0x7bb9fc: ubfx            x4, x4, #0, #0x20
    // 0x7bba00: LoadField: r0 = r2->field_13
    //     0x7bba00: ldur            w0, [x2, #0x13]
    // 0x7bba04: r1 = LoadInt32Instr(r0)
    //     0x7bba04: sbfx            x1, x0, #1, #0x1f
    // 0x7bba08: sub             x0, x1, #3
    // 0x7bba0c: mov             x1, x3
    // 0x7bba10: cmp             x1, x0
    // 0x7bba14: b.hs            #0x7bbd84
    // 0x7bba18: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x7bba18: ldur            w0, [x2, #0x17]
    // 0x7bba1c: DecompressPointer r0
    //     0x7bba1c: add             x0, x0, HEAP, lsl #32
    // 0x7bba20: LoadField: r1 = r2->field_1b
    //     0x7bba20: ldur            w1, [x2, #0x1b]
    // 0x7bba24: r2 = LoadInt32Instr(r1)
    //     0x7bba24: sbfx            x2, x1, #1, #0x1f
    // 0x7bba28: add             x1, x2, x3
    // 0x7bba2c: r2 = 4278255360
    //     0x7bba2c: movz            x2, #0xff00
    //     0x7bba30: movk            x2, #0xff00, lsl #16
    // 0x7bba34: and             x3, x4, x2
    // 0x7bba38: ubfx            x3, x3, #0, #0x20
    // 0x7bba3c: asr             x2, x3, #8
    // 0x7bba40: r3 = 16711935
    //     0x7bba40: movz            x3, #0xff
    //     0x7bba44: movk            x3, #0xff, lsl #16
    // 0x7bba48: and             x5, x4, x3
    // 0x7bba4c: ubfx            x5, x5, #0, #0x20
    // 0x7bba50: lsl             x3, x5, #8
    // 0x7bba54: orr             x4, x2, x3
    // 0x7bba58: mov             x2, x4
    // 0x7bba5c: ubfx            x2, x2, #0, #0x20
    // 0x7bba60: r3 = 4294901760
    //     0x7bba60: orr             x3, xzr, #0xffff0000
    // 0x7bba64: and             x5, x2, x3
    // 0x7bba68: ubfx            x5, x5, #0, #0x20
    // 0x7bba6c: asr             x2, x5, #0x10
    // 0x7bba70: ubfx            x4, x4, #0, #0x20
    // 0x7bba74: r3 = 65535
    //     0x7bba74: orr             x3, xzr, #0xffff
    // 0x7bba78: and             x5, x4, x3
    // 0x7bba7c: ubfx            x5, x5, #0, #0x20
    // 0x7bba80: lsl             x3, x5, #0x10
    // 0x7bba84: orr             x4, x2, x3
    // 0x7bba88: ubfx            x4, x4, #0, #0x20
    // 0x7bba8c: LoadField: r2 = r0->field_7
    //     0x7bba8c: ldur            x2, [x0, #7]
    // 0x7bba90: str             w4, [x2, x1]
    // 0x7bba94: ldur            x0, [fp, #-0x18]
    // 0x7bba98: LeaveFrame
    //     0x7bba98: mov             SP, fp
    //     0x7bba9c: ldp             fp, lr, [SP], #0x10
    // 0x7bbaa0: ret
    //     0x7bbaa0: ret             
    // 0x7bbaa4: r0 = noElement()
    //     0x7bbaa4: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0x7bbaa8: r0 = Throw()
    //     0x7bbaa8: bl              #0xec04b8  ; ThrowStub
    // 0x7bbaac: brk             #0
    // 0x7bbab0: r0 = tooFew()
    //     0x7bbab0: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x7bbab4: r0 = Throw()
    //     0x7bbab4: bl              #0xec04b8  ; ThrowStub
    // 0x7bbab8: brk             #0
    // 0x7bbabc: r0 = ConcurrentModificationError()
    //     0x7bbabc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7bbac0: mov             x1, x0
    // 0x7bbac4: ldur            x0, [fp, #-0x48]
    // 0x7bbac8: StoreField: r1->field_b = r0
    //     0x7bbac8: stur            w0, [x1, #0xb]
    // 0x7bbacc: mov             x0, x1
    // 0x7bbad0: r0 = Throw()
    //     0x7bbad0: bl              #0xec04b8  ; ThrowStub
    // 0x7bbad4: brk             #0
    // 0x7bbad8: mov             x0, x2
    // 0x7bbadc: r0 = ConcurrentModificationError()
    //     0x7bbadc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7bbae0: mov             x1, x0
    // 0x7bbae4: ldur            x0, [fp, #-0x48]
    // 0x7bbae8: StoreField: r1->field_b = r0
    //     0x7bbae8: stur            w0, [x1, #0xb]
    // 0x7bbaec: mov             x0, x1
    // 0x7bbaf0: r0 = Throw()
    //     0x7bbaf0: bl              #0xec04b8  ; ThrowStub
    // 0x7bbaf4: brk             #0
    // 0x7bbaf8: mov             x0, x2
    // 0x7bbafc: r0 = ConcurrentModificationError()
    //     0x7bbafc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7bbb00: mov             x1, x0
    // 0x7bbb04: ldur            x0, [fp, #-0x48]
    // 0x7bbb08: StoreField: r1->field_b = r0
    //     0x7bbb08: stur            w0, [x1, #0xb]
    // 0x7bbb0c: mov             x0, x1
    // 0x7bbb10: r0 = Throw()
    //     0x7bbb10: bl              #0xec04b8  ; ThrowStub
    // 0x7bbb14: brk             #0
    // 0x7bbb18: mov             x0, x3
    // 0x7bbb1c: r0 = ConcurrentModificationError()
    //     0x7bbb1c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7bbb20: mov             x1, x0
    // 0x7bbb24: ldur            x0, [fp, #-0x10]
    // 0x7bbb28: StoreField: r1->field_b = r0
    //     0x7bbb28: stur            w0, [x1, #0xb]
    // 0x7bbb2c: mov             x0, x1
    // 0x7bbb30: r0 = Throw()
    //     0x7bbb30: bl              #0xec04b8  ; ThrowStub
    // 0x7bbb34: brk             #0
    // 0x7bbb38: r0 = ConcurrentModificationError()
    //     0x7bbb38: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7bbb3c: mov             x1, x0
    // 0x7bbb40: ldur            x0, [fp, #-0x10]
    // 0x7bbb44: StoreField: r1->field_b = r0
    //     0x7bbb44: stur            w0, [x1, #0xb]
    // 0x7bbb48: mov             x0, x1
    // 0x7bbb4c: r0 = Throw()
    //     0x7bbb4c: bl              #0xec04b8  ; ThrowStub
    // 0x7bbb50: brk             #0
    // 0x7bbb54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb58: b               #0x7b91e0
    // 0x7bbb5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb60: b               #0x7b9394
    // 0x7bbb64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbb64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbb68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb6c: b               #0x7b9674
    // 0x7bbb70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb74: b               #0x7b9928
    // 0x7bbb78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb7c: b               #0x7b99f0
    // 0x7bbb80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb84: b               #0x7b9a7c
    // 0x7bbb88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb8c: b               #0x7b9b18
    // 0x7bbb90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbb90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbb94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbb98: b               #0x7b9ef8
    // 0x7bbb9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbb9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbba0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbba0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbba8: b               #0x7ba234
    // 0x7bbbac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbc0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbbc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbbc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbc8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbbcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbbd0: b               #0x7baacc
    // 0x7bbbd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbd4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbd8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbdc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbe0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbe4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbe4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbe8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbf0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbf4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbf8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbbfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbbfc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbc2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbc30: b               #0x7bb18c
    // 0x7bbc34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc38: SaveReg d0
    //     0x7bbc38: str             q0, [SP, #-0x10]!
    // 0x7bbc3c: r0 = 74
    //     0x7bbc3c: movz            x0, #0x4a
    // 0x7bbc40: r30 = DoubleToIntegerStub
    //     0x7bbc40: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7bbc44: LoadField: r30 = r30->field_7
    //     0x7bbc44: ldur            lr, [lr, #7]
    // 0x7bbc48: blr             lr
    // 0x7bbc4c: mov             x2, x0
    // 0x7bbc50: RestoreReg d0
    //     0x7bbc50: ldr             q0, [SP], #0x10
    // 0x7bbc54: b               #0x7bb280
    // 0x7bbc58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc58: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbc60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbc64: b               #0x7bb3d0
    // 0x7bbc68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbc68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbc6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc7c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbc8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbc90: b               #0x7bb5f4
    // 0x7bbc94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbc94: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbc98: str             x10, [THR, #0x7a8]  ; THR::
    // 0x7bbc9c: stp             x12, x13, [SP, #-0x10]!
    // 0x7bbca0: stp             x10, x11, [SP, #-0x10]!
    // 0x7bbca4: stp             x8, x9, [SP, #-0x10]!
    // 0x7bbca8: stp             x6, x7, [SP, #-0x10]!
    // 0x7bbcac: stp             x4, x5, [SP, #-0x10]!
    // 0x7bbcb0: stp             x2, x3, [SP, #-0x10]!
    // 0x7bbcb4: SaveReg r0
    //     0x7bbcb4: str             x0, [SP, #-8]!
    // 0x7bbcb8: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x7bbcbc: r4 = 0
    //     0x7bbcbc: movz            x4, #0
    // 0x7bbcc0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x7bbcc4: blr             lr
    // 0x7bbcc8: brk             #0
    // 0x7bbccc: str             x10, [THR, #0x7a8]  ; THR::
    // 0x7bbcd0: stp             x11, x12, [SP, #-0x10]!
    // 0x7bbcd4: stp             x9, x10, [SP, #-0x10]!
    // 0x7bbcd8: stp             x7, x8, [SP, #-0x10]!
    // 0x7bbcdc: stp             x5, x6, [SP, #-0x10]!
    // 0x7bbce0: stp             x3, x4, [SP, #-0x10]!
    // 0x7bbce4: stp             x1, x2, [SP, #-0x10]!
    // 0x7bbce8: SaveReg r0
    //     0x7bbce8: str             x0, [SP, #-8]!
    // 0x7bbcec: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x7bbcf0: r4 = 0
    //     0x7bbcf0: movz            x4, #0
    // 0x7bbcf4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x7bbcf8: blr             lr
    // 0x7bbcfc: brk             #0
    // 0x7bbd00: str             x9, [THR, #0x7a8]  ; THR::
    // 0x7bbd04: stp             x11, x12, [SP, #-0x10]!
    // 0x7bbd08: stp             x9, x10, [SP, #-0x10]!
    // 0x7bbd0c: stp             x7, x8, [SP, #-0x10]!
    // 0x7bbd10: stp             x5, x6, [SP, #-0x10]!
    // 0x7bbd14: stp             x3, x4, [SP, #-0x10]!
    // 0x7bbd18: stp             x1, x2, [SP, #-0x10]!
    // 0x7bbd1c: SaveReg r0
    //     0x7bbd1c: str             x0, [SP, #-8]!
    // 0x7bbd20: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x7bbd24: r4 = 0
    //     0x7bbd24: movz            x4, #0
    // 0x7bbd28: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x7bbd2c: blr             lr
    // 0x7bbd30: brk             #0
    // 0x7bbd34: str             x9, [THR, #0x7a8]  ; THR::
    // 0x7bbd38: stp             x12, x13, [SP, #-0x10]!
    // 0x7bbd3c: stp             x10, x11, [SP, #-0x10]!
    // 0x7bbd40: stp             x8, x9, [SP, #-0x10]!
    // 0x7bbd44: stp             x6, x7, [SP, #-0x10]!
    // 0x7bbd48: stp             x4, x5, [SP, #-0x10]!
    // 0x7bbd4c: stp             x2, x3, [SP, #-0x10]!
    // 0x7bbd50: SaveReg r1
    //     0x7bbd50: str             x1, [SP, #-8]!
    // 0x7bbd54: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x7bbd58: r4 = 0
    //     0x7bbd58: movz            x4, #0
    // 0x7bbd5c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x7bbd60: blr             lr
    // 0x7bbd64: brk             #0
    // 0x7bbd68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbd68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbd6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbd6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbd70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbd70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbd74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbd74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bbd78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbd78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbd7c: b               #0x7bb8f8
    // 0x7bbd80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bbd80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bbd84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbd84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _calcTableChecksum(/* No info */) {
    // ** addr: 0x7bbdb8, size: 0xd8
    // 0x7bbdb8: EnterFrame
    //     0x7bbdb8: stp             fp, lr, [SP, #-0x10]!
    //     0x7bbdbc: mov             fp, SP
    // 0x7bbdc0: LoadField: r3 = r2->field_13
    //     0x7bbdc0: ldur            w3, [x2, #0x13]
    // 0x7bbdc4: r4 = LoadInt32Instr(r3)
    //     0x7bbdc4: sbfx            x4, x3, #1, #0x1f
    // 0x7bbdc8: sub             x3, x4, #3
    // 0x7bbdcc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7bbdcc: ldur            w4, [x2, #0x17]
    // 0x7bbdd0: DecompressPointer r4
    //     0x7bbdd0: add             x4, x4, HEAP, lsl #32
    // 0x7bbdd4: LoadField: r5 = r2->field_1b
    //     0x7bbdd4: ldur            w5, [x2, #0x1b]
    // 0x7bbdd8: r2 = LoadInt32Instr(r5)
    //     0x7bbdd8: sbfx            x2, x5, #1, #0x1f
    // 0x7bbddc: r10 = 0
    //     0x7bbddc: movz            x10, #0
    // 0x7bbde0: r9 = 0
    //     0x7bbde0: movz            x9, #0
    // 0x7bbde4: r8 = 4278255360
    //     0x7bbde4: movz            x8, #0xff00
    //     0x7bbde8: movk            x8, #0xff00, lsl #16
    // 0x7bbdec: r7 = 16711935
    //     0x7bbdec: movz            x7, #0xff
    //     0x7bbdf0: movk            x7, #0xff, lsl #16
    // 0x7bbdf4: r6 = 4294901760
    //     0x7bbdf4: orr             x6, xzr, #0xffff0000
    // 0x7bbdf8: r5 = 65535
    //     0x7bbdf8: orr             x5, xzr, #0xffff
    // 0x7bbdfc: CheckStackOverflow
    //     0x7bbdfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bbe00: cmp             SP, x16
    //     0x7bbe04: b.ls            #0x7bbe84
    // 0x7bbe08: cmp             x9, x3
    // 0x7bbe0c: b.ge            #0x7bbe74
    // 0x7bbe10: mov             x0, x3
    // 0x7bbe14: mov             x1, x9
    // 0x7bbe18: cmp             x1, x0
    // 0x7bbe1c: b.hs            #0x7bbe8c
    // 0x7bbe20: add             x1, x2, x9
    // 0x7bbe24: LoadField: r11 = r4->field_7
    //     0x7bbe24: ldur            x11, [x4, #7]
    // 0x7bbe28: ldr             w12, [x11, x1]
    // 0x7bbe2c: and             x1, x12, x8
    // 0x7bbe30: lsr             w11, w1, #8
    // 0x7bbe34: and             x1, x12, x7
    // 0x7bbe38: lsl             w12, w1, #8
    // 0x7bbe3c: orr             x1, x11, x12
    // 0x7bbe40: and             x11, x1, x6
    // 0x7bbe44: lsr             w12, w11, #0x10
    // 0x7bbe48: and             x11, x1, x5
    // 0x7bbe4c: lsl             w1, w11, #0x10
    // 0x7bbe50: orr             x11, x12, x1
    // 0x7bbe54: mov             x1, x10
    // 0x7bbe58: ubfx            x1, x1, #0, #0x20
    // 0x7bbe5c: add             w12, w1, w11
    // 0x7bbe60: add             x0, x9, #4
    // 0x7bbe64: ubfx            x12, x12, #0, #0x20
    // 0x7bbe68: mov             x10, x12
    // 0x7bbe6c: mov             x9, x0
    // 0x7bbe70: b               #0x7bbdfc
    // 0x7bbe74: mov             x0, x10
    // 0x7bbe78: LeaveFrame
    //     0x7bbe78: mov             SP, fp
    //     0x7bbe7c: ldp             fp, lr, [SP], #0x10
    // 0x7bbe80: ret
    //     0x7bbe80: ret             
    // 0x7bbe84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bbe84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bbe88: b               #0x7bbe08
    // 0x7bbe8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bbe8c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _updateCompoundGlyph(/* No info */) {
    // ** addr: 0x7bc85c, size: 0x288
    // 0x7bc85c: EnterFrame
    //     0x7bc85c: stp             fp, lr, [SP, #-0x10]!
    //     0x7bc860: mov             fp, SP
    // 0x7bc864: AllocStack(0x58)
    //     0x7bc864: sub             SP, SP, #0x58
    // 0x7bc868: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0x7bc868: mov             x0, x2
    //     0x7bc86c: mov             x2, x3
    //     0x7bc870: stur            x3, [fp, #-0x10]
    // 0x7bc874: CheckStackOverflow
    //     0x7bc874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bc878: cmp             SP, x16
    //     0x7bc87c: b.ls            #0x7bcac8
    // 0x7bc880: LoadField: r3 = r0->field_f
    //     0x7bc880: ldur            w3, [x0, #0xf]
    // 0x7bc884: DecompressPointer r3
    //     0x7bc884: add             x3, x3, HEAP, lsl #32
    // 0x7bc888: stur            x3, [fp, #-8]
    // 0x7bc88c: r0 = LoadClassIdInstr(r3)
    //     0x7bc88c: ldur            x0, [x3, #-1]
    //     0x7bc890: ubfx            x0, x0, #0xc, #0x14
    // 0x7bc894: mov             x1, x3
    // 0x7bc898: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7bc898: sub             lr, x0, #0xf60
    //     0x7bc89c: ldr             lr, [x21, lr, lsl #3]
    //     0x7bc8a0: blr             lr
    // 0x7bc8a4: mov             x2, x0
    // 0x7bc8a8: ldur            x1, [fp, #-8]
    // 0x7bc8ac: stur            x2, [fp, #-0x18]
    // 0x7bc8b0: r0 = LoadClassIdInstr(r1)
    //     0x7bc8b0: ldur            x0, [x1, #-1]
    //     0x7bc8b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7bc8b8: str             x1, [SP]
    // 0x7bc8bc: r0 = GDT[cid_x0 + 0xd4c0]()
    //     0x7bc8bc: movz            x17, #0xd4c0
    //     0x7bc8c0: add             lr, x0, x17
    //     0x7bc8c4: ldr             lr, [x21, lr, lsl #3]
    //     0x7bc8c8: blr             lr
    // 0x7bc8cc: mov             x2, x0
    // 0x7bc8d0: ldur            x1, [fp, #-8]
    // 0x7bc8d4: stur            x2, [fp, #-0x20]
    // 0x7bc8d8: r0 = LoadClassIdInstr(r1)
    //     0x7bc8d8: ldur            x0, [x1, #-1]
    //     0x7bc8dc: ubfx            x0, x0, #0xc, #0x14
    // 0x7bc8e0: r0 = GDT[cid_x0 + 0xcd4c]()
    //     0x7bc8e0: movz            x17, #0xcd4c
    //     0x7bc8e4: add             lr, x0, x17
    //     0x7bc8e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7bc8ec: blr             lr
    // 0x7bc8f0: mov             x2, x0
    // 0x7bc8f4: r0 = BoxInt64Instr(r2)
    //     0x7bc8f4: sbfiz           x0, x2, #1, #0x1f
    //     0x7bc8f8: cmp             x2, x0, asr #1
    //     0x7bc8fc: b.eq            #0x7bc908
    //     0x7bc900: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bc904: stur            x2, [x0, #7]
    // 0x7bc908: ldur            x1, [fp, #-0x18]
    // 0x7bc90c: r2 = LoadClassIdInstr(r1)
    //     0x7bc90c: ldur            x2, [x1, #-1]
    //     0x7bc910: ubfx            x2, x2, #0xc, #0x14
    // 0x7bc914: ldur            x16, [fp, #-0x20]
    // 0x7bc918: stp             x0, x16, [SP]
    // 0x7bc91c: mov             x0, x2
    // 0x7bc920: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x7bc920: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x7bc924: r0 = GDT[cid_x0 + -0xfff]()
    //     0x7bc924: sub             lr, x0, #0xfff
    //     0x7bc928: ldr             lr, [x21, lr, lsl #3]
    //     0x7bc92c: blr             lr
    // 0x7bc930: mov             x3, x0
    // 0x7bc934: stur            x3, [fp, #-0x18]
    // 0x7bc938: LoadField: r0 = r3->field_13
    //     0x7bc938: ldur            w0, [x3, #0x13]
    // 0x7bc93c: r1 = LoadInt32Instr(r0)
    //     0x7bc93c: sbfx            x1, x0, #1, #0x1f
    // 0x7bc940: sub             x4, x1, #1
    // 0x7bc944: stur            x4, [fp, #-0x48]
    // 0x7bc948: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x7bc948: ldur            w5, [x3, #0x17]
    // 0x7bc94c: DecompressPointer r5
    //     0x7bc94c: add             x5, x5, HEAP, lsl #32
    // 0x7bc950: stur            x5, [fp, #-8]
    // 0x7bc954: LoadField: r0 = r3->field_1b
    //     0x7bc954: ldur            w0, [x3, #0x1b]
    // 0x7bc958: r6 = LoadInt32Instr(r0)
    //     0x7bc958: sbfx            x6, x0, #1, #0x1f
    // 0x7bc95c: stur            x6, [fp, #-0x40]
    // 0x7bc960: r10 = 10
    //     0x7bc960: movz            x10, #0xa
    // 0x7bc964: r0 = 32
    //     0x7bc964: movz            x0, #0x20
    // 0x7bc968: ldur            x7, [fp, #-0x10]
    // 0x7bc96c: r9 = 65280
    //     0x7bc96c: orr             x9, xzr, #0xff00
    // 0x7bc970: r8 = 255
    //     0x7bc970: movz            x8, #0xff
    // 0x7bc974: stur            x10, [fp, #-0x38]
    // 0x7bc978: CheckStackOverflow
    //     0x7bc978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bc97c: cmp             SP, x16
    //     0x7bc980: b.ls            #0x7bcad0
    // 0x7bc984: tbz             w0, #5, #0x7bcab8
    // 0x7bc988: mov             x0, x4
    // 0x7bc98c: mov             x1, x10
    // 0x7bc990: cmp             x1, x0
    // 0x7bc994: b.hs            #0x7bcad8
    // 0x7bc998: add             x0, x6, x10
    // 0x7bc99c: LoadField: r1 = r5->field_7
    //     0x7bc99c: ldur            x1, [x5, #7]
    // 0x7bc9a0: ldrh            w2, [x1, x0]
    // 0x7bc9a4: mov             x0, x2
    // 0x7bc9a8: ubfx            x0, x0, #0, #0x20
    // 0x7bc9ac: and             x1, x0, x9
    // 0x7bc9b0: ubfx            x1, x1, #0, #0x20
    // 0x7bc9b4: asr             x0, x1, #8
    // 0x7bc9b8: ubfx            x2, x2, #0, #0x20
    // 0x7bc9bc: and             x1, x2, x8
    // 0x7bc9c0: ubfx            x1, x1, #0, #0x20
    // 0x7bc9c4: lsl             x2, x1, #8
    // 0x7bc9c8: orr             x11, x0, x2
    // 0x7bc9cc: stur            x11, [fp, #-0x30]
    // 0x7bc9d0: add             x12, x10, #2
    // 0x7bc9d4: mov             x0, x4
    // 0x7bc9d8: mov             x1, x12
    // 0x7bc9dc: stur            x12, [fp, #-0x28]
    // 0x7bc9e0: cmp             x1, x0
    // 0x7bc9e4: b.hs            #0x7bcadc
    // 0x7bc9e8: add             x0, x6, x12
    // 0x7bc9ec: LoadField: r1 = r5->field_7
    //     0x7bc9ec: ldur            x1, [x5, #7]
    // 0x7bc9f0: ldrh            w2, [x1, x0]
    // 0x7bc9f4: mov             x0, x2
    // 0x7bc9f8: ubfx            x0, x0, #0, #0x20
    // 0x7bc9fc: and             x1, x0, x9
    // 0x7bca00: ubfx            x1, x1, #0, #0x20
    // 0x7bca04: asr             x0, x1, #8
    // 0x7bca08: ubfx            x2, x2, #0, #0x20
    // 0x7bca0c: and             x1, x2, x8
    // 0x7bca10: ubfx            x1, x1, #0, #0x20
    // 0x7bca14: lsl             x2, x1, #8
    // 0x7bca18: orr             x1, x0, x2
    // 0x7bca1c: lsl             x2, x1, #1
    // 0x7bca20: mov             x1, x7
    // 0x7bca24: r0 = _getValueOrData()
    //     0x7bca24: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7bca28: mov             x1, x0
    // 0x7bca2c: ldur            x4, [fp, #-0x10]
    // 0x7bca30: LoadField: r0 = r4->field_f
    //     0x7bca30: ldur            w0, [x4, #0xf]
    // 0x7bca34: DecompressPointer r0
    //     0x7bca34: add             x0, x0, HEAP, lsl #32
    // 0x7bca38: cmp             w0, w1
    // 0x7bca3c: b.ne            #0x7bca48
    // 0x7bca40: r0 = Null
    //     0x7bca40: mov             x0, NULL
    // 0x7bca44: b               #0x7bca4c
    // 0x7bca48: mov             x0, x1
    // 0x7bca4c: ldur            x5, [fp, #-0x18]
    // 0x7bca50: ldur            x6, [fp, #-0x30]
    // 0x7bca54: cmp             w0, NULL
    // 0x7bca58: b.eq            #0x7bcae0
    // 0x7bca5c: r3 = LoadInt32Instr(r0)
    //     0x7bca5c: sbfx            x3, x0, #1, #0x1f
    //     0x7bca60: tbz             w0, #0, #0x7bca68
    //     0x7bca64: ldur            x3, [x0, #7]
    // 0x7bca68: r0 = LoadClassIdInstr(r5)
    //     0x7bca68: ldur            x0, [x5, #-1]
    //     0x7bca6c: ubfx            x0, x0, #0xc, #0x14
    // 0x7bca70: mov             x1, x5
    // 0x7bca74: ldur            x2, [fp, #-0x28]
    // 0x7bca78: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7bca78: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7bca7c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7bca7c: sub             lr, x0, #1, lsl #12
    //     0x7bca80: ldr             lr, [x21, lr, lsl #3]
    //     0x7bca84: blr             lr
    // 0x7bca88: ldur            x0, [fp, #-0x30]
    // 0x7bca8c: branchIfSmi(r0, 0x7bca98)
    //     0x7bca8c: tbz             w0, #0, #0x7bca98
    // 0x7bca90: r2 = 8
    //     0x7bca90: movz            x2, #0x8
    // 0x7bca94: b               #0x7bca9c
    // 0x7bca98: r2 = 6
    //     0x7bca98: movz            x2, #0x6
    // 0x7bca9c: ldur            x1, [fp, #-0x38]
    // 0x7bcaa0: add             x10, x1, x2
    // 0x7bcaa4: ldur            x3, [fp, #-0x18]
    // 0x7bcaa8: ldur            x4, [fp, #-0x48]
    // 0x7bcaac: ldur            x5, [fp, #-8]
    // 0x7bcab0: ldur            x6, [fp, #-0x40]
    // 0x7bcab4: b               #0x7bc968
    // 0x7bcab8: r0 = Null
    //     0x7bcab8: mov             x0, NULL
    // 0x7bcabc: LeaveFrame
    //     0x7bcabc: mov             SP, fp
    //     0x7bcac0: ldp             fp, lr, [SP], #0x10
    // 0x7bcac4: ret
    //     0x7bcac4: ret             
    // 0x7bcac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bcac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bcacc: b               #0x7bc880
    // 0x7bcad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bcad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bcad4: b               #0x7bc984
    // 0x7bcad8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bcad8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bcadc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bcadc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bcae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bcae0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void addGlyph(dynamic, dynamic) {
    // ** addr: 0x7c8334, size: 0x214
    // 0x7c8334: EnterFrame
    //     0x7c8334: stp             fp, lr, [SP, #-0x10]!
    //     0x7c8338: mov             fp, SP
    // 0x7c833c: AllocStack(0x48)
    //     0x7c833c: sub             SP, SP, #0x48
    // 0x7c8340: SetupParameters()
    //     0x7c8340: ldr             x0, [fp, #0x18]
    //     0x7c8344: ldur            w3, [x0, #0x17]
    //     0x7c8348: add             x3, x3, HEAP, lsl #32
    //     0x7c834c: stur            x3, [fp, #-0x18]
    // 0x7c8350: CheckStackOverflow
    //     0x7c8350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8354: cmp             SP, x16
    //     0x7c8358: b.ls            #0x7c8538
    // 0x7c835c: LoadField: r4 = r3->field_b
    //     0x7c835c: ldur            w4, [x3, #0xb]
    // 0x7c8360: DecompressPointer r4
    //     0x7c8360: add             x4, x4, HEAP, lsl #32
    // 0x7c8364: stur            x4, [fp, #-0x10]
    // 0x7c8368: LoadField: r0 = r4->field_f
    //     0x7c8368: ldur            w0, [x4, #0xf]
    // 0x7c836c: DecompressPointer r0
    //     0x7c836c: add             x0, x0, HEAP, lsl #32
    // 0x7c8370: LoadField: r5 = r0->field_7
    //     0x7c8370: ldur            w5, [x0, #7]
    // 0x7c8374: DecompressPointer r5
    //     0x7c8374: add             x5, x5, HEAP, lsl #32
    // 0x7c8378: ldr             x0, [fp, #0x10]
    // 0x7c837c: stur            x5, [fp, #-8]
    // 0x7c8380: r2 = Null
    //     0x7c8380: mov             x2, NULL
    // 0x7c8384: r1 = Null
    //     0x7c8384: mov             x1, NULL
    // 0x7c8388: branchIfSmi(r0, 0x7c83b0)
    //     0x7c8388: tbz             w0, #0, #0x7c83b0
    // 0x7c838c: r4 = LoadClassIdInstr(r0)
    //     0x7c838c: ldur            x4, [x0, #-1]
    //     0x7c8390: ubfx            x4, x4, #0xc, #0x14
    // 0x7c8394: sub             x4, x4, #0x3c
    // 0x7c8398: cmp             x4, #1
    // 0x7c839c: b.ls            #0x7c83b0
    // 0x7c83a0: r8 = int
    //     0x7c83a0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c83a4: r3 = Null
    //     0x7c83a4: add             x3, PP, #0x47, lsl #12  ; [pp+0x47770] Null
    //     0x7c83a8: ldr             x3, [x3, #0x770]
    // 0x7c83ac: r0 = int()
    //     0x7c83ac: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c83b0: ldr             x0, [fp, #0x10]
    // 0x7c83b4: r2 = LoadInt32Instr(r0)
    //     0x7c83b4: sbfx            x2, x0, #1, #0x1f
    //     0x7c83b8: tbz             w0, #0, #0x7c83c0
    //     0x7c83bc: ldur            x2, [x0, #7]
    // 0x7c83c0: ldur            x1, [fp, #-8]
    // 0x7c83c4: r0 = readGlyph()
    //     0x7c83c4: bl              #0x7c85d4  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::readGlyph
    // 0x7c83c8: mov             x1, x0
    // 0x7c83cc: r0 = copy()
    //     0x7c83cc: bl              #0x7c8548  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfGlyphInfo::copy
    // 0x7c83d0: mov             x2, x0
    // 0x7c83d4: stur            x2, [fp, #-8]
    // 0x7c83d8: LoadField: r1 = r2->field_13
    //     0x7c83d8: ldur            w1, [x2, #0x13]
    // 0x7c83dc: DecompressPointer r1
    //     0x7c83dc: add             x1, x1, HEAP, lsl #32
    // 0x7c83e0: r0 = LoadClassIdInstr(r1)
    //     0x7c83e0: ldur            x0, [x1, #-1]
    //     0x7c83e4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c83e8: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x7c83e8: movz            x17, #0xd35d
    //     0x7c83ec: add             lr, x0, x17
    //     0x7c83f0: ldr             lr, [x21, lr, lsl #3]
    //     0x7c83f4: blr             lr
    // 0x7c83f8: mov             x3, x0
    // 0x7c83fc: ldur            x2, [fp, #-0x10]
    // 0x7c8400: stur            x3, [fp, #-0x38]
    // 0x7c8404: LoadField: r4 = r2->field_1b
    //     0x7c8404: ldur            w4, [x2, #0x1b]
    // 0x7c8408: DecompressPointer r4
    //     0x7c8408: add             x4, x4, HEAP, lsl #32
    // 0x7c840c: stur            x4, [fp, #-0x30]
    // 0x7c8410: ArrayLoad: r5 = r2[0]  ; List_4
    //     0x7c8410: ldur            w5, [x2, #0x17]
    // 0x7c8414: DecompressPointer r5
    //     0x7c8414: add             x5, x5, HEAP, lsl #32
    // 0x7c8418: ldur            x0, [fp, #-0x18]
    // 0x7c841c: stur            x5, [fp, #-0x28]
    // 0x7c8420: LoadField: r6 = r0->field_f
    //     0x7c8420: ldur            w6, [x0, #0xf]
    // 0x7c8424: DecompressPointer r6
    //     0x7c8424: add             x6, x6, HEAP, lsl #32
    // 0x7c8428: stur            x6, [fp, #-0x20]
    // 0x7c842c: CheckStackOverflow
    //     0x7c842c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c8430: cmp             SP, x16
    //     0x7c8434: b.ls            #0x7c8540
    // 0x7c8438: r0 = LoadClassIdInstr(r3)
    //     0x7c8438: ldur            x0, [x3, #-1]
    //     0x7c843c: ubfx            x0, x0, #0xc, #0x14
    // 0x7c8440: mov             x1, x3
    // 0x7c8444: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x7c8444: movz            x17, #0x292d
    //     0x7c8448: movk            x17, #0x1, lsl #16
    //     0x7c844c: add             lr, x0, x17
    //     0x7c8450: ldr             lr, [x21, lr, lsl #3]
    //     0x7c8454: blr             lr
    // 0x7c8458: tbnz            w0, #4, #0x7c84f4
    // 0x7c845c: ldur            x2, [fp, #-0x38]
    // 0x7c8460: r0 = LoadClassIdInstr(r2)
    //     0x7c8460: ldur            x0, [x2, #-1]
    //     0x7c8464: ubfx            x0, x0, #0xc, #0x14
    // 0x7c8468: mov             x1, x2
    // 0x7c846c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x7c846c: movz            x17, #0x384d
    //     0x7c8470: movk            x17, #0x1, lsl #16
    //     0x7c8474: add             lr, x0, x17
    //     0x7c8478: ldr             lr, [x21, lr, lsl #3]
    //     0x7c847c: blr             lr
    // 0x7c8480: ldur            x1, [fp, #-0x30]
    // 0x7c8484: mov             x2, x0
    // 0x7c8488: stur            x0, [fp, #-0x18]
    // 0x7c848c: r0 = _hashCode()
    //     0x7c848c: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7c8490: ldur            x1, [fp, #-0x30]
    // 0x7c8494: ldur            x2, [fp, #-0x18]
    // 0x7c8498: mov             x5, x0
    // 0x7c849c: r3 = -2
    //     0x7c849c: orr             x3, xzr, #0xfffffffffffffffe
    // 0x7c84a0: r0 = _set()
    //     0x7c84a0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7c84a4: ldur            x1, [fp, #-0x28]
    // 0x7c84a8: ldur            x2, [fp, #-0x18]
    // 0x7c84ac: r0 = _hashCode()
    //     0x7c84ac: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7c84b0: ldur            x1, [fp, #-0x28]
    // 0x7c84b4: ldur            x2, [fp, #-0x18]
    // 0x7c84b8: mov             x3, x0
    // 0x7c84bc: r0 = _add()
    //     0x7c84bc: bl              #0x69b44c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x7c84c0: ldur            x16, [fp, #-0x20]
    // 0x7c84c4: ldur            lr, [fp, #-0x18]
    // 0x7c84c8: stp             lr, x16, [SP]
    // 0x7c84cc: ldur            x0, [fp, #-0x20]
    // 0x7c84d0: ClosureCall
    //     0x7c84d0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7c84d4: ldur            x2, [x0, #0x1f]
    //     0x7c84d8: blr             x2
    // 0x7c84dc: ldur            x2, [fp, #-0x10]
    // 0x7c84e0: ldur            x3, [fp, #-0x38]
    // 0x7c84e4: ldur            x4, [fp, #-0x30]
    // 0x7c84e8: ldur            x5, [fp, #-0x28]
    // 0x7c84ec: ldur            x6, [fp, #-0x20]
    // 0x7c84f0: b               #0x7c842c
    // 0x7c84f4: ldur            x0, [fp, #-0x10]
    // 0x7c84f8: ldur            x3, [fp, #-8]
    // 0x7c84fc: LoadField: r2 = r0->field_13
    //     0x7c84fc: ldur            w2, [x0, #0x13]
    // 0x7c8500: DecompressPointer r2
    //     0x7c8500: add             x2, x2, HEAP, lsl #32
    // 0x7c8504: LoadField: r4 = r3->field_7
    //     0x7c8504: ldur            x4, [x3, #7]
    // 0x7c8508: r0 = BoxInt64Instr(r4)
    //     0x7c8508: sbfiz           x0, x4, #1, #0x1f
    //     0x7c850c: cmp             x4, x0, asr #1
    //     0x7c8510: b.eq            #0x7c851c
    //     0x7c8514: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c8518: stur            x4, [x0, #7]
    // 0x7c851c: mov             x1, x2
    // 0x7c8520: mov             x2, x0
    // 0x7c8524: r0 = []=()
    //     0x7c8524: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c8528: r0 = Null
    //     0x7c8528: mov             x0, NULL
    // 0x7c852c: LeaveFrame
    //     0x7c852c: mov             SP, fp
    //     0x7c8530: ldp             fp, lr, [SP], #0x10
    // 0x7c8534: ret
    //     0x7c8534: ret             
    // 0x7c8538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8538: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c853c: b               #0x7c835c
    // 0x7c8540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c8540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c8544: b               #0x7c8438
  }
}
