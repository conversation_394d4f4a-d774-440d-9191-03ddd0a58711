// lib: , url: package:pdf/src/pdf/format/array.dart

// class id: 1050779, size: 0x8
class :: {
}

// class id: 915, size: 0x10, field offset: 0x8
class PdfArray<X0 bound PdfDataType> extends PdfDataType {

  _ PdfArray(/* No info */) {
    // ** addr: 0x7b6438, size: 0xac
    // 0x7b6438: EnterFrame
    //     0x7b6438: stp             fp, lr, [SP, #-0x10]!
    //     0x7b643c: mov             fp, SP
    // 0x7b6440: AllocStack(0x18)
    //     0x7b6440: sub             SP, SP, #0x18
    // 0x7b6444: SetupParameters(PdfArray<X0 bound PdfDataType> this /* r1 => r0, fp-0x10 */, [dynamic _ = Null /* r3, fp-0x8 */])
    //     0x7b6444: mov             x0, x1
    //     0x7b6448: stur            x1, [fp, #-0x10]
    //     0x7b644c: ldur            w1, [x4, #0x13]
    //     0x7b6450: sub             x2, x1, #2
    //     0x7b6454: cmp             w2, #2
    //     0x7b6458: b.lt            #0x7b646c
    //     0x7b645c: add             x1, fp, w2, sxtw #2
    //     0x7b6460: ldr             x1, [x1, #8]
    //     0x7b6464: mov             x3, x1
    //     0x7b6468: b               #0x7b6470
    //     0x7b646c: mov             x3, NULL
    //     0x7b6470: stur            x3, [fp, #-8]
    // 0x7b6474: CheckStackOverflow
    //     0x7b6474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6478: cmp             SP, x16
    //     0x7b647c: b.ls            #0x7b64dc
    // 0x7b6480: LoadField: r1 = r0->field_7
    //     0x7b6480: ldur            w1, [x0, #7]
    // 0x7b6484: DecompressPointer r1
    //     0x7b6484: add             x1, x1, HEAP, lsl #32
    // 0x7b6488: r2 = 0
    //     0x7b6488: movz            x2, #0
    // 0x7b648c: r0 = _GrowableList()
    //     0x7b648c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b6490: ldur            x2, [fp, #-8]
    // 0x7b6494: stur            x0, [fp, #-0x18]
    // 0x7b6498: cmp             w2, NULL
    // 0x7b649c: b.eq            #0x7b64a8
    // 0x7b64a0: mov             x1, x0
    // 0x7b64a4: r0 = addAll()
    //     0x7b64a4: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7b64a8: ldur            x1, [fp, #-0x10]
    // 0x7b64ac: ldur            x0, [fp, #-0x18]
    // 0x7b64b0: StoreField: r1->field_b = r0
    //     0x7b64b0: stur            w0, [x1, #0xb]
    //     0x7b64b4: ldurb           w16, [x1, #-1]
    //     0x7b64b8: ldurb           w17, [x0, #-1]
    //     0x7b64bc: and             x16, x17, x16, lsr #2
    //     0x7b64c0: tst             x16, HEAP, lsr #32
    //     0x7b64c4: b.eq            #0x7b64cc
    //     0x7b64c8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b64cc: r0 = Null
    //     0x7b64cc: mov             x0, NULL
    // 0x7b64d0: LeaveFrame
    //     0x7b64d0: mov             SP, fp
    //     0x7b64d4: ldp             fp, lr, [SP], #0x10
    // 0x7b64d8: ret
    //     0x7b64d8: ret             
    // 0x7b64dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b64dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b64e0: b               #0x7b6480
  }
  static _ fromNum(/* No info */) {
    // ** addr: 0x7c9bb8, size: 0xb8
    // 0x7c9bb8: EnterFrame
    //     0x7c9bb8: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9bbc: mov             fp, SP
    // 0x7c9bc0: AllocStack(0x28)
    //     0x7c9bc0: sub             SP, SP, #0x28
    // 0x7c9bc4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7c9bc4: mov             x0, x1
    //     0x7c9bc8: stur            x1, [fp, #-8]
    // 0x7c9bcc: CheckStackOverflow
    //     0x7c9bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9bd0: cmp             SP, x16
    //     0x7c9bd4: b.ls            #0x7c9c68
    // 0x7c9bd8: r1 = Function '<anonymous closure>': static.
    //     0x7c9bd8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36670] AnonymousClosure: static (0x7c9c70), in [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum (0x7c9bb8)
    //     0x7c9bdc: ldr             x1, [x1, #0x670]
    // 0x7c9be0: r2 = Null
    //     0x7c9be0: mov             x2, NULL
    // 0x7c9be4: r0 = AllocateClosure()
    //     0x7c9be4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7c9be8: mov             x1, x0
    // 0x7c9bec: ldur            x0, [fp, #-8]
    // 0x7c9bf0: r2 = LoadClassIdInstr(r0)
    //     0x7c9bf0: ldur            x2, [x0, #-1]
    //     0x7c9bf4: ubfx            x2, x2, #0xc, #0x14
    // 0x7c9bf8: r16 = <PdfNum>
    //     0x7c9bf8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36678] TypeArguments: <PdfNum>
    //     0x7c9bfc: ldr             x16, [x16, #0x678]
    // 0x7c9c00: stp             x0, x16, [SP, #8]
    // 0x7c9c04: str             x1, [SP]
    // 0x7c9c08: mov             x0, x2
    // 0x7c9c0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7c9c0c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7c9c10: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7c9c10: movz            x17, #0xf28c
    //     0x7c9c14: add             lr, x0, x17
    //     0x7c9c18: ldr             lr, [x21, lr, lsl #3]
    //     0x7c9c1c: blr             lr
    // 0x7c9c20: LoadField: r1 = r0->field_7
    //     0x7c9c20: ldur            w1, [x0, #7]
    // 0x7c9c24: DecompressPointer r1
    //     0x7c9c24: add             x1, x1, HEAP, lsl #32
    // 0x7c9c28: mov             x2, x0
    // 0x7c9c2c: r0 = _GrowableList.of()
    //     0x7c9c2c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7c9c30: r1 = <PdfNum>
    //     0x7c9c30: add             x1, PP, #0x36, lsl #12  ; [pp+0x36678] TypeArguments: <PdfNum>
    //     0x7c9c34: ldr             x1, [x1, #0x678]
    // 0x7c9c38: stur            x0, [fp, #-8]
    // 0x7c9c3c: r0 = PdfArray()
    //     0x7c9c3c: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7c9c40: stur            x0, [fp, #-0x10]
    // 0x7c9c44: ldur            x16, [fp, #-8]
    // 0x7c9c48: str             x16, [SP]
    // 0x7c9c4c: mov             x1, x0
    // 0x7c9c50: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7c9c50: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7c9c54: r0 = PdfArray()
    //     0x7c9c54: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7c9c58: ldur            x0, [fp, #-0x10]
    // 0x7c9c5c: LeaveFrame
    //     0x7c9c5c: mov             SP, fp
    //     0x7c9c60: ldp             fp, lr, [SP], #0x10
    // 0x7c9c64: ret
    //     0x7c9c64: ret             
    // 0x7c9c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9c68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9c6c: b               #0x7c9bd8
  }
  [closure] static PdfNum <anonymous closure>(dynamic, num) {
    // ** addr: 0x7c9c70, size: 0x20
    // 0x7c9c70: EnterFrame
    //     0x7c9c70: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9c74: mov             fp, SP
    // 0x7c9c78: r0 = PdfNum()
    //     0x7c9c78: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7c9c7c: ldr             x1, [fp, #0x10]
    // 0x7c9c80: StoreField: r0->field_7 = r1
    //     0x7c9c80: stur            w1, [x0, #7]
    // 0x7c9c84: LeaveFrame
    //     0x7c9c84: mov             SP, fp
    //     0x7c9c88: ldp             fp, lr, [SP], #0x10
    // 0x7c9c8c: ret
    //     0x7c9c8c: ret             
  }
  static _ fromObjects(/* No info */) {
    // ** addr: 0x7ca4a4, size: 0x9c
    // 0x7ca4a4: EnterFrame
    //     0x7ca4a4: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca4a8: mov             fp, SP
    // 0x7ca4ac: AllocStack(0x28)
    //     0x7ca4ac: sub             SP, SP, #0x28
    // 0x7ca4b0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7ca4b0: mov             x0, x1
    //     0x7ca4b4: stur            x1, [fp, #-8]
    // 0x7ca4b8: CheckStackOverflow
    //     0x7ca4b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca4bc: cmp             SP, x16
    //     0x7ca4c0: b.ls            #0x7ca538
    // 0x7ca4c4: r1 = Function '<anonymous closure>': static.
    //     0x7ca4c4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c098] AnonymousClosure: static (0x7ca540), in [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects (0x7ca4a4)
    //     0x7ca4c8: ldr             x1, [x1, #0x98]
    // 0x7ca4cc: r2 = Null
    //     0x7ca4cc: mov             x2, NULL
    // 0x7ca4d0: r0 = AllocateClosure()
    //     0x7ca4d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ca4d4: r16 = <PdfIndirect>
    //     0x7ca4d4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] TypeArguments: <PdfIndirect>
    //     0x7ca4d8: ldr             x16, [x16, #0xa0]
    // 0x7ca4dc: ldur            lr, [fp, #-8]
    // 0x7ca4e0: stp             lr, x16, [SP, #8]
    // 0x7ca4e4: str             x0, [SP]
    // 0x7ca4e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ca4e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ca4ec: r0 = map()
    //     0x7ca4ec: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x7ca4f0: LoadField: r1 = r0->field_7
    //     0x7ca4f0: ldur            w1, [x0, #7]
    // 0x7ca4f4: DecompressPointer r1
    //     0x7ca4f4: add             x1, x1, HEAP, lsl #32
    // 0x7ca4f8: mov             x2, x0
    // 0x7ca4fc: r0 = _GrowableList.of()
    //     0x7ca4fc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7ca500: r1 = <PdfIndirect>
    //     0x7ca500: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] TypeArguments: <PdfIndirect>
    //     0x7ca504: ldr             x1, [x1, #0xa0]
    // 0x7ca508: stur            x0, [fp, #-8]
    // 0x7ca50c: r0 = PdfArray()
    //     0x7ca50c: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7ca510: stur            x0, [fp, #-0x10]
    // 0x7ca514: ldur            x16, [fp, #-8]
    // 0x7ca518: str             x16, [SP]
    // 0x7ca51c: mov             x1, x0
    // 0x7ca520: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7ca520: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7ca524: r0 = PdfArray()
    //     0x7ca524: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7ca528: ldur            x0, [fp, #-0x10]
    // 0x7ca52c: LeaveFrame
    //     0x7ca52c: mov             SP, fp
    //     0x7ca530: ldp             fp, lr, [SP], #0x10
    // 0x7ca534: ret
    //     0x7ca534: ret             
    // 0x7ca538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca538: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca53c: b               #0x7ca4c4
  }
  [closure] static PdfIndirect <anonymous closure>(dynamic, PdfObjectBase<PdfDataType>) {
    // ** addr: 0x7ca540, size: 0x30
    // 0x7ca540: EnterFrame
    //     0x7ca540: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca544: mov             fp, SP
    // 0x7ca548: CheckStackOverflow
    //     0x7ca548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca54c: cmp             SP, x16
    //     0x7ca550: b.ls            #0x7ca568
    // 0x7ca554: ldr             x1, [fp, #0x10]
    // 0x7ca558: r0 = ref()
    //     0x7ca558: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7ca55c: LeaveFrame
    //     0x7ca55c: mov             SP, fp
    //     0x7ca560: ldp             fp, lr, [SP], #0x10
    // 0x7ca564: ret
    //     0x7ca564: ret             
    // 0x7ca568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca568: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca56c: b               #0x7ca554
  }
  _ uniq(/* No info */) {
    // ** addr: 0x7cb134, size: 0x238
    // 0x7cb134: EnterFrame
    //     0x7cb134: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb138: mov             fp, SP
    // 0x7cb13c: AllocStack(0x30)
    //     0x7cb13c: sub             SP, SP, #0x30
    // 0x7cb140: CheckStackOverflow
    //     0x7cb140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb144: cmp             SP, x16
    //     0x7cb148: b.ls            #0x7cb35c
    // 0x7cb14c: LoadField: r0 = r1->field_b
    //     0x7cb14c: ldur            w0, [x1, #0xb]
    // 0x7cb150: DecompressPointer r0
    //     0x7cb150: add             x0, x0, HEAP, lsl #32
    // 0x7cb154: stur            x0, [fp, #-8]
    // 0x7cb158: LoadField: r2 = r0->field_b
    //     0x7cb158: ldur            w2, [x0, #0xb]
    // 0x7cb15c: r3 = LoadInt32Instr(r2)
    //     0x7cb15c: sbfx            x3, x2, #1, #0x1f
    // 0x7cb160: cmp             x3, #1
    // 0x7cb164: b.gt            #0x7cb178
    // 0x7cb168: r0 = Null
    //     0x7cb168: mov             x0, NULL
    // 0x7cb16c: LeaveFrame
    //     0x7cb16c: mov             SP, fp
    //     0x7cb170: ldp             fp, lr, [SP], #0x10
    // 0x7cb174: ret
    //     0x7cb174: ret             
    // 0x7cb178: LoadField: r2 = r1->field_7
    //     0x7cb178: ldur            w2, [x1, #7]
    // 0x7cb17c: DecompressPointer r2
    //     0x7cb17c: add             x2, x2, HEAP, lsl #32
    // 0x7cb180: r1 = Null
    //     0x7cb180: mov             x1, NULL
    // 0x7cb184: r3 = <X0 bound PdfDataType, bool>
    //     0x7cb184: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee90] TypeArguments: <X0 bound PdfDataType, bool>
    //     0x7cb188: ldr             x3, [x3, #0xe90]
    // 0x7cb18c: r30 = InstantiateTypeArgumentsStub
    //     0x7cb18c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7cb190: LoadField: r30 = r30->field_7
    //     0x7cb190: ldur            lr, [lr, #7]
    // 0x7cb194: blr             lr
    // 0x7cb198: stur            x0, [fp, #-0x10]
    // 0x7cb19c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x7cb19c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7cb1a0: ldr             x0, [x0, #0x778]
    //     0x7cb1a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7cb1a8: cmp             w0, w16
    //     0x7cb1ac: b.ne            #0x7cb1b8
    //     0x7cb1b0: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x7cb1b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7cb1b8: ldur            x1, [fp, #-0x10]
    // 0x7cb1bc: stur            x0, [fp, #-0x18]
    // 0x7cb1c0: r0 = _Map()
    //     0x7cb1c0: bl              #0x5f8d3c  ; Allocate_MapStub -> _Map<X0, X1> (size=-0x8)
    // 0x7cb1c4: mov             x1, x0
    // 0x7cb1c8: ldur            x0, [fp, #-0x18]
    // 0x7cb1cc: stur            x1, [fp, #-0x20]
    // 0x7cb1d0: StoreField: r1->field_1b = r0
    //     0x7cb1d0: stur            w0, [x1, #0x1b]
    // 0x7cb1d4: StoreField: r1->field_b = rZR
    //     0x7cb1d4: stur            wzr, [x1, #0xb]
    // 0x7cb1d8: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x7cb1d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7cb1dc: ldr             x0, [x0, #0x780]
    //     0x7cb1e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7cb1e4: cmp             w0, w16
    //     0x7cb1e8: b.ne            #0x7cb1f4
    //     0x7cb1ec: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x7cb1f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7cb1f4: ldur            x3, [fp, #-0x20]
    // 0x7cb1f8: StoreField: r3->field_f = r0
    //     0x7cb1f8: stur            w0, [x3, #0xf]
    // 0x7cb1fc: StoreField: r3->field_13 = rZR
    //     0x7cb1fc: stur            wzr, [x3, #0x13]
    // 0x7cb200: ArrayStore: r3[0] = rZR  ; List_4
    //     0x7cb200: stur            wzr, [x3, #0x17]
    // 0x7cb204: ldur            x4, [fp, #-8]
    // 0x7cb208: LoadField: r0 = r4->field_b
    //     0x7cb208: ldur            w0, [x4, #0xb]
    // 0x7cb20c: r5 = LoadInt32Instr(r0)
    //     0x7cb20c: sbfx            x5, x0, #1, #0x1f
    // 0x7cb210: stur            x5, [fp, #-0x30]
    // 0x7cb214: r0 = 0
    //     0x7cb214: movz            x0, #0
    // 0x7cb218: CheckStackOverflow
    //     0x7cb218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb21c: cmp             SP, x16
    //     0x7cb220: b.ls            #0x7cb364
    // 0x7cb224: LoadField: r1 = r4->field_b
    //     0x7cb224: ldur            w1, [x4, #0xb]
    // 0x7cb228: r2 = LoadInt32Instr(r1)
    //     0x7cb228: sbfx            x2, x1, #1, #0x1f
    // 0x7cb22c: cmp             x5, x2
    // 0x7cb230: b.ne            #0x7cb33c
    // 0x7cb234: cmp             x0, x2
    // 0x7cb238: b.ge            #0x7cb2f0
    // 0x7cb23c: LoadField: r1 = r4->field_f
    //     0x7cb23c: ldur            w1, [x4, #0xf]
    // 0x7cb240: DecompressPointer r1
    //     0x7cb240: add             x1, x1, HEAP, lsl #32
    // 0x7cb244: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x7cb244: add             x16, x1, x0, lsl #2
    //     0x7cb248: ldur            w6, [x16, #0xf]
    // 0x7cb24c: DecompressPointer r6
    //     0x7cb24c: add             x6, x6, HEAP, lsl #32
    // 0x7cb250: stur            x6, [fp, #-0x18]
    // 0x7cb254: add             x7, x0, #1
    // 0x7cb258: mov             x0, x6
    // 0x7cb25c: ldur            x2, [fp, #-0x10]
    // 0x7cb260: stur            x7, [fp, #-0x28]
    // 0x7cb264: r1 = Null
    //     0x7cb264: mov             x1, NULL
    // 0x7cb268: cmp             w2, NULL
    // 0x7cb26c: b.eq            #0x7cb28c
    // 0x7cb270: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cb270: ldur            w4, [x2, #0x17]
    // 0x7cb274: DecompressPointer r4
    //     0x7cb274: add             x4, x4, HEAP, lsl #32
    // 0x7cb278: r8 = X0
    //     0x7cb278: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7cb27c: LoadField: r9 = r4->field_7
    //     0x7cb27c: ldur            x9, [x4, #7]
    // 0x7cb280: r3 = Null
    //     0x7cb280: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee98] Null
    //     0x7cb284: ldr             x3, [x3, #0xe98]
    // 0x7cb288: blr             x9
    // 0x7cb28c: ldur            x2, [fp, #-0x10]
    // 0x7cb290: r0 = true
    //     0x7cb290: add             x0, NULL, #0x20  ; true
    // 0x7cb294: r1 = Null
    //     0x7cb294: mov             x1, NULL
    // 0x7cb298: cmp             w2, NULL
    // 0x7cb29c: b.eq            #0x7cb2bc
    // 0x7cb2a0: LoadField: r4 = r2->field_1b
    //     0x7cb2a0: ldur            w4, [x2, #0x1b]
    // 0x7cb2a4: DecompressPointer r4
    //     0x7cb2a4: add             x4, x4, HEAP, lsl #32
    // 0x7cb2a8: r8 = X1
    //     0x7cb2a8: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x7cb2ac: LoadField: r9 = r4->field_7
    //     0x7cb2ac: ldur            x9, [x4, #7]
    // 0x7cb2b0: r3 = Null
    //     0x7cb2b0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eea8] Null
    //     0x7cb2b4: ldr             x3, [x3, #0xea8]
    // 0x7cb2b8: blr             x9
    // 0x7cb2bc: ldur            x1, [fp, #-0x20]
    // 0x7cb2c0: ldur            x2, [fp, #-0x18]
    // 0x7cb2c4: r0 = _hashCode()
    //     0x7cb2c4: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7cb2c8: ldur            x1, [fp, #-0x20]
    // 0x7cb2cc: ldur            x2, [fp, #-0x18]
    // 0x7cb2d0: mov             x5, x0
    // 0x7cb2d4: r3 = true
    //     0x7cb2d4: add             x3, NULL, #0x20  ; true
    // 0x7cb2d8: r0 = _set()
    //     0x7cb2d8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7cb2dc: ldur            x0, [fp, #-0x28]
    // 0x7cb2e0: ldur            x4, [fp, #-8]
    // 0x7cb2e4: ldur            x3, [fp, #-0x20]
    // 0x7cb2e8: ldur            x5, [fp, #-0x30]
    // 0x7cb2ec: b               #0x7cb218
    // 0x7cb2f0: mov             x0, x3
    // 0x7cb2f4: ldur            x1, [fp, #-8]
    // 0x7cb2f8: r0 = clear()
    //     0x7cb2f8: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0x7cb2fc: ldur            x1, [fp, #-0x10]
    // 0x7cb300: r0 = _CompactIterable()
    //     0x7cb300: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7cb304: mov             x1, x0
    // 0x7cb308: ldur            x0, [fp, #-0x20]
    // 0x7cb30c: StoreField: r1->field_b = r0
    //     0x7cb30c: stur            w0, [x1, #0xb]
    // 0x7cb310: r0 = -2
    //     0x7cb310: orr             x0, xzr, #0xfffffffffffffffe
    // 0x7cb314: StoreField: r1->field_f = r0
    //     0x7cb314: stur            x0, [x1, #0xf]
    // 0x7cb318: r0 = 2
    //     0x7cb318: movz            x0, #0x2
    // 0x7cb31c: ArrayStore: r1[0] = r0  ; List_8
    //     0x7cb31c: stur            x0, [x1, #0x17]
    // 0x7cb320: mov             x2, x1
    // 0x7cb324: ldur            x1, [fp, #-8]
    // 0x7cb328: r0 = addAll()
    //     0x7cb328: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7cb32c: r0 = Null
    //     0x7cb32c: mov             x0, NULL
    // 0x7cb330: LeaveFrame
    //     0x7cb330: mov             SP, fp
    //     0x7cb334: ldp             fp, lr, [SP], #0x10
    // 0x7cb338: ret
    //     0x7cb338: ret             
    // 0x7cb33c: mov             x0, x4
    // 0x7cb340: r0 = ConcurrentModificationError()
    //     0x7cb340: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7cb344: mov             x1, x0
    // 0x7cb348: ldur            x0, [fp, #-8]
    // 0x7cb34c: StoreField: r1->field_b = r0
    //     0x7cb34c: stur            w0, [x1, #0xb]
    // 0x7cb350: mov             x0, x1
    // 0x7cb354: r0 = Throw()
    //     0x7cb354: bl              #0xec04b8  ; ThrowStub
    // 0x7cb358: brk             #0
    // 0x7cb35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb35c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb360: b               #0x7cb14c
    // 0x7cb364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb368: b               #0x7cb224
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c550, size: 0x5c
    // 0xd7c550: ldr             x1, [SP]
    // 0xd7c554: cmp             w1, NULL
    // 0xd7c558: b.ne            #0xd7c564
    // 0xd7c55c: r0 = false
    //     0xd7c55c: add             x0, NULL, #0x30  ; false
    // 0xd7c560: ret
    //     0xd7c560: ret             
    // 0xd7c564: r2 = 60
    //     0xd7c564: movz            x2, #0x3c
    // 0xd7c568: branchIfSmi(r1, 0xd7c574)
    //     0xd7c568: tbz             w1, #0, #0xd7c574
    // 0xd7c56c: r2 = LoadClassIdInstr(r1)
    //     0xd7c56c: ldur            x2, [x1, #-1]
    //     0xd7c570: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c574: cmp             x2, #0x393
    // 0xd7c578: b.ne            #0xd7c5a4
    // 0xd7c57c: ldr             x2, [SP, #8]
    // 0xd7c580: LoadField: r3 = r2->field_b
    //     0xd7c580: ldur            w3, [x2, #0xb]
    // 0xd7c584: DecompressPointer r3
    //     0xd7c584: add             x3, x3, HEAP, lsl #32
    // 0xd7c588: LoadField: r2 = r1->field_b
    //     0xd7c588: ldur            w2, [x1, #0xb]
    // 0xd7c58c: DecompressPointer r2
    //     0xd7c58c: add             x2, x2, HEAP, lsl #32
    // 0xd7c590: cmp             w3, w2
    // 0xd7c594: r16 = true
    //     0xd7c594: add             x16, NULL, #0x20  ; true
    // 0xd7c598: r17 = false
    //     0xd7c598: add             x17, NULL, #0x30  ; false
    // 0xd7c59c: csel            x0, x16, x17, eq
    // 0xd7c5a0: ret
    //     0xd7c5a0: ret             
    // 0xd7c5a4: r0 = false
    //     0xd7c5a4: add             x0, NULL, #0x30  ; false
    // 0xd7c5a8: ret
    //     0xd7c5a8: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7e8c4, size: 0x1b0
    // 0xe7e8c4: EnterFrame
    //     0xe7e8c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e8c8: mov             fp, SP
    // 0xe7e8cc: AllocStack(0x28)
    //     0xe7e8cc: sub             SP, SP, #0x28
    // 0xe7e8d0: SetupParameters(PdfArray<X0 bound PdfDataType> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xe7e8d0: mov             x4, x1
    //     0xe7e8d4: mov             x0, x3
    //     0xe7e8d8: stur            x3, [fp, #-0x18]
    //     0xe7e8dc: mov             x3, x2
    //     0xe7e8e0: stur            x1, [fp, #-8]
    //     0xe7e8e4: stur            x2, [fp, #-0x10]
    // 0xe7e8e8: CheckStackOverflow
    //     0xe7e8e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e8ec: cmp             SP, x16
    //     0xe7e8f0: b.ls            #0xe7ea60
    // 0xe7e8f4: mov             x1, x0
    // 0xe7e8f8: r2 = "["
    //     0xe7e8f8: ldr             x2, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xe7e8fc: r0 = putString()
    //     0xe7e8fc: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7e900: ldur            x0, [fp, #-8]
    // 0xe7e904: LoadField: r3 = r0->field_b
    //     0xe7e904: ldur            w3, [x0, #0xb]
    // 0xe7e908: DecompressPointer r3
    //     0xe7e908: add             x3, x3, HEAP, lsl #32
    // 0xe7e90c: stur            x3, [fp, #-0x28]
    // 0xe7e910: LoadField: r0 = r3->field_b
    //     0xe7e910: ldur            w0, [x3, #0xb]
    // 0xe7e914: cbz             w0, #0xe7ea44
    // 0xe7e918: r4 = 0
    //     0xe7e918: movz            x4, #0
    // 0xe7e91c: ldur            x0, [fp, #-0x18]
    // 0xe7e920: stur            x4, [fp, #-0x20]
    // 0xe7e924: CheckStackOverflow
    //     0xe7e924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e928: cmp             SP, x16
    //     0xe7e92c: b.ls            #0xe7ea68
    // 0xe7e930: LoadField: r1 = r3->field_b
    //     0xe7e930: ldur            w1, [x3, #0xb]
    // 0xe7e934: r2 = LoadInt32Instr(r1)
    //     0xe7e934: sbfx            x2, x1, #1, #0x1f
    // 0xe7e938: cmp             x4, x2
    // 0xe7e93c: b.ge            #0xe7ea44
    // 0xe7e940: LoadField: r1 = r3->field_f
    //     0xe7e940: ldur            w1, [x3, #0xf]
    // 0xe7e944: DecompressPointer r1
    //     0xe7e944: add             x1, x1, HEAP, lsl #32
    // 0xe7e948: ArrayLoad: r5 = r1[r4]  ; Unknown_4
    //     0xe7e948: add             x16, x1, x4, lsl #2
    //     0xe7e94c: ldur            w5, [x16, #0xf]
    // 0xe7e950: DecompressPointer r5
    //     0xe7e950: add             x5, x5, HEAP, lsl #32
    // 0xe7e954: stur            x5, [fp, #-8]
    // 0xe7e958: cmp             x4, #0
    // 0xe7e95c: b.le            #0xe7ea08
    // 0xe7e960: r1 = LoadClassIdInstr(r5)
    //     0xe7e960: ldur            x1, [x5, #-1]
    //     0xe7e964: ubfx            x1, x1, #0xc, #0x14
    // 0xe7e968: cmp             x1, #0x38d
    // 0xe7e96c: b.ne            #0xe7e97c
    // 0xe7e970: mov             x4, x0
    // 0xe7e974: r5 = 32
    //     0xe7e974: movz            x5, #0x20
    // 0xe7e978: b               #0xe7ea10
    // 0xe7e97c: cmp             x1, #0x38a
    // 0xe7e980: b.ne            #0xe7e990
    // 0xe7e984: mov             x4, x0
    // 0xe7e988: r5 = 32
    //     0xe7e988: movz            x5, #0x20
    // 0xe7e98c: b               #0xe7ea10
    // 0xe7e990: cmp             x1, #0x393
    // 0xe7e994: b.ne            #0xe7e9a4
    // 0xe7e998: mov             x4, x0
    // 0xe7e99c: r5 = 32
    //     0xe7e99c: movz            x5, #0x20
    // 0xe7e9a0: b               #0xe7ea10
    // 0xe7e9a4: sub             x16, x1, #0x390
    // 0xe7e9a8: cmp             x16, #1
    // 0xe7e9ac: b.hi            #0xe7e9bc
    // 0xe7e9b0: mov             x4, x0
    // 0xe7e9b4: r5 = 32
    //     0xe7e9b4: movz            x5, #0x20
    // 0xe7e9b8: b               #0xe7ea10
    // 0xe7e9bc: mov             x1, x0
    // 0xe7e9c0: r2 = 1
    //     0xe7e9c0: movz            x2, #0x1
    // 0xe7e9c4: r0 = _ensureCapacity()
    //     0xe7e9c4: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7e9c8: ldur            x4, [fp, #-0x18]
    // 0xe7e9cc: LoadField: r2 = r4->field_7
    //     0xe7e9cc: ldur            w2, [x4, #7]
    // 0xe7e9d0: DecompressPointer r2
    //     0xe7e9d0: add             x2, x2, HEAP, lsl #32
    // 0xe7e9d4: LoadField: r3 = r4->field_b
    //     0xe7e9d4: ldur            x3, [x4, #0xb]
    // 0xe7e9d8: add             x0, x3, #1
    // 0xe7e9dc: StoreField: r4->field_b = r0
    //     0xe7e9dc: stur            x0, [x4, #0xb]
    // 0xe7e9e0: LoadField: r0 = r2->field_13
    //     0xe7e9e0: ldur            w0, [x2, #0x13]
    // 0xe7e9e4: r1 = LoadInt32Instr(r0)
    //     0xe7e9e4: sbfx            x1, x0, #1, #0x1f
    // 0xe7e9e8: mov             x0, x1
    // 0xe7e9ec: mov             x1, x3
    // 0xe7e9f0: cmp             x1, x0
    // 0xe7e9f4: b.hs            #0xe7ea70
    // 0xe7e9f8: r5 = 32
    //     0xe7e9f8: movz            x5, #0x20
    // 0xe7e9fc: ArrayStore: r2[r3] = r5  ; TypeUnknown_1
    //     0xe7e9fc: add             x0, x2, x3
    //     0xe7ea00: strb            w5, [x0, #0x17]
    // 0xe7ea04: b               #0xe7ea10
    // 0xe7ea08: mov             x4, x0
    // 0xe7ea0c: r5 = 32
    //     0xe7ea0c: movz            x5, #0x20
    // 0xe7ea10: ldur            x6, [fp, #-0x20]
    // 0xe7ea14: ldur            x1, [fp, #-8]
    // 0xe7ea18: r0 = LoadClassIdInstr(r1)
    //     0xe7ea18: ldur            x0, [x1, #-1]
    //     0xe7ea1c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ea20: ldur            x2, [fp, #-0x10]
    // 0xe7ea24: mov             x3, x4
    // 0xe7ea28: r0 = GDT[cid_x0 + -0xf87]()
    //     0xe7ea28: sub             lr, x0, #0xf87
    //     0xe7ea2c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ea30: blr             lr
    // 0xe7ea34: ldur            x0, [fp, #-0x20]
    // 0xe7ea38: add             x4, x0, #1
    // 0xe7ea3c: ldur            x3, [fp, #-0x28]
    // 0xe7ea40: b               #0xe7e91c
    // 0xe7ea44: ldur            x1, [fp, #-0x18]
    // 0xe7ea48: r2 = "]"
    //     0xe7ea48: ldr             x2, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xe7ea4c: r0 = putString()
    //     0xe7ea4c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ea50: r0 = Null
    //     0xe7ea50: mov             x0, NULL
    // 0xe7ea54: LeaveFrame
    //     0xe7ea54: mov             SP, fp
    //     0xe7ea58: ldp             fp, lr, [SP], #0x10
    // 0xe7ea5c: ret
    //     0xe7ea5c: ret             
    // 0xe7ea60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ea60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ea64: b               #0xe7e8f4
    // 0xe7ea68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ea68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ea6c: b               #0xe7e930
    // 0xe7ea70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7ea70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
