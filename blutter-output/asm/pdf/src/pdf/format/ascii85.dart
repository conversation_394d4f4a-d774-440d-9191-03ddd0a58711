// lib: , url: package:pdf/src/pdf/format/ascii85.dart

// class id: 1050780, size: 0x8
class :: {
}

// class id: 6443, size: 0xc, field offset: 0xc
class Ascii85Encoder extends Converter<dynamic, dynamic> {

  _ convert(/* No info */) {
    // ** addr: 0xcf9d50, size: 0x41c
    // 0xcf9d50: EnterFrame
    //     0xcf9d50: stp             fp, lr, [SP, #-0x10]!
    //     0xcf9d54: mov             fp, SP
    // 0xcf9d58: AllocStack(0x20)
    //     0xcf9d58: sub             SP, SP, #0x20
    // 0xcf9d5c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xcf9d5c: mov             x3, x2
    //     0xcf9d60: stur            x2, [fp, #-8]
    // 0xcf9d64: CheckStackOverflow
    //     0xcf9d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf9d68: cmp             SP, x16
    //     0xcf9d6c: b.ls            #0xcfa114
    // 0xcf9d70: mov             x0, x3
    // 0xcf9d74: r2 = Null
    //     0xcf9d74: mov             x2, NULL
    // 0xcf9d78: r1 = Null
    //     0xcf9d78: mov             x1, NULL
    // 0xcf9d7c: r4 = 60
    //     0xcf9d7c: movz            x4, #0x3c
    // 0xcf9d80: branchIfSmi(r0, 0xcf9d8c)
    //     0xcf9d80: tbz             w0, #0, #0xcf9d8c
    // 0xcf9d84: r4 = LoadClassIdInstr(r0)
    //     0xcf9d84: ldur            x4, [x0, #-1]
    //     0xcf9d88: ubfx            x4, x4, #0xc, #0x14
    // 0xcf9d8c: sub             x4, x4, #0x74
    // 0xcf9d90: cmp             x4, #3
    // 0xcf9d94: b.ls            #0xcf9dac
    // 0xcf9d98: r8 = Uint8List
    //     0xcf9d98: add             x8, PP, #0x10, lsl #12  ; [pp+0x10a00] Type: Uint8List
    //     0xcf9d9c: ldr             x8, [x8, #0xa00]
    // 0xcf9da0: r3 = Null
    //     0xcf9da0: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c058] Null
    //     0xcf9da4: ldr             x3, [x3, #0x58]
    // 0xcf9da8: r0 = Uint8List()
    //     0xcf9da8: bl              #0x5feb94  ; IsType_Uint8List_Stub
    // 0xcf9dac: ldur            x2, [fp, #-8]
    // 0xcf9db0: LoadField: r0 = r2->field_13
    //     0xcf9db0: ldur            w0, [x2, #0x13]
    // 0xcf9db4: r3 = LoadInt32Instr(r0)
    //     0xcf9db4: sbfx            x3, x0, #1, #0x1f
    // 0xcf9db8: stur            x3, [fp, #-0x18]
    // 0xcf9dbc: add             x0, x3, #3
    // 0xcf9dc0: r1 = 4
    //     0xcf9dc0: movz            x1, #0x4
    // 0xcf9dc4: sdiv            x4, x0, x1
    // 0xcf9dc8: r16 = 5
    //     0xcf9dc8: movz            x16, #0x5
    // 0xcf9dcc: mul             x0, x4, x16
    // 0xcf9dd0: add             x5, x0, #2
    // 0xcf9dd4: stur            x5, [fp, #-0x10]
    // 0xcf9dd8: r0 = BoxInt64Instr(r5)
    //     0xcf9dd8: sbfiz           x0, x5, #1, #0x1f
    //     0xcf9ddc: cmp             x5, x0, asr #1
    //     0xcf9de0: b.eq            #0xcf9dec
    //     0xcf9de4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcf9de8: stur            x5, [x0, #7]
    // 0xcf9dec: mov             x4, x0
    // 0xcf9df0: r0 = AllocateUint8Array()
    //     0xcf9df0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xcf9df4: mov             x2, x0
    // 0xcf9df8: ldur            x3, [fp, #-8]
    // 0xcf9dfc: ldur            x4, [fp, #-0x18]
    // 0xcf9e00: r8 = 0
    //     0xcf9e00: movz            x8, #0
    // 0xcf9e04: r7 = 0
    //     0xcf9e04: movz            x7, #0
    // 0xcf9e08: r6 = 122
    //     0xcf9e08: movz            x6, #0x7a
    // 0xcf9e0c: r5 = 85
    //     0xcf9e0c: movz            x5, #0x55
    // 0xcf9e10: CheckStackOverflow
    //     0xcf9e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf9e14: cmp             SP, x16
    //     0xcf9e18: b.ls            #0xcfa11c
    // 0xcf9e1c: cmp             x7, x4
    // 0xcf9e20: b.ge            #0xcfa09c
    // 0xcf9e24: ldur            x0, [fp, #-0x10]
    // 0xcf9e28: mov             x1, x8
    // 0xcf9e2c: cmp             x1, x0
    // 0xcf9e30: b.hs            #0xcfa124
    // 0xcf9e34: ArrayStore: r2[r8] = rZR  ; TypeUnknown_1
    //     0xcf9e34: add             x0, x2, x8
    //     0xcf9e38: strb            wzr, [x0, #0x17]
    // 0xcf9e3c: add             x9, x8, #1
    // 0xcf9e40: ldur            x0, [fp, #-0x10]
    // 0xcf9e44: mov             x1, x9
    // 0xcf9e48: cmp             x1, x0
    // 0xcf9e4c: b.hs            #0xcfa128
    // 0xcf9e50: ArrayStore: r2[r9] = rZR  ; TypeUnknown_1
    //     0xcf9e50: add             x0, x2, x9
    //     0xcf9e54: strb            wzr, [x0, #0x17]
    // 0xcf9e58: add             x10, x8, #2
    // 0xcf9e5c: ldur            x0, [fp, #-0x10]
    // 0xcf9e60: mov             x1, x10
    // 0xcf9e64: cmp             x1, x0
    // 0xcf9e68: b.hs            #0xcfa12c
    // 0xcf9e6c: ArrayStore: r2[r10] = rZR  ; TypeUnknown_1
    //     0xcf9e6c: add             x0, x2, x10
    //     0xcf9e70: strb            wzr, [x0, #0x17]
    // 0xcf9e74: add             x10, x8, #3
    // 0xcf9e78: ldur            x0, [fp, #-0x10]
    // 0xcf9e7c: mov             x1, x10
    // 0xcf9e80: cmp             x1, x0
    // 0xcf9e84: b.hs            #0xcfa130
    // 0xcf9e88: ArrayStore: r2[r10] = rZR  ; TypeUnknown_1
    //     0xcf9e88: add             x0, x2, x10
    //     0xcf9e8c: strb            wzr, [x0, #0x17]
    // 0xcf9e90: add             x10, x8, #4
    // 0xcf9e94: ldur            x0, [fp, #-0x10]
    // 0xcf9e98: mov             x1, x10
    // 0xcf9e9c: cmp             x1, x0
    // 0xcf9ea0: b.hs            #0xcfa134
    // 0xcf9ea4: ArrayStore: r2[r10] = rZR  ; TypeUnknown_1
    //     0xcf9ea4: add             x0, x2, x10
    //     0xcf9ea8: strb            wzr, [x0, #0x17]
    // 0xcf9eac: sub             x10, x4, x7
    // 0xcf9eb0: cmp             x10, #2
    // 0xcf9eb4: b.gt            #0xcf9f14
    // 0xcf9eb8: cmp             x10, #1
    // 0xcf9ebc: b.gt            #0xcf9edc
    // 0xcf9ec0: lsl             x0, x10, #1
    // 0xcf9ec4: cmp             w0, #2
    // 0xcf9ec8: b.ne            #0xcf9f7c
    // 0xcf9ecc: LoadField: r0 = r3->field_7
    //     0xcf9ecc: ldur            x0, [x3, #7]
    // 0xcf9ed0: ldrb            w1, [x0, x7]
    // 0xcf9ed4: lsl             x0, x1, #0x18
    // 0xcf9ed8: b               #0xcf9ff0
    // 0xcf9edc: LoadField: r0 = r3->field_7
    //     0xcf9edc: ldur            x0, [x3, #7]
    // 0xcf9ee0: ldrb            w1, [x0, x7]
    // 0xcf9ee4: lsl             x11, x1, #0x18
    // 0xcf9ee8: add             x12, x7, #1
    // 0xcf9eec: mov             x0, x4
    // 0xcf9ef0: mov             x1, x12
    // 0xcf9ef4: cmp             x1, x0
    // 0xcf9ef8: b.hs            #0xcfa138
    // 0xcf9efc: LoadField: r0 = r3->field_7
    //     0xcf9efc: ldur            x0, [x3, #7]
    // 0xcf9f00: ldrb            w1, [x0, x12]
    // 0xcf9f04: lsl             x0, x1, #0x10
    // 0xcf9f08: orr             x1, x11, x0
    // 0xcf9f0c: mov             x0, x1
    // 0xcf9f10: b               #0xcf9ff0
    // 0xcf9f14: lsl             x0, x10, #1
    // 0xcf9f18: cmp             w0, #6
    // 0xcf9f1c: b.ne            #0xcf9f7c
    // 0xcf9f20: LoadField: r0 = r3->field_7
    //     0xcf9f20: ldur            x0, [x3, #7]
    // 0xcf9f24: ldrb            w1, [x0, x7]
    // 0xcf9f28: lsl             x11, x1, #0x18
    // 0xcf9f2c: add             x12, x7, #1
    // 0xcf9f30: mov             x0, x4
    // 0xcf9f34: mov             x1, x12
    // 0xcf9f38: cmp             x1, x0
    // 0xcf9f3c: b.hs            #0xcfa13c
    // 0xcf9f40: LoadField: r0 = r3->field_7
    //     0xcf9f40: ldur            x0, [x3, #7]
    // 0xcf9f44: ldrb            w1, [x0, x12]
    // 0xcf9f48: lsl             x0, x1, #0x10
    // 0xcf9f4c: orr             x12, x11, x0
    // 0xcf9f50: add             x11, x7, #2
    // 0xcf9f54: mov             x0, x4
    // 0xcf9f58: mov             x1, x11
    // 0xcf9f5c: cmp             x1, x0
    // 0xcf9f60: b.hs            #0xcfa140
    // 0xcf9f64: LoadField: r0 = r3->field_7
    //     0xcf9f64: ldur            x0, [x3, #7]
    // 0xcf9f68: ldrb            w1, [x0, x11]
    // 0xcf9f6c: lsl             x0, x1, #8
    // 0xcf9f70: orr             x1, x12, x0
    // 0xcf9f74: mov             x0, x1
    // 0xcf9f78: b               #0xcf9ff0
    // 0xcf9f7c: LoadField: r0 = r3->field_7
    //     0xcf9f7c: ldur            x0, [x3, #7]
    // 0xcf9f80: ldrb            w1, [x0, x7]
    // 0xcf9f84: lsl             x11, x1, #0x18
    // 0xcf9f88: add             x12, x7, #1
    // 0xcf9f8c: mov             x0, x4
    // 0xcf9f90: mov             x1, x12
    // 0xcf9f94: cmp             x1, x0
    // 0xcf9f98: b.hs            #0xcfa144
    // 0xcf9f9c: LoadField: r0 = r3->field_7
    //     0xcf9f9c: ldur            x0, [x3, #7]
    // 0xcf9fa0: ldrb            w1, [x0, x12]
    // 0xcf9fa4: lsl             x0, x1, #0x10
    // 0xcf9fa8: orr             x12, x11, x0
    // 0xcf9fac: add             x11, x7, #2
    // 0xcf9fb0: mov             x0, x4
    // 0xcf9fb4: mov             x1, x11
    // 0xcf9fb8: cmp             x1, x0
    // 0xcf9fbc: b.hs            #0xcfa148
    // 0xcf9fc0: LoadField: r0 = r3->field_7
    //     0xcf9fc0: ldur            x0, [x3, #7]
    // 0xcf9fc4: ldrb            w1, [x0, x11]
    // 0xcf9fc8: lsl             x0, x1, #8
    // 0xcf9fcc: orr             x11, x12, x0
    // 0xcf9fd0: add             x12, x7, #3
    // 0xcf9fd4: mov             x0, x4
    // 0xcf9fd8: mov             x1, x12
    // 0xcf9fdc: cmp             x1, x0
    // 0xcf9fe0: b.hs            #0xcfa14c
    // 0xcf9fe4: LoadField: r0 = r3->field_7
    //     0xcf9fe4: ldur            x0, [x3, #7]
    // 0xcf9fe8: ldrb            w1, [x0, x12]
    // 0xcf9fec: orr             x0, x11, x1
    // 0xcf9ff0: cbnz            x0, #0xcfa014
    // 0xcf9ff4: cmp             x10, #4
    // 0xcf9ff8: b.lt            #0xcfa014
    // 0xcf9ffc: ArrayStore: r2[r8] = r6  ; TypeUnknown_1
    //     0xcf9ffc: add             x0, x2, x8
    //     0xcfa000: strb            w6, [x0, #0x17]
    // 0xcfa004: add             x0, x7, #4
    // 0xcfa008: mov             x8, x9
    // 0xcfa00c: mov             x7, x0
    // 0xcfa010: b               #0xcf9e10
    // 0xcfa014: mov             x11, x0
    // 0xcfa018: r9 = 4
    //     0xcfa018: movz            x9, #0x4
    // 0xcfa01c: CheckStackOverflow
    //     0xcfa01c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfa020: cmp             SP, x16
    //     0xcfa024: b.ls            #0xcfa150
    // 0xcfa028: tbnz            x9, #0x3f, #0xcfa070
    // 0xcfa02c: add             x12, x8, x9
    // 0xcfa030: sdiv            x1, x11, x5
    // 0xcfa034: msub            x0, x1, x5, x11
    // 0xcfa038: cmp             x0, xzr
    // 0xcfa03c: b.lt            #0xcfa158
    // 0xcfa040: add             x13, x0, #0x21
    // 0xcfa044: ldur            x0, [fp, #-0x10]
    // 0xcfa048: mov             x1, x12
    // 0xcfa04c: cmp             x1, x0
    // 0xcfa050: b.hs            #0xcfa160
    // 0xcfa054: ArrayStore: r2[r12] = r13  ; TypeUnknown_1
    //     0xcfa054: add             x0, x2, x12
    //     0xcfa058: strb            w13, [x0, #0x17]
    // 0xcfa05c: sdiv            x0, x11, x5
    // 0xcfa060: sub             x1, x9, #1
    // 0xcfa064: mov             x11, x0
    // 0xcfa068: mov             x9, x1
    // 0xcfa06c: b               #0xcfa01c
    // 0xcfa070: cmp             x10, #4
    // 0xcfa074: b.lt            #0xcfa08c
    // 0xcfa078: add             x0, x7, #4
    // 0xcfa07c: add             x1, x8, #5
    // 0xcfa080: mov             x8, x1
    // 0xcfa084: mov             x7, x0
    // 0xcfa088: b               #0xcf9e10
    // 0xcfa08c: add             x0, x10, #1
    // 0xcfa090: add             x1, x8, x0
    // 0xcfa094: mov             x5, x1
    // 0xcfa098: b               #0xcfa0a0
    // 0xcfa09c: mov             x5, x8
    // 0xcfa0a0: r4 = 126
    //     0xcfa0a0: movz            x4, #0x7e
    // 0xcfa0a4: r3 = 62
    //     0xcfa0a4: movz            x3, #0x3e
    // 0xcfa0a8: add             x6, x5, #1
    // 0xcfa0ac: ldur            x0, [fp, #-0x10]
    // 0xcfa0b0: mov             x1, x5
    // 0xcfa0b4: cmp             x1, x0
    // 0xcfa0b8: b.hs            #0xcfa164
    // 0xcfa0bc: ArrayStore: r2[r5] = r4  ; TypeUnknown_1
    //     0xcfa0bc: add             x0, x2, x5
    //     0xcfa0c0: strb            w4, [x0, #0x17]
    // 0xcfa0c4: add             x4, x6, #1
    // 0xcfa0c8: ldur            x0, [fp, #-0x10]
    // 0xcfa0cc: mov             x1, x6
    // 0xcfa0d0: cmp             x1, x0
    // 0xcfa0d4: b.hs            #0xcfa168
    // 0xcfa0d8: ArrayStore: r2[r6] = r3  ; TypeUnknown_1
    //     0xcfa0d8: add             x0, x2, x6
    //     0xcfa0dc: strb            w3, [x0, #0x17]
    // 0xcfa0e0: r0 = BoxInt64Instr(r4)
    //     0xcfa0e0: sbfiz           x0, x4, #1, #0x1f
    //     0xcfa0e4: cmp             x4, x0, asr #1
    //     0xcfa0e8: b.eq            #0xcfa0f4
    //     0xcfa0ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcfa0f0: stur            x4, [x0, #7]
    // 0xcfa0f4: str             x0, [SP]
    // 0xcfa0f8: mov             x1, x2
    // 0xcfa0fc: r2 = 0
    //     0xcfa0fc: movz            x2, #0
    // 0xcfa100: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xcfa100: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xcfa104: r0 = sublist()
    //     0xcfa104: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xcfa108: LeaveFrame
    //     0xcfa108: mov             SP, fp
    //     0xcfa10c: ldp             fp, lr, [SP], #0x10
    // 0xcfa110: ret
    //     0xcfa110: ret             
    // 0xcfa114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfa114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfa118: b               #0xcf9d70
    // 0xcfa11c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfa11c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfa120: b               #0xcf9e1c
    // 0xcfa124: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa124: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa128: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa12c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa12c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa130: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa130: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa134: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa134: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa138: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa138: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa13c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa13c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa140: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa140: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa144: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa144: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa148: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa14c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa14c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfa150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfa154: b               #0xcfa028
    // 0xcfa158: add             x0, x0, x5
    // 0xcfa15c: b               #0xcfa040
    // 0xcfa160: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa160: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa164: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa164: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcfa168: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa168: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
