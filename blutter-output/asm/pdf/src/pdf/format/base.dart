// lib: , url: package:pdf/src/pdf/format/base.dart

// class id: 1050781, size: 0x8
class :: {
}

// class id: 903, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class PdfDataType extends Object {

  Uint8List toList(PdfDataType) {
    // ** addr: 0x7b5780, size: 0x48
    // 0x7b5780: EnterFrame
    //     0x7b5780: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5784: mov             fp, SP
    // 0x7b5788: CheckStackOverflow
    //     0x7b5788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b578c: cmp             SP, x16
    //     0x7b5790: b.ls            #0x7b57a8
    // 0x7b5794: ldr             x1, [fp, #0x10]
    // 0x7b5798: r0 = toList()
    //     0x7b5798: bl              #0x7b57b0  ; [package:pdf/src/pdf/format/base.dart] PdfDataType::toList
    // 0x7b579c: LeaveFrame
    //     0x7b579c: mov             SP, fp
    //     0x7b57a0: ldp             fp, lr, [SP], #0x10
    // 0x7b57a4: ret
    //     0x7b57a4: ret             
    // 0x7b57a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b57a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b57ac: b               #0x7b5794
  }
  Uint8List toList(PdfDataType) {
    // ** addr: 0x7b57b0, size: 0x34
    // 0x7b57b0: EnterFrame
    //     0x7b57b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7b57b4: mov             fp, SP
    // 0x7b57b8: CheckStackOverflow
    //     0x7b57b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b57bc: cmp             SP, x16
    //     0x7b57c0: b.ls            #0x7b57dc
    // 0x7b57c4: r0 = _toStream()
    //     0x7b57c4: bl              #0x7b5844  ; [package:pdf/src/pdf/format/base.dart] PdfDataType::_toStream
    // 0x7b57c8: mov             x1, x0
    // 0x7b57cc: r0 = output()
    //     0x7b57cc: bl              #0x7b57e4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::output
    // 0x7b57d0: LeaveFrame
    //     0x7b57d0: mov             SP, fp
    //     0x7b57d4: ldp             fp, lr, [SP], #0x10
    // 0x7b57d8: ret
    //     0x7b57d8: ret             
    // 0x7b57dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b57dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b57e0: b               #0x7b57c4
  }
  _ _toStream(/* No info */) {
    // ** addr: 0x7b5844, size: 0xa8
    // 0x7b5844: EnterFrame
    //     0x7b5844: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5848: mov             fp, SP
    // 0x7b584c: AllocStack(0x18)
    //     0x7b584c: sub             SP, SP, #0x18
    // 0x7b5850: SetupParameters(PdfDataType this /* r1 => r1, fp-0x8 */)
    //     0x7b5850: stur            x1, [fp, #-8]
    // 0x7b5854: CheckStackOverflow
    //     0x7b5854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5858: cmp             SP, x16
    //     0x7b585c: b.ls            #0x7b58e4
    // 0x7b5860: r0 = PdfStream()
    //     0x7b5860: bl              #0x7b58f8  ; AllocatePdfStreamStub -> PdfStream (size=0x14)
    // 0x7b5864: stur            x0, [fp, #-0x10]
    // 0x7b5868: StoreField: r0->field_b = rZR
    //     0x7b5868: stur            xzr, [x0, #0xb]
    // 0x7b586c: r4 = 2
    //     0x7b586c: movz            x4, #0x2, lsl #16
    // 0x7b5870: r0 = AllocateUint8Array()
    //     0x7b5870: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b5874: ldur            x3, [fp, #-0x10]
    // 0x7b5878: StoreField: r3->field_7 = r0
    //     0x7b5878: stur            w0, [x3, #7]
    // 0x7b587c: r1 = <PdfDataType>
    //     0x7b587c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b5880: ldr             x1, [x1, #0x4c8]
    // 0x7b5884: r0 = PdfObjectBase()
    //     0x7b5884: bl              #0x7b58ec  ; AllocatePdfObjectBaseStub -> PdfObjectBase<X0 bound PdfDataType> (size=0x24)
    // 0x7b5888: stur            x0, [fp, #-0x18]
    // 0x7b588c: StoreField: r0->field_b = rZR
    //     0x7b588c: stur            xzr, [x0, #0xb]
    // 0x7b5890: StoreField: r0->field_13 = rZR
    //     0x7b5890: stur            xzr, [x0, #0x13]
    // 0x7b5894: ldur            x3, [fp, #-8]
    // 0x7b5898: StoreField: r0->field_1b = r3
    //     0x7b5898: stur            w3, [x0, #0x1b]
    // 0x7b589c: r1 = Instance_PdfSettings
    //     0x7b589c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314d0] Obj!PdfSettings@e0c751
    //     0x7b58a0: ldr             x1, [x1, #0x4d0]
    // 0x7b58a4: StoreField: r0->field_1f = r1
    //     0x7b58a4: stur            w1, [x0, #0x1f]
    // 0x7b58a8: r1 = <String>
    //     0x7b58a8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7b58ac: r2 = 0
    //     0x7b58ac: movz            x2, #0
    // 0x7b58b0: r0 = _GrowableList()
    //     0x7b58b0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b58b4: ldur            x1, [fp, #-8]
    // 0x7b58b8: r0 = LoadClassIdInstr(r1)
    //     0x7b58b8: ldur            x0, [x1, #-1]
    //     0x7b58bc: ubfx            x0, x0, #0xc, #0x14
    // 0x7b58c0: ldur            x2, [fp, #-0x18]
    // 0x7b58c4: ldur            x3, [fp, #-0x10]
    // 0x7b58c8: r0 = GDT[cid_x0 + -0xf87]()
    //     0x7b58c8: sub             lr, x0, #0xf87
    //     0x7b58cc: ldr             lr, [x21, lr, lsl #3]
    //     0x7b58d0: blr             lr
    // 0x7b58d4: ldur            x0, [fp, #-0x10]
    // 0x7b58d8: LeaveFrame
    //     0x7b58d8: mov             SP, fp
    //     0x7b58dc: ldp             fp, lr, [SP], #0x10
    // 0x7b58e0: ret
    //     0x7b58e0: ret             
    // 0x7b58e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b58e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b58e8: b               #0x7b5860
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3533c, size: 0x58
    // 0xc3533c: EnterFrame
    //     0xc3533c: stp             fp, lr, [SP, #-0x10]!
    //     0xc35340: mov             fp, SP
    // 0xc35344: LoadField: r0 = r4->field_13
    //     0xc35344: ldur            w0, [x4, #0x13]
    // 0xc35348: sub             x1, x0, #2
    // 0xc3534c: add             x0, fp, w1, sxtw #2
    // 0xc35350: ldr             x0, [x0, #0x10]
    // 0xc35354: CheckStackOverflow
    //     0xc35354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35358: cmp             SP, x16
    //     0xc3535c: b.ls            #0xc3538c
    // 0xc35360: mov             x1, x0
    // 0xc35364: r0 = _toStream()
    //     0xc35364: bl              #0x7b5844  ; [package:pdf/src/pdf/format/base.dart] PdfDataType::_toStream
    // 0xc35368: mov             x1, x0
    // 0xc3536c: r0 = output()
    //     0xc3536c: bl              #0x7b57e4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::output
    // 0xc35370: mov             x1, x0
    // 0xc35374: r2 = 0
    //     0xc35374: movz            x2, #0
    // 0xc35378: r3 = Null
    //     0xc35378: mov             x3, NULL
    // 0xc3537c: r0 = createFromCharCodes()
    //     0xc3537c: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xc35380: LeaveFrame
    //     0xc35380: mov             SP, fp
    //     0xc35384: ldp             fp, lr, [SP], #0x10
    // 0xc35388: ret
    //     0xc35388: ret             
    // 0xc3538c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3538c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35390: b               #0xc35360
  }
}
