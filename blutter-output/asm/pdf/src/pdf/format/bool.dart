// lib: , url: package:pdf/src/pdf/format/bool.dart

// class id: 1050782, size: 0x8
class :: {
}

// class id: 914, size: 0xc, field offset: 0x8
//   const constructor, 
class PdfBool extends PdfDataType {

  bool field_8;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf189c, size: 0x28
    // 0xbf189c: ldr             x1, [SP]
    // 0xbf18a0: LoadField: r2 = r1->field_7
    //     0xbf18a0: ldur            w2, [x1, #7]
    // 0xbf18a4: DecompressPointer r2
    //     0xbf18a4: add             x2, x2, HEAP, lsl #32
    // 0xbf18a8: tst             x2, #0x10
    // 0xbf18ac: cset            x0, ne
    // 0xbf18b0: sub             x0, x0, #1
    // 0xbf18b4: r16 = -12
    //     0xbf18b4: movn            x16, #0xb
    // 0xbf18b8: and             x0, x0, x16
    // 0xbf18bc: add             x0, x0, #0x9aa
    // 0xbf18c0: ret
    //     0xbf18c0: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c5ac, size: 0x5c
    // 0xd7c5ac: ldr             x1, [SP]
    // 0xd7c5b0: cmp             w1, NULL
    // 0xd7c5b4: b.ne            #0xd7c5c0
    // 0xd7c5b8: r0 = false
    //     0xd7c5b8: add             x0, NULL, #0x30  ; false
    // 0xd7c5bc: ret
    //     0xd7c5bc: ret             
    // 0xd7c5c0: r2 = 60
    //     0xd7c5c0: movz            x2, #0x3c
    // 0xd7c5c4: branchIfSmi(r1, 0xd7c5d0)
    //     0xd7c5c4: tbz             w1, #0, #0xd7c5d0
    // 0xd7c5c8: r2 = LoadClassIdInstr(r1)
    //     0xd7c5c8: ldur            x2, [x1, #-1]
    //     0xd7c5cc: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c5d0: cmp             x2, #0x392
    // 0xd7c5d4: b.ne            #0xd7c600
    // 0xd7c5d8: ldr             x2, [SP, #8]
    // 0xd7c5dc: LoadField: r3 = r2->field_7
    //     0xd7c5dc: ldur            w3, [x2, #7]
    // 0xd7c5e0: DecompressPointer r3
    //     0xd7c5e0: add             x3, x3, HEAP, lsl #32
    // 0xd7c5e4: LoadField: r2 = r1->field_7
    //     0xd7c5e4: ldur            w2, [x1, #7]
    // 0xd7c5e8: DecompressPointer r2
    //     0xd7c5e8: add             x2, x2, HEAP, lsl #32
    // 0xd7c5ec: cmp             w3, w2
    // 0xd7c5f0: r16 = true
    //     0xd7c5f0: add             x16, NULL, #0x20  ; true
    // 0xd7c5f4: r17 = false
    //     0xd7c5f4: add             x17, NULL, #0x30  ; false
    // 0xd7c5f8: csel            x0, x16, x17, eq
    // 0xd7c5fc: ret
    //     0xd7c5fc: ret             
    // 0xd7c600: r0 = false
    //     0xd7c600: add             x0, NULL, #0x30  ; false
    // 0xd7c604: ret
    //     0xd7c604: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7ea74, size: 0x50
    // 0xe7ea74: EnterFrame
    //     0xe7ea74: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ea78: mov             fp, SP
    // 0xe7ea7c: mov             x0, x1
    // 0xe7ea80: mov             x1, x3
    // 0xe7ea84: CheckStackOverflow
    //     0xe7ea84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ea88: cmp             SP, x16
    //     0xe7ea8c: b.ls            #0xe7eabc
    // 0xe7ea90: LoadField: r2 = r0->field_7
    //     0xe7ea90: ldur            w2, [x0, #7]
    // 0xe7ea94: DecompressPointer r2
    //     0xe7ea94: add             x2, x2, HEAP, lsl #32
    // 0xe7ea98: tbnz            w2, #4, #0xe7eaa4
    // 0xe7ea9c: r2 = "true"
    //     0xe7ea9c: ldr             x2, [PP, #0x13a0]  ; [pp+0x13a0] "true"
    // 0xe7eaa0: b               #0xe7eaa8
    // 0xe7eaa4: r2 = "false"
    //     0xe7eaa4: ldr             x2, [PP, #0x13a8]  ; [pp+0x13a8] "false"
    // 0xe7eaa8: r0 = putString()
    //     0xe7eaa8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7eaac: r0 = Null
    //     0xe7eaac: mov             x0, NULL
    // 0xe7eab0: LeaveFrame
    //     0xe7eab0: mov             SP, fp
    //     0xe7eab4: ldp             fp, lr, [SP], #0x10
    // 0xe7eab8: ret
    //     0xe7eab8: ret             
    // 0xe7eabc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7eabc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7eac0: b               #0xe7ea90
  }
}
