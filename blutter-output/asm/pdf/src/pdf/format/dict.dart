// lib: , url: package:pdf/src/pdf/format/dict.dart

// class id: 1050784, size: 0x8
class :: {
}

// class id: 912, size: 0x10, field offset: 0x8
class PdfDict<X0 bound PdfDataType> extends PdfDataType {

  _ []=(/* No info */) {
    // ** addr: 0x7b551c, size: 0x94
    // 0x7b551c: EnterFrame
    //     0x7b551c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5520: mov             fp, SP
    // 0x7b5524: AllocStack(0x18)
    //     0x7b5524: sub             SP, SP, #0x18
    // 0x7b5528: SetupParameters(PdfDict<X0 bound PdfDataType> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b5528: mov             x5, x1
    //     0x7b552c: mov             x4, x2
    //     0x7b5530: stur            x1, [fp, #-8]
    //     0x7b5534: stur            x2, [fp, #-0x10]
    //     0x7b5538: stur            x3, [fp, #-0x18]
    // 0x7b553c: CheckStackOverflow
    //     0x7b553c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5540: cmp             SP, x16
    //     0x7b5544: b.ls            #0x7b55a8
    // 0x7b5548: LoadField: r2 = r5->field_7
    //     0x7b5548: ldur            w2, [x5, #7]
    // 0x7b554c: DecompressPointer r2
    //     0x7b554c: add             x2, x2, HEAP, lsl #32
    // 0x7b5550: mov             x0, x3
    // 0x7b5554: r1 = Null
    //     0x7b5554: mov             x1, NULL
    // 0x7b5558: cmp             w2, NULL
    // 0x7b555c: b.eq            #0x7b5580
    // 0x7b5560: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b5560: ldur            w4, [x2, #0x17]
    // 0x7b5564: DecompressPointer r4
    //     0x7b5564: add             x4, x4, HEAP, lsl #32
    // 0x7b5568: r8 = X0 bound PdfDataType
    //     0x7b5568: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b556c: ldr             x8, [x8, #0x6d8]
    // 0x7b5570: LoadField: r9 = r4->field_7
    //     0x7b5570: ldur            x9, [x4, #7]
    // 0x7b5574: r3 = Null
    //     0x7b5574: add             x3, PP, #0x36, lsl #12  ; [pp+0x366e0] Null
    //     0x7b5578: ldr             x3, [x3, #0x6e0]
    // 0x7b557c: blr             x9
    // 0x7b5580: ldur            x0, [fp, #-8]
    // 0x7b5584: LoadField: r1 = r0->field_b
    //     0x7b5584: ldur            w1, [x0, #0xb]
    // 0x7b5588: DecompressPointer r1
    //     0x7b5588: add             x1, x1, HEAP, lsl #32
    // 0x7b558c: ldur            x2, [fp, #-0x10]
    // 0x7b5590: ldur            x3, [fp, #-0x18]
    // 0x7b5594: r0 = []=()
    //     0x7b5594: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5598: r0 = Null
    //     0x7b5598: mov             x0, NULL
    // 0x7b559c: LeaveFrame
    //     0x7b559c: mov             SP, fp
    //     0x7b55a0: ldp             fp, lr, [SP], #0x10
    // 0x7b55a4: ret
    //     0x7b55a4: ret             
    // 0x7b55a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b55a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b55ac: b               #0x7b5548
  }
  X0? [](PdfDict<X0>, String) {
    // ** addr: 0x7b55c8, size: 0x84
    // 0x7b55c8: EnterFrame
    //     0x7b55c8: stp             fp, lr, [SP, #-0x10]!
    //     0x7b55cc: mov             fp, SP
    // 0x7b55d0: CheckStackOverflow
    //     0x7b55d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b55d4: cmp             SP, x16
    //     0x7b55d8: b.ls            #0x7b562c
    // 0x7b55dc: ldr             x0, [fp, #0x10]
    // 0x7b55e0: r2 = Null
    //     0x7b55e0: mov             x2, NULL
    // 0x7b55e4: r1 = Null
    //     0x7b55e4: mov             x1, NULL
    // 0x7b55e8: r4 = 60
    //     0x7b55e8: movz            x4, #0x3c
    // 0x7b55ec: branchIfSmi(r0, 0x7b55f8)
    //     0x7b55ec: tbz             w0, #0, #0x7b55f8
    // 0x7b55f0: r4 = LoadClassIdInstr(r0)
    //     0x7b55f0: ldur            x4, [x0, #-1]
    //     0x7b55f4: ubfx            x4, x4, #0xc, #0x14
    // 0x7b55f8: sub             x4, x4, #0x5e
    // 0x7b55fc: cmp             x4, #1
    // 0x7b5600: b.ls            #0x7b5614
    // 0x7b5604: r8 = String
    //     0x7b5604: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7b5608: r3 = Null
    //     0x7b5608: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3edf0] Null
    //     0x7b560c: ldr             x3, [x3, #0xdf0]
    // 0x7b5610: r0 = String()
    //     0x7b5610: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7b5614: ldr             x1, [fp, #0x18]
    // 0x7b5618: ldr             x2, [fp, #0x10]
    // 0x7b561c: r0 = getClipPath()
    //     0x7b561c: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7b5620: LeaveFrame
    //     0x7b5620: mov             SP, fp
    //     0x7b5624: ldp             fp, lr, [SP], #0x10
    // 0x7b5628: ret
    //     0x7b5628: ret             
    // 0x7b562c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b562c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5630: b               #0x7b55dc
  }
  _ PdfDict(/* No info */) {
    // ** addr: 0x7b5d6c, size: 0xc8
    // 0x7b5d6c: EnterFrame
    //     0x7b5d6c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5d70: mov             fp, SP
    // 0x7b5d74: AllocStack(0x28)
    //     0x7b5d74: sub             SP, SP, #0x28
    // 0x7b5d78: SetupParameters(PdfDict<X0 bound PdfDataType> this /* r1 => r0, fp-0x10 */, [dynamic _ = Null /* r4, fp-0x8 */])
    //     0x7b5d78: mov             x0, x1
    //     0x7b5d7c: stur            x1, [fp, #-0x10]
    //     0x7b5d80: ldur            w1, [x4, #0x13]
    //     0x7b5d84: sub             x2, x1, #2
    //     0x7b5d88: cmp             w2, #2
    //     0x7b5d8c: b.lt            #0x7b5da0
    //     0x7b5d90: add             x1, fp, w2, sxtw #2
    //     0x7b5d94: ldr             x1, [x1, #8]
    //     0x7b5d98: mov             x4, x1
    //     0x7b5d9c: b               #0x7b5da4
    //     0x7b5da0: mov             x4, NULL
    //     0x7b5da4: stur            x4, [fp, #-8]
    // 0x7b5da8: CheckStackOverflow
    //     0x7b5da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5dac: cmp             SP, x16
    //     0x7b5db0: b.ls            #0x7b5e2c
    // 0x7b5db4: LoadField: r2 = r0->field_7
    //     0x7b5db4: ldur            w2, [x0, #7]
    // 0x7b5db8: DecompressPointer r2
    //     0x7b5db8: add             x2, x2, HEAP, lsl #32
    // 0x7b5dbc: r1 = Null
    //     0x7b5dbc: mov             x1, NULL
    // 0x7b5dc0: r3 = <String, X0 bound PdfDataType>
    //     0x7b5dc0: add             x3, PP, #0x36, lsl #12  ; [pp+0x366f8] TypeArguments: <String, X0 bound PdfDataType>
    //     0x7b5dc4: ldr             x3, [x3, #0x6f8]
    // 0x7b5dc8: r30 = InstantiateTypeArgumentsStub
    //     0x7b5dc8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7b5dcc: LoadField: r30 = r30->field_7
    //     0x7b5dcc: ldur            lr, [lr, #7]
    // 0x7b5dd0: blr             lr
    // 0x7b5dd4: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0x7b5dd8: stp             x16, x0, [SP]
    // 0x7b5ddc: r0 = Map._fromLiteral()
    //     0x7b5ddc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b5de0: ldur            x2, [fp, #-8]
    // 0x7b5de4: stur            x0, [fp, #-0x18]
    // 0x7b5de8: cmp             w2, NULL
    // 0x7b5dec: b.eq            #0x7b5df8
    // 0x7b5df0: mov             x1, x0
    // 0x7b5df4: r0 = addAll()
    //     0x7b5df4: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x7b5df8: ldur            x1, [fp, #-0x10]
    // 0x7b5dfc: ldur            x0, [fp, #-0x18]
    // 0x7b5e00: StoreField: r1->field_b = r0
    //     0x7b5e00: stur            w0, [x1, #0xb]
    //     0x7b5e04: ldurb           w16, [x1, #-1]
    //     0x7b5e08: ldurb           w17, [x0, #-1]
    //     0x7b5e0c: and             x16, x17, x16, lsr #2
    //     0x7b5e10: tst             x16, HEAP, lsr #32
    //     0x7b5e14: b.eq            #0x7b5e1c
    //     0x7b5e18: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b5e1c: r0 = Null
    //     0x7b5e1c: mov             x0, NULL
    // 0x7b5e20: LeaveFrame
    //     0x7b5e20: mov             SP, fp
    //     0x7b5e24: ldp             fp, lr, [SP], #0x10
    // 0x7b5e28: ret
    //     0x7b5e28: ret             
    // 0x7b5e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b5e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5e30: b               #0x7b5db4
  }
  _ merge(/* No info */) {
    // ** addr: 0x7caddc, size: 0x358
    // 0x7caddc: EnterFrame
    //     0x7caddc: stp             fp, lr, [SP, #-0x10]!
    //     0x7cade0: mov             fp, SP
    // 0x7cade4: AllocStack(0x40)
    //     0x7cade4: sub             SP, SP, #0x40
    // 0x7cade8: SetupParameters(PdfDict<X0 bound PdfDataType> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7cade8: mov             x4, x1
    //     0x7cadec: mov             x3, x2
    //     0x7cadf0: stur            x1, [fp, #-8]
    //     0x7cadf4: stur            x2, [fp, #-0x10]
    // 0x7cadf8: CheckStackOverflow
    //     0x7cadf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cadfc: cmp             SP, x16
    //     0x7cae00: b.ls            #0x7cb120
    // 0x7cae04: LoadField: r2 = r4->field_7
    //     0x7cae04: ldur            w2, [x4, #7]
    // 0x7cae08: DecompressPointer r2
    //     0x7cae08: add             x2, x2, HEAP, lsl #32
    // 0x7cae0c: mov             x0, x3
    // 0x7cae10: r1 = Null
    //     0x7cae10: mov             x1, NULL
    // 0x7cae14: r8 = PdfDict<X0 bound PdfDataType>
    //     0x7cae14: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3ef40] Type: PdfDict<X0 bound PdfDataType>
    //     0x7cae18: ldr             x8, [x8, #0xf40]
    // 0x7cae1c: LoadField: r9 = r8->field_7
    //     0x7cae1c: ldur            x9, [x8, #7]
    // 0x7cae20: r3 = Null
    //     0x7cae20: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef48] Null
    //     0x7cae24: ldr             x3, [x3, #0xf48]
    // 0x7cae28: blr             x9
    // 0x7cae2c: ldur            x0, [fp, #-0x10]
    // 0x7cae30: LoadField: r2 = r0->field_b
    //     0x7cae30: ldur            w2, [x0, #0xb]
    // 0x7cae34: DecompressPointer r2
    //     0x7cae34: add             x2, x2, HEAP, lsl #32
    // 0x7cae38: stur            x2, [fp, #-0x18]
    // 0x7cae3c: LoadField: r1 = r2->field_7
    //     0x7cae3c: ldur            w1, [x2, #7]
    // 0x7cae40: DecompressPointer r1
    //     0x7cae40: add             x1, x1, HEAP, lsl #32
    // 0x7cae44: r0 = _CompactIterable()
    //     0x7cae44: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7cae48: mov             x1, x0
    // 0x7cae4c: ldur            x0, [fp, #-0x18]
    // 0x7cae50: StoreField: r1->field_b = r0
    //     0x7cae50: stur            w0, [x1, #0xb]
    // 0x7cae54: r2 = -2
    //     0x7cae54: orr             x2, xzr, #0xfffffffffffffffe
    // 0x7cae58: StoreField: r1->field_f = r2
    //     0x7cae58: stur            x2, [x1, #0xf]
    // 0x7cae5c: r2 = 2
    //     0x7cae5c: movz            x2, #0x2
    // 0x7cae60: ArrayStore: r1[0] = r2  ; List_8
    //     0x7cae60: stur            x2, [x1, #0x17]
    // 0x7cae64: r0 = iterator()
    //     0x7cae64: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x7cae68: mov             x2, x0
    // 0x7cae6c: ldur            x0, [fp, #-8]
    // 0x7cae70: stur            x2, [fp, #-0x30]
    // 0x7cae74: LoadField: r3 = r0->field_b
    //     0x7cae74: ldur            w3, [x0, #0xb]
    // 0x7cae78: DecompressPointer r3
    //     0x7cae78: add             x3, x3, HEAP, lsl #32
    // 0x7cae7c: stur            x3, [fp, #-0x28]
    // 0x7cae80: LoadField: r0 = r3->field_7
    //     0x7cae80: ldur            w0, [x3, #7]
    // 0x7cae84: DecompressPointer r0
    //     0x7cae84: add             x0, x0, HEAP, lsl #32
    // 0x7cae88: stur            x0, [fp, #-0x20]
    // 0x7cae8c: LoadField: r4 = r2->field_7
    //     0x7cae8c: ldur            w4, [x2, #7]
    // 0x7cae90: DecompressPointer r4
    //     0x7cae90: add             x4, x4, HEAP, lsl #32
    // 0x7cae94: stur            x4, [fp, #-0x10]
    // 0x7cae98: ldur            x5, [fp, #-0x18]
    // 0x7cae9c: stur            x5, [fp, #-8]
    // 0x7caea0: CheckStackOverflow
    //     0x7caea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7caea4: cmp             SP, x16
    //     0x7caea8: b.ls            #0x7cb128
    // 0x7caeac: mov             x1, x2
    // 0x7caeb0: r0 = moveNext()
    //     0x7caeb0: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x7caeb4: tbnz            w0, #4, #0x7cb110
    // 0x7caeb8: ldur            x3, [fp, #-0x30]
    // 0x7caebc: LoadField: r4 = r3->field_33
    //     0x7caebc: ldur            w4, [x3, #0x33]
    // 0x7caec0: DecompressPointer r4
    //     0x7caec0: add             x4, x4, HEAP, lsl #32
    // 0x7caec4: stur            x4, [fp, #-0x18]
    // 0x7caec8: cmp             w4, NULL
    // 0x7caecc: b.ne            #0x7caf00
    // 0x7caed0: mov             x0, x4
    // 0x7caed4: ldur            x2, [fp, #-0x10]
    // 0x7caed8: r1 = Null
    //     0x7caed8: mov             x1, NULL
    // 0x7caedc: cmp             w2, NULL
    // 0x7caee0: b.eq            #0x7caf00
    // 0x7caee4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7caee4: ldur            w4, [x2, #0x17]
    // 0x7caee8: DecompressPointer r4
    //     0x7caee8: add             x4, x4, HEAP, lsl #32
    // 0x7caeec: r8 = X0
    //     0x7caeec: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7caef0: LoadField: r9 = r4->field_7
    //     0x7caef0: ldur            x9, [x4, #7]
    // 0x7caef4: r3 = Null
    //     0x7caef4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef58] Null
    //     0x7caef8: ldr             x3, [x3, #0xf58]
    // 0x7caefc: blr             x9
    // 0x7caf00: ldur            x0, [fp, #-8]
    // 0x7caf04: mov             x1, x0
    // 0x7caf08: ldur            x2, [fp, #-0x18]
    // 0x7caf0c: r0 = _getValueOrData()
    //     0x7caf0c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7caf10: ldur            x5, [fp, #-8]
    // 0x7caf14: LoadField: r1 = r5->field_f
    //     0x7caf14: ldur            w1, [x5, #0xf]
    // 0x7caf18: DecompressPointer r1
    //     0x7caf18: add             x1, x1, HEAP, lsl #32
    // 0x7caf1c: cmp             w1, w0
    // 0x7caf20: b.ne            #0x7caf2c
    // 0x7caf24: r3 = Null
    //     0x7caf24: mov             x3, NULL
    // 0x7caf28: b               #0x7caf30
    // 0x7caf2c: mov             x3, x0
    // 0x7caf30: ldur            x0, [fp, #-0x28]
    // 0x7caf34: stur            x3, [fp, #-0x38]
    // 0x7caf38: cmp             w3, NULL
    // 0x7caf3c: b.eq            #0x7cb130
    // 0x7caf40: mov             x1, x0
    // 0x7caf44: ldur            x2, [fp, #-0x18]
    // 0x7caf48: r0 = _getValueOrData()
    //     0x7caf48: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7caf4c: ldur            x3, [fp, #-0x28]
    // 0x7caf50: LoadField: r1 = r3->field_f
    //     0x7caf50: ldur            w1, [x3, #0xf]
    // 0x7caf54: DecompressPointer r1
    //     0x7caf54: add             x1, x1, HEAP, lsl #32
    // 0x7caf58: cmp             w1, w0
    // 0x7caf5c: b.ne            #0x7caf64
    // 0x7caf60: r0 = Null
    //     0x7caf60: mov             x0, NULL
    // 0x7caf64: stur            x0, [fp, #-0x40]
    // 0x7caf68: cmp             w0, NULL
    // 0x7caf6c: b.ne            #0x7caff4
    // 0x7caf70: ldur            x0, [fp, #-0x18]
    // 0x7caf74: ldur            x2, [fp, #-0x20]
    // 0x7caf78: r1 = Null
    //     0x7caf78: mov             x1, NULL
    // 0x7caf7c: cmp             w2, NULL
    // 0x7caf80: b.eq            #0x7cafa0
    // 0x7caf84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7caf84: ldur            w4, [x2, #0x17]
    // 0x7caf88: DecompressPointer r4
    //     0x7caf88: add             x4, x4, HEAP, lsl #32
    // 0x7caf8c: r8 = X0
    //     0x7caf8c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7caf90: LoadField: r9 = r4->field_7
    //     0x7caf90: ldur            x9, [x4, #7]
    // 0x7caf94: r3 = Null
    //     0x7caf94: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef68] Null
    //     0x7caf98: ldr             x3, [x3, #0xf68]
    // 0x7caf9c: blr             x9
    // 0x7cafa0: ldur            x0, [fp, #-0x38]
    // 0x7cafa4: ldur            x2, [fp, #-0x20]
    // 0x7cafa8: r1 = Null
    //     0x7cafa8: mov             x1, NULL
    // 0x7cafac: cmp             w2, NULL
    // 0x7cafb0: b.eq            #0x7cafd0
    // 0x7cafb4: LoadField: r4 = r2->field_1b
    //     0x7cafb4: ldur            w4, [x2, #0x1b]
    // 0x7cafb8: DecompressPointer r4
    //     0x7cafb8: add             x4, x4, HEAP, lsl #32
    // 0x7cafbc: r8 = X1
    //     0x7cafbc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x7cafc0: LoadField: r9 = r4->field_7
    //     0x7cafc0: ldur            x9, [x4, #7]
    // 0x7cafc4: r3 = Null
    //     0x7cafc4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef78] Null
    //     0x7cafc8: ldr             x3, [x3, #0xf78]
    // 0x7cafcc: blr             x9
    // 0x7cafd0: ldur            x1, [fp, #-0x28]
    // 0x7cafd4: ldur            x2, [fp, #-0x18]
    // 0x7cafd8: r0 = _hashCode()
    //     0x7cafd8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7cafdc: ldur            x1, [fp, #-0x28]
    // 0x7cafe0: ldur            x2, [fp, #-0x18]
    // 0x7cafe4: ldur            x3, [fp, #-0x38]
    // 0x7cafe8: mov             x5, x0
    // 0x7cafec: r0 = _set()
    //     0x7cafec: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7caff0: b               #0x7cb0f8
    // 0x7caff4: ldur            x3, [fp, #-0x38]
    // 0x7caff8: r1 = LoadClassIdInstr(r3)
    //     0x7caff8: ldur            x1, [x3, #-1]
    //     0x7caffc: ubfx            x1, x1, #0xc, #0x14
    // 0x7cb000: cmp             x1, #0x393
    // 0x7cb004: b.ne            #0x7cb040
    // 0x7cb008: r2 = 60
    //     0x7cb008: movz            x2, #0x3c
    // 0x7cb00c: branchIfSmi(r0, 0x7cb018)
    //     0x7cb00c: tbz             w0, #0, #0x7cb018
    // 0x7cb010: r2 = LoadClassIdInstr(r0)
    //     0x7cb010: ldur            x2, [x0, #-1]
    //     0x7cb014: ubfx            x2, x2, #0xc, #0x14
    // 0x7cb018: cmp             x2, #0x393
    // 0x7cb01c: b.ne            #0x7cb040
    // 0x7cb020: LoadField: r1 = r0->field_b
    //     0x7cb020: ldur            w1, [x0, #0xb]
    // 0x7cb024: DecompressPointer r1
    //     0x7cb024: add             x1, x1, HEAP, lsl #32
    // 0x7cb028: LoadField: r2 = r3->field_b
    //     0x7cb028: ldur            w2, [x3, #0xb]
    // 0x7cb02c: DecompressPointer r2
    //     0x7cb02c: add             x2, x2, HEAP, lsl #32
    // 0x7cb030: r0 = addAll()
    //     0x7cb030: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7cb034: ldur            x1, [fp, #-0x40]
    // 0x7cb038: r0 = uniq()
    //     0x7cb038: bl              #0x7cb134  ; [package:pdf/src/pdf/format/array.dart] PdfArray::uniq
    // 0x7cb03c: b               #0x7cb0f8
    // 0x7cb040: sub             x16, x1, #0x390
    // 0x7cb044: cmp             x16, #1
    // 0x7cb048: b.hi            #0x7cb078
    // 0x7cb04c: ldur            x1, [fp, #-0x40]
    // 0x7cb050: r0 = 60
    //     0x7cb050: movz            x0, #0x3c
    // 0x7cb054: branchIfSmi(r1, 0x7cb060)
    //     0x7cb054: tbz             w1, #0, #0x7cb060
    // 0x7cb058: r0 = LoadClassIdInstr(r1)
    //     0x7cb058: ldur            x0, [x1, #-1]
    //     0x7cb05c: ubfx            x0, x0, #0xc, #0x14
    // 0x7cb060: sub             x16, x0, #0x390
    // 0x7cb064: cmp             x16, #1
    // 0x7cb068: b.hi            #0x7cb078
    // 0x7cb06c: mov             x2, x3
    // 0x7cb070: r0 = merge()
    //     0x7cb070: bl              #0x7caddc  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::merge
    // 0x7cb074: b               #0x7cb0f8
    // 0x7cb078: ldur            x0, [fp, #-0x18]
    // 0x7cb07c: ldur            x2, [fp, #-0x20]
    // 0x7cb080: r1 = Null
    //     0x7cb080: mov             x1, NULL
    // 0x7cb084: cmp             w2, NULL
    // 0x7cb088: b.eq            #0x7cb0a8
    // 0x7cb08c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cb08c: ldur            w4, [x2, #0x17]
    // 0x7cb090: DecompressPointer r4
    //     0x7cb090: add             x4, x4, HEAP, lsl #32
    // 0x7cb094: r8 = X0
    //     0x7cb094: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7cb098: LoadField: r9 = r4->field_7
    //     0x7cb098: ldur            x9, [x4, #7]
    // 0x7cb09c: r3 = Null
    //     0x7cb09c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef88] Null
    //     0x7cb0a0: ldr             x3, [x3, #0xf88]
    // 0x7cb0a4: blr             x9
    // 0x7cb0a8: ldur            x0, [fp, #-0x38]
    // 0x7cb0ac: ldur            x2, [fp, #-0x20]
    // 0x7cb0b0: r1 = Null
    //     0x7cb0b0: mov             x1, NULL
    // 0x7cb0b4: cmp             w2, NULL
    // 0x7cb0b8: b.eq            #0x7cb0d8
    // 0x7cb0bc: LoadField: r4 = r2->field_1b
    //     0x7cb0bc: ldur            w4, [x2, #0x1b]
    // 0x7cb0c0: DecompressPointer r4
    //     0x7cb0c0: add             x4, x4, HEAP, lsl #32
    // 0x7cb0c4: r8 = X1
    //     0x7cb0c4: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x7cb0c8: LoadField: r9 = r4->field_7
    //     0x7cb0c8: ldur            x9, [x4, #7]
    // 0x7cb0cc: r3 = Null
    //     0x7cb0cc: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef98] Null
    //     0x7cb0d0: ldr             x3, [x3, #0xf98]
    // 0x7cb0d4: blr             x9
    // 0x7cb0d8: ldur            x1, [fp, #-0x28]
    // 0x7cb0dc: ldur            x2, [fp, #-0x18]
    // 0x7cb0e0: r0 = _hashCode()
    //     0x7cb0e0: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x7cb0e4: ldur            x1, [fp, #-0x28]
    // 0x7cb0e8: ldur            x2, [fp, #-0x18]
    // 0x7cb0ec: ldur            x3, [fp, #-0x38]
    // 0x7cb0f0: mov             x5, x0
    // 0x7cb0f4: r0 = _set()
    //     0x7cb0f4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x7cb0f8: ldur            x5, [fp, #-8]
    // 0x7cb0fc: ldur            x2, [fp, #-0x30]
    // 0x7cb100: ldur            x3, [fp, #-0x28]
    // 0x7cb104: ldur            x0, [fp, #-0x20]
    // 0x7cb108: ldur            x4, [fp, #-0x10]
    // 0x7cb10c: b               #0x7cae9c
    // 0x7cb110: r0 = Null
    //     0x7cb110: mov             x0, NULL
    // 0x7cb114: LeaveFrame
    //     0x7cb114: mov             SP, fp
    //     0x7cb118: ldp             fp, lr, [SP], #0x10
    // 0x7cb11c: ret
    //     0x7cb11c: ret             
    // 0x7cb120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb124: b               #0x7cae04
    // 0x7cb128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb12c: b               #0x7caeac
    // 0x7cb130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cb130: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ fromObjectMap(/* No info */) {
    // ** addr: 0x7cb5e4, size: 0x78
    // 0x7cb5e4: EnterFrame
    //     0x7cb5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb5e8: mov             fp, SP
    // 0x7cb5ec: AllocStack(0x20)
    //     0x7cb5ec: sub             SP, SP, #0x20
    // 0x7cb5f0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7cb5f0: mov             x0, x1
    //     0x7cb5f4: stur            x1, [fp, #-8]
    // 0x7cb5f8: CheckStackOverflow
    //     0x7cb5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb5fc: cmp             SP, x16
    //     0x7cb600: b.ls            #0x7cb654
    // 0x7cb604: r1 = Function '<anonymous closure>': static.
    //     0x7cb604: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3efb0] AnonymousClosure: static (0x7cb65c), in [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap (0x7cb5e4)
    //     0x7cb608: ldr             x1, [x1, #0xfb0]
    // 0x7cb60c: r2 = Null
    //     0x7cb60c: mov             x2, NULL
    // 0x7cb610: r0 = AllocateClosure()
    //     0x7cb610: bl              #0xec1630  ; AllocateClosureStub
    // 0x7cb614: r16 = <String, PdfIndirect>
    //     0x7cb614: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efb8] TypeArguments: <String, PdfIndirect>
    //     0x7cb618: ldr             x16, [x16, #0xfb8]
    // 0x7cb61c: ldur            lr, [fp, #-8]
    // 0x7cb620: stp             lr, x16, [SP, #8]
    // 0x7cb624: str             x0, [SP]
    // 0x7cb628: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x7cb628: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x7cb62c: r0 = map()
    //     0x7cb62c: bl              #0x766c60  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::map
    // 0x7cb630: r1 = <PdfIndirect>
    //     0x7cb630: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] TypeArguments: <PdfIndirect>
    //     0x7cb634: ldr             x1, [x1, #0xa0]
    // 0x7cb638: stur            x0, [fp, #-8]
    // 0x7cb63c: r0 = PdfDict()
    //     0x7cb63c: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7cb640: ldur            x1, [fp, #-8]
    // 0x7cb644: StoreField: r0->field_b = r1
    //     0x7cb644: stur            w1, [x0, #0xb]
    // 0x7cb648: LeaveFrame
    //     0x7cb648: mov             SP, fp
    //     0x7cb64c: ldp             fp, lr, [SP], #0x10
    // 0x7cb650: ret
    //     0x7cb650: ret             
    // 0x7cb654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb654: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb658: b               #0x7cb604
  }
  [closure] static MapEntry<String, PdfIndirect> <anonymous closure>(dynamic, String, PdfObjectBase<PdfDataType>) {
    // ** addr: 0x7cb65c, size: 0x54
    // 0x7cb65c: EnterFrame
    //     0x7cb65c: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb660: mov             fp, SP
    // 0x7cb664: AllocStack(0x8)
    //     0x7cb664: sub             SP, SP, #8
    // 0x7cb668: CheckStackOverflow
    //     0x7cb668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb66c: cmp             SP, x16
    //     0x7cb670: b.ls            #0x7cb6a8
    // 0x7cb674: ldr             x1, [fp, #0x10]
    // 0x7cb678: r0 = ref()
    //     0x7cb678: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cb67c: r1 = <String, PdfIndirect>
    //     0x7cb67c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3efb8] TypeArguments: <String, PdfIndirect>
    //     0x7cb680: ldr             x1, [x1, #0xfb8]
    // 0x7cb684: stur            x0, [fp, #-8]
    // 0x7cb688: r0 = MapEntry()
    //     0x7cb688: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x7cb68c: ldr             x1, [fp, #0x18]
    // 0x7cb690: StoreField: r0->field_b = r1
    //     0x7cb690: stur            w1, [x0, #0xb]
    // 0x7cb694: ldur            x1, [fp, #-8]
    // 0x7cb698: StoreField: r0->field_f = r1
    //     0x7cb698: stur            w1, [x0, #0xf]
    // 0x7cb69c: LeaveFrame
    //     0x7cb69c: mov             SP, fp
    //     0x7cb6a0: ldp             fp, lr, [SP], #0x10
    // 0x7cb6a4: ret
    //     0x7cb6a4: ret             
    // 0x7cb6a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb6a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb6ac: b               #0x7cb674
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf18c4, size: 0x40
    // 0xbf18c4: EnterFrame
    //     0xbf18c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf18c8: mov             fp, SP
    // 0xbf18cc: AllocStack(0x8)
    //     0xbf18cc: sub             SP, SP, #8
    // 0xbf18d0: CheckStackOverflow
    //     0xbf18d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf18d4: cmp             SP, x16
    //     0xbf18d8: b.ls            #0xbf18fc
    // 0xbf18dc: ldr             x0, [fp, #0x10]
    // 0xbf18e0: LoadField: r1 = r0->field_b
    //     0xbf18e0: ldur            w1, [x0, #0xb]
    // 0xbf18e4: DecompressPointer r1
    //     0xbf18e4: add             x1, x1, HEAP, lsl #32
    // 0xbf18e8: str             x1, [SP]
    // 0xbf18ec: r0 = _getHash()
    //     0xbf18ec: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xbf18f0: LeaveFrame
    //     0xbf18f0: mov             SP, fp
    //     0xbf18f4: ldp             fp, lr, [SP], #0x10
    // 0xbf18f8: ret
    //     0xbf18f8: ret             
    // 0xbf18fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf18fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1900: b               #0xbf18dc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c608, size: 0x60
    // 0xd7c608: ldr             x1, [SP]
    // 0xd7c60c: cmp             w1, NULL
    // 0xd7c610: b.ne            #0xd7c61c
    // 0xd7c614: r0 = false
    //     0xd7c614: add             x0, NULL, #0x30  ; false
    // 0xd7c618: ret
    //     0xd7c618: ret             
    // 0xd7c61c: r2 = 60
    //     0xd7c61c: movz            x2, #0x3c
    // 0xd7c620: branchIfSmi(r1, 0xd7c62c)
    //     0xd7c620: tbz             w1, #0, #0xd7c62c
    // 0xd7c624: r2 = LoadClassIdInstr(r1)
    //     0xd7c624: ldur            x2, [x1, #-1]
    //     0xd7c628: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c62c: sub             x16, x2, #0x390
    // 0xd7c630: cmp             x16, #1
    // 0xd7c634: b.hi            #0xd7c660
    // 0xd7c638: ldr             x2, [SP, #8]
    // 0xd7c63c: LoadField: r3 = r2->field_b
    //     0xd7c63c: ldur            w3, [x2, #0xb]
    // 0xd7c640: DecompressPointer r3
    //     0xd7c640: add             x3, x3, HEAP, lsl #32
    // 0xd7c644: LoadField: r2 = r1->field_b
    //     0xd7c644: ldur            w2, [x1, #0xb]
    // 0xd7c648: DecompressPointer r2
    //     0xd7c648: add             x2, x2, HEAP, lsl #32
    // 0xd7c64c: cmp             w3, w2
    // 0xd7c650: r16 = true
    //     0xd7c650: add             x16, NULL, #0x20  ; true
    // 0xd7c654: r17 = false
    //     0xd7c654: add             x17, NULL, #0x30  ; false
    // 0xd7c658: csel            x0, x16, x17, eq
    // 0xd7c65c: ret
    //     0xd7c65c: ret             
    // 0xd7c660: r0 = false
    //     0xd7c660: add             x0, NULL, #0x30  ; false
    // 0xd7c664: ret
    //     0xd7c664: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7ed7c, size: 0xc4
    // 0xe7ed7c: EnterFrame
    //     0xe7ed7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ed80: mov             fp, SP
    // 0xe7ed84: AllocStack(0x20)
    //     0xe7ed84: sub             SP, SP, #0x20
    // 0xe7ed88: SetupParameters(PdfDict<X0 bound PdfDataType> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xe7ed88: mov             x0, x1
    //     0xe7ed8c: stur            x1, [fp, #-8]
    //     0xe7ed90: mov             x1, x3
    //     0xe7ed94: stur            x2, [fp, #-0x10]
    //     0xe7ed98: stur            x3, [fp, #-0x18]
    // 0xe7ed9c: CheckStackOverflow
    //     0xe7ed9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7eda0: cmp             SP, x16
    //     0xe7eda4: b.ls            #0xe7ee38
    // 0xe7eda8: r1 = 3
    //     0xe7eda8: movz            x1, #0x3
    // 0xe7edac: r0 = AllocateContext()
    //     0xe7edac: bl              #0xec126c  ; AllocateContextStub
    // 0xe7edb0: mov             x3, x0
    // 0xe7edb4: ldur            x0, [fp, #-8]
    // 0xe7edb8: stur            x3, [fp, #-0x20]
    // 0xe7edbc: StoreField: r3->field_f = r0
    //     0xe7edbc: stur            w0, [x3, #0xf]
    // 0xe7edc0: ldur            x1, [fp, #-0x10]
    // 0xe7edc4: StoreField: r3->field_13 = r1
    //     0xe7edc4: stur            w1, [x3, #0x13]
    // 0xe7edc8: ldur            x1, [fp, #-0x18]
    // 0xe7edcc: ArrayStore: r3[0] = r1  ; List_4
    //     0xe7edcc: stur            w1, [x3, #0x17]
    // 0xe7edd0: r2 = const [0x3c, 0x3c]
    //     0xe7edd0: add             x2, PP, #0x36, lsl #12  ; [pp+0x366a0] List<int>(2)
    //     0xe7edd4: ldr             x2, [x2, #0x6a0]
    // 0xe7edd8: r0 = putBytes()
    //     0xe7edd8: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe7eddc: ldur            x0, [fp, #-8]
    // 0xe7ede0: LoadField: r4 = r0->field_b
    //     0xe7ede0: ldur            w4, [x0, #0xb]
    // 0xe7ede4: DecompressPointer r4
    //     0xe7ede4: add             x4, x4, HEAP, lsl #32
    // 0xe7ede8: stur            x4, [fp, #-0x10]
    // 0xe7edec: LoadField: r3 = r0->field_7
    //     0xe7edec: ldur            w3, [x0, #7]
    // 0xe7edf0: DecompressPointer r3
    //     0xe7edf0: add             x3, x3, HEAP, lsl #32
    // 0xe7edf4: ldur            x2, [fp, #-0x20]
    // 0xe7edf8: r1 = Function '<anonymous closure>':.
    //     0xe7edf8: add             x1, PP, #0x36, lsl #12  ; [pp+0x366a8] AnonymousClosure: (0xe7ee40), in [package:pdf/src/pdf/format/dict.dart] PdfDict::output (0xe7ed7c)
    //     0xe7edfc: ldr             x1, [x1, #0x6a8]
    // 0xe7ee00: r0 = AllocateClosureTA()
    //     0xe7ee00: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xe7ee04: ldur            x1, [fp, #-0x10]
    // 0xe7ee08: mov             x2, x0
    // 0xe7ee0c: r0 = forEach()
    //     0xe7ee0c: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xe7ee10: ldur            x0, [fp, #-0x20]
    // 0xe7ee14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe7ee14: ldur            w1, [x0, #0x17]
    // 0xe7ee18: DecompressPointer r1
    //     0xe7ee18: add             x1, x1, HEAP, lsl #32
    // 0xe7ee1c: r2 = const [0x3e, 0x3e]
    //     0xe7ee1c: add             x2, PP, #0x36, lsl #12  ; [pp+0x366b0] List<int>(2)
    //     0xe7ee20: ldr             x2, [x2, #0x6b0]
    // 0xe7ee24: r0 = putBytes()
    //     0xe7ee24: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe7ee28: r0 = Null
    //     0xe7ee28: mov             x0, NULL
    // 0xe7ee2c: LeaveFrame
    //     0xe7ee2c: mov             SP, fp
    //     0xe7ee30: ldp             fp, lr, [SP], #0x10
    // 0xe7ee34: ret
    //     0xe7ee34: ret             
    // 0xe7ee38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ee38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ee3c: b               #0xe7eda8
  }
  [closure] void <anonymous closure>(dynamic, String, X0) {
    // ** addr: 0xe7ee40, size: 0xb8
    // 0xe7ee40: EnterFrame
    //     0xe7ee40: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ee44: mov             fp, SP
    // 0xe7ee48: AllocStack(0x8)
    //     0xe7ee48: sub             SP, SP, #8
    // 0xe7ee4c: SetupParameters()
    //     0xe7ee4c: ldr             x0, [fp, #0x20]
    //     0xe7ee50: ldur            w3, [x0, #0x17]
    //     0xe7ee54: add             x3, x3, HEAP, lsl #32
    //     0xe7ee58: stur            x3, [fp, #-8]
    // 0xe7ee5c: CheckStackOverflow
    //     0xe7ee5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ee60: cmp             SP, x16
    //     0xe7ee64: b.ls            #0xe7eef0
    // 0xe7ee68: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xe7ee68: ldur            w1, [x3, #0x17]
    // 0xe7ee6c: DecompressPointer r1
    //     0xe7ee6c: add             x1, x1, HEAP, lsl #32
    // 0xe7ee70: ldr             x2, [fp, #0x18]
    // 0xe7ee74: r0 = putString()
    //     0xe7ee74: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ee78: ldr             x0, [fp, #0x10]
    // 0xe7ee7c: r1 = LoadClassIdInstr(r0)
    //     0xe7ee7c: ldur            x1, [x0, #-1]
    //     0xe7ee80: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ee84: cmp             x1, #0x38c
    // 0xe7ee88: b.eq            #0xe7eea0
    // 0xe7ee8c: cmp             x1, #0x392
    // 0xe7ee90: b.eq            #0xe7eea0
    // 0xe7ee94: sub             x16, x1, #0x38e
    // 0xe7ee98: cmp             x16, #1
    // 0xe7ee9c: b.hi            #0xe7eeb4
    // 0xe7eea0: ldur            x3, [fp, #-8]
    // 0xe7eea4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xe7eea4: ldur            w1, [x3, #0x17]
    // 0xe7eea8: DecompressPointer r1
    //     0xe7eea8: add             x1, x1, HEAP, lsl #32
    // 0xe7eeac: r2 = 32
    //     0xe7eeac: movz            x2, #0x20
    // 0xe7eeb0: r0 = putByte()
    //     0xe7eeb0: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe7eeb4: ldr             x1, [fp, #0x10]
    // 0xe7eeb8: ldur            x0, [fp, #-8]
    // 0xe7eebc: LoadField: r2 = r0->field_13
    //     0xe7eebc: ldur            w2, [x0, #0x13]
    // 0xe7eec0: DecompressPointer r2
    //     0xe7eec0: add             x2, x2, HEAP, lsl #32
    // 0xe7eec4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xe7eec4: ldur            w3, [x0, #0x17]
    // 0xe7eec8: DecompressPointer r3
    //     0xe7eec8: add             x3, x3, HEAP, lsl #32
    // 0xe7eecc: r0 = LoadClassIdInstr(r1)
    //     0xe7eecc: ldur            x0, [x1, #-1]
    //     0xe7eed0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7eed4: r0 = GDT[cid_x0 + -0xf87]()
    //     0xe7eed4: sub             lr, x0, #0xf87
    //     0xe7eed8: ldr             lr, [x21, lr, lsl #3]
    //     0xe7eedc: blr             lr
    // 0xe7eee0: r0 = Null
    //     0xe7eee0: mov             x0, NULL
    // 0xe7eee4: LeaveFrame
    //     0xe7eee4: mov             SP, fp
    //     0xe7eee8: ldp             fp, lr, [SP], #0x10
    // 0xe7eeec: ret
    //     0xe7eeec: ret             
    // 0xe7eef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7eef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7eef4: b               #0xe7ee68
  }
}
