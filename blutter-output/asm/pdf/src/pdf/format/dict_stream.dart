// lib: , url: package:pdf/src/pdf/format/dict_stream.dart

// class id: 1050785, size: 0x8
class :: {
}

// class id: 913, size: 0x20, field offset: 0x10
class PdfDictStream extends PdfDict<dynamic> {

  _ output(/* No info */) {
    // ** addr: 0xe7eac4, size: 0x2ac
    // 0xe7eac4: EnterFrame
    //     0xe7eac4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7eac8: mov             fp, SP
    // 0xe7eacc: AllocStack(0x48)
    //     0xe7eacc: sub             SP, SP, #0x48
    // 0xe7ead0: SetupParameters(PdfDictStream this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe7ead0: mov             x0, x1
    //     0xe7ead4: stur            x1, [fp, #-0x10]
    //     0xe7ead8: stur            x2, [fp, #-0x18]
    //     0xe7eadc: stur            x3, [fp, #-0x20]
    // 0xe7eae0: CheckStackOverflow
    //     0xe7eae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7eae4: cmp             SP, x16
    //     0xe7eae8: b.ls            #0xe7ed68
    // 0xe7eaec: LoadField: r4 = r0->field_b
    //     0xe7eaec: ldur            w4, [x0, #0xb]
    // 0xe7eaf0: DecompressPointer r4
    //     0xe7eaf0: add             x4, x4, HEAP, lsl #32
    // 0xe7eaf4: stur            x4, [fp, #-8]
    // 0xe7eaf8: r1 = <PdfDataType>
    //     0xe7eaf8: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe7eafc: ldr             x1, [x1, #0x4c8]
    // 0xe7eb00: r0 = PdfDict()
    //     0xe7eb00: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe7eb04: stur            x0, [fp, #-0x28]
    // 0xe7eb08: ldur            x16, [fp, #-8]
    // 0xe7eb0c: str             x16, [SP]
    // 0xe7eb10: mov             x1, x0
    // 0xe7eb14: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe7eb14: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe7eb18: r0 = PdfDict()
    //     0xe7eb18: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0xe7eb1c: ldur            x1, [fp, #-0x28]
    // 0xe7eb20: r2 = "/Filter"
    //     0xe7eb20: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c020] "/Filter"
    //     0xe7eb24: ldr             x2, [x2, #0x20]
    // 0xe7eb28: r0 = contains()
    //     0xe7eb28: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0xe7eb2c: tbnz            w0, #4, #0xe7eb44
    // 0xe7eb30: ldur            x0, [fp, #-0x10]
    // 0xe7eb34: LoadField: r1 = r0->field_f
    //     0xe7eb34: ldur            w1, [x0, #0xf]
    // 0xe7eb38: DecompressPointer r1
    //     0xe7eb38: add             x1, x1, HEAP, lsl #32
    // 0xe7eb3c: mov             x0, x1
    // 0xe7eb40: b               #0xe7ec08
    // 0xe7eb44: ldur            x0, [fp, #-0x10]
    // 0xe7eb48: ldur            x3, [fp, #-0x18]
    // 0xe7eb4c: LoadField: r1 = r3->field_1f
    //     0xe7eb4c: ldur            w1, [x3, #0x1f]
    // 0xe7eb50: DecompressPointer r1
    //     0xe7eb50: add             x1, x1, HEAP, lsl #32
    // 0xe7eb54: LoadField: r2 = r1->field_7
    //     0xe7eb54: ldur            w2, [x1, #7]
    // 0xe7eb58: DecompressPointer r2
    //     0xe7eb58: add             x2, x2, HEAP, lsl #32
    // 0xe7eb5c: cmp             w2, NULL
    // 0xe7eb60: b.eq            #0xe7ec04
    // 0xe7eb64: LoadField: r1 = r0->field_f
    //     0xe7eb64: ldur            w1, [x0, #0xf]
    // 0xe7eb68: DecompressPointer r1
    //     0xe7eb68: add             x1, x1, HEAP, lsl #32
    // 0xe7eb6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe7eb6c: ldur            w4, [x2, #0x17]
    // 0xe7eb70: DecompressPointer r4
    //     0xe7eb70: add             x4, x4, HEAP, lsl #32
    // 0xe7eb74: mov             x2, x1
    // 0xe7eb78: mov             x1, x4
    // 0xe7eb7c: r0 = encode()
    //     0xe7eb7c: bl              #0xcebb90  ; [dart:convert] Codec::encode
    // 0xe7eb80: mov             x2, x0
    // 0xe7eb84: r1 = Null
    //     0xe7eb84: mov             x1, NULL
    // 0xe7eb88: r0 = Uint8List.fromList()
    //     0xe7eb88: bl              #0x6b9248  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0xe7eb8c: mov             x2, x0
    // 0xe7eb90: stur            x2, [fp, #-0x30]
    // 0xe7eb94: LoadField: r3 = r2->field_13
    //     0xe7eb94: ldur            w3, [x2, #0x13]
    // 0xe7eb98: ldur            x4, [fp, #-0x10]
    // 0xe7eb9c: stur            x3, [fp, #-8]
    // 0xe7eba0: LoadField: r1 = r4->field_f
    //     0xe7eba0: ldur            w1, [x4, #0xf]
    // 0xe7eba4: DecompressPointer r1
    //     0xe7eba4: add             x1, x1, HEAP, lsl #32
    // 0xe7eba8: r0 = LoadClassIdInstr(r1)
    //     0xe7eba8: ldur            x0, [x1, #-1]
    //     0xe7ebac: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ebb0: r0 = GDT[cid_x0 + 0xcd4c]()
    //     0xe7ebb0: movz            x17, #0xcd4c
    //     0xe7ebb4: add             lr, x0, x17
    //     0xe7ebb8: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ebbc: blr             lr
    // 0xe7ebc0: mov             x1, x0
    // 0xe7ebc4: ldur            x0, [fp, #-8]
    // 0xe7ebc8: r2 = LoadInt32Instr(r0)
    //     0xe7ebc8: sbfx            x2, x0, #1, #0x1f
    // 0xe7ebcc: cmp             x2, x1
    // 0xe7ebd0: b.ge            #0xe7ebfc
    // 0xe7ebd4: ldur            x0, [fp, #-0x28]
    // 0xe7ebd8: LoadField: r1 = r0->field_b
    //     0xe7ebd8: ldur            w1, [x0, #0xb]
    // 0xe7ebdc: DecompressPointer r1
    //     0xe7ebdc: add             x1, x1, HEAP, lsl #32
    // 0xe7ebe0: r2 = "/Filter"
    //     0xe7ebe0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c020] "/Filter"
    //     0xe7ebe4: ldr             x2, [x2, #0x20]
    // 0xe7ebe8: r3 = Instance_PdfName
    //     0xe7ebe8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c028] Obj!PdfName@e0c901
    //     0xe7ebec: ldr             x3, [x3, #0x28]
    // 0xe7ebf0: r0 = []=()
    //     0xe7ebf0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe7ebf4: ldur            x0, [fp, #-0x30]
    // 0xe7ebf8: b               #0xe7ec08
    // 0xe7ebfc: r0 = Null
    //     0xe7ebfc: mov             x0, NULL
    // 0xe7ec00: b               #0xe7ec08
    // 0xe7ec04: r0 = Null
    //     0xe7ec04: mov             x0, NULL
    // 0xe7ec08: cmp             w0, NULL
    // 0xe7ec0c: b.ne            #0xe7ec8c
    // 0xe7ec10: ldur            x0, [fp, #-0x10]
    // 0xe7ec14: LoadField: r1 = r0->field_13
    //     0xe7ec14: ldur            w1, [x0, #0x13]
    // 0xe7ec18: DecompressPointer r1
    //     0xe7ec18: add             x1, x1, HEAP, lsl #32
    // 0xe7ec1c: tbnz            w1, #4, #0xe7ec7c
    // 0xe7ec20: ldur            x2, [fp, #-0x28]
    // 0xe7ec24: LoadField: r3 = r0->field_f
    //     0xe7ec24: ldur            w3, [x0, #0xf]
    // 0xe7ec28: DecompressPointer r3
    //     0xe7ec28: add             x3, x3, HEAP, lsl #32
    // 0xe7ec2c: stur            x3, [fp, #-8]
    // 0xe7ec30: r1 = <Uint8List, Uint8List>
    //     0xe7ec30: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c030] TypeArguments: <Uint8List, Uint8List>
    //     0xe7ec34: ldr             x1, [x1, #0x30]
    // 0xe7ec38: r0 = Ascii85Encoder()
    //     0xe7ec38: bl              #0xe7ed70  ; AllocateAscii85EncoderStub -> Ascii85Encoder (size=0xc)
    // 0xe7ec3c: mov             x1, x0
    // 0xe7ec40: ldur            x2, [fp, #-8]
    // 0xe7ec44: r0 = convert()
    //     0xe7ec44: bl              #0xcf9d50  ; [package:pdf/src/pdf/format/ascii85.dart] Ascii85Encoder::convert
    // 0xe7ec48: mov             x4, x0
    // 0xe7ec4c: ldur            x0, [fp, #-0x28]
    // 0xe7ec50: stur            x4, [fp, #-8]
    // 0xe7ec54: LoadField: r1 = r0->field_b
    //     0xe7ec54: ldur            w1, [x0, #0xb]
    // 0xe7ec58: DecompressPointer r1
    //     0xe7ec58: add             x1, x1, HEAP, lsl #32
    // 0xe7ec5c: r2 = "/Filter"
    //     0xe7ec5c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c020] "/Filter"
    //     0xe7ec60: ldr             x2, [x2, #0x20]
    // 0xe7ec64: r3 = Instance_PdfName
    //     0xe7ec64: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c038] Obj!PdfName@e0c8f1
    //     0xe7ec68: ldr             x3, [x3, #0x38]
    // 0xe7ec6c: r0 = []=()
    //     0xe7ec6c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe7ec70: ldur            x0, [fp, #-8]
    // 0xe7ec74: ldur            x1, [fp, #-0x10]
    // 0xe7ec78: b               #0xe7ec90
    // 0xe7ec7c: mov             x1, x0
    // 0xe7ec80: LoadField: r0 = r1->field_f
    //     0xe7ec80: ldur            w0, [x1, #0xf]
    // 0xe7ec84: DecompressPointer r0
    //     0xe7ec84: add             x0, x0, HEAP, lsl #32
    // 0xe7ec88: b               #0xe7ec90
    // 0xe7ec8c: ldur            x1, [fp, #-0x10]
    // 0xe7ec90: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe7ec90: ldur            w2, [x1, #0x17]
    // 0xe7ec94: DecompressPointer r2
    //     0xe7ec94: add             x2, x2, HEAP, lsl #32
    // 0xe7ec98: tbnz            w2, #4, #0xe7ecd8
    // 0xe7ec9c: ldur            x2, [fp, #-0x18]
    // 0xe7eca0: LoadField: r1 = r2->field_1f
    //     0xe7eca0: ldur            w1, [x2, #0x1f]
    // 0xe7eca4: DecompressPointer r1
    //     0xe7eca4: add             x1, x1, HEAP, lsl #32
    // 0xe7eca8: LoadField: r3 = r1->field_b
    //     0xe7eca8: ldur            w3, [x1, #0xb]
    // 0xe7ecac: DecompressPointer r3
    //     0xe7ecac: add             x3, x3, HEAP, lsl #32
    // 0xe7ecb0: cmp             w3, NULL
    // 0xe7ecb4: b.eq            #0xe7ecd8
    // 0xe7ecb8: stp             x0, x3, [SP, #8]
    // 0xe7ecbc: str             x2, [SP]
    // 0xe7ecc0: mov             x0, x3
    // 0xe7ecc4: ClosureCall
    //     0xe7ecc4: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xe7ecc8: ldur            x2, [x0, #0x1f]
    //     0xe7eccc: blr             x2
    // 0xe7ecd0: mov             x2, x0
    // 0xe7ecd4: b               #0xe7ecdc
    // 0xe7ecd8: mov             x2, x0
    // 0xe7ecdc: ldur            x1, [fp, #-0x28]
    // 0xe7ece0: stur            x2, [fp, #-0x10]
    // 0xe7ece4: LoadField: r0 = r2->field_13
    //     0xe7ece4: ldur            w0, [x2, #0x13]
    // 0xe7ece8: stur            x0, [fp, #-8]
    // 0xe7ecec: r0 = PdfNum()
    //     0xe7ecec: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe7ecf0: mov             x1, x0
    // 0xe7ecf4: ldur            x0, [fp, #-8]
    // 0xe7ecf8: StoreField: r1->field_7 = r0
    //     0xe7ecf8: stur            w0, [x1, #7]
    // 0xe7ecfc: ldur            x0, [fp, #-0x28]
    // 0xe7ed00: LoadField: r2 = r0->field_b
    //     0xe7ed00: ldur            w2, [x0, #0xb]
    // 0xe7ed04: DecompressPointer r2
    //     0xe7ed04: add             x2, x2, HEAP, lsl #32
    // 0xe7ed08: mov             x3, x1
    // 0xe7ed0c: mov             x1, x2
    // 0xe7ed10: r2 = "/Length"
    //     0xe7ed10: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c040] "/Length"
    //     0xe7ed14: ldr             x2, [x2, #0x40]
    // 0xe7ed18: r0 = []=()
    //     0xe7ed18: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe7ed1c: ldur            x1, [fp, #-0x28]
    // 0xe7ed20: ldur            x2, [fp, #-0x18]
    // 0xe7ed24: ldur            x3, [fp, #-0x20]
    // 0xe7ed28: r0 = output()
    //     0xe7ed28: bl              #0xe7ed7c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::output
    // 0xe7ed2c: ldur            x1, [fp, #-0x20]
    // 0xe7ed30: r2 = "stream\n"
    //     0xe7ed30: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c048] "stream\n"
    //     0xe7ed34: ldr             x2, [x2, #0x48]
    // 0xe7ed38: r0 = putString()
    //     0xe7ed38: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ed3c: ldur            x1, [fp, #-0x20]
    // 0xe7ed40: ldur            x2, [fp, #-0x10]
    // 0xe7ed44: r0 = putBytes()
    //     0xe7ed44: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe7ed48: ldur            x1, [fp, #-0x20]
    // 0xe7ed4c: r2 = "\nendstream"
    //     0xe7ed4c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c050] "\nendstream"
    //     0xe7ed50: ldr             x2, [x2, #0x50]
    // 0xe7ed54: r0 = putString()
    //     0xe7ed54: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ed58: r0 = Null
    //     0xe7ed58: mov             x0, NULL
    // 0xe7ed5c: LeaveFrame
    //     0xe7ed5c: mov             SP, fp
    //     0xe7ed60: ldp             fp, lr, [SP], #0x10
    // 0xe7ed64: ret
    //     0xe7ed64: ret             
    // 0xe7ed68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ed68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ed6c: b               #0xe7eaec
  }
}
