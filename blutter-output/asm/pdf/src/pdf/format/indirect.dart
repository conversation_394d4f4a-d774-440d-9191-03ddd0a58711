// lib: , url: package:pdf/src/pdf/format/indirect.dart

// class id: 1050786, size: 0x8
class :: {
}

// class id: 910, size: 0x18, field offset: 0x8
//   const constructor, 
class PdfIndirect extends PdfDataType {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1904, size: 0xd4
    // 0xbf1904: EnterFrame
    //     0xbf1904: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1908: mov             fp, SP
    // 0xbf190c: AllocStack(0x8)
    //     0xbf190c: sub             SP, SP, #8
    // 0xbf1910: CheckStackOverflow
    //     0xbf1910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1914: cmp             SP, x16
    //     0xbf1918: b.ls            #0xbf19d0
    // 0xbf191c: ldr             x2, [fp, #0x10]
    // 0xbf1920: LoadField: r3 = r2->field_7
    //     0xbf1920: ldur            x3, [x2, #7]
    // 0xbf1924: r0 = BoxInt64Instr(r3)
    //     0xbf1924: sbfiz           x0, x3, #1, #0x1f
    //     0xbf1928: cmp             x3, x0, asr #1
    //     0xbf192c: b.eq            #0xbf1938
    //     0xbf1930: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1934: stur            x3, [x0, #7]
    // 0xbf1938: r1 = 60
    //     0xbf1938: movz            x1, #0x3c
    // 0xbf193c: branchIfSmi(r0, 0xbf1948)
    //     0xbf193c: tbz             w0, #0, #0xbf1948
    // 0xbf1940: r1 = LoadClassIdInstr(r0)
    //     0xbf1940: ldur            x1, [x0, #-1]
    //     0xbf1944: ubfx            x1, x1, #0xc, #0x14
    // 0xbf1948: str             x0, [SP]
    // 0xbf194c: mov             x0, x1
    // 0xbf1950: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1950: movz            x17, #0x64af
    //     0xbf1954: add             lr, x0, x17
    //     0xbf1958: ldr             lr, [x21, lr, lsl #3]
    //     0xbf195c: blr             lr
    // 0xbf1960: mov             x3, x0
    // 0xbf1964: ldr             x2, [fp, #0x10]
    // 0xbf1968: LoadField: r4 = r2->field_f
    //     0xbf1968: ldur            x4, [x2, #0xf]
    // 0xbf196c: r0 = BoxInt64Instr(r4)
    //     0xbf196c: sbfiz           x0, x4, #1, #0x1f
    //     0xbf1970: cmp             x4, x0, asr #1
    //     0xbf1974: b.eq            #0xbf1980
    //     0xbf1978: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf197c: stur            x4, [x0, #7]
    // 0xbf1980: r16 = LoadInt32Instr(r0)
    //     0xbf1980: sbfx            x16, x0, #1, #0x1f
    // 0xbf1984: r17 = 11601
    //     0xbf1984: movz            x17, #0x2d51
    // 0xbf1988: mul             x2, x16, x17
    // 0xbf198c: umulh           x16, x16, x17
    // 0xbf1990: eor             x2, x2, x16
    // 0xbf1994: r2 = 0
    //     0xbf1994: eor             x2, x2, x2, lsr #32
    // 0xbf1998: ubfiz           x2, x2, #1, #0x1e
    // 0xbf199c: r4 = LoadInt32Instr(r3)
    //     0xbf199c: sbfx            x4, x3, #1, #0x1f
    //     0xbf19a0: tbz             w3, #0, #0xbf19a8
    //     0xbf19a4: ldur            x4, [x3, #7]
    // 0xbf19a8: r3 = LoadInt32Instr(r2)
    //     0xbf19a8: sbfx            x3, x2, #1, #0x1f
    // 0xbf19ac: add             x2, x4, x3
    // 0xbf19b0: r0 = BoxInt64Instr(r2)
    //     0xbf19b0: sbfiz           x0, x2, #1, #0x1f
    //     0xbf19b4: cmp             x2, x0, asr #1
    //     0xbf19b8: b.eq            #0xbf19c4
    //     0xbf19bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf19c0: stur            x2, [x0, #7]
    // 0xbf19c4: LeaveFrame
    //     0xbf19c4: mov             SP, fp
    //     0xbf19c8: ldp             fp, lr, [SP], #0x10
    // 0xbf19cc: ret
    //     0xbf19cc: ret             
    // 0xbf19d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf19d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf19d4: b               #0xbf191c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c6bc, size: 0x74
    // 0xd7c6bc: ldr             x1, [SP]
    // 0xd7c6c0: cmp             w1, NULL
    // 0xd7c6c4: b.ne            #0xd7c6d0
    // 0xd7c6c8: r0 = false
    //     0xd7c6c8: add             x0, NULL, #0x30  ; false
    // 0xd7c6cc: ret
    //     0xd7c6cc: ret             
    // 0xd7c6d0: r2 = 60
    //     0xd7c6d0: movz            x2, #0x3c
    // 0xd7c6d4: branchIfSmi(r1, 0xd7c6e0)
    //     0xd7c6d4: tbz             w1, #0, #0xd7c6e0
    // 0xd7c6d8: r2 = LoadClassIdInstr(r1)
    //     0xd7c6d8: ldur            x2, [x1, #-1]
    //     0xd7c6dc: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c6e0: sub             x16, x2, #0x38e
    // 0xd7c6e4: cmp             x16, #1
    // 0xd7c6e8: b.hi            #0xd7c728
    // 0xd7c6ec: ldr             x2, [SP, #8]
    // 0xd7c6f0: LoadField: r3 = r2->field_7
    //     0xd7c6f0: ldur            x3, [x2, #7]
    // 0xd7c6f4: LoadField: r4 = r1->field_7
    //     0xd7c6f4: ldur            x4, [x1, #7]
    // 0xd7c6f8: cmp             x3, x4
    // 0xd7c6fc: b.ne            #0xd7c720
    // 0xd7c700: LoadField: r3 = r2->field_f
    //     0xd7c700: ldur            x3, [x2, #0xf]
    // 0xd7c704: LoadField: r2 = r1->field_f
    //     0xd7c704: ldur            x2, [x1, #0xf]
    // 0xd7c708: cmp             x3, x2
    // 0xd7c70c: r16 = true
    //     0xd7c70c: add             x16, NULL, #0x20  ; true
    // 0xd7c710: r17 = false
    //     0xd7c710: add             x17, NULL, #0x30  ; false
    // 0xd7c714: csel            x1, x16, x17, eq
    // 0xd7c718: mov             x0, x1
    // 0xd7c71c: b               #0xd7c724
    // 0xd7c720: r0 = false
    //     0xd7c720: add             x0, NULL, #0x30  ; false
    // 0xd7c724: ret
    //     0xd7c724: ret             
    // 0xd7c728: r0 = false
    //     0xd7c728: add             x0, NULL, #0x30  ; false
    // 0xd7c72c: ret
    //     0xd7c72c: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7eef8, size: 0xb8
    // 0xe7eef8: EnterFrame
    //     0xe7eef8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7eefc: mov             fp, SP
    // 0xe7ef00: AllocStack(0x20)
    //     0xe7ef00: sub             SP, SP, #0x20
    // 0xe7ef04: SetupParameters(PdfIndirect this /* r1 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe7ef04: mov             x4, x1
    //     0xe7ef08: stur            x1, [fp, #-0x10]
    //     0xe7ef0c: stur            x3, [fp, #-0x18]
    // 0xe7ef10: CheckStackOverflow
    //     0xe7ef10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ef14: cmp             SP, x16
    //     0xe7ef18: b.ls            #0xe7efa8
    // 0xe7ef1c: LoadField: r2 = r4->field_7
    //     0xe7ef1c: ldur            x2, [x4, #7]
    // 0xe7ef20: r0 = BoxInt64Instr(r2)
    //     0xe7ef20: sbfiz           x0, x2, #1, #0x1f
    //     0xe7ef24: cmp             x2, x0, asr #1
    //     0xe7ef28: b.eq            #0xe7ef34
    //     0xe7ef2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7ef30: stur            x2, [x0, #7]
    // 0xe7ef34: r1 = Null
    //     0xe7ef34: mov             x1, NULL
    // 0xe7ef38: r2 = 8
    //     0xe7ef38: movz            x2, #0x8
    // 0xe7ef3c: stur            x0, [fp, #-8]
    // 0xe7ef40: r0 = AllocateArray()
    //     0xe7ef40: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7ef44: mov             x2, x0
    // 0xe7ef48: ldur            x0, [fp, #-8]
    // 0xe7ef4c: StoreField: r2->field_f = r0
    //     0xe7ef4c: stur            w0, [x2, #0xf]
    // 0xe7ef50: r16 = " "
    //     0xe7ef50: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe7ef54: StoreField: r2->field_13 = r16
    //     0xe7ef54: stur            w16, [x2, #0x13]
    // 0xe7ef58: ldur            x0, [fp, #-0x10]
    // 0xe7ef5c: LoadField: r3 = r0->field_f
    //     0xe7ef5c: ldur            x3, [x0, #0xf]
    // 0xe7ef60: r0 = BoxInt64Instr(r3)
    //     0xe7ef60: sbfiz           x0, x3, #1, #0x1f
    //     0xe7ef64: cmp             x3, x0, asr #1
    //     0xe7ef68: b.eq            #0xe7ef74
    //     0xe7ef6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7ef70: stur            x3, [x0, #7]
    // 0xe7ef74: ArrayStore: r2[0] = r0  ; List_4
    //     0xe7ef74: stur            w0, [x2, #0x17]
    // 0xe7ef78: r16 = " R"
    //     0xe7ef78: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dc90] " R"
    //     0xe7ef7c: ldr             x16, [x16, #0xc90]
    // 0xe7ef80: StoreField: r2->field_1b = r16
    //     0xe7ef80: stur            w16, [x2, #0x1b]
    // 0xe7ef84: str             x2, [SP]
    // 0xe7ef88: r0 = _interpolate()
    //     0xe7ef88: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7ef8c: ldur            x1, [fp, #-0x18]
    // 0xe7ef90: mov             x2, x0
    // 0xe7ef94: r0 = putString()
    //     0xe7ef94: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ef98: r0 = Null
    //     0xe7ef98: mov             x0, NULL
    // 0xe7ef9c: LeaveFrame
    //     0xe7ef9c: mov             SP, fp
    //     0xe7efa0: ldp             fp, lr, [SP], #0x10
    // 0xe7efa4: ret
    //     0xe7efa4: ret             
    // 0xe7efa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7efa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7efac: b               #0xe7ef1c
  }
}
