// lib: , url: package:pdf/src/pdf/format/name.dart

// class id: 1050787, size: 0x8
class :: {
}

// class id: 909, size: 0xc, field offset: 0x8
//   const constructor, 
class PdfName extends PdfDataType {

  _OneByteString field_8;

  _ ==(/* No info */) {
    // ** addr: 0xd7c730, size: 0x9c
    // 0xd7c730: EnterFrame
    //     0xd7c730: stp             fp, lr, [SP, #-0x10]!
    //     0xd7c734: mov             fp, SP
    // 0xd7c738: AllocStack(0x10)
    //     0xd7c738: sub             SP, SP, #0x10
    // 0xd7c73c: CheckStackOverflow
    //     0xd7c73c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7c740: cmp             SP, x16
    //     0xd7c744: b.ls            #0xd7c7c4
    // 0xd7c748: ldr             x0, [fp, #0x10]
    // 0xd7c74c: cmp             w0, NULL
    // 0xd7c750: b.ne            #0xd7c764
    // 0xd7c754: r0 = false
    //     0xd7c754: add             x0, NULL, #0x30  ; false
    // 0xd7c758: LeaveFrame
    //     0xd7c758: mov             SP, fp
    //     0xd7c75c: ldp             fp, lr, [SP], #0x10
    // 0xd7c760: ret
    //     0xd7c760: ret             
    // 0xd7c764: r1 = 60
    //     0xd7c764: movz            x1, #0x3c
    // 0xd7c768: branchIfSmi(r0, 0xd7c774)
    //     0xd7c768: tbz             w0, #0, #0xd7c774
    // 0xd7c76c: r1 = LoadClassIdInstr(r0)
    //     0xd7c76c: ldur            x1, [x0, #-1]
    //     0xd7c770: ubfx            x1, x1, #0xc, #0x14
    // 0xd7c774: cmp             x1, #0x38d
    // 0xd7c778: b.ne            #0xd7c7b4
    // 0xd7c77c: ldr             x1, [fp, #0x18]
    // 0xd7c780: LoadField: r2 = r1->field_7
    //     0xd7c780: ldur            w2, [x1, #7]
    // 0xd7c784: DecompressPointer r2
    //     0xd7c784: add             x2, x2, HEAP, lsl #32
    // 0xd7c788: LoadField: r1 = r0->field_7
    //     0xd7c788: ldur            w1, [x0, #7]
    // 0xd7c78c: DecompressPointer r1
    //     0xd7c78c: add             x1, x1, HEAP, lsl #32
    // 0xd7c790: r0 = LoadClassIdInstr(r2)
    //     0xd7c790: ldur            x0, [x2, #-1]
    //     0xd7c794: ubfx            x0, x0, #0xc, #0x14
    // 0xd7c798: stp             x1, x2, [SP]
    // 0xd7c79c: mov             lr, x0
    // 0xd7c7a0: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c7a4: blr             lr
    // 0xd7c7a8: LeaveFrame
    //     0xd7c7a8: mov             SP, fp
    //     0xd7c7ac: ldp             fp, lr, [SP], #0x10
    // 0xd7c7b0: ret
    //     0xd7c7b0: ret             
    // 0xd7c7b4: r0 = false
    //     0xd7c7b4: add             x0, NULL, #0x30  ; false
    // 0xd7c7b8: LeaveFrame
    //     0xd7c7b8: mov             SP, fp
    //     0xd7c7bc: ldp             fp, lr, [SP], #0x10
    // 0xd7c7c0: ret
    //     0xd7c7c0: ret             
    // 0xd7c7c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7c7c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7c7c8: b               #0xd7c748
  }
  _ output(/* No info */) {
    // ** addr: 0xe7efb0, size: 0x228
    // 0xe7efb0: EnterFrame
    //     0xe7efb0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7efb4: mov             fp, SP
    // 0xe7efb8: AllocStack(0x48)
    //     0xe7efb8: sub             SP, SP, #0x48
    // 0xe7efbc: SetupParameters(PdfName this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xe7efbc: mov             x0, x3
    //     0xe7efc0: stur            x3, [fp, #-0x10]
    //     0xe7efc4: mov             x3, x1
    //     0xe7efc8: stur            x1, [fp, #-8]
    // 0xe7efcc: CheckStackOverflow
    //     0xe7efcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7efd0: cmp             SP, x16
    //     0xe7efd4: b.ls            #0xe7f1c8
    // 0xe7efd8: r1 = <int>
    //     0xe7efd8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe7efdc: r2 = 0
    //     0xe7efdc: movz            x2, #0
    // 0xe7efe0: r0 = _GrowableList()
    //     0xe7efe0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7efe4: mov             x2, x0
    // 0xe7efe8: ldur            x0, [fp, #-8]
    // 0xe7efec: stur            x2, [fp, #-0x40]
    // 0xe7eff0: LoadField: r3 = r0->field_7
    //     0xe7eff0: ldur            w3, [x0, #7]
    // 0xe7eff4: DecompressPointer r3
    //     0xe7eff4: add             x3, x3, HEAP, lsl #32
    // 0xe7eff8: stur            x3, [fp, #-0x38]
    // 0xe7effc: LoadField: r0 = r3->field_7
    //     0xe7effc: ldur            w0, [x3, #7]
    // 0xe7f000: r4 = LoadInt32Instr(r0)
    //     0xe7f000: sbfx            x4, x0, #1, #0x1f
    // 0xe7f004: stur            x4, [fp, #-0x30]
    // 0xe7f008: r0 = LoadClassIdInstr(r3)
    //     0xe7f008: ldur            x0, [x3, #-1]
    //     0xe7f00c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7f010: lsl             x0, x0, #1
    // 0xe7f014: stur            x0, [fp, #-0x28]
    // 0xe7f018: r1 = 0
    //     0xe7f018: movz            x1, #0
    // 0xe7f01c: CheckStackOverflow
    //     0xe7f01c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f020: cmp             SP, x16
    //     0xe7f024: b.ls            #0xe7f1d0
    // 0xe7f028: cmp             x1, x4
    // 0xe7f02c: b.ge            #0xe7f1b0
    // 0xe7f030: cmp             w0, #0xbc
    // 0xe7f034: b.ne            #0xe7f044
    // 0xe7f038: ArrayLoad: r5 = r3[r1]  ; TypedUnsigned_1
    //     0xe7f038: add             x16, x3, x1
    //     0xe7f03c: ldrb            w5, [x16, #0xf]
    // 0xe7f040: b               #0xe7f04c
    // 0xe7f044: add             x16, x3, x1, lsl #1
    // 0xe7f048: ldurh           w5, [x16, #0xf]
    // 0xe7f04c: lsl             x6, x5, #1
    // 0xe7f050: stur            x6, [fp, #-8]
    // 0xe7f054: add             x7, x1, #1
    // 0xe7f058: stur            x7, [fp, #-0x20]
    // 0xe7f05c: cmp             x5, #0x21
    // 0xe7f060: b.lt            #0xe7f0ac
    // 0xe7f064: cmp             x5, #0x7e
    // 0xe7f068: b.gt            #0xe7f0ac
    // 0xe7f06c: cmp             x5, #0x23
    // 0xe7f070: b.eq            #0xe7f0ac
    // 0xe7f074: cmp             x5, #0x2f
    // 0xe7f078: b.ne            #0xe7f084
    // 0xe7f07c: LoadField: r1 = r2->field_b
    //     0xe7f07c: ldur            w1, [x2, #0xb]
    // 0xe7f080: cbnz            w1, #0xe7f0ac
    // 0xe7f084: cmp             x5, #0x5b
    // 0xe7f088: b.eq            #0xe7f0ac
    // 0xe7f08c: cmp             x5, #0x5d
    // 0xe7f090: b.eq            #0xe7f0ac
    // 0xe7f094: cmp             x5, #0x28
    // 0xe7f098: b.eq            #0xe7f0ac
    // 0xe7f09c: cmp             x5, #0x3c
    // 0xe7f0a0: b.eq            #0xe7f0ac
    // 0xe7f0a4: cmp             x5, #0x3e
    // 0xe7f0a8: b.ne            #0xe7f144
    // 0xe7f0ac: LoadField: r1 = r2->field_b
    //     0xe7f0ac: ldur            w1, [x2, #0xb]
    // 0xe7f0b0: LoadField: r5 = r2->field_f
    //     0xe7f0b0: ldur            w5, [x2, #0xf]
    // 0xe7f0b4: DecompressPointer r5
    //     0xe7f0b4: add             x5, x5, HEAP, lsl #32
    // 0xe7f0b8: LoadField: r8 = r5->field_b
    //     0xe7f0b8: ldur            w8, [x5, #0xb]
    // 0xe7f0bc: r5 = LoadInt32Instr(r1)
    //     0xe7f0bc: sbfx            x5, x1, #1, #0x1f
    // 0xe7f0c0: stur            x5, [fp, #-0x18]
    // 0xe7f0c4: r1 = LoadInt32Instr(r8)
    //     0xe7f0c4: sbfx            x1, x8, #1, #0x1f
    // 0xe7f0c8: cmp             x5, x1
    // 0xe7f0cc: b.ne            #0xe7f0d8
    // 0xe7f0d0: mov             x1, x2
    // 0xe7f0d4: r0 = _growToNextCapacity()
    //     0xe7f0d4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe7f0d8: ldur            x0, [fp, #-0x40]
    // 0xe7f0dc: ldur            x1, [fp, #-0x18]
    // 0xe7f0e0: add             x2, x1, #1
    // 0xe7f0e4: lsl             x3, x2, #1
    // 0xe7f0e8: StoreField: r0->field_b = r3
    //     0xe7f0e8: stur            w3, [x0, #0xb]
    // 0xe7f0ec: LoadField: r2 = r0->field_f
    //     0xe7f0ec: ldur            w2, [x0, #0xf]
    // 0xe7f0f0: DecompressPointer r2
    //     0xe7f0f0: add             x2, x2, HEAP, lsl #32
    // 0xe7f0f4: add             x3, x2, x1, lsl #2
    // 0xe7f0f8: r16 = 70
    //     0xe7f0f8: movz            x16, #0x46
    // 0xe7f0fc: StoreField: r3->field_f = r16
    //     0xe7f0fc: stur            w16, [x3, #0xf]
    // 0xe7f100: ldur            x1, [fp, #-8]
    // 0xe7f104: r0 = _toPow2String()
    //     0xe7f104: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0xe7f108: mov             x1, x0
    // 0xe7f10c: r2 = 2
    //     0xe7f10c: movz            x2, #0x2
    // 0xe7f110: r3 = "0"
    //     0xe7f110: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xe7f114: r0 = padLeft()
    //     0xe7f114: bl              #0xebe370  ; [dart:core] _OneByteString::padLeft
    // 0xe7f118: r1 = <int>
    //     0xe7f118: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe7f11c: stur            x0, [fp, #-0x48]
    // 0xe7f120: r0 = CodeUnits()
    //     0xe7f120: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0xe7f124: mov             x1, x0
    // 0xe7f128: ldur            x0, [fp, #-0x48]
    // 0xe7f12c: StoreField: r1->field_b = r0
    //     0xe7f12c: stur            w0, [x1, #0xb]
    // 0xe7f130: mov             x2, x1
    // 0xe7f134: ldur            x1, [fp, #-0x40]
    // 0xe7f138: r0 = addAll()
    //     0xe7f138: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xe7f13c: ldur            x2, [fp, #-0x40]
    // 0xe7f140: b               #0xe7f19c
    // 0xe7f144: mov             x0, x2
    // 0xe7f148: LoadField: r1 = r0->field_b
    //     0xe7f148: ldur            w1, [x0, #0xb]
    // 0xe7f14c: LoadField: r2 = r0->field_f
    //     0xe7f14c: ldur            w2, [x0, #0xf]
    // 0xe7f150: DecompressPointer r2
    //     0xe7f150: add             x2, x2, HEAP, lsl #32
    // 0xe7f154: LoadField: r3 = r2->field_b
    //     0xe7f154: ldur            w3, [x2, #0xb]
    // 0xe7f158: r2 = LoadInt32Instr(r1)
    //     0xe7f158: sbfx            x2, x1, #1, #0x1f
    // 0xe7f15c: stur            x2, [fp, #-0x18]
    // 0xe7f160: r1 = LoadInt32Instr(r3)
    //     0xe7f160: sbfx            x1, x3, #1, #0x1f
    // 0xe7f164: cmp             x2, x1
    // 0xe7f168: b.ne            #0xe7f174
    // 0xe7f16c: mov             x1, x0
    // 0xe7f170: r0 = _growToNextCapacity()
    //     0xe7f170: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe7f174: ldur            x2, [fp, #-0x40]
    // 0xe7f178: ldur            x1, [fp, #-8]
    // 0xe7f17c: ldur            x0, [fp, #-0x18]
    // 0xe7f180: add             x3, x0, #1
    // 0xe7f184: lsl             x4, x3, #1
    // 0xe7f188: StoreField: r2->field_b = r4
    //     0xe7f188: stur            w4, [x2, #0xb]
    // 0xe7f18c: LoadField: r3 = r2->field_f
    //     0xe7f18c: ldur            w3, [x2, #0xf]
    // 0xe7f190: DecompressPointer r3
    //     0xe7f190: add             x3, x3, HEAP, lsl #32
    // 0xe7f194: ArrayStore: r3[r0] = r1  ; Unknown_4
    //     0xe7f194: add             x4, x3, x0, lsl #2
    //     0xe7f198: stur            w1, [x4, #0xf]
    // 0xe7f19c: ldur            x1, [fp, #-0x20]
    // 0xe7f1a0: ldur            x3, [fp, #-0x38]
    // 0xe7f1a4: ldur            x0, [fp, #-0x28]
    // 0xe7f1a8: ldur            x4, [fp, #-0x30]
    // 0xe7f1ac: b               #0xe7f01c
    // 0xe7f1b0: ldur            x1, [fp, #-0x10]
    // 0xe7f1b4: r0 = putBytes()
    //     0xe7f1b4: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe7f1b8: r0 = Null
    //     0xe7f1b8: mov             x0, NULL
    // 0xe7f1bc: LeaveFrame
    //     0xe7f1bc: mov             SP, fp
    //     0xe7f1c0: ldp             fp, lr, [SP], #0x10
    // 0xe7f1c4: ret
    //     0xe7f1c4: ret             
    // 0xe7f1c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f1c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f1cc: b               #0xe7efd8
    // 0xe7f1d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f1d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f1d4: b               #0xe7f028
  }
}
