// lib: , url: package:pdf/src/pdf/format/num.dart

// class id: 1050788, size: 0x8
class :: {
}

// class id: 907, size: 0xc, field offset: 0x8
//   const constructor, 
class PdfNumList extends PdfDataType {

  _ ==(/* No info */) {
    // ** addr: 0xd7c870, size: 0x5c
    // 0xd7c870: ldr             x1, [SP]
    // 0xd7c874: cmp             w1, NULL
    // 0xd7c878: b.ne            #0xd7c884
    // 0xd7c87c: r0 = false
    //     0xd7c87c: add             x0, NULL, #0x30  ; false
    // 0xd7c880: ret
    //     0xd7c880: ret             
    // 0xd7c884: r2 = 60
    //     0xd7c884: movz            x2, #0x3c
    // 0xd7c888: branchIfSmi(r1, 0xd7c894)
    //     0xd7c888: tbz             w1, #0, #0xd7c894
    // 0xd7c88c: r2 = LoadClassIdInstr(r1)
    //     0xd7c88c: ldur            x2, [x1, #-1]
    //     0xd7c890: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c894: cmp             x2, #0x38b
    // 0xd7c898: b.ne            #0xd7c8c4
    // 0xd7c89c: ldr             x2, [SP, #8]
    // 0xd7c8a0: LoadField: r3 = r2->field_7
    //     0xd7c8a0: ldur            w3, [x2, #7]
    // 0xd7c8a4: DecompressPointer r3
    //     0xd7c8a4: add             x3, x3, HEAP, lsl #32
    // 0xd7c8a8: LoadField: r2 = r1->field_7
    //     0xd7c8a8: ldur            w2, [x1, #7]
    // 0xd7c8ac: DecompressPointer r2
    //     0xd7c8ac: add             x2, x2, HEAP, lsl #32
    // 0xd7c8b0: cmp             w3, w2
    // 0xd7c8b4: r16 = true
    //     0xd7c8b4: add             x16, NULL, #0x20  ; true
    // 0xd7c8b8: r17 = false
    //     0xd7c8b8: add             x17, NULL, #0x30  ; false
    // 0xd7c8bc: csel            x0, x16, x17, eq
    // 0xd7c8c0: ret
    //     0xd7c8c0: ret             
    // 0xd7c8c4: r0 = false
    //     0xd7c8c4: add             x0, NULL, #0x30  ; false
    // 0xd7c8c8: ret
    //     0xd7c8c8: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7f3f0, size: 0x148
    // 0xe7f3f0: EnterFrame
    //     0xe7f3f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7f3f4: mov             fp, SP
    // 0xe7f3f8: AllocStack(0x28)
    //     0xe7f3f8: sub             SP, SP, #0x28
    // 0xe7f3fc: SetupParameters(dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0xe7f3fc: mov             x0, x3
    //     0xe7f400: stur            x3, [fp, #-0x20]
    //     0xe7f404: mov             x3, x2
    //     0xe7f408: stur            x2, [fp, #-0x18]
    // 0xe7f40c: CheckStackOverflow
    //     0xe7f40c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f410: cmp             SP, x16
    //     0xe7f414: b.ls            #0xe7f520
    // 0xe7f418: LoadField: r4 = r1->field_7
    //     0xe7f418: ldur            w4, [x1, #7]
    // 0xe7f41c: DecompressPointer r4
    //     0xe7f41c: add             x4, x4, HEAP, lsl #32
    // 0xe7f420: stur            x4, [fp, #-0x10]
    // 0xe7f424: r5 = 0
    //     0xe7f424: movz            x5, #0
    // 0xe7f428: stur            x5, [fp, #-8]
    // 0xe7f42c: CheckStackOverflow
    //     0xe7f42c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f430: cmp             SP, x16
    //     0xe7f434: b.ls            #0xe7f528
    // 0xe7f438: LoadField: r1 = r4->field_b
    //     0xe7f438: ldur            w1, [x4, #0xb]
    // 0xe7f43c: r2 = LoadInt32Instr(r1)
    //     0xe7f43c: sbfx            x2, x1, #1, #0x1f
    // 0xe7f440: cmp             x5, x2
    // 0xe7f444: b.ge            #0xe7f510
    // 0xe7f448: cmp             x5, #0
    // 0xe7f44c: b.le            #0xe7f49c
    // 0xe7f450: mov             x1, x0
    // 0xe7f454: r2 = 1
    //     0xe7f454: movz            x2, #0x1
    // 0xe7f458: r0 = _ensureCapacity()
    //     0xe7f458: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f45c: ldur            x3, [fp, #-0x20]
    // 0xe7f460: LoadField: r2 = r3->field_7
    //     0xe7f460: ldur            w2, [x3, #7]
    // 0xe7f464: DecompressPointer r2
    //     0xe7f464: add             x2, x2, HEAP, lsl #32
    // 0xe7f468: LoadField: r4 = r3->field_b
    //     0xe7f468: ldur            x4, [x3, #0xb]
    // 0xe7f46c: add             x0, x4, #1
    // 0xe7f470: StoreField: r3->field_b = r0
    //     0xe7f470: stur            x0, [x3, #0xb]
    // 0xe7f474: LoadField: r0 = r2->field_13
    //     0xe7f474: ldur            w0, [x2, #0x13]
    // 0xe7f478: r1 = LoadInt32Instr(r0)
    //     0xe7f478: sbfx            x1, x0, #1, #0x1f
    // 0xe7f47c: mov             x0, x1
    // 0xe7f480: mov             x1, x4
    // 0xe7f484: cmp             x1, x0
    // 0xe7f488: b.hs            #0xe7f530
    // 0xe7f48c: r5 = 32
    //     0xe7f48c: movz            x5, #0x20
    // 0xe7f490: ArrayStore: r2[r4] = r5  ; TypeUnknown_1
    //     0xe7f490: add             x0, x2, x4
    //     0xe7f494: strb            w5, [x0, #0x17]
    // 0xe7f498: b               #0xe7f4a4
    // 0xe7f49c: mov             x3, x0
    // 0xe7f4a0: r5 = 32
    //     0xe7f4a0: movz            x5, #0x20
    // 0xe7f4a4: ldur            x4, [fp, #-8]
    // 0xe7f4a8: ldur            x2, [fp, #-0x10]
    // 0xe7f4ac: LoadField: r0 = r2->field_b
    //     0xe7f4ac: ldur            w0, [x2, #0xb]
    // 0xe7f4b0: r1 = LoadInt32Instr(r0)
    //     0xe7f4b0: sbfx            x1, x0, #1, #0x1f
    // 0xe7f4b4: mov             x0, x1
    // 0xe7f4b8: mov             x1, x4
    // 0xe7f4bc: cmp             x1, x0
    // 0xe7f4c0: b.hs            #0xe7f534
    // 0xe7f4c4: LoadField: r0 = r2->field_f
    //     0xe7f4c4: ldur            w0, [x2, #0xf]
    // 0xe7f4c8: DecompressPointer r0
    //     0xe7f4c8: add             x0, x0, HEAP, lsl #32
    // 0xe7f4cc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xe7f4cc: add             x16, x0, x4, lsl #2
    //     0xe7f4d0: ldur            w1, [x16, #0xf]
    // 0xe7f4d4: DecompressPointer r1
    //     0xe7f4d4: add             x1, x1, HEAP, lsl #32
    // 0xe7f4d8: stur            x1, [fp, #-0x28]
    // 0xe7f4dc: r0 = PdfNum()
    //     0xe7f4dc: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe7f4e0: mov             x1, x0
    // 0xe7f4e4: ldur            x0, [fp, #-0x28]
    // 0xe7f4e8: StoreField: r1->field_7 = r0
    //     0xe7f4e8: stur            w0, [x1, #7]
    // 0xe7f4ec: ldur            x2, [fp, #-0x18]
    // 0xe7f4f0: ldur            x3, [fp, #-0x20]
    // 0xe7f4f4: r0 = output()
    //     0xe7f4f4: bl              #0xe7f1d8  ; [package:pdf/src/pdf/format/num.dart] PdfNum::output
    // 0xe7f4f8: ldur            x1, [fp, #-8]
    // 0xe7f4fc: add             x5, x1, #1
    // 0xe7f500: ldur            x3, [fp, #-0x18]
    // 0xe7f504: ldur            x0, [fp, #-0x20]
    // 0xe7f508: ldur            x4, [fp, #-0x10]
    // 0xe7f50c: b               #0xe7f428
    // 0xe7f510: r0 = Null
    //     0xe7f510: mov             x0, NULL
    // 0xe7f514: LeaveFrame
    //     0xe7f514: mov             SP, fp
    //     0xe7f518: ldp             fp, lr, [SP], #0x10
    // 0xe7f51c: ret
    //     0xe7f51c: ret             
    // 0xe7f520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f524: b               #0xe7f418
    // 0xe7f528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f52c: b               #0xe7f438
    // 0xe7f530: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7f530: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7f534: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7f534: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 908, size: 0xc, field offset: 0x8
//   const constructor, 
class PdfNum extends PdfDataType {

  _Mint field_8;

  _ |(/* No info */) {
    // ** addr: 0x7b64f0, size: 0xd8
    // 0x7b64f0: EnterFrame
    //     0x7b64f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7b64f4: mov             fp, SP
    // 0x7b64f8: AllocStack(0x18)
    //     0x7b64f8: sub             SP, SP, #0x18
    // 0x7b64fc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x7b64fc: stur            x2, [fp, #-8]
    // 0x7b6500: CheckStackOverflow
    //     0x7b6500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6504: cmp             SP, x16
    //     0x7b6508: b.ls            #0x7b65c0
    // 0x7b650c: LoadField: r0 = r1->field_7
    //     0x7b650c: ldur            w0, [x1, #7]
    // 0x7b6510: DecompressPointer r0
    //     0x7b6510: add             x0, x0, HEAP, lsl #32
    // 0x7b6514: r1 = 60
    //     0x7b6514: movz            x1, #0x3c
    // 0x7b6518: branchIfSmi(r0, 0x7b6524)
    //     0x7b6518: tbz             w0, #0, #0x7b6524
    // 0x7b651c: r1 = LoadClassIdInstr(r0)
    //     0x7b651c: ldur            x1, [x0, #-1]
    //     0x7b6520: ubfx            x1, x1, #0xc, #0x14
    // 0x7b6524: str             x0, [SP]
    // 0x7b6528: mov             x0, x1
    // 0x7b652c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7b652c: sub             lr, x0, #1, lsl #12
    //     0x7b6530: ldr             lr, [x21, lr, lsl #3]
    //     0x7b6534: blr             lr
    // 0x7b6538: mov             x1, x0
    // 0x7b653c: ldur            x0, [fp, #-8]
    // 0x7b6540: stur            x1, [fp, #-0x10]
    // 0x7b6544: LoadField: r2 = r0->field_7
    //     0x7b6544: ldur            w2, [x0, #7]
    // 0x7b6548: DecompressPointer r2
    //     0x7b6548: add             x2, x2, HEAP, lsl #32
    // 0x7b654c: r0 = 60
    //     0x7b654c: movz            x0, #0x3c
    // 0x7b6550: branchIfSmi(r2, 0x7b655c)
    //     0x7b6550: tbz             w2, #0, #0x7b655c
    // 0x7b6554: r0 = LoadClassIdInstr(r2)
    //     0x7b6554: ldur            x0, [x2, #-1]
    //     0x7b6558: ubfx            x0, x0, #0xc, #0x14
    // 0x7b655c: str             x2, [SP]
    // 0x7b6560: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7b6560: sub             lr, x0, #1, lsl #12
    //     0x7b6564: ldr             lr, [x21, lr, lsl #3]
    //     0x7b6568: blr             lr
    // 0x7b656c: mov             x1, x0
    // 0x7b6570: ldur            x0, [fp, #-0x10]
    // 0x7b6574: r2 = LoadInt32Instr(r0)
    //     0x7b6574: sbfx            x2, x0, #1, #0x1f
    //     0x7b6578: tbz             w0, #0, #0x7b6580
    //     0x7b657c: ldur            x2, [x0, #7]
    // 0x7b6580: r0 = LoadInt32Instr(r1)
    //     0x7b6580: sbfx            x0, x1, #1, #0x1f
    //     0x7b6584: tbz             w1, #0, #0x7b658c
    //     0x7b6588: ldur            x0, [x1, #7]
    // 0x7b658c: orr             x3, x2, x0
    // 0x7b6590: r0 = BoxInt64Instr(r3)
    //     0x7b6590: sbfiz           x0, x3, #1, #0x1f
    //     0x7b6594: cmp             x3, x0, asr #1
    //     0x7b6598: b.eq            #0x7b65a4
    //     0x7b659c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b65a0: stur            x3, [x0, #7]
    // 0x7b65a4: stur            x0, [fp, #-8]
    // 0x7b65a8: r0 = PdfNum()
    //     0x7b65a8: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b65ac: ldur            x1, [fp, #-8]
    // 0x7b65b0: StoreField: r0->field_7 = r1
    //     0x7b65b0: stur            w1, [x0, #7]
    // 0x7b65b4: LeaveFrame
    //     0x7b65b4: mov             SP, fp
    //     0x7b65b8: ldp             fp, lr, [SP], #0x10
    // 0x7b65bc: ret
    //     0x7b65bc: ret             
    // 0x7b65c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b65c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b65c4: b               #0x7b650c
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf19d8, size: 0x5c
    // 0xbf19d8: EnterFrame
    //     0xbf19d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf19dc: mov             fp, SP
    // 0xbf19e0: AllocStack(0x8)
    //     0xbf19e0: sub             SP, SP, #8
    // 0xbf19e4: CheckStackOverflow
    //     0xbf19e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf19e8: cmp             SP, x16
    //     0xbf19ec: b.ls            #0xbf1a2c
    // 0xbf19f0: ldr             x0, [fp, #0x10]
    // 0xbf19f4: LoadField: r1 = r0->field_7
    //     0xbf19f4: ldur            w1, [x0, #7]
    // 0xbf19f8: DecompressPointer r1
    //     0xbf19f8: add             x1, x1, HEAP, lsl #32
    // 0xbf19fc: r0 = 60
    //     0xbf19fc: movz            x0, #0x3c
    // 0xbf1a00: branchIfSmi(r1, 0xbf1a0c)
    //     0xbf1a00: tbz             w1, #0, #0xbf1a0c
    // 0xbf1a04: r0 = LoadClassIdInstr(r1)
    //     0xbf1a04: ldur            x0, [x1, #-1]
    //     0xbf1a08: ubfx            x0, x0, #0xc, #0x14
    // 0xbf1a0c: str             x1, [SP]
    // 0xbf1a10: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1a10: movz            x17, #0x64af
    //     0xbf1a14: add             lr, x0, x17
    //     0xbf1a18: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1a1c: blr             lr
    // 0xbf1a20: LeaveFrame
    //     0xbf1a20: mov             SP, fp
    //     0xbf1a24: ldp             fp, lr, [SP], #0x10
    // 0xbf1a28: ret
    //     0xbf1a28: ret             
    // 0xbf1a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1a30: b               #0xbf19f0
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c7cc, size: 0xa4
    // 0xd7c7cc: EnterFrame
    //     0xd7c7cc: stp             fp, lr, [SP, #-0x10]!
    //     0xd7c7d0: mov             fp, SP
    // 0xd7c7d4: AllocStack(0x10)
    //     0xd7c7d4: sub             SP, SP, #0x10
    // 0xd7c7d8: CheckStackOverflow
    //     0xd7c7d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7c7dc: cmp             SP, x16
    //     0xd7c7e0: b.ls            #0xd7c868
    // 0xd7c7e4: ldr             x0, [fp, #0x10]
    // 0xd7c7e8: cmp             w0, NULL
    // 0xd7c7ec: b.ne            #0xd7c800
    // 0xd7c7f0: r0 = false
    //     0xd7c7f0: add             x0, NULL, #0x30  ; false
    // 0xd7c7f4: LeaveFrame
    //     0xd7c7f4: mov             SP, fp
    //     0xd7c7f8: ldp             fp, lr, [SP], #0x10
    // 0xd7c7fc: ret
    //     0xd7c7fc: ret             
    // 0xd7c800: r1 = 60
    //     0xd7c800: movz            x1, #0x3c
    // 0xd7c804: branchIfSmi(r0, 0xd7c810)
    //     0xd7c804: tbz             w0, #0, #0xd7c810
    // 0xd7c808: r1 = LoadClassIdInstr(r0)
    //     0xd7c808: ldur            x1, [x0, #-1]
    //     0xd7c80c: ubfx            x1, x1, #0xc, #0x14
    // 0xd7c810: cmp             x1, #0x38c
    // 0xd7c814: b.ne            #0xd7c858
    // 0xd7c818: ldr             x1, [fp, #0x18]
    // 0xd7c81c: LoadField: r2 = r1->field_7
    //     0xd7c81c: ldur            w2, [x1, #7]
    // 0xd7c820: DecompressPointer r2
    //     0xd7c820: add             x2, x2, HEAP, lsl #32
    // 0xd7c824: LoadField: r1 = r0->field_7
    //     0xd7c824: ldur            w1, [x0, #7]
    // 0xd7c828: DecompressPointer r1
    //     0xd7c828: add             x1, x1, HEAP, lsl #32
    // 0xd7c82c: r0 = 60
    //     0xd7c82c: movz            x0, #0x3c
    // 0xd7c830: branchIfSmi(r2, 0xd7c83c)
    //     0xd7c830: tbz             w2, #0, #0xd7c83c
    // 0xd7c834: r0 = LoadClassIdInstr(r2)
    //     0xd7c834: ldur            x0, [x2, #-1]
    //     0xd7c838: ubfx            x0, x0, #0xc, #0x14
    // 0xd7c83c: stp             x1, x2, [SP]
    // 0xd7c840: mov             lr, x0
    // 0xd7c844: ldr             lr, [x21, lr, lsl #3]
    // 0xd7c848: blr             lr
    // 0xd7c84c: LeaveFrame
    //     0xd7c84c: mov             SP, fp
    //     0xd7c850: ldp             fp, lr, [SP], #0x10
    // 0xd7c854: ret
    //     0xd7c854: ret             
    // 0xd7c858: r0 = false
    //     0xd7c858: add             x0, NULL, #0x30  ; false
    // 0xd7c85c: LeaveFrame
    //     0xd7c85c: mov             SP, fp
    //     0xd7c860: ldp             fp, lr, [SP], #0x10
    // 0xd7c864: ret
    //     0xd7c864: ret             
    // 0xd7c868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7c868: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7c86c: b               #0xd7c7e4
  }
  _ output(/* No info */) {
    // ** addr: 0xe7f1d8, size: 0x218
    // 0xe7f1d8: EnterFrame
    //     0xe7f1d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7f1dc: mov             fp, SP
    // 0xe7f1e0: AllocStack(0x30)
    //     0xe7f1e0: sub             SP, SP, #0x30
    // 0xe7f1e4: SetupParameters(PdfNum this /* r1 => r0 */, dynamic _ /* r3 => r1, fp-0x8 */)
    //     0xe7f1e4: mov             x0, x1
    //     0xe7f1e8: mov             x1, x3
    //     0xe7f1ec: stur            x3, [fp, #-8]
    // 0xe7f1f0: CheckStackOverflow
    //     0xe7f1f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f1f4: cmp             SP, x16
    //     0xe7f1f8: b.ls            #0xe7f3e0
    // 0xe7f1fc: LoadField: r2 = r0->field_7
    //     0xe7f1fc: ldur            w2, [x0, #7]
    // 0xe7f200: DecompressPointer r2
    //     0xe7f200: add             x2, x2, HEAP, lsl #32
    // 0xe7f204: r0 = 60
    //     0xe7f204: movz            x0, #0x3c
    // 0xe7f208: branchIfSmi(r2, 0xe7f214)
    //     0xe7f208: tbz             w2, #0, #0xe7f214
    // 0xe7f20c: r0 = LoadClassIdInstr(r2)
    //     0xe7f20c: ldur            x0, [x2, #-1]
    //     0xe7f210: ubfx            x0, x0, #0xc, #0x14
    // 0xe7f214: sub             x16, x0, #0x3c
    // 0xe7f218: cmp             x16, #1
    // 0xe7f21c: b.hi            #0xe7f27c
    // 0xe7f220: r0 = 60
    //     0xe7f220: movz            x0, #0x3c
    // 0xe7f224: branchIfSmi(r2, 0xe7f230)
    //     0xe7f224: tbz             w2, #0, #0xe7f230
    // 0xe7f228: r0 = LoadClassIdInstr(r2)
    //     0xe7f228: ldur            x0, [x2, #-1]
    //     0xe7f22c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7f230: str             x2, [SP]
    // 0xe7f234: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe7f234: sub             lr, x0, #1, lsl #12
    //     0xe7f238: ldr             lr, [x21, lr, lsl #3]
    //     0xe7f23c: blr             lr
    // 0xe7f240: r1 = 60
    //     0xe7f240: movz            x1, #0x3c
    // 0xe7f244: branchIfSmi(r0, 0xe7f250)
    //     0xe7f244: tbz             w0, #0, #0xe7f250
    // 0xe7f248: r1 = LoadClassIdInstr(r0)
    //     0xe7f248: ldur            x1, [x0, #-1]
    //     0xe7f24c: ubfx            x1, x1, #0xc, #0x14
    // 0xe7f250: str             x0, [SP]
    // 0xe7f254: mov             x0, x1
    // 0xe7f258: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7f258: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7f25c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7f25c: movz            x17, #0x2b03
    //     0xe7f260: add             lr, x0, x17
    //     0xe7f264: ldr             lr, [x21, lr, lsl #3]
    //     0xe7f268: blr             lr
    // 0xe7f26c: ldur            x1, [fp, #-8]
    // 0xe7f270: mov             x2, x0
    // 0xe7f274: r0 = putString()
    //     0xe7f274: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7f278: b               #0xe7f3d0
    // 0xe7f27c: r0 = 60
    //     0xe7f27c: movz            x0, #0x3c
    // 0xe7f280: branchIfSmi(r2, 0xe7f28c)
    //     0xe7f280: tbz             w2, #0, #0xe7f28c
    // 0xe7f284: r0 = LoadClassIdInstr(r2)
    //     0xe7f284: ldur            x0, [x2, #-1]
    //     0xe7f288: ubfx            x0, x0, #0xc, #0x14
    // 0xe7f28c: mov             x1, x2
    // 0xe7f290: r2 = 5
    //     0xe7f290: movz            x2, #0x5
    // 0xe7f294: r0 = GDT[cid_x0 + -0xfac]()
    //     0xe7f294: sub             lr, x0, #0xfac
    //     0xe7f298: ldr             lr, [x21, lr, lsl #3]
    //     0xe7f29c: blr             lr
    // 0xe7f2a0: mov             x3, x0
    // 0xe7f2a4: stur            x3, [fp, #-0x10]
    // 0xe7f2a8: r0 = LoadClassIdInstr(r3)
    //     0xe7f2a8: ldur            x0, [x3, #-1]
    //     0xe7f2ac: ubfx            x0, x0, #0xc, #0x14
    // 0xe7f2b0: mov             x1, x3
    // 0xe7f2b4: r2 = "."
    //     0xe7f2b4: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xe7f2b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7f2b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe7f2bc: r0 = GDT[cid_x0 + -0xffc]()
    //     0xe7f2bc: sub             lr, x0, #0xffc
    //     0xe7f2c0: ldr             lr, [x21, lr, lsl #3]
    //     0xe7f2c4: blr             lr
    // 0xe7f2c8: tbnz            w0, #4, #0xe7f3c4
    // 0xe7f2cc: ldur            x2, [fp, #-0x10]
    // 0xe7f2d0: LoadField: r0 = r2->field_7
    //     0xe7f2d0: ldur            w0, [x2, #7]
    // 0xe7f2d4: r1 = LoadInt32Instr(r0)
    //     0xe7f2d4: sbfx            x1, x0, #1, #0x1f
    // 0xe7f2d8: sub             x0, x1, #1
    // 0xe7f2dc: mov             x3, x0
    // 0xe7f2e0: stur            x3, [fp, #-0x20]
    // 0xe7f2e4: CheckStackOverflow
    //     0xe7f2e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f2e8: cmp             SP, x16
    //     0xe7f2ec: b.ls            #0xe7f3e8
    // 0xe7f2f0: r0 = BoxInt64Instr(r3)
    //     0xe7f2f0: sbfiz           x0, x3, #1, #0x1f
    //     0xe7f2f4: cmp             x3, x0, asr #1
    //     0xe7f2f8: b.eq            #0xe7f304
    //     0xe7f2fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7f300: stur            x3, [x0, #7]
    // 0xe7f304: stur            x0, [fp, #-0x18]
    // 0xe7f308: stp             x0, x2, [SP]
    // 0xe7f30c: r0 = []()
    //     0xe7f30c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xe7f310: r1 = LoadClassIdInstr(r0)
    //     0xe7f310: ldur            x1, [x0, #-1]
    //     0xe7f314: ubfx            x1, x1, #0xc, #0x14
    // 0xe7f318: r16 = "0"
    //     0xe7f318: ldr             x16, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xe7f31c: stp             x16, x0, [SP]
    // 0xe7f320: mov             x0, x1
    // 0xe7f324: mov             lr, x0
    // 0xe7f328: ldr             lr, [x21, lr, lsl #3]
    // 0xe7f32c: blr             lr
    // 0xe7f330: tbnz            w0, #4, #0xe7f344
    // 0xe7f334: ldur            x0, [fp, #-0x20]
    // 0xe7f338: sub             x3, x0, #1
    // 0xe7f33c: ldur            x2, [fp, #-0x10]
    // 0xe7f340: b               #0xe7f2e0
    // 0xe7f344: ldur            x0, [fp, #-0x20]
    // 0xe7f348: ldur            x16, [fp, #-0x10]
    // 0xe7f34c: ldur            lr, [fp, #-0x18]
    // 0xe7f350: stp             lr, x16, [SP]
    // 0xe7f354: r0 = []()
    //     0xe7f354: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xe7f358: r1 = LoadClassIdInstr(r0)
    //     0xe7f358: ldur            x1, [x0, #-1]
    //     0xe7f35c: ubfx            x1, x1, #0xc, #0x14
    // 0xe7f360: r16 = "."
    //     0xe7f360: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xe7f364: stp             x16, x0, [SP]
    // 0xe7f368: mov             x0, x1
    // 0xe7f36c: mov             lr, x0
    // 0xe7f370: ldr             lr, [x21, lr, lsl #3]
    // 0xe7f374: blr             lr
    // 0xe7f378: tbnz            w0, #4, #0xe7f38c
    // 0xe7f37c: ldur            x0, [fp, #-0x20]
    // 0xe7f380: sub             x1, x0, #1
    // 0xe7f384: mov             x0, x1
    // 0xe7f388: b               #0xe7f390
    // 0xe7f38c: ldur            x0, [fp, #-0x20]
    // 0xe7f390: add             x2, x0, #1
    // 0xe7f394: r0 = BoxInt64Instr(r2)
    //     0xe7f394: sbfiz           x0, x2, #1, #0x1f
    //     0xe7f398: cmp             x2, x0, asr #1
    //     0xe7f39c: b.eq            #0xe7f3a8
    //     0xe7f3a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7f3a4: stur            x2, [x0, #7]
    // 0xe7f3a8: str             x0, [SP]
    // 0xe7f3ac: ldur            x1, [fp, #-0x10]
    // 0xe7f3b0: r2 = 0
    //     0xe7f3b0: movz            x2, #0
    // 0xe7f3b4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe7f3b4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe7f3b8: r0 = substring()
    //     0xe7f3b8: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe7f3bc: mov             x2, x0
    // 0xe7f3c0: b               #0xe7f3c8
    // 0xe7f3c4: ldur            x2, [fp, #-0x10]
    // 0xe7f3c8: ldur            x1, [fp, #-8]
    // 0xe7f3cc: r0 = putString()
    //     0xe7f3cc: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7f3d0: r0 = Null
    //     0xe7f3d0: mov             x0, NULL
    // 0xe7f3d4: LeaveFrame
    //     0xe7f3d4: mov             SP, fp
    //     0xe7f3d8: ldp             fp, lr, [SP], #0x10
    // 0xe7f3dc: ret
    //     0xe7f3dc: ret             
    // 0xe7f3e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f3e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f3e4: b               #0xe7f1fc
    // 0xe7f3e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f3e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f3ec: b               #0xe7f2f0
  }
}
