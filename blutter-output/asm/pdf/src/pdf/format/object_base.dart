// lib: , url: package:pdf/src/pdf/format/object_base.dart

// class id: 1050789, size: 0x8
class :: {
}

// class id: 867, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _PdfObjectBase&Object&PdfDiagnostic extends Object
     with PdfDiagnostic {
}

// class id: 868, size: 0x24, field offset: 0x8
class PdfObjectBase<X0 bound PdfDataType> extends _PdfObjectBase&Object&PdfDiagnostic {

  _ ref(/* No info */) {
    // ** addr: 0x7b5c90, size: 0x30
    // 0x7b5c90: EnterFrame
    //     0x7b5c90: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5c94: mov             fp, SP
    // 0x7b5c98: AllocStack(0x8)
    //     0x7b5c98: sub             SP, SP, #8
    // 0x7b5c9c: LoadField: r0 = r1->field_b
    //     0x7b5c9c: ldur            x0, [x1, #0xb]
    // 0x7b5ca0: stur            x0, [fp, #-8]
    // 0x7b5ca4: r0 = PdfIndirect()
    //     0x7b5ca4: bl              #0x7b5cc0  ; AllocatePdfIndirectStub -> PdfIndirect (size=0x18)
    // 0x7b5ca8: ldur            x1, [fp, #-8]
    // 0x7b5cac: StoreField: r0->field_7 = r1
    //     0x7b5cac: stur            x1, [x0, #7]
    // 0x7b5cb0: StoreField: r0->field_f = rZR
    //     0x7b5cb0: stur            xzr, [x0, #0xf]
    // 0x7b5cb4: LeaveFrame
    //     0x7b5cb4: mov             SP, fp
    //     0x7b5cb8: ldp             fp, lr, [SP], #0x10
    // 0x7b5cbc: ret
    //     0x7b5cbc: ret             
  }
  _ writeContent(/* No info */) {
    // ** addr: 0x862194, size: 0x68
    // 0x862194: EnterFrame
    //     0x862194: stp             fp, lr, [SP, #-0x10]!
    //     0x862198: mov             fp, SP
    // 0x86219c: AllocStack(0x8)
    //     0x86219c: sub             SP, SP, #8
    // 0x8621a0: SetupParameters(PdfObjectBase<X0 bound PdfDataType> this /* r1 => r2 */, dynamic _ /* r2 => r4, fp-0x8 */)
    //     0x8621a0: mov             x4, x2
    //     0x8621a4: stur            x2, [fp, #-8]
    //     0x8621a8: mov             x2, x1
    // 0x8621ac: CheckStackOverflow
    //     0x8621ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8621b0: cmp             SP, x16
    //     0x8621b4: b.ls            #0x8621f4
    // 0x8621b8: LoadField: r1 = r2->field_1b
    //     0x8621b8: ldur            w1, [x2, #0x1b]
    // 0x8621bc: DecompressPointer r1
    //     0x8621bc: add             x1, x1, HEAP, lsl #32
    // 0x8621c0: r0 = LoadClassIdInstr(r1)
    //     0x8621c0: ldur            x0, [x1, #-1]
    //     0x8621c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8621c8: mov             x3, x4
    // 0x8621cc: r0 = GDT[cid_x0 + -0xf87]()
    //     0x8621cc: sub             lr, x0, #0xf87
    //     0x8621d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8621d4: blr             lr
    // 0x8621d8: ldur            x1, [fp, #-8]
    // 0x8621dc: r2 = 10
    //     0x8621dc: movz            x2, #0xa
    // 0x8621e0: r0 = putByte()
    //     0x8621e0: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0x8621e4: r0 = Null
    //     0x8621e4: mov             x0, NULL
    // 0x8621e8: LeaveFrame
    //     0x8621e8: mov             SP, fp
    //     0x8621ec: ldp             fp, lr, [SP], #0x10
    // 0x8621f0: ret
    //     0x8621f0: ret             
    // 0x8621f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8621f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8621f8: b               #0x8621b8
  }
  _ output(/* No info */) {
    // ** addr: 0xe80250, size: 0xf4
    // 0xe80250: EnterFrame
    //     0xe80250: stp             fp, lr, [SP, #-0x10]!
    //     0xe80254: mov             fp, SP
    // 0xe80258: AllocStack(0x28)
    //     0xe80258: sub             SP, SP, #0x28
    // 0xe8025c: SetupParameters(PdfObjectBase<X0 bound PdfDataType> this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xe8025c: mov             x4, x1
    //     0xe80260: mov             x3, x2
    //     0xe80264: stur            x1, [fp, #-0x18]
    //     0xe80268: stur            x2, [fp, #-0x20]
    // 0xe8026c: CheckStackOverflow
    //     0xe8026c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80270: cmp             SP, x16
    //     0xe80274: b.ls            #0xe8033c
    // 0xe80278: LoadField: r5 = r3->field_b
    //     0xe80278: ldur            x5, [x3, #0xb]
    // 0xe8027c: stur            x5, [fp, #-0x10]
    // 0xe80280: LoadField: r2 = r4->field_b
    //     0xe80280: ldur            x2, [x4, #0xb]
    // 0xe80284: r0 = BoxInt64Instr(r2)
    //     0xe80284: sbfiz           x0, x2, #1, #0x1f
    //     0xe80288: cmp             x2, x0, asr #1
    //     0xe8028c: b.eq            #0xe80298
    //     0xe80290: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80294: stur            x2, [x0, #7]
    // 0xe80298: r1 = Null
    //     0xe80298: mov             x1, NULL
    // 0xe8029c: r2 = 8
    //     0xe8029c: movz            x2, #0x8
    // 0xe802a0: stur            x0, [fp, #-8]
    // 0xe802a4: r0 = AllocateArray()
    //     0xe802a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe802a8: mov             x2, x0
    // 0xe802ac: ldur            x0, [fp, #-8]
    // 0xe802b0: StoreField: r2->field_f = r0
    //     0xe802b0: stur            w0, [x2, #0xf]
    // 0xe802b4: r16 = " "
    //     0xe802b4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe802b8: StoreField: r2->field_13 = r16
    //     0xe802b8: stur            w16, [x2, #0x13]
    // 0xe802bc: ldur            x3, [fp, #-0x18]
    // 0xe802c0: LoadField: r4 = r3->field_13
    //     0xe802c0: ldur            x4, [x3, #0x13]
    // 0xe802c4: r0 = BoxInt64Instr(r4)
    //     0xe802c4: sbfiz           x0, x4, #1, #0x1f
    //     0xe802c8: cmp             x4, x0, asr #1
    //     0xe802cc: b.eq            #0xe802d8
    //     0xe802d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe802d4: stur            x4, [x0, #7]
    // 0xe802d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xe802d8: stur            w0, [x2, #0x17]
    // 0xe802dc: r16 = " obj\n"
    //     0xe802dc: add             x16, PP, #0x36, lsl #12  ; [pp+0x365f8] " obj\n"
    //     0xe802e0: ldr             x16, [x16, #0x5f8]
    // 0xe802e4: StoreField: r2->field_1b = r16
    //     0xe802e4: stur            w16, [x2, #0x1b]
    // 0xe802e8: str             x2, [SP]
    // 0xe802ec: r0 = _interpolate()
    //     0xe802ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe802f0: ldur            x1, [fp, #-0x20]
    // 0xe802f4: mov             x2, x0
    // 0xe802f8: r0 = putString()
    //     0xe802f8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe802fc: ldur            x1, [fp, #-0x18]
    // 0xe80300: r0 = LoadClassIdInstr(r1)
    //     0xe80300: ldur            x0, [x1, #-1]
    //     0xe80304: ubfx            x0, x0, #0xc, #0x14
    // 0xe80308: ldur            x2, [fp, #-0x20]
    // 0xe8030c: r0 = GDT[cid_x0 + 0xf283]()
    //     0xe8030c: movz            x17, #0xf283
    //     0xe80310: add             lr, x0, x17
    //     0xe80314: ldr             lr, [x21, lr, lsl #3]
    //     0xe80318: blr             lr
    // 0xe8031c: ldur            x1, [fp, #-0x20]
    // 0xe80320: r2 = "endobj\n"
    //     0xe80320: add             x2, PP, #0x36, lsl #12  ; [pp+0x36600] "endobj\n"
    //     0xe80324: ldr             x2, [x2, #0x600]
    // 0xe80328: r0 = putString()
    //     0xe80328: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe8032c: ldur            x0, [fp, #-0x10]
    // 0xe80330: LeaveFrame
    //     0xe80330: mov             SP, fp
    //     0xe80334: ldp             fp, lr, [SP], #0x10
    // 0xe80338: ret
    //     0xe80338: ret             
    // 0xe8033c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8033c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80340: b               #0xe80278
  }
}

// class id: 901, size: 0x18, field offset: 0x8
//   const constructor, 
class PdfSettings extends Object {

  bool field_10;
  PdfVersion field_14;
}

// class id: 6818, size: 0x14, field offset: 0x14
enum PdfVersion extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d884, size: 0x64
    // 0xc4d884: EnterFrame
    //     0xc4d884: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d888: mov             fp, SP
    // 0xc4d88c: AllocStack(0x10)
    //     0xc4d88c: sub             SP, SP, #0x10
    // 0xc4d890: SetupParameters(PdfVersion this /* r1 => r0, fp-0x8 */)
    //     0xc4d890: mov             x0, x1
    //     0xc4d894: stur            x1, [fp, #-8]
    // 0xc4d898: CheckStackOverflow
    //     0xc4d898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d89c: cmp             SP, x16
    //     0xc4d8a0: b.ls            #0xc4d8e0
    // 0xc4d8a4: r1 = Null
    //     0xc4d8a4: mov             x1, NULL
    // 0xc4d8a8: r2 = 4
    //     0xc4d8a8: movz            x2, #0x4
    // 0xc4d8ac: r0 = AllocateArray()
    //     0xc4d8ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d8b0: r16 = "PdfVersion."
    //     0xc4d8b0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efc8] "PdfVersion."
    //     0xc4d8b4: ldr             x16, [x16, #0xfc8]
    // 0xc4d8b8: StoreField: r0->field_f = r16
    //     0xc4d8b8: stur            w16, [x0, #0xf]
    // 0xc4d8bc: ldur            x1, [fp, #-8]
    // 0xc4d8c0: LoadField: r2 = r1->field_f
    //     0xc4d8c0: ldur            w2, [x1, #0xf]
    // 0xc4d8c4: DecompressPointer r2
    //     0xc4d8c4: add             x2, x2, HEAP, lsl #32
    // 0xc4d8c8: StoreField: r0->field_13 = r2
    //     0xc4d8c8: stur            w2, [x0, #0x13]
    // 0xc4d8cc: str             x0, [SP]
    // 0xc4d8d0: r0 = _interpolate()
    //     0xc4d8d0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d8d4: LeaveFrame
    //     0xc4d8d4: mov             SP, fp
    //     0xc4d8d8: ldp             fp, lr, [SP], #0x10
    // 0xc4d8dc: ret
    //     0xc4d8dc: ret             
    // 0xc4d8e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d8e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d8e4: b               #0xc4d8a4
  }
}
