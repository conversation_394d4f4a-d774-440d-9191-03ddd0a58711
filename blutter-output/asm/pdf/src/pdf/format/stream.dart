// lib: , url: package:pdf/src/pdf/format/stream.dart

// class id: 1050790, size: 0x8
class :: {
}

// class id: 866, size: 0x14, field offset: 0x8
class PdfStream extends Object {

  _ output(/* No info */) {
    // ** addr: 0x7b57e4, size: 0x60
    // 0x7b57e4: EnterFrame
    //     0x7b57e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b57e8: mov             fp, SP
    // 0x7b57ec: AllocStack(0x8)
    //     0x7b57ec: sub             SP, SP, #8
    // 0x7b57f0: CheckStackOverflow
    //     0x7b57f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b57f4: cmp             SP, x16
    //     0x7b57f8: b.ls            #0x7b583c
    // 0x7b57fc: LoadField: r2 = r1->field_7
    //     0x7b57fc: ldur            w2, [x1, #7]
    // 0x7b5800: DecompressPointer r2
    //     0x7b5800: add             x2, x2, HEAP, lsl #32
    // 0x7b5804: LoadField: r3 = r1->field_b
    //     0x7b5804: ldur            x3, [x1, #0xb]
    // 0x7b5808: r0 = BoxInt64Instr(r3)
    //     0x7b5808: sbfiz           x0, x3, #1, #0x1f
    //     0x7b580c: cmp             x3, x0, asr #1
    //     0x7b5810: b.eq            #0x7b581c
    //     0x7b5814: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b5818: stur            x3, [x0, #7]
    // 0x7b581c: str             x0, [SP]
    // 0x7b5820: mov             x1, x2
    // 0x7b5824: r2 = 0
    //     0x7b5824: movz            x2, #0
    // 0x7b5828: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x7b5828: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x7b582c: r0 = sublist()
    //     0x7b582c: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0x7b5830: LeaveFrame
    //     0x7b5830: mov             SP, fp
    //     0x7b5834: ldp             fp, lr, [SP], #0x10
    // 0x7b5838: ret
    //     0x7b5838: ret             
    // 0x7b583c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b583c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5840: b               #0x7b57fc
  }
  _ putBytes(/* No info */) {
    // ** addr: 0x7b7d70, size: 0xc0
    // 0x7b7d70: EnterFrame
    //     0x7b7d70: stp             fp, lr, [SP, #-0x10]!
    //     0x7b7d74: mov             fp, SP
    // 0x7b7d78: AllocStack(0x20)
    //     0x7b7d78: sub             SP, SP, #0x20
    // 0x7b7d7c: SetupParameters(PdfStream this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b7d7c: mov             x3, x2
    //     0x7b7d80: stur            x1, [fp, #-8]
    //     0x7b7d84: stur            x2, [fp, #-0x10]
    // 0x7b7d88: CheckStackOverflow
    //     0x7b7d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b7d8c: cmp             SP, x16
    //     0x7b7d90: b.ls            #0x7b7e28
    // 0x7b7d94: r0 = LoadClassIdInstr(r3)
    //     0x7b7d94: ldur            x0, [x3, #-1]
    //     0x7b7d98: ubfx            x0, x0, #0xc, #0x14
    // 0x7b7d9c: str             x3, [SP]
    // 0x7b7da0: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7b7da0: movz            x17, #0xc834
    //     0x7b7da4: add             lr, x0, x17
    //     0x7b7da8: ldr             lr, [x21, lr, lsl #3]
    //     0x7b7dac: blr             lr
    // 0x7b7db0: r2 = LoadInt32Instr(r0)
    //     0x7b7db0: sbfx            x2, x0, #1, #0x1f
    // 0x7b7db4: ldur            x1, [fp, #-8]
    // 0x7b7db8: r0 = _ensureCapacity()
    //     0x7b7db8: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0x7b7dbc: ldur            x0, [fp, #-8]
    // 0x7b7dc0: LoadField: r1 = r0->field_7
    //     0x7b7dc0: ldur            w1, [x0, #7]
    // 0x7b7dc4: DecompressPointer r1
    //     0x7b7dc4: add             x1, x1, HEAP, lsl #32
    // 0x7b7dc8: LoadField: r2 = r0->field_b
    //     0x7b7dc8: ldur            x2, [x0, #0xb]
    // 0x7b7dcc: ldur            x3, [fp, #-0x10]
    // 0x7b7dd0: r0 = setAll()
    //     0x7b7dd0: bl              #0x7b7e30  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::setAll
    // 0x7b7dd4: ldur            x1, [fp, #-8]
    // 0x7b7dd8: LoadField: r2 = r1->field_b
    //     0x7b7dd8: ldur            x2, [x1, #0xb]
    // 0x7b7ddc: ldur            x0, [fp, #-0x10]
    // 0x7b7de0: stur            x2, [fp, #-0x18]
    // 0x7b7de4: r3 = LoadClassIdInstr(r0)
    //     0x7b7de4: ldur            x3, [x0, #-1]
    //     0x7b7de8: ubfx            x3, x3, #0xc, #0x14
    // 0x7b7dec: str             x0, [SP]
    // 0x7b7df0: mov             x0, x3
    // 0x7b7df4: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7b7df4: movz            x17, #0xc834
    //     0x7b7df8: add             lr, x0, x17
    //     0x7b7dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x7b7e00: blr             lr
    // 0x7b7e04: r1 = LoadInt32Instr(r0)
    //     0x7b7e04: sbfx            x1, x0, #1, #0x1f
    // 0x7b7e08: ldur            x2, [fp, #-0x18]
    // 0x7b7e0c: add             x3, x2, x1
    // 0x7b7e10: ldur            x1, [fp, #-8]
    // 0x7b7e14: StoreField: r1->field_b = r3
    //     0x7b7e14: stur            x3, [x1, #0xb]
    // 0x7b7e18: r0 = Null
    //     0x7b7e18: mov             x0, NULL
    // 0x7b7e1c: LeaveFrame
    //     0x7b7e1c: mov             SP, fp
    //     0x7b7e20: ldp             fp, lr, [SP], #0x10
    // 0x7b7e24: ret
    //     0x7b7e24: ret             
    // 0x7b7e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b7e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b7e2c: b               #0x7b7d94
  }
  _ _ensureCapacity(/* No info */) {
    // ** addr: 0x7b8054, size: 0x22c
    // 0x7b8054: EnterFrame
    //     0x7b8054: stp             fp, lr, [SP, #-0x10]!
    //     0x7b8058: mov             fp, SP
    // 0x7b805c: AllocStack(0x30)
    //     0x7b805c: sub             SP, SP, #0x30
    // 0x7b8060: SetupParameters(PdfStream this /* r1 => r3, fp-0x28 */)
    //     0x7b8060: mov             x3, x1
    //     0x7b8064: stur            x1, [fp, #-0x28]
    // 0x7b8068: CheckStackOverflow
    //     0x7b8068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b806c: cmp             SP, x16
    //     0x7b8070: b.ls            #0x7b8278
    // 0x7b8074: LoadField: r5 = r3->field_7
    //     0x7b8074: ldur            w5, [x3, #7]
    // 0x7b8078: DecompressPointer r5
    //     0x7b8078: add             x5, x5, HEAP, lsl #32
    // 0x7b807c: stur            x5, [fp, #-0x20]
    // 0x7b8080: LoadField: r6 = r5->field_13
    //     0x7b8080: ldur            w6, [x5, #0x13]
    // 0x7b8084: stur            x6, [fp, #-0x18]
    // 0x7b8088: LoadField: r0 = r3->field_b
    //     0x7b8088: ldur            x0, [x3, #0xb]
    // 0x7b808c: r7 = LoadInt32Instr(r6)
    //     0x7b808c: sbfx            x7, x6, #1, #0x1f
    // 0x7b8090: stur            x7, [fp, #-0x10]
    // 0x7b8094: sub             x1, x7, x0
    // 0x7b8098: cmp             x1, x2
    // 0x7b809c: b.lt            #0x7b80b0
    // 0x7b80a0: r0 = Null
    //     0x7b80a0: mov             x0, NULL
    // 0x7b80a4: LeaveFrame
    //     0x7b80a4: mov             SP, fp
    //     0x7b80a8: ldp             fp, lr, [SP], #0x10
    // 0x7b80ac: ret
    //     0x7b80ac: ret             
    // 0x7b80b0: add             x1, x0, x2
    // 0x7b80b4: add             x2, x1, #0x10, lsl #12
    // 0x7b80b8: stur            x2, [fp, #-8]
    // 0x7b80bc: r0 = BoxInt64Instr(r2)
    //     0x7b80bc: sbfiz           x0, x2, #1, #0x1f
    //     0x7b80c0: cmp             x2, x0, asr #1
    //     0x7b80c4: b.eq            #0x7b80d0
    //     0x7b80c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b80cc: stur            x2, [x0, #7]
    // 0x7b80d0: mov             x4, x0
    // 0x7b80d4: r0 = AllocateUint8Array()
    //     0x7b80d4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b80d8: mov             x4, x0
    // 0x7b80dc: ldur            x0, [fp, #-0x10]
    // 0x7b80e0: stur            x4, [fp, #-0x30]
    // 0x7b80e4: tbz             x0, #0x3f, #0x7b80f0
    // 0x7b80e8: ldur            x3, [fp, #-8]
    // 0x7b80ec: b               #0x7b80fc
    // 0x7b80f0: ldur            x3, [fp, #-8]
    // 0x7b80f4: cmp             x0, x3
    // 0x7b80f8: b.le            #0x7b810c
    // 0x7b80fc: ldur            x2, [fp, #-0x18]
    // 0x7b8100: r1 = 0
    //     0x7b8100: movz            x1, #0
    // 0x7b8104: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7b8104: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7b8108: r0 = checkValidRange()
    //     0x7b8108: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x7b810c: ldur            x2, [fp, #-0x10]
    // 0x7b8110: cbnz            x2, #0x7b811c
    // 0x7b8114: ldur            x23, [fp, #-0x30]
    // 0x7b8118: b               #0x7b8244
    // 0x7b811c: ldur            x20, [fp, #-0x18]
    // 0x7b8120: cmp             w20, #0x800
    // 0x7b8124: b.ge            #0x7b81f8
    // 0x7b8128: ldur            x24, [fp, #-0x20]
    // 0x7b812c: ldur            x23, [fp, #-0x30]
    // 0x7b8130: mov             x0, x20
    // 0x7b8134: add             x25, x24, #0x17
    // 0x7b8138: add             x20, x23, #0x17
    // 0x7b813c: cbz             x0, #0x7b81f4
    // 0x7b8140: cmp             x20, x25
    // 0x7b8144: b.ls            #0x7b81ac
    // 0x7b8148: sxtw            x0, w0
    // 0x7b814c: add             x16, x25, x0, asr #1
    // 0x7b8150: cmp             x20, x16
    // 0x7b8154: b.hs            #0x7b81ac
    // 0x7b8158: mov             x25, x16
    // 0x7b815c: add             x20, x20, x0, asr #1
    // 0x7b8160: tbz             w0, #4, #0x7b816c
    // 0x7b8164: ldr             x16, [x25, #-8]!
    // 0x7b8168: str             x16, [x20, #-8]!
    // 0x7b816c: tbz             w0, #3, #0x7b8178
    // 0x7b8170: ldr             w16, [x25, #-4]!
    // 0x7b8174: str             w16, [x20, #-4]!
    // 0x7b8178: tbz             w0, #2, #0x7b8184
    // 0x7b817c: ldrh            w16, [x25, #-2]!
    // 0x7b8180: strh            w16, [x20, #-2]!
    // 0x7b8184: tbz             w0, #1, #0x7b8190
    // 0x7b8188: ldrb            w16, [x25, #-1]!
    // 0x7b818c: strb            w16, [x20, #-1]!
    // 0x7b8190: ands            w0, w0, #0xffffffe1
    // 0x7b8194: b.eq            #0x7b81f4
    // 0x7b8198: ldp             x16, x17, [x25, #-0x10]!
    // 0x7b819c: stp             x16, x17, [x20, #-0x10]!
    // 0x7b81a0: subs            w0, w0, #0x20
    // 0x7b81a4: b.ne            #0x7b8198
    // 0x7b81a8: b               #0x7b81f4
    // 0x7b81ac: tbz             w0, #4, #0x7b81b8
    // 0x7b81b0: ldr             x16, [x25], #8
    // 0x7b81b4: str             x16, [x20], #8
    // 0x7b81b8: tbz             w0, #3, #0x7b81c4
    // 0x7b81bc: ldr             w16, [x25], #4
    // 0x7b81c0: str             w16, [x20], #4
    // 0x7b81c4: tbz             w0, #2, #0x7b81d0
    // 0x7b81c8: ldrh            w16, [x25], #2
    // 0x7b81cc: strh            w16, [x20], #2
    // 0x7b81d0: tbz             w0, #1, #0x7b81dc
    // 0x7b81d4: ldrb            w16, [x25], #1
    // 0x7b81d8: strb            w16, [x20], #1
    // 0x7b81dc: ands            w0, w0, #0xffffffe1
    // 0x7b81e0: b.eq            #0x7b81f4
    // 0x7b81e4: ldp             x16, x17, [x25], #0x10
    // 0x7b81e8: stp             x16, x17, [x20], #0x10
    // 0x7b81ec: subs            w0, w0, #0x20
    // 0x7b81f0: b.ne            #0x7b81e4
    // 0x7b81f4: b               #0x7b8244
    // 0x7b81f8: ldur            x24, [fp, #-0x20]
    // 0x7b81fc: ldur            x23, [fp, #-0x30]
    // 0x7b8200: LoadField: r0 = r23->field_7
    //     0x7b8200: ldur            x0, [x23, #7]
    // 0x7b8204: LoadField: r1 = r24->field_7
    //     0x7b8204: ldur            x1, [x24, #7]
    // 0x7b8208: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x7b8208: mov             x20, THR
    //     0x7b820c: ldr             x9, [x20, #0x658]
    //     0x7b8210: mov             x17, fp
    //     0x7b8214: str             fp, [SP, #-8]!
    //     0x7b8218: mov             fp, SP
    //     0x7b821c: and             SP, SP, #0xfffffffffffffff0
    //     0x7b8220: mov             x19, sp
    //     0x7b8224: mov             sp, SP
    //     0x7b8228: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7b822c: blr             x9
    //     0x7b8230: movz            x16, #0x8
    //     0x7b8234: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7b8238: mov             sp, x19
    //     0x7b823c: mov             SP, fp
    //     0x7b8240: ldr             fp, [SP], #8
    // 0x7b8244: ldur            x1, [fp, #-0x28]
    // 0x7b8248: mov             x0, x23
    // 0x7b824c: StoreField: r1->field_7 = r0
    //     0x7b824c: stur            w0, [x1, #7]
    //     0x7b8250: ldurb           w16, [x1, #-1]
    //     0x7b8254: ldurb           w17, [x0, #-1]
    //     0x7b8258: and             x16, x17, x16, lsr #2
    //     0x7b825c: tst             x16, HEAP, lsr #32
    //     0x7b8260: b.eq            #0x7b8268
    //     0x7b8264: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b8268: r0 = Null
    //     0x7b8268: mov             x0, NULL
    // 0x7b826c: LeaveFrame
    //     0x7b826c: mov             SP, fp
    //     0x7b8270: ldp             fp, lr, [SP], #0x10
    // 0x7b8274: ret
    //     0x7b8274: ret             
    // 0x7b8278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b8278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b827c: b               #0x7b8074
  }
  _ putString(/* No info */) {
    // ** addr: 0x7cb8d4, size: 0x5c
    // 0x7cb8d4: EnterFrame
    //     0x7cb8d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb8d8: mov             fp, SP
    // 0x7cb8dc: AllocStack(0x10)
    //     0x7cb8dc: sub             SP, SP, #0x10
    // 0x7cb8e0: SetupParameters(PdfStream this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7cb8e0: mov             x0, x1
    //     0x7cb8e4: stur            x1, [fp, #-8]
    //     0x7cb8e8: stur            x2, [fp, #-0x10]
    // 0x7cb8ec: CheckStackOverflow
    //     0x7cb8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb8f0: cmp             SP, x16
    //     0x7cb8f4: b.ls            #0x7cb928
    // 0x7cb8f8: r1 = <int>
    //     0x7cb8f8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7cb8fc: r0 = CodeUnits()
    //     0x7cb8fc: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x7cb900: mov             x1, x0
    // 0x7cb904: ldur            x0, [fp, #-0x10]
    // 0x7cb908: StoreField: r1->field_b = r0
    //     0x7cb908: stur            w0, [x1, #0xb]
    // 0x7cb90c: mov             x2, x1
    // 0x7cb910: ldur            x1, [fp, #-8]
    // 0x7cb914: r0 = putBytes()
    //     0x7cb914: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0x7cb918: r0 = Null
    //     0x7cb918: mov             x0, NULL
    // 0x7cb91c: LeaveFrame
    //     0x7cb91c: mov             SP, fp
    //     0x7cb920: ldp             fp, lr, [SP], #0x10
    // 0x7cb924: ret
    //     0x7cb924: ret             
    // 0x7cb928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb928: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb92c: b               #0x7cb8f8
  }
  _ putByte(/* No info */) {
    // ** addr: 0x862100, size: 0x88
    // 0x862100: EnterFrame
    //     0x862100: stp             fp, lr, [SP, #-0x10]!
    //     0x862104: mov             fp, SP
    // 0x862108: AllocStack(0x10)
    //     0x862108: sub             SP, SP, #0x10
    // 0x86210c: SetupParameters(PdfStream this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x86210c: mov             x3, x1
    //     0x862110: mov             x0, x2
    //     0x862114: stur            x1, [fp, #-8]
    //     0x862118: stur            x2, [fp, #-0x10]
    // 0x86211c: CheckStackOverflow
    //     0x86211c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862120: cmp             SP, x16
    //     0x862124: b.ls            #0x86217c
    // 0x862128: mov             x1, x3
    // 0x86212c: r2 = 1
    //     0x86212c: movz            x2, #0x1
    // 0x862130: r0 = _ensureCapacity()
    //     0x862130: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0x862134: ldur            x2, [fp, #-8]
    // 0x862138: LoadField: r3 = r2->field_7
    //     0x862138: ldur            w3, [x2, #7]
    // 0x86213c: DecompressPointer r3
    //     0x86213c: add             x3, x3, HEAP, lsl #32
    // 0x862140: LoadField: r4 = r2->field_b
    //     0x862140: ldur            x4, [x2, #0xb]
    // 0x862144: add             x5, x4, #1
    // 0x862148: StoreField: r2->field_b = r5
    //     0x862148: stur            x5, [x2, #0xb]
    // 0x86214c: LoadField: r2 = r3->field_13
    //     0x86214c: ldur            w2, [x3, #0x13]
    // 0x862150: r0 = LoadInt32Instr(r2)
    //     0x862150: sbfx            x0, x2, #1, #0x1f
    // 0x862154: mov             x1, x4
    // 0x862158: cmp             x1, x0
    // 0x86215c: b.hs            #0x862184
    // 0x862160: ldur            x1, [fp, #-0x10]
    // 0x862164: ArrayStore: r3[r4] = r1  ; TypeUnknown_1
    //     0x862164: add             x2, x3, x4
    //     0x862168: strb            w1, [x2, #0x17]
    // 0x86216c: r0 = Null
    //     0x86216c: mov             x0, NULL
    // 0x862170: LeaveFrame
    //     0x862170: mov             SP, fp
    //     0x862174: ldp             fp, lr, [SP], #0x10
    // 0x862178: ret
    //     0x862178: ret             
    // 0x86217c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86217c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862180: b               #0x862128
    // 0x862184: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x862184: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ putComment(/* No info */) {
    // ** addr: 0xe817bc, size: 0x17c
    // 0xe817bc: EnterFrame
    //     0xe817bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe817c0: mov             fp, SP
    // 0xe817c4: AllocStack(0x40)
    //     0xe817c4: sub             SP, SP, #0x40
    // 0xe817c8: SetupParameters(PdfStream this /* r1 => r0, fp-0x8 */)
    //     0xe817c8: mov             x0, x1
    //     0xe817cc: stur            x1, [fp, #-8]
    // 0xe817d0: CheckStackOverflow
    //     0xe817d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe817d4: cmp             SP, x16
    //     0xe817d8: b.ls            #0xe81928
    // 0xe817dc: r1 = "https://github.com/DavBfr/dart_pdf"
    //     0xe817dc: add             x1, PP, #0x36, lsl #12  ; [pp+0x366c8] "https://github.com/DavBfr/dart_pdf"
    //     0xe817e0: ldr             x1, [x1, #0x6c8]
    // 0xe817e4: r2 = 10
    //     0xe817e4: movz            x2, #0xa
    // 0xe817e8: r0 = _splitWithCharCode()
    //     0xe817e8: bl              #0xe81938  ; [dart:core] _OneByteString::_splitWithCharCode
    // 0xe817ec: stur            x0, [fp, #-0x28]
    // 0xe817f0: LoadField: r1 = r0->field_b
    //     0xe817f0: ldur            w1, [x0, #0xb]
    // 0xe817f4: r3 = LoadInt32Instr(r1)
    //     0xe817f4: sbfx            x3, x1, #1, #0x1f
    // 0xe817f8: stur            x3, [fp, #-0x20]
    // 0xe817fc: ldur            x4, [fp, #-8]
    // 0xe81800: r1 = 0
    //     0xe81800: movz            x1, #0
    // 0xe81804: CheckStackOverflow
    //     0xe81804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81808: cmp             SP, x16
    //     0xe8180c: b.ls            #0xe81930
    // 0xe81810: LoadField: r2 = r0->field_b
    //     0xe81810: ldur            w2, [x0, #0xb]
    // 0xe81814: r5 = LoadInt32Instr(r2)
    //     0xe81814: sbfx            x5, x2, #1, #0x1f
    // 0xe81818: cmp             x3, x5
    // 0xe8181c: b.ne            #0xe8190c
    // 0xe81820: cmp             x1, x5
    // 0xe81824: b.ge            #0xe818fc
    // 0xe81828: LoadField: r2 = r0->field_f
    //     0xe81828: ldur            w2, [x0, #0xf]
    // 0xe8182c: DecompressPointer r2
    //     0xe8182c: add             x2, x2, HEAP, lsl #32
    // 0xe81830: ArrayLoad: r5 = r2[r1]  ; Unknown_4
    //     0xe81830: add             x16, x2, x1, lsl #2
    //     0xe81834: ldur            w5, [x16, #0xf]
    // 0xe81838: DecompressPointer r5
    //     0xe81838: add             x5, x5, HEAP, lsl #32
    // 0xe8183c: stur            x5, [fp, #-0x18]
    // 0xe81840: add             x6, x1, #1
    // 0xe81844: stur            x6, [fp, #-0x10]
    // 0xe81848: LoadField: r1 = r5->field_7
    //     0xe81848: ldur            w1, [x5, #7]
    // 0xe8184c: cbz             w1, #0xe818e4
    // 0xe81850: r1 = Null
    //     0xe81850: mov             x1, NULL
    // 0xe81854: r2 = 6
    //     0xe81854: movz            x2, #0x6
    // 0xe81858: r0 = AllocateArray()
    //     0xe81858: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8185c: r16 = "% "
    //     0xe8185c: add             x16, PP, #0x36, lsl #12  ; [pp+0x366d0] "% "
    //     0xe81860: ldr             x16, [x16, #0x6d0]
    // 0xe81864: StoreField: r0->field_f = r16
    //     0xe81864: stur            w16, [x0, #0xf]
    // 0xe81868: ldur            x1, [fp, #-0x18]
    // 0xe8186c: StoreField: r0->field_13 = r1
    //     0xe8186c: stur            w1, [x0, #0x13]
    // 0xe81870: r16 = "\n"
    //     0xe81870: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xe81874: ArrayStore: r0[0] = r16  ; List_4
    //     0xe81874: stur            w16, [x0, #0x17]
    // 0xe81878: str             x0, [SP]
    // 0xe8187c: r0 = _interpolate()
    //     0xe8187c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe81880: r1 = <int>
    //     0xe81880: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe81884: stur            x0, [fp, #-0x18]
    // 0xe81888: r0 = CodeUnits()
    //     0xe81888: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0xe8188c: mov             x3, x0
    // 0xe81890: ldur            x0, [fp, #-0x18]
    // 0xe81894: stur            x3, [fp, #-0x38]
    // 0xe81898: StoreField: r3->field_b = r0
    //     0xe81898: stur            w0, [x3, #0xb]
    // 0xe8189c: LoadField: r1 = r0->field_7
    //     0xe8189c: ldur            w1, [x0, #7]
    // 0xe818a0: r0 = LoadInt32Instr(r1)
    //     0xe818a0: sbfx            x0, x1, #1, #0x1f
    // 0xe818a4: ldur            x1, [fp, #-8]
    // 0xe818a8: mov             x2, x0
    // 0xe818ac: stur            x0, [fp, #-0x30]
    // 0xe818b0: r0 = _ensureCapacity()
    //     0xe818b0: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe818b4: ldur            x0, [fp, #-8]
    // 0xe818b8: LoadField: r1 = r0->field_7
    //     0xe818b8: ldur            w1, [x0, #7]
    // 0xe818bc: DecompressPointer r1
    //     0xe818bc: add             x1, x1, HEAP, lsl #32
    // 0xe818c0: LoadField: r2 = r0->field_b
    //     0xe818c0: ldur            x2, [x0, #0xb]
    // 0xe818c4: ldur            x3, [fp, #-0x38]
    // 0xe818c8: r0 = setAll()
    //     0xe818c8: bl              #0x7b7e30  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::setAll
    // 0xe818cc: ldur            x0, [fp, #-8]
    // 0xe818d0: LoadField: r1 = r0->field_b
    //     0xe818d0: ldur            x1, [x0, #0xb]
    // 0xe818d4: ldur            x2, [fp, #-0x30]
    // 0xe818d8: add             x3, x1, x2
    // 0xe818dc: StoreField: r0->field_b = r3
    //     0xe818dc: stur            x3, [x0, #0xb]
    // 0xe818e0: b               #0xe818e8
    // 0xe818e4: mov             x0, x4
    // 0xe818e8: ldur            x1, [fp, #-0x10]
    // 0xe818ec: mov             x4, x0
    // 0xe818f0: ldur            x0, [fp, #-0x28]
    // 0xe818f4: ldur            x3, [fp, #-0x20]
    // 0xe818f8: b               #0xe81804
    // 0xe818fc: r0 = Null
    //     0xe818fc: mov             x0, NULL
    // 0xe81900: LeaveFrame
    //     0xe81900: mov             SP, fp
    //     0xe81904: ldp             fp, lr, [SP], #0x10
    // 0xe81908: ret
    //     0xe81908: ret             
    // 0xe8190c: r0 = ConcurrentModificationError()
    //     0xe8190c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe81910: mov             x1, x0
    // 0xe81914: ldur            x0, [fp, #-0x28]
    // 0xe81918: StoreField: r1->field_b = r0
    //     0xe81918: stur            w0, [x1, #0xb]
    // 0xe8191c: mov             x0, x1
    // 0xe81920: r0 = Throw()
    //     0xe81920: bl              #0xec04b8  ; ThrowStub
    // 0xe81924: brk             #0
    // 0xe81928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81928: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8192c: b               #0xe817dc
    // 0xe81930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81934: b               #0xe81810
  }
}
