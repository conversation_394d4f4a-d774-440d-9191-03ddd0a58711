// lib: , url: package:pdf/src/pdf/format/string.dart

// class id: 1050791, size: 0x8
class :: {
}

// class id: 906, size: 0x14, field offset: 0x8
//   const constructor, 
class PdfString extends PdfDataType {

  factory _ PdfString.fromString(/* No info */) {
    // ** addr: 0x7b8bf8, size: 0x5c
    // 0x7b8bf8: EnterFrame
    //     0x7b8bf8: stp             fp, lr, [SP, #-0x10]!
    //     0x7b8bfc: mov             fp, SP
    // 0x7b8c00: AllocStack(0x8)
    //     0x7b8c00: sub             SP, SP, #8
    // 0x7b8c04: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x7b8c04: mov             x0, x1
    //     0x7b8c08: mov             x1, x2
    // 0x7b8c0c: CheckStackOverflow
    //     0x7b8c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b8c10: cmp             SP, x16
    //     0x7b8c14: b.ls            #0x7b8c4c
    // 0x7b8c18: r0 = _string()
    //     0x7b8c18: bl              #0x7b8c80  ; [package:pdf/src/pdf/format/string.dart] PdfString::_string
    // 0x7b8c1c: stur            x0, [fp, #-8]
    // 0x7b8c20: r0 = PdfString()
    //     0x7b8c20: bl              #0x7b8c74  ; AllocatePdfStringStub -> PdfString (size=0x14)
    // 0x7b8c24: ldur            x1, [fp, #-8]
    // 0x7b8c28: StoreField: r0->field_7 = r1
    //     0x7b8c28: stur            w1, [x0, #7]
    // 0x7b8c2c: r1 = Instance_PdfStringFormat
    //     0x7b8c2c: add             x1, PP, #0x46, lsl #12  ; [pp+0x46d70] Obj!PdfStringFormat@e2f041
    //     0x7b8c30: ldr             x1, [x1, #0xd70]
    // 0x7b8c34: StoreField: r0->field_b = r1
    //     0x7b8c34: stur            w1, [x0, #0xb]
    // 0x7b8c38: r1 = true
    //     0x7b8c38: add             x1, NULL, #0x20  ; true
    // 0x7b8c3c: StoreField: r0->field_f = r1
    //     0x7b8c3c: stur            w1, [x0, #0xf]
    // 0x7b8c40: LeaveFrame
    //     0x7b8c40: mov             SP, fp
    //     0x7b8c44: ldp             fp, lr, [SP], #0x10
    // 0x7b8c48: ret
    //     0x7b8c48: ret             
    // 0x7b8c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b8c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b8c50: b               #0x7b8c18
  }
  static _ _string(/* No info */) {
    // ** addr: 0x7b8c80, size: 0xfc
    // 0x7b8c80: EnterFrame
    //     0x7b8c80: stp             fp, lr, [SP, #-0x10]!
    //     0x7b8c84: mov             fp, SP
    // 0x7b8c88: AllocStack(0x48)
    //     0x7b8c88: sub             SP, SP, #0x48
    // 0x7b8c8c: SetupParameters(dynamic _ /* r1 => r0, fp-0x38 */)
    //     0x7b8c8c: mov             x0, x1
    //     0x7b8c90: stur            x1, [fp, #-0x38]
    // 0x7b8c94: CheckStackOverflow
    //     0x7b8c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b8c98: cmp             SP, x16
    //     0x7b8c9c: b.ls            #0x7b8d74
    // 0x7b8ca0: mov             x2, x0
    // 0x7b8ca4: r1 = Instance_Latin1Codec
    //     0x7b8ca4: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0x7b8ca8: r0 = encode()
    //     0x7b8ca8: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0x7b8cac: LeaveFrame
    //     0x7b8cac: mov             SP, fp
    //     0x7b8cb0: ldp             fp, lr, [SP], #0x10
    // 0x7b8cb4: ret
    //     0x7b8cb4: ret             
    // 0x7b8cb8: r3 = 4
    //     0x7b8cb8: movz            x3, #0x4
    // 0x7b8cbc: sub             SP, fp, #0x48
    // 0x7b8cc0: mov             x2, x3
    // 0x7b8cc4: r1 = Null
    //     0x7b8cc4: mov             x1, NULL
    // 0x7b8cc8: r0 = AllocateArray()
    //     0x7b8cc8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b8ccc: stur            x0, [fp, #-0x38]
    // 0x7b8cd0: r16 = 508
    //     0x7b8cd0: movz            x16, #0x1fc
    // 0x7b8cd4: StoreField: r0->field_f = r16
    //     0x7b8cd4: stur            w16, [x0, #0xf]
    // 0x7b8cd8: r16 = 510
    //     0x7b8cd8: movz            x16, #0x1fe
    // 0x7b8cdc: StoreField: r0->field_13 = r16
    //     0x7b8cdc: stur            w16, [x0, #0x13]
    // 0x7b8ce0: r1 = <int>
    //     0x7b8ce0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7b8ce4: r0 = AllocateGrowableArray()
    //     0x7b8ce4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7b8ce8: mov             x2, x0
    // 0x7b8cec: ldur            x0, [fp, #-0x38]
    // 0x7b8cf0: stur            x2, [fp, #-0x40]
    // 0x7b8cf4: StoreField: r2->field_f = r0
    //     0x7b8cf4: stur            w0, [x2, #0xf]
    // 0x7b8cf8: r0 = 4
    //     0x7b8cf8: movz            x0, #0x4
    // 0x7b8cfc: StoreField: r2->field_b = r0
    //     0x7b8cfc: stur            w0, [x2, #0xb]
    // 0x7b8d00: ldur            x1, [fp, #-0x30]
    // 0x7b8d04: r0 = _encodeUtf16be()
    //     0x7b8d04: bl              #0x7b8d7c  ; [package:pdf/src/pdf/format/string.dart] PdfString::_encodeUtf16be
    // 0x7b8d08: ldur            x1, [fp, #-0x40]
    // 0x7b8d0c: mov             x2, x0
    // 0x7b8d10: r0 = +()
    //     0x7b8d10: bl              #0x640a74  ; [dart:collection] ListBase::+
    // 0x7b8d14: stur            x0, [fp, #-0x40]
    // 0x7b8d18: LoadField: r4 = r0->field_b
    //     0x7b8d18: ldur            w4, [x0, #0xb]
    // 0x7b8d1c: stur            x4, [fp, #-0x38]
    // 0x7b8d20: r5 = LoadInt32Instr(r4)
    //     0x7b8d20: sbfx            x5, x4, #1, #0x1f
    // 0x7b8d24: stur            x5, [fp, #-0x48]
    // 0x7b8d28: tbz             x5, #0x3f, #0x7b8d40
    // 0x7b8d2c: mov             x2, x4
    // 0x7b8d30: mov             x3, x5
    // 0x7b8d34: r1 = 0
    //     0x7b8d34: movz            x1, #0
    // 0x7b8d38: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7b8d38: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7b8d3c: r0 = checkValidRange()
    //     0x7b8d3c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x7b8d40: ldur            x4, [fp, #-0x38]
    // 0x7b8d44: r0 = AllocateUint8Array()
    //     0x7b8d44: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x7b8d48: mov             x1, x0
    // 0x7b8d4c: ldur            x3, [fp, #-0x48]
    // 0x7b8d50: ldur            x5, [fp, #-0x40]
    // 0x7b8d54: r2 = 0
    //     0x7b8d54: movz            x2, #0
    // 0x7b8d58: r6 = 0
    //     0x7b8d58: movz            x6, #0
    // 0x7b8d5c: stur            x0, [fp, #-0x38]
    // 0x7b8d60: r0 = _slowSetRange()
    //     0x7b8d60: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x7b8d64: ldur            x0, [fp, #-0x38]
    // 0x7b8d68: LeaveFrame
    //     0x7b8d68: mov             SP, fp
    //     0x7b8d6c: ldp             fp, lr, [SP], #0x10
    // 0x7b8d70: ret
    //     0x7b8d70: ret             
    // 0x7b8d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b8d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b8d78: b               #0x7b8ca0
  }
  static _ _encodeUtf16be(/* No info */) {
    // ** addr: 0x7b8d7c, size: 0x444
    // 0x7b8d7c: EnterFrame
    //     0x7b8d7c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b8d80: mov             fp, SP
    // 0x7b8d84: AllocStack(0x48)
    //     0x7b8d84: sub             SP, SP, #0x48
    // 0x7b8d88: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7b8d88: mov             x0, x1
    //     0x7b8d8c: stur            x1, [fp, #-8]
    // 0x7b8d90: CheckStackOverflow
    //     0x7b8d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b8d94: cmp             SP, x16
    //     0x7b8d98: b.ls            #0x7b91b0
    // 0x7b8d9c: r1 = <int>
    //     0x7b8d9c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7b8da0: r2 = 0
    //     0x7b8da0: movz            x2, #0
    // 0x7b8da4: r0 = _GrowableList()
    //     0x7b8da4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b8da8: mov             x2, x0
    // 0x7b8dac: ldur            x0, [fp, #-8]
    // 0x7b8db0: stur            x2, [fp, #-0x38]
    // 0x7b8db4: LoadField: r1 = r0->field_7
    //     0x7b8db4: ldur            w1, [x0, #7]
    // 0x7b8db8: r3 = LoadInt32Instr(r1)
    //     0x7b8db8: sbfx            x3, x1, #1, #0x1f
    // 0x7b8dbc: stur            x3, [fp, #-0x30]
    // 0x7b8dc0: r1 = 0
    //     0x7b8dc0: movz            x1, #0
    // 0x7b8dc4: r4 = 65280
    //     0x7b8dc4: orr             x4, xzr, #0xff00
    // 0x7b8dc8: CheckStackOverflow
    //     0x7b8dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b8dcc: cmp             SP, x16
    //     0x7b8dd0: b.ls            #0x7b91b8
    // 0x7b8dd4: cmp             x1, x3
    // 0x7b8dd8: b.ge            #0x7b91a0
    // 0x7b8ddc: ArrayLoad: r5 = r0[r1]  ; TypedUnsigned_1
    //     0x7b8ddc: add             x16, x0, x1
    //     0x7b8de0: ldrb            w5, [x16, #0xf]
    // 0x7b8de4: stur            x5, [fp, #-0x28]
    // 0x7b8de8: add             x6, x1, #1
    // 0x7b8dec: stur            x6, [fp, #-0x20]
    // 0x7b8df0: tbnz            x5, #0x3f, #0x7b8e00
    // 0x7b8df4: r17 = 55296
    //     0x7b8df4: movz            x17, #0xd800
    // 0x7b8df8: cmp             x5, x17
    // 0x7b8dfc: b.lt            #0x7b8e18
    // 0x7b8e00: r17 = 57343
    //     0x7b8e00: movz            x17, #0xdfff
    // 0x7b8e04: cmp             x5, x17
    // 0x7b8e08: b.le            #0x7b8ef0
    // 0x7b8e0c: r17 = 65535
    //     0x7b8e0c: orr             x17, xzr, #0xffff
    // 0x7b8e10: cmp             x5, x17
    // 0x7b8e14: b.gt            #0x7b8ee8
    // 0x7b8e18: mov             x1, x5
    // 0x7b8e1c: ubfx            x1, x1, #0, #0x20
    // 0x7b8e20: and             x7, x1, x4
    // 0x7b8e24: ubfx            x7, x7, #0, #0x20
    // 0x7b8e28: asr             x8, x7, #8
    // 0x7b8e2c: stur            x8, [fp, #-0x18]
    // 0x7b8e30: LoadField: r1 = r2->field_b
    //     0x7b8e30: ldur            w1, [x2, #0xb]
    // 0x7b8e34: LoadField: r7 = r2->field_f
    //     0x7b8e34: ldur            w7, [x2, #0xf]
    // 0x7b8e38: DecompressPointer r7
    //     0x7b8e38: add             x7, x7, HEAP, lsl #32
    // 0x7b8e3c: LoadField: r9 = r7->field_b
    //     0x7b8e3c: ldur            w9, [x7, #0xb]
    // 0x7b8e40: r7 = LoadInt32Instr(r1)
    //     0x7b8e40: sbfx            x7, x1, #1, #0x1f
    // 0x7b8e44: stur            x7, [fp, #-0x10]
    // 0x7b8e48: r1 = LoadInt32Instr(r9)
    //     0x7b8e48: sbfx            x1, x9, #1, #0x1f
    // 0x7b8e4c: cmp             x7, x1
    // 0x7b8e50: b.ne            #0x7b8e5c
    // 0x7b8e54: mov             x1, x2
    // 0x7b8e58: r0 = _growToNextCapacity()
    //     0x7b8e58: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b8e5c: ldur            x0, [fp, #-0x38]
    // 0x7b8e60: ldur            x1, [fp, #-0x18]
    // 0x7b8e64: ldur            x3, [fp, #-0x10]
    // 0x7b8e68: r2 = 255
    //     0x7b8e68: movz            x2, #0xff
    // 0x7b8e6c: add             x4, x3, #1
    // 0x7b8e70: stur            x4, [fp, #-0x40]
    // 0x7b8e74: lsl             x5, x4, #1
    // 0x7b8e78: StoreField: r0->field_b = r5
    //     0x7b8e78: stur            w5, [x0, #0xb]
    // 0x7b8e7c: LoadField: r5 = r0->field_f
    //     0x7b8e7c: ldur            w5, [x0, #0xf]
    // 0x7b8e80: DecompressPointer r5
    //     0x7b8e80: add             x5, x5, HEAP, lsl #32
    // 0x7b8e84: lsl             x6, x1, #1
    // 0x7b8e88: ArrayStore: r5[r3] = r6  ; Unknown_4
    //     0x7b8e88: add             x1, x5, x3, lsl #2
    //     0x7b8e8c: stur            w6, [x1, #0xf]
    // 0x7b8e90: ldur            x1, [fp, #-0x28]
    // 0x7b8e94: ubfx            x1, x1, #0, #0x20
    // 0x7b8e98: and             x3, x1, x2
    // 0x7b8e9c: stur            x3, [fp, #-0x10]
    // 0x7b8ea0: LoadField: r1 = r5->field_b
    //     0x7b8ea0: ldur            w1, [x5, #0xb]
    // 0x7b8ea4: r5 = LoadInt32Instr(r1)
    //     0x7b8ea4: sbfx            x5, x1, #1, #0x1f
    // 0x7b8ea8: cmp             x4, x5
    // 0x7b8eac: b.ne            #0x7b8eb8
    // 0x7b8eb0: mov             x1, x0
    // 0x7b8eb4: r0 = _growToNextCapacity()
    //     0x7b8eb4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b8eb8: ldur            x0, [fp, #-0x38]
    // 0x7b8ebc: ldur            x2, [fp, #-0x10]
    // 0x7b8ec0: ldur            x1, [fp, #-0x40]
    // 0x7b8ec4: add             x3, x1, #1
    // 0x7b8ec8: lsl             x4, x3, #1
    // 0x7b8ecc: StoreField: r0->field_b = r4
    //     0x7b8ecc: stur            w4, [x0, #0xb]
    // 0x7b8ed0: LoadField: r3 = r0->field_f
    //     0x7b8ed0: ldur            w3, [x0, #0xf]
    // 0x7b8ed4: DecompressPointer r3
    //     0x7b8ed4: add             x3, x3, HEAP, lsl #32
    // 0x7b8ed8: lsl             w4, w2, #1
    // 0x7b8edc: ArrayStore: r3[r1] = r4  ; Unknown_4
    //     0x7b8edc: add             x2, x3, x1, lsl #2
    //     0x7b8ee0: stur            w4, [x2, #0xf]
    // 0x7b8ee4: b               #0x7b918c
    // 0x7b8ee8: mov             x0, x2
    // 0x7b8eec: b               #0x7b8ef4
    // 0x7b8ef0: mov             x0, x2
    // 0x7b8ef4: ldur            x1, [fp, #-0x28]
    // 0x7b8ef8: r17 = 65535
    //     0x7b8ef8: orr             x17, xzr, #0xffff
    // 0x7b8efc: cmp             x1, x17
    // 0x7b8f00: b.le            #0x7b90f4
    // 0x7b8f04: r17 = 1114111
    //     0x7b8f04: movz            x17, #0xffff
    //     0x7b8f08: movk            x17, #0x10, lsl #16
    // 0x7b8f0c: cmp             x1, x17
    // 0x7b8f10: b.gt            #0x7b90f4
    // 0x7b8f14: r3 = 65280
    //     0x7b8f14: orr             x3, xzr, #0xff00
    // 0x7b8f18: r2 = 1047552
    //     0x7b8f18: orr             x2, xzr, #0xffc00
    // 0x7b8f1c: sub             x4, x1, #0x10, lsl #12
    // 0x7b8f20: stur            x4, [fp, #-0x40]
    // 0x7b8f24: mov             x1, x4
    // 0x7b8f28: ubfx            x1, x1, #0, #0x20
    // 0x7b8f2c: and             x5, x1, x2
    // 0x7b8f30: ubfx            x5, x5, #0, #0x20
    // 0x7b8f34: asr             x1, x5, #0xa
    // 0x7b8f38: r17 = 55296
    //     0x7b8f38: movz            x17, #0xd800
    // 0x7b8f3c: add             x5, x1, x17
    // 0x7b8f40: stur            x5, [fp, #-0x28]
    // 0x7b8f44: mov             x1, x5
    // 0x7b8f48: ubfx            x1, x1, #0, #0x20
    // 0x7b8f4c: and             x6, x1, x3
    // 0x7b8f50: ubfx            x6, x6, #0, #0x20
    // 0x7b8f54: asr             x7, x6, #8
    // 0x7b8f58: stur            x7, [fp, #-0x18]
    // 0x7b8f5c: LoadField: r1 = r0->field_b
    //     0x7b8f5c: ldur            w1, [x0, #0xb]
    // 0x7b8f60: LoadField: r6 = r0->field_f
    //     0x7b8f60: ldur            w6, [x0, #0xf]
    // 0x7b8f64: DecompressPointer r6
    //     0x7b8f64: add             x6, x6, HEAP, lsl #32
    // 0x7b8f68: LoadField: r8 = r6->field_b
    //     0x7b8f68: ldur            w8, [x6, #0xb]
    // 0x7b8f6c: r6 = LoadInt32Instr(r1)
    //     0x7b8f6c: sbfx            x6, x1, #1, #0x1f
    // 0x7b8f70: stur            x6, [fp, #-0x10]
    // 0x7b8f74: r1 = LoadInt32Instr(r8)
    //     0x7b8f74: sbfx            x1, x8, #1, #0x1f
    // 0x7b8f78: cmp             x6, x1
    // 0x7b8f7c: b.ne            #0x7b8f88
    // 0x7b8f80: mov             x1, x0
    // 0x7b8f84: r0 = _growToNextCapacity()
    //     0x7b8f84: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b8f88: ldur            x0, [fp, #-0x38]
    // 0x7b8f8c: ldur            x1, [fp, #-0x18]
    // 0x7b8f90: ldur            x2, [fp, #-0x10]
    // 0x7b8f94: r3 = 255
    //     0x7b8f94: movz            x3, #0xff
    // 0x7b8f98: add             x4, x2, #1
    // 0x7b8f9c: stur            x4, [fp, #-0x48]
    // 0x7b8fa0: lsl             x5, x4, #1
    // 0x7b8fa4: StoreField: r0->field_b = r5
    //     0x7b8fa4: stur            w5, [x0, #0xb]
    // 0x7b8fa8: LoadField: r5 = r0->field_f
    //     0x7b8fa8: ldur            w5, [x0, #0xf]
    // 0x7b8fac: DecompressPointer r5
    //     0x7b8fac: add             x5, x5, HEAP, lsl #32
    // 0x7b8fb0: lsl             x6, x1, #1
    // 0x7b8fb4: ArrayStore: r5[r2] = r6  ; Unknown_4
    //     0x7b8fb4: add             x1, x5, x2, lsl #2
    //     0x7b8fb8: stur            w6, [x1, #0xf]
    // 0x7b8fbc: ldur            x1, [fp, #-0x28]
    // 0x7b8fc0: ubfx            x1, x1, #0, #0x20
    // 0x7b8fc4: and             x2, x1, x3
    // 0x7b8fc8: stur            x2, [fp, #-0x10]
    // 0x7b8fcc: LoadField: r1 = r5->field_b
    //     0x7b8fcc: ldur            w1, [x5, #0xb]
    // 0x7b8fd0: r5 = LoadInt32Instr(r1)
    //     0x7b8fd0: sbfx            x5, x1, #1, #0x1f
    // 0x7b8fd4: cmp             x4, x5
    // 0x7b8fd8: b.ne            #0x7b8fe4
    // 0x7b8fdc: mov             x1, x0
    // 0x7b8fe0: r0 = _growToNextCapacity()
    //     0x7b8fe0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b8fe4: ldur            x0, [fp, #-0x38]
    // 0x7b8fe8: ldur            x2, [fp, #-0x10]
    // 0x7b8fec: ldur            x1, [fp, #-0x48]
    // 0x7b8ff0: r3 = 65280
    //     0x7b8ff0: orr             x3, xzr, #0xff00
    // 0x7b8ff4: r4 = 1023
    //     0x7b8ff4: movz            x4, #0x3ff
    // 0x7b8ff8: add             x5, x1, #1
    // 0x7b8ffc: stur            x5, [fp, #-0x28]
    // 0x7b9000: lsl             x6, x5, #1
    // 0x7b9004: StoreField: r0->field_b = r6
    //     0x7b9004: stur            w6, [x0, #0xb]
    // 0x7b9008: LoadField: r6 = r0->field_f
    //     0x7b9008: ldur            w6, [x0, #0xf]
    // 0x7b900c: DecompressPointer r6
    //     0x7b900c: add             x6, x6, HEAP, lsl #32
    // 0x7b9010: lsl             w7, w2, #1
    // 0x7b9014: ArrayStore: r6[r1] = r7  ; Unknown_4
    //     0x7b9014: add             x2, x6, x1, lsl #2
    //     0x7b9018: stur            w7, [x2, #0xf]
    // 0x7b901c: ldur            x1, [fp, #-0x40]
    // 0x7b9020: ubfx            x1, x1, #0, #0x20
    // 0x7b9024: and             x2, x1, x4
    // 0x7b9028: ubfx            x2, x2, #0, #0x20
    // 0x7b902c: r17 = 56320
    //     0x7b902c: movz            x17, #0xdc00
    // 0x7b9030: add             x7, x2, x17
    // 0x7b9034: stur            x7, [fp, #-0x18]
    // 0x7b9038: mov             x1, x7
    // 0x7b903c: ubfx            x1, x1, #0, #0x20
    // 0x7b9040: and             x2, x1, x3
    // 0x7b9044: ubfx            x2, x2, #0, #0x20
    // 0x7b9048: asr             x8, x2, #8
    // 0x7b904c: stur            x8, [fp, #-0x10]
    // 0x7b9050: LoadField: r1 = r6->field_b
    //     0x7b9050: ldur            w1, [x6, #0xb]
    // 0x7b9054: r2 = LoadInt32Instr(r1)
    //     0x7b9054: sbfx            x2, x1, #1, #0x1f
    // 0x7b9058: cmp             x5, x2
    // 0x7b905c: b.ne            #0x7b9068
    // 0x7b9060: mov             x1, x0
    // 0x7b9064: r0 = _growToNextCapacity()
    //     0x7b9064: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b9068: ldur            x0, [fp, #-0x38]
    // 0x7b906c: ldur            x2, [fp, #-0x10]
    // 0x7b9070: ldur            x1, [fp, #-0x28]
    // 0x7b9074: r3 = 255
    //     0x7b9074: movz            x3, #0xff
    // 0x7b9078: add             x4, x1, #1
    // 0x7b907c: stur            x4, [fp, #-0x40]
    // 0x7b9080: lsl             x5, x4, #1
    // 0x7b9084: StoreField: r0->field_b = r5
    //     0x7b9084: stur            w5, [x0, #0xb]
    // 0x7b9088: LoadField: r5 = r0->field_f
    //     0x7b9088: ldur            w5, [x0, #0xf]
    // 0x7b908c: DecompressPointer r5
    //     0x7b908c: add             x5, x5, HEAP, lsl #32
    // 0x7b9090: lsl             x6, x2, #1
    // 0x7b9094: ArrayStore: r5[r1] = r6  ; Unknown_4
    //     0x7b9094: add             x2, x5, x1, lsl #2
    //     0x7b9098: stur            w6, [x2, #0xf]
    // 0x7b909c: ldur            x1, [fp, #-0x18]
    // 0x7b90a0: ubfx            x1, x1, #0, #0x20
    // 0x7b90a4: and             x2, x1, x3
    // 0x7b90a8: stur            x2, [fp, #-0x10]
    // 0x7b90ac: LoadField: r1 = r5->field_b
    //     0x7b90ac: ldur            w1, [x5, #0xb]
    // 0x7b90b0: r5 = LoadInt32Instr(r1)
    //     0x7b90b0: sbfx            x5, x1, #1, #0x1f
    // 0x7b90b4: cmp             x4, x5
    // 0x7b90b8: b.ne            #0x7b90c4
    // 0x7b90bc: mov             x1, x0
    // 0x7b90c0: r0 = _growToNextCapacity()
    //     0x7b90c0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b90c4: ldur            x0, [fp, #-0x38]
    // 0x7b90c8: ldur            x2, [fp, #-0x10]
    // 0x7b90cc: ldur            x1, [fp, #-0x40]
    // 0x7b90d0: add             x3, x1, #1
    // 0x7b90d4: lsl             x4, x3, #1
    // 0x7b90d8: StoreField: r0->field_b = r4
    //     0x7b90d8: stur            w4, [x0, #0xb]
    // 0x7b90dc: LoadField: r3 = r0->field_f
    //     0x7b90dc: ldur            w3, [x0, #0xf]
    // 0x7b90e0: DecompressPointer r3
    //     0x7b90e0: add             x3, x3, HEAP, lsl #32
    // 0x7b90e4: lsl             w4, w2, #1
    // 0x7b90e8: ArrayStore: r3[r1] = r4  ; Unknown_4
    //     0x7b90e8: add             x2, x3, x1, lsl #2
    //     0x7b90ec: stur            w4, [x2, #0xf]
    // 0x7b90f0: b               #0x7b918c
    // 0x7b90f4: LoadField: r1 = r0->field_b
    //     0x7b90f4: ldur            w1, [x0, #0xb]
    // 0x7b90f8: LoadField: r2 = r0->field_f
    //     0x7b90f8: ldur            w2, [x0, #0xf]
    // 0x7b90fc: DecompressPointer r2
    //     0x7b90fc: add             x2, x2, HEAP, lsl #32
    // 0x7b9100: LoadField: r3 = r2->field_b
    //     0x7b9100: ldur            w3, [x2, #0xb]
    // 0x7b9104: r2 = LoadInt32Instr(r1)
    //     0x7b9104: sbfx            x2, x1, #1, #0x1f
    // 0x7b9108: stur            x2, [fp, #-0x10]
    // 0x7b910c: r1 = LoadInt32Instr(r3)
    //     0x7b910c: sbfx            x1, x3, #1, #0x1f
    // 0x7b9110: cmp             x2, x1
    // 0x7b9114: b.ne            #0x7b9120
    // 0x7b9118: mov             x1, x0
    // 0x7b911c: r0 = _growToNextCapacity()
    //     0x7b911c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b9120: ldur            x0, [fp, #-0x38]
    // 0x7b9124: ldur            x1, [fp, #-0x10]
    // 0x7b9128: add             x2, x1, #1
    // 0x7b912c: stur            x2, [fp, #-0x18]
    // 0x7b9130: lsl             x3, x2, #1
    // 0x7b9134: StoreField: r0->field_b = r3
    //     0x7b9134: stur            w3, [x0, #0xb]
    // 0x7b9138: LoadField: r3 = r0->field_f
    //     0x7b9138: ldur            w3, [x0, #0xf]
    // 0x7b913c: DecompressPointer r3
    //     0x7b913c: add             x3, x3, HEAP, lsl #32
    // 0x7b9140: add             x4, x3, x1, lsl #2
    // 0x7b9144: r16 = 510
    //     0x7b9144: movz            x16, #0x1fe
    // 0x7b9148: StoreField: r4->field_f = r16
    //     0x7b9148: stur            w16, [x4, #0xf]
    // 0x7b914c: LoadField: r1 = r3->field_b
    //     0x7b914c: ldur            w1, [x3, #0xb]
    // 0x7b9150: r3 = LoadInt32Instr(r1)
    //     0x7b9150: sbfx            x3, x1, #1, #0x1f
    // 0x7b9154: cmp             x2, x3
    // 0x7b9158: b.ne            #0x7b9164
    // 0x7b915c: mov             x1, x0
    // 0x7b9160: r0 = _growToNextCapacity()
    //     0x7b9160: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b9164: ldur            x0, [fp, #-0x38]
    // 0x7b9168: ldur            x1, [fp, #-0x18]
    // 0x7b916c: add             x2, x1, #1
    // 0x7b9170: lsl             x3, x2, #1
    // 0x7b9174: StoreField: r0->field_b = r3
    //     0x7b9174: stur            w3, [x0, #0xb]
    // 0x7b9178: LoadField: r2 = r0->field_f
    //     0x7b9178: ldur            w2, [x0, #0xf]
    // 0x7b917c: DecompressPointer r2
    //     0x7b917c: add             x2, x2, HEAP, lsl #32
    // 0x7b9180: add             x3, x2, x1, lsl #2
    // 0x7b9184: r16 = 506
    //     0x7b9184: movz            x16, #0x1fa
    // 0x7b9188: StoreField: r3->field_f = r16
    //     0x7b9188: stur            w16, [x3, #0xf]
    // 0x7b918c: ldur            x1, [fp, #-0x20]
    // 0x7b9190: mov             x2, x0
    // 0x7b9194: ldur            x0, [fp, #-8]
    // 0x7b9198: ldur            x3, [fp, #-0x30]
    // 0x7b919c: b               #0x7b8dc4
    // 0x7b91a0: mov             x0, x2
    // 0x7b91a4: LeaveFrame
    //     0x7b91a4: mov             SP, fp
    //     0x7b91a8: ldp             fp, lr, [SP], #0x10
    // 0x7b91ac: ret
    //     0x7b91ac: ret             
    // 0x7b91b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b91b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b91b4: b               #0x7b8d9c
    // 0x7b91b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b91b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b91bc: b               #0x7b8dd4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c8cc, size: 0x5c
    // 0xd7c8cc: ldr             x1, [SP]
    // 0xd7c8d0: cmp             w1, NULL
    // 0xd7c8d4: b.ne            #0xd7c8e0
    // 0xd7c8d8: r0 = false
    //     0xd7c8d8: add             x0, NULL, #0x30  ; false
    // 0xd7c8dc: ret
    //     0xd7c8dc: ret             
    // 0xd7c8e0: r2 = 60
    //     0xd7c8e0: movz            x2, #0x3c
    // 0xd7c8e4: branchIfSmi(r1, 0xd7c8f0)
    //     0xd7c8e4: tbz             w1, #0, #0xd7c8f0
    // 0xd7c8e8: r2 = LoadClassIdInstr(r1)
    //     0xd7c8e8: ldur            x2, [x1, #-1]
    //     0xd7c8ec: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c8f0: cmp             x2, #0x38a
    // 0xd7c8f4: b.ne            #0xd7c920
    // 0xd7c8f8: ldr             x2, [SP, #8]
    // 0xd7c8fc: LoadField: r3 = r2->field_7
    //     0xd7c8fc: ldur            w3, [x2, #7]
    // 0xd7c900: DecompressPointer r3
    //     0xd7c900: add             x3, x3, HEAP, lsl #32
    // 0xd7c904: LoadField: r2 = r1->field_7
    //     0xd7c904: ldur            w2, [x1, #7]
    // 0xd7c908: DecompressPointer r2
    //     0xd7c908: add             x2, x2, HEAP, lsl #32
    // 0xd7c90c: cmp             w3, w2
    // 0xd7c910: r16 = true
    //     0xd7c910: add             x16, NULL, #0x20  ; true
    // 0xd7c914: r17 = false
    //     0xd7c914: add             x17, NULL, #0x30  ; false
    // 0xd7c918: csel            x0, x16, x17, eq
    // 0xd7c91c: ret
    //     0xd7c91c: ret             
    // 0xd7c920: r0 = false
    //     0xd7c920: add             x0, NULL, #0x30  ; false
    // 0xd7c924: ret
    //     0xd7c924: ret             
  }
  _ output(/* No info */) {
    // ** addr: 0xe7f538, size: 0xb0
    // 0xe7f538: EnterFrame
    //     0xe7f538: stp             fp, lr, [SP, #-0x10]!
    //     0xe7f53c: mov             fp, SP
    // 0xe7f540: AllocStack(0x28)
    //     0xe7f540: sub             SP, SP, #0x28
    // 0xe7f544: SetupParameters(PdfString this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xe7f544: mov             x0, x2
    //     0xe7f548: mov             x2, x3
    //     0xe7f54c: stur            x1, [fp, #-8]
    //     0xe7f550: stur            x3, [fp, #-0x10]
    // 0xe7f554: CheckStackOverflow
    //     0xe7f554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f558: cmp             SP, x16
    //     0xe7f55c: b.ls            #0xe7f5e0
    // 0xe7f560: LoadField: r3 = r1->field_f
    //     0xe7f560: ldur            w3, [x1, #0xf]
    // 0xe7f564: DecompressPointer r3
    //     0xe7f564: add             x3, x3, HEAP, lsl #32
    // 0xe7f568: tbnz            w3, #4, #0xe7f584
    // 0xe7f56c: LoadField: r3 = r0->field_1f
    //     0xe7f56c: ldur            w3, [x0, #0x1f]
    // 0xe7f570: DecompressPointer r3
    //     0xe7f570: add             x3, x3, HEAP, lsl #32
    // 0xe7f574: LoadField: r4 = r3->field_b
    //     0xe7f574: ldur            w4, [x3, #0xb]
    // 0xe7f578: DecompressPointer r4
    //     0xe7f578: add             x4, x4, HEAP, lsl #32
    // 0xe7f57c: cmp             w4, NULL
    // 0xe7f580: b.ne            #0xe7f5a0
    // 0xe7f584: LoadField: r3 = r1->field_7
    //     0xe7f584: ldur            w3, [x1, #7]
    // 0xe7f588: DecompressPointer r3
    //     0xe7f588: add             x3, x3, HEAP, lsl #32
    // 0xe7f58c: r0 = _output()
    //     0xe7f58c: bl              #0xe7f5e8  ; [package:pdf/src/pdf/format/string.dart] PdfString::_output
    // 0xe7f590: r0 = Null
    //     0xe7f590: mov             x0, NULL
    // 0xe7f594: LeaveFrame
    //     0xe7f594: mov             SP, fp
    //     0xe7f598: ldp             fp, lr, [SP], #0x10
    // 0xe7f59c: ret
    //     0xe7f59c: ret             
    // 0xe7f5a0: LoadField: r3 = r1->field_7
    //     0xe7f5a0: ldur            w3, [x1, #7]
    // 0xe7f5a4: DecompressPointer r3
    //     0xe7f5a4: add             x3, x3, HEAP, lsl #32
    // 0xe7f5a8: stp             x3, x4, [SP, #8]
    // 0xe7f5ac: str             x0, [SP]
    // 0xe7f5b0: mov             x0, x4
    // 0xe7f5b4: ClosureCall
    //     0xe7f5b4: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xe7f5b8: ldur            x2, [x0, #0x1f]
    //     0xe7f5bc: blr             x2
    // 0xe7f5c0: ldur            x1, [fp, #-8]
    // 0xe7f5c4: ldur            x2, [fp, #-0x10]
    // 0xe7f5c8: mov             x3, x0
    // 0xe7f5cc: r0 = _output()
    //     0xe7f5cc: bl              #0xe7f5e8  ; [package:pdf/src/pdf/format/string.dart] PdfString::_output
    // 0xe7f5d0: r0 = Null
    //     0xe7f5d0: mov             x0, NULL
    // 0xe7f5d4: LeaveFrame
    //     0xe7f5d4: mov             SP, fp
    //     0xe7f5d8: ldp             fp, lr, [SP], #0x10
    // 0xe7f5dc: ret
    //     0xe7f5dc: ret             
    // 0xe7f5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f5e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f5e4: b               #0xe7f560
  }
  _ _output(/* No info */) {
    // ** addr: 0xe7f5e8, size: 0x214
    // 0xe7f5e8: EnterFrame
    //     0xe7f5e8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7f5ec: mov             fp, SP
    // 0xe7f5f0: AllocStack(0x38)
    //     0xe7f5f0: sub             SP, SP, #0x38
    // 0xe7f5f4: SetupParameters(PdfString this /* r1 => r4, fp-0x38 */, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe7f5f4: mov             x4, x1
    //     0xe7f5f8: mov             x0, x2
    //     0xe7f5fc: stur            x2, [fp, #-8]
    //     0xe7f600: stur            x3, [fp, #-0x10]
    //     0xe7f604: stur            x1, [fp, #-0x38]
    // 0xe7f608: CheckStackOverflow
    //     0xe7f608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f60c: cmp             SP, x16
    //     0xe7f610: b.ls            #0xe7f7e4
    // 0xe7f614: LoadField: r1 = r4->field_b
    //     0xe7f614: ldur            w1, [x4, #0xb]
    // 0xe7f618: DecompressPointer r1
    //     0xe7f618: add             x1, x1, HEAP, lsl #32
    // 0xe7f61c: LoadField: r2 = r1->field_7
    //     0xe7f61c: ldur            x2, [x1, #7]
    // 0xe7f620: cmp             x2, #0
    // 0xe7f624: b.gt            #0xe7f7a8
    // 0xe7f628: mov             x1, x0
    // 0xe7f62c: r2 = 60
    //     0xe7f62c: movz            x2, #0x3c
    // 0xe7f630: r0 = putByte()
    //     0xe7f630: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe7f634: ldur            x3, [fp, #-0x10]
    // 0xe7f638: LoadField: r0 = r3->field_13
    //     0xe7f638: ldur            w0, [x3, #0x13]
    // 0xe7f63c: r4 = LoadInt32Instr(r0)
    //     0xe7f63c: sbfx            x4, x0, #1, #0x1f
    // 0xe7f640: stur            x4, [fp, #-0x30]
    // 0xe7f644: ldur            x0, [fp, #-8]
    // 0xe7f648: r1 = -1
    //     0xe7f648: movn            x1, #0
    // 0xe7f64c: r5 = 240
    //     0xe7f64c: movz            x5, #0xf0
    // 0xe7f650: CheckStackOverflow
    //     0xe7f650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f654: cmp             SP, x16
    //     0xe7f658: b.ls            #0xe7f7ec
    // 0xe7f65c: add             x6, x1, #1
    // 0xe7f660: stur            x6, [fp, #-0x28]
    // 0xe7f664: cmp             x6, x4
    // 0xe7f668: b.ge            #0xe7f794
    // 0xe7f66c: ArrayLoad: r1 = r3[r6]  ; List_1
    //     0xe7f66c: add             x16, x3, x6
    //     0xe7f670: ldrb            w1, [x16, #0x17]
    // 0xe7f674: mov             x7, x1
    // 0xe7f678: ubfx            x7, x7, #0, #0x20
    // 0xe7f67c: stur            x7, [fp, #-0x20]
    // 0xe7f680: and             x1, x7, x5
    // 0xe7f684: ubfx            x1, x1, #0, #0x20
    // 0xe7f688: asr             x2, x1, #4
    // 0xe7f68c: cmp             x2, #0xa
    // 0xe7f690: b.ge            #0xe7f69c
    // 0xe7f694: add             x8, x2, #0x30
    // 0xe7f698: b               #0xe7f6a8
    // 0xe7f69c: add             x1, x2, #0x61
    // 0xe7f6a0: sub             x2, x1, #0xa
    // 0xe7f6a4: mov             x8, x2
    // 0xe7f6a8: mov             x1, x0
    // 0xe7f6ac: stur            x8, [fp, #-0x18]
    // 0xe7f6b0: r2 = 1
    //     0xe7f6b0: movz            x2, #0x1
    // 0xe7f6b4: r0 = _ensureCapacity()
    //     0xe7f6b4: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f6b8: ldur            x3, [fp, #-8]
    // 0xe7f6bc: LoadField: r2 = r3->field_7
    //     0xe7f6bc: ldur            w2, [x3, #7]
    // 0xe7f6c0: DecompressPointer r2
    //     0xe7f6c0: add             x2, x2, HEAP, lsl #32
    // 0xe7f6c4: LoadField: r4 = r3->field_b
    //     0xe7f6c4: ldur            x4, [x3, #0xb]
    // 0xe7f6c8: add             x0, x4, #1
    // 0xe7f6cc: StoreField: r3->field_b = r0
    //     0xe7f6cc: stur            x0, [x3, #0xb]
    // 0xe7f6d0: LoadField: r0 = r2->field_13
    //     0xe7f6d0: ldur            w0, [x2, #0x13]
    // 0xe7f6d4: r1 = LoadInt32Instr(r0)
    //     0xe7f6d4: sbfx            x1, x0, #1, #0x1f
    // 0xe7f6d8: mov             x0, x1
    // 0xe7f6dc: mov             x1, x4
    // 0xe7f6e0: cmp             x1, x0
    // 0xe7f6e4: b.hs            #0xe7f7f4
    // 0xe7f6e8: ldur            x0, [fp, #-0x18]
    // 0xe7f6ec: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f6ec: add             x1, x2, x4
    //     0xe7f6f0: strb            w0, [x1, #0x17]
    // 0xe7f6f4: ldur            x1, [fp, #-0x20]
    // 0xe7f6f8: r0 = 15
    //     0xe7f6f8: movz            x0, #0xf
    // 0xe7f6fc: and             x2, x1, x0
    // 0xe7f700: mov             x1, x2
    // 0xe7f704: ubfx            x1, x1, #0, #0x20
    // 0xe7f708: cmp             x1, #0xa
    // 0xe7f70c: b.ge            #0xe7f724
    // 0xe7f710: mov             x1, x2
    // 0xe7f714: ubfx            x1, x1, #0, #0x20
    // 0xe7f718: add             x2, x1, #0x30
    // 0xe7f71c: mov             x4, x2
    // 0xe7f720: b               #0xe7f734
    // 0xe7f724: ubfx            x2, x2, #0, #0x20
    // 0xe7f728: add             x1, x2, #0x61
    // 0xe7f72c: sub             x2, x1, #0xa
    // 0xe7f730: mov             x4, x2
    // 0xe7f734: mov             x1, x3
    // 0xe7f738: stur            x4, [fp, #-0x18]
    // 0xe7f73c: r2 = 1
    //     0xe7f73c: movz            x2, #0x1
    // 0xe7f740: r0 = _ensureCapacity()
    //     0xe7f740: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f744: ldur            x3, [fp, #-8]
    // 0xe7f748: LoadField: r2 = r3->field_7
    //     0xe7f748: ldur            w2, [x3, #7]
    // 0xe7f74c: DecompressPointer r2
    //     0xe7f74c: add             x2, x2, HEAP, lsl #32
    // 0xe7f750: LoadField: r4 = r3->field_b
    //     0xe7f750: ldur            x4, [x3, #0xb]
    // 0xe7f754: add             x0, x4, #1
    // 0xe7f758: StoreField: r3->field_b = r0
    //     0xe7f758: stur            x0, [x3, #0xb]
    // 0xe7f75c: LoadField: r0 = r2->field_13
    //     0xe7f75c: ldur            w0, [x2, #0x13]
    // 0xe7f760: r1 = LoadInt32Instr(r0)
    //     0xe7f760: sbfx            x1, x0, #1, #0x1f
    // 0xe7f764: mov             x0, x1
    // 0xe7f768: mov             x1, x4
    // 0xe7f76c: cmp             x1, x0
    // 0xe7f770: b.hs            #0xe7f7f8
    // 0xe7f774: ldur            x0, [fp, #-0x18]
    // 0xe7f778: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f778: add             x1, x2, x4
    //     0xe7f77c: strb            w0, [x1, #0x17]
    // 0xe7f780: ldur            x1, [fp, #-0x28]
    // 0xe7f784: mov             x0, x3
    // 0xe7f788: ldur            x3, [fp, #-0x10]
    // 0xe7f78c: ldur            x4, [fp, #-0x30]
    // 0xe7f790: b               #0xe7f64c
    // 0xe7f794: mov             x3, x0
    // 0xe7f798: mov             x1, x3
    // 0xe7f79c: r2 = 62
    //     0xe7f79c: movz            x2, #0x3e
    // 0xe7f7a0: r0 = putByte()
    //     0xe7f7a0: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe7f7a4: b               #0xe7f7d4
    // 0xe7f7a8: mov             x3, x0
    // 0xe7f7ac: mov             x1, x3
    // 0xe7f7b0: r2 = 40
    //     0xe7f7b0: movz            x2, #0x28
    // 0xe7f7b4: r0 = putByte()
    //     0xe7f7b4: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe7f7b8: ldur            x1, [fp, #-0x38]
    // 0xe7f7bc: ldur            x2, [fp, #-8]
    // 0xe7f7c0: ldur            x3, [fp, #-0x10]
    // 0xe7f7c4: r0 = _putTextBytes()
    //     0xe7f7c4: bl              #0xe7f7fc  ; [package:pdf/src/pdf/format/string.dart] PdfString::_putTextBytes
    // 0xe7f7c8: ldur            x1, [fp, #-8]
    // 0xe7f7cc: r2 = 41
    //     0xe7f7cc: movz            x2, #0x29
    // 0xe7f7d0: r0 = putByte()
    //     0xe7f7d0: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe7f7d4: r0 = Null
    //     0xe7f7d4: mov             x0, NULL
    // 0xe7f7d8: LeaveFrame
    //     0xe7f7d8: mov             SP, fp
    //     0xe7f7dc: ldp             fp, lr, [SP], #0x10
    // 0xe7f7e0: ret
    //     0xe7f7e0: ret             
    // 0xe7f7e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f7e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f7e8: b               #0xe7f614
    // 0xe7f7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7f7ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7f7f0: b               #0xe7f65c
    // 0xe7f7f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7f7f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7f7f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7f7f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _putTextBytes(/* No info */) {
    // ** addr: 0xe7f7fc, size: 0x698
    // 0xe7f7fc: EnterFrame
    //     0xe7f7fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe7f800: mov             fp, SP
    // 0xe7f804: AllocStack(0x28)
    //     0xe7f804: sub             SP, SP, #0x28
    // 0xe7f808: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe7f808: mov             x0, x2
    //     0xe7f80c: stur            x2, [fp, #-0x18]
    //     0xe7f810: stur            x3, [fp, #-0x20]
    // 0xe7f814: CheckStackOverflow
    //     0xe7f814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f818: cmp             SP, x16
    //     0xe7f81c: b.ls            #0xe7fe40
    // 0xe7f820: LoadField: r1 = r3->field_13
    //     0xe7f820: ldur            w1, [x3, #0x13]
    // 0xe7f824: r4 = LoadInt32Instr(r1)
    //     0xe7f824: sbfx            x4, x1, #1, #0x1f
    // 0xe7f828: stur            x4, [fp, #-0x10]
    // 0xe7f82c: r1 = -1
    //     0xe7f82c: movn            x1, #0
    // 0xe7f830: CheckStackOverflow
    //     0xe7f830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7f834: cmp             SP, x16
    //     0xe7f838: b.ls            #0xe7fe48
    // 0xe7f83c: add             x5, x1, #1
    // 0xe7f840: stur            x5, [fp, #-8]
    // 0xe7f844: cmp             x5, x4
    // 0xe7f848: b.ge            #0xe7fe30
    // 0xe7f84c: ArrayLoad: r6 = r3[r5]  ; List_1
    //     0xe7f84c: add             x16, x3, x5
    //     0xe7f850: ldrb            w6, [x16, #0x17]
    // 0xe7f854: stur            x6, [fp, #-0x28]
    // 0xe7f858: lsl             x1, x6, #1
    // 0xe7f85c: cmp             x6, #0xc
    // 0xe7f860: b.gt            #0xe7fb1c
    // 0xe7f864: cmp             x6, #9
    // 0xe7f868: b.gt            #0xe7f9c4
    // 0xe7f86c: cmp             x6, #8
    // 0xe7f870: b.gt            #0xe7f924
    // 0xe7f874: cmp             w1, #0x10
    // 0xe7f878: b.ne            #0xe7f914
    // 0xe7f87c: mov             x1, x0
    // 0xe7f880: r2 = 1
    //     0xe7f880: movz            x2, #0x1
    // 0xe7f884: r0 = _ensureCapacity()
    //     0xe7f884: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f888: ldur            x3, [fp, #-0x18]
    // 0xe7f88c: LoadField: r2 = r3->field_7
    //     0xe7f88c: ldur            w2, [x3, #7]
    // 0xe7f890: DecompressPointer r2
    //     0xe7f890: add             x2, x2, HEAP, lsl #32
    // 0xe7f894: LoadField: r4 = r3->field_b
    //     0xe7f894: ldur            x4, [x3, #0xb]
    // 0xe7f898: add             x0, x4, #1
    // 0xe7f89c: StoreField: r3->field_b = r0
    //     0xe7f89c: stur            x0, [x3, #0xb]
    // 0xe7f8a0: LoadField: r0 = r2->field_13
    //     0xe7f8a0: ldur            w0, [x2, #0x13]
    // 0xe7f8a4: r1 = LoadInt32Instr(r0)
    //     0xe7f8a4: sbfx            x1, x0, #1, #0x1f
    // 0xe7f8a8: mov             x0, x1
    // 0xe7f8ac: mov             x1, x4
    // 0xe7f8b0: cmp             x1, x0
    // 0xe7f8b4: b.hs            #0xe7fe50
    // 0xe7f8b8: r0 = 92
    //     0xe7f8b8: movz            x0, #0x5c
    // 0xe7f8bc: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f8bc: add             x1, x2, x4
    //     0xe7f8c0: strb            w0, [x1, #0x17]
    // 0xe7f8c4: mov             x1, x3
    // 0xe7f8c8: r2 = 1
    //     0xe7f8c8: movz            x2, #0x1
    // 0xe7f8cc: r0 = _ensureCapacity()
    //     0xe7f8cc: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f8d0: ldur            x3, [fp, #-0x18]
    // 0xe7f8d4: LoadField: r2 = r3->field_7
    //     0xe7f8d4: ldur            w2, [x3, #7]
    // 0xe7f8d8: DecompressPointer r2
    //     0xe7f8d8: add             x2, x2, HEAP, lsl #32
    // 0xe7f8dc: LoadField: r4 = r3->field_b
    //     0xe7f8dc: ldur            x4, [x3, #0xb]
    // 0xe7f8e0: add             x0, x4, #1
    // 0xe7f8e4: StoreField: r3->field_b = r0
    //     0xe7f8e4: stur            x0, [x3, #0xb]
    // 0xe7f8e8: LoadField: r0 = r2->field_13
    //     0xe7f8e8: ldur            w0, [x2, #0x13]
    // 0xe7f8ec: r1 = LoadInt32Instr(r0)
    //     0xe7f8ec: sbfx            x1, x0, #1, #0x1f
    // 0xe7f8f0: mov             x0, x1
    // 0xe7f8f4: mov             x1, x4
    // 0xe7f8f8: cmp             x1, x0
    // 0xe7f8fc: b.hs            #0xe7fe54
    // 0xe7f900: r0 = 98
    //     0xe7f900: movz            x0, #0x62
    // 0xe7f904: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f904: add             x1, x2, x4
    //     0xe7f908: strb            w0, [x1, #0x17]
    // 0xe7f90c: mov             x2, x3
    // 0xe7f910: b               #0xe7fe1c
    // 0xe7f914: mov             x3, x0
    // 0xe7f918: r0 = 98
    //     0xe7f918: movz            x0, #0x62
    // 0xe7f91c: r0 = 92
    //     0xe7f91c: movz            x0, #0x5c
    // 0xe7f920: b               #0xe7fdd8
    // 0xe7f924: mov             x3, x0
    // 0xe7f928: r0 = 98
    //     0xe7f928: movz            x0, #0x62
    // 0xe7f92c: mov             x1, x3
    // 0xe7f930: r2 = 1
    //     0xe7f930: movz            x2, #0x1
    // 0xe7f934: r0 = _ensureCapacity()
    //     0xe7f934: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f938: ldur            x3, [fp, #-0x18]
    // 0xe7f93c: LoadField: r2 = r3->field_7
    //     0xe7f93c: ldur            w2, [x3, #7]
    // 0xe7f940: DecompressPointer r2
    //     0xe7f940: add             x2, x2, HEAP, lsl #32
    // 0xe7f944: LoadField: r4 = r3->field_b
    //     0xe7f944: ldur            x4, [x3, #0xb]
    // 0xe7f948: add             x0, x4, #1
    // 0xe7f94c: StoreField: r3->field_b = r0
    //     0xe7f94c: stur            x0, [x3, #0xb]
    // 0xe7f950: LoadField: r0 = r2->field_13
    //     0xe7f950: ldur            w0, [x2, #0x13]
    // 0xe7f954: r1 = LoadInt32Instr(r0)
    //     0xe7f954: sbfx            x1, x0, #1, #0x1f
    // 0xe7f958: mov             x0, x1
    // 0xe7f95c: mov             x1, x4
    // 0xe7f960: cmp             x1, x0
    // 0xe7f964: b.hs            #0xe7fe58
    // 0xe7f968: r0 = 92
    //     0xe7f968: movz            x0, #0x5c
    // 0xe7f96c: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f96c: add             x1, x2, x4
    //     0xe7f970: strb            w0, [x1, #0x17]
    // 0xe7f974: mov             x1, x3
    // 0xe7f978: r2 = 1
    //     0xe7f978: movz            x2, #0x1
    // 0xe7f97c: r0 = _ensureCapacity()
    //     0xe7f97c: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f980: ldur            x3, [fp, #-0x18]
    // 0xe7f984: LoadField: r2 = r3->field_7
    //     0xe7f984: ldur            w2, [x3, #7]
    // 0xe7f988: DecompressPointer r2
    //     0xe7f988: add             x2, x2, HEAP, lsl #32
    // 0xe7f98c: LoadField: r4 = r3->field_b
    //     0xe7f98c: ldur            x4, [x3, #0xb]
    // 0xe7f990: add             x0, x4, #1
    // 0xe7f994: StoreField: r3->field_b = r0
    //     0xe7f994: stur            x0, [x3, #0xb]
    // 0xe7f998: LoadField: r0 = r2->field_13
    //     0xe7f998: ldur            w0, [x2, #0x13]
    // 0xe7f99c: r1 = LoadInt32Instr(r0)
    //     0xe7f99c: sbfx            x1, x0, #1, #0x1f
    // 0xe7f9a0: mov             x0, x1
    // 0xe7f9a4: mov             x1, x4
    // 0xe7f9a8: cmp             x1, x0
    // 0xe7f9ac: b.hs            #0xe7fe5c
    // 0xe7f9b0: r0 = 116
    //     0xe7f9b0: movz            x0, #0x74
    // 0xe7f9b4: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7f9b4: add             x1, x2, x4
    //     0xe7f9b8: strb            w0, [x1, #0x17]
    // 0xe7f9bc: mov             x2, x3
    // 0xe7f9c0: b               #0xe7fe1c
    // 0xe7f9c4: mov             x3, x0
    // 0xe7f9c8: r0 = 116
    //     0xe7f9c8: movz            x0, #0x74
    // 0xe7f9cc: cmp             x6, #0xa
    // 0xe7f9d0: b.gt            #0xe7fa6c
    // 0xe7f9d4: mov             x1, x3
    // 0xe7f9d8: r2 = 1
    //     0xe7f9d8: movz            x2, #0x1
    // 0xe7f9dc: r0 = _ensureCapacity()
    //     0xe7f9dc: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7f9e0: ldur            x3, [fp, #-0x18]
    // 0xe7f9e4: LoadField: r2 = r3->field_7
    //     0xe7f9e4: ldur            w2, [x3, #7]
    // 0xe7f9e8: DecompressPointer r2
    //     0xe7f9e8: add             x2, x2, HEAP, lsl #32
    // 0xe7f9ec: LoadField: r4 = r3->field_b
    //     0xe7f9ec: ldur            x4, [x3, #0xb]
    // 0xe7f9f0: add             x0, x4, #1
    // 0xe7f9f4: StoreField: r3->field_b = r0
    //     0xe7f9f4: stur            x0, [x3, #0xb]
    // 0xe7f9f8: LoadField: r0 = r2->field_13
    //     0xe7f9f8: ldur            w0, [x2, #0x13]
    // 0xe7f9fc: r1 = LoadInt32Instr(r0)
    //     0xe7f9fc: sbfx            x1, x0, #1, #0x1f
    // 0xe7fa00: mov             x0, x1
    // 0xe7fa04: mov             x1, x4
    // 0xe7fa08: cmp             x1, x0
    // 0xe7fa0c: b.hs            #0xe7fe60
    // 0xe7fa10: r0 = 92
    //     0xe7fa10: movz            x0, #0x5c
    // 0xe7fa14: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fa14: add             x1, x2, x4
    //     0xe7fa18: strb            w0, [x1, #0x17]
    // 0xe7fa1c: mov             x1, x3
    // 0xe7fa20: r2 = 1
    //     0xe7fa20: movz            x2, #0x1
    // 0xe7fa24: r0 = _ensureCapacity()
    //     0xe7fa24: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fa28: ldur            x3, [fp, #-0x18]
    // 0xe7fa2c: LoadField: r2 = r3->field_7
    //     0xe7fa2c: ldur            w2, [x3, #7]
    // 0xe7fa30: DecompressPointer r2
    //     0xe7fa30: add             x2, x2, HEAP, lsl #32
    // 0xe7fa34: LoadField: r4 = r3->field_b
    //     0xe7fa34: ldur            x4, [x3, #0xb]
    // 0xe7fa38: add             x0, x4, #1
    // 0xe7fa3c: StoreField: r3->field_b = r0
    //     0xe7fa3c: stur            x0, [x3, #0xb]
    // 0xe7fa40: LoadField: r0 = r2->field_13
    //     0xe7fa40: ldur            w0, [x2, #0x13]
    // 0xe7fa44: r1 = LoadInt32Instr(r0)
    //     0xe7fa44: sbfx            x1, x0, #1, #0x1f
    // 0xe7fa48: mov             x0, x1
    // 0xe7fa4c: mov             x1, x4
    // 0xe7fa50: cmp             x1, x0
    // 0xe7fa54: b.hs            #0xe7fe64
    // 0xe7fa58: r0 = 110
    //     0xe7fa58: movz            x0, #0x6e
    // 0xe7fa5c: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fa5c: add             x1, x2, x4
    //     0xe7fa60: strb            w0, [x1, #0x17]
    // 0xe7fa64: mov             x2, x3
    // 0xe7fa68: b               #0xe7fe1c
    // 0xe7fa6c: r0 = 110
    //     0xe7fa6c: movz            x0, #0x6e
    // 0xe7fa70: cmp             x6, #0xc
    // 0xe7fa74: b.lt            #0xe7fb10
    // 0xe7fa78: mov             x1, x3
    // 0xe7fa7c: r2 = 1
    //     0xe7fa7c: movz            x2, #0x1
    // 0xe7fa80: r0 = _ensureCapacity()
    //     0xe7fa80: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fa84: ldur            x3, [fp, #-0x18]
    // 0xe7fa88: LoadField: r2 = r3->field_7
    //     0xe7fa88: ldur            w2, [x3, #7]
    // 0xe7fa8c: DecompressPointer r2
    //     0xe7fa8c: add             x2, x2, HEAP, lsl #32
    // 0xe7fa90: LoadField: r4 = r3->field_b
    //     0xe7fa90: ldur            x4, [x3, #0xb]
    // 0xe7fa94: add             x0, x4, #1
    // 0xe7fa98: StoreField: r3->field_b = r0
    //     0xe7fa98: stur            x0, [x3, #0xb]
    // 0xe7fa9c: LoadField: r0 = r2->field_13
    //     0xe7fa9c: ldur            w0, [x2, #0x13]
    // 0xe7faa0: r1 = LoadInt32Instr(r0)
    //     0xe7faa0: sbfx            x1, x0, #1, #0x1f
    // 0xe7faa4: mov             x0, x1
    // 0xe7faa8: mov             x1, x4
    // 0xe7faac: cmp             x1, x0
    // 0xe7fab0: b.hs            #0xe7fe68
    // 0xe7fab4: r0 = 92
    //     0xe7fab4: movz            x0, #0x5c
    // 0xe7fab8: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fab8: add             x1, x2, x4
    //     0xe7fabc: strb            w0, [x1, #0x17]
    // 0xe7fac0: mov             x1, x3
    // 0xe7fac4: r2 = 1
    //     0xe7fac4: movz            x2, #0x1
    // 0xe7fac8: r0 = _ensureCapacity()
    //     0xe7fac8: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7facc: ldur            x3, [fp, #-0x18]
    // 0xe7fad0: LoadField: r2 = r3->field_7
    //     0xe7fad0: ldur            w2, [x3, #7]
    // 0xe7fad4: DecompressPointer r2
    //     0xe7fad4: add             x2, x2, HEAP, lsl #32
    // 0xe7fad8: LoadField: r4 = r3->field_b
    //     0xe7fad8: ldur            x4, [x3, #0xb]
    // 0xe7fadc: add             x0, x4, #1
    // 0xe7fae0: StoreField: r3->field_b = r0
    //     0xe7fae0: stur            x0, [x3, #0xb]
    // 0xe7fae4: LoadField: r0 = r2->field_13
    //     0xe7fae4: ldur            w0, [x2, #0x13]
    // 0xe7fae8: r1 = LoadInt32Instr(r0)
    //     0xe7fae8: sbfx            x1, x0, #1, #0x1f
    // 0xe7faec: mov             x0, x1
    // 0xe7faf0: mov             x1, x4
    // 0xe7faf4: cmp             x1, x0
    // 0xe7faf8: b.hs            #0xe7fe6c
    // 0xe7fafc: r0 = 102
    //     0xe7fafc: movz            x0, #0x66
    // 0xe7fb00: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fb00: add             x1, x2, x4
    //     0xe7fb04: strb            w0, [x1, #0x17]
    // 0xe7fb08: mov             x2, x3
    // 0xe7fb0c: b               #0xe7fe1c
    // 0xe7fb10: r0 = 102
    //     0xe7fb10: movz            x0, #0x66
    // 0xe7fb14: r0 = 92
    //     0xe7fb14: movz            x0, #0x5c
    // 0xe7fb18: b               #0xe7fdd8
    // 0xe7fb1c: mov             x3, x0
    // 0xe7fb20: r0 = 102
    //     0xe7fb20: movz            x0, #0x66
    // 0xe7fb24: cmp             x6, #0x28
    // 0xe7fb28: b.gt            #0xe7fc7c
    // 0xe7fb2c: cmp             x6, #0xd
    // 0xe7fb30: b.gt            #0xe7fbcc
    // 0xe7fb34: mov             x1, x3
    // 0xe7fb38: r2 = 1
    //     0xe7fb38: movz            x2, #0x1
    // 0xe7fb3c: r0 = _ensureCapacity()
    //     0xe7fb3c: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fb40: ldur            x3, [fp, #-0x18]
    // 0xe7fb44: LoadField: r2 = r3->field_7
    //     0xe7fb44: ldur            w2, [x3, #7]
    // 0xe7fb48: DecompressPointer r2
    //     0xe7fb48: add             x2, x2, HEAP, lsl #32
    // 0xe7fb4c: LoadField: r4 = r3->field_b
    //     0xe7fb4c: ldur            x4, [x3, #0xb]
    // 0xe7fb50: add             x0, x4, #1
    // 0xe7fb54: StoreField: r3->field_b = r0
    //     0xe7fb54: stur            x0, [x3, #0xb]
    // 0xe7fb58: LoadField: r0 = r2->field_13
    //     0xe7fb58: ldur            w0, [x2, #0x13]
    // 0xe7fb5c: r1 = LoadInt32Instr(r0)
    //     0xe7fb5c: sbfx            x1, x0, #1, #0x1f
    // 0xe7fb60: mov             x0, x1
    // 0xe7fb64: mov             x1, x4
    // 0xe7fb68: cmp             x1, x0
    // 0xe7fb6c: b.hs            #0xe7fe70
    // 0xe7fb70: r0 = 92
    //     0xe7fb70: movz            x0, #0x5c
    // 0xe7fb74: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fb74: add             x1, x2, x4
    //     0xe7fb78: strb            w0, [x1, #0x17]
    // 0xe7fb7c: mov             x1, x3
    // 0xe7fb80: r2 = 1
    //     0xe7fb80: movz            x2, #0x1
    // 0xe7fb84: r0 = _ensureCapacity()
    //     0xe7fb84: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fb88: ldur            x3, [fp, #-0x18]
    // 0xe7fb8c: LoadField: r2 = r3->field_7
    //     0xe7fb8c: ldur            w2, [x3, #7]
    // 0xe7fb90: DecompressPointer r2
    //     0xe7fb90: add             x2, x2, HEAP, lsl #32
    // 0xe7fb94: LoadField: r4 = r3->field_b
    //     0xe7fb94: ldur            x4, [x3, #0xb]
    // 0xe7fb98: add             x0, x4, #1
    // 0xe7fb9c: StoreField: r3->field_b = r0
    //     0xe7fb9c: stur            x0, [x3, #0xb]
    // 0xe7fba0: LoadField: r0 = r2->field_13
    //     0xe7fba0: ldur            w0, [x2, #0x13]
    // 0xe7fba4: r1 = LoadInt32Instr(r0)
    //     0xe7fba4: sbfx            x1, x0, #1, #0x1f
    // 0xe7fba8: mov             x0, x1
    // 0xe7fbac: mov             x1, x4
    // 0xe7fbb0: cmp             x1, x0
    // 0xe7fbb4: b.hs            #0xe7fe74
    // 0xe7fbb8: r0 = 114
    //     0xe7fbb8: movz            x0, #0x72
    // 0xe7fbbc: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fbbc: add             x1, x2, x4
    //     0xe7fbc0: strb            w0, [x1, #0x17]
    // 0xe7fbc4: mov             x2, x3
    // 0xe7fbc8: b               #0xe7fe1c
    // 0xe7fbcc: r0 = 114
    //     0xe7fbcc: movz            x0, #0x72
    // 0xe7fbd0: cmp             x6, #0x28
    // 0xe7fbd4: b.lt            #0xe7fc70
    // 0xe7fbd8: mov             x1, x3
    // 0xe7fbdc: r2 = 1
    //     0xe7fbdc: movz            x2, #0x1
    // 0xe7fbe0: r0 = _ensureCapacity()
    //     0xe7fbe0: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fbe4: ldur            x3, [fp, #-0x18]
    // 0xe7fbe8: LoadField: r2 = r3->field_7
    //     0xe7fbe8: ldur            w2, [x3, #7]
    // 0xe7fbec: DecompressPointer r2
    //     0xe7fbec: add             x2, x2, HEAP, lsl #32
    // 0xe7fbf0: LoadField: r4 = r3->field_b
    //     0xe7fbf0: ldur            x4, [x3, #0xb]
    // 0xe7fbf4: add             x0, x4, #1
    // 0xe7fbf8: StoreField: r3->field_b = r0
    //     0xe7fbf8: stur            x0, [x3, #0xb]
    // 0xe7fbfc: LoadField: r0 = r2->field_13
    //     0xe7fbfc: ldur            w0, [x2, #0x13]
    // 0xe7fc00: r1 = LoadInt32Instr(r0)
    //     0xe7fc00: sbfx            x1, x0, #1, #0x1f
    // 0xe7fc04: mov             x0, x1
    // 0xe7fc08: mov             x1, x4
    // 0xe7fc0c: cmp             x1, x0
    // 0xe7fc10: b.hs            #0xe7fe78
    // 0xe7fc14: r0 = 92
    //     0xe7fc14: movz            x0, #0x5c
    // 0xe7fc18: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fc18: add             x1, x2, x4
    //     0xe7fc1c: strb            w0, [x1, #0x17]
    // 0xe7fc20: mov             x1, x3
    // 0xe7fc24: r2 = 1
    //     0xe7fc24: movz            x2, #0x1
    // 0xe7fc28: r0 = _ensureCapacity()
    //     0xe7fc28: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fc2c: ldur            x3, [fp, #-0x18]
    // 0xe7fc30: LoadField: r2 = r3->field_7
    //     0xe7fc30: ldur            w2, [x3, #7]
    // 0xe7fc34: DecompressPointer r2
    //     0xe7fc34: add             x2, x2, HEAP, lsl #32
    // 0xe7fc38: LoadField: r4 = r3->field_b
    //     0xe7fc38: ldur            x4, [x3, #0xb]
    // 0xe7fc3c: add             x0, x4, #1
    // 0xe7fc40: StoreField: r3->field_b = r0
    //     0xe7fc40: stur            x0, [x3, #0xb]
    // 0xe7fc44: LoadField: r0 = r2->field_13
    //     0xe7fc44: ldur            w0, [x2, #0x13]
    // 0xe7fc48: r1 = LoadInt32Instr(r0)
    //     0xe7fc48: sbfx            x1, x0, #1, #0x1f
    // 0xe7fc4c: mov             x0, x1
    // 0xe7fc50: mov             x1, x4
    // 0xe7fc54: cmp             x1, x0
    // 0xe7fc58: b.hs            #0xe7fe7c
    // 0xe7fc5c: r0 = 40
    //     0xe7fc5c: movz            x0, #0x28
    // 0xe7fc60: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fc60: add             x1, x2, x4
    //     0xe7fc64: strb            w0, [x1, #0x17]
    // 0xe7fc68: mov             x2, x3
    // 0xe7fc6c: b               #0xe7fe1c
    // 0xe7fc70: r0 = 40
    //     0xe7fc70: movz            x0, #0x28
    // 0xe7fc74: r0 = 92
    //     0xe7fc74: movz            x0, #0x5c
    // 0xe7fc78: b               #0xe7fdd8
    // 0xe7fc7c: r0 = 40
    //     0xe7fc7c: movz            x0, #0x28
    // 0xe7fc80: cmp             x6, #0x29
    // 0xe7fc84: b.gt            #0xe7fd20
    // 0xe7fc88: mov             x1, x3
    // 0xe7fc8c: r2 = 1
    //     0xe7fc8c: movz            x2, #0x1
    // 0xe7fc90: r0 = _ensureCapacity()
    //     0xe7fc90: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fc94: ldur            x3, [fp, #-0x18]
    // 0xe7fc98: LoadField: r2 = r3->field_7
    //     0xe7fc98: ldur            w2, [x3, #7]
    // 0xe7fc9c: DecompressPointer r2
    //     0xe7fc9c: add             x2, x2, HEAP, lsl #32
    // 0xe7fca0: LoadField: r4 = r3->field_b
    //     0xe7fca0: ldur            x4, [x3, #0xb]
    // 0xe7fca4: add             x0, x4, #1
    // 0xe7fca8: StoreField: r3->field_b = r0
    //     0xe7fca8: stur            x0, [x3, #0xb]
    // 0xe7fcac: LoadField: r0 = r2->field_13
    //     0xe7fcac: ldur            w0, [x2, #0x13]
    // 0xe7fcb0: r1 = LoadInt32Instr(r0)
    //     0xe7fcb0: sbfx            x1, x0, #1, #0x1f
    // 0xe7fcb4: mov             x0, x1
    // 0xe7fcb8: mov             x1, x4
    // 0xe7fcbc: cmp             x1, x0
    // 0xe7fcc0: b.hs            #0xe7fe80
    // 0xe7fcc4: r0 = 92
    //     0xe7fcc4: movz            x0, #0x5c
    // 0xe7fcc8: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fcc8: add             x1, x2, x4
    //     0xe7fccc: strb            w0, [x1, #0x17]
    // 0xe7fcd0: mov             x1, x3
    // 0xe7fcd4: r2 = 1
    //     0xe7fcd4: movz            x2, #0x1
    // 0xe7fcd8: r0 = _ensureCapacity()
    //     0xe7fcd8: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fcdc: ldur            x3, [fp, #-0x18]
    // 0xe7fce0: LoadField: r2 = r3->field_7
    //     0xe7fce0: ldur            w2, [x3, #7]
    // 0xe7fce4: DecompressPointer r2
    //     0xe7fce4: add             x2, x2, HEAP, lsl #32
    // 0xe7fce8: LoadField: r4 = r3->field_b
    //     0xe7fce8: ldur            x4, [x3, #0xb]
    // 0xe7fcec: add             x0, x4, #1
    // 0xe7fcf0: StoreField: r3->field_b = r0
    //     0xe7fcf0: stur            x0, [x3, #0xb]
    // 0xe7fcf4: LoadField: r0 = r2->field_13
    //     0xe7fcf4: ldur            w0, [x2, #0x13]
    // 0xe7fcf8: r1 = LoadInt32Instr(r0)
    //     0xe7fcf8: sbfx            x1, x0, #1, #0x1f
    // 0xe7fcfc: mov             x0, x1
    // 0xe7fd00: mov             x1, x4
    // 0xe7fd04: cmp             x1, x0
    // 0xe7fd08: b.hs            #0xe7fe84
    // 0xe7fd0c: r0 = 41
    //     0xe7fd0c: movz            x0, #0x29
    // 0xe7fd10: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fd10: add             x1, x2, x4
    //     0xe7fd14: strb            w0, [x1, #0x17]
    // 0xe7fd18: mov             x2, x3
    // 0xe7fd1c: b               #0xe7fe1c
    // 0xe7fd20: r0 = 41
    //     0xe7fd20: movz            x0, #0x29
    // 0xe7fd24: cmp             x6, #0x5c
    // 0xe7fd28: b.lt            #0xe7fdd4
    // 0xe7fd2c: cmp             w1, #0xb8
    // 0xe7fd30: b.ne            #0xe7fdcc
    // 0xe7fd34: mov             x1, x3
    // 0xe7fd38: r2 = 1
    //     0xe7fd38: movz            x2, #0x1
    // 0xe7fd3c: r0 = _ensureCapacity()
    //     0xe7fd3c: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fd40: ldur            x3, [fp, #-0x18]
    // 0xe7fd44: LoadField: r2 = r3->field_7
    //     0xe7fd44: ldur            w2, [x3, #7]
    // 0xe7fd48: DecompressPointer r2
    //     0xe7fd48: add             x2, x2, HEAP, lsl #32
    // 0xe7fd4c: LoadField: r4 = r3->field_b
    //     0xe7fd4c: ldur            x4, [x3, #0xb]
    // 0xe7fd50: add             x0, x4, #1
    // 0xe7fd54: StoreField: r3->field_b = r0
    //     0xe7fd54: stur            x0, [x3, #0xb]
    // 0xe7fd58: LoadField: r0 = r2->field_13
    //     0xe7fd58: ldur            w0, [x2, #0x13]
    // 0xe7fd5c: r1 = LoadInt32Instr(r0)
    //     0xe7fd5c: sbfx            x1, x0, #1, #0x1f
    // 0xe7fd60: mov             x0, x1
    // 0xe7fd64: mov             x1, x4
    // 0xe7fd68: cmp             x1, x0
    // 0xe7fd6c: b.hs            #0xe7fe88
    // 0xe7fd70: r0 = 92
    //     0xe7fd70: movz            x0, #0x5c
    // 0xe7fd74: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fd74: add             x1, x2, x4
    //     0xe7fd78: strb            w0, [x1, #0x17]
    // 0xe7fd7c: mov             x1, x3
    // 0xe7fd80: r2 = 1
    //     0xe7fd80: movz            x2, #0x1
    // 0xe7fd84: r0 = _ensureCapacity()
    //     0xe7fd84: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fd88: ldur            x3, [fp, #-0x18]
    // 0xe7fd8c: LoadField: r2 = r3->field_7
    //     0xe7fd8c: ldur            w2, [x3, #7]
    // 0xe7fd90: DecompressPointer r2
    //     0xe7fd90: add             x2, x2, HEAP, lsl #32
    // 0xe7fd94: LoadField: r4 = r3->field_b
    //     0xe7fd94: ldur            x4, [x3, #0xb]
    // 0xe7fd98: add             x0, x4, #1
    // 0xe7fd9c: StoreField: r3->field_b = r0
    //     0xe7fd9c: stur            x0, [x3, #0xb]
    // 0xe7fda0: LoadField: r0 = r2->field_13
    //     0xe7fda0: ldur            w0, [x2, #0x13]
    // 0xe7fda4: r1 = LoadInt32Instr(r0)
    //     0xe7fda4: sbfx            x1, x0, #1, #0x1f
    // 0xe7fda8: mov             x0, x1
    // 0xe7fdac: mov             x1, x4
    // 0xe7fdb0: cmp             x1, x0
    // 0xe7fdb4: b.hs            #0xe7fe8c
    // 0xe7fdb8: r0 = 92
    //     0xe7fdb8: movz            x0, #0x5c
    // 0xe7fdbc: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe7fdbc: add             x1, x2, x4
    //     0xe7fdc0: strb            w0, [x1, #0x17]
    // 0xe7fdc4: mov             x2, x3
    // 0xe7fdc8: b               #0xe7fe1c
    // 0xe7fdcc: r0 = 92
    //     0xe7fdcc: movz            x0, #0x5c
    // 0xe7fdd0: b               #0xe7fdd8
    // 0xe7fdd4: r0 = 92
    //     0xe7fdd4: movz            x0, #0x5c
    // 0xe7fdd8: mov             x1, x3
    // 0xe7fddc: r2 = 1
    //     0xe7fddc: movz            x2, #0x1
    // 0xe7fde0: r0 = _ensureCapacity()
    //     0xe7fde0: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe7fde4: ldur            x2, [fp, #-0x18]
    // 0xe7fde8: LoadField: r3 = r2->field_7
    //     0xe7fde8: ldur            w3, [x2, #7]
    // 0xe7fdec: DecompressPointer r3
    //     0xe7fdec: add             x3, x3, HEAP, lsl #32
    // 0xe7fdf0: LoadField: r4 = r2->field_b
    //     0xe7fdf0: ldur            x4, [x2, #0xb]
    // 0xe7fdf4: add             x5, x4, #1
    // 0xe7fdf8: StoreField: r2->field_b = r5
    //     0xe7fdf8: stur            x5, [x2, #0xb]
    // 0xe7fdfc: LoadField: r5 = r3->field_13
    //     0xe7fdfc: ldur            w5, [x3, #0x13]
    // 0xe7fe00: r0 = LoadInt32Instr(r5)
    //     0xe7fe00: sbfx            x0, x5, #1, #0x1f
    // 0xe7fe04: mov             x1, x4
    // 0xe7fe08: cmp             x1, x0
    // 0xe7fe0c: b.hs            #0xe7fe90
    // 0xe7fe10: ldur            x1, [fp, #-0x28]
    // 0xe7fe14: ArrayStore: r3[r4] = r1  ; TypeUnknown_1
    //     0xe7fe14: add             x5, x3, x4
    //     0xe7fe18: strb            w1, [x5, #0x17]
    // 0xe7fe1c: ldur            x1, [fp, #-8]
    // 0xe7fe20: mov             x0, x2
    // 0xe7fe24: ldur            x3, [fp, #-0x20]
    // 0xe7fe28: ldur            x4, [fp, #-0x10]
    // 0xe7fe2c: b               #0xe7f830
    // 0xe7fe30: r0 = Null
    //     0xe7fe30: mov             x0, NULL
    // 0xe7fe34: LeaveFrame
    //     0xe7fe34: mov             SP, fp
    //     0xe7fe38: ldp             fp, lr, [SP], #0x10
    // 0xe7fe3c: ret
    //     0xe7fe3c: ret             
    // 0xe7fe40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7fe40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7fe44: b               #0xe7f820
    // 0xe7fe48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7fe48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7fe4c: b               #0xe7f83c
    // 0xe7fe50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe58: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe60: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe7c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe8c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7fe90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7fe90: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 6817, size: 0x14, field offset: 0x14
enum PdfStringFormat extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d8e8, size: 0x64
    // 0xc4d8e8: EnterFrame
    //     0xc4d8e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d8ec: mov             fp, SP
    // 0xc4d8f0: AllocStack(0x10)
    //     0xc4d8f0: sub             SP, SP, #0x10
    // 0xc4d8f4: SetupParameters(PdfStringFormat this /* r1 => r0, fp-0x8 */)
    //     0xc4d8f4: mov             x0, x1
    //     0xc4d8f8: stur            x1, [fp, #-8]
    // 0xc4d8fc: CheckStackOverflow
    //     0xc4d8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d900: cmp             SP, x16
    //     0xc4d904: b.ls            #0xc4d944
    // 0xc4d908: r1 = Null
    //     0xc4d908: mov             x1, NULL
    // 0xc4d90c: r2 = 4
    //     0xc4d90c: movz            x2, #0x4
    // 0xc4d910: r0 = AllocateArray()
    //     0xc4d910: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d914: r16 = "PdfStringFormat."
    //     0xc4d914: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c1c8] "PdfStringFormat."
    //     0xc4d918: ldr             x16, [x16, #0x1c8]
    // 0xc4d91c: StoreField: r0->field_f = r16
    //     0xc4d91c: stur            w16, [x0, #0xf]
    // 0xc4d920: ldur            x1, [fp, #-8]
    // 0xc4d924: LoadField: r2 = r1->field_f
    //     0xc4d924: ldur            w2, [x1, #0xf]
    // 0xc4d928: DecompressPointer r2
    //     0xc4d928: add             x2, x2, HEAP, lsl #32
    // 0xc4d92c: StoreField: r0->field_13 = r2
    //     0xc4d92c: stur            w2, [x0, #0x13]
    // 0xc4d930: str             x0, [SP]
    // 0xc4d934: r0 = _interpolate()
    //     0xc4d934: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d938: LeaveFrame
    //     0xc4d938: mov             SP, fp
    //     0xc4d93c: ldp             fp, lr, [SP], #0x10
    // 0xc4d940: ret
    //     0xc4d940: ret             
    // 0xc4d944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d944: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d948: b               #0xc4d908
  }
}
