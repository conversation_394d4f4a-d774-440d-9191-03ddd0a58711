// lib: , url: package:pdf/src/pdf/format/xref.dart

// class id: 1050792, size: 0x8
class :: {
}

// class id: 904, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _PdfXrefTable&PdfDataType&PdfDiagnostic extends PdfDataType
     with PdfDiagnostic {
}

// class id: 905, size: 0x18, field offset: 0x8
class PdfXrefTable extends _PdfXrefTable&PdfDataType&PdfDiagnostic {

  _ output(/* No info */) {
    // ** addr: 0xe7fe94, size: 0x3bc
    // 0xe7fe94: EnterFrame
    //     0xe7fe94: stp             fp, lr, [SP, #-0x10]!
    //     0xe7fe98: mov             fp, SP
    // 0xe7fe9c: AllocStack(0x60)
    //     0xe7fe9c: sub             SP, SP, #0x60
    // 0xe7fea0: SetupParameters(PdfXrefTable this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x28 */)
    //     0xe7fea0: mov             x4, x1
    //     0xe7fea4: mov             x0, x3
    //     0xe7fea8: stur            x3, [fp, #-0x28]
    //     0xe7feac: mov             x3, x2
    //     0xe7feb0: stur            x1, [fp, #-0x18]
    //     0xe7feb4: stur            x2, [fp, #-0x20]
    // 0xe7feb8: CheckStackOverflow
    //     0xe7feb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7febc: cmp             SP, x16
    //     0xe7fec0: b.ls            #0xe80240
    // 0xe7fec4: LoadField: r1 = r3->field_1f
    //     0xe7fec4: ldur            w1, [x3, #0x1f]
    // 0xe7fec8: DecompressPointer r1
    //     0xe7fec8: add             x1, x1, HEAP, lsl #32
    // 0xe7fecc: LoadField: r2 = r1->field_13
    //     0xe7fecc: ldur            w2, [x1, #0x13]
    // 0xe7fed0: DecompressPointer r2
    //     0xe7fed0: add             x2, x2, HEAP, lsl #32
    // 0xe7fed4: LoadField: r5 = r2->field_7
    //     0xe7fed4: ldur            x5, [x2, #7]
    // 0xe7fed8: stur            x5, [fp, #-0x10]
    // 0xe7fedc: cmp             x5, #0
    // 0xe7fee0: b.gt            #0xe7fef0
    // 0xe7fee4: r6 = "1.4"
    //     0xe7fee4: add             x6, PP, #0x36, lsl #12  ; [pp+0x365c0] "1.4"
    //     0xe7fee8: ldr             x6, [x6, #0x5c0]
    // 0xe7feec: b               #0xe7fef8
    // 0xe7fef0: r6 = "1.5"
    //     0xe7fef0: add             x6, PP, #0x36, lsl #12  ; [pp+0x365c8] "1.5"
    //     0xe7fef4: ldr             x6, [x6, #0x5c8]
    // 0xe7fef8: stur            x6, [fp, #-8]
    // 0xe7fefc: r1 = Null
    //     0xe7fefc: mov             x1, NULL
    // 0xe7ff00: r2 = 6
    //     0xe7ff00: movz            x2, #0x6
    // 0xe7ff04: r0 = AllocateArray()
    //     0xe7ff04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7ff08: r16 = "%PDF-"
    //     0xe7ff08: add             x16, PP, #0x36, lsl #12  ; [pp+0x365d0] "%PDF-"
    //     0xe7ff0c: ldr             x16, [x16, #0x5d0]
    // 0xe7ff10: StoreField: r0->field_f = r16
    //     0xe7ff10: stur            w16, [x0, #0xf]
    // 0xe7ff14: ldur            x1, [fp, #-8]
    // 0xe7ff18: StoreField: r0->field_13 = r1
    //     0xe7ff18: stur            w1, [x0, #0x13]
    // 0xe7ff1c: r16 = "\n"
    //     0xe7ff1c: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xe7ff20: ArrayStore: r0[0] = r16  ; List_4
    //     0xe7ff20: stur            w16, [x0, #0x17]
    // 0xe7ff24: str             x0, [SP]
    // 0xe7ff28: r0 = _interpolate()
    //     0xe7ff28: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7ff2c: ldur            x1, [fp, #-0x28]
    // 0xe7ff30: mov             x2, x0
    // 0xe7ff34: r0 = putString()
    //     0xe7ff34: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe7ff38: ldur            x1, [fp, #-0x28]
    // 0xe7ff3c: r2 = const [0x25, 0xc2, 0xa5, 0xc2, 0xb1, 0xc3, 0xab, 0xa]
    //     0xe7ff3c: add             x2, PP, #0x36, lsl #12  ; [pp+0x365d8] List<int>(8)
    //     0xe7ff40: ldr             x2, [x2, #0x5d8]
    // 0xe7ff44: r0 = putBytes()
    //     0xe7ff44: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe7ff48: ldur            x1, [fp, #-0x28]
    // 0xe7ff4c: r0 = putComment()
    //     0xe7ff4c: bl              #0xe817bc  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putComment
    // 0xe7ff50: r1 = <PdfXref>
    //     0xe7ff50: add             x1, PP, #0x36, lsl #12  ; [pp+0x365e0] TypeArguments: <PdfXref>
    //     0xe7ff54: ldr             x1, [x1, #0x5e0]
    // 0xe7ff58: r2 = 0
    //     0xe7ff58: movz            x2, #0
    // 0xe7ff5c: r0 = _GrowableList()
    //     0xe7ff5c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7ff60: mov             x2, x0
    // 0xe7ff64: ldur            x0, [fp, #-0x18]
    // 0xe7ff68: stur            x2, [fp, #-8]
    // 0xe7ff6c: LoadField: r1 = r0->field_b
    //     0xe7ff6c: ldur            w1, [x0, #0xb]
    // 0xe7ff70: DecompressPointer r1
    //     0xe7ff70: add             x1, x1, HEAP, lsl #32
    // 0xe7ff74: r0 = iterator()
    //     0xe7ff74: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0xe7ff78: stur            x0, [fp, #-0x38]
    // 0xe7ff7c: LoadField: r2 = r0->field_7
    //     0xe7ff7c: ldur            w2, [x0, #7]
    // 0xe7ff80: DecompressPointer r2
    //     0xe7ff80: add             x2, x2, HEAP, lsl #32
    // 0xe7ff84: stur            x2, [fp, #-0x30]
    // 0xe7ff88: ldur            x3, [fp, #-8]
    // 0xe7ff8c: ldur            x4, [fp, #-0x28]
    // 0xe7ff90: CheckStackOverflow
    //     0xe7ff90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ff94: cmp             SP, x16
    //     0xe7ff98: b.ls            #0xe80248
    // 0xe7ff9c: mov             x1, x0
    // 0xe7ffa0: r0 = moveNext()
    //     0xe7ffa0: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xe7ffa4: tbnz            w0, #4, #0xe80168
    // 0xe7ffa8: ldur            x3, [fp, #-0x38]
    // 0xe7ffac: LoadField: r4 = r3->field_33
    //     0xe7ffac: ldur            w4, [x3, #0x33]
    // 0xe7ffb0: DecompressPointer r4
    //     0xe7ffb0: add             x4, x4, HEAP, lsl #32
    // 0xe7ffb4: stur            x4, [fp, #-0x40]
    // 0xe7ffb8: cmp             w4, NULL
    // 0xe7ffbc: b.ne            #0xe7fff0
    // 0xe7ffc0: mov             x0, x4
    // 0xe7ffc4: ldur            x2, [fp, #-0x30]
    // 0xe7ffc8: r1 = Null
    //     0xe7ffc8: mov             x1, NULL
    // 0xe7ffcc: cmp             w2, NULL
    // 0xe7ffd0: b.eq            #0xe7fff0
    // 0xe7ffd4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe7ffd4: ldur            w4, [x2, #0x17]
    // 0xe7ffd8: DecompressPointer r4
    //     0xe7ffd8: add             x4, x4, HEAP, lsl #32
    // 0xe7ffdc: r8 = X0
    //     0xe7ffdc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe7ffe0: LoadField: r9 = r4->field_7
    //     0xe7ffe0: ldur            x9, [x4, #7]
    // 0xe7ffe4: r3 = Null
    //     0xe7ffe4: add             x3, PP, #0x36, lsl #12  ; [pp+0x365e8] Null
    //     0xe7ffe8: ldr             x3, [x3, #0x5e8]
    // 0xe7ffec: blr             x9
    // 0xe7fff0: ldur            x5, [fp, #-0x28]
    // 0xe7fff4: ldur            x4, [fp, #-8]
    // 0xe7fff8: ldur            x3, [fp, #-0x40]
    // 0xe7fffc: LoadField: r6 = r5->field_b
    //     0xe7fffc: ldur            x6, [x5, #0xb]
    // 0xe80000: stur            x6, [fp, #-0x58]
    // 0xe80004: LoadField: r7 = r3->field_b
    //     0xe80004: ldur            x7, [x3, #0xb]
    // 0xe80008: stur            x7, [fp, #-0x50]
    // 0xe8000c: r0 = BoxInt64Instr(r7)
    //     0xe8000c: sbfiz           x0, x7, #1, #0x1f
    //     0xe80010: cmp             x7, x0, asr #1
    //     0xe80014: b.eq            #0xe80020
    //     0xe80018: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8001c: stur            x7, [x0, #7]
    // 0xe80020: r1 = Null
    //     0xe80020: mov             x1, NULL
    // 0xe80024: r2 = 8
    //     0xe80024: movz            x2, #0x8
    // 0xe80028: stur            x0, [fp, #-0x48]
    // 0xe8002c: r0 = AllocateArray()
    //     0xe8002c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe80030: mov             x2, x0
    // 0xe80034: ldur            x0, [fp, #-0x48]
    // 0xe80038: StoreField: r2->field_f = r0
    //     0xe80038: stur            w0, [x2, #0xf]
    // 0xe8003c: r16 = " "
    //     0xe8003c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe80040: StoreField: r2->field_13 = r16
    //     0xe80040: stur            w16, [x2, #0x13]
    // 0xe80044: ldur            x3, [fp, #-0x40]
    // 0xe80048: LoadField: r4 = r3->field_13
    //     0xe80048: ldur            x4, [x3, #0x13]
    // 0xe8004c: r0 = BoxInt64Instr(r4)
    //     0xe8004c: sbfiz           x0, x4, #1, #0x1f
    //     0xe80050: cmp             x4, x0, asr #1
    //     0xe80054: b.eq            #0xe80060
    //     0xe80058: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8005c: stur            x4, [x0, #7]
    // 0xe80060: ArrayStore: r2[0] = r0  ; List_4
    //     0xe80060: stur            w0, [x2, #0x17]
    // 0xe80064: r16 = " obj\n"
    //     0xe80064: add             x16, PP, #0x36, lsl #12  ; [pp+0x365f8] " obj\n"
    //     0xe80068: ldr             x16, [x16, #0x5f8]
    // 0xe8006c: StoreField: r2->field_1b = r16
    //     0xe8006c: stur            w16, [x2, #0x1b]
    // 0xe80070: str             x2, [SP]
    // 0xe80074: r0 = _interpolate()
    //     0xe80074: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe80078: ldur            x1, [fp, #-0x28]
    // 0xe8007c: mov             x2, x0
    // 0xe80080: r0 = putString()
    //     0xe80080: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe80084: ldur            x1, [fp, #-0x40]
    // 0xe80088: r0 = LoadClassIdInstr(r1)
    //     0xe80088: ldur            x0, [x1, #-1]
    //     0xe8008c: ubfx            x0, x0, #0xc, #0x14
    // 0xe80090: ldur            x2, [fp, #-0x28]
    // 0xe80094: r0 = GDT[cid_x0 + 0xf283]()
    //     0xe80094: movz            x17, #0xf283
    //     0xe80098: add             lr, x0, x17
    //     0xe8009c: ldr             lr, [x21, lr, lsl #3]
    //     0xe800a0: blr             lr
    // 0xe800a4: ldur            x1, [fp, #-0x28]
    // 0xe800a8: r2 = "endobj\n"
    //     0xe800a8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36600] "endobj\n"
    //     0xe800ac: ldr             x2, [x2, #0x600]
    // 0xe800b0: r0 = putString()
    //     0xe800b0: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe800b4: r0 = PdfXref()
    //     0xe800b4: bl              #0xe817b0  ; AllocatePdfXrefStub -> PdfXref (size=0x28)
    // 0xe800b8: mov             x2, x0
    // 0xe800bc: ldur            x0, [fp, #-0x58]
    // 0xe800c0: stur            x2, [fp, #-0x40]
    // 0xe800c4: ArrayStore: r2[0] = r0  ; List_8
    //     0xe800c4: stur            x0, [x2, #0x17]
    // 0xe800c8: r0 = Instance_PdfCrossRefEntryType
    //     0xe800c8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] Obj!PdfCrossRefEntryType@e2f021
    //     0xe800cc: ldr             x0, [x0, #0x608]
    // 0xe800d0: StoreField: r2->field_23 = r0
    //     0xe800d0: stur            w0, [x2, #0x23]
    // 0xe800d4: ldur            x1, [fp, #-0x50]
    // 0xe800d8: StoreField: r2->field_7 = r1
    //     0xe800d8: stur            x1, [x2, #7]
    // 0xe800dc: StoreField: r2->field_f = rZR
    //     0xe800dc: stur            xzr, [x2, #0xf]
    // 0xe800e0: ldur            x3, [fp, #-8]
    // 0xe800e4: LoadField: r1 = r3->field_b
    //     0xe800e4: ldur            w1, [x3, #0xb]
    // 0xe800e8: LoadField: r4 = r3->field_f
    //     0xe800e8: ldur            w4, [x3, #0xf]
    // 0xe800ec: DecompressPointer r4
    //     0xe800ec: add             x4, x4, HEAP, lsl #32
    // 0xe800f0: LoadField: r5 = r4->field_b
    //     0xe800f0: ldur            w5, [x4, #0xb]
    // 0xe800f4: r4 = LoadInt32Instr(r1)
    //     0xe800f4: sbfx            x4, x1, #1, #0x1f
    // 0xe800f8: stur            x4, [fp, #-0x50]
    // 0xe800fc: r1 = LoadInt32Instr(r5)
    //     0xe800fc: sbfx            x1, x5, #1, #0x1f
    // 0xe80100: cmp             x4, x1
    // 0xe80104: b.ne            #0xe80110
    // 0xe80108: mov             x1, x3
    // 0xe8010c: r0 = _growToNextCapacity()
    //     0xe8010c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe80110: ldur            x5, [fp, #-8]
    // 0xe80114: ldur            x2, [fp, #-0x50]
    // 0xe80118: add             x0, x2, #1
    // 0xe8011c: lsl             x1, x0, #1
    // 0xe80120: StoreField: r5->field_b = r1
    //     0xe80120: stur            w1, [x5, #0xb]
    // 0xe80124: LoadField: r1 = r5->field_f
    //     0xe80124: ldur            w1, [x5, #0xf]
    // 0xe80128: DecompressPointer r1
    //     0xe80128: add             x1, x1, HEAP, lsl #32
    // 0xe8012c: ldur            x0, [fp, #-0x40]
    // 0xe80130: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe80130: add             x25, x1, x2, lsl #2
    //     0xe80134: add             x25, x25, #0xf
    //     0xe80138: str             w0, [x25]
    //     0xe8013c: tbz             w0, #0, #0xe80158
    //     0xe80140: ldurb           w16, [x1, #-1]
    //     0xe80144: ldurb           w17, [x0, #-1]
    //     0xe80148: and             x16, x17, x16, lsr #2
    //     0xe8014c: tst             x16, HEAP, lsr #32
    //     0xe80150: b.eq            #0xe80158
    //     0xe80154: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe80158: mov             x3, x5
    // 0xe8015c: ldur            x0, [fp, #-0x38]
    // 0xe80160: ldur            x2, [fp, #-0x30]
    // 0xe80164: b               #0xe7ff8c
    // 0xe80168: ldur            x0, [fp, #-0x18]
    // 0xe8016c: ldur            x2, [fp, #-0x10]
    // 0xe80170: ldur            x5, [fp, #-8]
    // 0xe80174: LoadField: r3 = r0->field_7
    //     0xe80174: ldur            w3, [x0, #7]
    // 0xe80178: DecompressPointer r3
    //     0xe80178: add             x3, x3, HEAP, lsl #32
    // 0xe8017c: ldur            x1, [fp, #-0x20]
    // 0xe80180: stur            x3, [fp, #-0x30]
    // 0xe80184: r0 = ref()
    //     0xe80184: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0xe80188: ldur            x1, [fp, #-0x30]
    // 0xe8018c: mov             x3, x0
    // 0xe80190: r2 = "/Root"
    //     0xe80190: add             x2, PP, #0x36, lsl #12  ; [pp+0x36610] "/Root"
    //     0xe80194: ldr             x2, [x2, #0x610]
    // 0xe80198: r0 = []=()
    //     0xe80198: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe8019c: ldur            x0, [fp, #-0x10]
    // 0xe801a0: cmp             x0, #0
    // 0xe801a4: b.gt            #0xe801c0
    // 0xe801a8: ldur            x1, [fp, #-0x18]
    // 0xe801ac: ldur            x2, [fp, #-0x20]
    // 0xe801b0: ldur            x3, [fp, #-0x28]
    // 0xe801b4: ldur            x5, [fp, #-8]
    // 0xe801b8: r0 = _outputLegacy()
    //     0xe801b8: bl              #0xe8105c  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::_outputLegacy
    // 0xe801bc: b               #0xe801d4
    // 0xe801c0: ldur            x1, [fp, #-0x18]
    // 0xe801c4: ldur            x2, [fp, #-0x20]
    // 0xe801c8: ldur            x3, [fp, #-0x28]
    // 0xe801cc: ldur            x5, [fp, #-8]
    // 0xe801d0: r0 = _outputCompressed()
    //     0xe801d0: bl              #0xe80344  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::_outputCompressed
    // 0xe801d4: stur            x0, [fp, #-0x10]
    // 0xe801d8: r1 = Null
    //     0xe801d8: mov             x1, NULL
    // 0xe801dc: r2 = 6
    //     0xe801dc: movz            x2, #0x6
    // 0xe801e0: r0 = AllocateArray()
    //     0xe801e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe801e4: mov             x2, x0
    // 0xe801e8: r16 = "startxref\n"
    //     0xe801e8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36618] "startxref\n"
    //     0xe801ec: ldr             x16, [x16, #0x618]
    // 0xe801f0: StoreField: r2->field_f = r16
    //     0xe801f0: stur            w16, [x2, #0xf]
    // 0xe801f4: ldur            x3, [fp, #-0x10]
    // 0xe801f8: r0 = BoxInt64Instr(r3)
    //     0xe801f8: sbfiz           x0, x3, #1, #0x1f
    //     0xe801fc: cmp             x3, x0, asr #1
    //     0xe80200: b.eq            #0xe8020c
    //     0xe80204: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80208: stur            x3, [x0, #7]
    // 0xe8020c: StoreField: r2->field_13 = r0
    //     0xe8020c: stur            w0, [x2, #0x13]
    // 0xe80210: r16 = "\n%%EOF\n"
    //     0xe80210: add             x16, PP, #0x36, lsl #12  ; [pp+0x36620] "\n%%EOF\n"
    //     0xe80214: ldr             x16, [x16, #0x620]
    // 0xe80218: ArrayStore: r2[0] = r16  ; List_4
    //     0xe80218: stur            w16, [x2, #0x17]
    // 0xe8021c: str             x2, [SP]
    // 0xe80220: r0 = _interpolate()
    //     0xe80220: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe80224: ldur            x1, [fp, #-0x28]
    // 0xe80228: mov             x2, x0
    // 0xe8022c: r0 = putString()
    //     0xe8022c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe80230: r0 = Null
    //     0xe80230: mov             x0, NULL
    // 0xe80234: LeaveFrame
    //     0xe80234: mov             SP, fp
    //     0xe80238: ldp             fp, lr, [SP], #0x10
    // 0xe8023c: ret
    //     0xe8023c: ret             
    // 0xe80240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80244: b               #0xe7fec4
    // 0xe80248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80248: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8024c: b               #0xe7ff9c
  }
  _ _outputCompressed(/* No info */) {
    // ** addr: 0xe80344, size: 0xa04
    // 0xe80344: EnterFrame
    //     0xe80344: stp             fp, lr, [SP, #-0x10]!
    //     0xe80348: mov             fp, SP
    // 0xe8034c: AllocStack(0x88)
    //     0xe8034c: sub             SP, SP, #0x88
    // 0xe80350: SetupParameters(PdfXrefTable this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r0, fp-0x28 */)
    //     0xe80350: mov             x0, x5
    //     0xe80354: stur            x5, [fp, #-0x28]
    //     0xe80358: mov             x5, x1
    //     0xe8035c: mov             x4, x2
    //     0xe80360: stur            x1, [fp, #-0x10]
    //     0xe80364: stur            x2, [fp, #-0x18]
    //     0xe80368: stur            x3, [fp, #-0x20]
    // 0xe8036c: CheckStackOverflow
    //     0xe8036c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80370: cmp             SP, x16
    //     0xe80374: b.ls            #0xe80ce4
    // 0xe80378: LoadField: r6 = r3->field_b
    //     0xe80378: ldur            x6, [x3, #0xb]
    // 0xe8037c: stur            x6, [fp, #-8]
    // 0xe80380: r1 = Function '<anonymous closure>':.
    //     0xe80380: add             x1, PP, #0x36, lsl #12  ; [pp+0x36628] AnonymousClosure: (0xafb240), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xe80384: ldr             x1, [x1, #0x628]
    // 0xe80388: r2 = Null
    //     0xe80388: mov             x2, NULL
    // 0xe8038c: r0 = AllocateClosure()
    //     0xe8038c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe80390: str             x0, [SP]
    // 0xe80394: ldur            x1, [fp, #-0x28]
    // 0xe80398: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe80398: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe8039c: r0 = sort()
    //     0xe8039c: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xe803a0: ldur            x0, [fp, #-0x10]
    // 0xe803a4: LoadField: r2 = r0->field_f
    //     0xe803a4: ldur            x2, [x0, #0xf]
    // 0xe803a8: ldur            x1, [fp, #-0x28]
    // 0xe803ac: stur            x2, [fp, #-0x30]
    // 0xe803b0: r0 = last()
    //     0xe803b0: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xe803b4: LoadField: r1 = r0->field_7
    //     0xe803b4: ldur            x1, [x0, #7]
    // 0xe803b8: add             x2, x1, #1
    // 0xe803bc: ldur            x3, [fp, #-0x30]
    // 0xe803c0: cmp             x3, x2
    // 0xe803c4: b.le            #0xe803e4
    // 0xe803c8: r0 = BoxInt64Instr(r3)
    //     0xe803c8: sbfiz           x0, x3, #1, #0x1f
    //     0xe803cc: cmp             x3, x0, asr #1
    //     0xe803d0: b.eq            #0xe803dc
    //     0xe803d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe803d8: stur            x3, [x0, #7]
    // 0xe803dc: mov             x2, x0
    // 0xe803e0: b               #0xe80518
    // 0xe803e4: cmp             x3, x2
    // 0xe803e8: b.ge            #0xe80408
    // 0xe803ec: r0 = BoxInt64Instr(r2)
    //     0xe803ec: sbfiz           x0, x2, #1, #0x1f
    //     0xe803f0: cmp             x2, x0, asr #1
    //     0xe803f4: b.eq            #0xe80400
    //     0xe803f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe803fc: stur            x2, [x0, #7]
    // 0xe80400: mov             x2, x0
    // 0xe80404: b               #0xe80518
    // 0xe80408: r0 = BoxInt64Instr(r2)
    //     0xe80408: sbfiz           x0, x2, #1, #0x1f
    //     0xe8040c: cmp             x2, x0, asr #1
    //     0xe80410: b.eq            #0xe8041c
    //     0xe80414: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80418: stur            x2, [x0, #7]
    // 0xe8041c: mov             x4, x0
    // 0xe80420: stur            x4, [fp, #-0x38]
    // 0xe80424: r0 = 60
    //     0xe80424: movz            x0, #0x3c
    // 0xe80428: branchIfSmi(r4, 0xe80434)
    //     0xe80428: tbz             w4, #0, #0xe80434
    // 0xe8042c: r0 = LoadClassIdInstr(r4)
    //     0xe8042c: ldur            x0, [x4, #-1]
    //     0xe80430: ubfx            x0, x0, #0xc, #0x14
    // 0xe80434: cmp             x0, #0x3e
    // 0xe80438: b.ne            #0xe804b4
    // 0xe8043c: r0 = BoxInt64Instr(r3)
    //     0xe8043c: sbfiz           x0, x3, #1, #0x1f
    //     0xe80440: cmp             x3, x0, asr #1
    //     0xe80444: b.eq            #0xe80450
    //     0xe80448: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8044c: stur            x3, [x0, #7]
    // 0xe80450: r1 = 60
    //     0xe80450: movz            x1, #0x3c
    // 0xe80454: branchIfSmi(r0, 0xe80460)
    //     0xe80454: tbz             w0, #0, #0xe80460
    // 0xe80458: r1 = LoadClassIdInstr(r0)
    //     0xe80458: ldur            x1, [x0, #-1]
    //     0xe8045c: ubfx            x1, x1, #0xc, #0x14
    // 0xe80460: cmp             x1, #0x3e
    // 0xe80464: b.ne            #0xe80498
    // 0xe80468: d0 = 0.000000
    //     0xe80468: eor             v0.16b, v0.16b, v0.16b
    // 0xe8046c: scvtf           d1, x3
    // 0xe80470: fcmp            d1, d0
    // 0xe80474: b.ne            #0xe80498
    // 0xe80478: add             x4, x3, x2
    // 0xe8047c: r0 = BoxInt64Instr(r4)
    //     0xe8047c: sbfiz           x0, x4, #1, #0x1f
    //     0xe80480: cmp             x4, x0, asr #1
    //     0xe80484: b.eq            #0xe80490
    //     0xe80488: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8048c: stur            x4, [x0, #7]
    // 0xe80490: mov             x2, x0
    // 0xe80494: b               #0xe80518
    // 0xe80498: LoadField: d0 = r4->field_7
    //     0xe80498: ldur            d0, [x4, #7]
    // 0xe8049c: fcmp            d0, d0
    // 0xe804a0: b.vc            #0xe804ac
    // 0xe804a4: mov             x2, x4
    // 0xe804a8: b               #0xe80518
    // 0xe804ac: mov             x2, x0
    // 0xe804b0: b               #0xe80518
    // 0xe804b4: cbnz            x2, #0xe804fc
    // 0xe804b8: r0 = BoxInt64Instr(r3)
    //     0xe804b8: sbfiz           x0, x3, #1, #0x1f
    //     0xe804bc: cmp             x3, x0, asr #1
    //     0xe804c0: b.eq            #0xe804cc
    //     0xe804c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe804c8: stur            x3, [x0, #7]
    // 0xe804cc: r1 = 60
    //     0xe804cc: movz            x1, #0x3c
    // 0xe804d0: branchIfSmi(r0, 0xe804dc)
    //     0xe804d0: tbz             w0, #0, #0xe804dc
    // 0xe804d4: r1 = LoadClassIdInstr(r0)
    //     0xe804d4: ldur            x1, [x0, #-1]
    //     0xe804d8: ubfx            x1, x1, #0xc, #0x14
    // 0xe804dc: str             x0, [SP]
    // 0xe804e0: mov             x0, x1
    // 0xe804e4: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xe804e4: sub             lr, x0, #0xfb8
    //     0xe804e8: ldr             lr, [x21, lr, lsl #3]
    //     0xe804ec: blr             lr
    // 0xe804f0: tbnz            w0, #4, #0xe804fc
    // 0xe804f4: ldur            x2, [fp, #-0x38]
    // 0xe804f8: b               #0xe80518
    // 0xe804fc: ldur            x2, [fp, #-0x30]
    // 0xe80500: r0 = BoxInt64Instr(r2)
    //     0xe80500: sbfiz           x0, x2, #1, #0x1f
    //     0xe80504: cmp             x2, x0, asr #1
    //     0xe80508: b.eq            #0xe80514
    //     0xe8050c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80510: stur            x2, [x0, #7]
    // 0xe80514: mov             x2, x0
    // 0xe80518: ldur            x1, [fp, #-0x28]
    // 0xe8051c: ldur            x0, [fp, #-8]
    // 0xe80520: r3 = LoadInt32Instr(r2)
    //     0xe80520: sbfx            x3, x2, #1, #0x1f
    //     0xe80524: tbz             w2, #0, #0xe8052c
    //     0xe80528: ldur            x3, [x2, #7]
    // 0xe8052c: stur            x3, [fp, #-0x40]
    // 0xe80530: add             x2, x3, #1
    // 0xe80534: stur            x2, [fp, #-0x30]
    // 0xe80538: r0 = PdfXref()
    //     0xe80538: bl              #0xe817b0  ; AllocatePdfXrefStub -> PdfXref (size=0x28)
    // 0xe8053c: mov             x2, x0
    // 0xe80540: ldur            x0, [fp, #-8]
    // 0xe80544: stur            x2, [fp, #-0x38]
    // 0xe80548: ArrayStore: r2[0] = r0  ; List_8
    //     0xe80548: stur            x0, [x2, #0x17]
    // 0xe8054c: r1 = Instance_PdfCrossRefEntryType
    //     0xe8054c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36608] Obj!PdfCrossRefEntryType@e2f021
    //     0xe80550: ldr             x1, [x1, #0x608]
    // 0xe80554: StoreField: r2->field_23 = r1
    //     0xe80554: stur            w1, [x2, #0x23]
    // 0xe80558: ldur            x3, [fp, #-0x40]
    // 0xe8055c: StoreField: r2->field_7 = r3
    //     0xe8055c: stur            x3, [x2, #7]
    // 0xe80560: StoreField: r2->field_f = rZR
    //     0xe80560: stur            xzr, [x2, #0xf]
    // 0xe80564: ldur            x4, [fp, #-0x28]
    // 0xe80568: LoadField: r1 = r4->field_b
    //     0xe80568: ldur            w1, [x4, #0xb]
    // 0xe8056c: LoadField: r5 = r4->field_f
    //     0xe8056c: ldur            w5, [x4, #0xf]
    // 0xe80570: DecompressPointer r5
    //     0xe80570: add             x5, x5, HEAP, lsl #32
    // 0xe80574: LoadField: r6 = r5->field_b
    //     0xe80574: ldur            w6, [x5, #0xb]
    // 0xe80578: r5 = LoadInt32Instr(r1)
    //     0xe80578: sbfx            x5, x1, #1, #0x1f
    // 0xe8057c: stur            x5, [fp, #-0x48]
    // 0xe80580: r1 = LoadInt32Instr(r6)
    //     0xe80580: sbfx            x1, x6, #1, #0x1f
    // 0xe80584: cmp             x5, x1
    // 0xe80588: b.ne            #0xe80594
    // 0xe8058c: mov             x1, x4
    // 0xe80590: r0 = _growToNextCapacity()
    //     0xe80590: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe80594: ldur            x3, [fp, #-0x10]
    // 0xe80598: ldur            x4, [fp, #-0x28]
    // 0xe8059c: ldur            x5, [fp, #-0x30]
    // 0xe805a0: ldur            x2, [fp, #-0x48]
    // 0xe805a4: add             x0, x2, #1
    // 0xe805a8: lsl             x1, x0, #1
    // 0xe805ac: StoreField: r4->field_b = r1
    //     0xe805ac: stur            w1, [x4, #0xb]
    // 0xe805b0: LoadField: r1 = r4->field_f
    //     0xe805b0: ldur            w1, [x4, #0xf]
    // 0xe805b4: DecompressPointer r1
    //     0xe805b4: add             x1, x1, HEAP, lsl #32
    // 0xe805b8: ldur            x0, [fp, #-0x38]
    // 0xe805bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe805bc: add             x25, x1, x2, lsl #2
    //     0xe805c0: add             x25, x25, #0xf
    //     0xe805c4: str             w0, [x25]
    //     0xe805c8: tbz             w0, #0, #0xe805e4
    //     0xe805cc: ldurb           w16, [x1, #-1]
    //     0xe805d0: ldurb           w17, [x0, #-1]
    //     0xe805d4: and             x16, x17, x16, lsr #2
    //     0xe805d8: tst             x16, HEAP, lsr #32
    //     0xe805dc: b.eq            #0xe805e4
    //     0xe805e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe805e4: LoadField: r0 = r3->field_7
    //     0xe805e4: ldur            w0, [x3, #7]
    // 0xe805e8: DecompressPointer r0
    //     0xe805e8: add             x0, x0, HEAP, lsl #32
    // 0xe805ec: mov             x1, x0
    // 0xe805f0: stur            x0, [fp, #-0x38]
    // 0xe805f4: r2 = "/Type"
    //     0xe805f4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe805f8: ldr             x2, [x2, #0x630]
    // 0xe805fc: r3 = Instance_PdfName
    //     0xe805fc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36638] Obj!PdfName@e0c911
    //     0xe80600: ldr             x3, [x3, #0x638]
    // 0xe80604: r0 = []=()
    //     0xe80604: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe80608: ldur            x2, [fp, #-0x30]
    // 0xe8060c: r0 = BoxInt64Instr(r2)
    //     0xe8060c: sbfiz           x0, x2, #1, #0x1f
    //     0xe80610: cmp             x2, x0, asr #1
    //     0xe80614: b.eq            #0xe80620
    //     0xe80618: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8061c: stur            x2, [x0, #7]
    // 0xe80620: stur            x0, [fp, #-0x10]
    // 0xe80624: r0 = PdfNum()
    //     0xe80624: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe80628: mov             x1, x0
    // 0xe8062c: ldur            x0, [fp, #-0x10]
    // 0xe80630: StoreField: r1->field_7 = r0
    //     0xe80630: stur            w0, [x1, #7]
    // 0xe80634: mov             x3, x1
    // 0xe80638: ldur            x1, [fp, #-0x38]
    // 0xe8063c: r2 = "/Size"
    //     0xe8063c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36640] "/Size"
    //     0xe80640: ldr             x2, [x2, #0x640]
    // 0xe80644: r0 = []=()
    //     0xe80644: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe80648: r1 = <int>
    //     0xe80648: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8064c: r2 = 0
    //     0xe8064c: movz            x2, #0
    // 0xe80650: r0 = _GrowableList()
    //     0xe80650: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe80654: stur            x0, [fp, #-0x10]
    // 0xe80658: LoadField: r1 = r0->field_b
    //     0xe80658: ldur            w1, [x0, #0xb]
    // 0xe8065c: LoadField: r2 = r0->field_f
    //     0xe8065c: ldur            w2, [x0, #0xf]
    // 0xe80660: DecompressPointer r2
    //     0xe80660: add             x2, x2, HEAP, lsl #32
    // 0xe80664: LoadField: r3 = r2->field_b
    //     0xe80664: ldur            w3, [x2, #0xb]
    // 0xe80668: r2 = LoadInt32Instr(r1)
    //     0xe80668: sbfx            x2, x1, #1, #0x1f
    // 0xe8066c: stur            x2, [fp, #-0x48]
    // 0xe80670: r1 = LoadInt32Instr(r3)
    //     0xe80670: sbfx            x1, x3, #1, #0x1f
    // 0xe80674: cmp             x2, x1
    // 0xe80678: b.ne            #0xe80684
    // 0xe8067c: mov             x1, x0
    // 0xe80680: r0 = _growToNextCapacity()
    //     0xe80680: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe80684: ldur            x2, [fp, #-0x28]
    // 0xe80688: ldur            x0, [fp, #-0x10]
    // 0xe8068c: ldur            x1, [fp, #-0x48]
    // 0xe80690: add             x3, x1, #1
    // 0xe80694: lsl             x4, x3, #1
    // 0xe80698: StoreField: r0->field_b = r4
    //     0xe80698: stur            w4, [x0, #0xb]
    // 0xe8069c: LoadField: r4 = r0->field_f
    //     0xe8069c: ldur            w4, [x0, #0xf]
    // 0xe806a0: DecompressPointer r4
    //     0xe806a0: add             x4, x4, HEAP, lsl #32
    // 0xe806a4: ArrayStore: r4[r1] = rZR  ; Unknown_4
    //     0xe806a4: add             x5, x4, x1, lsl #2
    //     0xe806a8: stur            wzr, [x5, #0xf]
    // 0xe806ac: LoadField: r1 = r2->field_b
    //     0xe806ac: ldur            w1, [x2, #0xb]
    // 0xe806b0: r5 = LoadInt32Instr(r1)
    //     0xe806b0: sbfx            x5, x1, #1, #0x1f
    // 0xe806b4: stur            x5, [fp, #-0x68]
    // 0xe806b8: mov             x16, x4
    // 0xe806bc: mov             x4, x3
    // 0xe806c0: mov             x3, x16
    // 0xe806c4: r7 = 0
    //     0xe806c4: movz            x7, #0
    // 0xe806c8: r6 = 0
    //     0xe806c8: movz            x6, #0
    // 0xe806cc: r1 = 0
    //     0xe806cc: movz            x1, #0
    // 0xe806d0: stur            x4, [fp, #-0x60]
    // 0xe806d4: CheckStackOverflow
    //     0xe806d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe806d8: cmp             SP, x16
    //     0xe806dc: b.ls            #0xe80cec
    // 0xe806e0: LoadField: r8 = r2->field_b
    //     0xe806e0: ldur            w8, [x2, #0xb]
    // 0xe806e4: r9 = LoadInt32Instr(r8)
    //     0xe806e4: sbfx            x9, x8, #1, #0x1f
    // 0xe806e8: cmp             x5, x9
    // 0xe806ec: b.ne            #0xe80cc4
    // 0xe806f0: cmp             x1, x9
    // 0xe806f4: b.ge            #0xe80894
    // 0xe806f8: LoadField: r8 = r2->field_f
    //     0xe806f8: ldur            w8, [x2, #0xf]
    // 0xe806fc: DecompressPointer r8
    //     0xe806fc: add             x8, x8, HEAP, lsl #32
    // 0xe80700: ArrayLoad: r9 = r8[r1]  ; Unknown_4
    //     0xe80700: add             x16, x8, x1, lsl #2
    //     0xe80704: ldur            w9, [x16, #0xf]
    // 0xe80708: DecompressPointer r9
    //     0xe80708: add             x9, x9, HEAP, lsl #32
    // 0xe8070c: add             x8, x1, #1
    // 0xe80710: stur            x8, [fp, #-0x58]
    // 0xe80714: LoadField: r10 = r9->field_7
    //     0xe80714: ldur            x10, [x9, #7]
    // 0xe80718: stur            x10, [fp, #-0x50]
    // 0xe8071c: add             x1, x6, #1
    // 0xe80720: cmp             x10, x1
    // 0xe80724: b.eq            #0xe80874
    // 0xe80728: sub             x1, x6, x7
    // 0xe8072c: add             x6, x1, #1
    // 0xe80730: stur            x6, [fp, #-0x48]
    // 0xe80734: LoadField: r1 = r3->field_b
    //     0xe80734: ldur            w1, [x3, #0xb]
    // 0xe80738: r3 = LoadInt32Instr(r1)
    //     0xe80738: sbfx            x3, x1, #1, #0x1f
    // 0xe8073c: cmp             x4, x3
    // 0xe80740: b.ne            #0xe8074c
    // 0xe80744: mov             x1, x0
    // 0xe80748: r0 = _growToNextCapacity()
    //     0xe80748: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8074c: ldur            x2, [fp, #-0x10]
    // 0xe80750: ldur            x3, [fp, #-0x48]
    // 0xe80754: ldur            x4, [fp, #-0x60]
    // 0xe80758: add             x5, x4, #1
    // 0xe8075c: stur            x5, [fp, #-0x70]
    // 0xe80760: r0 = BoxInt64Instr(r5)
    //     0xe80760: sbfiz           x0, x5, #1, #0x1f
    //     0xe80764: cmp             x5, x0, asr #1
    //     0xe80768: b.eq            #0xe80774
    //     0xe8076c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80770: stur            x5, [x0, #7]
    // 0xe80774: StoreField: r2->field_b = r0
    //     0xe80774: stur            w0, [x2, #0xb]
    // 0xe80778: mov             x0, x5
    // 0xe8077c: mov             x1, x4
    // 0xe80780: cmp             x1, x0
    // 0xe80784: b.hs            #0xe80cf4
    // 0xe80788: LoadField: r6 = r2->field_f
    //     0xe80788: ldur            w6, [x2, #0xf]
    // 0xe8078c: DecompressPointer r6
    //     0xe8078c: add             x6, x6, HEAP, lsl #32
    // 0xe80790: r0 = BoxInt64Instr(r3)
    //     0xe80790: sbfiz           x0, x3, #1, #0x1f
    //     0xe80794: cmp             x3, x0, asr #1
    //     0xe80798: b.eq            #0xe807a4
    //     0xe8079c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe807a0: stur            x3, [x0, #7]
    // 0xe807a4: mov             x1, x6
    // 0xe807a8: ArrayStore: r1[r4] = r0  ; List_4
    //     0xe807a8: add             x25, x1, x4, lsl #2
    //     0xe807ac: add             x25, x25, #0xf
    //     0xe807b0: str             w0, [x25]
    //     0xe807b4: tbz             w0, #0, #0xe807d0
    //     0xe807b8: ldurb           w16, [x1, #-1]
    //     0xe807bc: ldurb           w17, [x0, #-1]
    //     0xe807c0: and             x16, x17, x16, lsr #2
    //     0xe807c4: tst             x16, HEAP, lsr #32
    //     0xe807c8: b.eq            #0xe807d0
    //     0xe807cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe807d0: LoadField: r0 = r6->field_b
    //     0xe807d0: ldur            w0, [x6, #0xb]
    // 0xe807d4: r1 = LoadInt32Instr(r0)
    //     0xe807d4: sbfx            x1, x0, #1, #0x1f
    // 0xe807d8: cmp             x5, x1
    // 0xe807dc: b.ne            #0xe807e8
    // 0xe807e0: mov             x1, x2
    // 0xe807e4: r0 = _growToNextCapacity()
    //     0xe807e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe807e8: ldur            x2, [fp, #-0x10]
    // 0xe807ec: ldur            x5, [fp, #-0x50]
    // 0xe807f0: ldur            x3, [fp, #-0x70]
    // 0xe807f4: add             x4, x3, #1
    // 0xe807f8: r0 = BoxInt64Instr(r4)
    //     0xe807f8: sbfiz           x0, x4, #1, #0x1f
    //     0xe807fc: cmp             x4, x0, asr #1
    //     0xe80800: b.eq            #0xe8080c
    //     0xe80804: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80808: stur            x4, [x0, #7]
    // 0xe8080c: StoreField: r2->field_b = r0
    //     0xe8080c: stur            w0, [x2, #0xb]
    // 0xe80810: mov             x0, x4
    // 0xe80814: mov             x1, x3
    // 0xe80818: cmp             x1, x0
    // 0xe8081c: b.hs            #0xe80cf8
    // 0xe80820: LoadField: r8 = r2->field_f
    //     0xe80820: ldur            w8, [x2, #0xf]
    // 0xe80824: DecompressPointer r8
    //     0xe80824: add             x8, x8, HEAP, lsl #32
    // 0xe80828: r0 = BoxInt64Instr(r5)
    //     0xe80828: sbfiz           x0, x5, #1, #0x1f
    //     0xe8082c: cmp             x5, x0, asr #1
    //     0xe80830: b.eq            #0xe8083c
    //     0xe80834: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80838: stur            x5, [x0, #7]
    // 0xe8083c: mov             x1, x8
    // 0xe80840: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe80840: add             x25, x1, x3, lsl #2
    //     0xe80844: add             x25, x25, #0xf
    //     0xe80848: str             w0, [x25]
    //     0xe8084c: tbz             w0, #0, #0xe80868
    //     0xe80850: ldurb           w16, [x1, #-1]
    //     0xe80854: ldurb           w17, [x0, #-1]
    //     0xe80858: and             x16, x17, x16, lsr #2
    //     0xe8085c: tst             x16, HEAP, lsr #32
    //     0xe80860: b.eq            #0xe80868
    //     0xe80864: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe80868: mov             x7, x5
    // 0xe8086c: mov             x3, x8
    // 0xe80870: b               #0xe8087c
    // 0xe80874: mov             x2, x0
    // 0xe80878: mov             x5, x10
    // 0xe8087c: mov             x6, x5
    // 0xe80880: ldur            x1, [fp, #-0x58]
    // 0xe80884: mov             x0, x2
    // 0xe80888: ldur            x2, [fp, #-0x28]
    // 0xe8088c: ldur            x5, [fp, #-0x68]
    // 0xe80890: b               #0xe806d0
    // 0xe80894: mov             x2, x0
    // 0xe80898: sub             x0, x6, x7
    // 0xe8089c: add             x5, x0, #1
    // 0xe808a0: stur            x5, [fp, #-0x48]
    // 0xe808a4: LoadField: r0 = r3->field_b
    //     0xe808a4: ldur            w0, [x3, #0xb]
    // 0xe808a8: r1 = LoadInt32Instr(r0)
    //     0xe808a8: sbfx            x1, x0, #1, #0x1f
    // 0xe808ac: cmp             x4, x1
    // 0xe808b0: b.ne            #0xe808bc
    // 0xe808b4: mov             x1, x2
    // 0xe808b8: r0 = _growToNextCapacity()
    //     0xe808b8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe808bc: ldur            x3, [fp, #-0x10]
    // 0xe808c0: ldur            x4, [fp, #-0x48]
    // 0xe808c4: ldur            x2, [fp, #-0x60]
    // 0xe808c8: add             x5, x2, #1
    // 0xe808cc: r0 = BoxInt64Instr(r5)
    //     0xe808cc: sbfiz           x0, x5, #1, #0x1f
    //     0xe808d0: cmp             x5, x0, asr #1
    //     0xe808d4: b.eq            #0xe808e0
    //     0xe808d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe808dc: stur            x5, [x0, #7]
    // 0xe808e0: StoreField: r3->field_b = r0
    //     0xe808e0: stur            w0, [x3, #0xb]
    // 0xe808e4: mov             x0, x5
    // 0xe808e8: mov             x1, x2
    // 0xe808ec: cmp             x1, x0
    // 0xe808f0: b.hs            #0xe80cfc
    // 0xe808f4: LoadField: r6 = r3->field_f
    //     0xe808f4: ldur            w6, [x3, #0xf]
    // 0xe808f8: DecompressPointer r6
    //     0xe808f8: add             x6, x6, HEAP, lsl #32
    // 0xe808fc: r0 = BoxInt64Instr(r4)
    //     0xe808fc: sbfiz           x0, x4, #1, #0x1f
    //     0xe80900: cmp             x4, x0, asr #1
    //     0xe80904: b.eq            #0xe80910
    //     0xe80908: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8090c: stur            x4, [x0, #7]
    // 0xe80910: mov             x1, x6
    // 0xe80914: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe80914: add             x25, x1, x2, lsl #2
    //     0xe80918: add             x25, x25, #0xf
    //     0xe8091c: str             w0, [x25]
    //     0xe80920: tbz             w0, #0, #0xe8093c
    //     0xe80924: ldurb           w16, [x1, #-1]
    //     0xe80928: ldurb           w17, [x0, #-1]
    //     0xe8092c: and             x16, x17, x16, lsr #2
    //     0xe80930: tst             x16, HEAP, lsr #32
    //     0xe80934: b.eq            #0xe8093c
    //     0xe80938: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8093c: cmp             x5, #2
    // 0xe80940: b.ne            #0xe80970
    // 0xe80944: LoadField: r0 = r6->field_f
    //     0xe80944: ldur            w0, [x6, #0xf]
    // 0xe80948: DecompressPointer r0
    //     0xe80948: add             x0, x0, HEAP, lsl #32
    // 0xe8094c: cbnz            w0, #0xe80970
    // 0xe80950: ldur            x0, [fp, #-0x30]
    // 0xe80954: LoadField: r1 = r6->field_13
    //     0xe80954: ldur            w1, [x6, #0x13]
    // 0xe80958: DecompressPointer r1
    //     0xe80958: add             x1, x1, HEAP, lsl #32
    // 0xe8095c: r2 = LoadInt32Instr(r1)
    //     0xe8095c: sbfx            x2, x1, #1, #0x1f
    //     0xe80960: tbz             w1, #0, #0xe80968
    //     0xe80964: ldur            x2, [x1, #7]
    // 0xe80968: cmp             x2, x0
    // 0xe8096c: b.eq            #0xe8098c
    // 0xe80970: mov             x1, x3
    // 0xe80974: r0 = fromNum()
    //     0xe80974: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe80978: ldur            x1, [fp, #-0x38]
    // 0xe8097c: mov             x3, x0
    // 0xe80980: r2 = "/Index"
    //     0xe80980: add             x2, PP, #0x36, lsl #12  ; [pp+0x36648] "/Index"
    //     0xe80984: ldr             x2, [x2, #0x648]
    // 0xe80988: r0 = []=()
    //     0xe80988: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe8098c: ldur            x2, [fp, #-0x28]
    // 0xe80990: ldur            x3, [fp, #-8]
    // 0xe80994: r0 = BoxInt64Instr(r3)
    //     0xe80994: sbfiz           x0, x3, #1, #0x1f
    //     0xe80998: cmp             x3, x0, asr #1
    //     0xe8099c: b.eq            #0xe809a8
    //     0xe809a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe809a4: stur            x3, [x0, #7]
    // 0xe809a8: r1 = 60
    //     0xe809a8: movz            x1, #0x3c
    // 0xe809ac: branchIfSmi(r0, 0xe809b8)
    //     0xe809ac: tbz             w0, #0, #0xe809b8
    // 0xe809b0: r1 = LoadClassIdInstr(r0)
    //     0xe809b0: ldur            x1, [x0, #-1]
    //     0xe809b4: ubfx            x1, x1, #0xc, #0x14
    // 0xe809b8: str             x0, [SP]
    // 0xe809bc: mov             x0, x1
    // 0xe809c0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe809c0: sub             lr, x0, #0xffa
    //     0xe809c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe809c8: blr             lr
    // 0xe809cc: LoadField: d0 = r0->field_7
    //     0xe809cc: ldur            d0, [x0, #7]
    // 0xe809d0: stp             fp, lr, [SP, #-0x10]!
    // 0xe809d4: mov             fp, SP
    // 0xe809d8: CallRuntime_LibcLog(double) -> double
    //     0xe809d8: and             SP, SP, #0xfffffffffffffff0
    //     0xe809dc: mov             sp, SP
    //     0xe809e0: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0xe809e4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe809e8: blr             x16
    //     0xe809ec: movz            x16, #0x8
    //     0xe809f0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe809f4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xe809f8: sub             sp, x16, #1, lsl #12
    //     0xe809fc: mov             SP, fp
    //     0xe80a00: ldp             fp, lr, [SP], #0x10
    // 0xe80a04: mov             v1.16b, v0.16b
    // 0xe80a08: d0 = 0.693147
    //     0xe80a08: add             x17, PP, #0x36, lsl #12  ; [pp+0x36650] IMM: double(0.6931471805599453) from 0x3fe62e42fefa39ef
    //     0xe80a0c: ldr             d0, [x17, #0x650]
    // 0xe80a10: fdiv            d2, d1, d0
    // 0xe80a14: fcmp            d2, d2
    // 0xe80a18: b.vs            #0xe80d00
    // 0xe80a1c: fcvtps          x0, d2
    // 0xe80a20: asr             x16, x0, #0x1e
    // 0xe80a24: cmp             x16, x0, asr #63
    // 0xe80a28: b.ne            #0xe80d00
    // 0xe80a2c: lsl             x0, x0, #1
    // 0xe80a30: r1 = LoadInt32Instr(r0)
    //     0xe80a30: sbfx            x1, x0, #1, #0x1f
    //     0xe80a34: tbz             w0, #0, #0xe80a3c
    //     0xe80a38: ldur            x1, [x0, #7]
    // 0xe80a3c: scvtf           d0, x1
    // 0xe80a40: d1 = 8.000000
    //     0xe80a40: fmov            d1, #8.00000000
    // 0xe80a44: fdiv            d2, d0, d1
    // 0xe80a48: fcmp            d2, d2
    // 0xe80a4c: b.vs            #0xe80d20
    // 0xe80a50: fcvtps          x0, d2
    // 0xe80a54: asr             x16, x0, #0x1e
    // 0xe80a58: cmp             x16, x0, asr #63
    // 0xe80a5c: b.ne            #0xe80d20
    // 0xe80a60: lsl             x0, x0, #1
    // 0xe80a64: stur            x0, [fp, #-0x10]
    // 0xe80a68: r1 = Null
    //     0xe80a68: mov             x1, NULL
    // 0xe80a6c: r2 = 6
    //     0xe80a6c: movz            x2, #0x6
    // 0xe80a70: r0 = AllocateArray()
    //     0xe80a70: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe80a74: stur            x0, [fp, #-0x78]
    // 0xe80a78: r16 = 2
    //     0xe80a78: movz            x16, #0x2
    // 0xe80a7c: StoreField: r0->field_f = r16
    //     0xe80a7c: stur            w16, [x0, #0xf]
    // 0xe80a80: ldur            x1, [fp, #-0x10]
    // 0xe80a84: StoreField: r0->field_13 = r1
    //     0xe80a84: stur            w1, [x0, #0x13]
    // 0xe80a88: r16 = 2
    //     0xe80a88: movz            x16, #0x2
    // 0xe80a8c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe80a8c: stur            w16, [x0, #0x17]
    // 0xe80a90: r1 = <int>
    //     0xe80a90: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe80a94: r0 = AllocateGrowableArray()
    //     0xe80a94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe80a98: mov             x2, x0
    // 0xe80a9c: ldur            x0, [fp, #-0x78]
    // 0xe80aa0: stur            x2, [fp, #-0x10]
    // 0xe80aa4: StoreField: r2->field_f = r0
    //     0xe80aa4: stur            w0, [x2, #0xf]
    // 0xe80aa8: r0 = 6
    //     0xe80aa8: movz            x0, #0x6
    // 0xe80aac: StoreField: r2->field_b = r0
    //     0xe80aac: stur            w0, [x2, #0xb]
    // 0xe80ab0: mov             x1, x2
    // 0xe80ab4: r0 = fromNum()
    //     0xe80ab4: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe80ab8: ldur            x1, [fp, #-0x38]
    // 0xe80abc: mov             x3, x0
    // 0xe80ac0: r2 = "/W"
    //     0xe80ac0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36658] "/W"
    //     0xe80ac4: ldr             x2, [x2, #0x658]
    // 0xe80ac8: r0 = []=()
    //     0xe80ac8: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe80acc: r1 = Function '<anonymous closure>':.
    //     0xe80acc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36660] AnonymousClosure: static (0xaec9e0), in [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumBy (0xaec92c)
    //     0xe80ad0: ldr             x1, [x1, #0x660]
    // 0xe80ad4: r2 = Null
    //     0xe80ad4: mov             x2, NULL
    // 0xe80ad8: r0 = AllocateClosure()
    //     0xe80ad8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe80adc: ldur            x1, [fp, #-0x10]
    // 0xe80ae0: mov             x2, x0
    // 0xe80ae4: r0 = reduce()
    //     0xe80ae4: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe80ae8: ldur            x2, [fp, #-0x28]
    // 0xe80aec: LoadField: r1 = r2->field_b
    //     0xe80aec: ldur            w1, [x2, #0xb]
    // 0xe80af0: r3 = LoadInt32Instr(r1)
    //     0xe80af0: sbfx            x3, x1, #1, #0x1f
    // 0xe80af4: add             x1, x3, #1
    // 0xe80af8: r3 = LoadInt32Instr(r0)
    //     0xe80af8: sbfx            x3, x0, #1, #0x1f
    //     0xe80afc: tbz             w0, #0, #0xe80b04
    //     0xe80b00: ldur            x3, [x0, #7]
    // 0xe80b04: stur            x3, [fp, #-8]
    // 0xe80b08: mul             x4, x1, x3
    // 0xe80b0c: r0 = BoxInt64Instr(r4)
    //     0xe80b0c: sbfiz           x0, x4, #1, #0x1f
    //     0xe80b10: cmp             x4, x0, asr #1
    //     0xe80b14: b.eq            #0xe80b20
    //     0xe80b18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe80b1c: stur            x4, [x0, #7]
    // 0xe80b20: stp             x0, NULL, [SP]
    // 0xe80b24: r0 = ByteData()
    //     0xe80b24: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xe80b28: mov             x4, x0
    // 0xe80b2c: ldur            x0, [fp, #-0x28]
    // 0xe80b30: stur            x4, [fp, #-0x78]
    // 0xe80b34: LoadField: r1 = r0->field_b
    //     0xe80b34: ldur            w1, [x0, #0xb]
    // 0xe80b38: r6 = LoadInt32Instr(r1)
    //     0xe80b38: sbfx            x6, x1, #1, #0x1f
    // 0xe80b3c: stur            x6, [fp, #-0x30]
    // 0xe80b40: ldur            x3, [fp, #-8]
    // 0xe80b44: r1 = 0
    //     0xe80b44: movz            x1, #0
    // 0xe80b48: CheckStackOverflow
    //     0xe80b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80b4c: cmp             SP, x16
    //     0xe80b50: b.ls            #0xe80d40
    // 0xe80b54: LoadField: r2 = r0->field_b
    //     0xe80b54: ldur            w2, [x0, #0xb]
    // 0xe80b58: r5 = LoadInt32Instr(r2)
    //     0xe80b58: sbfx            x5, x2, #1, #0x1f
    // 0xe80b5c: cmp             x6, x5
    // 0xe80b60: b.ne            #0xe80ca8
    // 0xe80b64: cmp             x1, x5
    // 0xe80b68: b.ge            #0xe80bb0
    // 0xe80b6c: LoadField: r2 = r0->field_f
    //     0xe80b6c: ldur            w2, [x0, #0xf]
    // 0xe80b70: DecompressPointer r2
    //     0xe80b70: add             x2, x2, HEAP, lsl #32
    // 0xe80b74: ArrayLoad: r5 = r2[r1]  ; Unknown_4
    //     0xe80b74: add             x16, x2, x1, lsl #2
    //     0xe80b78: ldur            w5, [x16, #0xf]
    // 0xe80b7c: DecompressPointer r5
    //     0xe80b7c: add             x5, x5, HEAP, lsl #32
    // 0xe80b80: add             x7, x1, #1
    // 0xe80b84: mov             x1, x5
    // 0xe80b88: mov             x2, x4
    // 0xe80b8c: ldur            x5, [fp, #-0x10]
    // 0xe80b90: stur            x7, [fp, #-8]
    // 0xe80b94: r0 = _compressedRef()
    //     0xe80b94: bl              #0xe80d48  ; [package:pdf/src/pdf/format/xref.dart] PdfXref::_compressedRef
    // 0xe80b98: mov             x3, x0
    // 0xe80b9c: ldur            x1, [fp, #-8]
    // 0xe80ba0: ldur            x0, [fp, #-0x28]
    // 0xe80ba4: ldur            x4, [fp, #-0x78]
    // 0xe80ba8: ldur            x6, [fp, #-0x30]
    // 0xe80bac: b               #0xe80b48
    // 0xe80bb0: mov             x0, x4
    // 0xe80bb4: ldur            x4, [fp, #-0x18]
    // 0xe80bb8: ldur            x3, [fp, #-0x20]
    // 0xe80bbc: ldur            x1, [fp, #-0x38]
    // 0xe80bc0: ldur            x2, [fp, #-0x40]
    // 0xe80bc4: LoadField: r5 = r3->field_b
    //     0xe80bc4: ldur            x5, [x3, #0xb]
    // 0xe80bc8: stur            x5, [fp, #-8]
    // 0xe80bcc: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xe80bcc: ldur            w6, [x0, #0x17]
    // 0xe80bd0: DecompressPointer r6
    //     0xe80bd0: add             x6, x6, HEAP, lsl #32
    // 0xe80bd4: stur            x6, [fp, #-0x10]
    // 0xe80bd8: r0 = _ByteBuffer()
    //     0xe80bd8: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xe80bdc: mov             x1, x0
    // 0xe80be0: ldur            x0, [fp, #-0x10]
    // 0xe80be4: StoreField: r1->field_7 = r0
    //     0xe80be4: stur            w0, [x1, #7]
    // 0xe80be8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe80be8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe80bec: r0 = asUint8List()
    //     0xe80bec: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xe80bf0: mov             x2, x0
    // 0xe80bf4: ldur            x0, [fp, #-0x38]
    // 0xe80bf8: stur            x2, [fp, #-0x78]
    // 0xe80bfc: LoadField: r3 = r0->field_b
    //     0xe80bfc: ldur            w3, [x0, #0xb]
    // 0xe80c00: DecompressPointer r3
    //     0xe80c00: add             x3, x3, HEAP, lsl #32
    // 0xe80c04: stur            x3, [fp, #-0x10]
    // 0xe80c08: r1 = <PdfDataType>
    //     0xe80c08: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe80c0c: ldr             x1, [x1, #0x4c8]
    // 0xe80c10: r0 = PdfDictStream()
    //     0xe80c10: bl              #0x862188  ; AllocatePdfDictStreamStub -> PdfDictStream (size=0x20)
    // 0xe80c14: mov             x2, x0
    // 0xe80c18: r0 = false
    //     0xe80c18: add             x0, NULL, #0x30  ; false
    // 0xe80c1c: stur            x2, [fp, #-0x38]
    // 0xe80c20: StoreField: r2->field_13 = r0
    //     0xe80c20: stur            w0, [x2, #0x13]
    // 0xe80c24: ArrayStore: r2[0] = r0  ; List_4
    //     0xe80c24: stur            w0, [x2, #0x17]
    // 0xe80c28: r0 = true
    //     0xe80c28: add             x0, NULL, #0x20  ; true
    // 0xe80c2c: StoreField: r2->field_1b = r0
    //     0xe80c2c: stur            w0, [x2, #0x1b]
    // 0xe80c30: ldur            x0, [fp, #-0x78]
    // 0xe80c34: StoreField: r2->field_f = r0
    //     0xe80c34: stur            w0, [x2, #0xf]
    // 0xe80c38: ldur            x0, [fp, #-0x10]
    // 0xe80c3c: StoreField: r2->field_b = r0
    //     0xe80c3c: stur            w0, [x2, #0xb]
    // 0xe80c40: ldur            x0, [fp, #-0x18]
    // 0xe80c44: LoadField: r3 = r0->field_1f
    //     0xe80c44: ldur            w3, [x0, #0x1f]
    // 0xe80c48: DecompressPointer r3
    //     0xe80c48: add             x3, x3, HEAP, lsl #32
    // 0xe80c4c: stur            x3, [fp, #-0x10]
    // 0xe80c50: r1 = <PdfDictStream>
    //     0xe80c50: add             x1, PP, #0x36, lsl #12  ; [pp+0x36668] TypeArguments: <PdfDictStream>
    //     0xe80c54: ldr             x1, [x1, #0x668]
    // 0xe80c58: r0 = PdfObjectBase()
    //     0xe80c58: bl              #0x7b58ec  ; AllocatePdfObjectBaseStub -> PdfObjectBase<X0 bound PdfDataType> (size=0x24)
    // 0xe80c5c: mov             x3, x0
    // 0xe80c60: ldur            x0, [fp, #-0x40]
    // 0xe80c64: stur            x3, [fp, #-0x18]
    // 0xe80c68: StoreField: r3->field_b = r0
    //     0xe80c68: stur            x0, [x3, #0xb]
    // 0xe80c6c: StoreField: r3->field_13 = rZR
    //     0xe80c6c: stur            xzr, [x3, #0x13]
    // 0xe80c70: ldur            x0, [fp, #-0x38]
    // 0xe80c74: StoreField: r3->field_1b = r0
    //     0xe80c74: stur            w0, [x3, #0x1b]
    // 0xe80c78: ldur            x0, [fp, #-0x10]
    // 0xe80c7c: StoreField: r3->field_1f = r0
    //     0xe80c7c: stur            w0, [x3, #0x1f]
    // 0xe80c80: r1 = <String>
    //     0xe80c80: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe80c84: r2 = 0
    //     0xe80c84: movz            x2, #0
    // 0xe80c88: r0 = _GrowableList()
    //     0xe80c88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe80c8c: ldur            x1, [fp, #-0x18]
    // 0xe80c90: ldur            x2, [fp, #-0x20]
    // 0xe80c94: r0 = output()
    //     0xe80c94: bl              #0xe80250  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::output
    // 0xe80c98: ldur            x0, [fp, #-8]
    // 0xe80c9c: LeaveFrame
    //     0xe80c9c: mov             SP, fp
    //     0xe80ca0: ldp             fp, lr, [SP], #0x10
    // 0xe80ca4: ret
    //     0xe80ca4: ret             
    // 0xe80ca8: r0 = ConcurrentModificationError()
    //     0xe80ca8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe80cac: mov             x1, x0
    // 0xe80cb0: ldur            x0, [fp, #-0x28]
    // 0xe80cb4: StoreField: r1->field_b = r0
    //     0xe80cb4: stur            w0, [x1, #0xb]
    // 0xe80cb8: mov             x0, x1
    // 0xe80cbc: r0 = Throw()
    //     0xe80cbc: bl              #0xec04b8  ; ThrowStub
    // 0xe80cc0: brk             #0
    // 0xe80cc4: mov             x0, x2
    // 0xe80cc8: r0 = ConcurrentModificationError()
    //     0xe80cc8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe80ccc: mov             x1, x0
    // 0xe80cd0: ldur            x0, [fp, #-0x28]
    // 0xe80cd4: StoreField: r1->field_b = r0
    //     0xe80cd4: stur            w0, [x1, #0xb]
    // 0xe80cd8: mov             x0, x1
    // 0xe80cdc: r0 = Throw()
    //     0xe80cdc: bl              #0xec04b8  ; ThrowStub
    // 0xe80ce0: brk             #0
    // 0xe80ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80ce8: b               #0xe80378
    // 0xe80cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80cf0: b               #0xe806e0
    // 0xe80cf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80cf4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80cf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80cf8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80cfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80cfc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80d00: SaveReg d2
    //     0xe80d00: str             q2, [SP, #-0x10]!
    // 0xe80d04: d0 = 0.000000
    //     0xe80d04: fmov            d0, d2
    // 0xe80d08: r0 = 64
    //     0xe80d08: movz            x0, #0x40
    // 0xe80d0c: r30 = DoubleToIntegerStub
    //     0xe80d0c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe80d10: LoadField: r30 = r30->field_7
    //     0xe80d10: ldur            lr, [lr, #7]
    // 0xe80d14: blr             lr
    // 0xe80d18: RestoreReg d2
    //     0xe80d18: ldr             q2, [SP], #0x10
    // 0xe80d1c: b               #0xe80a30
    // 0xe80d20: SaveReg d2
    //     0xe80d20: str             q2, [SP, #-0x10]!
    // 0xe80d24: d0 = 0.000000
    //     0xe80d24: fmov            d0, d2
    // 0xe80d28: r0 = 64
    //     0xe80d28: movz            x0, #0x40
    // 0xe80d2c: r30 = DoubleToIntegerStub
    //     0xe80d2c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe80d30: LoadField: r30 = r30->field_7
    //     0xe80d30: ldur            lr, [lr, #7]
    // 0xe80d34: blr             lr
    // 0xe80d38: RestoreReg d2
    //     0xe80d38: ldr             q2, [SP], #0x10
    // 0xe80d3c: b               #0xe80a64
    // 0xe80d40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80d40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80d44: b               #0xe80b54
  }
  _ _outputLegacy(/* No info */) {
    // ** addr: 0xe8105c, size: 0x428
    // 0xe8105c: EnterFrame
    //     0xe8105c: stp             fp, lr, [SP, #-0x10]!
    //     0xe81060: mov             fp, SP
    // 0xe81064: AllocStack(0x70)
    //     0xe81064: sub             SP, SP, #0x70
    // 0xe81068: SetupParameters(PdfXrefTable this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0xe81068: mov             x0, x5
    //     0xe8106c: stur            x5, [fp, #-0x20]
    //     0xe81070: mov             x5, x1
    //     0xe81074: mov             x4, x2
    //     0xe81078: stur            x1, [fp, #-8]
    //     0xe8107c: stur            x2, [fp, #-0x10]
    //     0xe81080: stur            x3, [fp, #-0x18]
    // 0xe81084: CheckStackOverflow
    //     0xe81084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81088: cmp             SP, x16
    //     0xe8108c: b.ls            #0xe81474
    // 0xe81090: r1 = Function '<anonymous closure>':.
    //     0xe81090: add             x1, PP, #0x36, lsl #12  ; [pp+0x36680] AnonymousClosure: (0xafb240), in [package:nuonline/app/modules/home/<USER>/home_view.dart] HomeView::build (0xaf9d94)
    //     0xe81094: ldr             x1, [x1, #0x680]
    // 0xe81098: r2 = Null
    //     0xe81098: mov             x2, NULL
    // 0xe8109c: r0 = AllocateClosure()
    //     0xe8109c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe810a0: str             x0, [SP]
    // 0xe810a4: ldur            x1, [fp, #-0x20]
    // 0xe810a8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe810a8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe810ac: r0 = sort()
    //     0xe810ac: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xe810b0: ldur            x0, [fp, #-8]
    // 0xe810b4: LoadField: r2 = r0->field_f
    //     0xe810b4: ldur            x2, [x0, #0xf]
    // 0xe810b8: ldur            x1, [fp, #-0x20]
    // 0xe810bc: stur            x2, [fp, #-0x28]
    // 0xe810c0: r0 = last()
    //     0xe810c0: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xe810c4: LoadField: r1 = r0->field_7
    //     0xe810c4: ldur            x1, [x0, #7]
    // 0xe810c8: add             x2, x1, #1
    // 0xe810cc: ldur            x3, [fp, #-0x28]
    // 0xe810d0: cmp             x3, x2
    // 0xe810d4: b.le            #0xe810f0
    // 0xe810d8: r0 = BoxInt64Instr(r3)
    //     0xe810d8: sbfiz           x0, x3, #1, #0x1f
    //     0xe810dc: cmp             x3, x0, asr #1
    //     0xe810e0: b.eq            #0xe810ec
    //     0xe810e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe810e8: stur            x3, [x0, #7]
    // 0xe810ec: b               #0xe81210
    // 0xe810f0: cmp             x3, x2
    // 0xe810f4: b.ge            #0xe81110
    // 0xe810f8: r0 = BoxInt64Instr(r2)
    //     0xe810f8: sbfiz           x0, x2, #1, #0x1f
    //     0xe810fc: cmp             x2, x0, asr #1
    //     0xe81100: b.eq            #0xe8110c
    //     0xe81104: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe81108: stur            x2, [x0, #7]
    // 0xe8110c: b               #0xe81210
    // 0xe81110: r0 = BoxInt64Instr(r2)
    //     0xe81110: sbfiz           x0, x2, #1, #0x1f
    //     0xe81114: cmp             x2, x0, asr #1
    //     0xe81118: b.eq            #0xe81124
    //     0xe8111c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe81120: stur            x2, [x0, #7]
    // 0xe81124: mov             x4, x0
    // 0xe81128: stur            x4, [fp, #-0x30]
    // 0xe8112c: r0 = 60
    //     0xe8112c: movz            x0, #0x3c
    // 0xe81130: branchIfSmi(r4, 0xe8113c)
    //     0xe81130: tbz             w4, #0, #0xe8113c
    // 0xe81134: r0 = LoadClassIdInstr(r4)
    //     0xe81134: ldur            x0, [x4, #-1]
    //     0xe81138: ubfx            x0, x0, #0xc, #0x14
    // 0xe8113c: cmp             x0, #0x3e
    // 0xe81140: b.ne            #0xe811b0
    // 0xe81144: r0 = BoxInt64Instr(r3)
    //     0xe81144: sbfiz           x0, x3, #1, #0x1f
    //     0xe81148: cmp             x3, x0, asr #1
    //     0xe8114c: b.eq            #0xe81158
    //     0xe81150: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe81154: stur            x3, [x0, #7]
    // 0xe81158: r1 = 60
    //     0xe81158: movz            x1, #0x3c
    // 0xe8115c: branchIfSmi(r0, 0xe81168)
    //     0xe8115c: tbz             w0, #0, #0xe81168
    // 0xe81160: r1 = LoadClassIdInstr(r0)
    //     0xe81160: ldur            x1, [x0, #-1]
    //     0xe81164: ubfx            x1, x1, #0xc, #0x14
    // 0xe81168: cmp             x1, #0x3e
    // 0xe8116c: b.ne            #0xe8119c
    // 0xe81170: d0 = 0.000000
    //     0xe81170: eor             v0.16b, v0.16b, v0.16b
    // 0xe81174: scvtf           d1, x3
    // 0xe81178: fcmp            d1, d0
    // 0xe8117c: b.ne            #0xe8119c
    // 0xe81180: add             x4, x3, x2
    // 0xe81184: r0 = BoxInt64Instr(r4)
    //     0xe81184: sbfiz           x0, x4, #1, #0x1f
    //     0xe81188: cmp             x4, x0, asr #1
    //     0xe8118c: b.eq            #0xe81198
    //     0xe81190: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe81194: stur            x4, [x0, #7]
    // 0xe81198: b               #0xe81210
    // 0xe8119c: LoadField: d0 = r4->field_7
    //     0xe8119c: ldur            d0, [x4, #7]
    // 0xe811a0: fcmp            d0, d0
    // 0xe811a4: b.vc            #0xe81210
    // 0xe811a8: mov             x0, x4
    // 0xe811ac: b               #0xe81210
    // 0xe811b0: cbnz            x2, #0xe811f8
    // 0xe811b4: r0 = BoxInt64Instr(r3)
    //     0xe811b4: sbfiz           x0, x3, #1, #0x1f
    //     0xe811b8: cmp             x3, x0, asr #1
    //     0xe811bc: b.eq            #0xe811c8
    //     0xe811c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe811c4: stur            x3, [x0, #7]
    // 0xe811c8: r1 = 60
    //     0xe811c8: movz            x1, #0x3c
    // 0xe811cc: branchIfSmi(r0, 0xe811d8)
    //     0xe811cc: tbz             w0, #0, #0xe811d8
    // 0xe811d0: r1 = LoadClassIdInstr(r0)
    //     0xe811d0: ldur            x1, [x0, #-1]
    //     0xe811d4: ubfx            x1, x1, #0xc, #0x14
    // 0xe811d8: str             x0, [SP]
    // 0xe811dc: mov             x0, x1
    // 0xe811e0: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xe811e0: sub             lr, x0, #0xfb8
    //     0xe811e4: ldr             lr, [x21, lr, lsl #3]
    //     0xe811e8: blr             lr
    // 0xe811ec: tbnz            w0, #4, #0xe811f8
    // 0xe811f0: ldur            x0, [fp, #-0x30]
    // 0xe811f4: b               #0xe81210
    // 0xe811f8: ldur            x2, [fp, #-0x28]
    // 0xe811fc: r0 = BoxInt64Instr(r2)
    //     0xe811fc: sbfiz           x0, x2, #1, #0x1f
    //     0xe81200: cmp             x2, x0, asr #1
    //     0xe81204: b.eq            #0xe81210
    //     0xe81208: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8120c: stur            x2, [x0, #7]
    // 0xe81210: stur            x0, [fp, #-0x30]
    // 0xe81214: r1 = <PdfXref>
    //     0xe81214: add             x1, PP, #0x36, lsl #12  ; [pp+0x365e0] TypeArguments: <PdfXref>
    //     0xe81218: ldr             x1, [x1, #0x5e0]
    // 0xe8121c: r2 = 0
    //     0xe8121c: movz            x2, #0
    // 0xe81220: r0 = _GrowableList()
    //     0xe81220: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe81224: stur            x0, [fp, #-0x38]
    // 0xe81228: LoadField: r1 = r0->field_b
    //     0xe81228: ldur            w1, [x0, #0xb]
    // 0xe8122c: LoadField: r2 = r0->field_f
    //     0xe8122c: ldur            w2, [x0, #0xf]
    // 0xe81230: DecompressPointer r2
    //     0xe81230: add             x2, x2, HEAP, lsl #32
    // 0xe81234: LoadField: r3 = r2->field_b
    //     0xe81234: ldur            w3, [x2, #0xb]
    // 0xe81238: r2 = LoadInt32Instr(r1)
    //     0xe81238: sbfx            x2, x1, #1, #0x1f
    // 0xe8123c: stur            x2, [fp, #-0x28]
    // 0xe81240: r1 = LoadInt32Instr(r3)
    //     0xe81240: sbfx            x1, x3, #1, #0x1f
    // 0xe81244: cmp             x2, x1
    // 0xe81248: b.ne            #0xe81254
    // 0xe8124c: mov             x1, x0
    // 0xe81250: r0 = _growToNextCapacity()
    //     0xe81250: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe81254: ldur            x4, [fp, #-0x18]
    // 0xe81258: ldur            x3, [fp, #-0x20]
    // 0xe8125c: ldur            x5, [fp, #-0x38]
    // 0xe81260: ldur            x0, [fp, #-0x28]
    // 0xe81264: add             x1, x0, #1
    // 0xe81268: lsl             x2, x1, #1
    // 0xe8126c: StoreField: r5->field_b = r2
    //     0xe8126c: stur            w2, [x5, #0xb]
    // 0xe81270: LoadField: r1 = r5->field_f
    //     0xe81270: ldur            w1, [x5, #0xf]
    // 0xe81274: DecompressPointer r1
    //     0xe81274: add             x1, x1, HEAP, lsl #32
    // 0xe81278: add             x2, x1, x0, lsl #2
    // 0xe8127c: r16 = Instance_PdfXref
    //     0xe8127c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36688] Obj!PdfXref@e0c951
    //     0xe81280: ldr             x16, [x16, #0x688]
    // 0xe81284: StoreField: r2->field_f = r16
    //     0xe81284: stur            w16, [x2, #0xf]
    // 0xe81288: LoadField: r0 = r4->field_b
    //     0xe81288: ldur            x0, [x4, #0xb]
    // 0xe8128c: mov             x1, x4
    // 0xe81290: stur            x0, [fp, #-0x28]
    // 0xe81294: r2 = "xref\n"
    //     0xe81294: add             x2, PP, #0x36, lsl #12  ; [pp+0x36690] "xref\n"
    //     0xe81298: ldr             x2, [x2, #0x690]
    // 0xe8129c: r0 = putString()
    //     0xe8129c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe812a0: ldur            x0, [fp, #-0x20]
    // 0xe812a4: LoadField: r1 = r0->field_b
    //     0xe812a4: ldur            w1, [x0, #0xb]
    // 0xe812a8: r4 = LoadInt32Instr(r1)
    //     0xe812a8: sbfx            x4, x1, #1, #0x1f
    // 0xe812ac: stur            x4, [fp, #-0x58]
    // 0xe812b0: ldur            x6, [fp, #-0x38]
    // 0xe812b4: r3 = 0
    //     0xe812b4: movz            x3, #0
    // 0xe812b8: r2 = 0
    //     0xe812b8: movz            x2, #0
    // 0xe812bc: r1 = 0
    //     0xe812bc: movz            x1, #0
    // 0xe812c0: CheckStackOverflow
    //     0xe812c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe812c4: cmp             SP, x16
    //     0xe812c8: b.ls            #0xe8147c
    // 0xe812cc: LoadField: r5 = r0->field_b
    //     0xe812cc: ldur            w5, [x0, #0xb]
    // 0xe812d0: r7 = LoadInt32Instr(r5)
    //     0xe812d0: sbfx            x7, x5, #1, #0x1f
    // 0xe812d4: cmp             x4, x7
    // 0xe812d8: b.ne            #0xe81458
    // 0xe812dc: cmp             x1, x7
    // 0xe812e0: b.ge            #0xe813d0
    // 0xe812e4: LoadField: r5 = r0->field_f
    //     0xe812e4: ldur            w5, [x0, #0xf]
    // 0xe812e8: DecompressPointer r5
    //     0xe812e8: add             x5, x5, HEAP, lsl #32
    // 0xe812ec: ArrayLoad: r7 = r5[r1]  ; Unknown_4
    //     0xe812ec: add             x16, x5, x1, lsl #2
    //     0xe812f0: ldur            w7, [x16, #0xf]
    // 0xe812f4: DecompressPointer r7
    //     0xe812f4: add             x7, x7, HEAP, lsl #32
    // 0xe812f8: stur            x7, [fp, #-0x50]
    // 0xe812fc: add             x8, x1, #1
    // 0xe81300: stur            x8, [fp, #-0x48]
    // 0xe81304: LoadField: r9 = r7->field_7
    //     0xe81304: ldur            x9, [x7, #7]
    // 0xe81308: stur            x9, [fp, #-0x40]
    // 0xe8130c: add             x1, x2, #1
    // 0xe81310: cmp             x9, x1
    // 0xe81314: b.eq            #0xe81338
    // 0xe81318: ldur            x1, [fp, #-8]
    // 0xe8131c: ldur            x2, [fp, #-0x18]
    // 0xe81320: mov             x5, x6
    // 0xe81324: r0 = _writeBlock()
    //     0xe81324: bl              #0xe81484  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::_writeBlock
    // 0xe81328: ldur            x1, [fp, #-0x38]
    // 0xe8132c: r2 = 0
    //     0xe8132c: movz            x2, #0
    // 0xe81330: r0 = length=()
    //     0xe81330: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xe81334: ldur            x3, [fp, #-0x40]
    // 0xe81338: ldur            x0, [fp, #-0x38]
    // 0xe8133c: stur            x3, [fp, #-0x68]
    // 0xe81340: LoadField: r1 = r0->field_b
    //     0xe81340: ldur            w1, [x0, #0xb]
    // 0xe81344: LoadField: r2 = r0->field_f
    //     0xe81344: ldur            w2, [x0, #0xf]
    // 0xe81348: DecompressPointer r2
    //     0xe81348: add             x2, x2, HEAP, lsl #32
    // 0xe8134c: LoadField: r4 = r2->field_b
    //     0xe8134c: ldur            w4, [x2, #0xb]
    // 0xe81350: r2 = LoadInt32Instr(r1)
    //     0xe81350: sbfx            x2, x1, #1, #0x1f
    // 0xe81354: stur            x2, [fp, #-0x60]
    // 0xe81358: r1 = LoadInt32Instr(r4)
    //     0xe81358: sbfx            x1, x4, #1, #0x1f
    // 0xe8135c: cmp             x2, x1
    // 0xe81360: b.ne            #0xe8136c
    // 0xe81364: mov             x1, x0
    // 0xe81368: r0 = _growToNextCapacity()
    //     0xe81368: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8136c: ldur            x5, [fp, #-0x38]
    // 0xe81370: ldur            x2, [fp, #-0x60]
    // 0xe81374: add             x0, x2, #1
    // 0xe81378: lsl             x1, x0, #1
    // 0xe8137c: StoreField: r5->field_b = r1
    //     0xe8137c: stur            w1, [x5, #0xb]
    // 0xe81380: LoadField: r1 = r5->field_f
    //     0xe81380: ldur            w1, [x5, #0xf]
    // 0xe81384: DecompressPointer r1
    //     0xe81384: add             x1, x1, HEAP, lsl #32
    // 0xe81388: ldur            x0, [fp, #-0x50]
    // 0xe8138c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe8138c: add             x25, x1, x2, lsl #2
    //     0xe81390: add             x25, x25, #0xf
    //     0xe81394: str             w0, [x25]
    //     0xe81398: tbz             w0, #0, #0xe813b4
    //     0xe8139c: ldurb           w16, [x1, #-1]
    //     0xe813a0: ldurb           w17, [x0, #-1]
    //     0xe813a4: and             x16, x17, x16, lsr #2
    //     0xe813a8: tst             x16, HEAP, lsr #32
    //     0xe813ac: b.eq            #0xe813b4
    //     0xe813b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe813b4: ldur            x3, [fp, #-0x68]
    // 0xe813b8: ldur            x2, [fp, #-0x40]
    // 0xe813bc: ldur            x1, [fp, #-0x48]
    // 0xe813c0: ldur            x0, [fp, #-0x20]
    // 0xe813c4: mov             x6, x5
    // 0xe813c8: ldur            x4, [fp, #-0x58]
    // 0xe813cc: b               #0xe812c0
    // 0xe813d0: ldur            x4, [fp, #-8]
    // 0xe813d4: mov             x5, x6
    // 0xe813d8: ldur            x0, [fp, #-0x30]
    // 0xe813dc: mov             x1, x4
    // 0xe813e0: ldur            x2, [fp, #-0x18]
    // 0xe813e4: r0 = _writeBlock()
    //     0xe813e4: bl              #0xe81484  ; [package:pdf/src/pdf/format/xref.dart] PdfXrefTable::_writeBlock
    // 0xe813e8: ldur            x1, [fp, #-0x18]
    // 0xe813ec: r2 = "trailer\n"
    //     0xe813ec: add             x2, PP, #0x36, lsl #12  ; [pp+0x36698] "trailer\n"
    //     0xe813f0: ldr             x2, [x2, #0x698]
    // 0xe813f4: r0 = putString()
    //     0xe813f4: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe813f8: ldur            x0, [fp, #-8]
    // 0xe813fc: LoadField: r1 = r0->field_7
    //     0xe813fc: ldur            w1, [x0, #7]
    // 0xe81400: DecompressPointer r1
    //     0xe81400: add             x1, x1, HEAP, lsl #32
    // 0xe81404: stur            x1, [fp, #-0x38]
    // 0xe81408: r0 = PdfNum()
    //     0xe81408: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe8140c: mov             x1, x0
    // 0xe81410: ldur            x0, [fp, #-0x30]
    // 0xe81414: StoreField: r1->field_7 = r0
    //     0xe81414: stur            w0, [x1, #7]
    // 0xe81418: mov             x3, x1
    // 0xe8141c: ldur            x1, [fp, #-0x38]
    // 0xe81420: r2 = "/Size"
    //     0xe81420: add             x2, PP, #0x36, lsl #12  ; [pp+0x36640] "/Size"
    //     0xe81424: ldr             x2, [x2, #0x640]
    // 0xe81428: r0 = []=()
    //     0xe81428: bl              #0x7b551c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::[]=
    // 0xe8142c: ldur            x1, [fp, #-0x38]
    // 0xe81430: ldur            x2, [fp, #-0x10]
    // 0xe81434: ldur            x3, [fp, #-0x18]
    // 0xe81438: r0 = output()
    //     0xe81438: bl              #0xe7ed7c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::output
    // 0xe8143c: ldur            x1, [fp, #-0x18]
    // 0xe81440: r2 = 10
    //     0xe81440: movz            x2, #0xa
    // 0xe81444: r0 = putByte()
    //     0xe81444: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xe81448: ldur            x0, [fp, #-0x28]
    // 0xe8144c: LeaveFrame
    //     0xe8144c: mov             SP, fp
    //     0xe81450: ldp             fp, lr, [SP], #0x10
    // 0xe81454: ret
    //     0xe81454: ret             
    // 0xe81458: r0 = ConcurrentModificationError()
    //     0xe81458: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe8145c: mov             x1, x0
    // 0xe81460: ldur            x0, [fp, #-0x20]
    // 0xe81464: StoreField: r1->field_b = r0
    //     0xe81464: stur            w0, [x1, #0xb]
    // 0xe81468: mov             x0, x1
    // 0xe8146c: r0 = Throw()
    //     0xe8146c: bl              #0xec04b8  ; ThrowStub
    // 0xe81470: brk             #0
    // 0xe81474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81478: b               #0xe81090
    // 0xe8147c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8147c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81480: b               #0xe812cc
  }
  _ _writeBlock(/* No info */) {
    // ** addr: 0xe81484, size: 0x1a4
    // 0xe81484: EnterFrame
    //     0xe81484: stp             fp, lr, [SP, #-0x10]!
    //     0xe81488: mov             fp, SP
    // 0xe8148c: AllocStack(0x30)
    //     0xe8148c: sub             SP, SP, #0x30
    // 0xe81490: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xe81490: mov             x4, x2
    //     0xe81494: stur            x2, [fp, #-0x10]
    //     0xe81498: stur            x5, [fp, #-0x18]
    // 0xe8149c: CheckStackOverflow
    //     0xe8149c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe814a0: cmp             SP, x16
    //     0xe814a4: b.ls            #0xe81614
    // 0xe814a8: r0 = BoxInt64Instr(r3)
    //     0xe814a8: sbfiz           x0, x3, #1, #0x1f
    //     0xe814ac: cmp             x3, x0, asr #1
    //     0xe814b0: b.eq            #0xe814bc
    //     0xe814b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe814b8: stur            x3, [x0, #7]
    // 0xe814bc: r1 = Null
    //     0xe814bc: mov             x1, NULL
    // 0xe814c0: r2 = 8
    //     0xe814c0: movz            x2, #0x8
    // 0xe814c4: stur            x0, [fp, #-8]
    // 0xe814c8: r0 = AllocateArray()
    //     0xe814c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe814cc: mov             x1, x0
    // 0xe814d0: ldur            x0, [fp, #-8]
    // 0xe814d4: StoreField: r1->field_f = r0
    //     0xe814d4: stur            w0, [x1, #0xf]
    // 0xe814d8: r16 = " "
    //     0xe814d8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe814dc: StoreField: r1->field_13 = r16
    //     0xe814dc: stur            w16, [x1, #0x13]
    // 0xe814e0: ldur            x0, [fp, #-0x18]
    // 0xe814e4: LoadField: r2 = r0->field_b
    //     0xe814e4: ldur            w2, [x0, #0xb]
    // 0xe814e8: ArrayStore: r1[0] = r2  ; List_4
    //     0xe814e8: stur            w2, [x1, #0x17]
    // 0xe814ec: r16 = "\n"
    //     0xe814ec: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xe814f0: StoreField: r1->field_1b = r16
    //     0xe814f0: stur            w16, [x1, #0x1b]
    // 0xe814f4: str             x1, [SP]
    // 0xe814f8: r0 = _interpolate()
    //     0xe814f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe814fc: ldur            x1, [fp, #-0x10]
    // 0xe81500: mov             x2, x0
    // 0xe81504: r0 = putString()
    //     0xe81504: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe81508: ldur            x0, [fp, #-0x18]
    // 0xe8150c: LoadField: r1 = r0->field_b
    //     0xe8150c: ldur            w1, [x0, #0xb]
    // 0xe81510: r2 = LoadInt32Instr(r1)
    //     0xe81510: sbfx            x2, x1, #1, #0x1f
    // 0xe81514: stur            x2, [fp, #-0x28]
    // 0xe81518: ldur            x3, [fp, #-0x10]
    // 0xe8151c: r1 = 0
    //     0xe8151c: movz            x1, #0
    // 0xe81520: CheckStackOverflow
    //     0xe81520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81524: cmp             SP, x16
    //     0xe81528: b.ls            #0xe8161c
    // 0xe8152c: LoadField: r4 = r0->field_b
    //     0xe8152c: ldur            w4, [x0, #0xb]
    // 0xe81530: r5 = LoadInt32Instr(r4)
    //     0xe81530: sbfx            x5, x4, #1, #0x1f
    // 0xe81534: cmp             x2, x5
    // 0xe81538: b.ne            #0xe815f8
    // 0xe8153c: cmp             x1, x5
    // 0xe81540: b.ge            #0xe815e8
    // 0xe81544: LoadField: r4 = r0->field_f
    //     0xe81544: ldur            w4, [x0, #0xf]
    // 0xe81548: DecompressPointer r4
    //     0xe81548: add             x4, x4, HEAP, lsl #32
    // 0xe8154c: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0xe8154c: add             x16, x4, x1, lsl #2
    //     0xe81550: ldur            w5, [x16, #0xf]
    // 0xe81554: DecompressPointer r5
    //     0xe81554: add             x5, x5, HEAP, lsl #32
    // 0xe81558: add             x4, x1, #1
    // 0xe8155c: mov             x1, x5
    // 0xe81560: stur            x4, [fp, #-0x20]
    // 0xe81564: r0 = _legacyRef()
    //     0xe81564: bl              #0xe81628  ; [package:pdf/src/pdf/format/xref.dart] PdfXref::_legacyRef
    // 0xe81568: r1 = <int>
    //     0xe81568: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8156c: stur            x0, [fp, #-8]
    // 0xe81570: r0 = CodeUnits()
    //     0xe81570: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0xe81574: mov             x1, x0
    // 0xe81578: ldur            x0, [fp, #-8]
    // 0xe8157c: StoreField: r1->field_b = r0
    //     0xe8157c: stur            w0, [x1, #0xb]
    // 0xe81580: mov             x2, x1
    // 0xe81584: ldur            x1, [fp, #-0x10]
    // 0xe81588: r0 = putBytes()
    //     0xe81588: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe8158c: ldur            x1, [fp, #-0x10]
    // 0xe81590: r2 = 1
    //     0xe81590: movz            x2, #0x1
    // 0xe81594: r0 = _ensureCapacity()
    //     0xe81594: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xe81598: ldur            x2, [fp, #-0x10]
    // 0xe8159c: LoadField: r3 = r2->field_7
    //     0xe8159c: ldur            w3, [x2, #7]
    // 0xe815a0: DecompressPointer r3
    //     0xe815a0: add             x3, x3, HEAP, lsl #32
    // 0xe815a4: LoadField: r4 = r2->field_b
    //     0xe815a4: ldur            x4, [x2, #0xb]
    // 0xe815a8: add             x0, x4, #1
    // 0xe815ac: StoreField: r2->field_b = r0
    //     0xe815ac: stur            x0, [x2, #0xb]
    // 0xe815b0: LoadField: r0 = r3->field_13
    //     0xe815b0: ldur            w0, [x3, #0x13]
    // 0xe815b4: r1 = LoadInt32Instr(r0)
    //     0xe815b4: sbfx            x1, x0, #1, #0x1f
    // 0xe815b8: mov             x0, x1
    // 0xe815bc: mov             x1, x4
    // 0xe815c0: cmp             x1, x0
    // 0xe815c4: b.hs            #0xe81624
    // 0xe815c8: r0 = 10
    //     0xe815c8: movz            x0, #0xa
    // 0xe815cc: ArrayStore: r3[r4] = r0  ; TypeUnknown_1
    //     0xe815cc: add             x1, x3, x4
    //     0xe815d0: strb            w0, [x1, #0x17]
    // 0xe815d4: ldur            x1, [fp, #-0x20]
    // 0xe815d8: mov             x3, x2
    // 0xe815dc: ldur            x0, [fp, #-0x18]
    // 0xe815e0: ldur            x2, [fp, #-0x28]
    // 0xe815e4: b               #0xe81520
    // 0xe815e8: r0 = Null
    //     0xe815e8: mov             x0, NULL
    // 0xe815ec: LeaveFrame
    //     0xe815ec: mov             SP, fp
    //     0xe815f0: ldp             fp, lr, [SP], #0x10
    // 0xe815f4: ret
    //     0xe815f4: ret             
    // 0xe815f8: r0 = ConcurrentModificationError()
    //     0xe815f8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe815fc: mov             x1, x0
    // 0xe81600: ldur            x0, [fp, #-0x18]
    // 0xe81604: StoreField: r1->field_b = r0
    //     0xe81604: stur            w0, [x1, #0xb]
    // 0xe81608: mov             x0, x1
    // 0xe8160c: r0 = Throw()
    //     0xe8160c: bl              #0xec04b8  ; ThrowStub
    // 0xe81610: brk             #0
    // 0xe81614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81614: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81618: b               #0xe814a8
    // 0xe8161c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8161c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81620: b               #0xe8152c
    // 0xe81624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe81624: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ PdfXrefTable(/* No info */) {
    // ** addr: 0xe88efc, size: 0x120
    // 0xe88efc: EnterFrame
    //     0xe88efc: stp             fp, lr, [SP, #-0x10]!
    //     0xe88f00: mov             fp, SP
    // 0xe88f04: AllocStack(0x20)
    //     0xe88f04: sub             SP, SP, #0x20
    // 0xe88f08: SetupParameters(PdfXrefTable this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe88f08: mov             x0, x1
    //     0xe88f0c: stur            x1, [fp, #-8]
    //     0xe88f10: stur            x2, [fp, #-0x10]
    // 0xe88f14: CheckStackOverflow
    //     0xe88f14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88f18: cmp             SP, x16
    //     0xe88f1c: b.ls            #0xe89014
    // 0xe88f20: r1 = <PdfDataType>
    //     0xe88f20: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe88f24: ldr             x1, [x1, #0x4c8]
    // 0xe88f28: r0 = PdfDict()
    //     0xe88f28: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe88f2c: mov             x1, x0
    // 0xe88f30: stur            x0, [fp, #-0x18]
    // 0xe88f34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe88f34: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe88f38: r0 = PdfDict()
    //     0xe88f38: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0xe88f3c: ldur            x0, [fp, #-0x18]
    // 0xe88f40: ldur            x1, [fp, #-8]
    // 0xe88f44: StoreField: r1->field_7 = r0
    //     0xe88f44: stur            w0, [x1, #7]
    //     0xe88f48: ldurb           w16, [x1, #-1]
    //     0xe88f4c: ldurb           w17, [x0, #-1]
    //     0xe88f50: and             x16, x17, x16, lsr #2
    //     0xe88f54: tst             x16, HEAP, lsr #32
    //     0xe88f58: b.eq            #0xe88f60
    //     0xe88f5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe88f60: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xe88f60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe88f64: ldr             x0, [x0, #0x778]
    //     0xe88f68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe88f6c: cmp             w0, w16
    //     0xe88f70: b.ne            #0xe88f7c
    //     0xe88f74: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xe88f78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe88f7c: r1 = <PdfObjectBase<PdfDataType>>
    //     0xe88f7c: add             x1, PP, #0x36, lsl #12  ; [pp+0x366f0] TypeArguments: <PdfObjectBase<PdfDataType>>
    //     0xe88f80: ldr             x1, [x1, #0x6f0]
    // 0xe88f84: stur            x0, [fp, #-0x18]
    // 0xe88f88: r0 = _Set()
    //     0xe88f88: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xe88f8c: mov             x1, x0
    // 0xe88f90: ldur            x0, [fp, #-0x18]
    // 0xe88f94: stur            x1, [fp, #-0x20]
    // 0xe88f98: StoreField: r1->field_1b = r0
    //     0xe88f98: stur            w0, [x1, #0x1b]
    // 0xe88f9c: StoreField: r1->field_b = rZR
    //     0xe88f9c: stur            wzr, [x1, #0xb]
    // 0xe88fa0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xe88fa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe88fa4: ldr             x0, [x0, #0x780]
    //     0xe88fa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe88fac: cmp             w0, w16
    //     0xe88fb0: b.ne            #0xe88fbc
    //     0xe88fb4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xe88fb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe88fbc: mov             x1, x0
    // 0xe88fc0: ldur            x0, [fp, #-0x20]
    // 0xe88fc4: StoreField: r0->field_f = r1
    //     0xe88fc4: stur            w1, [x0, #0xf]
    // 0xe88fc8: StoreField: r0->field_13 = rZR
    //     0xe88fc8: stur            wzr, [x0, #0x13]
    // 0xe88fcc: ArrayStore: r0[0] = rZR  ; List_4
    //     0xe88fcc: stur            wzr, [x0, #0x17]
    // 0xe88fd0: ldur            x1, [fp, #-8]
    // 0xe88fd4: StoreField: r1->field_b = r0
    //     0xe88fd4: stur            w0, [x1, #0xb]
    //     0xe88fd8: ldurb           w16, [x1, #-1]
    //     0xe88fdc: ldurb           w17, [x0, #-1]
    //     0xe88fe0: and             x16, x17, x16, lsr #2
    //     0xe88fe4: tst             x16, HEAP, lsr #32
    //     0xe88fe8: b.eq            #0xe88ff0
    //     0xe88fec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe88ff0: ldur            x0, [fp, #-0x10]
    // 0xe88ff4: StoreField: r1->field_f = r0
    //     0xe88ff4: stur            x0, [x1, #0xf]
    // 0xe88ff8: r1 = <String>
    //     0xe88ff8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe88ffc: r2 = 0
    //     0xe88ffc: movz            x2, #0
    // 0xe89000: r0 = _GrowableList()
    //     0xe89000: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe89004: r0 = Null
    //     0xe89004: mov             x0, NULL
    // 0xe89008: LeaveFrame
    //     0xe89008: mov             SP, fp
    //     0xe8900c: ldp             fp, lr, [SP], #0x10
    // 0xe89010: ret
    //     0xe89010: ret             
    // 0xe89014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe89014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe89018: b               #0xe88f20
  }
}

// class id: 911, size: 0x28, field offset: 0x18
//   const constructor, 
class PdfXref extends PdfIndirect {

  _Mint field_8;
  _Mint field_10;
  _Mint field_18;
  PdfCrossRefEntryType field_24;

  _ toString(/* No info */) {
    // ** addr: 0xc35234, size: 0xe8
    // 0xc35234: EnterFrame
    //     0xc35234: stp             fp, lr, [SP, #-0x10]!
    //     0xc35238: mov             fp, SP
    // 0xc3523c: AllocStack(0x18)
    //     0xc3523c: sub             SP, SP, #0x18
    // 0xc35240: SetupParameters(PdfXref this /* r3, fp-0x10 */)
    //     0xc35240: ldur            w0, [x4, #0x13]
    //     0xc35244: sub             x1, x0, #2
    //     0xc35248: add             x3, fp, w1, sxtw #2
    //     0xc3524c: ldr             x3, [x3, #0x10]
    //     0xc35250: stur            x3, [fp, #-0x10]
    // 0xc35254: CheckStackOverflow
    //     0xc35254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35258: cmp             SP, x16
    //     0xc3525c: b.ls            #0xc35314
    // 0xc35260: LoadField: r2 = r3->field_7
    //     0xc35260: ldur            x2, [x3, #7]
    // 0xc35264: r0 = BoxInt64Instr(r2)
    //     0xc35264: sbfiz           x0, x2, #1, #0x1f
    //     0xc35268: cmp             x2, x0, asr #1
    //     0xc3526c: b.eq            #0xc35278
    //     0xc35270: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35274: stur            x2, [x0, #7]
    // 0xc35278: r1 = Null
    //     0xc35278: mov             x1, NULL
    // 0xc3527c: r2 = 14
    //     0xc3527c: movz            x2, #0xe
    // 0xc35280: stur            x0, [fp, #-8]
    // 0xc35284: r0 = AllocateArray()
    //     0xc35284: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35288: mov             x2, x0
    // 0xc3528c: ldur            x0, [fp, #-8]
    // 0xc35290: StoreField: r2->field_f = r0
    //     0xc35290: stur            w0, [x2, #0xf]
    // 0xc35294: r16 = " "
    //     0xc35294: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35298: StoreField: r2->field_13 = r16
    //     0xc35298: stur            w16, [x2, #0x13]
    // 0xc3529c: ldur            x3, [fp, #-0x10]
    // 0xc352a0: LoadField: r4 = r3->field_f
    //     0xc352a0: ldur            x4, [x3, #0xf]
    // 0xc352a4: r0 = BoxInt64Instr(r4)
    //     0xc352a4: sbfiz           x0, x4, #1, #0x1f
    //     0xc352a8: cmp             x4, x0, asr #1
    //     0xc352ac: b.eq            #0xc352b8
    //     0xc352b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc352b4: stur            x4, [x0, #7]
    // 0xc352b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc352b8: stur            w0, [x2, #0x17]
    // 0xc352bc: r16 = " obj "
    //     0xc352bc: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c1b8] " obj "
    //     0xc352c0: ldr             x16, [x16, #0x1b8]
    // 0xc352c4: StoreField: r2->field_1b = r16
    //     0xc352c4: stur            w16, [x2, #0x1b]
    // 0xc352c8: LoadField: r0 = r3->field_23
    //     0xc352c8: ldur            w0, [x3, #0x23]
    // 0xc352cc: DecompressPointer r0
    //     0xc352cc: add             x0, x0, HEAP, lsl #32
    // 0xc352d0: LoadField: r1 = r0->field_f
    //     0xc352d0: ldur            w1, [x0, #0xf]
    // 0xc352d4: DecompressPointer r1
    //     0xc352d4: add             x1, x1, HEAP, lsl #32
    // 0xc352d8: StoreField: r2->field_1f = r1
    //     0xc352d8: stur            w1, [x2, #0x1f]
    // 0xc352dc: r16 = " "
    //     0xc352dc: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc352e0: StoreField: r2->field_23 = r16
    //     0xc352e0: stur            w16, [x2, #0x23]
    // 0xc352e4: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xc352e4: ldur            x4, [x3, #0x17]
    // 0xc352e8: r0 = BoxInt64Instr(r4)
    //     0xc352e8: sbfiz           x0, x4, #1, #0x1f
    //     0xc352ec: cmp             x4, x0, asr #1
    //     0xc352f0: b.eq            #0xc352fc
    //     0xc352f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc352f8: stur            x4, [x0, #7]
    // 0xc352fc: StoreField: r2->field_27 = r0
    //     0xc352fc: stur            w0, [x2, #0x27]
    // 0xc35300: str             x2, [SP]
    // 0xc35304: r0 = _interpolate()
    //     0xc35304: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35308: LeaveFrame
    //     0xc35308: mov             SP, fp
    //     0xc3530c: ldp             fp, lr, [SP], #0x10
    // 0xc35310: ret
    //     0xc35310: ret             
    // 0xc35314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35318: b               #0xc35260
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c668, size: 0x54
    // 0xd7c668: ldr             x1, [SP]
    // 0xd7c66c: cmp             w1, NULL
    // 0xd7c670: b.ne            #0xd7c67c
    // 0xd7c674: r0 = false
    //     0xd7c674: add             x0, NULL, #0x30  ; false
    // 0xd7c678: ret
    //     0xd7c678: ret             
    // 0xd7c67c: r2 = 60
    //     0xd7c67c: movz            x2, #0x3c
    // 0xd7c680: branchIfSmi(r1, 0xd7c68c)
    //     0xd7c680: tbz             w1, #0, #0xd7c68c
    // 0xd7c684: r2 = LoadClassIdInstr(r1)
    //     0xd7c684: ldur            x2, [x1, #-1]
    //     0xd7c688: ubfx            x2, x2, #0xc, #0x14
    // 0xd7c68c: cmp             x2, #0x38f
    // 0xd7c690: b.ne            #0xd7c6b4
    // 0xd7c694: ldr             x2, [SP, #8]
    // 0xd7c698: ArrayLoad: r3 = r2[0]  ; List_8
    //     0xd7c698: ldur            x3, [x2, #0x17]
    // 0xd7c69c: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xd7c69c: ldur            x2, [x1, #0x17]
    // 0xd7c6a0: cmp             x3, x2
    // 0xd7c6a4: r16 = true
    //     0xd7c6a4: add             x16, NULL, #0x20  ; true
    // 0xd7c6a8: r17 = false
    //     0xd7c6a8: add             x17, NULL, #0x30  ; false
    // 0xd7c6ac: csel            x0, x16, x17, eq
    // 0xd7c6b0: ret
    //     0xd7c6b0: ret             
    // 0xd7c6b4: r0 = false
    //     0xd7c6b4: add             x0, NULL, #0x30  ; false
    // 0xd7c6b8: ret
    //     0xd7c6b8: ret             
  }
  _ _compressedRef(/* No info */) {
    // ** addr: 0xe80d48, size: 0x314
    // 0xe80d48: EnterFrame
    //     0xe80d48: stp             fp, lr, [SP, #-0x10]!
    //     0xe80d4c: mov             fp, SP
    // 0xe80d50: mov             x4, x1
    // 0xe80d54: LoadField: r6 = r5->field_b
    //     0xe80d54: ldur            w6, [x5, #0xb]
    // 0xe80d58: r7 = LoadInt32Instr(r6)
    //     0xe80d58: sbfx            x7, x6, #1, #0x1f
    // 0xe80d5c: mov             x0, x7
    // 0xe80d60: r1 = 0
    //     0xe80d60: movz            x1, #0
    // 0xe80d64: cmp             x1, x0
    // 0xe80d68: b.hs            #0xe80f80
    // 0xe80d6c: LoadField: r6 = r5->field_f
    //     0xe80d6c: ldur            w6, [x5, #0xf]
    // 0xe80d70: DecompressPointer r6
    //     0xe80d70: add             x6, x6, HEAP, lsl #32
    // 0xe80d74: LoadField: r5 = r6->field_f
    //     0xe80d74: ldur            w5, [x6, #0xf]
    // 0xe80d78: DecompressPointer r5
    //     0xe80d78: add             x5, x5, HEAP, lsl #32
    // 0xe80d7c: LoadField: r8 = r4->field_23
    //     0xe80d7c: ldur            w8, [x4, #0x23]
    // 0xe80d80: DecompressPointer r8
    //     0xe80d80: add             x8, x8, HEAP, lsl #32
    // 0xe80d84: r16 = Instance_PdfCrossRefEntryType
    //     0xe80d84: add             x16, PP, #0x36, lsl #12  ; [pp+0x36608] Obj!PdfCrossRefEntryType@e2f021
    //     0xe80d88: ldr             x16, [x16, #0x608]
    // 0xe80d8c: cmp             w8, w16
    // 0xe80d90: cset            x9, eq
    // 0xe80d94: lsl             x9, x9, #1
    // 0xe80d98: r8 = LoadInt32Instr(r5)
    //     0xe80d98: sbfx            x8, x5, #1, #0x1f
    //     0xe80d9c: tbz             w5, #0, #0xe80da4
    //     0xe80da0: ldur            x8, [x5, #7]
    // 0xe80da4: r5 = LoadInt32Instr(r9)
    //     0xe80da4: sbfx            x5, x9, #1, #0x1f
    // 0xe80da8: LoadField: r9 = r2->field_13
    //     0xe80da8: ldur            w9, [x2, #0x13]
    // 0xe80dac: r10 = LoadInt32Instr(r9)
    //     0xe80dac: sbfx            x10, x9, #1, #0x1f
    // 0xe80db0: ArrayLoad: r11 = r2[0]  ; List_4
    //     0xe80db0: ldur            w11, [x2, #0x17]
    // 0xe80db4: DecompressPointer r11
    //     0xe80db4: add             x11, x11, HEAP, lsl #32
    // 0xe80db8: LoadField: r12 = r2->field_1b
    //     0xe80db8: ldur            w12, [x2, #0x1b]
    // 0xe80dbc: r2 = LoadInt32Instr(r12)
    //     0xe80dbc: sbfx            x2, x12, #1, #0x1f
    // 0xe80dc0: mov             x13, x3
    // 0xe80dc4: r14 = 0
    //     0xe80dc4: movz            x14, #0
    // 0xe80dc8: r3 = 255
    //     0xe80dc8: movz            x3, #0xff
    // 0xe80dcc: CheckStackOverflow
    //     0xe80dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80dd0: cmp             SP, x16
    //     0xe80dd4: b.ls            #0xe80f84
    // 0xe80dd8: cmp             x14, x8
    // 0xe80ddc: b.ge            #0xe80e34
    // 0xe80de0: sub             x19, x8, x14
    // 0xe80de4: sub             x20, x19, #1
    // 0xe80de8: lsl             x19, x20, #3
    // 0xe80dec: tbnz            x19, #0x3f, #0xe80f8c
    // 0xe80df0: lsr             w20, w5, w19
    // 0xe80df4: cmp             x19, #0x1f
    // 0xe80df8: csel            x20, x20, xzr, le
    // 0xe80dfc: and             x19, x20, x3
    // 0xe80e00: mov             x0, x10
    // 0xe80e04: mov             x1, x13
    // 0xe80e08: cmp             x1, x0
    // 0xe80e0c: b.hs            #0xe80fc0
    // 0xe80e10: add             x20, x2, x13
    // 0xe80e14: ubfx            x19, x19, #0, #0x20
    // 0xe80e18: LoadField: r23 = r11->field_7
    //     0xe80e18: ldur            x23, [x11, #7]
    // 0xe80e1c: strb            w19, [x23, x20]
    // 0xe80e20: add             x0, x13, #1
    // 0xe80e24: add             x1, x14, #1
    // 0xe80e28: mov             x14, x1
    // 0xe80e2c: mov             x13, x0
    // 0xe80e30: b               #0xe80dcc
    // 0xe80e34: mov             x0, x7
    // 0xe80e38: r1 = 1
    //     0xe80e38: movz            x1, #0x1
    // 0xe80e3c: cmp             x1, x0
    // 0xe80e40: b.hs            #0xe80fc4
    // 0xe80e44: LoadField: r2 = r6->field_13
    //     0xe80e44: ldur            w2, [x6, #0x13]
    // 0xe80e48: DecompressPointer r2
    //     0xe80e48: add             x2, x2, HEAP, lsl #32
    // 0xe80e4c: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xe80e4c: ldur            x5, [x4, #0x17]
    // 0xe80e50: r8 = LoadInt32Instr(r2)
    //     0xe80e50: sbfx            x8, x2, #1, #0x1f
    //     0xe80e54: tbz             w2, #0, #0xe80e5c
    //     0xe80e58: ldur            x8, [x2, #7]
    // 0xe80e5c: r2 = LoadInt32Instr(r9)
    //     0xe80e5c: sbfx            x2, x9, #1, #0x1f
    // 0xe80e60: r10 = LoadInt32Instr(r12)
    //     0xe80e60: sbfx            x10, x12, #1, #0x1f
    // 0xe80e64: r14 = 0
    //     0xe80e64: movz            x14, #0
    // 0xe80e68: CheckStackOverflow
    //     0xe80e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80e6c: cmp             SP, x16
    //     0xe80e70: b.ls            #0xe80fc8
    // 0xe80e74: cmp             x14, x8
    // 0xe80e78: b.ge            #0xe80ed0
    // 0xe80e7c: sub             x19, x8, x14
    // 0xe80e80: sub             x20, x19, #1
    // 0xe80e84: lsl             x19, x20, #3
    // 0xe80e88: cmp             x19, #0x3f
    // 0xe80e8c: b.hi            #0xe80fd0
    // 0xe80e90: asr             x20, x5, x19
    // 0xe80e94: ubfx            x20, x20, #0, #0x20
    // 0xe80e98: and             x19, x20, x3
    // 0xe80e9c: mov             x0, x2
    // 0xe80ea0: mov             x1, x13
    // 0xe80ea4: cmp             x1, x0
    // 0xe80ea8: b.hs            #0xe81010
    // 0xe80eac: add             x20, x10, x13
    // 0xe80eb0: ubfx            x19, x19, #0, #0x20
    // 0xe80eb4: LoadField: r23 = r11->field_7
    //     0xe80eb4: ldur            x23, [x11, #7]
    // 0xe80eb8: strb            w19, [x23, x20]
    // 0xe80ebc: add             x0, x13, #1
    // 0xe80ec0: add             x1, x14, #1
    // 0xe80ec4: mov             x14, x1
    // 0xe80ec8: mov             x13, x0
    // 0xe80ecc: b               #0xe80e68
    // 0xe80ed0: mov             x0, x7
    // 0xe80ed4: r1 = 2
    //     0xe80ed4: movz            x1, #0x2
    // 0xe80ed8: cmp             x1, x0
    // 0xe80edc: b.hs            #0xe81014
    // 0xe80ee0: ArrayLoad: r2 = r6[0]  ; List_4
    //     0xe80ee0: ldur            w2, [x6, #0x17]
    // 0xe80ee4: DecompressPointer r2
    //     0xe80ee4: add             x2, x2, HEAP, lsl #32
    // 0xe80ee8: LoadField: r5 = r4->field_f
    //     0xe80ee8: ldur            x5, [x4, #0xf]
    // 0xe80eec: r4 = LoadInt32Instr(r2)
    //     0xe80eec: sbfx            x4, x2, #1, #0x1f
    //     0xe80ef0: tbz             w2, #0, #0xe80ef8
    //     0xe80ef4: ldur            x4, [x2, #7]
    // 0xe80ef8: r2 = LoadInt32Instr(r9)
    //     0xe80ef8: sbfx            x2, x9, #1, #0x1f
    // 0xe80efc: r6 = LoadInt32Instr(r12)
    //     0xe80efc: sbfx            x6, x12, #1, #0x1f
    // 0xe80f00: mov             x7, x13
    // 0xe80f04: r8 = 0
    //     0xe80f04: movz            x8, #0
    // 0xe80f08: CheckStackOverflow
    //     0xe80f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe80f0c: cmp             SP, x16
    //     0xe80f10: b.ls            #0xe81018
    // 0xe80f14: cmp             x8, x4
    // 0xe80f18: b.ge            #0xe80f70
    // 0xe80f1c: sub             x9, x4, x8
    // 0xe80f20: sub             x10, x9, #1
    // 0xe80f24: lsl             x9, x10, #3
    // 0xe80f28: cmp             x9, #0x3f
    // 0xe80f2c: b.hi            #0xe81020
    // 0xe80f30: asr             x10, x5, x9
    // 0xe80f34: ubfx            x10, x10, #0, #0x20
    // 0xe80f38: and             x9, x10, x3
    // 0xe80f3c: mov             x0, x2
    // 0xe80f40: mov             x1, x7
    // 0xe80f44: cmp             x1, x0
    // 0xe80f48: b.hs            #0xe81058
    // 0xe80f4c: add             x1, x6, x7
    // 0xe80f50: ubfx            x9, x9, #0, #0x20
    // 0xe80f54: LoadField: r10 = r11->field_7
    //     0xe80f54: ldur            x10, [x11, #7]
    // 0xe80f58: strb            w9, [x10, x1]
    // 0xe80f5c: add             x0, x7, #1
    // 0xe80f60: add             x1, x8, #1
    // 0xe80f64: mov             x8, x1
    // 0xe80f68: mov             x7, x0
    // 0xe80f6c: b               #0xe80f08
    // 0xe80f70: mov             x0, x7
    // 0xe80f74: LeaveFrame
    //     0xe80f74: mov             SP, fp
    //     0xe80f78: ldp             fp, lr, [SP], #0x10
    // 0xe80f7c: ret
    //     0xe80f7c: ret             
    // 0xe80f80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80f80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80f84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80f88: b               #0xe80dd8
    // 0xe80f8c: str             x19, [THR, #0x7a8]  ; THR::
    // 0xe80f90: stp             x14, x19, [SP, #-0x10]!
    // 0xe80f94: stp             x12, x13, [SP, #-0x10]!
    // 0xe80f98: stp             x10, x11, [SP, #-0x10]!
    // 0xe80f9c: stp             x8, x9, [SP, #-0x10]!
    // 0xe80fa0: stp             x6, x7, [SP, #-0x10]!
    // 0xe80fa4: stp             x4, x5, [SP, #-0x10]!
    // 0xe80fa8: stp             x2, x3, [SP, #-0x10]!
    // 0xe80fac: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe80fb0: r4 = 0
    //     0xe80fb0: movz            x4, #0
    // 0xe80fb4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe80fb8: blr             lr
    // 0xe80fbc: brk             #0
    // 0xe80fc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80fc0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80fc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe80fc4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe80fc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe80fc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe80fcc: b               #0xe80e74
    // 0xe80fd0: tbnz            x19, #0x3f, #0xe80fdc
    // 0xe80fd4: asr             x20, x5, #0x3f
    // 0xe80fd8: b               #0xe80e94
    // 0xe80fdc: str             x19, [THR, #0x7a8]  ; THR::
    // 0xe80fe0: stp             x14, x19, [SP, #-0x10]!
    // 0xe80fe4: stp             x12, x13, [SP, #-0x10]!
    // 0xe80fe8: stp             x10, x11, [SP, #-0x10]!
    // 0xe80fec: stp             x8, x9, [SP, #-0x10]!
    // 0xe80ff0: stp             x6, x7, [SP, #-0x10]!
    // 0xe80ff4: stp             x4, x5, [SP, #-0x10]!
    // 0xe80ff8: stp             x2, x3, [SP, #-0x10]!
    // 0xe80ffc: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe81000: r4 = 0
    //     0xe81000: movz            x4, #0
    // 0xe81004: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe81008: blr             lr
    // 0xe8100c: brk             #0
    // 0xe81010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe81010: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe81014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe81014: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe81018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8101c: b               #0xe80f14
    // 0xe81020: tbnz            x9, #0x3f, #0xe8102c
    // 0xe81024: asr             x10, x5, #0x3f
    // 0xe81028: b               #0xe80f34
    // 0xe8102c: str             x9, [THR, #0x7a8]  ; THR::
    // 0xe81030: stp             x9, x11, [SP, #-0x10]!
    // 0xe81034: stp             x7, x8, [SP, #-0x10]!
    // 0xe81038: stp             x5, x6, [SP, #-0x10]!
    // 0xe8103c: stp             x3, x4, [SP, #-0x10]!
    // 0xe81040: SaveReg r2
    //     0xe81040: str             x2, [SP, #-8]!
    // 0xe81044: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe81048: r4 = 0
    //     0xe81048: movz            x4, #0
    // 0xe8104c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe81050: blr             lr
    // 0xe81054: brk             #0
    // 0xe81058: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe81058: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _legacyRef(/* No info */) {
    // ** addr: 0xe81628, size: 0x188
    // 0xe81628: EnterFrame
    //     0xe81628: stp             fp, lr, [SP, #-0x10]!
    //     0xe8162c: mov             fp, SP
    // 0xe81630: AllocStack(0x20)
    //     0xe81630: sub             SP, SP, #0x20
    // 0xe81634: SetupParameters(PdfXref this /* r1 => r2, fp-0x8 */)
    //     0xe81634: mov             x2, x1
    //     0xe81638: stur            x1, [fp, #-8]
    // 0xe8163c: CheckStackOverflow
    //     0xe8163c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81640: cmp             SP, x16
    //     0xe81644: b.ls            #0xe817a8
    // 0xe81648: ArrayLoad: r3 = r2[0]  ; List_8
    //     0xe81648: ldur            x3, [x2, #0x17]
    // 0xe8164c: r0 = BoxInt64Instr(r3)
    //     0xe8164c: sbfiz           x0, x3, #1, #0x1f
    //     0xe81650: cmp             x3, x0, asr #1
    //     0xe81654: b.eq            #0xe81660
    //     0xe81658: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8165c: stur            x3, [x0, #7]
    // 0xe81660: r1 = 60
    //     0xe81660: movz            x1, #0x3c
    // 0xe81664: branchIfSmi(r0, 0xe81670)
    //     0xe81664: tbz             w0, #0, #0xe81670
    // 0xe81668: r1 = LoadClassIdInstr(r0)
    //     0xe81668: ldur            x1, [x0, #-1]
    //     0xe8166c: ubfx            x1, x1, #0xc, #0x14
    // 0xe81670: str             x0, [SP]
    // 0xe81674: mov             x0, x1
    // 0xe81678: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe81678: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe8167c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe8167c: movz            x17, #0x2b03
    //     0xe81680: add             lr, x0, x17
    //     0xe81684: ldr             lr, [x21, lr, lsl #3]
    //     0xe81688: blr             lr
    // 0xe8168c: r1 = LoadClassIdInstr(r0)
    //     0xe8168c: ldur            x1, [x0, #-1]
    //     0xe81690: ubfx            x1, x1, #0xc, #0x14
    // 0xe81694: mov             x16, x0
    // 0xe81698: mov             x0, x1
    // 0xe8169c: mov             x1, x16
    // 0xe816a0: r2 = 10
    //     0xe816a0: movz            x2, #0xa
    // 0xe816a4: r3 = "0"
    //     0xe816a4: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xe816a8: r0 = GDT[cid_x0 + -0xff8]()
    //     0xe816a8: sub             lr, x0, #0xff8
    //     0xe816ac: ldr             lr, [x21, lr, lsl #3]
    //     0xe816b0: blr             lr
    // 0xe816b4: r1 = Null
    //     0xe816b4: mov             x1, NULL
    // 0xe816b8: r2 = 8
    //     0xe816b8: movz            x2, #0x8
    // 0xe816bc: stur            x0, [fp, #-0x10]
    // 0xe816c0: r0 = AllocateArray()
    //     0xe816c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe816c4: mov             x2, x0
    // 0xe816c8: ldur            x0, [fp, #-0x10]
    // 0xe816cc: stur            x2, [fp, #-0x18]
    // 0xe816d0: StoreField: r2->field_f = r0
    //     0xe816d0: stur            w0, [x2, #0xf]
    // 0xe816d4: r16 = " "
    //     0xe816d4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe816d8: StoreField: r2->field_13 = r16
    //     0xe816d8: stur            w16, [x2, #0x13]
    // 0xe816dc: ldur            x3, [fp, #-8]
    // 0xe816e0: LoadField: r4 = r3->field_f
    //     0xe816e0: ldur            x4, [x3, #0xf]
    // 0xe816e4: r0 = BoxInt64Instr(r4)
    //     0xe816e4: sbfiz           x0, x4, #1, #0x1f
    //     0xe816e8: cmp             x4, x0, asr #1
    //     0xe816ec: b.eq            #0xe816f8
    //     0xe816f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe816f4: stur            x4, [x0, #7]
    // 0xe816f8: str             x0, [SP]
    // 0xe816fc: r0 = toString()
    //     0xe816fc: bl              #0xc460ec  ; [dart:core] _Smi::toString
    // 0xe81700: mov             x1, x0
    // 0xe81704: r2 = 5
    //     0xe81704: movz            x2, #0x5
    // 0xe81708: r3 = "0"
    //     0xe81708: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xe8170c: r0 = padLeft()
    //     0xe8170c: bl              #0xebe370  ; [dart:core] _OneByteString::padLeft
    // 0xe81710: ldur            x1, [fp, #-0x18]
    // 0xe81714: ArrayStore: r1[2] = r0  ; List_4
    //     0xe81714: add             x25, x1, #0x17
    //     0xe81718: str             w0, [x25]
    //     0xe8171c: tbz             w0, #0, #0xe81738
    //     0xe81720: ldurb           w16, [x1, #-1]
    //     0xe81724: ldurb           w17, [x0, #-1]
    //     0xe81728: and             x16, x17, x16, lsr #2
    //     0xe8172c: tst             x16, HEAP, lsr #32
    //     0xe81730: b.eq            #0xe81738
    //     0xe81734: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe81738: ldur            x0, [fp, #-8]
    // 0xe8173c: LoadField: r1 = r0->field_23
    //     0xe8173c: ldur            w1, [x0, #0x23]
    // 0xe81740: DecompressPointer r1
    //     0xe81740: add             x1, x1, HEAP, lsl #32
    // 0xe81744: r16 = Instance_PdfCrossRefEntryType
    //     0xe81744: add             x16, PP, #0x36, lsl #12  ; [pp+0x36608] Obj!PdfCrossRefEntryType@e2f021
    //     0xe81748: ldr             x16, [x16, #0x608]
    // 0xe8174c: cmp             w1, w16
    // 0xe81750: b.ne            #0xe81760
    // 0xe81754: r0 = " n "
    //     0xe81754: add             x0, PP, #0x36, lsl #12  ; [pp+0x366b8] " n "
    //     0xe81758: ldr             x0, [x0, #0x6b8]
    // 0xe8175c: b               #0xe81768
    // 0xe81760: r0 = " f "
    //     0xe81760: add             x0, PP, #0x36, lsl #12  ; [pp+0x366c0] " f "
    //     0xe81764: ldr             x0, [x0, #0x6c0]
    // 0xe81768: ldur            x1, [fp, #-0x18]
    // 0xe8176c: ArrayStore: r1[3] = r0  ; List_4
    //     0xe8176c: add             x25, x1, #0x1b
    //     0xe81770: str             w0, [x25]
    //     0xe81774: tbz             w0, #0, #0xe81790
    //     0xe81778: ldurb           w16, [x1, #-1]
    //     0xe8177c: ldurb           w17, [x0, #-1]
    //     0xe81780: and             x16, x17, x16, lsr #2
    //     0xe81784: tst             x16, HEAP, lsr #32
    //     0xe81788: b.eq            #0xe81790
    //     0xe8178c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe81790: ldur            x16, [fp, #-0x18]
    // 0xe81794: str             x16, [SP]
    // 0xe81798: r0 = _interpolate()
    //     0xe81798: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe8179c: LeaveFrame
    //     0xe8179c: mov             SP, fp
    //     0xe817a0: ldp             fp, lr, [SP], #0x10
    // 0xe817a4: ret
    //     0xe817a4: ret             
    // 0xe817a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe817a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe817ac: b               #0xe81648
  }
}

// class id: 6816, size: 0x14, field offset: 0x14
enum PdfCrossRefEntryType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d94c, size: 0x64
    // 0xc4d94c: EnterFrame
    //     0xc4d94c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d950: mov             fp, SP
    // 0xc4d954: AllocStack(0x10)
    //     0xc4d954: sub             SP, SP, #0x10
    // 0xc4d958: SetupParameters(PdfCrossRefEntryType this /* r1 => r0, fp-0x8 */)
    //     0xc4d958: mov             x0, x1
    //     0xc4d95c: stur            x1, [fp, #-8]
    // 0xc4d960: CheckStackOverflow
    //     0xc4d960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d964: cmp             SP, x16
    //     0xc4d968: b.ls            #0xc4d9a8
    // 0xc4d96c: r1 = Null
    //     0xc4d96c: mov             x1, NULL
    // 0xc4d970: r2 = 4
    //     0xc4d970: movz            x2, #0x4
    // 0xc4d974: r0 = AllocateArray()
    //     0xc4d974: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d978: r16 = "PdfCrossRefEntryType."
    //     0xc4d978: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c1c0] "PdfCrossRefEntryType."
    //     0xc4d97c: ldr             x16, [x16, #0x1c0]
    // 0xc4d980: StoreField: r0->field_f = r16
    //     0xc4d980: stur            w16, [x0, #0xf]
    // 0xc4d984: ldur            x1, [fp, #-8]
    // 0xc4d988: LoadField: r2 = r1->field_f
    //     0xc4d988: ldur            w2, [x1, #0xf]
    // 0xc4d98c: DecompressPointer r2
    //     0xc4d98c: add             x2, x2, HEAP, lsl #32
    // 0xc4d990: StoreField: r0->field_13 = r2
    //     0xc4d990: stur            w2, [x0, #0x13]
    // 0xc4d994: str             x0, [SP]
    // 0xc4d998: r0 = _interpolate()
    //     0xc4d998: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d99c: LeaveFrame
    //     0xc4d99c: mov             SP, fp
    //     0xc4d9a0: ldp             fp, lr, [SP], #0x10
    // 0xc4d9a4: ret
    //     0xc4d9a4: ret             
    // 0xc4d9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d9a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d9ac: b               #0xc4d96c
  }
}
