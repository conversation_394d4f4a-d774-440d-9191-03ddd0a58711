// lib: , url: package:pdf/src/pdf/graphic_state.dart

// class id: 1050793, size: 0x8
class :: {
}

// class id: 865, size: 0x1c, field offset: 0x8
//   const constructor, 
class PdfGraphicState extends Object {

  _ output(/* No info */) {
    // ** addr: 0x7b594c, size: 0x23c
    // 0x7b594c: EnterFrame
    //     0x7b594c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5950: mov             fp, SP
    // 0x7b5954: AllocStack(0x28)
    //     0x7b5954: sub             SP, SP, #0x28
    // 0x7b5958: SetupParameters(PdfGraphicState this /* r1 => r0, fp-0x8 */)
    //     0x7b5958: mov             x0, x1
    //     0x7b595c: stur            x1, [fp, #-8]
    // 0x7b5960: CheckStackOverflow
    //     0x7b5960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5964: cmp             SP, x16
    //     0x7b5968: b.ls            #0x7b5b80
    // 0x7b596c: r1 = <PdfDataType>
    //     0x7b596c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b5970: ldr             x1, [x1, #0x4c8]
    // 0x7b5974: r0 = PdfDict()
    //     0x7b5974: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b5978: mov             x1, x0
    // 0x7b597c: stur            x0, [fp, #-0x10]
    // 0x7b5980: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b5980: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b5984: r0 = PdfDict()
    //     0x7b5984: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7b5988: ldur            x0, [fp, #-8]
    // 0x7b598c: LoadField: r1 = r0->field_b
    //     0x7b598c: ldur            w1, [x0, #0xb]
    // 0x7b5990: DecompressPointer r1
    //     0x7b5990: add             x1, x1, HEAP, lsl #32
    // 0x7b5994: stur            x1, [fp, #-0x18]
    // 0x7b5998: cmp             w1, NULL
    // 0x7b599c: b.eq            #0x7b59d4
    // 0x7b59a0: ldur            x2, [fp, #-0x10]
    // 0x7b59a4: r0 = PdfNum()
    //     0x7b59a4: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b59a8: mov             x1, x0
    // 0x7b59ac: ldur            x0, [fp, #-0x18]
    // 0x7b59b0: StoreField: r1->field_7 = r0
    //     0x7b59b0: stur            w0, [x1, #7]
    // 0x7b59b4: ldur            x0, [fp, #-0x10]
    // 0x7b59b8: LoadField: r2 = r0->field_b
    //     0x7b59b8: ldur            w2, [x0, #0xb]
    // 0x7b59bc: DecompressPointer r2
    //     0x7b59bc: add             x2, x2, HEAP, lsl #32
    // 0x7b59c0: mov             x3, x1
    // 0x7b59c4: mov             x1, x2
    // 0x7b59c8: r2 = "/CA"
    //     0x7b59c8: add             x2, PP, #0x47, lsl #12  ; [pp+0x477f8] "/CA"
    //     0x7b59cc: ldr             x2, [x2, #0x7f8]
    // 0x7b59d0: r0 = []=()
    //     0x7b59d0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b59d4: ldur            x0, [fp, #-8]
    // 0x7b59d8: LoadField: r1 = r0->field_7
    //     0x7b59d8: ldur            w1, [x0, #7]
    // 0x7b59dc: DecompressPointer r1
    //     0x7b59dc: add             x1, x1, HEAP, lsl #32
    // 0x7b59e0: stur            x1, [fp, #-0x18]
    // 0x7b59e4: cmp             w1, NULL
    // 0x7b59e8: b.eq            #0x7b5a20
    // 0x7b59ec: ldur            x2, [fp, #-0x10]
    // 0x7b59f0: r0 = PdfNum()
    //     0x7b59f0: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b59f4: mov             x1, x0
    // 0x7b59f8: ldur            x0, [fp, #-0x18]
    // 0x7b59fc: StoreField: r1->field_7 = r0
    //     0x7b59fc: stur            w0, [x1, #7]
    // 0x7b5a00: ldur            x0, [fp, #-0x10]
    // 0x7b5a04: LoadField: r2 = r0->field_b
    //     0x7b5a04: ldur            w2, [x0, #0xb]
    // 0x7b5a08: DecompressPointer r2
    //     0x7b5a08: add             x2, x2, HEAP, lsl #32
    // 0x7b5a0c: mov             x3, x1
    // 0x7b5a10: mov             x1, x2
    // 0x7b5a14: r2 = "/ca"
    //     0x7b5a14: add             x2, PP, #0x47, lsl #12  ; [pp+0x47800] "/ca"
    //     0x7b5a18: ldr             x2, [x2, #0x800]
    // 0x7b5a1c: r0 = []=()
    //     0x7b5a1c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5a20: ldur            x0, [fp, #-8]
    // 0x7b5a24: LoadField: r1 = r0->field_f
    //     0x7b5a24: ldur            w1, [x0, #0xf]
    // 0x7b5a28: DecompressPointer r1
    //     0x7b5a28: add             x1, x1, HEAP, lsl #32
    // 0x7b5a2c: cmp             w1, NULL
    // 0x7b5a30: b.eq            #0x7b5b30
    // 0x7b5a34: ldur            x2, [fp, #-0x10]
    // 0x7b5a38: str             x1, [SP]
    // 0x7b5a3c: r0 = toString()
    //     0x7b5a3c: bl              #0xc00448  ; [dart:core] _Enum::toString
    // 0x7b5a40: r1 = Null
    //     0x7b5a40: mov             x1, NULL
    // 0x7b5a44: r2 = 6
    //     0x7b5a44: movz            x2, #0x6
    // 0x7b5a48: stur            x0, [fp, #-0x18]
    // 0x7b5a4c: r0 = AllocateArray()
    //     0x7b5a4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b5a50: stur            x0, [fp, #-0x20]
    // 0x7b5a54: r16 = "/"
    //     0x7b5a54: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7b5a58: StoreField: r0->field_f = r16
    //     0x7b5a58: stur            w16, [x0, #0xf]
    // 0x7b5a5c: r16 = 28
    //     0x7b5a5c: movz            x16, #0x1c
    // 0x7b5a60: str             x16, [SP]
    // 0x7b5a64: ldur            x1, [fp, #-0x18]
    // 0x7b5a68: r2 = 13
    //     0x7b5a68: movz            x2, #0xd
    // 0x7b5a6c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x7b5a6c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x7b5a70: r0 = substring()
    //     0x7b5a70: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x7b5a74: r1 = LoadClassIdInstr(r0)
    //     0x7b5a74: ldur            x1, [x0, #-1]
    //     0x7b5a78: ubfx            x1, x1, #0xc, #0x14
    // 0x7b5a7c: str             x0, [SP]
    // 0x7b5a80: mov             x0, x1
    // 0x7b5a84: r0 = GDT[cid_x0 + -0xff6]()
    //     0x7b5a84: sub             lr, x0, #0xff6
    //     0x7b5a88: ldr             lr, [x21, lr, lsl #3]
    //     0x7b5a8c: blr             lr
    // 0x7b5a90: ldur            x1, [fp, #-0x20]
    // 0x7b5a94: ArrayStore: r1[1] = r0  ; List_4
    //     0x7b5a94: add             x25, x1, #0x13
    //     0x7b5a98: str             w0, [x25]
    //     0x7b5a9c: tbz             w0, #0, #0x7b5ab8
    //     0x7b5aa0: ldurb           w16, [x1, #-1]
    //     0x7b5aa4: ldurb           w17, [x0, #-1]
    //     0x7b5aa8: and             x16, x17, x16, lsr #2
    //     0x7b5aac: tst             x16, HEAP, lsr #32
    //     0x7b5ab0: b.eq            #0x7b5ab8
    //     0x7b5ab4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b5ab8: ldur            x1, [fp, #-0x18]
    // 0x7b5abc: r2 = 14
    //     0x7b5abc: movz            x2, #0xe
    // 0x7b5ac0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7b5ac0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7b5ac4: r0 = substring()
    //     0x7b5ac4: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x7b5ac8: ldur            x1, [fp, #-0x20]
    // 0x7b5acc: ArrayStore: r1[2] = r0  ; List_4
    //     0x7b5acc: add             x25, x1, #0x17
    //     0x7b5ad0: str             w0, [x25]
    //     0x7b5ad4: tbz             w0, #0, #0x7b5af0
    //     0x7b5ad8: ldurb           w16, [x1, #-1]
    //     0x7b5adc: ldurb           w17, [x0, #-1]
    //     0x7b5ae0: and             x16, x17, x16, lsr #2
    //     0x7b5ae4: tst             x16, HEAP, lsr #32
    //     0x7b5ae8: b.eq            #0x7b5af0
    //     0x7b5aec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b5af0: ldur            x16, [fp, #-0x20]
    // 0x7b5af4: str             x16, [SP]
    // 0x7b5af8: r0 = _interpolate()
    //     0x7b5af8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b5afc: stur            x0, [fp, #-0x18]
    // 0x7b5b00: r0 = PdfName()
    //     0x7b5b00: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b5b04: mov             x1, x0
    // 0x7b5b08: ldur            x0, [fp, #-0x18]
    // 0x7b5b0c: StoreField: r1->field_7 = r0
    //     0x7b5b0c: stur            w0, [x1, #7]
    // 0x7b5b10: ldur            x0, [fp, #-0x10]
    // 0x7b5b14: LoadField: r2 = r0->field_b
    //     0x7b5b14: ldur            w2, [x0, #0xb]
    // 0x7b5b18: DecompressPointer r2
    //     0x7b5b18: add             x2, x2, HEAP, lsl #32
    // 0x7b5b1c: mov             x3, x1
    // 0x7b5b20: mov             x1, x2
    // 0x7b5b24: r2 = "/BM"
    //     0x7b5b24: add             x2, PP, #0x47, lsl #12  ; [pp+0x47808] "/BM"
    //     0x7b5b28: ldr             x2, [x2, #0x808]
    // 0x7b5b2c: r0 = []=()
    //     0x7b5b2c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5b30: ldur            x0, [fp, #-8]
    // 0x7b5b34: LoadField: r1 = r0->field_13
    //     0x7b5b34: ldur            w1, [x0, #0x13]
    // 0x7b5b38: DecompressPointer r1
    //     0x7b5b38: add             x1, x1, HEAP, lsl #32
    // 0x7b5b3c: cmp             w1, NULL
    // 0x7b5b40: b.eq            #0x7b5b70
    // 0x7b5b44: ldur            x0, [fp, #-0x10]
    // 0x7b5b48: r0 = output()
    //     0x7b5b48: bl              #0x7b5ba8  ; [package:pdf/src/pdf/obj/smask.dart] PdfSoftMask::output
    // 0x7b5b4c: mov             x1, x0
    // 0x7b5b50: ldur            x0, [fp, #-0x10]
    // 0x7b5b54: LoadField: r2 = r0->field_b
    //     0x7b5b54: ldur            w2, [x0, #0xb]
    // 0x7b5b58: DecompressPointer r2
    //     0x7b5b58: add             x2, x2, HEAP, lsl #32
    // 0x7b5b5c: mov             x3, x1
    // 0x7b5b60: mov             x1, x2
    // 0x7b5b64: r2 = "/SMask"
    //     0x7b5b64: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebc8] "/SMask"
    //     0x7b5b68: ldr             x2, [x2, #0xbc8]
    // 0x7b5b6c: r0 = []=()
    //     0x7b5b6c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5b70: ldur            x0, [fp, #-0x10]
    // 0x7b5b74: LeaveFrame
    //     0x7b5b74: mov             SP, fp
    //     0x7b5b78: ldp             fp, lr, [SP], #0x10
    // 0x7b5b7c: ret
    //     0x7b5b7c: ret             
    // 0x7b5b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b5b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5b84: b               #0x7b596c
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1a34, size: 0x140
    // 0xbf1a34: EnterFrame
    //     0xbf1a34: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1a38: mov             fp, SP
    // 0xbf1a3c: AllocStack(0x20)
    //     0xbf1a3c: sub             SP, SP, #0x20
    // 0xbf1a40: CheckStackOverflow
    //     0xbf1a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1a44: cmp             SP, x16
    //     0xbf1a48: b.ls            #0xbf1b6c
    // 0xbf1a4c: ldr             x1, [fp, #0x10]
    // 0xbf1a50: LoadField: r0 = r1->field_7
    //     0xbf1a50: ldur            w0, [x1, #7]
    // 0xbf1a54: DecompressPointer r0
    //     0xbf1a54: add             x0, x0, HEAP, lsl #32
    // 0xbf1a58: r2 = LoadClassIdInstr(r0)
    //     0xbf1a58: ldur            x2, [x0, #-1]
    //     0xbf1a5c: ubfx            x2, x2, #0xc, #0x14
    // 0xbf1a60: str             x0, [SP]
    // 0xbf1a64: mov             x0, x2
    // 0xbf1a68: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1a68: movz            x17, #0x64af
    //     0xbf1a6c: add             lr, x0, x17
    //     0xbf1a70: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1a74: blr             lr
    // 0xbf1a78: mov             x2, x0
    // 0xbf1a7c: ldr             x1, [fp, #0x10]
    // 0xbf1a80: stur            x2, [fp, #-8]
    // 0xbf1a84: LoadField: r0 = r1->field_b
    //     0xbf1a84: ldur            w0, [x1, #0xb]
    // 0xbf1a88: DecompressPointer r0
    //     0xbf1a88: add             x0, x0, HEAP, lsl #32
    // 0xbf1a8c: r3 = LoadClassIdInstr(r0)
    //     0xbf1a8c: ldur            x3, [x0, #-1]
    //     0xbf1a90: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1a94: str             x0, [SP]
    // 0xbf1a98: mov             x0, x3
    // 0xbf1a9c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1a9c: movz            x17, #0x64af
    //     0xbf1aa0: add             lr, x0, x17
    //     0xbf1aa4: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1aa8: blr             lr
    // 0xbf1aac: mov             x1, x0
    // 0xbf1ab0: ldur            x0, [fp, #-8]
    // 0xbf1ab4: r2 = LoadInt32Instr(r0)
    //     0xbf1ab4: sbfx            x2, x0, #1, #0x1f
    //     0xbf1ab8: tbz             w0, #0, #0xbf1ac0
    //     0xbf1abc: ldur            x2, [x0, #7]
    // 0xbf1ac0: r0 = LoadInt32Instr(r1)
    //     0xbf1ac0: sbfx            x0, x1, #1, #0x1f
    //     0xbf1ac4: tbz             w1, #0, #0xbf1acc
    //     0xbf1ac8: ldur            x0, [x1, #7]
    // 0xbf1acc: mul             x1, x2, x0
    // 0xbf1ad0: ldr             x2, [fp, #0x10]
    // 0xbf1ad4: stur            x1, [fp, #-0x10]
    // 0xbf1ad8: LoadField: r0 = r2->field_f
    //     0xbf1ad8: ldur            w0, [x2, #0xf]
    // 0xbf1adc: DecompressPointer r0
    //     0xbf1adc: add             x0, x0, HEAP, lsl #32
    // 0xbf1ae0: r3 = LoadClassIdInstr(r0)
    //     0xbf1ae0: ldur            x3, [x0, #-1]
    //     0xbf1ae4: ubfx            x3, x3, #0xc, #0x14
    // 0xbf1ae8: str             x0, [SP]
    // 0xbf1aec: mov             x0, x3
    // 0xbf1af0: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1af0: movz            x17, #0x64af
    //     0xbf1af4: add             lr, x0, x17
    //     0xbf1af8: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1afc: blr             lr
    // 0xbf1b00: r1 = LoadInt32Instr(r0)
    //     0xbf1b00: sbfx            x1, x0, #1, #0x1f
    // 0xbf1b04: ldur            x0, [fp, #-0x10]
    // 0xbf1b08: mul             x2, x0, x1
    // 0xbf1b0c: ldr             x0, [fp, #0x10]
    // 0xbf1b10: stur            x2, [fp, #-0x18]
    // 0xbf1b14: LoadField: r1 = r0->field_13
    //     0xbf1b14: ldur            w1, [x0, #0x13]
    // 0xbf1b18: DecompressPointer r1
    //     0xbf1b18: add             x1, x1, HEAP, lsl #32
    // 0xbf1b1c: r0 = LoadClassIdInstr(r1)
    //     0xbf1b1c: ldur            x0, [x1, #-1]
    //     0xbf1b20: ubfx            x0, x0, #0xc, #0x14
    // 0xbf1b24: str             x1, [SP]
    // 0xbf1b28: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1b28: movz            x17, #0x64af
    //     0xbf1b2c: add             lr, x0, x17
    //     0xbf1b30: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1b34: blr             lr
    // 0xbf1b38: r2 = LoadInt32Instr(r0)
    //     0xbf1b38: sbfx            x2, x0, #1, #0x1f
    // 0xbf1b3c: ldur            x3, [fp, #-0x18]
    // 0xbf1b40: mul             x4, x3, x2
    // 0xbf1b44: r16 = 2011
    //     0xbf1b44: movz            x16, #0x7db
    // 0xbf1b48: mul             x2, x4, x16
    // 0xbf1b4c: r0 = BoxInt64Instr(r2)
    //     0xbf1b4c: sbfiz           x0, x2, #1, #0x1f
    //     0xbf1b50: cmp             x2, x0, asr #1
    //     0xbf1b54: b.eq            #0xbf1b60
    //     0xbf1b58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1b5c: stur            x2, [x0, #7]
    // 0xbf1b60: LeaveFrame
    //     0xbf1b60: mov             SP, fp
    //     0xbf1b64: ldp             fp, lr, [SP], #0x10
    // 0xbf1b68: ret
    //     0xbf1b68: ret             
    // 0xbf1b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1b6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1b70: b               #0xbf1a4c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc35724, size: 0xc8
    // 0xc35724: EnterFrame
    //     0xc35724: stp             fp, lr, [SP, #-0x10]!
    //     0xc35728: mov             fp, SP
    // 0xc3572c: AllocStack(0x8)
    //     0xc3572c: sub             SP, SP, #8
    // 0xc35730: CheckStackOverflow
    //     0xc35730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35734: cmp             SP, x16
    //     0xc35738: b.ls            #0xc357e4
    // 0xc3573c: r1 = Null
    //     0xc3573c: mov             x1, NULL
    // 0xc35740: r2 = 22
    //     0xc35740: movz            x2, #0x16
    // 0xc35744: r0 = AllocateArray()
    //     0xc35744: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35748: r16 = PdfGraphicState
    //     0xc35748: add             x16, PP, #0x47, lsl #12  ; [pp+0x47828] Type: PdfGraphicState
    //     0xc3574c: ldr             x16, [x16, #0x828]
    // 0xc35750: StoreField: r0->field_f = r16
    //     0xc35750: stur            w16, [x0, #0xf]
    // 0xc35754: r16 = " fillOpacity:"
    //     0xc35754: add             x16, PP, #0x47, lsl #12  ; [pp+0x47830] " fillOpacity:"
    //     0xc35758: ldr             x16, [x16, #0x830]
    // 0xc3575c: StoreField: r0->field_13 = r16
    //     0xc3575c: stur            w16, [x0, #0x13]
    // 0xc35760: ldr             x1, [fp, #0x10]
    // 0xc35764: LoadField: r2 = r1->field_7
    //     0xc35764: ldur            w2, [x1, #7]
    // 0xc35768: DecompressPointer r2
    //     0xc35768: add             x2, x2, HEAP, lsl #32
    // 0xc3576c: ArrayStore: r0[0] = r2  ; List_4
    //     0xc3576c: stur            w2, [x0, #0x17]
    // 0xc35770: r16 = " strokeOpacity:"
    //     0xc35770: add             x16, PP, #0x47, lsl #12  ; [pp+0x47838] " strokeOpacity:"
    //     0xc35774: ldr             x16, [x16, #0x838]
    // 0xc35778: StoreField: r0->field_1b = r16
    //     0xc35778: stur            w16, [x0, #0x1b]
    // 0xc3577c: LoadField: r2 = r1->field_b
    //     0xc3577c: ldur            w2, [x1, #0xb]
    // 0xc35780: DecompressPointer r2
    //     0xc35780: add             x2, x2, HEAP, lsl #32
    // 0xc35784: StoreField: r0->field_1f = r2
    //     0xc35784: stur            w2, [x0, #0x1f]
    // 0xc35788: r16 = " blendMode:"
    //     0xc35788: add             x16, PP, #0x47, lsl #12  ; [pp+0x47840] " blendMode:"
    //     0xc3578c: ldr             x16, [x16, #0x840]
    // 0xc35790: StoreField: r0->field_23 = r16
    //     0xc35790: stur            w16, [x0, #0x23]
    // 0xc35794: LoadField: r2 = r1->field_f
    //     0xc35794: ldur            w2, [x1, #0xf]
    // 0xc35798: DecompressPointer r2
    //     0xc35798: add             x2, x2, HEAP, lsl #32
    // 0xc3579c: StoreField: r0->field_27 = r2
    //     0xc3579c: stur            w2, [x0, #0x27]
    // 0xc357a0: r16 = " softMask:"
    //     0xc357a0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47848] " softMask:"
    //     0xc357a4: ldr             x16, [x16, #0x848]
    // 0xc357a8: StoreField: r0->field_2b = r16
    //     0xc357a8: stur            w16, [x0, #0x2b]
    // 0xc357ac: LoadField: r2 = r1->field_13
    //     0xc357ac: ldur            w2, [x1, #0x13]
    // 0xc357b0: DecompressPointer r2
    //     0xc357b0: add             x2, x2, HEAP, lsl #32
    // 0xc357b4: StoreField: r0->field_2f = r2
    //     0xc357b4: stur            w2, [x0, #0x2f]
    // 0xc357b8: r16 = " transferFunction:"
    //     0xc357b8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47850] " transferFunction:"
    //     0xc357bc: ldr             x16, [x16, #0x850]
    // 0xc357c0: StoreField: r0->field_33 = r16
    //     0xc357c0: stur            w16, [x0, #0x33]
    // 0xc357c4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc357c4: ldur            w2, [x1, #0x17]
    // 0xc357c8: DecompressPointer r2
    //     0xc357c8: add             x2, x2, HEAP, lsl #32
    // 0xc357cc: StoreField: r0->field_37 = r2
    //     0xc357cc: stur            w2, [x0, #0x37]
    // 0xc357d0: str             x0, [SP]
    // 0xc357d4: r0 = _interpolate()
    //     0xc357d4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc357d8: LeaveFrame
    //     0xc357d8: mov             SP, fp
    //     0xc357dc: ldp             fp, lr, [SP], #0x10
    // 0xc357e0: ret
    //     0xc357e0: ret             
    // 0xc357e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc357e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc357e8: b               #0xc3573c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7c9c8, size: 0x120
    // 0xd7c9c8: EnterFrame
    //     0xd7c9c8: stp             fp, lr, [SP, #-0x10]!
    //     0xd7c9cc: mov             fp, SP
    // 0xd7c9d0: AllocStack(0x10)
    //     0xd7c9d0: sub             SP, SP, #0x10
    // 0xd7c9d4: CheckStackOverflow
    //     0xd7c9d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7c9d8: cmp             SP, x16
    //     0xd7c9dc: b.ls            #0xd7cae0
    // 0xd7c9e0: ldr             x1, [fp, #0x10]
    // 0xd7c9e4: cmp             w1, NULL
    // 0xd7c9e8: b.ne            #0xd7c9fc
    // 0xd7c9ec: r0 = false
    //     0xd7c9ec: add             x0, NULL, #0x30  ; false
    // 0xd7c9f0: LeaveFrame
    //     0xd7c9f0: mov             SP, fp
    //     0xd7c9f4: ldp             fp, lr, [SP], #0x10
    // 0xd7c9f8: ret
    //     0xd7c9f8: ret             
    // 0xd7c9fc: r0 = 60
    //     0xd7c9fc: movz            x0, #0x3c
    // 0xd7ca00: branchIfSmi(r1, 0xd7ca0c)
    //     0xd7ca00: tbz             w1, #0, #0xd7ca0c
    // 0xd7ca04: r0 = LoadClassIdInstr(r1)
    //     0xd7ca04: ldur            x0, [x1, #-1]
    //     0xd7ca08: ubfx            x0, x0, #0xc, #0x14
    // 0xd7ca0c: cmp             x0, #0x361
    // 0xd7ca10: b.eq            #0xd7ca24
    // 0xd7ca14: r0 = false
    //     0xd7ca14: add             x0, NULL, #0x30  ; false
    // 0xd7ca18: LeaveFrame
    //     0xd7ca18: mov             SP, fp
    //     0xd7ca1c: ldp             fp, lr, [SP], #0x10
    // 0xd7ca20: ret
    //     0xd7ca20: ret             
    // 0xd7ca24: ldr             x2, [fp, #0x18]
    // 0xd7ca28: LoadField: r0 = r1->field_7
    //     0xd7ca28: ldur            w0, [x1, #7]
    // 0xd7ca2c: DecompressPointer r0
    //     0xd7ca2c: add             x0, x0, HEAP, lsl #32
    // 0xd7ca30: LoadField: r3 = r2->field_7
    //     0xd7ca30: ldur            w3, [x2, #7]
    // 0xd7ca34: DecompressPointer r3
    //     0xd7ca34: add             x3, x3, HEAP, lsl #32
    // 0xd7ca38: r4 = LoadClassIdInstr(r0)
    //     0xd7ca38: ldur            x4, [x0, #-1]
    //     0xd7ca3c: ubfx            x4, x4, #0xc, #0x14
    // 0xd7ca40: stp             x3, x0, [SP]
    // 0xd7ca44: mov             x0, x4
    // 0xd7ca48: mov             lr, x0
    // 0xd7ca4c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ca50: blr             lr
    // 0xd7ca54: tbnz            w0, #4, #0xd7cad0
    // 0xd7ca58: ldr             x2, [fp, #0x18]
    // 0xd7ca5c: ldr             x1, [fp, #0x10]
    // 0xd7ca60: LoadField: r0 = r1->field_b
    //     0xd7ca60: ldur            w0, [x1, #0xb]
    // 0xd7ca64: DecompressPointer r0
    //     0xd7ca64: add             x0, x0, HEAP, lsl #32
    // 0xd7ca68: LoadField: r3 = r2->field_b
    //     0xd7ca68: ldur            w3, [x2, #0xb]
    // 0xd7ca6c: DecompressPointer r3
    //     0xd7ca6c: add             x3, x3, HEAP, lsl #32
    // 0xd7ca70: r4 = LoadClassIdInstr(r0)
    //     0xd7ca70: ldur            x4, [x0, #-1]
    //     0xd7ca74: ubfx            x4, x4, #0xc, #0x14
    // 0xd7ca78: stp             x3, x0, [SP]
    // 0xd7ca7c: mov             x0, x4
    // 0xd7ca80: mov             lr, x0
    // 0xd7ca84: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ca88: blr             lr
    // 0xd7ca8c: tbnz            w0, #4, #0xd7cad0
    // 0xd7ca90: ldr             x2, [fp, #0x18]
    // 0xd7ca94: ldr             x1, [fp, #0x10]
    // 0xd7ca98: LoadField: r3 = r1->field_f
    //     0xd7ca98: ldur            w3, [x1, #0xf]
    // 0xd7ca9c: DecompressPointer r3
    //     0xd7ca9c: add             x3, x3, HEAP, lsl #32
    // 0xd7caa0: LoadField: r4 = r2->field_f
    //     0xd7caa0: ldur            w4, [x2, #0xf]
    // 0xd7caa4: DecompressPointer r4
    //     0xd7caa4: add             x4, x4, HEAP, lsl #32
    // 0xd7caa8: cmp             w3, w4
    // 0xd7caac: b.ne            #0xd7cad0
    // 0xd7cab0: LoadField: r3 = r1->field_13
    //     0xd7cab0: ldur            w3, [x1, #0x13]
    // 0xd7cab4: DecompressPointer r3
    //     0xd7cab4: add             x3, x3, HEAP, lsl #32
    // 0xd7cab8: LoadField: r1 = r2->field_13
    //     0xd7cab8: ldur            w1, [x2, #0x13]
    // 0xd7cabc: DecompressPointer r1
    //     0xd7cabc: add             x1, x1, HEAP, lsl #32
    // 0xd7cac0: cmp             w3, w1
    // 0xd7cac4: b.ne            #0xd7cad0
    // 0xd7cac8: r0 = true
    //     0xd7cac8: add             x0, NULL, #0x20  ; true
    // 0xd7cacc: b               #0xd7cad4
    // 0xd7cad0: r0 = false
    //     0xd7cad0: add             x0, NULL, #0x30  ; false
    // 0xd7cad4: LeaveFrame
    //     0xd7cad4: mov             SP, fp
    //     0xd7cad8: ldp             fp, lr, [SP], #0x10
    // 0xd7cadc: ret
    //     0xd7cadc: ret             
    // 0xd7cae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7cae0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7cae4: b               #0xd7c9e0
  }
}

// class id: 900, size: 0x30, field offset: 0x2c
class PdfGraphicStates extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7b53c4, size: 0x158
    // 0x7b53c4: EnterFrame
    //     0x7b53c4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b53c8: mov             fp, SP
    // 0x7b53cc: AllocStack(0x38)
    //     0x7b53cc: sub             SP, SP, #0x38
    // 0x7b53d0: CheckStackOverflow
    //     0x7b53d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b53d4: cmp             SP, x16
    //     0x7b53d8: b.ls            #0x7b5508
    // 0x7b53dc: LoadField: r0 = r1->field_2b
    //     0x7b53dc: ldur            w0, [x1, #0x2b]
    // 0x7b53e0: DecompressPointer r0
    //     0x7b53e0: add             x0, x0, HEAP, lsl #32
    // 0x7b53e4: stur            x0, [fp, #-0x20]
    // 0x7b53e8: LoadField: r2 = r1->field_1b
    //     0x7b53e8: ldur            w2, [x1, #0x1b]
    // 0x7b53ec: DecompressPointer r2
    //     0x7b53ec: add             x2, x2, HEAP, lsl #32
    // 0x7b53f0: LoadField: r3 = r2->field_7
    //     0x7b53f0: ldur            w3, [x2, #7]
    // 0x7b53f4: DecompressPointer r3
    //     0x7b53f4: add             x3, x3, HEAP, lsl #32
    // 0x7b53f8: stur            x3, [fp, #-0x18]
    // 0x7b53fc: LoadField: r4 = r2->field_b
    //     0x7b53fc: ldur            w4, [x2, #0xb]
    // 0x7b5400: DecompressPointer r4
    //     0x7b5400: add             x4, x4, HEAP, lsl #32
    // 0x7b5404: stur            x4, [fp, #-0x10]
    // 0x7b5408: r5 = 0
    //     0x7b5408: movz            x5, #0
    // 0x7b540c: stur            x5, [fp, #-8]
    // 0x7b5410: CheckStackOverflow
    //     0x7b5410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5414: cmp             SP, x16
    //     0x7b5418: b.ls            #0x7b5510
    // 0x7b541c: LoadField: r1 = r0->field_b
    //     0x7b541c: ldur            w1, [x0, #0xb]
    // 0x7b5420: r2 = LoadInt32Instr(r1)
    //     0x7b5420: sbfx            x2, x1, #1, #0x1f
    // 0x7b5424: cmp             x5, x2
    // 0x7b5428: b.ge            #0x7b54f8
    // 0x7b542c: r1 = Null
    //     0x7b542c: mov             x1, NULL
    // 0x7b5430: r2 = 4
    //     0x7b5430: movz            x2, #0x4
    // 0x7b5434: r0 = AllocateArray()
    //     0x7b5434: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b5438: r16 = "/a"
    //     0x7b5438: add             x16, PP, #0x47, lsl #12  ; [pp+0x47060] "/a"
    //     0x7b543c: ldr             x16, [x16, #0x60]
    // 0x7b5440: StoreField: r0->field_f = r16
    //     0x7b5440: stur            w16, [x0, #0xf]
    // 0x7b5444: ldur            x1, [fp, #-8]
    // 0x7b5448: lsl             x2, x1, #1
    // 0x7b544c: StoreField: r0->field_13 = r2
    //     0x7b544c: stur            w2, [x0, #0x13]
    // 0x7b5450: str             x0, [SP]
    // 0x7b5454: r0 = _interpolate()
    //     0x7b5454: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b5458: mov             x3, x0
    // 0x7b545c: ldur            x2, [fp, #-0x20]
    // 0x7b5460: stur            x3, [fp, #-0x28]
    // 0x7b5464: LoadField: r0 = r2->field_b
    //     0x7b5464: ldur            w0, [x2, #0xb]
    // 0x7b5468: r1 = LoadInt32Instr(r0)
    //     0x7b5468: sbfx            x1, x0, #1, #0x1f
    // 0x7b546c: mov             x0, x1
    // 0x7b5470: ldur            x1, [fp, #-8]
    // 0x7b5474: cmp             x1, x0
    // 0x7b5478: b.hs            #0x7b5518
    // 0x7b547c: LoadField: r0 = r2->field_f
    //     0x7b547c: ldur            w0, [x2, #0xf]
    // 0x7b5480: DecompressPointer r0
    //     0x7b5480: add             x0, x0, HEAP, lsl #32
    // 0x7b5484: ldur            x4, [fp, #-8]
    // 0x7b5488: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x7b5488: add             x16, x0, x4, lsl #2
    //     0x7b548c: ldur            w1, [x16, #0xf]
    // 0x7b5490: DecompressPointer r1
    //     0x7b5490: add             x1, x1, HEAP, lsl #32
    // 0x7b5494: r0 = output()
    //     0x7b5494: bl              #0x7b594c  ; [package:pdf/src/pdf/graphic_state.dart] PdfGraphicState::output
    // 0x7b5498: ldur            x2, [fp, #-0x18]
    // 0x7b549c: mov             x3, x0
    // 0x7b54a0: r1 = Null
    //     0x7b54a0: mov             x1, NULL
    // 0x7b54a4: stur            x3, [fp, #-0x30]
    // 0x7b54a8: cmp             w2, NULL
    // 0x7b54ac: b.eq            #0x7b54d0
    // 0x7b54b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b54b0: ldur            w4, [x2, #0x17]
    // 0x7b54b4: DecompressPointer r4
    //     0x7b54b4: add             x4, x4, HEAP, lsl #32
    // 0x7b54b8: r8 = X0 bound PdfDataType
    //     0x7b54b8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b54bc: ldr             x8, [x8, #0x6d8]
    // 0x7b54c0: LoadField: r9 = r4->field_7
    //     0x7b54c0: ldur            x9, [x4, #7]
    // 0x7b54c4: r3 = Null
    //     0x7b54c4: add             x3, PP, #0x47, lsl #12  ; [pp+0x477e8] Null
    //     0x7b54c8: ldr             x3, [x3, #0x7e8]
    // 0x7b54cc: blr             x9
    // 0x7b54d0: ldur            x1, [fp, #-0x10]
    // 0x7b54d4: ldur            x2, [fp, #-0x28]
    // 0x7b54d8: ldur            x3, [fp, #-0x30]
    // 0x7b54dc: r0 = []=()
    //     0x7b54dc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b54e0: ldur            x1, [fp, #-8]
    // 0x7b54e4: add             x5, x1, #1
    // 0x7b54e8: ldur            x0, [fp, #-0x20]
    // 0x7b54ec: ldur            x3, [fp, #-0x18]
    // 0x7b54f0: ldur            x4, [fp, #-0x10]
    // 0x7b54f4: b               #0x7b540c
    // 0x7b54f8: r0 = Null
    //     0x7b54f8: mov             x0, NULL
    // 0x7b54fc: LeaveFrame
    //     0x7b54fc: mov             SP, fp
    //     0x7b5500: ldp             fp, lr, [SP], #0x10
    // 0x7b5504: ret
    //     0x7b5504: ret             
    // 0x7b5508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b5508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b550c: b               #0x7b53dc
    // 0x7b5510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b5510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5514: b               #0x7b541c
    // 0x7b5518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b5518: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ PdfGraphicStates(/* No info */) {
    // ** addr: 0x7cb3f4, size: 0x9c
    // 0x7cb3f4: EnterFrame
    //     0x7cb3f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb3f8: mov             fp, SP
    // 0x7cb3fc: AllocStack(0x18)
    //     0x7cb3fc: sub             SP, SP, #0x18
    // 0x7cb400: SetupParameters(PdfGraphicStates this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7cb400: mov             x3, x1
    //     0x7cb404: mov             x0, x2
    //     0x7cb408: stur            x1, [fp, #-8]
    //     0x7cb40c: stur            x2, [fp, #-0x10]
    // 0x7cb410: CheckStackOverflow
    //     0x7cb410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb414: cmp             SP, x16
    //     0x7cb418: b.ls            #0x7cb488
    // 0x7cb41c: r1 = <PdfGraphicState>
    //     0x7cb41c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3efa8] TypeArguments: <PdfGraphicState>
    //     0x7cb420: ldr             x1, [x1, #0xfa8]
    // 0x7cb424: r2 = 0
    //     0x7cb424: movz            x2, #0
    // 0x7cb428: r0 = _GrowableList()
    //     0x7cb428: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7cb42c: ldur            x2, [fp, #-8]
    // 0x7cb430: StoreField: r2->field_2b = r0
    //     0x7cb430: stur            w0, [x2, #0x2b]
    //     0x7cb434: ldurb           w16, [x2, #-1]
    //     0x7cb438: ldurb           w17, [x0, #-1]
    //     0x7cb43c: and             x16, x17, x16, lsr #2
    //     0x7cb440: tst             x16, HEAP, lsr #32
    //     0x7cb444: b.eq            #0x7cb44c
    //     0x7cb448: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7cb44c: r1 = <PdfDataType>
    //     0x7cb44c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7cb450: ldr             x1, [x1, #0x4c8]
    // 0x7cb454: r0 = PdfDict()
    //     0x7cb454: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7cb458: mov             x1, x0
    // 0x7cb45c: stur            x0, [fp, #-0x18]
    // 0x7cb460: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7cb460: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7cb464: r0 = PdfDict()
    //     0x7cb464: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7cb468: ldur            x1, [fp, #-8]
    // 0x7cb46c: ldur            x2, [fp, #-0x10]
    // 0x7cb470: ldur            x3, [fp, #-0x18]
    // 0x7cb474: r0 = PdfObject()
    //     0x7cb474: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0x7cb478: r0 = Null
    //     0x7cb478: mov             x0, NULL
    // 0x7cb47c: LeaveFrame
    //     0x7cb47c: mov             SP, fp
    //     0x7cb480: ldp             fp, lr, [SP], #0x10
    // 0x7cb484: ret
    //     0x7cb484: ret             
    // 0x7cb488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb48c: b               #0x7cb41c
  }
  _ stateName(/* No info */) {
    // ** addr: 0xea1298, size: 0x168
    // 0xea1298: EnterFrame
    //     0xea1298: stp             fp, lr, [SP, #-0x10]!
    //     0xea129c: mov             fp, SP
    // 0xea12a0: AllocStack(0x28)
    //     0xea12a0: sub             SP, SP, #0x28
    // 0xea12a4: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xea12a4: mov             x0, x2
    //     0xea12a8: stur            x2, [fp, #-0x10]
    // 0xea12ac: CheckStackOverflow
    //     0xea12ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea12b0: cmp             SP, x16
    //     0xea12b4: b.ls            #0xea13f8
    // 0xea12b8: LoadField: r3 = r1->field_2b
    //     0xea12b8: ldur            w3, [x1, #0x2b]
    // 0xea12bc: DecompressPointer r3
    //     0xea12bc: add             x3, x3, HEAP, lsl #32
    // 0xea12c0: mov             x1, x3
    // 0xea12c4: mov             x2, x0
    // 0xea12c8: stur            x3, [fp, #-8]
    // 0xea12cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea12cc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea12d0: r0 = indexOf()
    //     0xea12d0: bl              #0x6ec5f4  ; [dart:collection] ListBase::indexOf
    // 0xea12d4: r1 = LoadInt32Instr(r0)
    //     0xea12d4: sbfx            x1, x0, #1, #0x1f
    //     0xea12d8: tbz             w0, #0, #0xea12e0
    //     0xea12dc: ldur            x1, [x0, #7]
    // 0xea12e0: tbz             x1, #0x3f, #0xea13a4
    // 0xea12e4: ldur            x3, [fp, #-8]
    // 0xea12e8: LoadField: r4 = r3->field_b
    //     0xea12e8: ldur            w4, [x3, #0xb]
    // 0xea12ec: stur            x4, [fp, #-0x18]
    // 0xea12f0: LoadField: r2 = r3->field_7
    //     0xea12f0: ldur            w2, [x3, #7]
    // 0xea12f4: DecompressPointer r2
    //     0xea12f4: add             x2, x2, HEAP, lsl #32
    // 0xea12f8: ldur            x0, [fp, #-0x10]
    // 0xea12fc: r1 = Null
    //     0xea12fc: mov             x1, NULL
    // 0xea1300: cmp             w2, NULL
    // 0xea1304: b.eq            #0xea1324
    // 0xea1308: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xea1308: ldur            w4, [x2, #0x17]
    // 0xea130c: DecompressPointer r4
    //     0xea130c: add             x4, x4, HEAP, lsl #32
    // 0xea1310: r8 = X0
    //     0xea1310: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xea1314: LoadField: r9 = r4->field_7
    //     0xea1314: ldur            x9, [x4, #7]
    // 0xea1318: r3 = Null
    //     0xea1318: add             x3, PP, #0x47, lsl #12  ; [pp+0x47050] Null
    //     0xea131c: ldr             x3, [x3, #0x50]
    // 0xea1320: blr             x9
    // 0xea1324: ldur            x0, [fp, #-8]
    // 0xea1328: LoadField: r1 = r0->field_f
    //     0xea1328: ldur            w1, [x0, #0xf]
    // 0xea132c: DecompressPointer r1
    //     0xea132c: add             x1, x1, HEAP, lsl #32
    // 0xea1330: LoadField: r2 = r1->field_b
    //     0xea1330: ldur            w2, [x1, #0xb]
    // 0xea1334: ldur            x1, [fp, #-0x18]
    // 0xea1338: r3 = LoadInt32Instr(r1)
    //     0xea1338: sbfx            x3, x1, #1, #0x1f
    // 0xea133c: stur            x3, [fp, #-0x20]
    // 0xea1340: r1 = LoadInt32Instr(r2)
    //     0xea1340: sbfx            x1, x2, #1, #0x1f
    // 0xea1344: cmp             x3, x1
    // 0xea1348: b.ne            #0xea1354
    // 0xea134c: mov             x1, x0
    // 0xea1350: r0 = _growToNextCapacity()
    //     0xea1350: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xea1354: ldur            x0, [fp, #-8]
    // 0xea1358: ldur            x2, [fp, #-0x20]
    // 0xea135c: add             x1, x2, #1
    // 0xea1360: lsl             x3, x1, #1
    // 0xea1364: StoreField: r0->field_b = r3
    //     0xea1364: stur            w3, [x0, #0xb]
    // 0xea1368: LoadField: r1 = r0->field_f
    //     0xea1368: ldur            w1, [x0, #0xf]
    // 0xea136c: DecompressPointer r1
    //     0xea136c: add             x1, x1, HEAP, lsl #32
    // 0xea1370: ldur            x0, [fp, #-0x10]
    // 0xea1374: ArrayStore: r1[r2] = r0  ; List_4
    //     0xea1374: add             x25, x1, x2, lsl #2
    //     0xea1378: add             x25, x25, #0xf
    //     0xea137c: str             w0, [x25]
    //     0xea1380: tbz             w0, #0, #0xea139c
    //     0xea1384: ldurb           w16, [x1, #-1]
    //     0xea1388: ldurb           w17, [x0, #-1]
    //     0xea138c: and             x16, x17, x16, lsr #2
    //     0xea1390: tst             x16, HEAP, lsr #32
    //     0xea1394: b.eq            #0xea139c
    //     0xea1398: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xea139c: mov             x0, x2
    // 0xea13a0: b               #0xea13a8
    // 0xea13a4: mov             x0, x1
    // 0xea13a8: stur            x0, [fp, #-0x20]
    // 0xea13ac: r1 = Null
    //     0xea13ac: mov             x1, NULL
    // 0xea13b0: r2 = 4
    //     0xea13b0: movz            x2, #0x4
    // 0xea13b4: r0 = AllocateArray()
    //     0xea13b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea13b8: mov             x2, x0
    // 0xea13bc: r16 = "/a"
    //     0xea13bc: add             x16, PP, #0x47, lsl #12  ; [pp+0x47060] "/a"
    //     0xea13c0: ldr             x16, [x16, #0x60]
    // 0xea13c4: StoreField: r2->field_f = r16
    //     0xea13c4: stur            w16, [x2, #0xf]
    // 0xea13c8: ldur            x3, [fp, #-0x20]
    // 0xea13cc: r0 = BoxInt64Instr(r3)
    //     0xea13cc: sbfiz           x0, x3, #1, #0x1f
    //     0xea13d0: cmp             x3, x0, asr #1
    //     0xea13d4: b.eq            #0xea13e0
    //     0xea13d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea13dc: stur            x3, [x0, #7]
    // 0xea13e0: StoreField: r2->field_13 = r0
    //     0xea13e0: stur            w0, [x2, #0x13]
    // 0xea13e4: str             x2, [SP]
    // 0xea13e8: r0 = _interpolate()
    //     0xea13e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea13ec: LeaveFrame
    //     0xea13ec: mov             SP, fp
    //     0xea13f0: ldp             fp, lr, [SP], #0x10
    // 0xea13f4: ret
    //     0xea13f4: ret             
    // 0xea13f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea13f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea13fc: b               #0xea12b8
  }
}

// class id: 6815, size: 0x14, field offset: 0x14
enum PdfBlendMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d9b0, size: 0x64
    // 0xc4d9b0: EnterFrame
    //     0xc4d9b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d9b4: mov             fp, SP
    // 0xc4d9b8: AllocStack(0x10)
    //     0xc4d9b8: sub             SP, SP, #0x10
    // 0xc4d9bc: SetupParameters(PdfBlendMode this /* r1 => r0, fp-0x8 */)
    //     0xc4d9bc: mov             x0, x1
    //     0xc4d9c0: stur            x1, [fp, #-8]
    // 0xc4d9c4: CheckStackOverflow
    //     0xc4d9c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d9c8: cmp             SP, x16
    //     0xc4d9cc: b.ls            #0xc4da0c
    // 0xc4d9d0: r1 = Null
    //     0xc4d9d0: mov             x1, NULL
    // 0xc4d9d4: r2 = 4
    //     0xc4d9d4: movz            x2, #0x4
    // 0xc4d9d8: r0 = AllocateArray()
    //     0xc4d9d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d9dc: r16 = "PdfBlendMode."
    //     0xc4d9dc: add             x16, PP, #0x47, lsl #12  ; [pp+0x477e0] "PdfBlendMode."
    //     0xc4d9e0: ldr             x16, [x16, #0x7e0]
    // 0xc4d9e4: StoreField: r0->field_f = r16
    //     0xc4d9e4: stur            w16, [x0, #0xf]
    // 0xc4d9e8: ldur            x1, [fp, #-8]
    // 0xc4d9ec: LoadField: r2 = r1->field_f
    //     0xc4d9ec: ldur            w2, [x1, #0xf]
    // 0xc4d9f0: DecompressPointer r2
    //     0xc4d9f0: add             x2, x2, HEAP, lsl #32
    // 0xc4d9f4: StoreField: r0->field_13 = r2
    //     0xc4d9f4: stur            w2, [x0, #0x13]
    // 0xc4d9f8: str             x0, [SP]
    // 0xc4d9fc: r0 = _interpolate()
    //     0xc4d9fc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4da00: LeaveFrame
    //     0xc4da00: mov             SP, fp
    //     0xc4da04: ldp             fp, lr, [SP], #0x10
    // 0xc4da08: ret
    //     0xc4da08: ret             
    // 0xc4da0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4da0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4da10: b               #0xc4d9d0
  }
}
