// lib: , url: package:pdf/src/pdf/graphics.dart

// class id: 1050794, size: 0x8
class :: {
}

// class id: 863, size: 0x18, field offset: 0x8
class PdfGraphics extends Object {

  late _PdfGraphicsContext _context; // offset: 0x8

  _ curveTo(/* No info */) {
    // ** addr: 0xac43f8, size: 0x28c
    // 0xac43f8: EnterFrame
    //     0xac43f8: stp             fp, lr, [SP, #-0x10]!
    //     0xac43fc: mov             fp, SP
    // 0xac4400: AllocStack(0x40)
    //     0xac4400: sub             SP, SP, #0x40
    // 0xac4404: r0 = 12
    //     0xac4404: movz            x0, #0xc
    // 0xac4408: mov             x3, x1
    // 0xac440c: stur            x1, [fp, #-0x10]
    // 0xac4410: stur            d1, [fp, #-0x20]
    // 0xac4414: stur            d2, [fp, #-0x28]
    // 0xac4418: stur            d3, [fp, #-0x30]
    // 0xac441c: stur            d4, [fp, #-0x38]
    // 0xac4420: stur            d5, [fp, #-0x40]
    // 0xac4424: CheckStackOverflow
    //     0xac4424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac4428: cmp             SP, x16
    //     0xac442c: b.ls            #0xac45d8
    // 0xac4430: r4 = inline_Allocate_Double()
    //     0xac4430: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xac4434: add             x4, x4, #0x10
    //     0xac4438: cmp             x1, x4
    //     0xac443c: b.ls            #0xac45e0
    //     0xac4440: str             x4, [THR, #0x50]  ; THR::top
    //     0xac4444: sub             x4, x4, #0xf
    //     0xac4448: movz            x1, #0xe15c
    //     0xac444c: movk            x1, #0x3, lsl #16
    //     0xac4450: stur            x1, [x4, #-1]
    // 0xac4454: StoreField: r4->field_7 = d0
    //     0xac4454: stur            d0, [x4, #7]
    // 0xac4458: mov             x2, x0
    // 0xac445c: stur            x4, [fp, #-8]
    // 0xac4460: r1 = Null
    //     0xac4460: mov             x1, NULL
    // 0xac4464: r0 = AllocateArray()
    //     0xac4464: bl              #0xec22fc  ; AllocateArrayStub
    // 0xac4468: mov             x2, x0
    // 0xac446c: ldur            x0, [fp, #-8]
    // 0xac4470: stur            x2, [fp, #-0x18]
    // 0xac4474: StoreField: r2->field_f = r0
    //     0xac4474: stur            w0, [x2, #0xf]
    // 0xac4478: ldur            d0, [fp, #-0x20]
    // 0xac447c: r0 = inline_Allocate_Double()
    //     0xac447c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac4480: add             x0, x0, #0x10
    //     0xac4484: cmp             x1, x0
    //     0xac4488: b.ls            #0xac460c
    //     0xac448c: str             x0, [THR, #0x50]  ; THR::top
    //     0xac4490: sub             x0, x0, #0xf
    //     0xac4494: movz            x1, #0xe15c
    //     0xac4498: movk            x1, #0x3, lsl #16
    //     0xac449c: stur            x1, [x0, #-1]
    // 0xac44a0: StoreField: r0->field_7 = d0
    //     0xac44a0: stur            d0, [x0, #7]
    // 0xac44a4: StoreField: r2->field_13 = r0
    //     0xac44a4: stur            w0, [x2, #0x13]
    // 0xac44a8: ldur            d0, [fp, #-0x28]
    // 0xac44ac: r0 = inline_Allocate_Double()
    //     0xac44ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac44b0: add             x0, x0, #0x10
    //     0xac44b4: cmp             x1, x0
    //     0xac44b8: b.ls            #0xac4624
    //     0xac44bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xac44c0: sub             x0, x0, #0xf
    //     0xac44c4: movz            x1, #0xe15c
    //     0xac44c8: movk            x1, #0x3, lsl #16
    //     0xac44cc: stur            x1, [x0, #-1]
    // 0xac44d0: StoreField: r0->field_7 = d0
    //     0xac44d0: stur            d0, [x0, #7]
    // 0xac44d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xac44d4: stur            w0, [x2, #0x17]
    // 0xac44d8: ldur            d0, [fp, #-0x30]
    // 0xac44dc: r0 = inline_Allocate_Double()
    //     0xac44dc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac44e0: add             x0, x0, #0x10
    //     0xac44e4: cmp             x1, x0
    //     0xac44e8: b.ls            #0xac463c
    //     0xac44ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xac44f0: sub             x0, x0, #0xf
    //     0xac44f4: movz            x1, #0xe15c
    //     0xac44f8: movk            x1, #0x3, lsl #16
    //     0xac44fc: stur            x1, [x0, #-1]
    // 0xac4500: StoreField: r0->field_7 = d0
    //     0xac4500: stur            d0, [x0, #7]
    // 0xac4504: StoreField: r2->field_1b = r0
    //     0xac4504: stur            w0, [x2, #0x1b]
    // 0xac4508: ldur            d0, [fp, #-0x38]
    // 0xac450c: r0 = inline_Allocate_Double()
    //     0xac450c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac4510: add             x0, x0, #0x10
    //     0xac4514: cmp             x1, x0
    //     0xac4518: b.ls            #0xac4654
    //     0xac451c: str             x0, [THR, #0x50]  ; THR::top
    //     0xac4520: sub             x0, x0, #0xf
    //     0xac4524: movz            x1, #0xe15c
    //     0xac4528: movk            x1, #0x3, lsl #16
    //     0xac452c: stur            x1, [x0, #-1]
    // 0xac4530: StoreField: r0->field_7 = d0
    //     0xac4530: stur            d0, [x0, #7]
    // 0xac4534: StoreField: r2->field_1f = r0
    //     0xac4534: stur            w0, [x2, #0x1f]
    // 0xac4538: ldur            d0, [fp, #-0x40]
    // 0xac453c: r0 = inline_Allocate_Double()
    //     0xac453c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac4540: add             x0, x0, #0x10
    //     0xac4544: cmp             x1, x0
    //     0xac4548: b.ls            #0xac466c
    //     0xac454c: str             x0, [THR, #0x50]  ; THR::top
    //     0xac4550: sub             x0, x0, #0xf
    //     0xac4554: movz            x1, #0xe15c
    //     0xac4558: movk            x1, #0x3, lsl #16
    //     0xac455c: stur            x1, [x0, #-1]
    // 0xac4560: StoreField: r0->field_7 = d0
    //     0xac4560: stur            d0, [x0, #7]
    // 0xac4564: StoreField: r2->field_23 = r0
    //     0xac4564: stur            w0, [x2, #0x23]
    // 0xac4568: r1 = <num>
    //     0xac4568: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xac456c: r0 = AllocateGrowableArray()
    //     0xac456c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xac4570: mov             x1, x0
    // 0xac4574: ldur            x0, [fp, #-0x18]
    // 0xac4578: stur            x1, [fp, #-8]
    // 0xac457c: StoreField: r1->field_f = r0
    //     0xac457c: stur            w0, [x1, #0xf]
    // 0xac4580: r0 = 12
    //     0xac4580: movz            x0, #0xc
    // 0xac4584: StoreField: r1->field_b = r0
    //     0xac4584: stur            w0, [x1, #0xb]
    // 0xac4588: r0 = PdfNumList()
    //     0xac4588: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xac458c: mov             x1, x0
    // 0xac4590: ldur            x0, [fp, #-8]
    // 0xac4594: StoreField: r1->field_7 = r0
    //     0xac4594: stur            w0, [x1, #7]
    // 0xac4598: ldur            x0, [fp, #-0x10]
    // 0xac459c: LoadField: r2 = r0->field_f
    //     0xac459c: ldur            w2, [x0, #0xf]
    // 0xac45a0: DecompressPointer r2
    //     0xac45a0: add             x2, x2, HEAP, lsl #32
    // 0xac45a4: LoadField: r4 = r0->field_13
    //     0xac45a4: ldur            w4, [x0, #0x13]
    // 0xac45a8: DecompressPointer r4
    //     0xac45a8: add             x4, x4, HEAP, lsl #32
    // 0xac45ac: mov             x3, x4
    // 0xac45b0: stur            x4, [fp, #-8]
    // 0xac45b4: r0 = output()
    //     0xac45b4: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xac45b8: ldur            x1, [fp, #-8]
    // 0xac45bc: r2 = " c "
    //     0xac45bc: add             x2, PP, #0x26, lsl #12  ; [pp+0x261d0] " c "
    //     0xac45c0: ldr             x2, [x2, #0x1d0]
    // 0xac45c4: r0 = putString()
    //     0xac45c4: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xac45c8: r0 = Null
    //     0xac45c8: mov             x0, NULL
    // 0xac45cc: LeaveFrame
    //     0xac45cc: mov             SP, fp
    //     0xac45d0: ldp             fp, lr, [SP], #0x10
    // 0xac45d4: ret
    //     0xac45d4: ret             
    // 0xac45d8: r0 = StackOverflowSharedWithFPURegs()
    //     0xac45d8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac45dc: b               #0xac4430
    // 0xac45e0: stp             q4, q5, [SP, #-0x20]!
    // 0xac45e4: stp             q2, q3, [SP, #-0x20]!
    // 0xac45e8: stp             q0, q1, [SP, #-0x20]!
    // 0xac45ec: stp             x0, x3, [SP, #-0x10]!
    // 0xac45f0: r0 = AllocateDouble()
    //     0xac45f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac45f4: mov             x4, x0
    // 0xac45f8: ldp             x0, x3, [SP], #0x10
    // 0xac45fc: ldp             q0, q1, [SP], #0x20
    // 0xac4600: ldp             q2, q3, [SP], #0x20
    // 0xac4604: ldp             q4, q5, [SP], #0x20
    // 0xac4608: b               #0xac4454
    // 0xac460c: SaveReg d0
    //     0xac460c: str             q0, [SP, #-0x10]!
    // 0xac4610: SaveReg r2
    //     0xac4610: str             x2, [SP, #-8]!
    // 0xac4614: r0 = AllocateDouble()
    //     0xac4614: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4618: RestoreReg r2
    //     0xac4618: ldr             x2, [SP], #8
    // 0xac461c: RestoreReg d0
    //     0xac461c: ldr             q0, [SP], #0x10
    // 0xac4620: b               #0xac44a0
    // 0xac4624: SaveReg d0
    //     0xac4624: str             q0, [SP, #-0x10]!
    // 0xac4628: SaveReg r2
    //     0xac4628: str             x2, [SP, #-8]!
    // 0xac462c: r0 = AllocateDouble()
    //     0xac462c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4630: RestoreReg r2
    //     0xac4630: ldr             x2, [SP], #8
    // 0xac4634: RestoreReg d0
    //     0xac4634: ldr             q0, [SP], #0x10
    // 0xac4638: b               #0xac44d0
    // 0xac463c: SaveReg d0
    //     0xac463c: str             q0, [SP, #-0x10]!
    // 0xac4640: SaveReg r2
    //     0xac4640: str             x2, [SP, #-8]!
    // 0xac4644: r0 = AllocateDouble()
    //     0xac4644: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4648: RestoreReg r2
    //     0xac4648: ldr             x2, [SP], #8
    // 0xac464c: RestoreReg d0
    //     0xac464c: ldr             q0, [SP], #0x10
    // 0xac4650: b               #0xac4500
    // 0xac4654: SaveReg d0
    //     0xac4654: str             q0, [SP, #-0x10]!
    // 0xac4658: SaveReg r2
    //     0xac4658: str             x2, [SP, #-8]!
    // 0xac465c: r0 = AllocateDouble()
    //     0xac465c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4660: RestoreReg r2
    //     0xac4660: ldr             x2, [SP], #8
    // 0xac4664: RestoreReg d0
    //     0xac4664: ldr             q0, [SP], #0x10
    // 0xac4668: b               #0xac4530
    // 0xac466c: SaveReg d0
    //     0xac466c: str             q0, [SP, #-0x10]!
    // 0xac4670: SaveReg r2
    //     0xac4670: str             x2, [SP, #-8]!
    // 0xac4674: r0 = AllocateDouble()
    //     0xac4674: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4678: RestoreReg r2
    //     0xac4678: ldr             x2, [SP], #8
    // 0xac467c: RestoreReg d0
    //     0xac467c: ldr             q0, [SP], #0x10
    // 0xac4680: b               #0xac4560
  }
  _ lineTo(/* No info */) {
    // ** addr: 0xac46e8, size: 0x14c
    // 0xac46e8: EnterFrame
    //     0xac46e8: stp             fp, lr, [SP, #-0x10]!
    //     0xac46ec: mov             fp, SP
    // 0xac46f0: AllocStack(0x20)
    //     0xac46f0: sub             SP, SP, #0x20
    // 0xac46f4: r0 = 4
    //     0xac46f4: movz            x0, #0x4
    // 0xac46f8: mov             x3, x1
    // 0xac46fc: stur            x1, [fp, #-0x10]
    // 0xac4700: stur            d1, [fp, #-0x20]
    // 0xac4704: CheckStackOverflow
    //     0xac4704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac4708: cmp             SP, x16
    //     0xac470c: b.ls            #0xac47f8
    // 0xac4710: r4 = inline_Allocate_Double()
    //     0xac4710: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xac4714: add             x4, x4, #0x10
    //     0xac4718: cmp             x1, x4
    //     0xac471c: b.ls            #0xac4800
    //     0xac4720: str             x4, [THR, #0x50]  ; THR::top
    //     0xac4724: sub             x4, x4, #0xf
    //     0xac4728: movz            x1, #0xe15c
    //     0xac472c: movk            x1, #0x3, lsl #16
    //     0xac4730: stur            x1, [x4, #-1]
    // 0xac4734: StoreField: r4->field_7 = d0
    //     0xac4734: stur            d0, [x4, #7]
    // 0xac4738: mov             x2, x0
    // 0xac473c: stur            x4, [fp, #-8]
    // 0xac4740: r1 = Null
    //     0xac4740: mov             x1, NULL
    // 0xac4744: r0 = AllocateArray()
    //     0xac4744: bl              #0xec22fc  ; AllocateArrayStub
    // 0xac4748: mov             x2, x0
    // 0xac474c: ldur            x0, [fp, #-8]
    // 0xac4750: stur            x2, [fp, #-0x18]
    // 0xac4754: StoreField: r2->field_f = r0
    //     0xac4754: stur            w0, [x2, #0xf]
    // 0xac4758: ldur            d0, [fp, #-0x20]
    // 0xac475c: r0 = inline_Allocate_Double()
    //     0xac475c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac4760: add             x0, x0, #0x10
    //     0xac4764: cmp             x1, x0
    //     0xac4768: b.ls            #0xac481c
    //     0xac476c: str             x0, [THR, #0x50]  ; THR::top
    //     0xac4770: sub             x0, x0, #0xf
    //     0xac4774: movz            x1, #0xe15c
    //     0xac4778: movk            x1, #0x3, lsl #16
    //     0xac477c: stur            x1, [x0, #-1]
    // 0xac4780: StoreField: r0->field_7 = d0
    //     0xac4780: stur            d0, [x0, #7]
    // 0xac4784: StoreField: r2->field_13 = r0
    //     0xac4784: stur            w0, [x2, #0x13]
    // 0xac4788: r1 = <num>
    //     0xac4788: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xac478c: r0 = AllocateGrowableArray()
    //     0xac478c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xac4790: mov             x1, x0
    // 0xac4794: ldur            x0, [fp, #-0x18]
    // 0xac4798: stur            x1, [fp, #-8]
    // 0xac479c: StoreField: r1->field_f = r0
    //     0xac479c: stur            w0, [x1, #0xf]
    // 0xac47a0: r0 = 4
    //     0xac47a0: movz            x0, #0x4
    // 0xac47a4: StoreField: r1->field_b = r0
    //     0xac47a4: stur            w0, [x1, #0xb]
    // 0xac47a8: r0 = PdfNumList()
    //     0xac47a8: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xac47ac: mov             x1, x0
    // 0xac47b0: ldur            x0, [fp, #-8]
    // 0xac47b4: StoreField: r1->field_7 = r0
    //     0xac47b4: stur            w0, [x1, #7]
    // 0xac47b8: ldur            x0, [fp, #-0x10]
    // 0xac47bc: LoadField: r2 = r0->field_f
    //     0xac47bc: ldur            w2, [x0, #0xf]
    // 0xac47c0: DecompressPointer r2
    //     0xac47c0: add             x2, x2, HEAP, lsl #32
    // 0xac47c4: LoadField: r4 = r0->field_13
    //     0xac47c4: ldur            w4, [x0, #0x13]
    // 0xac47c8: DecompressPointer r4
    //     0xac47c8: add             x4, x4, HEAP, lsl #32
    // 0xac47cc: mov             x3, x4
    // 0xac47d0: stur            x4, [fp, #-8]
    // 0xac47d4: r0 = output()
    //     0xac47d4: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xac47d8: ldur            x1, [fp, #-8]
    // 0xac47dc: r2 = " l "
    //     0xac47dc: add             x2, PP, #0x26, lsl #12  ; [pp+0x261e8] " l "
    //     0xac47e0: ldr             x2, [x2, #0x1e8]
    // 0xac47e4: r0 = putString()
    //     0xac47e4: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xac47e8: r0 = Null
    //     0xac47e8: mov             x0, NULL
    // 0xac47ec: LeaveFrame
    //     0xac47ec: mov             SP, fp
    //     0xac47f0: ldp             fp, lr, [SP], #0x10
    // 0xac47f4: ret
    //     0xac47f4: ret             
    // 0xac47f8: r0 = StackOverflowSharedWithFPURegs()
    //     0xac47f8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac47fc: b               #0xac4710
    // 0xac4800: stp             q0, q1, [SP, #-0x20]!
    // 0xac4804: stp             x0, x3, [SP, #-0x10]!
    // 0xac4808: r0 = AllocateDouble()
    //     0xac4808: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac480c: mov             x4, x0
    // 0xac4810: ldp             x0, x3, [SP], #0x10
    // 0xac4814: ldp             q0, q1, [SP], #0x20
    // 0xac4818: b               #0xac4734
    // 0xac481c: SaveReg d0
    //     0xac481c: str             q0, [SP, #-0x10]!
    // 0xac4820: SaveReg r2
    //     0xac4820: str             x2, [SP, #-8]!
    // 0xac4824: r0 = AllocateDouble()
    //     0xac4824: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac4828: RestoreReg r2
    //     0xac4828: ldr             x2, [SP], #8
    // 0xac482c: RestoreReg d0
    //     0xac482c: ldr             q0, [SP], #0x10
    // 0xac4830: b               #0xac4780
  }
  _ moveTo(/* No info */) {
    // ** addr: 0xac6914, size: 0x14c
    // 0xac6914: EnterFrame
    //     0xac6914: stp             fp, lr, [SP, #-0x10]!
    //     0xac6918: mov             fp, SP
    // 0xac691c: AllocStack(0x20)
    //     0xac691c: sub             SP, SP, #0x20
    // 0xac6920: r0 = 4
    //     0xac6920: movz            x0, #0x4
    // 0xac6924: mov             x3, x1
    // 0xac6928: stur            x1, [fp, #-0x10]
    // 0xac692c: stur            d1, [fp, #-0x20]
    // 0xac6930: CheckStackOverflow
    //     0xac6930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6934: cmp             SP, x16
    //     0xac6938: b.ls            #0xac6a24
    // 0xac693c: r4 = inline_Allocate_Double()
    //     0xac693c: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xac6940: add             x4, x4, #0x10
    //     0xac6944: cmp             x1, x4
    //     0xac6948: b.ls            #0xac6a2c
    //     0xac694c: str             x4, [THR, #0x50]  ; THR::top
    //     0xac6950: sub             x4, x4, #0xf
    //     0xac6954: movz            x1, #0xe15c
    //     0xac6958: movk            x1, #0x3, lsl #16
    //     0xac695c: stur            x1, [x4, #-1]
    // 0xac6960: StoreField: r4->field_7 = d0
    //     0xac6960: stur            d0, [x4, #7]
    // 0xac6964: mov             x2, x0
    // 0xac6968: stur            x4, [fp, #-8]
    // 0xac696c: r1 = Null
    //     0xac696c: mov             x1, NULL
    // 0xac6970: r0 = AllocateArray()
    //     0xac6970: bl              #0xec22fc  ; AllocateArrayStub
    // 0xac6974: mov             x2, x0
    // 0xac6978: ldur            x0, [fp, #-8]
    // 0xac697c: stur            x2, [fp, #-0x18]
    // 0xac6980: StoreField: r2->field_f = r0
    //     0xac6980: stur            w0, [x2, #0xf]
    // 0xac6984: ldur            d0, [fp, #-0x20]
    // 0xac6988: r0 = inline_Allocate_Double()
    //     0xac6988: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac698c: add             x0, x0, #0x10
    //     0xac6990: cmp             x1, x0
    //     0xac6994: b.ls            #0xac6a48
    //     0xac6998: str             x0, [THR, #0x50]  ; THR::top
    //     0xac699c: sub             x0, x0, #0xf
    //     0xac69a0: movz            x1, #0xe15c
    //     0xac69a4: movk            x1, #0x3, lsl #16
    //     0xac69a8: stur            x1, [x0, #-1]
    // 0xac69ac: StoreField: r0->field_7 = d0
    //     0xac69ac: stur            d0, [x0, #7]
    // 0xac69b0: StoreField: r2->field_13 = r0
    //     0xac69b0: stur            w0, [x2, #0x13]
    // 0xac69b4: r1 = <num>
    //     0xac69b4: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xac69b8: r0 = AllocateGrowableArray()
    //     0xac69b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xac69bc: mov             x1, x0
    // 0xac69c0: ldur            x0, [fp, #-0x18]
    // 0xac69c4: stur            x1, [fp, #-8]
    // 0xac69c8: StoreField: r1->field_f = r0
    //     0xac69c8: stur            w0, [x1, #0xf]
    // 0xac69cc: r0 = 4
    //     0xac69cc: movz            x0, #0x4
    // 0xac69d0: StoreField: r1->field_b = r0
    //     0xac69d0: stur            w0, [x1, #0xb]
    // 0xac69d4: r0 = PdfNumList()
    //     0xac69d4: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xac69d8: mov             x1, x0
    // 0xac69dc: ldur            x0, [fp, #-8]
    // 0xac69e0: StoreField: r1->field_7 = r0
    //     0xac69e0: stur            w0, [x1, #7]
    // 0xac69e4: ldur            x0, [fp, #-0x10]
    // 0xac69e8: LoadField: r2 = r0->field_f
    //     0xac69e8: ldur            w2, [x0, #0xf]
    // 0xac69ec: DecompressPointer r2
    //     0xac69ec: add             x2, x2, HEAP, lsl #32
    // 0xac69f0: LoadField: r4 = r0->field_13
    //     0xac69f0: ldur            w4, [x0, #0x13]
    // 0xac69f4: DecompressPointer r4
    //     0xac69f4: add             x4, x4, HEAP, lsl #32
    // 0xac69f8: mov             x3, x4
    // 0xac69fc: stur            x4, [fp, #-8]
    // 0xac6a00: r0 = output()
    //     0xac6a00: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xac6a04: ldur            x1, [fp, #-8]
    // 0xac6a08: r2 = " m "
    //     0xac6a08: add             x2, PP, #0x26, lsl #12  ; [pp+0x26200] " m "
    //     0xac6a0c: ldr             x2, [x2, #0x200]
    // 0xac6a10: r0 = putString()
    //     0xac6a10: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xac6a14: r0 = Null
    //     0xac6a14: mov             x0, NULL
    // 0xac6a18: LeaveFrame
    //     0xac6a18: mov             SP, fp
    //     0xac6a1c: ldp             fp, lr, [SP], #0x10
    // 0xac6a20: ret
    //     0xac6a20: ret             
    // 0xac6a24: r0 = StackOverflowSharedWithFPURegs()
    //     0xac6a24: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac6a28: b               #0xac693c
    // 0xac6a2c: stp             q0, q1, [SP, #-0x20]!
    // 0xac6a30: stp             x0, x3, [SP, #-0x10]!
    // 0xac6a34: r0 = AllocateDouble()
    //     0xac6a34: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac6a38: mov             x4, x0
    // 0xac6a3c: ldp             x0, x3, [SP], #0x10
    // 0xac6a40: ldp             q0, q1, [SP], #0x20
    // 0xac6a44: b               #0xac6960
    // 0xac6a48: SaveReg d0
    //     0xac6a48: str             q0, [SP, #-0x10]!
    // 0xac6a4c: SaveReg r2
    //     0xac6a4c: str             x2, [SP, #-8]!
    // 0xac6a50: r0 = AllocateDouble()
    //     0xac6a50: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac6a54: RestoreReg r2
    //     0xac6a54: ldr             x2, [SP], #8
    // 0xac6a58: RestoreReg d0
    //     0xac6a58: ldr             q0, [SP], #0x10
    // 0xac6a5c: b               #0xac69ac
  }
  _ closePath(/* No info */) {
    // ** addr: 0xac6bc8, size: 0x70
    // 0xac6bc8: EnterFrame
    //     0xac6bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xac6bcc: mov             fp, SP
    // 0xac6bd0: AllocStack(0x8)
    //     0xac6bd0: sub             SP, SP, #8
    // 0xac6bd4: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */)
    //     0xac6bd4: mov             x0, x1
    //     0xac6bd8: stur            x1, [fp, #-8]
    // 0xac6bdc: CheckStackOverflow
    //     0xac6bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6be0: cmp             SP, x16
    //     0xac6be4: b.ls            #0xac6c30
    // 0xac6be8: LoadField: r1 = r0->field_13
    //     0xac6be8: ldur            w1, [x0, #0x13]
    // 0xac6bec: DecompressPointer r1
    //     0xac6bec: add             x1, x1, HEAP, lsl #32
    // 0xac6bf0: r2 = "h "
    //     0xac6bf0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26218] "h "
    //     0xac6bf4: ldr             x2, [x2, #0x218]
    // 0xac6bf8: r0 = putString()
    //     0xac6bf8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xac6bfc: ldur            x0, [fp, #-8]
    // 0xac6c00: LoadField: r1 = r0->field_f
    //     0xac6c00: ldur            w1, [x0, #0xf]
    // 0xac6c04: DecompressPointer r1
    //     0xac6c04: add             x1, x1, HEAP, lsl #32
    // 0xac6c08: r0 = LoadClassIdInstr(r1)
    //     0xac6c08: ldur            x0, [x1, #-1]
    //     0xac6c0c: ubfx            x0, x0, #0xc, #0x14
    // 0xac6c10: r2 = true
    //     0xac6c10: add             x2, NULL, #0x20  ; true
    // 0xac6c14: r0 = GDT[cid_x0 + -0x1000]()
    //     0xac6c14: sub             lr, x0, #1, lsl #12
    //     0xac6c18: ldr             lr, [x21, lr, lsl #3]
    //     0xac6c1c: blr             lr
    // 0xac6c20: r0 = Null
    //     0xac6c20: mov             x0, NULL
    // 0xac6c24: LeaveFrame
    //     0xac6c24: mov             SP, fp
    //     0xac6c28: ldp             fp, lr, [SP], #0x10
    // 0xac6c2c: ret
    //     0xac6c2c: ret             
    // 0xac6c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac6c30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac6c34: b               #0xac6be8
  }
  _ restoreContext(/* No info */) {
    // ** addr: 0xe46864, size: 0x90
    // 0xe46864: EnterFrame
    //     0xe46864: stp             fp, lr, [SP, #-0x10]!
    //     0xe46868: mov             fp, SP
    // 0xe4686c: AllocStack(0x10)
    //     0xe4686c: sub             SP, SP, #0x10
    // 0xe46870: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x10 */)
    //     0xe46870: mov             x0, x1
    //     0xe46874: stur            x1, [fp, #-0x10]
    // 0xe46878: CheckStackOverflow
    //     0xe46878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4687c: cmp             SP, x16
    //     0xe46880: b.ls            #0xe468ec
    // 0xe46884: LoadField: r3 = r0->field_b
    //     0xe46884: ldur            w3, [x0, #0xb]
    // 0xe46888: DecompressPointer r3
    //     0xe46888: add             x3, x3, HEAP, lsl #32
    // 0xe4688c: stur            x3, [fp, #-8]
    // 0xe46890: LoadField: r1 = r3->field_f
    //     0xe46890: ldur            x1, [x3, #0xf]
    // 0xe46894: ArrayLoad: r2 = r3[0]  ; List_8
    //     0xe46894: ldur            x2, [x3, #0x17]
    // 0xe46898: cmp             x1, x2
    // 0xe4689c: b.eq            #0xe468dc
    // 0xe468a0: LoadField: r1 = r0->field_13
    //     0xe468a0: ldur            w1, [x0, #0x13]
    // 0xe468a4: DecompressPointer r1
    //     0xe468a4: add             x1, x1, HEAP, lsl #32
    // 0xe468a8: r2 = "Q "
    //     0xe468a8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36710] "Q "
    //     0xe468ac: ldr             x2, [x2, #0x710]
    // 0xe468b0: r0 = putString()
    //     0xe468b0: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe468b4: ldur            x1, [fp, #-8]
    // 0xe468b8: r0 = removeLast()
    //     0xe468b8: bl              #0x64f180  ; [dart:collection] ListQueue::removeLast
    // 0xe468bc: ldur            x1, [fp, #-0x10]
    // 0xe468c0: StoreField: r1->field_7 = r0
    //     0xe468c0: stur            w0, [x1, #7]
    //     0xe468c4: ldurb           w16, [x1, #-1]
    //     0xe468c8: ldurb           w17, [x0, #-1]
    //     0xe468cc: and             x16, x17, x16, lsr #2
    //     0xe468d0: tst             x16, HEAP, lsr #32
    //     0xe468d4: b.eq            #0xe468dc
    //     0xe468d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe468dc: r0 = Null
    //     0xe468dc: mov             x0, NULL
    // 0xe468e0: LeaveFrame
    //     0xe468e0: mov             SP, fp
    //     0xe468e4: ldp             fp, lr, [SP], #0x10
    // 0xe468e8: ret
    //     0xe468e8: ret             
    // 0xe468ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe468ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe468f0: b               #0xe46884
  }
  _ PdfGraphics(/* No info */) {
    // ** addr: 0xe46df0, size: 0x124
    // 0xe46df0: EnterFrame
    //     0xe46df0: stp             fp, lr, [SP, #-0x10]!
    //     0xe46df4: mov             fp, SP
    // 0xe46df8: AllocStack(0x20)
    //     0xe46df8: sub             SP, SP, #0x20
    // 0xe46dfc: r0 = Sentinel
    //     0xe46dfc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe46e00: mov             x4, x1
    // 0xe46e04: stur            x2, [fp, #-0x10]
    // 0xe46e08: mov             x16, x3
    // 0xe46e0c: mov             x3, x2
    // 0xe46e10: mov             x2, x16
    // 0xe46e14: stur            x1, [fp, #-8]
    // 0xe46e18: stur            x2, [fp, #-0x18]
    // 0xe46e1c: CheckStackOverflow
    //     0xe46e1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46e20: cmp             SP, x16
    //     0xe46e24: b.ls            #0xe46f0c
    // 0xe46e28: StoreField: r4->field_7 = r0
    //     0xe46e28: stur            w0, [x4, #7]
    // 0xe46e2c: r1 = <_PdfGraphicsContext>
    //     0xe46e2c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36810] TypeArguments: <_PdfGraphicsContext>
    //     0xe46e30: ldr             x1, [x1, #0x810]
    // 0xe46e34: r0 = ListQueue()
    //     0xe46e34: bl              #0x61b3b8  ; AllocateListQueueStub -> ListQueue<X0> (size=0x28)
    // 0xe46e38: mov             x1, x0
    // 0xe46e3c: stur            x0, [fp, #-0x20]
    // 0xe46e40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe46e40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe46e44: r0 = ListQueue()
    //     0xe46e44: bl              #0x61b248  ; [dart:collection] ListQueue::ListQueue
    // 0xe46e48: ldur            x0, [fp, #-0x20]
    // 0xe46e4c: ldur            x1, [fp, #-8]
    // 0xe46e50: StoreField: r1->field_b = r0
    //     0xe46e50: stur            w0, [x1, #0xb]
    //     0xe46e54: ldurb           w16, [x1, #-1]
    //     0xe46e58: ldurb           w17, [x0, #-1]
    //     0xe46e5c: and             x16, x17, x16, lsr #2
    //     0xe46e60: tst             x16, HEAP, lsr #32
    //     0xe46e64: b.eq            #0xe46e6c
    //     0xe46e68: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46e6c: ldur            x0, [fp, #-0x10]
    // 0xe46e70: StoreField: r1->field_f = r0
    //     0xe46e70: stur            w0, [x1, #0xf]
    //     0xe46e74: ldurb           w16, [x1, #-1]
    //     0xe46e78: ldurb           w17, [x0, #-1]
    //     0xe46e7c: and             x16, x17, x16, lsr #2
    //     0xe46e80: tst             x16, HEAP, lsr #32
    //     0xe46e84: b.eq            #0xe46e8c
    //     0xe46e88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46e8c: ldur            x0, [fp, #-0x18]
    // 0xe46e90: StoreField: r1->field_13 = r0
    //     0xe46e90: stur            w0, [x1, #0x13]
    //     0xe46e94: ldurb           w16, [x1, #-1]
    //     0xe46e98: ldurb           w17, [x0, #-1]
    //     0xe46e9c: and             x16, x17, x16, lsr #2
    //     0xe46ea0: tst             x16, HEAP, lsr #32
    //     0xe46ea4: b.eq            #0xe46eac
    //     0xe46ea8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46eac: r0 = Matrix4()
    //     0xe46eac: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe46eb0: r4 = 32
    //     0xe46eb0: movz            x4, #0x20
    // 0xe46eb4: stur            x0, [fp, #-0x10]
    // 0xe46eb8: r0 = AllocateFloat64Array()
    //     0xe46eb8: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe46ebc: mov             x1, x0
    // 0xe46ec0: ldur            x0, [fp, #-0x10]
    // 0xe46ec4: StoreField: r0->field_7 = r1
    //     0xe46ec4: stur            w1, [x0, #7]
    // 0xe46ec8: mov             x1, x0
    // 0xe46ecc: r0 = setIdentity()
    //     0xe46ecc: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe46ed0: r0 = _PdfGraphicsContext()
    //     0xe46ed0: bl              #0xe46f14  ; Allocate_PdfGraphicsContextStub -> _PdfGraphicsContext (size=0xc)
    // 0xe46ed4: ldur            x1, [fp, #-0x10]
    // 0xe46ed8: StoreField: r0->field_7 = r1
    //     0xe46ed8: stur            w1, [x0, #7]
    // 0xe46edc: ldur            x1, [fp, #-8]
    // 0xe46ee0: StoreField: r1->field_7 = r0
    //     0xe46ee0: stur            w0, [x1, #7]
    //     0xe46ee4: ldurb           w16, [x1, #-1]
    //     0xe46ee8: ldurb           w17, [x0, #-1]
    //     0xe46eec: and             x16, x17, x16, lsr #2
    //     0xe46ef0: tst             x16, HEAP, lsr #32
    //     0xe46ef4: b.eq            #0xe46efc
    //     0xe46ef8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46efc: r0 = Null
    //     0xe46efc: mov             x0, NULL
    // 0xe46f00: LeaveFrame
    //     0xe46f00: mov             SP, fp
    //     0xe46f04: ldp             fp, lr, [SP], #0x10
    // 0xe46f08: ret
    //     0xe46f08: ret             
    // 0xe46f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe46f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe46f10: b               #0xe46e28
  }
  _ setGraphicState(/* No info */) {
    // ** addr: 0xe47304, size: 0xa4
    // 0xe47304: EnterFrame
    //     0xe47304: stp             fp, lr, [SP, #-0x10]!
    //     0xe47308: mov             fp, SP
    // 0xe4730c: AllocStack(0x20)
    //     0xe4730c: sub             SP, SP, #0x20
    // 0xe47310: SetupParameters(PdfGraphics this /* r1 => r3, fp-0x8 */)
    //     0xe47310: mov             x3, x1
    //     0xe47314: stur            x1, [fp, #-8]
    // 0xe47318: CheckStackOverflow
    //     0xe47318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4731c: cmp             SP, x16
    //     0xe47320: b.ls            #0xe473a0
    // 0xe47324: LoadField: r1 = r3->field_f
    //     0xe47324: ldur            w1, [x3, #0xf]
    // 0xe47328: DecompressPointer r1
    //     0xe47328: add             x1, x1, HEAP, lsl #32
    // 0xe4732c: r0 = LoadClassIdInstr(r1)
    //     0xe4732c: ldur            x0, [x1, #-1]
    //     0xe47330: ubfx            x0, x0, #0xc, #0x14
    // 0xe47334: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe47334: sub             lr, x0, #0xffe
    //     0xe47338: ldr             lr, [x21, lr, lsl #3]
    //     0xe4733c: blr             lr
    // 0xe47340: mov             x3, x0
    // 0xe47344: ldur            x0, [fp, #-8]
    // 0xe47348: stur            x3, [fp, #-0x18]
    // 0xe4734c: LoadField: r4 = r0->field_13
    //     0xe4734c: ldur            w4, [x0, #0x13]
    // 0xe47350: DecompressPointer r4
    //     0xe47350: add             x4, x4, HEAP, lsl #32
    // 0xe47354: stur            x4, [fp, #-0x10]
    // 0xe47358: r1 = Null
    //     0xe47358: mov             x1, NULL
    // 0xe4735c: r2 = 4
    //     0xe4735c: movz            x2, #0x4
    // 0xe47360: r0 = AllocateArray()
    //     0xe47360: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe47364: mov             x1, x0
    // 0xe47368: ldur            x0, [fp, #-0x18]
    // 0xe4736c: StoreField: r1->field_f = r0
    //     0xe4736c: stur            w0, [x1, #0xf]
    // 0xe47370: r16 = " gs "
    //     0xe47370: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea80] " gs "
    //     0xe47374: ldr             x16, [x16, #0xa80]
    // 0xe47378: StoreField: r1->field_13 = r16
    //     0xe47378: stur            w16, [x1, #0x13]
    // 0xe4737c: str             x1, [SP]
    // 0xe47380: r0 = _interpolate()
    //     0xe47380: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe47384: ldur            x1, [fp, #-0x10]
    // 0xe47388: mov             x2, x0
    // 0xe4738c: r0 = putString()
    //     0xe4738c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe47390: r0 = Null
    //     0xe47390: mov             x0, NULL
    // 0xe47394: LeaveFrame
    //     0xe47394: mov             SP, fp
    //     0xe47398: ldp             fp, lr, [SP], #0x10
    // 0xe4739c: ret
    //     0xe4739c: ret             
    // 0xe473a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe473a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe473a4: b               #0xe47324
  }
  _ setTransform(/* No info */) {
    // ** addr: 0xe473b4, size: 0x370
    // 0xe473b4: EnterFrame
    //     0xe473b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe473b8: mov             fp, SP
    // 0xe473bc: AllocStack(0x48)
    //     0xe473bc: sub             SP, SP, #0x48
    // 0xe473c0: r3 = 12
    //     0xe473c0: movz            x3, #0xc
    // 0xe473c4: mov             x5, x1
    // 0xe473c8: mov             x4, x2
    // 0xe473cc: stur            x1, [fp, #-0x10]
    // 0xe473d0: stur            x2, [fp, #-0x18]
    // 0xe473d4: CheckStackOverflow
    //     0xe473d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe473d8: cmp             SP, x16
    //     0xe473dc: b.ls            #0xe47650
    // 0xe473e0: LoadField: r2 = r4->field_7
    //     0xe473e0: ldur            w2, [x4, #7]
    // 0xe473e4: DecompressPointer r2
    //     0xe473e4: add             x2, x2, HEAP, lsl #32
    // 0xe473e8: LoadField: r0 = r2->field_13
    //     0xe473e8: ldur            w0, [x2, #0x13]
    // 0xe473ec: r6 = LoadInt32Instr(r0)
    //     0xe473ec: sbfx            x6, x0, #1, #0x1f
    // 0xe473f0: mov             x0, x6
    // 0xe473f4: r1 = 0
    //     0xe473f4: movz            x1, #0
    // 0xe473f8: cmp             x1, x0
    // 0xe473fc: b.hs            #0xe47658
    // 0xe47400: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xe47400: ldur            d0, [x2, #0x17]
    // 0xe47404: mov             x0, x6
    // 0xe47408: r1 = 1
    //     0xe47408: movz            x1, #0x1
    // 0xe4740c: cmp             x1, x0
    // 0xe47410: b.hs            #0xe4765c
    // 0xe47414: LoadField: d1 = r2->field_1f
    //     0xe47414: ldur            d1, [x2, #0x1f]
    // 0xe47418: mov             x0, x6
    // 0xe4741c: stur            d1, [fp, #-0x48]
    // 0xe47420: r1 = 4
    //     0xe47420: movz            x1, #0x4
    // 0xe47424: cmp             x1, x0
    // 0xe47428: b.hs            #0xe47660
    // 0xe4742c: LoadField: d2 = r2->field_37
    //     0xe4742c: ldur            d2, [x2, #0x37]
    // 0xe47430: mov             x0, x6
    // 0xe47434: stur            d2, [fp, #-0x40]
    // 0xe47438: r1 = 5
    //     0xe47438: movz            x1, #0x5
    // 0xe4743c: cmp             x1, x0
    // 0xe47440: b.hs            #0xe47664
    // 0xe47444: LoadField: d3 = r2->field_3f
    //     0xe47444: ldur            d3, [x2, #0x3f]
    // 0xe47448: mov             x0, x6
    // 0xe4744c: stur            d3, [fp, #-0x38]
    // 0xe47450: r1 = 12
    //     0xe47450: movz            x1, #0xc
    // 0xe47454: cmp             x1, x0
    // 0xe47458: b.hs            #0xe47668
    // 0xe4745c: LoadField: d4 = r2->field_77
    //     0xe4745c: ldur            d4, [x2, #0x77]
    // 0xe47460: mov             x0, x6
    // 0xe47464: stur            d4, [fp, #-0x30]
    // 0xe47468: r1 = 13
    //     0xe47468: movz            x1, #0xd
    // 0xe4746c: cmp             x1, x0
    // 0xe47470: b.hs            #0xe4766c
    // 0xe47474: LoadField: d5 = r2->field_7f
    //     0xe47474: ldur            d5, [x2, #0x7f]
    // 0xe47478: stur            d5, [fp, #-0x28]
    // 0xe4747c: r0 = inline_Allocate_Double()
    //     0xe4747c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe47480: add             x0, x0, #0x10
    //     0xe47484: cmp             x1, x0
    //     0xe47488: b.ls            #0xe47670
    //     0xe4748c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe47490: sub             x0, x0, #0xf
    //     0xe47494: movz            x1, #0xe15c
    //     0xe47498: movk            x1, #0x3, lsl #16
    //     0xe4749c: stur            x1, [x0, #-1]
    // 0xe474a0: StoreField: r0->field_7 = d0
    //     0xe474a0: stur            d0, [x0, #7]
    // 0xe474a4: mov             x2, x3
    // 0xe474a8: stur            x0, [fp, #-8]
    // 0xe474ac: r1 = Null
    //     0xe474ac: mov             x1, NULL
    // 0xe474b0: r0 = AllocateArray()
    //     0xe474b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe474b4: mov             x2, x0
    // 0xe474b8: ldur            x0, [fp, #-8]
    // 0xe474bc: stur            x2, [fp, #-0x20]
    // 0xe474c0: StoreField: r2->field_f = r0
    //     0xe474c0: stur            w0, [x2, #0xf]
    // 0xe474c4: ldur            d0, [fp, #-0x48]
    // 0xe474c8: r0 = inline_Allocate_Double()
    //     0xe474c8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe474cc: add             x0, x0, #0x10
    //     0xe474d0: cmp             x1, x0
    //     0xe474d4: b.ls            #0xe476a0
    //     0xe474d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe474dc: sub             x0, x0, #0xf
    //     0xe474e0: movz            x1, #0xe15c
    //     0xe474e4: movk            x1, #0x3, lsl #16
    //     0xe474e8: stur            x1, [x0, #-1]
    // 0xe474ec: StoreField: r0->field_7 = d0
    //     0xe474ec: stur            d0, [x0, #7]
    // 0xe474f0: StoreField: r2->field_13 = r0
    //     0xe474f0: stur            w0, [x2, #0x13]
    // 0xe474f4: ldur            d0, [fp, #-0x40]
    // 0xe474f8: r0 = inline_Allocate_Double()
    //     0xe474f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe474fc: add             x0, x0, #0x10
    //     0xe47500: cmp             x1, x0
    //     0xe47504: b.ls            #0xe476b8
    //     0xe47508: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4750c: sub             x0, x0, #0xf
    //     0xe47510: movz            x1, #0xe15c
    //     0xe47514: movk            x1, #0x3, lsl #16
    //     0xe47518: stur            x1, [x0, #-1]
    // 0xe4751c: StoreField: r0->field_7 = d0
    //     0xe4751c: stur            d0, [x0, #7]
    // 0xe47520: ArrayStore: r2[0] = r0  ; List_4
    //     0xe47520: stur            w0, [x2, #0x17]
    // 0xe47524: ldur            d0, [fp, #-0x38]
    // 0xe47528: r0 = inline_Allocate_Double()
    //     0xe47528: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe4752c: add             x0, x0, #0x10
    //     0xe47530: cmp             x1, x0
    //     0xe47534: b.ls            #0xe476d0
    //     0xe47538: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4753c: sub             x0, x0, #0xf
    //     0xe47540: movz            x1, #0xe15c
    //     0xe47544: movk            x1, #0x3, lsl #16
    //     0xe47548: stur            x1, [x0, #-1]
    // 0xe4754c: StoreField: r0->field_7 = d0
    //     0xe4754c: stur            d0, [x0, #7]
    // 0xe47550: StoreField: r2->field_1b = r0
    //     0xe47550: stur            w0, [x2, #0x1b]
    // 0xe47554: ldur            d0, [fp, #-0x30]
    // 0xe47558: r0 = inline_Allocate_Double()
    //     0xe47558: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe4755c: add             x0, x0, #0x10
    //     0xe47560: cmp             x1, x0
    //     0xe47564: b.ls            #0xe476e8
    //     0xe47568: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4756c: sub             x0, x0, #0xf
    //     0xe47570: movz            x1, #0xe15c
    //     0xe47574: movk            x1, #0x3, lsl #16
    //     0xe47578: stur            x1, [x0, #-1]
    // 0xe4757c: StoreField: r0->field_7 = d0
    //     0xe4757c: stur            d0, [x0, #7]
    // 0xe47580: StoreField: r2->field_1f = r0
    //     0xe47580: stur            w0, [x2, #0x1f]
    // 0xe47584: ldur            d0, [fp, #-0x28]
    // 0xe47588: r0 = inline_Allocate_Double()
    //     0xe47588: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe4758c: add             x0, x0, #0x10
    //     0xe47590: cmp             x1, x0
    //     0xe47594: b.ls            #0xe47700
    //     0xe47598: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4759c: sub             x0, x0, #0xf
    //     0xe475a0: movz            x1, #0xe15c
    //     0xe475a4: movk            x1, #0x3, lsl #16
    //     0xe475a8: stur            x1, [x0, #-1]
    // 0xe475ac: StoreField: r0->field_7 = d0
    //     0xe475ac: stur            d0, [x0, #7]
    // 0xe475b0: StoreField: r2->field_23 = r0
    //     0xe475b0: stur            w0, [x2, #0x23]
    // 0xe475b4: r1 = <double>
    //     0xe475b4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe475b8: r0 = AllocateGrowableArray()
    //     0xe475b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe475bc: mov             x1, x0
    // 0xe475c0: ldur            x0, [fp, #-0x20]
    // 0xe475c4: stur            x1, [fp, #-8]
    // 0xe475c8: StoreField: r1->field_f = r0
    //     0xe475c8: stur            w0, [x1, #0xf]
    // 0xe475cc: r0 = 12
    //     0xe475cc: movz            x0, #0xc
    // 0xe475d0: StoreField: r1->field_b = r0
    //     0xe475d0: stur            w0, [x1, #0xb]
    // 0xe475d4: r0 = PdfNumList()
    //     0xe475d4: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe475d8: mov             x1, x0
    // 0xe475dc: ldur            x0, [fp, #-8]
    // 0xe475e0: StoreField: r1->field_7 = r0
    //     0xe475e0: stur            w0, [x1, #7]
    // 0xe475e4: ldur            x0, [fp, #-0x10]
    // 0xe475e8: LoadField: r2 = r0->field_f
    //     0xe475e8: ldur            w2, [x0, #0xf]
    // 0xe475ec: DecompressPointer r2
    //     0xe475ec: add             x2, x2, HEAP, lsl #32
    // 0xe475f0: LoadField: r4 = r0->field_13
    //     0xe475f0: ldur            w4, [x0, #0x13]
    // 0xe475f4: DecompressPointer r4
    //     0xe475f4: add             x4, x4, HEAP, lsl #32
    // 0xe475f8: mov             x3, x4
    // 0xe475fc: stur            x4, [fp, #-8]
    // 0xe47600: r0 = output()
    //     0xe47600: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe47604: ldur            x1, [fp, #-8]
    // 0xe47608: r2 = " cm "
    //     0xe47608: add             x2, PP, #0x36, lsl #12  ; [pp+0x36718] " cm "
    //     0xe4760c: ldr             x2, [x2, #0x718]
    // 0xe47610: r0 = putString()
    //     0xe47610: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe47614: ldur            x0, [fp, #-0x10]
    // 0xe47618: LoadField: r1 = r0->field_7
    //     0xe47618: ldur            w1, [x0, #7]
    // 0xe4761c: DecompressPointer r1
    //     0xe4761c: add             x1, x1, HEAP, lsl #32
    // 0xe47620: r16 = Sentinel
    //     0xe47620: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe47624: cmp             w1, w16
    // 0xe47628: b.eq            #0xe47718
    // 0xe4762c: LoadField: r0 = r1->field_7
    //     0xe4762c: ldur            w0, [x1, #7]
    // 0xe47630: DecompressPointer r0
    //     0xe47630: add             x0, x0, HEAP, lsl #32
    // 0xe47634: mov             x1, x0
    // 0xe47638: ldur            x2, [fp, #-0x18]
    // 0xe4763c: r0 = multiply()
    //     0xe4763c: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe47640: r0 = Null
    //     0xe47640: mov             x0, NULL
    // 0xe47644: LeaveFrame
    //     0xe47644: mov             SP, fp
    //     0xe47648: ldp             fp, lr, [SP], #0x10
    // 0xe4764c: ret
    //     0xe4764c: ret             
    // 0xe47650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47650: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47654: b               #0xe473e0
    // 0xe47658: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe47658: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4765c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe4765c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe47660: r0 = RangeErrorSharedWithFPURegs()
    //     0xe47660: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe47664: r0 = RangeErrorSharedWithFPURegs()
    //     0xe47664: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe47668: r0 = RangeErrorSharedWithFPURegs()
    //     0xe47668: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe4766c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe4766c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe47670: stp             q4, q5, [SP, #-0x20]!
    // 0xe47674: stp             q2, q3, [SP, #-0x20]!
    // 0xe47678: stp             q0, q1, [SP, #-0x20]!
    // 0xe4767c: stp             x4, x5, [SP, #-0x10]!
    // 0xe47680: SaveReg r3
    //     0xe47680: str             x3, [SP, #-8]!
    // 0xe47684: r0 = AllocateDouble()
    //     0xe47684: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe47688: RestoreReg r3
    //     0xe47688: ldr             x3, [SP], #8
    // 0xe4768c: ldp             x4, x5, [SP], #0x10
    // 0xe47690: ldp             q0, q1, [SP], #0x20
    // 0xe47694: ldp             q2, q3, [SP], #0x20
    // 0xe47698: ldp             q4, q5, [SP], #0x20
    // 0xe4769c: b               #0xe474a0
    // 0xe476a0: SaveReg d0
    //     0xe476a0: str             q0, [SP, #-0x10]!
    // 0xe476a4: SaveReg r2
    //     0xe476a4: str             x2, [SP, #-8]!
    // 0xe476a8: r0 = AllocateDouble()
    //     0xe476a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe476ac: RestoreReg r2
    //     0xe476ac: ldr             x2, [SP], #8
    // 0xe476b0: RestoreReg d0
    //     0xe476b0: ldr             q0, [SP], #0x10
    // 0xe476b4: b               #0xe474ec
    // 0xe476b8: SaveReg d0
    //     0xe476b8: str             q0, [SP, #-0x10]!
    // 0xe476bc: SaveReg r2
    //     0xe476bc: str             x2, [SP, #-8]!
    // 0xe476c0: r0 = AllocateDouble()
    //     0xe476c0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe476c4: RestoreReg r2
    //     0xe476c4: ldr             x2, [SP], #8
    // 0xe476c8: RestoreReg d0
    //     0xe476c8: ldr             q0, [SP], #0x10
    // 0xe476cc: b               #0xe4751c
    // 0xe476d0: SaveReg d0
    //     0xe476d0: str             q0, [SP, #-0x10]!
    // 0xe476d4: SaveReg r2
    //     0xe476d4: str             x2, [SP, #-8]!
    // 0xe476d8: r0 = AllocateDouble()
    //     0xe476d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe476dc: RestoreReg r2
    //     0xe476dc: ldr             x2, [SP], #8
    // 0xe476e0: RestoreReg d0
    //     0xe476e0: ldr             q0, [SP], #0x10
    // 0xe476e4: b               #0xe4754c
    // 0xe476e8: SaveReg d0
    //     0xe476e8: str             q0, [SP, #-0x10]!
    // 0xe476ec: SaveReg r2
    //     0xe476ec: str             x2, [SP, #-8]!
    // 0xe476f0: r0 = AllocateDouble()
    //     0xe476f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe476f4: RestoreReg r2
    //     0xe476f4: ldr             x2, [SP], #8
    // 0xe476f8: RestoreReg d0
    //     0xe476f8: ldr             q0, [SP], #0x10
    // 0xe476fc: b               #0xe4757c
    // 0xe47700: SaveReg d0
    //     0xe47700: str             q0, [SP, #-0x10]!
    // 0xe47704: SaveReg r2
    //     0xe47704: str             x2, [SP, #-8]!
    // 0xe47708: r0 = AllocateDouble()
    //     0xe47708: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe4770c: RestoreReg r2
    //     0xe4770c: ldr             x2, [SP], #8
    // 0xe47710: RestoreReg d0
    //     0xe47710: ldr             q0, [SP], #0x10
    // 0xe47714: b               #0xe475ac
    // 0xe47718: r9 = _context
    //     0xe47718: add             x9, PP, #0x36, lsl #12  ; [pp+0x36720] Field <PdfGraphics._context@2225251352>: late (offset: 0x8)
    //     0xe4771c: ldr             x9, [x9, #0x720]
    // 0xe47720: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe47720: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ clipPath(/* No info */) {
    // ** addr: 0xe479b0, size: 0x44
    // 0xe479b0: EnterFrame
    //     0xe479b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe479b4: mov             fp, SP
    // 0xe479b8: CheckStackOverflow
    //     0xe479b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe479bc: cmp             SP, x16
    //     0xe479c0: b.ls            #0xe479ec
    // 0xe479c4: LoadField: r0 = r1->field_13
    //     0xe479c4: ldur            w0, [x1, #0x13]
    // 0xe479c8: DecompressPointer r0
    //     0xe479c8: add             x0, x0, HEAP, lsl #32
    // 0xe479cc: mov             x1, x0
    // 0xe479d0: r2 = "W n "
    //     0xe479d0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e0b8] "W n "
    //     0xe479d4: ldr             x2, [x2, #0xb8]
    // 0xe479d8: r0 = putString()
    //     0xe479d8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe479dc: r0 = Null
    //     0xe479dc: mov             x0, NULL
    // 0xe479e0: LeaveFrame
    //     0xe479e0: mov             SP, fp
    //     0xe479e4: ldp             fp, lr, [SP], #0x10
    // 0xe479e8: ret
    //     0xe479e8: ret             
    // 0xe479ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe479ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe479f0: b               #0xe479c4
  }
  _ saveContext(/* No info */) {
    // ** addr: 0xe479f4, size: 0x8c
    // 0xe479f4: EnterFrame
    //     0xe479f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe479f8: mov             fp, SP
    // 0xe479fc: AllocStack(0x10)
    //     0xe479fc: sub             SP, SP, #0x10
    // 0xe47a00: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */)
    //     0xe47a00: mov             x0, x1
    //     0xe47a04: stur            x1, [fp, #-8]
    // 0xe47a08: CheckStackOverflow
    //     0xe47a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47a0c: cmp             SP, x16
    //     0xe47a10: b.ls            #0xe47a6c
    // 0xe47a14: LoadField: r1 = r0->field_13
    //     0xe47a14: ldur            w1, [x0, #0x13]
    // 0xe47a18: DecompressPointer r1
    //     0xe47a18: add             x1, x1, HEAP, lsl #32
    // 0xe47a1c: r2 = "q "
    //     0xe47a1c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36728] "q "
    //     0xe47a20: ldr             x2, [x2, #0x728]
    // 0xe47a24: r0 = putString()
    //     0xe47a24: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe47a28: ldur            x0, [fp, #-8]
    // 0xe47a2c: LoadField: r2 = r0->field_b
    //     0xe47a2c: ldur            w2, [x0, #0xb]
    // 0xe47a30: DecompressPointer r2
    //     0xe47a30: add             x2, x2, HEAP, lsl #32
    // 0xe47a34: stur            x2, [fp, #-0x10]
    // 0xe47a38: LoadField: r1 = r0->field_7
    //     0xe47a38: ldur            w1, [x0, #7]
    // 0xe47a3c: DecompressPointer r1
    //     0xe47a3c: add             x1, x1, HEAP, lsl #32
    // 0xe47a40: r16 = Sentinel
    //     0xe47a40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe47a44: cmp             w1, w16
    // 0xe47a48: b.eq            #0xe47a74
    // 0xe47a4c: r0 = copy()
    //     0xe47a4c: bl              #0xe47a80  ; [package:pdf/src/pdf/graphics.dart] _PdfGraphicsContext::copy
    // 0xe47a50: ldur            x1, [fp, #-0x10]
    // 0xe47a54: mov             x2, x0
    // 0xe47a58: r0 = _add()
    //     0xe47a58: bl              #0x61a57c  ; [dart:collection] ListQueue::_add
    // 0xe47a5c: r0 = Null
    //     0xe47a5c: mov             x0, NULL
    // 0xe47a60: LeaveFrame
    //     0xe47a60: mov             SP, fp
    //     0xe47a64: ldp             fp, lr, [SP], #0x10
    // 0xe47a68: ret
    //     0xe47a68: ret             
    // 0xe47a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47a70: b               #0xe47a14
    // 0xe47a74: r9 = _context
    //     0xe47a74: add             x9, PP, #0x36, lsl #12  ; [pp+0x36720] Field <PdfGraphics._context@2225251352>: late (offset: 0x8)
    //     0xe47a78: ldr             x9, [x9, #0x720]
    // 0xe47a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe47a7c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ drawImage(/* No info */) {
    // ** addr: 0xe47e7c, size: 0x105c
    // 0xe47e7c: EnterFrame
    //     0xe47e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe47e80: mov             fp, SP
    // 0xe47e84: AllocStack(0x80)
    //     0xe47e84: sub             SP, SP, #0x80
    // 0xe47e88: SetupParameters(PdfGraphics this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x38 */, dynamic _ /* d1 => d1, fp-0x40 */, [dynamic _ = Null /* r0 */, dynamic _ = Null /* r4, fp-0x8 */])
    //     0xe47e88: mov             x3, x1
    //     0xe47e8c: stur            x1, [fp, #-0x10]
    //     0xe47e90: stur            x2, [fp, #-0x18]
    //     0xe47e94: stur            d0, [fp, #-0x38]
    //     0xe47e98: stur            d1, [fp, #-0x40]
    //     0xe47e9c: ldur            w0, [x4, #0x13]
    //     0xe47ea0: sub             x1, x0, #8
    //     0xe47ea4: cmp             w1, #2
    //     0xe47ea8: b.lt            #0xe47ec8
    //     0xe47eac: add             x0, fp, w1, sxtw #2
    //     0xe47eb0: ldr             x0, [x0, #8]
    //     0xe47eb4: cmp             w1, #4
    //     0xe47eb8: b.lt            #0xe47ecc
    //     0xe47ebc: add             x4, fp, w1, sxtw #2
    //     0xe47ec0: ldr             x4, [x4]
    //     0xe47ec4: b               #0xe47ed0
    //     0xe47ec8: mov             x0, NULL
    //     0xe47ecc: mov             x4, NULL
    //     0xe47ed0: stur            x4, [fp, #-8]
    // 0xe47ed4: CheckStackOverflow
    //     0xe47ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47ed8: cmp             SP, x16
    //     0xe47edc: b.ls            #0xe48b60
    // 0xe47ee0: cmp             w0, NULL
    // 0xe47ee4: b.ne            #0xe47f34
    // 0xe47ee8: LoadField: r0 = r2->field_43
    //     0xe47ee8: ldur            w0, [x2, #0x43]
    // 0xe47eec: DecompressPointer r0
    //     0xe47eec: add             x0, x0, HEAP, lsl #32
    // 0xe47ef0: LoadField: r1 = r0->field_7
    //     0xe47ef0: ldur            x1, [x0, #7]
    // 0xe47ef4: cmp             x1, #4
    // 0xe47ef8: b.lt            #0xe47f08
    // 0xe47efc: LoadField: r0 = r2->field_3b
    //     0xe47efc: ldur            x0, [x2, #0x3b]
    // 0xe47f00: mov             x5, x0
    // 0xe47f04: b               #0xe47f10
    // 0xe47f08: LoadField: r0 = r2->field_33
    //     0xe47f08: ldur            x0, [x2, #0x33]
    // 0xe47f0c: mov             x5, x0
    // 0xe47f10: r0 = BoxInt64Instr(r5)
    //     0xe47f10: sbfiz           x0, x5, #1, #0x1f
    //     0xe47f14: cmp             x5, x0, asr #1
    //     0xe47f18: b.eq            #0xe47f24
    //     0xe47f1c: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe47f20: stur            x5, [x0, #7]
    // 0xe47f24: stp             x0, NULL, [SP]
    // 0xe47f28: r0 = _Double.fromInteger()
    //     0xe47f28: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe47f2c: LoadField: d0 = r0->field_7
    //     0xe47f2c: ldur            d0, [x0, #7]
    // 0xe47f30: b               #0xe47f38
    // 0xe47f34: LoadField: d0 = r0->field_7
    //     0xe47f34: ldur            d0, [x0, #7]
    // 0xe47f38: ldur            x0, [fp, #-8]
    // 0xe47f3c: stur            d0, [fp, #-0x48]
    // 0xe47f40: cmp             w0, NULL
    // 0xe47f44: b.ne            #0xe47ffc
    // 0xe47f48: ldur            x2, [fp, #-0x18]
    // 0xe47f4c: LoadField: r0 = r2->field_43
    //     0xe47f4c: ldur            w0, [x2, #0x43]
    // 0xe47f50: DecompressPointer r0
    //     0xe47f50: add             x0, x0, HEAP, lsl #32
    // 0xe47f54: LoadField: r3 = r0->field_7
    //     0xe47f54: ldur            x3, [x0, #7]
    // 0xe47f58: stur            x3, [fp, #-0x20]
    // 0xe47f5c: cmp             x3, #4
    // 0xe47f60: b.ge            #0xe47f70
    // 0xe47f64: LoadField: r0 = r2->field_3b
    //     0xe47f64: ldur            x0, [x2, #0x3b]
    // 0xe47f68: mov             x4, x0
    // 0xe47f6c: b               #0xe47f78
    // 0xe47f70: LoadField: r0 = r2->field_33
    //     0xe47f70: ldur            x0, [x2, #0x33]
    // 0xe47f74: mov             x4, x0
    // 0xe47f78: r0 = BoxInt64Instr(r4)
    //     0xe47f78: sbfiz           x0, x4, #1, #0x1f
    //     0xe47f7c: cmp             x4, x0, asr #1
    //     0xe47f80: b.eq            #0xe47f8c
    //     0xe47f84: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe47f88: stur            x4, [x0, #7]
    // 0xe47f8c: stp             x0, NULL, [SP]
    // 0xe47f90: r0 = _Double.fromInteger()
    //     0xe47f90: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe47f94: LoadField: d0 = r0->field_7
    //     0xe47f94: ldur            d0, [x0, #7]
    // 0xe47f98: ldur            d1, [fp, #-0x48]
    // 0xe47f9c: fmul            d2, d0, d1
    // 0xe47fa0: ldur            x0, [fp, #-0x20]
    // 0xe47fa4: stur            d2, [fp, #-0x50]
    // 0xe47fa8: cmp             x0, #4
    // 0xe47fac: b.lt            #0xe47fc0
    // 0xe47fb0: ldur            x2, [fp, #-0x18]
    // 0xe47fb4: LoadField: r0 = r2->field_3b
    //     0xe47fb4: ldur            x0, [x2, #0x3b]
    // 0xe47fb8: mov             x3, x0
    // 0xe47fbc: b               #0xe47fcc
    // 0xe47fc0: ldur            x2, [fp, #-0x18]
    // 0xe47fc4: LoadField: r0 = r2->field_33
    //     0xe47fc4: ldur            x0, [x2, #0x33]
    // 0xe47fc8: mov             x3, x0
    // 0xe47fcc: r0 = BoxInt64Instr(r3)
    //     0xe47fcc: sbfiz           x0, x3, #1, #0x1f
    //     0xe47fd0: cmp             x3, x0, asr #1
    //     0xe47fd4: b.eq            #0xe47fe0
    //     0xe47fd8: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe47fdc: stur            x3, [x0, #7]
    // 0xe47fe0: stp             x0, NULL, [SP]
    // 0xe47fe4: r0 = _Double.fromInteger()
    //     0xe47fe4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe47fe8: LoadField: d0 = r0->field_7
    //     0xe47fe8: ldur            d0, [x0, #7]
    // 0xe47fec: ldur            d1, [fp, #-0x50]
    // 0xe47ff0: fdiv            d2, d1, d0
    // 0xe47ff4: mov             v0.16b, v2.16b
    // 0xe47ff8: b               #0xe48000
    // 0xe47ffc: LoadField: d0 = r0->field_7
    //     0xe47ffc: ldur            d0, [x0, #7]
    // 0xe48000: ldur            x4, [fp, #-0x10]
    // 0xe48004: ldur            x3, [fp, #-0x18]
    // 0xe48008: stur            d0, [fp, #-0x50]
    // 0xe4800c: LoadField: r5 = r4->field_f
    //     0xe4800c: ldur            w5, [x4, #0xf]
    // 0xe48010: DecompressPointer r5
    //     0xe48010: add             x5, x5, HEAP, lsl #32
    // 0xe48014: stur            x5, [fp, #-8]
    // 0xe48018: r0 = LoadClassIdInstr(r5)
    //     0xe48018: ldur            x0, [x5, #-1]
    //     0xe4801c: ubfx            x0, x0, #0xc, #0x14
    // 0xe48020: mov             x1, x5
    // 0xe48024: mov             x2, x3
    // 0xe48028: r0 = GDT[cid_x0 + -0xffd]()
    //     0xe48028: sub             lr, x0, #0xffd
    //     0xe4802c: ldr             lr, [x21, lr, lsl #3]
    //     0xe48030: blr             lr
    // 0xe48034: ldur            x0, [fp, #-0x10]
    // 0xe48038: LoadField: r3 = r0->field_13
    //     0xe48038: ldur            w3, [x0, #0x13]
    // 0xe4803c: DecompressPointer r3
    //     0xe4803c: add             x3, x3, HEAP, lsl #32
    // 0xe48040: mov             x1, x3
    // 0xe48044: stur            x3, [fp, #-0x28]
    // 0xe48048: r2 = "q "
    //     0xe48048: add             x2, PP, #0x36, lsl #12  ; [pp+0x36728] "q "
    //     0xe4804c: ldr             x2, [x2, #0x728]
    // 0xe48050: r0 = putString()
    //     0xe48050: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe48054: ldur            x0, [fp, #-0x18]
    // 0xe48058: LoadField: r1 = r0->field_43
    //     0xe48058: ldur            w1, [x0, #0x43]
    // 0xe4805c: DecompressPointer r1
    //     0xe4805c: add             x1, x1, HEAP, lsl #32
    // 0xe48060: LoadField: r2 = r1->field_7
    //     0xe48060: ldur            x2, [x1, #7]
    // 0xe48064: cmp             x2, #3
    // 0xe48068: b.gt            #0xe48598
    // 0xe4806c: cmp             x2, #1
    // 0xe48070: b.gt            #0xe48300
    // 0xe48074: cmp             x2, #0
    // 0xe48078: b.gt            #0xe481b8
    // 0xe4807c: ldur            d3, [fp, #-0x38]
    // 0xe48080: ldur            d2, [fp, #-0x40]
    // 0xe48084: ldur            d1, [fp, #-0x48]
    // 0xe48088: ldur            d0, [fp, #-0x50]
    // 0xe4808c: r3 = 12
    //     0xe4808c: movz            x3, #0xc
    // 0xe48090: r4 = inline_Allocate_Double()
    //     0xe48090: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xe48094: add             x4, x4, #0x10
    //     0xe48098: cmp             x1, x4
    //     0xe4809c: b.ls            #0xe48b68
    //     0xe480a0: str             x4, [THR, #0x50]  ; THR::top
    //     0xe480a4: sub             x4, x4, #0xf
    //     0xe480a8: movz            x1, #0xe15c
    //     0xe480ac: movk            x1, #0x3, lsl #16
    //     0xe480b0: stur            x1, [x4, #-1]
    // 0xe480b4: StoreField: r4->field_7 = d1
    //     0xe480b4: stur            d1, [x4, #7]
    // 0xe480b8: mov             x2, x3
    // 0xe480bc: stur            x4, [fp, #-0x10]
    // 0xe480c0: r1 = Null
    //     0xe480c0: mov             x1, NULL
    // 0xe480c4: r0 = AllocateArray()
    //     0xe480c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe480c8: mov             x2, x0
    // 0xe480cc: ldur            x0, [fp, #-0x10]
    // 0xe480d0: stur            x2, [fp, #-0x30]
    // 0xe480d4: StoreField: r2->field_f = r0
    //     0xe480d4: stur            w0, [x2, #0xf]
    // 0xe480d8: r16 = 0.000000
    //     0xe480d8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe480dc: StoreField: r2->field_13 = r16
    //     0xe480dc: stur            w16, [x2, #0x13]
    // 0xe480e0: r16 = 0.000000
    //     0xe480e0: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe480e4: ArrayStore: r2[0] = r16  ; List_4
    //     0xe480e4: stur            w16, [x2, #0x17]
    // 0xe480e8: ldur            d0, [fp, #-0x50]
    // 0xe480ec: r0 = inline_Allocate_Double()
    //     0xe480ec: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe480f0: add             x0, x0, #0x10
    //     0xe480f4: cmp             x1, x0
    //     0xe480f8: b.ls            #0xe48b8c
    //     0xe480fc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48100: sub             x0, x0, #0xf
    //     0xe48104: movz            x1, #0xe15c
    //     0xe48108: movk            x1, #0x3, lsl #16
    //     0xe4810c: stur            x1, [x0, #-1]
    // 0xe48110: StoreField: r0->field_7 = d0
    //     0xe48110: stur            d0, [x0, #7]
    // 0xe48114: StoreField: r2->field_1b = r0
    //     0xe48114: stur            w0, [x2, #0x1b]
    // 0xe48118: ldur            d2, [fp, #-0x38]
    // 0xe4811c: r0 = inline_Allocate_Double()
    //     0xe4811c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48120: add             x0, x0, #0x10
    //     0xe48124: cmp             x1, x0
    //     0xe48128: b.ls            #0xe48ba4
    //     0xe4812c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48130: sub             x0, x0, #0xf
    //     0xe48134: movz            x1, #0xe15c
    //     0xe48138: movk            x1, #0x3, lsl #16
    //     0xe4813c: stur            x1, [x0, #-1]
    // 0xe48140: StoreField: r0->field_7 = d2
    //     0xe48140: stur            d2, [x0, #7]
    // 0xe48144: StoreField: r2->field_1f = r0
    //     0xe48144: stur            w0, [x2, #0x1f]
    // 0xe48148: ldur            d3, [fp, #-0x40]
    // 0xe4814c: r0 = inline_Allocate_Double()
    //     0xe4814c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48150: add             x0, x0, #0x10
    //     0xe48154: cmp             x1, x0
    //     0xe48158: b.ls            #0xe48bbc
    //     0xe4815c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48160: sub             x0, x0, #0xf
    //     0xe48164: movz            x1, #0xe15c
    //     0xe48168: movk            x1, #0x3, lsl #16
    //     0xe4816c: stur            x1, [x0, #-1]
    // 0xe48170: StoreField: r0->field_7 = d3
    //     0xe48170: stur            d3, [x0, #7]
    // 0xe48174: StoreField: r2->field_23 = r0
    //     0xe48174: stur            w0, [x2, #0x23]
    // 0xe48178: r1 = <double>
    //     0xe48178: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe4817c: r0 = AllocateGrowableArray()
    //     0xe4817c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe48180: mov             x1, x0
    // 0xe48184: ldur            x0, [fp, #-0x30]
    // 0xe48188: stur            x1, [fp, #-0x10]
    // 0xe4818c: StoreField: r1->field_f = r0
    //     0xe4818c: stur            w0, [x1, #0xf]
    // 0xe48190: r0 = 12
    //     0xe48190: movz            x0, #0xc
    // 0xe48194: StoreField: r1->field_b = r0
    //     0xe48194: stur            w0, [x1, #0xb]
    // 0xe48198: r0 = PdfNumList()
    //     0xe48198: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe4819c: mov             x1, x0
    // 0xe481a0: ldur            x0, [fp, #-0x10]
    // 0xe481a4: StoreField: r1->field_7 = r0
    //     0xe481a4: stur            w0, [x1, #7]
    // 0xe481a8: ldur            x2, [fp, #-8]
    // 0xe481ac: ldur            x3, [fp, #-0x28]
    // 0xe481b0: r0 = output()
    //     0xe481b0: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe481b4: b               #0xe48a80
    // 0xe481b8: ldur            d2, [fp, #-0x38]
    // 0xe481bc: ldur            d3, [fp, #-0x40]
    // 0xe481c0: ldur            d1, [fp, #-0x48]
    // 0xe481c4: ldur            d0, [fp, #-0x50]
    // 0xe481c8: r0 = 12
    //     0xe481c8: movz            x0, #0xc
    // 0xe481cc: fneg            d4, d1
    // 0xe481d0: fadd            d5, d1, d2
    // 0xe481d4: stur            d5, [fp, #-0x58]
    // 0xe481d8: r3 = inline_Allocate_Double()
    //     0xe481d8: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xe481dc: add             x3, x3, #0x10
    //     0xe481e0: cmp             x1, x3
    //     0xe481e4: b.ls            #0xe48bd4
    //     0xe481e8: str             x3, [THR, #0x50]  ; THR::top
    //     0xe481ec: sub             x3, x3, #0xf
    //     0xe481f0: movz            x1, #0xe15c
    //     0xe481f4: movk            x1, #0x3, lsl #16
    //     0xe481f8: stur            x1, [x3, #-1]
    // 0xe481fc: StoreField: r3->field_7 = d4
    //     0xe481fc: stur            d4, [x3, #7]
    // 0xe48200: mov             x2, x0
    // 0xe48204: stur            x3, [fp, #-0x10]
    // 0xe48208: r1 = Null
    //     0xe48208: mov             x1, NULL
    // 0xe4820c: r0 = AllocateArray()
    //     0xe4820c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48210: mov             x2, x0
    // 0xe48214: ldur            x0, [fp, #-0x10]
    // 0xe48218: stur            x2, [fp, #-0x30]
    // 0xe4821c: StoreField: r2->field_f = r0
    //     0xe4821c: stur            w0, [x2, #0xf]
    // 0xe48220: r16 = 0.000000
    //     0xe48220: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48224: StoreField: r2->field_13 = r16
    //     0xe48224: stur            w16, [x2, #0x13]
    // 0xe48228: r16 = 0.000000
    //     0xe48228: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe4822c: ArrayStore: r2[0] = r16  ; List_4
    //     0xe4822c: stur            w16, [x2, #0x17]
    // 0xe48230: ldur            d0, [fp, #-0x50]
    // 0xe48234: r0 = inline_Allocate_Double()
    //     0xe48234: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48238: add             x0, x0, #0x10
    //     0xe4823c: cmp             x1, x0
    //     0xe48240: b.ls            #0xe48bf8
    //     0xe48244: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48248: sub             x0, x0, #0xf
    //     0xe4824c: movz            x1, #0xe15c
    //     0xe48250: movk            x1, #0x3, lsl #16
    //     0xe48254: stur            x1, [x0, #-1]
    // 0xe48258: StoreField: r0->field_7 = d0
    //     0xe48258: stur            d0, [x0, #7]
    // 0xe4825c: StoreField: r2->field_1b = r0
    //     0xe4825c: stur            w0, [x2, #0x1b]
    // 0xe48260: ldur            d0, [fp, #-0x58]
    // 0xe48264: r0 = inline_Allocate_Double()
    //     0xe48264: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48268: add             x0, x0, #0x10
    //     0xe4826c: cmp             x1, x0
    //     0xe48270: b.ls            #0xe48c10
    //     0xe48274: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48278: sub             x0, x0, #0xf
    //     0xe4827c: movz            x1, #0xe15c
    //     0xe48280: movk            x1, #0x3, lsl #16
    //     0xe48284: stur            x1, [x0, #-1]
    // 0xe48288: StoreField: r0->field_7 = d0
    //     0xe48288: stur            d0, [x0, #7]
    // 0xe4828c: StoreField: r2->field_1f = r0
    //     0xe4828c: stur            w0, [x2, #0x1f]
    // 0xe48290: ldur            d3, [fp, #-0x40]
    // 0xe48294: r0 = inline_Allocate_Double()
    //     0xe48294: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48298: add             x0, x0, #0x10
    //     0xe4829c: cmp             x1, x0
    //     0xe482a0: b.ls            #0xe48c28
    //     0xe482a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe482a8: sub             x0, x0, #0xf
    //     0xe482ac: movz            x1, #0xe15c
    //     0xe482b0: movk            x1, #0x3, lsl #16
    //     0xe482b4: stur            x1, [x0, #-1]
    // 0xe482b8: StoreField: r0->field_7 = d3
    //     0xe482b8: stur            d3, [x0, #7]
    // 0xe482bc: StoreField: r2->field_23 = r0
    //     0xe482bc: stur            w0, [x2, #0x23]
    // 0xe482c0: r1 = <double>
    //     0xe482c0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe482c4: r0 = AllocateGrowableArray()
    //     0xe482c4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe482c8: mov             x1, x0
    // 0xe482cc: ldur            x0, [fp, #-0x30]
    // 0xe482d0: stur            x1, [fp, #-0x10]
    // 0xe482d4: StoreField: r1->field_f = r0
    //     0xe482d4: stur            w0, [x1, #0xf]
    // 0xe482d8: r0 = 12
    //     0xe482d8: movz            x0, #0xc
    // 0xe482dc: StoreField: r1->field_b = r0
    //     0xe482dc: stur            w0, [x1, #0xb]
    // 0xe482e0: r0 = PdfNumList()
    //     0xe482e0: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe482e4: mov             x1, x0
    // 0xe482e8: ldur            x0, [fp, #-0x10]
    // 0xe482ec: StoreField: r1->field_7 = r0
    //     0xe482ec: stur            w0, [x1, #7]
    // 0xe482f0: ldur            x2, [fp, #-8]
    // 0xe482f4: ldur            x3, [fp, #-0x28]
    // 0xe482f8: r0 = output()
    //     0xe482f8: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe482fc: b               #0xe48a80
    // 0xe48300: ldur            d2, [fp, #-0x38]
    // 0xe48304: ldur            d3, [fp, #-0x40]
    // 0xe48308: ldur            d1, [fp, #-0x48]
    // 0xe4830c: ldur            d0, [fp, #-0x50]
    // 0xe48310: r0 = 12
    //     0xe48310: movz            x0, #0xc
    // 0xe48314: cmp             x2, #2
    // 0xe48318: b.gt            #0xe48460
    // 0xe4831c: fneg            d4, d1
    // 0xe48320: fneg            d5, d0
    // 0xe48324: stur            d5, [fp, #-0x68]
    // 0xe48328: fadd            d6, d1, d2
    // 0xe4832c: stur            d6, [fp, #-0x60]
    // 0xe48330: fadd            d1, d0, d3
    // 0xe48334: stur            d1, [fp, #-0x58]
    // 0xe48338: r3 = inline_Allocate_Double()
    //     0xe48338: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xe4833c: add             x3, x3, #0x10
    //     0xe48340: cmp             x1, x3
    //     0xe48344: b.ls            #0xe48c40
    //     0xe48348: str             x3, [THR, #0x50]  ; THR::top
    //     0xe4834c: sub             x3, x3, #0xf
    //     0xe48350: movz            x1, #0xe15c
    //     0xe48354: movk            x1, #0x3, lsl #16
    //     0xe48358: stur            x1, [x3, #-1]
    // 0xe4835c: StoreField: r3->field_7 = d4
    //     0xe4835c: stur            d4, [x3, #7]
    // 0xe48360: mov             x2, x0
    // 0xe48364: stur            x3, [fp, #-0x10]
    // 0xe48368: r1 = Null
    //     0xe48368: mov             x1, NULL
    // 0xe4836c: r0 = AllocateArray()
    //     0xe4836c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48370: mov             x2, x0
    // 0xe48374: ldur            x0, [fp, #-0x10]
    // 0xe48378: stur            x2, [fp, #-0x30]
    // 0xe4837c: StoreField: r2->field_f = r0
    //     0xe4837c: stur            w0, [x2, #0xf]
    // 0xe48380: r16 = 0.000000
    //     0xe48380: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48384: StoreField: r2->field_13 = r16
    //     0xe48384: stur            w16, [x2, #0x13]
    // 0xe48388: r16 = 0.000000
    //     0xe48388: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe4838c: ArrayStore: r2[0] = r16  ; List_4
    //     0xe4838c: stur            w16, [x2, #0x17]
    // 0xe48390: ldur            d0, [fp, #-0x68]
    // 0xe48394: r0 = inline_Allocate_Double()
    //     0xe48394: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48398: add             x0, x0, #0x10
    //     0xe4839c: cmp             x1, x0
    //     0xe483a0: b.ls            #0xe48c64
    //     0xe483a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe483a8: sub             x0, x0, #0xf
    //     0xe483ac: movz            x1, #0xe15c
    //     0xe483b0: movk            x1, #0x3, lsl #16
    //     0xe483b4: stur            x1, [x0, #-1]
    // 0xe483b8: StoreField: r0->field_7 = d0
    //     0xe483b8: stur            d0, [x0, #7]
    // 0xe483bc: StoreField: r2->field_1b = r0
    //     0xe483bc: stur            w0, [x2, #0x1b]
    // 0xe483c0: ldur            d0, [fp, #-0x60]
    // 0xe483c4: r0 = inline_Allocate_Double()
    //     0xe483c4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe483c8: add             x0, x0, #0x10
    //     0xe483cc: cmp             x1, x0
    //     0xe483d0: b.ls            #0xe48c7c
    //     0xe483d4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe483d8: sub             x0, x0, #0xf
    //     0xe483dc: movz            x1, #0xe15c
    //     0xe483e0: movk            x1, #0x3, lsl #16
    //     0xe483e4: stur            x1, [x0, #-1]
    // 0xe483e8: StoreField: r0->field_7 = d0
    //     0xe483e8: stur            d0, [x0, #7]
    // 0xe483ec: StoreField: r2->field_1f = r0
    //     0xe483ec: stur            w0, [x2, #0x1f]
    // 0xe483f0: ldur            d0, [fp, #-0x58]
    // 0xe483f4: r0 = inline_Allocate_Double()
    //     0xe483f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe483f8: add             x0, x0, #0x10
    //     0xe483fc: cmp             x1, x0
    //     0xe48400: b.ls            #0xe48c94
    //     0xe48404: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48408: sub             x0, x0, #0xf
    //     0xe4840c: movz            x1, #0xe15c
    //     0xe48410: movk            x1, #0x3, lsl #16
    //     0xe48414: stur            x1, [x0, #-1]
    // 0xe48418: StoreField: r0->field_7 = d0
    //     0xe48418: stur            d0, [x0, #7]
    // 0xe4841c: StoreField: r2->field_23 = r0
    //     0xe4841c: stur            w0, [x2, #0x23]
    // 0xe48420: r1 = <double>
    //     0xe48420: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe48424: r0 = AllocateGrowableArray()
    //     0xe48424: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe48428: mov             x1, x0
    // 0xe4842c: ldur            x0, [fp, #-0x30]
    // 0xe48430: stur            x1, [fp, #-0x10]
    // 0xe48434: StoreField: r1->field_f = r0
    //     0xe48434: stur            w0, [x1, #0xf]
    // 0xe48438: r0 = 12
    //     0xe48438: movz            x0, #0xc
    // 0xe4843c: StoreField: r1->field_b = r0
    //     0xe4843c: stur            w0, [x1, #0xb]
    // 0xe48440: r0 = PdfNumList()
    //     0xe48440: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe48444: mov             x1, x0
    // 0xe48448: ldur            x0, [fp, #-0x10]
    // 0xe4844c: StoreField: r1->field_7 = r0
    //     0xe4844c: stur            w0, [x1, #7]
    // 0xe48450: ldur            x2, [fp, #-8]
    // 0xe48454: ldur            x3, [fp, #-0x28]
    // 0xe48458: r0 = output()
    //     0xe48458: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe4845c: b               #0xe48a80
    // 0xe48460: fneg            d4, d0
    // 0xe48464: stur            d4, [fp, #-0x60]
    // 0xe48468: fadd            d5, d0, d3
    // 0xe4846c: stur            d5, [fp, #-0x58]
    // 0xe48470: r3 = inline_Allocate_Double()
    //     0xe48470: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xe48474: add             x3, x3, #0x10
    //     0xe48478: cmp             x1, x3
    //     0xe4847c: b.ls            #0xe48cac
    //     0xe48480: str             x3, [THR, #0x50]  ; THR::top
    //     0xe48484: sub             x3, x3, #0xf
    //     0xe48488: movz            x1, #0xe15c
    //     0xe4848c: movk            x1, #0x3, lsl #16
    //     0xe48490: stur            x1, [x3, #-1]
    // 0xe48494: StoreField: r3->field_7 = d1
    //     0xe48494: stur            d1, [x3, #7]
    // 0xe48498: mov             x2, x0
    // 0xe4849c: stur            x3, [fp, #-0x10]
    // 0xe484a0: r1 = Null
    //     0xe484a0: mov             x1, NULL
    // 0xe484a4: r0 = AllocateArray()
    //     0xe484a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe484a8: mov             x2, x0
    // 0xe484ac: ldur            x0, [fp, #-0x10]
    // 0xe484b0: stur            x2, [fp, #-0x30]
    // 0xe484b4: StoreField: r2->field_f = r0
    //     0xe484b4: stur            w0, [x2, #0xf]
    // 0xe484b8: r16 = 0.000000
    //     0xe484b8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe484bc: StoreField: r2->field_13 = r16
    //     0xe484bc: stur            w16, [x2, #0x13]
    // 0xe484c0: r16 = 0.000000
    //     0xe484c0: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe484c4: ArrayStore: r2[0] = r16  ; List_4
    //     0xe484c4: stur            w16, [x2, #0x17]
    // 0xe484c8: ldur            d0, [fp, #-0x60]
    // 0xe484cc: r0 = inline_Allocate_Double()
    //     0xe484cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe484d0: add             x0, x0, #0x10
    //     0xe484d4: cmp             x1, x0
    //     0xe484d8: b.ls            #0xe48cd0
    //     0xe484dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe484e0: sub             x0, x0, #0xf
    //     0xe484e4: movz            x1, #0xe15c
    //     0xe484e8: movk            x1, #0x3, lsl #16
    //     0xe484ec: stur            x1, [x0, #-1]
    // 0xe484f0: StoreField: r0->field_7 = d0
    //     0xe484f0: stur            d0, [x0, #7]
    // 0xe484f4: StoreField: r2->field_1b = r0
    //     0xe484f4: stur            w0, [x2, #0x1b]
    // 0xe484f8: ldur            d2, [fp, #-0x38]
    // 0xe484fc: r0 = inline_Allocate_Double()
    //     0xe484fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48500: add             x0, x0, #0x10
    //     0xe48504: cmp             x1, x0
    //     0xe48508: b.ls            #0xe48ce8
    //     0xe4850c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48510: sub             x0, x0, #0xf
    //     0xe48514: movz            x1, #0xe15c
    //     0xe48518: movk            x1, #0x3, lsl #16
    //     0xe4851c: stur            x1, [x0, #-1]
    // 0xe48520: StoreField: r0->field_7 = d2
    //     0xe48520: stur            d2, [x0, #7]
    // 0xe48524: StoreField: r2->field_1f = r0
    //     0xe48524: stur            w0, [x2, #0x1f]
    // 0xe48528: ldur            d0, [fp, #-0x58]
    // 0xe4852c: r0 = inline_Allocate_Double()
    //     0xe4852c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe48530: add             x0, x0, #0x10
    //     0xe48534: cmp             x1, x0
    //     0xe48538: b.ls            #0xe48d00
    //     0xe4853c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe48540: sub             x0, x0, #0xf
    //     0xe48544: movz            x1, #0xe15c
    //     0xe48548: movk            x1, #0x3, lsl #16
    //     0xe4854c: stur            x1, [x0, #-1]
    // 0xe48550: StoreField: r0->field_7 = d0
    //     0xe48550: stur            d0, [x0, #7]
    // 0xe48554: StoreField: r2->field_23 = r0
    //     0xe48554: stur            w0, [x2, #0x23]
    // 0xe48558: r1 = <double>
    //     0xe48558: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe4855c: r0 = AllocateGrowableArray()
    //     0xe4855c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe48560: mov             x1, x0
    // 0xe48564: ldur            x0, [fp, #-0x30]
    // 0xe48568: stur            x1, [fp, #-0x10]
    // 0xe4856c: StoreField: r1->field_f = r0
    //     0xe4856c: stur            w0, [x1, #0xf]
    // 0xe48570: r0 = 12
    //     0xe48570: movz            x0, #0xc
    // 0xe48574: StoreField: r1->field_b = r0
    //     0xe48574: stur            w0, [x1, #0xb]
    // 0xe48578: r0 = PdfNumList()
    //     0xe48578: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe4857c: mov             x1, x0
    // 0xe48580: ldur            x0, [fp, #-0x10]
    // 0xe48584: StoreField: r1->field_7 = r0
    //     0xe48584: stur            w0, [x1, #7]
    // 0xe48588: ldur            x2, [fp, #-8]
    // 0xe4858c: ldur            x3, [fp, #-0x28]
    // 0xe48590: r0 = output()
    //     0xe48590: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe48594: b               #0xe48a80
    // 0xe48598: ldur            d2, [fp, #-0x38]
    // 0xe4859c: ldur            d3, [fp, #-0x40]
    // 0xe485a0: ldur            d1, [fp, #-0x48]
    // 0xe485a4: ldur            d0, [fp, #-0x50]
    // 0xe485a8: r0 = 12
    //     0xe485a8: movz            x0, #0xc
    // 0xe485ac: cmp             x2, #5
    // 0xe485b0: b.gt            #0xe4882c
    // 0xe485b4: cmp             x2, #4
    // 0xe485b8: b.gt            #0xe486fc
    // 0xe485bc: fneg            d4, d0
    // 0xe485c0: stur            d4, [fp, #-0x70]
    // 0xe485c4: fneg            d5, d1
    // 0xe485c8: stur            d5, [fp, #-0x68]
    // 0xe485cc: fadd            d6, d1, d2
    // 0xe485d0: stur            d6, [fp, #-0x60]
    // 0xe485d4: fadd            d1, d0, d3
    // 0xe485d8: mov             x2, x0
    // 0xe485dc: stur            d1, [fp, #-0x58]
    // 0xe485e0: r1 = Null
    //     0xe485e0: mov             x1, NULL
    // 0xe485e4: r0 = AllocateArray()
    //     0xe485e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe485e8: stur            x0, [fp, #-0x10]
    // 0xe485ec: r16 = 0.000000
    //     0xe485ec: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe485f0: StoreField: r0->field_f = r16
    //     0xe485f0: stur            w16, [x0, #0xf]
    // 0xe485f4: ldur            d0, [fp, #-0x70]
    // 0xe485f8: r1 = inline_Allocate_Double()
    //     0xe485f8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe485fc: add             x1, x1, #0x10
    //     0xe48600: cmp             x2, x1
    //     0xe48604: b.ls            #0xe48d18
    //     0xe48608: str             x1, [THR, #0x50]  ; THR::top
    //     0xe4860c: sub             x1, x1, #0xf
    //     0xe48610: movz            x2, #0xe15c
    //     0xe48614: movk            x2, #0x3, lsl #16
    //     0xe48618: stur            x2, [x1, #-1]
    // 0xe4861c: StoreField: r1->field_7 = d0
    //     0xe4861c: stur            d0, [x1, #7]
    // 0xe48620: StoreField: r0->field_13 = r1
    //     0xe48620: stur            w1, [x0, #0x13]
    // 0xe48624: ldur            d0, [fp, #-0x68]
    // 0xe48628: r1 = inline_Allocate_Double()
    //     0xe48628: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe4862c: add             x1, x1, #0x10
    //     0xe48630: cmp             x2, x1
    //     0xe48634: b.ls            #0xe48d34
    //     0xe48638: str             x1, [THR, #0x50]  ; THR::top
    //     0xe4863c: sub             x1, x1, #0xf
    //     0xe48640: movz            x2, #0xe15c
    //     0xe48644: movk            x2, #0x3, lsl #16
    //     0xe48648: stur            x2, [x1, #-1]
    // 0xe4864c: StoreField: r1->field_7 = d0
    //     0xe4864c: stur            d0, [x1, #7]
    // 0xe48650: ArrayStore: r0[0] = r1  ; List_4
    //     0xe48650: stur            w1, [x0, #0x17]
    // 0xe48654: r16 = 0.000000
    //     0xe48654: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48658: StoreField: r0->field_1b = r16
    //     0xe48658: stur            w16, [x0, #0x1b]
    // 0xe4865c: ldur            d0, [fp, #-0x60]
    // 0xe48660: r1 = inline_Allocate_Double()
    //     0xe48660: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48664: add             x1, x1, #0x10
    //     0xe48668: cmp             x2, x1
    //     0xe4866c: b.ls            #0xe48d50
    //     0xe48670: str             x1, [THR, #0x50]  ; THR::top
    //     0xe48674: sub             x1, x1, #0xf
    //     0xe48678: movz            x2, #0xe15c
    //     0xe4867c: movk            x2, #0x3, lsl #16
    //     0xe48680: stur            x2, [x1, #-1]
    // 0xe48684: StoreField: r1->field_7 = d0
    //     0xe48684: stur            d0, [x1, #7]
    // 0xe48688: StoreField: r0->field_1f = r1
    //     0xe48688: stur            w1, [x0, #0x1f]
    // 0xe4868c: ldur            d0, [fp, #-0x58]
    // 0xe48690: r1 = inline_Allocate_Double()
    //     0xe48690: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48694: add             x1, x1, #0x10
    //     0xe48698: cmp             x2, x1
    //     0xe4869c: b.ls            #0xe48d6c
    //     0xe486a0: str             x1, [THR, #0x50]  ; THR::top
    //     0xe486a4: sub             x1, x1, #0xf
    //     0xe486a8: movz            x2, #0xe15c
    //     0xe486ac: movk            x2, #0x3, lsl #16
    //     0xe486b0: stur            x2, [x1, #-1]
    // 0xe486b4: StoreField: r1->field_7 = d0
    //     0xe486b4: stur            d0, [x1, #7]
    // 0xe486b8: StoreField: r0->field_23 = r1
    //     0xe486b8: stur            w1, [x0, #0x23]
    // 0xe486bc: r1 = <double>
    //     0xe486bc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe486c0: r0 = AllocateGrowableArray()
    //     0xe486c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe486c4: mov             x1, x0
    // 0xe486c8: ldur            x0, [fp, #-0x10]
    // 0xe486cc: stur            x1, [fp, #-0x30]
    // 0xe486d0: StoreField: r1->field_f = r0
    //     0xe486d0: stur            w0, [x1, #0xf]
    // 0xe486d4: r0 = 12
    //     0xe486d4: movz            x0, #0xc
    // 0xe486d8: StoreField: r1->field_b = r0
    //     0xe486d8: stur            w0, [x1, #0xb]
    // 0xe486dc: r0 = PdfNumList()
    //     0xe486dc: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe486e0: mov             x1, x0
    // 0xe486e4: ldur            x0, [fp, #-0x30]
    // 0xe486e8: StoreField: r1->field_7 = r0
    //     0xe486e8: stur            w0, [x1, #7]
    // 0xe486ec: ldur            x2, [fp, #-8]
    // 0xe486f0: ldur            x3, [fp, #-0x28]
    // 0xe486f4: r0 = output()
    //     0xe486f4: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe486f8: b               #0xe48a80
    // 0xe486fc: fneg            d4, d0
    // 0xe48700: stur            d4, [fp, #-0x60]
    // 0xe48704: fadd            d5, d0, d3
    // 0xe48708: mov             x2, x0
    // 0xe4870c: stur            d5, [fp, #-0x58]
    // 0xe48710: r1 = Null
    //     0xe48710: mov             x1, NULL
    // 0xe48714: r0 = AllocateArray()
    //     0xe48714: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48718: stur            x0, [fp, #-0x10]
    // 0xe4871c: r16 = 0.000000
    //     0xe4871c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48720: StoreField: r0->field_f = r16
    //     0xe48720: stur            w16, [x0, #0xf]
    // 0xe48724: ldur            d0, [fp, #-0x60]
    // 0xe48728: r1 = inline_Allocate_Double()
    //     0xe48728: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe4872c: add             x1, x1, #0x10
    //     0xe48730: cmp             x2, x1
    //     0xe48734: b.ls            #0xe48d88
    //     0xe48738: str             x1, [THR, #0x50]  ; THR::top
    //     0xe4873c: sub             x1, x1, #0xf
    //     0xe48740: movz            x2, #0xe15c
    //     0xe48744: movk            x2, #0x3, lsl #16
    //     0xe48748: stur            x2, [x1, #-1]
    // 0xe4874c: StoreField: r1->field_7 = d0
    //     0xe4874c: stur            d0, [x1, #7]
    // 0xe48750: StoreField: r0->field_13 = r1
    //     0xe48750: stur            w1, [x0, #0x13]
    // 0xe48754: ldur            d1, [fp, #-0x48]
    // 0xe48758: r1 = inline_Allocate_Double()
    //     0xe48758: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe4875c: add             x1, x1, #0x10
    //     0xe48760: cmp             x2, x1
    //     0xe48764: b.ls            #0xe48da4
    //     0xe48768: str             x1, [THR, #0x50]  ; THR::top
    //     0xe4876c: sub             x1, x1, #0xf
    //     0xe48770: movz            x2, #0xe15c
    //     0xe48774: movk            x2, #0x3, lsl #16
    //     0xe48778: stur            x2, [x1, #-1]
    // 0xe4877c: StoreField: r1->field_7 = d1
    //     0xe4877c: stur            d1, [x1, #7]
    // 0xe48780: ArrayStore: r0[0] = r1  ; List_4
    //     0xe48780: stur            w1, [x0, #0x17]
    // 0xe48784: r16 = 0.000000
    //     0xe48784: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48788: StoreField: r0->field_1b = r16
    //     0xe48788: stur            w16, [x0, #0x1b]
    // 0xe4878c: ldur            d2, [fp, #-0x38]
    // 0xe48790: r1 = inline_Allocate_Double()
    //     0xe48790: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48794: add             x1, x1, #0x10
    //     0xe48798: cmp             x2, x1
    //     0xe4879c: b.ls            #0xe48dc0
    //     0xe487a0: str             x1, [THR, #0x50]  ; THR::top
    //     0xe487a4: sub             x1, x1, #0xf
    //     0xe487a8: movz            x2, #0xe15c
    //     0xe487ac: movk            x2, #0x3, lsl #16
    //     0xe487b0: stur            x2, [x1, #-1]
    // 0xe487b4: StoreField: r1->field_7 = d2
    //     0xe487b4: stur            d2, [x1, #7]
    // 0xe487b8: StoreField: r0->field_1f = r1
    //     0xe487b8: stur            w1, [x0, #0x1f]
    // 0xe487bc: ldur            d0, [fp, #-0x58]
    // 0xe487c0: r1 = inline_Allocate_Double()
    //     0xe487c0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe487c4: add             x1, x1, #0x10
    //     0xe487c8: cmp             x2, x1
    //     0xe487cc: b.ls            #0xe48ddc
    //     0xe487d0: str             x1, [THR, #0x50]  ; THR::top
    //     0xe487d4: sub             x1, x1, #0xf
    //     0xe487d8: movz            x2, #0xe15c
    //     0xe487dc: movk            x2, #0x3, lsl #16
    //     0xe487e0: stur            x2, [x1, #-1]
    // 0xe487e4: StoreField: r1->field_7 = d0
    //     0xe487e4: stur            d0, [x1, #7]
    // 0xe487e8: StoreField: r0->field_23 = r1
    //     0xe487e8: stur            w1, [x0, #0x23]
    // 0xe487ec: r1 = <double>
    //     0xe487ec: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe487f0: r0 = AllocateGrowableArray()
    //     0xe487f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe487f4: mov             x1, x0
    // 0xe487f8: ldur            x0, [fp, #-0x10]
    // 0xe487fc: stur            x1, [fp, #-0x30]
    // 0xe48800: StoreField: r1->field_f = r0
    //     0xe48800: stur            w0, [x1, #0xf]
    // 0xe48804: r0 = 12
    //     0xe48804: movz            x0, #0xc
    // 0xe48808: StoreField: r1->field_b = r0
    //     0xe48808: stur            w0, [x1, #0xb]
    // 0xe4880c: r0 = PdfNumList()
    //     0xe4880c: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe48810: mov             x1, x0
    // 0xe48814: ldur            x0, [fp, #-0x30]
    // 0xe48818: StoreField: r1->field_7 = r0
    //     0xe48818: stur            w0, [x1, #7]
    // 0xe4881c: ldur            x2, [fp, #-8]
    // 0xe48820: ldur            x3, [fp, #-0x28]
    // 0xe48824: r0 = output()
    //     0xe48824: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe48828: b               #0xe48a80
    // 0xe4882c: cmp             x2, #6
    // 0xe48830: b.gt            #0xe48954
    // 0xe48834: mov             x2, x0
    // 0xe48838: r1 = Null
    //     0xe48838: mov             x1, NULL
    // 0xe4883c: r0 = AllocateArray()
    //     0xe4883c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48840: stur            x0, [fp, #-0x10]
    // 0xe48844: r16 = 0.000000
    //     0xe48844: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48848: StoreField: r0->field_f = r16
    //     0xe48848: stur            w16, [x0, #0xf]
    // 0xe4884c: ldur            d0, [fp, #-0x50]
    // 0xe48850: r1 = inline_Allocate_Double()
    //     0xe48850: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48854: add             x1, x1, #0x10
    //     0xe48858: cmp             x2, x1
    //     0xe4885c: b.ls            #0xe48df8
    //     0xe48860: str             x1, [THR, #0x50]  ; THR::top
    //     0xe48864: sub             x1, x1, #0xf
    //     0xe48868: movz            x2, #0xe15c
    //     0xe4886c: movk            x2, #0x3, lsl #16
    //     0xe48870: stur            x2, [x1, #-1]
    // 0xe48874: StoreField: r1->field_7 = d0
    //     0xe48874: stur            d0, [x1, #7]
    // 0xe48878: StoreField: r0->field_13 = r1
    //     0xe48878: stur            w1, [x0, #0x13]
    // 0xe4887c: ldur            d1, [fp, #-0x48]
    // 0xe48880: r1 = inline_Allocate_Double()
    //     0xe48880: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48884: add             x1, x1, #0x10
    //     0xe48888: cmp             x2, x1
    //     0xe4888c: b.ls            #0xe48e14
    //     0xe48890: str             x1, [THR, #0x50]  ; THR::top
    //     0xe48894: sub             x1, x1, #0xf
    //     0xe48898: movz            x2, #0xe15c
    //     0xe4889c: movk            x2, #0x3, lsl #16
    //     0xe488a0: stur            x2, [x1, #-1]
    // 0xe488a4: StoreField: r1->field_7 = d1
    //     0xe488a4: stur            d1, [x1, #7]
    // 0xe488a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xe488a8: stur            w1, [x0, #0x17]
    // 0xe488ac: r16 = 0.000000
    //     0xe488ac: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe488b0: StoreField: r0->field_1b = r16
    //     0xe488b0: stur            w16, [x0, #0x1b]
    // 0xe488b4: ldur            d2, [fp, #-0x38]
    // 0xe488b8: r1 = inline_Allocate_Double()
    //     0xe488b8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe488bc: add             x1, x1, #0x10
    //     0xe488c0: cmp             x2, x1
    //     0xe488c4: b.ls            #0xe48e30
    //     0xe488c8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe488cc: sub             x1, x1, #0xf
    //     0xe488d0: movz            x2, #0xe15c
    //     0xe488d4: movk            x2, #0x3, lsl #16
    //     0xe488d8: stur            x2, [x1, #-1]
    // 0xe488dc: StoreField: r1->field_7 = d2
    //     0xe488dc: stur            d2, [x1, #7]
    // 0xe488e0: StoreField: r0->field_1f = r1
    //     0xe488e0: stur            w1, [x0, #0x1f]
    // 0xe488e4: ldur            d3, [fp, #-0x40]
    // 0xe488e8: r1 = inline_Allocate_Double()
    //     0xe488e8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe488ec: add             x1, x1, #0x10
    //     0xe488f0: cmp             x2, x1
    //     0xe488f4: b.ls            #0xe48e4c
    //     0xe488f8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe488fc: sub             x1, x1, #0xf
    //     0xe48900: movz            x2, #0xe15c
    //     0xe48904: movk            x2, #0x3, lsl #16
    //     0xe48908: stur            x2, [x1, #-1]
    // 0xe4890c: StoreField: r1->field_7 = d3
    //     0xe4890c: stur            d3, [x1, #7]
    // 0xe48910: StoreField: r0->field_23 = r1
    //     0xe48910: stur            w1, [x0, #0x23]
    // 0xe48914: r1 = <double>
    //     0xe48914: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe48918: r0 = AllocateGrowableArray()
    //     0xe48918: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe4891c: mov             x1, x0
    // 0xe48920: ldur            x0, [fp, #-0x10]
    // 0xe48924: stur            x1, [fp, #-0x30]
    // 0xe48928: StoreField: r1->field_f = r0
    //     0xe48928: stur            w0, [x1, #0xf]
    // 0xe4892c: r0 = 12
    //     0xe4892c: movz            x0, #0xc
    // 0xe48930: StoreField: r1->field_b = r0
    //     0xe48930: stur            w0, [x1, #0xb]
    // 0xe48934: r0 = PdfNumList()
    //     0xe48934: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe48938: mov             x1, x0
    // 0xe4893c: ldur            x0, [fp, #-0x30]
    // 0xe48940: StoreField: r1->field_7 = r0
    //     0xe48940: stur            w0, [x1, #7]
    // 0xe48944: ldur            x2, [fp, #-8]
    // 0xe48948: ldur            x3, [fp, #-0x28]
    // 0xe4894c: r0 = output()
    //     0xe4894c: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe48950: b               #0xe48a80
    // 0xe48954: fneg            d4, d1
    // 0xe48958: stur            d4, [fp, #-0x60]
    // 0xe4895c: fadd            d5, d1, d2
    // 0xe48960: mov             x2, x0
    // 0xe48964: stur            d5, [fp, #-0x58]
    // 0xe48968: r1 = Null
    //     0xe48968: mov             x1, NULL
    // 0xe4896c: r0 = AllocateArray()
    //     0xe4896c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48970: stur            x0, [fp, #-0x10]
    // 0xe48974: r16 = 0.000000
    //     0xe48974: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe48978: StoreField: r0->field_f = r16
    //     0xe48978: stur            w16, [x0, #0xf]
    // 0xe4897c: ldur            d0, [fp, #-0x50]
    // 0xe48980: r1 = inline_Allocate_Double()
    //     0xe48980: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48984: add             x1, x1, #0x10
    //     0xe48988: cmp             x2, x1
    //     0xe4898c: b.ls            #0xe48e68
    //     0xe48990: str             x1, [THR, #0x50]  ; THR::top
    //     0xe48994: sub             x1, x1, #0xf
    //     0xe48998: movz            x2, #0xe15c
    //     0xe4899c: movk            x2, #0x3, lsl #16
    //     0xe489a0: stur            x2, [x1, #-1]
    // 0xe489a4: StoreField: r1->field_7 = d0
    //     0xe489a4: stur            d0, [x1, #7]
    // 0xe489a8: StoreField: r0->field_13 = r1
    //     0xe489a8: stur            w1, [x0, #0x13]
    // 0xe489ac: ldur            d0, [fp, #-0x60]
    // 0xe489b0: r1 = inline_Allocate_Double()
    //     0xe489b0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe489b4: add             x1, x1, #0x10
    //     0xe489b8: cmp             x2, x1
    //     0xe489bc: b.ls            #0xe48e84
    //     0xe489c0: str             x1, [THR, #0x50]  ; THR::top
    //     0xe489c4: sub             x1, x1, #0xf
    //     0xe489c8: movz            x2, #0xe15c
    //     0xe489cc: movk            x2, #0x3, lsl #16
    //     0xe489d0: stur            x2, [x1, #-1]
    // 0xe489d4: StoreField: r1->field_7 = d0
    //     0xe489d4: stur            d0, [x1, #7]
    // 0xe489d8: ArrayStore: r0[0] = r1  ; List_4
    //     0xe489d8: stur            w1, [x0, #0x17]
    // 0xe489dc: r16 = 0.000000
    //     0xe489dc: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe489e0: StoreField: r0->field_1b = r16
    //     0xe489e0: stur            w16, [x0, #0x1b]
    // 0xe489e4: ldur            d0, [fp, #-0x58]
    // 0xe489e8: r1 = inline_Allocate_Double()
    //     0xe489e8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe489ec: add             x1, x1, #0x10
    //     0xe489f0: cmp             x2, x1
    //     0xe489f4: b.ls            #0xe48ea0
    //     0xe489f8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe489fc: sub             x1, x1, #0xf
    //     0xe48a00: movz            x2, #0xe15c
    //     0xe48a04: movk            x2, #0x3, lsl #16
    //     0xe48a08: stur            x2, [x1, #-1]
    // 0xe48a0c: StoreField: r1->field_7 = d0
    //     0xe48a0c: stur            d0, [x1, #7]
    // 0xe48a10: StoreField: r0->field_1f = r1
    //     0xe48a10: stur            w1, [x0, #0x1f]
    // 0xe48a14: ldur            d0, [fp, #-0x40]
    // 0xe48a18: r1 = inline_Allocate_Double()
    //     0xe48a18: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe48a1c: add             x1, x1, #0x10
    //     0xe48a20: cmp             x2, x1
    //     0xe48a24: b.ls            #0xe48ebc
    //     0xe48a28: str             x1, [THR, #0x50]  ; THR::top
    //     0xe48a2c: sub             x1, x1, #0xf
    //     0xe48a30: movz            x2, #0xe15c
    //     0xe48a34: movk            x2, #0x3, lsl #16
    //     0xe48a38: stur            x2, [x1, #-1]
    // 0xe48a3c: StoreField: r1->field_7 = d0
    //     0xe48a3c: stur            d0, [x1, #7]
    // 0xe48a40: StoreField: r0->field_23 = r1
    //     0xe48a40: stur            w1, [x0, #0x23]
    // 0xe48a44: r1 = <double>
    //     0xe48a44: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe48a48: r0 = AllocateGrowableArray()
    //     0xe48a48: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe48a4c: mov             x1, x0
    // 0xe48a50: ldur            x0, [fp, #-0x10]
    // 0xe48a54: stur            x1, [fp, #-0x30]
    // 0xe48a58: StoreField: r1->field_f = r0
    //     0xe48a58: stur            w0, [x1, #0xf]
    // 0xe48a5c: r0 = 12
    //     0xe48a5c: movz            x0, #0xc
    // 0xe48a60: StoreField: r1->field_b = r0
    //     0xe48a60: stur            w0, [x1, #0xb]
    // 0xe48a64: r0 = PdfNumList()
    //     0xe48a64: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe48a68: mov             x1, x0
    // 0xe48a6c: ldur            x0, [fp, #-0x30]
    // 0xe48a70: StoreField: r1->field_7 = r0
    //     0xe48a70: stur            w0, [x1, #7]
    // 0xe48a74: ldur            x2, [fp, #-8]
    // 0xe48a78: ldur            x3, [fp, #-0x28]
    // 0xe48a7c: r0 = output()
    //     0xe48a7c: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe48a80: ldur            x0, [fp, #-0x18]
    // 0xe48a84: ldur            x3, [fp, #-8]
    // 0xe48a88: r1 = Null
    //     0xe48a88: mov             x1, NULL
    // 0xe48a8c: r2 = 6
    //     0xe48a8c: movz            x2, #0x6
    // 0xe48a90: r0 = AllocateArray()
    //     0xe48a90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48a94: stur            x0, [fp, #-0x10]
    // 0xe48a98: r16 = " cm "
    //     0xe48a98: add             x16, PP, #0x36, lsl #12  ; [pp+0x36718] " cm "
    //     0xe48a9c: ldr             x16, [x16, #0x718]
    // 0xe48aa0: StoreField: r0->field_f = r16
    //     0xe48aa0: stur            w16, [x0, #0xf]
    // 0xe48aa4: r1 = Null
    //     0xe48aa4: mov             x1, NULL
    // 0xe48aa8: r2 = 4
    //     0xe48aa8: movz            x2, #0x4
    // 0xe48aac: r0 = AllocateArray()
    //     0xe48aac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48ab0: mov             x2, x0
    // 0xe48ab4: r16 = "/I"
    //     0xe48ab4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xe48ab8: ldr             x16, [x16, #0xbe0]
    // 0xe48abc: StoreField: r2->field_f = r16
    //     0xe48abc: stur            w16, [x2, #0xf]
    // 0xe48ac0: ldur            x0, [fp, #-0x18]
    // 0xe48ac4: LoadField: r3 = r0->field_b
    //     0xe48ac4: ldur            x3, [x0, #0xb]
    // 0xe48ac8: r0 = BoxInt64Instr(r3)
    //     0xe48ac8: sbfiz           x0, x3, #1, #0x1f
    //     0xe48acc: cmp             x3, x0, asr #1
    //     0xe48ad0: b.eq            #0xe48adc
    //     0xe48ad4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe48ad8: stur            x3, [x0, #7]
    // 0xe48adc: StoreField: r2->field_13 = r0
    //     0xe48adc: stur            w0, [x2, #0x13]
    // 0xe48ae0: str             x2, [SP]
    // 0xe48ae4: r0 = _interpolate()
    //     0xe48ae4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe48ae8: ldur            x1, [fp, #-0x10]
    // 0xe48aec: ArrayStore: r1[1] = r0  ; List_4
    //     0xe48aec: add             x25, x1, #0x13
    //     0xe48af0: str             w0, [x25]
    //     0xe48af4: tbz             w0, #0, #0xe48b10
    //     0xe48af8: ldurb           w16, [x1, #-1]
    //     0xe48afc: ldurb           w17, [x0, #-1]
    //     0xe48b00: and             x16, x17, x16, lsr #2
    //     0xe48b04: tst             x16, HEAP, lsr #32
    //     0xe48b08: b.eq            #0xe48b10
    //     0xe48b0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe48b10: ldur            x0, [fp, #-0x10]
    // 0xe48b14: r16 = " Do Q "
    //     0xe48b14: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e20] " Do Q "
    //     0xe48b18: ldr             x16, [x16, #0xe20]
    // 0xe48b1c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe48b1c: stur            w16, [x0, #0x17]
    // 0xe48b20: str             x0, [SP]
    // 0xe48b24: r0 = _interpolate()
    //     0xe48b24: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe48b28: ldur            x1, [fp, #-0x28]
    // 0xe48b2c: mov             x2, x0
    // 0xe48b30: r0 = putString()
    //     0xe48b30: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe48b34: ldur            x1, [fp, #-8]
    // 0xe48b38: r0 = LoadClassIdInstr(r1)
    //     0xe48b38: ldur            x0, [x1, #-1]
    //     0xe48b3c: ubfx            x0, x0, #0xc, #0x14
    // 0xe48b40: r2 = true
    //     0xe48b40: add             x2, NULL, #0x20  ; true
    // 0xe48b44: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe48b44: sub             lr, x0, #1, lsl #12
    //     0xe48b48: ldr             lr, [x21, lr, lsl #3]
    //     0xe48b4c: blr             lr
    // 0xe48b50: r0 = Null
    //     0xe48b50: mov             x0, NULL
    // 0xe48b54: LeaveFrame
    //     0xe48b54: mov             SP, fp
    //     0xe48b58: ldp             fp, lr, [SP], #0x10
    // 0xe48b5c: ret
    //     0xe48b5c: ret             
    // 0xe48b60: r0 = StackOverflowSharedWithFPURegs()
    //     0xe48b60: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe48b64: b               #0xe47ee0
    // 0xe48b68: stp             q2, q3, [SP, #-0x20]!
    // 0xe48b6c: stp             q0, q1, [SP, #-0x20]!
    // 0xe48b70: stp             x0, x3, [SP, #-0x10]!
    // 0xe48b74: r0 = AllocateDouble()
    //     0xe48b74: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48b78: mov             x4, x0
    // 0xe48b7c: ldp             x0, x3, [SP], #0x10
    // 0xe48b80: ldp             q0, q1, [SP], #0x20
    // 0xe48b84: ldp             q2, q3, [SP], #0x20
    // 0xe48b88: b               #0xe480b4
    // 0xe48b8c: SaveReg d0
    //     0xe48b8c: str             q0, [SP, #-0x10]!
    // 0xe48b90: SaveReg r2
    //     0xe48b90: str             x2, [SP, #-8]!
    // 0xe48b94: r0 = AllocateDouble()
    //     0xe48b94: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48b98: RestoreReg r2
    //     0xe48b98: ldr             x2, [SP], #8
    // 0xe48b9c: RestoreReg d0
    //     0xe48b9c: ldr             q0, [SP], #0x10
    // 0xe48ba0: b               #0xe48110
    // 0xe48ba4: SaveReg d2
    //     0xe48ba4: str             q2, [SP, #-0x10]!
    // 0xe48ba8: SaveReg r2
    //     0xe48ba8: str             x2, [SP, #-8]!
    // 0xe48bac: r0 = AllocateDouble()
    //     0xe48bac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48bb0: RestoreReg r2
    //     0xe48bb0: ldr             x2, [SP], #8
    // 0xe48bb4: RestoreReg d2
    //     0xe48bb4: ldr             q2, [SP], #0x10
    // 0xe48bb8: b               #0xe48140
    // 0xe48bbc: SaveReg d3
    //     0xe48bbc: str             q3, [SP, #-0x10]!
    // 0xe48bc0: SaveReg r2
    //     0xe48bc0: str             x2, [SP, #-8]!
    // 0xe48bc4: r0 = AllocateDouble()
    //     0xe48bc4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48bc8: RestoreReg r2
    //     0xe48bc8: ldr             x2, [SP], #8
    // 0xe48bcc: RestoreReg d3
    //     0xe48bcc: ldr             q3, [SP], #0x10
    // 0xe48bd0: b               #0xe48170
    // 0xe48bd4: stp             q4, q5, [SP, #-0x20]!
    // 0xe48bd8: stp             q0, q3, [SP, #-0x20]!
    // 0xe48bdc: SaveReg r0
    //     0xe48bdc: str             x0, [SP, #-8]!
    // 0xe48be0: r0 = AllocateDouble()
    //     0xe48be0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48be4: mov             x3, x0
    // 0xe48be8: RestoreReg r0
    //     0xe48be8: ldr             x0, [SP], #8
    // 0xe48bec: ldp             q0, q3, [SP], #0x20
    // 0xe48bf0: ldp             q4, q5, [SP], #0x20
    // 0xe48bf4: b               #0xe481fc
    // 0xe48bf8: SaveReg d0
    //     0xe48bf8: str             q0, [SP, #-0x10]!
    // 0xe48bfc: SaveReg r2
    //     0xe48bfc: str             x2, [SP, #-8]!
    // 0xe48c00: r0 = AllocateDouble()
    //     0xe48c00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c04: RestoreReg r2
    //     0xe48c04: ldr             x2, [SP], #8
    // 0xe48c08: RestoreReg d0
    //     0xe48c08: ldr             q0, [SP], #0x10
    // 0xe48c0c: b               #0xe48258
    // 0xe48c10: SaveReg d0
    //     0xe48c10: str             q0, [SP, #-0x10]!
    // 0xe48c14: SaveReg r2
    //     0xe48c14: str             x2, [SP, #-8]!
    // 0xe48c18: r0 = AllocateDouble()
    //     0xe48c18: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c1c: RestoreReg r2
    //     0xe48c1c: ldr             x2, [SP], #8
    // 0xe48c20: RestoreReg d0
    //     0xe48c20: ldr             q0, [SP], #0x10
    // 0xe48c24: b               #0xe48288
    // 0xe48c28: SaveReg d3
    //     0xe48c28: str             q3, [SP, #-0x10]!
    // 0xe48c2c: SaveReg r2
    //     0xe48c2c: str             x2, [SP, #-8]!
    // 0xe48c30: r0 = AllocateDouble()
    //     0xe48c30: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c34: RestoreReg r2
    //     0xe48c34: ldr             x2, [SP], #8
    // 0xe48c38: RestoreReg d3
    //     0xe48c38: ldr             q3, [SP], #0x10
    // 0xe48c3c: b               #0xe482b8
    // 0xe48c40: stp             q5, q6, [SP, #-0x20]!
    // 0xe48c44: stp             q1, q4, [SP, #-0x20]!
    // 0xe48c48: SaveReg r0
    //     0xe48c48: str             x0, [SP, #-8]!
    // 0xe48c4c: r0 = AllocateDouble()
    //     0xe48c4c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c50: mov             x3, x0
    // 0xe48c54: RestoreReg r0
    //     0xe48c54: ldr             x0, [SP], #8
    // 0xe48c58: ldp             q1, q4, [SP], #0x20
    // 0xe48c5c: ldp             q5, q6, [SP], #0x20
    // 0xe48c60: b               #0xe4835c
    // 0xe48c64: SaveReg d0
    //     0xe48c64: str             q0, [SP, #-0x10]!
    // 0xe48c68: SaveReg r2
    //     0xe48c68: str             x2, [SP, #-8]!
    // 0xe48c6c: r0 = AllocateDouble()
    //     0xe48c6c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c70: RestoreReg r2
    //     0xe48c70: ldr             x2, [SP], #8
    // 0xe48c74: RestoreReg d0
    //     0xe48c74: ldr             q0, [SP], #0x10
    // 0xe48c78: b               #0xe483b8
    // 0xe48c7c: SaveReg d0
    //     0xe48c7c: str             q0, [SP, #-0x10]!
    // 0xe48c80: SaveReg r2
    //     0xe48c80: str             x2, [SP, #-8]!
    // 0xe48c84: r0 = AllocateDouble()
    //     0xe48c84: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48c88: RestoreReg r2
    //     0xe48c88: ldr             x2, [SP], #8
    // 0xe48c8c: RestoreReg d0
    //     0xe48c8c: ldr             q0, [SP], #0x10
    // 0xe48c90: b               #0xe483e8
    // 0xe48c94: SaveReg d0
    //     0xe48c94: str             q0, [SP, #-0x10]!
    // 0xe48c98: SaveReg r2
    //     0xe48c98: str             x2, [SP, #-8]!
    // 0xe48c9c: r0 = AllocateDouble()
    //     0xe48c9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48ca0: RestoreReg r2
    //     0xe48ca0: ldr             x2, [SP], #8
    // 0xe48ca4: RestoreReg d0
    //     0xe48ca4: ldr             q0, [SP], #0x10
    // 0xe48ca8: b               #0xe48418
    // 0xe48cac: stp             q4, q5, [SP, #-0x20]!
    // 0xe48cb0: stp             q1, q2, [SP, #-0x20]!
    // 0xe48cb4: SaveReg r0
    //     0xe48cb4: str             x0, [SP, #-8]!
    // 0xe48cb8: r0 = AllocateDouble()
    //     0xe48cb8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48cbc: mov             x3, x0
    // 0xe48cc0: RestoreReg r0
    //     0xe48cc0: ldr             x0, [SP], #8
    // 0xe48cc4: ldp             q1, q2, [SP], #0x20
    // 0xe48cc8: ldp             q4, q5, [SP], #0x20
    // 0xe48ccc: b               #0xe48494
    // 0xe48cd0: SaveReg d0
    //     0xe48cd0: str             q0, [SP, #-0x10]!
    // 0xe48cd4: SaveReg r2
    //     0xe48cd4: str             x2, [SP, #-8]!
    // 0xe48cd8: r0 = AllocateDouble()
    //     0xe48cd8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48cdc: RestoreReg r2
    //     0xe48cdc: ldr             x2, [SP], #8
    // 0xe48ce0: RestoreReg d0
    //     0xe48ce0: ldr             q0, [SP], #0x10
    // 0xe48ce4: b               #0xe484f0
    // 0xe48ce8: SaveReg d2
    //     0xe48ce8: str             q2, [SP, #-0x10]!
    // 0xe48cec: SaveReg r2
    //     0xe48cec: str             x2, [SP, #-8]!
    // 0xe48cf0: r0 = AllocateDouble()
    //     0xe48cf0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48cf4: RestoreReg r2
    //     0xe48cf4: ldr             x2, [SP], #8
    // 0xe48cf8: RestoreReg d2
    //     0xe48cf8: ldr             q2, [SP], #0x10
    // 0xe48cfc: b               #0xe48520
    // 0xe48d00: SaveReg d0
    //     0xe48d00: str             q0, [SP, #-0x10]!
    // 0xe48d04: SaveReg r2
    //     0xe48d04: str             x2, [SP, #-8]!
    // 0xe48d08: r0 = AllocateDouble()
    //     0xe48d08: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d0c: RestoreReg r2
    //     0xe48d0c: ldr             x2, [SP], #8
    // 0xe48d10: RestoreReg d0
    //     0xe48d10: ldr             q0, [SP], #0x10
    // 0xe48d14: b               #0xe48550
    // 0xe48d18: SaveReg d0
    //     0xe48d18: str             q0, [SP, #-0x10]!
    // 0xe48d1c: SaveReg r0
    //     0xe48d1c: str             x0, [SP, #-8]!
    // 0xe48d20: r0 = AllocateDouble()
    //     0xe48d20: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d24: mov             x1, x0
    // 0xe48d28: RestoreReg r0
    //     0xe48d28: ldr             x0, [SP], #8
    // 0xe48d2c: RestoreReg d0
    //     0xe48d2c: ldr             q0, [SP], #0x10
    // 0xe48d30: b               #0xe4861c
    // 0xe48d34: SaveReg d0
    //     0xe48d34: str             q0, [SP, #-0x10]!
    // 0xe48d38: SaveReg r0
    //     0xe48d38: str             x0, [SP, #-8]!
    // 0xe48d3c: r0 = AllocateDouble()
    //     0xe48d3c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d40: mov             x1, x0
    // 0xe48d44: RestoreReg r0
    //     0xe48d44: ldr             x0, [SP], #8
    // 0xe48d48: RestoreReg d0
    //     0xe48d48: ldr             q0, [SP], #0x10
    // 0xe48d4c: b               #0xe4864c
    // 0xe48d50: SaveReg d0
    //     0xe48d50: str             q0, [SP, #-0x10]!
    // 0xe48d54: SaveReg r0
    //     0xe48d54: str             x0, [SP, #-8]!
    // 0xe48d58: r0 = AllocateDouble()
    //     0xe48d58: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d5c: mov             x1, x0
    // 0xe48d60: RestoreReg r0
    //     0xe48d60: ldr             x0, [SP], #8
    // 0xe48d64: RestoreReg d0
    //     0xe48d64: ldr             q0, [SP], #0x10
    // 0xe48d68: b               #0xe48684
    // 0xe48d6c: SaveReg d0
    //     0xe48d6c: str             q0, [SP, #-0x10]!
    // 0xe48d70: SaveReg r0
    //     0xe48d70: str             x0, [SP, #-8]!
    // 0xe48d74: r0 = AllocateDouble()
    //     0xe48d74: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d78: mov             x1, x0
    // 0xe48d7c: RestoreReg r0
    //     0xe48d7c: ldr             x0, [SP], #8
    // 0xe48d80: RestoreReg d0
    //     0xe48d80: ldr             q0, [SP], #0x10
    // 0xe48d84: b               #0xe486b4
    // 0xe48d88: SaveReg d0
    //     0xe48d88: str             q0, [SP, #-0x10]!
    // 0xe48d8c: SaveReg r0
    //     0xe48d8c: str             x0, [SP, #-8]!
    // 0xe48d90: r0 = AllocateDouble()
    //     0xe48d90: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48d94: mov             x1, x0
    // 0xe48d98: RestoreReg r0
    //     0xe48d98: ldr             x0, [SP], #8
    // 0xe48d9c: RestoreReg d0
    //     0xe48d9c: ldr             q0, [SP], #0x10
    // 0xe48da0: b               #0xe4874c
    // 0xe48da4: SaveReg d1
    //     0xe48da4: str             q1, [SP, #-0x10]!
    // 0xe48da8: SaveReg r0
    //     0xe48da8: str             x0, [SP, #-8]!
    // 0xe48dac: r0 = AllocateDouble()
    //     0xe48dac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48db0: mov             x1, x0
    // 0xe48db4: RestoreReg r0
    //     0xe48db4: ldr             x0, [SP], #8
    // 0xe48db8: RestoreReg d1
    //     0xe48db8: ldr             q1, [SP], #0x10
    // 0xe48dbc: b               #0xe4877c
    // 0xe48dc0: SaveReg d2
    //     0xe48dc0: str             q2, [SP, #-0x10]!
    // 0xe48dc4: SaveReg r0
    //     0xe48dc4: str             x0, [SP, #-8]!
    // 0xe48dc8: r0 = AllocateDouble()
    //     0xe48dc8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48dcc: mov             x1, x0
    // 0xe48dd0: RestoreReg r0
    //     0xe48dd0: ldr             x0, [SP], #8
    // 0xe48dd4: RestoreReg d2
    //     0xe48dd4: ldr             q2, [SP], #0x10
    // 0xe48dd8: b               #0xe487b4
    // 0xe48ddc: SaveReg d0
    //     0xe48ddc: str             q0, [SP, #-0x10]!
    // 0xe48de0: SaveReg r0
    //     0xe48de0: str             x0, [SP, #-8]!
    // 0xe48de4: r0 = AllocateDouble()
    //     0xe48de4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48de8: mov             x1, x0
    // 0xe48dec: RestoreReg r0
    //     0xe48dec: ldr             x0, [SP], #8
    // 0xe48df0: RestoreReg d0
    //     0xe48df0: ldr             q0, [SP], #0x10
    // 0xe48df4: b               #0xe487e4
    // 0xe48df8: SaveReg d0
    //     0xe48df8: str             q0, [SP, #-0x10]!
    // 0xe48dfc: SaveReg r0
    //     0xe48dfc: str             x0, [SP, #-8]!
    // 0xe48e00: r0 = AllocateDouble()
    //     0xe48e00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e04: mov             x1, x0
    // 0xe48e08: RestoreReg r0
    //     0xe48e08: ldr             x0, [SP], #8
    // 0xe48e0c: RestoreReg d0
    //     0xe48e0c: ldr             q0, [SP], #0x10
    // 0xe48e10: b               #0xe48874
    // 0xe48e14: SaveReg d1
    //     0xe48e14: str             q1, [SP, #-0x10]!
    // 0xe48e18: SaveReg r0
    //     0xe48e18: str             x0, [SP, #-8]!
    // 0xe48e1c: r0 = AllocateDouble()
    //     0xe48e1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e20: mov             x1, x0
    // 0xe48e24: RestoreReg r0
    //     0xe48e24: ldr             x0, [SP], #8
    // 0xe48e28: RestoreReg d1
    //     0xe48e28: ldr             q1, [SP], #0x10
    // 0xe48e2c: b               #0xe488a4
    // 0xe48e30: SaveReg d2
    //     0xe48e30: str             q2, [SP, #-0x10]!
    // 0xe48e34: SaveReg r0
    //     0xe48e34: str             x0, [SP, #-8]!
    // 0xe48e38: r0 = AllocateDouble()
    //     0xe48e38: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e3c: mov             x1, x0
    // 0xe48e40: RestoreReg r0
    //     0xe48e40: ldr             x0, [SP], #8
    // 0xe48e44: RestoreReg d2
    //     0xe48e44: ldr             q2, [SP], #0x10
    // 0xe48e48: b               #0xe488dc
    // 0xe48e4c: SaveReg d3
    //     0xe48e4c: str             q3, [SP, #-0x10]!
    // 0xe48e50: SaveReg r0
    //     0xe48e50: str             x0, [SP, #-8]!
    // 0xe48e54: r0 = AllocateDouble()
    //     0xe48e54: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e58: mov             x1, x0
    // 0xe48e5c: RestoreReg r0
    //     0xe48e5c: ldr             x0, [SP], #8
    // 0xe48e60: RestoreReg d3
    //     0xe48e60: ldr             q3, [SP], #0x10
    // 0xe48e64: b               #0xe4890c
    // 0xe48e68: SaveReg d0
    //     0xe48e68: str             q0, [SP, #-0x10]!
    // 0xe48e6c: SaveReg r0
    //     0xe48e6c: str             x0, [SP, #-8]!
    // 0xe48e70: r0 = AllocateDouble()
    //     0xe48e70: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e74: mov             x1, x0
    // 0xe48e78: RestoreReg r0
    //     0xe48e78: ldr             x0, [SP], #8
    // 0xe48e7c: RestoreReg d0
    //     0xe48e7c: ldr             q0, [SP], #0x10
    // 0xe48e80: b               #0xe489a4
    // 0xe48e84: SaveReg d0
    //     0xe48e84: str             q0, [SP, #-0x10]!
    // 0xe48e88: SaveReg r0
    //     0xe48e88: str             x0, [SP, #-8]!
    // 0xe48e8c: r0 = AllocateDouble()
    //     0xe48e8c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48e90: mov             x1, x0
    // 0xe48e94: RestoreReg r0
    //     0xe48e94: ldr             x0, [SP], #8
    // 0xe48e98: RestoreReg d0
    //     0xe48e98: ldr             q0, [SP], #0x10
    // 0xe48e9c: b               #0xe489d4
    // 0xe48ea0: SaveReg d0
    //     0xe48ea0: str             q0, [SP, #-0x10]!
    // 0xe48ea4: SaveReg r0
    //     0xe48ea4: str             x0, [SP, #-8]!
    // 0xe48ea8: r0 = AllocateDouble()
    //     0xe48ea8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48eac: mov             x1, x0
    // 0xe48eb0: RestoreReg r0
    //     0xe48eb0: ldr             x0, [SP], #8
    // 0xe48eb4: RestoreReg d0
    //     0xe48eb4: ldr             q0, [SP], #0x10
    // 0xe48eb8: b               #0xe48a0c
    // 0xe48ebc: SaveReg d0
    //     0xe48ebc: str             q0, [SP, #-0x10]!
    // 0xe48ec0: SaveReg r0
    //     0xe48ec0: str             x0, [SP, #-8]!
    // 0xe48ec4: r0 = AllocateDouble()
    //     0xe48ec4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe48ec8: mov             x1, x0
    // 0xe48ecc: RestoreReg r0
    //     0xe48ecc: ldr             x0, [SP], #8
    // 0xe48ed0: RestoreReg d0
    //     0xe48ed0: ldr             q0, [SP], #0x10
    // 0xe48ed4: b               #0xe48a3c
  }
  _ strokePath(/* No info */) {
    // ** addr: 0xe492e8, size: 0x70
    // 0xe492e8: EnterFrame
    //     0xe492e8: stp             fp, lr, [SP, #-0x10]!
    //     0xe492ec: mov             fp, SP
    // 0xe492f0: AllocStack(0x8)
    //     0xe492f0: sub             SP, SP, #8
    // 0xe492f4: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */)
    //     0xe492f4: mov             x0, x1
    //     0xe492f8: stur            x1, [fp, #-8]
    // 0xe492fc: CheckStackOverflow
    //     0xe492fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49300: cmp             SP, x16
    //     0xe49304: b.ls            #0xe49350
    // 0xe49308: LoadField: r1 = r0->field_13
    //     0xe49308: ldur            w1, [x0, #0x13]
    // 0xe4930c: DecompressPointer r1
    //     0xe4930c: add             x1, x1, HEAP, lsl #32
    // 0xe49310: r2 = "S "
    //     0xe49310: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dd90] "S "
    //     0xe49314: ldr             x2, [x2, #0xd90]
    // 0xe49318: r0 = putString()
    //     0xe49318: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4931c: ldur            x0, [fp, #-8]
    // 0xe49320: LoadField: r1 = r0->field_f
    //     0xe49320: ldur            w1, [x0, #0xf]
    // 0xe49324: DecompressPointer r1
    //     0xe49324: add             x1, x1, HEAP, lsl #32
    // 0xe49328: r0 = LoadClassIdInstr(r1)
    //     0xe49328: ldur            x0, [x1, #-1]
    //     0xe4932c: ubfx            x0, x0, #0xc, #0x14
    // 0xe49330: r2 = true
    //     0xe49330: add             x2, NULL, #0x20  ; true
    // 0xe49334: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe49334: sub             lr, x0, #1, lsl #12
    //     0xe49338: ldr             lr, [x21, lr, lsl #3]
    //     0xe4933c: blr             lr
    // 0xe49340: r0 = Null
    //     0xe49340: mov             x0, NULL
    // 0xe49344: LeaveFrame
    //     0xe49344: mov             SP, fp
    //     0xe49348: ldp             fp, lr, [SP], #0x10
    // 0xe4934c: ret
    //     0xe4934c: ret             
    // 0xe49350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49354: b               #0xe49308
  }
  _ setLineWidth(/* No info */) {
    // ** addr: 0xe49358, size: 0xb8
    // 0xe49358: EnterFrame
    //     0xe49358: stp             fp, lr, [SP, #-0x10]!
    //     0xe4935c: mov             fp, SP
    // 0xe49360: AllocStack(0x10)
    //     0xe49360: sub             SP, SP, #0x10
    // 0xe49364: SetupParameters(PdfGraphics this /* r1 => r1, fp-0x10 */)
    //     0xe49364: stur            x1, [fp, #-0x10]
    // 0xe49368: CheckStackOverflow
    //     0xe49368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4936c: cmp             SP, x16
    //     0xe49370: b.ls            #0xe493f0
    // 0xe49374: r0 = inline_Allocate_Double()
    //     0xe49374: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe49378: add             x0, x0, #0x10
    //     0xe4937c: cmp             x2, x0
    //     0xe49380: b.ls            #0xe493f8
    //     0xe49384: str             x0, [THR, #0x50]  ; THR::top
    //     0xe49388: sub             x0, x0, #0xf
    //     0xe4938c: movz            x2, #0xe15c
    //     0xe49390: movk            x2, #0x3, lsl #16
    //     0xe49394: stur            x2, [x0, #-1]
    // 0xe49398: StoreField: r0->field_7 = d0
    //     0xe49398: stur            d0, [x0, #7]
    // 0xe4939c: stur            x0, [fp, #-8]
    // 0xe493a0: r0 = PdfNum()
    //     0xe493a0: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe493a4: mov             x1, x0
    // 0xe493a8: ldur            x0, [fp, #-8]
    // 0xe493ac: StoreField: r1->field_7 = r0
    //     0xe493ac: stur            w0, [x1, #7]
    // 0xe493b0: ldur            x0, [fp, #-0x10]
    // 0xe493b4: LoadField: r2 = r0->field_f
    //     0xe493b4: ldur            w2, [x0, #0xf]
    // 0xe493b8: DecompressPointer r2
    //     0xe493b8: add             x2, x2, HEAP, lsl #32
    // 0xe493bc: LoadField: r4 = r0->field_13
    //     0xe493bc: ldur            w4, [x0, #0x13]
    // 0xe493c0: DecompressPointer r4
    //     0xe493c0: add             x4, x4, HEAP, lsl #32
    // 0xe493c4: mov             x3, x4
    // 0xe493c8: stur            x4, [fp, #-8]
    // 0xe493cc: r0 = output()
    //     0xe493cc: bl              #0xe7f1d8  ; [package:pdf/src/pdf/format/num.dart] PdfNum::output
    // 0xe493d0: ldur            x1, [fp, #-8]
    // 0xe493d4: r2 = " w "
    //     0xe493d4: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dd98] " w "
    //     0xe493d8: ldr             x2, [x2, #0xd98]
    // 0xe493dc: r0 = putString()
    //     0xe493dc: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe493e0: r0 = Null
    //     0xe493e0: mov             x0, NULL
    // 0xe493e4: LeaveFrame
    //     0xe493e4: mov             SP, fp
    //     0xe493e8: ldp             fp, lr, [SP], #0x10
    // 0xe493ec: ret
    //     0xe493ec: ret             
    // 0xe493f0: r0 = StackOverflowSharedWithFPURegs()
    //     0xe493f0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe493f4: b               #0xe49374
    // 0xe493f8: SaveReg d0
    //     0xe493f8: str             q0, [SP, #-0x10]!
    // 0xe493fc: SaveReg r1
    //     0xe493fc: str             x1, [SP, #-8]!
    // 0xe49400: r0 = AllocateDouble()
    //     0xe49400: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe49404: RestoreReg r1
    //     0xe49404: ldr             x1, [SP], #8
    // 0xe49408: RestoreReg d0
    //     0xe49408: ldr             q0, [SP], #0x10
    // 0xe4940c: b               #0xe49398
  }
  _ setLineDashPattern(/* No info */) {
    // ** addr: 0xe49410, size: 0xec
    // 0xe49410: EnterFrame
    //     0xe49410: stp             fp, lr, [SP, #-0x10]!
    //     0xe49414: mov             fp, SP
    // 0xe49418: AllocStack(0x20)
    //     0xe49418: sub             SP, SP, #0x20
    // 0xe4941c: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */, [int _ = 0 /* r2, fp-0x8 */])
    //     0xe4941c: mov             x0, x1
    //     0xe49420: stur            x1, [fp, #-0x10]
    //     0xe49424: mov             x1, x2
    //     0xe49428: ldur            w2, [x4, #0x13]
    //     0xe4942c: sub             x3, x2, #4
    //     0xe49430: cmp             w3, #2
    //     0xe49434: b.lt            #0xe49454
    //     0xe49438: add             x2, fp, w3, sxtw #2
    //     0xe4943c: ldr             x2, [x2, #8]
    //     0xe49440: sbfx            x3, x2, #1, #0x1f
    //     0xe49444: tbz             w2, #0, #0xe4944c
    //     0xe49448: ldur            x3, [x2, #7]
    //     0xe4944c: mov             x2, x3
    //     0xe49450: b               #0xe49458
    //     0xe49454: movz            x2, #0
    //     0xe49458: stur            x2, [fp, #-8]
    // 0xe4945c: CheckStackOverflow
    //     0xe4945c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49460: cmp             SP, x16
    //     0xe49464: b.ls            #0xe494f4
    // 0xe49468: r0 = fromNum()
    //     0xe49468: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe4946c: mov             x1, x0
    // 0xe49470: ldur            x0, [fp, #-0x10]
    // 0xe49474: LoadField: r2 = r0->field_f
    //     0xe49474: ldur            w2, [x0, #0xf]
    // 0xe49478: DecompressPointer r2
    //     0xe49478: add             x2, x2, HEAP, lsl #32
    // 0xe4947c: LoadField: r4 = r0->field_13
    //     0xe4947c: ldur            w4, [x0, #0x13]
    // 0xe49480: DecompressPointer r4
    //     0xe49480: add             x4, x4, HEAP, lsl #32
    // 0xe49484: mov             x3, x4
    // 0xe49488: stur            x4, [fp, #-0x18]
    // 0xe4948c: r0 = output()
    //     0xe4948c: bl              #0xe7e8c4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::output
    // 0xe49490: r1 = Null
    //     0xe49490: mov             x1, NULL
    // 0xe49494: r2 = 6
    //     0xe49494: movz            x2, #0x6
    // 0xe49498: r0 = AllocateArray()
    //     0xe49498: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe4949c: mov             x2, x0
    // 0xe494a0: r16 = " "
    //     0xe494a0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe494a4: StoreField: r2->field_f = r16
    //     0xe494a4: stur            w16, [x2, #0xf]
    // 0xe494a8: ldur            x3, [fp, #-8]
    // 0xe494ac: r0 = BoxInt64Instr(r3)
    //     0xe494ac: sbfiz           x0, x3, #1, #0x1f
    //     0xe494b0: cmp             x3, x0, asr #1
    //     0xe494b4: b.eq            #0xe494c0
    //     0xe494b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe494bc: stur            x3, [x0, #7]
    // 0xe494c0: StoreField: r2->field_13 = r0
    //     0xe494c0: stur            w0, [x2, #0x13]
    // 0xe494c4: r16 = " d "
    //     0xe494c4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46dc0] " d "
    //     0xe494c8: ldr             x16, [x16, #0xdc0]
    // 0xe494cc: ArrayStore: r2[0] = r16  ; List_4
    //     0xe494cc: stur            w16, [x2, #0x17]
    // 0xe494d0: str             x2, [SP]
    // 0xe494d4: r0 = _interpolate()
    //     0xe494d4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe494d8: ldur            x1, [fp, #-0x18]
    // 0xe494dc: mov             x2, x0
    // 0xe494e0: r0 = putString()
    //     0xe494e0: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe494e4: r0 = Null
    //     0xe494e4: mov             x0, NULL
    // 0xe494e8: LeaveFrame
    //     0xe494e8: mov             SP, fp
    //     0xe494ec: ldp             fp, lr, [SP], #0x10
    // 0xe494f0: ret
    //     0xe494f0: ret             
    // 0xe494f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe494f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe494f8: b               #0xe49468
  }
  _ setMiterLimit(/* No info */) {
    // ** addr: 0xe494fc, size: 0xb8
    // 0xe494fc: EnterFrame
    //     0xe494fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe49500: mov             fp, SP
    // 0xe49504: AllocStack(0x10)
    //     0xe49504: sub             SP, SP, #0x10
    // 0xe49508: SetupParameters(PdfGraphics this /* r1 => r1, fp-0x10 */)
    //     0xe49508: stur            x1, [fp, #-0x10]
    // 0xe4950c: CheckStackOverflow
    //     0xe4950c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49510: cmp             SP, x16
    //     0xe49514: b.ls            #0xe49594
    // 0xe49518: r0 = inline_Allocate_Double()
    //     0xe49518: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe4951c: add             x0, x0, #0x10
    //     0xe49520: cmp             x2, x0
    //     0xe49524: b.ls            #0xe4959c
    //     0xe49528: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4952c: sub             x0, x0, #0xf
    //     0xe49530: movz            x2, #0xe15c
    //     0xe49534: movk            x2, #0x3, lsl #16
    //     0xe49538: stur            x2, [x0, #-1]
    // 0xe4953c: StoreField: r0->field_7 = d0
    //     0xe4953c: stur            d0, [x0, #7]
    // 0xe49540: stur            x0, [fp, #-8]
    // 0xe49544: r0 = PdfNum()
    //     0xe49544: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe49548: mov             x1, x0
    // 0xe4954c: ldur            x0, [fp, #-8]
    // 0xe49550: StoreField: r1->field_7 = r0
    //     0xe49550: stur            w0, [x1, #7]
    // 0xe49554: ldur            x0, [fp, #-0x10]
    // 0xe49558: LoadField: r2 = r0->field_f
    //     0xe49558: ldur            w2, [x0, #0xf]
    // 0xe4955c: DecompressPointer r2
    //     0xe4955c: add             x2, x2, HEAP, lsl #32
    // 0xe49560: LoadField: r4 = r0->field_13
    //     0xe49560: ldur            w4, [x0, #0x13]
    // 0xe49564: DecompressPointer r4
    //     0xe49564: add             x4, x4, HEAP, lsl #32
    // 0xe49568: mov             x3, x4
    // 0xe4956c: stur            x4, [fp, #-8]
    // 0xe49570: r0 = output()
    //     0xe49570: bl              #0xe7f1d8  ; [package:pdf/src/pdf/format/num.dart] PdfNum::output
    // 0xe49574: ldur            x1, [fp, #-8]
    // 0xe49578: r2 = " M "
    //     0xe49578: add             x2, PP, #0x46, lsl #12  ; [pp+0x46e08] " M "
    //     0xe4957c: ldr             x2, [x2, #0xe08]
    // 0xe49580: r0 = putString()
    //     0xe49580: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe49584: r0 = Null
    //     0xe49584: mov             x0, NULL
    // 0xe49588: LeaveFrame
    //     0xe49588: mov             SP, fp
    //     0xe4958c: ldp             fp, lr, [SP], #0x10
    // 0xe49590: ret
    //     0xe49590: ret             
    // 0xe49594: r0 = StackOverflowSharedWithFPURegs()
    //     0xe49594: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe49598: b               #0xe49518
    // 0xe4959c: SaveReg d0
    //     0xe4959c: str             q0, [SP, #-0x10]!
    // 0xe495a0: SaveReg r1
    //     0xe495a0: str             x1, [SP, #-8]!
    // 0xe495a4: r0 = AllocateDouble()
    //     0xe495a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe495a8: RestoreReg r1
    //     0xe495a8: ldr             x1, [SP], #8
    // 0xe495ac: RestoreReg d0
    //     0xe495ac: ldr             q0, [SP], #0x10
    // 0xe495b0: b               #0xe4953c
  }
  _ setLineJoin(/* No info */) {
    // ** addr: 0xe495b4, size: 0x90
    // 0xe495b4: EnterFrame
    //     0xe495b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe495b8: mov             fp, SP
    // 0xe495bc: AllocStack(0x18)
    //     0xe495bc: sub             SP, SP, #0x18
    // 0xe495c0: CheckStackOverflow
    //     0xe495c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe495c4: cmp             SP, x16
    //     0xe495c8: b.ls            #0xe4963c
    // 0xe495cc: LoadField: r3 = r1->field_13
    //     0xe495cc: ldur            w3, [x1, #0x13]
    // 0xe495d0: DecompressPointer r3
    //     0xe495d0: add             x3, x3, HEAP, lsl #32
    // 0xe495d4: stur            x3, [fp, #-0x10]
    // 0xe495d8: LoadField: r4 = r2->field_7
    //     0xe495d8: ldur            x4, [x2, #7]
    // 0xe495dc: r0 = BoxInt64Instr(r4)
    //     0xe495dc: sbfiz           x0, x4, #1, #0x1f
    //     0xe495e0: cmp             x4, x0, asr #1
    //     0xe495e4: b.eq            #0xe495f0
    //     0xe495e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe495ec: stur            x4, [x0, #7]
    // 0xe495f0: r1 = Null
    //     0xe495f0: mov             x1, NULL
    // 0xe495f4: r2 = 4
    //     0xe495f4: movz            x2, #0x4
    // 0xe495f8: stur            x0, [fp, #-8]
    // 0xe495fc: r0 = AllocateArray()
    //     0xe495fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe49600: mov             x1, x0
    // 0xe49604: ldur            x0, [fp, #-8]
    // 0xe49608: StoreField: r1->field_f = r0
    //     0xe49608: stur            w0, [x1, #0xf]
    // 0xe4960c: r16 = " j "
    //     0xe4960c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e10] " j "
    //     0xe49610: ldr             x16, [x16, #0xe10]
    // 0xe49614: StoreField: r1->field_13 = r16
    //     0xe49614: stur            w16, [x1, #0x13]
    // 0xe49618: str             x1, [SP]
    // 0xe4961c: r0 = _interpolate()
    //     0xe4961c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe49620: ldur            x1, [fp, #-0x10]
    // 0xe49624: mov             x2, x0
    // 0xe49628: r0 = putString()
    //     0xe49628: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4962c: r0 = Null
    //     0xe4962c: mov             x0, NULL
    // 0xe49630: LeaveFrame
    //     0xe49630: mov             SP, fp
    //     0xe49634: ldp             fp, lr, [SP], #0x10
    // 0xe49638: ret
    //     0xe49638: ret             
    // 0xe4963c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4963c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49640: b               #0xe495cc
  }
  _ setLineCap(/* No info */) {
    // ** addr: 0xe49644, size: 0x90
    // 0xe49644: EnterFrame
    //     0xe49644: stp             fp, lr, [SP, #-0x10]!
    //     0xe49648: mov             fp, SP
    // 0xe4964c: AllocStack(0x18)
    //     0xe4964c: sub             SP, SP, #0x18
    // 0xe49650: CheckStackOverflow
    //     0xe49650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49654: cmp             SP, x16
    //     0xe49658: b.ls            #0xe496cc
    // 0xe4965c: LoadField: r3 = r1->field_13
    //     0xe4965c: ldur            w3, [x1, #0x13]
    // 0xe49660: DecompressPointer r3
    //     0xe49660: add             x3, x3, HEAP, lsl #32
    // 0xe49664: stur            x3, [fp, #-0x10]
    // 0xe49668: LoadField: r4 = r2->field_7
    //     0xe49668: ldur            x4, [x2, #7]
    // 0xe4966c: r0 = BoxInt64Instr(r4)
    //     0xe4966c: sbfiz           x0, x4, #1, #0x1f
    //     0xe49670: cmp             x4, x0, asr #1
    //     0xe49674: b.eq            #0xe49680
    //     0xe49678: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe4967c: stur            x4, [x0, #7]
    // 0xe49680: r1 = Null
    //     0xe49680: mov             x1, NULL
    // 0xe49684: r2 = 4
    //     0xe49684: movz            x2, #0x4
    // 0xe49688: stur            x0, [fp, #-8]
    // 0xe4968c: r0 = AllocateArray()
    //     0xe4968c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe49690: mov             x1, x0
    // 0xe49694: ldur            x0, [fp, #-8]
    // 0xe49698: StoreField: r1->field_f = r0
    //     0xe49698: stur            w0, [x1, #0xf]
    // 0xe4969c: r16 = " J "
    //     0xe4969c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e18] " J "
    //     0xe496a0: ldr             x16, [x16, #0xe18]
    // 0xe496a4: StoreField: r1->field_13 = r16
    //     0xe496a4: stur            w16, [x1, #0x13]
    // 0xe496a8: str             x1, [SP]
    // 0xe496ac: r0 = _interpolate()
    //     0xe496ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe496b0: ldur            x1, [fp, #-0x10]
    // 0xe496b4: mov             x2, x0
    // 0xe496b8: r0 = putString()
    //     0xe496b8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe496bc: r0 = Null
    //     0xe496bc: mov             x0, NULL
    // 0xe496c0: LeaveFrame
    //     0xe496c0: mov             SP, fp
    //     0xe496c4: ldp             fp, lr, [SP], #0x10
    // 0xe496c8: ret
    //     0xe496c8: ret             
    // 0xe496cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe496cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe496d0: b               #0xe4965c
  }
  _ fillPath(/* No info */) {
    // ** addr: 0xe496d4, size: 0xfc
    // 0xe496d4: EnterFrame
    //     0xe496d4: stp             fp, lr, [SP, #-0x10]!
    //     0xe496d8: mov             fp, SP
    // 0xe496dc: AllocStack(0x20)
    //     0xe496dc: sub             SP, SP, #0x20
    // 0xe496e0: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x18 */, {dynamic evenOdd = false /* r3, fp-0x10 */})
    //     0xe496e0: mov             x0, x1
    //     0xe496e4: stur            x1, [fp, #-0x18]
    //     0xe496e8: ldur            w1, [x4, #0x13]
    //     0xe496ec: ldur            w2, [x4, #0x1f]
    //     0xe496f0: add             x2, x2, HEAP, lsl #32
    //     0xe496f4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9b0] "evenOdd"
    //     0xe496f8: ldr             x16, [x16, #0x9b0]
    //     0xe496fc: cmp             w2, w16
    //     0xe49700: b.ne            #0xe49720
    //     0xe49704: ldur            w2, [x4, #0x23]
    //     0xe49708: add             x2, x2, HEAP, lsl #32
    //     0xe4970c: sub             w3, w1, w2
    //     0xe49710: add             x1, fp, w3, sxtw #2
    //     0xe49714: ldr             x1, [x1, #8]
    //     0xe49718: mov             x3, x1
    //     0xe4971c: b               #0xe49724
    //     0xe49720: add             x3, NULL, #0x30  ; false
    //     0xe49724: stur            x3, [fp, #-0x10]
    // 0xe49728: CheckStackOverflow
    //     0xe49728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4972c: cmp             SP, x16
    //     0xe49730: b.ls            #0xe497c8
    // 0xe49734: LoadField: r4 = r0->field_13
    //     0xe49734: ldur            w4, [x0, #0x13]
    // 0xe49738: DecompressPointer r4
    //     0xe49738: add             x4, x4, HEAP, lsl #32
    // 0xe4973c: stur            x4, [fp, #-8]
    // 0xe49740: r1 = Null
    //     0xe49740: mov             x1, NULL
    // 0xe49744: r2 = 6
    //     0xe49744: movz            x2, #0x6
    // 0xe49748: r0 = AllocateArray()
    //     0xe49748: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe4974c: r16 = "f"
    //     0xe4974c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9b8] "f"
    //     0xe49750: ldr             x16, [x16, #0x9b8]
    // 0xe49754: StoreField: r0->field_f = r16
    //     0xe49754: stur            w16, [x0, #0xf]
    // 0xe49758: ldur            x1, [fp, #-0x10]
    // 0xe4975c: tbnz            w1, #4, #0xe4976c
    // 0xe49760: r2 = "*"
    //     0xe49760: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xe49764: ldr             x2, [x2, #0xd20]
    // 0xe49768: b               #0xe49770
    // 0xe4976c: r2 = ""
    //     0xe4976c: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe49770: ldur            x1, [fp, #-0x18]
    // 0xe49774: StoreField: r0->field_13 = r2
    //     0xe49774: stur            w2, [x0, #0x13]
    // 0xe49778: r16 = " "
    //     0xe49778: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe4977c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe4977c: stur            w16, [x0, #0x17]
    // 0xe49780: str             x0, [SP]
    // 0xe49784: r0 = _interpolate()
    //     0xe49784: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe49788: ldur            x1, [fp, #-8]
    // 0xe4978c: mov             x2, x0
    // 0xe49790: r0 = putString()
    //     0xe49790: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe49794: ldur            x0, [fp, #-0x18]
    // 0xe49798: LoadField: r1 = r0->field_f
    //     0xe49798: ldur            w1, [x0, #0xf]
    // 0xe4979c: DecompressPointer r1
    //     0xe4979c: add             x1, x1, HEAP, lsl #32
    // 0xe497a0: r0 = LoadClassIdInstr(r1)
    //     0xe497a0: ldur            x0, [x1, #-1]
    //     0xe497a4: ubfx            x0, x0, #0xc, #0x14
    // 0xe497a8: r2 = true
    //     0xe497a8: add             x2, NULL, #0x20  ; true
    // 0xe497ac: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe497ac: sub             lr, x0, #1, lsl #12
    //     0xe497b0: ldr             lr, [x21, lr, lsl #3]
    //     0xe497b4: blr             lr
    // 0xe497b8: r0 = Null
    //     0xe497b8: mov             x0, NULL
    // 0xe497bc: LeaveFrame
    //     0xe497bc: mov             SP, fp
    //     0xe497c0: ldp             fp, lr, [SP], #0x10
    // 0xe497c4: ret
    //     0xe497c4: ret             
    // 0xe497c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe497c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe497cc: b               #0xe49734
  }
  _ drawShape(/* No info */) {
    // ** addr: 0xe497d0, size: 0x5c
    // 0xe497d0: EnterFrame
    //     0xe497d0: stp             fp, lr, [SP, #-0x10]!
    //     0xe497d4: mov             fp, SP
    // 0xe497d8: AllocStack(0x10)
    //     0xe497d8: sub             SP, SP, #0x10
    // 0xe497dc: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xe497dc: mov             x0, x1
    //     0xe497e0: stur            x1, [fp, #-8]
    //     0xe497e4: mov             x1, x2
    //     0xe497e8: stur            x2, [fp, #-0x10]
    // 0xe497ec: CheckStackOverflow
    //     0xe497ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe497f0: cmp             SP, x16
    //     0xe497f4: b.ls            #0xe49824
    // 0xe497f8: r0 = _PathProxy()
    //     0xe497f8: bl              #0xe49980  ; Allocate_PathProxyStub -> _PathProxy (size=0xc)
    // 0xe497fc: mov             x1, x0
    // 0xe49800: ldur            x0, [fp, #-8]
    // 0xe49804: StoreField: r1->field_7 = r0
    //     0xe49804: stur            w0, [x1, #7]
    // 0xe49808: mov             x2, x1
    // 0xe4980c: ldur            x1, [fp, #-0x10]
    // 0xe49810: r0 = writeSvgPathDataToPath()
    //     0xe49810: bl              #0xe4982c  ; [package:path_parsing/src/path_parsing.dart] ::writeSvgPathDataToPath
    // 0xe49814: r0 = Null
    //     0xe49814: mov             x0, NULL
    // 0xe49818: LeaveFrame
    //     0xe49818: mov             SP, fp
    //     0xe4981c: ldp             fp, lr, [SP], #0x10
    // 0xe49820: ret
    //     0xe49820: ret             
    // 0xe49824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49824: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49828: b               #0xe497f8
  }
  _ drawString(/* No info */) {
    // ** addr: 0xe49e60, size: 0x2e8
    // 0xe49e60: EnterFrame
    //     0xe49e60: stp             fp, lr, [SP, #-0x10]!
    //     0xe49e64: mov             fp, SP
    // 0xe49e68: AllocStack(0x48)
    //     0xe49e68: sub             SP, SP, #0x48
    // 0xe49e6c: SetupParameters(PdfGraphics this /* r1 => r5, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */, dynamic _ /* r3 => r0, fp-0x30 */, dynamic _ /* d0 => d0, fp-0x38 */, dynamic _ /* d1 => d1, fp-0x40 */, dynamic _ /* d2 => d2, fp-0x48 */, {dynamic charSpace = Null /* r6, fp-0x18 */, dynamic mode = Instance_PdfTextRenderingMode /* r4, fp-0x10 */})
    //     0xe49e6c: mov             x5, x1
    //     0xe49e70: mov             x0, x3
    //     0xe49e74: stur            x3, [fp, #-0x30]
    //     0xe49e78: mov             x3, x2
    //     0xe49e7c: stur            x1, [fp, #-0x20]
    //     0xe49e80: stur            x2, [fp, #-0x28]
    //     0xe49e84: stur            d0, [fp, #-0x38]
    //     0xe49e88: stur            d1, [fp, #-0x40]
    //     0xe49e8c: stur            d2, [fp, #-0x48]
    //     0xe49e90: ldur            w1, [x4, #0x13]
    //     0xe49e94: ldur            w2, [x4, #0x1f]
    //     0xe49e98: add             x2, x2, HEAP, lsl #32
    //     0xe49e9c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46d58] "charSpace"
    //     0xe49ea0: ldr             x16, [x16, #0xd58]
    //     0xe49ea4: cmp             w2, w16
    //     0xe49ea8: b.ne            #0xe49ecc
    //     0xe49eac: ldur            w2, [x4, #0x23]
    //     0xe49eb0: add             x2, x2, HEAP, lsl #32
    //     0xe49eb4: sub             w6, w1, w2
    //     0xe49eb8: add             x2, fp, w6, sxtw #2
    //     0xe49ebc: ldr             x2, [x2, #8]
    //     0xe49ec0: mov             x6, x2
    //     0xe49ec4: movz            x2, #0x1
    //     0xe49ec8: b               #0xe49ed4
    //     0xe49ecc: mov             x6, NULL
    //     0xe49ed0: movz            x2, #0
    //     0xe49ed4: stur            x6, [fp, #-0x18]
    //     0xe49ed8: lsl             x7, x2, #1
    //     0xe49edc: lsl             w2, w7, #1
    //     0xe49ee0: add             w7, w2, #8
    //     0xe49ee4: add             x16, x4, w7, sxtw #1
    //     0xe49ee8: ldur            w8, [x16, #0xf]
    //     0xe49eec: add             x8, x8, HEAP, lsl #32
    //     0xe49ef0: ldr             x16, [PP, #0x1c10]  ; [pp+0x1c10] "mode"
    //     0xe49ef4: cmp             w8, w16
    //     0xe49ef8: b.ne            #0xe49f20
    //     0xe49efc: add             w7, w2, #0xa
    //     0xe49f00: add             x16, x4, w7, sxtw #1
    //     0xe49f04: ldur            w2, [x16, #0xf]
    //     0xe49f08: add             x2, x2, HEAP, lsl #32
    //     0xe49f0c: sub             w4, w1, w2
    //     0xe49f10: add             x1, fp, w4, sxtw #2
    //     0xe49f14: ldr             x1, [x1, #8]
    //     0xe49f18: mov             x4, x1
    //     0xe49f1c: b               #0xe49f28
    //     0xe49f20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e570] Obj!PdfTextRenderingMode@e2ed21
    //     0xe49f24: ldr             x4, [x4, #0x570]
    //     0xe49f28: stur            x4, [fp, #-0x10]
    // 0xe49f2c: CheckStackOverflow
    //     0xe49f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49f30: cmp             SP, x16
    //     0xe49f34: b.ls            #0xe4a118
    // 0xe49f38: LoadField: r7 = r5->field_13
    //     0xe49f38: ldur            w7, [x5, #0x13]
    // 0xe49f3c: DecompressPointer r7
    //     0xe49f3c: add             x7, x7, HEAP, lsl #32
    // 0xe49f40: mov             x1, x7
    // 0xe49f44: stur            x7, [fp, #-8]
    // 0xe49f48: r2 = "BT "
    //     0xe49f48: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d60] "BT "
    //     0xe49f4c: ldr             x2, [x2, #0xd60]
    // 0xe49f50: r0 = putString()
    //     0xe49f50: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe49f54: ldur            x1, [fp, #-0x20]
    // 0xe49f58: ldur            x2, [fp, #-0x28]
    // 0xe49f5c: ldur            d0, [fp, #-0x38]
    // 0xe49f60: ldur            x3, [fp, #-0x18]
    // 0xe49f64: ldur            x5, [fp, #-0x10]
    // 0xe49f68: r0 = setFont()
    //     0xe49f68: bl              #0xe4a148  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFont
    // 0xe49f6c: ldur            d0, [fp, #-0x40]
    // 0xe49f70: r0 = inline_Allocate_Double()
    //     0xe49f70: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe49f74: add             x0, x0, #0x10
    //     0xe49f78: cmp             x1, x0
    //     0xe49f7c: b.ls            #0xe4a120
    //     0xe49f80: str             x0, [THR, #0x50]  ; THR::top
    //     0xe49f84: sub             x0, x0, #0xf
    //     0xe49f88: movz            x1, #0xe15c
    //     0xe49f8c: movk            x1, #0x3, lsl #16
    //     0xe49f90: stur            x1, [x0, #-1]
    // 0xe49f94: StoreField: r0->field_7 = d0
    //     0xe49f94: stur            d0, [x0, #7]
    // 0xe49f98: stur            x0, [fp, #-0x10]
    // 0xe49f9c: r1 = Null
    //     0xe49f9c: mov             x1, NULL
    // 0xe49fa0: r2 = 4
    //     0xe49fa0: movz            x2, #0x4
    // 0xe49fa4: r0 = AllocateArray()
    //     0xe49fa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe49fa8: mov             x2, x0
    // 0xe49fac: ldur            x0, [fp, #-0x10]
    // 0xe49fb0: stur            x2, [fp, #-0x18]
    // 0xe49fb4: StoreField: r2->field_f = r0
    //     0xe49fb4: stur            w0, [x2, #0xf]
    // 0xe49fb8: ldur            d0, [fp, #-0x48]
    // 0xe49fbc: r0 = inline_Allocate_Double()
    //     0xe49fbc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe49fc0: add             x0, x0, #0x10
    //     0xe49fc4: cmp             x1, x0
    //     0xe49fc8: b.ls            #0xe4a130
    //     0xe49fcc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe49fd0: sub             x0, x0, #0xf
    //     0xe49fd4: movz            x1, #0xe15c
    //     0xe49fd8: movk            x1, #0x3, lsl #16
    //     0xe49fdc: stur            x1, [x0, #-1]
    // 0xe49fe0: StoreField: r0->field_7 = d0
    //     0xe49fe0: stur            d0, [x0, #7]
    // 0xe49fe4: StoreField: r2->field_13 = r0
    //     0xe49fe4: stur            w0, [x2, #0x13]
    // 0xe49fe8: r1 = <num>
    //     0xe49fe8: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xe49fec: r0 = AllocateGrowableArray()
    //     0xe49fec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe49ff0: mov             x1, x0
    // 0xe49ff4: ldur            x0, [fp, #-0x18]
    // 0xe49ff8: stur            x1, [fp, #-0x10]
    // 0xe49ffc: StoreField: r1->field_f = r0
    //     0xe49ffc: stur            w0, [x1, #0xf]
    // 0xe4a000: r0 = 4
    //     0xe4a000: movz            x0, #0x4
    // 0xe4a004: StoreField: r1->field_b = r0
    //     0xe4a004: stur            w0, [x1, #0xb]
    // 0xe4a008: r0 = PdfNumList()
    //     0xe4a008: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe4a00c: mov             x1, x0
    // 0xe4a010: ldur            x0, [fp, #-0x10]
    // 0xe4a014: StoreField: r1->field_7 = r0
    //     0xe4a014: stur            w0, [x1, #7]
    // 0xe4a018: ldur            x0, [fp, #-0x20]
    // 0xe4a01c: LoadField: r4 = r0->field_f
    //     0xe4a01c: ldur            w4, [x0, #0xf]
    // 0xe4a020: DecompressPointer r4
    //     0xe4a020: add             x4, x4, HEAP, lsl #32
    // 0xe4a024: mov             x2, x4
    // 0xe4a028: ldur            x3, [fp, #-8]
    // 0xe4a02c: stur            x4, [fp, #-0x10]
    // 0xe4a030: r0 = output()
    //     0xe4a030: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe4a034: ldur            x1, [fp, #-8]
    // 0xe4a038: r2 = " Td "
    //     0xe4a038: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d68] " Td "
    //     0xe4a03c: ldr             x2, [x2, #0xd68]
    // 0xe4a040: r0 = putString()
    //     0xe4a040: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a044: ldur            x1, [fp, #-8]
    // 0xe4a048: r2 = "["
    //     0xe4a048: ldr             x2, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xe4a04c: r0 = putString()
    //     0xe4a04c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a050: ldur            x0, [fp, #-0x28]
    // 0xe4a054: r1 = LoadClassIdInstr(r0)
    //     0xe4a054: ldur            x1, [x0, #-1]
    //     0xe4a058: ubfx            x1, x1, #0xc, #0x14
    // 0xe4a05c: cmp             x1, #0x37f
    // 0xe4a060: b.ne            #0xe4a0a8
    // 0xe4a064: ldur            x2, [fp, #-0x30]
    // 0xe4a068: r1 = Instance_Latin1Codec
    //     0xe4a068: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xe4a06c: r0 = encode()
    //     0xe4a06c: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xe4a070: stur            x0, [fp, #-0x18]
    // 0xe4a074: r0 = PdfString()
    //     0xe4a074: bl              #0x7b8c74  ; AllocatePdfStringStub -> PdfString (size=0x14)
    // 0xe4a078: mov             x1, x0
    // 0xe4a07c: ldur            x0, [fp, #-0x18]
    // 0xe4a080: StoreField: r1->field_7 = r0
    //     0xe4a080: stur            w0, [x1, #7]
    // 0xe4a084: r0 = Instance_PdfStringFormat
    //     0xe4a084: add             x0, PP, #0x46, lsl #12  ; [pp+0x46d70] Obj!PdfStringFormat@e2f041
    //     0xe4a088: ldr             x0, [x0, #0xd70]
    // 0xe4a08c: StoreField: r1->field_b = r0
    //     0xe4a08c: stur            w0, [x1, #0xb]
    // 0xe4a090: r0 = false
    //     0xe4a090: add             x0, NULL, #0x30  ; false
    // 0xe4a094: StoreField: r1->field_f = r0
    //     0xe4a094: stur            w0, [x1, #0xf]
    // 0xe4a098: ldur            x2, [fp, #-0x28]
    // 0xe4a09c: ldur            x3, [fp, #-8]
    // 0xe4a0a0: r0 = output()
    //     0xe4a0a0: bl              #0xe7f538  ; [package:pdf/src/pdf/format/string.dart] PdfString::output
    // 0xe4a0a4: b               #0xe4a0c8
    // 0xe4a0a8: mov             x1, x0
    // 0xe4a0ac: r0 = LoadClassIdInstr(r1)
    //     0xe4a0ac: ldur            x0, [x1, #-1]
    //     0xe4a0b0: ubfx            x0, x0, #0xc, #0x14
    // 0xe4a0b4: ldur            x2, [fp, #-8]
    // 0xe4a0b8: ldur            x3, [fp, #-0x30]
    // 0xe4a0bc: r0 = GDT[cid_x0 + -0xff0]()
    //     0xe4a0bc: sub             lr, x0, #0xff0
    //     0xe4a0c0: ldr             lr, [x21, lr, lsl #3]
    //     0xe4a0c4: blr             lr
    // 0xe4a0c8: ldur            x0, [fp, #-0x10]
    // 0xe4a0cc: ldur            x1, [fp, #-8]
    // 0xe4a0d0: r2 = "]TJ "
    //     0xe4a0d0: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d78] "]TJ "
    //     0xe4a0d4: ldr             x2, [x2, #0xd78]
    // 0xe4a0d8: r0 = putString()
    //     0xe4a0d8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a0dc: ldur            x1, [fp, #-8]
    // 0xe4a0e0: r2 = "ET "
    //     0xe4a0e0: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d80] "ET "
    //     0xe4a0e4: ldr             x2, [x2, #0xd80]
    // 0xe4a0e8: r0 = putString()
    //     0xe4a0e8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a0ec: ldur            x1, [fp, #-0x10]
    // 0xe4a0f0: r0 = LoadClassIdInstr(r1)
    //     0xe4a0f0: ldur            x0, [x1, #-1]
    //     0xe4a0f4: ubfx            x0, x0, #0xc, #0x14
    // 0xe4a0f8: r2 = true
    //     0xe4a0f8: add             x2, NULL, #0x20  ; true
    // 0xe4a0fc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe4a0fc: sub             lr, x0, #1, lsl #12
    //     0xe4a100: ldr             lr, [x21, lr, lsl #3]
    //     0xe4a104: blr             lr
    // 0xe4a108: r0 = Null
    //     0xe4a108: mov             x0, NULL
    // 0xe4a10c: LeaveFrame
    //     0xe4a10c: mov             SP, fp
    //     0xe4a110: ldp             fp, lr, [SP], #0x10
    // 0xe4a114: ret
    //     0xe4a114: ret             
    // 0xe4a118: r0 = StackOverflowSharedWithFPURegs()
    //     0xe4a118: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe4a11c: b               #0xe49f38
    // 0xe4a120: SaveReg d0
    //     0xe4a120: str             q0, [SP, #-0x10]!
    // 0xe4a124: r0 = AllocateDouble()
    //     0xe4a124: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe4a128: RestoreReg d0
    //     0xe4a128: ldr             q0, [SP], #0x10
    // 0xe4a12c: b               #0xe49f94
    // 0xe4a130: SaveReg d0
    //     0xe4a130: str             q0, [SP, #-0x10]!
    // 0xe4a134: SaveReg r2
    //     0xe4a134: str             x2, [SP, #-8]!
    // 0xe4a138: r0 = AllocateDouble()
    //     0xe4a138: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe4a13c: RestoreReg r2
    //     0xe4a13c: ldr             x2, [SP], #8
    // 0xe4a140: RestoreReg d0
    //     0xe4a140: ldr             q0, [SP], #0x10
    // 0xe4a144: b               #0xe49fe0
  }
  _ setFont(/* No info */) {
    // ** addr: 0xe4a148, size: 0x1d0
    // 0xe4a148: EnterFrame
    //     0xe4a148: stp             fp, lr, [SP, #-0x10]!
    //     0xe4a14c: mov             fp, SP
    // 0xe4a150: AllocStack(0x40)
    //     0xe4a150: sub             SP, SP, #0x40
    // 0xe4a154: SetupParameters(PdfGraphics this /* r1 => r6, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0xe4a154: mov             x6, x1
    //     0xe4a158: mov             x4, x2
    //     0xe4a15c: stur            x1, [fp, #-0x10]
    //     0xe4a160: stur            x2, [fp, #-0x18]
    //     0xe4a164: stur            x3, [fp, #-0x20]
    //     0xe4a168: stur            x5, [fp, #-0x28]
    //     0xe4a16c: stur            d0, [fp, #-0x38]
    // 0xe4a170: CheckStackOverflow
    //     0xe4a170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4a174: cmp             SP, x16
    //     0xe4a178: b.ls            #0xe4a300
    // 0xe4a17c: LoadField: r7 = r6->field_f
    //     0xe4a17c: ldur            w7, [x6, #0xf]
    // 0xe4a180: DecompressPointer r7
    //     0xe4a180: add             x7, x7, HEAP, lsl #32
    // 0xe4a184: stur            x7, [fp, #-8]
    // 0xe4a188: r0 = LoadClassIdInstr(r7)
    //     0xe4a188: ldur            x0, [x7, #-1]
    //     0xe4a18c: ubfx            x0, x0, #0xc, #0x14
    // 0xe4a190: mov             x1, x7
    // 0xe4a194: mov             x2, x4
    // 0xe4a198: r0 = GDT[cid_x0 + -0xffc]()
    //     0xe4a198: sub             lr, x0, #0xffc
    //     0xe4a19c: ldr             lr, [x21, lr, lsl #3]
    //     0xe4a1a0: blr             lr
    // 0xe4a1a4: ldur            x0, [fp, #-0x10]
    // 0xe4a1a8: LoadField: r2 = r0->field_13
    //     0xe4a1a8: ldur            w2, [x0, #0x13]
    // 0xe4a1ac: DecompressPointer r2
    //     0xe4a1ac: add             x2, x2, HEAP, lsl #32
    // 0xe4a1b0: ldur            x1, [fp, #-0x18]
    // 0xe4a1b4: stur            x2, [fp, #-0x30]
    // 0xe4a1b8: r0 = name()
    //     0xe4a1b8: bl              #0x7c93d0  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::name
    // 0xe4a1bc: r1 = Null
    //     0xe4a1bc: mov             x1, NULL
    // 0xe4a1c0: r2 = 4
    //     0xe4a1c0: movz            x2, #0x4
    // 0xe4a1c4: stur            x0, [fp, #-0x10]
    // 0xe4a1c8: r0 = AllocateArray()
    //     0xe4a1c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe4a1cc: mov             x1, x0
    // 0xe4a1d0: ldur            x0, [fp, #-0x10]
    // 0xe4a1d4: StoreField: r1->field_f = r0
    //     0xe4a1d4: stur            w0, [x1, #0xf]
    // 0xe4a1d8: r16 = " "
    //     0xe4a1d8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe4a1dc: StoreField: r1->field_13 = r16
    //     0xe4a1dc: stur            w16, [x1, #0x13]
    // 0xe4a1e0: str             x1, [SP]
    // 0xe4a1e4: r0 = _interpolate()
    //     0xe4a1e4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe4a1e8: ldur            x1, [fp, #-0x30]
    // 0xe4a1ec: mov             x2, x0
    // 0xe4a1f0: r0 = putString()
    //     0xe4a1f0: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a1f4: ldur            d0, [fp, #-0x38]
    // 0xe4a1f8: r0 = inline_Allocate_Double()
    //     0xe4a1f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe4a1fc: add             x0, x0, #0x10
    //     0xe4a200: cmp             x1, x0
    //     0xe4a204: b.ls            #0xe4a308
    //     0xe4a208: str             x0, [THR, #0x50]  ; THR::top
    //     0xe4a20c: sub             x0, x0, #0xf
    //     0xe4a210: movz            x1, #0xe15c
    //     0xe4a214: movk            x1, #0x3, lsl #16
    //     0xe4a218: stur            x1, [x0, #-1]
    // 0xe4a21c: StoreField: r0->field_7 = d0
    //     0xe4a21c: stur            d0, [x0, #7]
    // 0xe4a220: stur            x0, [fp, #-0x10]
    // 0xe4a224: r0 = PdfNum()
    //     0xe4a224: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe4a228: mov             x1, x0
    // 0xe4a22c: ldur            x0, [fp, #-0x10]
    // 0xe4a230: StoreField: r1->field_7 = r0
    //     0xe4a230: stur            w0, [x1, #7]
    // 0xe4a234: ldur            x2, [fp, #-8]
    // 0xe4a238: ldur            x3, [fp, #-0x30]
    // 0xe4a23c: r0 = output()
    //     0xe4a23c: bl              #0xe7f1d8  ; [package:pdf/src/pdf/format/num.dart] PdfNum::output
    // 0xe4a240: ldur            x1, [fp, #-0x30]
    // 0xe4a244: r2 = " Tf "
    //     0xe4a244: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d88] " Tf "
    //     0xe4a248: ldr             x2, [x2, #0xd88]
    // 0xe4a24c: r0 = putString()
    //     0xe4a24c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a250: ldur            x0, [fp, #-0x20]
    // 0xe4a254: cmp             w0, NULL
    // 0xe4a258: b.eq            #0xe4a288
    // 0xe4a25c: r0 = PdfNum()
    //     0xe4a25c: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe4a260: mov             x1, x0
    // 0xe4a264: ldur            x0, [fp, #-0x20]
    // 0xe4a268: StoreField: r1->field_7 = r0
    //     0xe4a268: stur            w0, [x1, #7]
    // 0xe4a26c: ldur            x2, [fp, #-8]
    // 0xe4a270: ldur            x3, [fp, #-0x30]
    // 0xe4a274: r0 = output()
    //     0xe4a274: bl              #0xe7f1d8  ; [package:pdf/src/pdf/format/num.dart] PdfNum::output
    // 0xe4a278: ldur            x1, [fp, #-0x30]
    // 0xe4a27c: r2 = " Tc "
    //     0xe4a27c: add             x2, PP, #0x46, lsl #12  ; [pp+0x46d90] " Tc "
    //     0xe4a280: ldr             x2, [x2, #0xd90]
    // 0xe4a284: r0 = putString()
    //     0xe4a284: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a288: ldur            x0, [fp, #-0x28]
    // 0xe4a28c: r16 = Instance_PdfTextRenderingMode
    //     0xe4a28c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e570] Obj!PdfTextRenderingMode@e2ed21
    //     0xe4a290: ldr             x16, [x16, #0x570]
    // 0xe4a294: cmp             w0, w16
    // 0xe4a298: b.eq            #0xe4a2f0
    // 0xe4a29c: LoadField: r2 = r0->field_7
    //     0xe4a29c: ldur            x2, [x0, #7]
    // 0xe4a2a0: r0 = BoxInt64Instr(r2)
    //     0xe4a2a0: sbfiz           x0, x2, #1, #0x1f
    //     0xe4a2a4: cmp             x2, x0, asr #1
    //     0xe4a2a8: b.eq            #0xe4a2b4
    //     0xe4a2ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe4a2b0: stur            x2, [x0, #7]
    // 0xe4a2b4: r1 = Null
    //     0xe4a2b4: mov             x1, NULL
    // 0xe4a2b8: r2 = 4
    //     0xe4a2b8: movz            x2, #0x4
    // 0xe4a2bc: stur            x0, [fp, #-8]
    // 0xe4a2c0: r0 = AllocateArray()
    //     0xe4a2c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe4a2c4: mov             x1, x0
    // 0xe4a2c8: ldur            x0, [fp, #-8]
    // 0xe4a2cc: StoreField: r1->field_f = r0
    //     0xe4a2cc: stur            w0, [x1, #0xf]
    // 0xe4a2d0: r16 = " Tr "
    //     0xe4a2d0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46d98] " Tr "
    //     0xe4a2d4: ldr             x16, [x16, #0xd98]
    // 0xe4a2d8: StoreField: r1->field_13 = r16
    //     0xe4a2d8: stur            w16, [x1, #0x13]
    // 0xe4a2dc: str             x1, [SP]
    // 0xe4a2e0: r0 = _interpolate()
    //     0xe4a2e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe4a2e4: ldur            x1, [fp, #-0x30]
    // 0xe4a2e8: mov             x2, x0
    // 0xe4a2ec: r0 = putString()
    //     0xe4a2ec: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe4a2f0: r0 = Null
    //     0xe4a2f0: mov             x0, NULL
    // 0xe4a2f4: LeaveFrame
    //     0xe4a2f4: mov             SP, fp
    //     0xe4a2f8: ldp             fp, lr, [SP], #0x10
    // 0xe4a2fc: ret
    //     0xe4a2fc: ret             
    // 0xe4a300: r0 = StackOverflowSharedWithFPURegs()
    //     0xe4a300: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe4a304: b               #0xe4a17c
    // 0xe4a308: SaveReg d0
    //     0xe4a308: str             q0, [SP, #-0x10]!
    // 0xe4a30c: r0 = AllocateDouble()
    //     0xe4a30c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe4a310: RestoreReg d0
    //     0xe4a310: ldr             q0, [SP], #0x10
    // 0xe4a314: b               #0xe4a21c
  }
  _ drawRect(/* No info */) {
    // ** addr: 0xe64800, size: 0x1ec
    // 0xe64800: EnterFrame
    //     0xe64800: stp             fp, lr, [SP, #-0x10]!
    //     0xe64804: mov             fp, SP
    // 0xe64808: AllocStack(0x30)
    //     0xe64808: sub             SP, SP, #0x30
    // 0xe6480c: r0 = 8
    //     0xe6480c: movz            x0, #0x8
    // 0xe64810: mov             x3, x1
    // 0xe64814: stur            x1, [fp, #-0x10]
    // 0xe64818: stur            d1, [fp, #-0x20]
    // 0xe6481c: stur            d2, [fp, #-0x28]
    // 0xe64820: stur            d3, [fp, #-0x30]
    // 0xe64824: CheckStackOverflow
    //     0xe64824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64828: cmp             SP, x16
    //     0xe6482c: b.ls            #0xe64978
    // 0xe64830: r4 = inline_Allocate_Double()
    //     0xe64830: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xe64834: add             x4, x4, #0x10
    //     0xe64838: cmp             x1, x4
    //     0xe6483c: b.ls            #0xe64980
    //     0xe64840: str             x4, [THR, #0x50]  ; THR::top
    //     0xe64844: sub             x4, x4, #0xf
    //     0xe64848: movz            x1, #0xe15c
    //     0xe6484c: movk            x1, #0x3, lsl #16
    //     0xe64850: stur            x1, [x4, #-1]
    // 0xe64854: StoreField: r4->field_7 = d0
    //     0xe64854: stur            d0, [x4, #7]
    // 0xe64858: mov             x2, x0
    // 0xe6485c: stur            x4, [fp, #-8]
    // 0xe64860: r1 = Null
    //     0xe64860: mov             x1, NULL
    // 0xe64864: r0 = AllocateArray()
    //     0xe64864: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe64868: mov             x2, x0
    // 0xe6486c: ldur            x0, [fp, #-8]
    // 0xe64870: stur            x2, [fp, #-0x18]
    // 0xe64874: StoreField: r2->field_f = r0
    //     0xe64874: stur            w0, [x2, #0xf]
    // 0xe64878: ldur            d0, [fp, #-0x20]
    // 0xe6487c: r0 = inline_Allocate_Double()
    //     0xe6487c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe64880: add             x0, x0, #0x10
    //     0xe64884: cmp             x1, x0
    //     0xe64888: b.ls            #0xe649a4
    //     0xe6488c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe64890: sub             x0, x0, #0xf
    //     0xe64894: movz            x1, #0xe15c
    //     0xe64898: movk            x1, #0x3, lsl #16
    //     0xe6489c: stur            x1, [x0, #-1]
    // 0xe648a0: StoreField: r0->field_7 = d0
    //     0xe648a0: stur            d0, [x0, #7]
    // 0xe648a4: StoreField: r2->field_13 = r0
    //     0xe648a4: stur            w0, [x2, #0x13]
    // 0xe648a8: ldur            d0, [fp, #-0x28]
    // 0xe648ac: r0 = inline_Allocate_Double()
    //     0xe648ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe648b0: add             x0, x0, #0x10
    //     0xe648b4: cmp             x1, x0
    //     0xe648b8: b.ls            #0xe649bc
    //     0xe648bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe648c0: sub             x0, x0, #0xf
    //     0xe648c4: movz            x1, #0xe15c
    //     0xe648c8: movk            x1, #0x3, lsl #16
    //     0xe648cc: stur            x1, [x0, #-1]
    // 0xe648d0: StoreField: r0->field_7 = d0
    //     0xe648d0: stur            d0, [x0, #7]
    // 0xe648d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xe648d4: stur            w0, [x2, #0x17]
    // 0xe648d8: ldur            d0, [fp, #-0x30]
    // 0xe648dc: r0 = inline_Allocate_Double()
    //     0xe648dc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe648e0: add             x0, x0, #0x10
    //     0xe648e4: cmp             x1, x0
    //     0xe648e8: b.ls            #0xe649d4
    //     0xe648ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xe648f0: sub             x0, x0, #0xf
    //     0xe648f4: movz            x1, #0xe15c
    //     0xe648f8: movk            x1, #0x3, lsl #16
    //     0xe648fc: stur            x1, [x0, #-1]
    // 0xe64900: StoreField: r0->field_7 = d0
    //     0xe64900: stur            d0, [x0, #7]
    // 0xe64904: StoreField: r2->field_1b = r0
    //     0xe64904: stur            w0, [x2, #0x1b]
    // 0xe64908: r1 = <num>
    //     0xe64908: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xe6490c: r0 = AllocateGrowableArray()
    //     0xe6490c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe64910: mov             x1, x0
    // 0xe64914: ldur            x0, [fp, #-0x18]
    // 0xe64918: stur            x1, [fp, #-8]
    // 0xe6491c: StoreField: r1->field_f = r0
    //     0xe6491c: stur            w0, [x1, #0xf]
    // 0xe64920: r0 = 8
    //     0xe64920: movz            x0, #0x8
    // 0xe64924: StoreField: r1->field_b = r0
    //     0xe64924: stur            w0, [x1, #0xb]
    // 0xe64928: r0 = PdfNumList()
    //     0xe64928: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe6492c: mov             x1, x0
    // 0xe64930: ldur            x0, [fp, #-8]
    // 0xe64934: StoreField: r1->field_7 = r0
    //     0xe64934: stur            w0, [x1, #7]
    // 0xe64938: ldur            x0, [fp, #-0x10]
    // 0xe6493c: LoadField: r2 = r0->field_f
    //     0xe6493c: ldur            w2, [x0, #0xf]
    // 0xe64940: DecompressPointer r2
    //     0xe64940: add             x2, x2, HEAP, lsl #32
    // 0xe64944: LoadField: r4 = r0->field_13
    //     0xe64944: ldur            w4, [x0, #0x13]
    // 0xe64948: DecompressPointer r4
    //     0xe64948: add             x4, x4, HEAP, lsl #32
    // 0xe6494c: mov             x3, x4
    // 0xe64950: stur            x4, [fp, #-8]
    // 0xe64954: r0 = output()
    //     0xe64954: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe64958: ldur            x1, [fp, #-8]
    // 0xe6495c: r2 = " re "
    //     0xe6495c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e0c0] " re "
    //     0xe64960: ldr             x2, [x2, #0xc0]
    // 0xe64964: r0 = putString()
    //     0xe64964: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe64968: r0 = Null
    //     0xe64968: mov             x0, NULL
    // 0xe6496c: LeaveFrame
    //     0xe6496c: mov             SP, fp
    //     0xe64970: ldp             fp, lr, [SP], #0x10
    // 0xe64974: ret
    //     0xe64974: ret             
    // 0xe64978: r0 = StackOverflowSharedWithFPURegs()
    //     0xe64978: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe6497c: b               #0xe64830
    // 0xe64980: stp             q2, q3, [SP, #-0x20]!
    // 0xe64984: stp             q0, q1, [SP, #-0x20]!
    // 0xe64988: stp             x0, x3, [SP, #-0x10]!
    // 0xe6498c: r0 = AllocateDouble()
    //     0xe6498c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe64990: mov             x4, x0
    // 0xe64994: ldp             x0, x3, [SP], #0x10
    // 0xe64998: ldp             q0, q1, [SP], #0x20
    // 0xe6499c: ldp             q2, q3, [SP], #0x20
    // 0xe649a0: b               #0xe64854
    // 0xe649a4: SaveReg d0
    //     0xe649a4: str             q0, [SP, #-0x10]!
    // 0xe649a8: SaveReg r2
    //     0xe649a8: str             x2, [SP, #-8]!
    // 0xe649ac: r0 = AllocateDouble()
    //     0xe649ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe649b0: RestoreReg r2
    //     0xe649b0: ldr             x2, [SP], #8
    // 0xe649b4: RestoreReg d0
    //     0xe649b4: ldr             q0, [SP], #0x10
    // 0xe649b8: b               #0xe648a0
    // 0xe649bc: SaveReg d0
    //     0xe649bc: str             q0, [SP, #-0x10]!
    // 0xe649c0: SaveReg r2
    //     0xe649c0: str             x2, [SP, #-8]!
    // 0xe649c4: r0 = AllocateDouble()
    //     0xe649c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe649c8: RestoreReg r2
    //     0xe649c8: ldr             x2, [SP], #8
    // 0xe649cc: RestoreReg d0
    //     0xe649cc: ldr             q0, [SP], #0x10
    // 0xe649d0: b               #0xe648d0
    // 0xe649d4: SaveReg d0
    //     0xe649d4: str             q0, [SP, #-0x10]!
    // 0xe649d8: SaveReg r2
    //     0xe649d8: str             x2, [SP, #-8]!
    // 0xe649dc: r0 = AllocateDouble()
    //     0xe649dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe649e0: RestoreReg r2
    //     0xe649e0: ldr             x2, [SP], #8
    // 0xe649e4: RestoreReg d0
    //     0xe649e4: ldr             q0, [SP], #0x10
    // 0xe649e8: b               #0xe64900
  }
  _ setFillColor(/* No info */) {
    // ** addr: 0xe64b2c, size: 0x1b8
    // 0xe64b2c: EnterFrame
    //     0xe64b2c: stp             fp, lr, [SP, #-0x10]!
    //     0xe64b30: mov             fp, SP
    // 0xe64b34: AllocStack(0x28)
    //     0xe64b34: sub             SP, SP, #0x28
    // 0xe64b38: r0 = 6
    //     0xe64b38: movz            x0, #0x6
    // 0xe64b3c: mov             x3, x1
    // 0xe64b40: stur            x1, [fp, #-0x10]
    // 0xe64b44: CheckStackOverflow
    //     0xe64b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64b48: cmp             SP, x16
    //     0xe64b4c: b.ls            #0xe64c84
    // 0xe64b50: cmp             w2, NULL
    // 0xe64b54: b.eq            #0xe64c8c
    // 0xe64b58: LoadField: d0 = r2->field_f
    //     0xe64b58: ldur            d0, [x2, #0xf]
    // 0xe64b5c: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xe64b5c: ldur            d1, [x2, #0x17]
    // 0xe64b60: stur            d1, [fp, #-0x28]
    // 0xe64b64: LoadField: d2 = r2->field_1f
    //     0xe64b64: ldur            d2, [x2, #0x1f]
    // 0xe64b68: stur            d2, [fp, #-0x20]
    // 0xe64b6c: r4 = inline_Allocate_Double()
    //     0xe64b6c: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xe64b70: add             x4, x4, #0x10
    //     0xe64b74: cmp             x1, x4
    //     0xe64b78: b.ls            #0xe64c90
    //     0xe64b7c: str             x4, [THR, #0x50]  ; THR::top
    //     0xe64b80: sub             x4, x4, #0xf
    //     0xe64b84: movz            x1, #0xe15c
    //     0xe64b88: movk            x1, #0x3, lsl #16
    //     0xe64b8c: stur            x1, [x4, #-1]
    // 0xe64b90: StoreField: r4->field_7 = d0
    //     0xe64b90: stur            d0, [x4, #7]
    // 0xe64b94: mov             x2, x0
    // 0xe64b98: stur            x4, [fp, #-8]
    // 0xe64b9c: r1 = Null
    //     0xe64b9c: mov             x1, NULL
    // 0xe64ba0: r0 = AllocateArray()
    //     0xe64ba0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe64ba4: mov             x2, x0
    // 0xe64ba8: ldur            x0, [fp, #-8]
    // 0xe64bac: stur            x2, [fp, #-0x18]
    // 0xe64bb0: StoreField: r2->field_f = r0
    //     0xe64bb0: stur            w0, [x2, #0xf]
    // 0xe64bb4: ldur            d0, [fp, #-0x28]
    // 0xe64bb8: r0 = inline_Allocate_Double()
    //     0xe64bb8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe64bbc: add             x0, x0, #0x10
    //     0xe64bc0: cmp             x1, x0
    //     0xe64bc4: b.ls            #0xe64cb4
    //     0xe64bc8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe64bcc: sub             x0, x0, #0xf
    //     0xe64bd0: movz            x1, #0xe15c
    //     0xe64bd4: movk            x1, #0x3, lsl #16
    //     0xe64bd8: stur            x1, [x0, #-1]
    // 0xe64bdc: StoreField: r0->field_7 = d0
    //     0xe64bdc: stur            d0, [x0, #7]
    // 0xe64be0: StoreField: r2->field_13 = r0
    //     0xe64be0: stur            w0, [x2, #0x13]
    // 0xe64be4: ldur            d0, [fp, #-0x20]
    // 0xe64be8: r0 = inline_Allocate_Double()
    //     0xe64be8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe64bec: add             x0, x0, #0x10
    //     0xe64bf0: cmp             x1, x0
    //     0xe64bf4: b.ls            #0xe64ccc
    //     0xe64bf8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe64bfc: sub             x0, x0, #0xf
    //     0xe64c00: movz            x1, #0xe15c
    //     0xe64c04: movk            x1, #0x3, lsl #16
    //     0xe64c08: stur            x1, [x0, #-1]
    // 0xe64c0c: StoreField: r0->field_7 = d0
    //     0xe64c0c: stur            d0, [x0, #7]
    // 0xe64c10: ArrayStore: r2[0] = r0  ; List_4
    //     0xe64c10: stur            w0, [x2, #0x17]
    // 0xe64c14: r1 = <double>
    //     0xe64c14: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe64c18: r0 = AllocateGrowableArray()
    //     0xe64c18: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe64c1c: mov             x1, x0
    // 0xe64c20: ldur            x0, [fp, #-0x18]
    // 0xe64c24: stur            x1, [fp, #-8]
    // 0xe64c28: StoreField: r1->field_f = r0
    //     0xe64c28: stur            w0, [x1, #0xf]
    // 0xe64c2c: r0 = 6
    //     0xe64c2c: movz            x0, #0x6
    // 0xe64c30: StoreField: r1->field_b = r0
    //     0xe64c30: stur            w0, [x1, #0xb]
    // 0xe64c34: r0 = PdfNumList()
    //     0xe64c34: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe64c38: mov             x1, x0
    // 0xe64c3c: ldur            x0, [fp, #-8]
    // 0xe64c40: StoreField: r1->field_7 = r0
    //     0xe64c40: stur            w0, [x1, #7]
    // 0xe64c44: ldur            x0, [fp, #-0x10]
    // 0xe64c48: LoadField: r2 = r0->field_f
    //     0xe64c48: ldur            w2, [x0, #0xf]
    // 0xe64c4c: DecompressPointer r2
    //     0xe64c4c: add             x2, x2, HEAP, lsl #32
    // 0xe64c50: LoadField: r4 = r0->field_13
    //     0xe64c50: ldur            w4, [x0, #0x13]
    // 0xe64c54: DecompressPointer r4
    //     0xe64c54: add             x4, x4, HEAP, lsl #32
    // 0xe64c58: mov             x3, x4
    // 0xe64c5c: stur            x4, [fp, #-8]
    // 0xe64c60: r0 = output()
    //     0xe64c60: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe64c64: ldur            x1, [fp, #-8]
    // 0xe64c68: r2 = " rg "
    //     0xe64c68: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dd78] " rg "
    //     0xe64c6c: ldr             x2, [x2, #0xd78]
    // 0xe64c70: r0 = putString()
    //     0xe64c70: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe64c74: r0 = Null
    //     0xe64c74: mov             x0, NULL
    // 0xe64c78: LeaveFrame
    //     0xe64c78: mov             SP, fp
    //     0xe64c7c: ldp             fp, lr, [SP], #0x10
    // 0xe64c80: ret
    //     0xe64c80: ret             
    // 0xe64c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64c84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64c88: b               #0xe64b50
    // 0xe64c8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64c8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64c90: stp             q1, q2, [SP, #-0x20]!
    // 0xe64c94: SaveReg d0
    //     0xe64c94: str             q0, [SP, #-0x10]!
    // 0xe64c98: stp             x0, x3, [SP, #-0x10]!
    // 0xe64c9c: r0 = AllocateDouble()
    //     0xe64c9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe64ca0: mov             x4, x0
    // 0xe64ca4: ldp             x0, x3, [SP], #0x10
    // 0xe64ca8: RestoreReg d0
    //     0xe64ca8: ldr             q0, [SP], #0x10
    // 0xe64cac: ldp             q1, q2, [SP], #0x20
    // 0xe64cb0: b               #0xe64b90
    // 0xe64cb4: SaveReg d0
    //     0xe64cb4: str             q0, [SP, #-0x10]!
    // 0xe64cb8: SaveReg r2
    //     0xe64cb8: str             x2, [SP, #-8]!
    // 0xe64cbc: r0 = AllocateDouble()
    //     0xe64cbc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe64cc0: RestoreReg r2
    //     0xe64cc0: ldr             x2, [SP], #8
    // 0xe64cc4: RestoreReg d0
    //     0xe64cc4: ldr             q0, [SP], #0x10
    // 0xe64cc8: b               #0xe64bdc
    // 0xe64ccc: SaveReg d0
    //     0xe64ccc: str             q0, [SP, #-0x10]!
    // 0xe64cd0: SaveReg r2
    //     0xe64cd0: str             x2, [SP, #-8]!
    // 0xe64cd4: r0 = AllocateDouble()
    //     0xe64cd4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe64cd8: RestoreReg r2
    //     0xe64cd8: ldr             x2, [SP], #8
    // 0xe64cdc: RestoreReg d0
    //     0xe64cdc: ldr             q0, [SP], #0x10
    // 0xe64ce0: b               #0xe64c0c
  }
  _ drawBox(/* No info */) {
    // ** addr: 0xe64ce4, size: 0x40
    // 0xe64ce4: EnterFrame
    //     0xe64ce4: stp             fp, lr, [SP, #-0x10]!
    //     0xe64ce8: mov             fp, SP
    // 0xe64cec: CheckStackOverflow
    //     0xe64cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64cf0: cmp             SP, x16
    //     0xe64cf4: b.ls            #0xe64d1c
    // 0xe64cf8: LoadField: d0 = r2->field_7
    //     0xe64cf8: ldur            d0, [x2, #7]
    // 0xe64cfc: LoadField: d1 = r2->field_f
    //     0xe64cfc: ldur            d1, [x2, #0xf]
    // 0xe64d00: ArrayLoad: d2 = r2[0]  ; List_8
    //     0xe64d00: ldur            d2, [x2, #0x17]
    // 0xe64d04: LoadField: d3 = r2->field_1f
    //     0xe64d04: ldur            d3, [x2, #0x1f]
    // 0xe64d08: r0 = drawRect()
    //     0xe64d08: bl              #0xe64800  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawRect
    // 0xe64d0c: r0 = Null
    //     0xe64d0c: mov             x0, NULL
    // 0xe64d10: LeaveFrame
    //     0xe64d10: mov             SP, fp
    //     0xe64d14: ldp             fp, lr, [SP], #0x10
    // 0xe64d18: ret
    //     0xe64d18: ret             
    // 0xe64d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64d20: b               #0xe64cf8
  }
  _ drawEllipse(/* No info */) {
    // ** addr: 0xe64d24, size: 0x160
    // 0xe64d24: EnterFrame
    //     0xe64d24: stp             fp, lr, [SP, #-0x10]!
    //     0xe64d28: mov             fp, SP
    // 0xe64d2c: AllocStack(0x60)
    //     0xe64d2c: sub             SP, SP, #0x60
    // 0xe64d30: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d4, fp-0x18 */, dynamic _ /* d1 => d5, fp-0x20 */, dynamic _ /* d2 => d2, fp-0x28 */, dynamic _ /* d3 => d3, fp-0x30 */)
    //     0xe64d30: mov             x0, x1
    //     0xe64d34: mov             v4.16b, v0.16b
    //     0xe64d38: mov             v5.16b, v1.16b
    //     0xe64d3c: stur            x1, [fp, #-8]
    //     0xe64d40: stur            d0, [fp, #-0x18]
    //     0xe64d44: stur            d1, [fp, #-0x20]
    //     0xe64d48: stur            d2, [fp, #-0x28]
    //     0xe64d4c: stur            d3, [fp, #-0x30]
    // 0xe64d50: CheckStackOverflow
    //     0xe64d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64d54: cmp             SP, x16
    //     0xe64d58: b.ls            #0xe64e7c
    // 0xe64d5c: fsub            d6, d5, d3
    // 0xe64d60: mov             x1, x0
    // 0xe64d64: mov             v0.16b, v4.16b
    // 0xe64d68: mov             v1.16b, v6.16b
    // 0xe64d6c: stur            d6, [fp, #-0x10]
    // 0xe64d70: r0 = moveTo()
    //     0xe64d70: bl              #0xac6914  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::moveTo
    // 0xe64d74: ldur            d6, [fp, #-0x28]
    // 0xe64d78: d0 = 0.551784
    //     0xe64d78: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e9c0] IMM: double(0.551784) from 0x3fe1a836eb4e9814
    //     0xe64d7c: ldr             d0, [x17, #0x9c0]
    // 0xe64d80: fmul            d7, d6, d0
    // 0xe64d84: ldur            d8, [fp, #-0x18]
    // 0xe64d88: stur            d7, [fp, #-0x58]
    // 0xe64d8c: fadd            d9, d8, d7
    // 0xe64d90: stur            d9, [fp, #-0x50]
    // 0xe64d94: fadd            d10, d8, d6
    // 0xe64d98: ldur            d11, [fp, #-0x30]
    // 0xe64d9c: stur            d10, [fp, #-0x48]
    // 0xe64da0: fmul            d12, d11, d0
    // 0xe64da4: ldur            d13, [fp, #-0x20]
    // 0xe64da8: stur            d12, [fp, #-0x40]
    // 0xe64dac: fsub            d14, d13, d12
    // 0xe64db0: ldur            x1, [fp, #-8]
    // 0xe64db4: mov             v0.16b, v9.16b
    // 0xe64db8: ldur            d1, [fp, #-0x10]
    // 0xe64dbc: mov             v2.16b, v10.16b
    // 0xe64dc0: mov             v3.16b, v14.16b
    // 0xe64dc4: mov             v4.16b, v10.16b
    // 0xe64dc8: mov             v5.16b, v13.16b
    // 0xe64dcc: stur            d14, [fp, #-0x38]
    // 0xe64dd0: r0 = curveTo()
    //     0xe64dd0: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xe64dd4: ldur            d6, [fp, #-0x20]
    // 0xe64dd8: ldur            d0, [fp, #-0x40]
    // 0xe64ddc: fadd            d7, d6, d0
    // 0xe64de0: ldur            d0, [fp, #-0x30]
    // 0xe64de4: stur            d7, [fp, #-0x60]
    // 0xe64de8: fadd            d8, d6, d0
    // 0xe64dec: ldur            x1, [fp, #-8]
    // 0xe64df0: ldur            d0, [fp, #-0x48]
    // 0xe64df4: mov             v1.16b, v7.16b
    // 0xe64df8: ldur            d2, [fp, #-0x50]
    // 0xe64dfc: mov             v3.16b, v8.16b
    // 0xe64e00: ldur            d4, [fp, #-0x18]
    // 0xe64e04: mov             v5.16b, v8.16b
    // 0xe64e08: stur            d8, [fp, #-0x40]
    // 0xe64e0c: r0 = curveTo()
    //     0xe64e0c: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xe64e10: ldur            d6, [fp, #-0x18]
    // 0xe64e14: ldur            d0, [fp, #-0x58]
    // 0xe64e18: fsub            d7, d6, d0
    // 0xe64e1c: ldur            d0, [fp, #-0x28]
    // 0xe64e20: stur            d7, [fp, #-0x48]
    // 0xe64e24: fsub            d8, d6, d0
    // 0xe64e28: ldur            x1, [fp, #-8]
    // 0xe64e2c: mov             v0.16b, v7.16b
    // 0xe64e30: ldur            d1, [fp, #-0x40]
    // 0xe64e34: mov             v2.16b, v8.16b
    // 0xe64e38: ldur            d3, [fp, #-0x60]
    // 0xe64e3c: mov             v4.16b, v8.16b
    // 0xe64e40: ldur            d5, [fp, #-0x20]
    // 0xe64e44: stur            d8, [fp, #-0x30]
    // 0xe64e48: r0 = curveTo()
    //     0xe64e48: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xe64e4c: ldur            x1, [fp, #-8]
    // 0xe64e50: ldur            d0, [fp, #-0x30]
    // 0xe64e54: ldur            d1, [fp, #-0x38]
    // 0xe64e58: ldur            d2, [fp, #-0x48]
    // 0xe64e5c: ldur            d3, [fp, #-0x10]
    // 0xe64e60: ldur            d4, [fp, #-0x18]
    // 0xe64e64: ldur            d5, [fp, #-0x10]
    // 0xe64e68: r0 = curveTo()
    //     0xe64e68: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xe64e6c: r0 = Null
    //     0xe64e6c: mov             x0, NULL
    // 0xe64e70: LeaveFrame
    //     0xe64e70: mov             SP, fp
    //     0xe64e74: ldp             fp, lr, [SP], #0x10
    // 0xe64e78: ret
    //     0xe64e78: ret             
    // 0xe64e7c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe64e7c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe64e80: b               #0xe64d5c
  }
  _ drawLine(/* No info */) {
    // ** addr: 0xe65930, size: 0x64
    // 0xe65930: EnterFrame
    //     0xe65930: stp             fp, lr, [SP, #-0x10]!
    //     0xe65934: mov             fp, SP
    // 0xe65938: AllocStack(0x18)
    //     0xe65938: sub             SP, SP, #0x18
    // 0xe6593c: SetupParameters(PdfGraphics this /* r1 => r0, fp-0x8 */, dynamic _ /* d2 => d3, fp-0x10 */, dynamic _ /* d3 => d2, fp-0x18 */)
    //     0xe6593c: mov             x0, x1
    //     0xe65940: stur            d2, [fp, #-0x10]
    //     0xe65944: mov             v31.16b, v3.16b
    //     0xe65948: mov             v3.16b, v2.16b
    //     0xe6594c: mov             v2.16b, v31.16b
    //     0xe65950: stur            x1, [fp, #-8]
    //     0xe65954: stur            d2, [fp, #-0x18]
    // 0xe65958: CheckStackOverflow
    //     0xe65958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6595c: cmp             SP, x16
    //     0xe65960: b.ls            #0xe6598c
    // 0xe65964: mov             x1, x0
    // 0xe65968: r0 = moveTo()
    //     0xe65968: bl              #0xac6914  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::moveTo
    // 0xe6596c: ldur            x1, [fp, #-8]
    // 0xe65970: ldur            d0, [fp, #-0x10]
    // 0xe65974: ldur            d1, [fp, #-0x18]
    // 0xe65978: r0 = lineTo()
    //     0xe65978: bl              #0xac46e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::lineTo
    // 0xe6597c: r0 = Null
    //     0xe6597c: mov             x0, NULL
    // 0xe65980: LeaveFrame
    //     0xe65980: mov             SP, fp
    //     0xe65984: ldp             fp, lr, [SP], #0x10
    // 0xe65988: ret
    //     0xe65988: ret             
    // 0xe6598c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe6598c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe65990: b               #0xe65964
  }
  _ setStrokeColor(/* No info */) {
    // ** addr: 0xe65994, size: 0x1b8
    // 0xe65994: EnterFrame
    //     0xe65994: stp             fp, lr, [SP, #-0x10]!
    //     0xe65998: mov             fp, SP
    // 0xe6599c: AllocStack(0x28)
    //     0xe6599c: sub             SP, SP, #0x28
    // 0xe659a0: r0 = 6
    //     0xe659a0: movz            x0, #0x6
    // 0xe659a4: mov             x3, x1
    // 0xe659a8: stur            x1, [fp, #-0x10]
    // 0xe659ac: CheckStackOverflow
    //     0xe659ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe659b0: cmp             SP, x16
    //     0xe659b4: b.ls            #0xe65aec
    // 0xe659b8: cmp             w2, NULL
    // 0xe659bc: b.eq            #0xe65af4
    // 0xe659c0: LoadField: d0 = r2->field_f
    //     0xe659c0: ldur            d0, [x2, #0xf]
    // 0xe659c4: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xe659c4: ldur            d1, [x2, #0x17]
    // 0xe659c8: stur            d1, [fp, #-0x28]
    // 0xe659cc: LoadField: d2 = r2->field_1f
    //     0xe659cc: ldur            d2, [x2, #0x1f]
    // 0xe659d0: stur            d2, [fp, #-0x20]
    // 0xe659d4: r4 = inline_Allocate_Double()
    //     0xe659d4: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xe659d8: add             x4, x4, #0x10
    //     0xe659dc: cmp             x1, x4
    //     0xe659e0: b.ls            #0xe65af8
    //     0xe659e4: str             x4, [THR, #0x50]  ; THR::top
    //     0xe659e8: sub             x4, x4, #0xf
    //     0xe659ec: movz            x1, #0xe15c
    //     0xe659f0: movk            x1, #0x3, lsl #16
    //     0xe659f4: stur            x1, [x4, #-1]
    // 0xe659f8: StoreField: r4->field_7 = d0
    //     0xe659f8: stur            d0, [x4, #7]
    // 0xe659fc: mov             x2, x0
    // 0xe65a00: stur            x4, [fp, #-8]
    // 0xe65a04: r1 = Null
    //     0xe65a04: mov             x1, NULL
    // 0xe65a08: r0 = AllocateArray()
    //     0xe65a08: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe65a0c: mov             x2, x0
    // 0xe65a10: ldur            x0, [fp, #-8]
    // 0xe65a14: stur            x2, [fp, #-0x18]
    // 0xe65a18: StoreField: r2->field_f = r0
    //     0xe65a18: stur            w0, [x2, #0xf]
    // 0xe65a1c: ldur            d0, [fp, #-0x28]
    // 0xe65a20: r0 = inline_Allocate_Double()
    //     0xe65a20: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe65a24: add             x0, x0, #0x10
    //     0xe65a28: cmp             x1, x0
    //     0xe65a2c: b.ls            #0xe65b1c
    //     0xe65a30: str             x0, [THR, #0x50]  ; THR::top
    //     0xe65a34: sub             x0, x0, #0xf
    //     0xe65a38: movz            x1, #0xe15c
    //     0xe65a3c: movk            x1, #0x3, lsl #16
    //     0xe65a40: stur            x1, [x0, #-1]
    // 0xe65a44: StoreField: r0->field_7 = d0
    //     0xe65a44: stur            d0, [x0, #7]
    // 0xe65a48: StoreField: r2->field_13 = r0
    //     0xe65a48: stur            w0, [x2, #0x13]
    // 0xe65a4c: ldur            d0, [fp, #-0x20]
    // 0xe65a50: r0 = inline_Allocate_Double()
    //     0xe65a50: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe65a54: add             x0, x0, #0x10
    //     0xe65a58: cmp             x1, x0
    //     0xe65a5c: b.ls            #0xe65b34
    //     0xe65a60: str             x0, [THR, #0x50]  ; THR::top
    //     0xe65a64: sub             x0, x0, #0xf
    //     0xe65a68: movz            x1, #0xe15c
    //     0xe65a6c: movk            x1, #0x3, lsl #16
    //     0xe65a70: stur            x1, [x0, #-1]
    // 0xe65a74: StoreField: r0->field_7 = d0
    //     0xe65a74: stur            d0, [x0, #7]
    // 0xe65a78: ArrayStore: r2[0] = r0  ; List_4
    //     0xe65a78: stur            w0, [x2, #0x17]
    // 0xe65a7c: r1 = <double>
    //     0xe65a7c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe65a80: r0 = AllocateGrowableArray()
    //     0xe65a80: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe65a84: mov             x1, x0
    // 0xe65a88: ldur            x0, [fp, #-0x18]
    // 0xe65a8c: stur            x1, [fp, #-8]
    // 0xe65a90: StoreField: r1->field_f = r0
    //     0xe65a90: stur            w0, [x1, #0xf]
    // 0xe65a94: r0 = 6
    //     0xe65a94: movz            x0, #0x6
    // 0xe65a98: StoreField: r1->field_b = r0
    //     0xe65a98: stur            w0, [x1, #0xb]
    // 0xe65a9c: r0 = PdfNumList()
    //     0xe65a9c: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe65aa0: mov             x1, x0
    // 0xe65aa4: ldur            x0, [fp, #-8]
    // 0xe65aa8: StoreField: r1->field_7 = r0
    //     0xe65aa8: stur            w0, [x1, #7]
    // 0xe65aac: ldur            x0, [fp, #-0x10]
    // 0xe65ab0: LoadField: r2 = r0->field_f
    //     0xe65ab0: ldur            w2, [x0, #0xf]
    // 0xe65ab4: DecompressPointer r2
    //     0xe65ab4: add             x2, x2, HEAP, lsl #32
    // 0xe65ab8: LoadField: r4 = r0->field_13
    //     0xe65ab8: ldur            w4, [x0, #0x13]
    // 0xe65abc: DecompressPointer r4
    //     0xe65abc: add             x4, x4, HEAP, lsl #32
    // 0xe65ac0: mov             x3, x4
    // 0xe65ac4: stur            x4, [fp, #-8]
    // 0xe65ac8: r0 = output()
    //     0xe65ac8: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe65acc: ldur            x1, [fp, #-8]
    // 0xe65ad0: r2 = " RG "
    //     0xe65ad0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dda0] " RG "
    //     0xe65ad4: ldr             x2, [x2, #0xda0]
    // 0xe65ad8: r0 = putString()
    //     0xe65ad8: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe65adc: r0 = Null
    //     0xe65adc: mov             x0, NULL
    // 0xe65ae0: LeaveFrame
    //     0xe65ae0: mov             SP, fp
    //     0xe65ae4: ldp             fp, lr, [SP], #0x10
    // 0xe65ae8: ret
    //     0xe65ae8: ret             
    // 0xe65aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe65af0: b               #0xe659b8
    // 0xe65af4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65af4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65af8: stp             q1, q2, [SP, #-0x20]!
    // 0xe65afc: SaveReg d0
    //     0xe65afc: str             q0, [SP, #-0x10]!
    // 0xe65b00: stp             x0, x3, [SP, #-0x10]!
    // 0xe65b04: r0 = AllocateDouble()
    //     0xe65b04: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe65b08: mov             x4, x0
    // 0xe65b0c: ldp             x0, x3, [SP], #0x10
    // 0xe65b10: RestoreReg d0
    //     0xe65b10: ldr             q0, [SP], #0x10
    // 0xe65b14: ldp             q1, q2, [SP], #0x20
    // 0xe65b18: b               #0xe659f8
    // 0xe65b1c: SaveReg d0
    //     0xe65b1c: str             q0, [SP, #-0x10]!
    // 0xe65b20: SaveReg r2
    //     0xe65b20: str             x2, [SP, #-8]!
    // 0xe65b24: r0 = AllocateDouble()
    //     0xe65b24: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe65b28: RestoreReg r2
    //     0xe65b28: ldr             x2, [SP], #8
    // 0xe65b2c: RestoreReg d0
    //     0xe65b2c: ldr             q0, [SP], #0x10
    // 0xe65b30: b               #0xe65a44
    // 0xe65b34: SaveReg d0
    //     0xe65b34: str             q0, [SP, #-0x10]!
    // 0xe65b38: SaveReg r2
    //     0xe65b38: str             x2, [SP, #-8]!
    // 0xe65b3c: r0 = AllocateDouble()
    //     0xe65b3c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe65b40: RestoreReg r2
    //     0xe65b40: ldr             x2, [SP], #8
    // 0xe65b44: RestoreReg d0
    //     0xe65b44: ldr             q0, [SP], #0x10
    // 0xe65b48: b               #0xe65a74
  }
  _ getTransform(/* No info */) {
    // ** addr: 0xe6daa4, size: 0x54
    // 0xe6daa4: EnterFrame
    //     0xe6daa4: stp             fp, lr, [SP, #-0x10]!
    //     0xe6daa8: mov             fp, SP
    // 0xe6daac: CheckStackOverflow
    //     0xe6daac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6dab0: cmp             SP, x16
    //     0xe6dab4: b.ls            #0xe6dae4
    // 0xe6dab8: LoadField: r0 = r1->field_7
    //     0xe6dab8: ldur            w0, [x1, #7]
    // 0xe6dabc: DecompressPointer r0
    //     0xe6dabc: add             x0, x0, HEAP, lsl #32
    // 0xe6dac0: r16 = Sentinel
    //     0xe6dac0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe6dac4: cmp             w0, w16
    // 0xe6dac8: b.eq            #0xe6daec
    // 0xe6dacc: LoadField: r1 = r0->field_7
    //     0xe6dacc: ldur            w1, [x0, #7]
    // 0xe6dad0: DecompressPointer r1
    //     0xe6dad0: add             x1, x1, HEAP, lsl #32
    // 0xe6dad4: r0 = clone()
    //     0xe6dad4: bl              #0x644fd0  ; [package:vector_math/vector_math_64.dart] Matrix4::clone
    // 0xe6dad8: LeaveFrame
    //     0xe6dad8: mov             SP, fp
    //     0xe6dadc: ldp             fp, lr, [SP], #0x10
    // 0xe6dae0: ret
    //     0xe6dae0: ret             
    // 0xe6dae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6dae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6dae8: b               #0xe6dab8
    // 0xe6daec: r9 = _context
    //     0xe6daec: add             x9, PP, #0x36, lsl #12  ; [pp+0x36720] Field <PdfGraphics._context@2225251352>: late (offset: 0x8)
    //     0xe6daf0: ldr             x9, [x9, #0x720]
    // 0xe6daf4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe6daf4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ setStrokePattern(/* No info */) {
    // ** addr: 0xea1560, size: 0xe0
    // 0xea1560: EnterFrame
    //     0xea1560: stp             fp, lr, [SP, #-0x10]!
    //     0xea1564: mov             fp, SP
    // 0xea1568: AllocStack(0x20)
    //     0xea1568: sub             SP, SP, #0x20
    // 0xea156c: SetupParameters(PdfGraphics this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea156c: mov             x4, x1
    //     0xea1570: mov             x3, x2
    //     0xea1574: stur            x1, [fp, #-8]
    //     0xea1578: stur            x2, [fp, #-0x10]
    // 0xea157c: CheckStackOverflow
    //     0xea157c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1580: cmp             SP, x16
    //     0xea1584: b.ls            #0xea1638
    // 0xea1588: LoadField: r1 = r4->field_f
    //     0xea1588: ldur            w1, [x4, #0xf]
    // 0xea158c: DecompressPointer r1
    //     0xea158c: add             x1, x1, HEAP, lsl #32
    // 0xea1590: r0 = LoadClassIdInstr(r1)
    //     0xea1590: ldur            x0, [x1, #-1]
    //     0xea1594: ubfx            x0, x0, #0xc, #0x14
    // 0xea1598: mov             x2, x3
    // 0xea159c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xea159c: sub             lr, x0, #0xfff
    //     0xea15a0: ldr             lr, [x21, lr, lsl #3]
    //     0xea15a4: blr             lr
    // 0xea15a8: ldur            x0, [fp, #-8]
    // 0xea15ac: LoadField: r3 = r0->field_13
    //     0xea15ac: ldur            w3, [x0, #0x13]
    // 0xea15b0: DecompressPointer r3
    //     0xea15b0: add             x3, x3, HEAP, lsl #32
    // 0xea15b4: stur            x3, [fp, #-0x18]
    // 0xea15b8: r1 = Null
    //     0xea15b8: mov             x1, NULL
    // 0xea15bc: r2 = 6
    //     0xea15bc: movz            x2, #0x6
    // 0xea15c0: r0 = AllocateArray()
    //     0xea15c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea15c4: stur            x0, [fp, #-8]
    // 0xea15c8: r16 = "/Pattern CS"
    //     0xea15c8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51238] "/Pattern CS"
    //     0xea15cc: ldr             x16, [x16, #0x238]
    // 0xea15d0: StoreField: r0->field_f = r16
    //     0xea15d0: stur            w16, [x0, #0xf]
    // 0xea15d4: ldur            x1, [fp, #-0x10]
    // 0xea15d8: r0 = name()
    //     0xea15d8: bl              #0xea100c  ; [package:pdf/src/pdf/obj/pattern.dart] PdfPattern::name
    // 0xea15dc: ldur            x1, [fp, #-8]
    // 0xea15e0: ArrayStore: r1[1] = r0  ; List_4
    //     0xea15e0: add             x25, x1, #0x13
    //     0xea15e4: str             w0, [x25]
    //     0xea15e8: tbz             w0, #0, #0xea1604
    //     0xea15ec: ldurb           w16, [x1, #-1]
    //     0xea15f0: ldurb           w17, [x0, #-1]
    //     0xea15f4: and             x16, x17, x16, lsr #2
    //     0xea15f8: tst             x16, HEAP, lsr #32
    //     0xea15fc: b.eq            #0xea1604
    //     0xea1600: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xea1604: ldur            x0, [fp, #-8]
    // 0xea1608: r16 = " SCN "
    //     0xea1608: add             x16, PP, #0x51, lsl #12  ; [pp+0x51240] " SCN "
    //     0xea160c: ldr             x16, [x16, #0x240]
    // 0xea1610: ArrayStore: r0[0] = r16  ; List_4
    //     0xea1610: stur            w16, [x0, #0x17]
    // 0xea1614: str             x0, [SP]
    // 0xea1618: r0 = _interpolate()
    //     0xea1618: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea161c: ldur            x1, [fp, #-0x18]
    // 0xea1620: mov             x2, x0
    // 0xea1624: r0 = putString()
    //     0xea1624: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xea1628: r0 = Null
    //     0xea1628: mov             x0, NULL
    // 0xea162c: LeaveFrame
    //     0xea162c: mov             SP, fp
    //     0xea1630: ldp             fp, lr, [SP], #0x10
    // 0xea1634: ret
    //     0xea1634: ret             
    // 0xea1638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1638: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea163c: b               #0xea1588
  }
  _ setFillPattern(/* No info */) {
    // ** addr: 0xea18b4, size: 0x11c
    // 0xea18b4: EnterFrame
    //     0xea18b4: stp             fp, lr, [SP, #-0x10]!
    //     0xea18b8: mov             fp, SP
    // 0xea18bc: AllocStack(0x20)
    //     0xea18bc: sub             SP, SP, #0x20
    // 0xea18c0: SetupParameters(PdfGraphics this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea18c0: mov             x4, x1
    //     0xea18c4: mov             x3, x2
    //     0xea18c8: stur            x1, [fp, #-8]
    //     0xea18cc: stur            x2, [fp, #-0x10]
    // 0xea18d0: CheckStackOverflow
    //     0xea18d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea18d4: cmp             SP, x16
    //     0xea18d8: b.ls            #0xea19c8
    // 0xea18dc: LoadField: r1 = r4->field_f
    //     0xea18dc: ldur            w1, [x4, #0xf]
    // 0xea18e0: DecompressPointer r1
    //     0xea18e0: add             x1, x1, HEAP, lsl #32
    // 0xea18e4: r0 = LoadClassIdInstr(r1)
    //     0xea18e4: ldur            x0, [x1, #-1]
    //     0xea18e8: ubfx            x0, x0, #0xc, #0x14
    // 0xea18ec: mov             x2, x3
    // 0xea18f0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xea18f0: sub             lr, x0, #0xfff
    //     0xea18f4: ldr             lr, [x21, lr, lsl #3]
    //     0xea18f8: blr             lr
    // 0xea18fc: ldur            x0, [fp, #-8]
    // 0xea1900: LoadField: r3 = r0->field_13
    //     0xea1900: ldur            w3, [x0, #0x13]
    // 0xea1904: DecompressPointer r3
    //     0xea1904: add             x3, x3, HEAP, lsl #32
    // 0xea1908: stur            x3, [fp, #-0x18]
    // 0xea190c: r1 = Null
    //     0xea190c: mov             x1, NULL
    // 0xea1910: r2 = 6
    //     0xea1910: movz            x2, #0x6
    // 0xea1914: r0 = AllocateArray()
    //     0xea1914: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1918: stur            x0, [fp, #-8]
    // 0xea191c: r16 = "/Pattern cs"
    //     0xea191c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51268] "/Pattern cs"
    //     0xea1920: ldr             x16, [x16, #0x268]
    // 0xea1924: StoreField: r0->field_f = r16
    //     0xea1924: stur            w16, [x0, #0xf]
    // 0xea1928: r1 = Null
    //     0xea1928: mov             x1, NULL
    // 0xea192c: r2 = 4
    //     0xea192c: movz            x2, #0x4
    // 0xea1930: r0 = AllocateArray()
    //     0xea1930: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1934: mov             x2, x0
    // 0xea1938: r16 = "/P"
    //     0xea1938: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea193c: ldr             x16, [x16, #0x248]
    // 0xea1940: StoreField: r2->field_f = r16
    //     0xea1940: stur            w16, [x2, #0xf]
    // 0xea1944: ldur            x0, [fp, #-0x10]
    // 0xea1948: LoadField: r3 = r0->field_b
    //     0xea1948: ldur            x3, [x0, #0xb]
    // 0xea194c: r0 = BoxInt64Instr(r3)
    //     0xea194c: sbfiz           x0, x3, #1, #0x1f
    //     0xea1950: cmp             x3, x0, asr #1
    //     0xea1954: b.eq            #0xea1960
    //     0xea1958: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea195c: stur            x3, [x0, #7]
    // 0xea1960: StoreField: r2->field_13 = r0
    //     0xea1960: stur            w0, [x2, #0x13]
    // 0xea1964: str             x2, [SP]
    // 0xea1968: r0 = _interpolate()
    //     0xea1968: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea196c: ldur            x1, [fp, #-8]
    // 0xea1970: ArrayStore: r1[1] = r0  ; List_4
    //     0xea1970: add             x25, x1, #0x13
    //     0xea1974: str             w0, [x25]
    //     0xea1978: tbz             w0, #0, #0xea1994
    //     0xea197c: ldurb           w16, [x1, #-1]
    //     0xea1980: ldurb           w17, [x0, #-1]
    //     0xea1984: and             x16, x17, x16, lsr #2
    //     0xea1988: tst             x16, HEAP, lsr #32
    //     0xea198c: b.eq            #0xea1994
    //     0xea1990: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xea1994: ldur            x0, [fp, #-8]
    // 0xea1998: r16 = " scn "
    //     0xea1998: add             x16, PP, #0x51, lsl #12  ; [pp+0x51270] " scn "
    //     0xea199c: ldr             x16, [x16, #0x270]
    // 0xea19a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xea19a0: stur            w16, [x0, #0x17]
    // 0xea19a4: str             x0, [SP]
    // 0xea19a8: r0 = _interpolate()
    //     0xea19a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea19ac: ldur            x1, [fp, #-0x18]
    // 0xea19b0: mov             x2, x0
    // 0xea19b4: r0 = putString()
    //     0xea19b4: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xea19b8: r0 = Null
    //     0xea19b8: mov             x0, NULL
    // 0xea19bc: LeaveFrame
    //     0xea19bc: mov             SP, fp
    //     0xea19c0: ldp             fp, lr, [SP], #0x10
    // 0xea19c4: ret
    //     0xea19c4: ret             
    // 0xea19c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea19c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea19cc: b               #0xea18dc
  }
  static _ shapeBoundingBox(/* No info */) {
    // ** addr: 0xeab400, size: 0x6c
    // 0xeab400: EnterFrame
    //     0xeab400: stp             fp, lr, [SP, #-0x10]!
    //     0xeab404: mov             fp, SP
    // 0xeab408: AllocStack(0x10)
    //     0xeab408: sub             SP, SP, #0x10
    // 0xeab40c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xeab40c: stur            x1, [fp, #-8]
    // 0xeab410: CheckStackOverflow
    //     0xeab410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab414: cmp             SP, x16
    //     0xeab418: b.ls            #0xeab464
    // 0xeab41c: r0 = _PathBBProxy()
    //     0xeab41c: bl              #0xeab4f4  ; Allocate_PathBBProxyStub -> _PathBBProxy (size=0x38)
    // 0xeab420: d0 = inf
    //     0xeab420: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xeab424: stur            x0, [fp, #-0x10]
    // 0xeab428: StoreField: r0->field_7 = d0
    //     0xeab428: stur            d0, [x0, #7]
    // 0xeab42c: StoreField: r0->field_f = d0
    //     0xeab42c: stur            d0, [x0, #0xf]
    // 0xeab430: d0 = -inf
    //     0xeab430: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xeab434: ArrayStore: r0[0] = d0  ; List_8
    //     0xeab434: stur            d0, [x0, #0x17]
    // 0xeab438: StoreField: r0->field_1f = d0
    //     0xeab438: stur            d0, [x0, #0x1f]
    // 0xeab43c: StoreField: r0->field_27 = rZR
    //     0xeab43c: stur            xzr, [x0, #0x27]
    // 0xeab440: StoreField: r0->field_2f = rZR
    //     0xeab440: stur            xzr, [x0, #0x2f]
    // 0xeab444: ldur            x1, [fp, #-8]
    // 0xeab448: mov             x2, x0
    // 0xeab44c: r0 = writeSvgPathDataToPath()
    //     0xeab44c: bl              #0xe4982c  ; [package:path_parsing/src/path_parsing.dart] ::writeSvgPathDataToPath
    // 0xeab450: ldur            x1, [fp, #-0x10]
    // 0xeab454: r0 = box()
    //     0xeab454: bl              #0xeab46c  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::box
    // 0xeab458: LeaveFrame
    //     0xeab458: mov             SP, fp
    //     0xeab45c: ldp             fp, lr, [SP], #0x10
    // 0xeab460: ret
    //     0xeab460: ret             
    // 0xeab464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeab464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeab468: b               #0xeab41c
  }
}

// class id: 864, size: 0xc, field offset: 0x8
//   const constructor, 
class _PdfGraphicsContext extends Object {

  _ copy(/* No info */) {
    // ** addr: 0xe47a80, size: 0x4c
    // 0xe47a80: EnterFrame
    //     0xe47a80: stp             fp, lr, [SP, #-0x10]!
    //     0xe47a84: mov             fp, SP
    // 0xe47a88: AllocStack(0x8)
    //     0xe47a88: sub             SP, SP, #8
    // 0xe47a8c: CheckStackOverflow
    //     0xe47a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47a90: cmp             SP, x16
    //     0xe47a94: b.ls            #0xe47ac4
    // 0xe47a98: LoadField: r0 = r1->field_7
    //     0xe47a98: ldur            w0, [x1, #7]
    // 0xe47a9c: DecompressPointer r0
    //     0xe47a9c: add             x0, x0, HEAP, lsl #32
    // 0xe47aa0: mov             x1, x0
    // 0xe47aa4: r0 = clone()
    //     0xe47aa4: bl              #0x644fd0  ; [package:vector_math/vector_math_64.dart] Matrix4::clone
    // 0xe47aa8: stur            x0, [fp, #-8]
    // 0xe47aac: r0 = _PdfGraphicsContext()
    //     0xe47aac: bl              #0xe46f14  ; Allocate_PdfGraphicsContextStub -> _PdfGraphicsContext (size=0xc)
    // 0xe47ab0: ldur            x1, [fp, #-8]
    // 0xe47ab4: StoreField: r0->field_7 = r1
    //     0xe47ab4: stur            w1, [x0, #7]
    // 0xe47ab8: LeaveFrame
    //     0xe47ab8: mov             SP, fp
    //     0xe47abc: ldp             fp, lr, [SP], #0x10
    // 0xe47ac0: ret
    //     0xe47ac0: ret             
    // 0xe47ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47ac8: b               #0xe47a98
  }
}

// class id: 934, size: 0x38, field offset: 0x8
class _PathBBProxy extends PathProxy {

  _ _updateMinMax(/* No info */) {
    // ** addr: 0xac6a60, size: 0x168
    // 0xac6a60: LoadField: d2 = r1->field_7
    //     0xac6a60: ldur            d2, [x1, #7]
    // 0xac6a64: fcmp            d2, d0
    // 0xac6a68: b.le            #0xac6a78
    // 0xac6a6c: mov             v2.16b, v0.16b
    // 0xac6a70: d3 = 0.000000
    //     0xac6a70: eor             v3.16b, v3.16b, v3.16b
    // 0xac6a74: b               #0xac6ad4
    // 0xac6a78: fcmp            d0, d2
    // 0xac6a7c: b.le            #0xac6a88
    // 0xac6a80: d3 = 0.000000
    //     0xac6a80: eor             v3.16b, v3.16b, v3.16b
    // 0xac6a84: b               #0xac6ad4
    // 0xac6a88: d3 = 0.000000
    //     0xac6a88: eor             v3.16b, v3.16b, v3.16b
    // 0xac6a8c: fcmp            d2, d3
    // 0xac6a90: b.ne            #0xac6aa8
    // 0xac6a94: fadd            d4, d2, d0
    // 0xac6a98: fmul            d5, d4, d2
    // 0xac6a9c: fmul            d4, d5, d0
    // 0xac6aa0: mov             v2.16b, v4.16b
    // 0xac6aa4: b               #0xac6ad4
    // 0xac6aa8: fcmp            d2, d3
    // 0xac6aac: b.ne            #0xac6ac8
    // 0xac6ab0: fcmp            d0, #0.0
    // 0xac6ab4: b.vs            #0xac6ac8
    // 0xac6ab8: b.ne            #0xac6ac4
    // 0xac6abc: r2 = 0.000000
    //     0xac6abc: fmov            x2, d0
    // 0xac6ac0: cmp             x2, #0
    // 0xac6ac4: b.lt            #0xac6ad0
    // 0xac6ac8: fcmp            d0, d0
    // 0xac6acc: b.vc            #0xac6ad4
    // 0xac6ad0: mov             v2.16b, v0.16b
    // 0xac6ad4: StoreField: r1->field_7 = d2
    //     0xac6ad4: stur            d2, [x1, #7]
    // 0xac6ad8: LoadField: d2 = r1->field_f
    //     0xac6ad8: ldur            d2, [x1, #0xf]
    // 0xac6adc: fcmp            d2, d1
    // 0xac6ae0: b.le            #0xac6aec
    // 0xac6ae4: mov             v2.16b, v1.16b
    // 0xac6ae8: b               #0xac6b3c
    // 0xac6aec: fcmp            d1, d2
    // 0xac6af0: b.gt            #0xac6b3c
    // 0xac6af4: fcmp            d2, d3
    // 0xac6af8: b.ne            #0xac6b10
    // 0xac6afc: fadd            d4, d2, d1
    // 0xac6b00: fmul            d5, d4, d2
    // 0xac6b04: fmul            d4, d5, d1
    // 0xac6b08: mov             v2.16b, v4.16b
    // 0xac6b0c: b               #0xac6b3c
    // 0xac6b10: fcmp            d2, d3
    // 0xac6b14: b.ne            #0xac6b30
    // 0xac6b18: fcmp            d1, #0.0
    // 0xac6b1c: b.vs            #0xac6b30
    // 0xac6b20: b.ne            #0xac6b2c
    // 0xac6b24: r2 = 0.000000
    //     0xac6b24: fmov            x2, d1
    // 0xac6b28: cmp             x2, #0
    // 0xac6b2c: b.lt            #0xac6b38
    // 0xac6b30: fcmp            d1, d1
    // 0xac6b34: b.vc            #0xac6b3c
    // 0xac6b38: mov             v2.16b, v1.16b
    // 0xac6b3c: StoreField: r1->field_f = d2
    //     0xac6b3c: stur            d2, [x1, #0xf]
    // 0xac6b40: ArrayLoad: d2 = r1[0]  ; List_8
    //     0xac6b40: ldur            d2, [x1, #0x17]
    // 0xac6b44: fcmp            d2, d0
    // 0xac6b48: b.le            #0xac6b54
    // 0xac6b4c: mov             v0.16b, v2.16b
    // 0xac6b50: b               #0xac6b7c
    // 0xac6b54: fcmp            d0, d2
    // 0xac6b58: b.gt            #0xac6b7c
    // 0xac6b5c: fcmp            d2, d3
    // 0xac6b60: b.ne            #0xac6b70
    // 0xac6b64: fadd            d4, d2, d0
    // 0xac6b68: mov             v0.16b, v4.16b
    // 0xac6b6c: b               #0xac6b7c
    // 0xac6b70: fcmp            d0, d0
    // 0xac6b74: b.vs            #0xac6b7c
    // 0xac6b78: mov             v0.16b, v2.16b
    // 0xac6b7c: ArrayStore: r1[0] = d0  ; List_8
    //     0xac6b7c: stur            d0, [x1, #0x17]
    // 0xac6b80: LoadField: d0 = r1->field_1f
    //     0xac6b80: ldur            d0, [x1, #0x1f]
    // 0xac6b84: fcmp            d0, d1
    // 0xac6b88: b.gt            #0xac6bbc
    // 0xac6b8c: fcmp            d1, d0
    // 0xac6b90: b.le            #0xac6b9c
    // 0xac6b94: mov             v0.16b, v1.16b
    // 0xac6b98: b               #0xac6bbc
    // 0xac6b9c: fcmp            d0, d3
    // 0xac6ba0: b.ne            #0xac6bb0
    // 0xac6ba4: fadd            d2, d0, d1
    // 0xac6ba8: mov             v0.16b, v2.16b
    // 0xac6bac: b               #0xac6bbc
    // 0xac6bb0: fcmp            d1, d1
    // 0xac6bb4: b.vc            #0xac6bbc
    // 0xac6bb8: mov             v0.16b, v1.16b
    // 0xac6bbc: StoreField: r1->field_1f = d0
    //     0xac6bbc: stur            d0, [x1, #0x1f]
    // 0xac6bc0: r0 = Null
    //     0xac6bc0: mov             x0, NULL
    // 0xac6bc4: ret
    //     0xac6bc4: ret             
  }
  _ cubicTo(/* No info */) {
    // ** addr: 0xe8db44, size: 0x818
    // 0xe8db44: EnterFrame
    //     0xe8db44: stp             fp, lr, [SP, #-0x10]!
    //     0xe8db48: mov             fp, SP
    // 0xe8db4c: AllocStack(0xd0)
    //     0xe8db4c: sub             SP, SP, #0xd0
    // 0xe8db50: SetupParameters(_PathBBProxy this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d5, fp-0x28 */, dynamic _ /* d1 => d4, fp-0x30 */, dynamic _ /* d2 => d2, fp-0x38 */, dynamic _ /* d3 => d3, fp-0x40 */, dynamic _ /* d4 => d0, fp-0x48 */, dynamic _ /* d5 => d1, fp-0x50 */)
    //     0xe8db50: mov             x0, x1
    //     0xe8db54: stur            d0, [fp, #-0x28]
    //     0xe8db58: mov             v31.16b, v4.16b
    //     0xe8db5c: mov             v4.16b, v0.16b
    //     0xe8db60: mov             v0.16b, v31.16b
    //     0xe8db64: mov             v31.16b, v1.16b
    //     0xe8db68: mov             v1.16b, v4.16b
    //     0xe8db6c: mov             v4.16b, v31.16b
    //     0xe8db70: mov             v31.16b, v5.16b
    //     0xe8db74: mov             v5.16b, v1.16b
    //     0xe8db78: mov             v1.16b, v31.16b
    //     0xe8db7c: stur            x1, [fp, #-8]
    //     0xe8db80: stur            d4, [fp, #-0x30]
    //     0xe8db84: stur            d2, [fp, #-0x38]
    //     0xe8db88: stur            d3, [fp, #-0x40]
    //     0xe8db8c: stur            d0, [fp, #-0x48]
    //     0xe8db90: stur            d1, [fp, #-0x50]
    // 0xe8db94: CheckStackOverflow
    //     0xe8db94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8db98: cmp             SP, x16
    //     0xe8db9c: b.ls            #0xe8e2c4
    // 0xe8dba0: r1 = <double>
    //     0xe8dba0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe8dba4: r2 = 0
    //     0xe8dba4: movz            x2, #0
    // 0xe8dba8: r0 = _GrowableList()
    //     0xe8dba8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8dbac: ldur            d1, [fp, #-0x30]
    // 0xe8dbb0: d0 = 12.000000
    //     0xe8dbb0: fmov            d0, #12.00000000
    // 0xe8dbb4: stur            x0, [fp, #-0x20]
    // 0xe8dbb8: fmul            d2, d1, d0
    // 0xe8dbbc: ldur            d4, [fp, #-0x40]
    // 0xe8dbc0: stur            d2, [fp, #-0xb8]
    // 0xe8dbc4: d3 = 6.000000
    //     0xe8dbc4: fmov            d3, #6.00000000
    // 0xe8dbc8: fmul            d5, d4, d3
    // 0xe8dbcc: stur            d5, [fp, #-0xb0]
    // 0xe8dbd0: d6 = 9.000000
    //     0xe8dbd0: fmov            d6, #9.00000000
    // 0xe8dbd4: fmul            d7, d1, d6
    // 0xe8dbd8: stur            d7, [fp, #-0xa8]
    // 0xe8dbdc: fmul            d8, d4, d6
    // 0xe8dbe0: ldur            d10, [fp, #-0x50]
    // 0xe8dbe4: stur            d8, [fp, #-0xa0]
    // 0xe8dbe8: d9 = 3.000000
    //     0xe8dbe8: fmov            d9, #3.00000000
    // 0xe8dbec: fmul            d11, d10, d9
    // 0xe8dbf0: stur            d11, [fp, #-0x98]
    // 0xe8dbf4: fmul            d12, d1, d9
    // 0xe8dbf8: ldur            d13, [fp, #-0x28]
    // 0xe8dbfc: stur            d12, [fp, #-0x90]
    // 0xe8dc00: fmul            d14, d13, d0
    // 0xe8dc04: ldur            d0, [fp, #-0x38]
    // 0xe8dc08: stur            d14, [fp, #-0x88]
    // 0xe8dc0c: fmul            d15, d0, d3
    // 0xe8dc10: stur            d15, [fp, #-0x80]
    // 0xe8dc14: fmul            d16, d13, d6
    // 0xe8dc18: stur            d16, [fp, #-0x78]
    // 0xe8dc1c: fmul            d17, d0, d6
    // 0xe8dc20: ldur            d6, [fp, #-0x48]
    // 0xe8dc24: stur            d17, [fp, #-0x70]
    // 0xe8dc28: fmul            d18, d6, d9
    // 0xe8dc2c: stur            d18, [fp, #-0x68]
    // 0xe8dc30: fmul            d19, d13, d9
    // 0xe8dc34: stur            d19, [fp, #-0x60]
    // 0xe8dc38: r3 = 0
    //     0xe8dc38: movz            x3, #0
    // 0xe8dc3c: ldur            x2, [fp, #-8]
    // 0xe8dc40: d23 = -3.000000
    //     0xe8dc40: fmov            d23, #-3.00000000
    // 0xe8dc44: d22 = 0.000000
    //     0xe8dc44: eor             v22.16b, v22.16b, v22.16b
    // 0xe8dc48: d21 = 1.000000
    //     0xe8dc48: fmov            d21, #1.00000000
    // 0xe8dc4c: d20 = 0.000000
    //     0xe8dc4c: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5b178] IMM: double(1e-12) from 0x3d719799812dea11
    //     0xe8dc50: ldr             d20, [x17, #0x178]
    // 0xe8dc54: stur            x3, [fp, #-0x18]
    // 0xe8dc58: CheckStackOverflow
    //     0xe8dc58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8dc5c: cmp             SP, x16
    //     0xe8dc60: b.ls            #0xe8e2cc
    // 0xe8dc64: cmp             x3, #2
    // 0xe8dc68: b.ge            #0xe8e158
    // 0xe8dc6c: cbnz            x3, #0xe8dca0
    // 0xe8dc70: LoadField: d24 = r2->field_27
    //     0xe8dc70: ldur            d24, [x2, #0x27]
    // 0xe8dc74: fmul            d25, d24, d3
    // 0xe8dc78: fsub            d26, d25, d14
    // 0xe8dc7c: fadd            d25, d26, d15
    // 0xe8dc80: fmul            d26, d24, d23
    // 0xe8dc84: fadd            d27, d26, d16
    // 0xe8dc88: fsub            d26, d27, d17
    // 0xe8dc8c: fadd            d27, d26, d18
    // 0xe8dc90: fmul            d26, d24, d9
    // 0xe8dc94: fsub            d24, d19, d26
    // 0xe8dc98: mov             v26.16b, v27.16b
    // 0xe8dc9c: b               #0xe8dccc
    // 0xe8dca0: LoadField: d24 = r2->field_2f
    //     0xe8dca0: ldur            d24, [x2, #0x2f]
    // 0xe8dca4: fmul            d25, d24, d3
    // 0xe8dca8: fsub            d26, d25, d2
    // 0xe8dcac: fadd            d25, d26, d5
    // 0xe8dcb0: fmul            d26, d24, d23
    // 0xe8dcb4: fadd            d27, d26, d7
    // 0xe8dcb8: fsub            d26, d27, d8
    // 0xe8dcbc: fadd            d27, d26, d11
    // 0xe8dcc0: fmul            d26, d24, d9
    // 0xe8dcc4: fsub            d24, d12, d26
    // 0xe8dcc8: mov             v26.16b, v27.16b
    // 0xe8dccc: fcmp            d26, d22
    // 0xe8dcd0: b.ne            #0xe8dce8
    // 0xe8dcd4: fcmp            d20, d22
    // 0xe8dcd8: b.gt            #0xe8dd0c
    // 0xe8dcdc: mov             x2, x0
    // 0xe8dce0: mov             v1.16b, v22.16b
    // 0xe8dce4: b               #0xe8de24
    // 0xe8dce8: fcmp            d22, d26
    // 0xe8dcec: b.le            #0xe8dd04
    // 0xe8dcf0: fneg            d27, d26
    // 0xe8dcf4: fcmp            d20, d27
    // 0xe8dcf8: b.gt            #0xe8dd0c
    // 0xe8dcfc: mov             x2, x0
    // 0xe8dd00: b               #0xe8de20
    // 0xe8dd04: fcmp            d20, d26
    // 0xe8dd08: b.le            #0xe8de1c
    // 0xe8dd0c: fcmp            d25, d22
    // 0xe8dd10: b.ne            #0xe8dd20
    // 0xe8dd14: fcmp            d20, d22
    // 0xe8dd18: b.le            #0xe8dd4c
    // 0xe8dd1c: b               #0xe8dd40
    // 0xe8dd20: fcmp            d22, d25
    // 0xe8dd24: b.le            #0xe8dd38
    // 0xe8dd28: fneg            d26, d25
    // 0xe8dd2c: fcmp            d20, d26
    // 0xe8dd30: b.le            #0xe8dd4c
    // 0xe8dd34: b               #0xe8dd40
    // 0xe8dd38: fcmp            d20, d25
    // 0xe8dd3c: b.le            #0xe8dd4c
    // 0xe8dd40: mov             x2, x0
    // 0xe8dd44: mov             x0, x3
    // 0xe8dd48: b               #0xe8e0fc
    // 0xe8dd4c: fneg            d26, d24
    // 0xe8dd50: fdiv            d24, d26, d25
    // 0xe8dd54: stur            d24, [fp, #-0x58]
    // 0xe8dd58: fcmp            d24, d22
    // 0xe8dd5c: b.le            #0xe8de10
    // 0xe8dd60: fcmp            d21, d24
    // 0xe8dd64: b.le            #0xe8de08
    // 0xe8dd68: LoadField: r1 = r0->field_b
    //     0xe8dd68: ldur            w1, [x0, #0xb]
    // 0xe8dd6c: LoadField: r4 = r0->field_f
    //     0xe8dd6c: ldur            w4, [x0, #0xf]
    // 0xe8dd70: DecompressPointer r4
    //     0xe8dd70: add             x4, x4, HEAP, lsl #32
    // 0xe8dd74: LoadField: r5 = r4->field_b
    //     0xe8dd74: ldur            w5, [x4, #0xb]
    // 0xe8dd78: r4 = LoadInt32Instr(r1)
    //     0xe8dd78: sbfx            x4, x1, #1, #0x1f
    // 0xe8dd7c: stur            x4, [fp, #-0x10]
    // 0xe8dd80: r1 = LoadInt32Instr(r5)
    //     0xe8dd80: sbfx            x1, x5, #1, #0x1f
    // 0xe8dd84: cmp             x4, x1
    // 0xe8dd88: b.ne            #0xe8dd94
    // 0xe8dd8c: mov             x1, x0
    // 0xe8dd90: r0 = _growToNextCapacity()
    //     0xe8dd90: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8dd94: ldur            x2, [fp, #-0x20]
    // 0xe8dd98: ldur            d0, [fp, #-0x58]
    // 0xe8dd9c: ldur            x3, [fp, #-0x10]
    // 0xe8dda0: add             x0, x3, #1
    // 0xe8dda4: lsl             x1, x0, #1
    // 0xe8dda8: StoreField: r2->field_b = r1
    //     0xe8dda8: stur            w1, [x2, #0xb]
    // 0xe8ddac: LoadField: r1 = r2->field_f
    //     0xe8ddac: ldur            w1, [x2, #0xf]
    // 0xe8ddb0: DecompressPointer r1
    //     0xe8ddb0: add             x1, x1, HEAP, lsl #32
    // 0xe8ddb4: r0 = inline_Allocate_Double()
    //     0xe8ddb4: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe8ddb8: add             x0, x0, #0x10
    //     0xe8ddbc: cmp             x4, x0
    //     0xe8ddc0: b.ls            #0xe8e2d4
    //     0xe8ddc4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8ddc8: sub             x0, x0, #0xf
    //     0xe8ddcc: movz            x4, #0xe15c
    //     0xe8ddd0: movk            x4, #0x3, lsl #16
    //     0xe8ddd4: stur            x4, [x0, #-1]
    // 0xe8ddd8: StoreField: r0->field_7 = d0
    //     0xe8ddd8: stur            d0, [x0, #7]
    // 0xe8dddc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8dddc: add             x25, x1, x3, lsl #2
    //     0xe8dde0: add             x25, x25, #0xf
    //     0xe8dde4: str             w0, [x25]
    //     0xe8dde8: tbz             w0, #0, #0xe8de04
    //     0xe8ddec: ldurb           w16, [x1, #-1]
    //     0xe8ddf0: ldurb           w17, [x0, #-1]
    //     0xe8ddf4: and             x16, x17, x16, lsr #2
    //     0xe8ddf8: tst             x16, HEAP, lsr #32
    //     0xe8ddfc: b.eq            #0xe8de04
    //     0xe8de00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8de04: b               #0xe8de14
    // 0xe8de08: mov             x2, x0
    // 0xe8de0c: b               #0xe8de14
    // 0xe8de10: mov             x2, x0
    // 0xe8de14: ldur            x0, [fp, #-0x18]
    // 0xe8de18: b               #0xe8e0fc
    // 0xe8de1c: mov             x2, x0
    // 0xe8de20: d1 = 0.000000
    //     0xe8de20: eor             v1.16b, v1.16b, v1.16b
    // 0xe8de24: d0 = 4.000000
    //     0xe8de24: fmov            d0, #4.00000000
    // 0xe8de28: fmul            d2, d25, d25
    // 0xe8de2c: fmul            d3, d24, d0
    // 0xe8de30: fmul            d4, d3, d26
    // 0xe8de34: fsub            d3, d2, d4
    // 0xe8de38: fcmp            d1, d3
    // 0xe8de3c: b.le            #0xe8df50
    // 0xe8de40: fcmp            d3, d1
    // 0xe8de44: b.ne            #0xe8de5c
    // 0xe8de48: d2 = 0.000000
    //     0xe8de48: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5b178] IMM: double(1e-12) from 0x3d719799812dea11
    //     0xe8de4c: ldr             d2, [x17, #0x178]
    // 0xe8de50: fcmp            d2, d1
    // 0xe8de54: b.le            #0xe8df48
    // 0xe8de58: b               #0xe8de84
    // 0xe8de5c: d2 = 0.000000
    //     0xe8de5c: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5b178] IMM: double(1e-12) from 0x3d719799812dea11
    //     0xe8de60: ldr             d2, [x17, #0x178]
    // 0xe8de64: fcmp            d1, d3
    // 0xe8de68: b.le            #0xe8de7c
    // 0xe8de6c: fneg            d4, d3
    // 0xe8de70: fcmp            d2, d4
    // 0xe8de74: b.le            #0xe8df48
    // 0xe8de78: b               #0xe8de84
    // 0xe8de7c: fcmp            d2, d3
    // 0xe8de80: b.le            #0xe8df48
    // 0xe8de84: d3 = 2.000000
    //     0xe8de84: fmov            d3, #2.00000000
    // 0xe8de88: fneg            d4, d25
    // 0xe8de8c: fmul            d5, d26, d3
    // 0xe8de90: fdiv            d6, d4, d5
    // 0xe8de94: stur            d6, [fp, #-0x58]
    // 0xe8de98: fcmp            d6, d1
    // 0xe8de9c: b.le            #0xe8df48
    // 0xe8dea0: d4 = 1.000000
    //     0xe8dea0: fmov            d4, #1.00000000
    // 0xe8dea4: fcmp            d4, d6
    // 0xe8dea8: b.le            #0xe8df48
    // 0xe8deac: LoadField: r0 = r2->field_b
    //     0xe8deac: ldur            w0, [x2, #0xb]
    // 0xe8deb0: LoadField: r1 = r2->field_f
    //     0xe8deb0: ldur            w1, [x2, #0xf]
    // 0xe8deb4: DecompressPointer r1
    //     0xe8deb4: add             x1, x1, HEAP, lsl #32
    // 0xe8deb8: LoadField: r3 = r1->field_b
    //     0xe8deb8: ldur            w3, [x1, #0xb]
    // 0xe8debc: r4 = LoadInt32Instr(r0)
    //     0xe8debc: sbfx            x4, x0, #1, #0x1f
    // 0xe8dec0: stur            x4, [fp, #-0x10]
    // 0xe8dec4: r0 = LoadInt32Instr(r3)
    //     0xe8dec4: sbfx            x0, x3, #1, #0x1f
    // 0xe8dec8: cmp             x4, x0
    // 0xe8decc: b.ne            #0xe8ded8
    // 0xe8ded0: mov             x1, x2
    // 0xe8ded4: r0 = _growToNextCapacity()
    //     0xe8ded4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8ded8: ldur            x2, [fp, #-0x20]
    // 0xe8dedc: ldur            d0, [fp, #-0x58]
    // 0xe8dee0: ldur            x3, [fp, #-0x10]
    // 0xe8dee4: add             x0, x3, #1
    // 0xe8dee8: lsl             x1, x0, #1
    // 0xe8deec: StoreField: r2->field_b = r1
    //     0xe8deec: stur            w1, [x2, #0xb]
    // 0xe8def0: LoadField: r1 = r2->field_f
    //     0xe8def0: ldur            w1, [x2, #0xf]
    // 0xe8def4: DecompressPointer r1
    //     0xe8def4: add             x1, x1, HEAP, lsl #32
    // 0xe8def8: r0 = inline_Allocate_Double()
    //     0xe8def8: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe8defc: add             x0, x0, #0x10
    //     0xe8df00: cmp             x4, x0
    //     0xe8df04: b.ls            #0xe8e2f4
    //     0xe8df08: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8df0c: sub             x0, x0, #0xf
    //     0xe8df10: movz            x4, #0xe15c
    //     0xe8df14: movk            x4, #0x3, lsl #16
    //     0xe8df18: stur            x4, [x0, #-1]
    // 0xe8df1c: StoreField: r0->field_7 = d0
    //     0xe8df1c: stur            d0, [x0, #7]
    // 0xe8df20: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8df20: add             x25, x1, x3, lsl #2
    //     0xe8df24: add             x25, x25, #0xf
    //     0xe8df28: str             w0, [x25]
    //     0xe8df2c: tbz             w0, #0, #0xe8df48
    //     0xe8df30: ldurb           w16, [x1, #-1]
    //     0xe8df34: ldurb           w17, [x0, #-1]
    //     0xe8df38: and             x16, x17, x16, lsr #2
    //     0xe8df3c: tst             x16, HEAP, lsr #32
    //     0xe8df40: b.eq            #0xe8df48
    //     0xe8df44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8df48: ldur            x0, [fp, #-0x18]
    // 0xe8df4c: b               #0xe8e0fc
    // 0xe8df50: mov             v0.16b, v1.16b
    // 0xe8df54: d1 = 2.000000
    //     0xe8df54: fmov            d1, #2.00000000
    // 0xe8df58: fsqrt           d2, d3
    // 0xe8df5c: stur            d2, [fp, #-0xd0]
    // 0xe8df60: fneg            d3, d25
    // 0xe8df64: stur            d3, [fp, #-0xc8]
    // 0xe8df68: fadd            d4, d3, d2
    // 0xe8df6c: fmul            d5, d26, d1
    // 0xe8df70: stur            d5, [fp, #-0xc0]
    // 0xe8df74: fdiv            d6, d4, d5
    // 0xe8df78: stur            d6, [fp, #-0x58]
    // 0xe8df7c: fcmp            d6, d0
    // 0xe8df80: b.le            #0xe8e02c
    // 0xe8df84: d4 = 1.000000
    //     0xe8df84: fmov            d4, #1.00000000
    // 0xe8df88: fcmp            d4, d6
    // 0xe8df8c: b.le            #0xe8e02c
    // 0xe8df90: LoadField: r0 = r2->field_b
    //     0xe8df90: ldur            w0, [x2, #0xb]
    // 0xe8df94: LoadField: r1 = r2->field_f
    //     0xe8df94: ldur            w1, [x2, #0xf]
    // 0xe8df98: DecompressPointer r1
    //     0xe8df98: add             x1, x1, HEAP, lsl #32
    // 0xe8df9c: LoadField: r3 = r1->field_b
    //     0xe8df9c: ldur            w3, [x1, #0xb]
    // 0xe8dfa0: r4 = LoadInt32Instr(r0)
    //     0xe8dfa0: sbfx            x4, x0, #1, #0x1f
    // 0xe8dfa4: stur            x4, [fp, #-0x10]
    // 0xe8dfa8: r0 = LoadInt32Instr(r3)
    //     0xe8dfa8: sbfx            x0, x3, #1, #0x1f
    // 0xe8dfac: cmp             x4, x0
    // 0xe8dfb0: b.ne            #0xe8dfbc
    // 0xe8dfb4: mov             x1, x2
    // 0xe8dfb8: r0 = _growToNextCapacity()
    //     0xe8dfb8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8dfbc: ldur            x2, [fp, #-0x20]
    // 0xe8dfc0: ldur            d0, [fp, #-0x58]
    // 0xe8dfc4: ldur            x3, [fp, #-0x10]
    // 0xe8dfc8: add             x0, x3, #1
    // 0xe8dfcc: lsl             x1, x0, #1
    // 0xe8dfd0: StoreField: r2->field_b = r1
    //     0xe8dfd0: stur            w1, [x2, #0xb]
    // 0xe8dfd4: LoadField: r1 = r2->field_f
    //     0xe8dfd4: ldur            w1, [x2, #0xf]
    // 0xe8dfd8: DecompressPointer r1
    //     0xe8dfd8: add             x1, x1, HEAP, lsl #32
    // 0xe8dfdc: r0 = inline_Allocate_Double()
    //     0xe8dfdc: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe8dfe0: add             x0, x0, #0x10
    //     0xe8dfe4: cmp             x4, x0
    //     0xe8dfe8: b.ls            #0xe8e314
    //     0xe8dfec: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8dff0: sub             x0, x0, #0xf
    //     0xe8dff4: movz            x4, #0xe15c
    //     0xe8dff8: movk            x4, #0x3, lsl #16
    //     0xe8dffc: stur            x4, [x0, #-1]
    // 0xe8e000: StoreField: r0->field_7 = d0
    //     0xe8e000: stur            d0, [x0, #7]
    // 0xe8e004: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8e004: add             x25, x1, x3, lsl #2
    //     0xe8e008: add             x25, x25, #0xf
    //     0xe8e00c: str             w0, [x25]
    //     0xe8e010: tbz             w0, #0, #0xe8e02c
    //     0xe8e014: ldurb           w16, [x1, #-1]
    //     0xe8e018: ldurb           w17, [x0, #-1]
    //     0xe8e01c: and             x16, x17, x16, lsr #2
    //     0xe8e020: tst             x16, HEAP, lsr #32
    //     0xe8e024: b.eq            #0xe8e02c
    //     0xe8e028: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8e02c: ldur            d2, [fp, #-0xc8]
    // 0xe8e030: ldur            d3, [fp, #-0xc0]
    // 0xe8e034: ldur            d1, [fp, #-0xd0]
    // 0xe8e038: d0 = 0.000000
    //     0xe8e038: eor             v0.16b, v0.16b, v0.16b
    // 0xe8e03c: fsub            d4, d2, d1
    // 0xe8e040: fdiv            d1, d4, d3
    // 0xe8e044: stur            d1, [fp, #-0x58]
    // 0xe8e048: fcmp            d1, d0
    // 0xe8e04c: b.le            #0xe8e0f8
    // 0xe8e050: d2 = 1.000000
    //     0xe8e050: fmov            d2, #1.00000000
    // 0xe8e054: fcmp            d2, d1
    // 0xe8e058: b.le            #0xe8e0f8
    // 0xe8e05c: LoadField: r0 = r2->field_b
    //     0xe8e05c: ldur            w0, [x2, #0xb]
    // 0xe8e060: LoadField: r1 = r2->field_f
    //     0xe8e060: ldur            w1, [x2, #0xf]
    // 0xe8e064: DecompressPointer r1
    //     0xe8e064: add             x1, x1, HEAP, lsl #32
    // 0xe8e068: LoadField: r3 = r1->field_b
    //     0xe8e068: ldur            w3, [x1, #0xb]
    // 0xe8e06c: r4 = LoadInt32Instr(r0)
    //     0xe8e06c: sbfx            x4, x0, #1, #0x1f
    // 0xe8e070: stur            x4, [fp, #-0x10]
    // 0xe8e074: r0 = LoadInt32Instr(r3)
    //     0xe8e074: sbfx            x0, x3, #1, #0x1f
    // 0xe8e078: cmp             x4, x0
    // 0xe8e07c: b.ne            #0xe8e088
    // 0xe8e080: mov             x1, x2
    // 0xe8e084: r0 = _growToNextCapacity()
    //     0xe8e084: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8e088: ldur            x2, [fp, #-0x20]
    // 0xe8e08c: ldur            d0, [fp, #-0x58]
    // 0xe8e090: ldur            x3, [fp, #-0x10]
    // 0xe8e094: add             x0, x3, #1
    // 0xe8e098: lsl             x1, x0, #1
    // 0xe8e09c: StoreField: r2->field_b = r1
    //     0xe8e09c: stur            w1, [x2, #0xb]
    // 0xe8e0a0: LoadField: r1 = r2->field_f
    //     0xe8e0a0: ldur            w1, [x2, #0xf]
    // 0xe8e0a4: DecompressPointer r1
    //     0xe8e0a4: add             x1, x1, HEAP, lsl #32
    // 0xe8e0a8: r0 = inline_Allocate_Double()
    //     0xe8e0a8: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe8e0ac: add             x0, x0, #0x10
    //     0xe8e0b0: cmp             x4, x0
    //     0xe8e0b4: b.ls            #0xe8e334
    //     0xe8e0b8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8e0bc: sub             x0, x0, #0xf
    //     0xe8e0c0: movz            x4, #0xe15c
    //     0xe8e0c4: movk            x4, #0x3, lsl #16
    //     0xe8e0c8: stur            x4, [x0, #-1]
    // 0xe8e0cc: StoreField: r0->field_7 = d0
    //     0xe8e0cc: stur            d0, [x0, #7]
    // 0xe8e0d0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8e0d0: add             x25, x1, x3, lsl #2
    //     0xe8e0d4: add             x25, x25, #0xf
    //     0xe8e0d8: str             w0, [x25]
    //     0xe8e0dc: tbz             w0, #0, #0xe8e0f8
    //     0xe8e0e0: ldurb           w16, [x1, #-1]
    //     0xe8e0e4: ldurb           w17, [x0, #-1]
    //     0xe8e0e8: and             x16, x17, x16, lsr #2
    //     0xe8e0ec: tst             x16, HEAP, lsr #32
    //     0xe8e0f0: b.eq            #0xe8e0f8
    //     0xe8e0f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8e0f8: ldur            x0, [fp, #-0x18]
    // 0xe8e0fc: add             x3, x0, #1
    // 0xe8e100: ldur            d13, [fp, #-0x28]
    // 0xe8e104: ldur            d1, [fp, #-0x30]
    // 0xe8e108: ldur            d0, [fp, #-0x38]
    // 0xe8e10c: ldur            d4, [fp, #-0x40]
    // 0xe8e110: ldur            d6, [fp, #-0x48]
    // 0xe8e114: ldur            d10, [fp, #-0x50]
    // 0xe8e118: mov             x0, x2
    // 0xe8e11c: ldur            d2, [fp, #-0xb8]
    // 0xe8e120: ldur            d5, [fp, #-0xb0]
    // 0xe8e124: ldur            d7, [fp, #-0xa8]
    // 0xe8e128: ldur            d8, [fp, #-0xa0]
    // 0xe8e12c: ldur            d11, [fp, #-0x98]
    // 0xe8e130: ldur            d12, [fp, #-0x90]
    // 0xe8e134: ldur            d14, [fp, #-0x88]
    // 0xe8e138: ldur            d15, [fp, #-0x80]
    // 0xe8e13c: ldur            d16, [fp, #-0x78]
    // 0xe8e140: ldur            d17, [fp, #-0x70]
    // 0xe8e144: ldur            d18, [fp, #-0x68]
    // 0xe8e148: ldur            d19, [fp, #-0x60]
    // 0xe8e14c: d3 = 6.000000
    //     0xe8e14c: fmov            d3, #6.00000000
    // 0xe8e150: d9 = 3.000000
    //     0xe8e150: fmov            d9, #3.00000000
    // 0xe8e154: b               #0xe8dc3c
    // 0xe8e158: mov             x2, x0
    // 0xe8e15c: LoadField: r0 = r2->field_b
    //     0xe8e15c: ldur            w0, [x2, #0xb]
    // 0xe8e160: r3 = LoadInt32Instr(r0)
    //     0xe8e160: sbfx            x3, x0, #1, #0x1f
    // 0xe8e164: stur            x3, [fp, #-0x18]
    // 0xe8e168: r1 = 0
    //     0xe8e168: movz            x1, #0
    // 0xe8e16c: ldur            x0, [fp, #-8]
    // 0xe8e170: ldur            d7, [fp, #-0x28]
    // 0xe8e174: ldur            d3, [fp, #-0x30]
    // 0xe8e178: ldur            d8, [fp, #-0x38]
    // 0xe8e17c: ldur            d4, [fp, #-0x40]
    // 0xe8e180: ldur            d9, [fp, #-0x48]
    // 0xe8e184: ldur            d6, [fp, #-0x50]
    // 0xe8e188: d5 = 3.000000
    //     0xe8e188: fmov            d5, #3.00000000
    // 0xe8e18c: d2 = 1.000000
    //     0xe8e18c: fmov            d2, #1.00000000
    // 0xe8e190: CheckStackOverflow
    //     0xe8e190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e194: cmp             SP, x16
    //     0xe8e198: b.ls            #0xe8e354
    // 0xe8e19c: LoadField: r4 = r2->field_b
    //     0xe8e19c: ldur            w4, [x2, #0xb]
    // 0xe8e1a0: r5 = LoadInt32Instr(r4)
    //     0xe8e1a0: sbfx            x5, x4, #1, #0x1f
    // 0xe8e1a4: cmp             x3, x5
    // 0xe8e1a8: b.ne            #0xe8e2a4
    // 0xe8e1ac: cmp             x1, x5
    // 0xe8e1b0: b.ge            #0xe8e258
    // 0xe8e1b4: LoadField: r4 = r2->field_f
    //     0xe8e1b4: ldur            w4, [x2, #0xf]
    // 0xe8e1b8: DecompressPointer r4
    //     0xe8e1b8: add             x4, x4, HEAP, lsl #32
    // 0xe8e1bc: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0xe8e1bc: add             x16, x4, x1, lsl #2
    //     0xe8e1c0: ldur            w5, [x16, #0xf]
    // 0xe8e1c4: DecompressPointer r5
    //     0xe8e1c4: add             x5, x5, HEAP, lsl #32
    // 0xe8e1c8: add             x4, x1, #1
    // 0xe8e1cc: stur            x4, [fp, #-0x10]
    // 0xe8e1d0: LoadField: d0 = r5->field_7
    //     0xe8e1d0: ldur            d0, [x5, #7]
    // 0xe8e1d4: fsub            d1, d2, d0
    // 0xe8e1d8: fmul            d10, d1, d1
    // 0xe8e1dc: fmul            d11, d10, d1
    // 0xe8e1e0: LoadField: d10 = r0->field_27
    //     0xe8e1e0: ldur            d10, [x0, #0x27]
    // 0xe8e1e4: fmul            d12, d11, d10
    // 0xe8e1e8: fmul            d10, d1, d5
    // 0xe8e1ec: fmul            d13, d10, d1
    // 0xe8e1f0: fmul            d1, d13, d0
    // 0xe8e1f4: fmul            d13, d1, d7
    // 0xe8e1f8: fadd            d14, d12, d13
    // 0xe8e1fc: fmul            d12, d10, d0
    // 0xe8e200: fmul            d10, d12, d0
    // 0xe8e204: fmul            d12, d10, d8
    // 0xe8e208: fadd            d13, d14, d12
    // 0xe8e20c: fmul            d12, d0, d0
    // 0xe8e210: fmul            d14, d12, d0
    // 0xe8e214: fmul            d0, d14, d9
    // 0xe8e218: fadd            d12, d13, d0
    // 0xe8e21c: LoadField: d0 = r0->field_2f
    //     0xe8e21c: ldur            d0, [x0, #0x2f]
    // 0xe8e220: fmul            d13, d11, d0
    // 0xe8e224: fmul            d0, d1, d3
    // 0xe8e228: fadd            d1, d13, d0
    // 0xe8e22c: fmul            d0, d10, d4
    // 0xe8e230: fadd            d10, d1, d0
    // 0xe8e234: fmul            d0, d14, d6
    // 0xe8e238: fadd            d1, d10, d0
    // 0xe8e23c: mov             x1, x0
    // 0xe8e240: mov             v0.16b, v12.16b
    // 0xe8e244: r0 = _updateMinMax()
    //     0xe8e244: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xe8e248: ldur            x1, [fp, #-0x10]
    // 0xe8e24c: ldur            x2, [fp, #-0x20]
    // 0xe8e250: ldur            x3, [fp, #-0x18]
    // 0xe8e254: b               #0xe8e16c
    // 0xe8e258: mov             v3.16b, v9.16b
    // 0xe8e25c: mov             v2.16b, v6.16b
    // 0xe8e260: LoadField: d0 = r0->field_27
    //     0xe8e260: ldur            d0, [x0, #0x27]
    // 0xe8e264: LoadField: d1 = r0->field_2f
    //     0xe8e264: ldur            d1, [x0, #0x2f]
    // 0xe8e268: mov             x1, x0
    // 0xe8e26c: r0 = _updateMinMax()
    //     0xe8e26c: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xe8e270: ldur            x1, [fp, #-8]
    // 0xe8e274: ldur            d0, [fp, #-0x48]
    // 0xe8e278: ldur            d1, [fp, #-0x50]
    // 0xe8e27c: r0 = _updateMinMax()
    //     0xe8e27c: bl              #0xac6a60  ; [package:pdf/src/pdf/graphics.dart] _PathBBProxy::_updateMinMax
    // 0xe8e280: ldur            x0, [fp, #-8]
    // 0xe8e284: ldur            d0, [fp, #-0x48]
    // 0xe8e288: StoreField: r0->field_27 = d0
    //     0xe8e288: stur            d0, [x0, #0x27]
    // 0xe8e28c: ldur            d0, [fp, #-0x50]
    // 0xe8e290: StoreField: r0->field_2f = d0
    //     0xe8e290: stur            d0, [x0, #0x2f]
    // 0xe8e294: r0 = Null
    //     0xe8e294: mov             x0, NULL
    // 0xe8e298: LeaveFrame
    //     0xe8e298: mov             SP, fp
    //     0xe8e29c: ldp             fp, lr, [SP], #0x10
    // 0xe8e2a0: ret
    //     0xe8e2a0: ret             
    // 0xe8e2a4: mov             x0, x2
    // 0xe8e2a8: r0 = ConcurrentModificationError()
    //     0xe8e2a8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe8e2ac: mov             x1, x0
    // 0xe8e2b0: ldur            x0, [fp, #-0x20]
    // 0xe8e2b4: StoreField: r1->field_b = r0
    //     0xe8e2b4: stur            w0, [x1, #0xb]
    // 0xe8e2b8: mov             x0, x1
    // 0xe8e2bc: r0 = Throw()
    //     0xe8e2bc: bl              #0xec04b8  ; ThrowStub
    // 0xe8e2c0: brk             #0
    // 0xe8e2c4: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e2c4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e2c8: b               #0xe8dba0
    // 0xe8e2cc: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e2cc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e2d0: b               #0xe8dc64
    // 0xe8e2d4: SaveReg d0
    //     0xe8e2d4: str             q0, [SP, #-0x10]!
    // 0xe8e2d8: stp             x2, x3, [SP, #-0x10]!
    // 0xe8e2dc: SaveReg r1
    //     0xe8e2dc: str             x1, [SP, #-8]!
    // 0xe8e2e0: r0 = AllocateDouble()
    //     0xe8e2e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e2e4: RestoreReg r1
    //     0xe8e2e4: ldr             x1, [SP], #8
    // 0xe8e2e8: ldp             x2, x3, [SP], #0x10
    // 0xe8e2ec: RestoreReg d0
    //     0xe8e2ec: ldr             q0, [SP], #0x10
    // 0xe8e2f0: b               #0xe8ddd8
    // 0xe8e2f4: SaveReg d0
    //     0xe8e2f4: str             q0, [SP, #-0x10]!
    // 0xe8e2f8: stp             x2, x3, [SP, #-0x10]!
    // 0xe8e2fc: SaveReg r1
    //     0xe8e2fc: str             x1, [SP, #-8]!
    // 0xe8e300: r0 = AllocateDouble()
    //     0xe8e300: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e304: RestoreReg r1
    //     0xe8e304: ldr             x1, [SP], #8
    // 0xe8e308: ldp             x2, x3, [SP], #0x10
    // 0xe8e30c: RestoreReg d0
    //     0xe8e30c: ldr             q0, [SP], #0x10
    // 0xe8e310: b               #0xe8df1c
    // 0xe8e314: SaveReg d0
    //     0xe8e314: str             q0, [SP, #-0x10]!
    // 0xe8e318: stp             x2, x3, [SP, #-0x10]!
    // 0xe8e31c: SaveReg r1
    //     0xe8e31c: str             x1, [SP, #-8]!
    // 0xe8e320: r0 = AllocateDouble()
    //     0xe8e320: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e324: RestoreReg r1
    //     0xe8e324: ldr             x1, [SP], #8
    // 0xe8e328: ldp             x2, x3, [SP], #0x10
    // 0xe8e32c: RestoreReg d0
    //     0xe8e32c: ldr             q0, [SP], #0x10
    // 0xe8e330: b               #0xe8e000
    // 0xe8e334: SaveReg d0
    //     0xe8e334: str             q0, [SP, #-0x10]!
    // 0xe8e338: stp             x2, x3, [SP, #-0x10]!
    // 0xe8e33c: SaveReg r1
    //     0xe8e33c: str             x1, [SP, #-8]!
    // 0xe8e340: r0 = AllocateDouble()
    //     0xe8e340: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e344: RestoreReg r1
    //     0xe8e344: ldr             x1, [SP], #8
    // 0xe8e348: ldp             x2, x3, [SP], #0x10
    // 0xe8e34c: RestoreReg d0
    //     0xe8e34c: ldr             q0, [SP], #0x10
    // 0xe8e350: b               #0xe8e0cc
    // 0xe8e354: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e354: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e358: b               #0xe8e19c
  }
  get _ box(/* No info */) {
    // ** addr: 0xeab46c, size: 0x88
    // 0xeab46c: EnterFrame
    //     0xeab46c: stp             fp, lr, [SP, #-0x10]!
    //     0xeab470: mov             fp, SP
    // 0xeab474: AllocStack(0x20)
    //     0xeab474: sub             SP, SP, #0x20
    // 0xeab478: LoadField: d0 = r1->field_7
    //     0xeab478: ldur            d0, [x1, #7]
    // 0xeab47c: stur            d0, [fp, #-0x20]
    // 0xeab480: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xeab480: ldur            d1, [x1, #0x17]
    // 0xeab484: fcmp            d0, d1
    // 0xeab488: b.gt            #0xeab4a0
    // 0xeab48c: LoadField: d2 = r1->field_f
    //     0xeab48c: ldur            d2, [x1, #0xf]
    // 0xeab490: stur            d2, [fp, #-0x18]
    // 0xeab494: LoadField: d3 = r1->field_1f
    //     0xeab494: ldur            d3, [x1, #0x1f]
    // 0xeab498: fcmp            d2, d3
    // 0xeab49c: b.le            #0xeab4b4
    // 0xeab4a0: r0 = Instance_PdfRect
    //     0xeab4a0: add             x0, PP, #0x57, lsl #12  ; [pp+0x579e0] Obj!PdfRect@e0c6c1
    //     0xeab4a4: ldr             x0, [x0, #0x9e0]
    // 0xeab4a8: LeaveFrame
    //     0xeab4a8: mov             SP, fp
    //     0xeab4ac: ldp             fp, lr, [SP], #0x10
    // 0xeab4b0: ret
    //     0xeab4b0: ret             
    // 0xeab4b4: fsub            d4, d1, d0
    // 0xeab4b8: stur            d4, [fp, #-0x10]
    // 0xeab4bc: fsub            d1, d3, d2
    // 0xeab4c0: stur            d1, [fp, #-8]
    // 0xeab4c4: r0 = PdfRect()
    //     0xeab4c4: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xeab4c8: ldur            d0, [fp, #-0x20]
    // 0xeab4cc: StoreField: r0->field_7 = d0
    //     0xeab4cc: stur            d0, [x0, #7]
    // 0xeab4d0: ldur            d0, [fp, #-0x18]
    // 0xeab4d4: StoreField: r0->field_f = d0
    //     0xeab4d4: stur            d0, [x0, #0xf]
    // 0xeab4d8: ldur            d0, [fp, #-0x10]
    // 0xeab4dc: ArrayStore: r0[0] = d0  ; List_8
    //     0xeab4dc: stur            d0, [x0, #0x17]
    // 0xeab4e0: ldur            d0, [fp, #-8]
    // 0xeab4e4: StoreField: r0->field_1f = d0
    //     0xeab4e4: stur            d0, [x0, #0x1f]
    // 0xeab4e8: LeaveFrame
    //     0xeab4e8: mov             SP, fp
    //     0xeab4ec: ldp             fp, lr, [SP], #0x10
    // 0xeab4f0: ret
    //     0xeab4f0: ret             
  }
}

// class id: 935, size: 0xc, field offset: 0x8
class _PathProxy extends PathProxy {

  _ cubicTo(/* No info */) {
    // ** addr: 0xe8db08, size: 0x3c
    // 0xe8db08: EnterFrame
    //     0xe8db08: stp             fp, lr, [SP, #-0x10]!
    //     0xe8db0c: mov             fp, SP
    // 0xe8db10: CheckStackOverflow
    //     0xe8db10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8db14: cmp             SP, x16
    //     0xe8db18: b.ls            #0xe8db3c
    // 0xe8db1c: LoadField: r0 = r1->field_7
    //     0xe8db1c: ldur            w0, [x1, #7]
    // 0xe8db20: DecompressPointer r0
    //     0xe8db20: add             x0, x0, HEAP, lsl #32
    // 0xe8db24: mov             x1, x0
    // 0xe8db28: r0 = curveTo()
    //     0xe8db28: bl              #0xac43f8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::curveTo
    // 0xe8db2c: r0 = Null
    //     0xe8db2c: mov             x0, NULL
    // 0xe8db30: LeaveFrame
    //     0xe8db30: mov             SP, fp
    //     0xe8db34: ldp             fp, lr, [SP], #0x10
    // 0xe8db38: ret
    //     0xe8db38: ret             
    // 0xe8db3c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8db3c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8db40: b               #0xe8db1c
  }
}

// class id: 6812, size: 0x14, field offset: 0x14
enum PdfTextRenderingMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dadc, size: 0x64
    // 0xc4dadc: EnterFrame
    //     0xc4dadc: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dae0: mov             fp, SP
    // 0xc4dae4: AllocStack(0x10)
    //     0xc4dae4: sub             SP, SP, #0x10
    // 0xc4dae8: SetupParameters(PdfTextRenderingMode this /* r1 => r0, fp-0x8 */)
    //     0xc4dae8: mov             x0, x1
    //     0xc4daec: stur            x1, [fp, #-8]
    // 0xc4daf0: CheckStackOverflow
    //     0xc4daf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4daf4: cmp             SP, x16
    //     0xc4daf8: b.ls            #0xc4db38
    // 0xc4dafc: r1 = Null
    //     0xc4dafc: mov             x1, NULL
    // 0xc4db00: r2 = 4
    //     0xc4db00: movz            x2, #0x4
    // 0xc4db04: r0 = AllocateArray()
    //     0xc4db04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4db08: r16 = "PdfTextRenderingMode."
    //     0xc4db08: add             x16, PP, #0x33, lsl #12  ; [pp+0x33920] "PdfTextRenderingMode."
    //     0xc4db0c: ldr             x16, [x16, #0x920]
    // 0xc4db10: StoreField: r0->field_f = r16
    //     0xc4db10: stur            w16, [x0, #0xf]
    // 0xc4db14: ldur            x1, [fp, #-8]
    // 0xc4db18: LoadField: r2 = r1->field_f
    //     0xc4db18: ldur            w2, [x1, #0xf]
    // 0xc4db1c: DecompressPointer r2
    //     0xc4db1c: add             x2, x2, HEAP, lsl #32
    // 0xc4db20: StoreField: r0->field_13 = r2
    //     0xc4db20: stur            w2, [x0, #0x13]
    // 0xc4db24: str             x0, [SP]
    // 0xc4db28: r0 = _interpolate()
    //     0xc4db28: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4db2c: LeaveFrame
    //     0xc4db2c: mov             SP, fp
    //     0xc4db30: ldp             fp, lr, [SP], #0x10
    // 0xc4db34: ret
    //     0xc4db34: ret             
    // 0xc4db38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4db38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4db3c: b               #0xc4dafc
  }
}

// class id: 6813, size: 0x14, field offset: 0x14
enum PdfLineCap extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4da78, size: 0x64
    // 0xc4da78: EnterFrame
    //     0xc4da78: stp             fp, lr, [SP, #-0x10]!
    //     0xc4da7c: mov             fp, SP
    // 0xc4da80: AllocStack(0x10)
    //     0xc4da80: sub             SP, SP, #0x10
    // 0xc4da84: SetupParameters(PdfLineCap this /* r1 => r0, fp-0x8 */)
    //     0xc4da84: mov             x0, x1
    //     0xc4da88: stur            x1, [fp, #-8]
    // 0xc4da8c: CheckStackOverflow
    //     0xc4da8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4da90: cmp             SP, x16
    //     0xc4da94: b.ls            #0xc4dad4
    // 0xc4da98: r1 = Null
    //     0xc4da98: mov             x1, NULL
    // 0xc4da9c: r2 = 4
    //     0xc4da9c: movz            x2, #0x4
    // 0xc4daa0: r0 = AllocateArray()
    //     0xc4daa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4daa4: r16 = "PdfLineCap."
    //     0xc4daa4: add             x16, PP, #0x47, lsl #12  ; [pp+0x477d0] "PdfLineCap."
    //     0xc4daa8: ldr             x16, [x16, #0x7d0]
    // 0xc4daac: StoreField: r0->field_f = r16
    //     0xc4daac: stur            w16, [x0, #0xf]
    // 0xc4dab0: ldur            x1, [fp, #-8]
    // 0xc4dab4: LoadField: r2 = r1->field_f
    //     0xc4dab4: ldur            w2, [x1, #0xf]
    // 0xc4dab8: DecompressPointer r2
    //     0xc4dab8: add             x2, x2, HEAP, lsl #32
    // 0xc4dabc: StoreField: r0->field_13 = r2
    //     0xc4dabc: stur            w2, [x0, #0x13]
    // 0xc4dac0: str             x0, [SP]
    // 0xc4dac4: r0 = _interpolate()
    //     0xc4dac4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dac8: LeaveFrame
    //     0xc4dac8: mov             SP, fp
    //     0xc4dacc: ldp             fp, lr, [SP], #0x10
    // 0xc4dad0: ret
    //     0xc4dad0: ret             
    // 0xc4dad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dad4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dad8: b               #0xc4da98
  }
}

// class id: 6814, size: 0x14, field offset: 0x14
enum PdfLineJoin extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4da14, size: 0x64
    // 0xc4da14: EnterFrame
    //     0xc4da14: stp             fp, lr, [SP, #-0x10]!
    //     0xc4da18: mov             fp, SP
    // 0xc4da1c: AllocStack(0x10)
    //     0xc4da1c: sub             SP, SP, #0x10
    // 0xc4da20: SetupParameters(PdfLineJoin this /* r1 => r0, fp-0x8 */)
    //     0xc4da20: mov             x0, x1
    //     0xc4da24: stur            x1, [fp, #-8]
    // 0xc4da28: CheckStackOverflow
    //     0xc4da28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4da2c: cmp             SP, x16
    //     0xc4da30: b.ls            #0xc4da70
    // 0xc4da34: r1 = Null
    //     0xc4da34: mov             x1, NULL
    // 0xc4da38: r2 = 4
    //     0xc4da38: movz            x2, #0x4
    // 0xc4da3c: r0 = AllocateArray()
    //     0xc4da3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4da40: r16 = "PdfLineJoin."
    //     0xc4da40: add             x16, PP, #0x47, lsl #12  ; [pp+0x477d8] "PdfLineJoin."
    //     0xc4da44: ldr             x16, [x16, #0x7d8]
    // 0xc4da48: StoreField: r0->field_f = r16
    //     0xc4da48: stur            w16, [x0, #0xf]
    // 0xc4da4c: ldur            x1, [fp, #-8]
    // 0xc4da50: LoadField: r2 = r1->field_f
    //     0xc4da50: ldur            w2, [x1, #0xf]
    // 0xc4da54: DecompressPointer r2
    //     0xc4da54: add             x2, x2, HEAP, lsl #32
    // 0xc4da58: StoreField: r0->field_13 = r2
    //     0xc4da58: stur            w2, [x0, #0x13]
    // 0xc4da5c: str             x0, [SP]
    // 0xc4da60: r0 = _interpolate()
    //     0xc4da60: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4da64: LeaveFrame
    //     0xc4da64: mov             SP, fp
    //     0xc4da68: ldp             fp, lr, [SP], #0x10
    // 0xc4da6c: ret
    //     0xc4da6c: ret             
    // 0xc4da70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4da70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4da74: b               #0xc4da34
  }
}
