// lib: , url: package:pdf/src/pdf/io/vm.dart

// class id: 1050795, size: 0x8
class :: {

  static late (dynamic, List<int>) => List<int> defaultDeflate; // offset: 0x16e4

  static _ pdfCompute(/* No info */) async {
    // ** addr: 0xe887c4, size: 0xcc
    // 0xe887c4: EnterFrame
    //     0xe887c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe887c8: mov             fp, SP
    // 0xe887cc: AllocStack(0x30)
    //     0xe887cc: sub             SP, SP, #0x30
    // 0xe887d0: SetupParameters(dynamic _ /* r1, fp-0x18 */)
    //     0xe887d0: stur            NULL, [fp, #-8]
    //     0xe887d4: movz            x0, #0
    //     0xe887d8: add             x1, fp, w0, sxtw #2
    //     0xe887dc: ldr             x1, [x1, #0x10]
    //     0xe887e0: stur            x1, [fp, #-0x18]
    // 0xe887e4: LoadField: r0 = r4->field_f
    //     0xe887e4: ldur            w0, [x4, #0xf]
    // 0xe887e8: cbnz            w0, #0xe887f4
    // 0xe887ec: r2 = Null
    //     0xe887ec: mov             x2, NULL
    // 0xe887f0: b               #0xe88800
    // 0xe887f4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xe887f4: ldur            w0, [x4, #0x17]
    // 0xe887f8: add             x2, fp, w0, sxtw #2
    // 0xe887fc: ldr             x2, [x2, #0x10]
    // 0xe88800: stur            x2, [fp, #-0x10]
    // 0xe88804: CheckStackOverflow
    //     0xe88804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88808: cmp             SP, x16
    //     0xe8880c: b.ls            #0xe88888
    // 0xe88810: mov             x0, x2
    // 0xe88814: r0 = InitAsync()
    //     0xe88814: bl              #0x661298  ; InitAsyncStub
    // 0xe88818: r0 = environment()
    //     0xe88818: bl              #0x72376c  ; [dart:io] _Platform::environment
    // 0xe8881c: r1 = LoadClassIdInstr(r0)
    //     0xe8881c: ldur            x1, [x0, #-1]
    //     0xe88820: ubfx            x1, x1, #0xc, #0x14
    // 0xe88824: mov             x16, x0
    // 0xe88828: mov             x0, x1
    // 0xe8882c: mov             x1, x16
    // 0xe88830: r2 = "FLUTTER_TEST"
    //     0xe88830: add             x2, PP, #0xe, lsl #12  ; [pp+0xe6b8] "FLUTTER_TEST"
    //     0xe88834: ldr             x2, [x2, #0x6b8]
    // 0xe88838: r0 = GDT[cid_x0 + 0x55f]()
    //     0xe88838: add             lr, x0, #0x55f
    //     0xe8883c: ldr             lr, [x21, lr, lsl #3]
    //     0xe88840: blr             lr
    // 0xe88844: tbnz            w0, #4, #0xe88864
    // 0xe88848: ldur            x16, [fp, #-0x18]
    // 0xe8884c: str             x16, [SP]
    // 0xe88850: ldur            x0, [fp, #-0x18]
    // 0xe88854: ClosureCall
    //     0xe88854: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xe88858: ldur            x2, [x0, #0x1f]
    //     0xe8885c: blr             x2
    // 0xe88860: r0 = ReturnAsync()
    //     0xe88860: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe88864: ldur            x16, [fp, #-0x10]
    // 0xe88868: ldur            lr, [fp, #-0x18]
    // 0xe8886c: stp             lr, x16, [SP, #8]
    // 0xe88870: r16 = "dart_pdf"
    //     0xe88870: add             x16, PP, #0x36, lsl #12  ; [pp+0x36700] "dart_pdf"
    //     0xe88874: ldr             x16, [x16, #0x700]
    // 0xe88878: str             x16, [SP]
    // 0xe8887c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe8887c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe88880: r0 = run()
    //     0xe88880: bl              #0x696df8  ; [dart:isolate] Isolate::run
    // 0xe88884: r0 = ReturnAsync()
    //     0xe88884: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe88888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8888c: b               #0xe88810
  }
  static (dynamic, List<int>) => List<int> defaultDeflate() {
    // ** addr: 0xe8b504, size: 0x58
    // 0xe8b504: EnterFrame
    //     0xe8b504: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b508: mov             fp, SP
    // 0xe8b50c: AllocStack(0x8)
    //     0xe8b50c: sub             SP, SP, #8
    // 0xe8b510: r2 = Instance_ZLibCodec
    //     0xe8b510: add             x2, PP, #0x36, lsl #12  ; [pp+0x36918] Obj!ZLibCodec@e2cc81
    //     0xe8b514: ldr             x2, [x2, #0x918]
    // 0xe8b518: LoadField: r3 = r2->field_7
    //     0xe8b518: ldur            w3, [x2, #7]
    // 0xe8b51c: DecompressPointer r3
    //     0xe8b51c: add             x3, x3, HEAP, lsl #32
    // 0xe8b520: r1 = Function 'encode':.
    //     0xe8b520: add             x1, PP, #0x36, lsl #12  ; [pp+0x36920] AnonymousClosure: (0xcebc40), in [dart:convert] Codec::encode (0xcebb90)
    //     0xe8b524: ldr             x1, [x1, #0x920]
    // 0xe8b528: r0 = AllocateClosureTA()
    //     0xe8b528: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xe8b52c: mov             x3, x0
    // 0xe8b530: r2 = Null
    //     0xe8b530: mov             x2, NULL
    // 0xe8b534: r1 = Null
    //     0xe8b534: mov             x1, NULL
    // 0xe8b538: stur            x3, [fp, #-8]
    // 0xe8b53c: r8 = (dynamic this, List<int>) => List<int>
    //     0xe8b53c: ldr             x8, [PP, #0x32e8]  ; [pp+0x32e8] FunctionType: (dynamic this, List<int>) => List<int>
    // 0xe8b540: r3 = Null
    //     0xe8b540: add             x3, PP, #0x36, lsl #12  ; [pp+0x36928] Null
    //     0xe8b544: ldr             x3, [x3, #0x928]
    // 0xe8b548: r0 = DefaultTypeTest()
    //     0xe8b548: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe8b54c: ldur            x0, [fp, #-8]
    // 0xe8b550: LeaveFrame
    //     0xe8b550: mov             SP, fp
    //     0xe8b554: ldp             fp, lr, [SP], #0x10
    // 0xe8b558: ret
    //     0xe8b558: ret             
  }
}
