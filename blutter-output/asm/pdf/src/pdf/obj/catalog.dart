// lib: , url: package:pdf/src/pdf/obj/catalog.dart

// class id: 1050797, size: 0x8
class :: {
}

// class id: 898, size: 0x4c, field offset: 0x2c
class PdfCatalog extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7b5e40, size: 0x5f8
    // 0x7b5e40: EnterFrame
    //     0x7b5e40: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5e44: mov             fp, SP
    // 0x7b5e48: AllocStack(0x48)
    //     0x7b5e48: sub             SP, SP, #0x48
    // 0x7b5e4c: SetupParameters(PdfCatalog this /* r1 => r0, fp-0x10 */)
    //     0x7b5e4c: mov             x0, x1
    //     0x7b5e50: stur            x1, [fp, #-0x10]
    // 0x7b5e54: CheckStackOverflow
    //     0x7b5e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5e58: cmp             SP, x16
    //     0x7b5e5c: b.ls            #0x7b640c
    // 0x7b5e60: LoadField: r3 = r0->field_1b
    //     0x7b5e60: ldur            w3, [x0, #0x1b]
    // 0x7b5e64: DecompressPointer r3
    //     0x7b5e64: add             x3, x3, HEAP, lsl #32
    // 0x7b5e68: stur            x3, [fp, #-8]
    // 0x7b5e6c: r1 = Null
    //     0x7b5e6c: mov             x1, NULL
    // 0x7b5e70: r2 = 4
    //     0x7b5e70: movz            x2, #0x4
    // 0x7b5e74: r0 = AllocateArray()
    //     0x7b5e74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b5e78: r16 = "/"
    //     0x7b5e78: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7b5e7c: StoreField: r0->field_f = r16
    //     0x7b5e7c: stur            w16, [x0, #0xf]
    // 0x7b5e80: ldur            x1, [fp, #-0x10]
    // 0x7b5e84: LoadField: r2 = r1->field_23
    //     0x7b5e84: ldur            w2, [x1, #0x23]
    // 0x7b5e88: DecompressPointer r2
    //     0x7b5e88: add             x2, x2, HEAP, lsl #32
    // 0x7b5e8c: stur            x2, [fp, #-0x18]
    // 0x7b5e90: LoadField: r3 = r2->field_2b
    //     0x7b5e90: ldur            w3, [x2, #0x2b]
    // 0x7b5e94: DecompressPointer r3
    //     0x7b5e94: add             x3, x3, HEAP, lsl #32
    // 0x7b5e98: StoreField: r0->field_13 = r3
    //     0x7b5e98: stur            w3, [x0, #0x13]
    // 0x7b5e9c: str             x0, [SP]
    // 0x7b5ea0: r0 = _interpolate()
    //     0x7b5ea0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b5ea4: stur            x0, [fp, #-0x20]
    // 0x7b5ea8: r0 = PdfName()
    //     0x7b5ea8: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b5eac: mov             x3, x0
    // 0x7b5eb0: ldur            x0, [fp, #-0x20]
    // 0x7b5eb4: stur            x3, [fp, #-0x28]
    // 0x7b5eb8: StoreField: r3->field_7 = r0
    //     0x7b5eb8: stur            w0, [x3, #7]
    // 0x7b5ebc: ldur            x4, [fp, #-8]
    // 0x7b5ec0: LoadField: r5 = r4->field_7
    //     0x7b5ec0: ldur            w5, [x4, #7]
    // 0x7b5ec4: DecompressPointer r5
    //     0x7b5ec4: add             x5, x5, HEAP, lsl #32
    // 0x7b5ec8: mov             x0, x3
    // 0x7b5ecc: mov             x2, x5
    // 0x7b5ed0: stur            x5, [fp, #-0x20]
    // 0x7b5ed4: r1 = Null
    //     0x7b5ed4: mov             x1, NULL
    // 0x7b5ed8: cmp             w2, NULL
    // 0x7b5edc: b.eq            #0x7b5f00
    // 0x7b5ee0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b5ee0: ldur            w4, [x2, #0x17]
    // 0x7b5ee4: DecompressPointer r4
    //     0x7b5ee4: add             x4, x4, HEAP, lsl #32
    // 0x7b5ee8: r8 = X0 bound PdfDataType
    //     0x7b5ee8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b5eec: ldr             x8, [x8, #0x6d8]
    // 0x7b5ef0: LoadField: r9 = r4->field_7
    //     0x7b5ef0: ldur            x9, [x4, #7]
    // 0x7b5ef4: r3 = Null
    //     0x7b5ef4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c0a8] Null
    //     0x7b5ef8: ldr             x3, [x3, #0xa8]
    // 0x7b5efc: blr             x9
    // 0x7b5f00: ldur            x0, [fp, #-8]
    // 0x7b5f04: LoadField: r4 = r0->field_b
    //     0x7b5f04: ldur            w4, [x0, #0xb]
    // 0x7b5f08: DecompressPointer r4
    //     0x7b5f08: add             x4, x4, HEAP, lsl #32
    // 0x7b5f0c: mov             x1, x4
    // 0x7b5f10: ldur            x3, [fp, #-0x28]
    // 0x7b5f14: stur            x4, [fp, #-0x30]
    // 0x7b5f18: r2 = "/Version"
    //     0x7b5f18: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c0b8] "/Version"
    //     0x7b5f1c: ldr             x2, [x2, #0xb8]
    // 0x7b5f20: r0 = []=()
    //     0x7b5f20: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5f24: ldur            x0, [fp, #-0x10]
    // 0x7b5f28: LoadField: r1 = r0->field_2b
    //     0x7b5f28: ldur            w1, [x0, #0x2b]
    // 0x7b5f2c: DecompressPointer r1
    //     0x7b5f2c: add             x1, x1, HEAP, lsl #32
    // 0x7b5f30: r0 = ref()
    //     0x7b5f30: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b5f34: ldur            x2, [fp, #-0x20]
    // 0x7b5f38: mov             x3, x0
    // 0x7b5f3c: r1 = Null
    //     0x7b5f3c: mov             x1, NULL
    // 0x7b5f40: stur            x3, [fp, #-0x10]
    // 0x7b5f44: cmp             w2, NULL
    // 0x7b5f48: b.eq            #0x7b5f6c
    // 0x7b5f4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b5f4c: ldur            w4, [x2, #0x17]
    // 0x7b5f50: DecompressPointer r4
    //     0x7b5f50: add             x4, x4, HEAP, lsl #32
    // 0x7b5f54: r8 = X0 bound PdfDataType
    //     0x7b5f54: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b5f58: ldr             x8, [x8, #0x6d8]
    // 0x7b5f5c: LoadField: r9 = r4->field_7
    //     0x7b5f5c: ldur            x9, [x4, #7]
    // 0x7b5f60: r3 = Null
    //     0x7b5f60: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c0c0] Null
    //     0x7b5f64: ldr             x3, [x3, #0xc0]
    // 0x7b5f68: blr             x9
    // 0x7b5f6c: ldur            x1, [fp, #-0x30]
    // 0x7b5f70: ldur            x3, [fp, #-0x10]
    // 0x7b5f74: r2 = "/Pages"
    //     0x7b5f74: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c0d0] "/Pages"
    //     0x7b5f78: ldr             x2, [x2, #0xd0]
    // 0x7b5f7c: r0 = []=()
    //     0x7b5f7c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5f80: r0 = PdfName()
    //     0x7b5f80: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b5f84: mov             x3, x0
    // 0x7b5f88: r0 = "/UseNone"
    //     0x7b5f88: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] "/UseNone"
    //     0x7b5f8c: ldr             x0, [x0, #0xd8]
    // 0x7b5f90: stur            x3, [fp, #-0x10]
    // 0x7b5f94: StoreField: r3->field_7 = r0
    //     0x7b5f94: stur            w0, [x3, #7]
    // 0x7b5f98: mov             x0, x3
    // 0x7b5f9c: ldur            x2, [fp, #-0x20]
    // 0x7b5fa0: r1 = Null
    //     0x7b5fa0: mov             x1, NULL
    // 0x7b5fa4: cmp             w2, NULL
    // 0x7b5fa8: b.eq            #0x7b5fcc
    // 0x7b5fac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b5fac: ldur            w4, [x2, #0x17]
    // 0x7b5fb0: DecompressPointer r4
    //     0x7b5fb0: add             x4, x4, HEAP, lsl #32
    // 0x7b5fb4: r8 = X0 bound PdfDataType
    //     0x7b5fb4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b5fb8: ldr             x8, [x8, #0x6d8]
    // 0x7b5fbc: LoadField: r9 = r4->field_7
    //     0x7b5fbc: ldur            x9, [x4, #7]
    // 0x7b5fc0: r3 = Null
    //     0x7b5fc0: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c0e0] Null
    //     0x7b5fc4: ldr             x3, [x3, #0xe0]
    // 0x7b5fc8: blr             x9
    // 0x7b5fcc: ldur            x1, [fp, #-0x30]
    // 0x7b5fd0: ldur            x3, [fp, #-0x10]
    // 0x7b5fd4: r2 = "/PageMode"
    //     0x7b5fd4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c0f0] "/PageMode"
    //     0x7b5fd8: ldr             x2, [x2, #0xf0]
    // 0x7b5fdc: r0 = []=()
    //     0x7b5fdc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b5fe0: r1 = <PdfAnnot>
    //     0x7b5fe0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36838] TypeArguments: <PdfAnnot>
    //     0x7b5fe4: ldr             x1, [x1, #0x838]
    // 0x7b5fe8: r2 = 0
    //     0x7b5fe8: movz            x2, #0
    // 0x7b5fec: r0 = _GrowableList()
    //     0x7b5fec: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7b5ff0: mov             x1, x0
    // 0x7b5ff4: ldur            x0, [fp, #-0x18]
    // 0x7b5ff8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7b5ff8: ldur            w2, [x0, #0x17]
    // 0x7b5ffc: DecompressPointer r2
    //     0x7b5ffc: add             x2, x2, HEAP, lsl #32
    // 0x7b6000: r16 = Sentinel
    //     0x7b6000: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b6004: cmp             w2, w16
    // 0x7b6008: b.eq            #0x7b6414
    // 0x7b600c: LoadField: r0 = r2->field_2b
    //     0x7b600c: ldur            w0, [x2, #0x2b]
    // 0x7b6010: DecompressPointer r0
    //     0x7b6010: add             x0, x0, HEAP, lsl #32
    // 0x7b6014: LoadField: r2 = r0->field_2b
    //     0x7b6014: ldur            w2, [x0, #0x2b]
    // 0x7b6018: DecompressPointer r2
    //     0x7b6018: add             x2, x2, HEAP, lsl #32
    // 0x7b601c: LoadField: r0 = r2->field_b
    //     0x7b601c: ldur            w0, [x2, #0xb]
    // 0x7b6020: r3 = LoadInt32Instr(r0)
    //     0x7b6020: sbfx            x3, x0, #1, #0x1f
    // 0x7b6024: LoadField: r0 = r2->field_f
    //     0x7b6024: ldur            w0, [x2, #0xf]
    // 0x7b6028: DecompressPointer r0
    //     0x7b6028: add             x0, x0, HEAP, lsl #32
    // 0x7b602c: r2 = 0
    //     0x7b602c: movz            x2, #0
    // 0x7b6030: CheckStackOverflow
    //     0x7b6030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6034: cmp             SP, x16
    //     0x7b6038: b.ls            #0x7b6420
    // 0x7b603c: cmp             x2, x3
    // 0x7b6040: b.ge            #0x7b6080
    // 0x7b6044: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0x7b6044: add             x16, x0, x2, lsl #2
    //     0x7b6048: ldur            w4, [x16, #0xf]
    // 0x7b604c: DecompressPointer r4
    //     0x7b604c: add             x4, x4, HEAP, lsl #32
    // 0x7b6050: add             x5, x2, #1
    // 0x7b6054: LoadField: r2 = r4->field_53
    //     0x7b6054: ldur            w2, [x4, #0x53]
    // 0x7b6058: DecompressPointer r2
    //     0x7b6058: add             x2, x2, HEAP, lsl #32
    // 0x7b605c: LoadField: r4 = r2->field_b
    //     0x7b605c: ldur            w4, [x2, #0xb]
    // 0x7b6060: r2 = LoadInt32Instr(r4)
    //     0x7b6060: sbfx            x2, x4, #1, #0x1f
    // 0x7b6064: CheckStackOverflow
    //     0x7b6064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6068: cmp             SP, x16
    //     0x7b606c: b.ls            #0x7b6428
    // 0x7b6070: cmp             x2, #0
    // 0x7b6074: b.gt            #0x7b63d8
    // 0x7b6078: mov             x2, x5
    // 0x7b607c: b               #0x7b6030
    // 0x7b6080: LoadField: r0 = r1->field_b
    //     0x7b6080: ldur            w0, [x1, #0xb]
    // 0x7b6084: r3 = LoadInt32Instr(r0)
    //     0x7b6084: sbfx            x3, x0, #1, #0x1f
    // 0x7b6088: stur            x3, [fp, #-0x38]
    // 0x7b608c: cbz             x3, #0x7b63c8
    // 0x7b6090: ldur            x1, [fp, #-8]
    // 0x7b6094: r2 = "/AcroForm"
    //     0x7b6094: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c0f8] "/AcroForm"
    //     0x7b6098: ldr             x2, [x2, #0xf8]
    // 0x7b609c: r0 = getClipPath()
    //     0x7b609c: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7b60a0: cmp             w0, NULL
    // 0x7b60a4: b.ne            #0x7b6114
    // 0x7b60a8: r1 = <PdfDataType>
    //     0x7b60a8: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b60ac: ldr             x1, [x1, #0x4c8]
    // 0x7b60b0: r0 = PdfDict()
    //     0x7b60b0: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b60b4: mov             x1, x0
    // 0x7b60b8: stur            x0, [fp, #-8]
    // 0x7b60bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b60bc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b60c0: r0 = PdfDict()
    //     0x7b60c0: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7b60c4: ldur            x0, [fp, #-8]
    // 0x7b60c8: ldur            x2, [fp, #-0x20]
    // 0x7b60cc: r1 = Null
    //     0x7b60cc: mov             x1, NULL
    // 0x7b60d0: cmp             w2, NULL
    // 0x7b60d4: b.eq            #0x7b60f8
    // 0x7b60d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b60d8: ldur            w4, [x2, #0x17]
    // 0x7b60dc: DecompressPointer r4
    //     0x7b60dc: add             x4, x4, HEAP, lsl #32
    // 0x7b60e0: r8 = X0 bound PdfDataType
    //     0x7b60e0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b60e4: ldr             x8, [x8, #0x6d8]
    // 0x7b60e8: LoadField: r9 = r4->field_7
    //     0x7b60e8: ldur            x9, [x4, #7]
    // 0x7b60ec: r3 = Null
    //     0x7b60ec: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c100] Null
    //     0x7b60f0: ldr             x3, [x3, #0x100]
    // 0x7b60f4: blr             x9
    // 0x7b60f8: ldur            x1, [fp, #-0x30]
    // 0x7b60fc: ldur            x3, [fp, #-8]
    // 0x7b6100: r2 = "/AcroForm"
    //     0x7b6100: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c0f8] "/AcroForm"
    //     0x7b6104: ldr             x2, [x2, #0xf8]
    // 0x7b6108: r0 = []=()
    //     0x7b6108: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b610c: ldur            x3, [fp, #-8]
    // 0x7b6110: b               #0x7b6118
    // 0x7b6114: mov             x3, x0
    // 0x7b6118: mov             x0, x3
    // 0x7b611c: stur            x3, [fp, #-8]
    // 0x7b6120: r2 = Null
    //     0x7b6120: mov             x2, NULL
    // 0x7b6124: r1 = Null
    //     0x7b6124: mov             x1, NULL
    // 0x7b6128: r4 = 60
    //     0x7b6128: movz            x4, #0x3c
    // 0x7b612c: branchIfSmi(r0, 0x7b6138)
    //     0x7b612c: tbz             w0, #0, #0x7b6138
    // 0x7b6130: r4 = LoadClassIdInstr(r0)
    //     0x7b6130: ldur            x4, [x0, #-1]
    //     0x7b6134: ubfx            x4, x4, #0xc, #0x14
    // 0x7b6138: sub             x4, x4, #0x390
    // 0x7b613c: cmp             x4, #1
    // 0x7b6140: b.ls            #0x7b6158
    // 0x7b6144: r8 = PdfDict<PdfDataType>
    //     0x7b6144: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c110] Type: PdfDict<PdfDataType>
    //     0x7b6148: ldr             x8, [x8, #0x110]
    // 0x7b614c: r3 = Null
    //     0x7b614c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c118] Null
    //     0x7b6150: ldr             x3, [x3, #0x118]
    // 0x7b6154: r0 = PdfDict<PdfDataType>()
    //     0x7b6154: bl              #0x7b5928  ; IsType_PdfDict<PdfDataType>_Stub
    // 0x7b6158: r0 = PdfNum()
    //     0x7b6158: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b615c: stur            x0, [fp, #-0x10]
    // 0x7b6160: StoreField: r0->field_7 = rZR
    //     0x7b6160: stur            wzr, [x0, #7]
    // 0x7b6164: ldur            x1, [fp, #-8]
    // 0x7b6168: r2 = "/SigFlags"
    //     0x7b6168: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c128] "/SigFlags"
    //     0x7b616c: ldr             x2, [x2, #0x128]
    // 0x7b6170: r0 = getClipPath()
    //     0x7b6170: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7b6174: mov             x3, x0
    // 0x7b6178: r2 = Null
    //     0x7b6178: mov             x2, NULL
    // 0x7b617c: r1 = Null
    //     0x7b617c: mov             x1, NULL
    // 0x7b6180: stur            x3, [fp, #-0x18]
    // 0x7b6184: r4 = LoadClassIdInstr(r0)
    //     0x7b6184: ldur            x4, [x0, #-1]
    //     0x7b6188: ubfx            x4, x4, #0xc, #0x14
    // 0x7b618c: cmp             x4, #0x38c
    // 0x7b6190: b.eq            #0x7b61a8
    // 0x7b6194: r8 = PdfNum?
    //     0x7b6194: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c130] Type: PdfNum?
    //     0x7b6198: ldr             x8, [x8, #0x130]
    // 0x7b619c: r3 = Null
    //     0x7b619c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c138] Null
    //     0x7b61a0: ldr             x3, [x3, #0x138]
    // 0x7b61a4: r0 = DefaultNullableTypeTest()
    //     0x7b61a4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7b61a8: ldur            x0, [fp, #-0x18]
    // 0x7b61ac: cmp             w0, NULL
    // 0x7b61b0: b.ne            #0x7b61c0
    // 0x7b61b4: r2 = Instance_PdfNum
    //     0x7b61b4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7b61b8: ldr             x2, [x2, #0x148]
    // 0x7b61bc: b               #0x7b61c4
    // 0x7b61c0: mov             x2, x0
    // 0x7b61c4: ldur            x0, [fp, #-8]
    // 0x7b61c8: ldur            x1, [fp, #-0x10]
    // 0x7b61cc: r0 = |()
    //     0x7b61cc: bl              #0x7b64f0  ; [package:pdf/src/pdf/format/num.dart] PdfNum::|
    // 0x7b61d0: mov             x4, x0
    // 0x7b61d4: ldur            x3, [fp, #-8]
    // 0x7b61d8: stur            x4, [fp, #-0x18]
    // 0x7b61dc: LoadField: r5 = r3->field_7
    //     0x7b61dc: ldur            w5, [x3, #7]
    // 0x7b61e0: DecompressPointer r5
    //     0x7b61e0: add             x5, x5, HEAP, lsl #32
    // 0x7b61e4: mov             x0, x4
    // 0x7b61e8: mov             x2, x5
    // 0x7b61ec: stur            x5, [fp, #-0x10]
    // 0x7b61f0: r1 = Null
    //     0x7b61f0: mov             x1, NULL
    // 0x7b61f4: cmp             w2, NULL
    // 0x7b61f8: b.eq            #0x7b621c
    // 0x7b61fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b61fc: ldur            w4, [x2, #0x17]
    // 0x7b6200: DecompressPointer r4
    //     0x7b6200: add             x4, x4, HEAP, lsl #32
    // 0x7b6204: r8 = X0 bound PdfDataType
    //     0x7b6204: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6208: ldr             x8, [x8, #0x6d8]
    // 0x7b620c: LoadField: r9 = r4->field_7
    //     0x7b620c: ldur            x9, [x4, #7]
    // 0x7b6210: r3 = Null
    //     0x7b6210: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c150] Null
    //     0x7b6214: ldr             x3, [x3, #0x150]
    // 0x7b6218: blr             x9
    // 0x7b621c: ldur            x0, [fp, #-8]
    // 0x7b6220: LoadField: r4 = r0->field_b
    //     0x7b6220: ldur            w4, [x0, #0xb]
    // 0x7b6224: DecompressPointer r4
    //     0x7b6224: add             x4, x4, HEAP, lsl #32
    // 0x7b6228: mov             x1, x4
    // 0x7b622c: ldur            x3, [fp, #-0x18]
    // 0x7b6230: stur            x4, [fp, #-0x20]
    // 0x7b6234: r2 = "/SigFlags"
    //     0x7b6234: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c128] "/SigFlags"
    //     0x7b6238: ldr             x2, [x2, #0x128]
    // 0x7b623c: r0 = []=()
    //     0x7b623c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6240: ldur            x1, [fp, #-8]
    // 0x7b6244: r2 = "/Fields"
    //     0x7b6244: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c160] "/Fields"
    //     0x7b6248: ldr             x2, [x2, #0x160]
    // 0x7b624c: r0 = getClipPath()
    //     0x7b624c: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7b6250: cmp             w0, NULL
    // 0x7b6254: b.ne            #0x7b62c0
    // 0x7b6258: r1 = <PdfDataType>
    //     0x7b6258: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b625c: ldr             x1, [x1, #0x4c8]
    // 0x7b6260: r0 = PdfArray()
    //     0x7b6260: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7b6264: mov             x1, x0
    // 0x7b6268: stur            x0, [fp, #-8]
    // 0x7b626c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b626c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b6270: r0 = PdfArray()
    //     0x7b6270: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7b6274: ldur            x0, [fp, #-8]
    // 0x7b6278: ldur            x2, [fp, #-0x10]
    // 0x7b627c: r1 = Null
    //     0x7b627c: mov             x1, NULL
    // 0x7b6280: cmp             w2, NULL
    // 0x7b6284: b.eq            #0x7b62a8
    // 0x7b6288: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6288: ldur            w4, [x2, #0x17]
    // 0x7b628c: DecompressPointer r4
    //     0x7b628c: add             x4, x4, HEAP, lsl #32
    // 0x7b6290: r8 = X0 bound PdfDataType
    //     0x7b6290: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6294: ldr             x8, [x8, #0x6d8]
    // 0x7b6298: LoadField: r9 = r4->field_7
    //     0x7b6298: ldur            x9, [x4, #7]
    // 0x7b629c: r3 = Null
    //     0x7b629c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c168] Null
    //     0x7b62a0: ldr             x3, [x3, #0x168]
    // 0x7b62a4: blr             x9
    // 0x7b62a8: ldur            x1, [fp, #-0x20]
    // 0x7b62ac: ldur            x3, [fp, #-8]
    // 0x7b62b0: r2 = "/Fields"
    //     0x7b62b0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c160] "/Fields"
    //     0x7b62b4: ldr             x2, [x2, #0x160]
    // 0x7b62b8: r0 = []=()
    //     0x7b62b8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b62bc: ldur            x0, [fp, #-8]
    // 0x7b62c0: ldur            x3, [fp, #-0x38]
    // 0x7b62c4: r2 = Null
    //     0x7b62c4: mov             x2, NULL
    // 0x7b62c8: r1 = Null
    //     0x7b62c8: mov             x1, NULL
    // 0x7b62cc: r4 = 60
    //     0x7b62cc: movz            x4, #0x3c
    // 0x7b62d0: branchIfSmi(r0, 0x7b62dc)
    //     0x7b62d0: tbz             w0, #0, #0x7b62dc
    // 0x7b62d4: r4 = LoadClassIdInstr(r0)
    //     0x7b62d4: ldur            x4, [x0, #-1]
    //     0x7b62d8: ubfx            x4, x4, #0xc, #0x14
    // 0x7b62dc: cmp             x4, #0x393
    // 0x7b62e0: b.eq            #0x7b62f8
    // 0x7b62e4: r8 = PdfArray<PdfDataType>
    //     0x7b62e4: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c178] Type: PdfArray<PdfDataType>
    //     0x7b62e8: ldr             x8, [x8, #0x178]
    // 0x7b62ec: r3 = Null
    //     0x7b62ec: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c180] Null
    //     0x7b62f0: ldr             x3, [x3, #0x180]
    // 0x7b62f4: r0 = DefaultTypeTest()
    //     0x7b62f4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b62f8: r1 = <PdfDataType>
    //     0x7b62f8: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b62fc: ldr             x1, [x1, #0x4c8]
    // 0x7b6300: r0 = PdfDict()
    //     0x7b6300: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b6304: mov             x1, x0
    // 0x7b6308: stur            x0, [fp, #-8]
    // 0x7b630c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b630c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b6310: r0 = PdfDict()
    //     0x7b6310: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7b6314: ldur            x0, [fp, #-0x38]
    // 0x7b6318: cmp             x0, #0
    // 0x7b631c: b.gt            #0x7b63f4
    // 0x7b6320: ldur            x1, [fp, #-8]
    // 0x7b6324: r0 = isNotEmpty()
    //     0x7b6324: bl              #0x874120  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::isNotEmpty
    // 0x7b6328: tbnz            w0, #4, #0x7b63c8
    // 0x7b632c: ldur            x0, [fp, #-8]
    // 0x7b6330: r1 = Null
    //     0x7b6330: mov             x1, NULL
    // 0x7b6334: r2 = 4
    //     0x7b6334: movz            x2, #0x4
    // 0x7b6338: r0 = AllocateArray()
    //     0x7b6338: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b633c: r16 = "/Font"
    //     0x7b633c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c190] "/Font"
    //     0x7b6340: ldr             x16, [x16, #0x190]
    // 0x7b6344: StoreField: r0->field_f = r16
    //     0x7b6344: stur            w16, [x0, #0xf]
    // 0x7b6348: ldur            x1, [fp, #-8]
    // 0x7b634c: StoreField: r0->field_13 = r1
    //     0x7b634c: stur            w1, [x0, #0x13]
    // 0x7b6350: r16 = <String, PdfDict<PdfDataType>>
    //     0x7b6350: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c198] TypeArguments: <String, PdfDict<PdfDataType>>
    //     0x7b6354: ldr             x16, [x16, #0x198]
    // 0x7b6358: stp             x0, x16, [SP]
    // 0x7b635c: r0 = Map._fromLiteral()
    //     0x7b635c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b6360: r1 = <PdfDict<PdfDataType>>
    //     0x7b6360: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0x7b6364: ldr             x1, [x1, #0x758]
    // 0x7b6368: stur            x0, [fp, #-8]
    // 0x7b636c: r0 = PdfDict()
    //     0x7b636c: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b6370: mov             x3, x0
    // 0x7b6374: ldur            x0, [fp, #-8]
    // 0x7b6378: stur            x3, [fp, #-0x18]
    // 0x7b637c: StoreField: r3->field_b = r0
    //     0x7b637c: stur            w0, [x3, #0xb]
    // 0x7b6380: mov             x0, x3
    // 0x7b6384: ldur            x2, [fp, #-0x10]
    // 0x7b6388: r1 = Null
    //     0x7b6388: mov             x1, NULL
    // 0x7b638c: cmp             w2, NULL
    // 0x7b6390: b.eq            #0x7b63b4
    // 0x7b6394: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6394: ldur            w4, [x2, #0x17]
    // 0x7b6398: DecompressPointer r4
    //     0x7b6398: add             x4, x4, HEAP, lsl #32
    // 0x7b639c: r8 = X0 bound PdfDataType
    //     0x7b639c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b63a0: ldr             x8, [x8, #0x6d8]
    // 0x7b63a4: LoadField: r9 = r4->field_7
    //     0x7b63a4: ldur            x9, [x4, #7]
    // 0x7b63a8: r3 = Null
    //     0x7b63a8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c1a0] Null
    //     0x7b63ac: ldr             x3, [x3, #0x1a0]
    // 0x7b63b0: blr             x9
    // 0x7b63b4: ldur            x1, [fp, #-0x20]
    // 0x7b63b8: ldur            x3, [fp, #-0x18]
    // 0x7b63bc: r2 = "/DR"
    //     0x7b63bc: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c1b0] "/DR"
    //     0x7b63c0: ldr             x2, [x2, #0x1b0]
    // 0x7b63c4: r0 = []=()
    //     0x7b63c4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b63c8: r0 = Null
    //     0x7b63c8: mov             x0, NULL
    // 0x7b63cc: LeaveFrame
    //     0x7b63cc: mov             SP, fp
    //     0x7b63d0: ldp             fp, lr, [SP], #0x10
    // 0x7b63d4: ret
    //     0x7b63d4: ret             
    // 0x7b63d8: mov             x0, x2
    // 0x7b63dc: r1 = 0
    //     0x7b63dc: movz            x1, #0
    // 0x7b63e0: cmp             x1, x0
    // 0x7b63e4: b.hs            #0x7b6430
    // 0x7b63e8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x7b63e8: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x7b63ec: r0 = Throw()
    //     0x7b63ec: bl              #0xec04b8  ; ThrowStub
    // 0x7b63f0: brk             #0
    // 0x7b63f4: r1 = 0
    //     0x7b63f4: movz            x1, #0
    // 0x7b63f8: cmp             x1, x0
    // 0x7b63fc: b.hs            #0x7b6434
    // 0x7b6400: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x7b6400: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x7b6404: r0 = Throw()
    //     0x7b6404: bl              #0xec04b8  ; ThrowStub
    // 0x7b6408: brk             #0
    // 0x7b640c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b640c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6410: b               #0x7b5e60
    // 0x7b6414: r9 = catalog
    //     0x7b6414: add             x9, PP, #0x36, lsl #12  ; [pp+0x365b8] Field <PdfDocument.catalog>: late final (offset: 0x18)
    //     0x7b6418: ldr             x9, [x9, #0x5b8]
    // 0x7b641c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b641c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b6420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6424: b               #0x7b603c
    // 0x7b6428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6428: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b642c: b               #0x7b6070
    // 0x7b6430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b6430: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b6434: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b6434: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ PdfCatalog(/* No info */) {
    // ** addr: 0xe8b344, size: 0xcc
    // 0xe8b344: EnterFrame
    //     0xe8b344: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b348: mov             fp, SP
    // 0xe8b34c: AllocStack(0x28)
    //     0xe8b34c: sub             SP, SP, #0x28
    // 0xe8b350: r4 = Instance_PdfPageMode
    //     0xe8b350: add             x4, PP, #0x36, lsl #12  ; [pp+0x368f8] Obj!PdfPageMode@e2fbc1
    //     0xe8b354: ldr             x4, [x4, #0x8f8]
    // 0xe8b358: mov             x5, x1
    // 0xe8b35c: mov             x0, x3
    // 0xe8b360: mov             x3, x2
    // 0xe8b364: stur            x1, [fp, #-8]
    // 0xe8b368: stur            x2, [fp, #-0x10]
    // 0xe8b36c: CheckStackOverflow
    //     0xe8b36c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b370: cmp             SP, x16
    //     0xe8b374: b.ls            #0xe8b408
    // 0xe8b378: StoreField: r5->field_2b = r0
    //     0xe8b378: stur            w0, [x5, #0x2b]
    //     0xe8b37c: ldurb           w16, [x5, #-1]
    //     0xe8b380: ldurb           w17, [x0, #-1]
    //     0xe8b384: and             x16, x17, x16, lsr #2
    //     0xe8b388: tst             x16, HEAP, lsr #32
    //     0xe8b38c: b.eq            #0xe8b394
    //     0xe8b390: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xe8b394: StoreField: r5->field_3f = r4
    //     0xe8b394: stur            w4, [x5, #0x3f]
    // 0xe8b398: r1 = Null
    //     0xe8b398: mov             x1, NULL
    // 0xe8b39c: r2 = 4
    //     0xe8b39c: movz            x2, #0x4
    // 0xe8b3a0: r0 = AllocateArray()
    //     0xe8b3a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8b3a4: r16 = "/Type"
    //     0xe8b3a4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe8b3a8: ldr             x16, [x16, #0x630]
    // 0xe8b3ac: StoreField: r0->field_f = r16
    //     0xe8b3ac: stur            w16, [x0, #0xf]
    // 0xe8b3b0: r16 = Instance_PdfName
    //     0xe8b3b0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36900] Obj!PdfName@e0c931
    //     0xe8b3b4: ldr             x16, [x16, #0x900]
    // 0xe8b3b8: StoreField: r0->field_13 = r16
    //     0xe8b3b8: stur            w16, [x0, #0x13]
    // 0xe8b3bc: r16 = <String, PdfDataType>
    //     0xe8b3bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe8b3c0: ldr             x16, [x16, #0x820]
    // 0xe8b3c4: stp             x0, x16, [SP]
    // 0xe8b3c8: r0 = Map._fromLiteral()
    //     0xe8b3c8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8b3cc: r1 = <PdfDataType>
    //     0xe8b3cc: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe8b3d0: ldr             x1, [x1, #0x4c8]
    // 0xe8b3d4: stur            x0, [fp, #-0x18]
    // 0xe8b3d8: r0 = PdfDict()
    //     0xe8b3d8: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe8b3dc: mov             x1, x0
    // 0xe8b3e0: ldur            x0, [fp, #-0x18]
    // 0xe8b3e4: StoreField: r1->field_b = r0
    //     0xe8b3e4: stur            w0, [x1, #0xb]
    // 0xe8b3e8: mov             x3, x1
    // 0xe8b3ec: ldur            x1, [fp, #-8]
    // 0xe8b3f0: ldur            x2, [fp, #-0x10]
    // 0xe8b3f4: r0 = PdfObject()
    //     0xe8b3f4: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe8b3f8: r0 = Null
    //     0xe8b3f8: mov             x0, NULL
    // 0xe8b3fc: LeaveFrame
    //     0xe8b3fc: mov             SP, fp
    //     0xe8b400: ldp             fp, lr, [SP], #0x10
    // 0xe8b404: ret
    //     0xe8b404: ret             
    // 0xe8b408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b40c: b               #0xe8b378
  }
}
