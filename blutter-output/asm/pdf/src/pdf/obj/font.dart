// lib: , url: package:pdf/src/pdf/obj/font.dart

// class id: 1050799, size: 0x8
class :: {
}

// class id: 894, size: 0x30, field offset: 0x2c
abstract class PdfFont extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7c913c, size: 0x294
    // 0x7c913c: EnterFrame
    //     0x7c913c: stp             fp, lr, [SP, #-0x10]!
    //     0x7c9140: mov             fp, SP
    // 0x7c9144: AllocStack(0x30)
    //     0x7c9144: sub             SP, SP, #0x30
    // 0x7c9148: SetupParameters(PdfFont this /* r1 => r2, fp-0x18 */)
    //     0x7c9148: mov             x2, x1
    //     0x7c914c: stur            x1, [fp, #-0x18]
    // 0x7c9150: CheckStackOverflow
    //     0x7c9150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9154: cmp             SP, x16
    //     0x7c9158: b.ls            #0x7c93c4
    // 0x7c915c: LoadField: r3 = r2->field_1b
    //     0x7c915c: ldur            w3, [x2, #0x1b]
    // 0x7c9160: DecompressPointer r3
    //     0x7c9160: add             x3, x3, HEAP, lsl #32
    // 0x7c9164: stur            x3, [fp, #-0x10]
    // 0x7c9168: r0 = LoadClassIdInstr(r2)
    //     0x7c9168: ldur            x0, [x2, #-1]
    //     0x7c916c: ubfx            x0, x0, #0xc, #0x14
    // 0x7c9170: cmp             x0, #0x37f
    // 0x7c9174: b.ne            #0x7c9184
    // 0x7c9178: LoadField: r0 = r2->field_2b
    //     0x7c9178: ldur            w0, [x2, #0x2b]
    // 0x7c917c: DecompressPointer r0
    //     0x7c917c: add             x0, x0, HEAP, lsl #32
    // 0x7c9180: b               #0x7c9240
    // 0x7c9184: r7 = 4278255360
    //     0x7c9184: movz            x7, #0xff00
    //     0x7c9188: movk            x7, #0xff00, lsl #16
    // 0x7c918c: r6 = 16711935
    //     0x7c918c: movz            x6, #0xff
    //     0x7c9190: movk            x6, #0xff, lsl #16
    // 0x7c9194: r5 = 4294901760
    //     0x7c9194: orr             x5, xzr, #0xffff0000
    // 0x7c9198: r4 = 65535
    //     0x7c9198: orr             x4, xzr, #0xffff
    // 0x7c919c: LoadField: r0 = r2->field_3f
    //     0x7c919c: ldur            w0, [x2, #0x3f]
    // 0x7c91a0: DecompressPointer r0
    //     0x7c91a0: add             x0, x0, HEAP, lsl #32
    // 0x7c91a4: LoadField: r8 = r0->field_7
    //     0x7c91a4: ldur            w8, [x0, #7]
    // 0x7c91a8: DecompressPointer r8
    //     0x7c91a8: add             x8, x8, HEAP, lsl #32
    // 0x7c91ac: LoadField: r0 = r8->field_13
    //     0x7c91ac: ldur            w0, [x8, #0x13]
    // 0x7c91b0: r1 = LoadInt32Instr(r0)
    //     0x7c91b0: sbfx            x1, x0, #1, #0x1f
    // 0x7c91b4: sub             x0, x1, #3
    // 0x7c91b8: r1 = 0
    //     0x7c91b8: movz            x1, #0
    // 0x7c91bc: cmp             x1, x0
    // 0x7c91c0: b.hs            #0x7c93cc
    // 0x7c91c4: ArrayLoad: r0 = r8[0]  ; List_4
    //     0x7c91c4: ldur            w0, [x8, #0x17]
    // 0x7c91c8: DecompressPointer r0
    //     0x7c91c8: add             x0, x0, HEAP, lsl #32
    // 0x7c91cc: LoadField: r1 = r8->field_1b
    //     0x7c91cc: ldur            w1, [x8, #0x1b]
    // 0x7c91d0: LoadField: r8 = r0->field_7
    //     0x7c91d0: ldur            x8, [x0, #7]
    // 0x7c91d4: asr             w16, w1, #1
    // 0x7c91d8: add             x16, x8, w16, sxtw
    // 0x7c91dc: ldr             w0, [x16]
    // 0x7c91e0: and             x1, x0, x7
    // 0x7c91e4: ubfx            x1, x1, #0, #0x20
    // 0x7c91e8: asr             x7, x1, #8
    // 0x7c91ec: and             x1, x0, x6
    // 0x7c91f0: ubfx            x1, x1, #0, #0x20
    // 0x7c91f4: lsl             x0, x1, #8
    // 0x7c91f8: orr             x1, x7, x0
    // 0x7c91fc: mov             x0, x1
    // 0x7c9200: ubfx            x0, x0, #0, #0x20
    // 0x7c9204: and             x6, x0, x5
    // 0x7c9208: ubfx            x6, x6, #0, #0x20
    // 0x7c920c: asr             x0, x6, #0x10
    // 0x7c9210: ubfx            x1, x1, #0, #0x20
    // 0x7c9214: and             x5, x1, x4
    // 0x7c9218: ubfx            x5, x5, #0, #0x20
    // 0x7c921c: lsl             x1, x5, #0x10
    // 0x7c9220: orr             x4, x0, x1
    // 0x7c9224: cmp             x4, #0x10, lsl #12
    // 0x7c9228: b.ne            #0x7c9238
    // 0x7c922c: r0 = "/Type0"
    //     0x7c922c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e098] "/Type0"
    //     0x7c9230: ldr             x0, [x0, #0x98]
    // 0x7c9234: b               #0x7c9240
    // 0x7c9238: LoadField: r0 = r2->field_2b
    //     0x7c9238: ldur            w0, [x2, #0x2b]
    // 0x7c923c: DecompressPointer r0
    //     0x7c923c: add             x0, x0, HEAP, lsl #32
    // 0x7c9240: stur            x0, [fp, #-8]
    // 0x7c9244: r0 = PdfName()
    //     0x7c9244: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7c9248: mov             x3, x0
    // 0x7c924c: ldur            x0, [fp, #-8]
    // 0x7c9250: stur            x3, [fp, #-0x20]
    // 0x7c9254: StoreField: r3->field_7 = r0
    //     0x7c9254: stur            w0, [x3, #7]
    // 0x7c9258: ldur            x4, [fp, #-0x10]
    // 0x7c925c: LoadField: r5 = r4->field_7
    //     0x7c925c: ldur            w5, [x4, #7]
    // 0x7c9260: DecompressPointer r5
    //     0x7c9260: add             x5, x5, HEAP, lsl #32
    // 0x7c9264: mov             x0, x3
    // 0x7c9268: mov             x2, x5
    // 0x7c926c: stur            x5, [fp, #-8]
    // 0x7c9270: r1 = Null
    //     0x7c9270: mov             x1, NULL
    // 0x7c9274: cmp             w2, NULL
    // 0x7c9278: b.eq            #0x7c929c
    // 0x7c927c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c927c: ldur            w4, [x2, #0x17]
    // 0x7c9280: DecompressPointer r4
    //     0x7c9280: add             x4, x4, HEAP, lsl #32
    // 0x7c9284: r8 = X0 bound PdfDataType
    //     0x7c9284: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9288: ldr             x8, [x8, #0x6d8]
    // 0x7c928c: LoadField: r9 = r4->field_7
    //     0x7c928c: ldur            x9, [x4, #7]
    // 0x7c9290: r3 = Null
    //     0x7c9290: add             x3, PP, #0x47, lsl #12  ; [pp+0x47798] Null
    //     0x7c9294: ldr             x3, [x3, #0x798]
    // 0x7c9298: blr             x9
    // 0x7c929c: ldur            x0, [fp, #-0x10]
    // 0x7c92a0: LoadField: r4 = r0->field_b
    //     0x7c92a0: ldur            w4, [x0, #0xb]
    // 0x7c92a4: DecompressPointer r4
    //     0x7c92a4: add             x4, x4, HEAP, lsl #32
    // 0x7c92a8: mov             x1, x4
    // 0x7c92ac: ldur            x3, [fp, #-0x20]
    // 0x7c92b0: stur            x4, [fp, #-0x28]
    // 0x7c92b4: r2 = "/Subtype"
    //     0x7c92b4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ea78] "/Subtype"
    //     0x7c92b8: ldr             x2, [x2, #0xa78]
    // 0x7c92bc: r0 = []=()
    //     0x7c92bc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c92c0: r1 = Null
    //     0x7c92c0: mov             x1, NULL
    // 0x7c92c4: r2 = 4
    //     0x7c92c4: movz            x2, #0x4
    // 0x7c92c8: r0 = AllocateArray()
    //     0x7c92c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7c92cc: mov             x2, x0
    // 0x7c92d0: r16 = "/F"
    //     0x7c92d0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0x7c92d4: ldr             x16, [x16, #0xda0]
    // 0x7c92d8: StoreField: r2->field_f = r16
    //     0x7c92d8: stur            w16, [x2, #0xf]
    // 0x7c92dc: ldur            x0, [fp, #-0x18]
    // 0x7c92e0: LoadField: r3 = r0->field_b
    //     0x7c92e0: ldur            x3, [x0, #0xb]
    // 0x7c92e4: r0 = BoxInt64Instr(r3)
    //     0x7c92e4: sbfiz           x0, x3, #1, #0x1f
    //     0x7c92e8: cmp             x3, x0, asr #1
    //     0x7c92ec: b.eq            #0x7c92f8
    //     0x7c92f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c92f4: stur            x3, [x0, #7]
    // 0x7c92f8: StoreField: r2->field_13 = r0
    //     0x7c92f8: stur            w0, [x2, #0x13]
    // 0x7c92fc: str             x2, [SP]
    // 0x7c9300: r0 = _interpolate()
    //     0x7c9300: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7c9304: stur            x0, [fp, #-0x10]
    // 0x7c9308: r0 = PdfName()
    //     0x7c9308: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7c930c: mov             x3, x0
    // 0x7c9310: ldur            x0, [fp, #-0x10]
    // 0x7c9314: stur            x3, [fp, #-0x18]
    // 0x7c9318: StoreField: r3->field_7 = r0
    //     0x7c9318: stur            w0, [x3, #7]
    // 0x7c931c: mov             x0, x3
    // 0x7c9320: ldur            x2, [fp, #-8]
    // 0x7c9324: r1 = Null
    //     0x7c9324: mov             x1, NULL
    // 0x7c9328: cmp             w2, NULL
    // 0x7c932c: b.eq            #0x7c9350
    // 0x7c9330: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9330: ldur            w4, [x2, #0x17]
    // 0x7c9334: DecompressPointer r4
    //     0x7c9334: add             x4, x4, HEAP, lsl #32
    // 0x7c9338: r8 = X0 bound PdfDataType
    //     0x7c9338: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c933c: ldr             x8, [x8, #0x6d8]
    // 0x7c9340: LoadField: r9 = r4->field_7
    //     0x7c9340: ldur            x9, [x4, #7]
    // 0x7c9344: r3 = Null
    //     0x7c9344: add             x3, PP, #0x47, lsl #12  ; [pp+0x477a8] Null
    //     0x7c9348: ldr             x3, [x3, #0x7a8]
    // 0x7c934c: blr             x9
    // 0x7c9350: ldur            x1, [fp, #-0x28]
    // 0x7c9354: ldur            x3, [fp, #-0x18]
    // 0x7c9358: r2 = "/Name"
    //     0x7c9358: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb90] "/Name"
    //     0x7c935c: ldr             x2, [x2, #0xb90]
    // 0x7c9360: r0 = []=()
    //     0x7c9360: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9364: ldur            x2, [fp, #-8]
    // 0x7c9368: r0 = Instance_PdfName
    //     0x7c9368: add             x0, PP, #0x47, lsl #12  ; [pp+0x477b8] Obj!PdfName@e0c831
    //     0x7c936c: ldr             x0, [x0, #0x7b8]
    // 0x7c9370: r1 = Null
    //     0x7c9370: mov             x1, NULL
    // 0x7c9374: cmp             w2, NULL
    // 0x7c9378: b.eq            #0x7c939c
    // 0x7c937c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c937c: ldur            w4, [x2, #0x17]
    // 0x7c9380: DecompressPointer r4
    //     0x7c9380: add             x4, x4, HEAP, lsl #32
    // 0x7c9384: r8 = X0 bound PdfDataType
    //     0x7c9384: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9388: ldr             x8, [x8, #0x6d8]
    // 0x7c938c: LoadField: r9 = r4->field_7
    //     0x7c938c: ldur            x9, [x4, #7]
    // 0x7c9390: r3 = Null
    //     0x7c9390: add             x3, PP, #0x47, lsl #12  ; [pp+0x477c0] Null
    //     0x7c9394: ldr             x3, [x3, #0x7c0]
    // 0x7c9398: blr             x9
    // 0x7c939c: ldur            x1, [fp, #-0x28]
    // 0x7c93a0: r2 = "/Encoding"
    //     0x7c93a0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47680] "/Encoding"
    //     0x7c93a4: ldr             x2, [x2, #0x680]
    // 0x7c93a8: r3 = Instance_PdfName
    //     0x7c93a8: add             x3, PP, #0x47, lsl #12  ; [pp+0x477b8] Obj!PdfName@e0c831
    //     0x7c93ac: ldr             x3, [x3, #0x7b8]
    // 0x7c93b0: r0 = []=()
    //     0x7c93b0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c93b4: r0 = Null
    //     0x7c93b4: mov             x0, NULL
    // 0x7c93b8: LeaveFrame
    //     0x7c93b8: mov             SP, fp
    //     0x7c93bc: ldp             fp, lr, [SP], #0x10
    // 0x7c93c0: ret
    //     0x7c93c0: ret             
    // 0x7c93c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c93c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c93c8: b               #0x7c915c
    // 0x7c93cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c93cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ name(/* No info */) {
    // ** addr: 0x7c93d0, size: 0x78
    // 0x7c93d0: EnterFrame
    //     0x7c93d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c93d4: mov             fp, SP
    // 0x7c93d8: AllocStack(0x10)
    //     0x7c93d8: sub             SP, SP, #0x10
    // 0x7c93dc: SetupParameters(PdfFont this /* r1 => r0, fp-0x8 */)
    //     0x7c93dc: mov             x0, x1
    //     0x7c93e0: stur            x1, [fp, #-8]
    // 0x7c93e4: CheckStackOverflow
    //     0x7c93e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c93e8: cmp             SP, x16
    //     0x7c93ec: b.ls            #0x7c9440
    // 0x7c93f0: r1 = Null
    //     0x7c93f0: mov             x1, NULL
    // 0x7c93f4: r2 = 4
    //     0x7c93f4: movz            x2, #0x4
    // 0x7c93f8: r0 = AllocateArray()
    //     0x7c93f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7c93fc: mov             x2, x0
    // 0x7c9400: r16 = "/F"
    //     0x7c9400: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0x7c9404: ldr             x16, [x16, #0xda0]
    // 0x7c9408: StoreField: r2->field_f = r16
    //     0x7c9408: stur            w16, [x2, #0xf]
    // 0x7c940c: ldur            x0, [fp, #-8]
    // 0x7c9410: LoadField: r3 = r0->field_b
    //     0x7c9410: ldur            x3, [x0, #0xb]
    // 0x7c9414: r0 = BoxInt64Instr(r3)
    //     0x7c9414: sbfiz           x0, x3, #1, #0x1f
    //     0x7c9418: cmp             x3, x0, asr #1
    //     0x7c941c: b.eq            #0x7c9428
    //     0x7c9420: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7c9424: stur            x3, [x0, #7]
    // 0x7c9428: StoreField: r2->field_13 = r0
    //     0x7c9428: stur            w0, [x2, #0x13]
    // 0x7c942c: str             x2, [SP]
    // 0x7c9430: r0 = _interpolate()
    //     0x7c9430: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7c9434: LeaveFrame
    //     0x7c9434: mov             SP, fp
    //     0x7c9438: ldp             fp, lr, [SP], #0x10
    // 0x7c943c: ret
    //     0x7c943c: ret             
    // 0x7c9440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9444: b               #0x7c93f0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc35394, size: 0xc0
    // 0xc35394: EnterFrame
    //     0xc35394: stp             fp, lr, [SP, #-0x10]!
    //     0xc35398: mov             fp, SP
    // 0xc3539c: AllocStack(0x10)
    //     0xc3539c: sub             SP, SP, #0x10
    // 0xc353a0: CheckStackOverflow
    //     0xc353a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc353a4: cmp             SP, x16
    //     0xc353a8: b.ls            #0xc3544c
    // 0xc353ac: r1 = Null
    //     0xc353ac: mov             x1, NULL
    // 0xc353b0: r2 = 6
    //     0xc353b0: movz            x2, #0x6
    // 0xc353b4: r0 = AllocateArray()
    //     0xc353b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc353b8: stur            x0, [fp, #-8]
    // 0xc353bc: r16 = "Font("
    //     0xc353bc: add             x16, PP, #0x47, lsl #12  ; [pp+0x47790] "Font("
    //     0xc353c0: ldr             x16, [x16, #0x790]
    // 0xc353c4: StoreField: r0->field_f = r16
    //     0xc353c4: stur            w16, [x0, #0xf]
    // 0xc353c8: ldr             x1, [fp, #0x10]
    // 0xc353cc: r2 = LoadClassIdInstr(r1)
    //     0xc353cc: ldur            x2, [x1, #-1]
    //     0xc353d0: ubfx            x2, x2, #0xc, #0x14
    // 0xc353d4: cmp             x2, #0x37f
    // 0xc353d8: b.ne            #0xc353f4
    // 0xc353dc: LoadField: r2 = r1->field_2f
    //     0xc353dc: ldur            w2, [x1, #0x2f]
    // 0xc353e0: DecompressPointer r2
    //     0xc353e0: add             x2, x2, HEAP, lsl #32
    // 0xc353e4: mov             x16, x0
    // 0xc353e8: mov             x0, x2
    // 0xc353ec: mov             x2, x16
    // 0xc353f0: b               #0xc35408
    // 0xc353f4: LoadField: r2 = r1->field_3f
    //     0xc353f4: ldur            w2, [x1, #0x3f]
    // 0xc353f8: DecompressPointer r2
    //     0xc353f8: add             x2, x2, HEAP, lsl #32
    // 0xc353fc: mov             x1, x2
    // 0xc35400: r0 = fontName()
    //     0xc35400: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0xc35404: ldur            x2, [fp, #-8]
    // 0xc35408: mov             x1, x2
    // 0xc3540c: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3540c: add             x25, x1, #0x13
    //     0xc35410: str             w0, [x25]
    //     0xc35414: tbz             w0, #0, #0xc35430
    //     0xc35418: ldurb           w16, [x1, #-1]
    //     0xc3541c: ldurb           w17, [x0, #-1]
    //     0xc35420: and             x16, x17, x16, lsr #2
    //     0xc35424: tst             x16, HEAP, lsr #32
    //     0xc35428: b.eq            #0xc35430
    //     0xc3542c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35430: r16 = ")"
    //     0xc35430: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc35434: ArrayStore: r2[0] = r16  ; List_4
    //     0xc35434: stur            w16, [x2, #0x17]
    // 0xc35438: str             x2, [SP]
    // 0xc3543c: r0 = _interpolate()
    //     0xc3543c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35440: LeaveFrame
    //     0xc35440: mov             SP, fp
    //     0xc35444: ldp             fp, lr, [SP], #0x10
    // 0xc35448: ret
    //     0xc35448: ret             
    // 0xc3544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3544c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35450: b               #0xc353ac
  }
  _ PdfFont.create(/* No info */) {
    // ** addr: 0xe6604c, size: 0xd4
    // 0xe6604c: EnterFrame
    //     0xe6604c: stp             fp, lr, [SP, #-0x10]!
    //     0xe66050: mov             fp, SP
    // 0xe66054: AllocStack(0x28)
    //     0xe66054: sub             SP, SP, #0x28
    // 0xe66058: SetupParameters(PdfFont this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0xe66058: mov             x4, x1
    //     0xe6605c: mov             x0, x3
    //     0xe66060: mov             x3, x2
    //     0xe66064: stur            x1, [fp, #-8]
    //     0xe66068: stur            x2, [fp, #-0x10]
    // 0xe6606c: CheckStackOverflow
    //     0xe6606c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe66070: cmp             SP, x16
    //     0xe66074: b.ls            #0xe66118
    // 0xe66078: StoreField: r4->field_2b = r0
    //     0xe66078: stur            w0, [x4, #0x2b]
    //     0xe6607c: ldurb           w16, [x4, #-1]
    //     0xe66080: ldurb           w17, [x0, #-1]
    //     0xe66084: and             x16, x17, x16, lsr #2
    //     0xe66088: tst             x16, HEAP, lsr #32
    //     0xe6608c: b.eq            #0xe66094
    //     0xe66090: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe66094: r1 = Null
    //     0xe66094: mov             x1, NULL
    // 0xe66098: r2 = 4
    //     0xe66098: movz            x2, #0x4
    // 0xe6609c: r0 = AllocateArray()
    //     0xe6609c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe660a0: r16 = "/Type"
    //     0xe660a0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe660a4: ldr             x16, [x16, #0x630]
    // 0xe660a8: StoreField: r0->field_f = r16
    //     0xe660a8: stur            w16, [x0, #0xf]
    // 0xe660ac: r16 = Instance_PdfName
    //     0xe660ac: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df40] Obj!PdfName@e0c821
    //     0xe660b0: ldr             x16, [x16, #0xf40]
    // 0xe660b4: StoreField: r0->field_13 = r16
    //     0xe660b4: stur            w16, [x0, #0x13]
    // 0xe660b8: r16 = <String, PdfDataType>
    //     0xe660b8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe660bc: ldr             x16, [x16, #0x820]
    // 0xe660c0: stp             x0, x16, [SP]
    // 0xe660c4: r0 = Map._fromLiteral()
    //     0xe660c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe660c8: r1 = <PdfDataType>
    //     0xe660c8: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe660cc: ldr             x1, [x1, #0x4c8]
    // 0xe660d0: stur            x0, [fp, #-0x18]
    // 0xe660d4: r0 = PdfDict()
    //     0xe660d4: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe660d8: mov             x1, x0
    // 0xe660dc: ldur            x0, [fp, #-0x18]
    // 0xe660e0: StoreField: r1->field_b = r0
    //     0xe660e0: stur            w0, [x1, #0xb]
    // 0xe660e4: mov             x3, x1
    // 0xe660e8: ldur            x1, [fp, #-8]
    // 0xe660ec: ldur            x2, [fp, #-0x10]
    // 0xe660f0: r0 = PdfObject()
    //     0xe660f0: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe660f4: ldur            x0, [fp, #-0x10]
    // 0xe660f8: LoadField: r1 = r0->field_2f
    //     0xe660f8: ldur            w1, [x0, #0x2f]
    // 0xe660fc: DecompressPointer r1
    //     0xe660fc: add             x1, x1, HEAP, lsl #32
    // 0xe66100: ldur            x2, [fp, #-8]
    // 0xe66104: r0 = add()
    //     0xe66104: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xe66108: r0 = Null
    //     0xe66108: mov             x0, NULL
    // 0xe6610c: LeaveFrame
    //     0xe6610c: mov             SP, fp
    //     0xe66110: ldp             fp, lr, [SP], #0x10
    // 0xe66114: ret
    //     0xe66114: ret             
    // 0xe66118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe66118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6611c: b               #0xe66078
  }
  factory _ PdfFont.zapfDingbats(/* No info */) {
    // ** addr: 0xe6643c, size: 0xe8
    // 0xe6643c: EnterFrame
    //     0xe6643c: stp             fp, lr, [SP, #-0x10]!
    //     0xe66440: mov             fp, SP
    // 0xe66444: AllocStack(0x28)
    //     0xe66444: sub             SP, SP, #0x28
    // 0xe66448: r0 = 8
    //     0xe66448: movz            x0, #0x8
    // 0xe6644c: mov             x3, x2
    // 0xe66450: stur            x2, [fp, #-8]
    // 0xe66454: CheckStackOverflow
    //     0xe66454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe66458: cmp             SP, x16
    //     0xe6645c: b.ls            #0xe6651c
    // 0xe66460: mov             x2, x0
    // 0xe66464: r1 = Null
    //     0xe66464: mov             x1, NULL
    // 0xe66468: r0 = AllocateArray()
    //     0xe66468: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6646c: stur            x0, [fp, #-0x10]
    // 0xe66470: r16 = -2
    //     0xe66470: orr             x16, xzr, #0xfffffffffffffffe
    // 0xe66474: StoreField: r0->field_f = r16
    //     0xe66474: stur            w16, [x0, #0xf]
    // 0xe66478: r16 = -286
    //     0xe66478: movn            x16, #0x11d
    // 0xe6647c: StoreField: r0->field_13 = r16
    //     0xe6647c: stur            w16, [x0, #0x13]
    // 0xe66480: r16 = 1962
    //     0xe66480: movz            x16, #0x7aa
    // 0xe66484: ArrayStore: r0[0] = r16  ; List_4
    //     0xe66484: stur            w16, [x0, #0x17]
    // 0xe66488: r16 = 1640
    //     0xe66488: movz            x16, #0x668
    // 0xe6648c: StoreField: r0->field_1b = r16
    //     0xe6648c: stur            w16, [x0, #0x1b]
    // 0xe66490: r1 = <int>
    //     0xe66490: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe66494: r0 = AllocateGrowableArray()
    //     0xe66494: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe66498: mov             x2, x0
    // 0xe6649c: ldur            x0, [fp, #-0x10]
    // 0xe664a0: stur            x2, [fp, #-0x18]
    // 0xe664a4: StoreField: r2->field_f = r0
    //     0xe664a4: stur            w0, [x2, #0xf]
    // 0xe664a8: r0 = 8
    //     0xe664a8: movz            x0, #0x8
    // 0xe664ac: StoreField: r2->field_b = r0
    //     0xe664ac: stur            w0, [x2, #0xb]
    // 0xe664b0: r1 = <PdfDict<PdfDataType>>
    //     0xe664b0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe664b4: ldr             x1, [x1, #0x758]
    // 0xe664b8: r0 = PdfType1Font()
    //     0xe664b8: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe664bc: mov             x4, x0
    // 0xe664c0: r0 = 90
    //     0xe664c0: movz            x0, #0x5a
    // 0xe664c4: stur            x4, [fp, #-0x10]
    // 0xe664c8: r16 = const [0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.278, 0.974, 0.961, 0.974, 0.98, 0.719, 0.789, 0.79, 0.791, 0.69, 0.96, 0.939, 0.549, 0.855, 0.911, 0.933, 0.911, 0.945, 0.974, 0.755, 0.846, 0.762, 0.761, 0.571, 0.677, 0.763, 0.76, 0.759, 0.754, 0.494, 0.552, 0.537, 0.577, 0.692, 0.786, 0.788, 0.788, 0.79, 0.793, 0.794, 0.816, 0.823, 0.789, 0.841, 0.823, 0.833, 0.816, 0.831, 0.923, 0.744, 0.723, 0.749, 0.79, 0.792, 0.695, 0.776, 0.768, 0.792, 0.759, 0.707, 0.708, 0.682, 0.701, 0.826, 0.815, 0.789, 0.789, 0.707, 0.687, 0.696, 0.689, 0.786, 0.787, 0.713, 0.791, 0.785, 0.791, 0.873, 0.761, 0.762, 0.762, 0.759, 0.759, 0.892, 0.892, 0.788, 0.784, 0.438, 0.138, 0.277, 0.415, 0.392, 0.392, 0.668, 0.668, 0.746, 0.39, 0.39, 0.317, 0.317, 0.276, 0.276, 0.509, 0.509, 0.41, 0.41, 0.234, 0.234, 0.334, 0.334, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.746, 0.732, 0.544, 0.544, 0.91, 0.667, 0.76, 0.76, 0.776, 0.595, 0.694, 0.626, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.788, 0.894, 0.838, 1.016, 0.458, 0.748, 0.924, 0.748, 0.918, 0.927, 0.928, 0.928, 0.834, 0.873, 0.828, 0.924, 0.924, 0.917, 0.93, 0.931, 0.463, 0.883, 0.836, 0.836, 0.867, 0.867, 0.696, 0.696, 0.874, 0.746, 0.874, 0.76, 0.946, 0.771, 0.865, 0.771, 0.888, 0.967, 0.888, 0.831, 0.873, 0.927, 0.97, 0.918, 0.746]
    //     0xe664c8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de08] List<double>(256)
    //     0xe664cc: ldr             x16, [x16, #0xe08]
    // 0xe664d0: stp             x16, x0, [SP]
    // 0xe664d4: mov             x1, x4
    // 0xe664d8: ldur            x2, [fp, #-8]
    // 0xe664dc: ldur            x5, [fp, #-0x18]
    // 0xe664e0: d0 = 0.820000
    //     0xe664e0: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3de10] IMM: double(0.82) from 0x3fea3d70a3d70a3d
    //     0xe664e4: ldr             d0, [x17, #0xe10]
    // 0xe664e8: r3 = 653
    //     0xe664e8: movz            x3, #0x28d
    // 0xe664ec: d1 = -0.143000
    //     0xe664ec: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3de18] IMM: double(-0.143) from 0xbfc24dd2f1a9fbe7
    //     0xe664f0: ldr             d1, [x17, #0xe18]
    // 0xe664f4: r6 = "ZapfDingbats"
    //     0xe664f4: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3de20] "ZapfDingbats"
    //     0xe664f8: ldr             x6, [x6, #0xe20]
    // 0xe664fc: r7 = 28
    //     0xe664fc: movz            x7, #0x1c
    // 0xe66500: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe66500: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe66504: ldr             x4, [x4, #0xe28]
    // 0xe66508: r0 = PdfType1Font.create()
    //     0xe66508: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe6650c: ldur            x0, [fp, #-0x10]
    // 0xe66510: LeaveFrame
    //     0xe66510: mov             SP, fp
    //     0xe66514: ldp             fp, lr, [SP], #0x10
    // 0xe66518: ret
    //     0xe66518: ret             
    // 0xe6651c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6651c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe66520: b               #0xe66460
  }
  factory _ PdfFont.symbol(/* No info */) {
    // ** addr: 0xe67004, size: 0xe8
    // 0xe67004: EnterFrame
    //     0xe67004: stp             fp, lr, [SP, #-0x10]!
    //     0xe67008: mov             fp, SP
    // 0xe6700c: AllocStack(0x28)
    //     0xe6700c: sub             SP, SP, #0x28
    // 0xe67010: r0 = 8
    //     0xe67010: movz            x0, #0x8
    // 0xe67014: mov             x3, x2
    // 0xe67018: stur            x2, [fp, #-8]
    // 0xe6701c: CheckStackOverflow
    //     0xe6701c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67020: cmp             SP, x16
    //     0xe67024: b.ls            #0xe670e4
    // 0xe67028: mov             x2, x0
    // 0xe6702c: r1 = Null
    //     0xe6702c: mov             x1, NULL
    // 0xe67030: r0 = AllocateArray()
    //     0xe67030: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67034: stur            x0, [fp, #-0x10]
    // 0xe67038: r16 = -360
    //     0xe67038: movn            x16, #0x167
    // 0xe6703c: StoreField: r0->field_f = r16
    //     0xe6703c: stur            w16, [x0, #0xf]
    // 0xe67040: r16 = -586
    //     0xe67040: movn            x16, #0x249
    // 0xe67044: StoreField: r0->field_13 = r16
    //     0xe67044: stur            w16, [x0, #0x13]
    // 0xe67048: r16 = 2180
    //     0xe67048: movz            x16, #0x884
    // 0xe6704c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe6704c: stur            w16, [x0, #0x17]
    // 0xe67050: r16 = 2020
    //     0xe67050: movz            x16, #0x7e4
    // 0xe67054: StoreField: r0->field_1b = r16
    //     0xe67054: stur            w16, [x0, #0x1b]
    // 0xe67058: r1 = <int>
    //     0xe67058: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6705c: r0 = AllocateGrowableArray()
    //     0xe6705c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67060: mov             x2, x0
    // 0xe67064: ldur            x0, [fp, #-0x10]
    // 0xe67068: stur            x2, [fp, #-0x18]
    // 0xe6706c: StoreField: r2->field_f = r0
    //     0xe6706c: stur            w0, [x2, #0xf]
    // 0xe67070: r0 = 8
    //     0xe67070: movz            x0, #0x8
    // 0xe67074: StoreField: r2->field_b = r0
    //     0xe67074: stur            w0, [x2, #0xb]
    // 0xe67078: r1 = <PdfDict<PdfDataType>>
    //     0xe67078: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe6707c: ldr             x1, [x1, #0x758]
    // 0xe67080: r0 = PdfType1Font()
    //     0xe67080: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67084: mov             x4, x0
    // 0xe67088: r0 = 85
    //     0xe67088: movz            x0, #0x55
    // 0xe6708c: stur            x4, [fp, #-0x10]
    // 0xe67090: r16 = const [0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.25, 0.333, 0.713, 0.5, 0.549, 0.833, 0.778, 0.439, 0.333, 0.333, 0.5, 0.549, 0.25, 0.549, 0.25, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.278, 0.278, 0.549, 0.549, 0.549, 0.444, 0.549, 0.722, 0.667, 0.722, 0.612, 0.611, 0.763, 0.603, 0.722, 0.333, 0.631, 0.722, 0.686, 0.889, 0.722, 0.722, 0.768, 0.741, 0.556, 0.592, 0.611, 0.69, 0.439, 0.768, 0.645, 0.795, 0.611, 0.333, 0.863, 0.333, 0.658, 0.5, 0.5, 0.631, 0.549, 0.549, 0.494, 0.439, 0.521, 0.411, 0.603, 0.329, 0.603, 0.549, 0.549, 0.576, 0.521, 0.549, 0.549, 0.521, 0.549, 0.603, 0.439, 0.576, 0.713, 0.686, 0.493, 0.686, 0.494, 0.48, 0.2, 0.48, 0.549, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.587, 0.75, 0.62, 0.247, 0.549, 0.167, 0.713, 0.5, 0.753, 0.753, 0.753, 0.753, 1.042, 0.987, 0.603, 0.987, 0.603, 0.4, 0.549, 0.411, 0.549, 0.549, 0.713, 0.494, 0.46, 0.549, 0.549, 0.549, 0.549, 1.0, 0.603, 1.0, 0.658, 0.823, 0.686, 0.795, 0.987, 0.768, 0.768, 0.823, 0.768, 0.768, 0.713, 0.713, 0.713, 0.713, 0.713, 0.713, 0.713, 0.768, 0.713, 0.79, 0.79, 0.89, 0.823, 0.549, 0.25, 0.713, 0.603, 0.603, 1.042, 0.987, 0.603, 0.987, 0.603, 0.494, 0.329, 0.79, 0.79, 0.786, 0.713, 0.384, 0.384, 0.384, 0.384, 0.384, 0.384, 0.494, 0.494, 0.494, 0.494, 0.587, 0.329, 0.274, 0.686, 0.686, 0.686, 0.384, 0.384, 0.384, 0.384, 0.384, 0.384, 0.494, 0.494, 0.494, 0.587]
    //     0xe67090: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df48] List<double>(256)
    //     0xe67094: ldr             x16, [x16, #0xf48]
    // 0xe67098: stp             x16, x0, [SP]
    // 0xe6709c: mov             x1, x4
    // 0xe670a0: ldur            x2, [fp, #-8]
    // 0xe670a4: ldur            x5, [fp, #-0x18]
    // 0xe670a8: d0 = 1.010000
    //     0xe670a8: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3df50] IMM: double(1.01) from 0x3ff028f5c28f5c29
    //     0xe670ac: ldr             d0, [x17, #0xf50]
    // 0xe670b0: r3 = 653
    //     0xe670b0: movz            x3, #0x28d
    // 0xe670b4: d1 = -0.293000
    //     0xe670b4: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3df58] IMM: double(-0.293) from 0xbfd2c083126e978d
    //     0xe670b8: ldr             d1, [x17, #0xf58]
    // 0xe670bc: r6 = "Symbol"
    //     0xe670bc: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3df60] "Symbol"
    //     0xe670c0: ldr             x6, [x6, #0xf60]
    // 0xe670c4: r7 = 92
    //     0xe670c4: movz            x7, #0x5c
    // 0xe670c8: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe670c8: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe670cc: ldr             x4, [x4, #0xe28]
    // 0xe670d0: r0 = PdfType1Font.create()
    //     0xe670d0: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe670d4: ldur            x0, [fp, #-0x10]
    // 0xe670d8: LeaveFrame
    //     0xe670d8: mov             SP, fp
    //     0xe670dc: ldp             fp, lr, [SP], #0x10
    // 0xe670e0: ret
    //     0xe670e0: ret             
    // 0xe670e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe670e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe670e8: b               #0xe67028
  }
  factory _ PdfFont.timesItalic(/* No info */) {
    // ** addr: 0xe670ec, size: 0xf4
    // 0xe670ec: EnterFrame
    //     0xe670ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe670f0: mov             fp, SP
    // 0xe670f4: AllocStack(0x30)
    //     0xe670f4: sub             SP, SP, #0x30
    // 0xe670f8: r0 = 8
    //     0xe670f8: movz            x0, #0x8
    // 0xe670fc: mov             x3, x2
    // 0xe67100: stur            x2, [fp, #-8]
    // 0xe67104: CheckStackOverflow
    //     0xe67104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67108: cmp             SP, x16
    //     0xe6710c: b.ls            #0xe671d8
    // 0xe67110: mov             x2, x0
    // 0xe67114: r1 = Null
    //     0xe67114: mov             x1, NULL
    // 0xe67118: r0 = AllocateArray()
    //     0xe67118: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6711c: stur            x0, [fp, #-0x10]
    // 0xe67120: r16 = -338
    //     0xe67120: movn            x16, #0x151
    // 0xe67124: StoreField: r0->field_f = r16
    //     0xe67124: stur            w16, [x0, #0xf]
    // 0xe67128: r16 = -434
    //     0xe67128: movn            x16, #0x1b1
    // 0xe6712c: StoreField: r0->field_13 = r16
    //     0xe6712c: stur            w16, [x0, #0x13]
    // 0xe67130: r16 = 2020
    //     0xe67130: movz            x16, #0x7e4
    // 0xe67134: ArrayStore: r0[0] = r16  ; List_4
    //     0xe67134: stur            w16, [x0, #0x17]
    // 0xe67138: r16 = 1766
    //     0xe67138: movz            x16, #0x6e6
    // 0xe6713c: StoreField: r0->field_1b = r16
    //     0xe6713c: stur            w16, [x0, #0x1b]
    // 0xe67140: r1 = <int>
    //     0xe67140: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe67144: r0 = AllocateGrowableArray()
    //     0xe67144: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67148: mov             x2, x0
    // 0xe6714c: ldur            x0, [fp, #-0x10]
    // 0xe67150: stur            x2, [fp, #-0x18]
    // 0xe67154: StoreField: r2->field_f = r0
    //     0xe67154: stur            w0, [x2, #0xf]
    // 0xe67158: r0 = 8
    //     0xe67158: movz            x0, #0x8
    // 0xe6715c: StoreField: r2->field_b = r0
    //     0xe6715c: stur            w0, [x2, #0xb]
    // 0xe67160: r1 = <PdfDict<PdfDataType>>
    //     0xe67160: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67164: ldr             x1, [x1, #0x758]
    // 0xe67168: r0 = PdfType1Font()
    //     0xe67168: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe6716c: mov             x4, x0
    // 0xe67170: r0 = 76
    //     0xe67170: movz            x0, #0x4c
    // 0xe67174: stur            x4, [fp, #-0x10]
    // 0xe67178: r16 = const [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.333, 0.42, 0.5, 0.5, 0.833, 0.778, 0.214, 0.333, 0.333, 0.5, 0.675, 0.25, 0.333, 0.25, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.333, 0.333, 0.675, 0.675, 0.675, 0.5, 0.92, 0.611, 0.611, 0.667, 0.722, 0.611, 0.611, 0.722, 0.722, 0.333, 0.444, 0.667, 0.556, 0.833, 0.667, 0.722, 0.611, 0.722, 0.611, 0.5, 0.556, 0.722, 0.611, 0.833, 0.611, 0.556, 0.556, 0.389, 0.278, 0.389, 0.422, 0.5, 0.333, 0.5, 0.5, 0.444, 0.5, 0.444, 0.278, 0.5, 0.5, 0.278, 0.278, 0.444, 0.278, 0.722, 0.5, 0.5, 0.5, 0.5, 0.389, 0.389, 0.278, 0.5, 0.444, 0.667, 0.444, 0.444, 0.389, 0.4, 0.275, 0.4, 0.541, 0.35, 0.5, 0.35, 0.333, 0.5, 0.556, 0.889, 0.5, 0.5, 0.333, 1.0, 0.5, 0.333, 0.944, 0.35, 0.556, 0.35, 0.35, 0.333, 0.333, 0.556, 0.556, 0.35, 0.5, 0.889, 0.333, 0.98, 0.389, 0.333, 0.667, 0.35, 0.389, 0.556, 0.25, 0.389, 0.5, 0.5, 0.5, 0.5, 0.275, 0.5, 0.333, 0.76, 0.276, 0.5, 0.675, 0.333, 0.76, 0.333, 0.4, 0.675, 0.3, 0.3, 0.333, 0.5, 0.523, 0.25, 0.333, 0.3, 0.31, 0.5, 0.75, 0.75, 0.75, 0.5, 0.611, 0.611, 0.611, 0.611, 0.611, 0.611, 0.889, 0.667, 0.611, 0.611, 0.611, 0.611, 0.333, 0.333, 0.333, 0.333, 0.722, 0.667, 0.722, 0.722, 0.722, 0.722, 0.722, 0.675, 0.722, 0.722, 0.722, 0.722, 0.722, 0.556, 0.611, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.667, 0.444, 0.444, 0.444, 0.444, 0.444, 0.278, 0.278, 0.278, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.675, 0.5, 0.5, 0.5, 0.5, 0.5, 0.444, 0.5, 0.444]
    //     0xe67178: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df68] List<double>(256)
    //     0xe6717c: ldr             x16, [x16, #0xf68]
    // 0xe67180: stp             x16, x0, [SP, #8]
    // 0xe67184: r16 = -15.500000
    //     0xe67184: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df70] -15.5
    //     0xe67188: ldr             x16, [x16, #0xf70]
    // 0xe6718c: str             x16, [SP]
    // 0xe67190: mov             x1, x4
    // 0xe67194: ldur            x2, [fp, #-8]
    // 0xe67198: ldur            x5, [fp, #-0x18]
    // 0xe6719c: d0 = 0.883000
    //     0xe6719c: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3df78] IMM: double(0.883) from 0x3fec4189374bc6a8
    //     0xe671a0: ldr             d0, [x17, #0xf78]
    // 0xe671a4: r3 = 653
    //     0xe671a4: movz            x3, #0x28d
    // 0xe671a8: d1 = -0.217000
    //     0xe671a8: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3df80] IMM: double(-0.217) from 0xbfcbc6a7ef9db22d
    //     0xe671ac: ldr             d1, [x17, #0xf80]
    // 0xe671b0: r6 = "Times-Italic"
    //     0xe671b0: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3df88] "Times-Italic"
    //     0xe671b4: ldr             x6, [x6, #0xf88]
    // 0xe671b8: r7 = 32
    //     0xe671b8: movz            x7, #0x20
    // 0xe671bc: r4 = const [0, 0xb, 0x3, 0x9, italicAngle, 0xa, widths, 0x9, null]
    //     0xe671bc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3df90] List(9) [0, 0xb, 0x3, 0x9, "italicAngle", 0xa, "widths", 0x9, Null]
    //     0xe671c0: ldr             x4, [x4, #0xf90]
    // 0xe671c4: r0 = PdfType1Font.create()
    //     0xe671c4: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe671c8: ldur            x0, [fp, #-0x10]
    // 0xe671cc: LeaveFrame
    //     0xe671cc: mov             SP, fp
    //     0xe671d0: ldp             fp, lr, [SP], #0x10
    // 0xe671d4: ret
    //     0xe671d4: ret             
    // 0xe671d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe671d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe671dc: b               #0xe67110
  }
  factory _ PdfFont.timesBoldItalic(/* No info */) {
    // ** addr: 0xe671e0, size: 0xf4
    // 0xe671e0: EnterFrame
    //     0xe671e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe671e4: mov             fp, SP
    // 0xe671e8: AllocStack(0x30)
    //     0xe671e8: sub             SP, SP, #0x30
    // 0xe671ec: r0 = 8
    //     0xe671ec: movz            x0, #0x8
    // 0xe671f0: mov             x3, x2
    // 0xe671f4: stur            x2, [fp, #-8]
    // 0xe671f8: CheckStackOverflow
    //     0xe671f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe671fc: cmp             SP, x16
    //     0xe67200: b.ls            #0xe672cc
    // 0xe67204: mov             x2, x0
    // 0xe67208: r1 = Null
    //     0xe67208: mov             x1, NULL
    // 0xe6720c: r0 = AllocateArray()
    //     0xe6720c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67210: stur            x0, [fp, #-0x10]
    // 0xe67214: r16 = -400
    //     0xe67214: movn            x16, #0x18f
    // 0xe67218: StoreField: r0->field_f = r16
    //     0xe67218: stur            w16, [x0, #0xf]
    // 0xe6721c: r16 = -436
    //     0xe6721c: movn            x16, #0x1b3
    // 0xe67220: StoreField: r0->field_13 = r16
    //     0xe67220: stur            w16, [x0, #0x13]
    // 0xe67224: r16 = 1992
    //     0xe67224: movz            x16, #0x7c8
    // 0xe67228: ArrayStore: r0[0] = r16  ; List_4
    //     0xe67228: stur            w16, [x0, #0x17]
    // 0xe6722c: r16 = 1842
    //     0xe6722c: movz            x16, #0x732
    // 0xe67230: StoreField: r0->field_1b = r16
    //     0xe67230: stur            w16, [x0, #0x1b]
    // 0xe67234: r1 = <int>
    //     0xe67234: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe67238: r0 = AllocateGrowableArray()
    //     0xe67238: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6723c: mov             x2, x0
    // 0xe67240: ldur            x0, [fp, #-0x10]
    // 0xe67244: stur            x2, [fp, #-0x18]
    // 0xe67248: StoreField: r2->field_f = r0
    //     0xe67248: stur            w0, [x2, #0xf]
    // 0xe6724c: r0 = 8
    //     0xe6724c: movz            x0, #0x8
    // 0xe67250: StoreField: r2->field_b = r0
    //     0xe67250: stur            w0, [x2, #0xb]
    // 0xe67254: r1 = <PdfDict<PdfDataType>>
    //     0xe67254: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67258: ldr             x1, [x1, #0x758]
    // 0xe6725c: r0 = PdfType1Font()
    //     0xe6725c: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67260: mov             x4, x0
    // 0xe67264: r0 = 121
    //     0xe67264: movz            x0, #0x79
    // 0xe67268: stur            x4, [fp, #-0x10]
    // 0xe6726c: r16 = const [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.389, 0.555, 0.5, 0.5, 0.833, 0.778, 0.278, 0.333, 0.333, 0.5, 0.57, 0.25, 0.333, 0.25, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.333, 0.333, 0.57, 0.57, 0.57, 0.5, 0.832, 0.667, 0.667, 0.667, 0.722, 0.667, 0.667, 0.722, 0.778, 0.389, 0.5, 0.667, 0.611, 0.889, 0.722, 0.722, 0.611, 0.722, 0.667, 0.556, 0.611, 0.722, 0.667, 0.889, 0.667, 0.611, 0.611, 0.333, 0.278, 0.333, 0.57, 0.5, 0.333, 0.5, 0.5, 0.444, 0.5, 0.444, 0.333, 0.5, 0.556, 0.278, 0.278, 0.5, 0.278, 0.778, 0.556, 0.5, 0.5, 0.5, 0.389, 0.389, 0.278, 0.556, 0.444, 0.667, 0.5, 0.444, 0.389, 0.348, 0.22, 0.348, 0.57, 0.35, 0.5, 0.35, 0.333, 0.5, 0.5, 1.0, 0.5, 0.5, 0.333, 1.0, 0.556, 0.333, 0.944, 0.35, 0.611, 0.35, 0.35, 0.333, 0.333, 0.5, 0.5, 0.35, 0.5, 1.0, 0.333, 1.0, 0.389, 0.333, 0.722, 0.35, 0.389, 0.611, 0.25, 0.389, 0.5, 0.5, 0.5, 0.5, 0.22, 0.5, 0.333, 0.747, 0.266, 0.5, 0.606, 0.333, 0.747, 0.333, 0.4, 0.57, 0.3, 0.3, 0.333, 0.576, 0.5, 0.25, 0.333, 0.3, 0.3, 0.5, 0.75, 0.75, 0.75, 0.5, 0.667, 0.667, 0.667, 0.667, 0.667, 0.667, 0.944, 0.667, 0.667, 0.667, 0.667, 0.667, 0.389, 0.389, 0.389, 0.389, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.57, 0.722, 0.722, 0.722, 0.722, 0.722, 0.611, 0.611, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.722, 0.444, 0.444, 0.444, 0.444, 0.444, 0.278, 0.278, 0.278, 0.278, 0.5, 0.556, 0.5, 0.5, 0.5, 0.5, 0.5, 0.57, 0.5, 0.556, 0.556, 0.556, 0.556, 0.444, 0.5, 0.444]
    //     0xe6726c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df98] List<double>(256)
    //     0xe67270: ldr             x16, [x16, #0xf98]
    // 0xe67274: stp             x16, x0, [SP, #8]
    // 0xe67278: r16 = -15.000000
    //     0xe67278: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dfa0] -15
    //     0xe6727c: ldr             x16, [x16, #0xfa0]
    // 0xe67280: str             x16, [SP]
    // 0xe67284: mov             x1, x4
    // 0xe67288: ldur            x2, [fp, #-8]
    // 0xe6728c: ldur            x5, [fp, #-0x18]
    // 0xe67290: d0 = 0.921000
    //     0xe67290: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfa8] IMM: double(0.921) from 0x3fed78d4fdf3b646
    //     0xe67294: ldr             d0, [x17, #0xfa8]
    // 0xe67298: r3 = 669
    //     0xe67298: movz            x3, #0x29d
    // 0xe6729c: d1 = -0.218000
    //     0xe6729c: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfb0] IMM: double(-0.218) from 0xbfcbe76c8b439581
    //     0xe672a0: ldr             d1, [x17, #0xfb0]
    // 0xe672a4: r6 = "Times-BoldItalic"
    //     0xe672a4: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3dfb8] "Times-BoldItalic"
    //     0xe672a8: ldr             x6, [x6, #0xfb8]
    // 0xe672ac: r7 = 42
    //     0xe672ac: movz            x7, #0x2a
    // 0xe672b0: r4 = const [0, 0xb, 0x3, 0x9, italicAngle, 0xa, widths, 0x9, null]
    //     0xe672b0: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3df90] List(9) [0, 0xb, 0x3, 0x9, "italicAngle", 0xa, "widths", 0x9, Null]
    //     0xe672b4: ldr             x4, [x4, #0xf90]
    // 0xe672b8: r0 = PdfType1Font.create()
    //     0xe672b8: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe672bc: ldur            x0, [fp, #-0x10]
    // 0xe672c0: LeaveFrame
    //     0xe672c0: mov             SP, fp
    //     0xe672c4: ldp             fp, lr, [SP], #0x10
    // 0xe672c8: ret
    //     0xe672c8: ret             
    // 0xe672cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe672cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe672d0: b               #0xe67204
  }
  factory _ PdfFont.timesBold(/* No info */) {
    // ** addr: 0xe672d4, size: 0xe8
    // 0xe672d4: EnterFrame
    //     0xe672d4: stp             fp, lr, [SP, #-0x10]!
    //     0xe672d8: mov             fp, SP
    // 0xe672dc: AllocStack(0x28)
    //     0xe672dc: sub             SP, SP, #0x28
    // 0xe672e0: r0 = 8
    //     0xe672e0: movz            x0, #0x8
    // 0xe672e4: mov             x3, x2
    // 0xe672e8: stur            x2, [fp, #-8]
    // 0xe672ec: CheckStackOverflow
    //     0xe672ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe672f0: cmp             SP, x16
    //     0xe672f4: b.ls            #0xe673b4
    // 0xe672f8: mov             x2, x0
    // 0xe672fc: r1 = Null
    //     0xe672fc: mov             x1, NULL
    // 0xe67300: r0 = AllocateArray()
    //     0xe67300: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67304: stur            x0, [fp, #-0x10]
    // 0xe67308: r16 = -336
    //     0xe67308: movn            x16, #0x14f
    // 0xe6730c: StoreField: r0->field_f = r16
    //     0xe6730c: stur            w16, [x0, #0xf]
    // 0xe67310: r16 = -436
    //     0xe67310: movn            x16, #0x1b3
    // 0xe67314: StoreField: r0->field_13 = r16
    //     0xe67314: stur            w16, [x0, #0x13]
    // 0xe67318: r16 = 2000
    //     0xe67318: movz            x16, #0x7d0
    // 0xe6731c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe6731c: stur            w16, [x0, #0x17]
    // 0xe67320: r16 = 1870
    //     0xe67320: movz            x16, #0x74e
    // 0xe67324: StoreField: r0->field_1b = r16
    //     0xe67324: stur            w16, [x0, #0x1b]
    // 0xe67328: r1 = <int>
    //     0xe67328: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6732c: r0 = AllocateGrowableArray()
    //     0xe6732c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67330: mov             x2, x0
    // 0xe67334: ldur            x0, [fp, #-0x10]
    // 0xe67338: stur            x2, [fp, #-0x18]
    // 0xe6733c: StoreField: r2->field_f = r0
    //     0xe6733c: stur            w0, [x2, #0xf]
    // 0xe67340: r0 = 8
    //     0xe67340: movz            x0, #0x8
    // 0xe67344: StoreField: r2->field_b = r0
    //     0xe67344: stur            w0, [x2, #0xb]
    // 0xe67348: r1 = <PdfDict<PdfDataType>>
    //     0xe67348: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe6734c: ldr             x1, [x1, #0x758]
    // 0xe67350: r0 = PdfType1Font()
    //     0xe67350: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67354: mov             x4, x0
    // 0xe67358: r0 = 139
    //     0xe67358: movz            x0, #0x8b
    // 0xe6735c: stur            x4, [fp, #-0x10]
    // 0xe67360: r16 = const [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.333, 0.555, 0.5, 0.5, 1.0, 0.833, 0.278, 0.333, 0.333, 0.5, 0.57, 0.25, 0.333, 0.25, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.333, 0.333, 0.57, 0.57, 0.57, 0.5, 0.93, 0.722, 0.667, 0.722, 0.722, 0.667, 0.611, 0.778, 0.778, 0.389, 0.5, 0.778, 0.667, 0.944, 0.722, 0.778, 0.611, 0.778, 0.722, 0.556, 0.667, 0.722, 0.722, 1.0, 0.722, 0.722, 0.667, 0.333, 0.278, 0.333, 0.581, 0.5, 0.333, 0.5, 0.556, 0.444, 0.556, 0.444, 0.333, 0.5, 0.556, 0.278, 0.333, 0.556, 0.278, 0.833, 0.556, 0.5, 0.556, 0.556, 0.444, 0.389, 0.333, 0.556, 0.5, 0.722, 0.5, 0.5, 0.444, 0.394, 0.22, 0.394, 0.52, 0.35, 0.5, 0.35, 0.333, 0.5, 0.5, 1.0, 0.5, 0.5, 0.333, 1.0, 0.556, 0.333, 1.0, 0.35, 0.667, 0.35, 0.35, 0.333, 0.333, 0.5, 0.5, 0.35, 0.5, 1.0, 0.333, 1.0, 0.389, 0.333, 0.722, 0.35, 0.444, 0.722, 0.25, 0.333, 0.5, 0.5, 0.5, 0.5, 0.22, 0.5, 0.333, 0.747, 0.3, 0.5, 0.57, 0.333, 0.747, 0.333, 0.4, 0.57, 0.3, 0.3, 0.333, 0.556, 0.54, 0.25, 0.333, 0.3, 0.33, 0.5, 0.75, 0.75, 0.75, 0.5, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 1.0, 0.722, 0.667, 0.667, 0.667, 0.667, 0.389, 0.389, 0.389, 0.389, 0.722, 0.722, 0.778, 0.778, 0.778, 0.778, 0.778, 0.57, 0.778, 0.722, 0.722, 0.722, 0.722, 0.722, 0.611, 0.556, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.722, 0.444, 0.444, 0.444, 0.444, 0.444, 0.278, 0.278, 0.278, 0.278, 0.5, 0.556, 0.5, 0.5, 0.5, 0.5, 0.5, 0.57, 0.5, 0.556, 0.556, 0.556, 0.556, 0.5, 0.556, 0.5]
    //     0xe67360: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dfc0] List<double>(256)
    //     0xe67364: ldr             x16, [x16, #0xfc0]
    // 0xe67368: stp             x16, x0, [SP]
    // 0xe6736c: mov             x1, x4
    // 0xe67370: ldur            x2, [fp, #-8]
    // 0xe67374: ldur            x5, [fp, #-0x18]
    // 0xe67378: d0 = 0.935000
    //     0xe67378: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfc8] IMM: double(0.935) from 0x3fedeb851eb851ec
    //     0xe6737c: ldr             d0, [x17, #0xfc8]
    // 0xe67380: r3 = 676
    //     0xe67380: movz            x3, #0x2a4
    // 0xe67384: d1 = -0.218000
    //     0xe67384: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfb0] IMM: double(-0.218) from 0xbfcbe76c8b439581
    //     0xe67388: ldr             d1, [x17, #0xfb0]
    // 0xe6738c: r6 = "Times-Bold"
    //     0xe6738c: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3dfd0] "Times-Bold"
    //     0xe67390: ldr             x6, [x6, #0xfd0]
    // 0xe67394: r7 = 44
    //     0xe67394: movz            x7, #0x2c
    // 0xe67398: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe67398: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe6739c: ldr             x4, [x4, #0xe28]
    // 0xe673a0: r0 = PdfType1Font.create()
    //     0xe673a0: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe673a4: ldur            x0, [fp, #-0x10]
    // 0xe673a8: LeaveFrame
    //     0xe673a8: mov             SP, fp
    //     0xe673ac: ldp             fp, lr, [SP], #0x10
    // 0xe673b0: ret
    //     0xe673b0: ret             
    // 0xe673b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe673b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe673b8: b               #0xe672f8
  }
  factory _ PdfFont.times(/* No info */) {
    // ** addr: 0xe673bc, size: 0xe8
    // 0xe673bc: EnterFrame
    //     0xe673bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe673c0: mov             fp, SP
    // 0xe673c4: AllocStack(0x28)
    //     0xe673c4: sub             SP, SP, #0x28
    // 0xe673c8: r0 = 8
    //     0xe673c8: movz            x0, #0x8
    // 0xe673cc: mov             x3, x2
    // 0xe673d0: stur            x2, [fp, #-8]
    // 0xe673d4: CheckStackOverflow
    //     0xe673d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe673d8: cmp             SP, x16
    //     0xe673dc: b.ls            #0xe6749c
    // 0xe673e0: mov             x2, x0
    // 0xe673e4: r1 = Null
    //     0xe673e4: mov             x1, NULL
    // 0xe673e8: r0 = AllocateArray()
    //     0xe673e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe673ec: stur            x0, [fp, #-0x10]
    // 0xe673f0: r16 = -336
    //     0xe673f0: movn            x16, #0x14f
    // 0xe673f4: StoreField: r0->field_f = r16
    //     0xe673f4: stur            w16, [x0, #0xf]
    // 0xe673f8: r16 = -436
    //     0xe673f8: movn            x16, #0x1b3
    // 0xe673fc: StoreField: r0->field_13 = r16
    //     0xe673fc: stur            w16, [x0, #0x13]
    // 0xe67400: r16 = 2000
    //     0xe67400: movz            x16, #0x7d0
    // 0xe67404: ArrayStore: r0[0] = r16  ; List_4
    //     0xe67404: stur            w16, [x0, #0x17]
    // 0xe67408: r16 = 1796
    //     0xe67408: movz            x16, #0x704
    // 0xe6740c: StoreField: r0->field_1b = r16
    //     0xe6740c: stur            w16, [x0, #0x1b]
    // 0xe67410: r1 = <int>
    //     0xe67410: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe67414: r0 = AllocateGrowableArray()
    //     0xe67414: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67418: mov             x2, x0
    // 0xe6741c: ldur            x0, [fp, #-0x10]
    // 0xe67420: stur            x2, [fp, #-0x18]
    // 0xe67424: StoreField: r2->field_f = r0
    //     0xe67424: stur            w0, [x2, #0xf]
    // 0xe67428: r0 = 8
    //     0xe67428: movz            x0, #0x8
    // 0xe6742c: StoreField: r2->field_b = r0
    //     0xe6742c: stur            w0, [x2, #0xb]
    // 0xe67430: r1 = <PdfDict<PdfDataType>>
    //     0xe67430: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67434: ldr             x1, [x1, #0x758]
    // 0xe67438: r0 = PdfType1Font()
    //     0xe67438: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe6743c: mov             x4, x0
    // 0xe67440: r0 = 84
    //     0xe67440: movz            x0, #0x54
    // 0xe67444: stur            x4, [fp, #-0x10]
    // 0xe67448: r16 = const [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.333, 0.408, 0.5, 0.5, 0.833, 0.778, 0.18, 0.333, 0.333, 0.5, 0.564, 0.25, 0.333, 0.25, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.278, 0.278, 0.564, 0.564, 0.564, 0.444, 0.921, 0.722, 0.667, 0.667, 0.722, 0.611, 0.556, 0.722, 0.722, 0.333, 0.389, 0.722, 0.611, 0.889, 0.722, 0.722, 0.556, 0.722, 0.667, 0.556, 0.611, 0.722, 0.722, 0.944, 0.722, 0.722, 0.611, 0.333, 0.278, 0.333, 0.469, 0.5, 0.333, 0.444, 0.5, 0.444, 0.5, 0.444, 0.333, 0.5, 0.5, 0.278, 0.278, 0.5, 0.278, 0.778, 0.5, 0.5, 0.5, 0.5, 0.333, 0.389, 0.278, 0.5, 0.5, 0.722, 0.5, 0.5, 0.444, 0.48, 0.2, 0.48, 0.541, 0.35, 0.5, 0.35, 0.333, 0.5, 0.444, 1.0, 0.5, 0.5, 0.333, 1.0, 0.556, 0.333, 0.889, 0.35, 0.611, 0.35, 0.35, 0.333, 0.333, 0.444, 0.444, 0.35, 0.5, 1.0, 0.333, 0.98, 0.389, 0.333, 0.722, 0.35, 0.444, 0.722, 0.25, 0.333, 0.5, 0.5, 0.5, 0.5, 0.2, 0.5, 0.333, 0.76, 0.276, 0.5, 0.564, 0.333, 0.76, 0.333, 0.4, 0.564, 0.3, 0.3, 0.333, 0.5, 0.453, 0.25, 0.333, 0.3, 0.31, 0.5, 0.75, 0.75, 0.75, 0.444, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.889, 0.667, 0.611, 0.611, 0.611, 0.611, 0.333, 0.333, 0.333, 0.333, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.564, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 0.556, 0.5, 0.444, 0.444, 0.444, 0.444, 0.444, 0.444, 0.667, 0.444, 0.444, 0.444, 0.444, 0.444, 0.278, 0.278, 0.278, 0.278, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.564, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]
    //     0xe67448: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dfd8] List<double>(256)
    //     0xe6744c: ldr             x16, [x16, #0xfd8]
    // 0xe67450: stp             x16, x0, [SP]
    // 0xe67454: mov             x1, x4
    // 0xe67458: ldur            x2, [fp, #-8]
    // 0xe6745c: ldur            x5, [fp, #-0x18]
    // 0xe67460: d0 = 0.898000
    //     0xe67460: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfe0] IMM: double(0.898) from 0x3fecbc6a7ef9db23
    //     0xe67464: ldr             d0, [x17, #0xfe0]
    // 0xe67468: r3 = 662
    //     0xe67468: movz            x3, #0x296
    // 0xe6746c: d1 = -0.218000
    //     0xe6746c: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dfb0] IMM: double(-0.218) from 0xbfcbe76c8b439581
    //     0xe67470: ldr             d1, [x17, #0xfb0]
    // 0xe67474: r6 = "Times-Roman"
    //     0xe67474: add             x6, PP, #0x3d, lsl #12  ; [pp+0x3dfe8] "Times-Roman"
    //     0xe67478: ldr             x6, [x6, #0xfe8]
    // 0xe6747c: r7 = 28
    //     0xe6747c: movz            x7, #0x1c
    // 0xe67480: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe67480: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe67484: ldr             x4, [x4, #0xe28]
    // 0xe67488: r0 = PdfType1Font.create()
    //     0xe67488: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe6748c: ldur            x0, [fp, #-0x10]
    // 0xe67490: LeaveFrame
    //     0xe67490: mov             SP, fp
    //     0xe67494: ldp             fp, lr, [SP], #0x10
    // 0xe67498: ret
    //     0xe67498: ret             
    // 0xe6749c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6749c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe674a0: b               #0xe673e0
  }
  factory _ PdfFont.helveticaOblique(/* No info */) {
    // ** addr: 0xe674a4, size: 0xf4
    // 0xe674a4: EnterFrame
    //     0xe674a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe674a8: mov             fp, SP
    // 0xe674ac: AllocStack(0x30)
    //     0xe674ac: sub             SP, SP, #0x30
    // 0xe674b0: r0 = 8
    //     0xe674b0: movz            x0, #0x8
    // 0xe674b4: mov             x3, x2
    // 0xe674b8: stur            x2, [fp, #-8]
    // 0xe674bc: CheckStackOverflow
    //     0xe674bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe674c0: cmp             SP, x16
    //     0xe674c4: b.ls            #0xe67590
    // 0xe674c8: mov             x2, x0
    // 0xe674cc: r1 = Null
    //     0xe674cc: mov             x1, NULL
    // 0xe674d0: r0 = AllocateArray()
    //     0xe674d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe674d4: stur            x0, [fp, #-0x10]
    // 0xe674d8: r16 = -340
    //     0xe674d8: movn            x16, #0x153
    // 0xe674dc: StoreField: r0->field_f = r16
    //     0xe674dc: stur            w16, [x0, #0xf]
    // 0xe674e0: r16 = -450
    //     0xe674e0: movn            x16, #0x1c1
    // 0xe674e4: StoreField: r0->field_13 = r16
    //     0xe674e4: stur            w16, [x0, #0x13]
    // 0xe674e8: r16 = 2232
    //     0xe674e8: movz            x16, #0x8b8
    // 0xe674ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xe674ec: stur            w16, [x0, #0x17]
    // 0xe674f0: r16 = 1862
    //     0xe674f0: movz            x16, #0x746
    // 0xe674f4: StoreField: r0->field_1b = r16
    //     0xe674f4: stur            w16, [x0, #0x1b]
    // 0xe674f8: r1 = <int>
    //     0xe674f8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe674fc: r0 = AllocateGrowableArray()
    //     0xe674fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67500: mov             x2, x0
    // 0xe67504: ldur            x0, [fp, #-0x10]
    // 0xe67508: stur            x2, [fp, #-0x18]
    // 0xe6750c: StoreField: r2->field_f = r0
    //     0xe6750c: stur            w0, [x2, #0xf]
    // 0xe67510: r0 = 8
    //     0xe67510: movz            x0, #0x8
    // 0xe67514: StoreField: r2->field_b = r0
    //     0xe67514: stur            w0, [x2, #0xb]
    // 0xe67518: r1 = <PdfDict<PdfDataType>>
    //     0xe67518: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe6751c: ldr             x1, [x1, #0x758]
    // 0xe67520: r0 = PdfType1Font()
    //     0xe67520: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67524: mov             x4, x0
    // 0xe67528: r0 = 88
    //     0xe67528: movz            x0, #0x58
    // 0xe6752c: stur            x4, [fp, #-0x10]
    // 0xe67530: r16 = const [0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.355, 0.556, 0.556, 0.889, 0.667, 0.191, 0.333, 0.333, 0.389, 0.584, 0.278, 0.333, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.278, 0.278, 0.584, 0.584, 0.584, 0.556, 1.015, 0.667, 0.667, 0.722, 0.722, 0.667, 0.611, 0.778, 0.722, 0.278, 0.5, 0.667, 0.556, 0.833, 0.722, 0.778, 0.667, 0.778, 0.722, 0.667, 0.611, 0.722, 0.667, 0.944, 0.667, 0.667, 0.611, 0.278, 0.278, 0.278, 0.469, 0.556, 0.333, 0.556, 0.556, 0.5, 0.556, 0.556, 0.278, 0.556, 0.556, 0.222, 0.222, 0.5, 0.222, 0.833, 0.556, 0.556, 0.556, 0.556, 0.333, 0.5, 0.278, 0.556, 0.5, 0.722, 0.5, 0.5, 0.5, 0.334, 0.26, 0.334, 0.584, 0.35, 0.556, 0.35, 0.222, 0.556, 0.333, 1.0, 0.556, 0.556, 0.333, 1.0, 0.667, 0.333, 1.0, 0.35, 0.611, 0.35, 0.35, 0.222, 0.222, 0.333, 0.333, 0.35, 0.556, 1.0, 0.333, 1.0, 0.5, 0.333, 0.944, 0.35, 0.5, 0.667, 0.278, 0.333, 0.556, 0.556, 0.556, 0.556, 0.26, 0.556, 0.333, 0.737, 0.37, 0.556, 0.584, 0.333, 0.737, 0.333, 0.4, 0.584, 0.333, 0.333, 0.333, 0.556, 0.537, 0.278, 0.333, 0.333, 0.365, 0.556, 0.834, 0.834, 0.834, 0.611, 0.667, 0.667, 0.667, 0.667, 0.667, 0.667, 1.0, 0.722, 0.667, 0.667, 0.667, 0.667, 0.278, 0.278, 0.278, 0.278, 0.722, 0.722, 0.778, 0.778, 0.778, 0.778, 0.778, 0.584, 0.778, 0.722, 0.722, 0.722, 0.722, 0.667, 0.667, 0.611, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.889, 0.5, 0.556, 0.556, 0.556, 0.556, 0.278, 0.278, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.584, 0.611, 0.556, 0.556, 0.556, 0.556, 0.5, 0.556, 0.5]
    //     0xe67530: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dff0] List<double>(256)
    //     0xe67534: ldr             x16, [x16, #0xff0]
    // 0xe67538: stp             x16, x0, [SP, #8]
    // 0xe6753c: r16 = -12.000000
    //     0xe6753c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dff8] -12
    //     0xe67540: ldr             x16, [x16, #0xff8]
    // 0xe67544: str             x16, [SP]
    // 0xe67548: mov             x1, x4
    // 0xe6754c: ldur            x2, [fp, #-8]
    // 0xe67550: ldur            x5, [fp, #-0x18]
    // 0xe67554: d0 = 0.931000
    //     0xe67554: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e000] IMM: double(0.931) from 0x3fedcac083126e98
    //     0xe67558: ldr             d0, [x17]
    // 0xe6755c: r3 = 718
    //     0xe6755c: movz            x3, #0x2ce
    // 0xe67560: d1 = -0.225000
    //     0xe67560: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e008] IMM: double(-0.225) from 0xbfcccccccccccccd
    //     0xe67564: ldr             d1, [x17, #8]
    // 0xe67568: r6 = "Helvetica-Oblique"
    //     0xe67568: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e010] "Helvetica-Oblique"
    //     0xe6756c: ldr             x6, [x6, #0x10]
    // 0xe67570: r7 = 76
    //     0xe67570: movz            x7, #0x4c
    // 0xe67574: r4 = const [0, 0xb, 0x3, 0x9, italicAngle, 0xa, widths, 0x9, null]
    //     0xe67574: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3df90] List(9) [0, 0xb, 0x3, 0x9, "italicAngle", 0xa, "widths", 0x9, Null]
    //     0xe67578: ldr             x4, [x4, #0xf90]
    // 0xe6757c: r0 = PdfType1Font.create()
    //     0xe6757c: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67580: ldur            x0, [fp, #-0x10]
    // 0xe67584: LeaveFrame
    //     0xe67584: mov             SP, fp
    //     0xe67588: ldp             fp, lr, [SP], #0x10
    // 0xe6758c: ret
    //     0xe6758c: ret             
    // 0xe67590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67594: b               #0xe674c8
  }
  factory _ PdfFont.helveticaBoldOblique(/* No info */) {
    // ** addr: 0xe67598, size: 0xf4
    // 0xe67598: EnterFrame
    //     0xe67598: stp             fp, lr, [SP, #-0x10]!
    //     0xe6759c: mov             fp, SP
    // 0xe675a0: AllocStack(0x30)
    //     0xe675a0: sub             SP, SP, #0x30
    // 0xe675a4: r0 = 8
    //     0xe675a4: movz            x0, #0x8
    // 0xe675a8: mov             x3, x2
    // 0xe675ac: stur            x2, [fp, #-8]
    // 0xe675b0: CheckStackOverflow
    //     0xe675b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe675b4: cmp             SP, x16
    //     0xe675b8: b.ls            #0xe67684
    // 0xe675bc: mov             x2, x0
    // 0xe675c0: r1 = Null
    //     0xe675c0: mov             x1, NULL
    // 0xe675c4: r0 = AllocateArray()
    //     0xe675c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe675c8: stur            x0, [fp, #-0x10]
    // 0xe675cc: r16 = -340
    //     0xe675cc: movn            x16, #0x153
    // 0xe675d0: StoreField: r0->field_f = r16
    //     0xe675d0: stur            w16, [x0, #0xf]
    // 0xe675d4: r16 = -456
    //     0xe675d4: movn            x16, #0x1c7
    // 0xe675d8: StoreField: r0->field_13 = r16
    //     0xe675d8: stur            w16, [x0, #0x13]
    // 0xe675dc: r16 = 2228
    //     0xe675dc: movz            x16, #0x8b4
    // 0xe675e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xe675e0: stur            w16, [x0, #0x17]
    // 0xe675e4: r16 = 1924
    //     0xe675e4: movz            x16, #0x784
    // 0xe675e8: StoreField: r0->field_1b = r16
    //     0xe675e8: stur            w16, [x0, #0x1b]
    // 0xe675ec: r1 = <int>
    //     0xe675ec: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe675f0: r0 = AllocateGrowableArray()
    //     0xe675f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe675f4: mov             x2, x0
    // 0xe675f8: ldur            x0, [fp, #-0x10]
    // 0xe675fc: stur            x2, [fp, #-0x18]
    // 0xe67600: StoreField: r2->field_f = r0
    //     0xe67600: stur            w0, [x2, #0xf]
    // 0xe67604: r0 = 8
    //     0xe67604: movz            x0, #0x8
    // 0xe67608: StoreField: r2->field_b = r0
    //     0xe67608: stur            w0, [x2, #0xb]
    // 0xe6760c: r1 = <PdfDict<PdfDataType>>
    //     0xe6760c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67610: ldr             x1, [x1, #0x758]
    // 0xe67614: r0 = PdfType1Font()
    //     0xe67614: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67618: mov             x4, x0
    // 0xe6761c: r0 = 140
    //     0xe6761c: movz            x0, #0x8c
    // 0xe67620: stur            x4, [fp, #-0x10]
    // 0xe67624: r16 = const [0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.333, 0.474, 0.556, 0.556, 0.889, 0.722, 0.238, 0.333, 0.333, 0.389, 0.584, 0.278, 0.333, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.333, 0.333, 0.584, 0.584, 0.584, 0.611, 0.975, 0.722, 0.722, 0.722, 0.722, 0.667, 0.611, 0.778, 0.722, 0.278, 0.556, 0.722, 0.611, 0.833, 0.722, 0.778, 0.667, 0.778, 0.722, 0.667, 0.611, 0.722, 0.667, 0.944, 0.667, 0.667, 0.611, 0.333, 0.278, 0.333, 0.584, 0.556, 0.333, 0.556, 0.611, 0.556, 0.611, 0.556, 0.333, 0.611, 0.611, 0.278, 0.278, 0.556, 0.278, 0.889, 0.611, 0.611, 0.611, 0.611, 0.389, 0.556, 0.333, 0.611, 0.556, 0.778, 0.556, 0.556, 0.5, 0.389, 0.28, 0.389, 0.584, 0.35, 0.556, 0.35, 0.278, 0.556, 0.5, 1.0, 0.556, 0.556, 0.333, 1.0, 0.667, 0.333, 1.0, 0.35, 0.611, 0.35, 0.35, 0.278, 0.278, 0.5, 0.5, 0.35, 0.556, 1.0, 0.333, 1.0, 0.556, 0.333, 0.944, 0.35, 0.5, 0.667, 0.278, 0.333, 0.556, 0.556, 0.556, 0.556, 0.28, 0.556, 0.333, 0.737, 0.37, 0.556, 0.584, 0.333, 0.737, 0.333, 0.4, 0.584, 0.333, 0.333, 0.333, 0.611, 0.556, 0.278, 0.333, 0.333, 0.365, 0.556, 0.834, 0.834, 0.834, 0.611, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 1.0, 0.722, 0.667, 0.667, 0.667, 0.667, 0.278, 0.278, 0.278, 0.278, 0.722, 0.722, 0.778, 0.778, 0.778, 0.778, 0.778, 0.584, 0.778, 0.722, 0.722, 0.722, 0.722, 0.667, 0.667, 0.611, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.889, 0.556, 0.556, 0.556, 0.556, 0.556, 0.278, 0.278, 0.278, 0.278, 0.611, 0.611, 0.611, 0.611, 0.611, 0.611, 0.611, 0.584, 0.611, 0.611, 0.611, 0.611, 0.611, 0.556, 0.611, 0.556]
    //     0xe67624: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e018] List<double>(256)
    //     0xe67628: ldr             x16, [x16, #0x18]
    // 0xe6762c: stp             x16, x0, [SP, #8]
    // 0xe67630: r16 = -12.000000
    //     0xe67630: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dff8] -12
    //     0xe67634: ldr             x16, [x16, #0xff8]
    // 0xe67638: str             x16, [SP]
    // 0xe6763c: mov             x1, x4
    // 0xe67640: ldur            x2, [fp, #-8]
    // 0xe67644: ldur            x5, [fp, #-0x18]
    // 0xe67648: d0 = 0.962000
    //     0xe67648: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e020] IMM: double(0.962) from 0x3feec8b439581062
    //     0xe6764c: ldr             d0, [x17, #0x20]
    // 0xe67650: r3 = 718
    //     0xe67650: movz            x3, #0x2ce
    // 0xe67654: d1 = -0.228000
    //     0xe67654: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e028] IMM: double(-0.228) from 0xbfcd2f1a9fbe76c9
    //     0xe67658: ldr             d1, [x17, #0x28]
    // 0xe6765c: r6 = "Helvetica-BoldOblique"
    //     0xe6765c: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e030] "Helvetica-BoldOblique"
    //     0xe67660: ldr             x6, [x6, #0x30]
    // 0xe67664: r7 = 118
    //     0xe67664: movz            x7, #0x76
    // 0xe67668: r4 = const [0, 0xb, 0x3, 0x9, italicAngle, 0xa, widths, 0x9, null]
    //     0xe67668: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3df90] List(9) [0, 0xb, 0x3, 0x9, "italicAngle", 0xa, "widths", 0x9, Null]
    //     0xe6766c: ldr             x4, [x4, #0xf90]
    // 0xe67670: r0 = PdfType1Font.create()
    //     0xe67670: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67674: ldur            x0, [fp, #-0x10]
    // 0xe67678: LeaveFrame
    //     0xe67678: mov             SP, fp
    //     0xe6767c: ldp             fp, lr, [SP], #0x10
    // 0xe67680: ret
    //     0xe67680: ret             
    // 0xe67684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67688: b               #0xe675bc
  }
  factory _ PdfFont.helveticaBold(/* No info */) {
    // ** addr: 0xe6768c, size: 0xe8
    // 0xe6768c: EnterFrame
    //     0xe6768c: stp             fp, lr, [SP, #-0x10]!
    //     0xe67690: mov             fp, SP
    // 0xe67694: AllocStack(0x28)
    //     0xe67694: sub             SP, SP, #0x28
    // 0xe67698: r0 = 8
    //     0xe67698: movz            x0, #0x8
    // 0xe6769c: mov             x3, x2
    // 0xe676a0: stur            x2, [fp, #-8]
    // 0xe676a4: CheckStackOverflow
    //     0xe676a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe676a8: cmp             SP, x16
    //     0xe676ac: b.ls            #0xe6776c
    // 0xe676b0: mov             x2, x0
    // 0xe676b4: r1 = Null
    //     0xe676b4: mov             x1, NULL
    // 0xe676b8: r0 = AllocateArray()
    //     0xe676b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe676bc: stur            x0, [fp, #-0x10]
    // 0xe676c0: r16 = -340
    //     0xe676c0: movn            x16, #0x153
    // 0xe676c4: StoreField: r0->field_f = r16
    //     0xe676c4: stur            w16, [x0, #0xf]
    // 0xe676c8: r16 = -456
    //     0xe676c8: movn            x16, #0x1c7
    // 0xe676cc: StoreField: r0->field_13 = r16
    //     0xe676cc: stur            w16, [x0, #0x13]
    // 0xe676d0: r16 = 2006
    //     0xe676d0: movz            x16, #0x7d6
    // 0xe676d4: ArrayStore: r0[0] = r16  ; List_4
    //     0xe676d4: stur            w16, [x0, #0x17]
    // 0xe676d8: r16 = 1924
    //     0xe676d8: movz            x16, #0x784
    // 0xe676dc: StoreField: r0->field_1b = r16
    //     0xe676dc: stur            w16, [x0, #0x1b]
    // 0xe676e0: r1 = <int>
    //     0xe676e0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe676e4: r0 = AllocateGrowableArray()
    //     0xe676e4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe676e8: mov             x2, x0
    // 0xe676ec: ldur            x0, [fp, #-0x10]
    // 0xe676f0: stur            x2, [fp, #-0x18]
    // 0xe676f4: StoreField: r2->field_f = r0
    //     0xe676f4: stur            w0, [x2, #0xf]
    // 0xe676f8: r0 = 8
    //     0xe676f8: movz            x0, #0x8
    // 0xe676fc: StoreField: r2->field_b = r0
    //     0xe676fc: stur            w0, [x2, #0xb]
    // 0xe67700: r1 = <PdfDict<PdfDataType>>
    //     0xe67700: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67704: ldr             x1, [x1, #0x758]
    // 0xe67708: r0 = PdfType1Font()
    //     0xe67708: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe6770c: mov             x4, x0
    // 0xe67710: r0 = 140
    //     0xe67710: movz            x0, #0x8c
    // 0xe67714: stur            x4, [fp, #-0x10]
    // 0xe67718: r16 = const [0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.278, 0.333, 0.474, 0.556, 0.556, 0.889, 0.722, 0.238, 0.333, 0.333, 0.389, 0.584, 0.278, 0.333, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.333, 0.333, 0.584, 0.584, 0.584, 0.611, 0.975, 0.722, 0.722, 0.722, 0.722, 0.667, 0.611, 0.778, 0.722, 0.278, 0.556, 0.722, 0.611, 0.833, 0.722, 0.778, 0.667, 0.778, 0.722, 0.667, 0.611, 0.722, 0.667, 0.944, 0.667, 0.667, 0.611, 0.333, 0.278, 0.333, 0.584, 0.556, 0.333, 0.556, 0.611, 0.556, 0.611, 0.556, 0.333, 0.611, 0.611, 0.278, 0.278, 0.556, 0.278, 0.889, 0.611, 0.611, 0.611, 0.611, 0.389, 0.556, 0.333, 0.611, 0.556, 0.778, 0.556, 0.556, 0.5, 0.389, 0.28, 0.389, 0.584, 0.35, 0.556, 0.35, 0.278, 0.556, 0.5, 1.0, 0.556, 0.556, 0.333, 1.0, 0.667, 0.333, 1.0, 0.35, 0.611, 0.35, 0.35, 0.278, 0.278, 0.5, 0.5, 0.35, 0.556, 1.0, 0.333, 1.0, 0.556, 0.333, 0.944, 0.35, 0.5, 0.667, 0.278, 0.333, 0.556, 0.556, 0.556, 0.556, 0.28, 0.556, 0.333, 0.737, 0.37, 0.556, 0.584, 0.333, 0.737, 0.333, 0.4, 0.584, 0.333, 0.333, 0.333, 0.611, 0.556, 0.278, 0.333, 0.333, 0.365, 0.556, 0.834, 0.834, 0.834, 0.611, 0.722, 0.722, 0.722, 0.722, 0.722, 0.722, 1.0, 0.722, 0.667, 0.667, 0.667, 0.667, 0.278, 0.278, 0.278, 0.278, 0.722, 0.722, 0.778, 0.778, 0.778, 0.778, 0.778, 0.584, 0.778, 0.722, 0.722, 0.722, 0.722, 0.667, 0.667, 0.611, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.889, 0.556, 0.556, 0.556, 0.556, 0.556, 0.278, 0.278, 0.278, 0.278, 0.611, 0.611, 0.611, 0.611, 0.611, 0.611, 0.611, 0.584, 0.611, 0.611, 0.611, 0.611, 0.611, 0.556, 0.611, 0.556]
    //     0xe67718: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e018] List<double>(256)
    //     0xe6771c: ldr             x16, [x16, #0x18]
    // 0xe67720: stp             x16, x0, [SP]
    // 0xe67724: mov             x1, x4
    // 0xe67728: ldur            x2, [fp, #-8]
    // 0xe6772c: ldur            x5, [fp, #-0x18]
    // 0xe67730: d0 = 0.962000
    //     0xe67730: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e020] IMM: double(0.962) from 0x3feec8b439581062
    //     0xe67734: ldr             d0, [x17, #0x20]
    // 0xe67738: r3 = 718
    //     0xe67738: movz            x3, #0x2ce
    // 0xe6773c: d1 = -0.228000
    //     0xe6773c: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e028] IMM: double(-0.228) from 0xbfcd2f1a9fbe76c9
    //     0xe67740: ldr             d1, [x17, #0x28]
    // 0xe67744: r6 = "Helvetica-Bold"
    //     0xe67744: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e038] "Helvetica-Bold"
    //     0xe67748: ldr             x6, [x6, #0x38]
    // 0xe6774c: r7 = 118
    //     0xe6774c: movz            x7, #0x76
    // 0xe67750: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe67750: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe67754: ldr             x4, [x4, #0xe28]
    // 0xe67758: r0 = PdfType1Font.create()
    //     0xe67758: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe6775c: ldur            x0, [fp, #-0x10]
    // 0xe67760: LeaveFrame
    //     0xe67760: mov             SP, fp
    //     0xe67764: ldp             fp, lr, [SP], #0x10
    // 0xe67768: ret
    //     0xe67768: ret             
    // 0xe6776c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6776c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67770: b               #0xe676b0
  }
  factory _ PdfFont.helvetica(/* No info */) {
    // ** addr: 0xe67774, size: 0xe8
    // 0xe67774: EnterFrame
    //     0xe67774: stp             fp, lr, [SP, #-0x10]!
    //     0xe67778: mov             fp, SP
    // 0xe6777c: AllocStack(0x28)
    //     0xe6777c: sub             SP, SP, #0x28
    // 0xe67780: r0 = 8
    //     0xe67780: movz            x0, #0x8
    // 0xe67784: mov             x3, x2
    // 0xe67788: stur            x2, [fp, #-8]
    // 0xe6778c: CheckStackOverflow
    //     0xe6778c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67790: cmp             SP, x16
    //     0xe67794: b.ls            #0xe67854
    // 0xe67798: mov             x2, x0
    // 0xe6779c: r1 = Null
    //     0xe6779c: mov             x1, NULL
    // 0xe677a0: r0 = AllocateArray()
    //     0xe677a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe677a4: stur            x0, [fp, #-0x10]
    // 0xe677a8: r16 = -332
    //     0xe677a8: movn            x16, #0x14b
    // 0xe677ac: StoreField: r0->field_f = r16
    //     0xe677ac: stur            w16, [x0, #0xf]
    // 0xe677b0: r16 = -450
    //     0xe677b0: movn            x16, #0x1c1
    // 0xe677b4: StoreField: r0->field_13 = r16
    //     0xe677b4: stur            w16, [x0, #0x13]
    // 0xe677b8: r16 = 2000
    //     0xe677b8: movz            x16, #0x7d0
    // 0xe677bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xe677bc: stur            w16, [x0, #0x17]
    // 0xe677c0: r16 = 1862
    //     0xe677c0: movz            x16, #0x746
    // 0xe677c4: StoreField: r0->field_1b = r16
    //     0xe677c4: stur            w16, [x0, #0x1b]
    // 0xe677c8: r1 = <int>
    //     0xe677c8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe677cc: r0 = AllocateGrowableArray()
    //     0xe677cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe677d0: mov             x2, x0
    // 0xe677d4: ldur            x0, [fp, #-0x10]
    // 0xe677d8: stur            x2, [fp, #-0x18]
    // 0xe677dc: StoreField: r2->field_f = r0
    //     0xe677dc: stur            w0, [x2, #0xf]
    // 0xe677e0: r0 = 8
    //     0xe677e0: movz            x0, #0x8
    // 0xe677e4: StoreField: r2->field_b = r0
    //     0xe677e4: stur            w0, [x2, #0xb]
    // 0xe677e8: r1 = <PdfDict<PdfDataType>>
    //     0xe677e8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe677ec: ldr             x1, [x1, #0x758]
    // 0xe677f0: r0 = PdfType1Font()
    //     0xe677f0: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe677f4: mov             x4, x0
    // 0xe677f8: r0 = 88
    //     0xe677f8: movz            x0, #0x58
    // 0xe677fc: stur            x4, [fp, #-0x10]
    // 0xe67800: r16 = const [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.278, 0.278, 0.355, 0.556, 0.556, 0.889, 0.667, 0.191, 0.333, 0.333, 0.389, 0.584, 0.278, 0.333, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.278, 0.278, 0.584, 0.584, 0.584, 0.556, 1.015, 0.667, 0.667, 0.722, 0.722, 0.667, 0.611, 0.778, 0.722, 0.278, 0.5, 0.667, 0.556, 0.833, 0.722, 0.778, 0.667, 0.778, 0.722, 0.667, 0.611, 0.722, 0.667, 0.944, 0.667, 0.667, 0.611, 0.278, 0.278, 0.277, 0.469, 0.556, 0.333, 0.556, 0.556, 0.5, 0.556, 0.556, 0.278, 0.556, 0.556, 0.222, 0.222, 0.5, 0.222, 0.833, 0.556, 0.556, 0.556, 0.556, 0.333, 0.5, 0.278, 0.556, 0.5, 0.722, 0.5, 0.5, 0.5, 0.334, 0.26, 0.334, 0.584, 0.5, 0.655, 0.5, 0.222, 0.278, 0.333, 1.0, 0.556, 0.556, 0.333, 1.0, 0.667, 0.25, 1.0, 0.5, 0.611, 0.5, 0.5, 0.222, 0.221, 0.333, 0.333, 0.35, 0.556, 1.0, 0.333, 1.0, 0.5, 0.25, 0.938, 0.5, 0.5, 0.667, 0.278, 0.278, 0.556, 0.556, 0.556, 0.556, 0.26, 0.556, 0.333, 0.737, 0.37, 0.448, 0.584, 0.333, 0.737, 0.333, 0.606, 0.584, 0.35, 0.35, 0.333, 0.556, 0.537, 0.278, 0.333, 0.35, 0.365, 0.448, 0.869, 0.869, 0.879, 0.556, 0.667, 0.667, 0.667, 0.667, 0.667, 0.667, 1.0, 0.722, 0.667, 0.667, 0.667, 0.667, 0.278, 0.278, 0.278, 0.278, 0.722, 0.722, 0.778, 0.778, 0.778, 0.778, 0.778, 0.584, 0.778, 0.722, 0.722, 0.722, 0.722, 0.667, 0.666, 0.611, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.896, 0.5, 0.556, 0.556, 0.556, 0.556, 0.251, 0.251, 0.251, 0.251, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.556, 0.584, 0.611, 0.556, 0.556, 0.556, 0.556, 0.5, 0.555, 0.5]
    //     0xe67800: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e040] List<double>(256)
    //     0xe67804: ldr             x16, [x16, #0x40]
    // 0xe67808: stp             x16, x0, [SP]
    // 0xe6780c: mov             x1, x4
    // 0xe67810: ldur            x2, [fp, #-8]
    // 0xe67814: ldur            x5, [fp, #-0x18]
    // 0xe67818: d0 = 0.931000
    //     0xe67818: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e000] IMM: double(0.931) from 0x3fedcac083126e98
    //     0xe6781c: ldr             d0, [x17]
    // 0xe67820: r3 = 718
    //     0xe67820: movz            x3, #0x2ce
    // 0xe67824: d1 = -0.225000
    //     0xe67824: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e008] IMM: double(-0.225) from 0xbfcccccccccccccd
    //     0xe67828: ldr             d1, [x17, #8]
    // 0xe6782c: r6 = "Helvetica"
    //     0xe6782c: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e048] "Helvetica"
    //     0xe67830: ldr             x6, [x6, #0x48]
    // 0xe67834: r7 = 76
    //     0xe67834: movz            x7, #0x4c
    // 0xe67838: r4 = const [0, 0xa, 0x2, 0x9, widths, 0x9, null]
    //     0xe67838: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3de28] List(7) [0, 0xa, 0x2, 0x9, "widths", 0x9, Null]
    //     0xe6783c: ldr             x4, [x4, #0xe28]
    // 0xe67840: r0 = PdfType1Font.create()
    //     0xe67840: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67844: ldur            x0, [fp, #-0x10]
    // 0xe67848: LeaveFrame
    //     0xe67848: mov             SP, fp
    //     0xe6784c: ldp             fp, lr, [SP], #0x10
    // 0xe67850: ret
    //     0xe67850: ret             
    // 0xe67854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67854: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67858: b               #0xe67798
  }
  factory _ PdfFont.courierOblique(/* No info */) {
    // ** addr: 0xe6785c, size: 0xe8
    // 0xe6785c: EnterFrame
    //     0xe6785c: stp             fp, lr, [SP, #-0x10]!
    //     0xe67860: mov             fp, SP
    // 0xe67864: AllocStack(0x30)
    //     0xe67864: sub             SP, SP, #0x30
    // 0xe67868: r0 = 8
    //     0xe67868: movz            x0, #0x8
    // 0xe6786c: mov             x3, x2
    // 0xe67870: stur            x2, [fp, #-8]
    // 0xe67874: CheckStackOverflow
    //     0xe67874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67878: cmp             SP, x16
    //     0xe6787c: b.ls            #0xe6793c
    // 0xe67880: mov             x2, x0
    // 0xe67884: r1 = Null
    //     0xe67884: mov             x1, NULL
    // 0xe67888: r0 = AllocateArray()
    //     0xe67888: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6788c: stur            x0, [fp, #-0x10]
    // 0xe67890: r16 = -54
    //     0xe67890: movn            x16, #0x35
    // 0xe67894: StoreField: r0->field_f = r16
    //     0xe67894: stur            w16, [x0, #0xf]
    // 0xe67898: r16 = -500
    //     0xe67898: movn            x16, #0x1f3
    // 0xe6789c: StoreField: r0->field_13 = r16
    //     0xe6789c: stur            w16, [x0, #0x13]
    // 0xe678a0: r16 = 1698
    //     0xe678a0: movz            x16, #0x6a2
    // 0xe678a4: ArrayStore: r0[0] = r16  ; List_4
    //     0xe678a4: stur            w16, [x0, #0x17]
    // 0xe678a8: r16 = 1610
    //     0xe678a8: movz            x16, #0x64a
    // 0xe678ac: StoreField: r0->field_1b = r16
    //     0xe678ac: stur            w16, [x0, #0x1b]
    // 0xe678b0: r1 = <int>
    //     0xe678b0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe678b4: r0 = AllocateGrowableArray()
    //     0xe678b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe678b8: mov             x2, x0
    // 0xe678bc: ldur            x0, [fp, #-0x10]
    // 0xe678c0: stur            x2, [fp, #-0x18]
    // 0xe678c4: StoreField: r2->field_f = r0
    //     0xe678c4: stur            w0, [x2, #0xf]
    // 0xe678c8: r0 = 8
    //     0xe678c8: movz            x0, #0x8
    // 0xe678cc: StoreField: r2->field_b = r0
    //     0xe678cc: stur            w0, [x2, #0xb]
    // 0xe678d0: r1 = <PdfDict<PdfDataType>>
    //     0xe678d0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe678d4: ldr             x1, [x1, #0x758]
    // 0xe678d8: r0 = PdfType1Font()
    //     0xe678d8: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe678dc: r7 = 51
    //     0xe678dc: movz            x7, #0x33
    // 0xe678e0: stur            x0, [fp, #-0x10]
    // 0xe678e4: r16 = true
    //     0xe678e4: add             x16, NULL, #0x20  ; true
    // 0xe678e8: stp             x16, x7, [SP, #8]
    // 0xe678ec: r16 = -12.000000
    //     0xe678ec: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dff8] -12
    //     0xe678f0: ldr             x16, [x16, #0xff8]
    // 0xe678f4: str             x16, [SP]
    // 0xe678f8: mov             x1, x0
    // 0xe678fc: ldur            x2, [fp, #-8]
    // 0xe67900: ldur            x5, [fp, #-0x18]
    // 0xe67904: d0 = 0.910000
    //     0xe67904: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e050] IMM: double(0.91) from 0x3fed1eb851eb851f
    //     0xe67908: ldr             d0, [x17, #0x50]
    // 0xe6790c: r3 = 562
    //     0xe6790c: movz            x3, #0x232
    // 0xe67910: d1 = -0.220000
    //     0xe67910: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e058] IMM: double(-0.22) from 0xbfcc28f5c28f5c29
    //     0xe67914: ldr             d1, [x17, #0x58]
    // 0xe67918: r6 = "Courier-Oblique"
    //     0xe67918: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e060] "Courier-Oblique"
    //     0xe6791c: ldr             x6, [x6, #0x60]
    // 0xe67920: r4 = const [0, 0xb, 0x3, 0x9, isFixedPitch, 0x9, italicAngle, 0xa, null]
    //     0xe67920: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e068] List(9) [0, 0xb, 0x3, 0x9, "isFixedPitch", 0x9, "italicAngle", 0xa, Null]
    //     0xe67924: ldr             x4, [x4, #0x68]
    // 0xe67928: r0 = PdfType1Font.create()
    //     0xe67928: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe6792c: ldur            x0, [fp, #-0x10]
    // 0xe67930: LeaveFrame
    //     0xe67930: mov             SP, fp
    //     0xe67934: ldp             fp, lr, [SP], #0x10
    // 0xe67938: ret
    //     0xe67938: ret             
    // 0xe6793c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6793c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67940: b               #0xe67880
  }
  factory _ PdfFont.courierBoldOblique(/* No info */) {
    // ** addr: 0xe67944, size: 0xf0
    // 0xe67944: EnterFrame
    //     0xe67944: stp             fp, lr, [SP, #-0x10]!
    //     0xe67948: mov             fp, SP
    // 0xe6794c: AllocStack(0x30)
    //     0xe6794c: sub             SP, SP, #0x30
    // 0xe67950: r0 = 8
    //     0xe67950: movz            x0, #0x8
    // 0xe67954: mov             x3, x2
    // 0xe67958: stur            x2, [fp, #-8]
    // 0xe6795c: CheckStackOverflow
    //     0xe6795c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67960: cmp             SP, x16
    //     0xe67964: b.ls            #0xe67a2c
    // 0xe67968: mov             x2, x0
    // 0xe6796c: r1 = Null
    //     0xe6796c: mov             x1, NULL
    // 0xe67970: r0 = AllocateArray()
    //     0xe67970: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67974: stur            x0, [fp, #-0x10]
    // 0xe67978: r16 = -114
    //     0xe67978: movn            x16, #0x71
    // 0xe6797c: StoreField: r0->field_f = r16
    //     0xe6797c: stur            w16, [x0, #0xf]
    // 0xe67980: r16 = -500
    //     0xe67980: movn            x16, #0x1f3
    // 0xe67984: StoreField: r0->field_13 = r16
    //     0xe67984: stur            w16, [x0, #0x13]
    // 0xe67988: r16 = 1738
    //     0xe67988: movz            x16, #0x6ca
    // 0xe6798c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe6798c: stur            w16, [x0, #0x17]
    // 0xe67990: r16 = 1602
    //     0xe67990: movz            x16, #0x642
    // 0xe67994: StoreField: r0->field_1b = r16
    //     0xe67994: stur            w16, [x0, #0x1b]
    // 0xe67998: r1 = <int>
    //     0xe67998: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6799c: r0 = AllocateGrowableArray()
    //     0xe6799c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe679a0: mov             x2, x0
    // 0xe679a4: ldur            x0, [fp, #-0x10]
    // 0xe679a8: stur            x2, [fp, #-0x18]
    // 0xe679ac: StoreField: r2->field_f = r0
    //     0xe679ac: stur            w0, [x2, #0xf]
    // 0xe679b0: r0 = 8
    //     0xe679b0: movz            x0, #0x8
    // 0xe679b4: StoreField: r2->field_b = r0
    //     0xe679b4: stur            w0, [x2, #0xb]
    // 0xe679b8: r1 = <PdfDict<PdfDataType>>
    //     0xe679b8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe679bc: ldr             x1, [x1, #0x758]
    // 0xe679c0: r0 = PdfType1Font()
    //     0xe679c0: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe679c4: mov             x4, x0
    // 0xe679c8: r0 = 106
    //     0xe679c8: movz            x0, #0x6a
    // 0xe679cc: stur            x4, [fp, #-0x10]
    // 0xe679d0: r16 = -12.000000
    //     0xe679d0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dff8] -12
    //     0xe679d4: ldr             x16, [x16, #0xff8]
    // 0xe679d8: stp             x16, x0, [SP, #8]
    // 0xe679dc: r16 = true
    //     0xe679dc: add             x16, NULL, #0x20  ; true
    // 0xe679e0: str             x16, [SP]
    // 0xe679e4: mov             x1, x4
    // 0xe679e8: ldur            x2, [fp, #-8]
    // 0xe679ec: ldur            x5, [fp, #-0x18]
    // 0xe679f0: d0 = 0.910000
    //     0xe679f0: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e050] IMM: double(0.91) from 0x3fed1eb851eb851f
    //     0xe679f4: ldr             d0, [x17, #0x50]
    // 0xe679f8: r3 = 562
    //     0xe679f8: movz            x3, #0x232
    // 0xe679fc: d1 = -0.220000
    //     0xe679fc: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e058] IMM: double(-0.22) from 0xbfcc28f5c28f5c29
    //     0xe67a00: ldr             d1, [x17, #0x58]
    // 0xe67a04: r6 = "Courier-BoldOblique"
    //     0xe67a04: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e070] "Courier-BoldOblique"
    //     0xe67a08: ldr             x6, [x6, #0x70]
    // 0xe67a0c: r7 = 84
    //     0xe67a0c: movz            x7, #0x54
    // 0xe67a10: r4 = const [0, 0xb, 0x3, 0x9, isFixedPitch, 0xa, italicAngle, 0x9, null]
    //     0xe67a10: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e078] List(9) [0, 0xb, 0x3, 0x9, "isFixedPitch", 0xa, "italicAngle", 0x9, Null]
    //     0xe67a14: ldr             x4, [x4, #0x78]
    // 0xe67a18: r0 = PdfType1Font.create()
    //     0xe67a18: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67a1c: ldur            x0, [fp, #-0x10]
    // 0xe67a20: LeaveFrame
    //     0xe67a20: mov             SP, fp
    //     0xe67a24: ldp             fp, lr, [SP], #0x10
    // 0xe67a28: ret
    //     0xe67a28: ret             
    // 0xe67a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67a30: b               #0xe67968
  }
  factory _ PdfFont.courierBold(/* No info */) {
    // ** addr: 0xe67a34, size: 0xdc
    // 0xe67a34: EnterFrame
    //     0xe67a34: stp             fp, lr, [SP, #-0x10]!
    //     0xe67a38: mov             fp, SP
    // 0xe67a3c: AllocStack(0x28)
    //     0xe67a3c: sub             SP, SP, #0x28
    // 0xe67a40: r0 = 8
    //     0xe67a40: movz            x0, #0x8
    // 0xe67a44: mov             x3, x2
    // 0xe67a48: stur            x2, [fp, #-8]
    // 0xe67a4c: CheckStackOverflow
    //     0xe67a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67a50: cmp             SP, x16
    //     0xe67a54: b.ls            #0xe67b08
    // 0xe67a58: mov             x2, x0
    // 0xe67a5c: r1 = Null
    //     0xe67a5c: mov             x1, NULL
    // 0xe67a60: r0 = AllocateArray()
    //     0xe67a60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67a64: stur            x0, [fp, #-0x10]
    // 0xe67a68: r16 = -226
    //     0xe67a68: movn            x16, #0xe1
    // 0xe67a6c: StoreField: r0->field_f = r16
    //     0xe67a6c: stur            w16, [x0, #0xf]
    // 0xe67a70: r16 = -500
    //     0xe67a70: movn            x16, #0x1f3
    // 0xe67a74: StoreField: r0->field_13 = r16
    //     0xe67a74: stur            w16, [x0, #0x13]
    // 0xe67a78: r16 = 1498
    //     0xe67a78: movz            x16, #0x5da
    // 0xe67a7c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe67a7c: stur            w16, [x0, #0x17]
    // 0xe67a80: r16 = 1602
    //     0xe67a80: movz            x16, #0x642
    // 0xe67a84: StoreField: r0->field_1b = r16
    //     0xe67a84: stur            w16, [x0, #0x1b]
    // 0xe67a88: r1 = <int>
    //     0xe67a88: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe67a8c: r0 = AllocateGrowableArray()
    //     0xe67a8c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67a90: mov             x2, x0
    // 0xe67a94: ldur            x0, [fp, #-0x10]
    // 0xe67a98: stur            x2, [fp, #-0x18]
    // 0xe67a9c: StoreField: r2->field_f = r0
    //     0xe67a9c: stur            w0, [x2, #0xf]
    // 0xe67aa0: r0 = 8
    //     0xe67aa0: movz            x0, #0x8
    // 0xe67aa4: StoreField: r2->field_b = r0
    //     0xe67aa4: stur            w0, [x2, #0xb]
    // 0xe67aa8: r1 = <PdfDict<PdfDataType>>
    //     0xe67aa8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67aac: ldr             x1, [x1, #0x758]
    // 0xe67ab0: r0 = PdfType1Font()
    //     0xe67ab0: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67ab4: r7 = 51
    //     0xe67ab4: movz            x7, #0x33
    // 0xe67ab8: stur            x0, [fp, #-0x10]
    // 0xe67abc: r16 = true
    //     0xe67abc: add             x16, NULL, #0x20  ; true
    // 0xe67ac0: stp             x16, x7, [SP]
    // 0xe67ac4: mov             x1, x0
    // 0xe67ac8: ldur            x2, [fp, #-8]
    // 0xe67acc: ldur            x5, [fp, #-0x18]
    // 0xe67ad0: d0 = 0.910000
    //     0xe67ad0: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e050] IMM: double(0.91) from 0x3fed1eb851eb851f
    //     0xe67ad4: ldr             d0, [x17, #0x50]
    // 0xe67ad8: r3 = 562
    //     0xe67ad8: movz            x3, #0x232
    // 0xe67adc: d1 = -0.220000
    //     0xe67adc: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e058] IMM: double(-0.22) from 0xbfcc28f5c28f5c29
    //     0xe67ae0: ldr             d1, [x17, #0x58]
    // 0xe67ae4: r6 = "Courier-Bold"
    //     0xe67ae4: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e080] "Courier-Bold"
    //     0xe67ae8: ldr             x6, [x6, #0x80]
    // 0xe67aec: r4 = const [0, 0xa, 0x2, 0x9, isFixedPitch, 0x9, null]
    //     0xe67aec: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e088] List(7) [0, 0xa, 0x2, 0x9, "isFixedPitch", 0x9, Null]
    //     0xe67af0: ldr             x4, [x4, #0x88]
    // 0xe67af4: r0 = PdfType1Font.create()
    //     0xe67af4: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67af8: ldur            x0, [fp, #-0x10]
    // 0xe67afc: LeaveFrame
    //     0xe67afc: mov             SP, fp
    //     0xe67b00: ldp             fp, lr, [SP], #0x10
    // 0xe67b04: ret
    //     0xe67b04: ret             
    // 0xe67b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67b0c: b               #0xe67a58
  }
  factory _ PdfFont.courier(/* No info */) {
    // ** addr: 0xe67b10, size: 0xe4
    // 0xe67b10: EnterFrame
    //     0xe67b10: stp             fp, lr, [SP, #-0x10]!
    //     0xe67b14: mov             fp, SP
    // 0xe67b18: AllocStack(0x28)
    //     0xe67b18: sub             SP, SP, #0x28
    // 0xe67b1c: r0 = 8
    //     0xe67b1c: movz            x0, #0x8
    // 0xe67b20: mov             x3, x2
    // 0xe67b24: stur            x2, [fp, #-8]
    // 0xe67b28: CheckStackOverflow
    //     0xe67b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67b2c: cmp             SP, x16
    //     0xe67b30: b.ls            #0xe67bec
    // 0xe67b34: mov             x2, x0
    // 0xe67b38: r1 = Null
    //     0xe67b38: mov             x1, NULL
    // 0xe67b3c: r0 = AllocateArray()
    //     0xe67b3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe67b40: stur            x0, [fp, #-0x10]
    // 0xe67b44: r16 = -46
    //     0xe67b44: movn            x16, #0x2d
    // 0xe67b48: StoreField: r0->field_f = r16
    //     0xe67b48: stur            w16, [x0, #0xf]
    // 0xe67b4c: r16 = -500
    //     0xe67b4c: movn            x16, #0x1f3
    // 0xe67b50: StoreField: r0->field_13 = r16
    //     0xe67b50: stur            w16, [x0, #0x13]
    // 0xe67b54: r16 = 1430
    //     0xe67b54: movz            x16, #0x596
    // 0xe67b58: ArrayStore: r0[0] = r16  ; List_4
    //     0xe67b58: stur            w16, [x0, #0x17]
    // 0xe67b5c: r16 = 1610
    //     0xe67b5c: movz            x16, #0x64a
    // 0xe67b60: StoreField: r0->field_1b = r16
    //     0xe67b60: stur            w16, [x0, #0x1b]
    // 0xe67b64: r1 = <int>
    //     0xe67b64: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe67b68: r0 = AllocateGrowableArray()
    //     0xe67b68: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe67b6c: mov             x2, x0
    // 0xe67b70: ldur            x0, [fp, #-0x10]
    // 0xe67b74: stur            x2, [fp, #-0x18]
    // 0xe67b78: StoreField: r2->field_f = r0
    //     0xe67b78: stur            w0, [x2, #0xf]
    // 0xe67b7c: r0 = 8
    //     0xe67b7c: movz            x0, #0x8
    // 0xe67b80: StoreField: r2->field_b = r0
    //     0xe67b80: stur            w0, [x2, #0xb]
    // 0xe67b84: r1 = <PdfDict<PdfDataType>>
    //     0xe67b84: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe67b88: ldr             x1, [x1, #0x758]
    // 0xe67b8c: r0 = PdfType1Font()
    //     0xe67b8c: bl              #0xe66ff8  ; AllocatePdfType1FontStub -> PdfType1Font (size=0x50)
    // 0xe67b90: mov             x4, x0
    // 0xe67b94: r0 = 106
    //     0xe67b94: movz            x0, #0x6a
    // 0xe67b98: stur            x4, [fp, #-0x10]
    // 0xe67b9c: r16 = true
    //     0xe67b9c: add             x16, NULL, #0x20  ; true
    // 0xe67ba0: stp             x16, x0, [SP]
    // 0xe67ba4: mov             x1, x4
    // 0xe67ba8: ldur            x2, [fp, #-8]
    // 0xe67bac: ldur            x5, [fp, #-0x18]
    // 0xe67bb0: d0 = 0.910000
    //     0xe67bb0: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e050] IMM: double(0.91) from 0x3fed1eb851eb851f
    //     0xe67bb4: ldr             d0, [x17, #0x50]
    // 0xe67bb8: r3 = 562
    //     0xe67bb8: movz            x3, #0x232
    // 0xe67bbc: d1 = -0.220000
    //     0xe67bbc: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e058] IMM: double(-0.22) from 0xbfcc28f5c28f5c29
    //     0xe67bc0: ldr             d1, [x17, #0x58]
    // 0xe67bc4: r6 = "Courier"
    //     0xe67bc4: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e090] "Courier"
    //     0xe67bc8: ldr             x6, [x6, #0x90]
    // 0xe67bcc: r7 = 84
    //     0xe67bcc: movz            x7, #0x54
    // 0xe67bd0: r4 = const [0, 0xa, 0x2, 0x9, isFixedPitch, 0x9, null]
    //     0xe67bd0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e088] List(7) [0, 0xa, 0x2, 0x9, "isFixedPitch", 0x9, Null]
    //     0xe67bd4: ldr             x4, [x4, #0x88]
    // 0xe67bd8: r0 = PdfType1Font.create()
    //     0xe67bd8: bl              #0xe66524  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create
    // 0xe67bdc: ldur            x0, [fp, #-0x10]
    // 0xe67be0: LeaveFrame
    //     0xe67be0: mov             SP, fp
    //     0xe67be4: ldp             fp, lr, [SP], #0x10
    // 0xe67be8: ret
    //     0xe67be8: ret             
    // 0xe67bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67bec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67bf0: b               #0xe67b34
  }
  _ putText(/* No info */) {
    // ** addr: 0xea038c, size: 0x84
    // 0xea038c: EnterFrame
    //     0xea038c: stp             fp, lr, [SP, #-0x10]!
    //     0xea0390: mov             fp, SP
    // 0xea0394: AllocStack(0x18)
    //     0xea0394: sub             SP, SP, #0x18
    // 0xea0398: SetupParameters(PdfFont this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2 */)
    //     0xea0398: mov             x0, x1
    //     0xea039c: stur            x2, [fp, #-0x10]
    //     0xea03a0: mov             x16, x3
    //     0xea03a4: mov             x3, x2
    //     0xea03a8: mov             x2, x16
    //     0xea03ac: stur            x1, [fp, #-8]
    // 0xea03b0: CheckStackOverflow
    //     0xea03b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea03b4: cmp             SP, x16
    //     0xea03b8: b.ls            #0xea0408
    // 0xea03bc: r1 = Instance_Latin1Codec
    //     0xea03bc: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xea03c0: r0 = encode()
    //     0xea03c0: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xea03c4: stur            x0, [fp, #-0x18]
    // 0xea03c8: r0 = PdfString()
    //     0xea03c8: bl              #0x7b8c74  ; AllocatePdfStringStub -> PdfString (size=0x14)
    // 0xea03cc: mov             x1, x0
    // 0xea03d0: ldur            x0, [fp, #-0x18]
    // 0xea03d4: StoreField: r1->field_7 = r0
    //     0xea03d4: stur            w0, [x1, #7]
    // 0xea03d8: r0 = Instance_PdfStringFormat
    //     0xea03d8: add             x0, PP, #0x46, lsl #12  ; [pp+0x46d70] Obj!PdfStringFormat@e2f041
    //     0xea03dc: ldr             x0, [x0, #0xd70]
    // 0xea03e0: StoreField: r1->field_b = r0
    //     0xea03e0: stur            w0, [x1, #0xb]
    // 0xea03e4: r0 = false
    //     0xea03e4: add             x0, NULL, #0x30  ; false
    // 0xea03e8: StoreField: r1->field_f = r0
    //     0xea03e8: stur            w0, [x1, #0xf]
    // 0xea03ec: ldur            x2, [fp, #-8]
    // 0xea03f0: ldur            x3, [fp, #-0x10]
    // 0xea03f4: r0 = output()
    //     0xea03f4: bl              #0xe7f538  ; [package:pdf/src/pdf/format/string.dart] PdfString::output
    // 0xea03f8: r0 = Null
    //     0xea03f8: mov             x0, NULL
    // 0xea03fc: LeaveFrame
    //     0xea03fc: mov             SP, fp
    //     0xea0400: ldp             fp, lr, [SP], #0x10
    // 0xea0404: ret
    //     0xea0404: ret             
    // 0xea0408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea040c: b               #0xea03bc
  }
  _ stringMetrics(/* No info */) {
    // ** addr: 0xea0c88, size: 0xf0
    // 0xea0c88: EnterFrame
    //     0xea0c88: stp             fp, lr, [SP, #-0x10]!
    //     0xea0c8c: mov             fp, SP
    // 0xea0c90: AllocStack(0x18)
    //     0xea0c90: sub             SP, SP, #0x18
    // 0xea0c94: SetupParameters(PdfFont this /* r1 => r0, fp-0x8 */, {_Double letterSpacing = 0.000000 /* d0, fp-0x18 */})
    //     0xea0c94: mov             x0, x1
    //     0xea0c98: stur            x1, [fp, #-8]
    //     0xea0c9c: ldur            w1, [x4, #0x13]
    //     0xea0ca0: ldur            w3, [x4, #0x1f]
    //     0xea0ca4: add             x3, x3, HEAP, lsl #32
    //     0xea0ca8: ldr             x16, [PP, #0x4788]  ; [pp+0x4788] "letterSpacing"
    //     0xea0cac: cmp             w3, w16
    //     0xea0cb0: b.ne            #0xea0cd0
    //     0xea0cb4: ldur            w3, [x4, #0x23]
    //     0xea0cb8: add             x3, x3, HEAP, lsl #32
    //     0xea0cbc: sub             w4, w1, w3
    //     0xea0cc0: add             x1, fp, w4, sxtw #2
    //     0xea0cc4: ldr             x1, [x1, #8]
    //     0xea0cc8: ldur            d0, [x1, #7]
    //     0xea0ccc: b               #0xea0cd4
    //     0xea0cd0: eor             v0.16b, v0.16b, v0.16b
    //     0xea0cd4: stur            d0, [fp, #-0x18]
    // 0xea0cd8: CheckStackOverflow
    //     0xea0cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0cdc: cmp             SP, x16
    //     0xea0ce0: b.ls            #0xea0d70
    // 0xea0ce4: LoadField: r1 = r2->field_7
    //     0xea0ce4: ldur            w1, [x2, #7]
    // 0xea0ce8: cbnz            w1, #0xea0d00
    // 0xea0cec: r0 = Instance_PdfFontMetrics
    //     0xea0cec: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0xea0cf0: ldr             x0, [x0, #0x120]
    // 0xea0cf4: LeaveFrame
    //     0xea0cf4: mov             SP, fp
    //     0xea0cf8: ldp             fp, lr, [SP], #0x10
    // 0xea0cfc: ret
    //     0xea0cfc: ret             
    // 0xea0d00: r1 = Instance_Latin1Codec
    //     0xea0d00: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xea0d04: r0 = encode()
    //     0xea0d04: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xea0d08: ldur            x2, [fp, #-8]
    // 0xea0d0c: stur            x0, [fp, #-0x10]
    // 0xea0d10: r1 = LoadClassIdInstr(r2)
    //     0xea0d10: ldur            x1, [x2, #-1]
    //     0xea0d14: ubfx            x1, x1, #0xc, #0x14
    // 0xea0d18: cmp             x1, #0x37f
    // 0xea0d1c: b.ne            #0xea0d34
    // 0xea0d20: r1 = Function 'glyphMetrics':.
    //     0xea0d20: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e108] AnonymousClosure: (0xe71018), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics (0xe71054)
    //     0xea0d24: ldr             x1, [x1, #0x108]
    // 0xea0d28: r0 = AllocateClosure()
    //     0xea0d28: bl              #0xec1630  ; AllocateClosureStub
    // 0xea0d2c: mov             x3, x0
    // 0xea0d30: b               #0xea0d44
    // 0xea0d34: r1 = Function 'glyphMetrics':.
    //     0xea0d34: add             x1, PP, #0x47, lsl #12  ; [pp+0x47520] AnonymousClosure: (0x7b6e48), in [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::glyphMetrics (0x7b6cd0)
    //     0xea0d38: ldr             x1, [x1, #0x520]
    // 0xea0d3c: r0 = AllocateClosure()
    //     0xea0d3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xea0d40: mov             x3, x0
    // 0xea0d44: ldur            x2, [fp, #-0x10]
    // 0xea0d48: r1 = <PdfFontMetrics, int, PdfFontMetrics>
    //     0xea0d48: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e110] TypeArguments: <PdfFontMetrics, int, PdfFontMetrics>
    //     0xea0d4c: ldr             x1, [x1, #0x110]
    // 0xea0d50: r0 = MappedIterable()
    //     0xea0d50: bl              #0x7ac0ac  ; [dart:_internal] MappedIterable::MappedIterable
    // 0xea0d54: mov             x2, x0
    // 0xea0d58: ldur            d0, [fp, #-0x18]
    // 0xea0d5c: r1 = Null
    //     0xea0d5c: mov             x1, NULL
    // 0xea0d60: r0 = PdfFontMetrics.append()
    //     0xea0d60: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xea0d64: LeaveFrame
    //     0xea0d64: mov             SP, fp
    //     0xea0d68: ldp             fp, lr, [SP], #0x10
    // 0xea0d6c: ret
    //     0xea0d6c: ret             
    // 0xea0d70: r0 = StackOverflowSharedWithFPURegs()
    //     0xea0d70: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xea0d74: b               #0xea0ce4
  }
}
