// lib: , url: package:pdf/src/pdf/obj/font_descriptor.dart

// class id: 1050800, size: 0x8
class :: {
}

// class id: 893, size: 0x34, field offset: 0x2c
class PdfFontDescriptor extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7c9448, size: 0x770
    // 0x7c9448: EnterFrame
    //     0x7c9448: stp             fp, lr, [SP, #-0x10]!
    //     0x7c944c: mov             fp, SP
    // 0x7c9450: AllocStack(0x50)
    //     0x7c9450: sub             SP, SP, #0x50
    // 0x7c9454: SetupParameters(PdfFontDescriptor this /* r1 => r0, fp-0x10 */)
    //     0x7c9454: mov             x0, x1
    //     0x7c9458: stur            x1, [fp, #-0x10]
    // 0x7c945c: CheckStackOverflow
    //     0x7c945c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c9460: cmp             SP, x16
    //     0x7c9464: b.ls            #0x7c9aec
    // 0x7c9468: LoadField: r3 = r0->field_1b
    //     0x7c9468: ldur            w3, [x0, #0x1b]
    // 0x7c946c: DecompressPointer r3
    //     0x7c946c: add             x3, x3, HEAP, lsl #32
    // 0x7c9470: stur            x3, [fp, #-8]
    // 0x7c9474: r1 = Null
    //     0x7c9474: mov             x1, NULL
    // 0x7c9478: r2 = 4
    //     0x7c9478: movz            x2, #0x4
    // 0x7c947c: r0 = AllocateArray()
    //     0x7c947c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7c9480: stur            x0, [fp, #-0x28]
    // 0x7c9484: r16 = "/"
    //     0x7c9484: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7c9488: StoreField: r0->field_f = r16
    //     0x7c9488: stur            w16, [x0, #0xf]
    // 0x7c948c: ldur            x2, [fp, #-0x10]
    // 0x7c9490: LoadField: r3 = r2->field_2f
    //     0x7c9490: ldur            w3, [x2, #0x2f]
    // 0x7c9494: DecompressPointer r3
    //     0x7c9494: add             x3, x3, HEAP, lsl #32
    // 0x7c9498: stur            x3, [fp, #-0x20]
    // 0x7c949c: LoadField: r4 = r3->field_3f
    //     0x7c949c: ldur            w4, [x3, #0x3f]
    // 0x7c94a0: DecompressPointer r4
    //     0x7c94a0: add             x4, x4, HEAP, lsl #32
    // 0x7c94a4: mov             x1, x4
    // 0x7c94a8: stur            x4, [fp, #-0x18]
    // 0x7c94ac: r0 = fontName()
    //     0x7c94ac: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0x7c94b0: ldur            x1, [fp, #-0x28]
    // 0x7c94b4: ArrayStore: r1[1] = r0  ; List_4
    //     0x7c94b4: add             x25, x1, #0x13
    //     0x7c94b8: str             w0, [x25]
    //     0x7c94bc: tbz             w0, #0, #0x7c94d8
    //     0x7c94c0: ldurb           w16, [x1, #-1]
    //     0x7c94c4: ldurb           w17, [x0, #-1]
    //     0x7c94c8: and             x16, x17, x16, lsr #2
    //     0x7c94cc: tst             x16, HEAP, lsr #32
    //     0x7c94d0: b.eq            #0x7c94d8
    //     0x7c94d4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7c94d8: ldur            x16, [fp, #-0x28]
    // 0x7c94dc: str             x16, [SP]
    // 0x7c94e0: r0 = _interpolate()
    //     0x7c94e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7c94e4: stur            x0, [fp, #-0x28]
    // 0x7c94e8: r0 = PdfName()
    //     0x7c94e8: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7c94ec: mov             x3, x0
    // 0x7c94f0: ldur            x0, [fp, #-0x28]
    // 0x7c94f4: stur            x3, [fp, #-0x30]
    // 0x7c94f8: StoreField: r3->field_7 = r0
    //     0x7c94f8: stur            w0, [x3, #7]
    // 0x7c94fc: ldur            x4, [fp, #-8]
    // 0x7c9500: LoadField: r5 = r4->field_7
    //     0x7c9500: ldur            w5, [x4, #7]
    // 0x7c9504: DecompressPointer r5
    //     0x7c9504: add             x5, x5, HEAP, lsl #32
    // 0x7c9508: mov             x0, x3
    // 0x7c950c: mov             x2, x5
    // 0x7c9510: stur            x5, [fp, #-0x28]
    // 0x7c9514: r1 = Null
    //     0x7c9514: mov             x1, NULL
    // 0x7c9518: cmp             w2, NULL
    // 0x7c951c: b.eq            #0x7c9540
    // 0x7c9520: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9520: ldur            w4, [x2, #0x17]
    // 0x7c9524: DecompressPointer r4
    //     0x7c9524: add             x4, x4, HEAP, lsl #32
    // 0x7c9528: r8 = X0 bound PdfDataType
    //     0x7c9528: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c952c: ldr             x8, [x8, #0x6d8]
    // 0x7c9530: LoadField: r9 = r4->field_7
    //     0x7c9530: ldur            x9, [x4, #7]
    // 0x7c9534: r3 = Null
    //     0x7c9534: add             x3, PP, #0x46, lsl #12  ; [pp+0x46f88] Null
    //     0x7c9538: ldr             x3, [x3, #0xf88]
    // 0x7c953c: blr             x9
    // 0x7c9540: ldur            x0, [fp, #-8]
    // 0x7c9544: LoadField: r4 = r0->field_b
    //     0x7c9544: ldur            w4, [x0, #0xb]
    // 0x7c9548: DecompressPointer r4
    //     0x7c9548: add             x4, x4, HEAP, lsl #32
    // 0x7c954c: mov             x1, x4
    // 0x7c9550: ldur            x3, [fp, #-0x30]
    // 0x7c9554: stur            x4, [fp, #-0x38]
    // 0x7c9558: r2 = "/FontName"
    //     0x7c9558: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3ded8] "/FontName"
    //     0x7c955c: ldr             x2, [x2, #0xed8]
    // 0x7c9560: r0 = []=()
    //     0x7c9560: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9564: ldur            x0, [fp, #-0x10]
    // 0x7c9568: LoadField: r1 = r0->field_2b
    //     0x7c9568: ldur            w1, [x0, #0x2b]
    // 0x7c956c: DecompressPointer r1
    //     0x7c956c: add             x1, x1, HEAP, lsl #32
    // 0x7c9570: r0 = ref()
    //     0x7c9570: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7c9574: ldur            x2, [fp, #-0x28]
    // 0x7c9578: mov             x3, x0
    // 0x7c957c: r1 = Null
    //     0x7c957c: mov             x1, NULL
    // 0x7c9580: stur            x3, [fp, #-8]
    // 0x7c9584: cmp             w2, NULL
    // 0x7c9588: b.eq            #0x7c95ac
    // 0x7c958c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c958c: ldur            w4, [x2, #0x17]
    // 0x7c9590: DecompressPointer r4
    //     0x7c9590: add             x4, x4, HEAP, lsl #32
    // 0x7c9594: r8 = X0 bound PdfDataType
    //     0x7c9594: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9598: ldr             x8, [x8, #0x6d8]
    // 0x7c959c: LoadField: r9 = r4->field_7
    //     0x7c959c: ldur            x9, [x4, #7]
    // 0x7c95a0: r3 = Null
    //     0x7c95a0: add             x3, PP, #0x46, lsl #12  ; [pp+0x46f98] Null
    //     0x7c95a4: ldr             x3, [x3, #0xf98]
    // 0x7c95a8: blr             x9
    // 0x7c95ac: ldur            x1, [fp, #-0x38]
    // 0x7c95b0: ldur            x3, [fp, #-8]
    // 0x7c95b4: r2 = "/FontFile2"
    //     0x7c95b4: add             x2, PP, #0x46, lsl #12  ; [pp+0x46fa8] "/FontFile2"
    //     0x7c95b8: ldr             x2, [x2, #0xfa8]
    // 0x7c95bc: r0 = []=()
    //     0x7c95bc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c95c0: ldur            x2, [fp, #-0x18]
    // 0x7c95c4: LoadField: r3 = r2->field_7
    //     0x7c95c4: ldur            w3, [x2, #7]
    // 0x7c95c8: DecompressPointer r3
    //     0x7c95c8: add             x3, x3, HEAP, lsl #32
    // 0x7c95cc: LoadField: r0 = r3->field_13
    //     0x7c95cc: ldur            w0, [x3, #0x13]
    // 0x7c95d0: r1 = LoadInt32Instr(r0)
    //     0x7c95d0: sbfx            x1, x0, #1, #0x1f
    // 0x7c95d4: sub             x0, x1, #3
    // 0x7c95d8: r1 = 0
    //     0x7c95d8: movz            x1, #0
    // 0x7c95dc: cmp             x1, x0
    // 0x7c95e0: b.hs            #0x7c9af4
    // 0x7c95e4: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x7c95e4: ldur            w0, [x3, #0x17]
    // 0x7c95e8: DecompressPointer r0
    //     0x7c95e8: add             x0, x0, HEAP, lsl #32
    // 0x7c95ec: LoadField: r1 = r3->field_1b
    //     0x7c95ec: ldur            w1, [x3, #0x1b]
    // 0x7c95f0: LoadField: r3 = r0->field_7
    //     0x7c95f0: ldur            x3, [x0, #7]
    // 0x7c95f4: asr             w16, w1, #1
    // 0x7c95f8: add             x16, x3, w16, sxtw
    // 0x7c95fc: ldr             w0, [x16]
    // 0x7c9600: r1 = 4278255360
    //     0x7c9600: movz            x1, #0xff00
    //     0x7c9604: movk            x1, #0xff00, lsl #16
    // 0x7c9608: and             x3, x0, x1
    // 0x7c960c: ubfx            x3, x3, #0, #0x20
    // 0x7c9610: asr             x1, x3, #8
    // 0x7c9614: r3 = 16711935
    //     0x7c9614: movz            x3, #0xff
    //     0x7c9618: movk            x3, #0xff, lsl #16
    // 0x7c961c: and             x4, x0, x3
    // 0x7c9620: ubfx            x4, x4, #0, #0x20
    // 0x7c9624: lsl             x0, x4, #8
    // 0x7c9628: orr             x3, x1, x0
    // 0x7c962c: mov             x0, x3
    // 0x7c9630: ubfx            x0, x0, #0, #0x20
    // 0x7c9634: r1 = 4294901760
    //     0x7c9634: orr             x1, xzr, #0xffff0000
    // 0x7c9638: and             x4, x0, x1
    // 0x7c963c: ubfx            x4, x4, #0, #0x20
    // 0x7c9640: asr             x0, x4, #0x10
    // 0x7c9644: ubfx            x3, x3, #0, #0x20
    // 0x7c9648: r1 = 65535
    //     0x7c9648: orr             x1, xzr, #0xffff
    // 0x7c964c: and             x4, x3, x1
    // 0x7c9650: ubfx            x4, x4, #0, #0x20
    // 0x7c9654: lsl             x1, x4, #0x10
    // 0x7c9658: orr             x3, x0, x1
    // 0x7c965c: cmp             x3, #0x10, lsl #12
    // 0x7c9660: b.ne            #0x7c966c
    // 0x7c9664: r0 = 4
    //     0x7c9664: movz            x0, #0x4
    // 0x7c9668: b               #0x7c9670
    // 0x7c966c: r0 = 32
    //     0x7c966c: movz            x0, #0x20
    // 0x7c9670: lsl             x1, x0, #1
    // 0x7c9674: stur            x1, [fp, #-8]
    // 0x7c9678: r0 = PdfNum()
    //     0x7c9678: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7c967c: mov             x3, x0
    // 0x7c9680: ldur            x0, [fp, #-8]
    // 0x7c9684: stur            x3, [fp, #-0x10]
    // 0x7c9688: StoreField: r3->field_7 = r0
    //     0x7c9688: stur            w0, [x3, #7]
    // 0x7c968c: mov             x0, x3
    // 0x7c9690: ldur            x2, [fp, #-0x28]
    // 0x7c9694: r1 = Null
    //     0x7c9694: mov             x1, NULL
    // 0x7c9698: cmp             w2, NULL
    // 0x7c969c: b.eq            #0x7c96c0
    // 0x7c96a0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c96a0: ldur            w4, [x2, #0x17]
    // 0x7c96a4: DecompressPointer r4
    //     0x7c96a4: add             x4, x4, HEAP, lsl #32
    // 0x7c96a8: r8 = X0 bound PdfDataType
    //     0x7c96a8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c96ac: ldr             x8, [x8, #0x6d8]
    // 0x7c96b0: LoadField: r9 = r4->field_7
    //     0x7c96b0: ldur            x9, [x4, #7]
    // 0x7c96b4: r3 = Null
    //     0x7c96b4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46fb0] Null
    //     0x7c96b8: ldr             x3, [x3, #0xfb0]
    // 0x7c96bc: blr             x9
    // 0x7c96c0: ldur            x1, [fp, #-0x38]
    // 0x7c96c4: ldur            x3, [fp, #-0x10]
    // 0x7c96c8: r2 = "/Flags"
    //     0x7c96c8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dee0] "/Flags"
    //     0x7c96cc: ldr             x2, [x2, #0xee0]
    // 0x7c96d0: r0 = []=()
    //     0x7c96d0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c96d4: ldur            x1, [fp, #-0x18]
    // 0x7c96d8: r0 = xMin()
    //     0x7c96d8: bl              #0x7ca0bc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::xMin
    // 0x7c96dc: ldur            x1, [fp, #-0x18]
    // 0x7c96e0: stur            x0, [fp, #-0x40]
    // 0x7c96e4: r0 = unitsPerEm()
    //     0x7c96e4: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0x7c96e8: mov             x1, x0
    // 0x7c96ec: ldur            x0, [fp, #-0x40]
    // 0x7c96f0: scvtf           d0, x0
    // 0x7c96f4: scvtf           d1, x1
    // 0x7c96f8: fdiv            d2, d0, d1
    // 0x7c96fc: d0 = 1000.000000
    //     0x7c96fc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c9700: ldr             d0, [x17, #0x238]
    // 0x7c9704: fmul            d1, d2, d0
    // 0x7c9708: fcmp            d1, d1
    // 0x7c970c: b.vs            #0x7c9af8
    // 0x7c9710: fcvtzs          x0, d1
    // 0x7c9714: asr             x16, x0, #0x1e
    // 0x7c9718: cmp             x16, x0, asr #63
    // 0x7c971c: b.ne            #0x7c9af8
    // 0x7c9720: lsl             x0, x0, #1
    // 0x7c9724: ldur            x1, [fp, #-0x18]
    // 0x7c9728: stur            x0, [fp, #-8]
    // 0x7c972c: r0 = yMin()
    //     0x7c972c: bl              #0x7c9eb0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::yMin
    // 0x7c9730: ldur            x1, [fp, #-0x18]
    // 0x7c9734: stur            x0, [fp, #-0x40]
    // 0x7c9738: r0 = unitsPerEm()
    //     0x7c9738: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0x7c973c: mov             x1, x0
    // 0x7c9740: ldur            x0, [fp, #-0x40]
    // 0x7c9744: scvtf           d0, x0
    // 0x7c9748: scvtf           d1, x1
    // 0x7c974c: fdiv            d2, d0, d1
    // 0x7c9750: d0 = 1000.000000
    //     0x7c9750: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c9754: ldr             d0, [x17, #0x238]
    // 0x7c9758: fmul            d1, d2, d0
    // 0x7c975c: fcmp            d1, d1
    // 0x7c9760: b.vs            #0x7c9b18
    // 0x7c9764: fcvtzs          x0, d1
    // 0x7c9768: asr             x16, x0, #0x1e
    // 0x7c976c: cmp             x16, x0, asr #63
    // 0x7c9770: b.ne            #0x7c9b18
    // 0x7c9774: lsl             x0, x0, #1
    // 0x7c9778: ldur            x1, [fp, #-0x18]
    // 0x7c977c: stur            x0, [fp, #-0x10]
    // 0x7c9780: r0 = xMax()
    //     0x7c9780: bl              #0x7c9da0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::xMax
    // 0x7c9784: ldur            x1, [fp, #-0x18]
    // 0x7c9788: stur            x0, [fp, #-0x40]
    // 0x7c978c: r0 = unitsPerEm()
    //     0x7c978c: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0x7c9790: mov             x1, x0
    // 0x7c9794: ldur            x0, [fp, #-0x40]
    // 0x7c9798: scvtf           d0, x0
    // 0x7c979c: scvtf           d1, x1
    // 0x7c97a0: fdiv            d2, d0, d1
    // 0x7c97a4: d0 = 1000.000000
    //     0x7c97a4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c97a8: ldr             d0, [x17, #0x238]
    // 0x7c97ac: fmul            d1, d2, d0
    // 0x7c97b0: fcmp            d1, d1
    // 0x7c97b4: b.vs            #0x7c9b38
    // 0x7c97b8: fcvtzs          x0, d1
    // 0x7c97bc: asr             x16, x0, #0x1e
    // 0x7c97c0: cmp             x16, x0, asr #63
    // 0x7c97c4: b.ne            #0x7c9b38
    // 0x7c97c8: lsl             x0, x0, #1
    // 0x7c97cc: ldur            x1, [fp, #-0x18]
    // 0x7c97d0: stur            x0, [fp, #-0x30]
    // 0x7c97d4: r0 = yMax()
    //     0x7c97d4: bl              #0x7c9c90  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::yMax
    // 0x7c97d8: ldur            x1, [fp, #-0x18]
    // 0x7c97dc: stur            x0, [fp, #-0x40]
    // 0x7c97e0: r0 = unitsPerEm()
    //     0x7c97e0: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0x7c97e4: mov             x1, x0
    // 0x7c97e8: ldur            x0, [fp, #-0x40]
    // 0x7c97ec: scvtf           d0, x0
    // 0x7c97f0: scvtf           d1, x1
    // 0x7c97f4: fdiv            d2, d0, d1
    // 0x7c97f8: d0 = 1000.000000
    //     0x7c97f8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c97fc: ldr             d0, [x17, #0x238]
    // 0x7c9800: fmul            d1, d2, d0
    // 0x7c9804: fcmp            d1, d1
    // 0x7c9808: b.vs            #0x7c9b58
    // 0x7c980c: fcvtzs          x0, d1
    // 0x7c9810: asr             x16, x0, #0x1e
    // 0x7c9814: cmp             x16, x0, asr #63
    // 0x7c9818: b.ne            #0x7c9b58
    // 0x7c981c: lsl             x0, x0, #1
    // 0x7c9820: stur            x0, [fp, #-0x18]
    // 0x7c9824: r1 = Null
    //     0x7c9824: mov             x1, NULL
    // 0x7c9828: r2 = 8
    //     0x7c9828: movz            x2, #0x8
    // 0x7c982c: r0 = AllocateArray()
    //     0x7c982c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7c9830: mov             x2, x0
    // 0x7c9834: ldur            x0, [fp, #-8]
    // 0x7c9838: stur            x2, [fp, #-0x48]
    // 0x7c983c: StoreField: r2->field_f = r0
    //     0x7c983c: stur            w0, [x2, #0xf]
    // 0x7c9840: ldur            x0, [fp, #-0x10]
    // 0x7c9844: StoreField: r2->field_13 = r0
    //     0x7c9844: stur            w0, [x2, #0x13]
    // 0x7c9848: ldur            x0, [fp, #-0x30]
    // 0x7c984c: ArrayStore: r2[0] = r0  ; List_4
    //     0x7c984c: stur            w0, [x2, #0x17]
    // 0x7c9850: ldur            x0, [fp, #-0x18]
    // 0x7c9854: StoreField: r2->field_1b = r0
    //     0x7c9854: stur            w0, [x2, #0x1b]
    // 0x7c9858: r1 = <int>
    //     0x7c9858: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7c985c: r0 = AllocateGrowableArray()
    //     0x7c985c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7c9860: mov             x1, x0
    // 0x7c9864: ldur            x0, [fp, #-0x48]
    // 0x7c9868: StoreField: r1->field_f = r0
    //     0x7c9868: stur            w0, [x1, #0xf]
    // 0x7c986c: r0 = 8
    //     0x7c986c: movz            x0, #0x8
    // 0x7c9870: StoreField: r1->field_b = r0
    //     0x7c9870: stur            w0, [x1, #0xb]
    // 0x7c9874: r0 = fromNum()
    //     0x7c9874: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7c9878: ldur            x2, [fp, #-0x28]
    // 0x7c987c: mov             x3, x0
    // 0x7c9880: r1 = Null
    //     0x7c9880: mov             x1, NULL
    // 0x7c9884: stur            x3, [fp, #-8]
    // 0x7c9888: cmp             w2, NULL
    // 0x7c988c: b.eq            #0x7c98b0
    // 0x7c9890: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9890: ldur            w4, [x2, #0x17]
    // 0x7c9894: DecompressPointer r4
    //     0x7c9894: add             x4, x4, HEAP, lsl #32
    // 0x7c9898: r8 = X0 bound PdfDataType
    //     0x7c9898: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c989c: ldr             x8, [x8, #0x6d8]
    // 0x7c98a0: LoadField: r9 = r4->field_7
    //     0x7c98a0: ldur            x9, [x4, #7]
    // 0x7c98a4: r3 = Null
    //     0x7c98a4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46fc0] Null
    //     0x7c98a8: ldr             x3, [x3, #0xfc0]
    // 0x7c98ac: blr             x9
    // 0x7c98b0: ldur            x1, [fp, #-0x38]
    // 0x7c98b4: ldur            x3, [fp, #-8]
    // 0x7c98b8: r2 = "/FontBBox"
    //     0x7c98b8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dee8] "/FontBBox"
    //     0x7c98bc: ldr             x2, [x2, #0xee8]
    // 0x7c98c0: r0 = []=()
    //     0x7c98c0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c98c4: ldur            x1, [fp, #-0x20]
    // 0x7c98c8: r0 = ascent()
    //     0x7c98c8: bl              #0xe8e35c  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::ascent
    // 0x7c98cc: mov             v1.16b, v0.16b
    // 0x7c98d0: d0 = 1000.000000
    //     0x7c98d0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c98d4: ldr             d0, [x17, #0x238]
    // 0x7c98d8: fmul            d2, d1, d0
    // 0x7c98dc: fcmp            d2, d2
    // 0x7c98e0: b.vs            #0x7c9b78
    // 0x7c98e4: fcvtzs          x0, d2
    // 0x7c98e8: asr             x16, x0, #0x1e
    // 0x7c98ec: cmp             x16, x0, asr #63
    // 0x7c98f0: b.ne            #0x7c9b78
    // 0x7c98f4: lsl             x0, x0, #1
    // 0x7c98f8: stur            x0, [fp, #-8]
    // 0x7c98fc: r0 = PdfNum()
    //     0x7c98fc: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7c9900: mov             x3, x0
    // 0x7c9904: ldur            x0, [fp, #-8]
    // 0x7c9908: stur            x3, [fp, #-0x10]
    // 0x7c990c: StoreField: r3->field_7 = r0
    //     0x7c990c: stur            w0, [x3, #7]
    // 0x7c9910: mov             x0, x3
    // 0x7c9914: ldur            x2, [fp, #-0x28]
    // 0x7c9918: r1 = Null
    //     0x7c9918: mov             x1, NULL
    // 0x7c991c: cmp             w2, NULL
    // 0x7c9920: b.eq            #0x7c9944
    // 0x7c9924: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9924: ldur            w4, [x2, #0x17]
    // 0x7c9928: DecompressPointer r4
    //     0x7c9928: add             x4, x4, HEAP, lsl #32
    // 0x7c992c: r8 = X0 bound PdfDataType
    //     0x7c992c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9930: ldr             x8, [x8, #0x6d8]
    // 0x7c9934: LoadField: r9 = r4->field_7
    //     0x7c9934: ldur            x9, [x4, #7]
    // 0x7c9938: r3 = Null
    //     0x7c9938: add             x3, PP, #0x46, lsl #12  ; [pp+0x46fd0] Null
    //     0x7c993c: ldr             x3, [x3, #0xfd0]
    // 0x7c9940: blr             x9
    // 0x7c9944: ldur            x1, [fp, #-0x38]
    // 0x7c9948: ldur            x3, [fp, #-0x10]
    // 0x7c994c: r2 = "/Ascent"
    //     0x7c994c: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3def0] "/Ascent"
    //     0x7c9950: ldr             x2, [x2, #0xef0]
    // 0x7c9954: r0 = []=()
    //     0x7c9954: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9958: ldur            x1, [fp, #-0x20]
    // 0x7c995c: r0 = descent()
    //     0x7c995c: bl              #0xea0a04  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::descent
    // 0x7c9960: mov             v1.16b, v0.16b
    // 0x7c9964: d0 = 1000.000000
    //     0x7c9964: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7c9968: ldr             d0, [x17, #0x238]
    // 0x7c996c: fmul            d2, d1, d0
    // 0x7c9970: fcmp            d2, d2
    // 0x7c9974: b.vs            #0x7c9b98
    // 0x7c9978: fcvtzs          x0, d2
    // 0x7c997c: asr             x16, x0, #0x1e
    // 0x7c9980: cmp             x16, x0, asr #63
    // 0x7c9984: b.ne            #0x7c9b98
    // 0x7c9988: lsl             x0, x0, #1
    // 0x7c998c: stur            x0, [fp, #-8]
    // 0x7c9990: r0 = PdfNum()
    //     0x7c9990: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7c9994: mov             x3, x0
    // 0x7c9998: ldur            x0, [fp, #-8]
    // 0x7c999c: stur            x3, [fp, #-0x10]
    // 0x7c99a0: StoreField: r3->field_7 = r0
    //     0x7c99a0: stur            w0, [x3, #7]
    // 0x7c99a4: mov             x0, x3
    // 0x7c99a8: ldur            x2, [fp, #-0x28]
    // 0x7c99ac: r1 = Null
    //     0x7c99ac: mov             x1, NULL
    // 0x7c99b0: cmp             w2, NULL
    // 0x7c99b4: b.eq            #0x7c99d8
    // 0x7c99b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c99b8: ldur            w4, [x2, #0x17]
    // 0x7c99bc: DecompressPointer r4
    //     0x7c99bc: add             x4, x4, HEAP, lsl #32
    // 0x7c99c0: r8 = X0 bound PdfDataType
    //     0x7c99c0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c99c4: ldr             x8, [x8, #0x6d8]
    // 0x7c99c8: LoadField: r9 = r4->field_7
    //     0x7c99c8: ldur            x9, [x4, #7]
    // 0x7c99cc: r3 = Null
    //     0x7c99cc: add             x3, PP, #0x46, lsl #12  ; [pp+0x46fe0] Null
    //     0x7c99d0: ldr             x3, [x3, #0xfe0]
    // 0x7c99d4: blr             x9
    // 0x7c99d8: ldur            x1, [fp, #-0x38]
    // 0x7c99dc: ldur            x3, [fp, #-0x10]
    // 0x7c99e0: r2 = "/Descent"
    //     0x7c99e0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3def8] "/Descent"
    //     0x7c99e4: ldr             x2, [x2, #0xef8]
    // 0x7c99e8: r0 = []=()
    //     0x7c99e8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c99ec: ldur            x2, [fp, #-0x28]
    // 0x7c99f0: r0 = Instance_PdfNum
    //     0x7c99f0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7c99f4: ldr             x0, [x0, #0x148]
    // 0x7c99f8: r1 = Null
    //     0x7c99f8: mov             x1, NULL
    // 0x7c99fc: cmp             w2, NULL
    // 0x7c9a00: b.eq            #0x7c9a24
    // 0x7c9a04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9a04: ldur            w4, [x2, #0x17]
    // 0x7c9a08: DecompressPointer r4
    //     0x7c9a08: add             x4, x4, HEAP, lsl #32
    // 0x7c9a0c: r8 = X0 bound PdfDataType
    //     0x7c9a0c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9a10: ldr             x8, [x8, #0x6d8]
    // 0x7c9a14: LoadField: r9 = r4->field_7
    //     0x7c9a14: ldur            x9, [x4, #7]
    // 0x7c9a18: r3 = Null
    //     0x7c9a18: add             x3, PP, #0x46, lsl #12  ; [pp+0x46ff0] Null
    //     0x7c9a1c: ldr             x3, [x3, #0xff0]
    // 0x7c9a20: blr             x9
    // 0x7c9a24: ldur            x1, [fp, #-0x38]
    // 0x7c9a28: r2 = "/ItalicAngle"
    //     0x7c9a28: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3df00] "/ItalicAngle"
    //     0x7c9a2c: ldr             x2, [x2, #0xf00]
    // 0x7c9a30: r3 = Instance_PdfNum
    //     0x7c9a30: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7c9a34: ldr             x3, [x3, #0x148]
    // 0x7c9a38: r0 = []=()
    //     0x7c9a38: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9a3c: ldur            x2, [fp, #-0x28]
    // 0x7c9a40: r0 = Instance_PdfNum
    //     0x7c9a40: add             x0, PP, #0x47, lsl #12  ; [pp+0x47000] Obj!PdfNum@e0c7a1
    //     0x7c9a44: ldr             x0, [x0]
    // 0x7c9a48: r1 = Null
    //     0x7c9a48: mov             x1, NULL
    // 0x7c9a4c: cmp             w2, NULL
    // 0x7c9a50: b.eq            #0x7c9a74
    // 0x7c9a54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9a54: ldur            w4, [x2, #0x17]
    // 0x7c9a58: DecompressPointer r4
    //     0x7c9a58: add             x4, x4, HEAP, lsl #32
    // 0x7c9a5c: r8 = X0 bound PdfDataType
    //     0x7c9a5c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9a60: ldr             x8, [x8, #0x6d8]
    // 0x7c9a64: LoadField: r9 = r4->field_7
    //     0x7c9a64: ldur            x9, [x4, #7]
    // 0x7c9a68: r3 = Null
    //     0x7c9a68: add             x3, PP, #0x47, lsl #12  ; [pp+0x47008] Null
    //     0x7c9a6c: ldr             x3, [x3, #8]
    // 0x7c9a70: blr             x9
    // 0x7c9a74: ldur            x1, [fp, #-0x38]
    // 0x7c9a78: r2 = "/CapHeight"
    //     0x7c9a78: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3df08] "/CapHeight"
    //     0x7c9a7c: ldr             x2, [x2, #0xf08]
    // 0x7c9a80: r3 = Instance_PdfNum
    //     0x7c9a80: add             x3, PP, #0x47, lsl #12  ; [pp+0x47000] Obj!PdfNum@e0c7a1
    //     0x7c9a84: ldr             x3, [x3]
    // 0x7c9a88: r0 = []=()
    //     0x7c9a88: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9a8c: ldur            x2, [fp, #-0x28]
    // 0x7c9a90: r0 = Instance_PdfNum
    //     0x7c9a90: add             x0, PP, #0x47, lsl #12  ; [pp+0x47018] Obj!PdfNum@e0c791
    //     0x7c9a94: ldr             x0, [x0, #0x18]
    // 0x7c9a98: r1 = Null
    //     0x7c9a98: mov             x1, NULL
    // 0x7c9a9c: cmp             w2, NULL
    // 0x7c9aa0: b.eq            #0x7c9ac4
    // 0x7c9aa4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7c9aa4: ldur            w4, [x2, #0x17]
    // 0x7c9aa8: DecompressPointer r4
    //     0x7c9aa8: add             x4, x4, HEAP, lsl #32
    // 0x7c9aac: r8 = X0 bound PdfDataType
    //     0x7c9aac: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7c9ab0: ldr             x8, [x8, #0x6d8]
    // 0x7c9ab4: LoadField: r9 = r4->field_7
    //     0x7c9ab4: ldur            x9, [x4, #7]
    // 0x7c9ab8: r3 = Null
    //     0x7c9ab8: add             x3, PP, #0x47, lsl #12  ; [pp+0x47020] Null
    //     0x7c9abc: ldr             x3, [x3, #0x20]
    // 0x7c9ac0: blr             x9
    // 0x7c9ac4: ldur            x1, [fp, #-0x38]
    // 0x7c9ac8: r2 = "/StemV"
    //     0x7c9ac8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3df10] "/StemV"
    //     0x7c9acc: ldr             x2, [x2, #0xf10]
    // 0x7c9ad0: r3 = Instance_PdfNum
    //     0x7c9ad0: add             x3, PP, #0x47, lsl #12  ; [pp+0x47018] Obj!PdfNum@e0c791
    //     0x7c9ad4: ldr             x3, [x3, #0x18]
    // 0x7c9ad8: r0 = []=()
    //     0x7c9ad8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7c9adc: r0 = Null
    //     0x7c9adc: mov             x0, NULL
    // 0x7c9ae0: LeaveFrame
    //     0x7c9ae0: mov             SP, fp
    //     0x7c9ae4: ldp             fp, lr, [SP], #0x10
    // 0x7c9ae8: ret
    //     0x7c9ae8: ret             
    // 0x7c9aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c9aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c9af0: b               #0x7c9468
    // 0x7c9af4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7c9af4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7c9af8: stp             q0, q1, [SP, #-0x20]!
    // 0x7c9afc: d0 = 0.000000
    //     0x7c9afc: fmov            d0, d1
    // 0x7c9b00: r0 = 74
    //     0x7c9b00: movz            x0, #0x4a
    // 0x7c9b04: r30 = DoubleToIntegerStub
    //     0x7c9b04: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9b08: LoadField: r30 = r30->field_7
    //     0x7c9b08: ldur            lr, [lr, #7]
    // 0x7c9b0c: blr             lr
    // 0x7c9b10: ldp             q0, q1, [SP], #0x20
    // 0x7c9b14: b               #0x7c9724
    // 0x7c9b18: stp             q0, q1, [SP, #-0x20]!
    // 0x7c9b1c: d0 = 0.000000
    //     0x7c9b1c: fmov            d0, d1
    // 0x7c9b20: r0 = 74
    //     0x7c9b20: movz            x0, #0x4a
    // 0x7c9b24: r30 = DoubleToIntegerStub
    //     0x7c9b24: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9b28: LoadField: r30 = r30->field_7
    //     0x7c9b28: ldur            lr, [lr, #7]
    // 0x7c9b2c: blr             lr
    // 0x7c9b30: ldp             q0, q1, [SP], #0x20
    // 0x7c9b34: b               #0x7c9778
    // 0x7c9b38: stp             q0, q1, [SP, #-0x20]!
    // 0x7c9b3c: d0 = 0.000000
    //     0x7c9b3c: fmov            d0, d1
    // 0x7c9b40: r0 = 74
    //     0x7c9b40: movz            x0, #0x4a
    // 0x7c9b44: r30 = DoubleToIntegerStub
    //     0x7c9b44: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9b48: LoadField: r30 = r30->field_7
    //     0x7c9b48: ldur            lr, [lr, #7]
    // 0x7c9b4c: blr             lr
    // 0x7c9b50: ldp             q0, q1, [SP], #0x20
    // 0x7c9b54: b               #0x7c97cc
    // 0x7c9b58: stp             q0, q1, [SP, #-0x20]!
    // 0x7c9b5c: d0 = 0.000000
    //     0x7c9b5c: fmov            d0, d1
    // 0x7c9b60: r0 = 74
    //     0x7c9b60: movz            x0, #0x4a
    // 0x7c9b64: r30 = DoubleToIntegerStub
    //     0x7c9b64: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9b68: LoadField: r30 = r30->field_7
    //     0x7c9b68: ldur            lr, [lr, #7]
    // 0x7c9b6c: blr             lr
    // 0x7c9b70: ldp             q0, q1, [SP], #0x20
    // 0x7c9b74: b               #0x7c9820
    // 0x7c9b78: stp             q0, q2, [SP, #-0x20]!
    // 0x7c9b7c: d0 = 0.000000
    //     0x7c9b7c: fmov            d0, d2
    // 0x7c9b80: r0 = 74
    //     0x7c9b80: movz            x0, #0x4a
    // 0x7c9b84: r30 = DoubleToIntegerStub
    //     0x7c9b84: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9b88: LoadField: r30 = r30->field_7
    //     0x7c9b88: ldur            lr, [lr, #7]
    // 0x7c9b8c: blr             lr
    // 0x7c9b90: ldp             q0, q2, [SP], #0x20
    // 0x7c9b94: b               #0x7c98f8
    // 0x7c9b98: SaveReg d2
    //     0x7c9b98: str             q2, [SP, #-0x10]!
    // 0x7c9b9c: d0 = 0.000000
    //     0x7c9b9c: fmov            d0, d2
    // 0x7c9ba0: r0 = 74
    //     0x7c9ba0: movz            x0, #0x4a
    // 0x7c9ba4: r30 = DoubleToIntegerStub
    //     0x7c9ba4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7c9ba8: LoadField: r30 = r30->field_7
    //     0x7c9ba8: ldur            lr, [lr, #7]
    // 0x7c9bac: blr             lr
    // 0x7c9bb0: RestoreReg d2
    //     0x7c9bb0: ldr             q2, [SP], #0x10
    // 0x7c9bb4: b               #0x7c998c
  }
  _ PdfFontDescriptor(/* No info */) {
    // ** addr: 0xe65e90, size: 0xec
    // 0xe65e90: EnterFrame
    //     0xe65e90: stp             fp, lr, [SP, #-0x10]!
    //     0xe65e94: mov             fp, SP
    // 0xe65e98: AllocStack(0x28)
    //     0xe65e98: sub             SP, SP, #0x28
    // 0xe65e9c: SetupParameters(PdfFontDescriptor this /* r1 => r3, fp-0x10 */, dynamic _ /* r3 => r1 */)
    //     0xe65e9c: stur            x1, [fp, #-0x10]
    //     0xe65ea0: mov             x16, x3
    //     0xe65ea4: mov             x3, x1
    //     0xe65ea8: mov             x1, x16
    // 0xe65eac: CheckStackOverflow
    //     0xe65eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe65eb0: cmp             SP, x16
    //     0xe65eb4: b.ls            #0xe65f74
    // 0xe65eb8: mov             x0, x2
    // 0xe65ebc: StoreField: r3->field_2f = r0
    //     0xe65ebc: stur            w0, [x3, #0x2f]
    //     0xe65ec0: ldurb           w16, [x3, #-1]
    //     0xe65ec4: ldurb           w17, [x0, #-1]
    //     0xe65ec8: and             x16, x17, x16, lsr #2
    //     0xe65ecc: tst             x16, HEAP, lsr #32
    //     0xe65ed0: b.eq            #0xe65ed8
    //     0xe65ed4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe65ed8: mov             x0, x1
    // 0xe65edc: StoreField: r3->field_2b = r0
    //     0xe65edc: stur            w0, [x3, #0x2b]
    //     0xe65ee0: ldurb           w16, [x3, #-1]
    //     0xe65ee4: ldurb           w17, [x0, #-1]
    //     0xe65ee8: and             x16, x17, x16, lsr #2
    //     0xe65eec: tst             x16, HEAP, lsr #32
    //     0xe65ef0: b.eq            #0xe65ef8
    //     0xe65ef4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe65ef8: LoadField: r0 = r2->field_23
    //     0xe65ef8: ldur            w0, [x2, #0x23]
    // 0xe65efc: DecompressPointer r0
    //     0xe65efc: add             x0, x0, HEAP, lsl #32
    // 0xe65f00: stur            x0, [fp, #-8]
    // 0xe65f04: r1 = Null
    //     0xe65f04: mov             x1, NULL
    // 0xe65f08: r2 = 4
    //     0xe65f08: movz            x2, #0x4
    // 0xe65f0c: r0 = AllocateArray()
    //     0xe65f0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe65f10: r16 = "/Type"
    //     0xe65f10: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe65f14: ldr             x16, [x16, #0x630]
    // 0xe65f18: StoreField: r0->field_f = r16
    //     0xe65f18: stur            w16, [x0, #0xf]
    // 0xe65f1c: r16 = Instance_PdfName
    //     0xe65f1c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ded0] Obj!PdfName@e0c8b1
    //     0xe65f20: ldr             x16, [x16, #0xed0]
    // 0xe65f24: StoreField: r0->field_13 = r16
    //     0xe65f24: stur            w16, [x0, #0x13]
    // 0xe65f28: r16 = <String, PdfDataType>
    //     0xe65f28: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe65f2c: ldr             x16, [x16, #0x820]
    // 0xe65f30: stp             x0, x16, [SP]
    // 0xe65f34: r0 = Map._fromLiteral()
    //     0xe65f34: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe65f38: r1 = <PdfDataType>
    //     0xe65f38: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe65f3c: ldr             x1, [x1, #0x4c8]
    // 0xe65f40: stur            x0, [fp, #-0x18]
    // 0xe65f44: r0 = PdfDict()
    //     0xe65f44: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe65f48: mov             x1, x0
    // 0xe65f4c: ldur            x0, [fp, #-0x18]
    // 0xe65f50: StoreField: r1->field_b = r0
    //     0xe65f50: stur            w0, [x1, #0xb]
    // 0xe65f54: mov             x3, x1
    // 0xe65f58: ldur            x1, [fp, #-0x10]
    // 0xe65f5c: ldur            x2, [fp, #-8]
    // 0xe65f60: r0 = PdfObject()
    //     0xe65f60: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe65f64: r0 = Null
    //     0xe65f64: mov             x0, NULL
    // 0xe65f68: LeaveFrame
    //     0xe65f68: mov             SP, fp
    //     0xe65f6c: ldp             fp, lr, [SP], #0x10
    // 0xe65f70: ret
    //     0xe65f70: ret             
    // 0xe65f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe65f78: b               #0xe65eb8
  }
}
