// lib: , url: package:pdf/src/pdf/obj/function.dart

// class id: 1050801, size: 0x8
class :: {
}

// class id: 890, size: 0x50, field offset: 0x34
class PdfFunction extends PdfObjectStream
    implements PdfBaseFunction {

  _ prepare(/* No info */) {
    // ** addr: 0x7ca614, size: 0x2dc
    // 0x7ca614: EnterFrame
    //     0x7ca614: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca618: mov             fp, SP
    // 0x7ca61c: AllocStack(0x20)
    //     0x7ca61c: sub             SP, SP, #0x20
    // 0x7ca620: SetupParameters(PdfFunction this /* r1 => r0, fp-0x10 */)
    //     0x7ca620: mov             x0, x1
    //     0x7ca624: stur            x1, [fp, #-0x10]
    // 0x7ca628: CheckStackOverflow
    //     0x7ca628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca62c: cmp             SP, x16
    //     0x7ca630: b.ls            #0x7ca8e8
    // 0x7ca634: LoadField: r1 = r0->field_2b
    //     0x7ca634: ldur            w1, [x0, #0x2b]
    // 0x7ca638: DecompressPointer r1
    //     0x7ca638: add             x1, x1, HEAP, lsl #32
    // 0x7ca63c: LoadField: r3 = r0->field_33
    //     0x7ca63c: ldur            w3, [x0, #0x33]
    // 0x7ca640: DecompressPointer r3
    //     0x7ca640: add             x3, x3, HEAP, lsl #32
    // 0x7ca644: mov             x2, x3
    // 0x7ca648: stur            x3, [fp, #-8]
    // 0x7ca64c: r0 = putBytes()
    //     0x7ca64c: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0x7ca650: ldur            x0, [fp, #-0x10]
    // 0x7ca654: LoadField: r3 = r0->field_1b
    //     0x7ca654: ldur            w3, [x0, #0x1b]
    // 0x7ca658: DecompressPointer r3
    //     0x7ca658: add             x3, x3, HEAP, lsl #32
    // 0x7ca65c: stur            x3, [fp, #-0x18]
    // 0x7ca660: LoadField: r4 = r3->field_7
    //     0x7ca660: ldur            w4, [x3, #7]
    // 0x7ca664: DecompressPointer r4
    //     0x7ca664: add             x4, x4, HEAP, lsl #32
    // 0x7ca668: mov             x2, x4
    // 0x7ca66c: stur            x4, [fp, #-0x10]
    // 0x7ca670: r0 = Instance_PdfNum
    //     0x7ca670: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7ca674: ldr             x0, [x0, #0x148]
    // 0x7ca678: r1 = Null
    //     0x7ca678: mov             x1, NULL
    // 0x7ca67c: cmp             w2, NULL
    // 0x7ca680: b.eq            #0x7ca6a4
    // 0x7ca684: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca684: ldur            w4, [x2, #0x17]
    // 0x7ca688: DecompressPointer r4
    //     0x7ca688: add             x4, x4, HEAP, lsl #32
    // 0x7ca68c: r8 = X0 bound PdfDataType
    //     0x7ca68c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca690: ldr             x8, [x8, #0x6d8]
    // 0x7ca694: LoadField: r9 = r4->field_7
    //     0x7ca694: ldur            x9, [x4, #7]
    // 0x7ca698: r3 = Null
    //     0x7ca698: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b078] Null
    //     0x7ca69c: ldr             x3, [x3, #0x78]
    // 0x7ca6a0: blr             x9
    // 0x7ca6a4: ldur            x0, [fp, #-0x18]
    // 0x7ca6a8: LoadField: r4 = r0->field_b
    //     0x7ca6a8: ldur            w4, [x0, #0xb]
    // 0x7ca6ac: DecompressPointer r4
    //     0x7ca6ac: add             x4, x4, HEAP, lsl #32
    // 0x7ca6b0: mov             x1, x4
    // 0x7ca6b4: stur            x4, [fp, #-0x20]
    // 0x7ca6b8: r2 = "/FunctionType"
    //     0x7ca6b8: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b088] "/FunctionType"
    //     0x7ca6bc: ldr             x2, [x2, #0x88]
    // 0x7ca6c0: r3 = Instance_PdfNum
    //     0x7ca6c0: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7ca6c4: ldr             x3, [x3, #0x148]
    // 0x7ca6c8: r0 = []=()
    //     0x7ca6c8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca6cc: r0 = PdfNum()
    //     0x7ca6cc: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7ca6d0: mov             x3, x0
    // 0x7ca6d4: r0 = 16
    //     0x7ca6d4: movz            x0, #0x10
    // 0x7ca6d8: stur            x3, [fp, #-0x18]
    // 0x7ca6dc: StoreField: r3->field_7 = r0
    //     0x7ca6dc: stur            w0, [x3, #7]
    // 0x7ca6e0: mov             x0, x3
    // 0x7ca6e4: ldur            x2, [fp, #-0x10]
    // 0x7ca6e8: r1 = Null
    //     0x7ca6e8: mov             x1, NULL
    // 0x7ca6ec: cmp             w2, NULL
    // 0x7ca6f0: b.eq            #0x7ca714
    // 0x7ca6f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca6f4: ldur            w4, [x2, #0x17]
    // 0x7ca6f8: DecompressPointer r4
    //     0x7ca6f8: add             x4, x4, HEAP, lsl #32
    // 0x7ca6fc: r8 = X0 bound PdfDataType
    //     0x7ca6fc: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca700: ldr             x8, [x8, #0x6d8]
    // 0x7ca704: LoadField: r9 = r4->field_7
    //     0x7ca704: ldur            x9, [x4, #7]
    // 0x7ca708: r3 = Null
    //     0x7ca708: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b090] Null
    //     0x7ca70c: ldr             x3, [x3, #0x90]
    // 0x7ca710: blr             x9
    // 0x7ca714: ldur            x1, [fp, #-0x20]
    // 0x7ca718: ldur            x3, [fp, #-0x18]
    // 0x7ca71c: r2 = "/BitsPerSample"
    //     0x7ca71c: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0a0] "/BitsPerSample"
    //     0x7ca720: ldr             x2, [x2, #0xa0]
    // 0x7ca724: r0 = []=()
    //     0x7ca724: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca728: r0 = PdfNum()
    //     0x7ca728: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7ca72c: mov             x3, x0
    // 0x7ca730: r0 = 6
    //     0x7ca730: movz            x0, #0x6
    // 0x7ca734: stur            x3, [fp, #-0x18]
    // 0x7ca738: StoreField: r3->field_7 = r0
    //     0x7ca738: stur            w0, [x3, #7]
    // 0x7ca73c: mov             x0, x3
    // 0x7ca740: ldur            x2, [fp, #-0x10]
    // 0x7ca744: r1 = Null
    //     0x7ca744: mov             x1, NULL
    // 0x7ca748: cmp             w2, NULL
    // 0x7ca74c: b.eq            #0x7ca770
    // 0x7ca750: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca750: ldur            w4, [x2, #0x17]
    // 0x7ca754: DecompressPointer r4
    //     0x7ca754: add             x4, x4, HEAP, lsl #32
    // 0x7ca758: r8 = X0 bound PdfDataType
    //     0x7ca758: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca75c: ldr             x8, [x8, #0x6d8]
    // 0x7ca760: LoadField: r9 = r4->field_7
    //     0x7ca760: ldur            x9, [x4, #7]
    // 0x7ca764: r3 = Null
    //     0x7ca764: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b0a8] Null
    //     0x7ca768: ldr             x3, [x3, #0xa8]
    // 0x7ca76c: blr             x9
    // 0x7ca770: ldur            x1, [fp, #-0x20]
    // 0x7ca774: ldur            x3, [fp, #-0x18]
    // 0x7ca778: r2 = "/Order"
    //     0x7ca778: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0b8] "/Order"
    //     0x7ca77c: ldr             x2, [x2, #0xb8]
    // 0x7ca780: r0 = []=()
    //     0x7ca780: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca784: r1 = const [0, 0x1]
    //     0x7ca784: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a58] List<num>(2)
    //     0x7ca788: ldr             x1, [x1, #0xa58]
    // 0x7ca78c: r0 = fromNum()
    //     0x7ca78c: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca790: ldur            x2, [fp, #-0x10]
    // 0x7ca794: mov             x3, x0
    // 0x7ca798: r1 = Null
    //     0x7ca798: mov             x1, NULL
    // 0x7ca79c: stur            x3, [fp, #-0x18]
    // 0x7ca7a0: cmp             w2, NULL
    // 0x7ca7a4: b.eq            #0x7ca7c8
    // 0x7ca7a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca7a8: ldur            w4, [x2, #0x17]
    // 0x7ca7ac: DecompressPointer r4
    //     0x7ca7ac: add             x4, x4, HEAP, lsl #32
    // 0x7ca7b0: r8 = X0 bound PdfDataType
    //     0x7ca7b0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca7b4: ldr             x8, [x8, #0x6d8]
    // 0x7ca7b8: LoadField: r9 = r4->field_7
    //     0x7ca7b8: ldur            x9, [x4, #7]
    // 0x7ca7bc: r3 = Null
    //     0x7ca7bc: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b0c0] Null
    //     0x7ca7c0: ldr             x3, [x3, #0xc0]
    // 0x7ca7c4: blr             x9
    // 0x7ca7c8: ldur            x1, [fp, #-0x20]
    // 0x7ca7cc: ldur            x3, [fp, #-0x18]
    // 0x7ca7d0: r2 = "/Domain"
    //     0x7ca7d0: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0d0] "/Domain"
    //     0x7ca7d4: ldr             x2, [x2, #0xd0]
    // 0x7ca7d8: r0 = []=()
    //     0x7ca7d8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca7dc: r1 = const [0, 0x1, 0, 0x1, 0, 0x1]
    //     0x7ca7dc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a60] List<num>(6)
    //     0x7ca7e0: ldr             x1, [x1, #0xa60]
    // 0x7ca7e4: r0 = fromNum()
    //     0x7ca7e4: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca7e8: ldur            x2, [fp, #-0x10]
    // 0x7ca7ec: mov             x3, x0
    // 0x7ca7f0: r1 = Null
    //     0x7ca7f0: mov             x1, NULL
    // 0x7ca7f4: stur            x3, [fp, #-0x18]
    // 0x7ca7f8: cmp             w2, NULL
    // 0x7ca7fc: b.eq            #0x7ca820
    // 0x7ca800: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca800: ldur            w4, [x2, #0x17]
    // 0x7ca804: DecompressPointer r4
    //     0x7ca804: add             x4, x4, HEAP, lsl #32
    // 0x7ca808: r8 = X0 bound PdfDataType
    //     0x7ca808: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca80c: ldr             x8, [x8, #0x6d8]
    // 0x7ca810: LoadField: r9 = r4->field_7
    //     0x7ca810: ldur            x9, [x4, #7]
    // 0x7ca814: r3 = Null
    //     0x7ca814: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b0d8] Null
    //     0x7ca818: ldr             x3, [x3, #0xd8]
    // 0x7ca81c: blr             x9
    // 0x7ca820: ldur            x1, [fp, #-0x20]
    // 0x7ca824: ldur            x3, [fp, #-0x18]
    // 0x7ca828: r2 = "/Range"
    //     0x7ca828: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0e8] "/Range"
    //     0x7ca82c: ldr             x2, [x2, #0xe8]
    // 0x7ca830: r0 = []=()
    //     0x7ca830: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca834: ldur            x0, [fp, #-8]
    // 0x7ca838: LoadField: r1 = r0->field_b
    //     0x7ca838: ldur            w1, [x0, #0xb]
    // 0x7ca83c: r0 = LoadInt32Instr(r1)
    //     0x7ca83c: sbfx            x0, x1, #1, #0x1f
    // 0x7ca840: r1 = 3
    //     0x7ca840: movz            x1, #0x3
    // 0x7ca844: sdiv            x2, x0, x1
    // 0x7ca848: lsl             x0, x2, #1
    // 0x7ca84c: stur            x0, [fp, #-8]
    // 0x7ca850: r1 = Null
    //     0x7ca850: mov             x1, NULL
    // 0x7ca854: r2 = 2
    //     0x7ca854: movz            x2, #0x2
    // 0x7ca858: r0 = AllocateArray()
    //     0x7ca858: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ca85c: mov             x2, x0
    // 0x7ca860: ldur            x0, [fp, #-8]
    // 0x7ca864: stur            x2, [fp, #-0x18]
    // 0x7ca868: StoreField: r2->field_f = r0
    //     0x7ca868: stur            w0, [x2, #0xf]
    // 0x7ca86c: r1 = <int>
    //     0x7ca86c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7ca870: r0 = AllocateGrowableArray()
    //     0x7ca870: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7ca874: mov             x1, x0
    // 0x7ca878: ldur            x0, [fp, #-0x18]
    // 0x7ca87c: StoreField: r1->field_f = r0
    //     0x7ca87c: stur            w0, [x1, #0xf]
    // 0x7ca880: r0 = 2
    //     0x7ca880: movz            x0, #0x2
    // 0x7ca884: StoreField: r1->field_b = r0
    //     0x7ca884: stur            w0, [x1, #0xb]
    // 0x7ca888: r0 = fromNum()
    //     0x7ca888: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca88c: ldur            x2, [fp, #-0x10]
    // 0x7ca890: mov             x3, x0
    // 0x7ca894: r1 = Null
    //     0x7ca894: mov             x1, NULL
    // 0x7ca898: stur            x3, [fp, #-8]
    // 0x7ca89c: cmp             w2, NULL
    // 0x7ca8a0: b.eq            #0x7ca8c4
    // 0x7ca8a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca8a4: ldur            w4, [x2, #0x17]
    // 0x7ca8a8: DecompressPointer r4
    //     0x7ca8a8: add             x4, x4, HEAP, lsl #32
    // 0x7ca8ac: r8 = X0 bound PdfDataType
    //     0x7ca8ac: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca8b0: ldr             x8, [x8, #0x6d8]
    // 0x7ca8b4: LoadField: r9 = r4->field_7
    //     0x7ca8b4: ldur            x9, [x4, #7]
    // 0x7ca8b8: r3 = Null
    //     0x7ca8b8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b0f0] Null
    //     0x7ca8bc: ldr             x3, [x3, #0xf0]
    // 0x7ca8c0: blr             x9
    // 0x7ca8c4: ldur            x1, [fp, #-0x20]
    // 0x7ca8c8: ldur            x3, [fp, #-8]
    // 0x7ca8cc: r2 = "/Size"
    //     0x7ca8cc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36640] "/Size"
    //     0x7ca8d0: ldr             x2, [x2, #0x640]
    // 0x7ca8d4: r0 = []=()
    //     0x7ca8d4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca8d8: r0 = Null
    //     0x7ca8d8: mov             x0, NULL
    // 0x7ca8dc: LeaveFrame
    //     0x7ca8dc: mov             SP, fp
    //     0x7ca8e0: ldp             fp, lr, [SP], #0x10
    // 0x7ca8e4: ret
    //     0x7ca8e4: ret             
    // 0x7ca8e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca8e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca8ec: b               #0x7ca634
  }
  _ toString(/* No info */) {
    // ** addr: 0xc35600, size: 0xb0
    // 0xc35600: EnterFrame
    //     0xc35600: stp             fp, lr, [SP, #-0x10]!
    //     0xc35604: mov             fp, SP
    // 0xc35608: AllocStack(0x8)
    //     0xc35608: sub             SP, SP, #8
    // 0xc3560c: CheckStackOverflow
    //     0xc3560c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35610: cmp             SP, x16
    //     0xc35614: b.ls            #0xc356a8
    // 0xc35618: r1 = Null
    //     0xc35618: mov             x1, NULL
    // 0xc3561c: r2 = 14
    //     0xc3561c: movz            x2, #0xe
    // 0xc35620: r0 = AllocateArray()
    //     0xc35620: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35624: mov             x2, x0
    // 0xc35628: r16 = PdfFunction
    //     0xc35628: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b070] Type: PdfFunction
    //     0xc3562c: ldr             x16, [x16, #0x70]
    // 0xc35630: StoreField: r2->field_f = r16
    //     0xc35630: stur            w16, [x2, #0xf]
    // 0xc35634: r16 = " "
    //     0xc35634: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35638: StoreField: r2->field_13 = r16
    //     0xc35638: stur            w16, [x2, #0x13]
    // 0xc3563c: ldr             x3, [fp, #0x10]
    // 0xc35640: LoadField: r4 = r3->field_37
    //     0xc35640: ldur            x4, [x3, #0x37]
    // 0xc35644: r0 = BoxInt64Instr(r4)
    //     0xc35644: sbfiz           x0, x4, #1, #0x1f
    //     0xc35648: cmp             x4, x0, asr #1
    //     0xc3564c: b.eq            #0xc35658
    //     0xc35650: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35654: stur            x4, [x0, #7]
    // 0xc35658: ArrayStore: r2[0] = r0  ; List_4
    //     0xc35658: stur            w0, [x2, #0x17]
    // 0xc3565c: r16 = " "
    //     0xc3565c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35660: StoreField: r2->field_1b = r16
    //     0xc35660: stur            w16, [x2, #0x1b]
    // 0xc35664: LoadField: r4 = r3->field_3f
    //     0xc35664: ldur            x4, [x3, #0x3f]
    // 0xc35668: r0 = BoxInt64Instr(r4)
    //     0xc35668: sbfiz           x0, x4, #1, #0x1f
    //     0xc3566c: cmp             x4, x0, asr #1
    //     0xc35670: b.eq            #0xc3567c
    //     0xc35674: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35678: stur            x4, [x0, #7]
    // 0xc3567c: StoreField: r2->field_1f = r0
    //     0xc3567c: stur            w0, [x2, #0x1f]
    // 0xc35680: r16 = " "
    //     0xc35680: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35684: StoreField: r2->field_23 = r16
    //     0xc35684: stur            w16, [x2, #0x23]
    // 0xc35688: LoadField: r0 = r3->field_33
    //     0xc35688: ldur            w0, [x3, #0x33]
    // 0xc3568c: DecompressPointer r0
    //     0xc3568c: add             x0, x0, HEAP, lsl #32
    // 0xc35690: StoreField: r2->field_27 = r0
    //     0xc35690: stur            w0, [x2, #0x27]
    // 0xc35694: str             x2, [SP]
    // 0xc35698: r0 = _interpolate()
    //     0xc35698: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3569c: LeaveFrame
    //     0xc3569c: mov             SP, fp
    //     0xc356a0: ldp             fp, lr, [SP], #0x10
    // 0xc356a4: ret
    //     0xc356a4: ret             
    // 0xc356a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc356a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc356ac: b               #0xc35618
  }
  factory _ PdfFunction.fromColors(/* No info */) {
    // ** addr: 0xeaac10, size: 0x42c
    // 0xeaac10: EnterFrame
    //     0xeaac10: stp             fp, lr, [SP, #-0x10]!
    //     0xeaac14: mov             fp, SP
    // 0xeaac18: AllocStack(0x58)
    //     0xeaac18: sub             SP, SP, #0x58
    // 0xeaac1c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xeaac1c: mov             x0, x2
    //     0xeaac20: stur            x2, [fp, #-8]
    //     0xeaac24: stur            x3, [fp, #-0x10]
    // 0xeaac28: CheckStackOverflow
    //     0xeaac28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaac2c: cmp             SP, x16
    //     0xeaac30: b.ls            #0xeaafd4
    // 0xeaac34: r1 = <int>
    //     0xeaac34: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeaac38: r2 = 0
    //     0xeaac38: movz            x2, #0
    // 0xeaac3c: r0 = _GrowableList()
    //     0xeaac3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeaac40: mov             x4, x0
    // 0xeaac44: ldur            x3, [fp, #-0x10]
    // 0xeaac48: stur            x4, [fp, #-0x38]
    // 0xeaac4c: LoadField: r5 = r3->field_7
    //     0xeaac4c: ldur            w5, [x3, #7]
    // 0xeaac50: DecompressPointer r5
    //     0xeaac50: add             x5, x5, HEAP, lsl #32
    // 0xeaac54: stur            x5, [fp, #-0x30]
    // 0xeaac58: LoadField: r0 = r3->field_b
    //     0xeaac58: ldur            w0, [x3, #0xb]
    // 0xeaac5c: r6 = LoadInt32Instr(r0)
    //     0xeaac5c: sbfx            x6, x0, #1, #0x1f
    // 0xeaac60: stur            x6, [fp, #-0x28]
    // 0xeaac64: r0 = 0
    //     0xeaac64: movz            x0, #0
    // 0xeaac68: CheckStackOverflow
    //     0xeaac68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaac6c: cmp             SP, x16
    //     0xeaac70: b.ls            #0xeaafdc
    // 0xeaac74: LoadField: r1 = r3->field_b
    //     0xeaac74: ldur            w1, [x3, #0xb]
    // 0xeaac78: r2 = LoadInt32Instr(r1)
    //     0xeaac78: sbfx            x2, x1, #1, #0x1f
    // 0xeaac7c: cmp             x6, x2
    // 0xeaac80: b.ne            #0xeaafb4
    // 0xeaac84: cmp             x0, x2
    // 0xeaac88: b.ge            #0xeaaf80
    // 0xeaac8c: LoadField: r1 = r3->field_f
    //     0xeaac8c: ldur            w1, [x3, #0xf]
    // 0xeaac90: DecompressPointer r1
    //     0xeaac90: add             x1, x1, HEAP, lsl #32
    // 0xeaac94: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0xeaac94: add             x16, x1, x0, lsl #2
    //     0xeaac98: ldur            w7, [x16, #0xf]
    // 0xeaac9c: DecompressPointer r7
    //     0xeaac9c: add             x7, x7, HEAP, lsl #32
    // 0xeaaca0: stur            x7, [fp, #-0x20]
    // 0xeaaca4: add             x8, x0, #1
    // 0xeaaca8: stur            x8, [fp, #-0x18]
    // 0xeaacac: cmp             w7, NULL
    // 0xeaacb0: b.ne            #0xeaace4
    // 0xeaacb4: mov             x0, x7
    // 0xeaacb8: mov             x2, x5
    // 0xeaacbc: r1 = Null
    //     0xeaacbc: mov             x1, NULL
    // 0xeaacc0: cmp             w2, NULL
    // 0xeaacc4: b.eq            #0xeaace4
    // 0xeaacc8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeaacc8: ldur            w4, [x2, #0x17]
    // 0xeaaccc: DecompressPointer r4
    //     0xeaaccc: add             x4, x4, HEAP, lsl #32
    // 0xeaacd0: r8 = X0
    //     0xeaacd0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeaacd4: LoadField: r9 = r4->field_7
    //     0xeaacd4: ldur            x9, [x4, #7]
    // 0xeaacd8: r3 = Null
    //     0xeaacd8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a48] Null
    //     0xeaacdc: ldr             x3, [x3, #0xa48]
    // 0xeaace0: blr             x9
    // 0xeaace4: ldur            x1, [fp, #-0x38]
    // 0xeaace8: ldur            x0, [fp, #-0x20]
    // 0xeaacec: d1 = 255.000000
    //     0xeaacec: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xeaacf0: cmp             w0, NULL
    // 0xeaacf4: b.eq            #0xeaafe4
    // 0xeaacf8: LoadField: d0 = r0->field_f
    //     0xeaacf8: ldur            d0, [x0, #0xf]
    // 0xeaacfc: fmul            d2, d0, d1
    // 0xeaad00: mov             v0.16b, v2.16b
    // 0xeaad04: stp             fp, lr, [SP, #-0x10]!
    // 0xeaad08: mov             fp, SP
    // 0xeaad0c: CallRuntime_LibcRound(double) -> double
    //     0xeaad0c: and             SP, SP, #0xfffffffffffffff0
    //     0xeaad10: mov             sp, SP
    //     0xeaad14: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xeaad18: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaad1c: blr             x16
    //     0xeaad20: movz            x16, #0x8
    //     0xeaad24: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaad28: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xeaad2c: sub             sp, x16, #1, lsl #12
    //     0xeaad30: mov             SP, fp
    //     0xeaad34: ldp             fp, lr, [SP], #0x10
    // 0xeaad38: fcmp            d0, d0
    // 0xeaad3c: b.vs            #0xeaafe8
    // 0xeaad40: fcvtzs          x0, d0
    // 0xeaad44: asr             x16, x0, #0x1e
    // 0xeaad48: cmp             x16, x0, asr #63
    // 0xeaad4c: b.ne            #0xeaafe8
    // 0xeaad50: lsl             x0, x0, #1
    // 0xeaad54: r1 = LoadInt32Instr(r0)
    //     0xeaad54: sbfx            x1, x0, #1, #0x1f
    //     0xeaad58: tbz             w0, #0, #0xeaad60
    //     0xeaad5c: ldur            x1, [x0, #7]
    // 0xeaad60: r0 = 255
    //     0xeaad60: movz            x0, #0xff
    // 0xeaad64: and             x2, x1, x0
    // 0xeaad68: ldur            x3, [fp, #-0x38]
    // 0xeaad6c: stur            x2, [fp, #-0x48]
    // 0xeaad70: LoadField: r1 = r3->field_b
    //     0xeaad70: ldur            w1, [x3, #0xb]
    // 0xeaad74: LoadField: r4 = r3->field_f
    //     0xeaad74: ldur            w4, [x3, #0xf]
    // 0xeaad78: DecompressPointer r4
    //     0xeaad78: add             x4, x4, HEAP, lsl #32
    // 0xeaad7c: LoadField: r5 = r4->field_b
    //     0xeaad7c: ldur            w5, [x4, #0xb]
    // 0xeaad80: r4 = LoadInt32Instr(r1)
    //     0xeaad80: sbfx            x4, x1, #1, #0x1f
    // 0xeaad84: stur            x4, [fp, #-0x40]
    // 0xeaad88: r1 = LoadInt32Instr(r5)
    //     0xeaad88: sbfx            x1, x5, #1, #0x1f
    // 0xeaad8c: cmp             x4, x1
    // 0xeaad90: b.ne            #0xeaad9c
    // 0xeaad94: mov             x1, x3
    // 0xeaad98: r0 = _growToNextCapacity()
    //     0xeaad98: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaad9c: ldur            x1, [fp, #-0x38]
    // 0xeaada0: ldur            x0, [fp, #-0x48]
    // 0xeaada4: ldur            x2, [fp, #-0x40]
    // 0xeaada8: ldur            x3, [fp, #-0x20]
    // 0xeaadac: d1 = 255.000000
    //     0xeaadac: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xeaadb0: add             x4, x2, #1
    // 0xeaadb4: stur            x4, [fp, #-0x58]
    // 0xeaadb8: lsl             x5, x4, #1
    // 0xeaadbc: StoreField: r1->field_b = r5
    //     0xeaadbc: stur            w5, [x1, #0xb]
    // 0xeaadc0: LoadField: r5 = r1->field_f
    //     0xeaadc0: ldur            w5, [x1, #0xf]
    // 0xeaadc4: DecompressPointer r5
    //     0xeaadc4: add             x5, x5, HEAP, lsl #32
    // 0xeaadc8: stur            x5, [fp, #-0x50]
    // 0xeaadcc: lsl             w6, w0, #1
    // 0xeaadd0: ArrayStore: r5[r2] = r6  ; Unknown_4
    //     0xeaadd0: add             x0, x5, x2, lsl #2
    //     0xeaadd4: stur            w6, [x0, #0xf]
    // 0xeaadd8: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xeaadd8: ldur            d0, [x3, #0x17]
    // 0xeaaddc: fmul            d2, d0, d1
    // 0xeaade0: mov             v0.16b, v2.16b
    // 0xeaade4: stp             fp, lr, [SP, #-0x10]!
    // 0xeaade8: mov             fp, SP
    // 0xeaadec: CallRuntime_LibcRound(double) -> double
    //     0xeaadec: and             SP, SP, #0xfffffffffffffff0
    //     0xeaadf0: mov             sp, SP
    //     0xeaadf4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xeaadf8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaadfc: blr             x16
    //     0xeaae00: movz            x16, #0x8
    //     0xeaae04: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaae08: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xeaae0c: sub             sp, x16, #1, lsl #12
    //     0xeaae10: mov             SP, fp
    //     0xeaae14: ldp             fp, lr, [SP], #0x10
    // 0xeaae18: fcmp            d0, d0
    // 0xeaae1c: b.vs            #0xeab004
    // 0xeaae20: fcvtzs          x0, d0
    // 0xeaae24: asr             x16, x0, #0x1e
    // 0xeaae28: cmp             x16, x0, asr #63
    // 0xeaae2c: b.ne            #0xeab004
    // 0xeaae30: lsl             x0, x0, #1
    // 0xeaae34: r1 = LoadInt32Instr(r0)
    //     0xeaae34: sbfx            x1, x0, #1, #0x1f
    //     0xeaae38: tbz             w0, #0, #0xeaae40
    //     0xeaae3c: ldur            x1, [x0, #7]
    // 0xeaae40: r0 = 255
    //     0xeaae40: movz            x0, #0xff
    // 0xeaae44: and             x2, x1, x0
    // 0xeaae48: ldur            x1, [fp, #-0x50]
    // 0xeaae4c: stur            x2, [fp, #-0x40]
    // 0xeaae50: LoadField: r3 = r1->field_b
    //     0xeaae50: ldur            w3, [x1, #0xb]
    // 0xeaae54: r1 = LoadInt32Instr(r3)
    //     0xeaae54: sbfx            x1, x3, #1, #0x1f
    // 0xeaae58: ldur            x3, [fp, #-0x58]
    // 0xeaae5c: cmp             x3, x1
    // 0xeaae60: b.ne            #0xeaae6c
    // 0xeaae64: ldur            x1, [fp, #-0x38]
    // 0xeaae68: r0 = _growToNextCapacity()
    //     0xeaae68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaae6c: ldur            x2, [fp, #-0x38]
    // 0xeaae70: ldur            x0, [fp, #-0x40]
    // 0xeaae74: ldur            x1, [fp, #-0x58]
    // 0xeaae78: ldur            x3, [fp, #-0x20]
    // 0xeaae7c: d1 = 255.000000
    //     0xeaae7c: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xeaae80: add             x4, x1, #1
    // 0xeaae84: stur            x4, [fp, #-0x48]
    // 0xeaae88: lsl             x5, x4, #1
    // 0xeaae8c: StoreField: r2->field_b = r5
    //     0xeaae8c: stur            w5, [x2, #0xb]
    // 0xeaae90: LoadField: r5 = r2->field_f
    //     0xeaae90: ldur            w5, [x2, #0xf]
    // 0xeaae94: DecompressPointer r5
    //     0xeaae94: add             x5, x5, HEAP, lsl #32
    // 0xeaae98: stur            x5, [fp, #-0x50]
    // 0xeaae9c: lsl             w6, w0, #1
    // 0xeaaea0: ArrayStore: r5[r1] = r6  ; Unknown_4
    //     0xeaaea0: add             x0, x5, x1, lsl #2
    //     0xeaaea4: stur            w6, [x0, #0xf]
    // 0xeaaea8: LoadField: d0 = r3->field_1f
    //     0xeaaea8: ldur            d0, [x3, #0x1f]
    // 0xeaaeac: fmul            d2, d0, d1
    // 0xeaaeb0: mov             v0.16b, v2.16b
    // 0xeaaeb4: stp             fp, lr, [SP, #-0x10]!
    // 0xeaaeb8: mov             fp, SP
    // 0xeaaebc: CallRuntime_LibcRound(double) -> double
    //     0xeaaebc: and             SP, SP, #0xfffffffffffffff0
    //     0xeaaec0: mov             sp, SP
    //     0xeaaec4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xeaaec8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaaecc: blr             x16
    //     0xeaaed0: movz            x16, #0x8
    //     0xeaaed4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaaed8: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xeaaedc: sub             sp, x16, #1, lsl #12
    //     0xeaaee0: mov             SP, fp
    //     0xeaaee4: ldp             fp, lr, [SP], #0x10
    // 0xeaaee8: fcmp            d0, d0
    // 0xeaaeec: b.vs            #0xeab020
    // 0xeaaef0: fcvtzs          x0, d0
    // 0xeaaef4: asr             x16, x0, #0x1e
    // 0xeaaef8: cmp             x16, x0, asr #63
    // 0xeaaefc: b.ne            #0xeab020
    // 0xeaaf00: lsl             x0, x0, #1
    // 0xeaaf04: r1 = LoadInt32Instr(r0)
    //     0xeaaf04: sbfx            x1, x0, #1, #0x1f
    //     0xeaaf08: tbz             w0, #0, #0xeaaf10
    //     0xeaaf0c: ldur            x1, [x0, #7]
    // 0xeaaf10: r0 = 255
    //     0xeaaf10: movz            x0, #0xff
    // 0xeaaf14: and             x2, x1, x0
    // 0xeaaf18: ldur            x1, [fp, #-0x50]
    // 0xeaaf1c: stur            x2, [fp, #-0x40]
    // 0xeaaf20: LoadField: r3 = r1->field_b
    //     0xeaaf20: ldur            w3, [x1, #0xb]
    // 0xeaaf24: r1 = LoadInt32Instr(r3)
    //     0xeaaf24: sbfx            x1, x3, #1, #0x1f
    // 0xeaaf28: ldur            x3, [fp, #-0x48]
    // 0xeaaf2c: cmp             x3, x1
    // 0xeaaf30: b.ne            #0xeaaf3c
    // 0xeaaf34: ldur            x1, [fp, #-0x38]
    // 0xeaaf38: r0 = _growToNextCapacity()
    //     0xeaaf38: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaaf3c: ldur            x3, [fp, #-0x38]
    // 0xeaaf40: ldur            x0, [fp, #-0x40]
    // 0xeaaf44: ldur            x1, [fp, #-0x48]
    // 0xeaaf48: add             x2, x1, #1
    // 0xeaaf4c: lsl             x4, x2, #1
    // 0xeaaf50: StoreField: r3->field_b = r4
    //     0xeaaf50: stur            w4, [x3, #0xb]
    // 0xeaaf54: LoadField: r2 = r3->field_f
    //     0xeaaf54: ldur            w2, [x3, #0xf]
    // 0xeaaf58: DecompressPointer r2
    //     0xeaaf58: add             x2, x2, HEAP, lsl #32
    // 0xeaaf5c: lsl             w4, w0, #1
    // 0xeaaf60: ArrayStore: r2[r1] = r4  ; Unknown_4
    //     0xeaaf60: add             x0, x2, x1, lsl #2
    //     0xeaaf64: stur            w4, [x0, #0xf]
    // 0xeaaf68: ldur            x0, [fp, #-0x18]
    // 0xeaaf6c: mov             x4, x3
    // 0xeaaf70: ldur            x3, [fp, #-0x10]
    // 0xeaaf74: ldur            x5, [fp, #-0x30]
    // 0xeaaf78: ldur            x6, [fp, #-0x28]
    // 0xeaaf7c: b               #0xeaac68
    // 0xeaaf80: mov             x3, x4
    // 0xeaaf84: r1 = <PdfDict<PdfDataType>>
    //     0xeaaf84: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaaf88: ldr             x1, [x1, #0x758]
    // 0xeaaf8c: r0 = PdfFunction()
    //     0xeaaf8c: bl              #0xeab0b8  ; AllocatePdfFunctionStub -> PdfFunction (size=0x50)
    // 0xeaaf90: mov             x1, x0
    // 0xeaaf94: ldur            x2, [fp, #-8]
    // 0xeaaf98: ldur            x3, [fp, #-0x38]
    // 0xeaaf9c: stur            x0, [fp, #-8]
    // 0xeaafa0: r0 = PdfFunction()
    //     0xeaafa0: bl              #0xeab03c  ; [package:pdf/src/pdf/obj/function.dart] PdfFunction::PdfFunction
    // 0xeaafa4: ldur            x0, [fp, #-8]
    // 0xeaafa8: LeaveFrame
    //     0xeaafa8: mov             SP, fp
    //     0xeaafac: ldp             fp, lr, [SP], #0x10
    // 0xeaafb0: ret
    //     0xeaafb0: ret             
    // 0xeaafb4: mov             x0, x3
    // 0xeaafb8: r0 = ConcurrentModificationError()
    //     0xeaafb8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xeaafbc: mov             x1, x0
    // 0xeaafc0: ldur            x0, [fp, #-0x10]
    // 0xeaafc4: StoreField: r1->field_b = r0
    //     0xeaafc4: stur            w0, [x1, #0xb]
    // 0xeaafc8: mov             x0, x1
    // 0xeaafcc: r0 = Throw()
    //     0xeaafcc: bl              #0xec04b8  ; ThrowStub
    // 0xeaafd0: brk             #0
    // 0xeaafd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaafd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaafd8: b               #0xeaac34
    // 0xeaafdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaafdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaafe0: b               #0xeaac74
    // 0xeaafe4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xeaafe4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xeaafe8: SaveReg d0
    //     0xeaafe8: str             q0, [SP, #-0x10]!
    // 0xeaafec: r0 = 74
    //     0xeaafec: movz            x0, #0x4a
    // 0xeaaff0: r30 = DoubleToIntegerStub
    //     0xeaaff0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xeaaff4: LoadField: r30 = r30->field_7
    //     0xeaaff4: ldur            lr, [lr, #7]
    // 0xeaaff8: blr             lr
    // 0xeaaffc: RestoreReg d0
    //     0xeaaffc: ldr             q0, [SP], #0x10
    // 0xeab000: b               #0xeaad54
    // 0xeab004: SaveReg d0
    //     0xeab004: str             q0, [SP, #-0x10]!
    // 0xeab008: r0 = 74
    //     0xeab008: movz            x0, #0x4a
    // 0xeab00c: r30 = DoubleToIntegerStub
    //     0xeab00c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xeab010: LoadField: r30 = r30->field_7
    //     0xeab010: ldur            lr, [lr, #7]
    // 0xeab014: blr             lr
    // 0xeab018: RestoreReg d0
    //     0xeab018: ldr             q0, [SP], #0x10
    // 0xeab01c: b               #0xeaae34
    // 0xeab020: SaveReg d0
    //     0xeab020: str             q0, [SP, #-0x10]!
    // 0xeab024: r0 = 74
    //     0xeab024: movz            x0, #0x4a
    // 0xeab028: r30 = DoubleToIntegerStub
    //     0xeab028: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xeab02c: LoadField: r30 = r30->field_7
    //     0xeab02c: ldur            lr, [lr, #7]
    // 0xeab030: blr             lr
    // 0xeab034: RestoreReg d0
    //     0xeab034: ldr             q0, [SP], #0x10
    // 0xeab038: b               #0xeaaf04
  }
  _ PdfFunction(/* No info */) {
    // ** addr: 0xeab03c, size: 0x7c
    // 0xeab03c: EnterFrame
    //     0xeab03c: stp             fp, lr, [SP, #-0x10]!
    //     0xeab040: mov             fp, SP
    // 0xeab044: r7 = const [0, 0x1]
    //     0xeab044: add             x7, PP, #0x57, lsl #12  ; [pp+0x57a58] List<num>(2)
    //     0xeab048: ldr             x7, [x7, #0xa58]
    // 0xeab04c: r6 = const [0, 0x1, 0, 0x1, 0, 0x1]
    //     0xeab04c: add             x6, PP, #0x57, lsl #12  ; [pp+0x57a60] List<num>(6)
    //     0xeab050: ldr             x6, [x6, #0xa60]
    // 0xeab054: r5 = 8
    //     0xeab054: movz            x5, #0x8
    // 0xeab058: r4 = 3
    //     0xeab058: movz            x4, #0x3
    // 0xeab05c: mov             x0, x3
    // 0xeab060: CheckStackOverflow
    //     0xeab060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab064: cmp             SP, x16
    //     0xeab068: b.ls            #0xeab0b0
    // 0xeab06c: StoreField: r1->field_33 = r0
    //     0xeab06c: stur            w0, [x1, #0x33]
    //     0xeab070: ldurb           w16, [x1, #-1]
    //     0xeab074: ldurb           w17, [x0, #-1]
    //     0xeab078: and             x16, x17, x16, lsr #2
    //     0xeab07c: tst             x16, HEAP, lsr #32
    //     0xeab080: b.eq            #0xeab088
    //     0xeab084: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeab088: StoreField: r1->field_37 = r5
    //     0xeab088: stur            x5, [x1, #0x37]
    // 0xeab08c: StoreField: r1->field_3f = r4
    //     0xeab08c: stur            x4, [x1, #0x3f]
    // 0xeab090: StoreField: r1->field_47 = r7
    //     0xeab090: stur            w7, [x1, #0x47]
    // 0xeab094: StoreField: r1->field_4b = r6
    //     0xeab094: stur            w6, [x1, #0x4b]
    // 0xeab098: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeab098: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeab09c: r0 = PdfObjectStream()
    //     0xeab09c: bl              #0xe4714c  ; [package:pdf/src/pdf/obj/object_stream.dart] PdfObjectStream::PdfObjectStream
    // 0xeab0a0: r0 = Null
    //     0xeab0a0: mov             x0, NULL
    // 0xeab0a4: LeaveFrame
    //     0xeab0a4: mov             SP, fp
    //     0xeab0a8: ldp             fp, lr, [SP], #0x10
    // 0xeab0ac: ret
    //     0xeab0ac: ret             
    // 0xeab0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeab0b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeab0b4: b               #0xeab06c
  }
}

// class id: 891, size: 0x2c, field offset: 0x2c
abstract class PdfBaseFunction extends PdfObject<dynamic> {

  factory _ PdfBaseFunction.colorsAndStops(/* No info */) {
    // ** addr: 0xeaa694, size: 0x508
    // 0xeaa694: EnterFrame
    //     0xeaa694: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa698: mov             fp, SP
    // 0xeaa69c: AllocStack(0x60)
    //     0xeaa69c: sub             SP, SP, #0x60
    // 0xeaa6a0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r5 => r0, fp-0x10 */)
    //     0xeaa6a0: mov             x4, x2
    //     0xeaa6a4: mov             x0, x5
    //     0xeaa6a8: stur            x2, [fp, #-8]
    //     0xeaa6ac: stur            x5, [fp, #-0x10]
    // 0xeaa6b0: CheckStackOverflow
    //     0xeaa6b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa6b4: cmp             SP, x16
    //     0xeaa6b8: b.ls            #0xeaab88
    // 0xeaa6bc: LoadField: r1 = r0->field_b
    //     0xeaa6bc: ldur            w1, [x0, #0xb]
    // 0xeaa6c0: cbnz            w1, #0xeaa6e0
    // 0xeaa6c4: mov             x2, x4
    // 0xeaa6c8: r1 = <PdfDict<PdfDataType>>
    //     0xeaa6c8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaa6cc: ldr             x1, [x1, #0x758]
    // 0xeaa6d0: r0 = PdfFunction.fromColors()
    //     0xeaa6d0: bl              #0xeaac10  ; [package:pdf/src/pdf/obj/function.dart] PdfFunction::PdfFunction.fromColors
    // 0xeaa6d4: LeaveFrame
    //     0xeaa6d4: mov             SP, fp
    //     0xeaa6d8: ldp             fp, lr, [SP], #0x10
    // 0xeaa6dc: ret
    //     0xeaa6dc: ret             
    // 0xeaa6e0: mov             x2, x3
    // 0xeaa6e4: r1 = <PdfColor>
    //     0xeaa6e4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51260] TypeArguments: <PdfColor>
    //     0xeaa6e8: ldr             x1, [x1, #0x260]
    // 0xeaa6ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeaa6ec: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeaa6f0: r0 = List.from()
    //     0xeaa6f0: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xeaa6f4: ldur            x2, [fp, #-0x10]
    // 0xeaa6f8: r1 = <double>
    //     0xeaa6f8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xeaa6fc: stur            x0, [fp, #-0x10]
    // 0xeaa700: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeaa700: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeaa704: r0 = List.from()
    //     0xeaa704: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xeaa708: r1 = <PdfFunction>
    //     0xeaa708: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a08] TypeArguments: <PdfFunction>
    //     0xeaa70c: ldr             x1, [x1, #0xa08]
    // 0xeaa710: r2 = 0
    //     0xeaa710: movz            x2, #0
    // 0xeaa714: stur            x0, [fp, #-0x18]
    // 0xeaa718: r0 = _GrowableList()
    //     0xeaa718: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeaa71c: ldur            x1, [fp, #-0x10]
    // 0xeaa720: stur            x0, [fp, #-0x20]
    // 0xeaa724: r0 = first()
    //     0xeaa724: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xeaa728: mov             x5, x0
    // 0xeaa72c: ldur            x4, [fp, #-0x18]
    // 0xeaa730: stur            x5, [fp, #-0x28]
    // 0xeaa734: LoadField: r0 = r4->field_b
    //     0xeaa734: ldur            w0, [x4, #0xb]
    // 0xeaa738: r1 = LoadInt32Instr(r0)
    //     0xeaa738: sbfx            x1, x0, #1, #0x1f
    // 0xeaa73c: mov             x0, x1
    // 0xeaa740: r1 = 0
    //     0xeaa740: movz            x1, #0
    // 0xeaa744: cmp             x1, x0
    // 0xeaa748: b.hs            #0xeaab90
    // 0xeaa74c: LoadField: r0 = r4->field_f
    //     0xeaa74c: ldur            w0, [x4, #0xf]
    // 0xeaa750: DecompressPointer r0
    //     0xeaa750: add             x0, x0, HEAP, lsl #32
    // 0xeaa754: LoadField: r1 = r0->field_f
    //     0xeaa754: ldur            w1, [x0, #0xf]
    // 0xeaa758: DecompressPointer r1
    //     0xeaa758: add             x1, x1, HEAP, lsl #32
    // 0xeaa75c: LoadField: d0 = r1->field_7
    //     0xeaa75c: ldur            d0, [x1, #7]
    // 0xeaa760: d1 = 0.000000
    //     0xeaa760: eor             v1.16b, v1.16b, v1.16b
    // 0xeaa764: fcmp            d0, d1
    // 0xeaa768: b.le            #0xeaa78c
    // 0xeaa76c: ldur            x1, [fp, #-0x10]
    // 0xeaa770: mov             x3, x5
    // 0xeaa774: r2 = 0
    //     0xeaa774: movz            x2, #0
    // 0xeaa778: r0 = insert()
    //     0xeaa778: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xeaa77c: ldur            x1, [fp, #-0x18]
    // 0xeaa780: r2 = 0
    //     0xeaa780: movz            x2, #0
    // 0xeaa784: r3 = 0.000000
    //     0xeaa784: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xeaa788: r0 = insert()
    //     0xeaa788: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xeaa78c: ldur            x1, [fp, #-0x18]
    // 0xeaa790: r0 = last()
    //     0xeaa790: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xeaa794: LoadField: d0 = r0->field_7
    //     0xeaa794: ldur            d0, [x0, #7]
    // 0xeaa798: d1 = 1.000000
    //     0xeaa798: fmov            d1, #1.00000000
    // 0xeaa79c: fcmp            d1, d0
    // 0xeaa7a0: b.le            #0xeaa8fc
    // 0xeaa7a4: ldur            x0, [fp, #-0x10]
    // 0xeaa7a8: mov             x1, x0
    // 0xeaa7ac: r0 = last()
    //     0xeaa7ac: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xeaa7b0: mov             x4, x0
    // 0xeaa7b4: ldur            x3, [fp, #-0x10]
    // 0xeaa7b8: stur            x4, [fp, #-0x30]
    // 0xeaa7bc: LoadField: r2 = r3->field_7
    //     0xeaa7bc: ldur            w2, [x3, #7]
    // 0xeaa7c0: DecompressPointer r2
    //     0xeaa7c0: add             x2, x2, HEAP, lsl #32
    // 0xeaa7c4: mov             x0, x4
    // 0xeaa7c8: r1 = Null
    //     0xeaa7c8: mov             x1, NULL
    // 0xeaa7cc: cmp             w2, NULL
    // 0xeaa7d0: b.eq            #0xeaa7f0
    // 0xeaa7d4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeaa7d4: ldur            w4, [x2, #0x17]
    // 0xeaa7d8: DecompressPointer r4
    //     0xeaa7d8: add             x4, x4, HEAP, lsl #32
    // 0xeaa7dc: r8 = X0
    //     0xeaa7dc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeaa7e0: LoadField: r9 = r4->field_7
    //     0xeaa7e0: ldur            x9, [x4, #7]
    // 0xeaa7e4: r3 = Null
    //     0xeaa7e4: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a10] Null
    //     0xeaa7e8: ldr             x3, [x3, #0xa10]
    // 0xeaa7ec: blr             x9
    // 0xeaa7f0: ldur            x0, [fp, #-0x10]
    // 0xeaa7f4: LoadField: r1 = r0->field_b
    //     0xeaa7f4: ldur            w1, [x0, #0xb]
    // 0xeaa7f8: LoadField: r2 = r0->field_f
    //     0xeaa7f8: ldur            w2, [x0, #0xf]
    // 0xeaa7fc: DecompressPointer r2
    //     0xeaa7fc: add             x2, x2, HEAP, lsl #32
    // 0xeaa800: LoadField: r3 = r2->field_b
    //     0xeaa800: ldur            w3, [x2, #0xb]
    // 0xeaa804: r2 = LoadInt32Instr(r1)
    //     0xeaa804: sbfx            x2, x1, #1, #0x1f
    // 0xeaa808: stur            x2, [fp, #-0x38]
    // 0xeaa80c: r1 = LoadInt32Instr(r3)
    //     0xeaa80c: sbfx            x1, x3, #1, #0x1f
    // 0xeaa810: cmp             x2, x1
    // 0xeaa814: b.ne            #0xeaa820
    // 0xeaa818: mov             x1, x0
    // 0xeaa81c: r0 = _growToNextCapacity()
    //     0xeaa81c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaa820: ldur            x3, [fp, #-0x10]
    // 0xeaa824: ldur            x4, [fp, #-0x18]
    // 0xeaa828: ldur            x2, [fp, #-0x38]
    // 0xeaa82c: add             x0, x2, #1
    // 0xeaa830: lsl             x1, x0, #1
    // 0xeaa834: StoreField: r3->field_b = r1
    //     0xeaa834: stur            w1, [x3, #0xb]
    // 0xeaa838: LoadField: r1 = r3->field_f
    //     0xeaa838: ldur            w1, [x3, #0xf]
    // 0xeaa83c: DecompressPointer r1
    //     0xeaa83c: add             x1, x1, HEAP, lsl #32
    // 0xeaa840: ldur            x0, [fp, #-0x30]
    // 0xeaa844: ArrayStore: r1[r2] = r0  ; List_4
    //     0xeaa844: add             x25, x1, x2, lsl #2
    //     0xeaa848: add             x25, x25, #0xf
    //     0xeaa84c: str             w0, [x25]
    //     0xeaa850: tbz             w0, #0, #0xeaa86c
    //     0xeaa854: ldurb           w16, [x1, #-1]
    //     0xeaa858: ldurb           w17, [x0, #-1]
    //     0xeaa85c: and             x16, x17, x16, lsr #2
    //     0xeaa860: tst             x16, HEAP, lsr #32
    //     0xeaa864: b.eq            #0xeaa86c
    //     0xeaa868: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeaa86c: LoadField: r2 = r4->field_7
    //     0xeaa86c: ldur            w2, [x4, #7]
    // 0xeaa870: DecompressPointer r2
    //     0xeaa870: add             x2, x2, HEAP, lsl #32
    // 0xeaa874: r0 = 1.000000
    //     0xeaa874: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xeaa878: r1 = Null
    //     0xeaa878: mov             x1, NULL
    // 0xeaa87c: cmp             w2, NULL
    // 0xeaa880: b.eq            #0xeaa8a0
    // 0xeaa884: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeaa884: ldur            w4, [x2, #0x17]
    // 0xeaa888: DecompressPointer r4
    //     0xeaa888: add             x4, x4, HEAP, lsl #32
    // 0xeaa88c: r8 = X0
    //     0xeaa88c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeaa890: LoadField: r9 = r4->field_7
    //     0xeaa890: ldur            x9, [x4, #7]
    // 0xeaa894: r3 = Null
    //     0xeaa894: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a20] Null
    //     0xeaa898: ldr             x3, [x3, #0xa20]
    // 0xeaa89c: blr             x9
    // 0xeaa8a0: ldur            x0, [fp, #-0x18]
    // 0xeaa8a4: LoadField: r1 = r0->field_b
    //     0xeaa8a4: ldur            w1, [x0, #0xb]
    // 0xeaa8a8: LoadField: r2 = r0->field_f
    //     0xeaa8a8: ldur            w2, [x0, #0xf]
    // 0xeaa8ac: DecompressPointer r2
    //     0xeaa8ac: add             x2, x2, HEAP, lsl #32
    // 0xeaa8b0: LoadField: r3 = r2->field_b
    //     0xeaa8b0: ldur            w3, [x2, #0xb]
    // 0xeaa8b4: r2 = LoadInt32Instr(r1)
    //     0xeaa8b4: sbfx            x2, x1, #1, #0x1f
    // 0xeaa8b8: stur            x2, [fp, #-0x38]
    // 0xeaa8bc: r1 = LoadInt32Instr(r3)
    //     0xeaa8bc: sbfx            x1, x3, #1, #0x1f
    // 0xeaa8c0: cmp             x2, x1
    // 0xeaa8c4: b.ne            #0xeaa8d0
    // 0xeaa8c8: mov             x1, x0
    // 0xeaa8cc: r0 = _growToNextCapacity()
    //     0xeaa8cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaa8d0: ldur            x0, [fp, #-0x18]
    // 0xeaa8d4: ldur            x1, [fp, #-0x38]
    // 0xeaa8d8: add             x2, x1, #1
    // 0xeaa8dc: lsl             x3, x2, #1
    // 0xeaa8e0: StoreField: r0->field_b = r3
    //     0xeaa8e0: stur            w3, [x0, #0xb]
    // 0xeaa8e4: LoadField: r2 = r0->field_f
    //     0xeaa8e4: ldur            w2, [x0, #0xf]
    // 0xeaa8e8: DecompressPointer r2
    //     0xeaa8e8: add             x2, x2, HEAP, lsl #32
    // 0xeaa8ec: add             x3, x2, x1, lsl #2
    // 0xeaa8f0: r16 = 1.000000
    //     0xeaa8f0: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xeaa8f4: StoreField: r3->field_f = r16
    //     0xeaa8f4: stur            w16, [x3, #0xf]
    // 0xeaa8f8: b               #0xeaa900
    // 0xeaa8fc: ldur            x0, [fp, #-0x18]
    // 0xeaa900: ldur            x1, [fp, #-0x10]
    // 0xeaa904: LoadField: r2 = r0->field_b
    //     0xeaa904: ldur            w2, [x0, #0xb]
    // 0xeaa908: LoadField: r3 = r1->field_b
    //     0xeaa908: ldur            w3, [x1, #0xb]
    // 0xeaa90c: cmp             w2, w3
    // 0xeaa910: b.ne            #0xeaab48
    // 0xeaa914: r2 = 1
    //     0xeaa914: movz            x2, #0x1
    // 0xeaa918: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeaa918: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeaa91c: r0 = sublist()
    //     0xeaa91c: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xeaa920: mov             x3, x0
    // 0xeaa924: stur            x3, [fp, #-0x48]
    // 0xeaa928: LoadField: r4 = r3->field_7
    //     0xeaa928: ldur            w4, [x3, #7]
    // 0xeaa92c: DecompressPointer r4
    //     0xeaa92c: add             x4, x4, HEAP, lsl #32
    // 0xeaa930: stur            x4, [fp, #-0x30]
    // 0xeaa934: LoadField: r0 = r3->field_b
    //     0xeaa934: ldur            w0, [x3, #0xb]
    // 0xeaa938: r5 = LoadInt32Instr(r0)
    //     0xeaa938: sbfx            x5, x0, #1, #0x1f
    // 0xeaa93c: stur            x5, [fp, #-0x40]
    // 0xeaa940: ldur            x7, [fp, #-0x28]
    // 0xeaa944: ldur            x6, [fp, #-0x20]
    // 0xeaa948: r0 = 0
    //     0xeaa948: movz            x0, #0
    // 0xeaa94c: stur            x7, [fp, #-0x28]
    // 0xeaa950: CheckStackOverflow
    //     0xeaa950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa954: cmp             SP, x16
    //     0xeaa958: b.ls            #0xeaab94
    // 0xeaa95c: LoadField: r1 = r3->field_b
    //     0xeaa95c: ldur            w1, [x3, #0xb]
    // 0xeaa960: r2 = LoadInt32Instr(r1)
    //     0xeaa960: sbfx            x2, x1, #1, #0x1f
    // 0xeaa964: cmp             x5, x2
    // 0xeaa968: b.ne            #0xeaab68
    // 0xeaa96c: cmp             x0, x2
    // 0xeaa970: b.ge            #0xeaaad0
    // 0xeaa974: LoadField: r1 = r3->field_f
    //     0xeaa974: ldur            w1, [x3, #0xf]
    // 0xeaa978: DecompressPointer r1
    //     0xeaa978: add             x1, x1, HEAP, lsl #32
    // 0xeaa97c: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0xeaa97c: add             x16, x1, x0, lsl #2
    //     0xeaa980: ldur            w8, [x16, #0xf]
    // 0xeaa984: DecompressPointer r8
    //     0xeaa984: add             x8, x8, HEAP, lsl #32
    // 0xeaa988: stur            x8, [fp, #-0x10]
    // 0xeaa98c: add             x9, x0, #1
    // 0xeaa990: stur            x9, [fp, #-0x38]
    // 0xeaa994: cmp             w8, NULL
    // 0xeaa998: b.ne            #0xeaa9cc
    // 0xeaa99c: mov             x0, x8
    // 0xeaa9a0: mov             x2, x4
    // 0xeaa9a4: r1 = Null
    //     0xeaa9a4: mov             x1, NULL
    // 0xeaa9a8: cmp             w2, NULL
    // 0xeaa9ac: b.eq            #0xeaa9cc
    // 0xeaa9b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeaa9b0: ldur            w4, [x2, #0x17]
    // 0xeaa9b4: DecompressPointer r4
    //     0xeaa9b4: add             x4, x4, HEAP, lsl #32
    // 0xeaa9b8: r8 = X0
    //     0xeaa9b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeaa9bc: LoadField: r9 = r4->field_7
    //     0xeaa9bc: ldur            x9, [x4, #7]
    // 0xeaa9c0: r3 = Null
    //     0xeaa9c0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57a30] Null
    //     0xeaa9c4: ldr             x3, [x3, #0xa30]
    // 0xeaa9c8: blr             x9
    // 0xeaa9cc: ldur            x0, [fp, #-0x20]
    // 0xeaa9d0: ldur            x4, [fp, #-0x28]
    // 0xeaa9d4: ldur            x7, [fp, #-0x10]
    // 0xeaa9d8: r3 = 4
    //     0xeaa9d8: movz            x3, #0x4
    // 0xeaa9dc: mov             x2, x3
    // 0xeaa9e0: r1 = Null
    //     0xeaa9e0: mov             x1, NULL
    // 0xeaa9e4: r0 = AllocateArray()
    //     0xeaa9e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaa9e8: mov             x2, x0
    // 0xeaa9ec: ldur            x0, [fp, #-0x28]
    // 0xeaa9f0: stur            x2, [fp, #-0x50]
    // 0xeaa9f4: StoreField: r2->field_f = r0
    //     0xeaa9f4: stur            w0, [x2, #0xf]
    // 0xeaa9f8: ldur            x7, [fp, #-0x10]
    // 0xeaa9fc: StoreField: r2->field_13 = r7
    //     0xeaa9fc: stur            w7, [x2, #0x13]
    // 0xeaaa00: r1 = <PdfColor>
    //     0xeaaa00: add             x1, PP, #0x51, lsl #12  ; [pp+0x51260] TypeArguments: <PdfColor>
    //     0xeaaa04: ldr             x1, [x1, #0x260]
    // 0xeaaa08: r0 = AllocateGrowableArray()
    //     0xeaaa08: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeaaa0c: mov             x1, x0
    // 0xeaaa10: ldur            x0, [fp, #-0x50]
    // 0xeaaa14: StoreField: r1->field_f = r0
    //     0xeaaa14: stur            w0, [x1, #0xf]
    // 0xeaaa18: r0 = 4
    //     0xeaaa18: movz            x0, #0x4
    // 0xeaaa1c: StoreField: r1->field_b = r0
    //     0xeaaa1c: stur            w0, [x1, #0xb]
    // 0xeaaa20: ldur            x2, [fp, #-8]
    // 0xeaaa24: mov             x3, x1
    // 0xeaaa28: r1 = <PdfDict<PdfDataType>>
    //     0xeaaa28: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaaa2c: ldr             x1, [x1, #0x758]
    // 0xeaaa30: r0 = PdfFunction.fromColors()
    //     0xeaaa30: bl              #0xeaac10  ; [package:pdf/src/pdf/obj/function.dart] PdfFunction::PdfFunction.fromColors
    // 0xeaaa34: mov             x2, x0
    // 0xeaaa38: ldur            x0, [fp, #-0x20]
    // 0xeaaa3c: stur            x2, [fp, #-0x28]
    // 0xeaaa40: LoadField: r1 = r0->field_b
    //     0xeaaa40: ldur            w1, [x0, #0xb]
    // 0xeaaa44: LoadField: r3 = r0->field_f
    //     0xeaaa44: ldur            w3, [x0, #0xf]
    // 0xeaaa48: DecompressPointer r3
    //     0xeaaa48: add             x3, x3, HEAP, lsl #32
    // 0xeaaa4c: LoadField: r4 = r3->field_b
    //     0xeaaa4c: ldur            w4, [x3, #0xb]
    // 0xeaaa50: r3 = LoadInt32Instr(r1)
    //     0xeaaa50: sbfx            x3, x1, #1, #0x1f
    // 0xeaaa54: stur            x3, [fp, #-0x58]
    // 0xeaaa58: r1 = LoadInt32Instr(r4)
    //     0xeaaa58: sbfx            x1, x4, #1, #0x1f
    // 0xeaaa5c: cmp             x3, x1
    // 0xeaaa60: b.ne            #0xeaaa6c
    // 0xeaaa64: mov             x1, x0
    // 0xeaaa68: r0 = _growToNextCapacity()
    //     0xeaaa68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeaaa6c: ldur            x3, [fp, #-0x20]
    // 0xeaaa70: ldur            x2, [fp, #-0x58]
    // 0xeaaa74: add             x0, x2, #1
    // 0xeaaa78: lsl             x1, x0, #1
    // 0xeaaa7c: StoreField: r3->field_b = r1
    //     0xeaaa7c: stur            w1, [x3, #0xb]
    // 0xeaaa80: LoadField: r1 = r3->field_f
    //     0xeaaa80: ldur            w1, [x3, #0xf]
    // 0xeaaa84: DecompressPointer r1
    //     0xeaaa84: add             x1, x1, HEAP, lsl #32
    // 0xeaaa88: ldur            x0, [fp, #-0x28]
    // 0xeaaa8c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xeaaa8c: add             x25, x1, x2, lsl #2
    //     0xeaaa90: add             x25, x25, #0xf
    //     0xeaaa94: str             w0, [x25]
    //     0xeaaa98: tbz             w0, #0, #0xeaaab4
    //     0xeaaa9c: ldurb           w16, [x1, #-1]
    //     0xeaaaa0: ldurb           w17, [x0, #-1]
    //     0xeaaaa4: and             x16, x17, x16, lsr #2
    //     0xeaaaa8: tst             x16, HEAP, lsr #32
    //     0xeaaaac: b.eq            #0xeaaab4
    //     0xeaaab0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeaaab4: ldur            x7, [fp, #-0x10]
    // 0xeaaab8: ldur            x0, [fp, #-0x38]
    // 0xeaaabc: mov             x6, x3
    // 0xeaaac0: ldur            x3, [fp, #-0x48]
    // 0xeaaac4: ldur            x4, [fp, #-0x30]
    // 0xeaaac8: ldur            x5, [fp, #-0x40]
    // 0xeaaacc: b               #0xeaa94c
    // 0xeaaad0: ldur            x1, [fp, #-0x18]
    // 0xeaaad4: mov             x3, x6
    // 0xeaaad8: LoadField: r0 = r1->field_b
    //     0xeaaad8: ldur            w0, [x1, #0xb]
    // 0xeaaadc: r2 = LoadInt32Instr(r0)
    //     0xeaaadc: sbfx            x2, x0, #1, #0x1f
    // 0xeaaae0: sub             x0, x2, #1
    // 0xeaaae4: lsl             x2, x0, #1
    // 0xeaaae8: str             x2, [SP]
    // 0xeaaaec: r2 = 1
    //     0xeaaaec: movz            x2, #0x1
    // 0xeaaaf0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeaaaf0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeaaaf4: r0 = sublist()
    //     0xeaaaf4: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xeaaaf8: r1 = <PdfDict<PdfDataType>>
    //     0xeaaaf8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaaafc: ldr             x1, [x1, #0x758]
    // 0xeaab00: stur            x0, [fp, #-0x10]
    // 0xeaab04: r0 = PdfStitchingFunction()
    //     0xeaab04: bl              #0xeaac04  ; AllocatePdfStitchingFunctionStub -> PdfStitchingFunction (size=0x44)
    // 0xeaab08: mov             x3, x0
    // 0xeaab0c: ldur            x0, [fp, #-0x20]
    // 0xeaab10: stur            x3, [fp, #-0x18]
    // 0xeaab14: StoreField: r3->field_2b = r0
    //     0xeaab14: stur            w0, [x3, #0x2b]
    // 0xeaab18: ldur            x0, [fp, #-0x10]
    // 0xeaab1c: StoreField: r3->field_2f = r0
    //     0xeaab1c: stur            w0, [x3, #0x2f]
    // 0xeaab20: StoreField: r3->field_33 = rZR
    //     0xeaab20: stur            xzr, [x3, #0x33]
    // 0xeaab24: d0 = 1.000000
    //     0xeaab24: fmov            d0, #1.00000000
    // 0xeaab28: StoreField: r3->field_3b = d0
    //     0xeaab28: stur            d0, [x3, #0x3b]
    // 0xeaab2c: mov             x1, x3
    // 0xeaab30: ldur            x2, [fp, #-8]
    // 0xeaab34: r0 = PdfBaseFunction()
    //     0xeaab34: bl              #0xeaab9c  ; [package:pdf/src/pdf/obj/function.dart] PdfBaseFunction::PdfBaseFunction
    // 0xeaab38: ldur            x0, [fp, #-0x18]
    // 0xeaab3c: LeaveFrame
    //     0xeaab3c: mov             SP, fp
    //     0xeaab40: ldp             fp, lr, [SP], #0x10
    // 0xeaab44: ret
    //     0xeaab44: ret             
    // 0xeaab48: r0 = _Exception()
    //     0xeaab48: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xeaab4c: mov             x1, x0
    // 0xeaab50: r0 = "The number of colors in a gradient must match the number of stops"
    //     0xeaab50: add             x0, PP, #0x57, lsl #12  ; [pp+0x57a40] "The number of colors in a gradient must match the number of stops"
    //     0xeaab54: ldr             x0, [x0, #0xa40]
    // 0xeaab58: StoreField: r1->field_7 = r0
    //     0xeaab58: stur            w0, [x1, #7]
    // 0xeaab5c: mov             x0, x1
    // 0xeaab60: r0 = Throw()
    //     0xeaab60: bl              #0xec04b8  ; ThrowStub
    // 0xeaab64: brk             #0
    // 0xeaab68: mov             x0, x3
    // 0xeaab6c: r0 = ConcurrentModificationError()
    //     0xeaab6c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xeaab70: mov             x1, x0
    // 0xeaab74: ldur            x0, [fp, #-0x48]
    // 0xeaab78: StoreField: r1->field_b = r0
    //     0xeaab78: stur            w0, [x1, #0xb]
    // 0xeaab7c: mov             x0, x1
    // 0xeaab80: r0 = Throw()
    //     0xeaab80: bl              #0xec04b8  ; ThrowStub
    // 0xeaab84: brk             #0
    // 0xeaab88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaab88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaab8c: b               #0xeaa6bc
    // 0xeaab90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaab90: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaab94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaab94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaab98: b               #0xeaa95c
  }
  _ PdfBaseFunction(/* No info */) {
    // ** addr: 0xeaab9c, size: 0x68
    // 0xeaab9c: EnterFrame
    //     0xeaab9c: stp             fp, lr, [SP, #-0x10]!
    //     0xeaaba0: mov             fp, SP
    // 0xeaaba4: AllocStack(0x18)
    //     0xeaaba4: sub             SP, SP, #0x18
    // 0xeaaba8: SetupParameters(PdfBaseFunction this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeaaba8: mov             x0, x1
    //     0xeaabac: stur            x1, [fp, #-8]
    //     0xeaabb0: stur            x2, [fp, #-0x10]
    // 0xeaabb4: CheckStackOverflow
    //     0xeaabb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaabb8: cmp             SP, x16
    //     0xeaabbc: b.ls            #0xeaabfc
    // 0xeaabc0: r1 = <PdfDataType>
    //     0xeaabc0: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xeaabc4: ldr             x1, [x1, #0x4c8]
    // 0xeaabc8: r0 = PdfDict()
    //     0xeaabc8: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xeaabcc: mov             x1, x0
    // 0xeaabd0: stur            x0, [fp, #-0x18]
    // 0xeaabd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xeaabd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xeaabd8: r0 = PdfDict()
    //     0xeaabd8: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0xeaabdc: ldur            x1, [fp, #-8]
    // 0xeaabe0: ldur            x2, [fp, #-0x10]
    // 0xeaabe4: ldur            x3, [fp, #-0x18]
    // 0xeaabe8: r0 = PdfObject()
    //     0xeaabe8: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xeaabec: r0 = Null
    //     0xeaabec: mov             x0, NULL
    // 0xeaabf0: LeaveFrame
    //     0xeaabf0: mov             SP, fp
    //     0xeaabf4: ldp             fp, lr, [SP], #0x10
    // 0xeaabf8: ret
    //     0xeaabf8: ret             
    // 0xeaabfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaabfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaac00: b               #0xeaabc0
  }
}

// class id: 892, size: 0x44, field offset: 0x2c
class PdfStitchingFunction extends PdfBaseFunction {

  _ prepare(/* No info */) {
    // ** addr: 0x7ca1cc, size: 0x2d8
    // 0x7ca1cc: EnterFrame
    //     0x7ca1cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca1d0: mov             fp, SP
    // 0x7ca1d4: AllocStack(0x28)
    //     0x7ca1d4: sub             SP, SP, #0x28
    // 0x7ca1d8: SetupParameters(PdfStitchingFunction this /* r1 => r3, fp-0x18 */)
    //     0x7ca1d8: mov             x3, x1
    //     0x7ca1dc: stur            x1, [fp, #-0x18]
    // 0x7ca1e0: CheckStackOverflow
    //     0x7ca1e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca1e4: cmp             SP, x16
    //     0x7ca1e8: b.ls            #0x7ca494
    // 0x7ca1ec: LoadField: r4 = r3->field_1b
    //     0x7ca1ec: ldur            w4, [x3, #0x1b]
    // 0x7ca1f0: DecompressPointer r4
    //     0x7ca1f0: add             x4, x4, HEAP, lsl #32
    // 0x7ca1f4: stur            x4, [fp, #-0x10]
    // 0x7ca1f8: LoadField: r5 = r4->field_7
    //     0x7ca1f8: ldur            w5, [x4, #7]
    // 0x7ca1fc: DecompressPointer r5
    //     0x7ca1fc: add             x5, x5, HEAP, lsl #32
    // 0x7ca200: mov             x2, x5
    // 0x7ca204: stur            x5, [fp, #-8]
    // 0x7ca208: r0 = Instance_PdfNum
    //     0x7ca208: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b108] Obj!PdfNum@e0c7b1
    //     0x7ca20c: ldr             x0, [x0, #0x108]
    // 0x7ca210: r1 = Null
    //     0x7ca210: mov             x1, NULL
    // 0x7ca214: cmp             w2, NULL
    // 0x7ca218: b.eq            #0x7ca23c
    // 0x7ca21c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca21c: ldur            w4, [x2, #0x17]
    // 0x7ca220: DecompressPointer r4
    //     0x7ca220: add             x4, x4, HEAP, lsl #32
    // 0x7ca224: r8 = X0 bound PdfDataType
    //     0x7ca224: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca228: ldr             x8, [x8, #0x6d8]
    // 0x7ca22c: LoadField: r9 = r4->field_7
    //     0x7ca22c: ldur            x9, [x4, #7]
    // 0x7ca230: r3 = Null
    //     0x7ca230: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b110] Null
    //     0x7ca234: ldr             x3, [x3, #0x110]
    // 0x7ca238: blr             x9
    // 0x7ca23c: ldur            x0, [fp, #-0x10]
    // 0x7ca240: LoadField: r4 = r0->field_b
    //     0x7ca240: ldur            w4, [x0, #0xb]
    // 0x7ca244: DecompressPointer r4
    //     0x7ca244: add             x4, x4, HEAP, lsl #32
    // 0x7ca248: mov             x1, x4
    // 0x7ca24c: stur            x4, [fp, #-0x20]
    // 0x7ca250: r2 = "/FunctionType"
    //     0x7ca250: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b088] "/FunctionType"
    //     0x7ca254: ldr             x2, [x2, #0x88]
    // 0x7ca258: r3 = Instance_PdfNum
    //     0x7ca258: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b108] Obj!PdfNum@e0c7b1
    //     0x7ca25c: ldr             x3, [x3, #0x108]
    // 0x7ca260: r0 = []=()
    //     0x7ca260: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca264: ldur            x0, [fp, #-0x18]
    // 0x7ca268: LoadField: r2 = r0->field_2b
    //     0x7ca268: ldur            w2, [x0, #0x2b]
    // 0x7ca26c: DecompressPointer r2
    //     0x7ca26c: add             x2, x2, HEAP, lsl #32
    // 0x7ca270: mov             x1, x2
    // 0x7ca274: stur            x2, [fp, #-0x10]
    // 0x7ca278: r0 = fromObjects()
    //     0x7ca278: bl              #0x7ca4a4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects
    // 0x7ca27c: ldur            x2, [fp, #-8]
    // 0x7ca280: mov             x3, x0
    // 0x7ca284: r1 = Null
    //     0x7ca284: mov             x1, NULL
    // 0x7ca288: stur            x3, [fp, #-0x28]
    // 0x7ca28c: cmp             w2, NULL
    // 0x7ca290: b.eq            #0x7ca2b4
    // 0x7ca294: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca294: ldur            w4, [x2, #0x17]
    // 0x7ca298: DecompressPointer r4
    //     0x7ca298: add             x4, x4, HEAP, lsl #32
    // 0x7ca29c: r8 = X0 bound PdfDataType
    //     0x7ca29c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca2a0: ldr             x8, [x8, #0x6d8]
    // 0x7ca2a4: LoadField: r9 = r4->field_7
    //     0x7ca2a4: ldur            x9, [x4, #7]
    // 0x7ca2a8: r3 = Null
    //     0x7ca2a8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b120] Null
    //     0x7ca2ac: ldr             x3, [x3, #0x120]
    // 0x7ca2b0: blr             x9
    // 0x7ca2b4: ldur            x1, [fp, #-0x20]
    // 0x7ca2b8: ldur            x3, [fp, #-0x28]
    // 0x7ca2bc: r2 = "/Functions"
    //     0x7ca2bc: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b130] "/Functions"
    //     0x7ca2c0: ldr             x2, [x2, #0x130]
    // 0x7ca2c4: r0 = []=()
    //     0x7ca2c4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca2c8: ldur            x1, [fp, #-0x20]
    // 0x7ca2cc: r2 = "/Order"
    //     0x7ca2cc: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0b8] "/Order"
    //     0x7ca2d0: ldr             x2, [x2, #0xb8]
    // 0x7ca2d4: r3 = Instance_PdfNum
    //     0x7ca2d4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b108] Obj!PdfNum@e0c7b1
    //     0x7ca2d8: ldr             x3, [x3, #0x108]
    // 0x7ca2dc: r0 = []=()
    //     0x7ca2dc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca2e0: r1 = Null
    //     0x7ca2e0: mov             x1, NULL
    // 0x7ca2e4: r2 = 4
    //     0x7ca2e4: movz            x2, #0x4
    // 0x7ca2e8: r0 = AllocateArray()
    //     0x7ca2e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ca2ec: stur            x0, [fp, #-0x28]
    // 0x7ca2f0: r16 = 0.000000
    //     0x7ca2f0: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7ca2f4: StoreField: r0->field_f = r16
    //     0x7ca2f4: stur            w16, [x0, #0xf]
    // 0x7ca2f8: r16 = 1.000000
    //     0x7ca2f8: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x7ca2fc: StoreField: r0->field_13 = r16
    //     0x7ca2fc: stur            w16, [x0, #0x13]
    // 0x7ca300: r1 = <num>
    //     0x7ca300: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x7ca304: r0 = AllocateGrowableArray()
    //     0x7ca304: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7ca308: mov             x1, x0
    // 0x7ca30c: ldur            x0, [fp, #-0x28]
    // 0x7ca310: StoreField: r1->field_f = r0
    //     0x7ca310: stur            w0, [x1, #0xf]
    // 0x7ca314: r0 = 4
    //     0x7ca314: movz            x0, #0x4
    // 0x7ca318: StoreField: r1->field_b = r0
    //     0x7ca318: stur            w0, [x1, #0xb]
    // 0x7ca31c: r0 = fromNum()
    //     0x7ca31c: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca320: ldur            x2, [fp, #-8]
    // 0x7ca324: mov             x3, x0
    // 0x7ca328: r1 = Null
    //     0x7ca328: mov             x1, NULL
    // 0x7ca32c: stur            x3, [fp, #-0x28]
    // 0x7ca330: cmp             w2, NULL
    // 0x7ca334: b.eq            #0x7ca358
    // 0x7ca338: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca338: ldur            w4, [x2, #0x17]
    // 0x7ca33c: DecompressPointer r4
    //     0x7ca33c: add             x4, x4, HEAP, lsl #32
    // 0x7ca340: r8 = X0 bound PdfDataType
    //     0x7ca340: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca344: ldr             x8, [x8, #0x6d8]
    // 0x7ca348: LoadField: r9 = r4->field_7
    //     0x7ca348: ldur            x9, [x4, #7]
    // 0x7ca34c: r3 = Null
    //     0x7ca34c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b138] Null
    //     0x7ca350: ldr             x3, [x3, #0x138]
    // 0x7ca354: blr             x9
    // 0x7ca358: ldur            x1, [fp, #-0x20]
    // 0x7ca35c: ldur            x3, [fp, #-0x28]
    // 0x7ca360: r2 = "/Domain"
    //     0x7ca360: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b0d0] "/Domain"
    //     0x7ca364: ldr             x2, [x2, #0xd0]
    // 0x7ca368: r0 = []=()
    //     0x7ca368: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca36c: ldur            x0, [fp, #-0x18]
    // 0x7ca370: LoadField: r1 = r0->field_2f
    //     0x7ca370: ldur            w1, [x0, #0x2f]
    // 0x7ca374: DecompressPointer r1
    //     0x7ca374: add             x1, x1, HEAP, lsl #32
    // 0x7ca378: r0 = fromNum()
    //     0x7ca378: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca37c: ldur            x2, [fp, #-8]
    // 0x7ca380: mov             x3, x0
    // 0x7ca384: r1 = Null
    //     0x7ca384: mov             x1, NULL
    // 0x7ca388: stur            x3, [fp, #-0x18]
    // 0x7ca38c: cmp             w2, NULL
    // 0x7ca390: b.eq            #0x7ca3b4
    // 0x7ca394: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca394: ldur            w4, [x2, #0x17]
    // 0x7ca398: DecompressPointer r4
    //     0x7ca398: add             x4, x4, HEAP, lsl #32
    // 0x7ca39c: r8 = X0 bound PdfDataType
    //     0x7ca39c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca3a0: ldr             x8, [x8, #0x6d8]
    // 0x7ca3a4: LoadField: r9 = r4->field_7
    //     0x7ca3a4: ldur            x9, [x4, #7]
    // 0x7ca3a8: r3 = Null
    //     0x7ca3a8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b148] Null
    //     0x7ca3ac: ldr             x3, [x3, #0x148]
    // 0x7ca3b0: blr             x9
    // 0x7ca3b4: ldur            x1, [fp, #-0x20]
    // 0x7ca3b8: ldur            x3, [fp, #-0x18]
    // 0x7ca3bc: r2 = "/Bounds"
    //     0x7ca3bc: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b158] "/Bounds"
    //     0x7ca3c0: ldr             x2, [x2, #0x158]
    // 0x7ca3c4: r0 = []=()
    //     0x7ca3c4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca3c8: ldur            x0, [fp, #-0x10]
    // 0x7ca3cc: LoadField: r1 = r0->field_b
    //     0x7ca3cc: ldur            w1, [x0, #0xb]
    // 0x7ca3d0: r0 = LoadInt32Instr(r1)
    //     0x7ca3d0: sbfx            x0, x1, #1, #0x1f
    // 0x7ca3d4: lsl             x2, x0, #1
    // 0x7ca3d8: r1 = <int>
    //     0x7ca3d8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7ca3dc: r0 = _GrowableList()
    //     0x7ca3dc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7ca3e0: LoadField: r1 = r0->field_b
    //     0x7ca3e0: ldur            w1, [x0, #0xb]
    // 0x7ca3e4: r2 = LoadInt32Instr(r1)
    //     0x7ca3e4: sbfx            x2, x1, #1, #0x1f
    // 0x7ca3e8: LoadField: r1 = r0->field_f
    //     0x7ca3e8: ldur            w1, [x0, #0xf]
    // 0x7ca3ec: DecompressPointer r1
    //     0x7ca3ec: add             x1, x1, HEAP, lsl #32
    // 0x7ca3f0: r4 = 0
    //     0x7ca3f0: movz            x4, #0
    // 0x7ca3f4: r3 = 1
    //     0x7ca3f4: movz            x3, #0x1
    // 0x7ca3f8: CheckStackOverflow
    //     0x7ca3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca3fc: cmp             SP, x16
    //     0x7ca400: b.ls            #0x7ca49c
    // 0x7ca404: cmp             x4, x2
    // 0x7ca408: b.ge            #0x7ca430
    // 0x7ca40c: mov             x5, x4
    // 0x7ca410: ubfx            x5, x5, #0, #0x20
    // 0x7ca414: and             x6, x5, x3
    // 0x7ca418: lsl             w5, w6, #1
    // 0x7ca41c: ArrayStore: r1[r4] = r5  ; Unknown_4
    //     0x7ca41c: add             x6, x1, x4, lsl #2
    //     0x7ca420: stur            w5, [x6, #0xf]
    // 0x7ca424: add             x5, x4, #1
    // 0x7ca428: mov             x4, x5
    // 0x7ca42c: b               #0x7ca3f8
    // 0x7ca430: mov             x1, x0
    // 0x7ca434: r0 = fromNum()
    //     0x7ca434: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ca438: ldur            x2, [fp, #-8]
    // 0x7ca43c: mov             x3, x0
    // 0x7ca440: r1 = Null
    //     0x7ca440: mov             x1, NULL
    // 0x7ca444: stur            x3, [fp, #-8]
    // 0x7ca448: cmp             w2, NULL
    // 0x7ca44c: b.eq            #0x7ca470
    // 0x7ca450: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ca450: ldur            w4, [x2, #0x17]
    // 0x7ca454: DecompressPointer r4
    //     0x7ca454: add             x4, x4, HEAP, lsl #32
    // 0x7ca458: r8 = X0 bound PdfDataType
    //     0x7ca458: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ca45c: ldr             x8, [x8, #0x6d8]
    // 0x7ca460: LoadField: r9 = r4->field_7
    //     0x7ca460: ldur            x9, [x4, #7]
    // 0x7ca464: r3 = Null
    //     0x7ca464: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b160] Null
    //     0x7ca468: ldr             x3, [x3, #0x160]
    // 0x7ca46c: blr             x9
    // 0x7ca470: ldur            x1, [fp, #-0x20]
    // 0x7ca474: ldur            x3, [fp, #-8]
    // 0x7ca478: r2 = "/Encode"
    //     0x7ca478: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b170] "/Encode"
    //     0x7ca47c: ldr             x2, [x2, #0x170]
    // 0x7ca480: r0 = []=()
    //     0x7ca480: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca484: r0 = Null
    //     0x7ca484: mov             x0, NULL
    // 0x7ca488: LeaveFrame
    //     0x7ca488: mov             SP, fp
    //     0x7ca48c: ldp             fp, lr, [SP], #0x10
    // 0x7ca490: ret
    //     0x7ca490: ret             
    // 0x7ca494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca498: b               #0x7ca1ec
    // 0x7ca49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ca49c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ca4a0: b               #0x7ca404
  }
  _ toString(/* No info */) {
    // ** addr: 0xc35454, size: 0x1ac
    // 0xc35454: EnterFrame
    //     0xc35454: stp             fp, lr, [SP, #-0x10]!
    //     0xc35458: mov             fp, SP
    // 0xc3545c: AllocStack(0x8)
    //     0xc3545c: sub             SP, SP, #8
    // 0xc35460: CheckStackOverflow
    //     0xc35460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35464: cmp             SP, x16
    //     0xc35468: b.ls            #0xc355c8
    // 0xc3546c: r1 = Null
    //     0xc3546c: mov             x1, NULL
    // 0xc35470: r2 = 18
    //     0xc35470: movz            x2, #0x12
    // 0xc35474: r0 = AllocateArray()
    //     0xc35474: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35478: mov             x2, x0
    // 0xc3547c: r16 = PdfStitchingFunction
    //     0xc3547c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b100] Type: PdfStitchingFunction
    //     0xc35480: ldr             x16, [x16, #0x100]
    // 0xc35484: StoreField: r2->field_f = r16
    //     0xc35484: stur            w16, [x2, #0xf]
    // 0xc35488: r16 = " "
    //     0xc35488: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc3548c: StoreField: r2->field_13 = r16
    //     0xc3548c: stur            w16, [x2, #0x13]
    // 0xc35490: ldr             x3, [fp, #0x10]
    // 0xc35494: LoadField: d0 = r3->field_33
    //     0xc35494: ldur            d0, [x3, #0x33]
    // 0xc35498: r0 = inline_Allocate_Double()
    //     0xc35498: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3549c: add             x0, x0, #0x10
    //     0xc354a0: cmp             x1, x0
    //     0xc354a4: b.ls            #0xc355d0
    //     0xc354a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xc354ac: sub             x0, x0, #0xf
    //     0xc354b0: movz            x1, #0xe15c
    //     0xc354b4: movk            x1, #0x3, lsl #16
    //     0xc354b8: stur            x1, [x0, #-1]
    // 0xc354bc: StoreField: r0->field_7 = d0
    //     0xc354bc: stur            d0, [x0, #7]
    // 0xc354c0: mov             x1, x2
    // 0xc354c4: ArrayStore: r1[2] = r0  ; List_4
    //     0xc354c4: add             x25, x1, #0x17
    //     0xc354c8: str             w0, [x25]
    //     0xc354cc: tbz             w0, #0, #0xc354e8
    //     0xc354d0: ldurb           w16, [x1, #-1]
    //     0xc354d4: ldurb           w17, [x0, #-1]
    //     0xc354d8: and             x16, x17, x16, lsr #2
    //     0xc354dc: tst             x16, HEAP, lsr #32
    //     0xc354e0: b.eq            #0xc354e8
    //     0xc354e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc354e8: r16 = " "
    //     0xc354e8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc354ec: StoreField: r2->field_1b = r16
    //     0xc354ec: stur            w16, [x2, #0x1b]
    // 0xc354f0: LoadField: r0 = r3->field_2f
    //     0xc354f0: ldur            w0, [x3, #0x2f]
    // 0xc354f4: DecompressPointer r0
    //     0xc354f4: add             x0, x0, HEAP, lsl #32
    // 0xc354f8: mov             x1, x2
    // 0xc354fc: ArrayStore: r1[4] = r0  ; List_4
    //     0xc354fc: add             x25, x1, #0x1f
    //     0xc35500: str             w0, [x25]
    //     0xc35504: tbz             w0, #0, #0xc35520
    //     0xc35508: ldurb           w16, [x1, #-1]
    //     0xc3550c: ldurb           w17, [x0, #-1]
    //     0xc35510: and             x16, x17, x16, lsr #2
    //     0xc35514: tst             x16, HEAP, lsr #32
    //     0xc35518: b.eq            #0xc35520
    //     0xc3551c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35520: r16 = " "
    //     0xc35520: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35524: StoreField: r2->field_23 = r16
    //     0xc35524: stur            w16, [x2, #0x23]
    // 0xc35528: LoadField: d0 = r3->field_3b
    //     0xc35528: ldur            d0, [x3, #0x3b]
    // 0xc3552c: r0 = inline_Allocate_Double()
    //     0xc3552c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35530: add             x0, x0, #0x10
    //     0xc35534: cmp             x1, x0
    //     0xc35538: b.ls            #0xc355e8
    //     0xc3553c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35540: sub             x0, x0, #0xf
    //     0xc35544: movz            x1, #0xe15c
    //     0xc35548: movk            x1, #0x3, lsl #16
    //     0xc3554c: stur            x1, [x0, #-1]
    // 0xc35550: StoreField: r0->field_7 = d0
    //     0xc35550: stur            d0, [x0, #7]
    // 0xc35554: mov             x1, x2
    // 0xc35558: ArrayStore: r1[6] = r0  ; List_4
    //     0xc35558: add             x25, x1, #0x27
    //     0xc3555c: str             w0, [x25]
    //     0xc35560: tbz             w0, #0, #0xc3557c
    //     0xc35564: ldurb           w16, [x1, #-1]
    //     0xc35568: ldurb           w17, [x0, #-1]
    //     0xc3556c: and             x16, x17, x16, lsr #2
    //     0xc35570: tst             x16, HEAP, lsr #32
    //     0xc35574: b.eq            #0xc3557c
    //     0xc35578: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3557c: r16 = " "
    //     0xc3557c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35580: StoreField: r2->field_2b = r16
    //     0xc35580: stur            w16, [x2, #0x2b]
    // 0xc35584: LoadField: r0 = r3->field_2b
    //     0xc35584: ldur            w0, [x3, #0x2b]
    // 0xc35588: DecompressPointer r0
    //     0xc35588: add             x0, x0, HEAP, lsl #32
    // 0xc3558c: mov             x1, x2
    // 0xc35590: ArrayStore: r1[8] = r0  ; List_4
    //     0xc35590: add             x25, x1, #0x2f
    //     0xc35594: str             w0, [x25]
    //     0xc35598: tbz             w0, #0, #0xc355b4
    //     0xc3559c: ldurb           w16, [x1, #-1]
    //     0xc355a0: ldurb           w17, [x0, #-1]
    //     0xc355a4: and             x16, x17, x16, lsr #2
    //     0xc355a8: tst             x16, HEAP, lsr #32
    //     0xc355ac: b.eq            #0xc355b4
    //     0xc355b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc355b4: str             x2, [SP]
    // 0xc355b8: r0 = _interpolate()
    //     0xc355b8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc355bc: LeaveFrame
    //     0xc355bc: mov             SP, fp
    //     0xc355c0: ldp             fp, lr, [SP], #0x10
    // 0xc355c4: ret
    //     0xc355c4: ret             
    // 0xc355c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc355c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc355cc: b               #0xc3546c
    // 0xc355d0: SaveReg d0
    //     0xc355d0: str             q0, [SP, #-0x10]!
    // 0xc355d4: stp             x2, x3, [SP, #-0x10]!
    // 0xc355d8: r0 = AllocateDouble()
    //     0xc355d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc355dc: ldp             x2, x3, [SP], #0x10
    // 0xc355e0: RestoreReg d0
    //     0xc355e0: ldr             q0, [SP], #0x10
    // 0xc355e4: b               #0xc354bc
    // 0xc355e8: SaveReg d0
    //     0xc355e8: str             q0, [SP, #-0x10]!
    // 0xc355ec: stp             x2, x3, [SP, #-0x10]!
    // 0xc355f0: r0 = AllocateDouble()
    //     0xc355f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc355f4: ldp             x2, x3, [SP], #0x10
    // 0xc355f8: RestoreReg d0
    //     0xc355f8: ldr             q0, [SP], #0x10
    // 0xc355fc: b               #0xc35550
  }
}
