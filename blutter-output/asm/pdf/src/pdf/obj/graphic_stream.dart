// lib: , url: package:pdf/src/pdf/obj/graphic_stream.dart

// class id: 1050802, size: 0x8
class :: {
}

// class id: 883, size: 0x2c, field offset: 0x2c
abstract class PdfGraphicStream extends PdfObject<dynamic> {
}

// class id: 888, size: 0x50, field offset: 0x34
//   transformed mixin,
abstract class _PdfGraphicXObject&PdfXObject&PdfGraphicStream extends PdfXObject
     with PdfGraphicStream {

  _ prepare(/* No info */) {
    // ** addr: 0x7ca910, size: 0x4cc
    // 0x7ca910: EnterFrame
    //     0x7ca910: stp             fp, lr, [SP, #-0x10]!
    //     0x7ca914: mov             fp, SP
    // 0x7ca918: AllocStack(0x40)
    //     0x7ca918: sub             SP, SP, #0x40
    // 0x7ca91c: SetupParameters(_PdfGraphicXObject&PdfXObject&PdfGraphicStream this /* r1 => r0, fp-0x8 */)
    //     0x7ca91c: mov             x0, x1
    //     0x7ca920: stur            x1, [fp, #-8]
    // 0x7ca924: CheckStackOverflow
    //     0x7ca924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ca928: cmp             SP, x16
    //     0x7ca92c: b.ls            #0x7cadd4
    // 0x7ca930: r1 = <PdfDataType>
    //     0x7ca930: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7ca934: ldr             x1, [x1, #0x4c8]
    // 0x7ca938: r0 = PdfDict()
    //     0x7ca938: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7ca93c: mov             x1, x0
    // 0x7ca940: stur            x0, [fp, #-0x10]
    // 0x7ca944: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ca944: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ca948: r0 = PdfDict()
    //     0x7ca948: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7ca94c: ldur            x0, [fp, #-8]
    // 0x7ca950: LoadField: r1 = r0->field_4b
    //     0x7ca950: ldur            w1, [x0, #0x4b]
    // 0x7ca954: DecompressPointer r1
    //     0x7ca954: add             x1, x1, HEAP, lsl #32
    // 0x7ca958: tbnz            w1, #4, #0x7ca9a4
    // 0x7ca95c: ldur            x2, [fp, #-0x10]
    // 0x7ca960: r1 = <PdfName>
    //     0x7ca960: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eeb8] TypeArguments: <PdfName>
    //     0x7ca964: ldr             x1, [x1, #0xeb8]
    // 0x7ca968: r0 = PdfArray()
    //     0x7ca968: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7ca96c: stur            x0, [fp, #-0x18]
    // 0x7ca970: r16 = const [Instance of 'PdfName', Instance of 'PdfName', Instance of 'PdfName', Instance of 'PdfName']
    //     0x7ca970: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eec0] List<PdfName>(4)
    //     0x7ca974: ldr             x16, [x16, #0xec0]
    // 0x7ca978: str             x16, [SP]
    // 0x7ca97c: mov             x1, x0
    // 0x7ca980: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7ca980: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7ca984: r0 = PdfArray()
    //     0x7ca984: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7ca988: ldur            x0, [fp, #-0x10]
    // 0x7ca98c: LoadField: r1 = r0->field_b
    //     0x7ca98c: ldur            w1, [x0, #0xb]
    // 0x7ca990: DecompressPointer r1
    //     0x7ca990: add             x1, x1, HEAP, lsl #32
    // 0x7ca994: ldur            x3, [fp, #-0x18]
    // 0x7ca998: r2 = "/ProcSet"
    //     0x7ca998: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eec8] "/ProcSet"
    //     0x7ca99c: ldr             x2, [x2, #0xec8]
    // 0x7ca9a0: r0 = []=()
    //     0x7ca9a0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca9a4: ldur            x0, [fp, #-8]
    // 0x7ca9a8: LoadField: r1 = r0->field_3b
    //     0x7ca9a8: ldur            w1, [x0, #0x3b]
    // 0x7ca9ac: DecompressPointer r1
    //     0x7ca9ac: add             x1, x1, HEAP, lsl #32
    // 0x7ca9b0: LoadField: r2 = r1->field_13
    //     0x7ca9b0: ldur            w2, [x1, #0x13]
    // 0x7ca9b4: r3 = LoadInt32Instr(r2)
    //     0x7ca9b4: sbfx            x3, x2, #1, #0x1f
    // 0x7ca9b8: asr             x2, x3, #1
    // 0x7ca9bc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7ca9bc: ldur            w3, [x1, #0x17]
    // 0x7ca9c0: r4 = LoadInt32Instr(r3)
    //     0x7ca9c0: sbfx            x4, x3, #1, #0x1f
    // 0x7ca9c4: sub             x3, x2, x4
    // 0x7ca9c8: cbz             x3, #0x7ca9f8
    // 0x7ca9cc: ldur            x2, [fp, #-0x10]
    // 0x7ca9d0: r0 = fromObjectMap()
    //     0x7ca9d0: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7ca9d4: mov             x1, x0
    // 0x7ca9d8: ldur            x0, [fp, #-0x10]
    // 0x7ca9dc: LoadField: r2 = r0->field_b
    //     0x7ca9dc: ldur            w2, [x0, #0xb]
    // 0x7ca9e0: DecompressPointer r2
    //     0x7ca9e0: add             x2, x2, HEAP, lsl #32
    // 0x7ca9e4: mov             x3, x1
    // 0x7ca9e8: mov             x1, x2
    // 0x7ca9ec: r2 = "/Font"
    //     0x7ca9ec: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c190] "/Font"
    //     0x7ca9f0: ldr             x2, [x2, #0x190]
    // 0x7ca9f4: r0 = []=()
    //     0x7ca9f4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ca9f8: ldur            x0, [fp, #-8]
    // 0x7ca9fc: LoadField: r1 = r0->field_3f
    //     0x7ca9fc: ldur            w1, [x0, #0x3f]
    // 0x7caa00: DecompressPointer r1
    //     0x7caa00: add             x1, x1, HEAP, lsl #32
    // 0x7caa04: LoadField: r2 = r1->field_13
    //     0x7caa04: ldur            w2, [x1, #0x13]
    // 0x7caa08: r3 = LoadInt32Instr(r2)
    //     0x7caa08: sbfx            x3, x2, #1, #0x1f
    // 0x7caa0c: asr             x2, x3, #1
    // 0x7caa10: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7caa10: ldur            w3, [x1, #0x17]
    // 0x7caa14: r4 = LoadInt32Instr(r3)
    //     0x7caa14: sbfx            x4, x3, #1, #0x1f
    // 0x7caa18: sub             x3, x2, x4
    // 0x7caa1c: cbz             x3, #0x7caa4c
    // 0x7caa20: ldur            x2, [fp, #-0x10]
    // 0x7caa24: r0 = fromObjectMap()
    //     0x7caa24: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7caa28: mov             x1, x0
    // 0x7caa2c: ldur            x0, [fp, #-0x10]
    // 0x7caa30: LoadField: r2 = r0->field_b
    //     0x7caa30: ldur            w2, [x0, #0xb]
    // 0x7caa34: DecompressPointer r2
    //     0x7caa34: add             x2, x2, HEAP, lsl #32
    // 0x7caa38: mov             x3, x1
    // 0x7caa3c: mov             x1, x2
    // 0x7caa40: r2 = "/Shading"
    //     0x7caa40: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eed0] "/Shading"
    //     0x7caa44: ldr             x2, [x2, #0xed0]
    // 0x7caa48: r0 = []=()
    //     0x7caa48: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7caa4c: ldur            x0, [fp, #-8]
    // 0x7caa50: LoadField: r1 = r0->field_43
    //     0x7caa50: ldur            w1, [x0, #0x43]
    // 0x7caa54: DecompressPointer r1
    //     0x7caa54: add             x1, x1, HEAP, lsl #32
    // 0x7caa58: LoadField: r2 = r1->field_13
    //     0x7caa58: ldur            w2, [x1, #0x13]
    // 0x7caa5c: r3 = LoadInt32Instr(r2)
    //     0x7caa5c: sbfx            x3, x2, #1, #0x1f
    // 0x7caa60: asr             x2, x3, #1
    // 0x7caa64: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7caa64: ldur            w3, [x1, #0x17]
    // 0x7caa68: r4 = LoadInt32Instr(r3)
    //     0x7caa68: sbfx            x4, x3, #1, #0x1f
    // 0x7caa6c: sub             x3, x2, x4
    // 0x7caa70: cbz             x3, #0x7caaa0
    // 0x7caa74: ldur            x2, [fp, #-0x10]
    // 0x7caa78: r0 = fromObjectMap()
    //     0x7caa78: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7caa7c: mov             x1, x0
    // 0x7caa80: ldur            x0, [fp, #-0x10]
    // 0x7caa84: LoadField: r2 = r0->field_b
    //     0x7caa84: ldur            w2, [x0, #0xb]
    // 0x7caa88: DecompressPointer r2
    //     0x7caa88: add             x2, x2, HEAP, lsl #32
    // 0x7caa8c: mov             x3, x1
    // 0x7caa90: mov             x1, x2
    // 0x7caa94: r2 = "/Pattern"
    //     0x7caa94: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eed8] "/Pattern"
    //     0x7caa98: ldr             x2, [x2, #0xed8]
    // 0x7caa9c: r0 = []=()
    //     0x7caa9c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7caaa0: ldur            x0, [fp, #-8]
    // 0x7caaa4: LoadField: r1 = r0->field_47
    //     0x7caaa4: ldur            w1, [x0, #0x47]
    // 0x7caaa8: DecompressPointer r1
    //     0x7caaa8: add             x1, x1, HEAP, lsl #32
    // 0x7caaac: LoadField: r2 = r1->field_13
    //     0x7caaac: ldur            w2, [x1, #0x13]
    // 0x7caab0: r3 = LoadInt32Instr(r2)
    //     0x7caab0: sbfx            x3, x2, #1, #0x1f
    // 0x7caab4: asr             x2, x3, #1
    // 0x7caab8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7caab8: ldur            w3, [x1, #0x17]
    // 0x7caabc: r4 = LoadInt32Instr(r3)
    //     0x7caabc: sbfx            x4, x3, #1, #0x1f
    // 0x7caac0: sub             x3, x2, x4
    // 0x7caac4: cbz             x3, #0x7caaf4
    // 0x7caac8: ldur            x2, [fp, #-0x10]
    // 0x7caacc: r0 = fromObjectMap()
    //     0x7caacc: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7caad0: mov             x1, x0
    // 0x7caad4: ldur            x0, [fp, #-0x10]
    // 0x7caad8: LoadField: r2 = r0->field_b
    //     0x7caad8: ldur            w2, [x0, #0xb]
    // 0x7caadc: DecompressPointer r2
    //     0x7caadc: add             x2, x2, HEAP, lsl #32
    // 0x7caae0: mov             x3, x1
    // 0x7caae4: mov             x1, x2
    // 0x7caae8: r2 = "/XObject"
    //     0x7caae8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ea58] "/XObject"
    //     0x7caaec: ldr             x2, [x2, #0xa58]
    // 0x7caaf0: r0 = []=()
    //     0x7caaf0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7caaf4: ldur            x0, [fp, #-8]
    // 0x7caaf8: LoadField: r3 = r0->field_23
    //     0x7caaf8: ldur            w3, [x0, #0x23]
    // 0x7caafc: DecompressPointer r3
    //     0x7caafc: add             x3, x3, HEAP, lsl #32
    // 0x7cab00: stur            x3, [fp, #-0x20]
    // 0x7cab04: LoadField: r1 = r3->field_27
    //     0x7cab04: ldur            w1, [x3, #0x27]
    // 0x7cab08: DecompressPointer r1
    //     0x7cab08: add             x1, x1, HEAP, lsl #32
    // 0x7cab0c: cmp             w1, NULL
    // 0x7cab10: b.eq            #0x7cace0
    // 0x7cab14: LoadField: r4 = r0->field_1b
    //     0x7cab14: ldur            w4, [x0, #0x1b]
    // 0x7cab18: DecompressPointer r4
    //     0x7cab18: add             x4, x4, HEAP, lsl #32
    // 0x7cab1c: mov             x1, x4
    // 0x7cab20: stur            x4, [fp, #-0x18]
    // 0x7cab24: r2 = "/Group"
    //     0x7cab24: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eee0] "/Group"
    //     0x7cab28: ldr             x2, [x2, #0xee0]
    // 0x7cab2c: r0 = contains()
    //     0x7cab2c: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cab30: tbz             w0, #4, #0x7cace0
    // 0x7cab34: ldur            x3, [fp, #-0x10]
    // 0x7cab38: ldur            x0, [fp, #-0x18]
    // 0x7cab3c: r1 = Null
    //     0x7cab3c: mov             x1, NULL
    // 0x7cab40: r2 = 20
    //     0x7cab40: movz            x2, #0x14
    // 0x7cab44: r0 = AllocateArray()
    //     0x7cab44: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cab48: stur            x0, [fp, #-0x28]
    // 0x7cab4c: r16 = "/Type"
    //     0x7cab4c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0x7cab50: ldr             x16, [x16, #0x630]
    // 0x7cab54: StoreField: r0->field_f = r16
    //     0x7cab54: stur            w16, [x0, #0xf]
    // 0x7cab58: r16 = Instance_PdfName
    //     0x7cab58: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eee8] Obj!PdfName@e0c861
    //     0x7cab5c: ldr             x16, [x16, #0xee8]
    // 0x7cab60: StoreField: r0->field_13 = r16
    //     0x7cab60: stur            w16, [x0, #0x13]
    // 0x7cab64: r16 = "/S"
    //     0x7cab64: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eef0] "/S"
    //     0x7cab68: ldr             x16, [x16, #0xef0]
    // 0x7cab6c: ArrayStore: r0[0] = r16  ; List_4
    //     0x7cab6c: stur            w16, [x0, #0x17]
    // 0x7cab70: r16 = Instance_PdfName
    //     0x7cab70: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eef8] Obj!PdfName@e0c851
    //     0x7cab74: ldr             x16, [x16, #0xef8]
    // 0x7cab78: StoreField: r0->field_1b = r16
    //     0x7cab78: stur            w16, [x0, #0x1b]
    // 0x7cab7c: r16 = "/CS"
    //     0x7cab7c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ef00] "/CS"
    //     0x7cab80: ldr             x16, [x16, #0xf00]
    // 0x7cab84: StoreField: r0->field_1f = r16
    //     0x7cab84: stur            w16, [x0, #0x1f]
    // 0x7cab88: r16 = Instance_PdfName
    //     0x7cab88: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0x7cab8c: ldr             x16, [x16, #0xb98]
    // 0x7cab90: StoreField: r0->field_23 = r16
    //     0x7cab90: stur            w16, [x0, #0x23]
    // 0x7cab94: r16 = "/I"
    //     0x7cab94: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0x7cab98: ldr             x16, [x16, #0xbe0]
    // 0x7cab9c: StoreField: r0->field_27 = r16
    //     0x7cab9c: stur            w16, [x0, #0x27]
    // 0x7caba0: r0 = PdfBool()
    //     0x7caba0: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7caba4: r2 = false
    //     0x7caba4: add             x2, NULL, #0x30  ; false
    // 0x7caba8: StoreField: r0->field_7 = r2
    //     0x7caba8: stur            w2, [x0, #7]
    // 0x7cabac: ldur            x1, [fp, #-0x28]
    // 0x7cabb0: ArrayStore: r1[7] = r0  ; List_4
    //     0x7cabb0: add             x25, x1, #0x2b
    //     0x7cabb4: str             w0, [x25]
    //     0x7cabb8: tbz             w0, #0, #0x7cabd4
    //     0x7cabbc: ldurb           w16, [x1, #-1]
    //     0x7cabc0: ldurb           w17, [x0, #-1]
    //     0x7cabc4: and             x16, x17, x16, lsr #2
    //     0x7cabc8: tst             x16, HEAP, lsr #32
    //     0x7cabcc: b.eq            #0x7cabd4
    //     0x7cabd0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cabd4: ldur            x1, [fp, #-0x28]
    // 0x7cabd8: r16 = "/K"
    //     0x7cabd8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ef08] "/K"
    //     0x7cabdc: ldr             x16, [x16, #0xf08]
    // 0x7cabe0: StoreField: r1->field_2f = r16
    //     0x7cabe0: stur            w16, [x1, #0x2f]
    // 0x7cabe4: r0 = PdfBool()
    //     0x7cabe4: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7cabe8: mov             x1, x0
    // 0x7cabec: r0 = false
    //     0x7cabec: add             x0, NULL, #0x30  ; false
    // 0x7cabf0: StoreField: r1->field_7 = r0
    //     0x7cabf0: stur            w0, [x1, #7]
    // 0x7cabf4: mov             x0, x1
    // 0x7cabf8: ldur            x1, [fp, #-0x28]
    // 0x7cabfc: ArrayStore: r1[9] = r0  ; List_4
    //     0x7cabfc: add             x25, x1, #0x33
    //     0x7cac00: str             w0, [x25]
    //     0x7cac04: tbz             w0, #0, #0x7cac20
    //     0x7cac08: ldurb           w16, [x1, #-1]
    //     0x7cac0c: ldurb           w17, [x0, #-1]
    //     0x7cac10: and             x16, x17, x16, lsr #2
    //     0x7cac14: tst             x16, HEAP, lsr #32
    //     0x7cac18: b.eq            #0x7cac20
    //     0x7cac1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cac20: r16 = <String, PdfDataType>
    //     0x7cac20: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0x7cac24: ldr             x16, [x16, #0x820]
    // 0x7cac28: ldur            lr, [fp, #-0x28]
    // 0x7cac2c: stp             lr, x16, [SP]
    // 0x7cac30: r0 = Map._fromLiteral()
    //     0x7cac30: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7cac34: r1 = <PdfDataType>
    //     0x7cac34: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7cac38: ldr             x1, [x1, #0x4c8]
    // 0x7cac3c: stur            x0, [fp, #-0x28]
    // 0x7cac40: r0 = PdfDict()
    //     0x7cac40: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7cac44: mov             x3, x0
    // 0x7cac48: ldur            x0, [fp, #-0x28]
    // 0x7cac4c: stur            x3, [fp, #-0x30]
    // 0x7cac50: StoreField: r3->field_b = r0
    //     0x7cac50: stur            w0, [x3, #0xb]
    // 0x7cac54: ldur            x4, [fp, #-0x18]
    // 0x7cac58: LoadField: r2 = r4->field_7
    //     0x7cac58: ldur            w2, [x4, #7]
    // 0x7cac5c: DecompressPointer r2
    //     0x7cac5c: add             x2, x2, HEAP, lsl #32
    // 0x7cac60: mov             x0, x3
    // 0x7cac64: r1 = Null
    //     0x7cac64: mov             x1, NULL
    // 0x7cac68: cmp             w2, NULL
    // 0x7cac6c: b.eq            #0x7cac90
    // 0x7cac70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cac70: ldur            w4, [x2, #0x17]
    // 0x7cac74: DecompressPointer r4
    //     0x7cac74: add             x4, x4, HEAP, lsl #32
    // 0x7cac78: r8 = X0 bound PdfDataType
    //     0x7cac78: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cac7c: ldr             x8, [x8, #0x6d8]
    // 0x7cac80: LoadField: r9 = r4->field_7
    //     0x7cac80: ldur            x9, [x4, #7]
    // 0x7cac84: r3 = Null
    //     0x7cac84: add             x3, PP, #0x47, lsl #12  ; [pp+0x47030] Null
    //     0x7cac88: ldr             x3, [x3, #0x30]
    // 0x7cac8c: blr             x9
    // 0x7cac90: ldur            x0, [fp, #-0x18]
    // 0x7cac94: LoadField: r1 = r0->field_b
    //     0x7cac94: ldur            w1, [x0, #0xb]
    // 0x7cac98: DecompressPointer r1
    //     0x7cac98: add             x1, x1, HEAP, lsl #32
    // 0x7cac9c: ldur            x3, [fp, #-0x30]
    // 0x7caca0: r2 = "/Group"
    //     0x7caca0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eee0] "/Group"
    //     0x7caca4: ldr             x2, [x2, #0xee0]
    // 0x7caca8: r0 = []=()
    //     0x7caca8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cacac: ldur            x1, [fp, #-0x20]
    // 0x7cacb0: r0 = graphicStates()
    //     0x7cacb0: bl              #0x7cb36c  ; [package:pdf/src/pdf/document.dart] PdfDocument::graphicStates
    // 0x7cacb4: mov             x1, x0
    // 0x7cacb8: r0 = ref()
    //     0x7cacb8: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cacbc: mov             x1, x0
    // 0x7cacc0: ldur            x0, [fp, #-0x10]
    // 0x7cacc4: LoadField: r2 = r0->field_b
    //     0x7cacc4: ldur            w2, [x0, #0xb]
    // 0x7cacc8: DecompressPointer r2
    //     0x7cacc8: add             x2, x2, HEAP, lsl #32
    // 0x7caccc: mov             x3, x1
    // 0x7cacd0: mov             x1, x2
    // 0x7cacd4: r2 = "/ExtGState"
    //     0x7cacd4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef20] "/ExtGState"
    //     0x7cacd8: ldr             x2, [x2, #0xf20]
    // 0x7cacdc: r0 = []=()
    //     0x7cacdc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cace0: ldur            x0, [fp, #-0x10]
    // 0x7cace4: LoadField: r1 = r0->field_b
    //     0x7cace4: ldur            w1, [x0, #0xb]
    // 0x7cace8: DecompressPointer r1
    //     0x7cace8: add             x1, x1, HEAP, lsl #32
    // 0x7cacec: LoadField: r2 = r1->field_13
    //     0x7cacec: ldur            w2, [x1, #0x13]
    // 0x7cacf0: r3 = LoadInt32Instr(r2)
    //     0x7cacf0: sbfx            x3, x2, #1, #0x1f
    // 0x7cacf4: asr             x2, x3, #1
    // 0x7cacf8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cacf8: ldur            w3, [x1, #0x17]
    // 0x7cacfc: r1 = LoadInt32Instr(r3)
    //     0x7cacfc: sbfx            x1, x3, #1, #0x1f
    // 0x7cad00: sub             x3, x2, x1
    // 0x7cad04: cbz             x3, #0x7cadc4
    // 0x7cad08: ldur            x1, [fp, #-8]
    // 0x7cad0c: LoadField: r3 = r1->field_1b
    //     0x7cad0c: ldur            w3, [x1, #0x1b]
    // 0x7cad10: DecompressPointer r3
    //     0x7cad10: add             x3, x3, HEAP, lsl #32
    // 0x7cad14: mov             x1, x3
    // 0x7cad18: stur            x3, [fp, #-0x18]
    // 0x7cad1c: r2 = "/Resources"
    //     0x7cad1c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cad20: ldr             x2, [x2, #0xf28]
    // 0x7cad24: r0 = contains()
    //     0x7cad24: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cad28: tbnz            w0, #4, #0x7cad6c
    // 0x7cad2c: ldur            x1, [fp, #-0x18]
    // 0x7cad30: r2 = "/Resources"
    //     0x7cad30: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cad34: ldr             x2, [x2, #0xf28]
    // 0x7cad38: r0 = getClipPath()
    //     0x7cad38: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7cad3c: r1 = LoadClassIdInstr(r0)
    //     0x7cad3c: ldur            x1, [x0, #-1]
    //     0x7cad40: ubfx            x1, x1, #0xc, #0x14
    // 0x7cad44: sub             x16, x1, #0x390
    // 0x7cad48: cmp             x16, #1
    // 0x7cad4c: b.hi            #0x7cad6c
    // 0x7cad50: mov             x1, x0
    // 0x7cad54: ldur            x2, [fp, #-0x10]
    // 0x7cad58: r0 = merge()
    //     0x7cad58: bl              #0x7caddc  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::merge
    // 0x7cad5c: r0 = Null
    //     0x7cad5c: mov             x0, NULL
    // 0x7cad60: LeaveFrame
    //     0x7cad60: mov             SP, fp
    //     0x7cad64: ldp             fp, lr, [SP], #0x10
    // 0x7cad68: ret
    //     0x7cad68: ret             
    // 0x7cad6c: ldur            x3, [fp, #-0x18]
    // 0x7cad70: LoadField: r2 = r3->field_7
    //     0x7cad70: ldur            w2, [x3, #7]
    // 0x7cad74: DecompressPointer r2
    //     0x7cad74: add             x2, x2, HEAP, lsl #32
    // 0x7cad78: ldur            x0, [fp, #-0x10]
    // 0x7cad7c: r1 = Null
    //     0x7cad7c: mov             x1, NULL
    // 0x7cad80: cmp             w2, NULL
    // 0x7cad84: b.eq            #0x7cada8
    // 0x7cad88: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cad88: ldur            w4, [x2, #0x17]
    // 0x7cad8c: DecompressPointer r4
    //     0x7cad8c: add             x4, x4, HEAP, lsl #32
    // 0x7cad90: r8 = X0 bound PdfDataType
    //     0x7cad90: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cad94: ldr             x8, [x8, #0x6d8]
    // 0x7cad98: LoadField: r9 = r4->field_7
    //     0x7cad98: ldur            x9, [x4, #7]
    // 0x7cad9c: r3 = Null
    //     0x7cad9c: add             x3, PP, #0x47, lsl #12  ; [pp+0x47040] Null
    //     0x7cada0: ldr             x3, [x3, #0x40]
    // 0x7cada4: blr             x9
    // 0x7cada8: ldur            x0, [fp, #-0x18]
    // 0x7cadac: LoadField: r1 = r0->field_b
    //     0x7cadac: ldur            w1, [x0, #0xb]
    // 0x7cadb0: DecompressPointer r1
    //     0x7cadb0: add             x1, x1, HEAP, lsl #32
    // 0x7cadb4: ldur            x3, [fp, #-0x10]
    // 0x7cadb8: r2 = "/Resources"
    //     0x7cadb8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cadbc: ldr             x2, [x2, #0xf28]
    // 0x7cadc0: r0 = []=()
    //     0x7cadc0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cadc4: r0 = Null
    //     0x7cadc4: mov             x0, NULL
    // 0x7cadc8: LeaveFrame
    //     0x7cadc8: mov             SP, fp
    //     0x7cadcc: ldp             fp, lr, [SP], #0x10
    // 0x7cadd0: ret
    //     0x7cadd0: ret             
    // 0x7cadd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cadd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cadd8: b               #0x7ca930
  }
  _ _PdfGraphicXObject&PdfXObject&PdfGraphicStream(/* No info */) {
    // ** addr: 0xe46f4c, size: 0x12c
    // 0xe46f4c: EnterFrame
    //     0xe46f4c: stp             fp, lr, [SP, #-0x10]!
    //     0xe46f50: mov             fp, SP
    // 0xe46f54: AllocStack(0x20)
    //     0xe46f54: sub             SP, SP, #0x20
    // 0xe46f58: r5 = false
    //     0xe46f58: add             x5, NULL, #0x30  ; false
    // 0xe46f5c: stur            x1, [fp, #-8]
    // 0xe46f60: stur            x2, [fp, #-0x10]
    // 0xe46f64: CheckStackOverflow
    //     0xe46f64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46f68: cmp             SP, x16
    //     0xe46f6c: b.ls            #0xe47070
    // 0xe46f70: StoreField: r1->field_33 = r5
    //     0xe46f70: stur            w5, [x1, #0x33]
    // 0xe46f74: StoreField: r1->field_37 = r5
    //     0xe46f74: stur            w5, [x1, #0x37]
    // 0xe46f78: StoreField: r1->field_4b = r5
    //     0xe46f78: stur            w5, [x1, #0x4b]
    // 0xe46f7c: r16 = <String, PdfFont>
    //     0xe46f7c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36868] TypeArguments: <String, PdfFont>
    //     0xe46f80: ldr             x16, [x16, #0x868]
    // 0xe46f84: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe46f88: stp             lr, x16, [SP]
    // 0xe46f8c: r0 = Map._fromLiteral()
    //     0xe46f8c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe46f90: ldur            x1, [fp, #-8]
    // 0xe46f94: StoreField: r1->field_3b = r0
    //     0xe46f94: stur            w0, [x1, #0x3b]
    //     0xe46f98: ldurb           w16, [x1, #-1]
    //     0xe46f9c: ldurb           w17, [x0, #-1]
    //     0xe46fa0: and             x16, x17, x16, lsr #2
    //     0xe46fa4: tst             x16, HEAP, lsr #32
    //     0xe46fa8: b.eq            #0xe46fb0
    //     0xe46fac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46fb0: r16 = <String, PdfShading>
    //     0xe46fb0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36870] TypeArguments: <String, PdfShading>
    //     0xe46fb4: ldr             x16, [x16, #0x870]
    // 0xe46fb8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe46fbc: stp             lr, x16, [SP]
    // 0xe46fc0: r0 = Map._fromLiteral()
    //     0xe46fc0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe46fc4: ldur            x1, [fp, #-8]
    // 0xe46fc8: StoreField: r1->field_3f = r0
    //     0xe46fc8: stur            w0, [x1, #0x3f]
    //     0xe46fcc: ldurb           w16, [x1, #-1]
    //     0xe46fd0: ldurb           w17, [x0, #-1]
    //     0xe46fd4: and             x16, x17, x16, lsr #2
    //     0xe46fd8: tst             x16, HEAP, lsr #32
    //     0xe46fdc: b.eq            #0xe46fe4
    //     0xe46fe0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46fe4: r16 = <String, PdfPattern>
    //     0xe46fe4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36878] TypeArguments: <String, PdfPattern>
    //     0xe46fe8: ldr             x16, [x16, #0x878]
    // 0xe46fec: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe46ff0: stp             lr, x16, [SP]
    // 0xe46ff4: r0 = Map._fromLiteral()
    //     0xe46ff4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe46ff8: ldur            x1, [fp, #-8]
    // 0xe46ffc: StoreField: r1->field_43 = r0
    //     0xe46ffc: stur            w0, [x1, #0x43]
    //     0xe47000: ldurb           w16, [x1, #-1]
    //     0xe47004: ldurb           w17, [x0, #-1]
    //     0xe47008: and             x16, x17, x16, lsr #2
    //     0xe4700c: tst             x16, HEAP, lsr #32
    //     0xe47010: b.eq            #0xe47018
    //     0xe47014: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe47018: r16 = <String, PdfXObject>
    //     0xe47018: add             x16, PP, #0x36, lsl #12  ; [pp+0x36880] TypeArguments: <String, PdfXObject>
    //     0xe4701c: ldr             x16, [x16, #0x880]
    // 0xe47020: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe47024: stp             lr, x16, [SP]
    // 0xe47028: r0 = Map._fromLiteral()
    //     0xe47028: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe4702c: ldur            x1, [fp, #-8]
    // 0xe47030: StoreField: r1->field_47 = r0
    //     0xe47030: stur            w0, [x1, #0x47]
    //     0xe47034: ldurb           w16, [x1, #-1]
    //     0xe47038: ldurb           w17, [x0, #-1]
    //     0xe4703c: and             x16, x17, x16, lsr #2
    //     0xe47040: tst             x16, HEAP, lsr #32
    //     0xe47044: b.eq            #0xe4704c
    //     0xe47048: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe4704c: ldur            x2, [fp, #-0x10]
    // 0xe47050: r3 = "/Form"
    //     0xe47050: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea50] "/Form"
    //     0xe47054: ldr             x3, [x3, #0xa50]
    // 0xe47058: r5 = false
    //     0xe47058: add             x5, NULL, #0x30  ; false
    // 0xe4705c: r0 = PdfXObject()
    //     0xe4705c: bl              #0xe47078  ; [package:pdf/src/pdf/obj/xobject.dart] PdfXObject::PdfXObject
    // 0xe47060: r0 = Null
    //     0xe47060: mov             x0, NULL
    // 0xe47064: LeaveFrame
    //     0xe47064: mov             SP, fp
    //     0xe47068: ldp             fp, lr, [SP], #0x10
    // 0xe4706c: ret
    //     0xe4706c: ret             
    // 0xe47070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47070: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47074: b               #0xe46f70
  }
  get _ altered(/* No info */) {
    // ** addr: 0xea0d78, size: 0xc
    // 0xea0d78: LoadField: r0 = r1->field_4b
    //     0xea0d78: ldur            w0, [x1, #0x4b]
    // 0xea0d7c: DecompressPointer r0
    //     0xea0d7c: add             x0, x0, HEAP, lsl #32
    // 0xea0d80: ret
    //     0xea0d80: ret             
  }
  _ addFont(/* No info */) {
    // ** addr: 0xea0d84, size: 0xd8
    // 0xea0d84: EnterFrame
    //     0xea0d84: stp             fp, lr, [SP, #-0x10]!
    //     0xea0d88: mov             fp, SP
    // 0xea0d8c: AllocStack(0x20)
    //     0xea0d8c: sub             SP, SP, #0x20
    // 0xea0d90: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea0d90: mov             x3, x2
    //     0xea0d94: stur            x2, [fp, #-0x10]
    // 0xea0d98: CheckStackOverflow
    //     0xea0d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0d9c: cmp             SP, x16
    //     0xea0da0: b.ls            #0xea0e54
    // 0xea0da4: LoadField: r0 = r1->field_3b
    //     0xea0da4: ldur            w0, [x1, #0x3b]
    // 0xea0da8: DecompressPointer r0
    //     0xea0da8: add             x0, x0, HEAP, lsl #32
    // 0xea0dac: stur            x0, [fp, #-8]
    // 0xea0db0: r1 = Null
    //     0xea0db0: mov             x1, NULL
    // 0xea0db4: r2 = 4
    //     0xea0db4: movz            x2, #0x4
    // 0xea0db8: r0 = AllocateArray()
    //     0xea0db8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0dbc: mov             x2, x0
    // 0xea0dc0: r16 = "/F"
    //     0xea0dc0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0xea0dc4: ldr             x16, [x16, #0xda0]
    // 0xea0dc8: StoreField: r2->field_f = r16
    //     0xea0dc8: stur            w16, [x2, #0xf]
    // 0xea0dcc: ldur            x3, [fp, #-0x10]
    // 0xea0dd0: LoadField: r4 = r3->field_b
    //     0xea0dd0: ldur            x4, [x3, #0xb]
    // 0xea0dd4: r0 = BoxInt64Instr(r4)
    //     0xea0dd4: sbfiz           x0, x4, #1, #0x1f
    //     0xea0dd8: cmp             x4, x0, asr #1
    //     0xea0ddc: b.eq            #0xea0de8
    //     0xea0de0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0de4: stur            x4, [x0, #7]
    // 0xea0de8: stur            x0, [fp, #-0x18]
    // 0xea0dec: StoreField: r2->field_13 = r0
    //     0xea0dec: stur            w0, [x2, #0x13]
    // 0xea0df0: str             x2, [SP]
    // 0xea0df4: r0 = _interpolate()
    //     0xea0df4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0df8: ldur            x1, [fp, #-8]
    // 0xea0dfc: mov             x2, x0
    // 0xea0e00: r0 = containsKey()
    //     0xea0e00: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea0e04: tbz             w0, #4, #0xea0e44
    // 0xea0e08: ldur            x0, [fp, #-0x18]
    // 0xea0e0c: r1 = Null
    //     0xea0e0c: mov             x1, NULL
    // 0xea0e10: r2 = 4
    //     0xea0e10: movz            x2, #0x4
    // 0xea0e14: r0 = AllocateArray()
    //     0xea0e14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0e18: r16 = "/F"
    //     0xea0e18: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0xea0e1c: ldr             x16, [x16, #0xda0]
    // 0xea0e20: StoreField: r0->field_f = r16
    //     0xea0e20: stur            w16, [x0, #0xf]
    // 0xea0e24: ldur            x1, [fp, #-0x18]
    // 0xea0e28: StoreField: r0->field_13 = r1
    //     0xea0e28: stur            w1, [x0, #0x13]
    // 0xea0e2c: str             x0, [SP]
    // 0xea0e30: r0 = _interpolate()
    //     0xea0e30: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0e34: ldur            x1, [fp, #-8]
    // 0xea0e38: mov             x2, x0
    // 0xea0e3c: ldur            x3, [fp, #-0x10]
    // 0xea0e40: r0 = []=()
    //     0xea0e40: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea0e44: r0 = Null
    //     0xea0e44: mov             x0, NULL
    // 0xea0e48: LeaveFrame
    //     0xea0e48: mov             SP, fp
    //     0xea0e4c: ldp             fp, lr, [SP], #0x10
    // 0xea0e50: ret
    //     0xea0e50: ret             
    // 0xea0e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0e54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0e58: b               #0xea0da4
  }
  _ addXObject(/* No info */) {
    // ** addr: 0xea0e5c, size: 0xd8
    // 0xea0e5c: EnterFrame
    //     0xea0e5c: stp             fp, lr, [SP, #-0x10]!
    //     0xea0e60: mov             fp, SP
    // 0xea0e64: AllocStack(0x20)
    //     0xea0e64: sub             SP, SP, #0x20
    // 0xea0e68: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea0e68: mov             x3, x2
    //     0xea0e6c: stur            x2, [fp, #-0x10]
    // 0xea0e70: CheckStackOverflow
    //     0xea0e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0e74: cmp             SP, x16
    //     0xea0e78: b.ls            #0xea0f2c
    // 0xea0e7c: LoadField: r0 = r1->field_47
    //     0xea0e7c: ldur            w0, [x1, #0x47]
    // 0xea0e80: DecompressPointer r0
    //     0xea0e80: add             x0, x0, HEAP, lsl #32
    // 0xea0e84: stur            x0, [fp, #-8]
    // 0xea0e88: r1 = Null
    //     0xea0e88: mov             x1, NULL
    // 0xea0e8c: r2 = 4
    //     0xea0e8c: movz            x2, #0x4
    // 0xea0e90: r0 = AllocateArray()
    //     0xea0e90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0e94: mov             x2, x0
    // 0xea0e98: r16 = "/I"
    //     0xea0e98: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xea0e9c: ldr             x16, [x16, #0xbe0]
    // 0xea0ea0: StoreField: r2->field_f = r16
    //     0xea0ea0: stur            w16, [x2, #0xf]
    // 0xea0ea4: ldur            x3, [fp, #-0x10]
    // 0xea0ea8: LoadField: r4 = r3->field_b
    //     0xea0ea8: ldur            x4, [x3, #0xb]
    // 0xea0eac: r0 = BoxInt64Instr(r4)
    //     0xea0eac: sbfiz           x0, x4, #1, #0x1f
    //     0xea0eb0: cmp             x4, x0, asr #1
    //     0xea0eb4: b.eq            #0xea0ec0
    //     0xea0eb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0ebc: stur            x4, [x0, #7]
    // 0xea0ec0: stur            x0, [fp, #-0x18]
    // 0xea0ec4: StoreField: r2->field_13 = r0
    //     0xea0ec4: stur            w0, [x2, #0x13]
    // 0xea0ec8: str             x2, [SP]
    // 0xea0ecc: r0 = _interpolate()
    //     0xea0ecc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0ed0: ldur            x1, [fp, #-8]
    // 0xea0ed4: mov             x2, x0
    // 0xea0ed8: r0 = containsKey()
    //     0xea0ed8: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea0edc: tbz             w0, #4, #0xea0f1c
    // 0xea0ee0: ldur            x0, [fp, #-0x18]
    // 0xea0ee4: r1 = Null
    //     0xea0ee4: mov             x1, NULL
    // 0xea0ee8: r2 = 4
    //     0xea0ee8: movz            x2, #0x4
    // 0xea0eec: r0 = AllocateArray()
    //     0xea0eec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0ef0: r16 = "/I"
    //     0xea0ef0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xea0ef4: ldr             x16, [x16, #0xbe0]
    // 0xea0ef8: StoreField: r0->field_f = r16
    //     0xea0ef8: stur            w16, [x0, #0xf]
    // 0xea0efc: ldur            x1, [fp, #-0x18]
    // 0xea0f00: StoreField: r0->field_13 = r1
    //     0xea0f00: stur            w1, [x0, #0x13]
    // 0xea0f04: str             x0, [SP]
    // 0xea0f08: r0 = _interpolate()
    //     0xea0f08: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0f0c: ldur            x1, [fp, #-8]
    // 0xea0f10: mov             x2, x0
    // 0xea0f14: ldur            x3, [fp, #-0x10]
    // 0xea0f18: r0 = []=()
    //     0xea0f18: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea0f1c: r0 = Null
    //     0xea0f1c: mov             x0, NULL
    // 0xea0f20: LeaveFrame
    //     0xea0f20: mov             SP, fp
    //     0xea0f24: ldp             fp, lr, [SP], #0x10
    // 0xea0f28: ret
    //     0xea0f28: ret             
    // 0xea0f2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0f2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0f30: b               #0xea0e7c
  }
  _ addPattern(/* No info */) {
    // ** addr: 0xea0f34, size: 0xd8
    // 0xea0f34: EnterFrame
    //     0xea0f34: stp             fp, lr, [SP, #-0x10]!
    //     0xea0f38: mov             fp, SP
    // 0xea0f3c: AllocStack(0x20)
    //     0xea0f3c: sub             SP, SP, #0x20
    // 0xea0f40: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea0f40: mov             x3, x2
    //     0xea0f44: stur            x2, [fp, #-0x10]
    // 0xea0f48: CheckStackOverflow
    //     0xea0f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0f4c: cmp             SP, x16
    //     0xea0f50: b.ls            #0xea1004
    // 0xea0f54: LoadField: r0 = r1->field_43
    //     0xea0f54: ldur            w0, [x1, #0x43]
    // 0xea0f58: DecompressPointer r0
    //     0xea0f58: add             x0, x0, HEAP, lsl #32
    // 0xea0f5c: stur            x0, [fp, #-8]
    // 0xea0f60: r1 = Null
    //     0xea0f60: mov             x1, NULL
    // 0xea0f64: r2 = 4
    //     0xea0f64: movz            x2, #0x4
    // 0xea0f68: r0 = AllocateArray()
    //     0xea0f68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0f6c: mov             x2, x0
    // 0xea0f70: r16 = "/P"
    //     0xea0f70: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea0f74: ldr             x16, [x16, #0x248]
    // 0xea0f78: StoreField: r2->field_f = r16
    //     0xea0f78: stur            w16, [x2, #0xf]
    // 0xea0f7c: ldur            x3, [fp, #-0x10]
    // 0xea0f80: LoadField: r4 = r3->field_b
    //     0xea0f80: ldur            x4, [x3, #0xb]
    // 0xea0f84: r0 = BoxInt64Instr(r4)
    //     0xea0f84: sbfiz           x0, x4, #1, #0x1f
    //     0xea0f88: cmp             x4, x0, asr #1
    //     0xea0f8c: b.eq            #0xea0f98
    //     0xea0f90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0f94: stur            x4, [x0, #7]
    // 0xea0f98: stur            x0, [fp, #-0x18]
    // 0xea0f9c: StoreField: r2->field_13 = r0
    //     0xea0f9c: stur            w0, [x2, #0x13]
    // 0xea0fa0: str             x2, [SP]
    // 0xea0fa4: r0 = _interpolate()
    //     0xea0fa4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0fa8: ldur            x1, [fp, #-8]
    // 0xea0fac: mov             x2, x0
    // 0xea0fb0: r0 = containsKey()
    //     0xea0fb0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea0fb4: tbz             w0, #4, #0xea0ff4
    // 0xea0fb8: ldur            x0, [fp, #-0x18]
    // 0xea0fbc: r1 = Null
    //     0xea0fbc: mov             x1, NULL
    // 0xea0fc0: r2 = 4
    //     0xea0fc0: movz            x2, #0x4
    // 0xea0fc4: r0 = AllocateArray()
    //     0xea0fc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea0fc8: r16 = "/P"
    //     0xea0fc8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea0fcc: ldr             x16, [x16, #0x248]
    // 0xea0fd0: StoreField: r0->field_f = r16
    //     0xea0fd0: stur            w16, [x0, #0xf]
    // 0xea0fd4: ldur            x1, [fp, #-0x18]
    // 0xea0fd8: StoreField: r0->field_13 = r1
    //     0xea0fd8: stur            w1, [x0, #0x13]
    // 0xea0fdc: str             x0, [SP]
    // 0xea0fe0: r0 = _interpolate()
    //     0xea0fe0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea0fe4: ldur            x1, [fp, #-8]
    // 0xea0fe8: mov             x2, x0
    // 0xea0fec: ldur            x3, [fp, #-0x10]
    // 0xea0ff0: r0 = []=()
    //     0xea0ff0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea0ff4: r0 = Null
    //     0xea0ff4: mov             x0, NULL
    // 0xea0ff8: LeaveFrame
    //     0xea0ff8: mov             SP, fp
    //     0xea0ffc: ldp             fp, lr, [SP], #0x10
    // 0xea1000: ret
    //     0xea1000: ret             
    // 0xea1004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1008: b               #0xea0f54
  }
  set _ altered=(/* No info */) {
    // ** addr: 0xea1084, size: 0xc
    // 0xea1084: r0 = true
    //     0xea1084: add             x0, NULL, #0x20  ; true
    // 0xea1088: StoreField: r1->field_4b = r0
    //     0xea1088: stur            w0, [x1, #0x4b]
    // 0xea108c: ret
    //     0xea108c: ret             
  }
}

// class id: 889, size: 0x50, field offset: 0x50
class PdfGraphicXObject extends _PdfGraphicXObject&PdfXObject&PdfGraphicStream {
}
