// lib: , url: package:pdf/src/pdf/obj/image.dart

// class id: 1050803, size: 0x8
class :: {
}

// class id: 887, size: 0x48, field offset: 0x34
class PdfImage extends PdfXObject {

  get _ name(/* No info */) {
    // ** addr: 0xe48ed8, size: 0x78
    // 0xe48ed8: EnterFrame
    //     0xe48ed8: stp             fp, lr, [SP, #-0x10]!
    //     0xe48edc: mov             fp, SP
    // 0xe48ee0: AllocStack(0x10)
    //     0xe48ee0: sub             SP, SP, #0x10
    // 0xe48ee4: SetupParameters(PdfImage this /* r1 => r0, fp-0x8 */)
    //     0xe48ee4: mov             x0, x1
    //     0xe48ee8: stur            x1, [fp, #-8]
    // 0xe48eec: CheckStackOverflow
    //     0xe48eec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48ef0: cmp             SP, x16
    //     0xe48ef4: b.ls            #0xe48f48
    // 0xe48ef8: r1 = Null
    //     0xe48ef8: mov             x1, NULL
    // 0xe48efc: r2 = 4
    //     0xe48efc: movz            x2, #0x4
    // 0xe48f00: r0 = AllocateArray()
    //     0xe48f00: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe48f04: mov             x2, x0
    // 0xe48f08: r16 = "/I"
    //     0xe48f08: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xe48f0c: ldr             x16, [x16, #0xbe0]
    // 0xe48f10: StoreField: r2->field_f = r16
    //     0xe48f10: stur            w16, [x2, #0xf]
    // 0xe48f14: ldur            x0, [fp, #-8]
    // 0xe48f18: LoadField: r3 = r0->field_b
    //     0xe48f18: ldur            x3, [x0, #0xb]
    // 0xe48f1c: r0 = BoxInt64Instr(r3)
    //     0xe48f1c: sbfiz           x0, x3, #1, #0x1f
    //     0xe48f20: cmp             x3, x0, asr #1
    //     0xe48f24: b.eq            #0xe48f30
    //     0xe48f28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe48f2c: stur            x3, [x0, #7]
    // 0xe48f30: StoreField: r2->field_13 = r0
    //     0xe48f30: stur            w0, [x2, #0x13]
    // 0xe48f34: str             x2, [SP]
    // 0xe48f38: r0 = _interpolate()
    //     0xe48f38: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe48f3c: LeaveFrame
    //     0xe48f3c: mov             SP, fp
    //     0xe48f40: ldp             fp, lr, [SP], #0x10
    // 0xe48f44: ret
    //     0xe48f44: ret             
    // 0xe48f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48f4c: b               #0xe48ef8
  }
  get _ width(/* No info */) {
    // ** addr: 0xe48f70, size: 0x2c
    // 0xe48f70: LoadField: r2 = r1->field_43
    //     0xe48f70: ldur            w2, [x1, #0x43]
    // 0xe48f74: DecompressPointer r2
    //     0xe48f74: add             x2, x2, HEAP, lsl #32
    // 0xe48f78: LoadField: r3 = r2->field_7
    //     0xe48f78: ldur            x3, [x2, #7]
    // 0xe48f7c: cmp             x3, #4
    // 0xe48f80: b.lt            #0xe48f90
    // 0xe48f84: LoadField: r2 = r1->field_3b
    //     0xe48f84: ldur            x2, [x1, #0x3b]
    // 0xe48f88: mov             x0, x2
    // 0xe48f8c: b               #0xe48f98
    // 0xe48f90: LoadField: r2 = r1->field_33
    //     0xe48f90: ldur            x2, [x1, #0x33]
    // 0xe48f94: mov             x0, x2
    // 0xe48f98: ret
    //     0xe48f98: ret             
  }
  factory _ PdfImage.file(/* No info */) {
    // ** addr: 0xe6942c, size: 0xa4
    // 0xe6942c: EnterFrame
    //     0xe6942c: stp             fp, lr, [SP, #-0x10]!
    //     0xe69430: mov             fp, SP
    // 0xe69434: AllocStack(0x10)
    //     0xe69434: sub             SP, SP, #0x10
    // 0xe69438: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xe69438: mov             x0, x2
    //     0xe6943c: stur            x2, [fp, #-8]
    //     0xe69440: mov             x2, x3
    //     0xe69444: stur            x3, [fp, #-0x10]
    // 0xe69448: CheckStackOverflow
    //     0xe69448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6944c: cmp             SP, x16
    //     0xe69450: b.ls            #0xe694c8
    // 0xe69454: r0 = JpegDecoder()
    //     0xe69454: bl              #0xca55cc  ; AllocateJpegDecoderStub -> JpegDecoder (size=0x8)
    // 0xe69458: mov             x1, x0
    // 0xe6945c: ldur            x2, [fp, #-0x10]
    // 0xe69460: r0 = isValidFile()
    //     0xe69460: bl              #0xe6d174  ; [package:image/src/formats/jpeg_decoder.dart] JpegDecoder::isValidFile
    // 0xe69464: tbnz            w0, #4, #0xe69488
    // 0xe69468: ldur            x2, [fp, #-8]
    // 0xe6946c: ldur            x3, [fp, #-0x10]
    // 0xe69470: r1 = <PdfDict<PdfDataType>>
    //     0xe69470: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe69474: ldr             x1, [x1, #0x758]
    // 0xe69478: r0 = PdfImage.jpeg()
    //     0xe69478: bl              #0xe6a7e0  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage.jpeg
    // 0xe6947c: LeaveFrame
    //     0xe6947c: mov             SP, fp
    //     0xe69480: ldp             fp, lr, [SP], #0x10
    // 0xe69484: ret
    //     0xe69484: ret             
    // 0xe69488: ldur            x1, [fp, #-0x10]
    // 0xe6948c: r0 = decodeImage()
    //     0xe6948c: bl              #0xe69e30  ; [package:image/src/formats/formats.dart] ::decodeImage
    // 0xe69490: cmp             w0, NULL
    // 0xe69494: b.eq            #0xe694b8
    // 0xe69498: ldur            x2, [fp, #-8]
    // 0xe6949c: mov             x3, x0
    // 0xe694a0: r1 = <PdfDict<PdfDataType>>
    //     0xe694a0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe694a4: ldr             x1, [x1, #0x758]
    // 0xe694a8: r0 = PdfImage.fromImage()
    //     0xe694a8: bl              #0xe694d0  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage.fromImage
    // 0xe694ac: LeaveFrame
    //     0xe694ac: mov             SP, fp
    //     0xe694b0: ldp             fp, lr, [SP], #0x10
    // 0xe694b4: ret
    //     0xe694b4: ret             
    // 0xe694b8: r0 = "Unable to decode image"
    //     0xe694b8: add             x0, PP, #0x47, lsl #12  ; [pp+0x47100] "Unable to decode image"
    //     0xe694bc: ldr             x0, [x0, #0x100]
    // 0xe694c0: r0 = Throw()
    //     0xe694c0: bl              #0xec04b8  ; ThrowStub
    // 0xe694c4: brk             #0
    // 0xe694c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe694c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe694cc: b               #0xe69454
  }
  factory _ PdfImage.fromImage(/* No info */) {
    // ** addr: 0xe694d0, size: 0x60
    // 0xe694d0: EnterFrame
    //     0xe694d0: stp             fp, lr, [SP, #-0x10]!
    //     0xe694d4: mov             fp, SP
    // 0xe694d8: AllocStack(0x8)
    //     0xe694d8: sub             SP, SP, #8
    // 0xe694dc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r2 */)
    //     0xe694dc: mov             x0, x2
    //     0xe694e0: stur            x2, [fp, #-8]
    //     0xe694e4: mov             x2, x3
    // 0xe694e8: CheckStackOverflow
    //     0xe694e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe694ec: cmp             SP, x16
    //     0xe694f0: b.ls            #0xe69528
    // 0xe694f4: r1 = Null
    //     0xe694f4: mov             x1, NULL
    // 0xe694f8: r0 = PdfRasterBase.fromImage()
    //     0xe694f8: bl              #0xe69cf4  ; [package:pdf/src/pdf/raster.dart] PdfRasterBase::PdfRasterBase.fromImage
    // 0xe694fc: LoadField: r5 = r0->field_1b
    //     0xe694fc: ldur            w5, [x0, #0x1b]
    // 0xe69500: DecompressPointer r5
    //     0xe69500: add             x5, x5, HEAP, lsl #32
    // 0xe69504: LoadField: r6 = r0->field_7
    //     0xe69504: ldur            x6, [x0, #7]
    // 0xe69508: LoadField: r3 = r0->field_f
    //     0xe69508: ldur            x3, [x0, #0xf]
    // 0xe6950c: ldur            x2, [fp, #-8]
    // 0xe69510: r1 = <PdfDict<PdfDataType>>
    //     0xe69510: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe69514: ldr             x1, [x1, #0x758]
    // 0xe69518: r0 = PdfImage()
    //     0xe69518: bl              #0xe69530  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage
    // 0xe6951c: LeaveFrame
    //     0xe6951c: mov             SP, fp
    //     0xe69520: ldp             fp, lr, [SP], #0x10
    // 0xe69524: ret
    //     0xe69524: ret             
    // 0xe69528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6952c: b               #0xe694f4
  }
  factory _ PdfImage(/* No info */) {
    // ** addr: 0xe69530, size: 0x380
    // 0xe69530: EnterFrame
    //     0xe69530: stp             fp, lr, [SP, #-0x10]!
    //     0xe69534: mov             fp, SP
    // 0xe69538: AllocStack(0x50)
    //     0xe69538: sub             SP, SP, #0x50
    // 0xe6953c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r0, fp-0x18 */, dynamic _ /* r6 => r3, fp-0x20 */)
    //     0xe6953c: mov             x0, x5
    //     0xe69540: stur            x5, [fp, #-0x18]
    //     0xe69544: mov             x5, x3
    //     0xe69548: stur            x3, [fp, #-0x10]
    //     0xe6954c: mov             x3, x6
    //     0xe69550: stur            x2, [fp, #-8]
    //     0xe69554: stur            x6, [fp, #-0x20]
    // 0xe69558: CheckStackOverflow
    //     0xe69558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6955c: cmp             SP, x16
    //     0xe69560: b.ls            #0xe69888
    // 0xe69564: r1 = <PdfDict<PdfDataType>>
    //     0xe69564: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe69568: ldr             x1, [x1, #0x758]
    // 0xe6956c: r0 = PdfImage()
    //     0xe6956c: bl              #0xe69ce8  ; AllocatePdfImageStub -> PdfImage (size=0x48)
    // 0xe69570: mov             x1, x0
    // 0xe69574: ldur            x2, [fp, #-8]
    // 0xe69578: ldur            x3, [fp, #-0x20]
    // 0xe6957c: ldur            x5, [fp, #-0x10]
    // 0xe69580: r6 = Instance_PdfImageOrientation
    //     0xe69580: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xe69584: ldr             x6, [x6, #0x588]
    // 0xe69588: stur            x0, [fp, #-0x28]
    // 0xe6958c: r0 = PdfImage._()
    //     0xe6958c: bl              #0xe69b44  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage._
    // 0xe69590: ldur            x3, [fp, #-0x28]
    // 0xe69594: LoadField: r4 = r3->field_1b
    //     0xe69594: ldur            w4, [x3, #0x1b]
    // 0xe69598: DecompressPointer r4
    //     0xe69598: add             x4, x4, HEAP, lsl #32
    // 0xe6959c: stur            x4, [fp, #-0x38]
    // 0xe695a0: LoadField: r5 = r4->field_7
    //     0xe695a0: ldur            w5, [x4, #7]
    // 0xe695a4: DecompressPointer r5
    //     0xe695a4: add             x5, x5, HEAP, lsl #32
    // 0xe695a8: mov             x2, x5
    // 0xe695ac: stur            x5, [fp, #-0x30]
    // 0xe695b0: r0 = Instance_PdfNum
    //     0xe695b0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe695b4: ldr             x0, [x0, #0xb60]
    // 0xe695b8: r1 = Null
    //     0xe695b8: mov             x1, NULL
    // 0xe695bc: cmp             w2, NULL
    // 0xe695c0: b.eq            #0xe695e4
    // 0xe695c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe695c4: ldur            w4, [x2, #0x17]
    // 0xe695c8: DecompressPointer r4
    //     0xe695c8: add             x4, x4, HEAP, lsl #32
    // 0xe695cc: r8 = X0 bound PdfDataType
    //     0xe695cc: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe695d0: ldr             x8, [x8, #0x6d8]
    // 0xe695d4: LoadField: r9 = r4->field_7
    //     0xe695d4: ldur            x9, [x4, #7]
    // 0xe695d8: r3 = Null
    //     0xe695d8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb68] Null
    //     0xe695dc: ldr             x3, [x3, #0xb68]
    // 0xe695e0: blr             x9
    // 0xe695e4: ldur            x0, [fp, #-0x38]
    // 0xe695e8: LoadField: r4 = r0->field_b
    //     0xe695e8: ldur            w4, [x0, #0xb]
    // 0xe695ec: DecompressPointer r4
    //     0xe695ec: add             x4, x4, HEAP, lsl #32
    // 0xe695f0: mov             x1, x4
    // 0xe695f4: stur            x4, [fp, #-0x40]
    // 0xe695f8: r2 = "/BitsPerComponent"
    //     0xe695f8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb78] "/BitsPerComponent"
    //     0xe695fc: ldr             x2, [x2, #0xb78]
    // 0xe69600: r3 = Instance_PdfNum
    //     0xe69600: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe69604: ldr             x3, [x3, #0xb60]
    // 0xe69608: r0 = []=()
    //     0xe69608: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6960c: ldur            x1, [fp, #-0x28]
    // 0xe69610: r0 = name()
    //     0xe69610: bl              #0xe48ed8  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::name
    // 0xe69614: stur            x0, [fp, #-0x38]
    // 0xe69618: r0 = PdfName()
    //     0xe69618: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe6961c: mov             x3, x0
    // 0xe69620: ldur            x0, [fp, #-0x38]
    // 0xe69624: stur            x3, [fp, #-0x48]
    // 0xe69628: StoreField: r3->field_7 = r0
    //     0xe69628: stur            w0, [x3, #7]
    // 0xe6962c: mov             x0, x3
    // 0xe69630: ldur            x2, [fp, #-0x30]
    // 0xe69634: r1 = Null
    //     0xe69634: mov             x1, NULL
    // 0xe69638: cmp             w2, NULL
    // 0xe6963c: b.eq            #0xe69660
    // 0xe69640: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69640: ldur            w4, [x2, #0x17]
    // 0xe69644: DecompressPointer r4
    //     0xe69644: add             x4, x4, HEAP, lsl #32
    // 0xe69648: r8 = X0 bound PdfDataType
    //     0xe69648: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6964c: ldr             x8, [x8, #0x6d8]
    // 0xe69650: LoadField: r9 = r4->field_7
    //     0xe69650: ldur            x9, [x4, #7]
    // 0xe69654: r3 = Null
    //     0xe69654: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb80] Null
    //     0xe69658: ldr             x3, [x3, #0xb80]
    // 0xe6965c: blr             x9
    // 0xe69660: ldur            x1, [fp, #-0x40]
    // 0xe69664: ldur            x3, [fp, #-0x48]
    // 0xe69668: r2 = "/Name"
    //     0xe69668: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb90] "/Name"
    //     0xe6966c: ldr             x2, [x2, #0xb90]
    // 0xe69670: r0 = []=()
    //     0xe69670: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69674: ldur            x2, [fp, #-0x30]
    // 0xe69678: r0 = Instance_PdfName
    //     0xe69678: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0xe6967c: ldr             x0, [x0, #0xb98]
    // 0xe69680: r1 = Null
    //     0xe69680: mov             x1, NULL
    // 0xe69684: cmp             w2, NULL
    // 0xe69688: b.eq            #0xe696ac
    // 0xe6968c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6968c: ldur            w4, [x2, #0x17]
    // 0xe69690: DecompressPointer r4
    //     0xe69690: add             x4, x4, HEAP, lsl #32
    // 0xe69694: r8 = X0 bound PdfDataType
    //     0xe69694: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69698: ldr             x8, [x8, #0x6d8]
    // 0xe6969c: LoadField: r9 = r4->field_7
    //     0xe6969c: ldur            x9, [x4, #7]
    // 0xe696a0: r3 = Null
    //     0xe696a0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eba0] Null
    //     0xe696a4: ldr             x3, [x3, #0xba0]
    // 0xe696a8: blr             x9
    // 0xe696ac: ldur            x1, [fp, #-0x40]
    // 0xe696b0: r2 = "/ColorSpace"
    //     0xe696b0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] "/ColorSpace"
    //     0xe696b4: ldr             x2, [x2, #0xbb0]
    // 0xe696b8: r3 = Instance_PdfName
    //     0xe696b8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0xe696bc: ldr             x3, [x3, #0xb98]
    // 0xe696c0: r0 = []=()
    //     0xe696c0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe696c4: ldur            x2, [fp, #-8]
    // 0xe696c8: ldur            x3, [fp, #-0x18]
    // 0xe696cc: ldur            x5, [fp, #-0x20]
    // 0xe696d0: ldur            x6, [fp, #-0x10]
    // 0xe696d4: r1 = <PdfDict<PdfDataType>>
    //     0xe696d4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe696d8: ldr             x1, [x1, #0x758]
    // 0xe696dc: r0 = PdfImage._alpha()
    //     0xe696dc: bl              #0xe698b0  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage._alpha
    // 0xe696e0: LoadField: r1 = r0->field_b
    //     0xe696e0: ldur            x1, [x0, #0xb]
    // 0xe696e4: stur            x1, [fp, #-0x50]
    // 0xe696e8: r0 = PdfIndirect()
    //     0xe696e8: bl              #0x7b5cc0  ; AllocatePdfIndirectStub -> PdfIndirect (size=0x18)
    // 0xe696ec: mov             x3, x0
    // 0xe696f0: ldur            x0, [fp, #-0x50]
    // 0xe696f4: stur            x3, [fp, #-8]
    // 0xe696f8: StoreField: r3->field_7 = r0
    //     0xe696f8: stur            x0, [x3, #7]
    // 0xe696fc: StoreField: r3->field_f = rZR
    //     0xe696fc: stur            xzr, [x3, #0xf]
    // 0xe69700: mov             x0, x3
    // 0xe69704: ldur            x2, [fp, #-0x30]
    // 0xe69708: r1 = Null
    //     0xe69708: mov             x1, NULL
    // 0xe6970c: cmp             w2, NULL
    // 0xe69710: b.eq            #0xe69734
    // 0xe69714: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69714: ldur            w4, [x2, #0x17]
    // 0xe69718: DecompressPointer r4
    //     0xe69718: add             x4, x4, HEAP, lsl #32
    // 0xe6971c: r8 = X0 bound PdfDataType
    //     0xe6971c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69720: ldr             x8, [x8, #0x6d8]
    // 0xe69724: LoadField: r9 = r4->field_7
    //     0xe69724: ldur            x9, [x4, #7]
    // 0xe69728: r3 = Null
    //     0xe69728: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebb8] Null
    //     0xe6972c: ldr             x3, [x3, #0xbb8]
    // 0xe69730: blr             x9
    // 0xe69734: ldur            x1, [fp, #-0x40]
    // 0xe69738: ldur            x3, [fp, #-8]
    // 0xe6973c: r2 = "/SMask"
    //     0xe6973c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebc8] "/SMask"
    //     0xe69740: ldr             x2, [x2, #0xbc8]
    // 0xe69744: r0 = []=()
    //     0xe69744: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69748: ldur            x1, [fp, #-0x10]
    // 0xe6974c: ldur            x0, [fp, #-0x20]
    // 0xe69750: mul             x2, x0, x1
    // 0xe69754: stur            x2, [fp, #-0x50]
    // 0xe69758: r16 = 3
    //     0xe69758: movz            x16, #0x3
    // 0xe6975c: mul             x3, x2, x16
    // 0xe69760: stur            x3, [fp, #-0x10]
    // 0xe69764: r0 = BoxInt64Instr(r3)
    //     0xe69764: sbfiz           x0, x3, #1, #0x1f
    //     0xe69768: cmp             x3, x0, asr #1
    //     0xe6976c: b.eq            #0xe69778
    //     0xe69770: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69774: stur            x3, [x0, #7]
    // 0xe69778: mov             x4, x0
    // 0xe6977c: r0 = AllocateUint8Array()
    //     0xe6977c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe69780: mov             x3, x0
    // 0xe69784: ldur            x2, [fp, #-0x18]
    // 0xe69788: LoadField: r0 = r2->field_13
    //     0xe69788: ldur            w0, [x2, #0x13]
    // 0xe6978c: r4 = LoadInt32Instr(r0)
    //     0xe6978c: sbfx            x4, x0, #1, #0x1f
    // 0xe69790: ldur            x5, [fp, #-0x50]
    // 0xe69794: r6 = 0
    //     0xe69794: movz            x6, #0
    // 0xe69798: CheckStackOverflow
    //     0xe69798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6979c: cmp             SP, x16
    //     0xe697a0: b.ls            #0xe69890
    // 0xe697a4: cmp             x6, x5
    // 0xe697a8: b.ge            #0xe69864
    // 0xe697ac: r16 = 3
    //     0xe697ac: movz            x16, #0x3
    // 0xe697b0: mul             x7, x6, x16
    // 0xe697b4: lsl             x8, x6, #2
    // 0xe697b8: mov             x0, x4
    // 0xe697bc: mov             x1, x8
    // 0xe697c0: cmp             x1, x0
    // 0xe697c4: b.hs            #0xe69898
    // 0xe697c8: LoadField: r0 = r2->field_7
    //     0xe697c8: ldur            x0, [x2, #7]
    // 0xe697cc: ldrb            w9, [x0, x8]
    // 0xe697d0: ldur            x0, [fp, #-0x10]
    // 0xe697d4: mov             x1, x7
    // 0xe697d8: cmp             x1, x0
    // 0xe697dc: b.hs            #0xe6989c
    // 0xe697e0: ArrayStore: r3[r7] = r9  ; TypeUnknown_1
    //     0xe697e0: add             x0, x3, x7
    //     0xe697e4: strb            w9, [x0, #0x17]
    // 0xe697e8: add             x9, x7, #1
    // 0xe697ec: add             x10, x8, #1
    // 0xe697f0: mov             x0, x4
    // 0xe697f4: mov             x1, x10
    // 0xe697f8: cmp             x1, x0
    // 0xe697fc: b.hs            #0xe698a0
    // 0xe69800: LoadField: r0 = r2->field_7
    //     0xe69800: ldur            x0, [x2, #7]
    // 0xe69804: ldrb            w11, [x0, x10]
    // 0xe69808: ldur            x0, [fp, #-0x10]
    // 0xe6980c: mov             x1, x9
    // 0xe69810: cmp             x1, x0
    // 0xe69814: b.hs            #0xe698a4
    // 0xe69818: ArrayStore: r3[r9] = r11  ; TypeUnknown_1
    //     0xe69818: add             x0, x3, x9
    //     0xe6981c: strb            w11, [x0, #0x17]
    // 0xe69820: add             x9, x7, #2
    // 0xe69824: add             x7, x8, #2
    // 0xe69828: mov             x0, x4
    // 0xe6982c: mov             x1, x7
    // 0xe69830: cmp             x1, x0
    // 0xe69834: b.hs            #0xe698a8
    // 0xe69838: LoadField: r0 = r2->field_7
    //     0xe69838: ldur            x0, [x2, #7]
    // 0xe6983c: ldrb            w8, [x0, x7]
    // 0xe69840: ldur            x0, [fp, #-0x10]
    // 0xe69844: mov             x1, x9
    // 0xe69848: cmp             x1, x0
    // 0xe6984c: b.hs            #0xe698ac
    // 0xe69850: ArrayStore: r3[r9] = r8  ; TypeUnknown_1
    //     0xe69850: add             x0, x3, x9
    //     0xe69854: strb            w8, [x0, #0x17]
    // 0xe69858: add             x0, x6, #1
    // 0xe6985c: mov             x6, x0
    // 0xe69860: b               #0xe69798
    // 0xe69864: ldur            x0, [fp, #-0x28]
    // 0xe69868: LoadField: r1 = r0->field_2b
    //     0xe69868: ldur            w1, [x0, #0x2b]
    // 0xe6986c: DecompressPointer r1
    //     0xe6986c: add             x1, x1, HEAP, lsl #32
    // 0xe69870: mov             x2, x3
    // 0xe69874: r0 = putBytes()
    //     0xe69874: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe69878: ldur            x0, [fp, #-0x28]
    // 0xe6987c: LeaveFrame
    //     0xe6987c: mov             SP, fp
    //     0xe69880: ldp             fp, lr, [SP], #0x10
    // 0xe69884: ret
    //     0xe69884: ret             
    // 0xe69888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6988c: b               #0xe69564
    // 0xe69890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69894: b               #0xe697a4
    // 0xe69898: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe69898: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6989c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6989c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe698a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe698a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe698a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe698a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe698a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe698a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe698ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe698ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  factory _ PdfImage._alpha(/* No info */) {
    // ** addr: 0xe698b0, size: 0x294
    // 0xe698b0: EnterFrame
    //     0xe698b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe698b4: mov             fp, SP
    // 0xe698b8: AllocStack(0x50)
    //     0xe698b8: sub             SP, SP, #0x50
    // 0xe698bc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */, dynamic _ /* r6 => r5, fp-0x20 */)
    //     0xe698bc: mov             x0, x3
    //     0xe698c0: stur            x3, [fp, #-0x10]
    //     0xe698c4: mov             x3, x5
    //     0xe698c8: stur            x5, [fp, #-0x18]
    //     0xe698cc: mov             x5, x6
    //     0xe698d0: stur            x2, [fp, #-8]
    //     0xe698d4: stur            x6, [fp, #-0x20]
    // 0xe698d8: CheckStackOverflow
    //     0xe698d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe698dc: cmp             SP, x16
    //     0xe698e0: b.ls            #0xe69b30
    // 0xe698e4: r1 = <PdfDict<PdfDataType>>
    //     0xe698e4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe698e8: ldr             x1, [x1, #0x758]
    // 0xe698ec: r0 = PdfImage()
    //     0xe698ec: bl              #0xe69ce8  ; AllocatePdfImageStub -> PdfImage (size=0x48)
    // 0xe698f0: mov             x1, x0
    // 0xe698f4: ldur            x2, [fp, #-8]
    // 0xe698f8: ldur            x3, [fp, #-0x18]
    // 0xe698fc: ldur            x5, [fp, #-0x20]
    // 0xe69900: r6 = Instance_PdfImageOrientation
    //     0xe69900: add             x6, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xe69904: ldr             x6, [x6, #0x588]
    // 0xe69908: stur            x0, [fp, #-8]
    // 0xe6990c: r0 = PdfImage._()
    //     0xe6990c: bl              #0xe69b44  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage._
    // 0xe69910: ldur            x3, [fp, #-8]
    // 0xe69914: LoadField: r4 = r3->field_1b
    //     0xe69914: ldur            w4, [x3, #0x1b]
    // 0xe69918: DecompressPointer r4
    //     0xe69918: add             x4, x4, HEAP, lsl #32
    // 0xe6991c: stur            x4, [fp, #-0x30]
    // 0xe69920: LoadField: r5 = r4->field_7
    //     0xe69920: ldur            w5, [x4, #7]
    // 0xe69924: DecompressPointer r5
    //     0xe69924: add             x5, x5, HEAP, lsl #32
    // 0xe69928: mov             x2, x5
    // 0xe6992c: stur            x5, [fp, #-0x28]
    // 0xe69930: r0 = Instance_PdfNum
    //     0xe69930: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe69934: ldr             x0, [x0, #0xb60]
    // 0xe69938: r1 = Null
    //     0xe69938: mov             x1, NULL
    // 0xe6993c: cmp             w2, NULL
    // 0xe69940: b.eq            #0xe69964
    // 0xe69944: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69944: ldur            w4, [x2, #0x17]
    // 0xe69948: DecompressPointer r4
    //     0xe69948: add             x4, x4, HEAP, lsl #32
    // 0xe6994c: r8 = X0 bound PdfDataType
    //     0xe6994c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69950: ldr             x8, [x8, #0x6d8]
    // 0xe69954: LoadField: r9 = r4->field_7
    //     0xe69954: ldur            x9, [x4, #7]
    // 0xe69958: r3 = Null
    //     0xe69958: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebd0] Null
    //     0xe6995c: ldr             x3, [x3, #0xbd0]
    // 0xe69960: blr             x9
    // 0xe69964: ldur            x0, [fp, #-0x30]
    // 0xe69968: LoadField: r4 = r0->field_b
    //     0xe69968: ldur            w4, [x0, #0xb]
    // 0xe6996c: DecompressPointer r4
    //     0xe6996c: add             x4, x4, HEAP, lsl #32
    // 0xe69970: mov             x1, x4
    // 0xe69974: stur            x4, [fp, #-0x38]
    // 0xe69978: r2 = "/BitsPerComponent"
    //     0xe69978: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb78] "/BitsPerComponent"
    //     0xe6997c: ldr             x2, [x2, #0xb78]
    // 0xe69980: r3 = Instance_PdfNum
    //     0xe69980: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe69984: ldr             x3, [x3, #0xb60]
    // 0xe69988: r0 = []=()
    //     0xe69988: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6998c: r1 = Null
    //     0xe6998c: mov             x1, NULL
    // 0xe69990: r2 = 4
    //     0xe69990: movz            x2, #0x4
    // 0xe69994: r0 = AllocateArray()
    //     0xe69994: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe69998: mov             x2, x0
    // 0xe6999c: r16 = "/I"
    //     0xe6999c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xe699a0: ldr             x16, [x16, #0xbe0]
    // 0xe699a4: StoreField: r2->field_f = r16
    //     0xe699a4: stur            w16, [x2, #0xf]
    // 0xe699a8: ldur            x3, [fp, #-8]
    // 0xe699ac: LoadField: r4 = r3->field_b
    //     0xe699ac: ldur            x4, [x3, #0xb]
    // 0xe699b0: r0 = BoxInt64Instr(r4)
    //     0xe699b0: sbfiz           x0, x4, #1, #0x1f
    //     0xe699b4: cmp             x4, x0, asr #1
    //     0xe699b8: b.eq            #0xe699c4
    //     0xe699bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe699c0: stur            x4, [x0, #7]
    // 0xe699c4: StoreField: r2->field_13 = r0
    //     0xe699c4: stur            w0, [x2, #0x13]
    // 0xe699c8: str             x2, [SP]
    // 0xe699cc: r0 = _interpolate()
    //     0xe699cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe699d0: stur            x0, [fp, #-0x30]
    // 0xe699d4: r0 = PdfName()
    //     0xe699d4: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe699d8: mov             x3, x0
    // 0xe699dc: ldur            x0, [fp, #-0x30]
    // 0xe699e0: stur            x3, [fp, #-0x40]
    // 0xe699e4: StoreField: r3->field_7 = r0
    //     0xe699e4: stur            w0, [x3, #7]
    // 0xe699e8: mov             x0, x3
    // 0xe699ec: ldur            x2, [fp, #-0x28]
    // 0xe699f0: r1 = Null
    //     0xe699f0: mov             x1, NULL
    // 0xe699f4: cmp             w2, NULL
    // 0xe699f8: b.eq            #0xe69a1c
    // 0xe699fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe699fc: ldur            w4, [x2, #0x17]
    // 0xe69a00: DecompressPointer r4
    //     0xe69a00: add             x4, x4, HEAP, lsl #32
    // 0xe69a04: r8 = X0 bound PdfDataType
    //     0xe69a04: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69a08: ldr             x8, [x8, #0x6d8]
    // 0xe69a0c: LoadField: r9 = r4->field_7
    //     0xe69a0c: ldur            x9, [x4, #7]
    // 0xe69a10: r3 = Null
    //     0xe69a10: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebe8] Null
    //     0xe69a14: ldr             x3, [x3, #0xbe8]
    // 0xe69a18: blr             x9
    // 0xe69a1c: ldur            x1, [fp, #-0x38]
    // 0xe69a20: ldur            x3, [fp, #-0x40]
    // 0xe69a24: r2 = "/Name"
    //     0xe69a24: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb90] "/Name"
    //     0xe69a28: ldr             x2, [x2, #0xb90]
    // 0xe69a2c: r0 = []=()
    //     0xe69a2c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69a30: ldur            x2, [fp, #-0x28]
    // 0xe69a34: r0 = Instance_PdfName
    //     0xe69a34: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ebf8] Obj!PdfName@e0c8c1
    //     0xe69a38: ldr             x0, [x0, #0xbf8]
    // 0xe69a3c: r1 = Null
    //     0xe69a3c: mov             x1, NULL
    // 0xe69a40: cmp             w2, NULL
    // 0xe69a44: b.eq            #0xe69a68
    // 0xe69a48: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69a48: ldur            w4, [x2, #0x17]
    // 0xe69a4c: DecompressPointer r4
    //     0xe69a4c: add             x4, x4, HEAP, lsl #32
    // 0xe69a50: r8 = X0 bound PdfDataType
    //     0xe69a50: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69a54: ldr             x8, [x8, #0x6d8]
    // 0xe69a58: LoadField: r9 = r4->field_7
    //     0xe69a58: ldur            x9, [x4, #7]
    // 0xe69a5c: r3 = Null
    //     0xe69a5c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ec00] Null
    //     0xe69a60: ldr             x3, [x3, #0xc00]
    // 0xe69a64: blr             x9
    // 0xe69a68: ldur            x1, [fp, #-0x38]
    // 0xe69a6c: r2 = "/ColorSpace"
    //     0xe69a6c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] "/ColorSpace"
    //     0xe69a70: ldr             x2, [x2, #0xbb0]
    // 0xe69a74: r3 = Instance_PdfName
    //     0xe69a74: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebf8] Obj!PdfName@e0c8c1
    //     0xe69a78: ldr             x3, [x3, #0xbf8]
    // 0xe69a7c: r0 = []=()
    //     0xe69a7c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69a80: ldur            x1, [fp, #-0x18]
    // 0xe69a84: ldur            x0, [fp, #-0x20]
    // 0xe69a88: mul             x2, x1, x0
    // 0xe69a8c: stur            x2, [fp, #-0x48]
    // 0xe69a90: r0 = BoxInt64Instr(r2)
    //     0xe69a90: sbfiz           x0, x2, #1, #0x1f
    //     0xe69a94: cmp             x2, x0, asr #1
    //     0xe69a98: b.eq            #0xe69aa4
    //     0xe69a9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69aa0: stur            x2, [x0, #7]
    // 0xe69aa4: mov             x4, x0
    // 0xe69aa8: r0 = AllocateUint8Array()
    //     0xe69aa8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe69aac: mov             x3, x0
    // 0xe69ab0: ldur            x2, [fp, #-0x10]
    // 0xe69ab4: LoadField: r0 = r2->field_13
    //     0xe69ab4: ldur            w0, [x2, #0x13]
    // 0xe69ab8: r4 = LoadInt32Instr(r0)
    //     0xe69ab8: sbfx            x4, x0, #1, #0x1f
    // 0xe69abc: ldur            x5, [fp, #-0x48]
    // 0xe69ac0: r6 = 0
    //     0xe69ac0: movz            x6, #0
    // 0xe69ac4: CheckStackOverflow
    //     0xe69ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe69ac8: cmp             SP, x16
    //     0xe69acc: b.ls            #0xe69b38
    // 0xe69ad0: cmp             x6, x5
    // 0xe69ad4: b.ge            #0xe69b0c
    // 0xe69ad8: lsl             x0, x6, #2
    // 0xe69adc: add             x7, x0, #3
    // 0xe69ae0: mov             x0, x4
    // 0xe69ae4: mov             x1, x7
    // 0xe69ae8: cmp             x1, x0
    // 0xe69aec: b.hs            #0xe69b40
    // 0xe69af0: LoadField: r0 = r2->field_7
    //     0xe69af0: ldur            x0, [x2, #7]
    // 0xe69af4: ldrb            w1, [x0, x7]
    // 0xe69af8: ArrayStore: r3[r6] = r1  ; TypeUnknown_1
    //     0xe69af8: add             x0, x3, x6
    //     0xe69afc: strb            w1, [x0, #0x17]
    // 0xe69b00: add             x0, x6, #1
    // 0xe69b04: mov             x6, x0
    // 0xe69b08: b               #0xe69ac4
    // 0xe69b0c: ldur            x0, [fp, #-8]
    // 0xe69b10: LoadField: r1 = r0->field_2b
    //     0xe69b10: ldur            w1, [x0, #0x2b]
    // 0xe69b14: DecompressPointer r1
    //     0xe69b14: add             x1, x1, HEAP, lsl #32
    // 0xe69b18: mov             x2, x3
    // 0xe69b1c: r0 = putBytes()
    //     0xe69b1c: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe69b20: ldur            x0, [fp, #-8]
    // 0xe69b24: LeaveFrame
    //     0xe69b24: mov             SP, fp
    //     0xe69b28: ldp             fp, lr, [SP], #0x10
    // 0xe69b2c: ret
    //     0xe69b2c: ret             
    // 0xe69b30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69b30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69b34: b               #0xe698e4
    // 0xe69b38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69b38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69b3c: b               #0xe69ad0
    // 0xe69b40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe69b40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ PdfImage._(/* No info */) {
    // ** addr: 0xe69b44, size: 0x1a4
    // 0xe69b44: EnterFrame
    //     0xe69b44: stp             fp, lr, [SP, #-0x10]!
    //     0xe69b48: mov             fp, SP
    // 0xe69b4c: AllocStack(0x30)
    //     0xe69b4c: sub             SP, SP, #0x30
    // 0xe69b50: SetupParameters(PdfImage this /* r1 => r7, fp-0x8 */, dynamic _ /* r3 => r6, fp-0x10 */, dynamic _ /* r5 => r4, fp-0x18 */, dynamic _ /* r6 => r0 */)
    //     0xe69b50: mov             x7, x1
    //     0xe69b54: mov             x0, x6
    //     0xe69b58: mov             x6, x3
    //     0xe69b5c: mov             x4, x5
    //     0xe69b60: stur            x1, [fp, #-8]
    //     0xe69b64: stur            x3, [fp, #-0x10]
    //     0xe69b68: stur            x5, [fp, #-0x18]
    // 0xe69b6c: CheckStackOverflow
    //     0xe69b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe69b70: cmp             SP, x16
    //     0xe69b74: b.ls            #0xe69ce0
    // 0xe69b78: StoreField: r7->field_33 = r6
    //     0xe69b78: stur            x6, [x7, #0x33]
    // 0xe69b7c: StoreField: r7->field_3b = r4
    //     0xe69b7c: stur            x4, [x7, #0x3b]
    // 0xe69b80: StoreField: r7->field_43 = r0
    //     0xe69b80: stur            w0, [x7, #0x43]
    //     0xe69b84: ldurb           w16, [x7, #-1]
    //     0xe69b88: ldurb           w17, [x0, #-1]
    //     0xe69b8c: and             x16, x17, x16, lsr #2
    //     0xe69b90: tst             x16, HEAP, lsr #32
    //     0xe69b94: b.eq            #0xe69b9c
    //     0xe69b98: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xe69b9c: mov             x1, x7
    // 0xe69ba0: r3 = "/Image"
    //     0xe69ba0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ec10] "/Image"
    //     0xe69ba4: ldr             x3, [x3, #0xc10]
    // 0xe69ba8: r5 = true
    //     0xe69ba8: add             x5, NULL, #0x20  ; true
    // 0xe69bac: r0 = PdfXObject()
    //     0xe69bac: bl              #0xe47078  ; [package:pdf/src/pdf/obj/xobject.dart] PdfXObject::PdfXObject
    // 0xe69bb0: ldur            x0, [fp, #-8]
    // 0xe69bb4: LoadField: r2 = r0->field_1b
    //     0xe69bb4: ldur            w2, [x0, #0x1b]
    // 0xe69bb8: DecompressPointer r2
    //     0xe69bb8: add             x2, x2, HEAP, lsl #32
    // 0xe69bbc: ldur            x3, [fp, #-0x10]
    // 0xe69bc0: stur            x2, [fp, #-0x20]
    // 0xe69bc4: r0 = BoxInt64Instr(r3)
    //     0xe69bc4: sbfiz           x0, x3, #1, #0x1f
    //     0xe69bc8: cmp             x3, x0, asr #1
    //     0xe69bcc: b.eq            #0xe69bd8
    //     0xe69bd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69bd4: stur            x3, [x0, #7]
    // 0xe69bd8: stur            x0, [fp, #-8]
    // 0xe69bdc: r0 = PdfNum()
    //     0xe69bdc: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe69be0: mov             x3, x0
    // 0xe69be4: ldur            x0, [fp, #-8]
    // 0xe69be8: stur            x3, [fp, #-0x28]
    // 0xe69bec: StoreField: r3->field_7 = r0
    //     0xe69bec: stur            w0, [x3, #7]
    // 0xe69bf0: ldur            x4, [fp, #-0x20]
    // 0xe69bf4: LoadField: r5 = r4->field_7
    //     0xe69bf4: ldur            w5, [x4, #7]
    // 0xe69bf8: DecompressPointer r5
    //     0xe69bf8: add             x5, x5, HEAP, lsl #32
    // 0xe69bfc: mov             x0, x3
    // 0xe69c00: mov             x2, x5
    // 0xe69c04: stur            x5, [fp, #-8]
    // 0xe69c08: r1 = Null
    //     0xe69c08: mov             x1, NULL
    // 0xe69c0c: cmp             w2, NULL
    // 0xe69c10: b.eq            #0xe69c34
    // 0xe69c14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69c14: ldur            w4, [x2, #0x17]
    // 0xe69c18: DecompressPointer r4
    //     0xe69c18: add             x4, x4, HEAP, lsl #32
    // 0xe69c1c: r8 = X0 bound PdfDataType
    //     0xe69c1c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69c20: ldr             x8, [x8, #0x6d8]
    // 0xe69c24: LoadField: r9 = r4->field_7
    //     0xe69c24: ldur            x9, [x4, #7]
    // 0xe69c28: r3 = Null
    //     0xe69c28: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ec18] Null
    //     0xe69c2c: ldr             x3, [x3, #0xc18]
    // 0xe69c30: blr             x9
    // 0xe69c34: ldur            x0, [fp, #-0x20]
    // 0xe69c38: LoadField: r4 = r0->field_b
    //     0xe69c38: ldur            w4, [x0, #0xb]
    // 0xe69c3c: DecompressPointer r4
    //     0xe69c3c: add             x4, x4, HEAP, lsl #32
    // 0xe69c40: mov             x1, x4
    // 0xe69c44: ldur            x3, [fp, #-0x28]
    // 0xe69c48: stur            x4, [fp, #-0x30]
    // 0xe69c4c: r2 = "/Width"
    //     0xe69c4c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ec28] "/Width"
    //     0xe69c50: ldr             x2, [x2, #0xc28]
    // 0xe69c54: r0 = []=()
    //     0xe69c54: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69c58: ldur            x2, [fp, #-0x18]
    // 0xe69c5c: r0 = BoxInt64Instr(r2)
    //     0xe69c5c: sbfiz           x0, x2, #1, #0x1f
    //     0xe69c60: cmp             x2, x0, asr #1
    //     0xe69c64: b.eq            #0xe69c70
    //     0xe69c68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69c6c: stur            x2, [x0, #7]
    // 0xe69c70: stur            x0, [fp, #-0x20]
    // 0xe69c74: r0 = PdfNum()
    //     0xe69c74: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe69c78: mov             x3, x0
    // 0xe69c7c: ldur            x0, [fp, #-0x20]
    // 0xe69c80: stur            x3, [fp, #-0x28]
    // 0xe69c84: StoreField: r3->field_7 = r0
    //     0xe69c84: stur            w0, [x3, #7]
    // 0xe69c88: mov             x0, x3
    // 0xe69c8c: ldur            x2, [fp, #-8]
    // 0xe69c90: r1 = Null
    //     0xe69c90: mov             x1, NULL
    // 0xe69c94: cmp             w2, NULL
    // 0xe69c98: b.eq            #0xe69cbc
    // 0xe69c9c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe69c9c: ldur            w4, [x2, #0x17]
    // 0xe69ca0: DecompressPointer r4
    //     0xe69ca0: add             x4, x4, HEAP, lsl #32
    // 0xe69ca4: r8 = X0 bound PdfDataType
    //     0xe69ca4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe69ca8: ldr             x8, [x8, #0x6d8]
    // 0xe69cac: LoadField: r9 = r4->field_7
    //     0xe69cac: ldur            x9, [x4, #7]
    // 0xe69cb0: r3 = Null
    //     0xe69cb0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ec30] Null
    //     0xe69cb4: ldr             x3, [x3, #0xc30]
    // 0xe69cb8: blr             x9
    // 0xe69cbc: ldur            x1, [fp, #-0x30]
    // 0xe69cc0: ldur            x3, [fp, #-0x28]
    // 0xe69cc4: r2 = "/Height"
    //     0xe69cc4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ec40] "/Height"
    //     0xe69cc8: ldr             x2, [x2, #0xc40]
    // 0xe69ccc: r0 = []=()
    //     0xe69ccc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69cd0: r0 = Null
    //     0xe69cd0: mov             x0, NULL
    // 0xe69cd4: LeaveFrame
    //     0xe69cd4: mov             SP, fp
    //     0xe69cd8: ldp             fp, lr, [SP], #0x10
    // 0xe69cdc: ret
    //     0xe69cdc: ret             
    // 0xe69ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69ce0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69ce4: b               #0xe69b78
  }
  factory _ PdfImage.jpeg(/* No info */) {
    // ** addr: 0xe6a7e0, size: 0x348
    // 0xe6a7e0: EnterFrame
    //     0xe6a7e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe6a7e4: mov             fp, SP
    // 0xe6a7e8: AllocStack(0x50)
    //     0xe6a7e8: sub             SP, SP, #0x50
    // 0xe6a7ec: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xe6a7ec: mov             x0, x3
    //     0xe6a7f0: stur            x3, [fp, #-0x10]
    //     0xe6a7f4: mov             x3, x2
    //     0xe6a7f8: stur            x2, [fp, #-8]
    // 0xe6a7fc: CheckStackOverflow
    //     0xe6a7fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6a800: cmp             SP, x16
    //     0xe6a804: b.ls            #0xe6ab1c
    // 0xe6a808: mov             x2, x0
    // 0xe6a80c: r1 = Null
    //     0xe6a80c: mov             x1, NULL
    // 0xe6a810: r0 = PdfJpegInfo()
    //     0xe6a810: bl              #0xe6ab28  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::PdfJpegInfo
    // 0xe6a814: stur            x0, [fp, #-0x28]
    // 0xe6a818: LoadField: r2 = r0->field_7
    //     0xe6a818: ldur            w2, [x0, #7]
    // 0xe6a81c: DecompressPointer r2
    //     0xe6a81c: add             x2, x2, HEAP, lsl #32
    // 0xe6a820: stur            x2, [fp, #-0x20]
    // 0xe6a824: cmp             w2, NULL
    // 0xe6a828: b.eq            #0xe6ab24
    // 0xe6a82c: LoadField: r5 = r0->field_b
    //     0xe6a82c: ldur            x5, [x0, #0xb]
    // 0xe6a830: mov             x1, x0
    // 0xe6a834: stur            x5, [fp, #-0x18]
    // 0xe6a838: r0 = orientation()
    //     0xe6a838: bl              #0xc341e4  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::orientation
    // 0xe6a83c: mov             x2, x0
    // 0xe6a840: ldur            x0, [fp, #-0x20]
    // 0xe6a844: stur            x2, [fp, #-0x38]
    // 0xe6a848: r3 = LoadInt32Instr(r0)
    //     0xe6a848: sbfx            x3, x0, #1, #0x1f
    //     0xe6a84c: tbz             w0, #0, #0xe6a854
    //     0xe6a850: ldur            x3, [x0, #7]
    // 0xe6a854: stur            x3, [fp, #-0x30]
    // 0xe6a858: r1 = <PdfDict<PdfDataType>>
    //     0xe6a858: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe6a85c: ldr             x1, [x1, #0x758]
    // 0xe6a860: r0 = PdfImage()
    //     0xe6a860: bl              #0xe69ce8  ; AllocatePdfImageStub -> PdfImage (size=0x48)
    // 0xe6a864: mov             x1, x0
    // 0xe6a868: ldur            x2, [fp, #-8]
    // 0xe6a86c: ldur            x3, [fp, #-0x30]
    // 0xe6a870: ldur            x5, [fp, #-0x18]
    // 0xe6a874: ldur            x6, [fp, #-0x38]
    // 0xe6a878: stur            x0, [fp, #-8]
    // 0xe6a87c: r0 = PdfImage._()
    //     0xe6a87c: bl              #0xe69b44  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage._
    // 0xe6a880: ldur            x3, [fp, #-8]
    // 0xe6a884: LoadField: r4 = r3->field_1b
    //     0xe6a884: ldur            w4, [x3, #0x1b]
    // 0xe6a888: DecompressPointer r4
    //     0xe6a888: add             x4, x4, HEAP, lsl #32
    // 0xe6a88c: stur            x4, [fp, #-0x38]
    // 0xe6a890: LoadField: r5 = r4->field_7
    //     0xe6a890: ldur            w5, [x4, #7]
    // 0xe6a894: DecompressPointer r5
    //     0xe6a894: add             x5, x5, HEAP, lsl #32
    // 0xe6a898: mov             x2, x5
    // 0xe6a89c: stur            x5, [fp, #-0x20]
    // 0xe6a8a0: r0 = Instance_PdfNum
    //     0xe6a8a0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe6a8a4: ldr             x0, [x0, #0xb60]
    // 0xe6a8a8: r1 = Null
    //     0xe6a8a8: mov             x1, NULL
    // 0xe6a8ac: cmp             w2, NULL
    // 0xe6a8b0: b.eq            #0xe6a8d4
    // 0xe6a8b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6a8b4: ldur            w4, [x2, #0x17]
    // 0xe6a8b8: DecompressPointer r4
    //     0xe6a8b8: add             x4, x4, HEAP, lsl #32
    // 0xe6a8bc: r8 = X0 bound PdfDataType
    //     0xe6a8bc: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6a8c0: ldr             x8, [x8, #0x6d8]
    // 0xe6a8c4: LoadField: r9 = r4->field_7
    //     0xe6a8c4: ldur            x9, [x4, #7]
    // 0xe6a8c8: r3 = Null
    //     0xe6a8c8: add             x3, PP, #0x47, lsl #12  ; [pp+0x47490] Null
    //     0xe6a8cc: ldr             x3, [x3, #0x490]
    // 0xe6a8d0: blr             x9
    // 0xe6a8d4: ldur            x0, [fp, #-0x38]
    // 0xe6a8d8: LoadField: r4 = r0->field_b
    //     0xe6a8d8: ldur            w4, [x0, #0xb]
    // 0xe6a8dc: DecompressPointer r4
    //     0xe6a8dc: add             x4, x4, HEAP, lsl #32
    // 0xe6a8e0: mov             x1, x4
    // 0xe6a8e4: stur            x4, [fp, #-0x40]
    // 0xe6a8e8: r2 = "/BitsPerComponent"
    //     0xe6a8e8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb78] "/BitsPerComponent"
    //     0xe6a8ec: ldr             x2, [x2, #0xb78]
    // 0xe6a8f0: r3 = Instance_PdfNum
    //     0xe6a8f0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb60] Obj!PdfNum@e0c7d1
    //     0xe6a8f4: ldr             x3, [x3, #0xb60]
    // 0xe6a8f8: r0 = []=()
    //     0xe6a8f8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6a8fc: r1 = Null
    //     0xe6a8fc: mov             x1, NULL
    // 0xe6a900: r2 = 4
    //     0xe6a900: movz            x2, #0x4
    // 0xe6a904: r0 = AllocateArray()
    //     0xe6a904: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6a908: mov             x2, x0
    // 0xe6a90c: r16 = "/I"
    //     0xe6a90c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xe6a910: ldr             x16, [x16, #0xbe0]
    // 0xe6a914: StoreField: r2->field_f = r16
    //     0xe6a914: stur            w16, [x2, #0xf]
    // 0xe6a918: ldur            x3, [fp, #-8]
    // 0xe6a91c: LoadField: r4 = r3->field_b
    //     0xe6a91c: ldur            x4, [x3, #0xb]
    // 0xe6a920: r0 = BoxInt64Instr(r4)
    //     0xe6a920: sbfiz           x0, x4, #1, #0x1f
    //     0xe6a924: cmp             x4, x0, asr #1
    //     0xe6a928: b.eq            #0xe6a934
    //     0xe6a92c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6a930: stur            x4, [x0, #7]
    // 0xe6a934: StoreField: r2->field_13 = r0
    //     0xe6a934: stur            w0, [x2, #0x13]
    // 0xe6a938: str             x2, [SP]
    // 0xe6a93c: r0 = _interpolate()
    //     0xe6a93c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe6a940: stur            x0, [fp, #-0x38]
    // 0xe6a944: r0 = PdfName()
    //     0xe6a944: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe6a948: mov             x3, x0
    // 0xe6a94c: ldur            x0, [fp, #-0x38]
    // 0xe6a950: stur            x3, [fp, #-0x48]
    // 0xe6a954: StoreField: r3->field_7 = r0
    //     0xe6a954: stur            w0, [x3, #7]
    // 0xe6a958: mov             x0, x3
    // 0xe6a95c: ldur            x2, [fp, #-0x20]
    // 0xe6a960: r1 = Null
    //     0xe6a960: mov             x1, NULL
    // 0xe6a964: cmp             w2, NULL
    // 0xe6a968: b.eq            #0xe6a98c
    // 0xe6a96c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6a96c: ldur            w4, [x2, #0x17]
    // 0xe6a970: DecompressPointer r4
    //     0xe6a970: add             x4, x4, HEAP, lsl #32
    // 0xe6a974: r8 = X0 bound PdfDataType
    //     0xe6a974: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6a978: ldr             x8, [x8, #0x6d8]
    // 0xe6a97c: LoadField: r9 = r4->field_7
    //     0xe6a97c: ldur            x9, [x4, #7]
    // 0xe6a980: r3 = Null
    //     0xe6a980: add             x3, PP, #0x47, lsl #12  ; [pp+0x474a0] Null
    //     0xe6a984: ldr             x3, [x3, #0x4a0]
    // 0xe6a988: blr             x9
    // 0xe6a98c: ldur            x1, [fp, #-0x40]
    // 0xe6a990: ldur            x3, [fp, #-0x48]
    // 0xe6a994: r2 = "/Name"
    //     0xe6a994: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb90] "/Name"
    //     0xe6a998: ldr             x2, [x2, #0xb90]
    // 0xe6a99c: r0 = []=()
    //     0xe6a99c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6a9a0: ldur            x2, [fp, #-0x20]
    // 0xe6a9a4: r0 = Instance_PdfName
    //     0xe6a9a4: add             x0, PP, #0x47, lsl #12  ; [pp+0x474b0] Obj!PdfName@e0c8e1
    //     0xe6a9a8: ldr             x0, [x0, #0x4b0]
    // 0xe6a9ac: r1 = Null
    //     0xe6a9ac: mov             x1, NULL
    // 0xe6a9b0: cmp             w2, NULL
    // 0xe6a9b4: b.eq            #0xe6a9d8
    // 0xe6a9b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6a9b8: ldur            w4, [x2, #0x17]
    // 0xe6a9bc: DecompressPointer r4
    //     0xe6a9bc: add             x4, x4, HEAP, lsl #32
    // 0xe6a9c0: r8 = X0 bound PdfDataType
    //     0xe6a9c0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6a9c4: ldr             x8, [x8, #0x6d8]
    // 0xe6a9c8: LoadField: r9 = r4->field_7
    //     0xe6a9c8: ldur            x9, [x4, #7]
    // 0xe6a9cc: r3 = Null
    //     0xe6a9cc: add             x3, PP, #0x47, lsl #12  ; [pp+0x474b8] Null
    //     0xe6a9d0: ldr             x3, [x3, #0x4b8]
    // 0xe6a9d4: blr             x9
    // 0xe6a9d8: ldur            x1, [fp, #-0x40]
    // 0xe6a9dc: r2 = "/Intent"
    //     0xe6a9dc: add             x2, PP, #0x47, lsl #12  ; [pp+0x474c8] "/Intent"
    //     0xe6a9e0: ldr             x2, [x2, #0x4c8]
    // 0xe6a9e4: r3 = Instance_PdfName
    //     0xe6a9e4: add             x3, PP, #0x47, lsl #12  ; [pp+0x474b0] Obj!PdfName@e0c8e1
    //     0xe6a9e8: ldr             x3, [x3, #0x4b0]
    // 0xe6a9ec: r0 = []=()
    //     0xe6a9ec: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6a9f0: ldur            x2, [fp, #-0x20]
    // 0xe6a9f4: r0 = Instance_PdfName
    //     0xe6a9f4: add             x0, PP, #0x47, lsl #12  ; [pp+0x474d0] Obj!PdfName@e0c8d1
    //     0xe6a9f8: ldr             x0, [x0, #0x4d0]
    // 0xe6a9fc: r1 = Null
    //     0xe6a9fc: mov             x1, NULL
    // 0xe6aa00: cmp             w2, NULL
    // 0xe6aa04: b.eq            #0xe6aa28
    // 0xe6aa08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6aa08: ldur            w4, [x2, #0x17]
    // 0xe6aa0c: DecompressPointer r4
    //     0xe6aa0c: add             x4, x4, HEAP, lsl #32
    // 0xe6aa10: r8 = X0 bound PdfDataType
    //     0xe6aa10: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6aa14: ldr             x8, [x8, #0x6d8]
    // 0xe6aa18: LoadField: r9 = r4->field_7
    //     0xe6aa18: ldur            x9, [x4, #7]
    // 0xe6aa1c: r3 = Null
    //     0xe6aa1c: add             x3, PP, #0x47, lsl #12  ; [pp+0x474d8] Null
    //     0xe6aa20: ldr             x3, [x3, #0x4d8]
    // 0xe6aa24: blr             x9
    // 0xe6aa28: ldur            x1, [fp, #-0x40]
    // 0xe6aa2c: r2 = "/Filter"
    //     0xe6aa2c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c020] "/Filter"
    //     0xe6aa30: ldr             x2, [x2, #0x20]
    // 0xe6aa34: r3 = Instance_PdfName
    //     0xe6aa34: add             x3, PP, #0x47, lsl #12  ; [pp+0x474d0] Obj!PdfName@e0c8d1
    //     0xe6aa38: ldr             x3, [x3, #0x4d0]
    // 0xe6aa3c: r0 = []=()
    //     0xe6aa3c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6aa40: ldur            x0, [fp, #-0x28]
    // 0xe6aa44: LoadField: r1 = r0->field_13
    //     0xe6aa44: ldur            w1, [x0, #0x13]
    // 0xe6aa48: DecompressPointer r1
    //     0xe6aa48: add             x1, x1, HEAP, lsl #32
    // 0xe6aa4c: cmp             w1, #6
    // 0xe6aa50: b.ne            #0xe6aaa8
    // 0xe6aa54: ldur            x2, [fp, #-0x20]
    // 0xe6aa58: r0 = Instance_PdfName
    //     0xe6aa58: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0xe6aa5c: ldr             x0, [x0, #0xb98]
    // 0xe6aa60: r1 = Null
    //     0xe6aa60: mov             x1, NULL
    // 0xe6aa64: cmp             w2, NULL
    // 0xe6aa68: b.eq            #0xe6aa8c
    // 0xe6aa6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6aa6c: ldur            w4, [x2, #0x17]
    // 0xe6aa70: DecompressPointer r4
    //     0xe6aa70: add             x4, x4, HEAP, lsl #32
    // 0xe6aa74: r8 = X0 bound PdfDataType
    //     0xe6aa74: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6aa78: ldr             x8, [x8, #0x6d8]
    // 0xe6aa7c: LoadField: r9 = r4->field_7
    //     0xe6aa7c: ldur            x9, [x4, #7]
    // 0xe6aa80: r3 = Null
    //     0xe6aa80: add             x3, PP, #0x47, lsl #12  ; [pp+0x474e8] Null
    //     0xe6aa84: ldr             x3, [x3, #0x4e8]
    // 0xe6aa88: blr             x9
    // 0xe6aa8c: ldur            x1, [fp, #-0x40]
    // 0xe6aa90: r2 = "/ColorSpace"
    //     0xe6aa90: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] "/ColorSpace"
    //     0xe6aa94: ldr             x2, [x2, #0xbb0]
    // 0xe6aa98: r3 = Instance_PdfName
    //     0xe6aa98: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0xe6aa9c: ldr             x3, [x3, #0xb98]
    // 0xe6aaa0: r0 = []=()
    //     0xe6aaa0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6aaa4: b               #0xe6aaf8
    // 0xe6aaa8: ldur            x2, [fp, #-0x20]
    // 0xe6aaac: r0 = Instance_PdfName
    //     0xe6aaac: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ebf8] Obj!PdfName@e0c8c1
    //     0xe6aab0: ldr             x0, [x0, #0xbf8]
    // 0xe6aab4: r1 = Null
    //     0xe6aab4: mov             x1, NULL
    // 0xe6aab8: cmp             w2, NULL
    // 0xe6aabc: b.eq            #0xe6aae0
    // 0xe6aac0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6aac0: ldur            w4, [x2, #0x17]
    // 0xe6aac4: DecompressPointer r4
    //     0xe6aac4: add             x4, x4, HEAP, lsl #32
    // 0xe6aac8: r8 = X0 bound PdfDataType
    //     0xe6aac8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6aacc: ldr             x8, [x8, #0x6d8]
    // 0xe6aad0: LoadField: r9 = r4->field_7
    //     0xe6aad0: ldur            x9, [x4, #7]
    // 0xe6aad4: r3 = Null
    //     0xe6aad4: add             x3, PP, #0x47, lsl #12  ; [pp+0x474f8] Null
    //     0xe6aad8: ldr             x3, [x3, #0x4f8]
    // 0xe6aadc: blr             x9
    // 0xe6aae0: ldur            x1, [fp, #-0x40]
    // 0xe6aae4: r2 = "/ColorSpace"
    //     0xe6aae4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] "/ColorSpace"
    //     0xe6aae8: ldr             x2, [x2, #0xbb0]
    // 0xe6aaec: r3 = Instance_PdfName
    //     0xe6aaec: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebf8] Obj!PdfName@e0c8c1
    //     0xe6aaf0: ldr             x3, [x3, #0xbf8]
    // 0xe6aaf4: r0 = []=()
    //     0xe6aaf4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe6aaf8: ldur            x0, [fp, #-8]
    // 0xe6aafc: LoadField: r1 = r0->field_2b
    //     0xe6aafc: ldur            w1, [x0, #0x2b]
    // 0xe6ab00: DecompressPointer r1
    //     0xe6ab00: add             x1, x1, HEAP, lsl #32
    // 0xe6ab04: ldur            x2, [fp, #-0x10]
    // 0xe6ab08: r0 = putBytes()
    //     0xe6ab08: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0xe6ab0c: ldur            x0, [fp, #-8]
    // 0xe6ab10: LeaveFrame
    //     0xe6ab10: mov             SP, fp
    //     0xe6ab14: ldp             fp, lr, [SP], #0x10
    // 0xe6ab18: ret
    //     0xe6ab18: ret             
    // 0xe6ab1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6ab1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6ab20: b               #0xe6a808
    // 0xe6ab24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6ab24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 6811, size: 0x14, field offset: 0x14
enum PdfImageOrientation extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4db40, size: 0x64
    // 0xc4db40: EnterFrame
    //     0xc4db40: stp             fp, lr, [SP, #-0x10]!
    //     0xc4db44: mov             fp, SP
    // 0xc4db48: AllocStack(0x10)
    //     0xc4db48: sub             SP, SP, #0x10
    // 0xc4db4c: SetupParameters(PdfImageOrientation this /* r1 => r0, fp-0x8 */)
    //     0xc4db4c: mov             x0, x1
    //     0xc4db50: stur            x1, [fp, #-8]
    // 0xc4db54: CheckStackOverflow
    //     0xc4db54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4db58: cmp             SP, x16
    //     0xc4db5c: b.ls            #0xc4db9c
    // 0xc4db60: r1 = Null
    //     0xc4db60: mov             x1, NULL
    // 0xc4db64: r2 = 4
    //     0xc4db64: movz            x2, #0x4
    // 0xc4db68: r0 = AllocateArray()
    //     0xc4db68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4db6c: r16 = "PdfImageOrientation."
    //     0xc4db6c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47788] "PdfImageOrientation."
    //     0xc4db70: ldr             x16, [x16, #0x788]
    // 0xc4db74: StoreField: r0->field_f = r16
    //     0xc4db74: stur            w16, [x0, #0xf]
    // 0xc4db78: ldur            x1, [fp, #-8]
    // 0xc4db7c: LoadField: r2 = r1->field_f
    //     0xc4db7c: ldur            w2, [x1, #0xf]
    // 0xc4db80: DecompressPointer r2
    //     0xc4db80: add             x2, x2, HEAP, lsl #32
    // 0xc4db84: StoreField: r0->field_13 = r2
    //     0xc4db84: stur            w2, [x0, #0x13]
    // 0xc4db88: str             x0, [SP]
    // 0xc4db8c: r0 = _interpolate()
    //     0xc4db8c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4db90: LeaveFrame
    //     0xc4db90: mov             SP, fp
    //     0xc4db94: ldp             fp, lr, [SP], #0x10
    // 0xc4db98: ret
    //     0xc4db98: ret             
    // 0xc4db9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4db9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dba0: b               #0xc4db60
  }
}
