// lib: , url: package:pdf/src/pdf/obj/object.dart

// class id: 1050807, size: 0x8
class :: {
}

// class id: 869, size: 0x2c, field offset: 0x24
class PdfObject<X0 bound PdfDataType> extends PdfObjectBase<X0 bound PdfDataType> {

  _ PdfObject(/* No info */) {
    // ** addr: 0x7cb490, size: 0x10c
    // 0x7cb490: EnterFrame
    //     0x7cb490: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb494: mov             fp, SP
    // 0x7cb498: AllocStack(0x18)
    //     0x7cb498: sub             SP, SP, #0x18
    // 0x7cb49c: r0 = true
    //     0x7cb49c: add             x0, NULL, #0x20  ; true
    // 0x7cb4a0: mov             x4, x1
    // 0x7cb4a4: stur            x2, [fp, #-0x10]
    // 0x7cb4a8: mov             x16, x3
    // 0x7cb4ac: mov             x3, x2
    // 0x7cb4b0: mov             x2, x16
    // 0x7cb4b4: stur            x1, [fp, #-8]
    // 0x7cb4b8: stur            x2, [fp, #-0x18]
    // 0x7cb4bc: CheckStackOverflow
    //     0x7cb4bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb4c0: cmp             SP, x16
    //     0x7cb4c4: b.ls            #0x7cb588
    // 0x7cb4c8: StoreField: r4->field_27 = r0
    //     0x7cb4c8: stur            w0, [x4, #0x27]
    // 0x7cb4cc: mov             x0, x3
    // 0x7cb4d0: StoreField: r4->field_23 = r0
    //     0x7cb4d0: stur            w0, [x4, #0x23]
    //     0x7cb4d4: ldurb           w16, [x4, #-1]
    //     0x7cb4d8: ldurb           w17, [x0, #-1]
    //     0x7cb4dc: and             x16, x17, x16, lsr #2
    //     0x7cb4e0: tst             x16, HEAP, lsr #32
    //     0x7cb4e4: b.eq            #0x7cb4ec
    //     0x7cb4e8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7cb4ec: mov             x1, x3
    // 0x7cb4f0: r0 = genSerial()
    //     0x7cb4f0: bl              #0x7cb59c  ; [package:pdf/src/pdf/document.dart] PdfDocument::genSerial
    // 0x7cb4f4: ldur            x3, [fp, #-0x10]
    // 0x7cb4f8: LoadField: r1 = r3->field_1b
    //     0x7cb4f8: ldur            w1, [x3, #0x1b]
    // 0x7cb4fc: DecompressPointer r1
    //     0x7cb4fc: add             x1, x1, HEAP, lsl #32
    // 0x7cb500: r16 = Sentinel
    //     0x7cb500: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7cb504: cmp             w1, w16
    // 0x7cb508: b.eq            #0x7cb590
    // 0x7cb50c: ldur            x4, [fp, #-8]
    // 0x7cb510: StoreField: r4->field_b = r0
    //     0x7cb510: stur            x0, [x4, #0xb]
    // 0x7cb514: StoreField: r4->field_13 = rZR
    //     0x7cb514: stur            xzr, [x4, #0x13]
    // 0x7cb518: ldur            x0, [fp, #-0x18]
    // 0x7cb51c: StoreField: r4->field_1b = r0
    //     0x7cb51c: stur            w0, [x4, #0x1b]
    //     0x7cb520: ldurb           w16, [x4, #-1]
    //     0x7cb524: ldurb           w17, [x0, #-1]
    //     0x7cb528: and             x16, x17, x16, lsr #2
    //     0x7cb52c: tst             x16, HEAP, lsr #32
    //     0x7cb530: b.eq            #0x7cb538
    //     0x7cb534: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7cb538: mov             x0, x1
    // 0x7cb53c: StoreField: r4->field_1f = r0
    //     0x7cb53c: stur            w0, [x4, #0x1f]
    //     0x7cb540: ldurb           w16, [x4, #-1]
    //     0x7cb544: ldurb           w17, [x0, #-1]
    //     0x7cb548: and             x16, x17, x16, lsr #2
    //     0x7cb54c: tst             x16, HEAP, lsr #32
    //     0x7cb550: b.eq            #0x7cb558
    //     0x7cb554: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7cb558: r1 = <String>
    //     0x7cb558: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7cb55c: r2 = 0
    //     0x7cb55c: movz            x2, #0
    // 0x7cb560: r0 = _GrowableList()
    //     0x7cb560: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7cb564: ldur            x0, [fp, #-0x10]
    // 0x7cb568: LoadField: r1 = r0->field_13
    //     0x7cb568: ldur            w1, [x0, #0x13]
    // 0x7cb56c: DecompressPointer r1
    //     0x7cb56c: add             x1, x1, HEAP, lsl #32
    // 0x7cb570: ldur            x2, [fp, #-8]
    // 0x7cb574: r0 = add()
    //     0x7cb574: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x7cb578: r0 = Null
    //     0x7cb578: mov             x0, NULL
    // 0x7cb57c: LeaveFrame
    //     0x7cb57c: mov             SP, fp
    //     0x7cb580: ldp             fp, lr, [SP], #0x10
    // 0x7cb584: ret
    //     0x7cb584: ret             
    // 0x7cb588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb58c: b               #0x7cb4c8
    // 0x7cb590: r9 = settings
    //     0x7cb590: add             x9, PP, #0x36, lsl #12  ; [pp+0x36828] Field <PdfDocument.settings>: late final (offset: 0x1c)
    //     0x7cb594: ldr             x9, [x9, #0x828]
    // 0x7cb598: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7cb598: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc356b0, size: 0x74
    // 0xc356b0: EnterFrame
    //     0xc356b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc356b4: mov             fp, SP
    // 0xc356b8: AllocStack(0x10)
    //     0xc356b8: sub             SP, SP, #0x10
    // 0xc356bc: CheckStackOverflow
    //     0xc356bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc356c0: cmp             SP, x16
    //     0xc356c4: b.ls            #0xc3571c
    // 0xc356c8: ldr             x16, [fp, #0x10]
    // 0xc356cc: str             x16, [SP]
    // 0xc356d0: r0 = runtimeType()
    //     0xc356d0: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xc356d4: r1 = Null
    //     0xc356d4: mov             x1, NULL
    // 0xc356d8: r2 = 6
    //     0xc356d8: movz            x2, #0x6
    // 0xc356dc: stur            x0, [fp, #-8]
    // 0xc356e0: r0 = AllocateArray()
    //     0xc356e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc356e4: mov             x1, x0
    // 0xc356e8: ldur            x0, [fp, #-8]
    // 0xc356ec: StoreField: r1->field_f = r0
    //     0xc356ec: stur            w0, [x1, #0xf]
    // 0xc356f0: r16 = " "
    //     0xc356f0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc356f4: StoreField: r1->field_13 = r16
    //     0xc356f4: stur            w16, [x1, #0x13]
    // 0xc356f8: ldr             x0, [fp, #0x10]
    // 0xc356fc: LoadField: r2 = r0->field_1b
    //     0xc356fc: ldur            w2, [x0, #0x1b]
    // 0xc35700: DecompressPointer r2
    //     0xc35700: add             x2, x2, HEAP, lsl #32
    // 0xc35704: ArrayStore: r1[0] = r2  ; List_4
    //     0xc35704: stur            w2, [x1, #0x17]
    // 0xc35708: str             x1, [SP]
    // 0xc3570c: r0 = _interpolate()
    //     0xc3570c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35710: LeaveFrame
    //     0xc35710: mov             SP, fp
    //     0xc35714: ldp             fp, lr, [SP], #0x10
    // 0xc35718: ret
    //     0xc35718: ret             
    // 0xc3571c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3571c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35720: b               #0xc356c8
  }
}
