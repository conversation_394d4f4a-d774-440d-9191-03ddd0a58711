// lib: , url: package:pdf/src/pdf/obj/object_stream.dart

// class id: 1050808, size: 0x8
class :: {
}

// class id: 884, size: 0x34, field offset: 0x2c
class PdfObjectStream extends PdfObject<dynamic> {

  _ writeContent(/* No info */) {
    // ** addr: 0x862044, size: 0xbc
    // 0x862044: EnterFrame
    //     0x862044: stp             fp, lr, [SP, #-0x10]!
    //     0x862048: mov             fp, SP
    // 0x86204c: AllocStack(0x28)
    //     0x86204c: sub             SP, SP, #0x28
    // 0x862050: SetupParameters(PdfObjectStream this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x862050: mov             x3, x2
    //     0x862054: stur            x2, [fp, #-0x20]
    //     0x862058: mov             x2, x1
    //     0x86205c: stur            x1, [fp, #-0x18]
    // 0x862060: CheckStackOverflow
    //     0x862060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862064: cmp             SP, x16
    //     0x862068: b.ls            #0x8620f8
    // 0x86206c: LoadField: r0 = r2->field_2f
    //     0x86206c: ldur            w0, [x2, #0x2f]
    // 0x862070: DecompressPointer r0
    //     0x862070: add             x0, x0, HEAP, lsl #32
    // 0x862074: stur            x0, [fp, #-0x10]
    // 0x862078: LoadField: r1 = r2->field_1b
    //     0x862078: ldur            w1, [x2, #0x1b]
    // 0x86207c: DecompressPointer r1
    //     0x86207c: add             x1, x1, HEAP, lsl #32
    // 0x862080: LoadField: r4 = r1->field_b
    //     0x862080: ldur            w4, [x1, #0xb]
    // 0x862084: DecompressPointer r4
    //     0x862084: add             x4, x4, HEAP, lsl #32
    // 0x862088: stur            x4, [fp, #-8]
    // 0x86208c: LoadField: r1 = r2->field_2b
    //     0x86208c: ldur            w1, [x2, #0x2b]
    // 0x862090: DecompressPointer r1
    //     0x862090: add             x1, x1, HEAP, lsl #32
    // 0x862094: r0 = output()
    //     0x862094: bl              #0x7b57e4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::output
    // 0x862098: r1 = <PdfDataType>
    //     0x862098: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x86209c: ldr             x1, [x1, #0x4c8]
    // 0x8620a0: stur            x0, [fp, #-0x28]
    // 0x8620a4: r0 = PdfDictStream()
    //     0x8620a4: bl              #0x862188  ; AllocatePdfDictStreamStub -> PdfDictStream (size=0x20)
    // 0x8620a8: mov             x1, x0
    // 0x8620ac: ldur            x0, [fp, #-0x10]
    // 0x8620b0: StoreField: r1->field_13 = r0
    //     0x8620b0: stur            w0, [x1, #0x13]
    // 0x8620b4: r0 = true
    //     0x8620b4: add             x0, NULL, #0x20  ; true
    // 0x8620b8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8620b8: stur            w0, [x1, #0x17]
    // 0x8620bc: StoreField: r1->field_1b = r0
    //     0x8620bc: stur            w0, [x1, #0x1b]
    // 0x8620c0: ldur            x0, [fp, #-0x28]
    // 0x8620c4: StoreField: r1->field_f = r0
    //     0x8620c4: stur            w0, [x1, #0xf]
    // 0x8620c8: ldur            x0, [fp, #-8]
    // 0x8620cc: StoreField: r1->field_b = r0
    //     0x8620cc: stur            w0, [x1, #0xb]
    // 0x8620d0: ldur            x2, [fp, #-0x18]
    // 0x8620d4: ldur            x3, [fp, #-0x20]
    // 0x8620d8: r0 = output()
    //     0x8620d8: bl              #0xe7eac4  ; [package:pdf/src/pdf/format/dict_stream.dart] PdfDictStream::output
    // 0x8620dc: ldur            x1, [fp, #-0x20]
    // 0x8620e0: r2 = 10
    //     0x8620e0: movz            x2, #0xa
    // 0x8620e4: r0 = putByte()
    //     0x8620e4: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0x8620e8: r0 = Null
    //     0x8620e8: mov             x0, NULL
    // 0x8620ec: LeaveFrame
    //     0x8620ec: mov             SP, fp
    //     0x8620f0: ldp             fp, lr, [SP], #0x10
    // 0x8620f4: ret
    //     0x8620f4: ret             
    // 0x8620f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8620f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8620fc: b               #0x86206c
  }
  _ PdfObjectStream(/* No info */) {
    // ** addr: 0xe4714c, size: 0x1a0
    // 0xe4714c: EnterFrame
    //     0xe4714c: stp             fp, lr, [SP, #-0x10]!
    //     0xe47150: mov             fp, SP
    // 0xe47154: AllocStack(0x38)
    //     0xe47154: sub             SP, SP, #0x38
    // 0xe47158: SetupParameters(PdfObjectStream this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */, {dynamic isBinary = false /* r5, fp-0x10 */, dynamic type = Null /* r0, fp-0x8 */})
    //     0xe47158: stur            x1, [fp, #-0x18]
    //     0xe4715c: stur            x2, [fp, #-0x20]
    //     0xe47160: ldur            w0, [x4, #0x13]
    //     0xe47164: ldur            w3, [x4, #0x1f]
    //     0xe47168: add             x3, x3, HEAP, lsl #32
    //     0xe4716c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36818] "isBinary"
    //     0xe47170: ldr             x16, [x16, #0x818]
    //     0xe47174: cmp             w3, w16
    //     0xe47178: b.ne            #0xe4719c
    //     0xe4717c: ldur            w3, [x4, #0x23]
    //     0xe47180: add             x3, x3, HEAP, lsl #32
    //     0xe47184: sub             w5, w0, w3
    //     0xe47188: add             x3, fp, w5, sxtw #2
    //     0xe4718c: ldr             x3, [x3, #8]
    //     0xe47190: mov             x5, x3
    //     0xe47194: movz            x3, #0x1
    //     0xe47198: b               #0xe471a4
    //     0xe4719c: add             x5, NULL, #0x30  ; false
    //     0xe471a0: movz            x3, #0
    //     0xe471a4: stur            x5, [fp, #-0x10]
    //     0xe471a8: lsl             x6, x3, #1
    //     0xe471ac: lsl             w3, w6, #1
    //     0xe471b0: add             w6, w3, #8
    //     0xe471b4: add             x16, x4, w6, sxtw #1
    //     0xe471b8: ldur            w7, [x16, #0xf]
    //     0xe471bc: add             x7, x7, HEAP, lsl #32
    //     0xe471c0: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    //     0xe471c4: cmp             w7, w16
    //     0xe471c8: b.ne            #0xe471ec
    //     0xe471cc: add             w6, w3, #0xa
    //     0xe471d0: add             x16, x4, w6, sxtw #1
    //     0xe471d4: ldur            w3, [x16, #0xf]
    //     0xe471d8: add             x3, x3, HEAP, lsl #32
    //     0xe471dc: sub             w4, w0, w3
    //     0xe471e0: add             x0, fp, w4, sxtw #2
    //     0xe471e4: ldr             x0, [x0, #8]
    //     0xe471e8: b               #0xe471f0
    //     0xe471ec: mov             x0, NULL
    //     0xe471f0: stur            x0, [fp, #-8]
    // 0xe471f4: CheckStackOverflow
    //     0xe471f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe471f8: cmp             SP, x16
    //     0xe471fc: b.ls            #0xe472e4
    // 0xe47200: r0 = PdfStream()
    //     0xe47200: bl              #0x7b58f8  ; AllocatePdfStreamStub -> PdfStream (size=0x14)
    // 0xe47204: stur            x0, [fp, #-0x28]
    // 0xe47208: StoreField: r0->field_b = rZR
    //     0xe47208: stur            xzr, [x0, #0xb]
    // 0xe4720c: r4 = 2
    //     0xe4720c: movz            x4, #0x2, lsl #16
    // 0xe47210: r0 = AllocateUint8Array()
    //     0xe47210: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe47214: mov             x1, x0
    // 0xe47218: ldur            x0, [fp, #-0x28]
    // 0xe4721c: StoreField: r0->field_7 = r1
    //     0xe4721c: stur            w1, [x0, #7]
    // 0xe47220: ldur            x1, [fp, #-0x18]
    // 0xe47224: StoreField: r1->field_2b = r0
    //     0xe47224: stur            w0, [x1, #0x2b]
    //     0xe47228: ldurb           w16, [x1, #-1]
    //     0xe4722c: ldurb           w17, [x0, #-1]
    //     0xe47230: and             x16, x17, x16, lsr #2
    //     0xe47234: tst             x16, HEAP, lsr #32
    //     0xe47238: b.eq            #0xe47240
    //     0xe4723c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe47240: ldur            x0, [fp, #-0x10]
    // 0xe47244: StoreField: r1->field_2f = r0
    //     0xe47244: stur            w0, [x1, #0x2f]
    // 0xe47248: r16 = <String, PdfDataType>
    //     0xe47248: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe4724c: ldr             x16, [x16, #0x820]
    // 0xe47250: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe47254: stp             lr, x16, [SP]
    // 0xe47258: r0 = Map._fromLiteral()
    //     0xe47258: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe4725c: mov             x1, x0
    // 0xe47260: ldur            x0, [fp, #-8]
    // 0xe47264: stur            x1, [fp, #-0x10]
    // 0xe47268: cmp             w0, NULL
    // 0xe4726c: b.eq            #0xe472a8
    // 0xe47270: r0 = PdfName()
    //     0xe47270: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe47274: mov             x1, x0
    // 0xe47278: ldur            x0, [fp, #-8]
    // 0xe4727c: StoreField: r1->field_7 = r0
    //     0xe4727c: stur            w0, [x1, #7]
    // 0xe47280: ldur            x4, [fp, #-0x10]
    // 0xe47284: r0 = LoadClassIdInstr(r4)
    //     0xe47284: ldur            x0, [x4, #-1]
    //     0xe47288: ubfx            x0, x0, #0xc, #0x14
    // 0xe4728c: mov             x3, x1
    // 0xe47290: mov             x1, x4
    // 0xe47294: r2 = "/Type"
    //     0xe47294: add             x2, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe47298: ldr             x2, [x2, #0x630]
    // 0xe4729c: r0 = GDT[cid_x0 + -0x10d]()
    //     0xe4729c: sub             lr, x0, #0x10d
    //     0xe472a0: ldr             lr, [x21, lr, lsl #3]
    //     0xe472a4: blr             lr
    // 0xe472a8: ldur            x0, [fp, #-0x10]
    // 0xe472ac: r1 = <PdfDataType>
    //     0xe472ac: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe472b0: ldr             x1, [x1, #0x4c8]
    // 0xe472b4: r0 = PdfDict()
    //     0xe472b4: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe472b8: mov             x1, x0
    // 0xe472bc: ldur            x0, [fp, #-0x10]
    // 0xe472c0: StoreField: r1->field_b = r0
    //     0xe472c0: stur            w0, [x1, #0xb]
    // 0xe472c4: mov             x3, x1
    // 0xe472c8: ldur            x1, [fp, #-0x18]
    // 0xe472cc: ldur            x2, [fp, #-0x20]
    // 0xe472d0: r0 = PdfObject()
    //     0xe472d0: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe472d4: r0 = Null
    //     0xe472d4: mov             x0, NULL
    // 0xe472d8: LeaveFrame
    //     0xe472d8: mov             SP, fp
    //     0xe472dc: ldp             fp, lr, [SP], #0x10
    // 0xe472e0: ret
    //     0xe472e0: ret             
    // 0xe472e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe472e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe472e8: b               #0xe47200
  }
}
