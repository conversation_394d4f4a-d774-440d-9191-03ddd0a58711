// lib: , url: package:pdf/src/pdf/obj/page.dart

// class id: 1050810, size: 0x8
class :: {
}

// class id: 877, size: 0x48, field offset: 0x2c
//   transformed mixin,
abstract class _PdfPage&PdfObject&PdfGraphicStream extends PdfObject<dynamic>
     with PdfGraphicStream {

  _ prepare(/* No info */) {
    // ** addr: 0x7cbe8c, size: 0x4cc
    // 0x7cbe8c: EnterFrame
    //     0x7cbe8c: stp             fp, lr, [SP, #-0x10]!
    //     0x7cbe90: mov             fp, SP
    // 0x7cbe94: AllocStack(0x40)
    //     0x7cbe94: sub             SP, SP, #0x40
    // 0x7cbe98: SetupParameters(_PdfPage&PdfObject&PdfGraphicStream this /* r1 => r0, fp-0x8 */)
    //     0x7cbe98: mov             x0, x1
    //     0x7cbe9c: stur            x1, [fp, #-8]
    // 0x7cbea0: CheckStackOverflow
    //     0x7cbea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cbea4: cmp             SP, x16
    //     0x7cbea8: b.ls            #0x7cc350
    // 0x7cbeac: r1 = <PdfDataType>
    //     0x7cbeac: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7cbeb0: ldr             x1, [x1, #0x4c8]
    // 0x7cbeb4: r0 = PdfDict()
    //     0x7cbeb4: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7cbeb8: mov             x1, x0
    // 0x7cbebc: stur            x0, [fp, #-0x10]
    // 0x7cbec0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7cbec0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7cbec4: r0 = PdfDict()
    //     0x7cbec4: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0x7cbec8: ldur            x0, [fp, #-8]
    // 0x7cbecc: LoadField: r1 = r0->field_43
    //     0x7cbecc: ldur            w1, [x0, #0x43]
    // 0x7cbed0: DecompressPointer r1
    //     0x7cbed0: add             x1, x1, HEAP, lsl #32
    // 0x7cbed4: tbnz            w1, #4, #0x7cbf20
    // 0x7cbed8: ldur            x2, [fp, #-0x10]
    // 0x7cbedc: r1 = <PdfName>
    //     0x7cbedc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eeb8] TypeArguments: <PdfName>
    //     0x7cbee0: ldr             x1, [x1, #0xeb8]
    // 0x7cbee4: r0 = PdfArray()
    //     0x7cbee4: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7cbee8: stur            x0, [fp, #-0x18]
    // 0x7cbeec: r16 = const [Instance of 'PdfName', Instance of 'PdfName', Instance of 'PdfName', Instance of 'PdfName']
    //     0x7cbeec: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eec0] List<PdfName>(4)
    //     0x7cbef0: ldr             x16, [x16, #0xec0]
    // 0x7cbef4: str             x16, [SP]
    // 0x7cbef8: mov             x1, x0
    // 0x7cbefc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7cbefc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7cbf00: r0 = PdfArray()
    //     0x7cbf00: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7cbf04: ldur            x0, [fp, #-0x10]
    // 0x7cbf08: LoadField: r1 = r0->field_b
    //     0x7cbf08: ldur            w1, [x0, #0xb]
    // 0x7cbf0c: DecompressPointer r1
    //     0x7cbf0c: add             x1, x1, HEAP, lsl #32
    // 0x7cbf10: ldur            x3, [fp, #-0x18]
    // 0x7cbf14: r2 = "/ProcSet"
    //     0x7cbf14: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eec8] "/ProcSet"
    //     0x7cbf18: ldr             x2, [x2, #0xec8]
    // 0x7cbf1c: r0 = []=()
    //     0x7cbf1c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbf20: ldur            x0, [fp, #-8]
    // 0x7cbf24: LoadField: r1 = r0->field_33
    //     0x7cbf24: ldur            w1, [x0, #0x33]
    // 0x7cbf28: DecompressPointer r1
    //     0x7cbf28: add             x1, x1, HEAP, lsl #32
    // 0x7cbf2c: LoadField: r2 = r1->field_13
    //     0x7cbf2c: ldur            w2, [x1, #0x13]
    // 0x7cbf30: r3 = LoadInt32Instr(r2)
    //     0x7cbf30: sbfx            x3, x2, #1, #0x1f
    // 0x7cbf34: asr             x2, x3, #1
    // 0x7cbf38: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cbf38: ldur            w3, [x1, #0x17]
    // 0x7cbf3c: r4 = LoadInt32Instr(r3)
    //     0x7cbf3c: sbfx            x4, x3, #1, #0x1f
    // 0x7cbf40: sub             x3, x2, x4
    // 0x7cbf44: cbz             x3, #0x7cbf74
    // 0x7cbf48: ldur            x2, [fp, #-0x10]
    // 0x7cbf4c: r0 = fromObjectMap()
    //     0x7cbf4c: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7cbf50: mov             x1, x0
    // 0x7cbf54: ldur            x0, [fp, #-0x10]
    // 0x7cbf58: LoadField: r2 = r0->field_b
    //     0x7cbf58: ldur            w2, [x0, #0xb]
    // 0x7cbf5c: DecompressPointer r2
    //     0x7cbf5c: add             x2, x2, HEAP, lsl #32
    // 0x7cbf60: mov             x3, x1
    // 0x7cbf64: mov             x1, x2
    // 0x7cbf68: r2 = "/Font"
    //     0x7cbf68: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c190] "/Font"
    //     0x7cbf6c: ldr             x2, [x2, #0x190]
    // 0x7cbf70: r0 = []=()
    //     0x7cbf70: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbf74: ldur            x0, [fp, #-8]
    // 0x7cbf78: LoadField: r1 = r0->field_37
    //     0x7cbf78: ldur            w1, [x0, #0x37]
    // 0x7cbf7c: DecompressPointer r1
    //     0x7cbf7c: add             x1, x1, HEAP, lsl #32
    // 0x7cbf80: LoadField: r2 = r1->field_13
    //     0x7cbf80: ldur            w2, [x1, #0x13]
    // 0x7cbf84: r3 = LoadInt32Instr(r2)
    //     0x7cbf84: sbfx            x3, x2, #1, #0x1f
    // 0x7cbf88: asr             x2, x3, #1
    // 0x7cbf8c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cbf8c: ldur            w3, [x1, #0x17]
    // 0x7cbf90: r4 = LoadInt32Instr(r3)
    //     0x7cbf90: sbfx            x4, x3, #1, #0x1f
    // 0x7cbf94: sub             x3, x2, x4
    // 0x7cbf98: cbz             x3, #0x7cbfc8
    // 0x7cbf9c: ldur            x2, [fp, #-0x10]
    // 0x7cbfa0: r0 = fromObjectMap()
    //     0x7cbfa0: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7cbfa4: mov             x1, x0
    // 0x7cbfa8: ldur            x0, [fp, #-0x10]
    // 0x7cbfac: LoadField: r2 = r0->field_b
    //     0x7cbfac: ldur            w2, [x0, #0xb]
    // 0x7cbfb0: DecompressPointer r2
    //     0x7cbfb0: add             x2, x2, HEAP, lsl #32
    // 0x7cbfb4: mov             x3, x1
    // 0x7cbfb8: mov             x1, x2
    // 0x7cbfbc: r2 = "/Shading"
    //     0x7cbfbc: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eed0] "/Shading"
    //     0x7cbfc0: ldr             x2, [x2, #0xed0]
    // 0x7cbfc4: r0 = []=()
    //     0x7cbfc4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbfc8: ldur            x0, [fp, #-8]
    // 0x7cbfcc: LoadField: r1 = r0->field_3b
    //     0x7cbfcc: ldur            w1, [x0, #0x3b]
    // 0x7cbfd0: DecompressPointer r1
    //     0x7cbfd0: add             x1, x1, HEAP, lsl #32
    // 0x7cbfd4: LoadField: r2 = r1->field_13
    //     0x7cbfd4: ldur            w2, [x1, #0x13]
    // 0x7cbfd8: r3 = LoadInt32Instr(r2)
    //     0x7cbfd8: sbfx            x3, x2, #1, #0x1f
    // 0x7cbfdc: asr             x2, x3, #1
    // 0x7cbfe0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cbfe0: ldur            w3, [x1, #0x17]
    // 0x7cbfe4: r4 = LoadInt32Instr(r3)
    //     0x7cbfe4: sbfx            x4, x3, #1, #0x1f
    // 0x7cbfe8: sub             x3, x2, x4
    // 0x7cbfec: cbz             x3, #0x7cc01c
    // 0x7cbff0: ldur            x2, [fp, #-0x10]
    // 0x7cbff4: r0 = fromObjectMap()
    //     0x7cbff4: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7cbff8: mov             x1, x0
    // 0x7cbffc: ldur            x0, [fp, #-0x10]
    // 0x7cc000: LoadField: r2 = r0->field_b
    //     0x7cc000: ldur            w2, [x0, #0xb]
    // 0x7cc004: DecompressPointer r2
    //     0x7cc004: add             x2, x2, HEAP, lsl #32
    // 0x7cc008: mov             x3, x1
    // 0x7cc00c: mov             x1, x2
    // 0x7cc010: r2 = "/Pattern"
    //     0x7cc010: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eed8] "/Pattern"
    //     0x7cc014: ldr             x2, [x2, #0xed8]
    // 0x7cc018: r0 = []=()
    //     0x7cc018: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc01c: ldur            x0, [fp, #-8]
    // 0x7cc020: LoadField: r1 = r0->field_3f
    //     0x7cc020: ldur            w1, [x0, #0x3f]
    // 0x7cc024: DecompressPointer r1
    //     0x7cc024: add             x1, x1, HEAP, lsl #32
    // 0x7cc028: LoadField: r2 = r1->field_13
    //     0x7cc028: ldur            w2, [x1, #0x13]
    // 0x7cc02c: r3 = LoadInt32Instr(r2)
    //     0x7cc02c: sbfx            x3, x2, #1, #0x1f
    // 0x7cc030: asr             x2, x3, #1
    // 0x7cc034: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cc034: ldur            w3, [x1, #0x17]
    // 0x7cc038: r4 = LoadInt32Instr(r3)
    //     0x7cc038: sbfx            x4, x3, #1, #0x1f
    // 0x7cc03c: sub             x3, x2, x4
    // 0x7cc040: cbz             x3, #0x7cc070
    // 0x7cc044: ldur            x2, [fp, #-0x10]
    // 0x7cc048: r0 = fromObjectMap()
    //     0x7cc048: bl              #0x7cb5e4  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::fromObjectMap
    // 0x7cc04c: mov             x1, x0
    // 0x7cc050: ldur            x0, [fp, #-0x10]
    // 0x7cc054: LoadField: r2 = r0->field_b
    //     0x7cc054: ldur            w2, [x0, #0xb]
    // 0x7cc058: DecompressPointer r2
    //     0x7cc058: add             x2, x2, HEAP, lsl #32
    // 0x7cc05c: mov             x3, x1
    // 0x7cc060: mov             x1, x2
    // 0x7cc064: r2 = "/XObject"
    //     0x7cc064: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ea58] "/XObject"
    //     0x7cc068: ldr             x2, [x2, #0xa58]
    // 0x7cc06c: r0 = []=()
    //     0x7cc06c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc070: ldur            x0, [fp, #-8]
    // 0x7cc074: LoadField: r3 = r0->field_23
    //     0x7cc074: ldur            w3, [x0, #0x23]
    // 0x7cc078: DecompressPointer r3
    //     0x7cc078: add             x3, x3, HEAP, lsl #32
    // 0x7cc07c: stur            x3, [fp, #-0x20]
    // 0x7cc080: LoadField: r1 = r3->field_27
    //     0x7cc080: ldur            w1, [x3, #0x27]
    // 0x7cc084: DecompressPointer r1
    //     0x7cc084: add             x1, x1, HEAP, lsl #32
    // 0x7cc088: cmp             w1, NULL
    // 0x7cc08c: b.eq            #0x7cc25c
    // 0x7cc090: LoadField: r4 = r0->field_1b
    //     0x7cc090: ldur            w4, [x0, #0x1b]
    // 0x7cc094: DecompressPointer r4
    //     0x7cc094: add             x4, x4, HEAP, lsl #32
    // 0x7cc098: mov             x1, x4
    // 0x7cc09c: stur            x4, [fp, #-0x18]
    // 0x7cc0a0: r2 = "/Group"
    //     0x7cc0a0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eee0] "/Group"
    //     0x7cc0a4: ldr             x2, [x2, #0xee0]
    // 0x7cc0a8: r0 = contains()
    //     0x7cc0a8: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cc0ac: tbz             w0, #4, #0x7cc25c
    // 0x7cc0b0: ldur            x3, [fp, #-0x10]
    // 0x7cc0b4: ldur            x0, [fp, #-0x18]
    // 0x7cc0b8: r1 = Null
    //     0x7cc0b8: mov             x1, NULL
    // 0x7cc0bc: r2 = 20
    //     0x7cc0bc: movz            x2, #0x14
    // 0x7cc0c0: r0 = AllocateArray()
    //     0x7cc0c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cc0c4: stur            x0, [fp, #-0x28]
    // 0x7cc0c8: r16 = "/Type"
    //     0x7cc0c8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0x7cc0cc: ldr             x16, [x16, #0x630]
    // 0x7cc0d0: StoreField: r0->field_f = r16
    //     0x7cc0d0: stur            w16, [x0, #0xf]
    // 0x7cc0d4: r16 = Instance_PdfName
    //     0x7cc0d4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eee8] Obj!PdfName@e0c861
    //     0x7cc0d8: ldr             x16, [x16, #0xee8]
    // 0x7cc0dc: StoreField: r0->field_13 = r16
    //     0x7cc0dc: stur            w16, [x0, #0x13]
    // 0x7cc0e0: r16 = "/S"
    //     0x7cc0e0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eef0] "/S"
    //     0x7cc0e4: ldr             x16, [x16, #0xef0]
    // 0x7cc0e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7cc0e8: stur            w16, [x0, #0x17]
    // 0x7cc0ec: r16 = Instance_PdfName
    //     0x7cc0ec: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eef8] Obj!PdfName@e0c851
    //     0x7cc0f0: ldr             x16, [x16, #0xef8]
    // 0x7cc0f4: StoreField: r0->field_1b = r16
    //     0x7cc0f4: stur            w16, [x0, #0x1b]
    // 0x7cc0f8: r16 = "/CS"
    //     0x7cc0f8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ef00] "/CS"
    //     0x7cc0fc: ldr             x16, [x16, #0xf00]
    // 0x7cc100: StoreField: r0->field_1f = r16
    //     0x7cc100: stur            w16, [x0, #0x1f]
    // 0x7cc104: r16 = Instance_PdfName
    //     0x7cc104: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0x7cc108: ldr             x16, [x16, #0xb98]
    // 0x7cc10c: StoreField: r0->field_23 = r16
    //     0x7cc10c: stur            w16, [x0, #0x23]
    // 0x7cc110: r16 = "/I"
    //     0x7cc110: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0x7cc114: ldr             x16, [x16, #0xbe0]
    // 0x7cc118: StoreField: r0->field_27 = r16
    //     0x7cc118: stur            w16, [x0, #0x27]
    // 0x7cc11c: r0 = PdfBool()
    //     0x7cc11c: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7cc120: r2 = false
    //     0x7cc120: add             x2, NULL, #0x30  ; false
    // 0x7cc124: StoreField: r0->field_7 = r2
    //     0x7cc124: stur            w2, [x0, #7]
    // 0x7cc128: ldur            x1, [fp, #-0x28]
    // 0x7cc12c: ArrayStore: r1[7] = r0  ; List_4
    //     0x7cc12c: add             x25, x1, #0x2b
    //     0x7cc130: str             w0, [x25]
    //     0x7cc134: tbz             w0, #0, #0x7cc150
    //     0x7cc138: ldurb           w16, [x1, #-1]
    //     0x7cc13c: ldurb           w17, [x0, #-1]
    //     0x7cc140: and             x16, x17, x16, lsr #2
    //     0x7cc144: tst             x16, HEAP, lsr #32
    //     0x7cc148: b.eq            #0x7cc150
    //     0x7cc14c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cc150: ldur            x1, [fp, #-0x28]
    // 0x7cc154: r16 = "/K"
    //     0x7cc154: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ef08] "/K"
    //     0x7cc158: ldr             x16, [x16, #0xf08]
    // 0x7cc15c: StoreField: r1->field_2f = r16
    //     0x7cc15c: stur            w16, [x1, #0x2f]
    // 0x7cc160: r0 = PdfBool()
    //     0x7cc160: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7cc164: mov             x1, x0
    // 0x7cc168: r0 = false
    //     0x7cc168: add             x0, NULL, #0x30  ; false
    // 0x7cc16c: StoreField: r1->field_7 = r0
    //     0x7cc16c: stur            w0, [x1, #7]
    // 0x7cc170: mov             x0, x1
    // 0x7cc174: ldur            x1, [fp, #-0x28]
    // 0x7cc178: ArrayStore: r1[9] = r0  ; List_4
    //     0x7cc178: add             x25, x1, #0x33
    //     0x7cc17c: str             w0, [x25]
    //     0x7cc180: tbz             w0, #0, #0x7cc19c
    //     0x7cc184: ldurb           w16, [x1, #-1]
    //     0x7cc188: ldurb           w17, [x0, #-1]
    //     0x7cc18c: and             x16, x17, x16, lsr #2
    //     0x7cc190: tst             x16, HEAP, lsr #32
    //     0x7cc194: b.eq            #0x7cc19c
    //     0x7cc198: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cc19c: r16 = <String, PdfDataType>
    //     0x7cc19c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0x7cc1a0: ldr             x16, [x16, #0x820]
    // 0x7cc1a4: ldur            lr, [fp, #-0x28]
    // 0x7cc1a8: stp             lr, x16, [SP]
    // 0x7cc1ac: r0 = Map._fromLiteral()
    //     0x7cc1ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7cc1b0: r1 = <PdfDataType>
    //     0x7cc1b0: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7cc1b4: ldr             x1, [x1, #0x4c8]
    // 0x7cc1b8: stur            x0, [fp, #-0x28]
    // 0x7cc1bc: r0 = PdfDict()
    //     0x7cc1bc: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7cc1c0: mov             x3, x0
    // 0x7cc1c4: ldur            x0, [fp, #-0x28]
    // 0x7cc1c8: stur            x3, [fp, #-0x30]
    // 0x7cc1cc: StoreField: r3->field_b = r0
    //     0x7cc1cc: stur            w0, [x3, #0xb]
    // 0x7cc1d0: ldur            x4, [fp, #-0x18]
    // 0x7cc1d4: LoadField: r2 = r4->field_7
    //     0x7cc1d4: ldur            w2, [x4, #7]
    // 0x7cc1d8: DecompressPointer r2
    //     0x7cc1d8: add             x2, x2, HEAP, lsl #32
    // 0x7cc1dc: mov             x0, x3
    // 0x7cc1e0: r1 = Null
    //     0x7cc1e0: mov             x1, NULL
    // 0x7cc1e4: cmp             w2, NULL
    // 0x7cc1e8: b.eq            #0x7cc20c
    // 0x7cc1ec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc1ec: ldur            w4, [x2, #0x17]
    // 0x7cc1f0: DecompressPointer r4
    //     0x7cc1f0: add             x4, x4, HEAP, lsl #32
    // 0x7cc1f4: r8 = X0 bound PdfDataType
    //     0x7cc1f4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc1f8: ldr             x8, [x8, #0x6d8]
    // 0x7cc1fc: LoadField: r9 = r4->field_7
    //     0x7cc1fc: ldur            x9, [x4, #7]
    // 0x7cc200: r3 = Null
    //     0x7cc200: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef10] Null
    //     0x7cc204: ldr             x3, [x3, #0xf10]
    // 0x7cc208: blr             x9
    // 0x7cc20c: ldur            x0, [fp, #-0x18]
    // 0x7cc210: LoadField: r1 = r0->field_b
    //     0x7cc210: ldur            w1, [x0, #0xb]
    // 0x7cc214: DecompressPointer r1
    //     0x7cc214: add             x1, x1, HEAP, lsl #32
    // 0x7cc218: ldur            x3, [fp, #-0x30]
    // 0x7cc21c: r2 = "/Group"
    //     0x7cc21c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eee0] "/Group"
    //     0x7cc220: ldr             x2, [x2, #0xee0]
    // 0x7cc224: r0 = []=()
    //     0x7cc224: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc228: ldur            x1, [fp, #-0x20]
    // 0x7cc22c: r0 = graphicStates()
    //     0x7cc22c: bl              #0x7cb36c  ; [package:pdf/src/pdf/document.dart] PdfDocument::graphicStates
    // 0x7cc230: mov             x1, x0
    // 0x7cc234: r0 = ref()
    //     0x7cc234: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cc238: mov             x1, x0
    // 0x7cc23c: ldur            x0, [fp, #-0x10]
    // 0x7cc240: LoadField: r2 = r0->field_b
    //     0x7cc240: ldur            w2, [x0, #0xb]
    // 0x7cc244: DecompressPointer r2
    //     0x7cc244: add             x2, x2, HEAP, lsl #32
    // 0x7cc248: mov             x3, x1
    // 0x7cc24c: mov             x1, x2
    // 0x7cc250: r2 = "/ExtGState"
    //     0x7cc250: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef20] "/ExtGState"
    //     0x7cc254: ldr             x2, [x2, #0xf20]
    // 0x7cc258: r0 = []=()
    //     0x7cc258: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc25c: ldur            x0, [fp, #-0x10]
    // 0x7cc260: LoadField: r1 = r0->field_b
    //     0x7cc260: ldur            w1, [x0, #0xb]
    // 0x7cc264: DecompressPointer r1
    //     0x7cc264: add             x1, x1, HEAP, lsl #32
    // 0x7cc268: LoadField: r2 = r1->field_13
    //     0x7cc268: ldur            w2, [x1, #0x13]
    // 0x7cc26c: r3 = LoadInt32Instr(r2)
    //     0x7cc26c: sbfx            x3, x2, #1, #0x1f
    // 0x7cc270: asr             x2, x3, #1
    // 0x7cc274: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cc274: ldur            w3, [x1, #0x17]
    // 0x7cc278: r1 = LoadInt32Instr(r3)
    //     0x7cc278: sbfx            x1, x3, #1, #0x1f
    // 0x7cc27c: sub             x3, x2, x1
    // 0x7cc280: cbz             x3, #0x7cc340
    // 0x7cc284: ldur            x1, [fp, #-8]
    // 0x7cc288: LoadField: r3 = r1->field_1b
    //     0x7cc288: ldur            w3, [x1, #0x1b]
    // 0x7cc28c: DecompressPointer r3
    //     0x7cc28c: add             x3, x3, HEAP, lsl #32
    // 0x7cc290: mov             x1, x3
    // 0x7cc294: stur            x3, [fp, #-0x18]
    // 0x7cc298: r2 = "/Resources"
    //     0x7cc298: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cc29c: ldr             x2, [x2, #0xf28]
    // 0x7cc2a0: r0 = contains()
    //     0x7cc2a0: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cc2a4: tbnz            w0, #4, #0x7cc2e8
    // 0x7cc2a8: ldur            x1, [fp, #-0x18]
    // 0x7cc2ac: r2 = "/Resources"
    //     0x7cc2ac: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cc2b0: ldr             x2, [x2, #0xf28]
    // 0x7cc2b4: r0 = getClipPath()
    //     0x7cc2b4: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7cc2b8: r1 = LoadClassIdInstr(r0)
    //     0x7cc2b8: ldur            x1, [x0, #-1]
    //     0x7cc2bc: ubfx            x1, x1, #0xc, #0x14
    // 0x7cc2c0: sub             x16, x1, #0x390
    // 0x7cc2c4: cmp             x16, #1
    // 0x7cc2c8: b.hi            #0x7cc2e8
    // 0x7cc2cc: mov             x1, x0
    // 0x7cc2d0: ldur            x2, [fp, #-0x10]
    // 0x7cc2d4: r0 = merge()
    //     0x7cc2d4: bl              #0x7caddc  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::merge
    // 0x7cc2d8: r0 = Null
    //     0x7cc2d8: mov             x0, NULL
    // 0x7cc2dc: LeaveFrame
    //     0x7cc2dc: mov             SP, fp
    //     0x7cc2e0: ldp             fp, lr, [SP], #0x10
    // 0x7cc2e4: ret
    //     0x7cc2e4: ret             
    // 0x7cc2e8: ldur            x3, [fp, #-0x18]
    // 0x7cc2ec: LoadField: r2 = r3->field_7
    //     0x7cc2ec: ldur            w2, [x3, #7]
    // 0x7cc2f0: DecompressPointer r2
    //     0x7cc2f0: add             x2, x2, HEAP, lsl #32
    // 0x7cc2f4: ldur            x0, [fp, #-0x10]
    // 0x7cc2f8: r1 = Null
    //     0x7cc2f8: mov             x1, NULL
    // 0x7cc2fc: cmp             w2, NULL
    // 0x7cc300: b.eq            #0x7cc324
    // 0x7cc304: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc304: ldur            w4, [x2, #0x17]
    // 0x7cc308: DecompressPointer r4
    //     0x7cc308: add             x4, x4, HEAP, lsl #32
    // 0x7cc30c: r8 = X0 bound PdfDataType
    //     0x7cc30c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc310: ldr             x8, [x8, #0x6d8]
    // 0x7cc314: LoadField: r9 = r4->field_7
    //     0x7cc314: ldur            x9, [x4, #7]
    // 0x7cc318: r3 = Null
    //     0x7cc318: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ef30] Null
    //     0x7cc31c: ldr             x3, [x3, #0xf30]
    // 0x7cc320: blr             x9
    // 0x7cc324: ldur            x0, [fp, #-0x18]
    // 0x7cc328: LoadField: r1 = r0->field_b
    //     0x7cc328: ldur            w1, [x0, #0xb]
    // 0x7cc32c: DecompressPointer r1
    //     0x7cc32c: add             x1, x1, HEAP, lsl #32
    // 0x7cc330: ldur            x3, [fp, #-0x10]
    // 0x7cc334: r2 = "/Resources"
    //     0x7cc334: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ef28] "/Resources"
    //     0x7cc338: ldr             x2, [x2, #0xf28]
    // 0x7cc33c: r0 = []=()
    //     0x7cc33c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc340: r0 = Null
    //     0x7cc340: mov             x0, NULL
    // 0x7cc344: LeaveFrame
    //     0x7cc344: mov             SP, fp
    //     0x7cc348: ldp             fp, lr, [SP], #0x10
    // 0x7cc34c: ret
    //     0x7cc34c: ret             
    // 0x7cc350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cc350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cc354: b               #0x7cbeac
  }
  _ _PdfPage&PdfObject&PdfGraphicStream(/* No info */) {
    // ** addr: 0xe8ad08, size: 0x128
    // 0xe8ad08: EnterFrame
    //     0xe8ad08: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ad0c: mov             fp, SP
    // 0xe8ad10: AllocStack(0x28)
    //     0xe8ad10: sub             SP, SP, #0x28
    // 0xe8ad14: r0 = false
    //     0xe8ad14: add             x0, NULL, #0x30  ; false
    // 0xe8ad18: stur            x1, [fp, #-8]
    // 0xe8ad1c: stur            x2, [fp, #-0x10]
    // 0xe8ad20: stur            x3, [fp, #-0x18]
    // 0xe8ad24: CheckStackOverflow
    //     0xe8ad24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ad28: cmp             SP, x16
    //     0xe8ad2c: b.ls            #0xe8ae28
    // 0xe8ad30: StoreField: r1->field_2b = r0
    //     0xe8ad30: stur            w0, [x1, #0x2b]
    // 0xe8ad34: StoreField: r1->field_2f = r0
    //     0xe8ad34: stur            w0, [x1, #0x2f]
    // 0xe8ad38: StoreField: r1->field_43 = r0
    //     0xe8ad38: stur            w0, [x1, #0x43]
    // 0xe8ad3c: r16 = <String, PdfFont>
    //     0xe8ad3c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36868] TypeArguments: <String, PdfFont>
    //     0xe8ad40: ldr             x16, [x16, #0x868]
    // 0xe8ad44: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe8ad48: stp             lr, x16, [SP]
    // 0xe8ad4c: r0 = Map._fromLiteral()
    //     0xe8ad4c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8ad50: ldur            x1, [fp, #-8]
    // 0xe8ad54: StoreField: r1->field_33 = r0
    //     0xe8ad54: stur            w0, [x1, #0x33]
    //     0xe8ad58: ldurb           w16, [x1, #-1]
    //     0xe8ad5c: ldurb           w17, [x0, #-1]
    //     0xe8ad60: and             x16, x17, x16, lsr #2
    //     0xe8ad64: tst             x16, HEAP, lsr #32
    //     0xe8ad68: b.eq            #0xe8ad70
    //     0xe8ad6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8ad70: r16 = <String, PdfShading>
    //     0xe8ad70: add             x16, PP, #0x36, lsl #12  ; [pp+0x36870] TypeArguments: <String, PdfShading>
    //     0xe8ad74: ldr             x16, [x16, #0x870]
    // 0xe8ad78: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe8ad7c: stp             lr, x16, [SP]
    // 0xe8ad80: r0 = Map._fromLiteral()
    //     0xe8ad80: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8ad84: ldur            x1, [fp, #-8]
    // 0xe8ad88: StoreField: r1->field_37 = r0
    //     0xe8ad88: stur            w0, [x1, #0x37]
    //     0xe8ad8c: ldurb           w16, [x1, #-1]
    //     0xe8ad90: ldurb           w17, [x0, #-1]
    //     0xe8ad94: and             x16, x17, x16, lsr #2
    //     0xe8ad98: tst             x16, HEAP, lsr #32
    //     0xe8ad9c: b.eq            #0xe8ada4
    //     0xe8ada0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8ada4: r16 = <String, PdfPattern>
    //     0xe8ada4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36878] TypeArguments: <String, PdfPattern>
    //     0xe8ada8: ldr             x16, [x16, #0x878]
    // 0xe8adac: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe8adb0: stp             lr, x16, [SP]
    // 0xe8adb4: r0 = Map._fromLiteral()
    //     0xe8adb4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8adb8: ldur            x1, [fp, #-8]
    // 0xe8adbc: StoreField: r1->field_3b = r0
    //     0xe8adbc: stur            w0, [x1, #0x3b]
    //     0xe8adc0: ldurb           w16, [x1, #-1]
    //     0xe8adc4: ldurb           w17, [x0, #-1]
    //     0xe8adc8: and             x16, x17, x16, lsr #2
    //     0xe8adcc: tst             x16, HEAP, lsr #32
    //     0xe8add0: b.eq            #0xe8add8
    //     0xe8add4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8add8: r16 = <String, PdfXObject>
    //     0xe8add8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36880] TypeArguments: <String, PdfXObject>
    //     0xe8addc: ldr             x16, [x16, #0x880]
    // 0xe8ade0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe8ade4: stp             lr, x16, [SP]
    // 0xe8ade8: r0 = Map._fromLiteral()
    //     0xe8ade8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8adec: ldur            x1, [fp, #-8]
    // 0xe8adf0: StoreField: r1->field_3f = r0
    //     0xe8adf0: stur            w0, [x1, #0x3f]
    //     0xe8adf4: ldurb           w16, [x1, #-1]
    //     0xe8adf8: ldurb           w17, [x0, #-1]
    //     0xe8adfc: and             x16, x17, x16, lsr #2
    //     0xe8ae00: tst             x16, HEAP, lsr #32
    //     0xe8ae04: b.eq            #0xe8ae0c
    //     0xe8ae08: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8ae0c: ldur            x2, [fp, #-0x10]
    // 0xe8ae10: ldur            x3, [fp, #-0x18]
    // 0xe8ae14: r0 = PdfObject()
    //     0xe8ae14: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe8ae18: r0 = Null
    //     0xe8ae18: mov             x0, NULL
    // 0xe8ae1c: LeaveFrame
    //     0xe8ae1c: mov             SP, fp
    //     0xe8ae20: ldp             fp, lr, [SP], #0x10
    // 0xe8ae24: ret
    //     0xe8ae24: ret             
    // 0xe8ae28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ae28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ae2c: b               #0xe8ad30
  }
  get _ altered(/* No info */) {
    // ** addr: 0xea1090, size: 0xc
    // 0xea1090: LoadField: r0 = r1->field_43
    //     0xea1090: ldur            w0, [x1, #0x43]
    // 0xea1094: DecompressPointer r0
    //     0xea1094: add             x0, x0, HEAP, lsl #32
    // 0xea1098: ret
    //     0xea1098: ret             
  }
  _ addFont(/* No info */) {
    // ** addr: 0xea109c, size: 0xd8
    // 0xea109c: EnterFrame
    //     0xea109c: stp             fp, lr, [SP, #-0x10]!
    //     0xea10a0: mov             fp, SP
    // 0xea10a4: AllocStack(0x20)
    //     0xea10a4: sub             SP, SP, #0x20
    // 0xea10a8: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea10a8: mov             x3, x2
    //     0xea10ac: stur            x2, [fp, #-0x10]
    // 0xea10b0: CheckStackOverflow
    //     0xea10b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea10b4: cmp             SP, x16
    //     0xea10b8: b.ls            #0xea116c
    // 0xea10bc: LoadField: r0 = r1->field_33
    //     0xea10bc: ldur            w0, [x1, #0x33]
    // 0xea10c0: DecompressPointer r0
    //     0xea10c0: add             x0, x0, HEAP, lsl #32
    // 0xea10c4: stur            x0, [fp, #-8]
    // 0xea10c8: r1 = Null
    //     0xea10c8: mov             x1, NULL
    // 0xea10cc: r2 = 4
    //     0xea10cc: movz            x2, #0x4
    // 0xea10d0: r0 = AllocateArray()
    //     0xea10d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea10d4: mov             x2, x0
    // 0xea10d8: r16 = "/F"
    //     0xea10d8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0xea10dc: ldr             x16, [x16, #0xda0]
    // 0xea10e0: StoreField: r2->field_f = r16
    //     0xea10e0: stur            w16, [x2, #0xf]
    // 0xea10e4: ldur            x3, [fp, #-0x10]
    // 0xea10e8: LoadField: r4 = r3->field_b
    //     0xea10e8: ldur            x4, [x3, #0xb]
    // 0xea10ec: r0 = BoxInt64Instr(r4)
    //     0xea10ec: sbfiz           x0, x4, #1, #0x1f
    //     0xea10f0: cmp             x4, x0, asr #1
    //     0xea10f4: b.eq            #0xea1100
    //     0xea10f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea10fc: stur            x4, [x0, #7]
    // 0xea1100: stur            x0, [fp, #-0x18]
    // 0xea1104: StoreField: r2->field_13 = r0
    //     0xea1104: stur            w0, [x2, #0x13]
    // 0xea1108: str             x2, [SP]
    // 0xea110c: r0 = _interpolate()
    //     0xea110c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea1110: ldur            x1, [fp, #-8]
    // 0xea1114: mov             x2, x0
    // 0xea1118: r0 = containsKey()
    //     0xea1118: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea111c: tbz             w0, #4, #0xea115c
    // 0xea1120: ldur            x0, [fp, #-0x18]
    // 0xea1124: r1 = Null
    //     0xea1124: mov             x1, NULL
    // 0xea1128: r2 = 4
    //     0xea1128: movz            x2, #0x4
    // 0xea112c: r0 = AllocateArray()
    //     0xea112c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1130: r16 = "/F"
    //     0xea1130: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da0] "/F"
    //     0xea1134: ldr             x16, [x16, #0xda0]
    // 0xea1138: StoreField: r0->field_f = r16
    //     0xea1138: stur            w16, [x0, #0xf]
    // 0xea113c: ldur            x1, [fp, #-0x18]
    // 0xea1140: StoreField: r0->field_13 = r1
    //     0xea1140: stur            w1, [x0, #0x13]
    // 0xea1144: str             x0, [SP]
    // 0xea1148: r0 = _interpolate()
    //     0xea1148: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea114c: ldur            x1, [fp, #-8]
    // 0xea1150: mov             x2, x0
    // 0xea1154: ldur            x3, [fp, #-0x10]
    // 0xea1158: r0 = []=()
    //     0xea1158: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea115c: r0 = Null
    //     0xea115c: mov             x0, NULL
    // 0xea1160: LeaveFrame
    //     0xea1160: mov             SP, fp
    //     0xea1164: ldp             fp, lr, [SP], #0x10
    // 0xea1168: ret
    //     0xea1168: ret             
    // 0xea116c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea116c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1170: b               #0xea10bc
  }
  _ addXObject(/* No info */) {
    // ** addr: 0xea1174, size: 0xd8
    // 0xea1174: EnterFrame
    //     0xea1174: stp             fp, lr, [SP, #-0x10]!
    //     0xea1178: mov             fp, SP
    // 0xea117c: AllocStack(0x20)
    //     0xea117c: sub             SP, SP, #0x20
    // 0xea1180: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea1180: mov             x3, x2
    //     0xea1184: stur            x2, [fp, #-0x10]
    // 0xea1188: CheckStackOverflow
    //     0xea1188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea118c: cmp             SP, x16
    //     0xea1190: b.ls            #0xea1244
    // 0xea1194: LoadField: r0 = r1->field_3f
    //     0xea1194: ldur            w0, [x1, #0x3f]
    // 0xea1198: DecompressPointer r0
    //     0xea1198: add             x0, x0, HEAP, lsl #32
    // 0xea119c: stur            x0, [fp, #-8]
    // 0xea11a0: r1 = Null
    //     0xea11a0: mov             x1, NULL
    // 0xea11a4: r2 = 4
    //     0xea11a4: movz            x2, #0x4
    // 0xea11a8: r0 = AllocateArray()
    //     0xea11a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea11ac: mov             x2, x0
    // 0xea11b0: r16 = "/I"
    //     0xea11b0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xea11b4: ldr             x16, [x16, #0xbe0]
    // 0xea11b8: StoreField: r2->field_f = r16
    //     0xea11b8: stur            w16, [x2, #0xf]
    // 0xea11bc: ldur            x3, [fp, #-0x10]
    // 0xea11c0: LoadField: r4 = r3->field_b
    //     0xea11c0: ldur            x4, [x3, #0xb]
    // 0xea11c4: r0 = BoxInt64Instr(r4)
    //     0xea11c4: sbfiz           x0, x4, #1, #0x1f
    //     0xea11c8: cmp             x4, x0, asr #1
    //     0xea11cc: b.eq            #0xea11d8
    //     0xea11d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea11d4: stur            x4, [x0, #7]
    // 0xea11d8: stur            x0, [fp, #-0x18]
    // 0xea11dc: StoreField: r2->field_13 = r0
    //     0xea11dc: stur            w0, [x2, #0x13]
    // 0xea11e0: str             x2, [SP]
    // 0xea11e4: r0 = _interpolate()
    //     0xea11e4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea11e8: ldur            x1, [fp, #-8]
    // 0xea11ec: mov             x2, x0
    // 0xea11f0: r0 = containsKey()
    //     0xea11f0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea11f4: tbz             w0, #4, #0xea1234
    // 0xea11f8: ldur            x0, [fp, #-0x18]
    // 0xea11fc: r1 = Null
    //     0xea11fc: mov             x1, NULL
    // 0xea1200: r2 = 4
    //     0xea1200: movz            x2, #0x4
    // 0xea1204: r0 = AllocateArray()
    //     0xea1204: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1208: r16 = "/I"
    //     0xea1208: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ebe0] "/I"
    //     0xea120c: ldr             x16, [x16, #0xbe0]
    // 0xea1210: StoreField: r0->field_f = r16
    //     0xea1210: stur            w16, [x0, #0xf]
    // 0xea1214: ldur            x1, [fp, #-0x18]
    // 0xea1218: StoreField: r0->field_13 = r1
    //     0xea1218: stur            w1, [x0, #0x13]
    // 0xea121c: str             x0, [SP]
    // 0xea1220: r0 = _interpolate()
    //     0xea1220: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea1224: ldur            x1, [fp, #-8]
    // 0xea1228: mov             x2, x0
    // 0xea122c: ldur            x3, [fp, #-0x10]
    // 0xea1230: r0 = []=()
    //     0xea1230: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea1234: r0 = Null
    //     0xea1234: mov             x0, NULL
    // 0xea1238: LeaveFrame
    //     0xea1238: mov             SP, fp
    //     0xea123c: ldp             fp, lr, [SP], #0x10
    // 0xea1240: ret
    //     0xea1240: ret             
    // 0xea1244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1244: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1248: b               #0xea1194
  }
  _ stateName(/* No info */) {
    // ** addr: 0xea124c, size: 0x4c
    // 0xea124c: EnterFrame
    //     0xea124c: stp             fp, lr, [SP, #-0x10]!
    //     0xea1250: mov             fp, SP
    // 0xea1254: AllocStack(0x8)
    //     0xea1254: sub             SP, SP, #8
    // 0xea1258: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xea1258: stur            x2, [fp, #-8]
    // 0xea125c: CheckStackOverflow
    //     0xea125c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1260: cmp             SP, x16
    //     0xea1264: b.ls            #0xea1290
    // 0xea1268: LoadField: r0 = r1->field_23
    //     0xea1268: ldur            w0, [x1, #0x23]
    // 0xea126c: DecompressPointer r0
    //     0xea126c: add             x0, x0, HEAP, lsl #32
    // 0xea1270: mov             x1, x0
    // 0xea1274: r0 = graphicStates()
    //     0xea1274: bl              #0x7cb36c  ; [package:pdf/src/pdf/document.dart] PdfDocument::graphicStates
    // 0xea1278: mov             x1, x0
    // 0xea127c: ldur            x2, [fp, #-8]
    // 0xea1280: r0 = stateName()
    //     0xea1280: bl              #0xea1298  ; [package:pdf/src/pdf/graphic_state.dart] PdfGraphicStates::stateName
    // 0xea1284: LeaveFrame
    //     0xea1284: mov             SP, fp
    //     0xea1288: ldp             fp, lr, [SP], #0x10
    // 0xea128c: ret
    //     0xea128c: ret             
    // 0xea1290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1294: b               #0xea1268
  }
  _ addPattern(/* No info */) {
    // ** addr: 0xea1400, size: 0xd8
    // 0xea1400: EnterFrame
    //     0xea1400: stp             fp, lr, [SP, #-0x10]!
    //     0xea1404: mov             fp, SP
    // 0xea1408: AllocStack(0x20)
    //     0xea1408: sub             SP, SP, #0x20
    // 0xea140c: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xea140c: mov             x3, x2
    //     0xea1410: stur            x2, [fp, #-0x10]
    // 0xea1414: CheckStackOverflow
    //     0xea1414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1418: cmp             SP, x16
    //     0xea141c: b.ls            #0xea14d0
    // 0xea1420: LoadField: r0 = r1->field_3b
    //     0xea1420: ldur            w0, [x1, #0x3b]
    // 0xea1424: DecompressPointer r0
    //     0xea1424: add             x0, x0, HEAP, lsl #32
    // 0xea1428: stur            x0, [fp, #-8]
    // 0xea142c: r1 = Null
    //     0xea142c: mov             x1, NULL
    // 0xea1430: r2 = 4
    //     0xea1430: movz            x2, #0x4
    // 0xea1434: r0 = AllocateArray()
    //     0xea1434: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1438: mov             x2, x0
    // 0xea143c: r16 = "/P"
    //     0xea143c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea1440: ldr             x16, [x16, #0x248]
    // 0xea1444: StoreField: r2->field_f = r16
    //     0xea1444: stur            w16, [x2, #0xf]
    // 0xea1448: ldur            x3, [fp, #-0x10]
    // 0xea144c: LoadField: r4 = r3->field_b
    //     0xea144c: ldur            x4, [x3, #0xb]
    // 0xea1450: r0 = BoxInt64Instr(r4)
    //     0xea1450: sbfiz           x0, x4, #1, #0x1f
    //     0xea1454: cmp             x4, x0, asr #1
    //     0xea1458: b.eq            #0xea1464
    //     0xea145c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea1460: stur            x4, [x0, #7]
    // 0xea1464: stur            x0, [fp, #-0x18]
    // 0xea1468: StoreField: r2->field_13 = r0
    //     0xea1468: stur            w0, [x2, #0x13]
    // 0xea146c: str             x2, [SP]
    // 0xea1470: r0 = _interpolate()
    //     0xea1470: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea1474: ldur            x1, [fp, #-8]
    // 0xea1478: mov             x2, x0
    // 0xea147c: r0 = containsKey()
    //     0xea147c: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xea1480: tbz             w0, #4, #0xea14c0
    // 0xea1484: ldur            x0, [fp, #-0x18]
    // 0xea1488: r1 = Null
    //     0xea1488: mov             x1, NULL
    // 0xea148c: r2 = 4
    //     0xea148c: movz            x2, #0x4
    // 0xea1490: r0 = AllocateArray()
    //     0xea1490: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1494: r16 = "/P"
    //     0xea1494: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea1498: ldr             x16, [x16, #0x248]
    // 0xea149c: StoreField: r0->field_f = r16
    //     0xea149c: stur            w16, [x0, #0xf]
    // 0xea14a0: ldur            x1, [fp, #-0x18]
    // 0xea14a4: StoreField: r0->field_13 = r1
    //     0xea14a4: stur            w1, [x0, #0x13]
    // 0xea14a8: str             x0, [SP]
    // 0xea14ac: r0 = _interpolate()
    //     0xea14ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea14b0: ldur            x1, [fp, #-8]
    // 0xea14b4: mov             x2, x0
    // 0xea14b8: ldur            x3, [fp, #-0x10]
    // 0xea14bc: r0 = []=()
    //     0xea14bc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xea14c0: r0 = Null
    //     0xea14c0: mov             x0, NULL
    // 0xea14c4: LeaveFrame
    //     0xea14c4: mov             SP, fp
    //     0xea14c8: ldp             fp, lr, [SP], #0x10
    // 0xea14cc: ret
    //     0xea14cc: ret             
    // 0xea14d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea14d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea14d4: b               #0xea1420
  }
  set _ altered=(/* No info */) {
    // ** addr: 0xea14d8, size: 0xc
    // 0xea14d8: r0 = true
    //     0xea14d8: add             x0, NULL, #0x20  ; true
    // 0xea14dc: StoreField: r1->field_43 = r0
    //     0xea14dc: stur            w0, [x1, #0x43]
    // 0xea14e0: ret
    //     0xea14e0: ret             
  }
}

// class id: 878, size: 0x5c, field offset: 0x48
class PdfPage extends _PdfPage&PdfObject&PdfGraphicStream {

  _ prepare(/* No info */) {
    // ** addr: 0x7cb930, size: 0x51c
    // 0x7cb930: EnterFrame
    //     0x7cb930: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb934: mov             fp, SP
    // 0x7cb938: AllocStack(0x58)
    //     0x7cb938: sub             SP, SP, #0x58
    // 0x7cb93c: SetupParameters(PdfPage this /* r1 => r0, fp-0x8 */)
    //     0x7cb93c: mov             x0, x1
    //     0x7cb940: stur            x1, [fp, #-8]
    // 0x7cb944: CheckStackOverflow
    //     0x7cb944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb948: cmp             SP, x16
    //     0x7cb94c: b.ls            #0x7cbe28
    // 0x7cb950: mov             x1, x0
    // 0x7cb954: r0 = prepare()
    //     0x7cb954: bl              #0x7cbe8c  ; [package:pdf/src/pdf/obj/page.dart] _PdfPage&PdfObject&PdfGraphicStream::prepare
    // 0x7cb958: ldur            x0, [fp, #-8]
    // 0x7cb95c: LoadField: r2 = r0->field_1b
    //     0x7cb95c: ldur            w2, [x0, #0x1b]
    // 0x7cb960: DecompressPointer r2
    //     0x7cb960: add             x2, x2, HEAP, lsl #32
    // 0x7cb964: stur            x2, [fp, #-0x10]
    // 0x7cb968: LoadField: r1 = r0->field_23
    //     0x7cb968: ldur            w1, [x0, #0x23]
    // 0x7cb96c: DecompressPointer r1
    //     0x7cb96c: add             x1, x1, HEAP, lsl #32
    // 0x7cb970: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x7cb970: ldur            w3, [x1, #0x17]
    // 0x7cb974: DecompressPointer r3
    //     0x7cb974: add             x3, x3, HEAP, lsl #32
    // 0x7cb978: r16 = Sentinel
    //     0x7cb978: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7cb97c: cmp             w3, w16
    // 0x7cb980: b.eq            #0x7cbe30
    // 0x7cb984: LoadField: r1 = r3->field_2b
    //     0x7cb984: ldur            w1, [x3, #0x2b]
    // 0x7cb988: DecompressPointer r1
    //     0x7cb988: add             x1, x1, HEAP, lsl #32
    // 0x7cb98c: r0 = ref()
    //     0x7cb98c: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cb990: mov             x4, x0
    // 0x7cb994: ldur            x3, [fp, #-0x10]
    // 0x7cb998: stur            x4, [fp, #-0x20]
    // 0x7cb99c: LoadField: r5 = r3->field_7
    //     0x7cb99c: ldur            w5, [x3, #7]
    // 0x7cb9a0: DecompressPointer r5
    //     0x7cb9a0: add             x5, x5, HEAP, lsl #32
    // 0x7cb9a4: mov             x0, x4
    // 0x7cb9a8: mov             x2, x5
    // 0x7cb9ac: stur            x5, [fp, #-0x18]
    // 0x7cb9b0: r1 = Null
    //     0x7cb9b0: mov             x1, NULL
    // 0x7cb9b4: cmp             w2, NULL
    // 0x7cb9b8: b.eq            #0x7cb9dc
    // 0x7cb9bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cb9bc: ldur            w4, [x2, #0x17]
    // 0x7cb9c0: DecompressPointer r4
    //     0x7cb9c0: add             x4, x4, HEAP, lsl #32
    // 0x7cb9c4: r8 = X0 bound PdfDataType
    //     0x7cb9c4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cb9c8: ldr             x8, [x8, #0x6d8]
    // 0x7cb9cc: LoadField: r9 = r4->field_7
    //     0x7cb9cc: ldur            x9, [x4, #7]
    // 0x7cb9d0: r3 = Null
    //     0x7cb9d0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee08] Null
    //     0x7cb9d4: ldr             x3, [x3, #0xe08]
    // 0x7cb9d8: blr             x9
    // 0x7cb9dc: ldur            x0, [fp, #-0x10]
    // 0x7cb9e0: LoadField: r4 = r0->field_b
    //     0x7cb9e0: ldur            w4, [x0, #0xb]
    // 0x7cb9e4: DecompressPointer r4
    //     0x7cb9e4: add             x4, x4, HEAP, lsl #32
    // 0x7cb9e8: mov             x1, x4
    // 0x7cb9ec: ldur            x3, [fp, #-0x20]
    // 0x7cb9f0: stur            x4, [fp, #-0x28]
    // 0x7cb9f4: r2 = "/Parent"
    //     0x7cb9f4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee18] "/Parent"
    //     0x7cb9f8: ldr             x2, [x2, #0xe18]
    // 0x7cb9fc: r0 = []=()
    //     0x7cb9fc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cba00: r1 = Null
    //     0x7cba00: mov             x1, NULL
    // 0x7cba04: r2 = 8
    //     0x7cba04: movz            x2, #0x8
    // 0x7cba08: r0 = AllocateArray()
    //     0x7cba08: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cba0c: stur            x0, [fp, #-0x20]
    // 0x7cba10: r16 = 0.000000
    //     0x7cba10: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7cba14: StoreField: r0->field_f = r16
    //     0x7cba14: stur            w16, [x0, #0xf]
    // 0x7cba18: r16 = 0.000000
    //     0x7cba18: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7cba1c: StoreField: r0->field_13 = r16
    //     0x7cba1c: stur            w16, [x0, #0x13]
    // 0x7cba20: r16 = 595.275591
    //     0x7cba20: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ee20] 595.275590551181
    //     0x7cba24: ldr             x16, [x16, #0xe20]
    // 0x7cba28: ArrayStore: r0[0] = r16  ; List_4
    //     0x7cba28: stur            w16, [x0, #0x17]
    // 0x7cba2c: r16 = 841.889764
    //     0x7cba2c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ee28] 841.8897637795275
    //     0x7cba30: ldr             x16, [x16, #0xe28]
    // 0x7cba34: StoreField: r0->field_1b = r16
    //     0x7cba34: stur            w16, [x0, #0x1b]
    // 0x7cba38: r1 = <double>
    //     0x7cba38: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x7cba3c: r0 = AllocateGrowableArray()
    //     0x7cba3c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7cba40: mov             x1, x0
    // 0x7cba44: ldur            x0, [fp, #-0x20]
    // 0x7cba48: StoreField: r1->field_f = r0
    //     0x7cba48: stur            w0, [x1, #0xf]
    // 0x7cba4c: r0 = 8
    //     0x7cba4c: movz            x0, #0x8
    // 0x7cba50: StoreField: r1->field_b = r0
    //     0x7cba50: stur            w0, [x1, #0xb]
    // 0x7cba54: r0 = fromNum()
    //     0x7cba54: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7cba58: ldur            x2, [fp, #-0x18]
    // 0x7cba5c: mov             x3, x0
    // 0x7cba60: r1 = Null
    //     0x7cba60: mov             x1, NULL
    // 0x7cba64: stur            x3, [fp, #-0x20]
    // 0x7cba68: cmp             w2, NULL
    // 0x7cba6c: b.eq            #0x7cba90
    // 0x7cba70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cba70: ldur            w4, [x2, #0x17]
    // 0x7cba74: DecompressPointer r4
    //     0x7cba74: add             x4, x4, HEAP, lsl #32
    // 0x7cba78: r8 = X0 bound PdfDataType
    //     0x7cba78: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cba7c: ldr             x8, [x8, #0x6d8]
    // 0x7cba80: LoadField: r9 = r4->field_7
    //     0x7cba80: ldur            x9, [x4, #7]
    // 0x7cba84: r3 = Null
    //     0x7cba84: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee30] Null
    //     0x7cba88: ldr             x3, [x3, #0xe30]
    // 0x7cba8c: blr             x9
    // 0x7cba90: ldur            x1, [fp, #-0x28]
    // 0x7cba94: ldur            x3, [fp, #-0x20]
    // 0x7cba98: r2 = "/MediaBox"
    //     0x7cba98: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee40] "/MediaBox"
    //     0x7cba9c: ldr             x2, [x2, #0xe40]
    // 0x7cbaa0: r0 = []=()
    //     0x7cbaa0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbaa4: ldur            x0, [fp, #-8]
    // 0x7cbaa8: LoadField: r3 = r0->field_4f
    //     0x7cbaa8: ldur            w3, [x0, #0x4f]
    // 0x7cbaac: DecompressPointer r3
    //     0x7cbaac: add             x3, x3, HEAP, lsl #32
    // 0x7cbab0: stur            x3, [fp, #-0x48]
    // 0x7cbab4: LoadField: r1 = r3->field_b
    //     0x7cbab4: ldur            w1, [x3, #0xb]
    // 0x7cbab8: r4 = LoadInt32Instr(r1)
    //     0x7cbab8: sbfx            x4, x1, #1, #0x1f
    // 0x7cbabc: stur            x4, [fp, #-0x40]
    // 0x7cbac0: LoadField: r5 = r0->field_57
    //     0x7cbac0: ldur            w5, [x0, #0x57]
    // 0x7cbac4: DecompressPointer r5
    //     0x7cbac4: add             x5, x5, HEAP, lsl #32
    // 0x7cbac8: stur            x5, [fp, #-0x38]
    // 0x7cbacc: r1 = 0
    //     0x7cbacc: movz            x1, #0
    // 0x7cbad0: CheckStackOverflow
    //     0x7cbad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cbad4: cmp             SP, x16
    //     0x7cbad8: b.ls            #0x7cbe3c
    // 0x7cbadc: LoadField: r2 = r3->field_b
    //     0x7cbadc: ldur            w2, [x3, #0xb]
    // 0x7cbae0: r6 = LoadInt32Instr(r2)
    //     0x7cbae0: sbfx            x6, x2, #1, #0x1f
    // 0x7cbae4: cmp             x4, x6
    // 0x7cbae8: b.ne            #0x7cbe08
    // 0x7cbaec: cmp             x1, x6
    // 0x7cbaf0: b.ge            #0x7cbb8c
    // 0x7cbaf4: LoadField: r2 = r3->field_f
    //     0x7cbaf4: ldur            w2, [x3, #0xf]
    // 0x7cbaf8: DecompressPointer r2
    //     0x7cbaf8: add             x2, x2, HEAP, lsl #32
    // 0x7cbafc: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0x7cbafc: add             x16, x2, x1, lsl #2
    //     0x7cbb00: ldur            w6, [x16, #0xf]
    // 0x7cbb04: DecompressPointer r6
    //     0x7cbb04: add             x6, x6, HEAP, lsl #32
    // 0x7cbb08: stur            x6, [fp, #-0x20]
    // 0x7cbb0c: add             x7, x1, #1
    // 0x7cbb10: mov             x1, x5
    // 0x7cbb14: mov             x2, x6
    // 0x7cbb18: stur            x7, [fp, #-0x30]
    // 0x7cbb1c: r0 = _getValueOrData()
    //     0x7cbb1c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7cbb20: ldur            x2, [fp, #-0x38]
    // 0x7cbb24: LoadField: r1 = r2->field_f
    //     0x7cbb24: ldur            w1, [x2, #0xf]
    // 0x7cbb28: DecompressPointer r1
    //     0x7cbb28: add             x1, x1, HEAP, lsl #32
    // 0x7cbb2c: cmp             w1, w0
    // 0x7cbb30: b.ne            #0x7cbb38
    // 0x7cbb34: r0 = Null
    //     0x7cbb34: mov             x0, NULL
    // 0x7cbb38: cmp             w0, NULL
    // 0x7cbb3c: b.eq            #0x7cbe44
    // 0x7cbb40: LoadField: r1 = r0->field_f
    //     0x7cbb40: ldur            w1, [x0, #0xf]
    // 0x7cbb44: DecompressPointer r1
    //     0x7cbb44: add             x1, x1, HEAP, lsl #32
    // 0x7cbb48: r0 = LoadClassIdInstr(r1)
    //     0x7cbb48: ldur            x0, [x1, #-1]
    //     0x7cbb4c: ubfx            x0, x0, #0xc, #0x14
    // 0x7cbb50: r0 = GDT[cid_x0 + -0xffb]()
    //     0x7cbb50: sub             lr, x0, #0xffb
    //     0x7cbb54: ldr             lr, [x21, lr, lsl #3]
    //     0x7cbb58: blr             lr
    // 0x7cbb5c: tbz             w0, #4, #0x7cbb70
    // 0x7cbb60: ldur            x1, [fp, #-0x20]
    // 0x7cbb64: r0 = false
    //     0x7cbb64: add             x0, NULL, #0x30  ; false
    // 0x7cbb68: StoreField: r1->field_27 = r0
    //     0x7cbb68: stur            w0, [x1, #0x27]
    // 0x7cbb6c: b               #0x7cbb74
    // 0x7cbb70: r0 = false
    //     0x7cbb70: add             x0, NULL, #0x30  ; false
    // 0x7cbb74: ldur            x1, [fp, #-0x30]
    // 0x7cbb78: ldur            x0, [fp, #-8]
    // 0x7cbb7c: ldur            x3, [fp, #-0x48]
    // 0x7cbb80: ldur            x5, [fp, #-0x38]
    // 0x7cbb84: ldur            x4, [fp, #-0x40]
    // 0x7cbb88: b               #0x7cbad0
    // 0x7cbb8c: r1 = Function '<anonymous closure>':.
    //     0x7cbb8c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ee48] AnonymousClosure: static (0x5fb618), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onBackground (0x5fb628)
    //     0x7cbb90: ldr             x1, [x1, #0xe48]
    // 0x7cbb94: r2 = Null
    //     0x7cbb94: mov             x2, NULL
    // 0x7cbb98: r0 = AllocateClosure()
    //     0x7cbb98: bl              #0xec1630  ; AllocateClosureStub
    // 0x7cbb9c: ldur            x1, [fp, #-0x48]
    // 0x7cbba0: mov             x2, x0
    // 0x7cbba4: r0 = where()
    //     0x7cbba4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x7cbba8: LoadField: r1 = r0->field_7
    //     0x7cbba8: ldur            w1, [x0, #7]
    // 0x7cbbac: DecompressPointer r1
    //     0x7cbbac: add             x1, x1, HEAP, lsl #32
    // 0x7cbbb0: mov             x2, x0
    // 0x7cbbb4: r0 = _GrowableList.of()
    //     0x7cbbb4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7cbbb8: mov             x1, x0
    // 0x7cbbbc: r0 = fromObjects()
    //     0x7cbbbc: bl              #0x7ca4a4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects
    // 0x7cbbc0: ldur            x1, [fp, #-0x10]
    // 0x7cbbc4: r2 = "/Contents"
    //     0x7cbbc4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee50] "/Contents"
    //     0x7cbbc8: ldr             x2, [x2, #0xe50]
    // 0x7cbbcc: stur            x0, [fp, #-0x20]
    // 0x7cbbd0: r0 = contains()
    //     0x7cbbd0: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cbbd4: tbnz            w0, #4, #0x7cbc64
    // 0x7cbbd8: ldur            x1, [fp, #-0x10]
    // 0x7cbbdc: r2 = "/Contents"
    //     0x7cbbdc: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee50] "/Contents"
    //     0x7cbbe0: ldr             x2, [x2, #0xe50]
    // 0x7cbbe4: r0 = getClipPath()
    //     0x7cbbe4: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7cbbe8: cmp             w0, NULL
    // 0x7cbbec: b.eq            #0x7cbe48
    // 0x7cbbf0: r1 = LoadClassIdInstr(r0)
    //     0x7cbbf0: ldur            x1, [x0, #-1]
    //     0x7cbbf4: ubfx            x1, x1, #0xc, #0x14
    // 0x7cbbf8: cmp             x1, #0x393
    // 0x7cbbfc: b.ne            #0x7cbc40
    // 0x7cbc00: ldur            x1, [fp, #-0x20]
    // 0x7cbc04: LoadField: r2 = r1->field_b
    //     0x7cbc04: ldur            w2, [x1, #0xb]
    // 0x7cbc08: DecompressPointer r2
    //     0x7cbc08: add             x2, x2, HEAP, lsl #32
    // 0x7cbc0c: stur            x2, [fp, #-0x38]
    // 0x7cbc10: LoadField: r3 = r0->field_b
    //     0x7cbc10: ldur            w3, [x0, #0xb]
    // 0x7cbc14: DecompressPointer r3
    //     0x7cbc14: add             x3, x3, HEAP, lsl #32
    // 0x7cbc18: r16 = <PdfIndirect>
    //     0x7cbc18: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] TypeArguments: <PdfIndirect>
    //     0x7cbc1c: ldr             x16, [x16, #0xa0]
    // 0x7cbc20: stp             x3, x16, [SP]
    // 0x7cbc24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7cbc24: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7cbc28: r0 = whereType()
    //     0x7cbc28: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x7cbc2c: ldur            x1, [fp, #-0x38]
    // 0x7cbc30: mov             x3, x0
    // 0x7cbc34: r2 = 0
    //     0x7cbc34: movz            x2, #0
    // 0x7cbc38: r0 = insertAll()
    //     0x7cbc38: bl              #0x64d8c4  ; [dart:core] _GrowableList::insertAll
    // 0x7cbc3c: b               #0x7cbc64
    // 0x7cbc40: sub             x16, x1, #0x38e
    // 0x7cbc44: cmp             x16, #1
    // 0x7cbc48: b.hi            #0x7cbc64
    // 0x7cbc4c: ldur            x4, [fp, #-0x20]
    // 0x7cbc50: LoadField: r1 = r4->field_b
    //     0x7cbc50: ldur            w1, [x4, #0xb]
    // 0x7cbc54: DecompressPointer r1
    //     0x7cbc54: add             x1, x1, HEAP, lsl #32
    // 0x7cbc58: mov             x3, x0
    // 0x7cbc5c: r2 = 0
    //     0x7cbc5c: movz            x2, #0
    // 0x7cbc60: r0 = insert()
    //     0x7cbc60: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0x7cbc64: ldur            x0, [fp, #-0x20]
    // 0x7cbc68: mov             x1, x0
    // 0x7cbc6c: r0 = uniq()
    //     0x7cbc6c: bl              #0x7cb134  ; [package:pdf/src/pdf/format/array.dart] PdfArray::uniq
    // 0x7cbc70: ldur            x0, [fp, #-0x20]
    // 0x7cbc74: LoadField: r1 = r0->field_b
    //     0x7cbc74: ldur            w1, [x0, #0xb]
    // 0x7cbc78: DecompressPointer r1
    //     0x7cbc78: add             x1, x1, HEAP, lsl #32
    // 0x7cbc7c: LoadField: r2 = r1->field_b
    //     0x7cbc7c: ldur            w2, [x1, #0xb]
    // 0x7cbc80: cmp             w2, #2
    // 0x7cbc84: b.ne            #0x7cbcdc
    // 0x7cbc88: r0 = first()
    //     0x7cbc88: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x7cbc8c: ldur            x2, [fp, #-0x18]
    // 0x7cbc90: mov             x3, x0
    // 0x7cbc94: r1 = Null
    //     0x7cbc94: mov             x1, NULL
    // 0x7cbc98: stur            x3, [fp, #-0x38]
    // 0x7cbc9c: cmp             w2, NULL
    // 0x7cbca0: b.eq            #0x7cbcc4
    // 0x7cbca4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cbca4: ldur            w4, [x2, #0x17]
    // 0x7cbca8: DecompressPointer r4
    //     0x7cbca8: add             x4, x4, HEAP, lsl #32
    // 0x7cbcac: r8 = X0 bound PdfDataType
    //     0x7cbcac: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cbcb0: ldr             x8, [x8, #0x6d8]
    // 0x7cbcb4: LoadField: r9 = r4->field_7
    //     0x7cbcb4: ldur            x9, [x4, #7]
    // 0x7cbcb8: r3 = Null
    //     0x7cbcb8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee58] Null
    //     0x7cbcbc: ldr             x3, [x3, #0xe58]
    // 0x7cbcc0: blr             x9
    // 0x7cbcc4: ldur            x1, [fp, #-0x28]
    // 0x7cbcc8: ldur            x3, [fp, #-0x38]
    // 0x7cbccc: r2 = "/Contents"
    //     0x7cbccc: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee50] "/Contents"
    //     0x7cbcd0: ldr             x2, [x2, #0xe50]
    // 0x7cbcd4: r0 = []=()
    //     0x7cbcd4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbcd8: b               #0x7cbd30
    // 0x7cbcdc: mov             x1, x0
    // 0x7cbce0: r0 = isNotEmpty()
    //     0x7cbce0: bl              #0x89b0ec  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::isNotEmpty
    // 0x7cbce4: tbnz            w0, #4, #0x7cbd30
    // 0x7cbce8: ldur            x0, [fp, #-0x20]
    // 0x7cbcec: ldur            x2, [fp, #-0x18]
    // 0x7cbcf0: r1 = Null
    //     0x7cbcf0: mov             x1, NULL
    // 0x7cbcf4: cmp             w2, NULL
    // 0x7cbcf8: b.eq            #0x7cbd1c
    // 0x7cbcfc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cbcfc: ldur            w4, [x2, #0x17]
    // 0x7cbd00: DecompressPointer r4
    //     0x7cbd00: add             x4, x4, HEAP, lsl #32
    // 0x7cbd04: r8 = X0 bound PdfDataType
    //     0x7cbd04: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cbd08: ldr             x8, [x8, #0x6d8]
    // 0x7cbd0c: LoadField: r9 = r4->field_7
    //     0x7cbd0c: ldur            x9, [x4, #7]
    // 0x7cbd10: r3 = Null
    //     0x7cbd10: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee68] Null
    //     0x7cbd14: ldr             x3, [x3, #0xe68]
    // 0x7cbd18: blr             x9
    // 0x7cbd1c: ldur            x1, [fp, #-0x28]
    // 0x7cbd20: ldur            x3, [fp, #-0x20]
    // 0x7cbd24: r2 = "/Contents"
    //     0x7cbd24: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee50] "/Contents"
    //     0x7cbd28: ldr             x2, [x2, #0xe50]
    // 0x7cbd2c: r0 = []=()
    //     0x7cbd2c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbd30: ldur            x0, [fp, #-8]
    // 0x7cbd34: LoadField: r3 = r0->field_53
    //     0x7cbd34: ldur            w3, [x0, #0x53]
    // 0x7cbd38: DecompressPointer r3
    //     0x7cbd38: add             x3, x3, HEAP, lsl #32
    // 0x7cbd3c: stur            x3, [fp, #-0x20]
    // 0x7cbd40: LoadField: r0 = r3->field_b
    //     0x7cbd40: ldur            w0, [x3, #0xb]
    // 0x7cbd44: cbz             w0, #0x7cbdf8
    // 0x7cbd48: ldur            x1, [fp, #-0x10]
    // 0x7cbd4c: r2 = "/Annots"
    //     0x7cbd4c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee78] "/Annots"
    //     0x7cbd50: ldr             x2, [x2, #0xe78]
    // 0x7cbd54: r0 = contains()
    //     0x7cbd54: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x7cbd58: tbnz            w0, #4, #0x7cbda4
    // 0x7cbd5c: ldur            x1, [fp, #-0x10]
    // 0x7cbd60: r2 = "/Annots"
    //     0x7cbd60: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee78] "/Annots"
    //     0x7cbd64: ldr             x2, [x2, #0xe78]
    // 0x7cbd68: r0 = getClipPath()
    //     0x7cbd68: bl              #0x7b5634  ; [package:flutter_svg/src/vector_drawable.dart] DrawableDefinitionServer::getClipPath
    // 0x7cbd6c: r1 = LoadClassIdInstr(r0)
    //     0x7cbd6c: ldur            x1, [x0, #-1]
    //     0x7cbd70: ubfx            x1, x1, #0xc, #0x14
    // 0x7cbd74: cmp             x1, #0x393
    // 0x7cbd78: b.ne            #0x7cbdf8
    // 0x7cbd7c: LoadField: r2 = r0->field_b
    //     0x7cbd7c: ldur            w2, [x0, #0xb]
    // 0x7cbd80: DecompressPointer r2
    //     0x7cbd80: add             x2, x2, HEAP, lsl #32
    // 0x7cbd84: ldur            x1, [fp, #-0x20]
    // 0x7cbd88: stur            x2, [fp, #-8]
    // 0x7cbd8c: r0 = fromObjects()
    //     0x7cbd8c: bl              #0x7ca4a4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects
    // 0x7cbd90: LoadField: r2 = r0->field_b
    //     0x7cbd90: ldur            w2, [x0, #0xb]
    // 0x7cbd94: DecompressPointer r2
    //     0x7cbd94: add             x2, x2, HEAP, lsl #32
    // 0x7cbd98: ldur            x1, [fp, #-8]
    // 0x7cbd9c: r0 = addAll()
    //     0x7cbd9c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7cbda0: b               #0x7cbdf8
    // 0x7cbda4: ldur            x1, [fp, #-0x20]
    // 0x7cbda8: r0 = fromObjects()
    //     0x7cbda8: bl              #0x7ca4a4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects
    // 0x7cbdac: ldur            x2, [fp, #-0x18]
    // 0x7cbdb0: mov             x3, x0
    // 0x7cbdb4: r1 = Null
    //     0x7cbdb4: mov             x1, NULL
    // 0x7cbdb8: stur            x3, [fp, #-8]
    // 0x7cbdbc: cmp             w2, NULL
    // 0x7cbdc0: b.eq            #0x7cbde4
    // 0x7cbdc4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cbdc4: ldur            w4, [x2, #0x17]
    // 0x7cbdc8: DecompressPointer r4
    //     0x7cbdc8: add             x4, x4, HEAP, lsl #32
    // 0x7cbdcc: r8 = X0 bound PdfDataType
    //     0x7cbdcc: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cbdd0: ldr             x8, [x8, #0x6d8]
    // 0x7cbdd4: LoadField: r9 = r4->field_7
    //     0x7cbdd4: ldur            x9, [x4, #7]
    // 0x7cbdd8: r3 = Null
    //     0x7cbdd8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ee80] Null
    //     0x7cbddc: ldr             x3, [x3, #0xe80]
    // 0x7cbde0: blr             x9
    // 0x7cbde4: ldur            x1, [fp, #-0x28]
    // 0x7cbde8: ldur            x3, [fp, #-8]
    // 0x7cbdec: r2 = "/Annots"
    //     0x7cbdec: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ee78] "/Annots"
    //     0x7cbdf0: ldr             x2, [x2, #0xe78]
    // 0x7cbdf4: r0 = []=()
    //     0x7cbdf4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cbdf8: r0 = Null
    //     0x7cbdf8: mov             x0, NULL
    // 0x7cbdfc: LeaveFrame
    //     0x7cbdfc: mov             SP, fp
    //     0x7cbe00: ldp             fp, lr, [SP], #0x10
    // 0x7cbe04: ret
    //     0x7cbe04: ret             
    // 0x7cbe08: mov             x0, x3
    // 0x7cbe0c: r0 = ConcurrentModificationError()
    //     0x7cbe0c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7cbe10: mov             x1, x0
    // 0x7cbe14: ldur            x0, [fp, #-0x48]
    // 0x7cbe18: StoreField: r1->field_b = r0
    //     0x7cbe18: stur            w0, [x1, #0xb]
    // 0x7cbe1c: mov             x0, x1
    // 0x7cbe20: r0 = Throw()
    //     0x7cbe20: bl              #0xec04b8  ; ThrowStub
    // 0x7cbe24: brk             #0
    // 0x7cbe28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cbe28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cbe2c: b               #0x7cb950
    // 0x7cbe30: r9 = catalog
    //     0x7cbe30: add             x9, PP, #0x36, lsl #12  ; [pp+0x365b8] Field <PdfDocument.catalog>: late final (offset: 0x18)
    //     0x7cbe34: ldr             x9, [x9, #0x5b8]
    // 0x7cbe38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7cbe38: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7cbe3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cbe3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cbe40: b               #0x7cbadc
    // 0x7cbe44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cbe44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7cbe48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cbe48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ PdfPage(/* No info */) {
    // ** addr: 0xe8aa70, size: 0x298
    // 0xe8aa70: EnterFrame
    //     0xe8aa70: stp             fp, lr, [SP, #-0x10]!
    //     0xe8aa74: mov             fp, SP
    // 0xe8aa78: AllocStack(0x38)
    //     0xe8aa78: sub             SP, SP, #0x38
    // 0xe8aa7c: SetupParameters(PdfPage this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe8aa7c: mov             x4, x1
    //     0xe8aa80: mov             x0, x2
    //     0xe8aa84: stur            x1, [fp, #-8]
    //     0xe8aa88: stur            x2, [fp, #-0x10]
    //     0xe8aa8c: stur            x3, [fp, #-0x18]
    // 0xe8aa90: CheckStackOverflow
    //     0xe8aa90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8aa94: cmp             SP, x16
    //     0xe8aa98: b.ls            #0xe8ace8
    // 0xe8aa9c: r1 = <PdfObject<PdfDataType>>
    //     0xe8aa9c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36830] TypeArguments: <PdfObject<PdfDataType>>
    //     0xe8aaa0: ldr             x1, [x1, #0x830]
    // 0xe8aaa4: r2 = 0
    //     0xe8aaa4: movz            x2, #0
    // 0xe8aaa8: r0 = _GrowableList()
    //     0xe8aaa8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8aaac: ldur            x3, [fp, #-8]
    // 0xe8aab0: StoreField: r3->field_4f = r0
    //     0xe8aab0: stur            w0, [x3, #0x4f]
    //     0xe8aab4: ldurb           w16, [x3, #-1]
    //     0xe8aab8: ldurb           w17, [x0, #-1]
    //     0xe8aabc: and             x16, x17, x16, lsr #2
    //     0xe8aac0: tst             x16, HEAP, lsr #32
    //     0xe8aac4: b.eq            #0xe8aacc
    //     0xe8aac8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8aacc: r1 = <PdfAnnot>
    //     0xe8aacc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36838] TypeArguments: <PdfAnnot>
    //     0xe8aad0: ldr             x1, [x1, #0x838]
    // 0xe8aad4: r2 = 0
    //     0xe8aad4: movz            x2, #0
    // 0xe8aad8: r0 = _GrowableList()
    //     0xe8aad8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8aadc: ldur            x1, [fp, #-8]
    // 0xe8aae0: StoreField: r1->field_53 = r0
    //     0xe8aae0: stur            w0, [x1, #0x53]
    //     0xe8aae4: ldurb           w16, [x1, #-1]
    //     0xe8aae8: ldurb           w17, [x0, #-1]
    //     0xe8aaec: and             x16, x17, x16, lsr #2
    //     0xe8aaf0: tst             x16, HEAP, lsr #32
    //     0xe8aaf4: b.eq            #0xe8aafc
    //     0xe8aaf8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8aafc: r16 = <PdfObject<PdfDataType>, PdfGraphics>
    //     0xe8aafc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36840] TypeArguments: <PdfObject<PdfDataType>, PdfGraphics>
    //     0xe8ab00: ldr             x16, [x16, #0x840]
    // 0xe8ab04: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe8ab08: stp             lr, x16, [SP]
    // 0xe8ab0c: r0 = Map._fromLiteral()
    //     0xe8ab0c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8ab10: ldur            x3, [fp, #-8]
    // 0xe8ab14: StoreField: r3->field_57 = r0
    //     0xe8ab14: stur            w0, [x3, #0x57]
    //     0xe8ab18: ldurb           w16, [x3, #-1]
    //     0xe8ab1c: ldurb           w17, [x0, #-1]
    //     0xe8ab20: and             x16, x17, x16, lsr #2
    //     0xe8ab24: tst             x16, HEAP, lsr #32
    //     0xe8ab28: b.eq            #0xe8ab30
    //     0xe8ab2c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8ab30: r0 = Instance_PdfPageFormat
    //     0xe8ab30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e4e8] Obj!PdfPageFormat@e0c711
    //     0xe8ab34: ldr             x0, [x0, #0x4e8]
    // 0xe8ab38: StoreField: r3->field_47 = r0
    //     0xe8ab38: stur            w0, [x3, #0x47]
    // 0xe8ab3c: r0 = Instance_PdfPageRotation
    //     0xe8ab3c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36848] Obj!PdfPageRotation@e2ec01
    //     0xe8ab40: ldr             x0, [x0, #0x848]
    // 0xe8ab44: StoreField: r3->field_4b = r0
    //     0xe8ab44: stur            w0, [x3, #0x4b]
    // 0xe8ab48: r1 = Null
    //     0xe8ab48: mov             x1, NULL
    // 0xe8ab4c: r2 = 4
    //     0xe8ab4c: movz            x2, #0x4
    // 0xe8ab50: r0 = AllocateArray()
    //     0xe8ab50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8ab54: r16 = "/Type"
    //     0xe8ab54: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe8ab58: ldr             x16, [x16, #0x630]
    // 0xe8ab5c: StoreField: r0->field_f = r16
    //     0xe8ab5c: stur            w16, [x0, #0xf]
    // 0xe8ab60: r16 = Instance_PdfName
    //     0xe8ab60: add             x16, PP, #0x36, lsl #12  ; [pp+0x36850] Obj!PdfName@e0c921
    //     0xe8ab64: ldr             x16, [x16, #0x850]
    // 0xe8ab68: StoreField: r0->field_13 = r16
    //     0xe8ab68: stur            w16, [x0, #0x13]
    // 0xe8ab6c: r16 = <String, PdfDataType>
    //     0xe8ab6c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe8ab70: ldr             x16, [x16, #0x820]
    // 0xe8ab74: stp             x0, x16, [SP]
    // 0xe8ab78: r0 = Map._fromLiteral()
    //     0xe8ab78: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8ab7c: r1 = <PdfDataType>
    //     0xe8ab7c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe8ab80: ldr             x1, [x1, #0x4c8]
    // 0xe8ab84: stur            x0, [fp, #-0x20]
    // 0xe8ab88: r0 = PdfDict()
    //     0xe8ab88: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe8ab8c: mov             x1, x0
    // 0xe8ab90: ldur            x0, [fp, #-0x20]
    // 0xe8ab94: StoreField: r1->field_b = r0
    //     0xe8ab94: stur            w0, [x1, #0xb]
    // 0xe8ab98: mov             x3, x1
    // 0xe8ab9c: ldur            x1, [fp, #-8]
    // 0xe8aba0: ldur            x2, [fp, #-0x10]
    // 0xe8aba4: r0 = _PdfPage&PdfObject&PdfGraphicStream()
    //     0xe8aba4: bl              #0xe8ad08  ; [package:pdf/src/pdf/obj/page.dart] _PdfPage&PdfObject&PdfGraphicStream::_PdfPage&PdfObject&PdfGraphicStream
    // 0xe8aba8: ldur            x0, [fp, #-0x18]
    // 0xe8abac: cmp             w0, NULL
    // 0xe8abb0: b.eq            #0xe8ac00
    // 0xe8abb4: ldur            x1, [fp, #-0x10]
    // 0xe8abb8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe8abb8: ldur            w2, [x1, #0x17]
    // 0xe8abbc: DecompressPointer r2
    //     0xe8abbc: add             x2, x2, HEAP, lsl #32
    // 0xe8abc0: r16 = Sentinel
    //     0xe8abc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8abc4: cmp             w2, w16
    // 0xe8abc8: b.eq            #0xe8acf0
    // 0xe8abcc: LoadField: r1 = r2->field_2b
    //     0xe8abcc: ldur            w1, [x2, #0x2b]
    // 0xe8abd0: DecompressPointer r1
    //     0xe8abd0: add             x1, x1, HEAP, lsl #32
    // 0xe8abd4: LoadField: r2 = r1->field_2b
    //     0xe8abd4: ldur            w2, [x1, #0x2b]
    // 0xe8abd8: DecompressPointer r2
    //     0xe8abd8: add             x2, x2, HEAP, lsl #32
    // 0xe8abdc: r1 = LoadInt32Instr(r0)
    //     0xe8abdc: sbfx            x1, x0, #1, #0x1f
    //     0xe8abe0: tbz             w0, #0, #0xe8abe8
    //     0xe8abe4: ldur            x1, [x0, #7]
    // 0xe8abe8: mov             x16, x1
    // 0xe8abec: mov             x1, x2
    // 0xe8abf0: mov             x2, x16
    // 0xe8abf4: ldur            x3, [fp, #-8]
    // 0xe8abf8: r0 = insert()
    //     0xe8abf8: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xe8abfc: b               #0xe8acd8
    // 0xe8ac00: ldur            x1, [fp, #-0x10]
    // 0xe8ac04: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe8ac04: ldur            w0, [x1, #0x17]
    // 0xe8ac08: DecompressPointer r0
    //     0xe8ac08: add             x0, x0, HEAP, lsl #32
    // 0xe8ac0c: r16 = Sentinel
    //     0xe8ac0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8ac10: cmp             w0, w16
    // 0xe8ac14: b.eq            #0xe8acfc
    // 0xe8ac18: LoadField: r1 = r0->field_2b
    //     0xe8ac18: ldur            w1, [x0, #0x2b]
    // 0xe8ac1c: DecompressPointer r1
    //     0xe8ac1c: add             x1, x1, HEAP, lsl #32
    // 0xe8ac20: LoadField: r3 = r1->field_2b
    //     0xe8ac20: ldur            w3, [x1, #0x2b]
    // 0xe8ac24: DecompressPointer r3
    //     0xe8ac24: add             x3, x3, HEAP, lsl #32
    // 0xe8ac28: stur            x3, [fp, #-0x10]
    // 0xe8ac2c: LoadField: r2 = r3->field_7
    //     0xe8ac2c: ldur            w2, [x3, #7]
    // 0xe8ac30: DecompressPointer r2
    //     0xe8ac30: add             x2, x2, HEAP, lsl #32
    // 0xe8ac34: ldur            x0, [fp, #-8]
    // 0xe8ac38: r1 = Null
    //     0xe8ac38: mov             x1, NULL
    // 0xe8ac3c: cmp             w2, NULL
    // 0xe8ac40: b.eq            #0xe8ac60
    // 0xe8ac44: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8ac44: ldur            w4, [x2, #0x17]
    // 0xe8ac48: DecompressPointer r4
    //     0xe8ac48: add             x4, x4, HEAP, lsl #32
    // 0xe8ac4c: r8 = X0
    //     0xe8ac4c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8ac50: LoadField: r9 = r4->field_7
    //     0xe8ac50: ldur            x9, [x4, #7]
    // 0xe8ac54: r3 = Null
    //     0xe8ac54: add             x3, PP, #0x36, lsl #12  ; [pp+0x36858] Null
    //     0xe8ac58: ldr             x3, [x3, #0x858]
    // 0xe8ac5c: blr             x9
    // 0xe8ac60: ldur            x0, [fp, #-0x10]
    // 0xe8ac64: LoadField: r1 = r0->field_b
    //     0xe8ac64: ldur            w1, [x0, #0xb]
    // 0xe8ac68: LoadField: r2 = r0->field_f
    //     0xe8ac68: ldur            w2, [x0, #0xf]
    // 0xe8ac6c: DecompressPointer r2
    //     0xe8ac6c: add             x2, x2, HEAP, lsl #32
    // 0xe8ac70: LoadField: r3 = r2->field_b
    //     0xe8ac70: ldur            w3, [x2, #0xb]
    // 0xe8ac74: r2 = LoadInt32Instr(r1)
    //     0xe8ac74: sbfx            x2, x1, #1, #0x1f
    // 0xe8ac78: stur            x2, [fp, #-0x28]
    // 0xe8ac7c: r1 = LoadInt32Instr(r3)
    //     0xe8ac7c: sbfx            x1, x3, #1, #0x1f
    // 0xe8ac80: cmp             x2, x1
    // 0xe8ac84: b.ne            #0xe8ac90
    // 0xe8ac88: mov             x1, x0
    // 0xe8ac8c: r0 = _growToNextCapacity()
    //     0xe8ac8c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8ac90: ldur            x2, [fp, #-0x10]
    // 0xe8ac94: ldur            x3, [fp, #-0x28]
    // 0xe8ac98: add             x4, x3, #1
    // 0xe8ac9c: lsl             x5, x4, #1
    // 0xe8aca0: StoreField: r2->field_b = r5
    //     0xe8aca0: stur            w5, [x2, #0xb]
    // 0xe8aca4: LoadField: r1 = r2->field_f
    //     0xe8aca4: ldur            w1, [x2, #0xf]
    // 0xe8aca8: DecompressPointer r1
    //     0xe8aca8: add             x1, x1, HEAP, lsl #32
    // 0xe8acac: ldur            x0, [fp, #-8]
    // 0xe8acb0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8acb0: add             x25, x1, x3, lsl #2
    //     0xe8acb4: add             x25, x25, #0xf
    //     0xe8acb8: str             w0, [x25]
    //     0xe8acbc: tbz             w0, #0, #0xe8acd8
    //     0xe8acc0: ldurb           w16, [x1, #-1]
    //     0xe8acc4: ldurb           w17, [x0, #-1]
    //     0xe8acc8: and             x16, x17, x16, lsr #2
    //     0xe8accc: tst             x16, HEAP, lsr #32
    //     0xe8acd0: b.eq            #0xe8acd8
    //     0xe8acd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8acd8: r0 = Null
    //     0xe8acd8: mov             x0, NULL
    // 0xe8acdc: LeaveFrame
    //     0xe8acdc: mov             SP, fp
    //     0xe8ace0: ldp             fp, lr, [SP], #0x10
    // 0xe8ace4: ret
    //     0xe8ace4: ret             
    // 0xe8ace8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ace8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8acec: b               #0xe8aa9c
    // 0xe8acf0: r9 = catalog
    //     0xe8acf0: add             x9, PP, #0x36, lsl #12  ; [pp+0x365b8] Field <PdfDocument.catalog>: late final (offset: 0x18)
    //     0xe8acf4: ldr             x9, [x9, #0x5b8]
    // 0xe8acf8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8acf8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe8acfc: r9 = catalog
    //     0xe8acfc: add             x9, PP, #0x36, lsl #12  ; [pp+0x365b8] Field <PdfDocument.catalog>: late final (offset: 0x18)
    //     0xe8ad00: ldr             x9, [x9, #0x5b8]
    // 0xe8ad04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8ad04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 6810, size: 0x14, field offset: 0x14
enum PdfPageRotation extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dba4, size: 0x64
    // 0xc4dba4: EnterFrame
    //     0xc4dba4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dba8: mov             fp, SP
    // 0xc4dbac: AllocStack(0x10)
    //     0xc4dbac: sub             SP, SP, #0x10
    // 0xc4dbb0: SetupParameters(PdfPageRotation this /* r1 => r0, fp-0x8 */)
    //     0xc4dbb0: mov             x0, x1
    //     0xc4dbb4: stur            x1, [fp, #-8]
    // 0xc4dbb8: CheckStackOverflow
    //     0xc4dbb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dbbc: cmp             SP, x16
    //     0xc4dbc0: b.ls            #0xc4dc00
    // 0xc4dbc4: r1 = Null
    //     0xc4dbc4: mov             x1, NULL
    // 0xc4dbc8: r2 = 4
    //     0xc4dbc8: movz            x2, #0x4
    // 0xc4dbcc: r0 = AllocateArray()
    //     0xc4dbcc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dbd0: r16 = "PdfPageRotation."
    //     0xc4dbd0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3efc0] "PdfPageRotation."
    //     0xc4dbd4: ldr             x16, [x16, #0xfc0]
    // 0xc4dbd8: StoreField: r0->field_f = r16
    //     0xc4dbd8: stur            w16, [x0, #0xf]
    // 0xc4dbdc: ldur            x1, [fp, #-8]
    // 0xc4dbe0: LoadField: r2 = r1->field_f
    //     0xc4dbe0: ldur            w2, [x1, #0xf]
    // 0xc4dbe4: DecompressPointer r2
    //     0xc4dbe4: add             x2, x2, HEAP, lsl #32
    // 0xc4dbe8: StoreField: r0->field_13 = r2
    //     0xc4dbe8: stur            w2, [x0, #0x13]
    // 0xc4dbec: str             x0, [SP]
    // 0xc4dbf0: r0 = _interpolate()
    //     0xc4dbf0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dbf4: LeaveFrame
    //     0xc4dbf4: mov             SP, fp
    //     0xc4dbf8: ldp             fp, lr, [SP], #0x10
    // 0xc4dbfc: ret
    //     0xc4dbfc: ret             
    // 0xc4dc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dc00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dc04: b               #0xc4dbc4
  }
}
