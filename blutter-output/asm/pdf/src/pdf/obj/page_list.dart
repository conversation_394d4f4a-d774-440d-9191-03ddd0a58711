// lib: , url: package:pdf/src/pdf/obj/page_list.dart

// class id: 1050812, size: 0x8
class :: {
}

// class id: 875, size: 0x30, field offset: 0x2c
class PdfPageList extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7cc37c, size: 0x128
    // 0x7cc37c: EnterFrame
    //     0x7cc37c: stp             fp, lr, [SP, #-0x10]!
    //     0x7cc380: mov             fp, SP
    // 0x7cc384: AllocStack(0x28)
    //     0x7cc384: sub             SP, SP, #0x28
    // 0x7cc388: CheckStackOverflow
    //     0x7cc388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cc38c: cmp             SP, x16
    //     0x7cc390: b.ls            #0x7cc49c
    // 0x7cc394: LoadField: r0 = r1->field_1b
    //     0x7cc394: ldur            w0, [x1, #0x1b]
    // 0x7cc398: DecompressPointer r0
    //     0x7cc398: add             x0, x0, HEAP, lsl #32
    // 0x7cc39c: stur            x0, [fp, #-0x10]
    // 0x7cc3a0: LoadField: r2 = r1->field_2b
    //     0x7cc3a0: ldur            w2, [x1, #0x2b]
    // 0x7cc3a4: DecompressPointer r2
    //     0x7cc3a4: add             x2, x2, HEAP, lsl #32
    // 0x7cc3a8: mov             x1, x2
    // 0x7cc3ac: stur            x2, [fp, #-8]
    // 0x7cc3b0: r0 = fromObjects()
    //     0x7cc3b0: bl              #0x7ca4a4  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromObjects
    // 0x7cc3b4: mov             x4, x0
    // 0x7cc3b8: ldur            x3, [fp, #-0x10]
    // 0x7cc3bc: stur            x4, [fp, #-0x20]
    // 0x7cc3c0: LoadField: r5 = r3->field_7
    //     0x7cc3c0: ldur            w5, [x3, #7]
    // 0x7cc3c4: DecompressPointer r5
    //     0x7cc3c4: add             x5, x5, HEAP, lsl #32
    // 0x7cc3c8: mov             x0, x4
    // 0x7cc3cc: mov             x2, x5
    // 0x7cc3d0: stur            x5, [fp, #-0x18]
    // 0x7cc3d4: r1 = Null
    //     0x7cc3d4: mov             x1, NULL
    // 0x7cc3d8: cmp             w2, NULL
    // 0x7cc3dc: b.eq            #0x7cc400
    // 0x7cc3e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc3e0: ldur            w4, [x2, #0x17]
    // 0x7cc3e4: DecompressPointer r4
    //     0x7cc3e4: add             x4, x4, HEAP, lsl #32
    // 0x7cc3e8: r8 = X0 bound PdfDataType
    //     0x7cc3e8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc3ec: ldr             x8, [x8, #0x6d8]
    // 0x7cc3f0: LoadField: r9 = r4->field_7
    //     0x7cc3f0: ldur            x9, [x4, #7]
    // 0x7cc3f4: r3 = Null
    //     0x7cc3f4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c068] Null
    //     0x7cc3f8: ldr             x3, [x3, #0x68]
    // 0x7cc3fc: blr             x9
    // 0x7cc400: ldur            x0, [fp, #-0x10]
    // 0x7cc404: LoadField: r4 = r0->field_b
    //     0x7cc404: ldur            w4, [x0, #0xb]
    // 0x7cc408: DecompressPointer r4
    //     0x7cc408: add             x4, x4, HEAP, lsl #32
    // 0x7cc40c: mov             x1, x4
    // 0x7cc410: ldur            x3, [fp, #-0x20]
    // 0x7cc414: stur            x4, [fp, #-0x28]
    // 0x7cc418: r2 = "/Kids"
    //     0x7cc418: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c078] "/Kids"
    //     0x7cc41c: ldr             x2, [x2, #0x78]
    // 0x7cc420: r0 = []=()
    //     0x7cc420: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc424: ldur            x0, [fp, #-8]
    // 0x7cc428: LoadField: r1 = r0->field_b
    //     0x7cc428: ldur            w1, [x0, #0xb]
    // 0x7cc42c: stur            x1, [fp, #-0x10]
    // 0x7cc430: r0 = PdfNum()
    //     0x7cc430: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7cc434: mov             x3, x0
    // 0x7cc438: ldur            x0, [fp, #-0x10]
    // 0x7cc43c: stur            x3, [fp, #-8]
    // 0x7cc440: StoreField: r3->field_7 = r0
    //     0x7cc440: stur            w0, [x3, #7]
    // 0x7cc444: mov             x0, x3
    // 0x7cc448: ldur            x2, [fp, #-0x18]
    // 0x7cc44c: r1 = Null
    //     0x7cc44c: mov             x1, NULL
    // 0x7cc450: cmp             w2, NULL
    // 0x7cc454: b.eq            #0x7cc478
    // 0x7cc458: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc458: ldur            w4, [x2, #0x17]
    // 0x7cc45c: DecompressPointer r4
    //     0x7cc45c: add             x4, x4, HEAP, lsl #32
    // 0x7cc460: r8 = X0 bound PdfDataType
    //     0x7cc460: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc464: ldr             x8, [x8, #0x6d8]
    // 0x7cc468: LoadField: r9 = r4->field_7
    //     0x7cc468: ldur            x9, [x4, #7]
    // 0x7cc46c: r3 = Null
    //     0x7cc46c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c080] Null
    //     0x7cc470: ldr             x3, [x3, #0x80]
    // 0x7cc474: blr             x9
    // 0x7cc478: ldur            x1, [fp, #-0x28]
    // 0x7cc47c: ldur            x3, [fp, #-8]
    // 0x7cc480: r2 = "/Count"
    //     0x7cc480: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c090] "/Count"
    //     0x7cc484: ldr             x2, [x2, #0x90]
    // 0x7cc488: r0 = []=()
    //     0x7cc488: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc48c: r0 = Null
    //     0x7cc48c: mov             x0, NULL
    // 0x7cc490: LeaveFrame
    //     0x7cc490: mov             SP, fp
    //     0x7cc494: ldp             fp, lr, [SP], #0x10
    // 0x7cc498: ret
    //     0x7cc498: ret             
    // 0x7cc49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cc49c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cc4a0: b               #0x7cc394
  }
  _ PdfPageList(/* No info */) {
    // ** addr: 0xe8b41c, size: 0xd0
    // 0xe8b41c: EnterFrame
    //     0xe8b41c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b420: mov             fp, SP
    // 0xe8b424: AllocStack(0x28)
    //     0xe8b424: sub             SP, SP, #0x28
    // 0xe8b428: SetupParameters(PdfPageList this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe8b428: mov             x3, x1
    //     0xe8b42c: mov             x0, x2
    //     0xe8b430: stur            x1, [fp, #-8]
    //     0xe8b434: stur            x2, [fp, #-0x10]
    // 0xe8b438: CheckStackOverflow
    //     0xe8b438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b43c: cmp             SP, x16
    //     0xe8b440: b.ls            #0xe8b4e4
    // 0xe8b444: r1 = <PdfPage>
    //     0xe8b444: add             x1, PP, #0x36, lsl #12  ; [pp+0x36908] TypeArguments: <PdfPage>
    //     0xe8b448: ldr             x1, [x1, #0x908]
    // 0xe8b44c: r2 = 0
    //     0xe8b44c: movz            x2, #0
    // 0xe8b450: r0 = _GrowableList()
    //     0xe8b450: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8b454: ldur            x3, [fp, #-8]
    // 0xe8b458: StoreField: r3->field_2b = r0
    //     0xe8b458: stur            w0, [x3, #0x2b]
    //     0xe8b45c: ldurb           w16, [x3, #-1]
    //     0xe8b460: ldurb           w17, [x0, #-1]
    //     0xe8b464: and             x16, x17, x16, lsr #2
    //     0xe8b468: tst             x16, HEAP, lsr #32
    //     0xe8b46c: b.eq            #0xe8b474
    //     0xe8b470: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8b474: r1 = Null
    //     0xe8b474: mov             x1, NULL
    // 0xe8b478: r2 = 4
    //     0xe8b478: movz            x2, #0x4
    // 0xe8b47c: r0 = AllocateArray()
    //     0xe8b47c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8b480: r16 = "/Type"
    //     0xe8b480: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe8b484: ldr             x16, [x16, #0x630]
    // 0xe8b488: StoreField: r0->field_f = r16
    //     0xe8b488: stur            w16, [x0, #0xf]
    // 0xe8b48c: r16 = Instance_PdfName
    //     0xe8b48c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36910] Obj!PdfName@e0c941
    //     0xe8b490: ldr             x16, [x16, #0x910]
    // 0xe8b494: StoreField: r0->field_13 = r16
    //     0xe8b494: stur            w16, [x0, #0x13]
    // 0xe8b498: r16 = <String, PdfDataType>
    //     0xe8b498: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe8b49c: ldr             x16, [x16, #0x820]
    // 0xe8b4a0: stp             x0, x16, [SP]
    // 0xe8b4a4: r0 = Map._fromLiteral()
    //     0xe8b4a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe8b4a8: r1 = <PdfDataType>
    //     0xe8b4a8: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe8b4ac: ldr             x1, [x1, #0x4c8]
    // 0xe8b4b0: stur            x0, [fp, #-0x18]
    // 0xe8b4b4: r0 = PdfDict()
    //     0xe8b4b4: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe8b4b8: mov             x1, x0
    // 0xe8b4bc: ldur            x0, [fp, #-0x18]
    // 0xe8b4c0: StoreField: r1->field_b = r0
    //     0xe8b4c0: stur            w0, [x1, #0xb]
    // 0xe8b4c4: mov             x3, x1
    // 0xe8b4c8: ldur            x1, [fp, #-8]
    // 0xe8b4cc: ldur            x2, [fp, #-0x10]
    // 0xe8b4d0: r0 = PdfObject()
    //     0xe8b4d0: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe8b4d4: r0 = Null
    //     0xe8b4d4: mov             x0, NULL
    // 0xe8b4d8: LeaveFrame
    //     0xe8b4d8: mov             SP, fp
    //     0xe8b4dc: ldp             fp, lr, [SP], #0x10
    // 0xe8b4e0: ret
    //     0xe8b4e0: ret             
    // 0xe8b4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b4e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b4e8: b               #0xe8b444
  }
}
