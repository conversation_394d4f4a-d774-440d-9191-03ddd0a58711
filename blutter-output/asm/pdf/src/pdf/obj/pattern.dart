// lib: , url: package:pdf/src/pdf/obj/pattern.dart

// class id: 1050813, size: 0x8
class :: {
}

// class id: 873, size: 0x38, field offset: 0x2c
abstract class PdfPattern extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7cc580, size: 0x3b8
    // 0x7cc580: EnterFrame
    //     0x7cc580: stp             fp, lr, [SP, #-0x10]!
    //     0x7cc584: mov             fp, SP
    // 0x7cc588: AllocStack(0x50)
    //     0x7cc588: sub             SP, SP, #0x50
    // 0x7cc58c: SetupParameters(PdfPattern this /* r1 => r1, fp-0x10 */)
    //     0x7cc58c: stur            x1, [fp, #-0x10]
    // 0x7cc590: CheckStackOverflow
    //     0x7cc590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cc594: cmp             SP, x16
    //     0x7cc598: b.ls            #0x7cc880
    // 0x7cc59c: LoadField: r0 = r1->field_1b
    //     0x7cc59c: ldur            w0, [x1, #0x1b]
    // 0x7cc5a0: DecompressPointer r0
    //     0x7cc5a0: add             x0, x0, HEAP, lsl #32
    // 0x7cc5a4: stur            x0, [fp, #-8]
    // 0x7cc5a8: r0 = PdfNum()
    //     0x7cc5a8: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7cc5ac: mov             x3, x0
    // 0x7cc5b0: r0 = 4
    //     0x7cc5b0: movz            x0, #0x4
    // 0x7cc5b4: stur            x3, [fp, #-0x20]
    // 0x7cc5b8: StoreField: r3->field_7 = r0
    //     0x7cc5b8: stur            w0, [x3, #7]
    // 0x7cc5bc: ldur            x4, [fp, #-8]
    // 0x7cc5c0: LoadField: r5 = r4->field_7
    //     0x7cc5c0: ldur            w5, [x4, #7]
    // 0x7cc5c4: DecompressPointer r5
    //     0x7cc5c4: add             x5, x5, HEAP, lsl #32
    // 0x7cc5c8: mov             x0, x3
    // 0x7cc5cc: mov             x2, x5
    // 0x7cc5d0: stur            x5, [fp, #-0x18]
    // 0x7cc5d4: r1 = Null
    //     0x7cc5d4: mov             x1, NULL
    // 0x7cc5d8: cmp             w2, NULL
    // 0x7cc5dc: b.eq            #0x7cc600
    // 0x7cc5e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc5e0: ldur            w4, [x2, #0x17]
    // 0x7cc5e4: DecompressPointer r4
    //     0x7cc5e4: add             x4, x4, HEAP, lsl #32
    // 0x7cc5e8: r8 = X0 bound PdfDataType
    //     0x7cc5e8: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc5ec: ldr             x8, [x8, #0x6d8]
    // 0x7cc5f0: LoadField: r9 = r4->field_7
    //     0x7cc5f0: ldur            x9, [x4, #7]
    // 0x7cc5f4: r3 = Null
    //     0x7cc5f4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b040] Null
    //     0x7cc5f8: ldr             x3, [x3, #0x40]
    // 0x7cc5fc: blr             x9
    // 0x7cc600: ldur            x0, [fp, #-8]
    // 0x7cc604: LoadField: r4 = r0->field_b
    //     0x7cc604: ldur            w4, [x0, #0xb]
    // 0x7cc608: DecompressPointer r4
    //     0x7cc608: add             x4, x4, HEAP, lsl #32
    // 0x7cc60c: mov             x1, x4
    // 0x7cc610: ldur            x3, [fp, #-0x20]
    // 0x7cc614: stur            x4, [fp, #-0x28]
    // 0x7cc618: r2 = "/PatternType"
    //     0x7cc618: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b050] "/PatternType"
    //     0x7cc61c: ldr             x2, [x2, #0x50]
    // 0x7cc620: r0 = []=()
    //     0x7cc620: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc624: ldur            x0, [fp, #-0x10]
    // 0x7cc628: LoadField: r1 = r0->field_33
    //     0x7cc628: ldur            w1, [x0, #0x33]
    // 0x7cc62c: DecompressPointer r1
    //     0x7cc62c: add             x1, x1, HEAP, lsl #32
    // 0x7cc630: LoadField: r2 = r1->field_7
    //     0x7cc630: ldur            w2, [x1, #7]
    // 0x7cc634: DecompressPointer r2
    //     0x7cc634: add             x2, x2, HEAP, lsl #32
    // 0x7cc638: LoadField: r0 = r2->field_13
    //     0x7cc638: ldur            w0, [x2, #0x13]
    // 0x7cc63c: r3 = LoadInt32Instr(r0)
    //     0x7cc63c: sbfx            x3, x0, #1, #0x1f
    // 0x7cc640: mov             x0, x3
    // 0x7cc644: r1 = 0
    //     0x7cc644: movz            x1, #0
    // 0x7cc648: cmp             x1, x0
    // 0x7cc64c: b.hs            #0x7cc888
    // 0x7cc650: ArrayLoad: d0 = r2[0]  ; List_8
    //     0x7cc650: ldur            d0, [x2, #0x17]
    // 0x7cc654: mov             x0, x3
    // 0x7cc658: r1 = 1
    //     0x7cc658: movz            x1, #0x1
    // 0x7cc65c: cmp             x1, x0
    // 0x7cc660: b.hs            #0x7cc88c
    // 0x7cc664: LoadField: d1 = r2->field_1f
    //     0x7cc664: ldur            d1, [x2, #0x1f]
    // 0x7cc668: mov             x0, x3
    // 0x7cc66c: stur            d1, [fp, #-0x50]
    // 0x7cc670: r1 = 4
    //     0x7cc670: movz            x1, #0x4
    // 0x7cc674: cmp             x1, x0
    // 0x7cc678: b.hs            #0x7cc890
    // 0x7cc67c: LoadField: d2 = r2->field_37
    //     0x7cc67c: ldur            d2, [x2, #0x37]
    // 0x7cc680: mov             x0, x3
    // 0x7cc684: stur            d2, [fp, #-0x48]
    // 0x7cc688: r1 = 5
    //     0x7cc688: movz            x1, #0x5
    // 0x7cc68c: cmp             x1, x0
    // 0x7cc690: b.hs            #0x7cc894
    // 0x7cc694: LoadField: d3 = r2->field_3f
    //     0x7cc694: ldur            d3, [x2, #0x3f]
    // 0x7cc698: mov             x0, x3
    // 0x7cc69c: stur            d3, [fp, #-0x40]
    // 0x7cc6a0: r1 = 12
    //     0x7cc6a0: movz            x1, #0xc
    // 0x7cc6a4: cmp             x1, x0
    // 0x7cc6a8: b.hs            #0x7cc898
    // 0x7cc6ac: LoadField: d4 = r2->field_77
    //     0x7cc6ac: ldur            d4, [x2, #0x77]
    // 0x7cc6b0: mov             x0, x3
    // 0x7cc6b4: stur            d4, [fp, #-0x38]
    // 0x7cc6b8: r1 = 13
    //     0x7cc6b8: movz            x1, #0xd
    // 0x7cc6bc: cmp             x1, x0
    // 0x7cc6c0: b.hs            #0x7cc89c
    // 0x7cc6c4: LoadField: d5 = r2->field_7f
    //     0x7cc6c4: ldur            d5, [x2, #0x7f]
    // 0x7cc6c8: stur            d5, [fp, #-0x30]
    // 0x7cc6cc: r0 = inline_Allocate_Double()
    //     0x7cc6cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc6d0: add             x0, x0, #0x10
    //     0x7cc6d4: cmp             x1, x0
    //     0x7cc6d8: b.ls            #0x7cc8a0
    //     0x7cc6dc: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc6e0: sub             x0, x0, #0xf
    //     0x7cc6e4: movz            x1, #0xe15c
    //     0x7cc6e8: movk            x1, #0x3, lsl #16
    //     0x7cc6ec: stur            x1, [x0, #-1]
    // 0x7cc6f0: StoreField: r0->field_7 = d0
    //     0x7cc6f0: stur            d0, [x0, #7]
    // 0x7cc6f4: stur            x0, [fp, #-8]
    // 0x7cc6f8: r1 = Null
    //     0x7cc6f8: mov             x1, NULL
    // 0x7cc6fc: r2 = 12
    //     0x7cc6fc: movz            x2, #0xc
    // 0x7cc700: r0 = AllocateArray()
    //     0x7cc700: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cc704: mov             x2, x0
    // 0x7cc708: ldur            x0, [fp, #-8]
    // 0x7cc70c: stur            x2, [fp, #-0x10]
    // 0x7cc710: StoreField: r2->field_f = r0
    //     0x7cc710: stur            w0, [x2, #0xf]
    // 0x7cc714: ldur            d0, [fp, #-0x50]
    // 0x7cc718: r0 = inline_Allocate_Double()
    //     0x7cc718: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc71c: add             x0, x0, #0x10
    //     0x7cc720: cmp             x1, x0
    //     0x7cc724: b.ls            #0x7cc8c0
    //     0x7cc728: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc72c: sub             x0, x0, #0xf
    //     0x7cc730: movz            x1, #0xe15c
    //     0x7cc734: movk            x1, #0x3, lsl #16
    //     0x7cc738: stur            x1, [x0, #-1]
    // 0x7cc73c: StoreField: r0->field_7 = d0
    //     0x7cc73c: stur            d0, [x0, #7]
    // 0x7cc740: StoreField: r2->field_13 = r0
    //     0x7cc740: stur            w0, [x2, #0x13]
    // 0x7cc744: ldur            d0, [fp, #-0x48]
    // 0x7cc748: r0 = inline_Allocate_Double()
    //     0x7cc748: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc74c: add             x0, x0, #0x10
    //     0x7cc750: cmp             x1, x0
    //     0x7cc754: b.ls            #0x7cc8d8
    //     0x7cc758: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc75c: sub             x0, x0, #0xf
    //     0x7cc760: movz            x1, #0xe15c
    //     0x7cc764: movk            x1, #0x3, lsl #16
    //     0x7cc768: stur            x1, [x0, #-1]
    // 0x7cc76c: StoreField: r0->field_7 = d0
    //     0x7cc76c: stur            d0, [x0, #7]
    // 0x7cc770: ArrayStore: r2[0] = r0  ; List_4
    //     0x7cc770: stur            w0, [x2, #0x17]
    // 0x7cc774: ldur            d0, [fp, #-0x40]
    // 0x7cc778: r0 = inline_Allocate_Double()
    //     0x7cc778: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc77c: add             x0, x0, #0x10
    //     0x7cc780: cmp             x1, x0
    //     0x7cc784: b.ls            #0x7cc8f0
    //     0x7cc788: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc78c: sub             x0, x0, #0xf
    //     0x7cc790: movz            x1, #0xe15c
    //     0x7cc794: movk            x1, #0x3, lsl #16
    //     0x7cc798: stur            x1, [x0, #-1]
    // 0x7cc79c: StoreField: r0->field_7 = d0
    //     0x7cc79c: stur            d0, [x0, #7]
    // 0x7cc7a0: StoreField: r2->field_1b = r0
    //     0x7cc7a0: stur            w0, [x2, #0x1b]
    // 0x7cc7a4: ldur            d0, [fp, #-0x38]
    // 0x7cc7a8: r0 = inline_Allocate_Double()
    //     0x7cc7a8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc7ac: add             x0, x0, #0x10
    //     0x7cc7b0: cmp             x1, x0
    //     0x7cc7b4: b.ls            #0x7cc908
    //     0x7cc7b8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc7bc: sub             x0, x0, #0xf
    //     0x7cc7c0: movz            x1, #0xe15c
    //     0x7cc7c4: movk            x1, #0x3, lsl #16
    //     0x7cc7c8: stur            x1, [x0, #-1]
    // 0x7cc7cc: StoreField: r0->field_7 = d0
    //     0x7cc7cc: stur            d0, [x0, #7]
    // 0x7cc7d0: StoreField: r2->field_1f = r0
    //     0x7cc7d0: stur            w0, [x2, #0x1f]
    // 0x7cc7d4: ldur            d0, [fp, #-0x30]
    // 0x7cc7d8: r0 = inline_Allocate_Double()
    //     0x7cc7d8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cc7dc: add             x0, x0, #0x10
    //     0x7cc7e0: cmp             x1, x0
    //     0x7cc7e4: b.ls            #0x7cc920
    //     0x7cc7e8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7cc7ec: sub             x0, x0, #0xf
    //     0x7cc7f0: movz            x1, #0xe15c
    //     0x7cc7f4: movk            x1, #0x3, lsl #16
    //     0x7cc7f8: stur            x1, [x0, #-1]
    // 0x7cc7fc: StoreField: r0->field_7 = d0
    //     0x7cc7fc: stur            d0, [x0, #7]
    // 0x7cc800: StoreField: r2->field_23 = r0
    //     0x7cc800: stur            w0, [x2, #0x23]
    // 0x7cc804: r1 = <double>
    //     0x7cc804: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x7cc808: r0 = AllocateGrowableArray()
    //     0x7cc808: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7cc80c: mov             x1, x0
    // 0x7cc810: ldur            x0, [fp, #-0x10]
    // 0x7cc814: StoreField: r1->field_f = r0
    //     0x7cc814: stur            w0, [x1, #0xf]
    // 0x7cc818: r0 = 12
    //     0x7cc818: movz            x0, #0xc
    // 0x7cc81c: StoreField: r1->field_b = r0
    //     0x7cc81c: stur            w0, [x1, #0xb]
    // 0x7cc820: r0 = fromNum()
    //     0x7cc820: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7cc824: ldur            x2, [fp, #-0x18]
    // 0x7cc828: mov             x3, x0
    // 0x7cc82c: r1 = Null
    //     0x7cc82c: mov             x1, NULL
    // 0x7cc830: stur            x3, [fp, #-8]
    // 0x7cc834: cmp             w2, NULL
    // 0x7cc838: b.eq            #0x7cc85c
    // 0x7cc83c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc83c: ldur            w4, [x2, #0x17]
    // 0x7cc840: DecompressPointer r4
    //     0x7cc840: add             x4, x4, HEAP, lsl #32
    // 0x7cc844: r8 = X0 bound PdfDataType
    //     0x7cc844: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc848: ldr             x8, [x8, #0x6d8]
    // 0x7cc84c: LoadField: r9 = r4->field_7
    //     0x7cc84c: ldur            x9, [x4, #7]
    // 0x7cc850: r3 = Null
    //     0x7cc850: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b058] Null
    //     0x7cc854: ldr             x3, [x3, #0x58]
    // 0x7cc858: blr             x9
    // 0x7cc85c: ldur            x1, [fp, #-0x28]
    // 0x7cc860: ldur            x3, [fp, #-8]
    // 0x7cc864: r2 = "/Matrix"
    //     0x7cc864: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b068] "/Matrix"
    //     0x7cc868: ldr             x2, [x2, #0x68]
    // 0x7cc86c: r0 = []=()
    //     0x7cc86c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc870: r0 = Null
    //     0x7cc870: mov             x0, NULL
    // 0x7cc874: LeaveFrame
    //     0x7cc874: mov             SP, fp
    //     0x7cc878: ldp             fp, lr, [SP], #0x10
    // 0x7cc87c: ret
    //     0x7cc87c: ret             
    // 0x7cc880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cc880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cc884: b               #0x7cc59c
    // 0x7cc888: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7cc888: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7cc88c: r0 = RangeErrorSharedWithFPURegs()
    //     0x7cc88c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0x7cc890: r0 = RangeErrorSharedWithFPURegs()
    //     0x7cc890: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0x7cc894: r0 = RangeErrorSharedWithFPURegs()
    //     0x7cc894: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0x7cc898: r0 = RangeErrorSharedWithFPURegs()
    //     0x7cc898: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0x7cc89c: r0 = RangeErrorSharedWithFPURegs()
    //     0x7cc89c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0x7cc8a0: stp             q4, q5, [SP, #-0x20]!
    // 0x7cc8a4: stp             q2, q3, [SP, #-0x20]!
    // 0x7cc8a8: stp             q0, q1, [SP, #-0x20]!
    // 0x7cc8ac: r0 = AllocateDouble()
    //     0x7cc8ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc8b0: ldp             q0, q1, [SP], #0x20
    // 0x7cc8b4: ldp             q2, q3, [SP], #0x20
    // 0x7cc8b8: ldp             q4, q5, [SP], #0x20
    // 0x7cc8bc: b               #0x7cc6f0
    // 0x7cc8c0: SaveReg d0
    //     0x7cc8c0: str             q0, [SP, #-0x10]!
    // 0x7cc8c4: SaveReg r2
    //     0x7cc8c4: str             x2, [SP, #-8]!
    // 0x7cc8c8: r0 = AllocateDouble()
    //     0x7cc8c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc8cc: RestoreReg r2
    //     0x7cc8cc: ldr             x2, [SP], #8
    // 0x7cc8d0: RestoreReg d0
    //     0x7cc8d0: ldr             q0, [SP], #0x10
    // 0x7cc8d4: b               #0x7cc73c
    // 0x7cc8d8: SaveReg d0
    //     0x7cc8d8: str             q0, [SP, #-0x10]!
    // 0x7cc8dc: SaveReg r2
    //     0x7cc8dc: str             x2, [SP, #-8]!
    // 0x7cc8e0: r0 = AllocateDouble()
    //     0x7cc8e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc8e4: RestoreReg r2
    //     0x7cc8e4: ldr             x2, [SP], #8
    // 0x7cc8e8: RestoreReg d0
    //     0x7cc8e8: ldr             q0, [SP], #0x10
    // 0x7cc8ec: b               #0x7cc76c
    // 0x7cc8f0: SaveReg d0
    //     0x7cc8f0: str             q0, [SP, #-0x10]!
    // 0x7cc8f4: SaveReg r2
    //     0x7cc8f4: str             x2, [SP, #-8]!
    // 0x7cc8f8: r0 = AllocateDouble()
    //     0x7cc8f8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc8fc: RestoreReg r2
    //     0x7cc8fc: ldr             x2, [SP], #8
    // 0x7cc900: RestoreReg d0
    //     0x7cc900: ldr             q0, [SP], #0x10
    // 0x7cc904: b               #0x7cc79c
    // 0x7cc908: SaveReg d0
    //     0x7cc908: str             q0, [SP, #-0x10]!
    // 0x7cc90c: SaveReg r2
    //     0x7cc90c: str             x2, [SP, #-8]!
    // 0x7cc910: r0 = AllocateDouble()
    //     0x7cc910: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc914: RestoreReg r2
    //     0x7cc914: ldr             x2, [SP], #8
    // 0x7cc918: RestoreReg d0
    //     0x7cc918: ldr             q0, [SP], #0x10
    // 0x7cc91c: b               #0x7cc7cc
    // 0x7cc920: SaveReg d0
    //     0x7cc920: str             q0, [SP, #-0x10]!
    // 0x7cc924: SaveReg r2
    //     0x7cc924: str             x2, [SP, #-8]!
    // 0x7cc928: r0 = AllocateDouble()
    //     0x7cc928: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cc92c: RestoreReg r2
    //     0x7cc92c: ldr             x2, [SP], #8
    // 0x7cc930: RestoreReg d0
    //     0x7cc930: ldr             q0, [SP], #0x10
    // 0x7cc934: b               #0x7cc7fc
  }
  get _ name(/* No info */) {
    // ** addr: 0xea100c, size: 0x78
    // 0xea100c: EnterFrame
    //     0xea100c: stp             fp, lr, [SP, #-0x10]!
    //     0xea1010: mov             fp, SP
    // 0xea1014: AllocStack(0x10)
    //     0xea1014: sub             SP, SP, #0x10
    // 0xea1018: SetupParameters(PdfPattern this /* r1 => r0, fp-0x8 */)
    //     0xea1018: mov             x0, x1
    //     0xea101c: stur            x1, [fp, #-8]
    // 0xea1020: CheckStackOverflow
    //     0xea1020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1024: cmp             SP, x16
    //     0xea1028: b.ls            #0xea107c
    // 0xea102c: r1 = Null
    //     0xea102c: mov             x1, NULL
    // 0xea1030: r2 = 4
    //     0xea1030: movz            x2, #0x4
    // 0xea1034: r0 = AllocateArray()
    //     0xea1034: bl              #0xec22fc  ; AllocateArrayStub
    // 0xea1038: mov             x2, x0
    // 0xea103c: r16 = "/P"
    //     0xea103c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51248] "/P"
    //     0xea1040: ldr             x16, [x16, #0x248]
    // 0xea1044: StoreField: r2->field_f = r16
    //     0xea1044: stur            w16, [x2, #0xf]
    // 0xea1048: ldur            x0, [fp, #-8]
    // 0xea104c: LoadField: r3 = r0->field_b
    //     0xea104c: ldur            x3, [x0, #0xb]
    // 0xea1050: r0 = BoxInt64Instr(r3)
    //     0xea1050: sbfiz           x0, x3, #1, #0x1f
    //     0xea1054: cmp             x3, x0, asr #1
    //     0xea1058: b.eq            #0xea1064
    //     0xea105c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea1060: stur            x3, [x0, #7]
    // 0xea1064: StoreField: r2->field_13 = r0
    //     0xea1064: stur            w0, [x2, #0x13]
    // 0xea1068: str             x2, [SP]
    // 0xea106c: r0 = _interpolate()
    //     0xea106c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xea1070: LeaveFrame
    //     0xea1070: mov             SP, fp
    //     0xea1074: ldp             fp, lr, [SP], #0x10
    // 0xea1078: ret
    //     0xea1078: ret             
    // 0xea107c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea107c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1080: b               #0xea102c
  }
  _ PdfPattern(/* No info */) {
    // ** addr: 0xeaa410, size: 0x98
    // 0xeaa410: EnterFrame
    //     0xeaa410: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa414: mov             fp, SP
    // 0xeaa418: AllocStack(0x18)
    //     0xeaa418: sub             SP, SP, #0x18
    // 0xeaa41c: r0 = 2
    //     0xeaa41c: movz            x0, #0x2
    // 0xeaa420: stur            x1, [fp, #-8]
    // 0xeaa424: mov             x16, x3
    // 0xeaa428: mov             x3, x1
    // 0xeaa42c: mov             x1, x16
    // 0xeaa430: stur            x2, [fp, #-0x10]
    // 0xeaa434: CheckStackOverflow
    //     0xeaa434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa438: cmp             SP, x16
    //     0xeaa43c: b.ls            #0xeaa4a0
    // 0xeaa440: StoreField: r3->field_2b = r0
    //     0xeaa440: stur            x0, [x3, #0x2b]
    // 0xeaa444: mov             x0, x1
    // 0xeaa448: StoreField: r3->field_33 = r0
    //     0xeaa448: stur            w0, [x3, #0x33]
    //     0xeaa44c: ldurb           w16, [x3, #-1]
    //     0xeaa450: ldurb           w17, [x0, #-1]
    //     0xeaa454: and             x16, x17, x16, lsr #2
    //     0xeaa458: tst             x16, HEAP, lsr #32
    //     0xeaa45c: b.eq            #0xeaa464
    //     0xeaa460: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xeaa464: r1 = <PdfDataType>
    //     0xeaa464: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xeaa468: ldr             x1, [x1, #0x4c8]
    // 0xeaa46c: r0 = PdfDict()
    //     0xeaa46c: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xeaa470: mov             x1, x0
    // 0xeaa474: stur            x0, [fp, #-0x18]
    // 0xeaa478: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xeaa478: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xeaa47c: r0 = PdfDict()
    //     0xeaa47c: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0xeaa480: ldur            x1, [fp, #-8]
    // 0xeaa484: ldur            x2, [fp, #-0x10]
    // 0xeaa488: ldur            x3, [fp, #-0x18]
    // 0xeaa48c: r0 = PdfObject()
    //     0xeaa48c: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xeaa490: r0 = Null
    //     0xeaa490: mov             x0, NULL
    // 0xeaa494: LeaveFrame
    //     0xeaa494: mov             SP, fp
    //     0xeaa498: ldp             fp, lr, [SP], #0x10
    // 0xeaa49c: ret
    //     0xeaa49c: ret             
    // 0xeaa4a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa4a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa4a4: b               #0xeaa440
  }
}

// class id: 874, size: 0x40, field offset: 0x38
class PdfShadingPattern extends PdfPattern {

  _ prepare(/* No info */) {
    // ** addr: 0x7cc4a4, size: 0xbc
    // 0x7cc4a4: EnterFrame
    //     0x7cc4a4: stp             fp, lr, [SP, #-0x10]!
    //     0x7cc4a8: mov             fp, SP
    // 0x7cc4ac: AllocStack(0x10)
    //     0x7cc4ac: sub             SP, SP, #0x10
    // 0x7cc4b0: SetupParameters(PdfShadingPattern this /* r1 => r0, fp-0x8 */)
    //     0x7cc4b0: mov             x0, x1
    //     0x7cc4b4: stur            x1, [fp, #-8]
    // 0x7cc4b8: CheckStackOverflow
    //     0x7cc4b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cc4bc: cmp             SP, x16
    //     0x7cc4c0: b.ls            #0x7cc558
    // 0x7cc4c4: mov             x1, x0
    // 0x7cc4c8: r0 = prepare()
    //     0x7cc4c8: bl              #0x7cc580  ; [package:pdf/src/pdf/obj/pattern.dart] PdfPattern::prepare
    // 0x7cc4cc: ldur            x0, [fp, #-8]
    // 0x7cc4d0: LoadField: r2 = r0->field_1b
    //     0x7cc4d0: ldur            w2, [x0, #0x1b]
    // 0x7cc4d4: DecompressPointer r2
    //     0x7cc4d4: add             x2, x2, HEAP, lsl #32
    // 0x7cc4d8: stur            x2, [fp, #-0x10]
    // 0x7cc4dc: LoadField: r1 = r0->field_37
    //     0x7cc4dc: ldur            w1, [x0, #0x37]
    // 0x7cc4e0: DecompressPointer r1
    //     0x7cc4e0: add             x1, x1, HEAP, lsl #32
    // 0x7cc4e4: r0 = ref()
    //     0x7cc4e4: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cc4e8: mov             x4, x0
    // 0x7cc4ec: ldur            x3, [fp, #-0x10]
    // 0x7cc4f0: stur            x4, [fp, #-8]
    // 0x7cc4f4: LoadField: r2 = r3->field_7
    //     0x7cc4f4: ldur            w2, [x3, #7]
    // 0x7cc4f8: DecompressPointer r2
    //     0x7cc4f8: add             x2, x2, HEAP, lsl #32
    // 0x7cc4fc: mov             x0, x4
    // 0x7cc500: r1 = Null
    //     0x7cc500: mov             x1, NULL
    // 0x7cc504: cmp             w2, NULL
    // 0x7cc508: b.eq            #0x7cc52c
    // 0x7cc50c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc50c: ldur            w4, [x2, #0x17]
    // 0x7cc510: DecompressPointer r4
    //     0x7cc510: add             x4, x4, HEAP, lsl #32
    // 0x7cc514: r8 = X0 bound PdfDataType
    //     0x7cc514: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc518: ldr             x8, [x8, #0x6d8]
    // 0x7cc51c: LoadField: r9 = r4->field_7
    //     0x7cc51c: ldur            x9, [x4, #7]
    // 0x7cc520: r3 = Null
    //     0x7cc520: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b030] Null
    //     0x7cc524: ldr             x3, [x3, #0x30]
    // 0x7cc528: blr             x9
    // 0x7cc52c: ldur            x0, [fp, #-0x10]
    // 0x7cc530: LoadField: r1 = r0->field_b
    //     0x7cc530: ldur            w1, [x0, #0xb]
    // 0x7cc534: DecompressPointer r1
    //     0x7cc534: add             x1, x1, HEAP, lsl #32
    // 0x7cc538: ldur            x3, [fp, #-8]
    // 0x7cc53c: r2 = "/Shading"
    //     0x7cc53c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eed0] "/Shading"
    //     0x7cc540: ldr             x2, [x2, #0xed0]
    // 0x7cc544: r0 = []=()
    //     0x7cc544: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cc548: r0 = Null
    //     0x7cc548: mov             x0, NULL
    // 0x7cc54c: LeaveFrame
    //     0x7cc54c: mov             SP, fp
    //     0x7cc550: ldp             fp, lr, [SP], #0x10
    // 0x7cc554: ret
    //     0x7cc554: ret             
    // 0x7cc558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cc558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cc55c: b               #0x7cc4c4
  }
}
